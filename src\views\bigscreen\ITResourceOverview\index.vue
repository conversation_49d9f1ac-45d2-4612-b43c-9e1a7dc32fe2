<template>
  <div class="background">
    <div class="topBox">
      <div class="title">IT资源总体态势大屏</div>
      <div class="timeBox">{{dateStr}}</div>
    </div>
    <a-row :gutter="24" class="row-class">
      <a-col :xl="18" :lg="24" :md="24" class="col-left-class">
        <custom-title title="监控资源"></custom-title>
        <a-col :span="24" class="left-box">
          <a-col class="left-one">
            <card-title title="设备统计"></card-title>
            <device-statistic></device-statistic>
          </a-col>
          <a-col :span="24" class="left-two">
            <card-title title="设备状态监控"></card-title>
            <a-row :gutter="24" class="left-two-row">
              <a-col :lg="10" :md="24" class="left-two-col">
                <div class="left-two-card">
                  <pie-chart title="" style="width: 100%; height: 100%"></pie-chart>
                </div>
              </a-col>
              <a-col :lg="7" :md="24" class="left-two-col">
                <div class="left-two-card">
                  <online-chart title="" style="width: 100%; height: 100%"></online-chart>
                </div>
              </a-col>
              <a-col :lg="7" :md="24" class="left-two-col">
                <div class="left-two-card">
                  <change-chart-type :selectedIndex="selectedIndex" @changeType="changeType"></change-chart-type>
                  <port-speed-chart :showType="selectedIndex" style="width: 100%; height: 100%"></port-speed-chart>
                </div>
              </a-col>
            </a-row>
          </a-col>
          <a-col :span="24" class="left-three">
            <card-title title="使用情况"></card-title>
            <a-row :gutter="24" class="left-three-row">
              <a-col :lg="10" :md="24" class="left-three-col">
                <div class="left-three-card">
                  <disk-status-table title="" style="width: 100%; height: 100%"></disk-status-table>
                </div>
              </a-col>
              <a-col :lg="7" :md="24" class="left-three-col">
                <div class="left-three-card">
                  <cpu-usage-top title="" style="width: 100%; height: 100%"></cpu-usage-top>
                </div>
              </a-col>
              <a-col :lg="7" :md="24" class="left-three-col">
                <div class="left-three-card">
                  <memory-usage-table title="" style="width: 100%; height: 100%"></memory-usage-table>
                </div>
              </a-col>
            </a-row>
          </a-col>
          <div class="bottom-underline">
            <div class="innder-underline"></div>
          </div>
        </a-col>
      </a-col>
      <a-col :xl="6" :lg="24" :md="24" :xs="24" class="col-right-class">
        <custom-title title="告警统计"></custom-title>
        <div class="right-box">
          <div class="right-one">
            <card-title title="告警量"></card-title>
            <dashboard-chart title="" style="width: 100%; height: 100%"></dashboard-chart>
          </div>
          <div class="right-two">
            <card-title title="告警分类"></card-title>
            <div class="chart-wrapper">
              <alarm-category-chart title="" style="width: 100%; height: 100%"></alarm-category-chart>
            </div>
          </div>
          <div class="right-three">
            <card-title title="告警情况"></card-title>
            <div class="chart-wrapper">
              <alarm-info-chart title="" style="width: 100%; height: 100%"></alarm-info-chart>
            </div>
          </div>
          <div class="right-four">
            <card-title title="告警趋势"></card-title>
            <div class="chart-wrapper">
              <alarm-trend-chart title="" style="width: 100%; height: 100%"></alarm-trend-chart>
            </div>
          </div>
          <div class="bottom-underline">
            <div class="innder-underline"></div>
          </div>
        </div>
      </a-col>
    </a-row>
  </div>
</template>
<script>
import CardTitle from '@/views/bigscreen/ITResourceOverview/CardTitle.vue'
import CustomTitle from '@/views/bigscreen/ITResourceOverview/CustomTitle.vue'
import DeviceStatistic from '@/views/bigscreen/ITResourceOverview/DeviceStatistic.vue'
import PieChart from '@/views/bigscreen/ITResourceOverview/PieChart.vue'
import OnlineChart from '@/views/bigscreen/ITResourceOverview/OnlineChart.vue'
import PortSpeedChart from '@/views/bigscreen/ITResourceOverview/PortSpeedChart.vue'
import DiskStatusTable from '@/views/bigscreen/ITResourceOverview/DiskStatusTable.vue'
import CpuUsageTop from '@/views/bigscreen/ITResourceOverview/CpuUsageTop.vue'
import MemoryUsageTable from '@/views/bigscreen/ITResourceOverview/MemoryUsageTable.vue'
import DashboardChart from '@/views/bigscreen/ITResourceOverview/DashboardChart.vue'
import AlarmCategoryChart from '@/views/bigscreen/ITResourceOverview/AlarmCategoryChart.vue'
import AlarmInfoChart from '@/views/bigscreen/ITResourceOverview/AlarmInfoChart.vue'
import AlarmTrendChart from '@/views/bigscreen/ITResourceOverview/AlarmTrendChart.vue'
import changeChartType from './changeChartType.vue'
import moment from 'moment'
export default {
  name: 'ITResourceOverview',
  components: {
    CardTitle,
    CustomTitle,
    DeviceStatistic,
    PieChart,
    OnlineChart,
    PortSpeedChart,
    DiskStatusTable,
    CpuUsageTop,
    MemoryUsageTable,
    DashboardChart,
    AlarmCategoryChart,
    AlarmInfoChart,
    AlarmTrendChart,
    changeChartType
  },
  data() {
    return {
      selectedIndex: 0,
      timeStr: '',
      dateStr: '',
      dayStr: '',
    }
  },
  created() {
    this.getDateAndTime()
  },
  methods: {
    changeType(index) {
      this.selectedIndex = index
    },
    getDateAndTime() {
      // this.dateStr = moment().format('L')
      this.dateStr = moment().format('YYYY-MM-DD HH:mm:ss')
      // this.dayStr = moment().format('dddd')
      window.requestAnimationFrame(this.getDateAndTime)
    },
  }
}
</script>
<style lang="less" scoped>
.background {
  height: 100%;
  width: 100%;
  min-width: 17.5rem; // 1400/80px
  color: #fff;
  background-image: url('../../../assets/bigScreen/ITResource/bg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  .topBox {
    height: 1.3625rem; // 109/80px
    width: 100%;
    background: url('../../../assets/bigScreen/ITResource/header.png') center top no-repeat;
    background-size: 100% 100%;
    text-align: center;

    .title {
      height: 1.0875rem; // 87/80px
      display: flex;
      justify-content: center;
      align-items: center;
      font-family: FZZYJW;
      font-size: 0.575rem; // 46/80px
      letter-spacing: 0.1875rem; // 15/80px
      text-shadow: 0px 0.0375rem 0.2rem rgba(4, 7, 29, 0.5); // 3/80px 16/80px
    }
  }
  .timeBox {
    position: absolute;
    top: 0.4rem; // 32/80px
    left: 0.2625rem; // 21/80px
    font-family: DIN;
    font-size: 0.175rem; // 14/80px
    font-weight: 400;
    letter-spacing: 0.025rem; // 2/80px
  }
  .row-class {
    width: 100%;
    height: calc(100% - 1.3625rem); // 109/80px
    padding: 0.025rem 0.5rem 0.3rem 0.5125rem; // 2/80px 40/80px 24/80px 41/80px
    margin: 0 !important;
  }
  .col-left-class {
    height: 100%;
    position: relative;
    padding: 0 0.1375rem 0 0 !important; // 11/80px
  }
  .col-right-class {
    height: 100%;
    position: relative;
    padding: 0 0 0 0.125rem !important; // 10/80px
  }

  .bottom-underline {
    width: 100%;
    height: 0.025rem; // 2/80px
    background: #0090ff;
    position: absolute;
    bottom: 0; // 8/80px 0.1rem
    left: 0;
    .innder-underline {
      width: calc(100% - 0.3rem - 0.3rem); // 24/80px
      height: 0.025rem; // 2/80px
      background: #52e8df;
      position: absolute;
      bottom: 0;
      left: 0.3rem; // 24/80px
    }
  }
  .left-box {
    width: 100%;
    height: calc(100% - 0.15rem - 0.3625rem); // 12/80px 29/80px
    background: rgba(17, 28, 34, 0.5);
    margin-top: 0.15rem; // 12/80px
    padding-top: 0.075rem; // 6/80px
    position: relative;
    .left-one {
      width: 100%;
      height: 25.6%;
    }
    .left-two {
      width: 100%;
      height: 33.7%;
      .left-two-row {
        width: 100%;
        height: calc(100% - 0.425rem); // 34/80px
        margin: 0 !important;
        .left-two-col {
          height: 100%;
          padding: 0 !important;
          .left-two-card {
            width: 100%;
            height: 100%;
          }
        }
      }
    }
    .left-three {
      width: 100%;
      height: 40.7%;
      .left-three-row {
        width: 100%;
        height: calc(100% - 0.425rem); // 34/80px
        margin: 0 !important;
        .left-three-col {
          height: 100%;
          padding: 0 !important;
          .left-three-card {
            width: 100%;
            height: 100%;
            padding:32px 0 ;
          }
        }
      }
    }
  }
  .chart-wrapper {
    width: 100%;
    height: calc(100% - 0.3625rem);
  }
  .right-box {
    width: 100%;
    height: calc(100% - 0.15rem - 0.3625rem); // 12/80px 29/80px
    background: rgba(17, 28, 34, 0.5);
    margin-top: 0.15rem; // 12/80px
    padding-top: 0.075rem; // 6/80px
    position: relative;
    .right-one {
      width: 100%;
      height: 21%;
    }
    .right-two {
      width: 100%;
      height: 28%;
    }
    .right-three {
      width: 100%;
      height: 24%;
    }
    .right-four {
      width: 100%;
      height: 27%;
    }
  }
}
</style>
