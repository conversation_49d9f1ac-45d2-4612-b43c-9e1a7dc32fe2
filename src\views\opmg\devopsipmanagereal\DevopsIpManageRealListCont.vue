<template>
  <a-row style="overflow: hidden; height: 100%; overflow-y: auto" class="vScroll zxw" v-if="show">
    <a-col :span="22" class="colBack colhead">
      <a-button @click="onRescan" :disabled="disableSubmit">重新扫描</a-button>
    </a-col>
    <a-col :span="2" class="colBack colhead">
      <a-button
        @click="explain"
        style="border-radius:50%; height:35px; width:35px; text-align:center; margin-left:50px; display:inline-block"
        >?
      </a-button>
      <a-modal
        :visible="doubt"
        class="custom-class"
        style="color: black"
        title="详细说明"
        @ok="handleOk"
        @cancel="closedrawer"
        width="450px"
      >
        <div style="display: flex; margin-top: 20px">
          <div style="width: 50px; height: 30px; background-color: #5b8ff9"></div>
          <div style="margin-left: 20px"><p style="font-size: 18px">未使用：ping不通</p></div>
        </div>
        <div style="display: flex; margin-top: 20px">
          <div style="width: 50px; height: 30px; background-color: #5ad8a6"></div>
          <div style="margin-left: 20px"><p style="font-size: 18px">已使用：能ping通</p></div>
        </div>
        <div style="display: flex; margin-top: 20px">
          <div style="width: 50px; height: 30px; background-color: #389e0d"></div>
          <div style="margin-left: 20px"><p style="font-size: 18px">正常：IP和MAC都在白名单内</p></div>
        </div>
        <div style="display: flex; margin-top: 20px">
          <div style="width: 50px; height: 30px; background-color: #ffa940"></div>
          <div style="margin-left: 20px"><p style="font-size: 18px">非法占用：IP或MAC只有一个在白名单中</p></div>
        </div>
        <div style="display: flex; margin-top: 20px">
          <div style="width: 50px; height: 30px; background-color: #8b8f00"></div>
          <div style="margin-left: 20px"><p style="font-size: 18px">未获取到MAC：没有获取到MAC值</p></div>
        </div>
        <div style="display: flex; margin-top: 20px">
          <div style="width: 50px; height: 30px; background-color: pink"></div>
          <div style="margin-left: 20px"><p style="font-size: 18px">非法篡改：IP和MAC都不在白名单内</p></div>
        </div>
        <div style="display: flex; margin-top: 20px">
          <div style="width: 50px; height: 30px; background-color: white"></div>
          <div style="margin-left: 20px"><p style="font-size: 18px">数据为零时则在饼状图表中不显示</p></div>
        </div>
      </a-modal>
    </a-col>
    <a-col class="colBack colBoxOn" style="overflow: hidden; overflow-x: auto; white-space: nowrap">
      <div class="colBoxDiv">
        网段：<span>{{ networkSegment }}</span>
      </div>
      <div class="colBoxDiv">
        备注：<span>{{ remarks }}</span>
      </div>
      <div class="colBoxDiv">
        执行周期：<span>{{ executeCron }}</span>
      </div>
      <div class="colBoxDiv">
        创建时间：<span>{{ createTime }}</span>
      </div>
    </a-col>
    <a-col
      class="colBack colBox colBoxTw"
      style="
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        overflow: hidden;
        overflow-x: auto;
        white-space: nowrap;
      "
    >
      <div class="colBoxDivCenter">
        <span
          >未使用：<span>{{ occupancyRatioData.notUsed }}</span></span
        >
        <span style="margin-left: 9%"
          >占用率：<span>{{ occupancyRatioData.notUsedRate }}</span></span
        >
      </div>
      <div class="colBoxDivCenter">
        <span
          >已使用：<span>{{ occupancyRatioData.used }}</span></span
        >
        <span style="margin-left: 9%"
          >占用率：<span>{{ occupancyRatioData.usedRate }}</span></span
        >
      </div>
      <div class="colBoxDivCenter">
        正常：<span>{{ occupancyRatioData.normal }}</span>
      </div>
      <div class="colBoxDivCenter">
        非法占用：<span>{{ occupancyRatioData.illegalOccupation }}</span>
      </div>
      <div class="colBoxDivCenter">
        未获取到MAC：<span>{{ occupancyRatioData.noGetMAC }}</span>
      </div>
      <div class="colBoxDivCenter">
        非法篡改：<span>{{ occupancyRatioData.illegalFalsify }}</span>
      </div>
    </a-col>
    <a-col class="colBack colBox colBoxTh">
      <annularPie :data-obj="chartData" :color-obj='colorObj'></annularPie>
    </a-col>
    <a-col :span="24" class="colBoxFo" style="overflow: hidden; overflow-x: auto; white-space: nowrap">
      <a-col :span="24" style="height: 18%">
        <a-col :span="3" class="span spanOn">
          <span class="box boxOn"></span>
          <span>保&nbsp;留</span>
        </a-col>
        <a-col :span="3" class="span spanTw">
          <span class="box boxTw"></span>
          <span>未使用</span>
        </a-col>
        <a-col :span="3" class="span spanTh">
          <span class="box boxTh"></span>
          <span>正常接入</span>
        </a-col>
        <a-col :span="3" class="span spanFo">
          <span class="box boxFo"></span>
          <span>非法占用</span>
        </a-col>
        <a-col :span="3" class="span spanFi">
          <span class="box boxFi"></span>
          <span>未获取到MAC</span>
        </a-col>
        <a-col :span="3" class="span spansix">
          <span class="box boxsix"></span>
          <span>非法篡改</span>
        </a-col>
      </a-col>
      <a-col :span="24" class="ipBox" id="ipBox">
        <table class="mailTable">
          <tr v-for="(item, index) in flashingLists" :key="index">
            <td class="column">{{ item.ipAddress }}</td>
            <td
              class="tableBox"
              :class="`${item.ipState == '1' ? 'boxTw' : 'boxTh'} ${item.occupied == '0' ? 'boxFo' : ''}
            ${item.occupied == '2' ? 'boxFi' : ''}${item.occupied == '1' ? 'boxsix' : ''}`"
            >
              {{ item.sort }}
            </td>
            <td
              class="tableBox"
              v-for="(child, ind) in item.clink"
              :key="ind"
              :class="`${child.ipState == '1' ? 'boxTw' : 'boxTh'}
             ${child.occupied == '0' ? 'boxFo' : ''} ${child.nums == '0' ? 'boxOn' : ''}${
                child.occupied == '2' ? 'boxFi' : ''
              }${child.occupied == '1' ? 'boxsix' : ''}`"
            >
              <span>{{ child.sort }}</span>
            </td>
          </tr>
        </table>
      </a-col>
    </a-col>
    <a-col :span="24" class="colBoxFi">
      <a-col :span="24" style="height: 18%">
        <a-col :span="5" class="span keyword" style="display: flex; white-space: nowrap">
          <span style="float: left; margin-right: 7px">IP地址</span>
          <a-input-search
            placeholder="请输入"
            :allowClear="true"
            autocomplete="off"
            v-model="queryParam.ipAddress"
            @search="searchQuery"
          />
        </a-col>
        <a-col :span="3" class="span whiteList">
          <a-button type="shallow" @click="addIpManage()">添加到白名单</a-button>
        </a-col>
        <a-col :span="3" class="span export">
          <a-button @click="handleExportXls('IP地址详情')">导出</a-button>
        </a-col>
        <a-col :span="3" class="span refresh">
          <a-button @click="refreshData">刷新</a-button>
        </a-col>
        <a-col :span="24" style="margin-top: 76px; padding-left: 24px; padding-right: 24px">
          <a-table
            ref="table"
            size="middle"
            bordered
            rowKey="id"
            :columns="columns"
            :dataSource="dataSource"
            :pagination="ipagination"
            :loading="loading"
            :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
            class="j-table-force-nowrap"
            @change="handleTableChange"
          >
          </a-table>
        </a-col>
      </a-col>
    </a-col>
  </a-row>
</template>
<script>
import { filterObj } from '@/utils/util'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import  WebsocketMessageMixin  from '@/mixins/WebsocketMessageMixin'
import { httpAction, getAction } from '@/api/manage'
import annularPie from './annularPie'
export default {
  name: 'DevopsIpManageRealListCont',
  props: ['treeFlag'],
  components: {
    annularPie,
  },
  mixins: [JeecgListMixin,WebsocketMessageMixin],
  data() {
    return {
      doubt: false,
      networkSegment: '',
      subNet: '',
      remarks: '',
      executeCron: '',
      createTime: '',
      ipSegsentId: '',
      show: false,
      ipBoxCont: '',
      flashingLists: [],
      occupancyRatioData: [],
      disableSubmit: false,
      num: 0,
      data: ['2', '2', '1', '2', '2', '1', '1', '1', '1', '2'],
      // 表头
      columns: [
        {
          title: 'IP地址',
          align: 'center',
          dataIndex: 'ipAddress',
        },
        {
          title: 'MAC地址',
          align: 'center',
          dataIndex: 'macCode',
        },
        {
          title: '设备名称',
          align: 'center',
          dataIndex: 'terminalName',
        },
        {
          title: 'IP地址状态',
          align: 'center',
          dataIndex: 'ipStateText',
        },
        {
          title: '非法占用',
          align: 'center',
          dataIndex: 'occupiedText',
        },
        {
          title: '创建人',
          align: 'center',
          dataIndex: 'createByName',
        },
        {
          title: '创建时间',
          align: 'center',
          dataIndex: 'createTime',
        },
      ],
      url: {
        rescan: '/devopsipsegsent/devopsIpSegsent/rescan',
        list: '/devopsipmanagereal/devopsIpManageReal/list',
        exportXlsUrl: '/devopsipmanagereal/devopsIpManageReal/exportXls',
        addIpManages: '/devopsipmanage/devopsIpManage/addIpManages',
        flashingList: '/devopsipmanagereal/devopsIpManageReal/flashingList',
        occupancyRatio: '/devopsipmanagereal/devopsIpManageReal/occupancyRatio',
      },
      chartData: {},
      chartDataType: [
        { name: '未使用', field: 'notUsed' },
        { name: '已使用', field: 'used' },
        { name: '正常', field: 'normal' },
        { name: '非法占用', field: 'illegalOccupation' },
        { name: '未获取到MAC', field: 'noGetMAC' },
        { name: '非法篡改', field: 'illegalFalsify' },
      ],
      colorObj: {
        colorList1: [
          { color1: '#5B8FF9', color2: '#5B8FF9' },
          { color1: '#5AD8A6', color2: '#5AD8A6' }
        ],
        colorList2: [
          { color1: '#389E0D', color2: '#389E0D' },
          { color1: '#FFA940', color2: '#FFA940' },
          { color1: '#8b8f00', color2: '#8b8f00' },
          { color1: '#ffc0cb', color2: '#ffc0cb' }
        ]
      }
    }
  },
  computed: {
    rowCount: function () {
      return Math.ceil(this.tableData.length / 2)
    },
  },
  watch: {
    treeFlag: function (n, o) {
      this.networkSegment = n.ipSegsent
      this.subNet = n.ipSegsent.substring(n.ipSegsent.lastIndexOf('/') + 1)
      this.remarks = n.remarks
      this.executeCron = n.executeCron
      this.createTime = n.createTime
      this.ipSegsentId = n.id
      this.disableSubmit = false

      if (this.networkSegment.indexOf('/') != -1) {
        let i = this.networkSegment.substring(this.networkSegment.lastIndexOf('.') + 1)
        this.ipBoxCont = i
      } else {
        this.ipBoxCont = this.networkSegment.substring(this.networkSegment.lastIndexOf('.') + 1)
      }
      this.getShow()
      this.refreshData()
      this.getFlashingLists()
      this.occupancyRatio()
    },
  },
  methods: {
    testMessage(){
      console.log('Message from server ', event.data)
    },
    handleOk() {
      this.closedrawer()
    },
    explain() {
      this.doubt = true
    },
    closedrawer() {
      this.doubt = false
    },
    //去掉默认创建时间排序
    getQueryParams() {
      //获取查询条件
      var param = Object.assign({}, this.queryParam)
      param.field = this.getQueryField()
      param.pageNo = this.ipagination.current
      param.pageSize = this.ipagination.pageSize
      return filterObj(param)
    },
    occupancyRatio() {
      getAction(this.url.occupancyRatio, {
        segsentId: this.ipSegsentId,
      }).then((res) => {
        if (res.success) {
          let values = []
          for (let i=0;i<this.chartDataType.length;i++){
            let obj = {}
            let key=this.chartDataType[i].field
            if(res.result.hasOwnProperty(key)){
              obj = {
                name: this.chartDataType[i].name,
                value: res.result[key]=='0'?'':res.result[key]
              }
              values.push(obj)
            }
          }
          this.chartData={data1:values.slice(0,2),data2:values.slice(2,6)}
          this.occupancyRatioData = res.result
        }
      })
    },
    //获取闪烁
    getFlashingLists() {
      getAction(this.url.flashingList, {
        segsentId: this.ipSegsentId,
      }).then((res) => {
        if (res.success) {
          this.flashingLists = res.result
        }
      })
    },
    addIpManage() {
      const that = this
      var ids = ''
      if (this.selectionRows.length == 0) {
        that.$message.warning('请选择IP地址！')
        return
      }
      for (var a = 0; a < this.selectionRows.length; a++) {
        ids += this.selectionRows[a].id + ','
      }
      let formData = {
        realIds: ids,
      }
      httpAction(this.url.addIpManages, formData, 'post').then((res) => {
        if (res.success) {
          that.$message.success(res.result)
        } else {
          that.$message.warning(res.message)
        }
      })
    },
    refreshData() {
      this.queryParam.segsentId = this.ipSegsentId
      this.queryParam.ipAddress = ''
      this.loadData(1)
    },
    onSearch(value) {},
    getShow() {
      if (this.networkSegment == '') {
        this.show = false
      } else {
        this.show = true
        this.initTable()
      }
    },
    onRescan() {
      this.num = 0
      this.disableSubmit = false
      const that = this
      let formData = {
        id: this.ipSegsentId,
      }
      httpAction(this.url.rescan, formData, 'post').then((res) => {
        if (res.success) {
          that.$message.success(res.result)
          that.refreshData()
        } else {
          that.$message.warning(res.message)
        }
      })
    },
    initTable() {
      /*if(this.networkSegment != ''){
            let ipBox = document.getElementById('ipBox');
            let column = document.getElementsByClassName('column');
            let columnWidth = column[0].clientWidth;
            let ipBoxWidth = ipBox.clientWidth - columnWidth - 62;
            let contWidth = Math.ceil(ipBoxWidth/40);

          }*/
    },

    //接收
    websocketOnmessage: function (e) {
      if(e.data === "HeartBeat")return;
      // console.log("ip地址详情接收到推送消息了 === ",e)
      var data = eval('(' + e.data + ')') //解析对象
      if (data.messageType === 'ipManagerReal') {
        if (data.data === 'ok') {
          this.refreshData()
          this.getFlashingLists()
          this.occupancyRatio()
          this.num = this.num + 1
        }
      }
      if (this.num > 1) {
        this.disableSubmit = false
      }
    },
  },
}
</script>
<style lang="less" scoped>
@import "~@assets/less/scroll.less";

.colBack {
  background-color: #ffffff;
  margin-bottom: 16px;
  height: 302px;
  float: left;
}

.colhead {
  height: 69px;
  padding: 18px;
}

.colBox {
  margin-left: 16px;
}

.colBoxOn,
.colBoxTw {
  width: 26%;
}

.colBoxTh {
  width: calc(100% - 52% - 32px);
}

.colBoxDiv {
  height: 22%;
  font-size: 1.3em;
  padding: 22px;
  padding-top: 36px;
}

.colBoxDivCenter {
  font-size: 1.3em;
  padding-left: 20px;
}

.colBoxFo {
  background-color: #ffffff;
  position: relative;
}

.colBoxFi {
  background-color: #ffffff;
  position: relative;
  margin-top: 16px;
  /*height: 100%;*/
}

.span {
  position: absolute;
  top: 24px;
}

.spanOn,
.keyword {
  left: 24px;
}

.spanTw {
  left: 121px;
}

.spanTh {
  left: 228px;
}

.spanFo {
  left: 350px;
}

.spanFi {
  left: 472px;
}

.spansix {
  left: 614px;
}

.keyword {
  line-height: 30px;
}

.whiteList {
  left: 352px;
}

.export {
  left: 483px;
}

.refresh {
  left: 562px;
}

.box {
  width: 26px;
  height: 20px;
  display: block;
  float: left;
  margin-right: 8px;
}

.boxOn {
  background-color: #8eacff !important;
  color: #fff;
}

.boxTw {
  background-color: #fff;
  border: 1px solid #e8e8e8;
}

.boxTh {
  background-color: #389e0d;
  color: #fff;
}

.boxFo {
  background-color: #ffa940 !important;
  color: #fff;
}

.boxFi {
  background-color: #989d00 !important;
  color: #fff;
}

.boxsix {
  background-color: pink !important;
  color: #fff;
}

.ipBox {
  margin-top: 2px;
  padding: 61px 24px 22px 23px;
  height: calc(100% - 61px);
}

.mailTable {
  width: 100%;
}

.mailTable,
.mailTable tr,
.mailTable tr td {
  border: 1px solid #e6eaee;
}

.mailTable {
  font-size: 14px;
  color: #71787e;
}

.mailTable tr td {
  border: 1px solid #e6eaee;
  line-height: 35px;
  box-sizing: border-box;
  padding: 0 10px;
}

.tableBox {
  width: 3%;
  height: 38px;
  text-align: center;
}

.column {
  background-color: #eff3f6;
  color: #393c3e;
  text-align: center;
  width: 11%;
}
</style>