<template>
  <div style="height:100%;">
    <keep-alive>
      <component :is="pageName" style="height:100%" :data="data" />
    </keep-alive>
  </div>
</template>
<script>
  import TerminalBindList from './TerminalBindList'
  import TerminalBindDetails from './modules/TerminalBindDetails'
  export default {
    name: "TerminalBindManage",
    data() {
      return {
        isActive: 0,
        data: {}
      };
    },
    components: {
      TerminalBindList,
      TerminalBindDetails
    },
    created() {
      this.pButton1(0);
    },
    //使用计算属性
    computed: {
      pageName() {
        switch (this.isActive) {
          case 0:
            return "TerminalBindList";
            break;

          default:
            return "TerminalBindDetails";
            break;
        }
      }
    },
    methods: {
      pButton1(index) {
        this.isActive = index;
      },
      pButton2(index, item) {
        this.isActive = index;
        this.data = item;
      }
    }
  }
</script>