<template>
  <div style='height: 100%;overflow: hidden;display: flex;' >
    <div class='duty'>
      <departmentDutyLeft :show-look='showLook' :getEvenList="getEvenList" :showLook="showLook" ref="shift"></departmentDutyLeft>
    </div>
    <div class='duty-calendar'>
          <schedule :asycEventList="asycEventList"
                  :layHeight="layHeight"
                  :layWidth="layWidth"
                  @deleteNewEvent="deleteNewEvent"
                  @addEventShow="addEventShow"
                  :showDialog="showDialog"
                  :showLook="showLook"
                    @editNewShow='editNewShow'
        ></schedule>
        <add-shift-modal ref="child" :dataSource="dataSource" @refreshPage="refreshPage"></add-shift-modal>
    </div>
  </div>
</template>

<script>

import {getAction} from "@api/manage";
import moment from "moment";
import dateFunc from "vue-fullcalendar/src/components/dateFunc";

export default {
  name:'onduty',
  components: {
    schedule: () => import("@comp/schedule"),
    departmentDutyLeft: () => import("./modules/departmentDutyLeft"),
    addShiftModal: () => import("./modules/addShiftModal"),
  },
  props:{
    showLook:false
  },
  data() {
    return {
      asycEventList: [],
      dataSource: [],
      time: "",
      layWidth: 0,
      layHeight: 0,
      showDialog: true,
    }
  },
  created() {
    //加载页面
    this.initLayout()
  },
  methods: {
    getEvenList(even) {
      this.asycEventList = even
    },
    initLayout() {
      const mainWidth = 925
      this.layWidth = mainWidth / 7
      this.layHeight = 25
    },
    deleteNewEvent(id) {
      let _this=this
      this.$confirm({
        title: '确认删除',
        okText: '是',
        cancelText: '否',
        content: '是否删除选中数据?',
        zIndex: 10000,
        onOk: function () {
          getAction("/shiftUser/record/deleteById", {id: id}).then((res) => {
            if (res.success) {
              _this.$refs.shift.loadData1();
              _this.$refs.shift.getUserList();
            } else {
              this.$message.warning(res.message);
            }
          })
        }
      })

    },
    //添加弹窗
    addEventShow(t) {
      if (!this.showLook){
        return
      }
      this.dataSource=this.$refs.shift.dataSource
      let start = t + " 00:00:00"
      this.time=start
      let time1 = new Date(dateFunc.format(this.time, 'yyyy-MM-dd'));
      let newTime = new Date(dateFunc.format(new Date(), 'yyyy-MM-dd'));
      if (time1>=newTime){
        this.$refs.child.openVisible(start,{},"添加")
      }
    },
    //编辑弹窗
    editNewShow(data) {
      this.dataSource=this.$refs.shift.dataSource
      let addEventForm = {
        allDay: false,
        time: [moment(data.data.startTime), moment(data.data.endTime)],
        planColor: "#1890FF",
        showType:"2",
      }
      addEventForm = Object.assign(data.data)
      this.time=data.start + " 00:00:00"
      let time1 = new Date(dateFunc.format(this.time, 'yyyy-MM-dd'));
      let newTime = new Date(dateFunc.format(new Date(), 'yyyy-MM-dd'));
      if (time1>=newTime){
        this.$refs.child.openVisible(data.start,addEventForm,"编辑")
      }

    },
    refreshPage(){
      this.$refs.shift.loadData1();
      this.$refs.shift.getUserList();
    }
  }

}
</script>
<style lang="less" scoped>
#app{
  main{
    .main-box{
      overflow: hidden !important;
    }
  }
}

.duty {
  width:300px;
  margin-right: 12px;
  height: 100%;
}
.duty-calendar{
  width:calc(100% - 312px);
  height: 100%;
  overflow: hidden;
  overflow-x: auto;
}

/*@media (min-width: 576px) {
  #app{
    main{
      .main-box{
        overflow: hidden !important;
      }
    }
  }

  .duty {
    width:300px;
    margin-right: 12px;
    height: 100%;
  }
  .duty-calendar{
    width:calc(100% - 312px);
    min-width: 1000px;
    height: 100%
  }
}
@media (max-width: 576px){
  .duty {
    width:100%;
    margin-bottom: 12px;
    height: initial;
  }
  .duty-calendar{
    width:100%;
    height: 100%;
    margin-bottom:12px
  }
}*/
</style>