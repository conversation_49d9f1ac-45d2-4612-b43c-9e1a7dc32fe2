<template>
  <a-row :gutter='10' style='height: 100%' class='vScroll'>
    <a-col style='width: 100%; height: 100%; display: flex; flex-direction: column'>
      <!-- 查询区域 -->
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <div class='table-page-search-wrapper-style'>
          <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
            <a-row :gutter='24' ref='row'>
              <a-col :span='spanValue'>
                <a-form-item label='任务名称'>
                  <a-input :maxLength='maxLength' placeholder='请输入任务名称' v-model='queryParam.taskName' :allowClear='true'
                           autocomplete='off' />
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label='任务状态'>
                  <a-select default-value='lucy' v-model='queryParam.taskStatus' :allowClear='true'
                            placeholder='请选择任务状态'>
                    <a-select-option value='0'>
                      禁用
                    </a-select-option>
                    <a-select-option value='1'>
                      启用
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span='colBtnsSpan()'>
                <span class='table-page-search-submitButtons'
                      :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button type='primary' class='btn-search btn-search-style' @click='searchQuery'>查询</a-button>
                  <a-button class='btn-reset btn-reset-style' @click='searchReset'>重置</a-button>
                  <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <!-- 查询区域-END -->
      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <!-- 操作按钮区域 -->
        <div class='table-operator table-operator-style'>
          <a-button @click='handleAdd' v-has="'terminal:add'">新增</a-button>
          <a-dropdown v-if='selectedRowKeys.length > 0'>
            <a-menu slot='overlay' style='text-align: center'>
              <a-menu-item key='1' @click='batchDel'>删除</a-menu-item>
            </a-menu>
            <a-button> 批量操作
              <a-icon type='down' />
            </a-button>
          </a-dropdown>
        </div>
        <!-- table区域-begin -->
        <a-table ref='table' bordered rowKey='id' :columns='columns' :dataSource='dataSource'
                 :scroll='dataSource.length>0?{x:"max-content"}:{}' :pagination='ipagination' :loading='loading'
                 :rowSelection='{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }'
                 @change='handleTableChange'>
          <span class='caozuo' slot='receiveCount' slot-scope='text, record'><a
            @click='countGo(record)'>{{ text }}</a></span>
          <span class='caozuo' slot='receiveSuccessCount' slot-scope='text, record'>
            <a @click='successGo(record)'>{{ text }}</a>
          </span>
          <span class='caozuo' slot='action' slot-scope='text, record'>
            <a @click='handleDetailPage(record)'>查看</a>
            <a-divider type='vertical' />
            <a @click='handleEdit(record)'>编辑</a>
            <a-divider type='vertical' />
              <a-dropdown>
                <a-menu slot='overlay'>
                  <a-menu-item key='4'>
                     <a-popconfirm title='确定删除吗?' @confirm='() => handleDelete(record.id)'>
                        <a>删除</a>
                      </a-popconfirm>
                  </a-menu-item>
                    <a-menu-item key='5'>
                     <a @click='handleExecute(record)'>立即执行</a>
                  </a-menu-item>
                </a-menu>
                 <a>更多</a>
              </a-dropdown>
          </span>
          <template slot='tooltip' slot-scope='text'>
            <a-tooltip placement='topLeft' :title='text' trigger='hover'>
              <div class='tooltip'>
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
      </a-card>
      <!-- 表单区域 -->
      <reporting-statistics-modal ref='modalForm' @ok='modalFormOk'></reporting-statistics-modal>
    </a-col>
  </a-row>
</template>

<script>
import {
  JeecgListMixin
} from '@/mixins/JeecgListMixin'
import ReportingStatisticsModal from './modules/ReportingStatisticsModal.vue'
import {
  deleteAction, postAction,putAction
} from '@/api/manage'
import {
  YqFormSearchLocation
} from '@/mixins/YqFormSearchLocation'

export default {
  name: 'reportingList',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components: {
    ReportingStatisticsModal
  },
  data() {
    return {
      maxLength:50,
      description: '统计汇聚页面',
      formItemLayout: {
        labelCol: {
          style: 'width:100px'
        },
        wrapperCol: {
          style: 'width:calc(100% - 100px)'
        }
      },
      queryParam: {
        levelType: '1'
      },
      columns: [{
        title: '任务名称',
        dataIndex: 'taskName',
        customCell: () => {
          let cellStyle = 'text-align: left; min-width: 50px;max-width:300px'
          return {
            style: cellStyle
          }
        }
      },
        {
          title: '目标数据表',
          dataIndex: 'targetTable',
          customCell: () => {
            let cellStyle = 'text-align: center;'
            return {
              style: cellStyle
            }
          }
        }, {
          title: '唯一字段',
          dataIndex: 'uniqueColumn',
          customCell: () => {
            let cellStyle = 'text-align: center;'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '实体类全类名',
          dataIndex: 'dataClass',
          customCell: () => {
            let cellStyle = 'text-align: center'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '服务类全类名',
          dataIndex: 'serviceClass',
          customCell: () => {
            let cellStyle = 'text-align: center'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '执行频率',
          dataIndex: 'executeCron',
          customCell: () => {
            let cellStyle = 'text-align: left'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '任务状态',
          dataIndex: 'taskStatus',
          customCell: () => {
            let cellStyle = 'text-align: center; min-width: 110px'
            return {
              style: cellStyle
            }
          },
          customRender: (text, record) => {
            return text == 0 ? '禁用' : '启用'
          }
        },
        {
          title: '时间字段',
          dataIndex: 'timeColumn',
          customCell: () => {
            let cellStyle = 'text-align: center;'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '开始时间',
          dataIndex: 'startTime',
          customCell: () => {
            let cellStyle = 'text-align: center; min-width: 130px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '结束时间',
          dataIndex: 'endTime',
          customCell: () => {
            let cellStyle = 'text-align: center; min-width: 130px'
            return {
              style: cellStyle
            }
          }
        },

        {
          title: '备注',
          dataIndex: 'description',
          customCell: () => {
            let cellStyle = 'text-align: center'
            return {
              style: cellStyle
            }
          }
        },

        {
          title: '操作',
          dataIndex: 'action',
          fixed: 'right',
          customCell: () => {
            let cellStyle = 'text-align: center; width: 160px'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'action'
          }
        }
      ],
      url: {
        list: '/statis/reportTask/list',
        delete: '/statis/reportTask/deleteBatch',
        deleteBatch: '/statis/reportTask/deleteBatch'
      }
    }
  },
  created() {
  },
  methods: {
    countGo(record) {
      this.$router.push({
        path: '/jkdjgl/reporting/logManage',
        query: {
          'logType': '1',
          'parentDepartCode': record.parentDepartCode
        }
      })
    },
    successGo(record) {
      this.$router.push({
        path: '/jkdjgl/reporting/logManage',
        query: {
          // 'childDepartCode': record.childDepartCode,
          'logType': '1',
          'resultFlag': true,
          'parentDepartCode': record.parentDepartCode
        }
      })
    },
    searchReset() {
      this.queryParam = {
        levelType: '1'
      }
      this.loadData(1)
    },
    handleDelete: function(id) {
      var that = this
      deleteAction(that.url.delete, {
        ids: id
      }).then((res) => {
        if (res.success) {
          //重新计算分页问题
          that.reCalculatePage(1)
          that.$message.success(res.message)
          that.loadData()
        } else {
          that.$message.warning(res.message)
        }
      })
    },
    //立即执行
    handleExecute(record) {
      putAction("/statis/reportTask/execute",record).then((res) => {
        if(res.success){
          this.$message.success(res.message)
        }
      })
    },
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
</style>