<template>
  <div style="height: 100%;" class="vScroll hScroll">
    <div style="height: 100%;min-width: 992px !important;">
      <div class="colhead">
        <a-button @click="onRescan" type='primary' :disabled="disableSubmit">手动扫描</a-button>
        <a-popover title='状态说明' placement="bottomRight">
          <template slot='content'>
            <div style="display: flex" v-for='(item,index) in colorDesList' :key='"colorDesList_"+index'>
              <div style="width: 40px; height: 24px;border:  1px solid #e8e8e8" :style='{backgroundColor: item.color}'>
              </div>
              <div style="margin-left: 12px">
                <p style="font-size: 16px">{{item.describe}}</p>
              </div>
            </div>
          </template>
          <a-icon style='font-size: 20px; line-height: 32px' theme='twoTone' type='question-circle' />
        </a-popover>
      </div>
      <a-row :gutter='[12,12]' style='margin-right: 0px !important;'>
        <a-col :md='24' :lg='24' :xl='12' :xxl='7' class="colBox">
          <div class="colBoxChild">
            <div class="colBoxDiv">
              审计对象：<span>{{ networkSegment }}</span>
            </div>
            <div class="colBoxDiv">
              备注：<span>{{ remark }}</span>
            </div>
            <div class="colBoxDiv">
              执行周期：<span>{{ cron }}</span>
            </div>
            <div class="colBoxDiv">
              创建时间：<span>{{ createTime }}</span>
            </div>
          </div>
        </a-col>
        <a-col :md='24' :lg='24' :xl='12' :xxl='7' class="colBox">
          <div class="colBoxChild">
            <div class="colBoxDiv">
              <span>未使用：<span>{{ occupancyRatioData.notUsed }}</span></span>
              <span style="margin-left: 9%">未使用率：<span>{{ occupancyRatioData.notUsedRate }}</span></span>
            </div>
            <div class="colBoxDiv">
              <span>已使用：<span>{{ occupancyRatioData.used }}</span></span>
              <span style="margin-left: 9%">已使用率：<span>{{ occupancyRatioData.usedRate }}</span></span>
            </div>
            <div class="colBoxDiv">
              正常：<span>{{ occupancyRatioData.normal }}</span>
            </div>
            <div class="colBoxDiv">
              非法占用：<span>{{ occupancyRatioData.illegalOccupation }}</span>
            </div>
            <div class="colBoxDiv">
              未获取到MAC：<span>{{ occupancyRatioData.noGetMAC }}</span>
            </div>
            <div class="colBoxDiv">
              非法篡改：<span>{{ occupancyRatioData.illegalFalsify }}</span>
            </div>
          </div>
        </a-col>
        <a-col :md='24' :lg='24' :xl='24' :xxl='10' class="colBox">
          <div style='padding: 24px;height: 100%;background-color: #ffffff'>
            <ipPanelPie :data-obj="chartData" :color-obj='colorObj'> </ipPanelPie>
          </div>
        </a-col>
      </a-row>
      <div class="colBoxFo">
        <div class='ipDes'>
          <div class="ipDesBox" v-for='(item,index) in ipDesList' :key='"ipDesList_"+index'>
            <span class="box" :style='{backgroundColor:item.color}'></span>
            <span v-html='item.describe'></span>
          </div>
        </div>
        <div class="ipBox" id="ipBox">
          <table class="mailTable">
            <tr v-for="(item, index) in flashingLists" :key="index">
              <td class="column">{{ item.ipAddress }}</td>
              <a-tooltip>
                <template slot="title">
                  <span>{{item.occupied == 0 ? '非法占用' : item.occupied == 2 ? '未获取到Mac' :
                          item.occupied == 1 ? '非法篡改' : item.nums == '0' ? '保留' : item.ipState == 1 ? '正常接入' : item.ipState == 0 ? '未使用' : ''}}</span>
                </template>
                <td class="tableBox"
                  :class="`${item.ipState == '0' ? 'boxTw' : 'boxTh'} ${item.nums == '0' ? 'boxOn' : ''} ${item.occupied == '0' ? 'boxFo' : ''} ${item.occupied == '2' ? 'boxFi' : ''} ${item.occupied == '1' ? 'boxsix' : ''}`">
                  {{ item.sort }}
                </td>
              </a-tooltip>
              <td class="tableBox" v-for="(child, ind) in item.clink" :key="ind" :class="`${child.ipState == '0' ? 'boxTw' : 'boxTh'} ${child.nums == '0' ? 'boxOn' : ''} ${child.occupied == '0' ? 'boxFo' : ''} ${child.occupied == '2' ? 'boxFi' : ''} ${child.occupied == '1' ? 'boxsix' : ''}`">
                <a-tooltip>
                  <template slot="title">
                    <span>{{ child.occupied == 0 ? '非法占用' : child.occupied == 2 ? '未获取到Mac' :
                        child.occupied == 1 ? '非法篡改' : child.nums == '0' ? '保留' : child.ipState == 1 ? '正常接入' :
                        child.ipState == 0 ? '未使用' :'' }}</span>
                  </template>
                  <span>{{ child.sort }}</span>
                </a-tooltip>
              </td>
            </tr>
          </table>
        </div>
        <a-pagination style="text-align: right; position: relative; margin: 30px 30px 0 auto;" v-model="tableCurrent"
          :total="tableTotal" :show-total="(total) => `共 ${tableTotal} 页`" :pageSize="1" show-less-items
          @change="tableChange()" />
      </div>
      <a-col :span="24" class="colBoxFi">
        <div class="keyword">
          <span>IP地址：</span>
          <a-input-search placeholder="请输入" :allowClear="true" autocomplete="off" v-model="queryParam.ipAddress"
            @search="searchQuery" />
        </div>
        <div style="margin-top: 16px;">
          <a-table ref="table" size="middle" bordered rowKey="id" :columns="columns" :dataSource="dataSource"
            :pagination="ipagination" :loading="loading"
            :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }" class="j-table-force-nowrap"
            @change="handleTableChange">
          </a-table>
        </div>
      </a-col>
    </div>
  </div>
</template>
<script>
  import {
    filterObj
  } from '@/utils/util'
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import WebsocketMessageMixin from '@/mixins/WebsocketMessageMixin'
  import {
    httpAction,
    getAction
  } from '@/api/manage'
  import ipPanelPie from './ipPanelPie'
  export default {
    name: 'ipPanelList',
    props: ['treeFlag'],
    components: {
      ipPanelPie,
    },
    mixins: [JeecgListMixin, WebsocketMessageMixin],
    data() {
      return {
        tableCurrent: 1,
        tableTotal: 0,
        pageAuditId: '',
        doubt: false,
        networkSegment: '',
        remark: '',
        cron: '',
        createTime: '',
        ipSegsentId: '',
        auditId: '',
        flashingLists: [],
        occupancyRatioData: [],
        disableSubmit: false,
        num: 0,
        colorDesList: [{
            color: '#8eacff',
            describe: '保留：已分配未启用',
          },
          {
            // color:'#5b8ff9',
            color: '#f5f5f5',
            describe: '未使用：ping不通',
          },
          {
            color: '#5ad8a6',
            describe: '已使用：能ping通',
          },
          {
            color: '#389e0d',
            describe: '正常：IP和MAC都在白名单内',
          },
          {
            color: '#ffa940',
            describe: '非法占用：IP或MAC只有一个在白名单中',
          },
          {
            color: '#8b8f00',
            describe: '未获取到MAC：没有获取到MAC值',
          },
          {
            color: '#ffc0cb',
            describe: '非法篡改：IP和MAC都不在白名单内',
          },
          {
            color: '#ffffff',
            describe: '数据为零时则在饼状图表中不显示',
          }
        ],
        ipDesList: [{
            color: '#8eacff',
            describe: '保&nbsp;留',
          },
          {
            //color:'#fff',
            color: '#fafafa',
            describe: '未使用',
          },
          {
            color: '#389e0d',
            describe: '正常接入',
          },
          {
            color: ' #ffa940',
            describe: '非法占用',
          },
          {
            color: '#989d00',
            describe: '未获取到MAC',
          },
          {
            color: '#ffc0cb',
            describe: '非法篡改',
          }
        ],
        // 表头
        columns: [{
            title: 'IP地址',
            align: 'center',
            dataIndex: 'ipAddress',
          },
          {
            title: 'MAC地址',
            align: 'center',
            dataIndex: 'macCode',
          },
          {
            title: 'IP地址状态',
            align: 'center',
            dataIndex: 'ipStateText',
          },
          {
            title: '使用状态',
            align: 'center',
            dataIndex: 'occupiedText',
          },
          {
            title: '创建人',
            align: 'center',
            dataIndex: 'createByName',
          },
          {
            title: '创建时间',
            align: 'center',
            dataIndex: 'createTime',
          },
        ],
        url: {
          rescan: '/devops/ip/auditTask/execute',
          list: '/devops/ip/auditResultDetail/list',
          flashingList: '/devops/ip/board/querySegmentDetail',
          occupancyRatio: '/devops/ip/board/occupancyRatio',
          queryById: '/devops/ip/auditTask/queryById',
        },
        chartData: {},
        chartDataType: [{
            name: '未使用',
            field: 'notUsed'
          },
          {
            name: '已使用',
            field: 'used'
          },
          {
            name: '正常',
            field: 'normal'
          },
          {
            name: '非法占用',
            field: 'illegalOccupation'
          },
          {
            name: '未获取到MAC',
            field: 'noGetMAC'
          },
          {
            name: '非法篡改',
            field: 'illegalFalsify'
          },
        ],
        colorObj: {
          colorList1: [{
              // color1: '#5B8FF9',
              // color2: '#5B8FF9'
              color1: '#f5f5f5',
              color2: '#f5f5f5'
            },
            {
              color1: '#5AD8A6',
              color2: '#5AD8A6'
            }
          ],
          colorList2: [{
              color1: '#389E0D',
              color2: '#389E0D'
            },
            {
              color1: '#FFA940',
              color2: '#FFA940'
            },
            {
              color1: '#8b8f00',
              color2: '#8b8f00'
            },
            {
              color1: '#ffc0cb',
              color2: '#ffc0cb'
            }
          ]
        },
        disableMixinCreated: true
      }
    },
    computed: {
      rowCount: function () {
        return Math.ceil(this.tableData.length / 2)
      },
    },
    watch: {
      treeFlag: function (n, o) {
        this.remark = n.remark ? n.remark : ''
        this.createTime = n.createTime ? n.createTime : ''
        this.ipSegsentId = n.id ? n.id : ''
        this.auditId = n.auditId ? n.auditId : ''
        this.disableSubmit = false
        if (n.auditId) {
          this.getFlashingLists(n.auditId)
          this.getQuery(n.auditId)
          this.occupancyRatio(n.auditId)
          this.refreshData(this.auditId)
        }
      },
    },
    methods: {
      tableChange(page) {
        this.getFlashingLists(this.pageAuditId)
      },

      getQuery(id) {
        getAction(this.url.queryById, {
          id: id
        }).then((res) => {
          this.cron = res.result.cron
          this.networkSegment = res.result.auditObjectText
        })
      },
      handleOk() {
        this.closedrawer()
      },
      explain() {
        this.doubt = true
      },
      closedrawer() {
        this.doubt = false
      },
      //去掉默认创建时间排序
      getQueryParams() {
        //获取查询条件
        var param = Object.assign({}, this.queryParam)
        param.field = this.getQueryField()
        param.pageNo = this.ipagination.current
        param.pageSize = this.ipagination.pageSize
        return filterObj(param)
      },
      occupancyRatio(auditId) {
        getAction(this.url.occupancyRatio, {
          segmentId: this.ipSegsentId,
          auditId: auditId,
        }).then((res) => {
          if (res.success) {
            let values = []
            for (let i = 0; i < this.chartDataType.length; i++) {
              let obj = {}
              let key = this.chartDataType[i].field
              if (res.result.hasOwnProperty(key)) {
                obj = {
                  name: this.chartDataType[i].name,
                  value: res.result[key] == '0' ? '' : res.result[key]
                }
                values.push(obj)
              }
            }
            this.chartData = {
              data1: values.slice(0, 2),
              data2: values.slice(2, 6)
            }
            this.occupancyRatioData = res.result
          }
        })
      },
      //获取闪烁
      getFlashingLists(auditId) {
        this.pageAuditId = auditId
        getAction(this.url.flashingList, {
          segmentId: this.ipSegsentId,
          auditId: this.pageAuditId,
          pageNo: this.tableCurrent
        }).then((res) => {
          if (res.success) {
            this.flashingLists = res.result.records
            this.tableTotal = res.result.total
            this.tableCurrent = res.result.current
          }
        })
      },
      refreshData(auditId) {
        this.queryParam.auditId = this.auditId
        this.queryParam.segmentId = this.ipSegsentId
        this.queryParam.ipAddress = ''
        this.loadData(1)
      },
      loadData(arg) {
        //加载数据 若传入参数1则加载第一页的内容
        if (arg === 1) {
          this.ipagination.current = 1
        }
        var params = this.getQueryParams() //查询条件
        this.loading = true
        getAction(this.url.list, params).then((res) => {
          if (res.success && res.result) {
            this.dataSource = res.result.records || res.result
            if (this.dataSource.length < 9) {
              this.clientHeight = false
            }
            this.ipagination.total = res.result.total ? res.result.total : 0
          }
          if (res.code === 510) {
            this.$message.warning(res.message)
          }
          this.loading = false
        })
      },
      onSearch(value) {},
      onRescan() {
        this.num = 0
        this.disableSubmit = false
        const that = this
        let formData = {
          id: this.auditId,
        }
        getAction(this.url.rescan, formData, 'get').then((res) => {
          if (res.success) {
            that.$message.success(res.message)
            that.refreshData()
          } else {
            that.$message.warning(res.message)
          }
        })
      },
      //接收
      websocketOnmessage: function (e) {
        if (e.data === "HeartBeat") return;
        var data = eval('(' + e.data + ')') //解析对象
        if (data.messageType === 'ipManagerReal') {
          if (data.data === 'ok') {
            this.refreshData()
            this.getFlashingLists()
            this.occupancyRatio()
            this.num = this.num + 1
          }
        }
        if (this.num > 1) {
          this.disableSubmit = false
        }
      },
    },
  }
</script>
<style lang="less" scoped>
  @import "~@assets/less/scroll.less";

  .colhead {
    display: flex;
    justify-content: space-between;
    padding: 24px;
    background-color: #ffffff;
    margin-bottom: 12px;
    margin-right: 6px;
  }

  .colBox {
    height: 302px;
    overflow: hidden;
    overflow-x: auto;
    white-space: nowrap;

    .colBoxChild {
      background-color: #ffffff;
      padding: 24px;
      height: 100%;
      display: flex;
      flex-flow: column nowrap;
      justify-content: start;
      align-items: start;

      .colBoxDiv {
        font-size: 1.3em;
        padding: 6px 0px 6px 0px;
      }
    }
  }

  .colBoxFo {
    background-color: #ffffff;
    position: relative;
    margin-top: 12px;
    padding: 24px;
    overflow: hidden;
    white-space: nowrap;

    .ipDes {
      display: flex;
      justify-content: start;
      align-items: center;
      flex-flow: row nowrap;

      .ipDesBox {
        margin-right: 24px;
        display: flex;
        justify-content: start;
        align-items: center;
        flex-flow: row nowrap;

        .box {
          width: 26px;
          height: 20px;
          display: inline-block;
          margin-right: 8px;
          border: 1px solid #e8e8e8;
        }
      }
    }

    .ipBox {
      margin-top: 16px;
      overflow-x: auto;

      .mailTable {
        font-size: 14px;
        color: #71787e;
      }

      .mailTable,
      .mailTable tr,
      .mailTable tr td {
        border: 1px solid #e6eaee;
      }

      .mailTable tr td {
        border: 1px solid #e6eaee;
        line-height: 35px;
        box-sizing: border-box;
        padding: 0 10px;
      }

      .tableBox {
        width: 3%;
        height: 38px;
        text-align: center;
      }

      .column {
        background-color: #eff3f6;
        color: #393c3e;
        text-align: center;
        width: 11%;
      }
      .boxTw {
        //background-color: #fff;
        background-color: #fafafa;
        //background-color: #f0f5f0;
        border: 1px solid #e8e8e8;
      }

      .boxTh {
        background-color: #389e0d;
        color: #fff;
      }

      .boxOn {
        background-color: #8eacff !important;
        color: #fff;
      }

      .boxFo {
        background-color: #ffa940 !important;
        color: #fff;
      }

      .boxFi {
        background-color: #989d00 !important;
        color: #fff;
      }

      .boxsix {
        background-color: pink !important;
        color: #fff;
      }
    }
  }

  .colBoxFi {
    background-color: #ffffff;
    margin-top: 12px;
    padding: 24px 24px 8px 24px;

    .keyword {
      display: flex;
      white-space: nowrap;
      width: 300px;
      line-height: 32px;
    }
  }
</style>