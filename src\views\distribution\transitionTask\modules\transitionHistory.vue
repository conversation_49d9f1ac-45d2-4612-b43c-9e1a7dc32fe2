<template>
  <a-row :gutter='10' style='height: 100%' class='vScroll'>
    <a-col style='width: 100%; height: 100%; display: flex; flex-direction: column'>
      <!-- 查询区域 -->
<!--      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <div class='table-page-search-wrapper'>
          <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
            <a-row :gutter='24' ref='row'>
              <a-col :span='spanValue'>
                <a-form-item label='名称'>
                  <a-input :maxLength='maxLength' placeholder='请输入名称' v-model='queryParam.taskName' :allowClear='true'
                           autocomplete='off' />
                </a-form-item>
              </a-col>
              <a-col :span='colBtnsSpan()'>
                <span class='table-page-search-submitButtons'
                      :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button class='btn-search btn-search-style' type='primary' @click='searchQuery'>查询</a-button>
                  <a-button class='btn-reset btn-reset-style' @click='searchReset'>重置</a-button>
                  <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>-->

      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <!-- 操作按钮区域 -->
<!--        <div class='table-operator table-operator-style'>
          <a-button class='btn-add' @click='handleAdd'>新增</a-button>
          <a-dropdown v-if='selectedRowKeys.length > 0'>
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item key='1' @click='batchDel'>删除</a-menu-item>
            </a-menu>
            <a-button>批量操作
              <a-icon type='down' />
            </a-button>
          </a-dropdown>
        </div>-->

        <!-- table区域-begin -->
        <a-table ref='table' bordered :rowKey='(record,index)=>{return record.id}' :columns='columns'
                 :dataSource='dataSource' :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
                 :pagination='ipagination'
                 :loading='loading'
                 @change='handleTableChange'>
          <!-- 字符串超长截取省略号显示-->
          <template slot='index' slot-scope='text,record,index'>
            <span>{{ index + 1 }}</span>
          </template>
          <span slot='templateContent' slot-scope='text'>
            <j-ellipsis :value='text' :length='25' />
          </span>
          <span slot='action' slot-scope='text, record' class='caozuo' style='padding-top: 10px;padding-bottom: 10px;'>
            <a @click='handleDetail(record)'>查看</a>
<!--            <a-divider type='vertical' />
            <a-popconfirm title='确定删除吗?' @confirm='() => handleDelete(record.id)'>
              <a>删除</a>
            </a-popconfirm>-->
          </span>
          <template slot='tooltip' slot-scope='text'>
            <a-tooltip placement='topLeft' :title='text' trigger='hover'>
              <div class='tooltip'>
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
      </a-card>
    </a-col>
    <transition-detail-modal ref='detailModal'></transition-detail-modal>
  </a-row>
</template>

<script>
import {
  JeecgListMixin
} from '@/mixins/JeecgListMixin'
import transitionDetailModal from '@views/distribution/transitionTask/modules/transitionDetailModal.vue'
import {
  deleteAction,
  getAction,
  putAction
} from '@/api/manage'
import {
  YqFormSearchLocation
} from '@/mixins/YqFormSearchLocation'
import { historyData } from "@/views/distribution/transitionTask/mock/historyData"
export default {
  name: 'transitionHistory',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  props: {
    jobId: {
      type:String,
      default: () => {
        return ''
      }
    }
  },
  components: {
    transitionDetailModal
  },
  data() {
    return {
      // maxLength:50,
      formItemLayout: {
        labelCol: {
          style: 'width:80px'
        },
        wrapperCol: {
          style: 'width:calc(100% - 80px)'
        }
      },
      // 表头
      columns: [
        {
          title: '序号',
          dataIndex: 'index',
          scopedSlots: {
            customRender: 'index'
          },
          customCell: () => {
            let cellStyle = 'width:60px;text-align: center'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '开始时间',
          dataIndex: 'startTime',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 100px;max-width:260px'
            return { style: cellStyle }
          },
        },
        {
          title: '执行结果',
          dataIndex: 'transferResult',
          customRender: (text) => {
            return text == 1 ? '成功' : '失败'
          }
        },
        {
          title: '源数据库地址',
          dataIndex: 'sourceDatabaseUrl',
        },{
          title: '源bucket桶',
          dataIndex: 'sourceDatabaseBucket',
        },
        {
          title: '目标数据库地址',
          dataIndex: 'targetDatabaseUrl',
        }, {
          title: '目标bucket桶',
          dataIndex: 'targetDatabaseBucket',
        },
        //
        // {
        //   title: '迁移文件数量',
        //   dataIndex: 'fileCount',
        // },
         {
          title: '结束时间',
          dataIndex: 'endTime',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 100px;max-width:260px'
            return { style: cellStyle }
          },
        },
        {
          title: '操作',
          align: 'center',
          width: 180,
          fixed: 'right',
          dataIndex: 'action',
          scopedSlots: {
            customRender: 'action'
          }
        }
      ],
      url: {
        list: '/distributedStorage/transfer/jobHistory',
        delete: '',
        deleteBatch: ''
      },
      disableMixinCreated: true,
    }
  },
  created() {
    this.queryParam.jobId = this.jobId;
    this.loadData()
    // this.dataSource = historyData;
  },
  mounted() {
  },
  computed: {
    importExcelUrl: function() {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    }
  },
  methods: {
    handleDelete: function(id) {
      return;
      if (!this.url.delete) {
        this.$message.error('请设置url.delete属性!')
        return
      }
      var that = this
      deleteAction(that.url.delete, { ids: id }).then((res) => {
        if (res.success) {
          //重新计算分页问题
          that.reCalculatePage(1)
          that.$message.success(res.message)
          that.loadData()
        } else {
          that.$message.warning(res.message)
        }
      })
    },
    handleDetail(record) {
      this.$refs.detailModal.open(record)
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';

/deep/ .ant-table-tbody .ant-table-row td {
  padding-top: 5px;
  padding-bottom: 5px;
}
</style>