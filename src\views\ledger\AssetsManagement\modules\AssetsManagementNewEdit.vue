<template>
  <a-modal :title="title"
           :width="modalWidth"
           :visible="visible"
           :confirmLoading="confirmLoading"
           @cancel="handleCancel"
           @ok="handleOk"
           okText=""
           cancelText="关闭"
           wrapClassName="ant-modal-cust-warp"
           style="height:80%;"
           :centered="true">
    <a-tabs :animated="false" defaultActiveKey="1"
            @change="callback">
      <a-tab-pane tab="资产信息"
                  key="1">
        <a-form :form="form">
          <div style="text-align:center;font-size:18px;margin-bottom: 10px;">资产基本信息</div>
          <div>
            <a-row>
              <a-col :span="12">
                <a-form-item :labelCol="labelCol"
                             :wrapperCol="wrapperCol"
                             label="资产名称">
                  <a-input placeholder="请输入资产名称"
                           v-decorator="['']" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item :labelCol="labelCol"
                             :wrapperCol="wrapperCol"
                             label="质保开始时间">
                  <a-input placeholder="请输入"
                           v-decorator="['']" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col :span="12">
                <a-form-item :labelCol="labelCol"
                             :wrapperCol="wrapperCol"
                             label="资产类型">
                  <a-input placeholder="请输入资产类型"
                           v-decorator="['']" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item :labelCol="labelCol"
                             :wrapperCol="wrapperCol"
                             label="质保期限(月)">
                  <a-input placeholder="请输入质保期限（月）"
                           v-decorator="['']" />
                  <!-- <a-select placeholder=""
                        v-model="nodeType">
                <a-select-option key="2" value="2"></a-select-option>
                <a-select-option key="3" value="3"></a-select-option>
              </a-select> -->
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item :labelCol="labelCol"
                             :wrapperCol="wrapperCol"
                             label="资产编号">
                  <a-input placeholder="请输入资产编号"
                           v-decorator="['']" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item :labelCol="labelCol"
                             :wrapperCol="wrapperCol"
                             label="保修单位">
                  <a-input placeholder="保修单位"
                           v-decorator="['']" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col :span="12">
                <a-form-item :labelCol="labelCol"
                             :wrapperCol="wrapperCol"
                             label="ip地址">
                  <a-input placeholder="请输入ip地址"
                           v-decorator="['']" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item :labelCol="labelCol"
                             :wrapperCol="wrapperCol"
                             label="保修单位电话">
                  <a-input placeholder="请输入保修单位电话"
                           v-decorator="['']" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col :span="12">
                <a-form-item :labelCol="labelCol"
                             :wrapperCol="wrapperCol"
                             label="资产状态">
                  <a-input placeholder="请输入资产状态"
                           v-decorator="['']" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item :labelCol="labelCol"
                             :wrapperCol="wrapperCol"
                             label="保修联系人">
                  <a-input placeholder="请输入保修联系人"
                           v-decorator="['']" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col :span="12">
                <a-form-item :labelCol="labelCol"
                             :wrapperCol="wrapperCol"
                             label="添加方式">
                  <a-input placeholder="请输入添加方式"
                           v-decorator="['']" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item :labelCol="labelCol"
                             :wrapperCol="wrapperCol"
                             label="保修人电话">
                  <a-input placeholder="请输入保修人电话"
                           v-decorator="['']" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col :span="12">
                <a-form-item :labelCol="labelCol"
                             :wrapperCol="wrapperCol"
                             label="归属地">
                  <a-input placeholder="请输入归属地"
                           v-decorator="['']" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item :labelCol="labelCol"
                             :wrapperCol="wrapperCol"
                             label="生产厂商">
                  <a-input placeholder="请输入生产厂商"
                           v-decorator="['']" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col :span="12">
                <a-form-item :labelCol="labelCol"
                             :wrapperCol="wrapperCol"
                             label="单位">
                  <a-input placeholder="请输入单位"
                           v-decorator="['']" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item :labelCol="labelCol"
                             :wrapperCol="wrapperCol"
                             label="型号">
                  <a-input placeholder="请输入型号"
                           v-decorator="['']" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col :span="12">
                <a-form-item :labelCol="labelCol"
                             :wrapperCol="wrapperCol"
                             label="部门">
                  <a-input placeholder="请输入部门"
                           v-decorator="['']" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item :labelCol="labelCol"
                             :wrapperCol="wrapperCol"
                             label="是否过保">
                  <a-input placeholder="请输入是否过保"
                           v-decorator="['']" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col :span="12">
                <a-form-item :labelCol="labelCol"
                             :wrapperCol="wrapperCol"
                             label="最近操作人">
                  <a-input placeholder="请输入最近操作人"
                           v-decorator="['']" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item :labelCol="labelCol"
                             :wrapperCol="wrapperCol"
                             label="最近修改日期">
                  <a-input placeholder="请输入最近修改日期"
                           v-decorator="['']" />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
          <a-divider />
          <div style="text-align:center;font-size:18px;margin-bottom: 10px;">资产变更</div>
          <a-row>
            <a-col :span="12">
              <a-form-item :labelCol="labelCol"
                           :wrapperCol="wrapperCol"
                           label="采购日期">
                <a-date-picker @change="onChange"
                               style="" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item :labelCol="labelCol"
                           :wrapperCol="wrapperCol"
                           label="采购人">
                <a-input placeholder="请输入采购人"
                         v-decorator="['']" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item :labelCol="labelCol"
                           :wrapperCol="wrapperCol"
                           label="下发日期">
                <a-date-picker @change="onChange" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item :labelCol="labelCol"
                           :wrapperCol="wrapperCol"
                           label="属地">
                <a-input placeholder="请输入属地"
                         v-decorator="['']" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item :labelCol="labelCol"
                           :wrapperCol="wrapperCol"
                           label="部署日期">
                <a-date-picker @change="onChange"
                               style="" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item :labelCol="labelCol"
                           :wrapperCol="wrapperCol"
                           label="单位">
                <a-input placeholder="请输入单位"
                         v-decorator="['']" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item :labelCol="labelCol"
                           :wrapperCol="wrapperCol"
                           label="上线日期">
                <a-date-picker @change="onChange"
                               style="" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item :labelCol="labelCol"
                           :wrapperCol="wrapperCol"
                           label="使用人">
                <a-input placeholder="请输入使用人"
                         v-decorator="['']" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item :labelCol="labelCol"
                           :wrapperCol="wrapperCol"
                           label="暂存日期">
                <a-date-picker @change="onChange"
                               style="" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item :labelCol="labelCol"
                           :wrapperCol="wrapperCol"
                           label="暂存到">
                <a-input placeholder="请输入"
                         v-decorator="['']" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item :labelCol="labelCol"
                           :wrapperCol="wrapperCol"
                           label="转移扶贫日期">
                <a-date-picker @change="onChange"
                               style="" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item :labelCol="labelCol"
                           :wrapperCol="wrapperCol"
                           label="转移到">
                <a-input placeholder="请输入"
                         v-decorator="['']" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item :labelCol="labelCol"
                           :wrapperCol="wrapperCol"
                           label="销毁日期">
                <a-date-picker @change="onChange"
                               style="" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item :labelCol="labelCol"
                           :wrapperCol="wrapperCol"
                           label="销毁备注">
                <a-input placeholder="请输入销毁备注"
                         v-decorator="['']" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-tab-pane>
      <a-tab-pane tab="状态变更"
                  key="2"
                  forceRender>
        <a-card style="margin-bottom:10px">
          <a-row>
            <a-col :span="6">状态变更</a-col>
            <a-col :span="6">操作员：Admin</a-col>
            <a-col :span="6">采购</a-col>
            <a-col :span="6">操作时间：2019-02-21 08:51:42</a-col>
          </a-row>
          <a-divider />
          <a-row>
            <a-col>1.状态从入库更改为出库</a-col>
            <a-col>2.指派给用户Admin</a-col>
            <a-col>状态时间：2019-02-21 08:51:42</a-col>
          </a-row>
        </a-card>
        <a-card style="margin-bottom:10px">
          <a-row>
            <a-col :span="6">状态变更</a-col>
            <a-col :span="6">操作员：Admin</a-col>
            <a-col :span="6">已下发</a-col>
            <a-col :span="6">操作时间：2019-02-21 08:51:42</a-col>
          </a-row>
          <a-divider />
          <a-row>
            <a-col>1.状态从入库更改为出库</a-col>
            <a-col>2.指派给用户Admin</a-col>
            <a-col>状态时间：2019-02-21 08:51:42</a-col>
          </a-row>
        </a-card>
        <a-card style="margin-bottom:10px">
          <a-row>
            <a-col :span="6">状态变更</a-col>
            <a-col :span="6">操作员：Admin</a-col>
            <a-col :span="6">已部署</a-col>
            <a-col :span="6">操作时间：2019-02-21 08:51:42</a-col>
          </a-row>
          <a-divider />
          <a-row>
            <a-col>1.状态从入库更改为出库</a-col>
            <a-col>2.指派给用户Admin</a-col>
            <a-col>状态时间：2019-02-21 08:51:42</a-col>
          </a-row>
        </a-card>
      </a-tab-pane>
    </a-tabs>
  </a-modal>
</template>
<script>
export default {
  name: 'AssetsManagementModal',
  data() {
    return {
      title: '操作',
      visible: false,
      confirmLoading: false,
      /* 弹框宽 */
      modalWidth: '50%',
      form: this.$form.createForm(this),
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      showHeader: false,
      columns: [
        { title: 'Name', dataIndex: 'name', key: 'name', width: 100 },
        { title: 'actionPeople', dataIndex: 'actionPeople', key: 'actionPeople', width: 150 },
        { title: 'status', dataIndex: 'status', key: 'status' },
        { title: 'time', dataIndex: 'time', key: 'time' }
      ]
    }
  },
  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      this.model = Object.assign({}, record)
      this.visible = true
      //编辑页面禁止修改角色编码
      if (this.model.id) {
        this.roleDisabled = true
      } else {
        this.roleDisabled = false
      }
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let formData = Object.assign(this.model, values)
          // let ids = formData.id
          // let remarkss =  formData.remarks
        }
      })
    },
    handleCancel() {
      this.close()
    },
    callback(key) {
    },
    //单个日期框
    onChange(date, dateString) {
    }
  }
}
</script>
<style scoped>
body {
  height: auto;
  overflow: hidden !important;
}
.ant-calendar-picker {
  width: 100%;
}
</style>
