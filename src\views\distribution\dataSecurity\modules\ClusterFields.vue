<template>
  <div style="height: 100%">
    <!--按字段名称过滤字段-->
    <a-input-search placeholder='请输入' v-model='searchText' style='width: 100%;' />
    <!--呈现字段：选定字段、可用字段-->
    <div class='list-main'>
      <a-spin :spinning='loading'>
        <ul class="fields">
          <li
            :title="item.clusterName"
            v-for='item in filterList'
            :key='item.value'
            class='field-item'
            :class='item.id===clusterId?"active":""'
            @click='changeField(item)'>
           <span>
            <a-icon type="cluster"/>
            {{ item.clusterName }}
           </span>
          </li>
        </ul>
      </a-spin>
    </div>
  </div>
</template>
<script>
import { getAction } from '@api/manage'

export default {
  name: 'ClusterFields',
  props: {
    clusterUrl: {
      type: String,
      required:false,
      default: '/distributedStorage/cluster'
    }
  },
  data() {
    return {
      searchText: '',
      clusterList: [],
      clusterId:'',
      loading: false,
    }
  },
  watch: {},
  created() {
    this.getClusterList()
  },
  computed: {
    filterList() {
      return this.clusterList.filter(item => item.clusterName.indexOf(this.searchText) != -1)
    }
  },
  methods: {
    getClusterList() {
      this.loading=true
      getAction(this.clusterUrl).then((res) => {
        this.clusterList=[]
        if (res.success) {
          if (res.result.length > 0) {
            this.clusterList = res.result;
            this.clusterId=this.clusterList[0].id
            this.$emit('change-field',this.clusterList[0])
          }
          this.loading=false
        }else {
        this.$message.warning(res.message)
        }
      }).catch((err)=>{
        this.clusterList=[]
        this.loading=false
        this.$message.warning(err.message)
      })
    },
    /*根据搜索内容，过滤选定字段和可选字段*/
    changeField(item) {
      this.clusterId=item.id
      this.$emit('change-field',item)
    }
  }
}
</script>

<style scoped lang='less'>
.list-main {
  font-size: 14px;
  margin-top: 16px;
  height: calc(100% - 32px - 16px);
  overflow: hidden;
  overflow-y: auto;

  .fields {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    justify-content: start;
    align-items: start;
    flex-flow: column nowrap;

    .field-item {
      width: 100%;
      height: 24px;
      line-height: 24px;
      // border-bottom: 1px solid #e5e5e5;

      span{
        display: inline-block;
        width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        padding: 0 5px;
        border-radius: 2px;
      }
    }

    .field-item:hover,.field-item.active {
      span{
        color: #1890ff;
        background-color: rgb(236, 245, 255);
      }
    }
  }
}
</style>