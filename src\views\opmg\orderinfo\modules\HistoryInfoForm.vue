<template>
  <!-- <a-table
    :columns="columns"
    :data-source="data"
    ref="table"
    size="middle"
    bordered
    rowKey="id"
  > -->
  <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="data"
        :pagination="ipagination"
        :loading="loading"
        class="j-table-force-nowrap"
       >
  </a-table>
</template>
<script>
import { getAction} from '@/api/manage'
export default {
  // 关联配置项
  name: 'HistoryInfoForm',
  data() {
    return {
        columns: [
            {
                title: '时间',
                align: 'center',
                dataIndex: 'createTime'
            },
            {
                title: '操作人',
                align: 'center',
                dataIndex: 'operateUserName'
            },
            {
                title: '操作内容',
                align: 'center',
                dataIndex: 'operateContent'
            },
        ],
        data: [],
        loading:false,
        ipagination:{
          current: 1,
          pageSize: 10,
          pageSizeOptions: ['10', '20', '30'],
          showTotal: (total, range) => {
            return range[0] + "-" + range[1] + " 共" + total + "条"
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0
      },
        url:{list: "/operate_history/operateHistoryInfo/list",},
    }
  },
  methods: {
    getDataList(id) {

      this.loading = true;
      getAction(this.url.list, {businessId:id}).then((res) => {

        if (res.success) {
          this.data = res.result.records||res.result;
          this.ipagination.total = res.result.total;
          }
        if(res.code===510){
          this.$message.warning(res.message)
        }
        this.loading = false;
      })
      // getAction(this.url.list,{businessId:id}).then(res => {
      //   if (res.success) {
      //     this.loading = false;
      //     this.data = res.result;
      //     if (!res.result || res.result.length == 0) {
      //       this.$message.warning( "未找到历史数据");
      //     }
      //   }else {
      //     this.$message.error( res.message);
      //   }
      // });
    },
  }
}
</script>
<style scoped></style>
