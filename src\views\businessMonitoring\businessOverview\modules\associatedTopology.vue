<template>
  <div class="top-wrapper">
    <a-button class="btn" @click="showTopoList" type="primary" v-if="!isResizing">关联拓扑</a-button>
    <div class="tMap">
      <div class="tBtn">
        <a-popover title='备注'>
          <template slot='content'>
            <p>高亮的设备表示“主业务系统设备”</p>
          </template>
          <a-icon type='question-circle' theme='twoTone'
                  style='font-size: 18px; line-height: 45px;margin-left: 10px' />
        </a-popover>
        <a-button type="default" icon="retweet" v-if="!listPage" @click="switchPage">列表</a-button>
        <a-button type="default" icon="retweet" v-else @click="switchPage">拓扑</a-button>
        <a-button v-if="!listPage" type="default" icon="fullscreen" @click="switchFullScreen" >全屏</a-button>
        <!-- <a-button type="default" icon="fullscreen-exit" @click="switchFullScreen" v-else>恢复</a-button> -->
      </div>

      <div v-show="!listPage">
        <div v-if="businessInfo.topoId && !fullScreenOpenning" style="width: 100%; height: 400px">
          <vis-edit ref="bigScreen" operate="show" isBusiness @businessDetail="topoOpenPreview" :lighterSetting="lighterSetting"></vis-edit>
        </div>
        <div v-else>
          <a-empty description="未关联拓扑图"/>
        </div>
      </div>
      <div v-show="listPage">
        <associated-device-list
          :pageParam="pageParam"
          @OpenPreview="OpenPreview"
          :businessInfo="businessInfo"
        ></associated-device-list>
      </div>
    </div>
    <associated-topo-list ref="associatedTopoList" @ok="okHandle"></associated-topo-list>
    <associate-fullsreen ref="fullScreen" @close="fullScreenClose"></associate-fullsreen>
  </div>
</template>
<script>
import associatedDeviceList from './associatedDeviceList'
import AssociatedTopoList from './AssociatedTopoList.vue'
import VisEdit from '@/views/topo/nettopo/modules/VisEdit'
import AssociateFullsreen from './associateFullsreen.vue'
export default {
  name:'associatedTopology',
  components: {
    associatedDeviceList,
    AssociatedTopoList,
    VisEdit,
    AssociateFullsreen,
  },
  props: {
  },
  data() {
    return {
      listPage: false,
      isResizing: false,
      fullScreenOpenning:false,
      businessInfo: {},
      pageParam: {},
      lighterSetting: {
        isZoom: false, // 是否设置画布的缩放级别
        isCenter:false, // 是否将指定的点与视口中心对齐
        fillColor: '#ff7500' // 指定的节点填充的颜色
      }
    }
  },
  created() {},
  mounted() {
   
  },
  methods: {
    okHandle(topoId,deviceCode){
      this.businessInfo.topoId = topoId;
      this.businessInfo.businessNode = deviceCode;
      this.createTopo();
      this.$message.success("请稍后操作刷新，更新数据！")
    },
    //渲染拓扑图
    createTopo() {
      if (!this.listPage && this.businessInfo.topoId) {
        this.$nextTick(()=>{
          this.$refs.bigScreen.createTopo(this.businessInfo.topoId, false, this.businessInfo.businessNode)
        })
      }
    },
    //展示关联拓扑图
    showTopoList() {
      let businesId = this.businessInfo.id;
      let topoId = this.businessInfo.topoId;
      this.$refs.associatedTopoList.show(businesId, topoId)
    },
    show(record, pageParam, listPage) {
      this.businessInfo = record
      this.pageParam = pageParam
      this.listPage = listPage
      this.$nextTick(()=>{
        this.createTopo();
      })
    },
    switchPage() {
      this.listPage = !this.listPage
      this.createTopo();
      this.$parent.listPage=this.listPage
    },
    fullScreenClose(){
      this.fullScreenOpenning = false;
      this.$nextTick(()=>{
        this.createTopo()
      })
    },
    switchFullScreen() {
      this.fullScreenOpenning = true;
      this.$refs.fullScreen.show(this.businessInfo.topoId,this.businessInfo)
      // this.isResizing = !this.isResizing
      // this.$emit('resize')
    },
    topoOpenPreview(record){
       let deviceInfo = {
        data: record,
        pageParam: {
          current:1,
          pageSize: 10,
        },
      }
      this.OpenPreview(deviceInfo)
    },
    OpenPreview(deviceInfo) {
      deviceInfo.listPage = this.listPage
      this.$emit('OpenPreview', deviceInfo)
    },
  },
}
</script>
<style scoped lang='less'>
.top-wrapper {
  width: 100%;
  display: flex;
  flex-flow: column nowrap;

  .btn {
    align-self: start;
    margin-bottom: 10px;
  }

  .tMap {
    background: #ffffff;
    border: 1px solid #e8e8e8;
    padding: 12px;
    flex: 1;

    .tBtn {
      text-align: right;

      button {
        margin-left: 12px;
        margin-bottom: 12px;
      }
    }
  }
}
</style>