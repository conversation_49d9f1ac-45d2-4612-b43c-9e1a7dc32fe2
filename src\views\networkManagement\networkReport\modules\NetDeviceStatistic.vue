<template>
  <div style='height: 100%'>
    <card-frame>
      <div slot='titleSlot' class='title'>
        <yq-icon class='icon' type="netDevice"></yq-icon>
        <span class='text'>设备统计</span>
      </div>
      <div slot='bodySlot'>
        <div class='body-box-wrapper' v-for='(items,index) in statisticData.columnData' :class='"items_"+index' :key='"items_"+index'>
          <div class='device-wrapper'>
            <yq-icon class='device-icon' :type="statisticData.iconName[index]"></yq-icon>
            <span class='device-text'>{{statisticData.typeText[index]}}</span>
          </div>
          <a-row class='data-wrapper'>
            <a-col :span='8' v-for='(count,idx) in items' :key='"item_"+idx'>
              <div class='data-column' :style='{borderRight:idx<items.length-1 ? "1px solid #e8e8e8" : "none"}'>
                <div class='item' :class='"item_"+idx'>
                  <span class='data-count'>{{count}}</span>
                  <span class='data-unit'>{{statisticData.unit[idx]}}</span></div>
                <div :class='[statisticData.columnText[idx].length<3 ? "column-text":""]'>{{statisticData.columnText[idx]}}</div>
              </div>
            </a-col>
          </a-row>
        </div>
      </div>
    </card-frame>
  </div>

<!--  <a-card
    class="tabs-card"
    :body-style="{paddingTop: '12px', paddingRight: '12px', paddingLeft: '12px', paddingBottom: '0px' }"
    :bordered="false"
    :head-style="{ paddingLeft: '11px', paddingRight: '11px', height: '44px' }"
  >
      <span slot="title" class="title">
        <yq-icon style="color: #409eff; fontSize: 30px" type="netDevice"></yq-icon>
        <yq-icon style="color: #409eff; fontSize: 30px" type="netAlarm"></yq-icon>
        <yq-icon style="color: #409eff; fontSize: 30px" type="netConfig"></yq-icon>
        <yq-icon style="color: #409eff; fontSize: 30px" type="netIP"></yq-icon>
        <yq-icon style="color: #409eff; fontSize: 30px" type="netChange"></yq-icon>

        <span style="margin-left: 8px">设备统计</span>
      </span>
    <div class='table-operator table-operator-style'>
      &lt;!&ndash; table区域-begin &ndash;&gt;
      <a-table
          ref='table'
          bordered
          rowKey='id'
          :columns='columns'
          :dataSource='dataSource'
          :scroll='dataSource.length>0?{x:"max-content"}:{}'
          :pagination='ipagination'
          :loading='loading'
          @change='handleTableChange'
        >
          <template slot='tooltip' slot-scope='text'>
            <a-tooltip placement='topLeft' :title='text' trigger='hover'>
              <div class='tooltip'>
                {{ text }}
              </div>
            </a-tooltip>
          </template>

        </a-table>
    </div>
    </a-card>-->
</template>
<script>
import yqIcon from '@comp/tools/SvgIcon'
import cardFrame from '@views/networkManagement/networkReport/modules/CardFrame.vue'
import { getAction } from '@api/manage'
export default {
  name: "NetDeviceStatistic",
  components: { yqIcon, cardFrame },
  data() {
    return {
      statisticData: {
        iconName: [],
        typeText: [],
        columnText: ['告警', '设备', '告警设备'],
        unit:['条','台','台'],
        columnData: [],
      },
      url: {
        list: '/net/device/overviewByCategory',
      },
      dictCode : 'Network'
    }
  },
  created() {
    this.getData()
  },
  methods:{
    getData(){
      getAction(this.url.list,{dictCode:this.dictCode}).then((res)=>{
        if(res.success){
          let lst=res.result
          if(lst.length>0){
            this.statisticData.typeText = res.result.map((item) => item.categoryName);
            this.statisticData.columnData = res.result.map((item) => [item.history,item.total,item.alarm]);
            this.statisticData.iconName = res.result.map((item) => (item.categoryName === "路由器" ? 'router' : 'switchboard'));
          }
        }
      })
    }
  }
}
</script>
<style scoped lang="less">
.items_0{
  margin-bottom: 18px;
}

.body-box-wrapper{
  display:flex;
  flexFlow:row nowrap;
  justify-content:start;
  align-items: center;
  padding: 31px 48px;
  border: 1px solid #e8e8e8;
  border-radius: 2px;
  box-shadow: 1px 2px 7px 3px rgba(157, 168, 186, 0.47);

  .device-wrapper{
    margin-right: 30px;
    display: flex;
    flex-flow: column nowrap;
    justify-content: center;
    align-items: center;

    .device-icon{
      color: #409eff;
      font-size: 40px;
    }

    .device-text{
      white-space: nowrap
    }
  }

  .data-wrapper{
    flex:auto;

    .data-column{
      width: 100%;
      display: flex;
      flex-flow: column nowrap;
      justify-content: center;
      align-items: center;

      .item_0{
        color: #d54e48;
      }
      .item_1{
        color: #589be0;
      }
      .item_2{
        color: #f2a158;
      }

      .item{
        padding:0px 8px;
        width: 100%;
        overflow: hidden;
        text-align: center;

        .data-count{
          max-width:calc(100% - 20px);
          height: 40px;
          line-height:45px;
          //font-size: 40px;
          //40/80
          font-size: 0.5rem;
          display: inline-block;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .data-unit{
          display: inline-block;
          height: 20px;
          line-height:15px;
          width: 20px;
        }
      }

      .column-text{
        letter-spacing: 4px
      }
    }
  }
}
</style>
