<template>
  <a-row :gutter='10' style='height: 100%' class='vScroll'>
    <a-col style='width: 100%; height: 100%; display: flex; flex-direction: column'>
      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <!-- table区域-begin -->
        <a-table ref='table' bordered :rowKey='(record,index)=>{return record.id}' :columns='columns'
                 :dataSource='dataSource' :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
                 :pagination='ipagination'
                 :loading='loading'
                 @change='handleTableChange'>
          <!-- 字符串超长截取省略号显示-->
          <template slot='index' slot-scope='text,record,index'>
            <span>{{ index + 1 }}</span>
          </template>
          <span slot='templateContent' slot-scope='text'>
            <j-ellipsis :value='text' :length='25' />
          </span>
          <span slot='action' slot-scope='text, record' class='caozuo' style='padding-top: 10px;padding-bottom: 10px;'>
            <a @click='handleDetail(record)'>查看</a>
            <!--            <a-divider type='vertical' />
                        <a-popconfirm title='确定删除吗?' @confirm='() => handleDelete(record.id)'>
                          <a>删除</a>
                        </a-popconfirm>-->
          </span>
          <template slot='tooltip' slot-scope='text'>
            <a-tooltip placement='topLeft' :title='text' trigger='hover'>
              <div class='tooltip'>
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
      </a-card>
    </a-col>
  </a-row>
</template>

<script>
import {
  JeecgListMixin
} from '@/mixins/JeecgListMixin'
import {
  deleteAction,
  getAction,
  putAction
} from '@/api/manage'
import {
  YqFormSearchLocation
} from '@/mixins/YqFormSearchLocation'
import { historyData } from "@/views/distribution/transitionTask/mock/historyData"
export default {
  name: 'transitionDetail',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  props: {
    jobId: {
      type:String,
      default: () => {
        return ''
      }
    }
  },
  components: {

  },
  data() {
    return {
      formItemLayout: {
        labelCol: {
          style: 'width:80px'
        },
        wrapperCol: {
          style: 'width:calc(100% - 80px)'
        }
      },
      // 表头
      columns: [
        {
          title: '序号',
          dataIndex: 'index',
          scopedSlots: {
            customRender: 'index'
          },
          customCell: () => {
            let cellStyle = 'width:60px;text-align: center'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: ' 被清除前文件路径',
          dataIndex: 'fileName',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 100px;max-width:260px'
            return { style: cellStyle }
          }
        },

        // {
        //   title: '操作',
        //   align: 'center',
        //   width: 180,
        //   fixed: 'right',
        //   dataIndex: 'action',
        //   scopedSlots: {
        //     customRender: 'action'
        //   }
        // }
      ],
      url: {
        list: '/distributedStorage/dump/jobHistory/detail',
        delete: '',
        deleteBatch: ''
      },
      disableMixinCreated: true,
    }
  },
  created() {
    this.queryParam.jobHistoryId = this.jobId;
    this.loadData()
    // this.dataSource = historyData;
  },
  mounted() {
  },
  computed: {
    importExcelUrl: function() {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    }
  },
  methods: {
    handleDelete: function(id) {
      return;
      if (!this.url.delete) {
        this.$message.error('请设置url.delete属性!')
        return
      }
      var that = this
      deleteAction(that.url.delete, { ids: id }).then((res) => {
        if (res.success) {
          //重新计算分页问题
          that.reCalculatePage(1)
          that.$message.success(res.message)
          that.loadData()
        } else {
          that.$message.warning(res.message)
        }
      })
    },
    handleDetail(record) {
      this.$refs.logDetail.show(record)
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';

/deep/ .ant-table-tbody .ant-table-row td {
  padding-top: 5px;
  padding-bottom: 5px;
}
</style>