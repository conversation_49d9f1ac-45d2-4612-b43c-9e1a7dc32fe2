import { DagreLayout } from '@antv/layout'
import Hierarchy from '@antv/hierarchy'
import {
  nodeConfig,
  edgeConfig,
  appNodeConfig,
  appEdgeConfig,
  getDeptNodeData,
  relatives,
  CreateLargeNode
} from '../models/autoTopoConfig'
import { getAction, postAction, httpAction } from '@/api/manage'
import { uuidX6 } from '@/utils/util'
import {
  queryDepartTreeList
} from '@/api/api'

export default {
  data() {
    return {
      deviceList: {
        nodes: [],
        edges: []
      },
      deviceCodes: [],
      autoVirtaulData: null
    }
  },
  methods: {
    // 点击自动生成
    autoClick() {
      let type = this.topoInfo.topoType
      if (type === '1') {
        this.$refs.businessType.show(this.globalGridAttr.topoConfig.busCode)
      } else if (type === '0') {
        this.netAutoCreate()
      } else if (type === '2') {
        this.creatDeptTopo(this.globalGridAttr.topoConfig.busCode)
      }
    },
    //单位拓扑图自动生成
    creatDeptTopo() {
      if (this.$refs.depNodeTree) {
        let nodes = this.$refs.depNodeTree.assetsCategoryTree
        let layoutResult = this.getHierarchyLayout(nodes[0])
        const model = { nodes: [], edges: [] }
        this.traverse(layoutResult, model)
        console.log("自动的 === ",layoutResult,model)
        this.graph.fromJSON(model)
        this.graph.zoomToFit({ padding: 30 })
        // 初始化设备状态
        this.initDeviceStatus()
      }

    },
    getHierarchyLayout(renderNodes) {
      //生成布局数据 在这里调整布局参数
      if (renderNodes.length === 0) return
      const result = Hierarchy.compactBox(renderNodes, {
        direction: 'V',//V
        getHeight() {
          return 60
        },
        getWidth() {
          return 60
        },
        getHGap() {
          return 60
        },
        getVGap() {
          return 60
        }
        // getSide: () => {
        //   return 'right'
        // },
      })
      return result
    },
    traverse(data, model) {
      //生成父子节点
      if (data) {
        let node = getDeptNodeData(data)
        model.nodes?.push(node)
      }
      //生成父子连线
      if (data.children) {
        data.children.forEach((item) => {
          model.edges?.push(edgeConfig({id:`${data.data.topoNodeId}`},{id:`${item.data.topoNodeId}`}))
          this.traverse(item, model)
        })
      }
    },
    //应用自动拓扑生成
    appAutoCreate(businessCode) {
      let nodesNum = this.graph.getNodes().length
      // console.log("点击生成应用拓扑图", businessCode)
      if (nodesNum > 0) {
        this.$confirm({
          content: '自动生成拓扑图将清空当前所有节点？',
          okText: '确认',
          cancelText: '取消',
          onOk: () => {
            this.graph.clearCells()
            this.getAppRelatives(businessCode)
          }
        })
      } else {
        this.getAppRelatives(businessCode)
      }

    },
    // 点击自动生成
    async netAutoCreate() {
      await getAction('/product/product/list', {
        displayName: '通用交换机',
        column: 'createTime',
        order: 'desc',
        field: 'id',
        pageNo: 1,
        pageSize: 8
      }).then(res => {
        if (res.success) {
          this.autoVirtaulData = res.result.records[0]
        }
      })
      // CreateLargeNode(this.graph);
      // return;
      let nodesNum = this.graph.getNodes().length
      if (nodesNum > 0) {
        this.$confirm({
          content: '自动生成拓扑图将清空当前所有节点？',
          okText: '确认',
          cancelText: '取消',
          onOk: () => {
            this.graph.clearCells()
            this.getDeviceRelatives()
          }
        })
      } else {
        this.getDeviceRelatives()
      }
    },
    // 从后台获取设备关系信息
    getDeviceRelatives() {
      this.deviceList.nodes = []
      this.deviceList.edges = []
      // 自动拓扑测试数据
      // this.getAllDeviceId(relatives)
      // this.generateAutoTopo()
      // return;
      // let loading = this.$loading({
      //     background: 'rgba(0,0,0,0.6)',
      //     text: '正在获取设备信息',
      // })
      this.spinning = true
      this.spinTip = '正在加载数据...'
      getAction('/topo/autoTopo/swTree')
        .then((res) => {
          this.spinning = false
          // console.log('获取到的设备关系信息', res)
          // 此处获取完设备关系开始生成自动拓扑
          this.getAllDeviceId(res)
          this.generateAutoTopo()
        })
        .catch((err) => {
          this.spinning = false
          // 无数据测试用demo
          // console.log('得到设备信息 === ', this.deviceList)
          this.$message.error('获取设备关系信息失败')
          console.log('获取数据出错', err)
        })
    },
    // 生成自动拓扑
    generateAutoTopo() {
      let infoPromises = this.deviceCodes.map((el) => {
        return getAction(this.url.deviceInfo, { deviceCode: el })
      })
      Promise.allSettled(infoPromises)
        .then((res) => {
          let deviceInfos = res
            .filter((el) => el.status === 'fulfilled')
            .map((el) => {
              return el.value.result
            })
          deviceInfos.forEach((element) => {
            let node = this.deviceList.nodes.find(dev => dev.data.deviceCode === element.code)
            if (node) {


              let icon = element.icons ? element.icons[0] : ''
              node.data.deviceId = element.id
              node.data.deviceCode = element.code
              node.data.deviceName = element.name
              node.attrs.label.text = element.name
              node.attrs.image['xlink:href'] = icon ? window._CONFIG['staticDomainURL'] + '/' + icon : ''

            }
          })
          this.dagreLayoutDo(true)
        })
        .catch((err) => {
          this.$message.error('生成拓扑失败！')
          console.log('获取设备信息出错', err)
        })
    },
    getAllDeviceId(list, pnode) {
      list.forEach((el) => {
        if (el.code) {
          this.deviceCodes.push(el.code)
        }
        let id = uuidX6()
        let shape = this.globalGridAttr.shapeNames[this.globalGridAttr.topoConfig.shapeType]
        let node = nodeConfig(shape, id, el.code, el.virtual ? true : false, el)
        if (el.virtual) {
          let icons = this.autoVirtaulData.icon.split(',')
          let icon = icons[0] || ''
          node.data.productId = this.autoVirtaulData.id
          node.data.productName = this.autoVirtaulData.displayName
          node.attrs.image['xlink:href'] = icon ? window._CONFIG['staticDomainURL'] + '/' + icon : ''
        }
        this.deviceList.nodes.push(node)
        if (pnode) {
          let edge = edgeConfig(pnode, node)
          this.deviceList.edges.push(edge)
        }
        if (el.children) {
          this.getAllDeviceId(el.children, node)
        }
      })
    },
    //获取拓扑应用的关系
    getAppRelatives(code) {
      getAction('/business/info/getAutoTopology', { code: code }).then(res => {
        if (res.success) {
          let relatives = res.result.data.getServiceTopology
          if (relatives.nodes.length > 0) {
            this.generateAppAutoTopo(relatives)
            this.globalGridAttr.topoConfig.busCode = code
          } else {
            this.$message.warning('没有相关业务节点！')
          }
        } else {
          this.$message.error('获取业务关系信息失败！')
        }
      })
    },
    //自动生成应用拓扑
    generateAppAutoTopo(relatives) {
      let shape = this.globalGridAttr.shapeNames[this.globalGridAttr.topoConfig.shapeType]
      this.deviceList.nodes = []
      this.deviceList.edges = []
      relatives.nodes.forEach(el => {
        let namestr = el.name
        if (el.type) {
          namestr = namestr + '_' + el.type
        }
        let node = appNodeConfig(shape, uuidX6(), namestr, el.id)
        this.deviceList.nodes.push(node)
      })
      relatives.calls.forEach(el => {
        let sourceNode = this.deviceList.nodes.find(snode => snode.data.relativesId === el.source)
        let targetNode = this.deviceList.nodes.find(tnode => tnode.data.relativesId === el.target)
        let edge = appEdgeConfig(sourceNode, targetNode)
        this.deviceList.edges.push(edge)
      })
      this.dagreLayoutDo()
      // console.log("开始自动生成应用拓扑图 === ", relatives)
    },
    //使用自动布局生成拓扑
    dagreLayoutDo(reverse) {
      let dagreData = {
        nodes: this.deviceList.nodes,
        edges: this.deviceList.edges
      }
      const dagreLayout = new DagreLayout({
        type: 'dagre',
        rankdir: 'TB',
        align: undefined,
        ranksep: 20,
        nodesep: 30
      })
      const model = dagreLayout.layout(dagreData)
      if (reverse) {
        model.edges.forEach(el => {
          let tem = el.source
          el.source = el.target
          el.target = tem
        })
      }

      this.graph.fromJSON(model)
      this.initDeviceStatus()
      // this.graph.zoomToFit({ padding: 15 })
      this.graph.centerContent()
    }
  }
}