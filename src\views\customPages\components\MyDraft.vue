<template>
  <a-card :loading='loadingBusiness' style='width: 100%; height: 100%'>
    <div slot='title'>
      <a-icon type='calendar' />
      我的草稿 【<a slot='extra' @click='goPage(4)'>{{ dataSourceCount4 }}</a>】
      <a-tooltip title='刷新'>
        <a-icon type='sync' style='color: #ccc; cursor: pointer; font-size: 14px' @click='loadBusiness' />
      </a-tooltip>
    </div>
    <a slot='extra' @click='goPage(4)'>更多
      <a-icon type='double-right' />
    </a>
    <a-table :body-style="{ overflowX: 'auto' }" rowKey='id' :columns='columnsbusiness' bordered
             :dataSource='dataSource4' :pagination='false'
             size='middle'>
              <span slot='action' slot-scope='text, record'>
                <a-popconfirm title='确定提交申请吗?' @confirm='() => handleApply(record)'>
                  <a>提交申请</a>
                </a-popconfirm>
                <a-divider type='vertical' />
                <a @click='handleEdit(record)'>编辑</a>
                <a-divider type='vertical' />
                <a-popconfirm title='确定删除吗?' @confirm='() => handleDeleteBusiness(record.id)'>
                  <a>删除</a>
                </a-popconfirm>
              </span>
    </a-table>
    <business-edit ref="businessEdit" :isview="isview"></business-edit>
  </a-card>
</template>
<script>
import BusinessEdit from '@views/flowable/process-business/modules/BusinessEdit.vue'
import { deleteAction, getAction, postAction } from '@api/manage'
import { filterDictTextByCache } from '@comp/dict/JDictSelectUtil'

export default {
  name: 'MyDraft',
  components: { BusinessEdit },
  data() {
    return {
      loadingBusiness: false,
      dataSource4: [],
      dataSourceCount4: 0,
      columnsbusiness: [
        {
          title: '所属流程',
          width: '31%',
          dataIndex: 'processName',
          align: 'center',
        },
        {
          title: '流程类型',
          dataIndex: 'processType',
          width: '15%',
          customRender: (text) => {
            //字典值替换通用方法
            return filterDictTextByCache('bpm_process_type', text)
          },
          align: 'center',
        },
        {
          title: '创建时间',
          align: 'center',
          width: '25%',
          dataIndex: 'createTime',
        },
        {
          title: '操作',
          dataIndex: 'action',
          width: '25%',
          scopedSlots: {
            customRender: 'action'
          },
          align: 'center',
        },
      ],
      url: {
        businessList: '/business/actZBusiness/list',
        applyUrl: '/business/actZBusiness/sbApply',
        deleteBusiness: '/business/actZBusiness/delete',
      },

      isview: undefined,
    }
  },
  created() {
    this.loadBusiness()
  },
  methods: {
    loadBusiness() {
      if (!this.url.businessList) {
        this.$message.error('请设置url.businessList!')
        return
      }
      this.loadingBusiness = true
      getAction(this.url.businessList, {
        pageNo: 1,
        pageSize: 6
      }).then((res) => {
        if (res.success) {
          this.dataSource4 = res.result.records || res.result
          this.dataSourceCount4 = res.result.total
        } else {
          this.$message.warning(res.message)
        }
        this.loadingBusiness = false
      })
    },
    handleApply(record) {
      const { id } = record || {};
      let formValue=JSON.parse(record.formValue)
      let assignNextNode=formValue.assignNextNode
      postAction(this.url.applyUrl, {
        id: id,assignNextNode:assignNextNode
      }).then((res) => {
        if (res.success) {
          this.$message.success(res.message)
        } else {
          this.$message.error('提交失败！')
        }
        this.loadBusiness()
        // this.loadDataMyProcess()
      })
    },
    handleEdit(record) {
      if (!record.id) {
        this.$message.error('申请不存在')
        return
      }
      this.isview = true
      this.$refs.businessEdit.init(record)
    },
    handleDeleteBusiness(id) {
      if (!this.url.deleteBusiness) {
        this.$message.error('请设置url.deleteBusiness!')
        return
      }
      var that = this
      deleteAction(that.url.deleteBusiness, {
        id: id
      }).then((res) => {
        if (res.success) {
          //重新计算分页问题
          that.$message.success(res.message)
          that.loadBusiness()
        } else {
          that.$message.warning(res.message)
        }
      })
    },
    goPage() {
      this.$router.push({
        path: '/flowable/processBusiness'
      })
    },
  },
}
</script>

<style scoped lang='less'>
/deep/ .ant-card-body{
  height: calc(100% - 56px);
  overflow-y: auto;
}
</style>
