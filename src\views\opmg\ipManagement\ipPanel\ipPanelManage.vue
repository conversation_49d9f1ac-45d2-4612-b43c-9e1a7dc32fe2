<template>
  <div style="height: 100%;display: flex;justify-content: space-between;flex-flow: row nowrap">
    <div class="listTree" id="listTree">
      <div class='tree-box'>
        <a-tree v-if='treeData.length>0' :tree-data="treeData" @select="onSelect"
          :replace-fields="{children:'children', title:'title', key:'newId'}"
          :default-expanded-keys="expandedTree(this.treeData)" :default-selected-keys="expandedTree(this.treeData)">
        </a-tree>
      </div>
    </div>
    <div class="listCont">
      <ipPanel-list ref="ipPanelList" :treeFlag="treeFlag">
      </ipPanel-list>
    </div>
  </div>
</template>
<script>
  import ipPanelList from './ipPanelList'
  import {
    getAction
  } from '@/api/manage'
  export default {
    name: 'ipPanelManage',
    components: {
      ipPanelList,
    },
    data() {
      return {
        treeFlag: {},
        treeData: [],
        url: {
          querySegmentList: '/devops/ip/board/querySegmentList'
        }
      }
    },
    computed: {},
    created() {
      this.getList()
    },
    methods: {
      onSelect(value, e) {
        if (e.selectedNodes.length == 0) {
          return
        } else {
          let node = e.selectedNodes[0].data.props
          this.treeFlag = {
            id: node.id,
            auditId: node.auditId
          }
        }
      },
      getList() {
        getAction(this.url.querySegmentList).then((res) => {
          this.treeData = res.result
          if (this.treeData.length > 0) {
            this.treeData.forEach(ele => {
              if (ele.children && ele.children.length > 0) {
                ele.children.forEach(cele => {
                  cele['newId'] = cele.id + '_' + cele.auditId // 后台返回的id不是唯一的key,构造一个全新的key
                })
              }
              ele['selectable'] = false // 设置父节点不可被选中
            });
            this.treeFlag = this.getFlag(this.treeData)
          }
        })
      },
      expandedTree(tree) {
        let treeKey = []
        if (tree.length > 0) {
          for (let i = 0; i < tree.length; i++) {
            if (tree[i].children.length > 0) {
              treeKey = [tree[i].children[0].newId]
              return treeKey
            }
          }
        }
      },
      getFlag(tree) {
        if (tree.length > 0) {
          for (let i = 0; i < tree.length; i++) {
            if (tree[i].children.length > 0) {
              let treeKey = tree[i].children[0]
              return treeKey
            } else {
              return {}
            }
          }
        }
      }
    },
  }
</script>
<style scoped lang='less'>
  .listTree {
    width: 284px;
    height: 100%;
    background-color: #ffffff;
    padding: 14px 24px 14px 10px;
    margin-right: 16px;
    overflow: hidden;

    .tree-box {
      height: 100%;
      overflow-x: auto;
      white-space: nowrap;
    }
  }

  .listCont {
    width: calc(100% - 284px - 16px);
    height: 100%;
  }
</style>