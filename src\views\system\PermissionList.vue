<template>
  <a-row :gutter='10' style='height: 100%' class='vScroll'>
    <a-col style='width: 100%; height: 100%; display: flex; flex-direction: column'>
      <a-card :bordered="false" :body-style="{ paddingBottom: '0' }" class='card-style'>
        <!-- <div v-if="this.rightcolval == 1" style="height: 30px"></div> -->
                <!-- 查询区域 -->
        <div class="table-page-search-wrapper">
          <!-- 搜索区域 -->
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="菜单名称">
                  <a-input placeholder="请输入"
                           :maxLength="maxLength"
                           v-model="queryParam.name"
                           :allowClear='true'
                           autocomplete='off'/>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="平台类型">
                  <a-tree-select
                    allowClear
                    :getPopupContainer="(node) => node.parentNode"
                    :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }"
                    v-model="queryParam.platformType"
                    placeholder="请选择平台类型"
                    :tree-data="platformTypes"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="colBtnsSpan()">
                <span
                  class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                >
                  <a-button
                    type="primary"
                    class="btn-search btn-search-style"
                    @click="searchQuery"
                    style="margin-left: 12px"
                    >查询</a-button
                  >
                  <a-button class="btn-reset btn-reset-style" @click="searchReset" style="margin-left: 8px"
                    >重置</a-button
                  >
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <!-- 操作按钮区域 -->
        <div class='table-operator table-operator-style'>
          <a-button @click='handleAdd'>新增</a-button>
          <a-button @click='batchDel' v-if='selectedRowKeys.length > 0'>批量删除</a-button>
        </div>

        <!-- table区域-begin -->
        <a-table
          :columns='columns'
          :pagination='false'
          :dataSource='dataSource'
          :loading='loading'
          :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
          :expandedRowKeys='expandedRowKeys'
          :rowSelection='{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }'
          @expandedRowsChange='handleExpandedRowsChange'
        >
        <span slot='action' slot-scope='text, record' class='caozuo'>
          <a href='javascript:;' @click='handleDetail(record)' style='color: #409eff'>查看</a>
          <a-divider type='vertical' />
          <a @click='handleEdit(record)'>编辑</a>
          <a-divider type='vertical' />
          <a-dropdown>
            <a class='ant-dropdown-link'> 更多 <a-icon type='down' /> </a>
            <a-menu slot='overlay'>
              <a-menu-item> </a-menu-item>
              <a-menu-item>
                <a href='javascript:;' @click='handleAddSub(record)' style='color: #409eff'>添加下级</a>
              </a-menu-item>
              <a-menu-item>
                <a href='javascript:;' @click='handleDataRule(record)' style='color: #409eff'>数据规则</a>
              </a-menu-item>

              <a-menu-item>
                <a-popconfirm title='确定删除吗?' @confirm='() => handleDelete(record.id)'>
                  <a style='color: #409eff'>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>
          <!-- 字符串超长截取省略号显示 -->
          <span slot='url' slot-scope='text'>
          <j-ellipsis :value='text' :length='25' />
        </span>
          <!-- 字符串超长截取省略号显示-->
          <span slot='component' slot-scope='text'>
          <j-ellipsis :value='text' />
        </span>
          <template slot='tooltip' slot-scope='text'>
            <a-tooltip placement='topLeft' :title='text' trigger='hover'>
              <div class='tooltip'>
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
        <!-- table区域-end -->
      </a-card>
      <permission-modal ref='modalForm' @ok='modalFormOk'></permission-modal>
      <permission-data-rule-list ref='PermissionDataRuleList' @ok='modalFormOk'></permission-data-rule-list>

    </a-col>
  </a-row>
</template>

<script>
import PermissionModal from './modules/PermissionModal'
import { getPermissionList } from '@/api/api'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import PermissionDataRuleList from './PermissionDataRuleList'
import JEllipsis from '@/components/jeecg/JEllipsis'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
import { filterObj } from '@/utils/util'
import { getAction, deleteAction } from '@/api/manage'


const columns = [
  {
    title: '菜单名称',
    dataIndex: 'name',
    key: 'name'
  },
  {
    title: '菜单类型',
    dataIndex: 'menuType',
    key: 'menuType',
    customRender: function(text) {
      if (text == 0) {
        return '菜单'
      } else if (text == 1) {
        return '菜单'
      } else if (text == 2) {
        return '按钮/权限'
      } else {
        return text
      }
    }
  },
  {
    title: '平台类型',
    dataIndex: 'platformTypeText',
    key: 'platformTypeText',
  },
  {
    title: 'icon',
    dataIndex: 'icon',
    key: 'icon'
  },
  {
    title: '组件',
    dataIndex: 'component',
    key: 'component',
    scopedSlots: { customRender: 'component' }
  },
  {
    title: '路径',
    dataIndex: 'url',
    key: 'url',
    scopedSlots: { customRender: 'url' }
  },
  {
    title: '排序',
    dataIndex: 'sortNo',
    key: 'sortNo'
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    scopedSlots: { customRender: 'action' },
    align: 'center',
    width: 170
  }
]

export default {
  name: 'PermissionList',
  mixins: [JeecgListMixin,YqFormSearchLocation],
  components: {
    PermissionDataRuleList,
    PermissionModal,
    JEllipsis
  },
  data() {
    return {
      maxLength:50,
      formItemLayout: {
        labelCol: {
          style: 'width:80px',
        },
        wrapperCol: {
          style: 'width:calc(100% - 80px)'
        }
      },
      platformTypes: [],
      queryParam: {},
      description: '这是菜单管理页面',
      // 表头
      columns: columns,
      loading: false,
      // 展开的行，受控属性
      expandedRowKeys: [],
      url: {
        list: '/sys/permission/list',
        delete: '/sys/permission/delete',
        deleteBatch: '/sys/permission/deleteBatch',
        platformTypeUrl: '/sys/permission/platformTypeList',
        platformTypeText: '/sys/permission/platformTypeText',
      },
      sysType: window._CONFIG['system_Type'],
      // platformType: window._CONFIG['platform_Type']
    }
  },
  created() {
    this.getSelectTree()
  },
  methods: {
    searchQuery(){
      this.loadData();
    },

    loadData() {
      this.dataSource = []
      let params = this.getQueryParams() //查询条件
      getPermissionList(params).then((res) => {
        if (res.success) {
          this.dataSource = res.result
        }
      })
    },
    getQueryParams() {
      var param = Object.assign({}, this.queryParam, this.isorter)
      param.field = this.getQueryField()
      //param.pageNo = this.ipagination.current
      //param.pageSize = this.ipagination.pageSize
      // if (this.superQueryParams) {
      //   param['superQueryParams'] = encodeURI(this.superQueryParams)
      //   param['superQueryMatchType'] = this.superQueryMatchType
      // }
      return filterObj(param)
    },
    // 打开数据规则编辑
    handleDataRule(record) {
      this.$refs.PermissionDataRuleList.edit(record)
    },
    handleAddSub(record) {
      this.$refs.modalForm.title = '添加子菜单'
      this.$refs.modalForm.localMenuType = 1
      this.$refs.modalForm.disableSubmit = false
      this.$refs.modalForm.edit({ platformType:record.platformType,status: '1', permsType: '1', route: true, parentId: record.id })
    },
    handleExpandedRowsChange(expandedRows) {
      this.expandedRowKeys = expandedRows
    },
    getSelectTree() {
      getAction(this.url.platformTypeUrl).then((res) => {
        this.platformTypes = res.result
      })
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
</style>