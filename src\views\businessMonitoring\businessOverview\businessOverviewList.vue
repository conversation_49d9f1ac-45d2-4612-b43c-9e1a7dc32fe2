<template>
  <div style='height: 100%'>
    <a-row class='row-top vScroll'>
      <a-col class='col-top'>
        <!-- 查询区域 -->
        <a-card :bordered='false' :bodyStyle="{ paddingBottom: '4px' }" style='margin-left: 16px; margin-right: 16px'>
          <div class='table-page-search-wrapper'>
            <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
              <a-row :gutter='24' ref='row'>
                <a-col :span='spanValue'>
                  <a-form-item label='业务名称'>
                    <a-input :maxLength='maxLength' placeholder='请输入业务名称' v-model='queryParam.businessName' :allowClear='true' autocomplete='off'>
                    </a-input>
                  </a-form-item>
                </a-col>
                <a-col :span='spanValue'>
                  <a-form-item label='业务状态'>
                    <j-dict-select-tag v-model="queryParam.businessState" placeholder="请选择业务状态" dictCode="business_state" />
                  </a-form-item>
                </a-col>
                <a-col :span='spanValue'>
                  <a-form-item label='业务类型'>
                    <j-dict-select-tag v-model="queryParam.businessType" placeholder="请选择业务状态" dictCode="business_type" />
                  </a-form-item>
                </a-col>
                <a-col :span='colBtnsSpan()'>
                <span class='table-page-search-submitButtons'
                      :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button type='primary' class='btn-search btn-search-style' @click='searchQuery'>查询</a-button>
                  <a-button class='btn-reset btn-reset-style' @click='searchReset'>重置</a-button>
                  <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
                </a-col>
              </a-row>
            </a-form>
          </div>
          <div class='table-operator table-operator-style'>
            <a-button @click='handleAdd'>新增</a-button>
          </div>
        </a-card>
        <!-- 查询区域-END -->

        <!-- 列表有数据 -->
        <a-card :bordered='false' :bodyStyle="{ padding: '0' ,height:'100%'}" class='card-table' v-if='dataSource.length>0'>
          <!-- table区域-begin -->
          <div class='div-table-container'>
            <a-row :gutter='16' style='margin-left: 8px; margin-right: 8px'>
              <a-col v-bind='CardColLayout' v-for='(item, index) in dataSource' :key='"one_"+index'>
                <div class='cardCont'>
                  <div class='cardContOne'>
                    <div class='oneDivP'>
                      <a-tooltip placement='topLeft' :title='item.businessName' trigger='hover'>
                        <p class='oPFir'>{{ item.businessName }}</p>
                      </a-tooltip>
                    </div>
                  </div>
                  <a-row class='cardContTwo'>
                    <a-col :span='8' class='cardContTwoChild oColor' v-for='(title,idx) in bDatalist' :key='"two_"+idx'>{{title}}</a-col>
                    <a-col :span='8' class='cardContTwoChild tColor'>
                      <span>{{item.restHeartbeat?item.restHeartbeat.responseResult:''}}</span>
                    </a-col>
                    <!--                  <a-col :span='8' class='cardContTwoChild tColor' v-if="item.businessState == '0'">
                                        <span :class="[item.businessState == '0'?'box':'boxRed']"></span>
                                        {{item.businessState == '0' ?'正常':'错误'}}
                                      </a-col>-->
                    <a-col :span='8' class='cardContTwoChild tColor' >
                      <a-tooltip placement='top' :title='getBusinessText(item.businessType,businessTypeList)' trigger='hover'>
                        <div class='text'>
                          {{getBusinessText(item.businessType,businessTypeList)}}
                        </div>
                      </a-tooltip>
                    </a-col>
                    <a-col :span='8' class='cardContTwoChild tColor'>
                      <a-tooltip placement='top' :title='item.healthLevel' trigger='hover'>
                        <div class='text'>
                          {{item.healthLevel}}
                        </div>
                      </a-tooltip>
                    </a-col>
                  </a-row>
                  <a-row class='cardContThree'>
                    <a-col :span='8' class='cardContThreeCol aCol' @click='handleEdit(item)'>
                      <a-icon type='edit' class='tCon' />编辑
                    </a-col>
                    <a-col :span='8' class='cardContThreeCol aCol' @click='handleDetailPage(item)'>
                      <a-icon type='eye' class='tCon' />查看
                    </a-col>
                    <a-col :span='8' class='cardContThreeCol aCol' @click='deleteRecord(item)'>
                      <a-icon type='delete' class='tCon' />删除
                    </a-col>
                  </a-row>
                  <div class='cardContFour' v-if='item.alarmCount>0'>
                    <div class='box'>
                      <div class='square' :style='{background: getAlarmColor(item.alarmLevel)}'></div>
                      <div class='triangle' :style='{borderTopColor:  getAlarmColor(item.alarmLevel)}'></div>
                      <span class='alarm-count' :title='item.alarmCount' v-if='item.alarmCount<100'>{{item.alarmCount}}</span>
                      <a-tooltip v-else placement='topLeft' :title='"告警"+item.alarmCount+"次"' trigger='hover'>
                        <span class='alarm-count'>99<sup>+</sup></span>
                      </a-tooltip>
<!--                      <span class='alarm-count' v-if='item.alarmCount>=100'>99<sup>+</sup></span>-->
                    </div>
                  </div>
                </div>
              </a-col>
            </a-row>
          </div>
          <div class='pagination'>
            <a-pagination show-quick-jumper show-size-changer :hideOnSinglePage='false'
                          :default-current='ipagination.current' :total='ipagination.total' @change='onChange'
                          :page-size='ipagination.pageSize' :pageSizeOptions='ipagination.pageSizeOptions'
                          :show-total='(total) => `共 ${ipagination.total} 条`' @showSizeChange='onChange'
                          size='small'>
            </a-pagination>
          </div>
        </a-card>
        <!-- 列表有数据-END -->

        <!-- 正在加载数据效果或无数据界面 -->
        <a-card :bordered='false' :bodyStyle="{ padding: '0' }" class='card-empty' v-else>
          <a-spin :spinning='loading' style='min-height: 100%;width: 100%' v-if='loading'></a-spin>
          <a-list :data-source='[]' v-else></a-list>
        </a-card>
        <!-- 正在加载数据效果或无数据界面-END -->

        <business-overview-modal ref='modalForm' @ok='modalFormOk'></business-overview-modal>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { JeecgListMixin} from '@/mixins/JeecgListMixin'
import {getAction,deleteAction} from '@/api/manage'
import { YqFormSearchLocation} from '@/mixins/YqFormSearchLocation'
import { ajaxGetDictItems, getDictItemsFromCache } from '@api/api'
import businessOverviewModal from '@views/businessMonitoring/businessOverview/modules/businessOverviewModal.vue'
export default {
  name: 'businessOverviewList',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components: {
    businessOverviewModal
  },
  data() {
    return {
      maxLength:50,
      description: '业务概览页面',
      formItemLayout: {
        labelCol: {
          style: 'width:90px'
        },
        wrapperCol: {
          style: 'width:calc(100% - 90px)'
        }
      },
      CardColLayout: {
        xl: {span: 6},
        lg: {span: 8},
        md: {span: 12},
        sm: {span: 24},
        xs: {span: 24}
      },
      ipagination: {
        pageSize: 8,
        pageSizeOptions: ['8', '16', '24'],
      },
      url: {
        list: '/business/info/list',
        delete: '/business/info/remove',
        alarmLevel: '/alarm/alarmLevel/getLevelList', //获取告警级别数据
      },
      businessTypeList:[],
      bDatalist:['业务状态','业务类型','健康评分'],
      alarmLevelList:[],
      disableMixinCreated:true
    }
  },
  created() {
    this.initDictData('businessTypeList','business_type')
    this.getAlarmLevelData()
  },
 activated() {
    this.loadData()
 },
  methods: {
    /*获取告警级别内容*/
    getAlarmLevelData() {
      let that = this
      getAction(that.url.alarmLevel).then((res) => {
        if (res.success) {
          this.alarmLevelList = res.result
        }
      })
    },
    /*获取当前告警级别颜色*/
    getAlarmColor(text) {
      let color='#ffffff'
      for (let i=0;i<this.alarmLevelList.length;i++){
        if (this.alarmLevelList[i].value == text) {
          color= this.alarmLevelList[i].color
          break
        }
      }
      return color
    },
    /*通过value值，从字典数据中获取text*/
    getBusinessText(){
      let txt=''
      for (let i=0;i<arguments[1].length;i++){
        if(arguments[1][i].value==arguments[0]){
          txt= arguments[1][i].text
          break
        }
      }
      return txt
    },
    /*获取字典数据*/
    initDictData(dictOption,dictCode) {
      if (dictCode != null && dictCode != '') {
        //优先从缓存中读取字典配置
        if (getDictItemsFromCache(dictCode)) {
          this[dictOption] = getDictItemsFromCache(dictCode)
          return
        }

        //根据字典Code, 初始化字典数组
        ajaxGetDictItems(dictCode, null).then((res) => {
          if (res.success) {
            this[dictOption] = res.result
          }
        })
      }
    },
    /*通过value值，从字典数据中获取text*/
    getStatusName(filed, dictOption,item) {
      let filedName=filed+'Text'
      for (let i = 0; i < dictOption.length; i++) {
        if (dictOption[i].value == item[filed]) {
          return  dictOption[i].text
        }
      }
    },
    //删除
    deleteRecord(record) {
      if (!this.url.delete) {
        this.$message.error('请设置url.delete属性!')
        return
      }
      var that = this
      this.$confirm({
        title: '确认删除',
        okText: '是',
        cancelText: '否',
        content: '是否删除选中数据?',
        onOk: function() {
          that.loading = true
          deleteAction(that.url.delete, {
            businessId: record.id
          }).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.loadData()
            } else {
              that.$message.warning(res.message)
            }
          })
        }
      })
    },
    onChange(pageNumber, pageSize) {
      this.ipagination.pageSize = pageSize
      this.ipagination.current = pageNumber
      this.loadData()
    },
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
.row-top{
  height: 100%;
  margin-left: -16px;
  margin-right: -16px;

  .col-top{
    width: 100% !important;
    min-height: 100% !important;
    display: flex;
    flex-direction: column;
  }
}
.card-table{
  width: 100%;
  flex: auto;
  background-color: rgba(255, 255, 255, 0);
}
.card-empty{
  width: 100%;
  flex: auto;
  background-color: rgba(255, 255, 255, 0);
  display: flex;
  justify-content: center;
  align-items: center;
}
.cardCont {
  width: 100%;
  position: relative;
  background: #ffffff;
  -webkit-box-shadow: 0 3px 7px -1px rgba(0, 0, 0, 0.16);
  box-shadow: 0px 3px 7px -1px rgba(0, 0, 0, 0.16);
  border-radius: 2px;
  margin: 16px 0 0px 0px;
  height: 188px;
}

.cardContOne {
  overflow: hidden;
  padding: 22px 0px 0 26px;
  display: inline-block;
  white-space: nowrap;
  width: 100%;

  .oneDivP {
    width: calc(100% - 80px);
    margin-bottom: 8px;

    .oPFir {
      font-family: PingFangSC-Regular;
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
      margin-bottom: 8px;

      white-space: nowrap;
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      cursor: default;
    }

    .oPTwo {
      font-family: PingFangSC-Regular;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.65);
      margin-bottom: 8px;

      white-space: nowrap;
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      cursor: default;
    }
  }
}

.cardContTwo {
  padding: 8px 0 0 0;
  margin-bottom:0px;

  .cardContTwoChild {
    text-align: center;
    margin-bottom: 9px;
  }

  .oColor {
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
  }

  .tColor {
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
  }

  .box {
    color:#13a40a !important;
    /*width: 8px;
    height: 8px;
    background: #13a40a;
    border-radius: 50%;
    display: inline-block;
    margin-right: 5px;*/
  }

  .boxRed {
    color:red !important;
    //width: 8px;
    //height: 8px;
   // background: red;
    //border-radius: 50%;
    //display: inline-block;
    //margin-right: 5px;
  }
  .text{
    width: 100%;
    height: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}

.cardContThree {
  position: absolute;
  bottom: 0;
  width: 100%;
  text-align: center;
  padding: 14px 0 14px 0;
 //background: #f3f3f3;
  color: #409eff;
  background: rgba(236, 245, 255, 0.66);
  border-top: 1px solid #dadada;
  border-radius: 0 0 2px 2px;
  border-radius: 0px 0px 2px 2px;
  margin-bottom:0px;
}

.cardContThree .cardContThreeCol {
  cursor: pointer;
  border-right: 2px solid #dadada;

  .tCon {
    margin-right: 8px;
  }
}

.cardContThree div:nth-child(3) {
  border: none;
}

.cardContFour {
  position: absolute;
  top: 0;
  right: 20px;
  width: 60px;
  height: 60px;

  .box {
    height: 100%;
    position: relative;

    .alarm-count {
      position: absolute;
      top: 0px;
      text-align: center;
      color: #ffffff;
      font-size: 18px;
      height: 60px;
      line-height: 50px;
      width: 100%;
    }

    .square {
      position: absolute;
      top: 0;
      left: 0;
      height: 40px;
      width: 100%;
      //background: red;
    }

    .triangle {
      position: absolute;
      top: 40px;
      left: 0;
      height: 0px;
      width: 0px;
      border-top-width: 20px;
      border-top-style: solid;
      border-left: 30px solid transparent;
      border-right: 30px solid transparent;
    }
  }
}

.pagination {
  text-align: right;
  position: relative;
  margin: 30px 30px 30px auto
}
</style>