<template>
  <j-modal :title="title" :width="width" :visible="visible" :destroyOnClose="true" :centered='true' switchFullscreen
    @ok="handleOk" @cancel="handleCancel" cancelText="关闭">
    <terminal-bind-form ref="realForm" @ok="submitCallback"> </terminal-bind-form>
  </j-modal>
</template>

<script>
  import TerminalBindForm from './TerminalBindForm'
  export default {
    name: 'TerminalBindModal',
    components: {
      TerminalBindForm
    },
    data() {
      return {
        title: '终端绑定',
        width: '1000px',
        visible: false,
      }
    },
    methods: {
      add(record) {
        this.visible = true
        this.$nextTick(() => {
          this.$refs.realForm.edit(record);
        })
      },
      close() {
        this.$emit('close');
        this.visible = false;
      },
      handleOk() {
        this.$refs.realForm.submitForm();
      },
      submitCallback() {
        this.$emit('ok');
        this.visible = false;
      },
      handleCancel() {
        this.close()
      }
    }
  }
</script>
<style lang='less' scoped>
  @import '~@assets/less/normalModal.less';
</style>