<template>
  <a-row :gutter='10' class='vScroll' style='height: 100%'>
    <a-col style='width: 100%; height: 100%; display: flex; flex-direction: column'>
      <!-- 查询区域 -->
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class="card-style">
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="模块名称">
                  <j-input
                    placeholder="请输入模块名称"
                    :autocomplete="'off'"
                    :allow-clear="true"
                    v-model="queryParam.moduleName" :maxLength="50"
                  />
                </a-form-item>
              </a-col>
               <a-col :span="spanValue">
                <a-form-item label="任务请求时间">
                  <a-range-picker
                    class="a-range-picker-choice-date"
                    @change="onChangeRequestTime"
                    v-model="queryParam.requestTime1"
                    show-time
                    format="YYYY-MM-DD HH:mm:ss"
                    :placeholder="['开始时间', '截止时间']"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="colBtnsSpan()">
                <span  class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button class="btn-search btn-search-style" type="primary" @click="searchQuery">查询</a-button>
                  <a-button class="btn-reset btn-reset-style" @click="searchReset">重置</a-button>
                  <a v-if="isVisible" class="btn-updown-style" @click="doToggleSearch">
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <!-- 查询区域-END -->
      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <div class="table-operator">
          <a-button
            @click="batchDel"
            v-if="selectedRowKeys.length > 0"
            ghost
            type="primary"
            icon="delete">批量删除
          </a-button>
        </div>
        <!-- table区域-begin -->
        <a-table
          ref='table'
          :columns='columns'
          :dataSource='dataSource'
          :loading='loading'
          :pagination='ipagination'
          :rowSelection='{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }'
          :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
          bordered
          rowKey='id'
          @change='handleTableChange'>
          <span slot='action' slot-scope='text, record' class='caozuo'>
              <a @click='downloadFile(record.fileUrl)'>下载</a>
              <a-divider type='vertical' />
               <a-popconfirm title='确定删除吗?' @confirm='() => handleDelete(record.id)'>
              <a>删除</a>
            </a-popconfirm>
          </span>
          <template slot='tooltip' slot-scope='text'>
            <a-tooltip :title='text' placement='topLeft' trigger='hover'>
              <div class='tooltip'>
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
      </a-card>
    </a-col>
  </a-row>
</template>

<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'

export default {
  name: 'downloadManagementList',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components: {},
  data() {
    return {
      //disableMixinCreated: true,
      requestTime:[],
      formItemLayout: {
        labelCol: {
          style: 'width:105px'
        },
        wrapperCol: {
          style: 'width:calc(100% - 105px)'
        }
      },
      // 表头
      columns: [
        {
          title: '模块名称',
          dataIndex: 'moduleName'
        },
        {
          title: '文件名称',
          dataIndex: 'fileName'
        },
        {
          title: '任务请求时间',
          dataIndex: 'requestTime'
        },
        {
          title: '任务完成时间',
          dataIndex: 'createTime'
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 160,
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: '/sys/downloadManage/list',
        delete: '/sys/downloadManage/delete',
        deleteBatch: '/sys/downloadManage/deleteBatch',
      }
    }
  },
  mounted() {
    // this.dataSource = [{
    //   id:1,
    //   moduleName: '监控中心/终端管理/终端统计',
    //   requestTime: '2023-04-04 11:08:07',
    //   finishedTime: '2023-04-04 11:10:07'
    // }]
    // this.getModuleName()
  },
  methods: {
    onChangeRequestTime(date, dateString) {
      this.queryParam.requestTime_begin = dateString[0]
      this.queryParam.requestTime_end = dateString[1]
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';

</style>