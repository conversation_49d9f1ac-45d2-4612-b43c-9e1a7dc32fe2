<template>
  <div id="ceshiDiv" class="ceshi-div">
    <div class="ceshi-item" @click="handleMenuClick('1')">连接测试</div>
    <div class="ceshi-item" @click="handleMenuClick('2')">SSH连接</div>
    <ce-shi-modal ref="ceshiForm"></ce-shi-modal>
  </div>
</template>

<script>
import CeShiModal from '../CeShiModal'
export default {
  props: {
    cell:""
  },
  components: {
    CeShiModal,
  },
  data() {
    return {
      deviceCode:""
    }
  },
  created(){
    this.deviceCode = this.cell.getData().deviceCode;
  },
  methods: {
    handleMenuClick(key) {
      let data = this.cell.getData()
      if (this.deviceCode) {
        if (key === '1') {
          this.$refs.ceshiForm.title = '连接测试'
        } else {
          this.$refs.ceshiForm.title = 'SSH连接'
        }
        this.$refs.ceshiForm.edit(key, data.deviceIp || "")
        this.$refs.ceshiForm.disableSubmit = false
      }
      this.$emit('hide')
    },
  },
}
</script>

<style lang="less" scoped>
.ceshi-div {
  .ceshi-item {
    color: rgba(0, 0, 0, 0.65);
    height: 24px;
    line-height: 24px;
    margin-bottom: 10px;
    align-items: center;
    cursor: pointer;
    font-weight: bold;
  }

  .ceshi-item:hover {
    color: #000;
  }
}
</style>