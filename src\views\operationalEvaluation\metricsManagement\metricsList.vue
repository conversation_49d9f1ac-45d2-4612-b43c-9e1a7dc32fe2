<template>
  <a-spin style="height: 100%" :spinning="loading">
    <div style="height: 100%; overflow-x: auto; display: flex; align-items: center">
      <div class="left-col">
        <metrics-tree @refreshData="refreshData"></metrics-tree>
      </div>
      <div class="right-col">
        <div class="content-div" style="display: flex; flex-direction: column">
          <!-- 查询区域 -->
          <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class="card-style">
            <div class="table-page-search-wrapper">
              <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
                <a-row :gutter="24" ref="row">
                  <a-col :span="spanValue">
                    <a-form-item label="指标名称">
                      <a-input
                        :maxLength='maxLength'
                        placeholder="请输入指标名称"
                        v-model="queryParam.metricsName"
                        autocomplete="off"
                        :allowClear="true"
                      >
                      </a-input>
                    </a-form-item>
                  </a-col>
                  <a-col :span="colBtnsSpan()">
                    <span
                      class="table-page-search-submitButtons"
                      :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                    >
                      <a-button class="btn-search btn-search-style" type="primary" @click="searchQuery">查询</a-button>
                      <a-button class="btn-reset btn-reset-style" @click="searchReset">重置</a-button>
                      <a v-if="isVisible" class="btn-updown-style" @click="doToggleSearch">
                        {{ toggleSearchStatus ? '收起' : '展开' }}
                        <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                      </a>
                    </span>
                  </a-col>
                </a-row>
              </a-form>
            </div>
          </a-card>
          <!-- 表格区域 -->
          <a-card :bordered="false" style="flex: 1 1 auto">
            <!-- 操作按钮区域 -->
            <div class="table-operator table-operator-style">
              <a-button @click="handleAdd">新增</a-button>
              <a-dropdown v-if="selectedRowKeys.length > 0">
                <a-menu slot="overlay" style="text-align: center">
                  <a-menu-item key="1" @click="batchDel">删除</a-menu-item>
                </a-menu>
                <a-button
                  >批量操作
                  <a-icon type="down" />
                </a-button>
              </a-dropdown>
            </div>
            <a-table
              ref="table"
              bordered
              rowKey="id"
              :columns="columns"
              :dataSource="dataSource"
              :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
              :pagination="ipagination"
              :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
              @change="handleTableChange"
            >
              <template slot="tooltip" slot-scope="text">
                <a-tooltip placement="topLeft" :title="text" trigger="hover">
                  <div class="tooltip">
                    {{ text }}
                  </div>
                </a-tooltip>
              </template>
              <span slot="action" slot-scope="text, record" class="caozuo">
                <a @click="handleDetailPage(record)">查看</a>
                <a-divider type="vertical" />
                <a @click="handleEdit(record)">编辑</a>
                <a-divider type="vertical" />
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </span>
            </a-table>
          </a-card>
        </div>
      </div>
      <metrics-modal ref="modalForm" :metricsTypeId="metricsTypeId" @ok="modalFormOk"></metrics-modal>
    </div>
  </a-spin>
</template>

<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
import metricsTree from './modules/metricsTree.vue'
import metricsModal from './modules/metricsModal.vue'
export default {
  name: 'metricsList',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components: {
    metricsTree,
    metricsModal,
  },
  data() {
    return {
      maxLength: 50,
      description: '评估指标管理',
      formItemLayout: {
        labelCol: {
          style: 'width:80px',
        },
        wrapperCol: {
          style: 'width:calc(100% - 80px)',
        },
      },
      // disableMixinCreated: true,
      //查询条件
      queryParam: {},
      loading: false,
      //表头
      columns: [
        {
          title: '指标名称',
          dataIndex: 'metricsName',
        },
        {
          title: '指标类别',
          dataIndex: 'metricsTypeId_dictText',
        },
        {
          title: '指标标识',
          dataIndex: 'metricsCode',
        },
        {
          title: '指标备注',
          dataIndex: 'groupDesc',
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 200px;max-width:300px'
            return {
              style: cellStyle,
            }
          },
          scopedSlots: {
            customRender: 'tooltip',
          },
        },
        {
          title: '创建人',
          dataIndex: 'createBy_dictText',
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 120,
          scopedSlots: {
            customRender: 'action',
          },
        },
      ],
      // 树
      url: {
        list: '/evaluate/metricsInfo/pageList', // 查询指标的列表
        delete: '/evaluate/metricsInfo/delete', // 删除指标
        deleteBatch: '/evaluate/metricsInfo/deleteBatch', // 批量删除指标
        getCategoryList: '/evaluate/metricsType/list', // 获取全部类别
      },
      categoryList: [],
      metricsTypeId: '', // 当前选中的类别id
    }
  },
  created() {},
  methods: {
    searchReset() {
      this.queryParam = {
        metricsTypeId: this.metricsTypeId,
      }
      this.loadData(1)
    },
    refreshData(value) {
      this.metricsTypeId = value
      if (value) {
        this.queryParam.metricsTypeId = value
      } else {
        this.queryParam.metricsTypeId = ''
      }
      this.loadData(1)
    },
  },
}
</script>
<style lang="less" scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';

.table-page-search-wrapper {
  margin-bottom: 0;
}
.left-col {
  height: 100%;
  width: calc(300px - 15px);
  margin-right: 15px;
  background: #fff;
}

.right-col {
  height: 100%;
  width: calc(100% - 300px);
  min-width: 800px;
}

.content-div {
  width: 100%;
  height: 100%;
  border-radius: 3px;
}
/deep/ .ant-spin-container {
  height: 100%;
}
</style>
