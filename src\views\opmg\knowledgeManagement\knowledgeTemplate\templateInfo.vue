<template>
  <a-card :bordered='false' style='overflow: hidden;overflow-y: auto;'>
    <div class='view-action'>
      <span class='return'>
        <img src='~@/assets/return1.png' alt='' @click='getGo'>
      </span>
    </div>
    <a-descriptions :column='{ xxl: 1, xl: 1, lg: 1, md: 1, sm: 1, xs: 1 }' bordered>
      <a-descriptions-item label='模板名称' v-if='record.templateName'>{{ record.templateName }}</a-descriptions-item>
      <a-descriptions-item label='创建时间' v-if='record.createTime'>{{ record.createTime }}</a-descriptions-item>
      <a-descriptions-item label='描述'  v-if='record.description'>{{ record.description }}</a-descriptions-item>
      <a-descriptions-item label='模板内容' v-if='record.content'>
        <div v-html='record.content'></div>
      </a-descriptions-item>
    </a-descriptions>
  </a-card>
</template>
<script>
import { setImgAllPath } from '@/utils/imagePathAboutTinymce'
export default {
  name: 'templateInfo',
  props: {
    data: {
      type: Object,
      required: false,
      default: {},
    }
  },
  data() {
    return {
      record: {},
    }
  },
  watch: {
    data: {
      handler(val) {
        this.record = val
        let content = val.content
        if (content && content.length > 0) {
          this.record.content = setImgAllPath(content)
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    //返回上一级
    getGo() {
      this.$parent.pButton1(0)
    }
  }
}
</script>

<style scoped lang='less'>
.view-action {
  display: flex;
  justify-content: right;
  align-items: center;
  flex-flow: row nowrap;
  margin-bottom: 12px;

  .edit {
    margin-left: 10px;
    color: #409eff;
    cursor: pointer;

    .icon{
      color: #409eff;
      margin-right: 6px
    }
  }

  .return {
    img {
      width: 20px;
      height: 20px;
      cursor: pointer
    }
  }
}

::v-deep .ant-descriptions-view {
  border-radius: 0px;
  overflow-x: auto;
}

::v-deep .ant-descriptions-bordered .ant-descriptions-item-label {
  background-color: rgb(250, 250, 250);
  text-align: center;
  width: 17%;
}

::v-deep .ant-descriptions-item-label,
.ant-descriptions-item-content {
  color: rgb(96, 98, 102) !important;
}

::v-deep .ant-descriptions-bordered .ant-descriptions-item-content {
  word-break: break-word;
  white-space: normal;
}
</style>