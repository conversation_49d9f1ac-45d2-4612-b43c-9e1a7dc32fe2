<template>
  <j-modal
    :title='title'
    :width='width'
    :visible='visible'
    :destroyOnClose='true'
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    :centered='true'
    switch-fullscreen
    @ok='handleOk'
    @cancel='handleCancel'
    cancelText='关闭'>
    <div slot="footer" style="text-align: right">
      <a-button @click="handleCancel">关闭</a-button>
      <a-button type="primary" v-if="!disableSubmit" @click="handleOk">确认</a-button>
      <a-button type="primary" v-if="disableSubmit" @click="handleExport('word')">word导出</a-button>
      <a-button type="primary" v-if="disableSubmit" @click="handleExport('pdf')">pdf导出</a-button>
    </div>
    <a-spin :spinning='confirmLoading'>
      <j-form-container :disabled='disableSubmit'>
        <a-form-model ref='form' slot='detail' :model='model' :rules='validatorRules' :labelCol='labelCol' :wrapperCol='wrapperCol'>
          <a-row>
            <a-col :span='24'>
              <a-form-model-item label='名称' prop='materialName'>
                <a-input style='width: 100%' v-model='model.materialName' :allow-clear='true' autocomplete='off' placeholder='请输入名称' />
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
              <a-form-model-item label='类型' prop='materialType'>
                <j-dict-select-tag v-model="model.materialType" placeholder="请选择类型" dictCode="supportingMaterialTypes"></j-dict-select-tag>
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
              <a-form-model-item class="select-metrics-item" ref="metrics"  label='指标'  prop='metricsId'>
                <div style="width: 100%;display: flex;flex-flow: row nowrap;justify-content: space-between;align-items: center;">
                  <a-tree-select
                    :style="{width:model.metricsId&&model.metricsId_dictText==='主数据处理中心（节点）运行'?'calc(100% - 130px)':'100%' }"
                    :dropdownStyle="{ maxHeight: '400px', overflow: 'auto'}" allowClear
                    :getPopupContainer='(node) => node.parentNode'
                    v-model='model.metricsId'
                    placeholder='请选择指标'
                    :replaceFields="{key:'id',value:'id',title:'title',children:'children'}"
                    :tree-data='metricsData'
                    @select="selectedMetrics"
                    @change="changeMetrics">
                  </a-tree-select>
                  <a-radio-group
                    style="width: 130px;text-align: right"
                    v-if="model.metricsId&&model.metricsId_dictText==='主数据处理中心（节点）运行'"
                    buttonStyle="solid" v-model="model.uploadMethod" @change="handleAddReport">
                    <a-radio-button value="auto" @click="clickAddReport('auto')">自动</a-radio-button>
                    <a-radio-button value="manual">手动</a-radio-button>
                  </a-radio-group>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span='24' v-if="model.uploadMethod==='auto'&&model.filePath&&model.filePath.length>0" :key="'filePath_'+model.filePath.length">
              <a-form-model-item label='文件' prop='filePath'>
                <yq-file-upload
                  :buttonVisible="false"
                  :multiple="false"
                  :return-url="false"
                  v-model="model.filePath"
                @change="changeFiles">
                </yq-file-upload>
              </a-form-model-item>
            </a-col>
            <a-col :span='24' v-else-if="model.uploadMethod==='manual'">
              <a-form-model-item label='附件' prop='materialFields'>
                <yq-file-upload
                  :multiple="true"
                  :return-url="false"
                  v-model="model.materialFields"
                  @change="changeFiles">
                </yq-file-upload>
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
              <a-form-model-item label="说明"  prop="materialDesc">
                <a-textarea
                  v-model="model.materialDesc"
                  :allowClear="true"
                  autocomplete="off"
                  placeholder="请输入说明"
                  :auto-size="{ minRows: 2, maxRows: 8 }"/>
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </j-form-container>
    </a-spin>
  </j-modal>
</template>

<script>
import { downFile, getAction,deleteAction, httpAction } from '@api/manage'
import JSelectUserByDep from '@comp/jeecgbiz/JSelectUserByDep.vue'
import yqFileUpload from '@comp/yq/yqUpload/yqFileUpload.vue'
import {
  ValidateRequiredFields,
  ValidateOptionalFields
} from '@/utils/rules.js'
export default {
  name: 'supportingMaterialsModal',
  components: { JSelectUserByDep,yqFileUpload },
  data() {
    return {
      title: '',
      width: '800px',
      visible: false,
      disableSubmit: false,
      confirmLoading: false,
      uploading:false,
      labelCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 5
        }
      },
      wrapperCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 16
        }
      },
      model: {

      },
      metricsData: [],
      validatorRules: {
        materialName: [
          { required: true, validator: (rule, value, callback) =>ValidateRequiredFields(rule, value, callback,'名称',50,1) }
        ],
        materialType: [
          { required: true, message: '请选择类型' }
        ],
        metricsId: [
          { required: true, validator: this.validateMetrics }
        ],
        materialDesc: [
          { required: false, validator: (rule, value, callback) =>ValidateOptionalFields(rule, value, callback,'说明',200) }
        ]
      },
      url: {
        add: '/evaluate/materialInfo/add',
        edit: '/evaluate/materialInfo/edit',
        metricsTree: '/evaluate/metricsType/tree',
        exportInfoAndFile:'/evaluate/materialInfo/exportInfoAndFile',
        getMaterialByXJ: '/evaluate/materialInfo/getMaterialByXJ'
      }
    }
  },
  created() {
    this.loadTree()
  },
  methods: {
    add(info) {
      this.edit({
        filePath:'[]',
        materialFields:[]
      }, info)
    },
    edit(record, info) {
      this.visible = true
      this.projectId = info.projectId
      this.model= Object.assign({}, record)
      this.$nextTick(()=>{
        if (record.metricsId||info.metricsId){
          // this.model['metricsId']=record.metricsId||info.metricsId
         this.$set(this.model,'metricsId',record.metricsId||info.metricsId)
         this.$set(this.model,'metricsId_dictText',record.metricsId_dictText||info.metricsId_dictText)
        }
        if (this.model.filePath){
          this.model.filePath=JSON.parse(this.model.filePath)
          this.$set(this.model,'uploadMethod',this.model.filePath.length>0?'auto':'manual')
        }else {
          this.$set(this.model,'uploadMethod','manual')
        }
        if (this.model.attachmentPath){
          this.model.materialFields=JSON.parse(this.model.attachmentPath)
        }
      })
    },
    close() {
      this.confirmLoading = false
      this.visible = false
    },
    handleOk() {
      const that = this
      that.$refs.form.validate((validate, value) => {
        if (validate && !that.confirmLoading) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!that.model.id) {
            httpurl += that.url.add
            method = 'post'
          } else {
            httpurl += that.url.edit
            method = 'put'
          }
          let formData = {
            ...that.model
          }
          formData.projectId=that.projectId
          formData.attachmentPath=JSON.stringify(formData.materialFields)
          formData.filePath=JSON.stringify(formData.filePath)
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
                that.close()
              } else {
                that.$message.warning(res.message)
              }
              that.confirmLoading = false
            }).catch((res) => {
            that.$message.warning(res.message)
            that.confirmLoading = false
          })
        }
      })
    },
    submitCallback() {
      this.$emit('ok')
      this.visible = false
    },
    handleCancel() {
      this.close()
    },
    // 查询树
    loadTree() {
      var that = this
      this.confirmLoading = true
      that.metricsData = []
      getAction(that.url.metricsTree).then((res) => {
        if (res.success && res.result.length > 0) {
          this.metricsData = res.result
        }else {
          this.metricsData=[]
        }
        that.confirmLoading = false
      }).catch(() => {
        that.confirmLoading = false
      })
    },
    selectedMetrics(val,com,node){
      this.model['metricsNodeType']=''
      this.model['metricsId_dictText']=''
      if (val){
        this.model['metricsNodeType']=node.node.dataRef.type
        this.model['metricsId_dictText']=com.title
      }
      this.model.filePath=[]
      this.model.materialFields=[]
      this.model.uploadMethod='manual'
    },
    changeMetrics(val){
      this.$refs.metrics.onFieldBlur()
    },
    changeFiles(val){
      // console.log('附件，文件val===',val)
      // console.log('附件，文件model.filePath===',this.model.filePath)
    },
    validateMetrics(rule, value, callback){
      if (rule.required){
        if (!value){
          callback('请选择指标')
        }else {
          if (this.model['metricsNodeType']==='category'){
            callback('勿选择指标分类，请选择指标')
          }else {
            callback()
          }
        }
      }
    },
    handleAddReport(value) {
      this.model.uploadMethod = value.target.value
      if (value.target.value === 'manual') {
        this.$set(this.model, 'materialFields', [])
        if (this.model.filePath && this.model.filePath.length > 0) {
          this.model.materialFields = this.model.filePath
          this.model.filePath = []
        }
      }
    },
    clickAddReport(value){
      if (value==='auto'&&this.model.filePath&&this.model.filePath.length===0){
        deleteAction(this.url.getMaterialByXJ).then((res)=>{
          this.model.filePath=[]
          if (res.success&&res.result){
            this.model.filePath.push(res.result)
          }
        }).catch(()=>{
          this.model.filePath=[]
        })
      }
    },
    handleExport(type){
      if (!this.uploading){
        this.uploading = true
        downFile(this.url.exportInfoAndFile, {id:this.model.id,exportType:type}).then((data) => {
          if (!data) {
            this.$message.warning('文件下载失败')
            this.uploading = false
            return
          }
          if (typeof window.navigator.msSaveBlob !== 'undefined') {
            window.navigator.msSaveBlob(new Blob([data], { type: 'application/vnd.ms-excel' }), fileName + '.excel')
          }
          else {
            if (data.type == 'multipart/form-data') {
              let url = window.URL.createObjectURL(new Blob([data], { type: 'multipart/form-data' }))
              let link = document.createElement('a')
              link.style.display = 'none'
              link.href = url
              link.setAttribute('download', record.materialName + '详情.zip')
              document.body.appendChild(link)
              link.click()
              // 清理资源
              setTimeout(() => {
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
              }, 100)
            }
            else {
              this.$message.error('导出失败')
            }
          }
          this.uploading = false
        }).catch(()=>{
          this.uploading = false
        })
      }
    },
  }
}
</script>
<style scoped lang='less'>
@import '~@assets/less/normalModal.less';
</style>