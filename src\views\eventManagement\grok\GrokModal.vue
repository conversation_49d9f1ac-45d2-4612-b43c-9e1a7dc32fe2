<template>
  <j-modal
    :title="title"
    :width="800"
    :centered='true'
    switchFullscreen
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="GROK名称"
        >
          <a-input
            placeholder="请输入GROK名称"
            v-decorator.trim="[ 'grokName', validatorRules.grokName]"
            autocomplete="off"
            :allowClear="true"
          />
        </a-form-item>

        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="GROK标识"
        >
          <a-input
            placeholder="请输入GROK标识"
            v-decorator.trim="[ 'grokCode', validatorRules.grokCode]"
            autocomplete="off"
            :allowClear="true"
          />
        </a-form-item>

        <a-form-item
          class="two-words"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="描述"
        >
          <a-input
            v-decorator="['description', validatorRules.description]"
            autocomplete="off"
            :allowClear="true"
          />
        </a-form-item>

      </a-form>
    </a-spin>
  </j-modal>
</template>

<script>
import pick from 'lodash.pick'
import { putAction, postAction } from '@api/manage'

export default {
  name: 'DictModal',
  data() {
    return {
      value: 1,
      title: '操作',
      visible: false,
      model: {},
      labelCol: {
        xs: {
          span: 24,
        },
        sm: {
          span: 5,
        },
      },
      wrapperCol: {
        xs: {
          span: 24,
        },
        sm: {
          span: 16,
        },
      },
      confirmLoading: false,
      form: this.$form.createForm(this),
      validatorRules: {
        grokName: {
          rules: [
            {
              required: true,
              message: '请输入GROK名称!',
            },
            { max: 100, message: 'GROK名称长度不能超过100个字符' },
          ],
        },
        grokCode: {
          rules: [
            {
              required: true,
              message: '请输入GROK标识!',
            },
            { max: 100, message: 'GROK标识长度不能超过100个字符' },
          ],
        },
        description: {
          rules: [{ required: false, min: 0, max: 255, message: '描述长度不能超过255个字符' }]
        }
      },
      url: {
        add: '/grok/add',
        edit: '/grok/edit',
      }
    }
  },
  created() {},
  methods: {
    handleChange(value) {
      this.model.status = value
    },
    add() {
      this.edit({})
    },
    edit(record) {
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(pick(this.model, 'grokName', 'grokCode', 'description'))
      })
    },
    // 确定
    handleOk() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          values.grokName = (values.grokName || '').trim()
          values.grokCode = (values.grokCode || '').trim()
          values.description = (values.description || '').trim()
          let formData = Object.assign(this.model, values)
          let res;
          if (!this.model.id) {
            res = postAction(this.url.add, formData)
          } else {
            res = putAction(this.url.edit, formData)
          }
          res.then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
              that.close()
            })
        }
      })
    },
    // 关闭
    handleCancel() {
      this.close()
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
  },
}
</script>
<style lang="less" scoped>
@import '~@assets/less/normalModal.less';

::v-deep .two-words > div > label {
  letter-spacing: 4px;
}

::v-deep .two-words > div > label::after {
  letter-spacing: 0px;
}
</style>