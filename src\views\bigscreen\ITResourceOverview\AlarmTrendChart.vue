<template>
  <div>
    <a-spin :spinning="loading" wrapperClassName="custom-ant-spin">
      <div v-if="alarmData&&alarmData.length>0" style="padding-top: 0.175rem;height: calc(100% - 0.175rem)" ref="AlarmLevelTrendChart"></div>
      <a-list :data-source="[]" v-else />
    </a-spin>
  </div>
</template>
<script>
import { heightPixel, widthPixel } from '@views/statsCenter/com/calculatePixel'
import { getAction } from '@api/manage'

export default {
  props: {
    unit: {
      type: String,
      required: false,
      default: ''
    }
  },
  data() {
    return {
      loading:false,
      myChart: null,
      xData: [],
      alarmData:[],
      legend:[],
      url:{
        alarmLevelTrend: '/openAPI/alarmLevelTrend'
      }
    }
  },

  mounted() {
    // this.getMockJson()
    this.getAlarmLevelTrend()
  },
  destroyed() {
    window.removeEventListener("resize", this.alarmLevelTrendChartResize)
    if (this.myChart){
      this.myChart.dispose()
    }
  },
  methods: {
    getAlarmLevelTrend() {
      this.loading = true
      this.xData=[]
      this.alarmData=[]
      this.legend=[]
      getAction(this.url.alarmLevelTrend).then((res) => {
        if (res.success && res.result) {
          this.xData = res.result.time ? res.result.time.map(item => item.slice(5)) : []
          this.alarmData = res.result.value || []
          this.legend = res.result.title || []
          if (this.alarmData.length > 0) {
            this.$nextTick(() => {
              this.drawChart()
            })
          }
        } else {
          //this.$message.warning(res.message)
          if (this.myChart){
            this.myChart.dispose()
          }
        }
        this.loading = false
      }).catch((err) => {
        this.loading = false
       // this.$message.warning(err.message)
        if (this.myChart){
          this.myChart.dispose()
        }
      })
    },
    getMockJson() {
      this.loading = true
      this.alarmData = [
        {
          "name":'紧急',
          "color":'255,168,0',
          'data':[30,50,23,99,50,10,10]
        },
        {
          "name":'重要',
          "color":'43,236,217',
          'data':[70,5,10,20,0,50,6]
        },
        {
          "name":'次要',
          "color":'0,157,255',
          'data':[22,60,4,224,123,10,2]
        },
        {
          "name":'提示',
          "color":'93,200,255',
          'data':[10,40,59,69,80,30,41]
        }
      ]
      this.xData = ["08-01","08-02","08-03","08-04","08-05","08-06","08-07",]
      this.legend=this.alarmData.map(item=>item.name)

      this.drawChart()
      /*  getAction(location.origin+"/statsCenter/mock/homeData.json").then((res) => {
        if(res){
          console.log('res.deviceAlarmData===',res.deviceAlarmData)
        }
        this.loading = false
      }).catch((err)=>{
        this.loading = false
      })*/
    },
    // 趋势分析折线图
    drawChart() {
      let h25 = heightPixel(25)
      let w2 = widthPixel(2)
      let w4 = widthPixel(4)
      let w10 = widthPixel(10)
      let w16 = widthPixel(16)
      let w20 = widthPixel(20)

      let seriesOption =[]
      this.alarmData.forEach((ele, i) => {
        seriesOption.push(
          {
            name: ele.name,
            type: 'line',
            smooth: true,
            symbol:'none',
            lineStyle: {
              normal: {
                width: w2,
                color: `rgba(${ele.color})`, // 线条颜色
              },
              borderColor: `rgba(${ele.color})`,
            },
            itemStyle: {
              normal: {
                color: `rgba(${ele.color})`,
                borderColor: `rgba(${ele.color})`,
                borderWidth: 2
              },
              emphasis: {
                color: `rgba(${ele.color})`
              }
            },
            data: ele.data,
          })
      })

      this.myChart = this.$echarts.init(this.$refs.AlarmLevelTrendChart)
      this.myChart.setOption(
        {
          grid: {
            top: w20,
            left: '10%',
            right: w20,
            bottom: h25,
          },
          tooltip: {
            show: true,
            trigger: 'axis',
            transitionDuration: 0, //echart防止tooltip的抖动
            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: 'line', // 默认为直线，可选为：'line' | 'shadow'
              lineStyle: {
                type: 'dotted',
                color: '#ffffff'
              }
            },
          },
          legend:
            {
              show: true,
              // left:"right",
              right: w20,
              data: this.legend,
              type: 'scroll',
              color: '#fff',
              itemWidth: w16,
              itemHeight: w4,
              padding:[0,0,0,w20],
              textStyle: {
                fontWeight: 300,
                fontSize: w10,
                color: '#fff'
              },
             /* itemStyle: {
                color: '#2BEBD8',
              }*/
            },
          xAxis: [
            {
              type: 'category',
              boundaryGap: false,
              axisLine: {
                //坐标轴轴线相关设置。数学上的x轴
                show: true,
                lineStyle: {
                  color: 'rgba(68,211,255,0.3)',
                  width: 1,
                  type: 'Dashed',
                },
              },
              axisLabel: {
                show: true,
                textStyle: {
                  color: 'rgba(210,224,226,0.8)', //更改坐标轴文字颜色
                  fontSize: w10,
                  fontWeight: 300
                },
              },
              splitLine: {
                show: false,
              },
              axisTick: {
                show: false,
              },
              data: this.xData,
            }],
          yAxis: [
            {
              position: 'left',
              yAxisIndex: 0,
              /* nameTextStyle: {
                color: 'rgba(250,250,250,.6)',
                fontSize: w12,
                padding: w10,
              },*/
              // min: 0,
              // max: this.max,
              splitLine: {
                show: false,
                lineStyle: {
                  color: ['rgba(68,211,255,0.3)'],
                  width: 1,
                  type: 'Dashed',
                },
              },
              axisLine: {
                show: true,
                lineStyle: {
                  color: 'rgba(68,211,255,0.3)',
                  width: 1,
                  type: 'Dashed',
                }
              },
              axisLabel: {
                show: true,
                // margin: w15,
                textStyle: {
                  color: 'rgba(255,255,255,0.65)', //更改坐标轴文字颜色
                  fontSize: w10,
                  fontWeight: 400
                }
              },
              axisTick: {
                show: false
              }
            }
          ],
          series: seriesOption,
        }
      )
      window.addEventListener("resize", this.alarmLevelTrendChartResize)
    },
    alarmLevelTrendChartResize(){
      this.myChart.dispose()
    }
  }
}
</script>

<style scoped lang="less">
::v-deep .ant-empty-description{
  color:#fff !important;
}
</style>
