<template>
  <a-card>
    <a-col :span="24" style="margin-bottom: 5px">
      <div style="text-align: right">
        <img src="~@/assets/return1.png" alt="" @click="getGo" style="width: 20px; height: 20px; cursor: pointer" />
      </div>
    </a-col>
    <table class="gridtable">
      <tr>
        <td class="leftTd" style="font-size: 14px">类 型</td>
        <td class="rightTd" style="font-size: 14px">{{ data.orderCategoryText }}</td>
        <td class="leftTd" style="font-size: 14px">文件名称</td>
        <td class="rightTd" style="font-size: 14px">{{ data.title }}</td>
      </tr>
      <tr>
        <td class="leftTd" style="font-size: 14px">描 述</td>
        <td class="rightTd" colspan="3" style="font-size: 14px">{{ data.description }}</td>
      </tr>
      <tr>
        <td class="leftTd" style="font-size: 14px">解决方案</td>
        <td class="rightTd" colspan="3" style="font-size: 14px">{{ data.plan }}</td>
      </tr>
      <tr>
        <td class="leftTd" style="font-size: 14px">附 件</td>
        <td class="rightTd" colspan="3" style="padding-bottom: 0; font-size: 14px">
          <div v-for="(item, index) in files" :key="index" style="float: left; margin-left: 10px">
            <div>
              <a @click="fontClick(item.fileUrl)" style="color: #40a9ff">{{ item.fileName }}</a>
            </div>
          </div>
        </td>
      </tr>
    </table>
  </a-card>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import pick from 'lodash.pick'
import { validateDuplicateValue } from '@/utils/util'
import JFormContainer from '@/components/jeecg/JFormContainer'
import JDictSelectTag from '@/components/dict/JDictSelectTag'
import JUpload from '@/components/jeecg/JUpload'

export default {
  name: 'UmpKnowledgeDetailForm',
  components: {
    JFormContainer,
    JDictSelectTag,
    JUpload,
  },
  props: {
    //流程表单data
    formData: {
      type: Object,
      default: () => {},
      required: false,
    },
    //表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false,
    },
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false,
    },
    data: {
      type: Object,
    },
  },
  data() {
    return {
      form: this.$form.createForm(this),
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      confirmLoading: false,
      defaultActiveKey: '1' /**默认打开的标签 */,
      files: [],
      validatorRules: {},
      urla: window._CONFIG['downloadUrl'] + '/',
      url: {
        add: '/knowledges/add',
        edit: '/knowledges/edit',
        queryById: '/knowledges/queryById',
        queryFileGroup: '/fileinfo/fileInfo/queryByFileGroups',
      },
      // data: {},
    }
  },
  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    },
  },
  mounted() {
    //如果是流程中表单，则需要加载流程表单data
    //   this.showFlowData();
    this.getFilesUrl(this.data.fileGroup)
  },
  methods: {
    getFilesUrl(fileGroup) {
      if ('' != fileGroup && null != fileGroup) {
        let params = { fileGroup: fileGroup }
        getAction(this.url.queryFileGroup, params).then((res) => {
          if (res.success) {
            this.files = res.result
          }
        })
      }
    },
    fontClick(path) {
      // window.open(window._CONFIG['downloadUrl']+"/"+path);
      window.open(window._CONFIG['domianURL'] + '/sys/common/downloadFile/' + path)
    },
    popupCallback(row) {
      this.form.setFieldsValue(pick(row, 'type', 'title', 'description', 'plan', 'recordType'))
    },
    //返回上一级
    getGo() {
      this.$parent.pButton2(0)
    },
  },
}
</script>
<style scoped>
table.gridtable {
  font-family: verdana, arial, sans-serif;
  font-size: 11px;
  color: #606266;
  border-width: 1px;
  border-color: #e8e8e8;
  border-collapse: collapse;
  text-align: left;
  width: 100%;
}
table.gridtable td {
  border-width: 1px;
  border-style: solid;
  border-color: #e8e8e8;
}
.leftTd {
  width: 17%;
  background-color: #fafafa;
  padding: 16px 24px;
  text-align: center;
}
.rightTd {
  width: 35%;
  padding: 16px 24px;
}
#urla {
  width: 100px;
  height: 100px;
}
#urlas {
  width: 100px;
  height: 100px;
}
.orientation {
  width: 100%;
  /*margin: 0 auto;*/
  position: relative;
  overflow: hidden;
}
.orientationFile {
  width: 100%;
  /*margin: 0 auto;*/
  position: relative;
  overflow: hidden;
}
.font {
  position: absolute;
  bottom: 0;
  background: rgba(0, 0, 0, 0.75);
  left: 0;
  width: 100%;
  height: 30%;
  color: #fff;
  line-height: 32px;
  cursor: pointer;
  transform: translateY(109%);
  transition: all 0.3s ease-out 0s;
  text-align: center;
}
.font1 {
  position: absolute;
  bottom: 0;
  background: rgba(0, 0, 0, 0.75);
  left: 0;
  width: 100%;
  height: 30%;
  color: #fff;
  line-height: 32px;
  cursor: pointer;
  transform: translateY(109%);
  transition: all 0.3s ease-out 0s;
  text-align: center;
}
.orientation:hover .font {
  transform: translateY(0%);
}
.orientationFile:hover .font1 {
  transform: translateY(0%);
}
</style>
