<template>
  <div style='height: 100%;' class='soft-page'>
    <a-spin :spinning='loading' v-if='!isOpeningPreview' style='width: 100%; height: 100%;'>
      <!-- 查询区域 -->
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <div class='table-page-search-wrapper-style'>
          <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
            <a-row :gutter='24' ref='row'>
              <a-col :span='spanValue'>
                <a-form-item label='软件名称'>
                  <a-input :maxLength='maxLength' placeholder='请输入软件名称' :allowClear='true' autocomplete='off'
                          v-model='queryParam.softwareName'>
                  </a-input>
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label='软件类别'>
                  <j-dict-select-tag
                    v-model='queryParam.softwareType'
                    placeholder='请选择软件类别'
                    dictCode='softwareCategory'
                    :allowClear="true"
                    @change="changeSoftwareType" :maxLength="50"
                  />
                </a-form-item>
              </a-col>
              <a-col :span='colBtnsSpan()'>
                <span class='table-page-search-submitButtons'
                      :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button type='primary' @click='searchQuery' class='btn-search-style'>查询</a-button>
                  <a-button @click='searchReset' style='margin-left: 10px' class='btn-reset-style'>重置</a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <!-- 查询区域-END -->
      <a-card :bordered='false' :bodyStyle="{ padding: '24px' }" style='height: calc(100% - 96px)' class='core'>
        <div class='contentBox' style='width:100%;height: 100%'>
          <div class="table-operator">
            <a-button @click='handleAdd' style="margin: 0 !important;">新增</a-button>
            <a-button v-if="tab == 2" @click='getGo' style='margin-left: 10px' class='btn-reset-style'>返回</a-button>
          </div>
          <div v-if="tab == 1 && filteredMenuItems.length>0" class='menu-list'>
            <div class='menu-item' v-for='item in filteredMenuItems' :key='item.value'>
              <div class="title">
                <span class="soft-type">{{ item.title }}</span>
                <span @click='menuClick(item)' class="more">查看全部</span>
              </div>
              <div class='soft-list' style='height: calc(100% - 45px);'>
                <a-row :gutter='24' type='flex' style="margin-left: -30px;margin-right:-24px">
                  <div v-for='(citem,idx) in item.dataSource' :key='citem.id+idx' class="soft-item soft-item-padding" :class="{ 'last-row': isLastRow(idx, item.dataSource.length) }">
                    <div class='soft-card-box'>
                      <soft-card :record='citem'
                        :types='types'
                        :cpu-list='cpuList'
                        :dict-options='dictOptions'
                        @click.native='handleDetailPage(citem)'
                        @delete='deleteHandle(citem)' @edit='handleEdit(citem)'>
                      </soft-card>
                    </div>
                  </div>
                </a-row>
              </div>
            </div>
          </div>
          <!-- 查看全部--样式2 -->
          <div v-else-if="tab==2 && dataSource.length>0">
            <div class='soft-list' style='height: calc(100% - 45px);padding-bottom: 30px;margin-top:-5px;min-width:1300px;'>
              <a-row :gutter='24' type='flex' style="margin-left: -24px;margin-right:-24px">
                <a-col :span="24" v-for='item in dataSource' :key="item.id + '_type'" class="soft-item-padding">
                  <div class='soft-card-box'>
                    <soft-card-All :record='item'
                      :types='types'
                      :cpu-list='cpuList'
                      :dict-options='dictOptions'
                      @click.native='handleDetailPage(item)'
                      @delete='deleteHandle(item)' @edit='handleEdit(item)'>
                    </soft-card-All>
                  </div>
                </a-col>
              </a-row>
            </div>
            <div class='pagination'>
              <a-pagination show-size-changer :hideOnSinglePage='false' :default-current='ipagination.current'
                :showSizeChanger='ipagination.showSizeChanger'
                :total='ipagination.total' @change='onChange' :page-size='ipagination.pageSize'
                :pageSizeOptions='ipagination.pageSizeOptions'
                :show-total='(total) => `共 ${ipagination.total} 条`' size='small'>
              </a-pagination>
            </div>
          </div>
          <a-empty v-else-if="!loading" style='margin-top: 8%' />
        </div>
      </a-card>
    </a-spin>
    <SoftwareInfoDetails
      style="width: 100%;height:100%;"
      v-else-if='isOpeningPreview'
      :data="detailsRecord">
    </SoftwareInfoDetails>
    <software-modal ref='modalForm' :types='types' @ok='modalFormOk'></software-modal>
  </div>
</template>

<script>
import '@/assets/less/TableExpand.less'
import {
  mixinDevice
} from '@/utils/mixin'
import {
  JeecgListMixin
} from '@/mixins/JeecgListMixin'
import SoftwareModal from './modules/SoftwareModal'
import JSuperQuery from '@/components/jeecg/JSuperQuery.vue'
import {
  YqFormSearchLocation
} from '@/mixins/YqFormSearchLocation'
import {
  ajaxGetDictItems
} from '@/api/api'
import {
  deleteAction, downFile, getAction
} from '@/api/manage'
import SoftCard from '@views/opmg/softwarePoc/modules/SoftCard.vue'
import SoftCardAll from '@views/opmg/softwarePoc/modules/SoftCardAll.vue'
import SoftwareInfoDetails from './modules/SoftwareInfoDetails'
export default {
  name: 'SoftwareList',
  mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
  components: {
    SoftwareModal,
    JSuperQuery,
    SoftCard,
    SoftCardAll,
    SoftwareInfoDetails
  },
  data() {
    return {
      maxLength:50,
      description: '软件管理表管理页面',
      url: {
        list: '/software/softwareInfoManage/list',
        delete: '/software/softwareInfoManage/delete',
        deleteBatch: '/software/softwareInfoManage/deleteBatch',
        exportXlsUrl: '/software/softwareRepository/exportXls'
      },
      disableMixinCreated: true,
      dictOptions: [],
      cpuList: [],
      curType: 'all',
      tab: 1,
      detailsRecord: {}, // 详情页数据
      isOpeningPreview: false,
      dataSource: [],
      filteredMenuItems: [],
      types: [],
      screenWidth: window.innerWidth, // 当前屏幕宽度
      loading: true, // 初始状态为加载中
      ipagination: {
        current: 1,
        pageSize: 6,
        pageSizeOptions: ['10', '20', '50'],
        showTotal: (total, range) => {
          return ' 共' + total + '条'
        },
        showQuickJumper: true,
        showSizeChanger: false,
        total: 0
      },
    }
  },
  created() {

  },
  mounted() {
    window.addEventListener('resize', this.handleResize); // 监听屏幕宽度变化
    this.initDictData()
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize); // 移除监听
  },
  computed: {
    // 根据屏幕宽度动态计算每行元素数量
    itemsPerRow() {
      if (this.screenWidth < 768) {
        return 1; // md 屏幕下每行 1 个
      } else if (this.screenWidth >= 768 && this.screenWidth < 1600) {
        return 2; // xl 屏幕下每行 2 个
      } else {
        return 3; // xxl 屏幕下每行 3 个
      }
    }
  },
  methods: {
    // 判断当前元素是否属于最后一行
    isLastRow(index, len) {
      const totalItems = len || 0;
      const remainder = totalItems % this.itemsPerRow;
      const lastRowStartIndex = totalItems - (remainder || this.itemsPerRow);
      return index >= lastRowStartIndex;
    },
    // 监听屏幕宽度变化
    handleResize() {
      this.screenWidth = window.innerWidth;
    },
    async loadData() {
      var params = this.getQueryParams() //查询条件
      this.loading = true
      const requests = this.menuItems.map(item => {
        return getAction(this.url.list, {...params, softwareType: item.value,pageSize: 9, pageNo: 1})
          .then(res => {
            if (res.success && res.result) {
              item.dataSource = res.result.records || res.result;
            } else {
              item.dataSource = []
            }
          })
          .catch(error => {
            item.dataSource = []
            // console.error(`请求失败`, error);
          });
      });
      this.loading = false

      // 等待所有请求完成
      await Promise.all(requests);
      this.filteredMenuItems = this.menuItems.filter(item => item.dataSource.length > 0);
    },
    loadDataByType(arg) {
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1
      }

      var params = this.getQueryParams() //查询条件
      this.loading = true
      getAction(this.url.list, params).then((res) => {
        if (res.success && res.result) {
          this.dataSource = res.result.records || res.result
          if (this.dataSource.length < 9) {
            this.clientHeight = false
          }
          this.ipagination.total =res.result.total?res.result.total:0
        }
        if (res.code === 510) {
          this.$message.warning(res.message)
        }
        this.loading = false
      }).catch(err => {
        this.$message.warning(err.message)
        this.loading = false
      })
    },
    onShowSizeChange(current, pageSize) {
      this.ipagination.pageSize = pageSize
      this.ipagination.current = current
      // this.loadData()
    },
    onChange(pageNum, pageSize) {
      this.ipagination.current = pageNum
      this.ipagination.pageSize = pageSize
      this.loadDataByType()
    },
    changeSoftwareType(e) {
      this.$forceUpdate()
    },
    // 类型点击
    menuClick(item) {
      this.curType = item.value;
      this.queryParam.softwareType = item.value =="all" ? undefined : item.value
      this.loadDataByType(1)
      this.tab = 2;
    },
    initDictData() {
      //根据字典Code, 初始化字典数组
      ajaxGetDictItems('cpuArch', null).then((res) => {
        if (res.success) {
          this.cpuList = res.result
        }
      })
      ajaxGetDictItems('os_type', null).then((res) => {
        if (res.success) {
          this.dictOptions = res.result
        }
      })
      ajaxGetDictItems('app_classify', null).then((res) => {
        if (res.success) {
          this.types = res.result
        }
      })
      ajaxGetDictItems('softwareCategory', null).then((res) => {
        if (res.success) {
          this.menuItems = res.result
          this.loadData()
        }
      })
    },
    deleteHandle(item) {
      if (!this.url.delete) {
        this.$message.error('请设置url.delete属性!')
        return
      }
      var that = this
      deleteAction(that.url.delete, {
        id: item.id
      }).then((res) => {
        if (res.success) {
          that.$message.success(res.message)
          that.loadData()
        } else {
          that.$message.warning(res.message)
        }
      })
    },
    //下载
    downloadSofware(data) {
      let path = data.softwareFile
      if (path) {
        let arr = path.split('/')
        let fileName = ''
        if (arr.length > 0) {
          fileName = arr[arr.length - 1]
        }
        let url = window._CONFIG['domianURL'] + '/software/softwareRepository/download' + '/' + path + '?id=' + data.id
        this.downloadFileByURL(url, fileName)
      } else {
        this.$message.warning('没有软件文件！')
      }
    },
    pButton2() {
      this.isOpeningPreview = false
      this.detailsRecord = {}
      if (this.tab == 1) {
        this.loadData()
      } else if (this.tab == 2) {
        this.loadDataByType()
      }
    },
    handleDetailPage: function (record, index = 1) {
      this.isOpeningPreview = true
      this.detailsRecord = record
    },
    modalFormOk() {
      this.searchQuery()
    },
    searchQuery() {
      if (this.tab == 1) {
        if (this.queryParam.softwareType) {
          // 跳转到查看全部的列表
          this.menuClick({ value: this.queryParam.softwareType })
        } else {
          this.loadData(1)
        }
      } else if (this.tab == 2) {
        this.loadDataByType(1)
      }
    },
    //重置
    searchReset() {
      this.queryParam = {}
      this.tab = 1;
      this.loadData(1)
    },
    // 返回上一页
    getGo() {
      this.searchReset()
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
@primaryColor: #409eff;
.handleAdd{
  color: @primaryColor;
  border-color: @primaryColor;
}

.menu-list {
  margin-top: -14px;

  .menu-item {
    width: 100%;
    color: #8a8a8a;
    margin-top: 10px;
    border-bottom: 1px solid #e8e8e8;
    margin-top: 38px;
    padding-bottom: 40px;

    .title {
      display: flex;
      justify-content: space-between;
      .soft-type {
        font-size: 18px;
        color: #000000;
        line-height: 24px;
        font-weight: 500;
      }
      .more {
        cursor: pointer;
        font-size: 14px;
        color: #1890FF;
        line-height: 24px;
        font-weight: 400;
        padding-right: 12px;
      }
    }
    &:last-child {
      border: none;
    }
  }
}

/deep/ .ant-spin-container{
  height: 100%;
}

/deep/ .core .ant-card-body {
  height: 100%;
  overflow-y: auto;
}

.soft-list::-webkit-scrollbar {
  width: 0;
}

.soft-card-box {
  width: 100%;
}

.pagination {
  position: fixed;
  bottom: 15px;
  left: 50%;
  background: #fff;
  padding: 10px 20px;
}

.soft-item-padding {
  padding-left: 30px !important;
  padding-right: 30px !important;
}

/* 页面自适应效果 */
.soft-item {
  width: 100%; /* 默认每行 1 个元素（md 屏幕） */
}

/* xl 屏幕下每行 2 个元素 */
@media (min-width: 768px) and (max-width: 1600px) {
  .soft-item {
    width: 50%;
  }
}

/* xxl 屏幕下每行 3 个元素 */
@media (min-width: 1600px) {
  .soft-item {
    width: 33.33%;
  }
}
</style>