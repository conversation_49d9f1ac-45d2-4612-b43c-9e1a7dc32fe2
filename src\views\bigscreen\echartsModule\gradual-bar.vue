<template>
  <div :id="chartId" ref="chartDom" style="width: 100%; height: 100%"></div>
</template>

<script>
  export default {
    props: ['chartId', 'colorObj', 'round', 'chartObj'],
    data() {
      return {
        barWidth: 0,
      }
    },
    created() {},
    mounted() {
      if (this.chartId) {
        let rect = this.$refs.chartDom.getBoundingClientRect()
        let syy = (rect.height - 16) / 10 - 20
        syy <= 2 ? (syy = 2) : syy
        this.barWidth = syy
      }
    },
    watch: {
      chartObj() {
        if (this.chartId) {
          this.drawChart()
        }
      }
    },
    methods: {
      drawChart() {
        var datas = this.chartObj
        let myChart = this.$echarts.init(document.getElementById(this.chartId))
        myChart.setOption({
          tooltip: {
            show: true,
            trigger: 'item',
            axisPointer: {
              type: 'shadow',
            },
            backgroundColor: 'rgba(9, 24, 48, 0.5)',
            borderColor: 'rgba(75, 253, 238, 0.4)',
            textStyle: {
              color: '#CFE3FC',
            },
            borderWidth: 1,
            appendToBody: true,
            formatter: (a, b) => {
              return '名称：' + a.name + "<br/> 使用率：" + a.value + "%"
            },
            transitionDuration: 0, //echart防止tooltip的抖动
          },
          legend: {
            show: false,
          },
          grid: {
            left: 0,
            right: '5%',
            top: 16,
            bottom: 0,
            containLabel: false,
          },
          xAxis: {
            show: false,
            type: 'value',
          },
          yAxis: [{
              gridIndex: 0,
              splitLine: 'none',
              axisTick: 'none',
              axisLine: 'none',
              data: datas,
              inverse: true,
              axisLabel: {
                show: true,
                verticalAlign: 'bottom',
                align: 'left',
                padding: [0, 10, this.barWidth / 2, 10],
                textStyle: {
                  color: '#fff',
                  fontSize: '14',
                },
                formatter: function (value, idx) {
                  if (datas[idx].name.length > 13) {
                    return datas[idx].name.substring(0, 13) + '...'
                  } else {
                    return datas[idx].name
                  }
                },
                // formatter: (value) => {
                //   if (value.length > 13) {
                //     return value.substring(0, 13) + '...'
                //   } else {
                //     return value
                //   }
                // },
              },
            },
            {
              gridIndex: 0,
              splitLine: 'none',
              axisTick: 'none',
              axisLine: 'none',
              data: datas,
              inverse: true,
              axisLabel: {
                show: true,
                verticalAlign: 'bottom',
                align: 'right',
                padding: [0, 10, this.barWidth / 2, 0],
                textStyle: {
                  color: '#fff',
                  fontSize: '14',
                },
                formatter: function (value, idx) {
                  return datas[idx].memUsed + "%"
                },
              },
            },
          ],
          series: [{
            z: 2,
            type: 'bar',
            showBackground: true,
            backgroundStyle: {
              color: '#212129',
            },
            barWidth: this.barWidth,
            data: datas.map((item, i) => {
              let colors = ['#12191C', '#63DEA8']
              if (i % 2 === 0) {
                colors = ['#12191C', '#E5C83D']
              }
              if (this.colorObj) {
                colors = this.colorObj;
              }

              var itemStyle = {
                color: new this.$echarts.graphic.LinearGradient(0, 0, 1, 0, [{
                    offset: 0,
                    color: colors[0],
                  },
                  {
                    offset: 1,
                    color: colors[1],
                  },
                ]),
                barBorderRadius: this.round,
              }
              return {
                name: item.name,
                value: item.memUsed,
                itemStyle: itemStyle,
              }
            }),
            label: {
              normal: {
                color: '#fff',
                show: false,
                position: [0, '-' + (this.barWidth + 4) + 'px'],
                textStyle: {
                  fontSize: 14,
                },
                formatter: function (a, b) {
                  return a.name
                },
              },
            },
          }, ],
        })
        window.addEventListener('resize', () => {
          myChart.resize()
        })
      },
    },
  }
</script>

<style>
</style>