<template>
  <div class="container">
    <a-row :gutter="24" style="height:100%;">
      <a-col style='width: 100%; height: 100%; display: flex; flex-direction: column'>
        <a-card :bordered='false' style='width: 100%; flex: auto'>
          <a-col :xs="{ span: 22, offset: 1 }" :sm="{ span: 18, offset: 3 }" :md="{ span: 13, offset: 6 }"
                 style="height:100%;">
            <div class="inner">
              <div class="header-box">
                <div class="title">{{ knowledgeSearchName }}</div>
                <img class="plane-img" src="~@assets/img/paper-plane.png" />
              </div>
              <div class="input-box">
                <a-input-group compact style="height:100%;">
                  <a-input v-model="value" placeholder="请输入关键字" style="" class="a-input"
                           @pressEnter="goSearchResultPage(0)" :maxLength="50"/>
                  <a-button type="primary" class="btn" style="" @click="goSearchResultPage(0)">
                    <a-icon type="search"></a-icon>
                    搜索
                  </a-button>
                </a-input-group>
              </div>
            </div>
            <div class="out">
              <!-- 热门知识 -->
              <hot-knowledge :theme="'theme1'" @goDetail="goKnowledgeDetail" style="flex:1;margin-right: 24px;overflow: hidden">
                <span slot="top">TOP</span>
              </hot-knowledge>
              <!-- 搜索历史 -->
              <history-knowledge ref="history" :theme="'theme1'" :historyList="historyList" @goSearchResultPage="goSearchResultPage" @reloadKSearchHistory="getSearchHistoryList" style="flex:1;overflow: hidden"></history-knowledge>
            </div>
          </a-col>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import hotKnowledge from './modules/hotKnowledge'
import historyKnowledge from './modules/historyKnowledge'
import { knowledgeSearchIndexMixins } from './modules/knowledgeSearchIndexMixins'
export default {
  name: 'knowledgeSearchIndex',
  mixins: [knowledgeSearchIndexMixins],
  components: {
    hotKnowledge,
    historyKnowledge
  },
  data() {
    return {
      value: '',
      defaultName: '信创联合运维知识库', // 设置默认值
    }
  },
  created() {
    // 根据配置字典获取知识搜索大标题（平台）
    this.getKnowledgeSearchName('platform')
  },
  methods: {},
}
</script>
<style scoped lang="less">
.over {
  /* 溢出用省略号*/
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
}

.container {
  width: 100%;
  height: 100%;
  background: #fff;

  .inner {
    width: 100%;
    margin-top: 18%;

    .header-box {
      display: flex;
      justify-content: center;
      align-items: center;

      .title {
        /*38/64*/
        font-size: 0.59375rem;
        color: rgba(51, 51, 51, 0.85);
        letter-spacing: 3px;
        text-align: center;
        font-weight: 550;
        white-space: nowrap;
      }

      .plane-img {
        /*114/64*/
        width: 1.78125rem;
        /*75/64*/
        height: 1.171875rem;

        position: relative;
        top: -22px;
        margin-left: 5px;
      }
    }

    .input-box {
      width: 100%;
      margin-top: 60px;
      height: 54px;
      position: relative;

      .a-input {
        width: calc(100% - 2.4375rem);
        height: 100%;
        text-indent: 2em;
        border: 1px solid #E7E7E7;
        box-shadow: 2px 2px 6px 1px rgba(22, 27, 33, 0.16);
        border-radius: 4px;
        font-size: 18px;
      }

      .btn {
        /*156/64*/
        width: 2.4375rem;
        height: 100%;
        background: #409EFF;
        border-color: #409EFF;
        font-size: 18px;
        letter-spacing: 5px;
      }

      .line {
        position: absolute;
        top: 0;
        bottom: 0;
        margin: auto;
        left: 24px;
        width: 3px;
        height: 25px;
        background-color: #364d80;
        border-radius: 3px;
        z-index: 1;
      }
    }
  }

  .out {
    width: 100%;
    padding-top: 5%;
    padding-bottom: 5%;
    display: flex;
    justify-content: space-between;

  }
}
</style>