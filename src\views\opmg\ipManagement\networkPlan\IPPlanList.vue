<template>
  <a-col :xl='20' :lg='18' :md='16' :sm='14' :xs='14' style='height: 100%; overflow: hidden; overflow-y: auto'>
    <a-row style='height: 100%; margin-left: 16px; margin-right: 4px'>
      <a-col style='width: 100%; height: 100%; display: flex; flex-direction: column'>
        <!-- 查询区域 -->
        <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0', marginRight: '12px' }" class='card-style'
          style='width: 100%'>
          <div class='table-page-search-wrapper'>
            <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
              <a-row :gutter='24' ref='row'>
                <a-col :span='spanValue'>
                  <a-form-item label='使用人'>
                    <a-input placeholder='请输入使用人' v-model='queryParam.userName' :allowClear='true' autocomplete='off'
                      :maxLength="maxLength" />
                  </a-form-item>
                </a-col>
                <a-col :span='spanValue'>
                  <a-form-item label='IP地址'>
                    <a-input placeholder='请输入IP地址' v-model='queryParam.ipAddress' :allowClear='true'
                      autocomplete='off' :maxLength="maxLength"/>
                  </a-form-item>
                </a-col>
                <a-col :span='spanValue'>
                  <a-form-item label='MAC地址'>
                    <a-input placeholder='请输入MAC地址' v-model='queryParam.macAddress' :allowClear='true'
                      autocomplete='off' :maxLength="maxLength"/>
                  </a-form-item>
                </a-col>
                <a-col :span='spanValue' v-show='toggleSearchStatus'>
                  <a-form-item label='联系电话'>
                    <a-input placeholder='请输入联系电话' v-model='queryParam.phone' :allowClear='true' autocomplete='off'
                      :maxLength="maxLength" />
                  </a-form-item>
                </a-col>
                <a-col :span='colBtnsSpan()'>
                  <span class='table-page-search-submitButtons'
                    :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                    <a-button type='primary' class='btn-search btn-search-style' @click='searchQuery'>查询</a-button>
                    <a-button class='btn-reset btn-reset-style' @click='searchReset'>重置</a-button>
                    <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                      {{ toggleSearchStatus ? '收起' : '展开' }}
                      <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                    </a>
                  </span>
                </a-col>
              </a-row>
            </a-form>
          </div>
        </a-card>
        <a-card :bordered='false' style='width: 100%; flex: auto'>
          <div class='table-operator table-operator-style'>
            <a-button @click='handleAdd({})'>添加IP</a-button>
            <a-dropdown v-if='selectedRowKeys.length > 0'>
              <a-menu slot="overlay" style='text-align: center'>
                <a-menu-item key='1' @click='batchDel'>删除</a-menu-item>
              </a-menu>
              <a-button> 批量操作
                <a-icon type='down' />
              </a-button>
            </a-dropdown>
          </div>
          <a-table ref='table' bordered :row-key='(record, index) => {return record.id}' :columns='planColumn'
            :dataSource='dataSource' :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
            :pagination='ipagination' :loading='loading'
            :rowSelection='{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }' @change='handleTableChange'>
            <span slot='action' class='caozuo' slot-scope='text, record'>
              <a @click='handleDetailPage1(record)'>查看</a>
              <a-divider type='vertical' />
              <a-dropdown>
                <a class='ant-dropdown-link'>更多
                  <a-icon type='down' /></a>
                <a-menu slot='overlay'>
                  <a-menu-item>
                    <a @click='handleEdit(record)' style="color: #409eff">编辑</a>
                  </a-menu-item>
                  <a-menu-item>
                    <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                      <a style="color: #409eff">删除</a>
                    </a-popconfirm>
                  </a-menu-item>
                </a-menu>
              </a-dropdown>
            </span>
            <span slot='isEnabled' slot-scope='text'>{{ text==1 ? '是' : '否' }}</span>
          </a-table>
        </a-card>
      </a-col>
    </a-row>
    <IP-plan-modal ref='modalForm' @ok='modalFormOk'></IP-plan-modal>
  </a-col>
</template>

<script>
  import IPPlanModal from './modules/IPPlanModal.vue'
  import {
    filterObj
  } from '@/utils/util'
  import {
    getAction,
    deleteAction,
  } from '@/api/manage'
  import {
    YqFormSearchLocation
  } from '@/mixins/YqFormSearchLocation'
  export default {
    mixins: [YqFormSearchLocation],
    components: {
      IPPlanModal
    },
    props: {
      info: {
        type: Object,
        default: () => {},
      },
      topData: {
        type: Object,
        default: () => {},
      },
    },
    data() {
      return {
        maxLength:50,
        formItemLayout: {
          labelCol: {
            style: 'width:80px'
          },
          wrapperCol: {
            style: 'width:calc(100% - 80px)'
          }
        },
        loading: false,
        queryParam: {},
        columns: [],
        dataSource: [],
        ipagination: {
          current: 1,
          pageSize: 10,
          pageSizeOptions: ['10', '20', '50'],
          showTotal: (total, range) => {
            return ' 共' + total + '条'
          },
          showQuickJumper: true,
          showSizeChanger: true,
          total: 0
        },
        selectedRowKeys: [],
        selectionRows: [],
        toggleSearchStatus: false,
        IPPlanName: '', // 当前的ip
        segmentId: '', // 当前所属网段id
        planColumn: [{
            title: '使用人',
            dataIndex: 'userName',
            customCell: () => {
              let cellStyle = 'text-align: left;min-width: 80px;max-width:200px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: 'IP地址',
            dataIndex: 'ipAddress',
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 100px;max-width:200px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: 'MAC地址',
            dataIndex: 'macAddress',
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 100px;max-width:300px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '联系电话',
            dataIndex: 'phone',
            customCell: () => {
              let cellStyle = 'text-align: center;width: 180px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '是否启用',
            dataIndex: 'enabled',
            scopedSlots: {
              customRender: 'isEnabled'
            },
            customCell: () => {
              let cellStyle = 'text-align: center;max-width:100px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '是否扫描',
            dataIndex: 'scan',
            scopedSlots: {
              customRender: 'isEnabled'
            },
            customCell: () => {
              let cellStyle = 'text-align: center;max-width:100px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            fixed: 'right',
            width: 140,
            scopedSlots: {
              customRender: 'action'
            }
          }
        ],
        url: {
          list: '/devops/ip/plan/list',
          delete: '/devops/ip/plan/delete',
          deleteBatch: '/devops/ip/plan/deleteBatch',
        },
      }
    },
    watch: {
      info: {
        immediate: true,
        handler(newVal) {
          if (newVal.IPPlanName != null && newVal.IPPlanName != '') {
            this.IPPlanName = newVal.IPPlanName
            this.queryParam.ipAddress = newVal.IPPlanName
          } else {
            this.IPPlanName = null
            this.queryParam.ipAddress = null
          }

          if (newVal.segmentId != null && newVal.segmentId != '') {
            this.segmentId = newVal.segmentId
            this.queryParam.segmentId = newVal.segmentId
          } else {
            this.segmentId = null
            this.queryParam.segmentId = null
          }
          this.loadData()
        }
      }
    },
    methods: {
      searchQuery() {
        this.loadData(1)
      },
      searchReset() {
        this.queryParam = {segmentId:this.segmentId}
        this.loadData(1)
      },
      onSelectChange(selectedRowKeys, selectionRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectionRows = selectionRows
      },
      handleEdit: function (record) {
        this.$refs.modalForm.edit(record);
        this.$refs.modalForm.title = '编辑';
        this.$refs.modalForm.disableSubmit = false;
      },
      handleAdd({}) {
        this.$refs.modalForm.add({}, this.topData);
        this.$refs.modalForm.title = '新增';
        this.$refs.modalForm.disableSubmit = false;
      },
      handleDelete: function (id) {
        if (!this.url.delete) {
          this.$message.error('请设置url.delete属性!')
          return
        }
        var that = this
        deleteAction(that.url.delete, {
          id: id
        }).then((res) => {
          if (res.success) {
            //重新计算分页问题
            that.reCalculatePage(1)
            that.$message.success(res.message)
            that.loadData()
          } else {
            that.$message.warning(res.message)
          }
        })
      },
      reCalculatePage(count) {
        //总数量-count
        let total = this.ipagination.total - count
        //获取删除后的分页数
        let currentIndex = Math.ceil(total / this.ipagination.pageSize)
        //删除后的分页数<所在当前页
        if (currentIndex < this.ipagination.current) {
          this.ipagination.current = currentIndex
        }
      },
      handleTableChange(pagination, filters, sorter) {
        //分页、排序、筛选变化时触发
        //TODO 筛选
        if (Object.keys(sorter).length > 0) {
          this.isorter.column = sorter.field
          this.isorter.order = 'ascend' == sorter.order ? 'asc' : 'desc'
        }
        this.ipagination = pagination
        this.loadData()
      },
      handleToggleSearch() {
        this.toggleSearchStatus = !this.toggleSearchStatus
      },
      loadData(arg) {
        if (this.IPPlanName != null && this.IPPlanName != '') {
          this.queryParam.ipAddress = this.IPPlanName
          this.queryParam.segmentId = null
        }
        if (!this.url.list) {
          this.$message.error('请设置url.list属性!')
          return
        }
        //加载数据 若传入参数1则加载第一页的内容
        if (arg === 1) {
          this.ipagination.current = 1
        }

        var params = this.getQueryParams() //查询条件
        this.loading = true
        getAction(this.url.list, params).then((res) => {
          if (res.success) {
            //update-begin---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
            this.dataSource = res.result.records || res.result
            if (this.dataSource.length < 9) {
              this.clientHeight = false
            }
            //author:weng    Date:20210402  for：if(res.result.total>0) 有错误，无查询结果时，页码显示有问题
            this.ipagination.total = res.result.total ? res.result.total : 0
            //update-end---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
          }
          if (res.code === 510) {
            this.$message.warning(res.message)
          }
          this.loading = false
        })
        this.$emit('refresh')
      },
      batchDel: function () {
        if (!this.url.deleteBatch) {
          this.$message.error('请设置url.deleteBatch属性!')
          return
        }
        if (this.selectedRowKeys.length <= 0) {
          this.$message.warning('请选择一条记录！')
          return
        } else {
          var ids = ''
          for (var a = 0; a < this.selectedRowKeys.length; a++) {
            ids += this.selectedRowKeys[a] + ','
          }
          var that = this
          this.$confirm({
            title: '确认删除',
            okText: '是',
            cancelText: '否',
            content: '是否删除选中数据?',
            onOk: function () {
              that.loading = true
              deleteAction(that.url.deleteBatch, {
                  ids: ids
                })
                .then((res) => {
                  if (res.success) {
                    //重新计算分页问题
                    that.reCalculatePage(that.selectedRowKeys.length)
                    that.$message.success(res.message)
                    that.loadData()
                    that.onClearSelected()
                  } else {
                    that.$message.warning(res.message)
                  }
                })
                .finally(() => {
                  that.loading = false
                })
            }
          })
        }
      },
      getQueryParams() {
        //获取查询条件
        let sqp = {}
        if (this.superQueryParams) {
          sqp['superQueryParams'] = encodeURI(this.superQueryParams)
          sqp['superQueryMatchType'] = this.superQueryMatchType
        }
        var param = Object.assign(sqp, this.queryParam, this.isorter, this.filters)
        param.field = this.getQueryField()
        param.pageNo = this.ipagination.current
        param.pageSize = this.ipagination.pageSize
        return filterObj(param)
      },
      getQueryField() {
        //TODO 字段权限控制
        var str = 'id,'
        this.columns.forEach(function (value) {
          str += ',' + value.dataIndex
        })
        return str
      },
      handleDetailPage1(record) {
        this.$emit('detail', 4, record)
      },
      modalFormOk() {
        // 新增/修改 成功时，重载列表
        this.loadData()
      },
    }
  }
</script>

<style scoped lang="less">
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';
</style>