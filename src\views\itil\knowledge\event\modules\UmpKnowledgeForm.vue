<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-item class="two-words" label="类型" :labelCol="labelCol" :wrapperCol="wrapperCol" style="margin-bottom: 14px">
               <a-select
                placeholder="请选择类型"
                 v-decorator="[ 'type', { rules: [{ required: true, message: '请选择类型' }] },]" >
                <a-select-option value="故障">故障</a-select-option>
                <a-select-option value="咨询">咨询</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item class="two-words" label="标题" :labelCol="labelCol" :wrapperCol="wrapperCol" style="margin-bottom: 16px">
              <a-input v-decorator="['title', { rules: [{ required: true, message: '请输入标题!' },{min:2,max:20,message:'标题长度在2-20个字符之间'}] }]" placeholder="请输入标题"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item class="two-words" label="描述" :labelCol="labelCol" :wrapperCol="wrapperCol" style="margin-bottom: 16px">
              <a-textarea
                  v-decorator="['description', { rules: [{ required: true, message: '请输入描述!' },{max:50,message:'描述长度不可超过50个字符'}] }]"
                  placeholder="请输入描述"
                  :auto-size="{ minRows: 3, maxRows: 5 }"
                />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="解决方案" :labelCol="labelCol" :wrapperCol="wrapperCol" style="margin-bottom: 16px">
              <a-textarea
                  v-decorator="['plan',{rules:[{max:200,message:'解决方案长度不可超过200字符'}]}]"
                  placeholder="请输入解决方案"
                  :auto-size="{ minRows: 3, maxRows: 5 }"
                />
            </a-form-item>
          </a-col>
          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import pick from 'lodash.pick'
import { validateDuplicateValue } from '@/utils/util'
import JFormContainer from '@/components/jeecg/JFormContainer'

export default {
  name: 'UmpKnowledgeForm',
  components: {
    JFormContainer
  },
  props: {
    //流程表单data
    formData: {
      type: Object,
      default: () => {},
      required: false
    },
    //表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false
    },
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  data() {
    return {
      form: this.$form.createForm(this),
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      confirmLoading: false,
      validatorRules: {},
      url: {
        add: '/knowledge/add',
        edit: '/knowledge/edit',
        queryById: '/knowledge/queryById'
      }
    }
  },
  computed: {
    //可行性测试，根据文件路径动态加载组件
    LcDict: function() {
      var myComponent = () => import(`@/components/dict/JDictSelectTag`)
      return myComponent
    },
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    }
  },
  created() {
    //如果是流程中表单，则需要加载流程表单data
    this.showFlowData()
  },
  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(pick(this.model, 'type', 'title', 'description', 'plan', 'recordType'))
      })
    },
    //渲染流程表单数据
    showFlowData() {
      if (this.formBpm === true) {
        let params = { id: this.formData.dataId }
        getAction(this.url.queryById, params).then(res => {
          if (res.success) {
            this.edit(res.result)
          }
        })
      }
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let formData = Object.assign(this.model, values)
          formData.recordType = 1
          httpAction(httpurl, formData, method)
            .then(res => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    popupCallback(row) {
      this.form.setFieldsValue(pick(row, 'type', 'title', 'description', 'plan', 'recordType'))
    }
  }
}
</script>
<style lang="less" scoped>
::v-deep .two-words > div > label {
  letter-spacing: 4px;
}
::v-deep .two-words > div > label::after {
  letter-spacing: 0px;
}
</style>