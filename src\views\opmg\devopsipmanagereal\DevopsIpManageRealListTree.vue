<template>
  <div class="tree">
    <div v-for="(item, index) in treeData" :key="index" class="ipStyle">
      <a @click="getTreeIP(item, index)" :class="{ active: activeKey === index }" :id="'treeStyle' + index" v-clickDown>
        <a-icon v-if="activeKey === index" type="folder-open" style="margin-right: 6px" />
        <a-icon v-else type="folder" style="margin-right: 6px" />
        {{ item.ipSegsent }}
      </a>
    </div>
  </div>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
export default {
  name: 'DevopsIpManageRealListTree',
  props: ['getTree'],
  data() {
    return {
      treeData: [],
      activeKey: '',
      ipCont: 'aaa',
      url: '/devopsipsegsent/devopsIpSegsent/getIpSegsentArr',
    }
  },
  created() {
    this.initData()
  },
  directives: {
    clickDown: {
      inserted(el, binging) {
        el.id == 'treeStyle0' ? el.click() : null // 只点击第一个，id是在循环中手动添加的
      },
    },
  },
  mounted() {
    // this.getTreeIP(this.treeData[0],0);
  },
  methods: {
    initData() {
      getAction(this.url, {}).then((res) => {
        if (res.success) {
          this.treeData = res.result
        }
      })
    },
    getTreeIP(item, index) {
      this.ipCont = item
      this.activeKey = index
      this.getTree(item)
    },
  },
}
</script>

<style scoped>
.tree {
  background-color: #ffffff;
  height: 100%;
  padding: 24px;
  overflow: hidden;
  overflow-x: auto;
  white-space: nowrap;
}
.ipStyle {
  font-size: 1.3em;
  padding-bottom: 17px;
}
.ipStyle a {
  color: #595959;
  padding: 4px 7px;
}
a:active,
a:hover,
.active {
  background-color: #ecf5ff;
  color: #409eff !important;
}
</style>