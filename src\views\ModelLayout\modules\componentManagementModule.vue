<template>
  <a-drawer :title="title" :width="drawerWidth" @close="handleCancel" :destroyOnClose="true" :visible="visible"
    :confirmLoading="confirmLoading">
    <div :style="{ width: '100%', border: '1px solid #e9e9e9', padding: '10px 16px', background: '#fff' }">
      <a-spin :spinning="confirmLoading">
        <a-form :form="form">
          <a-form-item label="类型判断" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-radio-group @change="isCmpChange"
              v-decorator="['isCmp', { initialValue: isCmp, rules: [{ required: true, message: '请选择类型判断' }] }]">
              <a-radio :value="'0'">分类</a-radio>
              <a-radio :value="'1'">组件</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item label="父级节点" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-tree-select allow-clear style="width: 100%" :dropdownStyle="{ maxHeight: '200px', overflow: 'auto' }"
              :treeData="treeData" v-model="parentId" placeholder="请选择父级节点" :disabled="disableParent"
              @select="parentChange">
            </a-tree-select>
          </a-form-item>
          <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" :label="nameLabel">
            <a-input placeholder="请输入名称" v-decorator="['name', validatorRules.name]" autocomplete="off"
              :allowClear="true" />
          </a-form-item>
          <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" :label="codeLabel">
            <a-input placeholder="请输入标识" v-decorator="['code', validatorRules.code]" autocomplete="off"
              :allowClear="true" />
          </a-form-item>
          <a-form-item label="分类类型" :labelCol="labelCol" :wrapperCol="wrapperCol" v-if="!this.isCmpJudge">
            <a-select v-decorator="['cmpGroup', validatorRules.cmpGroup]" placeholder="请选择分类类型" autocomplete="off"
              :disabled="disableGroup" @change="groupChange">
              <a-select-option v-for="item in typeList" :key="item.id" :value="item.value">{{
                item.title
              }}</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="分类协议" :labelCol="labelCol" :wrapperCol="wrapperCol" :allowClear="true"
            v-if="!this.isCmpJudge && (this.group == 2 || this.group == 3)">
            <a-select
              v-decorator="['protocol', { initialValue: protocol, rules: [{ required: true, message: '请选择协议' }] }]"
              placeholder="请选择分类协议" autocomplete="off" :disabled="disableProtocol">
              <a-select-option v-for="item in protocolList" :key="item.id" :value="item.value">{{
                item.title
              }}</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="序号">
            <a-input-number placeholder="请输入序号" v-decorator="['cmpSerial']" autocomplete="off" :allowClear="true"
              :min="1"></a-input-number>
          </a-form-item>
          <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" :label="descLabel">
            <a-input placeholder="请输入说明" v-decorator="['cmpDesc', validatorRules.cmpDesc]" autocomplete="off"
              :allowClear="true" />
          </a-form-item>
          <params-Model ref="paramModel" v-model="paramModel" :childrenDataList="childrenDataList"
            @func="childrenChange" :childrenChange="childrenChange"
            v-if="this.childrenDataList.length > 0 && this.isCmpJudge"></params-Model>
          <div style="text-align: center; margin-bottom: 10px" v-if="this.isCmpJudge & this.group != 3">
            <a @click="addChildren">
              <a-icon type="plus" />
              添加参数</a>
          </div>
          <a-form-item class="two-words" label="图标" :labelCol="labelCol" :wrapperCol="wrapperCol"
            v-if="this.isCmpJudge">
            <!-- <svg-upload v-decorator="['cmpIcon']"></svg-upload> -->

            <j-image-upload :isMultiple="true" bizPath='image/cmpIcon' v-decorator="['cmpIcon']"></j-image-upload>
          </a-form-item>
        </a-form>
      </a-spin>
      <a-row :style="{ textAlign: 'right' }">
        <a-button :style="{ marginRight: '8px' }" @click="handleCancel"> 关闭 </a-button>
        <a-button :disabled="disableSubmit" @click="handleOk" type="primary">确定</a-button>
      </a-row>
    </div>
    <params-Model-Module ref="modalForm" @ok="modalFormOk"></params-Model-Module>
  </a-drawer>
</template>

<script>
  import {
    addModel,
    editModel,
    ajaxGetDictItems,
    getDictItemsFromCache
  } from '@/api/api'
  import pick from 'lodash.pick'
  import JDictSelectTag from '@/components/dict/JDictSelectTag'
  import paramsModel from './paramsModel.vue'
  import paramsModelModule from './paramsModelModule.vue'
  // import svgUpload from '@/components/jeecg/svgUpload'
  import JImageUpload from '@/components/jeecg/JImageUpload'
  import {
    getAction,
    deleteAction,
    httpAction
  } from '@/api/manage'

  export default {
    name: 'componentManagementModule',
    components: {
      JDictSelectTag,
      // svgUpload,
      JImageUpload,
      paramsModel,
      paramsModelModule,
    },

    data() {
      return {
        localMenuType: 0,
        protocol: undefined,
        group: '',
        nameLabel: '',
        codeLabel: '',
        descLabel: '',
        isCmpJudge: false,
        disableGroup: false,
        disableProtocol: false,
        paramModel: {},
        drawerWidth: 700,
        childrenDataList: [], //子列表
        treeData: [],
        typeList: [],
        protocolList: [],
        title: '操作',
        visible: false,
        disableSubmit: false,
        disableParent: false,
        model: {},
        parentId: undefined,
        isCmp: '0',
        show: true, //根据菜单类型，动态显示隐藏表单元素
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          },
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          },
        },
        validatorRules: {
          name: {
            rules: [{
                required: true,
                message: '请输入名称'
              },
              {
                max: 20,
                message: '名称长度不超过20个字符！',
                trigger: 'blur'
              },
            ],
          },
          code: {
            rules: [{
                required: true,
                message: '请输入标识'
              },
              {
                min: 0,
                max: 50,
                message: '标识长度应在0-50之间！  '
              },
            ],
          },
          cmpGroup: {
            rules: [{
              required: true,
              message: '请选择组件类型'
            }],
          },
          cmpDesc: {
            rules: [{
              max: 100,
              message: '组件说明长度在100个字符以内！  '
            }],
          },
        },
        url: {
          add: '/flow/cmp/add',
          edit: '/flow/cmp/edit',
          parent: '/flow/cmp/queryCmpCategoryTree',
        },
        confirmLoading: false,
        form: this.$form.createForm(this),
        sysType: window._CONFIG['system_Type'],
        platformType: '',
        platformTypes: [],
      }
    },
    created() {
      //获取组件类型的字典
      this.initDictData('cmpType', 'typeList')
      this.initDictData('cmpProtocol', 'protocolList')
    },
    methods: {
      groupChange(value) {
        this.group = value
        if (value == 1) {
          this.protocol = 'COMMON'
        } else {
          this.protocol = undefined
        }
      },
      initDictData(dictCode, listname) {
        //根据字典Code, 初始化字典数组
        ajaxGetDictItems(dictCode, null).then((res) => {
          if (res.success) {
            this[listname] = res.result
          }
        })
      },
      isCmpChange(value) {
        if (value.target.value == 1) {
          this.isCmpJudge = true
          this.nameLabel = '组件名称'
          this.codeLabel = '组件标识'
          this.descLabel = '组件说明'
        } else {
          this.isCmpJudge = false
          this.nameLabel = '分类名称'
          this.codeLabel = '分类标识'
          this.descLabel = '分类说明'
        }
      },
      parentChange(value) {
        this.parentId = value
      },
      loadTree() {
        var that = this
        getAction(this.url.parent).then((res) => {
          if (res.success) {
            that.treeData = res.result
          }
        })
      },
      add() {
        // 默认值
        this.edit({})
      },
      edit(record) {
        if (record.id != '' && record.id != null && record.id != undefined) {
          this.disableParent = true
        } else {
          this.disableParent = false
        }
        if (record.isCmp == 1) {
          this.isCmpJudge = true
          this.nameLabel = '组件名称'
          this.codeLabel = '组件标识'
          this.descLabel = '组件说明'
        } else {
          this.isCmpJudge = false
          this.nameLabel = '分类名称'
          this.codeLabel = '分类标识'
          this.descLabel = '分类说明'
        }
        if (this.localMenuType == 1) {
          this.disableParent = true
          this.disableGroup = true
          this.disableProtocol = true
        } else {
          this.disableGroup = false
          this.disableProtocol = false
        }
        if (record.cmpGroup != undefined) {
          this.group = record.cmpGroup
        } else {
          this.group = ''
        }
        if (record.parentId && record.parentId != undefined && record.parentId != null) {
          this.parentId = record.parentId
          this.isCmp = '0'
        }
        this.form.resetFields()
        this.model = Object.assign({}, record)
        if (this.model.paramModel && this.model.paramModel != null) {
          this.childrenDataList = JSON.parse(this.model.paramModel)
        } else {
          this.childrenDataList = []
        }
        this.visible = true
        this.loadTree()
        let fieldsVal = pick(
          this.model,
          'name',
          'isCmp',
          'code',
          'cmpSerial',
          'cmpIcon',
          'protocol',
          'cmpGroup',
          'cmpDesc'
        )
        this.$nextTick(() => {
          this.form.setFieldsValue(fieldsVal)
        })
      },
      close() {
        this.$emit('close')
        this.disableSubmit = false
        this.visible = false
        this.parentId = undefined
      },
      handleOk() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            let formData = Object.assign(this.model, values)
            if (this.group == 1) {
              formData.protocol = 'COMMON'
            }
            formData.parentId = this.parentId
            if (this.childrenDataList.length > 0) {
              formData.paramModel = JSON.stringify(this.childrenDataList)
            }
            that.confirmLoading = true
            let obj
            if (!this.model.id) {
              obj = addModel(formData)
            } else {
              obj = editModel(formData)
            }
            obj
              .then((res) => {
                if (res.success) {
                  that.$message.success(res.message)
                  this.localMenuType = 0
                  that.$emit('ok')
                } else {
                  that.$message.warning(res.message)
                }
              })
              .finally(() => {
                that.confirmLoading = false
                that.close()
              })
          }
        })
      },
      handleCancel() {
        this.localMenuType = 0
        this.close()
      },
      // 添加参数按钮 打开新的子级页面，本质上还是当前vue组件
      addChildren() {
        const that = this
        // 此add 先去ProertyMetadataModal组件中的add方法， 然后ProertyMetadataModal组件再调用本组件的add方法
        that.$refs.modalForm.add()
        that.$refs.modalForm.title = '新增'
        that.$refs.modalForm.disableSubmit = false
      },
      modalFormOk(info) {
        // 新增/修改 成功时，重载列表
        let data = JSON.parse(info)
        this.childrenDataList.push(data)
      },
      childrenChange(data) {
        this.childrenDataList = data
      },
    },
  }
</script>

<style lang='less' scoped>
  ::v-deep .two-words>div>label {
    letter-spacing: 4px;
  }

  ::v-deep .two-words>div>label::after {
    letter-spacing: 0px;
  }

  @media (max-width: 700px) {
    ::v-deep .ant-drawer-content-wrapper {
      max-width: 100vw;
      margin: 0;
    }
  }
</style>