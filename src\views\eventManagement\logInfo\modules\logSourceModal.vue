<template>
  <j-modal
    :title='title'
    :width='width'
    :centered='true'
    :visible='visible'
    :destroyOnClose='true'
    switchFullscreen
    cancelText='关闭'
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @ok='handleOk'
    @cancel='handleCancel'
  >
    <a-spin :spinning='confirmLoading'>
      <j-form-container>
        <a-form-model ref='form' :model='model' :rules='validatorRules' slot='detail'>
          <a-row justify='space-between' type='flex'>
            <a-col :span='24'>
              <a-form-model-item label='名称' prop='dataViewName' v-bind='formItemLayout'>
                <a-input
                  v-model='model.dataViewName'
                  :allow-clear='true'
                  autocomplete='off'
                  placeholder='请输入名称'/>
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
              <a-form-model-item label='索引模式' prop='indexPatterns' v-bind='formItemLayout'>
<!--                <a-input
                  v-model='model.indexPatterns'
                  :allow-clear='true'
                  autocomplete='off'
                  placeholder='请选择配置方案'/>-->

                <a-select
                  v-model='model.indexPatterns'
                  :allow-clear='true'
                  mode="multiple" 
                  autocomplete='off'
                  placeholder='请选择配置方案'
                  @change='changeIndexPatterns'
                >
                  <a-select-option v-for='item in indexPatternsList' :key='"indexPatterns_"+item' :label='item' :value='item'>
                    {{ item }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
              <a-form-model-item label='时间戳字段' prop='timestampFiled' v-bind='formItemLayout'>
<!--                <a-input
                  v-model='model.timestampFiled'
                  :allow-clear='true'
                  autocomplete='off'
                  placeholder='请输入时间戳字段'/>-->
                <a-select
                  v-model='model.timestampFiled'
                  :allow-clear='true'
                  autocomplete='off'
                  placeholder='请选择时间戳字段'>
                  <a-select-option v-for='item in timestampFiledList' :key='"timestampFiled_"+item' :label='item' :value='item'>
                    {{ item }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
              <a-form-model-item label="日志分类" prop='logCategory' v-bind='formItemLayout'>
                <a-tree-select
                  v-model='model.logCategory'
                  :tree-data="treeData"
                  :multiple="true"
                  :replaceFields="replaceFields"
                  :dropdownMatchSelectWidth="true"
                  :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                  allow-clear
                  placeholder="请选择日志分类"
                />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </j-form-container>
    </a-spin>
  </j-modal>
</template>
<script>
import { getAction, httpAction } from '@api/manage'
import { ajaxGetDictItems, getDictItemsFromCache } from '@api/api'

export default {
  name: 'dataViewModal',
  data() {
    return {
      title: '说明',
      width: '800px',
      disableSubmit: false,
      visible: false,
      confirmLoading: false,
      formItemLayout: {
        labelCol: {
          xs: {span: 24},
          sm: {span: 5}
        },
        wrapperCol: {
          xs: {span: 24},
          sm: {span: 16}
        }
      },
      model: {},
      indexPatternsList:[],
      timestampFiledList:[],
      validatorRules: {
        dataViewName: [
          { required: true,message:'请输入名称' },
          { min:1,max:20,message:'名称长度应在【1-20】个字符之间' }
        ],
        indexPatterns: [
          { required: true,message:'请选择索引模式'}
        ],
        timestampFiled: [
          { required: true,message:'请选择时间戳字段' }
        ],
        logCategory: [
          { required: false,message:'请选择日志分类' }
        ]
      },
      url:{
        add: '/logAnalyze/add',//新增提交日志来源
        edit:'/logAnalyze/edit',//编辑提交日志来源
        totalIndex:'/logAnalyze/getIndex',//获取所有索引下拉数据
        dateFields: "/logAnalyze/getDateFields",//获取时间戳字段
        getLogCategoryTree: '/log/logCategory/getLogCategoryTree', //获取分类树
      },
      replaceFields: {
        children: 'children',
        title: 'name',
        key: 'code',
        value: 'code',
      },
      treeData: [],
    }
  },
created() {
  this.getIndexPatterns()
  this.loadTreeData()
},
  methods: {
    add(){
      this.edit({
        indexPatterns: [],
        logCategory: []
      })
    },
    edit(record){
      this.visible = true
      if (record.id) {
        // 处理多选索引模式
        this.getTimestampFields(record.indexPatterns)
      }
      this.$nextTick(()=>{
        this.model = JSON.parse(JSON.stringify(record))
        // 处理字符串转数组
        if (this.model.indexPatterns && typeof this.model.indexPatterns === 'string') {
          this.model.indexPatterns = this.model.indexPatterns.split(',')
        } else {
          this.model.indexPatterns = []
        }
        if (this.model.logCategory && typeof this.model.logCategory === 'string') {
          this.model.logCategory = this.model.logCategory.split(',')
        } else { 
          this.model.logCategory = []
        }
      })      
    },
    close() {
      this.visible = false
    },
    handleOk() {
      let that=this
      that.$refs.form.validate((err, values) => {
        if (err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!that.model.id) {
            httpurl += that.url.add
            method = 'post'
          } else {
            httpurl += that.url.edit
            method = 'put'
          }
          let formData = Object.assign(that.model, values)
          if (formData.logCategory && Array.isArray(formData.logCategory)) {
            formData.logCategory = formData.logCategory.join(',')
          }
          if (formData.indexPatterns && Array.isArray(formData.indexPatterns)) {
            formData.indexPatterns = formData.indexPatterns.join(',')
          }
          console.log('formData==', formData)
          
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                let id=this.model.id?this.model.id:''
                that.$emit('ok',id)
                this.close()
              } else {
                that.$message.warning(res.message)
              }
              this.confirmLoading = false
            }).catch((err) => {
            this.confirmLoading = false
            this.$message.error(err.message)
            this.close()
          })
        }
      })
    },
    handleCancel() {
      this.close()
    },
    /*获取所有索引*/
    getIndexPatterns(){
      this.indexPatternsList=[]
      getAction(this.url.totalIndex).then((res)=>{
        if (res.success){
          this.indexPatternsList=res.result
          //console.log('所有索引this.indexPatternsList===',this.indexPatternsList)
        }else {
          this.$message.warning(res.message)
        }
      }).catch((err)=>{
        this.$message.warning(err.message)
      })
    },
    /*根据索引，获取时间戳下拉数据*/
    getTimestampFields(indexName){
      // 将数组转换为逗号分隔字符串
      const indices = Array.isArray(indexName) ? indexName.join(',') : indexName
      getAction(this.url.dateFields,{indexName:indices}).then((res)=>{
        if (res.success){
          // 合并去重所有索引的时间字段
          this.timestampFiledList = [...new Set([...this.timestampFiledList, ...res.result])]
        }else {
          this.$message.warning(res.message)
        }
      }).catch((err)=>{
        this.$message.warning(err.message)
      })
    },
    /*改变索引，获取对应的时间戳下拉数据*/
    changeIndexPatterns(value){
      this.timestampFiledList=[]
      delete this.model['timestampFiled']
      if (value && Array.isArray(value) && value.length > 0) {
        // 传递逗号分隔的字符串
        this.getTimestampFields(value.join(','))
      }
    },
    // 加载树数据
    loadTreeData() {
      getAction(this.url.getLogCategoryTree).then(res => {
        if (res && res.result && Array.isArray(res.result)) {
          this.treeData = this.formatTreeData(res.result)
        }
      })
    },
    // 格式化树数据
    formatTreeData(data) {
      return data.map(item => {
        const node = {
          ...item,
          isLeaf: item.hasChild === '0',
          scopedSlots: { title: 'title' }
        }
        
        // 如果存在子节点，递归格式化
        if (item.children && item.children.length > 0) {
          node.children = this.formatTreeData(item.children)
        }
        
        return node
      })
    },
  }
}
</script>
<style scoped lang='less'>
@import '~@assets/less/normalModal.less';
</style>