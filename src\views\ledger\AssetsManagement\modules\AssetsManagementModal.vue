<template>
  <j-modal
    :title='title'
    :visible='visible'
    :centered='true'
    switchFullscreen
    :confirmLoading='confirmLoading'
    :destroyOnClose='true'
    :width='modalWidth'
    :closable='true'
    @cancel='handleCancel'
    @ok='handleOk'
    okText=''
    cancelText='关闭'
  >
      <a-form :form='form'>
        <a-row :gutter='24'>
          <a-col v-bind='formItemLayout'>
            <a-form-item :labelCol='labelCol' :wrapperCol='wrapperCol' label='资产名称'>
              <a-input
                :allowClear='true'
                autocomplete='off'
                placeholder='请输入资产名称'
                v-decorator="['assetName', formValidator.assetName]" />
            </a-form-item>
          </a-col>
          <a-col v-bind='formItemLayout'>
            <a-form-item :labelCol='labelCol' :wrapperCol='wrapperCol' label='质保开始时间'>
              <j-date
                placeholder='请选择质保开始时间'
                style='width: 100%'
                :trigger-change='true'
                :showTime='true'
                dateFormat='YYYY-MM-DD HH:mm:ss'
                v-decorator="['warrantyStart', formValidator.warrantyStart]"
              ></j-date>
            </a-form-item>
          </a-col>
          <a-col v-bind='formItemLayout'>
            <a-form-item :labelCol='labelCol' :wrapperCol='wrapperCol' label='资产类型'>
              <j-dict-select-tag
                v-decorator="['assetType', formValidator.assetType]"
                :trigger-change='true'
                placeholder='请选择资产类型'
                title='资产类型'
                dictCode='asset_type'
              />
            </a-form-item>
          </a-col>
          <a-col v-bind='formItemLayout'>
            <a-form-item :labelCol='labelCol' :wrapperCol='wrapperCol' label='到保日期'>
              <j-date
                placeholder='请选择到保日期'
                style='width: 100%'
                :trigger-change='true'
                :showTime='true'
                dateFormat='YYYY-MM-DD HH:mm:ss'
                v-decorator="['warrantyEnd', formValidator.warrantyEnd]"
              ></j-date>
            </a-form-item>
          </a-col>
          <a-col v-bind='formItemLayout'>
            <a-form-item :labelCol='labelCol' :wrapperCol='wrapperCol' label='资产编号'>
              <a-input :allowClear='true' autocomplete='off' placeholder='请输入资产编号'
                       v-decorator="['assetNo', formValidator.assetNo]" />
            </a-form-item>
          </a-col>
          <a-col v-bind='formItemLayout'>
            <a-form-item :labelCol='labelCol' :wrapperCol='wrapperCol' label='保修单位'>
              <a-input :allowClear='true' autocomplete='off' placeholder='保修单位'
                       v-decorator="['warrantyUnit', formValidator.warrantyUnit]" />
            </a-form-item>
          </a-col>
          <a-col v-bind='formItemLayout'>
            <a-form-item :labelCol='labelCol' :wrapperCol='wrapperCol' label='IP地址'>
              <a-input :allowClear='true' autocomplete='off' placeholder='请输入IP地址'
                       v-decorator="['ipAddress', formValidator.ipAddress]" />
            </a-form-item>
          </a-col>
          <a-col v-bind='formItemLayout'>
            <a-form-item :labelCol='labelCol' :wrapperCol='wrapperCol' label='保修联系人'>
              <a-input :allowClear='true' autocomplete='off' placeholder='请输入保修联系人'
                       v-decorator="['warranties', formValidator.warranties]" />
            </a-form-item>
          </a-col>
          <a-col v-bind='formItemLayout'>
            <a-form-item :labelCol='labelCol' :wrapperCol='wrapperCol' label='资产状态'>
              <j-dict-select-tag
                v-decorator="['assetStatus', formValidator.assetStatus]"
                :trigger-change='true'
                placeholder='请选择资产状态'
                dictCode='asset_queryStatus'
              />
            </a-form-item>
          </a-col>
          <a-col v-bind='formItemLayout'>
            <a-form-item :labelCol='labelCol' :wrapperCol='wrapperCol' label='保修人电话'>
              <a-input :allowClear='true' autocomplete='off' placeholder='请输入保修人电话'
                       v-decorator="['warrantyPhone', formValidator.warrantyPhone]" />
            </a-form-item>
          </a-col>
          <a-col v-bind='formItemLayout'>
            <a-form-item :labelCol='labelCol' :wrapperCol='wrapperCol' label='添加方式'>
              <a-select
                :getPopupContainer='(node) => node.parentNode'
                :allowClear='true'
                placeholder='请选择添加方式'
                v-decorator="['addType', formValidator.addType]"
              >
                <a-select-option value='1'>新增</a-select-option>
                <a-select-option value='2'>原有</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col v-bind='formItemLayout'>
            <a-form-item :labelCol='labelCol' :wrapperCol='wrapperCol' label='生产厂商'>
              <a-input :allowClear='true' autocomplete='off' placeholder='请输入生产厂商'
                       v-decorator="['producer', formValidator.producer]" />
            </a-form-item>
          </a-col>
          <a-col v-bind='formItemLayout'>
            <a-form-item :labelCol='labelCol' :wrapperCol='wrapperCol' label='归属地'>
              <yq-area-cascader-select
                placeholder='请选择归属地'
                v-decorator="['region', formValidator.region]"
              ></yq-area-cascader-select>
            </a-form-item>
          </a-col>
          <a-col v-bind='formItemLayout'>
            <a-form-item class='two-words formItem' :labelCol='labelCol' :wrapperCol='wrapperCol' label='型号'>
              <a-input :allowClear='true' autocomplete='off' placeholder='请输入型号'
                       v-decorator="['model', formValidator.model]" />
            </a-form-item>
          </a-col>
          <a-col v-bind='formItemLayout'>
            <a-form-item :labelCol='labelCol' :wrapperCol='wrapperCol' label='使用单位'>
              <a-input :allowClear='true' autocomplete='off' placeholder='请输入使用单位'
                       v-decorator="['userUnit', formValidator.userUnit]" />
            </a-form-item>
          </a-col>
          <a-col v-bind='formItemLayout'>
            <a-form-item :labelCol='labelCol' :wrapperCol='wrapperCol' label='使用人'>
              <a-input :allowClear='true' autocomplete='off' placeholder='请输入使用人'
                       v-decorator="['userid', formValidator.userid]" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
  </j-modal>
</template>
<script>
import pick from 'lodash.pick'
import JDate from '@/components/jeecg/JDate'
import { addRecord, editRecord } from '@api/AssetsManagement'
import JDictSelectTag from '@/components/dict/JDictSelectTag.vue'
import YqAreaCascaderSelect from '@/components/areaDict/YqAreaCascaderSelect'

import { ajaxGetDictItems } from '@api/api'

export default {
  name: 'AssetsManagementModal',
  components: {
    JDate,
    JDictSelectTag,
    YqAreaCascaderSelect
  },
  // props: ['queryStatus', 'assetType'],
  data() {
    return {
      title: '操作',
      visible: false,
      confirmLoading: false,
      /* 弹框宽 */
      modalWidth: '1000px',
      form: this.$form.createForm(this),
      formItemLayout: {
        md: { span: 12 },
        sm: { span: 24 }
      },
      labelCol: {
        //style: 'width:105px',
        xs: { span: 24 },
        sm: { span: 5 },
        md:{span: 8},
        lg:{span: 6},
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 18 },
        md:{span: 16},
        lg:{span: 16},
      },
      showHeader: false,
      columns: [
        { title: 'Name', dataIndex: 'name', key: 'name', width: 100 },
        { title: 'actionPeople', dataIndex: 'actionPeople', key: 'actionPeople', width: 150 },
        { title: 'status', dataIndex: 'status', key: 'status' },
        { title: 'time', dataIndex: 'time', key: 'time' }
      ],
      assetStatus: '',
      formValidator: {
        assetName: {
          rules: [
            {
              required: true,
              message: '必填!'
            },
            {
              max: 60,
              message: '最大长度 60 个字符',
              trigger: 'blur'
            }
          ]
        },
        assetType: {
          rules: [
            {
              required: true,
              message: '必选!'
            },
            {
              max: 60,
              message: '最大长度 60 个字符',
              trigger: 'blur'
            }
          ]
        },
        assetNo: {
          rules: [
            {
              required: true,
              message: '必填!'
            },
            {
              max: 50,
              message: '最大长度50个字符',
              trigger: 'blur'
            }
          ]
        },
        warrantyUnit: {
          rules: [
            {
              required: true,
              message: '必填!'
            },
            {
              max: 50,
              message: '最大长度 50 个字符',
              trigger: 'blur'
            }
          ]
        },
        ipAddress: {
          rules: [
            {
              required: true,
              message: '必填'
            },
            {

              pattern:
                /^(\d|[1-9]\d|1\d{2}|2[0-5][0-5])\.(\d|[1-9]\d|1\d{2}|2[0-5][0-5])\.(\d|[1-9]\d|1\d{2}|2[0-5][0-5])\.(\d|[1-9]\d|1\d{2}|2[0-5][0-5])$/,
              message: '请输入正确的IP地址!'
            }
          ]
        },
        warranties: {
          rules: [
            {
              required: true,
              message: '必填!'
            },
            {
              max: 50,
              message: '最大长度50个字符',
              trigger: 'blur'
            }
          ]
        },
        warrantyPhone: {
          rules: [
            {
              required: true,
              message: '必填!'
            },
            {
              pattern: /(^(\d{3,4}-)?\d{7,8})$|(1[3-9]\d{9})$/,
              message: '请输入正确的联系电话',
              trigger: 'blur'
            }
          ]
        },
        assetStatus: {
          rules: [
            {
              required: true,
              message: '必选!'
            }
          ]
        },
        addType: {
          rules: [
            {
              required: true,
              message: '必选!'
            }
          ]
        },
        producer: {
          rules: [
            {
              required: true,
              message: '必填!'
            },
            {
              max: 60,
              message: '最大长度 60 个字符',
              trigger: 'blur'
            }
          ]
        },
        region: {
          rules: [
            {
              required: true,
              message: '必选!'
            }
          ]
        },
        model: {
          rules: [
            {
              required: true,
              message: '必填!'
            },
            {
              max: 30,
              message: '最大长度 30 个字符',
              trigger: 'blur'
            }
          ]
        },
        userUnit: {
          rules: [
            {
              required: true,
              message: '必填!'
            },
            {
              max: 60,
              message: '最大长度 60 个字符',
              trigger: 'blur'
            }
          ]
        },
        userid: {
          rules: [
            {
              required: false
            },
            {
              max: 50,
              message: '最大长度50个字符',
              trigger: 'blur'
            }
          ]
        }
      },
      //资产状态
      queryStatus: []
    }
  },
  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(
          pick(
            this.model,
            'addType',
            'assetName',
            'assetNo',
            'assetStatus',
            'assetType',
            'ipAddress',
            'model',
            'producer',
            'region',
            'userid',
            'userUnit',
            'warranties',
            'warrantyPhone',
            'warrantyStart',
            'warrantyUnit',
            'warrantyEnd'
          )
        )
      })
      // this.getQueryStatus();
    },
    getQueryStatus(code) {
      let that = this
      ajaxGetDictItems('asset_queryStatus', null).then((res) => {
        if (res.success) {
          that.queryStatus = res.result
        } else {
          that.$message.error('资产状态字典信息获取失败')
        }
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let formData = Object.assign(this.model, values)
          let obj
          if (!this.model.id) {
            obj = addRecord(formData)
          } else {
            obj = editRecord(formData)
          }
          obj
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
              that.close()
            })
        }
      })
    },
    handleCancel() {
      this.close()
    },
    callback(key) {
    },
    //单个日期框
    onChange(date, dateString) {
    }
  }
}
</script>
<style scoped lang='less'>
@import '~@assets/less/normalModal.less';
.ant-calendar-picker {
  width: 100%;
}
</style>
