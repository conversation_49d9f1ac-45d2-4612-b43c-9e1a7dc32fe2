<template>
  <div>
    <div>
      <div class="headerBox">
        <div class="colorBox">
          <span class="colorTotal">绑定设备</span>
        </div>
      </div>
      <a-table
        ref="table"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        @change="handleTableChange"
      >
        <template slot="tooltip" slot-scope="text">
          <a-tooltip placement="topLeft" :title="text" trigger="hover">
            <div class="tooltip">{{ text }}</div>
          </a-tooltip>
        </template>
      </a-table>
    </div>
  </div>
</template>
<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { getAction } from '@/api/manage'
export default {
  name: 'bandDeviceModal',
  mixins: [JeecgListMixin],
  data() {
    return {
      record: {},
      url: {
        getDevice: '/device/deviceGroup/getDevice',
        getAllDevice: '/device/deviceGroup/getResourceList'
      },
      dataSource: [],
      disableMixinCreated:true,
      columns: [
        {
          title: '设备名称',
          dataIndex: 'name',
          customHeaderCell: () => ({ style: { textAlign: 'center' } }), //头部单元格水平居中
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 150px;max-width:300px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '产品名称',
          dataIndex: 'productName',
          customHeaderCell: () => ({ style: { textAlign: 'center' } }),
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 150px;max-width:300px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: 'IP',
          dataIndex: 'ip',
          customHeaderCell: () => ({ style: { textAlign: 'center' } }),
          customCell: () => {
            let cellStyle = 'text-align: center;width:160px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '绑定时间',
          dataIndex: 'createTime',
          customHeaderCell: () => ({ style: { textAlign: 'center' } }),
          customCell: () => {
            let cellStyle = 'text-align: center;width:160px'
            return {
              style: cellStyle
            }
          }
        }
      ]
    }
  },
  props: {
    data: {
      type: Object
    }
  },
  methods: {
    // 获取已绑定设备
    loadData() {
      if (!this.data.id) {
        return false
      }
      let httpUrl = ''
      if (this.data.isMaxPrivilege == 1) {
        httpUrl = this.url.getAllDevice
      } else {
        httpUrl = this.url.getDevice
      }
      getAction(httpUrl, {
        groupId: this.data.id,
        pageSize: this.ipagination.pageSize,
        pageNo: this.ipagination.current
      }).then(res => {
        if (res.success && res.result) {
          this.dataSource = res.result.records || res.result
          this.ipagination.total = res.result.total ? res.result.total : 0
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.headerBox {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.colorBox {
  font-size: 14px;
  margin-bottom: 10px;

  .colorTotal {
    padding-left: 7px;
    border-left: 4px solid #1e3674;
  }
}
</style>