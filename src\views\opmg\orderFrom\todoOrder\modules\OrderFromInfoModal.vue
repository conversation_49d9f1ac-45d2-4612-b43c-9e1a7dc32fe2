<template>
  <j-modal
    :title="title"
    :width="width"
    :centered="true"
    :visible="visible"
    switchFullscreen
    @ok="handleOk"
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @cancel="handleCancel"
    cancelText="关闭">
    <order-from-info-form ref="realForm" @ok="submitCallback" :disabled="disableSubmit"></order-from-info-form>
  </j-modal>
</template>

<script>

  import OrderFromInfoForm from './OrderFromInfoForm'
  export default {
    name: 'OrderFromInfoModal',
    components: {
      OrderFromInfoForm
    },
    data () {
      return {
        title:'',
        width:800,
        visible: false,
        disableSubmit: false
      }
    },
    methods: {
      add () {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.add();
        })
      },
      edit (record) {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.edit(record);
        })
      },
      close () {
        this.$emit('close');
        this.visible = false;
      },
      handleOk () {
        this.$refs.realForm.submitForm();
      },
      submitCallback(){
        this.$emit('ok');
        this.visible = false;
      },
      handleCancel () {
        this.close()
      }
    }
  }
</script>
<style scoped lang='less'>
@import '~@assets/less/normalModal.less';
</style>