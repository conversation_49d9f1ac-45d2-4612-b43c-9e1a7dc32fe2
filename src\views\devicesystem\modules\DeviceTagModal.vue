<template>
  <j-modal :title="title" :width="width" :visible="visible" switchFullscreen :destroyOnClose="true" :centered='true'
    @ok="handleOk" @cancel="handleCancel" cancelText="关闭">
    <a-spin :spinning="confirmLoading">
      <a-transfer style='display: flex;flex-flow: row wrap' :data-source='tagData' :targetKeys='targetKeys'
        :show-search='showSearch'
        :list-style="{flex:1,minHeight: '570px', width: '600px',marginTop:'10px',marginBottom:'10px' }"
        :titles="['未绑定标签', '已绑定标签']" :operations="['绑定', '解绑']" :filter-option='filterOption' :show-select-all='false'
        @change='tagChange'>
        <template slot='children'
          slot-scope='{ props: { direction, filteredItems, selectedKeys }, on: { itemSelectAll, itemSelect } }'>
          <a-table size='small' :data-source='filteredItems'
            :row-selection='getRowSelection({ selectedKeys, itemSelectAll, itemSelect })'
            :columns="direction === 'left' ? leftColumns : rightColumns"
            :pagination="direction === 'left' ? leftIpagination : rightIpagination"
            @change='handleTableChange(direction,$event)' />
        </template>
      </a-transfer>
      <a-switch un-checked-children='打开搜索' checked-children='关闭搜索' :checked='showSearch' style='margin-top: 16px'
        @change='triggerShowSearch' />
    </a-spin>
  </j-modal>
</template>

<script>
  import {
    getAction,
    postAction
  } from '@api/manage'
  import difference from 'lodash/difference'
  export default {
    name: 'DeviceTagModal',
    data() {
      return {
        title: '标签绑定',
        width: '1300px',
        visible: false,
        confirmLoading: false,
        tagData: [],
        targetKeys: [],
        showSearch: true,
        leftIpagination: {},
        rightIpagination: {},
        ipagination: {
          current: 1,
          pageSize: 10,
          pageSizeOptions: ['10', '20', '30'],
          showTotal: (total, range) => {
            return ' 共' + total + '条'
          },
          showQuickJumper: true,
          showSizeChanger: true,
          total: 0
        },
        leftColumns: [{
            dataIndex: 'title',
            title: '标签',
            customCell: () => {
              let cellStyle = 'text-align: left;min-width: 50px;max-width:100px'
              return {
                style: cellStyle
              }
            }
          },
          {
            dataIndex: 'description',
            title: '描述',
            customCell: () => {
              let cellStyle = 'text-align: left;min-width: 120px;max-width:150px'
              return {
                style: cellStyle
              }
            }
          }
        ],
        rightColumns: [{
            dataIndex: 'title',
            title: '标签',
            customCell: () => {
              let cellStyle = 'text-align: left;min-width: 50px;max-width:100px'
              return {
                style: cellStyle
              }
            }
          },
          {
            dataIndex: 'description',
            title: '描述',
            customCell: () => {
              let cellStyle = 'text-align: left;min-width: 120px;max-width:150px'
              return {
                style: cellStyle
              }
            }
          }
        ],
        deviceId: '',
        url: {
          unAttachTags: '/utl/tagresource/unAttachTags', //未绑定标签
          alreadyAttachTags: '/utl/tagresource/alreadyAttachTags', //已绑定标签
          attachTags: '/utl/tagresource/attachTags', //绑定标签
          detachTags: '/utl/tagresource/detachTags', //解绑标签
        },
      }
    },
    mounted() {
      Object.assign(this.leftIpagination, this.ipagination)
      Object.assign(this.rightIpagination, this.ipagination)
    },
    methods: {
      filterOption(inputValue, option) {
        return option.title.indexOf(inputValue) > -1
      },
      handleTableChange(direction, pagination) {
        direction === 'left' ? this.leftIpagination = pagination : this.rightIpagination = pagination
      },
      bindaTag(record) {
        let that = this
        that.$nextTick(() => {
          Object.assign(that.leftIpagination, that.ipagination)
          Object.assign(that.rightIpagination, that.ipagination)

          that.deviceId = record.id
          that.confirmLoading = true
          that.tagData = []
          that.targetKeys = []
          Promise.all([that.getDataSource(record.id), that.getTargetKeys(record.id)]).then((res) => {
            that.tagData = res[0].data
            that.targetKeys = res[1].data
            that.confirmLoading = false
          }).catch((err) => {
            that.confirmLoading = false
            that.$message.warning('发生异常，请稍后重试')
          })
        })
      },
      getDataSource(deviceId) {
        let that = this
        return new Promise((resolve, reject) => {
          getAction(that.url.unAttachTags, {
            resourceId: deviceId,
            resourceType: 'momg_device_info'
          }).then((res) => {
            if (res.success) {
              let data = []
              if (res.result.length > 0) {
                let tagDatas = res.result.map((item) => {
                  return {
                    key: item.tagKey.toString(),
                    title: item.tagName.toString(),
                    description: item.description != null ? item.description.toString() : ''
                  }
                })
                data = tagDatas
              }
              resolve({
                success: true,
                message: res.message,
                data: data
              })
            } else {
              reject({
                success: false,
                message: res.message,
              })
            }
          })
        })
      },
      getTargetKeys(deviceId) {
        let that = this
        return new Promise((resolve, reject) => {
          getAction(that.url.alreadyAttachTags, {
            resourceId: deviceId,
            resourceType: 'momg_device_info'
          }).then((res) => {
            if (res.success) {
              let data = []
              if (res.result.length > 0) {
                data = res.result.map((item) => item.tagKey)
              }
              resolve({
                success: true,
                message: res.message,
                data: data
              })
            } else {
              reject({
                success: false,
                message: res.message,
              })
            }
          })
        })
      },
      getRowSelection({
        selectedKeys,
        itemSelectAll,
        itemSelect
      }) {
        return {
          onSelectAll(selected, selectedRows) {
            const treeSelectedKeys = selectedRows.filter((item) => !item.disabled).map(({
              key
            }) => key)
            const diffKeys = selected ?
              difference(treeSelectedKeys, selectedKeys) :
              difference(selectedKeys, treeSelectedKeys)
            itemSelectAll(diffKeys, selected)
          },
          onSelect({
            key
          }, selected) {
            itemSelect(key, selected)
          },
          selectedRowKeys: selectedKeys
        }
      },
      tagChange(nextTargetKeys) {
        this.targetKeys = nextTargetKeys
      },
      triggerShowSearch(showSearch) {
        this.showSearch = showSearch
      },
      handleOk() {
        let that = this
        that.confirmLoading = true
        postAction(that.url.attachTags, {
          resourceType: 'momg_device_info',
          resourceId: that.deviceId,
          tagKeys: that.targetKeys.length > 0 ? that.targetKeys : []
        }).then((res) => {
          if (res.success) {
            that.$message.success('保存成功')
            that.visible = false
            that.$emit('tagOk')
          } else {
            that.$message.warning(res.message)
          }
          that.confirmLoading = false
        }).catch((err) => {
          that.$message.warning(err.message)
          that.confirmLoading = false
        })
      },
      handleCancel() {
        this.close()
      },
      close() {
        this.$emit('close');
        this.visible = false;
      },
    }
  }
</script>
<style lang="less" scoped>
  @import '~@assets/less/normalModal.less';
</style>