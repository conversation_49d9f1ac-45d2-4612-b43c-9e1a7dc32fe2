<template>
  <j-modal
    :title="title"
    :width="modalWidth"
    :visible="visible"
    :centered='true'
    switchFullscreen
    :destroyOnClose='true'
    @ok="handleOk"
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @cancel="handleCancel"
    cancelText="关闭"
  >
    <product-form ref="realForm" @ok="submitCallback" :disabled="disableSubmit"></product-form>
  </j-modal>
</template>

<script>

  import ProductForm from './ProductForm'
  export default {
    name: 'ProductModal',
    components: {
      ProductForm
    },
    data () {
      return {
        title:'',
        modalWidth:'800px',
        visible: false,
        disableSubmit: false
      }
    },
    methods: {
      add () {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.add();
        })
      },
      edit (record) {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.edit(record);
        })
      },
      close () {
        this.$emit('close');
        this.visible = false;
      },
      handleOk () {
        this.$refs.realForm.submitForm();
      },
      submitCallback(){
        this.$emit('ok');
        this.visible = false;
      },
      handleCancel () {
        this.close()
      }
    }
  }
</script>
<style scoped lang='less'>
@import '~@assets/less/normalModal.less';
</style>