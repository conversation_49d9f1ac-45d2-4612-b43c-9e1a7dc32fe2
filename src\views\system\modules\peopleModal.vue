<template>
  <j-modal :title="title" :width="modalWidth" :visible="visible" :centered="true" switchFullscreen
    :destroyOnClose="true" @ok="handleOk" @cancel="handleCancel" cancelText="关闭">
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="用户账号">
          <a-input placeholder="请输入用户账号" v-decorator="['username', validatorRules.username]" :allowClear="true"
            autocomplete="off" :readOnly='!!model.id' />
        </a-form-item>
        <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="用户姓名">
          <a-input placeholder="请输入用户姓名" v-decorator="['realname', validatorRules.realname]" :allowClear="true"
            autocomplete="off" />
        </a-form-item>
        <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="工号">
          <a-input placeholder="请输入工号" v-decorator="['workNo', validatorRules.workNo]" :allowClear="true"
            autocomplete="off" />
        </a-form-item>
        <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="服务优先级">
          <a-input-number v-decorator="['priority', validatorRules.priority]" :min="1" placeholder='请输入服务优先级'>
          </a-input-number>
        </a-form-item>
        <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="头像">
          <j-image-upload bizPath='image/usesrIcon' class='avatar-uploader' text='上传' v-model='fileList'>
          </j-image-upload>
        </a-form-item>
        <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="邮箱">
          <a-input placeholder="请输入邮箱" v-decorator="['email', validatorRules.email]" :allowClear="true"
            autocomplete="off" />
        </a-form-item>
        <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="手机号码">
          <a-input placeholder="请输入手机号码" v-decorator="['phone', validatorRules.phone]" :allowClear="true"
            autocomplete="off" />
        </a-form-item>
        <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="座机">
          <a-input placeholder="请选择座机" v-decorator="['telephone', validatorRules.telephone]" :allowClear="true"
            autocomplete="off" />
        </a-form-item>
      </a-form>
    </a-spin>
  </j-modal>
</template>

<script>
  import pick from 'lodash.pick'
  import JImageUpload from '../../../components/jeecg/JImageUpload'
  import {
    ajaxGetDictItems,
    duplicateCheck
  } from '@/api/api'
  import {
    httpAction,
    getAction
  } from '@/api/manage'
  import JSelectUserByDepNoRight from '@/components/flowable/JSelectUserByDepNoRight'
  import {
    phoneValidator
  } from '@/mixins/phoneValidator'

  export default {
    name: 'serviceModal',
    mixins: [phoneValidator],
    components: {
      JSelectUserByDepNoRight,
      JImageUpload,
    },
    data() {
      return {
        title: '',
        modalWidth: '800px',
        visible: false,
        userId: '',
        model: {},
        fileList: [],
        labelCol: {
          xs: {
            span: 24,
          },
          sm: {
            span: 5,
          },
        },
        wrapperCol: {
          xs: {
            span: 24,
          },
          sm: {
            span: 16,
          },
        },
        wrapperCol1: {
          xs: {
            span: 24,
          },
          sm: {
            span: 2,
          },
        },
        confirmLoading: false,
        form: this.$form.createForm(this),
        validatorRules: {
          username: {
            rules: [{
                required: true,
                message: '请输入用户账号'
              },
              {
                validator: this.validateUsername
              }
            ],
          },
          realname: {
            rules: [{
              required: true,
              message: '请输入用户姓名'
            }],
          },
          workNo: {
            rules: [{
                required: true,
                message: '请输入工号'
              },
              {
                pattern: /^[a-zA-Z0-9_-]{1,50}$/,
                message: '工号支持英文、数字、下划线、短划线'
              }, {
                validator: this.validateWorkNo
              }
            ]
          },
          email: {
            rules: [{
              required: false,
              pattern: /^([a-z0-9_\.-]+)@([\da-z\.-]+)\.([a-z\.]{2,6})$/,
              message: '请输入正确的邮箱地址',
            }, ],
          },
          phone: {
            rules: [{
              required: true,
              message: '请输入手机号码'
            }, {
              validator: this.validatePhone
            }]
          },
          providerDesc: {
            rules: [{
                required: false
              },
              {
                max: 200,
                message: '描述长度在200个字符内'
              }
            ],
          },
          priority: {
            rules: [{
              required: true,
              message: '请输入优先级'
            }]
          },
        },
        url: {
          add: '/serviceProvider/user/addEngineer',
          edit: '/serviceProvider/user/editEngineer',
          userDepartList: '/sys/user/userDepartList', // 引入为指定用户查看部门信息需要的url
        },
      }
    },
    created() {},
    methods: {
      //添加
      add() {
        this.edit({})
      },

      //编辑
      edit(record) {
        this.userId = record.id
        this.form.resetFields()
        setTimeout(() => {
          this.fileList = record.avatar
        }, 5)
        this.model = Object.assign({}, record)
        this.visible = true
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.model, 'realname', 'priority', 'username', 'workNo', 'phone',
            'email', 'telephone', ''))
        })
      },

      validateWorkNo(rule, value, callback) {
        var params = {
          tableName: 'sys_users',
          fieldName: 'work_no',
          fieldVal: value,
          dataId: this.userId
        }
        getAction('/sys/duplicate/checkForUser', params).then((res) => {
          if (res.success) {
            callback()
          } else {
            callback('工号已存在!')
          }
        })
      },
      validateUsername(rule, value, callback) {
        var params = {
          tableName: 'sys_users',
          fieldName: 'username',
          fieldVal: value,
          dataId: this.userId
        }
        duplicateCheck(params).then((res) => {
          if (res.success) {
            callback()
          } else {
            callback('用户账号已存在!')
          }
        })
      },
      //关闭
      close() {
        this.$emit('close')
        this.fileList = []
        this.visible = false
      },
      //确认
      handleOk() {
        if (this.title == '详情') {
          return
        }
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.model.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'put'
            }
            let formData = Object.assign(this.model, values)
            if (this.fileList != '') {
              formData.avatar = this.fileList
            } else {
              formData.avatar = null
            }
            httpAction(httpurl, formData, method)
              .then((res) => {
                if (res.success) {
                  that.$message.success(res.message)
                  that.$emit('ok')
                } else {
                  that.$message.warning(res.message)
                }
              })
              .finally(() => {
                that.confirmLoading = false
                that.close()
              })
          }
        })
      },
      validatePhone(rule, value, callback) {
        var params = {
          tableName: 'sys_users',
          fieldName: 'phone',
          fieldVal: value,
          dataId: this.userId
        }
        let reg = /(^(0[0-9]{2,3}\-)?([2-9][0-9]{6,7})+(\-[0-9]{1,4})?$)|(^((\d3)|(\d{3}\-))?(1[123456789]\d{9})$)/;
        if (!value || reg.test(value)) {
          getAction('/sys/duplicate/checkForUser', params).then((res) => {
            if (res.success) {
              callback()
            } else {
              callback('手机号已存在!')
            }
          })
        } else {
          callback('请输入正确的手机号或座机号');
        }
      },
      //取消
      handleCancel() {
        this.close()
      },
    },
  }
</script>

<style lang="less" scoped>
  @import '~@assets/less/normalModal.less';

  ::v-deep .two-words>div>label {
    letter-spacing: 4px;
  }

  ::v-deep .two-words>div>label::after {
    letter-spacing: 0px;
  }
</style>