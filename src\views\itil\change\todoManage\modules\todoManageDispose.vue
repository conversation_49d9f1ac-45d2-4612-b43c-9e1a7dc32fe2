<template>
  <a-drawer
    :width="modalWidth"
    :title="title"
    placement="right"
    :closable="false"
    :destroyOnClose="true"
    :visible="visible"
    :after-visible-change="afterVisibleChange"
    @close="onClose"
  >
    <a-form ref="form" :model="form" :label-width="85">
      <div v-if="form.status == 1">
        <a-form-item label="处理人" prop="assignees" :required="true">
          <l-select-user-by-dep v-model="form.handleUser" :multi="false"></l-select-user-by-dep>
        </a-form-item>
      </div>
      <!--programmeForm 方案 -->
      <div v-if="form.status == 2">
        <a-form-item label="标题" prop="assignees">
          <a-input placeholder="请输入" v-model="programmeForm.title" />
        </a-form-item>
        <a-form-item label="方案描述" prop="assignees">
          <a-textarea v-model="programmeForm.describe" placeholder="请输入" :auto-size="{ minRows: 3, maxRows: 5 }" />
        </a-form-item>
      </div>
      <!--实施 方案 -->
      <div v-if="form.status == 6">
        <a-row>
          <a-col :span="6">
            <a-button type="primary" @click="buttonClick()"> 提出发布 </a-button>
          </a-col>
        </a-row>
        <a-form-item label="标题" prop="assignees">
          <a-input placeholder="请输入" v-model="implementForm.title" />
        </a-form-item>
        <a-form-item label="实施描述" prop="assignees">
          <a-textarea v-model="implementForm.describe" placeholder="请输入" :auto-size="{ minRows: 3, maxRows: 5 }" />
        </a-form-item>
      </div>
      <div v-if="form.status == 3">
        <a-form-item label="审核结果" prop="reason">
          <a-radio-group name="radioGroup" :default-value="0" v-model="form.type" @change="onChange">
            <a-radio :value="0"> 通过 </a-radio>
            <a-radio :value="1"> 驳回 </a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="审批意见" prop="reason">
          <a-input type="textarea" v-model="form.comment" :rows="4" />
        </a-form-item>
        <a-form-item label="文件上传">
          <j-upload v-model="form.file"></j-upload>
        </a-form-item>
        <a-form-item label="下一审批人" prop="assignees" v-show="showAssign" :error="error">
          <a-select v-model="form.assignees" placeholder="请选择" allowClear mode="multiple" :loading="userLoading">
            <a-select-option v-for="(item, i) in assigneeList" :key="i" :value="item.username">{{
              item.realname
            }}</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="下一审批人" v-show="isGateway">
          <span>分支网关处暂不支持自定义选择下一审批人，将发送给下一节点所有人</span>
        </a-form-item>
        <div v-show="form.type == 1">
          <a-form-item label="驳回至">
            <a-select v-model="form.backTaskKey" :loading="backLoading" @change="changeBackTask">
              <a-select-option v-for="(item, i) in backList" :key="i" :value="item.key">{{
                item.name
              }}</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="指定原节点审批人" prop="assignees" v-show="form.backTaskKey != -1" :error="error">
            <a-select v-model="form.assignees" placeholder="请选择" allowClear mode="multiple" :loading="userLoading">
              <a-select-option v-for="(item, i) in assigneeList" :key="i" :value="item.username">{{
                item.realname
              }}</a-select-option>
            </a-select>
          </a-form-item>
        </div>
      </div>
      <a-form-item label="消息通知">
        <a-checkbox v-model="form.sendMessage">站内消息通知</a-checkbox>
        <!-- <a-checkbox v-model="form.sendSms" disabled>短信通知</a-checkbox>
        <a-checkbox v-model="form.sendEmail" disabled>邮件通知</a-checkbox> -->
      </a-form-item>
    </a-form>
    <div
      :style="{
        position: 'absolute',
        right: 0,
        bottom: 0,
        width: '100%',
        borderTop: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
        textAlign: 'right',
        zIndex: 1,
      }"
    >
      <a-button :style="{ marginRight: '8px' }" @click="onClose"> 取消 </a-button>
      <a-button type="primary" @click="handelSubmit"> 提交 </a-button>
    </div>
  </a-drawer>
</template>
<script>
import { queryDepartTreeList } from '@/api/api'
import JUpload from '@/components/jeecg/JUpload'
import JSelectUserByDep from '@/components/jeecgbiz/JSelectUserByDep'
import { getAction, deleteAction, putAction, postAction } from '@/api/manage'
import LSelectUserByDep from '@/components/jeecgbiz/LSelectUserByDep'
// import JSelectUserByDep from '@/components/jeecgbiz/JSelectUserByDep'
export default {
  components: { JSelectUserByDep, JUpload, LSelectUserByDep },
  name: 'todoDispose',
  data() {
    return {
      form: {
        id: '',
        userId: '',
        procInstId: '',
        comment: '',
        file: '',
        type: 0,
        assignees: [],
        backTaskKey: '',
        sendMessage: true,
        sendSms: false,
        sendEmail: false,
        status: 1,
        handleUser: '',
      },
      handleForm: {
        handleResult: '',
        title: '',
        describe: '',
      },
      programmeForm: {
        title: '',
        describe: '',
      },
      implementForm: {
        title: '',
        describe: '',
      },
      nextNodeParam: {
        procDefId: '',
        currActId: '',
        users: [],
        status: '',
        handleType: '',
      },
      formValidate: {
        // 表单验证规则

        handleResult: [
          {
            required: true,
            message: '请输入名称!',
          },
        ],
      },
      backList: [
        {
          key: '-1',
          name: '发起人',
        },
      ],
      error: '',
      showAssign: false,
      userLoading: false,
      backLoading: false,
      selectCount: 0, // 多选计数
      selectList: [], // 多选数据
      assigneeList: [],
      isGateway: false,
      assignedUser: '', //提出解决方案人员
      title: '变更处理',
      visible: false,
      autoExpandParent: true,
      modalWidth: '500px',
      checkedKeys: [],
      todoId: '',
      loading: false,
      owner: '',
      url: {
        todoList: '/actTask/todoList',
        pass: '/change/pass',
        back: '/change/back',
        backToTask: '/change/backToTask',
        delegate: '/actTask/delegate',
        getNextNode: '/activiti_process/getNextNode',
        getNode: '/activiti_process/getNode/',
        getBackList: '/actTask/getBackList/',
        passAll: '/actTask/passAll/',
        backAll: '/actTask/backAll/',
      },
    }
  },
  created() {
    // this.loadTree()
  },
  methods: {
    changeBackTask(v) {
      if (v == '-1') {
        return
      }
      this.userLoading = true
      getAction(this.url.getNode + v).then((res) => {
        this.userLoading = false
        if (res.success) {
          if (res.result.users && res.result.users.length > 0) {
            this.assigneeList = res.result.users
            // 默认勾选
            let ids = []
            res.result.users.forEach((e) => {
              ids.push(e.username)
            })
            this.form.assignees = ids
          }
        }
      })
    },

    afterVisibleChange(val) {},
    showDrawer() {
      this.visible = true
    },
    onClose() {
      this.visible = false
    },
    handelSubmit() {
      this.submitLoading = true
      var formData = Object.assign({}, this.form)
      if (formData.status == 1) {
        formData.assignees = formData.handleUser
      }
      //提出解决方案
      if (formData.status == 2) {
        formData.assignees = this.owner
      }
      //审核处理方案并且同意
      if (formData.status == 3 && this.form.type == 0) {
        formData.assignees = this.assignedUser
      }
      //审核处理方案并且驳回
      if (formData.status == 3 && this.form.type == 1) {
        formData.assignees = this.form.assignees.join(',')
      }
      //方案
      if (formData.status == 2) {
        formData.programmeForm = JSON.stringify(this.programmeForm)
        // formData.handleForm = JSON.stringify(this.handleForm);
      }
      //实施
      if (formData.status == 6) {
        formData.assignees = this.owner
        formData.implementForm = JSON.stringify(this.implementForm)
      }
      if (formData.type == 0) {
        this.adopt(formData)
      } else if (formData.type == 1) {
        this.reject(formData)
      }
    },
    //通过
    adopt(formData) {
      // 通过
      if (this.showAssign && formData.assignees.length < 1) {
        this.$message.error('请至少选择一个审批人')
        this.submitLoading = false
        return
      } else {
        this.error = ''
      }
      if (formData.status == 1 && formData.handleUser == ''){
        this.$message.error('请选择处理人')
        return
      }
      postAction(this.url.pass, formData).then((res) => {
        this.submitLoading = false
        if (res.success) {
          this.$message.success('操作成功')
          this.visible = false
          this.$emit('ok')
        }
      })
    },
    // 驳回
    reject(formData) {
      // 驳回
      if (formData.backTaskKey == '-1') {
        // 驳回至发起人
        postAction(this.url.back, formData).then((res) => {
          this.submitLoading = false
          if (res.success) {
            this.$message.success('操作成功')
            this.visible = false
            this.$emit('ok')
          }
        })
      } else {
        // 自定义驳回
        if (formData.backTaskKey != '-1' && formData.assignees.length < 1) {
          this.$message.error('请至少选择一个审批人')
          this.submitLoading = false
          return
        } else {
          this.error = ''
        }
        postAction(this.url.backToTask, formData).then((res) => {
          this.submitLoading = false
          if (res.success) {
            this.$message.success('操作成功')
            this.visible = false
            this.$emit('ok')
          }
        })
      }
    },

    show(v) {
      this.processDefinition = v
      this.form.id = v.id
      this.form.procInstId = v.procInstId
      this.form.priority = v.priority
      this.form.type = 0
      this.modalTaskVisible = true
      this.userLoading = true
      this.form.procDefId = v.procDefId
      this.visible = true
      this.form.status = v.status
      this.nextNodeParam.procDefId = v.procDefId
      this.nextNodeParam.currActId = v.key
      this.nextNodeParam.users = v.users
      this.nextNodeParam.status = v.status
      this.nextNodeParam.handleType = v.handleType
      this.owner = v.owner
      this.assignedUser = v.assignedUser
      this.form.file = ''
      //this.getAssignees();
    },
    //下级审批人
    getAssignees(v, users) {
      var procDefId = this.nextNodeParam.procDefId
      var currActId = this.nextNodeParam.currActId
      getAction(this.url.getNextNode, { procDefId: procDefId, currActId: currActId }).then((res) => {
        this.userLoading = false
        if (res.success) {
          if (res.result.type == 3 || res.result.type == 4) {
            this.isGateway = true
            this.showAssign = false
            this.error = ''
            return
          }
          this.isGateway = false
          // 默认勾选
          let ids = []
          if (res.result.users && res.result.users.length > 0) {
            this.error = ''
            this.assigneeList = res.result.users

            res.result.users.forEach((e) => {
              ids.push(e.username)
            })
            this.form.assignees = ids
            this.showAssign = true
          } else {
            if (this.nextNodeParam.status == 3 && this.nextNodeParam.handleType == 1) {
              this.form.assignees = []
              this.showAssign = false
              return
            }
            if (this.nextNodeParam.users && this.nextNodeParam.users.length > 0) {
              this.assigneeList = this.nextNodeParam.users
              this.nextNodeParam.users.forEach((e) => {
                ids.push(e.username)
              })
              this.form.assignees = ids
              this.showAssign = true
            } else {
              this.showAssign = false
            }
          }
        }
      })
    },
    //获取驳回节点
    backTask() {
      // 获取可驳回节点
      this.backList = []
      this.form.backTaskKey = this.processDefinition.assignedNodeId
      this.backLoading = true
      getAction(this.url.getBackList + this.processDefinition.procInstId).then((res) => {
        this.backLoading = false
        if (res.success) {
          res.result.forEach((e) => {
            if (e.key === this.processDefinition.assignedNodeId) {
              this.backList.push(e)
            }
          })
        }
      })
      this.changeBackTask(this.form.backTaskKey)
    },
    onChange(e) {
      var value = e.target.value
      if (0 == value) {
        this.setAssignees()
        this.showAssign = false
      } else {
        this.showAssign = false
        this.backTask()
      }
    },
    setAssignees() {
      this.form.assignees = this.assignedUser
    },
    onExpand(expandedKeys) {
      // if not set autoExpandParent to false, if children expanded, parent can not collapse.
      // or, you can remove all expanded children keys.
      this.expandedKeys = expandedKeys
      this.autoExpandParent = false
    },
    onCheck(checkedKeys) {
      this.checkedKeys = checkedKeys
    },
    onSelect(selectedKeys, info) {
      this.selectedKeys = selectedKeys
    },
    buttonClick() {
      this.$router.push({ path: '/release/apply', query: { changeId: this.processDefinition.busId } })
    },
  },
  watch: {},
  mounted() {},
}
</script>
<style scoped>
.drawer-bootom-button {
  position: absolute;
  bottom: 0;
  width: 100%;
  border-top: 1px solid #e8e8e8;
  padding: 10px 16px;
  text-align: right;
  left: 0;
  background: #fff;
  border-radius: 0 0 2px 2px;
}
</style>
