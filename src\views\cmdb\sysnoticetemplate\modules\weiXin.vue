<template>
  <div>
    <a-col :span='23'>
      <a-form-model-item :autoLink='true' label='微信模板id' prop='subject' :rules='validatorRules.subject'>
        <a-input v-model='personalizedObject.subject' :allowClear='true' autocomplete='off' placeholder='请输入微信模板id'
                 @change='changeValue' />
      </a-form-model-item>
    </a-col>
    <a-col v-if='usersList.length>0' :span='23'>
      <a-form-model-item :autoLink='true' class='two-words' label='关联人' prop='sendTo' :rules='validatorRules.sendTo'>
        <a-select v-model='personalizedObject.sendTo' :allowClear='true' :getPopupContainer='(node) => node.parentNode'
                  mode='multiple'
                  option-filter-prop='children' placeholder='请选择关联人' show-search @change='changeValue'>
          <a-select-option v-for='item in usersList' :key='item.id' :value=item.id>
            {{ item.realName }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
    </a-col>
    <a-col :span='23'>
<!--      <a-form-model-item ref="weixinExtends" v-for='(item, index) in personalizedObject.weixinExtendsList' :key="'weixinExtendsList['+index+']'" label='消息模板内容'
                         :prop="'weixinExtendsList['+index+'].value'" :autoLink='true'>
        <div style='display: flex;justify-content: space-between;align-items:center;white-space: nowrap;flex-direction: row'>
          <a-input v-model='personalizedObject.weixinExtendsList[index].value' :allowClear='true' autocomplete='off' placeholder='请输入消息模板内容'
                   style='margin-right: 20px' @change='changeValue'/>
          <a @click='deleteInfo(index)'>删除</a>
        </div>
      </a-form-model-item>-->
      <a-form-model-item  v-for='(item, index) in personalizedObject.weixinExtendsList' :key="index" label='消息模板内容'
                          :prop="'weixinExtendsList.' + index + '.value'"  :rules="validatorRules.weixinExtendsList">
        <div style='display: flex;justify-content: space-between;align-items:center;white-space: nowrap;flex-direction: row'>
          <a-input v-model='item.value' :allowClear='true' autocomplete='off' placeholder='请输入消息模板内容'
                   style='margin-right: 10px' @change='changeValue' @blur='changeValue'/>
<!--          <a @click='deleteInfo(index)' style='margin-right: 20px'>删除</a>-->
            <a-icon style='font-size: 20px; line-height: 40px; margin-right: 10px' type="minus-circle-o" @click="deleteInfo(index)"/>
        </div>
      </a-form-model-item>
      <div style='text-align: center; margin-bottom: 10px'>
        <a @click='addProperty'>
          <a-icon type='plus' />
          增加扩展属性</a>
        <span v-if='personalizedObject.weixinExtendsList&&personalizedObject.weixinExtendsList.length>0' style='margin-left: 10px'>
          <a-popover title='参数说明'>
            <template slot='content'>
              <p>消息模板内容要求输入表达式，格式为：变量=值，例如value=1</p>
            </template>
            <a-icon style='font-size: 20px; line-height: 40px' theme='twoTone' type='question-circle' />
          </a-popover>
        </span>
      </div>
    </a-col>
  </div>
</template>

<script>

export default {
  name: 'weiXin',
  props: {
    data: {
      type: Object,
      required: false,
      default: () => {
        let personalizedObject = {
          subject: '',
          sendTo: undefined,
          weixinExtendsList: []
        }
        return personalizedObject
      }
    },
    usersList: {
      type: Array,
      required: true,
      default: []
    }
  },
  data() {
    /*    const getTips=(fullField)=>{
          let str=''
          switch (fullField){
            case 'subject':
              str='请输入微信模板id！'
              break
            case 'sendTo' :
              str='请选择关联人！'
              break
          }
          return str
        }
        const customValidate = (rule, value, callback) => {
          if (rule.required) {
            if (value && value.length > 0) {
              callback()
            } else {
              //callback('请输入微信模板id！')
              callback(getTips(rule.fullField))
            }
          } else {
            callback()
          }
        }
        */
    return {
      personalizedObject: {
        subject: '',
        sendTo: undefined,
        weixinExtendsList: []
      },
      validatorRules: {
        // subject: [
        //   { required: true, validator: customValidate }
        // ],
        subject: [
          { required: true, validator: this.subjectValidate }
        ],
        sendTo: [
          { required: true, validator: this.sendtoValidate }
        ],
        weixinExtendsList:[
          { required: true, validator: this.weixinExtendsListValidate }
        ],
      }
    }
  },
  watch: {
    data: {
      handler(val) {
        if (Object.keys(val).length > 0) {
          this.personalizedObject = val
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    changeValue() {
      this.$emit('changeModelValue', this.personalizedObject)
    },
    addProperty() {
      let val=''
      this.personalizedObject.weixinExtendsList.push({value: val})
      this.changeValue()
    },
    deleteInfo(index) {
      this.personalizedObject.weixinExtendsList.splice(index, 1)
      this.changeValue()
    },
    getTips(fullField,value) {
      let str = ''
      if(fullField=='subject'){
        str = '请输入微信模板id！'
      }
      else if(fullField=='sendTo'){
        str = '请选择关联人！'
      }
      else if(fullField.includes('weixinExtendsList', 0)){
        str = '请输入消息模板内容!'
      }
      return str
    },
    subjectValidate(rule, value, callback) {
      if (rule.required) {
        if (value && value.length > 0) {
          if(value.length<2||value.length>20){
            callback('微信模板id长度应在 2-30 之间！')
          }
          else{
            callback()
          }
        } else {
          callback(this.getTips(rule.fullField,value))
        }
      } else {
        callback()
      }
    },
    sendtoValidate(rule, value, callback) {
      if (rule.required) {
        if (value && value.length > 0) {
            callback()
        } else {
          callback(this.getTips(rule.fullField,value))
        }
      } else {
        callback()
      }
    },
    weixinExtendsListValidate(rule, value, callback) {
      if (rule.required) {
        if (value && value.length > 0) {
          if(rule.fullField.includes('weixinExtendsList', 0)){
            if(value.length<3||value.length>50){
              callback('消息模板内容长度应在 3-50 之间！')
            }
           else if(value.indexOf('=')&&value.split('=').length===2&&value.split('=')[0]&&value.split('=')[1]){
              callback()
            }
            else {
              callback('请输入正确的表达式格式，例如：value=1')
            }
          }
          else{
            callback()
          }
        } else {
          callback(this.getTips(rule.fullField,value))
        }
      } else {
        callback()
      }
    },
    setPersonalizedData(data){
      let arr=[]
      if(data.length>0){
        data.map((item)=>{
          arr.push({value:item})
        })
      }
      return arr
    }
  }
}
</script>