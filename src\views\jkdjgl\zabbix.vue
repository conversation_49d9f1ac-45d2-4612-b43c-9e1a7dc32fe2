<template>
  <div style='height: 100%'>
    <a-card :bodyStyle="{ paddingBottom: '0', marginRight: '12px' }" :bordered='false' class='card-style'
            style='width: 100%'>
      <div class='table-page-search-wrapper'>
        <a-form layout='inline' v-bind='formItemLayout' @keyup.enter.native='searchQuery'>
          <a-row ref='row' :gutter='24'>
            <a-col :span='spanValue'>
              <a-form-item label='设备名称'>
                <a-input :maxLength='maxLength' v-model='queryParam.name' :allowClear='true' autocomplete='off'
                         placeholder='请输入设备名称' />
              </a-form-item>
            </a-col>
            <a-col :span='spanValue'>
              <a-form-item label='监控状态'>
                <j-dict-select-tag v-model='queryParam.status' placeholder='请选择设备状态' dictCode='device_status' />
              </a-form-item>
            </a-col>
            <a-col :span='spanValue'>
              <a-form-item label='标签'>
                <a-select
                  v-model='queryParam.tagKeys'
                  :allowClear='true'
                  :getPopupContainer='(target) => target.parentNode'
                  :maxTagCount='1'
                  :maxTagTextLength='5'
                  mode='multiple'
                  optionFilterProp='children'
                  placeholder='请选择标签'
                  showSearch
                  >
                  <a-select-option v-for='(item, index) in taginfoSelectData' :key='index' :value='item.tagKey'>
                    {{ item.tagName }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span='colBtnsSpan()'>
              <span :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                          class='table-page-search-submitButtons'>
                <a-button class='btn-search btn-search-style' type='primary' @click='dosearch'>查询</a-button>
                <a-button class='btn-reset btn-reset-style' @click='searchReset'>重置</a-button>
                <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                        {{ toggleSearchStatus ? '收起' : '展开' }}
                  <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                </a>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered='false' style='width: 100%; flex: auto;height: calc(100% - 96px);overflow-y: auto'>
      <div class='table-operator table-operator-style'>
        <a-button @click="handleExportXls('设备信息表','Zabbix')">导出</a-button>
        <a-dropdown v-if='selectedRowKeys.length > 0'>
          <a-menu slot="overlay" style='text-align: center'>
            <a-menu-item key='1' @click='batchDel'>删除</a-menu-item>
            <a-menu-item v-if=' batchEnable === 0' key='2' @click='batchEnableOperate(1)'>启用</a-menu-item>
            <a-menu-item v-else-if=' batchEnable === 1' key='3' @click='batchEnableOperate(0)'>禁用</a-menu-item>
          </a-menu>
          <a-button> 批量操作
            <a-icon type='down' />
          </a-button>
        </a-dropdown>
        <!--        <a-button @click="batchEnableOperate(1)" v-if="selectedRowKeys.length > 0 && batchEnable === 0">启用</a-button>-->
        <!--        <a-button  @click="batchEnableOperate(0)" v-if="selectedRowKeys.length > 0 && batchEnable === 1">禁用</a-button>-->
        <a-popover title='监控状态说明'>
          <template slot='content'>
            <div style='display: flex'>
              <div>
                <span>在线：</span>
                <img alt='' class='stateImg' src='~@assets/bigScreen/28.png' />
              </div>
              <div class='stateBox'>
                <span>离线：</span>
                <img alt='' class='stateImg' src='~@assets/bigScreen/57.png' />
              </div>
              <div class='stateBox'>
                <span>告警：</span>
                <img alt='' class='stateImg' src='~@assets/bigScreen/56.png' />
              </div>
              <div class='stateBox'>
                <span style='margin-left: 5px'>禁用：</span>
                <a-icon class='stateImg' style='font-size: 16px' theme='twoTone' two-tone-color='#eb2f96'
                        type='stop' />
                <span style='margin-left: 5px'></span>
              </div>
            </div>
          </template>
          <a-icon style='font-size: 18px' theme='twoTone' type='question-circle' />
        </a-popover>
      </div>
      <a-table
        style='overflow: hidden'
        ref='table'
        :columns='columns'
        :dataSource='dataSource'
        :loading='loading'
        :pagination='ipagination'
        :row-key='(record, index) => {return record.id}'
        :rowSelection='{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }'
        :scroll='dataSource.length > 0 ? { x: true } : {}'
        bordered
        @change='handleTableChange'>
<!--        <div slot='filterDropdown'>
          <a-card>
            <div style='text-align: right;font-size: small;color: #AAAAAC'>拖拽可进行排序</div>
            <a-divider style='margin: 5px 0px 5px 0px'></a-divider>
            <a-checkbox-group
              :key='refreshKey'
              v-model='settingColumns'
              :defaultValue='settingColumns'
              @change='onColSettingsChange'
            >
              <a-row style='width: 170px'>
                <a-col v-for='(item, idx) in defColumns'
                       v-if="item.key!='rowIndex'&&item.dataIndex != 'action'"
                       :key='"defColumns_"+idx' :span='24' @dragover='allowDrop($event)' @drop='drop($event,idx)'>
                  <div draggable='true' @dragend='dragEnd($event,idx)' @dragstart='dragStart($event,idx)'>
                    <a-checkbox :disabled='item.disabled' :key='"defColumns_checkbox_"+idx' :value='item.dataIndex'>
                      {{ item.title }}
                    </a-checkbox>
                  </div>
                </a-col>
              </a-row>
            </a-checkbox-group>
          </a-card>
        </div>
        <a-icon slot='filterIcon' :style="{ fontSize: '16px', color: '#108ee9' }" title='动态列表显示'
                type='setting' />-->
        <template slot='status' slot-scope='text, record'>
          <span v-if='record.enable == 1'>
            <img v-if='record.status==1' alt='' class='stateImg' src='~@assets/bigScreen/28.png' />
            <img v-else alt='' class='stateImg' src='~@assets/bigScreen/57.png' />
            <img v-if='record. alarmStatus==1' alt='' class='stateImg alarmStatus'
                       src='~@assets/bigScreen/56.png' />
            <a-icon class='stateImg' style='font-size: 16px' theme='twoTone' two-tone-color='#eb2f96'
                          type='stop' />
          </span>
          <span style='margin-left: 10px'>{{ text }}</span>
        </template>

        <span slot='action' slot-scope='text, record' class='caozuo'>
          <a @click='handleDetailPage(record)'>查看</a>
          <a-divider type='vertical' />
           <a-dropdown>
          <a-menu slot='overlay'>
            <a-menu-item key='4' @click='delRecord(record)'>
              <a>删除</a>
            </a-menu-item>
            <a-menu-item v-if=' record.enable === 0' key='5' @click='deviceEnable(record,1)'>
              <a>启用</a>
            </a-menu-item>
            <a-menu-item v-else-if=' record.enable === 1' key='6' @click='deviceEnable(record,0)'>
              <a>禁用</a>
            </a-menu-item>
          </a-menu>
           <a>更多</a>
        </a-dropdown>
        </span>
        <template slot='Enable' slot-scope='text, record'>
          <div v-if='record.enable === 1'>
            <!-- <a-icon type="check-circle" theme="twoTone" two-tone-color="#52c41a" /> -->
            <a-icon style='font-size: 20px' theme='twoTone' two-tone-color='#52c41a' type='pause-circle' />
            <!-- <span style="margin-left: 5px">{{record.name}}</span> -->
          </div>
          <div v-else>
            <!-- <a-icon type="stop" theme="twoTone" two-tone-color="#eb2f96"/> -->
            <a-icon style='font-size: 20px' theme='twoTone' two-tone-color='#eb2f96' type='play-circle' />
            <!-- <span style="margin-left: 5px">{{record.name}}</span> -->
          </div>
        </template>
        <template slot='tooltip' slot-scope='text'>
          <a-tooltip :title='text' placement='topLeft' trigger='hover'>
            <div class='tooltip'>
              {{ text }}
            </div>
          </a-tooltip>
        </template>
        <template slot='tagInfoList' slot-scope='text'>
          <a-popover title='标签'>
            <template slot='content'>
              <div v-for='item in text' :key='item.id' style='margin: 5px 0'>
                      <span :style="{
                          'background-color': item.tagColor,
                          color: 'white',
                          'border-radius': '10px',
                          padding: '2px 10px',
                        }">
                        {{ item.tagName }}
                      </span>
              </div>
            </template>
            <a-icon type='environment' />
            <!-- <div style="display:flex,direction:column">
              <p v-for="item in text"
                :style="{'background-color':item.tag_color, color:'white',padding:'2px 1px',}">
                {{item.tag_name}}
                </p>
            </div> -->
          </a-popover>
        </template>
      </a-table>
    </a-card>
    <zabbix-host-list ref='zabbixHostList'></zabbix-host-list>
  </div>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import {
  httpAction,
  getAction,
  postAction,
  deleteAction
} from '@/api/manage'
import JDictSelectTag from '@/components/dict/JDictSelectTag.vue'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
import deviceStatistic from '@views/devicesystem/modules/DeviceStatistic.vue'
import Vue from 'vue'
import ZabbixHostList from '@views/jkdjgl/model/ZabbixHostList.vue'

export default {
  name: 'DeviceInfoList',
  mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
  components: {
    ZabbixHostList,
    JDictSelectTag,
    deviceStatistic
  },
  data() {
    return {
      maxLength:50,
      description: '设备表管理页面',
      //设备数量
      deviceInfo: [
        {
          type: '全部设备',
          count: '',
          img: require('@/assets/01.png')
        },
        {
          type: '在线设备',
          count: '',
          img: require('@/assets/02.png')
        },
        {
          type: '离线设备',
          count: '',
          img: require('@/assets/03.png')
        },
        {
          type: '未启用设备',
          count: '',
          img: require('@/assets/04.png')
        }],
      /* deviceInfo: {
         allNum: '',
         outNum: '',
         onNum: '',
         disabledNum: ''
       },*/
      formItemLayout: {
        labelCol: { style: 'width:80px' },
        wrapperCol: {
          style: 'width:calc(100% - 80px)'
        }
      },
      taginfoSelectData: [],
      //0显示禁用，1显示启用，2列表未选中，禁用启用都显示，3、列表选中了已禁用、已启用设备，此时不显示禁用启用
      showBatchEnable: 2,
      //刷新列表操作设置
      refreshKey: 0,
      //列设置
      settingColumns: [],
      //列表默认显示的所有字段
      // defColumns
      columns: [
        {
          title: '序号',
          dataIndex: '',
          disabled: false,
          display: true,
          // fixed: 'left',
          width: 60,
          key: 'rowIndex',
          customRender: function(t, r, index) {
            return parseInt(index) + 1
          }
        },
        {
          title: '设备名称',
          dataIndex: 'name',
          display: true,//列表是否默认显示
          disabled: false,//列操作中，checkbox是否可操作
          scopedSlots: {
            customRender: 'status'
          },
          customCell: () => {
            let cellStyle = 'text-align: left'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '产品名称',
          dataIndex: 'productName',
          display: false,
          disabled: false
        },
        {
          title: '通信协议',
          dataIndex: 'transferProtocol',
          display: true,
          disabled: false,
          scopedSlots: {
            customRender: 'transferProtocol'
          }
        },
        {
          title: '所属网关',
          dataIndex: 'gatewayName',
          display: true,
          disabled: false,
          scopedSlots: {
            customRender: 'gatewayName'
          },
          customRender: (text, record, index) => {
            if (record.gatewayName == '' || record.gatewayName == null || record.gatewayName == undefined) {
              return '未绑定网关'
            } else {
              return text
            }
          }
        },
        {
          title: '添加时间',
          dataIndex: 'createTime',
          display: false,
          disabled: false,
          scopedSlots: {
            customRender: 'createTime'
          }
        },
        {
          title: '标签',
          dataIndex: 'tagInfoList',
          display: false,
          disabled: false,
          scopedSlots: {
            customRender: 'tagInfoList'
          }
        },
        {
          title: '关联资产',
          dataIndex: 'assetsName',
          display: true,
          disabled: false
        },
        /*{
          title: '创建人',
          dataIndex: 'createBy',
           display:false,
          disabled:false,
        },*/
        // {
        //   title: '添加时间',
        //   dataIndex: 'createTime',
        //   display:true,
        //   disabled:false,
        // },
        /* {
           title: '单位名称',
           dataIndex: 'momgDeptName',
            display:false,
           disabled:false,
         },*/
        {
          title: '设备说明',
          dataIndex: 'description',
          display: false,
          disabled: false,
          scopedSlots: {
            customRender: 'tooltip'
          },
          customCell: () => {
            let cellStyle = 'text-align: left;max-width:400px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '操作',
          dataIndex: 'action',
          display: true,
          disabled: false,
          align: 'center',
          fixed: 'right',
          width: 140,
          scopedSlots: {
            // filterDropdown: 'filterDropdown',
            // filterIcon: 'filterIcon',
            customRender: 'action'
          }
        }
      ],
      // 表头
      // columns: [],
      url: {
        list: '/device/deviceInfo/vcenterList',
        delete: '/device/deviceInfo/delete',
        deleteBatch: '/device/deviceInfo/deleteBatchDevice',
        exportXlsUrl: '/device/deviceInfo/exportXls',
        importExcelUrl: 'device/deviceInfo/importExcel',
        deviceEnable: '/device/deviceInfo/updateEnable',
        batchEnable: '/device/deviceInfo/batchUpdateEnable',
        deviceInfoUrl: '/device/deviceInfo/showCounts',
        getUrl: '/device/deviceInfo/getUrl',
        unbund: '/gateway/deviceInfo/Unbundling', //解绑
        childUrl: '/gateway/deviceInfo/queryGatewayChildById'
      },
      disableMixinCreated: false,
      queryParam: {}
    }
  },
  computed: {
    importExcelUrl: function() {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    }
  },
  created() {
    // this.initColumns()
    this.queryAllTags()
  },
  activated() {
    this.loadData()
  },
  methods: {
    queryAllTags() {
      getAction('/device/deviceInfo/taginfo4Select').then((res) => {
        if (res.success) {
          this.taginfoSelectData = res.result
        }
      })
    },
/*    /!*列设置更改事件*!/
    onColSettingsChange(checkedValues) {
      this.refreshKey++
      this.settingColumns = checkedValues
      let tempDataIndex = []
      let tempColumns = []
      this.defColumns.forEach((item) => {
        if (item.fixed) {
          item.fixed = null
        }

        if (item.key == 'rowIndex' || item.dataIndex == 'action') {
          tempColumns.push(item)
        }
        if (this.settingColumns.includes(item.dataIndex)) {
          item.disabled = checkedValues.length > 1 ? false : true
          tempDataIndex.push(item.dataIndex)
          tempColumns.push(item)
        } else {
          item.disabled = false
        }
      })
      this.columns = tempColumns
      // this.columns[0].fixed = 'left'
      // this.columns[1].fixed = 'left'
      //this.columns[2].fixed = 'left'
      this.columns[this.columns.length - 1].fixed = 'right'
      this.settingColumns = tempDataIndex
      //将已选要显示列表dataIndex数据写入缓存中
      var key = this.$route.name + ':colsettings'
      // Vue.ls.set(key, tempDataIndex, 7 * 24 * 60 * 60 * 1000)
      Vue.ls.set(key, tempDataIndex)
    },
    /!*读取缓存，设置表头*!/
    initColumns() {
      //获取默认列表的缓存dataIndex数据集合
      var key1 = this.$route.name + ':allCols'
      Vue.ls.remove(key1)
      let allCols = Vue.ls.get(key1)
      if (allCols) {
        let tempDefColumns = []
        allCols.forEach(dataIndex => {
          this.defColumns.forEach(item => {
            if (dataIndex == item.dataIndex) {
              tempDefColumns.push(item)
            }
          })
        })
        tempDefColumns.unshift(this.defColumns[0])
        tempDefColumns.push(this.defColumns[this.defColumns.length - 1])
        this.defColumns = tempDefColumns //按照缓存排序重新给默认列表复制
      }
      //缓存数据集合为空时，按照data中定义的defColumns列表顺序写缓存
      else {
        let tempDataIndex = []
        this.defColumns.forEach(item => {
          if (item.dataIndex != 'action' && item.key != 'rowIndex') {
            tempDataIndex.push(item.dataIndex)
          }
        })
        // Vue.ls.set(key2, tempDataIndex, 7 * 24 * 60 * 60 * 1000);
        Vue.ls.set(key1, tempDataIndex)
      }

      //权限过滤（列权限控制时打开，修改第二个参数为授权码前缀）
      //从缓存中获取已选且在列表中显示的dataIndex数据
      var key2 = this.$route.name + ':colsettings'
      let colSettings = Vue.ls.get(key2)
      //已选显示列表缓存dataIndex数据集合为空
      if (colSettings == null || colSettings == undefined) {
        let allSettingColumns = []
        this.defColumns.forEach(function(item, i, array) {
          if (item.fixed) {
            item.fixed = null
          }
          item.disabled = false
          if (item.display) {
            allSettingColumns.push(item.dataIndex)
          }
        })
        this.settingColumns = allSettingColumns
        Vue.ls.set(key2, allSettingColumns)
        this.columns = this.defColumns.filter((item) => {
          return item.display === true
        })
      }
      //已选列表缓存dataIndex数据集合不为空，设置table要显示的列
      else {
        this.settingColumns = colSettings
        const cols = this.defColumns.filter((item) => {
          if (item.fixed) {
            item.fixed = null
          }
          if (item.dataIndex == 'action' || item.key == 'rowIndex') {
            return true
          }
          if (colSettings.includes(item.dataIndex)) {
            item.disabled = colSettings.length > 1 ? false : true//只有一个字段时，不可点击取消
            return true
          }
          return false
        })
        this.columns = cols
      }
      // this.columns[0].fixed = 'left'
      // this.columns[1].fixed = 'left'
      /!* this.columns[2].fixed = 'left'*!/
      this.columns[this.columns.length - 1].fixed = 'right'
    },
    dragStart(event, id) {
      this.id = id
      this.dom = event.currentTarget.cloneNode(true)
    },
    dragEnd(event, index) {
      event.preventDefault()
      //table列显示顺序同步拖拽后的顺序
      let tempSettingDataIndex = []
      let tempAllDataIndex = []
      const cols = this.defColumns.filter(item => {
        if (item.fixed) {
          item.fixed = null
        }
        if (item.key == 'rowIndex' || item.dataIndex == 'action') {
          return true
        } else {
          tempAllDataIndex.push(item.dataIndex)
        }
        if (this.settingColumns.includes(item.dataIndex)) {
          tempSettingDataIndex.push(item.dataIndex)
          return true
        }
        return false
      })
      this.settingColumns = tempSettingDataIndex //按照排序重新保存选中显示列表
      this.columns = cols
      // this.columns[0].fixed = 'left'
      // this.columns[1].fixed = 'left'
      //this.columns[2].fixed = 'left'
      this.columns[this.columns.length - 1].fixed = 'right'
      var key1 = this.$route.name + ':colsettings' //浏览器缓存重新记录排序后的选中列表
      //Vue.ls.set(key1, tempColumns, 7 * 24 * 60 * 60 * 1000)//保留7天缓存
      Vue.ls.set(key1, tempSettingDataIndex)

      var key2 = this.$route.name + ':allCols' //浏览器缓存重新记录排序后的所有列表
      Vue.ls.set(key2, tempAllDataIndex)

      this.refreshKey++
      this.dom.remove()
    },
    allowDrop(event) {
      event.preventDefault()
    },
    drop(event, index) {
      event.preventDefault()
      //默认列表重新进行排序
      if (index != this.id) {
        let temp = this.defColumns[this.id]
        this.defColumns.splice(this.id, 1)
        this.defColumns.splice(index, 0, temp)
      }
    },*/

    loadData(arg) {
      if (!this.url.list) {
        this.$message.error('请设置url.list属性!')
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1
      }
      //从数据中心index.vue路由跳转后，根据路由参数设置列表查询条件
      if (this.$route.params.deviceName) {
        this.queryParam.name = this.$route.params.deviceName
      }
      var params = this.getQueryParams() //查询条件
      this.loading = true
      params.transfer = 'Zabbix'
      getAction(this.url.list, params).then((res) => {
        if (res.success) {
          this.dataSource = res.result.records || res.result
          if (this.dataSource.length < 9) {
            this.clientHeight = false
          }
          this.ipagination.total = res.result.total
        }
        if (res.code === 510) {
          this.$message.warning(res.message)
        }
        this.loading = false
      })
    },
    //勾选时进行状态判断
    onSelectChange(selectedRowKeys, selectionRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectionRows = selectionRows
      let disableFlag = this.selectionRows.some((ele) => ele.enable === 0)
      let ableFlag = this.selectionRows.some((ele) => ele.enable === 1)
      if (disableFlag && ableFlag) {
        this.batchEnable = 2
      } else if (disableFlag) {
        this.batchEnable = 0
      } else {
        this.batchEnable = 1
      }
    },
    handleDetailPage: function(record) {
      this.$refs.zabbixHostList.show(record)
    },
    //设备禁用/启用
    deviceEnable(record,enable) {
      let that = this;
      this.$confirm({
        title: '确认操作',
        okText: '是',
        cancelText: '否',
        content: '是否确定修改这条数据?',
        onOk: function() {
          that.loading = true
          getAction(that.url.batchEnable, {
            ids: record.id,
            enable: enable,
            transfer: 'Zabbix'
          })
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.loadData()
                that.onClearSelected()
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.loading = false
            })
        }
      })
    },
    //批量禁用、启用
    batchEnableOperate(enable) {
      if (!this.url.batchEnable) {
        this.$message.error('请设置url.batchConfirm属性!')
        return
      }
      if (this.selectedRowKeys.length <= 0) {
        this.$message.warning('请选择一条记录！')
        return
      } else {
        var ids = ''
        for (var a = 0; a < this.selectedRowKeys.length; a++) {
          ids += this.selectedRowKeys[a] + ','
        }
        var that = this
        this.$confirm({
          title: '确认操作',
          okText: '是',
          cancelText: '否',
          content: '是否确定修改选中数据?',
          onOk: function() {
            that.loading = true
            getAction(that.url.batchEnable, {
              ids: ids,
              enable: enable,
              transfer: 'Zabbix'
            })
              .then((res) => {
                if (res.success) {
                  that.$message.success(res.message)
                  that.loadData()
                  that.onClearSelected()
                } else {
                  that.$message.warning(res.message)
                }
              })
              .finally(() => {
                that.loading = false
              })
          }
        })
      }
    },
    //单个删除
    delRecord(record) {
      let that = this;
      this.$confirm({
        title: '确认删除',
        okText: '是',
        cancelText: '否',
        content: '是否删除这条数据?',
        onOk: function() {
          that.loading = true
          deleteAction(that.url.deleteBatch, { ids: record.id, transfer: 'Zabbix' })
            .then((res) => {
              if (res.success) {
                //重新计算分页问题
                that.$message.success(res.message)
                that.loadData()
                that.onClearSelected()
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.loading = false
            })
        }
      })
    },
    //批量删除
    batchDel: function() {
      if (!this.url.deleteBatch) {
        this.$message.error('请设置url.deleteBatch属性!')
        return
      }
      if (this.selectedRowKeys.length <= 0) {
        this.$message.warning('请选择一条记录！')
        return
      } else {
        var ids = ''
        for (var a = 0; a < this.selectedRowKeys.length; a++) {
          ids += this.selectedRowKeys[a] + ','
        }
        var that = this
        this.$confirm({
          title: '确认删除',
          okText: '是',
          cancelText: '否',
          content: '是否删除选中数据?',
          onOk: function() {
            that.loading = true
            deleteAction(that.url.deleteBatch, { ids: ids, transfer: 'Zabbix' })
              .then((res) => {
                if (res.success) {
                  //重新计算分页问题
                  that.reCalculatePage(that.selectedRowKeys.length)
                  that.$message.success(res.message)
                  that.loadData()
                  that.onClearSelected()
                } else {
                  that.$message.warning(res.message)
                }
              })
              .finally(() => {
                that.loading = false
              })
          }
        })
      }
    },
    //表单查询,点击查询按钮，默认查询第一页
    dosearch() {
      this.loadData(1)
    },
    searchReset() {
      //重置form表单，不重置tree选中节点
      if (this.queryParam.option) {
        let type = this.queryParam.type
        let option = this.queryParam.option
        this.queryParam = {}
        this.queryParam.type = type
        this.queryParam.option = option
      } else {
        this.queryParam = {}
      }
      this.queryParam.transfer = 'Zabbix'
      this.loadData(1)
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';

.gutter-example {
  margin-bottom: 8px
}

.stateBox {
  margin-left: 20px;
}

.stateImg {
  vertical-align: middle;
}

.alarmStatus {
  margin-left: 8px;
}

.overlay {
  color: #409eff
}
//修改列表筛选弹窗遮挡的问题
/deep/ .ant-card-body{
  height: 100%;
}
/deep/ .ant-table-wrapper{
  height: calc(100% - 40px);
}
//修改选择框多选溢出
/deep/ .table-page-search-wrapper .ant-form-inline .ant-form-item .ant-form-item-control-wrapper{
  width: calc(100% - 80px);
}
/deep/ .ant-select-selection--multiple{
  height: 32px;
  overflow-y: auto;
}
</style>