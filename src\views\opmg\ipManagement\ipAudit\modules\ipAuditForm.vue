<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container>
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-item label="任务名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['auditName', validatorRules.auditName]" :allowClear="true" autocomplete="off"
                placeholder="请输入任务名称"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="审计类型" :labelCol="labelCol" :wrapperCol="wrapperCol" :required="true">
              <a-radio-group v-decorator="['auditType', validatorRules.auditType]" @change="onChange">
                <a-radio :value="'0'">
                  子网组
                </a-radio>
                <a-radio :value="'1'">
                  子网
                </a-radio>
                <a-radio :value="'2'">
                  网段
                </a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="审计对象" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-select v-decorator="['auditObject', validatorRules.auditObject]" :allowClear="true"
                placeholder="请选择审计对象">
                <a-select-option v-for="item in auditList" :key="item.id">{{item.title}}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="定时任务表达式" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <!-- <j-cron ref="innerVueCron" v-decorator="['cron', { initialValue: '0 0 0 * * ? *' }, validatorRules.cron]"
                @change="setCorn"></j-cron> -->
              <j-cron ref="innerVueCron" v-decorator="['cron',{ initialValue: '0 0 0 * * ? *', rules: [{ required:
                  true,validator:this.validateCorn }]}]" @change="setCorn"></j-cron>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="审计策略" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-checkbox-group v-decorator="['auditStrategy',{ initialValue: [] },validatorRules.auditStrategy]"
                @change="groupChange">
                <a-row>
                  <a-col :span="12">
                    <a-checkbox value="0">使用的IP是否已配置</a-checkbox>
                  </a-col>
                  <a-col :span="12">
                    <a-checkbox value="1">IP对应的MAC地址是否一致</a-checkbox>
                  </a-col>
                  <a-col :span="12">
                    <a-checkbox value="2">对应的设备是否在线</a-checkbox>
                  </a-col>
                </a-row>
              </a-checkbox-group>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="是否告警" :labelCol="labelCol1" :wrapperCol="wrapperCol1" :required="true">
              <a-switch checkedChildren="是" unCheckedChildren="否"
                v-decorator="['alarm', { initialValue: false, valuePropName: 'checked' }]" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="是否启用" :labelCol="labelCol1" :wrapperCol="wrapperCol1" :required="true">
              <a-switch checkedChildren="是" unCheckedChildren="否"
                v-decorator="['enabled', { initialValue: false, valuePropName: 'checked' }]" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="备注" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-textarea :autoSize="{ minRows: 1, maxRows: 4 }" v-decorator="['remark',validatorRules.remark]"
                :allowClear="true" autocomplete="off" placeholder="请输入备注"></a-textarea>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
  import {
    httpAction,
    getAction
  } from '@/api/manage'
  import pick from 'lodash.pick'
  import {
    validateDuplicateValue
  } from '@/utils/util'
  import JFormContainer from '@/components/jeecg/JFormContainer'
  import JDictSelectTag from '@/components/dict/JDictSelectTag'
  import JCron from '@/components/jeecg/JCron.vue'

  export default {
    name: 'DevopsBackupProForm',
    components: {
      JFormContainer,
      JDictSelectTag,
    },
    data() {
      return {
        form: this.$form.createForm(this),
        model: {},
        auditList: [],
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          },
        },
        labelCol1: {
          xs: {
            span: 24
          },
          sm: {
            span: 10
          },
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          },
        },
        wrapperCol1: {
          xs: {
            span: 24
          },
          sm: {
            span: 10
          },
        },
        confirmLoading: false,
        model: {},
        validatorRules: {
          auditName: {
            rules: [{
                required: true,
                message: '请输入任务名称'
              },
              {
                min: 2,
                max: 30,
                message: '任务名称长度在2到30个字符',
                trigger: 'blur'
              },
            ],
          },
          auditType: {
            rules: [{
              required: true,
              message: '请选择审计类型'
            }, ],
          },
          auditObject: {
            rules: [{
              required: true,
              message: '请选择审计对象'
            }, ],
          },
          cron: {
            rules: [{
              required: true,
              message: '请输入定时任务表达式'
            }, ],
          },
          auditStrategy: {
            rules: [{
              required: true,
              message: '请选择审计策略'
            }, ],
          },
          remark: {
            rules: [{
              max: 200,
              message: '备注字符不能超过200个字符'
            }, ],
          },
        },
        url: {
          add: '/devops/ip/auditTask/add',
          edit: '/devops/ip/auditTask/edit',
          queryById: '/devops/ip/auditTask/queryById',
          queryListByType: '/devops/ip/queryListByType',
          validateCorn: '/autoInspection/devopsAutoInspection/cronCheck'
        },
      }
    },
    methods: {
      groupChange(value) {
        if (value.length > 0) {
          if (value[value.length - 1] == '1') {
            this.$message.info('此审计策略只能在部署本产品的服务器局域网内生效', 5)
          }
        }
      },
      onChange(e) {
        this.form.setFieldsValue({
          auditObject: ''
        })
        let value = e.target.value
        this.$forceUpdate()
        this.getType(value)
      },
      getType(value) {
        getAction(this.url.queryListByType, {
          type: value
        }).then((res) => {
          this.auditList = res.result
        })
      },
      add() {
        this.edit({})
      },
      validateCorn(rule, value, callback) {
        if (rule.required) {
          if (value && value.length > 0) {
            if (value.split(' ').length > 7) {
              callback('cron表达式格式错误!')
            } else {
              this.getValidateCornTips(value).then((res) => {
                callback(res)
              }).catch((err) => {
                callback(err)
              })
            }
          } else {
            callback('请输入或选择cron表达式')
          }
        } else {
          if (value && value.length > 0) {
            if (value.split(' ').length > 7) {
              callback('cron表达式格式错误!')
            } else {
              this.getValidateCornTips(value).then((res) => {
                callback(res)
              }).catch((err) => {
                callback(err)
              })
            }
          } else {
            callback()
          }
        }
      },
      getValidateCornTips(value) {
        return new Promise((resolve, reject) => {
          getAction(this.url.validateCorn, {
            cronExpression: value
          }).then((res) => {
            if (res.success) {
              resolve()
            } else {
              this.$message.warning(res.message)
              reject('cron表达式格式错误!')
            }
          }).catch((err) => {
            this.$message.warning(err.message)
            reject('cron表达式格式错误!')
          })
        })
      },
      setCorn(data) {
        if (data && data.target != null) {
          let dataList = data.target.value.split(' ')
          if (dataList[0] == '*') {
            this.$message.warning('请确认是否每秒都执行')
          }
        } else {
          let dataList = data.split(' ')
          if (dataList[0] == '*') {
            this.$message.warning('请确认是否每秒都执行')
          }
        }
        this.$nextTick(() => {
          this.form.cronExpression = data
        })
      },
      edit(record) {
        let auditStrategyArray = []
        if (record && record.auditType != null) {
          this.getType(record.auditType)
        }
        this.form.resetFields()
        if (record['auditStrategy'] && record['auditStrategy'].length > 0) {
          auditStrategyArray = record['auditStrategy'].split(',')
        } else {
          record['auditStrategy'] = []
        }
        record['enabled'] = Boolean(record['enabled'])
        record['alarm'] = Boolean(record['alarm'])
        this.model = Object.assign({}, record)
        this.model.auditStrategy = auditStrategyArray
        this.visible = true
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.model, 'auditName', 'auditType', 'auditObject', 'cron', 'enabled',
            'alarm', 'auditStrategy', 'remark'))
        })
      },
      submitForm() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.model.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'put'
            }
            values['alarm'] = Number(values['alarm'])
            values['enabled'] = Number(values['enabled'])
            let formData = Object.assign(this.model, values)
            formData.auditStrategy = formData.auditStrategy.join(',')
            httpAction(httpurl, formData, method)
              .then((res) => {
                if (res.success) {
                  that.$message.success(res.message)
                  that.$emit('ok')
                } else {
                  that.$message.warning(res.message)
                }
              })
              .finally(() => {
                that.confirmLoading = false
              })
          }
        })
      },
      popupCallback(row) {
        this.form.setFieldsValue(pick(row, 'auditName', 'auditType', 'auditObject', 'cron', 'enabled', 'alarm',
          'auditStrategy', 'remark'))
      },
    },
  }
</script>