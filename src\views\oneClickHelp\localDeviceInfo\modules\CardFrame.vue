<template>
  <div class='bg'>
    <div class='head-wrapper'>
      <div class='head-img'  v-if='showHeadBgImg'></div>
      <div class='head-left'></div>
      <div class='head-right'></div>

      <div class='left'>
        <img class='img' src='/oneClickHelp/localDeviceInfo/head.png' v-if='showTitleImg'/>
        <span class='title'>{{title}}</span>
      </div>
      <div class='right' v-if='showRefresh'  @click='handleRefresh' title='刷新'><a-icon type='reload' class='icon'/></div>
    </div>

    <div  class='body-wrapper'>
      <slot name='bodySlot'></slot>
      <div class='footer-wrapper' v-if='showFooter'></div>
    </div>
  </div>
</template>
<script>
import fa from 'element-ui/src/locale/lang/fa'

export default {
  name: "CardFrame",
  props:{
    showRefresh: {
      type: Boolean,
      required: false,
      default: false
    },
    showHeadBgImg:{
      type:Boolean,
      required:false,
      default:false
    },
    showTitleImg:{
      type:Boolean,
      required:false,
      default:true
    },
    showFooter:{
      type:Boolean,
      required:false,
      default:false
    },
    title:{
      type:String,
      required:true,
    },
  },
  data() {
    return {}
  },
  methods:{
    handleRefresh(){
      this.$emit('handleRefresh')
    }
  }
}
</script>

<style scoped lang="less">
.bg {
  //border: 1px solid white;
  height: 100%;
  background-image: url("/oneClickHelp/localDeviceInfo/bg.png"), linear-gradient(180deg, rgba(18, 41, 83, 0.00) 0%, rgba(18, 41, 83, 0.50) 18%, rgba(41, 83, 139, 0.10) 100%);

  .head-wrapper {
    height: 0.625rem; //50px/80px
    background-color: rgba(76, 139, 255, 0.10);
    border: 1px solid rgba(47, 91, 255, 0.30);
    position: relative;
    padding-left: 0.225rem; //18px/80;
    padding-right: 0.225rem; //18px/80
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-flow: row nowrap;
    font-size: 0.2rem; //16px

    .head-img {
      position: absolute;
      content: '';
      height: 100%;
      //width: 100%;
      bottom: 0px;
      left: 0px;
      right: 0px;
      top: 0px;
      background-image: url("/oneClickHelp/localDeviceInfo/leftHeadBg.png");
      //background-size: cover;
      background-repeat: no-repeat;
      background-position: right center;
    }

    .head-left,.head-right{
      position: absolute;
      content: '';
      width: 100%;
      height: 100%;
      top:0;
      bottom:0;
      right:0;
      left:0
    }
    .head-left::before {
      position: absolute;
      content: '';
      top: 0;
      left: 0;
      width: 3px;
      height: 3px;
      background-color: #2F5BFF;
    }

    .head-left::after {
      position: absolute;
      content: '';
      bottom: 0;
      left: 0;
      width: 3px;
      height: 3px;
      background-color: #2F5BFF;
    }

    .head-right::before {
      position: absolute;
      content: '';
      top: 0;
      right: 0;
      width: 3px;
      height: 3px;
      background-color: #2F5BFF;
    }

    .head-right::after {
      position: absolute;
      content: '';
      bottom: 0;
      right: 0;
      width: 3px;
      height: 3px;
      background-color: #2F5BFF;
    }

    .left{

      .title {
        color: #ffffff;
        margin-left: 5px;
      }
    }

    .right{
      color:#ffffff;
      z-index: 100;
      .icon{
        cursor: pointer;
      }
      .icon:hover{
        color:#66ffff;
      }
    }
  }

  .body-wrapper {
    height: calc(100% - 0.625rem); //calc(100% - 50px)
    position: relative;
    overflow-x: hidden;
    overflow-y: auto;

    .footer-wrapper::before {
      position: absolute;
      content: '';
      bottom: 0;
      left: 0;
      width: 27px;
      height: 27px;
      background-image: url("/oneClickHelp/localDeviceInfo/leftFooterBg.png");
      background-size: cover;
      background-repeat: no-repeat;
    }

    .footer-wrapper::after {
      position: absolute;
      content: '';
      bottom: 0;
      right: 0;
      width: 27px;
      height: 27px;
      background-image: url("/oneClickHelp/localDeviceInfo/leftFooterBg.png");
      background-size: cover;
      background-repeat: no-repeat;
      transform: rotate(270deg);
    }

    .body-empty {
      height: 100%;
      .spin{
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
}
::v-deep .ant-empty .ant-empty-description{
  color:#ffffff !important;
}
</style>