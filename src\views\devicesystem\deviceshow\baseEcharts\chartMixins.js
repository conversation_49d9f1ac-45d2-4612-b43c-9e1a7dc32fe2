export const chartMixins = {
  data() {
    return {
      resizeObserver: null
    }
  },
  mounted() {
    this.initResizeObserver()
  },
  beforeDestroy() {
    this.delResizeObserver()
  },
  methods: {
    initResizeObserver() {
      let instance = this.$refs.baseEcharts
      this.resizeObserver = new ResizeObserver((entries) => {
        if (this.myChart) {
          this.myChart.resize()
        }
      })
      this.resizeObserver.observe(instance)
    },
    delResizeObserver() {
      let instance = this.$refs.baseEcharts
      if (instance) {
        this.resizeObserver.unobserve(instance)
      }
      this.resizeObserver = null
    },
  }
}