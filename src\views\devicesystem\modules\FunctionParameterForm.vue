<template>
  <a-spin :spinning='confirmLoading'>
    <j-form-container :disabled='formDisabled'>
      <a-form :form='form' slot='detail'>
        <a-row>
          <a-col :span='24'>
            <a-form-item label='参数名称' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <a-input v-decorator="['name',validatorRules.name]" placeholder='请输入参数名称' :allowClear='true' autocomplete='off'/>
            </a-form-item>
          </a-col>
          <a-col :span='24'>
            <a-form-item label='参数项' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <a-input v-decorator="['item',validatorRules.item]" placeholder='请输入参数项' :allowClear='true' autocomplete='off'/>
            </a-form-item>
          </a-col>
          <a-col :span='24'>
            <a-form-item label='参数默认值' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <a-input v-decorator="['defaultValue',validatorRules.defaultValue]" placeholder='请输入默认值' :allowClear='true' autocomplete='off'/>
            </a-form-item>
          </a-col>
          <a-col :span='24'>
            <a-form-item label='默认值是否可编辑' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <a-radio-group  v-decorator="['isEditable', { initialValue: '0' }]">
                <a-radio value="0">否</a-radio>
                <a-radio value="1">是</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
          <a-col :span='24'>
            <a-form-item label='描述' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <a-textarea
                v-decorator="['description',validatorRules.description]"
                :auto-size="{ minRows: 2, maxRows: 5 }"
                placeholder='请输入描述'
                :allowClear='true'
                autocomplete='off'
              ></a-textarea>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import pick from 'lodash.pick'
import JFormContainer from '@/components/jeecg/JFormContainer'
import JDictSelectTag from '@/components/dict/JDictSelectTag'
import {ValidateOptionalFields,ValidateRequiredFields} from '@/utils/rules.js'
export default {
  name: 'FunctionDefinitionForm',
  components: {
    JFormContainer,
    JDictSelectTag,
  },
  props: {
    // 流程表单data
    formData: {
      type: Object,
      default: () => {
      },
      required: false
    },
    // 表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false
    },
    // 表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    },
  },
  data() {
    return {
      form: this.$form.createForm(this),
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 7 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 14 }
      },
      confirmLoading: false,
      validatorRules: {
        name: {
          rules: [
            { required: true,min: 2, max: 50, validator: this.fieldValidate }
          ]
        },
        item: {
          rules: [
            { required: true,min: 2, max: 100, validator: this.fieldValidate }
          ]
        },
        defaultValue: {
          rules: [
            {
              required: false,
              validator: (rule, value, callback) => ValidateOptionalFields(rule, value, callback, '参数默认值', 30)
            }
          ]
        },
        description: {
          rules: [
            {
              required: false,
              validator: (rule, value, callback) => ValidateOptionalFields(rule, value, callback, '描述', 200)
            },
          ]
        }
      },
      parameterIndex: 0,
      parameterList: [],
      tips: {
        name:'参数名称',
        item: '参数项',
      }
    }
  },
  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    }
  },

  methods: {
    add(index, parameterList) {
      this.edit({}, index, parameterList)
    },

    edit(record, index, parameterList) {
      this.parameterList = parameterList
      this.parameterIndex = index
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(
          pick(
            this.model,
            'name',
            'item',
            'defaultValue',
            'isEditable',
            'description'
          ))
      })
    },
    pClose() {
      const that = this
      that.$emit('ok')
    },

    submitForm() {
      const that = this
      // 触发表单验证
      that.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let formData = Object.assign(that.model, values)
          that.$emit('ok', formData, that.parameterIndex)
        }
      })
    },
    fieldValidate(rule, value, callback) {
      // 1. 优先处理空值情况
      if (!value) {
        callback(this.tipTextByValueAndFieldName(value, rule.fullField));
        return; // 直接终止后续检查
      }

      const trimmedValue = value.trim();
      const fieldName = this.tips[rule.fullField] || ''; // 缓存字段显示名

      // 2. 检查空白字符
      if (trimmedValue === '') {
        callback(`${fieldName}不能全为空白字符`);
        return;
      }

      if (value !== trimmedValue) {
        callback(`${fieldName}首尾不能包含空白字符！`);
        return;
      }

      // 3. 检查长度限制
      const length = value.length;
      if (rule.min && rule.max) {
        if (length < rule.min || length > rule.max) {
          callback(`${fieldName}长度应在 ${rule.min}-${rule.max} 个字符之间！`);
          return;
        }
      } else if (rule.max && length > rule.max) {
        callback(`${fieldName}长度不能超出 ${rule.max} 个字符！`);
        return;
      } else if (rule.min && length < rule.min) {
        callback(`${fieldName}长度不能少于 ${rule.min} 个字符！`);
        return;
      }

      // 4. 检查重复值
      if (this.parameterList?.length > 0) {
        const isDuplicate = this.parameterList.some((item, index) =>
          item[rule.fullField] === value && this.parameterIndex !== index
        );

        if (isDuplicate) {
          callback(this.tipTextByValueAndFieldName(value, rule.fullField));
          return;
        }
      }

      // 5. 所有验证通过
      callback();
    },
    tipTextByValueAndFieldName(value, fieldName) {
      return value ? "该" + this.tips[fieldName] + "已存在！" : "请输入" + this.tips[fieldName] + "!"
    }
  }
}
</script>