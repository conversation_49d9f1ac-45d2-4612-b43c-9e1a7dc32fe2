<template>
  <card-frame style='height: 100%'>
    <div slot='titleSlot' class='title'>
        <yq-icon class='icon' type='netAlarm'></yq-icon>
        <span class='text'>最近告警</span>
    </div>
    <template slot='extraSlot'>
      <div class='extra-wrapper'>
        <a-select
          style='border-radius: 100%;width:250px'
          v-model='queryParam.level'
          placeholder='请选择告警等级'
          :show-search='true'
          :getPopupContainer='(node) => node.parentNode'
          option-filter-prop='label'
          :allow-clear='false'
          @change='changeAlarmLevel'
        >
          <a-select-option v-for='item in alarmLevelList' :label='item.title' :value='item.value'
                           :key='item.value'>
            {{ item.title }}
          </a-select-option>
        </a-select>
      </div>
    </template>
    <!-- table区域-begin -->
    <div slot='bodySlot'>
      <a-table
        ref='table'
        bordered
        rowKey='id'
        :columns='columns'
        :dataSource='dataSource'
        :scroll='dataSource.length>0?{x:"max-content"}:{}'
        :loading='loading'
        :pagination='false'
      >
        <template slot='footer' slot-scope='text' v-if='dataSource.length>0'>
          <a-pagination
            style='background: #fff;text-align: right;margin-top: 2px'
            show-quick-jumper
            :show-size-changer='false'
            size='small'
            :hideOnSinglePage='false'
            :default-current='ipagination.current'
            :total='ipagination.total'
            :page-size='ipagination.pageSize'
            :pageSizeOptions='ipagination.pageSizeOptions'
            :show-total='(total) => `共 ${ipagination.total} 条`'
            @change='onChangePage'
            @showSizeChange='onShowSizeChange'>
          </a-pagination>
        </template>
        <template slot='tooltip' slot-scope='text'>
          <a-tooltip placement='topLeft' :title='text' trigger='hover'>
            <div class='tooltip'>
              {{ text }}
            </div>
          </a-tooltip>
        </template>
      </a-table>
    </div>
  </card-frame>
</template>
<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import yqIcon from '@comp/tools/SvgIcon'
import { getAction } from '@api/manage'
import cardFrame from '@views/networkManagement/networkReport/modules/CardFrame.vue'

export default {
  name: 'RecentAlarmList',
  mixins: [JeecgListMixin],
  components: { cardFrame, yqIcon },
  data() {
    return {
      ipagination: {
        current: 1,
        pageSize: 5,
        pageSizeOptions: ['5', '10', '15']
      },
      columns: [
        {
          title: '告警名称',
          dataIndex: 'templateName',
          customCell: () => {
            let cellStyle = 'text-align: center'
            return { style: cellStyle }
          }
        },
        {
          title: '告警时间',
          dataIndex: 'createTime',
          customCell: () => {
            let cellStyle = 'text-align: center'
            return { style: cellStyle }
          }
        },
        {
          title: '告警次数',
          dataIndex: 'repeatTimes',
          customCell: () => {
            let cellStyle = 'text-align: center'
            return { style: cellStyle }
          }
        },
        {
          title: '设备名称',
          dataIndex: 'deviceName',
          customCell: () => {
            let cellStyle = 'text-align: center'
            return { style: cellStyle }
          }
        }
      ],
      queryParam:{
        dictCode:'Network',
      },
      url: {
        list: '/net/device/alarmHistoryByLevel',
        alarmLevel: '/alarm/alarmLevel/getLevelList' //获取告警级别所有数据
      },
      alarmLevelList: [],
      disableMixinCreated:true
    }
  },
  created() {
    this.getAlarmLevelData()
  },
  methods: {
    changeAlarmLevel(level) {
      this.queryParam.alarmLevel = level
      this.loadData()
    },
    onChangePage(pageNumber, pageSize) {
      this.ipagination.pageSize = pageSize
      this.ipagination.current = pageNumber
      this.loadData()
    },
    onShowSizeChange(current, pageSize) {
      this.ipagination.pageSize = pageSize
      this.ipagination.current = current
      this.loadData()
    },
    /*获取告警级别内容*/
    getAlarmLevelData() {
      let that = this
      getAction(that.url.alarmLevel).then((res) => {
        if (res.success) {
          this.alarmLevelList = res.result
          if(this.alarmLevelList.length>0){
            this.queryParam.level=this.alarmLevelList[0].value
            this.loadData()
          }
        }
      })
    },
    loadData(arg) {
      if (!this.url.list) {
        this.$message.error('请设置url.list属性!')
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1
      }
      var params = this.getQueryParams() //查询条件
      this.loading = true
      getAction(this.url.list, params).then((res) => {
        if (res.success) {
          this.dataSource = res.result.records || res.result
          if (this.dataSource.length < 9) {
            this.clientHeight = false
          }
          this.ipagination.total = res.result.total ? res.result.total:0
        }
        if (res.code === 510) {
          this.$message.warning(res.message)
        }
        this.loading = false
      })
    },
  }
}
</script>

<style scoped lang='less'>
/*@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';*/

/*::v-deep .ant-card-head-wrapper {
  height: 100%;

  .ant-card-head-title {
    height: 100%;

    .title {
      height: 100%;
      display: flex;
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
      margin-top: 2px;
      align-items: center;
    }

    !*.extra-wrapper {

    }*!
  }
}*/

/*::v-deep  .ant-select-selection{
  border-radius:16px !important;
}

::v-deep .ant-pagination.mini .ant-pagination-options-quick-jumper input {
  border-radius: 16px;
}
::v-deep  .ant-pagination-item-active {
 // border-radius: 16px;
  border: none !important;
}*/
::v-deep .ant-table-thead > tr > th, .ant-table-tbody > tr > td {
  padding: 9px 8px !important;
}
::v-deep .ant-table-tbody .ant-table-row td {
  padding-top: 9px !important;
  padding-bottom: 9px !important;
}
::v-deep .ant-table-footer {
  background: #fff;
  border: none !important;
  margin-top: 1px;
  padding: 12px 12px 1.5px;
}
</style>