::v-deep .ant-modal-wrap {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1000;
  overflow: hidden;
  overflow-y: auto !important;
  outline: 0;
  -webkit-overflow-scrolling: touch;
}
/*::v-deep .ant-modal {
  margin: 0;
  padding: 18px 18px;
}*/

/*::v-deep .ant-modal-body {
  padding: 24px 48px;
 !* max-height: calc(100vh - 55px - 55px - 48px);
  overflow-y:auto*!
}*/
.tableModal{
  .ant-modal-body {
    padding: 24px 48px !important;
    max-height: calc(100vh - 55px - 55px - 48px) !important;
    overflow-y:auto
  }
}
.j-modal-box.fullscreen {
  margin: 0 !important;
  max-width: 100vw !important;

  ::v-deep .ant-modal {
    max-width: 100vw !important;
    margin: 0;
    padding-bottom: 0px;
     .ant-modal-body {
      max-height: calc(100vh - 55px - 55px);
    }
  }
}
/*@media (max-width: 1212px),(max-width: 1012px),(max-width: 912px),(max-width: 812px),(max-width: 779px),(max-width: 587px),(max-width: 532px),(max-width: 512px) {*/
@media (min-width: 480px){
  ::v-deep .ant-modal {
    max-width: calc(100vw - 12px) !important;
  }
}

::v-deep .two-words > div > label {
  letter-spacing: 4px;
}
::v-deep .two-words > div > label::after {
  letter-spacing: 0px;
}