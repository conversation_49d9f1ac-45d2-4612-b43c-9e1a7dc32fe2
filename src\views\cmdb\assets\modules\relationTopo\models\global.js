export const globalGridAttr = {
  //画布配置
  type: 'mesh',
  size: 1,
  color: '#e5e5e5',
  thickness: 1,
  colorSecond: '#d0d0d0',
  thicknessSecond: 1,
  factor: 4,
  bgColor: '#e5e5e5',
  showImage: true,
  repeat: 'Repeat',
  angle: 30,
  position: 'center',
  bgSize: '100% 100%',
  opacity: 1,
  bgimg:'',

  //边配置
  stroke: '#5F95FF',
  strokeWidth: 1,
  connector: 'normal',
  label: '',

  //节点配置
  nodeLabel: '',
  nodeStrokeWidth: 1,
  nodeFill: '#ffffff',
  nodeFontSize: 12,
  nodeColor: '#080808',
  nodeUsers: '',
  portGroup:'top',
  nodeX:'',//x坐标
  nodeY:'',//y坐标
  nodeWidth:'',//节点宽度
  nodeHeight:'',//节点高度
  nodeImg:'',//节点图片
  nodeIp:'',//节点ip
  productModel:'',//产品类型
  productName:'',//产品名称
  manufacturer:'',//制造公司
  imgList:[]
}


export const globalGridAttr2 = {
  //画布配置
  type: 'mesh',
  size: 1,
  color: '#e5e5e5',
  thickness: 1,
  colorSecond: '#d0d0d0',
  thicknessSecond: 1,
  factor: 4,
  bgColor: '#e5e5e5',
  showImage: true,
  repeat: 'Repeat',
  angle: 30,
  position: 'center',
  bgSize: '100% 100%',
  opacity: 1,
  bgimg:'',

  //边配置
  stroke: '#5F95FF',
  strokeWidth: 1,
  connector: 'Rounded',
  label: '',

  //节点配置
  nodeLabel: '',
  nodeStrokeWidth: 1,
  nodeFill: '#ffffff',
  nodeFontSize: 12,
  nodeColor: '#080808',
  nodeUsers: '',
  portGroup:'top',
  nodeX:'',//x坐标
  nodeY:'',//y坐标
  nodeWidth:'',//节点宽度
  nodeHeight:'',//节点高度
  nodeImg:'',//节点图片
  nodeIp:'',//节点ip
  productModel:'',//产品类型
  productName:'',//产品名称
  manufacturer:'',//制造公司
  imgList:[]
}

