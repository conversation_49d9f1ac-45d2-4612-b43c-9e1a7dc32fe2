<template>
  <a-row :gutter='10' style='height: 100%;' class='vScroll zhl zhll'>
    <a-col style='width:100%;height: 100%;display: flex;flex-direction: column'>
      <!-- 查询区域 -->
      <a-card
        :bordered='false'
        :bodyStyle="{paddingBottom:'0'}"
        class='card-style'>
        <div class='table-page-search-wrapper'>
        <!-- 搜索区域 -->
        <a-form layout="inline" v-bind='formItemLayout'>
          <a-row :gutter='24' ref='row'>
            <a-col :span='spanValue'>
              <a-form-item label="资产类型">
                <a-select
                  :getPopupContainer="(node) => node.parentNode"
                  placeholder="请选择"
                  @change="handleChange"
                  v-model="queryParam.assetType"
                  :allowClear="true"
                >
                  <a-select-option v-for="(item, index) in AssetType" :key="index" :value="item.value">{{
                    item.text
                  }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span='spanValue'>
              <a-form-item label="资产状态">
                <a-select
                  :getPopupContainer="(node) => node.parentNode"
                  placeholder="请选择"
                  @change="handleChange"
                  v-model="queryParam.assetStatus"
                  :allowClear="true"
                >
                  <a-select-option v-for="(item, index) in QueryStatus" :key="index" :value="item.value">{{
                    item.text
                  }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span='spanValue'>
              <a-form-item label="添加方式">
                <a-select
                  :getPopupContainer="(node) => node.parentNode"
                  placeholder="请选择"
                  v-model="queryParam.addType"
                  @change="handleChange"
                  :allowClear="true"
                >
                  <a-select-option value="1">新增</a-select-option>
                  <a-select-option value="2">原有</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>

            <a-col :span='spanValue' v-show="toggleSearchStatus">
              <a-form-item label="质保时间">
                <a-range-picker @change="onTimeChange" v-model="fromToDate"  class='a-range-picker-choice-date'>
                  <template slot="dateRender" slot-scope="current">
                    <div class="ant-calendar-date" :style="getCurrentStyle(current)">
                      {{ current.date() }}
                    </div>
                  </template>
                </a-range-picker>
              </a-form-item>
            </a-col>

            <a-col :span='spanValue' v-show="toggleSearchStatus">
              <a-form-item label="归属地">
                <a-input placeholder="请输入" :maxLength="maxLength" v-model="queryParam.region" :allowClear="true"/>
              </a-form-item>
            </a-col>

            <a-col :span='spanValue' v-show="toggleSearchStatus" >
              <a-form-item label="单位">
                <a-input
                  :maxLength="maxLength"
                  placeholder="请输入"
                  v-model="queryParam.userUnit"
                  :allowClear="true"
                ></a-input>
              </a-form-item>
            </a-col>

            <a-col :span='spanValue' v-show="toggleSearchStatus">
              <a-form-item label="关键字">
                <a-input
                  :maxLength="maxLength1"
                  placeholder="资产名称/ip地址"
                  v-model="queryParam.queryKeywords"
                  :allowClear="true"
                ></a-input>
              </a-form-item>
            </a-col>

            <a-col :span='colBtnsSpan()'>
              <span class='table-page-search-submitButtons'
                    :style="toRight && { float: 'right', overflow: 'hidden' } || {} ">
                <a-button type="primary" class='btn-search btn-search-style' @click="searchQuery">查询</a-button>
                <a-button class='btn-reset btn-reset-style' @click="mySearchReset">重置</a-button>
               <a v-if="isVisible" class='btn-updown-style' @click="doToggleSearch">
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
      </a-card>

      <a-card :bordered='false'
              style='width:100%;flex: auto'>
        <div class='div-table-container'>
          <!-- 按钮区域 -->
          <div class="table-operator table-operator-style">
            <a-button @click="handleAdd">新增</a-button>
            <a-button @click="handleExportXls('资产台账导出表')">导出</a-button>
            <a-upload
              name="file"
              :showUploadList="false"
              :multiple="false"
              :headers="tokenHeader"
              :action="importExcelUrl"
              @change="handleImportExcel"
            >
              <a-button>导入</a-button>
            </a-upload>
            <a-button @click="handleTemplateXls()">下载模版</a-button>
            <a-dropdown v-if="selectedRowKeys.length > 0">
              <a-menu slot="overlay" style='text-align: center'>
                <a-menu-item key="1" @click="batchDel">删除</a-menu-item>
                <a-menu-item @click="batchChangeStatus()">修改资产状态</a-menu-item>
              </a-menu>
              <a-button>
                批量操作
                <a-icon type="down" />
              </a-button>
            </a-dropdown>
          </div>

        <!-- table区域 -->
        <a-table
          ref="table"
          bordered
          rowKey="id"
          :columns="columns"
          :dataSource="dataSource"
          :scroll='dataSource.length > 0 ? { x:"max-content"} : {}'
          :pagination="ipagination"
          :loading="loading"
          :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          @change="handleTableChange"
        >
          <span slot="name" slot-scope="text, record">
            <a @click="handleAssetsManagementView(record.id)" style="color: #409eff">{{ text }}</a>
          </span>

          <span slot="assetType" slot-scope="text, record">
            <span v-for="dict in AssetType" v-if="dict.value == record.assetType">
              {{ dict.text }}
            </span>
          </span>

          <span slot="addType" slot-scope="text, record">
            <span v-if="record.addType == '1'">
              {{ '新增' }}
            </span>
            <span v-if="record.addType == '2'">
              {{ '原有' }}
            </span>
          </span>
          <span slot="assetStatus" slot-scope="text, record">
            <span v-for="dict in QueryStatus" v-if="dict.value == record.assetStatus">
              {{ dict.text }}
            </span>
          </span>
          <template slot="region" slot-scope="text, record"  >
           <span v-for="dict in sysAreaList" v-if="dict.id == record.region">
             <a-tooltip placement='topLeft' :title='dict.text' trigger='hover'>
            <div class='tooltip'>
              {{ dict.text }}
            </div>
             </a-tooltip>
           </span>
          </template>
          <span slot="action" slot-scope="text, record" class="caozuo">
            <a @click="handleEdit(record)">编辑</a>
            <a-divider type="vertical" />
            <a-dropdown>
              <a class="ant-dropdown-link"> 更多 <a-icon type="down" /> </a>
              <a-menu slot="overlay">
                <a-menu-item>
                  <a href="javascript:;" style="color: #409eff" @click="handleChangeStatus(record.id)">修改资产状态</a>
                </a-menu-item>
                <a-menu-item>
                  <a href="javascript:;" style="color: #409eff" @click="handleChangeInfo(record.id)">修改变更信息</a>
                </a-menu-item>
                <a-menu-item>
                  <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                    <a style="color: #409eff">删除</a>
                  </a-popconfirm>
                </a-menu-item>
              </a-menu>
            </a-dropdown>
          </span>
          <template slot='tooltip' slot-scope='text'>
            <a-tooltip placement='topLeft' :title='text' trigger='hover'>
              <span class='tooltip'>
                {{ text }}
              </span>
            </a-tooltip>
          </template>
        </a-table>
      </div>
      </a-card>

    <!----------------------------资产基本信息编辑 -------------------------->
    <AssetsManagementModal ref="modalForm" @ok="modalFormOk"></AssetsManagementModal>
    <!----------------------------资产状态修改 -------------------------->
    <AssetsStatusEdit ref="AssetsStatusEdit" @ok="modalFormOk"></AssetsStatusEdit>
    <!----------------------------资产信息详情 -------------------------->
    <AssetsManagementView ref="AssetsManagementView" @ok="modalFormOk"></AssetsManagementView>
    <change-info-edit-modal ref="ChangeInfoEdit"></change-info-edit-modal>
    <!-- 下载模版 -->
    <iframe id="download" style="display: none"></iframe>
    </a-col>
  </a-row>
</template>
<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import AssetsManagementModal from './modules/AssetsManagementModal'
import AssetsStatusEdit from './modules/AssetsStatusEdit'
import { ajaxGetDictItems } from '@api/api'
import AssetsManagementView from './modules/AssetsManagementView'
import ChangeInfoEditModal from './modules/ChangeInfoEditModal'
import { getAction } from '@/api/manage'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'

export default {
  name: 'AssetsManagement',
  mixins: [JeecgListMixin,YqFormSearchLocation],
  components: {
    AssetsManagementModal,
    AssetsStatusEdit,
    AssetsManagementView,
    ChangeInfoEditModal,
  },
  data() {
    return {
      maxLength:50,
      maxLength1:100,
      formItemLayout: {
        labelCol: {
          style: 'width:90px'
        },
        wrapperCol: {
          style: 'width:calc(100% - 90px)'
        }
      },
      // 表头
      columns: [
        {
          title: '资产编号',
          dataIndex: 'assetNo',
          width:160,
          align: 'center',
          fixed: 'left',
        },
        {
          title: '资产名称',
          dataIndex: 'assetName',
          scopedSlots: { customRender: 'name' },
          customCell: () => {
            let cellStyle = 'text-align: center; min-width: 100px;max-width:300px'
            return { style: cellStyle }
          },
        },
        {
          title: '资产类型',
          dataIndex: 'assetType',
          scopedSlots: { customRender: 'assetType' },
          customCell: () => {
            let cellStyle = 'text-align: center; min-width: 100px;max-width:300px'
            return { style: cellStyle }
          },
        },
        {
          title: '资产状态',
          dataIndex: 'assetStatus',
          scopedSlots: { customRender: 'assetStatus' },
          customCell: () => {
            let cellStyle = 'text-align: center; min-width: 100px;max-width:300px'
            return { style: cellStyle }
          },
        },
        {
          title: '添加方式',
          dataIndex: 'addType',
          scopedSlots: { customRender: 'addType' },
          customCell: () => {
            let cellStyle = 'text-align: center; min-width: 100px;max-width:300px'
            return { style: cellStyle }
          },
        },
        {
          title: '归属地',
          dataIndex: 'region',
          scopedSlots: { customRender: 'region' },
          customCell: () => {
            let cellStyle = 'text-align: left; min-width: 100px;max-width:400px'
            return { style: cellStyle }
          },
        },
        {
          title: '单位',
          dataIndex: 'userUnit',
          scopedSlots: { customRender: 'tooltip' },
          customCell: () => {
            let cellStyle = 'text-align: left; min-width: 100px;max-width:400px'
            return { style: cellStyle }
          },
        },
        {
          title: '使用人',
          dataIndex: 'userid',
          customCell: () => {
            let cellStyle = 'text-align: center; min-width: 100px;max-width:300px'
            return { style: cellStyle }
          },
        },
        {
          title: '保修人',
          dataIndex: 'warranties',
          customCell: () => {
            let cellStyle = 'text-align: center; min-width: 100px;max-width:300px'
            return { style: cellStyle }
          },
        },
        {
          title: '保修单位',
          dataIndex: 'warrantyUnit',
          scopedSlots: { customRender: 'tooltip' },
          customCell: () => {
            let cellStyle = 'text-align: left; min-width: 100px;max-width:400px'
            return { style: cellStyle }
          },
        },
        {
          title: 'ip地址',
          dataIndex: 'ipAddress',
          customCell: () => {
            let cellStyle = 'text-align: center; min-width: 100px;max-width:300px'
            return { style: cellStyle }
          },
        },
        {
          title: '质保开始日期',
          dataIndex: 'warrantyStart',
          customCell: () => {
            let cellStyle = 'text-align: center; min-width: 100px;max-width:300px'
            return { style: cellStyle }
          },
        },
        {
          title: '到保日期',
          dataIndex: 'warrantyEnd',
          customCell: () => {
            let cellStyle = 'text-align: center; min-width: 100px;max-width:300px'
            return { style: cellStyle }
          },
        },
        {
          title: '操作',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' },
          width: 160,
          align:'center',
          fixed: 'right',
        },
      ],
      url: {
        list: '/ledger/ledgerManage/list',
        delete: '/ledger/ledgerManage/delete',
        deleteBatch: '/ledger/ledgerManage/deleteBatch',
        exportXlsUrl: '/ledger/ledgerManage/exportXls',
        importExcelUrl: '/ledger/ledgerManage/importExcel',
        downloadTemplateXlsUrl: '/ledger/ledgerManage/downloadTemplate',
        getSysAreaList: '/sys/dict/getSysAreaList',
      },
      //资产状态
      QueryStatus: [],
      //资产类型
      AssetType: [],
      fromToDate: [], //质保开始时间范围
      sysAreaList: [],
    }
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
    downloadTemplateXlsUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.downloadTemplateXlsUrl}`
    },
  },
  mounted() {
    this.getQueryStatus('asset_queryStatus')
    this.getQueryAssetType('asset_type')
    this.getSysAreaList()
  },
  methods: {
    //excel模板
    handleTemplateXls() {
      const path = this.downloadTemplateXlsUrl
      document.getElementById('download').src = path
    },
    //刷新
    Refresh() {},
    mySearchReset() {
      this.fromToDate = []
      this.queryParam = {}
      this.loadData(1)
    },
    handleChange(value) {
    },
    onTimeChange(date, dateString) {
      //查询质保开始时间范围
      this.queryParam.queryWarrantyStartTimeFrom = dateString[0]
      this.queryParam.queryWarrantyStartTimeTo = dateString[1]
    },
    //日期调用方法
    getCurrentStyle(current, today) {
      const style = {}
      if (current.date() === 1) {
        style.border = '1px solid #1890ff'
        style.borderRadius = '50%'
      }
      return style
    },
    //单个修改资产状态
    handleChangeStatus(id) {
      this.$refs.AssetsStatusEdit.edit(id)
      this.$refs.AssetsStatusEdit.title = '修改资产状态'
      this.$refs.AssetsStatusEdit.disableSubmit = false
    },
    //修改变更信息
    handleChangeInfo(id) {
      this.$refs.ChangeInfoEdit.loadData(id)
      this.$refs.ChangeInfoEdit.title = '修改资产状态变更信息'
      this.$refs.ChangeInfoEdit.disableSubmit = false
    },
    //批量修改资产状态
    batchChangeStatus: function () {
      if (this.selectedRowKeys.length <= 0) {
        this.$message.warning('请至少选择一条记录！')
        return
      } else {
        var ids = ''
        for (var a = 0; a < this.selectedRowKeys.length; a++) {
          ids += this.selectedRowKeys[a] + ','
        }
        this.$refs.AssetsStatusEdit.batchEdit(ids)
        this.$refs.AssetsStatusEdit.title = '批量修改资产状态'
        this.$refs.AssetsStatusEdit.disableSubmit = false
      }
    },

    //详情页面
    handleAssetsManagementView(id) {
      this.$refs.AssetsManagementView.show(id)
      this.$refs.AssetsManagementView.title = '资产详情'
      this.$refs.AssetsManagementView.disableSubmit = false
    },
    //字典获取选择框内容--------------------------------------------
    getDicData() {
      this.getQueryStatus('asset_queryStatus')
      this.getQueryAssetType('asset_type')
    },
    getQueryStatus(code) {
      let that = this
      ajaxGetDictItems(code, null).then((res) => {
        if (res.success) {
          that.QueryStatus = res.result
        } else {
          that.$message.error('资产状态字典信息获取失败')
        }
      })
    },
    getQueryAssetType(code) {
      let that = this
      ajaxGetDictItems(code, null).then((res) => {
        if (res.success) {
          that.AssetType = res.result
        } else {
          that.$message.error('资产类型字典信息获取失败')
        }
      })
    },
    getSysAreaList() {
      getAction(this.url.getSysAreaList, null).then((res) => {
        if (res.success) {
          this.sysAreaList = res.result
        }
      })
    },
    //字典获取选择框内容-----------------------------------------end
    //属地---------------------------------------------
    onChangeCascader(value) {
    },
    //属地---------------------------------------------end
  }
}
</script>
<style lang="less" scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
</style>
