<template>
  <div style="height: 100%">
    <a-row class="rowBox">
      <a-col :lg='24' :xl='12' class="topCol">
        <div class="chart" id="trend" v-if="showTrendLineState===2"></div>
        <a-spin class="chart-spin" :spinning="showTrendLineState===1" v-else-if="showTrendLineState===1"></a-spin>
        <div class="chart-empty" v-else><a-empty></a-empty></div>
      </a-col>
      <a-col  :lg='24' :xl='12' class="middleCol">
        <div class="chart" id="allocation" v-if="showAllocationState==2"></div>
        <a-spin class="chart-spin" :spinning="showAllocationState===1" v-else-if="showAllocationState===1"></a-spin>
        <div class="chart-empty" v-else><a-empty></a-empty></div>
      </a-col>
      <a-col  :lg="24" :xl="24" class="bottomCol">
        <div class="chart" id="alarm" v-if="showAlarmState===2"></div>
        <a-spin class="chart-spin" :spinning="showAlarmState===1" v-else-if="showAlarmState===1"></a-spin>
        <div class="chart-empty" v-else><a-empty></a-empty></div>
      </a-col>
    </a-row>
  </div>
</template>

<script>
  import echarts from 'echarts'
  import { widthPixel } from '@views/statsCenter/com/calculatePixel'
  import { ref, reactive, watch, onMounted, markRaw } from 'vue';
  import {
    getAction
  } from '@/api/manage'
  export default {
    props: ['treeFlag'],
    data() {
      return {
        showTrendLineState:0,//0无数据，1正在加载，2数据加载完成有数据
        showAllocationState:0,//0无数据，1正在加载，2数据加载完成有数据
        showAlarmState:0,//0无数据，1正在加载，2数据加载完成有数据
        eChartsTitle: '',
        url: {
          queryAnalysisNum: '/devops/ip/analysis/queryAnalysisNum',
          querySegmentAnalysis: '/devops/ip/analysis/querySegmentAnalysis',
          queryAnalysisAlarm: '/devops/ip/analysis/queryAnalysisAlarm'
        }
      }
    },
    watch: {
      treeFlag: function(n) {
        this.showTrendLineState = 0
        this.showAllocationState = 0
        this.showAlarmState = 0
        if (n.id) {
          this.getTrendData(n.id, n.type)
          this.getAllocationData(n.id, n.type)
          this.getAlarmData(n.id, n.type)
          this.eChartsTitle = n.title
        }
      }
    },
    mounted() {
    },
    methods: {
      //计算图表网格偏移
      setGridLeft(strLen) {
        // let left=widthPixel(12)
        let left = 12
        let offset0 = left * (strLen)+24
        let mod = offset0 % 2
        let offset = mod == 0 ? offset0 / 2 : (offset0 + mod) / 2
        return offset
      },
      getTrendData(id, type) {
        this.showTrendLineState = 1
        getAction(this.url.queryAnalysisNum, {
          id: id,
          type: type
        }).then((res) => {
          if (res.success && res.result && res.result.length > 0) {
            this.showTrendLineState = 2
            this.$nextTick(() => {
              this.getTrend(res.result.reverse())
            })
          } else {
            this.showTrendLineState = 0
          }
        }).catch((err)=>{
          this.$message.error(err.message)
          this.showTrendLineState = 0
        })
      },
      getTrend(data) {
        let dataOut = []
        let dataBack = []
        let time = []
        data.forEach((ele) => {
          dataOut.push(ele.distributeNum)
          dataBack.push(ele.recoveryNum)
          time.push(ele.date)
        })
        const max0 = dataOut.reduce((a, b) => Math.max(a, b));
        const max1 = dataBack.reduce((a, b) => Math.max(a, b));
        const max =Math.max( max0 , max1) + ''
        let length = max.length
        let mod = length % 3
        let quotient = Math.ceil(length / 3)
        let offsetLeft = 0
        if (max < 5) {
          offsetLeft = this.setGridLeft(3)
        } else {
          if (mod == 0) {
            offsetLeft = this.setGridLeft(length+ quotient - 1)
          } else {
            offsetLeft = this.setGridLeft(length + quotient)
          }
        }
        let colorList = ["#5B8FF9", "#5AD8A6"]
        let myChart = this.$echarts.init(document.getElementById('trend'))
        myChart.setOption({
          title: {
            show: true,
             //text:this.eChartsTitle + "IP分配 / 回收趋势",
            text: '{a|}'+`{b|${"  "+this.eChartsTitle + "IP分配 / 回收趋势"}}`,
            textStyle: {
              rich: {
                a: {
                  fontSize: 14,
                  color: '#1E3674',
                  fontWeight:700,
                  borderColor:'#1E3674',
                  borderWidth:3,
                },
                b: {
                  color: '#000000',
                  fontSize: 14,
                  fontWeight:'bold',
                },
              }
            },
            left: "left",
            top: "0px",
          },
          color: colorList,
          legend: {
            center: true,
            left: "left",
            top: 32,
            data: ["已分配", "已回收"],
            itemWidth: 12,
            itemHeight: 12,
            itemGap: 16,
            icon: "rect",
            textStyle: {
              color: "rgb(0, 0, 0,0.6)",
              fontSize: "12",
            }
          },
          grid: {
            top: 80,
            right: 0,
            left: offsetLeft,
            bottom: 24
          },
          tooltip: {
            show: true,
            trigger: 'axis',
            transitionDuration: 0, //echart防止tooltip的抖动
            axisPointer: {
              type: 'line',
              lineStyle: {
                type: 'dotted',
                color: 'rgba(0,0,0,0.58)'
              }
            },
            backgroundColor: "#fff",
            padding: 10,
            borderColor: 'rgba(0,0,0,0.1)',
            borderWidth: 1,
            borderRadius: 2,
            textStyle: {
              color: 'rgba(0,0,0,0.85)',
              fontSize: 12
            },
            formatter: (params) => {
              let txt = ''
              if (params.length > 0) {
                let hr = `<div style='display: block;height:12px'></div>`
                txt = params[0].name + "</br>" + hr
                for (let i = 0; i < params.length; i++) {
                  let dataTxt = `<span style='display: inline-block;width: 7px;height:7px;background:${colorList[i]} ;border-radius: 100%;margin-right: 2px'></span>`
                  txt += dataTxt + " " + params[i].seriesName + "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" + params[i].value
                  if (i != params.length - 1) {
                    txt += "</br>"
                  }
                }
              }
              return txt
            },
          },
          xAxis: [{
            type: "category",
            axisTick: {
              show: false
            },
            axisLine: {
              show: true,
              lineStyle: {
                type: "solid",
                color: "rgb(0, 0, 0,0.15)"
              }
            },
            // X轴上的 字体样式
            axisLabel: {
              inside: false,
              color: "rgb(0, 0, 0,0.85)",
              fontWeight: "normal",
              fontSize: "12",
              lineHeight: 22
            },
            data: time
          }],
          yAxis: [{
            type: "value",
            name: "",
            scale:true,
            splitNumber: 5,
            axisLabel: {
              color: "rgb(0, 0, 0,0.85)",
              fontSize: "12",
            },
            splitLine: {
              show: true, // 是否显示 Y轴线条
              lineStyle: {
                type: "solid",
                color: "rgb(0, 0, 0,0.15)"
              }
            },
            axisLine: {
              show: false, // 是否显示 Y轴刻度线
              lineStyle: {
                type: "dashed",
                color: "rgb(47, 105, 212);"
              }
            },
            axisTick: {
              show: false
            },
          }],
          series: [{
            name: ["已分配"],
            type: "line",
            symbol: 'emptyCircle',
            smooth: false,
            showSymbol: true,
            areaStyle: {
              color: '#d6e3fd'
            },
            label: {
              show: false,
              position: 'top',
            },
            data: dataOut
          }, {
            name: ["已回收"],
            type: "line",
            symbol: 'emptyCircle',
            smooth: false,
            showSymbol: true,
            areaStyle: {
              color: '#d6f5e9'
            },
            label: {
              show: false,
              position: 'top',
            },
            data: dataBack
          }]
        })
        window.addEventListener('resize', () => {
          myChart.resize()
        })
      },
      getAllocationData(id, type) {
        this.showAllocationState=1
        getAction(this.url.querySegmentAnalysis, {
          id: id,
          type: type
        }).then((res) => {
          if (res.success&&res.result&&res.result.length>0){
            this.showAllocationState=2
            this.$nextTick(()=>{
              this.getAllocation(res.result)
            })
          }else {
            this.showAllocationState=0
          }
        }).catch((err)=>{
          this.showAllocationState=0
          this.$message.error(err.message)
        })
      },
      getAllocation(data) {
        if (data && data.length > 0) {
          let xData = []
          let yData = []
          let grayBar = []
          data.forEach((ele) => {
            xData.push(ele.segmentName)
            yData.push(ele.useRate)
          })
          for (let i = 0; i < yData.length; i++) {
            grayBar.push(100)
          }

          let myChart = this.$echarts.init(document.getElementById('allocation'))
          myChart.setOption({
            title: {
              show: true,
              //text:this.eChartsTitle + "分配率TOP10",
              text: '{a|}'+`{b|${"  "+this.eChartsTitle + "分配率TOP10"}}`,
              textStyle: {
                rich: {
                  a: {
                    fontSize: 14,
                    color: '#1E3674',
                    fontWeight:700,
                    borderColor:'#1E3674',
                    borderWidth:3,
                  },
                  b: {
                    color: '#000000',
                    fontSize: 14,
                    fontWeight:'bold',
                  },
                }
              },
              left: "left",
              top: "0px",
            },
            grid: {
              //left: "15%",
              left: 0,
              // right: "10%",
              right: 0,
              bottom: 0,
              top: 32,
              containLabel: false,
            },
            tooltip: {
              show: true,
              // trigger: 'axis',
              transitionDuration: 0, //echart防止tooltip的抖动
              axisPointer: {
                type: 'line',
                lineStyle: {
                  type: 'dotted',
                  color: 'rgba(0,0,0,0.58)'
                }
              },
              backgroundColor: "#fff",
              padding: 10,
              borderColor: 'rgba(0,0,0,0.1)',
              borderWidth: 1,
              borderRadius: 2,
              textStyle: {
                color: 'rgba(0,0,0,0.85)',
                fontSize: 12
              },
              formatter: (params) => {
                let txt = ''
                if (params.value) {
                  let hr = `<div style='display: block;height:12px'></div>`
                  txt = params.seriesName + "</br>" + hr
                  let dataTxt = `<span style='display: inline-block;width: 7px;height:7px;background:#73A0FA;border-radius: 100%;margin-right: 2px'></span>`
                  txt += dataTxt + " " + params.name + "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" + params.value + "%"
                }
                return txt
              },
            },
            xAxis: [
              {
                show: false,
                max: '100',
              },
              {
                show: false,
                max: '100',
              },
            ],
            yAxis: [{
              type: "category",
              data: xData,
              axisLabel: {
                lineHeight: 0,
                fontSize: 12,
                verticalAlign: 'bottom',
                align: 'left',
                padding: [0, 0, 14, 8],
                formatter: (value) => {
                  if (value.length > 20) {
                    return value.substring(0, 20) + '...'
                  } else {
                    return value
                  }
                }
              },
              axisLine: {
                show: false,
              },
              axisTick: {
                show: false,
              },
            }],
            series: [
              {
                show: true,
                type: 'bar',
                barGap: '-100%',
                barWidth: 12, //统计条宽度
                itemStyle: {
                  normal: {
                    barBorderRadius: 6,
                    color: '#F0F1F2'
                  },
                },
                z: 1,
                tooltip: {
                  show: false
                },
                silent: true,
                data: grayBar,
              },
              {
                name: "分配率",
                show: true,
                type: "bar",
                xAxisIndex: 1,
                barWidth: 12,
                itemStyle: {
                  normal: {
                    barBorderRadius: 6,
                    color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
                      offset: 0,
                      color: '#73A0FA'
                    }, {
                      offset: 1,
                      color: '#76EABC'
                    }]),
                  },
                },
                label: {
                  show: true,
                  position: 'right',
                  fontSize: 12,
                  color: 'rgba(0,0,0,0.65)',
                  formatter: function(data) {
                    return data.data + '%';
                  },
                },
                labelLine: {
                  show: false,
                },
                z: 2,
                data: yData
              },
            ],
          })
          window.addEventListener('resize', () => {
            myChart.resize()
          })
        }
      },
      getAlarmData(id, type) {
        this.showAlarmState=1
        getAction(this.url.queryAnalysisAlarm, {
          id: id,
          type: type
        }).then((res) => {
          if (res.success&&res.result&&res.result.length>0){
            this.showAlarmState=2
            this.$nextTick(()=>{
              this.getAlarm( res.result.reverse())
            })
          }else {
            this.showAlarmState=0
          }
        }).catch((err)=>{
          this.showAlarmState=0
          this.$message.error(err.message)
        })
      },
      getAlarm(data) {
        let zeroNum = []
        let oneNum = []
        let twoNum = []
        let time = []
        let colorList = ['rgba(91,143,249,0.85)', 'rgba(90,216,166,0.85)', 'rgba(255,167,105,0.85)']
        let nameList = ['使用的IP是否已配置', 'IP对应的MAC地址是否一致', '对应的设备是否在线']
        data.forEach((ele) => {
          zeroNum.push(ele.zeroNum)
          oneNum.push(ele.oneNum)
          twoNum.push(ele.twoNum)
          time.push(ele.date)
        })

   /*     const max0 = zeroNum.reduce((a, b) => Math.max(a, b));
        const max1 = oneNum.reduce((a, b) => Math.max(a, b));
        const max2 = twoNum.reduce((a, b) => Math.max(a, b));
        const max=Math.max(max0,max1,max2)+''
        let length = max.length
        let mod = length % 3
        let quotient = Math.ceil(length / 3)
        let offsetLeft = 0
        if (max < 5) {
          offsetLeft = this.setGridLeft(3)
        } else {
          if (mod == 0) {
            offsetLeft = this.setGridLeft(length+ quotient - 1)
          } else {
            offsetLeft = this.setGridLeft(length + quotient)
          }
        }*/

        let myChart = this.$echarts.init(document.getElementById('alarm'))
        myChart.setOption({
          title: {
            show: true,
            //text:this.eChartsTitle + "告警统计分析",
            text: '{a|}'+`{b|${"  "+this.eChartsTitle + "告警统计分析"}}`,
            textStyle: {
              rich: {
                a: {
                  fontSize: 14,
                  color: '#1E3674',
                  fontWeight:700,
                  borderColor:'#1E3674',
                  borderWidth:3,
                },
                b: {
                  color: '#000000',
                  fontSize: 14,
                  fontWeight:'bold',
                },
              }
            },
            left: "left",
            top: "0px",
          },
          tooltip: {
            show: true,
            trigger: 'axis',
            transitionDuration: 0, //echart防止tooltip的抖动
            axisPointer: {
             /* type: 'line',
              lineStyle: {
                // color: 'rgba(91,192,249,0.10)',
                type: 'dotted',
                color: 'rgba(0,0,0,0.58)'
              }*/
              type: 'shadow',
              shadowStyle: {
                color: 'rgba(91,192,249,0.10)',
              }
            },
            backgroundColor: "#fff",
            padding: 10,
            borderColor: 'rgba(0,0,0,0.1)',
            borderWidth: 1,
            borderRadius: 2,
            textStyle: {
              color: 'rgba(0,0,0,0.85)',
              fontSize: 12
            },
            formatter: (params) => {
              let txt = ''
              //tooltip类型为item时可用
             /* if (params.dataIndex) {
                let hr = `<div style='display: block;height:12px'></div>`
                txt = params.name + "</br>" + hr
                let value0 = zeroNum[params.dataIndex]
                let value1 = oneNum[params.dataIndex]
                let value2 = twoNum[params.dataIndex]
                let lis = [{ value: value0 }, { value: value1 }, { value: value2 }]
                for (let i = 0; i < lis.length; i++) {
                  let dataTxt = `<span style='display: inline-block;width: 7px;height:7px;background:${colorList[i]} ;border-radius: 100%;margin-right: 2px'></span>`
                  txt += dataTxt + " " + nameList[i] + "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" + lis[i].value
                  if (i != lis.length - 1) {
                    txt += "</br>"
                  }
                }
              }*/
                //tooltip类型为axis时可用
              if(params&&params.length>0){
                let hr = `<div style='display: block;height:12px'></div>`
                txt = params[0].name + "</br>" + hr
                for (let i=0;i<params.length;i++){
                  let dataTxt = `<span style='display: inline-block;width: 7px;height:7px;background:${colorList[i]} ;border-radius: 100%;margin-right: 2px'></span>`
                  txt += dataTxt + " " + params[i].seriesName + "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" + params[i].value
                  if (i != params.length - 1) {
                    txt += "</br>"
                  }
                }
              }
              return txt
            }
          },
          legend: {
            data: nameList,
            right: 0,
            top: 0,
            itemWidth: 12,
            itemHeight: 12,
            itemGap: 16,
            /* center: true,
             left: "center",*/
          },
          grid: {
            left: 0,
            right: 0,
            bottom: 0,
            top: 64,
            containLabel: true,//确保标签被包含在网格中
          },
          xAxis: [{
            type: 'category',
            boundaryGap: [0,10],//控制柱状图的坐标轴两边留白还是紧贴坐标轴
            //坐标轴刻度线
            axisTick: {
              show: false
            },
            //坐标轴线
            axisLine: {
              show: false,
              lineStyle: {
                color: "rgb(0, 0, 0,0.15)",
                width: 2,
                type: "solid"
              }
            },
            axisLabel:{
              overflow:"truncate",
              align:'center',
              color:'rgb(0, 0, 0,0.85)'
            },
            //网格线
            splitLine: {
              show: true,
              lineStyle: {
                type: "solid",
                color: "rgb(0, 0, 0,0.15)"
              }
            },
            data: time
          }],
          yAxis: [{
            type: 'value',
            axisTick: {
              show: false
            },
            splitLine: {
              show: true,
              // 是否显示 Y轴线条
              lineStyle: {
                type: "dotted",
                color: "rgb(0, 0, 0,0.15)"
              }
            },
            axisLabel:{
              color:'rgb(0, 0, 0,0.85)'
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: "rgb(0, 0, 0,0.15)",
                width: 2,
                type: "solid"
              }
            },
          }],
          series: [
            {
              name: nameList[0],
              type: 'bar',
              barWidth: 9,
              barGap: '60%',//不同系列的柱间距离，为百分比（如 '30%'，表示柱子宽度的 30%），'-100%'表示两个系列的柱子重叠
              //barCategoryGap:'0%',//同一系列的柱间距离，默认为类目间距的20%，可设固定值
              itemStyle: {
                normal: {
                  color: colorList[0],
                  align:'left'
                }
              },
              label: {
                normal: {
                  show: false
                }
              },
              data: zeroNum
            },
            {
              name: nameList[1],
              type: 'bar',
              barWidth: 9,
              barGap: '60%',//不同系列的柱间距离，为百分比（如 '30%'，表示柱子宽度的 30%），'-100%'表示两个系列的柱子重叠
              //barCategoryGap:'0%',//同一系列的柱间距离，默认为类目间距的20%，可设固定值
              itemStyle: {
                normal: {
                  color: colorList[1]
                }
              },
              label: {
                normal: {
                  show: false,
                }
              },
              data: oneNum
            },
            {
              name: nameList[2],
              type: 'bar',
              barWidth: 9,
              barGap: '60%',//不同系列的柱间距离，为百分比（如 '30%'，表示柱子宽度的 30%），'-100%'表示两个系列的柱子重叠
              //barCategoryGap:'0%',//同一系列的柱间距离，默认为类目间距的20%，可设固定值
              itemStyle: {
                normal: {
                  color: colorList[2]
                }
              },
              label: {
                normal: {
                  show: false,
                }
              },
              data: twoNum
            },
          ]
        })
        window.addEventListener('resize', () => {
          myChart.resize()
        })
      },
    }
  }
</script>

<style scoped lang="less">
  .rowBox {
    height: 100%;
    min-width: 800px;

    .topCol,.middleCol {
      height: 52%;
      min-height:435px;
    }

    .bottomCol {
      height: calc(100% - 52% - 16px);
      min-height: 391px;
    }
    .flex-align{
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .chart{
      height: 100%;
      padding: 24px;
      background-color: #fff;
      border-radius: 3px;
    }
    .chart-spin{
      .chart;
       width: 100%;
      .flex-align
    }
    .chart-empty{
      .chart;
      .flex-align
    }
  }

  @media screen and (min-width: 1200px){
    .topCol{
      width: calc(50% - 8px);
      margin-right: 8px;
      margin-bottom: 16px;
    }
    .middleCol{
      width: calc(50% - 8px);
      margin-left: 8px;
      margin-bottom: 16px;
    }
  }

  @media (max-width: 1199px){
    .topCol{
      width: 100%;
      margin-right: 0px;
      margin-bottom: 16px;
    }
    .middleCol{
      width: 100%;
      margin-left: 0px;
      margin-bottom: 16px;
    }
  }
</style>