<template>
  <a-spin :spinning='confirmLoading'>
    <j-form-container :disabled='formDisabled'>
      <a-form-model ref='form' slot='detail' :model='model' :rules='validatorRules'>
        <a-row>
          <a-col v-bind='formItemLayout'>
            <a-form-model-item label='业务绑定' prop='code' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <a-select
                :getPopupContainer='node=>node.parentNode'
                :allow-clear='true'
                v-model='model.code'
                show-search
                placeholder='请选择'
                option-filter-prop='children'
              >
                <a-select-option v-for='(item, key) in codeList' :key='key' :value='item.value'
                                 :disabled='item.disabled'>
                  {{ item.text }}
                </a-select-option>
              </a-select>
              <!--              <j-dict-select-tag v-model='model.code' dictCode='code-generation-bind' placeholder='请选择业务绑定'
                                               type='list' />-->
            </a-form-model-item>
          </a-col>
          <a-col v-bind='formItemLayout'>

            <!--  规则区域          -->
            <!--         todo-weichen 页面样式     -->
            <div
              v-for='(rule, index) in model.rules'
              :key='rule.id'
              style=' margin-bottom: 8px'
            >
              <a-row>
                <a-col :md='10' :sm='9' :xs='8'>
                  <a-form-model-item
                    :prop="'type_'+index"
                    :key="'type_'+index"
                    label='编码规则'
                    :labelCol='{ xs: { span: 24 },sm: { span: 9 }, md: { span: 8 }}'
                    :wrapperCol='{ xs: { span: 24 }, sm: { span: 15 },md: { span: 16 }}'>
                    <a-select
                      :getPopupContainer='node=>node.parentNode'
                      :allow-clear='true'
                      v-model='model["type_"+index]'
                      :placeholder='model["codePlaceholder_"+index]'
                      @change='changeRule($event,index)'
                    >
                      <a-select-option v-for='(item, key) in ruleList' :key='key' :value='item.value'
                                       :disabled='item.disabled'>
                        {{ item.text }}
                      </a-select-option>
                    </a-select>
                    <!--                    <j-dict-select-tag v-model='rule.type' :val.sync='rule.ruleModel' dictCode='code-generation-rule'
                                                                               placeholder='请选择编码规则' style='width:100%' />-->
                  </a-form-model-item>
                </a-col>
                <a-col :md='12' :sm='12' :xs='13'>
                  <a-form-model-item
                    :prop="'content_'+index"
                    :key="'content_'+index"
                    label='内容'
                    :labelCol='{ xs: { span: 24 }, sm: { span: 4 }}'
                    :wrapperCol='{xs: { span: 24 },sm: { span: 20 }}'>
                    <a-select
                      :getPopupContainer='node=>node.parentNode'
                      v-if='model["type_"+index]==="date"'
                      :allow-clear='true'
                      placeholder='请选择日期格式'
                      v-model='model["content_"+index]'
                      @change='changeContent'
                    >
                      <a-select-option v-for='(item, key) in dateArr' :key='0' :value='item.key'>
                        {{ item.content }}
                      </a-select-option>
                    </a-select>

                    <a-input-number
                    v-else-if='(model["type_"+index]==="date number")||model["type_"+index]==="serial number"'
                    :min="1" :step="1" :precision='0'  :default-value="1"
                    :placeholder='model["ruleModel_" + index]'
                    style='width: 100%'
                    v-model='model["content_"+index]'
                    @change='changeContent'
                    />

                    <a-input v-else
                             :allow-clear='true'
                             :placeholder='model["ruleModel_" + index]'
                             v-model='model["content_"+index]'
                             @change='changeContent'
                    />

                  </a-form-model-item>
                </a-col>
                <a-col :md='2' :sm='3' :xs='3'>
                  <div class='delete-button'>
                    <a-button icon='delete' @click='removeRule(index)' />
                  </div>
                </a-col>
              </a-row>
            </div>
          </a-col>
          <!--            新增按钮-->
          <a-col v-bind='formItemLayout'>
            <a-form-model-item :wrapperCol='{span:24}'>
              <div class='new-button'>
                <a-button block icon='plus' type='dashed' @click='addRule' style='width: 80%'>
                  新增规则
                </a-button>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span='24' v-if='ruleInstance.length>0'>
            <a-form-model-item label='实例' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <div class='instance'>
                <span style='margin-right: 12px' v-for='(item) in ruleInstance'>
                  {{item}}
                </span>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

import { httpAction } from '@api/manage'
import { initDictOptions } from '@comp/dict/JDictSelectUtil'
import { ajaxGetDictItems, getDictItemsFromCache } from '@api/api'
import moment from 'moment'
export default {
  name: 'CodeRuleSettingForm',
  components: {},
  props: {
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    },
    hadCodeList: {
      type: Array,
      default: [],
      required: false
    }
  },
  data() {
    return {
      formItemLayout: {
        md: { span: 24 },
        sm: { span: 24 }
      },
      model: {
        rules: []
      },
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 15 }
      },
      confirmLoading: false,
      validatorRules: {
        code: [
          { required: true, message: '请选择业务绑定!', trigger: 'blur' }
        ],
        rule: [
          { required: true, message: '请输入规则!', trigger: 'blur' }
        ]
      },
      dateArr: [
        { content: '年月日', key: 'yyyyMMdd' },
        { content: '年日月', key: 'yyyyddMM' },
        { content: '月年日', key: 'MMyyyydd' },
        { content: '月日年', key: 'MMddyyyy' },
        { content: '日年月', key: 'ddyyyyMM' },
        { content: '日月年', key: 'ddMMyyyy' }
      ],
      ruleInstance:[],
      ruleInstanceLength:5,
      url: {
        add: '/CodeRuleSetting/codeRuleSetting/add',
        edit: '/CodeRuleSetting/codeRuleSetting/edit',
        queryById: '/CodeRuleSetting/codeRuleSetting/queryById'
      },
      ruleList: [],
      codeList: [],
    }
  },
  computed: {
    formDisabled() {
      return this.disabled
    }
  },
  watch: {
    hadCodeList: {
      handler(list) {
        if (list) {
          list.forEach((val) => {
            for (let i = 0; i < this.codeList.length; i++) {
              if (this.codeList[i].value === val.code) {
                this.codeList[i].disabled = true
              }
            }
          })
        }
      },
      immediate: true,
      deep: true
    },
    ruleList: {
      handler(list) {
        this.setRuleDisabled(list)
      },
      immediate: true,
      deep: true
    }
  },
  created() {
    //备份model原始值
    this.modelDefault = this.model
    this.getCodeList()
    this.getRuleList()
  },
  methods: {
    getCodeList() {
      initDictOptions('code-generation-bind').then((res) => {
        if (res.success) {
          this.codeList = res.result
          if (this.hadCodeList) {
            this.hadCodeList.forEach((val) => {
              for (let i = 0; i < this.codeList.length; i++) {
                if (this.codeList[i].value === val.code) {
                  this.codeList[i].disabled = true
                }
              }
            })
          }
        }
      })
    },
    getRuleList() {
      initDictOptions('code-generation-rule').then((res) => {
        if (res.success) {
          this.ruleList = res.result
          for (let i = 0; i < this.ruleList.length; i++) {
            this.ruleList[i].disabled = false
          }
        }
      })
    },

    changeRule(val, index) {
      this.ruleInstance=[]
      this.model['ruleModel_' + index] = '请输入内容！'
      this.model['content_' + index] = undefined
      this.setRuleDisabled(this.ruleList)
    },
    setRuleDisabled(list) {
      if (list && list.length > 0) {
        list.forEach((item) => {
          let has = false
          item.disabled = false
          if (this.model.rules && this.model.rules.length > 0) {
            for (let j = 0; j < this.model.rules.length; j++) {
              if (item.value === this.model['type_' + j]) {
                has = true
                this.model['ruleModel_' + j] = item.description
              }
            }
          }
          if (has) {
            item.disabled = has ? true : false
          }
        })
      }
      this.model = { ...this.model }
      list = [...list]
      this.validatorRules = { ...this.validatorRules }
    },
    changeContent(val) {
      this.model = { ...this.model }
      this.ruleInstance=[]
    },

    removeRule(index) {
      this.ruleInstance=[]
      for (let idx = 0; idx < this.model.rules.length; idx++) {
        if (idx > index) {
          this.model['content_' + (idx - 1)] = this.model['content_' + idx]
          this.model['type_' + (idx - 1)] = this.model['type_' + idx]

          this.model['ruleModel_' + (idx - 1)] = this.model['ruleModel_' + idx]
          this.model['codePlaceholder_' + (idx - 1)] = this.model['codePlaceholder_' + idx]
          this.model['id_' + (idx - 1)] = this.model['id_' + idx]

          this.validatorRules['type_' + (idx - 1)] = this.validatorRules['type_' + idx]
          this.validatorRules['content_' + (idx - 1)] = this.validatorRules['content_' + idx]
        }
        if (idx == this.model.rules.length - 1) {
          delete this.model['content_' + idx]
          delete this.model['type_' + idx]
          delete this.model['ruleModel_' + idx]
          delete this.model['codePlaceholder_' + idx]
          delete this.model['id_' + idx]
          delete this.validatorRules['type_' + idx]
          delete this.validatorRules['content_' + idx]
        }
      }
      this.model.rules.splice(index, 1)
      this.setRuleDisabled(this.ruleList)
    },
    addRule() {
      this.ruleInstance=[]
      this.model.rules.push({})
      let index = this.model.rules.length - 1
      this.model['type_' + index] = undefined
      this.model['content_' + index] = undefined
      this.model['ruleModel_' + index] = '请输入内容！'
      this.model['codePlaceholder_' + index] = '请选择编码规则！'
      this.model['id_' + index] = Date.now()
      this.validatorRules['type_' + index] = [{ required: true, message: '请选择编码规则!', trigger: 'blur' }]
      this.validatorRules['content_' + index] = [{ required: true, message: '请输入内容!', trigger: 'blur' }]
    },

    add() {
      this.edit(this.modelDefault)
      //this.edit({})
    },
    edit(record) {
      this.model = JSON.parse(JSON.stringify(record))
      if (this.model.rules && this.model.rules.length > 0) {
        this.model.rules.forEach((item, index) => {
          this.model['type_' + index] = item.type
          this.model['content_' + index] = item.content
          this.model['ruleModel_' + index] = item.ruleModel
          this.model['codePlaceholder_' + index] = '请选择编码规则！'
          this.model['id_' + index] = item.id
          this.validatorRules['type_' + index] = [{ required: true, message: '请选择编码规则!', trigger: 'blur' }]
          this.validatorRules['content_' + index] = [{ required: true, message: '请输入内容!', trigger: 'blur' }]
        })
      }else{
        this.model.rules = []
      }
      this.visible = true
    },
    validateDateNumber() {
      const that = this
      let selectedDate=false
      let selectedDateNumber=false
      that.model.rules.forEach((item, index) => {
        if (that.model['type_' + index] === 'date') {
          selectedDate = true
        }
        if (that.model['type_' + index] === 'date number') {
          selectedDateNumber = true
        }
      })
      return !selectedDate&&selectedDateNumber ?false:true
    },
    validateNumber(){
      const that = this
      let selectedSerialNumber=false
      let selectedDateNumber=false
      that.model.rules.forEach((item, index) => {
        if (that.model['type_' + index] === 'serial number') {
          selectedSerialNumber = true
        }
        if (that.model['type_' + index] === 'date number') {
          selectedDateNumber = true
        }
      })
      return selectedDateNumber&&selectedSerialNumber ?false:true
    },
    setRulesData() {
      let rulesData = []
      for (let i = 0; i < this.model.rules.length; i++) {
        let temp = this.model
        let param = {
          type: this.model['type_' + i],
          content: this.model['content_' + i],
          id: this.model['id_' + i]
        }
        rulesData.push(param)
      }
      this.model.rules = rulesData
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.$refs.form.validate((valid) => {
        if(this.model.rules.length===0){
          that.$message.warning('请先制定规则')
          return
        }
        if(!this.validateNumber()){
          that.$message.warning('日期序号和递增序号只能二选一')
          return
        }
        if (!this.validateDateNumber()) {
          that.$message.warning('选择了日期序号，必须选择日期')
          return
        }

        if (valid) {
          this.setRulesData()
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          httpAction(httpurl, this.model, method).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.$emit('ok')
            } else {
              that.$message.warning(res.message)
            }
          }).finally(() => {
            that.confirmLoading = false
          })
        }
      })
    },
    moment,
    generateInstance(){
      const that = this
      // 触发表单验证
      this.$refs.form.validate((valid) => {
        if(this.model.rules.length===0){
          that.$message.warning('请先制定规则')
          return
        }
        if(!this.validateNumber()){
          that.$message.warning('日期序号和递增序号只能二选一')
          return
        }

        if(!this.validateDateNumber()){
          that.$message.warning('选择了日期序号，必须选择日期')
          return
        }
        if (valid) {
          let contentData=[]
          let numberLenght=0
          let numberIndex=0
          for (let i=0;i<that.model.rules.length;i++){
             let param={}
            param.type=that.model['type_'+i]
            if(that.model['type_'+i]==='date'){
              let format=that.model['content_'+i].toUpperCase()
              param.content=(moment().format(format)).toString()
            }
            else {
              param.content=that.model['content_'+i]
              if(that.model['type_'+i].indexOf('number')!=-1){
                numberLenght=that.model['content_'+i]
                numberIndex=i
              }
            }
            contentData.push(param)
          }

          if(numberLenght!=0){
            for (let i=1;i<this.ruleInstanceLength+1;i++){
              let spliceString=''
              for(let j=0;j<contentData.length;j++){
                if(j===numberIndex){
                  spliceString+=(i.toString()).padStart(numberLenght,'0')
                }
                else {
                  spliceString+=contentData[j].content
                }
              }
              this.ruleInstance.push(spliceString)
            }
          }
        }
      }
    )}
  }
}

</script>
<style scoped lang='less'>
.new-button {
  text-align: center;
}

.delete-button {
  height: 40px;
  line-height: 40px;
  margin-left: 12px;
  margin-top: 0px;
}

.instance{
  padding: 10px;
  border-radius: 4px;
  display: flex;
  justify-content: start;
  align-items: center;
  flex-wrap: wrap;
  border: 1px solid #d9d9d9
}
@media (max-width: 575px) {
  .delete-button {
    margin-top: 29px;
  }
}

</style>