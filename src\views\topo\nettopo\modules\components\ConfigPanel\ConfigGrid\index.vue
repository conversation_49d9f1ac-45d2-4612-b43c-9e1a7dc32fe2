<template>
  <a-tabs :animated="false" v-model="activeKey">
    <a-tab-pane tab="拓扑配置" key="1">
      <div v-if="activeKey == 1" class="tab-con">
        <a-row align="middle">
          <a-col :span="6">适应视口</a-col>
          <a-col :span="18">
            <a-switch checkedChildren="开" unCheckedChildren="关" defaultChecked v-model="topoConfig.zoomFit" />
          </a-col>
        </a-row>
        <a-row align="middle" v-show='topoConfig.zoomFit'>
          <a-col :span="6">视口边距</a-col>
          <a-col :span="18">
            <a-input-number v-model="topoConfig.fitPadding" />
          </a-col>
        </a-row>
        <a-row align="middle">
          <a-col :span="6">显示弹窗</a-col>
          <a-col :span="18">
            <a-switch checkedChildren="开" unCheckedChildren="关" defaultChecked v-model="globalGridAttr.infoPopup" />
          </a-col>
        </a-row>
        <a-row align="middle">
          <a-col :span="6">节点样式</a-col>
          <a-col :span="18">
            <a-select
              style="width: 100%"
              :value="topoConfig.shapeType"
              @change="
                (val) => {
                  topoConfig.shapeType = val
                }
              "
            >
              <a-select-option :value="idx" v-for="(shape, idx) in globalGridAttr.shapeTypes" :key="idx">
                {{ shape }}
              </a-select-option>
            </a-select>
          </a-col>
        </a-row>
        <a-row align="middle">
          <a-col :span="6">文字颜色</a-col>
          <a-col :span="18">
             <Shader v-model="topoConfig.labelColor"
             @change="
                (e) => {
                  topoConfig.labelColor = e
                }
              "></Shader>
          </a-col>
        </a-row>

        <a-row align="middle">
          <a-col :span="6">连线箭头</a-col>
          <a-col :span="18">
            <a-select
              style="width: 100%"
              :value="topoConfig.edgeMarker"
              @change="
                (val) => {
                  topoConfig.edgeMarker = val
                }
              "
            >
              <a-select-option :value="idx" v-for="(arrow, idx) in globalGridAttr.edgeMarkerArr" :key="idx">
                {{ arrow }}
              </a-select-option>
            </a-select>
          </a-col>
        </a-row>
        <a-row align="middle">
          <a-col :span="6">连线类型</a-col>
          <a-col :span="18">
            <a-select
              style="width: 100%"
              :value="topoConfig.edgeType"
              @change="
                (val) => {
                  topoConfig.edgeType = val
                }
              "
            >
              <a-select-option :value="idx" v-for="(item, idx) in globalGridAttr.edgeTypeArr" :key="idx">{{
                item
              }}</a-select-option>
            </a-select>
          </a-col>
        </a-row>
        <a-row align="middle">
          <a-col :span="6">连线样式</a-col>
          <a-col :span="18">
            <a-select
              style="width: 100%"
              :value="topoConfig.edgeDash"
              @change="
                (val) => {
                  topoConfig.edgeDash = val
                }
              "
            >
              <a-select-option :value="dash" v-for="(dash, idx) in globalGridAttr.edgeDashArr" :key="idx">
                <line-dash :dash-type="dash"></line-dash>
              </a-select-option>
            </a-select>
          </a-col>
        </a-row>
        <a-row align="middle">
          <a-col :span="6">连线属性</a-col>
          <a-col :span="18">
            <a-select
              mode="multiple"
              style="width: 100%"
              allowClear
              :value="topoConfig.edgeProperties"
              @change="
                (val) => {
                  topoConfig.edgeProperties = val;
                  globalGridAttr.topoConfig.edgeProperties = val;
                }
              "
            >
              <a-select-option :value="item.value" v-for="item in globalGridAttr.edgeProperties" :key="item.value">
               {{item.text}}
              </a-select-option>
            </a-select>
          </a-col>
        </a-row>
        <a-row align="middle">
          <a-col :span="6">连线状态</a-col>
          <a-col :span="14">
            <a-switch
              checkedChildren="开"
              unCheckedChildren="关"
              defaultChecked
              v-model="topoConfig.edgeStatus"
              @change="edgeStatusChange"
            />
          </a-col>
        </a-row>
        <div v-if="!isSimple">
             <a-row align="middle" >
          <a-col :span="6">连线动画</a-col>
          <a-col :span="14">
            <a-switch
              checkedChildren="开"
              unCheckedChildren="关"
              v-model="topoConfig.edgeAni"
              @change="animationChange"
            />
          </a-col>
        </a-row>
        <a-row align="middle">
          <a-col :span="6">动画时长</a-col>
          <a-col :span="18">
            <a-input-number
            style="width:100%"
            v-model="topoConfig.aniTime"
            :min="3"
            :max="10"
            @change="aniTimeChange" />
          </a-col>
        </a-row>
        </div>

        <!-- <a-row align="middle">
        <a-col :span="6">背景闪烁</a-col>
        <a-col :span="14">
          <a-switch checkedChildren="开" unCheckedChildren="关" defaultChecked v-model="globalGridAttr.flicker"
            @change="flickerChange" />
        </a-col>
      </a-row> -->
        <a-row align="middle">
          <a-col :span="6">背景类型</a-col>
          <a-col :span="18">
            <a-radio-group name="radioGroup" :value="topoConfig.bgType" @change="radioChange">
              <a-radio :value="'0'"> 无背景 </a-radio>
              <a-radio :value="'1'"> 调色板 </a-radio>
              <a-radio :value="'2'"> 图片 </a-radio>
            </a-radio-group>
          </a-col>
        </a-row>
        <a-row align="middle" v-if="topoConfig.bgType === '1'">
          <a-col :span="6">背景颜色</a-col>
          <a-col :span="18">
             <Shader v-model="topoConfig.bgColor"></Shader>
          </a-col>
        </a-row>
        <a-row align="middle" v-if="topoConfig.bgType === '2' && topoConfig.bgimg">
          <a-col :span="6">背景图</a-col>
          <a-col :span="18">
            <div style="width: 150px; height: 80px; margin-bottom: 5px" v-if="topoConfig.bgimg">
              <img class="bg-img" :src="topoBgImg" />
            </div>
          </a-col>
        </a-row>
        <a-row align="middle" v-if="topoConfig.bgType === '2'">
          <a-col :span="6">图片列表</a-col>
        </a-row>
        <div v-if="topoConfig.bgType === '2'" :class="{ height144: !topoConfig.bgimg, height96: topoConfig.bgimg }">
          <topo-image-upload
            isMultiple
            :accept="accept"
            :handle-change-additional-fun="handleChangeAdditionalFun"
            :beforeUploadFun="beforeUploadFun"
            @change="changeImgList"
            @changeImg="changeImg"
            @delete='deleteBg'
            :value="globalGridAttr.bgImgList"
          >
          </topo-image-upload>
        </div>
      </div>
    </a-tab-pane>
    <a-tab-pane tab="拓扑信息" key="2" :forceRender="false">
      <div v-if="activeKey == 2">
        <a-row align="middle">
          <a-col :span="6">拓扑名称:</a-col>
          <a-col :span="18">
            <a-input
              type="text"
              :value="globalGridAttr.topoName"
              style="width: 100%"
              @change="
                (e) => {
                  globalGridAttr.topoName = e.target.value
                }
              "
            />
          </a-col>
        </a-row>
        <a-row align="middle">
          <a-col :span="6">选择场景:</a-col>
          <a-col :span="18">
            <a-select
              style="width: 100%"
              :value="globalGridAttr.showType"
              @change="
                (val) => {
                  globalGridAttr.showType = val
                }
              "
            >
              <a-select-option value="0">未选择</a-select-option>
              <a-select-option value="1">数据中心展示</a-select-option>
            </a-select>
          </a-col>
        </a-row>
        <a-row align="middle" v-if='globalGridAttr.topoType==0'>
          <a-col :span="6">绑定单位:</a-col>
          <a-col :span="18">
            <a-tree-select
              v-model='globalGridAttr.deptId'
              tree-node-filter-prop='title'
              :replaceFields='replaceFields'
              :treeData='availableDepartments'
              show-search
              style='width: 100%'
              :multiple='false'
              :maxTagCount='1'
              :dropdownMatchSelectWidth='true'
              :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
              placeholder='请选择单位'
              allow-clear
            >
            </a-tree-select>
          </a-col>
        </a-row>

        <a-row align="middle">
          <a-col :span="6">拓扑类型:</a-col>
          <a-col :span="18">
            {{ ['网络拓扑', '应用拓扑',"单位拓扑"][globalGridAttr.topoType] }}
          </a-col>
        </a-row>
      </div>
    </a-tab-pane>
  </a-tabs>
</template>

<script>
import FlowGraph from '../../../graph'
import TopoImageUpload from '@/components/topo/TopoImageUpload'
import { getAction } from '@/api/manage'
// 连线图案组件
import LineDash from '../LineDash.vue'
import Shader from '@/components/shader/shader.vue';
import { checkAccept, checkBeforeUpload, compareFileSizes } from '@comp/yq/yqUpload/YqUploadCommonFuns'
export default {
  name: 'Index',
  components: {
    TopoImageUpload,
    LineDash,
    Shader,
  },
  props: {
    globalGridAttr: {
      type: Object,
      default: null,
      required: true,
    },
  },
  data() {
    return {
      accept:'image/jpg,image/png, image/jpeg, image/jfif, image/pjp, image/pjpeg',
      acceptTips:'jpg、png、jpeg、jfif、pjp、pjpeg',
      activeKey: '1',
      topoConfig: {},
      isSimple: false,
      availableDepartments: [],
      deptId: undefined,
      replaceFields: {
        children: 'children',
        title: 'deptName',
        key: 'deptId',
        value: 'deptId'
      },
    }
  },
  computed: {
    topoBgImg() {
      if (this.topoConfig.bgimg) {
        return this.topoConfig.bgimg
        // return window._CONFIG['staticDomainURL'] + '/' + this.topoConfig.bgimg
      } else {
        return ''
      }
    },
  },
  created() {
    this.isSimple = window.config.simpleModel !== 0
    this.topoConfig = this.globalGridAttr.topoConfig
    // console.log("当前拓扑绑定的单位 ===",this.globalGridAttr)
    if(this.globalGridAttr.topoType==0){
      this.getAvailableDepartments()
    }
  },
  mounted() {},

  methods: {
    /**
     * 选择文件格式是否正确
     */
    beforeUploadFun(file) {
      return true
    },
    /*文件发生改变后，附加处理方法，一次性上传多个文件，剔除列表中不满足条件的文件*/
    handleChangeAdditionalFun(fileList){
      let list = fileList
      if (fileList.length > 0) {
        list = fileList.filter((item) => {
          return compareFileSizes(true, 10, item.size, 'MB')
        }).filter((item) => {
          return checkAccept(item, this.acceptTips)
        })
      }
      return list
    },
    //获取能绑定的部门
    getAvailableDepartments() {
      getAction('/monitor/situation/getAvailableDeptForNetTop').then((res) => {
        if (res.success) {
          this.availableDepartments = res.result
          if(this.globalGridAttr.deptId){
            this.changeDeptAvailable(this.availableDepartments)
          }
        }
      })
    },
    changeDeptAvailable(list){
      for(let i =0 ; i<list.length ;i++){
        if(list[i].deptId === this.globalGridAttr.deptId){
          list[i].disabled = false;
          return;
        }
        if(list[i].children && list[i].children.length){
          this.changeDeptAvailable(list[i].children)
        }
      }
    },
    aniTimeChange(e) {},
    animationChange(e) {
      this.topoConfig.edgeAni = e
    },
    edgeStatusChange(e) {
      this.topoConfig.edgeStatus = e
    },
    flickerChange(e) {
      this.globalGridAttr.flicker = e
    },
    changeImgList(path, status) {
      if (!!path) {
        this.globalGridAttr.bgImgList = path.split(',')
        let imgListLength = this.globalGridAttr.bgImgList.length
        let imgUrl = this.globalGridAttr.bgImgList[imgListLength - 1]
        // this.topoConfig.bgimg = imgUrl
      } else {
        this.globalGridAttr.bgImgList = []
        this.topoConfig.bgimg = ''
        const { graph } = FlowGraph
        graph.clearBackground();
      }
    },
    changeImg(fileUrl) {
      if (this.topoConfig.bgType === '2') {
        if(fileUrl.startsWith("http://") ||fileUrl.startsWith("https:''") ){
          this.topoConfig.bgimg = fileUrl
        }else{
          this.topoConfig.bgimg = window._CONFIG['staticDomainURL'] + '/' + fileUrl
        }
      }
    },
    deleteBg(file) {
      if(this.topoConfig.bgimg === file.url){
        this.topoConfig.bgimg=""
      }
    },
    radioChange(e) {
      let val = e.target.value
      this.topoConfig.bgType = val
    },
    topoBgChange(e){
      //  const { graph } = FlowGraph
      //   graph.updateBackground({
      //     color: this.topoConfig.bgColor,
      //   })
    }
  },
}
</script>
<style lang="less" scoped>
.bg-img {
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.height144 {
  // height: calc(100% - 128px);
  // overflow-y: auto;
}

.height96 {
  // height: calc(100% - 181px);
  // overflow-y: auto;
}
.ant-tabs {
  color: rgba(0, 0, 0, 0.65);
  height: 100%;
}
.ant-tabs .ant-tabs-top-content,
.ant-tabs .ant-tabs-bottom-content {
  width: 100%;
  height: calc(100% - 64px);
}
.ant-tabs .ant-tabs-top-content > .ant-tabs-tabpane,
.ant-tabs .ant-tabs-bottom-content > .ant-tabs-tabpane {
  height: 100% !important;
}
.tab-con {
  height: 80vh;
  overflow-y: scroll;
}
::v-deep .ant-upload-list-picture-card-container {
  width: 100% !important;
}

::v-deep .ant-upload-list-picture-card .ant-upload-list-item {
  width: 100% !important;
}

::v-deep .ant-upload.ant-upload-select-picture-card {
  width: 100% !important;
}
</style>