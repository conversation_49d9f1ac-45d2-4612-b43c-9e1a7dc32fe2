<template>
  <a-card style="height:100%">
    <a-row>
      <a-row>
        <a-col :span="24">
          <span style="float: right;margin-bottom: 12px;">
            <img
              src="~@/assets/return1.png"
              alt
              @click="getGo"
              style="width: 20px;height: 20px;cursor: pointer"
            />
          </span>
        </a-col>
        <a-col :span="24">
          <table class="gridtable">
            <tr>
              <td class="leftTd">类型</td>
              <td class="rightTd">{{ data.type}}</td>
              <td class="leftTd">标题</td>
              <td class="rightTd">{{ data.title }}</td>
            </tr>
            <tr>
              <td class="leftTd">描述</td>
              <td class="rightTd">{{ data.description }}</td>
              <td class="leftTd">解决方案</td>
              <td class="rightTd">{{ data.plan }}</td>
            </tr>
          </table>
        </a-col>
      </a-row>
    </a-row>
  </a-card>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import pick from 'lodash.pick'
import { validateDuplicateValue } from '@/utils/util'
import JFormContainer from '@/components/jeecg/JFormContainer'
import JDictSelectTag from '@/components/dict/JDictSelectTag'
export default {
  name: 'DevopsBackupTaskDetails',
  components: {
    JFormContainer,
    JDictSelectTag
  },
  props: {
    data: {
      type: Object
    }
  },
  data() {
    return {
      form: this.$form.createForm(this),
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      url: {
        queryById: '/devopsBackupTask/devopsBackupTask/queryById'
      }
    }
  },
  computed: {},
  created() {},
  methods: {
    modalFormOk() {
      let params = { id: this.data.id }
      getAction(this.url.queryById, params).then(res => {
        if (res.success) {
          this.data = res.result
        }
      })
    },
    //详情编辑
    handleDetailEdit: function(record) {
      this.$refs.modalForm.edit(record)
      this.$refs.modalForm.title = '编辑'
      this.$refs.modalForm.disableSubmit = false
    },
    //返回上一级
    getGo() {
      this.$parent.pButton2(0)
    }
  }
}
</script>
<style lang="less" scoped>
/deep/ .ant-descriptions-item-label {
  text-align: center !important;
}
table.gridtable {
  font-family: verdana, arial, sans-serif;
  font-size: 14px;
  color: #606266;
  border-width: 1px;
  border-color: #e8e8e8;
  border-collapse: collapse;
  text-align: left;
  width: 100%;
}
table.gridtable td {
  border-width: 1px;
  border-style: solid;
  border-color: #e8e8e8;
}
.leftTd {
  width: 17%;
  background-color: #fafafa;
  padding: 16px 24px;
  text-align: center;
}
.rightTd {
  width: 35%;
  padding: 16px 24px;
}
</style>