<template>
  <div style="height:100%">
      <component :is="pageName" :data="data"/>
  </div>
</template>
<script>
  import DevopsBackupTaskList from './DevopsBackupTaskList'
  import DevopsBackupTaskDetails from './modules/DevopsBackupTaskDetails'
  export default {
    name: "DevopsBackupTaskManage",
    data() {
      return {
        isActive: 0,
        data:{}
      };
    },
    components: {
      DevopsBackupTaskList,
      DevopsBackupTaskDetails
    },
    created(){
      this.pButton1(0);
    },
    //使用计算属性
    computed: {
      pageName() {
        switch (this.isActive) {
          case 0:
            return "DevopsBackupTaskList";
            break;

          default:
            return "DevopsBackupTaskDetails";
            break;
        }
      }
    },
    methods: {
      pButton1(index) {
        this.isActive = index;
      },
      pButton2(index,item) {
        this.isActive = index;
        this.data = item;
      }
    }
  }
</script>