<template>
  <div
    style='background-color: #FFFFFF;height: calc(100% - 30px); border-radius: 3px;display: flex;flex-direction: column'>
    <!-- 树-->
    <div class='hScroll vScroll' style='flex:auto;margin:5px 14px 14px 14px'>
      <template>
        <a-dropdown>
          <a-tree 
            @select='onSelect'
            :draggable='true'
            :defaultExpandAll='true'
            :selectedKeys='selectedKeys'
            :checkedKeys='checkedKeys'
            :treeData='assetsCategoryTree'
            :checkStrictly='checkStrictly'
            :expandedKeys='expandedKeys'
            :autoExpandParent='autoExpandParent'
            :defaultSelectedKeys='defaultSelectedKeys'
            @expand='onExpand'
            style='user-select: none'
            :showIcon='true'>
            <template slot='title' slot-scope='{ title ,type}'>
              <span v-if='title.indexOf(searchValue) > -1 && title !== firstTitle'>
                {{ title.substr(0, title.indexOf(searchValue)) }}
                <span style='color: #f50'>{{ searchValue }}</span>
                {{ title.substr(title.indexOf(searchValue) + searchValue.length) }}
              </span>
              <span v-else-if='title.indexOf(searchValue) > -1 && title === firstTitle'>
                <span style='background-color: #bae7ff; color: #f50'>{{ title }}</span>
              </span>
              <span v-else>{{ title }}</span>
            </template>
          </a-tree>
        </a-dropdown>
      </template>
    </div>
  </div>
</template>
<script>
  import {
    queryAssetsCategoryTreeList
  } from '@/api/device'
  import {
    getAction,
    deleteAction,
    putAction,
    postAction,
    httpAction
  } from '@/api/manage'

  //为tree生成对应地title slot
  const generateTitleSlotScopes = (data) => {
    for (let i = 0; i < data.length; i++) {
      // var info = data[i];
      data[i].scopedSlots = {
        title: 'title'
      }
      if (data[i].children) {
        generateTitleSlotScopes(data[i].children)
      }
    }
  }
  export default {
    name: 'device-tree-expand',
    data() {
      return {
        firstTitle: '', //存储搜素tree的第一个title
        // 树
        assetsCategoryTree: [],
        treeData: [],
        expandedKeys: [],
        searchValue: '',
        autoExpandParent: true,
        selectedKeys: [],
        checkedKeys: [],
        checkStrictly: true,
        defaultSelectedKeys: [],
        curSelectedNode: undefined
      }
    },
    props: {
      placeholder: {
        type: String,
        default: 'String',
        required: false
      },
      //树节点type值数组，要与后端传的type值一一对应
      arrType: {
        type: Array,
        default: null,
        required: false
      },
      treeUrl: {
        type: String,
        default: '/device/deviceInfo/vcenterTree',
        required: false
      },
      params: {
        type: String,
        default: 'String',
        required: false
      },
      //是否显示搜索框
      inputFlag: {
        type: Boolean,
        default: true,
        required: false
      }
    },
    watch: {},
    mounted() {
      this.loadTree()
    },
    methods: {
      // 查询树
      loadTree() {
        var that = this
        that.treeData = []
        that.assetsCategoryTree = []
        getAction(this.treeUrl, {deviceId:this.params}).then((res) => {
          if (res.success) {
            // 部门全选后，再添加部门，选中数量增多
            this.allTreeKeys = []
            if (res.result.length > 0) {
              generateTitleSlotScopes(res.result)
              that.assetsCategoryTree = [...res.result]
              this.$emit('selected',that.assetsCategoryTree[0])

            }
            this.loading = false
          }
        })
      },
      // 选择树的方法
      onSelect(selectedKeys, e) {
        if (selectedKeys.length > 0) {
          this.selectedKeys = selectedKeys
          this.firstTitle = ''
          this.curSelectedNode = e.node
          this.$emit('selected', e.selectedNodes[0].data.props.dataRef )
        }
      },
      onExpand(expandedKeys) {
        if (expandedKeys.length == 1) {
          this.defaultSelectedKeys = expandedKeys
        } else {
          this.defaultSelectedKeys = expandedKeys.splice(0, 1)
        }
        this.expandedKeys = expandedKeys
        this.autoExpandParent = true
      }
    }
  }
</script>
<style lang='less' scoped>
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';

  /*垂直滚动条*/
  .vScroll::-webkit-scrollbar {
    margin-right: -24px !important;
  }

  /*水平滚动条*/
  .hScroll::-webkit-scrollbar {
    margin-right: -24px !important;
    margin-left: -24px !important;
  }

  .loadAllBtn:hover {
    background-color: #ecf5ff;
    color: #409eff;
    cursor: pointer;
  }

  .loadAllBtn {
    margin-left: 24px;
    padding: 5px;
    height: 24px;
    line-height: 24px;
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5;
  }
</style>