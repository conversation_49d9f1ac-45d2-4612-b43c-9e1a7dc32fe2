<template>
  <div class='zr-bigscreen-footer'>
    <div class='zr-footer-box'>
      <div
        class='zr-footer-item'
        :class='{"zr-footer-item-active": item.name === activeBtn}'
        v-for="item in footerBtns"
        @click.prevent='changePage(item)'
        >
        <img class='zr-footer-img' :src='item.icon'>
      </div>
      <div class='active-icon' :style='{left:activeLeft}'></div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'ZrBigScreenLayout',
  data() {
    return {
      footerBtns: [
        {
          name: 'yxpgst',
          icon: '/zrBigScreen/yxpgst.png',
          page:"/operationsView/OpEval"
        },
        {
          name: 'zhst',
          icon: '/zrBigScreen/zhst.png',
          page:"/operationsView/compNational"
        },
        {
          name: 'wlst',
          icon: '/zrBigScreen/wlst.png',
          page:"/operationsView/network"
        },
      ],
      activeBtn: 'zhst'
    }
  },
  created(){
    this.resetPaths()
  },
  mounted() {},
  watch: {
    '$route.path':{
     immediate:true,
      handler(){
        this.resetPaths()
        let pageName = this.footerBtns.find(el=>el.page === this.$route.path)?.name
        if(pageName){
          this.activeBtn = pageName
        }
      }
    }
  },
  computed: {
    activeLeft(){
      let idx = this.footerBtns.findIndex(el=>el.name === this.activeBtn)
      return `calc((100% / 3) * ${idx})`
    },
  },
  beforeDestroy() {
  },
  methods: {
    resetPaths(){
      if(window.config.ywtsStatic){
        this.footerBtns.forEach(item=>{
          if(!item.page.startsWith("/static")){
            item.page = "/static"+item.page
          }
        })
      }
    },
    changePage(item){
      if(item.page){
        this.$router.push(item.page)
      }else{
        this.$message.warn("暂无页面！")
      }
    }
  }
}
</script>



<style scoped lang='less'>
.zr-bigscreen-footer {
  display: flex;
  justify-content: center;
  align-items:end;
  height: calc(62 / 19.2 * 1vw);
  width: calc(1060/ 19.2 * 1vw);
  position: fixed;
  bottom:0 ;
  left: calc(50% - (1060 / 19.2 * 1vw) / 2);
  background-image: url(/zrBigScreen/zrFooterBg.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.zr-footer-box{
  display: flex;
  position:relative;
  justify-content: space-around;
  align-items: center;
  width: calc((510 / 19.2 * 1vw));
  height: calc(50 / 19.2 * 1vw);
  .zr-footer-item{
    width: calc(100% / 3);
    height:100%;
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0.3;
    cursor: pointer;
    .zr-footer-img{
      width: calc((24 / 19.2 * 1vw));
      height: calc(24 / 19.2 * 1vw);
    }
  }
  .zr-footer-item-active{
    opacity: 1;
    .zr-footer-img{
      width: calc((28 / 19.2 * 1vw));
      height: calc(28 / 19.2 * 1vw);
    }
  }
  .active-icon{
    position: absolute;
    top:calc(-15 / 19.2 * 1vw - 1.5px);
    left:0;
    width: calc(100% / 3);
    height: calc(18 / 19.2 * 1vw);
    background-image: url(/zrBigScreen/activeIcon.png);
    background-size: 100% 100%;
  }
}
</style>