<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container>
      <a-form :form="form" slot="detail" :labelCol="labelCol" :wrapperCol="wrapperCol">
        <a-row :gutter='24'>
          <a-col :span="24">
            <a-form-item label="任务名称" >
              <a-input v-decorator="['name', validatorRules.name]" :allowClear="true" autocomplete="off"
                       placeholder="请输入任务名称"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="数据库">
              <a-select  v-decorator="['databaseId', validatorRules.databaseId]" :options="sources" placeholder="请选择数据库" style="width: 100%" >
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="bucket桶">
              <a-input style='width: 100%' v-decorator="['databaseBucket', validatorRules.databaseBucket]" :allow-clear='true'
                       autocomplete='off'
                       placeholder='请输入bucket桶' />
            </a-form-item>
          </a-col>

          <a-col :span="24">
            <a-form-item label="执行时间">
              <!-- cron表达式  -->
              <j-cron ref="innerVueCron" v-decorator="['executeCron', validatorRules.executeCron]" @change="setCorn" :hideYear="true"></j-cron>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="清除规则">
              <a-select  v-decorator="['dumpRuleId', validatorRules.dumpRuleId]" placeholder="请选择清除规则" :options="rules" style="width: 100%" >
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
import {
  httpAction, getAction, deleteAction
} from '@/api/manage'
import pick from 'lodash.pick'
import JFormContainer from '@/components/jeecg/JFormContainer'
import JCron from '@/components/jeecg/JCron.vue'
import {
    JeecgListMixin
} from '@/mixins/JeecgListMixin'
export default {
  name: 'transitionTaskForm',
  mixins: [JeecgListMixin],
  components: {
    JFormContainer,
    JCron,
  },
  props: {
    rules:{
      type:Array,
      default:() => {
        return []
      }
    },
    sources:{
      type:Array,
      default:() => {
        return []
      }
    },
  },
  data() {
    return {
      form: this.$form.createForm(this),
      model: {
        strategyMode: '1',
      },
      labelCol: {
        xs: {
          span: 24,
        },
        sm: {
          span: 4,
        },
        md: {
          span: 3,
        },
        lg: {
          span: 3,
        },
      },
      wrapperCol: {
        xs: {
          span: 24,
        },
        sm: {
          span: 20,
        },
        md: {
          span: 21,
        },
        lg: {
          span: 21,
        },
      },
      confirmLoading: false,
      validatorRules: {
        name: {
          rules: [
            {required: true,message: '请输入任务名称'},
            {min: 2,max: 30,message: '长度在2到30个字符',trigger: 'blur'}
          ]
        },
        databaseId: {
          rules: [{ required: true, message: '请选择数据库' }]
        },
        databaseBucket: {
          rules: [
            {required: true,message: '请输入bucket桶'},
            {min: 1, max: 30,message: '长度在2到30个字符',trigger: 'blur'}]
        },
        executeCron: {
          initialValue: '0 0 0 * * ?',
          rules: [{ required: true, validator: this.validateCorn}]
        },
        dumpRuleId: {
          rules: [{ required: true, message: '请选择清除规则' }]
        }
      },
      url: {
        add: '/distributedStorage/dump/job', // 添加任务
        edit: '/distributedStorage/dump/job', // 编辑任务
        validateCorn: '/autoInspection/devopsAutoInspection/cronCheckSix', // 校验cron表达式
        list: '/distributedStorage/dump/getDevice', // 获取某个设备的任务列表
        delete: '/distributedStorage/dump/unbindDevice', // 某个任务解绑设备
      },
      effectiveDate: null,
      taskId: undefined,
      effectiveStatus: '0', // 默认永久有效
      disableMixinCreated: true,
    }
  },
  methods: {
    closeForm() {
      this.$emit('closeForm')
    },
    add() {
      this.edit({})
    },
    edit(record) {
      this.queryParam.taskId = record.id
      this.form.resetFields()
      this.model = Object.assign({}, record)
      if(record.id){
        this.$nextTick(() => {
          let pickdata = pick(this.model,["name","databaseId","databaseBucket","executeCron","dumpRuleId"])
          this.formInit(pickdata)
        })
      }
    },
    formInit(pickData) {
      this.form.setFieldsValue(
        pickData
      )
    },
    //向后端请求table数据
    loadTableData() {
      this.loading = true
      this.loadData()
    },
    tableOK(idList, list) {
      let deviceIds = this.model.deviceIds || []
      if (list && list.length > 0) {
        list.forEach(ele => {
          if (deviceIds.indexOf(ele.id) == -1) {
            this.dataSource.push(ele)
          }
        })
        let ids = this.dataSource.map(m => {
          return m.id
        })
        this.model.deviceIds = ids.join(',')
      }
    },
    //提交
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (
          !err
        ) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }

          let formData = Object.assign(that.model, values)

          if (that.effectiveStatus == '0') {
            // 永久有效
            formData.effectiveDate = 0
          } else {
            formData.effectiveDate = that.effectiveDate
          }

          httpAction(httpurl, formData, method)
            .then((res) => {
              that.confirmLoading = false
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
                that.closeForm()
              } else {
                that.$message.warning(res.message)
              }
            })
            .catch((err) => {
              that.$message.warning(err.message)
              that.confirmLoading = false
          })
        }
      })
    },
    validateCorn(rule, value, callback) {
      if (rule.required) {
        if (value && value.length > 0) {
          getAction(this.url.validateCorn, {
            cronExpression: value
          }).then((res) => {
            if (res.success) {
              callback()
            } else {
              callback('cron表达式格式错误!')
            }
          })
        } else {
          callback('请输入cron表达式')
        }
      } else {
        callback()
      }
    },
    //周期执行
      setCorn(data) {
      if (data && data.target != null) {
        let dataList = data.target.value.split(' ')
        if (dataList[0] === '*') {
          this.$message.warning('请确认是否每秒都执行')
        }
      } else {
        let dataList = data.split(' ')
        if (dataList[0] === '*') {
          this.$message.warning('请确认是否每秒都执行')
        }
      }

      if (Object.keys(data).length === 0) {
        this.$message.warning('请输入cron表达式!');
      }
    },
  }
}
</script>
<style scoped lang='less'>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
.colorBox {
  margin-top: 18px;
  margin-bottom: 18px;
}
.colorTotal {
  padding-left: 7px;
  border-left: 4px solid #1e3674;
}

/* 定义滚动条样式 */
::-webkit-scrollbar {
  width: 0.15rem
  /* 12/80 */
;
  // height: 6px;
  //background-color: #222325;
}

/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track {
  box-shadow: inset 0 0 0px rgba(240, 240, 240, 0.5);
  // border-radius: 50%;
  background-color: #eaeaea;
  border-radius: 0.1rem
  /* 8/80 */
;
}

/*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
  border-radius: 0.1rem
  /* 8/80 */
;
  box-shadow: inset 0 0 0px rgba(240, 240, 240, 0.5);
  background-color: #d6d6d6;
}

::v-deep .ant-spin-nested-loading {
  padding-right: 13px;
}
</style>