<template>
  <a-drawer
    :title="title"
    :width="drawerWidth"
    @close="handleCancel"
    :destroyOnClose="true"
    :visible="visible"
    :confirmLoading="confirmLoading"
  >
    <a-descriptions :column="{ xxl: 1, xl: 1, lg: 1, md: 1, sm: 1, xs: 1 }" bordered>
      <a-descriptions-item label="类型判断">{{ item.isCmp == '0' ? '分类' : '组件' }}</a-descriptions-item>
      <a-descriptions-item label="父级节点">
        {{ item.parentId == '0' ? '无' : item.parentText }}
      </a-descriptions-item>
      <a-descriptions-item :label="nameLabel">{{ item.name }}</a-descriptions-item>
      <a-descriptions-item :label="codeLabel">{{ item.code }}</a-descriptions-item>
      <a-descriptions-item label="分类类型" v-if="!this.isCmpJudge">{{ item.cmpGroupText }}</a-descriptions-item>
      <a-descriptions-item label="分类协议" v-if="!this.isCmpJudge && (this.group == 2 || this.group == 3)">
        {{ item.protocol }}
      </a-descriptions-item>
      <a-descriptions-item label="序号">{{ item.cmpSerial }}</a-descriptions-item>
      <a-descriptions-item :label="descLabel">{{ item.cmpDesc }}</a-descriptions-item>
      <a-descriptions-item label="参数类型" v-if="this.isCmpJudge">{{ item.paramType }}</a-descriptions-item>
      <a-descriptions-item label="图标" v-if="this.isCmpJudge">
        <img :src="picSrc + '/' + item.cmpIcon" style="width: 30px; height: 30px" v-if="item.cmpIcon" />
      </a-descriptions-item>
    </a-descriptions>
  </a-drawer>
</template>

<script>
import { addModel, editModel, ajaxGetDictItems, getDictItemsFromCache } from '@/api/api'
import pick from 'lodash.pick'
import JDictSelectTag from '@/components/dict/JDictSelectTag'
import paramsModelLook from './paramsModelLook.vue'
import svgUpload from '@/components/jeecg/svgUpload'
import { getAction, deleteAction, httpAction } from '@/api/manage'

export default {
  name: 'componentManagementModule',
  components: {
    JDictSelectTag,
    svgUpload,
    paramsModelLook,
  },

  data() {
    return {
      item: {},
      group: '',
      nameLabel: '',
      codeLabel: '',
      descLabel: '',
      isCmpJudge: false,
      disableGroup: false,
      disableProtocol: false,
      paramType: '',
      paramModel: {},
      drawerWidth: 600,
      childrenDataList: [], //子列表
      typeList: [],
      protocolList: [],
      title: '操作',
      visible: false,
      disableSubmit: false,
      disableParent: false,
      model: {},
      parentId: '',
      isCmp: '0',
      show: true, //根据菜单类型，动态显示隐藏表单元素
      picSrc: window._CONFIG['staticDomainURL'],
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      url: {
        add: '/flow/cmp/add',
        edit: '/flow/cmp/edit',
      },
      confirmLoading: false,
      form: this.$form.createForm(this),
    }
  },
  created() {
    //获取组件类型的字典
    this.initDictData('cmpType', 'typeList')
    this.initDictData('cmpProtocol', 'protocolList')
  },
  methods: {
    initDictData(dictCode, listname) {
      //根据字典Code, 初始化字典数组
      ajaxGetDictItems(dictCode, null).then((res) => {
        if (res.success) {
          this[listname] = res.result
        }
      })
    },
    add() {
      // 默认值
      this.edit({})
    },
    edit(record) {
      this.item = record
      if (record.id != '' && record.id != null && record.id != undefined) {
        this.disableParent = true
      } else {
        this.disableParent = false
      }
      if (record.isCmp == 1) {
        this.isCmpJudge = true
        this.nameLabel = '组件名称'
        this.codeLabel = '组件标识'
        this.descLabel = '组件说明'
      } else {
        this.isCmpJudge = false
        this.nameLabel = '分类名称'
        this.codeLabel = '分类标识'
        this.descLabel = '分类说明'
      }
      if (record.cmpGroup != undefined) {
        this.group = record.cmpGroup
      } else {
        this.group = ''
      }
      if (record.parentId && record.parentId != undefined && record.parentId != null) {
        this.parentId = record.parentId
        this.isCmp = '0'
      }
      this.paramType = record.paramType
      this.form.resetFields()
      this.model = Object.assign({}, record)
      if (this.model.paramModel && this.model.paramModel != null) {
        this.childrenDataList = JSON.parse(this.model.paramModel)
      } else {
        this.childrenDataList = []
      }
      this.visible = true
    },
    close() {
      this.$emit('close')
      this.disableSubmit = false
      this.visible = false
      this.parentId = ''
    },
    handleCancel() {
      this.close()
    },
  },
}
</script>

<style lang='less' scoped>
::v-deep .two-words > div > label {
  letter-spacing: 4px;
}
::v-deep .two-words > div > label::after {
  letter-spacing: 0px;
}
@media (max-width: 700px) {
  ::v-deep .ant-drawer-content-wrapper {
    max-width: 100vw;
    margin: 0;
  }
}
</style>
