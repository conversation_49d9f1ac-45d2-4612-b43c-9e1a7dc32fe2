<template>
  <div style="height: 100%; width: 100%">
    <div class="topProductBox">
      <top-product v-for="(item,idx) in products" :key="idx" :src="item.src" :name="item.name" :value="item.value">
      </top-product>
    </div>
    <div class="alarmAll">
      <div class="planet-box">
        <planet-orbiting :alarmData="dataList" :allNum="allNum" :paused="isPuased"></planet-orbiting>
      </div>

    </div>
  </div>
</template>

<script>
  import PlanetOrbiting from './PlanetOrbiting.vue'
  import topProduct from './topProduct.vue'
  import {
    getAction
  } from '@/api/manage'
  export default {
    components: {
      topProduct,
      PlanetOrbiting,
    },
    data() {
      return {
        allNum: 0,
        products: [{
            src: require('/public/statsCenter/alarm/server.png'),
            name: '服务器',
            value: 7,
          },
          {
            src: require('/public/statsCenter/alarm/networkDevice.png'),
            name: '交换机',
            value: 5,
          },
          {
            src: require('/public/statsCenter/alarm/database.png'),
            name: '数据库',
            value: 4,
          },
          {
            src: require('/public/statsCenter/alarm/middleware.png'),
            name: '应用服务器',
            value: 2,
          },
          {
            src: require('/public/statsCenter/alarm/desktop.png'),
            name: '终端',
            value: 1,
          },
          {
            src: require('/public/statsCenter/alarm/safeDevice.png'),
            name: '存储产品',
            value: 1,
          },
        ],
        url: {
          productCategory: 'data-analysis/alarm/count/productCategory',
        },
        dataList: []
      }
    },
    mounted() {
      this.getProduct()
    },
    computed: {
      isPuased() {
        return false; //window._CONFIG['simpleModel'] === 0?false:true;
      }
    },
    methods: {
      getProduct() {
        getAction(location.origin + "/statsCenter/mock/alarmData.json").then((res) => {
          this.dataList = res.alarmAll.dataList
          this.allNum = res.alarmAll.allNum
        })
      },
    },
  }
</script>

<style scoped lang="less">
  .topProductBox {
    height: 80px;
    width: 100%;
    display: flex;
    justify-content: space-around;
  }

  .alarmAll {
    height: calc(100% - 80px);
    width: 100%;
  }

  .planet-box {
    height: 80%;
  }
</style>