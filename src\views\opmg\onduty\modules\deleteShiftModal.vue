<template>
  <j-modal title='批量删除'
           :visible='delVisible'
           :maskClosable='true'
           :centered="true"
           :width='900'
           switchFullscreen
           cancelText='关闭'
           @cancel='handleCancel'
           @ok='handleOk'>
    <a-form-model ref='form' :model='form' :rules='validatorRules' :label-col='labelCol' :wrapper-col='wrapperCol'>
      <a-row :gutter='24'>
        <a-col v-bind='formItemLayout'>
          <a-form-model-item label='日期' prop='dateList'>
            <a-range-picker v-model='form.dateList'
                            :getCalendarContainer="node=> node.parentNode"
                            format='YYYY-MM-DD'
                            :disabled-date="disabledDate"
                            style='width: 100%'
                            @change='onChange'
            />
          </a-form-model-item>
        </a-col>
        <a-col v-bind='formItemLayout'>
          <a-form-model-item label='班次' prop='ids'>
            <a-select
              mode="multiple"
              labelInValue
              :getPopupContainer='node=>node.parentNode'
              v-model='form.ids'
              :allow-clear='true'
              placeholder='请选择班次信息'
            >
              <a-select-option v-for='(data,i) in dataSource' :key='i' :value='data.id'>
                {{ data.shiftName }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col v-bind='formItemLayout'>
          <a-form-model-item label='值班人' prop='userIds'>
            <a-select
              mode="multiple"
              labelInValue
              :getPopupContainer='node=>node.parentNode'
              v-model='form.userIds'
              :allow-clear='true'
              placeholder='请选择值班人'
            >
              <a-select-option v-for='(data,i) in userList' :key='i' :value='data.username'>
                {{ data.realname }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
  </j-modal>
</template>

<script>
import { getAction } from '@api/manage'
import moment from "moment";

export default {
  name: 'deleteShiftModal',
  props: {
    userList: {
      type: Array,
      default: []
    },
    dataSource: {
      type: Array,
      default: []
    }
  },
  data() {
    return {
      confirmLoading: false,
      delVisible: false,
      form: {
        ids:[],
        userIds:[]
      },
      formItemLayout: {
        md: { span: 24 },
        sm: { span: 24 }
      },
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
        md: { span: 5 },
        lg: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
        md: { span: 15 },
        lg: { span: 16 }
      },
      validatorRules: {
        dateList: [{ required: true, message: '请选择日期!' }],
      }
    }
  },

  methods: {
    disabledDate (current) {
      return current && current < moment().endOf('day');
    },
    handleCancel() {
      this.delVisible = false
    },
    handleOk() {
      let ids=[]
      if (this.form.ids&&this.form.ids.length>0){
        this.form.ids.forEach(o=>{
          ids.push(o.key);
        })
        this.form.id=ids.toString()
      }
      let userIds=[]
      if (this.form.userIds&&this.form.userIds.length>0){
        this.form.userIds.forEach(o=>{
          userIds.push(o.key);
        })
        this.form.userId=userIds.toString()
      }
      this.$refs.form.validate((valid) => {
        if (valid) {
          let _this = this
          _this.confirmLoading=true
          this.$confirm({
            title: '确认删除',
            okText: '是',
            cancelText: '否',
            content: '是否删除所选条件的数据?',
            onOk: function() {
              getAction('/shiftUser/record/deleteRecords', _this.form).then((res) => {
                _this.confirmLoading=false
                if (res.success) {
                  _this.$emit('loadData1')
                  _this.$emit('getUserList')
                  _this.delVisible = false
                } else {
                  this.$message.warning(res.message)
                }
              })
            }
          })
        }
      })
    },
    openVisible() {
      this.form = {}
      this.delVisible = true
    },

    /**
     * 时间选择
     *
     * @param date
     * @param dateString
     */
    onChange(date, dateString) {
      this.form.startDate = dateString[0]
      this.form.endDate = dateString[1]
    }
  }
}
</script>
<style scoped lang='less'>
@import '~@assets/less/normalModal.less';
</style>