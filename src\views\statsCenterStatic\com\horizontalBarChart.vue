<template>
  <div ref='horizontalBarChart'></div>
</template>
<script>
import { heightPixel, widthPixel } from '@views/statsCenter/com/calculatePixel'
// import echarts from 'echarts'

export default {
name: "horizontalBarChart",
  props: {
    chartData:{
      type: Array,
      required: true
    },
    tipDescription:{
      type: String,
      required: false,
      default:''
    },
    unit:{
      type: String,
      required: false,
      default:''
    },
    max:{
      type: Number,
      required: true
    },
    labelMaxLength:{
      type: Number,
      required: false,
      default:Infinity
    }
  },
  data(){
  return{
    myChart:null
  }
  },
  watch:{
    chartData:{
      handler(nVal,oVal){
        this.$nextTick(()=>{
          this.drawBarChart(nVal)
        })
      },
      deep:true,
      immediate:true
    }
  },
destroyed() {
  window.removeEventListener('resize',  this.changeResize)
},
  methods:{
    drawBarChart(chartData) {
      if (chartData.length > 0) {

        let w5=widthPixel(5)
        let w4=widthPixel(4)
        let w6=widthPixel(6)
        let w8=widthPixel(8)
        let w10=widthPixel(10)
        let w12=widthPixel(12)
        let w14=widthPixel(14)
        let w22=widthPixel(22)
        let w20=widthPixel(20)
        let w50= widthPixel(50)


        let arr = []
        let brr = []
        chartData.forEach((e) => {
          arr.push(e.name)
          brr.push(e.value)
        })

        let maxArr=this.getMaxArr(chartData.length)

        this.myChart = this.$echarts.init(this.$refs.horizontalBarChart)
        this.myChart.clear()
        this.myChart.setOption({
          grid: {
            top: w20,
            left:w20,
            right: w50,
            bottom:0,
            containLabel: true, //防止标签溢出
          },
          tooltip: {
            show: true,
            trigger: 'axis',
            axisPointer: {
              show:false,
              // 坐标轴指示器，坐标轴触发有效
              type: 'none', // 默认为直线，可选为：'line' | 'shadow'
              lineStyle:{
                type:'none',
                color:'#ffffff'
              }
            },
            transitionDuration: 0, //echart防止tooltip的抖动
            formatter:(value)=>{
              let nameTxt=`<span style='text-align:left;font-size:${w14+"px"};height: ${w22+"px"};line-height: ${w22+"px"};display: inline-block'>${value[1].data.name}</span>`
              let dataTxt=`<span style='text-align:left;font-size:${w14+"px"};height: ${w22+"px"};line-height: ${w22+"px"};display: inline-block'><span style="display: inline-block;width: ${w10+"px"};height: ${w10+"px"};background: #03FFFF;border-radius: 100%;margin-right: ${w5+"px"}"></span>${this.tipDescription} ${value[1].value}${this.unit}</span>`

              return  nameTxt+"</br>" +dataTxt
            }
          },
          yAxis: [
            {
              type: 'category',
              data: arr,
              splitLine: {
                show: false,
              }, //去除网格线
              axisTick: {
                show: false,
              },
              axisLine: {
                show: false, //y轴线消失
                lineStyle: {
                  //y轴字体颜色
                  color: '#f6f6f6',
                },
              },
              axisLabel: {
                show:false
              },
            }
          ],
          xAxis : {
            type: 'value',
            min:0,
            max:this.max,
            scale:true,
            splitLine: {
              show:false ,//隐藏或显示网格线
              lineStyle:{
                type:'solid', //设置网格线类型 dotted：点线 solid:实线  dashed:虚线
                opacity:0.4,
                color:"#3D455D"
              }
            },
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false
            },
            axisLabel: {
              show:false,
              textStyle: {
                color: '#9FA5AD',
                fontSize: w14,
                fontWeight: 400
              },
            },
          },

          series: [
            {
              zlevel: 0,
              data: maxArr,
              type: 'bar',
              showBackground: false,
              backgroundStyle: {
                color: '#0D2D51', //柱状图背景颜色
              },
              barWidth: w10, //柱图宽度
              itemStyle: {
                normal: {
                  barBorderRadius: 0,
                  color: 'rgb(13,45,81)', //柱状图背景颜色
                  label: {
                    show: true,
                    position: 'right',
                    padding:0,
                    textStyle: {
                      color: '#9FA5AD',
                      fontSize: w12,
                      fontWeight: 400
                    },
                    formatter:(value)=>{
                      let index=value.dataIndex
                      return  brr[index]+this.unit
                    }
                  },
                },
              },
            },
            {
              zlevel: 1,
              data: chartData,
              type: 'bar',
              silent:true,
              showBackground: false,
              backgroundStyle: {
                color: 'rgba(224,25,25,0.2)', //柱状图背景颜色
              },
              barWidth: w4, //柱图宽度
              barGap: '-100%',//设置不同系列之间的间距,当设置多个则只有一个值有效
              //barCategoryGap:50,//设置同系列柱状图之间的间距，当设置barWidth后该值无效且多个则只有一个值有效
              itemStyle: {
                normal: {
                  barBorderRadius: [0,w10,w10,0],
                  color:'#03FFFF',
                  /* color: new echarts.graphic.LinearGradient(0, 0, 0,1, [
                     {
                       offset: 0,
                       color: '#01A5FE',
                     },
                     {
                       offset: 1,
                       color: '#03F8FB',
                     },
                   ]),*/
                  label: {
                    show: true,
                    position: 'left',
                    align:'left',
                    verticalAlign: "bottom",
                    padding:[0,0, w6,  w6],
                    textStyle: {
                      color: '#fff',
                      fontSize: w12,
                      fontWeight: 400
                    },
                    formatter: (value) => {
                      if(isNaN(this.labelMaxLength)){
                        return value.name
                      }else {
                        if (value.name.length > this.labelMaxLength) {
                          return value.name.substring(0, this.labelMaxLength) + '...'
                        } else {
                          return value.name
                        }
                      }
                    }

                   /* formatter:function(value){
                      return value.name
                    }*/
                  }
                }
              }
            },
            {
              zlevel: 2,
              name: '横向顶部涟漪圆点',
              data: brr,
              type: 'effectScatter',
              silent:true,
              symbolPosition: 'start',
              symbolSize: w8,
              symbolOffset: [-w4, 0],
              color: 'rgba(34,223,223,0.4)',
              rippleEffect: { //涟漪特效
                period:5, //动画时间，值越小速度越快
                brushType: 'stroke', //波纹绘制方式 stroke, fill
                scale: 4,//波纹圆环最大限制，值越大波纹越大
                number: 2,
              },
            },
            {
              zlevel: 3,
              name: '横向顶部圆点',
              type: 'scatter',
              data: brr,
              silent: true,
              symbolPosition: 'end',
              symbolSize: w8,
              symbolOffset: [-w4, 0],
              color: '#FFFFFF',
              animationDelay: 1500,
              animationDuration: 1000
            },
          ]
        })

        window.addEventListener('resize', this.changeResize)
      }
    },
    changeResize(){
      if (this.myChart){
        this.myChart.resize()
      }
    },
    getMaxArr(length){
      let arr=[]
      for (let i=0;i<length;i++){
        arr.push(this.max)
      }
      return arr
    }
  }
}
</script>

<style scoped lang="less">

</style>