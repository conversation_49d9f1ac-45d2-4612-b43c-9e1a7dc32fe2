<template>
  <a-card style='width: 100%; height: 100%'>
    <div slot='title'>
      <a-icon type='menu' />
      快捷入口
      <!--      <a-tooltip title='刷新'>-->
      <!--        <a-icon type='sync' style='color: #ccc; cursor: pointer; font-size: 14px' @click='loadBusiness' />-->
      <!--      </a-tooltip>-->
    </div>
        <a slot='extra' @click='setQuickEntrance'>
          <a-icon type='setting' @click.native='settingClick' />
        </a>
    <a-row :gutter="0" style='height: 100%'>
      <a-col :span="8" v-for='(item,idx) in quickMenus' :key='item.id' style='height: 50%;'>
        <div class='menu-item' @click='menuClick(item)'>
          <div class='menu-item-content'>
<!--            <a-icon :type="iconTypes[idx]" theme="twoTone" style='font-size: 32px' />-->
            <div class='menu-icon'>
              <img :src='`/img/quikMenu${idx+1}.png`'>
              <div class='first-text' :style='{color:colors[idx]}'>{{item.name.slice(0,1)}}</div>
            </div>
            <span style='margin-top: 12px'>  {{item.name}}</span>
          </div>
        </div>
      </a-col>
    </a-row>
    <quick-menu ref='menuSetting' :menu-list='menuList'  @ok='resertQuickEntryMenu' @selectedLen='selectedLen'></quick-menu>
  </a-card>
</template>

<script>
import Vue from 'vue'
import { ACCESS_TOKEN, PLATFORM_TYPE } from '@/store/mutation-types'
import { generateBigscreenRouter, generateIndexRouter } from '@/utils/util'
import store from '@/store'
import router from '@/router'
import { mapActions } from 'vuex'
import { RESET_MENUS} from "@/store/mutation-types"
import QuickMenu from '@views/customPages/components/quickMenu.vue'
import {getAction} from '@api/manage'

export default {
  name: 'QuickEntrance',
  components: { QuickMenu },
  data(){
    return {
      iconTypes:["bank","fire","flag","dashboard","heart","rocket"],
      menuList:[],
      selectedMenuList:[],
      defaultMenus:[],
      menusSource:[
        {
          "name": "设备信息",
          "platformType":1,
        },
        {
          "name": "我的消息",
          "platformType":3,
        },
        {
          "name": "资产信息",
          "platformType":6,
        },
        {
          "name": "知识库",
          "platformType":2,
        },
        {
          "name": "首页",
          "platformType":4,
        },
        {
          "name": "值班安排",
          "platformType":2,
        }
      ],
      quickMenus:[
      ],
      flatters:[],
      colors:['#579EF8',"#469A2D","#5DCAE9","#F2A042","#2D298D","#ED6A4D"],
    }
  },
  created() {
    this.getMenuList();
  },
  mounted() {
  },
  methods:{
    ...mapActions(['Logout', 'GetPermissionList']),
    //获取用户设置的快捷入口；
    getMenus(){
      getAction("/quick/entrance/queryMenu").then(res=>{
        if(res.success && res.result && res.result.length > 0){
          this.quickMenus = res.result;
          this.selectedMenuList = res.result;
          this.quickMenus.forEach(el=>{
            if(el.name === "首页"){
              let tem  = this.flatters.find(fel=>fel.name === el.name && fel.platformType == el.platformType)
              if(tem.platformTypeText){
                el.name = tem.platformTypeText;
              }
            }

          })
        }
        else{
          this.quickMenus = this.defaultMenus;
          this.selectedMenuList = []
        }
      })
    },
    getMenuList() {
      getAction("sys/permission/platformTypeList").then(res=>{
        if(res.success && res.result){
          res.result.forEach(el=>{
            let isRight = false;
            if(window.config.DataCenterType === 0 && el.value == 4){
              isRight = true;
            }else if(window.config.DataCenterType === 1 && el.value == 8){
              isRight = true;
            }
            else if(el.value != 7 && el.value != 9 && el.value != 4 && el.value != 8){
              isRight = true;
            }
            if(isRight){
              this.menuList.push({
                "id": el.value,
                "key": el.value,
                "title": el.title,
                "parentId": "",
                "name": el.title,
                "icon": "",
                "component": "",
                "url": "",
                "platformType":el.value,
                "platformTypeText": el.title,
                "children": [],
                "leaf": true,
                disabled: true,
              })
            }
          })
          getAction("/sys/permission/queryByUser").then(sRes=>{
            if(sRes.success && sRes.result){
              // console.log("获取到的菜单 == ",sRes)
              this.setDisabledMenu(sRes.result);
              sRes.result.forEach(el=>{
                let m = this.menuList.find(mel=>mel.platformType == el.platformType)
                if(m){
                  m.children.push(el)
                }
              })
              this.flatters = [];
              this.getFlatters(this.menuList);
              this.setDefaultMenus();
            }
          }).finally(()=>{
            this.getMenus();
          })
        }
      })
    },
    //拉平菜单
    getFlatters(list,platformTypeText){
      list.forEach(el=>{
        if(el.children && el.children.length > 0){
          this.getFlatters(el.children,el.platformTypeText||"")
        }else{
          el.platformTypeText = platformTypeText;
          this.flatters.push(el);
        }
      })
    },
    //设置不可选择菜单
    setDisabledMenu(list,type){
      list.forEach(el=>{
        if(el.children && el.children.length > 0){
          el.disabled = true;
          this.setDisabledMenu(el.children,type)
        }else{
          el.disabled = type == 1?true:false;
        }
      })
    },
    selectedLen(type){
      this.setDisabledMenu(this.menuList,type)
    },
    //默认的快捷菜单
    setDefaultMenus(){
        this.defaultMenus = [];
        this.menusSource.forEach(el=>{
          let tem  = this.flatters.find(fel=>fel.name === el.name && fel.platformType == el.platformType)
          if(tem){
            this.defaultMenus.push({
                "url": tem.url,
                "component": tem.component,
                "id": tem.id,
                "name": tem.name==="首页"?tem.platformTypeText:tem.name,
                "platformType":tem.platformType,
                "icon":"",
            })
          }
        })
    },
    setQuickEntrance(){

    },
    menuClick(item) {
      let index = item.platformType;
      if (sessionStorage.getItem(PLATFORM_TYPE) == index) {
        this.$router.push({
          path: item.url,
        })
        return
      }
      sessionStorage.setItem(PLATFORM_TYPE, index)
      const that = this
      that.GetPermissionList(index).then((res) => {
        if(res === "1"){
          this.$message.warning("没有添加菜单！")
          return
        };
        const menuData = res.result.menu
        var redirect = item.url;

        let constRoutes = []
        if (index == 4 || index == 8) {
          constRoutes = generateBigscreenRouter(menuData)
        } else {
          constRoutes = generateIndexRouter(menuData)
        }
        // 添加主界面路由
        store.dispatch('UpdateAppRouter', {
          constRoutes
        }).then(() => {
          // 根据roles权限生成可访问的路由表
          // 动态添加可访问路由表
          router.addRoutes(store.getters.addRouters)
          this.$store.commit(RESET_MENUS,true);
          this.$router.push({
            path: redirect
          })
          // }
        })
      })
    },
    resertQuickEntryMenu(menus){
      let idArr = menus.map(el=>el.id);
      let ids = idArr.join();
      getAction("/quick/entrance/add", { ids: ids }).then((res) => {
        if (res.success) {
          this.$message.success(res.message)
        } else {
          this.$message.warning(res.message)
        }
      }).finally(() => {
        this.getMenus();
      })
    },
    settingClick(){
      this.$refs.menuSetting.show(this.selectedMenuList)
    }
  }
}
</script>

<style scoped lang='less'>
.menu-item{
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  box-shadow: 1px 0 0 0 #f0f0f0, 0 1px 0 0 #f0f0f0, 1px 1px 0 0 #f0f0f0, 1px 0 0 0 #f0f0f0 inset, 0 1px 0 0 #f0f0f0 inset;
  .menu-item-content{
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #252631;
    font-size: 16px;
  }
  .menu-icon{
    width: 50px;
    height: 50px;
    position: relative;
    img{
      width: 100%;
      height: 100%;
    }
    .first-text{
      position: absolute;
      font-size: 20px;
      bottom: 0px;
      right: 0px;
      font-family: BDZYJT--GB1-0;
    }
  };
}
.menu-item:hover{
  z-index: 1;
  position: relative;
  box-shadow: 0 1px 2px -2px rgba(0, 0, 0, 0.16), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09);
}
::v-deep .ant-card-body{
  height: calc(100% - 62px);
  padding:0px;
}
</style>