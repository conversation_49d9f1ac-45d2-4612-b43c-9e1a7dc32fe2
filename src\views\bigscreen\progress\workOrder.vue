<template>
  <div class="body">
    <div class="header">
      <div class="header-right">
        <div class="header-right-time">
          <span>选择日期:</span>
          <div class="time-range-span">
            <a-range-picker dropdownClassName="big-screen-range-picker" v-model="this.todayTime" size="small"
              @change="onChange" />
          </div>
        </div>
        <button class="header-search">
          <a @click="search"> 查询 </a>
        </button>
      </div>
    </div>
    <div class="core">
      <div class="core-top">
        <div class="core-top-left">
          <div class="title">
            <img src="@/assets/bigScreen/9.png" alt="" />
            <span>工单处理数量TOP10</span>
          </div>
          <div class="core-top-left-body">
            <div class="core-top-left-body-Histogram" id="handleTopHistogram"></div>
          </div>
        </div>
        <div class="core-top-core">
          <div class="core-top-core-top">
            <div class="title">
              <img src="@/assets/bigScreen/9.png" alt="" />
              <span>工单信息统计</span>
            </div>
            <div class="core-top-core-top-body">
              <div class="core-top-core-top-body-item">
                <span class="core-top-core-top-body-item-title">{{ workOrderDataOne.name }}</span>
                <div class="core-top-core-top-body-item-value">
                  <animate-number from="0" :to="workOrderDataOne.value || 0" :key="workOrderDataOne.value"
                    duration="5000"></animate-number>
                </div>
              </div>
              <div class="core-top-core-top-body-item">
                <span class="core-top-core-top-body-item-title"> {{ workOrderDataTwo.name }}</span>
                <div class="core-top-core-top-body-item-value">
                  <animate-number from="0" :to="workOrderDataTwo.value || 0" :key="workOrderDataTwo.value"
                    duration="5000"></animate-number>
                </div>
              </div>
              <div class="core-top-core-top-body-item">
                <span class="core-top-core-top-body-item-title">{{ workOrderDataThree.name }}</span>
                <div class="core-top-core-top-body-item-value">
                  <animate-number from="0" :to="workOrderDataThree.value || 0" :key="workOrderDataThree.value"
                    duration="5000"></animate-number>
                  <span> h </span>
                </div>
              </div>
            </div>
          </div>
          <div class="core-top-core-bottom">
            <div class="title">
              <img src="@/assets/bigScreen/9.png" alt="" />
              <span>工单达标率统计</span>
            </div>
            <div class="core-top-core-bottom-body">
              <div class="core-top-core-bottom-body-Pie" id="orderCountPie"></div>
            </div>
          </div>
        </div>
        <div class="core-top-right">
          <div class="title">
            <img src="@/assets/bigScreen/9.png" alt="" />
            <span>平均处理时间TOP10</span>
          </div>
          <div class="core-top-right-body">
            <div class="core-top-right-body-Histogram" id="averageTopHistogram"></div>
          </div>
        </div>
      </div>
      <div class="core-bottom">
        <div class="core-bottom-left">
          <div class="title">
            <img src="@/assets/bigScreen/9.png" alt="" />
            <span>工单类型统计</span>
          </div>
          <div class="core-bottom-left-body">
            <div class="core-bottom-left-body-Pic" id="typeCountPie"></div>
          </div>
        </div>
        <div class="core-bottom-right">
          <div class="title">
            <img src="@/assets/bigScreen/9.png" alt="" />
            <span>工单数量统计</span>
          </div>
          <div class="core-bottom-right-body">
            <div class="core-bottom-right-body-Line" id="dayCountLine"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  import echarts from 'echarts'
  import vueSeamlessScroll from 'vue-seamless-scroll'
  import {
    getAction
  } from '@/api/manage'

  export default {
    data() {
      return {
        time1: '',
        time2: '',
        todayTime: [],
        url: {
          handleTop: '/data-analysis/order/handle/top',
          count: '/data-analysis/order/count',
          typeCount: '/data-analysis/order/type/count',
          dayCount: '/data-analysis/order/day/count',
          handleAverageTop: '/data-analysis/order/handle/average/top',
        },
        workOrderDataOne: '',
        workOrderDataTwo: '',
        workOrderDataThree: '',
      }
    },
    components: {
      vueSeamlessScroll,
    },
    created() {
      let moment = require('moment');
      let today = moment().format('YYYY-MM-DD')
      this.time1 = today
      this.time2 = today
      this.todayTime[0] = this.time1
      this.todayTime[1] = this.time2
    },
    mounted() {
      this.handleTop()
      this.count()
      this.handleAverageTop()
      this.typeCount()
      this.dayCount()
    },
    methods: {
      // 查询
      search() {
        this.handleTop()
        this.count()
        this.handleAverageTop()
        this.typeCount()
        this.dayCount()
      },

      onChange(dates, dateStrings) {
        this.todayTime = dateStrings
        this.time1 = dateStrings[0]
        this.time2 = dateStrings[1]
      },

      // 工单处理量柱状图数据
      handleTop() {
        getAction(this.url.handleTop, {
          time1: this.time1,
          time2: this.time2
        }).then((res) => {
          if (res.code == 200) {
            this.handleTopHistogram(res.result)
          }
        })
      },

      // 工单处理量柱状图
      handleTopHistogram(data) {
        let xArr = []
        let yArr = []
        data.forEach((e) => {
          xArr.push(e.name)
          yArr.push(e.value)
        })
        // let xArr = ['admin', 'user1', 'user2', 'user3', 'user4', 'user5', 'user6', 'user7', 'user8', 'user9', 'user10']
        // let yArr = [31, 28, 25, 23, 19, 18, 14, 13, 12, 11, 10]

        let myChart = this.$echarts.init(document.getElementById('handleTopHistogram'))
        myChart.setOption({
          title: {
            text: '单位(个)',
            textStyle: {
              color: '#4f92bf',
              fontSize: '14',
              fontWeight: 'normal',
            },
          },
          tooltip: {
            show: true,
          },
          xAxis: {
            type: 'category',
            splitLine: {
              show: false
            }, //去除网格线
            show: true,
            data: xArr,
            axisLine: {
              show: true,
              lineStyle: {
                color: '#4f92bf',
              },
            },
          },
          yAxis: {
            minInterval: 1,
            type: 'value',
            splitLine: {
              show: true,
              lineStyle: {
                color: '#223641',
              },
            },
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false, //y轴线消失
              lineStyle: {
                //y轴字体颜色
                color: '#4f92bf',
              },
            },
          },

          grid: {
            top: 40,
            left: 40, // 调整这个属性
            right: 10,
            bottom: 40,
          },
          series: [{
            data: yArr,
            type: 'bar',
            barWidth: 8, //柱图宽度
            showBackground: true,
            itemStyle: {
              normal: {
                color: '#188bf8',
              },
            },
            backgroundStyle: {
              color: 'rgba(255,255,255,0)',
            },
          }, ],
        })
        window.addEventListener("resize", () => {
          myChart.resize();
        });
      },

      formatter: function (num) {
        if (num && num != null && num != undefined) {
          return num.toFixed(2) //小数点后几位，数字就是几小数点后几位
        }
      },

      // 工单信息统计与工单达标率统计数据
      count() {
        getAction(this.url.count, {
          time1: this.time1,
          time2: this.time2
        }).then((res) => {
          if (res.code == 200) {
            this.workOrderDataOne = res.result.number[0]
            this.workOrderDataTwo = res.result.number[1]
            this.workOrderDataThree = res.result.number[2]
            this.orderCountPie(res.result.chart)
          }
        })
      },

      // 工单达标率统计环形图
      orderCountPie(pieData) {
        var titleArr = [],
          seriesArr = []

        pieData.forEach(function (item, index) {
          titleArr.push({
            text: item.name,
            left: index * 35 + 13 + '%',
            top: '80%',
            textAlign: 'center',
            textStyle: {
              fontWeight: 'normal',
              fontSize: '14',
              color: '#fff',
              textAlign: 'center',
            },
          })
        })

        seriesArr.push({
          name: pieData[0].name,
          type: 'pie',
          clockWise: false,
          radius: ['45%', '55%'],
          itemStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                  offset: 0,
                  color: '#08d4f7',
                },
                {
                  offset: 1,
                  color: '#c1f632',
                },
              ]),
              label: {
                show: false,
              },
              labelLine: {
                show: false,
              },
            },
          },
          hoverAnimation: false,
          center: [0 * 34 + 15.5 + '%', '45%'],
          data: [{
              value: pieData[0].value,
              label: {
                normal: {
                  formatter: function (params) {
                    return params.value + '%'
                  },
                  position: 'center',
                  show: true,
                  textStyle: {
                    fontSize: '16',
                    color: '#1cc7ff',
                  },
                },
              },
            },
            {
              value: 100 - pieData[0].value,
              name: 'invisible',
              itemStyle: {
                normal: {
                  color: '#014b70',
                },
                emphasis: {
                  color: '#014b70',
                },
              },
            },
          ],
        }, {
          name: pieData[1].name,
          type: 'pie',
          clockWise: false,
          radius: ['45%', '55%'],
          itemStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                  offset: 0,
                  color: '#08d4f7',
                },
                {
                  offset: 1,
                  color: '#c1f632',
                },
              ]),
              label: {
                show: false,
              },
              labelLine: {
                show: false,
              },
            },
          },
          hoverAnimation: false,
          center: [1 * 34 + 15.5 + '%', '45%'],
          data: [{
              value: pieData[1].value,
              label: {
                normal: {
                  formatter: function (params) {
                    return params.value + '%'
                  },
                  position: 'center',
                  show: true,
                  textStyle: {
                    fontSize: '16',
                    color: '#1cc7ff',
                  },
                },
              },
            },
            {
              value: 100 - pieData[1].value,
              name: 'invisible',
              itemStyle: {
                normal: {
                  color: '#014b70',
                },
                emphasis: {
                  color: '#014b70',
                },
              },
            },
          ],
        }, {
          name: pieData[2].name,
          type: 'pie',
          clockWise: false,
          radius: ['45%', '55%'],
          itemStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                  offset: 0,
                  color: '#08d4f7',
                },
                {
                  offset: 1,
                  color: '#c1f632',
                },
              ]),
              label: {
                show: false,
              },
              labelLine: {
                show: false,
              },
            },
          },
          hoverAnimation: false,
          center: [2 * 34 + 15.5 + '%', '45%'],
          data: [{
              value: pieData[2].value,
              label: {
                normal: {
                  formatter: function (params) {
                    return params.value + '%'
                  },
                  position: 'center',
                  show: true,
                  textStyle: {
                    fontSize: '16',
                    color: '#1cc7ff',
                  },
                },
              },
            },
            {
              value: 100 - pieData[2].value,
              name: 'invisible',
              itemStyle: {
                normal: {
                  color: '#014b70',
                },
                emphasis: {
                  color: '#014b70',
                },
              },
            },
          ],
        })

        let myChart = this.$echarts.init(document.getElementById('orderCountPie'))
        myChart.setOption({
          grid: {
            left: '5%',
            right: '2%',
            bottom: '0%',
            top: '0%',
            containLabel: true,
          },
          title: titleArr,
          series: seriesArr,
        })
        window.addEventListener("resize", () => {
          myChart.resize();
        });
      },

      // 平均处理时间柱状图数据
      handleAverageTop() {
        getAction(this.url.handleAverageTop, {
          time1: this.time1,
          time2: this.time2
        }).then((res) => {
          if (res.code == 200) {
            this.averageTopHistogram(res.result)
          }
        })
      },

      // 平均处理时间柱状图
      averageTopHistogram(data) {
        let xArr = []
        let yArr = []
        data.forEach((e) => {
          xArr.push(e.name)
          yArr.push(e.value)
        })
        let myChart = this.$echarts.init(document.getElementById('averageTopHistogram'))
        myChart.setOption({
          title: {
            text: '单位(小时)',
            textStyle: {
              color: '#4f92bf',
              fontSize: '14',
              fontWeight: 'normal',
            },
          },
          tooltip: {
            show: true,
            transitionDuration: 0, //echart防止tooltip的抖动
          },
          xAxis: {
            type: 'category',
            splitLine: {
              show: false
            }, //去除网格线
            show: true,
            data: xArr,
            axisLine: {
              show: true,
              lineStyle: {
                color: '#4f92bf',
              },
            },
          },
          yAxis: {
            type: 'value',
            splitLine: {
              show: true,
              lineStyle: {
                color: '#223641',
              },
            },
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false, //y轴线消失
              lineStyle: {
                //y轴字体颜色
                color: '#4f92bf',
              },
            },
          },

          grid: {
            top: 40,
            left: 40, // 调整这个属性
            right: 10,
            bottom: 40,
          },
          series: [{
            data: yArr,
            type: 'bar',
            barWidth: 8, //柱图宽度
            showBackground: false,
            itemStyle: {
              normal: {
                color: '#0feff1',
              },
            },
          }, ],
        })
        window.addEventListener("resize", () => {
          myChart.resize();
        });
      },

      // 工单类型统计饼状图数据
      typeCount() {
        getAction(this.url.typeCount, {
          time1: this.time1,
          time2: this.time2
        }).then((res) => {
          if (res.code == 200) {
            this.typeCountPie(res.result)
          }
        })
      },

      // 工单类型统计饼状图
      typeCountPie(data) {
        let myChart = this.$echarts.init(document.getElementById('typeCountPie'))
        myChart.setOption({
          tooltip: {
            show: true,
            transitionDuration: 0, //echart防止tooltip的抖动
          },
          label: {
            formatter(data) {
              let value = data.name + ':' + data.percent + '%'
              return value
            },
            color: '#fff',
          },
          legend: {
            orient: 'horizontal',
            top: 'bottom',
            left: 'center',
            icon: 'circle',
            textStyle: {
              color: '#fff',
            },
          },
          color: [
            '#177edd',
            '#64aec7',
            '#12a8a7',
            '#5dbc9c',
            '#29a84d',
            '#88bb5e',
            '#d7b017',
            '#c58353',
            '#d0405b',
            '#be71b5',
          ],
          series: [{
            hoverAnimation: false, // 取消掉饼图鼠标移上去时自动放大
            name: '工单类型统计',
            type: 'pie',
            radius: '50%',
            data: data,
          }, ],
        })
        window.addEventListener("resize", () => {
          myChart.resize();
        });
      },

      // 工单数据统计折线图数据
      dayCount() {
        getAction(this.url.dayCount, {
          time1: this.time1,
          time2: this.time2
        }).then((res) => {
          if (res.code == 200) {
            this.dayCountLine(res.result)
          }
        })
      },

      // 工单数据统计折线图
      dayCountLine(data) {
        let xArr = []
        let yArr = []
        data.forEach((e) => {
          xArr.push(e.name)
          yArr.push(e.value)
        })
        let myChart = this.$echarts.init(document.getElementById('dayCountLine'))
        myChart.setOption({
          tooltip: {
            show: true,
            trigger: 'axis',
            transitionDuration: 0, //echart防止tooltip的抖动
          },
          xAxis: [{
            type: 'category',
            boundaryGap: false,
            data: xArr,
            axisLabel: {
              show: true,
              textStyle: {
                color: '#5189ba', //更改坐标轴文字颜色
              },
            },
          }, ],
          yAxis: [{
            minInterval: 1,
            type: 'value',
            axisLabel: {
              show: true,
              textStyle: {
                color: '#5189ba', //更改坐标轴文字颜色
              },
            },
            axisLine: {
              show: false,
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: ['#1c2a37'],
                width: 2,
                type: 'solid',
              },
            },
          }, ],
          grid: {
            top: 40,
            right: 40,
            bottom: 40,
            left: 40,
          },
          series: [{
            name: '工单数量',
            type: 'line',
            areaStyle: {},
            emphasis: {
              focus: 'series',
            },
            data: yArr,
            itemStyle: {
              normal: {
                areaStyle: {
                  type: 'default',
                  color: new echarts.graphic.LinearGradient(
                    0,
                    0,
                    0,
                    1, //变化度
                    //两种种由深及浅的颜色
                    [{
                        offset: 0,
                        color: '#187da9',
                      },
                      {
                        offset: 1,
                        color: '#14323f',
                      },
                    ]
                  ),
                },
                color: '#05ccf5', //改变折线点的颜色
              },
            },
            lineStyle: {
              color: '#04bbdb', //改变折线颜色
            },
          }, ],
        })
        window.addEventListener("resize", () => {
          myChart.resize();
        });
      },
    },
  }
</script>
<style lang="less" scoped>
  .title {
    height: 14%;
    display: flex;
    align-items: center;
    font-size: 0.225rem
      /* 18/80 */
    ;
    color: #45c5e0;
    padding-left: 0.15rem
      /* 12/80 */
    ;
    letter-spacing: 0.025rem
      /* 2/80 */
    ;

    img {
      width: 0.125rem
        /* 10/80 */
      ;
      height: 0.1625rem
        /* 13/80 */
      ;
      margin-right: 0.0875rem
        /* 7/80 */
      ;
    }
  }

  .body {
    width: 100%;
    height: 100%;
    padding: 0 0.2rem 0.1rem 0.2rem;
    display: flex;
    flex-direction: column;

    .header {
      width: 100%;
      height: 7%;
      display: flex;
      justify-content: flex-end;

      .header-right {
        width: 34%;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        margin-right: 0.65rem
          /* 52/80 */
        ;

        .header-right-time {
          font-size: 0.175rem
            /* 14/80 */
          ;
          font-family: PingFang SC;
          letter-spacing: 0px;
          font-weight: 100;
          color: #ffffff;
          display: flex;
          align-items: center;

          .time-range-span {
            margin-right: 0.4375rem
              /* 35/80 */
            ;
            margin-left: 0.2rem
              /* 16/80 */
            ;
          }
        }

        .header-search {
          width: 0.85rem
            /* 68/80 */
          ;
          height: 0.4rem
            /* 32/80 */
          ;
          background: none;
          border: 2px solid #8f9094;
          border-radius: 10%;
          margin-right: 0.25rem
            /* 20/80 */
          ;

          a {
            color: #fff;
          }
        }
      }
    }

    .core {
      width: 100%;
      height: 93%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .core-top {
        width: 100%;
        height: 49%;
        display: flex;
        justify-content: space-between;

        .core-top-left {
          width: 33%;
          height: 100%;
          background: #111217;
          border-radius: 0.075rem
            /* 6/80 */
          ;

          .core-top-left-body {
            width: 100%;
            height: 86%;
            display: flex;
            align-items: center;
            justify-content: center;

            .core-top-left-body-Histogram {
              width: 100%;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }
        }

        .core-top-core {
          width: 33%;
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: space-between;

          .core-top-core-top {
            width: 100%;
            height: 47%;
            background: #111217;
            border-radius: 0.075rem
              /* 6/80 */
            ;

            .title {
              height: 28%;
            }

            .core-top-core-top-body {
              width: 100%;
              height: 72%;
              display: flex;
              align-items: center;
              justify-content: space-between;
              color: #fff;
              font-size: 0.2rem
                /* 16/80 */
              ;
              padding: 0.3rem
                /* 24/80 */
              ;

              .core-top-core-top-body-item {
                width: 33%;
                height: 100%;
                display: flex;
                flex-direction: column;
                align-items: center;

                .core-top-core-top-body-item-title {
                  display: flex;
                  width: 100%;
                  height: 20%;
                  padding-left: 0.25rem
                    /* 20/80 */
                  ;
                  border-left: 0.025rem
                    /* 2/80 */
                    solid #009bee;
                }

                .core-top-core-top-body-item-value {
                  width: 100%;
                  border-left: 0.025rem
                    /* 2/80 */
                    solid #172647;
                  height: 80%;
                  display: flex;
                  align-items: center;
                  padding-left: 0.25rem
                    /* 20/80 */
                  ;
                  color: #44eeff;
                  font-size: 0.45rem
                    /* 36/80 */
                  ;
                  font-family: 'zhenku';
                }
              }

              div:nth-child(1) {
                .core-top-core-top-body-item-title {
                  border: none;
                }

                .core-top-core-top-body-item-value {
                  border: none;
                }
              }

              div:nth-child(3) {
                .core-top-core-top-body-item-value {
                  color: #fec81e;
                  font-size: 0.3rem
                    /* 24/80 */
                  ;
                  font-family: none;
                }
              }
            }
          }

          .core-top-core-bottom {
            width: 100%;
            height: 51%;
            background: #111217;
            border-radius: 0.075rem
              /* 6/80 */
            ;

            .title {
              height: 22%;
            }

            .core-top-core-bottom-body {
              width: 100%;
              height: 72%;
              display: flex;
              align-items: center;
              justify-content: center;

              .core-top-core-bottom-body-Pie {
                width: 100%;
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
              }
            }
          }
        }

        .core-top-right {
          width: 33%;
          height: 100%;
          background: #111217;
          border-radius: 0.075rem
            /* 6/80 */
          ;

          .core-top-right-body {
            width: 100%;
            height: 86%;
            display: flex;
            align-items: center;
            justify-content: center;

            .core-top-right-body-Histogram {
              width: 100%;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }
        }
      }

      .core-bottom {
        width: 100%;
        height: 50%;
        display: flex;
        justify-content: space-between;

        .core-bottom-left {
          width: 49.7%;
          height: 100%;
          background: #111217;
          border-radius: 0.075rem
            /* 6/80 */
          ;

          .core-bottom-left-body {
            width: 100%;
            height: 86%;
            display: flex;
            justify-content: center;

            .core-bottom-left-body-Pic {
              width: 100%;
              height: 90%;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }
        }

        .core-bottom-right {
          width: 49.7%;
          height: 100%;
          background: #111217;
          border-radius: 0.075rem
            /* 6/80 */
          ;

          .core-bottom-right-body {
            width: 100%;
            height: 86%;
            display: flex;
            align-items: center;
            justify-content: center;

            .core-bottom-right-body-Line {
              width: 100%;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }
        }
      }
    }
  }

  ::v-deep .ant-calendar-picker {
    width: 3.95rem
      /* 316/80 */
    ;
    height: 100%;

    .ant-calendar-picker-input.ant-input {
      background-color: #101217;
      color: #909090;
      height: 100%;
      display: flex;
      align-items: center;

      .ant-calendar-range-picker-separator {
        color: #feffff;
        line-height: 0.375rem
          /* 30/80 */
        ;
      }
    }
  }
</style>