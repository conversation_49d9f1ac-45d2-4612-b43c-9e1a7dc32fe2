<template>
  <j-modal :title='title' :width='width' :visible='visible' :destroyOnClose='true'
           :okButtonProps="{style:{display:'none'}}" :centered='true'
           switchFullscreen
           @cancel='handleCancel'
           cancelText='关闭'>
    <div>
      <a-tabs v-model='curKey' @change="tabChange">

        <a-tab-pane key="1" tab="基本信息">
          <div style='width:100%;padding:0 5px'>
            <a-descriptions :column='{ xxl: 2, xl: 2, lg: 2, md: 2, sm: 2, xs: 2 }' bordered>
              <a-descriptions-item label='对接名称'>{{ record.systemName }}</a-descriptions-item>
              <a-descriptions-item label='采集模式'>{{ ['推模式', '拉模式'][record.collectMode] }}</a-descriptions-item>
              <a-descriptions-item label='对接系统标识'>{{ record.systemCode }}</a-descriptions-item>
              <a-descriptions-item label='数据类型'>{{ dataTypeStr }}</a-descriptions-item>
              <!--            <a-descriptions-item label='服务地址'>{{ record.urlPrefix }}</a-descriptions-item>-->
              <a-descriptions-item label='数据源'>{{record.collectMode===0?record.requestUrl:(record.abutmentTask?record.abutmentTask.requestUrl:"")}}</a-descriptions-item>
              <a-descriptions-item label='身份识别码'>{{ record.accessToken }}</a-descriptions-item>
              <a-descriptions-item label='采集总数'>{{ record.deviceTotal }}</a-descriptions-item>
              <a-descriptions-item label='采集成功数'>{{ record.deviceSuccess }}</a-descriptions-item>
              <a-descriptions-item label='采集失败数'>{{ record.deviceFailed }}</a-descriptions-item>
              <a-descriptions-item label='告警总数'>{{ record.alarmTotal }}</a-descriptions-item>
              <a-descriptions-item label='告警成功数'>{{ record.alarmSuccess }}</a-descriptions-item>
              <a-descriptions-item label='告警失败数'>{{ record.alarmFailed }}</a-descriptions-item>
              <a-descriptions-item v-if='record.collectMode === 1&&record.abutmentTask' label='任务名称'>{{ record.abutmentTask.taskName }}</a-descriptions-item>
              <a-descriptions-item v-if='record.collectMode === 1&&record.abutmentTask' label='监控系统ID'>{{ record.abutmentTask.systemId }}</a-descriptions-item>
              <a-descriptions-item v-if='record.collectMode === 1&&record.abutmentTask' label='网关'>{{ record.abutmentTask.gatewayCode }}</a-descriptions-item>
              <a-descriptions-item v-if='record.collectMode === 1&&record.abutmentTask' label='是否启用'>{{ ['否', '是'][record.abutmentTask.enable] }}</a-descriptions-item>
              <a-descriptions-item v-if='record.collectMode === 1&&record.abutmentTask' label='执行频率'>{{ record.abutmentTask.executeCron }}</a-descriptions-item>
              <a-descriptions-item v-if='record.collectMode === 1&&record.abutmentTask' label='请求类型'>{{ record.abutmentTask.requestType }}</a-descriptions-item>
              <a-descriptions-item label='描述'>{{ record.description }}</a-descriptions-item>
            </a-descriptions>
          </div>

        </a-tab-pane>
        <a-tab-pane key="2" tab="设备列表" force-render>
          <jkdj-devices v-if='curKey === "2"' :record='record'></jkdj-devices>
        </a-tab-pane>
        <a-tab-pane key="4" tab="采集记录" force-render>
         <jkdj-logs v-if='curKey === "4"' :record='record'></jkdj-logs>
        </a-tab-pane>
<!--        <a-tab-pane key="3" tab="告警列表">-->
<!--          <jkdj-alarms :record='record'></jkdj-alarms>-->
<!--        </a-tab-pane>-->
      </a-tabs>
    </div>
  </j-modal>
</template>
<script>
import JkdjDevices from '@views/jkdjgl/model/JkdjDevices.vue'
import JkdjAlarms from '@views/jkdjgl/model/JkdjAlarms.vue'
import { ajaxGetDictItem } from '@api/api'
import JkdjLogs from '@views/jkdjgl/model/JkdjLogs.vue'
export default {
  name: 'JkdjInfo',
  components: { JkdjLogs, JkdjAlarms, JkdjDevices },
  data() {
    return {
      title: '详情',
      width: '1200px',
      visible: false,
      disableSubmit: false,
      confirmLoading: false,
      record: {},
      datatypeList:[],
      curKey:"1",
    }
  },
  created() {
    this.getDatatype();
  },
  mounted() {

  },
  computed: {
    dataTypeStr() {
      let maparr = this.datatypeList.map(el=>el.text+"（"+el.value+"）");
      return maparr.join()
    }
  },
  methods: {
    getDatatype(){
      ajaxGetDictItem("jkdj_data_type").then((res) => {
        if(res.success){
          this.datatypeList = res.result
        }
      })
    },
    show(record) {
      this.record = record
      this.visible = true
    },
    handleCancel() {
      this.close()
    },
    handleOk() {
      this.close()
    },
    close() {
      this.visible = false
    },
    tabChange(){

    },
  }
}
</script>


<style scoped lang='less'>
@import '~@assets/less/normalModal.less';
//@import '~@assets/less/common.less';
//@import '~@assets/less/scroll.less';

///deep/.ant-table-tbody .ant-table-row td {
//  padding-top: 5px;
//  padding-bottom: 5px;
//}
/*
.colorBox1 {
  margin-bottom: 20px;
  margin-right: 1px;
}

.colorBox {
  margin-bottom: 10px;
}

.colorTotal {
  padding-left: 7px;
  border-left: 4px solid #1e3674;
}
*/

//::v-deep .ant-descriptions {
//  width: 100%;
//}
</style>