<template>
  <div style='background-color: #FFFFFF;height: 100%; border-radius: 3px;display: flex;flex-direction: column'>
    <div v-if='inputFlag' style='margin:20px 12px 10px 0'>
      <a-input-search @change='onChange' v-model='inputSearch' style='width: 100%' :placeholder='placeholder' />
    </div>
    <!-- 树-->
    <div class='hScroll vScroll' style='flex:auto;'
         :style="{ marginTop: inputFlag ? '5px' : '20px' }">
      <template>
        <div v-if='isShowAllBtn' style='margin-bottom:5px;'>
                    <span class='loadAllBtn' @click='loadAll' style='user-select: none' :style='{
                        backgroundColor: option == "" && type == "" ? "#ecf5ff" : "#fff",
                        color: option == "" && type == "" ? "#409eff" : "rgba(0, 0, 0, 0.65)",

                    }'>
                        <a-icon v-if="isShowBtnIcon" :type='btnIconName' style='color:#409eff' />
                        {{ btnName }}
                    </span>
        </div>
        <a-dropdown :trigger='[this.dropTrigger]' @visibleChange='dropStatus'>
          <a-tree :selectedKeys.sync='selectedKeys' :treeData='treeData' :expandedKeys='expandedKeys'
                  :showIcon='true' @expand='onExpand' @select='onSelect' style='user-select: none'>
            <template slot='title' slot-scope='{ title ,type, dataRef }'>
              <a-icon v-if="isShowIcon" :type='setIcon(type)' style='color:#409eff' />
              <a-icon v-if='dataRef && dataRef.info && dataRef.info.isFocus === 1' type="star" style="font-size: 10px; color: #f50; margin-right: 4px;" />
              <span v-if='title.indexOf(searchValue) > -1 && title !== firstTitle'>
                                {{ title.substr(1, title.indexOf(searchValue)) }}
                                <span style='color: #f50'>{{ searchValue }}</span>
                                {{ title.substr(title.indexOf(searchValue) + searchValue.length) }}
                            </span>
              <span v-else-if='title.indexOf(searchValue) > -1 && title === firstTitle'>
                                <span style='background-color: #bae7ff; color: #f50'>{{ title }}</span>
                            </span>
              <span v-else>{{ title }}</span>
            </template>
          </a-tree>

        </a-dropdown>
      </template>
      <a-empty v-if='treeData.length == 0' description='暂无关联指标' style="margin-top: 50px"></a-empty>
    </div>
  </div>
</template>
<script>
import {
  getAction,
} from '@/api/manage'

//通过Key获取对应地title
const getTitleByKey = (key, tree) => {
  let selectTitle
  for (let i = 0; i < tree.length; i++) {
    const node = tree[i]
    if (node.key === key) {
      selectTitle = node.title
      break
    }
    if (node.children) {
      selectTitle = getTitleByKey(key, node.children)
    }
  }
  return selectTitle
}
//子节点匹配，获取父节点，用于展开tree
const getParentKey = (key, tree) => {
  let parentKey
  for (let i = 0; i < tree.length; i++) {
    const node = tree[i]
    if (node.children) {
      if (node.children.some((item) => item.key === key)) {
        parentKey = node.key
      } else if (getParentKey(key, node.children)) {
        parentKey = getParentKey(key, node.children)
      }
    }
  }
  return parentKey
}
//生成tree节点的数组[{ key, title: node.title }]
const dataList = []
const generateList = (data) => {
  for (let i = 0; i < data.length; i++) {
    const node = data[i]
    dataList.push({
      key:node.key,
      title: node.title,
    })
    if (node.children) {
      generateList(node.children)
    }
  }
}
const changeTreeList = (data) => {
  for (let i = 0; i < data.length; i++) {
    const node = data[i]
    if(node.icon){
      node.icon = undefined
    }
    if (node.children) {
      changeTreeList(node.children)
    }
  }
}
//为tree生成对应地title slot
const generateTitleSlotScopes = (data) => {
  for (let i = 0; i < data.length; i++) {
    // var info = data[i];
    data[i].scopedSlots = {
      title: 'title'
    }
    let keys=Object.keys(data[i])
    data[i].disabled=keys.includes('userWithPermission') ? !data[i].userWithPermission:false
    if (data[i].children) {
      generateTitleSlotScopes(data[i].children)
    }
  }
}
export default {
  name: 'MetricsTree',
  props: {
    fieldKey: {
      type: String,
      default: 'key',
      required: false
    },
    placeholder: {
      type: String,
      default: '请输入类型名称',
      required: false
    },
    //加载资源按钮名称，调用页面可根据需要进行修改
    btnName: {
      type: String,
      default: '全部资源',
      required: false
    },
    //加载所有资源按钮默认不显示，根据调用需求，设置是否显示
    isShowAllBtn: {
      type: Boolean,
      default: false,
      required: false
    },
    //加载所有资源按钮是否添加图标
    isShowBtnIcon: {
      type: Boolean,
      default: false,
      required: false
    },
    //加载所有资源按钮图标名称
    btnIconName: {
      type: String,
      default: 'String',
      required: false
    },
    //树节点是否显示图标
    isShowIcon: {
      type: Boolean,
      default: true,
      required: false
    },
    //树节点type值数组，要与后端传的type值一一对应
    arrType: {
      type: Array,
      default: null,
      required: false
    },
    //根据type值，树节点应用不用的icon
    iconName: {
      type: Array,
      default: null,
      required: false
    },
    treeUrl: {
      type: String,
      default: '/assetscategory/assetsCategory/selectTree',
      required: false
    },
    params: {
      type: Object,
      default: () => {
        return {}
      },
      required: false
    },
    //是否显示搜索框
    inputFlag: {
      type: Boolean,
      default: true,
      required: false
    },
    callBackSelectedFun: {
      type: Function,
      default: null,
      required: false
    }
  },
  data() {
    return {
      firstTitle: '', //存储搜素tree的第一个title
      inputSearch: '',
      // 树
      option: '',
      type: '',
      treeData: [],
      expandedKeys: [],
      searchValue: '',
      autoExpandParent: true,
      dropTrigger: '',
      selectedKeys: [],
      selectedTitle: '',
      defaultSelectedKeys: [],
      curSelectedNode: undefined
    }
  },

  watch: {},
  mounted() {
    this.loadTree()
  },
  methods: {
    setIcon(type) {
      let name = '';
      if (this.arrType.length > 0) {
        for (let i = 0; i < this.arrType.length; i++) {
          if (this.arrType[i] == type) {
            name = this.iconName[i];
            break;
          }
        }
      }
      return name
    },
    //调用页面补充
    loadAll() {
      if (this.selectedKeys != []) {
        this.selectedKeys = []
      }
      this.option = '';
      this.type = ''
      this.$emit('selected', this.option, this.type)
    },
    // 查询树
   async loadTree() {
      var that = this
      that.treeData = []
     await getAction(this.treeUrl, this.params).then((res) => {
        if (res.success) {
          // 部门全选后，再添加部门，选中数量增多
          this.allTreeKeys = []
          if (res.result.length > 0) {
            generateTitleSlotScopes(res.result)
            that.treeData = [...res.result]
            changeTreeList(that.treeData)
          }
          this.loading = false
          generateList(that.treeData)
          if (that.callBackSelectedFun && (typeof that.callBackSelectedFun === 'function')) {
            this.$nextTick(() => {
              const selectedKeys = that.callBackSelectedFun(that.treeData)
              that.selectedKeys=[...selectedKeys]
            })
          }
        }
      })
    },
    // 右键点击下拉框改变事件
    dropStatus(visible) {
      if (visible == false) {
        this.dropTrigger = ''
      }
    },
    // 选择树的方法
    onSelect(selectedKeys, e) {
      //当前节点被选中，连着第二次点击时，selectedKeys会变为[],有点类似取消选中，
      if (selectedKeys.length > 0) {
        this.selectedKeys = selectedKeys
        this.firstTitle = ''
        this.option = e.selectedNodes[0].data.props.dataRef[this.fieldKey]
        this.type = e.selectedNodes[0].data.props.dataRef.type
        let title = e.selectedNodes[0].data.props.dataRef.title
        this.curSelectedNode = e.node
        //向父组件弹射抛值
        this.$emit('selected', this.option, this.type, title, e.selectedNodes[0].data.props.dataRef)
      }
    },
    //tree的查询框输入时，默认选中匹配的第一个（用firstTitle表示）
    onChange(e) {
      const value = e.target.value
      this.searchValue = value
      //查询框第一个匹配的node对应的key
      let firstSearchedKey = ''
      const expandedKeys = dataList
        .map((item) => {
          if (item.title && item.title.indexOf(value) > -1) {
            //查询框第一个匹配的node对应的key
            if (firstSearchedKey == '') {
              firstSearchedKey = item[this.fieldKey]
            }
            return getParentKey(item[this.fieldKey], this.treeData)
          }
          return null
        })
        .filter((item, i, self) => item && self.indexOf(item) === i)
      Object.assign(this, {
        expandedKeys,
        searchValue: value,
        autoExpandParent: true
      })
      if (this.expandedKeys.length > 0 && value.trim().length > 0) {
        this.firstTitle = getTitleByKey(firstSearchedKey, this.treeData)
        this.selectedKeys = [firstSearchedKey]
        //向父组件弹射抛值
        this.$emit('change', firstSearchedKey)
      }
      if (value.trim().length == 0) {
        //查询设备信息,此情况下，没有分类被选中
        this.firstTitle = ''
        this.selectedKeys = []
        //向父组件弹射抛值
        this.$emit('change')
      }
    },
    // 右键操作方法
    rightHandle(node) {
      this.dropTrigger = 'contextmenu'
      this.rightClickSelectedKey = node.node.eventKey
      this.rightClickSelectedBean = node.node.dataRef
    },
    onExpand(expandedKeys) {
      this.expandedKeys = expandedKeys
      this.autoExpandParent = true
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';

/*垂直滚动条*/
.vScroll::-webkit-scrollbar {
  margin-right: -24px !important;
}

/*水平滚动条*/
.hScroll::-webkit-scrollbar {
  margin-right: -24px !important;
  margin-left: -24px !important;
}

.loadAllBtn:hover {
  background-color: #ecf5ff !important;
  color: #409eff !important;
  cursor: pointer;
}

.loadAllBtn {
  padding-left: 5px;
  padding: 5px;
  height: 24px;
  line-height: 24px;
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1.5;
}
</style>