<template>
  <a-spin :spinning='confirmLoading'>
    <j-form-container>
      <a-form :form='form' slot='detail' :labelCol='labelCol' :wrapperCol='wrapperCol'>
        <!--资产状态---start-->
        <div v-if='assetsStatusList.length > 0'>
          <div class='colorBox'>
            <span class='colorTotal'>资产状态信息</span>
          </div>
          <a-row :gutter='32'>
            <a-col :span='12'>
              <a-form-item label='资产状态:'>
                <a-select
                  placeholder='请选择资产状态'
                  :getPopupContainer='(node) => node.parentNode'
                  :allowClear='true'
                  style='width: 100%'
                  v-decorator="['statusId']">
                  <a-select-option v-for='item in assetsStatusList' :value='item.id' :key="'assetsStatus_'+item.id"
                  >{{ item.name }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </div>
        <!--资产状态---end-->

        <!--资产信息---start-->
        <div key='assets-items'>
          <div class='colorBox'>
            <span class='colorTotal'>资产信息</span>
          </div>
          <a-row :gutter='32'>
            <a-col :span='12'>
              <a-form-item label='资产编号'>
                <a-input
                  :disabled='assetsAuto'
                  v-decorator="['assetsCode',{initialValue: autoAssetsCode,
                  rules: validatorRules.assetsCode.rules}]"
                  :allowClear='true'
                  autocomplete='off'
                  placeholder='请输入资产编号'
                />
              </a-form-item>
              <a-form-item label='资产类型'>
                <j-tree-select-expand
                  :disabled='syncDevice'
                  v-decorator="['assetsCategoryId', validatorRules.assetsCategoryId]"
                  placeholder='请选择资产类型'
                  dict='cmdb_assets_category,category_name,id'
                  pidField='parent_id'
                  condition='{"delflag":0,"category_state":"0"}'
                  pidValue='0'
                  @change='changeAssetsCategory($event)'
                />
              </a-form-item>
              <a-form-item class='two-words' label='型号'>
                <a-select :getPopupContainer='(node) => node.parentNode'
                          v-decorator="['assetsModel',validatorRules.assetsModel]"
                          mode="SECRET_COMBOBOX_MODE_DO_NOT_USE"
                          show-search
                          :autoClearSearchValue='true'
                          :allowClear='true'
                          placeholder='请选择或输入型号'>
                  <a-select-option v-for='(item,index) in assetsModelList' :key='"assetsModel_"+index'
                                   :value='item.model' :label='item.model'>
                    {{ item.model }}
                  </a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label='是否预警'>
                <a-radio-group v-decorator="['isWarning',{initialValue:'0'}]" @change='changeWarning($event)'>
                  <a-radio value='1'>是</a-radio>
                  <a-radio value='0'>否</a-radio>
                </a-radio-group>
              </a-form-item>
              <a-form-item v-if="assetsInfo.isWarning=== '1'" label='质保开始日期'>
                <j-date
                  placeholder='请选择质保开始日期'
                  v-decorator="['startQualityTime', validatorRules.startQualityTime]"
                  :trigger-change='true'
                  style='width: 100%'
                />
              </a-form-item>
              <a-form-item v-if="assetsInfo.isWarning=== '1'" label='保修单位'>
                <a-input rules: v-decorator="['repairFac', validatorRules.repairFac]" placeholder='请输入保修单位'
                         :allowClear='true' autocomplete='off' style='width: 100%' />
              </a-form-item>
              <a-form-item v-if="assetsInfo.isWarning=== '1'" label='保修联系人'>
                <a-input
                  v-decorator="['warrantyConnect', validatorRules.warrantyConnect]"
                  placeholder='请输入保修联系人'
                  :allowClear='true'
                  autocomplete='off'
                  style='width: 100%'
                />
              </a-form-item>
              <a-form-item label='关联合同'>
                <a-select
                  v-decorator="['contractId']"
                  show-search
                  :allow-clear='true'
                  placeholder="请选择合同名称"
                >
                  <a-select-option v-for='item in contractList' :key='"contract_" + item.id' :label='item.name'
                                   :value='item.id'>
                    {{ item.name }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span='12'>
              <a-form-item label='名称'>
                <a-input
                  v-decorator="['assetsName', validatorRules.assetsName]"
                  :allowClear='true'
                  autocomplete='off'
                  placeholder='请输入名称'
                ></a-input>
              </a-form-item>
              <a-form-model-item label='供应商'>
                <a-select
                  :getPopupContainer='(node) => node.parentNode'
                  :allowClear='true'
                  v-decorator="['producerId', validatorRules.producerId]"
                  placeholder='请选择供应商'
                  @change='changeProducer'
                >
                  <a-select-option v-for='item in supplierList' :key="'supplier_'+item.id" :value='item.id'
                  >{{ item.name }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
              <a-form-item label='入库日期'>
                <j-date
                  style='width: 100%'
                  placeholder='请选择入库日期'
                  v-decorator="['storageTime',{rules: [{ required: assetsInfo.isWarning==='1', message: '请选择入库日期' }]}]"
                />
              </a-form-item>
              <a-form-item label='关联设备' v-if='displaySyncDevice'>
                <a-switch v-model='syncDevice' :default-checked='true'
                          checkedChildren='是' unCheckedChildren='否'
                          @change='syncDeviceFun'></a-switch>
              </a-form-item>
              <a-form-item v-if="assetsInfo.isWarning=== '1'" label='质保期限(月)'>
                <a-input-number
                  :min='0'
                  :max='1000'
                  v-decorator="['qualityTerm', validatorRules.qualityTerm]"
                  placeholder='请输入质保期限(月)'
                  style='width: 100%'
                />
              </a-form-item>
              <a-form-item v-if="assetsInfo.isWarning=== '1'" label='保修单位电话'>
                <a-input
                  v-decorator="['repairPhone', validatorRules.repairPhone]"
                  placeholder='请输入保修单位电话'
                  :allowClear='true'
                  autocomplete='off'
                  style='width: 100%'
                />
              </a-form-item>
              <a-form-item v-if="assetsInfo.isWarning=== '1'" label='保修人电话'>
                <a-input
                  v-decorator="['warrantyPhone', validatorRules.warrantyPhone]"
                  placeholder='请输入保修人电话'
                  :allowClear='true'
                  autocomplete='off'
                  style='width: 100%'
                />
              </a-form-item>
            </a-col>
          </a-row>
        </div>
        <!--资产信息---end-->

        <!--附加字段---start-->
        <div v-if='additionalFieldList.length>0'>
          <div class='colorBox'>
            <span class='colorTotal'>附加字段</span>
          </div>
          <a-row :gutter='32'>
            <a-col :span='12' v-for='item in additionalFieldList' :key="'additionalField_'+item.id">
              <a-form-item :label='item.name'>
                <a-input
                  v-if="item.type == '计数器'"
                  v-decorator="[
                  `addCodes[${item.id}]`,
                  {
                    validateTrigger: ['change', 'blur'],
                    initialValue: item.value,
                    rules: [
                      {
                        required: item.isInput === 1,
                        whitespace: true,
                        message: '请输入' + item.name,
                      },
                    ],
                  },
                ]"
                  :placeholder="'请输入'+item.name"
                  type='number'
                />
                <a-input
                  v-else-if="item.type == '单行文本'"
                  v-decorator="[
                  `addCodes[${item.id}]`,
                  {
                    validateTrigger: ['change', 'blur'],
                    initialValue: item.value,
                    rules: [
                      {
                        required: item.isInput === 1,
                        whitespace: true,
                        message: '请输入' + item.name,
                      },
                    ],
                  },
                ]"
                  :placeholder="'请输入'+item.name"
                  type='text'
                />
                <a-input
                  v-else-if="item.type == '文本文档'||item.type == '多行文本'"
                  v-decorator="[
                  `addCodes[${item.id}]`,
                  {
                    validateTrigger: ['change', 'blur'],
                    initialValue: item.value,
                    rules: [
                      {
                        required: item.isInput === 1,
                        whitespace: true,
                        message: '请输入' + item.name,
                      },
                    ],
                  },
                ]"
                  :placeholder="'请输入'+item.name"
                  type='textarea'
                />
                <j-dict-select-tag
                  v-else-if="item.type == '下拉框'"
                  type='list'
                  v-decorator="[
                  `addCodes[${item.id}]`,
                  {
                    validateTrigger: ['change', 'blur'],
                    initialValue: item.value,
                    rules: [
                      {
                        required: item.isInput === 1,
                        whitespace: true,
                        message: '请选择' + item.name,
                      },
                    ],
                  },
                ]"
                  :trigger-change='true'
                  :dictCode='item.dictType'
                  :placeholder="'请选择'+item.name"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </div>
        <!--附加字段---end-->

        <!--设备参数---start-->
        <div v-if='syncDevice' key='device-items'>
          <div class='colorBox'>
            <span class='colorTotal'>设备参数</span>
          </div>
          <a-row :gutter='32'>
            <a-col :span='12'>
              <a-form-item label='产品名称:'>
                <a-tree-select
                  v-decorator="['productId', validatorRules.productId]"
                  :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                  :allow-clear='true'
                  show-search
                  tree-node-filter-prop='title'
                  placeholder='请选择产品名称'
                  @select='selectProductName'
                  @change='changeProductName'
                  :tree-data='productList'
                  tree-icon>
                </a-tree-select>
              </a-form-item>
              <a-form-item label='设备标识'>
                <a-input
                  v-decorator="['deviceCode', validatorRules.deviceCode]"
                  placeholder='请输入设备唯一标识'
                  :allowClear='true'
                  autocomplete='off'
                  :disabled='canEditDevCode'
                />
              </a-form-item>
              <a-form-item label='设备说明'>
                <a-textarea v-decorator="['description', validatorRules.description]"
                            :autoSize='{minRows:2,maxRows:4}' placeholder='请输入设备说明'
                            :allowClear='true' />
              </a-form-item>
            </a-col>
            <a-col :span='12'>
               <a-form-item label="规格">
                <a-input
                  v-decorator="['specifications', validatorRules.specifications]"
                  placeholder="请输入设备的长宽高 (例:120*90*80)"
                  :allowClear="true"
                  autocomplete="off"
                  suffix="mm"
                />
              </a-form-item>
              <a-form-item label="占用容量">
                <a-input
                  v-decorator="['capacity', validatorRules.capacity]"
                  placeholder="请输入放置设备所要占用的容量"
                  :allow-clear="true"
                  autocomplete="off"
                  :maxLength="10"
                  suffix="U位"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <!--设备参数---end-->
          <!-- 设备位置---start -->
          <div class='colorBox'>
            <span class='colorTotal'>设备位置</span>
          </div>
          <a-row :gutter='32'>
            <a-col :span='12'>
              <a-form-item label='机房'>
                <a-tree-select
                  v-decorator="['roomId', validatorRules.roomId]"
                  :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                  :allow-clear='true'
                  show-search
                  tree-node-filter-prop='title'
                  placeholder='请选择机房'
                  :replace-fields="{children:'children', title:'name', key:'id', value: 'id',icon:'icon' }"
                  @select='selectRoom'
                  @change='changeRoom'
                  :tree-data='roomList'
                  tree-icon>
                </a-tree-select>
              </a-form-item>
               <a-form-item label='U位'>
                <a-select
                  v-decorator="['layerPool',{rules:  [{required: roomNodeType==='room', validator: this.validateLayerPool, trigger: 'blur'}]}]"
                  :allow-clear='true'
                  :maxTagCount='3'
                  :maxTagTextLength="3"
                  :show-search='true'
                  mode='multiple'
                  option-filter-prop='label'
                  placeholder='请选择U'
                >
                  <a-select-option v-for='(item,index) in layerPoolList' :key='"layerPool_"+index' :label='item'
                                   :value='item'>
                    {{ item }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span='12'>
              <a-form-item label='机柜'>
                <a-select
                  v-decorator="['cabinetId',{rules: [{ required: roomNodeType==='room', validator: this.validateCabinet, trigger: 'blur' }]}]"
                  :allow-clear='true'
                  :show-search='true'
                  option-filter-prop='label'
                  placeholder='请选择机柜'
                  @change='changeCabinet'
                >
                  <a-select-option v-for='item in cabinetList' :key='"cabinet_"+item.id' :label='item.name'
                                   :value='item.id'>
                    {{ item.name }}
                  </a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="所在位置">
                <a-input
                  v-decorator="['location', validatorRules.location]"
                  placeholder="请填写所在位置"
                  :allowClear="true"
                  autocomplete="off"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </div>
        <!-- 设备位置---end -->

        <!--连接参数---start-->
        <div v-if='collectInfoList.length > 0'>
          <div v-for='(item, index) in collectInfoList' :key='index'>
            <div class='colorBox'>
              <span class='colorTotal'>{{ item.key }}连接参数</span>
            </div>
            <a-row :gutter='32'>
              <a-col :span='12' v-for='(item1, idx) in item.value' :key='idx'>
                <a-form-item :label='item1.displayName'>
                  <div style='white-space: nowrap'>
                    <a-input
                      v-if="item1.controlType == 'inputString'"
                      v-decorator="[
                          item1.connectName + '_' + idx + '_' + index,
                          {
                            initialValue: item1.connectValue,
                            rules: [{ required: assetsOperateType?false:true, message: '请输入' + item1.displayName }],
                          },
                        ]"
                      :placeholder="'请输入'+item1.displayName"
                      :allowClear='true'
                      autocomplete='off'
                      @change='changeConParam($event, index, idx)'
                    ></a-input>
                    <j-dict-select-tag
                      v-if="item1.controlType == 'inputSelect'"
                      v-decorator="[
                          item1.connectName + '_' + idx + '_' + index,
                          {
                            initialValue: item1.connectValue,
                            rules: [{ required: assetsOperateType?false:true, message: '请输入' + item1.displayName }],
                          },
                        ]"
                      :dictCode='item1.dictType'
                      :placeholder="'请选择'+item1.displayName"
                      :trigger-change='true'
                      @change='changeConParam1($event, index, idx)'
                      :allowClear='true'
                    />
                    <a-input-password
                      v-if="item1.controlType == 'inputPassword'"
                      v-decorator="[
                          item1.connectName + '_' + idx + '_' + index,
                          {
                            initialValue: item1.connectValue,
                            rules: [{ required: assetsOperateType?false:true, message: '请输入' + item1.displayName }],
                          },
                        ]"
                      :placeholder="'请输入'+item1.displayName"
                      :allowClear='true'
                      autocomplete='off'
                      @change='changeConParam($event, index, idx)'
                    >
                    </a-input-password>
                    <a-input-number
                      v-if="item1.controlType == 'inputNumber'"
                      v-decorator="[
                          item1.connectName + '_' + idx + '_' + index,
                          {
                            initialValue: item1.connectValue,
                            rules: [{ required: assetsOperateType?false:true, message: '请输入' + item1.displayName }],
                          },
                        ]"
                      :placeholder="'请输入'+item1.displayName"
                      :allowClear='true'
                      autocomplete='off'
                      @change='changeConParam1($event, index, idx)'
                    ></a-input-number>
                    <a-popover title='备注' v-if="item1.description != null && item1.description != ''">
                      <template slot='content'>
                        <p>{{ item1.description }}</p>
                      </template>
                      <a-icon type='question-circle' theme='twoTone'
                              style='font-size: 18px; line-height: 45px;margin-left: 10px' />
                    </a-popover>
                  </div>
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </div>
        <!--连接参数---end-->

        <!--附件、历史、关联资产---start-->
        <a-col :span='24' v-if='assetsInfo.id&&assetsInfo.id.length>0'>
          <div class='tabs-container'>
            <a-tabs :animated='false' default-active-key='1' class='device-tabs'>
              <a-tab-pane key='1' tab='附件' force-render>
                <div class='clearfix'>
                  <j-upload v-model='filesList' :number='5'></j-upload>
                </div>
              </a-tab-pane>
              <a-tab-pane key='2' tab='历史' v-if='!assetsOperateType'>
                <assets-history-list ref='historyList' :assetsId='assetsInfo.id'></assets-history-list>
              </a-tab-pane>
              <a-tab-pane key="3" tab="关系" v-if='!assetsOperateType'>
                <div class="relation-btn-div">
                  <a-button class="add-btn" @click="handleTopoAdd">添加关系</a-button>
                  <!-- <a-button class="enlarge-btn" @click="enlargeTopo(assetsId)">放大按钮</a-button> -->
                  <a @click="enlargeTopo(assetsInfo.id)" class="enlargeButton">
                    <a-icon type="fullscreen" />
                  </a>
                </div>
                <div style="height: 200px; width: 100%">
                  <relation-topo
                    ref="relation"
                    :id="'editContainer'"
                    :assetsId="assetsInfo.id"
                    :refresh="refreshTopo"
                    :showDelAdd='true'
                    @transmit="transmit"
                    @addAssets="addAssociatedAssets"
                  ></relation-topo>
                </div>
              </a-tab-pane>
              <a-tab-pane key='4' tab='关联流程' v-if='!assetsOperateType'>
                <asset-association-process-list ref='processList'
                                                :assets-info='assetsInfo'></asset-association-process-list>
              </a-tab-pane>
            </a-tabs>
          </div>
        </a-col>
        <!--附件、历史、关联资产---end-->
      </a-form>
    </j-form-container>
    <relation-topo-modal ref="topoModal"></relation-topo-modal>
    <add-asets-modal ref="addAsetsoModal" @ok="doRefresh"></add-asets-modal>
  </a-spin>
</template>

<script>
import AssetsHistoryList from './AssetsHistoryList.vue'
import { httpAction, getAction } from '@/api/manage'
import pick from 'lodash.pick'
import { phoneValidator } from '@/mixins/phoneValidator'
import { queryPostionTreeList } from '@api/device'
import AssetAssociationProcessList from '@views/cmdb/assets/modules/AssetsAssociationProcessList.vue'
import { queryConfigureDictItem } from '@/api/api'
import AddAsetsModal from '@views/cmdb/assets/modules/AddAsetsModal.vue'
import RelationTopo from '@views/cmdb/assets/modules/relationTopo/RelationTopo.vue'
import RelationTopoModal from '@views/cmdb/assets/modules/relationTopo/RelationTopoModal.vue'
export default {
  name: 'AssetsForm',
  mixins: [phoneValidator],
  components: {
    AssetAssociationProcessList,
    AssetsHistoryList,
    AddAsetsModal,
    RelationTopo,
    RelationTopoModal
  },
  props: {
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    },
    // 资产变更--临时变更id
    tempId: {
      type: String,
      default: ''
    },
    // 资产操作类型
    assetsOperateType: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
      form: this.$form.createForm(this, { name: 'asset_info_form' }),
      labelCol: {
        xs: { span: 24 },
        sm: { span: 24 },
        md: { span: 6 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 24 },
        md: { span: 16 }
      },
      confirmLoading: false,

      displaySyncDevice: false,//是否显示同步、关联设备
      syncDevice: true,//是否同步、关联设备
      autoAssetsCode: '',//自动生成的资产编号
      assetsAuto: window._CONFIG['assetsAuto'],//配置：资产编号是否自动生产
      assetsStatusList: [],//资产状态下拉数据
      additionalFieldList: [],//附加字段
      supplierList: [],//供应商下拉数据
      assetsModelList: [],//资产型号下拉数据
      downloadFiles: [],
      filesList: [],//附件

      productNodeCatId: '',//记录最后一级产品分类id
      productNodeType: undefined,//记录当前产品名称所选节点类型（产品分类、产品）
      productNodeTitle: null, //记录当前产品名称所选节点名称
      productList: [],//产品分类及产品树结构数据
      collectInfoList: [],//连接参数
      canEditDevCode: false,//控制设备标识是否可编辑

      roomNodeType: '',//房间物理位置所选的最后一级数据类型（notRoom/room）
      roomList: [],//房间物理位置数据
      cabinetList: [],//房间里的机柜
      layerPoolList: [],//U下拉数据
      hisCabinetId: undefined,//编辑情况下，记录开始的机柜id
      hisLayerPoolList: [],//编辑情况下，记录开始U的下拉数据

      validatorRules: {
        assetsCategoryId: {
          rules: [
            { required: true, message: '请选择资产类型' }
          ]
        },
        assetsName: {
          rules: [
            { required: true, message: '请输入名称' },
            { min: 2, message: '名称长度应在 2-50 之间', trigger: 'blur' },
            { max: 50, message: '名称长度应在 2-50 之间', trigger: 'blur' }
          ]
        },
        assetsCode: {
          rules: [
            { required: true, message: '请输入资产编号' },
            { min: 2, message: '资产编号长度应在 2-20 之间', trigger: 'blur' },
            { max: 20, message: '资产编号长度应在 2-20 之间', trigger: 'blur' }
          ]
        },
        producerId: {
          rules: [
            { required: true, message: '请选择供应商' }
          ]
        },
        assetsModel: {
          rules: [
            { required: true, message: '请选择或输入型号' },
            { min: 2, max: 100, message: '型号长度在2-100个字符之间' },
          ]
        },
        storageTime: {
          rules: [
            { required: true, message: '请输入入库日期' }
          ]
        },
        startQualityTime: {
          rules: [
            { required: true, message: '请输入质保开始日期' }
          ]
        },
        qualityTerm: {
          rules: [
            { required: true, message: '请输入质保期限(月)' }
          ]
        },
        repairFac: {
          rules: [
            { required: true, message: '请输入保修单位' },
            { pattern: /^[\u4E00-\u9FA50-9a-zA-Z]{1,50}$/, message: '保修单位由50个以内的中英文、数字组成' }
          ]
        },
        repairPhone: {
          rules: [
            { required: true, message: '请输入保修单位电话' },
            { validator: this.phone }
          ]
        },
        warrantyConnect: {
          rules: [
            { required: true, message: '请输入保修联系人' },
            { pattern: /^[\u4E00-\u9FA5]{1,10}$/, message: '保修联系人由10个以内的汉字组成' }
          ]
        },
        warrantyPhone: {
          rules: [
            { required: true, message: '请输入保修人电话' },
            { validator: this.phone }
          ]
        },
        productId: {
          rules: [
            { required: true, validator: this.validateProduct, trigger: 'blur' }
          ]
        },
        deviceCode: {
          rules: [
            { required: true, message: '请输入设备标识'},
            {
              min: 4,
              max: 32,
              pattern: /^(([a-zA-Z]+|[0-9]+|[a-zA-Z0-9-.:]){4,32})$/,
              message: '可包含字母、数字、英文横线或英文句号,4-32个字符'
            }
          ]
        },
        capacity: {
          rules: [
            {
              required: false,pattern: /^[1-9]{1}[0-9]*$/,message: '容量可为空、或请输入≥1的整数'
            }
          ]
        },
        specifications:{
          rules: [
            {
              required: false,pattern: /^[1-9]\d*\*[1-9]\d*\*[1-9]\d*$/,message: '请输入正确格式，例：120*90*80'
            }
          ]
        },
        roomId: {
          rules: [
            { required: false, validator: this.validateRoom, trigger: 'blur' }
          ]
        },
        description: {
          rules: [
            { required: false, min: 0, max: 200, message: '设备说明长度应在 0-200 之间' }]
        },
        location: {
          rules: [
            { required: false, min: 3, max: 25, message: '所在位置长度应在 3-25 之间' }]
        },
      },
      url: {
        add: '/device/deviceInfo/addAssets',
        edit: '/device/deviceInfo/editAssets',

        queryFilesByAssetsId: '/assets/assets/queryByIds',//编辑时，根据资产id获取附件
        queryStatusByAssetsCategoryId: 'assets/assets/queryStatusByAssetsCategoryId',//根据资产类型id，获取资产状态下拉数据
        findCodeName: '/extendField/extendField/findCodeName',//切换资产时，通过资产类型的id.获取附加字段
        findCodeValue: 'extendField/extendField/findCodeValue',//编辑：通过资产类型的id.资产信息id获取附加字段
        generateCode: '/CodeRuleSetting/codeRuleSetting/generateCodeByBiz', //自动生成资产编号
        supplier:'/supplier/cmdbSupplier/queryAllSupplier',//获取供应商下拉数据
        contract: '/category/contract/list', // 获取合同列表
        queryAllProduct: '/assetscategory/assetsCategory/selectTree', //获取所有的产品
        queryCollectInfoList: '/device/deviceInfo/selectConnectInfo',//1、新增，通过产品id.获取连接参数；2、编辑，通过设备id.获取连接参数
        cabinet: '/topo/room/cabinet',//根据机房id，获取机柜
        layerPool: '/topo/cabinet/getFreeLayerList',//根据机柜id，获取机柜剩余u
        queryDeviceAndPositionInfo: '/device/deviceInfo/selectDeviceInfoAndPositionInfo',//编辑，根据资产id，获取设备信息和设备位置信息
        querAssetsModelList: '/itInnovate/cmdbItInnovate/list',//根据产品类型/资产类型，供应商获取资产型号下拉数据

        assetsChangeAdd: '/device/deviceInfoAct/addAssetsAct', // 资产变更-新增临时数据
        assetsChangeEdit: '/device/deviceInfoAct/edit', // 资产变更-编辑临时数据
        editHasAssets: '/device/deviceInfoAct/editAssetsAct',  // 资产变更-编辑已有资产
        queryTempFilesByAssetsId: '/device/deviceInfoAct/queryByIds', //编辑临时数据时，根据资产id获取附件
        findTempCodeValue: '/device/deviceInfoAct/getAssetsExtend', // 查临时资产的附加字段
        queryTempDeviceAndPositionInfo: '/device/deviceInfoAct/selectDeviceInfoAndPositionInfo' // 查临时资产关联设备的连接参数
      },
      assetsInfo: {}, // 资产基础信息
      assetsId: '', // 资产id--资产变更用
      deviceInfo: {},
      refreshTopo: false,
      relationList: [],
      contractList: [] // 关联合同列表
    }
  },

  methods: {
    /***************资产新增、编辑初始化***************/
    add() {
      this.edit({})
    },
    edit(record) {
      this.assetsInfo = JSON.parse(JSON.stringify(record))
      // 设置资产id
      if (this.tempId && !!this.assetsOperateType && this.assetsOperateType !== 'editHasAssets') {
        this.assetsId = record.assetsId
      } else {
        this.assetsInfo['id'] = record.id
	      this.assetsId = record.id
      }
      this.visible = true
      this.confirmLoading = true
      Promise.all([this.getAllSupplier(), this.queryAllProduct(), this.loadRoomTree(), this.getContract()]).then((res) => {
        let pass = res.every((item, index) => {
          if (!item.success) {
            this.$message.warning(item.message)
          }
          return item.success == true
        })
        if (pass) {
          this.form.resetFields()
          //新增
          if (!this.assetsInfo.id) {
            this.assetsInfo['isWarning'] = '0'
            this.autoAssetsCode = ''
            if(this.assetsAuto){
              Promise.all([this.getGenerateAssetsCode(),this.getConfigDictAboutDisSyncDevice(), this.getConfigDictAboutSyncDevice()]).then((res1) => {
                this.confirmLoading = false
              }).catch(()=>{
                this.confirmLoading = false
              })
            }else {
              Promise.all([this.getConfigDictAboutDisSyncDevice(), this.getConfigDictAboutSyncDevice()]).then((res1) => {
                this.confirmLoading = false
              }).catch(()=>{
                this.confirmLoading = false
              })
            }
          }
          //编辑
         else if (this.assetsInfo.id) {
            Promise.all([this.getConfigDictAboutDisSyncDevice(),this.getAssetsStatusByAssetsCategoryId(record.assetsCategoryId),
              this.getAdditionalFieldByCarIdAndAssetsId(record),
              this.getFilesByAssetsId(this.assetsInfo.id),
              this.getDeviceAndPositionInfo(this.assetsInfo.id)
            ]).then((res1) => {
              let pass2 = res1.every((item, index) => {

                if (!item.success) {
                  this.$message.warning(item.message)
                }
                return item.success == true
              })
              if (pass2) {
                this.getAssetsModelList(record.assetsCategoryId, record.producerId)
                this.setAssetsInfo(record)
                this.confirmLoading = false
              }
            }).catch(()=>{
              this.confirmLoading = false
            })
          }
        }
      })
    },

    /*新增：获取配置字典，设置是否默认同步、关联设备*/
    getConfigDictAboutSyncDevice() {
      return new Promise((resolve, reject) => {
        queryConfigureDictItem({
          parentCode: 'assetDeviceAutosyn',
          childCode: 'syncDevice'
        }).then((res) => {
          if (res.success) {
            this.syncDevice = res.result == '1' ? true : false
            resolve({ success: true, message: res.message })
          } else {
            this.syncDevice = false
            resolve({ success: true, message: '操作成功' })
          }
        }).catch((err) => {
          this.syncDevice = false
          resolve({ success: true, message: '操作成功' })
        })
      })
    },
    /*获取配置字典，是否显示同步至资产切换按钮*/
    getConfigDictAboutDisSyncDevice() {
      return new Promise((resolve, reject) => {
        queryConfigureDictItem({
          parentCode: 'assetDeviceDisplayAutosyn',
          childCode: 'displaySyncDevice'
        }).then((res) => {
          if (res.success) {
            this.displaySyncDevice = res.result == '1' ? true : false
            resolve({ success: true, message: res.message })
          } else {
            this.displaySyncDevice = false
            resolve({ success: true, message: '操作成功' })
          }
        }).catch((err) => {
          this.displaySyncDevice = false
          resolve({ success: true, message: '操作成功' })
        })
      })
    },
    /*编辑：处理回显资产相关信息*/
    setAssetsInfo(assetsInfo) {
      if (assetsInfo && Object.keys(assetsInfo)) {
        this.assetsInfo['isWarning'] = assetsInfo.isWarning
        if (!assetsInfo.producerId) {
          assetsInfo.producerId = undefined
        }
        if (!assetsInfo.assetsModel) {
          assetsInfo.assetsModel = undefined
        }
        if (!assetsInfo.contractId) {
          assetsInfo.contractId = undefined
        }
        if (this.assetsStatusList.length === 0 || !assetsInfo.statusId) {
          assetsInfo.statusId = undefined
        }

        this.$nextTick(() => {
          this.$nextTick(() => {
            if (this.assetsInfo.isWarning === '1') {
              this.form.setFieldsValue(pick(assetsInfo,
                'statusId', 'assetsCode', 'assetsName', 'assetsCategoryId', 'producerId', 'assetsModel', 'contractId',
                'storageTime', 'isWarning', 'startQualityTime', 'qualityTerm',
                'repairFac', 'repairPhone', 'warrantyConnect', 'warrantyPhone'))
            } else {
              this.form.setFieldsValue(pick(assetsInfo,
                'statusId', 'assetsCode', 'assetsName', 'assetsCategoryId', 'producerId', 'assetsModel', 'storageTime', 'isWarning', 'contractId'
              ))
            }
          })
        })
      }
    },
    /*编辑：根据资产类型id和资产id获取附加字段*/
    getAdditionalFieldByCarIdAndAssetsId(assetsInfo) {
      return new Promise((resolve, reject) => {
        this.additionalFieldList = []
        if (assetsInfo && Object.keys(assetsInfo)) {
          if (assetsInfo.assetsCategoryId && this.assetsInfo.id) {

            let httpurl = ''
            let paramObj = {}
            if (this.assetsOperateType == 'editTempAssets') {
              // 资产变更用：编辑临时表数据
              httpurl += this.url.findTempCodeValue
              paramObj = {
                assetsCategoryId: assetsInfo.assetsCategoryId.trim(),
                assetsActId: this.assetsInfo.id.trim()
              }
            } else {
              httpurl += this.url.findCodeValue
              paramObj = {
                assetsCategoryId: assetsInfo.assetsCategoryId.trim(),
                assetsId: this.assetsInfo.id.trim()
              }
            }
            getAction(httpurl, paramObj).then((res) => {
              if (res.success) {
                this.additionalFieldList = res.result
                console.log('this.additionalFieldList===',this.additionalFieldList)
                resolve({ success: true, message: res.message })
              } else {
                reject({ success: false, message: res.message })
              }
            }).catch((err) => {
              reject({ success: false, message: err.message })
            })
          }
        } else {
          resolve({ success: true, message: '请求成功' })
        }
      })
    },
    /*编辑时，根据资产id获取附件*/
    getFilesByAssetsId(assetsId) {
      return new Promise((resolve, reject) => {
        let httpurl = ''
        if (this.assetsOperateType == 'editTempAssets') {
          // 资产变更用：编辑临时表数据
          httpurl += this.url.queryTempFilesByAssetsId
        } else {
          httpurl += this.url.queryFilesByAssetsId
        }

        getAction(httpurl, { id: this.assetsInfo.id }).then((res) => {
          if (res.success) {
            this.filesList = res.result.fileUrlList
            resolve({ success: true, message: res.message })
          } else {
            reject({ success: false, message: '请求失败' })
          }
        }).catch((err) => {
          reject({ success: false, message: err.message })
        })

      })
    },
    /*编辑：通过资产id，获取设备信息、及物理位置信息*/
    getDeviceAndPositionInfo(assetsId) {
      //this.syncDevice=false
      return new Promise((resolve, reject) => {
        let httpurl = ''
        let paramObj
        if (this.assetsOperateType == 'editTempAssets') {
          // 资产变更用：编辑临时表数据
          httpurl += this.url.queryTempDeviceAndPositionInfo
          paramObj = { assetsActId: this.assetsInfo.id.trim() }
        } else {
          httpurl += this.url.queryDeviceAndPositionInfo
          paramObj = { assetsId: assetsId }
        }

        getAction(httpurl, paramObj).then((res) => {
          if (res.success) {
            let devInfo = res.result.deviceInfo
            let posInfo = res.result.cabinet2device
            if (devInfo && Object.keys(devInfo)) {
              this.syncDevice = true
              this.deviceInfo = devInfo
            } else {
              this.syncDevice = false
            }
            Promise.all([this.queryAllProduct(), this.loadRoomTree(), this.getDevicePositionInfo(posInfo),
              this.getCollectInfoListByDeviceId(devInfo, res.result.connectInfoArray)]).then((res1) => {
              let result = res1.every((item, index) => {
                return item.success == true
              })
              if (result) {
                //回显设备基本信息
                this.setDeviceInfo(devInfo)
                resolve({ success: true, message: '请求成功' })
              } else {
                reject({ success: false, message: '请求失败' })
              }
            }).catch((err1) => {
              reject({ success: false, message: err1.message })
            })
          } else {
            reject({ success: false, message: res.message })
          }
        }).catch((err) => {
          reject({ success: false, message: err.message })
        })
      })
    },
    /*编辑：处理回显设备相关信息*/
    setDeviceInfo(deviceInfo) {
      if (deviceInfo && Object.keys(deviceInfo)) {
        this.canEditDevCode = true
        this.productNodeType = 'product'
        this.productNodeCatId = deviceInfo.categoryId
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(deviceInfo,
            'productId', 'deviceCode', 'description', 'capacity', 'location', 'specifications'))
        })
      }
    },
    /*编辑：处理回显设备物理位置信息*/
    getDevicePositionInfo(positionInfo) {
      this.hisCabinetId = undefined
      this.hisLayerPoolList = []
      return new Promise((resolve, reject) => {
        if (positionInfo && Object.keys(positionInfo)) {
          let roomId = positionInfo.roomId
          if (roomId) {
            this.getCabinetList(roomId).then((res) => {
              let cabinetId = positionInfo.cabinetId
              if (cabinetId) {
                this.roomNodeType = 'room'
                this.hisCabinetId = cabinetId//记录开始的机柜id，用于机柜的校验逻辑判断
                let layerPool = positionInfo.layerPool ? positionInfo.layerPool : []
                this.hisLayerPoolList = JSON.parse(JSON.stringify(layerPool))

                //加载u，判断该机房下是否有其他空闲u，若有，将回显数据加入u下拉数据中，若没有，下拉数据直接为回显数据
                this.getLayerPoolList(cabinetId).then((res1) => {
                  if (this.hisLayerPoolList.length > 0) {
                    this.hisLayerPoolList.map((item) => {
                      this.layerPoolList.push(item)
                    })
                    this.layerPoolList = this.layerPoolList.sort((a, b) => {
                      return parseInt(a) - parseInt(b)
                    })
                  }
                  let posInfo = {
                    roomId: roomId,
                    cabinetId: cabinetId,
                    layerPool: positionInfo.layerPool ? positionInfo.layerPool : []
                  }
                  this.$nextTick(() => {
                    this.form.setFieldsValue(pick(posInfo, 'roomId', 'cabinetId', 'layerPool'))
                  })
                  resolve({ success: true, message: '请求成功' })
                })
              }
            }).catch((err) => {
              reject({ success: false, message: err.message })
            })
          }
        } else {
          resolve({ success: true, message: '请求成功' })
        }
      })
    },
    /*编辑：通过设备id.获取连接参数*/
    getCollectInfoListByDeviceId(deviceInfo, connectInfoArray) {
      return new Promise((resolve, reject) => {
        if (this.assetsOperateType == 'editTempAssets') {
          // 资产变更：编辑临时资产
          if (connectInfoArray && connectInfoArray.length > 0) {
            this.collectInfoList = connectInfoArray
          } else {
            this.collectInfoList = []
          }
          resolve({ success: true, message: '请求成功' })
        } else {
          if (deviceInfo && Object.keys(deviceInfo).length > 0) {
            getAction(this.url.queryCollectInfoList, { deviceId: deviceInfo.id }).then((res) => {
              if (!!res) {
                this.collectInfoList = res
                this.productNodeTitle = deviceInfo.productName,
                  resolve({ success: true, message: '请求成功' })
              } else {
                reject({
                  success: false,
                  message: '请求失败'
                })
              }
            }).catch((err) => {
              reject({
                success: false,
                message: '请求失败'
              })
            })
          } else {
            resolve({ success: true, message: '请求成功' })
          }
        }
      })
    },

    /*获取产品名称的下拉框数据源*/
    queryAllProduct() {
      return new Promise((resolve, reject) => {
        getAction(this.url.queryAllProduct)
          .then((res) => {
            if (res.success) {
              this.productList = res.result
              this.setProductNodeIcon(this.productList, '')
              resolve({ success: true, message: res.message })
            } else {
              reject({ success: false, message: res.message })
            }
          }).catch((err) => {
          reject({ success: false, message: err.message })
        })
      })
    },
    /*设置产品分类、产品节点的图标*/
    setProductNodeIcon(data, pid = '') {
      if (data.length && data.length > 0) {
        for (let i = 0; i < data.length; i++) {
          data[i] = {
            ...data[i],
            categoryId: data[i].type != 'product' ? data[i].value : pid,
            isLeaf: data[i].children.length == 0,
            icon:
              data[i].type != 'product' ? (
                <a-icon type='folder' style='color:#409eff' />
              ) : (
                <a-icon type='file' style='color:#409eff' />
              )
          }
          if (data[i].children.length > 0) {
            this.setProductNodeIcon(data[i].children, data[i].value)
          }
        }
      }
    },

    /*获取机房树*/
    loadRoomTree() {
      return new Promise((resolve, reject) => {
        var that = this
        that.roomList = []
        queryPostionTreeList().then((res) => {
          if (res.success) {
            if (res.result && res.result.length > 0) {
              that.roomList = res.result
              that.setRoomNodeIcon(that.roomList)
              resolve({ success: true, message: res.message })
            } else {
              resolve({ success: true, message: res.message })
            }
          } else {
            reject({ success: false, message: res.message })
          }
        }).catch((err) => {
          reject({ success: false, message: err.message })
        })
      })
    },
    /*设置机房、非机房节点的图标*/
    setRoomNodeIcon(data) {
      if (data.length && data.length > 0) {
        for (let i = 0; i < data.length; i++) {
          data[i].icon = data[i].type === 'room' ?
            (<a-icon type='home' style='color:#409eff' />) :
            (<a-icon type='environment' style='color:#409eff' />)
          data[i].isLeaf = !data[i].children || data[i].children.length === 0 ? true : false
          if (data[i].children && data[i].children.length > 0) {
            this.setRoomNodeIcon(data[i].children)
          }
        }
      }
    },

    /*获取供应商下拉数据*/
    getAllSupplier() {
      return new Promise((resolve, reject) => {
        getAction(this.url.supplier).then((res) => {
          if (res.success) {
            this.supplierList = res.result
            resolve({ success: true, message: res.message })
          } else {
            reject({ success: false, message: res.message })
          }
        }).catch((err) => {
          reject({ success: false, message: err.message })
        })
      })
    },
    /*获取合同列表*/
    getContract() {
      return new Promise((resolve, reject) => {
        getAction(this.url.contract, { pageSize: -1 }).then((res) => {
          if (res.success) {
            this.contractList = res.result.records
            resolve({ success: true, message: res.message })
          } else {
            reject({ success: false, message: res.message })
          }
        }).catch((err) => {
          reject({ success: false, message: err.message })
        })
      })
    },

    /*新增时，若配置了自动生成资产编号，需要调用此方法*/
    getGenerateAssetsCode() {
      return new Promise((resolve, reject)=>{
        getAction(this.url.generateCode,{ bizName: 'asset_code_rule' }).then((res) => {
          if(res.success){
            this.autoAssetsCode = res.result
            this.$message.success(res.message)
            resolve({success: true, message:res.message})
          }else {
            this.$message.warning(res.message)
            reject({success:false,message:res.message})
          }
        }).catch((err)=>{
          this.$message.warning(err.message)
          reject({success:false,message:err.message})
        })
      })
    },


    /***************资产信息***************/

    /*是否预警*/
    changeWarning(value) {
      this.assetsInfo['isWarning'] = value.target.value
    },

    //选择不同资产类型请求对应的资产状态下拉数据及附加字段数据
    changeAssetsCategory(e) {
      this.additionalFieldList = []
      this.assetsStatusList = []
      let supplierId = this.form.getFieldValue('producerId')
      this.getAssetsModelList(e, supplierId)
      if (!!e) {
        this.getAdditionalFieldByCarId(e.trim())
        this.getAssetsStatusByAssetsCategoryId(e.trim())
      }
    },
    //根据所选产品分类（资产类型）获取附加字段
    getAdditionalFieldByCarId(categoryId) {
      getAction(this.url.findCodeName, { assetsCategoryId: categoryId }).then((res) => {
        if (res.success) {
          this.additionalFieldList = res.result
        } else {
          this.$message.warning(res.message)
        }
      }).catch((err) => {
        this.$message.warning(err.message)
      })
    },
    //根据资产类型id获取资产状态下拉数据
    getAssetsStatusByAssetsCategoryId(categoryId) {
      return new Promise((resolve, reject) => {
        getAction(this.url.queryStatusByAssetsCategoryId, { assetsCategoryId: categoryId }).then((res) => {
          if (res.success) {
            this.assetsStatusList = res.result
            resolve({ success: true, message: '请求成功' })
          } else {
            reject({ success: true, message: res.message })
          }
        }).catch((err) => {
          reject({ success: true, message: err.message })
        })
      })
    },
    /*选择供应商，加载资产型号下拉数据*/
    changeProducer(e) {
      let assetsCategoryId = this.form.getFieldValue('assetsCategoryId')
      this.getAssetsModelList(assetsCategoryId, e)
    },
    /*资产型号交互*/
    // changeAssetsModel(value) {
    //   this.$nextTick(() => {
    //     if (value) {
    //       this.form.setFieldsValue(pick({ assetsModel: value }, 'assetsModel'))
    //     }
    //   })
    // },
    /*通过产品类型，供应商获取资产型号下拉数据*/
    getAssetsModelList(productId, supplierId) {
      this.assetsModelList = []
      if (productId && supplierId) {
        let param = {
          productCategoryId: productId,
          supplier: supplierId,
          pageSize: '-1',
          current: '1'
        }
        getAction(this.url.querAssetsModelList, param).then((res) => {
          if (res.success) {
            this.assetsModelList = res.result.records

          } else {
            this.$message.warning(res.message)
          }
        }).catch((err) => {
          this.$message.warning(err.message)
        })
      }
    },
    /*是否同步至设备*/
    syncDeviceFun(value) {
      if (value) {
        let pass = true
        if (this.productList.length === 0 || this.roomList.length === 0) {
          Promise.all([this.queryAllProduct(), this.loadRoomTree()]).then((res) => {
            pass = res.every((item, index) => {
              if (!item.success) {
                this.$message.warning(item.message)
              }
              return item.success == true
            })
          })
        }
        if (pass) {
          this.$nextTick(() => {
            this.form.setFieldsValue(pick({ assetsCategoryId: undefined }, 'assetsCategoryId'))
            this.additionalFieldList = []
            this.assetsStatusList = []
            this.layerPoolList = []
            this.cabinetList = []
            this.assetsModelList = []
          })
        }
        //判断是新增还是编辑，若是编辑设备标识不可编辑，取值不变
        this.canEditDevCode = this.assetsInfo.id && this.deviceInfo.deviceCode ? true : false
        if (this.canEditDevCode) {
          this.$nextTick(() => {
            this.form.setFieldsValue(pick({ deviceCode: this.deviceInfo.deviceCode }, 'deviceCode'))
          })
        }
      } else {
        this.collectInfoList = []
        this.roomNodeType = ''
        this.productNodeCatId = ''
        this.productNodeType = undefined
      }
    },

    /*右键拓扑根节点添加关系*/
    addAssociatedAssets(){
      this.handleTopoAdd(this.assetsInfo.id, this.relationList)
    },
    /*更新加入拓扑关系的数据*/
    transmit(data) {
      let newArr = []
      data.forEach((ele) => {
        newArr.push(ele.store.data.data.id)
      })
      this.relationList = newArr
    },
    /*打开待添加拓扑关系的资产信息列表界面*/
    handleTopoAdd(assetsId, data) {
      this.refreshTopo = false
      this.$refs.addAsetsoModal.show(this.assetsInfo.id, this.relationList)
      this.$refs.addAsetsoModal.title = '添加关系'
      this.$refs.addAsetsoModal.disableSubmit = false
    },
    /*打开更大的拓扑图界面*/
    enlargeTopo() {
      this.$refs.topoModal.show(this.assetsInfo.id)
      this.$refs.topoModal.title = '关系拓扑'
      this.$refs.topoModal.disableSubmit = true
    },
    /*更新拓扑图*/
    doRefresh() {
      this.refreshTopo = true
    },
    /***************设备信息交互***************/
    /*选择产品*/
    selectProductName(e, node) {
      if (!!e) {
        this.productNodeType = node.dataRef.type
        this.productNodeCatId = node.dataRef.categoryId
        this.productNodeTitle = node.dataRef.title
        this.$nextTick(() => {
          this.form.setFieldsValue(pick({ assetsCategoryId: this.productNodeCatId }, 'assetsCategoryId'))
          this.changeAssetsCategory(this.productNodeCatId)
        })
        if (node.dataRef.type != 'product') {
          this.productNodeType = 'category'
          this.collectInfoList = []
          return
        }
        this.getCollectInfoListByproductId(e.trim())
      }
    },
    /*清除产品名称选项时，将记录节点类型变量制空*/
    changeProductName(value) {
      if (!value) {
        this.productNodeType = undefined
        this.productNodeCatId = ''
        this.form.setFieldsValue(pick({ assetsCategoryId: undefined }, 'assetsCategoryId'))
        this.collectInfoList = []
        this.additionalFieldList = []
        this.assetsStatusList = []
        this.assetsModelList = []
      }
    },
    /*新增情况下，通过产品id.获取连接参数*/
    getCollectInfoListByproductId(productId) {
      let paramObj = { productId: productId }
      getAction(this.url.queryCollectInfoList, paramObj)
        .then((res) => {
          if (!!res) {
            res.forEach((ele) => {
              if (ele.value.defaultValue != null && ele.value.defaultValue.length > 0) {
                ele.value.connectValue = ele.value.defaultValue
              }
            })
            this.collectInfoList = res
          }
        }).catch((err) => {
        this.$message.warning(err.message)
      })
    },
    //检验产品名称是否选择正确
    validateProduct(rule, value, callback) {
      if (rule.required) {
        if (value) {
          if (this.productNodeType === 'category') {
            callback('勿选择产品分类名称，请选择产品名称')
          } else {
            callback()
          }
        } else {
          callback('请选择产品名称')
        }
      } else {
        callback()
      }
    },

    /*机房为空时，将机房、u数据制空*/
    changeRoom(value) {
      if (!value) {
        this.roomNodeType = undefined
        this.cabinetList = []
        this.layerPoolList = []
        this.form.setFieldsValue(pick({ cabinetId: undefined, layerPool: [] }, 'cabinetId', 'layerPool'))
      }
    },
    /*选择机房加载机柜数据*/
    selectRoom(e, node) {
      if (!!e) {
        this.roomNodeType = node.dataRef.type
        if (node.dataRef.type != 'room') {
          this.roomNodeType = 'notRoom'
          return
        }
        this.$nextTick(() => {
          this.cabinetList = []
          this.layerPoolList = []
          this.form.setFieldsValue(pick({ cabinetId: undefined, layerPool: [] }, 'cabinetId', 'layerPool'))
          this.getCabinetList(e)
        })
      }
    },
    /*根据机房id获取机柜下拉数据*/
    getCabinetList(roomId) {
      return new Promise((resolve, reject) => {
        let paramObj = {
          roomId: roomId.trim(),
          viewFlag: 3 //3D：3  拓扑 2
        }
        getAction(this.url.cabinet, paramObj)
          .then((res) => {
            if (res.success) {
              this.cabinetList = res.result
              resolve({
                success: true,
                message: res.message
              })
            } else {
              reject({
                success: false,
                message: res.message
              })
            }
          }).catch((err) => {
          reject({
            success: false,
            message: err.message
          })
        })
      })
    },

    //校验机房是否选择正确
    validateRoom(rule, value, callback) {
      if (value) {
        if (this.roomNodeType === 'notRoom') {
          return callback(new Error('请选择到机房'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    /*选择机柜加载u数据*/
    changeCabinet(value) {
      /* this.$nextTick(() => {*/
      this.layerPoolList = []
      this.form.setFieldsValue(pick({ layerPool: [] }, 'layerPool'))
      /* })*/
    },
    /*根据机柜id获取u下拉数据*/
    getLayerPoolList(cabinetId) {
      return new Promise((resolve, reject) => {
        getAction(this.url.layerPool, { cabinetId: cabinetId }).then((res) => {
          if (res.success) {
            this.layerPoolList = res.result ? res.result : []
            resolve({
              success: true,
              message: res.message
            })
          } else {
            reject({
              success: false,
              message: res.message
            })
          }
        }).catch((err) => {
          reject({
            success: false,
            message: err.message
          })
        })
      })
    },
    /*校验机柜*/
    validateCabinet(rule, value, callback) {
      if (rule.required) {
        if (value && value.length > 0) {
          this.getLayerPoolList(value).then((res) => {
            if (res.success) {
              if (this.hisCabinetId === value && this.hisLayerPoolList.length > 0) {
                this.hisLayerPoolList.map((item) => {
                  this.layerPoolList.push(item)
                })
                this.layerPoolList = this.layerPoolList.sort((a, b) => {
                  return parseInt(a) - parseInt(b)
                })
                callback()
              } else if (this.hisCabinetId !== value && this.layerPoolList.length === 0) {
                callback('该机柜没有空闲的u可选，请选择其他机柜')
              } else {
                callback()
              }
            } else {
              this.$message.warning(res.message)
              callback('请选择机柜')
            }
          }).catch((err) => {
            this.$message.warning(err.message)
            callback('请选择机柜')
          })
        } else {
          callback('请选择机柜')
        }
      } else {
        callback()
      }
    },
    /*校验U位*/
    validateLayerPool(rule, value, callback) {
      if (rule.required) {
        if (value && value.length > 0) {
          let arr = JSON.parse(JSON.stringify(value))
          if (arr.length < 2) {
            callback()
          } else {
            let newArr = arr.sort((a, b) => {
              return a - b
            })
            let isTips = false
            for (let i = 0; i < newArr.length - 1; i++) {
              if (newArr[i + 1] - newArr[i] > 1) {
                isTips = true
                break
              }
            }
            if (isTips) {
              callback('请选择编号连续的U位')
            } else {
              callback()
            }
          }
        } else {
          callback('请选择U位')
        }
      } else {
        callback()
      }
    },
    /*改变连接参数方法*/
    changeConParam(e, pidx, idx) {
      this.collectInfoList[pidx].value[idx].connectValue = e.target.value
    },
    /*改变连接参数方法1*/
    changeConParam1(e, pidx, idx) {
      this.collectInfoList[pidx].value[idx].connectValue = e
    },

    /***************提交***************/
    submitForm() {
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          this.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.assetsInfo.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'post'
          }
          //提交数据：设备、连接参数、资产（基本信息、附件、附加字段）
          let formData = {
            deviceInfo: this.getDeviceInfo(values),
            templateList: this.getTemplateList(),
            assetsInfo: this.getAssetsInfoObject(values)
          }
          httpAction(httpurl, formData, method)
            .then((res) => {
              this.confirmLoading = false
              if (res.success) {
                this.$message.success(res.message)
                this.$emit('ok')
              } else {
                this.$message.warning(res.message)
              }
            }).catch((err) => {
            this.$message.warning(err.message)
            this.confirmLoading = false
          })
        }
      })
    },
    /***************资产变更数据提交***************/
    submitAssetsChangeForm(record) {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (that.assetsOperateType == 'editHasAssets') {
            // 编辑已有资产
            httpurl += this.url.editHasAssets
            method = 'post'
          } else {
            if (!this.assetsInfo.id) {
              // 编辑临时表资产
              httpurl += this.url.assetsChangeAdd
              method = 'post'
            } else {
              // 新增临时表资产
              httpurl += this.url.assetsChangeEdit
              method = 'post'
            }
          }

          //提交数据：设备、连接参数、资产（基本信息、附件、附加字段）
          let formData = {
            deviceInfo: this.getDeviceInfo(values),
            templateList: this.getTemplateList(),
            assetsInfo: this.getAssetsInfoObject(values)
          }
          httpAction(httpurl, formData, method)
            .then((res) => {
              this.confirmLoading = false
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('addAssetsChangeOk')
              } else {
                that.$message.warning(res.message)
              }
            }).catch((err) => {
            that.$message.warning(err.message)
            this.confirmLoading = false
          })
        }
      })
    },

    /*整理设备信息数据*/
    getDeviceInfo(values) {
      let info = undefined
      if (this.syncDevice) {
        info = JSON.parse(JSON.stringify(this.deviceInfo))
        info['productId'] = values.productId
        info['name'] = values.assetsName
        info['deviceCode'] = values.deviceCode
        info['description'] = values.description
        info['productName'] = this.productNodeTitle
        info['specifications'] = values.specifications
        info['capacity'] = values.capacity
        info['location'] = values.location

        /*  if(this.deviceInfo.deviceId){
            Object.assign(info, {id:this.deviceInfo.deviceId})
          }*/
        if (this.roomNodeType === 'room') {
          Object.assign(info, {
            positionInfo: {
              cabinetId: values.cabinetId,
              layerPool: values.layerPool
            }
          })
        }
      }
      return info
    },
    /*整理链接参数信息*/
    getTemplateList() {
      const that = this
      let paramTemplate = undefined
      if (this.syncDevice) {
        paramTemplate = []
        if (that.collectInfoList.length > 0) {
          that.collectInfoList.forEach((ele) => {
            ele.value.forEach((item) => {
              paramTemplate.push({
                deviceId: item.deviceId,
                connectCode: item.connectCode,
                connectValue: item.connectValue,
                persistentconf: item.persistentconf,
                templateId: item.templateId,
                transferProtocol: item.transferProtocol,
                transferProtocolId: item.transferProtocolId
              })
            })
          })
        }
      }
      return paramTemplate
    },
    /*整理资产信息*/
    getAssetsInfoObject(values) {
      let info = {}
      info = JSON.parse(JSON.stringify(this.assetsInfo))
      if (this.assetsOperateType) {
        // 资产变更用
        Object.assign(info, {
          tempProcessInstanceId: this.tempId,
          assetsId: this.assetsId,
          assetsUnique: this.assetsInfo.assetsUnique,
          statusId: values.statusId ? values.statusId : '',
          statusName: '',
          assetsCode: values.assetsCode,
          assetsName: values.assetsName,
          assetsCategoryId: values.assetsCategoryId,
          producerId: values.producerId ? values.producerId : '',
          assetsModel: values.assetsModel,
          storageTime: values.storageTime,
          isWarning: values.isWarning,
          contractId: values.contractId ? values.contractId : '',
          extendValue: this.getAdditionalFieldObject(values),
          assetsFile: this.getfiles()
        })
      } else {
        Object.assign(info, {
          statusId: values.statusId ? values.statusId : '',
          statusName: '',
          assetsCode: values.assetsCode,
          assetsName: values.assetsName,
          assetsCategoryId: values.assetsCategoryId,
          producerId: values.producerId ? values.producerId : '',
          assetsModel: values.assetsModel,
          storageTime: values.storageTime,
          isWarning: values.isWarning,
          contractId: values.contractId ? values.contractId : '',
          extendValue: this.getAdditionalFieldObject(values),
          assetsFile: this.getfiles()
        })
      }

      Object.assign(info, {
        startQualityTime: values.isWarning === '1' ? values.startQualityTime : '',
        qualityTerm: values.isWarning === '1' ? values.qualityTerm : '',
        repairFac: values.isWarning === '1' ? values.repairFac : '',
        repairPhone: values.isWarning === '1' ? values.repairPhone : '',
        warrantyConnect: values.isWarning === '1' ? values.warrantyConnect : '',
        warrantyPhone: values.isWarning === '1' ? values.warrantyPhone : ''
      })
      return info
    },
    /*整理链接参数信息*/
    getAdditionalFieldObject(values) {
      //参数模板数据拼装
      let extendValue = []
      this.additionalFieldList.forEach((ele) => {
        extendValue.push({
          name: ele.name,
          value: values.addCodes[ele.id] || null,
          type: ele.type,
          id: ele.id,
          dictType: ele.dictType
        })
      })
      return extendValue.length > 0 ? extendValue : undefined
    },
    /*整理附件*/
    getfiles() {
      let faleUrl = ''
      if (this.filesList instanceof Array) {
        faleUrl = this.filesList.join(',')
        /* for (var i = 0; i < this.filesList.length; i++) {
           faleUrl = faleUrl + ',' + this.filesList[i]
         }*/
      } else {
        faleUrl = this.filesList
      }
      return faleUrl
    },

    // 重写a-upload的文件上传处理方式
    downloadFilesCustomRequest(data) {
      this.saveFile(data)
    },
    // 上传并保存文件
    saveFile(data) {
      const formData = new FormData()
      formData.append('file', data.file)
      dibootApi.upload('/demo/upload', formData).then((res) => {
        // 发送http请求
        if (res.code === 0) {
          let file = this.fileFormatter(res.data)
          // 上传单个文件后，将该文件会先到a-upload组件的已上传文件列表中的操作
          this.downloadFiles.push(file)
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 对上传成功返回的数据进行格式化处理，格式化a-upload能显示在已上传列表中的格式（这个格式官方文档有给出的）
    fileFormatter(data) {
      let file = {
        uid: data.uuid, // 文件唯一标识，建议设置为负数，防止和内部产生的 id 冲突
        name: data.name, // 文件名
        status: 'done', // 状态有：uploading done error removed
        response: '{"status": "success"}' // 服务端响应内容
      }
      return file
    },
    // 没错，删除某个已上传的文件的时候，就是调用的这里
    handleDownloadFileRemove(file) {
      const index = this.downloadFiles.indexOf(file)
      const newFileList = this.downloadFiles.slice()
      newFileList.splice(index, 1)
      this.downloadFiles = newFileList
    },

    // 重新获取最新的资产数据
/*    getAssetsById(asstesId) {
      getAction(this.url.queryAssetsById, {
        id: asstesId
      }).then((res) => {
        if (res.success) {
          this.statusId = res.result.statusId
          this.statusName = res.result.statusName
        }
      })
    }*/
  }
}
</script>
<style lang='less' scoped>
.colorBox {
  margin-bottom: 18px;
}

.colorTotal {
  padding-left: 7px;
  border-left: 4px solid #1e3674;
}

::v-deep .ant-form {
  .ant-form-item {
    .ant-form-item-label {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}


.relation-btn-div {
  display: flex;
  justify-content: flex-start;
  padding-bottom: 8px;

  .add-btn {
    height: 28px;
    margin-right: 16px;
    background: #ecf5ff;
    border: 1px solid #b3d8ff;
    border-radius: 4px;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #539cf7;
  }

  .enlarge-btn {
    height: 28px;
    background: #ecf5ff;
    border: 1px solid #b3d8ff;
    border-radius: 4px;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #539cf7;
  }

  .enlargeButton {
    position: absolute;
    right: 14px;
    top: 106px;
    z-index: 100;
  }
}
/*.edit-btn {
  background: #ecf5ff;
  border: 1px solid #b3d8ff;
  border-radius: 4px;
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: #409eff;
  width: 120px;
  height: 28px;
  cursor: pointer;
  margin: 0px 0px 24px 0px;
}*/

//.tabs-container {
//  //height: calc(100% - 135px);
//}

/*.ant-tabs-bar {
  margin-bottom: 24px !important;
}*/

/*::v-deep .ant-tabs {
  height: 100%;
  background-color: white;
  padding: 0 20px;
  border-radius: 3px;*/

/*  .ant-tabs-content {
    height: calc(100% - 60px);

    .ant-tabs-tabpane-active {
      height: 240px;
      overflow-y: auto;
      overflow-x: hidden;
    }
  }
}*/

/*/deep/ .ant-row {
  div:nth-child(5) {
    .ant-form-item {
      .ant-form-item-label {
        label {
          // letter-spacing: 4px;
        }

        label:after {
          letter-spacing: 0;
        }
      }
    }
  }
}*/

::v-deep .two-words > div > label {
  letter-spacing: 4px;
}

::v-deep .two-words > div > label::after {
  letter-spacing: 0px;
}


</style>