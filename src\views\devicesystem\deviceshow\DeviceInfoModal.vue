<template>
  <div style='height: 100% !important;width:100%;display: flex;flex-direction: column'>
    <div class='header-info'>
      <div style='width:calc(100% - 21px);margin-right: 16px'>
        <div class='p-info-title'>
          <span class="productName">设备：{{ deviceInfo.name }}</span>
          <div v-if="functionList.length>0" class="button-list">
            <a-button v-for="(item,index) in functionList" :key="index" @click="handleImplementation(item)" size="small" class="btn">
              {{item.name}}
            </a-button>
          </div>
        </div>
        <div class="p-info-desc">
          <span class='span-status'>
            <span v-if='deviceInfo.enable==1'>
              <img v-if='deviceInfo.status === 0' src='~@assets/img/gray-circle.png' />
              <img v-else-if='deviceInfo.status === 1' src='~@assets/img/green-circle.png' />
              <span class='status' key='statusName'>{{ deviceInfo.statusName }}</span>
              <span v-if='deviceInfo.alarmStatus === 1' class='alarm-status'>
                  <img src='~@assets/img/red-circle.png' />
                  <span class='status'>{{ deviceInfo.alarmStatusName }}</span>
              </span>
            </span>
            <span v-else>
              <img src='~@assets/img/gray-circle.png' />
              <span class='status' key='enableName'>{{ deviceInfo.enableName }}</span>
            </span>
          </span>
          <p class='p-info-product'>
            <span class='span-assets'>产品名称：{{ deviceInfo.productName }}</span>
            <span class='span-assets' v-if='deviceInfo.tagInfoList&&deviceInfo.tagInfoList.length>0'>设备标签：
              <span class='span-tag' v-for='item in deviceInfo.tagInfoList' :key='item.tagKey' :title='item.tagName'
                    :style="{'background-color': item.tagColor}">{{ item.tagName }}
              </span>
            </span>
          </p>
        </div>
      </div>
      <div style='width: 21px'>
        <span class='header-back' v-if="!hideBack">
          <img src='~@assets/return1.png' alt='' @click='getGo'
               style='width: 20px; height: 20px; cursor: pointer' />
        </span>
      </div>
    </div>
    <div style='flex:1;height: 50%'>
      <tabs
        ref='deviceTabs'
        :deviceInfo.sync='deviceInfo'
        @updateDeviceInfo="updateDeviceInfo"
        :is-editing='isEditing'
        :render-states='renderStates'>
      </tabs>
    </div>
    <device-function-modal :deviceInfo='deviceInfo' ref="modalForm" @ok="getFunctionList"></device-function-modal>
  </div>
</template>

<script>
import Tabs from './Tabs'
import { ajaxGetDictItems, getDictItemsFromCache } from '@api/api'
import { getAction } from '@/api/manage'
import DeviceFunctionModal from './DeviceFunctionModal.vue'

export default {
  name: 'DeviceInfoModal',
  components: { Tabs, DeviceFunctionModal },
  data() {
    return {
      deviceInfo: {},
      functionList: [],
      disableMixinCreated: true,
      url:{
        getFunctionList: '/product/deviceControlCommand/commands', //设备功能table数据请求接口
        queryById: '/device/deviceInfo/queryById',
      }
    }
  },
  props: {
    data: {
      type: Object
    },
    isEditing: {
      type: Boolean,
      default: true,
      required: false
    },
    renderStates: {
      type: Object,
      required: false,
      default:()=>{return {}}
    },
    hideBack:{
      type:Boolean,
      default:false,
    },
  },
  created() {
    this.deviceInfo = this.data
    this.initDictData('status', 'device_status')
    this.initDictData('alarmStatus', 'device_alarm_status')
    this.initDictData('enable', 'device_enable_status')
    this.getFunctionList()
  },
  mounted() {},
  methods: {
    updateDeviceInfo(){
      getAction(this.url.queryById,{id:this.deviceInfo.id}).then(res => {
        if (res.success&&res.result&&res.result.records&&res.result.records.length>0){
          this.deviceInfo = res.result.records[0]
          this.initDictData('status', 'device_status')
          this.initDictData('alarmStatus', 'device_alarm_status')
          this.initDictData('enable', 'device_enable_status')
          this.getFunctionList()
        }
        //console.log('重新请求设备信息res===',res)
      })
    },
    initDictData(filed, dictCode) {
      if (dictCode != null && dictCode != '') {
        //优先从缓存中读取字典配置
        if (getDictItemsFromCache(dictCode)) {
          let dictOption = getDictItemsFromCache(dictCode)
          this.getStatusName(filed, dictOption)
          return
        }

        //根据字典Code, 初始化字典数组
        ajaxGetDictItems(dictCode, null).then((res) => {
          if (res.success) {
            let dictOption = res.result
            this.getStatusName(filed, dictOption)
          }
        })
      }
    },

    getStatusName(filed, dictOption) {
      let filedName = filed + 'Name'
      for (let i = 0; i < dictOption.length; i++) {
        if (dictOption[i].value == this.deviceInfo[filed]) {
          this.deviceInfo[filedName] = dictOption[i].text
          return
        }
      }
    },
    //返回上一级
    getGo() {
      let tabKey = this.$refs.deviceTabs.defaultActiveKey
      if (tabKey == '4') {
        this.$refs.deviceTabs.$refs.journalManagement.pButton1(0)
      } else if (tabKey == '7') {
        this.$refs.deviceTabs.$refs.alarmManagement.pButton1(0)
      }else if (tabKey == '9') {
        this.$refs.deviceTabs.$refs.deviceAlarmManagement.pButton1(0)
      }
      this.$refs.deviceTabs.defaultActiveKey = '1'
      this.$parent.pButton1(0)
    },
    // 获取设备功能列表，默认展示一页
    getFunctionList() {
      getAction(this.url.getFunctionList, {
        productId: this.deviceInfo.productId,
        deviceId: this.deviceInfo.id,
        pageNo: 1,
        pageSize: -1
      }).then((res) => {
        if (res.success) {
          this.functionList = res.result.records || res.result
        } else {
          this.$message.warning(res.message)
        }
        if (res.code === 510) {
          this.$message.warning(res.message)
        }
      }).catch(err=>{
        this.$message.warning(err.message)
      })
    },
    // 执行命令
    handleImplementation(record) {
      this.$refs.modalForm.edit(record)
      this.$refs.modalForm.title = '执行'
      this.$refs.modalForm.disableSubmit = true
    }
  }
}
</script>

<style scoped lang="less">
.header-info {
  border-radius: 3px;
  margin-bottom: 16px;
  padding: 24px;
  background: #fff;
  display: flex;
  justify-content: space-between;
  align-items: start;
}

.p-info-title {
  display: flex;
  align-items: flex-start;
  flex-wrap: wrap;
  font-family: PingFangSC-Medium;
  font-size: 18px;
  color: #000000;
  .productName {
    margin-right: 40px;
    margin-bottom: 12px;
  }
  .button-list {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin-bottom: 10px;
    .btn {
      margin-right: 12px;
      //margin-bottom: 6px;
      height: auto;
      padding: 2px 12px 2px 12px;
    }
    .more {
      margin-right: 10px;
      margin-bottom: 4px;
      font-size: 14px;
      color: #409EFF;
      cursor: pointer;
      white-space: nowrap;
    }
  }
}
.p-info-desc {
  display: flex;
  align-items: center;
  flex-wrap: wrap;

  .span-status {
    margin-right: 40px;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
  }

  .span-status img {
    width: 6px;
    height: 6px;
  }
  .alarm-status {
    margin-left: 10px;
  }

  .status {
    padding: 0px 6px 0px 2px;
  }

  .span-assets {
    margin-right: 40px;
    display: flex;
    align-items: center;
  }

  .span-tag {
    display: inline-block;
    color: white;
    border-radius: 30px;
    padding: 0px 10px;
    margin:2px 4px;
    line-height: 30px;
    height: 30px;
    max-width:260px ;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .p-info-product {
  /* line-height: 45px;
    height: 45px;*/
    margin-bottom: 0px;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
    display: flex;
    justify-content:start;
    align-items: center;
    flex-flow: row wrap;
  }
}

.header-back {
  text-align: right;
}
</style>