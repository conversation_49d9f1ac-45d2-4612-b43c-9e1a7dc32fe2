<template>
  <div class="knowledgeInfo">
    <card-frame :title="'知识详情'" :showHeadBgImg="true">
    <div slot="bodySlot" style='padding: 24px 24px 0 24px;'>
      <knowledge-base-info-header
        :show-opmg-k-info='false'
        :col-status='colStatus'
        :k-info='kInfo'
        @handleCollection='handleCollection'
        @handleShare='handleShare'
        @getGo='getGo'>
      </knowledge-base-info-header>
      <div class="line"></div>
      <knowledge-base-info-body
        :kInfo='kInfo'
        :show-opmg-k-info='false'
        :approval-view='false'
        :show-share='false'
        :canDownload='canDownload'
        :unlike-status='unlikeStatus'
        :like-status='likeStatus'
        :kkfileview-url='kkfileviewUrl'>
      </knowledge-base-info-body>
    </div>
  </card-frame>
  <add-share-modal ref='addShareModal'></add-share-modal>
  </div>
</template>

<script>
import { knowledgeInfoMixins } from '@views/opmg/knowledgeManagement/knowledgeBase/modules/knowledgeInfoMixins'
import addShareModal from '@views/oneClickHelp/knowledgeBase/modules/AddShareModal.vue'
import cardFrame from '@views/oneClickHelp/localDeviceInfo/modules/CardFrame.vue'
import knowledgeBaseInfoHeader from '@views/opmg/knowledgeManagement/knowledgeBase/modules/KnowledgeBaseInfoHeader.vue'
import knowledgeBaseInfoBody from '@views/opmg/knowledgeManagement/knowledgeBase/modules/KnowledgeBaseInfoBody.vue'
export default {
  name: 'KnowledgeBaseInfo',
  mixins: [knowledgeInfoMixins],
  props: {
    data: {
      type: Object,
      required: false,
      default: {},
    }
  },
  components: {
    cardFrame,
    addShareModal,
    knowledgeBaseInfoHeader,
    knowledgeBaseInfoBody
  },
  data() {
    return {}
  },
  activated() {
    if ( this.kkfileviewUrl===''){
      this.getKkfileviewURL().then((res)=>{
        this.kkfileviewUrl=res
        this.init()
      })
    }else {
      this.init()
    }
  },
  methods: {}
}
</script>

<style scoped lang='less'>
@import '~@assets/less/onclickStyle.less';
.knowledgeInfo {
  height: 100% !important;
  overflow: hidden;
  width: 100%;
  background-image: url(~@assets/img/yunweiBackground.png);
  background-size: 100%;
  background-repeat: no-repeat;
  background-color: #091425 !important;

  .topImg {
    height: 50px;
    width: 100%;
    background-image: url('../../../../public/oneClickHelp/localDeviceInfo/leftHeadBg.png');
    background-repeat: no-repeat;
    background-position: right center;
    position: relative;

    .head-left::before {
      position: absolute;
      content: '';
      top: 0;
      left: 0;
      width: 3px;
      height: 3px;
      background-color: #2f5bff;
    }

    .head-left::after {
      position: absolute;
      content: '';
      bottom: 0;
      left: 0;
      width: 3px;
      height: 3px;
      background-color: #2f5bff;
    }

    .head-right::before {
      position: absolute;
      content: '';
      top: 0;
      right: 0;
      width: 3px;
      height: 3px;
      background-color: #2f5bff;
    }

    .head-right::after {
      position: absolute;
      content: '';
      bottom: 0;
      right: 0;
      width: 3px;
      height: 3px;
      background-color: #2f5bff;
    }
  }

  .line {
    width: 100%;
    height: 0.5px;
    margin: 0px 0px 24px 0px;
    background: linear-gradient(180deg, transparent, rgba(64, 158, 255, 1));
    box-shadow: 0 -1px 6px 1px #409eff9c;
    clear: both;
  }
}

::-webkit-scrollbar {
  display: none;
}
</style>