<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    switchFullscreen
    @ok="handleOk"
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @cancel="handleCancel"
    cancelText="关闭">
    <devops-order-info-detail-form ref="DevopsOrderInfoDetailForm" @ok="submitCallback" :disabled="disableSubmit"></devops-order-info-detail-form>
  </j-modal>
</template>

<script>

  import DevopsOrderInfoDetailForm from './DevopsOrderInfoFormDetail'
  export default {
    name: 'DevopsOrderInfoDetailModal',
    components: {
      DevopsOrderInfoDetailForm
    },
    data () {
      return {
        title:'',
        width:896,
        visible: false,
        disableSubmit: false
      }
    },
    methods: {
      add () {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.DevopsOrderInfoDetailForm.add();
        })
      },
      edit (record) {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.DevopsOrderInfoDetailForm.edit(record);
        })
      },
      close () {
        this.$emit('close');
        this.visible = false;
      },
      handleOk () {
        this.$refs.DevopsOrderInfoDetailForm.submitForm();
      },
      submitCallback(){
        this.$emit('ok');
        this.visible = false;
      },
      handleCancel () {
        this.close()
      }
    }
  }
</script>