<template>
  <a-modal :title="title" :width="800" :visible="visible" :confirmLoading='confirmLoading' :maskClosable='false'
    @ok='handleOk' @cancel="handleCancel" cancelText="关闭">
    <template #footer>
      <a-button key="back" @click="handleCancel">关闭</a-button>
      <a-button key="submit" type="primary" v-show="!disableSubmit" :loading="confirmLoading" @click="handleOk">确定
      </a-button>
    </template>
    <a-spin :spinning="confirmLoading">
      <a-form-model ref="form" :label-col="labelCol" :wrapper-col="wrapperCol" :model="model" :rules="validatorRules">
        <a-row :gutter='24'>
          <a-col v-bind='formItemLayout'>
            <a-form-model-item label="流程ID" required prop="id" hasFeedback>
              <a-input v-model="model.id" :disabled="disableSubmit" />
            </a-form-model-item>
          </a-col>
          <a-col v-bind='formItemLayout'>
            <a-form-model-item label="流程名称" prop="name" hasFeedback>
              <a-input v-model="model.name" :disabled="disableSubmit" />
            </a-form-model-item>
          </a-col>
          <a-col v-bind='formItemLayout'>
            <a-form-model-item label="流程编码" prop="key" hasFeedback>
              <a-input v-model="model.key" :disabled="disableSubmit" />
            </a-form-model-item>
          </a-col>
          <a-col v-bind='formItemLayout'>
            <a-form-model-item label="流程分类" prop="category" hasFeedback>
              <a-input v-model="model.category" :disabled="disableSubmit" />
            </a-form-model-item>
          </a-col>
          <a-col v-bind='formItemLayout'>
            <a-form-model-item label="流程描述" prop="description" hasFeedback>
              <a-input v-model="model.description" :disabled="disableSubmit" />
            </a-form-model-item>
          </a-col>
          <a-col v-bind='formItemLayout'>
            <a-form-model-item label="版本" prop="version" hasFeedback>
              <a-input v-model="model.version" :disabled="disableSubmit" />
            </a-form-model-item>
          </a-col>
          <a-col v-bind='formItemLayout'>
            <a-form-model-item label="状态" prop="suspended" hasFeedback>
              <a-input v-model="model.suspended ? '挂起' : '激活' " :disabled="disableSubmit" />
            </a-form-model-item>
          </a-col>
          <a-col v-bind='formItemLayout'>
            <a-form-model-item label="表单编码" prop="formKey" hasFeedback>
              <a-input v-model="model.formKey" :disabled="disableSubmit" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </a-spin>
  </a-modal>
</template>

<script>
  import {
    httpAction
  } from '@/api/manage'

  export default {
    name: 'processDefinitionModule',
    data() {
      return {
        disableSubmit: false,
        title: '操作',
        visible: false,
        model: {},
        formItemLayout: {
          md: {
            span: 24
          },
          sm: {
            span: 24
          }
        },
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          },
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          },
        },
        confirmLoading: false,
        form: this.$form.createForm(this),
        validatorRules: {
          name: [{
              required: true,
              message: '请输入姓名!'
            },
            {
              min: 2,
              max: 30,
              message: '长度在 2 到 30 个字符',
              trigger: 'blur'
            }
          ],
          email: [{
            required: false,
            type: 'email',
            message: '邮箱格式不正确',
            trigger: 'blur'
          }]
        },
        url: {
          // add: "/test/jeecgDemo/add",
          // edit: "/test/jeecgDemo/edit",
        },
      }
    },
    created() {},
    methods: {
      add() {
        this.edit({});
      },
      edit(record) {
        this.model = Object.assign({}, record);
        this.visible = true;
      },
      close() {
        this.$refs.form.resetFields();
        this.$emit('close');
        this.visible = false;
      },
      handleOk() {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if (!this.model.id) {
              httpurl += this.url.add;
              method = 'post';
            } else {
              httpurl += this.url.edit;
              method = 'put';
            }
            httpAction(httpurl, this.model, method).then((res) => {
              if (res.success) {
                that.$message.success(res.message);
                that.$emit('ok');
              } else {
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
              that.close();
            })
          }
        })
      },
      handleCancel() {
        this.close()
      }
    }
  }
</script>
<style scoped lang='less'>
  @import '~@assets/less/YQNormalModal.less';
</style>