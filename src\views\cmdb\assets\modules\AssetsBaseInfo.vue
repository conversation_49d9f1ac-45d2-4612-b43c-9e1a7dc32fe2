<template>
  <a-spin class='scroll' :spinning='confirmLoading'>
    <div>
      <!--资产信息-->
      <div style="margin-top: 24px; margin-right:1px">
        <div class="colorBox1">
          <div class='colorBox'>
            <span class="colorTotal">资产信息</span>
            <span v-if="editabled" style="margin-left: 20px; color: #409eff; cursor: pointer" @click="doEdit(assetsInfo)">
            <a-icon type="edit" style="margin-right: 6px" />编辑
          </span>
          </div>
          <a-descriptions  :column="{ xxl: 2, xl: 2, lg: 2, md: 2, sm: 2, xs: 2 }" bordered>
            <a-descriptions-item  label="唯一标识符">{{assetsInfo.assetsUnique }}</a-descriptions-item>
            <a-descriptions-item  label="资产编号">{{assetsInfo.assetsCode }}</a-descriptions-item>
            <a-descriptions-item  label='资产类型'>{{assetsInfo.assetsCategoryText }}</a-descriptions-item>
            <a-descriptions-item  label='供应商'>{{assetsInfo.producerName }}</a-descriptions-item>
            <a-descriptions-item  label='型号'>{{assetsInfo.assetsModel }}</a-descriptions-item>
            <a-descriptions-item  label='入库日期'>{{assetsInfo.storageTime }}</a-descriptions-item>
            <a-descriptions-item  label='是否预警'>{{assetsInfo.isWarning==='1'?"是":"否" }}</a-descriptions-item>

            <template v-if='assetsInfo.isWarning==="1"'>
              <a-descriptions-item  label="质保开始日期">{{assetsInfo.startQualityTime }}</a-descriptions-item>
              <a-descriptions-item  label='质保期限(月)'>{{assetsInfo.qualityTerm }}</a-descriptions-item>
              <a-descriptions-item  label='保修单位'>{{assetsInfo.repairFac }}</a-descriptions-item>
              <a-descriptions-item  label='保修单位电话'>{{assetsInfo.repairPhone }}</a-descriptions-item>
              <a-descriptions-item  label='保修联系人'>{{assetsInfo.warrantyConnect }}</a-descriptions-item>
              <a-descriptions-item  label='保修人电话'>{{assetsInfo.warrantyPhone}}</a-descriptions-item>
            </template>

            <a-descriptions-item  label='关联合同'>{{assetsInfo.contractName }}</a-descriptions-item>
            <a-descriptions-item  label="所属部门">{{assetsInfo.departmentText }}</a-descriptions-item>
            <a-descriptions-item  label='所属人'>{{assetsInfo.ownerText }}</a-descriptions-item>
          </a-descriptions>
        </div>
      </div>
      <!--附加字段-->
      <div v-if="additionalFieldList.length > 0" style="margin-top: 24px; margin-right:1px">
        <div class="colorBox1"  >
          <div class='colorBox'>
            <span class="colorTotal">附加字段</span>
          </div>
          <a-descriptions :column="{ xxl: 2, xl: 2, lg: 2, md: 2, sm: 2, xs: 2 }" bordered>
            <a-descriptions-item v-for='(addItem,idx) in additionalFieldList' :span='additionalFieldList.length % 2 === 1?2:1' :key="'additionalField_'+idx" :label="addItem.name">
              <span v-if="addItem.type == '下拉框'">{{ getDictData(addItem.dictType, addItem.value) }}</span>
              <span v-else>{{ addItem.value }}</span>
            </a-descriptions-item>
          </a-descriptions>
        </div>
      </div>
      <!--设备基础信息-->
      <div class="colorBox1" v-if='Object.keys(deviceInfo).length>0'>
        <div class="colorBox">
          <span class="colorTotal">设备信息</span>
        </div>
        <a-descriptions :column="{ xxl: 2, xl: 2, lg: 2, md: 2, sm: 2, xs: 2 }" bordered>
          <a-descriptions-item label="产品名称">{{ deviceInfo.productName }}</a-descriptions-item>
          <a-descriptions-item label="机柜">{{ positionInfo.location }}{{ positionInfo.cabinetName }}</a-descriptions-item>
          <a-descriptions-item label="U位">{{ positionInfo.layerPool }}</a-descriptions-item>
          <a-descriptions-item label="所在位置">{{ deviceInfo.location }}</a-descriptions-item>
          <a-descriptions-item label="设备说明">{{ deviceInfo.description }}</a-descriptions-item>
        </a-descriptions>
      </div>
      <!--连接参数-->
      <div v-if="collectInfoList.length > 0" style="margin-top: 24px; margin-right:1px">
        <div class="colorBox1" v-for='(cInfoItem,idx) in collectInfoList' :key="'collectInfo_'+idx">
          <div class='colorBox'>
            <span class="colorTotal">{{cInfoItem.key}}连接参数</span>
          </div>
          <a-descriptions v-if="cInfoItem.value.length > 0" :column="{ xxl: 2, xl: 2, lg: 2, md: 2, sm: 2, xs: 2 }"  bordered>
            <a-descriptions-item v-for="(item, index) in cInfoItem.value" :key="'collectInfoItem_'+index" :label="item.displayName" :span='cInfoItem.value.length % 2 === 1?2:1'>
              {{ item.connectValue }}
            </a-descriptions-item>
          </a-descriptions>
        </div>
      </div>
      <!--附件、历史、关联流程-->
      <a-tabs :animated="false" default-active-key="1" @change="callback" style="margin-top: 10px; white-space: nowrap;padding: 8px 0 24px 0px;">
        <a-tab-pane key="1" tab="附件" force-render>
          <div class="file" v-for="(item, index) in filesList" :key="'files_'+index">
            <a-icon type='paper-clip'></a-icon>
            <a :href="filesUrl + item" target="_bank" :download="item"> {{ getFileName(item) }} </a>
          </div>
        </a-tab-pane>
        <a-tab-pane key="2" tab="历史" v-if="!isFromAssetsUpdate">
          <assets-history-list ref="historyList" :assetsId='assetsInfo.id'></assets-history-list>
        </a-tab-pane>
        <a-tab-pane key='3' tab='关系' force-render v-if="!isFromAssetsUpdate">
          <div class='relation-btn-div'>
            <a @click='enlargeTopo' class='icon-fullscreen'>
              <a-icon type='fullscreen' />
            </a>
          </div>
          <div style='height: 400px'>
            <relation-topo v-if="!editStatus" ref='relation' :id='"editContainer1"' :assetsId='assetsInfo.id'></relation-topo>
          </div>
        </a-tab-pane>
        <a-tab-pane key="4" tab="关联流程" v-if="!isFromAssetsUpdate">
          <assets-association-process-list ref="processList" :assets-info='assetsInfo'></assets-association-process-list>
        </a-tab-pane>
      </a-tabs>
      <assets-modal ref="assetsModal" @ok="modalFormOk" @close = "editStatus = false"></assets-modal>
      <relation-topo-modal ref='topoModal'></relation-topo-modal>
    </div>
  </a-spin>
</template>

<script>
import { ajaxGetDictItems, getDictItemsFromCache } from '@/api/api'
import {getAction} from '@/api/manage'
import AssetsAssociationProcessList from '@views/cmdb/assets/modules/AssetsAssociationProcessList.vue'
import AssetsHistoryList from '@views/cmdb/assets/modules/AssetsHistoryList.vue'
import RelationTopo from '@views/cmdb/assets/modules/relationTopo/RelationTopo.vue'
import RelationTopoModal from '@views/cmdb/assets/modules/relationTopo/RelationTopoModal.vue'
export default {
  name: 'AssetsBaseInfo',
  components: {
    AssetsHistoryList,
    AssetsAssociationProcessList,
    RelationTopo,
    RelationTopoModal,
    AssetsModal:()=> import ('@views/cmdb/assets/modules/AssetsModal.vue' )
  },
  props: {
    //控制是否显示基础信息编辑按钮
    editabled: {
      type: Boolean,
      default: false,
      required: true,
    },
    // from资产变更
    isFromAssetsUpdate: {
      type: Boolean,
      default: false,
    }
  },
  data() {
    return {
      confirmLoading: false,
      assetsInfo: {},
      deviceInfo: {},
      positionInfo: {
        roomName: '',
        cabinetName: '',
        layerpool: ''
      },
      collectInfoList: [],//连接参数
      additionalFieldList: [],//附加字段
      assetsStatusList: [],//资产状态下下拉数据
      filesList: [],//附件
      filesUrl: window._CONFIG['domianURL'] + '/sys/common/static/',
      url: {
        edit: '/assets/assets/edit',
        // queryAssetsInfo: '/assets/assets/queryById',//编辑后，重新获取资产信息
        queryAssetsInfo: '/assets/assets/queryByAssetsId',//编辑后，重新获取资产信息
        queryFilesByAssetsId: '/assets/assets/queryByIds',//根据资产id获取附件
        findCodeValue: 'extendField/extendField/findCodeValue',//通过产品分类/资产类型的id,及资产信息的id获取附加字段
        queryDeviceAndPositionInfo: '/device/deviceInfo/selectDeviceInfoAndPositionInfo',//根据资产id，获取设备信息和设备位置信息
        queryCollectInfoList: '/device/deviceInfo/selectConnectInfo',//通过设备id.获取连接参数
        queryStatus: '/assets/assets/queryStatus',//获取资产下拉数据

        updateStatus: '/assets/assets/updateStatus',

        queryTempFilesByAssetsId: '/device/deviceInfoAct/queryByIds', //编辑临时数据时，根据资产id获取附件
        findTempCodeValue: '/device/deviceInfoAct/getAssetsExtend', // 查临时资产的附加字段
        queryTempDeviceAndPositionInfo: '/device/deviceInfoAct/selectDeviceInfoAndPositionInfo', // 查临时资产关联设备的连接参数
      },
      assetsStatus: false,
      assetsStatusName: '',
      assetsStatusId: '',
      assetsId: '', // 资产id
      editStatus:false,
    }
  },

  watch: {
    assetsInfo(val, oldVal) {
      if (this.isFromAssetsUpdate) {
        this.assetsId = val.assetsId
      } else {
        this.assetsId = val.id
      }
      this.init(val)
    }
  },
  methods: {
    show(record) {
      this.assetsInfo = record
    },
    init(info) {
      this.getFilesByAssetsId(this.assetsId)
      this.getAdditionalFieldByCarIdAndAssetsId(info)
      this.getDeviceAndPositionInfo(this.assetsId)
    },
    callback(key) {
    },
    /*根据资产id获取附件*/
    getFilesByAssetsId(assetsId) {
      let httpurl = ''
      if (this.isFromAssetsUpdate) {
          // 资产变更用
        httpurl += this.url.queryTempFilesByAssetsId
      } else {
        httpurl += this.url.queryFilesByAssetsId
      }
      return new Promise((resolve, reject) => {
        getAction(httpurl, {id: this.assetsInfo.id}).then((res) => {
          if (res.success) {
            this.filesList = res.result.fileUrlList
            resolve({ success: true, message: res.message })
          } else {
            reject({ success: false, message: '请求失败' })
          }
        }).catch((err) => {
          reject({ success: false, message: err.message })
        })
      })
    },
    /*根据资产类型id和资产id获取附加字段*/
    getAdditionalFieldByCarIdAndAssetsId(assetsInfo) {
      let httpurl = ''
      let paramObj = {}
      if (this.isFromAssetsUpdate) {
        // 资产变更用
        httpurl += this.url.findTempCodeValue
        paramObj = {
          assetsCategoryId: assetsInfo.assetsCategoryId.trim(),
          assetsActId:  this.assetsInfo.id.trim(),
        }
      } else {
        httpurl += this.url.findCodeValue
        paramObj = {
          assetsCategoryId: assetsInfo.assetsCategoryId.trim(),
          assetsId:  this.assetsId.trim(),
        }
      }
      return new Promise((resolve, reject) => {
        this.additionalFieldList = []
        if (assetsInfo && Object.keys(assetsInfo)) {
          if (assetsInfo.assetsCategoryId && this.assetsId) {
            getAction(httpurl, paramObj).then((res) => {
              if (res.success) {
                this.additionalFieldList = res.result
                resolve({ success: true, message: res.message })
              } else {
                reject({ success: false, message: res.message })
              }
            }).catch((err) => {
              reject({ success: false, message: err.message })
            })
          }
        } else {
          resolve({ success: true, message: '请求成功' })
        }
      })
    },
    /*通过资产id，获取设备信息、及物理位置信息*/
    getDeviceAndPositionInfo(assetsId) {
      this.deviceInfo = {}
      this.positionInfo = { roomName: '', cabinetName: '', layerpool: '' }
      let httpurl = ''
      let paramObj
      if (this.isFromAssetsUpdate) {
        // 资产变更用：编辑临时表数据
        httpurl += this.url.queryTempDeviceAndPositionInfo
        paramObj = { assetsActId: this.assetsInfo.id }
      } else {
        httpurl += this.url.queryDeviceAndPositionInfo
        paramObj = { assetsId: assetsId }
      }
      return new Promise((resolve, reject) => {
        getAction(httpurl, paramObj).then((res) => {
          if (res.success) {
            let posInfo = res.result.cabinet2device
            if (posInfo && Object.keys(posInfo)) {
              Object.assign(this.positionInfo, posInfo)
            }
            let devInfo = res.result.deviceInfo
            if (devInfo && Object.keys(devInfo)) {
              this.deviceInfo = devInfo
            }
            this.$emit('getDeviceInfo',this.deviceInfo)
            this.getCollectInfoListByDeviceId(devInfo, res.result.connectInfoArray)
          }
        })
      })
    },
    /*通过设备id.获取连接参数*/
    getCollectInfoListByDeviceId(deviceInfo, connectInfoArray) {
      this.collectInfoList = []
      return new Promise((resolve, reject) => {
        if (this.isFromAssetsUpdate) {
          // 资产变更：编辑临时资产
          if (connectInfoArray && connectInfoArray.length > 0) {
            this.collectInfoList = connectInfoArray
          } else {
            this.collectInfoList = []
          }
          resolve({success:true,message:'请求成功'})
        } else {
          if (deviceInfo && Object.keys(deviceInfo).length > 0) {
            getAction(this.url.queryCollectInfoList, { deviceId: deviceInfo.id }).then((res) => {
              if (!!res) {
                this.collectInfoList = res
                resolve({ success: true, message: '请求成功' })
              } else {
                reject({
                  success: false,
                  message: '请求失败'
                })
              }
            }).catch((err) => {
              reject({
                success: false,
                message: '请求失败'
              })
            })
          } else {
            resolve({ success: true, message: '请求成功' })
          }
        }
      })
    },
    queryAssetsStatusList() {
      getAction(this.url.queryStatus, {
        assetsId: this.assetsInfo.id,
      }).then((res) => {
        if (this.assetsId == '') {
          return
        }
        if (res.success) {
          this.assetsStatusList = res.result
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    editButton() {
      this.assetsStatus = true
    },
    /*保存资产状态*/
    preservationStates() {
      let data = {
        updateAssetsId: this.assetsId,
        status_name: this.assetsStatusName,
        status_id: this.assetsStatusId,
      }
      putAction(this.url.updateStatus, data).then((res) => {
        if (res.code == 200) {
          this.assetsStatus = false
          this.cardTopData.statusId = this.assetsStatusId
          this.cardTopData.statusName = this.assetsStatusName
        }
      })
    },
    selectAssetsStatus(value) {
      if (value) {
        this.assetsStatusName = value.label
        this.assetsStatusId = value.key
      } else {
        this.assetsStatusName = ''
        this.assetsStatusId = ''
      }
    },
    //返回上一级
    getGo() {
      let tabKey = this.$refs.assetsTabs.defaultActiveKey
      if (tabKey == '4') {
        this.$refs.assetsTabs.$refs.journalManagement.pButton1(0)
      } else if (tabKey == '7') {
        this.$refs.assetsTabs.$refs.alarmManagement.pButton1(0)
      } else if (tabKey == '9') {
        this.$refs.assetsTabs.$refs.deviceAlarmManagement.pButton1(0)
      }
      this.$refs.assetsTabs.defaultActiveKey = '1'
      this.$parent.pButton1(0)
    },
    /*编辑操作*/
    doEdit: function(record) {
      this.editStatus = true;
      this.$refs.assetsModal.edit(record)
      this.$refs.assetsModal.title = '编辑'
      this.$refs.assetsModal.disableSubmit = false
    },
    /*编辑后，重新加载查看信息*/
    modalFormOk() {
      getAction(this.url.queryAssetsInfo, {
        assetsId: this.assetsInfo.id,
      }).then((res) => {
        if (res.code == 200) {
          // this.assetsInfo =
          this.$emit('changeAssetsBaseInfo',res.result)
          this.getFilesByAssetsId(this.assetsInfo.id)
          this.getAdditionalFieldByCarIdAndAssetsId(this.assetsInfo)
          this.getDeviceAndPositionInfo(this.assetsInfo.id)
          this.editStatus =false;
        }
      })
    },
   /* modalFormOk() {
      getAction(this.url.queryAssetsInfo, {
        id: this.assetsInfo.id,
      }).then((res) => {
        if (res.code == 200) {
          // this.assetsInfo =
          this.$emit('changeDeviceBaseInfo',res.result)
          this.getFilesByAssetsId(this.assetsInfo.id)
          this.getAdditionalFieldByCarIdAndAssetsId(this.assetsInfo)
          this.getDeviceAndPositionInfo(this.assetsInfo.id)
        }
      })
    },*/
    /*打开派发窗口*/
    handleDispatch() {
      this.$refs.assetsDispatch.edit(this.assetsInfo.id)
      this.$refs.assetsDispatch.title = '派发'
      this.$refs.assetsDispatch.disableSubmit = false
    },
    getDictData(dictCode, dictValue) {
      if (dictCode != null && dictCode != '') {
        //优先从缓存中读取字典配置
        if (getDictItemsFromCache(dictCode)) {
          const dictOptions = getDictItemsFromCache(dictCode)
          const obj =  dictOptions.filter(ele => {
            return dictValue == ele.value
          });
          if (obj[0]) {
            return obj[0].text
          } else {
            return dictValue
          }
        }
      }
    },

    /*打开更大的拓扑图界面*/
    enlargeTopo() {
      this.$refs.topoModal.show(this.assetsInfo.id)
      this.$refs.topoModal.title = '关系拓扑'
      this.$refs.topoModal.disableSubmit = true
    },
    getFileName(path) {
      if (path.lastIndexOf("\\") >= 0) {
        let reg = new RegExp("\\\\", "g");
        path = path.replace(reg,"/");
      }
      return path.substring(path.lastIndexOf("/") + 1);
    }
  }
}
</script>

<style lang='less' scoped='scoped'>
.scroll{
  height: 100%;
  overflow: hidden;
  overflow-y: auto;
}
::v-deep .ant-descriptions-view {
  border-radius: 0px;
}

::v-deep .ant-descriptions-bordered .ant-descriptions-item-label {
  background-color: rgb(250, 250, 250);
  text-align: center;
  width: 17%;
}

::v-deep .ant-descriptions-item-label,
.ant-descriptions-item-content {
  color: rgb(96, 98, 102) !important;
}

::v-deep .ant-descriptions-bordered .ant-descriptions-item-content {
  width: 35%;
}

.colorBox1 {
  margin-bottom: 20px;
  margin-right: 1px;
}

.colorBox {
  margin-bottom: 10px;
}

.colorTotal {
  padding-left: 7px;
  border-left: 4px solid #1e3674;
}

::v-deep .ant-descriptions {
  width: 100%;
}

.relation-btn-div {
  display: flex;
  justify-content: flex-start;
  padding-bottom: 8px;
  position: relative;

/*  .add-btn {
    height: 28px;
    margin-right: 16px;
    background: #ecf5ff;
    border: 1px solid #b3d8ff;
    border-radius: 4px;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #409eff;
  }

  .enlarge-btn {
    height: 28px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #737578;
  }*/

  .icon-fullscreen {
    /*position: absolute;
    right: 1%;
    top: 15%;*/
    position:absolute;
    content: "";
    top:16px;
    right:0;
    z-index: 100000;
  }
}
</style>