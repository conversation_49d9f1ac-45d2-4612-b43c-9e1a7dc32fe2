<template>
  <j-modal
    :title='title'
    :width='width'
    :centered='true'
    :visible='visible'
    :confirmLoading='confirmLoading'
    switchFullscreen
    @ok='handleOk'
    @cancel='handleCancel'
    cancelText='关闭'
  >
    <a-spin :spinning='confirmLoading'>
      <a-form :form='form'>
        <a-form-item
          class='two-words'
          label='类型'
          :labelCol='labelCol'
          :wrapperCol='wrapperCol'
        >
          <j-tree-select-expand
            v-decorator="['configType', validatorRules.configType]"
            placeholder='请选择类型'
            dict='itil_config_item_type,type_name,id'
            condition='{"del_type":0}'
            pidField='pid'
            pidValue=''
            @change='onChange'
          />
        </a-form-item>
        <a-form-item
          class='two-words'
          label='名称'
          :labelCol='labelCol'
          :wrapperCol='wrapperCol'
        >
          <a-input
            v-decorator="['name', validatorRules.name]"
            :allowClear='true'
            autocomplete='off'
            placeholder='请输入名称'
          />
        </a-form-item>
        <a-form-item
          class='two-words'
          label='状态'
          :labelCol='labelCol'
          :wrapperCol='wrapperCol'
        >
          <component
            :is='LcDict'
            :trigger-change='true'
            v-decorator="['state', { initialValue: '', rules: [{ required: true, message: '不能为空' }] }]"
            placeholder='请选择状态'
            dictCode='itil_config_item_type_state'
          ></component>
        </a-form-item>
        <a-form-item
          label='IP地址'
          :labelCol='labelCol'
          :wrapperCol='wrapperCol'
        >
          <a-input
            v-decorator="['ipAddress', validatorRules.ipAddress]"
            :allowClear='true'
            autocomplete='off'
            placeholder='请输入IP地址'
          />
        </a-form-item>
        <a-form-item
          label='采购日期'
          :labelCol='labelCol'
          :wrapperCol='wrapperCol'
        >
          <a-date-picker
            v-decorator="['purchaseTime']"
            style='width: 100%'
          />
        </a-form-item>
        <a-form-item
          label='保修期(月)'
          :labelCol='labelCol'
          :wrapperCol='wrapperCol'
        >
          <a-input-number
            v-decorator="['warrantyTime', validatorRules.warrantyTime]"
            :min='0'
            placeholder='请输入保修期'
            style='width: 100%'
          />
        </a-form-item>
        <a-form-item
          label='所属人'
          :labelCol='labelCol'
          :wrapperCol='wrapperCol'
        >
          <j-select-user-by-dep
            v-decorator="['owner', {initialValue:'',rules:validatorRules.owner.rules}]"
            :multi='true'
          >
          </j-select-user-by-dep>
        </a-form-item>
        <a-form-item
          label='联系电话'
          :labelCol='labelCol'
          :wrapperCol='wrapperCol'
        >
          <a-input
            v-decorator="['contact', validatorRules.contact]"
            :allowClear='true'
            autocomplete='off'
            placeholder='请输入联系电话'
          />
        </a-form-item>
        <a-form-item
          label='资产净值(元)'
          :labelCol='labelCol'
          :wrapperCol='wrapperCol'
        >
          <a-input-number
            v-decorator="['netWorth', validatorRules.netWorth]"
            :formatter="(value) => ` ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
            :parser="(value) => value.replace(/\$\s?|(,*)/g, '')"
            :min='0'
            placeholder='请输入资产净值'
            style='width: 100%'
          />
        </a-form-item>
        <a-form-item
          label='配置项描述'
          :labelCol='labelCol'
          :wrapperCol='wrapperCol'
        >
          <a-textarea
            v-decorator="['describes', validatorRules.describes]"
            placeholder='请输入配置项描述'
            :auto-size='{ minRows: 2, maxRows: 6 }'
          />
        </a-form-item>
        <a-form-item
          class='two-words'
          label='附件'
          :labelCol='labelCol'
          :wrapperCol='wrapperCol'
        >
          <j-upload
            v-decorator="['annexs']"
            :number='5'
          ></j-upload>
        </a-form-item>
      </a-form>
    </a-spin>
  </j-modal>
</template>

<script>
import { httpAction } from '@/api/manage'
import pick from 'lodash.pick'
import { validateDuplicateValue } from '@/utils/util'
import JTreeSelectExpand from '@/components/jeecg/JTreeSelectExpand'
import JSelectUserByDep from '@/components/jeecgbiz/JSelectUserByDep'
import JUpload from '@/components/jeecg/JUpload'
import { phoneValidator } from '@/mixins/phoneValidator'
import moment from 'moment'
export default {
  name: 'itilConfigItemLibraryModal',
  mixins: [phoneValidator],
  components: {
    JTreeSelectExpand,
    JSelectUserByDep,
    JUpload
  },
  data() {
    return {
      tips: '',
      validateStatus: 'success',
      form: this.$form.createForm(this),
      title: '操作',
      width: '800px',
      visible: false,
      model: {owner:''},
      labelCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 5
        }
      },
      wrapperCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 16
        }
      },
      confirmLoading: false,
      validatorRules: {
        configType: {
          rules: [
            {
              required: true,
              message: '请输入类型!'
            }
          ]
        },
        name: {
          rules: [
            { required: true, message: '请输入名称!' },
            { min: 2, max: 20, message: '名称长度应在2-20个字符之间!', trigger: 'blur' }
          ]
        },
        state: {
          rules: [
            {
              required: true,
              message: '请输入状态!'
            }
          ]
        },
        owner: {
          rules: [
            {
              required: true,
              message: '请输入所属人!'
            }
          ]
        },
        contact: {
          rules: [{ validator: this.phone }]
          /*rules: [
            {
              required: true,
              pattern: /^((13[0-9])|(17[0-1,6-8])|(15[^4,\\D])|(18[0-9]))\d{8}$/,
              message: '请输入联系电话!',
            },
          ],*/
        },
        describes: {
          rules: [
            { required: true, message: '请输入配置项描述!' },
            { max: 100, message: '配置项描述不可超过100个字符', trigger: 'blur' }
          ]
        },
        ipAddress: {
          rules: [
            {
              required: true,
              pattern:
                /^(\d|[1-9]\d|1\d{2}|2[0-5][0-5])\.(\d|[1-9]\d|1\d{2}|2[0-5][0-5])\.(\d|[1-9]\d|1\d{2}|2[0-5][0-5])\.(\d|[1-9]\d|1\d{2}|2[0-5][0-5])$/,
              message: '请输入正确的IP地址!'
            }
          ]
        },
        warrantyTime: {
          rules: [
            {
              required: true,
              message: '请输入保修期'
            }
          ]
        },
        netWorth: {
          rules: [
            {
              required: true,
              message: '请输入资产净值'
            }
          ]
        }
      },
      url: {
        add: '/itilconfigitemlibrary/itilConfigItemLibrary/add',
        edit: '/itilconfigitemlibrary/itilConfigItemLibrary/edit'
      }
    }
  },
  created() {
  },
  computed: {
    //可行性测试，根据文件路径动态加载组件
    LcDict: function() {
      var myComponent = () => import(`@/components/dict/JDictSelectTag`)
      return myComponent
    }
  },
  methods: {
    onChange(value) {
      const that = this
      if ('1304295391790104578' == value) {
        this.model.configType = null
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.model, 'configType'))
        })
        that.$message.warning('请选择其他类型')
      }
    },
    add() {
      this.edit({
        purchaseTime: 'null',
      })
    },
    moment,
    edit(record) {
     /* this.form.resetFields()*/
      this.model = Object.assign({purchaseTime:'null',owner:''}, record)
      let pTime=this.model.purchaseTime
      this.model.purchaseTime=pTime?moment(pTime):'null'
      this.visible = true
      // this.model.purchaseTime=this.model.purchaseTime? memont(this.model.purchaseTime):null
      this.$nextTick(() => {
        this.form.setFieldsValue(
          pick(
            this.model,
            'configType',
            'state',
            'name',
            'owner',
            'ipAddress',
            'purchaseTime',
            'warrantyTime',
            'contact',
            'netWorth',
            'describes',
            'annexs'
          )
        )
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
      this.validateStatus = 'success'
      this.tips = ''
    },
    handleOk() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let formData = Object.assign(this.model, values)
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
              that.close()
            })
        }
      })
    },
    handleCancel() {
      this.close()
    },
    popupCallback(row) {
      this.form.setFieldsValue(
        pick(
          row,
          'configType',
          'state',
          'code',
          'name',
          'owner',
          'ipAddress',
          'purchaseTime',
          'warrantyTime',
          'contact',
          'netWorth',
          'describes',
          'annexs'
        )
      )
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/normalModal.less';
</style>