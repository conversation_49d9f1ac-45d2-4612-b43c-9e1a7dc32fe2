export default {
  data() {
    return {}
  },
  watch:{
    "$store.state.deviceStatus.readyState"(e){
      if(e === 1){
        this.addMessageListener()
      }
      else{
        this.removeMessageListener()
      }

    }
  },
  created() {

  },
  mounted() {
   this.addMessageListener()
  },
  beforeDestroy() {
   this.removeMessageListener()
  },
  methods: {
    addMessageListener(){
      if (window.WEBSOCKET && window.WEBSOCKET.readyState === 1) {
        window.WEBSOCKET.addEventListener('message', this.websocketOnmessage)
      }
    },
    removeMessageListener(){
      if(window.WEBSOCKET){
        window.WEBSOCKET.removeEventListener('message', this.websocketOnmessage)
      }
    },
    // 此方法建议在组件内写
    websocketOnmessage(e) {
    }
  }
}