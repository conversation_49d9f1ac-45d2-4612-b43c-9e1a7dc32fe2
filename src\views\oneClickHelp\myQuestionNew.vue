<template>
  <div class="my-question">
    <div class="topImg">
      <div class='head-left'></div>
      <div class='head-right'></div>
    </div>
    <div class="my-question_bottom" ref="pageBody">
      <div class="leftList">
        <div v-for="(item, index) in listData" :key="index" :class="index === subscript ? 'select' : '_select'">
          <a @click="treeSelect(item.id)">{{ item.name }}</a>
        </div>
      </div>
      <div class="lightXian"></div>
      <div class="rightList" ref="rightBox">
        <!-- 搜索区域 -->
        <a-card :bodyStyle="{ paddingBottom: '0' }" class="searchBox" :bordered="false" ref="searchBox">
          <a-form class="ant-advanced-search-form">
            <a-row :gutter="24">
              <a-col :span="formSpan">
                <a-form-item label="时间范围">
                  <a-select placeholder="请选择" v-model="queryParam.timeRange" :allowClear="true"
                    :getPopupContainer="(node) => node.parentNode">
                    <a-select-option value="1">最近一天</a-select-option>
                    <a-select-option value="3">最近三天</a-select-option>
                    <a-select-option value="7">最近七天</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="formSpan">
                <a-form-item label="问题类型">
                  <a-select placeholder="请选择" v-model="queryParam.questionType" :allowClear="true"
                    :getPopupContainer="(node) => node.parentNode">
                    <a-select-option v-for="(item, index) in IssueType" :key="index" :value="item.value">
                      {{ item.text }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="formSpan">
                <a-form-item label="模糊查询">
                  <a-input :allowClear="true" placeholder="请输入关键字" v-model="queryParam.queryKeywords" />
                </a-form-item>
              </a-col>
              <a-col :span="formSpan" v-if="oneClickHelp">
                <a-form-item>
                  <span>
                    <a-button type="primary" @click="myQSearch" class="search">查询</a-button>
                    <a-button @click="myQReset" style="margin-left: 15px" class="btn-reset-style">重置</a-button>
                  </span>
                </a-form-item>
              </a-col>
              <a-col :span="6" v-else>
                <a-form-item>
                  <span class="search-btns">
                    <a-button @click="myQReset" style="margin-left: 15px" class="btn-reset-style">重置</a-button>
                    <a-button type="primary" @click="myQSearch" class="search">查询</a-button>
                  </span>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-card>
        <!-- </div> -->
        <div ref="qbox" class="qbox" :style="{ height: qboxHeight }">
          <div :style="{ height: listHeight }">
            <div class="rightOne" v-if="subscript == 0">
              <div class="rightOne-item" v-for="(item, index) in rightData" :key="index">
                <img src="@/assets/question.png" alt="" class="question-img" />
                <div class="rightOne-item-top-right">
                  <span class="rightOne-item-top-title" style="margin-left: 0px; color: #409eff; cursor: pointer"
                    @click="getInfo(item.id)">
                    {{ item.question }}
                  </span>
                  <div class="rightOne-item-top-time">{{ item.createTime }}</div>

                  <div class="rightOne-item-center">流程步骤：{{ item.answererContent }}</div>
                  <div class="rightOne-item-center" v-if="item.unsolveCause !== null">
                    未解决原因：{{ item.unsolveCause }}
                  </div>
                  <div class="rightOne-item-bottom">
                    <a-button @click="clos(item)" class="btn1"> 撤销问题 </a-button>
                    &nbsp;&nbsp;
                    <a-button type="primary" @click="urge(item.id)" class="btn2" style="width: 77px"> 催办 </a-button>
                  </div>
                </div>
              </div>
            </div>

            <div class="rightOne" v-if="subscript == 1">
              <div class="rightOne-item" v-for="(item, index) in rightData" :key="index">
                <img src="@/assets/question.png" alt="" class="question-img" />
                <div class="rightOne-item-top-right">
                  <span class="rightOne-item-top-title" style="margin-left: 0px; color: #409eff; cursor: pointer"
                    @click="getInfo(item.id)">
                    {{ item.question }}
                  </span>
                  <div class="rightOne-item-top-time">{{ item.createTime }}</div>
                  <div class="rightOne-item-center">
                    <div class="rightTwo-item-center-top">
                      <span> 流程步骤：{{ item.answererContent }} </span>
                      <span>
                        {{ item.confirmTime }}
                      </span>
                    </div>
                    <div class="rightOne-item-center" v-if="item.unsolveCause !== null">
                      未解决原因：{{ item.unsolveCause }}
                    </div>
                    <div class="rightOne-item-center-center"></div>
                  </div>
                  <div class="rightOne-item-bottom">
                    <a-button v-if="!item.associationId" type="primary" @click="resolvedQuestionId(item.id)" class='btn2'> 评价 </a-button>
                    <a-button v-else type="primary" @click="resolved(item.associationId)" class='btn2'> 评价 </a-button>
                  </div>
                </div>
              </div>
            </div>

            <div class="rightOne" v-if="subscript == 2">
              <div class="rightOne-item" v-for="(item, index) in rightData" :key="index">
                <img src="@/assets/question.png" alt="" class="question-img" />
                <div class="rightOne-item-top-right">
                  <span class="rightOne-item-top-title" style="margin-left: 0px; color: #409eff; cursor: pointer"
                    @click="getInfo(item.id)">
                    {{ item.question }}
                  </span>
                  <div class="rightOne-item-top-time">{{ item.createTime }}</div>
                  <div class="rightOne-item-center">
                    <div class="rightTwo-item-center-top">
                      <span> 流程步骤：{{ item.answererContent }} </span>
                      <span>
                        {{ item.confirmTime }}
                      </span>
                    </div>
                    <div class="rightOne-item-center-center"></div>
                  </div>
                  <div class="rightOne-item-bottom">
                    <a-button v-if="!item.associationId" type="primary"  @click="resolvedLookQuestionId(item.id)"  class='btn2'> 查看评价 </a-button>
                    <a-button v-else type="primary" @click="resolvedLook(item.associationId)" class='btn2'> 查看评价 </a-button>
                  </div>
                </div>
              </div>
            </div>

            <div class="rightOne" v-if="subscript == 3">
              <div class="rightOne-item" v-for="(item, index) in rightData" :key="index">
                <img src="@/assets/question.png" alt="" class="question-img" />
                <div class="rightOne-item-top-right">
                  <span class="rightOne-item-top-title" style="margin-left: 0px; color: #409eff; cursor: pointer"
                    @click="getInfo(item.id)">
                    {{ item.question }}
                  </span>
                  <div class="rightOne-item-top-time">{{ item.createTime }}</div>
                  <div class="rightOne-item-center">
                    <div class="rightTwo-item-center-top">
                      <span> 流程步骤：{{ item.answererContent }} </span>
                      <span>
                        {{ item.confirmTime }}
                      </span>
                    </div>
                  </div>
                  <div class="rightOne-item-bottom">
                    <a-button type="primary" @click="open(item)" class="btn2"> 取消撤销 </a-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="pagination" ref="pageBox">
            <a-pagination :pageSize="pageInfo.pageSize" :total="pageInfo.total" :current="pageInfo.current"
              @change="qPageChange" />
          </div>
        </div>
      </div>

      <!-- 查看页面 -->
      <question-Info-Modal ref="modalForm" @ok="modalFormOk"></question-Info-Modal>
      <!--  评价  -->
      <evaluation-modal ref="evaluationModal" @ok="searchReset"></evaluation-modal>
    </div>
  </div>
</template>
<script>
  import {
    ajaxGetDictItems
  } from '@api/api'
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import QuestionInfoModal from './QuestionInfoModal'
  import {
    postAction,
    httpAction,
    getAction,
    deleteAction
  } from '@/api/manage'
  import EvaluationModal from './EvaluationModal.vue'
  import {
    YqFormSearchLocation
  } from '@/mixins/YqFormSearchLocation'
  import {
    getHostNameLocal
  } from '@/utils/util'
  export default {
    name: 'myQuestion',
    mixins: [JeecgListMixin, YqFormSearchLocation],
    components: {
      EvaluationModal,
      QuestionInfoModal,
    },
    data() {
      return {
        columns: [],
        formItemLayout: {
          md: {
            span: 6,
          },
          sm: {
            span: 12,
          },
        },
        listData: [{
            name: '待解决',
            id: 0,
          },
          {
            name: '待评价',
            id: 1,
          },
          {
            name: '已评价',
            id: 2,
          },
          {
            name: '已撤销',
            id: 3,
          },
        ],
        subscript: 0,
        url: {
          list: '/question/question/listOften111', //查询列表
          delete: '/question/question/delete', //关闭接口
          editFront: '/question/question/editFront', //编辑
          edit: '',
        },
        rightData: [],
        weight: 1,
        ipAddr: '',
        //工单状态
        WorkOrderStatus: [],
        //问题类型
        IssueType: [],
        listHeight: 0,
        qboxHeight: 0,
        pageInfo: {
          pageSize: 5,
          total: 0,
          current: 1,
        },
        labelCol: {
          span: 8
        },
        wrapperCol: {
          span: 16
        },
      }
    },
    created() {

    },
    mounted() {
      this.subscript = 0
      this.getDataList(this.subscript)
      this.getDicData()
      setTimeout(() => {
        this.computeQbox()
      }, 10);
    },
    computed: {
      oneClickHelp() {
        return this.$route.meta.oneClickHelp
      },
      formSpan() {
        if (this.oneClickHelp) {
          return 24 / 4
        } else {
          return 24 / 4
        }
      }
    },
    methods: {
      qPageChange(pageNum, pageSize) {
        this.pageInfo.current = pageNum
        this.pageInfo.pageSize = pageSize
        this.getDataList()
      },
      modalFormOk() {},
      searchReset() {
        this.myQReset()
      },
      computeQbox() {
        let rh = this.$refs.rightBox.getBoundingClientRect().height
        let sh = this.$refs.searchBox.$el.getBoundingClientRect().height
        let ph = this.$refs.pageBox.getBoundingClientRect().height
        this.qboxHeight = rh - sh
        this.listHeight = rh - sh - ph - 46 + 'px'
      },
      //查询字典
      getDicData() {
        this.getWorkOrderStatus('WorkOrder_status')
        this.getIssueType('serviceRequestClass')
      },
      getWorkOrderStatus(code) {
        ajaxGetDictItems(code, null).then((res) => {
          let temp = res.result
          for (let i = 0; i < temp.length; i++) {
            if (temp[i].text == '全部') {
              this.WorkOrderStatus.push({
                text: temp[i].text,
                value: ''
              })
            } else {
              this.WorkOrderStatus.push({
                text: temp[i].text,
                value: temp[i].text
              })
            }
          }
        })
      },
      getIssueType(code) {
        ajaxGetDictItems(code, null).then((res) => {
          let temp = res.result
          for (let i = 0; i < temp.length; i++) {
            if (temp[i].text == '全部') {
              this.IssueType.push({
                text: temp[i].text,
                value: ''
              })
            } else {
              this.IssueType.push({
                text: temp[i].text,
                value: temp[i].value
              })
            }
          }
        })
      },
      //催办
      urge(questionId) {
        getAction('/question/question/queryById', {
          id: questionId
        }).then((res) => {
          if (res.success) {
            this.weight = res.result.weight
            this.weight = this.weight + 1
            postAction('/question/question/editUrge', {
              id: questionId,
              weight: this.weight
            }).then((res) => {
              if (res.success) {
                this.$message.success(res.message)
                this.getDataList(this.subscript)
                this.$emit('ok')
              } else {
                this.$message.warning(res.message)
              }
            })
          }
        })
      },
      //查看详情
      getInfo(questionId) {
        this.$refs.modalForm.title = '问题详情'
        this.$refs.modalForm.show(questionId)
      },
      //点击左侧状态树
      treeSelect(i) {
        if (this.subscript === i) return
        this.pageInfo.current = 1
        this.subscript = i
        this.getDataList()
      },
      myQReset() {
        this.queryParam = {}
        this.pageInfo.current = 1
        this.getDataList()
      },
      myQSearch() {
        this.pageInfo.current = 1
        this.getDataList()
      },

      getDataList() {
        let hostName = getHostNameLocal()
        if (hostName === null) {
          this.$message.warning('请检查是否已经添加终端信息？')
          return
        }
        let params = {
          ...this.queryParam,
          status: this.subscript,
          pageNo: this.pageInfo.current,
          pageSize: this.pageInfo.pageSize,
          ip: hostName,
        }
        getAction(this.url.list, params).then((res) => {
          if (res.success) {
            this.rightData = res.result.records
            this.pageInfo.total = res.result.total
          }
        })
      },
      //关闭并刷新数据
      clos(question) {
        var that = this
        this.$confirm({
          title: '确认撤销',
          content: '是否撤销选中数据?',
          onOk: function () {
            that.loading = true
            getAction('/question/question/revoke', {
                questionId: question.id
              })
              .then((res) => {
                if (res.success) {
                  that.getDataList(that.subscript)
                  that.$message.success(res.message)
                  that.$emit('ok')
                } else {
                  that.$message.warning(res.message)
                }
              })
              .finally(() => {
                that.loading = false
              })
          },
        })
      },
      //删除并刷新数据
      dele(question) {
        var that = this
        this.$confirm({
          title: '确认删除',
          content: '是否删除选中数据?',
          onOk: function () {
            that.loading = true
            deleteAction('/question/question/delete', {
                id: question.id
              })
              .then((res) => {
                if (res.success) {
                  that.getDataList(that.subscript)
                  that.$message.success(res.message)
                  that.$emit('ok')
                } else {
                  that.$message.warning(res.message)
                }
              })
              .finally(() => {
                that.loading = false
              })
          },
        })
      },
      //取消关闭并刷新数据
      open(question) {
        var that = this
        this.$confirm({
          title: '确认取消关闭',
          content: '是否取消关闭选中数据?',
          onOk: function () {
            that.loading = true
            getAction('/question/question/cancelRevoke', {
                questionId: question.id
              })
              .then((res) => {
                if (res.success) {
                  that.getDataList(that.subscript)
                  that.$message.success(res.message)
                  that.$emit('ok')
                } else {
                  that.$message.warning(res.message)
                }
              })
              .finally(() => {
                that.loading = false
              })
          },
        })
      },
      //评价
      resolved(id) {
        this.$refs.evaluationModal.add(id)
        this.$refs.evaluationModal.title = '评价'
        this.$refs.evaluationModal.disableSubmit = false
      },
      //根据问题id评价
      resolvedQuestionId(id) {
        this.$refs.evaluationModal.addQuestion(id)
        this.$refs.evaluationModal.title = '评价'
        this.$refs.evaluationModal.disableSubmit = false
      },
      //查看评价
      resolvedLook(id) {
        this.$refs.evaluationModal.edit(id)
        this.$refs.evaluationModal.title = '查看评价'
        this.$refs.evaluationModal.disableSubmit = false
      },
      //根据问题id查看评价
      resolvedLookQuestionId(id) {
        this.$refs.evaluationModal.editQuestion(id)
        this.$refs.evaluationModal.title = '查看评价'
        this.$refs.evaluationModal.disableSubmit = false
      },
      //未解决
      unresolved(id) {
        this.$prompt('', '未解决的原因', {
            confirmButtonText: '确定',
            closeOnClickModal: false,
            cancelButtonText: '取消',
            inputPlaceholder: '请输入',
            confirmButtonClass: 'confirmButton',
            inputValidator: function (value) {
              if (value === '' || value === null || value.trim().length == 0) {
                return false
              }
            },
            inputErrorMessage: '请填写未解决的原因',
          })
          .then((value) => {
            //  this.$message.success('已填写')
            var that = this
            let method = 'post'
            let formData = {
              id: id,
              status: '未解决',
              unsolveCause: value.value,
            }
            httpAction(that.url.editFront, formData, method)
              .then((res) => {
                if (res.success) {
                  that.$message.success(res.message)
                  that.getDataList(that.subscript)
                } else {
                  that.$message.warning(res.message)
                }
              })
              .finally(() => {
                that.confirmLoading = false
              })
          })
          .catch(() => {
            this.$message.info('已取消')
          })
      },
    },
  }
</script>
<style lang="less" scoped>
  @import '~@assets/less/onclickStyle.less';

  .topImg {
    height: 50px;
    width: 100%;
    background-image: url('../../../public/oneClickHelp/localDeviceInfo/leftHeadBg.png');
    background-repeat: no-repeat;
    background-position: right center;
    position: relative;

    .head-left::before {
      position: absolute;
      content: '';
      top: 0;
      left: 0;
      width: 3px;
      height: 3px;
      background-color: #2F5BFF;
    }

    .head-left::after {
      position: absolute;
      content: '';
      bottom: 0;
      left: 0;
      width: 3px;
      height: 3px;
      background-color: #2F5BFF;
    }

    .head-right::before {
      position: absolute;
      content: '';
      top: 0;
      right: 0;
      width: 3px;
      height: 3px;
      background-color: #2F5BFF;
    }

    .head-right::after {
      position: absolute;
      content: '';
      bottom: 0;
      right: 0;
      width: 3px;
      height: 3px;
      background-color: #2F5BFF;
    }
  }

  ::-webkit-scrollbar {
    display: none;
  }

  .lightXian {
    margin-top: 24px;
    height: calc(100% - 24px);
    width: 1px;
    margin-left: 10px;
    background: linear-gradient(to bottom, #0C1A32 0%, rgb(20, 100, 219, .8) 50%, #0B1931 100%);
  }

  .search-btns {
    display: flex;
    flex-direction: row-reverse;
    justify-content: center;
    margin-top: 6px;
  }

  // @import '~@assets/less/common.less';
  // @import '~@assets/less/scroll.less';
  .my-question {
    height: 100%;
    width: 100%;
    background-image: url('../../assets/img/yunweiBackground.png');
    background-size: 100%;
    background-repeat: no-repeat;
    background-color: #091425;
  }

  .my-question_bottom {
    width: 100%;
    height: calc(100% - 50px);
    display: flex;
    display: flex;
    // background: #fff;
    @primaryColor: #409eff;

    .search {
      background-color: @primaryColor;
      border-color: @primaryColor;
      height: 28px;
      width: 73px;
    }

    .leftList {
      width: 261px;
      padding: 21px 0px;

      div {
        width: 100%;
        height: 45px;
        line-height: 45px;
        text-align: center;
        font-size: 16px;
        letter-spacing: 1px;

        a {
          color: rgba(0, 0, 0, 0.65);
        }
      }

      .select {
        background: linear-gradient(90deg, #0A1425 0%, rgba(8, 83, 255, 0.32) 51%, #101B2B 100%);
        margin-bottom: 15px;

        a {
          color: white;
        }
      }

      ._select {
        margin-bottom: 15px;

        a {
          color: white;
        }
      }
    }

    .rightList {
      width: calc(100% - 237px);
      height: 100%;
      overflow: hidden;
      padding: 0 16px;

      .searchBox {
        border-radius: 0;
        background-color: rgba(255, 255, 255, 0);
      }

      .qbox {
        padding: 14px 24px 0;
      }

      .rightOne {
        overflow-y: auto;
        width: 100%;
        height: calc(100% - 0px);
        padding-right: 20px;

        .rightOne-item {
          width: 100%;
          border-bottom: 1px dashed rgba(151, 151, 151, 0.35);
          display: flex;
          align-items: top;
          margin-top: 49px;

          .question-img {
            width: 24px;
            height: 24px;
            margin-right: 10px;
          }

          .rightOne-item-top-right {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            color: #1f3773;
            font-weight: 600;
            font-size: 16px;
            width: 100%;

            .rightOne-item-top-title {
              font-size: 18px;
              margin-top: -3px;
            }
          }

          .rightOne-item-top-time {
            font-size: 14px;
            color: #8a8a8a;
            letter-spacing: 0.88px;
            font-weight: 400;
            margin-top: 2px;
          }

          // }
          .rightOne-item-center {
            width: 100%;
            margin-top: 26px;
            line-height: 28px;
            display: flex;
            align-items: center;
            font-size: 14px;
            font-weight: 300;
            color: #FFFFFF;

            .rightTwo-item-center-top {
              width: 100%;
              height: 30px;
              display: flex;
              align-items: center;
              justify-content: space-between;
            }
          }

          .rightOne-item-bottom {
            padding-left: 35px;
            padding: 35px 0;
            display: flex;
            align-items: center;

            .btn1 {
              background-color: rgba(255, 255, 255, 0);
              color: #E4E4E4;
            }

            .btn2 {
              background-color: @primaryColor;
              border-color: @primaryColor;
            }
          }
        }
      }
    }

    .pagination {
      display: flex;
      justify-content: center;
      margin-top: 12px;
    }
  }
</style>
<style>
  .ant-advanced-search-form .ant-form-item {
    display: flex;
  }

  .ant-advanced-search-form .ant-form-item-control-wrapper {
    flex: 1;
  }

  #components-form-demo-advanced-search .ant-form {
    max-width: none;
  }
</style>
