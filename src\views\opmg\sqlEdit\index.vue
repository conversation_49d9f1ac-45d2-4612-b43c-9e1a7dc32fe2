<template>
    <div id="nav" style="height:100%">
      <a-tabs default-active-key="Home" @change="callback" class="top-tabs">
        <a-tab-pane key="Home" tab="SQL编辑器" class="top-tab-pane">
          <home v-if="hackReset" ref="Home"></home>
        </a-tab-pane>
        <a-tab-pane key="Setting" tab="设置" class="top-tab-pane">
          <setting @updateSetting="upSetting"></setting>
        </a-tab-pane>
      </a-tabs>
    </div>
</template>
  <script>
import Home from './Home.vue'
import Setting from './Setting.vue'
export default {
  name: 'index',
  components: {
    Home,
    Setting,
  },
  data() {
    return {
      hackReset: true,
    }
  },
  methods: {
    /**保存设置后重新加载Home */
    upSetting() {
      this.hackReset = false
      this.$nextTick(() => {
        this.hackReset = true
      })
    },
    callback(key) {},
  },
}
</script>
  <style lang="less" scoped>
.top-tabs {
  height: 100%;
  ::v-deep &>.ant-tabs-content{
    height: 100%;
    .top-tab-pane{
      height:  calc(100% - 44px - 20px);
      overflow: hidden;
    }
  }
}
</style>
