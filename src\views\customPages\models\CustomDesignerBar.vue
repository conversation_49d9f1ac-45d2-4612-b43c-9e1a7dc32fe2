<template>
  <a-tabs :activeKey="activeKey" @change="tabChange">
    <a-tab-pane key="grid" tab="栅格">
      <div class='menu-item' @click='addRow'>
        <a-icon type="table" />
        <span style='margin-left: 8px;'>添加栅格</span>
        <div style='flex:1'></div>
        <a-icon type="plus" />
      </div>
    </a-tab-pane>
    <a-tab-pane key="attr" tab="属性" v-if='blockId'>
      <div style="margin-bottom: 16px">
        <div>列间距</div>
        <a-input type='number' min='0' v-model='selectRow.gutter' >
        </a-input>
      </div>
      <div style="margin-bottom: 16px">
        <div>行间距</div>
        <a-input type='number' min='0' v-model='selectRow.gutterBottom' >
        </a-input>
      </div>
      <div style="margin-bottom: 16px">
        <div>高度</div>
        <a-input type='number' min='0' v-model='selectRow.height' >
        </a-input>
      </div>
      <div v-if='selectRow'>
        <div style="margin-bottom: 16px" v-for='(item,idx) in selectRow.cols' :key='item.id'>
          <div>第{{idx+1}}列占位格数</div>
          <a-input type='number'  v-model='item.lg' @change='spanChange($event,item)'>
            <a-icon slot="addonAfter" type="close" @click.native='delCol(item.id)' />
          </a-input>
        </div>
        <div style='margin-bottom: 12px' v-if='selectRow.cols && selectRow.cols.length>0'>
          <a-icon type="info-circle" theme="twoTone" />
          <a style='margin-left: 5px'>所有列栅格数不能大于24</a>
        </div>
      </div>
      <div>
        <a-button @click='delRow' type="danger" style='margin-right: 16px'>删除行</a-button>
        <a-button type='primary' @click='addCol' v-if='spanMax > 0'>添加列</a-button>
      </div>

    </a-tab-pane>
  </a-tabs>
</template>

<script>
import { uuidX6 } from '@/utils/util'

export default {
  name: 'CustomDesignerBar',
  props: {
    blockId:{
      type:String,
      default:"",
      require: true,
    },
    selectRow:{
      type:Object,
      default:()=>null,
      require:true,
    },
  },
  data() {
    return {
      activeKey:"grid"
    }
  },
  created() {

  },
  mounted() {
  },
  computed:{
    spanRemaining(){
      if(this.selectRow.cols && this.selectRow.cols.length>1){
        let sum = this.selectRow.cols.slice(0,-1).reduce((pre,cur)=>{
          return pre + cur.lg
        },0)
        return 24 - sum
      }
      return 24
    },
    spanMax(){
      if(this.selectRow){
        let sum = this.selectRow.cols.reduce((pre,cur)=>{
          return pre + cur.lg
        },0)
        return 24 - sum
      }
      else{
        return 0;
      }
    }
  },
  methods:{
    addRow(){
      let rowObj =  {
        id:"row-"+uuidX6(),
        type:"row",
        justify:"start",
        gutter:16,
        gutterBottom:16,
        height:430,
        cols:[]
      }
      this.$emit("addRow",rowObj)
    },
    delRow(){
      this.$confirm({
        content: '要删除这个栅格吗？',
        onOk:()=>{
          this.$emit("delRow")
        },
        cancelText: '取消',
        okText: '删除',
      });
    },
    addCol(){
      let colObj =  {
        id:"col-"+uuidX6(),
        rowId:this.blockId,
        type:"col",
        xxl:this.spanMax,
        xl:this.spanMax,
        lg:this.spanMax,
        md:this.spanMax,
        sm:this.spanMax,
        xs:this.spanMax,
        offset:0,
        order:0,
        pull:0,
        push:0,
        children:[],
        componentPath:"",
        componentName:"",
      }
      this.$emit("addCol",colObj)
    },
    delCol(colId){
      this.$emit("delCol",colId)
    },
    tabChange(key) {
      if(key === "grid"){
        this.$emit("setBlockId","")
      }
    },
    spanChange(e,item){
      let v = e.target.value
      if(v<=0){
        item.lg = 1;
      }else if(v > this.spanRemaining){
        item.lg = this.spanRemaining;
      }
      item.lg = Number(item.lg)
      item.xxl = item.lg;
      item.xl = item.lg;
      item.md = item.lg;
    },
    menuClick(){

    },
  }
}
</script>


<style scoped lang='less'>
.menu-item{
  display: flex;
  align-items: center;
  padding: 10px;
  border-radius: 8px;
  color: rgba(0, 0, 0, 0.88);
  cursor: pointer;
}
.menu-item:hover{
  color: rgba(0, 0, 0, 0.88);
  background-color: rgba(0, 0, 0, 0.06)
}
</style>