<template>
  <div :class='[showOpmgKInfo?"opmg-know-comment":"one-click-help-comment"]'>
    <div class='add-comment' v-if='!approvalView'>
      <div class='like-unlike-container'>
         以上内容对你有帮助吗？
        <span class='like-unlike-wrapper'>
          <span class='like-unlike' @click='changeLike'>
            <a-icon type='like'
                  :theme='curLikeStatus===1?"filled":"outlined"'
                  :class='{"active-like":curLikeStatus===1}'
                  class='like-unlike-icon'/>
          </span>
          {{curLikeNum}}
        </span>
        <span class='like-unlike-wrapper'>
          <span class='like-unlike' @click='changeUnlike'>
          <a-icon type='dislike'
                  :theme='curUnlikeStatus===1?"filled":"outlined"'
                  :class='{"active-unlike":curUnlikeStatus===1}'
                  class='like-unlike-icon'/>
          </span>
          {{curUnlikeNum}}
        </span>
      </div>
      <div class='table-operator table-operator-style' v-if='canComment===true' style='margin-bottom: 0px'>
        <a-button v-if='showOpmgKInfo' @click='handleAdd' class="action-btn">评论</a-button>
        <a-button v-else @click='handleAdd' icon='message' class="action-btn">评论</a-button>
      </div>
    </div >
    <div class='comment-icon' v-else>
      <span>知识创建者设置是否允许评论：</span>
      <yq-icon :key='allowCommentKey' v-if='canComment===true' :type='"allowComment"' style='font-size: 30px' title='允许评论'></yq-icon>
      <yq-icon :key='notAllowCommentKey' v-else :type='"notAllowComment"' style='font-size: 30px' title='不允许评论'></yq-icon>
    </div>
    <!--    运维中心--知识详情--评论-->
    <a-table
      v-if='showOpmgKInfo'
      ref='table'
      bordered
      :row-key='(record, index) => {return record.id}'
      :columns='columns'
      :dataSource='dataSource'
      :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
      :pagination='ipagination'
      :loading='loading'
      @change='handleTableChange'>
      <span slot='action' class='caozuo' slot-scope='text, record'  >
<!--        审批查看、或者不是知识的创建者也不是评论的创建者都不能删除评论-->
<!--        <a-popconfirm :disabled='approvalView||(sysUserId!==record.userId&&createKnowledgeUserId!==sysUserId)'  title="确定删除吗?" @confirm="() => handleDelete(record.id)">
          <a :class='{"disabled-delete":approvalView||(sysUserId!==record.userId&&createKnowledgeUserId!==sysUserId)}'>删除</a>
        </a-popconfirm>-->
        <!--        审批查看、或者不是评论的创建者不能删除评论-->
         <a-popconfirm :disabled='approvalView||sysUserId!==record.userId'  title="确定删除吗?" @confirm="() => handleDelete(record.id)">
          <a :class='{"disabled-delete":approvalView||sysUserId!==record.userId}'>删除</a>
        </a-popconfirm>
      </span>

      <template slot='tooltip' slot-scope='text'>
        <a-tooltip placement='topLeft' :title='text' trigger='hover'>
          <div class='tooltip'>
            {{ text }}
          </div>
        </a-tooltip>
      </template>
    </a-table>
<!--    运维助手--知识详情--评论-->
    <a-table
      v-else
      ref='table'
      bordered
      :row-key='(record, index) => { return record.id }'
      :columns='columns'
      :dataSource='dataSource'
      :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
      :pagination='ipagination'
      :loading='loading'
      @change='handleTableChange'
      :rowClassName="newStyle"
      :locale='locale'>
      <span slot='action' class='caozuo' slot-scope='text, record'
            v-if='sysUserId == record.userId || createKnowledgeUserId == sysUserId'>
        <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)"  overlayClassName='oneClickHelpPopconfirm'>
          <a-icon type="delete" style="margin-right: 10px; cursor: pointer;" />
          <span style="cursor: pointer;">删除</span>
        </a-popconfirm>
      </span>

      <template slot='tooltip' slot-scope='text'>
        <a-tooltip placement='topLeft' :title='text' trigger='hover' overlayClassName='oneClickHelpTooltip'>
          <div class='tooltip'>
            {{ text }}
          </div>
        </a-tooltip>
      </template>
    </a-table>
    <add-opmg-comment v-if='showOpmgKInfo' ref='modalForm' @ok='modalFormOk' :knowledge-id='kInfo.id'></add-opmg-comment>
    <add-one-click-help-comment v-else ref='modalForm' @ok='modalFormOk' :knowledge-id='kInfo.id'></add-one-click-help-comment>
  </div>
</template>
<script>
import {JeecgListMixin} from '@/mixins/JeecgListMixin'
import {postParamsAction,putParamsAction, getAction } from '@api/manage'
import addOpmgComment from '@views/opmg/knowledgeManagement/knowledgeBase/modules/AddCommentModal.vue'
import addOneClickHelpComment from '@views/oneClickHelp/knowledgeBase/modules/AddCommentModal.vue'
import YqIcon from '@comp/tools/SvgIcon'
import Empty from '@/components/oneClickHelp/Empty.vue'
export default {
  name: "KnowledgeComment",
  components:{ YqIcon, addOpmgComment,addOneClickHelpComment},
  mixins: [JeecgListMixin],
  props:{
    kInfo:{
      type:Object,
      required:true
    },
    likeStatus:{
      type:Number,
      required:true,
      default:-1
    },
    unlikeStatus:{
      type:Number,
      required:true,
      default:-1
    },
    /**若是通过知识审批列表打卡查看，
     收藏、分享、打印、评论、关联、点赞、点踩都不可操作性，
     附件统统可以下载，同时告诉管理员，知识创建者设置的允许下载附件状态*/
    approvalView:{
      type:Boolean,
      required:false,
      default:false
    },
    /*用于区分运维中心和运维助手知识详情页面，采用不同的样式,默认采用运维中心知识详情样式*/
    showOpmgKInfo: {
      type: Boolean,
      required: false,
      default: true
    }
  },
  data() {
    return {
      sysUserId:this.$store.getters.userInfo.id,
      createKnowledgeUserId:'',
      curLikeStatus:-1,
      curLikeNum:0,
      curUnlikeStatus:-1,
      curUnlikeNum:0,
      canComment:false,
      disableMixinCreated:true,
      notAllowCommentKey:0,
      allowCommentKey:0,
      locale: {
        emptyText: <Empty/>
      },
      columns: [
        {
          title: '评论时间',
          dataIndex: 'createTime',
          customCell: () => {
            let cellStyle = 'text-align: center;width:180px'
            return {
              style: cellStyle
            }
          },
        },
        {
          title: '评论内容',
          dataIndex: 'comment',
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'tooltip'
          }
        },
        {
          title: '评论人',
          dataIndex: 'userName',
          customCell: () => {
            let cellStyle = 'text-align: center;width:200px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '操作',
          dataIndex: 'action',
          customCell: () => {
            let cellStyle = 'text-align: center;width:100px'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'action'
          }
        }
      ],
      url: {
        list: '/kbase/knowledges/list/comment',//评论列表
        delete:'/kbase/knowledges/comment',

        like: '/kbase/knowledges/like',//选中点赞调用
        cancelLike: '/kbase/knowledges/cancelLike',//取消选中点赞调用

        unlike: '/kbase/knowledges/unlike',//选中点踩调用
        cancelUnlike: '/kbase/knowledges/cancelUnlike',//取消选中点踩调用

        extendsInfo: '/kbase/knowledges/extendsInfo',//统计点赞、点踩数目
      }
    }
  },
  watch:{
    kInfo:{
      handler(val){
        this.curLikeNum=0
        this.curUnlikeNum=0
        this.canComment=false
        this.dataSource=[]
        this.queryParam={}
        this.ipagination.current=1
        this.ipagination.pageSize=10
        if(Object.keys(val).length>0){
          this.getLikeAndUnlikeNum(val.id)
          this.createKnowledgeUserId=val.createByUserId
          let config=val.otherConfig
          if(config&&config.length>0){
            this.canComment=config.indexOf('allowComment')>-1?true:false
          }
          this.allowCommentKey++
          this.notAllowCommentKey++
          this.queryParam.knowledgeId=val.id
          this.loadData(1)
        }
      },
      deep:true,
      immediate:true
    },
    likeStatus:{
      handler(val){
       this.curLikeStatus=val
      },
      deep:true,
      immediate:true
    },
    unlikeStatus:{
      handler(val){
        this.curUnlikeStatus=val
      },
      deep:true,
      immediate:true
    }
  },
  methods:{
    getLikeAndUnlikeNum(id){
      let that=this
      getAction(that.url.extendsInfo,{knowledgeId:id}).then((res)=>{
        if(res.success){
          that.curLikeNum=res.result.like
          that.curUnlikeNum=res.result.unlike
        }else {
          that.$message.warning(res.message)
        }
      }).catch((err)=>{
        that.$message.warning(err.message)
      })
    },
    changeLike(){
      switch (this.curLikeStatus){
        case 0:
          this.selectLike()
          break;
        default:
          this.cancleLike()
          break
      }
    },
    selectLike(){
      postParamsAction(this.url.like,{knowledgeId:this.kInfo.id}).then((res)=>{
        if(res.success){
          this.curLikeStatus=1
          this.curUnlikeStatus=0
          this.curLikeNum=res.result.like
          this.curUnlikeNum=res.result.unlike
          this.$message.success('点赞成功')
        }else {
          this.$message.warning(res.message)
        }
      }).catch((err)=>{
        this.$message.warning(err.message)
      })
    },
    cancleLike(){
      putParamsAction(this.url.cancelLike,{knowledgeId:this.kInfo.id}).then((res)=>{
        if(res.success){
          this.curLikeStatus=0
          this.curLikeNum=res.result.like
          this.curUnlikeNum=res.result.unlike
          this.$message.success('取消点赞成功')
        }else {
          this.$message.warning(res.message)
        }
      }).catch((err)=>{
        this.$message.warning(err.message)
      })
    },

    changeUnlike(){
      switch (this.curUnlikeStatus){
        case 0:
          this.selectUnlike()
          break;
        default:
          this.cancleUnlike()
          break
      }
    },
    selectUnlike(){
      postParamsAction(this.url.unlike,{knowledgeId:this.kInfo.id}).then((res)=>{
        if(res.success){
          this.curLikeStatus=0
          this.curUnlikeStatus=1
          this.curLikeNum=res.result.like
          this.curUnlikeNum=res.result.unlike
          this.$message.success('点踩成功')
        }else {
          this.$message.warning(res.message)
        }
      }).catch((err)=>{
        this.$message.warning(err.message)
      })
    },
    cancleUnlike(){
      putParamsAction(this.url.cancelUnlike,{knowledgeId:this.kInfo.id}).then((res)=>{
        if(res.success){
          this.curUnlikeStatus=0
          this.curLikeNum=res.result.like
          this.curUnlikeNum=res.result.unlike
          this.$message.success('取消点踩成功')
        }else {
          this.$message.warning(res.message)
        }
      }).catch((err)=>{
        this.$message.warning(err.message)
      })
    },

    newStyle() {
      return 'newRowStyle' // 返回到行样式
    }
  }
}
</script>

<style scoped lang="less">
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
.opmg-know-comment{
  .add-comment{
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-flow: row wrap;
    margin-bottom: 12px;
  }
  .comment-icon{
    display: flex;
    justify-content: start;
    align-items: center;
    flex-flow: row wrap;
    margin-bottom: 16px;
  }
  .like-unlike-wrapper{
    margin: 0px 20px;

    .like-unlike {
      display: inline-block;
      white-space: nowrap;
      width: 30px;
      height: 30px;
      line-height: 30px;
      border-radius: 50%;
      border: 1px solid #e8e8e8;
      text-align: center;


      .like-unlike-icon {
        font-size: 18px;
      }
    }
    .like-unlike:hover{
      cursor: pointer;
      border: 1px solid #409eff;
    }
    .active-like,.active-unlike{
      color:#409eff
    }
  }

  .table-operator{
    margin-bottom: -16px;
    .action-btn {
      margin: 0px;
    }
  }
  .caozuo{
    .disabled-delete{
      color: rgba(0, 0, 0, 0.25) !important;
      cursor: default;
    }
  }
}
.one-click-help-comment{
  .add-comment {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-flow: row wrap;
    margin-bottom: 12px;
    color: #fff;
    font-size: 14px;
  }
  .like-unlike-container{
    display: flex;
    align-items: center;
    .small-line{
      width: 1px;
      height: 15px;
      background-color: #fff;
      margin: 0 6px 0 10px;
    }
    .like-unlike-wrapper {

      .like-unlike {
        display: inline-block;
        white-space: nowrap;
        width: 30px;
        height: 30px;
        line-height: 30px;
        text-align: center;


        .like-unlike-icon {
          font-size: 18px;
        }
      }

      .active-like,
      .active-unlike {
        color: #409eff
      }
    }
  }
  .table-operator{
    margin-bottom: -16px;
    .action-btn {
      margin: 0px;
      background: rgba(64, 158, 255, 0.25) !important;
      border: 1px solid #409EFF;
      color: #FFFFFF;
    }
  }

  .caozuo {
    color: #D93D3D !important;
  }

  /deep/ .newRowStyle {
    height: 54px;
    border: 1px solid rgba(255,255,255,0.47) !important;
  }
  /*********table*********/
  /deep/ .ant-table-bordered.ant-table-empty .ant-table-placeholder {
    border: 1px solid rgba(255,255,255,0.24) !important;
  }
  /deep/ .ant-empty-description {
    color: rgba(255,255,255,0.85) !important;
  }
  /deep/ .ant-table-bordered .ant-table-thead > tr > th, /deep/ .ant-table-bordered .ant-table-tbody > tr > td{
    border: 1px solid rgba(255,255,255,0.47) !important;
  }
}
</style>