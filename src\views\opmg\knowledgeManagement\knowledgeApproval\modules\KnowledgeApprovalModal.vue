<template>
  <j-modal
    :title='title'
    :width='width'
    :centered='true'
    :visible='visible'
    :destroyOnClose='true'
    switchFullscreen
    cancelText='关闭'
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @ok='handleOk'
    @cancel='handleCancel'
  >
    <a-spin :spinning='confirmLoading'>
      <j-form-container :disabled='disableSubmit'>
        <a-form-model ref='form' :model='model' :rules='validatorRules' slot='detail' v-bind='formItemLayout'>
          <a-row>
            <a-col :span='24'>
              <a-form-model-item label='审批结果' prop='result'>
                <a-radio-group v-model='model.result' :options='radioOption' />
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
              <a-form-model-item label='备注' prop='comment'>
                <a-textarea style='width: 100%' v-model='model.comment' :autoSize='{minRows:4,maxRows:8}'
                            :allow-clear='true' autocomplete='off' placeholder='请输入备注内容' />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </j-form-container>
    </a-spin>
  </j-modal>
</template>
<script>
import { getAction, httpAction } from '@api/manage'
import wordUpload from '@views/opmg/knowledgeManagement/knowledgeBase/modules/WordUpload.vue'
import { ValidateOptionalFields } from '@/utils/rules'

export default {
  name: 'KnowledgeApprovalModal',
  components: { wordUpload },
  props: {
    //判断知识是否来自流程
    addFromFlowable:{
      type:Boolean,
      required:false,
      default:false
    }
  },
  data() {
    return {
      title: '新增',
      width: '600px',
      disableSubmit: false,
      visible: false,
      confirmLoading: false,
      sysUserId:this.$store.getters.userInfo.id,
      formItemLayout: {
        labelCol: {
          xs:{span:24 },
          sm:{span:24},
          md:{span:5},
          lg:{span:5}
        },
        wrapperCol: {
          xs:{span:24 },
          sm:{span:24},
          md:{span:18},
          lg:{span:16}
        }
      },
      radioOption: [
        { label: '同意', value: 'true',key:'agree', checked: true },
        { label: '不同意', value: 'false',key:'disagree' , checked: false}],
      model: {
        result: 'true'
      },
      validatorRules: {
        result: [
          { required: true, message: '请选择审批结果' }
        ],
        comment: [
          { required: false, validator: (rule, value, callback) =>ValidateOptionalFields(rule, value, callback,'备注',200)}
        ]
      },
      url: {
        add: '/kbase/knowledges/review'
      }
    }
  },
  methods: {
    approval(record) {
      this.edit({
        result: 'true',
        knowledgeId:record.id
      })
    },
    /*编辑知识*/
    edit(record) {
      this.visible = true
        this.$nextTick(() => {
          this.model =JSON.parse(JSON.stringify(record))
        })
    },
    /*提交表单数据*/
    handleOk() {
      let that = this
      that.$refs.form.validate((err, values) => {
        if (err&&!that.confirmLoading) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!that.model.id) {
              httpurl +=that.url.add
              method = 'post'
          }
          let formData = JSON.parse(JSON.stringify(that.model))
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
                that.close()
              } else {
                that.$message.warning(res.message)
              }
              that.confirmLoading = false
            }).catch((err) => {
            that.$message.warning(err.message)
            that.confirmLoading = false
          })
        }
      })
    },
    handleCancel() {
      this.close()
    },
    close() {
      this.visible = false
    },
  }
}
</script>
<style scoped lang='less'>
@import '~@assets/less/normalModal.less';
</style>