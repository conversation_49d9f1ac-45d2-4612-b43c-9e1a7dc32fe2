/*
1、弹窗可切换全屏模式和非全屏模式
2、弹窗宽度不固定，随着浏览器窗口变化而变化
3、全屏模式下：垂直滚动条在白色弹窗内（弹窗高度= 设备可视窗口的高度）
4、非全屏模式下:弹窗最大高度=浏览器可视窗口高度，垂直滚动条出现在白色弹窗内，需令其属性wrapClassName=‘limit-height-modal’
*/
.j-modal-box{
  ::v-deep .limit-height-modal{
   .ant-modal {
      margin: 0 !important;
      padding: 18px 18px !important;
      max-width: calc(100vw - 12px) !important;
      width: auto;

      .ant-modal-body {
        padding: 24px !important;
        height: calc(100vh - 55px - 55px - 48px) !important;
        max-height: calc(100vh - 55px - 55px - 48px);
        overflow-y:auto
      }
    }
  }
}

.j-modal-box.fullscreen {
  ::v-deep .limit-height-modal{
    overflow: hidden !important;
    .ant-modal {
      width: 100vw !important;
      max-width: 100vw !important;
      margin: 0;
      padding: 0px !important;

       .ant-modal-body {
        padding: 24px !important;
        height: calc(100vh - 55px - 55px) !important;
         max-height: calc(100vh - 55px - 55px) !important;
        overflow-y:auto
      }
    }
  }
}

::v-deep .two-words > div > label {
  letter-spacing: 4px;
}
::v-deep .two-words > div > label::after {
  letter-spacing: 0px;
}