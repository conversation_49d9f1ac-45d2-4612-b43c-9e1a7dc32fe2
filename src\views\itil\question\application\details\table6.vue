<template>
  <div style="overflow: hidden; overflow-x: auto">
    <table class="gridtable">
      <tr>
        <td class="leftTd">结 果</td>
        <td class="rightTd">
          <span>
            <div v-if="data.handleResult == 1">成功解决</div>
            <div v-if="data.handleResult == 2">未成功解决</div>
            <div v-if="data.handleResult == 3">未复现</div>
          </span>
        </td>
      </tr>
      <tr>
        <td class="leftTd">标 题</td>
        <td class="rightTd">{{ data.title }}</td>
      </tr>
      <tr>
        <td class="leftTd">描 述</td>
        <td class="rightTd">{{ data.describe }}</td>
      </tr>
    </table>
  </div>
</template>
<script>
import { getAction } from '@/api/manage'
export default {
  // 处理结果
  name: 'table6',
  data() {
    return {
      data: {},
    }
  },
  methods: {
    getData(id) {
      // this.visible = true
      getAction('/actTask/getProcessResults/', { id: id }).then((res) => {
        if (res.success) {
          this.data = res.result
        }
      })
    },
  },
}
</script>
<style scoped>
table.gridtable {
  white-space: nowrap;
  font-family: verdana, arial, sans-serif;
  font-size: 11px;
  color: #606266;
  border-width: 1px;
  border-color: #e8e8e8;
  border-collapse: collapse;
  text-align: left;
  width: 100%;
}
table.gridtable td {
  border-width: 1px;
  border-style: solid;
  border-color: #e8e8e8;
}
.leftTd {
  width: 17%;
  background-color: #fafafa;
  padding: 16px 24px;
  text-align: center;
}
.rightTd {
  width: 35%;
  padding: 16px 24px;
}
</style>
