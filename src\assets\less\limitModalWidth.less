/*
1、弹窗无全屏模式
2、弹窗宽度固定，当弹窗宽度大于浏览器可视窗口宽度时，出现水平滚动条
*/
::v-deep .ant-modal-wrap {
  white-space: nowrap;
}
::v-deep .ant-modal-body {
  padding: 24px 24px;
  //max-height: calc(100vh - 55px - 55px - 48px);
  //overflow-y:auto
}
@media (min-width: 480px){
  ::v-deep .ant-modal {
    //top: 0px;
   // margin: 0 !important;
    padding: 24px;
  }
}
@media (max-width: 767px){
  ::v-deep .ant-modal {
    //top: 0px;
    max-width: none !important;
    //margin: 0 !important;
    padding: 24px;
  }
}