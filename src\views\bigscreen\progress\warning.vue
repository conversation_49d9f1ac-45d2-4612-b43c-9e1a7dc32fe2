<template>
  <div class="body_box">
    <div class="header">
      <div class="header-right">
        <div class="header-right-time">
          <span>选择日期:</span>
          <div class="time-range-span">
            <a-range-picker dropdownClassName="big-screen-range-picker" v-model="this.todayTime" size="small"
              @change="onChange" />
          </div>
        </div>
        <button class="left-top-top-right-time-search" @click="timeChange">
          <a> 查询 </a>
        </button>
        <button class="export" @click="exportPDF">
          <a> 导出 </a>
        </button>
      </div>
    </div>
    <div class="body">
      <div class="left">
        <div class="left-bottom">
          <div class="info-topTitle">
            <div class="left-topTitle">
              <img src="../../../assets/bigScreen/9.png" alt="" />
              <span>告警统计信息</span>
            </div>
          </div>
          <div class="left-bottom-core">
            <div class="left-bottom-core-table">
              <div class="left-bottom-core-tableTitle">
                <span>设备名称</span>
                <span>产品名称</span>
                <span>告警名称</span>
                <span>告警级别</span>
                <span>告警重复次数</span>
                <span>责任人</span>
              </div>
              <div class="left-bottom-core-table-table">
                <vue-seamless-scroll :data="maintenanceData" :class-option="warning"
                  class="left-bottom-core-table-table-seamless-warp">
                  <ul>
                    <li v-for="(item, index) in maintenanceData" :key="index">
                      <span> {{ item.name }} </span>
                      <span> {{ item.categoryName }} </span>
                      <span> {{ item.alarmName }} </span>
                      <span :style="{color:item.color}"> {{ item.alarmLevelName }} </span>
                      <span> {{ item.alarmRepeat }} </span>
                      <span v-if="item.conform_dictText"> {{ item.conform_dictText }} </span>
                      <span v-else>--</span>
                    </li>
                  </ul>
                </vue-seamless-scroll>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="core">
        <div class="core-top">
          <div class="topTitle">
            <img src="../../../assets/bigScreen/9.png" alt="" />
            <span>告警数量设备TOP10</span>
          </div>
          <div class="core-top-body" ref="alarmNumber" id="alarmNumber"></div>
        </div>
        <div class="core-bottom">
          <div class="topTitle">
            <img src="../../../assets/bigScreen/9.png" alt="" />
            <span>告警规则触发TOP10</span>
          </div>
          <div class="core-bottom-body" ref="alarmRules" id="alarmRules"></div>
        </div>
      </div>
      <div class="right">
        <div class="right-core">
          <div class="topTitle">
            <img src="../../../assets/bigScreen/9.png" alt="" />
            <span>告警重复次数设备TOP10</span>
          </div>
          <div class="right-core-body" ref="alarmRepeatTimes" id="alarmRepeatTimes"></div>
        </div>
        <div class="right-bottom">
          <div class="topTitle">
            <img src="../../../assets/bigScreen/9.png" alt="" />
            <span>告警发生时间</span>
          </div>
          <div class="right-bottom-body">
            <div class="right-bottom-body-left" ref="alarmOccurTime" id="alarmOccurTime"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  import vueSeamlessScroll from 'vue-seamless-scroll'
  import {
    getAction
  } from '@/api/manage'
  import echarts from 'echarts'
  export default {
    data() {
      return {
        warningData: [],
        resourcesData: [],
        alarmNumList: [],
        alarmRulesList: [],
        alarmRepeatTimesList: [],
        alarmOccurTimeList: [],
        workOrderData: [],
        todayTime: [],
        workOrderDataOne: {},
        workOrderDataTwo: {},
        workOrderDataThree: {},
        url: {
          alarmCount: '/data-analysis/alarm/count/list',
          alarmCountExport: '/data-analysis/alarm/count/export', //导出
          alarmNumber: '/data-analysis/alarm/template/top10', //告警数量
          alarmRules: '/data-analysis/alarm/template/top', //告警规则
          alarmRepeatTimes: '/data-analysis/alarm/repeat/top', //告警重复次数
          alarmOccurTime: '/data-analysis/alarm/day/count', //告警发生时间
          // orderCount: '/data-analysis/order/count',
          // cmdbCount: '/data-analysis/cmdb/count',
        },
        theServerData: [],
        percent: 0,
        maintenanceData: [],
        time1: '',
        time2: '',
      }
    },
    components: {
      vueSeamlessScroll,
    },
    created() {
      let moment = require('moment');
      let today = moment().format('YYYY-MM-DD')
      this.time1 = today
      this.time2 = today
      this.todayTime[0] = this.time1
      this.todayTime[1] = this.time2
    },
    mounted() {
      //告警统计数据
      this.alarmList()
      //告警数量资源topo10
      this.alarmNumTop10()
      //告警发生时间
      this.alarmOccurTime()
      //告警重复次数资源topo10
      this.alarmRepeatTimesTop10()
      //告警规则触发topo10
      this.alarmRulesTop10()
    },
    computed: {
      warning() {
        return {
          step: 0.15, // 数值越大速度滚动越快
          limitMoveNum: 15, // 开始无缝滚动的数据量 this.dataList.length
          hoverStop: false, // 是否开启鼠标悬停stop
          direction: 1, // 0向下 1向上 2向左 3向右
          // openWatch: true, // 开启数据实时监控刷新dom
          singleHeight: 100, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
          // singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
          waitTime: 2, // 单步运动停止的时间(默认值1000ms)
        }
      },
    },
    methods: {
      onChange(dates, dateStrings) {
        this.todayTime = dateStrings
        this.time1 = dateStrings[0]
        this.time2 = dateStrings[1]
      },
      timeChange() {
        //告警统计数据
        this.alarmList()
        //告警数量资源topo10
        this.alarmNumTop10()
        //告警发生时间
        this.alarmOccurTime()
        //告警重复次数资源topo10
        this.alarmRepeatTimesTop10()
        //告警规则触发topo10
        this.alarmRulesTop10()
      },
      // 告警统计数据
      alarmList() {
        getAction(this.url.alarmCount, {
          time1: this.time1,
          time2: this.time2,
          pageNo: 1,
          pageSize: 15
        }).then((res) => {
          if (res.code == 200) {
            this.maintenanceData = res.result.records
          }
        })
      },

      exportPDF() {
        getAction(this.url.alarmCountExport, {
          time1: this.time1,
          time2: this.time2
        }).then((res) => {
          if (res.code == 200) {
            // window.location.href = `${window._CONFIG['downloadUrl']}/${res.result}`
            window.open(window._CONFIG['domianURL'] + '/sys/common/downloadFile/' + res.result)
            // window.open(window._CONFIG['downloadUrl'] + '/' + res.result)
          } else {
            this.$message.error(res.message)
          }
        })
      },

      // 导出
      exportXlsx() {
        window.location.href =
          `${window._CONFIG['domianURL']}/${this.url.alarmCountExport}?time1=${this.time1}&time2=${this.time2}`
      },

      // 设备总览数据
      overview() {
        getAction(this.url.overview).then((res) => {
          if (res.code == 200) {
            this.resourcesData = res.result
          }
        })
      },

      // 资源告警量数据
      alarmTop() {
        getAction(this.url.alarmTop).then((res) => {
          if (res.code == 200) {
            this.alarmNumList = res.result
            // this.alarmNumListHistogram()
          }
        })
      },

      //告警数量资源topo10,请求后端接口
      alarmNumTop10() {
        getAction(this.url.alarmNumber, {
          time1: this.time1,
          time2: this.time2
        }).then((res) => {
          if (res.code == 200) {
            this.alarmNumList = res.result || []
            this.alarmNumber(this.alarmNumList)
          }
        })
      },

      //告警数量资源topo10
      alarmNumber(list) {
        let arr = []
        let brr = []
        list.forEach((e) => {
          arr.push(e.name)
          brr.push(e.value)
        })

        let myChart = this.$echarts.init(this.$refs.alarmNumber)
        myChart.setOption({
          xAxis: {
            type: 'value',
            splitLine: {
              show: false
            }, //去除网格线
            show: false,
          },
          yAxis: [{
              type: 'category',
              data: arr,
              splitLine: {
                show: false
              }, //去除网格线
              axisTick: {
                show: false,
              },
              axisLine: {
                show: false, //y轴线消失
                lineStyle: {
                  //y轴字体颜色
                  color: '#f6f6f6',
                },
              },
              axisLabel: {
                show: false,
              },
            },
            {
              type: 'category',
              data: brr,
              splitLine: {
                show: false
              }, //去除网格线
              axisTick: {
                show: false,
              },
              axisLine: {
                show: false, //y轴线消失
                lineStyle: {
                  //y轴字体颜色
                  color: '#f6f6f6',
                },
              },
              axisLabel: {
                show: true,
              },
            },
          ],
          grid: {
            top: 0,
            left: 20, // 调整这个属性
            right: 50,
            bottom: 0,
          },
          series: [{
            data: brr,
            type: 'bar',
            showBackground: true,
            backgroundStyle: {
              color: 'rgba(180, 180, 180, 0.2)', //柱状图背景颜色
            },
            barWidth: 10, //柱图宽度
            itemStyle: {
              emphasis: {
                barBorderRadius: 30,
              },
              normal: {
                barBorderRadius: [10, 10, 10, 10],
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
                    offset: 0,
                    color: '#3679fb',
                  },
                  {
                    offset: 1,
                    color: '#0cf6f7',
                  },
                ]),
                label: {
                  show: true,
                  position: 'right',
                  textStyle: {
                    color: '#00a1e7',
                    fontSize: 16,
                  },
                },
              },
            },
            label: {
              normal: {
                color: '#fff',
                show: true,
                position: [0, '-20px'],
                textStyle: {
                  fontSize: 16,
                },
                formatter: function (a, b) {
                  return a.name
                },
              },
            },
            backgroundStyle: {
              color: 'rgba(255,255,255,0)',
            },
          }, ],
        })
        window.addEventListener("resize", () => {
          myChart.resize();
        });
      },

      //告警规则触发topo10,请求后端接口
      alarmRulesTop10() {
        getAction(this.url.alarmRules, {
          time1: this.time1,
          time2: this.time2
        }).then((res) => {
          if (res.code == 200) {
            this.alarmRulesList = res.result || []
            this.alarmRules(this.alarmRulesList)
          }
        })
      },

      //告警规则触发topo10
      alarmRules(list) {
        let arr = []
        let brr = []
        list.forEach((e) => {
          arr.push(e.name)
          brr.push(e.value)
        })
        arr.reverse()
        brr.reverse()

        let myChart = this.$echarts.init(this.$refs.alarmRules)
        myChart.setOption({
          xAxis: {
            type: 'value',
            splitLine: {
              show: false
            }, //去除网格线
            show: false,
          },
          yAxis: [{
              type: 'category',
              data: arr,
              splitLine: {
                show: false
              }, //去除网格线
              axisTick: {
                show: false,
              },
              axisLine: {
                show: false, //y轴线消失
                lineStyle: {
                  //y轴字体颜色
                  color: '#f6f6f6',
                },
              },
              axisLabel: {
                show: false,
              },
            },
            {
              type: 'category',
              data: brr,
              splitLine: {
                show: false
              }, //去除网格线
              axisTick: {
                show: false,
              },
              axisLine: {
                show: false, //y轴线消失
                lineStyle: {
                  //y轴字体颜色
                  color: '#f6f6f6',
                },
              },
              axisLabel: {
                show: true,
              },
            },
          ],
          grid: {
            top: 10,
            left: 20, // 调整这个属性
            right: 40,
            bottom: 0,
          },
          series: [{
            data: brr,
            type: 'bar',
            showBackground: true,
            backgroundStyle: {
              color: 'rgba(180, 180, 180, 0.2)', //柱状图背景颜色
            },
            barWidth: 10, //柱图宽度
            itemStyle: {
              emphasis: {
                barBorderRadius: 30,
              },
              normal: {
                barBorderRadius: [10, 10, 10, 10],
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
                    offset: 0,
                    color: '#3679fb',
                  },
                  {
                    offset: 1,
                    color: '#0cf6f7',
                  },
                ]),
                label: {
                  show: true,
                  position: 'right',
                  textStyle: {
                    color: '#00a1e7',
                    fontSize: 16,
                  },
                },
              },
            },
            label: {
              normal: {
                color: '#fff',
                show: true,
                position: [0, '-20px'],
                textStyle: {
                  fontSize: 16,
                },
                formatter: function (a, b) {
                  return a.name
                },
              },
            },
            backgroundStyle: {
              color: 'rgba(255,255,255,0)',
            },
          }, ],
        })
        window.addEventListener("resize", () => {
          myChart.resize();
        });
      },

      orderCount() {
        getAction(this.url.orderCount).then((res) => {
          if (res.code == 200) {}
        })
      },

      // 资产统计柱状图数据
      cmdbCount() {
        getAction(this.url.cmdbCount).then((res) => {
          if (res.code == 200) {}
        })
      },

      formatter: function (num) {
        return num.toFixed(2) //小数点后几位，数字就是几小数点后几位
      },

      alarmRepeatTimesTop10() {
        getAction(this.url.alarmRepeatTimes, {
          time1: this.time1,
          time2: this.time2
        }).then((res) => {
          if (res.code == 200) {
            this.alarmRepeatTimesList = res.result || []
            this.alarmRepeatTimes(this.alarmRepeatTimesList)
          }
        })
      },

      // 告警重复次数资源topo10
      alarmRepeatTimes(list) {
        let arr = []
        let brr = []
        list.forEach((e) => {
          arr.push(e.name)
          brr.push(e.value)
        })
        arr.reverse()
        brr.reverse()

        // //当前视口宽度
        // let nowClientWidth = document.documentElement.clientWidth;
        // // 换算方法
        // let nowSize = function (val, initWidth = 1920) {
        //   return val * (nowClientWidth / initWidth);
        // };
        let myChart = this.$echarts.init(this.$refs.alarmRepeatTimes)
        myChart.setOption({
          xAxis: {
            type: 'value',
            splitLine: {
              show: false
            }, //去除网格线
            show: false,
          },
          yAxis: [{
              type: 'category',
              data: arr,
              splitLine: {
                show: false
              }, //去除网格线
              axisTick: {
                show: false,
              },
              axisLine: {
                show: false, //y轴线消失
                lineStyle: {
                  //y轴字体颜色
                  color: '#f6f6f6',
                },
              },
              axisLabel: {
                show: false,
              },
            },
            {
              type: 'category',
              data: brr,
              splitLine: {
                show: false
              }, //去除网格线
              axisTick: {
                show: false,
              },
              axisLine: {
                show: false, //y轴线消失
                lineStyle: {
                  //y轴字体颜色
                  color: '#f6f6f6',
                },
              },
              axisLabel: {
                show: true,
              },
            },
          ],
          grid: {
            top: 10,
            left: 20, // 调整这个属性
            right: 50,
            bottom: 0,
          },
          series: [{
            data: brr,
            type: 'bar',
            showBackground: true,
            backgroundStyle: {
              color: '', //柱状图背景颜色
            },
            barWidth: 10, //柱图宽度
            itemStyle: {
              emphasis: {
                barBorderRadius: 30,
              },
              normal: {
                barBorderRadius: [10, 10, 10, 10],
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
                    offset: 0,
                    color: '#3679fb',
                  },
                  {
                    offset: 1,
                    color: '#0cf6f7',
                  },
                ]),
                label: {
                  show: true,
                  position: 'right',
                  textStyle: {
                    color: '#00a1e7',
                    fontSize: 16,
                  },
                },
              },
            },
            label: {
              normal: {
                color: '#fff',
                show: true,
                position: [0, '-16px'],
                textStyle: {
                  fontSize: 16,
                },
                formatter: function (a, b) {
                  return a.name
                },
              },
            },
            backgroundStyle: {
              color: 'rgba(255,255,255,0)',
            },
          }, ],
        })
        window.addEventListener("resize", () => {
          myChart.resize();
        });
      },

      //告警发生时间折线图,调后台接口
      alarmOccurTime() {
        getAction(this.url.alarmOccurTime, {
          time1: this.time1,
          time2: this.time2
        }).then((res) => {
          if (res.code == 200) {
            this.alarmOccurTimeList = res.result || []
            this.alarmOccurTimeChart(this.alarmOccurTimeList)
          }
        })
      },

      //告警发生时间折线图
      alarmOccurTimeChart(list) {
        let xArr = []
        let yArr = []
        list.forEach((e) => {
          xArr.push(e.name)
          yArr.push(e.value)
        })
        let myChart = this.$echarts.init(this.$refs.alarmOccurTime)
        myChart.setOption({
          tooltip: {
            show: true,
            trigger: 'axis',
            transitionDuration: 0, //echart防止tooltip的抖动
          },
          xAxis: [{
            type: 'category',
            boundaryGap: false,
            data: xArr,
            axisLabel: {
              show: true,
              textStyle: {
                color: '#5189ba', //更改坐标轴文字颜色
              },
            },
            axisLine: {
              lineStyle: {
                color: 'rgba(101, 198, 231, 1)',
                opacity: 0.2,
                width: 1,
              },
            },
          }, ],
          yAxis: [{
            type: 'value',
            axisLabel: {
              show: true,
              textStyle: {
                color: '#5189ba', //更改坐标轴文字颜色
              },
            },
            axisLine: {
              show: false,
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: 'rgba(101, 198, 231, 1)',
                opacity: 0.2,
                width: 1,
              },
            },
          }, ],
          grid: {
            top: 20,
            left: 50, // 调整这个属性
            right: 20,
            bottom: 25,
          },
          series: [{
            name: '使用频次',
            type: 'line',
            areaStyle: {},
            emphasis: {
              focus: 'series',
            },
            data: yArr,
            itemStyle: {
              normal: {
                areaStyle: {
                  type: 'default',
                  color: new echarts.graphic.LinearGradient(
                    0,
                    0,
                    0,
                    1, //变化度 //两种种由深及浅的颜色
                    [{
                        offset: 0,
                        color: '#187da9',
                      },
                      {
                        offset: 1,
                        color: '#14323f',
                      },
                    ]
                  ),
                },
                color: '#05ccf5', //改变折线点的颜色
              },
            },
            lineStyle: {
              color: '#04bbdb', //改变折线颜色
            },
          }, ],
        })
        window.addEventListener("resize", () => {
          myChart.resize();
        });
      },
    },
  }
</script>

<style lang="less" scoped>
  ::v-deep .ant-calendar-picker {
    width: 3.95rem
      /* 316/80 */
    ;
    height: 100%;

    .ant-calendar-picker-input.ant-input {
      background-color: #101217;
      color: #909090;
      height: 100%;
      display: flex;
      align-items: center;

      .ant-calendar-range-picker-separator {
        color: #feffff;
        line-height: 0.375rem
          /* 30/80 */
        ;
      }
    }
  }

  .body_box {
    width: 100%;
    height: 100%;
    padding: 0 0.2rem 0.1rem 0.2rem;
    display: flex;
    flex-direction: column;

    .header {
      width: 100%;
      height: 7%;
      display: flex;
      justify-content: flex-end;

      .header-right {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        margin-right: 0.65rem
          /* 52/80 */
        ;

        .header-right-time {
          font-size: 0.175rem
            /* 14/80 */
          ;
          font-family: PingFang SC;
          letter-spacing: 0px;
          font-weight: 100;
          color: #ffffff;
          display: flex;
          align-items: center;

          .time-range-span {
            margin-right: 0.4375rem
              /* 35/80 */
            ;
            margin-left: 0.2rem
              /* 16/80 */
            ;
          }
        }

        .left-top-top-right-time-search {
          width: 0.85rem
            /* 68/80 */
          ;
          height: 0.4rem
            /* 32/80 */
          ;
          background: none;
          border: 2px solid #8f9094;
          border-radius: 10%;
          margin-right: 0.25rem
            /* 20/80 */
        }

        a {
          color: #fff;
        }

        .export {
          width: 0.85rem
            /* 68/80 */
          ;
          height: 0.4rem
            /* 32/80 */
          ;
          background: #1187d1;
          border: 0px;
          border-radius: 10%;
        }
      }
    }
  }

  .body {
    padding: 0
      /* 20/80 */
      0.2rem 0.1rem 0.2rem;
    width: 100%;
    height: 100%;
    display: flex;

    .left {
      width: 58.7%;
      height: 100%;

      .left-top {
        height: 67.6%;
        width: 100%;
        background: #131419;
        display: flex;
        align-items: center;
        justify-content: center;

        .left-top-core {
          width: 100%;
          height: 100%;
          // background-color: #fff;
        }
      }

      .left-bottom {
        height: 100%;
        width: 100%;
        background: #131419;
        border-radius: 0.075rem
          /* 6/80 */
        ;
        overflow: hidden;

        .left-bottom-core {
          width: 100%;
          height: 92%;
          display: flex;
          justify-content: center;

          .left-bottom-core-table {
            width: 100%;
            height: 100%;
            overflow: hidden;
            padding: 0 0.25rem
              /* 20/80 */
            ;

            .left-bottom-core-tableTitle {
              display: flex;
              height: 38px;
              align-items: center;

              span {
                height: 100%;
                width: 16%;
                font-size: 0.225rem
                  /* 18/80 */
                ;
                display: flex;
                align-items: center;
                justify-content: center;
                text-align: center;
                color: #fff;
                background: #38393b;
                border-left: 1px solid #111217;
              }

              span:nth-child(1) {
                border: none;
                width: 20%;
              }
            }

            .left-bottom-core-table-table {
              width: 100%;
              height: calc(100% - 38px);
              overflow: hidden;

              .left-bottom-core-table-table-seamless-warp {
                ul {
                  width: 100%;
                  height: 100%;
                  padding: 0;
                  margin: 0;
                  display: flex;
                  flex-direction: column;
                  justify-content: space-around;

                  li {
                    text-align: center;
                    height: 0.65rem
                      /* 52/80 */
                    ;
                    width: 100%;
                    line-height: 0.65rem
                      /* 52/80 */
                    ;
                    display: flex;
                    justify-content: space-around;
                    text-align: center;
                    font-size: 14px;
                    background: #26272c;

                    span {
                      width: 16%;
                      color: rgba(255, 255, 255, 0.75);
                      border-right: 1px solid #2d2d37;
                      border-bottom: 1px solid #24242e;
                      white-space: nowrap;
                      text-overflow: ellipsis;
                      overflow: hidden;
                    }

                    span:nth-child(1) {
                      width: 20%;
                      padding: 0 0.2rem
                        /* 16/80 */
                      ;
                      white-space: nowrap;
                      text-overflow: ellipsis;
                      overflow: hidden;
                      border-left: 1px solid #2d2d37;
                    }

                    .commonly {
                      color: #ffe200;
                    }

                    .serious {
                      color: #ff1111;
                    }
                  }

                  li:nth-child(even) {
                    background: #111217;
                    text-align: center;
                  }
                }
              }
            }
          }
        }
      }
    }

    .core {
      width: 20%;
      height: 100%;
      margin: 0 0.125rem
        /* 10/80 */
      ;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .core-top {
        width: 100%;
        height: 49.5%;
        background: #131419;
        border-radius: 0.075rem
          /* 6/80 */
        ;

        .core-top-body {
          height: 88%;
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }

      .core-bottom {
        width: 100%;
        height: 49.5%;
        background: #131419;
        // margin-top: 0.125rem /* 10/80 */;
        border-radius: 0.075rem
          /* 6/80 */
        ;

        .core-bottom-body {
          width: 100%;
          display: flex;
          height: 88%;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }

    .right {
      width: 20%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .right-core {
        height: 49.5%;
        width: 100%;
        background: #131419;
        // margin-bottom: 0.25rem /* 20/80 */;
        border-radius: 0.075rem
          /* 6/80 */
        ;

        .right-core-body {
          width: 100%;
          height: 88%;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }

      .right-bottom {
        width: 100%;
        height: 49.5%;
        background: #131419;
        border-radius: 0.075rem
          /* 6/80 */
        ;

        .right-bottom-body {
          display: flex;
          width: 100%;
          height: 88%;
          justify-content: center;
          align-items: center;

          .right-bottom-body-left {
            width: 92%;
            height: 92%;
          }

          // .right-bottom-body-right {
          //   height: 2.825rem /* 226/80 */;
          //   display: flex;
          //   flex-direction: column;
          //   color: #fff;
          //   align-items: center;
          //   justify-content: space-between;
          //   div {
          //     display: flex;
          //     flex-direction: column;
          //     justify-content: space-between;
          //     .five {
          //       height: 45.2px;
          //       display: flex;
          //       align-items: center;
          //       justify-content: center;
          //       color: #00a1e7;
          //       font-size: 14px;
          //     }
          //     .four {
          //       height: 56.5px;
          //       display: flex;
          //       align-items: center;
          //       justify-content: center;
          //       color: #00a1e7;
          //       font-size: 14px;
          //     }
          //     .three {
          //       height: 75.3px;
          //       display: flex;
          //       align-items: center;
          //       justify-content: center;
          //       color: #00a1e7;
          //       font-size: 14px;
          //     }
          //     .two {
          //       height: 113px;
          //       display: flex;
          //       align-items: center;
          //       justify-content: center;
          //       color: #00a1e7;
          //       font-size: 14px;
          //     }
          //     .one {
          //       height: 226px;
          //       display: flex;
          //       align-items: center;
          //       justify-content: center;
          //       color: #00a1e7;
          //       font-size: 14px;
          //     }
          //   }
          // }
        }
      }
    }
  }

  .topTitle {
    height: 12%;
    display: flex;
    align-items: center;
    font-size: 0.225rem
      /* 18/80 */
    ;
    color: #45c5e0;
    padding-left: 0.15rem
      /* 12/80 */
    ;
    letter-spacing: 0.025rem
      /* 2/80 */
    ;

    img {
      width: 0.125rem
        /* 10/80 */
      ;
      height: 0.1625rem
        /* 13/80 */
      ;
      margin-right: 0.0875rem
        /* 7/80 */
      ;
    }
  }

  .info-topTitle {
    height: 8%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-left: 0.15rem
      /* 12/80 */
    ;
    letter-spacing: 0.05rem
      /* 4/80 */
    ;
    font-size: 0.225rem
      /* 18/80 */
    ;
    // font-family: PingFang SC;
    // font-weight: 100;
    color: #44c5e3;

    img {
      width: 10px;
      height: 13px;
      margin-right: 7px;
    }
  }

  .right-query {
    font-size: 0.175rem
      /* 14/80 */
    ;
    font-family: PingFang SC;
    letter-spacing: 0px;
    font-weight: 100;
    color: #ffffff;
    display: flex;
    align-items: center;

    .time-range-span {
      margin-right: 0.4375rem
        /* 35/80 */
      ;
      margin-left: 0.2rem
        /* 16/80 */
      ;
      // height: 0.4875rem; /* 39/80 */
      display: flex;
      align-items: center;
    }

    .export-span {
      margin-left: 0.25rem
        /* 20/80 */
      ;
      margin-right: 0.25rem
        /* 20/80 */
      ;
    }

    .query-btn {
      background: #101217;
      border: 1px solid #ffffff;
      font-size: 0.15rem
        /* 12/80 */
      ;
      font-family: PingFang SC;
      font-weight: 100;
      color: #ffffff;
      height: 0.4rem
        /* 32/80 */
      ;
    }

    .export-btn {
      background: #1187d2;
      border: 1px solid #0e2361;
      font-size: 0.15rem
        /* 12/80 */
      ;
      font-family: PingFang SC;
      font-weight: 100;
      color: #ffffff;
      height: 0.4rem
        /* 32/80 */
      ;
    }
  }

  ::v-deep .ant-calendar-picker {
    width: 3.95rem
      /* 316/80 */
    ;
    height: 100%;

    .ant-calendar-picker-input.ant-input {
      background-color: #101217;
      color: #909090;
      height: 100%;

      .ant-calendar-range-picker-separator {
        color: #feffff;
        line-height: 0.375rem
          /* 30/80 */
        ;
      }
    }
  }

  /deep/ .ant-calendar-range-picker-input {
    padding-top: 0.05rem
      /* 4/80 */
       !important;
  }
</style>