<template>
  <div style="height:100%">
    <keep-alive>
      <component style="height:100%" :is="pageName" :data="data" />
    </keep-alive>
  </div>
</template>
<script>
  import orderStatisticsList from './orderStatisticsList'
  import orderStatisticsDetail from './modules/orderStatisticsDetail'
  export default {
    name: "orderStatisticsMange",
    data() {
      return {
        isActive: 0,
        data: {},
      };
    },
    components: {
      orderStatisticsList,
      orderStatisticsDetail
    },
    created() {
      this.pButton1(0);
    },
    //使用计算属性
    computed: {
      pageName() {
        switch (this.isActive) {
          case 0:
            return "orderStatisticsList";
          default:
            return "orderStatisticsDetail";
        }
      }
    },
    methods: {
      pButton1(index) {
        this.isActive = index;
      },
      async pButton2(index, item) {
        this.isActive = index;
        this.data = item
      }
    }
  }
</script>