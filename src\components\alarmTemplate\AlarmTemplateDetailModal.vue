<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    :destroyOnClose="true"
    :centered='true'
    @ok="handleOk"
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @cancel="handleCancel"
    cancelText="关闭"
  >
    <alarm-template-detail ref="realForm" @ok="submitCallback" :disabled="disableSubmit" :deviceInfo="deviceInfo" :showFlag="showFlag"></alarm-template-detail>
  </j-modal>
</template>

<script>
  import AlarmTemplateDetail from './AlarmTemplateDetail'
  export default {
    name: 'AlarmTemplateDetailModal',
    components: {
      AlarmTemplateDetail,
    },
    data () {
      return {
        title:'',
        width:'1150px',
        visible: false,
        disableSubmit: false,
        deviceInfo: null,
        showFlag:true
      }
    },

    methods: {
      add () {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.addAlarm();
        })
      },
      edit (record) {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.edit(record);
        })
      },
      close () {
        this.$emit('close');
        this.visible = false;
        // this.$refs.realForm.onAddchange();
      },
      handleOk () {
        this.$refs.realForm.submitForm();
        // this.$emit('ok');
      },
      submitCallback(){
        this.$emit('ok');
        this.visible = false;
      },
      handleCancel () {
        this.close()
      }
    }
  }
</script>
<style scoped lang='less'>
@import '~@assets/less/limitModalWidth.less';
</style>