<template>
  <div style="padding: 0 0px 0 0">
    <a-table
      ref="table"
      bordered
      :rowKey="
        (record, index) => {
          return record.id
        }
      "
      :columns="columns"
      :dataSource="dataSource"
      :loading="loading"
      :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
      :pagination="ipagination"
      @change="handleTableChange"
    >
      <template slot="status" slot-scope="text, record">
        <span v-if="record.enable == 1">
          <img v-if="record.status == 1" src="~@assets/bigScreen/28.png" alt="" class="stateImg" />
          <img v-else src="~@assets/bigScreen/57.png" alt="" class="stateImg" />
          <img v-if="record.alarmStatus == 1" src="~@assets/bigScreen/56.png" alt="" class="stateImg alarmStatus" />
        </span>
        <span v-else>
          <a-icon type="stop" theme="twoTone" two-tone-color="#eb2f96" style="font-size: 16px" class="stateImg" />
        </span>
        <span style="margin-left: 10px;" :class="record.deviceCode === businessInfo.businessNode ? 'light': ''">{{ text }}</span>
      </template>
      <template slot="tooltip" slot-scope="text">
        <a-tooltip placement="topLeft" :title="text" trigger="hover">
          <div class="tooltip">
            {{ text }}
          </div>
        </a-tooltip>
      </template>
      <span slot="action" class="caozuo" slot-scope="text, record">
        <a @click="handleDetailPage(record)">查看</a>
      </span>
<!--      <template slot="tagInfoList" slot-scope="text">
        <a-popover title="标签">
          <template slot="content">
            <div v-for="item in text" :key="item.id" style="margin: 5px 0">
              <span
                :style="{
                  'background-color': item.tagColor,
                  color: 'white',
                  'border-radius': '10px',
                  padding: '2px 10px',
                }"
              >
                {{ item.tagName }}
              </span>
            </div>
          </template>
          <a-icon type="environment" />
          &lt;!&ndash; <div style="display:flex,direction:column">
            <p v-for="item in text"
              :style="{'background-color':item.tag_color, color:'white',padding:'2px 1px',}">
              {{item.tag_name}}
              </p>
          </div> &ndash;&gt;
        </a-popover>
      </template>-->
    </a-table>
  </div>
</template>

<script>
import { getAction } from '@/api/manage'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { resolveOptions } from '@antv/x6/lib/registry/router/manhattan/options'
export default {
  name: 'associatedDeviceList',
  mixins: [JeecgListMixin],
  props: {
    businessInfo: {
      type: Object,
      default: {},
    },
    pageParam: {
      type: Object,
      default: {
        current: 1,
        pageSize: 10,
      },
    },
  },
  data() {
    return {
      // 表头
      columns: [
        {
          title: '设备名称',
          dataIndex: 'name',
          scopedSlots: {
            customRender: 'status',
          },
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
            return {
              style: cellStyle,
            }
          },
        },
        {
          title: '产品名称',
          dataIndex: 'productName',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 130px'
            return {
              style: cellStyle,
            }
          },
        },
        {
          title: '通信协议',
          dataIndex: 'transferProtocol',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 130px'
            return {
              style: cellStyle,
            }
          },
        },
        {
          title: '添加时间',
          dataIndex: 'createTime',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 150px'
            return {
              style: cellStyle,
            }
          },
        },
     /*   {
          title: '标签',
          dataIndex: 'tagInfoList',
          scopedSlots: {
            customRender: 'tagInfoList',
          },
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 100px;'
            return {
              style: cellStyle,
            }
          },
        },*/
        {
          title: '设备说明',
          dataIndex: 'description',
          scopedSlots: {
            customRender: 'tooltip',
          },
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 120px;max-width:400px'
            return {
              style: cellStyle,
            }
          },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 100,
          scopedSlots: {
            customRender: 'action',
          },
        },
      ],
      url: {
        list: '/business/info/topoDeviceList',
        //list: '/device/deviceInfo/list',
      },
      disableMixinCreated: true,
      queryParam: {
        topoId: '',
      },
    }
  },
  watch: {
    businessInfo: {
      handler(val, oldVal) {
        this.init(val)
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    init(record) {
      if (record.topoId) {
        this.queryParam.topoId = record.topoId
        if (Object.keys(this.pageParam).length > 0) {
          this.ipagination.current = this.pageParam.current
          this.ipagination.pageSize = this.pageParam.pageSize
        }
        this.loadData()
      }
    },
    /*获取告警级别内容*/
    getAlarmLevelData() {
      let that = this
      getAction(that.url.alarmLevel).then((res) => {
        if (res.success) {
          this.alarmLevelList = res.result
        }
      })
    },
    getAlarmColor(text, record) {
      let temp = this.alarmLevelList.filter((item) => {
        if (item.value == text) {
          return true
        }
      })
      record.levelColor = temp[0].color
      return temp[0].color
    },
    getAlarmTitle(text, record) {
      let temp = this.alarmLevelList.filter((item) => {
        if (item.value == text) {
          return true
        }
      })
      record.levelName = temp[0].title
      return temp[0].title
    },
    handleDetailPage: function (record) {
      let param = {
        data: record,
        pageParam: {
          current: this.ipagination.current,
          pageSize: this.ipagination.pageSize,
        },
      }
      this.$emit('OpenPreview', param)
    },
  },
}
</script>

<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
.light {
  color: #000;
  font-weight:bold;
}
</style>