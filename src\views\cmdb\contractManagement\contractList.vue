<template>
  <a-row :gutter="10" style="height: 100%;" class="vScroll">
    <a-col style="width:100%;height: 100%;display: flex;flex-direction: column">
      <!-- 查询区域 -->
      <a-card
        :bordered="false"
        :bodyStyle="{ paddingBottom: '0', marginRight: '12px' }"
        class="card-style"
        style="width: 100%"
      >
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="合同名称">
                  <a-input
                    :maxLength="maxLength"
                    placeholder="请输入合同名称"
                    v-model="queryParam.name"
                    :allowClear="true"
                    autocomplete="off"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="合同类型">
                  <j-dict-select-tag  v-model="queryParam.type" dictCode='contractType'
                    placeholder='请选择合同类型' />
                </a-form-item>
              </a-col>
              <a-col :span="colBtnsSpan()">
                <span
                  class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                >
                  <a-button
                    type="primary"
                    class="btn-search btn-search-style"
                    @click="searchQuery"
                  >查询</a-button>
                  <a-button class="btn-reset btn-reset-style" @click="searchReset">重置</a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered="false" style="width: 100%; flex: auto">
        <!-- 操作按钮区域 -->
        <div class="table-operator table-operator-style">
          <a-button @click="handleAdd">新增</a-button>
          <a-dropdown v-if="selectedRowKeys.length > 0">
            <a-menu slot="overlay">
              <a-menu-item key="1" @click="batchDel">
                <a-icon />删除
              </a-menu-item>
            </a-menu>
            <a-button>
              批量操作
              <a-icon type="down" />
            </a-button>
          </a-dropdown>
        </div>
        <a-table
          ref="table"
          bordered
          rowKey="id"
          :columns="columns"
          :dataSource="dataSource"
          :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          :pagination="ipagination"
          :loading="loading"
          @change="handleTableChange"
        >
          <template slot="tooltip" slot-scope="text">
            <a-tooltip placement="topLeft" :title="text" trigger="hover">
              <div class="tooltip">{{ text }}</div>
            </a-tooltip>
          </template>
          <template slot="amount" slot-scope="text">
            <span>￥{{text}}</span>
          </template>
          <span slot="action" slot-scope="text, record">
            <a @click="handleDetailPage(record)" style="color: #409eff">查看</a>
            <a-divider type="vertical" />
            <a @click="handleEdit(record)" style="color: #409eff">编辑</a>
            <a-divider type="vertical" />
            <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
              <a style="color: #409eff">删除</a>
            </a-popconfirm>
          </span>
        </a-table>
      </a-card>
      <add-contract-modal ref="modalForm" @ok="modalFormOk"></add-contract-modal>
    </a-col>
  </a-row>
</template>

<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
import addContractModal from './modules/addContractModal'
export default {
  name: 'contractManagementList',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components: {
    addContractModal
  },
  data() {
    return {
      maxLength:50,
      description: '合同管理页面',
      formItemLayout: {
        labelCol: {
          style: 'width:80px'
        },
        wrapperCol: {
          style: 'width:calc(100% - 80px)'
        }
      },
      // 表头
      columns: [
        {
          title: '合同编号',
          dataIndex: 'code'
        },
        {
          title: '合同名称',
          dataIndex: 'name'
        },
        {
          title: '合同类型',
          dataIndex: 'type'
        },
        {
          title: '合同描述',
          dataIndex: 'description',
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'tooltip'
          }
        },
        {
          title: '合同金额',
          dataIndex: 'amount',
          scopedSlots: {
            customRender: 'amount'
          }
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 147,
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: '/category/contract/list',
        delete: '/category/contract/delete',
        deleteBatch: '/category/contract/deleteBatch'
      }
    }
  },
  created() {},
  methods: {}
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
</style>