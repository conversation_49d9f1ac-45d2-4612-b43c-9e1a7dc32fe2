<template>
  <j-modal :title="title" :width="width" :centered="true" :visible="visible" :destroyOnClose="true" switchFullscreen
    cancelText="取消" okText="生成报告" :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }" @ok="submitOk"
    @cancel="handleCancel">
    <div class="table-operator table-operator-style">
      <a-button @click="handleAdd">新增模板</a-button>
    </div>
    <a-table ref="table" bordered rowKey="id" :columns="columns" :filterMultiple="false" :dataSource="dataSource"
      :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}" :pagination="ipagination" :loading="loading"
      :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange, type: 'radio' }"
      @change="handleTableChange">
      <span class="caozuo" slot="action" slot-scope="text, record">
        <a @click="handleDetail(record)">查看</a>
        <a-divider type="vertical" />
        <a @click="handleEdit(record)">编辑</a>
        <a-divider type="vertical" />
        <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
          <a>删除</a>
        </a-popconfirm>
      </span>
      <template slot="tooltip" slot-scope="text">
        <a-tooltip placement="topLeft" :title="text" trigger="hover">
          <div class="tooltip">
            {{ text }}
          </div>
        </a-tooltip>
      </template>
      <template slot="contentSlot" slot-scope="text">
        <a-tooltip placement="topLeft" overlayClassName="platformTableTooltip">
          <template>
            <div slot="title" v-html="text" id="textHtml"></div>
          </template>
          <div>
            <div class="tooltip">{{ text }}</div>
          </div>
        </a-tooltip>
      </template>
    </a-table>
    <add-template-modal ref="modalForm" @ok="modalFormOk"></add-template-modal>
  </j-modal>
</template>

<script>
  import {
    getAction
  } from '@/api/manage'
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import addTemplateModal from '@views/operationalEvaluationNew/operationalOrganization/modules/addTemplateModal.vue'
  export default {
    components: {
      addTemplateModal
    },
    name: 'templateListModal',
    mixins: [JeecgListMixin],
    props: {},
    data() {
      return {
        title: '选择模版',
        width: '1200px',
        disableSubmit: false,
        visible: false,
        confirmLoading: false,
        selectedTemplate: null,
        proInfo: {},
        columns: [{
            title: '模版名称',
            dataIndex: 'templateName',
            customCell: () => {
              let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
              return {
                style: cellStyle,
              }
            },
            scopedSlots: {
              customRender: 'tooltip',
            },
          },
          {
            title: '创建时间',
            dataIndex: 'createTime',
          },
          {
            title: '模板内容',
            dataIndex: 'template',
            scopedSlots: {
              customRender: 'contentSlot',
            },
            customCell: () => {
              let cellStyle = 'text-align: left; min-width: 100px;max-width:400px'
              return {
                style: cellStyle,
              }
            },
          },
          {
            title: '操作',
            dataIndex: 'action',
            fixed: 'right',
            width: 160,
            scopedSlots: {
              customRender: 'action'
            },
          },
        ],
        url: {
          list: '/devops/reportTemplate/list',
          delete: '/devops/reportTemplate/delete',
          render: '/devops/projectInfo/render',
        }
      }
    },
    methods: {
      show(record) {
        this.proInfo = record
        this.visible = true
        this.loadData()
      },
      close() {
        this.visible = false
      },
      handleCancel() {
        this.close()
      },
      // 生成模版
      submitOk() {
        if (this.selectedRowKeys.length <= 0) {
          this.$message.warning('请选择模版！')
          return
        } else {
          getAction(this.url.render, {
            templateId: this.selectedRowKeys[0],
            projectId: this.proInfo.id
          }).then((res) => {
            this.$message.success(res.message)
            this.$emit('ok')
            this.visible = false
          })
        }
      },
    },
  }
</script>

<style lang="less" scoped>
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';
</style>