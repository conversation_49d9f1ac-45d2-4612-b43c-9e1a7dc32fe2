<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="12">
            <a-form-item label="软件名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <!-- <a-input v-decorator="['patchName',validatorRules.patchName]" placeholder="请输入补丁名称"  ></a-input> -->
              <j-dict-select-tag  v-model="softwareId" placeholder="请选择软件名称"  dictCode="devops_software_info,software_name,id" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="补丁版本" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['patchVersion',validatorRules.patchVersion]" placeholder="请输入补丁版本(必须是数字)"  ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="操作系统" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag type="list" v-decorator="['patchOs',validatorRules.patchOs]" :trigger-change="true" dictCode="patch_os" placeholder="请选择操作系统" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="架构" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag type="list" v-decorator="['frawork',validatorRules.frawork]" :trigger-change="true" dictCode="cpuArch" placeholder="请选择架构" />
            </a-form-item>
          </a-col>
          <!-- <a-col :span="24">
            <a-form-item label="补丁文件id" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['patchFileId']" placeholder="请输入补丁文件id"  ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="安装脚本id" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['scriptFileId']" placeholder="请输入安装脚本id"  ></a-input>
            </a-form-item>
          </a-col> -->
          <a-col :span="12" style="height:70px">
            <a-form-item label="补丁文件" :labelCol="labelCol" :wrapperCol="wrapperCol">
               <span style="color:blue">{{model.patchFileName}}</span>
               <!-- <a-input v-decorator="['patchFileName',validatorRules.patchFileName]"></a-input> -->
              <!-- <j-upload v-decorator="['patchFileName',validatorRules.patchFileName]" :trigger-change="true" :number="1" ></j-upload> -->
            </a-form-item>
          </a-col>
          <a-col :span="12"  style="height:70px">
            <a-form-item label="脚本文件" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <span style="color:blue">{{model.scriptFileName}}</span>
              <!-- <a-input v-decorator="['scriptFileName',validatorRules.scriptFileName]"></a-input> -->
              <!-- <j-upload v-decorator="['scriptFileName',validatorRules.scriptFileName]" :trigger-change="true" :number="1" ></j-upload> -->
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="描述" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['patchDescribe']" rows="4" placeholder="请输入描述" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="是否有效" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag type="list" v-decorator="['effect']" :trigger-change="true" dictCode="valid_status" placeholder="请选择是否有效" />
            </a-form-item>
          </a-col>
          <!-- <a-col :span="24">
            <a-form-item label="软件id" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['softwareId']" placeholder="请输入软件id"  ></a-input>
            </a-form-item>
          </a-col> -->
          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import pick from 'lodash.pick'
  import { validateDuplicateValue } from '@/utils/util'
  import JFormContainer from '@/components/jeecg/JFormContainer'
  import JUpload from '@/components/jeecg/JUpload'
  import JDictSelectTag from "@/components/dict/JDictSelectTag"

  export default {
    name: 'PatchInfoForm',
    components: {
      JFormContainer,
      JUpload,
      JDictSelectTag,
    },
    props: {
      //流程表单data
      formData: {
        type: Object,
        default: ()=>{},
        required: false
      },
      //表单模式：true流程表单 false普通表单
      formBpm: {
        type: Boolean,
        default: false,
        required: false
      },
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        form: this.$form.createForm(this),
        model: {},
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 18 },
        },
        softwareId:"",
        confirmLoading: false,
        validatorRules: {
          // softwareId:{
          //   rules:[
          //     {required:true,message:"请选择软件名称"}
          //   ]
          // },
          // patchVersion:{
          //   rules:[
          //     {required:true,message:"请输入补丁版本"}
          //   ]
          // },
          // patchOs:{
          //   rules:[
          //     {required:true,message:"请选择操作系统"}
          //   ]
          // },
          // frawork:{
          //   rules:[
          //     {required:true,message:"请选择架构"}
          //   ]
          // },
          // patchFileName:{
          //   rules:[
          //     {required:true,message:"请选择补丁文件"}
          //   ]
          // },
          // scriptFileName:{
          //   rules:[
          //     {required:true,message:"请选择脚本文件"}
          //   ]
          // }
        },
        url: {
          add: "/patch/devopePatchInfo/add",
          edit: "/patch/devopePatchInfo/edit",
          queryById: "/patch/devopePatchInfo/queryById"
        }
      }
    },
    computed: {
      formDisabled(){
        if(this.formBpm===true){
          if(this.formData.disabled===false){
            return false
          }
          return true
        }
        return this.disabled
      },
      showFlowSubmitButton(){
        if(this.formBpm===true){
          if(this.formData.disabled===false){
            return true
          }
        }
        return false
      }
    },
    created () {
      //如果是流程中表单，则需要加载流程表单data
      this.showFlowData();
    },
    methods: {
      add () {
        this.edit({});
      },
      edit (record) {
        this.softwareId = record.softwareId;
        this.form.resetFields();
        this.model = Object.assign({}, record);
        this.visible = true;
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.model,'patchName','patchVersion','patchOs','frawork','patchFileId','scriptFileId','patchFileName','scriptFileName','patchDescribe','effect','softwareId'))
        })
      },
      //渲染流程表单数据
      showFlowData(){
        if(this.formBpm === true){
          let params = {id:this.formData.dataId};
          getAction(this.url.queryById,params).then((res)=>{
            if(res.success){
              this.edit (res.result);
            }
          });
        }
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            let formData = Object.assign(this.model, values);
            formData.softwareId = that.softwareId;
            httpAction(httpurl,formData,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
         
        })
      },
      popupCallback(row){
        this.form.setFieldsValue(pick(row,'patchName','patchVersion','patchOs','frawork','patchFileId','scriptFileId','patchFileName','scriptFileName','patchDescribe','effect','softwareId'))
      },
    }
  }
</script>