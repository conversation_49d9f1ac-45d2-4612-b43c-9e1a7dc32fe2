<template>
  <div>
    <div class="img_name_box">
      <img :src="src" alt="" />
      <span class="name_style">{{ name }}</span>
    </div>
    <div class="topBox"></div>
    <div class="num_box">
      <div class="value_box">
        <animate-number from="0" :to="value || 0" :key="value" duration="2000"></animate-number>
      </div>
      <div class="unit_style">个</div>
    </div>
    <div class="bottomBox"></div>
  </div>
</template>

<script>
  export default {
    props: {
      name: {
        type: String,
        default: '',
      },
      src: {
        type: String,
        default: '',
      },
      value: {
        type: String | Number,
        default: 0,
      },
    },
    data() {
      return {}
    },
    methods: {},
  }
</script>

<style scoped lang="less">
  .img_name_box {
    display: flex;
    align-items: center;

    .name_style {
      margin-left: 10px;
      font-size: 0.21rem;
      font-family: Adobe Heiti Std;
      font-weight: normal;
      color: #ffffff;
    }
  }

  .topBox {
    margin-top: 15px;
    width: 1.5rem;
    height: 1px;
    background-image: linear-gradient(to right,
        rgba(22, 129, 218, .1) 0%,
        rgba(22, 129, 218, 1) 50%,
        rgba(22, 129, 218, .1) 100%);
  }

  .num_box {
    width: 1.5rem;
    height: 0.525rem;
    display: flex;
    justify-content: center;
    align-items: center;
    background-image: linear-gradient(to right,
        rgba(84, 177, 255, 0) 0%,
        rgba(84, 177, 255, 0.2) 50%,
        rgba(84, 177, 255, 0) 100%);

    .value_box {
      font-size: 23px;
      font-weight: 400;
      color: #26e3ff;
      height: 40px;
      font-family: zhengku;
      background: linear-gradient(to top, #54b1ff 0%, #ffffff 86.8896484375%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .unit_style {
      margin-left: 10px;
      font-size: 13px;
      font-family: AlibabaPuHuiTiR;
      font-weight: 400;
      color: #26b0ff;
    }
  }

  .bottomBox {
    width: 1.5rem;
    height: 1px;
    background-image: linear-gradient(to right,
        rgba(22, 129, 218, .1) 0%,
        rgba(22, 129, 218, 1) 50%,
        rgba(22, 129, 218, .1) 100%);
  }
</style>