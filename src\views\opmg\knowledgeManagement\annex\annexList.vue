<template>
  <a-row style='height: 100%; margin-left: 16px; margin-right: 4px'>
    <a-col style='width: 100%; height: 100%; display: flex; flex-direction: column'>
      <!-- 查询区域 -->
      <a-card :bordered='false' class='card-style' style='width: 100%'>
        <div class='table-page-search-wrapper'>
          <a-table :columns="topColumns" :data-source="topData" :pagination="false" bordered>
            <template slot="customRenderStatus" slot-scope="status">
              <a-tag v-if="status==0" color="green">已启动</a-tag>
              <a-tag v-if="status==-1" color="orange">已暂停</a-tag>
            </template>
            <template slot='cronExpression' slot-scope='text'>
            </template>
            <span slot='topAction' slot-scope='text, record' class='caozuo'>
              <a @click='taskJump'>定时任务详情</a>
            </span>
          </a-table>
        </div>
      </a-card>
      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <div style='margin-top:15px;'>
          <a-table ref='table' bordered rowKey='id' :columns='columns' :dataSource='dataSource'
            :pagination='ipagination' :loading='loading' @change='handleTableChange'>
            <template slot='isPrivate' slot-scope='text'>
              <a-icon :type='text===visibility[1].value?"lock":"global"'
                :style='{color:text===visibility[1].value?"#FE9400":"#4BD863"}'></a-icon>
              {{ text === visibility[1].value ? visibility[1].label : visibility[0].label }}
            </template>

            <span slot='action' slot-scope='text, record' class='caozuo'>
              <a @click='handleDetailPage(record)'>查看</a>
              <a-divider type='vertical' />
              <a @click="execute(record.id)">执行一次</a>
            </span>

            <template slot='tooltip' slot-scope='text'>
              <a-tooltip placement='topLeft' :title='text' trigger='hover'>
                <div class='tooltip'>
                  {{ text }}
                </div>
              </a-tooltip>
            </template>
          </a-table>
        </div>
      </a-card>
    </a-col>
  </a-row>
</template>

<script>
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import {
    postAction,
    getAction
  } from '@/api/manage'
  import cronstrue from 'cronstrue'
  import { mapActions } from 'vuex'
  import router from '@/router'
  import store from '@/store'
  import 'cronstrue/locales/zh_CN'
  import { approvalState, visibility } from '@views/opmg/knowledgeManagement/knowledgeBase/modules/dataListAndFunc'
  import { generateBigscreenRouter, generateIndexRouter } from '@/utils/util'
  import { PLATFORM_TYPE, RESET_MENUS } from '@/store/mutation-types'
  export default {
    name: 'annexList',
    mixins: [JeecgListMixin],
    data() {
      return {
        topData: [],
        visibility: visibility,
        approvalState: approvalState,
        topColumns: [{
            title: 'cron解析',
            dataIndex: 'cronExpression',
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 150px;max-width:300px'
              return {
                style: cellStyle
              }
            }
          }, {
            title: '任务状态',
            dataIndex: 'status',
            scopedSlots: {
              customRender: 'customRenderStatus'
            },
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 150px;max-width:300px'
              return {
                style: cellStyle
              }
            }
          }, {
            title: '描述',
            dataIndex: 'description',
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 150px;max-width:300px'
              return {
                style: cellStyle
              }
            },
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            fixed: 'right',
            width: 150,
            scopedSlots: {
              customRender: 'topAction'
            }
          }
        ],
        columns: [{
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            isUsed: false,
            customCell: () => {
              let cellStyle = 'text-align:center;width:60px'
              return {
                style: cellStyle
              }
            },
            customRender: function (t, r, index) {
              return parseInt(index) + 1
            },
          }, {
            title: '标题',
            dataIndex: 'title',
            customCell: () => {
              let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
              return {
                style: cellStyle
              }
            },
            scopedSlots: {
              customRender: 'tooltip'
            }
          },
          {
            title: '主题名称',
            dataIndex: 'topicName',
            customCell: () => {
              let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
              return {
                style: cellStyle
              }
            },
            scopedSlots: {
              customRender: 'tooltip'
            }
          },
          {
            title: '能见度',
            dataIndex: 'isPrivate',
            customCell: () => {
              let cellStyle = 'width:100px'
              return {
                style: cellStyle
              }
            },
            scopedSlots: {
              customRender: 'isPrivate'
            }
          },
          {
            title: '创建人员',
            dataIndex: 'createBy'
          },
          {
            title: '索引失败报错',
            dataIndex: 'attachmentIndexFailureText'
          },
          {
            title: '索引失败时间',
            dataIndex: 'attachmentIndexFailureTime',
            customCell: () => {
              let cellStyle = 'width: 160px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '索引失败次数',
            dataIndex: 'attachmentIndexFailureCount'
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            fixed: 'right',
            width: 150,
            scopedSlots: {
              customRender: 'action'
            }
          }
        ],
        url: {
          list: '/kbase/knowledges/getIndexAttachmentFailedKnowledge',
          attachment: '/kbase/knowledges/attachment',
          cronList: 'sys/quartzJob/list'
        }
      }
    },
    activated() {
      this.loadData()
    },
    created() {
      this.getCron()
    },
    methods: {
      ...mapActions(['GetMenuPermissions']),
      taskJump() {
        let that = this
        let params = {
          url: '/isystem/QuartzJobList',
          component: 'system/QuartzJobList',
        }
        that.GetMenuPermissions(params).then((res) => {
            if (res.success) {
              sessionStorage.setItem(PLATFORM_TYPE, res.platformType)
              const menuData = res.menu
              let constRoutes = []
              if (res.platformType === 4 || res.platformType === 8) {
                constRoutes = generateBigscreenRouter(menuData)
              } else {
                constRoutes = generateIndexRouter(menuData)
              }
              // 添加主界面路由
              store
                .dispatch('UpdateAppRouter', {
                  constRoutes,
                })
                .then(() => {
                  // 根据roles权限生成可访问的路由表
                  // 动态添加可访问路由表
                  router.addRoutes(store.getters.addRouters)
                  that.$store.commit(RESET_MENUS, true)
                  that.$router.push({
                    name: 'isystem-QuartzJobList',
                    path: '/isystem/QuartzJobList',
                  })
                })
            } else {
              alert(res.message)
            }
          })
          .catch((err) => {
            alert(err.message)
          })
      },
      getCron() {
        let params = {}
        params.jobClassName = 'HandleIndexFailedAttachment'
        getAction(this.url.cronList, params).then((res) => {
          if (res.success) {
            this.topData = res.result.records
            this.topData[0].cronExpression = cronstrue.toString(this.topData[0].cronExpression, {
              locale: 'zh_CN'
            })
          } else {
            this.$message.warning(res.message)
          }
        })
      },
      // 查看
      handleDetailPage: function (record) {
        this.$parent.pButton2(1, record)
      },
      execute(id) {
        let Knowledge = {}
        Knowledge.id = id
        postAction(this.url.attachment, Knowledge).then((res) => {
          if (res.success) {
            this.$message.success(res.message)
          } else {
            this.$message.warning(res.message)
          }
        })
      },
    }
  }
</script>
<style lang='less' scoped>
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';
</style>