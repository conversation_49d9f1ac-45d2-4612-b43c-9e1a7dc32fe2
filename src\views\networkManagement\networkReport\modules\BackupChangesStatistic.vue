<template>
  <div style='height: 100%'>
    <card-frame>
      <div slot='titleSlot' class='title'>
        <yq-icon class='icon' type='netChange'></yq-icon>
        <span class='text'>备份变更汇总(近1个月)</span>
      </div>
      <div slot='bodySlot' class='body-height' v-if='chartData.total >0'>
        <pieChart :name="'备份变更汇总(近1个月)'" :data-type-name='dataTypeName' :chart-data='chartData'></pieChart>
      </div>
      <div slot='bodySlot' class='body-height body-empty' v-else>
        <a-spin :spinning='loading' v-if='loading'></a-spin>
        <a-list :data-source='[]' v-else />
      </div>
    </card-frame>
  </div>
</template>
<script>
import { getAction } from '@api/manage'
import yqIcon from '@comp/tools/SvgIcon'
import cardFrame from '@views/networkManagement/networkReport/modules/CardFrame.vue'
import pieChart from '@views/networkManagement/networkReport/modules/TwoElementsPieChart.vue'

export default {
  name: 'BackupChangesStatistic',
  components: { cardFrame, yqIcon, pieChart },
  data() {
    return {
      loading: false,
      dataTypeName: ['已变更设备', '未变更设备'],
      chartData: {},
      days: 30,
      dictCode: 'Network',
      url: {
        requestDataUrl: '/net/device/devConfigureBackCount'
      }
    }
  },
  created() {
    this.getChartData()
  },
  methods: {
    getChartData() {
      this.chartData = {
        todoNum: 0,
        doneNum: 0,
        total: 0,
        pTotal: 100
      }
      this.loading = true
      getAction(this.url.requestDataUrl, { dictCode: this.dictCode, days: this.days }).then((res) => {
        if (res.success) {
          let todoNum = res.result.none ? parseInt(res.result.none) : 0
          let doneNum = (res.result.failed ? parseInt(res.result.failed) : 0) + (res.result.success ? parseInt(res.result.success) : 0)
          let data = {
            todoNum: todoNum,
            doneNum: doneNum,
            total: todoNum + doneNum,
            pTotal: 100
          }
          data.pTodoNum = data.total > 0 ? parseInt(data.todoNum / data.total * 100) : 0
          data.pDoneNum = data.total > 0 ? (data.pTotal - data.pTodoNum) : 0
          this.chartData = data
        } else {
          this.$message.error(res.message)
        }
        this.loading = false
      }).catch((err) => {
        this.$message.error(err.message)
        this.loading = false
      })
    }
  }
}
</script>

<style scoped lang='less'>
.tabs-card {
  height: 100%;
}

/*::v-deep .ant-card-head-wrapper{
  height: 100%;

  .ant-card-head-title{
    height: 100%;

    .title {
      height: 100%;
      display: flex;
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
      margin-top: 2px;
      align-items: center;
    }
  }
}*/
::v-deep .ant-card-body {
  height: calc(100% - 48px);
}
</style>