<template>
  <div style='background-color: #ffffff;height: 100%;padding:12px'>
    <a-spin :spinning="confirmLoading">
      <div class="left-wrapper">
        <div v-if='showLook' class="left-btn">
          <a-button @click='openOnduty' type='primary'>
            值班安排
          </a-button>
          <a-button @click='deleteOnDuty' type='danger'>
            批量删除
          </a-button>
        </div>

        <a-select  v-if="$yqHasPermission('dutyShift:show')"
                    :getPopupContainer='node=>node.parentNode'
                    placeholder='请选择班次信息'
                    v-model='shiftId'
                    @change='changUserList'
                    style='width:100%'
                    :allow-clear='true'
          >
            <a-select-option v-for='(data,i) in dataSource' :key='i' :value='data.id'>
              {{ data.shiftName }}
            </a-select-option>
          </a-select>
        <div :style='{height:$yqHasPermission("dutyShift:show")? "calc(100% - 57px - 32px - 8px)":"calc(100% - 57px - 8px)",overflow: "auto",marginTop: "8px"}'>
            <a-tree :tree-data='userList' show-icon
                    :replace-fields='replaceFields'
                    @select='onSelect'
                    default-expand-all
            >
              <a-icon slot='user' type='user' />
            </a-tree>
          </div>

      </div>
    </a-spin>
    <j-modal :visible='visible'
             title='值班安排'
             @cancel='handleCancel'
             :maskClosable='true'
             :width='900'
             switchFullscreen
             :centered="true"
    >
      <template slot='footer'>
        <a-button type='text' @click='visible = false'>关 闭</a-button>
      </template>

      <div style='padding: 5px' v-has="'dutyShift:add'">
        <a-button type='primary' @click='handleAdd()'>添 加</a-button>
      </div>

      <div>
        <a-table
          ref='table'
          :columns='columns'
          bordered
          :dataSource='dataSource'
          rowKey='id'
          size='middle'
          :scroll={x:true}
        >
          <span slot='action' slot-scope='text, record' v-if='!record.holidayType'>
          <template>
               <a @click='shiftArrangement(record)'>排班</a>
                <a-divider type='vertical' />
               <a @click='handleEdit(record)'>编辑</a>
               <a-divider type='vertical' v-has="'dutyShift:delete'" />
               <a-popconfirm v-has="'dutyShift:delete'" title='确定删除吗?' @confirm='() => handleDelete(record.id)'>
                 <a>删除</a>
               </a-popconfirm>
             </template>
          </span>
          <span slot='time' slot-scope='text, record'>
              <template>{{ record.startTime }}~{{ record.endTime }}</template>
            </span>
        </a-table>
      </div>
    </j-modal>

    <duty-shift-modal ref='modalForm' @loadData='loadData' @loadData1='loadData1'></duty-shift-modal>
    <shift-arrangement-modal ref='shiftArrangementModal' @loadData1='loadData1' @getUserList='getUserList'></shift-arrangement-modal>
    <!--    <shift-form ref='modalForm' @loadData='loadData' @loadData1='loadData1'></shift-form>-->
    <delete-shift-modal ref='deleteForm' @loadData1='loadData1' @getUserList='getUserList' :dataSource='dataSource' :userList='userList'></delete-shift-modal>
  </div>
</template>

<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { deleteAction, getAction } from '@api/manage'
import dateFunc from 'vue-fullcalendar/src/components/dateFunc'
import deleteShiftModal from './deleteShiftModal'
import dutyShiftModal from './dutyShiftModal'
import shiftArrangementModal from './shiftArrangementModal'
export default {
  name:'dutyArrangementLeft',
  props: ['getEvenList', 'showLook'],
  mixins: [JeecgListMixin],
  components: {
    deleteShiftModal,dutyShiftModal,shiftArrangementModal
  },

  data() {
    return {
      replaceFields: {
        title: 'realname',
        children: 'child',
        key: 'username'
      },
      url: {
        list: '/duty/shift/queryList',
        delete: '/duty/shift/delete'
      },
      userList: [],
      shiftId: undefined,
      userId: '',
      data: {},
      visible: false,
      //表头
      columns: [
        {
          title: '序号',
          dataIndex: '',
          key: 'rowIndex',
          customCell: () => {
            let cellStyle = 'text-align:center;min-width:60px'
            return { style: cellStyle }
          },
          customRender: function(t, r, index) {
            return parseInt(index) + 1
          }
        },
        {
          title: '班次名称',
          dataIndex: 'shiftName',
          customCell: () => {
            let cellStyle = 'text-align:center;min-width:120px'
            return { style: cellStyle }
          }
        },
        {
          title: '时间范围',
          dataIndex: 'time',
          customCell: () => {
            let cellStyle = 'text-align:center;width:120px'
            return { style: cellStyle }
          },
          scopedSlots: { customRender: 'time' }
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 150,
          scopedSlots: { customRender: 'action' }
        }
      ],
      asycEventList: [],
      confirmLoading: false
    }
  },

  mounted() {
    this.loadData1()
    this.getUserList()
  },
  methods: {
    handleDelete(id) {
      if (!this.url.delete) {
        this.$message.error('请设置url.delete属性!')
        return
      }
      var that = this
      deleteAction(that.url.delete, { id: id }).then((res) => {
        if (res.success) {
          if (this.shiftId===id){
            this.shiftId=undefined
          }
          //重新计算分页问题
          that.reCalculatePage(1)
          that.$message.success(res.message)
          that.loadData()
          that.loadData1()
          that.getUserList()
        } else {
          that.$message.warning(res.message)
        }
      })
    },
    onSelect(checkedKeys) {
      this.userId = checkedKeys[0]
      this.loadData1()
    },
    /**
     * 将时分秒转为时间戳
     *
     * @param time
     * @returns {number}
     */
    timeToSec(time) {
      if (time !== null) {
        var s = "";
        var hour = time.split(":")[0];
        var min = time.split(":")[1];
        var sec = time.split(":")[2];
        s = Number(hour * 3600) + Number(min * 60) + Number(sec);
        return s;
      }
    },
    loadData1() {
      this.asycEventList = []
      this.confirmLoading = true
      getAction('/shiftUser/record/queryUserRecords', { shiftId: this.shiftId, userId: this.userId }).then((res) => {
        if (res.success) {
          if (res.result.length > 0) {
            for (let i = 0; i < res.result.length; i++) {
              let event = {}
              event.planColor = res.result[i].color
              let a=""
              if (this.timeToSec(res.result[i].startTime+":00")-this.timeToSec(res.result[i].endTime+":00")>0){
                a="次日"
              }
              event.date = res.result[i].startTime + '~'+a+ res.result[i].endTime
              event.title = res.result[i].userName
              event.id = res.result[i].id
              event.recordId = res.result[i].id
              event.shiftName = res.result[i].shiftName
              event.shiftId = res.result[i].shiftId
              event.userId = res.result[i].userId

              event.time = new Date(dateFunc.format(res.result[i].dutyDate, 'yyyy-MM-dd'));
              event.newTime = new Date(dateFunc.format(new Date(), 'yyyy-MM-dd'));
              event.startTime = dateFunc.format(res.result[i].dutyDate, 'yyyy-MM-dd')
              event.endTime = dateFunc.format(res.result[i].dutyDate, 'yyyy-MM-dd')
              this.asycEventList.push(event)
            }
          } else {
            this.asycEventList = []
          }
          this.getEvenList(this.asycEventList)
          this.confirmLoading = false
        } else {
          this.$message.warning(res.message)
          this.confirmLoading = false
        }
      }).catch(err => {
        this.confirmLoading = false
      })
    },

    /**
     * 打开值安排
     */
    openOnduty() {
      this.visible = true
      this.loadData()
    },
    /**
     * 关闭弹窗
     */
    handleCancel() {
      this.visible = false
    },
    /**
     * 获取人员信息
     * @param a
     */
    getUserList() {
      getAction('/duty/shift/queryUsers').then((res) => {
        if (res.success) {
          if (res.result.length > 0) {
            for (let value of res.result) {
              value.slots = { icon: 'user' }
            }
            this.userList = res.result
          } else {
            this.userList = []
            this.asycEventList = []
            this.getEvenList(this.asycEventList)
          }
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    changUserList(a) {
      if (a === undefined) {
        this.userId = ''
        this.shiftId = undefined
      }
      this.loadData1()
    },
    shiftArrangement(item) {
      this.$refs.shiftArrangementModal.add(item.id)
      //this.$refs.modalForm.addSchedule(item.id)
    },
    deleteOnDuty() {
      this.$refs.deleteForm.openVisible()
    }
  }
}
</script>

<style scoped lang='less'>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
@import '~@assets/less/normalModal.less';

::v-deep .ant-spin-nested-loading{
  height: 100%;
  .ant-spin-container{
    height: 100%;

    .left-wrapper{
      height: 100%;
      display: flex;
      flex-flow: column nowrap;
      .left-btn{
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-bottom:12px;
        margin-bottom: 12px;
        border-bottom:1px solid #e8e8e8
      }
    }
  }
}
::v-deep .ant-tree li span.ant-tree-switcher, .ant-tree li span.ant-tree-iconEle{
  display: none;
}
</style>