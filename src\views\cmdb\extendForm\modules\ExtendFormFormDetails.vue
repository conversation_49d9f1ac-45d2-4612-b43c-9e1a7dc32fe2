<template>
  <div class='lookTop'>
    <a-card>
      <a-row>
        <a-col :span="24">
          <div class='lookEditionWrapper'>
            <div class='lookEdition' @click="handleEdit(data)" v-has="'extendFields:edit'">
              <a-icon type="edit" class='editIcon' />编辑
            </div>
            <div class='lookReturn'>
              <img src="~@/assets/return1.png" alt="" @click="getGo" class='returnImage'/>
            </div>
          </div>
        </a-col>
        <a-col :span="24">
          <table class="gridtable">
            <tr>
              <td class="leftTd">表单名称</td>
              <td class="rightTd">{{ data.name }}</td>
              <td class="leftTd">表单编码</td>
              <td class="rightTd">{{ data.code }}</td>
            </tr>
            <tr>
              <td class="leftTd">描述</td>
              <td class="rightTd">{{ data.description }}</td>
              <td class="leftTd">资产类型</td>
              <td class="rightTd">{{ data.categoryName }}</td>
            </tr>
          </table>
        </a-col>
      </a-row>
      <extend-form-modal ref="modalForm" @ok="query"></extend-form-modal>
    </a-card>
  </div>

</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import pick from 'lodash.pick'
import { validateDuplicateValue } from '@/utils/util'
import JFormContainer from '@/components/jeecg/JFormContainer'
import ExtendFormModal from './ExtendFormModal'

export default {
  name: 'ExtendFormFormDetails',
  components: {
    ExtendFormModal,
    JFormContainer,
  },
  props: {
    //流程表单data
    formData: {
      type: Object,
      default: () => {},
      required: false,
    },
    //表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false,
    },
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false,
    },
    data: {
      type: Object,
    },
  },
  data() {
    return {
      form: this.$form.createForm(this),
      assetsCategoryList: [],
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      confirmLoading: false,

      url: {
        add: '/extendForm/extendForm/add',
        edit: '/extendForm/extendForm/edit',
        queryById: '/extendForm/extendForm/queryById',
        CategoryqueryById: '/category/cmdbAssetsCategory1/queryById',
        findAll: '/category/cmdbAssetsCategory1/findAll',
      },
      validatorRules: {
        assetsCategoryId: {
          rules: [{ required: true, message: '请选择资产类型!' }],
        },
        ip: {
          rules: [{ required: true, message: '请选择设备IP!' }],
        },
        macAddr: {
          rules: [{ required: true, message: '请输入MAc地址!' }],
        },
      },
    }
  },
  mounted() {},
  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    },
  },
  created() {
    //如果是流程中表单，则需要加载流程表单data
    this.showFlowData()
    this.findAll()
  },
  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(pick(this.model, 'name', 'code', 'description', 'assetsCategoryId'))
      })
    },
    handleEdit: function (record) {
      this.$refs.modalForm.edit(record)
      this.$refs.modalForm.title = '编辑'
      this.$refs.modalForm.disableSubmit = false
    },
    //渲染流程表单数据
    showFlowData() {
      if (this.formBpm === true) {
        let params = { id: this.formData.dataId }
        getAction(this.url.queryById, params).then((res) => {
          if (res.success) {
            this.edit(res.result)
          }
        })
      }
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let formData = Object.assign(this.model, values)
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    popupCallback(row) {
      this.form.setFieldsValue(pick(row, 'name', 'code', 'description', 'assetsCategoryId'))
    },
    //返回上一级
    getGo() {
      this.$parent.pButton2(0)
    },
    findAll() {
      getAction(this.url.findAll).then((res) => {
        if (res.success) {
          this.assetsCategoryList = res.result
        }
      })
    },

    query() {
      getAction(this.url.queryById, { id: this.data.id }).then((res) => {
        if (res.code == 200) {
          this.data = res.result
        }
      })
    },

    //   selDynamicTempByProId(e) {
    //   if (!!e) {
    //     let paramObj = {
    //       assetsCategoryId: e.trim()
    //     }
    //     getAction(this.url.CategoryqueryById, paramObj).then(res => {
    //       if (!!res) {

    //         res.forEach(ele => {
    //           if (ele.defaultValue != null && ele.defaultValue.length > 0) {
    //             ele.connectValue = ele.defaultValue
    //           }
    //         })
    //         this.collectInfoList = res
    //       }
    //     })
    //   }
    // },
  },
}
</script>
<style scoped>
@import '~@assets/less/lookPage.less';
</style>
