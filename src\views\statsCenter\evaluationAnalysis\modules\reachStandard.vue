<template>
  <card-frame :title="title" :titleBgPath="'/statsCenter/evaluate/title2.png'">
    <div slot="bodySlot" style="height: 100%;width: 100%;" id="orderCountPie" class="bg"></div>
  </card-frame>
</template>

<script>
import echarts from 'echarts'
import cardFrame from '@views/statsCenter/com/cardFrame.vue'
import { getAction } from '@/api/manage'
export default {
  components: {
    cardFrame
  },
  data() {
    return {
      time1: '',
      time2: '',
      url: {
        count: '/data-analysis/order/count'
      }
    }
  },
  props: {
    title: ''
  },
  mounted() {
    this.count()
  },
  methods: {
    // 服务请求处理量柱状图数据
    count() {
      getAction(this.url.count, {
        time1: this.time1,
        time2: this.time2
      }).then(res => {
        if (res.code == 200) {
          this.orderCountPie(res.result.chart)
        }
      })
    },

    // 服务请求达标率统计环形图
    orderCountPie(pieData) {
      var titleArr = [],
        seriesArr = []
      const centerX = 100 / 3 // 原点x轴的单位距离
      const color = [
        {
          outCircle: '#0C7988',
          percent: '#8AFFFB', // 百分比颜色
          inCircleBackground1: '#A3FFFC', // 进度渐变色1
          inCircleBackground2: '#42BAFF' // 进度渐变色2
        },
        {
          outCircle: '#1D5EA4',
          percent: '#3693E7', // 百分比颜色
          inCircleBackground1: '#83BCF3', // 进度渐变色1
          inCircleBackground2: '#2E8EE5' // 进度渐变色2
        },
        {
          outCircle: '#1D5EA4',
          percent: '#3693E7', // 百分比颜色
          inCircleBackground1: '#46FDFF', // 进度渐变色1
          inCircleBackground2: '#1EABFB' // 进度渐变色2
        }
      ]

      pieData.forEach(function(item, index) {
        const centerXOffset = centerX * (index % 3) + centerX / 2
        const center = [centerXOffset + '%', '50%']
        const ratio = ((item.value / 100) * 360).toFixed(0)

        titleArr.push({
          text: item.name + '（%）',
          left: centerXOffset + '%',
          bottom: '0',
          textAlign: 'center',
          textStyle: {
            fontWeight: 'normal',
            fontSize: '12',
            color: '#fff',
            textAlign: 'center'
          }
        })

        seriesArr.push(
          {
            // 最外圈圆环
            z: 1,
            type: 'pie',
            radius: ['58%', '61%'],
            center,
            itemStyle: {
              color: color[index].outCircle
            },
            label: {
              show: false
            },
            hoverAnimation: false,
            data: [100]
          },
          {
            // 次外圈圆环
            z: 1,
            type: 'pie',
            radius: '50%',
            center,
            itemStyle: {
              normal: {
                color: {
                  type: 'radial',
                  x: 0.5,
                  y: 0.5,
                  r: 0.5,
                  colorStops: [
                    {
                      offset: 0,
                      color: 'rgba(0,0,0,0)' // 0% 处的颜色
                    },
                    {
                      offset: 0.4,
                      color: 'rgba(0,0,0,0)' // 50% 处的颜色
                    },
                    {
                      offset: 1,
                      color: color[index].outCircle // 100% 处的颜色
                    }
                  ]
                }
              }
            },
            label: {
              show: false
            },
            hoverAnimation: false,
            data: [100]
          },
          {
            // 内圆刻度
            z: 2,
            type: 'gauge',
            radius: '50%',
            center,
            startAngle: 90,
            endAngle: ~ratio + 91,
            splitNumber: ((item.value / 100) * 11).toFixed(1), // 分割成11份
            axisLine: {
              show: false
            },
            axisLabel: {
              show: false
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: true,
              length: 12,
              lineStyle: {
                color: '#002334', // 刻度颜色
                width: 3 // 刻度宽度大小
              }
            },
            hoverAnimation: false,
            detail: {
              show: false
            }
          },
          {
            // 内圆进度百分比
            z: 1,
            name: pieData[index].name,
            type: 'pie',
            startAngle: 89,
            radius: ['37%', '50%'],
            center,
            hoverAnimation: false,
            data: [
              {
                value: pieData[index].value,
                label: {
                  normal: {
                    formatter: function(params) {
                      return params.value.toFixed(0) + '%'
                      // return params.value + '%'
                    },
                    position: 'center',
                    show: true,
                    textStyle: {
                      fontSize: '16',
                      fontWeight: '600',
                      color: color[index].percent
                    }
                  }
                },
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      {
                        offset: 0,
                        color: color[index].inCircleBackground1
                      },
                      {
                        offset: 1,
                        color: color[index].inCircleBackground2
                      }
                    ]),
                    label: {
                      show: false
                    },
                    labelLine: {
                      show: false
                    }
                  }
                }
              },
              {
                value: 100 - pieData[index].value,
                name: 'invisible',
                itemStyle: {
                  normal: {
                    color: 'transparent'
                  }
                }
              }
            ]
          }
        )
      })

      let myChart = this.$echarts.init(document.getElementById('orderCountPie'))
      myChart.setOption({
        grid: {
          left: '5%',
          right: '5%',
          bottom: '0%',
          top: '0%',
          containLabel: true
        },
        title: titleArr,
        series: seriesArr
      })
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    }
  }
}
</script>

<style scoped lang="less">
.bg {
  position: relative;
  top: -0.09rem;;
  background-color: rgba(0, 23, 34, 0.36);
}
</style>