<svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" xmlns:xlink="http://www.w3.org/1999/xlink" class="x6-graph-svg" viewBox="-270 -276.8666687011719 600 166.86666870117188"><style type="text/css"><![CDATA[
            .x6-graph-svg-viewport {
              font-size:32px;
              transform: matrix(1, 0, 0, 1, 0, 0);
            }
          ]]></style><defs style="border: 0px none rgba(0, 0, 0, 0.65); color: rgba(0, 0, 0, 0.65); cursor: grab; outline: rgba(0, 0, 0, 0.65) none 0px;"/><g class="x6-graph-svg-viewport" transform="matrix(1,0,0,1,399,509)" style="border: 0px none rgba(0, 0, 0, 0.65); color: rgba(0, 0, 0, 0.65); cursor: grab; outline: rgba(0, 0, 0, 0.65) none 0px;"><g class="x6-graph-svg-primer" style="border: 0px none rgba(0, 0, 0, 0.65); color: rgba(0, 0, 0, 0.65); cursor: grab; outline: rgba(0, 0, 0, 0.65) none 0px;"/><g class="x6-graph-svg-stage" style="border: 0px none rgba(0, 0, 0, 0.65); color: rgba(0, 0, 0, 0.65); cursor: grab; outline: rgba(0, 0, 0, 0.65) none 0px;"><g data-cell-id="16d800d9-380e-458e-9241-7fe8f9efc416" data-shape="panel-node" class="x6-cell x6-node" transform="translate(-270,-260)" style="border: 0px none rgba(0, 0, 0, 0.65); color: rgba(0, 0, 0, 0.65); cursor: move; outline: rgba(0, 0, 0, 0.65) none 0px;"><rect fill="#E7EFF5" stroke="#E8E8E8" stroke-width="1" width="600" height="150" style="border: 0px none rgba(0, 0, 0, 0.65); color: rgba(0, 0, 0, 0.65); cursor: move; outline: rgba(0, 0, 0, 0.65) none 0px;"/><image xlink:href="http://192.168.16.249/insight-api/sys/common/download/equipment_top_1705400321118.jpg" preserveAspectRatio="none" width="600" height="150" style="border: 0px none rgba(0, 0, 0, 0.65); color: rgba(0, 0, 0, 0.65); cursor: move; outline: rgba(0, 0, 0, 0.65) none 0px;"/><text font-size="14" fill="rgba(0,0,0,0.85)" text-anchor="middle" font-family="Arial, helvetica, sans-serif" transform="matrix(1,0,0,1,300,0)" xml:space="preserve" style="border: 0px none rgba(0, 0, 0, 0.65); color: rgba(0, 0, 0, 0.65); cursor: move; outline: rgba(0, 0, 0, 0.65) none 0px;"><tspan dy="-0.3em" class="v-line" style="border: 0px none rgba(0, 0, 0, 0.65); color: rgba(0, 0, 0, 0.65); cursor: move; outline: rgba(0, 0, 0, 0.65) none 0px;">面板</tspan></text></g><g data-cell-id="a653bf55-11b1-48a5-b779-12b24b8da4cb" data-shape="panel-element" class="x6-cell x6-node" transform="translate(-230,-230)" style="border: 0px none rgba(0, 0, 0, 0.65); color: rgba(0, 0, 0, 0.65); cursor: move; outline: rgba(0, 0, 0, 0.65) none 0px;"><rect fill="none" stroke="none" width="32" height="32" style="border: 0px none rgba(0, 0, 0, 0.65); color: rgba(0, 0, 0, 0.65); cursor: move; outline: rgba(0, 0, 0, 0.65) none 0px;"/><foreignObject width="32" height="32" style="border: 0px none rgba(0, 0, 0, 0.65); color: rgba(0, 0, 0, 0.65); cursor: move; outline: rgba(0, 0, 0, 0.65) none 0px; overflow: visible;"><body xmlns="http://www.w3.org/1999/xhtml" style="width: 100%; height: 100%; background: transparent; border: 0px none rgba(0, 0, 0, 0.65); color: rgba(0, 0, 0, 0.65); cursor: move; margin: 0px; outline: rgba(0, 0, 0, 0.65) none 0px; overflow: auto;"><div data-v-35f75068="" class="icon-box" style="border: 0px none rgba(0, 0, 0, 0.65); color: rgba(0, 0, 0, 0.65); cursor: move; height: 32px; outline: rgba(0, 0, 0, 0.65) none 0px; overflow: hidden;"><i data-v-35f75068="" class="anticon" style="color: rgb(140, 140, 140); transform: rotate(0deg); cursor: move; display: inline-block; height: 32px; width: 32px;"><svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" fill="currentColor" viewBox="0 0 1024 1024" aria-hidden="true" focusable="false" class="" style="cursor: move; display: inline-block;"><path d="M0 64l0 832 960 0L960 64 0 64zM896 832 64 832 64 128l832 0L896 832zM832 384l-128 0L704 256l-64 0L640 192 320 192l0 64L256 256l0 128L128 384l0 384 704 0L832 384zM768 704 192 704 192 448l64 0 64 0L320 384 320 320l64 0L384 256l192 0 0 64 64 0 0 64 0 64 64 0 64 0L768 704zM256 512l64 0 0 128L256 640 256 512zM384 512l64 0 0 128L384 640 384 512zM512 512l64 0 0 128L512 640 512 512zM640 512l64 0 0 128-64 0L640 512z" p-id="1462" style="transform: translate(0.5em, 0.5em); cursor: move;"/></svg></i></div></body></foreignObject><text font-size="14" fill="rgba(0,0,0,0.85)" text-anchor="middle" transform="matrix(1,0,0,1,16,32)" xml:space="preserve" style="border: 0px none rgba(0, 0, 0, 0.65); color: rgba(0, 0, 0, 0.65); cursor: move; outline: rgba(0, 0, 0, 0.65) none 0px;"><tspan dy="0.8em" class="v-line" style="border: 0px none rgba(0, 0, 0, 0.65); color: rgba(0, 0, 0, 0.65); cursor: move; outline: rgba(0, 0, 0, 0.65) none 0px;">RJ45</tspan></text></g></g><g class="x6-graph-svg-decorator" style="border: 0px none rgba(0, 0, 0, 0.65); color: rgba(0, 0, 0, 0.65); cursor: grab; outline: rgba(0, 0, 0, 0.65) none 0px;"/><g class="x6-graph-svg-overlay" style="border: 0px none rgba(0, 0, 0, 0.65); color: rgba(0, 0, 0, 0.65); cursor: grab; outline: rgba(0, 0, 0, 0.65) none 0px;"/></g></svg>