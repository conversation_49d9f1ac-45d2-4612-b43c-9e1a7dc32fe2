<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll zhl zhll">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0', paddingTop: '24px' }">
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery">
            <a-row :gutter="24" ref="row">
              <a-col :xxl="6" :xl="8" :lg="10" :md="12" :sm="24">
                <a-form-item label="流程名称">
                  <a-input placeholder="请输入流程名称" v-model="queryParam.name"></a-input>
                </a-form-item>
              </a-col>
              <a-col :xxl="6" :xl="8" :lg="10" :md="12" :sm="24">
                <span class="table-page-search-submitButtons" :style="{ overflow: 'hidden' } || {}">
                  <a-button type="primary" class="btn-search btn-search-style" @click="searchQuery">查询</a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
        <div class="table-operator">
          <a-button @click="addTopo">新增</a-button>
        </div>
      </a-card>
      <a-card
        :bordered="false"
        :bodyStyle="{ padding: '0' }"
        style="width: 100%; flex: auto; background-color: rgba(255, 255, 255, 0)"
      >
        <!-- table区域-begin -->
        <div class="topo-list-div">
          <a-row :gutter="16" type="flex" align="middle">
            <a-col v-for="topoInfo in dataSource" :key="topoInfo.id" v-bind="CardColLayout" style="margin-bottom: 20px">
              <a-card class="topo-card" style="box-shadow: 0 3px 7px -1px rgb(0 0 0 / 16%)">
                <a slot="extra" href="#" style="margin: 0px">
                </a>
                <span class="span-toponame" @click="editName(topoInfo)">{{ topoInfo.name }}</span>
                <div
                  class="hover"
                  :style="{ height: '200px' }"
                  v-if="!editFlag && topoInfo.topoSvg && JSON.parse(topoInfo.topoDataJson).cells.length > 0"
                  v-html="topoInfo.topoSvg"
                  @click="showTopo(topoInfo)"
                ></div>
                <div
                  class="hover"
                  :style="{ height: '200px', display: 'flex', 'justify-content': 'center', 'align-items': 'center' }"
                  v-else
                >
                  <div class="ant-empty ant-empty-normal">
                    <div class="ant-empty-image">
                      <svg width="64" height="41" viewBox="0 0 64 41" xmlns="http://www.w3.org/2000/svg">
                        <g transform="translate(0 1)" fill="none" fill-rule="evenodd">
                          <ellipse fill="#F5F5F5" cx="32" cy="33" rx="32" ry="7" />
                          <g fill-rule="nonzero" stroke="#D9D9D9">
                            <path
                              d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"
                            />
                            <path
                              d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z"
                              fill="#FAFAFA"
                            />
                          </g>
                        </g>
                      </svg>
                    </div>
                    <p class="ant-empty-description">暂无数据</p>
                  </div>
                </div>
                <template slot="actions" class="ant-card-actions">
                  <span @click="editTopo(topoInfo, imgUrlList)">
                    <a-icon type="edit" style="margin-right: 8px" />配置
                  </span>
                  <span @click="deleteTopo(topoInfo.id)"> <a-icon type="delete" style="margin-right: 8px" />删除 </span>
                </template>
              </a-card>
            </a-col>
          </a-row>
          <a-modal :title="title" :visible="visible" @ok="handleOk" @cancel="visible = !visible">
            <a-form>
            	<a-col :span="24">
              	<a-form-item :required="true" label="流程名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              	  <a-input v-model="chainName" placeholder="流程名称" :maxLength="20"/>
              	</a-form-item>
            	</a-col>
            	<a-col :span="24">
              	<a-form-item :required="true" label="流程标识" :labelCol="labelCol" :wrapperCol="wrapperCol">
              	  <a-input v-model="chainId" placeholder="流程标识" :maxLength="20"/>
              	</a-form-item>
            	</a-col>
            	<a-col :span="24">
              	<a-form-item :required="true" label="流程说明" :labelCol="labelCol" :wrapperCol="wrapperCol">
              	  <a-input v-model="chainDesc" placeholder="流程说明" :maxLength="50"/>
              	</a-form-item>
            	</a-col>
            	<a-col :span="24">
              	<a-form-item :required="true" label="流程协议" :labelCol="labelCol" :wrapperCol="wrapperCol">
              	  <a-select v-model="protocol" placeholder="流程协议" :maxLength="20">
										<a-select-option v-for="item in protocolList" :key="item.id" :value="item.id">{{item.name}}</a-select-option>
									</a-select>
              	</a-form-item>
            	</a-col>
            	<a-col :span="24">
              	<a-form-item :required="true" label="产品" :labelCol="labelCol" :wrapperCol="wrapperCol">
              	  <a-select v-model="productId" placeholder="产品" :maxLength="20">
										<a-select-option v-for="item in ProductList" :key="item.productId" :value="item.productId">{{item.productName}}</a-select-option>
									</a-select>
              	</a-form-item>
            	</a-col>
            </a-form>
          </a-modal>
        </div>
        <div class="pagination-div">
          <a-pagination
            show-quick-jumper
            show-size-changer
            :hideOnSinglePage="false"
            :default-current="ipagination.current"
            :total="ipagination.total"
            @change="onChange"
            :page-size="ipagination.pageSize"
            :pageSizeOptions="ipagination.pageSizeOptions"
            :show-total="(total) => `共 ${ipagination.total} 条`"
            @showSizeChange="onShowSizeChange"
            size="small"
          ></a-pagination>
        </div>
      </a-card>
      <!-- <topo-view ref="topoView" @close="closeModal"></topo-view> -->
      <!-- <topo-edit ref="topoEdit" @ok="modalFormOk" @close="closeModal"></topo-edit> -->
    </a-col>
  </a-row>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { getAction, postAction, putAction, deleteAction } from '@/api/manage'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { Modal } from 'ant-design-vue';
/*import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'*/
export default {
  name: 'app',
  mixins: [JeecgListMixin],
  components: {
  },
  data() {
    return {
      CardColLayout: {
        xl: { span: 6 },
        lg: { span: 6 },
        md: { span: 8 },
        sm: { span: 12 },
        xs: { span: 24 },
      },
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      editFlag: false,
      queryParam: {
        chainName:'',
        chainId:'',
        chainDesc:'',
        protocol:'',
      },
      visible: false,
      title: '新增',
      topoId: '',
      chainName: '',
      chainDesc:'',
      protocol:'',
      productId:'',
      chainId:'',
      dataSource: [],
			protocolList:[],
			ProductList:[],
      ipagination: {
        current: 1,
        pageSize: 8,
        pageSizeOptions: ['8', '16', '24'],
        showTotal: (total, range) => {
          return range[0] + '-' + range[1] + ' 共' + total + '条'
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0,
      },
      url: {
        list: '/flow/chain/list',
        delete: '/topo/topoInfo/delete',
        add: '/flow/chain/add',
				protocolList:'/product/product/queryTransferProtocols',
				ProductList:'/flow/chain/queryProduct',
        edit: '/flow/chain/edit',
        deleteBatch: '/topo/topoInfo/deleteBatch',
        exportXlsUrl: '/topo/topoInfo/exportXls',
        importExcelUrl: 'topo/topoInfo/importExcel',
      },
      // 表头
      columns: [
        {
          title: '拓扑名称',
          align: 'center',
          dataIndex: 'name',
        },
      ],
      hoverHeight: '0px',
      clientHeight: '',
      imgUrlList: [], //拓扑背景图片列表
    }
  },
  mounted() {
    this.getTopoImgList()
    this.clientHeight = `${document.documentElement.clientHeight}` //获取浏览器可视区域高度
    let that = this
    window.onresize = function () {
      that.clientHeight = `${document.documentElement.clientHeight}`
    }
  },
  methods: {
    modalFormOk() {
      this.editFlag = false
      // 新增/修改 成功时，重载列表
      this.loadData()
    },
    closeModal() {
      this.editFlag = false
    },
    loadData(arg) {
      if (!this.url.list) {
        this.$message.error('请设置url.list属性!')
        return
      }
      if (arg === 1) {
        this.ipagination.current = 1
      }
      var params = this.getQueryParams() //查询条件
      this.loading = true
      getAction(this.url.list, params).then((res) => {
        if (res.success) {
          this.imgUrlList = res.result.imgUrlList;
          this.dataSource = res.result.records
          this.ipagination.total = res.result.total
          setTimeout(() => {
            let hoverEl = document.getElementsByClassName('hover');
            if(hoverEl && hoverEl[0]){
              this.hoverHeight = (hoverEl[0].clientWidth / 5) * 3 + 'px'
            }

          }, 50)
        }
        if (res.code === 510) {
          this.$message.warning(res.message)
        }
        this.loading = false
      })
    },
    topobgimg(e, img) {},
    editTopoShow(id, showType) {
      let type = showType === '1' ? '0' : '1'
      let content = showType === '1' ? '确定不在数据中心展示?' : '是否在数据中心展示?'
      var that = this
      this.$confirm({
        title: ' 提示',
        okText: '确认',
        cancelText: '取消',
        content: content,
        onOk: function () {
          that.loading = true
          putAction(that.url.edit, { id: id, showType: type }).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.loadData()
            } else {
              that.$message.warning(res.message)
            }
          })
        },
      })
    },
    onShowSizeChange(current, pageSize) {
      this.ipagination.pageSize = pageSize
      this.ipagination.current = current
      this.loadData()
    },
    onChange(pageNumber, pageSize) {
      this.ipagination.pageSize = pageSize
      this.ipagination.current = pageNumber
      this.tabShow = false
      this.loadData()
    },
		getProtocolList(){
			getAction(this.url.protocolList).then((res) => {
				if(res.success){
				this.protocolList = res.result
				}
			})
		},
		getProductList(){
			getAction(this.url.ProductList).then((res) => {
				if(res.success){
				this.ProductList = res.result
				}
			})
		},
    getTopoImgList() {
      getAction('/product/topoImg/getPruductImgList').then((res) => {
        this.$store.state.topo.productTopoImg = res.result
      })
    },
    handleOk() {
      if (this.chainName == '') {
        this.$message.warning('请输入流程名称!')
        return
      } else if(this.chainId == ''){
        this.$message.warning('请输入流程标识!')
        return
      } else if(this.chainDesc == ''){
        this.$message.warning('请输入流程说明!')
        return
      } else if(this.protocol == ''){
        this.$message.warning('请选择流程协议!')
        return
      } else if(this.productId == ''){
        this.$message.warning('请选择产品!')
        return
      } else {
        if (this.title == '新增') {
          postAction(this.url.add, {
                chainName:this.chainName,
                chainId:this.chainId,
                chainDesc:this.chainDesc,
                protocol:this.protocol,
                productId:this.productId }).then((res) => {
            if (res.code == 200) {
              this.loadData()
              this.visible = false
            } else {
              this.$message.warning(res.message)
            }
          })
        } else if (this.title == '修改') {
          putAction(this.url.edit, { id: this.topoId, name: this.chainName }).then((res) => {
            if (res.code == 200) {
              this.loadData()
              this.visible = false
              this.topoId = ''
            } else {
              this.$message.warning(res.message)
            }
          })
        } else {
          this.visible = false
        }
        this.chainName = ''
      }
    },
    addTopo() {
      this.visible = true
      this.title = '新增'
			this.getProtocolList()
			this.getProductList()
    },
    changeTopoName() {
      this.visible = true
      this.title = '修改'
    },
    showTopo(record) {
      this.editFlag = true
    //   this.$refs.topoView.show(record)
    },
    editTopo(record, imgList) {
      this.editFlag = true
      this.$refs.topoEdit.edit(Object.assign({}, record), imgList)
    },
    deleteTopo(record) {
      var that = this
      this.$confirm({
        title: '确认删除',
        okText: '是',
        cancelText: '否',
        content: '是否删除数据?',
        onOk: function () {
          that.loading = true
          deleteAction('/flow/chain/delete', { id: record }).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.loadData()
            } else {
              that.$message.warning(res.message)
            }
          })
        },
      })
    },
    editName(record) {
      this.visible = true
      this.title = '修改'
      this.chainName = record.name
      this.topoId = record.id
    },
  },
}
</script>

<style lang="less" scoped>
@import '~@assets/less/common.less';
.table-container {
  background-color: #fff;
}

.table-operator {
  margin-bottom: 10px;
}

.ant-table-tbody .ant-table-row td {
  padding-top: 15px;
  padding-bottom: 15px;
}

.anty-row-operator button {
  margin: 0 5px;
}

.ant-modal-cust-warp .ant-modal-body {
  height: calc(100% - 110px) !important;
  overflow-y: auto;
}

.ant-modal-cust-warp .ant-modal-content {
  height: 90% !important;
  overflow-y: hidden;
}

.hover {
  cursor: pointer;
  width: 100%;
  height: 100%;
  margin: auto;
  border: 1px solid #e8e8e8;
  position: relative;
}
.topobgimg {
  background: url('/assets/topobgImage/bgcolor-gray.png');
}
.topobgcolor {
  background-color: #f3f3f3;
}
::v-deep .hover > svg {
  width: 100% !important;
  height: 100% !important;
}
::v-deep .hover > svg > g {
  transform: matrix(1, 0, 0, 1, 1, 1) !important;
}
.pagination-div {
  display: flex;
  flex-direction: row-reverse;
  padding-bottom: 20px;
}
::v-deep .ant-card-head-title {
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
}
::v-deep .ant-card-extra {
  clear: both;
  margin: 0px;
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  padding: 12px 0 !important;
}
::v-deep .ant-card-head {
  border-bottom: 0px solid #e8e8e8;
}
::v-deep .ant-card-actions {
  border-top: 0px solid #e8e8e8;
}
::v-deep .ant-card-bordered {
  width: 100%;
  position: relative;
  .ant-card-head-wrapper {
    justify-content: flex-end;
  }
}
::v-deep .ant-card-body {
  padding: 0px 22px 16px;
  width: 100%;
  margin: 0px;
}
.span-toponame {
  cursor: pointer;
  position: absolute;
  top: 14px;
}
</style>
