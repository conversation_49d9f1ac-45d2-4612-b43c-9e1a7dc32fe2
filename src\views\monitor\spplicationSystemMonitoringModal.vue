<template>
  <a-row :gutter='10' style='height: 100%;' class='vScroll'>
    <a-col style='width:100%;height: 100%;display: flex;flex-direction: column'>
<!--      <a-button @click='colseCard'></a-button>-->
      <!-- 查询区域 -->
      <a-card
        :bordered='false'
        style='height: 120px; margin-bottom: 10px'
      >
        <div class='header-title'>
          <span>{{ cardDataList.appName }}</span>
          <img
            @click='colseCard'
            src='~@/assets/return1.png'
            alt=''
            style='width: 20px; height: 20px; cursor: pointer'
          />
        </div>
        <div class='header-oddNumber'>
          <span>更新时间：{{ cardDataList.updated }} </span>
          <div>
            <a-button
              style='margin-left: 16px'
              @click='exportDetails'
            >导出详情
            </a-button>
          </div>
        </div>
      </a-card>
      <!-- 查询区域-END -->
      <a-card :bordered='false' style='width:100%;flex: auto'>
        <div class='essential-title'>基本信息</div>
        <div class='essentialInformation'>
          <div class='essentialInformation-item'>
            <span>系统名称</span>
            <span> {{ cardDataList.appName }} </span>
          </div>
          <div class='essentialInformation-item'>
            <span>使用单位</span>
            <span> {{ cardDataList.deptName }} </span>
          </div>
          <div class='essentialInformation-item'>
            <span>访问地址</span>
            <span> {{ cardDataList.appUrl }} </span>
          </div>
          <div class='essentialInformation-item'>
            <span>更新时间</span>
            <span>{{ cardDataList.updated }}</span>
          </div>
        </div>
        <div class='monitoring-title'>监控情况</div>
        <div class='monitoringSituation'>
          <a-table
            ref='table'
            bordered
            :row-key='(record,index)=>{return record.id}'
            :columns='columns'
            :dataSource='dataSource'
            :scroll='dataSource.length > 0 ? { x:"max-content"} : {}'
            :pagination='ipagination'
            :loading='loading'
            @change='handleTableChange'
          >
            <template slot='tooltip' slot-scope='text'>
              <a-tooltip placement='topLeft' :title='text' trigger='hover'>
                <div class='tooltip'>
                  {{ text }}
                </div>
              </a-tooltip>
            </template>
          </a-table>
        </div>
      </a-card>
    </a-col>
  </a-row>
</template>

<script>
import { getAction } from '@/api/manage'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
export default {
  name: 'spplicationSystemMonitoringaModal',
  mixins: [JeecgListMixin],
  data() {
    return {
      columns: [
        {
          title: '登录次数',
          dataIndex: 'loginNum',
          customCell: () => {
            let cellStyle = 'text-align: right;min-width: 100px;max-width:300px'
            return { style: cellStyle }
          },
        },
        {
          title: '登录时长(h)',
          dataIndex: 'loginTime',
          customCell: () => {
            let cellStyle = 'text-align: right;min-width: 100px;max-width:300px'
            return { style: cellStyle }
          },
        },
        {
          title: '活跃用户量',
          dataIndex: 'user',
          customCell: () => {
            let cellStyle = 'text-align: right;min-width: 100px;max-width:300px'
            return { style: cellStyle }
          },
        },
        {
          title: '操作频率（次/h）',
          dataIndex: 'frequency',
          customCell: () => {
            let cellStyle = 'text-align: right;min-width: 100px;max-width:300px'
            return { style: cellStyle }
          },
        },
        {
          title: '更新时间',
          dataIndex: 'upTime',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 100px;max-width:300px'
            return { style: cellStyle }
          },
        }
      ],
      url:{
        list:'/itilSyAppData/itilSyAppData/list'
      },
      cardDataList: {},
      uptime: '',
      sysId:'',
      disableMixinCreated:true
    }
  },
  props: {
    data: {

    }
  },
  watch:{
    data(){
      this.initCard()
    }
  },
  mounted() {
    this.initCard()
  },
  methods: {
    initCard() {
      this.ipagination.current=1
      this.ipagination.pageSize=10
      this.cardDataList={}
      this.uptime=''
      this.dataSource=[]
      this.sysId=this.data
      this.queryParam.id=this.data
      this.loadData()
      this.cardList()
    },
    cardList() {
      getAction('/open/ops/getById', { id: this.sysId }).then((res) => {
        if (res.success) {
          this.cardDataList = res.result
          this.uptime = res.result.updated
        }
      })
    },
    loadData(arg) {
      if (!this.url.list) {
        this.$message.error('请设置url.list属性!')
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1
      }
      var params = this.getQueryParams() //查询条件
      this.loading = true
      getAction(this.url.list, params).then((res) => {
        if (res.success) {
          //update-begin---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
          this.dataSource = res.result.records || res.result
          if(this.dataSource.length>0){
            this.uptime=this.dataSource[0].upTime
          }
          if (this.dataSource.length < 9) {
            this.clientHeight = false
          }
          //author:weng    Date:20210402  for：if(res.result.total>0) 有错误，无查询结果时，页码显示有问题
          this.ipagination.total = res.result.total
          // if(res.result.total>0)
          // {
          //   this.ipagination.total = res.result.total;
          // }
          //update-end---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
        }
        if (res.code === 510) {
          this.$message.warning(res.message)
        }
        this.loading = false
      })
    },
    exportDetails() {
      getAction('/open/ops/syAppExportXls', { id: this.sysId }).then((res) => {
        if (res.success) {
          window.open(window._CONFIG['domianURL'] + '/sys/common/downloadFile/' + res.result)
        }
      })
    },
    colseCard() {
      this.$parent.pButton2(0)
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import "~@assets/less/scroll.less";

.header-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-family: PingFangSC-Medium;
  font-size: 18px;
  color: #000000;
}

.header-oddNumber {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 20px;

  span {
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
  }

  div {
    button {
      margin-bottom: 0;
    }

    button:nth-child(2) {
      margin-right: 0px;
    }
  }
}

.essential-title {
  font-family: PingFangSC-Medium;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.85);
  border-left: 4px solid #1e3674;
  padding-left: 8px;
}

.essentialInformation {
  margin-top: 20px;
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  border: 1px solid #e8e8e8;
  border-bottom: none;

  .essentialInformation-item {
    width: 50%;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #e8e8e8;

    span {
      display: flex;
      align-items: center;
      height: 40px;
      border-right: 1px solid #e8e8e8;
    }

    span:nth-child(1) {
      width: 30%;
      justify-content: center;
      background: #fafafa;
    }

    span:nth-child(2) {
      width: 70%;
      padding-left: 6%;
    }
  }

  div:nth-child(2n + 0) {
    span:nth-child(2) {
      border: none;
    }
  }
}

.monitoring-title {
  font-family: PingFangSC-Medium;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.85);
  border-left: 4px solid #1e3674;
  margin-top: 35px;
  padding-left: 8px;
  margin-bottom: 20px;
}
</style>
