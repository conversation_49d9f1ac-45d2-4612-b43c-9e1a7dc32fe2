<template>
  <a-row style='height: 100%'>
    <a-col style='height: 100%; display: flex; flex-direction: column'>
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0', marginRight: '12px' }" class='card-style'>
        <!-- 查询区域 -->
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col v-show="getVisible('processName')" :span="spanValue">
                <a-form-item :label="getTitle('processName')">
                  <a-input :allow-clear='true' :maxLength='maxLength' autocomplete="off" placeholder="请输入所属流程全量内容"
                    v-model="queryParam.processName" />
                </a-form-item>
              </a-col>
              <a-col v-show="getVisible('processType')" :span="spanValue">
                <a-form-item :label="getTitle('processType')">
                  <j-dict-select-tag v-model='queryParam.processType' placeholder='请选择流程类别' dictCode='bpm_process_type'
                    @change='changeCategory' />
                </a-form-item>
              </a-col>
              <a-col v-show="getVisible('createTime')" :span="spanValue">
                <a-form-item :label="getTitle('createTime')">
                  <a-range-picker :getCalendarContainer="node=> node.parentNode" format="YYYY-MM-DD HH:mm:ss" showTime
                    v-model="queryParam.searchCreateTime" :placeholder="['开始时间', '结束时间']" @change="onCreateTimeChange"
                    style="width: 100%" />
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <span class="table-page-search-submitButtons" style="overflow: hidden;">
                  <a-button icon="search" type="primary" @click="searchQuery">查询</a-button>
                  <a-button icon="reload" style="margin-left: 8px" @click="searchReset">重置</a-button>
                  <a v-if="queryItems.length>0" style="margin-left: 8px" @click="doToggleSearch">{{queryName}}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
        <!-- 查询区域-END -->

        <!--自定义查询项 -->
        <div v-if="toggleSearchStatus" class="custom-query-item">
          <a-checkbox-group v-model="settingQueryItems" :defaultValue="settingQueryItems" style="width:100%"
            @change="onQuerySettingsChange">
            <a-row :gutter="24">
              <template v-for="(item,index) in queryItems">
                <a-col v-show='item.checked' :span='querySpanValue' class='col-checkbox'>
                  <a-checkbox :disabled="item.disabled" :value="item.dataIndex">
                    <j-ellipsis :length="7" :value="item.title"></j-ellipsis>
                  </a-checkbox>
                </a-col>
              </template>
            </a-row>
          </a-checkbox-group>
        </div>
        <!-- 自定义查询项-END -->
      </a-card>

      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <!-- 操作按钮区域 -->
        <div class="table-operator">
          <a-dropdown v-if="selectedRowKeys.length > 0">
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item key="1" @click="batchDel">删除</a-menu-item>
            </a-menu>
            <a-button type='primary' style="margin-left: 8px"> 批量操作
              <a-icon type="down" />
            </a-button>
          </a-dropdown>
        </div>

        <!-- table区域-begin -->
        <div>
          <!--      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px">
            <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择
            <a style="font-weight: 600">{{ selectedRowKeys.length }}</a
            >项
            <a style="margin-left: 24px" @click="onClearSelected">清空</a>
          </div>-->

          <a-table
            ref="table"
            bordered
            rowKey="id"
            :columns="columns"
            :dataSource="dataSource"
            :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
            :pagination="ipagination"
            :loading="loading"
            :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
            @change="handleTableChange">

            <template slot='tooltip' slot-scope='text'>
              <a-tooltip placement='topLeft'
                :title='text'
                trigger='hover'>
                <div class='tooltip'>
                  {{ text }}
                </div>
              </a-tooltip>
            </template>

            <span slot="action" slot-scope="text, record">
              <!-- 列表页隐藏了提交申请按钮，只能点击编辑按钮在表单里面提交操作 -->
              <!-- <a-popconfirm title="确定提交申请吗?" @confirm="() => handleApply(record)">
                <a>提交申请</a>
              </a-popconfirm>
              <a-divider type="vertical" /> -->
              <a @click="handleEdit(record)">编辑</a>
              <a-divider type="vertical" />
              <a-popconfirm title="确定删除吗?" @confirm="() => handleDeleteOrder(record)">
                <a>删除</a>
              </a-popconfirm>
              <!-- <template v-if="record.status == 0">
                <a-popconfirm title="确定提交申请吗?" @confirm="() => handleApply(record.id)">
                  <a>提交申请</a>
                </a-popconfirm>
                <a-divider type="vertical" />
                <a @click="handleEdit(record)">编辑</a>
                <a-divider type="vertical" />
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </template>
              <template v-else-if="record.status == 1">
                <a-popconfirm title="确定撤回吗?" @confirm="() => cancel(record)">
                  <a>撤回</a>
                </a-popconfirm>
                <a-divider type="vertical" />
                <a href="javascript:void(0);" @click="history(record,'查看进度')">查看进度</a>
                <a-divider type="vertical" />
                <a href="javascript:void(0);" @click="detail(record)">表单数据</a>
              </template>
              <template v-else-if="(record.status == 2 && record.result == 3) || record.status == 3">
                <a-popconfirm title="确定提交申请吗?" @confirm="() => handleApply(record.id)">
                  <a href="javascript:void(0);">重新申请</a>
                </a-popconfirm>
                <a-divider type="vertical" />
                <a href="javascript:void(0);" @click="handleEdit(record)">编辑</a>
                <a-divider type="vertical" />
                <a href="javascript:void(0);" @click="history(record,'审批历史')">审批历史</a>
              </template>
              <template v-else>
                <a href="javascript:void(0);" @click="detail(record)">表单数据</a>
                <a-divider type="vertical" />
                <a href="javascript:void(0);" @click="history(record,'审批历史')">审批历史</a>
              </template> -->
            </span>
          </a-table>
        </div>
      </a-card>

      <business-edit ref="businessEdit" @ok="reload"></business-edit>
    </a-col>
  </a-row>
</template>

<script>
  import '@/assets/less/TableExpand.less'
  import {
    mixinDevice
  } from '@/utils/mixin'
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import {
    filterDictTextByCache
  } from '@/components/dict/JDictSelectUtil'
  import BusinessEdit from './modules/BusinessEdit.vue'
  import {
    postAction,
    deleteAction,
    putAction,
    getAction
  } from '@/api/manage'
  import {
    YqFormSeniorSearchLocation
  } from '@/mixins/YqFormSeniorSearchLocation'

  export default {
    name: 'ActZBusinessList',
    mixins: [JeecgListMixin, mixinDevice, YqFormSeniorSearchLocation],
    components: {
      BusinessEdit,
    },
    data() {
      return {
        maxLength:50,
        formItemLayout: {
          labelCol: {
            style: 'width:80px',
          },
          wrapperCol: {
            style: 'width:calc(100% - 80px)'
          }
        },
        description: '流程业务草稿表管理页面',
        // 表头
        columns: [{
            title: '序号',
            width: 60,
            dataIndex: '',
            key: 'rowIndex',
            isUsed: false,
            customCell: () => {
              let cellStyle = 'text-align:center;'
              return {
                style: cellStyle
              }
            },
            customRender: function (t, r, index) {
              return parseInt(index) + 1
            },
          },
          {
            title: '所属流程',
            dataIndex: 'processName',
            isUsed: true,
            customCell: () => {
              let cellStyle = 'text-align:center;min-width: 120px;max-width: 260px'
              return {
                style: cellStyle
              }
            },
          },
          {
            title: '流程版本',
            dataIndex: 'processDefVersion',
            isUsed: false,
            customRender: (text) => {
              return 'v' + text
            },
            customCell: () => {
              let cellStyle = 'text-align:center;width:100px'
              return {
                style: cellStyle
              }
            },
          },
          {
            title: '流程类型',
            dataIndex: 'processType',
            isUsed: true,
            customCell: () => {
              let cellStyle = 'text-align:center;min-width: 120px;max-width: 260px'
              return {
                style: cellStyle
              }
            },
            customRender: (text) => {
              //字典值替换通用方法
              return filterDictTextByCache('bpm_process_type', text)
            },
          },
          {
            title: '创建时间',
            dataIndex: 'createTime',
            isUsed: true,
            customCell: () => {
              let cellStyle = 'text-align:center;width:200px'
              return {
                style: cellStyle
              }
            },
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            fixed: 'right',
            width: 200,
            isUsed: false,
            scopedSlots: {
              customRender: 'action'
            },
          },
        ],
        url: {
          list: '/business/actZBusiness/list',
          delete: '/business/actZBusiness/delete',
          // deleteBatch: '/business/actZBusiness/deleteBatch',
          deleteBatch: '/alarm/alarmHistory/deleteBusinessBatch',
          exportXlsUrl: '/business/actZBusiness/exportXls',
          importExcelUrl: 'business/actZBusiness/importExcel',
          cancelUrl: '/business/actZBusiness/cancel',
          deleteAlarmOrder:'/alarm/alarmHistory/deleteBusinessAndAlarm',
        },
        dictOptions: {},
      }
    },
    created() {
      this.getColumns(this.columns)
    },
    computed: {
      importExcelUrl: function () {
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
      },
    },
    methods: {
      changeCategory(value) {
        if (!value) {
          this.queryParam.processDefinitionCategory = undefined
        }
      },
      onCreateTimeChange: function (value, dateString) {
        this.queryParam.createTime_begin = dateString[0]
        this.queryParam.createTime_end = dateString[1]
      },
      cancel(record) {
        let cancelForm = {
          id: record.id,
          reason: '我要撤回',
        }
        postAction(this.url.cancelUrl, cancelForm)
          .then((res) => {
            if (res.code == 200) {
              this.$message.success('撤回成功')
            } else {
              this.$message.error('删除失败')
            }
            // this.modalCancelVisible = false
            // this.resetCancleForm();
            this.loadData()
          })
          .finally(() => (this.submitLoading = false))
      },
      history(record, title) {
        if (!record.id) {
          this.$message.error('流程实例ID不存在')
          return
        }
        record.state = record.endTime
        this.$refs.processHistoryModal.init(record.procInstId)
        this.$refs.processHistoryModal.title = title
        this.$refs.processHistoryModal.disableSubmit = false
      },
      reload() {
        this.loadData()
      },
      handleEdit(record) {
        if (!record.id) {
          this.$message.error('申请不存在')
          return
        }
        this.$refs.businessEdit.init(record)
      },
      handleApply(record) {
        let temUrl = '/business/actZBusiness/sbApply'
        let formValue = JSON.parse(record.formValue)
        //工单来源：告警转工单
        if (formValue.alarmId && formValue.alarmId.length > 0) {
          temUrl = '/alarm/alarmHistory/sbApply'
        }
        //工单来源：问题转工单
        if (formValue.questionId && formValue.questionId.length > 0) {
          temUrl = '/question/question/sbApply'
        }
        var _this = this
        getAction('/flowable/processDefinition/queryById', {
          processDefinitionId: record.procDefId
        }).then((res) => {
          if (res.code == 500) {
            _this.$confirm({
              title: '流程定义未找到或已删除，无法提交申请，是否删除该草稿',
              okText: '是',
              cancelText: '否',
              onOk() {
                _this.handleDeleteOrder(record)
              },
              onCancel() {
              },
              class: 'test',
            })
          }
          if (res.code == 200 && res.result.suspended == true) {
            _this.$message.warning('所属流程已被挂起，请联系管理员！')
          }

          if (res.code == 200 && res.result.suspended == false) {
            postAction(temUrl, {
              id: record.id
            }).then((res) => {
              if (res.success) {
                _this.$message.success(res.message)
              } else {
                _this.$message.error(res.message)
              }
              _this.loadData()
            })
          }
        })
      },
      handleDeatails: function (record) {},
      handleDeleteOrder(record){
        let formValue=JSON.parse(record.formValue)
        //工单来源：告警转工单
        if (formValue.alarmId&&formValue.alarmId.length>0){
          if (!this.url.deleteAlarmOrder) {
            this.$message.error('请设置url.deleteAlarmOrder属性!')
            return
          }
          var that = this
          deleteAction(that.url.deleteAlarmOrder, { id: record.id,alarmId:formValue.alarmId}).then((res) => {
            if (res.success) {
              //重新计算分页问题
              that.reCalculatePage(1)
              that.$message.success(res.message)
              that.loadData()
            } else {
              that.$message.warning(res.message)
            }
          })
        }
        else {
          this.handleDelete(record.id)
        }
      }
    }
  }
</script>
<style scoped lang='less'>
  @import '~@assets/less/common.less';
  @import '~@assets/less/YQCommon.less';
  @import '~@assets/less/scroll.less';
</style>
