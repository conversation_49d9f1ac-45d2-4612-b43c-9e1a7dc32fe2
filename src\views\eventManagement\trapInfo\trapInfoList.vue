<template>
  <a-row :gutter='10' style='height: 100%;' class='vScroll'>
    <a-col style='width:100%;height: 100%;display: flex;flex-direction: column'>
      <!-- 查询区域 -->
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0', marginRight: '12px' }" class='card-style'
        style='width: 100%'>
        <div class='table-page-search-wrapper'>
          <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
            <a-row :gutter='24' ref='row'>
              <a-col :span='spanValue'>
                <a-form-item label='设备名称'>
                  <a-input :maxLength='maxLength' placeholder='请输入设备名称' v-model='queryParam.deviceName' :allowClear='true'
                    autocomplete='off' />
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label='IP地址'>
                  <a-input :maxLength='maxLength' placeholder='请输入IP地址' v-model='queryParam.sourceIp' :allowClear='true' autocomplete='off' />
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="创建时间">
                  <a-range-picker style='width: 100%' :getCalendarContainer="node=> node.parentNode"
                    format="YYYY-MM-DD HH:mm:ss" showTime v-model="queryParam.createTimeRange"
                    :placeholder="['开始时间', '结束时间']" @change="onDateChange" />
                </a-form-item>
              </a-col>
              <a-col :span='spanValue' v-show='toggleSearchStatus'>
                <a-form-item label='消息内容'>
                  <a-input :maxLength='maxLength' placeholder='请输入消息内容' v-model='queryParam.trapMessage' :allowClear='true'
                    autocomplete='off' />
                </a-form-item>
              </a-col>
              <a-col :span='colBtnsSpan()'>
                <span class='table-page-search-submitButtons'
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button type='primary' class='btn-search btn-search-style' @click='searchQuery'>查询</a-button>
                  <a-button class='btn-reset btn-reset-style' @click='searchReset'>重置</a-button>
                  <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <a-table ref='table' bordered rowKey="id" :columns='columns' :dataSource='dataSource'
          :scroll='dataSource.length > 0 ? { x: "max-content" } : {}' :pagination='ipagination' :loading='loading'
          @change='handleTableChange'>
          <template slot='tooltip' slot-scope='text'>
            <a-tooltip placement='topLeft' :title='text' trigger='hover'>
              <div class='tooltip'>
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
      </a-card>
    </a-col>
  </a-row>
</template>

<script>
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import {
    YqFormSearchLocation
  } from '@/mixins/YqFormSearchLocation'
  import {
    filterObj
  } from '@/utils/util'
  import {
    getAction
  } from '@/api/manage'
  export default {
    name: 'trapInfoList',
    mixins: [JeecgListMixin, YqFormSearchLocation],

    data() {
      return {
        maxLength:50,
        // 查询条件
        queryParam: {
          deviceName: '',
          sourceIp: '',
          trapMessage: '',
          startTime: '',
          endTime: '',
        },
        // 表头
        columns: [{
            title: '设备名称',
            dataIndex: 'deviceName',
            scopedSlots: {
              customRender: 'tooltip'
            },
            customCell: () => {
              let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '设备标识',
            dataIndex: 'deviceCode',
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 150px;max-width:300px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: 'IP地址',
            dataIndex: 'sourceIp',
            customCell: () => {
              let cellStyle = 'text-align: center;width:200px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '备份创建时间',
            dataIndex: 'createTime',
            customCell: () => {
              let cellStyle = 'text-align: center;width:200px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '消息内容',
            dataIndex: 'trapMessage',
            scopedSlots: {
              customRender: 'tooltip'
            },
            customCell: () => {
              let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
              return {
                style: cellStyle
              }
            }
          }
        ],
        url: {
          list: '/trap/snmp/list',
        }
      }
    },
    methods: {
      getQueryParams() {
        let param = Object.assign({}, this.queryParam, this.isorter)
        param.field = this.getQueryField()
        param.pageNo = this.ipagination.current
        param.pageSize = this.ipagination.pageSize
        delete param.createTimeRange // 时间参数不传递后台
        return filterObj(param)
      },
      onDateChange: function (value, dateString) {
        this.queryParam.startTime = dateString[0]
        this.queryParam.endTime = dateString[1]
        console.log(this.queryParam);
      },
    }
  }
</script>
<style lang='less' scoped>
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';
</style>