<template>
  <div style="height:100%;">
    <keep-alive exclude='appointmentApprovalDetails'>
      <component :is="pageName" style="height:100%" :data="data"/>
    </keep-alive>
  </div>
</template>
<script>
import appointmentApprovalList from './appointmentApprovalList'
import appointmentApprovalDetails from './appointmentApprovalDetails'
export default {
  name: "appointmentApprovalManage",
  data() {
    return {
      isActive: 0,
      data:{}
    };
  },
  components: {
    appointmentApprovalList,
    appointmentApprovalDetails
  },
  created(){
    this.pButton1(0);
  },
  //使用计算属性
  computed: {
    pageName() {
      switch (this.isActive) {
        case 0:
          return "appointmentApprovalList";
          break;

        default:
          return "appointmentApprovalDetails";
          break;
      }
    }
  },
  methods: {
    pButton1(index) {
      this.isActive = index;
    },
    pButton2(index,item) {
      this.isActive = index;
      this.data = item;
    }
  }
}
</script>