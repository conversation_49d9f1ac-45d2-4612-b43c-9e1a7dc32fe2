import { getAction, deleteAction, putAction, postAction, httpAction } from '@/api/manage'
import Vue from 'vue'
import * as url from 'url'

//表单管理
const addForm = (params) => postAction("/flowable/form/save", params);
const editForm = (params) => putAction("/flowable/form/update", params);

//常用语管理
const addLanguage = (params) => postAction("/language/languageManage/add", params);
const ediLanguage = (params) => putAction("/language/languageManage/edit", params);

//模型管理
const module = {
  baseUrl: '/flowable/processDefinition'
  , activate: function (params) {
    return putAction(`${(this.baseUrl)}/activate`, params)
  }
  , suspend: function (params) {
    return putAction(`${(this.baseUrl)}/suspend`, params)
  }
}

//流程实例
const processInstanceActivate = (params) => putAction("/flowable/processInstance/activate", params);
const processInstanceSuspend = (params) => putAction("/flowable/processInstance/suspend", params);

// 开启流程
const processInstance = {
  baseUrl: '/flowable/processInstance',
  start: function (params) {
    return postAction(`${(this.baseUrl)}/start`, params)
  }
}

// 我的待办
export const taskTodoApi = {
  baseUrl: '/flowable/task',
  claim: function (params) {
    return putAction(`${(this.baseUrl)}/claim`, params)
  },
  unclaim: function (params) {
    return putAction(`${(this.baseUrl)}/unclaim`, params)
  },
}

export {
  addForm,
  editForm,
  ediLanguage,
  addLanguage,
  module,
  processInstance,
  processInstanceActivate,
  processInstanceSuspend
}