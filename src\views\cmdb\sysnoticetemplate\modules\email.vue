<template>
  <div>
<!--    <a-col :span='23'>
      <a-form-model-item class='two-words' label='附件' prop='attachments' :rules='validatorRules.attachments'>
        &lt;!&ndash; <journal-manage ref="journalManage" :deviceInfo="record"></journal-manage> &ndash;&gt;
        <div class='clearfix'>
          <j-upload v-model='personalizedObject.attachments' :number='5' @change='changeAttachments'></j-upload>
        </div>
      </a-form-model-item>
    </a-col>-->
    <a-col :span='23'>
      <a-form-model-item class='two-words' label='标题' prop='subject' :rules='validatorRules.subject'>
        <a-input v-model='personalizedObject.subject' :allowClear='true' autocomplete='off' placeholder='请输入标题' @change='changeValue'/>
      </a-form-model-item>
    </a-col>
    <a-col :span='23'>
      <a-form-model-item class='two-words' label='收件人' prop='sendTo' :rules='validatorRules.sendTo'>
        <a-select v-model='personalizedObject.sendTo' :allowClear='true' :getPopupContainer='(node) => node.parentNode'
                  mode='multiple' option-filter-prop='children'
                  placeholder='请选择收件人' show-search @change='changeValue'>
          <a-select-option v-for='item in usersList' :key='item.id' :value='item.id'>
            {{ item.realName }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
    </a-col>
  </div>
</template>

<script>
export default {
  name: 'email',
  props: {
    data: {
      type: Object,
      required: false,
      default: () => {
        let personalizedObject = {
          //attachments: [],
          subject: '',
          sendTo: undefined
        }
        return personalizedObject
      }
    },
    usersList: {
      type: Array,
      required: true,
      default: []
    }
  },
  data() {
    return {
      personalizedObject: {
        //attachments: [],
        subject: '',
        sendTo: undefined
      },
      //attachments:'',
      validatorRules: {
      /*  attachments: [
          { required: false, message: '请选择文件!' }
        ],*/
        subject: [
          { required: true, validator: this.subjectValidate }
        ],
        sendTo: [
          { required: true, validator: this.sendToValidate }
        ]
      }
    }
  },
  watch: {
    data: {
      handler(val) {
        if (Object.keys(val).length > 0) {
          this.personalizedObject = val
         /* if(val.attachments&&val.attachments.length>0){
            this.attachments=val.attachments.join(',')
          }*/
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    changeValue() {
      this.$emit('changeModelValue', this.personalizedObject)
    },
/*    changeAttachments(e){
      this.attachments=e
      this.personalizedObject.attachments=e.length>0?e.split(','):[]
      this.changeValue()
    },*/
    getTips(fullField) {
      let str = ''
      switch (fullField) {
        case 'subject':
          str = '请输入标题！'
          break
        case 'sendTo' :
          str = '请选择收件人！'
          break
      }
      return str
    },
    subjectValidate(rule, value, callback) {
      if (rule.required) {
        if (value && value.length > 0) {
          if(value.length<2||value.length>20){
            callback('标题长度应在 2-20 之间！')
          }
          else {
            callback()
          }
        } else {
          callback(this.getTips(rule.fullField))
        }
      } else {
        callback()
      }
    },
    sendToValidate(rule, value, callback) {
      if (rule.required) {
        if (value && value.length > 0) {
          callback()
        } else {
          callback(this.getTips(rule.fullField))
        }
      } else {
        callback()
      }
    }
  }
}
</script>