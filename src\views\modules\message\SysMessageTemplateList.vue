<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll zxw">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <!-- 查询区域 -->
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="模板编码">
                  <a-input
                    placeholder="请输入"
                    autocomplete="off"
                    :allowClear="true"
                    v-model="queryParam.templateCode"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="模板内容">
                  <a-input
                    placeholder="请输入"
                    autocomplete="off"
                    :allowClear="true"
                    v-model="queryParam.templateContent"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="模板标题">
                  <a-input
                    placeholder="请输入"
                    autocomplete="off"
                    :allowClear="true"
                    v-model="queryParam.templateName"
                  />
                </a-form-item>
              </a-col>
              <!-- <a-col v-show="toggleSearchStatus" :span="spanValue">
                <a-form-item label="模板类型">
                  <a-input placeholder="请输入" autocomplete="off" v-model="queryParam.templateType"></a-input>
                </a-form-item>
              </a-col> -->
              <a-col v-show="toggleSearchStatus" :span="spanValue">
                <a-form-item label="模板类型:">
                  <j-dict-select-tag
                    :allowClear="true"
                    v-model="queryParam.templateType"
                    dictCode="template_type"
                    placeholder="请选择模板类型"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="colBtnsSpan()">
                <span
                  class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                >
                  <a-button type="primary" @click="searchQuery" class="btn-search-style">查询</a-button>
                  <a-button @click="searchReset" class="btn-reset-style" style="margin-left: 8px">重置</a-button>
                  <a @click="doToggleSearch" class="btn-updown-style">
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered="false" style='flex: auto'>
        <!-- 操作按钮区域 -->
        <div class="table-operator tableBottom">
          <a-button @click="handleAdd">新增</a-button>
          <a-button @click="handleExportXls('消息模板')">导出</a-button>
          <a-button @click="handleTemplateXls()">下载模版</a-button>
          <a-upload
            name="file"
            :showUploadList="false"
            :multiple="false"
            :headers="tokenHeader"
            :action="importExcelUrl"
            @change="handleImportExcel"
          >
            <a-button>导入</a-button>
          </a-upload>
          <a-dropdown v-if="selectedRowKeys.length > 0">
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item key="1" @click="batchDel">删除</a-menu-item>
            </a-menu>
            <a-button>
              批量操作
              <a-icon type="down" />
            </a-button>
          </a-dropdown>
        </div>
        <!-- table区域-begin -->
        <div>
          <a-table
            ref="table"
            bordered
            rowKey="id"
            :columns="columns"
            :dataSource="dataSource"
            :pagination="ipagination"
            :loading="loading"
            :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
            @change="handleTableChange">
            <template slot='templateContent' slot-scope='text'>
              <a-tooltip placement='topLeft' overlayClassName='platformTableTooltip'>
                <template>
                  <div slot='title' v-html='text' id="textHtml"></div>
                </template>
                <div>
                  <div class='tooltip'>{{text}}</div>
                </div>
              </a-tooltip>
            </template>

            <span
              slot="action"
              slot-scope="text, record"
              class="caozuo"
              style="display: inline-block; white-space: nowrap; text-align: center"
            >
              <a @click="handleTest(record)">发送测试</a>
              <a-divider type="vertical" />
              <a @click="handleEdit(record)">编辑</a>
              <a-divider type="vertical" />
              <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)"> <a>删除</a> </a-popconfirm>
            </span>
            <template slot="tooltip" slot-scope="text">
              <a-tooltip placement="topLeft" :title="text" trigger="hover">
                <div class="tooltip">
                  {{ text }}
                </div>
              </a-tooltip>
            </template>
          </a-table>
        </div>
        <!-- table区域-end -->

        <!-- 表单区域 -->
        <sysMessageTemplate-modal ref="modalForm" @ok="modalFormOk"></sysMessageTemplate-modal>

        <sysMessageTest-modal ref="testModal"></sysMessageTest-modal>
      </a-card>
      <iframe id="download" style="display: none"></iframe>
    </a-col>
  </a-row>
</template>

<script>
import SysMessageTemplateModal from './modules/SysMessageTemplateModal'
import SysMessageTestModal from './modules/SysMessageTestModal'
import {JeecgListMixin} from '@/mixins/JeecgListMixin'
import JEllipsis from '@/components/jeecg/JEllipsis'
import {YqFormSearchLocation} from '@/mixins/YqFormSearchLocation'
import { setImgAllPath } from '@/utils/imagePathAboutTinymce'
import { getAction } from '@api/manage'
export default {
  name: 'SysMessageTemplateList',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components: {
    JEllipsis,
    SysMessageTemplateModal,
    SysMessageTestModal,
  },
  data() {
    return {
      description: '消息模板管理页面',
      // 表头
      columns: [
        {
          title: '模板编码',
          dataIndex: 'templateCode',
        },
        {
          title: '模板标题',
          dataIndex: 'templateName',
        },
        {
          title: '模板内容',
          dataIndex: 'templateContent',
          scopedSlots: { customRender: 'templateContent' },
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 200px;max-width:300px'
            return { style: cellStyle }
          }
        },
        {
          title: '模板类型',
          dataIndex: 'templateType_dictText',
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 200,
          scopedSlots: { customRender: 'action' },
        },
      ],
      url: {
        list: '/sys/message/sysMessageTemplate/list',
        delete: '/sys/message/sysMessageTemplate/delete',
        deleteBatch: '/sys/message/sysMessageTemplate/deleteBatch',
        exportXlsUrl: 'sys/message/sysMessageTemplate/exportXls',
        importExcelUrl: 'sys/message/sysMessageTemplate/importExcel',
        downloadTemplateXlsUrl: 'sys/message/sysMessageTemplate/downloadTemplate',
      },
    }
  },
  mounted() {},
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
    downloadTemplateXlsUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.downloadTemplateXlsUrl}`
    },
  },
  methods: {
    handleTemplateXls() {
      document.getElementById('download').src = this.downloadTemplateXlsUrl
    },
    handleTest(record) {
      this.$refs.testModal.open(record)
      this.$refs.testModal.title = '发送测试'
    },
    loadData(arg) {
      if (!this.url.list) {
        this.$message.error('请设置url.list属性!')
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1
      }

      var params = this.getQueryParams() //查询条件
      this.loading = true
      getAction(this.url.list, params).then((res) => {
        if (res.success) {
          //update-begin---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
          this.dataSource = res.result.records || res.result
          if (this.dataSource.length>0){
            for (let i=0;i<this.dataSource.length;i++){
              let template=this.dataSource[i].templateContent
              if (template&&template.length>0){
                let content=setImgAllPath(template)
                this.dataSource[i].templateContent=content
              }
            }
          }
          if (this.dataSource.length < 9) {
            this.clientHeight = false
          }
          //author:weng    Date:20210402  for：if(res.result.total>0) 有错误，无查询结果时，页码显示有问题
          this.ipagination.total =res.result.total?res.result.total:0
          //update-end---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
        }
        if (res.code === 510) {
          this.$message.warning(res.message)
        }
        this.loading = false
      })
    },
  },
}
</script>
<style lang="less" scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
/** Button按钮间距 */
.ant-btn {
  margin-left: 3px;
}
.ant-card-body .table-operator {
  margin-bottom: 8px;
}

.ant-table-tbody .ant-table-row td {
  padding-top: 15px;
  padding-bottom: 15px;
}

.anty-row-operator button {
  margin: 0 5px;
}

.ant-btn-danger {
  background-color: #ffffff;
}

/*.ant-modal-cust-warp {
  height: 100%;
}

.ant-modal-cust-warp .ant-modal-body {
  height: calc(100% - 110px) !important;
  overflow-y: auto;
}

.ant-modal-cust-warp .ant-modal-content {
  height: 90% !important;
  overflow-y: hidden;
}
/deep/ .ant-form-item-label {
  width: 78px !important;
  padding: 0 !important;
}*/
/*表头样式*/
::v-deep .ant-table-thead > tr > th {
  text-align: center;
  white-space: nowrap;
}

/*内容对齐方式、省略显示*/
::v-deep .ant-table-tbody > tr > td {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;

  &:first-child,
  &:nth-child(5) {
    text-align: center;
  }

  &:nth-child(2),
  &:nth-child(3),
  &:nth-child(4) {
    text-align: left;
  }
}
</style>