<template>
  <card-frame :title='"告警数量统计"' :sub-title='""'>
    <div slot='bodySlot' style="height: 100%;width: 100%;">
      <div class="daySelect">
        <div>
          <span :class="buttonStyle" @click="timeChange" v-show="!monthJudge">近7天</span>
          <span :class="buttonStyle1" @click="timeChange1" v-show="!monthJudge" style="margin-left: 20px;">近30天</span>
          <span :class="buttonStyle2" @click="timeChange2" v-show="monthJudge">近半年</span>
          <span :class="buttonStyle3" @click="timeChange3" v-show="monthJudge" style="margin-left: 20px;">近一年</span>
        </div>
        <div style="margin-right:15px;color: rgb(209, 206, 206)">
          <span style="letter-spacing: 1px;margin-right: 15px;">单位:</span>
          <span :class="dayStyle" @click="dayChange">天</span>
          <span :class="monthStyle" @click="monthChange" style="margin-left: 20px;">月</span>
        </div>
      </div>
      <div style="height: calc(100% - 40px);width: 100%;" id="warningSituationHistogram"></div>
    </div>
  </card-frame>
</template>
<script>
  import echarts from 'echarts/lib/echarts'
  import cardFrame from '@views/statsCenter/com/cardFrame.vue'
  import {
    getAction
  } from '@/api/manage'
  export default {
    components: {
      cardFrame
    },
    data() {
      return {
        monthJudge: false,
        buttonStyle: 'button_style',
        buttonStyle1: 'button_style',
        buttonStyle2: 'button_style',
        buttonStyle3: 'button_style',
        dayStyle: 'button_style',
        monthStyle: 'button_style',
        url: {
          countDays: '/data-analysis/alarm/count/date',
        }
      };
    },
    mounted() {
      this.dayChange()
    },
    methods: {
      dayChange() {
        this.monthJudge = false
        this.dayStyle = 'button_style1'
        this.monthStyle = 'button_style'
        this.timeChange()
      },
      monthChange() {
        this.monthJudge = true
        this.dayStyle = 'button_style'
        this.monthStyle = 'button_style1'
        this.timeChange2()
      },
      timeChange() {
        this.countDays(7)
        this.buttonStyle = 'button_style1'
        this.buttonStyle1 = 'button_style'
        this.buttonStyle2 = 'button_style'
        this.buttonStyle3 = 'button_style'

      },
      timeChange1() {
        this.countDays(30)
        this.buttonStyle = 'button_style'
        this.buttonStyle1 = 'button_style1'
        this.buttonStyle2 = 'button_style'
        this.buttonStyle3 = 'button_style'
      },
      timeChange2() {
        this.countDays2(6)
        this.buttonStyle = 'button_style'
        this.buttonStyle1 = 'button_style'
        this.buttonStyle2 = 'button_style1'
        this.buttonStyle3 = 'button_style'
      },
      timeChange3() {
        this.countDays2(12)
        this.buttonStyle = 'button_style'
        this.buttonStyle1 = 'button_style'
        this.buttonStyle2 = 'button_style'
        this.buttonStyle3 = 'button_style1'
      },
      countDays(day) {
        getAction(this.url.countDays, {
          time: day,
          isDay: 1
        }).then((res) => {
          if (res.code == 200) {
            this.warningSituationHistogram(res.result, res.result.line)
          }
        })
      },
      countDays2(day) {
        getAction(this.url.countDays, {
          time: day,
          isDay: 0
        }).then((res) => {
          if (res.code == 200) {
            this.warningSituationHistogram(res.result, res.result.line)
          }
        })
      },

      // 告警情况统计柱状图
      warningSituationHistogram(data, dataList) {
        let yarr1 = []
        let yarr2 = []
        let xarr = []
        dataList.forEach((e) => {
          yarr1.push(e.value1)
          yarr2.push(e.value2)
          xarr.push(e.name)
        })

        let myChart = this.$echarts.init(document.getElementById('warningSituationHistogram'))
        myChart.setOption({
          legend: {
            data: [data.value1, data.value2],
            left: 'right',
            textStyle: {
              color: '#edf1fc',
              fontSize: 12,
              padding: [0, 10, 0, 0]
            },
            itemWidth: 10,
            itemHeight: 10,
          },
          tooltip: {
            show: true,
            trigger: 'axis',
            transitionDuration: 0, //echart防止tooltip的抖动
          },
          xAxis: [{
            type: 'category',
            data: xarr,
            boundaryGap: true,
            axisLine: {
              lineStyle: {
                //x轴字体颜色
                color: 'rgba(250,250,250,.6)',
              },
            },
            axisLabel: {
              textStyle: {
                color: 'rgba(250,250,250,.6)',
                padding: [5, 0, 0, 0],
                fontSize: 12
              },
            },
            axisTick: {
              show: true,
              alignWithLabel: true,
            },
          }, ],
          yAxis: [{
            type: 'value',
            axisLine: {
              show: false, //y轴线消失
              lineStyle: {
                //y轴字体颜色
                color: 'rgba(250,250,250,.6)',
                fontSize: 12
              },
            },
            axisLabel: {
              textStyle: {
                color: 'rgba(250,250,250,.6)',
                fontSize: 12
              },
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: ['#1c2a37'],
                width: 2,
                type: 'Dashed',
              },
            },
          }, ],
          dataZoom: [{
            id: 'dataZoomY',
            xAxisIndex: [0],
            show: false, //是否显示滑动条，不影响使用
            type: 'slider', // 这个 dataZoom 组件是 slider 型 dataZoom 组件
            startValue: 0, // 从头开始。
            endValue: 12,
            zoomLock: true,
            showDataShadow: false, //是否显示数据阴影 默认auto
            backgroundColor: 'rgba(255,255,255,0)',
            showDetail: false, //即拖拽时候是否显示详细数值信息 默认true
            realtime: true, //是否实时更新
            filterMode: 'filter',
            handleIcon: 'circle',
            handleStyle: {
              color: 'rgba(205,205,205,1)',
              borderColor: 'rgba(205,205,205,1)',
            },
            moveHandleSize: 0,
            brushSelect: false, //刷选功能，设为false可以防止拖动条长度改变 ************（这是一个坑）
          }, {
            type: 'inside',
            xAxisIndex: 0,
            zoomOnMouseWheel: false, //滚轮是否触发缩放
            moveOnMouseMove: true, //鼠标滚轮触发滚动
            moveOnMouseWheel: true
          }],
          grid: {
            top: '12%',
            right: '5%',
            bottom: '12%',
            left: '8%',
          },
          series: [{
              name: data.value1,
              type: 'bar',
              data: yarr1,
              barWidth: 10, //柱图宽度
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                    offset: 0,
                    color: '#C5E0FE'
                  },
                  {
                    offset: 1,
                    color: '#4D91F1'
                  },
                ]),
              },
            },
            {
              name: data.value2,
              type: 'bar',
              data: yarr2,
              barWidth: 10, //柱图宽度
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                    offset: 0,
                    color: '#C5E0FE'
                  },
                  {
                    offset: 1,
                    color: '#19E2A7'
                  },
                ]),
              },
            },
          ],
        })
        window.addEventListener("resize", () => {
          myChart.resize();
        });
      },
    }
  }
</script>

<style scoped lang="less">
  .daySelect {
    height: 40px;
    width: 100%;
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
    font-size: 14px;
  }

  .button_style {
    margin: 0 5px;
    user-select: none;
    cursor: pointer;
    color: rgb(209, 206, 206)
  }

  .button_style1 {
    margin: 0 5px;
    user-select: none;
    cursor: pointer;
    color: #27BDFF;
  }
</style>