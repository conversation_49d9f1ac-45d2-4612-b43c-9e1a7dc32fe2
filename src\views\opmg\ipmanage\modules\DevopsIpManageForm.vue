<template>
  <a-spin :spinning='confirmLoading'>
    <j-form-container :disabled='formDisabled'>
      <a-form slot='detail' :form='form'>
        <a-row>
          <a-col :span='24'>
            <a-form-item :labelCol='labelCol' :wrapperCol='wrapperCol' label='设备名称'>
              <a-input v-decorator="['terminalName', validatorRules.terminalName]" :allowClear='true' autocomplete='off'
                       placeholder='请输入设备名称'></a-input>
            </a-form-item>
          </a-col>
          <a-col :span='24'>
            <a-form-item :labelCol='labelCol' :wrapperCol='wrapperCol' label='IP地址'>
              <a-input v-decorator="['ipAddress', validatorRules.ipAddress]" :allowClear='true' autocomplete='off'
                       placeholder='请输入IP地址'></a-input>
            </a-form-item>
          </a-col>
          <a-col :span='24'>
            <a-form-item :labelCol='labelCol' :wrapperCol='wrapperCol' label='MAC地址'>
              <a-input v-decorator="['macCode', validatorRules.macCode]" :allowClear='true' autocomplete='off'
                       placeholder='请输入MAC地址' @change='changeMAC' />
            </a-form-item>
          </a-col>
          <a-col :span='24'>
            <a-form-item :help='Tips' :labelCol='labelCol' :required='true' :validate-status='validateStatus'
                         :wrapperCol='wrapperCol' label='使用人' style='margin-bottom: 16px'>
              <j-select-user-by-dep :key='refreshKey' v-model='utilizeUserId' :multi='true'
                                    :pUserNames='utilizeUserText' @change='selectUserByDep'>
              </j-select-user-by-dep>
            </a-form-item>
          </a-col>
          <a-col v-if='showFlowSubmitButton' :span='24' style='text-align: center'>
            <a-button @click='submitForm'>提 交</a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
import { getAction, httpAction } from '@/api/manage'
import pick from 'lodash.pick'
import JFormContainer from '@/components/jeecg/JFormContainer'
import JSelectUserByDep from '@/components/jeecgbiz/JSelectUserByDep'
import { duplicateCheck } from '@/api/api'

export default {
  name: 'DevopsIpManageForm',
  components: {
    JFormContainer,
    JSelectUserByDep
  },
  props: {
    //流程表单data
    formData: {
      type: Object,
      default: () => {
      },
      required: false
    },
    //表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false
    },
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  data() {
    return {
      Tips: '',
      validateStatus: 'success',
      refreshKey: 0,
      utilizeUserId: '',
      utilizeUserText: '',
      form: this.$form.createForm(this),
      model: {},
      labelCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 5
        }
      },
      wrapperCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 16
        }
      },
      confirmLoading: false,
      ipManageId: '',
      validatorRules: {
        terminalName: {
          rules: [{
            required: true,
            message: '请输入设备名称!'
          },
            {
              min: 2,
              message: '设备名称长度应在 2-20 之间！',
              trigger: 'blur'
            },
            {
              max: 20,
              message: '设备名称长度应在 2-20 之间！',
              trigger: 'blur'
            },
            {
              validator: this.validateTerminalName
            }
          ]
        },
        /*macCode: {
          rules: [{
            required: true,
            pattern: /^[a-f|\d]{2}:[a-f|\d]{2}:[a-f|\d]{2}:[a-f|\d]{2}:[a-f|\d]{2}:[a-f|\d]{2}$/,
            //pattern: /^[a-fA-F\d]{2}:[a-fA-F\d]{2}:[a-fA-F\d]{2}:[a-fA-F\d]{2}:[a-fA-F\d]{2}:[a-fA-F\d]{2}$|^[a-fA-F\d]{2}-[a-fA-F\d]{2}-[a-fA-F\d]{2}-[a-fA-F\d]{2}-[a-fA-F\d]{2}-[a-fA-F\d]{2}$/,
            //pattern: /^[A-F0-9]{2}(-[A-F0-9]{2}){5}$|^[A-F0-9]{2}(:[A-F0-9]{2}){5}$/,
            message: '要求MAC地址由小写字母、数字和冒号组成，例如：f8:89:d2:7f:b2:c1'
          },
            {
              validator: this.validateMacCode
            }
          ]
        },*/
        macCode: {
          rules: [{
            required: true,
            validator: this.validateMacCode
          }]
        },
        ipAddress: {
          rules: [{
            required: true,
            pattern: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
            message: '请输入正确的IP地址!'
          },
            {
              validator: this.validateIpAddress
            }
          ]
        }
      },
      url: {
        add: '/devopsipmanage/devopsIpManage/add',
        edit: '/devopsipmanage/devopsIpManage/edit',
        queryById: '/devopsipmanage/devopsIpManage/queryById'
      },
      iconShow: '0'
    }
  },
  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    }
  },
  created() {
    //如果是流程中表单，则需要加载流程表单data
    this.showFlowData()
  },
  methods: {
    selectUserByDep(userIds, userNames) {
      this.utilizeUserId = userIds
      this.utilizeUserText = userNames
      this.Tips = ''
      this.validateUserId(userIds)
    },
    add() {
      this.refreshKey++
      this.edit({})
    },
    edit(record) {
      this.refreshKey++
      this.ipManageId = record.id
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.utilizeUserId = this.model.utilizeUserId
      this.utilizeUserText = this.model.utilizeUserText
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(pick(this.model, 'terminalName', 'ipAddress', 'macCode'))
      })
    },
    //校验选择用户
    validateUserId(utilizeUserId) {
      let bo = utilizeUserId != '' && utilizeUserId != undefined && utilizeUserId != null
      this.validateStatus = bo ? 'success' : 'error'
      this.tips = bo ? '' : '请选择用户'
      return bo
    },
    validateTerminalName(rule, value, callback) {
      var params = {
        tableName: 'devops_ip_manage',
        fieldName: 'terminal_name',
        fieldVal: value,
        dataId: this.ipManageId
      }
      duplicateCheck(params).then((res) => {
        if (res.success) {
          callback()
        } else {
          callback('设备名称已存在!')
        }
      })
    },

    validateMacCode(rule, value, callback) {
      if (rule.required) {
        if (value) {
          let regexp = new RegExp(/^[a-f|\d]{2}:[a-f|\d]{2}:[a-f|\d]{2}:[a-f|\d]{2}:[a-f|\d]{2}:[a-f|\d]{2}$/).test(value)
          if (regexp) {
            var params = {
              tableName: 'devops_ip_manage',
              fieldName: 'mac_code',
              fieldVal: value,
              dataId: this.ipManageId
            }
            duplicateCheck(params).then((res) => {
              if (res.success) {
                callback()
              } else {
                callback('MAC地址已存在!')
              }
            })
          } else {
            callback('要求MAC地址由小写字母、数字和冒号组成，例如：f8:89:d2:7f:b2:c1')
          }
        } else {
          callback('请输入MAC地址')
        }
      }
    },

    validateIpAddress(rule, value, callback) {
      var params = {
        tableName: 'devops_ip_manage',
        fieldName: 'ip_address',
        fieldVal: value,
        dataId: this.ipManageId
      }
      duplicateCheck(params).then((res) => {
        if (res.success) {
          callback()
        } else {
          callback('IP地址已存在!')
        }
      })
    },
    changeMAC(val) {

    },
    //渲染流程表单数据
    showFlowData() {
      if (this.formBpm === true) {
        let params = {
          id: this.formData.dataId
        }
        getAction(this.url.queryById, params).then((res) => {
          if (res.success) {
            this.edit(res.result)
          }
        })
      }
    },
    submitForm() {
      const that = this
      // 触发表单验证
      let bo = this.validateUserId(this.utilizeUserId)
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let formData = Object.assign(this.model, values)
          if (this.utilizeUserId && this.utilizeUserId.length > 0) {
            formData.utilizeUserId = this.utilizeUserId
          } else {
            this.validateStatus = 'error'
            this.Tips = '请输入选择人'
            that.confirmLoading = false
            return
          }
          formData.utilizeUserText = this.utilizeUserText
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    popupCallback(row) {
      this.form.setFieldsValue(pick(row, 'terminalName', 'ipAddress', 'macCode'))
    },
    //返回上一级
    getGo() {
      this.$parent.pButton2(0)
    }
  }
}
</script>