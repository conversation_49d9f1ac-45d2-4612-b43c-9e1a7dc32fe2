/*与终端信息类似的页面的滚动条样式，涉及页面垂直方向和表格table水平方向的滚动条*/
/*垂直滚动条宽度，水平滚动条高度*/
//@size: 12px;
/*滚动条圆角*/
//@radius: 8px;
/*滚动条滑块背景色*/
//@thumbColor: #D6D6D6;
/*滚动条背景色*/
//@trackColor: #EAEAEA;
/*页面垂直滚动条样式*/
.vScroll {
  overflow-y: auto !important;
}
.hScroll {
  min-width: 0px !important;
  overflow-x: auto !important;
}

/*本脚本中注释了滚动条所有样式，其原因是：在globalLayout父组件中，已定义了滚动条的全局样式*/
/*垂直滚动条背景*/
/*.vScroll::-webkit-scrollbar{
  width: @size;
  background-color: @trackColor;
  border-radius: @radius !important;
  -moz-border-radius: @radius;
}*/
/*垂直滚动条滑块*/
/*.vScroll::-webkit-scrollbar-thumb{
  background-color: @thumbColor;
  border-radius: @radius;
  -moz-border-radius: @radius;
}*/
/*水平滚动条背景*/
/*.hScroll::-webkit-scrollbar{
  height: @size !important;
  background-color: @trackColor !important;
  border-radius: @radius !important;
  -moz-border-radius: @radius;
}*/
/*水平滚动条滑块*/
/*.hScroll::-webkit-scrollbar-thumb{
  background-color: @thumbColor !important;
  border-radius: @radius !important;
  -moz-border-radius: @radius;
}*/
/*table水平滚动条*/
::v-deep .ant-table-body {
  min-width: 0px !important;
  overflow-x: auto !important;
}

/*背景*/
/*::v-deep .ant-table-body::-webkit-scrollbar{
  height: @size !important;
  background-color: @trackColor !important;
  border-radius: @radius !important;
  -moz-border-radius: @radius;
}*/
/*滑块*/
/*
::v-deep .ant-table-body::-webkit-scrollbar-thumb {
  background-color: @thumbColor !important;
  border-radius: @radius !important;
  -moz-border-radius: @radius;
}
*/

