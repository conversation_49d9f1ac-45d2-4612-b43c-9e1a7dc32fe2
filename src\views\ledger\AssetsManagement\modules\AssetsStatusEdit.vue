<template>
  <j-modal
    :title='title'
    :visible='visible'
    :centered='true'
    switchFullscreen
    :confirmLoading='confirmLoading'
    :destroyOnClose='true'
    :width='modalWidth'
    :closable='true'
    @cancel='handleCancel'
    @ok='handleOk'
    okText=''
    cancelText='关闭'
  >
    <a-form :form="form">
      <!-- 不同状态不同内容 -->
        <a-row :gutter='24'>
          <a-col :span='24'>
            <a-form-item label="资产状态：" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-select
                :getPopupContainer="(node) => node.parentNode"
                 allowClear
                placeholder="请选择"
                @change="handleChange"
                v-decorator="['status', formValidator.status]">
                <a-select-option v-for="item in queryStatus" :key="item.value">{{ item.text }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <!-- 下发 -->
        <a-row :gutter='24' v-if="ContentFlag == 'yxf'">
          <a-col :span='24'>
            <a-form-item label="下发到" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input placeholder="请输入" :allowClear="true" autocomplete="off" v-decorator="['info', formValidator.info]"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span='24'>
            <a-form-item label="下发时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date
                placeholder="请选择"
                style="width: 100% !important"
                :trigger-change="true"
                :showTime="true"
                dateFormat="YYYY-MM-DD HH:mm:ss"
                v-decorator="['time', formValidator.time]"
              >
              </j-date>
            </a-form-item>
          </a-col>
        </a-row>
        <!-- 已部署 -->
        <a-row :gutter='24' v-else-if="ContentFlag == 'ybs'">
          <a-col :span='24'>
            <a-form-item label="单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input placeholder="请输入" :allowClear="true" autocomplete="off" v-decorator="['info', formValidator.info]"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span='24'>
            <a-form-item label="部署日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date
                placeholder="请选择"
                style="width: 100%"
                :trigger-change="true"
                :showTime="true"
                dateFormat="YYYY-MM-DD HH:mm:ss"
                v-decorator="['time', formValidator.time]"
              >
              </j-date>
            </a-form-item>
          </a-col>
        </a-row>
        <!-- 已上线 -->
        <a-row :gutter='24' v-else-if="ContentFlag == 'ysx'">
          <a-col :span='24'>
            <a-form-item label="使用人" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input placeholder="请输入" :allowClear="true" autocomplete="off" v-decorator="['info', formValidator.info]"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span='24'>
            <a-form-item label="上线日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date
                placeholder="请选择"
                style="width: 100%"
                :trigger-change="true"
                :showTime="true"
                dateFormat="YYYY-MM-DD HH:mm:ss"
                v-decorator="['time', formValidator.time]"
              >
              </j-date>
            </a-form-item>
          </a-col>
        </a-row>
        <!-- 已暂存 -->
        <a-row :gutter='24' v-else-if="ContentFlag == 'yhc'">
          <a-col :span='24'>
            <a-form-item label="暂存到" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input placeholder="请输入" :allowClear="true" autocomplete="off" v-decorator="['info', formValidator.info]"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span='24'>
            <a-form-item label="暂存日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date
                placeholder="请选择"
                style="width: 100%"
                :trigger-change="true"
                :showTime="true"
                dateFormat="YYYY-MM-DD HH:mm:ss"
                v-decorator="['time', formValidator.time]"
              >
              </j-date>
            </a-form-item>
          </a-col>
        </a-row>
        <!-- 已转移 -->
        <a-row :gutter='24' v-else-if="ContentFlag == 'yzy'">
          <a-col :span='24'>
            <a-form-item label="转移扶贫到" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input placeholder="请输入" :allowClear="true" autocomplete="off" v-decorator="['info', formValidator.info]"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span='24'>
            <a-form-item label="转移日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date
                placeholder="请选择"
                style="width: 100%"
                :trigger-change="true"
                :showTime="true"
                dateFormat="YYYY-MM-DD HH:mm:ss"
                v-decorator="['time', formValidator.time]"
              >
              </j-date>
            </a-form-item>
          </a-col>
        </a-row>
        <!-- 已销毁 -->
        <a-row :gutter='24' v-else-if="ContentFlag == 'yxh'">
          <a-col :span='24'>
            <a-form-item label="销毁备注" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input placeholder="请输入" :allowClear="true" autocomplete="off" v-decorator="['info', formValidator.info]"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span='24'>
            <a-form-item label="销毁日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date
                placeholder="请选择"
                style="width: 100%"
                :trigger-change="true"
                :showTime="true"
                dateFormat="YYYY-MM-DD HH:mm:ss"
                v-decorator="['time', formValidator.time]"
              >
              </j-date>
            </a-form-item>
          </a-col>
        </a-row>
        <!-- 维修 -->
        <a-row :gutter='24' v-else-if="ContentFlag == 'ywx'">
          <a-col :span='24'>
            <a-form-item label="维修原因" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input placeholder="请输入" :allowClear="true" autocomplete="off" v-decorator="['info', formValidator.info]"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span='24'>
            <a-form-item label="维修时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date
                placeholder="请选择"
                style="width: 100%"
                :trigger-change="true"
                :showTime="true"
                dateFormat="YYYY-MM-DD HH:mm:ss"
                v-decorator="['time', formValidator.time]"
              >
              </j-date>
            </a-form-item>
          </a-col>
        </a-row>
        <!-- 已使用 -->
        <a-row :gutter='24' v-else-if="ContentFlag == 'ysy'">
          <a-col :span='24'>
            <a-form-item label="使用人" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input placeholder="请输入" :allowClear="true" autocomplete="off" v-decorator="['info', formValidator.info]"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span='24'>
            <a-form-item label="使用日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date
                placeholder="请选择"
                style="width: 100%"
                :trigger-change="true"
                :showTime="true"
                dateFormat="YYYY-MM-DD HH:mm:ss"
                v-decorator="['time', formValidator.time]"
              >
              </j-date>
            </a-form-item>
          </a-col>
        </a-row>
    </a-form>
  </j-modal>
</template>
<script>
import pick from 'lodash.pick'
import JDate from '@/components/jeecg/JDate'
import { ajaxGetDictItems } from '@api/api'
import { ledgerChangeEdit, ledgerEditStatus } from '@api/AssetsManagement'
import JDictSelectTag from '@/components/dict/JDictSelectTag.vue'

export default {
  name: 'AssetsStatusEdit',
  components: {
    JDate,
    JDictSelectTag,
  },
  data() {
    return {
      title: '操作',
      visible: false,
      confirmLoading: false,
      /* 弹框宽 */
      modalWidth: '520px',
      form: this.$form.createForm(this),
      ids: null, //编辑的记录id
      labelCol: {
        //style: 'width:105px',
        xs: { span: 24 },
        sm: { span: 6 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      ContentFlag: 'yxf',
      formValidator: {
        status: {
          initialValue: 'yxf',
          rules: [
            {
              required: true,
              message: '必选!',
            },
          ],
        },
        info: {
          rules: [
            {
              required: true,
              message: '必填!',
            },
            {
              min: 2,
              max: 30,
              message: '长度在 2 到 30 个字符',
              trigger: 'blur',
            },
          ],
        },
        time: {
          rules: [
            {
              required: true,
              message: '必选!',
            },
          ],
        },
      },
      AssetStatusData: [],
      queryStatus: [],
    }
  },
  methods: {
    add() {
      this.edit({})
    },
    edit(id) {
      this.form.resetFields()
      this.ids = id
      this.visible = true
    },
    batchEdit(ids) {
      this.form.resetFields()
      this.ids = ids
      this.visible = true
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let formData = Object.assign({ ids: that.ids }, values)
          ledgerEditStatus(formData)
            .then((res) => {
              if (res.success) {
                this.$message.success('修改成功')
                that.$emit('ok')
              } else {
                this.$message.error('修改失败')
              }
            })
            .finally(() => {
              that.confirmLoading = false
              that.close()
            })
        }
      })
    },
    handleCancel() {
      this.close()
    },
    handleChange(value) {

      this.ContentFlag = value
    },
    //字典获取状态--------------------------------------------
    getDicData() {
      this.getAssetStatus('asset_status')
    },
    getAssetStatus(code) {
      let that = this
      ajaxGetDictItems(code, null).then((res) => {
        let temp = res.result
        this.queryStatus = temp
        for (let i = 0; i < temp.length; i++) {
          that.AssetStatusData.push(temp[i].title)
        }
      })
    },
    //字典获取状态-------------------------------------end----

    //日期
    onChange(date, dateString) {
    },
  },

  mounted() {
    this.getDicData()
  },
}
</script>
<style lang='less' scoped>
@import '~@assets/less/normalModal.less';
</style>
