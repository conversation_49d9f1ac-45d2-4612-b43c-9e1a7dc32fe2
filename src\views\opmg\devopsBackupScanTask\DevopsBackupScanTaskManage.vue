<template>
  <div style="height: 100%;">
      <component :is="pageName" :data="data"/>
  </div>
</template>
<script>
  import DevopsBackupScanTaskList from './DevopsBackupScanTaskList'
  import DevopsBackupScanTaskDetails from './modules/DevopsBackupScanTaskDetails'
  export default {
    name: "DevopsBackupScanTaskManage",
    data() {
      return {
        isActive: 0,
        data:{}
      };
    },
    components: {
      DevopsBackupScanTaskList,
      DevopsBackupScanTaskDetails
    },
    created(){
      this.pButton1(0);
    },
    //使用计算属性
    computed: {
      pageName() {
        switch (this.isActive) {
          case 0:
            return "DevopsBackupScanTaskList";
            break;

          default:
            return "DevopsBackupScanTaskDetails";
            break;
        }
      }
    },
    methods: {
      pButton1(index) {
        this.isActive = index;
      },
      pButton2(index,item) {
        this.isActive = index;
        this.data = item;
      }
    }
  }
</script>