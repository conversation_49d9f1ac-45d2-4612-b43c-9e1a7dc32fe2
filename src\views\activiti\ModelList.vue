<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll zhl zhll">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <!-- 查询区域 -->
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="模型名称">
                  <a-input placeholder="请输入" v-model="queryParam.keyWord"></a-input>
                </a-form-item>
              </a-col>
              <a-col :span="colBtnsSpan()">
                <span
                  class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                >
                  <a-button type="primary" class="btn-search btn-search-style" @click="searchQuery">查询</a-button>
                  <a-button class="btn-reset btn-reset-style" @click="searchReset" style="margin-left: 10px"
                    >重置</a-button
                  >
                  <a-button style="margin-left: 10px" @click="createObj.visible = true">创建流程模型</a-button>
                  <a v-if="isVisible" class="btn-updown-style" @click="doToggleSearch">
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>

      <a-card :bordered="false" style="width: 100%; flex: auto">
        <!-- table区域-begin -->
        <a-table
          ref="table"
          bordered
          rowKey="id"
          :columns="columns"
          :dataSource="dataSource"
          :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
          :pagination="ipagination"
          :loading="loading"
          @change="handleTableChange"
        >
          <span slot="revision" slot-scope="text, record"> v.{{ text }} </span>
          <span slot="metaInfo" slot-scope="text, record">
            <j-ellipsis :value="JSON.parse(text).description" :length="10" />
          </span>
          <!-- 字符串超长截取省略号显示-->
          <span slot="logContent" slot-scope="text, record">
            <j-ellipsis :value="text" :length="10" />
          </span>
          <span slot="make" slot-scope="text, record" class="caozuo">
            <a href="javascript:void(0);" @click="deployment(record)">发布</a>
            <a-divider type="vertical" />
            <a href="javascript:void(0);" @click="updatelc(record.id)">设计流程</a>
            <a-divider type="vertical" />
            <a-popconfirm
              title="是否确认删除?"
              @confirm="deletelc(1, record)"
              @cancel="deletelc(0)"
              okText="确定"
              cancelText="取消"
            >
              <a href="javascript:void(0);">删除</a>
            </a-popconfirm>
          </span>
          <template slot="tooltip" slot-scope="text">
            <a-tooltip placement="topLeft" :title="text" trigger="hover">
              <div class="tooltip">
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
      </a-card>
      <!-- table区域-end -->
      <a-modal
        title="创建模型"
        :visible="createObj.visible"
        @ok="createObjOk"
        :confirmLoading="createObj.confirmLoading"
        @cancel="createObj.visible = false"
        :width="800"
      >
        <div>
          <a-row type="flex" style="margin-bottom: 16px">
            <a-col :span="4" style="text-align: center; float: right">
              <span>模型名称：</span>
            </a-col>
            <a-col :span="20">
              <a-input placeholder="输入模型名称" v-model="createObj.name"></a-input>
            </a-col>
          </a-row>
          <a-row type="flex" style="margin-bottom: 16px">
            <a-col :span="4" style="text-align: center; float: right">
              <span>模型Key:</span>
            </a-col>
            <a-col :span="20">
              <a-input placeholder="输入模型Key"
                       @change='keyChange()'
                       v-model="createObj.key"></a-input>
            </a-col>
          </a-row>
          <a-row type="flex" style="margin-bottom: 16px">
            <a-col :span="4" style="text-align: center; float: right">
              <span>模型描述：</span>
            </a-col>
            <a-col :span="20">
              <a-textarea placeholder="输入模型描述" v-model="createObj.description" :rows="4" />
            </a-col>
          </a-row>
        </div>
      </a-modal>
      <!-- <a-modal
      title="设计模型"
      :visible="updateObj.visible"
      :footer="null" :maskClosable="false"
      width="90%"
      @cancel="cancelUpdate"
      style="top: 20px;"
    >
      <iframe  :src="iframUrl" frameborder="0" width="100%" height="800px" scrolling="auto" style="background-color: #fff;"></iframe>
    </a-modal> -->
    </a-col>
  </a-row>
</template>

<script>
import { filterObj } from '@/utils/util'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import JEllipsis from '@/components/jeecg/JEllipsis'
import { deleteAction, getAction, downFile } from '@/api/manage'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'

export default {
  name: 'ModelList',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components: {
    JEllipsis,
  },
  data() {
    return {
      formItemLayout: {
        labelCol: {
          style: 'width:90px',
        },
        wrapperCol: {
          style: 'width:calc(100% - 90px)'
        }
      },
      innerHeight: '100%',
      /*流程设计器连接*/
      iframUrl: '',
      /*新增流程框参数*/
      createObj: {
        visible: false,
        confirmLoading: false,
      },
      /*设计流程框参数*/
      updateObj: {
        visible: false,
        confirmLoading: false,
      },

      description: '这是流程模型列表页面',
      // 查询条件
      queryParam: {
        createTimeRange: [],
        keyWord: '',
      },
      tabKey: '1',
      // 表头
      columns: [
        {
          title: '',
          dataIndex: '',
          key: 'rowIndex',
          customRender: function (t, r, index) {
            return parseInt(index) + 1
          },
        },
        {
          title: '模型id',
          dataIndex: 'id',
        },
        {
          title: '模型名称',
          dataIndex: 'name',
          scopedSlots: { customRender: 'logContent' },
        },
        {
          title: '模型key',
          dataIndex: 'key',
          scopedSlots: { customRender: 'tooltip' },
        },
        {
          title: '版本',
          dataIndex: 'revision',
          scopedSlots: { customRender: 'revision' },
        },
        {
          title: '备注描述',
          dataIndex: 'metaInfo',
          scopedSlots: { customRender: 'metaInfo' },
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          // sorter:true
        },
        {
          title: '最后更新时间',
          dataIndex: 'lastUpdateTime',
        },
        {
          title: '操作',
          width: 180,
          fixed: 'right',
          dataIndex: '',
          scopedSlots: { customRender: 'make' },
          align: 'center',
        },
      ],
      labelCol: {
        xs: { span: 1 },
        sm: { span: 2 },
      },
      wrapperCol: {
        xs: { span: 10 },
        sm: { span: 16 },
      },
      url: {
        list: '/activiti/models/modelListData',
        delete: '/activiti/models/delete/',
        deployment: '/activiti/models/deployment/',
        create: '/activiti/models/create',
        update: '/activiti/modeler.html?modelId=',
      },
    }
  },
  mounted() {},
  methods: {
    keyChange(){
     let value=this.createObj.key.replace(/[\W]/g,'')
      this.createObj.key= value.replace("_",'')
    },
    /*创建流程*/
    createObjOk(e) {
      this.createObj.confirmLoading = true
      this.updateObj.visible = true
      this.iframUrl =
        window._CONFIG['domianURL'] +
        `${this.url.create}?name=${this.createObj.name || ''}&key=${this.createObj.key || ''}&description=${
          this.createObj.description || ''
        }`
      window.open(this.iframUrl, '_blank')
      this.createObj.visible = false
      this.createObj.confirmLoading = false
    },
    /*修改流程*/
    updatelc(id) {
      var _this = this
      this.$message.loading('稍等。。。', 0.8).then(() => {
        _this.createObj.confirmLoading = true
        _this.iframUrl = window._CONFIG['domianURL'] + `${_this.url.update}${id}`
        window.open(this.iframUrl, '_blank')
        _this.updateObj.visible = true
        _this.createObj.confirmLoading = false
      })
    },
    cancelUpdate() {
      var _this = this
      this.$confirm({
        title: '关闭前请确认已保存修改?',
        content: '关闭后未保存的修改将丢失！',
        okText: '确认关闭',
        okType: 'danger',
        cancelText: '再看看',
        onOk() {
          _this.updateObj.visible = false
          _this.loadData()
        },
        onCancel() {},
      })
    },
    /*发布流程*/
    deployment(row) {
      var _this = this
      var id = row.id
      var name = row.name
      this.$confirm({
        title: '确认部署流程?',
        okText: '确定',
        cancelText: '取消',
        content: `确认部署流程：${name}`,
        onOk() {
          getAction(_this.url.deployment + id).then((res) => {
            if (res.success) {
              _this.$message.success(res.message)
            } else {
              _this.$message.error(res.message)
            }
            this.loadData()
          })
        },
        onCancel() {},
      })
    },
    /*删除模型*/
    deletelc(y, row) {
      if (y) {
        getAction(this.url.delete + row.id).then((res) => {
          if (res.success) {
            this.$message.success(res.message)
          } else {
            this.$message.error(res.message)
          }
          this.loadData()
        })
      }
    },
    handleTableChange(pagination, filters, sorter) {
      //分页、排序、筛选变化时触发
      //TODO 筛选
      if (Object.keys(sorter).length > 0) {
        this.isorter.column = sorter.field
        this.isorter.order = 'ascend' == sorter.order ? 'asc' : 'desc'
      }
      this.ipagination = pagination
      // this.loadData();
    },
    loadData(arg) {
      if (!this.url.list) {
        this.$message.error('请设置url.list属性!')
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1
      }
      var params = this.getQueryParams() //查询条件
      this.loading = true
      getAction(this.url.list, params).then((res) => {
        if (res.success) {
          let records = res.result || []
          this.dataSource = records
          this.ipagination.total = records.length
        }
        if (res.code === 510) {
          this.$message.warning(res.message)
        }
        this.loading = false
      })
    },
    getQueryParams() {
      var param = Object.assign({}, this.queryParam, this.isorter)
      delete param.createTimeRange // 时间参数不传递后台
      return filterObj(param)
    },

    // 重置
    searchReset() {
      var that = this
      var logType = that.queryParam.logType
      that.queryParam = {} //清空查询区域参数
      that.queryParam.logType = logType
      that.loadData(this.ipagination.current)
    },
    onDateChange: function (value, dateString) {
      this.queryParam.createTime_begin = dateString[0]
      this.queryParam.createTime_end = dateString[1]
    },
    onDateOk(value) {},
  },
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
/*给table列设置宽度*/
::v-deep .ant-table-scroll .ant-table-thead > tr > th,
::v-deep .ant-table-scroll .ant-table-tbody > tr > td {
  /*标题*/
  &:nth-child(1) {
    min-width: 10px;
    max-width: 200px;
  }
  /*模型id*/
  &:nth-child(2) {
    min-width: 80px;
    max-width: 300px;
  }

  /*模型名称*/

  &:nth-child(3) {
    min-width: 100px;
    max-width: 300px;
  }

  /*模型key*/

  &:nth-child(4) {
    min-width: 50px;
    max-width: 300px;
  }

  /*版本*/

  &:nth-child(5) {
    min-width: 20px;
    max-width: 150px;
  }

  /*备注描述*/

  &:nth-child(6) {
    min-width: 200px;
    max-width: 300px;
  }
  /*创建时间*/

  &:nth-child(7) {
    min-width: 150px;
    max-width: 250px;
  }
  /*最后更新时间*/

  &:nth-child(8) {
    min-width: 150px;
    max-width: 250px;
  }
}

/*表头样式*/
::v-deep .ant-table-thead > tr > th {
  text-align: center;
  white-space: nowrap;
}

/*内容对齐方式、省略显示*/
::v-deep .ant-table-tbody > tr > td {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;

  &:first-child,
  &:nth-child(5) {
    text-align: center;
  }
  &:nth-child(3),
  &:nth-child(4),
  &:nth-child(6) {
    text-align: left;
  }
  &:nth-child(2),
  &:nth-child(7),
  &:nth-child(8) {
    text-align: right;
  }
}
</style>

