//run-compiler 模式下可用；
import { svgStore } from "./svgStore"
import { getAction, httpAction } from '@/api/manage'
export default {
    props: {
        type: {
            type: String,
            default: "",
            require: false,
        },
        //获取svg数据 url
        outSide: {
            type: String,
            default: "",
            require: false,
        },
    },
    data() {
        return {
            SvgComp: {},
            svgs: {}
        }
    },
    beforeCreate() {
    },
    created() {
        this.svgs = svgStore;
        // Object.assign(this.svgs,svgStore)
        this.SvgComp = {
            template: this.svgs[this.type] || "<div></div>"
        }
    },

    methods: {
        getSvgs() {
            if (this.outSide) {
                getAction(this.outSide)
                    .then(res => {
                        if (res) {
                            if (this.svgs[this.type] === undefined) {
                                this.svgs[this.type] = res
                                this.SvgComp = {
                                    template: this.svgs[this.type] || ""
                                }
                            }else if(this.SvgComp.template === "<div></div>"){
                                //防止同时加载多个相同图标 不能显示的问题
                                this.SvgComp = {
                                    template: this.svgs[this.type]
                                }
                            }
                        }
                    }).catch(error => {

                    })


            }
        }
    },
    render() {
        if (this.svgs[this.type]) {
            return <a-icon component={this.SvgComp}></a-icon>
        }
        else if (this.outSide) {
            this.getSvgs()
            return <a-icon component={this.SvgComp}></a-icon>
        }

    },
}