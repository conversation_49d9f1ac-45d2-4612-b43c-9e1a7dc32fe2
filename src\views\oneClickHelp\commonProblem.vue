<template>
  <div class="body">
    <div class="tree">
      <el-input
        placeholder="请输入搜索内容"
        v-model="filterText"
        class="input-with-select"
        style="width: 90%"
        suffix-icon="el-icon-search"
      >
      </el-input>
      <el-tree
        :data="data"
        :props="defaultProps"
        default-expand-all
        :filter-node-method="filterNode"
        @node-click="handleNodeClick"
        ref="tree"
        highlight-current
      >
      </el-tree>
    </div>
    <div class="dataList">
      <div class="detaList-item" v-for="(item, index) in dataList" :key="index">
        <div class="detaList-item-top">
          <img src="@/assets/wen.png" alt="" />
          <span style="margin-left: 0px; color: #409eff; cursor: pointer" @click="getInfo(item.id)">
            {{ item.question }}
          </span>
        </div>
        <div class="detaList-item-core">
          <span> 解决方法： {{ item.answererContent }}</span>
        </div>
      </div>
    </div>

    <!-- 查看页面 -->
    <question-Info-Modal ref="modalForm" @ok="modalFormOk"></question-Info-Modal>
  </div>
</template>
<script>
import QuestionInfoModal from './QuestionInfoModal'
import { httpAction, getAction, deleteAction } from '@/api/manage'
export default {
  name: 'commonProblem',
  components: {
    QuestionInfoModal,
  },
  data() {
    return {
      filterText: '',
      data: [],
      dataList: [],
      url: {
        getDictCodeChild: '/sys/dict/getDictCodeChild',
        list: '/question/question/listOften1',
      },
      defaultProps: {
        children: 'children',
        label: 'label',
      },
      firstId: '',
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    },
  },
  mounted() {
    let params = {
      dictCode: 'serviceRequestClass',
    }
    getAction(this.url.getDictCodeChild, params).then((res) => {
      if (res.success) {
        this.data = res.result
        this.firstId = this.data[0].id
        this.getList()
      }
    })
  },
  methods: {
    //查看详情
    getInfo(questionId) {
      this.$refs.modalForm.title = '问题详情'
      this.$refs.modalForm.show(questionId)
    },
    filterNode(value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },
    handleNodeClick(data) {
      this.firstId = data.id
      this.getList()
    },
    //获取常见问题LIST
    getList() {
      let params = {
        commType: '1',
        questionType: this.firstId,
      }
      getAction(this.url.list, params).then((res) => {
        if (res.success) {
          this.dataList = res.result.records
        }
      })
    },
  },
}
</script>
<style lang="less" scoped>
.body {
  width: 100%;
  height: 100%;
  display: flex;
  .tree {
    border-right: 1px solid #e4e4e4;
    width: 240px;
    height: 100%;
    padding: 0 16px;
    el-tree {
      width: 100%;
    }
  }
  .dataList {
    overflow-y: auto;
    width: calc(100% - 240px);
    height: 560px;
    padding-left: 20px;
    .detaList-item {
      width: 100%;
      .detaList-item-top {
        display: flex;
        align-items: center;
        color: #1d3774;
        font-weight: 600;
        font-size: 16px;
        img {
          margin-right: 10px;
        }
      }
      .detaList-item-core {
        height: 40px;
        line-height: 40px;
      }
    }
  }
  ::v-deep .el-tree-node__content {
    height: 32px;
    line-height: 32px;
    color: rgba(0, 0, 0, 0.65);
    text-align: center;
    width: 100%;
  }
  ::v-deep .el-tree {
    margin-top: 15px !important;
    height: 40px;
    line-height: 40px;
  }
  // ::v-deep .el-tree-node__label {
  //   width: 75%;
  // }
  ::v-deep .detaList-item-core {
    padding-left: 35px;
  }
  ::v-deep .detaList-item {
    margin-bottom: 32px;
  }
  ::v-deep .el-tree-node:focus > .el-tree-node__content {
    color: #157ee7;
    background-color: #eff6fe;
  }
}
</style>