<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    :destroyOnClose="true"
    :centered="true"
    switchFullscreen
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭"
    okText="保存">
    <scan-task-form
      ref="realForm"
      @ok="submitCallback"
      @closeForm='close'
      :disabled="disableSubmit"
    ></scan-task-form>
  </j-modal>
</template>

<script>
import scanTaskForm from './scanTaskForm'
export default {
  name: 'scanTaskModal',
  components: {
    scanTaskForm,
  },
  data() {
    return {
      title: '',
      width: '1200px',
      visible: false,
      disableSubmit: false,
    }
  },
  methods: {
    add() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.add()
      })
    },
    edit(record) {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.edit(record)
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      this.$refs.realForm.submitForm()
    },
    submitCallback() {
      this.$emit('ok')
      this.visible = false
    },
    handleCancel() {
      this.$nextTick(() => {
        this.$refs.realForm.closeForm()
      })
    },
  },
}
</script>
<style lang="less" scoped>
@import '~@assets/less/normalModal.less';
</style>