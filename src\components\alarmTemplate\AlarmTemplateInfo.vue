<template>
  <a-spin :spinning='loading'>
    <div class="div-container">
      <div class="alarm-info">
        <p class="p-title">
          <!--        <span><b>告警：{{ alarmInfo.name }}</b></span>-->
          <span class="colorTotal">基础信息</span>
          <span style="margin-left: 10px; color: #409eff; cursor: pointer; font-size: 14px"
            @click="handleDetailEdit(alarmInfo)">
            <a-icon type="edit" style="margin-right: 6px" />编辑
          </span>
        </p>
        <table class="gridtable">
          <tr>
            <td class="leftTd">产品名称</td>
            <td class="rightTd">{{ alarmInfo.proName }}</td>
            <td class="leftTd">设备名称</td>
            <td class="rightTd">{{ alarmDeviceNames }}</td>
          </tr>
          <tr>
            <td class="leftTd">告警名称</td>
            <td class="rightTd">{{ alarmInfo.name }}</td>
            <td class="leftTd">启用状态</td>
            <td class="rightTd" >{{ alarmInfo.isOnline === '0' ? '禁用' : '启用' }}</td>
          </tr>
          <tr>
            <td class="leftTd">告警描述</td>
            <td colspan='3' class="rightTd">{{ alarmInfo.remark }}</td>
          </tr>
        </table>
      </div>
      <div class="rules-info">
        <p class="p-title">
          <span class="colorTotal">告警规则</span>
        </p>
        <div style='margin-bottom: 12px'>
          <a-radio-group v-model='alarmInfo.triggerType' :disabled='true'>
            <a-radio :value="'0'">满足任意规则触发</a-radio>
            <a-radio :value="'1'">满足所有规则触发</a-radio>
          </a-radio-group>
        </div>
        <div v-for="(item,i) in ruleList" :key="i">
          <p> <span class="item-name"></span>规则{{i + 1}}</p>
          <table class="rule-table">
            <tr style="background-color: #FAFAFA">
              <th style="width:25%">指标</th>
              <th style="width:10%">条件</th>
              <th style="width:25%; word-wrap: break-word;word-break:break-all;overflow-wrap: break-word">级别名称</th>
              <th style="width:10%">告警等级</th>
              <th style="width:10%">阈值</th>
              <th style="width:10%">触发次数</th>
              <th style="width:10%">升级次数</th>
            </tr>
            <tr v-for="(items,index) in item.contentsArr" :key="index">
              <td class='custom-colum'>{{item.subjectIndexText}}</td>
              <td class='custom-colum'>{{item.conditionsText}}</td>
              <td class='custom-colum'>{{items.title}}</td>
              <td class='custom-colum'>{{items.level}}</td>
              <td class='custom-colum'>{{items.value}}</td>
              <td class='custom-colum'>{{items.times}}</td>
              <td class='custom-colum'>{{items.changeTimes}}</td>
            </tr>
          </table>
        </div>
      </div>

      <div class="notice-info" v-if="alarmInfo.sceneId&&sceneList&&sceneList.length>0">
        <p class="p-title">
          <span class="colorTotal">自恢复设置</span>
        </p>
        <table class="gridtable">
          <tr>
            <td class="leftTd" style="width: 17%">脚本场景</td>
            <td class="rightTd" style="width: 83%">{{ getSceneName(alarmInfo.sceneId) }}</td>
          </tr>
        </table>
      </div>

      <div class="notice-info" v-if="alarmInfo.noticeStatus">
        <p class="p-title">
          <span class="colorTotal">消息通知设置</span>
        </p>
        <table class="alarm-notice-table" v-if='pushRulesArr.length>0'>
          <tr style="background-color: #FAFAFA">
            <th>告警模式</th>
<!--            <th>告警时间（分钟）</th>-->
<!--            <th>告警次数</th>-->
            <th>留观次数</th>
            <th>重复通知间隔(次)</th>
            <th>通知模板</th>
          </tr>
          <tr v-for="(item,i) in pushRulesArr" :key="i">
            <td class='custom-colum'>{{item.title}}</td>
<!--            <td class='custom-colum'>{{item.timeRange}}</td>
            <td class='custom-colum'>{{item.alarmCount}}</td>-->
            <td class='custom-colum'>{{item.noticeVisitsTimes}}</td>
            <td class='custom-colum'>{{item.noticeIntervalTimes}}</td>
            <td class='custom-colum'>{{item.noticeTemplateNames}}</td>
          </tr>
        </table>
        <table class="gridtable">
          <tr>
            <td class="leftTd">生效时间</td>
            <td :colspan='3' class="rightTd">{{ alarmInfo.effectiveDateText }}</td>
          </tr>
          <tr>
            <td class="leftTd">开始时间</td>
            <td class="rightTd">{{ alarmInfo.beginTime }}</td>
            <td class="leftTd">结束时间</td>
            <td class="rightTd">{{ alarmInfo.endTime }}</td>
          </tr>
          <tr>
            <td class="leftTd">告警沉默周期(分钟)</td>
            <td class="rightTd">{{ alarmInfo.silentTime }}</td>
            <td class="leftTd">告警解除模板</td>
            <td class="rightTd">{{alarmInfo.noAlarmNoticeIdText}}</td>
          </tr>
        </table>
      </div>
      <alarm-template-detail-modal ref="modalForm" @ok="modalFormOk"></alarm-template-detail-modal>
    </div>
  </a-spin>
</template>
<script>
  import AlarmTemplateDetailModal from './AlarmTemplateDetailModal'
  import {
    filterObj
  } from '@/utils/util'
  import {
    getAction
  } from '@/api/manage'
  import {
    ajaxGetDictItems,
    getDictItemsFromCache
  } from '@/api/api'
  export default {
    name: 'alarmTemplateInfoModal',
    components: {
      AlarmTemplateDetailModal,
    },
    data() {
      return {
        queryParam: {
          id: '',
        },
        visible: false,
        disabled: true,
        /* 加载状态 */
        loading: false,
        url: {
          rule: '/alarm/alarmTemplate/findTemAndRule',
          queryById: '/alarm/alarmTemplate/queryById',
          alarmLevel: '/alarm/alarmLevel/getLevelList',//获取告警级别数据
          devList: '/sys/notice/template/devList',
          scene: '/autoControl/task/getSceneList'//脚本场景下拉数据
        },
        alarmInfo: {
          noticeStatus: false
        },
        deviceInfo: {},
        showFlag: true,
        alarmDeviceNames: '',
        ruleList: [],
        pushRulesArr: [],
        alarmLevelList: [],
        sceneList:[],
        //通知生效时间下拉数据
        effectiveDateList: [
          { label: '周一', value: '2' }, { label: '周二', value: '3' }, { label: '周三', value: '4' },
          { label: '周四', value: '5' }, { label: '周五', value: '6' }, { label: '周六', value: '7' },
          { label: '周日', value: '1' }
        ],
      }
    },
    created() {
      this.getSceneList()
      this.getAlarmContents()
    },
    methods: {
      modalFormOk() {
        let params = {
          id: this.alarmInfo.id,
        }
        getAction(this.url.queryById, params).then((res) => {
          if (res.success) {
            this.alarmInfo = {
              ...res.result,
            }
            this.getEffectiveDate(this.alarmInfo)
            this.getDeviceList(this.alarmInfo.productId)
            this.getRulesData()
          }
        })
      },
      //详情编辑
      handleDetailEdit() {
        this.$refs.modalForm.title = '编辑告警'
        this.$refs.modalForm.edit(this.alarmInfo)
        this.$refs.modalForm.deviceInfo = this.deviceInfo
        this.$refs.modalForm.showFlag = this.showFlag
      },
      show(alarmInfo, deviceInfo, showFlag = true) {
        this.visible = true
        this.alarmInfo = alarmInfo
        this.deviceInfo = deviceInfo,
          this.showFlag = showFlag
        this.getEffectiveDate(alarmInfo)
        this.getDeviceList(this.alarmInfo.productId)
        this.getAlarmContents().then((res)=>{
          if (res){
            this.getAlarmNotice();
            this.getRulesData()
          }
        })
      },
      getEffectiveDate(alarmInfo) {
        let effectiveDateIndexArr = alarmInfo.effectiveDate.substring(1, alarmInfo.effectiveDate.length - 1).split(',')
        let matchDateArr = []
        for (let i = 0; i < effectiveDateIndexArr.length; i++) {
          this.effectiveDateList.filter((item) => {
            if (effectiveDateIndexArr[i] == item.value) {
              matchDateArr.push(item.label)
            }
          })
        }
        this.alarmInfo.effectiveDateText = matchDateArr.join(',')
      },
      getAlarmNotice() {
        this.pushRulesArr = JSON.parse(this.alarmInfo.pushRule)
        this.pushRulesArr=this.getNewAlarmTitle(this.pushRulesArr)
      },
      /* 根据产品id，获取设备下拉数据*/
      getDeviceList(productId) {
        let that = this
        if (!productId) {
          return
        }
        let ids = productId.split(',')
        let id = ids[ids.length - 1]
        getAction(that.url.devList, {
          productId: id
        }).then((res) => {
          if (res.success) {
            let deviceList = res.result
            let names = ''
            let alarmDev = this.alarmInfo.deviceIdList
            if (deviceList && deviceList.length > 0 && alarmDev && alarmDev.length > 0) {
              this.alarmInfo.deviceIdList.map((deviceId) => {
                deviceList.map((device) => {
                  if (deviceId == device.id) {
                    names += device.code + ','
                  }
                })
              })
              this.alarmDeviceNames = names ? names.substr(0, names.length - 1) : ''
            }
          }
        })
      },
      getRulesData(arg) {
        var that = this
        that.loading = true
        that.ruleList = []
        getAction(that.url.rule, {
          id: that.alarmInfo.id
        }).then((res) => {
          if (res.success) {
            //根据告警字典与后台返回的contentJson,匹配解析json，组装前端需要的数据结构
            that.ruleList = res.result
            for (let i = 0; i < that.ruleList.length; i++) {
              let item = that.ruleList[i]
              let contentsArr = JSON.parse(item.contents)
              item.contentsArr =  this.getNewAlarmTitle(contentsArr)
            }
          }
          if (res.code === 510) {
            that.$message.warning(res.message)
          }
          that.loading = false
        })
      },
      getNewAlarmTitle(contentsArr) {
        let tempArr=[]
        for (let i = 0; i < this.alarmLevelList.length; i++) {
          let levObj=this.alarmLevelList[i]

          for (let k = 0; k < contentsArr.length; k++) {
            let conObj=contentsArr[k]
            if (levObj.level==conObj.level){
              contentsArr[k].title=levObj.title
              tempArr.push(contentsArr[k])
            }
          }
        }
        return tempArr
      },
      /*获取告警级别内容*/
      getAlarmContents() {
        let that = this
        return new Promise(function(resolve, reject) {
          that.alarmLevelList = []
          getAction(that.url.alarmLevel).then((res) => {
            that.alarmLevelList = []
            if (res.success) {
              for (let i = 0; i < res.result.length; i++) {
                let item = res.result[i]
                let param = {
                  /* title: item.levelName,
                   level: item.alarmLevel*/
                  title: item.title,
                  level: item.value
                }
                that.alarmLevelList.push(param)
              }
              resolve( true)
            } else {
              that.$message.warning(res.message)
              reject( false)
            }
          }).catch((err) => {
            that.$message.error(err.message)
            reject( false)
          })
        })
      },
      /**获取脚本场景下拉数据*/
      getSceneList() {
        let that = this
        that.sceneList = []
        getAction(that.url.scene).then((res) => {
          if (res.success) {
            that.sceneList = res.result
          } else {
            that.$message.warning(res.message)
          }
        }).catch((err) => {
          that.$message.warning(err.message)
        })
      },
      /**获取脚本场景*/
      getSceneName(sceneId){
        console.log('changjing===',this.sceneList.find((item) => item.id == sceneId))
         return this.sceneList.find((item) => item.id == sceneId).sceneName
        }
    }
  }
</script>
<style lang="less" scoped>
  //@import '~@assets/less/common.less';
  //@import '~@assets/less/scroll.less';

  .div-container {
    height: 100%;
    background: #ffffff;
    padding: 24px;

    .alarm-info {
      position: relative;
      //margin-bottom: 16px;
      border-radius: 3px;
    }

    .rules-info {
      margin-top: 24px;
      width: 100%;

      .rule-table {
        width: 100%;
        border: 1px solid #e8e8e8;
        text-align: center;
        font-size: 14px;
        margin-bottom: 12px;
      }

      .rule-table td,
      .rule-table th {
        height: 50px;
        border: 1px solid #e8e8e8;
      }
      .trigger-row {
        margin-bottom: 12px;
      }
    }

    .notice-info {
      margin-top: 24px;
      width: 100%;

      .alarm-notice-table {
        width: 100%;
        border: 1px solid #e8e8e8;
        text-align: center;
        font-size: 14px;
        margin-bottom: 16px;
      }

      .alarm-notice-table td,
      .alarm-notice-table th {
        height: 50px;
        border: 1px solid #e8e8e8;
      }
    }

    .p-title {
      margin-bottom: 16px;
      font-family: PingFangSC-Medium;
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);

      .colorTotal {
        padding-left: 7px;
        border-left: 4px solid #1e3674;
      }
    }

    .item-name {
      display: inline-block;
      margin: 12px 8px 0px 6px;
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background: #1e3674;
      //box-shadow: 0 2px 4px 0 rgba(0, 0, 0, .2);
    }

    table.gridtable {
      font-size: 14px;
      width: 100%;

      td {
        border-width: 1px;
        border-style: solid;
        border-color: #e8e8e8;
        padding: 16px 24px;
      }

      .leftTd {
        width: 17%;
        background-color: #fafafa;
        text-align: center;
      }

      .rightTd {
        width: 35%;
        white-space: normal;
        word-break: break-all;
        word-wrap: normal;
      }

      .rightTd-2 {
        width: 83%;
      }
    }

    .alarm-notice-table .custom-colum, .rule-table .custom-colum{
      word-wrap: break-word;word-break:break-all;overflow-wrap: break-word
    }
  }
</style>