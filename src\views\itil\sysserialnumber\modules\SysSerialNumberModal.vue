<template>
  <a-modal
    :title="title"
    :width="width"
    :visible="visible"
    :confirmLoading="confirmLoading"
    switchFullscreen
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭">
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item label="模块名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['moduleName']" placeholder="请输入模块名称"></a-input>
        </a-form-item>
        <a-form-item label="模块编码" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['moduleCode']" placeholder="请输入模块编码"></a-input>
        </a-form-item>
        <a-form-item label="序列号模板" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['configTemplet']" placeholder="请输入序列号模板"></a-input>
        </a-form-item>
        <a-form-item label="预生成序列号个数" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['preMaxNum']" placeholder="请输入预生成序列号存放到缓存的个数"></a-input>
        </a-form-item>
        <a-form-item label="是否自动增长模式" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['isAutoIncrement']" placeholder="请输入是否自动增长模式，0：否  1：是"></a-input>
        </a-form-item>
       

      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>

  import { httpAction } from '@/api/manage'
  import pick from 'lodash.pick'
  import { validateDuplicateValue } from '@/utils/util'


  export default {
    name: "SysSerialNumberModal",
    components: { 
    },
    data () {
      return {
        form: this.$form.createForm(this),
        title:"操作",
        width:800,
        visible: false,
        model: {},
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        validatorRules: {
        },
        url: {
          add: "/sysserialnumber/sysSerialNumber/add",
          edit: "/sysserialnumber/sysSerialNumber/edit",
        }
      }
    },
    created () {
    },
    methods: {
      add () {
        this.edit({});
      },
      edit (record) {
        this.form.resetFields();
        this.model = Object.assign({}, record);
        this.visible = true;
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.model,'moduleCode','configTemplet','maxSerial','preMaxNum','isAutoIncrement','moduleName'))
        })
      },
      close () {
        this.$emit('close');
        this.visible = false;
      },
      handleOk () {
        const that = this;
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            let formData = Object.assign(this.model, values);
            httpAction(httpurl,formData,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
              that.close();
            })
          }
         
        })
      },
      handleCancel () {
        this.close()
      },
      popupCallback(row){
        this.form.setFieldsValue(pick(row,'moduleCode','configTemplet','maxSerial','preMaxNum','isAutoIncrement','moduleName'))
      },

      
    }
  }
</script>