<template>
  <div style="height: 100%">
    <keep-alive exclude="knowledgeBaseInfo">
      <component :is="pageName" :data="data" :fromtype="fromtype" style="height: 100%" />
    </keep-alive>
  </div>
</template>
<script>
import knowledgeSearchIndex from '@views/oneClickHelp/knowledgeSearch/knowledgeSearchIndex.vue'
import knowledgeSearchResult from '@views/oneClickHelp/knowledgeSearch/knowledgeSearchResult.vue'
import knowledgeBaseInfo from '@views/oneClickHelp/knowledgeBase/KnowledgeBaseInfo.vue'
export default {
  name: 'knowledgeSearch',
  data() {
    return {
      isActive: 1,
      data: {},
      fromtype: 0
    }
  },
  components: {
    knowledgeSearchIndex,
    knowledgeSearchResult,
    knowledgeBaseInfo,
  },
  created() {
    this.pButton1(1)
  },
  //使用计算属性
  computed: {
    pageName() {
      switch (this.isActive) {
        case 0:
          return 'knowledgeSearchResult'
        case 1:
          return 'knowledgeSearchIndex'
        default:
          return 'knowledgeBaseInfo'
      }
    },
  },
  methods: {
    pButton1(index) {
      this.isActive = index
    },
    pButton2(index, item, fromtype) {
      this.isActive = index
      this.data = item
      this.fromtype = fromtype || 0
    },
  },
}
</script>