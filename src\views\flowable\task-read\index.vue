<template>
  <a-row style='height: 100%'>
    <a-col style='height: 100%; display: flex; flex-direction: column'>
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0', marginRight: '12px' }" class='card-style'>
        <!-- 查询区域 -->
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="关键字">
                  <a-input v-model='queryParam.keyword' :maxLength='maxLength' :allow-clear='true' autocomplete='off' placeholder='请输入关键字' />
                </a-form-item>
              </a-col>
              <a-col v-show="getVisible('name')" :span="spanValue">
                <a-form-item :label="getTitle('name')">
                  <a-input v-model="queryParam.processInstanceName" :maxLength='maxLength' placeholder="请输入业务标题" :allow-clear='true' />
                </a-form-item>
              </a-col>
              <a-col v-show="getVisible('status')" :span="spanValue">
                <a-form-item :label="getTitle('status')">
                  <a-select v-model="queryParam.status" placeholder="请选择状态"
                            :getPopupContainer="(target) => target.parentNode" :allow-clear='true'>
                    <a-select-option value="CC">待阅</a-select-option>
                    <a-select-option value="YY">已阅</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col v-show="getVisible('startUserName')" :span="spanValue">
                <a-form-item :label="getTitle('startUserName')">
                  <a-select :getPopupContainer='node=>node.parentNode' :allow-clear='true' v-model="queryParam.startUserId"
                            show-search placeholder="请选择发起人" option-filter-prop="children" :filter-option="filterOption">
                    <a-select-option v-for="(item, key) in userList" :key="key" :value="item.username">
                      <div style="display: inline-block; width: 100%" :title="item.realname">
                        {{ item.realname }}
                        <span style="font-size: 6px; color: rgba(0, 0, 0, 0.45);">{{
                            '(' + item.username + ')'
                          }}</span>
                      </div>
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col v-show="getVisible('startTime')" :span="spanValue">
                <a-form-item :label="getTitle('startTime')">
                  <a-range-picker :getCalendarContainer="node=> node.parentNode" style='width: 100%'
                                  v-model='queryParam.searchStartTime' :placeholder="['开始时间', '结束时间']" format='YYYY-MM-DD HH:mm:ss'
                                  showTime @change='onCreatedTimeChange' />
                </a-form-item>
              </a-col>
              <a-col v-show="getVisible('endTime')" :span="spanValue">
                <a-form-item :label="getTitle('endTime')">
                  <a-range-picker :getCalendarContainer="node=> node.parentNode" style='width: 100%'
                                  v-model='queryParam.searchEndTime' :placeholder="['开始时间', '结束时间']" format='YYYY-MM-DD HH:mm:ss' showTime
                                  @change='onEndTimeChange' />
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <span class="table-page-search-submitButtons" style="overflow: hidden;">
                  <a-button icon="search" type="primary" @click="searchQuery">查询</a-button>
                  <a-button icon="reload" style="margin-left: 8px" @click="searchReset">重置</a-button>
                  <a v-if="queryItems.length>0" style="margin-left: 8px" @click="doToggleSearch">{{queryName}}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
        <!-- 查询区域-END -->

        <!--自定义查询项 -->
        <div v-if="toggleSearchStatus" class="custom-query-item">
          <a-checkbox-group v-model="settingQueryItems" :defaultValue="settingQueryItems" style="width:100%"
            @change="onQuerySettingsChange">
            <a-row :gutter="24">
              <template v-for="(item,index) in queryItems">
                <a-col v-show='item.checked' :span='querySpanValue' class='col-checkbox'>
                  <a-checkbox :value="item.dataIndex">
                    <j-ellipsis :length="7" :value="item.title"></j-ellipsis>
                  </a-checkbox>
                </a-col>
              </template>
            </a-row>
          </a-checkbox-group>
        </div>
        <!-- 自定义查询项-END -->
      </a-card>

      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <!-- 操作按钮区域 -->
        <div class="table-operator">
          <a-dropdown v-if="selectedRowKeys.length > 0">
            <a-menu slot="overlay">
              <a-menu-item key="1" @click="batchDel">
                <a-icon type="delete" />
                删除
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </div>

        <!-- table区域-begin -->
        <div>
          <!-- <div class="ant-alert ant-alert-info" style="margin-bottom: 16px">
            <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择
            <a style="font-weight: 600">{{ selectedRowKeys.length }}</a
            >项
            <a style="margin-left: 24px" @click="onClearSelected">清空</a>
            <span style="float: right">
              <a @click="loadData()"><a-icon type="sync" />刷新</a>
              <a-divider type="vertical" />
              <a-popover title="自定义列" trigger="click" placement="leftBottom">
                <template slot="content">
                  <a-checkbox-group @change="onColSettingsChange" v-model="settingColumns" :defaultValue="settingColumns">
                    <a-row style="width: 400px">
                      <template v-for="(item, index) in defColumns">
                        <template v-if="item.key != 'rowIndex' && item.dataIndex != 'action'">
                          <a-col :key="index" :span="12"
                            ><a-checkbox :value="item.dataIndex"
                              ><j-ellipsis :value="item.title" :length="10"></j-ellipsis></a-checkbox
                          ></a-col>
                        </template>
                      </template>
                    </a-row>
                  </a-checkbox-group>
                </template>
                <a><a-icon type="setting" />设置</a>
              </a-popover>
            </span>
          </div> -->
          <a-table
            ref="table"
            bordered rowKey="id"
            :columns="columns"
            :dataSource="dataSource"
            :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
            :pagination="ipagination"
            :loading="loading"
            @change="handleTableChange">
            <span slot="status" slot-scope="status">
              <a-tag color="#fa8c16" v-if="status == 6"> 待阅</a-tag>
              <a-tag color="#87d068" v-if="status == 7"> 已阅</a-tag>
            </span>
            <div slot="filterDropdown">
              <a-card>
                <a-checkbox-group @change="onColSettingsChange" v-model="settingColumns" :defaultValue="settingColumns">
                  <a-row style="width: 400px">
                    <template v-for="(item, index) in defColumns">
                      <template v-if="item.key != 'rowIndex' && item.dataIndex != 'action'">
                        <a-col :span="12">
                          <a-checkbox :value="item.dataIndex">
                            <j-ellipsis :value="item.title" :length="10"></j-ellipsis>
                          </a-checkbox>
                        </a-col>
                      </template>
                    </template>
                  </a-row>
                </a-checkbox-group>
              </a-card>
            </div>

            <template slot='tooltip' slot-scope='text'>
              <a-tooltip placement='top'
                :title='text'
                trigger='hover'>
                <div class='tooltip'>
                  {{ text }}
                </div>
              </a-tooltip>
            </template>
            <template slot='tooltip2' slot-scope='text'>
              <a-tooltip placement='topLeft'
                :title='text'
                trigger='hover'>
                <div class='tooltip'>
                  {{ text }}
                </div>
              </a-tooltip>
            </template>

            <a-icon slot="filterIcon" type="setting" :style="{ fontSize: '16px', color: '#108ee9' }" />

            <span slot="action" slot-scope="text, record">
              <a @click="btnView(record)">查看</a>
            </span>
          </a-table>
        </div>
        <!-- table区域-end -->
      </a-card>
      <!-- 一查看详情区域 -->
      <process-instance-info-modal ref="processInstanceInfoModalForm" @ok="modalFormOk" @close="modalFormOk"></process-instance-info-modal>
    </a-col>
  </a-row>
</template>

<script>
  import {
    getUserList
  } from '@/api/api'
  import JSuperQuery from '@/components/jeecg/JSuperQuery.vue'
  import JInput from '@/components/jeecg/JInput.vue'
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import Vue from 'vue'
  import {
    filterObj, getUrlParamValue
  } from '@/utils/util'
  import {
    putAction
  } from '@/api/manage'
  import ProcessInstanceInfoModal from '../process-instance/modules/ProcessInstanceInfoModal'
  import {
    YqFormSeniorSearchLocation
  } from '@/mixins/YqFormSeniorSearchLocation'
  import { getAction } from '../../../api/manage'

  export default {
    name: 'taskToRead',
    mixins: [JeecgListMixin, YqFormSeniorSearchLocation],
    components: {
      JSuperQuery,
      JInput,
      ProcessInstanceInfoModal,
    },
    data() {
      return {
        maxLength:50,
        formItemLayout: {
          labelCol: {
            style: 'width:80px'
          },
          wrapperCol: {
            style: 'width:calc(100% - 80px)'
          }
        },
        disableMixinCreated: true,
        userList: [],
        description: '单表示例列表',
        //字典数组缓存
        sexDictOptions: [],
        importExcelUrl: `${window._CONFIG['domianURL']}/test/jeecgDemo/importExcel`,
        //列设置
        settingColumns: [],
        //列定义
        columns: [{
            title: '序号',
            width: 60,
            dataIndex: '',
            key: 'rowIndex',
            isUsed: false,
            customCell: () => {
              let cellStyle = 'text-align:center;'
              return {
                style: cellStyle
              }
            },
            customRender: function (t, r, index) {
              return parseInt(index) + 1
            },
          },
          {
            title: '业务标题',
            dataIndex: 'name',
            isUsed: true,
            customCell: () => {
              let cellStyle = 'text-align:center;min-width: 150px;max-width:300px'
              return {
                style: cellStyle
              }
            },
            scopedSlots: {
              customRender: 'tooltip'
            }
          },
          {
            title: '发起人',
            dataIndex: 'startUserName',
            isUsed: true,
            customCell: () => {
              let cellStyle = 'text-align:center;width: 200px'
              return {
                style: cellStyle
              }
            }
          },{
            title: '状态',
            dataIndex: 'status',
            scopedSlots: {
              customRender: 'status'
            },
            customCell: () => {
              let cellStyle = 'text-align:center;width:100px'
              return {
                style: cellStyle
              }
            },
          },
          {
            title: '开始时间',
            dataIndex: 'startTime',
            isUsed: true,
            customCell: () => {
              let cellStyle = 'text-align:center;width:180px'
              return {
                style: cellStyle
              }
            },
            // sorter: true
          },
          {
            title: '结束时间',
            dataIndex: 'endTime',
            isUsed: true,
            customCell: () => {
              let cellStyle = 'text-align:center;width:180px'
              return {
                style: cellStyle
              }
            },
            // sorter: true
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            fixed: 'right',
            width: 120,
            isUsed: false,
            scopedSlots: {
              /*  filterDropdown: 'filterDropdown',
                filterIcon: 'filterIcon',*/
              customRender: 'action',
            },
          },
        ],
        url: {
          list: '/flowable/processInstance/listCcToMe',
        },
      }
    },
    props: {
      status: {
        type: String,
        default: 'CC'
      }
    },
    watch: {
      // 监听路由的查询参数变化,为了解决跳转到相同路由携带的参数key不同时,页面不刷新问题
      '$route': {
        immediate: true,
        handler(newKey) {
          // 需要重置表单数据
          this.searchReset()
        }
      },
      // 设置CC待阅, YY已阅
      status: {
        handler(nv) {
          console.log(nv)
          if(nv) {
            this.queryParam['status'] = nv
          }
        },
        immediate: true
      }
    },
    created() {
      //this.initColumns()
      this.getuserList()
      this.getColumns(this.columns)
    },
    methods: {
      onCreatedTimeChange: function (value, dateString) {
        this.queryParam.taskCreatedAfter = dateString[0]
        this.queryParam.taskCreatedBefore = dateString[1]
      },
      onEndTimeChange: function (value, dateString) {
        this.queryParam.taskEndAfter = dateString[0]
        this.queryParam.taskEndBefore = dateString[1]
      },
      filterOption(input, option) {
        return (
          option.componentOptions.children[0].children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
        )
      },
      getuserList() {
        let param = {
          pageSize: 10000
        }
        getUserList(param).then((res) => {
          if (res.success) {
            this.userList = res.result.records
          }
        })
      },
      getQueryParams() {
        //高级查询器
        let sqp = {}
        if (this.superQueryParams) {
          sqp['superQueryParams'] = encodeURI(this.superQueryParams)
          sqp['superQueryMatchType'] = this.superQueryMatchType
        }
        const processDefinitionKey = getUrlParamValue('key')
        if (!!processDefinitionKey) {
          this.queryParam.processDefinitionKey = processDefinitionKey
        } else {
          this.queryParam.processDefinitionKey = null
        }
        if (this.status) {
          this.queryParam['status'] = this.status
        }
        var param = Object.assign(sqp, this.queryParam, this.isorter, this.filters)

        param.field = this.getQueryField()
        param.pageNo = this.ipagination.current
        param.pageSize = this.ipagination.pageSize
        return filterObj(param)
      },
      onetomany: function () {
        this.$refs.jeecgDemoTabsModal.add()
        this.$refs.jeecgDemoTabsModal.title = '编辑'
      },
      btnView(record) {
        this.$refs.processInstanceInfoModalForm.init(record.id, true, record)
        this.$refs.processInstanceInfoModalForm.title = '流程实例信息'
        this.$refs.processInstanceInfoModalForm.disableSubmit = false
      },
      btnClaim() {},
      btnRead(id) {
        let taskIds = id ? [id] : this.selectedRecords.map((record) => {
          return record.id
        })
        if (taskIds.length == 0) {
          this.$message.error('请选择要设置为已阅的记录')
          return
        }
        putAction('/flowable/task/read', {
          taskIds
        }).then((res) => {
          this.$message.error(res.message)
          this.loadData();
        })
      },
      //跳转单据页面
      jump() {
        this.$router.push({
          path: '/jeecg/helloworld'
        })
      },
      //列设置更改事件
      onColSettingsChange(checkedValues) {
        var key = this.$route.name + ':colsettings'
        Vue.ls.set(key, checkedValues)
        this.settingColumns = checkedValues
        const cols = this.defColumns.filter((item) => {
          if (item.key == 'rowIndex' || item.dataIndex == 'action') {
            return true
          }
          if (this.settingColumns.includes(item.dataIndex)) {
            return true
          }
          return false
        })
        this.columns = cols
      },
      initColumns() {
        //权限过滤（列权限控制时打开，修改第二个参数为授权码前缀）
        //this.defColumns = colAuthFilter(this.defColumns,'testdemo:');

        var key = this.$route.name + ':colsettings'
        let colSettings = Vue.ls.get(key)
        if (colSettings == null || colSettings == undefined) {
          let allSettingColumns = []
          this.defColumns.forEach(function (item, i, array) {
            allSettingColumns.push(item.dataIndex)
          })
          this.settingColumns = allSettingColumns
          this.columns = this.defColumns
        } else {
          this.settingColumns = colSettings
          const cols = this.defColumns.filter((item) => {
            if (item.key == 'rowIndex' || item.dataIndex == 'action') {
              return true
            }
            if (colSettings.includes(item.dataIndex)) {
              return true
            }
            return false
          })
          this.columns = cols
        }
      },
    },
  }
</script>
<style scoped lang='less'>
  @import '~@assets/less/common.less';
  @import '~@assets/less/YQCommon.less';
  @import '~@assets/less/scroll.less';
</style>