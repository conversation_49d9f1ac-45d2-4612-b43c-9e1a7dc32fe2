// 设置节点边线的状态属性
import { getAction, postAction, httpAction } from '@/api/manage'
import { Vector } from '@antv/x6'
import { darkenColor } from '@/utils/util'
import {shapeExtends} from '../graph/shape'

export default {
  data() {
    return {
      alarmLevelList: [],
      edgeAnis: {},
      nodeAnis: {},
      statusAniNodes: [],
      showStatus: true,
      statusInterval: null,
      onOffColors: { onLine: '#1BCB3C', offLine: '#FF1D42' },
      lineColors: { connected: '#52c41a', notConnected: '#ff4d4f' },
      alarmColor:"#FFAE38",
      darkAlarmColor:"#FFAE38",
      darkOfflineColor: '#FF1D42'
    }
  },
  created() {
    let config = window.config.TopoStatuColors
    if (Object.prototype.toString.call(config) !== '[object Object]') {
      config = {}
    }
    this.onOffColors = Object.assign(this.onOffColors, config)
    this.darkOfflineColor = darkenColor(this.onOffColors.offLine, 60)
    this.darkAlarmColor = darkenColor(this.alarmColor, 60)
    let lineConfig = window.config.TopoLineColors
    if (Object.prototype.toString.call(lineConfig) !== '[object Object]') {
      lineConfig = {}
    }
    this.lineColors = Object.assign(this.lineColors, lineConfig)
  },
  watch: {
    'globalGridAttr.topoConfig.edgeAni'(e) {
      if (this.graph && this.operate === 'create') {
        let edges = this.graph.getEdges()
        this.setAllEdgesAni(edges)
      }
    }
  },
  methods: {
    setAllEdgesAni(edges) {
      edges.forEach((el,idx) => {
        if (this.globalGridAttr.topoConfig.edgeAni && el.data.status === 1) {
          let temTimer = setTimeout(()=>{
            this.setEdgeAni(el)
            clearTimeout(temTimer)
            temTimer = null
          },10*idx)
        } else {
          this.clearEdgeAni(el)
        }
      })
    },
   //节点状态动画
    //给异常节点添加动画
    setNodeAnimate(node) {
      if (this.nodeAnis[node.id]&&this.nodeAnis[node.id].length>0) {
        // 如果已经存在动画，则先停止之前的动画
        this.nodeAnis[node.id].forEach(animate => {
          this.stopAni(animate)
        })
      }
      this.nodeAnis[node.id] = []
      // 节点动画
      const view = this.graph.findView(node)
      let nodeData = node.getData()
      if (nodeData  && view) {
        let value
        if( nodeData.status !== undefined && nodeData.status == 0){
          value = `${this.onOffColors.offLine};${this.darkOfflineColor};${this.onOffColors.offLine}`
        }else if(nodeData.alarmNode){
          value = `${this.alarmColor};${this.darkAlarmColor};${this.alarmColor}`
        }else{
          return;
        }
        // 主填充颜色动画
        let anShape = shapeExtends[node.shape]
        let colorAni = view.animate(anShape, {
          attributeType: 'XML',
          attributeName: 'fill',
          values: value,
          dur: '1.5s',
          repeatCount: 'indefinite'
        })
        this.nodeAnis[node.id].push(colorAni)
        const body = view.findOne('body')
        let scaleAni = body.animate([
          { transform: 'scale(1)' },
          { transform: 'scale(0.99)' },
          { transform: 'scale(1)'}
        ], {
          duration: 1500,
          iterations: Infinity
        })
        this.nodeAnis[node.id].push(scaleAni)
      }
    },
    //清除节点动画
    clearNodesAni(node) {
      // 停止所有动画
      for (let key in this.nodeAnis){
        if(this.nodeAnis[key]){
          this.nodeAnis[key].forEach(animate => {
            this.stopAni(animate)
          })
          const view = this.graph.findViewByCell(key)
          if(view){
            view.remove("animate")
          }
        }
      }
      this.nodeAnis = {}
    },
    stopAni(animate){
      if (animate && typeof animate === 'function') {
        animate() // 停止动画
      } else if (animate && animate.finish && animate.effect) {
        const timing = animate.effect.getComputedTiming()
        if (timing.iterations !== Infinity) {
          animate.finish() // 仅在迭代次数有限时停止动画
        } else {
          animate.cancel() // 对于无限迭代的动画，使用 cancel 方法
        }
      }
    },
    //设置状态动画
    setStatusAni() {
      this.statusInterval = setInterval(() => {
        this.showStatus = !this.showStatus
        this.setNodeOpacity()
      }, 1000)
    },
    setNodeOpacity() {
      let nodes = this.graph.getNodes()
      nodes.forEach(el => {
        if (el.data.status == 0 || el.data.alarmNode) {
          let color = el.data.alarmNode ? this.alarmColor : this.onOffColors.offLine
          el.setAttrs({
            body: {
              fill: this.showStatus ? color : this.onOffColors.onLine
            }
          })
        }
      })
    },
    //设置边线动画
    setEdgeAni(edge) {
      //省资源模式不加载动画
      if (window.config.simpleModel !== 0) return
      if (this.globalGridAttr.topoConfig.edgeAni && !this.edgeAnis[edge.id]) {
        let tem = {}
        tem.view = this.graph.findViewByCell(edge)
        tem.path = tem.view.findOne('path')
        if (tem.path) {
          tem.token = Vector.create('circle', { r: 6, fill: '#feb662',opacity:0})
          tem.token.appendTo(tem.path.parentNode)
          this.edgeAnis[edge.id] = tem
          let alongPathTimer = setTimeout(()=>{
            clearTimeout(alongPathTimer)
            alongPathTimer = null
            this.edgeAnis[edge.id].stop = tem.token.animateAlongPath(
              {
                dur: this.globalGridAttr.topoConfig.aniTime + 's',
                repeatCount: 'indefinite',
                start:()=>{
                  tem.token.attr({ opacity: 1 })
                },
                complete: () => {
                }
              },
              tem.path
            )

          },50)

        }

      }

    },
    //清除边线动画
    clearEdgeAni(el) {
      if (this.edgeAnis[el.id]) {
        this.edgeAnis[el.id].stop()
        this.edgeAnis[el.id].token.remove()
        delete this.edgeAnis[el.id]
      }
    },
    // 设置拓扑图节点告警状态 节点背景告警级别设定的告警颜色
    setNodeAlarm(node, level) {
      // let alarmLevelInfo = this.alarmLevelList.find(el => el.value == level)
      //项目暂不考虑离线告警
      if(node.data.status == 0){
        return;
      }
      node.setData({
        alarmNode: true,
        alarmColor:this.alarmColor, //alarmLevelInfo ? alarmLevelInfo.color : '#D81E06'
      })
      node.setAttrs({
        image: {
          opacity: 1
        },
        body: {
          opacity: 1,//this.showStatus ? 1 : 0,
          fill: this.alarmColor//alarmLevelInfo ? alarmLevelInfo.color : '#D81E06'
        }
      })
      this.setNodeAnimate(node)
    },
    // 拓扑节点在线 离线改变
    setNodeOnAndOff(node, status) {
      // console.log("设置状态",node,status)
      //编辑状态下没有设备码的节点不允许设置状态
      if(!node.data.deviceCode && this.operate==="create"){
       return;
      }
      let colors = this.onOffColors
      node.setData({
        status: status
      })
      node.setAttrs({
        image: {
          opacity: 1
        },
        body: {
          opacity: 1,//status === 0&&!this.showStatus ? 0 : 1,
          fill: status === 0 ? colors.offLine : colors.onLine
        }
      })
      this.setNodeAnimate(node)
    },
    // 边线状态改变
    setEdgeStatus(edge) {
      let colors = this.lineColors
      if (edge.data.edgeType === 'connecting') {
        let source = edge.getSourceNode()
        let target = edge.getTargetNode()
        //如果拓扑图关闭边线状态 边线全显示绿色
        if (!this.globalGridAttr.topoConfig.edgeStatus) {
          edge.attr(
            'line/stroke',
            colors.connected
          )
          return
        }
        //当起始节点都在线时 边线显示在线状态
        if (source.data.status === 1 && target.data.status === 1) {
          edge.attr(
            'line/stroke',
            colors.connected
          )
          edge.data.status = 1
          // this.setEdgeAni(edge)
        } else {
          edge.attr(
            'line/stroke',
            colors.notConnected
          )
          edge.data.status = 0
          // this.clearEdgeAni(edge)
        }
      }
    },
    // 获取设备告警级别列表
    async getAlarmLevelData() {
      await getAction('/alarm/alarmLevel/getLevelList').then((res) => {
        if (res.success) {
          this.alarmLevelList = res.result
        }
      })
    }
  }
}