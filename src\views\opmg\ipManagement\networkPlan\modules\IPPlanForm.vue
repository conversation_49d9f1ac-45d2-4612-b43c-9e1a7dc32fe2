<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container>
      <a-form :form="form" slot="detail">
        <a-row>
          <div class='colorBox'>
            <span class="colorTotal">IP信息</span>
          </div>
          <a-col :span="24">
            <a-form-item label="所属子网组" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-select v-decorator="['subnetGroupId', validatorRules.subnetGroupId]" :allowClear="true"
                @change="groupChange" placeholder="请选择所属子网组">
                <a-select-option v-for="item in groupList" :key="item.id">{{item.title}}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="所属子网" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-select v-decorator="['subnetId', validatorRules.subnetId]" :allowClear="true" @change="subnetChange"
                placeholder="请选择所属子网">
                <a-select-option v-for="item in childList" :key="item.id">{{item.title}}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="所属网段" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-select v-decorator="['segmentId', validatorRules.segmentId]" :allowClear="true" placeholder="请选择所属网段">
                <a-select-option v-for="item in segmentList" :key="item.id">{{item.title}}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="使用人" :labelCol="labelCol" :wrapperCol="wrapperCol" :required="true">
              <J-select-user-radio v-model="model.userId" :multi="false" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="IP地址" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['ipAddress',validatorRules.ipAddress]" :allowClear="true" autocomplete="off"
                placeholder="请输入IP地址">
              </a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="MAC地址" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['macAddress',validatorRules.macAddress]" :allowClear="true" autocomplete="off"
                placeholder="请输入MAC地址">
              </a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="联系电话" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['phone',validatorRules.phone]" :allowClear="true" autocomplete="off"
                placeholder="请输入联系电话"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="是否启用" :labelCol="labelCol1" :wrapperCol="wrapperCol1" :required="true">
              <a-switch v-decorator="['enabled', { initialValue: false, valuePropName: 'checked'  }]"
                checked-children='是' un-checked-children='否' />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="是否扫描" :labelCol="labelCol1" :wrapperCol="wrapperCol1" :required="true">
              <a-switch v-decorator="['scan', { initialValue: false, valuePropName: 'checked'  }]" checked-children='是'
                un-checked-children='否' />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="备注" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-textarea :autoSize="{ minRows: 1, maxRows: 4 }" v-decorator="['remark',validatorRules.remark]"
                :allowClear="true" autocomplete="off" placeholder="请输入备注"></a-textarea>
            </a-form-item>
          </a-col>
          <div class='colorBox' v-if="model.extendList && model.extendList.length > 0">
            <span class="colorTotal">附加字段</span>
          </div>
          <a-col :span="24" v-for="(item,index) in model.extendList" :key="index">
            <a-form-item :label="item.name" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <div style='white-space: nowrap'>
                <a-input v-if="item.type == '单行文本'" v-decorator="[
                            item.connectName + '_' + index,
                            {
                              initialValue: item.value,
                            },
                          ]" :placeholder="'请输入'+item.name" :allowClear="true" autocomplete="off"
                  @change="changeConParam($event, index)"></a-input>
                <a-textarea v-if="item.type == '文本文档'||item.type == '多行文本'" v-decorator="[
                            item.connectName + '_' + index,
                            {
                              initialValue: item.value,
                            },
                          ]" :placeholder="'请输入'+item.name" :allowClear="true" autocomplete="off"
                  @change="changeConParam($event, index)"></a-textarea>
                <j-dict-select-tag v-if="item.type == '下拉框'" v-decorator="[
                            item.connectName + '_' + index,
                            {
                              initialValue: item.value,
                            },
                          ]" :dictCode="item.dictType" :placeholder="'请选择'+item.name" :trigger-change="true"
                  @change="changeConParam1($event, index)" :allowClear="true" />
                <a-input-number v-if="item.type == '计数器'" v-decorator="[
                            item.connectName + '_' + index,
                            {
                              initialValue: item.value,
                            },
                          ]" :placeholder="'请输入'+item.name" :allowClear="true" autocomplete="off"
                  @change="changeConParam1($event, index)" />
              </div>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
  import {
    httpAction,
    getAction
  } from '@/api/manage'
  import pick from 'lodash.pick'
  import JFormContainer from '@/components/jeecg/JFormContainer'
  import JDictSelectTag from '@/components/dict/JDictSelectTag'
  import JSelectUserRadio from '@/components/flowable/JSelectUserRadio'

  export default {
    name: 'DevopsBackupProForm',
    components: {
      JFormContainer,
      JDictSelectTag,
      JSelectUserRadio
    },
    data() {
      return {
        form: this.$form.createForm(this),
        childList: [],
        groupList: [],
        segmentList: [],
        model: {},
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          },
        },
        labelCol1: {
          xs: {
            span: 24
          },
          sm: {
            span: 10
          },
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          },
        },
        wrapperCol1: {
          xs: {
            span: 24
          },
          sm: {
            span: 12
          },
        },
        confirmLoading: false,
        validatorRules: {
          subnetGroupId: {
            rules: [{
              required: true,
              message: '请选择所属子网组'
            }, ],
          },
          subnetId: {
            rules: [{
              required: true,
              message: '请选择所属子网'
            }, ],
          },
          segmentId: {
            rules: [{
              required: true,
              message: '请选择所属网段'
            }, ],
          },
          ipAddress: {
            rules: [{
              required: true,
              message: '请输入IP地址'
            }, {
              pattern: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
              message: '请输入正确的IP地址'
            }],
          },
          macAddress: {
            rules: [{
              pattern: /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/,
              message: '请输入正确的MAC地址'
            }]
          },
          phone: {
            rules: [{
              pattern: /^1[345789]\d{9}$/,
              message: '请输入正确的联系电话'
            }]
          },
          remark: {
            rules: [{
              max: 200,
              message: '备注长度不能超过200个字符'
            }]
          },
        },
        url: {
          add: '/devops/ip/plan/add',
          edit: '/devops/ip/plan/edit',
          queryById: '/devops/ip/plan/queryById',
          queryAll: '/devops/ip/planExtend/queryAll',
          group: '/devops/ip/queryTree',
        },
      }
    },
    created() {
      this.getGroup()
    },
    methods: {
      getGroup() {
        getAction(this.url.group, {
          id: 0,
          type: 0
        }).then((res) => {
          this.groupList = res.result
        })
      },
      getChild(id) {
        getAction(this.url.group, {
          id: id,
          type: 1
        }).then((res) => {
          this.childList = res.result
        })
      },
      getSegment(id) {
        getAction(this.url.group, {
          id: id,
          type: 2
        }).then((res) => {
          this.segmentList = res.result
        })
      },
      groupChange(value) {
        let childId = value
        this.getChild(childId)
      },
      subnetChange(value) {
        let subnetId = value
        this.getSegment(subnetId)
      },
      show(record) {
        getAction(this.url.queryById, {
          id: record.id
        }).then((res) => {
          if (res.success) {
            this.getChild(res.result.subnetGroupId)
            this.getSegment(res.result.subnetId)
            this.edit(res.result);
          }
        });
      },
      add({}, data) {
        this.getChild(data.id)
        this.getSegment(data.children[0].id)
        this.edit({}, data)
      },
      edit(record, data) {
        getAction(this.url.queryAll).then((res) => {
          if (res.success) {
            if (record.extendList && record.extendList.length > 0) {
              this.model.extendList = record.extendList
            } else {
              this.model.extendList = res.result
            }
            this.$forceUpdate();
          }
        })
        this.form.resetFields()
        record['scan'] = Boolean(record['scan'])
        record['enabled'] = Boolean(record['enabled'])
        this.model = Object.assign({}, record)
        if (data != null) {
          this.model.subnetGroupId = data.id
          this.model.subnetId = data.children[0].id
          this.model.segmentId = data.children[0].children[0].id
        }
        this.visible = true
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.model, 'segmentId', 'subnetId', 'subnetGroupId', 'scan', 'remark',
            'enabled', 'phone', 'macAddress', 'ipAddress'))
        })
      },
      changeConParam1(e, index) {
        this.model.extendList[index].value = e
      },
      changeConParam(e, index) {
        this.model.extendList[index].value = e.target.value
      },
      submitForm() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.model.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'put'
            }
            values['enabled'] = Number(values['enabled'])
            values['scan'] = Number(values['scan'])
            let formData = Object.assign(this.model, values)
            httpAction(httpurl, formData, method)
              .then((res) => {
                if (res.success) {
                  that.$message.success(res.message)
                  that.$emit('ok')
                } else {
                  that.$message.warning(res.message)
                }
              })
              .finally(() => {
                that.confirmLoading = false
              })
          }
        })
      },
      popupCallback(row) {
        this.form.setFieldsValue(pick(row, 'segmentId', 'enabled', 'scan', 'subnetId', 'subnetGroupId', 'remark',
          'phone', 'macAddress', 'ipAddress'))
      },
    },
  }
</script>
<style lang='less' scoped='scoped'>
  .colorBox {
    margin-bottom: 10px;
  }

  .colorTotal {
    padding-left: 7px;
    border-left: 4px solid #1e3674;
  }
</style>