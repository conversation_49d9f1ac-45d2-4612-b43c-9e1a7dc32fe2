<template>
  <div style="height: 100%">
    <component :is="pageName" :data="data" />
  </div>
</template>
<script>
import DevopsAutoInspectionList from './DevopsAutoInspectionList'
import DevopsAutoInspectionDetails from './modules/DevopsAutoInspectionDetails'
export default {
  name: 'DevopsAutoInspectionManage',
  data() {
    return {
      isActive: 0,
      data: {},
    }
  },
  components: {
    DevopsAutoInspectionList,
    DevopsAutoInspectionDetails,
  },
  created() {
    this.pButton1(0)
  },
  //使用计算属性
  computed: {
    pageName() {
      switch (this.isActive) {
        case 0:
          return 'DevopsAutoInspectionList'
          break

        default:
          return 'DevopsAutoInspectionDetails'
          break
      }
    },
  },
  methods: {
    pButton1(index) {
      this.isActive = index
    },
    pButton2(index, item) {
      this.isActive = index
      this.data = item
    },
  },
}
</script>