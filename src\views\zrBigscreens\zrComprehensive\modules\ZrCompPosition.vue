<template>
  <div class='zr-comp-position'>
    <zr-bigscreen-title title='物理位置'></zr-bigscreen-title>
    <slot>
      <div class='pos-content' ref='posContent'>
        <vue-seamless-scroll
          :data='listData'
          :class-option='classOption'
          class='warp'
        >
          <ul class='ul-item'>
            <li class='li-item' v-for='(item, index) in listData' :key='index'>
              <div class='pos-icon'></div>
              <div class='pos-info' :title='item.posInfo'>
                <span>{{ item.posInfo }}</span>

              </div>
              <div class='device-block' :title='item.deviceNames'>
                <span>{{ item.deviceNames }}</span>
              </div>
            </li>
          </ul>
        </vue-seamless-scroll>
      </div>
    </slot>

  </div>
</template>
<script>
import ZrBigscreenTitle from '@views/zrBigscreens/modules/ZrBigscreenTitle.vue'
import vueSeamlessScroll from 'vue-seamless-scroll'
import resizeObserverMixin from '@views/statsCenter/com/resizeObserverMixin'

export default {
  name: 'ZrBusinessPosition',
  components: { ZrBigscreenTitle, vueSeamlessScroll },
  mixins: [resizeObserverMixin],
  data() {
    return {
      listData: [
        {
          id: 1,
          posInfo: '01楼1101机房',
          deviceNames: '路由器\n交换机A、交换机B、服务器1、服务器10'
        },
        {
          id: 2,
          posInfo: '01楼1102机房',
          deviceNames: '路由器\n交换机A、交换机B、服务器11、服务器21、服务器31'
        },
       /* {
          id: 3,
          posInfo: '01楼1103机房',
          deviceNames: '路由器\n交换机A、交换机B、服务器11、服务器21、服务器31'
        },*/
      ],
      classOption: {
        limitMoveNum: 3,
        direction: 2,
        singleWidth: 238 ,
        autoPlay:false,
      }
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    // 屏幕变化回调
    resizeObserverCb() {
      this.setPlayState()
    },
    //设置滚动状态
    setPlayState() {
      if (this.$refs.posContent && this.listData.length) {
        let bounded = this.$refs.posContent.getBoundingClientRect()
        this.maxNum = Math.floor(bounded.width / 214)
        if (this.maxNum>0 && this.maxNum < this.listData.length) {
          this.classOption.limitMoveNum = this.maxNum
          this.classOption.autoPlay = true
          return
        }
      }
      this.classOption.autoPlay = false
      this.classOption.limitMoveNum = this.listData.length + 1
    }
  }
}
</script>

<style scoped lang='less'>
.zr-comp-position {
  height: 100%;
  .pos-content {
    padding: 16px 0px;
  }
}

.warp {
  width: 100%;
  height: 186px;
  margin: 0 auto;
  overflow: hidden;

  .ul-item {
    list-style: none;
    padding: 0;
    margin: 0 auto;
    display: flex;

    .li-item {
      width: 214px;
      height: 186px;
      margin-right: 24px;
      padding: 20px 36px 20px 43px;
      background: rgba(29, 78, 140, 0.25);
      position: relative;
      .pos-icon {
        position: absolute;
        width: 10px;
        height: 13px;
        background-image: url(/zrBigScreen/pos.png);
        background-size: 100% 100%;
        left: 22px;
        top: 30px;
      }
      .pos-info {
        width: 100%;
        font-weight: 400;
        font-size: 20px;
        color: rgba(137, 218, 255, 0.95);

        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .device-block {
        white-space: pre-line;
        font-weight: 400;
        margin-top: 8px;
        font-size: 14px;
        line-height: 38px;
        height:110px;
        overflow: hidden;
        color: rgba(237, 245, 255, 0.95);
      }
    }
  }
}
</style>