<template>
  <j-modal
    :title='title'
    :width='width'
    :centered='true'
    :visible='visible'
    :destroyOnClose='true'
    @cancel='handleCancel'
    :footer='null'
  >
    <a-spin :spinning='confirmLoading'>
      <j-form-container :disabled='disableSubmit'>
        <a-form-model ref='form' :model='model' :rules='validatorRules' slot='detail' v-bind='formItemLayout'>
          <a-row>
            <a-col :span='24' v-if='isConfirmSharing===false'>
              <a-form-model-item label='有效期' prop='expirationDayNum'>
                <a-radio-group v-model='model.expirationDayNum' :options='timeRadioOption' @change='changeShareTime'/>
              </a-form-model-item>
            </a-col>
            <a-col  :span='24' v-if='isConfirmSharing===true'>
              <div>
                <div>链接：{{model.link}}</div>
                <div style='margin:20px 0px 30px'>
                  <span>提取码：{{model.password}}</span>
                  <span style='margin-left: 20px' v-html='timeTips'></span>
                </div>
              </div>
            </a-col>
            <a-col :span='24' style='text-align: center'>
              <a-button type='primary' @click='createLink' v-if='isConfirmSharing===false'>创建链接</a-button>
              <a-button type='primary' @click='copyAndShareLink' id='copyLink' :data-clipboard-text="shareContent" v-if='isConfirmSharing===true' >复制链接及提取码</a-button>
            </a-col>
          </a-row>
        </a-form-model>
      </j-form-container>
    </a-spin>
  </j-modal>
</template>
<script>
import { getAction, postAction } from '@api/manage'
export default {
  name: 'AddShareModal',
  data() {
    return {
      title: '新增',
      width: '600px',
      disableSubmit: false,
      visible: false,
      confirmLoading: false,
      formItemLayout: {
        labelCol: {
          xs:{span:24 },
          sm:{span:24},
          md:{span:3}
        },
        wrapperCol: {
          xs:{span:24 },
          sm:{span:24},
          md:{span:20}
        }
      },
      knowledgeId:'',
      isConfirmSharing:false,
      timeTips:'链接<span style="color:#ff4d4f">1天</span>后失效',
      timeRadioOption: [
        { label: '1天', value: 1 ,tips:'链接<span style="color:#ff4d4f">1天</span>后失效'},
        { label: '7天', value: 7,tips:'链接<span style="color:#ff4d4f">7天</span>后失效' },
        { label: '30天', value: 30,tips:'链接<span style="color:#ff4d4f">30天</span>后失效' },
        { label: '永久有效', value: 0,tips:'链接<span style="color:#ff4d4f">永久有效</span>' }],
      model: {
        expirationDayNum:1
      },
      validatorRules: {
        comment: [
          { required: true, message: '请输入评论信息' },
          { min: 1,max: 100,message: '评论长度应在1-100之间'}
        ]
      },
      url: {
        add: '/kbase/knowledges/share/add',
        password:'/kbase/knowledges/share/queryById'
      },
      shareContent:'',
      shareRoute:'/#'+"/knowledgeManagement/Sharing?shareId="
    }
  },
  mounted() {
    if (!!window.location.port) {
      this.shareRoute = window.location.href.slice(0, window.location.href.indexOf(window.location.port) + window.location.port.length) + this.shareRoute
    } else {
      //服务器端口为80时
      this.shareRoute  = window.location.href.slice(0, window.location.href.indexOf(window.location.host) + window.location.host.length) + this.shareRoute
    }
  },
  methods: {
    createLink(){
      postAction(this.url.add,{knowledgeId:this.knowledgeId,expirationDayNum:this.model.expirationDayNum}).then((res)=>{
        if(res.success){
          this.$message.success('创建分享链接成功')
          this.model.link+=res.result.shareId
          this.model.password=res.result.password
          this.isConfirmSharing=true
        }else {
          this.$message.warning(err.message)
        }
      }).catch((err)=>{
        this.$message.warning(err.message)
      })
    },
    copyAndShareLink(e) {
      let that = this
      if (that.model.password) {
        this.shareContent = "链接：" + this.model.link + '   分享码：' + this.model.password
        // 兼容非安全域，非安全域下不可使用navigator.clipboard.writeText
        if (navigator.clipboard && window.isSecureContext) {
          navigator.clipboard.writeText(this.shareContent).then(() => {
            this.$message.success('复制成功')
            this.close()
          }).catch((error) => {
            this.$message.warning('复制失败，请手动复制')
            //console.error("复制失败，请手动复制");
          })
        } else {
          var ele = document.createElement("input");
          ele.value = this.shareContent;
          document.body.appendChild(ele);
          ele.select();
          document.execCommand("copy");
          document.body.removeChild(ele);
          if (document.execCommand("copy")) {
            this.$message.success('复制成功')
            this.close()
          } else {
            this.$message.warning('复制失败，请手动复制')
            //console.error("复制失败，请手动复制");
          }
        }
      } else {
        this.$message.warning('链接或提取码为空，复制失败')
      }
    },
    changeShareTime(value){
     this.timeRadioOption.map((item)=>{
       if(item.value===value.target.value){
         this.timeTips=item.tips
       }
     })
    },
    add(knowledgeId) {
      this.knowledgeId=knowledgeId
      this.visible = true
      this.isConfirmSharing=false
      this.$nextTick(() => {
        this.model = {
          expirationDayNum:1,
          link: this.shareRoute ,
          password:''
        }
      })
    },
    edit(record){
      this.visible = true
      this.$nextTick(() => {
        this.model = {
          link: '',
          password:''
        }
        this.getPassword(record)
      })
    },
    getPassword(record){
      this.isConfirmSharing=true
      this.timeTips=''
      this.confirmLoading=true
      getAction(this.url.password,{id:record.shareId}).then((res)=>{
        if(res.success){
          this.model.link=this.shareRoute+record.shareId
          this.model.password=res.result.password
          if(res.result.remainDay.indexOf('永久')>-1){
            this.timeTips= '链接<span style="color:#ff4d4f">永久有效</span>'
          }else {
            this.timeTips='链接<span style="color:#ff4d4f">'+res.result.remainDay+'天</span>后失效'
          }
        }else {
          this.$message.warning(res.message)
        }
        this.confirmLoading=false
      }).catch((err)=>{
        this.confirmLoading=false
        this.$message.warning(err.message)
      })
    },
    handleOk() {
      let that = this
      that.$refs.form.validate((err, values) => {
        if (err) {
          that.confirmLoading = true
          let httpurl = that.url.add
          let method = 'post'

          let formData = JSON.parse(JSON.stringify(that.model))
          formData.knowledgeId=that.knowledgeId
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
                that.close()
              } else {
                that.$message.warning(res.message)
              }
              that.confirmLoading = false
            }).catch((err) => {
            that.$message.warning(err.message)
            that.confirmLoading = false
          })
        }
      })
    },
    handleCancel() {
      this.close()
    },
    close() {
      this.visible = false
    },
  }
}
</script>
<style scoped lang='less'>
@import '~@assets/less/normalModal.less';
</style>