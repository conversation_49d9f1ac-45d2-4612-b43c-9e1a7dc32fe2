export  const packageTypes = ".tar,.tgz"
export const packageFileAccept = ".deb,.rpm"
export const scriptFileAccept = [
  '.sh',
  '.csh',
  '.py',
  '.gz',
  '.tar',
  '.tbz',
  '.tgz',
  '.zip',
  '.rar',
  '.bat',
  '.pl',
  '.7z',
  '.rpm',
  '.deb',
  '.conf'
].join(",")
export const patchFileAccept = ".tar,.tar.gz"
export const patchFileNameSuffix = [
  '.tar',
  '.tgz',
]
export const scriptFileNameSuffix = [
  '.tar',
  '.tgz',
]
export  const  patchFileNameSuffix1 = [
    '.tar',
    '.tbz',
    '.tgz',
    '.zip',
    '.rar',
    '.jar',
    '.class',
    '.java',
    '.c',
    '.cpp',
    '.h',
    '.so',
    '.7z',
    '.rpm',
    '.deb'
  ]
  export  const  scriptFileNameSuffix2= [
  '.sh',
  '.csh',
  '.py',
  '.gz',
  '.tar',
  '.tbz',
  '.tgz',
  '.zip',
  '.rar',
  '.bat',
  '.pl',
  '.7z',
  '.rpm',
  '.deb',
  '.conf'
]