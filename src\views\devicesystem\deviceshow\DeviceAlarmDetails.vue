<template>
  <div class="div-container">
    <a-row  class="row">
      <a-col :span="24" style="margin-bottom:14px">
        <div style="text-align: right;">
          <img src="~@assets/return1.png" alt="" @click="getGo"
               style="width: 20px;height: 20px;cursor: pointer">
        </div>
      </a-col>
      <a-col :span="24">
        <!--告警信息-->
        <device-alarm-basic-info ref='alarmInfo' :alarm-info='data'></device-alarm-basic-info>
        <!--告警自动恢复-->
        <alarm-auto-recovery ref='alarmAutoRecovery' :alarm-rule-id='data.alarmRuleId'></alarm-auto-recovery>
        <!--评论-->
        <alarm-comment :alarm-info='data'></alarm-comment>
        <!--历史记录-->
        <alarm-assignment-history :alarm-info='data'></alarm-assignment-history>
      </a-col>
    </a-row>
  </div>
</template>
<script>
import deviceAlarmBasicInfo from '@views/alarmManage/modules/AlarmBasicInfo.vue'
import alarmAssignmentHistory from '@views/alarmManage/modules/AlarmOperationHistory.vue'
import AlarmAutoRecovery from '@views/alarmManage/modules/AlarmAutoRecovery.vue'
import alarmComment from '@views/alarmManage/modules/AlarmComment.vue'

export default {
  name: "DeviceAlarmDetails",
  props: {
    data: {
      type: Object
    }
  },
  components: { alarmComment, AlarmAutoRecovery, alarmAssignmentHistory, deviceAlarmBasicInfo },
  data() {
    return {}
  },
  mounted() {
    this.show()
  },
  methods: {
    show() {
      this.visible = true
      this.record = this.data
    },
    //返回上一级
    getGo() {
      this.$parent.pButton1(0)
    }
  }
}
</script>

<style scoped lang="less">
.div-container {
  height: 100%;
  overflow: hidden;
  overflow-y: auto;
}
.row {
  position: relative;
  margin-bottom: 16px;
  margin-right: 1px;
}
</style>