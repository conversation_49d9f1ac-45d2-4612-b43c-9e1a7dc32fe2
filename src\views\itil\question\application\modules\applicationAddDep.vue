<template>
  <a-modal
    :title="title"
    :width="modalWidth"
    :visible="visible"
    :confirmLoading="confirmLoading"
    :destroyOnClose="true"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭"
    wrapClassName="ant-modal-cust-warp"
    style="top:5%;height: 95%;overflow: auto"
  >
    <div  style="padding-top: 0px;">
      <fm-generate-form :data="startFormJson" ref="generateStartForm" :value="variables" :remote="remoteFuncs">
      </fm-generate-form>   
         
      <a-tabs :animated="false" default-active-key="1" @change="callback">
        <a-tab-pane key="1" tab="附件">
          <div class="clearfix">
                <!-- 保障部门：<j-dict-select-tag :trigger-change="true"  v-model="depCode" placeholder="选择保障单位" dictCode="sys_depart,depart_name,org_code,org_category = 4" style="width: 50%;"/><br>  -->
                保障部门：<j-dict-select-tag  v-model="depCode" placeholder="请选择软件名称"  dictCode="sys_depart,depart_name,org_code,org_category = 3" style="width: 25%;"/>
                <j-upload v-model="files" :number=5 style="padding-top: 20px;"></j-upload>
              
          </div>
        </a-tab-pane>
        <!-- <a-tab-pane key="2" tab="关联配置项" force-render>
          <a-table
              ref="table"
              size="middle"
              bordered
              rowKey="id"
              :columns="columns"
              :dataSource="dataSource"
              :pagination="ipagination"
              :loading="loading"
              :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
              class="j-table-force-nowrap"
              @change="handleTableChange">
          </a-table>
        </a-tab-pane> -->
      </a-tabs>
    </div>
  </a-modal>
</template>
<script>
 import { getAction, postAction } from '@/api/manage'
 import JUpload from '@/components/jeecg/JUpload'
 import {ajaxGetDictItems} from '@/api/api'
 import { JeecgListMixin } from '@/mixins/JeecgListMixin'
   import JDictSelectTag from "@/components/dict/JDictSelectTag"
export default {
  name: 'questionApplicationAddDep',
  mixins:[JeecgListMixin],
  components: { 
    JUpload
  },
  data() {
    return {
      title: '操作',
      confirmLoading: false,
      processDefinition:{formKey:'question_form'},
      /* 弹框宽 */
      modalWidth: '55%',
      form: this.$form.createForm(this),
      visible: false,
      required: false,
      startFormJson: undefined,
      variables: undefined,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      // 上传相关
      previewVisible: false,
      previewImage: '',
      files:'',
      //事件ID
      eventId:'',
      depCode:'',

      remoteFuncs: {
          getType (resolve) {
            ajaxGetDictItems('question_type', null).then((res) => {
              if (res.success) {
                const options= res.result;
                resolve(options)
              }
            })
          },
        },
      // 关联配置项
      columns: [
       {
          title: '序号',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function(t, r, index) {
            return parseInt(index) + 1
          }
        },
        {
          title: '分类',
          align: 'center',
          dataIndex: 'configType_dictText'
        },
        {
          title: '编号',
          align: 'center',
          dataIndex: 'code'
        },
        {
          title: '名称',
          align: 'center',
          dataIndex: 'name'
        },
        {
          title: '状态',
          align: 'center',
          dataIndex: 'state_dictText'
        }
      ],
      dataSource: [],
      url: {
        list: "/itilconfigitemlibrary/itilConfigItemLibrary/list",
      },
    
    }
  },
//   computed: {
//       //可行性测试，根据文件路径动态加载组件
//       LcDict: function() {
//         var myComponent = () => import(`@/components/dict/JDictSelectTag`)
//         return myComponent
//       }
//     },
  created() {
    this.variables = null
  },
  mounted(){
    if (this.processDefinition.formKey) {
      getAction('/flowableform/umpFlowableForm/queryByKey', { key: this.processDefinition.formKey,tableId: null}).then(res => {
        if (res.success) {
          var formData = res.result
          if (formData && formData.formJson) {
            this.startFormJson = JSON.parse(formData.formJson)
            this.variables = null;
          }
        }
      })

    }
  },
  methods: {
    add(v,eventId) {
      this.eventId = eventId;
      this.processDefinition =  v;
      this.selectedRowKeys = [];
      this.variables = null;
      this.visible = true;
      this.files = ''; //文件
      if(this.processDefinition.tableId){
        this.getData();
      }
      if(null != v.busId){
          getAction('/businessrelation/actZBusinessRelation/list', { processId: this.processDefinition.busId}).then(res =>{
            if (res.success) {
              this.selectedRowKeys = res.result.itilConfigIds;
              this.files = res.result.fileUrlList;
              
            }
          })
      }
    },
    //获取数据
    getData(){
      if (this.processDefinition.formKey) {
        getAction('/flowableform/umpFlowableForm/queryByKey', { key: this.processDefinition.formKey,tableId: this.processDefinition.tableId}).then(res => {
          if (res.success) {
            var formData = res.result
            if (formData && formData.formJson) {
              // this.startFormJson = JSON.parse(formData.formJson)
              this.variables = JSON.parse(formData.formValue)
              
            }
          }
        })

      }
    },
    // 关闭弹框
    close() {
      this.$emit('close')
      this.visible = false
      this.current = 0
    },
    // 提交
    handleOk() {
        if (this.$refs.generateStartForm) {
          if(this.processDefinition.tableId){
            this.$refs.generateStartForm.getData().then(values => {
            if (values && values != undefined) {
              let formData = Object.assign(this.data || {}, values)
              formData.procDefId = this.processDefinition.id
              formData.procDeTitle = this.processDefinition.name
              formData.form_value = JSON.stringify(values)
              //Object.assign({processInstanceFormData}, values)
              formData.filedNames = 'form_value' + ',' + 'form_key'
              formData.form_key = this.processDefinition.formKey
              formData.id = this.processDefinition.tableId
              formData.itilConfigIds = this.selectedRowKeys;
              let faleUrl='';
              if(this.files instanceof Array){
                for(var i=0;i<this.files.length;i++){
                  faleUrl=faleUrl+","+this.files[i]
                }
              }else{
                faleUrl = this.files
              }
              
              formData.file = faleUrl;
              postAction('/question/editForm', formData).then((res) => {
                this.uploading = false
                if(res.success){
                  this.$message.success("保存成功")
                  this.visible = false
                  this.$emit('ok')
                }else{
                  this.$message.warning(res.message)
                  this.visible = false;
                  this.$emit('ok')
                }
              })
            }
          }).catch(e => {
          })
          }else{
            this.$refs.generateStartForm.getData().then(values => {
            if (values && values != undefined) {
              let formData = Object.assign(this.data || {}, values)
              formData.form_value = JSON.stringify(values)
              //Object.assign({processInstanceFormData}, values)
              formData.filedNames = 'form_value' + ',' + 'form_key'
              formData.form_key = this.processDefinition.formKey
              formData.itilConfigIds = this.selectedRowKeys;
              formData.file = this.files;
              formData.eventId = this.eventId;//绑定事件ID
              formData.depCode = this.depCode;//绑定的保障局
              postAction('/question/add', formData).then((res) => {
                this.uploading = false
                if(res.success){
                  this.$message.success("保存成功")
                  this.visible = false
                  this.$emit('ok')
                }else{
                  this.$message.warning(res.message)
                  this.visible = false
                  this.$emit('ok')
                }
              })
            }
          }).catch(e => {
          })
          }
          
        }
    },
    handleCancel() {
      this.close()
    },
    // tab
    callback(key) {
    },
    // 上传相关
    onCancel() {
      this.previewVisible = false
    },
    async handlePreview(file) {
      if (!file.url && !file.preview) {
        file.preview = await getBase64(file.originFileObj)
      }
      this.previewImage = file.url || file.preview
      this.previewVisible = true
    },
    handleChange({ fileList }) {
      this.fileList = fileList
    }
  },
  
}
</script>
<style scoped></style>
