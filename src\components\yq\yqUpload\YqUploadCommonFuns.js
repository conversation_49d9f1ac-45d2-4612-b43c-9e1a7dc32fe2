/*上传附件涉及的常用函数：限制文件格式、限制文件大小、一次上传多个文件时的大小限制、文件预览下载*/
import { downloadFile, getFileAccessHttpUrl } from '@/api/manage'
import Vue from 'vue'
/*文件上传前的预处理：比如文件大小*/
export function checkBeforeUpload(file, isEnabled = false, maxSize, unit,isTips= true) {
  if (isEnabled) {
    return compareFileSizes(isEnabled, maxSize, file.size, unit,isTips)
  } else {
    return true
  }
}
/*限制文件上传格式*/
export function checkAccept(file, accept,isTips= true) {
  var testmsg = file.name.substring(file.name.lastIndexOf('.') + 1)
  const extension = accept.includes(testmsg)
  if (!extension&&isTips) {
    Vue.prototype.$message.warning('请上传符合' + accept + '格式的文件！',3)
  }
  return extension
}
/*文件发生改变后，附加处理方法，一次性上传多个文件，剔除列表中不满足条件的文件*/
export function checkChangedFiles(fileList, isEnabled, maxSize, unit,isTips= true) {
  let list = fileList
  if (fileList.length > 0) {
    list = fileList.filter((item) => {
      return compareFileSizes(isEnabled, maxSize, item.size, unit,isTips)
    })
  }
  return list
}
/*下载预览文件*/
export function previewFile(fileInfo) {
  if (!fileInfo || !fileInfo.url || !fileInfo.name) {
    Vue.prototype.$message.warning('未知的文件')
    return
  }
  let url = getFileAccessHttpUrl(fileInfo.url)
  downloadFile(url, fileInfo.name)
}
/*计算当前上传文件是否超限*/
export function compareFileSizes(isEnabled = false, maxSize, realSize, unit,isTips= true) {
  if (isEnabled && !isNaN(maxSize) && realSize > convertUnits(maxSize, unit)) {
    if (isTips){
      let msg = '上传文件不能大于' + maxSize + unit
      Vue.prototype.$message.warning(msg,3)
    }
    return false
  }
  return true
}

/*转换文件单位到bit，计算文件大小*/
export function convertUnits(size, fromUnit) {
  //B==byte
  // const units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  const fromIndex = units.indexOf(fromUnit.toUpperCase())

  if (fromIndex === -1) {
    throw new Error('目前未支持到' + fromUnit + '单位')
  }
  //换算到bit==b
  if (fromUnit.toUpperCase() === 'B') {
    return size * 8
  } else {
    //先换算到byte==B，再到bit==b
    return size * Math.pow(1024, fromIndex) * 8
  }
}