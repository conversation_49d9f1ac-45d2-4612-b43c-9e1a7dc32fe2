<template>
  <div class='background' :style='pageScale?pageStyle:{}'>
    <div class='left_border'></div>
    <div class='right_border'></div>
    <div class='topBox'>
      <div class='sj_left'></div>
      <div class='sj_left2'></div>
      <div class='sj_right'></div>
      <div class='sj_right2'></div>
      <div class='topName'><img :src='sz_title.szTitleImg' alt=''
                                style='width: 240px;height: 130px;'>{{ sz_title.szBigTitle }}
      </div>
      <div class='title_time'>
        <div style='font-size: 0.35rem'>{{ data }}</div>
        <div style='font-size: 0.35rem; margin-left: 1rem;'>{{ dayOfWeek }}</div>
        <div style='font-size: 0.35rem; margin-left: 1rem;'>{{ time }}</div>
      </div>
    </div>
    <a-row :gutter='24' class='row-class'>
      <a-col :span='7' class='col-class' style='margin-top: -0.4rem;'>
        <div class='left_top'>
          <div class='left_top_left' @mousewheel='handleMouseWheel'>
            <div class='topTitle'>
              <img src='@/assets/szImg/roatTag.png' alt='' style='margin-right: -12px' />
              <div class='left_top_title'>值班表</div>
            </div>
            <vue-seamless-scroll :data='rotaData' :class-option='rotaWarning' class='seamless_warp' ref='seamlessDiv'>
              <div v-for='(item,index) in rotaData' :key='index' style='height: 2rem;width: 100%;'>
                <rota-com :name='item.realname' :career='item.post' :time="item.dutyDate + ' ' +item.week"
                          :src="item.avatar != '' && item.avatar != null ? imgUrl + '/' + item.avatar : require('../../assets/bigScreen/5-select.png')">
                </rota-com>
              </div>
            </vue-seamless-scroll>
          </div>
          <div class='view_box'>
            <div class='topTitle_view'>
              <img src='@/assets/szImg/viewTag.png' alt='' style='margin-right: -12px' />
              <div class='left_top_title'>工单总览</div>
            </div>
            <div style='height: 38%;width: 100%;display: flex;'>
              <div style='height: 100%;width: 33%;'>
                <div style='height: 50%;width: 100%;'>
                  <overview-com :title='this.overviewList[0].title' :changeNum='unit + this.overviewList[0].differ'
                                :value='this.overviewList[0].value' :src="require('../../assets/szImg/viewSq.png')"
                                :src2="require('../../assets/szImg/viewAdd.png')">
                  </overview-com>
                </div>
                <div style='height: 50%;width: 100%;'>
                  <overview-com :title='this.overviewList[1].title'
                                :changeNum='unitBottom + this.overviewList[1].differ'
                                :value='this.overviewList[1].value'
                                :src="require('../../assets/szImg/viewBj.png')"
                                :src2="require('../../assets/szImg/viewBottom.png')"
                                :color="'#00F6FF'">
                  </overview-com>
                </div>
              </div>
              <div style='height: 100%;width: 67%;' id='workOver'></div>
            </div>
            <div style='height: calc(62% - 120px);width: 100%;padding: 10px 48px;'>
              <div style='font-size: 0.3rem;color: rgba(250,250,250,.6);'>近一个月告警趋势</div>
              <div style='height: 100%;width: 100%;' id='trend'></div>
            </div>
          </div>
        </div>
        <div class='left_bottom'>
          <div class='hj_box'>
            <div class='topTitle_hj'>
              <img src='@/assets/szImg/hj.png' alt='' />
              <div class='left_bottom_title'>动力环境监控</div>
            </div>
            <div style='height: calc(100% - 120px);margin-top:20px;background-color: #072D72;'></div>
          </div>
          <div class='left-bottom-core'>
            <div class='topTitle_alarm'>
              <img src='@/assets/szImg/alarmTag.png' alt='' style='margin-right: -12px' />
              <div class='left_bottom_title'>告警信息</div>
            </div>
            <div class='left-bottom-table' @mousewheel='handleMouseAlarm' v-if='warningData.length >0'>
              <vue-seamless-scroll :data='warningData' :class-option='warning' class='seamless-warp'
                                   ref='seamlessAlarm'>
                <ul>
                  <li v-for='(item, index) in warningData' :key='index' class='tabel-item'>
                    <div class='tabel-item-left' :style='{color: item.color}'>
                      [{{ item.alarmLevelName }}]
                    </div>
                    <div class='tabel-item-right'>{{ item.info }}</div>
                  </li>
                </ul>
                <!-- <div v-for="item in warningData" :key="item.id">
                  <span style="width: 20%;"> {{ item.createTime.substring(5,10) }} </span>
                  <span> {{ item.deviceName }} </span>
                  <span> {{ item.templateName }} </span>
                  <img v-if="item.alarmLevel == '一般告警'" src="../.././assets/szImg/yiban.png" />
                  <img v-else src="../.././assets/szImg/yanzhong.png" />
                </div> -->
              </vue-seamless-scroll>
            </div>
            <div class='ant-empty ant-empty-normal' v-else>
              <div class='ant-empty-image'>
                <svg width='200' height='140' viewBox='0 0 64 41' xmlns='http://www.w3.org/2000/svg'>
                  <g transform='translate(0 1)' fill='none' fill-rule='evenodd'>
                    <ellipse fill='#F5F5F5' cx='32' cy='33' rx='32' ry='7' />
                    <g fill-rule='nonzero' stroke='#D9D9D9'>
                      <path
                        d='M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z' />
                      <path
                        d='M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z'
                        fill='#FAFAFA' />
                    </g>
                  </g>
                </svg>
              </div>
              <p class='ant-empty-description' style='font-size:0.4rem;color: white !important'>暂无数据</p>
            </div>
          </div>
        </div>
      </a-col>
      <a-col :span='10' class='col-class-center'>
        <div class='center_bottom'>
          <div class='center_top'>
            <div class='equipmentTotal'>
              <div style='display: flex;margin-left: 40px;'>
                <div :class='titleClass' @click='topoDian'>网络拓扑
                </div>
                <div style='color: rgba(250,250,250,.8);font-size: 0.5rem;margin-left: 0.5rem;'>|</div>
                <div :class='titleClass_3d' style='margin-left: 0.5rem' @click='computeRoom'>
                  3D机房
                </div>
              </div>
              <div style='height: 100%;width: 100%;margin-top: 0.4rem;'>
                <device-com :color="'#118FFA'" :value='item.all' :unit="'设备总数(个)'"
                            :src="require('../../assets/szImg/deviceTotal.png')"></device-com>
              </div>
              <div style='height: 100%;width: 100%;'>
                <device-com :color="'#18CA16'" :value='item.on' :unit="'在线（个）'"
                            :src="require('../../assets/szImg/on.png')"></device-com>
              </div>
              <div style='height: 100%;width: 100%;'>
                <device-com :color='offLineColor' :value='item.out' :unit="'离线（个）'"
                            :src="require('../../assets/szImg/out.png')"></device-com>
              </div>
              <div style='height: 100%;width: 100%;'>
                <device-com :color="'#E91C00'" :value='item.alarms' :unit="'告警（个）'"
                            :src="require('../../assets/szImg/deviceAlarm.png')"></device-com>
              </div>
            </div>
          </div>
          <div class='topo-class'>
            <div style='width: 100%; height: 100%'>
              <!-- <sz-vis-edit v-if="nettopoList.length > 0 && this.topo" ref="bigScreen" operate="show" topoBgByTheme>
              </sz-vis-edit> -->
              <vis-edit v-if='nettopoList.length > 0 && this.topo' ref='bigScreen' operate='show' topoBgByTheme>
              </vis-edit>
              <motor-room v-else></motor-room>
            </div>
          </div>
          <div class='center_bottom_img'>
            <div class='bottom_left'>运维管理</div>
            <div class='bottom_center' style='color: #09469B;'> |</div>
            <div class='bottom_center' style='color: #9096B1; '>安全态势</div>
          </div>
        </div>
      </a-col>
      <a-col :span='7' class='col-class' style='margin-top: -0.4rem;'>
        <div class='right_top'>
          <div class='topTitle_security'>
            <img src='../../assets/szImg/Security.png' alt='' style='' />
            <div class='right_top_title'>安防监控</div>
          </div>
          <div style='height: calc(100% - 140px);margin-top: 40px;background-color: #072D72;'></div>
        </div>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import {
  formatDate
} from '@/utils/util'
import {
  getAction
} from '@/api/manage'
import echarts from 'echarts/lib/echarts'
import vueSeamlessScroll from 'vue-seamless-scroll'
import VisEdit from '@/views/topo/nettopo/modules/VisEdit.vue'
import ChartsTopTitle from './moduels/charts-top-title.vue'
import overviewCom from './moduels/overviewCom.vue'
import motorRoom from './moduels/motorRoom.vue'
import deviceCom from './moduels/deviceCom.vue'
import rotaCom from './moduels/rotaCom.vue'
import {
  WebsocketMixin
} from '@/mixins/WebsocketMixin'

export default {
  components: {
    vueSeamlessScroll,
    ChartsTopTitle,
    VisEdit,
    motorRoom,
    rotaCom,
    deviceCom,
    overviewCom
  },
  mixins: [WebsocketMixin],
  data() {
    return {
      sz_title: window._CONFIG['customization'],
      titleClass_3d: 'class2',
      titleClass: 'class1',
      topo: true,
      dayOfWeek: '',
      imgUrl: window._CONFIG['downloadUrl'],
      data: '',
      time: '',
      nettopoList: [],
      overviewList: [{
        title: '',
        value: '',
        differ: ''
      }, {
        title: '',
        value: '',
        differ: ''
      }],
      netPageNo: 1,
      hoverHeight: '0px',
      netPapeTotalNo: 0,
      warningData: [],
      rotaData: [],
      item: {},
      unit: '',
      unitBottom: '',
      url: {
        alarmList: '/data-analysis/alarm/realtime', // 告警轮播数据
        showCounts: '/device/deviceInfo/showCounts', // 设备数量统计
        list: '/topo/topoInfo/list', // 拓扑图数据
        alarmTrend: '/data-analysis/alarm/alarmLevelTrend',
        count: '/data-analysis/order/countByType',
        queryUsersByWeek: '/duty/shift/queryUsersByWeek',
        trendStatistics: '/data-analysis/order/trendStatistics',
        listCementByUser: '/sys/annountCement/listByUser'
        // getItemValueList: '/configure/dictItem/getItemMap'
      },
      offLineColor: '#8c8c8c', //'#F8B43C'
      pageScale: false,
      pageStyle: {
        transform: `scale(0.25,0.25)`,
        transformOrigin: '0 0'
      },
      dateTimer: null,
      pageTimer: null,
      trenChart: null,
      workOverChart:null,
    }
  },
  computed: {
    warning() {
      return {
        step: 0.15, // 数值越大速度滚动越快
        limitMoveNum: 11, // 开始无缝滚动的数据量 this.dataList.length
        hoverStop: true, // 是否开启鼠标悬停stop
        direction: 1, // 0向下 1向上 2向左 3向右
        // openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 86, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
        // singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
        waitTime: 2 // 单步运动停止的时间(默认值1000ms)
      }
    },
    rotaWarning() {
      return {
        step: 2, // 数值越大速度滚动越快
        limitMoveNum: 4, // 开始无缝滚动的数据量 this.dataList.length
        hoverStop: true, // 是否开启鼠标悬停stop
        direction: 1, // 0向下 1向上 2向左 3向右
        // openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 86, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
        // singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
        waitTime: 2 // 单步运动停止的时间(默认值1000ms)
      }
    }
  },
  created() {
    if (process.env.NODE_ENV === 'development') {
      this.pageScale = true
    }
    let TopoStatuColors = window.config.TopoStatuColors
    if (TopoStatuColors && TopoStatuColors.offLine) {
      this.offLineColor = TopoStatuColors.offLine
    }
    this.dateTimer = setInterval(() => {
      let weekDays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
      let today = new Date()
      this.dayOfWeek = weekDays[today.getDay()]
      this.data = formatDate(Date.parse(new Date()), 'yyyy-MM-dd')
      this.time = formatDate(Date.parse(new Date()), 'hh:mm:ss')
    }, 1000)
  },
  mounted() {
    this.getPageData()
    this.refresh()
    // this.outTimeList()
  },
  beforeDestroy() {
    if(this.trenChart){
      this.trenChart.dispose();
      this.trenChart = null;
    }
    if(this.workOverChart){
      this.workOverChart.dispose();
      this.workOverChart = null;
    }
    if (this.pageTimer) {
      clearInterval(this.pageTimer)
      this.pageTimer = null
    }
    if (this.dateTimer) {
      clearInterval(this.dateTimer)
      this.dateTimer = null
    }
  },
  methods: {
    getPageData() {
      this.getNetTopoList()
      this.alarmList()
      this.showCounts()
      this.alarmTrend(30)
      this.count()
      this.queryUsersByWeek()
      this.trendStatistics()
    },
    getList() {
      getAction(this.url.listCementByUser)
        .then((res) => {
        })
    },
    refresh() {
      this.pageTimer = setInterval(() => {
        this.getPageData()
      }, 60 * 1000)
    },
    topoDian() {
      this.topo = true
      this.titleClass_3d = 'class2'
      this.titleClass = 'class1'
      this.netPageNo = 1
      this.nettopoList.length = 0
      this.getNetTopoList()
    },
    computeRoom() {
      this.topo = false
      this.titleClass_3d = 'class1'
      this.titleClass = 'class2'
    },
    trendStatistics() {
      getAction(this.url.trendStatistics).then((res) => {
        if (res.success) {
          if (res.result[0].type == 0) {
            this.overviewList[0].title = '较上月平稳'
          } else if (res.result[0].type == 1) {
            this.overviewList[0].title = '较上月增加'
            this.unit = '+'
          } else {
            this.overviewList[0].title = '较上月减少'
            this.unit = '-'
          }
          if (res.result[1].type == 0) {
            this.overviewList[1].title = '较上月平稳'
          } else if (res.result[1].type == 1) {
            this.overviewList[1].title = '较上月增加'
            this.unitBottom = '+'
          } else {
            this.overviewList[1].title = '较上月减少'
            this.unitBottom = '-'
          }
          this.overviewList[0].differ = res.result[0].differ
          this.overviewList[1].differ = res.result[1].differ
          this.overviewList[0].value = res.result[0].value
          this.overviewList[1].value = res.result[1].value
        }
      })
    },
    handleMouseWheel(e) {
      if (Math.abs(this.$refs.seamlessDiv.yPos) < this.$refs.seamlessDiv.realBoxHeight / 2 || e.deltaY < 0) {
        this.$refs.seamlessDiv.yPos -= e.deltaY
        this.$refs.seamlessDiv.yPos = this.$refs.seamlessDiv.yPos >
        0 ? 0 : this.$refs.seamlessDiv.yPos
      }
    },
    handleMouseAlarm(e) {
      if (Math.abs(this.$refs.seamlessAlarm.yPos) < this.$refs.seamlessAlarm.realBoxHeight / 2 || e.deltaY < 0) {
        this.$refs.seamlessAlarm.yPos -= e.deltaY
        this.$refs.seamlessAlarm.yPos = this.$refs.seamlessAlarm.yPos >
        0 ? 0 : this.$refs.seamlessAlarm.yPos
      }
    },
    queryUsersByWeek() {
      getAction(this.url.queryUsersByWeek).then((res) => {
        if (res.success) {
          this.rotaData = res.result
        }
      })
    },
    //拓扑数据
    getNetTopoList() {
      getAction(this.url.list, {
        topoType: '0',
        showType: '1',
        pageNo: this.netPageNo,
        pageSize: 30
      }).then((res) => {
        if (res.success) {
          this.netPageNo += 1
          this.netPapeTotalNo = Math.ceil(res.result.pageList.total / 10)
          this.nettopoList = [...this.nettopoList, ...res.result.pageList.records]
          if (this.nettopoList.length > 0) {
            let temTimer = setTimeout(() => {
              this.hoverHeight = ([document.getElementsByClassName('img-div')][0].clientWidth / 5) * 3 +
                'px'
              clearTimeout(temTimer)
            }, 50)
            this.$nextTick(() => {
              if (this.netPageNo == 2) {
                this.$refs.bigScreen.createTopo(this.nettopoList[0].id)
              }
            })
          }
        }
      })
    },
    // 告警轮播数据
    alarmList() {
      getAction(this.url.alarmList).then((res) => {
        if (res.code == 200) {
          if (res.result != '暂无信息') {
            this.warningData = res.result
          }
        }
      })
    },
    // 设备数量统计
    showCounts() {
      getAction(this.url.showCounts).then((res) => {
        if (res.success) {
          this.item = res.result
        }
      })
    },
    alarmTrend(day) {
      getAction(this.url.alarmTrend, {
        day: day
      }).then((res) => {
        if (res.code == 200) {
          this.trend(res.result)
        }
      })
    },
    //工单申请、办结数据
    count() {
      getAction(this.url.count).then((res) => {
        if (res.code == 200) {
          this.workOver(res.result)
        }
      })
    },
    workOver(data) {
      let name = []
      let applyList = []
      let completionList = []
      data.forEach((ele => {
        name.push(ele.name)
        applyList.push(ele.apply)
        completionList.push(ele.completion)
      }))
      if(this.workOverChart === null){
        this.workOverChart = this.$echarts.init(document.getElementById('workOver'))
        this.workOverChart.setOption({
          color: ['#7A8FF6', '#00F6FF'],
          tooltip: {
            trigger: 'axis'
          },
          legend: {
            icon: 'reat',
            left: '10%',
            itemWidth: 18,
            itemGap: 40,
            data: ['工单申请', '工单办结'],
            textStyle: {
              color: 'rgba(250,250,250,.6)',
              fontSize: 28,
              fontWeight: 'bold'
            }
          },
          grid: {
            left: '8%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: [{
            type: 'category',
            data: name,
            axisLine: {
              lineStyle: {
                width: 1,
                color: '#FFFFFF'
              }
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: '#FFFFFF'
              }
            }
          }],
          yAxis: [{
            type: 'value',
            axisTick: {
              show: false
            },
            axisLine: {
              show: true,
              lineStyle: {
                width: 1,
                color: '#FFFFFF'
              }
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: '#FFFFFF'
              }
            },
            splitLine: {
              show: false
            }
          }],
          series: [{
            name: '工单申请',
            type: 'line',
            smooth: true,
            lineStyle: {
              width: 5
            },
            showSymbol: false,
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                offset: 0,
                color: 'rgba(128,149,254,1)'
              },
                {
                  offset: 1,
                  color: 'rgba(128,149,254,.1)'
                }
              ])
            },
            emphasis: {
              focus: 'series'
            },
            data: applyList
          },
            {
              name: '工单办结',
              type: 'line',
              smooth: true,
              lineStyle: {
                width: 5
              },
              showSymbol: false,
              areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                  offset: 0,
                  color: 'rgba(0,246,255,1)'
                },
                  {
                    offset: 1,
                    color: 'rgba(0,246,255,.1)'
                  }
                ])
              },
              emphasis: {
                focus: 'series'
              },
              data: completionList
            }
          ]
        })
        window.addEventListener('resize', () => {
          this.workOverChart.resize()
        })
      }else{
        this.workOverChart.setOption({
          xAxis: [{ data: name}],
          series: [{ data: applyList},{ data: completionList}]
        })
      }

    },
    trend(data) {
      let time = data.time
      let value = data.value[0].data.map((item, index) => {
        return item + data.value[1].data[index]
      })
      if (this.trenChart === null) {
        this.trenChart = this.$echarts.init(document.getElementById('trend'))
        this.trenChart.setOption({
          grid: {
            top: '7%',
            right: '5%',
            left: '5%',
            bottom: '15%',
            containLabel: true
          },
          tooltip: {
            show: true,
            trigger: 'axis',
            axisPointer: {
              type: 'line'
            },
            textStyle: {
              fontSize: 12
            }
          },
          xAxis: {
            type: 'category',
            splitNumber: 6,
            data: time,
            // x轴文字
            axisLabel: {
              textStyle: {
                color: 'rgba(255,255,255,0.6)',
                fontWeight: 'bold',
                fontSize: 20
              }
            },
            // x轴
            axisLine: {
              show: true,
              lineStyle: {
                width: 4,
                color: '#073F8C'
              }
            },
            splitLine: {
              show: true,
              lineStyle: {
                width: 3,
                color: '#24376B',
                type: 'dashed'
              }
            },
            // x轴刻度
            axisTick: {
              show: false
            }
          },
          yAxis: {
            type: 'value',
            // y轴 文字
            axisLabel: {
              show: true,
              textStyle: {
                fontSize: 20,
                fontWeight: 'bold',
                color: 'rgba(250,250,250,.8)'
              }
            },
            // y轴
            axisLine: {
              show: true,
              lineStyle: {
                width: 4,
                color: '#073F8C'
              }
            },
            // y轴横向 标线
            splitLine: {
              show: true,
              lineStyle: {
                width: 1,
                color: 'rgba(255,255,255,.1)',
                type: 'dashed'
              }
            }
          },
          series: [{
            data: value,
            type: 'line',
            lineStyle: {
              width: 4,
              color: '#0793F7'
            },
            // 平滑 属性
            smooth: true,
            showSymbol: false,
            // 区域渐变效果
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0, 0, 0, 1,
                  [{
                    offset: 0,
                    color: 'rgba(11,145,241, 0.7)'
                  },
                    {
                      offset: 1,
                      color: 'rgba(11,145,241, 0.1)'
                    }
                  ],
                  false
                )
              }
            }
          }

          ]

        })
        window.addEventListener('resize', () => {
          this.trenChart.resize()
        })
      } else {
        // time = time.map(el=>"20250101")
        // value = value.map(el=>Math.random())
        this.trenChart.setOption({
          xAxis: {
            data: time
          },
          series: [
            {
              data: value
            }
          ]
        })
      }
    }
  }
}
</script>

<style lang='less' scoped>
.left-bottom-table::-webkit-scrollbar {
  display: none;
  /*隐藏滚动条*/
}

.left_border {
  height: 100%;
  width: 1rem;
  position: absolute;
  left: 0.2rem;
  bottom: 0.7rem;
  background-image: url('../../assets/szImg/left.png');
  background-size: 80% 90%;
  background-repeat: no-repeat;
  background-position: bottom;
}

.right_border {
  height: 100%;
  width: 1rem;
  position: absolute;
  right: 0.2rem;
  bottom: 0.6rem;
  background-image: url('../../assets/szImg/right.png');
  background-size: 80% 90%;
  background-repeat: no-repeat;
  background-position: bottom;
}

.sj_right {
  height: 0.8rem;
  width: 0.7rem;
  position: absolute;
  right: 37.7%;
  top: 0.8rem;
  background-image: url('../../assets/szImg/sj_right.png');
  background-size: 80% 90%;
  background-repeat: no-repeat;
}

.sj_right2 {
  height: 0.8rem;
  width: 0.7rem;
  position: absolute;
  right: 38.2%;
  top: 0.8rem;
  background-image: url('../../assets/szImg/sj_right.png');
  background-size: 80% 90%;
  background-repeat: no-repeat;
}

.sj_left {
  height: 0.8rem;
  width: 0.7rem;
  position: absolute;
  left: 38.2%;
  top: 0.8rem;
  background-image: url('../../assets/szImg/sj_left.png');
  background-size: 80% 90%;
  background-repeat: no-repeat;
}

.sj_left2 {
  height: 0.8rem;
  width: 0.7rem;
  position: absolute;
  left: 37.7%;
  top: 0.8rem;
  background-image: url('../../assets/szImg/sj_left.png');
  background-size: 80% 90%;
  background-repeat: no-repeat;
}

.title_time {
  display: flex;
  position: absolute;
  top: -0.005rem;
  right: 1rem;
  letter-spacing: 5px;
  color: rgba(255, 255, 255, .6);
}

.select_time {
  position: absolute;
  height: 0.32rem;
  width: 3.5rem;
  top: 1.3rem;
  right: 2rem;
}

.topBox {
  height: 10%;
  width: 100%;
  background-image: url('../../assets/szImg/header2.png');
  background-size: 100%;
  background-position: bottom;
  background-repeat: no-repeat;
  align-items: center;
  text-align: center;
  position: relative;

  .topName {
    font-size: 0.7rem;
    font-weight: 600;
    letter-spacing: 10px;
    line-height: 3;
    background: linear-gradient(to bottom, #E8F1F6, #0ABBF6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}

.topTitle {
  display: flex;
  align-items: center;
  font-size: 0.5rem;
  height: 100px;
  background-size: 100% 40%;
  background-position: bottom;
  background-image: url('../../assets/szImg/rotaTitle.png');
  background-repeat: no-repeat;
}

.hj_box {
  height: 100%;
  width: 56%;
  background-color: #031955;
  background-image: url('../../assets/szImg/hjBorder.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding: 0.3rem 0.7rem 0.7rem 0.7rem;

  .topTitle_hj {
    display: flex;
    align-items: center;
    font-size: 0.5rem;
    height: 100px;
    background-size: 100% 40%;
    background-position: bottom;
    background-image: url('../../assets/szImg/hjTitle.png');
    background-repeat: no-repeat;
  }
}

.topTitle_alarm {
  display: flex;
  align-items: center;
  font-size: 0.5rem;
  height: 100px;
  background-size: 100% 40%;
  background-position: bottom;
  background-image: url('../../assets/szImg/alarmTitle.png');
  background-repeat: no-repeat;
}

.topTitle_security {
  display: flex;
  align-items: center;
  font-size: 0.5rem;
  height: 100px;
  background-size: 100% 40%;
  background-position: bottom;
  background-image: url('../../assets/szImg/jkTitle.png');
  background-repeat: no-repeat;
}

.view_box {
  height: 100%;
  width: 65%;
  background-color: #031955;
  background-image: url('../../assets/szImg/viewBorder.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding: 0.2rem 0.3rem 0.5rem 0.4rem;

  .topTitle_view {
    display: flex;
    align-items: center;
    font-size: 0.5rem;
    height: 100px;
    background-size: 100% 40%;
    background-position: bottom;
    background-image: url('../../assets/szImg/viewTitle.png');
    background-repeat: no-repeat;
  }
}

.background {
  height: 100%;
  width: 100%;
  min-width: 7680px;
  min-height: 2160px;
  background-image: url('../../../public/img/backImg/szBackground.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  padding: 0;
  position: relative;

}

.left_top {
  width: 100%;
  height: 49%;
  display: flex;
  justify-content: space-between;

  .left_top_left {
    padding: 0.2rem 0.3rem 0.5rem 0.4rem;
    height: 100%;
    width: 34%;
    background-color: #031955;
    background-image: url('../../assets/szImg/roatBorder.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }

  .left_top_title {
    letter-spacing: 3px;
    height: 21px;
    font-size: 0.35rem;
    font-weight: 600;
    line-height: 19px;
    color: rgba(250, 250, 250, .8);
    margin-left: 20px;
  }

  .seamless_warp {
    width: 100%;
    height: calc(100% - 120px);
    overflow: hidden;
  }
}

.left_bottom {
  width: 100%;
  height: calc(51% - 24px);
  margin-top: 24px;
  display: flex;
  justify-content: space-between;

  .left_bottom_title {
    letter-spacing: 3px;
    height: 21px;
    font-size: 0.35rem;
    line-height: 19px;
    font-weight: 600;
    color: rgba(250, 250, 250, .8);
    margin-left: 20px;
  }


  .left-bottom-core {
    height: 100%;
    width: 43%;
    padding: 0.1rem 0.25rem;
    background-color: #031A57;
    background-image: url('../../assets/szImg/alarmBorder.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    padding: 0.3rem 0.7rem 0.7rem 0.7rem;

    .left-bottom-table {
      width: 100%;
      height: calc(100% - 100px);
      overflow: hidden;

      .seamless-warp {
        width: 100%;
        height: 100%;
        overflow: hidden;
        padding: 24px 0;

        ul {
          margin: 0;
          padding: 0;

          .tabel-item {
            width: 100%;
            min-height: 0.625rem
            /* 50/80 */;
            display: flex;
            align-items: center;
            font-size: 0.23rem;

            .tabel-item-left {
              width: 20%;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
              color: #d62629;
            }

            .tabel-item-right {
              height: 100%;
              width: 80%;
              color: rgba(255, 255, 255, 0.85);
              display: flex;
              align-items: center;
              // overflow: hidden;
              word-wrap: break-word;
              // overflow-wrap: break-word;
              white-space: normal;
            }
          }
        }

        div {
          align-items: center;
          width: 100%;
          line-height: 0.5rem;
          display: flex;
          justify-content: space-around;

          li:nth-child(2n + 0) {
            background: #0C2D62;
          }

          span {
            width: 100%;
            color: rgba(255, 255, 255, 0.75);
            font-size: 0.3rem;
            text-align: center;
          }

          img {
            height: 0.3rem;
          }
        }
      }
    }
  }
}

.center_top {
  width: 22%;
  position: absolute;
  z-index: 20;
  padding: 14px 24px;

  .equipmentTotal {
    height: 100%;
    width: 100%;

    .stateTitle {
      font-size: 0.35rem;;
      color: #5bb0ff;
      margin-right: 8px;
    }

    .unit {
      font-size: 0.35rem;;
      color: #5bb0ff;
      font-family: Adobe Heiti Std;
      margin-left: 8px;
    }
  }
}

.center_bottom {
  width: 100%;
  height: 100%;
  position: relative;

  .center_bottom_img {
    width: 100%;
    height: 12%;
    font-size: 0.6rem;
    display: flex;
    justify-content: center;
    align-items: center;
    background-image: url('../../assets/szImg/bottom.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: bottom;

    .bottom_left {
      cursor: pointer;
      font-size: 0.7rem;
      color: #CBDDF8;
      height: 60%;
      line-height: 1.6;
      background-image: url('../../assets/szImg/light.png');
      background-repeat: no-repeat;
      background-position: bottom;
    }

    .bottom_center {
      margin-left: 1rem;
      height: 60%;
      line-height: 2;
      cursor: pointer;
    }
  }
}

.right_top {
  width: 100%;
  height: 100%;
  background-color: #031955;
  background-image: url('../../assets/szImg/securityBorder.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding: 0.3rem 0.7rem 0.7rem 0.7rem;

  .right_top_title {
    letter-spacing: 3px;
    height: 21px;
    font-size: 0.35rem;
    line-height: 19px;
    font-weight: 600;
    color: rgba(250, 250, 250, .8);
    margin-left: 20px;
  }
}

.row-class {
  width: 100%;
  height: 90%;
  padding: 48px 140px 0 140px;
}

.col-class {
  height: 100%;
  padding: 0 24px 96px 24px;
}

.col-class-center {
  height: 100%;
  padding: 0 24px;
}

.topo-class {
  width: calc(100% - 2.8rem);
  height: 88%;
  margin-left: 2.3rem;
}

.class1 {
  color: rgba(250, 250, 250, .9);
  font-size: 0.5rem;
  letter-spacing: 5px;
  cursor: pointer;
}

.class2 {
  color: rgba(250, 250, 250, .4);
  font-size: 0.5rem;
  letter-spacing: 5px;
  cursor: pointer;
}

/deep/ .ant-select-selection-selected-value {
  line-height: 0.5rem;
  font-size: 0.3rem;
  height: 0.6rem;
}

/deep/ .ant-select-dropdown-menu {
  background-color: #0B86BC;
  padding-top: 1px;
  padding-bottom: 1px;
}

/deep/ .ant-select-dropdown-menu-item {
  font-size: 0.3rem;
  line-height: 0.5rem;
  height: 0.6rem;
  background-color: #081E5C;
  border-color: #0B86BC;
  color: rgba(255, 255, 255, .6);
}

/deep/ .ant-select-arrow .ant-select-arrow-icon svg {
  height: 0.2rem;
  width: 0.2rem;
  color: rgba(255, 255, 255, .6);
}

/deep/ .ant-select-selection--single {
  height: 0.6rem;
  background-color: #081E5C;
  border-color: #0B86BC;
  color: rgba(255, 255, 255, .6);
}

/deep/ .ant-select-selection__placeholder {
  top: 0;
  margin-top: 0;
  height: 0.6rem;
  line-height: 0.5rem;
  font-size: 0.3rem;
}

/deep/ .ant-empty-normal {
  margin-top: 2rem;
}

/deep/ .ant-empty-normal .ant-empty-image {
  height: 2rem;
}

/deep/ .spin-block-theme {
  background: transparent !important;
}
</style>
<style scoped>
.ant-carousel >>> .slick-slide {
  text-align: center;
  line-height: 360px;
  overflow: hidden;
}
</style>