<template>
  <a-row style="height: 100%">
    <a-col :xl="4" :lg="6" :md="8" :sm="10" :xs="10" style="height: 100%; background: #fff">
      <div style="height: 100%; margin-right: 16px; overflow-x: auto">
        <a-card :bordered="false" style="height: 100%; min-width: 140px">
          <a-dropdown :visible="menuVisible" :trigger="[dropTrigger]" @visibleChange="dropStatus">
            <span style="user-select: none">
              <a-tree
                v-if="treeData && treeData.length > 0"
                @select="onSelect"
                @rightClick="rightHandle"
                @expand="onExpand"
                :selectedKeys="selectedKeys"
                :treeData="treeData"
                :loadData="onLoadData"
                :replace-fields="replaceFields"
                :defaultExpandAll="defaultExpandAll"
                :autoExpandParent="autoExpandParent"
                :show-line="true"
              />
            </span>
            <a-menu slot="overlay">
              <a-menu-item @click="addchildNode">增加类型</a-menu-item>
              <a-menu-item @click="editchildNode" v-show="menuItemVisible">编辑</a-menu-item>
              <a-menu-item key="2" @click="delChildNode" v-show="menuItemVisible && childrenDel">删除</a-menu-item>
              <a-menu-item @click="closeDrop" key="3">取消</a-menu-item>
            </a-menu>
          </a-dropdown>
        </a-card>
        <child-node-modal ref="modalForm1" @ok="modalFormOk1"></child-node-modal>
      </div>
    </a-col>
    <a-col :xl="20" :lg="18" :md="16" :sm="14" :xs="14" style="height: 100%; overflow: hidden; overflow-y: auto">
      <a-row style="height: 100%; margin-left: 16px">
        <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
          <a-card
            :bordered="false"
            :bodyStyle="{ paddingBottom: '0', marginRight: '12px' }"
            class="card-style"
            style="width: 100%"
          >
            <!-- 查询区域 -->
            <div class="table-page-search-wrapper">
              <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
                <a-row :gutter="24" ref="row">
                  <a-col :span="spanValue">
                    <a-form-item label="物品名称">
                      <a-input
                        :maxLength="maxLength"
                        v-model="queryParam.goodName"
                        autocomplete="off"
                        :allowClear="true"
                        placeholder="请输入物品名称"
                        style="width: 100%"
                      ></a-input>
                    </a-form-item>
                  </a-col>
                  <a-col :span="spanValue">
                    <a-form-item label="更新时间">
                      <a-range-picker
                        v-model="queryParam.warehousingTime"
                        @change="onChangePicker"
                        format="YYYY-MM-DD"
                        :placeholder="['开始时间', '截止时间']"
                        style="width: 100%"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="colBtnsSpan()">
                    <span
                      class="table-page-search-submitButtons"
                      :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                    >
                      <a-button type="primary" @click="searchQuery">查询</a-button>
                      <a-button @click="searchReset" style="margin-left: 10px">重置</a-button>
                    </span>
                  </a-col>
                </a-row>
              </a-form>
            </div>
            <!-- 查询区域-END -->
          </a-card>

          <a-card :bordered="false" style="flex: auto">
            <!-- 操作按钮区域 -->
            <div class="table-operator">
              <a-button @click="handleAdd" v-has="'stock:add'">新增</a-button>
              <a-button @click="handleExportXls('库存表')" v-has="'stock:export'">导出</a-button>
              <a-button @click="handleTemplateXls()" v-has="'stock:import'">下载模版</a-button>
              <a-upload
                v-has="'stock:import'"
                name="file"
                :showUploadList="false"
                :multiple="false"
                :headers="tokenHeader"
                :action="importExcelUrl"
                @change="handleImportExcel"
              >
                <a-button>导入</a-button>
              </a-upload>

              <a-dropdown v-if="selectedRowKeys.length > 0" v-has="'stock:delete'">
                <a-menu slot="overlay" style='text-align: center'>
                  <a-menu-item key="1" @click="batchDelVerify">删除 </a-menu-item>
                </a-menu>
                <a-button style="margin-left: 8px">
                  批量操作
                  <a-icon type="down" />
                </a-button>
              </a-dropdown>
            </div>

            <!-- table区域-begin -->
            <div>
              <a-table
                ref="table"
                bordered
                rowKey="id"
                :columns="columns"
                :dataSource="dataSource"
                :pagination="ipagination"
                :loading="loading"
                :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
                @change="handleTableChange"
              >
                <template slot="htmlSlot" slot-scope="text">
                  <div v-html="text"></div>
                </template>
                <template slot="imgSlot" slot-scope="text">
                  <span v-if="!text" style="font-size: 14px; font-style: italic">无图片</span>
                  <img
                    v-else
                    :src="getImgView(text)"
                    height="25px"
                    alt=""
                    style="max-width: 80px; font-size: 14px; font-style: italic"
                  />
                </template>
                <template slot="fileSlot" slot-scope="text">
                  <span v-if="!text" style="font-size: 14px; font-style: italic">无文件</span>
                  <a-button
                    v-else
                    :ghost="true"
                    type="primary"
                    icon="download"
                    size="small"
                    @click="downloadFile(text)"
                  >
                    下载
                  </a-button>
                </template>

                <span slot="action" slot-scope="text, record">
                  <a @click="handleEdit(record)" v-has="'stock:edit'">编辑</a>
                  <span v-has="'stock:delete'">
                    <a-divider type="vertical" />
                    <a v-if='record.stockNum > 0' @click="deleteVerify(record)" >删除</a>
                    <a-popconfirm v-else title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                      <a>删除</a>
                    </a-popconfirm>
                  </span>
                </span>
                <template slot="tooltip" slot-scope="text">
                  <a-tooltip placement="topLeft" :title="text" trigger="hover">
                    <div class="tooltip">
                      {{ text }}
                    </div>
                  </a-tooltip>
                </template>
              </a-table>
            </div>
            <!-- 下载模版 -->
            <iframe id="download" style="display: none"></iframe>
            <itil-stock-info-modal ref="modalForm" @ok="modalFormOk"></itil-stock-info-modal>
          </a-card>
        </a-col>
      </a-row>
    </a-col>
  </a-row>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import ItilStockInfoModal from './modules/ItilStockInfoModal'
import childNodeModal from './modules/childNodeModal'
import { getAction, deleteAction } from '../../../api/manage'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'

export default {
  name: 'ItilStockInfoList',
  mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
  components: {
    ItilStockInfoModal,
    childNodeModal,
  },
  data() {
    return {
      maxLength:50,
      // 表头
      columns: [
        {
          title: '物品编号',
          dataIndex: 'goodsCode'
        },
        {
          title: '物品名称',
          dataIndex: 'goodsName'
        },
        {
          title: '物品单位',
          dataIndex: 'unitCode'
        },
        {
          title: '当前库存',
          dataIndex: 'stockNum'
        },
        {
          title: '总金额(￥)',
          dataIndex: 'totalMoney'
        },
        {
          title: '更新时间',
          dataIndex: 'updateTime'
        },
        {
          title: '操作',
          dataIndex: 'action',
          fixed: 'right',
          align: 'center',
          width: 147,
          scopedSlots: { customRender: 'action' },
        },
      ],
      childrenDel: false,
      dropTrigger: '',
      treeData: [],
      selectedKeys: [],
      checkedKeys: [],
      replaceFields: {
        key: 'id',
        title: 'typeName',
        children: 'children',
      },
      checkStrictly: true,
      autoExpandParent: true,
      defaultExpandAll: true,
      url: {
        list: '/itilStockInfo/itilStockInfo/list',
        delete: '/itilStockInfo/itilStockInfo/delete',
        deleteBatch: '/itilStockInfo/itilStockInfo/deleteBatch',
        exportXlsUrl: '/itilStockInfo/itilStockInfo/exportXls',
        importExcelUrl: 'itilStockInfo/itilStockInfo/importExcel',
        downloadTemplateXlsUrl: '/itilStockInfo/itilStockInfo/downloadTemplate',
      },
      superFieldList: [],
      rightClickSelectedKey: '',
      //控制右键整个菜单显隐
      menuVisible: false,
      //控制右键菜单中删除、编辑菜单项的显隐
      menuItemVisible: true,
      nodeData: {},
      nodeType: '',
    }
  },
  created() {},
  mounted() {
    this.getSuperFieldList()
    this.getTreeData()
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
    downloadTemplateXlsUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.downloadTemplateXlsUrl}`
    },
  },
  methods: {
    deleteVerify(record) {
      var that = this
      that.$confirm({
        title: '确认删除',
        okText: '是',
        cancelText: '否',
        content: '当前物品库存不为0，是否删除',
        onOk: function () {
          that.handleDelete(record.id)
        }
      })
    },
    batchDelVerify: function () {
      if (this.selectedRowKeys.length <= 0) {
        this.$message.warning('请选择一条记录！')
        return
      } else {
        var ids = ''
        let str = '' // 记录有库存的编号
        let num = 0 // 记录有库存的商品数
        let nonum = 0 // 记录无库存的商品数
        for (let i = 0; i < this.selectionRows.length; i++) {
          ids += this.selectionRows[i].id + ','
          if (this.selectionRows[i].stockNum > 0) {
            num ++
            if (i==this.selectionRows.length - 1) {
              str += this.selectionRows[i].goodsCode
            } else {
              str += this.selectionRows[i].goodsCode + '、'
            }
          }
        }
        nonum = this.selectionRows.length - num
        let contentText = ''
        if (num > 0) {
          contentText = `本次删除包含：无库存(${nonum}项)、有库存(${num}项)。有库存商品编号如下：${str}`
        } else {
          contentText = '是否删除选中数据?'
        }
        var that = this
        this.$confirm({
          title: '确认删除',
          okText: '是',
          cancelText: '否',
          content: contentText,
          onOk: function () {
            that.loading = true
            deleteAction(that.url.deleteBatch, { ids: ids })
              .then((res) => {
                if (res.success) {
                  //重新计算分页问题
                  that.reCalculatePage(that.selectedRowKeys.length)
                  that.$message.success(res.message)
                  that.loadData()
                  that.onClearSelected()
                } else {
                  that.$message.warning(res.message)
                }
              })
              .finally(() => {
                that.loading = false
              })
          }
        })
      }
    },
    initDictConfig() {},
    getSuperFieldList() {
      let fieldList = []
      fieldList.push({ type: 'string', value: 'goodsCode', text: '物品编号', dictCode: '' })
      fieldList.push({ type: 'string', value: 'goodsName', text: '物品名称', dictCode: '' })
      fieldList.push({ type: 'string', value: 'unitCode', text: '物品单位', dictCode: '' })
      fieldList.push({ type: 'int', value: 'stockNum', text: '当前库存', dictCode: '' })
      fieldList.push({ type: 'double', value: 'totalMoney', text: '总金额', dictCode: '' })
      fieldList.push({ type: 'string', value: 'updateTime', text: '更新时间', dictCode: '' })
      this.superFieldList = fieldList
    },
    onChangePicker(data, dateString) {
      this.queryParam.time1 = dateString[0]
      this.queryParam.time2 = dateString[1]
    },
    getTreeData() {
      this.treeData = []
      getAction('/itilStockInfo/itilStockInfo/tree').then((res) => {
        if (res.success) {
          this.treeData = [...res.result]
        }
      })
    },
    onLoadData(treeNode) {
      var that = this
      return new Promise((resolve) => {
        let param = {
          id: treeNode.$vnode.key,
        }
        getAction('/itilStockInfo/itilStockInfo/tree', param).then((res) => {
          if (res.success) {
            /*let parentDataRef =that.nodeData.$parent.dataRef; debugger
            const children = parentDataRef.children;
            const currentDataRef = that.nodeData.dataRef
            const index = children.indexOf(currentDataRef)
            children.splice(index, 1);*/
            let parentDataRef = treeNode.dataRef
            let children = parentDataRef.children
            if (!children) {
              treeNode.dataRef.children = [...res.result]
            } else if (res.result.length >= children?.length) {
              children.sort((a, b) => {
                return res.result.findIndex((p) => p.id == a.id) - res.result.findIndex((p) => p.id == b.id)
              })
              res.result?.forEach((item, i, array) => {
                let child = children.find((p) => p.id == item.id)
                if (child) {
                  //child={...child,...item,children:child?.children};
                  ;({ typeName: child.typeName, sort: child.sort, describes: child.describes } = item)
                } else {
                  children.splice(i, 0, item)
                }
              })
            } else {
              children?.forEach((item, i, array) => {
                let index = res.result.findIndex((p) => p.id == item.id)
                if (index < 0) {
                  children.splice(i, 1)
                }
              })
            }
            // treeNode.dataRef.children = [...res.result]
          }
          resolve()
        })
      })
    },
    onContextMenuClick(treeKey, menuKey) {},
    dropStatus(visible) {
      if (visible == false) {
        this.dropTrigger = ''
      }
      this.menuVisible = !this.menuVisible
    },
    onExpand() {
      this.closeDrop()
    },
    onSelect(selectedKeys, e) {
      this.closeDrop()
      this.queryParam.typeId = e.node.eventKey
      this.loadData()
      this.selectedKeys = selectedKeys
      this.firstTitle = ''
      if (e.selectedNodes.length < 1) {
        return
      }
      const nodeData = e.selectedNodes[0].data.props.dataRef
      this.selectedNodeType = nodeData.type
      this.selectedNodeId = nodeData.id
      //向父组件弹射抛值
      // this.$emit('selected', this.selectedNodeType, this.selectedNodeId)
    },
    rightHandle(node) {
      this.childrenDel = node.node.dataRef.children != null && node.node.dataRef.children.length > 0 ? false : true
      this.nodeTypeName = node.node.dataRef.typeName
      this.rightClickSelectedKey = node.node.eventKey
      this.rightClickSelectedBean = node.node.dataRef
      this.nodeData = node.node
      this.menuItemVisible = this.nodeData.pos == '0-0' ? false : true
      this.menuVisible = true
      this.dropTrigger = 'contextmenu'
    },
    closeDrop() {
      this.dropTrigger = ''
      this.menuVisible = false
    },
    addchildNode() {
      this.closeDrop()
      this.$refs.modalForm1.add(this.rightClickSelectedKey)
      this.$refs.modalForm1.title = '添加子节点'
      this.nodeType = 'add'
    },
    editchildNode() {
      this.closeDrop()
      this.$refs.modalForm1.edit(this.rightClickSelectedKey)
      this.$refs.modalForm1.title = '编辑子节点'
      this.nodeType = 'edit'
    },
    modalFormOk1() {
      // this.getTreeData()
      if (this.nodeType == 'add') {
        this.onLoadData(this.nodeData)
      } else if (this.nodeType == 'edit') {
        this.onLoadData(this.nodeData.$parent)
      }
    },
    delChildNode() {
      let that = this
      that.closeDrop()
      this.$confirm({
        title: '确认删除',
        okText: '是',
        cancelText: '否',
        content: '是否删除选中数据?',
        onOk: function () {
          //that.filterKeys(that.nodeData)
          deleteAction('/itilDepotType/itilDepotType/delete', { id: that.rightClickSelectedKey })
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.onLoadData(that.nodeData.$parent)
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {})
        },
      })
    },
    handleTemplateXls() {
      const path = this.downloadTemplateXlsUrl
      document.getElementById('download').src = path
    },
  },
}
</script>
<style scoped lang='less'>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
</style>