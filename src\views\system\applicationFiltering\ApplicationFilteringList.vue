<template>
  <div style='height: 100%'>
    <a-card :bordered='false' style='width: 100%; flex: auto'>
      <div class='table-operator table-operator-style'>
        <a-button @click='handleAdd'>新增</a-button>
        <a-dropdown v-if="selectedRowKeys.length > 0">
          <a-menu slot="overlay" style='text-align: center'>
            <a-menu-item key='1' @click='batchDel'>删除</a-menu-item>
          </a-menu>
          <a-button> 批量操作
            <a-icon type='down' />
          </a-button>
        </a-dropdown>
      </div>
      <a-table
        ref='table'
        bordered
        :row-key='(record, index) => {return record.id}'
        :columns='columns'
        :dataSource='dataSource'
        :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
        :pagination='ipagination'
        :loading='loading'
        :rowSelection='{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }'
        @change='handleTableChange'>
        <span slot='action' class='caozuo' slot-scope='text, record'>
          <a @click='handleDetailPage(record)'>查看</a>
          <a-divider type='vertical' />
          <a-dropdown>
            <a class='ant-dropdown-link'>更多 <a-icon type='down' /></a>
            <a-menu slot='overlay'>
              <a-menu-item>
                <a @click='handleEdit(record)'>编辑</a>
              </a-menu-item>
               <a-menu-item v-if="record.strategyStatus==1">
                <a @click='handleForbid(record,0)'>启用</a>
              </a-menu-item>
               <a-menu-item v-else>
                <a @click='handleForbid(record,1)'>禁用</a>
              </a-menu-item>
              <a-menu-item>
                <a @click='confirmDelete(record.id)'>删除</a>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>
        <template slot='tooltip' slot-scope='text'>
          <a-tooltip placement='topLeft' :title='text' trigger='hover'>
            <div class='tooltip'>
              {{ text }}
            </div>
          </a-tooltip>
        </template>
        <template slot='scopeType' slot-scope='text'>
          <span>{{text==1?'批量':'全部'}}</span>
        </template>

        <template slot='status' slot-scope='text'>
          <span>{{text==1?'过滤应用':'展示应用'}}</span>
        </template>

        <template slot='strategyStatus' slot-scope='text'>
          <span :style="{color:text==1?'#FE9400':'#4BD863'}">{{text==1?'禁用':'启用'}}</span>
        </template>
      </a-table>
    </a-card>
    <application-filtering-modal ref='modalForm' @ok='modalFormOk'></application-filtering-modal>
  </div>
</template>

<script>
import { postAction} from '@/api/manage'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import applicationFilteringModal from '@views/system/applicationFiltering/modules/ApplicationFilteringModal.vue'
export default {
  name: 'ApplicationFiltering',
  components: {applicationFilteringModal },
  mixins: [JeecgListMixin],
  data() {
    return {
      url: {
        list: '/terminal/application/list',
        delete:'/terminal/application/delete',
        deleteBatch:'/terminal/application/deleteBatch',
        forbid:'/terminal/application/edit'
      },
      columns: [
        {
          title: '策略名称',
          dataIndex: 'strategyName'
        },
        {
          title: '策略描述',
          dataIndex: 'remark',
          scopedSlots: { customRender: 'tooltip' }
        },
        {
          title: '配置范围',
          dataIndex: 'scopeType',
          scopedSlots: { customRender: 'scopeType' }
        },
        {
          title: '展示方式',
          dataIndex: 'status',
          scopedSlots: { customRender: 'status' }
        },
        {
          title: '策略状态',
          dataIndex: 'strategyStatus',
          scopedSlots: { customRender: 'strategyStatus' }
        },
        {
          title: '操作',
          dataIndex: 'action',
          fixed: 'right',
          width: 150,
          scopedSlots: {
            customRender: 'action'
          }
        }
      ]
    }
  },
  methods: {
    handleForbid(record,status){
      let data=JSON.parse(JSON.stringify(record))
      data.strategyStatus=status;
     postAction(this.url.forbid,data).then((res)=>{
       if(res.success){
         this.loadData()
       }
     })
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
</style>