<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-item label="交接人" :labelCol="labelCol" :wrapperCol="wrapperCol" :required="true">
              <j-dict-select-tag v-model="shifteUser" placeholder="请选择交接人"
                dictCode="sys_users,realname,id,del_flag=0 and status!=2   order by create_time" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="交接记录" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <!-- <a-input v-decorator="['shiftrecord']" placeholder="请输入交接记录"  ></a-input> -->
              <a-textarea placeholder="请输入交接记录" v-decorator="['shiftrecord']" :rows="4" />
            </a-form-item>
          </a-col>
          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
  import {
    httpAction,
    getAction
  } from '@/api/manage'
  import pick from 'lodash.pick'
  import {
    validateDuplicateValue
  } from '@/utils/util'
  import JFormContainer from '@/components/jeecg/JFormContainer'
  import JDate from '@/components/jeecg/JDate'
  import JDictSelectTag from '@/components/dict/JDictSelectTag.vue'

  export default {
    name: 'DevopsArrangementShiftForm',
    components: {
      JFormContainer,
      JDate,
      JDictSelectTag,
    },
    props: {
      //流程表单data
      formData: {
        type: Object,
        default: () => {},
        required: false,
      },
      //表单模式：true流程表单 false普通表单
      formBpm: {
        type: Boolean,
        default: false,
        required: false,
      },
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false,
      },
    },
    data() {
      return {
        form: this.$form.createForm(this),
        model: {},
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          },
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          },
        },
        confirmLoading: false,
        validatorRules: {},
        url: {
          add: '/arrangementshift/devopsArrangementShift/add',
          edit: '/arrangementshift/devopsArrangementShift/edit',
          queryById: '/arrangementshift/devopsArrangementShift/queryById',
          doOffWork: '/arrangementshift/devopsArrangementShift/doOffWork',
        },
        shifteUser: '',
      }
    },
    computed: {
      formDisabled() {
        if (this.formBpm === true) {
          if (this.formData.disabled === false) {
            return false
          }
          return true
        }
        return this.disabled
      },
      showFlowSubmitButton() {
        if (this.formBpm === true) {
          if (this.formData.disabled === false) {
            return true
          }
        }
        return false
      },
    },
    created() {
      //如果是流程中表单，则需要加载流程表单data
      this.showFlowData()
    },
    methods: {
      add() {
        this.edit({})
      },
      edit(record) {
        this.form.resetFields()
        this.model = Object.assign({}, record)
        this.visible = true
        this.$nextTick(() => {
          this.form.setFieldsValue(
            pick(this.model, 'arrangementId', 'status', 'creattTime', 'endTime', 'shifteUser', 'shiftrecord')
          )
        })
      },
      //渲染流程表单数据
      showFlowData() {
        if (this.formBpm === true) {
          let params = {
            id: this.formData.dataId
          }
          getAction(this.url.queryById, params).then((res) => {
            if (res.success) {
              this.edit(res.result)
            }
          })
        }
      },
      submitForm() {
        if (this.shifteUser == '' || this.shifteUser == null) {
          this.$message.warning('请选择交接人')
          return
        }
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.model.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.doOffWork
              method = 'put'
            }
            let formData = Object.assign(this.model, values)
            formData.shifteUser = this.shifteUser
            httpAction(httpurl, formData, method)
              .then((res) => {
                if (res.success) {
                  that.$message.success(res.message)
                  that.$emit('ok')
                } else {
                  that.$message.warning(res.message)
                }
              })
              .finally(() => {
                that.confirmLoading = false
              })
          }
        })
      },
      popupCallback(row) {
        this.form.setFieldsValue(
          pick(row, 'arrangementId', 'status', 'creattTime', 'endTime', 'shifteUser', 'shiftrecord')
        )
      },
    },
  }
</script>