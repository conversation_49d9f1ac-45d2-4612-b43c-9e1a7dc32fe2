<template>
  <div style="height: 100%">
    <keep-alive exclude="knowledgeBaseInfo">
      <component :is="pageName" :data="data" :fromtype="fromtype" :back-route-info='backRouteInfo' style="height: 100%" />
    </keep-alive>
  </div>
</template>
<script>
import knowledgeSearchIndex from './knowledgeSearchIndex.vue'
import knowledgeSearchResult from './knowledgeSearchResult.vue'
import knowledgeBaseInfo from '../knowledgeBase/KnowledgeBaseInfo.vue'
import store from '@/store'
export default {
  name: 'knowledgeSearch',
  data() {
    return {
      isActive: 1,
      data: {},
      fromtype: 0,
      backRouteInfo:null
    }
  },
  components: {
    knowledgeSearchIndex,
    knowledgeSearchResult,
    knowledgeBaseInfo,
  },
  created() {
    //this.pButton1(1)
  },
  //使用计算属性
  computed: {
    pageName() {
      switch (this.isActive) {
        case 0:
          return 'knowledgeSearchResult'
        case 1:
          return 'knowledgeSearchIndex'
        default:
          return 'knowledgeBaseInfo'
      }
    },
  },
  watch:{
    //运维工作台路由跳至该页面后，搜索结果
    '$route':{
      handler(val){
        if(val.params&&val.params.routeName&&val.params.routePath){
          this.data={value:val.params.value}
          this.backRouteInfo={
            routeName:val.params.routeName,
            routePath:val.params.routePath,
            routeFlag:val.params.routeFlag,
            value:val.params.value
          }
          sessionStorage.setItem('backYWGZTRouteInfo', JSON.stringify(this.backRouteInfo))
          this.isActive=0
        }else{

        }
      },
      immediate:true,
      deep:true
    }
  },
mounted() {
  //从运维工作台跳至知识搜索界面，刷新页面，根据缓存，传值，重新加载搜索结果面层
  let infoJson=sessionStorage.getItem('backYWGZTRouteInfo')
  let info=infoJson&&infoJson.length>0?JSON.parse(infoJson):null
  if (info&&info.routeName&&info.routePath&&info.routeFlag==="ywgzt"){
    this.backRouteInfo=info
    this.data={value:info.value}
    this.isActive=0
  }
},
  destroyed() {
    //离开知识搜索页面，清空有运维平台跳至搜索界面相关缓存数据
    sessionStorage.setItem('backYWGZTRouteInfo', '')
  },
  methods: {
    pButton1(index) {
      this.isActive = index
    },
    pButton2(index, item, fromtype) {
      this.isActive = index
      this.data = item
      this.fromtype = fromtype || 0
    },
  },
}
</script>