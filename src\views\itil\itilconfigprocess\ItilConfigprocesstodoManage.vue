<template>
  <a-row :gutter="10" style="height: 100%">
    <a-col :xl="4" :lg="4" :md="6" :sm="6" :xs="8" style="height: 100%; overflow: hidden">
      <a-card :bordered="false" style="height: 100%; overflow-x: auto">
        <!-- 按钮操作区域 -->
        <a-row style="margin-left: 14px">
          <!--<a-button @click="refresh" type="default" icon="reload" :loading="loading">刷新</a-button>-->
        </a-row>
        <div style="background: #fff; height: 100%">
          <a-input-search @search="onSearch" style="width: 100%; margin-bottom: 12px" placeholder="请输入类型名称" />
          <!-- 树-->
          <a-col :sm="24">
            <template>
              <a-dropdown :trigger="[this.dropTrigger]" @visibleChange="dropStatus">
                <span style="user-select: none">
                  <a-tree
                    multiple
                    @select="onSelect"
                    @check="onCheck"
                    :selectedKeys="selectedKeys"
                    :checkedKeys="checkedKeys"
                    :treeData="departTree"
                    :checkStrictly="checkStrictly"
                    :expandedKeys="iExpandedKeys"
                    :autoExpandParent="autoExpandParent"
                    @expand="onExpand"
                  />
                </span>
              </a-dropdown>
            </template>
          </a-col>
        </div>
      </a-card>
    </a-col>
    <!--右边-->
    <a-col :xl="20" :lg="20" :md="18" :sm="18" :xs="16" style="height: 100%">
      <a-row :gutter="10" style="height: 100%" class="vScroll zxw">
        <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
          <!-- 查询区域 -->
          <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class="card-style">
            <div class="table-page-search-wrapper">
              <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
                <a-row :gutter="24" ref="row">
                  <a-col :span="spanValue">
                    <a-form-item label="名称">
                      <a-input
                        placeholder="请输入"
                        autocomplete="off"
                        :allowClear="true"
                        v-model="queryParam.title"
                      ></a-input>
                    </a-form-item>
                  </a-col>
                  <a-col :span="colBtnsSpan()">
                    <span
                      class="table-page-search-submitButtons"
                      :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                    >
                      <a-button type="primary" @click="searchQuery">查询</a-button>
                      <a-button @click="searchReset" style="margin-left: 10px">重置</a-button>
                    </span>
                  </a-col>
                </a-row>
              </a-form>
            </div>
            <!-- 查询区域-END -->
          </a-card>
          <a-card :bordered="false" style="padding-bottom: 0; height: calc(100% - 90px)" class="core">
            <!-- 操作按钮区域 -->
            <div class="table-operator tableBottom">
              <!-- <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>&nbsp; -->
              <a-button @click="handleExportXlsEven('配置我的待办')">导出</a-button>&nbsp;
            </div>
            <!-- table区域-begin -->
            <div>
              <a-table
                ref="table"
                bordered
                rowKey="id"
                :columns="columns"
                :dataSource="dataSource"
                :pagination="ipagination"
                :loading="loading"
                :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
                class="j-table-force-nowrap"
                @change="handleTableChange"
              >
                <span slot="status" slot-scope="status, record">
                  <div v-if="record.status == 0">草稿</div>
                  <div v-if="record.status == 1">处理中</div>
                  <div v-if="record.status == 2">已结束</div>
                  <div v-if="record.status == 3">已撤回</div>
                </span>
                <template slot="htmlSlot" slot-scope="text">
                  <div v-html="text"></div>
                </template>
                <span slot="action" slot-scope="text, record" class="caozuo">
                  <a @click="handleDeatails(record)">查看</a>
                  <a-divider type="vertical" />
                  <a @click="passTask(record)">处理</a>
                </span>
              </a-table>
            </div>
            <a-modal :title="modalTaskTitle" v-model="modalTaskVisible" :mask-closable="false" :width="500">
              <div v-if="modalTaskVisible">
                <a-form ref="form" :model="form" :label-width="85" :rules="formValidate">
                  <a-form-item label="审核结果" prop="reason">
                    <a-radio-group name="radioGroup" :default-value="1" v-model="form.type">
                      <a-radio :value="0"> 通过 </a-radio>
                      <a-radio :value="1"> 驳回 </a-radio>
                    </a-radio-group>
                  </a-form-item>
                  <a-form-item label="审批意见" prop="reason">
                    <a-input type="textarea" :allowClear="true" autocomplete="off" v-model="form.comment" :rows="4" />
                  </a-form-item>
                  <a-form-item label="文件上传">
                    <j-upload v-model="form.file" :number="5"></j-upload>
                  </a-form-item>
                  <a-form-item label="下一审批人" prop="assignees" v-show="showAssign" :error="error">
                    <a-select
                      v-model="form.assignees"
                      :getPopupContainer="(node) => node.parentNode"
                      placeholder="请选择"
                      allowClear
                      mode="multiple"
                      :loading="userLoading"
                    >
                      <a-select-option v-for="(item, i) in assigneeList" :key="i" :value="item.id">{{
                        item.username
                      }}</a-select-option>
                    </a-select>
                  </a-form-item>
                  <a-form-item label="下一审批人" v-show="isGateway">
                    <span>分支网关处暂不支持自定义选择下一审批人，将发送给下一节点所有人</span>
                  </a-form-item>
                  <div v-show="form.type == 1">
                    <a-form-item label="驳回至">
                      <a-select
                        v-model="form.backTaskKey"
                        :getPopupContainer="(node) => node.parentNode"
                        :loading="backLoading"
                        @change="changeBackTask"
                      >
                        <a-select-option v-for="(item, i) in backList" :key="i" :value="item.key">{{
                          item.name
                        }}</a-select-option>
                      </a-select>
                    </a-form-item>
                    <a-form-item
                      label="指定原节点审批人"
                      prop="assignees"
                      v-show="form.backTaskKey != -1"
                      :error="error"
                    >
                      <a-select
                        v-model="form.assignees"
                        placeholder="请选择"
                        :getPopupContainer="(node) => node.parentNode"
                        allowClear
                        mode="multiple"
                        :loading="userLoading"
                      >
                        <a-select-option v-for="(item, i) in assigneeList" :key="i" :value="item.id">{{
                          item.username
                        }}</a-select-option>
                      </a-select>
                    </a-form-item>
                  </div>
                  <a-form-item label="选择委托人" prop="userId" :error="error" v-show="form.type == 2">
                    <JSelectUserByDep v-model="form.userId" :multi="false"></JSelectUserByDep>
                  </a-form-item>
                  <a-form-item label="消息通知">
                    <a-checkbox v-model="form.sendMessage">站内消息通知</a-checkbox>
                    <a-checkbox v-model="form.sendSms" disabled>短信通知</a-checkbox>
                    <a-checkbox v-model="form.sendEmail" disabled>邮件通知</a-checkbox>
                  </a-form-item>
                </a-form>
              </div>
              <div slot="footer" style="text-align: right">
                <a-button type="text" @click="modalTaskVisible = false">取消</a-button>
                <a-button type="primary" :loading="submitLoading" @click="handelSubmit">提交</a-button>
              </div>
            </a-modal>
            <itilConfigProcessDetails ref="itilConfigProcessDetails" @ok="modalFormOk"></itilConfigProcessDetails>
          </a-card>
        </a-col>
      </a-row>
    </a-col>
  </a-row>
</template>

<script>
// import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
// import ItilConfigItemTypeTreeModal from '@/views/itil/itilconfigitemlibrary/modules/ItilConfigItemTypeTreeModal'
//  import ItilConfigItemLibraryModal from '@/views/itil/itilconfigitemlibrary/modules/ItilConfigItemLibraryModal'
import { queryItilconfigItemtypeTreeList, deleteByConfigItemtypeId, searchByConfigItemtype } from '@/api/api'
import { getAction, deleteAction, putAction, postAction, downFile } from '@/api/manage'
import JUpload from '@/components/jeecg/JUpload'
import JSelectUserByDep from '@/components/jeecgbiz/JSelectUserByDep'
import itilConfigProcessDetails from './modules/ItilConfigProcessDetails'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'

export default {
  name: 'ItilConfigprocesstodoManage',
  mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
  components: {
    JUpload,
    JSelectUserByDep,
    itilConfigProcessDetails,
    // ItilConfigItemLibraryModal,
    // ItilConfigItemTypeTreeModal
  },
  data() {
    return {
      formItemLayout: {
        labelCol: {
          style: 'width:70px',
        },
        wrapperCol: {
          style: 'width:calc(100% - 70px)'
        }
      },
      description: '配置项库管理页面',
      //树
      departTree: [],
      treeData: [],
      dropTrigger: '',
      selectedKeys: [],
      checkedKeys: [],
      checkStrictly: true,
      iExpandedKeys: [],
      autoExpandParent: true,
      currFlowId: '',
      currFlowName: '',
      rightClickSelectedBean: {},
      //审批
      form: {
        id: '',
        userId: '',
        procInstId: '',
        comment: '',
        file: '',
        type: 1,
        assignees: [],
        backTaskKey: '-1',
        sendMessage: true,
        sendSms: false,
        sendEmail: false,
      },
      backList: [
        {
          key: '-1',
          name: '发起人',
        },
      ],
      isGateway: false,
      error: '',
      showAssign: false,
      submitLoading: false, // 添加或编辑提交状态
      modalTaskVisible: false,
      modalTaskTitle: '',
      formValidate: {
        // 表单验证规则
      },
      assigneeList: [],
      userLoading: false,
      backLoading: false,
      //查询
      queryParam: {
        configType: '',
      },
      // 表头
      columns: [
        {
          title: '编号',
          dataIndex: 'code',
        },
        {
          title: '名称',
          dataIndex: 'title',
        },
        {
          title: '类型',
          dataIndex: 'configType_text',
        },
        {
          title: '审批状态',
          dataIndex: 'status',
          scopedSlots: { customRender: 'status' },
        },

        {
          title: '所属人',
          dataIndex: 'owner_text',
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 147,
          scopedSlots: { customRender: 'action' },
        },
      ],
      url: {
        list: '/itilconfigprocess/ItilConfigProcess/todoList',
        delete: '/itilconfigitemlibrary/itilConfigItemLibrary/delete',
        deleteBatch: '/itilconfigitemlibrary/itilConfigItemLibrary/deleteBatch',
        importExcelUrl: 'itilconfigitemlibrary/itilConfigItemLibrary/importExcel',
        getNextNode: '/activiti_process/getNextNode',
        pass: '/itilconfigprocess/ItilConfigProcess/pass',
        back: '/actTask/back',
        backToTask: '/actTask/backToTask',
        exportXlsUrl: '/itilconfigprocess/ItilConfigProcess/todoExportXls',
      },
      uid: this.$route.query.id,
      dictOptions: {},
    }
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
    //可行性测试，根据文件路径动态加载组件
    LcDict: function () {
      var myComponent = () => import(`@/components/dict/JDictSelectTag`)
      return myComponent
    },
  },
  mounted() {
    this.refresh()
    if (this.uid) {
      this.queryParam.title = this.uid.title
      this.searchQuery()
      this.loadData()
    }
  },
  // created(){
  //   this.refresh();
  // },
  methods: {
    handleExportXlsEven(fileName) {
      if (!fileName || typeof fileName != 'string') {
        fileName = '导出文件'
      }
      let param = this.getQueryParams()
      if (this.selectedRowKeys && this.selectedRowKeys.length > 0) {
        param['selections'] = this.selectedRowKeys.join(',')
      }
      downFile(this.url.exportXlsUrl, param).then((data) => {
        if (!data) {
          this.$message.warning('文件下载失败')
          return
        }
        if (typeof window.navigator.msSaveBlob !== 'undefined') {
          window.navigator.msSaveBlob(new Blob([data], { type: 'application/vnd.ms-excel' }), fileName + '.xls')
        } else {
          let url = window.URL.createObjectURL(new Blob([data], { type: 'application/vnd.ms-excel' }))
          let link = document.createElement('a')
          link.style.display = 'none'
          link.href = url
          link.setAttribute('download', fileName + '.xls')
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link) //下载完成移除元素
          window.URL.revokeObjectURL(url) //释放掉blob对象
        }
      })
    },
    // loadData() {
    //   this.refresh();
    // },
    handleDeatails: function (record) {
      this.$refs.itilConfigProcessDetails.edit(record)
      this.$refs.itilConfigProcessDetails.title = '配置详情'
      this.$refs.itilConfigProcessDetails.disableSubmit = false
      // this.$refs.itilConfigProcessDetails.tabaaa(record)
    },
    refresh() {
      this.loading = true
      this.loadTree()
    },
    //查询树
    loadTree() {
      var that = this
      that.treeData = []
      that.departTree = []
      queryItilconfigItemtypeTreeList().then((res) => {
        if (res.success) {
          //部门全选后，再添加部门，选中数量增多
          this.allTreeKeys = []
          for (let i = 0; i < res.result.length; i++) {
            let temp = res.result[i]
            that.treeData.push(temp)
            that.departTree.push(temp)
            that.setThisExpandedKeys(temp)
            // that.getAllKeys(temp);
          }
          this.loading = false
        }
      })
    },
    setThisExpandedKeys(node) {
      if (node.children && node.children.length > 0) {
        this.iExpandedKeys.push(node.key)
        for (let a = 0; a < node.children.length; a++) {
          this.setThisExpandedKeys(node.children[a])
        }
      }
    },
    onSearch(value) {
      let that = this
      if (value) {
        searchByConfigItemtype({ typeName: value }).then((res) => {
          if (res.success) {
            that.departTree = []
            for (let i = 0; i < res.result.length; i++) {
              let temp = res.result[i]
              that.departTree.push(temp)
            }
          } else {
            that.$message.warning(res.message)
          }
        })
      } else {
        that.loadTree()
      }
    },
    onExpand(expandedKeys) {
      // if not set autoExpandParent to false, if children expanded, parent can not collapse.
      // or, you can remove all expanded children keys.
      this.iExpandedKeys = expandedKeys
      this.autoExpandParent = false
    },
    // 右键点击下拉框改变事件
    dropStatus(visible) {
      if (visible == false) {
        this.dropTrigger = ''
      }
    },
    //选择树的方法
    onSelect(selectedKeys, e) {
      this.hiding = false
      let record = e.node.dataRef
      this.currSelected = Object.assign({}, record)
      this.model = this.currSelected
      this.selectedKeys = [record.key]
      this.model.parentId = record.parentId
      this.setValuesToForm(record)
      // this.$refs.departAuth.show(record.id);
    },
    // 触发onSelect事件时,为部门树右侧的form表单赋值
    setValuesToForm(record) {
      this.queryParam.configType = record.key
      this.loadData()
    },
    getCurrSelectedTitle() {
      return !this.currSelected.title ? '' : this.currSelected.title
    },
    onCheck(checkedKeys, info) {
      this.hiding = false
      //this.checkedKeys = checkedKeys.checked
      // <!---- author:os_chengtgen -- date:20190827 --  for:切换父子勾选模式 =======------>
      if (this.checkStrictly) {
        this.checkedKeys = checkedKeys.checked
      } else {
        this.checkedKeys = checkedKeys
      }
      // <!---- author:os_chengtgen -- date:20190827 --  for:切换父子勾选模式 =======------>
    },
    // 右键店家下拉关闭下拉框
    closeDrop() {
      this.dropTrigger = ''
    },
    //配置审批
    passTask(v) {
      this.modalTaskTitle = '配置项处理'
      this.form.id = v.id
      this.form.procInstId = v.procInstId
      this.form.priority = v.priority
      this.form.type = 0
      this.modalTaskVisible = true
      this.userLoading = true
      getAction(this.url.getNextNode, { procDefId: v.procDefId, currActId: v.key }).then((res) => {
        this.userLoading = false
        if (res.success) {
          if (res.result.type == 3 || res.result.type == 4) {
            this.isGateway = true
            this.showAssign = false
            this.error = ''
            return
          }
          this.isGateway = false
          if (res.result.users && res.result.users.length > 0) {
            this.error = ''
            this.assigneeList = res.result.users
            // 默认勾选
            let ids = []
            res.result.users.forEach((e) => {
              ids.push(e.username)
            })
            this.form.assignees = ids
            this.showAssign = true
          } else {
            this.form.assignees = []
            this.showAssign = false
          }
        }
      })
    },
    handelSubmit() {
      this.submitLoading = true
      var formData = Object.assign({}, this.form)
      formData.assignees = formData.assignees.join(',')
      if (formData.type == 0) {
        // 通过
        if (this.showAssign && formData.assignees.length < 1) {
          this.$message.error('请至少选择一个审批人')
          this.submitLoading = false
          return
        } else {
          this.error = ''
        }
        postAction(this.url.pass, formData).then((res) => {
          this.submitLoading = false
          if (res.success) {
            this.$message.success('操作成功')
            this.modalTaskVisible = false
            this.loadData()
          }
        })
      } else if (formData.type == 1) {
        // 驳回
        if (formData.backTaskKey == '-1') {
          // 驳回至发起人
          postAction(this.url.back, formData).then((res) => {
            this.submitLoading = false
            if (res.success) {
              this.$message.success('操作成功')
              this.modalTaskVisible = false
              this.loadData()
            }
          })
        } else {
          // 自定义驳回
          if (formData.backTaskKey != '-1' && formData.assignees.length < 1) {
            this.$message.error('请至少选择一个审批人')
            this.submitLoading = false
            return
          } else {
            this.error = ''
          }
          postAction(this.url.backToTask, formData).then((res) => {
            this.submitLoading = false
            if (res.success) {
              this.$message.success('操作成功')
              this.modalTaskVisible = false
              this.loadData()
            }
          })
        }
      }
    },
    changeBackTask(v) {
      if (v == '-1') {
        return
      }
      this.userLoading = true
      getAction(this.url.getNode + v).then((res) => {
        this.userLoading = false
        if (res.success) {
          if (res.result.users && res.result.users.length > 0) {
            this.assigneeList = res.result.users
            // 默认勾选
            let ids = []
            res.result.users.forEach((e) => {
              ids.push(e.username)
            })
            this.form.assignees = ids
          }
        }
      })
    },
    // <!---- author:os_chengtgen -- date:20190827 --  for:切换父子勾选模式 =======------>

    initDictConfig() {},
  },
}
</script>
<style lang="less" scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
/*表头样式*/
::v-deep .ant-table-thead > tr > th {
  text-align: center;
  white-space: nowrap;
}

/*内容对齐方式、省略显示*/
::v-deep .ant-table-tbody > tr > td {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;

  &:first-child,
  &:nth-child(2),
  &:nth-child(4),
  &:nth-child(5),
  &:nth-child(6),
  &:nth-child(7) {
    text-align: center;
  }

  &:nth-child(3) {
    text-align: left;
  }
}
</style>
