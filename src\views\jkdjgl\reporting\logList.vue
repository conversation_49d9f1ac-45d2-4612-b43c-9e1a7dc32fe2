<template>
  <a-row :gutter='10' style='height: 100%' class='vScroll'>
    <a-col style='width: 100%; height: 100%; display: flex; flex-direction: column'>
      <!-- 查询区域 -->
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <div class='table-page-search-wrapper-style'>
          <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
            <a-row :gutter='24' ref='row'>
              <a-col :span='spanValue'>
                <a-form-item label='日志类型'>
                  <a-select placeholder='请输入日志类型' v-model='queryParam.logType' :allowClear='true'>
                    <a-select-option value="0">推送</a-select-option>
                    <a-select-option value="1">接收</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label='操作类型'>
                  <a-select placeholder='请输入操作类型' v-model='queryParam.operateType' :allowClear='true'>
                    <a-select-option value="I">新增</a-select-option>
                    <a-select-option value="U">编辑</a-select-option>
                    <a-select-option value="D">删除</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label='日志结果'>
                  <a-select placeholder='请输入日志结果' v-model='queryParam.resultFlag' :allowClear='true'>
                    <a-select-option :value="true">true</a-select-option>
                    <a-select-option :value="false">false</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span='spanValue' v-show="toggleSearchStatus">
                <a-form-item label='上级单位'>
                  <a-tree-select placeholder='请选择上级单位' v-model='queryParam.parentDepartCode' :allowClear='true'
                                 :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }"
                    :treeData="parentTree" :replaceFields='{value:"departNameEn"}'></a-tree-select>
                </a-form-item>
              </a-col>
              <a-col :span='spanValue' v-show="toggleSearchStatus">
                <a-form-item label='下级单位'>
                  <a-tree-select placeholder='请选择下级单位' v-model='queryParam.childDepartCode' :allowClear='true'
                                 :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }"
                                 :treeData="parentTree" :replaceFields='{value:"departNameEn"}'></a-tree-select>
                </a-form-item>
              </a-col>
              <a-col :span='spanValue' v-show="toggleSearchStatus">
                <a-form-item label="时间范围">
                  <a-range-picker @change="onChange" format="YYYY-MM-DD HH:mm:ss" />
                </a-form-item>
              </a-col>
              <a-col :span='colBtnsSpan()'>
                <span class='table-page-search-submitButtons'
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button type='primary' class='btn-search btn-search-style' @click='searchQuery'>查询</a-button>
                  <a-button class='btn-reset btn-reset-style' @click='searchReset'>重置</a-button>
                  <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <!-- 查询区域-END -->
      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <!-- table区域-begin -->
        <a-table ref='table' bordered rowKey='id' :columns='columns' :dataSource='dataSource'
          :scroll='dataSource.length>0?{x:"max-content"}:{}' :pagination='ipagination' :loading='loading'
          @change='handleTableChange'>
          <span class='caozuo' slot='action' slot-scope='text, record'>
            <a @click='handleDetailPage(record)'>查看</a>
          </span>
          <template slot='tooltip' slot-scope='text'>
            <a-tooltip placement='topLeft' :title='text' trigger='hover'>
              <div class='tooltip'>
                {{ text }}
              </div>
            </a-tooltip>
          </template>
          <span slot='logType' slot-scope='text'>{{ text == 0 ? '推送': '接收' }}</span>
          <span slot='dataType' slot-scope='text'>{{getDataType(text)}}</span>
          <template slot='tooltip' slot-scope='text'>
            <a-tooltip placement='topLeft' :title='text' trigger='hover'>
              <div class='tooltip'>
                {{ text }}
              </div>
            </a-tooltip>
          </template>
          <template slot='tooltipData' slot-scope='text'>
            <a-tooltip placement='left' :title='JSON.stringify(text)' trigger='hover'>
              <div class='tooltip'>
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
      </a-card>
    </a-col>
  </a-row>
</template>

<script>
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import {
    getAction
  } from '@/api/manage'
  import {
    YqFormSearchLocation
  } from '@/mixins/YqFormSearchLocation'
  export default {
    name: 'logList',
    mixins: [JeecgListMixin, YqFormSearchLocation],
    data() {
      return {
        description: '日志管理页面',
        formItemLayout: {
          labelCol: {
            style: 'width:100px'
          },
          wrapperCol: {
            style: 'width:calc(100% - 100px)'
          }
        },
        disableMixinCreated: true,
        queryParam: {
          childDepartCode: null,
          parentDepartCode: null,
          logType: undefined,
          resultFlag: undefined
        },
        parentTree: [],
        columns: [{
            title: '序号',
            dataIndex: '',
            fixed: 'left',
            key: 'rowIndex',
            customCell: () => {
              let cellStyle = 'text-align:center;width:60px'
              return {
                style: cellStyle
              }
            },
            customRender: function (t, r, index) {
              return parseInt(index) + 1
            },
          }, {
            title: '结果标识',
            dataIndex: 'resultFlag',
            customCell: () => {
              let cellStyle = 'text-align: center; min-width: 100px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '日志类型',
            dataIndex: 'logType',
            scopedSlots: {
              customRender: 'logType'
            },
            customCell: () => {
              let cellStyle = 'text-align: center; min-width: 100px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '数据类型',
            dataIndex: 'dataType',
            scopedSlots: {
              customRender: 'dataType'
            },
            customCell: () => {
              let cellStyle = 'text-align: center; min-width: 100px;max-width: 100px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '操作类型',
            dataIndex: 'operateType',
            scopedSlots: {
              customRender: 'dataType'
            },
            customCell: () => {
              let cellStyle = 'text-align: center; min-width: 100px;max-width: 100px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '下级单位名称',
            dataIndex: 'childDepartName',
            customCell: () => {
              let cellStyle = 'text-align: center; min-width: 100px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '上级单位名称',
            dataIndex: 'parentDepartName',
            customCell: () => {
              let cellStyle = 'text-align: center; min-width: 100px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '数据内容',
            dataIndex: 'dataContent',
            scopedSlots: {
              customRender: 'tooltipData'
            },
            customCell: () => {
              let cellStyle = 'text-align: center; min-width: 100px;max-width: 350px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '响应内容',
            dataIndex: 'responseContent',
            scopedSlots: {
              customRender: 'tooltip'
            },
            customCell: () => {
              let cellStyle = 'text-align: center; min-width: 100px;max-width: 350px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '记录生成时间',
            dataIndex: 'createTime',
            customCell: () => {
              let cellStyle = 'text-align: center; width: 100px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '操作',
            dataIndex: 'action',
            fixed: 'right',
            customCell: () => {
              let cellStyle = 'text-align: center; width: 80px'
              return {
                style: cellStyle
              }
            },
            scopedSlots: {
              customRender: 'action'
            }
          }
        ],
        url: {
          list: '/dataReport/manage/logList',
          departList: '/device/statis/queryMyDeptTreeList'
        }
      }
    },
    created() {
      this.getDepart()
    },
    mounted() {
      let params = this.$route.query
      this.queryParam.logType = params.logType != undefined ? params.logType : undefined
      this.queryParam.resultFlag = params.resultFlag != undefined ? params.resultFlag : undefined
      this.queryParam.childDepartCode = params.childDepartCode != null ? params.childDepartCode : null
      this.queryParam.parentDepartCode = params.parentDepartCode != null ? params.parentDepartCode : null
      this.loadData()
    },
    methods: {
      getDepart() {
        getAction(this.url.departList).then((res) => {
          this.parentTree = res.result
        })
      },
      getDataType(data) {
        if (data == 'device') {
          return '设备'
        } else if (data == 'depart') {
          return '单位'
        } else if (data == 'terminal') {
          return '终端'
        } else if (data == 'data') {
          return '监控数据'
        } else if (data == 'statistics') {
          return '统计数据'
        } else if (data == 'I') {
          return '新增'
        } else if (data == 'U') {
          return '编辑'
        } else {
          return '删除'
        }
      },
      onChange(date, dateString) {
        this.queryParam.startTime = dateString[0]
        this.queryParam.endTime = dateString[1]
      },
    }
  }
</script>
<style lang='less' scoped>
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';
</style>