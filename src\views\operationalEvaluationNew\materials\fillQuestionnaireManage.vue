<template>
  <div style="height:100%;overflow: hidden;overflow-y: auto"> 
    <component :is="pageName" style="height:100%" :data="data"/>
  </div>
</template>
<script>
import fillQuestionnaireList from './modules/fillQuestionnaireList'
export default {
  name: "fillQuestionnaireManage",
  data() {
    return {
      isActive: 0,
      data:{}
    };
  },
  components: {
    fillQuestionnaireList
  },
  created(){
    this.pButton1(0);
  },
  //使用计算属性
  computed: {
    pageName() {
      switch (this.isActive) {
        case 0:
          return "fillQuestionnaireList";
          break;
        default:
          return "fillQuestionnaireList";
          break;
      }
    }
  },
  methods: {
    pButton1(index) {
      this.isActive = index;
    },
    pButton2(index,item) {
      this.isActive = index;
      this.data = item;
    }
  }
}
</script>