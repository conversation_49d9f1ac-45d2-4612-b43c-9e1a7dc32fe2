<template>
  <a-modal
    :title="title"
    :width="width"
    :visible="visible"
    :centered="true"
    switchFullscreen
    @ok="handleOk"
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @cancel="handleCancel"
    cancelText="关闭">
    <template slot="footer">
      <a-button type="primary" @click="textCont">测试连接</a-button>
      <a-button type="primary" @click="handleOk">确定</a-button>
      <a-button @click="handleCancel">关闭</a-button>
    </template>
    <devops-backup-resource-form ref="realForm" @ok="submitCallback" :disabled="disableSubmit"></devops-backup-resource-form>
  </a-modal>
</template>

<script>

  import DevopsBackupResourceForm from './DevopsBackupResourceForm'
  export default {
    name: 'DevopsBackupResourceModal',
    components: {
      DevopsBackupResourceForm
    },
    data () {
      return {
        title:'',
        width:800,
        visible: false,
        disableSubmit: false
      }
    },
    methods: {
      add () {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.add();
        })
      },
      edit (record) {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.edit(record);
        })
      },
      close () {
        this.$emit('close');
        this.visible = false;
      },
      handleOk () {
        this.$refs.realForm.submitForm();
      },
      textCont(){
        this.$refs.realForm.textCont();
      },
      submitCallback(){
        this.$emit('ok');
        this.visible = false;
      },
      handleCancel () {
        this.close()
      }
    }
  }
</script>
<style scoped lang='less'>
@import '~@assets/less/normalModal.less';
</style>