<template>
  <div :class='[showOpmgKInfo?"opmg-know-body":"one-click-help-body"]'>
    <div class='body-info'>
      <!--富文本、模板、附件-->
      <div class='plan-attachment-info'>
        <!--富文本、模板-->
        <div id='printContent' class='plan' v-html='kInfo.plan'></div>
        <!-- 附件-->
        <attachment-preview
          :k-info='kInfo'
          :approvalView='approvalView'
          :can-download='canDownload'
          :kkfileview-url='kkfileviewUrl'
          :show-opmg-k-info='showOpmgKInfo'
          :show-share='showShare'
        ></attachment-preview>
      </div>
      <!-- 评论、关联、审批历史-->
      <!--      分享页面不加载tab-->
      <!--      非分享页面加载tab：运维助手只显示评论，运维中心评论、关联、审批历史都显示，其中关联是否显示还需读取配置字典-->
      <div class='table-info' v-if='!showShare'>
        <a-tabs :active-key='defaultActiveKey'  :animated="false" @change="callback">
          <a-tab-pane key="1" tab="评论">
            <knowledge-comment
              :approval-view='approvalView'
              :k-info='kInfo'
              :like-status='likeStatus'
              :unlike-status='unlikeStatus'
              :show-opmg-k-info='showOpmgKInfo'
            >
            </knowledge-comment>
          </a-tab-pane>

          <a-tab-pane v-if='associationVisible==1&&showOpmgKInfo' key="2" tab="关联">
            <knowledge-relation
              :approval-view='approvalView'
              :k-info='kInfo'>
            </knowledge-relation>
          </a-tab-pane>
          <a-tab-pane key="3" tab="审批历史" v-if='showOpmgKInfo'>
            <knowledge-approval-history :k-info='kInfo'></knowledge-approval-history>
          </a-tab-pane>
        </a-tabs>
      </div>

      <!--      <div class='table-info'>
      <a-tabs :animated="false" default-active-key="1" :active-key='defaultActiveKey' @change="callback">
        <a-tab-pane key="1" tab="评论">
          <div class="table-class">
            <knowledge-comment
              :knowledge-info='kInfo'
              :like-status='likeStatus'
              :unlike-status='unlikeStatus'
              :showOpmgKInfo='false'>
            </knowledge-comment>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>-->
    </div>
  </div>
</template>
<script>
import attachmentPreview from '@views/opmg/knowledgeManagement/knowledgeBase/modules/AttachmentPreview.vue'
import knowledgeApprovalHistory
  from '@views/opmg/knowledgeManagement/knowledgeBase/modules/KnowledgeApprovalHistory.vue'
import knowledgeRelation from '@views/opmg/knowledgeManagement/knowledgeBase/modules/KnowledgeRelation.vue'
import knowledgeComment from '@views/opmg/knowledgeManagement/knowledgeBase/modules/KnowledgeComment.vue'
import { queryConfigureDictItem } from '@api/api'

export default {
  name: "",
  props: {
    kInfo: {
      type: Object,
      required: true
    },
    kkfileviewUrl: {
      type: [String],
      required: true
    },
    canDownload: {
      type: Boolean,
      required: false,
      default: false
    },
    likeStatus: {
      type: Number,
      required: true,
      default: -1

    },
    unlikeStatus: {
      type: Number,
      required: true,
      default: -1
    },
    /**若是通过知识审批列表打卡查看，
     收藏、分享、打印、评论、关联、点赞、点踩都不可操作性，
     附件统统可以下载，同时告诉管理员，知识创建者设置的允许下载附件状态*/
    approvalView: {
      type: Boolean,
      required: false,
      default: false
    },
    /*用于区分运维中心和运维助手知识详情页面，采用不同的样式,默认采用运维中心知识详情样式*/
    showOpmgKInfo: {
      type: Boolean,
      required: false,
      default: true
    },
    /*区分是否是分享页面，默认不是分享页面*/
    showShare: {
      type: Boolean,
      required: false,
      default: false
    },
  },
  components: { attachmentPreview,knowledgeComment, knowledgeRelation, knowledgeApprovalHistory},
  data() {
    return {
      defaultActiveKey: '1',
      /**获取配置字典值，控制关联显隐，默认显示*/
      associationVisible: 1,
    }
  },
  watch: {
    kInfo: {
      handler(val) {
        this.defaultActiveKey= '1'
      },
      deep:true,
      immediate:true
    }
  },
  mounted() {
    if (this.showOpmgKInfo && !this.showShare) {
      this.getConfigureDict()
    }
  },
  methods: {
    getConfigureDict() {
      queryConfigureDictItem({
        parentCode: 'knowledgeAssociationProcessVisible',
        childCode: 'associationVisible'
      }).then((res) => {
        if (res.success) {
          this.associationVisible = res.result
        } else {
          this.associationVisible = '1'
        }
      }).catch(() => {
        this.associationVisible = '1'
      })
    },
    callback(key) {
      this.defaultActiveKey = key
    },
  }
}
</script>

<style scoped lang="less">
//页面第二部分样式
.opmg-know-body {
  border-radius: 3px;
  padding: 24px;
  background: #fff;
  //height: calc(100% - 16px - 16px - 200px);
  //min-height: 200px;
  overflow: hidden;

  .body-info {
    //height: 100%;
    padding-right: 2px;
    overflow-y: auto !important;
  }

  .plan-attachment-info {
    .plan {
      background: #F7F7F7;
      font-size: 14px;
      padding: 16px;
      border-radius: 2px;
      white-space: normal;
      word-break: break-all;
      overflow-x: auto;

      img{
        margin-right: 12px !important;
      }
    }
  }
  .table-info {

  }
}

.one-click-help-body {
  .body-info {
    //min-height: 100px;

    .plan-attachment-info {
      .plan {
        background: #F7F7F7;
        font-size: 14px;
        padding: 16px;
        border-radius: 2px;
        white-space: normal;
        word-break: break-all;
        overflow-x: auto;

        img{
          margin-right: 12px !important;
        }
        //border-bottom: 1px solid #e8e8e8;
      }
    }

    .table-info {
      padding-bottom: 20px;

      .table-class {
        padding: 0 0 0 28px;
      }
    }
  }

  /*************tabs选项卡***************/

  /deep/ .ant-tabs-nav-wrap {
    padding-left: 35px;
  }

  //下划线修改
  /deep/ .ant-tabs-ink-bar {
    width: 113px !important;
    border-bottom: 1px solid #409EFF;
  }

  /deep/ .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn,
  /deep/ .ant-tabs-tab:hover,
  /deep/ .ant-tabs-nav .ant-tabs-tab-active,
  /deep/ .ant-tabs-ink-bar {
    color: #409EFF;
  }

  /deep/ .ant-tabs-nav .ant-tabs-tab-active {
    width: 113px;
    letter-spacing: 5px;
  }

  /deep/ .ant-tabs-bar {
    border-bottom: 1px solid rgba(255, 255, 255, 0.21);
  }

  /deep/ .ant-tabs-nav .ant-tabs-tab {
    color: #fff;
    padding: 12px 42px;
    margin-right: 72px;
  }
}
</style>