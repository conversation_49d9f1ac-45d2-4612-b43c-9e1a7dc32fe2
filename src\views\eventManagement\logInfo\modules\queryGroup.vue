<template>
  <div style='display: flex;flex-flow: row nowrap'>
      <a-button-group style='margin-right: 12px'>
        <a-popover placement='bottomLeft' trigger='click' v-model='queryGroupVisible'>
          <template slot='content'>
            <!--默认显示已保存查询条件菜单-->
            <div class='main-box-content' v-if='activeMenuKey==activeMenuKeyEnum.menuUI'>
              <ul style='list-style: none;margin: 0;padding: 0'>
                <li class='li-menu-item li-menu-item-active' @click='switchInterface(2)'>
                  <a-icon type='plus-circle' class='li-menu-icon' />添加筛选
                </li>
                <!--全部禁用-->
                <li class='li-menu-item li-menu-item-active' v-if='enableStatus==enableStatusEnum.enabled||enableStatus==enableStatusEnum.both' @click='enableHandler(enableStatusEnum.disabled)'>
                  <a-icon type='eye-invisible' class='li-menu-icon' />全部禁用
                </li>
                <li class='li-menu-item li-menu-item-disabled' v-else>
                  <a-icon type='eye-invisible' class='li-menu-icon' />全部禁用
                </li>
                <!--全部启用-->
                <li class='li-menu-item li-menu-item-active' v-if='enableStatus==enableStatusEnum.disabled||enableStatus==enableStatusEnum.both' @click='enableHandler(enableStatusEnum.enabled)'>
                  <a-icon type='eye' class='li-menu-icon'/>全部启用
                </li>
                <li class='li-menu-item li-menu-item-disabled'  v-else>
                  <a-icon type='eye' class='li-menu-icon'/>全部启用
                </li>
                <!--反向包括-->
                <li class='li-menu-item li-menu-item-active' v-if='enableStatus!==enableStatusEnum.noBoth' @click='invertAllQuery'>
                  <a-icon type='swap' class='li-menu-icon' />反向包括
                </li>
                <li class='li-menu-item li-menu-item-disabled' v-else>
                  <a-icon type='swap' class='li-menu-icon' />反向包括
                </li>
                <!--全部删除-->
                <li class='li-menu-item li-menu-item-active' v-if='enableStatus!==enableStatusEnum.noBoth' @click='deleteAllQuery'>
                  <a-icon type='delete' class='li-menu-icon'/>全部删除
                </li>
                <li class='li-menu-item li-menu-item-disabled' v-else>
                  <a-icon type='delete' class='li-menu-icon'/>全部删除
                </li>

                <li class='li-menu-item li-menu-item-active li-menu-item-divider' @click='switchInterface(3)'>
                  <div>
                    <a-icon type='menu' class='li-menu-icon ' />加载已保存查询
                  </div>
                  <a-icon type='right'></a-icon>
                </li>
                <li class='li-menu-item li-menu-item-active' @click='newOrSaveGroup(groupOperationTypeEnum.addOperation)'>
                  <yq-icon type='addQueryGroup' class='li-menu-icon'></yq-icon>
                  新建
                </li>
                <li class='li-menu-item li-menu-item-active' @click='newOrSaveGroup(groupOperationTypeEnum.saveOperation)'>
                  <a-icon type='save' class='li-menu-icon' />
                  另存为
                </li>
              </ul>
            </div>
            <!--通过菜单：新增查询条件-->
            <div class='query-form-content' v-else-if='activeMenuKey==activeMenuKeyEnum.addQueryUI'>
              <query-form
              ref='queryForm1'
              :log-analyze-id='logAnalyzeId'
              :field-list='fieldList'
              :group-id='activeGroupInfo.id'
              @ok='completeAddingFilter1'
              @cancel='backQueryMenu'>
              </query-form>
            </div>
            <!--已保存查询分组列表-->
            <div class='group-list-content' v-else-if='activeMenuKey==activeMenuKeyEnum.groupListUI'>
              <query-group-list
                ref='queryGroupList'
                :log-analyze-id='logAnalyzeId'
                :group-list='groupList'
                 @selectGroup='updateActiveGroupAndList'
                @editGroup='editGroup'
                @deleteGroup='deleteGroup'>
              </query-group-list>
            </div>
            <!--新建、另存、编辑查询组界面-->
            <div class='group-operation-content' v-else-if='activeMenuKey==activeMenuKeyEnum.groupNewOrEditUI'>
              <add-query-group
                :group-operation-type='groupOperationType'
                :log-analyze-id='logAnalyzeId'
                :group-info='currEditedTermGroup'
                @ok='updateDataAndInterface'
                @cancel='cancelGroupOperation'>
              </add-query-group>
            </div>
          </template>
          <template slot='title'>
            <div class='main-box-headline' v-if='activeMenuKey==activeMenuKeyEnum.menuUI'>
              <div>
                <div class='title' v-if='activeGroupInfo.queryName'>{{ activeGroupInfo.queryName }}</div>
                <span v-else>空</span>
              </div>
              <!--打开已保存查询界面-->
              <div @click='switchInterface(activeMenuKeyEnum.groupListUI)' class='more-group'>
                <a-icon type='right'></a-icon>
              </div>
            </div>
            <span class='query-form-headline' v-else-if='activeMenuKey==activeMenuKeyEnum.addQueryUI'>添加筛选</span>
            <span class='group-list-headline' v-else-if='activeMenuKey==activeMenuKeyEnum.groupListUI'>
              <span @click='switchInterface(1)' class='back'><a-icon class='icon' type='left'/></span>加载已保存查询
            </span>
            <span class='group-operation-headline' v-else-if='activeMenuKey==activeMenuKeyEnum.groupNewOrEditUI'>
              <span @click='cancelGroupOperation(groupOperationType!=groupOperationTypeEnum.editOperation?activeMenuKeyEnum.menuUI:activeMenuKeyEnum.groupListUI)' class='back'><a-icon class='icon' type='left'/></span>{{groupOperationTitle}}
            </span>
          </template>
          <a-button title='已保存查询菜单' icon='menu'></a-button>
        </a-popover>

        <!--添加筛选弹窗-->
        <a-popover placement='bottomLeft' v-model='queryFormVisible' trigger='click'>
          <template slot='content'>
            <div style='width:640px'>
              <query-form
                ref='queryForm2'
                :log-analyze-id='logAnalyzeId'
                :field-list='fieldList'
                :group-id='activeGroupInfo.id'
                @ok='completeAddingFilter2'
                @cancel='changeQueryFormVisible'>
              </query-form>
            </div>
          </template>
          <template slot='title'>
            <span>添加筛选</span>
          </template>
          <a-button title='添加筛选' icon='plus-circle'></a-button>
        </a-popover>
      </a-button-group>

      <div style='flex: auto'>
        <a-input-search :max-length='500' placeholder='手动输入语句回车或点击搜索按钮进行查询' :allowClear='true' style='width: 100%' @blur='blurQuery' @search='searchQuery' />
      </div>
    </div>
</template>
<script>
import { getAction } from '@api/manage'
import queryForm from '@views/eventManagement/logInfo/modules/queryForm.vue'
import yqIcon from '@comp/tools/SvgIcon'
import queryGroupList from '@views/eventManagement/logInfo/modules/queryGroupList.vue'
import addQueryGroup from '@views/eventManagement/logInfo/modules/addQueryGroup.vue'
import {activeMenuKeyEnum,groupOperationTypeEnum,enableStatusEnum,opertionTips} from '@views/eventManagement/logInfo/modules/status'

export default {
  name: 'filterQuery',
  props: {
    logAnalyzeId: {
      type: String,
      required: false,
      default: ''
    },
    /**0全部禁用状态，显示全部启用按钮；1全部启用状态，显示全部禁用按钮；2部分禁用、启用状态，显示禁用和启用按钮;3查询条件为空时，禁用、启用按钮都不可操作*/
    enableStatus:{
      type: Number,
      required: false,
      default:enableStatusEnum.noBoth
    },
    fieldList:{
      type: Array,
      required: false,
      default: ()=>{return []}
    }
  },
  mixins:{},
  components: {
    queryForm,
    yqIcon,
    queryGroupList,
    addQueryGroup
  },
  data() {
    return {
      /**查询分组弹窗显隐*/
      queryGroupVisible: false,
      activeMenuKeyEnum:activeMenuKeyEnum,
      activeMenuKey: activeMenuKeyEnum.menuUI,
      /**查询分组列表*/
      groupList: [],//
      /**当前激活使用的查询分组*/
      activeGroupInfo: {},
      groupOperationTypeEnum:groupOperationTypeEnum,
      groupOperationType: groupOperationTypeEnum.noOperation,
      /**新建、另存、编辑分组界面标题*/
      groupOperationTitle:"",
      /**当前被编辑或者要另存为的分组*/
      currEditedTermGroup:null,
      enableStatusEnum:enableStatusEnum,
      /**新增查询条件弹窗显隐*/
      queryFormVisible: false,
      url: {
        queryActiveTermGroup: '/logAnalyze/queryById',//获取当前激活的查询组
        queryTermGroupList: '/logAnalyze/group/list',//获取查询组列表
        enable:'/logAnalyze/term/allEnable',//全部禁用、启用查询条件
        allInverse:'/logAnalyze/term/allInverse',//全部取反查询条件
        clearTerms:'/logAnalyze/group/clearTerms'//全部删除查询条件
      }
    }
  },
  watch: {
    logAnalyzeId: {
      handler(nval, oval) {
        this.activeGroupInfo = {}
        if (nval) {
          this.updateActiveGroupAndList()
        }
      },
      deep: true,
      immediate: true
    },
    queryGroupVisible: {
      handler(nVal, oVal) {
        this.changeGroupVisible(nVal)
      },
      immediate: true,
      deep: true
    },
    queryFormVisible: {
      handler(nVal, oVal) {
        this.changeFormVisible(nVal)
      }
    }
  },
  mounted() {},
  methods: {
    /*************针对已保存查询的操作**************/
    /**全部禁用、启用操作*/
    enableHandler(operation){
      this.$emit('enableHandler',operation)
    },
    /**全部取反所有查询条件*/
    invertAllQuery(){
      this.$emit('invertAllQuery')
    },
    /**全部删除：清空所有查询条件*/
    deleteAllQuery(){
      this.$emit('deleteAllQuery')
    },
    /**点击已保存查询菜单按钮时，显示默认主界面--菜单*/
    changeGroupVisible(visible) {
      if (visible) {
        this.switchInterface(activeMenuKeyEnum.menuUI)
      }
    },
    /**改变key，切换界面（默认界面、分组编辑新建另存为界面、分组列表界面、添加筛选界面）*/
    switchInterface(key) {
      if (!this.logAnalyzeId||this.logAnalyzeId.length===0){
        if (key===this.activeMenuKeyEnum.addQueryUI){
          this.$message.warning(opertionTips.addQuery)
        }else if(key===this.activeMenuKeyEnum.groupNewOrEditUI){
          this.$message.warning(opertionTips.newOrSaveGroup)
        }
      }
      this.activeMenuKey = key
      //console.log('this.activeMenuKey===', this.activeMenuKey)
    },
    /**添加筛选后，更新查询列表和查询标签*/
    completeAddingFilter1() {
       //this.switchInterface(1)
        this.$emit('completeAddingFilter')
        this.queryGroupVisible=false
    },
    /**针对分组的操作：0无操作，1新增，2另存，3编辑*/
    updateGroupOperationType(type){
      this.groupOperationType = type
      let temp=''
      switch (type){
        case 1:
          temp='新建'
          break;
        case 2:
          temp='另存'
          break;
        case 3:
          temp='编辑'
          break;
        default :
          temp=''
          break;
      }
      this.groupOperationTitle=temp?temp+'查询':''
    },
    /**取消针对分组的操作，返回主界面*/
    cancelGroupOperation(key){
      this.updateGroupOperationType(groupOperationTypeEnum.noOperation)
      this.currEditedTermGroup=null
      this.switchInterface(key)
    },
    /**打开查询分组的新建或者另存为界面*/
    newOrSaveGroup(type) {
      this.switchInterface(activeMenuKeyEnum.groupNewOrEditUI)
      this.updateGroupOperationType(type)
      this.currEditedTermGroup=type===groupOperationTypeEnum.saveOperation?this.activeGroupInfo:null
    },
    /**新建、另存为、编辑查询后，更新当前激活分组和分组列表,并切换界面*/
    updateDataAndInterface(groupOperationType) {
      this.updateActiveGroupAndList()
      //编辑完后，隐藏分组编辑界面，显示分组列表界面
      if (groupOperationType===groupOperationTypeEnum.editOperation) {
        this.switchInterface(activeMenuKeyEnum.groupListUI)
        this.$nextTick(() => {
          this.$nextTick(() => {
            this.$refs.queryGroupList.selectGroup(this.currEditedTermGroup)
          })
        })
      }
      //新建、另存为操作完成后，隐藏分组新建、另存为界面，显示默认主菜单界面
      else if(groupOperationType===groupOperationTypeEnum.addOperation||groupOperationType===groupOperationTypeEnum.saveOperation) {
        this.switchInterface(activeMenuKeyEnum.menuUI)
      }
      this.updateGroupOperationType(groupOperationTypeEnum.noOperation)
    },
    /**打开查询分组的编辑界面*/
    editGroup(group){
      this.updateGroupOperationType(groupOperationTypeEnum.editOperation)
      this.currEditedTermGroup=group
      this.switchInterface(activeMenuKeyEnum.groupNewOrEditUI)
    },
    /**删除查询某个分组后，更新当前激活查询和查询分组列表*/
    deleteGroup(group) {
      this.updateActiveGroupAndList()
    },
    /**更新当前激活分组和分组列表*/
    updateActiveGroupAndList(){
      this.getActiveTermGroup(this.logAnalyzeId)
      this.getTermGroupList(this.logAnalyzeId)
    },
    /**返回已保存菜单默认界面*/
    backQueryMenu() {
      //console.log('取消新增查询条件，返回菜单')
      this.$refs.queryForm1.initForm()
      this.switchInterface(activeMenuKeyEnum.menuUI)
      this.updateGroupOperationType(groupOperationTypeEnum.noOperation)
    },
    /**根据日志来源id，获取当前激活的查询分组*/
    getActiveTermGroup(logAnalyzeId) {
      if (logAnalyzeId) {
        getAction(this.url.queryActiveTermGroup, { logAnalyzeId: logAnalyzeId }).then((res) => {
          if (res.success) {
            this.activeGroupInfo = res.result.logAnalyzeQuery
            this.$emit('changeActiveGroup',this.activeGroupInfo)
            //console.log('当前激活的查询分组this.activeGroupInfo===', res.result)
          } else {
            this.$message.warning(res.message)
          }
        }).catch((err) => {
          this.$message.warning(err.message)
        })
      }
    },
    /**根据日志来源id，获取所有查询分组*/
    getTermGroupList(logAnalyzeId) {
      this.groupList = []
      getAction(this.url.queryTermGroupList, { logAnalyzeId: logAnalyzeId }).then((res) => {
        this.groupList = []
        if (res.success) {
          this.groupList = res.result
          //console.log('查询分组列表this.groupList===', this.groupList)
        } else {
          this.$message.warning(res.message)
        }
      }).catch((err) => {
        this.$message.warning(err.message)
      })
    },


    /*************添加筛选**************/
    /**添加筛选完成后，更新查询条件标签*/
    completeAddingFilter2() {
      this.changeQueryFormVisible()
      this.$emit('completeAddingFilter')
    },
    /**改变新增查询条件表单显隐*/
    changeQueryFormVisible() {
      this.queryFormVisible = false
    },
    /**查询条件汽包窗口隐藏时重置表单*/
    changeFormVisible(visible) {
      if (!visible) {
        // this.$nextTick(() => {
          this.$refs.queryForm2.initForm()
        // })
      }
    },

    /*************手动输入添加查询条件**************/
    blurQuery(e){
      this.$emit('customQuery',e.target.value)
    },
    searchQuery(value){
      this.$emit('customQuery',value)
    }
  }
}
</script>

<style scoped lang='less'>
@import '~@assets/less/common.less';
.main-box-content{
  width:260px;

  .li-menu-item {
    font-size: 14px;
    padding: 4px 0px;
    margin-bottom: 0px;
    cursor: pointer;

    .li-menu-icon {
      margin-right: 8px;
    }
  }

  .li-menu-item-active:hover{
    color: #409eff;
    cursor: pointer;
  }
  .li-menu-item-disabled{
    color: #E8E8E8;
    cursor:default;
  }
  .li-menu-item-divider {
    margin-top: 6px;
    padding-top: 6px;
    border-top: 1px solid #E8E8E8;
    display: flex;
    justify-content: space-between;
    flex-flow: row nowrap;
  }
}

.query-form-content{
  width: 640px;
}

.group-list-content{
  width: 300px;
}

.group-operation-content{
  width: 380px;
}

.main-box-headline{
  display: flex;
  justify-content: space-between;

  .more-group:hover {
    color: #409eff;
    cursor: pointer;
  }
}

.group-list-headline,.group-operation-headline{
  .back:hover {
    color: #409eff;
    cursor: pointer;
  }
  .icon{
    margin-right: 12px
  }
}
</style>