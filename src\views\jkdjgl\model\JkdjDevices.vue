<template>
  <div style='width:100%;padding:0 5px'>
    <a-table  style='width:calc(100% - 4px)' ref="table" bordered :rowKey="(record,index)=>{return record.id}" :columns="columns"
             :dataSource="dataSource" :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}" :pagination="ipagination"
             :loading="loading" @change="handleTableChange">
      <!-- 字符串超长截取省略号显示-->
      <template slot="index" slot-scope="text,record,index">
        <span>{{index+1}}</span>
      </template>
      <template slot="abutmentAlarmList" slot-scope="text,record,index">
        <span v-if='text && text.length>0' style='color:#1890ff;cursor: pointer' @click='alarmClick(record)'>{{text.length}}</span>
        <span v-else>0</span>
      </template>

    </a-table>
    <jkdj-device-alarm ref='jkdjDeviceAlarm'></jkdj-device-alarm>
  </div>

</template>

<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
import JkdjDeviceAlarm from '@views/jkdjgl/model/JkdjDeviceAlarm.vue'

export default {
  name: 'JkdjDevices',
  components: { JkdjDeviceAlarm },
  mixins: [JeecgListMixin, YqFormSearchLocation],
  props:{
    record:{
      type:Object,
      default:()=>{return {}},
      required:true,
    }
  },
  data(){
    return{
      columns: [
        {
          title: '序号',
          dataIndex: 'index',
          scopedSlots: {
            customRender: 'index'
          },
          width: 60,
          customCell: () => {
            let cellStyle = 'text-align: center'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '设备名称',
          dataIndex: 'name',
          scopedSlots: {
            customRender: 'name'
          },
          customCell: () => {
            let cellStyle = 'text-align: center'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: 'IP',
          dataIndex: 'ip',
          customCell: () => {
            let cellStyle = 'text-align: center'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '告警数量',
          dataIndex: 'abutmentAlarmList',
          scopedSlots: {
            customRender: 'abutmentAlarmList'
          },
        },
      ],
      url: {
        list: '/abutment/system/getDevice',
      },
      disableMixinCreated:true,
    }
  },
  created() {
    this.queryParam.systemId = this.record.id;
    this.loadData()
  },
  mounted() {
  },
  methods:{
    alarmClick(record){
      this.$refs.jkdjDeviceAlarm.show(record)
    }
  }
}
</script>



<style scoped lang='less'>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';

/deep/.ant-table-tbody .ant-table-row td {
  padding-top: 5px;
  padding-bottom: 5px;
}
</style>