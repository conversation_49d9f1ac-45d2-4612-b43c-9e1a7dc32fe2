<template>
  <a-date-picker
    ref='yearPicker'
    v-model="momVal"
    format="YYYY"
    mode='year'
    :disabled="disabled || readOnly"
    :placeholder="placeholder"
    :open='yearPanel'
    :disabled-date="disabledDate"
    dropdownClassName='yq-year-picker'
    @panelChange="panelChange"
    @focus='yearFocus'
  />
</template>
<script>
import moment from 'moment'
export default {
  name: 'YqYearPicker',
  props: {
    placeholder:{
      type: String,
      default: '请选择年度',
      required: false
    },
    value:{
      type: String,
      required: false
    },
    readOnly:{
      type: Boolean,
      required: false,
      default: false
    },
    disabled:{
      type: Boolean,
      required: false,
      default: false
    },
    disabledDate:{
      type:Function,
      default: (node) => false,
    },
    getCalendarContainer: {
      type: Function,
      default: (node) => node.parentNode
    }
  },
  data () {
    let timeStr = this.value;
    return {
      decorator:"",
      yearPanel:false,
      momVal:!timeStr?null:moment(timeStr,"YYYY")
    }
  },
  watch: {
    value (val) {
      if(!val){
        this.momVal = null
      }else{
        this.momVal = moment(val,"YYYY")
      }
    }
  },
  methods: {
    moment,
    yearFocus(e){
      this.yearPanel = true;
      setTimeout(()=>{
        window.addEventListener("click",this.hidePanel)
      },300)
    },
    hidePanel(e){
      let whiteClass = [
        "ant-calendar-year-panel-decade-select-content",
        "ant-calendar-year-panel-next-decade-btn",
        "ant-calendar-year-panel-prev-decade-btn"
      ]
      // console.log("className ==",e.target.parentNode.className)
      if(whiteClass.includes(e.target.classList[0])){
        return;
      }
      if(e.target.parentNode&&e.target.parentNode.className.split(" ").includes('ant-calendar-year-panel-cell-disabled') ){
        return
      }
      window.removeEventListener("click",this.hidePanel)
      this.yearPanel = false;
    },
    panelChange(e,mode){
      if(mode===null){
        this.$emit('change', moment(e).format("YYYY"));
      }
    },
  },
  //2.2新增 在组件内定义 指定父组件调用时候的传值属性和事件类型
  model: {
    prop: 'value',
    event: 'change'
  }
}
</script>
<style>
.yq-year-picker .ant-calendar-year-panel-selected-cell .ant-calendar-year-panel-year {
  background: transparent;
//color: #fff;
  color: #364d80;
}
.yq-year-picker .ant-calendar-year-panel-cell-disabled .ant-calendar-year-panel-year, .yq-year-picker .ant-calendar-year-panel-cell-disabled .ant-calendar-year-panel-year:hover {
  color: rgba(0, 0, 0, 0.25);
  background: #f5f5f5;
  cursor: not-allowed;
}
</style>
<style scoped lang='less'>

</style>