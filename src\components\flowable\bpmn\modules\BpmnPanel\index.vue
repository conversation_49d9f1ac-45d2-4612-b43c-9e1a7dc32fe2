<template>
  <div ref="propertyPanel" class="property-panel">
    <div class="node-name">{{ showNodeName }}</div>
    <component
      ref="_component"
      :is="getComponent"
      v-if="node"
      :element="node"
      :modeler="modeler"
      :processCategory="processCategory"
      :businessList="businessList"
      :taskCategory="taskCategory"
      :processInfo="processInfo"
      @setProcessInfo="setProcessInfo"
    />
  </div>
</template>

<script>
import bpmnProcess from './BpmnProcess'
import bpmnNode from './BpmnNode'
import { NodeName } from '../../translate/lang/zh'
export default {
  name: 'BpmnPanel',
  components: { bpmnProcess, bpmnNode },
  props: {
    processCategory: {
      type: Array,
      required: true,
    },
    businessList: {
      type: Array,
      required: true,
    },
    taskCategory: {
      type: Array,
      required: true,
    },
    modeler: {
      type: Object,
      required: true,
    },
    processInfo: {
      type: Object,
      default: () => {
        return {}
      },
    },
  },
  data() {
    return {
      node: null,
    }
  },
  computed: {
    getComponent() {
      if (this.node) {
        let type = this.node.type
        if (type === 'bpmn:Process') {
          return 'bpmnProcess'
        } else {
          return 'bpmnNode'
        }
      }
      return null
    },
    showNodeName() {
      if (this.node) {
        const bizObj = this.node.businessObject
        let test = bizObj?.eventDefinitions
        if (bizObj) {
          const type = bizObj.eventDefinitions ? bizObj.eventDefinitions[0].$type : bizObj.$type
          return NodeName[type] || type
        }
      }
      return ''
    },
  },
  created() {},
  mounted() {
    this.handleModeler()
  },
  methods: {
    setProcessInfo(key,val){
       this.$emit("setProcessInfo",key,val)
    },
    handleModeler() {
      this.modeler.on('root.added', (e) => {
        // console.log('监听到 added', e)
        if (e.element.type === 'bpmn:Process') {
          // this.node = null
          this.node = e.element
          // this.$nextTick().then(() => {

          // })
        }
      })
      this.modeler.on('element.click', (e) => {
        // console.log('监听到 element.click', e)
        const { element } = e
        // console.log(element)
        if (element.type === 'bpmn:Process') {
          this.node = element
        }
      })
      // this.modeler.on('element.changed', (e) => {
      //    const { element} = e
      //   console.log('监听到 element.changed == ',element)
      //   // // console.log(element)
      //   // if (element.type === 'bpmn:Process') {
      //   //   this.node = element
      //   // }
      // })
      this.modeler.on('selection.changed', (e) => {
        // hack 同类型面板不刷新
        const element = e.newSelection[0]
        //  console.log('监听到 selection.changed', element)
        if (element) {
          if (this.node && this.node.id === element.id) return
          this.node = null
          this.$nextTick().then(() => {
            this.node = element
          })
        } else {
          this.node = null
        }
      })
    },
    validate() {
      if (this.$refs._component) {
        return this.$refs._component.validate()
      }
    },
  },
}
</script>

<style scoped lang="less">
.property-panel {
  height: 100%;
  border-left: 1px solid #d9d9d9;
  background: #fafafa;
  .node-name {
    font-size: 16px;
    font-weight: 550;
    padding: 12px 16px;
    border-bottom: 1px solid #d9d9d9;
  }
}
</style>