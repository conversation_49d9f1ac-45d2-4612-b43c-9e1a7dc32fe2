<template>
  <!-- 内存使用率 -->
  <div style="height: 100%;width: 100%;">
    <recent-time @changeType="changeType" :selectedIndex="selectedIndex"></recent-time>
    <div ref="baseEcharts" style="height: 100%;width: 100%;"></div>
  </div>
</template>


<script>
import recentTime from './recentTime.vue'
import { chartMixins } from './chartMixins'
export default {
  name: 'memUsageRate',
  mixins: [chartMixins],
  components: {
    recentTime
  },
  props: {
    chartData: {
      type: Object,
      default: () => {}
    },
    fontSizeObject: {
      type: Object,
      default: function () {
        return {
          legendFontSize: 8,
          xAxisFontSize: 8,
          yAxisFontSize: 10
        }
      }
    }
  },
  watch: {
    chartData: {
      handler(nVal, oVal) {
        this.$nextTick(() => {
          this.initData(nVal)
        })
      },
      deep: true,
      immediate: true
    }
  },
  data() {
    return {
      myChart: null,
      selectedIndex: 0
    }
  },
  methods: {
    changeType(index) {
      this.selectedIndex = index
      this.initData()
    },
    initData() {
      let xData = []
      let yData = []
      let xAllData = []
      let yAllData = [] // 30日内存使用量数据
      let lineData = []
      let lineAllData = [] //折线图数据
      let unit = '' // 获取内存使用量的单位
      if (this.chartData.memUsed && this.chartData.memUsed.length > 0) {
        // 内存使用量
        xAllData = this.chartData.memUsed.map(item => item.time) //  处理x轴数据
        yAllData = this.chartData.memUsed.map(item => item.value) // 处理y轴数据
        let result = this.chartData.memUsed.find(item=>item.value.unit)
        if (result && result.value && result.value.value) {
          unit = result.value.unit
        }
      }
      if (this.chartData.memRate && this.chartData.memRate.length > 0) {
        // 内存使用率, 处理折线图数据
        xAllData = this.chartData.memRate.map(item => item.time) // 处理x轴数据
        lineAllData = this.chartData.memRate.map(item => item.value) // 处理y轴数据
      }

      if (this.selectedIndex == 0 && xAllData.length > 6) {
        // 截取近七日数据
        xData = xAllData.slice(xAllData.length - 7, xAllData.length)
        yData = yAllData.slice(xAllData.length - 7, xAllData.length)
        lineData = lineAllData.slice(xAllData.length - 7, xAllData.length)
      } else {
        // 全部数据
        xData = xAllData
        yData = yAllData
        lineData = lineAllData
      }
      this.myChart = this.$echarts.init(this.$refs.baseEcharts)
      this.myChart.clear()
      let option = {
        color: ['rgba(91,143,249,0.85)', '#73DEB3'],
        grid: {
          top: '18%',
          left: '6%',
          right: '7%',
          bottom: 4,
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            let html = `${params[0].name}<br/>`
            params.map((item, i) => {
              if (item.value !== '' && item.value !== null && item.value !== undefined) {
                html += `${item.marker}${item.seriesName}：${item.value} ${item.data.unit || ''} <br/>`
              } else {
                html += `${item.marker}${item.seriesName}：无数据<br/>`
              }
            })

            return html
          }
        },
        legend: {
          top: '2%',
          left: '7%',
          itemWidth: 7,
          itemHeight: 7,
          textStyle: {
            fontSize: this.fontSizeObject.legendFontSize
          }
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: true,
            axisLine: {
              show: true,
              lineStyle: {
                color: 'rgba(0,0,0,0.15)'
              }
            },
            axisLabel: {
              textStyle: {
                color: 'rgba(0,0,0,0.45)',
                fontSize: this.fontSizeObject.xAxisFontSize
              }
            },
            axisTick: {
              show: false
            },
            data: xData
          }
        ],
        yAxis: [
          {
            type: 'value',
            axisTick: {
              show: false
            },
            splitLine: {
              show: true,
              lineStyle: {
                type: 'dashed',
                color: 'rgba(0,0,0,0.15)',
                width: 1
              }
            },
            axisLine: {
              show: false
            },
            axisLabel: {
              formatter: '{value}%',
              textStyle: {
                color: 'rgba(0,0,0,0.45)',
                fontSize: this.fontSizeObject.yAxisFontSize
              }
            }
          },
          {
            type: 'value',
            axisTick: {
              show: false
            },
            position: 'right',
            splitLine: {
              show: false,
              lineStyle: {
                type: 'dashed',
                color: 'rgba(0,0,0,0.15)',
                width: 1
              }
            },
            axisLine: {
              show: false
            },
            axisLabel: {
              formatter: '{value}' + unit,
              textStyle: {
                color: 'rgba(0,0,0,0.45)',
                fontSize: this.fontSizeObject.yAxisFontSize
              }
            }
          }
        ],
        dataZoom: [
          {
            xAxisIndex: [0],
            show: false, //是否显示滑动条，不影响使用
            start: 0, // 从头开始。
            endValue: 30,
            realtime: true, //是否实时更新
          },
          {
            type: 'inside',
            xAxisIndex: 0,
            zoomOnMouseWheel: true, //滚轮是否触发缩放
            moveOnMouseMove: true, //鼠标滚轮触发滚动
            moveOnMouseWheel: true
          }
        ],
        series: [
          {
            type: 'line',
            name: '内存使用率',
            data: lineData,
            symbol: 'circle',
            showAllSymbol: true,
            emphasis: {
              focus: 'series' //高亮显示
            },
            symbolSize: 4,
            lineStyle: {
              color: '#73DEB3'
            },
            itemStyle: {
              // 折线拐点标志的样式
              normal: {
                color: '#73DEB3',
                // borderColor: 'rgba(255, 234, 0, 0.5)',
                borderWidth: 4
              }
            }
          },
          {
            type: 'bar',
            name: '内存使用量',
            barGap: 0.2,
            barWidth: this.selectedIndex == 1 ? 9: 12,
            yAxisIndex: 1,
            label: {
              show: false,
              position: 'inside',
              color: 'rgba(0,0,0,0.45)'
            },
            data: yData
          }
        ]
      }

      this.myChart.setOption(option)
    }
  }
}
</script>