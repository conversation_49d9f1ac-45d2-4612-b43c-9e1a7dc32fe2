<template>
  <a-modal
    title="新增报告"
    :width="width"
    :visible="visible"
    :centered="true"
    :confirmLoading="confirmLoading"
    switchFullscreen
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭"
  >
    <a-form :form="form">
      <a-row>
        <a-col :span="24">
          <a-form-item label="报告名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input
              v-decorator="['name', validatorRules.name]"
              autocomplete="off"
              :allowClear="true"
              placeholder="请输入报告名称"
            ></a-input>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="时间范围" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-range-picker
              format="YYYY-MM-DD"
              v-decorator="['time', validatorRules.time]"
              @change="rangeChange"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <!--           -->
          <a-form-item label="选择项目" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <j-search-select-tag
              :trigger-change="true"
              v-decorator="['projectId', validatorRules.projectId]"
              placeholder="请选择项目"
              :dictOptions="searchOptions"
              @change="onChange"
            >
            </j-search-select-tag>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="选择供应商" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-select
              show-search
              placeholder="选择供应商"
              option-filter-prop="children"
              style="width: 100%"
              :filter-option="filterOption"
              v-decorator="['operSupplierId' , validatorRules.supplier]"
            >
              <a-select-option v-for="item in operSuppliers" :key="item.value">
                {{ item.text }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="备注信息" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-textarea v-decorator="['remarks']" rows="4" placeholder="请输入备注信息" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>

<script>
import { httpAction, getAction, postAction } from '@/api/manage'
import JSearchSelectTag from '@/components/dict/JSearchSelectTag'
import { ajaxGetDictItems, getDictItemsFromCache } from '@/api/api'
import pick from 'lodash.pick'
export default {
  name: 'serviceAdd',
  components: { JSearchSelectTag },
  data() {
    return {
      form: this.$form.createForm(this),
      title: '',
      width: '800px',
      visible: false,
      model: {},
      confirmLoading: false,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      validatorRules: {
        name: {
          rules: [{ required: true, message: '请输入报告名称!' },{max:30,message: '报告名称长度不可超过30个字符！' }],
        },
        time: {
          rules: [{ required: true, message: '请选择时间范围!' }],
        },
        projectId: {
          rules: [{ required: true, message: '请选择项目!' }],
        },
        supplier: {
          rules: [{ required: true, message: '请选择供应商!' }],
        },
      },
      url: {
        add: '/report/itilReportInfo/add',
        getDepList: '/itilconfigitemlibrary/itilConfigItemLibrary/getDepartByTargetId',
      },
      startTimes: '',
      endTimes: '',
      projectId: '',
      operSupplierId: '',
      searchOptions: [],
      operSuppliers: [],
    }
  },
  computed: {
    //可行性测试，根据文件路径动态加载组件
    LcDict: function () {
      var myComponent = () => import(`@/components/dict/JDictSelectTag`)
      return myComponent
    },
  },
  methods: {
    onChange(value) {
      this.operSuppliers = []
      //加载审批部门
      getAction(this.url.getDepList, { targetId: value }).then((res) => {
        if (res.success) {
          this.operSuppliers = res.result
        }
      })
    },
    //
    filterOption(input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
    },
    add() {
      this.edit({})
      //根据字典Code, 初始化字典数组
      ajaxGetDictItems('itil_sy_project,project_name,id', null).then((res) => {
        if (res.success) {
          this.searchOptions = res.result
        }
      })
    },
    edit(record) {
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(
          pick(
            this.model,
            'buttonPer',
            'createBy',
            'createText',
            'createTime',
            'delflag',
            'depType',
            'endTime',
            'fileId',
            'id',
            'name',
            'operSupplierId',
            'operSupplierText',
            'projectId',
            'projectText',
            'remarks',
            'startTime',
            'status',
            'statusText',
            'sysOrgCode',
            'updateBy',
            'updateTime',
            'whAssess',
            'whButtonCheck',
            'whCheck'
          )
        )
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let formData = Object.assign(this.model, values)
          let params = {
            name: formData.name,
            startTime: this.startTimes,
            endTime: this.endTimes,
            projectId: formData.projectId,
            operSupplierId: formData.operSupplierId,
            remarks: formData.remarks,
          }

          httpAction(httpurl, params, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.visible = false
              that.confirmLoading = false
            })
        }
      })
    },
    handleCancel() {
      this.close()
    },
    //日期
    rangeChange(dates, dateStrings) {
      this.startTimes = dateStrings[0]
      this.endTimes = dateStrings[1]
    },
  },
}
</script>
<style lang="less" scoped>
::v-deep .ant-modal-body {
  padding: 24px 48px 24px 48px;
}
::v-deep .ant-modal {
  padding: 24px;
}
@media (max-width: 812px) {
  ::v-deep .ant-modal {
    max-width: calc(100vw - 12px);
    margin: 0;
  }
}
</style>