<template>
  <a-form :form="form" class="user-info-form">
    <!-- 用户账号 -->
    <a-form-item label="用户账号" :labelCol="labelCol" :wrapperCol="wrapperCol">
      <a-input
        size="large"
        placeholder="请输入用户账号"
        v-decorator.trim="['username', validatorRules.username]"
        :readOnly="!!model.id"
        :allowClear="true"
        autocomplete="off"
      />
    </a-form-item>
    <!-- 用户姓名 -->
    <a-form-item label="用户姓名" :labelCol="labelCol" :wrapperCol="wrapperCol">
      <a-input
        size="large"
        placeholder="请输入用户姓名"
        v-decorator.trim="['realname', validatorRules.realname]"
        :allowClear="true"
        autocomplete="off"
      />
    </a-form-item>
    <!-- 联系方式 -->
    <a-form-item label="联系方式" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="phone" style="z-index: 1">
      <a-input
        size="large"
        placeholder="请输入联系方式"
        v-decorator="['phone', validatorRules.phone]"
        autocomplete="off"
        :allowClear="true"
      />
    </a-form-item>
    <!-- 单位 -->
    <a-form-model-item :label="'部\u3000门'" prop="selecteddeparts" :labelCol="labelCol" :wrapperCol="wrapperCol">
      <a-tree-select
        size="large"
        :getPopupContainer="(node) => node.parentNode"
        tree-node-filter-prop="title"
        v-decorator.trim="['selecteddeparts', validatorRules.selecteddeparts]"
        :replaceFields="replaceFields"
        :treeData="departList"
        show-search
        style="width: 100%"
        :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
        placeholder="请选择您所在的单位"
        allow-clear
        multiple
        @change="onChangeTree"
        @select="onSelect"
      >
      </a-tree-select>
    </a-form-model-item>
    <!-- 头像 -->
    <a-form-item class="two-words" :label="'头\u3000像'" :labelCol="labelCol" :wrapperCol="wrapperCol">
      <j-image-upload bizPath="image/usesrIcon" class="avatar-uploader" text="上传" v-model="fileList"></j-image-upload>
    </a-form-item>
    <a-form-item class="formBtn">
      <div class="btn-box">
        <span>
          <a-button :disabled="confirmLoading" class="save-btn" @click="save()">保存</a-button>
          <a-button class="cancel-btn" @click="cancelEdit">取消 </a-button>
        </span>
      </div>
    </a-form-item>
  </a-form>
</template>

<script>
import Vue from 'vue'
import { getAction, postAction, putAction } from '@/api/manage'
import { queryall } from '@/api/api'
import JImageUpload from '@/components/jeecg/JImageUpload'
import { phoneValidator } from '@/mixins/phoneValidator'
import { mapActions, mapGetters, mapState } from 'vuex'
import { getQueryStringRegExp } from '@/utils/util'
import { isArray } from '@antv/x6/lib/util/lang/lang'
import { USER_INFO, USER_DEPART } from '@/store/mutation-types'
import { duplicateCheck } from '@/api/api'
export default {
  name: 'oneClickHelpInfoEdit',
  components: {
    JImageUpload,
  },
  mixins: [phoneValidator],
  props: {
    departList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      validatorRules: {
        username: {
          rules: [
            {
              required: true,
              message: '请输入用户账号!',
            },
            {
              pattern: /^[a-zA-Z0-9_]+$/,
              message: '用户账号仅包含英文数字下划线',
            },
            { min: 2, max: 20, message: '用户账号在2-20个字符之间' },
          ],
        },
        realname: {
          rules: [
            { required: true, message: '请输入用户姓名!' },
            { min: 2, max: 20, message: '用户姓名在2-20位字符之间' },
          ],
        },
        selecteddeparts: {
          rules: [{ required: true, message: '请选择单位' }],
        },
        phone: {
          rules: [
            {
              required: true,
              message: '请输入手机号！',
            },
            { validator: this.phone },
            // {
            //   validator: this.validatePhone,
            // },
          ],
        },
      },
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 19 },
      },
      confirmLoading: false,
      form: this.$form.createForm(this),
      fileList: [],
      replaceFields: {
        children: 'children',
        title: 'title',
        key: 'id',
        value: 'id',
      },
      pwdRuleUrl: 'umpPwdManage/umpPwdManage/list',
      hostName: '',
      model: {},
      url: {
        getInfo: '/sys/user/getInfo', //个人信息
        updateUserInfo: '/sys/user/updateUserInfo', //编辑个人信息
      },
      flatDeparts: [],
    }
  },
  mounted() {
    this.getInfo()
    this.getFlatDeparts(this.departList)
  },

  methods: {
    ...mapGetters(['nickname', 'avatar', 'userInfo', 'departs']),
    getInfo() {
      getAction('/sys/user/getInfo').then(res=>{
        if(res.success){
          let infos = this.userInfo();
          let userInfo =  Object.assign(infos, res.result);
          let departs = this.departs()
          let departIds = []
          if (departs && departs.length > 0) {
            departIds = departs.map((el) => el.id)
          }
          console.log('用户的信息学 == ', departIds, departs)

          let data = {
            username: userInfo.username,
            realname: userInfo.realname,
            phone: userInfo.phone,
            selecteddeparts: departIds,
          }
          this.form.setFieldsValue(data)
          //JImageUpload 的绑定值是图片地址数组
          this.fileList = [userInfo.avatar]
          this.model = userInfo
          queryall({ username: userInfo.username }).then((res) => {
            if (res.success) {
              let tem = []
              let codes = userInfo.roleCodes.split(',')
              tem = res.result.filter((el) => codes.includes(el.roleCode)).map((el) => el.id)
              this.model.selectedroles = tem.join()
            }
          })
        }
      })

    },
    save() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let formData = Object.assign(this.model, values)
          let selecteddeparts = formData.selecteddeparts || []
          formData.selecteddeparts = selecteddeparts.join()
          formData.avatar = Array.isArray(that.fileList) ? that.fileList.join() : that.fileList
          // console.log('执行修改 === ', formData)
          if (formData.selectedroles === '' || formData.selectedroles === undefined) {
            that.$message.error('用户角色信息丢失无法修改个人信息')
            return
          }
          putAction('/sys/user/edit', formData)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                this.$ls.set(USER_INFO, formData, 7 * 24 * 60 * 60 * 1000)

                let departArr = this.flatDeparts.filter((el) => selecteddeparts.includes(el.id))
                this.$ls.set(USER_DEPART, departArr, 7 * 24 * 60 * 60 * 1000)
                this.$store.commit('SET_INFO', formData)
                this.$store.commit('SET_DEPART', departArr)
                this.$store.commit('USER_INFO_CHANGE', true)
                
                this.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .catch((err) => {
              that.$message.warning('请检查接口')
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },

    onChangeTree(value) {},
    onSelect() {
      getAction('/sys/sysDepart/queryById', {
        id: arguments[0],
      }).then((res) => {
        if (res.success) {
          this.model.addrId = res.result.cityId
          this.model.deptAddress = res.result.address
        }
      })
    },
    cancelEdit() {
      this.$emit('cancelEdit')
    },
    validatePhone(rule, value, callback) {
      if (value === this.userInfo().phone) {
        callback()
        return
      }
      var params = {
        tableName: 'sys_users',
        fieldName: 'phone',
        fieldVal: value,
        dataId: this.userId,
      }
      duplicateCheck(params).then((res) => {
        if (res.success) {
          callback()
        } else {
          callback('手机号已存在!')
        }
      })
    },
    getFlatDeparts(list) {
      list.forEach((el) => {
        this.flatDeparts.push(el)
        if (el.children && el.children.length > 0) {
          this.getFlatDeparts(el.children)
        }
      })
    },
  },
}
</script>
<style lang="less" scoped>
@import '~@assets/less/onclickStyle.less';
.user-info-form {
  width: 60%;
  max-width: 650px;
}
.btn-box {
  display: flex;
  width: 100%;
  justify-content: center;
}
.save-btn {
  background: #409eff;
  border-radius: 4px;
  border-color: #409eff;
  color: #fff;
  margin-right: 24px;
}
.cancel-btn {
  // background: #144e90;
  border-radius: 4px;
  // border: none;
  color: #fff;
}
.save-btn:hover {
  background: #007dff;
  border-color: #007dff;
}
.cancel-btn:hover {
  border-color: #007dff;
}
// /deep/ .ant-input {
//   background-color: rgba(255, 255, 255, 0);
//   color: rgba(255, 255, 255, 0.7);
//   border: 1px solid #144e90;
// }
// /deep/ .ant-select-selection {
//   border: 1px solid #144e90;
// }
// /deep/ .ant-select-selection:hover {
//   border-color: #144e90;
// }
// /deep/ .ant-select-dropdown {
//   background-color: rgba(0, 0, 0, 0.6);
//   color: #fff;
// }
// /deep/ .ant-select-tree li .ant-select-tree-node-content-wrapper.ant-select-tree-node-selected {
//   background-color: transparent;
//   color: #66ffff;
//   background-image: linear-gradient(
//     90deg,
//     rgba(21, 85, 175, 0.6) 2%,
//     rgba(21, 85, 175, 0.26) 43%,
//     rgba(21, 85, 175, 0) 100%
//   );
// }
// /deep/ .ant-select-tree li .ant-select-tree-node-content-wrapper:hover {
//   background-color: transparent;
//   color: #66ffff;
//   background-image: linear-gradient(
//     90deg,
//     rgba(21, 85, 175, 0.6) 2%,
//     rgba(21, 85, 175, 0.26) 43%,
//     rgba(21, 85, 175, 0) 100%
//   );
// }
// /deep/ .ant-select-tree {
//   color: #fff;
// }
// /deep/ .ant-select-selection__clear {
//   background-color: transparent;
// }
// /deep/ .ant-select-tree li .ant-select-tree-node-content-wrapper {
//   color: #fff;
// }
// /deep/.ant-select-tree-dropdown .ant-select-dropdown-search {
//   background-color: #030b2b;
// }
/deep/ .ant-select-tree-dropdown .ant-select-dropdown-search .ant-select-search__field {
  background-color: #030b2b;
  border-color: #144e90;
}
/deep/ .ant-upload.ant-upload-select-picture-card {
  background-color: transparent;
}
// /deep/ .anticon {
//   color: #144e90;
// }
// /deep/ .ant-upload.ant-upload-select-picture-card {
//   border-color: #144e90;
// }
</style>