<template>
  <div class="main">
    <a-form
      v-if="authorizedOrNot === true && [0, 2, false].includes(UKey) && isSsoAccessToken === false"
      :form="form"
      class="user-layout-login"
      ref="formLogin"
      id="formLogin"
    >
      <a-form-item class="formBox">
        <a-input
          size="large"
          v-decorator="['username', validatorRules.username, { validator: this.handleUsernameOrEmail }]"
          type="text"
          autocomplete="off"
          disableautocomplete
          placeholder="请输入您的登录账号"
        >
        </a-input>
      </a-form-item>
      <a-form-item class="formBox">
        <a-input
          v-decorator="['password', validatorRules.password]"
          size="large"
          type="password"
          autocomplete="off"
          disableautocomplete
          placeholder="请输入您的登录密码"
        >
        </a-input>
      </a-form-item>
      <a-row :gutter="0">
        <a-col :span="24">
          <a-form-item style="position: relative" class="formBox">
            <a-input
              v-decorator="['inputCode', validatorRules.inputCode]"
              size="large"
              type="text"
              @change="inputCodeChange"
              placeholder="请输入验证码"
              autoComplete="off"
            >
            </a-input>
          </a-form-item>
          <div class="codeStyle">
            <img
              v-if="requestCodeSuccess"
              style="margin-top: 2px"
              :src="randCodeImage"
              @click="handleChangeCheckCode"
            />
            <img v-else style="width: 100%" src="../../assets/checkcode.png" @click="handleChangeCheckCode" />
          </div>
        </a-col>
      </a-row>
      <!--        </a-tab-pane>-->
      <!--        <a-tab-pane key="tab2" tab="手机号登录">-->
      <!--          <a-form-item>-->
      <!--            <a-input-->
      <!--              v-decorator="['mobile',validatorRules.mobile]"-->
      <!--              size="large"-->
      <!--              type="text"-->
      <!--              placeholder="手机号">-->
      <!--              <a-icon slot="prefix" type="mobile" :style="{ color: 'rgba(0,0,0,.25)' }"/>-->
      <!--            </a-input>-->
      <!--          </a-form-item>-->
      <!--          <a-row :gutter="16">-->
      <!--            <a-col class="gutter-row" :span="16">-->
      <!--              <a-form-item>-->
      <!--                <a-input-->
      <!--                  v-decorator="['captcha',validatorRules.captcha]"-->
      <!--                  size="large"-->
      <!--                  type="text"-->
      <!--                  placeholder="请输入验证码">-->
      <!--                  <a-icon slot="prefix" type="mail" :style="{ color: 'rgba(0,0,0,.25)' }"/>-->
      <!--                </a-input>-->
      <!--              </a-form-item>-->
      <!--            </a-col>-->
      <!--            <a-col class="gutter-row" :span="8">-->
      <!--              <a-button-->
      <!--                class="getCaptcha"-->
      <!--                tabindex="-1"-->
      <!--                :disabled="state.smsSendBtn"-->
      <!--                @click.stop.prevent="getCaptcha"-->
      <!--                v-text="!state.smsSendBtn && '获取验证码' || (state.time+' s')"></a-button>-->
      <!--            </a-col>-->
      <!--          </a-row>-->
      <!--        </a-tab-pane>-->
      <!--      </a-tabs>-->
      <!--      <a-form-item>-->
      <!--        <a-checkbox v-decorator="['rememberMe', {initialValue: true, valuePropName: 'checked'}]" >自动登录</a-checkbox>-->
      <!--        <router-link :to="{ name: 'alteration'}" class="forge-password" style="float: right;">-->
      <!--          忘记密码-->
      <!--        </router-link>-->
      <!--       <router-link :to="{ name: 'register'}" class="forge-password" style="float: right;margin-right: 10px" >-->
      <!--          注册账户-->
      <!--        </router-link>-->
      <!--      </a-form-item>-->
      <a-form-item v-if="UKey === 2">
        <div class="ukey-tip"><a-icon type="info-circle" /> 插入UKey自动登录</div>
      </a-form-item>
      <a-form-item style="text-align: center" class="formBtn">
        <a-button
          size="large"
          type="primary"
          htmlType="submit"
          class="login-button"
          :loading="loginLoading"
          @click.stop.prevent="certificate"
          :disabled="loginDisabled"
          >登&nbsp;&nbsp;录
        </a-button>
      </a-form-item>
    </a-form>
    <div v-else-if="authorizedOrNot === true && isSsoAccessToken" class="authorization">
      <div class="authorization-upload" style="font-size: 24px">
        <a> {{ doAuthorization?"授权成功，请重新进入！":"登录失败，请稍候再试！" }} </a>
      </div>
    </div>
    <div v-else-if="authorizedOrNot === true && UKey === 1" class="authorization">
      <div class="authorization-upload" style="font-size: 24px">
        <a> 未检测到UKEY信息 </a>
      </div>
    </div>
    <div v-if="authorizedOrNot === false" class="authorization">
      <div class="authorization-upload">
        <a-upload name="file" :multiple="true" :action="uploadUrl" @change="handleChange">
          <a> 未授权或者授权过期（点击此处上传授权证书）222222 </a>
        </a-upload>
      </div>
      <div class="authorization-button">
        <a-button
          size="large"
          type="primary"
          htmlType="submit"
          class="shouquan-button"
          :disabled="fileList.length != 1"
          @click.stop.prevent="authorizationButton"
          >授&nbsp;&nbsp;权
        </a-button>
      </div>
    </div>
    <two-step-captcha
      v-if="requiredTwoStepCaptcha"
      :visible="stepCaptchaVisible"
      @success="stepCaptchaSuccess"
      @cancel="stepCaptchaCancel"
    ></two-step-captcha>
    <login-select-tenant ref="loginSelect" @success="loginSelectOk"></login-select-tenant>
  </div>
</template>

<script>
//import md5 from "md5"
import api from '@/api'
import TwoStepCaptcha from '@/components/tools/TwoStepCaptcha'
import { mapActions } from 'vuex'
import { timeFix } from '@/utils/util'
import Vue from 'vue'
import { ACCESS_TOKEN, PLATFORM_TYPE, ONE_PLATFORM_FLAG, ENCRYPTED_STRING } from '@/store/mutation-types'
import { putAction, postAction, getAction } from '@/api/manage'
import { encryption, getEncryptedString } from '@/utils/encryption/aesEncrypt'
import { phoneValidator } from '@/mixins/phoneValidator'
import router from '@/router'
import store from '@/store'
import ThirdLogin from './third/ThirdLogin'
import LoginSelectTenant from './LoginSelectTenant'
import { generateIndexRouter, generateBigscreenRouter } from '@/utils/util'
export default {
  components: {
    LoginSelectTenant,
    TwoStepCaptcha,
    ThirdLogin,
  },
  mixins: [phoneValidator],
  data() {
    return {
      customActiveKey: 'tab1',
      loginLoading: false, //登录加载中
      loginDisabled: false, //登录按钮是否可操作
      // login type: 0 email, 1 username, 2 telephone
      loginType: 0,
      requiredTwoStepCaptcha: false,
      stepCaptchaVisible: false,
      form: this.$form.createForm(this),
      encryptedString: {
        key: '',
        iv: '',
      },
      state: {
        time: 60,
        smsSendBtn: false,
      },
      validatorRules: {
        username: {
          rules: [{ required: true, message: '请输入登录账号!' },{pattern:/^\S+$/,message:"账号不对！"}, { validator: this.handleUsernameOrEmail }],
        },
        password: { rules: [{ required: true, message: '请输入登录密码!', validator: 'click' }] },
        mobile: { rules: [{ validator: this.mobilePhone }] },
        captcha: { rule: [{ required: true, message: '请输入验证码!' }] },
        inputCode: { rules: [{ required: true, message: '请输入验证码!' }] },
      },
      fileList: [],
      key: '',
      verifiedCode: '',
      inputCodeContent: '',
      inputCodeNull: true,
      currentUsername: '',
      currdatetime: '',
      randCodeImage: '',
      requestCodeSuccess: false,
      authorizedOrNot: true,
      authorization: 'authorization-text',
      uploadUrl: `${window._CONFIG['domianURL']}/license/importLicense`,
      UKey: window.config.UKey,
      isSsoAccessToken:false,
      doAuthorization: false,//sso 下是否执行授权操作了
    }
  },
  created() {
    //this.currdatetime = new Date().getTime()
    Vue.ls.remove(ACCESS_TOKEN)
    this.getRouterData()
    this.handleChangeCheckCode()
    this.isSsoAccessToken = sessionStorage.getItem("sso_access_token")?true:false;
    // update-begin- --- author:scott ------ date:20190805 ---- for:密码加密逻辑暂时注释掉，有点问题
    //this.getEncrypte();
    // update-end- --- author:scott ------ date:20190805 ---- for:密码加密逻辑暂时注释掉，有点问题
  },
  methods: {
    ...mapActions(['Login', 'UkeyLogin', 'Logout', 'PhoneLogin']),

    handleChange(info) {
      if (info.file.status !== 'uploading') {
        if (info.fileList.length > 1) {
          info.fileList.shift()
        }
      }
      if (info.file.response) {
        if (info.file.response.status === 200) {
          if (info.file.status === 'done') {
            this.$message.success(`${info.file.name} 文件上传成功`)
          } else if (info.file.status === 'removed') {
            this.$message.success(`${info.file.name} 文件删除成功!`)
          }
        } else {
          this.$message.error(`${info.file.name} 文件上传失败`)
          info.fileList.shift()
        }
      }
      this.fileList = info.fileList
    },

    authorizationButton() {
      getAction('license/installLicense').then((res) => {
        if (res.status == 200) {
          this.$message.success(res.message, '请登录')
          this.authorizedOrNot = true
          this.doAuthorization = true;
        } else {
          this.$message.error(res.message)
        }
      })


    },
    // handler
    handleUsernameOrEmail(rule, value, callback) {
      const regex = /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+((\.[a-zA-Z0-9_-]{2,3}){1,2})$/
      if (regex.test(value)) {
        this.loginType = 0
      } else {
        this.loginType = 1
      }
      callback()
    },
    handleTabClick(key) {
      this.customActiveKey = key
    },
    /*登录*/
    certificate() {

          this.handleSubmit()

    },
    Ukey() {
      let that = this
      var ws = new WebSocket('ws://127.0.0.1:30318/') //访问url

      //打开连接
      ws.onopen = function () {
        //获取随机数
        postAction('/sys/LoginRandom').then((res) => {
          if (res.success) {
            this.key = res.result
            var str =
              '<?xml version="1.0" encoding="UTF-8"?><getsignandtokenreq version="1"><challenge>' +
              res.result +
              '</challenge></getsignandtokenreq>'
            //str 中的chanllenge数据需要填写应用调用1.1接口获取到的随机数，验证票据时应使用一致的随机数
            ws.send(str)
          } else {
            // that.$message.error(res.message)
            this.authorizedOrNot = false
            return that.$message.error(res.message)
          }
        })
      }
      //处理响应消息
      ws.onmessage = function (evt) {
        var received_msg = evt.data
        //received_msg为单点登录接口返回的票据数据xml格式，应用根据格式解析。
        //字符串转xml文件
        function createXml(received_msg) {
          if (document.all) {
            var xmlDom = new ActiveXObject('Microsoft.XMLDOM')
            xmlDom.loadXML(received_msg)
            return xmlDom
          } else {
            return new DOMParser().parseFromString(received_msg, 'text/xml')
          }
        }
        var domxml = createXml(received_msg)
        //获取标签值 判断返回结果值1:失败; 0:成功
        var tag = domxml.getElementsByTagName('result')
        var result = tag[0].firstChild.nodeValue
        if (1 === result) {
          var err = domxml.getElementsByTagName('errorinfo')
          var errorinfo = err[0].firstChild.nodeValue
          return that.$message.error(errorinfo)
        }
        var tag1 = domxml.getElementsByTagName('tokeninfo')
        var tokeninfo = tag1[0].firstChild.nodeValue
        //票据验证
        var s =
          '<?xml version="1.0" encoding="UTF-8"?><verifyidentityticketreq version="1"><challenge>' +
          this.key +
          '</challenge><identityticket>' +
          tokeninfo +
          '</identityticket><appserverid>app server id</appserverid></verifyidentityticketreq>'
        //校验接口
        // let that = this
        //证书
        // postAction('/sys/UkeyLogin', { xmlString: s }).then((res) => {
        //   if (res.success) {
        //   }
        // })
      }

      ws.onclose = function (e) {
        if (e) {
        }
        //连接关闭消息，可不处理，或者打印调试日志
      }
    },
    /*登录后，向后端提交登录信息*/
    handleSubmit() {
      let that = this
      let loginParams = {}
      that.loginLoading = true
      that.loginDisabled = true
      // 使用账户密码登录
      if (that.customActiveKey === 'tab1') {
        that.form.validateFields(
          ['username', 'password', 'inputCode', 'rememberMe'],
          { force: true },
          (err, values) => {
            if (!err) {
              loginParams.username = values.username
              // update-begin- --- author:scott ------ date:20190805 ---- for:密码加密逻辑暂时注释掉，有点问题
              //loginParams.password = md5(values.password)
              //loginParams.password = encryption(values.password,that.encryptedString.key,that.encryptedString.iv)
              loginParams.password = values.password
              loginParams.remember_me = values.rememberMe
              // update-begin- --- author:scott ------ date:20190805 ---- for:密码加密逻辑暂时注释掉，有点问题
              loginParams.captcha = that.inputCodeContent
              loginParams.checkKey = that.currdatetime
              // todo 设置验证码失效提醒，具体时间根据后端randomImage接口内设置的redis有效时间有关
              if (new Date().getTime() - that.currdatetime > 60 * 1000) {
                that.requestFailed({ message: '验证码超时，请刷新后重试' })
                that.loginLoading = false
                that.loginDisabled = false
                this.handleChangeCheckCode()
                return
              }
              that
                .Login(loginParams)
                .then((res) => {
                  //此处添加判断账号是否已经登录的逻辑
                  if (res.result.isOline) {
                    that.$confirm({
                      title: '提示',
                      content: '此帐号已在其他终端登录，是否继续登录!',
                      okText: '确定',
                      cancelText: '取消',
                      onOk: () => { that.$refs.loginSelect.show(res.result)},
                      onCancel: () => {
                        that.Logout({})
                          .then(() => {
                          window.location.reload()
                          })
                          .catch((err) => {
                            that.$message.error({
                              title: '错误',
                              description: err.message,
                            })
                          })
                      },
                    })
                    return
                  }
                  that.$refs.loginSelect.show(res.result)
                })
                .catch((err) => {
                  that.requestFailed(err)
                })
              that.loginLoading = false
              that.loginDisabled = true
            } else {
              that.loginLoading = false
              that.loginDisabled = false
            }
          }
        )
        // 使用手机号登录
      } else {
        that.form.validateFields(['mobile', 'captcha', 'rememberMe'], { force: true }, (err, values) => {
          if (!err) {
            loginParams.mobile = values.mobile
            loginParams.captcha = values.captcha
            loginParams.remember_me = values.rememberMe
            that
              .PhoneLogin(loginParams)
              .then((res) => {
                this.$refs.loginSelect.show(res.result)
              })
              .catch((err) => {
                that.requestFailed(err)
              })
          }
        })
      }
    },
    getCaptcha(e) {
      e.preventDefault()
      let that = this
      this.form.validateFields(['mobile'], { force: true }, (err, values) => {
        if (!values.mobile) {
          that.cmsFailed('请输入手机号')
        } else if (!err) {
          this.state.smsSendBtn = true
          let interval = window.setInterval(() => {
            if (that.state.time-- <= 0) {
              that.state.time = 60
              that.state.smsSendBtn = false
              window.clearInterval(interval)
            }
          }, 1000)

          const hide = this.$message.loading('验证码发送中..', 0)
          let smsParams = {}
          smsParams.mobile = values.mobile
          smsParams.smsmode = '0'
          postAction('/sys/sms', smsParams)
            .then((res) => {
              if (!res.success) {
                setTimeout(hide, 0)
                this.cmsFailed(res.message)
              }
              setTimeout(hide, 500)
            })
            .catch((err) => {
              setTimeout(hide, 1)
              clearInterval(interval)
              that.state.time = 60
              that.state.smsSendBtn = false
              this.requestFailed(err)
            })
        }
      })
    },
    stepCaptchaSuccess() {
      this.loginSuccess()
    },
    stepCaptchaCancel() {
      this.Logout().then(() => {
        this.loginLoading = false
        this.loginDisabled = false
        this.stepCaptchaVisible = false
      })
    },
    handleChangeCheckCode() {
      this.currdatetime = new Date().getTime()
      if (this.form.getFieldValue('inputCode') != null || this.form.getFieldValue('inputCode') != '') {
        this.form.setFieldsValue({
          inputCode: '',
        })
      }
      getAction(`/sys/randomImage/${this.currdatetime}`)
        .then((res) => {
          if (res.success) {
            this.randCodeImage = res.result
            this.requestCodeSuccess = true
          } else {
            this.$message.error(res.message)
            this.requestCodeSuccess = false
          }
        })
        .catch(() => {
          this.requestCodeSuccess = false
        })
    },
    async loginSuccess() {
      var userPlatforms = []
      await getAction('/sys/permission/getUserPlatformTypeByToken')
        .then((res) => {
          if (res.success) {
            if (res.result == '') {
              this.loginLoading = false
              this.loginDisabled = false
              this.$message.warning('当前用户没有菜单权限，请联系管理员分配权限')
            } else {
              //用户只用运维助手菜单的
              if (res.result === '9') {
                this.$warning({
                  title: '提示',
                  content: '没有平台权限，请联系管理员！',
                  onOk: () => {
                    store.dispatch('Logout').then(() => {
                      this.$router.push({ path: '/user/login' })
                      window.location.reload()
                    })
                  },
                })
                return
              }
              userPlatforms = [...res.result.split(',')]
              Vue.ls.set(ONE_PLATFORM_FLAG, false)
              //判断是否为单模块
              if (userPlatforms.length == 1 && userPlatforms[0] != '') {
                Vue.ls.set(ONE_PLATFORM_FLAG, true)
              }

              if (window._CONFIG['system_Type'] === '0') {
                //一个模块
                if (Vue.ls.get(ONE_PLATFORM_FLAG)) {
                  //加载单模块菜单，直接显示单模块，
                  this.entrancePlanning(parseInt(userPlatforms[0]))
                }
                //多个模块，默认跳至首页（模块选择页面）
                else {
                  //路由默认跳至首页可选择模块
                  this.$router.push({ path: '/gateway/gateway' }).catch(() => {})
                }
              }
              else if (window._CONFIG['system_Type'] === '1') {
                //2用户身份为上级，1用户身份为普通用户
                if (store.getters.userInfo.userIdentity === 2) {
                  //加载数据中心菜单，直接显示该模块
                  this.entrancePlanning(4)
                } else {
                  //加载监控中心菜单，直接显示该模块
                  this.entrancePlanning(1)
                }
              }
              else if (window._CONFIG['system_Type'] === '2') {
                //
                getAction('/sys/permission/platformTypeList')
                  .then(res=>{
                    if(res.success && res.result){
                      let platType = res.result[0].value
                      if (userPlatforms.includes(platType)) {
                        this.entrancePlanning(platType)
                      } else {
                        this.entrancePlanning(userPlatforms[0])
                      }
                    }
                })
              }
              this.$notification.success({
                message: '欢迎',
                description: `${timeFix()}，欢迎回来`,
              })
            }
          } else {
            this.loginLoading = false
            this.loginDisabled = false
            // alert(err.message)
          }
        })
        .catch((err) => {
          this.loginLoading = false
          this.loginDisabled = false
          // alert(err.message)
        })
    },
    ...mapActions(['GetPermissionList']),
    entrancePlanning(index) {
      sessionStorage.setItem(PLATFORM_TYPE, index)
      const that = this
      that.GetPermissionList(index).then((res) => {
        if (res === '1') {
          //处理上级用户没有数据中心查看权限
          if (store.getters.userInfo.userIdentity === 2 && window._CONFIG['system_Type'] === '1' && index === 4) {
            this.entrancePlanning(1)
          } else if (window._CONFIG['system_Type'] === '2' && index === 1) {
            this.entrancePlanning(4)
          } else {
            this.loginLoading = false
            this.loginDisabled = false
            this.$message.warning('当前用户没有菜单权限，请联系管理员分配权限')
            return
          }
        } else {
          const menuData = res.result.menu
          var redirect = ''
          if (menuData && menuData.length > 0) {
            if (menuData[0].children == null || menuData[0].children == undefined) {
              redirect = menuData[0].path
            } else {
              redirect = menuData[0].children[0].path
            }
          } else {
            return
          }

          let constRoutes = []
          if (index === 4) {
            constRoutes = generateBigscreenRouter(menuData)
          } else {
            constRoutes = generateIndexRouter(menuData)
          }
          // 添加主界面路由
          store.dispatch('UpdateAppRouter', { constRoutes }).then(() => {
            // 根据roles权限生成可访问的路由表
            // 动态添加可访问路由表
            router.addRoutes(store.getters.addRouters)
            this.$router.push({ path: redirect })
            // }
          })
        }
      })
    },
    cmsFailed(err) {
      this.$notification['error']({
        message: '登录失败',
        description: err,
        duration: 4,
      })
    },
    requestFailed(err) {
      this.$notification['error']({
        message: '登录失败',
        description: ((err.response || {}).data || {}).message || err.message || '请求出现错误，请稍后再试',
        duration: 4,
      })
      this.loginLoading = false
      this.loginDisabled = false
    },
    validateInputCode(rule, value, callback) {
      if (!value || this.verifiedCode == this.inputCodeContent) {
        callback()
      } else {
        callback('您输入的验证码不正确!')
      }
    },
    generateCode(value) {
      this.verifiedCode = value.toLowerCase()
    },
    inputCodeChange(e) {
      this.inputCodeContent = e.target.value
    },
    loginSelectOk() {
      this.loginSuccess()
    },
    getRouterData() {
      this.$nextTick(() => {
        if (this.$route.params.username) {
          this.form.setFieldsValue({
            username: this.$route.params.username,
          })
        }
        if (this.$route.query.authorizedOrNot) {
          Vue.prototype.$message.error({ content: '未授权或者授权过期，请上传授权证书！', duration: 5 });
          this.authorizedOrNot = false;
        }else{

        }
      })
    },
    //获取密码加密规则
    getEncrypte() {
      var encryptedString = Vue.ls.get(ENCRYPTED_STRING)
      if (encryptedString == null) {
        getEncryptedString().then((data) => {
          this.encryptedString = data
        })
      } else {
        this.encryptedString = encryptedString
      }
    },
  },
}
</script>

<style lang="less" scoped>
input::placeholder{
  color:rgba(255,255,255,0.8) !important;
}
.ukey-tip {
  color: #fff;
  font-size: 18px;
  text-align: center;
  width: 100%;
  // font-style: italic;
}
.user-layout-login {
  label {
    font-size: 14px;
  }

  .getCaptcha {
    display: block;
    width: 100%;
    height: 40px;
  }

  .forge-password {
    font-size: 14px;
  }
  button.login-button {
    padding: 0 15px;
    font-size: 20px;
    height: 60px;
    width: 46%;
    background-image: url(../../assets/login.png);
    background-repeat: no-repeat;
    background-color: transparent;
    border: 0;
    border-radius: 50px;
  }

  .user-login-other {
    text-align: left;
    margin-top: 24px;
    line-height: 22px;

    .item-icon {
      font-size: 24px;
      color: rgba(0, 0, 0, 0.2);
      margin-left: 16px;
      vertical-align: middle;
      cursor: pointer;
      transition: color 0.3s;

      &:hover {
        color: #1e3674;
      }
    }

    .register {
      float: right;
    }
  }
}
.authorization {
  color: #fff;
  .authorization-upload {
    width: 468px;
    height: 248px;
    background-image: url(../../assets/05.png);
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    letter-spacing: 2px;
    a {
      color: #fff;
    }
    a:hover {
      color: skyblue;
    }
  }
  .authorization-button {
    width: 100%;
    display: flex;
    justify-content: center;
    margin-top: 40px;

    .shouquan-button {
      padding: 0 15px;
      font-size: 20px;
      height: 60px;
      width: 46%;
      background-image: url(../../assets/login.png);
      background-repeat: no-repeat;
      background-color: transparent;
      border: 0;
      border-radius: 50px;
    }
  }
}
/deep/ .ant-upload-list-item-name {
  color: #fff !important;
}
/deep/ .anticon-paper-clip {
  color: #fff !important;
}
</style>
<style scoped>
input::placeholder{
  color:rgba(255,255,255,0.8) !important;
}
.valid-error .ant-select-selection__placeholder {
  color: #f5222d;
}
.codeStyle {
  position: absolute;
  top: 17px;
  right: 25px;
  border-left: 1px solid rgb(170, 170, 170);
  padding-left: 10px;
  width: 25%;
}
#username,
#password,
#inputCode {
  width: 100%;
  height: 64px;
  background-color: transparent;
  color: #fff;
  outline: none;
  border-inline: none;
  border: 1px solid #b2b4be;
  border-radius: 45px;
  text-align: center;
  margin-bottom: 4px;
  font-size: 24px;
  padding-top: 0;
}
#inputCode {
  padding: 0 104px 0 93px;
}
/*chrome浏览器input自动填充颜色设为透明  设置字体颜色*/
input:-webkit-autofill {
  -webkit-text-fill-color: #ffffff !important;
  transition: background-color 5000s ease-in-out 0s;
}
input:-internal-autofill-selected {
  background-color: transparent !important;
  color: rgb(0, 0, 0) !important;
  border: 1px solid #b2b4be;
  border-radius: 45px;
}
input::-webkit-input-placeholder {
  /* WebKit browsers */
  color: rgba(255, 255, 255, 0.45);
  font-size: 18px;
}
input:-moz-placeholder {
  /* Mozilla Firefox 4 to 18 */
  color: rgba(255, 255, 255, 0.45);
  font-size: 18px;
}
input::-moz-placeholder {
  /* Mozilla Firefox 19+ */
  color: rgba(255, 255, 255, 0.45);
  font-size: 18px;
}
input:-ms-input-placeholder {
  /* Internet Explorer 10+ */
  color: rgba(255, 255, 255, 0.45);
  font-size: 18px;
}
.code {
  font-size: 24px;
  color: #aaa;
  cursor: pointer;
  padding-bottom: 6px;
  line-height: 30px;
  text-align: center;
  user-select: none;
}
.formBtn {
  margin-top: 30px;
}
.has-error .ant-form-explain,
.has-error .ant-form-split {
  text-align: center;
}
.formBox {
  text-align: center;
}
@media (min-width: 1440px) and (max-width: 1620px) {
  #username,
  #password,
  #inputCode {
    height: 62px !important;
  }
  .formBtn {
    margin-top: 15px;
  }
}
@media (max-width: 1380px) {
  #username,
  #password,
  #inputCode {
    width: 90% !important;
    height: 57px !important;
  }
  .formBox {
    text-align: center;
  }
  .formBtn {
    margin-top: 15px;
  }
  .ant-form-item {
    margin-bottom: 15px !important;
  }
  button.login-button {
    font-size: 20px !important;
    height: 50px !important;
    width: 37% !important;
    background-size: 100% !important;
  }
  .codeStyle {
    position: absolute;
    top: 12px;
    right: 35px;
    border-left: 1px solid rgb(170, 170, 170);
    padding-left: 10px;
    width: 25%;
  }
}
</style>
