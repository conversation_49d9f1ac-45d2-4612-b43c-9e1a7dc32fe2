<template>
  <j-modal
    :title='title'
    :width='width'
    :visible='visible'
    :destroyOnClose='true'
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    :centered='true'
    switch-fullscreen
    @ok='handleOk'
    @cancel='handleCancel'
    cancelText='关闭'>
    <a-spin :spinning='confirmLoading'>
      <j-form-container :disabled='disableSubmit'>
        <a-form-model ref='form' slot='detail' :model='model' :rules='validatorRules' :labelCol='labelCol'
          :wrapperCol='wrapperCol'>
          <a-row>
            <a-col :span='24'>
              <a-form-model-item label='级别名称' prop='levelName'>
                <a-input style='width: 100%' v-model='model.levelName' :allow-clear='true' autocomplete='off'
                  placeholder='请输入级别名称' />
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
              <a-form-model-item label='告警等级' prop='alarmLevel'>
                <a-input-number style='width: 200px' v-model='model.alarmLevel' :min='0' :max="9999999999999" :precision='0' :step="1" :allow-clear='true'
                  autocomplete='off' placeholder='请输入告警等级' />
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
              <a-form-model-item label='告警颜色' prop='color' :labelCol='labelCol' :wrapperCol='wrapperCol1'>
                <a-input class='color' type='color' v-model='model.color' :allow-clear='true' autocomplete='off'
                  placeholder='请输入告警颜色' />
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
              <a-form-model-item label='级别描述' prop='remark'>
                <a-textarea style='width: 100%' v-model='model.remark' :autoSize='{minRows:3,maxRows:6}'
                  :allow-clear='true' autocomplete='off' placeholder='请输入级别描述' />
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
              <a-form-model-item label='告警模式' prop='alarmMode'>
                <a-radio-group v-model='model.alarmMode' @change=radioChange>
                  <a-radio value="0">上传语音</a-radio>
                  <a-radio value="1">语音播报</a-radio>
                </a-radio-group>
              </a-form-model-item>
            </a-col>
            <a-col :span='24' v-if="modeType == '0'">
              <a-form-model-item label='告警声音' prop='voice'>
                <yq-upload v-model='model.voice' :showUploadList='true' :accept='accept' :multiple='false' :number='1'
                  :file-list='fileList' :before-upload='beforeUpload' bizPath='voice/alarmVoice'></yq-upload>
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
              <a-form-model-item :labelCol='labelCol' :wrapperCol='wrapperCol' label='评分' prop='score'>
                <a-input-number style='width: 200px;margin-right: 8px;' placeholder='请输入评分' v-model='model.score' :min='0' :max='100'
                                :allow-clear='true' autocomplete='off' />
                <a-tooltip title="用于设备健康度计算，满分100分，扣分制，健康度最低为0">
                  <a-icon type="info-circle" theme="twoTone" />
                </a-tooltip>
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </j-form-container>
    </a-spin>
  </j-modal>
</template>

<script>
  import {
    httpAction
  } from '@api/manage'
  import YqUpload from '@comp/jeecg/yqUpload.vue'
  import {ValidateOptionalFields,ValidateRequiredFields} from '@/utils/rules.js'
  export default {
    name: 'AlarmLevelModal',
    components: {
      YqUpload
    },
    data() {
      return {
        title: '',
        width: '800px',
        visible: false,
        disableSubmit: false,
        confirmLoading: false,
        modeType: '0',
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          }
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          }
        },
        wrapperCol1: {
          xs: {
            span: 6
          },
          sm: {
            span: 4
          }
        },
        model: {
          levelName: '',
          alarmMode: '0',
          alarmLevel: null,
          color: '#DA2F2F',
          remark: '',
          voice: '',
          id: '',
          score: 0
        },
        validatorRules: {
          levelName: [{
            required: true,
            validator: (rule, value, callback) => ValidateRequiredFields(rule, value, callback, '级别名称', 50, 1)
          }
          ],
          alarmLevel: [{
            required: true,
            message: '请输入告警等级！'
          }],
          color: [{
            required: true,
            message: '请选择告警颜色！'
          }],
          voice: [{
            required: true,
            message: '请上传告警声音！'
          }],
          alarmMode: [{
            required: true,
            message: '请选择告警模式！'
          }],
          remark: [{
            required: false,
            validator: (rule, value, callback) =>ValidateOptionalFields(rule, value, callback,'级别描述',200)}
          ],
          score: [{
            required: true,
            message: '请输入评分！'
          }],
        },
        accept: '.mp3',
        fileList: null,
        url: {
          add: '/alarm/alarmLevel/add',
          edit: '/alarm/alarmLevel/edit'
        }
      }
    },

    methods: {
      radioChange(value) {
        this.model.voice = ''
        this.modeType = value.target.value
      },
      beforeUpload(file) {
        var testmsg = file.name.substring(file.name.lastIndexOf('.') + 1)
        const extension = this.accept.includes(testmsg)
        if (!extension) {
          this.$message.error('上传文件只能是' + this.accept + '格式！')
        }
        return extension
      },
      add() {
        this.model = {
          levelName: '',
          alarmMode: '0',
          alarmLevel: null,
          color: '#DA2F2F',
          remark: '',
          voice: '',
          id: '',
          score: 0
        }
        this.edit(this.model)
      },
      edit(record) {
        this.visible = true
        this.$nextTick(() => {
          Object.assign(this.model, record)
          this.modeType = this.model.alarmMode
          if (this.model && this.model.color && this.model.color.includes('rgb')) {
            this.model.color = this.hexChange(this.model.color)
          }
        })
      },
      // 将颜色RGB格式转换问Hex格式
      hexChange(color) {
        var values = color
          .replace(/rgba?\(/, '')
          .replace(/\)/, '')
          .replace(/[\s+]/g, '')
          .split(',')
        var a = parseFloat(values[3] || 1),
          r = Math.floor(a * parseInt(values[0]) + (1 - a) * 255),
          g = Math.floor(a * parseInt(values[1]) + (1 - a) * 255),
          b = Math.floor(a * parseInt(values[2]) + (1 - a) * 255)
        return (
          '#' +
          ('0' + r.toString(16)).slice(-2) +
          ('0' + g.toString(16)).slice(-2) +
          ('0' + b.toString(16)).slice(-2)
        )
      },
      close() {
          this.fileList = null
        this.loading = false
        this.uploadGoOn = false
        this.visible = false
      },
      handleOk() {
        const that = this
        that.$refs.form.validate((err, value) => {
          if (err) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!that.model.id) {
              httpurl += that.url.add
              method = 'post'
            } else {
              httpurl += that.url.edit
              method = 'put'
            }
            let formData = {
              ...that.model
            }
            // 将颜色Hex格式转换问RGB格式
            let color = formData.color.slice(1)
            let rgbColor = [parseInt('0x' + color.slice(0, 2)),
              parseInt('0x' + color.slice(2, 4)),
              parseInt('0x' + color.slice(4, 6))
            ]
            formData.color = 'rgb(' + rgbColor.toString() + ')'
            httpAction(httpurl, formData, method)
              .then((res) => {
                if (res.success) {
                  that.$message.success(res.message)
                  that.$emit('ok')
                  that.close()
                } else {
                  that.$message.warning(res.message)
                }
                that.confirmLoading = false
              }).catch((res) => {
                that.$message.warning(res.message)
                that.confirmLoading = false
              })
          }
        })
      },
      submitCallback() {
        this.$emit('ok')
        this.visible = false
      },
      handleCancel() {
        this.close()
      }
    }
  }
</script>
<style scoped lang='less'>
  @import '~@assets/less/normalModal.less';

  .color ::v-deep.ant-input {
    padding: 0px 30px 0px 11px !important;
  }
</style>