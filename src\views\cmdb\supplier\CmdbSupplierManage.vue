<template>
  <div style='height: 100%'>
    <keep-alive exclude='CmdbSupplierDetails'>
      <component :is="pageName" :data="data" />
    </keep-alive>
  </div>

</template>
<script>
import CmdbSupplierList from './CmdbSupplierList'
import CmdbSupplierDetails from './modules/CmdbSupplierDetails'
export default {
  name: 'CmdbSupplierManage',
  data() {
    return {
      isActive: 0,
      data: {},
    }
  },
  components: {
    CmdbSupplierList,
    CmdbSupplierDetails,
  },
  created() {
    this.pButton1(0)
  },
  //使用计算属性
  computed: {
    pageName() {
      switch (this.isActive) {
        case 0:
          return 'CmdbSupplierList'
          break

        default:
          return 'CmdbSupplierDetails'
          break
      }
    },
  },
  methods: {
    pButton1(index) {
      this.isActive = index
    },
    pButton2(index, item) {
      this.isActive = index
      this.data = item
    },
  },
}
</script>