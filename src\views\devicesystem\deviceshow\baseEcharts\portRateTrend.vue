<template>
  <div style="width:100%;height:100%;">
    <!-- 需要判断物模型没有配置总速率的情况 -->
    <recent-time v-if="allSpeed.length>0" @changeType="changeType" :selectedIndex="selectedIndex" :typeList="typeList"></recent-time>
    <div style="width:100%;height:100%;position:relative;">
      <!-- 总速率 -->
      <port-all-rate
        ref="allSpeed"
        :chartData="allSpeed"
        :fontSizeObject="fontSizeObject"
        v-if="allSpeed.length>0 && selectedIndex==0"
        style="position:absolute;top:0;left:0;width:100%;height:100%"
      ></port-all-rate>
      <!-- 上下行速率 -->
      <in-speed
        ref="inSpeed"
        :chartData="{inSpeed:inSpeed,outSpeed:outSpeed}"
        :fontSizeObject="fontSizeObject"
        v-if="(allSpeed.length==0 && selectedIndex==0) || selectedIndex==1"
        style="position:absolute;top:0;left:0;width:100%;height:100%"
      ></in-speed>
    </div>
  </div>
</template>
<script>
import portAllRate from './portAllRate.vue'
import inSpeed from './inSpeed.vue'
import recentTime from './recentTime.vue'
export default {
  name: 'portRateTrend',
  components: {
    portAllRate,
    inSpeed,
    recentTime
  },
  props: {
    allSpeed: {
      type: Array,
      default: () => []
    },
    inSpeed: {
      type: Array,
      default: () => []
    },
    outSpeed: {
      type: Array,
      default: () => []
    },
    fontSizeObject: {
      type: Object,
      default: function() {
        return {
          legendFontSize: 8,
          xAxisFontSize: 8,
          yAxisFontSize: 10
        }
      }
    }
  },
  data() {
    return {
      selectedIndex: 0,
      typeList: ['总速率', '上下行速率']
    }
  },
  methods: {
    changeType(index) {
      this.selectedIndex = index
      if (this.selectedIndex == 0) {
        // 总速率
        this.$nextTick(() => {
          this.$refs.allSpeed.initData(this.allSpeed)
        })
      } else {
        // 上下行速率
        this.$nextTick(() => {
          this.$refs.inSpeed.initData({inSpeed: this.inSpeed, outSpeed: this.outSpeed})
        })
      }
    }
  }
}
</script>
