<template>
  <a-spin :spinning="confirmLoading">
    <div class="colorBox">
      <span class="colorTotal">分组信息</span>
    </div>
    <j-form-container>
      <a-form :form="form" slot="detail">
        <a-row :gutter="24">
          <a-col :span="24">
            <a-form-item label="分组名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                v-decorator="['groupName', validatorRules.groupName]"
                :allowClear="true"
                autocomplete="off"
                placeholder="请输入分组名称"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item
              :label-col="labelCol"
              :wrapper-col="wrapperCol"
              label="运维人员"
              required
              class="form-item"
            >
              <j-select-user-by-dep v-decorator="['userIds', {initialValue: '', rules: validatorRules.userIds.rules}]" :multi="true"></j-select-user-by-dep>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="描述" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-textarea
                v-decorator="['description', validatorRules.description]"
                placeholder="请输入描述"
                :autoSize="{ minRows: 2, maxRows: 5 }"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="选择设备" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-radio-group v-model="queryParam.isMaxPrivilege" placeholder="选择设备">
                <a-radio value="0">部分设备</a-radio>
                <a-radio value="1">全部设备</a-radio>
              </a-radio-group>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>

      <div v-show="queryParam.isMaxPrivilege == '0'">
        <div class="colorBox">
          <span class="colorTotal">设备选择</span>
        </div>

        <!-- 设备选择 新增表格 -->
        <div>
          <div class="table-operator table-operator-style">
            <a-button @click="handleAddDevice" type="primary">新增</a-button>
            <a-button v-if="taskId && dataSource.length" @click="unbindAllDevice">解绑全部</a-button>
            <a-button v-else-if="dataSource.length" @click="unbindAllDevice">删除全部</a-button>
          </div>
          <a-table
            size="middle"
            bordered
            :row-key="(record, index) => {
              return record.id
          }
              "
            :columns="columns"
            :dataSource="dataSource"
            :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
            :pagination="ipagination"
            :loading="loading"
            @change="handleTableChange"
          >
            <span slot="action" slot-scope="text, record">
              <a-popconfirm v-if="taskId" title="确定解绑设备吗?" @confirm="() => deleteTablbeData(record)">
                  <a>解绑</a>
              </a-popconfirm>
              <a v-else @click="deleteTablbeData(record)">删除</a>
            </span>
          </a-table>
        </div>
      </div>
    <device-selection-modal ref="modalForm" @loadTableData="loadTableData" @ok="tableOK"></device-selection-modal>
  </a-spin>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import pick from 'lodash.pick'
import JFormContainer from '@/components/jeecg/JFormContainer'
import JSelectUserByDep from '@comp/jeecgbiz/JSelectUserByDep.vue'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
export default {
  name: 'addGroupForm',
  mixins: [JeecgListMixin],
  components: {
    JFormContainer,
    JSelectUserByDep,
    DeviceSelectionModal: () => import('./DeviceSelectionModal')
  },
  data() {
    return {
      form: this.$form.createForm(this),
      model: {
        deviceIds: '',
        users: ''
      },
      labelCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 4
        },
        md: {
          span: 6
        },
        lg: {
          span: 4
        }
      },
      wrapperCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 19
        },
        md: {
          span: 15
        },
        lg: {
          span: 18
        }
      },
      confirmLoading: false,
      formItemLayout: {
        labelCol: {
          span: 8
        },
        wrapperCol: {
          span: 16
        }
      },
      // 查询条件
      queryParam: {
        groupName: '',
        isMaxPrivilege: '1' // 是否最大权限(1 是；0 否)
      },
      validatorRules: {
        groupName: {
          rules: [
            {
              required: true,
              message: '请输入分组名称'
            },
            {
              min: 2,
              max: 30,
              message: '长度在2到30个字符',
              trigger: 'blur'
            }
          ]
        },
        userIds: {
          rules: [
            {
              required: true,
              message: '请选择运维人员'
            }
          ]
        },
        description: {
          rules: [{ required: false, min: 0, max: 255, message: '描述长度应在 0-255 之间' }]
        },
      },
      url: {
        list: '/device/deviceGroup/getDevice', // 查询已绑定设备
        add: '/device/deviceGroup/add',
        edit: '/device/deviceGroup/edit',
        delete: '/device/deviceGroup/unbindDevice' // 解除绑定设备
      },
      taskId: undefined,
      disableMixinCreated: true,
      // 表头
      columns: [
        {
          title: '产品名称',
          dataIndex: 'productName',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 150px;max-width:300px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '设备名称',
          dataIndex: 'name',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 150px;max-width:300px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: 'IP',
          dataIndex: 'ip',
          customCell: () => {
            let cellStyle = 'text-align: center;width:100px'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'status'
          }
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          scopedSlots: {
            customRender: 'action'
          }
        }
      ],
      dataSource: [],
      // 选择用户查询条件配置
      selectUserQueryConfig: [{ key: 'phone', label: '电话' }],
      defaultUser: {
        name: '用户',
        width: 1250,
        displayKey: 'realname',
        returnKeys: ['id', 'username'],
        queryParamText: '账号',
        queryParamCode: 'username'
      }
    }
  },
  created() {
    this.dataSource = []
  },
  mounted() {},
  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      this.taskId = record.id
      this.visible = true
      this.model = Object.assign({}, record)
      this.$nextTick(() => {
        this.formInit(this.model)
      })
      if (null != this.taskId && undefined != this.taskId && '' != this.taskId) {
        this.loadTableData()
      } else {
        this.dataSource = []
      }
      if (record.id) {
        this.loadData()
        if (record.isMaxPrivilege == 0) {
          this.queryParam.isMaxPrivilege = '0'
        } else {
          this.queryParam.isMaxPrivilege = '1'
        }
      }
    },
    formInit(pickData) {
      this.form.setFieldsValue(pick(pickData, 'groupName', 'userIds', 'description', 'isMaxPrivilege'))
    },
    loadData(arg) {
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1
      }
      var params = this.getQueryParams() //查询条件
      this.loading = true
      getAction(this.url.list, params).then(res => {
        if (res.success) {
          this.dataSource = res.result.records || res.result
          if (this.dataSource.length < 9) {
            this.clientHeight = false
          }
          this.ipagination.total = res.result.total ? res.result.total : 0
        }
        if (res.code === 510) {
          this.$message.warning(res.message)
        }
        this.loading = false
      })
    },
    handleTableChange(pagination, filters, sorter) {
      //分页、排序、筛选变化时触发
      //TODO 筛选
      if (Object.keys(sorter).length > 0) {
        this.isorter.column = sorter.field
        this.isorter.order = 'ascend' == sorter.order ? 'asc' : 'desc'
      }
      this.ipagination = pagination
      if (this.taskId) {
        this.loadData()
      }
    },
    //向后端请求table数据
    loadTableData() {
      this.queryParam.groupId = this.taskId
      this.loading = true
      this.loadData()
    },
    tableOK(idList, list) {
      let deviceIds = this.model.deviceIds || []
      if (list && list.length > 0) {
        list.forEach(ele => {
          if (deviceIds.indexOf(ele.id) == -1) {
            this.dataSource.push(ele)
          }
        })
        let ids = this.dataSource.map(m => {
          return m.id
        })
        this.model.deviceIds = ids.join(',')
      }
    },
    //table中操作删除数据
    deleteTablbeData(record) {
      if (null == this.taskId || '' == this.taskId) {
        this.dataSource.forEach((ele, index) => {
          if (this.dataSource[index].id == record.id) {
            this.dataSource.splice(index, 1)
          }
          let ids = this.dataSource.map(m => {
            return m.id
          })
          this.model.deviceIds = ids.join(',')
        })
      } else {
        let param = {
          groupId: this.taskId,
          deviceId: record.id
        }
        this.loading = true
        getAction(this.url.delete, param).then((res) => {
          if (res.success) {
            this.$message.success(res.message)
            this.loadTableData()
          } else {
            this.$message.warning(res.message)
          }
          this.loading = false
        })
        .catch((err) => {
          this.loading = false
          this.$message.error(err.message)
        })
      }
    },
    //提交
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (
          !err
          //  && !isError
        ) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }

          let formData = Object.assign(this.model, values)
          formData.deviceIds = this.model.deviceIds != null ? this.model.deviceIds : ''
          formData.id = that.taskId
          if (this.queryParam.isMaxPrivilege == '0') {
            formData.isMaxPrivilege = 0
          } else {
            formData.isMaxPrivilege = 1
            formData.deviceIds = ''
          }

          httpAction(httpurl, formData, method)
            .then(res => {
              if (res.success) {
                that.$message.success(res.message)

                if (!this.model.id) {
                  that.taskId = res.result
                  that.queryParam.id = that.taskId
                }
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    // 删除或解绑全部设备
    unbindAllDevice() {
      if (null == this.taskId || '' == this.taskId) {
        this.dataSource = []
        this.model.deviceIds = ''
      } else {
        var that = this
        that.$confirm({
          title: '确认',
          okText: '是',
          cancelText: '否',
          content: '是否解绑全部设备?',
          onOk: function () {
            let param = {
              groupId: that.taskId
            }
            that.loading = true
            getAction(that.url.delete, param).then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.loadTableData()

              } else {
                that.$message.warning(res.message)
              }
              that.loading = false
            })
            .catch((err) => {
              that.loading = false
              this.$message.error(err.message)
            })
          }
        })
      }
    },
    //新增添加设备
    handleAddDevice: function() {
       let record = {
        id: this.taskId
      }
      if (this.taskId) {
        this.$refs.modalForm.edit(record)
      } else {
        this.$refs.modalForm.edit(record, this.model.deviceIds)
      }
      this.$refs.modalForm.title = '设备添加'
      this.$refs.modalForm.disableSubmit = false
    }
  }
}
</script>
<style scoped lang='less'>
.colorBox {
  margin-top: 25px;
  margin-bottom: 18px;
  .colorTotal {
    padding-left: 7px;
    border-left: 4px solid #1e3674;
  }
}
</style>