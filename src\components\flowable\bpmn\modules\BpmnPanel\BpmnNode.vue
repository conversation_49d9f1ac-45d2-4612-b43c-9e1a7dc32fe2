<template>
  <div>
    <a-form ref="refForm" :form="formData" :label-col="{ span: 5 }" :wrapper-col="{ span: 19 }">
      <a-collapse
          :activeKey="activeName"
          :bordered="false"
          accordion
          expandIconPosition="right"
          @change="collapseChange"
      >
        <a-collapse-panel key="1" header="基本设置">
          <a-form-item label="节点ID">
            <a-input v-model="formData.id" disabled="" />
            <a-button
                @click="
                () => {
                  this.formData.id = '__initiator__'
                }
              "
            >设置为开始节点</a-button
            >
          </a-form-item>
          <a-form-item label="节点名称">
            <a-input v-model="formData.name" allow-clear />
          </a-form-item>
          <a-form-item label="节点描述">
            <a-textarea v-model="formData.documentation" :rows="3" allow-clear />
          </a-form-item>
<!--          <a-form-item label="到期时间">
            <a-input @click="setDue" v-model="formData.dueDate" allow-clear readOnly  />

            <SettingDueDate ref="setDueDate" @getDueDate="getDueDate"></SettingDueDate>
          </a-form-item>-->
          <a-form-item v-if="!!showConfig.taskCategory" label="任务分类">
            <a-select v-model="formData.category" :getPopupContainer="(node) => node.parentNode">
              <a-select-option v-for="item in taskCategory" :key="item.value" :value="item.value">
                {{ item.title }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item v-if="!!showConfig.conditionExpression" label="流转条件">
            <a-input v-model="formData.conditionExpression" allow-clear />
          </a-form-item>
          <a-form-item v-if="!!showConfig.async" label="异步">
            <a-switch v-model="formData.async" />
          </a-form-item>
          <!-- <a-form-item v-if="!!showConfig.multiInstance" label="多实例">
            <a-button type="primary">编辑</a-button>
          </a-form-item> -->
          <a-form-item label="类" v-if="showConfig.class">
            <a-select
                allow-clear
                v-model="formData.classType"
                :getPopupContainer="(node) => node.parentNode"
                @change="classTypeChange"
            >
              <a-select-option v-for="item in listenerTypes" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="值" v-if="showConfig.class && formData.classType">
            <a-textarea v-model="formData.classValue" :rows="3" allow-clear @blur="classValueBlur" />
          </a-form-item>
        </a-collapse-panel>
        <a-collapse-panel v-if="!!showConfig.multiInstance" key="multiInstance" header="多实例">
          <span slot="extra" class="listener-add" @click.stop="showMultiInstance"> <a-icon type="edit" /> 编辑 </span>
          <multi-instance
              ref="multiInstance"
              :element="element"
              :modeler="modeler"
              :multiInstanceShow="multiInstanceShow"
              :title="multiInstanceTitle"
              @changeMultiInstanceShow="changeMultiInstanceShow"
          ></multi-instance>
        </a-collapse-panel>
        <a-collapse-panel v-if="!!showConfig.candidate" key="2" header="候选配置">
          <a-form-item v-if="!!showConfig.initiator" label="创建者">
            <a-input v-model="formData.initiator" allow-clear />
          </a-form-item>
          <a-form-item v-if="!!showConfig.assignee" label="执行人">
            <a-input v-model="formData.assignee" allow-clear />
          </a-form-item>
          <a-form-item v-if="!!showConfig.candidateUsers" label="候选用户">
            <a-input v-model="formData.candidateUsers" allow-clear />
          </a-form-item>
          <a-form-item v-if="!!showConfig.candidateGroups" label="候选组">
            <a-input v-model="formData.candidateGroups" allow-clear />
          </a-form-item>
        </a-collapse-panel>
        <a-collapse-panel v-if="!!showConfig.form" key="form" header="表单配置">
          <div style="min-height: 300px">
            <a-form-item v-if="!!showConfig.formKey" label="表单Key">
              <a-select
                  :getPopupContainer="(node) => node.parentNode"
                  v-model="formData.formKey"
                  :filter-option="filterOption"
                  allow-clear
                  option-filter-prop="children"
                  show-search
                  style="width: 200px"
                  @blur="handleBlur"
                  @change="handleChange"
                  @focus="handleFocus"
              >
                <a-select-option v-for="form in forms" :key="form.formKey">
                  {{ form.formName }}
                </a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item v-if="!!showConfig.buttons" label="按钮">
              <a-select
                  :getPopupContainer="(node) => node.parentNode"
                  v-model="formData.buttons"
                  :filter-option="filterOption"
                  allow-clear
                  autoClearSearchValue
                  mode="multiple"
                  option-filter-prop="children"
                  show-search
                  style="width: 200px"
              >
                <a-select-option v-for="item in buttonList" :key="item.value" :value="item.value">
                  {{ item.title }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </div>
        </a-collapse-panel>
        <a-collapse-panel v-if="!showConfig.class" key="executionListener" header="执行监听器">
          <span slot="extra" class="listener-add" @click.stop="showListenerForm">
            <a-icon type="plus-circle" /> 添加
          </span>
          <listener
              ref="listenerTable"
              :element="element"
              :listenerShow="listenerShow"
              :modeler="modeler"
              :title="listenerTitle"
              type="Execution"
              @changeListenerShow="changeListenerShow"
          ></listener>
        </a-collapse-panel>
        <a-collapse-panel v-if="!!showConfig.taskListener" key="taskListener" header="任务监听器">
          <span slot="extra" class="listener-add" @click.stop="showTaskListenerForm">
            <a-icon type="plus-circle" /> 添加
          </span>
          <listener
              ref="taskListenerTable"
              :element="element"
              :listenerShow="taskListenerShow"
              :modeler="modeler"
              :title="taskListenerTitle"
              type="Task"
              @changeListenerShow="changeListenerShow"
          ></listener>
        </a-collapse-panel>
<!--        <a-collapse-panel key="propertyForm" header="附加属性">
          <span class="listener-add" slot="extra" @click.stop="showPropertyForm">
            <a-icon type="plus-circle" /> 添加
          </span>
          <properties
            ref="propertiesTable"
            :propertiesShow="propertiesShow"
            :title="propertiesTitle"
            :element="element"
            :modeler="modeler"
            @changePropertiesShow="changePropertiesShow"
          ></properties>
        </a-collapse-panel>-->
        <a-collapse-panel v-if="!!showConfig.form" key="slaLevelForm" header="服务级别配置">
          <span class="listener-add" slot="extra" @click.stop="showSlaLevelForm">
            <a-icon type="plus-circle" /> 添加
          </span>
          <slaLevel
            ref="slaLevelTable"
            :processKey="processInfo.key"
            :element="element"
            :modeler="modeler"
          ></slaLevel>
        </a-collapse-panel>
      </a-collapse>
    </a-form>
  </div>
</template>

<script>
import mixinPanel from '../../mixins/mixinPanel'
import mixinNode from '../../mixins/mixinNode'
import Listener from '../Listeners'
import MultiInstance from '../MultiInstance'
import Properties from '../Properties'
import slaLevel from '../SlaLevel'
import SettingDueDate from '../SettingDueDate'
import { getAction } from '@api/manage'
import { ajaxGetDictItems, getDictItemsFromCache } from '@api/api'

export default {
  name: 'BpmnProcess',
  components: {
    SettingDueDate,
    Properties,
    slaLevel,
    Listener,
    MultiInstance,
  },
  mixins: [mixinPanel, mixinNode],
  props: {
    taskCategory: {
      type: Array,
      required: true,
    },
    processInfo: {
      type: Object,
      default: () => {
        return {}
      },
    },
  },
  data() {
    return {
      buttonList: [],
      activeName: '1',
      formData: {},
      rules: {
        id: [{ required: true, message: '节点ID不能为空', trigger: 'change' }],
        name: [{ required: true, message: '节点名称不能为空', trigger: 'change' }],
      },
      listenerShow: false,
      listenerTitle: '执行监听器',
      taskListenerShow: false,
      taskListenerTitle: '任务监听器',
      multiInstanceTitle: '编辑多实例',
      multiInstanceShow: false,
      hasMultiInstance: false,
      forms: [],
      listenerTypes: [
        { value: 'class', label: '类' },
        { value: 'expression', label: '表达式' },
        { value: 'delegateExpression', label: '代理表达式' },
      ],
      propertiesShow: false,
      propertiesTitle: '附加属性',
      propertiesLength: 0,

    }
  },
  created() {
    this.initFormData()
    // 分割bottons 修改已保存的数据展示时无法正常展示
    if (this.formData.buttons && typeof this.formData.buttons === 'string') {
      this.formData.buttons = this.formData.buttons.split(',')
    }
  },
  mounted() {
    this.initDictData('process_button', 'buttonList')
  },
  methods: {
    getDueDate(value){
      this.$set(this.formData,"dueDate",value)
    },
    setDue(){
      this.$refs.setDueDate.openVisible(this.formData.dueDate);
    },
    initDictData(dictCode, list) {
      //优先从缓存中读取字典配置
      if (getDictItemsFromCache(dictCode)) {
        this[list] = getDictItemsFromCache(dictCode)
        console.log(list, this[list])
        return
      }

      //根据字典Code, 初始化字典数组
      ajaxGetDictItems(dictCode, null).then((res) => {
        if (res.success) {
          this[list] = res.result
        }
      })
    },
    initFormData() {
      const data = {
        ...this.element.businessObject,
        ...this.element.businessObject.$attrs,
      }
      const targetNamespace = this.element.businessObject.$parent.targetNamespace
      data.processCategory = targetNamespace
      data.documentation = this.getDocumentation()
      if (this.element.businessObject.conditionExpression) {
        data.conditionExpression = this.parseCDATA(this.element.businessObject.conditionExpression.body)
      }
      this.formData = data
      //服务任务的初始值
      if(data.$type === "bpmn:ServiceTask"){
        this.listenerTypes.forEach(el => {
          if(data[el.value]){
            this.$set(this.formData,"classValue",data[el.value])
            this.$set(this.formData,"classType",el.value)

          }
        });
      }
      this.hasMultiInstance = this.element.businessObject.loopCharacteristics ? true : false
      // 初始化表单数据
      getAction('/flowable/form/list', { pageSize: 1000 }).then(({ result, success }) => {
        if (success) {
          this.forms = result.records
        }
      })
    },

    handleChange(value) {
      // console.log(`selected ${value}`)
    },
    handleBlur() {
      // console.log('blur')
    },
    //监听服务任务类
    classTypeChange(e){
      this.formData.classValue = "";
      this.listenerTypes.forEach(el => {
        this.updateProperty(el.value,undefined,true)
      });
    },
    //监听服务任务值 并给服务任务添加属性
    classValueBlur() {
      this.listenerTypes.forEach(el => {
        if(this.formData.classType === el.value){
          this.updateProperty(this.formData.classType,this.formData.classValue,true)
        }
        else{
          this.updateProperty(el.value,undefined,true)
        }
      });
    },
    handleFocus() {
      // console.log('focus')
    },
    filterOption(input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
    },
    //监听折叠面板
    collapseChange(e) {
      // if(this.activeName)
      this.activeName = e
      // console.log("监听到折叠面板变化 === ",e,this.activeName)
    },
    //显示添加监听器
    showListenerForm() {
      this.activeName = 'executionListener'
      this.listenerTitle = '添加监听器'
      if (this.$refs.listenerTable) {
        this.$refs.listenerTable.init()
        this.listenerShow = true
      } else {
        this.$nextTick(() => {
          this.listenerShow = true
        })
      }
    },
    showTaskListenerForm() {
      this.activeName = 'taskListener'
      this.taskListenerTitle = '添加监听器'
      if (this.$refs.taskListenerTable) {
        this.$refs.taskListenerTable.init()
        this.taskListenerShow = true
      } else {
        this.$nextTick(() => {
          this.taskListenerShow = true
        })
      }
    },
    showMultiInstance() {
      this.activeName = 'multiInstance'
      this.taskListenerTitle = '编辑多实例'
      if (this.$refs.multiInstance) {
        this.$refs.multiInstance.init()
        this.multiInstanceShow = true
      } else {
        this.$nextTick(() => {
          this.multiInstanceShow = true
        })
      }
    },
    //显示属性
    showPropertyForm() {
      this.activeName = 'propertyForm'
      this.propertiesTitle = '添加附加属性'
      if (this.$refs.propertiesTable) {
        this.$refs.propertiesTable.init()
      }
      this.propertiesShow = true
    },
    showSlaLevelForm(){
      this.activeName = 'slaLevelForm'
      this.$refs.slaLevelTable.slaLevelFormShow = true
    },
    //改变监听器弹窗
    changeListenerShow(v) {
      this.listenerShow = v
      this.taskListenerShow = v
    },
    changeTaskListenerShow(v) {},
    changePropertiesShow(v) {
      this.propertiesShow = v
    },
    changeMultiInstanceShow(v) {
      this.multiInstanceShow = v
    },
    saveProperties(v) {
      this.propertiesLength = v
    },
  },
}
</script>

<style lang="less" scoped>
.listener-add {
  color: #1890ff;
  cursor: pointer;
}
</style>