<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-form-item label="排班人员:" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <j-dict-select-tag
            v-model="userId"
            placeholder="请选择用户"
            dictCode="sys_users,realname,id,del_flag=0 and status!=2   order by create_time"
          />
        </a-form-item>
      </a-form>
    </j-form-container>
  </a-spin>
</template>
<script>
import { postAction, getAction } from '@/api/manage'
import { validateDuplicateValue } from '@/utils/util'
import JFormContainer from '@/components/jeecg/JFormContainer'
import JDate from '@/components/jeecg/JDate'
import AInput from 'ant-design-vue/es/input/Input'
import pick from 'lodash.pick'
import JDictSelectTag from '@/components/dict/JDictSelectTag.vue'

export default {
  name: 'DevopsArrangementAllocateForm',
  components: {
    AInput,
    JFormContainer,
    JDate,
    JDictSelectTag,
  },

  props: {
    //流程表单data
    formData: {
      type: Object,
      default: () => {},
      required: false,
    },
    //表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false,
    },
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false,
    },
  },

  data() {
    return {
      replaceFields: { title: 'name' },
      expandedKeys: [],
      backupsExpandedKeys: [],
      autoExpandParent: false,
      checkedKeys: [],
      selectedKeys: [],
      searchValue: '',
      searchStr: '',
      form: this.$form.createForm(this),
      model: {},
      userList: [],

      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      confirmLoading: false,
      validatorRules: {},
      url: '/arrangementinfo/devopsArrangementInfo/batchSetUpArrangement',
      ids: '',
      userId: '',
    }
  },

  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    },
  },
  created() {
    //如果是流程中表单，则需要加载流程表单data
    this.showFlowData()
  },

  mounted: function () {},
  methods: {
    add() {
      this.edit({})
    },
    edit(ids) {
      this.ids = ids
      this.userId = ''
    },
    //渲染流程表单数据
    showFlowData() {
      if (this.formBpm === true) {
        let params = { id: this.formData.dataId }
        getAction(this.url.queryById, params).then((res) => {
          if (res.success) {
            this.edit(res.result)
          }
        })
      }
    },
    submitForm() {
      var ids = this.ids
      var userId = this.userId
      if (userId == '') {
        this.$message.warning('请选择排班人员！')
      }

      let param = {
        ids: this.ids, // ip
        userId: this.userId,
      }
      postAction(this.url, param).then((res) => {
        if (res.success) {
          this.$message.success(res.message)
          this.$emit('ok')
        } else {
          this.$message.console.error()
          res.message
        }
      })
    },
    popupCallback(row) {
      this.form.setFieldsValue(
        pick(
          row,
          'orderCategoryId',
          'warningId',
          'confirmUserId',
          'orderState',
          'wamingCreateTime',
          'remarks',
          'handlerUserId',
          'allocTime',
          'responseSecond',
          'handlerResults',
          'handleEndTime',
          'handleSecond',
          'createBy',
          'createTime',
          'updateBy',
          'updateTime',
          'sysOrgCode'
        )
      )
    },
  },
}
</script>
