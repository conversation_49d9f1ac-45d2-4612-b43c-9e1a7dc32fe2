<template>
  <j-modal
    title="新增入库单"
    :width="width"
    :centered='true'
    :visible="visible"
    :destroyOnClose='true'
    @ok="handleOk"
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }"
    @cancel="handleCancel"
    cancelText="关闭"
  >
    <itil-in-depot-form ref="realForm" @ok="submitCallback" :disabled="disableSubmit"></itil-in-depot-form>
  </j-modal>
</template>

<script>
import ItilInDepotForm from './ItilInDepotForm'
export default {
  name: 'ItilInDepotModal',
  components: {
    ItilInDepotForm,
  },
  data() {
    return {
      title: '',
      width: 1100,
      visible: false,
      disableSubmit: false,
    }
  },
  methods: {
    add() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.add()
      })
    },
    edit(record) {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.edit(record)
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
      this.$refs.realForm.form.resetFields()
      this.$refs.realForm.coreTable = []
    },
    handleOk() {
      this.$refs.realForm.submitForm()
    },
    submitCallback() {
      this.$emit('ok')
      this.visible = false
    },
    handleCancel() {
      this.close()
    },
  },
}
</script>
<style lang="less" scoped>
@import '~@assets/less/limitModalWidth.less';
</style>