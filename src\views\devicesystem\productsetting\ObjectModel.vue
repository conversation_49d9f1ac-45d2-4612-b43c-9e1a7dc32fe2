<template>
  <a-tabs type='card' :tab-bar-gutter='10' :animated='false' :active-key='defaultActiveKey' default-active-key='1'   @change='callback'>
    <a-tab-pane key='1' tab='属性定义'>
      <proerty-metadata-list   ref='proertyMetadataList'></proerty-metadata-list>
    </a-tab-pane>
    <!--    <a-tab-pane key="2" tab="事件定义">
      事件定义
    </a-tab-pane> -->
    <!--    <a-tab-pane key="3" tab="Tab 3">
      Content of Tab Pane 3
    </a-tab-pane> -->
    <a-tab-pane key='4' tab='功能定义'>
      <function-definition  ref='functionDefinition'></function-definition>
    </a-tab-pane>
  </a-tabs>
</template>

<script>
import ProertyMetadataList from './ProertyMetadataList'
import FunctionDefinition from './FunctionDefinition'

export default {
  name: 'ObjectModel',
  components: {
    ProertyMetadataList,
    FunctionDefinition
  },
  data() {
    return {
      defaultActiveKey: '1',
      record: {}
    }
  },
  methods: {
    callback(key) {
      let that=this
      that.defaultActiveKey = key
      setTimeout(
        function() {
          if (key == '1') {
            that.$refs.proertyMetadataList.show(that.record)
          } else if (key == '4') {
            that.$refs.functionDefinition.show(that.record)
          }
        }, 100)
    },
    show(record) {
      let that=this
      that.record = record
      that.defaultActiveKey = '1'
      that.$nextTick(()=>{
        if (that.$refs.proertyMetadataList) {
          that.$refs.proertyMetadataList.show(record)
        }
        if (that.$refs.functionDefinition) {
          that.$refs.functionDefinition.show(record)
        }
      })
    }
  }
}
</script>