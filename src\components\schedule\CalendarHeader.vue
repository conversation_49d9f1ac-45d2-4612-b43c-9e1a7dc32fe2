<template>
  <div class="calendar-head">
    <div>
      <a-button-group>
        <a-button @click="changeTime('reduce')"
          >
          <a-icon type="left" />
          </a-button>
        <a-button @click="changeTime('add')"
          >
          <a-icon type="right" /></a-button>
      </a-button-group>
      <a-button 
      type="primary"
      style="margin-left:12px"
      :disabled="showToday"
      @click="changeTime('today')"
          >今天</a-button>
    </div>
    <div >
      <span class="time-text" v-if="timeData.timeType === 'month'">{{
        timeData.year + "年 " + timeData.month + "月"
      }}</span>
      <span class="time-text" v-else-if="timeData.timeType === 'week'">{{
      weekDayStr
      }}</span>
       <span class="time-text" v-else-if="timeData.timeType === 'day'">{{
      dateTitleStr
      }}</span>
    </div>
   <div>
<!--      <a-button-group>
        <a-button
          @click="changeTimeType('month')"
          :type="timeData.timeType === 'month' ? 'primary' : ''"
          >月</a-button
        >
        <a-button
        
          @click="changeTimeType('week')"
          :type="timeData.timeType === 'week' ? 'primary' : ''"
          >周</a-button
        >
        <a-button
          @click="changeTimeType('day')"
          :type="timeData.timeType === 'day' ? 'primary' : ''"
          >日</a-button
        >
      </a-button-group>-->
    </div>
  </div>
</template>

<script>
export default {
  name: "calendarHeadar",
  props: {
    timeData: {
      type: Object,
      default: () => {
        return {
          year: 0,
          month: 0,
          timeType: "month",
        };
      },
    },
    showToday: {
      type: Boolean,
      default: false,
    },
    weekDayStr:{
      type:String,
      default:""
    },
    dateTitleStr:{
       type:String,
      default:""
    }
  },
  data() {
    return {
      showAccout: false,
      selectAccoutId: "",
      selectColor: "",
    };
  },
  
  components: {
    // checkPopverInput: () => import('./checkPopverInput')
  },
  mounted() {},
  methods: {
    changeTime(a) {
      this.$emit("changeTime", a);
    },
    changeTimeType(t) {
      this.$emit("changeTimeType", t);
    },
    addEventShow(t) {
      this.$emit("addEventShow", t);
    },
    accountChange(items) {
      console.log(items);
    },
    colorChange(items) {
      console.log(items);
    },
  },
};
</script>
<style lang="less" scoped>
.calendar-head {
  align-items: center;
  margin: 0 12px;
  display: flex;
  justify-content: space-between;
}
.time-text{
  font-size: 20px;
  font-weight: bold;
}
</style>


