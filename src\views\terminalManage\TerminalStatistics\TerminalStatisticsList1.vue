<template>
  <a-row :gutter='10' style='height: 100%;' class='vScroll'>
    <a-col style='width:100%;height: 100%;display: flex;flex-direction: column'>
      <a-card
        :bordered='false'
        :bodyStyle="{paddingBottom:'0'}"
        class='card-style'>
      <!-- 查询区域 -->
      <div class="table-page-search-wrapper">
        <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
          <a-row :gutter="24" ref="row">
            <!-- <a-col :span='spanValue'>
              <a-form-item label="选择状态">
                <a-select  placeholder="请输入终端名称" v-model="queryParam.deviceType">
                  <a-select-option :key="0" value="开机">开机</a-select-option>
                  <a-select-option :key="1" value="关机">关机</a-select-option>
                </a-select>
              </a-form-item>
            </a-col> -->
            <a-col :span='spanValue'>
              <a-form-item label="终端名称">
                <!-- <a-input :maxLength='maxLength' placeholder="请输入搜索关键词" v-model="queryParam.title"></a-input> -->
                <a-input :maxLength='maxLength' placeholder="请输入终端名称" v-model="queryParam.name"></a-input>
              </a-form-item>
            </a-col>

            <!-- <a-col :span='spanValue'>
              <a-form-item label="单位">
                <a-tree-select
                  v-model="searchKey"
                  tree-node-filter-prop="title"
                  :replaceFields="replaceFields"
                  :treeData="selectOption"
                  show-search
                  :searchValue='bsearchKey'
                  multiple
                  :maxTagCount='1'
                  :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                  placeholder="请选择单位"
                  allow-clear
                  @change="onChangeTree"
                  @search="onSearch"
                  @select="onSelect"
                >
                </a-tree-select>
              </a-form-item>
            </a-col> -->

            <!-- <a-col :span='spanValue'>
              <a-form-item label="使用人">
                <a-input :maxLength='maxLength' placeholder="请输入使用人" v-model="queryParam.username" ></a-input>
              </a-form-item>
            </a-col>
            <a-col :span='spanValue'>
              <a-form-item label="所属网关">
                <a-input :maxLength='maxLength' placeholder="请输入网关" v-model="queryParam.gatewayCode"></a-input>
              </a-form-item>
            </a-col> -->

            <a-col :span='spanValue'>
              <a-form-item label="选择日期">
                <div>
                  <a-range-picker class='a-range-picker-choice-date'
                    :ranges="{
                      昨天: [moment().startOf('day').subtract(1, 'days'), moment().endOf('day').subtract(1, 'days')],
                      '7天': [
                        moment().startOf('day').subtract(1, 'weeks'),
                        moment().startOf('day').subtract(1, 'days'),
                      ],
                      '30天': [
                        moment().startOf('day').subtract(30, 'days'),
                        moment().startOf('day').subtract(1, 'days'),
                      ],
                    }"
                    @change="onChangePicker"
                    :dropdownClassName="'yq-picker'"
                    :disabledDate="disabledDate"
                    v-model="queryParam.createTimeRange"
                  />
                </div>
              </a-form-item>
            </a-col>
            <a-col :span='colBtnsSpan()'>
             <span class='table-page-search-submitButtons'
                   :style="toRight && { float: 'right', overflow: 'hidden' } || {} ">
              <a-button type="primary" class='btn-search btn-search-style' @click="dosearch">查询</a-button>
              <a-button class='btn-reset btn-reset-style' @click="searchReset">重置</a-button>
               <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>

      <a-card :bordered='false' style='width:100%;flex: auto'>
      <!-- 操作按钮区域 -->
          <div class='table-operator table-operator-style'>
        <a-button @click="handleExportXls('分段统计')">导出</a-button>

        <!-- <a-dropdown v-if="selectedRowKeys.length > 0">
          <a-menu slot="overlay" style='text-align: center'>
            <a-menu-item key="1" @click="batchDel">
             删除
            </a-menu-item>
          </a-menu>
          <a-button> 批量操作
            <a-icon type="down" />
          </a-button>
        </a-dropdown> -->
      </div>
      <!-- table区域-begin -->

        <a-table
          ref="table"
          bordered
          :rowKey="(record,index)=>{return index}"
          :columns="columns"
          :dataSource="dataSource"
          :scroll='dataSource.length > 0 ? { x:"max-content"} : {}'
          :pagination="ipagination"
          :loading="loading"
          @change="handleTableChange"
        >
          <template slot="htmlSlot" slot-scope="text">
            <div v-html="text"></div>
          </template>
          <template slot="onTotal" slot-scope="text">
            <span v-if="text" style="font-size: 14px">
              {{ (parseInt(text) / 60).toFixed(2) }}
            </span>
          </template>

          <template slot="onTotal1" slot-scope="text">
            <span v-if="text" style="font-size: 14px"> {{ ((parseInt(text) / 60 / 24) * 100).toFixed(2) }} % </span>
          </template>

          <template slot="fileSlot" slot-scope="text">
            <span v-if="!text" style="font-size: 14px">无文件</span>
            <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
              下载
            </a-button>
          </template>
          <!-- <span
            slot="action"
            slot-scope="text, record"
            class="caozuo"
          >
            <a @click="handleDetailPage(record)">查看</a>
            <a-divider type="vertical" />
            <a @click="handleEdit(record)">编辑</a>
            <a-divider type="vertical" />
            <a-popconfirm
              title="确定删除吗?"
              @confirm="() => handleDelete(record.id)"
            >
              <a>删除</a>
            </a-popconfirm>
          </span> -->
          <template slot='tooltip' slot-scope='text'>
            <a-tooltip placement='topLeft' :title='text' trigger='hover'>
              <div class='tooltip'>
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
      </a-card>
  </a-col>
  </a-row>
</template>

<script>
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import moment from 'moment'
import '@/assets/less/TableExpand.less'
import JSuperQuery from '@/components/jeecg/JSuperQuery.vue'
import JDictSelectTag from '@/components/dict/JDictSelectTag.vue'
import { getAction } from '@/api/manage'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'

export default {
  name: 'TerminalStatisticsList',
  mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
  components: {
    JSuperQuery,
    JDictSelectTag,
  },
  data() {
    return {
      maxLength:50,
      formItemLayout: {
        labelCol: {
          style: 'width:90px'
        },
        wrapperCol: {
          style: 'width:calc(100% - 90px)'
        }
      },
      searchKey: undefined,
      bsearchKey: '',
      replaceFields: {
        children: 'children',
        title: 'deptName',
        key: 'deptId',
        value: 'deptName',
      },
      selectOption: [],
      description: '终端统计',
      // 表头
      columns: [
        {
          title: '终端名称',
          dataIndex: 'name',
        },
        {
          title: '终端IP',
          dataIndex: 'ip',
        },
        {
          title: 'SN',
          dataIndex: 'sn',
        },
        {
          title: 'MAC地址',
          dataIndex: 'mac',
        },
        {
          title: '单位',
          dataIndex: 'momgDeptName',
          scopedSlots: { customRender: 'tooltip' }
        },

        {
          title: '终端类型',
          dataIndex: 'deviceType1',
        },

        {
          title: '使用人',
          dataIndex: 'username',
        },
        {
          title: '在线总时长(h)',
          dataIndex: 'onTotal',
          scopedSlots: { customRender: 'onTotal' },
        },
      ],
      url: {
        list: '/device/statis/timeInterval',
        exportXlsUrl: '/device/statis/timeIntervalExcel',
        // delete: '/knowledge/delete',
      },
      dictOptions: {},
      //日期范围
      dateFormat: 'YYYY/MM/DD',
      monthFormat: 'YYYY/MM',
    }
  },
  created() {
    this.select()
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
  },
  mounted() {
  },
  methods: {
    onChangeTree(value) {
      this.value = value
    },
    onSearch(e) {
      this.bsearchKey = e
    },
    onSelect() {
    },
    select() {
      getAction('/sys/sysDepart/queryAllTree').then((res) => {
        for (let i = 0; i < res.length; i++) {
          let temp = res[i]
          this.selectOption.push(temp)
        }
      })
    },
    //表单查询,点击查询按钮，默认查询第一页
    dosearch() {
      if (!this.searchKey && this.bsearchKey) {
        this.searchKey = this.bsearchKey
      }
      if (Array.isArray(this.searchKey)) {
        this.queryParam.momgDeptName = this.searchKey.join(',')
      } else if (typeof this.searchKey === 'string') {
        this.queryParam.momgDeptName = this.searchKey
        // this.searchKey = ""
      }
      this.loadData(1)
    },
    onChangePicker(value, dateString) {
      this.queryParam.startTime = dateString[0]
      this.queryParam.endTime = dateString[1]
    },
    initDictConfig() {},
    moment,
    //限制日期不可选
    disabledDate(current) {
      // return current && current > moment().subtract(1, "days"); //不包括当天
      return current < moment().subtract(31, 'days') || current > moment().subtract(1, 'days')
    },
  },
}
</script>
<style lang="less" scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';

.gutter-example {
  background-color: #ececec;
  margin-bottom: 10px;
}

.gutter-row {
  padding-right: 0px !important;
}

.gutter-example ::v-deep .ant-row > div {
  background: transparent;
  border: 0;
}

.gutter-box {
  background: white;
  padding: 5px 0;
}

.p-device-status {
  text-align: center;
  height: 30px;
  line-height: 30px;
  margin-bottom: 0px;
}

.span-title {
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
}

.span-num {
  font-family: PingFangSC-Medium;
  font-size: 24px;
}

.color-blue {
  color: #409eff;
}

.color-green {
  color: #139b33;
}

.color-red {
  color: #df1a1a;
}

.color-grey {
  color: #868686;
}

/*给table列设置宽度*/
::v-deep .ant-table-scroll .ant-table-thead > tr > th,
::v-deep .ant-table-scroll .ant-table-tbody > tr > td {
  /*终端名称*/

  &:nth-child(1) {
    min-width: 130px;
    max-width: 300px;
  }

  /*IP*/

  &:nth-child(2) {
    min-width: 130px;
    max-width: 300px;
  }

  /*SN*/

  &:nth-child(3) {
    min-width: 160px;
    max-width: 300px;
  }

  /*MAC地址*/

  &:nth-child(4) {
    min-width: 150px;
    max-width: 320px;
  }

  /*单位*/

  &:nth-child(5) {
    min-width: 150px;
    max-width: 350px;
  }

  /*终端类型*/

  &:nth-child(6) {
    min-width: 100px;
    max-width: 250px;
  }

  /*使用人*/

  &:nth-child(7) {
    min-width: 100px;
    max-width: 220px;
  }

  /*当天总时长*/

  &:nth-child(8) {
    min-width: 100px;
    max-width: 300px;
  }
}

/*内容对齐方式、省略显示*/
::v-deep .ant-table-tbody > tr > td {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;

  &:nth-child(-n+4), &:nth-child(6), &:nth-child(7){
    text-align: center;
  }

  &:nth-child(5){
    text-align: left;
  }
  &:last-child{
    text-align: right;
  }
}

/*表头样式*/
::v-deep .ant-table-thead > tr > th {
  text-align: center;
  white-space: nowrap;
}

</style>
