<template>
  <div style="height:100%">
      <component :is="pageName" :data="data"/>
  </div>
</template>
<script>
  import DevopsBackupResourceList from './DevopsBackupResourceList'
  import DevopsBackupDetailsList from './modules/DevopsBackupDetailsList'
  export default {
    name: "DevopsBackupProManage",
    data() {
      return {
        isActive: 0,
        data:{}
      };
    },
    components: {
      DevopsBackupResourceList,
      DevopsBackupDetailsList
    },
    created(){
      this.pButton1(0);
    },
    //使用计算属性
    computed: {
      pageName() {
        switch (this.isActive) {
          case 0:
            return "DevopsBackupResourceList";
            break;

          default:
            return "DevopsBackupDetailsList";
            break;
        }
      }
    },
    methods: {
      pButton1(index) {
        this.isActive = index;
      },
      pButton2(index,item) {
        this.isActive = index;
        this.data = item;
      }
    }
  }
</script>