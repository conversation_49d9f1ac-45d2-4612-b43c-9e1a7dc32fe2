<template>
  <div style="height:100%;">
    <component
      :is="pageName"
      style="height:100%"
      :data="data"
    />
  </div>
</template>
<script>
import TerminalStatisticsList1 from './TerminalStatisticsList1'
// import TerminalDetails from './modules/TerminalDetails'
export default {
  name: "TerminalManage",
  data() {
    return {
      isActive: 0,
      data: {}
    };
  },
  components: {
    TerminalStatisticsList1,
    // TerminalDetails
  },
  created() {
    this.pButton1(0);
  },
  //使用计算属性
  computed: {
    pageName() {
      switch (this.isActive) {
        case 0:
          return "TerminalStatisticsList1";
          break;

        default:
          return "TerminalDetails";
          break;
      }
    }
  },
  methods: {
    pButton1(index) {
      this.isActive = index;
    },
    pButton2(index, item) {
      this.isActive = index;
      this.data = item;
    }
  }
}
</script>