<template>
  <j-modal
    :title='title'
    :width='width'
    :visible='visible'
    :destroyOnClose='true'
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    :centered='true'
    switch-fullscreen
    @ok='handleOk'
    @cancel='handleCancel'
    cancelText='关闭'>
    <a-spin :spinning='confirmLoading'>
      <j-form-container :disabled='disableSubmit'>
        <a-form-model ref='form' slot='detail' :model='model' :rules='validatorRules' :labelCol='labelCol'
          :wrapperCol='wrapperCol'>
          <a-row>
            <a-col :span='24'>
              <a-form-model-item label='项目名称' prop='projectName'>
                <a-input style='width: 100%' v-model='model.projectName' :allow-clear='true' autocomplete='off'
                  placeholder='请输入项目名称' />
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
              <a-form-model-item ref='users'  label='发起人'  prop='sender'>
                <!--  通过部门选择用户控件 -->
                <j-select-user-by-dep v-model='model.sender' :multi='false'></j-select-user-by-dep>
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
              <a-form-model-item label='评估时间' prop='evaluateTime'>
                <a-date-picker
                  format="YYYY-MM-DD"
                  v-model='model.evaluateTime'
                  :allow-clear='true'
                  autocomplete='off'
                  placeholder='请选择评估时间'
                @change="onChangeDate"/>
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </j-form-container>
    </a-spin>
  </j-modal>
</template>

<script>
  import { httpAction} from '@api/manage'
  import JSelectUserByDep from '@comp/jeecgbiz/JSelectUserByDep.vue'
  import {
    ValidateRequiredFields,
    ValidateOptionalFields
  } from '@/utils/rules.js'
  export default {
    name: 'materialsModal',
    components: { JSelectUserByDep },
    data() {
      return {
        title: '',
        width: '800px',
        visible: false,
        disableSubmit: false,
        confirmLoading: false,
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          }
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          }
        },
        evaluateTime:'',
        model: {},
        validatorRules: {
          projectName: [
            { required: true,validator: (rule, value, callback) =>ValidateRequiredFields(rule, value, callback,'项目名称',50,1)}
          ],
          sender: [
            { required: true, message: '请输入发起人'}
          ],
          evaluateTime: [
            {required: true,message: '请选择发起时间'}
          ]
        },
        url: {
          add: '/evaluate/projectInfo/add',
          edit: '/evaluate/projectInfo/edit'
        }
      }
    },
    methods: {
      onChangeDate(date, dateString) {
        this.evaluateTime = dateString
      },
      add() {
        this.edit({})
      },
      edit(record) {
        this.visible = true
        this.model=Object.assign({}, record)
        this.evaluateTime=record.evaluateTime||''
      },
      close() {
        this.confirmLoading = false
        this.visible = false
      },
      handleOk() {
        const that = this
        that.$refs.form.validate((err, value) => {
          if (err&&!that.confirmLoading) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!that.model.id) {
              httpurl += that.url.add
              method = 'post'
            } else {
              httpurl += that.url.edit
              method = 'put'
            }
            let formData = {
              ...that.model
            }
            formData.evaluateTime = that.evaluateTime
            httpAction(httpurl, formData, method)
              .then((res) => {
                if (res.success) {
                  that.$message.success(res.message)
                  that.$emit('ok')
                  that.close()
                } else {
                  that.$message.warning(res.message)
                }
                that.confirmLoading = false
              }).catch((res) => {
                that.$message.warning(res.message)
                that.confirmLoading = false
              })
          }
        })
      },
      submitCallback() {
        this.$emit('ok')
        this.visible = false
      },
      handleCancel() {
        this.close()
      }
    }
  }
</script>
<style scoped lang='less'>
  @import '~@assets/less/normalModal.less';
</style>