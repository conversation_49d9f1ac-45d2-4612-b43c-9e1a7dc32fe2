<template>
  <div style='width: 100%'>
    <a-select
      v-model='curLogAnalyzeId'
      style="width: 100%"
      show-search
      :placeholder="placeholder"
      option-filter-prop="children"
      :optionLabelProp='"label"'
      :filter-option="filterOption"
      @change="handleChange"
    >
      <div slot="dropdownRender" slot-scope="menu" v-if='showAdd'>
        <v-nodes :vnodes="menu" />
        <a-divider style="margin: 4px 0;" />
        <div
          class='add'
          @mousedown="e => e.preventDefault()"
          @click.stop="add"
        >
          <a-icon type="plus" />新增
        </div>
      </div>
      <a-select-option v-for='(item,index) in sourceList' :key='item.id' :value='item.id' :label='item.dataViewName'>
        <div class='selected-row'>
          <div class='selected-label' style='' :title='item.dataViewName'>{{item.dataViewName}}</div>
          <div v-if='showDelete||showEdit'>
            <span class='edit' title='编辑' v-if='showEdit' @click.stop='edit(item)'><a-icon type='edit'/></span>
            <span class='delete' title='删除' v-if='showDelete' @click.stop='deleteSource(item.id)'><a-icon type='delete'/></span>
          </div>
        </div>
      </a-select-option>
    </a-select>

    <!-- 日志来源新增、编辑组件-->
    <log-source-modal ref='logSourceModal' @ok='loadSource'></log-source-modal>
  </div>
</template>
<script>
import { getAction ,deleteAction} from '@api/manage'
import logSourceModal from '@views/eventManagement/logInfo/modules/logSourceModal.vue'
export default {
  props:{
    placeholder:{
      type:String,
      required:false,
      default:'请选择'
    },
    dataUrl:{
      type:String,
      required: true
    },
    showAdd:{
      type:Boolean,
      required:false,
      default: true
    },
    showEdit:{
      type:Boolean,
      required:false,
      default: true
    },
    showDelete:{
      type:Boolean,
      required:false,
      default: true
    },
    deleteUrl:{
      type:String,
      required: false,
      default:''
    },
  },
  components: {
    logSourceModal,
    VNodes: {
      functional: true,
      render: (h, ctx) => ctx.props.vnodes,
    },
  },
  data(){
    return{
      curLogAnalyzeId:undefined,
      sourceList:[],
      editedLogAnalyzeId:'',//新增时为空，编辑时有值
      deletedLogAnalyzeId:''
    }
  },
  created() {
    this.getDataSource()
  },
  methods: {
    /**
     * 加载日志来源下拉数据，
     * 根据日志数据是否为空，及当前操作情况，自动匹配当前日志选中情况，并向父级发送事件，重新加载可用字段和列表信息*/
    getDataSource() {
        getAction(this.dataUrl).then((res)=>{
          if (res.success){
            this.sourceList=res.result
            //console.log('更新日志来源下拉数据this.sourceList===',this.sourceList)
            //开始数据不为空、新增、编辑
            if (this.sourceList.length>0){
              //开始未选中日志来源，或者新增第一条数据后，自动匹配选中第一条
              if (!this.curLogAnalyzeId){
                this.curLogAnalyzeId=this.sourceList[0].id
                // console.log('开始未选或者新增第一条数据，this.curLogAnalyzeId为空时===',this.curLogAnalyzeId)
                this.$emit('change',this.sourceList[0])
              }
              //删除、新增、编辑
              else{
                //新增、删除
               if (!this.editedLogAnalyzeId){
                  //删除
                  if (this.deletedLogAnalyzeId) {
                    //删除当前选中的日志来源
                    if (this.deletedLogAnalyzeId === this.curLogAnalyzeId) {
                      this.curLogAnalyzeId = this.sourceList[0].id
                      this.$emit('change', this.sourceList[0])
                      this.deletedLogAnalyzeId=''
                    }
                  }
                  //新增,自动匹配选中最后一条
                  else {
                    let last=this.sourceList[this.sourceList.length-1]
                    this.curLogAnalyzeId=last.id
                    this.$emit('change',last)
                  }
               }
               //编辑
               else {
                 this.curLogAnalyzeId=this.editedLogAnalyzeId
                 this.handleChange(this.curLogAnalyzeId)
                  // 额外的编辑方法（需要刷新loadQueryTags）
                 this.$emit('editChange')
               }
                this.editedLogAnalyzeId=''
              }
            }
            //开始或者删除后数据为空
            else{
              this.deletedLogAnalyzeId=''
              this.editedLogAnalyzeId=''
              this.curLogAnalyzeId=undefined
              this.$emit('change',null)
            }
          }else {
            this.$message.warning(res.message)
          }
        }).catch((err)=>{
          this.$message.warning(err.message)
        })
    },
    /**
     * 切换日志来源，
     * 向父级发送事件，重新加载可用字段和列表信息
     * @value 当前选中日志的id*/
    handleChange(value) {
      //console.log('日志来源发生变化value===',value)
      let curLogAnalyze=null
      if (this.sourceList.length>0){
        curLogAnalyze= this.sourceList.find((item)=>{
          return item.id===value
        })
      }
      //console.log('curLogAnalyze===',curLogAnalyze)
      this.$emit('change',curLogAnalyze)
    },
    handleBlur() {
      console.log('blur');
    },
    handleFocus() {
      console.log('focus');
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      );
    },
    /**根据id，删除日志来源数据*/
    deleteSource(id){
      if(this.showDelete&&this.deleteUrl){
        deleteAction(this.deleteUrl,{id:id}).then((res)=>{
          if (res.success){
            this.deletedLogAnalyzeId=id
            this.getDataSource()
            this.$message.success(res.message)
          }else {
            this.$message.warning(res.message)
          }
        }).catch((err)=>{
          this.$message.warning(err.message)
        })
      }
    },
    add(){
      this.$refs.logSourceModal.title='新增'
      this.$refs.logSourceModal.add()
    },
    edit(record){
      this.$refs.logSourceModal.title='编辑'
      this.$refs.logSourceModal.edit(record)
    },
    /*重新加载数据来源下拉数据*/
    loadSource(id){
      this.editedLogAnalyzeId=id
      this.getDataSource()
    },
  },
};
</script>
<style scoped lang='less'>
.add{
  padding: 4px 8px;
  cursor: pointer;
  text-align: center;
  color:rgba(0, 0, 0, 0.65)
}
.add:hover{
  color:#409eff
}

.selected-row{
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-flow: row nowrap;
  overflow: hidden;

  .selected-label{
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    margin-right: 8px
  }
  .edit{
    margin-right: 8px;
    color:rgba(0, 0, 0, 0.65)
  }

  .delete{
    color:rgba(0, 0, 0, 0.65)
  }

  .edit:hover,.delete:hover{
    color:#409eff
  }
}
</style>