<template>
  <a-row style='height: 100%; margin-right: 4px; overflow: hidden; overflow-y: auto'>
    <a-col style='width: 100%; height: 100%; display: flex; flex-direction: column'>
      <!-- 查询区域 -->
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0', marginRight: '12px' }" class='card-style'
        style='width: 100%'>
        <div class='table-page-search-wrapper'>
          <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
            <a-row :gutter='24' ref='row'>
              <a-col :span='spanValue'>
                <a-form-item label='设备名称'>
                  <a-input placeholder='请输入设备名称' v-model='queryParam.name' :allowClear='true' autocomplete='off'
                    :maxLength="maxLength" />
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label='设备IP'>
                  <a-input placeholder='请输入设备IP' v-model='queryParam.ip' :allowClear='true' autocomplete='off'
                    :maxLength="maxLength" />
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label="设备状态">
                  <a-select v-model='queryParam.status' :allowClear="true" placeholder="请选择设备状态">
                    <a-select-option value="1">启用</a-select-option>
                    <a-select-option value="0">禁用</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span='colBtnsSpan()'>
                <span class='table-page-search-submitButtons'
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button type='primary' class='btn-search btn-search-style' @click='searchQuery'>查询</a-button>
                  <a-button class='btn-reset btn-reset-style' @click='searchReset'>重置</a-button>
                  <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <a-table ref='table' bordered :row-key='(record, index) => {return record.id}' :columns='columns'
          :dataSource='dataSource' :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}" :pagination='ipagination'
          :loading='loading' @change='handleTableChange'>
          <template slot='tooltip' slot-scope='text'>
            <a-tooltip placement='topLeft' :title='text' trigger='hover'>
              <div class='tooltip'>
                {{ text }}
              </div>
            </a-tooltip>
          </template>
          <template slot="deviceName" slot-scope="text,record">
            <span style='color: #409eff;cursor: pointer' @click='handleViewDeviceInfo(record)'>{{text}}</span>
          </template>
          <template slot='status' slot-scope='text'>
            <span> {{ text == 0 ? '禁用' : '启用' }}</span>
          </template>
        </a-table>
      </a-card>
    </a-col>
  </a-row>
</template>

<script>
  import '@/assets/less/TableExpand.less'
  import {
    mixinDevice
  } from '@/utils/mixin'
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import {
    httpAction,
    getAction,
    postAction,
    deleteAction
  } from '@/api/manage'
  import JDictSelectTag from '@/components/dict/JDictSelectTag.vue'
  import {
    YqFormSearchLocation
  } from '@/mixins/YqFormSearchLocation'
  export default {
    name: 'ipAuditList',
    mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
    components: {
      JDictSelectTag,
    },
    data() {
      return {
        maxLength:50,
        description: '网络设备管理页面',
        formItemLayout: {
          labelCol: {
            style: 'width:80px'
          },
          wrapperCol: {
            style: 'width:calc(100% - 80px)'
          }
        },
        // 表头
        columns: [{
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            isUsed: false,
            customCell: () => {
              let cellStyle = 'text-align:center;width:60px'
              return {
                style: cellStyle
              }
            },
            customRender: function (t, r, index) {
              return parseInt(index) + 1
            },
          }, {
            title: '设备名称',
            dataIndex: 'name',
            scopedSlots: {
              customRender: 'deviceName'
            },
            customCell: () => {
              let cellStyle = 'text-align: left;min-width: 100px;max-width:300px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '设备IP',
            dataIndex: 'ip',
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 100px;max-width:300px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '设备类型',
            dataIndex: 'categoryName',
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 100px;max-width:300px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '设备状态',
            dataIndex: 'status',
            scopedSlots: {
              customRender: 'status'
            },
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 80px;max-width:300px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: 'CPU使用率(%)',
            dataIndex: 'cpuRate',
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 80px;max-width:300px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '内存使用率(%)',
            dataIndex: 'memRate',
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 100px;max-width:300px'
              return {
                style: cellStyle
              }
            }
          },
        ],
        queryParam: {
          dictCode: 'Network'
        },
        url: {
          list: '/devops/ip/network/getDeviceByCategoryDict',
          deviceInfo: '/device/deviceInfo/queryById', //通过设备id，查找设备信息
        },
      }
    },
    created() {},
    mounted() {},
    methods: {
      searchReset() {
        this.queryParam = {}
        this.queryParam.dictCode = 'Network'
        this.loadData(1)
      },
      /**查看设备信息*/
      handleViewDeviceInfo(record) {
        getAction(this.url.deviceInfo, {
          id: record.id
        }).then((res) => {
          if (res.success&&res.result&&res.result.records&&res.result.records.length>0) {
            this.$parent.pButton2(1, res.result.records[0])
          }
        })
      },
    }
  }
</script>
<style lang='less' scoped>
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';

  .stateBox {
    margin-left: 20px;
  }

  .stateImg {
    vertical-align: middle;
  }

  .alarmStatus {
    margin-left: 8px;
  }

  .overlay {
    color: #409eff
  }
</style>