<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll zxw">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <!-- 查询区域 -->
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class="card-style">
        <!-- 标题 -->
        <!-- 查询区域 -->
        <div class="table-page-search-wrapper">
          <!-- 搜索区域 -->
          <a-form layout="inline" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="时间范围">
                  <a-select
                    placeholder="请选择"
                    :getPopupContainer="(node) => node.parentNode"
                    v-model="queryParam.timeRange"
                    :allowClear="true"
                  >
                    <a-select-option value="1">最近一天</a-select-option>
                    <a-select-option value="3">最近三天</a-select-option>
                    <a-select-option value="7">最近七天</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="问题类型">
                  <a-select
                    placeholder="请选择"
                    :getPopupContainer="(node) => node.parentNode"
                    v-model="queryParam.questionType"
                    :allowClear="true"
                  >
                    <a-select-option v-for="(item, index) in IssueType" :key="index" :value="item.value">
                      {{ item.text }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="关键字搜索">
                  <a-input
                    :allowClear="true"
                    autocomplete="off"
                    placeholder="请输入关键字"
                    v-model="queryParam.queryKeywords"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="colBtnsSpan()">
                <span
                  class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                >
                  <!-- 按钮区域 -->
                  <a-button type="primary" style="margin-left: 8px" @click="searchQuery" class="btn-search-style"
                    >查询</a-button
                  >
                  <a-button @click="searchReset" style="margin-left: 10px" class="btn-reset-style">重置</a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered="false" style="width: 100%; flex: auto">
        <!-- table区域 -->
        <div>
          <a-table
            ref="table"
            bordered
            rowKey="id"
            :columns="columns"
            :dataSource="dataSource"
            :pagination="ipagination"
            :loading="loading"
            @change="handleTableChange"
          >
            <span slot="status">
              <div>待处理</div>
            </span>
            <span
              slot="action"
              slot-scope="text, record"
              style="display: inline-block; white-space: nowrap; text-align: center"
            >
              <a style="color: #409eff" @click="handleDispose(record)">处理</a>
              <a-divider type="vertical" />
              <a style="color: #409eff" @click="handleAllot(record)">请求分配</a>
            </span>
            <template slot="tooltip" slot-scope="text">
              <a-tooltip placement="topLeft" :title="text" trigger="hover">
                <div class="tooltip">
                  {{ text }}
                </div>
              </a-tooltip>
            </template>
          </a-table>
        </div>
      </a-card>
      <DisposeWorkOrderDispose ref="DisposeWorkOrderDispose" @ok="modalFormOk"></DisposeWorkOrderDispose>
      <DisposeWorkOrderAllot ref="DisposeWorkOrderAllot" @ok="modalFormOk"></DisposeWorkOrderAllot>
    </a-col>
  </a-row>
</template>
<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import DisposeWorkOrderDispose from './modules/DisposeWorkOrderDispose'
import DisposeWorkOrderAllot from './modules/DisposeWorkOrderAllot'
import { ajaxGetDictItems } from '@api/api'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'

export default {
  name: 'DisposeWorkOrder',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components: {
    DisposeWorkOrderDispose,
    DisposeWorkOrderAllot,
  },
  data() {
    return {
      // 表头
      columns: [
        {
          title: '序号',
          dataIndex: '',
          width: 40,
          key: 'rowIndex',
          customRender: function (t, r, index) {
            return parseInt(index) + 1
          },
        },
        {
          title: '问题描述',
          dataIndex: 'question',
          width: 350,
        },
        {
          title: '问题类型',
          dataIndex: 'questionTypeText',
        },
        {
          title: '提问人',
          dataIndex: 'quizzer',
        },
        {
          title: '提问时间',
          dataIndex: 'createTime',
          sorter: true,
        },
        {
          title: '联系电话',
          dataIndex: 'contact',
        },
        {
          title: '分配人',
          dataIndex: 'confirmor',
        },
        {
          title: '分配时间',
          dataIndex: 'confirmTime',
        },
        {
          title: '备注',
          dataIndex: 'remark',
        },
        {
          title: '操作',
          align: 'center',
          fixed: 'right',
          width: 150,
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' },
        },
      ],
      url: {
        list: '/question/question/listPending',
      },
      IssueType: [],
    }
  },
  mounted() {
    this.getDicData()
  },
  methods: {
    handleChange(value) {},
    //处理
    handleDispose(record) {
      this.$refs.DisposeWorkOrderDispose.edit(record)
      this.$refs.DisposeWorkOrderDispose.title = '工单处理'
      this.$refs.DisposeWorkOrderDispose.disableSubmit = false
    },
    //请求分配
    handleAllot(record) {
      this.$refs.DisposeWorkOrderAllot.edit(record)
      this.$refs.DisposeWorkOrderAllot.title = '请求分配'
      this.$refs.DisposeWorkOrderAllot.disableSubmit = false
    },
    //字典获取工单状态&&问题类型选择框内容--------------------------------------------
    getDicData() {
      this.getIssueType('helpQuestionType')
    },

    getIssueType(code) {
      let that = this
      ajaxGetDictItems(code, null).then((res) => {
        let temp = res.result
        for (let i = 0; i < temp.length; i++) {
          if (temp[i].text == '全部') {
            that.IssueType.push({ text: temp[i].text, value: '' })
          } else {
            that.IssueType.push({ text: temp[i].text, value: temp[i].text })
          }
        }
      })
    },
    //字典获取工单状态&&问题类型选择框内容-----------------------------------------end
  },
}
</script>
<style lang='less' scoped>
@import '~@assets/less/scroll.less';
body {
  height: auto;
  overflow: hidden !important;
}
.title {
  text-align: center;
  font-weight: 700;
  font-style: normal;
  font-size: 23px;
  margin-bottom: 34px;
  margin-top: 10px;
}
.table-header span {
  margin-bottom: 16px;
  margin-right: 20px;
  display: inline-block;
  color: red;
}
.table-operator .ant-btn {
  padding: 0 32px !important;
}

/*表头样式*/
::v-deep .ant-table-thead > tr > th {
  text-align: center;
  white-space: nowrap;
}

/*内容对齐方式、省略显示*/
::v-deep .ant-table-tbody > tr > td {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;

  &:first-child,
  &:nth-child(2),
  &:nth-child(3),
  &:nth-child(4),
  &:nth-child(5),
  &:nth-child(6),
  &:nth-child(7),
  &:nth-child(8),
  &:nth-child(9) {
    text-align: center;
  }
}
</style>
