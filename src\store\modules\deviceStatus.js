import Vue from 'vue'
const deviceStatus = {
  state: {
    dataList: [],
    alarmList: [],//设备告警状态
    readyState: 3,// websocket 的连接状态 0 正在连接 1 连接 2 正在关闭 3 关闭
  },
  mutations: {
    // 设备在线离线转台管理
    CHANGE_WEBSOCKET_STATUS: (state, status) => {
      state.readyState = status;
    },
    ADD_STATUS_DATA_LIST: (state, record) => {
      state.dataList.push(record)
    },
    DEL_STATUS_DATA_LIST: (state, record) => {
      state.dataList = state.dataList.filter(ele => ele.deviceId !== record.deviceId)
    },
    CLEAR_STATUS_LIST: (state) => {
      state.dataList = [];
    },
    // 设备告警状态管理
    CLEAR_ALARM_LIST: (state) => {
      state.alarmList = []
    },
    ADD_ALARM_LIST: (state, record) => {
      state.alarmList.push(record)
    },
    DEL_ALARM_LIST: (state, record) => {
      state.alarmList = state.alarmList.filter(ele => ele.deviceId !== record.deviceId)
    },
    UPDATE_ALARM_LIST: (state, params) => {
      // console.log("更新告警列表 === ", params)
      if (params.item) {
        state.alarmList.splice(params.index, 1, params.item)
      }
      else {
        state.alarmList.splice(params.index, 1)
      }
    },


  },
  actions: {
    // 设备在线离线转台管理
    addStatusDataList({ commit }, record) {
      commit('ADD_STATUS_DATA_LIST', record)
    },
    delStatusDataList({ commit }, record) {
      commit('DEL_STATUS_DATA_LIST', record)
    },
    // 设备告警状态管理
    addAlarmList({ commit }, record) {
      commit('ADD_ALARM_LIST', record)
    },
    delAlarmList({ commit }, record) {
      commit('DEL_ALARM_LIST', record)
    },
    updateAlarmList({ commit }, params) {
      commit('UPDATE_ALARM_LIST', params)
    },
  }
}
export default deviceStatus