<template>
    <div style="height: 100%;" :bodyStyle="{ padding: 0 }">
        <a-page-header :title="info.projectName" :ghost="false">
            <template slot="extra">
                <span>
                    <img src="~@/assets/return1.png" alt="" @click="getGo"
                        style="width: 20px; height: 20px; cursor: pointer" />
                </span>
            </template>
            <a-descriptions size="small" :column="6">
                <a-descriptions-item label="发起人">
                    {{ info.sender_dictText }}
                </a-descriptions-item>
                <a-descriptions-item label="评估时间">
                    {{ info.evaluateTime }}
                </a-descriptions-item>
            </a-descriptions>
        </a-page-header>
        <a-row style='height: calc(100% - 120px);margin-top: 16px;'>
            <a-col :xl='4' :lg='6' :md='8' :sm='10' :xs='10' style='height: 100%; background: #fff;'>
                <!--  -->
                <metrics-tree :inputFlag='false' @selected='treeSeletedSearch' :tree-url="'/evaluate/metricsType/tree'"
                    :btnIconName="'appstore'" :is-show-all-btn='false' :btnName="'全部指标'" :fieldKey='"id"'
                    :is-show-btn-icon='true' :is-show-icon='false' :params="{ projectId: info.id }">
                </metrics-tree>
            </a-col>
            <a-col :xl='20' :lg='18' :md='16' :sm='14' :xs='14'
                style='height: 100%; overflow: hidden; overflow-y: auto'>
                <div style=" padding: 0 16px; ">
                    <a-list v-if="evaluateList.length" :data-source="evaluateList">
                        <a-list-item slot="renderItem" slot-scope="item, index">
                            <a-card :title="item.materialName" style="width: 100%;">
                                <a-descriptions size="small" :column="4">
                                    <a-descriptions-item label="类型">
                                        {{ item.materialType }}
                                    </a-descriptions-item>
                                    <a-descriptions-item label="关联指标">
                                        {{ item.metricsId_dictText }}
                                    </a-descriptions-item>
                                    <a-descriptions-item label="创建人">
                                        {{ item.createBy_dictText }}
                                    </a-descriptions-item>
                                    <a-descriptions-item label="创建时间">
                                        {{ item.createTime }}
                                    </a-descriptions-item>
                                    <a-descriptions-item label="附件数量">
                                        <a-dropdown>
                                            <a>{{ item.attachmentNum }}</a>
                                            <a-menu slot="overlay">
                                                <a-menu-item v-for="(record, index) in item.attachmentPath"
                                                    :key="index">
                                                    <a @click="downAttachment(record)">{{ record.fileName}}</a>
                                                </a-menu-item>
                                            </a-menu>
                                        </a-dropdown>
                                    </a-descriptions-item>
                                    <a-descriptions-item label="说明">
                                        {{ item.materialDesc }}
                                    </a-descriptions-item>
                                </a-descriptions>
                            </a-card>
                        </a-list-item>
                    </a-list>
                    <a-empty v-else description="暂无佐证材料"></a-empty>
                </div>
            </a-col>
        </a-row>
    </div>
</template>
<script>
import { getAction } from '@/api/manage'
import MetricsTree from './MetricsTree.vue'
import { downloadFile } from '@/api/manage'
export default {
    name: 'OperationalOrganizationDetail',
    components: {
        MetricsTree
    },
    props: {
        data: {
            type: Object,
            default: () => { return }
        }
    },
    data() {
        return {
            width: '70%',
            visible: false,
            disableSubmit: false,
            info: {},
            evaluateList: [],
            activeMetricsId: "",
        }
    },
    created() {
        this.info = this.data.record;
    },
    computed: {

    },
    methods: {
        getGo() {
            this.$parent.pButton2(0)
        },
        treeSeletedSearch(id = '') {
            // console.log('选中指标ID:', id);
            this.activeMetricsId = id
            this.getMaterialInfo();
            // this.loadData(1)
        },
        //获取材料信息
        getMaterialInfo() {
            getAction('/evaluate/materialInfo/pageList', { projectId: this.info.id, metricsId: this.activeMetricsId })
                .then(res => {
                    if (res.code === 200 && res.result) {
                        this.evaluateList = res.result.records || [];
                        this.evaluateList.forEach(item => {
                            if (item.attachmentPath) {
                                item.attachmentPath = JSON.parse(item.attachmentPath);
                                item.attachmentNum = item.attachmentPath.length;
                            }
                        });
                    } else {
                        this.$message.error(res.message);
                    }
                }).catch(err => {
                    this.$message.error('获取材料信息失败');
                });
        },
        // 下载附件材料
        downAttachment(record) {
            //  console.log('查看评估报告', record)
            downloadFile(window._CONFIG['downloadUrl']+"/"+record.filePath, record.fileName)
                .then(res => {
                }).catch(err => {
                    this.$message.error('下载失败')
                })
        },
    }
}
</script>
<style scoped lang="less">

</style>