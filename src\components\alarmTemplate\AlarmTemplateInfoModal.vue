<template>
  <div class='scroll'>
    <div style="position: absolute; right: 24px; top: 18px; z-index: 10">
      <img src="~@assets/return1.png" alt="" @click="getGo()"
        style="width: 20px; height: 20px; cursor: pointer" />
    </div>
    <alarm-template-info ref="realForm"></alarm-template-info>
  </div>
</template>

<script>
  import AlarmTemplateInfo from './AlarmTemplateInfo'
  export default {
    inject: ['reload'],
    name: 'alarmTemplateInfoModal',
    data() {
      return {}
    },
    components: {
      AlarmTemplateInfo,
    },
    props: {
      deviceInfo: {
        type: Object,
        required: false,
        default: null
      },
      alarmInfo: {
        type: Object,
        required: true,
        default: null
      },
      showFlag: {
        type: Boolean,
        required: false,
        default: true
      },
    },
    watch: {
      alarmInfo: function (val) {
        this.$refs.realForm.show(val, this.deviceInfo, this.showFlag)
      },
    },
    mounted() {
      this.$refs.realForm.show(this.alarmInfo, this.deviceInfo, this.showFlag)
    },
    methods: {
      //返回上一级
      getGo() {
        this.$parent.pButton1(0)
      },
    },
  }
</script>

<style scoped>
.scroll{
  position: relative;
  height: 100%;
  overflow: hidden;
  overflow-y: auto;
}
</style>