<template>
  <a-card style="height: 100%; overflow: hidden">
    <a-row>
      <a-col :span="24">
        <span style="float: right; margin-bottom: 12px"><img src="~@/assets/return1.png" alt="" @click="getGo"
            style="width: 20px; height: 20px; cursor: pointer" /></span>
      </a-col>
      <a-col :span="24" style="overflow-x: auto">
        <table class="gridtable">
          <tr>
            <td class="leftTd">软件名称</td>
            <td class="rightTd">{{ data.softwareName }}</td>
            <td class="leftTd">软件版本</td>
            <td class="rightTd">{{ data.softwareVersion }}</td>
          </tr>
          <tr>
            <td class="leftTd">操作系统</td>
            <td class="rightTd">{{ getDictName(data.softwareOs)  }}</td>
            <td class="leftTd">cpu架构</td>
            <td class="rightTd">{{ data.softwareCpu }}</td>
          </tr>
          <tr>
            <td class="leftTd">软件文件</td>
            <td class="rightTd">{{ data.softwareFile }}</td>
            <td class="leftTd">描述</td>
            <td class="rightTd">{{ data.softwareDescribe }}</td>
          </tr>
        </table>
      </a-col>
    </a-row>
    <drive-software-modal ref="modalForm" @ok="modalFormOk">
    </drive-software-modal>
  </a-card>
</template>

<script>
import driveSoftwareModal from './driveSoftwareModal'
import {
    ajaxGetDictItems
  } from '@/api/api'
import {
  httpAction,
  getAction
} from '@/api/manage'
export default {
  name: 'driveSoftwareDetails',
  props: {
    data: {
      type: Object,
    },
  },
  components: {
    driveSoftwareModal,
  },
  data() {
    return {
      form: this.$form.createForm(this),
      model: {},
      labelCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 5
        },
      },
      wrapperCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 16
        },
      },
      url: {
        queryById: ' /software/softwareRepository/queryById',
      },
       dictOptions: [],
        cpuList: [],
    }
  },
  created() {
    this.initDictData()
  },
  mounted() { },
  methods: {
    initDictData() {
      //根据字典Code, 初始化字典数组
      ajaxGetDictItems('cpuArch', null).then((res) => {
        if (res.success) {
          this.cpuList = res.result
        }
      })
      ajaxGetDictItems('os_type', null).then((res) => {
        if (res.success) {
          this.dictOptions = res.result
        }
      })
    },
    getDictName(value) {
      let dictName = ''
      this.dictOptions.forEach((res, i) => {
        if (this.dictOptions[i].value == value) {
          dictName = this.dictOptions[i].text
        }
      })
      return dictName
    },
    modalFormOk() {
      let params = {
        id: this.data.id
      }
      getAction(this.url.queryById, params).then((res) => {
        if (res.success) {
          this.data = res.result
        }
      })
    },
    //详情编辑
    handleDetailEdit: function (record) {
      this.$refs.modalForm.edit(record)
      this.$refs.modalForm.title = '编辑'
      this.$refs.modalForm.disableSubmit = false
    },
    //返回上一级
    getGo() {
      this.$parent.pButton2(0)
    },
  },
}
</script>
<style lang="less" scoped>
::v-deep .two-words>div>label {
  letter-spacing: 4px;
}

::v-deep .two-words>div>label::after {
  letter-spacing: 0px;
}

table.gridtable {
  font-family: verdana, arial, sans-serif;
  font-size: 14px;
  color: #606266;
  border-width: 1px;
  border-color: #e8e8e8;
  border-collapse: collapse;
  text-align: left;
  width: 100%;
}

table.gridtable td {
  border-width: 1px;
  border-style: solid;
  border-color: #e8e8e8;
  white-space: nowrap;
}

.leftTd {
  width: 17%;
  background-color: #fafafa;
  padding: 16px 24px;
  text-align: center;
}

.rightTd {
  width: 35%;
  padding: 16px 24px;
}
</style>