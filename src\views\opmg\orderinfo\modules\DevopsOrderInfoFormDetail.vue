<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-row>

          <a-col :span="12">
            <a-form-item label="描述" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['orderDescription']" placeholder="请输入描述内容"  ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="故障类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['orderCategoryId']" placeholder="请输入故障类型(资源类型)"  ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
          <a-form-item label="确认人" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['confirmUserId']" placeholder="请输入确认人姓名"  ></a-input>
          </a-form-item>
          </a-col>
          <a-col :span="12">
          <a-form-item label="工单状态" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input-number v-decorator="['orderState']" placeholder="请输入工单状态(0:待分配,1:待处理,2已处理:)" style="width: 100%" />
          </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="故障触发时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date placeholder="请选择故障触发时间" v-decorator="['wamingCreateTime']" :trigger-change="true" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
          <a-form-item label="创建日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <j-date placeholder="请选择创建日期" v-decorator="['createTime']" :trigger-change="true" style="width: 100%" />
          </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="备注" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['remarks']" placeholder="请输入备注"  ></a-input>
            </a-form-item>
          </a-col>
          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>

    <a-tabs :animated="false" defaultActiveKey="1">
      <a-tab-pane tab="历史" key="1">
        <Operate-History-Info-Form ref="OperateHistoryInfoForm"></Operate-History-Info-Form>
      </a-tab-pane>
    </a-tabs>

  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import pick from 'lodash.pick'
  import { validateDuplicateValue } from '@/utils/util'
  import JFormContainer from '@/components/jeecg/JFormContainer'
  import JDate from '@/components/jeecg/JDate'
  import OperateHistoryInfoForm from '@/views/opmg/operatehistory/modules/OperateHistoryInfoForm'

  export default {
    name: 'DevopsOrderInfoDetailForm',
    components: {
      JFormContainer,
      JDate,
      OperateHistoryInfoForm
    },
    props: {
      //流程表单data
      formData: {
        type: Object,
        default: ()=>{},
        required: false
      },
      //表单模式：true流程表单 false普通表单
      formBpm: {
        type: Boolean,
        default: false,
        required: false
      },
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        form: this.$form.createForm(this),
        model: {},
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        validatorRules: {
        },
        url: {
          add: "/orderinfo/devopsOrderInfo/add",
          edit: "/orderinfo/devopsOrderInfo/edit",
          queryById: "/orderinfo/devopsOrderInfo/queryById"
        }
      }
    },
    computed: {
      formDisabled(){
        if(this.formBpm===true){
          if(this.formData.disabled===false){
            return false
          }
          return true
        }
        return this.disabled
      },
      showFlowSubmitButton(){
        if(this.formBpm===true){
          if(this.formData.disabled===false){
            return true
          }
        }
        return false
      }
    },
    created () {
      //如果是流程中表单，则需要加载流程表单data
      this.showFlowData();
    },
    methods: {
      add () {
        this.edit({});
      },
      edit (record) {
        this.form.resetFields();
        this.model = Object.assign({}, record);
        this.visible = true;
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.model,'orderCategoryId','warningId','confirmUserId','orderState','wamingCreateTime','remarks','handlerUserId','allocTime','responseSecond','handlerResults','handleEndTime','handleSecond','createBy','createTime','updateBy','updateTime','sysOrgCode'))
        })
      },
      //渲染流程表单数据
      showFlowData(){
        if(this.formBpm === true){
          let params = {id:this.formData.dataId};
          getAction(this.url.queryById,params).then((res)=>{
            if(res.success){
              this.edit (res.result);
            }
          });
        }
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
              method = 'put';
            }
            let formData = Object.assign(this.model, values);
            httpAction(httpurl,formData,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }

        })
      },
      popupCallback(row){
        this.form.setFieldsValue(pick(row,'orderCategoryId','warningId','confirmUserId','orderState','wamingCreateTime','remarks','handlerUserId','allocTime','responseSecond','handlerResults','handleEndTime','handleSecond','createBy','createTime','updateBy','updateTime','sysOrgCode'))
      },
    }
  }
</script>