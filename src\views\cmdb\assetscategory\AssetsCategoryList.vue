<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <!-- 操作按钮区域 -->
      <a-card :bordered="false" style="width: 100%; flex: auto">
        <div class="table-operator table-operator-style">
          <a-button @click="handleAdd">新增</a-button>
          <a-button @click="handleExportXls('产品分类')">导出</a-button>
          <a-button @click="handleTemplateXls()">下载模版</a-button>
          <a-upload
            name="file"
            :showUploadList="false"
            :multiple="false"
            :headers="tokenHeader"
            :action="importExcelUrl"
            @change="handleImportExcel"
          >
            <a-button>导入</a-button>
          </a-upload>
          <a-dropdown v-if="selectedRowKeys.length > 0">
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item key="1" @click="batchDel">
                删除
              </a-menu-item>
            </a-menu>
            <a-button>
              批量操作
              <a-icon type="down" />
            </a-button>
          </a-dropdown>
        </div>
        <!-- table区域-begin -->
        <a-table
          ref="table"
          rowKey="id"
          bordered
          :columns="columns"
          :dataSource="dataSource"
          :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
          :pagination="ipagination"
          :loading="loading"
          :expandedRowKeys="expandedRowKeys"
          @change="handleTableChange"
          @expand="handleExpand"
          v-bind="tableProps"
        >
          <span class="caozuo" slot="action" slot-scope="text, record">
            <a @click="handleEdit(record)">编辑</a>
            <a-divider type="vertical" />
            <a @click="handleAddChild(record)">新增下级</a>
            <a-divider type="vertical" />
            <a-popconfirm title="确定删除吗?" @confirm="() => handleDeleteNode(record.id)" placement="topLeft">
              <a>删除</a>
            </a-popconfirm>
          </span>
           <template slot="categoryName" slot-scope="text">
           <span>{{text}}</span>
          </template>
          <template slot="tooltip" slot-scope="text">
            <a-tooltip placement="topLeft" :title="text" trigger="hover">
              <div class="tooltip">
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
      </a-card>
      <assetsCategory-modal ref="modalForm" @ok="modalFormOk"></assetsCategory-modal>
      <!-- 下载模版 -->
      <iframe id="download" style="display: none"></iframe>
    </a-col>
  </a-row>
</template>

<script>
import { getAction, deleteAction } from '@api/manage'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import AssetsCategoryModal from './modules/AssetsCategoryModal'
import { filterMultiDictText } from '@comp/dict/JDictSelectUtil'
import { filterObj } from '@/utils/util'
import JSuperQuery from '@comp/jeecg/JSuperQuery.vue'

export default {
  name: 'AssetsCategoryList',
  mixins: [JeecgListMixin],
  components: {
    AssetsCategoryModal,
    JSuperQuery,
  },
  data() {
    return {
      description: '资产类型管理页面',
      // 表头
      columns: [
        {
          title: '类型名称',
          dataIndex: 'categoryName' ,
          scopedSlots: { customRender: 'categoryName' },
          customCell: () => {
            let cellStyle = 'text-align: left'
            return { style: cellStyle }
          },
        },
        {
          title: '类型编号',
          dataIndex: 'categoryCode'
        },
        {
          title: '描述',
          dataIndex: 'categoryDescribe',
          scopedSlots: { customRender: 'tooltip' },
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 100px;max-width:400px'
            return { style: cellStyle }
          },
        },
        {
          title: '操作',
          dataIndex: 'action',
          fixed: 'right',
          width: 180,
          align: 'center',
          scopedSlots: { customRender: 'action' },
        },
      ],
      url: {
        list: '/assetscategory/assetsCategory/rootList',
        childList: '/assetscategory/assetsCategory/childList',
        getChildListBatch: '/assetscategory/assetsCategory/getChildListBatch',
        delete: '/assetscategory/assetsCategory/delete',
        deleteBatch: '/assetscategory/assetsCategory/deleteBatch',
        exportXlsUrl: '/assetscategory/assetsCategory/exportXls',
        importExcelUrl: 'assetscategory/assetsCategory/importExcel',
        downloadTemplateXlsUrl: '/assetscategory/assetsCategory/downloadTemplate',
      },
      expandedRowKeys: [],
      hasChildrenField: 'hasChild',
      pidField: 'parentId',
      dictOptions: {},
      loadParent: false,
      superFieldList: [],
    }
  },
  created() {
    this.getSuperFieldList()
  },
  computed: {
    importExcelUrl() {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
    downloadTemplateXlsUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.downloadTemplateXlsUrl}`
    },
    tableProps() {
      let _this = this
      return {
        // 列表项是否可选择
        rowSelection: {
          selectedRowKeys: _this.selectedRowKeys,
          onChange: (selectedRowKeys) => (_this.selectedRowKeys = selectedRowKeys),
        },
      }
    },
  },
  methods: {
    //excel模板
    handleTemplateXls() {
      const path = this.downloadTemplateXlsUrl
      document.getElementById('download').src = path
    },
    loadData(arg) {
      if (arg == 1) {
        this.ipagination.current = 1
      }
      this.loading = true
      let params = this.getQueryParams()
      params.hasQuery = 'true'
      getAction(this.url.list, params)
        .then((res) => {
          if (res.success) {
            let result = res.result
            if (Number(result.total) > 0) {
              this.ipagination.total = Number(result.total)
              this.dataSource = this.getDataByResult(res.result.records)
              return this.loadDataByExpandedRows(this.dataSource)
            } else {
              this.ipagination.total = 0
              this.dataSource = []
            }
          } else {
            this.$message.warning(res.message)
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 根据已展开的行查询数据（用于保存后刷新时异步加载子级的数据）
    loadDataByExpandedRows(dataList) {
      if (this.expandedRowKeys.length > 0) {
        return getAction(this.url.getChildListBatch, { parentIds: this.expandedRowKeys.join(',') }).then((res) => {
          if (res.success && res.result.records.length > 0) {
            //已展开的数据批量子节点
            let records = res.result.records
            const listMap = new Map()
            for (let item of records) {
              let pid = item[this.pidField]
              if (this.expandedRowKeys.join(',').includes(pid)) {
                let mapList = listMap.get(pid)
                if (mapList == null) {
                  mapList = []
                }
                mapList.push(item)
                listMap.set(pid, mapList)
              }
            }
            let childrenMap = listMap
            let fn = (list) => {
              if (list) {
                list.forEach((data) => {
                  if (this.expandedRowKeys.includes(data.id)) {
                    data.children = this.getDataByResult(childrenMap.get(data.id))
                    fn(data.children)
                  }
                })
              }
            }
            fn(dataList)
          }
        })
      } else {
        return Promise.resolve()
      }
    },
    getQueryParams(arg) {
      //获取查询条件
      let sqp = {}
      let param = {}
      if (this.superQueryParams) {
        sqp['superQueryParams'] = encodeURI(this.superQueryParams)
        sqp['superQueryMatchType'] = this.superQueryMatchType
      }
      if (arg) {
        param = Object.assign(sqp, this.isorter, this.filters)
      } else {
        param = Object.assign(sqp, this.queryParam, this.isorter, this.filters)
      }
      if (JSON.stringify(this.queryParam) === '{}' || arg) {
        param.hasQuery = 'false'
      } else {
        param.hasQuery = 'true'
      }
      param.field = this.getQueryField()
      param.pageNo = this.ipagination.current
      param.pageSize = this.ipagination.pageSize
      return filterObj(param)
    },
    searchReset() {
      //重置
      this.expandedRowKeys = []
      this.queryParam = {}
      this.loadData(1)
    },
    getDataByResult(result) {
      if (result) {
        return result.map((item) => {
          //判断是否标记了带有子节点
          if (item[this.hasChildrenField] == '1') {
            let loadChild = { id: item.id + '_loadChild', name: 'loading...', isLoading: true }
            item.children = [loadChild]
          }
          return item
        })
      }
    },
    handleExpand(expanded, record) {
      // 判断是否是展开状态
      if (expanded) {
        this.expandedRowKeys.push(record.id)
        if (record.children.length > 0 && record.children[0].isLoading === true) {
          let params = this.getQueryParams(1) //查询条件
          params[this.pidField] = record.id
          params.hasQuery = 'false'
          params.superQueryParams = ''
          getAction(this.url.childList, params).then((res) => {
            if (res.success) {
              if (res.result.records) {
                record.children = this.getDataByResult(res.result.records)
                this.dataSource = [...this.dataSource]
              } else {
                record.children = ''
                record.hasChildrenField = '0'
              }
            } else {
              this.$message.warning(res.message)
            }
          })
        }
      } else {
        let keyIndex = this.expandedRowKeys.indexOf(record.id)
        if (keyIndex >= 0) {
          this.expandedRowKeys.splice(keyIndex, 1)
        }
      }
    },
    handleAddChild(record) {
      this.loadParent = true
      let obj = {}
      obj[this.pidField] = record['id']
      this.$refs.modalForm.add(obj)
    },
    handleDeleteNode(id) {
      if (!this.url.delete) {
        this.$message.error('请设置url.delete属性!')
        return
      }
      var that = this
      deleteAction(that.url.delete, { id: id }).then((res) => {
        if (res.success) {
          this.searchReset()
        } else {
          that.$message.warning(res.message)
        }
      })
    },
    /*    batchDel() {
      if (this.selectedRowKeys.length <= 0) {
        this.$message.warning('请选择一条记录！')
        return false
      } else {
        let ids = ''
        let that = this
        that.selectedRowKeys.forEach(function (val) {
          ids += val + ','
        })
        that.$confirm({
          title: '确认删除',
          okText: '是',
          cancelText: '否',
          content: '是否删除选中数据?',
          onOk: function () {
            that.handleDeleteNode(ids)
            that.onClearSelected()
          },
        })
      }
    },*/
    getSuperFieldList() {
      let fieldList = []
      fieldList.push({ type: 'string', value: 'categoryName', text: '类型名称', dictCode: '' })
      fieldList.push({ type: 'string', value: 'categoryCode', text: '类型编号', dictCode: '' })
      fieldList.push({ type: 'string', value: 'categoryDescribe', text: '描述', dictCode: '' })
      fieldList.push({ type: 'string', value: 'categorySerial', text: '序号', dictCode: '' })
      fieldList.push({ type: 'string', value: 'categoryState', text: '状态（0启用;1未启用）', dictCode: '' })
      fieldList.push({ type: 'int', value: 'isMonitorable', text: '状态（0不监控;1监控）', dictCode: '' })
      fieldList.push({ type: 'string', value: 'parentId', text: '父级节点', dictCode: '' })
      this.superFieldList = fieldList
    },
  },
}
</script>
<style lang='less' scoped>
.pro-icon{
  display: inline-block;
  padding:3px;
  margin-right: 5px;
  background: #F0F0F0;
  img{
    width:20px;height:20px
  }
}
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
</style>
