<template>
  <a-row style='height: 100%'>
    <a-col style='height: 100%; display: flex; flex-direction: column'>
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0', marginRight: '12px' }" class='card-style'>

    <!-- 查询区域 -->
    <div class='table-page-search-wrapper'>
      <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
        <a-row :gutter='24' ref='row'>
          <a-col :span="spanValue">
            <a-form-item label="申请单位">
              <a-tree-select
                :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }"
                v-model="queryParam.userUnit"
                tree-node-filter-prop="title"
                :replaceFields="replaceFields"
                :treeData="selectOption"
                style="width: 100%"
                placeholder="请选择单位"
                allow-clear
                @change="onChangeTree"
              >
              </a-tree-select>
            </a-form-item>
          </a-col>
          <a-col   :span="spanValue">
            <a-form-item label="申请人">
              <a-select v-show='showValue' :getPopupContainer='node=>node.parentNode' v-model="queryParam.contactUserId" show-search
                        placeholder="请选择申请人" option-filter-prop="children" :filter-option="filterOption"  allow-clear
                        @change='changeUser'
              >

                   <a-select-option v-for="(item, key) in userList" :key="key" :value="item.id">
                  <div style="display: inline-block; width: 100%" :title="item.realname">
                    {{ item.realname }}
                  </div>
                </a-select-option>
              </a-select>
              <a-select v-show='!showValue' :getPopupContainer='node=>node.parentNode' v-model="queryParam.contactUserId" show-search
                        placeholder="请选择申请人" option-filter-prop="children" :filter-option="filterOption"   allow-clear
                        @change='changeUser'
              >
                   <a-select-option v-for="(item, key) in userList" :key="key" :value="item.value">
                  <div style="display: inline-block; width: 100%" :title="item.title">
                    {{ item.title }}
                  </div>
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col v-show="getVisible('name')" :span="spanValue">
            <a-form-item :label="getTitle('name')">
              <a-input v-model="queryParam.processInstanceName" :maxLength='maxLength' :allow-clear="true" autocomplete="off"
                placeholder="请输入业务标题" />
            </a-form-item>
          </a-col>
          <a-col v-show="getVisible('processDefinitionName')" :span="spanValue">
            <a-form-item :label="getTitle('processDefinitionName')">
              <a-input :allow-clear='true' :maxLength='maxLength' autocomplete='off' placeholder='请输入流程定义名称全量内容'
                v-model='queryParam.processDefinitionName' />
            </a-form-item>
          </a-col>
          <a-col v-show="getVisible('processDefinitionKey')" :span="spanValue">
            <a-form-item :label="getTitle('processDefinitionKey')">
              <a-select :getPopupContainer='node=>node.parentNode' v-model="queryParam.processDefinitionKey" show-search
                        placeholder="请选择流程定义编码" option-filter-prop="children" :filter-option="filterOption"   allow-clear
              >
                <a-select-option v-for="(item, key) in processDefinitionKeyList" :key="item" :value="item">
                  <div style="display: inline-block; width: 100%" :title="item">
                    {{ item }}
                  </div>
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <a-col v-show="getVisible('startUserName')" :span="spanValue">
            <a-form-item :label="getTitle('startUserName')">
              <a-select :getPopupContainer='node=>node.parentNode' v-model="queryParam.startUserId" show-search
                placeholder="请选择发起人" option-filter-prop="children" :filter-option="filterOption" allow-clear>
                <a-select-option v-for="(item, key) in starUsers" :key="key"  :value="item.username">
                  <div style="display: inline-block; width: 100%" :title="item.realname">
                    {{ item.realname }}
                    <span style="font-size: 12px; color: rgba(0, 0, 0, 0.45);">{{
                    '(' + item.username + ')'
                  }}</span>
                  </div>
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col v-show="getVisible('suspended')" :span='spanValue'>
            <a-form-item :label="getTitle('suspended')">
              <a-select placeholder='请选择状态' :allow-clear='true' :getPopupContainer='(node) => node.parentNode'
                v-model='queryParam.suspended'>
                <a-select-option value='true'>冻结</a-select-option>
                <a-select-option value='false'>解冻</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col v-show="getVisible('startTime')" :span="spanValue">
            <a-form-item :label="getTitle('startTime')">
              <a-range-picker :getCalendarContainer="node=> node.parentNode" style='width: 100%'
                v-model='queryParam.searchStartTime' :placeholder="['开始时间', '结束时间']" format='YYYY-MM-DD HH:mm:ss'
                showTime @change='onCreatedTimeChange' />
            </a-form-item>
          </a-col>
          <a-col v-show="getVisible('endTime')" :span="spanValue">
            <a-form-item :label="getTitle('endTime')">
              <a-range-picker :getCalendarContainer="node=> node.parentNode" style='width: 100%'
                v-model='queryParam.searchEndTime' :placeholder="['开始时间', '结束时间']" format='YYYY-MM-DD HH:mm:ss' showTime
                @change='onEndTimeChange' />
            </a-form-item>
          </a-col>
          <a-col :span="spanValue">
            <span class="table-page-search-submitButtons" style="overflow: hidden;">
              <a-button icon="search" type="primary" @click="searchQuery">查询</a-button>
              <a-button icon="reload" style="margin-left: 8px" @click="searchReset1">重置</a-button>
              <a v-if="queryItems.length>0" style="margin-left: 8px" @click="doToggleSearch">{{queryName}}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
              </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

        <!--自定义查询项 -->
        <div v-if="toggleSearchStatus" class="custom-query-item">
          <a-checkbox-group v-model="settingQueryItems" :defaultValue="settingQueryItems" style="width:100%"
            @change="onQuerySettingsChange">
            <a-row :gutter="24">
              <template v-for="(item,index) in queryItems">
                <a-col v-show='item.checked' :span='querySpanValue' class='col-checkbox'>
                  <a-checkbox :disabled="item.disabled" :value="item.dataIndex">
                    <j-ellipsis :length="7" :value="item.title"></j-ellipsis>
                  </a-checkbox>
                </a-col>
              </template>
            </a-row>
          </a-checkbox-group>
        </div>
        <!-- 自定义查询项-END -->
      </a-card>

      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <!-- table区域-begin -->
        <div>
          <!-- <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
            <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{
              selectedRowKeys.length }}</a>项
            <a style="margin-left: 24px" @click="onClearSelected">清空</a>
            <span style="float:right;">
              <a @click="loadData()"><a-icon type="sync" />刷新</a>
              <a-divider type="vertical" />
              <a-popover title="自定义列" trigger="click" placement="leftBottom">
                <template slot="content">
                  <a-checkbox-group @change="onColSettingsChange" v-model="settingColumns" :defaultValue="settingColumns">
                    <a-row style="width: 400px">
                      <template v-for="(item,index) in defColumns">
                        <template v-if="item.key!='rowIndex'&& item.dataIndex!='action'">
                            <a-col :span="12"><a-checkbox :value="item.dataIndex"><j-ellipsis :value="item.title" :length="10"></j-ellipsis></a-checkbox></a-col>
                        </template>
                      </template>
                    </a-row>
                  </a-checkbox-group>
                </template>
                <a><a-icon type="setting" />设置</a>
              </a-popover>
            </span>
          </div> -->

          <a-table ref='table' bordered rowKey='id' :columns='columns' :dataSource='dataSource'
            :pagination='ipagination' :loading='loading' @change='handleTableChange'>

            <span slot='suspended' slot-scope='suspended'>
              <div v-if='!suspended'>解冻</div>
              <div v-else>冻结</div>
            </span>
            <div slot='filterDropdown'>
              <a-card>
                <a-checkbox-group @change='onColSettingsChange' v-model='settingColumns' :defaultValue='settingColumns'>
                  <a-row style='width: 400px'>
                    <template v-for='(item,index) in defColumns'>
                      <template v-if="item.key!='rowIndex'&& item.dataIndex!='action'">
                        <a-col :span='12' :key='index'>
                          <a-checkbox :value='item.dataIndex'>
                            <j-ellipsis :value='item.title' :length='10'></j-ellipsis>
                          </a-checkbox>
                        </a-col>
                      </template>
                    </template>
                  </a-row>
                </a-checkbox-group>
              </a-card>
            </div>
            <a-icon slot='filterIcon' type='setting' :style="{ fontSize:'16px',color:  '#108ee9' }" />

            <span slot='action' slot-scope='text, record'>
              <a href='javascript:' @click='handleInstanceInfo(record)'>查看</a>
              <a-divider type='vertical' />
              <a-dropdown>
                <a class='ant-dropdown-link'>更多
                  <a-icon type='down' /></a>
                <a-menu slot='overlay'>
                  <a-menu-item>
                    <a @click='handleInstanceDetail(record)'>任务信息</a>
                  </a-menu-item>
                  <a-menu-item>
                    <a href='javascript:' @click='handleTaskList(record)'>任务列表</a>
                  </a-menu-item>
                  <a-menu-item v-if='!record.suspended&&!record.endTime'>
                    <a-popconfirm title='确定冻结吗?' @confirm='() => handleSuspend(record.id)'>
                      <a>冻结</a>
                    </a-popconfirm>
                  </a-menu-item>
                  <a-menu-item v-if='record.suspended'>
                    <a-popconfirm title='确定解冻吗?' @confirm='() => handleActivate(record.id)'>
                      <a>解冻</a>
                    </a-popconfirm>
                  </a-menu-item>
                  <a-menu-item>
                    <a-popconfirm title='确定删除吗?' @confirm='() => handleDelete(record.id)'>
                      <a>删除</a>
                    </a-popconfirm>
                  </a-menu-item>
                  <a-menu-item>
                    <a-popconfirm title='确定删除吗?' @confirm='() => handleDelete(record.id,true)'>
                      <a>删除包含历史</a>
                    </a-popconfirm>
                  </a-menu-item>
                </a-menu>
              </a-dropdown>
            </span>
          </a-table>
        </div>
        <!-- table区域-end -->
      </a-card>

      <!-- 查看区域 -->
      <process-instancedetail-modal ref='processInstanceDetailModalForm' @ok='modalFormOk'></process-instancedetail-modal>
      <!-- 一查看详情区域 -->
      <process-instance-info-modal ref='processInstanceInfoModalForm' @ok='modalFormOk'></process-instance-info-modal>
    </a-col>
  </a-row>
</template>

<script>
  import {
    getUserList
  } from '@/api/api'
  import JSuperQuery from '@/components/jeecg/JSuperQuery.vue'
  import JInput from '@/components/jeecg/JInput.vue'
  //import JeecgDemoTabsModal from './modules/JeecgDemoTabsModal'
  import {
    initDictOptions
  } from '@/components/dict/JDictSelectUtil'
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import Vue from 'vue'
  import {
    filterObj
  } from '@/utils/util'
  import ProcessInstancedetailModal from './modules/ProcessInstanceDetailModal'
  import ProcessInstanceInfoModal from './modules/ProcessInstanceInfoModal'
  import {
    deleteAction
  } from '@/api/manage'
  import {
    processInstanceActivate,
    processInstanceSuspend
  } from '@/api/flowable'
  import {
    YqFormSeniorSearchLocation
  } from '@/mixins/YqFormSeniorSearchLocation'
  import { getAction } from '../../../api/manage'

  export default {
    name: 'processInstance',
    mixins: [JeecgListMixin, YqFormSeniorSearchLocation],
    components: {
      JSuperQuery,
      JInput,
      ProcessInstancedetailModal,
      ProcessInstanceInfoModal,
    },
    data() {
      return {
        maxLength:50,
        selectOption: [],
        replaceFields: {
          children: 'children',
          title: 'deptName',
          key: 'deptId',
          value: 'deptId',
        },
        formItemLayout: {
          labelCol: {
            style: 'width:105px'
          },
          wrapperCol: {
            style: 'width:calc(100% - 105px)'
          }
        },
        processDefinitionKeyList:[],
        userList: [],
        starUsers:[],//发起人数组
        description: '单表示例列表',
        //字典数组缓存
        sexDictOptions: [],
        importExcelUrl: `${window._CONFIG['domianURL']}/test/jeecgDemo/importExcel`,
        //表头
        // columns: [],
        //列设置
        settingColumns: [],
        procInstId: '', //流程实例ID
        //列定义
        columns: [
          {
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            isUsed: false,
            customCell: () => {
              let cellStyle = 'text-align:center;width:60px'
              return {
                style: cellStyle
              }
            },
            customRender: function (t, r, index) {
              return parseInt(index) + 1
            }
          },
          {
            title: '业务标题',
            dataIndex: 'name',
            isUsed: true,
            customCell: () => {
              let cellStyle = 'text-align:center'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '流程名称',
            dataIndex: 'processDefinitionName',
            isUsed: true,
            customCell: () => {
              let cellStyle = 'text-align:center'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '流程编码',
            dataIndex: 'processDefinitionKey',
            isUsed: true,
            customCell: () => {
              let cellStyle = 'text-align:center'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '流程版本',
            dataIndex: 'processDefinitionVersion',
            isUsed: false,
            customCell: () => {
              let cellStyle = 'text-align:center'
              return {
                style: cellStyle
              }
            },
            customRender: (text) => {
              //字典值替换通用方法
              return 'v' + text
            }
          },
          {
            title: '发起人',
            dataIndex: 'startUserName',
            isUsed: true,
            customCell: () => {
              let cellStyle = 'text-align:center'
              return {
                style: cellStyle
              }
            }
          } ,{
            title: '申请人',
            dataIndex: 'contactUserName',
            isUsed: false,
            customCell: () => {
              let cellStyle = 'text-align:center'
              return {
                style: cellStyle
              }
            }
          },{
            title: '申请单位',
            dataIndex: 'unitName',
            isUsed: false,
            customCell: () => {
              let cellStyle = 'text-align:center'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '状态',
            dataIndex: 'suspended',
            isUsed: false,
            customCell: () => {
              let cellStyle = 'text-align:center'
              return {
                style: cellStyle
              }
            },
            scopedSlots: {
              customRender: 'suspended'
            }
          },
          {
            title: '开始时间',
            dataIndex: 'startTime',
            isUsed: true,
            customCell: () => {
              let cellStyle = 'text-align:center;width:160px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '结束时间',
            dataIndex: 'endTime',
            isUsed: true,
            customCell: () => {
              let cellStyle = 'text-align:center;width:160px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            fixed: 'right',
            isUsed: false,
            width: 150,
            scopedSlots: {
              customRender: 'action'
            }
          }
        ],
        url: {
          list: '/flowable/processInstance/list',
          delete: '/flowable/processInstance/delete',
          processDefinitionKeyList:'/flowable/processDefinition/queryKeyList'
        },
        showValue:true
      }
    },
    created() {
      this.getProcessDefinitionKeyList()
      this.getColumns(this.columns)
      this.getuserList()
      this.selectDepart()
    },
    methods: {
      changeUser() {
        this.$forceUpdate()
      },
      searchReset1() {
        this.getuserList()
        this.searchReset()
      },
      selectDepart() {
        getAction('/sys/sysDepart/queryAllTree').then((res) => {
          for (let i = 0; i < res.length; i++) {
            let temp = res[i]
            this.selectOption.push(temp)
          }
        })
      },
      onChangeTree(value){
        this.queryParam.contactUserId=undefined
        if (value===undefined||value===""||value===null){
          this.getuserList()
          return
        }
        getAction("sys/user/queryList",{"deptId":value}).then(res=>{
           if (res.success&&res.result.length>0){
             this.showValue=false
             this.userList=res.result
           }else {
             this.getuserList()
           }
        })

      },
      onCreatedTimeChange: function (value, dateString) {
        this.queryParam.startedAfter = dateString[0]
        this.queryParam.startedBefore = dateString[1]
      },
      onEndTimeChange: function (value, dateString) {
        this.queryParam.finishedAfter = dateString[0]
        this.queryParam.finishedBefore = dateString[1]
      },
      changeStartUser(e, node) {
        if (!e) {
          this.queryParam.startUserId = undefined
        }
      },
      getQueryParams() {
        //高级查询器
        let sqp = {}
        if (this.superQueryParams) {
          sqp['superQueryParams'] = encodeURI(this.superQueryParams)
          sqp['superQueryMatchType'] = this.superQueryMatchType
        }
        if (this.$route.query && this.$route.query.processDefinitionId) {
          this.queryParam.processDefinitionId = this.$route.query.processDefinitionId
        }
        var param = Object.assign(sqp, this.queryParam, this.isorter, this.filters)
        param.field = this.getQueryField()
        param.pageNo = this.ipagination.current
        param.pageSize = this.ipagination.pageSize
        delete param.birthdayRange //范围参数不传递后台
        return filterObj(param)
      },
      handleTaskList: function (record) {
        this.$router.push({
          path: '/task',
          query: {
            processInstanceId: record.id,
          },
        })
      },
      handleInstanceInfo: function (record) {
        if (!record.id) {
          this.$message.error('流程实例ID不存在')
          return
        }
        this.$refs.processInstanceInfoModalForm.init(record.id)
        this.$refs.processInstanceInfoModalForm.title = '流程实例信息'
        this.$refs.processInstanceInfoModalForm.disableSubmit = false
      },
      handleInstanceDetail: function (record) {
        if (!record.id) {
          this.$message.error('流程实例ID不存在')
          return
        }
        this.$refs.processInstanceDetailModalForm.edit(record)
        this.$refs.processInstanceDetailModalForm.title = '任务信息'
        this.$refs.processInstanceDetailModalForm.disableSubmit = false
      },
      filterOption(input, option) {
        return (
          option.componentOptions.children[0].children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
        )
      },
      getProcessDefinitionKeyList() {
        this.processDefinitionKeyList=[]
        let param = {
          pageSize: -1
        }
        getAction(this.url.processDefinitionKeyList,param).then((res) => {
          if (res.success&&res.result) {
            this.processDefinitionKeyList = res.result
          }else {
            this.processDefinitionKeyList=[]
          }
        }).catch((err)=>{
          this.$message.error(err.message)
          this.processDefinitionKeyList=[]
        })
      },
      getuserList() {
        this.showValue=true
        let param = {
          pageSize: 1000000
        }
        getUserList(param).then((res) => {
          if (res.success) {
            this.starUsers = res.result.records
            this.userList = res.result.records
          }
        })
      },
      initDictConfig() {
        //初始化字典 - 性别
        initDictOptions('sex').then((res) => {
          if (res.success) {
            this.sexDictOptions = res.result
          }
        })
      },
      onetomany: function () {
        this.$refs.jeecgDemoTabsModal.add()
        this.$refs.jeecgDemoTabsModal.title = '编辑'
      },
      //跳转单据页面
      jump() {
        this.$router.push({
          path: '/jeecg/helloworld'
        })
      },
      onBirthdayChange: function (value, dateString) {
        this.queryParam.birthday_begin = dateString[0]
        this.queryParam.birthday_end = dateString[1]
      },
      //列设置更改事件
      onColSettingsChange(checkedValues) {
        var key = this.$route.name + ':colsettings'
        Vue.ls.set(key, checkedValues, 7 * 24 * 60 * 60 * 1000)
        this.settingColumns = checkedValues
        const cols = this.defColumns.filter(item => {
          if (item.key == 'rowIndex' || item.dataIndex == 'action') {
            return true
          }
          if (this.settingColumns.includes(item.dataIndex)) {
            return true
          }
          return false
        })
        this.columns = cols
      },
      initColumns() {
        //权限过滤（列权限控制时打开，修改第二个参数为授权码前缀）
        //this.defColumns = colAuthFilter(this.defColumns,'testdemo:');

        var key = this.$route.name + ':colsettings'
        let colSettings = Vue.ls.get(key)
        if (colSettings == null || colSettings == undefined) {
          let allSettingColumns = []
          this.defColumns.forEach(function (item, i, array) {
            allSettingColumns.push(item.dataIndex)
          })
          this.settingColumns = allSettingColumns
          this.columns = this.defColumns
        } else {
          this.settingColumns = colSettings
          const cols = this.defColumns.filter(item => {
            if (item.key == 'rowIndex' || item.dataIndex == 'action') {
              return true
            }
            if (colSettings.includes(item.dataIndex)) {
              return true
            }
            return false
          })
          this.columns = cols
        }
      },
      handleDelete(id, caseHis) {
        deleteAction(this.url.delete, {
          processInstanceId: id,
          cascade: caseHis
        }).then((res) => {
          if (res.code == 200) {
            this.$message.success(res.message)
          } else {
            this.$message.error('删除失败')
          }

          this.loadData()
        })
      },
      //挂起流程实例
      handleSuspend(id) {
        processInstanceSuspend({
          processInstanceId: id
        }).then((res) => {
          if (res.code == 200) {
            this.$message.success('冻结成功')
          } else {
            this.$message.error('冻结失败')
          }
          this.loadData()
        })
      },
      //激活流程实例
      handleActivate(id) {
        processInstanceActivate({
          processInstanceId: id
        }).then((res) => {
          if (res.code == 200) {
            this.$message.success('解冻成功')
          } else {
            this.$message.error('解冻失败')
          }
          this.loadData()
        })
      }
    },
  }
</script>
<style scoped lang='less'>
  @import '~@assets/less/common.less';
  @import '~@assets/less/YQCommon.less';
</style>