<template>
  <div class="tab-box">
    <a-tabs class="productsetting-tab" :animated="true" :active-key="defaultActiveKey" @change="callback">
      <a-tab-pane key="1" tab="产品信息">
        <base-info ref="basicInfo"></base-info>
      </a-tab-pane>
      <a-tab-pane key="2" tab="物模型">
        <object-model ref="objectModel"></object-model>
      </a-tab-pane>
      <a-tab-pane key="3" tab="连接参数">
        <connect-info ref="connectInfo"></connect-info>
      </a-tab-pane>
      <a-tab-pane key="4" tab="面板管理">
        <panel-manage :obj="obj" ref="panel" v-if="defaultActiveKey === '4'"></panel-manage>
      </a-tab-pane>
      <a-tab-pane key="5" tab="配置管理">
        <config-manage ref="configManage"></config-manage>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script>
import BaseInfo from './BaseInfo'
import ConnectInfo from './ConnectInfo'
import ObjectModel from './ObjectModel'
import PanelManage from './PanelManage'
import ConfigManage from './ConfigManagementList.vue'
export default {
  name: 'productTabs',
  components: {
    BaseInfo,
    ConnectInfo,
    ObjectModel,
    PanelManage,
    ConfigManage,
  },
  data() {
    return {
      record: {},
      defaultActiveKey: '1',
    }
  },
  props: {
    obj: {
      type: Object,
      required:true,
      default:()=>{return {}}
    }
  },
  watch: {
    obj:{
      handler(val){
        this.show(val)
      },
      deep:true,
      immediate:true
    }
  },
  methods: {
    callback(key) {
      let that = this
      that.defaultActiveKey = key
      setTimeout(function () {
        if (key == '1') {
          that.$refs.basicInfo.show(that.record)
        } else if (key == '2') {
          that.$refs.objectModel.show(that.record)
        } else if (key == '3') {
          that.$refs.connectInfo.show(that.record)
        } else if (key == '4') {
          that.$refs.panel.show(that.record)
        } else if (key == '5') {
          that.$refs.configManage.show(that.record)
        }
      }, 100)
    },
    show(obj) {
      let that = this
      that.defaultActiveKey = '1'
      that.record = obj
      //window.localStorage.productsettingId = obj.id
      that.$nextTick(()=>{
        if (that.$refs.basicInfo) {
          that.$refs.basicInfo.show(obj)
        }
        if (that.$refs.objectModel) {
          that.$refs.objectModel.show(obj)
        }
        if (that.$refs.connectInfo) {
          that.$refs.connectInfo.show(obj)
        }
        if (this.$refs.panel) {
          this.$refs.panel.show(obj)
        }
        /* if (this.$refs.imgSetting) { this.$refs.imgSetting.show(this.data) }*/
      })
    },
  },
}
</script>
<style lang="less" scoped>
.tab-box {
  height: 100%;
  .productsetting-tab {
    height: 100%;
  }
}

/deep/ .ant-tabs-content {
  height: calc(100% - 60px);
}
.ant-tabs .ant-tabs-top-content > .ant-tabs-tabpane-active {
  height: 100%;
  overflow-y: auto;
}
</style>