<template>
  <div v-if="desItems.length>0">
    <slot name="title-extension" v-if="showTitle">
      <div class="title-flag-wrapper" :style="{marginTop: titleMarginTop,marginBottom:titleMarginBottom}" >
        <span class="title-flag" :style="{borderLeftColor:titleBorderLeftColor}">{{title}}</span>
      </div>
    </slot>

    <a-descriptions :colon="colon" :column='column' :bordered="bordered" :style="{padding:padding}">
      <a-descriptions-item v-for='(item,index) in desItems' :key="'desItems_'+index" >
        <span slot="label" :style="{letterSpacing:item.label.length<limitedLength?letterSpacing+'px':'0px',display:'inline-block',width:labelWidth}">{{item.label}}</span>
        <div :style="{paddingBottom:paddingBottom}">
          <span v-if="!item.comType" v-html="item.value"></span>
          <span v-else>
          <a-progress
            v-if="item.comType==='progress'"
            style="width: 200px"
            size="small"
            :stroke-color="item.color"
            :percent="item.value"
            status="active" />
            <a
              style="cursor: pointer;color:#4b90de"
              v-else-if="item.comType==='triggerParentMethod'"
              @click='triggerParentMethod(item)'
            >{{item.value}}</a>
        </span>
        </div>
      </a-descriptions-item>
    </a-descriptions>
  </div>
</template>
<script>

export default {
  name: "descriptions",
  props: {
    //是否显示标题
    showTitle: {
      type: Boolean,
      required: false,
      default: true,
    },
    //标题名
    title: {
      type: String,
      required: false,
      default: '基本信息',
    },
    titleBorderLeftColor:{
      type: String,
      required: false,
      default: '#1e3674',
    },
    titleMarginTop:{
      type: String,
      required: false,
      default: '20px',
    },
    titleMarginBottom:{
      type: String,
      required: false,
      default: '10px',
    },
    //数据
    items: {
      type: Array,
      required: true,
      default: () => []
    },
    //是否有边框
    bordered: {
      type: Boolean,
      required: false,
      default: true,
    },
    //内边框属性
    padding:{
      type: String,
      required: false,
      default: '0',
    },
    /*每个子项bottom内边距*/
    paddingBottom:{
      type: String,
      required: false,
      default: '0',
    },
    //字之间的空隙
    letterSpacing:{
      type: Number,
      required: false,
      default: 0,
    },
    //小于该值时，字间隙设置起作用，反之不起作用
    limitedLength:{
      type: Number,
      required: false,
      default: 0,
    },
    //label宽度
    labelWidth:{
      type: String,
      required: false,
      default: 'auto',
    },
    //每行显示的列数
    column:{
      type:Object,
      required:false,
      default:()=>{
        return { xxl: 2, xl: 2, lg: 2, md: 2, sm: 2, xs: 2 }}
    },
    //是否显示冒号
    colon: {
      type: Boolean,
      required: false,
      default: true,
    },
  },
  data() {
    return {
      desItems: [],

    }
  },
  watch: {
    items: {
      handler(val) {
        if (val&&val.length && val.length > 0) {
          this.desItems = val.filter(item => {
            return item.value != null && item.value !== ''
          })
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods:{
    triggerParentMethod(item){
      this.$emit('triggerParentMethod',item)
    }
  }
}
</script>

<style scoped lang="less">
.title-flag-wrapper {
  .title-flag {
    padding-left: 7px;
    font-size: 14px;
    border-left-width: 4px;
    border-left-style: solid;
    color: rgba(0,0,0,0.85);
  }
}

::v-deep .ant-descriptions-view {
  border-radius: 0px;
}

::v-deep .ant-descriptions-item-label {
  color: rgba(0,0,0,.45);
}
.content{
  color: rgba(0,0,0,0.85) !important;
  word-break: break-all;
  white-space: normal;
  min-width:260px;
}
::v-deep .ant-descriptions-item-content {
  .content
}
::v-deep .ant-descriptions-bordered .ant-descriptions-item-label {
  text-align: center;
  color: rgba(0,0,0,.65);
  width: 15%;
}

::v-deep .ant-descriptions-bordered .ant-descriptions-item-content {
  width: 35%;
  .content
}
</style>