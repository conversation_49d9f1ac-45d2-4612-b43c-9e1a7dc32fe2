<template>
    <a-row :gutter='10' style='height: 100%;' class='vScroll'>
      <a-col style='width:100%;height: 100%;display: flex;flex-direction: column'>
<!--        <a-button @click='openCard(1)'></a-button>-->
        <!-- 查询区域 -->
        <a-card
          :bordered='false'
          :bodyStyle="{paddingBottom:'0'}"
          class='card-style'>
          <div class='table-page-search-wrapper'>
            <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
              <a-row :gutter='24' ref='row'>
                <a-col :span='spanValue'>
                  <a-form-item label='系统名称'>
                    <a-input
                      :maxLength='maxLength'
                      :allowClear='true'
                      autocomplete='off'
                      v-model='queryParam.appName'
                      placeholder='请输入系统名称'
                    />
                  </a-form-item>
                </a-col>

                <a-col :span='colBtnsSpan()'>
               <span class='table-page-search-submitButtons'
                     :style="toRight && { float: 'right', overflow: 'hidden' } || {} ">
                  <a-button type='primary' class='btn-search btn-search-style' @click='searchQuery'>查询</a-button>
                  <a-button class='btn-reset btn-reset-style' @click='searchReset'>重置</a-button>
                  <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                   <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
               </span>
                </a-col>
              </a-row>
            </a-form>
          </div>
        </a-card>
        <!-- 查询区域-END -->
        <a-card :bordered='false'
                style='width:100%;flex: auto'>
          <!-- 操作按钮区域 -->
          <div class='table-operator table-operator-style'></div>
          <!-- table区域-begin -->
          <a-table
            ref='table'
            bordered
            rowKey='id'
            :columns='columns'
            :dataSource='dataSource'
            :scroll='dataSource.length > 0 ? { x:"max-content"} : {}'
            :pagination='ipagination'
            :loading='loading'
            @change='handleTableChange'
          >
            <span class='caozuo' slot='action' slot-scope='text, record'>
              <a @click='openCard(record)'>查看</a>
            </span>
            <template slot='tooltip' slot-scope='text'>
              <a-tooltip placement='topLeft' :title='text' trigger='hover'>
                <div class='tooltip'>
                  {{ text }}
                </div>
              </a-tooltip>
            </template>
          </a-table>
        </a-card>
      </a-col>
    </a-row>
    <!--    <div
          v-show="detailDisplay == false"
          style="height: 100%; overflow: hidden; overflow-y: auto"
        >
          <a-card
            :bordered="false"
            style="height: 80px; margin-bottom: 10px"
          >
            &lt;!&ndash; 查询区域 &ndash;&gt;
            <div class="table-page-search-wrapper">
              <a-form
                layout="inline"
                @keyup.enter.native="searchQuery"
              >
                <a-row :gutter="24">
                  <a-col :span="6">
                    <a-form-item label="系统名称">
                      <a-input
                      :maxLength='maxLength'
                        v-model="queryParam.appName"
                        placeholder="请输入系统名称"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="6">
                    <a-button
                      type="primary"
                      @click="searchQuery"
                    >查询</a-button>
                    <a-button
                      @click="searchReset"
                      style="margin-left: 10px"
                    >重置</a-button>
                  </a-col>
                </a-row>
              </a-form>
            </div>
            &lt;!&ndash; 查询区域-END &ndash;&gt;
          </a-card>
          <a-card
            :bordered="false"
            style="height: calc(100% - 90px)"
            class="core"
          >
            &lt;!&ndash; 操作按钮区域 &ndash;&gt;
            <div class="table-operator tableBottom"></div>

            &lt;!&ndash; table区域-begin &ndash;&gt;
            <div>
              <a-table
                ref="table"
                size="middle"
                bordered
                rowKey="id"
                :columns="columns"
                :dataSource="dataSource"
                :pagination="ipagination"
                :loading="loading"
                class="j-table-force-nowrap"
                @change="handleTableChange"
              >
                <span
                  slot="action"
                  slot-scope="text, record"
                  class="caozuo"
                >
                  <a @click="openCard(record)">查看</a>
                </span>
              </a-table>

            </div>
          </a-card>
        </div>-->
</template>

<script>
// import '@/assets/less/TableExpand.less'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { getAction } from '@/api/manage'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'

export default {
  name: 'spplicationSystemMonitoringList',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  data() {
    return {
      maxLength:50,
      formItemLayout: {
        labelCol: {
          style: 'width:90px'
        },
        wrapperCol: {
          style: 'width:calc(100% - 90px)'
        }
      },
      // 表头
      columns: [
        {
          title: '系统名称',
          dataIndex: 'appName',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 100px;max-width:400px'
            return { style: cellStyle }
          },
        },
        {
          title: '使用单位',
          dataIndex: 'deptName',
          scopedSlots: { customRender: 'tooltip' },
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 100px;max-width:400px'
            return { style: cellStyle }
          },
        },
        {
          title: '访问地址',
          dataIndex: 'appUrl',
          scopedSlots: { customRender: 'tooltip' },
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 100px;max-width:400px'
            return { style: cellStyle }
          },
        },
        {
          title: '更新时间',
          dataIndex: 'updated',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 100px;max-width:300px'
            return { style: cellStyle }
          },
        },
        {
          title: '操作',
          dataIndex: 'action',
          fixed: 'right',
          width: 120,
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: '/open/ops/lists'
      },
    }
  },
  mounted() {
  },
  methods: {
    openCard(record) {
      this.$parent.pButton1(1, record.id)
    },
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import "~@assets/less/scroll.less";

.header-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-family: PingFangSC-Medium;
  font-size: 18px;
  color: #000000;
}

.header-oddNumber {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 20px;

  span {
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
  }

  div {
    button {
      margin-bottom: 0;
    }

    button:nth-child(2) {
      margin-right: 0px;
    }
  }
}

.essential-title {
  font-family: PingFangSC-Medium;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.85);
  border-left: 4px solid #1e3674;
  padding-left: 8px;
}

.essentialInformation {
  margin-top: 20px;
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  border: 1px solid #e8e8e8;
  border-bottom: none;

  .essentialInformation-item {
    width: 50%;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #e8e8e8;

    span {
      display: flex;
      align-items: center;
      height: 40px;
      border-right: 1px solid #e8e8e8;
    }

    span:nth-child(1) {
      width: 30%;
      justify-content: center;
      background: #fafafa;
    }

    span:nth-child(2) {
      width: 70%;
      padding-left: 6%;
    }
  }

  div:nth-child(2n + 0) {
    span:nth-child(2) {
      border: none;
    }
  }
}

.monitoring-title {
  font-family: PingFangSC-Medium;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.85);
  border-left: 4px solid #1e3674;
  margin-top: 35px;
  padding-left: 8px;
  margin-bottom: 20px;
}
</style>

