<template>
  <div class="card-title">
    <div class="left-line"></div>
    <div class="text">{{ title }}</div>
    <div class="right-line"></div>
    <div class="underline"></div>
  </div>
</template>

<script>
export default {
  name: 'CardTitle',
  data() {
    return {
     
    }
  },
  props: {
    title: {
      type: String,
      default: ''
    }
  }
}
</script>

<style lang="less" scoped>
.card-title {
  width: 100%;
  height: 34px;
  position: relative;
  display: flex;
  align-items: center;
  padding-left: 23px;
  .text {
    font-family: Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    letter-spacing: 2px;
  }
  .left-line {
    width: 33px;
    height: 2px;
    background: #2de6ff;
    position: absolute;
    bottom: 0;
    left: 23px;
  }
  .underline {
    width: 100%;
    height: 2px;
    background: rgba(45, 230, 255, 0.3);
    opacity: 0.3;
    position: absolute;
    bottom: 0;
    left: 0;
  }
  .right-line {
    width: 15px;
    height: 2px;
    background: #2de6ff;
    position: absolute;
    bottom: 0;
    right: 0;
  }
}
</style>
