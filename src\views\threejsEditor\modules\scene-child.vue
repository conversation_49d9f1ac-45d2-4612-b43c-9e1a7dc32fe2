<template>
  <div>
    <div
      v-for="(item, idx) in sceneChildren"
      :key="idx"
      class="scene-item"
      @click="showChild(item)"
    >
      <a-icon type="code-sandbox-circle" theme="filled" />
      <span style="margin-left: 8px">{{
        item.userData.name || item.name
      }}</span>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      sceneChildren: [],
    };
  },
  created() {},
  mounted() {
    if (scene) {
      this.init();
    }
  },
  methods: {
    init() {
      this.sceneChildren = scene.children.filter((el) => {
        return el.name && el.name.includes("yq_");
      });
    },
    showChild(item) {
      this.$root.$emit("showChild", item);
    },
  },
};
</script>
<style scoped>
.scene-child {
  height:100%;
  /* overflow-y: auto; */
}
.scene-item {
  margin: 16px;
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #8c8cc8;
  cursor: pointer;
}
</style>