<template>
  <a-spin :spinning="loading" class="theme1">
    <card-frame :title="'知识搜索'" :showHeadBgImg="true" :showFooter="true">
      <a-row slot="bodySlot" :gutter="24" style="padding: 0 68px;">
        <!--搜索区域 -->
        <div class="header-wrapper-r">
            <a-form class="header-form">
            <a-input-search v-model="queryParam.keyword" placeholder="请输入关键字" size="large" @search="onSearch"
              style="border:none;">
            </a-input-search>
          </a-form>
        </div>
        <!-- 返回按钮 -->
        <div class='header-back' @click='getGo'>
          <img src="../../../../public/oneClickHelp/back.png" width='28' height='28'>返回
        </div>
        <div class='body-wrapper'>
          <div class="body-info-l">
            <a-tabs :defaultActiveKey="0" size="small" @change="changeTab">
              <a-tab-pane key="0">
                <span slot="tab" style="color:#fff">文&nbsp;&nbsp;本</span>
              </a-tab-pane>
              <a-tab-pane key="1">
                <span slot="tab" style="color:#fff">文&nbsp;&nbsp;档</span>
              </a-tab-pane>
            </a-tabs>
            <div v-if="dataSource.length > 0" class="item-wrapper">
              <!-- 知识内容 -->
              <knowledgeItem ref="knowledgeItem" :kkfileview-url='kkfileviewUrl' :theme="'dark'" :dataSource="dataSource" @goDetail="goDetail" class="dark"></knowledgeItem>

              <!-- 分页 -->
              <div class="pagination">
                <a-pagination
                  size="small"
                  :hideOnSinglePage="true"
                  :defaultPageSize="ipagination.pageSize"
                  :pageSize="ipagination.pageSize"
                  :total="ipagination.total"
                  :current="ipagination.current"
                  show-size-changer
                  show-quick-jumper
                  @change="qPageChange"
                  @showSizeChange="qPageChange"
                  :show-total='(total) => `共 ${ipagination.total} 条`'
                  style="margin-bottom:15px;" />
              </div>
            </div>
            <!-- 暂无数据 -->
            <div v-if="dataSource.length == 0 && loading == false" style="height: 70%;">
              <empty/>
            </div>
          </div>
            <!-- 热门知识 -->
          <div class="body-info-r">
            <hot-knowledge :theme="'theme4'" @goDetail="goDetail" style="margin: -48px 16% 0 20%;">
              <img slot="prefixIcon" style="height: 18px; width: 18px;margin-right:10px" src="/oneClickHelp/localDeviceInfo/head.png">
            </hot-knowledge>
          </div>
        </div>
      </a-row>
    </card-frame>
  </a-spin>
</template>

<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import cardFrame from '@views/oneClickHelp/localDeviceInfo/modules/CardFrame.vue'
import Empty from '@/components/oneClickHelp/Empty.vue'
import hotKnowledge from '@views/opmg/knowledgeManagement/knowledgeSearch/modules/hotKnowledge'
import knowledgeItem from '@views/opmg/knowledgeManagement/knowledgeSearch/modules/knowledgeItem'
import { knowledgeSearchAttachmentPreviewMixins } from '@/views/opmg/knowledgeManagement/knowledgeSearch/modules/knowledgeSearchAttachmentPreviewMixins'
export default {
  name: 'knowledgeSearchResult',
  mixins: [JeecgListMixin, knowledgeSearchAttachmentPreviewMixins],
  components: {
    cardFrame,
    Empty,
    hotKnowledge,
    knowledgeItem
  },
  props: {
    data: {
      type: Object,
      required: false,
      default: {},
    }
  },
  data() {
    return {
      disableMixinCreated: true,
      url: {
        list: '/kbase/knowledges/search',
      },
      queryParam: {
        knowledgeType: 0
      },
      kkfileviewUrl: ''
    }
  },

  watch: {
    data: {
      handler(val) {
        if (val.value) {
          this.queryParam.keyword = val.value;
        }
      },
      deep: true,
      immediate: true,
    },
  },
  activated() {
    if (this.kkfileviewUrl === '') {
      this.getKkfileviewURL().then((res)=>{
        this.kkfileviewUrl = res
        this.getlist(1);
      })
    } else {
      this.getlist(1);
    }
  },
  methods: {
    getlist(arg) {
      if (!this.queryParam.keyword.trim()) {
        return false
      }
      this.loadData(arg);
    },
    onSearch() {
      this.getlist(1);
    },
    qPageChange(pageNum, pageSize) {
      this.ipagination.current = pageNum
      this.ipagination.pageSize = pageSize
      this.getlist()
    },
    //返回上一级
    getGo() {
      this.$parent.pButton1(1)
    },
    // 跳转详情
    goDetail(item) {
      this.$parent.pButton2(2, item);
    },
    setName(str) {
      let partter = /<font color='red'>/g
      return str.replace(partter, "<font color='#409EFF'>")
    },
    changeTab(e) {
      this.queryParam.knowledgeType = e
      this.getlist(1);
    }
  },
}

</script>
<style scoped lang="less">
@import '~@assets/less/onclickStyle.less';
@color: #1890FF;

.header-back {
  position: absolute;
  top: 20px;
  right: 50px;
  text-align: right;
  color: #fff;
  font-size: 14px;
  display: flex;
  align-items: center;
  cursor: pointer;
  img {
    margin-right: 3px;
  }
}

.header-wrapper-r {
  width: 100%;
  display: flex;
  .header-form {
    width: 63%;
    margin-bottom: 10px;
    margin-top: 25px;
  }
}

.body-wrapper {
  display: flex;
  justify-content: space-between;
  width: 100%;
  height: 100%;

  .body-info-l {
    width: 63%;
    height: calc(100vh - 195px - 62px);
    overflow: auto;
  }
  .body-info-r {
     width: 37%;
  }
}

.pagination {
  display: flex;
  justify-content: center;
  padding-right: 20px;
  margin-top: 12px;
}

/****输入框样式****/
::v-deep .ant-input-suffix {
  font-size: 22px;
}

::v-deep .ant-input-search-icon {
  color: #fff;
}

::v-deep .ant-input-affix-wrapper .ant-input-suffix {
  right: 31px;
}

::v-deep .ant-input {
  border: 1px solid rgba(80, 129, 249, 0.49) !important;
  border-radius: 28px;
  height: 58px;
  font-size: 14px;
  padding-left: 32px;
  background: transparent;
  color: #fff;
}

/****tab 样式 ****/
/deep/ .ant-tabs-tab {
  padding: 5px 22px !important;
  margin-right: 10px !important;
  font-size: 15px;
}
/deep/ .ant-tabs-tab-active {
  color: @color !important;
}
/deep/ .ant-tabs-ink-bar {
  background-color: @color;
}
/deep/ .ant-tabs-bar {
  border-bottom: none !important
}

/********定制深色模式下知识内容的颜色*********/
::v-deep .item-info {
  color: #fff;
  border: 1px solid rgba(123,174,255,0.25) !important;
}
::v-deep .item-info, ::v-deep .item-info .like-info {
  color: #fff !important;
}
::v-deep .item-info .fileWrapper .divider .line {
  border-top: 1px dashed rgba(151,151,151,0.35);
}
::v-deep .item-info .like-info .line {
  background-color: #979797 !important;
}
::v-deep .item-info .fileWrapper .fileContent .bg {
  background: #101C2E !important;
}
/********定制深色模式下知识内容的颜色*********/

::-webkit-scrollbar {
  display: none;
}
</style>
