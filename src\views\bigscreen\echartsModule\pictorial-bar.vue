<template>
  <div :id="chartId" ref="chartDom" style="width: 100%; height: 100%"></div>
</template>

<script>
  import {
    getAction,
  } from '@/api/manage'
  export default {
    props: ['chartId', 'chartObj'],
    data() {
      return {
        symbolSize: [0, 12],
        cpuData: [],
        url: {
          cputop10: 'device/statistics/cputop10',
        },
      }
    },
    created() {
      this.getCpu()
    },
    watch: {},
    methods: {
      getCpu() {
        getAction(this.url.cputop10).then((res) => {
          this.cpuData = res.result
          this.drawChart(this.cpuData)
        })
      },
      drawChart(data) {
        var nameDatas = data.map((el) => el.name)
        var datas = data.map((el) => el.cpuRate)
        var bgdata = []
        data.forEach((element) => {
          bgdata.push(100)
        })
        let myChart = this.$echarts.init(document.getElementById(this.chartId))
        myChart.setOption({
          legend: {
            show: false,
          },
          tooltip: {
            show: true,
            trigger: 'item',
            axisPointer: {
              type: 'shadow',
            },
            backgroundColor: 'rgba(9, 24, 48, 0.5)',
            borderColor: 'rgba(75, 253, 238, 0.4)',
            textStyle: {
              color: '#CFE3FC',
            },
            borderWidth: 1,
            appendToBody: true,
            formatter: (a, b) => {
              return '名称：' + nameDatas[a.dataIndex] + "<br/> 使用率：" + a.value + "%"
            },
            transitionDuration: 0, //echart防止tooltip的抖动
          },
          grid: {
            left: 0,
            right: 60,
            top: 16,
            bottom: 0,
            //   containLabel: true,
          },
          yAxis: [{
              type: 'category',
              inverse: true,
              show: false,
            },
            {
              type: 'category',
              inverse: true,
              axisTick: 'none',
              axisLine: 'none',
              show: true,
              data: datas,
              axisLabel: {
                show: true,
                fontSize: 12,
                color: '#00c7ff',
                formatter: (a, b) => {
                  return a + '%'
                },
              },
            },
          ],
          xAxis: {
            show: false,
            type: 'value',
          },
          series: [{
              name: 'value',
              type: 'bar',
              barWidth: this.symbolSize[1] - 2,
              showBackground: true,
              backgroundStyle: {
                color: '#162425',
              },
              itemStyle: {
                normal: {
                  color: (params) => {
                    var colors = ['#F9FFFC', '#3FFFC2']
                    if (params.dataIndex === 0) {
                      colors = ['#F9FFFC', '#C49700']
                    }
                    return {
                      type: 'linear',
                      x: 0,
                      y: 0,
                      x2: 1,
                      y2: 0,
                      colorStops: [{
                          offset: 0,
                          color: colors[0],
                        },
                        {
                          offset: 1,
                          color: colors[1],
                        },
                      ],
                    }
                  },
                },
              },
              data: datas,
              z: 10,
              zlevel: 2,
              label: {
                normal: {
                  color: '#fff',
                  show: true,
                  position: [0, '-' + (this.symbolSize[1] - 2 + 5) + 'px'],
                  textStyle: {
                    fontSize: 12,
                  },
                  formatter: function (a) {
                    return nameDatas[a.dataIndex]
                  },
                },
              },
            },
            {
              // 值分隔
              type: 'pictorialBar',

              itemStyle: {
                normal: {
                  color: '#111217',
                },
              },
              symbolRepeat: 'fixed',
              symbolMargin: 2,
              symbol: 'rect',
              symbolClip: true,
              symbolSize: this.symbolSize,
              symbolPosition: 'start',
              symbolOffset: [-1, -5 / 4],
              symbolBoundingData: 100,
              data: bgdata,
              width: 25,
              z: 0,
              zlevel: 1,
              tooltip: {
                show: false
              },
            },
            {
              name: '背景框',
              type: 'bar',
              barWidth: this.symbolSize[1] + 3,
              barGap: '-' +
                (((this.symbolSize[1] + 3) / 2 + (this.symbolSize[1] - 2) / 2) / (this.symbolSize[1] - 2)) * 100 +
                '%',
              itemStyle: {
                normal: {
                  color: 'transparent',
                  borderColor: '#36383D',
                },
              },
              data: bgdata,
              z: 0,
              zlevel: 0,
              tooltip: {
                show: false
              },
            },
          ],
        })
        window.addEventListener('resize', () => {
          myChart.resize()
        })
      },
    },
  }
</script>

<style>
</style>