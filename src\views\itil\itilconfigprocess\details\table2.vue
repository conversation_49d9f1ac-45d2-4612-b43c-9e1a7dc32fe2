<template>
  <div style="overflow: hidden; overflow-x: auto">
    <a-table
      :loading="loading"
      rowKey="id"
      :dataSource="data"
      size="middle"
      bordered
      ref="table"
      style="white-space: nowrap"
    >
      <a-table-column title="动作" dataIndex="name" width="150" align="center">
        <template slot-scope="t">
          <span> {{ t }} </span>
        </template>
      </a-table-column>
      <a-table-column title="详情" dataIndex="comment" width="150" align="center">
        <template slot-scope="t">
          <pre><p style="text-align:left;margin-bottom: 0px;width: 400px;word-wrap:break-word;">{{t}}</p></pre>
        </template>
      </a-table-column>
      <a-table-column title="操作人" dataIndex="assignees" width="150" align="center">
        <template slot-scope="t">
          <div v-if="t">
            <span v-for="item in t">
              <span v-if="item.isExecutor">{{ item.username }} </span>
              <span v-else>{{ item.username }} </span>
            </span>
          </div>
        </template>
      </a-table-column>
      <a-table-column title="完成时间" dataIndex="endTime" width="150" align="center">
        <template slot-scope="t">
          <span>{{ t }}</span>
        </template>
      </a-table-column>
      <a-table-column title="附件" dataIndex="files" width="200" align="center">
        <template slot-scope="t">
          <div v-for="(item, index) in t">
            <span @click="fontClick(item.url)" style="color: #40a9ff">{{ item.fileName }}</span>
          </div>
        </template>
      </a-table-column>
    </a-table>
  </div>
</template>
<script>
import { getAction } from '@/api/manage'
export default {
  // 历史信息
  name: 'table2',
  data() {
    return {
      loading: false, // 表单加载状态
      data: [],
      url: {
        historicFlow: '/actTask/historicFlow/',
      },
    }
  },
  methods: {
    getDataList(id) {
      this.loading = true
      getAction(this.url.historicFlow + id).then((res) => {
        if (res.success) {
          this.loading = false
          this.data = res.result
          if (!res.result || res.result.length == 0) {
            this.$message.warning('未找到该记录审批历史数据,历史数据可能已被删除')
          }
        } else {
          this.$message.error(res.message)
        }
      })
    },
    handleTableChange(pagination, filters, sorter) {
      //分页、排序、筛选变化时触发
      //TODO 筛选
      if (Object.keys(sorter).length > 0) {
        this.isorter.column = sorter.field
        this.isorter.order = 'ascend' == sorter.order ? 'asc' : 'desc'
      }
      this.ipagination = pagination
      // this.loadData();
    },
    fontClick(path) {
      window.open(window._CONFIG['downloadUrl'] + '/' + path)
    },
  },
}
</script>
<style scoped></style>
