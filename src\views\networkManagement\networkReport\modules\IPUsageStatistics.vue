<template>
  <div style='height: 100%'>
    <card-frame>
      <div slot='titleSlot' class='title'>
        <yq-icon class='icon' type="netIP"></yq-icon>
        <span class='text'>IP使用统计</span>
      </div>
      <template slot='extraSlot'>
        <div class='extra-wrapper'>
          <a-tree-select
            v-if='treeData.length > 0'
            :tree-data="treeData"
            @select="onSelect"
            style='border-radius: 100%;width:250px'
            :getPopupContainer='(node) => node.parentNode'
            :dropdown-style="{ maxHeight: '280px', overflow: 'auto' }"
            :replace-fields="{ children:'children', title: 'title', key:'newId', value:'newId'}"
            :defaultValue="`${treeData[0].children[0].newId}`"
            :treeDefaultExpandedKeys="[`${treeData[0].children[0].newId}`]"
            placeholder="请选择IP网段"
          />
        </div>
      </template>
      <div slot='bodySlot' class='body-height'  v-if='chartDataLength>0'>
        <ipPanelPie :data-obj="chartData" :color-obj='colorObj'> </ipPanelPie>
      </div>
      <div slot='bodySlot' class='body-height body-empty' v-else>
        <a-spin :spinning='loading' v-if='loading'></a-spin>
        <a-list :data-source="[]" v-else/>
      </div>
    </card-frame>
  </div>
</template>
<script>
import { getAction } from '@api/manage'
import ipPanelPie from '@views/opmg/ipManagement/ipPanel/ipPanelPie'
import yqIcon from '@comp/tools/SvgIcon'
import cardFrame from '@views/networkManagement/networkReport/modules/CardFrame.vue'


export default {
  name: "IPUsageStatistics",
  components: { cardFrame, yqIcon, ipPanelPie },
  data() {
    return {
      loading:false,
      ipSegsentId:'',
      treeData: [],
      chartData: {},
      chartDataLength:0,
      chartDataType: [
        { name: '未使用', field: 'notUsed' },
        { name: '已使用', field: 'used' },
        { name: '正常', field: 'normal' },
        { name: '非法占用', field: 'illegalOccupation' },
        { name: '未获取到MAC', field: 'noGetMAC' },
        { name: '非法篡改', field: 'illegalFalsify' },
      ],
      colorObj: {
        colorList1: [{
            color1: '#5B8FF9',
            color2: '#5B8FF9'
          },
          {
            color1: '#5AD8A6',
            color2: '#5AD8A6'
          }
        ],
        colorList2: [{
            color1: '#389E0D',
            color2: '#389E0D'
          },
          {
            color1: '#FFA940',
            color2: '#FFA940'
          },
          {
            color1: '#8b8f00',
            color2: '#8b8f00'
          },
          {
            color1: '#ffc0cb',
            color2: '#ffc0cb'
          }
        ]
      },
      url: {
        occupancyRatio: '/devops/ip/board/occupancyRatio',
        querySegmentList: '/devops/ip/board/querySegmentList'
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    onSelect(value, e, extra) {
      if (extra.selectedNodes.length == 0) {
        this.$message.warning('请正确选择网段!')
        return
      } else {
        let node = extra.selectedNodes[0].data.props
        this.ipSegsentId = node.id
        this.occupancyRatio(node.auditId)
      }
    },
    getList() {
      getAction(this.url.querySegmentList).then(res => {
        if (res.success) {
          this.treeData = res.result
          if (this.treeData.length > 0) {
            this.treeData.forEach(ele => {
              if (ele.children && ele.children.length > 0) {
                ele.children.forEach(cele => {
                  cele['newId'] = cele.id + '_' + cele.auditId // 后台返回的id不是唯一的key,构造一个全新的key
                })
              }
              ele['newId'] = ele.id
              ele['selectable'] = false // 设置父节点不可被选中
            });
            // 设置默认选中并加载第一个网段数据
            if (this.treeData[0].children && this.treeData[0].children.length > 0) { 
              this.ipSegsentId = this.treeData[0].children[0].id
              this.occupancyRatio(this.treeData[0].children[0].auditId)
            }
          }
        } else {
          this.loading = false
          this.$message.warning(res.message)
        }
      }).catch((err) => {
        this.loading = false
        this.$message.warning(err.message)
      })
    },
    occupancyRatio(auditId) {
      this.chartData = {}
      this.loading = true
      this.chartDataLength = 0
      getAction(this.url.occupancyRatio, {
        segmentId: this.ipSegsentId,
        auditId: auditId,
      }).then((res) => {
        if (res.success) {
          let values = []
          let sum = 0
          for (let i = 0; i < this.chartDataType.length; i++) {
            let obj = {}
            let key = this.chartDataType[i].field
            if (res.result.hasOwnProperty(key)) {
              obj = {
                name: this.chartDataType[i].name,
                value: res.result[key] == '0' ? '' : res.result[key]
              }
              values.push(obj)
              if (!isNaN(res.result[key])) {
                sum += res.result[key] * 1
              }
            }
          }
          if (sum > 0) {
            this.chartData = {
              data1: values.slice(0, 2),
              data2: values.slice(2, 6)
            }
            this.chartDataLength = values.length
          }
          this.loading = false
        } else {
          this.loading = false
          this.$message.error(res.message)
        }
        
      }).catch((err) => {
        this.loading = false
        this.$message.error(err.message)
      })
    }
  }
}
</script>