<template>
  <a-card style="height: 100%;overflow: auto">
    <div style="position: absolute; right: 24px; top: 18px; z-index: 10">
      <img
        src="~@assets/return1.png"
        alt
        @click="getGo()"
        style="width: 20px; height: 20px; cursor: pointer"
      />
    </div>
    <a-row>
      <a-col :span="24">
        <div class="colorBox">
          <span class="colorTotal">任务信息</span>
        </div>
        <table class="gridtable">
          <tr>
            <td class="leftTd">任务名称</td>
            <td class="rightTd">{{ record.taskName }}</td>
            <td class="leftTd">所属网关</td>
            <td class="rightTd">{{ record.gatewayName }}</td>
          </tr>
          <tr>
            <td class="leftTd">IP网段类型</td>
            <td
              class="rightTd"
            >{{ getFilterType(record.filterType) }}</td>
            <td class="leftTd">IP网段</td>
            <td class="rightTd">{{record.filterType==='3'? record.customIpSegment: record.ipName}}</td>
          </tr>
          <tr>
            <td class="leftTd">执行方式</td>
            <td class="rightTd">{{record.executeType == 1 ? '周期' : '手动'}}</td>
            <td class="leftTd">扫描频率</td>
            <td class="rightTd">{{ record.executeCron }}</td>
          </tr>
          <tr>
            <td class="leftTd">启用状态</td>
            <td class="rightTd">{{ record.isEnable == 1 ? '启用' : '禁用' }}</td>
            <td class="leftTd">执行次数</td>
            <td class="rightTd">{{record.executeTimes}}</td>
          </tr>
          <tr>
            <td class="leftTd">发现设备数</td>
            <td class="rightTd">{{record.scanRecordNum}}</td>
            <td class="leftTd">连接协议</td>
            <td class="rightTd">{{ record.transferProtocol }}</td>
          </tr>
        </table>
        <div class="colorBox" style="margin-top:24px;">
          <span class="colorTotal">{{record.transferProtocol}}连接参数</span>
        </div>
        <table v-if="record.transferProtocol == 'SNMP'" class="gridtable">
          <tr>
            <td class="leftTd">端口号</td>
            <td class="rightTd">{{record.taskParam.port}}</td>
            <td class="leftTd">SNMP版本</td>
            <td class="rightTd">{{record.taskParam.version}}</td>
          </tr>
          <tr>
            <td class="leftTd">团体名</td>
            <td class="rightTd">{{record.taskParam.community}}</td>
            <td class="leftTd">用户名</td>
            <td class="rightTd">{{record.taskParam.username}}</td>
          </tr>
          <tr>
            <td class="leftTd">安全级别</td>
            <td class="rightTd">{{record.taskParam.snmpAuthLevel}}</td>
            <td class="leftTd">认证协议</td>
            <td class="rightTd">{{record.taskParam.sAuth}}</td>
          </tr>
          <tr>
            <td class="leftTd">认证密码</td>
            <td class="rightTd">
              <PasswordField :value="record.taskParam.sAuth_passwd" />
            </td>
            <td class="leftTd">加密协议</td>
            <td class="rightTd">{{record.taskParam.spriv}}</td>
          </tr>
          <tr>
            <td class="leftTd">加密密码</td>
            <td class="rightTd">
              <PasswordField :value="record.taskParam.spriv_passwd" />
            </td>
          </tr>
        </table>
        <table v-if="record.transferProtocol == 'SSH'" class="gridtable">
          <tr>
            <td class="leftTd">端口号</td>
            <td class="rightTd">{{record.taskParam.port}}</td>
            <td class="leftTd">SSH用户名</td>
            <td class="rightTd">{{record.taskParam.sshUsername}}</td>
          </tr>
          <tr>
            <td class="leftTd">SSH密码</td>
            <td class="rightTd">
              <PasswordField :value="record.taskParam.sshPassword" />
            </td>
            <td class="leftTd">超时设置</td>
            <td class="rightTd">{{record.taskParam.timeOut}}</td>
          </tr>
        </table>
        <table v-if="record.transferProtocol == 'PING'" class="gridtable">
          <tr>
            <td class="leftTd">超时设置</td>
            <td class="rightTd">{{record.taskParam.timeOut}}</td>
            <td class="leftTd">数据包大小</td>
            <td class="rightTd">{{record.taskParam.packetSize}}</td>
          </tr>
        </table>
      </a-col>
    </a-row>
  </a-card>
</template>

<script>
import PasswordField from '@/components/tools/PasswordField.vue'
export default {
  name: 'scanTaskDetails',
  components: {
    PasswordField
  },
  props: {
    data: {
      type: Object
    }
  },
  data() {
    return {
      record: {}
    }
  },
  watch: {
    data: {
      handler(val) {
        if (val) {
          this.record = Object.assign({}, val)
        }
        if (val.taskParam && val.taskParam.length > 0) {
          this.record['taskParam'] = JSON.parse(val.taskParam)
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    getFilterType(type){
      // 获取IP网段类型中文名称
      switch (type) {
        case '-1':
          return '全网'
          break;
        case '0':
          return '子网组'
          break;
        case '1':
          return '子网'
          break;
        case '2':
          return '网段'
          break;
        case '3':
          return '扩展'
          break;
        default :
          return ''
          break;
      }
    },
    //返回上一级
    getGo() {
      this.$parent.pButton2(0)
    }
  }
}
</script>
<style scoped>
table.gridtable {
  font-family: verdana, arial, sans-serif;
  font-size: 14px;
  color: #606266;
  border-width: 1px;
  border-color: #e8e8e8;
  border-collapse: collapse;
  text-align: left;
  width: 100%;
}

table.gridtable td {
  border-width: 1px;
  border-style: solid;
  border-color: #e8e8e8;
}

.leftTd {
  width: 17%;
  background-color: #fafafa;
  padding: 16px 24px;
  text-align: center;
}

.rightTd {
  width: 35%;
  padding: 16px 24px;
}

.rightTd2 {
  width: 83%;
  padding: 16px 24px;
}

.colorBox {
  margin-top: 10px;
  margin-bottom: 10px;
}

.colorTotal {
  padding-left: 7px;
  border-left: 4px solid #1e3674;
}
</style>