<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    switchFullscreen
    :destroyOnClose="true"
    :centered='true'
    @ok="handleOk"
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @cancel="handleCancel"
    cancelText="关闭">
    <a-spin :spinning="confirmLoading">
      <j-form-container :disabled="disableSubmit">
        <a-form :form="form" slot="detail" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-row :gutter="32">
            <a-col :span='12'>
              <a-form-item label="产品分类:">
                <a-tree-select
                  v-decorator="['categoryId', validatorRules.categoryId]"
                  allowClear :getPopupContainer="(node) => node.parentNode"
                  placeholder="请选择产品分类"
                  :tree-data="assetsCategoryTree"
                  :dropdownStyle="{maxHeight: '400px',overflow: 'auto'}"
                >
                </a-tree-select>
              </a-form-item>
            </a-col>
            <a-col :span='12'>
              <a-form-item label="设备名称" >
                <a-input
                  v-decorator="['name', validatorRules.name]"
                  placeholder="请输入设备名称"
                  :allowClear="true"
                  autocomplete="off"
                />
              </a-form-item>
            </a-col>
            <a-col :span='12'>
              <a-form-item label="设备唯一标识">
                <a-input
                  v-decorator="['deviceCode', validatorRules.deviceCode]"
                  placeholder="请输入设备唯一标识"
                  :allowClear="true"
                  autocomplete="off"
                />
              </a-form-item>
            </a-col>
            <a-col :span='12'>
              <a-form-item label="IP地址">
                <a-input
                  v-decorator="['ip', validatorRules.ip]"
                  placeholder="请输入IP地址"
                  :allowClear="true"
                  autocomplete="off"
                />
              </a-form-item>
            </a-col>
            <a-col :span='12'>
              <a-form-item label="所属单位">
                <a-tree-select v-decorator="['deptId',validatorRules.deptId]" :getPopupContainer="(node) => node.parentNode"
                               tree-node-filter-prop="title" :replaceFields="replaceFields" :treeData="departTreeData" show-search
                               :searchValue="bsearchKey" style="width: 100%"
                               :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }" placeholder="请选择所属单位" allow-clear
                               @change="onChangeTree" @search="onSearch">
                </a-tree-select>
              </a-form-item>
            </a-col>
            <a-col :span='12'>
              <a-form-item label="所在位置">
                <a-input
                  v-decorator="['location', validatorRules.location]"
                  placeholder="请输入所在位置"
                  :allowClear="true"
                  autocomplete="off"
                />
              </a-form-item>
            </a-col>
            <a-col :span='12'>
              <a-form-item label="使用人">
                <a-input
                  v-decorator="['username', validatorRules.username]"
                  placeholder="请输入使用人"
                  :allowClear="true"
                  autocomplete="off"
                />
              </a-form-item>
            </a-col>
            <a-col :span='12'>
              <a-form-item label="是否启用">
                <a-radio-group  v-decorator="['enable',{initialValue:'0'} ]">
                  <a-radio value="0">未启用</a-radio>
                  <a-radio value="1">已启用</a-radio>
                </a-radio-group>
              </a-form-item>
            </a-col>
            <a-col :span='12'>
              <a-form-item label="描述">
                <a-textarea v-decorator="['description', validatorRules.description]"
                            :autoSize='{minRows:2,maxRows:4}' placeholder="请输入描述信息"
                            :allowClear="true" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </j-form-container>
    </a-spin>
  </j-modal>
</template>

<script>
  import pick from 'lodash.pick'
  import { getAction, httpAction } from '@api/manage'
  export default {
    name: 'deviceReportingModal',
    components: {},
    data () {
      return {
        form: this.$form.createForm(this),
        title:'',
        width:'1000px',
        visible: false,
        disableSubmit: false,
        confirmLoading: false,
        labelCol: {
          xs: { span: 24 },
          sm: { span: 24 },
          md: { span: 6 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 24 },
          md: { span: 18 },
        },
        formCode: false,
        assetsCategoryTree: [],
        replaceFields: {
          children: 'children',
          title: 'deptName',
          key: 'deptId',
          value: 'deptId',
        },
        departTreeData: [],
        bsearchKey: '',
        canEditDevCode: false,//控制设备标识是否可编辑
        roomNodeType: '',//房间物理位置所选的最后一级数据类型（notRoom/room）
        roomList: [],//房间物理位置数据
        cabinetList: [],//房间里的机柜
        layerPoolList: [],//U下拉数据
        hisCabinetId: undefined,//编辑情况下，记录开始的机柜id
        hisLayerPoolList: [],//编辑情况下，记录开始U的下拉数据
        validatorRules: {
          categoryId: {
            rules: [
              { required: true, message: '请选择产品分类' },
            ]
          },
          name: {
            rules: [
              { required: true, message: '请输入设备名称' },
              { min: 2, message: '名称长度应在 2-50 之间', trigger: 'blur' },
              { max: 50, message: '名称长度应在 2-50 之间', trigger: 'blur' },
            ]
          },
          deviceCode: {
            rules: [
              {
                required: true, message: '输入设备唯一标识'
              },
              {
                pattern: /^(([a-zA-Z]+|[0-9]+|[a-zA-Z0-9-.:]){4,32})$/,
                message: '可包含字母、数字、英文横线或英文句号,4-32个字符'
              },
            ]
          },
          ip: {
            rules: [
              {
                required: true, message: '请输入设备ip'
              },
              {
                pattern: /^(((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?))(:\d{1,5})?)$/,
                message: '请输入正确格式的ip地址'
              }
            ]
          },
          deptId: {
            rules: [
              {
                required: true, message: '请选择所属单位'
              }
            ]
          },
          username: {
            rules: [
              { required: false, message: '请输入使用人' },
              {  max: 20, message: '使用人长度应不应超出 20 个字符' }]
          },
          location: {
            rules: [
              { required: false, message: '请输入所在位置' },
              {  max: 50, message: '所在位置长度不应超出 50 个字符' }]
          },
          description: {
            rules: [
              { required: false, min: 0, max: 200, message: '描述信息长度不应超出 200 个字符' }]
          },
        },
        url: {
          add: '/device/brigadeInfo/add',
          edit: '/device/brigadeInfo/edit',
          productTypeUrl: '/assetscategory/assetsCategory/selectAssetsCategoryTree',//获取产品分类下拉数据
        },
        deviceInfo: {}
      }
    },
    created() {
      this.getDepartList()
      this.getCategoryTree()
    },
    methods: {
      getDepartList() {
        getAction('/sys/sysDepart/queryAllTree').then((res) => {
          for (let i = 0; i < res.length; i++) {
            let temp = res[i]
            this.departTreeData.push(temp)
          }
        })
      },
      onChangeTree(value) {},
      onSearch(e) {
        this.bsearchKey = e
      },
      /*获取产品分类下拉数据源*/
      getCategoryTree() {
        return new Promise((resolve, reject) => {
          getAction(this.url.productTypeUrl)
            .then((res) => {
              if (res.success) {
                this.assetsCategoryTree = res.result
                resolve({ success: true, message: res.message })
              } else {
                reject({ success: false, message: res.message })
              }
            }).catch((err) => {
            reject({ success: false, message: err.message })
          })
        })
      },

      /***************设备新增、编辑初始化***************/
      add() {
        this.edit({enable:'0'})
      },
      edit (record) {
        this.deviceInfo = JSON.parse(JSON.stringify(record))
        this.visible = true
        this.form.resetFields()
        //this.canEditDevCode = record.id ? true : false

        //编辑
        if (record.id) {
          this.deviceInfo.deptId = record.deptId || undefined
          this.$nextTick(() => {
            this.form.setFieldsValue(pick(this.deviceInfo,
              'categoryId', 'name', 'deviceCode', 'ip', 'description', 'deptId', 'capacity', 'location','username','enable'))
          })
        }
      },
      handleOk () {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err && !that.confirmLoading) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.deviceInfo.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'post'
            }
            //提交数据：设备、连接参数、资产（基本信息和附加字段）
            let formData =this.getDeviceInfo(values)
            httpAction(httpurl, formData, method)
              .then((res) => {
                that.confirmLoading = false
                if (res.success) {
                  that.$emit('ok')
                  that.close()
                } else {
                  that.$message.warning(res.message)
                }
              }).catch((err)=>{
              that.confirmLoading = false
              that.$message.warning(err.message)
            })
          }
        })
      },
      /*整理设备信息数据*/
      getDeviceInfo(values) {
        let info =JSON.parse(JSON.stringify(this.deviceInfo))
        info['categoryId'] = values.categoryId
        info['name'] = values.name
        info['deviceCode'] = values.deviceCode
        info['ip'] = values.ip
        info['deptId'] = values.deptId
        info['location'] = values.location
        info['username'] = values.username
        info['enable'] = values.enable
        info['description'] = values.description
        return info
      },
      handleCancel () {
        this.close()
      },
      close () {
        this.$emit('close');
        this.visible = false;
      },
    }
  }
</script>
<style lang="less" scoped>
@import '~@assets/less/normalModal.less';
</style>