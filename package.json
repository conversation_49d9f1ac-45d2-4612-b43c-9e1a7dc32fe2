{"name": "vue-antd-jeecg", "version": "2.4.0", "private": true, "scripts": {"pre": "cnpm install || yarn --registry https://registry.npm.taobao.org || npm install --registry https://registry.npm.taobao.org ", "serve": "vue-cli-service serve", "build:test": "vue-cli-service build --mode test", "buildLog": "vue-cli-service build", "lint": "vue-cli-service lint", "build": "vue-cli-service build && node buildLog.js", "report": "vue-cli-service build --report", "build:nohash": "vue-cli-service --mode development build --no-module && node buildLog.js"}, "dependencies": {"@antv/data-set": "^0.11.4", "@antv/layout": "^0.3.20", "@antv/x6": "^1.18.4", "@antv/x6-vue-shape": "^1.5.4", "@babel/plugin-proposal-optional-chaining": "^7.16.7", "@jeecg/antd-online-mini": "2.4.0-beta4", "@kangc/v-md-editor": "^1.7.12", "@tinymce/tinymce-vue": "^2.1.0", "@toast-ui/editor": "^2.1.2", "@vue/composition-api": "^1.7.1", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^1.0.2", "@wchbrad/vue-easy-tree": "^1.0.5", "and": "^0.0.3", "ant-design-vue": "^1.7.2", "area-data": "^5.0.6", "axios": "^0.21.1", "bpmn-js": "^7.5.0", "bpmn-js-properties-panel": "^0.43.1", "camunda-bpmn-moddle": "^6.1.2", "clipboard": "^2.0.4", "color": "^3.2.1", "copy-webpack-plugin": "^6.0.3", "core-js": "^3.6.5", "cronstrue": "^2.50.0", "cross-env": "^7.0.3", "dagre": "^0.8.5", "dayjs": "^1.8.0", "dedent": "^1.5.1", "diff-match-patch": "^1.0.5", "dom-align": "1.12.0", "e-vue-contextmenu": "^0.1.3", "echarts": "^4.9.0", "echarts-gl": "^1.1.2", "echarts-liquidfill": "^2.0.6", "element-ui": "^2.15.1", "enquire.js": "^2.1.6", "execa": "^7.1.1", "exports-loader": "^3.0.0", "file-saver": "^2.0.5", "form-making": "^1.2.10", "highlight.js": "^11.11.1", "imports-loader": "^3.0.0", "js-cookie": "^2.2.0", "jspdf": "^2.3.1", "less-loader": "5.0.0", "lodash.get": "^4.4.2", "lodash.pick": "^4.4.0", "md5": "^2.2.1", "moment": "^2.29.1", "nprogress": "^0.2.0", "quill": "^1.3.7", "quill-image-drop-module": "^1.0.3", "screenfull": "^4.2.0", "skywalking-client-js": "^0.12.0", "sql-formatter": "^4.0.2", "three": "^0.129.0", "tinymce": "^5.3.2", "vcolorpicker": "^1.1.0", "viser-vue": "^2.4.8", "vue": "^2.6.10", "vue-animate-number": "^0.4.2", "vue-area-linkage": "^5.1.0", "vue-clipboard2": "^0.3.3", "vue-codemirror": "^4.0.6", "vue-codemirror-lite": "^1.0.4", "vue-color": "^2.8.2", "vue-cropper": "^0.5.4", "vue-fullcalendar": "^1.0.9", "vue-i18n": "^8.7.0", "vue-infinite-loading": "^2.4.5", "vue-infinite-scroll": "^2.0.2", "vue-json-editor-fix-cn": "^1.4.3", "vue-loader": "^15.7.0", "vue-ls": "^3.2.0", "vue-photo-preview": "^1.1.3", "vue-print-nb-jeecg": "^1.0.9", "vue-quill-editor": "^3.0.6", "vue-router": "^3.6.5", "vue-seamless-scroll": "^1.1.23", "vue-splitpane": "^1.0.4", "vue-visjs": "^0.4.1", "vue2-ace-editor": "0.0.15", "vuedraggable": "^2.20.0", "vuex": "^3.1.0", "vxe-table": "2.9.13", "vxe-table-plugin-antd": "1.8.10", "webpack": "^4.46.0", "xe-utils": "2.4.8"}, "devDependencies": {"@babel/polyfill": "^7.2.5", "@vue/cli-plugin-babel": "^3.3.0", "@vue/cli-plugin-router": "~4.5.15", "@vue/cli-plugin-vuex": "~4.5.15", "@vue/cli-service": "^4.5.13", "compression-webpack-plugin": "^6.1.1", "html-webpack-plugin": "^4.2.0", "less": "3", "lib-flexible": "^0.3.2", "node-sass": "^4.14.1", "pug": "^3.0.2", "pug-plain-loader": "^1.1.0", "sass-loader": "^7.3.1", "vue-template-compiler": "^2.6.10", "webpack-bundle-analyzer": "^4.9.0"}, "postcss": {"plugins": {"autoprefixer": {}}}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"]}