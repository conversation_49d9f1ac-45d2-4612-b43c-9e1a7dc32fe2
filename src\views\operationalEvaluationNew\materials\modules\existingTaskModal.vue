<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    :destroyOnClose="true"
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }"
    :centered="true"
    switch-fullscreen
    :maskClosable="false"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭"
  >
    <a-card :bordered="false" :bodyStyle="{ padding: '0' }" class="card-style">
      <div class="table-page-search-wrapper">
        <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
          <a-row :gutter="24" ref="row">
            <a-col :span="spanValue">
              <a-form-item label="报告名称">
                <a-input
                  :maxLength="maxLength"
                  :allowClear="true"
                  autocomplete="off"
                  v-model="queryParam.projectName"
                  placeholder="请输入报告名称"
                />
              </a-form-item>
            </a-col>
            <a-col :span="colBtnsSpan()">
              <span
                class="table-page-search-submitButtons"
                :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
              >
                <a-button type="primary" class="btn-search btn-search-style" @click="searchQuery">查询</a-button>
                <a-button class="btn-reset btn-reset-style" @click="searchReset">重置</a-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false" :bodyStyle="{ padding: '0' }" class="card-style">
      <a-table
        ref="table"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{
          selectedRowKeys: selectedRowKeys,
          onChange: tableType == 'exist' ? onSelectChange : onSelectChange1,
          type: getType,
        }"
        @change="handleTableChange"
      >
        <template slot="tooltip" slot-scope="text">
          <a-tooltip placement="topLeft" :title="text" trigger="hover">
            <div class="tooltip">
              {{ text }}
            </div>
          </a-tooltip>
        </template>
        <template slot="status" slot-scope="text, record">
          <a-tag class="tag" :color="statusConfig(record.status).color">
            {{ statusConfig(record.status).text }}
          </a-tag>
        </template>
        <template slot="evaluationCycle" slot-scope="text, record">
          {{ record.startTime }} - {{ record.endTime }}
        </template>
      </a-table>
    </a-card>
  </j-modal>
</template>
<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
import { getStatusConfig } from '@/views/operationalEvaluationNew/modules/statusConfig'
export default {
  name: 'existingTaskModal',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  data() {
    return {
      title: '选择已有报告配置',
      width: '1200px',
      visible: false,
      disableSubmit: false,
      confirmLoading: false,
      tableType: 'exist',
      queryParam: {
        isAllStatus: 'false',
        },
      spanValue: 8,
      maxLength: 50,
      formItemLayout: {
        labelCol: {
          style: 'width:90px',
        },
        wrapperCol: {
          style: 'width:calc(100% - 90px)',
        },
      },
      // 表头
      columns: [
        {
          title: '报告名称',
          dataIndex: 'projectName',
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
            return {
              style: cellStyle,
            }
          },
          scopedSlots: {
            customRender: 'tooltip',
          },
        },
        {
          title: '状态',
          dataIndex: 'status',
          scopedSlots: {
            customRender: 'status',
          },
        },
        {
          title: '评估周期',
          dataIndex: 'evaluationCycle',
          scopedSlots: {
            customRender: 'evaluationCycle',
          },
        },
        {
          title: '发起人',
          dataIndex: 'sender_dictText',
        },
      ],
      url: {
        list: '/devops/projectInfo/pageList',
      },
    }
  },
  computed: {
    // 计算属性的 getter
    getType: function () {
      return this.tableType == 'exist' ? 'radio' : 'checkbox'
    },
  },
  methods: {
    // 获取填报状态的颜色配置
    statusConfig(status) {
      return getStatusConfig(status)
    },
    handleOk() {
      console.log('this.selectionRows===', this.selectionRows)
      this.visible = false
      this.$emit('ok', this.selectionRows[0])
    },
    handleCancel() {
      this.visible = false
    },
    copyTask() {
      this.visible = true
    },
    onSelectChange1(selectedRowKeys, selectionRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectionRows = selectionRows
    },
    searchReset() {
      this.queryParam = {
        isAllStatus: 'false',
      }
      this.loadData(1)
    },
  },
}
</script>
<style lang="less" scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
</style>
