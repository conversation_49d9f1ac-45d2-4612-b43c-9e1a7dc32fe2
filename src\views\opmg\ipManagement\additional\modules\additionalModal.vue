<template>
  <j-modal :title="title" :width="width" :visible="visible" switchFullscreen :destroyOnClose="true" :centered='true'
    @ok="handleOk" @cancel="handleCancel" cancelText="关闭">
    <additional-form ref="realForm" @ok="submitCallback"></additional-form>
  </j-modal>
</template>

<script>
  import additionalForm from './additionalForm'
  export default {
    name: 'additionalModal',
    components: {
      additionalForm
    },
    data() {
      return {
        title: '',
        width: '800px',
        visible: false,
      }
    },
    methods: {
      add() {
        this.visible = true
        this.$nextTick(() => {
          this.$refs.realForm.add();
        })
      },
      edit(record) {
        this.visible = true
        this.$nextTick(() => {
          this.$refs.realForm.edit(record);
        })
      },
      close() {
        this.$emit('close');
        this.visible = false;
      },
      handleOk() {
        this.$refs.realForm.submitForm();
        this.$emit("refresh");
        // this.close();
      },
      submitCallback(e) {
        this.$emit('ok', e);
        this.visible = false;
      },
      handleCancel() {
        this.close()
      }
    }
  }
</script>
<style lang="less" scoped>
  @import '~@assets/less/normalModal.less';
</style>