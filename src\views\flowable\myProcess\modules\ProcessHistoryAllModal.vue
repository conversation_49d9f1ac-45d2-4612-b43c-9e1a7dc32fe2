<template>
  <j-modal
    :title="title"
    :width="1200"
    :visible='visible'
    :maskClosable='false'
    :destroyOnClose="true"
    switchFullscreen
    :confirmLoading="confirmLoading"
    @cancel="handleCancel"
    cancelText="关闭"
  >
    <template slot="footer">
      <a-button type='primary' @click="addKnowledge" v-if='itilProcessItem.length>0'>加入知识库</a-button>
      <a-button type='primary' v-if="showWithdraw" @click="withdraw">撤销</a-button>
<!--      <a-button key="return"  @click="showBackTaskModal" style="background-color: #ff4d4f; color: white;">退回</a-button>
      <div v-if="taskBackNodesVisible">
      <task-back-nodes
        ref="taskBackNodesRef"
        :visible.sync="taskBackNodesVisible"
        :execute-task-id="taskId"
        @backTaskFinished="handleBackTaskFinished"
      />
      </div>-->
      <a-button key="close" @click="handleCancel"> 关闭 </a-button>
    </template>
    <a-tabs class='tab-border'  default-active-key="comments">
      <a-tab-pane  key="comments" tab="流程审批进度历史">
        <a-table
            class='tab-pane-box-padding'
            ref="table"
            size="small"
            bordered
            rowKey="id"
            :columns="columns"
            :dataSource="dataSource"
            :pagination="ipagination"
            @change="handleTableChange"
          >
          <span slot="status" slot-scope="status">
            <span v-if="status == 'done'" style="color: #52c41a">已办理</span>
            <span v-else style="color: #999999">待处理</span>
          </span>
          <span slot="action" slot-scope="text, record">
            <a v-if="record.endTime == null" @click="urge(record)">催办</a>
          </span>
        </a-table>
      </a-tab-pane>
      <a-tab-pane  key="form" tab="流程表单">
        <div v-if="generateStartFormVisible">
          <k-form-build
            :value="startFormJson"
            :dynamicData="dynamicData"
            :defaultValue="variables"
            :disabled="disabledEdit"
            @assetsHandler="assetsHandler"
            ref="generateStartForm" />
        </div>
        <assets-update-list ref="assetsUpdateModal"></assets-update-list>

      </a-tab-pane>

      <a-tab-pane key="processIntanceImage" tab="实时流程图">
        <div class="tab-pane-box-padding">
          <process-picture-real :row="processInstanceId"></process-picture-real>
          <!-- <img style="max-width: 1200px; max-height: 600px" :src="imagePath" alt="流程图" /> -->
        </div>
      </a-tab-pane>
    </a-tabs>
    <add-knowledge-modal ref='addKnowledge' :add-from-flowable='true'></add-knowledge-modal>
    <a-modal
      title="催办"
      :visible='urgeVisible'
      :maskClosable='false'
      :destroyOnClose='true'
      :confirm-loading="urgeConfirmLoading"
      @ok="handleUrge"
      @cancel="handleCancelUrge"
    >
      <a-form ref="form" :model="form" :label-width="85" :rules="formValidate">
        <a-form-item label="催办内容" prop="reason">
          <a-input v-model='form.urgemessage' :allow-clear='true' :rows='4' autocomplete='off' type='textarea' />
        </a-form-item>
        <!--        <a-form-item label="提醒方式">
                  <a-checkbox v-model="form.sendMessage">站内消息通知</a-checkbox>
                  <a-checkbox v-model="form.sendSms" disabled>短信通知</a-checkbox>
                  <a-checkbox v-model="form.sendEmail" disabled>邮件通知</a-checkbox>
                </a-form-item>-->
      </a-form>
    </a-modal>
  </j-modal>
</template>
<script>
import { getAction, postAction } from '@/api/manage'
import ProcessPictureReal from '../../process-instance/modules/ProcessPictureReal'
import AssetsUpdateList from '../../../cmdb/assets/assetsUpdate/AssetsUpdateList'
import addKnowledgeModal from '@views/opmg/knowledgeManagement/knowledgeBase/modules/AddKnowledgeModal.vue'
import TaskBackNodes from '../../task-todo/modules/TaskBackNodes.vue'
import { putAction } from '../../../../api/manage'
export default {
  name: 'ProcessHistoryModal',
  components: {
    TaskBackNodes,
    ProcessPictureReal,AssetsUpdateList,addKnowledgeModal
  },
  props: {showAll:false},
  data() {
    return {
      taskBackNodesVisible: false,
      generateStartFormVisible: false,
      startFormJson: {},
      variables: null,
      disabledEdit: true,
      dynamicData: {
        bpmnDepartTreeData: []
      },
      urgeVisible: false,
      taskId:"",
      showWithdraw: false,
      urgeConfirmLoading: false,
      form: {
        urgemessage: '请尽快办理！',
        sendMessage: true,
      },
      formValidate: {
        // 表单验证规则
      },
      title: '',
      visible: false,
      confirmLoading: false,
      /* 分页参数 */
      ipagination: {
        current: 1,
        pageSize: 5,
        pageSizeOptions: ['5', '10', '15'],
        showTotal: (total, range) => {
          return range[0] + '-' + range[1] + ' 共' + total + '条'
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0,
      },
      //表头
      //列设置
      settingColumns: [],
      //列定义
      columns: [
        {
          title: '序号',
          dataIndex: '',
          key: 'rowIndex',
          customCell:() =>{
            let cellStyle = 'text-align:center;width:60px;max-width:120px !important;min-width:60px'
            return {style:cellStyle}
          },
          customRender: function (t, r, index) {
            return parseInt(index) + 1
          },
        },
        {
          title: '任务名称',
          dataIndex: 'name',
          customCell:() =>{
            let cellStyle = 'text-align:center'
            return {style:cellStyle}
          },
        },
        {
          title: '处理人',
          dataIndex: 'assigneeName',
          customCell:() =>{
            let cellStyle = 'text-align:center'
            return {style:cellStyle}
          },
        },
        {
          title: '候选人',
          dataIndex: 'candidateUserNames',
          customRender: (text) => {
            //字典值替换通用方法
            let str = ''
            if (text == null) {
              return ''
            }
            text.forEach((element) => {
              str += element + ','
            })
            return str.substring(0, str.length - 1)
          },
          customCell:() =>{
            let cellStyle = 'text-align:center'
            return {style:cellStyle}
          },
        },
        // {
        //   title: '审批操作',
        //   align: 'center',
        //   dataIndex: 'commentType',
        //   customCell:() =>{
        //     let cellStyle = 'text-align:center'
        //     return {style:cellStyle}
        //   },
        // },
        // {
        //   title: '审批意见',
        //   align: 'center',
        //   dataIndex: 'commentFullMessage',
        //   customCell:() =>{
        //     let cellStyle = 'text-align:center'
        //     return {style:cellStyle}
        //   },
        // },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          customCell:() =>{
            let cellStyle = 'text-align:center;width:160px'
            return {style:cellStyle}
          },
        },
        {
          title: '完成时间',
          dataIndex: 'endTime',
          customCell:() =>{
            let cellStyle = 'text-align:center;width:160px'
            return {style:cellStyle}
          },
        },
        {
          title: '状态',
          dataIndex: 'status',
          scopedSlots: { customRender: 'status' },
          customCell:() =>{
            let cellStyle = 'text-align:center;width:120px'
            return {style:cellStyle}
          },
        },
        {
          title: '操作',
          dataIndex: 'action',
          fixed:'right',
          width:70,
          align: 'center',
          scopedSlots: {
           /* filterDropdown: 'filterDropdown',
            filterIcon: 'filterIcon',*/
            customRender: 'action',
          },
        },
      ],
      dataSource: [],
      processInstanceId: null,
      itilProcessKey:[],//记录需要加入知识库的表单类型（问题、事件、工单）
      itilProcessItem:[],
      task: {},
      url: {
        historylist: '/flowable/task/list',
        urge: '/flowable/task/urge',
        queryProcessDefinitionKey:'/kbase/relation/check',//获取流程定义key
      },
    }
  },
  created() {
    this.getTreeData()
    this.getItilProcessKey('itilProcessKey')
    if (this.showAll){
      this.columns.splice(this.columns.length-1, 1);
    }
  },
  methods: {
    withdraw(){
      var this_ = this
      this.$confirm({
        title: '确认撤销',
        okText: '是',
        cancelText: '否',
        content: '是否撤销该流程?',
        onOk: function() {
          getAction("/flowable/task/withdraw", {
            taskId: this_.taskId
          }).then((res) => {
            if (res.success) {
              this_.$message.success(res.message)
            } else {
              this_.$message.warning(res.message)
            }
          })

        }
      })
    },
    urge(record) {
      this.urgeVisible = true
      this.task = record
    },
    handleUrge() {
      postAction(this.url.urge, {
        username: this.task.assignee,
        candidates: this.task.candidateUsers,
        msg: this.form.urgemessage,
        taskId: this.task.id,
      }).then((res) => {
        if (res.code == 200) {
          this.$message.success('催办成功')
          this.urgeVisible = false
          this.resetForm()
        } else {
          this.$message.error('催办失败')
          this.urgeVisible = false
          this.resetForm()
        }
      })
    },
    handleTableChange(pagination, filters, sorter) {
      //分页、排序、筛选变化时触发
      //TODO 筛选
      console.log(pagination)
      if (Object.keys(sorter).length > 0) {
        this.isorter.column = sorter.field
        this.isorter.order = 'ascend' == sorter.order ? 'asc' : 'desc'
      }
      this.ipagination = pagination
      //this.loadData();
    },
    resetForm() {
      this.form.urgemessage = '请尽快办理！'
    },
    handleCancelUrge() {
      this.urgeVisible = false
      this.resetForm()
    },
    init(processInstanceId,isAddKnowledge=true) {
      this.processInstanceId = processInstanceId
      this.visible = true
      getAction(this.url.historylist, { processInstanceId: this.processInstanceId })
        .then((res) => {
          this.dataSource = res.result.records
          this.initForm(processInstanceId)
          this.taskId = res.result.records[0].id
        })
        .finally(() => {})

      this.itilProcessItem=[]
      if(isAddKnowledge){
        this.getProcessDefinitionKey(processInstanceId)
      }

    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleCancel() {
      this.dataSource = []
      this.close()
    },

    //打开资产编辑
    assetsHandler(e){
      if (this.variables && this.variables.tempProcessInstanceId) {
        this.$refs.assetsUpdateModal.tempId = this.variables.tempProcessInstanceId
      } else {
        this.$refs.assetsUpdateModal.tempId = ''
      }
      this.$refs.assetsUpdateModal.init(e)
    },
    initForm(id) {
      getAction('/flowable/processInstance/formData', { processInstanceId: id }).then((res) => {
        if (res.code != 200) {
          this.$message.error(res.message)
        } else {
          const data = res.result
          this.showBusinessKey = data.showBusinessKey
          this.businessKey = data.businessKey
          if (data && data.renderedStartForm) {
            this.startFormJson = JSON.parse(data.renderedStartForm)
            this.variables = data.variables
            this.generateStartFormVisible = true
          }
        }
      })
    },
    getTreeData() {
      getAction('/sys/sysDepart/queryTreeList').then((res) => {
        this.dynamicData.bpmnDepartTreeData = res.result
      })
    },
    /*获取加入知识库的表单类型（问题、事件、工单）*/
    getItilProcessKey(dictCode){
      getAction('/kbase/relation/getDict').then((res)=>{
        if (res.success){
          this.itilProcessKey=res.result
        }
      })
    },
    /*根据流程实例id，获取流程定义key*/
    getProcessDefinitionKey(processInstanceId){
      getAction(this.url.queryProcessDefinitionKey,{processInstanceId: processInstanceId}).then((res)=>{
        if (res.success){
          this.knowledgeBtnActiveStatus(res.result)
        }
      })
    },
    /*是否激活加入知识库按钮*/
    knowledgeBtnActiveStatus(processInfo){
      this.itilProcessItem=[]
      if (this.itilProcessKey.length){
        this.itilProcessItem= this.itilProcessKey.filter((item,index)=>{
          let obj=JSON.parse(item.value)
          if(obj.processDefinitionKey===processInfo.processDefinitionKey){
            obj.processDefinitionKey=processInfo.processDefinitionKey
            obj.processDefinitionName=processInfo.processDefinitionName
            return obj
          }
        })
      }
    },
    /*加入知识库*/
    addKnowledge(){
      let keys=Object.keys(this.variables)
      if(keys.length>0){
        let attKeys=keys.filter((item)=>{
          //根据custom_attachments_前缀找到附件属性
          if(item.includes("custom_attachments_")){
            return item
          }
        })
        let urlArr=[]
        for (let i=0;i<attKeys.length;i++){
          let attArr=this.variables[attKeys[i]]
          for (let k=0;k<attArr.length;k++){
            urlArr.push(attArr[k].url)
          }
        }
        console.log(this.itilProcessItem)
        let obj=JSON.parse(this.itilProcessItem[0].value)
        this.$refs.addKnowledge.addKnowledgeFromProcess({
          title:this.variables.title,
          plan:this.variables.plan,
          files:urlArr.length>0? urlArr.join(','):'',
          processInstanceId: this.processInstanceId,
          processDefinitionKey:obj.processDefinitionKey,
          processDefinitionName:obj.processDefinitionName,
        })
        this.$refs.addKnowledge.title = '新增';
        this.$refs.addKnowledge.disableSubmit = false;
      }else {
        this.$message.warning("暂无数据")
      }
    },
/*    showBackTaskModal() {
      this.$confirm({
        title: '确认退回',
        content: '确定要退回此任务吗？',
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          this.taskBackNodesVisible = true;
        },
        onCancel: () => {
          // 用户取消操作
        }
      });
    },
    handleBackTaskFinished(backNode) {
      putAction('/flowable/task/back', {
        taskId: this.taskId,
        activityId: backNode.nodeId,
        activityName: backNode.nodeName,
        userId: backNode.userId,
        message: this.message
      }).then(res => {
        this.$message.success(res.message)
        this.taskBackNodesVisible = false
        this.$store.commit('TASK_TIMESTIMP', res.timestamp)
        this.$emit('close')
        this.init(this.processInstanceId,this.isAddKnowledge=true)
      })
    },*/

  },
}
</script>
<style scoped lang='less'>
@import '~@assets/less/common.less';
@import '~@assets/less/YQCommon.less';
@import '~@assets/less/normalModal.less';
//在全屏模式下，实时流程图容器containers的高度样式如下；至于非全屏模式下，containers高度样式在流程图组件中设定
.j-modal-box.fullscreen{
  ::v-deep .containers {
    height: calc(100vh - 56px - 24px - 1px - 60px - 32px - 12px - 1px - 24px - 56px) !important;
  }
}
.tab-border{
  border: 1px solid #e8e8e8
}
.tab-pane-box-padding{
  padding: 0px 5px !important;
}
::v-deep .ant-table-tbody tr {
    height: 38px !important;
}
</style>
