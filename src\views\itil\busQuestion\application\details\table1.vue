<template>
  <div style="overflow: hideen; overflow-x: auto">
    <table class="gridtable">
      <tr>
        <td class="leftTd">编 码</td>
        <td class="rightTd">{{ data.processNumber }}</td>
        <td class="leftTd">标 题</td>
        <td class="rightTd">{{ data.title }}</td>
      </tr>

      <tr>
        <td class="leftTd">类 型</td>
        <td class="rightTd">
          <span>
            <div v-if="data.eventType == 'hardware'">硬件</div>
            <div v-if="data.eventType == 'software'">软件</div>
            <div v-if="data.eventType == 'line'">线路</div>
            <div v-if="data.eventType == 'cloud'">云</div>
            <div v-if="data.eventType == 'network'">网络</div>
            <div v-if="data.eventType == 'safe'">安全</div>
          </span>
        </td>
        <td class="leftTd">优先级</td>
        <td class="rightTd">
          <span>
            <div v-if="data.priority == 0" style="color: #ffb300">低</div>
            <div v-if="data.priority == 1" style="color: #fc7611">中</div>
            <div v-if="data.priority == 2" style="color: #df1a1a">高</div>
          </span>
        </td>
      </tr>

      <tr>
        <td class="leftTd">报告人</td>
        <td class="rightTd">{{ data.createBy }}</td>
        <td class="leftTd">联系电话</td>
        <td class="rightTd">{{ data.contact }}</td>
      </tr>

      <tr>
        <td class="leftTd">状 态</td>
        <td class="rightTd">
          <span>
            <div v-if="data.status == 0">新建</div>
            <div v-if="data.status == 1">审批</div>
            <div v-if="data.status == 2">处理</div>
            <div v-if="data.status == 3">审核</div>
            <div v-if="data.status == 4">评价</div>
            <div v-if="data.status == 5">关闭</div>
            <div v-if="data.status == 6">退回</div>
          </span>
        </td>
        <td class="leftTd">报告时间</td>
        <td class="rightTd">{{ data.createTime }}</td>
      </tr>

      <tr>
        <td class="leftTd">联系人</td>
        <td class="rightTd">{{ data.owner_name }}</td>
        <td class="leftTd">当前处理人</td>
        <td class="rightTd">{{ data.assigneeName }}</td>
      </tr>

      <tr>
        <td class="leftTd">期望完成时间</td>
        <td class="rightTd">{{ data.completionDate }}</td>
        <td class="leftTd">实际完成时间</td>
        <td class="rightTd">{{ data.completeTime }}</td>
      </tr>

      <tr>
        <td class="leftTd">问题描述</td>
        <td class="rightTd" colspan="3">{{ data.description }}</td>
      </tr>
      <tr>
        <td class="leftTd">附 件</td>
        <td class="rightTd" colspan="3" style="padding-bottom: 0">
          <div v-for="(item, index) in data.files" style="float: left; margin-left: 10px">
            <!--            {{item}}-->
            <div
              v-if="
                item.suffix == 'png' ||
                item.suffix == 'jpg' ||
                item.suffix == 'jpeg' ||
                item.suffix == 'gif' ||
                item.suffix == 'ico' ||
                item.suffix == 'bmp' ||
                item.suffix == 'pic' ||
                item.suffix == 'tif'
              "
            >
              <div class="orientation" style="float: none">
                <img :src="urla + item.url" alt="" id="urla" />
                <span class="font" @click="fontClick(item.url)">下载文件</span>
              </div>
            </div>
            <div v-else-if="item.suffix == 'pdf'">
              <div class="orientationFile" style="float: none">
                <img src="@/assets/img/pdf.png" alt="" id="urlas" />
                <span class="font1" @click="fontClick(item.url)">下载文件</span>
              </div>
            </div>
            <div v-else-if="item.suffix == 'doc'">
              <div class="orientationFile">
                <img src="@/assets/img/doc.png" alt="" id="urlas" />
                <span class="font1" @click="fontClick(item.url)">下载文件</span>
              </div>
            </div>
            <div v-else-if="item.suffix == 'docx'">
              <div class="orientationFile">
                <img src="@/assets/img/docx.png" alt="" id="urlas" />
                <span class="font1" @click="fontClick(item.url)">下载文件</span>
              </div>
            </div>
            <div v-else-if="item.suffix == 'txt'">
              <div class="orientationFile">
                <img src="@/assets/img/txt.png" alt="" id="urlas" />
                <span class="font1" @click="fontClick(item.url)">下载文件</span>
              </div>
            </div>
            <div v-else>
              <a @click="fontClick(item.url)" style="color: #40a9ff">{{ item.fileName }}</a>
            </div>
          </div>
        </td>
      </tr>
    </table>
    <!-- <div v-if="show == '1'">
      <a-button type="primary" @click="buttonClick()"  style=" float: right; margin: 50px 20px 10px 10px;">
        加入知识库
      </a-button>

    </div> -->
  </div>
</template>
<script>
import JFormContainer from '@/components/jeecg/JFormContainer'
import { httpAction, getAction, postAction } from '@/api/manage'
export default {
  // 基本信息
  name: 'table1',
  components: {
    JFormContainer,
  },
  data() {
    return {
      data: {},
      visible: false,
      valueNew: {},
      returnValue: {},
      show: '0',
      urla: window._CONFIG['downloadUrl'] + '/',
    }
  },
  methods: {
    getData(value, show) {
      this.show = show
      this.returnValue = value
      // this.visible = true
      getAction('/busQuestion/getQuestDetails', { busId: this.returnValue.busId }).then((res) => {
        if (res.success) {
          this.data = res.result
        }
      })
    },
    buttonClick() {
      let formData = {}
      formData.busId = this.returnValue.busId
      formData.procInstId = this.returnValue.procInstId

      postAction('/busQuestion/saveUmpKnowledge', formData).then((res) => {
        this.uploading = false
        if (res.success) {
          this.$message.success(res.message)
          this.dialogStartProcessVisibleInChild = false
        } else {
          this.$message.warning(res.message)
          this.dialogStartProcessVisibleInChild = false
        }
      })
    },
    fontClick(path) {
      window.open(window._CONFIG['downloadUrl'] + '/' + path)
    },
  },
}
</script>
<style scoped>
table.gridtable {
  white-space: nowrap;
  font-family: verdana, arial, sans-serif;
  font-size: 14px;
  color: #606266;
  border-width: 1px;
  border-color: #e8e8e8;
  border-collapse: collapse;
  text-align: left;
  width: 100%;
}
table.gridtable td {
  border-width: 1px;
  border-style: solid;
  border-color: #e8e8e8;
}
.leftTd {
  width: 17%;
  background-color: #fafafa;
  padding: 16px 24px;
  text-align: center;
}
.rightTd {
  width: 35%;
  padding: 16px 24px;
}
#urla {
  width: 100px;
  height: 100px;
}
#urlas {
  width: 100px;
  height: 100px;
}
.orientation {
  width: 100%;
  /*margin: 0 auto;*/
  position: relative;
  overflow: hidden;
}
.orientationFile {
  width: 100%;
  /*margin: 0 auto;*/
  position: relative;
  overflow: hidden;
}
.font {
  position: absolute;
  bottom: 0;
  background: rgba(0, 0, 0, 0.75);
  left: 0;
  width: 100%;
  height: 30%;
  color: #fff;
  line-height: 32px;
  cursor: pointer;
  transform: translateY(109%);
  transition: all 0.3s ease-out 0s;
  text-align: center;
}
.font1 {
  position: absolute;
  bottom: 0;
  background: rgba(0, 0, 0, 0.75);
  left: 0;
  width: 100%;
  height: 30%;
  color: #fff;
  line-height: 32px;
  cursor: pointer;
  transform: translateY(109%);
  transition: all 0.3s ease-out 0s;
  text-align: center;
}
.orientation:hover .font {
  transform: translateY(0%);
}
.orientationFile:hover .font1 {
  transform: translateY(0%);
}
</style>