<template>
  <yq-modal
    :title='"false"'
    :width='width'
    :visible='visible'
    :destroyOnClose='true'
    cancelText='取消'
    :footer='null'
    :cancelButtonProps="{ style: { display: 'none' }}"
    @ok='handleOk'
    @cancel='handleCancel'
  >
    <a-spin :spinning='confirmLoading'>
      <div style='display: flex;flex-flow: row nowrap;overflow: hidden;text-overflow: ellipsis;white-space: nowrap'>
          <a-icon :type='iconName' style='color:#FE9400;font-size: 22px'></a-icon>
          <span style='color:#ffffff;font-size: 16px;margin-left: 16px;font-weight: 500'>{{title}}</span>
        </div>
      <div style='color:#ffffff;margin-top: 12px;margin-left: 38px'>{{content}}</div>
      <div style='margin-top: 24px;text-align: right'>
        <a-button class='btn-reset' style='margin-right: 20px' @click='handleCancel'>取消</a-button>
        <a-button type='primary' class='btn-search' style='margin-left:0' @click='handleOk'>确认</a-button>
      </div>
    </a-spin>
  </yq-modal>
</template>
<script>
import {httpAction } from '@api/manage'
export default {
  name: 'ConfimModal',
  data() {
    return {
      title: '',
      iconName:'question-circle',
      content:'',
      iconColor:'#faad14',
      disableSubmit: false,
      width: '416px',
      visible: false,
      confirmLoading: false,
      ids:''
    }
  },
  methods: {
    add(ids='') {
      this.visible = true
      this.dis=ids
      this.$nextTick(() => {
        this.model = {}
      })
    },
    handleOk() {
      let that = this
      that.$emit('ok',this.dis)
      that.close()
    },
    handleCancel() {
      this.close()
    },
    close() {
      this.visible = false
    },
  }
}
</script>
<style scoped lang='less'>
@import "~@assets/less/onclickStyle.less";
/*@import "~@assets/less/normalModal.less";*/
::v-deep .ant-modal-header{
  display: none;
}
::v-deep .ant-modal-close{
  display: none;
}
</style>