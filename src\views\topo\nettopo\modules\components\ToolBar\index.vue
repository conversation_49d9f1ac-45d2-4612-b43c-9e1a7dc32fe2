<template>
  <div class="bar" @click="handleClick">
    <a-tooltip placement="bottom">
      <template #title>
        <span>放大</span>
      </template>
      <a-button name="zoomIn"  class="item-space" size="small" icon="zoom-in"> </a-button>
    </a-tooltip>
    <a-tooltip placement="bottom">
      <template #title>
        <span>缩小</span>
      </template>
      <a-button name="zoomOut"  class="item-space" size="small" icon="zoom-out"> </a-button>
    </a-tooltip>
    <a-tooltip placement="bottom" v-if="operate === 'show'">
      <template #title>
        <span>居中</span>
      </template>
      <a-button name="alignCenter" class="item-space" size="small" icon="align-center"> </a-button>
    </a-tooltip>
    <a-tooltip placement="bottom" v-if="operate === 'create'">
      <template #title>
        <span>清空</span>
      </template>
      <a-button name="delete"  class="item-space delete-img" size="small"> </a-button>
    </a-tooltip>

    <a-tooltip placement="bottom" v-if="operate === 'create'">
      <template #title>
        <span>回退</span>
      </template>
      <a-button :disabled="!canUndo" name="undo"  class="item-space" size="small" icon="undo">
      </a-button>
    </a-tooltip>

    <a-tooltip placement="bottom" v-if="operate === 'create'">
      <template #title>
        <span>前进</span>
      </template>
      <a-button :disabled="!canRedo" name="redo"  class="item-space" size="small" icon="redo">
      </a-button>
    </a-tooltip>

    <!-- <a-tooltip placement="bottom">
      <template #title>
        <span>复制 (Cmd + Shift + Z)</span>
      </template>
      <a-button name="copy"  class="item-space" size="small" icon="copy">
      </a-button>
    </a-tooltip> -->

    <a-tooltip placement="bottom" v-if="operate === 'create'">
      <template #title>
        <span>删除</span>
      </template>
      <a-button name="cut"  class="item-space" size="small" icon="delete"> </a-button>
    </a-tooltip>

    <!-- <a-tooltip placement="bottom">
      <template #title>
        <span>粘贴 (Cmd + V)</span>
      </template>
      <a-button name="paste"  class="item-space" size="small" icon="snippets">
      </a-button>
    </a-tooltip> -->

    <a-popover v-model="visible" title="导出文件" trigger="hover" placement="bottom">
      <a slot="content">
        <div v-for="item in imageTypeList" :key="item.key" class="menu-item" @click="exportFile(item.name)">
          <a-icon :type="item.icon" class="menus-item-icon" />
          <span>{{item.name}}</span>
        </div>
      </a>
      <a-button name="exportFile"  class="item-space" size="small" icon="upload"></a-button>
    </a-popover>

    <a-tooltip placement="bottom" v-if="operate === 'create'">
      <template #title>
        <span>导入</span>
      </template>
      <a-button name="download"  class="item-space" size="small" icon="download"> </a-button>
    </a-tooltip>
    <!-- <a-tooltip placement="bottom" v-if="operate === 'create'">
      <template #title>
        <span>划线</span>
      </template>
      <a-button name="drawLine"  class="item-space" size="small" icon="highlight"> </a-button>
    </a-tooltip> -->
    <!-- <a-tooltip placement="bottom" v-if="operate === 'show'">
      <template #title>
        <span>全屏</span>
      </template>
      <a-button name="fullscreen"  class="item-space" size="small" icon="fullscreen">
      </a-button>
    </a-tooltip> -->

    <!-- <a-tooltip placement="bottom">
      <template #title>
        <span>打印 (Cmd + P)</span>
      </template>
      <a-button name="print"  class="item-space" size="small" icon="printer">
      </a-button>
    </a-tooltip> -->

    <!-- <a-tooltip placement="bottom">
      <template #title>
        <span>导出 (Cmd + P)</span>
      </template>
      <a-button name="toJSON"  class="item-space" size="small">
        toJSON
      </a-button>
    </a-tooltip> -->
    <a-tooltip placement="bottom" v-if="operate === 'create'">
      <template #title>
        <span>自动生成</span>
      </template>
      <a-button type="primary" name="auto"  class="item-space" size="small" icon="cluster">
      </a-button>
    </a-tooltip>
    <a-tooltip placement="bottom" v-if="sonTopoFlag">
      <template #title>
        <span>跳转到父拓扑</span>
      </template>
      <a-button name="back"  class="item-space" size="small" icon="rollback"> </a-button>
    </a-tooltip>
    <a-tooltip placement="bottom"  v-if="operate === 'create'">
      <template #title>
        <span>选择</span>
      </template>
      <div v-if="!isSelectionEnabled" class="nav-icon icon-hover" style="background-color:#fff">
        <img name="selection" src="./img/arrow.png" alt="">
      </div>
      <div v-if="isSelectionEnabled" class="nav-icon" style="background-color:#1E3674">
        <img name="selection" src="./img/arrow-white.png" alt="">
      </div>
    </a-tooltip>
    <a-tooltip placement="bottom"  v-if="operate === 'create'">
      <template #title>
        <span>网格</span>
      </template>
      <div v-if="!isSelectionGrid" class="nav-icon icon-hover" style="background-color:#fff">
        <img name="grid" src="./img/grid.png" alt="">
      </div>
      <div v-if="isSelectionGrid" class="nav-icon" style="background-color:#1E3674">
        <img name="grid" src="./img/grid-white.png" alt="">
      </div>
    </a-tooltip>
    <a-tooltip placement="bottom" v-if="showMiniMap">
      <template #title>
        <span>小地图</span>
      </template>
      <a-button v-if="toggleMiniMap"  type="primary" name="minimap"  class="item-space" size="small" icon="environment"> </a-button>
      <a-button v-else name="minimap"  class="item-space" size="small" icon="environment"> </a-button>
    </a-tooltip>
    <a-dropdown v-if="operate === 'create'" overlayClassName="layout-class" :getPopupContainer='node=>node.parentNode'>
      <a-button name="layout"  class="item-space" size="small" icon="pic-center"></a-button>
      <a-menu slot="overlay">
        <a-menu-item @click="chooseLayoutType('circle')">环形布局</a-menu-item>
        <a-sub-menu key="test" title="树形布局">
          <a-menu-item @click="chooseLayoutType('tree','TB')">起始位置在上</a-menu-item>
          <a-menu-item @click="chooseLayoutType('tree','BT')">起始位置在下</a-menu-item>
        </a-sub-menu>
      </a-menu>
    </a-dropdown>
    <a-tooltip placement="bottom">
      <template #title>
        <span>查找</span>
      </template>
      <a-button v-if="showSearchModal"  type="primary" name="search"  class="item-space" size="small" icon="search"> </a-button>
      <a-button v-else name="search"  class="item-space" size="small" icon="search"> </a-button>
    </a-tooltip>
    <a-input
      style="top: 10000px;position:fixed"
      class="file-input"
      id="file"
      type="file"
      accept=".json"
      v-if="operate === 'create'"
      @change="getJson"
    />
    <search ref="searchModal"></search>
    <!-- <topo-big-screen-modal ref="screenModal"></topo-big-screen-modal> -->
  </div>
</template>

<script>
  const tryToJSON = (val) => {
    try {
        return JSON.parse(val)
    } catch (error) {
        return val
    }
}
import FlowGraph from '../../graph'
import { DataUri } from '@antv/x6'
import { CircularLayout, DagreLayout } from '@antv/layout'
import FileSaver from 'file-saver'
import Search from '../Search.vue'
// import TopoBigScreenModal from '../../TopoBigScreenModal.vue'
export default {
  name: 'Index',
  components: {
    Search
    // TopoBigScreenModal
  },
  data() {
    return {
      canUndo: '',
      canRedo: '',
      backFlag: false,
      isSelectionEnabled: false, // 是否可以框选
      isSelectionGrid: false, // 是否开启画布网格
      showSearchModal: false, // 是否开启查找弹框
      gridOption: {
        type: 'dot', // 网格类型
        size: 10, // 网格大小
        color: '#a0a0a0', // 主网格颜色
        thickness: 1, // 主网格线宽度
      },
      imageTypeList: [{
        icon: 'picture', 
        name: 'PNG'
      },
      {
        icon: 'picture', 
        name: 'JPG'
      },
      {
        icon: 'file-image', 
        name: 'SVG'
      },
      {
        icon: 'file', 
        name: 'JSON'
      }
    ], // 导出文件类型
    layoutTypeList:[{
        icon: 'circle', 
        name: '环形布局',
        key: 'circle'
      },
      {
        icon: 'tree', 
        name: '树形布局',
        key: 'tree'
      }], // 布局方式
      visible: false, // 是否显示导出文件菜单
      layoutTypeVisible: false, // 是否显示布局方式菜单
      apiUrl: window._CONFIG['domianURL'],
    }
  },
  props: {
    sonTopoFlag: {
      type: Boolean,
      default: false,
      require: false,
    },
    operate: {
      type: String,
      default: 'create',
      required: true,
    },
    topoInfo: {
      type: Object,
      default: {},
      required: false,
    },
    isAuto: {
      type: Boolean,
      default: true,
      require: false,
    },
    showMiniMap: {
      type: Boolean,
      default: false
    },
    toggleMiniMap: {
      type: Boolean,
      default: false
    }
  },
  mounted() {
    setTimeout(() => {
      this.initEvent()
    }, 200)
  },
  methods: {
    initEvent() {
      const { graph } = FlowGraph
      const { history } = graph
      history.on('change', () => {
        this.canUndo = history.canUndo()
        this.canRedo = history.canRedo()
      })
      // graph.bindKey('ctrl+z', () => {
      //   if (history.canUndo()) {
      //     history.undo()
      //   }
      //   return false
      // })
      // graph.bindKey('ctrl+shift+z', () => {
      //   if (history.canRedo()) {
      //     history.redo()
      //   }
      //   return false
      // })
      // graph.bindKey('ctrl+d', () => {
      //   graph.clearCells()
      //   return false
      // })
      // graph.bindKey('ctrl+s', () => {
      //   graph.toPNG((datauri) => {
      //     DataUri.downloadDataUri(datauri, 'chart.png')
      //   })
      //   return false
      // })
      // graph.bindKey('ctrl+p', () => {
      //   graph.printPreview()
      //   return false
      // })
      graph.bindKey('ctrl+c', this.copy)
      graph.bindKey('ctrl+v', this.paste)
      // 绑定删除快捷键
      graph.bindKey('ctrl+x', this.cut)
      graph.bindKey('del', this.cut)
    },
    copy() {
      // 处理只能复制群组容器、文本节点、虚拟节点、边线节点、矩形节点
      const { graph } = FlowGraph
      const cells = graph.getSelectedCells()
      if (cells && cells.length) {
        const result =  cells.every(cell => {
          return cell.data.nodeType == 'text' || cell.data.edgeType == 'decoration' ||
          cell.data.nodeType == 'rectDragNode' || cell.data.nodeType == 'group' || cell.data.isVirtual
        })
        if (result) {
          graph.copy(cells)
        } else {
          // 清空剪切板
          graph.cleanClipboard()
        }
      } else {
        return false
      }
    },
    cut() {
      const { graph } = FlowGraph
      const cells = graph.getSelectedCells()
      if (cells.length) {
        graph.cut(cells)
      }
      return false
    },
    paste() {
      const { graph } = FlowGraph
      if (!graph.isClipboardEmpty()) {
        const cells = graph.paste({ offset: 32 })
        graph.cleanSelection()
        graph.select(cells)
      }
      return false
    },
    getJson(e) {
      const { graph } = FlowGraph
      const file = document.getElementById('file').files[0]
      if (!!file) {
        const reader = new FileReader()
        reader.readAsText(file)
        const _this = this
        reader.onload = function () {
          // this.result为读取到的json字符串，需转成json对象
          _this.ImportJSON = JSON.parse(this.result)
          _this.$emit('importTopo')
          // graph.fromJSON(_this.ImportJSON)
        }
      } else {
        this.$message.warning('请先选择合适的json文件')
      }
    },
    selection() {
      const { graph } = FlowGraph
      this.isSelectionEnabled =  !this.isSelectionEnabled
      if (this.isSelectionEnabled) {
        graph.disablePanning() 
        graph.setRubberbandModifiers(null) // 启用框选
      } else {
        graph.cleanSelection()
        graph.enablePanning() // 启用画布平移
        graph.setRubberbandModifiers('alt') // 禁用框选
      }
    },
    // 设置网格背景
    grid() {
      this.isSelectionGrid = !this.isSelectionGrid
      this.createGridOption(this.isSelectionGrid)
    },
    createGridOption(e) {
      const { graph } = FlowGraph
      let option = {
        visible: e ? true : false,
        size: this.gridOption.size,
        type: this.gridOption.type,
        args: [{
          color: this.gridOption.color,
          thickness: this.gridOption.thickness,
        }]
      }
      graph.drawGrid(option)
    },
    // 切换小地图显示隐藏
    minimap() {
      this.$emit('changeMiniMap',!this.toggleMiniMap)
    },
    showExportType() {
      this.visible = true
    },
    showLayoutType() {
      this.layoutTypeVisible = true
    },
    //选择布局类型
    chooseLayoutType(key,type) {
      this.$parent.globalGridAttr.topoConfig.edgeAni = false
      switch (key) {
        case 'circle':
          this.setCircleLayout()
          break
        case 'tree':
          this.setTreeLayout(type)
          break
        default:
          break
      }
      this.layoutTypeVisible = false
    },
    // 计算圆环的半径大小
    calculateRadius(numElements) {
      // 设置初始半径
      let radius = 200;
      // 如果有多于2个元素，则每次递增20像素作为新的半径值
      for (let i = 3; i <= numElements; i++) {
          radius += 20;
      }
      return radius;
    },
    // 序列化:导出当前页面的节点和边
    topoJson() {
      const { graph } = FlowGraph
      let json = graph.toJSON()
      let data = {
        nodes: [],
        edges: [],
      }
      json.cells.forEach(el => {
        if (el.shape !== "edge") {
          data.nodes.push(el)
        } else {
          if (el.source.cell || el.target.cell) {
            data.edges.push(el)
          }
        }
      })
      return data
    },
    // 树形布局
    setTreeLayout(type) {
      if (!type) {
        return
      }
      const { graph } = FlowGraph
      let data = this.topoJson()
      if (data.nodes.some((item) => item.data.nodeType == 'group')) {
        this.$message.warning('不支持对群组容器使用布局！')
        return
      }
      graph.clearCells() // 先清除旧数据
      const dagreLayout = new DagreLayout({
        type: 'dagre',
        rankdir: type, // 布局的方向。	'TB' | 'BT' | 'LR' | 'RL'
        align: undefined, // 节点对齐方式 'UL' | 'UR' | 'DL' | 'DR' | undefined
        // nodesep: 35,
        // ranksep: 35,
        // controlPoints: true
        // nodesep: 15,
      })
      const model = dagreLayout.layout(data)
      graph.fromJSON(model)
      this.$parent.clearStatusInterval()
      this.$parent.initDeviceStatus()
      graph.scaleContentToFit()
      graph.centerContent()
    },
    // 环形布局
    setCircleLayout() {
      const { graph } = FlowGraph
      // 构造布局所需数据
      let data = this.topoJson()
      if (data.nodes.length < 3) {
        return
      }
      if (data.nodes.some((item) => item.data.nodeType == 'group')) {
        this.$message.warning('不支持对群组容器使用布局！')
        return
      }
      graph.clearCells()
      const circularLayout = new CircularLayout({
        type: 'circular',
        radius: this.calculateRadius(this.topoInfo.nodeList.length),
        ordering: 'topology' // 排序依据。默认null,代表直接使用数据中的顺序,topology 按照拓扑排序,degree 按照度数大小排序
        // controlPoints: true,
        // connecting: {
        //   allowLoop: true,
        // }
      })
      const model = circularLayout.layout(data)
      graph.fromJSON(model)
      this.$parent.clearStatusInterval()
      this.$parent.initDeviceStatus()
      graph.scaleContentToFit()
      graph.centerContent()
    },
    // 导出为图片
    exportFile(name) {
      const { graph } = FlowGraph
      switch (name) {
        case 'PNG':
          graph.toPNG(
            (dataUri) => {
              DataUri.downloadDataUri(dataUri, `${this.topoInfo.topoName}.png`)
            },
            {
              backgroundColor: 'white',
              padding: {
                top: 20,
                right: 30,
                bottom: 40,
                left: 50,
              },
              quality: 1,
            }
          )
        break 
        case 'JPG':
          graph.toJPEG(
            (dataUri) => {
              DataUri.downloadDataUri(dataUri, `${this.topoInfo.topoName}.jpg`)
            },
            {
              backgroundColor: 'white',
              padding: {
                top: 20,
                right: 30,
                bottom: 40,
                left: 50,
              },
              quality: 1,
            }
          )
          break
        case 'SVG':
          graph.toSVG(
            (dataUri) => {
              DataUri.downloadDataUri(DataUri.svgToDataUrl(dataUri), `${this.topoInfo.topoName}.svg`)
            },
            {
              preserveDimensions: {
                width: '100%',
                height: '100%',
              },
              stylesheet: `.x6-graph-svg-viewport {transform: matrix(1, 0, 0, 1, 0, 0);}`,
            }
          )
          break
        case 'JSON':
          // 将json转换成字符串
          let cellCount = graph.getCellCount()
          if (cellCount > 0) {
            let topoJson = graph.toJSON()
            let xlink = 'xlink:href'
            topoJson.cells.forEach((item, i) => {
              if (item.attrs && item.attrs.image && item.attrs.image[xlink]) {
                let images = item.attrs.image[xlink].split('/')
                images.forEach((ele, index) => {
                  if (images[index] == 'sys') {
                    images.splice(0, index)
                  } else {
                    return
                  }
                })
                item.attrs.image[xlink] = images.join('/')
              }
            })
            const data = JSON.stringify(topoJson)
            const blob = new Blob([data], { type: '' })
            FileSaver.saveAs(blob, `${this.topoInfo.topoName}.json`)
          } else {
            this.$message.warning('没有节点/边数据！')
          }
          break
        default:
          break  
      }
      this.visible = false
    },
    handleClick(event) {
      const { graph } = FlowGraph
      const name = event.target.name
      switch (name) {
        case 'undo':
          graph.history.undo()
          break
        case 'redo':
          graph.history.redo()
          break
        case 'delete':
          graph.clearCells()
          break
        case 'auto':
          this.$emit('auto')
          break
        case 'exportFile':
         this.showExportType();
          break
        case 'print':
          graph.printPreview()
          break
        case 'copy':
          this.copy()
          break
        case 'cut':
          this.cut()
          break
        case 'paste':
          this.paste()
          break
        case 'toJSON':
          // graph.fromJSON({cells:[graph.toJSON().cells[0],graph.toJSON().cells[1]]})
          break
        case 'back':
          this.$emit('back')
          break
        case 'download':
          document.getElementById('file').click()
          break
        case 'fullscreen':
          this.$refs.screenModal.show(this.topoInfo)
          break
        case 'zoomIn':
          graph.zoom(0.2)
          break
        case 'zoomOut':
          graph.zoom(-0.2)
          break
        case 'alignCenter':
          graph.centerContent()
          break
        case "drawLine":
          this.$emit("drawLine")
          break
        case 'selection':
          this.selection();
          break
        case 'grid':
          this.grid();
          break
        case 'minimap':
          this.minimap();
          break
        case 'layout':
          this.showLayoutType();
          break
        case 'search':
          this.$refs.searchModal.visible = true;
          this.showSearchModal = !this.showSearchModal
          break
        default:
          break
      }
    },
  },
}
</script>

<style lang="less" scoped>
button {
  margin-right: 8px;
}
.bar {
  margin-left: 16px;
  margin-right: 16px;
  display: flex;
  align-items: center;
}
.item-space {
  //margin-left:16px;
}
.file-input {
  display: inline-block;
  width: 188px;
  height: 30px;
  margin-right: 8px;
  padding: 0px;
}
.delete-img {
  width: 24px;
  height: 24px;
  padding: 0;
  font-size: 14px;
  border-radius: 4px;
  background: url('./img/clear-up.png') no-repeat center;
}
button.item-space.delete-img.ant-btn.ant-btn-sm {
  background: url('./img/clear-up.png') no-repeat center;
}

.menu-item {
   padding: 6px 10px;
   color: #000;
  .menus-item-icon {
    margin-right: 10px;
  }
  &:hover {
    background-color: #f1f1f1;
  }
}
.nav-icon {
    width: 24px;
    height: 24px;
    padding: 0;
    font-size: 14px;
    margin-right: 8px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    img {
      width: 16px;
      height: 16px;
    }
}
.icon-hover {
  &:hover {
      border: 1px solid #1E3674;
    }
}
/* 设置布局下拉菜单样式 */
.layout-class {
  .ant-dropdown-menu {
    min-width: 120px;
    text-align: left;
    padding: 4px 3px;
    color: #000;
  }
}
::v-deep .ant-dropdown-menu-item:hover, ::v-deep .ant-dropdown-menu-submenu-title:hover {
  background-color: #f1f1f1 !important;
}
::v-deep .ant-dropdown-menu-submenu-arrow {
  right: 0;
}
::v-deep .ant-dropdown-menu-submenu-content {
  padding: 4px 3px;
}
/* 设置布局下拉菜单样式 */
</style>
