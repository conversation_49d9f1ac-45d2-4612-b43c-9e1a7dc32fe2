<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    :destroyOnClose="true"
    switchFullscreen
    @ok="handleOk"
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }"
    @cancel="handleCancel"
    cancelText="关闭"
  >
    <a-spin :spinning="confirmLoading">
      <j-form-container>
        <a-form :form="form" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-row>
            <a-col :span="24">
              <a-form-item label="部门" >
                <a-select placeholder="请选择部门" :allowClear="true"
                          :getPopupContainer="(node) => node.parentNode"
                      v-decorator="['departId', { rules: [{ required: false, message: '请选择部门!' }] }]"
                      @change="changeDepart">
                  <a-select-option v-for="(item, index) in departList" :key="'depart_'+index"  :value='item.id' :label='item.departName'>{{ item.departName }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="24">
             <a-form-item label="员工">
               <a-select placeholder="请选择员工" :allowClear="true" :getPopupContainer="(node) => node.parentNode"
                      v-decorator="['userId', { rules: [{ required: false, message: '请选择员工!' }] }]" @change='changeUser'>
                 <a-select-option v-for="item in userList" :key="item.id" :value="item.id" :label='item.realname'>{{ item.realname }}
                 </a-select-option>
               </a-select>
             </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </j-form-container>
    </a-spin>
  </j-modal>
</template>
<script>
import {getAction,putAction} from '@api/manage'
import pick from 'lodash.pick'
import fa from 'element-ui/src/locale/lang/fa'

export default {
  name: "AssetsDispatchModal",
  computed: {
    fa() {
      return fa
    }
  },
  data() {
    return {
      form: this.$form.createForm(this, {
        name: 'coordinated',
      }),
      title: '',
      width: '500px',
      visible: false,
      disableSubmit: false,
      confirmLoading:false,
      wrapperCol: {
        xs: {span: 24},
        sm: {span: 16},
      },
      labelCol: {
        xs: {span: 24},
        sm: {span: 5},
      },
      departInfo:{
        departId:'',
        departName:'',
        userId:'',
        realname:''
      },
      departList:[],
      userList:[],
      assetsId:'',
      url:{
        queryDepart:'/assets/assets/findDepart',
        queryUser:'/assets/assets/findUser',
        dispatch:'/assets/assets/edit1',
      },
    }
  },
  created() {
    this.getDepartList()
  },
  methods:{
    getDepartList() {
      getAction(this.url.queryDepart).then((res) => {
        if (res.success) {
          this.departList = res.result
        } else {
          this.$message.warning(res.message)
        }
      }).catch((err) => {
        this.$message.warning(err.message)
      })
    },

    edit(assetId,info){
      this.assetsId=assetId
      this.departInfo=info
      this.visible=true
      this.form.resetFields()
      if(this.departInfo.departId){
        this.getUserListByDepartId(this.departInfo.departId).then((res)=>{
          if(res.success){
            this.$nextTick(()=>{
              this.form.setFieldsValue(pick({departId:this.departInfo.departId,userId: this.departInfo.userId?this.departInfo.userId:undefined },'departId','userId'))
            })
          }
        })
      }
    },
    changeDepart(e,node) {
      this.userList = []
      this.form.setFieldsValue(pick({userId: undefined },'userId'))
      this.departInfo.realname =''
      this.departInfo.departName =e?node.componentOptions.propsData.label: ''
      if (e) {
        this.getUserListByDepartId(e)
      }
    },
    /*根据部门id，获取用户下拉数据*/
    getUserListByDepartId(e) {
      this.userList=[]
      return new Promise((resolve, reject)=>{
        getAction(this.url.queryUser, {
          departId: e,
        }).then((res) => {
          if (res.success) {
            this.userList = res.result != null ? res.result : []
            resolve({success:true,message:'请求成功'})
          }else {
            reject({success:false,message:res.massage})
          }
        }).catch((err)=>{
          reject({success:false,message:err.message})
        })
      })
    },
    changeUser(e,node){
      this.departInfo.realname =e?node.componentOptions.propsData.label: ''
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      let that = this
      that.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          putAction(that.url.dispatch, {
            id: that.assetsId,
            departmentId: values.departId,
            ownerId: values.userId,
          }).then((res) => {
            if (res.success) {
              that.departInfo.departId = values.departId?values.departId:''
              that.departInfo.userId = values.userId?values.userId:''
              that.$emit('ok', that.departInfo)
              that.close()
            } else {
              that.$message.warning(err.message)
            }
            that.confirmLoading = false
          }).catch((err) => {
            that.$message.warning(err.message)
            that.confirmLoading = false
          })
        }
      })
    },
    handleCancel() {
      this.close()
    },
  }
}
</script>

<style scoped lang="less">

</style>