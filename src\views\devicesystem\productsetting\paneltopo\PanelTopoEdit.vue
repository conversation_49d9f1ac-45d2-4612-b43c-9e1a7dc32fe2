<template>
  <j-modal
    :title="title"
    :visible="visible"
    :destroyOnClose="true"
    fullscreen
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭"
    okText="保存"
  >
    <panel-topo v-if="visible"
    ref="panelTopo"
    operate="create"
    v-model="panelType"
    toolbar
    :productId="productId"
    @ok="submitCallback"
     ></panel-topo>
  </j-modal>
</template>

<script>
import PanelTopo from './PanelTopo'
export default {
  name: 'PanelTopoEdit',
  components: {
    PanelTopo,
  },
  data() {
    return {
      title: '',
      width: 800,
      record: {},
      visible: false,
      disableSubmit: false,
      panelType:"0",
      productId:"",
    }
  },
  methods: {
    edit(record,panelType) {
      this.panelType = panelType
      this.record = record
      this.productId = this.record.id
      this.visible = true
      // this.$nextTick(()=>{
      //   this.$refs.panelTopo.createPanelTopo(record)
      // })
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      // this.$refs.panelTopo.spinning = true
      // this.$refs.panelTopo.spinTip = "正在保存..."
      let timer = setTimeout(() => {
        clearTimeout(timer)
        timer = null;
        this.$refs.panelTopo.save()
      }, 100)
    },
    handleCancel() {
      this.$confirm({
        title: '确定已经保存当前面板？',
        okText: '关闭',
        cancelText: '取消',
        onOk: () => {
          this.close()
        },
        onCancel: () => {
        },
      })

    },
    submitCallback() {
      this.$emit('ok')
      this.visible = false
    },
  },
}
</script>

<style lang="less" scoped>
@import '~@assets/less/normalModal.less';
</style>
