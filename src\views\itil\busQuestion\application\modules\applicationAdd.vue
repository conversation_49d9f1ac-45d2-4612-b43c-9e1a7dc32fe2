<template>
  <a-modal
    :title="title"
    :width="modalWidth"
    :visible="visible"
    :confirmLoading="confirmLoading"
    :destroyOnClose="true"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭"
    wrapClassName="ant-modal-cust-warp"
    style="top: 5%; height: 95%; overflow-x: auto; overflow: hidden"
  >
    <div style="padding-top: 0px">
      <fm-generate-form :data="startFormJson" ref="generateStartForm" :value="variables" :remote="remoteFuncs" disabled>
      </fm-generate-form>
      <a-tabs :animated="false" default-active-key="1" @change="callback">
        <a-tab-pane key="1" tab="附件">
          <div class="clearfix">
            <j-upload v-model="files" :number="5" style="padding-top: 20px"></j-upload>
          </div>
        </a-tab-pane>
        <!-- <a-tab-pane v-if="uid != 6" key="2" tab="关联配置项" force-render>

        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery">
            <a-row :gutter="24">
              <a-col :md="6" :sm="8">
                <a-form-item label="名称">
                  <a-input placeholder="请输入名称" v-model="queryParam.name" style="width: 94%"></a-input>
                </a-form-item>
              </a-col>
              <a-col :md="6" :sm="8">
                <a-form-item label="编号">
                  <a-input placeholder="请输入编号" v-model="queryParam.code" style="width: 94%"></a-input>
                </a-form-item>
              </a-col>
              <a-col :md="6" :sm="12" style="padding-left: 24px;">
                <a-button type="primary" @click="searchQuery">查询</a-button>
                <a-button type="primary" @click="searchReset" style="margin-left:15px">重置</a-button>
              </a-col>
            </a-row>
          </a-form>
        </div>
          <a-table
              ref="table"
              size="middle"
              bordered
              rowKey="id"
              :columns="columns"
              :dataSource="dataSource"
              :pagination="ipagination"
              :loading="loading"
              :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
              class="j-table-force-nowrap"
              @change="handleTableChange">
          </a-table>
        </a-tab-pane> -->
      </a-tabs>
    </div>
  </a-modal>
</template>
<script>
import { getAction, postAction } from '@/api/manage'
import JUpload from '@/components/jeecg/JUpload'
import { ajaxGetDictItems, getProgectList } from '@/api/api'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
export default {
  name: 'questionApplicationAdd',
  mixins: [JeecgListMixin],
  components: {
    JUpload,
  },
  data() {
    return {
      title: '操作',
      confirmLoading: false,
      processDefinition: { formKey: 'sy_question_form' },
      /* 弹框宽 */
      modalWidth: '1000px',
      form: this.$form.createForm(this),
      visible: false,
      required: false,
      startFormJson: undefined,
      variables: undefined,
      disabledValue: true,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      // 上传相关
      previewVisible: false,
      previewImage: '',
      files: '',
      //事件ID
      eventId: '',
      depCode: '',
      uid: '',
      remoteFuncs: {
        getType(resolve) {
          ajaxGetDictItems('question_type_sy', null).then((res) => {
            if (res.success) {
              const options = res.result
              resolve(options)
            }
          })
        },
        getProgect(resolve) {
          getProgectList(null).then((res) => {
            if (res.success) {
              const options = res.result
              resolve(options)
            }
          })
        },
      },
      // 关联配置项
      columns: [
        {
          title: '序号',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function (t, r, index) {
            return parseInt(index) + 1
          },
        },
        {
          title: '分类',
          align: 'center',
          dataIndex: 'configType_dictText',
        },
        {
          title: '编号',
          align: 'center',
          dataIndex: 'code',
        },
        {
          title: '名称',
          align: 'center',
          dataIndex: 'name',
        },
        {
          title: '单位名称',
          align: 'center',
          dataIndex: 'unitName',
        },
        {
          title: '项目名称',
          align: 'center',
          dataIndex: 'projectName',
        },
        {
          title: '合同名称',
          align: 'center',
          dataIndex: 'contractName',
        },
        {
          title: '状态',
          align: 'center',
          dataIndex: 'state_dictText',
        },
      ],
      dataSource: [],
      url: {
        list: '/itilconfigitemlibrary/itilConfigItemLibrary/list',
      },
    }
  },
  created() {
    this.variables = null
  },
  mounted() {
    if (this.processDefinition.formKey) {
      getAction('/flowableform/umpFlowableForm/queryByKey', {
        key: this.processDefinition.formKey,
        tableId: null,
      }).then((res) => {
        if (res.success) {
          var formData = res.result
          if (formData && formData.formJson) {
            this.startFormJson = JSON.parse(formData.formJson)
            this.variables = null
          }
        }
      })
    }
  },
  methods: {
    add(v, eventId, uid) {
      this.eventId = eventId
      this.processDefinition = v
      this.selectedRowKeys = []
      this.variables = null
      this.visible = true
      this.files = '' //文件
      this.uid = uid
      if (this.processDefinition.tableId) {
        this.getData()
      }
      if (null != v.busId) {
        getAction('/businessrelation/actZBusinessRelation/list', { processId: this.processDefinition.busId }).then(
          (res) => {
            if (res.success) {
              this.selectedRowKeys = res.result.itilConfigIds
              this.files = res.result.fileUrlList
            }
          }
        )
      }
      setTimeout(() => {
        this.modalWidth = '1000px'
      }, 300)
    },
    //获取数据
    getData() {
      if (this.processDefinition.formKey) {
        getAction('/flowableform/umpFlowableForm/queryByKey', {
          key: this.processDefinition.formKey,
          tableId: this.processDefinition.tableId,
        }).then((res) => {
          if (res.success) {
            var formData = res.result
            if (formData && formData.formJson) {
              // this.startFormJson = JSON.parse(formData.formJson)
              this.variables = JSON.parse(formData.formValue)
            }
          }
        })
      }
    },
    // 关闭弹框
    close() {
      this.$emit('close')
      this.visible = false
      this.current = 0
    },
    // 提交
    handleOk() {
      if (this.$refs.generateStartForm) {
        this.confirmLoading = true
        if (this.processDefinition.tableId) {
          this.$refs.generateStartForm
            .getData()
            .then((values) => {
              if (values && values != undefined) {
                let formData = Object.assign(this.data || {}, values)
                formData.procDefId = this.processDefinition.id
                formData.procDeTitle = this.processDefinition.name
                formData.form_value = JSON.stringify(values)
                //Object.assign({processInstanceFormData}, values)
                formData.filedNames = 'form_value' + ',' + 'form_key'
                formData.form_key = this.processDefinition.formKey
                formData.id = this.processDefinition.tableId
                formData.itilConfigIds = this.selectedRowKeys
                let faleUrl = ''
                if (this.files instanceof Array) {
                  for (var i = 0; i < this.files.length; i++) {
                    faleUrl = faleUrl + ',' + this.files[i]
                  }
                } else {
                  faleUrl = this.files
                }
                this.confirmLoading = false
                formData.file = faleUrl
                postAction('/busQuestion/editForm', formData).then((res) => {
                  this.uploading = false
                  if (res.success) {
                    this.$message.success('保存成功')
                    this.visible = false
                    this.$emit('ok')
                    this.confirmLoading = false
                  } else {
                    this.$message.warning(res.message)
                    this.visible = false
                    this.$emit('ok')
                    this.confirmLoading = false
                  }
                })
              }
            })
            .catch((e) => {
              this.confirmLoading = false
            })
        } else {
          this.$refs.generateStartForm
            .getData()
            .then((values) => {
              if (values && values != undefined) {
                let formData = Object.assign(this.data || {}, values)
                formData.form_value = JSON.stringify(values)
                //Object.assign({processInstanceFormData}, values)
                formData.filedNames = 'form_value' + ',' + 'form_key'
                formData.form_key = this.processDefinition.formKey
                formData.itilConfigIds = this.selectedRowKeys
                formData.file = this.files
                formData.depCode = this.depCode
                formData.eventId = this.eventId //绑定事件ID
                postAction('/busQuestion/add', formData).then((res) => {
                  this.uploading = false
                  if (res.success) {
                    this.$message.success('保存成功')
                    this.visible = false
                    this.$emit('ok')
                    this.confirmLoading = false
                  } else {
                    this.$message.warning(res.message)
                    this.visible = false
                    this.$emit('ok')
                    this.confirmLoading = false
                  }
                })
              }
            })
            .catch((e) => {
              this.confirmLoading = false
            })
        }
      }
    },
    handleCancel() {
      this.close()
      this.confirmLoading = false
    },
    // tab
    callback(key) {},
    // 上传相关
    onCancel() {
      this.previewVisible = false
    },
    async handlePreview(file) {
      if (!file.url && !file.preview) {
        file.preview = await getBase64(file.originFileObj)
      }
      this.previewImage = file.url || file.preview
      this.previewVisible = true
    },
    handleChange({ fileList }) {
      this.fileList = fileList
    },
  },
}
</script>
<style scoped lang="less">
::v-deep .ant-modal-body {
  padding: 24px 48px 24ox 48px;
}
::v-deep .ant-modal {
  padding: 24px;
}
@media (max-width: 1012px) {
  ::v-deep .ant-modal {
    top: 0px;
    width: 1000px !important;
    max-width: 1000px !important;
    margin: 0 !important;
  }

  .ant-modal {
    top: 0px;
    width: 1000px !important;
    max-width: 1000px !important;
    margin: 0 !important;
  }
}
</style>
