<template>
  <j-modal
    :title="title"
    :width="width"
    :centered="true"
    :visible="visible"
    :destroyOnClose="true"
    switchFullscreen
    cancelText="关闭"
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <j-form-container>
        <a-form-model slot="detail" ref="form" :model="model" :rules="validatorRules" v-bind="formItemLayout">
          <a-row>
            <a-col :span="24">
              <a-form-model-item label="业务名称" prop="businessName">
                <a-input
                  v-model="model.businessName"
                  :allow-clear="true"
                  autocomplete="off"
                  placeholder="请输入业务名称"
                />
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item label="业务标识" prop="businessCode">
                <a-input
                  :disabled="model.id?true:false"
                  v-model="model.businessCode"
                  :allow-clear="true"
                  autocomplete="off"
                  placeholder="请输入业务标识"
                />
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item label="业务类型" prop="businessType">
                <a-select
                  v-model="model.businessType"
                  :allow-clear="true"
                  autocomplete="off"
                  placeholder="请选择业务类型"
                >
                  <a-select-option
                    v-for="item in businessTypeList"
                    :key="item.value"
                    :label="item.text"
                    :value="item.value"
                  >
                    {{ item.text }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item label="访问地址" prop="businessAddress">
                <a-input
                  v-model="model.businessAddress"
                  :allow-clear="true"
                  autocomplete="off"
                  placeholder="请输入访问地址"
                />
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item label="业务描述" prop="businessRemark">
                <a-textarea
                  v-model="model.businessRemark"
                  :auto-size="{ minRows: 1, maxRows: 6 }"
                  :allowClear="true"
                  autocomplete="off"
                  placeholder="请输入描述信息"
                />
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item label="单位" prop="deptId"  ref='deptId'>
                <j-select-depart
                  v-model='model.deptId'
                  :placeholder="'请选择单位'"
                  @blur="() => {$refs.deptId.onFieldBlur()}"
                >
                </j-select-depart>
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item label="图片" prop="businessImage">
                <j-image-upload
                  v-model="model.businessImage"
                  :isMultiple='false'
                  :accept="accept"
                  :handle-change-additional-fun="handleChangeAdditionalFun"
                  :beforeUploadFun="beforeUploadFun"
                  >
                </j-image-upload>
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </j-form-container>
    </a-spin>
  </j-modal>
</template>
<script>
import { httpAction } from '@api/manage'
import { ajaxGetDictItems, getDictItemsFromCache } from '@api/api'
import {
  checkBeforeUpload,
  checkAccept,
  compareFileSizes
} from '@comp/yq/yqUpload/YqUploadCommonFuns.js'
export default {
  name: 'businessOverviewModal',
  props: {},
  data() {
    return {
      title: '新增',
      width: '800px',
      disableSubmit: false,
      visible: false,
      confirmLoading: false,
      formItemLayout: {
        labelCol: {sm:{span: 5} ,xs:{span:24} },
        wrapperCol: { sm:{span: 16} ,xs:{span:24}},
      },
      accept:'image/jpg,image/png, image/jpeg, image/jfif, image/pjp, image/pjpeg',
      acceptTips:'jpg、png、jpeg、jfif、pjp、pjpeg',
      model: {},
      businessTypeList: [],
      validatorRules: {
        businessName: [
          { required: true, message: '请输入业务名称!' },
          { min: 2, max: 50, message: '业务名称长度应在 2-50 之间！', trigger: 'blur' },
        ],
        businessCode: [
          { required: true, message: '请输入业务标识!' },
          { min: 2, max: 18, message: '业务标识长度应在 2-18 之间！', trigger: 'blur' },
        ],
        businessType: [{ required: true, message: '请选择业务类型!' }],
        businessAddress: [
          { required: true, message: '请输入访问地址!' },
          { max: 200, message: '访问地址长度不能超过200个字符！', trigger: 'blur' }],
        businessRemark: [{ required: false, validator: this.descriptionValidate }],
        deptId: [{ required: true, message: '请选择单位' }],
        businessImage: [{ required: true, message: '请上传图片' }],
      },
      url: {
        add: '/business/info/add',
        edit: '/business/info/edit',
      },
    }
  },
  created() {
    this.initDictData('businessTypeList', 'business_type')
  },
  methods: {
    initDictData(dictOption, dictCode) {
      if (dictCode != null && dictCode != '') {
        //优先从缓存中读取字典配置
        if (getDictItemsFromCache(dictCode)) {
          this[dictOption] = getDictItemsFromCache(dictCode)
          return
        }

        //根据字典Code, 初始化字典数组
        ajaxGetDictItems(dictCode, null).then((res) => {
          if (res.success) {
            this[dictOption] = res.result
          }
        })
      }
    },
    descriptionValidate(rule, value, callback) {
      if (value && value.length > 200) {
        callback('业务描述长度应在 0-200 之间')
      } else {
        callback()
      }
    },
    add() {
      this.edit({})
    },
    edit(record) {
      this.visible = true
      this.$nextTick(() => {
        this.model = Object.assign({}, record)
      })
    },
    close() {
      this.confirmLoading=false
      this.visible = false
    },
    handleOk() {
      this.$refs.form.validate((err, values) => {
        let that = this
        if (err&&that.confirmLoading===false) {
          that.confirmLoading =true
          let httpurl = ''
          let method = ''
          if (!that.model.id) {
            httpurl += that.url.add
            method = 'put'
          } else {
            httpurl += that.url.edit
            method = 'put'
          }
          let formData = JSON.parse(JSON.stringify(this.model))
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.close()
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
              that.confirmLoading = false
            })
            .catch((res) => {
              that.$message.warning(res.message)
              that.confirmLoading = false
            })
        }
      })
    },
    handleCancel() {
      this.close()
    },
    /**
     * 选择文件格式是否正确
     */
    beforeUploadFun(file) {
      let result = checkAccept(file, this.acceptTips,false)
      if (!result) {
        return result
      }
      return checkBeforeUpload(file, true, 10, 'MB',false)
    },
    /*文件发生改变后，附加处理方法，一次性上传多个文件，剔除列表中不满足条件的文件*/
    handleChangeAdditionalFun(fileList){
      let list = fileList
      if (fileList.length > 0) {
        list = fileList.filter((item) => {
          return compareFileSizes(true, 10, item.size, 'MB')
        }).filter((item) => {
          return checkAccept(item, this.acceptTips)
        })
      }
      return list
    }
  },
}
</script>
<style scoped lang='less'>
@import '~@assets/less/normalModal.less';
</style>