<template>
  <div class='zhl'>
    <!-- {{ productId }} -->
      <!-- 查询区域 -->
      <div class="table-page-search-wrapper">
        <a-form layout="inline" @keyup.enter.native="searchQuery">
          <a-row :gutter="24"> </a-row>
        </a-form>
      </div>
      <!-- 查询区域-END -->

      <!-- 操作按钮区域 -->
      <div class="table-operator table-operator-style">
        <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
        <a-dropdown v-if="selectedRowKeys.length > 0">
          <a-menu slot='overlay' style='text-align: center'>
            <a-menu-item key='1' @click='batchDel'>删除</a-menu-item>
          </a-menu>
          <a-button> 批量操作
            <a-icon type='down' />
          </a-button>
        </a-dropdown>
      </div>

      <!-- table区域-begin -->
      <div>
        <a-table
          ref="table"
          bordered
          :row-key="(record,index)=>{return record.id}"
          :columns="columns"
          :dataSource="dataSource"
          :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
          :pagination="ipagination"
          :loading="loading"
          :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          @change="handleTableChange"
        >
          <template slot="htmlSlot" slot-scope="text">
            <div v-html="text"></div>
          </template>
          <template slot="controlType" slot-scope="text">
            <div v-if="text == 'inputString'">文本输入框</div>
            <div v-if="text == 'inputNumber'">数字输入框</div>
            <div v-if="text == 'inputPassword'">密码输入框</div>
            <div v-if="text == 'inputSelect'">选择框</div>
          </template>
          <template slot="imgSlot" slot-scope="text">
            <span v-if="!text" style="font-size: 14px; ">无图片</span>
            <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width:80px;font-size: 14px; " />
          </template>
          <template slot="fileSlot" slot-scope="text">
            <span v-if="!text" style="font-size: 14px; ">无文件</span>
            <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
              下载
            </a-button>
          </template>

          <span slot="action" slot-scope="text, record" class="caozuo">
            <a @click="handleDetail(record)">查看</a>
            <a-divider type="vertical" />
            <a @click="handleEdit(record)">编辑</a>
            <a-divider type="vertical" />
            <a-popconfirm title="确定删除吗?" @confirm="() => deleteConnect(record.id)">
              <a>删除</a>
            </a-popconfirm>

            <!-- <a>删除</a> -->
          </span>
          <template slot='tooltip' slot-scope='text'>
            <a-tooltip placement='topLeft' :title='text' trigger='hover'>
              <span class='tooltip'>
                {{ text }}
              </span>
            </a-tooltip>
          </template>
        </a-table>
      </div>

      <device-connect-template-info-modal ref="modalForm" :productInfo='record' @ok="modalFormOk"></device-connect-template-info-modal>
  </div>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { httpAction, getAction, deleteAction } from '@/api/manage'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import DeviceConnectTemplateInfoModal from '../modules/DeviceConnectTemplateInfoModal'
import JSuperQuery from '@/components/jeecg/JSuperQuery.vue'
export default {
  name: 'ConnectInfo',
  mixins: [JeecgListMixin, mixinDevice],
  components: {
    DeviceConnectTemplateInfoModal,
    JSuperQuery
  },
  data() {
    return {
      productId: '',
      record: {},
      // 表头
      columns: [
        // {
        //   title: '#',
        //   dataIndex: '',
        //   key: 'rowIndex',
        //   width: 60,
        //   align: 'center',
        //   customRender: function (t, r, index) {
        //     return parseInt(index) + 1
        //   }
        // },
        {
          title: '标识',
          dataIndex: 'code',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 100px;max-width:300px'
            return { style: cellStyle }
          },
        },
        {
          title: '中文名',
          dataIndex: 'displayName',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 100px;max-width:300px'
            return { style: cellStyle }
          },
        },
        {
          title: '默认值',
          dataIndex: 'defaultValue',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 50px;max-width:300px'
            return { style: cellStyle }
          },
        },
        // {
        //   title: '校验参数',
        //   dataIndex: 'validatorRule',
        //   scopedSlots: { customRender: 'tooltip' },
        //   customCell: () => {
        //     let cellStyle = 'text-align: center;min-width: 50px;max-width:300px'
        //     return { style: cellStyle }
        //   },
        // },
        {
          title: '序号',
          dataIndex: 'numIndex',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 50px;max-width:300px'
            return { style: cellStyle }
          },
        },
        {
          title: '控件类型',
          dataIndex: 'controlType',
          scopedSlots: { customRender: 'controlType' },
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 50px;max-width:250px'
            return { style: cellStyle }
          },
        },
         {
          title: '传输协议',
          dataIndex: 'transferProtocol',
          scopedSlots: { customRender: 'tooltip' },
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 50px;max-width:300px'
            return { style: cellStyle }
          },
        },
        {
          title: '备注',
          dataIndex: 'remark',
          scopedSlots: { customRender: 'tooltip' },
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 100px;max-width:400px'
            return { style: cellStyle }
          },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 150,
          scopedSlots: { customRender: 'action' }
        }
      ],
      /* 排序参数 */
      isorter:{
        column: 'numIndex',
        order: 'desc',
      },
      url: {
        list: '/product/deviceConnectTemplateInfo/list',
        delete: '/product/deviceConnectTemplateInfo/delete',
        deleteBatch: '/product/deviceConnectTemplateInfo/deleteBatch',
        exportXlsUrl: '/product/deviceConnectTemplateInfo/exportXls',
        importExcelUrl: 'product/deviceConnectTemplateInfo/importExcel'
      },
      dictOptions: {},
      disableMixinCreated:true
    }
  },
  methods: {
    show(record) {
      this.$nextTick(()=>{
        this.record = record
        this.queryParam.productId = this.record.id
        this.ipagination.pageSize=10
        this.ipagination.current=1
        this.loadData()
      })
    },
    deleteConnect(id) {
      let that = this
      deleteAction(that.url.deleteBatch, { ids: id }).then(res => {
        if (res.success) {
          that.$message.success(res.message)
          that.loadData()
        } else {
          that.$message.warning(res.message)
        }
      })
    }
  }
}
</script>

<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
</style>
