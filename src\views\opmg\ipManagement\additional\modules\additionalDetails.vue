<template>
  <a-card style="height: 100%;">
    <a-row>
      <a-col :span="24">
        <span style="float: right;margin-bottom: 12px;"><img src="~@/assets/return1.png" alt="" @click="getGo"
            style="width: 20px;height: 20px;cursor: pointer"></span>
      </a-col>
      <a-col :span="24">
        <table class="gridtable">
          <tr>
            <td class="leftTd">字段名称</td>
            <td class="rightTd">{{ data.name }}</td>
            <td class="leftTd">字段标识</td>
            <td class="rightTd">{{ data.code }}</td>
          </tr>
          <tr>
            <td class="leftTd">字段类型</td>
            <td class="rightTd">{{ data.type }}</td>
            <td class="leftTd">字段描述</td>
            <td class="rightTd">{{data.remark}}</td>
          </tr>
          <tr v-if="data.type == '下拉框'">
            <td class="leftTd">数据字典</td>
            <td class="rightTd">{{ data.dictType }}</td>
          </tr>
        </table>
      </a-col>
    </a-row>
  </a-card>
</template>

<script>
  export default {
    name: 'additionalDetails',
    props: {
      data: {
        type: Object
      }
    },
    data() {
      return {
        form: this.$form.createForm(this),
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          },
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          },
        },
        confirmLoading: false,
      }
    },
    mounted() {},
    methods: {
      //返回上一级
      getGo() {
        this.$parent.pButton2(0);
      }
    }
  }
</script>
<style scoped>
  table.gridtable {
    font-family: verdana, arial, sans-serif;
    font-size: 14px;
    color: #606266;
    border-width: 1px;
    border-color: #e8e8e8;
    border-collapse: collapse;
    text-align: left;
    width: 100%;
  }

  table.gridtable td {
    border-width: 1px;
    border-style: solid;
    border-color: #e8e8e8;
  }

  .leftTd {
    width: 17%;
    background-color: #FAFAFA;
    padding: 16px 24px;
    text-align: center;
  }

  .rightTd {
    width: 35%;
    padding: 16px 24px;
  }
</style>