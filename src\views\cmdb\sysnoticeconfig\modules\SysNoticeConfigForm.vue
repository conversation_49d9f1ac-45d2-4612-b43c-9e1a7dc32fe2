<template>
  <a-spin :spinning='confirmLoading'>
    <j-form-container :disabled='formDisabled'>
      <a-form :form='form' slot='detail'>
        <a-row>
          <a-col :span='24'>
            <a-form-item class='two-words' label='通知名称' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <a-input v-decorator="['configName', validatorRules.assetsName]" placeholder='请输入通知名称'
                       :allowClear='true'
                       autocomplete='off'></a-input>
            </a-form-item>
          </a-col>
          <a-col :span='24'>
            <a-form-item label='通知类型' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <a-select placeholder='请选择通知类型' v-model='configType' :getPopupContainer='(node) => node.parentNode'
                        :allowClear='true' :disabled='!addFlag'>
                <a-select-option v-for='coupon in couponList' :key='coupon.value' :value='coupon.value'>{{
                    coupon.title
                  }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <div>
            <a-col :span='24' v-for='item in extendField[this.configType]' :key='item.id'>
              <a-form-item :label='item.fieldName' :labelCol='labelCol' :wrapperCol='wrapperCol'>
                <template v-if="item.fieldType !== 'select' && item.fieldType !== 'switch'">
                  <a-input :type="!!item.fieldType?item.fieldType:'text'" v-model='item.fieldValue'
                           :placeholder='item.placeholder' :allowClear='true' autocomplete='off'>{{ item.fieldValue }}
                  </a-input>
                </template>
                <template v-if="item.fieldType === 'switch'">
                  <a-switch unCheckedChildren='否' checkedChildren='是' v-model='item.fieldValue'></a-switch>
                </template>
                <template v-if="item.fieldType === 'select'">
                  <a-select v-if="configType === 'http'" v-model='item.fieldValue' placeholder='请选择请求方式'
                            :allowClear='true'>
                    <a-select-option value='GET'>GET</a-select-option>
                    <a-select-option value='POST'>POST</a-select-option>
                    <a-select-option value='PUT'>PUT</a-select-option>
                    <a-select-option value='DELETE'>DELETE</a-select-option>
                  </a-select>
                  <a-select v-if="configType === 'eBus'" v-model='item.fieldValue' placeholder='请选择文件类型'
                            :allowClear='true'>
                    <a-select-option value='DBA'>DBA</a-select-option>
                    <a-select-option value='FTP'>FTP</a-select-option>
                  </a-select>
                </template>
              </a-form-item>
            </a-col>
          </div>
          <a-col v-if='showFlowSubmitButton' :span='24' style='text-align: center'>
            <a-button @click='submitForm'>提 交</a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
import {
  httpAction,
  getAction
} from '@/api/manage'
import pick from 'lodash.pick'
import {
  validateDuplicateValue
} from '@/utils/util'
import JFormContainer from '@/components/jeecg/JFormContainer'
import {
  ajaxGetDictItems,
  getDictItemsFromCache
} from '@/api/api'
import {
  mixinDevice
} from '@/utils/mixin'
import {
  JeecgListMixin
} from '@/mixins/JeecgListMixin'

export default {
  name: 'SysNoticeConfigForm',
  mixins: [JeecgListMixin, mixinDevice],
  components: {
    JFormContainer
  },
  props: {
    //流程表单data
    formData: {
      type: Object,
      default: () => {
      },
      required: false
    },
    //表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false
    },
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  data() {
    return {
      sslEnable: false,
      changepassword: '',
      form: this.$form.createForm(this),
      model: {},
      addFlag: false,
      labelCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 5
        }
      },
      wrapperCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 16
        }
      },
      confirmLoading: false,
      url: {
        add: '/sys/notice/config/add',
        edit: '/sys/notice/config/edit',
        queryById: '/sys/notice/config/queryById',
        list: '/sysNoticeExtend/sysNoticeExtend/findUserExtend'
      },

      dataSource: [],
      couponList: [],
      validatorRules: {
        assetsName: {
          rules: [{
            required: true,
            message: '请输入通知名称!'
          }, {
            min: 1,
            max: 50,
            message: '通知名称长度应在1-50个字符内'
          }]
        }
      },
      configType: '',
      configName: '',
      extendList: [],
      extendField: {
        email: [
          {
          fieldName: '服务器地址',
          fieldCode: 'url',
          fieldValue: '',
          placeholder: '请输入服务器地址'
        },
          {
            fieldName: '端口号',
            fieldCode: 'port',
            fieldValue: '',
            placeholder: '请输入端口号'
          },
          {
            fieldName: '发件人',
            fieldCode: 'from',
            fieldValue: '',
            placeholder: '请输入发件人'
          },
          {
            fieldName: '用户名',
            fieldCode: 'username',
            fieldValue: '',
            placeholder: '请输入用户名'
          },
          {
            fieldName: '密码',
            fieldCode: 'password',
            fieldType: 'password',
            fieldValue: '',
            placeholder: '请输入密码'
          },
          {
            fieldName: '是否开启SSL链接',
            fieldCode: 'sslEnable',
            fieldType: 'switch',
            fieldValue: false
          }
        ],
        message: [{
          fieldName: 'AccessKey ID',
          fieldCode: 'ID',
          fieldValue: '',
          placeholder: '请输入AccessKey ID'
        },
          {
            fieldName: 'AccessKey Secret',
            fieldCode: 'secret',
            fieldValue: '',
            placeholder: '请输入AccessKey Secret'
          },
          {
            fieldName: '访问域名',
            fieldCode: 'https',
            fieldValue: '',
            placeholder: '请输入访问域名'
          }
        ],
        dingding_robot: [
          {
          fieldName: 'Webhook',
          fieldCode: 'webhook',
          fieldValue: '',
          placeholder: '请输入Webhook'
        },
          {
            fieldName: '密钥',
            fieldCode: 'secret',
            fieldValue: '',
            placeholder: '请输入密钥'
          }
        ],
        wxWork: [
          {
          fieldName: 'Webhook',
          fieldCode: 'webhook',
          fieldValue: '',
          placeholder: '请输入Webhook'
        }
        ],
        weixin: [
          {
          fieldName: 'token',
          fieldCode: 'token',
          fieldValue: '',
          placeholder: '请输入token'
        },
          {
            fieldName: 'appId',
            fieldCode: 'appid',
            fieldValue: '',
            placeholder: '请输入appId'
          },
          {
            fieldName: 'appSecret',
            fieldCode: 'secret',
            fieldValue: '',
            placeholder: '请输入appSecret'
          },
          {
            fieldName: '域名网址',
            fieldCode: 'domainUrl',
            fieldValue: '',
            placeholder: '请输入域名网址'
          }
        ],
        http: [
          {
          fieldName: 'REST服务地址',
          fieldCode: 'url',
          fieldType: 'text',
          fieldValue: '',
          placeholder: '请输入REST服务地址'
        }, {
          fieldName: '请求方式',
          fieldCode: 'method',
          fieldType: 'select',
          fieldValue: '',
          placeholder: '请选择请求方式'
        }],
        message_telecom: [
          {
          fieldName: '发送地址',
          fieldCode: 'url',
          fieldType: 'text',
          fieldValue: '',
          placeholder: '请输入REST服务地址'
        }, {
          fieldName: '账户',
          fieldCode: 'accountId',
          fieldType: 'text',
          fieldValue: '',
          placeholder: '请输入账户'
        }, {
          fieldName: '密码',
          fieldCode: 'password',
          fieldType: 'password',
          fieldValue: '',
          placeholder: '请输入密码'
        }, {
          fieldName: '产品编码',
          fieldCode: 'productId',
          fieldType: 'text',
          fieldValue: '',
          placeholder: '请输入产品编码'
        }],
        eBus: [
          {
          fieldName: '请求地址',
          fieldCode: 'url',
          fieldType: 'text',
          fieldValue: '',
          placeholder: '请输入请求地址'
        },
          {
            fieldName: 'token',
            fieldCode: 'token',
            fieldType: 'text',
            fieldValue: '',
            placeholder: '请输入token'
          },
          {
            fieldName: '文件类型',
            fieldCode: 'fileType',
            fieldType: 'select',
            fieldValue: '',
            placeholder: '请输入文件类型(DBA/FTP)'
          }
        ]
      }
    }
  },
  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        return this.formData.disabled !== false
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    }
  },
  created() {
    //如果是流程中表单，则需要加载流程表单data
    this.showFlowData()
    this.initDictData()
  },
  methods: {
    initDictData() {
      if (getDictItemsFromCache('noticeType')) {
        this.couponList = getDictItemsFromCache('noticeType')
        return
      }
      //根据字典Code, 初始化字典数组
      ajaxGetDictItems('noticeType', null).then((res) => {
        if (res.success) {
          this.couponList = res.result
        }
      })
    },
    add() {
      this.edit({})
    },
    queryById(record) {
      this.$nextTick(() => {
        this.form.setFieldsValue(pick(
          this.model
          // 'id',
          // 'type'
        ))
      })
      this.model = Object.assign({}, record)
      getAction(this.url.queryById, {
        id: record.id
      }).then((res) => {
        if (res.success) {
          //this.result = res.result.records
          this.form.setFieldsValue(pick({
              configType: res.result.configType,
              configName: res.result.configName
            },
            //'type',
            'configName'
            //'id'
          ))
          this.configType = res.result.configType
          if (this.extendField[this.configType] != null && this.extendField[this.configType].length > 0) {
            const localExtend = JSON.parse(JSON.stringify(this.extendField[this.configType]))
            const extend = res.result.extend
            this.packedExtendFields(extend, localExtend)
            this.extendField[this.configType] = localExtend
          }
        }
      })
    },
    packedExtendFields(resExtend, localExtend) {
      for (let resItem of resExtend) {
        if (resItem.fieldType === 'switch') {
          resItem.fieldValue = resItem.fieldValue === 'true'
        }
        for (let localIndex in localExtend) {
          let localItem = localExtend[localIndex]
          if (localItem.fieldCode === resItem.fieldCode) {
            localExtend[localIndex] = resItem
            break
          }
        }
      }
    },
    edit(record) {
      if (!this.model.id) {
        this.addFlag = true
      }
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.configType = record.configType
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(pick(
          this.model,
          'configName'
          //'type'
        ))
      })
    },
    //渲染流程表单数据
    showFlowData() {
      if (this.formBpm === true) {
        let params = {
          id: this.formData.dataId
        }
        getAction(this.url.queryById, params).then((res) => {
          if (res.success) {
            this.edit(res.result)
          }
        })
      }
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let extendField = []
          if (this.extendField[this.configType] != null) {
            this.extendField[this.configType].forEach((ele) => {
              extendField.push({
                fieldName: ele.fieldName,
                fieldValue: ele.fieldValue,
                fieldCode: ele.fieldCode,
                fieldType: ele.fieldType
              })
            })
          }
          let formData = Object.assign(this.model, values)
          formData.extend = extendField
          formData.configType = this.configType
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    popupCallback(row) {
      this.form.setFieldsValue(pick(
        row,
        'configName'
        // 'type'
      ))
    }
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';

.form-row {
  display: flex;
  margin: 0px 0px !important;
  align-items: center;
  height: 60px;
  background-color: white;
}

.form-col {
  height: 34px;
}

.div-table-container {
  padding: 10px 5px 0 10px;
  background-color: white;
  margin-top: 10px;
  margin-right: -9px;
  height: calc(100% - 150px);
}

.gutter-example {
  background-color: #ececec;
  margin-bottom: 10px;
}

.gutter-row {
  padding-right: 0px !important;
}

.gutter-example >>> .ant-row > div {
  background: transparent;
  border: 0;
}

.gutter-box {
  background: white;
  padding: 5px 0;
}

.p-device-status {
  text-align: center;
  height: 30px;
  line-height: 30px;
  margin-bottom: 0px;
}

.span-title {
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
}

.span-num {
  font-family: PingFangSC-Medium;
  font-size: 24px;
}

.color-blue {
  color: #409eff;
}

.color-green {
  color: #139b33;
}

.color-red {
  color: #df1a1a;
}

.color-grey {
  color: #868686;
}

.table-page-search-wrapper {
  margin-right: -9px;
}

::v-deep .two-words > div > label {
  letter-spacing: 4px;
}

::v-deep .two-words > div > label::after {
  letter-spacing: 0px;
}
</style>