<template>
  <a-row style='height: 100%' >
    <a-col v-if='!queryId' :lg='6' :md='8' :sm='10' :xl='4' :xs='10' style='height: 100%; background: #fff'>
      <device-tree-expand :arr-type="[null, 'product']" :btnIconName="'appstore'"
                          :btnName="'全部设备'" :icon-name="['menu', 'bars']"
                          :inputFlag='true' :is-show-all-btn='true' :is-show-btn-icon='true'
                          :is-show-icon='true'
                          :tree-url="'/assetscategory/assetsCategory/selectTree'"
                          @selected='treeSeletedSearch'></device-tree-expand>
    </a-col>
    <a-col v-bind='rightSpan' style='height: 100%; overflow: hidden; overflow-y: auto'>
      <a-row style='height: 100%;margin-right: 4px' :style='{marginLeft: queryId?"0px":"16px"}'>
        <a-col style='width: 100%; height: 100%; display: flex; flex-direction: column'>
          <div class='gutter-example' v-if='!queryId'>
            <device-statistic
              :col-span='{xxl: 6,xl: 6,lg: 12,md: 24,sm:24,xs: 24}'
              :device-info='deviceInfo'
              :show-sync='true'
              @executeSync='getDeviceInfoMap(true)'>
            </device-statistic>
          </div>
          <!-- 查询区域 -->
          <a-card :bodyStyle="{ paddingBottom: '0', marginRight: '12px' }" :bordered='false' class='card-style'
                  style='width: 100%'>
            <div class='table-page-search-wrapper'>
              <a-form layout='inline' v-bind='formItemLayout' @keyup.enter.native='searchQuery'>
                <a-row ref='row' :gutter='24'>
                  <a-col :span='spanValue'>
                    <a-form-item label='设备名称'>
                      <a-input :maxLength='maxLength' v-model='queryParam.name' :allowClear='true' autocomplete='off'
                               placeholder='请输入设备名称' />
                    </a-form-item>
                  </a-col>
                  <a-col :span='spanValue'>
                    <a-form-item label='IP地址'>
                      <a-input :maxLength='maxLength' v-model='queryParam.ip' :allowClear='true' autocomplete='off'
                               placeholder='请输入IP地址' />
                    </a-form-item>
                  </a-col>
                  <a-col :span='spanValue'>
                    <a-form-item label='在线状态'>
                      <j-dict-select-tag v-model='queryParam.status' dictCode='device_status'
                                         placeholder='请选择在线状态' />
                    </a-form-item>
                  </a-col>
                  <a-col v-show='toggleSearchStatus' :span='spanValue'>
                    <a-form-item label='告警状态'>
                      <j-dict-select-tag v-model='queryParam.alarmStatus' dictCode='device_alarm_status'
                                         placeholder='请选择告警状态' />
                    </a-form-item>
                  </a-col>
                  <a-col v-show='toggleSearchStatus' :span='spanValue'>
                    <a-form-item label='启用状态'>
                      <j-dict-select-tag v-model='queryParam.enable' dictCode='device_enable_status'
                                         placeholder='请选择启用状态' />
                    </a-form-item>
                  </a-col>
                  <!-- <a-col v-show='toggleSearchStatus' :span='spanValue'>
                    <a-form-item label='所属平台'>
                      <a-select v-model='queryParam.platformCode' :allowClear='true'
                        :getPopupContainer='(target) => target.parentNode'
                        optionFilterProp='children' placeholder='请选择所属平台' showSearch>
                        <a-select-option v-for='(item, index) in platformList' :key='index'
                          :value='item.selfPlatformCode'>
                          {{ item.selfDepartName }}
                        </a-select-option>
                      </a-select>
                    </a-form-item>
                  </a-col> -->
                  <a-col v-show='toggleSearchStatus' :span='spanValue'>
                    <a-form-item label='标签'>
                      <a-select v-model='queryParam.tagKeys'
                                :allowClear='true'
                                :getPopupContainer='(target) => target.parentNode'
                                :maxTagCount='1'
                                :maxTagTextLength='5'
                                mode='multiple'
                                optionFilterProp='children'
                                placeholder='请选择标签'
                                showSearch>
                        <a-select-option v-for='(item, index) in taginfoSelectData' :key='index' :value='item.tagKey'>
                          {{ item.tagName }}
                        </a-select-option>
                      </a-select>
                    </a-form-item>
                  </a-col>
                  <a-col :span='colBtnsSpan()'>
                    <span :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                          class='table-page-search-submitButtons'>
                      <a-button class='btn-search btn-search-style' type='primary' @click='dosearch'>查询</a-button>
                      <a-button class='btn-reset btn-reset-style' @click='searchReset'>重置</a-button>
                      <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                        {{ toggleSearchStatus ? '收起' : '展开' }}
                        <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                      </a>
                    </span>
                  </a-col>
                </a-row>
              </a-form>
            </div>
          </a-card>
          <a-card :bordered='false' style='width: 100%; flex: auto'>
            <div class='table-operator table-operator-style'>
              <a-button v-if='!queryId' @click='handleAdd'>新增</a-button>
              <a-button @click="handleExportXls('设备信息表')">导出</a-button>
              <a-dropdown v-if='selectedRowKeys.length > 0'>
                <a-menu slot="overlay" style='text-align: center'>
                  <a-menu-item key='1' @click='batchDel'>删除</a-menu-item>
                </a-menu>
                <a-button> 批量操作
                  <a-icon type='down' />
                </a-button>
              </a-dropdown>
              <a-button v-if='selectedRowKeys.length > 0' @click='batchEnableOperate(1)'>启用</a-button>
              <a-button v-if='selectedRowKeys.length > 0' @click='batchEnableOperate(0)'>禁用</a-button>
              <a-popover title='监控状态说明'>
                <template slot='content'>
                  <div style='display: flex'>
                    <div>
                      <span>在线：</span>
                      <img alt='' class='stateImg' src='~@assets/bigScreen/28.png' />
                    </div>
                    <div class='stateBox'>
                      <span>离线：</span>
                      <img alt='' class='stateImg' src='~@assets/bigScreen/57.png' />
                    </div>
                    <div class='stateBox'>
                      <span>告警：</span>
                      <img alt='' class='stateImg' src='~@assets/bigScreen/56.png' />
                    </div>
                    <div class='stateBox'>
                      <span style='margin-left: 5px'>禁用：</span>
                      <a-icon class='stateImg' style='font-size: 16px' theme='twoTone' two-tone-color='#eb2f96'
                              type='stop' />
                      <span style='margin-left: 5px'></span>
                    </div>
                  </div>
                </template>
                <a-icon style='font-size: 18px' theme='twoTone' type='question-circle' />
              </a-popover>
            </div>
            <a-table
              style='overflow: hidden'
              ref='table'
              class="terminal-table"
              :columns='columns'
              :dataSource='dataSource'
              :loading='loading'
              :pagination='ipagination'
              :row-key='(record, index) => {return record.id}'
              :rowSelection='{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }'
              :scroll="dataSource.length > 0 ? { x: true } : {}"
              bordered
              @change='handleTableChange'>
              <div slot='filterDropdown'>
                <a-card>
                  <div style='text-align: right;font-size: small;color: #AAAAAC'>拖拽可进行排序</div>
                  <a-divider style='margin: 5px 0px 5px 0px'></a-divider>
                  <a-checkbox-group
                    v-model='settingColumns'
                    @change='onColSettingsChange'
                    :key="refreshKey">
                    <a-row style='width: 170px;max-height:350px;overflow-y: auto'>
                      <a-col v-for='(item, idx) in defColumns'
                             v-if="item.key!=='rowIndex'&&item.dataIndex !== 'action'"
                             :span='24' :key='"defColumns_"+idx' @drop='drop($event,idx)' @dragover='allowDrop($event)'>
                        <div draggable='true' @dragstart='dragStart($event,idx)' @dragend='dragEnd($event,idx)'
                             :key="'defColumns_draggable_'+idx">
                          <a-checkbox :disabled='item.disabled' :value='item.dataIndex' :key='"defColumns_checkbox_"+idx'>
                            {{ item.title }}
                          </a-checkbox>
                        </div>
                      </a-col>
                    </a-row>
                  </a-checkbox-group>
                </a-card>
              </div>
              <a-icon slot='filterIcon' :style="{ fontSize: '16px', color: '#108ee9' }" title='动态列表显示'
                      type='setting' />

              <template slot='htmlSlot' slot-scope='text' width='500'>
                <div v-html='text'></div>
              </template>
              <template slot='imgSlot' slot-scope='text'>
                <span v-if='!text' style='font-size: 14px'>无图片</span>
                <img v-else :src='getImgView(text)' alt='' height='25px' style='max-width: 80px; font-size: 14px' />
              </template>

              <template slot='status' slot-scope='text, record'>
                <span v-if='record.enable == 1'>
                  <img v-if='record.status==1' alt='' class='stateImg' src='~@assets/bigScreen/28.png' />
                  <img v-else alt='' class='stateImg' src='~@assets/bigScreen/57.png' />
                  <img v-if='record. alarmStatus==1' alt='' class='stateImg alarmStatus'
                       src='~@assets/bigScreen/56.png' />
                </span>
                <span v-else>
                  <a-icon class='stateImg' style='font-size: 16px' theme='twoTone' two-tone-color='#eb2f96'
                          type='stop' />
                </span>
                <span style='margin-left: 10px'>{{ text }}</span>
              </template>
              <template slot='isFocus' slot-scope='text, record'>
                <span v-if="text==1" style="color:rgb(64, 222, 90)">是</span>
                <span v-else>否</span>
              </template>

              <span slot='action' slot-scope='text, record' class='caozuo'>
                <a @click='handleDetailPage(record)'>查看</a>
                <a-divider type='vertical' />
                <a-dropdown>
                  <a class='ant-dropdown-link'>更多
                    <a-icon type='down' /></a>
                  <a-menu slot='overlay'>
                    <a-menu-item>
                      <a class='overlay' @click='bindaTag(record)'>标签</a>
                    </a-menu-item>
                    <a-menu-item v-if='!queryId'>
                      <a class='overlay' @click='handleEdit(record)'>编辑</a>
                    </a-menu-item>
                    <a-menu-item
                      v-if="record.gatewayName!= '' && record.gatewayName != null && record.gatewayName != undefined">
                      <a class='overlay' @click='getUntie(record)'>解绑</a>
                    </a-menu-item>
                    <a-menu-item>
                      <a class='overlay' @click='deleteRecord(record)'>删除</a>
                    </a-menu-item>
                    <a-menu-item>
                      <a v-if='record.enable === 1' class='overlay' @click='deviceEnable(record)'>禁用</a>
                      <a v-else class='overlay' @click='deviceEnable(record)'>启用</a>
                    </a-menu-item>
                    <a-menu-item v-if="record.enable === 1&&record.status==1">
                      <a class='overlay' @click='sshRemoteOperation(record)'>远程管理</a>
                    </a-menu-item>
                     <a-menu-item v-if="record.webManageUrl">
                      <a class='overlay' @click='openWebManageUrl(record.webManageUrl)'>Web管理</a>
                    </a-menu-item>
                  </a-menu>
                </a-dropdown>
              </span>
              <template slot='Enable' slot-scope='text, record'>
                <div v-if='record.enable === 1'>
                  <!-- <a-icon type="check-circle" theme="twoTone" two-tone-color="#52c41a" /> -->
                  <a-icon style='font-size: 20px' theme='twoTone' two-tone-color='#52c41a' type='pause-circle' />
                  <!-- <span style="margin-left: 5px">{{record.name}}</span> -->
                </div>
                <div v-else>
                  <!-- <a-icon type="stop" theme="twoTone" two-tone-color="#eb2f96"/> -->
                  <a-icon style='font-size: 20px' theme='twoTone' two-tone-color='#eb2f96' type='play-circle' />
                  <!-- <span style="margin-left: 5px">{{record.name}}</span> -->
                </div>
              </template>
              <template slot='tooltip' slot-scope='text'>
                <a-tooltip :title='text' placement='topLeft' trigger='hover'>
                  <div class='tooltip'>
                    {{ text }}
                  </div>
                </a-tooltip>
              </template>
              <template slot='tagInfoList' slot-scope='text'>
                <a-popover title='标签'>
                  <template slot='content'>
                    <div v-for='item in text' :key='item.id' style='margin: 5px 0'>
                      <span :style="{
                          'background-color': item.tagColor,
                          color: 'white',
                          'border-radius': '10px',
                          padding: '2px 10px',
                        }">
                        {{ item.tagName }}
                      </span>
                    </div>
                  </template>
                  <a-icon type='environment' />
                  <!-- <div style="display:flex,direction:column">
                    <p v-for="item in text"
                      :style="{'background-color':item.tag_color, color:'white',padding:'2px 1px',}">
                      {{item.tag_name}}
                      </p>
                  </div> -->
                </a-popover>
              </template>
              <template slot='number' slot-scope='text,record,index'>
                <div>{{ calcNumber(index) }}</div>
              </template>
            </a-table>
          </a-card>
        </a-col>
      </a-row>
    </a-col>

    <device-info-modal ref='modalForm' @ok='modalFormOk'></device-info-modal>
    <device-tag-modal ref='deviceTagModal' @tagOk='tagOk'></device-tag-modal>
    <ssh-remote-management-modal ref="sshRemoteManagementModal" ></ssh-remote-management-modal>
  </a-row>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import DeviceInfoModal from './modules/DeviceInfoFormModal.vue'
import {
  httpAction,
  getAction,
  postAction,
  deleteAction
} from '@/api/manage'
import JDictSelectTag from '@/components/dict/JDictSelectTag.vue'
//引入公共devicetree组件
import DeviceTreeExpand from '@/components/tree/DeviceTreeExpand.vue'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
import deviceTagModal from './modules/DeviceTagModal.vue'
import deviceStatistic from '@views/devicesystem/modules/DeviceStatistic.vue'
import Vue from 'vue'
import {addCancelDebounce} from '@/utils/util'
import sshRemoteManagementModal from '@views/devicesystem/deviceshow/SSHRemoteManagementModal.vue'
export default {
  name: 'DeviceInfoList',
  mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
  components: {
    DeviceInfoModal,
    JDictSelectTag,
    'device-tree-expand': DeviceTreeExpand,
    deviceTagModal,
    deviceStatistic,
    sshRemoteManagementModal
  },
  data() {
    return {
      maxLength:50,
      description: '设备表管理页面',
      //设备数量
      deviceInfo: [
        {
          type: '全部设备',
          count: '',
          img: require('@/assets/01.png')
        },
        {
          type: '在线设备',
          count: '',
          img: require('@/assets/02.png')
        },
        {
          type: '离线设备',
          count: '',
          img: require('@/assets/03.png')
        },
        {
          type: '未启用设备',
          count: '',
          img: require('@/assets/04.png')
        }],
      /* deviceInfo: {
         allNum: '',
         outNum: '',
         onNum: '',
         disabledNum: ''
       },*/
      formItemLayout: {
        labelCol: { style: 'width:80px' },
        wrapperCol: {
          style: 'width:calc(100% - 80px)'
        }
      },
      // platformList:[],
      taginfoSelectData: [],
      // 表头
      columns: [],
      //列设置
      settingColumns: [],
      //列表默认显示的所有字段
      defColumns: [
        {
          title: '序号',
          dataIndex: '',
          disabled:false,
          width: 60,
          key: 'rowIndex',
          scopedSlots: {
            customRender: 'number'
          }
        },
        {
          title: '设备名称',
          dataIndex: 'name',
          checked:true,//列表是否默认显示
          disabled:false,//列操作中，checkbox是否可操作
          scopedSlots: {
            customRender: 'status'
          },
          customCell: () => {
            let cellStyle = 'text-align: left'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '设备标识',
          dataIndex: 'deviceCode',
          checked:false,
          disabled:false,
        },
        {
          title: 'IP地址',
          dataIndex: 'ip',
          checked:true,
          disabled:false,
        },
        {
          title: '产品名称',
          dataIndex: 'productName',
          checked:true,
          disabled:false,
        },
        {
          title: '通信协议',
          dataIndex: 'transferProtocol',
          checked:false,
          disabled:false,
        },
        {
          title: '所属网关',
          dataIndex: 'gatewayName',
          checked:false,
          disabled:false,
          customRender: (text, record, index) => {
            if (record.gatewayName == '' || record.gatewayName == null || record.gatewayName == undefined||record.gatewayName.length == 0) {
              return '未绑定网关'
            } else {
              return text
            }
          }
        },
        {
          title: '关联资产',
          dataIndex: 'assetsName',
          checked:true,
          disabled:false,
        },
        {
          title: '标签',
          dataIndex: 'tagInfoList',
          checked:false,
          disabled:false,
          scopedSlots: {
            customRender: 'tagInfoList'
          }
        },
        /*{
          title: '创建人',
          dataIndex: 'createBy',
           checked:false,
          disabled:false,
        },*/
        {
          title: '添加时间',
          dataIndex: 'createTime',
          checked:true,
          disabled:false,
        },
        {
          title: '数据来源',
          dataIndex: 'platformCode',
          checked:true,
          disabled:false,
          customRender: (text, record, index) => {
            return this.sysPlatformcode === text?'本地数据':'上报数据'
          }
        },
       /* {
          title: '单位名称',
          dataIndex: 'momgDeptName',
           checked:false,
          disabled:false,
        },*/
        {
          title: '管理人',
          dataIndex: 'manager_dictText',
          checked:false,
          disabled:false,
        },
        {
          title: '运维人',
          dataIndex: 'operator_dictText',
          checked:false,
          disabled:false,
        },
        {
          title: '是否重点关注',
          dataIndex: 'isFocus',
          checked:false,
          disabled:false,
          scopedSlots: {
            customRender: 'isFocus'
          },
        },
        {
          title: '设备说明',
          dataIndex: 'description',
          checked:false,
          disabled:false,
          scopedSlots: {
            customRender: 'tooltip'
          },
          customCell: () => {
            let cellStyle = 'text-align: left;max-width:400px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '操作',
          dataIndex: 'action',
          display:true,
          disabled:false,
          align: 'center',
          fixed: 'right',
          width: 140,
          scopedSlots: {
            filterDropdown: 'filterDropdown',
            filterIcon: 'filterIcon',
            customRender: 'action'
          }
        }
      ],
      //刷新列表操作设置
      refreshKey: 0,
      dragItemIndex: 0,
      dragDom: null,
      isDown: 1,

      url: {
        // detachTags: '/utl/tagresource/detachTags', //解绑标签
        list: '/device/deviceInfo/list',
        delete: '/device/deviceInfo/delete',
        deleteBatch: '/device/deviceInfo/deleteBatchDevice',
        exportXlsUrl: '/device/deviceInfo/exportXls',
        importExcelUrl: 'device/deviceInfo/importExcel',
        deviceEnable: '/device/deviceInfo/updateEnable',
        batchEnable: '/device/deviceInfo/batchUpdateEnable',
        deviceInfoUrl: '/device/deviceInfo/showCounts',
        getUrl: '/device/deviceInfo/getUrl',
        unbund: '/gateway/deviceInfo/Unbundling', //解绑
        childUrl: '/gateway/deviceInfo/queryGatewayChildById',
        // getPlatform: '/dataReport/manage/list',
      },
      disableMixinCreated: true,
      queryParam: {
        // dictCode : 'data_dict_key_netProductCategory',
      },
      sysPlatformcode:'',
      // exportFields:["name","status","alarmStatus","enable" ,"ip", "productName","transferProtocol","gatewayName","createTime","assetsName","tagInfoList","description"]
      debounceCalWidth:null,
      queryId:'',
      rightSpan:{
        lg:18,
        md:16,
        sm:14,
        xl:20,
        xs:14
      }
      // :lg='18' :md='16' :sm='14' :xl='20' :xs='14'
    }
  },
  computed: {
    importExcelUrl: function() {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    }
  },
  created() {
    ///?productId=1475308459648892929

    this.getPlatformCode()
    this.getDeviceInfoMap()
    this.queryAllTags()
    this.initColumns()
    // this.getPlatform()
  },
  watch:{
    "$route":{
      immediate:true,
      deep:true,
      handler(){
        this.queryId = this.$route.meta.query?.productId
        if(this.queryId){
          this.rightSpan={span:24}
          this.queryParam.option = this.queryId
        }
        // console.log("router",this.$route,this.queryId)
        this.toggleSearchStatus=false
        this.searchReset()
      }
    }
  },
  mounted(){
    this.debounceCalWidth=addCancelDebounce(this.setColumnsWidth.bind(this),500)
    window.addEventListener("resize", this.debounceCalWidth)
  },
  beforeDestroy(){
    if (this.debounceCalWidth) {
      window.removeEventListener("resize", this.debounceCalWidth);
      this.debounceCalWidth.cancel(); // 取消计时器
    }
  },
 /* activated() {
    this.loadData()
  },*/
  methods: {
    /**web管理跳转页面*/
    openWebManageUrl(url){
      window.open(url,'_blank')
    },
    //获取系统数据来源
 getPlatformCode() {
   getAction("/data/reportAndConverge/getPlatformCode").then(res=>{
     if(res.success){
       this.sysPlatformcode = res.result
     }
   })
 },
    // getPlatform() {
    //   let params = {
    //     levelType: '0',
    //     pageNo: 1,
    //     pageSize: -1
    //   }
    //   getAction(this.url.getPlatform, params).then((res) => {
    //     if (res.success) {
    //       this.platformList = res.result.records
    //     }
    //   })
    //  },
    /*读取缓存，设置表头*/
    initColumns() {
      //获取默认列表的缓存dataIndex数据集合
      var key1 = this.$route.name + ':allCols'
      let allCols = Vue.ls.get(key1)

      // 提取 defColumns 的第一个和最后一个元素
      const firstItem =JSON.parse(JSON.stringify(this.defColumns[0]))
      const lastItem =JSON.parse(JSON.stringify(this.defColumns[this.defColumns.length - 1]))
      //缓存数据集合不为空---根据缓存顺序，重新给数组defColumns元素排序
      if (allCols) {
        let indexArr = []
        // 根据缓存顺序重新排序 defColumns
        const sortedColumns = allCols
          .map(dataIndex => this.defColumns.find(item => item.dataIndex === dataIndex))
          .filter(item => item) // 过滤掉 undefined（未找到的项）
        indexArr = sortedColumns.map(item => item.dataIndex)
        // 将第一个元素插入到排序后的数组开头
        sortedColumns.unshift(firstItem)

        // 过滤出未在缓存中出现的列
        const remainingColumns = this.defColumns.filter(
          item => !allCols.includes(item.dataIndex) && item.key !== firstItem.key && item.dataIndex !== lastItem.dataIndex
        )
        if (remainingColumns.length > 0) {
          let indexRemainIndexArr = remainingColumns.map(item => item.dataIndex)
          indexArr.push(...indexRemainIndexArr)
        }
        Vue.ls.set(key1, indexArr)
        // 将剩余列和最后一个元素插入到排序后的数组末尾
        if (remainingColumns.length > 0) {
          sortedColumns.push(...remainingColumns)
        }
        sortedColumns.push(lastItem)

        // 更新 defColumns
        this.defColumns = sortedColumns
      }
      //缓存数据集合为空时，按照data中定义的defColumns列表顺序写缓存
      else {
        let tempDataIndex = []
        this.defColumns.forEach(item => {
          if (item.dataIndex != 'action' && item.key != 'rowIndex') {
            tempDataIndex.push(item.dataIndex)
          }
        })
        Vue.ls.set(key1, tempDataIndex)
      }

      //从缓存中获取已选且在列表中显示的dataIndex数据
      var key2 = this.$route.name + ':colsettings'
      let colSettings = Vue.ls.get(key2)
      //已选显示列表缓存dataIndex数据集合为空
      if (!colSettings||colSettings.length==0) {
        let allSettingColumns = []
        let columns = []
        this.defColumns.forEach(function(item, i, array) {
          delete item.fixed
          if (item.checked) {
            allSettingColumns.push(item.dataIndex)
            columns.push(item)
          }
        })
        this.settingColumns = allSettingColumns
        Vue.ls.set(key2, allSettingColumns)
        this.columns = columns
        this.columns.unshift(firstItem)
        this.columns.push(lastItem)
      }
      //已选列表缓存dataIndex数据集合不为空，设置table要显示的列
      else {
        this.settingColumns = this.defColumns.filter(
          item => colSettings.includes(item.dataIndex) && item.key !== firstItem.key && item.dataIndex !== lastItem.dataIndex).map(item => item.dataIndex)
        Vue.ls.set(key2, this.settingColumns)

        this.columns = this.defColumns.filter(item => {
          delete item.fixed
          if (item.dataIndex === 'action' || item.key === 'rowIndex') {
            return true
          }
          if (this.settingColumns.includes(item.dataIndex)) {
            item.disabled = this.settingColumns.length <= 1//只有一个字段时，不可点击取消
            item.checked = true
            return true
          } else {
            item.checked = false
            return false
          }
        })
      }
      this.columns[0]['fixed'] = 'left'
      this.columns[1]['fixed'] = 'left'
      this.columns[this.columns.length - 1].fixed = 'right'
    },
    /*列设置更改事件*/
    onColSettingsChange(checkedValues) {
      let tempColumns = []
      this.defColumns.forEach((item) => {
        delete item.fixed
        if (item.key === 'rowIndex' || item.dataIndex === 'action') {
          tempColumns.push(item)
        }
        if (checkedValues.includes(item.dataIndex)) {
          item.disabled = checkedValues.length <= 1
          item.checked = true
          tempColumns.push(item)
        } else {
          if (item.checked) {
            item.checked = false
          }
          if (item.disabled) {
            item.disabled = false
          }
        }
      })
      tempColumns[0]['fixed'] = 'left'
      tempColumns[1]['fixed'] = 'left'
      tempColumns[tempColumns.length - 1]['fixed'] = 'right'
      this.columns.splice(0, this.columns.length, ...tempColumns)
      //将已选并在列表显示的字段dataIndex写入缓存中
      var key = this.$route.name + ':colsettings'
      // Vue.ls.set(key, this.settingColumns, 7 * 24 * 60 * 60 * 1000)
      Vue.ls.set(key, this.settingColumns)
      this.setColumnsWidth()
    },
    dragStart(event, index) {
      this.dragItemIndex = index
      this.dragDom = event.currentTarget.cloneNode(true)
    },
    allowDrop(event) {
      event.preventDefault()
    },
    drop(event, index) {
      event.preventDefault()
      //默认列表重新进行排序
      if (index != this.dragItemIndex) {
        let temp = this.defColumns[this.dragItemIndex]
        this.defColumns.splice(this.dragItemIndex, 1)
        this.defColumns.splice(index, 0, temp)
      }
    },
    dragEnd(event, index) {
      //event.preventDefault()
      //table列显示顺序同步拖拽后的顺序
      let tempSettingDataIndex = []
      let tempAllDataIndex = []
      let cols = this.defColumns.filter(item => {
        delete item.fixed
        if (item.key === 'rowIndex' || item.dataIndex === 'action') {
          return true
        } else {
          tempAllDataIndex.push(item.dataIndex)
        }
        if (this.settingColumns.includes(item.dataIndex)) {
          tempSettingDataIndex.push(item.dataIndex)
          return true
        }
        return false
      })
      this.settingColumns.splice(0, this.settingColumns.length, ...tempSettingDataIndex) //按照排序重新保存选中显示列表
      cols[0]['fixed'] = 'left'
      cols[1]['fixed'] = 'left'
      cols[cols.length - 1]['fixed'] = 'right'
      this.columns.splice(0, this.columns.length, ...cols)
      var key1 = this.$route.name + ':colsettings' //浏览器缓存重新记录排序后的选中列表
      //Vue.ls.set(key1, tempColumns, 7 * 24 * 60 * 60 * 1000)//保留7天缓存
      Vue.ls.set(key1, this.settingColumns)

      var key2 = this.$route.name + ':allCols' //浏览器缓存重新记录排序后的所有列表
      Vue.ls.set(key2, tempAllDataIndex)
      this.dragDom.remove()
      ++this.refreshKey
      this.setColumnsWidth()
    },
    /*获取滚动区列宽，用于设置对应固定列的宽*/
    setColumnsWidth() {
      this.$nextTick(() => {
        if (this.columns && this.columns.length > 0) {
          let table = document.querySelector('.terminal-table')
          if (table) {
            let scrollThs = table.querySelector('.ant-table-scroll .ant-table-thead tr').querySelectorAll('th')
            let leftFixedThs = table.querySelector('.ant-table-fixed-left .ant-table-thead tr').querySelectorAll('th')
            for (let i = 0; i < leftFixedThs.length; i++) {
              leftFixedThs[i].setAttribute('data-index', i); // 添加 data-index 属性
              leftFixedThs[i].style.setProperty(`--height-${i}`, scrollThs[i].offsetHeight + 'px');
              leftFixedThs[i].style.setProperty(`--width-${i}`, scrollThs[i].offsetWidth + 'px');
            }
          }
        }
      })
    },
    calcNumber(index) {
      return (parseInt(this.ipagination.current) - 1) * parseInt(this.ipagination.pageSize) + (parseInt(index) + 1)
    },
    loadData(arg) {
      if (!this.url.list) {
        this.$message.error('请设置url.list属性!')
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1
      }
      //从数据中心index.vue路由跳转后，根据路由参数设置列表查询条件
      if (this.$route.params.deviceName) {
        this.queryParam.name = this.$route.params.deviceName
      }
      var params = this.getQueryParams() //查询条件
      this.loading = true
      getAction(this.url.list, params).then((res) => {
        if (res.success) {
          this.dataSource = res.result.records || res.result
          if (this.dataSource.length < 9) {
            this.clientHeight = false
          }
          this.ipagination.total = res.result.total
          if (this.$route.params.deviceName && this.$route.params.tabKey) {
            if (this.dataSource.length > 0) {
              this.routeDetailPage(this.dataSource[0], this.$route.params.tabKey)
            } else {
              this.$message.warning('设备[' + this.$route.params.deviceName + ']不存在')
            }
          }
          this.debounceCalWidth()
        }
        if (res.code === 510) {
          this.$message.warning(res.message)
        }
        this.loading = false
      })
    },
    //设置数据中心路由最终跳转页面，并重置路由和列表查询条件
    routeDetailPage(record, tabKey) {
      let records = record
      records.tabKey = tabKey
      delete this.$route.params.deviceName
      delete this.$route.params.tabKey
      delete this.queryParam.name
      this.$parent.pButton2(1, records)
    },
    getUntie(record) {
      let that = this
      if (record.gatewayName == '' || record.gatewayName == null || record.gatewayName == undefined) {
        that.$message.warning('该设备未绑定网关')
        return
      }
      let params = {
        gatewayId: record.gatewayId,
        ids: record.id
      }
      that.$confirm({
        title: '确认操作',
        okText: '是',
        cancelText: '否',
        content: '是否确定解绑选中的设备?',
        onOk: function() {
          postAction(that.url.unbund, params).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.loadData()
            } else {
              that.$message.warning(res.message)
            }
          })
        }
      })
    },
    queryAllTags() {
      getAction('/device/deviceInfo/taginfo4Select').then((res) => {
        if (res.success) {
          this.taginfoSelectData = res.result
        }
      })
    },
    tagOk() {
      this.loadData()
    },
    bindaTag(record) {
      this.$refs.deviceTagModal.visible = true
      this.$refs.deviceTagModal.bindaTag(record)
    },
    handleAdd: function() {
      this.$refs.modalForm.add()
      this.$refs.modalForm.title = '添加设备'
      this.$refs.modalForm.disableSubmit = false
    },
    getDeviceInfoMap(flag = false) {
      getAction(this.url.deviceInfoUrl).then((res) => {
        if (res.success) {
          this.deviceInfo[0].count = res.result.all
          this.deviceInfo[3].count = res.result.disable
          this.deviceInfo[1].count = res.result.on
          this.deviceInfo[2].count = res.result.out
          if (flag) {
            this.$message.success('同步成功')
          }
        }
      })
    },
    handleDetailPage: function(record) {
      this.$parent.pButton2(1, record)
    },
    //删除
    deleteRecord(record) {
      if (!this.url.deleteBatch) {
        this.$message.error('请设置url.delete属性!')
        return
      }
      var that = this
      this.$confirm({
        title: '确认删除',
        okText: '是',
        cancelText: '否',
        content: '是否删除选中数据?',
        onOk: function() {
          that.loading = true
          deleteAction(that.url.deleteBatch, {
            ids: record.id
          }).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.loadData()
            } else {
              that.$message.warning(res.message)
              that.loadData()
            }
          })
        }
      })
    },
    //设备禁用/启用
    deviceEnable(record) {
      let enable = record.enable
      getAction(this.url.deviceEnable, {
        enable: enable === 1 ? 0 : 1,
        deviceId: record.id
      }).then((res) => {
        if (res.success) {
          if (enable === 1) {
            this.$message.success('禁用成功!')
          } else {
            this.$message.success('启用成功!')
          }
          this.loadData()
        }
      })
    },
    //批量禁用、启用
    batchEnableOperate(enable) {
      if (!this.url.batchEnable) {
        this.$message.error('请设置url.batchConfirm属性!')
        return
      }
      if (this.selectedRowKeys.length <= 0) {
        this.$message.warning('请选择一条记录！')
        return
      }
      var that = this
      this.$confirm({
        title: '确认操作',
        okText: '是',
        cancelText: '否',
        content: '是否确定修改选中数据?',
        onOk: function() {
          that.loading = true
          getAction(that.url.batchEnable, {
            ids: that.selectedRowKeys.join(','),
            enable: enable
          })
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.loadData()
                that.onClearSelected()
              } else {
                that.$message.warning(res.message)
              }
              that.loading = false
            })
            .catch((err) => {
              that.loading = false
              that.$message.warning(err.message)
            })
        }
      })
    },
    sshRemoteOperation(record){
      this.$refs.sshRemoteManagementModal.edit(record)
      this.$refs.sshRemoteManagementModal.title = '远程管理'
      this.$refs.sshRemoteManagementModal.disableSubmit = false
    },
    treeSeletedSearch(option = '', type = '') {
      this.queryParam.option = option
      this.queryParam.type = type
      this.loadData(1)
    },
    //表单查询,点击查询按钮，默认查询第一页
    dosearch() {
      this.loadData(1)
    },
    searchReset() {
      //重置form表单，不重置tree选中节点
      if (this.queryParam.option) {
        let type = this.queryParam.type
        let option = this.queryParam.option
        this.queryParam = {}
        this.queryParam.type = type
        this.queryParam.option = option
      } else {
        this.queryParam = {}
      }
      this.loadData(1)
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
/* 为每个 th 元素设置独立的样式 */
::v-deep .ant-table-fixed-left .ant-table-thead tr th[data-index="0"] {
  height: var(--height-0) !important;
  width: var(--width-0) !important;
}

::v-deep .ant-table-fixed-left .ant-table-thead tr th[data-index="1"] {
  height: var(--height-1) !important;
  width: var(--width-1) !important;
}

::v-deep .ant-table-fixed-left .ant-table-thead tr th[data-index="2"] {
  height: var(--height-2) !important;
  width: var(--width-2) !important;
}
.gutter-example {
  margin-bottom: 8px
}

.stateBox {
  margin-left: 20px;
}

.stateImg {
  vertical-align: middle;
}

.alarmStatus {
  margin-left: 8px;
}

.overlay {
  color: #409eff
}
/deep/ .ant-card-body{
  height: 100%;
}
/deep/ .ant-table-wrapper{
  height: calc(100% - 40px);
}
</style>