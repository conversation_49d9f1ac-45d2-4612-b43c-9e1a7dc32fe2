<template>
  <j-modal :title="title" :centered='true' switchFullscreen :width="800" :ok=false :visible="visible"
    :confirmLoading="confirmLoading" :okButtonProps="{ props: {disabled: disableSubmit} }" @ok="handleOk"
    @cancel="handleCancel" cancelText="关闭">

    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="节点类型">
          <a-select placeholder="请选择节点类型" :allowClear='true' @change="typeChange"
            v-decorator="['nodeType', { initialValue: '0', rules: [{ required: true, message: '请选择节点类型' }]}]">
            <a-select-option v-for="item in typeList" :value="item.value">{{ item.title }}</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="行政区划" :labelCol="labelCol" :wrapperCol="wrapperCol"
          v-if="model.nodeType == '1'">
          <yq-area-cascader-select placeholder="请选择归属地" v-decorator="['cityId', validatorRules.cityId]"
            @change="cityChange">
          </yq-area-cascader-select>
        </a-form-item>
        <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="单位名称" :hidden="false" hasFeedback>
          <a-input id="departName" placeholder="请输入单位/部门名称" v-decorator="['departName', validatorRules.departName ]"
            :allowClear='true' autocomplete='off' />
        </a-form-item>
        <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="单位别名">
          <a-input placeholder="请输入单位/部门别名" v-decorator="['code', validatorRules.code]" :allowClear='true'
                   autocomplete='off' />
        </a-form-item>
        <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="单位标识" :hidden="false" hasFeedback>
          <a-input id="departNameEn" placeholder="请输入单位/部门标识"
            v-decorator="['departNameEn', validatorRules.departNameEn ]" :allowClear='true' autocomplete='off' />
        </a-form-item>
        <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" :hidden="seen" label="上级部门" hasFeedback>
          <a-tree-select :getPopupContainer='(node) => node.parentNode' style="width:100%"
            :dropdownStyle="{maxHeight:'200px',overflow:'auto'}" :treeData="departTree" v-model="model.parentId"
            placeholder="请选择上级部门" :disabled="condition">
          </a-tree-select>
        </a-form-item>
<!--        <a-form-item v-if="seen" :labelCol="labelCol" :wrapperCol="wrapperCol" label="所属平台标识">
          <a-input placeholder="请输入所属平台标识" v-decorator="['platformCode',validatorRules.platformCode]" :allowClear='true'
                   autocomplete='off' />
        </a-form-item>-->
        <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="单位类型">
<!--          <template v-if="seen">
            <a-radio-group v-decorator="['orgCategory',validatorRules.orgCategory]" placeholder="请选择单位类型">
              <a-radio value="1">
                一级部门
              </a-radio>
            </a-radio-group>
          </template>-->
          <template >
            <a-radio-group v-decorator="['orgCategory',validatorRules.orgCategory]" placeholder="请选择单位类型">
              <a-radio v-for='item in deptTypes' :value="item.value">
                {{item.title}}
              </a-radio>
<!--              <a-radio value="3">
                保障部
              </a-radio>
              <a-radio value="5">
                委办局
              </a-radio>
              <a-radio value="4">
                运维供应商
              </a-radio>
              <a-radio value="6">
                服务商
              </a-radio>-->
            </a-radio-group>
          </template>
        </a-form-item>
        <!--        <a-form-item :required="true" :labelCol="labelCol" :wrapperCol="wrapperCol" label='组织级别'>-->
        <!--          <j-dict-select-tag v-model='model.orgLevel' placeholder='请选择组织级别' dictCode='orgLevel' />-->
        <!--        </a-form-item>-->
        <a-form-item class="two-words" :labelCol="labelCol" :wrapperCol="wrapperCol" label="手机号">
          <a-input placeholder="请输入电话" v-decorator="['mobile',validatorRules.mobile]" :allowClear='true'
            autocomplete='off' />
        </a-form-item>
        <a-form-item class="two-words" :labelCol="labelCol" :wrapperCol="wrapperCol" label="传真">
          <a-input placeholder="请输入传真" v-decorator="['fax', validatorRules.fax]" :allowClear='true'
            autocomplete='off' />
        </a-form-item>
<!--        <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="业务CODE">
          <a-input placeholder="请输入CODE" v-decorator="['code', validatorRules.code]" :allowClear='true'
            autocomplete='off' />
        </a-form-item>-->
        <a-form-item class="two-words" :labelCol="labelCol" :wrapperCol="wrapperCol" label="地址">
          <a-input placeholder="请输入地址" v-decorator="['address', {}]" :allowClear='true' autocomplete='off' />
        </a-form-item>
        <a-form-item label="行政区划" :labelCol="labelCol" :wrapperCol="wrapperCol" v-if="model.nodeType == '0'">
          <yq-area-cascader-select placeholder="请选择归属地" v-decorator="['cityId', validatorRules.cityId]">
          </yq-area-cascader-select>
        </a-form-item>

        <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="计划数量">
          <a-input placeholder="请输入计划数量" v-decorator="['planNumber', validatorRules.planNumber]" :allowClear='true'
            autocomplete='off' />
        </a-form-item>
        <a-form-item label="经度" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['longitude',validatorRules.longitude]" placeholder="请输入经度" :allowClear='true'
            autocomplete='off' />
        </a-form-item>
        <a-form-item label="纬度" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['latitude',validatorRules.latitude]" placeholder="请输入纬度" :allowClear='true'
            autocomplete='off' />
        </a-form-item>
        <a-form-item class="two-words" :labelCol="labelCol" :wrapperCol="wrapperCol" label="排序">
          <a-input-number v-decorator="[ 'departOrder',{'initialValue':0}]" :allowClear='true' autocomplete='off'
            :min="0" />
        </a-form-item>
        <a-form-item class="two-words" :labelCol="labelCol" :wrapperCol="wrapperCol" label="备注">
          <a-textarea placeholder="请输入备注" v-decorator="['memo', validatorRules.memo]" :allowClear='true'
            autocomplete='off' />
        </a-form-item>
      </a-form>
    </a-spin>
  </j-modal>
</template>

<script>
  import {
    httpAction
  } from '@/api/manage'
  import {
    queryIdTree,
    ajaxGetDictItems,
  } from '@/api/api'
  import pick from 'lodash.pick'
  import ATextarea from 'ant-design-vue/es/input/TextArea'
  import YqAreaCascaderSelect from '@/components/areaDict/YqAreaCascaderSelect'
  import JDictSelectTag from '@comp/dict/JDictSelectTag.vue'
  import {
    phoneValidator
  } from '@/mixins/phoneValidator'
  export default {
    name: "SysDepartModal",
    components: {
      ATextarea,
      YqAreaCascaderSelect,
      JDictSelectTag
    },
    mixins: [phoneValidator],
    data() {
      return {
        departTree: [],
        orgTypeData: [],
        typeList: [],
        phoneWarning: '',
        departName: "",
        title: "操作",
        seen: false,
        visible: false,
        condition: true,
        disableSubmit: false,
        model: {},
        menuhidden: false,
        menuusing: true,
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          },
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          },
        },

        confirmLoading: false,
        form: this.$form.createForm(this),
        validatorRules: {
          departName: {
            rules: [{
              required: true,
              message: '请输入单位/部门名称!'
            },{
              pattern: /^(?!\s*$).+/,
              message: '单位/部门名称不能为空或仅包含空格'
            },{
              max: 300,
              message: '单位/部门名称长度不能超出300个字符'
            }]
          },
          departNameEn: {
            rules: [{
              required: true,
              message: '请输入单位标识!'
            },{
              pattern: /^[^\s]+$/,
              message: '单位标识不能包含空格'
            },{
              max: 150,
              message: '单位标识不能超出150个字符'
            }]
          },
          /*platformCode: {
            rules: [{
              required: true,
              message: '请输入所属平台标识!'
            }]
          },*/
          orgCode: {
            rules: [{
              required: true,
              message: '请输入单位编码!'
            }]
          },
          mobile: {
            rules: [{
              validator: this.phone
            }]
          },
          cityId: {
            rules: [{
              required: true,
              message: '请输入行政区划!'
            }]
          },
          // orgLevel: {
          //   rules: [{
          //     required: true,
          //     message: '请选择组织级别!'
          //   }]
          // },
          fax: {
            rules: [{
              max: 50,
              message: '传真长度不能超出50个字符'
            }],
          },
          code: {
            rules: [{
              required: true,
              message: '请输入单位/部门别名!'
            },{
              pattern: /^(?!\s*$).+/,
              message: '别名不能为空或仅包含空格'
            },{
              max: 50,
              message: '别名长度不能超出50个字符'
            }]
          },
          planNumber: {
            rules: [{
              max: 10,
              message: '计划数量长度不能超出10个字符'
            }]
          },
          longitude: {
            rules: [{
              pattern: /^(-?180(\.0{0,5})?|(-?1[0-7]\d(\.\d{1,5})?)|(-?[1-9]\d(\.\d{1,5})?)|(-?[0-9](\.\d{1,5})?))$/,
              message: '请输入正确的经度值(-180度 - 180度)'
            }]
          },
          latitude: {
            rules: [{
              pattern: /^(-?[0-8]?\d(\.\d{1,5})?|-?90(\.0{1,5})?)$/,
              message: '请输入正确的纬度值(-90度 - 90度)'
            }]
          },
          memo: {
            rules: [{
              max: 200,
              message: '备注长度不能超出200个字符'
            }]
          }
        },
        url: {
          add: "/sys/sysDepart/add",
        },
        dictDisabled: true,
        deptTypes: [],
      }
    },
    created() {
      this.getDictData('depart_type_flag')
      this.getDeptTypes('DEPARTMENT_CATEGORIES')
    },
    methods: {
      typeChange(e) {
        this.model.nodeType = e
        if (e == '1') {
          this.$nextTick(() => {
            this.form.setFieldsValue({
              departName: this.model.departName,
              departNameEn: this.model.departNameEn,
              orgCategory: this.model.orgCategory,
              departOrder: this.model.departOrder,
              mobile: this.model.mobile,
              address: this.model.address,
              memo: this.model.memo,
              code: this.model.code,
              contacts: this.model.contacts,
              planNumber: this.model.planNumber,
              cityId: this.model.cityId,
              longitude: this.model.longitude,
              latitude: this.model.latitude,
            })
          })
        } else {
          this.$nextTick(() => {
            this.form.setFieldsValue({
              departName: '',
              departNameEn: this.model.departNameEn,
              orgCategory: this.model.orgCategory,
              departOrder: this.model.departOrder,
              mobile: this.model.mobile,
              address: this.model.address,
              memo: this.model.memo,
              code: this.model.code,
              contacts: this.model.contacts,
              planNumber: this.model.planNumber,
              cityId: this.model.cityId,
              longitude: this.model.longitude,
              latitude: this.model.latitude,
            })
          })
        }
      },
      cityChange(value, name) {
        this.form.setFieldsValue({
          departName: name
        })
      },
      getDictData(dictCode) {
        ajaxGetDictItems(dictCode, null).then((res) => {
          if (res.success) {
            this.typeList = res.result
          }
        })
      },
      getDeptTypes(dictCode) {
        ajaxGetDictItems(dictCode, null).then((res) => {
          if (res.success) {
            this.deptTypes = res.result
          }
        })
      },
      loadTreeData() {
        var that = this;
        queryIdTree().then((res) => {
          if (res.success) {
            that.departTree = [];
            for (let i = 0; i < res.result.length; i++) {
              let temp = res.result[i];
              that.departTree.push(temp);
            }
          }
        })
      },
      add(depart) {
        if (depart) {
          this.seen = false;
          this.dictDisabled = false;
        } else {
          this.seen = true;
          this.dictDisabled = true;
        }
        this.edit(depart);
      },
      edit(record) {
        this.form.resetFields();
        this.model = Object.assign({}, {});
        if (record && !!record.id) {
          this.model.nodeType = record.nodeType
        } else {
          this.model.nodeType = '0'
        }
        this.visible = true;
        this.loadTreeData();
        this.model.parentId = record != null ? record.toString() : null;
        if (this.seen) {
          this.model.orgCategory = '1';
        } else {
          this.model.orgCategory = '2';
        }
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.model, 'orgCategory', 'departName', 'departNameEn', 'departNameAbbr',
            'departOrder', 'description', 'orgType', 'orgCode', 'mobile', 'fax', 'address', 'memo', 'status',
            'delFlag', 'latitude', 'longitude'))
        });
      },
      close() {
        this.$emit('close');
        this.disableSubmit = false;
        this.visible = false;
      },
      handleOk() {
        const that = this;
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true;
            let formData = Object.assign(this.model, values);
            //时间格式化
            httpAction(this.url.add, formData, "post").then((res) => {
              if (res.success) {
                that.$message.success(res.message);
                that.loadTreeData();
                that.$emit('ok');
              } else {
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
              that.close();
            })

          }
        })
      },
      handleCancel() {
        this.close()
      },
    }
  }
</script>

<style scoped lang='less'>
  @import '~@assets/less/normalModal.less';
</style>