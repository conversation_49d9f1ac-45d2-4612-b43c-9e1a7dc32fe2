<template>
  <div id="flowStencil" class="sider">
    <a-input placeholder="输入关键字进行过滤" v-model="filterText"></a-input>
    <vue-easy-tree ref="veTree" node-key="id" :data="assetsCategoryTree" :props="replaceFields"
      height="calc(100vh - 325px)" :filter-node-method="filterNode">
      <div class="custom-tree-node" slot-scope="{ node, data }">
        <div :draggable="draggable" @mousedown="startDrag($event,'topo', data)">
          <img v-if="data.icon != null && data.icon.length > 0" :src="setUrl(data.icon)"
            style="width: 20px; height: 20px" />
          <span>{{ node.label }}</span>
        </div>
      </div>
    </vue-easy-tree>
  </div>
</template>

<script>
  import VueEasyTree from '@wchbrad/vue-easy-tree'
  import {
    getAction,
    postAction,
    httpAction
  } from '@/api/manage'
  export default {
    components: {
      VueEasyTree,
    },
    data() {
      return {
        // 树数据
        assetsCategoryTree: [],
        filterText: '',
        replaceFields: {
          label: 'name',
          key: 'id',
        },
        draggable: true,
        picSrc: window._CONFIG['staticDomainURL'],
        url: {
          treeUrl: '/autoControl/scene/queryScriptTree',
        },
      }
    },
    created() {
      this.loadTree()
    },
    watch: {
      filterText(val) {
        this.$refs.veTree.filter(val)
      },
    },
    methods: {
      // 加载设备树列表
      loadTree() {
        var that = this
        that.assetsCategoryTree = []
        //为tree生成对应地title slot
        const generateSlotScopes = (data) => {
          for (let i = 0; i < data.length; i++) {
            if (data[i].id == null || data[i].id == '') {
              data[i].id = i
              data[i].children = data[i].child
            }
            if (data[i].scriptName != null) {
              data[i].name = data[i].scriptName
            }
            if (data[i].children) {
              generateSlotScopes(data[i].children)
            }
          }
        }
        getAction(this.url.treeUrl).then((res) => {
          if (res.success) {
            that.assetsCategoryTree = res.result
            generateSlotScopes(that.assetsCategoryTree)
          }
        })
      },
      filterNode(value, data) {
        if (!value) return true
        return data.name.indexOf(value) !== -1
      },
      //tree拖拽节点
      async startDrag(e, type, data) {
        console.log(e, 'e');
        console.log(type, 'type');
        console.log(data, 'data');
        if (data.children && data.children.length > 0) {
          return
        } else {
          this.$emit('dragNode', e, type, data)
        }
        return
      },
      setUrl(url) {
        if (url.includes('http')) {
          return url
        } else {
          return this.picSrc + '/' + url
        }
      }
    },
  }
</script>

<style lang="less" scoped>
  .node-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
  }

  .text-div {
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 36px;
  }
</style>