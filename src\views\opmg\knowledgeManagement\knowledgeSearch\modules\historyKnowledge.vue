<template>
  <div :class="theme" v-if="historyList.length>0">
    <div class="history" >
      <div style="display: flex; align-items: center;">
        <slot name="prefixIcon"></slot>
        <div class="title">搜索历史</div>
      </div>

      <div class="wrapper">
        <div
          v-for="(hitem, hindex) in historyList"
          :key="'his_' + hindex"
          class="item"
        >
          <div class="item-text" @click="goSearchResultPage(hitem.keyword)">
            <div class="time">{{ hitem.time }}</div>
            <div class="name" :title="hitem.keyword"> {{ hitem.keyword }}</div>
          </div>
          <span class="delete" @click="deleteKSearchHistory(hitem.keyword)"><a-icon class="delete-icon" type="delete"/></span>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { deleteAction } from '@/api/manage'
export default {
  data() {
    return {
      value: '',
      url:{
        delete:'/kbase/knowledges/searchHistory'
      }
    }
  },
  props: {
    // 搜索历史
    historyList: {
      type: Array,
      default: () => []
    },
    // 主题类型
    theme: {
      type: String,
      default: ''
    }
  },
  watch: {
    historyList: {
      handler() {},
      deep: true,
      immediate: true
    }
  },
  methods: {
    goSearchResultPage(key) {
      this.$emit('goSearchResultPage', key)
    },
    deleteKSearchHistory(keyword){
      deleteAction(this.url.delete,{keyword:keyword}).then(res=>{
        this.$emit('reloadKSearchHistory')
      })
    },
  }
}
</script>
<style scoped lang="less">
.history {
  width: 100%;

  .title {
    font-size: 16px;
    font-weight: bold;
    height: 34px;
    line-height: 34px;
  }

  .wrapper {
    .item {
      font-size: 14px;
      padding: 5px;
      white-space: nowrap;
      overflow: hidden;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .item-text {
        white-space: nowrap;
        overflow: hidden;
        cursor: pointer;
        display: flex;

        .time {
          font-size: 13px;
          margin-right: 16px;
        }

        .name {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .delete {
        margin-left: 16px;
        cursor: pointer;
        display: none;
      }
    }

    .item:hover {
      border-radius: 3px;
      .delete {
        display: inline-block;
      }
    }
  }
}
/*平台*/
.theme1{
  .history {
    .wrapper {
      .item {

        .item-text {

          .time {
            color: #9195a3;
          }

          .name {
            color: #2440b3;
          }
        }

        .delete {
          color: #9195a3 !important;
        }
      }

      .item:hover {
        background-color: #ecf5ff;
        .item-text:hover {
          .time {
            color: #315efb;
          }

          .name {
            color: #315efb;
          }
        }

        .delete:hover {
          .delete-icon {
            color: #315efb !important;
          }

        }
      }
    }
  }
}
/*运维助手*/
.theme2{
  .history {
    .title {
      color: #fff;
    }
    .wrapper {
      .item {
        .item-text {
          .time {
            color: #9195a3;
          }
          .name {
            color: #fff;
          }
        }

        .delete {
          color: #9195a3 !important;
        }
      }

      .item:hover {
        background-color: rgba(235, 235, 235, 0.2);
        .item-text:hover {
          .time {
            color: #66ffff;
          }

          .name {
            color: #66ffff;
          }
        }

        .delete:hover {
          .delete-icon {
            color:#66ffff !important;
          }
        }
      }
    }
  }
}

</style>