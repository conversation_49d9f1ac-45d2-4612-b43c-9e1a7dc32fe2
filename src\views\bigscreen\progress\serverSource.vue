<template>
  <div class="page-box big-screen-theme">
    <div class="page-inside">
      <a-row :gutter="16" style="height: 100%">
        <a-col style="height: 100%" :span="24">
          <table-module></table-module>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script>
import tableModule from '../moduels/server-table-module.vue'
export default {
  components: {
    tableModule
  },
  data() {
    return {}
  },
  methods: {},
}
</script>

<style lang="less" scoped>
.page-box {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  .page-inside {
    width: calc(100% - 32px);
    height: calc(100% - 32px);
  }
}
</style>