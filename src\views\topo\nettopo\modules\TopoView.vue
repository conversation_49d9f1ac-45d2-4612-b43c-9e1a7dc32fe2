<template>
  <j-modal
    :visible="visible"
    :destroyOnClose="true"
    fullscreen
    :footer=null
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭">
    <vis-edit v-if="visible" ref="visEdit" operate="show" :title="title" toolbar></vis-edit>
  </j-modal>
</template>

<script>
    import VisEdit from './VisEdit'
  export default {
    name: 'TopoEdit',
    components: {
      VisEdit
    },
    data () {
      return {
        title: '拓扑图查看',
        width: 800,
        record: {},
        visible: false,
        disableSubmit: false,
      }
    },
    methods: {
      show(id,name) {
        this.title = name
        this.visible = true
        this.$nextTick(() => {
          this.$refs.visEdit.createTopo(id)
          // 查看详情默认打开小地图
          this.$refs.visEdit.toggleMiniMap = true;
        })
      },
      close () {
        this.$emit('close')
        this.visible = false
      },
      handleOk () {
        this.$refs.visEdit.save()
        // this.close()
      },
      handleCancel () {
        this.close()
      },
      submitCallback(){
        this.$emit('ok');
        this.visible = false;
      },
    }
  }
</script>

<style lang="less" scoped>
  ::v-deep .ant-modal-body {
    padding: 0 !important;
  }
  ::v-deep .ant-modal-content, ::v-deep .ant-modal-content .ant-modal-body {
    overflow: hidden !important;
  }
</style>
