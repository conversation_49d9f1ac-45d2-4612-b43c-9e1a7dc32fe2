<template>
  <div class="config">
    <config-grid 
    v-if="cellType === 'grid'" 
    :globalGridAttr="globalGridAttr" 
    :productId="productId"
    />
    <config-node 
    v-if="cellType === 'node'" 
    :globalGridAttr="globalGridAttr" 
    :size="selects.length"
    :productId="productId" 
    />
    <config-edge v-if="cellType === 'edge'" :globalGridAttr="globalGridAttr" :size="selects.length" />
  </div>
</template>

<script>
import ConfigGrid from './ConfigGrid/index.vue'
import ConfigNode from './ConfigNode/index.vue'
import ConfigEdge from './ConfigEdge/index.vue'
import FlowGraph from '../../graph'
import { globalGridAttr } from '../../models/global'
export default {
  name: 'Index',
  components: {
    ConfigGrid,
    ConfigNode,
    ConfigEdge,
  },
  props: {
    //拓扑图的编辑查看状态
    operate: {
      type: String,
      default: 'create',
      required: true,
    },
    productId: {
      type: String,
      default: '',
      required: true,
    },
    selects:{
      type:Array,
      default:()=>[],
      required:true,
    }
  },
  data() {
    return {
      globalGridAttr: globalGridAttr,
    }
  },
  computed: {
    cellType() {
      let node = this.selects.find((el) => el.isNode())
      let edge = this.selects.find((el) => el.isEdge())
      if (node && edge) {
        return 'grid'
      } else if (node) {
        return 'node'
      } else if (edge) {
        return 'edge'
      } else {
        return 'grid'
      }
    },
  },
  created() {
  },
  mounted() {},
  methods: {},
}
</script>
<style>
.ele-attr .ant-tabs-content{
  height: calc(100% - 74px);
  overflow:auto;
}
.ele-attr .ant-row{
  margin-bottom: 12px;
}
</style>
<style lang="less" scoped>
.config {
    width: 270px;
    padding: 0 8px;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
  }
</style>
