<template>
  <div :id="chartId" ref="chartDom" style="width: 100%; height: 100%"></div>
</template>

<script>
  import {
    getAction,
  } from '@/api/manage'
  export default {
    props: ['chartId'],
    data() {
      return {
        speedData: [],
        url: {
          netSpeed: 'device/statistics/netSpeed'
        }
      }
    },
    created() {
      this.getSpeed()
    },
    methods: {
      getSpeed() {
        getAction(this.url.netSpeed).then((res) => {
          this.speedData = res.result
          this.drawChart(this.speedData)
        })
      },
      drawChart(item) {
        let maxv = 300
        if (maxv < 100) maxv = 100
        let data = item.map((el) => el.netSpeed)
        let xData = item.map((el) => el.name)
        let myChart = this.$echarts.init(document.getElementById(this.chartId))
        myChart.setOption({
          backgroundColor: '',
          tooltip: {
            show: true,
            trigger: 'item',
            axisPointer: {
              type: 'shadow',
            },
            backgroundColor: 'rgba(9, 24, 48, 0.5)',
            borderColor: 'rgba(75, 253, 238, 0.4)',
            textStyle: {
              color: '#CFE3FC',
            },
            borderWidth: 1,
            appendToBody: true,
            formatter: (a, b) => {
              return '名称：' + a.name + "<br /> 吞吐量：" + a.data
            },
            transitionDuration: 0, //echart防止tooltip的抖动
          },
          dataZoom: [{
            xAxisIndex: [0],
            show: false, //是否显示滑动条，不影响使用
            start: 0, // 从头开始。
            endValue: 9,
            realtime: true, //是否实时更新
          },
          {
            type: 'inside',
            xAxisIndex: 0,
            zoomOnMouseWheel: true, //滚轮是否触发缩放
            moveOnMouseMove: true, //鼠标滚轮触发滚动
            moveOnMouseWheel: true
          }],
          grid: {
            right: 16,
            top: 32,
            left: 30,
            bottom: 0,
            containLabel: true,
          },
          xAxis: {
            type: 'category',
            boundaryGap: true,
            data: xData,
            axisLabel: {
              interval: 0, //设置为 1，表示『隔一个标签显示一个标签』
              textStyle: {
                color: '#fff',
                fontStyle: 'normal',
                fontSize: 12,
              },
              formatter: function(value) {
                var temp = '' // 拼接加 \n 返回的类目项
                var everyLineNum = 4 // 每行显示文字个数
                var maxLength = 7 // 最多显示文字个数
                if (xData.length < 5) {
                  everyLineNum = 8
                  maxLength = 15
                }
                if (value.length > everyLineNum) {
                  if (value.length > maxLength) {
                    temp = value.substring(0, everyLineNum) + '\n' + value.substring(everyLineNum, maxLength) + '...'
                  } else {
                    temp = value.substring(0, everyLineNum) + '\n' + value.substring(everyLineNum, maxLength)
                  }
                  return temp
                } else {
                  return value
                }
              }
            },
            axisTick: {
              //坐标轴刻度相关设置。
              show: false,
            },
            axisLine: {
              //坐标轴轴线相关设置
              lineStyle: {
                color: 'rgba(77, 128, 254, 0.2)',
              },
            },
            splitLine: {
              //坐标轴在 grid 区域中的分隔线。竖向
              show: false,
              lineStyle: {
                color: 'rgba(77, 128, 254, 0.2)',
              },
            },
          },
          yAxis: [{
            type: 'value',
            name: `${item.length>0?'单位（MB/S）':''}`,
             nameTextStyle: {
              color: "#fff",
              fontSize: 10,
              padding: [0, 0, 0, -30]
            },
            // max: 100,
            interval: 5000 / 10,
            splitNumber: 100,
            axisLabel: {
              textStyle: {
                color: '#fff',
                fontStyle: 'normal',
                fontSize: 12,
              },
            },
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: true,
              lineStyle: {
                type: 'dotted',
                color: 'rgba(77, 128, 254, 0.2)',
              },
            },
          }, ],
          series: [{
            name: 'value',
            type: 'pictorialBar',
            barWidth: '60%',
            stack: '总量',
            label: {
              normal: {
                position: 'insideTop',
                show: true,
              },
            },
            itemStyle: {
              normal: {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [{
                      offset: 0,
                      color: 'rgba(0, 151, 251, 1)', // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: 'rgba(0, 34, 66, 0.2)', // 100% 处的颜色
                    },
                  ],
                  globalCoord: false, // 缺省为 false
                }, //渐变颜色
              },
            },
            symbol: 'path://M12.000,-0.000 C12.000,-0.000 16.074,60.121 22.731,60.121 C26.173,60.121 -3.234,60.121 0.511,60.121 C7.072,60.121 12.000,-0.000 12.000,-0.000 Z',
            data: data,
          }, ],
        })
        window.addEventListener('resize', () => {
          myChart.resize()
        })
      },
    },
  }
</script>

<style lang="less" scoped>
</style>