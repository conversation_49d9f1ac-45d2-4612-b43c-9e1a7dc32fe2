<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-item label="类型名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['typeName', validatorRules.typeName]" :allowClear="true" autocomplete="off"
                placeholder="请输入类型名称"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="类型标识" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['typeCode', validatorRules.typeCode]" :allowClear="true" autocomplete="off"
                placeholder="请输入类型标识"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="所属分类" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-select v-decorator="['dictValue', validatorRules.dictValue]" :allowClear="true" autocomplete="off"
                placeholder="请选择所属分类">
                <a-select-option v-for='item in dictList' :key='item.value' :value='item.value'>
                  {{ item.text }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span='24' style='margin-top: 10px'>
            <a-form-item :labelCol='labelCol' :wrapperCol='wrapperCol' class='two-words' label='图标' required>
<!--              <j-svg-upload v-decorator="['typeIcon']" :isMultiple='false' bizPath='image/productIcon'>-->
<!--              </j-svg-upload>-->
              <div v-if="model.typeIcon"  class='svg-upload-box'>
                <YqSvg :type="model.typeIcon" :outSide="outSideUrl + model.typeIcon"
                       style="color: #1677ff; font-size: 36px;" />
                <div class='icon-del'>
                  <span @click="delIcon">
                    <a-icon type="delete" style='font-size: 24px' />
                  </span>
                </div>
              </div>
              <j-upload
                v-else
                v-decorator="['typeIcon',validatorRules.typeIcon]"
                fileType='image'
                specialFileType='panel_svg'
                :multiple='false'
                :number='1'
                bizPath='image/productIcon'
                :size='3*1024*1024'
                accept='.svg'
                @change="handleIconChange">
              </j-upload>
            </a-form-item>
          </a-col>
          <a-col :span='24' style='margin-top: 10px;color:orangered'>
           温馨提示：图标必须使用svg图片 图片资源需要平台建设方提供
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>
<script>
  import {
    httpAction,
    getAction
  } from '@/api/manage'
  import pick from 'lodash.pick'
  import {
    validateDuplicateValue
  } from '@/utils/util'
  import {
    ajaxGetDictItems
  } from '@/api/api'
  import JFormContainer from '@/components/jeecg/JFormContainer'
  import JSvgUpload from '@/components/jeecg/JSvgUpload'
  import JDictSelectTag from '@/components/dict/JDictSelectTag'
  import YqSvg from '@/components/tools/SvgIcon/index.js'
  export default {
    name: 'CmdbAssetsCategory1Form',
    components: {
      JFormContainer,
      JDictSelectTag,
      JSvgUpload,
      YqSvg
    },
    props: {
      //流程表单data
      formData: {
        type: Object,
        default: () => {},
        required: false,
      },
      //表单模式：true流程表单 false普通表单
      formBpm: {
        type: Boolean,
        default: false,
        required: false,
      },
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false,
      },
    },
    data() {
      return {
        form: this.$form.createForm(this),
        model: {typeIcon: ''},
        dictList: [],
        outSideUrl: window._CONFIG['downloadUrl'] + '/',
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          },
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          },
        },
        confirmLoading: false,
        validatorRules: {
          typeName: {
            rules: [{
                required: true,
                message: '请输入类型名称!'
              },
              {
                min: 2,
                message: '类型名称长度应在 2-20 之间！',
                trigger: 'blur'
              },
              {
                max: 20,
                message: '类型名称长度应在 2-20 之间！',
                trigger: 'blur'
              }
            ],
          },
          typeCode: {
            rules: [{
                required: true,
                message: '请输入类型标识!'
              },
              {
                min: 2,
                message: '类型标识长度应在 2-20 之间！',
                trigger: 'blur'
              },
              {
                max: 20,
                message: '类型标识长度应在 2-20 之间！',
                trigger: 'blur'
              }
            ],
          },
          dictValue: {
            rules: [{
              required: true,
              message: '请选择所属分类!'
            }],
          },
          typeIcon: {
            rules: [{
              required: true,
              message: '请上传图标!'
            }],
          },
        },
        url: {
          add: '/device/panelType/add',
          edit: '/device/panelType/edit',
          queryById: '/device/panelType/queryById',
        },
      }
    },
    computed: {
      showIconBox() {
        return this.form.getFieldValue('typeIcon')
      },
      formDisabled() {
        if (this.formBpm === true) {
          if (this.formData.disabled === false) {
            return false
          }
          return true
        }
        return this.disabled
      },
      showFlowSubmitButton() {
        if (this.formBpm === true) {
          if (this.formData.disabled === false) {
            return true
          }
        }
        return false
      },
    },
    created() {
      this.showFlowData()
    },
    mounted() {
      this.initDictData()
    },
    methods: {
      add() {
        this.edit({})
      },
      initDictData() {
        //根据字典Code, 初始化字典数组
        ajaxGetDictItems('device_Config_value', null).then((res) => {
          if (res.success) {
            this.dictList = res.result
          }
        })
      },
      delIcon() {
        this.$set(this.model, 'typeIcon', '')
        this.$nextTick(() => {
          this.form.setFieldsValue({
            typeIcon: ''
          })
        })
      },
      edit(record) {
        this.form.resetFields()
        Object.assign(this.model, record)
        this.visible = true
        this.$nextTick(() => {
          this.form.setFieldsValue(
            pick(
              this.model,
              'typeName',
              'typeCode',
              'typeIcon',
              'dictValue',
            )
          )
        })

      },
      //渲染流程表单数据
      showFlowData() {
        if (this.formBpm === true) {
          let params = {
            id: this.formData.dataId
          }
          getAction(this.url.queryById, params).then((res) => {
            if (res.success) {
              this.edit(res.result)
            }
          })
        }
      },
      submitForm() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.model.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'put'
            }
            let formData = Object.assign(this.model, values)
            httpAction(httpurl, formData, method)
              .then((res) => {
                if (res.success) {
                  that.$message.success(res.message)
                  that.$emit('ok')
                } else {
                  that.$message.warning(res.message)
                }
              })
              .finally(() => {
                that.confirmLoading = false
                // that.loadData()
              })
          }
        })
      },
      handleIconChange(file) {
        this.model.typeIcon = file
      },
      popupCallback(row) {
        this.form.setFieldsValue(
          pick(
            row,
            'typeName',
            'typeCode',
            'typeIcon',
            'dictValue',
          )
        )
      },
    },
  }
</script>
<style lang="less" scoped>
  ::v-deep .two-words>div>label {
    letter-spacing: 4px;
  }

  ::v-deep .two-words>div>label::after {
    letter-spacing: 0px;
  }
  .svg-upload-box {
    border: 1px dashed #d9d9d9;
    border-radius: 4px;
    background-color: #fafafa;
    display: table-cell;
    width: 104px;
    height: 104px;
    padding: 8px;
    text-align: center;
    vertical-align: middle;
    position: relative;
  }
  .icon-del{
    width: 106px;
    height: 106px;
    position: absolute;
    top:-1px;
    left:-1px;
    background-color: rgba(255, 255, 255, 0.8);
    display: none;
    justify-content: center;
    align-items: center;
  }
  .svg-upload-box:hover:hover{
    .icon-del{
      display: flex;
    }
  }
</style>