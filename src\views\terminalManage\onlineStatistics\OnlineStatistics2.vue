<template>
  <a-row :gutter="10" style="height: 100%;overflow: hidden" class="vScroll zhl zhll">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <div class="gutter-example">
        <a-row :gutter="16" >
          <a-col :lg="6" :md="12" :xs="24" class='col-example'>
            <a-row class="row-example">
              <a-col :span='24' class="gutter-row col-one">
                <div class="gutter-box">
                  <div class="gutter-box-left-top">
                    <span>分发总数</span>
                  </div>
                  <div class="gutter-box-left-bottom">
                    <span>{{ statisticsInfo.planNum }}</span>
                  </div>
                </div>
              </a-col>
            </a-row>
          </a-col>

          <a-col  :lg="6" :md="12" :xs="24" class='col-example'>
            <a-row class="row-example">
              <a-col :span='24' class="gutter-row col-four">
                <div class="gutter-box">
                  <div class="gutter-box-left-top">
                    <span>注册总数</span>
                  </div>
                  <div class="gutter-box-left-bottom">
                    <span>{{ statisticsInfo.allNum }}</span>
                  </div>
                </div>
              </a-col>
            </a-row>
          </a-col>

<!--          <a-col  :lg="6" :md="12" :xs="24" class='col-example'>
            <a-row class="row-example">
              <a-col :span='24' class="gutter-row col-three">
                <div class="gutter-box">
                  <div class="gutter-box-left-top">
                    <span>已开机（联网超过10分钟）</span>
                  </div>
                  <div class="gutter-box-left-bottom">
                    <span>{{ statisticsInfo.openMoreTY }}</span>
                  </div>
                </div>
              </a-col>
            </a-row>
          </a-col>

          <a-col  :lg="6" :md="12" :xs="24" class='col-example'>
            <a-row class="row-example">
              <a-col :span='24' class="gutter-row col-two">
                <div class="gutter-box">
                  <div class="gutter-box-left-top">
                    <span>已开机（联网不足10分钟）</span>
                  </div>
                  <div class="gutter-box-left-bottom">
                    <span>{{ statisticsInfo.openLessTY }}</span>
                  </div>
                </div>
              </a-col>
            </a-row>
          </a-col>-->
          <a-col  :lg="6" :md="12" :xs="24" class='col-example'>
            <a-row class="row-example">
              <a-col :span='24' class="gutter-row col-three">
                <div class="gutter-box">
                  <div class="gutter-box-left-top">
                    <span>已开机</span>
                  </div>
                  <div class="gutter-box-left-bottom">
                    <span>{{parseInt( statisticsInfo.openMoreTY) + parseInt( statisticsInfo.openLessTY)}}</span>
                  </div>
                </div>
              </a-col>
            </a-row>
          </a-col>

          <a-col  :lg="6" :md="12" :xs="24" class='col-example'>
            <a-row class="row-example">
              <a-col :span='24' class="gutter-row col-two">
                <div class="gutter-box">
                  <div class="gutter-box-left-top">
                    <span>未开机</span>
                  </div>
                  <div class="gutter-box-left-bottom">
                    <span>{{parseInt(statisticsInfo.allNum) -parseInt( statisticsInfo.openMoreTY)- parseInt(statisticsInfo.openLessTY) }}</span>
                  </div>
                </div>
              </a-col>
            </a-row>
          </a-col>
        </a-row>
      </div>
      <!-- 查询区域 -->
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <div class="table-page-search-wrapper-style">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="单位">
                  <a-tree-select
                    v-model="searchKey"
                    tree-node-filter-prop="title"
                    :replaceFields="replaceFields"
                    :treeData="selectOption"
                    show-search
                    :searchValue="bsearchKey"
                    style="width: 100%"
                    multiple
                    :maxTagCount='1'
                    :dropdownMatchSelectWidth="true"
                    :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                    placeholder="请选择单位"
                    allow-clear
                    @change="onChangeTree"
                    @search="onSearch"
                    @select="onSelect"
                  >
                  </a-tree-select>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="统计时间">
                  <a-range-picker
                    class="a-range-picker-choice-date"
                    :key="updateKey"
                    v-model="queryParam.warehousingTime"
                    :disabled-date="disabledDate"
                    format="YYYY-MM-DD"
                    :placeholder="['开始时间', '截止时间']"
                    @calendarChange="calendarPriceRangeChange"
                    @openChange="openChange"
                    @change="onChangePicker"
                    :default-value="[startTime, endTime]"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="colBtnsSpan()">
                <span
                  class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                >
                  <a-button type="primary" class="btn-search btn-search-style" @click="dosearch">查询</a-button>
                  <a-button class="btn-reset btn-reset-style" @click="doreset">重置</a-button>
                  <a v-if="isVisible" class="btn-updown-style" @click="doToggleSearch">
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered="false" style="width: 100%; flex: auto">
        <div class="table-operator table-operator-style">
          <a-button class="btn-add" @click="selfexport">导出</a-button>
        </div>
        <a-table
          ref="table"
          bordered
          :rowKey="(record,index)=>{return index}"
          :columns="columns"
          :dataSource="dataSource"
          :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
          :pagination="ipagination"
          :loading="loading"
          @change="handleTableChange"
        >
          <template slot="rate" slot-scope="text">
            <span>{{ text }}%</span>
          </template>
          <template slot="tooltip" slot-scope="text">
            <a-tooltip placement="topLeft" :title="text" trigger="hover">
              <div class="tooltip">
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
      </a-card>
    </a-col>
  </a-row>
</template>

<script>
import moment from 'moment'
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import JSuperQuery from '@/components/jeecg/JSuperQuery.vue'
import { queryAssetsCategoryTreeList } from '@/api/device'
import { httpAction, getAction, deleteAction } from '@/api/manage'
import JDictSelectTag from '@/components/dict/JDictSelectTag.vue'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'

export default {
  name: 'OnlineStatistics',
  mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
  components: {
    JSuperQuery,
    JDictSelectTag,
  },
  data() {
    return {
      updateKey: 0,
      startTime: moment().startOf('day').subtract(4, 'days'), // 创建起始时间-筛选框
      endTime: moment().endOf('day').subtract(1, 'days'), // 创建结束时间-筛选框
      formItemLayout: {
        labelCol: {
          style: 'width:80px',
        },
        wrapperCol: {
          style: 'width:calc(100% - 80px)'
        }
      },
      offsetDays: 86400000 * 9, //最多选择7天
      searchKey: undefined,
      bsearchKey: '',
      value: undefined,
      replaceFields: {
        children: 'children',
        title: 'departName',
        key: 'key',
        value: 'id',
      },
      //tree
      selectOption: [],
      batchEnable: 1,
      description: '设备表管理页面',
      firstTitle: '', //存储搜素tree的第一个title
      // 树
      assetsCategoryTree: [],
      treeData: [],
      expandedKeys: [],
      searchValue: '',
      autoExpandParent: true,
      dropTrigger: '',
      selectedKeys: [],
      selectedTitle: '',
      checkedKeys: [],
      checkStrictly: true,
      // iExpandedKeys: [],
      currFlowId: '',
      currFlowName: '',
      rightClickSelectedBean: {},
      // 表头
      columns: [
        {
          title: '单位',
          dataIndex: 'deptName',
          scopedSlots: { customRender: 'tooltip' },
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 150px;max-width:400px'
            return { style: cellStyle }
          }
        },
        {
          title: '分发数',
          dataIndex: 'planNumber',
          customCell: () => {
            let cellStyle = 'text-align: right;min-width: 100px;max-width:300px'
            return { style: cellStyle }
          }
        },
        {
          title: '注册数',
          dataIndex: 'count',
          customCell: () => {
            let cellStyle = 'text-align: right;min-width: 100px;max-width:150px'
            return { style: cellStyle }
          }
        },
        {
          title: '未开机',
          dataIndex: 'offCount',
          customCell: () => {
            let cellStyle = 'text-align: right;min-width: 100px;max-width:300px'
            return { style: cellStyle }
          }
        },
      /*  {
          title: '已开机（联网超过10分钟）',
          dataIndex: 'yesThreeCount',
          customCell: () => {
            let cellStyle = 'text-align: right;min-width: 100px;max-width:300px'
            return { style: cellStyle }
          }
        },
        {
          title: '已开机（联网不足10分钟）',
          dataIndex: 'noThreeCount',
          customCell: () => {
            let cellStyle = 'text-align: right;min-width: 100px;max-width:300px'
            return { style: cellStyle }
          }
        },
        {
          title: '在线数',
          dataIndex: 'onlineTotal',
          customCell: () => {
            let cellStyle = 'text-align: right;min-width: 100px;max-width:300px'
            return { style: cellStyle }
          }
        },*/
        {
          title: '已开机',
          dataIndex: 'yesThreeCount',
          customCell: () => {
            let cellStyle = 'text-align: right;min-width: 100px;max-width:300px'
            return { style: cellStyle }
          }
        },
        {
          title: '有效使用数',
          dataIndex: 'effectiveCount',
          customCell: () => {
            let cellStyle = 'text-align: right;min-width: 100px;max-width:300px'
            return { style: cellStyle }
          }
        },
        {
          title: '有效使用率(%)',
          dataIndex: 'rate',
          customRender: (text, record, index) => {
            let rate = text
            if(rate==null||rate==undefined||rate==''||rate <= 0){
              rate = 0
            }
            return rate + '%'
          },
          customCell: () => {
            let cellStyle = 'text-align: right;min-width: 100px;max-width:300px'
            return { style: cellStyle }
          }
        },
      ],
      url: {
        list: '/device/statis/findRate',
        getStatisticsNum: '/device/statis/findDeviceStatisDay',
        exportXlsUrl: '/device/statis/findRateExcel',
        importExcelUrl: 'device/deviceInfo/importExcel',
        getUrl: '/device/deviceInfo/getUrl',
      },
      dataSource: [], //table数据
      dictOptions: {},
      superFieldList: [],
      name: '',
      status: '',
      statuslist: [
        {
          name: '在线',
          code: '1',
        },
        {
          name: '离线',
          code: '0',
        },
        {
          name: '告警',
          code: '2',
        },
      ],
      categoryId: '', //选取tree的key
      statisticsInfo: {
        allNum: 0,
        openLessTY: 0,
        openMoreTY: 0,
        planNum: 0,
      },
    }
  },

  created() {},
  mounted() {
    if (this.startTime && this.endTime) {
      this.queryParam.startTime = this.startTime.format('YYYY-MM-DD')
      this.queryParam.endTime = this.endTime.format('YYYY-MM-DD')
    }
    this.select()
    this.getStatisticsNum()
  },

  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
  },
  methods: {
    //table分页,由于后端一次返回的是所有数据，所以翻页就不重新请求数据了
    handleTableChange(pagination, filters, sorter) {
      //分页、排序、筛选变化时触发
      //TODO 筛选
      if (Object.keys(sorter).length > 0) {
        this.isorter.column = sorter.field
        this.isorter.order = 'ascend' == sorter.order ? 'asc' : 'desc'
      }
      this.ipagination = pagination
      //this.loadData()
    },
    //根据选择的开始时间/结束时间，动态渲染要禁用的日期
    disabledDate(current) {
      if (this.selectPriceDate) {
        let selectV = moment(this.selectPriceDate, 'YYYY-MM-DD').valueOf()
        if (moment(new Date(selectV + this.offsetDays), 'YYYY-MM-DD') > moment().startOf('day')) {
          return (
            current > moment().startOf('day') || current < moment(new Date(selectV - this.offsetDays), 'YYYY-MM-DD')
          )
        } else {
          return (
            current > moment(new Date(selectV + this.offsetDays), 'YYYY-MM-DD') ||
            current < moment(new Date(selectV - this.offsetDays), 'YYYY-MM-DD')
          )
        }
      } else {
        return current > moment().startOf('day')
      }
    },
    openChange(status) {
      if (!status) {
        this.selectPriceDate = ''
      }
    },
    //选择开始时间/结束时间
    calendarPriceRangeChange(date) {
      this.selectPriceDate = date[0]
    },
    select() {
      ///sys/sysDepart/queryAllTree
      getAction('/device/statis/queryMyDeptTreeList').then((res) => {
        if (res.success && res.result ) {
          for (let i = 0; i < res.result.length; i++) {
            let temp = res.result[i]
            this.selectOption.push(temp)
          }
        }
        // for (let i = 0; i < res.length; i++) {
        //   let temp = res[i]
        //   this.selectOption.push(temp)
        // }
      })
    },

    onChangeTree(value) {
      this.value = value
      // this.queryParam.deptId = value.join(",")
    },
    onSearch(e) {
      this.bsearchKey = e
    },
    onSelect() {},
    onChangePicker(value, dateString) {
      this.startTime = value[0]
      this.endTime = value[1]
    },
    getStatisticsNum() {
      getAction(this.url.getStatisticsNum).then((res) => {
        if (res.code == 200) {
          this.statisticsInfo.allNum = res.result ? res.result.deviceTotle : 0
          this.statisticsInfo.openLessTY = res.result ? res.result.onNotPassCount : 0
          this.statisticsInfo.openMoreTY = res.result ? res.result.onPassCount : 0
          this.statisticsInfo.planNum = res.result ? res.result.planNumber : 0
        } else {
          this.$message.error(res.message)
        }
      })
    },
    //表单查询,点击查询按钮，默认查询第一页
    selfexport() {
      if (!this.searchKey && this.bsearchKey) {
        this.searchKey = this.bsearchKey
      }
      if (Array.isArray(this.searchKey)) {
        this.queryParam.deptId = this.searchKey.join(',')
      } else if (typeof this.searchKey === 'string') {
        this.queryParam.deptId = this.searchKey
        // this.searchKey = ""
      }
      this.handleExportXls('单位统计')
    },

    dosearch() {
      if (!this.searchKey && this.bsearchKey) {
        this.searchKey = this.bsearchKey
      }
      if (Array.isArray(this.searchKey)) {
        this.queryParam.deptId = this.searchKey.join(',')
      } else if (typeof this.searchKey === 'string') {
        this.queryParam.deptId = this.searchKey
        // this.searchKey = ""
      }
      if (this.startTime && this.endTime) {
        this.queryParam.startTime = this.startTime.format('YYYY-MM-DD')
        this.queryParam.endTime = this.endTime.format('YYYY-MM-DD')
      } else {
        this.queryParam.startTime = null
        this.queryParam.endTime = null
      }
      this.bsearchKey = ''
      this.loadData(1)
    },
    //表单重置
    doreset() {
      this.updateKey++
      ;(this.startTime = moment().startOf('day').subtract(4, 'days')), // 创建起始时间-筛选框
        (this.endTime = moment().endOf('day').subtract(1, 'days')), // 创建结束时间-筛选框
        (this.queryParam.startTime = this.startTime.format('YYYY-MM-DD'))
      this.queryParam.endTime = this.endTime.format('YYYY-MM-DD')
      //重置form表单，不重置tree选中节点
      this.queryParam = {
        deptId: '',
      }
      this.searchKey = undefined
      this.bsearchKey = ''
      this.loadData(1)
      this.type = undefined
    },
  },
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';

.col-example{
  margin-bottom: 16px;
}
.col-one {
  padding-left: 0px !important;
  border-radius: 3px;
  background-image: linear-gradient(90deg, #5d95fc 5%, #aecbff 97%);
}
.col-two {
  background-image: linear-gradient(90deg, #29ca7f 4%, #c3fcc0 98%);
  padding-left: 0px !important;
  margin-right: 12px !important;
  border-radius: 3px;
}
.col-three {
  background-image: linear-gradient(90deg, #f44967 5%, #fdbac5 93%);
  padding-left: 0!important;
  margin-right: 16px !important;
  border-radius: 3px;
}
.col-four {
  background-image: linear-gradient(90deg, #49f46e 5%, #fdbac5 93%);
  padding-left: 0!important;
  margin-right: 16px !important;
  border-radius: 3px;
}
.gutter-box{
  height: 1.2875rem /* 103/80 */;
  margin: 0.2rem /* 16/80 */ 0.3rem /* 24/80 */;
  padding: 0.125rem /* 10/80 */ 0.125rem;
  background: rgba(255, 255, 255, 0.1);

  .gutter-box-left-top {
    height: 50%;
    display: flex;
    justify-content: center;

    span {
      font-family: MicrosoftYaHei;
      font-size: 0.2rem /* 16/80 */;
      color: rgba(255, 255, 255, 0.85);
      display: flex;
      align-items: center;
    }
  }

  .gutter-box-left-bottom {
    height: 50%;
    display: flex;
    justify-content: center;

    span {
      font-family: Eurostile-Bold;
      font-size: 0.5rem /* 40/80 */;
      color: #ffffff;
      display: flex;
      align-items: center;
    }
  }
}

.div-table-container {
  /* padding: 18px 30px 18px 24px; */
  /* background-color: white; */
  //margin-top: 16px;
  /* margin-right: -9px; */
  /* height: calc(100% - 158px); */
}

.gutter-example {
  width: 100%;
}

.row-example {
  margin: 0px !important;
}

.gutter-row {
  padding-right: 0px !important;
  //margin-right: 10px;
  //margin-bottom: 16px;
}
.gutter-row-four {
  padding-right: 0px !important;
  border-radius: 3px;
  margin-bottom: 16px;
}

.posi-col {
  position: relative;
}

.sync-img {
  position: absolute;
  top: 14px;
  right: 20px;
  cursor: pointer;
  color: #fff;
  z-index: 100;
}

.p-device-status {
  text-align: center;
  height: 30px;
  line-height: 30px;
  margin-bottom: 0px;
}

.span-title {
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
}

.span-num {
  font-family: PingFangSC-Medium;
  font-size: 24px;
}

.color-blue {
  color: #409eff;
}

.color-green {
  color: #139b33;
}

.color-red {
  color: #df1a1a;
}

.color-grey {
  color: #868686;
}

.table-page-search-wrapper {
  //margin-right: -9px;
}

.table-operator {
  //margin-bottom: 10px;
}

.ant-table-row-cell-break-word span a {
  color: #409eff !important;
}

//.ant-row .ant-col-3 .ant-card .ant-card-body {
//  height: 810px !important;
//}
/*表头样式*/
::v-deep .ant-table-thead > tr > th {
  text-align: center;
  white-space: nowrap;
}

/*内容对齐方式、省略显示*/
::v-deep .ant-table-tbody > tr > td {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}
</style>


