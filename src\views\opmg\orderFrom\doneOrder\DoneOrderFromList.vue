<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll zxw">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <!-- 查询区域 -->
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <div class="table-page-search-wrapper-style">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="12" ref="row">
             <a-col :span="spanValue">
                <!--起止时间选择-->
                <a-form-item label="创建日期:">
                  <a-range-picker
                    @change="onChange"
                    v-model="queryParam.createTimeRange"
                    format="YYYY-MM-DD"
                    :placeholder="['开始时间', '截止时间']"
                    class="a-range-picker-choice-date"
                  />
                </a-form-item>
              </a-col>

              <a-col :span="colBtnsSpan()">
                <span
                  class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                >
                  <a-button type="primary" @click="searchQuery" class="btn-search-style">查询</a-button>
                  <a-button @click="searchReset" style="margin-left: 10px" class="btn-reset-style">重置</a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <!-- 查询区域-END -->

      <a-card :bordered="false" style="flex: auto" class="core">
        <!-- 操作按钮区域 -->
        <a-row class="lastBtn2">
          <div class="table-operator">
            <!-- <a-button @click="handleAdd">新增</a-button> -->
            <!-- <a-button @click="handleExportXls('软件管理表')">导出</a-button>  -->
            <a-dropdown v-if="selectedRowKeys.length > 0" v-has="'done:delete'">
              <a-menu slot="overlay" style='text-align: center'>
                <a-menu-item key="1" @click="batchDel">删除</a-menu-item>
              </a-menu>
              <a-button> 批量操作 <a-icon type="down" /></a-button>
            </a-dropdown>
          </div>
        </a-row>

        <!-- table区域-begin -->
        <a-table
          ref="table"
          bordered
          rowKey="id"
          :columns="columns"
          :dataSource="dataSource"
          :pagination="ipagination"
          :loading="loading"
          :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          class="j-table-force-nowrap"
          @change="handleTableChange"
        >
          <template slot="htmlSlot" slot-scope="text">
            <div v-html="text"></div>
          </template>
          <template slot="imgSlot" slot-scope="text">
            <span v-if="!text" style="font-size: 14px">无图片</span>
            <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width: 80px; font-size: 14px" />
          </template>
          <template slot="fileSlot" slot-scope="text">
            <span v-if="!text" style="font-size: 14px">无文件</span>
            <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
              下载
            </a-button>
          </template>

          <span
            slot="action"
            slot-scope="text, record"
            class="caozuo"
            style="display: inline-block; white-space: nowrap; text-align: center"
          >
            <a @click="handleDetailPage(record)">查看</a>
            <!-- <a-divider type="vertical" />
            <a @click="handleEdit(record)">处理</a>
            <a-divider type="vertical" /> -->
            <!-- <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
              <a>删除</a>
            </a-popconfirm> -->
          </span>
          <template slot="tooltip" slot-scope="text">
            <a-tooltip placement="topLeft" :title="text" trigger="hover">
              <div class="tooltip">
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
      </a-card>

      <done-order-from-info-modal ref="modalForm" @ok="modalFormOk"></done-order-from-info-modal>
    </a-col>
  </a-row>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import DoneOrderFromInfoModal from './modules/DoneOrderFromInfoModal'
import JSuperQuery from '@/components/jeecg/JSuperQuery.vue'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'

export default {
  name: 'DoneOrderFromList',
  mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
  components: {
    DoneOrderFromInfoModal,
    JSuperQuery,
  },
  data() {
    return {
      description: '我的待办页面',
      // 表头
      columns: [
        {
          title: '执行人',
          dataIndex: 'implementUser',
        },
        {
          title: '执行时间',
          dataIndex: 'createTime',
        },
        {
          title: '巡检人',
          dataIndex: 'inspectUser',
        },
        {
          title: '巡检地点',
          dataIndex: 'inspectPlace',
        },
        {
          title: '巡检时间',
          dataIndex: 'inspectTime',
        },
        {
          title: '巡检内容',
          dataIndex: 'inspectContent',
          scopedSlots: {
            customRender: 'tooltip'
          },
          customCell: () => {
            let cellStyle = 'text-align: left; min-width: 150px;max-width:350px'
            return {
              style: cellStyle
            }
          },
        },
        {
          title: '操作',
          dataIndex: 'action',
          fixed: 'right',
          align: 'center',
          width: 147,
          scopedSlots: { customRender: 'action' },
        },
      ],
      url: {
        list: '/orderFrom/orderFromInfo/doneList',
        delete: '/software/devopsSoftwareInfo/delete',
        deleteBatch: '/orderFrom/orderFromInfo/deleteBatch',
        exportXlsUrl: '/software/devopsSoftwareInfo/exportXls',
        importExcelUrl: 'software/devopsSoftwareInfo/importExcel',
      },
      dictOptions: {},
      superFieldList: [],
    }
  },
  created() {
    this.getSuperFieldList()
  },
  mounted() {},
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
  },
  methods: {
    initDictConfig() {},
    getSuperFieldList() {
      let fieldList = []
      fieldList.push({ type: 'string', value: 'softwareName', text: '软件名称', dictCode: '' })
      fieldList.push({ type: 'string', value: 'softwareDescribe', text: '描述', dictCode: '' })
      fieldList.push({ type: 'string', value: 'createBy', text: '创建人', dictCode: '' })
      fieldList.push({ type: 'datetime', value: 'createTime', text: '创建日期' })
      fieldList.push({ type: 'string', value: 'updateBy', text: '更新人', dictCode: '' })
      fieldList.push({ type: 'datetime', value: 'updateTime', text: '更新日期' })
      fieldList.push({ type: 'string', value: 'sysOrgCode', text: '所属部门', dictCode: '' })
      this.superFieldList = fieldList
    },
    onChange(date, dateString) {
      this.queryParam.startTime = dateString[0]
      this.queryParam.endTime = dateString[1]
    },
  },
}
</script>
<style lang="less" scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
.table-page-search-wrapper .ant-form-inline .ant-form-item {
  margin-bottom: 0 !important;
}
.ant-table-pagination.ant-pagination {
  margin: 16px 0 0 0 !important;
}
::v-deep .ant-table-thead > tr > th {
  text-align: center;
}
::v-deep .ant-table-tbody > tr > td {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;

  &:nth-child(2),
  &:nth-child(3) {
    text-align: left;
  }

  &:nth-child(3) {
    min-width: 200px;
    max-width: 250px;
  }
}
</style>
