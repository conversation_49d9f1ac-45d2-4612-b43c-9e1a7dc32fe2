<template>
  <div style="height:100%">
    <keep-alive exclude='DeviceInfoModal,Prometheus'>
      <component style="height:100%" :is="pageName" :data="data" :is-editing='true' :render-states='renderStates'/>
    </keep-alive>
  </div>
</template>
<script>
import Prometheus from '@views/jkdjgl/model/Prometheus.vue'
import PrometheusChildren from '@views/jkdjgl/model/PrometheusChildren.vue'
import DeviceInfoModal from '@/views/devicesystem/deviceshow/DeviceInfoModal'
export default {
  name: "cloudInfoMange",
  data() {
    return {
      isActive: 0,
      data: {},
      renderStates:{
        showBaseInfo:true,
        showStateInfo:true,
        showDeviceFunction:false,
        showCloudHost:false,
        showAlarm:false,
      },
      prometheusData: null,
      parentIndex: 0,
    };
  },
  components: {
    Prometheus,
    PrometheusChildren,
    DeviceInfoModal
  },
  created() {
    this.pButton1(0);
  },
  //使用计算属性
  computed: {
    pageName() {
      switch (this.isActive) {
        case 0:
          return "Prometheus";
          break;
        case 1:
          return "PrometheusChildren";
          break;
        default:
          return "DeviceInfoModal";
          break;
      }
    }
  },
  methods: {
    pButton1(index) {
      //信息返回判断是否是子设备列表跳转
      if(this.prometheusData && this.parentIndex == 1){
        this.pButton2(this.parentIndex,this.prometheusData)
        this.parentIndex = 0;
      }
      else if (this.prometheusData) {
        //子设备返回
        this.prometheusData = null;
        this.isActive = index;
      }else{
        //父级信息返回
        this.isActive = index;
      }

    },
    pButton2(index, item) {
      //查看子设备
      if(index == 1){
        this.prometheusData = item;
      }
      //查看子设备信息
      if(index == 2 && this.prometheusData){
        this.parentIndex = this.isActive;
        this.renderStates ={
          showBaseInfo:true,
            showStateInfo:true,
            showDeviceFunction:false,
            showCloudHost:false,
            showAlarm:true,
          showJournal:true,
        }
      }else if(index == 2){
        this.renderStates ={
          showBaseInfo:true,
          showStateInfo:true,
          showDeviceFunction:false,
          showCloudHost:false,
          showAlarm:false,
          showJournal:true,
        }
      }
      this.isActive = index;
      this.data = item;
    }
  }
}
</script>