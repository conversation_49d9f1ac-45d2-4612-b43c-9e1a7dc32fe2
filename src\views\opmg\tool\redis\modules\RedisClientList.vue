<template>
  <a-row :gutter='10' style='height: 100%' class='vScroll zxw'>
    <a-col style='width: 100%; height: 100%; display: flex; flex-direction: column'>
      <!-- 查询区域 -->
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <div class='table-page-search-wrapper-style'>
          <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
            <a-row :gutter='24' ref='row'>
              <a-col :span='spanValue'>
                <a-form-item label='连接名'>
                  <a-input
                    placeholder='连接名'
                    :allowClear='true'
                    autocomplete='off'
                    v-model='queryParam.databaseAlias' :maxLength="maxLength"
                  ></a-input>
                </a-form-item>
              </a-col>

              <a-col :span='colBtnsSpan()'>
                <span class='table-page-search-submitButtons'
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                >
                  <a-button type='primary' @click='searchQuery' class='btn-search-style'>查询</a-button>
                  <a-button @click='searchReset' style='margin-left: 10px' class='btn-reset-style'>重置</a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>

      <a-card :bordered='false' style='flex: auto' class='core'>
        <a-row class='lastBtn2'>
          <div class='table-operator'>
            <a-button @click='handleAdd'>新增</a-button>
            <a-dropdown v-if='selectedRowKeys.length > 0'>
              <a-menu slot='overlay' style='text-align: center'>
                <a-menu-item key='1' @click='batchDel'>删除</a-menu-item>
              </a-menu>
              <a-button> 批量操作
                <a-icon type='down' />
              </a-button>
            </a-dropdown>
          </div>
        </a-row>
        <!-- table区域-begin -->
        <a-table
          ref='table'
          bordered
          rowKey='id'
          :columns='columns'
          :dataSource='dataSource'
          :scroll='dataSource.length>0?{x:"max-content"}:{}'
          :pagination='ipagination'
          :loading='loading'
          :rowSelection='{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }'
          @change='handleTableChange'
        >
          <span slot='action' slot-scope='text, record'>
          <a @click='handleView(record)'>查看</a>
            <a-divider type='vertical' />
          <a @click='handleEdit(record)'>编辑</a>
            <a-divider type='vertical' />
            <a-popconfirm title='确定删除吗?' @confirm='() => handleDelete(record.id)'>
                <a>删除</a>
            </a-popconfirm>
        </span>
        </a-table>
      </a-card>
      <redis-client-modal ref='modalForm' @ok='modalFormOk'></redis-client-modal>
    </a-col>
  </a-row>
</template>

<script>
import '@assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import RedisClientModal from './RedisClientModal.vue'
import { filterMultiDictText } from '@comp/dict/JDictSelectUtil'
import JSuperQuery from '@comp/jeecg/JSuperQuery.vue'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
import { getAction } from '@api/manage'

export default {
  name: 'DevopsAutoInspectionList',
  mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
  components: {
    RedisClientModal,
    JSuperQuery
  },
  data() {
    return {
      maxLength:50,
      description: 'redis客户端',
      // 表头
      columns: [
        {
          title: '连接名',
          dataIndex: 'databaseAlias',
          customCell: () => {
            let cellStyle = 'text-align: center'
            return { style: cellStyle }
          }
        },
        {
          title: '连接地址',
          dataIndex: 'host',
          customCell: () => {
            let cellStyle = 'text-align: center'
            return { style: cellStyle }
          }
        },
        {
          title: '端口号',
          dataIndex: 'port',
          customCell: () => {
            let cellStyle = 'text-align: center'
            return { style: cellStyle }
          }
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          customCell: () => {
            let cellStyle = 'text-align: center'
            return { style: cellStyle }
          }
        },
        {
          title: '操作',
          dataIndex: 'action',
          fixed: 'right',
          align: 'center',
          width: 147,
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: '/redis/client/list',
        delete: '/redis/client/delete',
        deleteBatch: '/redis/client/deleteBatch',
      },
    }
  },
  mounted() {
  },
  computed: {},
  methods: {
    handleView(record){
      this.$parent.pButton1(1, record);
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
</style>
