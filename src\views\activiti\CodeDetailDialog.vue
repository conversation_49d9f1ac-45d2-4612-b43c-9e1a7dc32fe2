<template>
    <el-dialog
    :title = "title"
    :visible.sync="visible"
    :modal="modal"
    width="60%"
    :before-close="handleClose">
     <fm-generate-form :data="startFormJson" :value="variables" ref="generateStartForm">
      </fm-generate-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">关 闭</el-button>
    </span>
  </el-dialog>
</template>
<script>
import {getAction} from '@/api/manage'
export default {
  data() {
    return {
      title:"",
      visible:false,
      startFormJson: undefined,
      variables: undefined,
      modal:false
    }
  },
  computed: {
  },
  watch: {},
  mounted() {},
  methods: {
    see(key){
      this.visible=true;
      getAction('/flowableform/umpFlowableForm/queryByKey',{key}).then(res=>{
            if(res.success){
            var formData = res.result;
            if(formData &&formData.formJson){
               this.startFormJson =JSON.parse(formData.formJson);
            }
        }
    })
    },
    handleClose(){
      this.visible = false
    }
   
  }
}
</script>

<style lang="scss" scoped>

</style>
