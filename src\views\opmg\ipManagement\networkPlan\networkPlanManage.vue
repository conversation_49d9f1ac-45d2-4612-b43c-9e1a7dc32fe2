<template>
  <div style="height:100%">
    <keep-alive exclude='networkPlanDetails'>
      <component style="height:100%" :is="pageName" :data="data" />
    </keep-alive>
  </div>
</template>
<script>
  import networkPlanList from './networkPlanList'
  import networkPlanDetails from './modules/networkPlanDetails'
  import networkChildrenDetails from './modules/networkChildrenDetails'
  import segmentDetails from './modules/segmentDetails'
  import IPPlanDetails from './modules/IPPlanDetails'
  export default {
    name: "networkPlanManage",
    data() {
      return {
        isActive: 0,
        data: {},
      }
    },
    components: {
      networkPlanList,
      networkPlanDetails,
      networkChildrenDetails,
      segmentDetails,
      IPPlanDetails
    },
    created() {
      this.pButton1(0);
    },
    //使用计算属性
    computed: {
      pageName() {
        switch (this.isActive) {
          case 0:
            return "networkPlanList";
          case 1:
            return "networkPlanDetails";
          case 2:
            return "networkChildrenDetails";
          case 3:
            return "segmentDetails";
          default:
            return "IPPlanDetails";
        }
      }
    },
    methods: {
      pButton1(index) {
        this.isActive = index;
      },
      pButton2(index, item) {
        this.isActive = index;
        this.data = item
      }
    }
  }
</script>