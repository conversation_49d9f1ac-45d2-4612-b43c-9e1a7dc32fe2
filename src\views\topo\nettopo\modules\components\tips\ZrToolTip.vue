<template>
  <a-tooltip
    trigger='click'
    overlayClassName='panel-tip-card'
    :visible='visible'
    :placement='placement'
    :get-popup-container='getPopupContainer'
    >
    <template slot='title'>
      <div style='min-width: 300px' @mouseenter='enterTipCard' @mouseleave='leaveTipCard'>
        <div v-if="tipData && tipData.shape==='node'">
          <div class='node-title'>
            <span>{{ title }}</span>
          </div>
          <div style='margin-left:12px;margin-top: 8px'>
            <dept-info-tip v-if='tipData.type === "2"' v-bind="tipData"></dept-info-tip>
            <device-info-tip v-else-if='tipData.type === "0"' v-bind="tipData"></device-info-tip>
            <app-device-tip v-else-if='tipData.type === "1"' v-bind="tipData"></app-device-tip>
          </div>
        </div>
        <div v-else-if="tipData && tipData.shape==='edge'">
          <div v-if='tipData.server'>
            <div class='node-title'>
              <span>服务端：</span>
              <!--            <div class="rect-icon">
                          </div>-->
              <!--            <img :src="serverIcon" style="height: 18px;margin-right: 5px" />-->
              <span>{{  }}</span>
            </div>
            <a-descriptions :column='4' layout='vertical' size='small'>
              <a-descriptions-item label='状态1'>
              </a-descriptions-item>
              <a-descriptions-item label='状态2'></a-descriptions-item>
            </a-descriptions>
          </div>
          <!--        <div v-if="!tipData.client&&!tipData.server">暂无数据</div>-->
        </div>
      </div>

    </template>
    <span class='panel-tip-node'
          :style='`left:${left}px;top:${top}px;width:${width}px;height:${height}px;z-index:${tipZindex}`'
          @click='tipClick'> </span>
  </a-tooltip>
</template>

<script>
import DeptInfoTip from './DeptInfoTip.vue'
import DeviceInfoTip from './DeviceInfoTip.vue'
import AppDeviceTip from './AppDeviceTip.vue'

export default {
  name: 'ZrToolTip',
  components: { AppDeviceTip, DeviceInfoTip, DeptInfoTip },
  props: {
    left: {
      type: Number,
      default: 0,
      require: false
    },
    top: {
      type: Number,
      default: 0,
      require: false
    },
    width: {
      type: Number,
      default: 32,
      require: false
    },
    height: {
      type: Number,
      default: 32,
      require: false
    },
    visible: {
      type: Boolean,
      default: false,
      require: false
    },
    tipZindex: {
      type: Number,
      default: -1,
      require: false
    },
    tipData: {
      type: Object,
      default: () => {
        return null
      },
      require: false
    },
  },
  data() {
    return {}
  },
  created() {

  },
  mounted() {
  },
  computed: {
    title(){
      if(this.tipData){
        if(this.tipData.shape==='node'){
          let cell = this.tipData.cell
          return this.tipData.type==="2"?cell.attrs.label.text:"节点信息"
        }else{
          return "连线信息"
        }

      }
      return ""
    },
    placement() {
      if (this.left < window.screen.width / 3) {
        if (this.top >= (window.innerHeight * 2) / 3) {
          return 'topLeft'
        } else if (this.top < window.innerHeight / 3) {
          return 'bottomLeft'
        } else {
          return 'right'
        }
      } else if (this.left > (window.screen.width * 2) / 3) {
        if (this.top >= (window.innerHeight * 2) / 3) {
          return 'topRight'
        } else if (this.top < window.innerHeight / 3) {
          return 'bottomRight'
        } else {
          return 'left'
        }
      } else {
        if (this.top >= window.innerHeight / 2) {
          return 'top'
        } else {
          return 'bottom'
        }
      }
    },
  },
  watch: {
    tipData: {
      handler(newVal) {
        // console.log('提示数据 == ', newVal)
      },
      deep: false
    },
    visible(newVal) {
      if (newVal) {
        this.$nextTick(() => {
          this.$emit('enterTipCard')
        })
      } else {
        // this.$emit('hide')
      }
    }
  },
  methods: {
    tipClick() {
      this.$emit('cellClick')
    },
    getPopupContainer(trigger) {
      return trigger.parentElement
    },
    enterTipCard() {
      this.$emit('enterTipCard')
    },
    leaveTipCard() {
      this.$emit('leaveTipCard')
    }
  }
}
</script>
<style lang='less'>
.panel-tip-card {
  max-width: unset;
  .ant-tooltip-content{
    .ant-tooltip-arrow{
      display: none;
    }
    .ant-tooltip-inner{
      //background: rgba(0,14,40,0.8)
      background-image: url(/zrBigScreen/tipBg.png);
      background-color:transparent;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      padding: 24px 22px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0);
    }
  }
  .ant-descriptions-title {
    color: #fff;
    margin-bottom: 8px;
  }
  .ant-descriptions-view {
    width: auto;
    table {
      width: auto
    }

    .ant-descriptions-item-label {
      color: #fff;
    }
    .ant-descriptions-item-content {
      color: #fff;
    }
  }

  .detail-btn {
    text-align: center;
    margin-bottom: 8px;
    cursor: pointer;
  }
}
</style>
<style lang='less' scoped>
.panel-tip-node {
  position: fixed;
  z-index: -1;
  display: block;
  width: 32px;
  height: 32px;
  background: transparent;

}
.node-title {
  font-size: 21px;
  height: 48px;
  width: 100%;
  padding-left: 32px;
  display: flex;
  align-items: center;
  color: #FFFFFF;
  font-weight: bold;
  background-image: url(/zrBigScreen/tipTitleBg.png);
  background-size: auto;
  background-repeat: no-repeat;
  .rect-icon {
    width: 8px;
    height: 8px;
    border-radius: 1px;
    background: @primary-color;
    margin-right: 8px;
  }
}
</style>