<template>
  <div style="height:100%">
<!--    <keep-alive>-->
      <component style="height:100%" :is="pageName" :data="data" :table-params='tableParams' :showabled='true' />
<!--    </keep-alive>-->
  </div>
</template>
<script>
  import vCenterModal from './vCenterModal'
  import vCenterList from './vCenterList'
  export default {
    name: "vCenterMange",
    data() {
      return {
        isActive: 0,
        data: {},
        tableParams: {}
      };
    },
    components: {
      vCenterModal,
      vCenterList
    },
    created() {
      this.pButton1(0);
    },
    //使用计算属性
    computed: {
      pageName() {
        switch (this.isActive) {
          case 0:
            return "vCenterModal";
          default:
            return "vCenterList";
        }
      }
    },
    methods: {
      pButton1(index, item) {
        this.isActive = index;
        this.data = item;
      },
      pButton2(index, item) {
        this.isActive = index;
        this.data = item.data
        this.tableParams = {
          ipagination: item.ipagination,
          queryParam: item.queryParam
        }
      }
    }
  }
</script>