<template>
  <div>
    <a-spin :spinning="loading" wrapperClassName="custom-ant-spin">
      <div v-if="yData&&yData.length>0" style="padding-top: 0.175rem;width: 100%;height: calc(100% - 0.175rem)" ref="AlarmCategoryTrendChart"></div>
      <a-list :data-source="[]" v-else />
    </a-spin>
  </div>
</template>
<script>
import echarts from 'echarts/lib/echarts'
import { heightPixel, widthPixel } from '@views/statsCenter/com/calculatePixel'
import { getAction } from '@api/manage'
export default {
  props: {},
  data() {
    return {
      loading: false,
      myChart: null,
      xData: [],
      yData: [],
      legend: [],
      url: {
        alarmDeviceCategoryCount: '/openAPI/alarmDeviceCategoryCount'
      }
    }
  },
  mounted() {
    this.getAlarmDeviceCategoryCount()
    //this.getMockJson()
  },
  destroyed() {
    window.removeEventListener("resize", this.alarmCategoryTrendChartResize)
    if (this.myChart){
      this.myChart.dispose()
    }
  },
  methods: {
    getAlarmDeviceCategoryCount() {
      this.loading = true
      this.xData=[]
      this.yData=[]
      getAction(this.url.alarmDeviceCategoryCount).then((res) => {
        console.log('res.result==',res.result)
        if (res.success && res.result) {
          this.xData = res.result.categoryName || []
          this.yData = res.result.alarmCount || []
          if (this.yData.length > 0) {
            this.$nextTick(() => {
              this.drawChart()
            })
          }
        } else {
          if (this.myChart){
            this.myChart.dispose()
          }
         // this.$message.warning(res.message)
        }
        this.loading = false
      }).catch((err) => {
        this.loading = false
        if (this.myChart){
          this.myChart.dispose()
        }
        //this.$message.warning(err.message)
      })
    },

    getMockJson() {
      let alarmData = [
        {
          "levelName": '网络类型',
          'value': 42
        },
        {
          "levelName": '安全类型',
          'value': 50
        },
        {
          "levelName": '内存使用率',
          'value': 55
        },
        {
          "levelName": 'cup使用率',
          'value': 38
        },
        {
          "levelName": '磁盘使用率',
          'value': 20
        },
        {
          "levelName": '端口流量',
          'value': 43
        },
        {
          "levelName": '终端类型',
          'value': 60
        }
      ]
      this.xData = alarmData.map(item => item.levelName)
      this.yData = alarmData.map(item => item.value)
      this.$nextTick(() => {
        this.drawChart()
      })
      /*  getAction(location.origin+"/statsCenter/mock/homeData.json").then((res) => {
        if(res){
          console.log('res.deviceAlarmData===',res.deviceAlarmData)
        }
        this.loading = false
      }).catch((err)=>{
        this.loading = false
      })*/
    },
    // 趋势分析折线图
    drawChart() {
      let w2 = widthPixel(2)
      let w4 = widthPixel(4)
      let w10 = widthPixel(10)
      let w14 = widthPixel(14)
      let w12 = widthPixel(12)
      let w16 = widthPixel(16)
      let w20 = widthPixel(20)
      let w26 = widthPixel(26)
      let w32 = widthPixel(32)
      let w30 = widthPixel(30)
      let w60 = widthPixel(60)
      let w62 = widthPixel(62)

      this.myChart = echarts.init(this.$refs.AlarmCategoryTrendChart)
      var barWidth = w14;
      let leftOffset = w30
      // 地面
      const topPanel = echarts.graphic.extendShape({
        shape: {
          x: 0,
          y: 0
        },
        buildPath: function(ctx, shape) {
          //该数据点在图表画布上的像素坐标 [x像素位置, y像素位置]（相对于图表容器左上角）
          /* 假设 y 轴范围是 [0, 100]，图表高度为 400px，网格（grid）的 bottom 为 30px，那么：
          y=0 的像素位置可能是 400 - 30 = 370px（从顶部到底部的距离）。
          y=100 的像素位置可能是 30px（靠近顶部）。
         因此，shape.api.coord([0, 0]) 可能会返回类似 [50, 370]*/
          let width = shape.api.getWidth()
          /* console.log('width===',width)
          let start1=width*0.07+leftOffset
          let start2=width*0.07+leftOffset*2*/
          let start1 = leftOffset
          let start2 = leftOffset * 2
          const zeroPoint = shape.api.coord([0, 0]);
          const yAxisZero1 = zeroPoint[1] + w20;
          const yAxisZero2 = zeroPoint[1] - w12;

          // 绘制填充区域
          ctx.beginPath()
          // 有待完善 leftOffset即grid left的值
          ctx.moveTo(start1, yAxisZero1)
          ctx.lineTo(start2, yAxisZero2)
          /* ctx.lineTo(width - w30, yAxisZero2)
           ctx.lineTo(width - w60, yAxisZero1)*/
          ctx.lineTo(width - w20 - w2, yAxisZero2)
          ctx.lineTo(width - w20 - w32, yAxisZero1)
          ctx.closePath()
          ctx.fill(); // 填充颜色（由 style.fill 决定）

          // 绘制边框
          ctx.beginPath();
          ctx.moveTo(start1, yAxisZero1);
          ctx.lineTo(start2, yAxisZero2);
          ctx.lineTo(width - w20 - w2, yAxisZero2);
          ctx.lineTo(width - w20 - w32, yAxisZero1);
          ctx.closePath();
          ctx.stroke(); // 绘制边框（由 style.stroke 决定）
        }
      })
      const bottomPanel = echarts.graphic.extendShape({
        shape: {
          x: 0,
          y: 0
        },
        buildPath: function(ctx, shape) {
          let width = shape.api.getWidth()
          const zeroPoint = shape.api.coord([0, 0]);
          const yAxisZero1 = zeroPoint[1] + w26;
          const yAxisZero2 = zeroPoint[1] - w4;

          // 绘制填充区域
          ctx.beginPath()
          ctx.moveTo(w32, yAxisZero1)
          ctx.lineTo(w62, yAxisZero2)
          ctx.lineTo(width - w20, yAxisZero2)
          ctx.lineTo(width - w20 - w30, yAxisZero1)
          ctx.closePath()
          ctx.fill(); // 填充颜色（由 style.fill 决定）

          // 绘制边框
          ctx.beginPath();
          ctx.moveTo(w32, yAxisZero1);
          ctx.lineTo(w62, yAxisZero2);
          ctx.lineTo(width - w20, yAxisZero2);
          ctx.lineTo(width - w20 - w30, yAxisZero1);
          ctx.closePath();
          ctx.stroke(); // 绘制边框（由 style.stroke 决定）
        }
      })
      echarts.graphic.registerShape('topPanel', topPanel)
      echarts.graphic.registerShape('bottomPanel', bottomPanel)
      let option = {
        grid: {
          top: w20,
          left: leftOffset,
          right: w60,
          bottom: 0,
          containLabel: true // 确保轴标签不会被截断
        },
        tooltip: [
          {
            show: true,
            trigger: 'axis',
            transitionDuration: 0, //echart防止tooltip的抖动
            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: 'line', // 默认为直线，可选为：'line' | 'shadow'
              xAxisIndex: 0,//绑定x轴，鼠标移入对应x的柱体才出现轴线
              lineStyle: {
                type: 'dotted',
                color: '#ffffff'
              }
            }
          }
        ],
        legend: {
          show: false
        },
        dataZoom: [
          {
            id: 'dataZoomY',
            xAxisIndex: [0],
            show: false, //是否显示滑动条，不影响使用
            type: 'slider', // 这个 dataZoom 组件是 slider 型 dataZoom 组件
            startValue: 0, // 从头开始。
            endValue: 4,
            zoomLock: true,
            showDataShadow: false, //是否显示数据阴影 默认auto
            backgroundColor: 'rgba(255,255,255,0)',
            showDetail: false, //即拖拽时候是否显示详细数值信息 默认true
            realtime: true, //是否实时更新
            filterMode: 'filter',
            handleIcon: 'circle',
            handleStyle: {
              color: 'rgba(205,205,205,1)',
              borderColor: 'rgba(205,205,205,1)'
            },
            moveHandleSize: 0,
            brushSelect: false //刷选功能，设为false可以防止拖动条长度改变 ************（这是一个坑）
          },
          {
            type: 'inside',
            xAxisIndex: 0,
            zoomOnMouseWheel: false, //滚轮是否触发缩放
            moveOnMouseMove: true, //鼠标滚轮触发滚动
            moveOnMouseWheel: true
          }
        ],
        xAxis: [
          {
            type: 'category',
            boundaryGap: true, // true 表示两端留空，false 表示紧贴边缘
            xAxisIndex: 0,
            axisLine: {
              //坐标轴轴线相关设置。数学上的x轴
              show: false,
              lineStyle: {
                color: 'rgba(68,211,255,0.3)',
                width: 1,
                type: 'Dashed',
              },
            },
            axisLabel: {
              show: true,
              margin: w30,
              textStyle: {
                color: 'rgba(210,224,226,0.8)', //更改坐标轴文字颜色
                fontSize: w10,
                fontWeight: 300
              },
            },
            splitLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            data: this.xData,
          },
          // 辅助 xAxis（用于固定自定义图形）
          {
            type: 'category',
            gridIndex: 0,
            show: false, // 隐藏轴
            axisLabel: { show: false },
            axisLine: { show: false },
            axisTick: { show: false },
            splitLine: { show: false }
          }
        ],
        yAxis: [
          {
            type: 'value',
            position: 'left',
            yAxisIndex: 0,
            min: 0,
            zLevel: 1,
            // offset: w32,
            /* nameTextStyle: {
              color: 'rgba(250,250,250,.6)',
              fontSize: w12,
              padding: w10,
            },*/
            // min: 0,
            // max: this.max,
            splitLine: {
              show: false,
              lineStyle: {
                color: ['#194D5A'],
                width: 1,
                type: 'Dashed',
              },
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: 'rgba(68,211,255,0.3)',
                width: 1,
                // type: 'Dashed',
              }
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: 'rgba(255,255,255,0.65)', //更改坐标轴文字颜色
                fontSize: w10,
                fontWeight: 400
              }
            },
            axisTick: {
              show: false
            }
          }
        ],
        series: [
          {
            type: 'custom',
            silent: true,
            legendHoverLink: false,
            // coordinateSystem: null, // 不关联任何坐标系  //不能使用此属性，会覆盖整个系列，导致不渲染了
            xAxisIndex: 1, // 绑定到辅助 xAxis   //绑定不参与滚动的x轴
            tooltip: { show: false },
            z: 0,
            renderItem: (params, api) => {
              // const location = api.coord([0, 0])
              /*必须写成new echarts.graphic.渐变函数 方式地面开始才会渲染出来，否则鼠标移入才显示，不知道原因*/
              let topColor = new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                offset: 0,
                color: '#0A293D'
              },
                {
                  offset: 1,
                  color: '#0A293D'
                }
              ])

              let bottomColor = new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                offset: 0,
                color: 'rgba(10,41,61,0)'
              },
                {
                  offset: 1,
                  color: 'rgba(10,41,61,0)'
                }
              ])
              return {
                type: 'group',
                children: [
                  {
                    type: 'topPanel',
                    shape: {
                      api,
                    },
                    style: {
                      fill: topColor,// 填充颜色
                      stroke: '#13334A',    // 边框颜色
                      lineWidth: 2          // 边框宽度
                    }
                  },
                  {
                    type: 'bottomPanel',
                    coordinateSystem: null, // 不关联任何坐标系
                    shape: {
                      api,
                    },
                    style: {
                      fill: bottomColor,// 填充颜色
                      stroke: 'rgba(19,51,74,0.5)',    // 边框颜色
                      lineWidth: 2          // 边框宽度
                    }
                  }
                ]
              }
            },
            data: [0, 0]
          },
          {
            type: 'pictorialBar',
            symbolPosition: 'start',
            z:1,
            zLevel:1,
            silent: true,
            symbolOffset: [0, barWidth/4],
            symbolSize: [barWidth, barWidth / 2],
            hoverAnimation: false,
            tooltip: { show: false },
            itemStyle: {
              color: '#0D4357',
              shadowColor: '#0D4357',
              shadowBlur: barWidth,
              shadowOffsetY: -barWidth / 16,
            },
            data: this.yData,
          },
          {
            name: '告警次数',
            type: 'bar',
            z:0,
            zLevel:0,
            barWidth: w14,
            // barMinHeight:w14,
            xAxisIndex: 0,
            yxAxisIndex: 0,
            // barGap: w32,   // 柱子之间的间距占柱子宽度的 w32
            barGap: '15%',   // 柱子之间的间距占柱子宽度的 10%
            barCategoryGap: w32, // 不同类别之间的间距占分类区间的 w32
            label: {
              normal: {
                show: true,
                position: 'top',
                // padding: [0, 2],
                textStyle: {
                  fontSize: w10,
                  color: '#ffffff'
                },
                // formatter: '{c}' + ' ' + '个'
              }
            },
            labelLine: {
              show: false
            },
            data: this.yData,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: 'rgb(29,228,251)' // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: 'rgba(29,228,251,0)' // 100% 处的颜色
                    }
                  ], false)
              }
            }
          },
          {
            type: 'pictorialBar',
            symbolPosition: 'end',
            z:1,
            zLevel:1,
            silent: true,
            symbolOffset: [0, -barWidth/4],
            symbolSize: [barWidth, barWidth / 2],
            hoverAnimation: false,
            tooltip: { show: false },
            itemStyle: {
              color: '#58EEFF',
              shadowColor: '#58EEFF',
              shadowBlur: barWidth,
              shadowOffsetY: -barWidth / 16,
            },
            data: this.yData,
          }
        ]
      }
      this.myChart.setOption(option)
      window.addEventListener("resize", this.alarmCategoryTrendChartResize)
    },
    alarmCategoryTrendChartResize(){
      this.myChart.resize();
    }
  }
}
</script>

<style scoped lang="less">
::v-deep .ant-empty-description{
  color:#fff !important;
}
</style>
