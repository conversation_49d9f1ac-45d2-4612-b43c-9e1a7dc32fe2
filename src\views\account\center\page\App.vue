<template>
  <a-list>
    <a-list-item>
      <a-card :bordered="false">
        <div>
          <p style="margin-left:10px">姓名:
            <a-input type="text" id="realname" :disabled="dis" :value="realname" />
          </p>
          <p style="margin-left:-4px">手机号:
            <a-input type="text" id="phone" :disabled="dis" :value="phone" />
          </p>
          <p style="margin-left:-4px">座机号:
            <a-input type="text" id="telephone" :disabled="dis" :value="telephone" />
          </p>
          <p style="margin-left:10px">职务:
            <a-input type="text" id="post" :disabled="dis" :value="post" />
          </p>
          <p style="margin-left:10px">邮箱:
            <a-input type="text" id="email" :disabled="dis" :value="email" />
          </p>
          <p style="margin-left:10px">生日:
            <a-input type="text" id="brithday" :disabled="dis" :value="createTime" />
          </p>

          <span style="margin-left:10px">性别:</span>
          <a-radio-group class="radio-group" id="sex" v-model="sex" :disabled="dis">
            <a-radio id="sex1" :value=1 type="radio"> 男 </a-radio>
            <a-radio id="sex2" :value=2 type="radio"> 女 </a-radio>
          </a-radio-group>

        </div>
        <a-button style="margin-left:60px;margin-top:20px;height: 100%; overflow: hidden; overflow-y: auto"
          @click="save()">保存</a-button>
        <a-button style="margin-left:20px;height: 100%; overflow: hidden; overflow-y: auto" @click="editDis()">编辑
        </a-button>

        <a-divider />
      </a-card>
    </a-list-item>
  </a-list>
</template>

<script>
  import AList from 'ant-design-vue/es/list'
  import AListItem from 'ant-design-vue/es/list/Item'
  import { editUser } from '../../../../api/api'

  export default {
    name: 'Article',
    components: {
      AList,
      AListItem,
    },

    data() {
      return {
        dis: true,
        tagInputVisible: false,
        tagInputValue: '',
        post: '',
        departName: '',
        email: '',
        username: '',
        phone: '',
        telephone: '',
        signature: '',
        sex: '',
        realname: '',
        createTime: '',
        userid: '',
        noTitleKey: 'app',
        url: {
          getInfo: '/sys/user/getInfo', //个人信息
          updateUserInfo: '/sys/user/updateUserInfo', //编辑个人信息
          editUser: '/sys/user/edit', //编辑个人信息
        },
      }
    },
    mounted() {
      this.getInfo()
    },

    methods: {
      getInfo() {

        this.$http.get(this.url.getInfo).then((res) => {
          if (res.code == 200) {
            this.post = res.result.post
            this.departName = res.result.orgCode
            this.email = res.result.email
            this.username = res.result.username
            this.phone = res.result.phone
            this.telephone = res.result.telephone
            this.createTime = res.result.birthday
            this.realname = res.result.realname
            this.sex = res.result.sex
            this.userid = res.result.id
          } else {
            this.$message.error(res.message)
          }
        })
      },
      editDis() {
        this.dis = false
      },
      save() {

        var phone = document.getElementById('phone').value
        var telephone = document.getElementById('telephone').value
        var realname = document.getElementById('realname').value
        var post = document.getElementById('post').value
        var email = document.getElementById('email').value
        var brithday = document.getElementById('brithday').value

        this.$http
          .put(this.url.editUser, {
            id: this.userid,
            realname: realname,
            post: post,
            phone: phone,
            telephone: telephone,
            email: email,
            birthday: brithday,
            sex: this.sex,
          })
          .then((res) => {
            if (res.code == 200) {
              this.getInfo()
            } else {}
          })
        this.dis = true
      },
    },
  }
</script>
<style scoped>
  .ant-input {
    width: 500px;
    margin-left: 30px;
  }

  .ant-input-disabled {
    width: 500px;
    margin-left: 30px;
  }

  .radio-group {
    margin-left: 30px;
  }

  .s {
    margin-left: 20px;
  }
</style>