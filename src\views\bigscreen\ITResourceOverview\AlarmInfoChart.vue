<template>
  <div>
    <a-spin :spinning="loading" wrapperClassName="custom-ant-spin">
      <div v-if="this.yLineData&&this.yLineData.length > 0|| this.yBarData&&this.yBarData.length > 0" style="padding-top: 0.175rem;height: calc(100% - 0.175rem)" ref="AlarmSituationTrendChart"></div>
      <a-list :data-source="[]" v-else />
    </a-spin>
  </div>
</template>
<script>
import echarts from 'echarts/lib/echarts'
import { heightPixel, widthPixel } from '@views/statsCenter/com/calculatePixel'
import { getAction } from '@api/manage'

export default {
  props: {},
  data() {
    return {
      loading:false,
      myChart: null,
      xData: [],
      yBarData: [],
      yLineData: [],
      url:{
        alarmSituation: '/openAPI/alarmSituation'
      }
    }
  },
  mounted() {
    this.getAlarmSituation()
    // this.getMockJson()
  },
  destroyed() {
    window.removeEventListener("resize", this.alarmSituationTrendChartResize)
    if (this.myChart){
      this.myChart.dispose()
    }
  },
  methods: {
    getAlarmSituation() {
      this.loading = true
      this.xData=[]
      this.yLineData=[]
      this.yBarData=[]
      getAction(this.url.alarmSituation).then((res) => {
        if (res.success && res.result) {
          this.xData =res.result.time? res.result.time.map(item => item.slice(5)) :[]
          this.yLineData = res.result.alarmRate ||[]
          this.yBarData = res.result.alarmCount ||[]

          if (this.yLineData.length > 0|| this.yBarData.length > 0) {
            this.$nextTick(() => {
              this.drawChart()
            })
          }
        } else {
         // this.$message.warning(res.message)
          if (this.myChart) {
            this.myChart.dispose()
          }
        }
        this.loading = false
      }).catch((err) => {
        this.loading = false
        if (this.myChart) {
          this.myChart.dispose()
        }
        //this.$message.warning(err.message)
      })
    },
    getMockJson() {
      this.loading = true
      let alarmData = [
        {
          "value": 30,
          "name": "08-01"
        },
        {
          "value": 45,
          "name": "08-02"
        },
        {
          "value": 57,
          "name": "08-03"
        },
        {
          "value": 68,
          "name": "08-04"
        },
        {
          "value": 86,
          "name": "08-05"
        },
        {
          "value": 97,
          "name": "08-06"
        },
        {
          "value": 100,
          "name": "08-07"
        }
      ]
      this.xData = alarmData.map(item => item.name)
      this.yLineData = alarmData.map(item => item.value)
      this.yBarData = alarmData.map(item => item.value + 8)
      this.drawChart()
      /*  getAction(location.origin+"/statsCenter/mock/homeData.json").then((res) => {
        if(res){
          console.log('res.deviceAlarmData===',res.deviceAlarmData)
        }
        this.loading = false
      }).catch((err)=>{
        this.loading = false
      })*/
    },
    // 趋势分析折线图
    drawChart() {
      let h25 = heightPixel(25)
      let w2 = widthPixel(2)
      let w4 = widthPixel(4)
      let w5 = widthPixel(5)
      let w8 = widthPixel(8)
      let w9 = widthPixel(9)
      let w10 = widthPixel(10)
      let w14 = widthPixel(14)
      let w12 = widthPixel(12)
      let w15 = widthPixel(15)
      let w16 = widthPixel(16)
      let w20 = widthPixel(20)
      let w22 = widthPixel(22)
      let w32 = widthPixel(32)

      let seriesOption = [
        {
          name: '告警率',
          type: 'line',
          // symbol: 'circle',
          symbol: 'none',
          smooth: true,
          yAxisIndex: 0,
          lineStyle: {
            normal: {
              width: w2,
              color: '#2BECD9', // 线条颜色
            },
            borderColor: '#2BECD9',
          },
          itemStyle: {
            color: '#2BECD9',
            borderColor: '#2BECD9',
            borderWidth: w2,
          },
          areaStyle: {
            //区域填充样式
            normal: {
              //线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是‘true’，则该四个值是绝对像素位置。
              color: new echarts.graphic.LinearGradient(
                0, 0, 0, 1,
                [{
                  offset: 0,
                  color: 'rgba(43,236,217,0.4)',
                },
                  {
                    offset: 1,
                    color: '#01173C',
                  },
                ],
                false
              ),
              /* shadowColor: '#01173C', //阴影颜色
              shadowBlur: w20 //shadowBlur设图形阴影的模糊大小。配合shadowColor,shadowOffsetX/Y, 设置图形的阴影效果。*/
            },
          },
          data: this.yLineData,
        },
        {
          name: '告警数量',
          type: 'bar',
          zlevel: 1,
          yAxisIndex: 1,
          barWidth: w8,
          label: {
            normal: {
              show: false,
              position: 'right',
              // padding: [0, 2],
              textStyle: {
                fontSize: w12,
                color: '#9FA5AD'
              },
              formatter: '{c}' + ' ' + '个'
            }
          },
          labelLine: {
            show: false
          },
          data: this.yBarData,
          itemStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(
                0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: '#20DCF9' // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: '#01173C' // 100% 处的颜色
                  }
                ], false)
            }
          }
        },
      ]

      this.myChart = this.$echarts.init(this.$refs.AlarmSituationTrendChart)
      this.myChart.setOption(
        {
          grid: {
            top: w20,
            left: '10%',
            right: '10%',
            bottom: h25,
          },
          tooltip: {
            show: true,
            trigger: 'axis',
            transitionDuration: 0, //echart防止tooltip的抖动
            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: 'line', // 默认为直线，可选为：'line' | 'shadow'
              lineStyle: {
                type: 'dotted',
                color: '#ffffff'
              }
            },
            formatter: (value) => {
              console.log('value===',value)
              let tips = value[0].axisValue + "</br>"
              let dataTxt = ''
              for (let i = 0; i < value.length; i++) {
                let vTxt = ''
                if (value[i].value != undefined && value[i].value != null && value[i].value != '') {
                  vTxt = `${value[i].seriesName} ${value[i].value}${i == 1 ? '个' : '%'}`
                } else {
                  vTxt = `${value[i].seriesName} 无数据`
                }
                let txt = `<span style='display:inline-block;margin-right:${w5 + "px"};border-radius:${w10 + "px"};width:${w10 + "px"};height:${w10 + "px"};background-color:${i==1?"#20DCF9":"#2BECD9"}'></span>
                           <span style='text-align:left;font-size:${w14 + "px"}'>${vTxt}</span>`
                dataTxt += (i != value.length - 1) ? txt + '</br>' : txt
              }
              return tips + dataTxt
            }
          },
          legend: [
            {
              show: true,
              right: "24%",
              data: ['告警数量'],
              color: '#fff',
              itemWidth: w8,
              itemHeight: w8,
              padding:[0,0,0,w20],
              textStyle: {
                fontWeight: 300,
                fontSize: w10,
                color: '#fff',
              },
              itemStyle: {
                color: '#20DCF9',
              }
            },
            {
              show: true,
              right: w32,
              data: ['告警率'],
              color: '#fff',
              itemWidth: w16,
              itemHeight: w4,
              padding:[0,0,0,w20],
              textStyle: {
                fontWeight: 300,
                fontSize: w10,
                color: '#fff'
              },
              itemStyle: {
                color: '#2BEBD8',
              }
            }
            ],
          xAxis: [
            {
              type: 'category',
              boundaryGap: false,
              axisLine: {
                //坐标轴轴线相关设置。数学上的x轴
                show: false,
                lineStyle: {
                  color: '#1f313d',
                },
              },
              axisLabel: {
                show: true,
                textStyle: {
                  color: 'rgba(210,224,226,0.8)', //更改坐标轴文字颜色
                  fontSize: w10,
                  fontWeight: 300
                },
              },
              splitLine: {
                show: false,
              },
              axisTick: {
                show: false,
              },
              data: this.xData,
            }],
          yAxis: [
            {
              position: 'left',
              yAxisIndex: 0,
              offset: w15,
              /* nameTextStyle: {
                color: 'rgba(250,250,250,.6)',
                fontSize: w12,
                padding: w10,
              },*/
              // min: 0,
              // max: this.max,
              splitLine: {
                show: false,
                lineStyle: {
                  color: ['#1c2a37'],
                  width: w2,
                  type: 'Dashed',
                }
              },
              axisLine: {
                show: true,
                lineStyle: {
                  width: 1,
                  color: '#194D5A'
                }
              },
              axisLabel: {
                show: true,
                // margin: w15,
                textStyle: {
                  color: 'rgba(255,255,255,0.65)', //更改坐标轴文字颜色
                  fontSize: w10,
                  fontWeight: 400
                }
              },
              axisTick: {
                show: false
              }
            },
            {
              /* nameTextStyle: {
                color: 'rgba(250,250,250,.6)',
                fontSize: w12,
                padding: w10,
              },*/
              position: 'right',
              yAxisIndex: 1,
              offset: w15,
              splitLine: {
                show: false,
                lineStyle: {
                  color: ['#1c2a37'],
                  width: w2,
                  type: 'Dashed',
                }
              },
              axisLine: {
                show: true,
                lineStyle: {
                  width: 1,
                  color: '#194D5A'
                }
              },
              axisLabel: {
                show: true,
                // margin: w15,
                position: 'right',
                textStyle: {
                  color: 'rgba(255,255,255,0.65)', //更改坐标轴文字颜色
                  fontSize: w10,
                  fontWeight: 400
                }
              },
              axisTick: {
                show: true
              }
            }
          ],
          series: seriesOption,
        }
      )
      window.addEventListener("resize", this.alarmSituationTrendChartResize)
    },
    alarmSituationTrendChartResize(){
      this.myChart.resize();
    }
  }
}
</script>

<style scoped lang="less">
::v-deep .ant-empty-description{
  color:#fff !important;
}
</style>
