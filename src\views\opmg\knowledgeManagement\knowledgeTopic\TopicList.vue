<template>
  <a-row :gutter='10' style='height: 100%' class='vScroll'>
    <a-col style='width: 100%; height: 100%; display: flex; flex-direction: column'>
      <!-- 查询区域 -->
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <div class='table-page-search-wrapper'>
          <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
            <a-row :gutter='24' ref='row'>
              <a-col :span='spanValue'>
                <a-form-item label='主题名称'>
                  <a-input placeholder='请输入主题名称' v-model='queryParam.topicName' :allowClear='true'
                           autocomplete='off' :maxLength="maxLength"/>
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label='主题编码'>
                  <a-input placeholder='请输入主题编码' v-model='queryParam.topicCode' :allowClear='true'
                           autocomplete='off' :maxLength="maxLength"/>
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label='权限'>
                  <a-select v-model="queryParam.isPublic" placeholder="请选择主题权限" :allowClear='true'>
                    <a-select-option v-for="(item,index) in authorityOption" :key="'isPublic_'+index" :value="item.value">{{item.label}}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span='colBtnsSpan()'>
                <span class='table-page-search-submitButtons'
                      :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button class='btn-search btn-search-style' type='primary' @click='searchQuery'>查询</a-button>
                  <a-button class='btn-reset btn-reset-style' @click='searchReset'>重置</a-button>
                  <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <!-- 操作按钮区域 -->
        <div class='table-operator table-operator-style'>
          <a-button class='btn-add' @click='handleAddChild("root")'>新增</a-button>
          <a-dropdown v-if='selectedRowKeys.length > 0'>
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item key='1' @click='batchDel'>删除</a-menu-item>
            </a-menu>
            <a-button>批量操作
              <a-icon type='down' />
            </a-button>
          </a-dropdown>
        </div>

        <!-- table区域-begin -->
        <a-table
          ref="table"
          rowKey="id"
          bordered
          :columns="columns"
          :dataSource="dataSource"
          :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
          :pagination="ipagination"
          :loading="loading"
          :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange, getCheckboxProps: getCheckboxProps}"
          :expandedRowKeys="expandedRowKeys"
          @change="handleTableChange"
          @expand="handleExpand">

          <span slot="checkPermission" slot-scope="text, record">
          {{ record.topicName }}
        </span>
          <span class="caozuo" slot="action" slot-scope="text, record">
            <a @click="handleEdit(record)" :class='{"action-disable":!record.userWithPermission}'>编辑</a>
            <a-divider type="vertical" />
            <a @click="handleAddChild(record)" :class='{"action-disable":!record.userWithPermission}'>新增下级</a>
            <a-divider type="vertical" />

            <a-popconfirm title="确定删除吗?"   @confirm="() => handleDeleteTopic(record)" v-if='record.userWithPermission' placement="topLeft">
              <a :class='{"action-disable":!record.userWithPermission}'>删除</a>
            </a-popconfirm>
             <a v-else class='action-disable' @click="handleDeleteTopic(record)">删除</a>
          </span>
          <template slot="authority" slot-scope="text">
            {{text==1? "公开":"部分可见"}}
          </template>
          <template slot="tooltip" slot-scope="text">
            <a-tooltip placement="topLeft" :title="text" trigger="hover">
              <div class="tooltip">
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
      </a-card>
      <!-- table区域-end -->
      <topic-modal ref='modalForm' @ok='modalFormOk'></topic-modal>
    </a-col>
  </a-row>
</template>

<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
import topicModal from './modules/TopicModal'
import { deleteAction, getAction, putAction } from '@api/manage'
import { filterObj } from '@/utils/util'

export default {
  name: 'TopicList',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components: {
    topicModal,
  },
  data() {
    return {
      maxLength:50,
      description: '可用监控设置界面',
      formItemLayout: {
        labelCol: {
          style: 'width:80px'
        },
        wrapperCol: {
          style: 'width:calc(100% - 80px)'
        }
      },
      authorityOption: [
        { label: '公开', value: '1' },
        { label: '部分可见', value: '0' }],
      expandedRowKeys: [],
      hasChildrenField: 'isLeaf',
      pidField: 'parentId',
      columns: [
        {
          title: '主题名称',
          dataIndex: 'topicName',
          customCell: () => {
            let cellStyle = 'text-align: left'
            return {
              style: cellStyle
            }
          },
          scopedSlots: { customRender: 'checkPermission' }
        },
        {
          title: '主题编码',
          dataIndex: 'topicCode',
        },
        {
          title: '权限',
          dataIndex: 'isPublic',
          scopedSlots: {
            customRender: 'authority'
          },
          customCell: () => {
            let cellStyle = 'width: 100px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '描述',
          dataIndex: 'topicDescription',
          scopedSlots: {
            customRender: 'description'
          },
          customCell: () => {
            let cellStyle = 'text-align: left;min-width:100px;max-width:300px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '操作',
          align: 'center',
          width: 180,
          fixed: 'right',
          dataIndex: 'action',
          scopedSlots: {
            customRender: 'action'
          }
        }
      ],
      url: {
        getChildList:'/knowledges/topic/getChildList',
        getChildListBatch:'/knowledges/topic/getChildListBatch',
        delete: '/knowledges/topic/delete',
        deleteBatch: '/knowledges/topic/deleteBatch',
        searchTopic:'/knowledges/topic/searchTopic'
      },
    }
  },
  methods: {
    searchQuery() {
      if (this.triggerQuery()) {
        this.reloadData(1)
      }else {
        this.loadData(1)
      }
    },
    triggerQuery(){
      let keys=Object.keys(this.queryParam)
      let isTN=keys.includes('topicName')&&this.queryParam.topicName.trim().length>0
      let isTC=keys.includes('topicCode')&&this.queryParam.topicCode.trim().length>0
      let isP=keys.includes('isPublic')&&this.queryParam.isPublic
      return isTN||isTC||isP
    },
    searchReset() {
      //重置
      this.expandedRowKeys = []
      this.queryParam = {}
      this.loadData(1)
    },
    reloadData(arg){
      if (arg == 1) {
        this.ipagination.current = 1
      }
      this.loading = true
      let params = this.getQueryParams()
      params.hasQuery = 'true'
      getAction(this.url.searchTopic, params).then((res) => {
        if (res.success) {
          let result = res.result
          if (Number(result.total) > 0) {
            this.ipagination.total = Number(result.total)
            this.dataSource = res.result.records
            let currentIndex = Math.ceil(result.total / this.ipagination.pageSize)
            //删除后的分页数<所在当前页
            if (currentIndex < this.ipagination.current) {
              this.ipagination.current = currentIndex
              this.reloadData()
            }
          } else {
            this.ipagination.current=1
            this.ipagination.total = 0
            this.dataSource = []
          }
          this.loading = false
        } else {
          this.$message.warning(res.message)
          this.loading = false
        }
      }).catch((err)=>{
        this.$message.warning(err.message)
        this.loading = false
      })
    },
    getCheckboxProps (record) {
      return ({
        props: {
          disabled: !record.userWithPermission,
          topicName: record.topicName
        }
      })
    },
    loadData(arg) {
      if (arg == 1) {
        this.ipagination.current = 1
      }
      this.loading = true
      let params = this.getQueryParams()
      params.hasQuery = 'true'

      getAction(this.url.getChildList, params).then((res) => {
        if (res.success) {
          let result = res.result
          if (Number(result.total) > 0) {
            this.ipagination.total = Number(result.total)
            this.dataSource = this.getDataByResult(res.result.records)
            this.loadDataByExpandedRows(this.dataSource)
            let currentIndex = Math.ceil(result.total / this.ipagination.pageSize)
            //删除后的分页数<所在当前页
            if (currentIndex < this.ipagination.current) {
              this.ipagination.current = currentIndex
              this.loadData()
            }
          } else {
            this.ipagination.total = 0
            this.dataSource = []
          }
          this.loading = false
        } else {
          this.$message.warning(res.message)
          this.loading = false
        }
      }).catch((err)=>{
        this.$message.warning(err.message)
        this.loading = false
      })
    },
    // 根据已展开的行查询数据（用于保存后刷新时异步加载子级的数据）
    loadDataByExpandedRows(dataList) {
      if (this.expandedRowKeys.length > 0) {
        return getAction(this.url.getChildListBatch, { parentIds: this.expandedRowKeys.join(',') }).then((res) => {
          if (res.success) {
            // if (res.result.length > 0) {
            //已展开的数据批量子节点
            let records = res.result
            const listMap = new Map()
            for (let item of records) {
              let pid = item[this.pidField]
              if (this.expandedRowKeys.join(',').includes(pid)) {
                let mapList = listMap.get(pid)
                if (mapList == null) {
                  mapList = []
                }
                mapList.push(item)
                listMap.set(pid, mapList)
              }
            }
            let childrenMap = listMap
            let fn = (list) => {
              if (list) {
                list.forEach((data) => {
                  if (this.expandedRowKeys.includes(data.id)) {
                    data.children = this.getDataByResult(childrenMap.get(data.id))
                    fn(data.children)
                  }
                })
              }
            }
            fn(dataList)
            // }
          }else {
            this.$message.warning(res.message)
          }
          this.loading = false
        })
      } else {
        this.loading = false
      }
    },
    getQueryParams(arg) {
      //获取查询条件
      let sqp = {}
      let param = {}
      if (this.superQueryParams) {
        sqp['superQueryParams'] = encodeURI(this.superQueryParams)
        sqp['superQueryMatchType'] = this.superQueryMatchType
      }
      if (arg) {
        param = Object.assign(sqp, this.isorter, this.filters)
      } else {
        param = Object.assign(sqp, this.queryParam, this.isorter, this.filters)
      }
      if (JSON.stringify(this.queryParam) === '{}' || arg) {
        param.hasQuery = 'false'
      } else {
        param.hasQuery = 'true'
      }
      param.field = this.getQueryField()
      param.pageNo = this.ipagination.current
      param.pageSize = this.ipagination.pageSize
      return filterObj(param)
    },
    handleTableChange(pagination, filters, sorter) {
      //分页、排序、筛选变化时触发
      //TODO 筛选
      if (Object.keys(sorter).length > 0) {
        this.isorter.column = sorter.field
        this.isorter.order = 'ascend' == sorter.order ? 'asc' : 'desc'
      }
      this.ipagination = pagination
      if (this.triggerQuery()){
        this.reloadData()
      }else {
        this.loadData()
      }
    },
    getDataByResult(result) {
      if (result) {
        return result.map((item) => {
          //判断是否标记了带有子节点
          if (item[this.hasChildrenField] == '0') {
            let loadChild = { id: item.id + '_loadChild', name: 'loading...', isLoading: true }
            item.children = [loadChild]
          }
          return item
        })
      }
    },
    /*    removeEmptyChildren(result){
          if (result) {
            return result.map((item) => {
              //判断是否标记了带有子节点
              if (item.name==="loading...") {
                item.children = []
              }
              return item
            })
          }
        },*/
    handleExpand(expanded, record) {
      // 判断是否是展开状态
      if (expanded) {
        this.expandedRowKeys.push(record.id)
        if (record.children.length > 0 && record.children[0].isLoading === true) {
          let params = this.getQueryParams(1) //查询条件
          // params[this.pidField] = record.id
          params.id= record.id
          params.hasQuery = 'false'
          params.superQueryParams = ''
          getAction(this.url.getChildList, params).then((res) => {
            if (res.success) {
              if (res.result) {
                record.children = this.getDataByResult(res.result)
                this.dataSource = [...this.dataSource]
              } else {
                record.children = ''
                record.isLeaf = '1'
              }
            } else {
              this.$message.warning(res.message)
            }
          })
        }
      } else {
        let keyIndex = this.expandedRowKeys.indexOf(record.id)
        if (keyIndex >= 0) {
          this.expandedRowKeys.splice(keyIndex, 1)
        }
      }
    },

    handleEdit: function (record) {
      if (!record.userWithPermission){
        this.$message.info('无权限操作')
        return
      }
      this.$refs.modalForm.edit(record);
      this.$refs.modalForm.title = '编辑';
      this.$refs.modalForm.disableSubmit = false;
    },

    handleAddChild(record) {
      if (record!=="root"&&!record.userWithPermission){
        this.$message.info('无权限操作')
        return
      }
      this.$refs.modalForm.add(record.id)
      this.$refs.modalForm.title = '新增';
      this.$refs.modalForm.disableSubmit = false;
    },

    /*  handleDeleteNode(id) {
        if (!this.url.delete) {
          this.$message.error('请设置url.delete属性!')
          return
        }
        var that = this
        deleteAction(that.url.delete, { id: id }).then((res) => {
          if (res.success) {
            that.searchReset()
          } else {
            that.$message.warning(res.message)
          }
        })
      },*/

    handleDeleteTopic(record){
      if (!record.userWithPermission){
        this.$message.info('无权限操作')
        return
      }
      this.handleDelete(record.id)
    },
    handleDelete: function (id) {
      if (!this.url.delete) {
        this.$message.error('请设置url.delete属性!')
        return
      }
      var that = this
      deleteAction(that.url.delete, { id: id }).then((res) => {
        if (res.success) {
          //重新计算分页问题
          that.reCalculatePage(1)
          that.$message.success(res.message)
          if (that.triggerQuery()){
            that.reloadData()
          }else {
            that.loadData()
          }
        } else {
          that.$message.warning(res.message)
          that.loading=false
        }
      }).catch((err)=>{
        that.$message.warning(err.message)
        that.loading=false
      })
    },
    batchDel: function () {
      if (!this.url.deleteBatch) {
        this.$message.error('请设置url.deleteBatch属性!')
        return
      }
      if (this.selectedRowKeys.length <= 0) {
        this.$message.warning('请选择一条记录！')
        return
      } else {
        var ids = ''
        for (var a = 0; a < this.selectedRowKeys.length; a++) {
          ids += this.selectedRowKeys[a] + ','
        }
        var that = this
        this.$confirm({
          title: '确认删除',
          okText: '是',
          cancelText: '否',
          content: '是否删除选中数据?',
          onOk: function () {
            that.loading = true
            deleteAction(that.url.deleteBatch, { ids: ids })
              .then((res) => {
                if (res.success) {
                  //重新计算分页问题
                  that.reCalculatePage(that.selectedRowKeys.length)
                  that.$message.success(res.message)
                  if (that.triggerQuery()){
                    that.reloadData()
                  }else {
                    that.loadData()
                  }
                  that.onClearSelected()
                } else {
                  that.$message.warning(res.message)
                  that.loading=false
                }
              }).catch((err)=>{
                that.$message.error(err.message)
                that.loading = false
            })
          }
        })
      }
    },
    modalFormOk(){
      if (this.triggerQuery()){
        this.reloadData(1)
      }else {
        this.loadData(1)
      }
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
.caozuo .action-disable{
  color: rgba(0, 0, 0, 0.25) !important;
}
</style>