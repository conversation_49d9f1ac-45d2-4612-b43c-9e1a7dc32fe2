<template>
  <div>
    <!--按字段名称过滤字段-->
    <a-input-search placeholder='搜索字段名称' v-model='searchField' style='width: 100%;padding: 0 16px'
                    @search='changeSearchedField' />
    <!--按字段类型过滤字段-->
    <a-select
      v-model='searchType'
      style='width:calc(100% - 32px);margin: 12px 16px 0'
      show-search
      :allow-clear='true'
      placeholder='按类型筛选'
      option-filter-prop='children'
      :optionLabelProp='"label"'
      @change='changeSearchedType'
    >
      <a-select-option v-for='(item,index) in searchTypes' :key='"searchTypes_"+index' :value='item' :label='item'>
       {{item}}
      </a-select-option>
    </a-select>
    <!--呈现字段：选定字段、可用字段-->
    <a-spin :spinning='loading'>
      <a-collapse :defaultActiveKey="['1','2']">
        <a-collapse-panel key='1' :show-arrow='true' :header="'选定字段('+selectedFields.length+')'">
          <div v-if='selectedFields.length===0' class='empty'>暂无数据</div>
          <div v-else>
            <div v-for='(item,index) in selectedFields' :key='"selectedFields_"+index' class='field-wrapper'>
              <div class='field' :title='"字段名:"+item.field+"，类型："+item.type'>
                <span v-if="item.title">{{ item.title }} ({{ item.field }})</span>
                <span v-else>{{ item.field }}</span>
              </div>
              <div class='action' @click='deleteField(item,index)'>
                <a-icon type='close-circle' />
              </div>
            </div>
          </div>
        </a-collapse-panel>
        <a-collapse-panel key='2' :show-arrow='true' :header="'可用字段('+availableFields.length+')'">
          <div v-if='availableFields.length===0' class='empty'>暂无数据</div>
          <div v-else>
            <div v-for='(item,index) in availableFields' :key='"availableFields_"+index' class='field-wrapper'>
              <div class='field' :title='"字段名:"+item.field+"，类型："+item.type'>
                <span v-if="item.title">{{ item.title }} ({{ item.field }})</span>
                <span v-else>{{ item.field }}</span>
              </div>
              <div class='action' @click='addField(item,index)'>
                <a-icon type='plus-circle' />
              </div>
            </div>
          </div>
        </a-collapse-panel>
      </a-collapse>
    </a-spin>
  </div>
</template>
<script>
import { getAction } from '@api/manage'
export default {
  name: 'logFields',
  props: {
    logAnalyzeId:{
      type: String,
      required: false,
      default: ''
    },
    fieldList:{
      type: Array,
      required: false,
      default: ()=>{return []}
    },
    loading:{
      type: Boolean,
      required: false,
      default:false
    },
    timestamp:{
      type: String,
      required: false,
      default: ''
    }
  },
  data() {
    return {
      searchField: '',
      searchType: undefined,
      searchTypes: [],

      selectedFields: [],
      oldSelectedFields: [],

      availableFields: [],
      oldAvailableFields: [],
      url:{
        feilds: '/logAnalyze/getFields'//加载字段数据接口
      }
    }
  },
  watch: {
    fieldList: {
      handler(nval, oval) {
        this.searchField = ''
        this.searchType = undefined
        this.searchTypes = []
        this.oldSelectedFields = []
        this.oldAvailableFields = []
        this.selectedFields = []
        this.availableFields = []
        let tempFields=nval
        if (this.timestamp&&this.timestamp.length>0&&nval.length>0){
          tempFields=nval.filter((item)=>{
            //console.log(item.field!==this.timestamp)
            return item.field!==this.timestamp
          })
        }
        this.initTypes(tempFields)
        this.initFields(tempFields)
      }
    }
  },
  methods: {
    /*初始化可选字段和可用字段*/
    initFields(list) {
      for (let i = 0; i < list.length; i++) {
        let obj = {
          field: list[i].field,
          title: list[i].title,
          type: list[i].type,
          oldIndex: i
        }
        this.oldAvailableFields.push(obj)
      }
      this.resetFields(this.oldAvailableFields, this.availableFields)
      this.resetFields(this.oldSelectedFields, this.selectedFields)
    },
    initTypes(list){
      for (let i = 0; i < list.length; i++) {
        if (!this.searchTypes.includes(list[i].type)){
          this.searchTypes.push(list[i].type)
        }
      }
    },
    /*根据搜索内容，过滤选定字段和可选字段*/
    changeSearchedField(value) {
      this.availableFields = []
      this.selectedFields = []
      if (value.length>0) {
        this.selectedFields =this.filterOldFieldArray(this.oldSelectedFields,value)
        this.availableFields =this.filterOldFieldArray(this.oldAvailableFields,value)
      } else {
        this.resetFields(this.oldAvailableFields, this.availableFields)
        this.resetFields(this.oldSelectedFields, this.selectedFields)
      }
    },
    filterOldFieldArray(oldArray,field){
      let tempField=field.toLowerCase()
      let arr=[]
      if (oldArray.length>0){
         arr= oldArray.filter((item) => {
           let tempItemField=item.field.toLowerCase()
           let index=tempItemField.indexOf(tempField)
          if (this.searchType){
            return index !== -1&&item.type===this.searchType
          }else {
            return index !== -1
          }
        })
      }
      return arr
    },
    /*重置可选字段和可用字段*/
    resetFields(oldArray, curArray) {
      for (let i = 0; i < oldArray.length; i++) {
        if (this.searchType){
          if(oldArray[i].type===this.searchType){
           let obj = {
              field: oldArray[i].field,
              title: oldArray[i].title,
              type: oldArray[i].type,
              oldIndex: oldArray[i].oldIndex
            }
            curArray.push(obj)
          }
        }else{
          let obj = {
            field: oldArray[i].field,
            title: oldArray[i].title,
            type: oldArray[i].type,
            oldIndex: oldArray[i].oldIndex
          }
          curArray.push(obj)
        }
      }
    },
    /*根据类型，过滤搜索选定字段和可选字段*/
    changeSearchedType(value){
      this.changeSearchedField(this.searchField)
    },
    /*将选定字段变为可选字段*/
    deleteField(item, index) {
      let obj = {
        field: item.field,
        title: item.title,
        type: item.type,
        oldIndex: item.oldIndex
      }
      this.oldAvailableFields.splice(item.oldIndex, 0, obj)
      this.oldSelectedFields = this.oldSelectedFields.filter((m) => {
        return m.oldIndex !== item.oldIndex
      })
      //按照初始索引排序
      this.oldAvailableFields = this.oldAvailableFields.sort((a, b) => {
        return a.oldIndex - b.oldIndex
      })
      this.changeSearchedField(this.searchField)
      this.$emit('deleteField', item.field)
    },
    /*将可用字段变为选定字段*/
    addField(item, index) {
      let obj = {
        field: item.field,
        title: item.title,
        type: item.type,
        oldIndex: item.oldIndex
      }
      this.oldSelectedFields.push(obj)
      this.oldAvailableFields = this.oldAvailableFields.filter((m) => {
        return m.oldIndex !== item.oldIndex
      })
      this.changeSearchedField(this.searchField)
      this.$emit('addField', item.field)
    }
  }
}
</script>

<style scoped lang='less'>
::v-deep .ant-input-affix-wrapper .ant-input-suffix {
  right: 25px
}

::v-deep .ant-spin-nested-loading {
  width: 100%;
  margin-top: 12px;
  height: calc(100% - 24px - 32px - 32px);

  .ant-spin-container {
    height: 100%;
  }
}

::v-deep .ant-collapse {
  background-color: #fff;
  border: 0px solid transparent;
  height: 100%;
  margin-right: 1px;
  overflow: hidden;
  overflow-y: auto;

  .ant-collapse-item {
    border-bottom: 0px solid transparent;

    .ant-collapse-header {
      padding: 4px 16px 0px;

      .ant-collapse-arrow {
        position: relative;
        margin-right: 6px;
        left: 0px;
        transform: translateY(0%);
      }
    }

    .ant-collapse-content {
      border-top: 0px solid transparent;

      .ant-collapse-content-box {
        padding: 0px 12px 0px 30px;
      }
    }
  }
}

.empty {
  text-align: center;
  color: rgba(0, 0, 0, 0.35)
}

.field-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-flow: row nowrap;
  overflow: hidden;
  border-radius: 2px;
  padding: 0 4px;

  .field {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    margin-right: 16px;
  }

  .action {
    display: none;
    color: rgba(0, 0, 0, 0.65);
  }
}

.field-wrapper:hover {
  background-color: #ecf5ff;

  .action {
    display: block;
    color: rgba(0, 0, 0, 0.65);
    cursor: pointer;
  }

  .action:hover {
    color: #409eff
  }
}
</style>