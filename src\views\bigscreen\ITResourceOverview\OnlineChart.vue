<template>
  <!-- 设备在线情况 -->
  <div>
    <div slot="bodySlot" class="empty-wrapper" v-if="chartData.length === 0">
      <a-spin :spinning="loading" v-if="loading" class="spin"></a-spin>
      <a-list :data-source="[]" v-else />
    </div>
    <div v-else ref="baseEcharts" style="height: 100%; width: 100%"></div>
  </div>
</template>

<script>
import { getAction } from '@/api/manage'
export default {
  name: 'OnlineChart',
  props: {
    fontSizeObject: {
      type: Object,
      default: function () {
        return {
          legendFontSize: 12,
          xAxisFontSize: 12,
          yAxisFontSize: 12,
        }
      },
    },
  },
  data() {
    return {
      myChart: null,
      selectedIndex: 0,
      loading: false,
      chartData: [],
      url: {
        deviceUsageSituation: '/openAPI/deviceUsageSituation',
      },
    }
  },
  created() {
    this.deviceUsageSituation()
  },
  methods: {
    deviceUsageSituation() {
      this.loading = true
      getAction(this.url.deviceUsageSituation)
        .then((res) => {
          if (res.code == 200) {
            if (res.result) {
              this.chartData = res.result
              this.$nextTick(() => {
                this.initData(this.chartData)
              })
            }
          }
          this.loading = false
        })
        .catch((err) => {
          this.$message.warning(err.message)
          this.loading = false
        })
    },
    initData(data) {
      // x轴数据
      let xData = data.time.map(item => item.slice(5, 10)) || []
      // 柱状图数据
      let barData = data.onlineCount || []
      //折线图数据
      let lineData = data.onlineRate || []
      // 完整日期数据保留用于tooltip
      let fullDateData = data.time || []
      // 初始化图表
      let myChart = this.$echarts.init(this.$refs.baseEcharts)

      let color = ['rgba(6,55,107,0.6)', '#89F7FE']
      let option = {
        color: color,
        grid: {
          top: '30%',
          left: '6%',
          right: '6%',
          bottom: 0,
          containLabel: true,
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
          formatter: function (params) {
            let html = `${fullDateData[params[0].dataIndex]}<br/>`
            let unit = ''
            params.map((item, i) => {
              if (item.seriesIndex == 0) {
                unit = ''
              }
              if (item.seriesIndex == 1) {
                unit = '%'
              }
              html += `${item.marker}${item.seriesName} ${item.value || 0} ${unit} <br/>`
            })
            return html
          },
        },
        legend: {
          top: '10%',
          right: '15px',
          itemWidth: 8,
          itemHeight: 8,
          textStyle: {
            textalign: 'right',
            color: 'rgba(255, 255, 255, 0.8)',
            fontSize: this.fontSizeObject.legendFontSize,
          },
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: true,
            axisLine: {
              show: true,
              lineStyle: {
                color: 'rgba(235, 241, 249, 0.2)',
              },
            },
            axisLabel: {
              textStyle: {
                color: 'rgba(255, 255, 255, 0.8)',
                fontSize: this.fontSizeObject.xAxisFontSize,
              },
            },
            axisTick: {
              show: false,
            },
            data: xData,
          },
        ],
        yAxis: [
          {
            type: 'value',
            axisTick: {
              show: false,
            },
            splitLine: {
              show: true,
              lineStyle: {
                type: 'solid',
                color: 'rgba(235, 241, 249, 0.2)',
                width: 1,
              },
            },
            axisLine: {
              show: false,
            },
            axisLabel: {
              formatter: '{value}',
              textStyle: {
                color: 'rgba(255, 255, 255, 0.8)',
                fontSize: this.fontSizeObject.yAxisFontSize,
              },
            },
          },
          {
            type: 'value',
            // name:'单位: 次',
            nameTextStyle: {
              color: 'rgba(255, 255, 255, 0.8)',
              fontSize: this.fontSizeObject.yAxisFontSize,
            },
            axisTick: {
              show: false,
            },
            position: 'right',
            splitLine: {
              show: false,
            },
            axisLine: {
              show: false,
            },
            axisLabel: {
              formatter: '{value}%',
              textStyle: {
                color: 'rgba(255, 255, 255, 0.8)',
                fontSize: this.fontSizeObject.yAxisFontSize,
              },
            },
          },
        ],
        dataZoom: [
          {
            xAxisIndex: [0],
            show: false, //是否显示滑动条，不影响使用
            start: 0, // 从头开始。
            endValue: 30,
            realtime: true, //是否实时更新
          },
          {
            type: 'inside',
            xAxisIndex: 0,
            zoomOnMouseWheel: true, //滚轮是否触发缩放
            moveOnMouseMove: true, //鼠标滚轮触发滚动
            moveOnMouseWheel: true,
          },
        ],
        series: [
          {
            type: 'bar',
            name: '在线数量',
            legendIcon: 'rect', // 添加矩形图标类型
            barGap: 0.2,
            barWidth: 15,
            itemStyle: {
              normal: {
                borderColor: '#1158A2',
                borderWidth: 1,
              },
            },
            label: {
              show: false,
              position: 'inside',
              color: 'rgba(255, 255, 255, 0.8)',
              fontSize: 10,
            },
            data: barData,
          },
          {
            type: 'line',
            name: '在线率',
            yAxisIndex: 1,
            smooth: true,
            data: lineData,
            symbol: 'circle',
            showAllSymbol: true,
            emphasis: {
              focus: 'series', //高亮显示
            },
            symbolSize: 6,
            lineStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 1,
                y2: 0,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(137, 247, 254, 1)', // 0% 处的颜色
                  },
                  {
                    offset: 0.5,
                    color: 'rgba(54, 115, 254, 1)', // 50% 处的颜色
                  },
                  {
                    offset: 1,
                    color: 'rgba(137, 247, 254, 1)', // 100% 处的颜色
                  },
                ],
                globalCoord: false, // 缺省为 false
              },
            },
            itemStyle: {
              // 折线拐点标志的样式
              normal: {
                // symbolSize: [16, 6], // 折线图例尺寸[宽,高]
                color: `${color[1]}`,
                // borderColor: 'rgba(99, 255, 255, 1)',
                borderWidth: 2,
              },
            },
          },
        ],
      }
      myChart.setOption(option)
      window.addEventListener('resize', () => {
       myChart.resize()
      })
    },
  },
}
</script>
<style lang="less" scoped>
.empty-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
::v-deep {
  .ant-empty-description {
    color: #fff !important;
  }
}
</style>
