<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="disableSubmit">
      <a-form-model  slot="detail"  ref='form' :model="model" :rules='validatorRules' v-bind='formItemLayout'>
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="策略名称" props="networkSegment">
              <a-input v-model="model.networkSegment" :allowClear="true" autocomplete="off"
                placeholder="请输入策略名称"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="生效范围" props="effectiveType" >
              <a-radio-group v-model="model.effectiveType">
                <a-radio value='0'>全部</a-radio>
                <a-radio value='1'>部分</a-radio>
              </a-radio-group>
            </a-form-model-item>
          </a-col>
          <a-col :span="24" v-if="model.effectiveType==1">
            <a-form-model-item  ref='departments' class="two-words" label="部门" prop='departments'>
              <j-select-depart v-model='model.departments' :multi='true'
                               @blur="() => {$refs.departments.onFieldBlur()}"
              ></j-select-depart>
            </a-form-model-item>
          </a-col>
          <a-col :span="24" v-if="model.effectiveType==1">
            <a-form-model-item ref='users' class="two-words" label="人员" prop='users'>
              <j-select-user-by-dep v-model='model.users' btnName="人员" :multi='true'  @blur="() => {$refs.users.onFieldBlur()}">
              </j-select-user-by-dep>
            </a-form-model-item>
          </a-col>
          <a-col :span="24" v-if="model.effectiveType==1">
            <a-form-model-item class="two-words" label="终端" ref="terminals">
              <j-select-terminal-by-dep v-model='model.terminals' :multi='true'  @blur="() => {$refs.terminals.onFieldBlur()}">
              </j-select-terminal-by-dep>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="是否生效" >
              <a-radio-group v-model="model.isEffective">
                <a-radio value='1'>是</a-radio>
                <a-radio value='0'>否</a-radio>
              </a-radio-group>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>
  import {
    httpAction,
    getAction
  } from '@/api/manage'
  import JFormContainer from '@/components/jeecg/JFormContainer'
  import JDate from '@/components/jeecg/JDate'
  import JSelectUserByDep from '@comp/jeecgbiz/JSelectUserByDep.vue'
  import JSelectTerminalByDep from '@comp/jeecgbiz/JSelectTerminalByDep.vue'
  export default {
    name: 'strategyForm',
    components: {
      JSelectUserByDep,
      JFormContainer,
      JDate,
      JSelectTerminalByDep
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false,
      },
    },
    data() {
      return {
        model: {
          effectiveType:0,
          isEffective:0
        },
        disableSubmit: false,
        confirmLoading: false,
        formItemLayout: {
          labelCol: {
            xs: { span: 24 },
            sm: { span: 24 },
            md: { span: 5 }
          },
          wrapperCol: {
            xs: { span: 24 },
            sm: { span: 24 },
            md: { span: 16 }
          }
        },
        validatorRules: {
          topicName: [
            { required: true, min: 2, max: 20, validator: this.validateTopicNameAndCode }],
          topicCode: [
            { required: true, min: 2, max: 50, validator: this.validateTopicNameAndCode }],
          roles: [
            { required: false, validator: this.customValidate, trigger: 'change' }
          ],
          users: [
            { required: false,  validator: this.customValidate, trigger: 'change' }
          ],
          departments: [
            { required: false,  validator: this.customValidate, trigger: 'change'}
          ],
          topicSerial: [
            { required: false,  message: '请输入序号', trigger: 'change'}
          ],
          topicDescription: [
            { required: false, min: 0, max: 200, message: '描述信息长度应0-200之间', trigger: 'blur' }
          ]
        },
        url: {
          add: '/software/devopsSoftwareInfo/add',
          edit: '/software/devopsSoftwareInfo/edit',
          queryById: '/software/devopsSoftwareInfo/queryById',
        },
      }
    },
    created() {
      //如果是流程中表单，则需要加载流程表单data
      this.showFlowData()
    },
    methods: {
      add() {
        this.edit({})
      },
      edit(record) {
        this.model = Object.assign({}, record)
        this.visible = true
        this.$nextTick(() => {
        })
      },
      submitForm() {
        const that = this
        // 触发表单验证
        this.refs.form.validate((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.model.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'put'
            }
            let formData = Object.assign(this.model, values)
            httpAction(httpurl, formData, method)
              .then((res) => {
                if (res.success) {
                  that.$message.success(res.message)
                  that.$emit('ok')
                } else {
                  that.$message.warning(res.message)
                }
              })
              .finally(() => {
                that.confirmLoading = false
              })
          }
        })
      }
    },
  }
</script>
<style lang="less" scoped>
  ::v-deep .two-words>div>label {
    letter-spacing: 4px;
  }

  ::v-deep .two-words>div>label::after {
    letter-spacing: 0px;
  }
</style>