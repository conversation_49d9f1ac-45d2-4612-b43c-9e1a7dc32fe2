<template>
  <a-tabs :animated="false" defaultActiveKey="1">
    <a-tab-pane tab="背景" key="1">
      <a-row align="middle">
        <a-col :span="6">背景类型</a-col>
        <a-col :span="16">
          <a-radio-group name="radioGroup" :value="globalGridAttr.bgType" @change="radioChange">
            <a-radio :value="'1'">
              调色板
            </a-radio>
            <a-radio :value="'2'">
              图片
            </a-radio>
          </a-radio-group>
        </a-col>
      </a-row>
      <a-row align="middle" v-if="globalGridAttr.bgType === '1'">
        <a-col :span="6">背景颜色</a-col>
        <a-col :span="14">
          <a-input type="color" :value="globalGridAttr.bgColor" style="width: 100%" @change="(e) => { globalGridAttr.bgColor = e.target.value }" />
        </a-col>
      </a-row>
      <a-row align="middle" v-if="globalGridAttr.bgType === '2'">
        <a-col :span="6">背景图</a-col>
        <a-col :span="14">
          <div style="width:150px; height:80px;margin-bottom:5px" v-if="globalGridAttr.bgimg">
            <img class="bg-img" :src="globalGridAttr.bgimg" />
          </div>
        </a-col>
      </a-row>
      <a-row align="middle" v-if="globalGridAttr.bgType === '2'">
        <a-col :span="6">图片列表</a-col>
      </a-row>
      <div
        v-if="globalGridAttr.bgType === '2'"
        :class="{height144:!globalGridAttr.bgimg, height96:globalGridAttr.bgimg}"
      >
        <topo-image-upload isMultiple @change="changeImgList" @changeImg="changeImg" :value="globalGridAttr.bgImgList"></topo-image-upload>
      </div>
    </a-tab-pane>
  </a-tabs>
</template>

<script>
import { gridOpt, backGroundOpt, gridSizeOpt, fontColorOpt } from './method'
import imgData from '@/imagejson/imgjson.json'
import FlowGraph from '../../../graph'
import TopoImageUpload from '@/components/topo/TopoImageUpload'

const GRID_TYPE_ENUM = {
  DOT: 'dot',
  FIXED_DOT: 'fixedDot',
  MESH: 'mesh',
  DOUBLE_MESH: 'doubleMesh'
}
const REPEAT_TYPE_ENUM = {
  NO_REPEAT: 'no-repeat',
  REPEAT: 'repeat',
  REPEAT_X: 'repeat-x',
  REPEAT_Y: 'repeat-y',
  ROUND: 'round',
  SPACE: 'space',
  FLIPX: 'flipX',
  FLIPY: 'flipY',
  FLIPXY: 'flipXY',
  WATERMARK: 'watermark'
}
export default {
  name: 'Index',
  components: {
    TopoImageUpload
  },
  props: {
    globalGridAttr: {
      type: Object,
      default: null,
      required: true
    }
  },
  data() {
    return {
      imgData: imgData,
      GRID_TYPE: GRID_TYPE_ENUM,
      REPEAT_TYPE: REPEAT_TYPE_ENUM
    }
  },
  mounted() {
    gridOpt(this.globalGridAttr)
    gridSizeOpt(this.globalGridAttr)
    backGroundOpt(this.globalGridAttr)
    // fontColorOpt(this.globalGridAttr)
  },
  computed: {
    gridOptCpt() {
      return {
        type: this.globalGridAttr.type,
        color: this.globalGridAttr.color,
        thickness: this.globalGridAttr.thickness,
        thicknessSecond: this.globalGridAttr.thicknessSecond,
        colorSecond: this.globalGridAttr.colorSecond,
        factor: this.globalGridAttr.factor
      }
    },
    gridSizeOptCpt() {
      return {
        size: this.globalGridAttr.size
      }
    },
    backGroundOpt() {
      return {
        bgColor: this.globalGridAttr.bgColor,
        showImage: this.globalGridAttr.showImage,
        bgimg: this.globalGridAttr.bgimg,
        repeat: this.globalGridAttr.repeat,
        angle: this.globalGridAttr.angle,
        bgSize: this.globalGridAttr.bgSize,
        position: this.globalGridAttr.position,
        opacity: this.globalGridAttr.opacity
      }
    }
  },
  watch: {
    // 监听网格变化
    gridOptCpt: {
      handler(nv) {
        gridOpt(nv)
      },
      immediate: false,
      deep: false
    },
    // 监听网格大小变化
    gridSizeOptCpt: {
      handler(nv) {
        gridSizeOpt(nv)
      },
      immediate: false,
      deep: false
    },
    // 监听背景变化
    backGroundOpt: {
      handler(nv) {
        backGroundOpt(nv)
      },
      immediate: false,
      deep: true
    }
  },
  methods: {
    changeImgList(path,status){
      if(!!path){
        this.globalGridAttr.bgImgList = path.split(",")
        let imgListLength = this.globalGridAttr.bgImgList.length
        let imgUrl = this.globalGridAttr.bgImgList[imgListLength-1]
        this.globalGridAttr.bgimg = imgUrl.includes('http') ? imgUrl : window._CONFIG['staticDomainURL'] + '/' + imgUrl
      }else{
        this.globalGridAttr.bgImgList = []
        this.globalGridAttr.bgimg = ""
        const { graph } = FlowGraph
        graph.updateBackground({color:'e5e5e5'})
      }
    },
    changeImg(fileUrl){
      if(fileUrl.startsWith("http://") ||fileUrl.startsWith("https:''") ){
        this.globalGridAttr.bgimg = fileUrl
      }else{
        this.globalGridAttr.bgimg = window._CONFIG['staticDomainURL'] + '/' + fileUrl
      }
    },
    radioChange(e){
      let val = e.target.value
      if(val === '1'){
        this.globalGridAttr.showImage = false
      }else{
        this.globalGridAttr.showImage = true
      }
      this.globalGridAttr.bgType = val
    }
  }
}
</script>

<style lang="less" scoped>
.bg-img {
  width: 100%;
  height: 100%;
  cursor: pointer;
}
.height144 {
  height: calc(100% - 128px);
  overflow-y: auto;
}
.height96 {
  height: calc(100% - 181px);
  overflow-y: auto;
}
.ant-tabs .ant-tabs-top-content > .ant-tabs-tabpane, .ant-tabs .ant-tabs-bottom-content > .ant-tabs-tabpane{
  height:100% !important;
}
::v-deep .ant-upload-list-picture-card-container{
  width: 100% !important;
}
::v-deep .ant-upload-list-picture-card .ant-upload-list-item{
  width: 100% !important;
}
::v-deep .ant-upload.ant-upload-select-picture-card{
  width: 100% !important;
}
</style>
