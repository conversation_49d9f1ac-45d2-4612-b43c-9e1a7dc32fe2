window.config = {
  /*
 sysType取值意义
 0:存在模块入口首页，
 1:无模块入口首页，直接打开数据中心，默认显示菜单一功能页面, 遵义项目 为 "1"时必须为在用户管理中为用户设定上级身份.
 2:无模块入库首页，通过设置数据字典子系统数据数据顺序确定登录首入排序第一的子系统
 */
  sysType: '0',
  platformType: '系统配置', // sysType为1时，进入管理页面的名称
  // apiUrl: "http://localhost:8091/insight-api",
  apiUrl:"http://*************/insight-api",
  // apiUrl:"http://*************:8091/insight-api",
  // apiUrl:"http://*************:8091/insight-api",
  //casUrl:"http://*************:8443/cas",
  // casSSo: 'true',
  //网页标题配置
  titleConfig:
    {
      title: "远桥运维监控平台",
      icon:'configImg/titleIcon1.png',//山东远桥logo
      // icon: 'configImg/logo-white2.png',//北京远桥logo
    },
  //行政区划根节点id（当前为：毕节市【522400】呼和浩特 150100）
  // rootAreaId: '520000',
  rootAreaId: '520300',

  //登录页图标 UserLayout.vue
  login: {
    logoName: "远桥运维监控平台",
    loginDesc: "科技创新&nbsp;自主可控",
    loginUrl:"/configImg/logo.png", //54*54  山东远桥logo
    // loginUrl: "/configImg/logo2.png",
    loginNameUrl: '/configImg/logoName.png', // 450*53
    // loginNameUrl: '/configImg/logoName-blankSpace.png', // 450*53  空白图
    loginBottomUrl: '/configImg/logoBottom.png',//北京远桥logo7
    loginSysName: 'Copyright &copy; 2021 北京远桥科技有限公司 出品',
  },
  //入口首页 gateway.vue
  gateway: {
    gatewaySysName: '远桥运维监控平台',
    gatewayBtmSysName: '远桥运维监控平台',
    gatewayBtmEnSysName: 'Operation and maintenance monitoring platform'
  },
  //系统左上角 Logo.vue
  sysConfig: {
    logourl:'/configImg/logo-white.png', //78*78  山东远桥logo
    // logourl: '/configImg/logo-white2.png',//北京远桥logo
    systemName: '远桥运维监控平台'
  },
  //大屏左上角 BigScreenData.vue
  bigscreen: {
    bigScreenLogoUrl:'/configImg/logo.png', //54*54  山东远桥logo
    // bigScreenLogoUrl: '/configImg/logo2.png',//北京远桥logo
    bigScreenSysName: '远桥运维监控平台'
  },
  //终端注册 activation.vue
  activation: {
    activationLogoUrl:'/configImg/06.png', //234*228   山东远桥logo
    // activationLogoUrl: '/configImg/logo-white2.png',//北京远桥logo
    activationSysName: '远桥运维监控平台',//中文系统名称
    activationEnSysName: 'yuan qiao Operation and maintenance monitoring platform',
  },
  //一键帮助 oneClickHelp/index.vue
  oneClickHelp: {
    isNew: false,//false 使用老页面  true 使用新页面
    // helpLogoUrl:'/configImg/06.png'//234*228  山东远桥logo
    helpLogoUrl: '/oneClickHelp/nmlogo.png', //运维助手logo
    pageLogoUrl: "/oneClickHelp/nmlogo1.png",
    pageStyle: "multiple",// 页面展示类型 single 单一  multiple多
    title: "运维助手",
    platformTitle: "信创一体化运维保障平台",
    bottomTitle: "山东远桥信息科技有限公司",
    telphone: '************',
    pageLogo: "/oneClickHelp/layout/logo.png",
    pageTitle:"信创运维小助手",//建议最多6个字
  },
  //省资源模式  0关闭，1开启
  simpleModel: 0,

  //logo显示  0不显示  1显示
  logoShow: 1,

  //资产编号自动生成 false不自动 true自动
  assetsAuto: false,

  // 各项目个性化配置
  customization: {
    // cz_+项目名
    // cz_yulin:{
    // }
  },
  //新疆多后台接口
  multipleURL: {
    $UKEY:"/insight-api/sys/SZUKeyLogin",//ukey登录 完整的接口路径
    $NWZHGL: "/nwzhgl-api",//http://**************:9091
    $INSIGHT: "/insight-api",//http://*************9
    $YQSM: "/yqsm-api",//http://**************:277
  },
  //是否ukey登录
  UKey: false,
  //应用系统唯一标识  应用系统编号，在维豪资源管理系统中注册后
  appUri: "",

  //数据中心布局类型
  //0-正常 数据中心 菜单平台类型设为4
  //1-统计中心 菜单平台类型设为8 （内蒙定制 ）
  DataCenterType: 1,

  //此值不为空字符串时 服务中心跳转此url页面
  //url字符串后面不带/
  //http://**************:288
  serveCenterURL: "",
  //拓扑图节点在线离线状态颜色
  TopoStatuColors:{ onLine: "#52c41a", offLine: '#8c8c8c' },
  //拓扑图连线状态颜色
  TopoLineColors:{connected:"#52c41a",notConnected:'#ff4d4f'},
  //授权信息展示配置  0 隐藏部分信息  1 全部展示
  authType:1,
}