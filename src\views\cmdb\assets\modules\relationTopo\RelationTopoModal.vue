<template>
  <j-modal
    :title="title"
    :width="width"
    :centered="true"
    :visible="visible"
    :destroyOnClose="true"
    :footer="null"
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }"
    @cancel="handleCancel"
    cancelText="关闭"
  >
    <enlarge-relation-topo ref="relationTopo" :assetsId="assetsId" :showDelAdd="true"></enlarge-relation-topo>
  </j-modal>
</template>

<script>
import EnlargeRelationTopo from './EnlargeRelationTopo'
import { httpAction, getAction } from '@/api/manage'
export default {
  name: 'RelationTopoModal',
  components: {
    EnlargeRelationTopo,
  },

  data() {
    return {
      title: '',
      statusList: [],
      width: 1200,
      visible: false,
      disableSubmit: false,
      url: {
        queryStatus: '/assets/assets/queryStatus',
        add: '/assets/assets/addField',
      },
      assetsId: '', //资产id
    }
  },
  methods: {
    add() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.add()
      })
    },
    edit(record) {
      this.visible = true
      getAction(this.url.queryStatus, { assetsId: record.id }).then((res) => {
        if (!!res) {
          this.statusList = res.result
        }
      })
      //record = this.records
      this.$nextTick(() => {
        this.$refs.realForm.edit(record)
      })
    },
    show(assetsId) {
      this.visible = true
      this.assetsId = assetsId
      this.$nextTick(() => {
        this.$refs.relationTopo.show()
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
    },

    //  handleOk () {
    //     this.$refs.realForm.submitForm();
    //     this.$emit("refresh");
    //     // this.close();
    //   },

    handleOk() {
      this.$refs.relationTopo.submitForm()
      this.close()
    },
    submitCallback() {
      this.$emit('ok')
      this.visible = false
    },
    handleCancel() {
      this.close()
    },
  },
}
</script>
<style scoped lang='less'>
@import '~@assets/less/limitModalWidth.less';
</style>