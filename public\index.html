<!DOCTYPE html>
<html lang='zh-cmn-Hans'>

<head>
  <meta charset='utf-8'>
  <meta http-equiv='X-UA-Compatible' content='IE=edge'>
  <meta name='viewport' content='width=device-width,initial-scale=1.0'>
  <title></title>
  <link rel='icon' href='<%= BASE_URL %>logo.png'>
  <!-- <link rel="stylesheet" href="./style11.css"> -->
  <!--  小程序自定义图标样式-->
  <link rel='stylesheet' href='./uni/iconfont.css'>
  <!--设备：ssh远程管理依赖样式-->
  <link rel='stylesheet' href="<%= BASE_URL %>webssh/xterm.min.css">
  <style>
      html,
      body,
      #app {
          height: 100vh;
          margin: 0px;
          padding: 0px;
          background: #0b132b;
      }


      .loader {
          position: absolute;
          top: 38%;
          left: 45%;
          font-size: 25vmin;
          width: 1em;
          height: 1em;
          background-color: #21346e;
          border-radius: 1em;
          position: relative;
      }

      .loader::after {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          border: 0.1rem solid #21346e;
          border-radius: inherit;
      }

      .loader.loaded::after {
          opacity: 0;
          border-color: #3d7af5;
          transform: scale(1.6);
          transition: opacity 0.6s ease, transform 0.6s ease-out;
      }

      #loaded1 {
          display: none;
      }

      .loader .meter {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          border-radius: inherit;
          overflow: hidden;
      }

      .loader .meter .fill {
          background-color: #3d7af5;
          width: 100%;
          height: 300px;
          position: absolute;
          left: 0;
          bottom: 0;
          z-index: 10;
          transform: translateY(1.5vh);
          filter: drop-shadow(0 0 1rem black);
          animation: heightAni 4s ease-in-out infinite;
          /* transition: height 1s linear; */
      }

      @keyframes heightAni {
          0% {
              /* height: 0%; */
              /* transform:rotate(0deg) */
              transform: translateY(500px);
          }

          50% {
              /* height: 50%; */
              /* transform:rotate(180deg) */
              transform: translateY(250px);
          }

          100% {
              /* height: 100%; */
              /* transform:rotate(360deg) */
              transform: translateY(0px);
          }
      }


      .loader .meter .fill svg {
          color: #3d7af5;
          position: absolute;
          transform: translateY(calc(-100% + 1px));
          width: 200%;
          animation: waves 1s infinite alternate;
      }

      @keyframes waves {
          0% {
              left: -10%;
              top: 2vh;
          }

          50% {
              top: 0vh;
          }

          100% {
              left: -60%;
              top: 1vh;
          }
      }

      .loader .meter .fill-text {
          position: absolute;
          z-index: 15;
          top: 50%;
          left: 50%;
          font-size: 1rem;
          transform: translate(-50%, -50%);
          color: rgba(15, 5, 20, 0.9);
          text-transform: uppercase;
          letter-spacing: 0.1em;
          font-weight: 900;
          pointer-events: none;
      }

      /* .loader .ball {
        position: absolute;
        background-color: #21346e;
        top: 50%;
        left: 0;
        border-radius: inherit;
      }

      .loader .ball.a {
        width: 0.25em;
        height: 0.25em;
        transform-origin: 1.5em;
        animation: rotate-a 4s linear infinite;
      }

      .loader .ball.b {
        width: 0.3em;
        height: 0.3em;
        transform-origin: 1em;
        animation: rotate-b 7s linear reverse infinite;
      } */

      @keyframes rotate-a {
          from {
              transform: translate(-1em, -50%) rotate(0deg);
          }

          to {
              transform: translate(-1em, -50%) rotate(360deg);
          }
      }

      @keyframes rotate-b {
          from {
              transform: translate(-0.5em, -50%) rotate(0deg);
          }

          to {
              transform: translate(-0.5em, -50%) rotate(360deg);
          }
      }

      .reload-button {
          position: absolute;
          bottom: 1rem;
          left: 1rem;
          border: 0;
          background-color: #21346e;
          font-family: monospace;
          color: rgba(15, 5, 20, 0.9);
          border-radius: 8px;
          padding: 1rem 2rem;
          box-shadow: 0 0 1rem 0.25rem rgba(0, 0, 0, 0.5);
          text-transform: uppercase;
          letter-spacing: 0.1em;
          outline: none;
          font-weight: 900;
      }

      .reload-button:hover {
          background-color: #3d7af5;
          cursor: pointer;
          box-shadow: 0 0 0.5rem 0.125rem rgba(0, 0, 0, 0.5);
      }

      .reload-button:active {
          transform: translateY(0.5em);
      }

      .signature {
          position: absolute;
          bottom: 1rem;
          right: 1rem;
      }

      .signature a {
          display: flex;
          align-items: center;
          text-decoration: none;
          color: #ffffff;
          font-family: monospace;
      }

      .signature img {
          height: 2rem;
          width: 2rem;
          margin-left: 1em;
          border-radius: 2rem;
      }

      /* 定义滚动条样式 */
      .topo-tree-select::-webkit-scrollbar {
          background-color: #222325 !important;
          width: 7px;
      }

      /*定义滚动条轨道 内阴影+圆角*/
      .topo-tree-select::-webkit-scrollbar-track {
          box-shadow: inset 0 0 0px rgba(240, 240, 240, 0.5);
          background-color: #313237;
      }

      /*定义滑块 内阴影+圆角*/
      .topo-tree-select::-webkit-scrollbar-thumb {
          border-radius: 10px;
          box-shadow: inset 0 0 0px rgba(240, 240, 240, 0.5);
          background-color: #5C5A5B;
      }

      .topo-tree-select::-webkit-scrollbar-corner {

          background-color: #222325;

      }

      /* 滚动条优化 end */

      /* 资产预警详情页，字体居中 */
      .desc-info .ant-descriptions-item-label {
          text-align: center;
      }

      body {
          overflow: auto !important;
          overflow-y: auto !important;
      }


      .topo-tree-select .ant-select-dropdown-content .ant-select-tree {
          color: #fff;
      }

      .topo-tree-select .ant-select-dropdown-content .ant-select-tree li .ant-select-tree-node-content-wrapper {
          color: #fff;
      }

      .topo-tree-select .ant-select-dropdown-content .ant-select-tree li .ant-select-tree-node-content-wrapper:hover {
          background-color: #303031;
      }

      .topo-tree-select .ant-select-tree li .ant-select-tree-node-content-wrapper.ant-select-tree-node-selected {
          background-color: #303031;
      }

      .confirmButton {
          background: #1e3674 !important;
          border-color: #1e3674 !important;
      }

      .ant-empty-image svg {
          opacity: 0.5;
      }

      .ant-empty .ant-empty-description {
          color: white;
          opacity: 0.5;
      }

      /* 日期范围 */
      .yq-picker .ant-calendar-range .ant-calendar-footer-extra {
          float: right !important;
      }

      .yq-picker .ant-tag {
          font-family: PingFangSC-Regular;
          font-size: 14px;
          color: #409EFF;
          background-color: #ecf5ff;
          background: #ECF5FF;
          border: 1px solid #B3D8FF;
          border-radius: 4px;
          border-radius: 4px;
          padding: 0 12px !important;
      }

      /* 暂无数据样式 */
      .ant-empty-description {
          color: rgba(0, 0, 0, 0.35) !important;
      }

      /*小程序的自定义图标*/
      @font-face {
          font-family: "uniiconsSelf";
          src: url('/uni/iconfont.ttf') format('truetype');
      }

      .uni-icons {
          font-family: "uniicons", "uniiconsSelf" !important;
          text-decoration: none;
          text-align: center;
      }

  </style>
</head>

<body style='overflow-y: auto;'>
<!-- 引入非国产化控件 -->
<!-- <object style="position: absolute;top:-1000px" id="loginOcx"
  classid="CLSID:91571FEA-14D0-41B4-B4B9-7C49A9EE66F8"></object> -->
<!-- 引入国产化控件 -->
<embed type='application/x-wellhope-initshare' style='position:absolute' width='0' height='0'
       id='initshare' />
<div id='app'>
  <!-- <div id="loader-wrapper">
  <div id="loader"></div>
  <div class="loader-section section-left"></div>
  <div class="loader-section section-right"></div>
  <div class="load_title">正在加载,请耐心等待

  </div>
</div> -->

  <div class='loader loaded' id='loaded1'>
    <div class='ball a'></div>
    <div class='ball b'></div>
    <div class='meter'>
      <div class='fill'>
        <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'>
          <path fill='currentColor'
                d='M0,128L34.3,138.7C68.6,149,137,171,206,176C274.3,181,343,171,411,154.7C480,139,549,117,617,144C685.7,171,754,245,823,234.7C891.4,224,960,128,1029,106.7C1097.1,85,1166,139,1234,165.3C1302.9,192,1371,192,1406,192L1440,192L1440,320L1405.7,320C1371.4,320,1303,320,1234,320C1165.7,320,1097,320,1029,320C960,320,891,320,823,320C754.3,320,686,320,617,320C548.6,320,480,320,411,320C342.9,320,274,320,206,320C137.1,320,69,320,34,320L0,320Z'>
          </path>
        </svg>
      </div>
      <span class='fill-text'>Loading</span>
    </div>
  </div>
</div>
<script src='<%= BASE_URL %>config.js' type='text/javascript'></script>
<script src='<%= BASE_URL %>zrBigScreen/mock/ZrOrganizations.js' type='text/javascript'></script>
<script src='<%= BASE_URL %>cdn/babel-polyfill/polyfill_7_2_5.js'></script>
<!-- <script src="<%= BASE_URL %>libgif.js" type="text/javascript"></script> -->
<!-- <script src="<%= BASE_URL %>config.webssh.js" type="text/javascript"></script> -->
<script src='<%= BASE_URL %>static/static-echarts/echarts.js' type='text/javascript'></script>
<script src='<%= BASE_URL %>static/static-echarts/longitudeLatitude.js' type='text/javascript'></script>
<script src='<%= BASE_URL %>static/static-echarts/city.js' type='text/javascript'></script>
<script src='<%= BASE_URL %>cdn/hotkeys.min.js' type='text/javascript'></script>
<!--自定义图标名称-->
<script src='<%= BASE_URL %>uni/iconfont.js' type='text/javascript'></script>
<!--设备：ssh远程管理-->
<script src="<%= BASE_URL %>webssh/jquery-3.4.1.min.js" type='text/javascript'></script>
<script src="<%= BASE_URL %>webssh/xterm.min.js" type='text/javascript' charset="utf-8"></script>
<script src="<%= BASE_URL %>webssh/xterm-addon-fit.min.js" type='text/javascript'></script>
<script src="<%= BASE_URL %>webssh/webssh.js" type='text/javascript' charset="utf-8"></script>
<!-- 全局配置 -->
<script>
  var scene, camera
  window._CONFIG = {}
  //赋值移动到配置文件config.js文件中

  window._CONFIG['system_Type'] = window.config.sysType
  window._CONFIG['platform_Type'] = window.config.platformType
  //登录页配置
  window._CONFIG['loginName'] = window.config.login.logoName
  window._CONFIG['visionStr'] = window.config.login.visionStr
  window._CONFIG['loginDesc'] = window.config.login.loginDesc
  window._CONFIG['loginUrl'] = window.config.login.loginUrl
  window._CONFIG['loginNameUrl'] = window.config.login.loginNameUrl
  window._CONFIG['loginBottomUrl'] = window.config.login.loginBottomUrl
  window._CONFIG['loginSysName'] = window.config.login.loginSysName

  //首页配置
  window._CONFIG['gatewaySysName'] = window.config.gateway.gatewaySysName
  window._CONFIG['gatewayVisionStr'] = window.config.gateway.gatewayVisionStr
  window._CONFIG['gatewayBtmSysName'] = window.config.gateway.gatewayBtmSysName
  window._CONFIG['gatewayBtmEnSysName'] = window.config.gateway.gatewayBtmEnSysName

  //系统左上角配置
  window._CONFIG['logoUrl'] = window.config.sysConfig.logourl
  window._CONFIG['systemName'] = window.config.sysConfig.systemName

  //大屏左上角配置
  window._CONFIG['bigScreenLogoUrl'] = window.config.bigscreen.bigScreenLogoUrl
  window._CONFIG['bigScreenSysName'] = window.config.bigscreen.bigScreenSysName

  //终端注册配置
  window._CONFIG['activationLogoUrl'] = window.config.activation.activationLogoUrl
  window._CONFIG['activationSysName'] = window.config.activation.activationSysName
  window._CONFIG['activationEnSysName'] = window.config.activation.activationEnSysName

  //一键帮助
  window._CONFIG['helpLogoUrl'] = window.config.oneClickHelp.helpLogoUrl

  //apiUrl配置
  window._CONFIG['domianURL'] = window.config.apiUrl
  if (!window._CONFIG['domianURL'].startsWith('http')) {
    //端口存在时，http://*************:8080/user/login
    if (!!window.location.port) {
      window._CONFIG['domianURL'] = window.location.href.slice(0, window.location.href.indexOf(window.location.port) + window.location.port.length) + window._CONFIG['domianURL']
    } else {
      //服务器端口为80时，默认不存在端口 http://*************/user/login
      // window.location.host获取ip， window.location.href获取方位路径
      window._CONFIG['domianURL'] = window.location.href.slice(0, window.location.href.indexOf(window.location.host) + window.location.host.length) + window._CONFIG['domianURL']
    }
  }
  window._CONFIG['downloadUrl'] = window._CONFIG['domianURL'] + '/sys/common/download'
  window._CONFIG['pathUrl'] = window._CONFIG['domianURL'] + '/drive/driveInfo/download'
  //单点登录地址
  window._CONFIG['casPrefixUrl'] = window.config.casUrl
  window._CONFIG['casSSo'] = window.config.casSSo
  window._CONFIG['staticDomainURL'] = window._CONFIG['domianURL'] + '/sys/common/static'
  window._CONFIG['pdfDomainURL'] = window._CONFIG['domianURL'] + '/sys/common/pdf/pdfPreviewIframe'
  //项目定制
  window._CONFIG['customization'] = window.config.customization

  //省资源模式
  window._CONFIG['simpleModel'] = window.config.simpleModel
  //国办配置
  window._CONFIG['ZFNW'] = window.config.ZFNW

  //项目logo显示
  window._CONFIG['logoShow'] = window.config.logoShow

  //资产编号自动生成
  window._CONFIG['assetsAuto'] = window.config.assetsAuto

  //修改网页标题和标题图标
  document.title = window.config.titleConfig.title
  document.querySelector('link[rel=icon]').href = window._CONFIG['logoShow'] == 0 ? './configImg/kongBai.png' : '<%= BASE_URL %>' + window.config.titleConfig.icon

  function showdiv() {
    var loaded1 = document.getElementById('loaded1')
    if (window._CONFIG['simpleModel'] == 0) {
      loaded1.style.display = 'block'
    } else {
      return
    }
  }

  showdiv()
</script>
</body>

</html>