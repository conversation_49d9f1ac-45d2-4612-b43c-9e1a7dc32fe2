<template>
  <a-row :gutter='10' style='height: 100%' class='vScroll zxw'>
    <a-col style='width: 100%; height: 100%; display: flex; flex-direction: column'>
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <!-- 查询区域 -->
        <div class='table-page-search-wrapper'>
          <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
            <a-row :gutter='24' ref='row'>
              <a-col :span='spanValue'>
                <a-form-item label='GROK名称'>
                  <a-input
                    :maxLength='maxLength'
                    placeholder='请输入'
                    autocomplete='off'
                    :allowClear='true'
                    v-model='queryParam.grokName'
                  ></a-input>
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label='GROK标识'>
                  <a-input
                    :maxLength='maxLength'
                    placeholder='请输入'
                    autocomplete='off'
                    :allowClear='true'
                    v-model='queryParam.grokCode'
                  ></a-input>
                </a-form-item>
              </a-col>
              <a-col :span='colBtnsSpan()'>
                <span
                  class='table-page-search-submitButtons'
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                >
                  <a-button type='primary' @click='searchQuery' class='btn-search-style'>查询</a-button>
                  <a-button @click='searchReset' class='btn-reset-style'>重置</a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered='false' style='flex: auto' class='core'>
        <div class='table-operator'>
          <a-button @click='handleAdd'>新增</a-button>
          <a-button @click='refreshCache()'>刷新缓存</a-button>
        </div>
        <a-table
          ref='table'
          rowKey='id'
          :columns='columns'
          :dataSource='dataSource'
          :pagination='ipagination'
          :loading='loading'
          bordered
          @change='handleTableChange'
        >
          <span
            slot='action'
            slot-scope='text, record'
            class='caozuo'
            style='display: inline-block; white-space: nowrap; text-align: center'
          >
            <a @click='settingGrokFormula(record)'>配置</a>
            <a-divider type='vertical' />
            <a @click='handleEdit(record)'>
              编辑
            </a>
            <a-divider type='vertical' />
            <a-popconfirm title='确定删除吗?' @confirm='() => handleDelete(record.id)'>
              <a>删除</a>
            </a-popconfirm>
          </span>
          <template slot='tooltip' slot-scope='text'>
            <a-tooltip placement='topLeft' :title='text' trigger='hover'>
              <div class='tooltip'>
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>

        <grok-modal ref='modalForm' @ok='modalFormOk'></grok-modal>
        <!-- 字典类型 -->
        <grok-formula-list ref='grokFormulaList'></grok-formula-list>
      </a-card>
    </a-col>
  </a-row>
</template>

<script>
import { filterObj } from '@/utils/util'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
import GrokFormulaList from '@views/eventManagement/grok/GrokFormulaList.vue'
import GrokModal from '@views/eventManagement/grok/GrokModal.vue'
import { getAction } from '@api/manage'
import Vue from 'vue'
import { UI_CACHE_DB_DICT_DATA } from '@/store/mutation-types'

export default {
  name: 'DictList',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components: { GrokModal, GrokFormulaList },
  data() {
    return {
      maxLength:50,
      isDown: 1,
      description: '',
      visible: false,
      // 查询条件
      queryParam: {
        grokCode: '',
        grokName: ''
      },
      // 表头
      columns: [
        {
          title: 'GROK名称',
          dataIndex: 'grokName',
          width: 200
        },
        {
          title: 'GROK标识',
          dataIndex: 'grokCode',
          width: 200
        },
        {
          title: '描述',
          dataIndex: 'description',
          scopedSlots: { customRender: 'tooltip' }
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          scopedSlots: { customRender: 'action' },
          width: 200
        }
      ],
      dict: '',
      labelCol: {
        xs: { span: 8 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 16 },
        sm: { span: 19 }
      },
      url: {
        list: '/grok/list',
        delete: '/grok/delete',
        refreshCache: 'grok/refreshCache'
      }
    }
  },
  mounted() {
  },
  methods: {
    //编辑字典数据
    settingGrokFormula(record) {
      this.$refs.grokFormulaList.edit(record)
    },
    refreshCache() {
      getAction(this.url.refreshCache)
        .then((res) => {
          if (res.success) {
            this.$message.success('刷新缓存完成！')
          }
        })
        .catch((e) => {
          this.$message.warn('刷新缓存失败！')
        })
    }
  },
  watch: {
    openKeys(val) {
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
// ::v-deep .ant-form-item-control-wrapper {
//   width: 72%;
// }
/*表头样式*/
::v-deep .ant-table-thead > tr > th {
  text-align: center;
  white-space: nowrap;
}

/*内容对齐方式、省略显示*/
::v-deep .ant-table-tbody > tr > td {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;

  &:first-child,
  &:nth-child(2) {
    text-align: center;
  }

  &:nth-child(3) {
    text-align: left;
  }
}
</style>