<template>
  <!-- 告警级别趋势分析 -->
  <div style="height: 100%;width: 100%;">
    <div v-if="chartData.length===0">
      <a-list :data-source="[]" />
    </div>
    <div v-else ref="alarmLevelsTrend" style="height: 100%;width: 100%;"></div>
  </div>
</template>
<script>
import echarts from 'echarts/lib/echarts'
import { getAction } from '@/api/manage'
export default {
  data() {
    return {
      chartData: [],
      url: {
        list: '/monitor/Overview/alarmLevelTrend'
      }
    }
  },
  props: {
    timeData: {
      type: Object,
      default: () => {}
    }
  },
  watch: {
    timeData: {
      deep: true,
      handler(val) {
        this.getData()
      }
    }
  },
  mounted() {
    this.getData()
  },
  methods: {
    getData() {
      const params = {};
      if (this.timeData.startTime) {
        params.startTime = this.timeData.startTime;
      }
      if (this.timeData.endTime) {
        params.endTime = this.timeData.endTime;
      }
      getAction(this.url.list, params).then(res => {
        if (res.code == 200) {
          if (res.result.value) {
            this.chartData = res.result.value
            this.$nextTick(() => {
              this.initChart(res.result)
            })
          } else {
            this.chartData = []
            if (this.myChart) {
              this.myChart.dispose()
            }
          }
        }
      })
    },
    // 告警趋势分析折线图
    initChart(data) {
      // 处理x轴的时间
      let time = []
      let fullTime = [] // 保存完整日期用于tooltip
      if (data.time && data.time.length > 0) {
        data.time.map(i => {
          fullTime.push(i) // 保存完整日期
          time.push(i.substr(5, 10))
        })
      }
      let value = data.value || []
      let color = ['254,97,35', '47,121,212', '37,211,244', '68,207,34']
      let serveList = []
      value.forEach((ele, i) => {
        serveList.push({
          name: ele.name,
          type: 'line',
          symbolSize: 4,
          lineStyle: {
            normal: {
              width: 2,
              color: `rgba(${ele.color})` // 线条颜色
            },
            borderColor: `rgba(${ele.color})`
          },
          itemStyle: {
            normal: {
              color: `rgba(${ele.color})`,
              borderColor: `rgba(${ele.color})`,
              borderWidth: 2
            },
            emphasis: {
              color: `rgba(${ele.color})`
            }
          },
          areaStyle: {
            //区域填充样式
            normal: {
              //线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是‘true’，则该四个值是绝对像素位置。
              color: new echarts.graphic.LinearGradient(
                0,
                0,
                0,
                1,
                [
                  {
                    offset: 0,
                    color: `rgba(${ele.color},.2)`
                  },
                  {
                    offset: 1,
                    color: `rgba(${ele.color},0)`
                  }
                ],
                false
              ),
              shadowColor: `rgba(${ele.color},.2)`, //阴影颜色
              shadowBlur: 20 //shadowBlur设图形阴影的模糊大小。配合shadowColor,shadowOffsetX/Y, 设置图形的阴影效果。
            }
          },
          data: ele.data
        })
      })
      this.myChart = this.$echarts.init(this.$refs.alarmLevelsTrend)
      this.myChart.clear()

      let option = {
        tooltip: {
          trigger: 'axis',
          formatter: function(params) {
            let str = ''
            let html = ''
            // 使用完整日期显示
            let name = fullTime[params[0].dataIndex] || '' // 获取对应索引的完整日期
            params.map(val => {
              str += `${val.marker}${val.seriesName}: ${val.value}条<br>`
            })
            html = `${name} <br>${str}`
            return html
          }
        },
        legend: {
          type: 'scroll',
          top: '0%',
          right: 15,
          itemWidth: 18,
          itemHeight: 7,
          textStyle: {
            color: 'rgba(0,0,0,0.65)',
            fontSize: 12
          }
        },
        grid: {
          top: '17%',
          left: '3%',
          right: '9%',
          bottom: 10,
          containLabel: true
        },
        dataZoom: [
          {
            id: 'dataZoomY',
            xAxisIndex: [0],
            show: false, //是否显示滑动条，不影响使用
            type: 'slider', // 这个 dataZoom 组件是 slider 型 dataZoom 组件
            startValue: 0, // 从头开始。
            endValue: 6,
            zoomLock: true,
            showDataShadow: false, //是否显示数据阴影 默认auto
            backgroundColor: 'rgba(255,255,255,0)',
            showDetail: false, //即拖拽时候是否显示详细数值信息 默认true
            realtime: true, //是否实时更新
            filterMode: 'filter',
            handleIcon: 'circle',
            handleStyle: {
              color: 'rgba(205,205,205,1)',
              borderColor: 'rgba(205,205,205,1)'
            },
            moveHandleSize: 0,
            brushSelect: false //刷选功能，设为false可以防止拖动条长度改变 ************（这是一个坑）
          },
          {
            type: 'inside',
            xAxisIndex: 0,
            zoomOnMouseWheel: false, //滚轮是否触发缩放
            moveOnMouseMove: true, //鼠标滚轮触发滚动
            moveOnMouseWheel: true
          }
        ],
        xAxis: [
          {
            type: 'category',
            boundaryGap: false,
            axisLine: {
              //坐标轴轴线相关设置
              show: true,
              lineStyle: {
                color: '#C1C1C1'
              }
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: 'rgba(0,0,0,0.45)' //更改坐标轴文字颜色
              }
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: '#8CA0B3',
                type: 'dashed',
                width: 0.5
              }
            },
            axisTick: {
              show: false
            },
            data: time
          }
        ],
        yAxis: [
          {
            splitLine: {
              lineStyle: {
                color: '#8CA0B3',
                type: 'dashed',
                width: 0.5
              }
            },
            axisLine: {
              //坐标轴轴线相关设置
              show: true,
              lineStyle: {
                color: '#C1C1C1'
              }
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: 'rgba(0,0,0,0.45)' //更改坐标轴文字颜色
              }
            },
            axisTick: {
              show: false
            }
          }
        ],
        series: serveList
      }
      this.myChart.setOption(option)
      window.addEventListener('resize', () => {
        this.myChart.resize()
      })
    }
  }
}
</script>
<style scoped lang="less">
</style>