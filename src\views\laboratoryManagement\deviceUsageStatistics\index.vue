<template>
  <div class="body">
    <!-- <div class="header">
      <div class="header-right">
        <div class="header-right-time">
          <span>选择日期:</span>
          <div class="time-range-span">
            <a-range-picker size="small" @change="onChange" />
          </div>
        </div>
        <a-button type='primary' class='btn-search btn-search-style' @click='search'>查询</a-button>
      </div>
    </div> -->
    <div class="core">
      <div class="core-top">
        <div class="core-top-left">
          <div class="title">
            <img src="@/assets/bigScreen/9.png" alt="" />
            <span>设备预约次数 TOP10</span>
          </div>
          <div class="core-top-left-body">
            <div class="core-top-left-body-Histogram" id="handleTopHistogram"></div>
          </div>
        </div>
        <div class="core-top-core">
          <div class="core-top-core-top">
            <div class="title">
              <img src="@/assets/bigScreen/9.png" alt="" />
              <span>用户预约次数 TOP10</span>
            </div>
            <div class="core-top-core-top-body">
              <div class="core-top-center-body-Histogram" id="userNum"></div>
            </div>
          </div>
        </div>
        <div class="core-top-right">
          <div class="title">
            <img src="@/assets/bigScreen/9.png" alt="" />
            <span>项目预约次数 TOP10</span>
          </div>
          <div class="core-top-right-body">
            <div class="core-top-right-body-Histogram" id="averageTopHistogram"></div>
          </div>
        </div>
      </div>
      <div class="core-bottom">
        <div class="core-bottom-left">
          <div class="title">
            <img src="@/assets/bigScreen/9.png" alt="" />
            <span>设备预约时长 TOP10</span>
          </div>
          <div class="core-bottom-left-body">
            <div class="core-bottom-left-body-Pic" id="typeCountPie"></div>
          </div>
        </div>
        <div class="core-bottom-center">
          <div class="title">
            <img src="@/assets/bigScreen/9.png" alt="" />
            <span>用户预约时长 TOP10</span>
          </div>
          <div class="core-bottom-center-body">
            <div class="core-bottom-center-body-Line" id="dayCountLine"></div>
          </div>
        </div>
        <div class="core-bottom-right">
          <div class="title">
            <img src="@/assets/bigScreen/9.png" alt="" />
            <span>项目预约时长 TOP10</span>
          </div>
          <div class="core-bottom-right-body">
            <div class="core-bottom-right-body-Line" id="evaluate"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  import echarts from 'echarts'
  import vueSeamlessScroll from 'vue-seamless-scroll'
  import {
    getAction
  } from '@/api/manage'

  export default {
    data() {
      return {
        time1: '',
        time2: '',
        deviceList: [{
          name: '服务器251',
          value: 9
        }, {
          name: 'tg_00:50:56:9a:51:c1',
          value: 8
        }, {
          name: 'bjb-02',
          value: 7
        }, {
          name: 'tg_192.168.16.151',
          value: 5
        }, {
          name: 'tg_tg_00:0c:29:da:36:1b',
          value: 3
        }, {
          name: 'bjb-01',
          value: 2
        }, {
          name: ' tg_00:0c:29:70:5f:d2',
          value: 2
        }, {
          name: 'tg_8c:e5:ef:97:4b:82',
          value: 2
        }, {
          name: 'tg_00:0c:29:4e:eb:b3',
          value: 1
        }],
        userList: [{
          name: '管理员',
          value: 7
        }, {
          name: 'auditadm',
          value: 7
        }, {
          name: 'gcs1',
          value: 5
        }, {
          name: 'Bala',
          value: 5
        }, {
          name: 'laowang',
          value: 3
        }, {
          name: 'fwsBird',
          value: 2
        }, {
          name: 'zhangtiantian',
          value: 2
        }],
        productList: [{
          name: '运维监控App',
          value: 8
        }, {
          name: '内蒙小程序',
          value: 4
        }, {
          name: '运维服务',
          value: 3
        }, {
          name: '平台测试',
          value: 3
        }],
        url: {
          handleTop: '/data-analysis/order/handle/top',
          typeCount: '/data-analysis/order/type/count',
          dayCount: '/data-analysis/order/day/count',
          handleAverageTop: '/data-analysis/order/handle/average/top',
          evaluate: '/data-analysis/order/evaluation',
          export: '/data-analysis/order/export',
        },
      }
    },
    components: {
      vueSeamlessScroll,
    },
    mounted() {
      this.typeCount()
      this.dayCount()
      this.getEvaluate()
      this.handleTop()
      this.handleAverageTop()
    },
    methods: {
      handleTop() {
        getAction(this.url.handleTop, {
          time1: this.time1,
          time2: this.time2
        }).then((res) => {
          if (res.code == 200) {
            this.handleTopHistogram(res.result)
            this.userNum()
          }
        })
      },
      // 查询
      search() {
        this.typeCount()
        this.dayCount()
        this.getEvaluate()
      },

      onChange(dates, dateStrings) {
        this.time1 = dateStrings[0]
        this.time2 = dateStrings[1]
      },
      //评价信息统计饼图数据
      getEvaluate() {
        getAction(this.url.evaluate, {
          time1: this.time1,
          time2: this.time2
        }).then((res) => {
          if (res.code == 200) {
            this.evaluate(res.result)
          }
        })
      },
      evaluate(data) {
        function attackSourcesDataFmt(sData) {
          var sss = []
          sData.forEach(function (item, i) {
            if (item.value == undefined || item.value == null || item.value == '') {
              item.value = 0
            }
            sss.push({
              value: item.value,
            })
          })
          return sss
        }
        let xarr = ['运维监控App', '内蒙小程序', '运维服务', '平台测试']
        let yarr = [3, 3, 4, 8]
        let myChart = this.$echarts.init(document.getElementById('evaluate'))
        myChart.setOption({
          tooltip: {
            show: true,
            trigger: 'axis',
            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
            },
            transitionDuration: 0, //echart防止tooltip的抖动
          },
          xAxis: {
            type: 'value',
            splitLine: {
              show: false,
            }, //去除网格线
            show: false,
          },
          yAxis: [{
              type: 'category',
              data: xarr,
              splitLine: {
                show: false,
              }, //去除网格线
              axisTick: {
                show: false,
              },
              axisLine: {
                show: false, //y轴线消失
                lineStyle: {
                  //y轴字体颜色
                  color: '#C2C3CD',
                },
              },
              axisLabel: {
                formatter: (value) => {
                  if (value.length > 8) {
                    return value.substring(0, 8) + '...'
                  } else {
                    return value
                  }
                },
              },
            },
            {
              type: 'category',
              inverse: true,
              axisTick: 'none',
              axisLine: 'none',
              show: true,
              axisLabel: {
                textStyle: {
                  color: '#C2C3CD',
                  fontSize: '12',
                },
              },
              data: attackSourcesDataFmt(this.productList),
            },
          ],
          grid: {
            top: 0,
            left: 90, // 调整这个属性
            right: 50,
            bottom: 0,
          },
          series: [{
            data: yarr,
            type: 'bar',
            // showBackground: true,
            // backgroundStyle: {
            //   color: '#d3d9df',
            //   barBorderRadius: 30,
            // },
            barWidth: 10, //柱图宽度
            itemStyle: {
              emphasis: {
                barBorderRadius: 30,
              },
              normal: {
                barBorderRadius: [10, 10, 10, 10],
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
                    offset: 0,
                    color: 'rgba(85,206,199,1)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(85,206,199,0.5)',
                  },
                ]),
              },
            },
          }, ],
        })
        window.addEventListener('resize', () => {
          myChart.resize()
        })
      },
      handleTopHistogram(data) {
        function attackSourcesDataFmt(sData) {
          var sss = []
          sData.forEach(function (item, i) {
            if (item.value == undefined || item.value == null || item.value == '') {
              item.value = 0
            }
            sss.push({
              value: item.value,
            })
          })
          return sss
        }
        let xarr = ['服务器251', 'tg_00:50:56:9a:51:c1', 'bjb-02', 'tg_192.168.16.151', 'renda121', 'mariadb', 'tg_00:0c:29:70:5f:d2',
          'tg_8c:e5:ef:97:4b:82', 'tg_tg_00:0c:29:da:36:1b'
        ]
        let yarr = [2, 2, 3, 3, 4, 5, 7, 8, 9]
        let myChart = this.$echarts.init(document.getElementById('handleTopHistogram'))
        myChart.setOption({
          tooltip: {
            show: true,
            trigger: 'axis',
            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
            },
            transitionDuration: 0, //echart防止tooltip的抖动
          },
          xAxis: {
            type: 'value',
            splitLine: {
              show: false,
            }, //去除网格线
            show: false,
          },
          yAxis: [{
              type: 'category',
              data: xarr,
              splitLine: {
                show: false,
              }, //去除网格线
              axisTick: {
                show: false,
              },
              axisLine: {
                show: false, //y轴线消失
                lineStyle: {
                  //y轴字体颜色
                  color: '#C2C3CD',
                },
              },
              axisLabel: {
                formatter: (value) => {
                  if (value.length > 8) {
                    return value.substring(0, 8) + '...'
                  } else {
                    return value
                  }
                },
              },
            },
            {
              type: 'category',
              inverse: true,
              axisTick: 'none',
              axisLine: 'none',
              show: true,
              axisLabel: {
                textStyle: {
                  color: '#C2C3CD',
                  fontSize: '12',
                },
              },
              data: attackSourcesDataFmt(this.deviceList),
            },
          ],
          grid: {
            top: 0,
            left: 90, // 调整这个属性
            right: 50,
            bottom: 0,
          },
          series: [{
            data: yarr,
            type: 'bar',
            // showBackground: true,
            // backgroundStyle: {
            //   color: '#d3d9df',
            //   barBorderRadius: 30,
            // },
            barWidth: 10, //柱图宽度
            itemStyle: {
              emphasis: {
                barBorderRadius: 30,
              },
              normal: {
                barBorderRadius: [10, 10, 10, 10],
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
                    offset: 0,
                    color: '#1890FF',
                  },
                  {
                    offset: 1,
                    color: '#85C0FF',
                  },
                ]),
              },
            },
          }, ],
        })
        window.addEventListener('resize', () => {
          myChart.resize()
        })
      },
      userNum() {
        function attackSourcesDataFmt(sData) {
          var sss = []
          sData.forEach(function (item, i) {
            if (item.value == undefined || item.value == null || item.value == '') {
              item.value = 0
            }
            sss.push({
              value: item.value,
            })
          })
          return sss
        }
        let xarr = ['auditadm', 'gcs1', 'Bala', 'laowang', 'fwsBird',
          'zhangtiantian', 'zxw'
        ]
        let yarr = [2, 2, 3, 5, 5, 7, 7]
        let myChart = this.$echarts.init(document.getElementById('userNum'))
        myChart.setOption({
          tooltip: {
            show: true,
            trigger: 'axis',
            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
            },
            transitionDuration: 0, //echart防止tooltip的抖动
          },
          xAxis: {
            type: 'value',
            splitLine: {
              show: false,
            }, //去除网格线
            show: false,
          },
          yAxis: [{
              type: 'category',
              data: xarr,
              splitLine: {
                show: false,
              }, //去除网格线
              axisTick: {
                show: false,
              },
              axisLine: {
                show: false, //y轴线消失
                lineStyle: {
                  //y轴字体颜色
                  color: '#C2C3CD',
                },
              },
              axisLabel: {
                formatter: (value) => {
                  if (value.length > 8) {
                    return value.substring(0, 8) + '...'
                  } else {
                    return value
                  }
                },
              },
            },
            {
              type: 'category',
              inverse: true,
              axisTick: 'none',
              axisLine: 'none',
              show: true,
              axisLabel: {
                textStyle: {
                  color: '#C2C3CD',
                  fontSize: '12',
                },
              },
              data: attackSourcesDataFmt(this.userList),
            },
          ],
          grid: {
            top: 0,
            left: 90, // 调整这个属性
            right: 50,
            bottom: 0,
          },
          series: [{
            data: yarr,
            type: 'bar',
            // showBackground: true,
            // backgroundStyle: {
            //   color: '#d3d9df',
            //   barBorderRadius: 30,
            // },
            barWidth: 10, //柱图宽度
            itemStyle: {
              emphasis: {
                barBorderRadius: 30,
              },
              normal: {
                barBorderRadius: [10, 10, 10, 10],
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
                    offset: 0,
                    color: 'rgba(112,151,202,1)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(112,151,202,0.5)',
                  },
                ]),
              },
            },
          }, ],
        })
        window.addEventListener('resize', () => {
          myChart.resize()
        })
      },
      // 平均处理时间柱状图数据
      handleAverageTop() {
        getAction(this.url.handleAverageTop, {
          time1: this.time1,
          time2: this.time2
        }).then((res) => {
          if (res.code == 200) {
            this.averageTopHistogram(res.result)
          }
        })
      },

      // 平均处理时间柱状图
      averageTopHistogram(data) {
        function attackSourcesDataFmt(sData) {
          var sss = []
          sData.forEach(function (item, i) {
            if (item.value == undefined || item.value == null || item.value == '') {
              item.value = 0
            }
            sss.push({
              value: item.value,
            })
          })
          return sss
        }
        let xarr = ['运维监控App', '内蒙小程序', '运维服务', '平台测试']
        let yarr = [3, 3, 4, 8]
        let myChart = this.$echarts.init(document.getElementById('averageTopHistogram'))
        myChart.setOption({
          tooltip: {
            show: true,
            trigger: 'axis',
            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
            },
            transitionDuration: 0, //echart防止tooltip的抖动
          },
          xAxis: {
            type: 'value',
            splitLine: {
              show: false,
            }, //去除网格线
            show: false,
          },
          yAxis: [{
              type: 'category',
              data: xarr,
              splitLine: {
                show: false,
              }, //去除网格线
              axisTick: {
                show: false,
              },
              axisLine: {
                show: false, //y轴线消失
                lineStyle: {
                  //y轴字体颜色
                  color: '#C2C3CD',
                },
              },
              axisLabel: {
                formatter: (value) => {
                  if (value.length > 8) {
                    return value.substring(0, 8) + '...'
                  } else {
                    return value
                  }
                },
              },
            },
            {
              type: 'category',
              inverse: true,
              axisTick: 'none',
              axisLine: 'none',
              show: true,
              axisLabel: {
                textStyle: {
                  color: '#C2C3CD',
                  fontSize: '12',
                },
              },
              data: attackSourcesDataFmt(this.productList),
            },
          ],
          grid: {
            top: 0,
            left: 90, // 调整这个属性
            right: 50,
            bottom: 0,
          },
          series: [{
            data: yarr,
            type: 'bar',
            // showBackground: true,
            // backgroundStyle: {
            //   color: '#d3d9df',
            //   barBorderRadius: 30,
            // },
            barWidth: 10, //柱图宽度
            itemStyle: {
              emphasis: {
                barBorderRadius: 30,
              },
              normal: {
                barBorderRadius: [10, 10, 10, 10],
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
                    offset: 0,
                    color: 'rgba(85,206,199,1)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(85,206,199,0.5)',
                  },
                ]),
              },
            },
          }, ],
        })
        window.addEventListener('resize', () => {
          myChart.resize()
        })
      },

      // 服务请求类型统计饼状图数据
      typeCount() {
        getAction(this.url.typeCount, {
          time1: this.time1,
          time2: this.time2
        }).then((res) => {
          if (res.code == 200) {
            this.typeCountPie()
          }
        })
      },

      // 服务请求类型统计饼状图
      typeCountPie() {
        function attackSourcesDataFmt(sData) {
          var sss = []
          sData.forEach(function (item, i) {
            if (item.value == undefined || item.value == null || item.value == '') {
              item.value = 0
            }
            sss.push({
              value: item.value,
            })
          })
          return sss
        }
        let xarr = ['服务器251', 'tg_00:50:56:9a:51:c1', 'bjb-02', 'tg_192.168.16.151', 'renda121', 'mariadb', 'tg_00:0c:29:70:5f:d2',
          'tg_8c:e5:ef:97:4b:82', 'tg_tg_00:0c:29:da:36:1b'
        ]
        let yarr = [2, 2, 3, 3, 4, 5, 7, 8, 9]
        let myChart = this.$echarts.init(document.getElementById('typeCountPie'))
        myChart.setOption({
          tooltip: {
            show: true,
            trigger: 'axis',
            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
            },
            transitionDuration: 0, //echart防止tooltip的抖动
          },
          xAxis: {
            type: 'value',
            splitLine: {
              show: false,
            }, //去除网格线
            show: false,
          },
          yAxis: [{
              type: 'category',
              data: xarr,
              splitLine: {
                show: false,
              }, //去除网格线
              axisTick: {
                show: false,
              },
              axisLine: {
                show: false, //y轴线消失
                lineStyle: {
                  //y轴字体颜色
                  color: '#C2C3CD',
                },
              },
              axisLabel: {
                formatter: (value) => {
                  if (value.length > 8) {
                    return value.substring(0, 8) + '...'
                  } else {
                    return value
                  }
                },
              },
            },
            {
              type: 'category',
              inverse: true,
              axisTick: 'none',
              axisLine: 'none',
              show: true,
              axisLabel: {
                textStyle: {
                  color: '#C2C3CD',
                  fontSize: '12',
                },
              },
              data: attackSourcesDataFmt(this.deviceList),
            },
          ],
          grid: {
            top: 0,
            left: 90, // 调整这个属性
            right: 50,
            bottom: 0,
          },
          series: [{
            data: yarr,
            type: 'bar',
            // showBackground: true,
            // backgroundStyle: {
            //   color: '#d3d9df',
            //   barBorderRadius: 30,
            // },
            barWidth: 10, //柱图宽度
            itemStyle: {
              emphasis: {
                barBorderRadius: 30,
              },
              normal: {
                barBorderRadius: [10, 10, 10, 10],
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
                    offset: 0,
                    color: '#1890FF',
                  },
                  {
                    offset: 1,
                    color: '#85C0FF',
                  },
                ]),
              },
            },
          }, ],
        })
        window.addEventListener('resize', () => {
          myChart.resize()
        })
      },

      // 服务请求数据统计折线图数据
      dayCount() {
        getAction(this.url.dayCount, {
          time1: this.time1,
          time2: this.time2
        }).then((res) => {
          if (res.code == 200) {
            this.dayCountLine()
          }
        })
      },

      // 服务请求数据统计折线图
      dayCountLine() {
        function attackSourcesDataFmt(sData) {
          var sss = []
          sData.forEach(function (item, i) {
            if (item.value == undefined || item.value == null || item.value == '') {
              item.value = 0
            }
            sss.push({
              value: item.value,
            })
          })
          return sss
        }
        let xarr = ['auditadm', 'gcs1', 'Bala', 'laowang', 'fwsBird',
          'zhangtiantian', 'zxw'
        ]
        let yarr = [2, 2, 3, 5, 5, 7, 7]
        let myChart = this.$echarts.init(document.getElementById('dayCountLine'))
        myChart.setOption({
          tooltip: {
            show: true,
            trigger: 'axis',
            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
            },
            transitionDuration: 0, //echart防止tooltip的抖动
          },
          xAxis: {
            type: 'value',
            splitLine: {
              show: false,
            }, //去除网格线
            show: false,
          },
          yAxis: [{
              type: 'category',
              data: xarr,
              splitLine: {
                show: false,
              }, //去除网格线
              axisTick: {
                show: false,
              },
              axisLine: {
                show: false, //y轴线消失
                lineStyle: {
                  //y轴字体颜色
                  color: '#C2C3CD',
                },
              },
              axisLabel: {
                formatter: (value) => {
                  if (value.length > 8) {
                    return value.substring(0, 8) + '...'
                  } else {
                    return value
                  }
                },
              },
            },
            {
              type: 'category',
              inverse: true,
              axisTick: 'none',
              axisLine: 'none',
              show: true,
              axisLabel: {
                textStyle: {
                  color: '#C2C3CD',
                  fontSize: '12',
                },
              },
              data: attackSourcesDataFmt(this.userList),
            },
          ],
          grid: {
            top: 0,
            left: 90, // 调整这个属性
            right: 50,
            bottom: 0,
          },
          series: [{
            data: yarr,
            type: 'bar',
            // showBackground: true,
            // backgroundStyle: {
            //   color: '#d3d9df',
            //   barBorderRadius: 30,
            // },
            barWidth: 10, //柱图宽度
            itemStyle: {
              emphasis: {
                barBorderRadius: 30,
              },
              normal: {
                barBorderRadius: [10, 10, 10, 10],
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
                    offset: 0,
                    color: 'rgba(112,151,202,1)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(112,151,202,0.5)',
                  },
                ]),
              },
            },
          }, ],
        })
        window.addEventListener('resize', () => {
          myChart.resize()
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  .title {
    height: 14%;
    display: flex;
    align-items: center;
    font-size: 0.225rem
      /* 18/80 */
    ;
    color: #656665;
    padding-left: 0.15rem
      /* 12/80 */
    ;
    letter-spacing: 0.025rem
      /* 2/80 */
    ;

    img {
      width: 0.125rem
        /* 10/80 */
      ;
      height: 0.1625rem
        /* 13/80 */
      ;
      margin-right: 0.0875rem
        /* 7/80 */
      ;
    }
  }

  .body {
    width: 100%;
    height: 100%;
    padding: 0 0.2rem 0.1rem 0.2rem;
    display: flex;
    flex-direction: column;

    // .header {
    //   width: 100%;
    //   height: 7%;
    //   display: flex;
    //   margin-bottom: 10px;

    //   .header-right {
    //     width: 34%;
    //     display: flex;
    //     align-items: center;
    //     margin-left: 0.5rem;

    //     .header-right-time {
    //       font-size: 0.175rem
    //         /* 14/80 */
    //       ;
    //       font-family: PingFang SC;
    //       letter-spacing: 0px;
    //       font-weight: 100;
    //       // color: #ffffff;
    //       display: flex;
    //       align-items: center;

    //       .time-range-span {
    //         margin-right: 0.4375rem
    //           /* 35/80 */
    //         ;
    //         margin-left: 0.2rem
    //           /* 16/80 */
    //         ;
    //       }
    //     }
    //   }
    // }

    .core {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .core-top {
        width: 100%;
        height: 49%;
        display: flex;
        justify-content: space-between;

        .core-top-left {
          width: 33%;
          height: 100%;
          background: #ffffff;
          border-radius: 0.075rem
            /* 6/80 */
          ;

          .core-top-left-body {
            width: 100%;
            height: 86%;
            display: flex;
            align-items: center;
            justify-content: center;

            .core-top-left-body-Histogram {
              width: 100%;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }
        }

        .core-top-core {
          width: 33%;
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: space-between;

          .core-top-core-top {
            width: 100%;
            height: 100%;
            background: #ffffff;
            border-radius: 0.075rem
              /* 6/80 */
            ;

            .core-top-core-top-body {
              width: 100%;
              height: 86%;
              display: flex;
              align-items: center;
              justify-content: space-between;
              color: #fff;
              font-size: 0.2rem
                /* 16/80 */
              ;
              ;

              .core-top-center-body-Histogram {
                width: 100%;
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
              }
            }
          }
        }

        .core-top-right {
          width: 33%;
          height: 100%;
          background: #ffffff;
          border-radius: 0.075rem
            /* 6/80 */
          ;

          .core-top-right-body {
            width: 100%;
            height: 86%;
            display: flex;
            align-items: center;
            justify-content: center;

            .core-top-right-body-Histogram {
              width: 100%;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }
        }
      }

      .core-bottom {
        width: 100%;
        height: 50%;
        display: flex;
        justify-content: space-between;

        .core-bottom-left {
          width: 33%;
          height: 100%;
          background: #ffffff;
          border-radius: 0.075rem
            /* 6/80 */
          ;

          .core-bottom-left-body {
            width: 100%;
            height: 86%;
            display: flex;
            justify-content: center;

            .core-bottom-left-body-Pic {
              width: 100%;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }
        }

        .core-bottom-center {
          width: 33%;
          height: 100%;
          background: #ffffff;
          border-radius: 0.075rem
            /* 6/80 */
          ;

          .core-bottom-center-body {
            width: 100%;
            height: 86%;
            display: flex;
            align-items: center;
            justify-content: center;

            .core-bottom-center-body-Line {
              width: 100%;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }
        }

        .core-bottom-right {
          width: 33%;
          height: 100%;
          background: #ffffff;
          border-radius: 0.075rem
            /* 6/80 */
          ;

          .core-bottom-right-body {
            width: 100%;
            height: 86%;
            display: flex;
            align-items: center;
            justify-content: center;

            .core-bottom-right-body-Line {
              width: 100%;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }
        }
      }
    }
  }
</style>