<template>
  <div class="bigScreenData">
    <div class="header">
      <div class="left">
        <div class="logo">
          <div class="logo-left">
            <!--            <router-link  :to="{ path: sysRoute }" v-if="systemType !== '0'&&!onePtmFlag">
              <img v-show="logoShow" :src="logoUrl" alt="" />
              <span>{{ title }}</span>
            </router-link>-->
            <router-link :to="{ path: '/gateway' }" v-if="systemType === '0' && !onePtmFlag">
              <img v-show="logoShow" :src="logoUrl" alt="" style="margin-right: -15px" />
              <span :style='{fontSize:`calc(0.275rem * 10 / ${title.length>10?title.length:10})`}'>{{ title }}</span>
            </router-link>
            <a v-else-if="systemType !== '0'" href="javascript:void(0)" @click="logoClick">
              <img v-show="logoShow" :src="logoUrl" alt="" style="margin-right: -15px" />
              <span :style='{fontSize:`calc(0.275rem * 10 / ${title.length>10?title.length:10})`}'>{{ title }}</span>
            </a>
            <div v-else>
              <img v-show="logoShow" :src="logoUrl" alt="" style="margin-right: -15px" />
              <span :style='{fontSize:`calc(0.275rem * 10 / ${title.length>10?title.length:10})`}'>{{ title }}</span>
            </div>
          </div>
          <div class="logo-right">
            <span class="text"> {{ currentPage }} </span>
          </div>
        </div>
      </div>
      <div class="right">
        <gateway-menus
          v-if="systemType==='2'"
          :menusName="'数据中心'"
          :textStyle="{fontSize:'15px'}"
          :icon2Style="{fontSize:'10px'}"
          colorTheme="darkTheme"
          style="margin-right: 16px;"
        ></gateway-menus>
        <div class="sysRoute" v-if="!['0','2'].includes(systemType)" @click="entrancePlanning(1)">{{ platformType }}</div>
        <div class="time">{{ date | formatDate }}</div>
        <div class="time2">{{ date2 | formatDate2 }}</div>

        <!--        <div class="return" v-if="systemType === '0' && !onePtmFlag">
          <router-link :to="{ path: '/gateway' }">
            <img src="../../assets/bigScreen/10.png" alt="" />
          </router-link>
        </div>-->
        <div class="name">欢迎您，{{ nickname() }}</div>
        <div class="action">
          <a-icon class="exit" type="poweroff" title="退出登录" @click="handleLogout" />
          <!--          <a class="logout_title" href="javascript:;" @click="handleLogout">-->
          <!--          <img src="~@/assets/edit.png" alt="" title="退出登录" />-->
          <!--      </a>-->
        </div>
      </div>
    </div>
    <!-- 左侧菜单 -->
    <div class="sidebar">
      <div class="sidebar-left">
        <div class="sidebar-left-item" v-for="(menuItem, idx) in menusArr" :key="idx">
          <div>
            <el-popover placement="right" trigger="hover" v-if="menuItem.children">
              <div>
                <div v-for="(citem, cidx) in menuItem.children" :key="cidx">
                  <router-link
                    :to="{ name: citem.name }"
                    :class="currentPage == menuItem.meta.title + '/' + citem.meta.title ? 'Select' : 'noselect'"
                  >
                    {{ citem.meta.title }}</router-link
                  >
                </div>
              </div>
              <a slot="reference">
                <a-icon
                  :type="menuItem.meta.icon"
                  :style="{
                    fontSize: '36px',
                    color: menuItem.meta.title == currentPage.split('/')[0] ? '#02c6ec' : '#A0A0A0',
                  }"
                ></a-icon>
              </a>
            </el-popover>

            <a-tooltip placement="rightTop" v-else>
              <template slot="title">
                <span>{{ menuItem.meta.title }}</span>
              </template>
              <router-link :to="{ name: menuItem.name }">
                <a-icon
                  :type="menuItem.meta.icon"
                  :style="{ fontSize: '36px', color: currentPage == menuItem.meta.title ? '#02c6ec' : '#A0A0A0' }"
                >
                </a-icon>
              </router-link>
            </a-tooltip>
          </div>
        </div>
      </div>
      <div class="sidebar-right">
        <router-view></router-view>
      </div>
    </div>
  </div>
</template>

<script>
import { getAction, postAction } from '@/api/manage'
import GlobalLayout from '@/components/page/GlobalLayout'
import Contextmenu from '@/components/menu/Contextmenu'
import { mixin, mixinDevice } from '@/utils/mixin.js'
import { triggerWindowResizeEvent, generateIndexRouter, generateBigscreenRouter } from '@/utils/util'
const indexKey = '/dashboard/analysis'
import Vue from 'vue'
import router from '@/router'
import store from '@/store'
import { CACHE_INCLUDED_ROUTES, ONE_PLATFORM_FLAG, PLATFORM_TYPE } from '@/store/mutation-types'
import { mapActions, mapGetters, mapState } from 'vuex'
import { path } from '@antv/x6/lib/registry/marker/path'
import { WebsocketMixin } from '@/mixins/WebsocketMixin'
import GatewayMenus from '../tools/GatewayMenus.vue'
var padDate = function (value) {
  return value < 10 ? '0' + value : value
}

export default {
  name: 'TabLayout',
  components: {
    GlobalLayout,
    Contextmenu,
    GatewayMenus,
  },
  mixins: [mixin, mixinDevice, WebsocketMixin],
  data() {
    return {
      logoShow: window._CONFIG['logoShow'] === 1 ? true : false,
      title: window._CONFIG['bigScreenSysName'],
      logoUrl:window._CONFIG['bigScreenLogoUrl'],
      pageList: [],
      linkList: [],
      activePage: '',
      menuVisible: false,
      menuItemList: [
        {
          key: '4',
          icon: 'reload',
          text: '刷 新',
        },
        {
          key: '1',
          icon: 'arrow-left',
          text: '关闭左侧',
        },
        {
          key: '2',
          icon: 'arrow-right',
          text: '关闭右侧',
        },
        {
          key: '3',
          icon: 'close',
          text: '关闭其它',
        },
      ],
      menusArr: [],
      reloadFlag: true,
      currentPage: '',
      date: new Date(), //实时时间
      date2: new Date(), //实时时间
      userName: '',
      systemType: window._CONFIG['system_Type'],
      platformType: window._CONFIG['platform_Type'],
      sysRoute: 'bigscreen/index',
      onePtmFlag: false,
      lockReconnect: false,
      heartCheck: null,
      reTimer: null,
    }
  },

  /* update_end author:wuxianquan date:20190828 for: 关闭当前tab页，供子页面调用->望菜单能配置外链，直接弹出新页面而不是嵌入iframe #428 */
  computed: {
    // multipage() {
    //   //判断如果是手机模式，自动切换为单页面模式
    //   if (this.isMobile()) {
    //     return false
    //   } else {
    //     return this.$store.state.app.multipage
    //   }
    // },
    textWidth() {
      // 创建临时元素来获取文本宽度
      const tempEl = document.createElement('span')
      tempEl.style.fontSize = this.fontSize + 'px'
      tempEl.style.visibility = 'hidden'
      tempEl.innerHTML = this.title
      document.body.appendChild(tempEl)
      const width = tempEl.offsetWidth
      document.body.removeChild(tempEl)
      return width
    }
  },
  filters: {
    formatDate: function (value) {
      var date = new Date(value)
      var hours = padDate(date.getHours())
      var minutes = padDate(date.getMinutes())
      var seconds = padDate(date.getSeconds())
      //整理好的数据返回出去
      return hours + ':' + minutes + ':' + seconds
    },
    formatDate2: function (value) {
      var date = new Date(value)
      var year = date.getFullYear()
      var month = padDate(date.getMonth() + 1)
      var day = padDate(date.getDate())
      //整理好的数据返回出去
      return year + '/' + month + '/' +day
    },
  },
  watch: {
    $route: function (newRoute) {
      // let breadcrumbs = newRoute.matched
      this.getCurrentPage(newRoute.matched)
      // this.activePage = newRoute.fullPath
      if (!this.multipage) {
        this.linkList = [newRoute.fullPath]
        this.pageList = [Object.assign({}, newRoute)]
        // update-begin-author:taoyan date:20200211 for: TASK #3368 【路由缓存】首页的缓存设置有问题，需要根据后台的路由配置来实现是否缓存
      } else if (indexKey == newRoute.fullPath) {
        //首页时 判断是否缓存 没有缓存 刷新之
        if (newRoute.meta.keepAlive === false) {
          this.routeReload()
        }
        // update-end-author:taoyan date:20200211 for: TASK #3368 【路由缓存】首页的缓存设置有问题，需要根据后台的路由配置来实现是否缓存
      } else if (this.linkList.indexOf(newRoute.fullPath) < 0) {
        this.linkList.push(newRoute.fullPath)
        this.pageList.push(Object.assign({}, newRoute))
        //// update-begin-author:sunjianlei date:20200103 for: 如果新增的页面配置了缓存路由，那么就强制刷新一遍 #842
        // if (newRoute.meta.keepAlive) {
        //   this.routeReload()
        // }
        //// update-end-author:sunjianlei date:20200103 for: 如果新增的页面配置了缓存路由，那么就强制刷新一遍 #842
      } else if (this.linkList.indexOf(newRoute.fullPath) >= 0) {
        let oldIndex = this.linkList.indexOf(newRoute.fullPath)
        let oldPositionRoute = this.pageList[oldIndex]
        this.pageList.splice(
          oldIndex,
          1,
          Object.assign({}, newRoute, {
            meta: oldPositionRoute.meta,
          })
        )
      }
    },

    multipage: function (newVal) {
      if (this.reloadFlag) {
        if (!newVal) {
          this.linkList = [this.$route.fullPath]
          this.pageList = [this.$route]
        }
      }
    },
  },
  created() {
    this.onePtmFlag = Vue.ls.get(ONE_PLATFORM_FLAG)
    if (this.$store.state.user.sysTypeMenu.length > 0) {
      this.menusArr = this.$store.state.user.sysTypeMenu
      let firstMenu = this.menusArr[0]
      this.sysRoute = firstMenu.children && firstMenu.children.length > 0 ? firstMenu.children[0].path : firstMenu.path
      this.matchRoutePath()
      this.initRoute()
    } else {
      sessionStorage.setItem(PLATFORM_TYPE, 4)
      this.GetPermissionList(4).then((res) => {
        let menus = res.result.menu
        this.menusArr = menus[0].children
        if (window._CONFIG['system_Type'] === '1') {
          this.saveSysTypeMenu(this.menusArr)
        }
        let firstMenu = this.menusArr[0]
        this.sysRoute =
          firstMenu.children && firstMenu.children.length > 0 ? firstMenu.children[0].path : firstMenu.path
        this.matchRoutePath()
        this.initRoute()
      })
    }
  },
  mounted() {
    let pro__Login_Username = window.localStorage.getItem('pro__Login_Username')
    this.userName = pro__Login_Username.match(/"value":"(\S*)","expire"/)[1]

    this.timer = setInterval(() => {
      this.date = new Date()
    }, 1000)
    this.initWebSocket()
  },
  beforeDestroy: function () {
    clearTimeout(this.reTimer)
    this.reTimer = null
    if (this.timer) {
      clearInterval(this.timer) //在vue示例销毁前，清除我们的定时器
    }
  },
  methods: {
    logoClick() {
      let st = window._CONFIG['system_Type']
      if (st === '2') {
        getAction('/sys/permission/getUserPlatformTypeByToken').then((res) => {
          if (res.success && res.result) {
            let tem = res.result.split(',')
            getAction('/sys/permission/platformTypeList').then((sres) => {
              if (sres.success && sres.result) {
                let plat = sres.result[0].value
                if (tem.includes(plat)) {
                  this.entrancePlanning(plat)
                } else {
                  this.entrancePlanning(tem[0])
                }
              }
            })
          }
        })
      }else if (st === '0'){
        this.$router.push({
          path: '/gateway'
        })
      }
      else {
        this.entrancePlanning(1)
      }
    },
    ...mapActions(['Logout', 'GetPermissionList', 'saveSysTypeMenu']),
    ...mapGetters(['nickname']),

    handleLogout() {
      const that = this
      this.$confirm({
        title: '提示',
        okText: '确定',
        cancelText: '取消',
        content: '真的要注销登录吗 ?',
        onOk() {
          return that
            .Logout({})
            .then(() => {
              // update-begin author:wangshuai date:20200601 for: 退出登录跳转登录页面
              that.$router.push({
                path: '/user/login',
              })
              // update-end author:wangshuai date:20200601 for: 退出登录跳转登录页面
              //window.location.reload()
            })
            .catch((err) => {
              that.$message.error({
                title: '错误',
                description: err.message,
              })
            })
        },
        onCancel() {},
      })
    },
    getSysRoute(sysRoute) {
      //system_Type配置为0,且用户模块权限非单，左上角点击跳转至模块选择页面
      if (window._CONFIG['system_Type'] === '0' && !this.onePtmFlag) {
        this.sysRoute = '/gateway'
      }
      //system_Type配置非0，左上角点击跳转至数据中心第一个菜单
      else {
        this.sysRoute = sysRoute
      }
    },
    initRoute() {
      // 复制一个route对象出来，不能影响原route
      let currentRoute = Object.assign({}, this.$route)
      currentRoute.meta = Object.assign({}, currentRoute.meta)
      // update-begin-author:sunjianlei date:20191223 for: 修复刷新后菜单Tab名字显示异常
      let storeKey = 'route:title:' + currentRoute.fullPath
      let routeTitle = this.$ls.get(storeKey)
      if (routeTitle) {
        currentRoute.meta.title = routeTitle
      }
      // update-end-author:sunjianlei date:20191223 for: 修复刷新后菜单Tab名字显示异常
      this.pageList.push(currentRoute)
      this.linkList.push(currentRoute.fullPath)
      this.getCurrentPage(this.$route.matched)
      // this.activePage = currentRoute.fullPath
    },
    //
    matchRoutePath() {
      let rPath = this.$route.path
      for (let i = 0; i < this.menusArr.length; i++) {
        let item = this.menusArr[i]
        if (rPath === item.path) {
          if (item.children && item.children.length > 0) {
            this.$router.push({
              path: item.children[0].path,
            })
            return
          }
        }
      }
    },
    entrancePlanning(index) {
      sessionStorage.setItem(PLATFORM_TYPE, index)
      const that = this
      that.GetPermissionList(index).then((res) => {
        if (res == 1) {
          return
        }
        const menuData = res.result.menu
        var redirect = ''
        if (menuData && menuData.length > 0) {
          if (menuData[0].children && menuData[0].children.length > 0) {
            redirect = menuData[0].children[0].path
          } else {
            redirect = menuData[0].path
          }
        } else {
          return
        }

        let constRoutes = []
        if (index === 4) {
          constRoutes = generateBigscreenRouter(menuData)
        } else {
          constRoutes = generateIndexRouter(menuData)
        }
        // 添加主界面路由
        store
          .dispatch('UpdateAppRouter', {
            constRoutes,
          })
          .then(() => {
            // 根据roles权限生成可访问的路由表
            // 动态添加可访问路由表
            router.addRoutes(store.getters.addRouters)
            this.$router.push({
              path: redirect,
            })
          })
      })
    },
    // update-begin-author:sunjianlei date:20200120 for: 动态更改页面标题
    getCurrentPage(breadcrumbs) {
      //生成头部面包屑
      this.currentPage = ''
      for (let i = 2; i < breadcrumbs.length; i++) {
        if (this.currentPage) {
          this.currentPage = this.currentPage + '/' + breadcrumbs[i].meta.title
        } else {
          this.currentPage = breadcrumbs[i].meta.title
        }
      }
    },
    //update-begin-author:taoyan date:20191008 for:路由刷新
    routeReload() {
      this.reloadFlag = false
      let ToggleMultipage = 'ToggleMultipage'
      this.$store.dispatch(ToggleMultipage, false)
      this.$nextTick(() => {
        this.$store.dispatch(ToggleMultipage, true)
        this.reloadFlag = true
      })
    },
  },
}
</script>

<style lang="less" scoped>
.bigScreenData {
  background: #222224;
  width: 100%;
  height: 100%;

  .header {
    width: 100%;
    height: 7%;
    background-image: linear-gradient(#242328, #292933);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-image: url('../../assets/bigScreen/23.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;

    .left {
      width: 70%;
      height: 100%;

      .logo {
        height: 100%;
        // width: 36%;
        display: flex;
        align-items: center;

        // justify-content: space-between;
        .logo-left {
          width: 25.7%;
          display: flex;
          justify-content: center;
          align-items: center;
          // margin-left: 0.4rem /* 32/80 */;
          color: #fffeff;

          a,
          div {
            display: flex;
            align-items: center;

            img {
              width: 0.4125rem /* 33/80 */;
              height: 0.4125rem /* 33/80 */;
            }

            span {
              margin-left: 0.25rem /* 20/80 */;
              letter-spacing: 4px;
              font-size: 0.275rem /* 22/80 */;
              color: #fffeff;
            }
          }
        }

        .logo-right {
          height: 100%;
          width: 300px;
          // margin-left: 2.125rem /* 170/80 */;
          background-image: -webkit-linear-gradient(bottom, #06bdf6, #00fcf7);
          -webkit-background-clip: text;

          -webkit-text-fill-color: transparent;
          display: flex;
          align-items: center;
          font-size: 0.225rem /* 18/80 */;
          font-weight: 600;
          letter-spacing: 0.075rem /* 6/80 */;
          padding-top: 0.375rem /* 30/80 */;
          margin-left: 9%;
        }
      }
    }

    .right {
      display: flex;
      height: 60%;
      white-space: nowrap;
      margin-right: 0.825rem /* 66/80 */;
      margin-top: 0.275rem /* 22/80 */;
      align-items: center;

      .sysRoute {
        font-size: 0.3rem /* 24/80 */;
        color: #fff;
        font-family: 'zhengku';
        height: 100%;
        margin-right: 0.375rem /* 30/80 */;
        cursor: pointer;
      }

      .time {
        font-size: 0.3rem /* 24/80 */;
        color: #fff;
        // font-weight: 600;
        // display: flex;
        // align-items: center;
        font-family: 'zhengku';
        height: 100%;
        width: 120px;
        //text-align: end;
        text-align: start;
      }

      .time2 {
        color: #fff;
        font-size: 0.2rem /* 16/80 */;
        margin-left: 0.175rem /* 14/80 */;
        // display: flex;
        // align-items: center;
        line-height: 0.575rem /* 46/80 */;
        height: 100%;
      }

      .return {
        margin-left: 0.35rem /* 28/80 */;
        display: flex;
        align-items: center;
        height: 100%;

        img {
          width: 0.2125rem /* 17/80 */;
          height: 0.225rem /* 18/80 */;
        }
      }

      .name {
        color: #fff;
        font-size: 0.175rem /* 14/80 */;
        margin-left: 0.375rem /* 30/80 */;
        display: flex;
        align-items: center;
        height: 100%;
      }

      .exit {
        margin-left: 0.3rem;
        cursor: pointer;
        font-size: 0.3rem;
        color: #ffffff;
      }
    }
  }

  .sidebar {
    width: 100%;
    height: 93%;
    display: flex;

    .sidebar-left {
      width: 70px /* 70/80 */;
      height: calc(100% - 32px);
      margin: 16px 0 0 16px /* 16/80 */;
      background: #111217;
      color: #fff;
      display: flex;
      flex-direction: column;
      padding-top: 0.375rem /* 30/80 */;
      overflow-y: auto;
      &::-webkit-scrollbar {
        display: none;
      }
      .sidebar-left-item {
        padding: 0.075rem /* 6/80 */ 0;

        div {
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 14%;

          a {
            display: flex;
            width: 0.425rem /* 34/80 */;
            height: 0.625rem /* 50/80 */;
            align-items: center;
            justify-content: center;

            img {
              width: 0.475rem /* 38/80 */;
              height: 0.475rem /* 38/80 */;
            }
          }
        }

        .sidebar-left-item-select {
          background: #040406;
        }

        a {
          color: #818181;
        }
      }
    }

    .sidebar-right {
      width: calc(100% - 86px);
      height: 100%;
      display: flex;

      // align-items: center;
      .main {
        width: 100%;
        height: 100%;
      }
    }
  }
}

.noselect {
  color: #818181;
}

.Select {
  color: #00c7ff;
}

// /deep/ .el-popover {
//   min-width: 84px !important;
//   background: #000 !important;
//   border: none !important;
//   a:hover {
//     color: #00c7ff !important;
//   }
// }

// /deep/ .el-popper[x-placement^='right'] .popper__arrow {
//   border-right-color: #222224 !important;
// }
// /deep/ .el-popper[x-placement^='right'] .popper__arrow::after {
//   border-right-color: #222224 !important;
// }

#components-layout-demo-top-side-2 .logo {
  width: 120px;
  height: 31px;
  background: rgba(255, 255, 255, 0.2);
  margin: 16px 28px 16px 0;
  float: left;
}

/*
 * The following styles are auto-applied to elements with
 * transition="page-transition" when their visibility is toggled
 * by Vue.js.
 *
 * You can easily play with the page transition by editing
 * these styles.
 */

.page-transition-enter {
  opacity: 0;
}

.page-transition-leave-active {
  opacity: 0;
}

.page-transition-enter .page-transition-container,
.page-transition-leave-active .page-transition-container {
  -webkit-transform: scale(1.1);
  transform: scale(1.1);
}

/*美化弹出Tab样式*/
.ant-tabs-nav-container {
  margin-top: 4px;
}

/* 修改 ant-tabs 样式 */
.tab-layout-tabs.ant-tabs {
  border-bottom: 1px solid #ccc;
  border-left: 1px solid #ccc;
  background-color: white;
  padding: 0 20px;

  .ant-tabs-bar {
    margin: 4px 0 0;
    border: none;
  }
}

.tab-layout-tabs.ant-tabs {
  &.ant-tabs-card .ant-tabs-tab {
    padding: 0 24px !important;
    background-color: white !important;
    margin-right: 10px !important;

    .ant-tabs-close-x {
      width: 12px !important;
      height: 12px !important;
      opacity: 0 !important;
      cursor: pointer !important;
      font-size: 12px !important;
      margin: 0 !important;
      position: absolute;
      top: 36%;
      right: 6px;
    }

    &:hover .ant-tabs-close-x {
      opacity: 1 !important;
    }
  }
}

.tab-layout-tabs.ant-tabs.ant-tabs-card > .ant-tabs-bar {
  .ant-tabs-tab {
    border: none !important;
    border-bottom: 1px solid transparent !important;
  }

  /*.ant-tabs-tab-active {
      border-color: @primary-color!important;
    }*/
}

// .tooltip{
//   position: relative;
//   left: 0.625rem /* 50/80 */;
//   bottom: 0.5rem /* 40/80 */;
//   z-index: 10;
//   width: 1.1rem /* 88/80 */;
//   height: 1.325rem /* 106/80 */;
//   padding: 0.15rem /* 12/80 */ 0.2rem /* 16/80 */;
//   background: skyblue;
// }
</style>