<template>
  <a-modal :title="title"
           :width="modalWidth"
           :visible="visible"
           :confirmLoading="confirmLoading"
           @cancel="handleCancel"
           @ok="handleOk"
           okText=""
           cancelText="关闭"
           wrapClassName="ant-modal-cust-warp"
           :centered="true">
    <a-form :form="form">
      <!-- <div style="text-align:center;font-size:18px;">修改资产状态</div> -->

      <!-- 不同状态不同内容 -->
      <div style="margin-left: 74px;">
        <a-row>
          <a-col>
            <a-form-item label="资产状态："
                         :labelCol="{ span: 5 }"
                         :wrapperCol="{ span: 12 }">
              <a-select placeholder="请选择"
                        @change="handleChange"
                        defaultValue="已下发"
                        :allowClear="true">
                <a-select-option :value="index"
                                 v-for="(item, index) in AssetStatusData"
                                 :key="index">{{item}}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row v-if="ContentFlag == 0">
          <a-col>
            <a-form-item label="下发到"
                         :labelCol="{ span: 5 }"
                         :wrapperCol="{ span: 12 }">
              <a-input placeholder="请输入"
                       :allowClear="true"
                       required></a-input>
            </a-form-item>
          </a-col>
          <a-col>
            <a-form-item label="下发时间"
                         :labelCol="{ span: 5 }"
                         :wrapperCol="{ span: 12 }">
              <a-date-picker @change="onChange"
                             style="width: 100%;" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row v-else-if="ContentFlag == 1">
          <a-col>
            <a-form-item label="单位"
                         :labelCol="{ span: 5 }"
                         :wrapperCol="{ span: 12 }">
              <a-input placeholder="请输入"
                       :allowClear="true"
                       required></a-input>
            </a-form-item>
          </a-col>
          <a-col>
            <a-form-item label="部署日期"
                         :labelCol="{ span: 5 }"
                         :wrapperCol="{ span: 12 }">
              <a-date-picker @change="onChange"
                             style="width: 100%;" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row v-else-if="ContentFlag == 2">
          <a-col>
            <a-form-item label="使用人"
                         :labelCol="{ span: 5 }"
                         :wrapperCol="{ span: 12 }">
              <a-input placeholder="请输入"
                       :allowClear="true"
                       required></a-input>
            </a-form-item>
          </a-col>
          <a-col>
            <a-form-item label="使用日期"
                         :labelCol="{ span: 5 }"
                         :wrapperCol="{ span: 12 }">
              <a-date-picker @change="onChange"
                             style="width: 100%;" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row v-else-if="ContentFlag == 3">
          <a-col>
            <a-form-item label="暂存到"
                         :labelCol="{ span: 5 }"
                         :wrapperCol="{ span: 12 }">
              <a-input placeholder="请输入"
                       :allowClear="true"
                       required></a-input>
            </a-form-item>
          </a-col>
          <a-col>
            <a-form-item label="暂存日期"
                         :labelCol="{ span: 5 }"
                         :wrapperCol="{ span: 12 }">
              <a-date-picker @change="onChange"
                             style="width: 100%;" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row v-else-if="ContentFlag == 4">
          <a-col>
            <a-form-item label="转移扶贫到"
                         :labelCol="{ span: 5 }"
                         :wrapperCol="{ span: 12 }">
              <a-input placeholder="请输入"
                       :allowClear="true"
                       required></a-input>
            </a-form-item>
          </a-col>
          <a-col>
            <a-form-item label="转移日期"
                         :labelCol="{ span: 5 }"
                         :wrapperCol="{ span: 12 }">
              <a-date-picker @change="onChange"
                             style="width: 100%;" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row v-else-if="ContentFlag == 5">
          <a-col>
            <a-form-item label="销毁方式"
                         :labelCol="{ span: 5 }"
                         :wrapperCol="{ span: 12 }">
              <a-input placeholder="请输入"
                       :allowClear="true"
                       required></a-input>
            </a-form-item>
          </a-col>
          <a-col>
            <a-form-item label="销毁日期"
                         :labelCol="{ span: 5 }"
                         :wrapperCol="{ span: 12 }">
              <a-date-picker @change="onChange"
                             style="width: 100%;" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row v-else-if="ContentFlag == 6">
          <a-col>
            <a-form-item label="维修原因"
                         :labelCol="{ span: 5 }"
                         :wrapperCol="{ span: 12 }">
              <a-input placeholder="请输入"
                       :allowClear="true"
                       required></a-input>
            </a-form-item>
          </a-col>
          <a-col>
            <a-form-item label="维修开始"
                         :labelCol="{ span: 5 }"
                         :wrapperCol="{ span: 12 }">
              <a-date-picker @change="onChange"
                             style="width: 100%;" />
            </a-form-item>
          </a-col>
          <a-col>
            <a-form-item label="维修结束"
                         :labelCol="{ span: 5 }"
                         :wrapperCol="{ span: 12 }">
              <a-date-picker @change="onChange"
                             style="width: 100%;" />
            </a-form-item>
          </a-col>
        </a-row>
      </div>
    </a-form>
  </a-modal>
</template>
<script>
import { ajaxGetDictItems } from '@api/api'
export default {
  name: 'AssetsManagementEdit',
  data() {
    return {
      title: '操作',
      visible: false,
      confirmLoading: false,
      /* 弹框宽 */
      modalWidth: '27%',
      form: this.$form.createForm(this),
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      AssetStatusData: [],
      ContentFlag: 0
    }
  },
  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      this.model = Object.assign({}, record)
      this.visible = true
      //编辑页面禁止修改角色编码
      if (this.model.id) {
        this.roleDisabled = true
      } else {
        this.roleDisabled = false
      }
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let formData = Object.assign(this.model, values)
          // let ids = formData.id
          // let remarkss =  formData.remarks
        }
      })
    },
    handleCancel() {
      this.close()
    },
    handleChange(value) {
      this.ContentFlag = value
    },
    //字典获取状态--------------------------------------------
    getDicData() {
      this.getAssetStatus('asset_status')
    },
    getAssetStatus(code) {
      let that = this
      ajaxGetDictItems(code, null).then(res => {
        let temp = res.result
        for (let i = 0; i < temp.length; i++) {
          that.AssetStatusData.push(temp[i].title)
        }
      })
    },
    //字典获取状态-------------------------------------end----

    //日期
    onChange(date, dateString) {
    }
  },

  mounted() {
    this.getDicData()
  }
}
</script>
<style scoped>
.ant-form-item {
  margin-bottom: 12px !important;
}
</style>
