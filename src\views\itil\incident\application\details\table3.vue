<template>
  <a-table
    :columns="columns"
    :data-source="data"
    ref="table"
    size="middle"
    bordered
    rowKey="id"
  >
  </a-table>
</template>
<script>
export default {
  // 关联配置项
  name: 'table1',
  data() {
    return {
        columns: [
            {
                title: '序号',
                dataIndex: '',
                    key: 'rowIndex',
                width: 60,
                    align: 'center',
                customRender: function(t, r, index) {
                    return parseInt(index) + 1
                 }
            },
            {
                title: '分类',
                align: 'center',
                dataIndex: 'classify'
            },
            {
                title: '编号',
                align: 'center',
                dataIndex: 'uri'
            },
            {
                title: '名称',
                align: 'center',
                dataIndex: 'name'
            },
            {
                title: '状态',
                align: 'center',
                dataIndex: 'status'
            }
        ],
        data: [
            {
                id: '01',
                classify: '终端',
                uri: 'PZ202009100001',
                name: '台式电脑-124',
                status: '使用'
            },
            {
                id: '02',
                classify: '终端',
                uri: 'PZ202009100001',
                name: '台式电脑-124',
                status: '使用'
            },
        ]
    }
  }
}
</script>
<style scoped></style>
