<template>
  <div class="body">
    <!-- 搜索区域 -->
    <div class="header">
      <div class="header-left-time">
        <span>选择日期:</span>
        <div class="time-range-span">
          <a-range-picker
            v-model="searchDate"
            @change="onTimeChange"
            format="YYYY-MM-DD"
            :defaultPickerValue="defaultPickerValue"
            :disabledDate="disabledDate"
            class="a-range-picker-choice-date"
          ></a-range-picker>
        </div>
      </div>
      <button class="header-search" @click="searchQuery">查询</button>
      <button class="header-search" style="color: #747679;background:none;" @click="searchReset">重置</button>
    </div>

    <div class="top">
      <a-row :gutter="24" style="height: 100%">
        <a-col :xs="24" :sm="24" :md="24" :lg="24" :xl="10" :xxl="8" style="height: 100%">
          <div class="left">
            <div class="left-top border" style="padding-right:0.2rem;">
              <!-- 告警数据统计 -->
              <div class="title">告警数据统计</div>
              <alarm-data-statistics ref="alarmDataStatistics" class="wrapper" :timeData="timeData"></alarm-data-statistics>
            </div>
            <div class="left-bottom border">
              <!-- 历史告警区域分布 -->
              <div class="title">机房告警统计</div>
              <room-alarm-statistics ref="roomAlarmStatistics" class="wrapper" :timeData="timeData"></room-alarm-statistics>
            </div>
          </div>
        </a-col>
        <a-col :xs="24" :sm="24" :md="24" :lg="24" :xl="14" :xxl="16" style="height: 100%;padding-right:15px !important;">
          <div class="right border" style="padding-bottom:0.3rem;">
            <!-- 历史告警 -->
            <div class="title">
              历史告警
              <div class="more" @click="openHistoryAlarmModal()">
                更多
                <a-icon type="right" />
              </div>
            </div>
            <history-alarm
              ref="historyAlarm"
              class="wrapper"
              :timeData="timeData"
              style="overflow-y:auto"
            ></history-alarm>
          </div>
        </a-col>
      </a-row>
    </div>
    <div class="bottom">
      <a-row :gutter="24" style="height: 100%">
        <!-- <div class="bottom"> -->
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="6" style="height: 100%" class="one">
          <div class="bottom-one border2">
            <!-- 告警级别分布 -->
            <div class="title">告警级别分布</div>
            <div class="wrapper">
              <alarm-level-distribution ref="alarmLevelDistribution" :timeData="timeData"></alarm-level-distribution>
            </div>
          </div>
        </a-col>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="6" style="height: 100%" class="two">
          <div class="bottom-one border2">
            <!-- 告警级别趋势分析 -->
            <div class="title">告警级别趋势分析</div>
            <div class="wrapper">
              <alarm-levels-trend ref="alarmLevelsTrend" :timeData="timeData"></alarm-levels-trend>
            </div>
          </div>
        </a-col>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="6" style="height: 100%" class="three">
          <div class="bottom-one border2">
            <!-- 告警分组统计 -->
            <div class="title" style="padding-bottom:0;">告警分组统计</div>
            <div class="wrapper" style="height: calc(100% - 0.31rem);">
              <alarm-group ref="alarmGroup" :timeData="timeData"></alarm-group>
            </div>
          </div>
        </a-col>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="6" style="height: 100%" class="four">
          <div class="bottom-one border2">
            <!-- 告警最长持续时间top -->
            <div class="title">告警最长持续时间top</div>
            <div class="wrapper">
              <alarm-time-top ref="alarmTimeTop" :timeData="timeData"></alarm-time-top>
            </div>
          </div>
        </a-col>
        <!-- 历史告警弹框 -->
        <history-alarm-modal ref="historyAlarmModal" class="wrapper" :timeData="timeData"></history-alarm-modal>
      </a-row>
    </div>
  </div>
</template>
<script>
import moment from 'moment'
import AlarmDataStatistics from './modules/AlarmDataStatistics.vue'
import AlarmGroup from './modules/AlarmGroup.vue'
import AlarmLevelDistribution from './modules/AlarmLevelDistribution.vue'
import AlarmLevelsTrend from './modules/AlarmLevelsTrend.vue'
import AlarmTimeTop from './modules/AlarmTimeTop.vue'
import HistoryAlarm from './modules/HistoryAlarm.vue'
import RoomAlarmStatistics from './modules/RoomAlarmStatistics.vue'
import HistoryAlarmModal from './modules/HistoryAlarmModal.vue'
export default {
  name: 'monitorOverview',
  components: {
    AlarmDataStatistics,
    AlarmGroup,
    AlarmLevelDistribution,
    AlarmLevelsTrend,
    AlarmTimeTop,
    HistoryAlarm,
    RoomAlarmStatistics,
    HistoryAlarmModal
  },
  data() {
    return {
      queryParam: {
        startTime: null,
        endTime: null
      },
      timeData: {},
      searchDate: [],
      defaultPickerValue: [
        moment(new Date())
          .subtract(1, 'months')
          .format('YYYY-MM-DD'),
        moment(new Date()).format('YYYY-MM-DD')
      ]
    }
  },
  methods: {
    disabledDate(current) {
      return current > moment().subtract(0, 'day') //今天之后的年月日不可选，不包括今天
    },
    onTimeChange(date, dateString) {
      //查询质保开始时间范围
      this.queryParam.startTime = dateString[0]
      this.queryParam.endTime = dateString[1]
    },
    openHistoryAlarmModal() {
      this.$refs.historyAlarmModal.visible = true
      this.$refs.historyAlarmModal.open()
    },
    searchQuery() {
      this.timeData = {
        startTime: this.queryParam.startTime,
        endTime: this.queryParam.endTime
      }
    },
    searchReset() {
      this.searchDate = []
      this.queryParam = {
        startTime: null,
        endTime: null
      }
      this.timeData = {
        startTime: null,
        endTime: null
      }
    }
  }
}
</script>
<style scoped lang="less">
.border {
  background-color: #fff;
  padding: 0.25rem 0.25rem 0.2rem 0.25rem;
}
.border2 {
  background-color: #fff;
  padding: 0.25rem 0 0 0.25rem;
}
.wrapper {
  height: calc(100% - 0.48rem);
}

.body {
  width: 100%;
  height: 100%;
  min-height: 800px !important;
  overflow-x: hidden;
  overflow-y: auto;
  .header {
    width: 100%;
    height: 7%;
    display: flex;
    align-items: center;
    margin-bottom: 0.1875rem;

    .header-left-time {
      font-size: 0.175rem /* 14/80 */;
      font-family: PingFang SC;
      letter-spacing: 0px;
      color: rgba(0, 0, 0, 0.65);
      display: flex;
      align-items: center;
      white-space: nowrap;
      margin-left: 0.25rem;

      .time-range-span {
        // width: 2.975rem /* 238/80 */;
        margin-right: 0.4375rem /* 35/80 */;
        margin-left: 0.2rem /* 16/80 */;
      }
    }

    .header-search {
      width: 0.9125rem;
      height: 0.35rem;
      margin-right: 0.25rem;
      /* 20/80 */
      background: #1e3674;
      border-radius: 4px;
      border: 1px solid #dcdfe6;
      color: #fff;
      cursor: pointer;
    }
  }
  .title {
    color: #252631;
    font-size: 0.2rem; //16/80
    font-weight: bold;
    padding-bottom: 0.15rem;
  }
  .top {
    width: 100%;
    height: 58.5%;
    .left {
      width: 100%;
      height: 100%;

      .left-top {
        width: 100%;
        height: 45.9%;
        margin-bottom: 0.1875rem;
      }
      .left-bottom {
        width: 100%;
        height: calc(54.1% - 0.1875rem);
        background-color: #fff;
        position: relative;
      }
    }
  }
  .right {
    width: 100%;
    height: 100%;
    .title {
      position: relative;
    }
    .more {
      font-size: 0.175rem; //14/80
      font-weight: 400;
      color: #909399;
      position: absolute;
      right: 0;
      top: 0;
      cursor: pointer;
      z-index: 99;
    }
  }
  .bottom {
    height: calc(100% - 7% - 58.5% - 15px - 15px);
    .bottom-one {
      width: 100%;
      height: 100%;
      background: #fff;
    }
  }
}
@media (min-width: 1200px) {
  .bottom {
    .one {
      width: 22.1%;
    }
    .two {
      width: 26%;
    }
    .three {
      width: 24%;
    }
    .four {
      width: calc(27.9% - 15px);
    }
  }
}

::v-deep .ant-col {
  padding-left: 15px !important;
  padding-right: 0 !important;
  margin-bottom: 15px !important;
}
</style>