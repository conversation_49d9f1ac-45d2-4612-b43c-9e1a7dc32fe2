<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll zxw">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <!-- 查询区域 -->
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <div class="table-page-search-wrapper-style">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="任务名称">
                  <a-input
                    placeholder="请输入任务名称"
                    :allowClear="true"
                    autocomplete="off"
                    v-model="queryParam.taskName" :maxLength="50"
                  ></a-input>
                </a-form-item>
              </a-col>

              <a-col :span="colBtnsSpan()">
                <span
                  class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                >
                  <a-button type="primary" @click="searchQuery" class="btn-search-style">查询</a-button>
                  <a-button @click="searchReset" style="margin-left: 10px" class="btn-reset-style">重置</a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <!-- 查询区域-END -->

      <a-card :bordered="false" style="flex: auto" class="core">
        <!-- 操作按钮区域 -->
        <div class="table-operator" style="display: flex; flex-flow: row nowrap;justify-content: space-between;align-items: center">
          <a-row style="width: 100%;">
            <a-col :sm="16" :xs="24">
              <a-button @click="handleAdd">新增</a-button>
              <a-dropdown v-if="selectedRowKeys.length > 0">
                <a-menu slot="overlay" style='text-align: center'>
                  <a-menu-item key="1" @click="batchDel">删除</a-menu-item>
                </a-menu>
                <a-button> 批量操作
                  <a-icon type="down" />
                </a-button>
              </a-dropdown>
            </a-col>
            <a-col :sm="8" :xs="24" class="radio-group">
              <a-radio-group default-value="all" :options="taskClassification"
                             @change="changeTaskClassification"></a-radio-group>
            </a-col>
          </a-row>
        </div>

        <!-- table区域-begin -->
        <a-table
          ref="table"
          bordered
          :rowKey="(record,index)=>record.id"
          :columns="columns"
          :dataSource="dataSource"
          :pagination="ipagination"
          :loading="loading"
          :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          class="j-table-force-nowrap"
          @change="handleTableChange"
        >
          <template slot="htmlSlot" slot-scope="text">
            <div v-html="text"></div>
          </template>
          <template slot="imgSlot" slot-scope="text">
            <span v-if="!text" style="font-size: 14px">无图片</span>
            <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width: 80px; font-size: 14px" />
          </template>
          <template slot="fileSlot" slot-scope="text">
            <span v-if="!text" style="font-size: 14px">无文件</span>
            <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
              下载
            </a-button>
          </template>
          <template slot="taskStatus" slot-scope="text,record">
            <span style="color: #fff;border-radius:3px;font-size: 12px;padding: 2px 6px" :style="{backgroundColor:record.statusColor}">{{text}}</span>
          </template>
          <template slot="plannedTime" slot-scope="text,record">
            <span>{{record.plannedStartTime}} ~ {{record.plannedEndTime}}</span>
          </template>
          <template slot="actualTime" slot-scope="text,record">
            <span v-if="record.actualStartTime">{{record.actualStartTime}}<span>~ {{record.actualEndTime ? record.actualEndTime:'暂无'}}</span></span>
            <span v-else>暂无</span>
          </template>
          <span class='caozuo' slot='action' slot-scope='text, record'>
            <a @click='handleDetailPage(record)'>查看</a>
            <span>
              <a-divider type="vertical" />
              <a-dropdown>
                <a class="ant-dropdown-link">更多<a-icon type="down" /></a>
                <a-menu slot="overlay" style="text-align: center">
                  <a-menu-item @click='handleDetailPage(record,1)'>
                    查看终端
                  </a-menu-item>
                  <a-menu-item @click='confirmDelete(record.id)'>
                    删除
                  </a-menu-item>
                  <a-menu-item v-if="record.taskStatus==1" @click="pause(record)">
                    暂停
                  </a-menu-item>
                  <a-menu-item v-if="record.taskStatus==2" @click="resume(record)">
                    恢复
                  </a-menu-item>
                   <a-menu-item v-if="record.taskStatus!=3&&record.taskStatus!=4" @click="terminate(record)">
                   停止
                  </a-menu-item>
                </a-menu>
              </a-dropdown>
            </span>
          </span>
          <template slot="tooltip" slot-scope="text">
            <a-tooltip placement="topLeft" :title="text" trigger="hover">
              <div class="tooltip">
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
      </a-card>

      <task-modal ref="modalForm" @ok="modalFormOk"></task-modal>
    </a-col>
  </a-row>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import taskModal from './modules/taskModal'
import JSuperQuery from '@/components/jeecg/JSuperQuery.vue'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
import { getAction ,postAction} from '@api/manage'
import { getDictData, setStatusColor } from '@views/opmg/taskManagement/modules/comFunctions'
export default {
  name: 'softwareTaskList',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components: {
    taskModal,
    JSuperQuery
  },
  data() {
    return {
      description: '软件任务管理页面',
      disableMixinCreated: true,
      queryParam: {
        taskState: 'all'
      },
      taskClassification: [
        {
          label: '全部',
          value: 'all'
        },
        {
          label: '执行中',
          value: 'running'
        },
        {
          label: '历史',
          value: 'history'
        }
      ],
      statusList:[],
      columns: [
        {
          title: '序号',
          dataIndex: '',
          fixed: 'left',
          key: 'rowIndex',
          width: 60,
          customRender: function(t, r, index) {
            return parseInt(index) + 1
          }
        },
        {
          title: '任务名称',
          dataIndex: 'taskName'
        },
        {
          title: '任务类型',
          dataIndex: 'taskType_dictText'
        },
        {
          title: '计划起止时间',
          dataIndex: 'plannedStartTime',
          customCell: () => {
            let cellStyle = 'width:310px'
            return { style: cellStyle }
          },
          scopedSlots: {
            customRender: 'plannedTime'
          }
        },
       /* {
          title: '计划结束时间',
          dataIndex: 'plannedEndTime',

        },*/
        {
          title: '实际起止时间',
          dataIndex: 'actualStartTime',
          customCell: () => {
            let cellStyle = 'max-width:310px'
            return { style: cellStyle }
          },
          scopedSlots: {
            customRender: 'actualTime'
          }
        },
        /*{
          title: '实际结束时间',
          dataIndex: 'actualEndTime'
        },*/

       /* {
          title: '升级包名称',
          dataIndex: 'softwarePatchId_dictText',
          customRender: function (t,r) {
            return !t ? '' : t+"_"+r.softwarePatchVersion
          },
        },*/
        {
          title: '下发策略',
          dataIndex: 'policyId_dictText'
        },
        {
          title: '执行状态',
          dataIndex: 'taskStatus_dictText',
          scopedSlots: {
            customRender: 'taskStatus'
          }
        },
       /* {
          title: '通知方式',
          dataIndex: 'noticeType_dictText'
        },*/
      /*  {
          title: '通知内容',
          dataIndex: 'noticeContext',
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 100px;max-width:250px'
            return { style: cellStyle }
          },
          scopedSlots: {
            customRender: 'tooltip'
          }
        },*/
        {
          title: '操作',
          dataIndex: 'action',
          fixed: 'right',
          align: 'center',
          width: 140,
          scopedSlots: {
            // filterDropdown: 'filterDropdown',
            // filterIcon: 'filterIcon',
            customRender: 'action'
          }
        }
      ],
      url: {
        list: '/software/softwareTask/list',
        pause: '/software/softwareTask/pause',
        resume: '/software/softwareTask/resume',
        terminate: '/software/softwareTask/terminate',
        delete: '/software/softwareTask/delete',
        deleteBatch: '/software/softwareTask/deleteBatch'
      }
    }
  },
  created() {
    getDictData('softwareTaskStatus').then((res)=>{
      if (res.length>0) {
       this.statusList= setStatusColor(res)
      }
    })
  },
  computed: {},
  activated() {
    this.$nextTick(()=>{
      this.loadData()
    })
  },
  methods: {
    loadData(arg) {
      if (!this.url.list) {
        this.$message.error('请设置url.list属性!')
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1
      }
      var params = this.getQueryParams() //查询条件
      this.loading = true
      getAction(this.url.list, params).then((res) => {
        if (res.success && res.result) {
          this.dataSource = res.result.records || res.result
          if (this.dataSource.length < 9) {
            this.clientHeight = false
          }
          if (this.dataSource.length>0){
            this.dataSource.map((r) => {
              let m=this.statusList.filter(item=>{
                return item.key==r.taskStatus
              })
              r['statusColor']=m[0].color
            })
          }
          this.ipagination.total =res.result.total?res.result.total:0
        }
        if (res.code === 510) {
          this.$message.warning(res.message)
        }
        this.loading = false
      })
    },
    pause(record){
      postAction(this.url.pause,{id:record.id}).then(res => {
        if (res.success ) {
          this.loadData()
        }else {
          this.$message.warning(res.message)
        }
      }).catch((err)=>{
        this.$message.error(err.message)
      })
    },
    resume(record){
      postAction(this.url.resume,{id:record.id}).then(res => {
        if (res.success ) {
          this.loadData()
        }else {
          this.$message.warning(res.message)
        }
      }).catch((err)=>{
        this.$message.error(err.message)
      })
    },
    terminate(record){
      postAction(this.url.terminate,{id:record.id}).then(res => {
        if (res.success ) {
          this.loadData()
        }else {
          this.$message.warning(res.message)
        }
      }).catch((err)=>{
        this.$message.error(err.message)
      })
    },
    /*任务查看*/
    handleDetailPage: function (record,index=3) {
      this.$parent.pButton2(index, record)
    },
    changeTaskClassification(e) {
      this.queryParam.taskState=e.target.value
      this.loadData(1)
    },
  }
}
</script>
<style lang="less" scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
.radio-group{
  height: 32px;
  line-height: 40px;
}
@media (max-height: 499px) {
  .radio-group{
    text-align: left;
  }
}
@media (min-height: 500px) {
  .radio-group{
    text-align: right;
  }
}
</style>
