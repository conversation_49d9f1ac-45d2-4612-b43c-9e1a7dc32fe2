<template>
  <a-spin :spinning="confirmLoading">
    <div class="colorBox">
      <span class="colorTotal">设备参数</span>
    </div>
    <a-table
      v-if='columns&&columns.length>0'
      style='overflow: hidden'
      ref='table'
      :columns='columns'
      :dataSource='dataSource'
      :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
      :loading='loading'
      :pagination='ipagination'
      :row-key='(record, index) => {return index}'
      bordered
      @change='handleTableChange'>
      <span v-for="item in titleSlots" :key="item.code" :slot="item.code">
        <a-tooltip>
          <template slot="title">
            {{ item.code }}
          </template>
          {{ item.name }}
        </a-tooltip>
      </span>
    </a-table>
  </a-spin>
</template>

<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
export default {
  name: 'StateDetailInfoForm',
  mixins: [JeecgListMixin],
  components: {},
  data() {
    return {
      form: this.$form.createForm(this),
      disableMixinCreated: true,
      stateInfos: null,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 7 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 14 },
      },
      confirmLoading: false,
      columns: [], //表格列
      titleSlots: [], //表头
    }
  },
  methods: {
    edit(record) {
      let that = this
      that.$nextTick(() => {
        that.stateInfos = record
        that.columns = []
        that.dataSource = []
        if (that.stateInfos && that.stateInfos.value[0] && that.stateInfos.value[0].value) {
          let list = that.stateInfos.value[0].value
          for (let i = 0; i < list.length; i++) {
            let trData = {}
            if (list[i].length&&list[i].length>0){
              //定义列字段columns
              if (i===0){
                let newList=list[i].sort((a,b)=>{
                  return b.serial-a.serial<0
                })
                for (let k = 0; k < newList.length; k++) {
                  let colItem = {
                    // title:newList[k].name,
                    dataIndex: newList[k].code,
                    slots: { title: newList[k].code },
                    ellipsis: true,
                    customHeaderCell: () => ({ style: { textAlign: 'center' } }), //头部单元格水平居中
                    customCell: (record, rowIndex) => {
                      let cellStyle = `text-align: center;max-width:400px`
                      return {
                        style: cellStyle
                      }
                    }
                  }
                  that.columns.push(colItem)
                  this.titleSlots.push({
                    name: newList[k].name,
                    code: newList[k].code,
                  })
                }
              }
              //获取列数据
              for (let k = 0; k < list[i].length; k++){
                trData[list[i][k].code] = list[i][k].value + list[i][k].unit
                trData.serial=list[i][k].serial
                trData.type=list[i][k].type
              }
              that.dataSource.push(trData)
            }
          }
        }        
      })
    },
    handleTableChange(pagination, filters, sorter) {
      //分页、排序、筛选变化时触发
      //TODO 筛选
      if (Object.keys(sorter).length > 0) {
        this.isorter.column = sorter.field
        this.isorter.order = 'ascend' == sorter.order ? 'asc' : 'desc'
      }
      this.ipagination = pagination
    }
  }
}
</script>

<style lang="less" scoped="scoped">
@import '~@assets/less/scroll.less';

.colorBox {
  margin-bottom: 18px;
}
.colorTotal {
  padding-left: 7px;
  border-left: 4px solid #1e3674;
}
</style>
