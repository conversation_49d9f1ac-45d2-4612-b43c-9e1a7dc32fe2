<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-item label="软件名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['softwareName', formValidator.softwareName]" :allowClear="true" autocomplete="off"
                placeholder="请输入软件名称"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item class="two-words" label="描述" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-textarea v-decorator="['softwareDescribe', formValidator.softwareDescribe]" :allowClear="true"
                autocomplete="off"  :autoSize="{ minRows: 2, maxRows: 6 }" placeholder="请输入描述"></a-textarea>
            </a-form-item>
          </a-col>
          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
  import {
    httpAction,
    getAction
  } from '@/api/manage'
  import pick from 'lodash.pick'
  import {
    validateDuplicateValue
  } from '@/utils/util'
  import JFormContainer from '@/components/jeecg/JFormContainer'
  import {
    duplicateCheck
  } from '@/api/api'
  import JDate from '@/components/jeecg/JDate'

  export default {
    name: 'DevopsSoftwareInfoForm',
    components: {
      JFormContainer,
      JDate,
    },
    props: {
      //流程表单data
      formData: {
        type: Object,
        default: () => {},
        required: false,
      },
      //表单模式：true流程表单 false普通表单
      formBpm: {
        type: Boolean,
        default: false,
        required: false,
      },
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false,
      },
    },
    data() {
      return {
        form: this.$form.createForm(this),
        model: {},
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          },
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          },
        },
        confirmLoading: false,
        validatorRules: {},
        softwareId: '',
        formValidator: {
          softwareName: {
            rules: [
              {required: true,message: '请输入软件名称',  trigger: 'blur'},
              {validator: this.validateSoftwareName,  trigger: 'blur'},
              {min: 2,max: 50,message: '软件名称长度在2到50个字符',trigger: 'blur'},
            ],
          },
          softwareDescribe: {
            rules: [
              {max: 255,message: '描述长度不能超过255个字符',trigger: 'blur'}],
          }
        },
        url: {
          add: '/software/devopsSoftwareInfo/add',
          edit: '/software/devopsSoftwareInfo/edit',
          queryById: '/software/devopsSoftwareInfo/queryById',
        },
      }
    },
    computed: {
      formDisabled() {
        if (this.formBpm === true) {
          if (this.formData.disabled === false) {
            return false
          }
          return true
        }
        return this.disabled
      },
      showFlowSubmitButton() {
        if (this.formBpm === true) {
          if (this.formData.disabled === false) {
            return true
          }
        }
        return false
      },
    },
    created() {
      //如果是流程中表单，则需要加载流程表单data
      this.showFlowData()
    },
    methods: {
      add() {
        this.edit({})
      },
      edit(record) {
        this.softwareId = record.id
        this.form.resetFields()
        this.model = Object.assign({}, record)
        this.visible = true
        this.$nextTick(() => {
          this.form.setFieldsValue(
            pick(
              this.model,
              'softwareName',
              'softwareDescribe'
              // 'createBy',
              // 'createTime',
              // 'updateBy',
              // 'updateTime',
              // 'sysOrgCode'
            )
          )
        })
      },
      //渲染流程表单数据
      showFlowData() {
        if (this.formBpm === true) {
          let params = {
            id: this.formData.dataId
          }
          getAction(this.url.queryById, params).then((res) => {
            if (res.success) {
              this.edit(res.result)
            }
          })
        }
      },
      validateSoftwareName(rule, value, callback) {
        var params = {
          tableName: 'devope_software_info',
          fieldName: 'software_name',
          fieldVal: value,
          dataId: this.softwareId,
        }
        duplicateCheck(params).then((res) => {
          if (res.success) {
            callback()
          } else {
            callback('软件名已存在!')
          }
        }).catch((err)=>{
          callback(err.message)
        })
      },
      submitForm() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.model.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'put'
            }
            let formData = Object.assign(this.model, values)
            httpAction(httpurl, formData, method)
              .then((res) => {
                if (res.success) {
                  that.$message.success(res.message)
                  that.$emit('ok')
                } else {
                  that.$message.warning(res.message)
                }
              })
              .finally(() => {
                that.confirmLoading = false
              })
          }
        })
      },
      popupCallback(row) {
        this.form.setFieldsValue(
          pick(row, 'softwareName', 'softwareDescribe', 'createBy', 'createTime', 'updateBy', 'updateTime',
            'sysOrgCode')
        )
      },
    },
  }
</script>
<style lang="less" scoped>
  ::v-deep .two-words>div>label {
    letter-spacing: 4px;
  }

  ::v-deep .two-words>div>label::after {
    letter-spacing: 0px;
  }
</style>