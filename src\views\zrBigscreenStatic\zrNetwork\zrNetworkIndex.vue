<template>
  <div id='zrNetworkView' ref='zrNetwork'>
    <div class='zr-network-topo'>
      <div class='zr-network-topo-l' style='width: calc(100% - 24%)'>
        <div class='top-info'></div>
        <div id='topoBox'>
        </div>
        <div class='status-legend'>
          <zr-focus-info scene='network'></zr-focus-info>
          <div class='legend-block-list'>
            <div class='legend-block-list-item' v-for='item in unitLevels' :key='"v_"+item.value'>
              <div v-if='item.value===1' class='legend-block-list-item-icon level-circle'></div>
              <div v-if='item.value===2' class='legend-block-list-item-icon level-rect'></div>
              <div v-if='item.value===3' class='legend-block-list-item-icon level-diamond'></div>
              <div class='level-legend-list-item-text'>{{ item.label }}</div>
            </div>
            <div class='legend-block-list-item' v-for='item in businessStatus' :key='item.value'>
              <div class='legend-block-list-item-icon' :style='{backgroundColor:item.color}'></div>
              <div class='legend-block-list-item-text'>{{ item.label }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class='zr-network-topo-r' style='width: 24%'>
        <zr-network-nodes :nodes='renderNodes'></zr-network-nodes>
      </div>
    </div>
    <!-- 提示弹窗组件 -->
    <zr-tool-tip
      :left='tipLeft'
      :top='tipTop'
      :width='tipWidth'
      :height='tipHeight'
      :tipData='tipData'
      :visible='tipShow'
      :tipZindex='tipZindex'
      :status='businessStatus'
      @hide='toolTipHide'
      @cellClick='cellClick'
      @enterTipCard='enterTipCard'
      @leaveTipCard='leaveTipCard'
    ></zr-tool-tip>
  </div>
</template>
<script>
import { Graph, Model } from '@antv/x6'
import Hierarchy from '@antv/hierarchy'
import { organizations, departtLinks } from '../modules/zrOrganizations'
import { businessStatus, unitLevels } from '../modules/zrUtil'
import ZrToolTip from '@views/zrBigscreenStatic/zrNetwork/modules/ZrToolTip.vue'
import ZrNetworkNodes from '@views/zrBigscreenStatic/zrNetwork/modules/ZrNetworkNodes.vue'
import ZrTopoEventMixin from '@views/zrBigscreenStatic/zrNetwork/modules/ZrTopoEventMixin'
import resizeObserverMixin from '@views/statsCenter/com/resizeObserverMixin'
import { flatTreeData, darkenColor } from '@/utils/util'
import ZrFocusInfo from '@views/zrBigscreenStatic/zrCompNational/modules/ZrFocusInfo.vue'
import VisEdit from '@views/topo/nettopo/modules/VisEdit.vue'
import { getAction } from '@/api/manage'

export default {
  name: 'zrNetworkIndex',
  components: {
    ZrToolTip,
    ZrFocusInfo,
    ZrNetworkNodes,
    VisEdit
  },
  mixins: [ZrTopoEventMixin, resizeObserverMixin],
  data() {
    return {
      originResource: {},
      graph: null,
      businessStatus: JSON.parse(JSON.stringify(businessStatus)), // 深拷贝，防止修改原数据
      animates: {},
      unitLevels,
      rootNodes: [],
      circleIds: [],
      rectIds: [],
      departtLinks: [],
      renderOk: false, // 渲染状态
      renderNodes: {},
      topoLevel: 1,
      organizations
    }
  },
  created() {
    this.originResource = flatTreeData([organizations], null, [])
    this.businessStatus.map(el => {
      el.dcolor = darkenColor(el.color, 50) // 生成比状态颜色较暗的颜色
      return el
    })
  },
  mounted() {
    let tem = JSON.parse(JSON.stringify(organizations))
    this.renderNodes = this.filterTreeNodes([tem], node => node.level <= 3)[0]
    this.initNetworkTopo()
    this.nodeAnimate()
  },
  beforeDestroy() {
    this.destroyTopo()
  },
  methods: {
    // 过滤树形数据中不需要的节点
    filterTreeNodes(treeData, filterFn) {
      const filterNode = (node) => {
        // 深度优先遍历子节点
        if (node.children) {
          node.children = node.children
            .map(child => filterNode(child))
            .filter(Boolean) // 过滤掉返回null的节点
        }

        // 检查当前节点是否符合条件
        const keepNode = filterFn(node)

        // 如果节点符合条件或者有符合条件的子节点，则保留
        if (keepNode || (node.children && node.children.length >= 0)) {
          return node
        }

        return null // 不符合条件的节点将被过滤掉
      }
      return treeData.map(node => filterNode(node)).filter(Boolean)
    },

    //销毁拓扑图
    destroyTopo() {
      // 停止所有动画
      Object.values(this.animates).forEach(animateList => {
        animateList.forEach(animate => {
          if (animate && typeof animate === 'function') {
            animate() // 停止动画
          } else if (animate && animate.finish && animate.effect) {
            const timing = animate.effect.getComputedTiming()
            if (timing.iterations !== Infinity) {
              animate.finish() // 仅在迭代次数有限时停止动画
            } else {
              animate.cancel() // 对于无限迭代的动画，使用 cancel 方法
            }
          }
        })
      })
      this.animates = {} // 清空动画列表
      // 销毁图表实例
      if (this.graph) {
        this.graph.dispose()
        this.graph = null
      }
    },
    // 监听窗口大小变化
    resizeObserverCb() {
      // 监听窗口大小变化，重新设置拓扑图大小
      if (this.renderOk && this.graph) {
        this.destroyTopo() // 销毁之前的拓扑图
        this.initNetworkTopo() // 重新初始化拓扑图
        this.nodeAnimate()
      }
    },
    //初始化拓扑图
    initNetworkTopo() {
      this.graph = new Graph({
        container: document.getElementById('topoBox'),
        autoResize: true,
        panning: true,
        snapline: true,
        grid: {
          visible: false
        },
        connecting: {
          // connector: 'smooth'
        },
        // 防止拖动
        interacting: {
          nodeMovable: false
        },
        // 添加滚轮缩放功能
        mousewheel: {
          enabled: true,
          // modifiers: ['ctrl'],
          zoomAtMousePosition: true,
          minScale: 0.01,
          factor: 1.1
        }
      })
      //生成布局数据 在这里调整布局参数
      if (this.renderNodes.length === 0) return
      const result = Hierarchy.compactBox(this.renderNodes, {
        direction: 'V',//V
        getHeight() {
          return 40
        },
        getWidth() {
          return 80
        },
        getHGap() {
          return 4
        },
        getVGap() {
          return 8
        }
        // getSide: () => {
        //   return 'right'
        // },
      })
      const model = { nodes: [], edges: [] }
      //根据布局数据生成拓扑图节点
      const traverse = (data) => {
        if (data) {
          let node = this.createNode(data)
          model.nodes?.push(node)
        }
        if (data.children) {
          data.children.forEach((item) => {
            model.edges?.push({
              source: `${data.id}`,
              target: `${item.id}`,
              zIndex: 0,
              attrs: {
                line: {
                  stroke: '#fff',
                  strokeWidth: 0.5,
                  targetMarker: null
                }
              }
            })
            traverse(item)
          })
        }
      }
      traverse(result)
      this.graph.fromJSON(model)
      this.graph.zoomToFit({ padding: 30 })
      if (this.topoLevel === 1) {
        //创建中央节点包围盒
        this.createBoundingBox(this.graph, [this.renderNodes.id], {
          fill: 'rgba(77,182,255,0.5)',
          stroke: 'transparent',
          strokeWidth: 0,
          padding: 12,
          label: '中央节点',
          labelColor: '#fff',
          fontSize: 6,
          zIndex: -1
        })
        //创建一级节点的包围盒
        this.createBoundingBox(this.graph, this.circleIds, {
          fill: 'rgba(77,182,255,0.2)',
          stroke: 'transparent',
          strokeWidth: 0,
          padding: 12,
          label: '区域节点',
          labelColor: '#fff',
          fontSize: 6,
          zIndex: -1
        })
        let rootNode = this.graph.getCellById(this.renderNodes.id)
        let pos = rootNode.position()
        this.createAncestorsNode('JW_X_G', 'JW训G部', pos)
        //创建二级节点的包围盒
        /* this.createBoundingBox(this.graph, this.rectIds, {
           fill: 'rgba(77,182,255,0.1)',
           stroke: 'transparent',
           strokeWidth: 0.5,
           padding:12,
           label: '集团节点',
           labelColor: '#fff',
           fontSize: 6,
           zIndex: -1
         })*/
      } else {
        this.createDepLink()
        let rootNode = this.graph.getCellById(this.renderNodes.id)
        let pos = rootNode.position()
        let temnode = this.originResource.find(item => item.id === this.renderNodes.id)
        if (temnode) {
          let pNode = this.originResource.find(item => item.id === temnode.flatParentId)
          this.createAncestorsNode(pNode.id, pNode.name, pos)
        }
      }
      // console.log("rootNode === ",rootNode,pos)
      this.setup()
      this.renderOk = true // 渲染完成
    },
    //根据级别创建节点
    createNode(item) {
      if (item.data.level === 1) {
        this.circleIds.push(item.id)
        return this.createCircle(item)
      } else if (item.data.level === 2) {
        this.rectIds.push(item.id)
        return this.createRect(item)
      } else {
        return this.createDiamond(item)
      }
    },
    // 创建三级部门之间的连接线
    createDepLink() {
      departtLinks.forEach(item => {
        if (this.graph.getCellById(item.source) && this.graph.getCellById(item.target)) {
          this.graph.addEdge({
            source: item.source,
            target: item.target,
            zIndex: 0,
            router: {
              name: 'manhattan'
              // args: {
              //   startDirections: ['bottom'],
              //   endDirections: ['top'],
              // },
            },
            attrs: {
              line: {
                // stroke: '#fff',
                strokeWidth: 0.5,
                stroke: '#1890ff',
                strokeDasharray: 2,
                targetMarker: null
              }
            }
          })
        }
      })
    },
    //创建祖宗节点
    createAncestorsNode(id, name, pos) {
      let status = this.businessStatus.find(el => el.value === 0)
      this.graph.addNode({
        shape: 'circle',
        id: id,
        x: pos.x - 80,
        y: pos.y -60,
        width: 16,
        height: 16,
        label: name,
        attrs: {
          body: {
            fill: status.color,
            stroke: 'transparent',
            strokeWidth: 1,
            strokeDasharray: 1,
            opacity: 0.8
          },
          label: {
            refX: 0.5,
            refY: -6,
            textAnchor: 'middle',
            textVerticalAnchor: 'top',
            fontSize: 6,
            fill: '#ffffff'
          }
        },
        data: {
          root: true
        }
      })
      this.graph.addEdge({
        source: this.renderNodes.id,
        target: id,
        attrs: {
          line: {
            stroke: '#ffffff',
            strokeDasharray: 3,
            targetMarker: null,
            strokeWidth: 1,
            style: {
              animation: 'zr-network-dash-line 60s infinite linear'
            }
          }
        }
      })
    },
    // 创建矩形
    createRect(item) {
      let status = this.businessStatus.find(el => el.value === item.data.status)
      return {
        id: `${item.id}`,
        x: item.x + 0,
        y: item.y + 0,
        shape: 'rect',
        width: 12,
        height: 12,
        label: item.data.name,
        attrs: {
          body: {
            fill: status.color,
            stroke: 'transparent'
          },
          label: {
            refX: 0.5,
            refY: '100%',
            refY2: 2,
            textAnchor: 'middle',
            textVerticalAnchor: 'top',
            fontSize: 6,
            fill: '#fff'
          }
        },
        data: {
          ...item.data,
          color: status.color,
          dcolor: status.dcolor
        }
      }
    },
    //创建圆形
    createCircle(item) {
      let status = this.businessStatus.find(el => el.value === item.data.status)
      return {
        shape: 'circle',
        id: `${item.id}`,
        x: item.x + 0,
        y: item.y + 0,
        width: 16,
        height: 16,
        label: item.data.name,
        attrs: {
          body: {
            fill: status.color,
            stroke: 'transparent'
          },
          label: {
            refX: 0.5,
            refY: '100%',
            refY2: 2,
            textAnchor: 'middle',
            textVerticalAnchor: 'top',
            fontSize: 6,
            fill: '#fff'
          }
        },
        data: {
          ...item.data,
          color: status.color,
          dcolor: status.dcolor
        }
      }
    },
    //创建菱形
    createDiamond(item) {
      let status = this.businessStatus.find(el => el.value === item.data.status)
      return {
        shape: 'polygon',
        id: `${item.id}`,
        x: item.x + 0,
        y: item.y + 0,
        width: 12,
        height: 12,
        label: item.data.name,
        attrs: {
          body: {
            fill: status.color,
            // stroke: '#9254de',
            stroke: 'transparent',
            refPoints: '0,10 10,0 20,10 10,20'
          },
          label: {
            refX: 0.5,
            refY: '100%',
            refY2: 2,
            textAnchor: 'middle',
            textVerticalAnchor: 'top',
            fontSize: 5,
            fill: '#fff'
          }
        },
        data: {
          ...item.data,
          color: status.color,
          dcolor: status.dcolor
        }
      }
    },
    //给异常节点添加动画
    nodeAnimate() {
      // 节点动画
      const nodes = this.graph.getNodes()
      nodes.forEach((node) => {
        const view = this.graph.findView(node)
        let nodeData = node.getData()
        if (nodeData && nodeData.status !== undefined && nodeData.status !== 0 && view) {
          if (this.animates[node.id] === undefined) {
            // 如果已经存在动画，则先停止之前的动画
            this.animates[node.id] = []
          } else {
            // 停止之前的动画
            this.animates[node.id].forEach(animate => animate && animate())
          }
          let value = `${nodeData.color};${nodeData.dcolor};${nodeData.color}`
          // 主填充颜色动画
          let colorAni = view.animate(node.shape, {
            attributeType: 'XML',
            attributeName: 'fill',
            values: value,
            dur: '1.5s',
            repeatCount: 'indefinite'
          })
          this.animates[node.id].push(colorAni)
          const body = view.findOne('body')
          let scaleAni = body.animate([
            { transform: 'scale(1)' },
            { transform: 'scale(0.9)' },
            { transform: 'scale(1)' }
          ], {
            duration: 1500,
            iterations: Infinity
          })
          this.animates[node.id].push(scaleAni)
        }
      })
    },
    //创建包围盒的方法 根据传入的节点id 和 选项来创建一个矩形节点作为包围盒
    createBoundingBox(graph, nodeIds, options = {}) {
      if (!nodeIds || nodeIds.length === 0) return null

      // 获取所有选定节点
      const nodes = nodeIds.map(id => graph.getCellById(id)).filter(Boolean)
      if (nodes.length === 0) return null

      // 计算包围盒的边界
      let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity

      nodes.forEach(node => {
        const bbox = node.getBBox()
        minX = Math.min(minX, bbox.x)
        minY = Math.min(minY, bbox.y)
        maxX = Math.max(maxX, bbox.x + bbox.width)
        maxY = Math.max(maxY, bbox.y + bbox.height)
      })

      // 添加一些内边距
      const padding = options.padding || 20
      minX -= padding
      minY -= padding
      maxX += padding
      maxY += padding

      // 创建矩形节点
      const rect = graph.addNode({
        shape: 'rect',
        x: minX,
        y: minY,
        width: maxX - minX,
        height: maxY - minY,
        attrs: {
          body: {
            fill: options.fill || 'rgba(230, 230, 230, 0.5)',
            stroke: options.stroke || '#d9d9d9',
            strokeWidth: options.strokeWidth || 1,
            strokeDasharray: options.strokeDasharray || '5,5',
            rx: options.rx || 4,
            ry: options.ry || 4
          },
          label: {
            text: options.label || '',
            fill: options.labelColor || '#666',
            fontSize: options.fontSize || 12,
            refX: 0.5,
            refY: 5
          }
        },
        zIndex: options.zIndex || -1 // 确保矩形在节点下方
      })

      return rect
    }
  }
}
</script>
<style >
@keyframes zr-network-dash-line {
  to {
    stroke-dashoffset: -1000
  }
}
</style>
<style scoped lang='less'>
#zrNetworkView {
  padding: 0px 16px 0px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;

  .zr-network-topo {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: space-between;

    .zr-network-topo-l {
      position: relative;
      height: 100%;
      width: 75%;
      .top-info {
        height: 8px;
      }
      #topoBox {
        width: 100%;
        height: calc(100% - 50px - 8px);
        background: rgba(77, 182, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.1);
      }
    }

    .zr-network-topo-r {
      width: 25%;
      padding: 0 12px 0px
    }
  }
}

.status-legend {
  position: absolute;
  bottom: 0px;
  //right: 33px;
  width: 100%;
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  //width:100%;
  .legend-block-list {
    display: flex;

    .legend-block-list-item {
      display: flex;
      align-items: center;
      font-weight: 400;
      font-size: 14px;
      padding: 0 20px;
      color: #E3E7EF;

      .legend-block-list-item-icon {
        width: 14px;
        height: 14px;
        margin-right: 12px;
      }
    }

    .level-circle {
      border-radius: 50%;
      background-color: #ffffff;
    }

    .level-rect {
      width: 14px;
      height: 14px;
      background-color: #ffffff;
    }

    .level-diamond {
      width: 14px;
      height: 14px;
      transform: rotate(45deg);
      background-color: #ffffff;
    }
  }

}
</style>