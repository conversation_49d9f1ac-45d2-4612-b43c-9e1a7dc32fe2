<template>
  <div class="body">
    <div class="left">
      <div class="left-top"></div>
      <div class="left-bottom"></div>
    </div>
    <div class="core">
      <div class="core-top"></div>
      <div class="core-core" id="pieChart">
        <div class="core-core-left"></div>
        <div class="core-core-right"></div>
      </div>
      <div class="core-bottom" id="histogram"></div>
    </div>
    <div class="right">
      <div class="right-top"></div>
      <div class="right-core"></div>
      <div class="right-bottom"></div>
    </div>
  </div>
</template>
<script>
import echarts from 'echarts/lib/echarts'

import 'echarts/lib/component/graphic'
export default {
  data() {
    return {
      pieChartData: [
        { value: 34, name: '正常' },
        { value: 41, name: '一般告警' },
        { value: 25, name: '严重告警' },
      ],
    }
  },
  mounted() {
    this.pieChart()
    this.histogram()
  },
  methods: {
    pieChart() {
      let myChart = this.$echarts.init(document.getElementById('pieChart'))

      myChart.setOption({
        title: {
          text: '',
          left: 'center',
          textStyle: {
            color: '#41c1dc',
          },
        },
        tooltip: {
          trigger: 'item',
        },
        color: ['#04c5fc', '#498ae8', '#fdd400', 'red'],
        series: [
          {
            type: 'pie',
            radius: '50%',
            data: this.pieChartData,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)',
              },
            },
            labelLine: {
              normal: {
                position: 'inner',
                show: false,
              },
            },

            itemStyle: {
              normal: {
                label: {
                  show: false,
                },
                labelLine: {
                  show: false,
                },
              },
            },
          },
        ],
      })
    },

    histogram() {
      let myChart = this.$echarts.init(document.getElementById('histogram'))
      myChart.setOption({
        xAxis: {
          type: 'value',
          splitLine: { show: false }, //去除网格线
          show: false,
        },
        yAxis: {
          type: 'category',
          data: ['其他-192.168.1.1', '安全设备-JM', '应用服务器-tongweb', '操作系统linux', '服务器-192.168.1.1'],
          splitLine: { show: false }, //去除网格线
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false, //y轴线消失
          },
          axisLabel: {
            formatter: (value) => {
              if (value >= 10000) {
                value = value / 10000 + 'W'
              }
              if (value >= 1000) {
                value = value / 1000 + 'K'
              }
              return value
            },
          },
        },

        grid: {
          top: 48,
          left: 130, // 调整这个属性
          right: 50,
          bottom: 50,
        },
        series: [
          {
            data: [8.02, 15.5, 22.54, 28.32, 31.27],
            type: 'bar',
            showBackground: true,
            backgroundStyle: {
              color: 'rgba(180, 180, 180, 0.2)',//柱状图背景颜色
            },
            barWidth: 10, //柱图宽度
            itemStyle: {
              emphasis: {
                barBorderRadius: 30,
              },
              normal: {
                 barBorderRadius:[10, 10, 10, 10],
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                  {
                    offset: 0,
                    color: '#3679fb',
                  },
                  {
                    offset: 1,
                    color: '#0cf6f7',
                  },
                ]),
              },
            },
            backgroundStyle: {
              color: '#293e6d',
            },
          },
        ],
      })
    },
  },
}
</script>
<style lang="less" scoped>
.body {
  padding: 20px;
  width: 100%;
  height: 100%;
  display: flex;
  //   background: #222224;
  .left {
    width: 792px;
    height: 100%;
    .left-top {
      height: 642px;
      width: 792px;
    }
    .left-bottom {
      height: 280px;
      width: 792px;
      margin-top: 20px;
    }
  }
  .core {
    width: 472px;
    height: 100%;
    margin: 0 20px;
    .core-top {
    }
    .core-core {
      height: 346px;
      width: 100%;
    }
    .core-bottom {
      width: 100%;
      height: 280px;
    }
  }
  .right {
    height: 100%;
    width: 472px;
    background: yellow;
  }
}
</style>