<template>
  <j-modal :title='title' :width='width' :visible='visible' :destroyOnClose='true'
           :centered='true'
           :okButtonProps="{ style: { display: 'none' }}"
           switchFullscreen
           @cancel='handleCancel'
           cancelText='关闭'>
    <div>
      <a-tabs default-active-key='1' @change='tabChange'>
        <a-tab-pane key='1' tab='基本信息'>
          <a-descriptions :column='{ xxl: 2, xl: 2, lg: 2, md: 2, sm: 2, xs: 2 }' bordered>
            <a-descriptions-item
              v-for='item in zabbixInfo'
              :key='item.key'
              :label='item.name'>
              {{ item.transfer ? zabbixFields[item.key][record[item.key]] : record[item.key] }}
            </a-descriptions-item>
          </a-descriptions>
        </a-tab-pane>
        <a-tab-pane key='2' tab='监控项' force-render>
          <zabbix-monitors :record='record'></zabbix-monitors>
        </a-tab-pane>
        <a-tab-pane key='3' tab='告警列表'>
          <zabbix-host-alarm :record='record'></zabbix-host-alarm>
        </a-tab-pane>
      </a-tabs>
    </div>
  </j-modal>
</template>
<script>

import { ajaxGetDictItem } from '@api/api'
import { zabbixInfo, zabbixFields } from '@views/jkdjgl/model/zabbixFields'
import ZabbixMonitors from '@views/jkdjgl/model/ZabbixHostMonitors.vue'
import ZabbixHostAlarm from '@views/jkdjgl/model/ZabbixHostAlarm.vue'

export default {
  name: 'ZabbixHostDetail',
  components: { ZabbixHostAlarm, ZabbixMonitors },
  data() {
    return {
      title: '主机详情',
      width: '1000px',
      visible: false,
      disableSubmit: false,
      confirmLoading: false,
      record: {},
      datatypeList: [],
      zabbixInfo,
      zabbixFields
    }
  },
  created() {
  },
  mounted() {

  },
  computed: {},
  methods: {
    show(record) {
      this.record = record
      this.visible = true
    },
    handleCancel() {
      this.close()
    },
    handleOk() {
      this.close()
    },
    close() {
      this.visible = false
    },
    tabChange() {

    }
  }
}
</script>

<style scoped lang='less'>
@import '~@assets/less/normalModal.less';
</style>