<template>
  <j-modal
    title="查找"
    :width="width"
    :visible="visible"
    :destroyOnClose="true"
    :closable="true"
    :maskClosable="true"
    :footer="null"
    :maskStyle="{'animation':'none','background':'#fff','opacity':'0'}"
    @cancel="handleCancel"
    cancelText="关闭"
    v-drag-modal
  >
    <a-input-search v-model="keyword" placeholder="搜索内容" :maxLength="30" style="width: 100%" @change="check">
      <a-select
        slot="addonBefore"
        default-value="name"
        v-model="type"
        style="width: 100px"
        @change="changeType"
      >
        <a-select-option value="name">设备名称</a-select-option>
        <a-select-option value="ip">设备IP</a-select-option>
      </a-select>
    </a-input-search>
    <div class="content">
      <div v-if="list.length>0 && keyword">
        <div class="title">搜索结果({{list.length}})</div>
        <div class="result">
          <div v-for="(item,index) in list" :key="index" :class="[selectedIndex==index?'active':'', 'item']" @click="selectNode(item,index)">
            <a-icon type="gateway" class="icon" />
            <div class="name" v-if="item.data&&item.data.deviceName">{{item.data.deviceName}}</div>
            <div
              class="name"
              v-if="item.data&&item.data.deviceIp&&type=='ip'"
            >{{item.data.deviceIp}}</div>
          </div>
        </div>
      </div>
      <div v-else-if="loading && keyword" class="nodata">没有搜索到相关内容</div>
    </div>
  </j-modal>
</template>
<script>
import FlowGraph from '../graph'
import { simpleDebounce } from '@/utils/util'

export default {
  name: 'Search',
  data() {
    return {
      width: 400,
      visible: false,
      nodata: false,
      keyword: '',
      loading: false,
      type: 'name', // 查询类型:设备name，设备ip
      list: [], // 搜索结果列表
      sum: 0, // 搜索结果总数
      selectedIndex: -1 //默认选中搜素结果的的索引值
    }
  },
  methods: {
    handleCancel() {
      this.visible = false
      this.$parent.showSearchModal = false
      this.loading = false
      this.selectedIndex = -1
    },
    changeType() {
      this.list = []
      this.keyword = ''
    },
    onSearch: simpleDebounce(function() {
      this.search(this.keyword, this.type)
    }, 1000),
        // 查找功能
    search(val,type) {
      if (!val) {
        return
      }
      // 查找设备名称、设备ip
      const { graph } = FlowGraph
      let nodes = graph.getNodes();
      let list = []
      nodes.forEach(el=> {
        if (type=='name') { // 根据name查设备
          if (el.data.deviceName && el.data.deviceName.indexOf(val) > -1) {
            list.push(el)
          }
        } else if (type=='ip') {
          if (el.data.deviceIp && el.data.deviceIp.indexOf(val) > -1) {
            list.push(el)
          }
        }
      })
      this.loading = true
      this.list = list
    },
    selectNode(el,index) {
      this.selectedIndex = index
      const pos = el.position() // 获取选中的节点的位置
      const { graph } = FlowGraph
      graph.resetSelection(el) // 设置当前节点为选中状态
      graph.centerPoint(pos.x || 0, pos.y || 0) //  使指定的点与视口中心对齐
      graph.zoomTo(1)
    },
    check() {
      this.loading = false
      this.onSearch()
    }
  }
}
</script>
<style lang="less" scoped>
::v-deep .ant-modal {
  top: 70px;
}
.content {
  margin-top: 18px;
  .nodata {
    color: #aaa;
    font-size: 13px;
    margin-top: 18px;
    margin-left: 3px;
  }
  .result {
    margin-top: 10px;
    margin-left: 3px;
    max-height: 400px;
    overflow: auto;
    .title {
      color: #aaa;
      font-size: 13px;
      margin-bottom: 10px;
    }
    .item {
      display: flex;
      align-items: center;
      color: #000;
      font-size: 16px;
      padding: 5px 10px;
      cursor: pointer;
      .icon {
        margin-right: 15px;
      }
      .name {
        margin-right: 10px;
      }
      &.active {
        background-color: #f1f1f1;
      }
    }
  }
}
</style>