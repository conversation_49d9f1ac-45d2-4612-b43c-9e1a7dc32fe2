<template>
  <div class="ajax-options">
    <a-form-item :label="label">
      <a-button type="primary" @click="editParams" style="margin-bottom: 12px; width: 100%"> {{title}} </a-button>
    </a-form-item>
    <a-modal
      :title="title"
      :visible="visible"
      cancelText="关闭"
      :destroyOnClose="true"
      centered
      width="850px"
       @ok="visible = false"
      @cancel="visible = false"
    >
      <div class="json-box-9136076486841527" v-if="type === 'linkage'">
        <codemirror
          style="height: 100%"
          ref="myEditor"
          v-model="code"
          @change="codeChange"
        ></codemirror>
      </div>
       <div class="json-box-9136076486841527" v-else-if="type === 'update'">
        <codemirror
          style="height: 100%"
          ref="myEditor"
          v-model="code"
          :options="{
            mode: 'javascript',
            extraKeys: { 'Ctrl-Space': 'autocomplete' },
          }"
          @change="codeChange"
        ></codemirror>
       </div>
       <div class="json-box-9136076486841527" v-else-if="type === 'btn'">
        <codemirror
          style="height: 100%"
          ref="myEditor"
          v-model="code"
          :options="{
            mode: 'javascript',
            extraKeys: { 'Ctrl-Space': 'autocomplete' },
          }"
          @change="codeChange"
        ></codemirror>
       </div>
    </a-modal>
  </div>
</template>
<script>
import { codemirror } from 'vue-codemirror-lite'
export default {
  name: 'KAjaxOption',
  components: {
    codemirror,
  },
  props: {
    value: {
      type: String,
      required: true,
    },
    type: {
      type: String,
      default: 'linkage',
    },
  },
  data() {
    return {
      visible: false,
      code: '',
    }
  },
  created() {},
  mounted() {},
  watch: {
    value: {
      deep: false,
      immediate: true,
      handler() {
        this.code = this.value
      },
    },
  },
  computed:{
    title(){
      return {
        linkage:"编辑联动数据",
        update:"编辑update回调函数",
        btn:"编辑动态函数",
      }[this.type]
    },
    label(){
      return {
        linkage:"联动数据",
        update:"update回调",
        btn:"编辑动态函数",
      }[this.type]
    },
  },
  methods: {
    editParams(t) {
      this.visible = true
    },
    codeChange(e) {
      this.$emit('input', e)
    },
  },
}
</script>
<style lang="less" scoped>
::v-deep .CodeMirror {
  height: 100%;
}
</style>