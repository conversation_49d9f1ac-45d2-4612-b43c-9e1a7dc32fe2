<template>
  <div class='zr-national-status' ref='zrNationalNodes'>
    <div class='national-status-content' v-if='listData.length'>
      <slot>
        <vue-seamless-scroll v-if='srollOption.autoPlay' :data='listData' class='scroll-warp'
                             :class-option='srollOption'>
          <ul class='scroll-list'>
            <li class='scroll-list-item' v-for='(item, index) in listData'
                :key='index'>
              <div v-if='item.name === "运行状态"'>
                <span>{{item.name}}：</span>
                <span :style='{color:item.color}'>{{item.label}}</span>
              </div>
              <div v-else class='days-item' style='display: flex;justify-content: center'>
                <days-box :days='item.value' :label='item.name' :unit='"天"'></days-box>
              </div>
            </li>
          </ul>
        </vue-seamless-scroll>
      </slot>
    </div>
  </div>
</template>
<script>
import ZrBigscreenTitle from '@views/zrBigscreens/modules/ZrBigscreenTitle.vue'
import vueSeamlessScroll from 'vue-seamless-scroll'
import resizeObserverMixin from '@views/statsCenter/com/resizeObserverMixin'
import { StatusInfo, businessStatus } from '@/views/zrBigscreens/modules/zrUtil'
import {ajaxGetDictItems} from '@/api/api'
import DaysBox from './DaysBox.vue'
export default {
  name: 'ZrStatusInfo',
  components: { ZrBigscreenTitle, vueSeamlessScroll, DaysBox },
  mixins: [resizeObserverMixin],
  data() {
    return {
      listData: [],
      srollOption: {
        step: 0.8, // 步长
        speed: 100, // 滚动速度
        timer: 3000,// 滚动时间间隔
        autoPlay: true,
        limitMoveNum: 2,
        singleHeight: 60 ,
        waitTime: 5000,
      },
      maxNum: 0,
      colors:[
        '#55A7F4', // 正常
        '#F4A655', // 异常
        '#F45555'  // 故障
      ]
    }
  },
  created() {
    this.getStatusFromDict()
   /* this.listData = StatusInfo.filter(el=>el.show).map(item=>{
      if(item.name==='运行状态'){
        let stu = businessStatus.find(el=>el.value===item.value)
        if(stu){
          item.label = stu.label
          item.color = stu.color
        }
      }
      return item
    })*/
  },
  mounted() {

  },
  methods: {
    getStatusFromDict(){
      ajaxGetDictItems('ZR_BIGSCREEN_STATUS').then(res => {
          if(res.success){
            this.listData = res.result.filter(el=>Number(el.value)>=0).map(item => {
              let tem = {
                name: item.text,
                value: Number(item.value),
              }
              if(tem.name==='运行状态'){
                let stu = businessStatus.find(el=>el.value==item.value)
                if(stu){
                  tem.label = stu.label
                  tem.color = stu.color
                }
              }
              return tem
            })
          }
        // console.log("获取到的数据", this.listData)
      })
    },
    // 屏幕变化回调
    resizeObserverCb() {
    },
  }
}
</script>


<style scoped lang='less'>
.zr-national-status {
  height: 60px;
  .national-status-content {
    height: 100%;
    overflow: hidden;
  }
}
.scroll-warp {
  height: 100%;
  overflow: hidden;

  .scroll-list {
    width: 100%;
    height: 100%;
    margin: 0px;
    padding: 0px;
  }


}
.scroll-list-item {
  display: flex;
  align-items: center;
  color: rgba(237, 245, 255, 0.95);
  font-size: 24px;
  justify-content: center;
  height: 60px;
  font-weight: bold;
  line-height: 60px;
  letter-spacing: 3px;
}

</style>