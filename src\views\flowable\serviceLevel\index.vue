<template>
  <a-row :gutter="10" style="height: 100%;" class="vScroll">
    <a-col style="width:100%;height: 100%;display: flex;flex-direction: column">
      <!-- 查询区域 -->
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0', marginRight: '12px' }" class='card-style'>
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col v-show="getVisible('proInsName')" :span="spanValue">
                <a-form-item :label="getTitle('proInsName')">
                  <a-input :maxLength='maxLength' v-model="queryParam.proInsName" :allow-clear="true" autocomplete="off"
                    placeholder="请输入业务标题" />
                </a-form-item>
              </a-col>
               <a-col v-show="getVisible('taskName')" :span="spanValue">
                <a-form-item :label="getTitle('taskName')">
                  <a-input :maxLength='maxLength' v-model="queryParam.taskName" :allow-clear="true" autocomplete="off"
                    placeholder="请输入任务名称" />
                </a-form-item>
              </a-col>
              <a-col v-show="getVisible('taskCreateTime')" :span="spanValue">
                <a-form-item :label="getTitle('taskCreateTime')">
                  <a-range-picker :getCalendarContainer="node=> node.parentNode" style='width: 100%'
                    v-model='queryParam.searchStartTime' :placeholder="['开始时间', '结束时间']" format='YYYY-MM-DD HH:mm:ss'
                    showTime @change='onCreatedTimeChange' />
                </a-form-item>
              </a-col>
              <a-col v-show="getVisible('taskEndTime')" :span="spanValue">
                <a-form-item :label="getTitle('taskEndTime')">
                  <a-range-picker :getCalendarContainer="node=> node.parentNode" style='width: 100%'
                    v-model='queryParam.searchEndTime' :placeholder="['开始时间', '结束时间']" format='YYYY-MM-DD HH:mm:ss' showTime
                    @change='onEndTimeChange' />
                </a-form-item>
              </a-col>
              <a-col v-show="getVisible('assignee_dictText')" :span="spanValue">
                <a-form-item :label="getTitle('assignee_dictText')">
                  <a-select :getPopupContainer='node=>node.parentNode' :allow-clear='true' v-model="queryParam.assignee"
                    show-search placeholder="请选择执行人" option-filter-prop="children" :filter-option="filterOption">
                    <a-select-option v-for="(item, key) in userList" :key="key" :value="item.username">
                      <div style="display: inline-block; width: 70%" :title="item.realname">
                        {{ item.realname }}
                        <span style="font-size: 10px; color: rgba(0, 0, 0, 0.45);">{{
                            '(' + item.username + ')'
                          }}</span>
                      </div>

                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col v-show="getVisible('applyPerson_dictText')" :span="spanValue">
                <a-form-item :label="getTitle('applyPerson_dictText')">
                  <a-select :getPopupContainer='node=>node.parentNode' :allow-clear='true' v-model="queryParam.applyPerson"
                    show-search placeholder="请选择发起人" option-filter-prop="children" :filter-option="filterOption">
                    <a-select-option v-for="(item, key) in userList" :key="key" :value="item.username">
                      <div style="display: inline-block; width: 70%" :title="item.realname">
                        {{ item.realname }}
                        <span style="font-size: 10px; color: rgba(0, 0, 0, 0.45);">{{
                            '(' + item.username + ')'
                          }}</span>
                      </div>

                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <span class="table-page-search-submitButtons" style="overflow: hidden;">
                  <a-button icon="search" type="primary" @click="searchQuery">查询</a-button>
                  <a-button icon="reload" style="margin-left: 8px" @click="searchReset">重置</a-button>
                  <a v-if="queryItems.length>0" style="margin-left: 8px" @click="doToggleSearch">{{queryName}}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
        <!--自定义查询项 -->
        <div v-if="toggleSearchStatus" class="custom-query-item">
          <a-checkbox-group v-model="settingQueryItems" :defaultValue="settingQueryItems" style="width:100%"
            @change="onQuerySettingsChange">
            <a-row :gutter="24">
              <div v-for="(item,index) in queryItems" :key="index">
                <a-col v-show='item.checked' :span='querySpanValue' class='col-checkbox'>
                  <a-checkbox :disabled="item.disabled" :value="item.dataIndex">
                    <j-ellipsis :length="7" :value="item.title"></j-ellipsis>
                  </a-checkbox>
                </a-col>
              </div>
            </a-row>
          </a-checkbox-group>
        </div>
        <!-- 自定义查询项-END -->
      </a-card>
      <a-card :bordered="false" style="width: 100%; flex: auto">
        <a-table
          ref="table"
          bordered
          rowKey="id"
          :columns="columns"
          :dataSource="dataSource"
          :pagination="ipagination"
          :loading="loading"
          @change="handleTableChange"
        >
          <template slot="slaType" slot-scope="text, record">
            <div>{{record.slaType_dictText}}超时 {{record.overTime}}</div>
          </template>
          <template slot="tooltip" slot-scope="text">
            <a-tooltip placement="topLeft" :title="text" trigger="hover">
              <div class="tooltip">{{ text }}</div>
            </a-tooltip>
          </template>
          <span slot="action" slot-scope="text, record">
            <a @click="handleInfo(record)" style="color: #409eff">查看详情</a>
          </span>
        </a-table>
      </a-card>
      <!-- 一查看详情区域 -->
      <ProcessInstanceInfoModal ref="instanceInfoModalForm" @ok="modalFormOk" :showAddKnowledgeButton="false"></ProcessInstanceInfoModal>
    </a-col>
  </a-row>
</template>

<script>
import { getUserList } from '@/api/api'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { YqFormSeniorSearchLocation } from '@/mixins/YqFormSeniorSearchLocation'
import ProcessInstanceInfoModal from '../process-instance/modules/ProcessInstanceInfoModal'
export default {
  name: 'serviceLevelList',
  mixins: [JeecgListMixin, YqFormSeniorSearchLocation],
  components:{
    ProcessInstanceInfoModal
  },
  data() {
    return {
      maxLength:50,
      description: '服务水平列表',
      formItemLayout: {
        labelCol: {
          style: 'width:100px'
        },
        wrapperCol: {
          style: 'width:calc(100% - 100px)'
        }
      },
      // 表头
      columns: [
        {
          title: '序号',
          dataIndex: '',
          key: 'rowIndex',
          isUsed: false,
          customCell: () => {
            let cellStyle = 'text-align:center;width:60px'
            return {
              style: cellStyle
            }
          },
          customRender: function (t, r, index) {
            return parseInt(index) + 1
          },
        },
        {
          title: '业务标题',
          dataIndex: 'proInsName',
          isUsed: true,
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 150px;max-width:300px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '任务名称',
          dataIndex: 'taskName',
          isUsed: true,
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 150px;max-width:350px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '执行人',
          dataIndex: 'assignee_dictText',
          isUsed: true,
          customCell: () => {
            let cellStyle = 'text-align:center'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '发起人',
          dataIndex: 'applyPerson_dictText',
          isUsed: true,
          customCell: () => {
            let cellStyle = 'text-align:center'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: 'SLA服务目标',
          dataIndex: 'slaType_dictText',
          customCell: () => {
            let cellStyle = 'text-align:center'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'slaType',
          }
        },
        {
          title: '开始时间',
          dataIndex: 'taskCreateTime',
          isUsed: true,
          customCell: () => {
            let cellStyle = 'text-align:center;width:160px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '结束时间',
          dataIndex: 'taskEndTime',
          isUsed: true,
          customCell: () => {
            let cellStyle = 'text-align:center;width:160px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 147,
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: '/sla/process/list',
      },
      userList: []
    }
  },
  created() {
    this.getColumns(this.columns)
    this.getuserList()
  },
  methods: {
    getuserList() {
      let param = {
        pageSize: 10000
      }
      getUserList(param).then((res) => {
        if (res.success) {
          this.userList = res.result.records
        }
      })
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      )
    },
    onCreatedTimeChange: function (value, dateString) {
      this.queryParam.taskCreateTimeAfter = dateString[0]
      this.queryParam.taskCreateTimeBefore = dateString[1]
    },
    onEndTimeChange: function (value, dateString) {
      this.queryParam.taskEndTimeAfter = dateString[0]
      this.queryParam.taskEndTimeBefore = dateString[1]
    },
    //查看详情
    handleInfo(record) {
      if (!record.taskId) {
        this.$message.error('任务ID不存在')
        return
      }
      this.$refs.instanceInfoModalForm.init(record.proInsId,false)
      this.$refs.instanceInfoModalForm.title = '查看'
      this.$refs.instanceInfoModalForm.disableSubmit = false
    },
  }
}
</script>
<style lang='less' scoped>
  @import '~@assets/less/common.less';
  @import '~@assets/less/YQCommon.less';
</style>