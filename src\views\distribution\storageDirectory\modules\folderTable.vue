<template>
  <div>
    <div class="bread-crumb-box">
      <span @click="goBack" class="back"><a-icon class="icon" type="left"/></span>
      <span  v-if='bucketName'>
        <span @click="openBucket" class="action-box" :class="{'current-node':folderPath.length===0}">{{bucketName}} </span>
         <span v-for="(item,index) in folderPath" :key="'folderPath_'+index">
           <span> / </span>
           <span class="action-box" :class="{'current-node':index===folderPath.length-1}" @click="openFolder(item,index)">{{item}}</span>
         </span>
      </span>
    </div>
    <div class="table-page-search-wrapper">
      <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
      <a-row :gutter='24' ref='row'>
        <a-col :lg="12" :md="14" :sm="24" :xs="24">
          <a-form-item label='目录名称'>
            <a-input :maxLength='maxLength' placeholder='请输入目录名称' v-model='queryParam.searchWord' :allowClear='true' autocomplete='off' />
          </a-form-item>
        </a-col>
        <a-col :lg="4" :md="8" :sm="24" :xs="24">
          <span class='table-page-search-submitButtons'>
            <a-button type='primary' class='btn-search' @click='searchQuery'>查询</a-button>
            <a-button class='btn-reset btn-reset-style' @click='searchReset'>重置</a-button>
          </span>
        </a-col>
      </a-row>
    </a-form>
    </div>
    <div class='table-operator table-operator-style'>
      <a-button @click='handleAdd'>新增</a-button>
    </div>
    <a-table
      ref='table'
      bordered
      size="middle"
      :row-key='(record, index) => {return index}'
      :columns='columns'
      :dataSource='dataSource'
      :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
      :pagination='ipagination'
      :loading='loading'
      :rowSelection='{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }'
      @change='handleTableChange'>
      <span slot='filename' slot-scope='text, record'>
        <a v-if="record.isDir" @click='openTableFolder(text)' style="color:#409eff">
          <a-icon type="folder"  class="table-icon"/>
          {{text}}
        </a>
        <span v-else>
          <a-icon type="file" class="table-icon"/>
          {{text}}
        </span>
      </span>
      <span slot='action' class='caozuo' slot-scope='text, record'>
        <a v-if="record.isDir" @click='handleEdit(record.filename)'>编辑</a>
        <span v-else style="color:#e8e8e8 ">编辑</span>
        <a-divider type='vertical' />
        <a v-if="record.isDir" @click='handleDelete(record.filename)'>删除</a>
        <span v-else style="color:#e8e8e8 ">删除</span>
      </span>
      <template slot='tooltip' slot-scope='text'>
        <a-tooltip placement='topLeft' :title='text' trigger='hover'>
          <div class='tooltip'>
            {{ text }}
          </div>
        </a-tooltip>
      </template>
    </a-table>
   <add-directory-modal ref="modalForm" @ok="loadData"></add-directory-modal>
  </div>
</template>

<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { deleteAction, getAction } from '@api/manage'
import addDirectoryModal from '@views/distribution/storageDirectory/modules/addDirectoryModal.vue'
import GatewayMenus from '@comp/tools/GatewayMenus.vue'
export default {
  name: 'storageDirectoryList',
  props:{
    clusterId:{
      type:String,
      required:true
    },
    bucketName:{
      type:String,
      required:true
    }
  },
  mixins: [JeecgListMixin],
  components: {
    GatewayMenus,
    addDirectoryModal
  },
  data() {
    return {
      maxLength:50,
      formItemLayout: {
        labelCol: { style: 'width:80px' },
        wrapperCol: { style: 'width:calc(100% - 80px)' },
      },
      disableMixinCreated: true,
      folderPath:[],
      // 表头
      columns: [
        {
          title: '名称',
          dataIndex: 'filename',
          customCell: () => {
            let cellStyle = 'text-align: left'
            return {
              style: cellStyle
            }
          },
          scopedSlots: { customRender: 'filename' }
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 100,
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: '',
        query1:'/distributedStorage/list',
        query2:'/distributedStorage/search',
        delete: '/distributedStorage/delete',
        deleteBatch: ''
      }
    }
  },
  watch:{
    bucketName:{
      deep:true,
      immediate: true,
      handler(val){
        this.queryParam={}
        this.dataSource=[]
        this.folderPath=[]
        if (val){
          this.queryParam.clusterId = this.clusterId
          this.queryParam.bucketName=val
          this.url.list=this.url.query1
          this.loadData(1)
        }
      }
    }
  },
  created() {
  },
  methods: {
    searchQuery() {
      this.url.list =this.queryParam.searchWord&&this.queryParam.searchWord.trim()? this.url.query2:this.url.query1
      this.loadData(1)
    },
    searchReset() {
      delete this.queryParam.searchWord
      this.url.list=this.url.query1
      this.loadData(1)
    },
    goBack(){
      this.$emit('openClusterTable')
    },
    openBucket(){
      this.folderPath=[]
      delete this.queryParam.searchWord
      this.url.list=this.url.query1
      this.loadData(1)
      this.$emit('openBucket')
    },
    openFolder(filename,index){
      this.folderPath=this.folderPath.splice(0,index+1)
      delete this.queryParam.searchWord
      this.url.list=this.url.query1
      this.loadData(1)
    },
    loadData(arg) {
      if (!this.url.list) {
        this.$message.error('请设置url.list属性!')
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1
      }
      if (this.folderPath.length>0){
        this.queryParam.path=this.folderPath.join('/')
      }
      else {
        delete this.queryParam.path
      }
      var params = this.getQueryParams() //查询条件
      this.loading = true
      getAction(this.url.list, params).then((res) => {
        this.dataSource =[]
        if (res.success && res.result) {
         let data=res.result.records || res.result
          for (let i = 0; i < data.length; i++) {
            if (data[i].filename!==""){
              this.dataSource.push(data[i])
            }
          }
          if (this.dataSource.length < 9) {
            this.clientHeight = false
          }
          this.ipagination.total= this.dataSource.length
        }
        if (res.code === 510) {
          this.$message.warning(res.message)
        }
        this.loading = false
      })
    },
    openTableFolder(filename){
      this.folderPath.push(filename)
      this.url.list=this.url.query1
      delete this.queryParam.searchWord
      this.loadData(1)
    },
    handleDelete(fileName) {
      if (!this.url.delete) {
        this.$message.error('请设置url.delete属性!')
        return
      }
      var that = this
      let params={
        clusterId:this.queryParam.clusterId,
        bucketName:this.queryParam.bucketName,
        path:this.folderPath.length>0?this.folderPath.join('/')+'/'+fileName:fileName
      }
      deleteAction(that.url.delete, params).then((res) => {
        if (res.success) {
          //重新计算分页问题
          that.reCalculatePage(1)
          that.$message.success(res.message)
          that.loadData()
        } else {
          that.$message.warning(res.message)
        }
      })
    },
    handleAdd: function () {
      let param={
        clusterId:this.queryParam.clusterId,
        bucketName:this.queryParam.bucketName,
        pathArray:this.folderPath
      }
      this.$refs.modalForm.add(param);
      this.$refs.modalForm.title = '新增';
      this.$refs.modalForm.disableSubmit = false;
    },
    handleEdit: function (folderName) {
      let param={
        clusterId:this.queryParam.clusterId,
        bucketName:this.queryParam.bucketName,
        pathArray:this.folderPath,
        folderName:folderName,
      }
      this.$refs.modalForm.edit(param);
      this.$refs.modalForm.title = '编辑';
      this.$refs.modalForm.disableSubmit = false;
    },
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
.bread-crumb-box{
  margin-bottom: 16px;
  font-size: 18px;
  font-weight: 500;

  .back{
    display: inline-block;
    width: 32px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    margin-right: 8px;
    background: #fafafa;
    border:1px solid #e8e8e8;
    border-radius: 3px;
    cursor: pointer;
    .icon{
      color:#409eff;
    }

    &:hover{
      background:#409eff ;
      .icon{
        color: #fff;
      }
    }
  }
  .action-box{
    cursor: pointer;
  }
  .action-box:hover{
    cursor: pointer;
    color: #409eff;
  }
  .current-node{
    color: #000;
  }
}
.table-icon{
  font-size:18px;
  margin-right: 8px
}

</style>