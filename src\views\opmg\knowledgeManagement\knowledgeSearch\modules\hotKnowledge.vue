<template>
  <div :class="theme" v-if="hotKnowledgeList.length>0">
    <div class="HotKnowledge" >
      <div style="display: flex; align-items: center;">
        <slot name="prefixIcon"></slot>
        <div class="title">热门知识</div>
      </div>
      <div class="wrapper">
        <div
          v-for="(kitem, kindex) in hotKnowledgeList"
          :key="'kl_' + kindex"
          class="item"
          @click="goKnowledgePage(kitem)"
        >
          <div :class="['number', 'top' + kindex]" class="prefix">
            <!-- 显示小红旗图标--start -->
            <div v-if="showIcon">
              <img src="@/assets/img/hongqi.png" style="width: 14px; height: 18px; margin-top: 2px"
                   v-if="kindex == 0 || kindex == 1 || kindex == 2" />
              <div style="width: 14px; height: 18px" v-else></div>
            </div>
            <!-- 显示小红旗图标--end -->

            <slot name="top"></slot>
            {{ kindex + 1 }}
          </div>
          <div class="name over" :title="kitem.title">{{ kitem.title }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { getAction } from '@/api/manage'
export default {
  data() {
    return {
      hotKnowledgeList: [], // 热门知识
      url: {
        getKnowledgesList: '/kbase/knowledges/pageView',
        checkAuth: '/kbase/knowledges/checkAuthorityById' // 判断是否有权限查看知识
      }
    }
  },
  props: {
    // 主题类型
    theme: {
      type: String,
      default: ''
    },
    showIcon: {
      type: Boolean,
      default: false
    }
  },
  mounted() {
    this.getKnowledgesList()
  },
  methods: {
    goKnowledgePage(item) {
      if (item.id) {
        // 跳转到热门知识
        // 判断知识是否存在
        getAction(this.url.checkAuth, { id: item.id })
          .then(res => {
            if (res.success) {
              this.$emit('goDetail', item)
            } else {
              this.$message.warning(res.message)
            }
          })
          .catch(err => {
            this.$message.warning(err.message)
          })
      }
    },
    // 热门知识列表
    getKnowledgesList() {
      getAction(this.url.getKnowledgesList).then(res => {
        if (res.success) {
          if (res.result.length > 10) {
            this.hotKnowledgeList = res.result.slice(0, 10)
          } else {
            this.hotKnowledgeList = res.result
          }
        }
      })
    }
  }
}
</script>
<style scoped lang="less">
/*****样式1：平台知识搜索门口页面热门知识样式******/
.theme1 {
  .HotKnowledge {
    .wrapper {
      .item{
        .prefix {
          width: 40px;
        }
        .name {
          width: calc(100% - 40px);
        }
      }
      .item:hover {
        background-color: #ecf5ff;
        .name{
          color: #315efb;
        }
      }
    }
  }
}
/*****样式2：运维助手知识搜索门户页面热门知识样式******/
.theme2 {
  @color:#fff;
  .HotKnowledge {
    .title {
      color:  @color;
      font-weight: normal;
      // font-size: 18px;
    }
    .wrapper {
      .item {
        color:  @color !important;

        .prefix {
          width: 55px;
        }

        .name {
          width: calc(100% - 55px);
        }
      }
      .item:hover{
        background-color: rgba(235, 235, 235, 0.2);
        .name{
          color: #66ffff;
        }
      }
      .number {
        color: #b9b9b9;

      }
    }
  }
}

/*****样式3:平台搜索结果页面右上角热门知识样式******/
.theme3 {
  .HotKnowledge {
    .title {
      font-size: 18px;
    }
    .wrapper {
      .item {
        color: rgba(0, 0, 0, 0.85) !important;
        .prefix{
          width: 16px;
        }
        .name{
          width: calc(100% - 16px);
        }
      }
      .item:hover{
        background-color: #ecf5ff;
        .name{
          color: #315efb;
        }
      }
      .number {
        color: #b9b9b9;
      }
    }
  }
}
/*****样式4:运维助手知识搜索结果页面右上角热门知识样式******/
.theme4 {
  @color:#fff;
  .HotKnowledge {
    .title {
      color:  @color;
      font-weight: normal;
      // font-size: 18px;
    }
    .wrapper {
      .item {
        color: @color !important;
        .prefix{
          width: 16px;
        }
        .name{
          width: calc(100% - 16px);
        }
      }
      .item:hover{
        background-color: rgba(235, 235, 235, 0.2);
        .name{
          color: #66ffff;
        }
      }
      .number {
        color: #b9b9b9;
      }
    }
  }
}
/*****热门知识****/
.HotKnowledge {
  width: 100%;

  .title {
    font-size: 16px;
    font-weight: bold;
    height: 34px;
    line-height: 34px;
  }

  .wrapper {
    width: 100%;
    .item {
      padding:5px;
      border-radius: 3px;
      font-size: 14px;
      color: #2440b3;
      white-space: nowrap;
      overflow: hidden;
      cursor: pointer;
      display: flex;
      .prefix {
        display: flex;
        align-items: center;
      }

      .number {
        color: #9195a3;
        margin-right: 10px;
      }
      .over {
        /* 溢出用省略号*/
        display: -webkit-box;
        -webkit-line-clamp: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-box-orient: vertical;
      }

      .name {
        overflow: hidden;
        display: inline;
      }

      .top0 {
        color: #f01616;
      }

      .top1 {
        color: #ff7945;
      }

      .top2 {
        color: #ff7945;
      }

      &:visited {
        color: #771caa;
      }
    }
  }
}
</style>