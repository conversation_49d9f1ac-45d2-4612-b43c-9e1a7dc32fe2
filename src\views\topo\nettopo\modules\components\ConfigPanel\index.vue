<template>
  <div class="config">
    <config-grid v-if="cellType === 'grid'" :globalGridAttr="globalGridAttr" />
    <config-node v-if="cellType === 'node'" :globalGridAttr="globalGridAttr" :size="selects.length" @bindDevice='bindDevice' />
    <config-edge v-if="cellType === 'edge'" :globalGridAttr="globalGridAttr" :size="selects.length" />
  </div>
</template>

<script>
import ConfigGrid from './ConfigGrid/index.vue'
import ConfigNode from './ConfigNode/index.vue'
import ConfigEdge from './ConfigEdge/index.vue'
import FlowGraph from '../../graph'
import './index.less'
import { globalGridAttr } from '../../models/global'
export default {
  name: 'Index',
  components: {
    ConfigGrid,
    ConfigNode,
    ConfigEdge,
  },
  props: {
    // //选中的节点连线id
    // cellId: {
    //   type: String,
    //   default: '',
    //   required: true,
    // },
    // //面板要展示的内容类型
    // cellType: {
    //   type: String,
    //   default: 'grid',
    //   required: true,
    // },
    //拓扑图的编辑查看状态
    operate: {
      type: String,
      default: 'create',
      required: true,
    },
  },
  data() {
    return {
      globalGridAttr: globalGridAttr,
      cellId: '',
      selects: [],
    }
  },
  computed: {
    cellType() {
      let node = this.selects.find((el) => el.isNode())
      let edge = this.selects.find((el) => el.isEdge())
      if (node && edge) {
        return 'grid'
      } else if (node) {
        return 'node'
      } else if (edge) {
        return 'edge'
      } else {
        return 'grid'
      }
    },
  },
  created() {
    if (this.operate === 'create') {
      const { graph } = FlowGraph
      //监听节点连线被选中时
      graph.on('cell:selected', ({ cell }) => {
        if(cell.data.nodeType === "app_auto_node" || cell.data.edgeType === "app_auto_edge" || cell.data.groupEdge ){
          return
        }
        this.cellId = cell.id
        this.selects = []

        let nodes = graph.getNodes()
        nodes.forEach((el) => {
          el.removePorts(null, { silent: false })
        })

        this.$nextTick(() => {
          this.selects = graph.getSelectedCells()
        })
      })
      // 监听节点取消被选中
      graph.on('cell:unselected', ({ cell }) => {
        if(cell.data.nodeType === "app_auto_node" || cell.data.edgeType === "app_auto_edge" ){
          return
        }
        this.cellId = ''
        this.selects = graph.getSelectedCells()
      })
    }
  },
  mounted() {},
  methods: {
    bindDevice(node) {
      this.$emit("bindDevice",node)
    },
  },
}
</script>

<style lang="less" scoped>
</style>
