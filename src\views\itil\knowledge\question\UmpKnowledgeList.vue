<template>
  <a-row :gutter='10' style='height: 100%' class='vScroll'>
    <a-col style='width: 100%; height: 100%; display: flex; flex-direction: column'>
      <!-- 查询区域 -->
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <div class='table-page-search-wrapper'>
          <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
            <a-row :gutter='24' ref='row'>
              <a-col :span='spanValue'>
                <a-form-item label='标题'>
                  <!-- <a-input :maxLength='maxLength' placeholder="请输入搜索关键词" v-model="queryParam.title"></a-input> -->
                  <j-input :maxLength='maxLength' placeholder='输入标题模糊查询' v-model='queryParam.title'></j-input>
                </a-form-item>
              </a-col>
              <!--            <a-col :span='spanValue'>-->
              <!--              <a-form-item label="状态">-->
              <!--                <j-dict-select-tag-->
              <!--                  v-model="queryParam.type"-->
              <!--                  placeholder="请选择"-->
              <!--                  dictCode="knowledge_question_type"-->
              <!--                />-->
              <!--              </a-form-item>-->
              <!--            </a-col>-->
              <a-col :span='colBtnsSpan()'>
                <span class='table-page-search-submitButtons'
                      :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
              <a-button type='primary' class='btn-search btn-search-style' @click='searchQuery'>查询</a-button>
              <a-button class='btn-reset btn-reset-style' @click='searchReset'>重置</a-button>
                   <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                   </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
        <!-- 查询区域-END -->
      </a-card>
      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <!-- 操作按钮区域 -->
        <div class='table-operator table-operator-style'>
          <a-button @click='handleAdd'>新增</a-button>
          <a-button @click="handleExportXls('问题知识库')">导出</a-button>
          <!-- <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl" @change="handleImportExcel">
          <a-button type="primary" icon="import">导入</a-button>
        </a-upload> -->
          <a-dropdown v-if='selectedRowKeys.length > 0'>
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item key='1' @click='batchDel'>删除</a-menu-item>
            </a-menu>
            <a-button> 批量操作
              <a-icon type='down' />
            </a-button>
          </a-dropdown>
        </div>
        <!-- table区域-begin -->
        <a-table
          ref='table'
          bordered
          rowKey='id'
          :columns='columns'
          :dataSource='dataSource'
          :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
          :pagination='ipagination'
          :loading='loading'
          :rowSelection='{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }'
          class='j-table-force-nowrap'
          @change='handleTableChange'
        >
          <template slot='htmlSlot' slot-scope='text'>
            <div v-html='text'></div>
          </template>
          <template slot='imgSlot' slot-scope='text'>
            <span v-if='!text' style='font-size: 14px'>无图片</span>
            <img v-else :src='getImgView(text)' height='25px' alt='' style='max-width: 80px; font-size: 14px' />
          </template>
          <template slot='fileSlot' slot-scope='text'>
            <span v-if='!text' style='font-size: 14px'>无文件</span>
            <a-button v-else :ghost='true' type='primary' icon='download' size='small' @click='downloadFile(text)'>
              下载
            </a-button>
          </template>

          <span slot='action' slot-scope='text, record' class='caozuo'>
            <a @click='handleDetailPage(record)'>查看</a>
            <a-divider type='vertical' />
            <a @click='handleEdit(record)'>编辑</a>
            <a-divider type='vertical' />
            <a-popconfirm title='确定删除吗?' @confirm='() => handleDelete(record.id)'>
              <a>删除</a>
            </a-popconfirm>
            <!-- <a-dropdown>
              <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
              <a-menu slot="overlay">
                <a-menu-item>
                </a-menu-item>
                <a-menu-item>
                </a-menu-item>
              </a-menu>
            </a-dropdown> -->
          </span>
          <template slot='tooltip' slot-scope='text'>
            <a-tooltip placement='topLeft' :title='text' trigger='hover'>
              <div class='tooltip'>
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>

        <ump-knowledge-modal ref='modalForm' @ok='modalFormOk'></ump-knowledge-modal>
      </a-card>
    </a-col>
  </a-row>
</template>

<script>
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import UmpKnowledgeModal from './modules/UmpKnowledgeModal'
import JInput from '@/components/jeecg/JInput'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'

export default {
  name: 'UmpKnowledgeList',
  mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
  components: {
    UmpKnowledgeModal,
    JInput
  },
  data() {
    return {
      maxLength:50,
      formItemLayout: {
        labelCol: {
          style: 'width:60px'
        },
        wrapperCol: {
          style: 'width:calc(100% - 60px)'
        }
      },
      description: '问题知识库管理页面',
      queryParam: {
        title: '',
        type: '',
        recordType: '2'
      },
      // 表头
      columns: [
        /*{
          title: '序号',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function(t, r, index) {
            return parseInt(index) + 1
          }
        },*/
        {
          title: '标题',
          dataIndex: 'title',
          scopedSlots: { customRender: 'tooltip' }
        },
        {
          title: '创建人',
          dataIndex: 'createBy_dictText'
        },
        {
          title: '类型',
          dataIndex: 'type_dictText'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '更新时间',
          dataIndex: 'updateTime'
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 160,
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: '/knowledge/list',
        delete: '/knowledge/delete',
        deleteBatch: '/knowledge/deleteBatch',
        exportXlsUrl: '/knowledge/exportXls',
        importExcelUrl: 'knowledge/umpKnowledge/importExcel'
      },
      dictOptions: {}
    }
  },
  created() {
  },
  computed: {
    importExcelUrl: function() {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    }
  },
  mounted() {
  },
  methods: {
    initDictConfig() {
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
/*给table列设置宽度*/
::v-deep .ant-table-scroll .ant-table-thead > tr > th,
::v-deep .ant-table-scroll .ant-table-tbody > tr > td {
  /*标题*/

  &:nth-child(2) {
    min-width: 150px;
    max-width: 300px;
  }

  /*创建人*/

  &:nth-child(3) {
    min-width: 100px;
    max-width: 200px;
  }

  /*类型*/

  &:nth-child(4) {
    min-width: 50px;
    max-width: 120px;
  }

  /*创建时间*/

  &:nth-child(5) {
    min-width: 150px;
    max-width: 300px;
  }

  /*更新时间*/

  &:nth-child(6) {
    min-width: 150px;
    max-width: 300px;
  }
}

/*表头样式*/
::v-deep .ant-table-thead > tr > th {
  text-align: center;
  white-space: nowrap;
}

/*内容对齐方式、省略显示*/
::v-deep .ant-table-tbody > tr > td {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;

  &:first-child,
  &:nth-child(3),
  &:nth-child(4) {
    text-align: center;
  }

  &:nth-child(2) {
    text-align: left;
  }

  &:nth-child(5), &:nth-child(6) {
    text-align: right;
  }
}
</style>

