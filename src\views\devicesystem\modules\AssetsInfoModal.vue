<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    :destroyOnClose="true"
    switchFullscreen
    :centered='true'
    @ok="handleOk"
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }"
    @cancel="handleCancel"
    cancelText="关闭">
    <div>
      <assets-info-list ref="realForm" @ok="submitCallback" :disabled="disableSubmit"></assets-info-list>
    </div>
  </j-modal>
</template>

<script>
import AssetsInfoList from './AssetsInfoList'
export default {
  name: 'AssetsInfoModal',
  components: {
    AssetsInfoList
  },
  data() {
    return {
      title: '',
      width: '1200px',
      visible: false,
      disableSubmit: false,
      selectAssets: {},
    }
  },

  methods: {
    add() {
      this.visible = true
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      const that = this
      this.selectAssets = this.$refs.realForm.selectAssets
      if (!this.selectAssets.id||this.selectAssets.id.length == 0) {
        that.$message.warning('请选择要关联的资产!')
      } else {
        let arrayL = []
        arrayL.push(this.$refs.realForm.selectAssets)
        that.$emit('func', arrayL)
        that.$emit('ok')
        that.close()
      }
    },
    submitCallback() {
      this.$emit('ok')
      this.visible = false
    },
    handleCancel() {
      this.close()
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/normalModal.less';
</style>