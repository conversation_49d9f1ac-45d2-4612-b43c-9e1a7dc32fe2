<template>
  <div style='height: 100%'>
    <keep-alive exclude='AlarmInfoDetails'>
      <component :is='pageName' :data='data'/>
    </keep-alive>
  </div>
</template>
<script>
import AuditTaskList from '@views/opmg/ipManagement/ipAlarm/ipAlarmList.vue'
/*告警信息查看*/
import AlarmInfoDetails from '@views/opmg/ipManagement/ipAlarm/modules/ipAlarmDetails.vue'

export default {
  name: 'AlarmTaskManage',
  props: {},
  data() {
    return {
      isActive: 0,
      data: {},
    }
  },
  components: {
    AuditTaskList,
    AlarmInfoDetails,
  },
  created() {
    this.pButton1(0)
  },
  //使用计算属性
  computed: {
    pageName() {
      switch (this.isActive) {
        case 1:
          return 'AlarmInfoDetails'
          break;
        default:
          return 'AuditTaskList'
          break;
      }
    }
  },
  methods: {
    pButton1(index) {
      this.isActive = index
    },
    pButton2(index, item) {
      this.isActive = index
      this.data = item//告警信息
    }
  }
}
</script>