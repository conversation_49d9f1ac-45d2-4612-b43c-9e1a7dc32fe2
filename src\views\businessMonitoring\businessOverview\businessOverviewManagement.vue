<template>
  <keep-alive exclude='businessOverviewInfo'>
    <component :is="pageName" :data="data"/>
  </keep-alive>
</template>
<script>
import businessOverviewList from './businessOverviewList'
import businessOverviewInfo from './businessOverviewInfo'
export default {
  name: "businessOverviewManagement",
  data() {
    return {
      isActive: 0,
      data:{},
    };
  },

  components: {
    businessOverviewList,
    businessOverviewInfo
  },
  created(){
    this.pButton1(0);
  },
  //使用计算属性
  computed: {
    pageName() {
      switch (this.isActive) {
        case 0:
          return "businessOverviewList";
          break;

        default:
          return "businessOverviewInfo";
          break;
      }
    }
  },
  methods: {
    pButton1(index) {
      this.isActive = index;
    },
    pButton2(index,item) {
      this.isActive = index;
      this.data = item;
    }
  }
}
</script>