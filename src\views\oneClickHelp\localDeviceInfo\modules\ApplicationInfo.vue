<template>
  <card-frame title='应用'>
    <div slot='bodySlot' class='bodySlot' v-if='Object.keys(info).length >0&&info.infoArr.length>0'>
      <div class='disk-info'>
        <div class='left'>
          <div class='left-table'>
            <div class='table-header table-layout'>
              <div >应用名称</div>
              <div>版本</div>
              <div>状态</div>
            </div>
            <div class='table-body-tr table-layout' v-for='(item ,index) in info.infoArr' :key='"body_"+index'>
              <div class='table-td'>{{item.name}}</div>
              <div class='table-td'>{{item.version}}</div>
              <div class='table-td'>{{item.runStatus}}</div>
            </div>
          </div>
        </div>
        <!--        <div class='right' v-if='info.trendXData.length>0&&info.trendYData.length>0'>-->
        <!--          <div class='rate'>-->
        <!--            {{rate}}-->
        <!--          </div>-->
        <!--          <div id='chart_disk' class='chart'></div>-->
        <!--        </div>-->
      </div>
    </div>
    <div slot='bodySlot' class='body-height body-empty' v-else>
      <a-spin :spinning='loading' v-if='loading' class='spin'></a-spin>
      <a-list :data-source='[]' :locale='locale' v-else />
    </div>
  </card-frame>
</template>
<script>
import cardFrame from '@views/oneClickHelp/localDeviceInfo/modules/CardFrame.vue'
import Empty from '@/components/oneClickHelp/Empty.vue'
import echarts from 'echarts/lib/echarts'
import 'echarts/lib/component/graphic'
export default {
  name: "DiskInfo",
  props: {
    info: {
      type: Object,
      required: true
    },
    loading: {
      type: Boolean,
      required: false,
      default:false
    }
  },
  components:{cardFrame, Empty},
  data() {
    return {
      rate:'',
      locale: {
        emptyText: <Empty/>
      }
    }
  },
  watch:{
    info:{
      handler(nValue){
      }
    },
    deep:true,
    immediate:true
  },
  methods: {

  }
}
</script>

<style scoped lang="less">
@media (min-width: 1600px) {
  .bodySlot {
    height: 100%;
    //padding: 0.5rem 0.65rem 0.4rem; //40px 52px 32px/80
    overflow: hidden;
    padding-top: 0.1rem;

    .disk-info {
      height: 100%;
      display: flex;
      flex-flow: row nowrap;
      flex-direction: column;
      .left {
        width: 100%;
        //padding-right: 0.1rem;
        height: 100%;
        max-height: 100%;
        margin-bottom:0;

        .left-table {
          height: 100%;
          overflow: hidden;
          overflow-y: auto;

          .table-layout {
            display: flex;
            flex-flow: row nowrap;
            justify-content: space-around;
            align-items: center;
            height: 0.5625rem; // 45px;
            line-height: 0.5625rem; //45px;
            color: #fff;
            font-size: 0.2rem;
            padding: 0.125rem 0.375rem; //10px 30px/80px

            div {
              flex: 1;
              text-align: center;
            }
          }

          .table-header {
            background-color: rgba(6, 126, 223, 0.13);
          }

          .table-body-tr {
            border-bottom: 1px dotted rgba(63, 80, 125, 0.42);
          }
        }
      }

      .right {
        padding-left: 0.1rem;
        height: 100%;
        width: 50%;

        .rate {
          margin-left: 0.75rem; // 60px/80;
          font-size: 0.55rem; //44px/80;
          height:0.8125rem ;
          line-height: 0.8125rem; // 65px/80;
          font-weight: 700;
          color: #FFFFFF;
          font-family: DIN-Medium
        }

        .chart {
          height: calc(100% - 0.8125rem)
        }
      }
    }
  }
}
@media (max-width: 1599px) {
  .bodySlot {
    height: 100%;
    padding: 0.5rem 0.65rem 0.4rem; //40px 52px 32px/80
    overflow: hidden;

    .disk-info {
      height: 100%;
      display: flex;
      flex-flow: column nowrap;

      .left {
        width: 100%;
        padding-right: 0;
        //height: 50%;
        height: max-content;
        max-height:50%;
        margin-bottom:0.3rem;

        .left-table {
          height: 100%;
          overflow: hidden;
          overflow-y: auto;

          .table-layout {
            display: flex;
            flex-flow: row nowrap;
            justify-content: space-around;
            align-items: center;
            height: 0.5625rem; // 45px;
            line-height: 0.5625rem; //45px;
            color: #fff;
            font-size: 0.2rem;
            padding: 0.125rem 0.375rem; //10px 30px/80px

            div {
              flex: 1;
              text-align: center;
            }
          }

          .table-header {
            background-color: rgba(6, 126, 223, 0.13);
          }

          .table-body-tr {
            border-bottom: 1px dotted rgba(63, 80, 125, 0.42);
          }
        }
      }

      .right {
        padding-left: 0;
        height: 50%;
        width: 100%;

        .rate {
          margin-left: 0.75rem; // 60px/80;
          font-size: 0.55rem; //44px/80;
          height:0.8125rem ;
          line-height: 0.8125rem; // 65px/80;
          font-weight: 700;
          color: #FFFFFF;
          font-family: DIN-Medium
        }

        .chart {
          height: calc(100% - 0.8125rem)
        }
      }
    }
  }
}
</style>