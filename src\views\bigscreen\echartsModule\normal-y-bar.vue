<template>
  <div :id="chartId" ref="chartDom" style="width: 100%; height: 100%"></div>
</template>

<script>
  export default {
    props: ['chartId', 'chartObj'],
    data() {
      return {}
    },
    created() {},
    mounted() {

    },
    watch: {
      chartObj() {
        if (this.chartId) {
          this.drawChart()
        }
      }
    },
    methods: {
      drawChart() {
        let maxv = 300
        if (maxv < 100) maxv = 100
        let data = this.chartObj.map(el => el.netInOutTop)
        let xData = this.chartObj.map(el => el.name)
        let myChart = this.$echarts.init(document.getElementById(this.chartId))
        myChart.setOption({
          tooltip: {
            show: true,
            trigger: 'item',
            axisPointer: {
              type: 'shadow',
            },
            backgroundColor: 'rgba(9, 24, 48, 0.5)',
            borderColor: 'rgba(75, 253, 238, 0.4)',
            textStyle: {
              color: '#CFE3FC',
            },
            borderWidth: 1,
            appendToBody: true,
            formatter: (a, b) => {
              return '名称：' + a.name + "<br /> 总流量：" + a.data

            },
            transitionDuration: 0, //echart防止tooltip的抖动
          },
          dataZoom: [{
            xAxisIndex: [0],
            show: false, //是否显示滑动条，不影响使用
            start: 0, // 从头开始。
            endValue: 9,
            realtime: true, //是否实时更新
          },
          {
            type: 'inside',
            xAxisIndex: 0,
            zoomOnMouseWheel: true, //滚轮是否触发缩放
            moveOnMouseMove: true, //鼠标滚轮触发滚动
            moveOnMouseWheel: true
          }],
          grid: {
            top: 32,
            right: 16,
            left: 30,
            bottom: 0,
            containLabel: true,
          },
          xAxis: [{
            type: 'category',
            data: xData,
            axisLine: {
              lineStyle: {
                color: '#FFFFFF',
              },
            },
            axisLabel: {
              interval: 0,
              color: '#e2e9ff',
              textStyle: {
                fontSize: 12,
              },
              formatter: function(value) {
                var temp = '' // 拼接加 \n 返回的类目项
                var everyLineNum = 4 // 每行显示文字个数
                var maxLength = 7 // 最多显示文字个数
                if (xData.length < 5) {
                  everyLineNum = 8
                  maxLength = 15
                }
                if (value.length > everyLineNum) {
                  if (value.length > maxLength) {
                    temp = value.substring(0, everyLineNum) + '\n' + value.substring(everyLineNum, maxLength) + '...'
                  } else {
                    temp = value.substring(0, everyLineNum) + '\n' + value.substring(everyLineNum, maxLength)
                  }
                  return temp
                } else {
                  return value
                }
              }
            },
            axisTick: {
              show: false,
            },
          }, ],
          yAxis: [{
            name: `${this.chartObj.length>0?'单位（MB）':''}`,
            nameTextStyle: {
              color: "#fff",
              fontSize: 10,
              padding: [0, 0, 0, -30]
            },
            axisLabel: {
              formatter: '{value}',
              color: '#e2e9ff',
            },
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: '#FFFFFF',
              },
            },
            splitLine: {
              lineStyle: {
                color: 'rgba(255,255,255,0.12)',
              },
            },
          }, ],
          series: [{
            type: 'bar',
            data: data,
            barWidth: '50%',
            itemStyle: {
              normal: {
                color: new this.$echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [{
                      offset: 0,
                      color: 'rgba(0,244,255,1)',
                    },
                    {
                      offset: 1,
                      color: 'rgba(0,77,167,1)',
                    },
                  ],
                  false
                ),
                shadowColor: 'rgba(0,160,221,1)',
                shadowBlur: 4,
              },
            },
            label: {
              normal: {
                show: true,
                lineHeight: 10,
                formatter: '{c}',
                position: 'top',
                textStyle: {
                  color: '#00D6F9',
                  fontSize: 12,
                },
              },
            },
          }, ],
        })
        window.addEventListener('resize', () => {
          myChart.resize()
        })
      },
    },
  }
</script>

<style lang="less" scoped>
</style>