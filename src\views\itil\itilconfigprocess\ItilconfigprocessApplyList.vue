<template>
  <a-row :gutter="10" style="height: 100%; display: flex">
    <a-col :xl="4" :lg="4" :md="6" :sm="6" :xs="8" style="height: 100%; overflow: hidden">
      <a-card :bordered="false" style="height: 100%; overflow-x: auto">
        <!-- 按钮操作区域 -->
        <a-row style="margin-left: 14px">
          <!--<a-button @click="refresh" type="default" icon="reload" :loading="loading">刷新</a-button>-->
        </a-row>
        <div style="background: #fff; height: 100%">
          <a-input-search
            @search="onSearch"
            autocomplete="off"
            :allowClear="true"
            style="width: 100%; margin-bottom: 12px"
            placeholder="请输入类型名称"
          />
          <!-- 树-->
          <a-col :sm="24">
            <template>
              <a-dropdown :trigger="[this.dropTrigger]" @visibleChange="dropStatus">
                <span style="user-select: none">
                  <a-tree
                    multiple
                    @select="onSelect"
                    @check="onCheck"
                    @rightClick="rightHandle"
                    :selectedKeys="selectedKeys"
                    :checkedKeys="checkedKeys"
                    :treeData="departTree"
                    :checkStrictly="checkStrictly"
                    :expandedKeys="iExpandedKeys"
                    :autoExpandParent="autoExpandParent"
                    @expand="onExpand"
                  />
                </span>
                <!--新增右键点击事件,和增加添加和删除功能-->
                <a-menu slot="overlay">
                  <a-menu-item @click="handleAddType" key="1">添加</a-menu-item>
                  <a-menu-item @click="handleEditType">编辑</a-menu-item>
                  <a-menu-item @click="handleDeleteType" key="2">删除</a-menu-item>
                  <a-menu-item @click="closeDrop" key="3">取消</a-menu-item>
                </a-menu>
              </a-dropdown>
            </template>
          </a-col>
        </div>
      </a-card>
      <!---- author:os_chengtgen -- date:20190827 --  for:切换父子勾选模式 =======------>
      <!-- <div class="drawer-bootom-button">
          <a-dropdown :trigger="['click']" placement="topCenter">
            <a-menu slot="overlay">
              <a-menu-item key="1" @click="switchCheckStrictly(1)">父子关联</a-menu-item>
              <a-menu-item key="2" @click="switchCheckStrictly(2)">取消关联</a-menu-item>
              <a-menu-item key="3" @click="checkALL">全部勾选</a-menu-item>
              <a-menu-item key="4" @click="cancelCheckALL">取消全选</a-menu-item>
              <a-menu-item key="5" @click="expandAll">展开所有</a-menu-item>
              <a-menu-item key="6" @click="closeAll">合并所有</a-menu-item>
            </a-menu>
            <a-button>
              树操作 <a-icon type="up" />
            </a-button>
          </a-dropdown>
        </div> -->
      <!---- author:os_chengtgen -- date:20190827 --  for:切换父子勾选模式 =======------>
    </a-col>
    <!--右边-->
    <a-col :xl="20" :lg="20" :md="18" :sm="18" :xs="16" style="height: 100%">
      <a-row :gutter="10" style="height: 100%" class="vScroll zxw">
        <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
          <!-- 查询区域 -->
          <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class="card-style">
            <div class="table-page-search-wrapper">
              <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
                <a-row :gutter="24" ref="row">
                  <a-col :span="spanValue">
                    <a-form-item label="名称">
                      <a-input
                        placeholder="请输入"
                        autocomplete="off"
                        :allowClear="true"
                        v-model="queryParam.title"
                      ></a-input>
                    </a-form-item>
                  </a-col>
                  <a-col :span="spanValue">
                    <a-form-item label="审核状态">
                      <a-select
                        v-model="queryParam.status"
                        :getPopupContainer="(node) => node.parentNode"
                        placeholder="请选择"
                        :allowClear="true"
                      >
                        <a-select-option value="0">草稿</a-select-option>
                        <a-select-option value="1">处理中</a-select-option>
                        <a-select-option value="2">已结束</a-select-option>
                        <a-select-option value="3">已撤回</a-select-option>
                      </a-select>
                    </a-form-item>
                  </a-col>
                  <a-col :span="colBtnsSpan()">
                    <span
                      class="table-page-search-submitButtons"
                      :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                    >
                      <a-button type="primary" @click="searchQuery" class="btn-search-style">查询</a-button>
                      <a-button @click="searchReset" style="margin-left: 10px" class="btn-reset-style">重置</a-button>
                    </span>
                  </a-col>
                </a-row>
              </a-form>
            </div>
            <!-- 查询区域-END -->

            <!-- 操作按钮区域 -->
          </a-card>
          <a-card :bordered="false" style="flex: auto" class="core">
            <div class="table-operator tableBottom">
              <a-button @click="chooseProcess()">新增</a-button>
              <a-button @click="handleExportXlsEven('配置我的申请')">导出</a-button>

              <!-- <a-button @click="loadData" type="primary" icon="reload">刷新</a-button>&nbsp; -->

              <a-dropdown v-if="selectedRowKeys.length > 0">
                <a-menu slot="overlay" style='text-align: center'>
                  <a-menu-item key="1" @click="batchDel">删除 </a-menu-item>
                </a-menu>
                <a-button>
                  批量操作
                  <a-icon type="down" />
                </a-button>
              </a-dropdown>
            </div>
            <!-- table区域-begin -->
            <div>
              <a-table
                ref="table"
                bordered
                rowKey="id"
                :columns="columns"
                :dataSource="dataSource"
                :pagination="ipagination"
                :loading="loading"
                :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
                class="j-table-force-nowrap"
                @change="handleTableChange"
              >
                <span slot="status" slot-scope="status, record">
                  <div v-if="record.status == 0">草稿</div>
                  <div v-if="record.status == 1">处理中</div>
                  <div v-if="record.status == 2">已结束</div>
                  <div v-if="record.status == 3">已撤回</div>
                </span>

                <span slot="result" slot-scope="result, record">
                  <div v-if="record.result == 0">未提交</div>
                  <div v-if="record.result == 1">处理中</div>
                  <div v-if="record.result == 2" style="color: #139b33">已通过</div>
                  <div v-if="record.result == 3" style="color: #df1a1a">已驳回</div>
                </span>

                <template slot="htmlSlot" slot-scope="text">
                  <div v-html="text"></div>
                </template>
                <template slot="imgSlot" slot-scope="text">
                  <span v-if="!text" style="font-size: 14px">无图片</span>
                  <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width: 80px; font-size: 14px" />
                </template>
                <template slot="fileSlot" slot-scope="text">
                  <span v-if="!text" style="font-size: 14px">无文件</span>
                  <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="uploadFile(text)">
                    下载
                  </a-button>
                </template>

                <span slot="action" slot-scope="text, record" class="caozuo">
                  <template v-if="record.status == 0">
                    <a href="javascript:void(0);" @click="apply(record)">提交申请</a>
                  </template>
                  <template v-if="record.status != 0">
                    <a @click="handleDeatails(record)">查看</a>
                  </template>
                  <template v-if="record.status == 0 || record.status == 3">
                    <a-divider type="vertical" />
                    <a @click="edit(record)">编辑</a>
                  </template>
                  <template v-if="record.status == 0">
                    <a-divider type="vertical" />
                    <a-popconfirm title="确定删除吗?" @confirm="() => remove(record)">
                      <a>删除</a>
                    </a-popconfirm>
                  </template>
                  <!-- <template v-if="record.status == 0">
                <a-divider type="vertical" />
                <a-dropdown>
                  <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
                  <a-menu slot="overlay">
                    <a-menu-item v-if="record.status == 0 || record.status == 3">
                      <a @click="edit(record)">编辑</a>
                    </a-menu-item>
                    <a-menu-item v-if="record.status == 0">
                      <a-popconfirm title="确定删除吗?" @confirm="() => remove(record)">
                        <a>删除</a>
                      </a-popconfirm>
                    </a-menu-item>
                  </a-menu>
                </a-dropdown>
              </template> -->
                </span>
              </a-table>
            </div>
            <!--提交申请表单-->
            <a-modal title="提交申请" v-model="modalVisible" :mask-closable="false" :width="500" :footer="null">
              <div v-if="modalVisible">
                <a-form-item label="选择审批人" v-show="showAssign">
                  <l-select-user-by-dep v-model="form.assignees" :multi="false"></l-select-user-by-dep>
                </a-form-item>
                <a-form-item label="下一审批人" v-show="isGateway">
                  <a-alert
                    type="info"
                    showIcon
                    message="分支网关处不支持自定义选择下一审批人，将自动下发给所有可审批人。"
                    >，将发送给下一节点所有人</a-alert
                  >
                </a-form-item>
                <a-form-item label="消息通知">
                  <a-checkbox v-model="form.sendMessage">站内消息通知</a-checkbox>
                  <a-checkbox v-model="form.sendSms" disabled>短信通知</a-checkbox>
                  <a-checkbox v-model="form.sendEmail" disabled>邮件通知</a-checkbox>
                </a-form-item>
                <div slot="footer" style="text-align: right">
                  <a-button type="text" @click="modalVisible = false">取消</a-button>
                  <div style="display: inline-block; width: 20px"></div>
                  <a-button type="primary" :disabled="submitLoading" @click="applySubmit">提交</a-button>
                </div>
              </div>
            </a-modal>
            <itilConfigProcessModal ref="itilConfigProcess" @ok="loadData"></itilConfigProcessModal>
            <itilConfigItemTypeTree-modal ref="modalTypeForm" @ok="refresh"></itilConfigItemTypeTree-modal>
            <itilConfigProcessDetails ref="itilConfigProcessDetails" @ok="modalFormOk"></itilConfigProcessDetails>
          </a-card>
        </a-col>
      </a-row>
    </a-col>
  </a-row>
</template>

<script>
// import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import ItilConfigItemTypeTreeModal from '@/views/itil/itilconfigitemlibrary/modules/ItilConfigItemTypeTreeModal'
import ItilConfigProcessModal from './modules/ItilConfigProcessModal'
import { queryItilconfigItemtypeTreeList, deleteByConfigItemtypeId, searchByConfigItemtype } from '@/api/api'
import { deleteAction, getAction, downFile, postAction } from '@/api/manage'
import { initDictOptions, filterDictText } from '@/components/dict/JDictSelectUtil'
import { activitiMixin } from '@/views/activiti/mixins/activitiMixin'
import { filterObj } from '@/utils/util'
import JEllipsis from '@/components/jeecg/JEllipsis'
import pick from 'lodash.pick'
import JTreeSelect from '@/components/jeecg/JTreeSelect'
import historicDetail from '@/views/activiti/historicDetail'
import itilConfigProcessDetails from './modules/ItilConfigProcessDetails'
import LSelectUserByDep from '@/components/jeecgbiz/LSelectUserByDep'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'

export default {
  name: 'ItilconfigprocessApplyList',
  mixins: [JeecgListMixin, mixinDevice, activitiMixin, YqFormSearchLocation],
  components: {
    ItilConfigProcessModal,
    ItilConfigItemTypeTreeModal,
    itilConfigProcessDetails,
    LSelectUserByDep,
  },
  data() {
    return {
      formItemLayout: {
        labelCol: {
          style: 'width:70px',
        },
        wrapperCol: {
          style: 'width:calc(100% - 70px)'
        }
      },
      description: '配置项库管理页面',
      //树
      departTree: [],
      treeData: [],
      dropTrigger: '',
      selectedKeys: [],
      checkedKeys: [],
      checkStrictly: true,
      iExpandedKeys: [],
      autoExpandParent: true,
      currFlowId: '',
      currFlowName: '',
      rightClickSelectedBean: {},
      //查询
      queryParam: {
        configType: '',
        eventType: '',
      },
      form: {
        priority: 0,
        assignees: [],
        sendMessage: true,
      },
      modalVisible: false,
      submitLoading: false,
      isGateway: false,
      assigneeList: [],
      // 表头
      columns: [
        /*{
          title: '序号',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function(t, r, index) {
            return parseInt(index) + 1
          }
        },*/
        {
          title: '编号',
          dataIndex: 'code',
        },
        {
          title: '名称',
          dataIndex: 'name',
        },
        {
          title: '类型',
          dataIndex: 'configType_text',
        },
        {
          title: '审核状态',
          dataIndex: 'status',
          scopedSlots: { customRender: 'status' },
        },
        {
          title: '结果',
          dataIndex: 'result',
          scopedSlots: { customRender: 'result' },
        },
        {
          title: '所属人',
          dataIndex: 'owner_text',
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 147,
          scopedSlots: { customRender: 'action' },
        },
      ],
      url: {
        list: '/itilconfigprocess/ItilConfigProcess/myApplyList',
        delete: '/itilconfigitemlibrary/itilConfigItemLibrary/delete',
        deleteBatch: '/itilconfigitemlibrary/itilConfigItemLibrary/deleteBatch',
        exportXlsUrl: '/itilconfigitemlibrary/itilConfigItemLibrary/exportXls',
        importExcelUrl: 'itilconfigitemlibrary/itilConfigItemLibrary/importExcel',
        getFirstNode: '/actProcessIns/getFirstNode',
        applyBusiness: '/actBusiness/apply',
        delByIds: '/actBusiness/delByIds',
        exportXlsUrl: '/itilconfigprocess/ItilConfigProcess/exportXls',
      },
      uid: this.$route.query.id,
      dictOptions: {},
      releaseId: this.$route.query.releaseId,
    }
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
    //可行性测试，根据文件路径动态加载组件
    LcDict: function () {
      var myComponent = () => import(`@/components/dict/JDictSelectTag`)
      return myComponent
    },
  },
  // created(){
  //   this.refresh();
  // },
  mounted() {
    this.refresh()
    if (this.uid) {
      this.chooseProcess()
    }
  },
  methods: {
    handleExportXlsEven(fileName) {
      if (!fileName || typeof fileName != 'string') {
        fileName = '导出文件'
      }
      let param = this.getQueryParams()
      if (this.selectedRowKeys && this.selectedRowKeys.length > 0) {
        param['selections'] = this.selectedRowKeys.join(',')
      }
      downFile(this.url.exportXlsUrl, param).then((data) => {
        if (!data) {
          this.$message.warning('文件下载失败')
          return
        }
        if (typeof window.navigator.msSaveBlob !== 'undefined') {
          window.navigator.msSaveBlob(new Blob([data], { type: 'application/vnd.ms-excel' }), fileName + '.xls')
        } else {
          let url = window.URL.createObjectURL(new Blob([data], { type: 'application/vnd.ms-excel' }))
          let link = document.createElement('a')
          link.style.display = 'none'
          link.href = url
          link.setAttribute('download', fileName + '.xls')
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link) //下载完成移除元素
          window.URL.revokeObjectURL(url) //释放掉blob对象
        }
      })
    },
    handleDeatails: function (record) {
      this.$refs.itilConfigProcessDetails.edit(record)
      this.$refs.itilConfigProcessDetails.title = '配置详情'
      this.$refs.itilConfigProcessDetails.disableSubmit = false
      // this.$refs.itilConfigProcessDetails.tabaaa(record)
    },
    // loadData() {
    //   this.refresh();
    // },
    refresh() {
      this.loading = true
      this.loadTree()
    },
    //查询树
    loadTree() {
      var that = this
      that.treeData = []
      that.departTree = []
      queryItilconfigItemtypeTreeList().then((res) => {
        if (res.success) {
          //部门全选后，再添加部门，选中数量增多
          this.allTreeKeys = []
          for (let i = 0; i < res.result.length; i++) {
            let temp = res.result[i]
            that.treeData.push(temp)
            that.departTree.push(temp)
            that.setThisExpandedKeys(temp)
            that.getAllKeys(temp)
          }
          this.loading = false
        }
      })
    },
    setThisExpandedKeys(node) {
      if (node.children && node.children.length > 0) {
        this.iExpandedKeys.push(node.key)
        for (let a = 0; a < node.children.length; a++) {
          this.setThisExpandedKeys(node.children[a])
        }
      }
    },
    onSearch(value) {
      let that = this
      if (value) {
        searchByConfigItemtype({ typeName: value }).then((res) => {
          if (res.success) {
            that.departTree = []
            for (let i = 0; i < res.result.length; i++) {
              let temp = res.result[i]
              that.departTree.push(temp)
            }
          } else {
            that.$message.warning(res.message)
          }
        })
      } else {
        that.loadTree()
      }
    },
    onExpand(expandedKeys) {
      // if not set autoExpandParent to false, if children expanded, parent can not collapse.
      // or, you can remove all expanded children keys.
      this.iExpandedKeys = expandedKeys
      this.autoExpandParent = false
    },
    // 右键点击下拉框改变事件
    dropStatus(visible) {
      if (visible == false) {
        this.dropTrigger = ''
      }
    },
    // 右键操作方法
    rightHandle(node) {
      this.dropTrigger = 'contextmenu'
      this.rightClickSelectedKey = node.node.eventKey
      this.rightClickSelectedBean = node.node.dataRef
    },
    //选择树的方法
    onSelect(selectedKeys, e) {
      this.hiding = false
      let record = e.node.dataRef
      this.currSelected = Object.assign({}, record)
      this.model = this.currSelected
      this.selectedKeys = [record.key]
      this.model.parentId = record.parentId
      this.setValuesToForm(record)
      // this.$refs.departAuth.show(record.id);
    },
    // 触发onSelect事件时,为部门树右侧的form表单赋值
    setValuesToForm(record) {
      this.queryParam.eventType = record.key
      this.loadData()
    },
    //树的添加类型
    handleAddType() {
      this.$refs.modalTypeForm.add(this.rightClickSelectedKey)
      this.$refs.modalTypeForm.title = '新增'
    },
    //树的编辑类型
    handleEditType() {
      this.$refs.modalTypeForm.edit(this.rightClickSelectedBean)
      this.$refs.modalTypeForm.title = '编辑'
    },
    //删除类型
    handleDeleteType() {
      var that = this
      this.$confirm({
        title: '确认删除',
        okText: '是',
        cancelText: '否',
        content: '是否删除此类型以及子节点数据吗?',
        onOk: function () {
          deleteByConfigItemtypeId({ id: that.rightClickSelectedKey }).then((resp) => {
            if (resp.success) {
              //删除成功后，去除已选中中的数据
              that.checkedKeys.splice(
                that.checkedKeys.findIndex((key) => key === that.rightClickSelectedKey),
                1
              )
              that.$message.success('删除成功!')
              that.loadTree()
            } else {
              that.$message.warning('删除失败!')
            }
          })
        },
      })
    },
    getCurrSelectedTitle() {
      return !this.currSelected.title ? '' : this.currSelected.title
    },
    onCheck(checkedKeys, info) {
      this.hiding = false
      //this.checkedKeys = checkedKeys.checked
      // <!---- author:os_chengtgen -- date:20190827 --  for:切换父子勾选模式 =======------>
      if (this.checkStrictly) {
        this.checkedKeys = checkedKeys.checked
      } else {
        this.checkedKeys = checkedKeys
      }
      // <!---- author:os_chengtgen -- date:20190827 --  for:切换父子勾选模式 =======------>
    },
    // 右键店家下拉关闭下拉框
    closeDrop() {
      this.dropTrigger = ''
    },
    // <!---- author:os_chengtgen -- date:20190827 --  for:切换父子勾选模式 =======------>
    expandAll() {
      this.iExpandedKeys = this.allTreeKeys
    },
    closeAll() {
      this.iExpandedKeys = []
    },
    checkALL() {
      this.checkStriccheckStrictlytly = false
      this.checkedKeys = this.allTreeKeys
    },
    cancelCheckALL() {
      //this.checkedKeys = this.defaultCheckedKeys
      this.checkedKeys = []
    },
    switchCheckStrictly(v) {
      if (v == 1) {
        this.checkStrictly = false
      } else if (v == 2) {
        this.checkStrictly = true
      }
    },
    getAllKeys(node) {
      this.allTreeKeys.push(node.key)
      if (node.children && node.children.length > 0) {
        for (let a = 0; a < node.children.length; a++) {
          this.getAllKeys(node.children[a])
        }
      }
    },
    //添加流程
    chooseProcess() {
      let v = { formKey: 'itilconfigprocess_from' }
      if (!v.formKey) {
        this.$message.warning('该流程信息未配置表单，请联系开发人员！')
        return
      }
      // this.lcModa.formComponent = this.getFormComponent(v.routeName).component;
      // this.lcModa.title = '发起流程：'+v.name;
      // this.lcModa.isNew = true;
      // this.lcModa.processData = v;
      // this.lcModa.visible = true;
      this.$refs.itilConfigProcess.title = '新增'
      this.$refs.itilConfigProcess.initData(v, this.releaseId)
    },
    // <!---- author:os_chengtgen -- date:20190827 --  for:切换父子勾选模式 =======------>
    //提交申请弹框
    apply(v) {
      if (!v.procDefId || v.procDefId == 'null') {
        this.$message.error('流程定义为空')
        return
      }
      this.form.id = v.id
      this.form.procDefId = v.procDefId
      this.form.title = v.title

      this.form.assignees = ''
      this.modalVisible = true
      this.isGateway = false
      this.form.firstGateway = true
      this.showAssign = true
      // 加载审批人
      // getAction(this.url.getFirstNode, { procDefId: v.procDefId }).then(res => {
      //   if (res.success) {
      //     if (res.result.type == 3 || res.result.type == 4) {
      //       this.isGateway = true
      //       this.modalVisible = true
      //       this.form.firstGateway = true
      //       this.showAssign = false
      //       this.error = ''
      //       return
      //     }
      //     this.form.firstGateway = false
      //     this.isGateway = false
      //     if (res.result.users && res.result.users.length > 0) {
      //       this.error = ''
      //       this.assigneeList = res.result.users
      //       // 默认勾选
      //       let ids = []
      //       res.result.users.forEach(e => {
      //         ids.push(e.username)
      //       })
      //       this.form.assignees = ids
      //       this.showAssign = true
      //     } else {
      //       this.form.assignees = []
      //       this.showAssign = true
      //       this.error = '审批节点未分配候选审批人员，请联系管理员！'
      //     }
      //     if (this.error) {
      //       this.$message.error(this.error)
      //       return
      //     }
      //     this.modalVisible = true
      //   } else {
      //     this.$message.error(res.message)
      //   }
      // })
    },
    //申请提交
    applySubmit() {
      if (this.showAssign && this.form.assignees.length < 1) {
        this.error = '请至少选择一个审批人'
        this.$message.error(this.error)
        return
      } else {
        this.error = ''
      }
      this.submitLoading = true
      var params = Object.assign({}, this.form)
      // params.assignees = params.assignees.join(',')
      postAction(this.url.applyBusiness, params)
        .then((res) => {
          if (res.success) {
            this.$message.success('操作成功')
            this.loadData()
            this.modalVisible = false
          } else {
            this.$message.error(res.message)
          }
        })
        .finally(() => (this.submitLoading = false))
    },
    //编辑
    edit(r, isView) {
      if (!r.formKey) {
        this.$message.warning('该流程信息未配置表单，请联系开发人员！')
        return
      }
      this.$refs.itilConfigProcess.initData(r)
      this.$refs.itilConfigProcess.title = '编辑'
    },
    //删除
    remove(r) {
      postAction(this.url.delByIds, { ids: r.id }).then((res) => {
        if (res.success) {
          this.$message.success(res.message)
          this.loadData()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    cancel(v) {
      this.cancelForm.id = v.id
      this.cancelForm.procInstId = v.procInstId
      this.modalCancelVisible = true
    },
    initDictConfig() {},
  },
}
</script>
<style lang="less" scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
/*表头样式*/
::v-deep .ant-table-thead > tr > th {
  text-align: center;
  white-space: nowrap;
}

/*内容对齐方式、省略显示*/
::v-deep .ant-table-tbody > tr > td {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;

  &:first-child,
  &:nth-child(2),
  &:nth-child(4),
  &:nth-child(5),
  &:nth-child(6),
  &:nth-child(7),
  &:nth-child(8) {
    text-align: center;
  }

  &:nth-child(3) {
    text-align: left;
  }
}
</style>