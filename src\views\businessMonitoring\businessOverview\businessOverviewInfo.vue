<template>
  <div class='top-box'>
    <div class='col-container' v-if='!isOpeningPreview'>
      <div class='card-box1'>
        <div class='card'>
          <div class='title'>{{ businessInfo ? businessInfo.businessName : '' }}</div>
          <img src='~@/assets/return1.png' alt='' @click='goBack'>
        </div>

      </div>
      <div class='card-box2'>
        <div class='card'>
          <a-tabs class='business-tabs' :animated='false' default-active-key='1' :active-key='defaultActiveKey' @change='callback'>
            <a-tab-pane key='1' tab='业务概览'>
              <div class='table-operator table-operator-style'>
                <a-button @click='refreshData'>刷新</a-button>
              </div>
              <basic-info class='basic-info' ref='basicInfo' v-if='!isResizing'></basic-info>
              <topology
                class='topology'
                ref='topology'
                @resize='resize'
                @OpenPreview='OpenPreview'></topology>
            </a-tab-pane>
            <a-tab-pane key='2' tab='进程监控'>
              <process-monitor :business-info='businessInfo'></process-monitor>
            </a-tab-pane>
          </a-tabs>
      </div>
      </div>
    </div>
    <associated-device-modal
      v-else-if='isOpeningPreview'
      :data='deviceInfo'
      :is-editing='true'
      :render-states='renderStates'>
    </associated-device-modal>
  </div>
</template>

<script>
import basicInfo from './modules/businessBasicInfo.vue'
import topology from './modules/associatedTopology.vue'
import { ajaxGetDictItems, getDictItemsFromCache } from '@api/api'
import associatedDeviceModal from '@/views/devicesystem/deviceshow/DeviceInfoModal'
import processMonitor from '@views/businessMonitoring/businessOverview/modules/processMonitor.vue'
import { getAction } from '@api/manage'


export default {
  name: 'businessOverviewInfo',
  components: {
    processMonitor,
    basicInfo,
    topology,
    associatedDeviceModal
  },
  props: {
    data: {
      type: Object,
      required: true,
      default: null
    }
  },
  data() {
    return {
      defaultActiveKey: '1',
      businessTypeList: [],
      businessInfo: null,
      isResizing: false,
      isOpeningPreview: false,
      renderStates: {
        showBaseInfo: true,
        showStateInfo: true,
        showDeviceFunction: true,
        showJournal: true,
        showAlarm: true,
        showDeviceAlarm: true
      },
      deviceInfo: {},
      pageParam: {},
      listPage: false,
      url: {
        queryOne: '/business/info/queryOne'
      }
    }
  },
  created() {
    this.initDictData('businessTypeList', 'business_type')
  },
  mounted() {
    this.updateTopoList(this.data)
  },
  methods: {
    goBack() {
      this.$parent.pButton1(0)
    },
    /*获取字典数据*/
    initDictData(dictOption, dictCode) {
      if (dictCode != null && dictCode != '') {
        //优先从缓存中读取字典配置
        if (getDictItemsFromCache(dictCode)) {
          this[dictOption] = getDictItemsFromCache(dictCode)
          return
        }

        //根据字典Code, 初始化字典数组
        ajaxGetDictItems(dictCode, null).then((res) => {
          if (res.success) {
            this[dictOption] = res.result
          }
        })
      }
    },
    /*通过value值，从字典数据中获取text*/
    getBusinessText() {
      let txt = ''
      for (let i = 0; i < arguments[1].length; i++) {
        if (arguments[1][i].value == arguments[0]) {
          txt = arguments[1][i].text
          break
        }
      }
      return txt
    },
    resize() {
      this.isResizing = !this.isResizing
      if (!this.isResizing) {
        this.$nextTick(() => {
          this.$refs.basicInfo.show(this.businessInfo)
        })
      }
    },
    OpenPreview(deviceInfo) {
      this.isOpeningPreview = true
      this.deviceInfo = deviceInfo.data
      this.pageParam = deviceInfo.pageParam
      this.listPage = deviceInfo.listPage
    },
    pButton1(index) {
      this.isOpeningPreview = false
      this.deviceInfo = {}
      this.updateTopoList(this.businessInfo)
    },
    refreshData() {
      getAction(this.url.queryOne, { id: this.businessInfo.id }).then((res) => {
        if (res.success) {
          this.updateTopoList(res.result)
        } else {
          this.$message.warning(res.message)
        }
      }).catch((err) => {
        this.$message.warning(err.message)
      })
    },
    updateTopoList(data) {
      this.$nextTick(() => {
        this.businessInfo = JSON.parse(JSON.stringify(data))
        this.businessInfo.businessTypeText = this.getBusinessText(this.businessInfo.businessType, this.businessTypeList)
        if (!this.isResizing) {
          this.$refs.basicInfo.show(this.businessInfo)
        }
        this.$refs.topology.show(this.businessInfo, this.pageParam, this.listPage)
      })
    },
    callback(key) {
      this.defaultActiveKey = key
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';

.top-box {
  height: 100%;

  .col-container {
    height: 100%;
    overflow: hidden;
    overflow-y: auto;

    .card-box1 {
      background: #fff;
      padding: 24px;
      border-radius: 2px;
      margin-bottom: 16px;

      .card{
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-flow: row nowrap;

        .title {
          display: inline-block;
          width: calc(100% - 40px);
          font-family: PingFangSC-Medium;
          font-size: 18px;
          font-weight: 600;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        img {
          margin-left: 20px;
          width: 20px;
          height: 20px;
          cursor: pointer;
        }
      }
    }

    .card-box2 {
      border-radius: 2px;
      background: #ffffff;
      //height: calc(100% - 75px - 16px);
      padding: 0px 0px 24px 24px;

      .card {
        height: 100%;
        padding-right: 24px;

        .basic-info {
          margin-bottom: 24px;
        }

        .topology {
          min-height: 334px;
        }
      }
    }
  }
}


::v-deep .ant-tabs .business-tabs {
  height: 100%;
  background-color: white;
  padding: 8px 0px 0px;
  border-radius: 3px;

  .ant-tabs-content {
    height: calc(100% - 64px);
    overflow: hidden;
    .ant-tabs-tabpane-active {
      height: 100%;
      padding-right: 1px;
       overflow-y: auto;
       overflow-x: hidden;
    }
  }
  .com{
    height: 100%;
    /* overflow-y: auto;
     overflow-x: hidden;*/
  }
}
</style>