<template>
  <div class='zr-business-view' ref='zrBusinessView'>
    <div class='business-view-left'>
      <div class='business-view-left-top' ref='lefTop'>
        <zr-comp-businesses  :systemList="systemList"></zr-comp-businesses>
      </div>
      <div v-if='leftBottomH' class='business-view-left-bottom' :style='{height:leftBottomH}'>
        <div class='business-view-left-middle'>
          <zr-comp-support :softwares="softwares"></zr-comp-support>
        </div>
        <div class='business-view-left-bottom'>
          <zr-comp-hardware :hardwares="hardwares"></zr-comp-hardware>
        </div>
      </div>
    </div>
    <div class='business-view-center'>
      <zr-status-info :deptId='deptId'></zr-status-info>
      <div class='topo-box'>
        <vis-edit v-show='topoId' ref='visEdit' :blurId='blurId' topoBgByTheme label-color='#fff' operate='show' @emitExceptionNodes="setFocusInfo"></vis-edit>
      </div>
      <div class='legend-block'>
        <zr-focus-info scene='unitNode' v-if='focusInfo' :infoText="focusInfo"></zr-focus-info>
        <div class='legend-block-list'>
          <div class='legend-block-list-item' v-for='item in businessStatus' :key='item.value'>
            <div class='legend-block-list-item-icon' :style='{backgroundColor:item.color}'></div>
            <div class='legend-block-list-item-text'>{{item.label}}</div>
          </div>
        </div>
      </div>
    </div>
    <div class='business-view-right'>
      <div ref='rightTop'>
        <div class='business-view-right-top' ref='businessPosition'>
          <zr-comp-position :topoId='topoId' :posList="posInfo" @changeBlur='changeBlur'></zr-comp-position>
        </div>
        <div class='business-view-right-middle' ref='businessManager'>
          <zr-comp-manager :managerList="managerList"></zr-comp-manager>
        </div>
      </div>
      <div v-if='rightBottomH' class='business-view-right-bottom' ref='businessOperations'
           :style='{height:rightBottomH}'>
        <zr-comp-operations :operatorList="operatorList"></zr-comp-operations>
      </div>
    </div>
  </div>
</template>
<script>
import resizeObserverMixin from '@views/statsCenter/com/resizeObserverMixin'
import ZrCompBusinesses from '@views/zrBigscreens/zrComprehensive/modules/ZrCompBusinesses.vue'
import ZrCompHardware from '@views/zrBigscreens/zrComprehensive/modules/ZrCompHardware.vue'
import ZrCompSupport from '@views/zrBigscreens/zrComprehensive/modules/ZrCompSupport.vue'
// import ZrCompPosition from '@views/zrBigscreens/zrComprehensive/modules/ZrCompPosition.vue'
import ZrCompPosition from '@views/zrBigscreens/zrBusiness/modules/ZrBusinessPosition.vue'
import ZrCompManager from '@views/zrBigscreens/zrComprehensive/modules/ZrCompManager.vue'
import ZrCompOperations from '@views/zrBigscreens/zrComprehensive/modules/ZrCompOperations.vue'
import VisEdit from '@views/topo/nettopo/modules/VisEdit.vue'
import { businessStatus } from '@views/zrBigscreens/modules/zrUtil'
import { organizations } from '@/views/zrBigscreens/modules/zrOrganizations'
import { flatTreeData } from '@/utils/util'
import ZrStatusInfo from '@views/zrBigscreens/zrCompNational/modules/ZrStatusInfo.vue'
import ZrFocusInfo from '@views/zrBigscreens/zrCompNational/modules/ZrFocusInfo.vue'
import { getAction, postAction, httpAction } from '@/api/manage'
export default {
  name: 'businessIndex',
  components: {
    VisEdit,
    ZrCompBusinesses,
    ZrCompSupport,
    ZrCompHardware,
    ZrCompPosition,
    ZrCompManager,
    ZrCompOperations,
    ZrStatusInfo,
    ZrFocusInfo
  },
  mixins: [resizeObserverMixin],
  data() {
    return {
      centerH: '',
      centerPd: "",
      legndBottom:"",
      leftBottomH: 0,
      rightBottomH: 0,
      businessStatus,
      topoId: window.ZrOrgTopoId || '1853673361481719810', // 默认拓扑图ID
      deptId: "",
      systemList:[],
      softwares:[],
      hardwares:[],
      focusInfo:"",
      blurId:"",
      managerList:[],
      operatorList:[],
      posInfo:[],
    }
  },
  created() {
    this.deptId = this.$route.query.deptId
    this.topoId = this.$route.query.topoId
    this.getBusinesses()
  },
  mounted() {
    this.getTopoId()
    // this.$refs.visEdit.createTopo(this.topoId)
  },
  methods: {
    changeBlur(e) {
      this.blurId = e
    },
    //根据单位获取拓扑图id
    getTopoId(){
      this.$refs.visEdit.createTopo(this.topoId)
      this.getDeviceInfo()
     /* getAction("/monitor/situation/getNetTopoByDeptId",{ deptId:this.deptId}).then(res=>{
        if(res.success && res.result){
          this.topoId = res.result.id

        }
      })*/
    },
    //根据拓扑获取设备信息
    getDeviceInfo(){
      getAction("/monitor/situation/getPositionAndManagerInfoByTopoId",{topoId:this.topoId}).then(res=>{
        if(res.success && res.result){
          let posData = res.result.devPositionList || []
          this.posInfo = posData.map(el=>{
            let position = ""
            if(el.positionInfo){
              position = el.positionInfo.roomName + "_" + el.positionInfo.cabinetName
            }
            return {
              id:el.id,
              deviceCode:el.deviceCode,
              position:position,
            }
          })
          this.managerList = res.result.managerList || []
          this.operatorList = res.result.operatorList || []
        }
      })
    },
    //设置关注信息
    setFocusInfo(infoList){
      this.focusInfo = ""
      let tem = infoList.splice(0,3)
      for(let i = 0;i<tem.length;i++){
        if(this.focusInfo == ""){
          this.focusInfo = `${tem[i].name}（${tem[i].statusText}）`
        }else{
          this.focusInfo += `、${tem[i].name}（${tem[i].statusText}）`
        }
      }
    },
    //根据获取应用系统，软件、硬件
    getBusinesses(){
      getAction('/monitor/situation/getBusinessListByDeptId',{deptId:this.deptId,withMoreInfo:"true",pageNo:1, pageSize:-1}).then(res => {
        // console.log("根据获取应用系统，软件、硬件 === ",res)
        if (res.success && res.result && res.result.businessList) {
          this.systemList = res.result.businessList.records.map(el=>{
            // if(el.healthLevel){
            //
            // }
            el.status = 0;
            return el
          })
          this.softwares = res.result.soft
          this.hardwares = res.result.hard
        }
      })
    },
    //监听页面缩放 更新中间区域高度
    resizeObserverCb() {
      // let hScaleValue = window.innerHeight / 1080
      // this.centerH = `calc(100% - (70px * ${hScaleValue}))`
      // this.centerPd = `calc(100px * ${hScaleValue})`
      // this.legndBottom = `calc(60px * ${hScaleValue})`
      let leftTopRect = this.$refs.lefTop.getBoundingClientRect()
      this.leftBottomH = `calc(100% - ${leftTopRect.height}px)`
      let rightTopRect = this.$refs.rightTop.getBoundingClientRect()
      this.rightBottomH = `calc(100% - ${rightTopRect.height}px)`
      // 重新计算拓扑图画布
      if (this.$refs.visEdit) {
        let timer = setTimeout(() => {
          clearTimeout(timer)
          timer = null
          this.$refs.visEdit.resizeTopo()
        }, 80)
      }

    }
  }
}
</script>

<style scoped lang='less'>
.zr-business-view {
  padding-left: calc(33 / 19.2 * 1vw);
  padding-right: calc(33 / 19.2 * 1vw);
  display: flex;
  align-items: center;
  justify-content: space-around;
  height: 100%;

  .business-view-left {
    width: 25%;
    height: 100%;
    //background-image: url(/zrBigScreen/zrBusiness/businessLeftBg.png);
    //background-size: 100% 100%;
    //background-repeat: no-repeat;
    padding: 0px 0px 0px 0px;
    display: flex;
    flex-direction: column;

    .business-view-left-bottom {
      display: flex;
      flex-direction: column;

      .business-view-left-middle {
        height: 50%
      }

      .business-view-left-bottom {
        height: 50%;
      }
    }
  }

  .business-view-center {
    width: 50%;
    height: 100%;
    /*background-image: url(/zrBigScreen/zrBusiness/businessCenterBg.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;*/
    padding: 0px 12px;
    position: relative;
    .topo-box{
      margin-top: 8px;
      height: calc(100% - 118px);
    }
    .legend-block {
      position: absolute;
      bottom: 0px;
      display: flex;
      justify-content: center;
      flex-direction: column;
      align-items: center;
      width:100%;
      .legend-block-list {
        display: flex;
        .legend-block-list-item {
          display: flex;
          align-items: center;
          font-weight: 400;
          font-size: 14px;
          padding: 0 20px;
          color: #E3E7EF;
          .legend-block-list-item-icon{
            width:14px;
            height: 14px;
            margin-right: 12px;
          }
        }
      }
    }
  }

  .business-view-right {
    width: 25%;
    height: 100%;
  /*  background-image: url(/zrBigScreen/zrBusiness/businessRightBg.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;*/
    padding: 0px 0px 0px 0px;
    display: flex;
    flex-direction: column;
  }
}
</style>