<template>
  <j-modal :title="title" :width="modalWidth" :visible="visible" :centered="true" switchFullscreen
    :destroyOnClose="true" @ok="handleOk" @cancel="handleCancel" cancelText="关闭">
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="名称">
          <a-input placeholder="请输入标签名称" v-decorator="['tagName', validatorRules.tagName]" :allowClear="true"
            autocomplete="off" />
        </a-form-item>
        <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol1" label="颜色">
          <a-input placeholder="请输入标签颜色" v-decorator="['tagColor', validatorRules.tagColor]" autocomplete="off"
            type="color" />
        </a-form-item>
        <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="参与统计">
          <a-radio-group v-decorator="['isStatistic', validatorRules.isStatistic]">
            <a-radio value="1">是</a-radio>
            <a-radio value="0">否</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="描述">
          <a-input placeholder="请输入标签描述" v-decorator="['description', validatorRules.description]" :allowClear="true" autocomplete="off" />
        </a-form-item>
      </a-form>
    </a-spin>
  </j-modal>
</template>

<script>
  import pick from 'lodash.pick'
  import {
    httpAction,
    getAction
  } from '@/api/manage'
  import JDictSelectTag from '@/components/dict/JDictSelectTag'

  export default {
    name: 'TagModal',
    components: {
      JDictSelectTag,
    },
    data() {
      return {
        title: '',
        modalWidth: '800px',
        visible: false,
        model: {},
        labelCol: {
          xs: {
            span: 24,
          },
          sm: {
            span: 5,
          },
        },
        wrapperCol: {
          xs: {
            span: 24,
          },
          sm: {
            span: 16,
          },
        },
        wrapperCol1: {
          xs: {
            span: 24,
          },
          sm: {
            span: 2,
          },
        },
        confirmLoading: false,
        form: this.$form.createForm(this),
        validatorRules: {
          tagName: {
            rules: [
              { required: true, message: '请输入标签名称' },
              { max:30,message:'标签名称长度在30个字符之间'}
           ],
          },
          description: {
            rules: [
              { required: false },
              { max:50,message:'描述长度在50个字符之间'}
           ],
          },
          tagColor: {
            initialValue: '#000000',
          },
          isStatistic: {
            initialValue: '0',
          },
        },
        url: {
          add: '/utl/taginfo/add',
          edit: '/utl/taginfo/edit',
          getAllGroups: 'utl/taginfo/getAllTagGroup',
        },
        groupList: [],
      }
    },
    created() {},
    methods: {
      //获取所有标签组，后期添加标签时绑定所属标签组
      getAllGroups() {
        if (!this.url.getAllGroups) {
          this.$message.error('请设置url.getAllGroups!')
          return
        }
        getAction(this.url.getAllGroups, {
          tagName: ''
        }).then((res) => {
          if (res.success) {
            this.groupList = res.result
          } else {
            this.$message.warning(res.message)
          }
        })
      },

      //添加
      add() {
        this.edit({})
      },

      //编辑
      edit(record) {
        this.getAllGroups()
        this.form.resetFields()
        this.model = Object.assign({}, record)
        this.visible = true
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.model, 'tagName', 'tagColor', 'isStatistic','description'))
        })
      },

      //关闭
      close() {
        this.$emit('close')
        this.visible = false
      },

      //确认
      handleOk() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.model.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'put'
            }

            let formData = Object.assign(this.model, values)
            httpAction(httpurl, formData, method)
              .then((res) => {
                if (res.success) {
                  that.$message.success(res.message)
                  that.$emit('ok')
                } else {
                  that.$message.warning(res.message)
                }
              })
              .finally(() => {
                that.confirmLoading = false
                that.close()
              })
          }
        })
      },

      //取消
      handleCancel() {
        this.close()
      },
    },
  }
</script>

<style lang="less" scoped>
  @import '~@assets/less/normalModal.less';

  ::v-deep .two-words>div>label {
    letter-spacing: 4px;
  }

  ::v-deep .two-words>div>label::after {
    letter-spacing: 0px;
  }
</style>