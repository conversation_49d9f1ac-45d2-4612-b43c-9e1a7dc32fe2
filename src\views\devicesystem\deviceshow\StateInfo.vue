<template>
  <div :class='[showScroll?"scroll":""]'>
    <div >
      <a-row :gutter="24"  style='margin-right: 1px'>
        <div class="colorBox" style="margin-left: 12px;" v-if="deviceStatusList.length>0 || frontList.length>0 || alarmAmount.length>0">
          <span class="colorTotal">基本状态</span>
        </div>
        <a-col :xxl='6' :xl="8" :lg="12" :md="12" :sm="24" :style="{ marginBottom: '24px' }" v-for='(item,index) in deviceStatusList' :key='"device_"+index'>
          <chart-card
            :class="['cc-title', item.class]"
            :title="item.class==='alarm-status'?'告警状态':'设备状态'">
            <a-tooltip title="同步" slot="action">
              <a-icon type="reload" @click="getRunInfoData(deviceInfo.id, true)" />
            </a-tooltip>
            <div class="total">
              {{ item.text }}
            </div>
            <div :title='"信息更新时间: "+onLineTime'>
              <span slot="term">信息更新时间</span>
              {{ onLineTime }}
            </div>
          </chart-card>
        </a-col>

        <!-- 前置显示 -->
        <span v-for="(item,index) in frontList" :key="'front_'+index">
          <a-col :xxl='6' :xl="8" :lg="12" :md="12" :sm="24" :style="{ marginBottom: '24px' }">
            <chart-card
              :class="['cc-title', item.value[item.value.length - 1].value === '已连接' ? 'on-status' : 'out-status']"
              :title="item.name">
              <a-tooltip title="同步" slot="action">
                <a-icon type="reload" @click="getRunInfoData(deviceInfo.id, true)" />
              </a-tooltip>
              <div class="total">
                {{ item.value[item.value.length - 1].value }}
              </div>
              <div :title='"信息更新时间:"+item.value[item.value.length - 1].timestamp'>
                <span slot="term">信息更新时间</span>
                {{ item.value[item.value.length - 1].timestamp }}
              </div>
            </chart-card>
          </a-col>
        </span>

        <a-col :xxl='6' :xl="8" :lg="12" :md="12" :sm="24" :style="{ marginBottom: '24px' }" v-for="(item,index) in alarmAmount"
               :key="'alarm_'+index">
          <chart-card class="cc-title cc-serious" :title="item.levelName">
            <a-tooltip title="同步" slot="action">
              <a-icon type="reload" @click="getRunInfoData(deviceInfo.id, true)" />
            </a-tooltip>
            <div :style="{color:item.color,'font-size': '24px'}">
              <span>{{ item.repeatTimes }}</span><span style=" font-size: 18px"> 次</span>
            </div>
            <div :title='"最近时间:"+item.alarmTime'>
              <span slot="term">最近时间</span>
              {{ item.alarmTime }}
            </div>
          </chart-card>
        </a-col>
      </a-row>
      <!-- 各种协议数据 -->
      <div v-for="(item, index) in this.transferProtocolData" :key="'proto_'+index" style='margin-right: 12px'>
        <div class="colorBox">
          <span class="colorTotal">{{ item }}协议</span>
        </div>
        <a-row :gutter="24">
          <!--line/pie/gauge/text-->
          <span v-for="(items, index) in dataList" :key="'run_'+index" v-if="items.transferProtocol == item">
            <!-- 折线图/饼图/仪表盘/文本 卡片 -->
          <a-col :xxl='6' :xl="8" :lg="12" :md="12" :sm="24" :style="{ marginBottom: '24px' }" v-if="items.type != 'table'">
            <chart-card :class="[
                'cc-title',
                items.value[items.value.length - 1].value === '关机状态' ||
                items.value[items.value.length - 1].value === '未连接'
                  ? 'out-status'
                  : 'on-status',
              ]" :title="items.name" :code="items.code" :total="items.value[items.value.length - 1].value + ''" :unit="items.unit"
                        :chartData="items" :flagType="items.value[items.value.length - 1].type + ''">
              <a-tooltip title="同步" slot="action">
                <a-icon type="reload" @click="getRunInfoData(deviceInfo.id, true)" />
              </a-tooltip>
            </chart-card>
          </a-col>
          <!-- 表格卡片 -->
          <a-col :xxl='6' :xl="8" :lg="12" :md="12" :sm="24" :style="{ marginBottom: '24px' }" v-if="items.type == 'table'">
            <chart-card
              :class="['cc-title', items.value[items.value.length - 1].value === '未连接' ? 'out-status' : 'on-status']"
              :title="items.name" :code="items.code" :total="items.value[0].value[0].unit">
              <a-tooltip title="详情" slot="action" style="margin-right: 6px">
                <a-icon type="profile" @click="handleEdit(items)" />
              </a-tooltip>
              <a-tooltip title="同步" slot="action">
                <a-icon type="reload" @click="getRunInfoData(deviceInfo.id, true)" />
              </a-tooltip>

              <div class="table-item">
                <div class="item" v-if="items.value[0].value.length >= 1">
                  <span :title='items.value[0].value[0][0].name+":"+items.value[0].value[0][0].value+" "+items.value[0].value[0][0].unit'>
                    {{ items.value[0].value[0][0].name }}:{{ items.value[0].value[0][0].value }}
                    {{ items.value[0].value[0][0].unit }}</span>
                  <span :title='items.value[0].value[0][1].name+":"+items.value[0].value[0][1].value+" "+items.value[0].value[0][1].unit'>
                    {{ items.value[0].value[0][1].name }}:{{ items.value[0].value[0][1].value }}
                    {{ items.value[0].value[0][1].unit }}</span>
                </div>
                <div class="item" v-if="items.value[0].value.length >= 2">
                  <span :title='items.value[0].value[1][0].name+":"+items.value[0].value[1][0].value+" "+items.value[0].value[1][0].unit'>
                    {{ items.value[0].value[1][0].name }}:{{ items.value[0].value[1][0].value }}
                    {{ items.value[0].value[1][0].unit }}</span>
                  <span :title='items.value[0].value[1][1].name+":"+items.value[0].value[1][1].value+" "+items.value[0].value[1][1].unit'>
                    {{ items.value[0].value[1][1].name }}:{{ items.value[0].value[1][1].value }}
                    {{ items.value[0].value[1][1].unit }}</span>
                </div>
                <div style="margin-top: 10px;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;"
                     :title='"信息更新时间: "+items.value[0].timestamp'>
                  信息更新时间 : {{ items.value[0].timestamp }}
                </div>
              </div>
            </chart-card>
          </a-col>
        </span>
        </a-row>
      </div>
    </div>
    <state-detail-info-modal ref="modalForm"></state-detail-info-modal>
    <state-port-info-modal ref="portForm"></state-port-info-modal>
  </div>
</template>

<script>
import ChartCard from '@/components/ChartCard'
import ACol from 'ant-design-vue/es/grid/Col'
import ATooltip from 'ant-design-vue/es/tooltip/Tooltip'
import Trend from '@/components/Trend'
import MiniArea from '@/components/chart/MiniArea'
import Bar from '@/components/chart/Bar'
import MiniBar from '@/components/chart/MiniBar'
import MiniProgress from '@/components/chart/MiniProgress'
import {
  getStateInfo
} from '@/api/device'
import {
  httpAction,
  getAction,
  postAction
} from '@/api/manage'
import {
  JeecgListMixin
} from '@/mixins/JeecgListMixinNoInit'
import stateDetailInfoModal from '../modules/StateDetailInfoModal.vue'
import statePortInfoModal from '../modules/StatePortInfoModal'
import {
  ajaxGetDictItems,
  getDictItemsFromCache
} from '@/api/api'

import axios from 'axios'
export default {
  name: 'stateInfo',
  mixins: [JeecgListMixin],
  props:{
    showScroll:{
      type: Boolean,
      required:false,
      default:true,
    }
  },
  data() {
    return {
      transferProtocolData: [],
      url: {
        getRunInfo: '/alarm/alarmTemplate/findAlarmTimesByDevId', //获取设备的运行信息
        getPortInfo: '/topo/device/status', //获取设备端口流量信息
        queryRelaTemplate: '/device/deviceInfo/selectConnectInfo',
      },
      deviceStatusList: [], //设备状态
      onLineTime: '', //设备上线时间
      alarmAmount: [],
      dataList: [],
      tableList: [],
      frontList: [],
      portInfo: [],
      nowDate: new Date(),
      ENCNList: null,
      CZ_Data: window._CONFIG['customization'],
      deviceInfo:{}
    }
  },
  components: {
    ATooltip,
    ACol,
    ChartCard,
    MiniArea,
    MiniBar,
    MiniProgress,
    Bar,
    Trend,
    'state-detail-info-modal': stateDetailInfoModal,
    'state-port-info-modal': statePortInfoModal,
  },
  watch: {
    deviceInfo(val, oldVal) {
      this.init(val)
    }
  },
  methods: {
    show(record) {
      this.deviceInfo = record
    },
    init(record) {
      this.$nextTick(() => {
        this.CZ_Data = window._CONFIG['customization']
        this.getRunInfoData(record.id, false)
        //this. getRunPortData(record.id,false);
        // this.getDictENCNInfo(record.id)
      })
    },
    getRunInfoData(deviceId, flag = false) {
      //判断是否属于定制，且是否是外网设备
      //遵义定制，访问外网设备
      if (this.CZ_Data && this.CZ_Data.cz_zunyi && this.CZ_Data.cz_zunyi.internetFlag == this.deviceInfo
        .internetFlag) {
        let urlStart = this.CZ_Data.cz_zunyi.internetServiceURL
        this.getInternetRunInfo(urlStart + this.url.getRunInfo, deviceId, flag)
      } else {
        //默认访问内网设备
        this.getRunInfo(deviceId, flag)
      }
    },
    getRunPortData(deviceId, flag = false) {
      //判断是否属于定制，且是否是外网设备
      //遵义定制，访问外网设备
      if (this.CZ_Data && this.CZ_Data.cz_zunyi && this.CZ_Data.cz_zunyi.internetFlag == this.deviceInfo
        .internetFlag) {
        let urlStart = this.CZ_Data.cz_zunyi.internetServiceURL
        this.getInternetPortInfo(urlStart + this.url.getPortInfo, deviceId)
      } else {
        //默认访问内网设备
        this.getPortInfo(deviceId)
      }
    },

    getInternetRunInfo(url, deviceId, flag = false) {
      axios({
        // url:'http://*************:8000/category',
        url: url,
        method: 'get',
        params: {
          deviceId: deviceId,
        },
      }).then((res) => {
        this.initRunInfo(res.data, flag)
      })
    },
    getInternetPortInfo(url, deviceId) {
      axios({
        // url:'http://localhost:8091/insight-api'+this.url.getPortInfo,
        url: url,
        method: 'post',
        params: {
          id: deviceId,
        },
      }).then((res) => {
        this.initPortInfo(res.data)
      })
    },

    getRunInfo(deviceId, flag = false) {
      //获取设备的运行信息
      getAction(this.url.getRunInfo, {
        deviceId: deviceId
      }).then((res) => {
        this.initRunInfo(res, flag)
      })
    },
    getPortInfo(deviceId) {
      //获取设备的运行信息
      postAction(this.url.getPortInfo, {
        id: deviceId
      }).then((res) => {
        this.initPortInfo(res)
      })
    },

    initRunInfo(res, flag = false) {
      if (res.success) {
        let info = res.result
        this.deviceStatusList.length=0
        this.deviceStatusList.push({
          text :info.status==1? '在线':'离线',
          class:info.status==1?'on-status':"out-status"
        })
        if(info.alarmStatus==1){
          this.deviceStatusList.push({
            text :'告警',
            class:'alarm-status'
          })
        }

        this.onLineTime = info.upTime
        this.alarmAmount = info.alarmAmount.sort((a, b) => {
          return a.alarmLevel - b.alarmLevel
        })
        this.dataList = info.dataList
        this.tableList = info.tableList
        this.frontList = info.frontList
        info.momgProertyMetadata.forEach((e) => {
          this.dataList.forEach((ele, index) => {
            if (e.code == ele.code) {
              this.$set(this.dataList[index], 'transferProtocol', e.transferProtocol)
            }
          })
        })
        // if (this.dataList.serial == this.dataList.serial) {
        //   this.dataList.sort((a, b) => {
        //     return a.createTime - b.createTime
        //   })
        // } else {
        this.dataList.sort((a, b) => {
          if (a.serial == b.serial) {
            return a.createTime - b.createTime
          } else {
            return a.serial - b.serial
          }
        })
        // }
        let arr = []
        this.dataList.forEach((ele) => {
          arr.push(ele.transferProtocol)
        })
        this.transferProtocolData = new Set(arr)
        if (flag) {
          this.$message.success('同步成功')
        }
      } else {
        if (flag) {
          this.$message.warn('同步失败')
        }
      }
    },
    initPortInfo(res) {
      if (res.success) {
        if (res.result && res.result.portInfo) {
          this.portInfo = [...res.result.portInfo]
          this.nowDate = new Date()
        }
      }
    },

 /*   getDictENCNInfo() {
      ajaxGetDictItems('devicestatus_EN_CN').then((res) => {
        this.ENCNList = res.result
      })
    },*/

    transfor(str) {
      let str1 = ''
      this.ENCNList.forEach((element) => {
        if (element.title == str) {
          str1 = element.value
        }
      })
      return str1
    },
    handleEdit: function (record) {
      this.$refs.modalForm.edit(record)
      this.$refs.modalForm.title = '查看更多'
      this.$refs.modalForm.disableSubmit = true
    },
    handleDetail(record) {
      this.$refs.portForm.edit(record)
      this.$refs.portForm.title = '查看更多'
      this.$refs.portForm.disableSubmit = false
    },
  },
}
</script>

<style lang='less' scoped>
.scroll{
  height: 100%;
  overflow: hidden;
  overflow-y: auto;
}
.cc-title {
  background: #ffffff;
  border: 1px solid #f5f5f5;
  box-shadow: 3px 3px 9px -1px rgba(0, 0, 0, 0.2);
}

.chart-card-title {
  font-family: PingFangSC-Regular !important;
  color: rgba(0, 0, 0, 0.65) !important;
}

.on-status .total {
  font-family: PingFangSC-Regular;
  font-size: 24px;
  color: #139b33;
}

.out-status .total {
  font-family: PingFangSC-Regular;
  font-size: 24px;
  color: #868686;
}

.alarm-status .total {
  font-family: PingFangSC-Regular;
  font-size: 24px;
  color: #ff871d;
}

.cc-serious .total {
  font-family: PingFangSC-Regular;
  font-size: 24px;
  color: #df1a1a;
}

.cc-normal .total {
  font-family: PingFangSC-Regular;
  font-size: 24px;
  color: #ffb300;
}

.content-fix {
  font-family: PingFangSC-Regular;
  font-size: 14px;
}

.table-item {
  display: flex;
  flex-wrap: wrap;
}

.table-item .item {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}

.table-item .item span {
  width: 49%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.table-item .item span:nth-child(2n + 0) {
  margin-left: 2%;
}

.chart-card-footer {
  display: none;
}
.colorBox {
  margin-top: 20px;
  margin-bottom: 10px;

  .colorTotal {
    padding-left: 7px;
    font-size: 14px;
    border-left: 4px solid #1e3674;
  }
}

//.colorBox {
//  margin-bottom: 10px;
//  .colorTotal {
//    padding-left: 7px;
//    border-left: 4px solid #1e3674;
//  }
//}

</style>