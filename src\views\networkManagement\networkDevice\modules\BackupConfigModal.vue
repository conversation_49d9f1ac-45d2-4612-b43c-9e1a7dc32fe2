<template>
  <j-modal
    :title='title'
    :width='width'
    :centered='true'
    :visible='visible'
    :destroyOnClose='true'
    switchFullscreen
    cancelText='关闭'
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @ok='handleOk'
    @cancel='handleCancel'
  >
    <a-spin :spinning='confirmLoading'>
      <j-form-container>
        <a-form-model ref='form' :model='model' :rules='validatorRules' slot='detail'>
          <a-row justify='space-between' type='flex'>
            <a-col :span='24' v-if='actionType==="backUp"'>
              <a-form-model-item label='配置方案' prop='configureManageId' v-bind='formItemLayout'>
                <a-select
                  v-model='model.configureManageId'
                  :allow-clear='true'
                  autocomplete='off'
                  placeholder='请选择配置方案'>
                  <a-select-option v-for='item in solutionList' :key='"solu_"+item.id' :label='item.taskName' :value='item.id'>
                    {{ item.taskName }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
              <a-form-model-item label='说明' prop='description' v-bind='formItemLayout'>
                <a-textarea
                  v-model='model.description'
                  :auto-size='{ minRows: 5, maxRows: 5 }'
                  :allowClear='true'
                  autocomplete='off'
                  placeholder='请输入说明信息' />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </j-form-container>
    </a-spin>
  </j-modal>
</template>
<script>
import { getAction, httpAction } from '@api/manage'
import { ajaxGetDictItems, getDictItemsFromCache } from '@api/api'

export default {
  name: 'BackupConfigModal',
  data() {
    return {
      title: '说明',
      width: '600px',
      disableSubmit: false,
      actionType:'',
      visible: false,
      confirmLoading: false,
      formItemLayout: {
        labelCol: {
          xs: {span: 24},
          sm: {span: 6}
        },
        wrapperCol: {
          xs: {span: 24},
          sm: {span: 16}
        }
      },
      model: {},
      solutionList:[],
      validatorRules: {
        configureManageId: [
          { required: true,message:'请选择配置方案！' }
        ],
        description: [
          { required: false, validator: this.descriptionValidate }
        ]
      },
      url:{
        configureManage: '/net/device/configureManageList',
        bacOrRec:'/net/device/devSettingBacOrRec',
      },
    }
  },

  methods: {
    descriptionValidate(rule, value, callback) {
      if (value && value.length > 500) {
        callback('说明信息长度应在 0-500 之间')
      } else {
        callback()
      }
    },
    addRemark(deviceInfo,record) {
      this.visible=true
      this.model={}
      this.model.deviceId=deviceInfo.id
      this.model.productId=deviceInfo.productId
      if(record){
        this.model.backRecordId=record.id
        this.model.configureManageId=record.configureManageId
      }else {
        getAction(this.url.configureManage,{
          productId:deviceInfo.productId,
          pageNo : 1,
          pageSize : -1
        }).then((res)=>{
          this.solutionList=res.result.records
        })
      }
    },
    close() {
      this.visible = false
    },
    handleOk() {
      this.$refs.form.validate((err, values) => {
        if (err) {
          this.confirmLoading = true
          let param = {
            deviceId: this.model.deviceId,
            configureManageId: this.model.configureManageId,
            type: this.actionType,
            description: this.model.description
          }
          if(this.actionType=='recovery'){
            param.backRecordId= this.model.backRecordId
          }
          getAction(this.url.bacOrRec, param).then((res) => {
            if (res.success) {
              this.$message.success(res.message)
              this.close()
              this.$emit('ok')
            }
            else {
              this.$message.warning(res.message)
            }
            this.confirmLoading = false
          }).catch((err) => {
            this.confirmLoading = false
            this.$message.error(err.message)
            this.close()
          })
        }
      })
    },
    handleCancel() {
      this.close()
    },
  }
}
</script>
<style scoped lang='less'>
@import '~@assets/less/normalModal.less';
</style>