<template>
  <div style="height: 100%">
    <keep-alive exclude='transportProtocolDetails'>
      <component :is="pageName" :data="data" style="height:100%" />
    </keep-alive>
  </div>
</template>
<script>
  import transportProtocolList from './transportProtocolList'
  import transportProtocolDetails from './modules/transportProtocolDetails'
  export default {
    name: 'transportProtocolManage',
    data() {
      return {
        isActive: 0,
        data: {}
      }
    },
    components: {
      transportProtocolList,
      transportProtocolDetails,
    },
    created() {
      this.pButton1(0)
    },
    //使用计算属性
    computed: {
      pageName() {
        switch (this.isActive) {
          case 0:
            return 'transportProtocolList';

          default:
            return 'transportProtocolDetails';
        }
      },
    },
    methods: {
      pButton1(index) {
        this.isActive = index;
      },
      pButton2(index, item) {
        this.isActive = index;
        this.data = item
      }
    }
  }
</script>