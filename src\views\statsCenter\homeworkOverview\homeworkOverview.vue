<template>
  <div class="terminal-statistics" ref="terminalStatistics">
    <div class="terminal-statistics-inside">
      <div class="terminal-header">
        <div class="terminal-class" v-for="(item, idx) in terminals" :key="idx">
          <img :src="item.src" alt="" />
          <div class="terminal-class-right">
            <div class="terminal-num">{{ item.value }}</div>
            <div class="terminal-class-name">{{ item.name }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="homework" id="homeworkOverview"></div>
  </div>
</template>

<script>
  import resizeObserverMixin from '@views/statsCenter/com/resizeObserverMixin'
  import echarts from 'echarts'
  import {
    rect
  } from '@antv/x6/lib/registry/connection-point/rect'
  import {
    getAction
  } from '@/api/manage'
  export default {
    name: 'homeworkOverview',
    mixins: [resizeObserverMixin],
    data() {
      return {
        terminals: [{
            src: require('/public/statsCenter/terminal/terminals.png'),
            name: '作业数量',
            value: 0,
          },
          {
            src: require('/public/statsCenter/terminal/onlineCur.png'),
            name: '场景数据',
            value: 0,
          },
          {
            src: require('/public/statsCenter/terminal/offline.png'),
            name: '脚本数量',
            value: 0,
          },
          {
            src: require('/public/statsCenter/terminal/online.png'),
            name: '适配器数量',
            value: 0,
          },
          {
            src: require('/public/statsCenter/terminal/domestic.png'),
            name: '软件数量',
            value: 0,
          },
          {
            src: require('/public/statsCenter/terminal/alarm.png'),
            name: '设备数量',
            value: 0,
          },
        ],
        url: {
          taskTrend: 'autoControl/task/overview/taskTrend',
        }
      }
    },
    created() {
      this.getTerminalNum()
      this.getTask()
      this.$nextTick(() => {
        this.homeworkOverview()
      })
    },
    mounted() {},
    methods: {
      getTerminalNum() {
        getAction('autoControl/task/overview/statistics').then((res) => {
          if (res.success && res.result) {
            this.terminals[0].value = res.result.softwareNumber || 0
            this.terminals[1].value = res.result.sceneData || 0
            this.terminals[2].value = res.result.scriptNumber || 0
            this.terminals[3].value = res.result.adaptersNumber || 0
            this.terminals[4].value = res.result.softwareNumber || 0
            this.terminals[5].value = res.result.deviceNumber || 0
          }
        })
      },
      getTask() {
        getAction(this.url.taskTrend, {}).then((res) => {
          console.log(res.result);
          let data = res.result

          this.homeworkOverview(data)
        })
      },
      homeworkOverview(data) {
        let time = []
        let num = []
        data.forEach((ele) => {
          time.push(ele.date1)
          num.push(ele.taskCount)
        })
        let myChart = this.$echarts.init(document.getElementById('homeworkOverview'))
        myChart.setOption({
          color: ['#00B3F9'],
          tooltip: {
            trigger: 'axis',
            backgroundColor: 'rgba(13, 64, 71, 0.50)',
            borderColor: 'rgba(143, 225, 252, 0.60)',
            padding: 8,
            textStyle: {
              color: '#fff',
            }
          },
          legend: {
            data: ['作业执行数量'],
            icon: 'circle',
            itemWidth: 10,
            itemHeight: 10,
            top: 0,
            right: 50,
            textStyle: {
              fontSize: 14,
              color: '#ffffff'
            }
          },
          grid: {
            left: '5%',
            right: '5%',
            bottom: '8%',
            top: '6%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            axisLine: {
              show: true,
              lineStyle: {
                color: '#ffffff'
              }
            },
            axisTick: {
              show: true,
              alignWithLabel: true,
            },
            axisLabel: {
              textStyle: {
                color: '#ffffff', //更改坐标轴文字颜色
                fontSize: 12 //更改坐标轴文字大小
              }
            },
            data: time
          },
          yAxis: {
            splitLine: {
              show: true,
              lineStyle: {
                color: '#737A85',
                type: 'dashed'
              },
            },
            axisLabel: {
              textStyle: {
                color: '#ffffff', //更改坐标轴文字颜色
                fontSize: 12 //更改坐标轴文字大小
              }
            },
            axisLine: {
              show: false
            }
          },
          series: [{
              name: '作业执行数量',
              type: 'line',
              symbol: 'none',
              label: {
                show: true,
                position: 'right',
                color: '#ffffff'
              },
              emphasis: {
                focus: 'series'
              },
              data: num
            },

          ]
        })
        window.addEventListener('resize', () => {
          myChart.resize()
        })
      },
    },
  }
</script>

<style lang="less" scoped>
  .terminal-statistics {
    width: 100%;
    height: 100%;
    background: transparent;

    .terminal-statistics-inside {
      width: 1670px;
      height: 200px;
      margin: 0 auto;

      .terminal-header {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 125px;

        .terminal-class {
          display: flex;
          width: 250px;
          height: 70px;
          color: #fff;
          align-items: center;
          padding-right: 10px;
          border-right: 1px solid rgba(53, 172, 255, 0.56);
          margin-right: 50px;

          img {
            width: 102px;
            height: 70px;
          }

          .terminal-class-right {
            width: calc(100% - 102px);

            .terminal-num {
              font-size: 31px;
              font-family: DIN;
              font-weight: bold;
              color: #41bbff;
              line-height: 31px;
              background: linear-gradient(0deg, #61dffe 4.931640625%, #44a5ff 100%);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              margin-bottom: 10px;
            }

            .terminal-class-name {
              font-size: 16px;
              font-family: Source Han Sans CN;
              font-weight: 400;
              color: #ffffff;
              line-height: 24px;
            }
          }
        }

        .terminal-class:last-child {
          border: none;
          margin-right: 0px;
        }
      }
    }

    .homework {
      height: calc(100% - 200px);
      width: 60%;
      margin: 0 auto
    }
  }
</style>
