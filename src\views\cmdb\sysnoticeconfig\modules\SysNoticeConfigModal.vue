<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    :centered='true'
    switchFullscreen
    :destroyOnClose="true"
    @ok="handleOk"
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @cancel="handleCancel"
    cancelText="关闭">
    <sys-notice-config-form ref="realForm" @ok="submitCallback" :disabled="disableSubmit"></sys-notice-config-form>
  </j-modal>
</template>

<script>

  import SysNoticeConfigForm from './SysNoticeConfigForm'
  export default {
    name: 'SysNoticeConfigModal',
    components: {
      SysNoticeConfigForm
    },
    data () {
      return {
        title:'',
        width:'800px',
        visible: false,
        disableSubmit: false
      }
    },
    methods: {
      add () {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.add();
        })
      },
      edit (record) {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.queryById(record);
        })
      },
      close () {
        this.$emit('close');
        this.visible = false;
      },
      handleOk () {
        this.$refs.realForm.submitForm();
      },
      submitCallback(){
        this.$emit('ok');
        this.visible = false;
      },
      handleCancel () {
        this.close()
      }
    }
  }
</script>
<style scoped lang='less'>
::v-deep .ant-modal-body {
  padding: 24px 48px 24px 48px;
}

::v-deep .ant-modal {
  padding: 24px;
}

@media (max-width: 812px) {
  ::v-deep .ant-modal {
    max-width: calc(100vw - 12px);
    margin: 0;
  }
}
.j-modal-box.fullscreen {
  margin: 0 !important;
  max-width: 100vw !important;
  ::v-deep .ant-modal {
    max-width: 100vw !important;
    margin: 0;
  }
}
</style>