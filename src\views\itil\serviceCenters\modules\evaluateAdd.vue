<template>
  <a-modal
    title="添加评价"
    :width="width"
    :visible="visible"
    :confirmLoading="confirmLoading"
    switchFullscreen
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭"
  >
    <a-form :form="form">
      <a-row>
        <a-col :span="24">
          <a-form-item label="满意度" :labelCol="labelCol" :wrapperCol="wrapperCol" v-if="type != 's'">
            <a-col style="padding: 36px">
              <a-col :span="12" style="width: 41%">
                <a-rate
                  v-decorator="['satisf', validatorRules.rateValue]"
                  style="font-size: 36px; white-space: nowrap"
                  :allowClear="allowClear"
                  @change="handleRate"
                />
                <div style="text-align: center">{{ desc[rateNum - 1] }}</div>
              </a-col>
            </a-col>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="评价信息" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-textarea v-decorator="['describes']" rows="4" placeholder="请输入评价信息" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>

<script>
import pick from 'lodash.pick'
import { postAction, getAction, deleteAction } from '@/api/manage'
export default {
  name: 'evaluateAdd',
  components: {},
  data() {
    return {
      form: this.$form.createForm(this),
      desc: ['非常不满意', '不满意', '一般', '满意', '非常满意'],
      title: '',
      width: 950,
      visible: false,
      model: {},
      confirmLoading: false,
      allowClear: false,
      satisf: 1,
      type: '',
      rateNum: 0,
      reportId: '',
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      validatorRules: {
        rateValue: {
          rules: [{ required: true, message: '请选择满意度' }],
        },
      },
      url: {
        add: '/reportassess/itilReportAssessInfo/add',
      },
    }
  },
  methods: {
    add(e) {
      this.reportId = e.id
      this.type = e.depType
      this.edit({})
    },
    edit(record) {
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(pick(this.model, '', '', ''))
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let formData = Object.assign(this.model, values)
          formData.reportId = this.reportId
          formData.id = null
          formData.depType = this.type
          postAction(that.url.add, formData)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.visible = false
              that.confirmLoading = false
            })
        }
      })
    },
    handleCancel() {
      this.close()
    },
    handleRate(num) {
      this.rateNum = num
    },
  },
}
</script>

<style scoped>
.evaluPai {
  float: right;
  width: 100%;
  line-height: 24px;
}
</style>