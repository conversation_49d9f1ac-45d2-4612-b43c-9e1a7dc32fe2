<template>
  <div style="height:100%">
    <keep-alive exclude='ipAlarmDetails'>
      <component style="height:100%" :is="pageName" :data="data" />
    </keep-alive>
  </div>
</template>
<script>
  import ipAlarmList from './ipAlarmList'
  import ipAlarmDetails from './modules/ipAlarmDetails'
  export default {
    name: "ipAlarmManage",
    data() {
      return {
        isActive: 0,
        data: {},
      }
    },
    components: {
      ipAlarmList,
      ipAlarmDetails
    },
    created() {
      this.pButton1(0);
    },
    //使用计算属性
    computed: {
      pageName() {
        switch (this.isActive) {
          case 0:
            return "ipAlarmList";
          default:
            return "ipAlarmDetails";
        }
      }
    },
    methods: {
      pButton1(index) {
        this.isActive = index;
      },
      pButton2(index, item) {
        this.isActive = index;
        this.data = item
      }
    }
  }
</script>