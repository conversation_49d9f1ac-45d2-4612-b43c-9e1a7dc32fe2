<template>
  <a-row style='height: 100%'>
    <a-col style='height: 100%; display: flex; flex-direction: column'>
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0', marginRight: '12px' }" class='card-style'>
        <!-- 查询区域 -->
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref='row'>
              <a-col :span="spanValue">
                <a-form-item label="常用语">
                  <a-input :maxLength='maxLength' :allow-clear='true' autocomplete='off' placeholder='请输入常用语' v-model='queryParam.languageInfo' />
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="数据类型">
                  <a-select :allow-clear='true' autocomplete='off' placeholder='请选择数据类型' v-model='queryParam.status'>
                    <a-select-option :value="0">用户添加</a-select-option>
                    <a-select-option :value="1">系统内置</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="所属模块">
                  <a-select :allow-clear='true' autocomplete='off' placeholder='请选择所属模块' v-model='queryParam.module'>
                    <a-select-option :value="item.value" v-for="item in dictOptions">{{item.text}}</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <span class="table-page-search-submitButtons" style="overflow: hidden;">
                  <a-button icon="search" type="primary" @click="searchQuery">查询</a-button>
                  <a-button icon="reload" style="margin-left: 8px" @click="searchReset">重置</a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
        <!-- 查询区域-END -->
      </a-card>

      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <!-- 操作按钮区域 -->
        <div class="table-operator">
          <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
          <a-dropdown v-if="selectedRowKeys.length > 0">
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item key="1" @click="batchDel">删除</a-menu-item>
            </a-menu>
            <a-button style="margin-left: 8px"> 批量操作
              <a-icon type="down" />
            </a-button>
          </a-dropdown>
        </div>

        <!-- table区域-begin -->
        <div>
          <a-table ref="table" bordered rowKey="id" :columns="columns" :dataSource="dataSource"
            :pagination="ipagination" :loading="loading"
            :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}" @change="handleTableChange">

            <div slot="filterDropdown">
              <a-card>
                <a-checkbox-group @change="onColSettingsChange" v-model="settingColumns" :defaultValue="settingColumns">
                  <a-row style="width: 400px">
                    <template v-for="(item,index) in defColumns">
                      <template v-if="item.key!='rowIndex'&& item.dataIndex!='action'">
                        <a-col :span="12" :key="index">
                          <a-checkbox :value="item.dataIndex">
                            <j-ellipsis :value="item.title" :length="10"></j-ellipsis>
                          </a-checkbox>
                        </a-col>
                      </template>
                    </template>
                  </a-row>
                </a-checkbox-group>
              </a-card>
            </div>
            <a-icon slot="filterIcon" type='setting' :style="{ fontSize:'16px',color:  '#108ee9' }" />
            <template slot="status" slot-scope="text, record">
              <span v-if="text == 1">系统内置</span>
              <span v-else>用户添加</span>
            </template>
            <template slot='tooltip' slot-scope='text'>
              <a-tooltip placement='topLeft' :title='text' trigger='hover'>
                <div class='tooltip'>
                  {{ text }}
                </div>
              </a-tooltip>
            </template>
            <span slot="action" slot-scope="text, record">
              <a @click="handleDetail(record)">查看</a>
              <a-divider type="vertical" />
              <a-dropdown>
                <a class="ant-dropdown-link">更多
                  <a-icon type="down" /></a>
                <a-menu slot="overlay">
                  <a-menu-item>
                    <a @click="handleEdit(record)">编辑</a>
                  </a-menu-item>
                  <a-menu-item>
                    <a-popconfirm title="确定删除吗?" @confirm="handleDelete(record.id)">
                      <a>删除</a>
                    </a-popconfirm>
                  </a-menu-item>
                </a-menu>
              </a-dropdown>
            </span>
          </a-table>
        </div>
        <!-- table区域-end -->
      </a-card>

      <!-- 表单区域 -->
      <CommonPhrasesForm ref="modalForm" @ok="modalFormOk"> </CommonPhrasesForm>
    </a-col>
  </a-row>
</template>

<script>
  import {
    ajaxGetDictItems
  } from '@/api/api'
  import CommonPhrasesForm from './modules/CommonPhrasesForm'
  import JSuperQuery from '@/components/jeecg/JSuperQuery.vue'
  import JInput from '@/components/jeecg/JInput.vue'
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import Vue from 'vue'
  import {
    deleteAction
  } from '@api/manage'
  import {
    YqFormSeniorSearchLocation
  } from '@/mixins/YqFormSeniorSearchLocation'

  export default {
    name: 'CommonPhrases',
    mixins: [JeecgListMixin, YqFormSeniorSearchLocation],
    components: {
      CommonPhrasesForm,
      JSuperQuery,
      JInput
    },
    data() {
      return {
        maxLength:50,
        description: '常用语管理表',
        formItemLayout: {
          labelCol: {
            style: 'width:80px'
          },
          wrapperCol: {
            style: 'width:calc(100% - 80px)'
          }
        },
        //字典数组缓存
        sexDictOptions: [],
        importExcelUrl: `${window._CONFIG['domianURL']}/test/jeecgDemo/importExcel`,
        //列设置
        settingColumns: [],
        //列定义
        columns: [{
            title: '数据类型',
            dataIndex: 'status',
            isUsed: true,
            customCell: () => {
              let cellStyle = 'text-align:center;width:140px'
              return {
                style: cellStyle
              }
            },
            scopedSlots: {
              customRender: 'status'
            },
          }, {
            title: '常用语',
            dataIndex: 'languageInfo',
            isUsed: true,
            scopedSlots: {
              customRender: 'tooltip'
            },
            customCell: () => {
              let cellStyle = 'text-align:left;min-width:140px;max-width:300px'
              return {
                style: cellStyle
              }
            }
          }, {
            title: '所属模块',
            dataIndex: 'module_dictText',
            isUsed: true,
            customCell: () => {
              let cellStyle = 'text-align:center'
              return {
                style: cellStyle
              }
            }
          }, {
            title: '排序',
            dataIndex: 'orderBy',
            customCell: () => {
              let cellStyle = 'text-align:center;width:60px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '创建人',
            dataIndex: 'createBy_dictText',
            isUsed: true,
            customCell: () => {
              let cellStyle = 'text-align:center;width:200px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '创建时间',
            dataIndex: 'createTime',
            isUsed: true,
            customCell: () => {
              let cellStyle = 'text-align:center;width:200px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '操作',
            dataIndex: 'action',
            isUsed: false,
            width: 240,
            align: "center",
            fixed: 'right',
            scopedSlots: {
              customRender: 'action'
            },
          }
        ],
        url: {
          list: '/language/languageManage/list',
          delete: '/language/languageManage/delete',
          deleteBatch: '/language/languageManage/deleteBatch',
        },
        dictOptions: [],
      }
    },
    created() {
      this.getColumns(this.columns)
      this.initDictData()
    },
    methods: {
      initDictData() {
        //根据字典Code, 初始化字典数组
        ajaxGetDictItems('Affiliation_module', null).then((res) => {
          if (res.success) {
            this.dictOptions = res.result
          }
        })
      },
      handleDetail: function (record) {
        this.$refs.modalForm.edit(record)
        this.$refs.modalForm.title = '详情'
        this.$refs.modalForm.disableSubmit = true
      },
      handleEdit: function (record) {
        this.$refs.modalForm.edit(record)
        this.$refs.modalForm.title = '编辑'
        this.$refs.modalForm.disableSubmit = false
      },
      handleAdd: function () {
        this.$refs.modalForm.add()
        this.$refs.modalForm.title = '新增'
        this.$refs.modalForm.disableSubmit = false
      },
      batchDel: function () {
        if (!this.url.deleteBatch) {
          this.$message.error('请设置url.deleteBatch属性!')
          return
        }
        var ids = ''
        for (var a = 0; a < this.selectedRowKeys.length; a++) {
          ids += this.selectedRowKeys[a] + ','
        }
        var that = this
        this.$confirm({
          title: '确认删除',
          okText: '是',
          cancelText: '否',
          content: '是否删除选中数据?',
          onOk: function () {
            that.loading = true
            deleteAction(that.url.deleteBatch, {
              ids: ids
            }).then((res) => {
              if (res.success) {
                //重新计算分页问题
                that.reCalculatePage(that.selectedRowKeys.length)
                that.$message.success(res.message)
                that.loadData()
                that.onClearSelected()
              } else {
                that.$message.warning(res.message)
              }
            }).finally(() => {
              that.loading = false
            })
          }
        })
      },
      handleDelete: function (id) {
        let that = this
        deleteAction(that.url.delete, {
          id: id
        }).then((res) => {
          if (res.success) {
            //重新计算分页问题
            that.reCalculatePage(1)
            that.$message.success(res.message)
            that.loadData()
          } else {
            that.$message.warning(res.message)
          }
        })
      },
      initDictConfig() {},
      //跳转单据页面
      jump() {
        this.$router.push({
          path: '/jeecg/helloworld'
        })
      },
      onBirthdayChange: function (value, dateString) {
        this.queryParam.birthday_begin = dateString[0];
        this.queryParam.birthday_end = dateString[1];
      },
      //列设置更改事件
      onColSettingsChange(checkedValues) {
        var key = this.$route.name + ":colsettings";
        Vue.ls.set(key, checkedValues, 7 * 24 * 60 * 60 * 1000)
        this.settingColumns = checkedValues;
        const cols = this.defColumns.filter(item => {
          if (item.key == 'rowIndex' || item.dataIndex == 'action') {
            return true
          }
          if (this.settingColumns.includes(item.dataIndex)) {
            return true
          }
          return false
        })
        this.columns = cols;
      },
      initColumns() {
        //权限过滤（列权限控制时打开，修改第二个参数为授权码前缀）
        //this.defColumns = colAuthFilter(this.defColumns,'testdemo:');

        var key = this.$route.name + ":colsettings";
        let colSettings = Vue.ls.get(key);
        if (colSettings == null || colSettings == undefined) {
          let allSettingColumns = [];
          this.defColumns.forEach(function (item, i, array) {
            allSettingColumns.push(item.dataIndex);
          })
          this.settingColumns = allSettingColumns;
          this.columns = this.defColumns;
        } else {
          this.settingColumns = colSettings;
          const cols = this.defColumns.filter(item => {
            if (item.key == 'rowIndex' || item.dataIndex == 'action') {
              return true;
            }
            if (colSettings.includes(item.dataIndex)) {
              return true;
            }
            return false;
          })
          this.columns = cols;
        }
      }
    },
  }
</script>
<style scoped lang='less'>
  @import '~@assets/less/common.less';
  @import '~@assets/less/YQCommon.less';
</style>