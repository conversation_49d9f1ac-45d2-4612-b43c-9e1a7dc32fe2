<template>
  <a-transfer
    showSearch
    class="tree-transfer"
    :data-source="dataSource"
    :filter-option="filterFunc"
    :target-keys="targetKeys"
    :render="item => item.title"
    :show-select-all="true"
    :operations="['添加', '删除']"
    @change="onChange"
    @search="onSearch"
  >
    <template
      slot="children"
      slot-scope="{ props: { direction, selectedKeys }, on: { itemSelect } }"
    >
      <a-tree
        v-if="direction === 'left'"
        blockNode
        checkable
        checkStrictly
        defaultExpandAll
        :checkedKeys="[...selectedKeys, ...targetKeys]"
        :treeData="treeData"
        @check="
            (_, props) => {
              onChecked(_, props, [...selectedKeys, ...targetKeys], itemSelect);
            }
          "
        @select="
            (_, props) => {
              onChecked(_, props, [...selectedKeys, ...targetKeys], itemSelect);
            }
          "
      />
    </template>
  </a-transfer>
</template>
<script>
import { queryDepartTreeList } from '@/api/api'
function isChecked(selectedKeys, eventKey) {
  return selectedKeys.indexOf(eventKey) !== -1;
}

export default {
  name: 'StratergyDeptTransfer',
  props: {
    value:{
      type:Array,
      default:()=>[],
    }
  },
  data() {
    return {
      allData: [],
      treeDataSource:[],
      targetKeys: [],
      dataSource: [],
      fileterKeys: [],
      transferDataSource: [],
    };
  },
  created() {
    this.treeDataSource= []
    this.targetKeys = JSON.parse(JSON.stringify(this.value));
    this.loadDepart();
  },
  computed: {
    treeData() {
      return this.handleTreeData( this.treeDataSource, this.targetKeys);
    },
  },
  methods: {
    //处理数据源格式并处理筛选
    mapTreeData(data,source,filtervalue){
      source.forEach(item=>{
        let tem = {
          title:item.title,
          key:item.key,
        }
        if(item.children && item.children.length){
          tem.children = [];
          this.mapTreeData(tem.children,item.children,filtervalue)
        }
        if(filtervalue!==undefined && filtervalue !== "" ){
          if(item.title.indexOf(filtervalue) !== -1){
            data.push(tem)
          }
        }else{
          data.push(tem)
        }

      })
    },
    //处理禁用
    handleTreeData(data, targetKeys = []) {
      data.forEach(item => {
        item['disabled'] = targetKeys.includes(item.key);
        if (item.children && item.children.length) {
          this.handleTreeData(item.children, targetKeys);
        }
      });
      return data;
    },
    //拉平树状数据
    flatten(list = []) {
      list.forEach(item => {
        this.transferDataSource.push(item);
        if(item.children && item.children.length){
          this.flatten(item.children);
        }
      });
    },
    //搜索
    onSearch(dir,value){
      if(dir==="left"){
        this.treeDataSource= []
        this.mapTreeData(this.treeDataSource,this.allData,value)
      }
    },
    //筛选
    filterFunc(inputValue, item){
      return item.title.indexOf(inputValue) !== -1
    },
    //加载单位树
    loadDepart(){
      queryDepartTreeList().then(res=>{
        if(res.success){
          this.allData = res.result
          // this.treeDataSource = [...res.result]
          this.mapTreeData(this.treeDataSource,res.result)
          // console.log("树状数据",this.treeDataSource)
          this.flatten(JSON.parse(JSON.stringify(this.treeDataSource)))
          // console.log("树状数据拉平",this.transferDataSource)
          this.dataSource = this.transferDataSource;
        }
      })
    },
    onChange(targetKeys) {
      // console.log('Target Keys:', targetKeys);
      this.targetKeys = targetKeys;
      this.$emit('change', targetKeys);
      this.$emit("update:value", targetKeys)
    },
    onChecked(_, e, checkedKeys, itemSelect) {
      const { eventKey } = e.node;
      itemSelect(eventKey, !isChecked(checkedKeys, eventKey));
    },
  },
}
</script>

<style scoped lang='less'>
.tree-transfer{
  height:100%;
}
.tree-transfer .ant-transfer-list:first-child {
  width: 50%;
  flex: none;
}
</style>