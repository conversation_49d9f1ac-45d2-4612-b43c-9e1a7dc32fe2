<template>
<card-frame :title='"服务请求信息统计"' :sub-title='""'>
  <div slot='bodySlot' class='empty-wrapper' v-if=' this.info.length===0'>
    <a-spin :spinning='loading' v-if='loading' class='spin'></a-spin>
    <a-list :data-source='[]' v-else />
  </div>
  <div slot='bodySlot' class="home-service-request" v-else>
    <a-row type='flex' justify="space-between" class='row'>
      <a-col :span='12' v-for='(item,index) in info' :key='"service_request_"+index' class='col'>
        <a-tooltip :title="index===0?'当月工单数量'+currMonthCount+item.unit:''" placement="left" overlayClassName='oneClickHelpTooltip'>
          <!--       <div class='order-wrapper'  :class='{"order-wrapper-margin":index<2}'>-->
          <div class='order-wrapper'>
            <div class='left-img' :style='{backgroundImage:`url(${item.img})`}'></div>
            <div class='right-data'>
              <div class='right-title' >{{item.title}}</div>
              <div class='right-number'>
                <animate-number class='number' v-if='index!==info.length-1&&index!==info.length-2' from="0" :to="item.value" :key="item.value" duration="5000">
                </animate-number>
                <animate-number class='number' v-else from="0" :to="item.value" :key="item.value" duration="5000" :formatter="formatter">
                </animate-number>
                <span class='right-unit'>{{item.unit}}</span></div>
            </div>
          </div>
        </a-tooltip>
      </a-col>
    </a-row>
  </div>
</card-frame>

</template>
<script>
import {  getAction} from '@/api/manage'
import cardFrame from '@views/statsCenter/com/cardFrame.vue'
export default {
  name:'serviceRequest',
  props: {
    adcode: {
      type: String,
      required: false,
      default: ''
    },
    handleRefresh:{
      type: Number,
      required: false,
      default: 0,
    }
  },
  components:{cardFrame},
  data() {
    return {
      loading: false,
      chartDataLength: 0,
      url: {
        count: '/data-analysis/order/informationStatistics',
        currentMonthCount:'/data-analysis/order/queryMonthStatistics'
      },
      imgList: ["total","done","duration","rate"],
      info:[],
      currMonthCount:0
    }
  },
  watch: {
    adcode: {
      handler(nVal, oVal) {
        //this.currentMonthCount(nVal)
        //this.count(nVal)
        this.getMockJson()
      },
      immediate: true,
      deep: true
    },
    handleRefresh: {
      handler(nVal, oVal) {
       // this.currentMonthCount(this.adcode)
       // this.count(this.adcode)
        this.getMockJson()
      }
    }
  },
  methods: {
    // 服务请求信息统计与服务请求达标率统计数据
    count(adcode) {
      this.loading = true
      this.info=[]
      getAction(this.url.count, {adCode:adcode}).then((res) => {
        this.info=[]
        if (res.success) {
          if (res.result.length > 0) {
            for (let i=0;i<res.result.length;i++){
              let m={
                title:res.result[i].name,
                value:res.result[i].value!=0?this.formatNumber(parseFloat(res.result[i].value)):0,
                unit:res.result[i].unit,
                img:"/statsCenter/homepage/"+this.imgList[i]+".png"
              }
              this.info.push(m)
            }
          }
        } else {
          this.$message.warning(res.message)
        }
        this.loading=false
      }).catch((err) => {
        this.$message.warning(err.message)
        this.loading = false
      })
    },
    currentMonthCount(adcode) {
      this.currMonthCount = ''
      getAction(this.url.currentMonthCount, {adCode:adcode}).then((res) => {
        if (res.success) {
          this.currMonthCount =res.result
        } else {
          this.$message.warning(res.message)
        }
      }).catch((err) => {
        this.$message.warning(err.message)
      })
    },
    getMockJson(){
      this.loading = true
      this.info=[]
      this.currMonthCount = ''
      getAction(location.origin+"/statsCenter/mock/homeData.json").then((res) => {
        if (res) {
          this.currMonthCount=res.serviceRequestData.currMonthCount
          let result = res.serviceRequestData.count
          if (result.length > 0) {
            for (let i=0;i<result.length;i++){
              let m={
                title:result[i].name,
                value:result[i].value!=0?this.formatNumber(parseFloat(result[i].value)):0,
                unit:result[i].unit,
                img:"/statsCenter/homepage/"+this.imgList[i]+".png"
              }
              this.info.push(m)
            }
          }
        }
        this.loading=false
      }).catch((err) => {
        this.loading = false
      })
    },
    formatNumber(num) {
      if(num!=undefined&&num!=null){
        return num.toString().replace(/\d+/, function(n) {
          return n.replace(/(\d)(?=(?:\d{4})+$)/g, '$1,')
        })
      }
    },
    formatter: function (num) {
      if (num < 0) {
        return ''
      }
      return num.toFixed(2) //小数点后几位，数字就是几小数点后几位
    },
  },
}
</script>
<style lang="less" scoped>
.home-service-request{
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;

  .row{
    height: 100%;
    width: 100%;
    .col{
      height: 50%;
      display: flex;
      justify-content: start;
      align-items: center;

      .order-wrapper{
        display: flex;
        justify-content: start;
        align-items: center;
        color:#ffffff;
        width:100%;

        .left-img{
          background-repeat: no-repeat;
          width: 0.9125rem;//73/80
          height: 1.1625rem;//93/80;
          background-size: 100%;
        }

        .right-data{
          display: flex;
          flex-flow: column nowrap;
          justify-content: space-between;
          align-items: start;
          width: calc(100% - 0.9125rem);//100%-73/80

          .right-title{
            color:#EDFBFF;
            font-size: 0.2rem;
            width:100%;height: 0.3rem;
            line-height: 0.3rem;
            padding-left: 0.2rem;
            background-image:linear-gradient(to right,transparent 0%,rgba(27,150,255,0.3) 10%,rgba(27,150,255,0.67) 25%,transparent)
          }
          .right-number{
            margin-top: 0.1rem;
            padding-left: 0.2rem;
            font-size:0.35rem;// 28px/80px;
            color:#F7FBFF;
            width: 100%;
            display: flex;
            flex-flow: row nowrap;
            justify-content: start;
            align-items: end;

            .number{
              font-family: DIN-Medium;
              height: 0.35rem;
              line-height: 0.35rem;//28/80
              display: inline-block;
              max-width: calc(100% - 0.2rem);
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;

              text-shadow: 0.025rem 0.025rem 0.075rem rgba(27,150,255, 0.67),
                -0.025rem -0.025rem 0.075rem rgba(27,150,255, 0.67)//2/80 6/80
            }
          }
          .right-unit{
            padding-left:0.05rem;
            font-size:0.1625rem;//13/80
            color:#9CC2E3;
            font-weight: 400;

          }
        }
      }
      .order-wrapper-margin{
        margin-bottom: 0.1rem;
      }
    }
  }

}
</style>