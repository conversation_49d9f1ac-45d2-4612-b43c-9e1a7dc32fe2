.ant-form-item-label > label {
  color: rgba(0, 0, 0, 0.65) !important;
}

.ant-input {
  color: rgba(0, 0, 0, 0.65) !important;
}

.ant-input-number {
  color: rgba(0, 0, 0, 0.65) !important;
}

.ant-select {
  color: rgba(0, 0, 0, 0.65) !important;
}

.ant-select.ant-select-selection {
  color: rgba(0, 0, 0, 0.65) !important;
}

/*输入框禁用*/
.ant-input[disabled] {
  color: rgba(0, 0, 0, 0.65) !important;
  /*background-color: #f9f9f9 !important;*/
  cursor: not-allowed !important;
}

.ant-input-number-disabled {
  color: rgba(0, 0, 0, 0.65) !important;
  /*background-color: #f9f9f9 !important;*/
  cursor: not-allowed !important;
}

.ant-select-disabled {
  color: rgba(0, 0, 0, 0.65) !important;
  cursor: not-allowed !important;
}

.ant-select-disabled .ant-select-selection {
  /*background-color: #f9f9f9 !important;*/
  cursor: not-allowed !important;
}