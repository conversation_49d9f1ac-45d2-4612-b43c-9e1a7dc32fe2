<template>
  <div class='row-container'>
    <a-card :bordered='false' class='card1' :bodyStyle='card1BodyStyle'>
      <section class='title'>{{ data ? data.name : '' }}</section>
      <img src='~@/assets/return1.png' alt='' @click='getGo'>
    </a-card>
    <a-card :bordered='false' class='card2' :bodyStyle='card2BodyStyle'>
      <div class='box config'>
        <div class='title'>监控配置</div>
        <a-descriptions :column='{ xxl: 2, xl: 2, lg: 2, md: 2, sm: 2, xs: 2 }' bordered>
          <a-descriptions-item label='任务名称'>{{ data.name }}</a-descriptions-item>
          <a-descriptions-item label='业务名称'>
            <span v-if='!data.bizInfoName' style='color:#ff0000'>业务已不存在</span>
            <span v-else>{{data.bizInfoName}}</span>
          </a-descriptions-item>
          <a-descriptions-item label='时间规则'>{{ data.cron }}</a-descriptions-item>
          <a-descriptions-item label='创建时间'>{{ data.createTime }}</a-descriptions-item>
          <a-descriptions-item label='状态'>
            <span v-if='data.status==1'>启用</span>
            <span v-else>禁用</span>
          </a-descriptions-item>
          <a-descriptions-item label='最近执行时间'>{{ data.lastTestTime }}</a-descriptions-item>
          <a-descriptions-item label='执行结果'>
            <a-tag v-if='data.lastTestResult===0' color='volcano-inverse' class='result-tag'>失败</a-tag>
            <a-tag v-else-if='data.lastTestResult===1' color='lime-inverse' class='result-tag'>成功</a-tag>
            <a-tag v-else color='blue-inverse' class='result-tag'>未执行</a-tag>
          </a-descriptions-item>
          <a-descriptions-item label='任务描述'>{{ data.description }}</a-descriptions-item>
        </a-descriptions>
      </div>
      <div class='box info'>
        <div class='title'>监控信息</div>
        <div class='top-tips' v-if='totalTime.length>0||stepError.length>0'>
          <span class='time' v-if='totalTime.length>0'>
            <a-icon type='clock-circle' style='color:#1e3674'/>
            {{totalTime }}
          </span>
          <span class='err' v-if='stepError.length>0'>
            <a-icon type='close-circle' style='color:#f5222d'/>
            {{stepError}}
          </span>
        </div>
        <a-table ref='table'
                 bordered
                 :rowKey='(record,index)=>{return index}'
                 :columns='columns'
                 :dataSource='dataSource'
                 :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
                 :pagination='ipagination'
                 :loading='loading'
                 @change='handleTableChange'>
          <template slot='executionResult' slot-scope='text,record'>
            <a-tag v-if='text===0' color='volcano-inverse' class='result-tag fail-tag' @click='tagClick(record)'>失败</a-tag>
            <a-tag v-else-if='text===1' color='lime-inverse' class='result-tag'>成功</a-tag>
            <a-tag v-else color='blue-inverse' class='result-tag'>未执行</a-tag>
          </template>
        </a-table>
      </div>
    </a-card>
  </div>
</template>

<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { getAction, putAction } from '@api/manage'
export default {
  name: 'availableMonitoringInfo',
  props: { data:{}},
  mixins: [JeecgListMixin],
  data() {
    return {
      card1BodyStyle: {
        display: 'flex !important',
        flexFlow: 'row nowrap !important',
        justifyContent: 'flex-start !important',
        alignItems: 'center !important',
      },
      card2BodyStyle: {
        display: 'flex !important',
        flexFlow: 'column nowrap !important',
        justifyContent: 'start !important',
      },
      ipagination: {
        pageSize: 30,
        pageSizeOptions: ['30', '60', '100'],
        showQuickJumper: false,
      },
      columns: [
        {
          title: '步骤序列',
          dataIndex: '',
          key: 'rowIndex',
          customRender: function(t, r, index) {
            return parseInt(index) + 1
          },
          customCell: () => {
            let cellStyle = 'text-align: center;width: 60px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '步骤名称',
          dataIndex: 'name',
          customCell: () => {
            let cellStyle = 'text-align: center;;min-width: 100px;'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '开始时间',
          dataIndex: 'startTime',
          customCell: () => {
            let cellStyle = 'text-align: center;width: 200px;'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '结束时间',
          dataIndex: 'endTime',
          customCell: () => {
            let cellStyle = 'text-align: center;width: 200px;'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '执行时长(ms)',
          dataIndex: 'durationMilliseconds',
          customCell: () => {
            let cellStyle = 'text-align: right;min-width: 80px;'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '执行情况',
          dataIndex: 'success',
          scopedSlots: {
            customRender: 'executionResult'
          },
          customCell: () => {
            let cellStyle = 'text-align: center;min-width:80px'
            return {
              style: cellStyle
            }
          }
        }
      ],
      url:{
        list:'/business/availabilityResult/queryById'
      },
      disableMixinCreated:true,
      totalTime:'',
      stepError:''
    }
  },
  watch:{
    data:{
      handler(newVal,oldVal){
        if(newVal.lastTestId){
          this.queryParam.id=newVal.lastTestId
          this.loadData()
        }
      },
      immediate:true,
      deep:true,
    }
  },
  methods: {
    getGo() {
      this.$parent.pButton1(0)
    },
    loadData(arg) {
      if (!this.url.list) {
        this.$message.error('请设置url.list属性!')
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1
      }
      var params = this.getQueryParams() //查询条件
      this.loading = true
      getAction(this.url.list, params).then((res) => {
        if (res.success) {
          this.totalTime=res.result&&res.result.durationMilliseconds? "总耗时"+ res.result.durationMilliseconds+"ms":""
          this.stepError=res.result&&res.result.message? res.result.message:""
          let data=res.result? res.result.stepResultList : []
          this.dataSource =data
          if (this.dataSource.length < 9) {
            this.clientHeight = false
          }
          this.ipagination.total = this.dataSource.length
        }
        if (res.code === 510) {
          this.$message.warning(res.message)
        }
        this.loading = false
      })
    },
    handleTableChange(pagination, filters, sorter) {
      //分页、排序、筛选变化时触发
      //TODO 筛选
      if (Object.keys(sorter).length > 0) {
        this.isorter.column = sorter.field
        this.isorter.order = 'ascend' == sorter.order ? 'asc' : 'desc'
      }
      this.ipagination = pagination
      // this.loadData()
    },
    tagClick(record){
      let that=this
      that.$confirm({
        title: `异常提示`,
        maskClosable:true,
        width:'600px',
        type:'error',
        content: `${record.errorInfo&&record.errorInfo.length>0?record.errorInfo:record.message}`,
        okButtonProps: { style: { display: 'none' }},
        cancelText: '关闭',
      })
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';

.row-container {
  display: flex;
  flex-flow: column nowrap;
  height: 100%;
}

.card1 > .ant-card-body {
  .title {
    font-family: PingFangSC-Medium;
    font-size: 18px;
    font-weight: 600;
    width: calc(100% - 40px)
  }

  img {
    margin-left: 20px;
    width: 20px;
    height: 20px;
    cursor: pointer;
  }
}

::v-deep .ant-descriptions-view {
  border-radius: 0px;
}

::v-deep .ant-descriptions-bordered .ant-descriptions-item-label {
  background-color: rgb(250, 250, 250);
  text-align: center;
  width: 17%;
}

::v-deep .ant-descriptions-item-label,
.ant-descriptions-item-content {
  color: rgb(96, 98, 102) !important;
}

::v-deep .ant-descriptions-bordered .ant-descriptions-item-content {
  width: 35%;
}

.card2 {
  margin-top: 16px;
  overflow: hidden;
  overflow-y: auto;
  flex: 1;

  .box {
    margin-bottom: 20px;

    .title {
      margin-bottom: 10px;
      padding-left: 7px;
      border-left: 4px solid #1e3674;
    }

    .top-tips {
      margin-bottom: 6px;
      background: #ecf5ff;
      border: 1px solid #b3d8ff;
      border-radius: 3px;
      padding: 6px;
      display: flex;
      justify-content: start;
      align-items: center;
      flex-flow: row nowrap;

      span{
        display: inline-block;
        white-space: nowrap;
      }
      .time {
        margin-right: 20px;
      }

      .err {
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .result-tag {
      text-align: center !important;
      width: 52px !important;
    }

    .fail-tag:hover{
      cursor: pointer;
      background: #f5222d;
    }
  }
}
::v-deep .ant-alert-warning{
  margin-bottom: 6px !important;
  border-radius: 3px !important;
  background-color: #ecf5ff !important;
  border: 1px solid #b3d8ff !important;
  padding: 6px 15px 10px 64px !important;

  .ant-alert-icon{
    top:10px !important;
    color: #40a9ff;
  }
}
</style>