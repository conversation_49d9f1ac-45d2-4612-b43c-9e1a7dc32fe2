<template>
  <div>
    <a-input-search placeholder='搜索查询名称' @search='searchGroupName' />
    <a-spin :spinning='confirmLoading'>
      <div class='list' :style='{maxHeight:maxHeight,overflow:"hidden",overflowY:"auto",padding:"12px 0px"}'>
        <div v-if='groupList.length>0'>
          <div v-for='(item,index) in currGroupList' class='group-item-wrapper'
               :class='{"selected-group-item-wrapper":item.isCurr==1}' :key='"group_"+index'
               @click.stop='selectGroup(item)'>
            <div class='group-content'>
              <div v-if='item.isCurr==1'>
                <a-icon type='check' style='margin-right: 4px'/>{{item.queryName=="DEFAULT"?item.queryName+"(默认)":item.queryName}}
              </div>
              <div v-else style='padding-left: 18px'>{{item.queryName=="DEFAULT"?item.queryName+"(默认)":item.queryName}}</div>
            </div>
            <div class='group-btns-wrapper' v-if='item.queryName!=="DEFAULT"'>
            <span title='编辑' style='margin-right: 8px' @click.stop='editGroup(item)'>
              <a-icon type='edit'/>
            </span>
              <span title='删除' @click.stop='deleteGroup(item)'>
              <a-icon type='delete'/>
            </span>
            </div>
          </div>
        </div>
        <div v-else>
          <a-list :data-source='[]'></a-list>
        </div>
      </div>
    </a-spin>
  </div>
</template>
<script>
import { getAction, httpAction,deleteAction } from '@api/manage'

export default {
  name: 'queryGroupList',
  props: {
    logAnalyzeId: {
      type: String,
      required: false,
      default: ''
    },
    groupList: {
      type: Array,
      required: false,
      default: ()=>{return []}
    },
    maxHeight: {
      type: String,
      required: false,
      default: '400px'
    }
  },
  data() {
    return {
      confirmLoading:false,
      currGroupList: [],
      url: {
        toggle:'logAnalyze/group/toggleQuery',//切换分组
        delete:'/logAnalyze/group/delete'
      }
    }
  },
  watch: {
    groupList: {
      handler(nval, oval) {
        //console.log('激活的查询组===', nval)
        this.currGroupList=[]
        for (let i=0;i<nval.length;i++){
          let obj={
            id:nval[i].id,
            isCurr:nval[i].isCurr,
            logAnalyzeId:nval[i].logAnalyzeId,
            queryName:nval[i].queryName
          }
          this.currGroupList.push(obj)
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    searchGroupName(value){
      let temp=value.toLowerCase()
      this.currGroupList=[]
      if (this.groupList.length>0){
        this.currGroupList=this.groupList.filter((item)=>{
          if (temp.length>0){
            let tempQueryName=item.queryName.toLowerCase()
            return tempQueryName.indexOf(temp)!==-1
          }else {
            return true
          }
        })
      }
    },
    selectGroup(group) {
      let that=this
      that.confirmLoading=true
      let httpurl =that.url.toggle
      let method = 'put'

      let formData={
        logAnalyzeId:group.logAnalyzeId,
        id:group.id
      }
      httpAction(httpurl, formData, method)
        .then((res) => {
          if (res.success) {
            that.$message.success(res.message)
            that.$emit('selectGroup')
          } else {
            that.$message.warning(res.message)
          }
          that.confirmLoading = false
        }).catch((err) => {
        that.$message.warning(err.message)
        that.confirmLoading = false
      })
    },
    editGroup(group){
      this.$emit('editGroup',group)
    },
    deleteGroup(group){
      this.confirmLoading=true
      deleteAction(this.url.delete,{isCurr:group.isCurr,queryId:group.id}).then((res)=>{
       if (res.success){
         this.$message.success(res.success)
         this.$emit('deleteGroup',group)
       }else {
         this.$message.warning(res.success)
       }
        this.confirmLoading=false
     }).catch((err)=>{
       this.$message.success(err.success)
        this.confirmLoading=false
     })
    }
  }
}
</script>

<style scoped lang='less'>
.group-item-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-flow: row nowrap;
  cursor: pointer;
  overflow: hidden;
  height:24px;
  line-height: 24px;
  padding:5px 12px;
  border-radius: 3px;

  .group-content {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    margin-right: 8px;
  }
}

.group-item-wrapper:hover {
  color: #409eff;
  background-color: #ecf5ff;
}

.group-btns-wrapper {
  span {
    color: rgba(0, 0, 0, 0.65)
  }

  span:hover {
    color: #409eff;
  }
}

.group-btns-wrapper>span{

}

.selected-group-item-wrapper {
  background-color: #ecf5ff;
  color:#409eff;
}
</style>