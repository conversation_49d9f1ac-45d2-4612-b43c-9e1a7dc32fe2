<template>
  <div style='height: 100%'>
    <keep-alive exclude='AlarmTemplateInfoModal'>
      <component :is='pageName':data="alarmInfo" :device-info='deviceInfo' />
    </keep-alive>
  </div>
</template>
<script>
import DeviceAlarmList from './DeviceAlarmList'
import DeviceAlarmInfoModal from './DeviceAlarmDetails.vue'

export default {
  name: 'DeviceAlarmManagement',
  props: {},
  data() {
    return {
      isActive: 0,
      alarmInfo:{},//告警信息
      deviceInfo:{},//设备信息
    }
  },
  components: {
    DeviceAlarmList,
    DeviceAlarmInfoModal
  },
  created() {
    this.pButton1(0)
  },
  //使用计算属性
  computed: {
    pageName() {
      switch (this.isActive) {
        case 0:
          return 'DeviceAlarmList'
          break
        default:
          return  'DeviceAlarmInfoModal'
          break
      }
    }
  },
  methods: {
    show(index,item) {
      this.isActive = index;
      this.deviceInfo = item;
    },
    pButton1(index, item) {
      this.isActive = index
    },
    pButton2(index, item) {
      this.isActive = index
      this.alarmInfo = item
    }
  }
}
</script>