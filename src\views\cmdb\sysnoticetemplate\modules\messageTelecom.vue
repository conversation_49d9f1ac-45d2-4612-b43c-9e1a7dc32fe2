<template>
<div>
    <a-col :span='23'>
      <a-form-model-item class='two-words' label='接收人' prop='sendTo' :rules='validatorRules.sendTo'>
        <a-select v-model='personalizedObject.sendTo' :allowClear='true' :getPopupContainer='(node) => node.parentNode'
                  mode='multiple' option-filter-prop='children'
                  placeholder='请选择接收人' show-search @change='changeValue'>
          <a-select-option v-for='item in usersList' :key='item.id' :value='item.id'>
            {{ item.realName }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
    </a-col>
</div>
</template>

<script>
export default {
  name: 'messageTelecom',
  props: {
    data: {
      type: Object,
      required: false,
      default: () => {
        let personalizedObject = {
          sendTo: undefined
        }
        return personalizedObject
      }
    },
    usersList: {
      type: Array,
      required: true,
      default: []
    }
  },
  data() {
    return {
      personalizedObject: {
        sendTo: undefined
      },
      validatorRules: {
        sendTo: [
          { required: true, validator: this.customValidate }
        ]
      }
    }
  },
  watch: {
    data: {
      handler(val) {
        if (Object.keys(val).length > 0) {
          this.personalizedObject = val
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    changeValue() {
      this.$emit('changeModelValue', this.personalizedObject)
    },
    getTips(fullField) {
      let str = ''
      switch (fullField) {
        case 'sendTo' :
          str = '请选择接收人！'
          break
      }
      return str
    },
    customValidate(rule, value, callback) {
      if (rule.required) {
        if (value && value.length > 0) {
          callback()
        } else {
          callback(this.getTips(rule.fullField))
        }
      } else {
        callback()
      }
    }
  }
}
</script>