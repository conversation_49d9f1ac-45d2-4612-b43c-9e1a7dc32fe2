<template>
  <a-card style="height: 100%;overflow: hidden;overflow-y: auto;">
    <a-row>
      <a-col :span="24">
        <span style="float: right;margin-bottom: 12px;"><img src="~@/assets/return1.png" alt="" @click="getGo"
            style="width: 20px;height: 20px;cursor: pointer"></span>
      </a-col>
      <div class='colorBox'>
        <span class="colorTotal">服务商信息</span>
      </div>
      <a-col :span="24">
        <table class="gridtable">
          <tr>
            <td class="leftTd">服务商</td>
            <td class="rightTd">{{ data.providerName }}</td>
            <td class="leftTd">服务优先级</td>
            <td class="rightTd">{{ data.priority }}</td>
          </tr>
          <tr>
            <td class="leftTd">联系电话</td>
            <td class="rightTd">{{ data.phone }}</td>
            <td class="leftTd">详细地址</td>
            <td class="rightTd">{{ data.providerAddress }}</td>
          </tr>
          <tr>
            <td class="leftTd">账号</td>
            <td class="rightTd">{{ data.userName }}</td>
            <td class="leftTd">描述</td>
            <td class="rightTd">{{ data.providerDesc }}</td>
          </tr>
        </table>
      </a-col>
      <a-col :span="24">
        <div class='colorBox' style="margin-top: 24px;">
          <span class="colorTotal">服务单位</span>
        </div>
        <div class='table-operator table-operator-style'>
          <a-button @click='handleAdd' :loading="allDepartsLoading">绑定部门</a-button>
        </div>
        <a-table ref="table" bordered rowKey="id" :loading="loading" :columns="columns" :dataSource="dataSource" :pagination="ipagination"
          @change="handleTableChange">
          <span slot="cityName" slot-scope="text">{{ getCityText(text) }}</span>
          <span slot="action" slot-scope="text, record">
            <a-popconfirm title="确定删除吗?" @confirm="() => deleteRecord(record.id)">
              <a>取消绑定</a>
            </a-popconfirm>
          </span>
        </a-table>


        <div class='colorBox' style="margin-top: 24px;">
          <span class="colorTotal">人员信息</span>
        </div>
        <div class='table-operator table-operator-style'>
          <a-button @click='handleAddUserRole' :loading="allUsersLoading">绑定用户</a-button>
        </div>
        <a-table ref="table" bordered rowKey="id" :loading="loadingP" :columns="columnsP" :dataSource="dataSourceP"
          :pagination="ipaginationP" @change="handleTableChange1">
          <template slot='avatarslot' slot-scope='text, record'>
            <div class='anty-img-wrap'>
              <a-avatar shape='square' :src='getAvatarView(record.avatar)' icon='user' />
            </div>
          </template>
          <span slot="serviceAction" slot-scope="text, record">
            <a-popconfirm title="确定删除吗?" @confirm="() => deleteService(record.id)">
              <a>取消绑定</a>
            </a-popconfirm>
          </span>
        </a-table>
       <j-select-depart ref="JSelectDepart" :show-input="false" :multi="true" :value="selectedDepartIds" @change="modalFormOk"></j-select-depart>
        <Select-User-Modal ref="selectUserModal" @selectFinished="selectOK"></Select-User-Modal>
      </a-col>
    </a-row>
  </a-card>
</template>

<script>
  import SelectUserModal from './SelectUserModal'
  import {
    putAction,
    deleteAction,
    getAction,
    getFileAccessHttpUrl
  } from '@/api/manage'
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import {
    getAreaTree
  } from '@/api/api'
  import {
    getRootAreaId
  } from '@/components/dict/JDictSelectUtil'
  export default {
    name: 'networkPlanDetails',
    mixins: [JeecgListMixin],
    components: {
      SelectUserModal
    },
    props: {
      data: {
        type: Object
      }
    },
    data() {
      return {
        queryParam: {
          providerId: ''
        },

        disableMixinCreated: true,
        allDepartsLoading:false,
        ipagination: {
          showQuickJumper: false,
        },
        columns: [
          {
            title: '部门名称',
            dataIndex: 'departName',
            customCell: () => {
              let cellStyle = 'text-align: center'
              return {
                style: cellStyle
              }
            }
          }, {
            title: '地区',
            dataIndex: 'cityId',
            scopedSlots: {
              customRender: 'cityName'
            },
            customCell: () => {
              let cellStyle = 'text-align: center'
              return {
                style: cellStyle
              }
            }
          }, {
            title: '联系人',
            dataIndex: 'contacts',
            customCell: () => {
              let cellStyle = 'text-align: center'
              return {
                style: cellStyle
              }
            }
          }, {
            title: '手机号',
            dataIndex: 'mobile',
            customCell: () => {
              let cellStyle = 'text-align: center'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            fixed: 'right',
            width: 147,
            scopedSlots: {
              customRender: 'action'
            }
          }
        ],
        selectedDepartIds:'',
        dictOptions: [],

        allUsersLoading:false,
        loadingP:false,
        isorterP:{
          column: 'createTime',
          order: 'desc'
        },
        ipaginationP: {
          current: 1,
          pageSize: 10,
          pageSizeOptions: ['10', '20', '50'],
          showTotal: (total, range) => {
            return ' 共' + total + '条'
          },
          showQuickJumper: false,
          showSizeChanger: true,
          total: 0
        },
        columnsP: [
          {
            title: '用户账号',
            dataIndex: 'username'
          },
          {
            title: '用户姓名',
            dataIndex: 'realname'
          },
          {
            title: '工号',
            dataIndex: 'workNo',
          },
          {
            title: '服务优先级',
            dataIndex: 'priority',
          },
          {
            title: '头像',
            dataIndex: 'avatar',
            scopedSlots: {
              customRender: 'avatarslot'
            }
          },
          {
            title: '邮箱',
            dataIndex: 'email',
          },
          {
            title: '手机号',
            dataIndex: 'phone',
          },
          {
            title: '座机',
            dataIndex: 'telephone',
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            fixed: 'right',
            width: 147,
            scopedSlots: {
              customRender: 'serviceAction'
            }
          }
        ],
        dataSourceP: [],

        url: {
          list: '/serviceProvider/depart/departListByProviderId',
          add: '/serviceProvider/depart/editProviderDepart',
          delete: '/serviceProvider/depart/deleteProviderDepart',
          addUserRole: '/serviceProvider/bindingEngineer',
          serviceDel: '/serviceProvider/unbindEngineer',
          engineerList: '/serviceProvider/engineerList',
          allDepts:'/serviceProvider/depart/getDepIdsByProviderId',
          allUsers:'/serviceProvider/user/getUserIdsByProviderId'
        }
      }
    },
    created() {
      this.initDictData()
    },
    mounted() {
      this.queryParam.providerId=this.data.id
      this.loadData()
      this.loadDataP()
    },
    methods: {
      initDictData() {
        let pid = ''
        getRootAreaId().then(res => {
          pid = res.data
        })
        getAreaTree({
          pid: pid
        }).then((res) => {
          if (res.success) {
            if (res.result) {
              this.dictOptions = this.IterationDelateNullChildren(res.result)
            }
          }
        })
      },
      //删除子节点为空的children属性
      IterationDelateNullChildren(arr) {
        if (arr.length) {
          for (let i in arr) {
            if (arr[i].children&&arr[i].children.length) {
              this.IterationDelateNullChildren(arr[i].children)
            } else {
              delete arr[i].children;
            }
          }
        }
        return arr
      },
      getCityText(cityId) {
        let cityText = ''
        this.dictOptions.forEach(ele => {
          if (ele.id == cityId) {
            cityText = ele.text
          }
        });
        return cityText
      },
      handleAdd: function () {
        this.allDepartsLoading=true
       getAction(this.url.allDepts,{providerId:this.data.id}).then(res => {
         let selectedKeys=[]
         if (res.success&&res.result&&res.result.length>0){
           selectedKeys=res.result
         }
         this.allDepartsLoading=false
         this.selectedDepartIds=selectedKeys.join(',')
         this.$refs.JSelectDepart.openModal()
       }).catch((err)=>{
         this.allDepartsLoading=false
         this.$message.error(err.message)
       })
      },
      deleteRecord: function (id) {
        var that = this
        deleteAction(that.url.delete, {
          depId: id,
          providerId: this.data.id,
        }).then((res) => {
          if (res.success) {
            //重新计算分页问题
            that.reCalculatePageFun(1,this.ipagination)
            that.$message.success(res.message)
            that.loadData()
          } else {
            that.$message.warning(res.message)
          }
        })
      },
      modalFormOk(data) {
        console.log('data===',data)
        this.loading=true
        putAction(this.url.add, {
          providerId: this.data.id,
          depIdList: data
        }).then((res) => {
          if (res.success) {
            this.loadData(1)
          }else {
            this.loading=false
            this.$message.warning(res.message)
          }
        }).catch((err)=>{
          this.loading=false
          this.$message.error(err.message)
        })
      },
      loadDataP(arg) {
        if (!this.url.engineerList) {
          this.$message.error('请设置url.engineerList属性!')
          return
        }
        //加载数据 若传入参数1则加载第一页的内容
        if (arg === 1) {
          this.ipaginationP.current = 1
        }
        var params ={
          pageNo:this.ipaginationP.current,
          pageSize:this.ipaginationP.pageSize,
          providerId:this.data.id
        }
        this.loadingP = true
        this.dataSourceP = []
        this.ipaginationP.total=0

        getAction(this.url.engineerList, params).then((res) => {
          if (res.success&& res.result&& res.result.records) {
            this.dataSourceP = res.result.records
            this.ipaginationP.total =res.result.total
          }
          this.loadingP = false
        }).catch((err)=>{
          this.loadingP = false
          this.$message.error(err.message)
        })
      },
      getAvatarView: function (avatar) {
        return getFileAccessHttpUrl(avatar)
      },
      handleAddUserRole() {
        this.allUsersLoading=true
        getAction(this.url.allUsers,{providerId:this.data.id}).then(res => {
          let selectedKeys=[]
          if (res.success&&res.result&&res.result.length>0){
            selectedKeys=res.result
          }
          this.allUsersLoading=false
          this.$refs.selectUserModal.selectedRowKeys=selectedKeys
          this.$refs.selectUserModal.visible = true
        }).catch((err)=>{
          this.allUsersLoading=false
          this.$message.error(err.message)
        })
      },
      deleteService(id) {
        var that = this
        deleteAction(that.url.serviceDel, {
          engineerId: id,
          providerId: this.data.id,
        }).then((res) => {
          if (res.success) {
            //重新计算分页问题
            that.reCalculatePageFun(1,this.ipaginationP)
            that.$message.success(res.message)
            that.loadDataP()
          } else {
            that.$message.warning(res.message)
          }
        })
      },
      reCalculatePageFun(count,ipagination) {
        //总数量-count
        let total =ipagination.total - count
        //获取删除后的分页数
        let currentIndex = Math.ceil(total / ipagination.pageSize)
        //删除后的分页数<所在当前页
        if (currentIndex < ipagination.current) {
          ipagination.current = currentIndex
        }
      },
      selectOK(data) {
        let engList = []
        for (let a = 0; a < data.length; a++) {
          engList.push(data[a])
        }
        let params= {
          providerId:this.data.id,
          engineerIdList : engList.join(',')
        }
        putAction(this.url.addUserRole, params).then((res) => {
          if (res.success) {
            this.loadDataP(1)
            this.$message.success(res.message)
          } else {
            this.$message.warning(res.message)
          }
        })
      },
      handleTableChange1(pagination, filters, sorter) {
        //分页、排序、筛选变化时触发
        //TODO 筛选
        if (Object.keys(sorter).length > 0) {
          this.isorterP.column = sorter.field
          this.isorterP.order = 'ascend' == sorter.order ? 'asc' : 'desc'
        }
        this.ipaginationP = pagination
        this.loadDataP()
      },

      //返回上一级
      getGo() {
        this.$parent.pButton2(0);
      }
    }
  }
</script>
<style lang='less' scoped>
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';

  table.gridtable {
    font-family: verdana, arial, sans-serif;
    font-size: 14px;
    color: #606266;
    border-width: 1px;
    border-color: #e8e8e8;
    border-collapse: collapse;
    text-align: left;
    width: 100%;
  }

  table.gridtable td {
    border-width: 1px;
    border-style: solid;
    border-color: #e8e8e8;
  }

  .leftTd {
    width: 17%;
    background-color: #FAFAFA;
    padding: 16px 24px;
    text-align: center;
  }

  .rightTd {
    width: 35%;
    padding: 16px 24px;
  }

  .colorBox {
    margin-bottom: 10px;
  }

  .colorTotal {
    padding-left: 7px;
    border-left: 4px solid #1e3674;
  }
</style>