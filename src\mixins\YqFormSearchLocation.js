import {addCancelDebounce} from '@/utils/util'
//该脚本用于计算查询form表单查询、重置、展开/收起按钮的位置
export const YqFormSearchLocation = {
  data() {
    return {
      //控制开始是否执行计算方法
      isDo:true,
      //isOpenStatus:false,
      //当前浏览器窗口宽度
      clientWidth: document.documentElement.clientWidth,
      //formItem总个数
      formItemsCount: 0,
      //当前每行最多可显示formItem个数
      perFormItemCount: 1,
      //开始可见formItem个数
      visibleFormItemCount: 4,
      //浏览器窗口宽度比较值数组
      aLimitWidth: [1200, 992, 768, 576],
      //不同窗口大小下每行col的个数的集合
      perRowColCount:[4,3,2,1],
      //不同窗口大小下每行col所占栅格数集合
      perColSpan:[6,8,12,24],
      //span所占的栅格数
      spanValue:6,
      // 查询\重置\展开（收起）时否靠右显示
      toRight: false,
      //展开、收起按钮是否可见
      isVisible:true,
      //当前ref=row的元素宽度
      rowWidth:600,
      //formItem的label和input宽度设置
      formItemLayout: {
        labelCol: {
          //style:'width:90px;text-align: justify; text-align-last: justify',
          style: 'width:90px;float:left',
          /* lg:{ span: 6},
           md:{ span: 5 },
           sm: { span: 3 },
           xs: { span: 24 },*/
        },
        wrapperCol: {
          style: 'width:calc(100% - 90px)',
          /* lg:{ span: 18},
           md:{ span: 19},
           sm: { span: 21},
           xs: { span: 24 },*/
        },
      },
      isUpdatedRowWidth:false,
      debouncedGetAreaRowSize:null
    }
  },

  watch:{
    // clientWidth(val){
    //   this.setBtnslocationStatus(val);
    // },
    rowWidth(val){
      //this.setBtnslocationStatus(val);
    }
  },
  //注销window.onresize事件
  beforeDestroy(){
    //window.onresize = null;
    //注销当前resize事件
    if (this.debouncedGetAreaRowSize) {
      window.removeEventListener("resize", this.debouncedGetAreaRowSize);
      this.debouncedGetAreaRowSize.cancel(); // 取消计时器
    }
  },
  //注销window.onresize事件
  /*destroy(){
    //window.onresize = null;
    //注销当前resize事件
    if (this.debouncedGetAreaRowSize) {
      window.removeEventListener("resize", this.debouncedGetAreaRowSize);
      this.debouncedGetAreaRowSize.cancel(); // 取消计时器
    }
  },*/
  mounted() {
    if(this.isDo){
      this.areaRowInitFuns();
    }
  },
  activated(){
    this.rowWidth = this.$refs.row.$el.offsetWidth;
    this.setBtnslocationStatus(this.rowWidth);
  },
  updated() {
    if(this.isUpdatedRowWidth){
      this.rowWidth = this.$refs.row.$el.offsetWidth;
      this.setBtnslocationStatus(this.rowWidth);
      this.isUpdatedRowWidth=false
    }
  },
  methods:{
    //以ref=row元素宽度为基准  初始化
    areaRowInitFuns(){
      let that=this
      that.$nextTick( () => {
        if(that.$refs.row&&that.$refs.row.$el){
          that.getFormItemsValue(that.$refs.row.$children)
          that.MountWindownOnResizeEvent()
          that.rowWidth =that.$refs.row.$el.offsetWidth;
          that.setBtnslocationStatus(that.rowWidth);
        }})
    },
    //获取查询板块总的查询条目总个数、可见个数
    getFormItemsValue(arr){
      let aChildren=arr;
      this.formItemsCount=aChildren.length;
      let hiddenFormItemCount=0;
      for (let i=0;i<this.formItemsCount;i++){
        if(aChildren[i].$el.style!=undefined&&aChildren[i].$el.style.display!=undefined&&(aChildren[i].$el.style.display=='none'))
        {
          hiddenFormItemCount++;
        }
      }
      this.visibleFormItemCount=this.formItemsCount-hiddenFormItemCount;
      if(this.visibleFormItemCount==this.formItemsCount){
        this.isVisible=false;
        this.toggleSearchStatus=true;
        //this.isOpenStatus=true;
      }else{
        this.isVisible=true;
        this.toggleSearchStatus=false;
        //this.isOpenStatus=false;
      }
    },
    //挂载window.onresize事件,获取当前ref=row的元素宽度值
    MountWindownOnResizeEvent(){
      // 绑定 this 到当前 Vue 实例
      if (!this.debouncedGetAreaRowSize){
        this.debouncedGetAreaRowSize = addCancelDebounce(this.getAreaRowSize.bind(this), 500);
        window.addEventListener("resize", this.debouncedGetAreaRowSize)
      }
    },
    getAreaRowSize() {
      if (this.$refs&&this.$refs.row&&this.$refs.row.$el){
        this.rowWidth = this.$refs.row.$el.offsetWidth;
        this.setBtnslocationStatus( this.rowWidth);
      }
    },
    //判断查询、重置、展开\收起按钮是否居右显示
    setBtnslocationStatus(val){
      for(let i=0;i<this.aLimitWidth.length;i++){
        if(val>=this.aLimitWidth[i]){
          this.perFormItemCount=this.perRowColCount[i];
          this.spanValue=this.perColSpan[i];
          break;
        }
        else{
          if(i>=this.aLimitWidth.length-1){
            this.perFormItemCount=this.perRowColCount[i];
            this.spanValue=this.perColSpan[i];
          }
        }
      }
      let remainder=this.toggleSearchStatus?this.formItemsCount%this.perFormItemCount:this.visibleFormItemCount%this.perFormItemCount;
      //let remainder=this.isOpenStatus?this.formItemsCount%this.perFormItemCount:this.visibleFormItemCount%this.perFormItemCount;
      if(this.perFormItemCount!=1){
        this.toRight=remainder==1?true:false;
      }
      else {
        this.toRight=true;
      }
    },
    //计算ref=row元素的宽度时，收起展开按钮调用此方法
    doToggleSearch(){
      /*toggleSearchStatus是@/mixins/JeecgListMixin.js或@/mixins/JeecgListMixinNoInit.js脚本中的变量，记录查询板块展开/收起状态，
      所以混入了JeecgListMixin或JeecgListMixinNoInit脚本的页面可以混入此脚本*/
      this.toggleSearchStatus=!this.toggleSearchStatus;
      //this.isOpenStatus=!this.isOpenStatus;
      this.setBtnslocationStatus(this.rowWidth);
    },
    //计算重置、查询、展开收起一组的span值
    colBtnsSpan(){
      return (!this.toggleSearchStatus && ((!this.toRight && this.spanValue) || 24)) || (this.toggleSearchStatus && ((!this.toRight && this.spanValue) || 24))
      //return (!this.isOpenStatus && ((!this.toRight && this.spanValue) || 24)) || (this.isOpenStatus && ((!this.toRight && this.spanValue) || 24))
    },
  }
}