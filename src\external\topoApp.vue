<template>
  <div id="app" style="width: 100%; height: 100%">
    <vis-edit ref="bigScreen" operate="show"></vis-edit>
  </div>
</template>

<script>
import { getAction, postAction, putAction, deleteAction } from '@/api/manage'
import VisEdit from '@/views/topo/nettopo/modules/VisEdit'
export default {
  components: {
    VisEdit,
  },
  data() {
    return {
      nettopoList: [],
      topoKey: '',
      topo: null,
    }
  },
  created() {},
  mounted() {
    let search = window.location.search
    if (search) {
      search = search.split('?')[1]
      search = search.split('&')[0]
      this.topoKey = search.split('=')[1]
      this.$refs.bigScreen.createTopo(this.topoKey)
    }
  },
  methods: {},
}
</script>

<style scoped>
</style>>
