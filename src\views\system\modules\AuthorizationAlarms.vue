<template>
  <a-table ref="table" bordered :rowKey="(record,index)=>{return record.id}" :columns="columns"
           :dataSource="dataSource" :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}" :pagination="ipagination"
           :loading="loading" @change="handleTableChange">
    <!-- 字符串超长截取省略号显示-->
    <template slot="index" slot-scope="text,record,index">
      <span>{{index+1}}</span>
    </template>
    <span slot="templateContent" slot-scope="text">
            <j-ellipsis :value="text" :length="25" />
          </span>
    <template slot="tooltip" slot-scope="text">
      <a-tooltip placement="topLeft" :title="text" trigger="hover">
        <div class='tooltip'>
          {{ text }}
        </div>
      </a-tooltip>
    </template>
  </a-table>
</template>
<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'

export default {
  name: 'AuthorizationAlarms',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  props:{
    record:{
      type:Object || null,
      default:()=>{return null},
      required:true,
    },
    licenseId:{
      type:String,
      default:"",
      required: true,
    },
  },
  data(){
    return{
      columns: [
        {
          title: '序号',
          dataIndex: 'index',
          scopedSlots: {
            customRender: 'index'
          },
          width: 80,
          customCell: () => {
            let cellStyle = 'text-align: center'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '告警类型',
          dataIndex: 'alarmType',
          scopedSlots: {
            customRender: 'alarmType'
          },
          customCell: () => {
            let cellStyle = 'text-align: center'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '告警原因',
          dataIndex: 'alarmReason',
          customCell: () => {
            let cellStyle = 'text-align: center'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '告警时间',
          dataIndex: 'createTime',
          scopedSlots: {
            customRender: 'createTime'
          },
        },
        // {
        //   title: '描述',
        //   dataIndex: 'description',
        //   scopedSlots: {
        //     customRender: 'description'
        //   },
        //   customCell: () => {
        //     let cellStyle = 'text-align: left;min-width: 200px;max-width:400px'
        //     return {
        //       style: cellStyle
        //     }
        //   }
        // },
        // {
        //   title: '操作',
        //   align: 'center',
        //   width: 180,
        //   fixed: 'right',
        //   dataIndex: 'action',
        //   scopedSlots: {
        //     customRender: 'action'
        //   },
        // },
      ],
      url: {
        list: 'license/alarm/list',
      },
      listUrl:"license/alarm/list",
      disableMixinCreated:true,
    }
  },
  watch:{
      licenseId(){
        if(this.licenseId!==""){
          // if(this.record && this.record.licenseName !== "local"){
          //   let contextStr = this.getContextStr()
          //   this.url.list = this.record.licenseAddress+contextStr+this.listUrl
          // }else{
          //   this.url.list = this.listUrl;
          // }
          this.url.list = this.listUrl;
          this.queryParam.licenseId = this.licenseId;
          this.loadData()
        }
      }
  },
  created() {

  },
  mounted() {
  },
  methods:{
    //获取接口上下文路径；
    getContextStr(){
      let apiUrl = window._CONFIG['domianURL'];
      let httpStrs = apiUrl.split("//");
      if(httpStrs.length > 0){
        let host = httpStrs[httpStrs.length-1];
        let strs = host.split("/")
        if(strs.length > 1){
          return "/"+strs[strs.length-1]+"/"
        }

      }
      return ""
    },
  }
}
</script>

<style scoped lang='less'>

</style>