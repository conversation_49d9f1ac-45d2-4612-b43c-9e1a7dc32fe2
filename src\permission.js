import Vue from 'vue'
import router from './router'
import store from './store'
import NProgress from 'nprogress' // progress bar
import 'nprogress/nprogress.css' // progress bar style
import { ACCESS_TOKEN, INDEX_MAIN_PAGE_PATH, ONE_PLATFORM_FLAG, PLATFORM_TYPE, USER_INFO, PTM_CHANGE } from '@/store/mutation-types'
import { generateBigscreenRouter, generateIndexRouter, getQueryStringRegExp, getStatsMenus } from '@/utils/util'
import { getAction } from '@/api/manage'
import { logout } from '@api/login'

NProgress.configure({ showSpinner: false }) // NProgress Configuration
//监听页面刷新卸载事件 防止清除sso access_token
window.addEventListener('beforeunload', function () {
  if (store.state.app.ssoAccessToken) {
    sessionStorage.setItem('sso_access_token', store.state.app.ssoAccessToken)
  }
})

//, '/user/register', '/user/register-result', '/user/alteration'
/**
 * 白名单
 * */
const whiteList = [
  '/user/login',
  '/user/register',
  '/user/register-result',
  '/user/alteration',
  '/user/activation',
  '/oneClickHelp/index',
  '/oneClickHelp/login',
  '/oneClickHelp/register',
  '/zfnw/topo',
  '/knowledgeManagement/Sharing',
  '/knowledgeManagement/knowledgeBase/knowledgePdf',
  '/gateway/threeInOne',
  '/bigscreen/szIndex',
  '/operationsView/business',
  '/operationsView/comprehensive',
  '/operationsView/network',
  '/operationsView/compNational',
  '/operationsView/OpEval',
  //静态大屏
  '/static/operationsView/business',
  '/static/operationsView/comprehensive',
  '/static/operationsView/network',
  '/static/operationsView/compNational',
  '/static/operationsView/OpEval',
]

function getUserUkeyInfo() {

  let xmlstr = ''
  let loginOcx = document.getElementById('loginOcx')
  let initshare = document.getElementById('initshare')
  console.log('UKey控件 == ', initshare)
  console.log('UKey控件的方法 == ', initshare.GetUserInfo)
  if (initshare && initshare.GetUserInfo) {
    xmlstr = initshare.GetUserInfo(window.config.appUri)
  } else if (loginOcx && loginOcx.GetUserInfo) {
    xmlstr = loginOcx.GetUserInfo(window.config.appUri)
  }
  if (process.env.NODE_ENV === 'development') {
    // xmlstr = Vue.ls.get("TEXT_API") || ""
    // console.log("时使用ukey", loginOcx)
    // xmlstr = '<nam>admin</nam><uri>e9ca23d68d884d4ebb19d07889727dae</uri>'
    //  xmlstr = '<nam>yw1</nam><uri>1704030018321367041</uri>'
    // xmlstr = '<nam>gcs2</nam><uri>1710909076181438465</uri>'
    // xmlstr = '<nam>auditadm</nam><uri>5c58815be87b476ba7fa92ecbd3a8e9c</uri>'
  }
  return xmlstr
}

let isReq = false
let autoLoginDone = false
let isGetPlatform = false
let isPopstate = false
//运维助手 ukey登录需要重写路由逻辑
function ukeyRoute(to) {
  if ([1, 2].includes(window.config.UKey) === false) return
  let xmlstr = getUserUkeyInfo()
  let sxml = Vue.ls.get('USER-INFO-XML')
  // console.log("执行了", xmlstr, sxml, isReq)
  if (xmlstr && Vue.ls.get(ACCESS_TOKEN) === null && !isReq) {
    Vue.ls.set('USER-INFO-XML', xmlstr)
    isReq = true
    getAction('license/licenseTest')
      .then((res) => {
        if (res.status == 200 || res.code == 200) {
          store.dispatch('UkeyLoginBT', { userInfoXml: xmlstr })
            .then(res => {
              // Vue.prototype.$message.success("正在登录！")
              // console.log("ukey 登录 === ",to)
              if (to.query) {
                sessionStorage.setItem('UKEY-LOGIN-PARAM', JSON.stringify(to.query))
              }
              router.push({ path: '/' })
              return
            }).catch((err) => {
              Vue.prototype.$message.error('UKey登录失败！')
              if (router.currentRoute.path !== '/user/login') {
                router.push({ path: '/user/login' })
              }
            })
        } else {
          if (router.currentRoute.path !== '/user/login') {
            router.push({ path: '/user/login', query: { authorizedOrNot: '1' } })
          }

        }

      })
      .catch((err) => {
        if (router.currentRoute.path !== '/user/login') {
          router.push({ path: '/user/login', query: { authorizedOrNot: '1' } })
        }
        // if(router.currentRoute.path !=="/user/login" && next){
        //   next({ path: '/user/login', query: { authorizedOrNot: '1' } })
        // }
      })
    let tiemr = setTimeout(() => {
      isReq = false
      clearTimeout(tiemr)
    }, 3000)
  } else if (Vue.ls.get(ACCESS_TOKEN) && Vue.ls.get('LOGIN_TYPE') === 'ukey' && xmlstr !== sxml) {
    clearUKeyTimer()
    store.dispatch('Logout').then(() => {
      window.location.reload()
    })
    return
  } else if (xmlstr && Vue.ls.get(ACCESS_TOKEN) && Vue.ls.get('LOGIN_TYPE') === 'account') {
    clearUKeyTimer()
    store.dispatch('Logout').then(() => {
      window.location.reload()
    })
    return
  } else if (Vue.ls.get(ACCESS_TOKEN) === null) {
    // if (to.path === '/user/login') {
    //   next()
    // } else {
    //   next({ path: '/user/login' })
    // }
    router.push({ path: '/user/login' })
  }
  setUkeyTimer()
}

function clearUKeyTimer() {
  if (window.ukeyTimer) {
    clearTimeout(window.ukeyTimer)
    window.ukeyTimer = null
  }
}

function setUkeyTimer(to, next) {
  if ([1, 2].includes(window.config.UKey)) {
    clearUKeyTimer()
    window.ukeyTimer = setTimeout(() => {
      ukeyRoute(to, next)
    }, 1000 * 3)
  }

}

//判断是否输入白名单路由 无条件进入
function isWhite(to) {
  return whiteList.indexOf(to.path) !== -1 || to.meta.oneClickHelp
}

function isOneClickHelp(to) {

  return to.path.startsWith('/oneClickHelp') && window.config.oneClickHelp.pageStyle === 'multiple'
}

// function setMenusQuery(menus) {
//   menus.forEach(menu => {
//     if (menu.children && menu.children.length > 0) {
//       setMenusQuery(menu.children);
//     } else {
//       menu.query = { hostname: Vue.ls.get("ONE_CLICK_HELP_MAC") }
//     }
//   });
//   return menus
// }
router.beforeEach((to, from, next) => {
  if (window.config.ENABLE_SKYWALKING_REPORT) {
    //路由上报到skywalking
    ClientMonitor.setPerformance({
      pagePath: location.href,//当前路由地址。
      useFmp: true,
      vue: Vue,
    });//因为有些参数已经在ClientMonitor.register中注册过了所以不用填加了。
  }

  // sso access_token 登录；
  /*中软单点登录start*/
  let accessToken = getQueryStringRegExp('access_token')
  let TIO = getQueryStringRegExp('threeInOne')
  // console.log("进入 === ", to.path,location.pathname)
  if (accessToken) {
    let strArr = accessToken.split('#')
    let accessTokenStr = strArr[0]
    sessionStorage.setItem('sso_access_token', accessTokenStr)
    store.commit('SET_SSO_ACCESS_TOKEN', accessTokenStr)
    let exitUrl = window.config.customization?.cz_zhongruan?.exitUrl
    if (location.pathname === "/sso" || location.pathname === "/sso/") {
      if (to.path === "/gateway/threeInOne") {
        window.location.href = window.location.origin + "/sso/#/gateway/threeInOne"
        window.location.reload();
      } else if (to.path) {
        next({ path: "/gateway/threeInOne" })
      } else {
        window.location.href = window.location.origin + "/#/gateway/threeInOne"
      }
      return;
    }
    if (window.config.customization.cz_zhongruan?.threeInOne && TIO !== "TIO") {
      window.location.href = window.location.origin + "/#/gateway/threeInOne"
      if (to.path) {
        window.location.reload()
      }
      return;
    }
    //如果进入三合一页面 不需要执行登录操作
    getAction('license/licenseTest')
      .then((res) => {
        if (res.status == 200 || res.code == 200) {
          store.dispatch('SsoAccessTokenLogin', { access_token: accessTokenStr })
            .then(res => {
              getAction('/sys/permission/getUserPlatformTypeByToken').then(perRes => {
                if (perRes.success) {
                  if (perRes.result == '') {
                    store.dispatch('Logout').then(() => {
                      Vue.prototype.$message.error({ content: '没有菜单权限，请联系管理员！', duration: 5 })
                      NProgress.done()
                      return
                    })
                  } else {
                    let userPlatforms = [...perRes.result.split(',')]
                    getAction('/sys/permission/platformTypeList').then(cRes => {
                      if (cRes.success && cRes.result) {
                        let platType = cRes.result[0].value
                        if (userPlatforms.includes(platType)) {
                          sessionStorage.setItem(PLATFORM_TYPE, platType)
                        } else {
                          sessionStorage.setItem(PLATFORM_TYPE, userPlatforms[0])
                        }
                        window.location.href = window.location.origin
                      } else {
                        window.location.href = exitUrl || window.location.origin + '/#/user/login'
                      }
                    })
                  }
                }
              })

            }).catch((err) => {
              Vue.prototype.$message.error({
                content: '登录失败，请稍后再试！',
                duration: 1 ,
                onClose: () => {
                  window.location.href = exitUrl || window.location.origin + '/#/user/login'
                }
              })
            })
        } else {
          window.location.href = exitUrl || window.location.origin + '/#/user/login?authorizedOrNot=1'
        }

      })
      .catch((err) => {
        window.location.href = exitUrl || window.location.origin + '/#/user/login?authorizedOrNot=1'
      })
    return
  }
  else if ((location.pathname === "/sso" || location.pathname === "/sso/") && to.path === "/gateway/threeInOne") {
    next()
    return;
  } else if (location.pathname === "/sso" || location.pathname === "/sso/" && to.path === "/") {
    window.location.href = window.location.origin + "/#/gateway/threeInOne"
    return;
  }
  /*中软单点登录end*/

  //设置ukey定时器
  if ([1, 2].includes(window.config.UKey)) {
    if (window.ukeyTimer === undefined) {
      ukeyRoute(to, next)
    }
  }
  /* let pathArr = location.pathname.split('/')
  let firIdx = pathArr.findIndex(el => el !== '')
  if (pathArr.length > 2 && firIdx === -1) {
    window.location.href = location.origin
    return
  } else {
    let pathStr = ''
    pathArr.forEach(el => {
      if (el !== '') {
        pathStr = pathStr + '/' + el
      }
    })
    let pidx = whiteList.indexOf(pathStr)
    //hash模式下走模式的白名单；
    if (router.mode === 'hash' && pidx !== -1) {
      let url = location.origin + '/#' + whiteList[pidx]
      if (location.search) {
        url += location.search
      }
      window.location.href = url
      return
    }
  } */
  NProgress.start() // start progress bar
  if (isOneClickHelp(to)) {
    //判断是不是第一次进入运维助手页面；
    let ochSession = sessionStorage.getItem('ONE_CLICK_HELP_SES')
    if (ochSession === null) {
      //第一次进入运维助手页面时 判定是否需要账号自动登录
      let oneClickHelpInfo = Vue.ls.get('ONE_CLICK_HELP_INFO')
      // console.log("没有进入过", oneClickHelpInfo)
      if (oneClickHelpInfo && to.query && to.query.hostname === oneClickHelpInfo.hostName) {
        // 此处需要自动登录一次；
        store.dispatch('OneClickHelpAutoLogin', oneClickHelpInfo)
          .then(res => {
            // console.log("自动登录", res)
            sessionStorage.setItem('ONE_CLICK_HELP_SES', '1')
            Vue.ls.set('ONE_CLICK_HELP_MAC', to.query.hostname)
            if (to.query.clientype !== undefined) {
              sessionStorage.setItem('ONE_CLICK_CLIENTTYPE', to.query.clientype)
            }
            if (to.query.osname || to.query.cpuname) {
              sessionStorage.setItem("ONE_CLICK_HELP_TERMINAL", JSON.stringify(to.query));
            }
            if (res.success) {
              next({ path: to.path, query: to.query })
            } else {
              store.dispatch('Logout').then(() => {
                Vue.ls.set('ONE_CLICK_HELP_MAC', to.query.hostname)
                next({ path: '/oneClickHelp/login' })
              })
            }
          }).catch(err => {
            sessionStorage.setItem('ONE_CLICK_HELP_SES', '1')
            store.dispatch('Logout').then(() => {
              Vue.ls.set('ONE_CLICK_HELP_MAC', to.query.hostname)
              next({ path: '/oneClickHelp/login' })
            })
          })
        return
      } else {
        sessionStorage.setItem('ONE_CLICK_HELP_SES', '1')
      }
    }
    // else {
    //   console.log("进入过了", ochSession)
    // }
  }
  //页面路径携带此参数 不显示顶部导航栏和左侧菜单(废弃 改用直接获取地址栏参数）
  // if (to.query.hideMenu === '1') {
  //   sessionStorage.setItem('YQ_HIDE_MENU', to.query.hideMenu)
  // } 
  // http://localhost:3000/#/statsCenter/homepageStatistics/index?akey=f6b4c89071cdde1ff7df86de4383dc85&ausername=admin
  if (to.query.ausername && to.query.akey && !autoLoginDone) {
    autoLoginDone = true
    let that = this
    store.dispatch('Logout').then(() => {
    }).finally(() => {
      let param = {
        ausername: to.query.ausername,
        akey: to.query.akey,
        path: to.path
      }
      store.dispatch('autoLogin', param).then(res => {
         // 获取统计中心静态页面
      if (window.config.useStaticData === 1) {
        getStatsMenus()
      }
        if (store.getters.permissionList.length === 0) {
          let index = sessionStorage.getItem(PLATFORM_TYPE)
          store.dispatch('GetPermissionList', index).then(res => {
            const menuData = res.result.menu
            if (menuData === null || menuData === '' || menuData === undefined) {
              next({ path: '/user/login' })
              alert('当前用户没有菜单权限，请联系管理员分配权限')
              NProgress.done()
              return
            }
            let constRoutes = ['4', '8'].includes(index + '') ? generateBigscreenRouter(menuData) : generateIndexRouter(menuData)
            // 添加主界面路由
            store.dispatch('UpdateAppRouter', { constRoutes }).then(() => {
              router.addRoutes(store.getters.addRouters)
              const redirect = decodeURIComponent(from.query.redirect || to.path)
              if (to.path === redirect) {
                next({ path: to.path })
              } else {
                // 跳转到目的路由
                next({ path: redirect })
              }

              NProgress.done()
            })
          }).catch((res) => {
            autoLoginDone = false
            store.dispatch('Logout').then(() => {
              next({ path: '/user/login' })
              alert(res.message)
              NProgress.done()
            })
          })
        }
      }).catch((res) => {
        autoLoginDone = false
        store.dispatch('Logout').then(() => {
          next({ path: '/user/login' })
          alert(res.message)
          NProgress.done()
        })
      })
    })
    return;
  } else {
    /* has token */
    if (Vue.ls.get(ACCESS_TOKEN)) {
      // 获取统计中心静态页面
      if (window.config.useStaticData === 1) {
        getStatsMenus()
      }
      autoLoginDone = false
      //运维助手登录时判断 用户是否绑定了终端信息
      if (isOneClickHelp(to)) {
        if (Vue.ls.get('ONE_CLICK_HELP_MAC')) {
          let userInfo = store.getters.userInfo
          let terminalInfo = userInfo.terminalInfo
          if (Vue.ls.get('ONE_CLICK_HELP_MAC') !== terminalInfo) {
            Vue.prototype.$message.error('已登录用户没有绑定该终端信息！')
            store.dispatch('Logout').then(() => {
              //自动判断终端类型保存数据
              if (to.query.osname || to.query.cpuname) {
                sessionStorage.setItem('ONE_CLICK_HELP_TERMINAL', JSON.stringify(to.query))
              }
              Vue.ls.set('ONE_CLICK_HELP_MAC', Vue.ls.get('ONE_CLICK_HELP_MAC'))
              if (to.query.clientype !== undefined) {
                sessionStorage.setItem('ONE_CLICK_CLIENTTYPE', to.query.clientype)
              }

              next({ path: '/oneClickHelp/login' })
            })
            return
          }
        } else {
          Vue.prototype.$message.error('没有终端信息!')
          store.dispatch('Logout').then(() => {
            Vue.ls.remove('ONE_CLICK_HELP_INFO')
            next({ path: '/oneClickHelp/login' })
          })
          return
        }

      }
      //多平台 进入门户时需要判断用户的平台权限
      if (to.path === '/gateway/gateway' && window._CONFIG['system_Type'] === '0') {
        var userPlatforms = []
        if (!isGetPlatform) {
          //获取用户平台权限 没有获取平台权限之前不向下执行
          getAction('/sys/permission/getUserPlatformTypeByToken')
            .then((res) => {
              if (res.success) {
                if (res.result) {
                  userPlatforms = [...res.result.split(',')]
                } else {
                  store.dispatch('Logout').then(() => {
                    next({ path: '/user/login' })
                  })
                }

                if (userPlatforms.length == 1) {
                  //单模块
                  Vue.ls.set(ONE_PLATFORM_FLAG, true)
                  sessionStorage.setItem(PLATFORM_TYPE, userPlatforms[0])
                  next({ path: '/' })
                } else {
                  Vue.ls.set(ONE_PLATFORM_FLAG, false)
                  //多模块，跳到模块选择页面(首页)，选择模块，请求加载对应模块菜单
                  next({ path: INDEX_MAIN_PAGE_PATH })
                  NProgress.done()
                }
                isGetPlatform = true
              } else {
                store.dispatch('Logout').then(() => {
                  next({ path: '/user/login' })
                })
              }
            }).catch(err => {
              store.dispatch('Logout').then(() => {
                next({ path: '/user/login' })
              })
            })
          return
        } else if (Vue.ls.get(ONE_PLATFORM_FLAG) === false) {
          //多平台权限直接进入平台门户页面
          next()
          return
        }

      }
      if (to.path === '/user/login') {
        //首页路由跳转 为 模块首页
        let path = window._CONFIG['system_Type'] === '0' ? INDEX_MAIN_PAGE_PATH : '/'
        next({ path: path })
        NProgress.done()
      } else if (['/oneClickHelp/login'].includes(to.path)) {
        Vue.ls.set('ONE_CLICK_HELP_MAC', Vue.ls.get('ONE_CLICK_HELP_MAC'))
        next({ path: '/oneClickHelp/index' })
      } else if (isWhite(to)) {
        // 在免登录白名单，直接进入
        // clearUKeyTimer()
        next()
      } else {
        if (store.getters.permissionList.length === 0) {
          var param = sessionStorage.getItem(PLATFORM_TYPE)
          if (param == null || param == '') {
            // isGetPlats = true;
            getAction('/sys/permission/getUserPlatformTypeByToken')
              .then(res => {
                if (res.success) {
                  let plats = res.result.split(',')
                  sessionStorage.setItem(PLATFORM_TYPE, plats[0])
                }
                next({ path: '/' })
              })
            return
          }
          var path = decodeURIComponent(from.query.redirect || to.path)
          if (path === '/' || path === '/user/login') {
            if (window._CONFIG['system_Type'] === '1') {
              if (store.getters.userInfo.userIdentity === 2) {
                //首页路由跳转 为 数据中心首页
                param = '4'
              } else {
                param = '1'
              }
              sessionStorage.setItem(PLATFORM_TYPE, param)
            }
          }
          //判断是运维助手菜单页面 获取平台为9的菜单；
          if (isOneClickHelp(to)) {
            param = '9'
          }
          store.dispatch('GetPermissionList', param).then(res => {
            const menuData = res.result.menu
            if (menuData === null || menuData === '' || menuData === undefined) {
              return
            }
            if (menuData.length === 0) {
              return
            } else {
              //给运维助手菜单添加终端参数
              if (isOneClickHelp(to)) {
                // setMenusQuery(menuData)
                store.commit('SET_PERMISSIONLIST', menuData)
              }
            }
            let constRoutes = ['4', '8'].includes(param + '') ? generateBigscreenRouter(menuData) : generateIndexRouter(menuData)
            // 添加主界面路由
            store.dispatch('UpdateAppRouter', { constRoutes }).then(() => {
              // 根据roles权限生成可访问的路由表
              // 动态添加可访问路由表
              router.addRoutes(store.getters.addRouters)
              const redirect = decodeURIComponent(from.query.redirect || to.path)
              if (to.path === redirect) {
                // hack方法 确保addRoutes已完成 ,set the replace: true so the navigation will not leave a history record
                // 当系统类型为3时 刷新跳转到服务中心
                if (window._CONFIG['system_Type'] === '2' && to.path === '/') {
                  let path = menuData[0].children && menuData[0].children.length > 0 ? menuData[0].children[0].path : menuData[0].path
                  let pa = sessionStorage.getItem('UKEY-LOGIN-PARAM')
                  if (pa) {
                    pa = JSON.parse(pa)
                  } else {
                    pa = {}
                  }
                  next({ path: path, query: pa })
                } else if (Vue.ls.get(ONE_PLATFORM_FLAG) && to.path === '/') {
                  let path = menuData[0].children && menuData[0].children.length > 0 ? menuData[0].children[0].path : menuData[0].path
                  next({ path: path })
                } else {
                  //当to.path === "/" 时 由于utils.js路由设定会重定向到/gateway/gateway
                  next({ ...to, replace: true })
                }
              } else {
                // 跳转到目的路由
                next({ path: redirect })
              }
            })
          })
            .catch(() => {
              /*store.dispatch('Logout').then(() => {
                next({ path: '/user/login' })
              })*/
            })
        } else {

          let pathstr = decodeURIComponent(from.query.redirect || to.path)
          if (pathstr === '/gateway/gateway' && window._CONFIG['system_Type'] === '1') {
            if (store.getters.userInfo.userIdentity === 2) {
              //首页路由跳转 为 数据中心首页
              param = '4'
            } else {
              param = '1'
            }
            let menus = store.getters.permissionList
            let path = menus[0].children && menus[0].children.length > 0 ? menus[0].children[0].path : menus[0].path
            next({ path: path })
            window.location.reload()
            NProgress.done()
          }
          else if (pathstr === '/gateway/gateway' && ['2'].includes(window._CONFIG['system_Type'])) {
            let menus = store.getters.permissionList
            let path = menus[0].children && menus[0].children.length > 0 ? menus[0].children[0].path : menus[0].path
            next({ path: path, replace: true })
            NProgress.done()
          } else if (pathstr === "/404") {
            //404可能事子系统切换导致没有当前菜单 替换成当前子系统第一个菜单；
            let menus = store.getters.permissionList;
            let path = menus[0].children && menus[0].children.length > 0 ? menus[0].children[0].path : menus[0].path
            next({ path: path, replace: true })
            NProgress.done()
          }
          else if (to.meta.url && (to.meta.url.startsWith("https://") || to.meta.url.startsWith("http://"))) {
            //菜单配置成http链接时 跳转到链接，根据打开方式,确定在本标签页加载还是打开新标签页
            let userStr = '${username}'
            let toUrl = to.meta.url
            if (toUrl.indexOf(userStr) != -1) {
              let userinfo = Vue.ls.get(USER_INFO)
              toUrl = toUrl.replace(userStr, userinfo?.username)
            }
            let tokenStr = "${token}";
            if (toUrl.indexOf(tokenStr) != -1) {
              let token = Vue.ls.get(ACCESS_TOKEN);
              toUrl = toUrl.replace(tokenStr, token);
            }
            //如果是iframe组件 直接next
            if(to.meta.componentName==="IframePageView"){
              next()
              return
            }
            let targetStr = "_self";
            if (to.meta.internalOrExternal) {
              targetStr = "_blank";
            }
            window.open(toUrl, targetStr)
            NProgress.done()
            return;
          } else {
            next()
          }
        }
      }
    } else {
      if (isOneClickHelp(to)) {
        if (to.query && to.query.hostname) {
          Vue.ls.set('ONE_CLICK_HELP_MAC', to.query.hostname)
          if (to.query.clientype !== undefined) {
            sessionStorage.setItem('ONE_CLICK_CLIENTTYPE', to.query.clientype)
          }
          //自动判断终端类型保存数据
          if (to.query.osname || to.query.cpuname) {
            sessionStorage.setItem('ONE_CLICK_HELP_TERMINAL', JSON.stringify(to.query))
          }

        }
        if (['/oneClickHelp/login', '/oneClickHelp/register'].includes(to.path)) {
          next()
        } else {
          Vue.ls.set('ONE_CLICK_HELP_MAC', Vue.ls.get('ONE_CLICK_HELP_MAC'))
          next({ path: '/oneClickHelp/login' })
        }

      } else if (isWhite(to)) {
        // 在免登录白名单，直接进入
        next()

      } else {
        // else {
        // next({ path: '/user/login', query: { redirect: to.fullPath } })

        // }
        if (![1, 2].includes(window.config.UKey)) {
          next({ path: '/user/login' })
        }
        NProgress.done() // if current page is login will not trigger afterEach hook, so manually handle it
      }
    }
  }
})

router.afterEach((to, from) => {
  if (isPopstate) {
    //历史回退路由 监听平台变化
    let params = {
      url: to.path,
    }
    isPopstate = false
    if(whiteList.includes(to.path))return;
    store.dispatch('GetMenuPermissions', params).then(res => {
      if (res.success) {
        if (res.platformType != sessionStorage.getItem(PLATFORM_TYPE)) {
          console.log("监听路由变化 === ", sessionStorage.getItem(PLATFORM_TYPE), to, from)
          sessionStorage.setItem(PLATFORM_TYPE, res.platformType)
          store.commit(PTM_CHANGE, true)
          let menuData = res.menu
          let constRoutes = []
          if ([4, 8].includes(res.platformType)) {
            constRoutes = generateBigscreenRouter(menuData)
          } else {
            constRoutes = generateIndexRouter(menuData)
          }
          // 添加主界面路由
          store.dispatch('UpdateAppRouter', {
            constRoutes
          }).then(() => {
            router.addRoutes(store.getters.addRouters)
          })
        }
      }
    })
  }
  NProgress.done() // finish progress bar
})


function popstateCallback(e) {
  isPopstate = true
}
window.removeEventListener('popstate', popstateCallback);
window.addEventListener('popstate', popstateCallback)
