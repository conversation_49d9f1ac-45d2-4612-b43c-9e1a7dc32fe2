<template>
  <a-row :gutter="10" class="vScroll" style="height: 100%">
    <a-col>
      <div class="header-info">
        <div>
          <p class="p-info-title">
            <span>{{data.groupName}}</span>
          </p>
        </div>
        <div class="p-info-subtitle">
          <div class="count">绑定设备数：{{data.isMaxPrivilege==1?'全部设备':data.bindCount}}</div>
          <div class="description">描述：{{data.description}}</div>
        </div>
        <div style="position: absolute;right: 30px;top: 18px;">
          <img alt src="~@/assets/return1.png" style="width: 20px;height: 20px;cursor: pointer" @click="getGo" />
        </div>
      </div>
      <div class="body-info">
        <band-user-info ref="bandUserModal" :data="data"></band-user-info>
        <band-device-info ref="bandDeviceModal" :data="data"></band-device-info>
      </div>
    </a-col>
  </a-row>
</template>
<script>
  import bandUserInfo from './modules/bandUserInfo'
  import bandDeviceInfo from './modules/bandDeviceInfo'
  export default {
    name: 'groupInfo',
    components: {
      bandUserInfo,
      bandDeviceInfo
    },
    props: {
      data: {
        type: Object
      }
    },
    data() {
      return {
        disableSubmit: false,
        closable: false, //不显示右上方关闭按钮
      }
    },
    mounted() {
      this.show()
    },
    methods: {
      show() {
        this.$refs.bandUserModal.loadData()
        this.$refs.bandDeviceModal.loadData()
      },
      //返回上一级
      getGo() {
        this.$parent.pButton1(0)
      }
    }
  }
</script>

<style lang='less' scoped>
  @import '~@assets/less/scroll.less';

  .header-info {
    position: relative;
    background-color: white;
    min-height: 107px;
    margin-bottom: 16px;
    padding: 10px 24px 10px 24px;
    border-radius: 3px;

    .p-info-title {
      line-height: 45px;
      height: 45px;
      margin-bottom: 0px;
      font-size: 18px;
      color: #000000;
    }

    .p-info-subtitle {
      display: flex;
      color: rgba(0, 0, 0, 0.65);
      font-size: 14px;

      .count {
        width: 200px;
      }

      .description {
        width: calc(100% - 200px);
        word-break: break-all;
      }

    }
  }

  .body-info {
    background-color: white;
    padding: 24px;
  }
</style>