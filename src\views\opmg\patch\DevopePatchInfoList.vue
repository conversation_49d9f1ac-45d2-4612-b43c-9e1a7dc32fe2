<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll zxw">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <!-- 查询区域 -->
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <div class="table-page-search-wrapper-style">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="补丁名称">
                  <a-input
                    placeholder="请输入补丁名称"
                    :allowClear="true"
                    autocomplete="off"
                    v-model="queryParam.patchName" :maxLength="50"
                  ></a-input>
                </a-form-item>
              </a-col>

              <a-col :span="colBtnsSpan()">
                <span
                  class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                >
                  <a-button type="primary" @click="searchQuery" class="btn-search-style">查询</a-button>
                  <a-button @click="searchReset" style="margin-left: 10px" class="btn-reset-style">重置</a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <!-- 查询区域-END -->

      <a-card :bordered="false" style="flex: auto" class="core">
        <a-row class="lastBtn2">
          <!-- 操作按钮区域 -->
          <div class="table-operator">
            <a-button @click="handleAdd">新增</a-button>
            <a-button @click="handleExportXls('补丁管理表')">导出</a-button>
            <a-dropdown v-if="selectedRowKeys.length > 0">
              <a-menu slot="overlay" style='text-align: center'>
                <a-menu-item key="1" @click="batchDel">删除</a-menu-item>
              </a-menu>
              <a-button> 批量操作 <a-icon type="down" /></a-button>
            </a-dropdown>
          </div>
        </a-row>
        <!-- table区域-begin -->
        <a-table
          ref="table"
          bordered
          rowKey="id"
          :columns="columns"
          :dataSource="dataSource"
          :pagination="ipagination"
          :loading="loading"
          :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          class="j-table-force-nowrap"
          @change="handleTableChange"
        >
          <template slot="htmlSlot" slot-scope="text">
            <div v-html="text"></div>
          </template>
          <template slot="imgSlot" slot-scope="text">
            <span v-if="!text" style="font-size: 14px">无图片</span>
            <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width: 80px; font-size: 14px" />
          </template>
          <template slot="fileSlot" slot-scope="text">
            <span v-if="!text" style="font-size: 14px">无文件</span>
            <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
              下载
            </a-button>
          </template>

          <span
            slot="action"
            slot-scope="text, record"
            class="caozuo"
            style="display: inline-block; white-space: nowrap; text-align: center"
          >
            <a style="color: #409eff" @click="handleDetailPage(record)">查看</a>
            <a-divider type="vertical" />
            <a @click="handleEdit(record)">编辑</a>
            <a-divider type="vertical" />
            <a-dropdown>
              <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
              <a-menu slot="overlay">
                <!-- <a-menu-item> </a-menu-item> -->
                <a-menu-item>
                  <a style="color: #409eff" @click="fontClick(record.patchFileName)">下载补丁</a>
                </a-menu-item>
                <a-menu-item>
                  <a style="color: #409eff" @click="fontClick(record.scriptFileName)">下载脚本</a>
                </a-menu-item>
                <a-menu-item>
                  <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                    <a style="color: #409eff">删除</a>
                  </a-popconfirm>
                </a-menu-item>
              </a-menu>
            </a-dropdown>
          </span>
          <template slot="tooltip" slot-scope="text">
            <a-tooltip placement="topLeft" :title="text" trigger="hover">
              <div class="tooltip">
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
      </a-card>

      <devope-patch-info-modal ref="modalForm" @ok="modalFormOk"></devope-patch-info-modal>
      <patch-info-modal ref="patchInfoModalForm" @ok="modalFormOk"></patch-info-modal>
    </a-col>
  </a-row>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import DevopePatchInfoModal from './modules/DevopePatchInfoModal'
import PatchInfoModal from './modules/PatchInfoModal'
import { filterMultiDictText } from '@/components/dict/JDictSelectUtil'
import JSuperQuery from '@/components/jeecg/JSuperQuery.vue'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'

export default {
  name: 'DevopePatchInfoList',
  mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
  components: {
    DevopePatchInfoModal,
    JSuperQuery,
    PatchInfoModal,
  },
  data() {
    return {
      description: '补丁管理表管理页面',
      // 表头
      columns: [
        {
          title: '补丁名称',
          dataIndex: 'patchName',
        },
        {
          title: '补丁版本',
          dataIndex: 'patchVersion'
        },
        {
          title: '操作系统',
          dataIndex: 'patchOsText',
        },
        {
          title: '架构',
          dataIndex: 'fraworkText',
        },
        {
          title: '补丁文件名称',
          dataIndex: 'patchFileNameText',
          //scopedSlots: {customRender: 'fileSlot'}
        },
        {
          title: '描述',
          dataIndex: 'patchDescribe',
          customCell: () => {
            let cellStyle = 'text-align: left; min-width: 100px;max-width:300px'
            return {
              style: cellStyle,
            }
          },
          scopedSlots: {customRender: 'tooltip'}
        },
        {
          title: '是否有效',
          dataIndex: 'effectText',
        },
        {
          title: '设备类型',
          dataIndex: 'resourceTypeText',
        },
        {
          title: '操作',
          dataIndex: 'action',
          fixed: 'right',
          align: 'center',
          width: 147,
          scopedSlots: { customRender: 'action' },
        },
      ],
      url: {
        list: '/patch/devopePatchInfo/list',
        delete: '/patch/devopePatchInfo/delete',
        deleteBatch: '/patch/devopePatchInfo/deleteBatch',
        exportXlsUrl: '/patch/devopePatchInfo/exportXls',
        importExcelUrl: 'patch/devopePatchInfo/importExcel',
      },
      dictOptions: {},
    }
  },
  created() {

  },
  mounted() {},
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
  },
  methods: {
    initDictConfig() {},
    //下载
    fontClick(path) {
      window.open(window._CONFIG['downloadUrl'] + '/' + path)
    },
    handleDetails: function (record) {
      this.$refs.patchInfoModalForm.edit(record)
      this.$refs.patchInfoModalForm.title = '详情'
      this.$refs.patchInfoModalForm.disableSubmit = true
    },
  },
}
</script>
<style lang="less" scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
.table-page-search-wrapper .ant-form-inline .ant-form-item {
  margin-bottom: 0 !important;
}
.ant-table-pagination.ant-pagination {
  margin: 16px 0 0 0 !important;
}
::v-deep .ant-table-thead > tr > th {
  text-align: center;
}
::v-deep .ant-table-tbody > tr > td {
  &:nth-child(4),
  &:nth-child(5),
  &:nth-child(7),
  &:nth-child(8),
  &:nth-child(9) {
    text-align: center;
  }
  &:nth-child(2),
  &:nth-child(6) {
    text-align: left;
  }
  &:nth-child(3) {
    text-align: right;
  }
}
</style>
