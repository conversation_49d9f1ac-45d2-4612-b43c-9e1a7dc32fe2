<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll zxw">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <!-- 查询区域 -->
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="职务编码">
                  <a-input
                    :maxLength="maxLength"
                    placeholder="请输入职务编码"
                    autocomplete="off"
                    :allowClear="true"
                    v-model="queryParam.code"
                  ></a-input>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="职务名称">
                  <a-input
                    :maxLength="maxLength"
                    placeholder="请输入职务名称"
                    autocomplete="off"
                    :allowClear="true"
                    v-model="queryParam.name"
                  ></a-input>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="职级">
                  <j-dict-select-tag
                    v-model="queryParam.postRank"
                    :allowClear="true"
                    placeholder="请选择职级"
                    dictCode="position_rank"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="colBtnsSpan()">
                <span
                  class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                >
                  <a-button type="primary" @click="searchQuery" class="btn-search-style">查询</a-button>
                  <a-button @click="searchReset" class="btn-reset-style">重置</a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered="false" style="flex: auto" class="core">
        <!-- 操作按钮区域 -->
        <div class="table-operator">
          <a-button @click="handleAdd">新增</a-button>
          <a-button @click="handleExportXls('职务表')">导出</a-button>
          <a-upload
            name="file"
            :showUploadList="false"
            :multiple="false"
            :headers="tokenHeader"
            :action="importExcelUrl"
            @change="handleImportExcel"
          >
            <a-button>导入</a-button>
          </a-upload>
          <a-dropdown v-if="selectedRowKeys.length > 0">
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item key="1" @click="batchDel">删除</a-menu-item>
            </a-menu>
            <a-button>
              批量操作
              <a-icon type="down" />
            </a-button>
          </a-dropdown>
        </div>
        <!-- table区域-begin -->
        <div>
          <a-table
            ref="table"
            bordered
            rowKey="id"
            :columns="columns"
            :dataSource="dataSource"
            :pagination="ipagination"
            :loading="loading"
            :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
            @change="handleTableChange"
          >
            <span
              slot="action"
              slot-scope="text, record"
              class="caozuo"
              style="display: inline-block; white-space: nowrap; text-align: center"
            >
              <a @click="handleEdit(record)">编辑</a>

              <a-divider type="vertical" />
              <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                <a>删除</a>
              </a-popconfirm>
            </span>
          </a-table>
        </div>
        <!-- table区域-end -->
        <!-- 表单区域 -->
        <sysPosition-modal ref="modalForm" @ok="modalFormOk"></sysPosition-modal>
      </a-card>
    </a-col>
  </a-row>
</template>

<script>
import SysPositionModal from './modules/SysPositionModal'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import JDictSelectTag from '@/components/dict/JDictSelectTag'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'

export default {
  name: 'SysPositionList',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components: {
    SysPositionModal,
    JDictSelectTag,
  },
  data() {
    return {
      maxLength:50,
      description: '职务表管理页面',
      // 表头
      columns: [
        {
          title: '职务编码',
          dataIndex: 'code',
        },
        {
          title: '职务名称',
          dataIndex: 'name',
        },
        {
          title: '职级',
          dataIndex: 'postRank_dictText',
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 160,
          scopedSlots: { customRender: 'action' },
        },
      ],
      url: {
        list: '/sys/position/list',
        delete: '/sys/position/delete',
        deleteBatch: '/sys/position/deleteBatch',
        exportXlsUrl: '/sys/position/exportXls',
        importExcelUrl: '/sys/position/importExcel',
      },
    }
  },
  mounted() {},
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
  },
}
</script>
<style lang="less" scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
/*表头样式*/
::v-deep .ant-table-thead > tr > th {
  text-align: center;
  white-space: nowrap;
}

/*内容对齐方式、省略显示*/
::v-deep .ant-table-tbody > tr > td {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;

  &:first-child,
  &:nth-child(2),
  &:nth-child(3),
  &:nth-child(4) {
    text-align: center;
  }
}
</style>