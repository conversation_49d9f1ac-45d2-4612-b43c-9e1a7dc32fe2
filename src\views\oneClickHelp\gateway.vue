<template>
  <div class="mouse">
     <div class="logo">
      <img :src="logo" alt="" />
      <span class="logo-text">{{ platformTitle }}</span>
    </div>
    <div class="top">
      <a @click="exitSystem">
        <img src="../gateway/images/Tab/28.png" />
      </a>
    </div>
    <div class="center">
      <div class="center-inside">
        <div class="center-inside-left"></div>
        <div class="center-inside-core">
          <div class="center-inside-core-top">
            <!-- 1 -->
            <div class="jiankong menu-item menu-item-left">
              <a class="content" @click="goPage(paths[0])">
                <img src="../gateway/images/Tab/22.png" alt="" />
                <img src="../gateway/images/Tab/15.png" alt="" />
                <img src="../gateway/images/Tab/13.png" alt="" />
                <span>{{  paths[0]?paths[0].meta.title:"" }}</span>
              </a>
              <div class="line ">
                <img class="line-line" src="../gateway/images/Tab/2.png" alt="" />
                <img class="line-luminescence" src="../gateway/images/Tab/1.png" alt="" />
              </div>
            </div>
            <!-- 2 -->
            <div class="fuwu menu-item menu-item-left">
              <a class="content" @click="goPage(paths[1])">
                <img src="../gateway/images/Tab/22.png" alt="" />
                <img src="../gateway/images/Tab/15.png" alt="" />
                <img src="../gateway/images/Tab/21.png" alt="" />
                <span>{{ paths[1]?paths[1].meta.title:"" }}</span>
              </a>
              <div class="line">
                <img class="line-line" src="../gateway/images/Tab/3.png" alt="" />
                <img class="line-luminescence" src="../gateway/images/Tab/1.png" alt="" />
              </div>
            </div>
            <!-- 3 -->
            <div class="zichan menu-item menu-item-left">
              <a class="content" @click="goPage(paths[2])">
                <img src="../gateway/images/Tab/22.png" alt="" />
                <img src="../gateway/images/Tab/15.png" alt="" />
                <img src="../gateway/images/Tab/23.png" alt="" />
                <span>{{ paths[2]?paths[2].meta.title:"" }}</span>
              </a>
              <div class="line">
                <img class="line-line" src="../gateway/images/Tab/4.png" alt="" />
                <img class="line-luminescence" src="../gateway/images/Tab/1.png" alt="" />
              </div>
            </div>
            <!-- 4 -->
            <div class="yunwei menu-item menu-item-right">
              <div class="line">
                <img class="line-line" src="../gateway/images/Tab/7.png" alt="" />
                <img class="line-luminescence" src="../gateway/images/Tab/1.png" alt="" />
              </div>
              <a class="content" @click="goPage(paths[3])">
                <img src="../gateway/images/Tab/22.png" alt="" />
                <img src="../gateway/images/Tab/15.png" alt="" />
                <img src="../gateway/images/Tab/25.png" alt="" />
                <span>{{ paths[3]?paths[3].meta.title:"" }}</span>
              </a>
            </div>
            <!-- 5 -->
            <div class="shuju menu-item menu-item-right">
              <div class="line">
                <img class="line-line" src="../gateway/images/Tab/6.png" alt="" />
                <img class="line-luminescence" src="../gateway/images/Tab/1.png" alt="" />
              </div>
              <a class="content" @click="goPage(paths[4])">
                <img src="../gateway/images/Tab/22.png" alt="" />
                <img src="../gateway/images/Tab/15.png" alt="" />
                <img src="../gateway/images/Tab/26.png" alt="" />
                <span>{{ paths[4]?paths[4].meta.title:"" }}</span>
              </a>
            </div>
            <!-- 6 -->
            <div class="quanxian menu-item menu-item-right">
              <div class="line">
                <img class="line-line" src="../gateway/images/Tab/5.png" alt="" />
                <img class="line-luminescence" src="../gateway/images/Tab/1.png" alt="" />
              </div>
              <a class="content" @click="goPage(paths[5])">
                <img src="../gateway/images/Tab/22.png" alt="" />
                <img src="../gateway/images/Tab/15.png" alt="" />
                <img src="../gateway/images/Tab/27.png" alt="" />
                <span>{{ paths[5]?paths[5].meta.title:"" }}</span>
              </a>
            </div>

            <div class="outermostLayer">
              <img src="../gateway/images/center/12.png" alt="" v-if="simpleModel" />
            </div>
            <div class="secondFloor">
              <img src="../gateway/images/center/11.png" alt="" v-if="simpleModel" />
            </div>
            <div class="thirdFloor">
              <img src="../gateway/images/center/10.png" alt="" v-if="simpleModel" />
            </div>
            <div class="fourthFloor">
              <img src="../gateway/images/center/8.png" alt="" v-if="simpleModel" />
            </div>
            <div class="fifthFloor">
              <img src="../gateway/images/center/7.png" alt="" v-if="simpleModel" />
            </div>
            <div class="sixthFloor rotates-wise rotateTwo" v-if="simpleModel">
              <img src="../gateway/images/center/6.png" alt="" />
            </div>
            <div class="seventhFloor rotates-wise rotateTwo" v-if="simpleModel">
              <img src="../gateway/images/center/5.png" alt="" />
            </div>
            <div class="eighthFloorSim" v-if="!simpleModel">
              <div class="earthImg"></div>
            </div>
            <div class="eighthFloor" v-if="simpleModel">
              <div class="eighthFloor-earth2">
                <div class="earth" id="earth"></div>
              </div>
            </div>
            <div class="ninthFloor"></div>
            <div class="iuminescence">
              <img src="../gateway/images/center/9.png" alt="" />
            </div>

            <div class="text" v-if="simpleModel">
              <span v-html="title"></span>
            </div>
            <div class="textTwo" v-if="!simpleModel">
              <span v-html="title"></span>
            </div>

            <div class="topLeft rotates-wise rotateOne" :style="!simpleModel ? 'animation-play-state:paused' : ''">
              <img src="../gateway/images/3.png" alt="" />
              <img src="../gateway/images/2.png" alt="" />
              <img src="../gateway/images/1.png" alt="" />
            </div>
            <div class="leftMiddle rotates-wise rotateOne" :style="!simpleModel ? 'animation-play-state:paused' : ''">
              <img src="../gateway/images/3.png" alt="" />
              <img src="../gateway/images/2.png" alt="" />
              <img src="../gateway/images/1.png" alt="" />
            </div>
            <div class="lowerLeft rotates-wise rotateOne" :style="!simpleModel ? 'animation-play-state:paused' : ''">
              <img src="../gateway/images/3.png" alt="" />
              <img src="../gateway/images/2.png" alt="" />
              <img src="../gateway/images/1.png" alt="" />
            </div>
            <div class="topRight rotates-wise rotateOne" :style="!simpleModel ? 'animation-play-state:paused' : ''">
              <img src="../gateway/images/3.png" alt="" />
              <img src="../gateway/images/2.png" alt="" />
              <img src="../gateway/images/1.png" alt="" />
            </div>
            <div class="rightMiddle rotates-wise rotateOne" :style="!simpleModel ? 'animation-play-state:paused' : ''">
              <img src="../gateway/images/3.png" alt="" />
              <img src="../gateway/images/2.png" alt="" />
              <img src="../gateway/images/1.png" alt="" />
            </div>
            <div class="lowerRight rotates-wise rotateOne" :style="!simpleModel ? 'animation-play-state:paused' : ''">
              <img src="../gateway/images/3.png" alt="" />
              <img src="../gateway/images/2.png" alt="" />
              <img src="../gateway/images/1.png" alt="" />
            </div>
          </div>
          <div class="center-inside-core-bottom">
            <div class="baseTriangle">
              <div style="width: 100%; height: 100%">
                <div class="cube-wrap">
                  <div :class="simpleModel ? cube : ''">
                    <div class="front-pane" v-if="simpleModel">
                      <img src="../gateway/images/inclinedPlane2.png" alt="" />
                    </div>
                    <div class="back-pane" v-if="simpleModel">
                      <img src="../gateway/images/inclinedPlane2.png" alt="" />
                    </div>
                    <div class="top-pane" v-if="simpleModel"><img src="../gateway/images/topSurface.png" alt="" /></div>
                    <div class="left-pane" v-if="simpleModel">
                      <img src="../gateway/images/inclinedPlane2.png" alt="" />
                    </div>
                    <div class="right-pane" v-if="simpleModel">
                      <img src="../gateway/images/inclinedPlane2.png" alt="" />
                    </div>
                    <div v-if="!simpleModel" style="margin-top: 20px">
                      <img src="../gateway/images/homeBottom.png" alt="" height="150px" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="center-inside-right"></div>
      </div>
    </div>
    <div class="bottom">
      <span style="font-size:16px;letter-spacing:3px" v-html="bottomTitle"></span>
      <span>客服服务热线：{{ telphone }}</span>
    </div>
  </div>
</template>

<script>
import { getAction, postAction } from '@/api/manage'
// import echarts from 'echarts/lib/echarts'
import 'echarts-gl'
//调用menu的API，用于处理后台返回来的数据
import Vue from 'vue'
import { mapActions, mapGetters, mapState } from 'vuex'
import { generateIndexRouter,getHostNameLocal } from '@/utils/util'
import { ACCESS_TOKEN, PLATFORM_TYPE } from '@/store/mutation-types'
import router from '@/router'
import store from '@/store'
export default {
  name: 'shunt',
  //属性
  data() {
    return {
      cube: 'cube',
      title: window.config.oneClickHelp.title,
      bottomTitle: window.config.oneClickHelp.bottomTitle,
      telphone: window.config.oneClickHelp.telphone,
       platformTitle: window.config.oneClickHelp.platformTitle,
      logo: window.config.oneClickHelp.helpLogoUrl,
      simpleModel: !window._CONFIG['simpleModel'],
      url: {
        checkHasMenu: '/sys/permission/checkHasMenu',
      },
      //数据中心的类型
      dataCenterType: window.config.DataCenterType || 0,
      platformRights: {},
      paths: [],
    }
  },
  created() {
    this.entrancePlanning('9')
  },
  mounted() {
    this.getHost()
    if (this.simpleModel) {
      this.earth()
    }
  },
  //方法
  methods: {
    earth() {
      let images = require('../gateway/images/center/earth.png')
      let myChart = this.$echarts.init(document.getElementById('earth'))
      myChart.setOption({
        globe: {
          baseTexture: images,
          heightTexture: images,
          displacementScale: 0.04,
          shading: 'color',
          realisticMaterial: {
            roughness: 0.9,
          },
          viewControl: {
            // 物体自转的速度。单位为角度 / 秒，默认为10 ，也就是36秒转一圈。
            autoRotateSpeed: 8,
          },
          postEffect: {
            enable: false,
          },
          light: {
            main: {
              intensity: 1,
              shadow: true,
            },
            ambientCubemap: {
              diffuseIntensity: 0.2,
            },
          },
        },
      })
    },
    getHost() {
      this.terminalCode = getHostNameLocal()
    },
    ...mapActions(['Logout', 'GetPermissionList']),
    //退出系统
    exitSystem() {
      const that = this
      this.$confirm({
        title: '提示',
        content: '真的要注销登录吗 ?',
        okText: '确定',
        cancelText: '取消',
        class: 'oneClickHelpConfirmModal',
        onOk() {
          return that
            .Logout({})
            .then((res) => {
              that.$router.push({ path: '/oneClickHelp/login'})
            })
            .catch((err) => {
              that.$message.error({
                title: '错误',
                description: err.message,
              })
            })
        },
        onCancel() {},
      })
    },
    setMenusQuery(menus) {
      menus.forEach((menu) => {
        if (menu.children && menu.children.length > 0) {
          this.setMenusQuery(menu.children)
        } else {
          menu.query = { hostname: this.terminalCode }
        }
      })
      return menus
    },
    entrancePlanning(index) {
      //内蒙定制 服务中心跳转到配置的url
     
      const that = this
      that.GetPermissionList(index).then((res) => {
        if (res === '1') {
          this.$message.warning('没有添加菜单！')
          return
        }
        const menuData = res.result.menu
        if (menuData && menuData.length > 0) {
          this.paths = menuData.filter((el) => !el.hidden)
          // console.log('菜单根数 === ', this.paths)
          this.setMenusQuery(menuData)
          this.$store.commit('SET_PERMISSIONLIST', menuData)
        } else {
          return
        }
         sessionStorage.setItem(PLATFORM_TYPE, index)
        let constRoutes = []
       constRoutes = generateIndexRouter(menuData)
        // console.log('生成的路由啊啊 === ', constRoutes)
        // 添加主界面路由
        store
          .dispatch('UpdateAppRouter', {
            constRoutes,
          })
          .then(() => {
            // 根据roles权限生成可访问的路由表
            // 动态添加可访问路由表
            router.addRoutes(store.getters.addRouters)
          })
      })
    },
    goPage(menu) {
      if(menu === undefined){
        // this.$message.warning('空菜单！')
        return;
      }
      if (this.terminalCode === null) {
        this.$message.warning('请检查是否已经添加终端信息？')
        return
      }
      let path = ''
      if (menu.children && menu.children.length > 0) {
        path = menu.children[0].path
      } else {
        path = menu.path
      }
      router.push({ path: path})
    },
  },
}
</script>

<style lang="scss" scoped>
.body {
  overflow-y: auto;
}

.mouse {
  width: 100%;
  height: 100%;
  background-image: url(../gateway/images/beijing1.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  position: relative;
  .logo {
    position: absolute;
    top: 8px;
    left: 8px;
    z-index: 100;
    display: flex;
    align-items: center;
    img {
      width: 40px;
      height: 40px;
    }
    .logo-text {
      color: #e5e6e7;
      font-size: 24px;
      font-weight: 550;
      letter-spacing: 3px;
      margin-left: 12px;
    }
  }
  .top {
    width: 100%;
    height: 10%;
    display: flex;
    align-items: center;
    justify-content: flex-end;

    a {
      margin-right: 0.325rem /* 26/80 */;
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        width: 0.3125rem /* 25/80 */;
        height: 0.3125rem /* 25/80 */;
        opacity: 0.5;
      }
    }
  }

  .center {
    width: 100%;
    height: 80%;
    // background: skyblue;
    display: flex;
    align-items: center;
    justify-content: center;

    .center-inside {
      width: 100%;
      height: 100%;
      display: flex;

      .center-inside-left {
        width: 33.3%;
        height: 100%;
        flex-direction: column;
        position: relative; //不脱离文档流
      }

      .center-inside-core {
        width: 33.3%;
        height: 100%;
        display: flex;
        flex-direction: column;

        .center-inside-core-top {
          width: 100%;
          height: 70%;
          display: flex;
          flex-direction: column;
          align-items: center;
          position: relative; //不脱离文档流

          .menu-item-right{
            .content:hover{
              transform: scale(1.1);
              transform-origin: top left;
              z-index: 1000;
            }
          }
          .menu-item-left{
            .content:hover{
              transform: scale(1.1);
              transform-origin: top right;
              z-index: 1000;
            }
          }
          // background: skyblue;
          .jiankong {
            height: 1.25rem /* 100/80 */;
            position: absolute;
            top: -0.25rem /* 20/80 */;
            right: 6.85rem /* 548/80 */;
            display: flex;
            align-items: center;
            justify-content: center;

            .content {
              width: 2.75rem /* 220/80 */;
              height: 1.25rem /* 100/80 */;
              display: flex;
              align-items: center;
              position: relative;

              img:nth-child(1) {
                position: absolute;
                width: 2.75rem /* 220/80 */;
                height: 1.0125rem /* 81/80 */;
              }

              img:nth-child(2) {
                position: absolute;
                width: 0.5rem /* 40/80 */;
                height: 0.5rem /* 40/80 */;
                left: 0.4rem /* 32/80 */;
              }

              img:nth-child(3) {
                position: absolute;
                width: 0.3rem /* 24/80 */;
                height: 0.3rem /* 24/80 */;
                left: 0.6375rem /* 51/80 */;
                top: 0.575rem /* 46/80 */;
              }

              span {
                font-size: 0.25rem /* 20/80 */;
                font-family: Adobe Heiti Std;
                font-weight: normal;
                color: #1ee3f3;
                position: absolute;
                left: 1.125rem /* 90/80 */;
              }
            }

            .line {
              height: 1.25rem /* 100/80 */;
              position: relative;
              display: flex;
              align-items: center;
              justify-content: center;
              top: 0.525rem /* 42/80 */;

              .line-line {
                width: 2.25rem /* 180/80 */;
                height: 1.025rem /* 82/80 */;
              }

              .line-luminescence {
                width: 0.525rem /* 42/80 */;
                height: 0.05rem /* 4/80 */;
                position: absolute;
                right: 1.75rem /* 140/80 */;
                top: 0.1rem /* 8/80 */;
              }
            }
          }

          .jiankong_no {
            height: 1.25rem /* 100/80 */;
            position: absolute;
            top: -0.25rem /* 20/80 */;
            right: 6.85rem /* 548/80 */;
            display: flex;
            align-items: center;
            justify-content: center;

            .content {
              cursor: default;
              width: 2.75rem /* 220/80 */;
              height: 1.25rem /* 100/80 */;
              display: flex;
              align-items: center;
              position: relative;

              img:nth-child(1) {
                position: absolute;
                width: 2.75rem /* 220/80 */;
                height: 1.0125rem /* 81/80 */;
              }

              img:nth-child(2) {
                position: absolute;
                width: 0.5rem /* 40/80 */;
                height: 0.5rem /* 40/80 */;
                left: 0.4rem /* 32/80 */;
              }

              img:nth-child(3) {
                opacity: 0;
                position: absolute;
                width: 0.3rem /* 24/80 */;
                height: 0.3rem /* 24/80 */;
                left: 0.6375rem /* 51/80 */;
                top: 0.575rem /* 46/80 */;
              }

              span {
                font-size: 0.25rem /* 20/80 */;
                font-family: Adobe Heiti Std;
                font-weight: normal;
                color: #424242;
                position: absolute;
                left: 1.125rem /* 90/80 */;
              }
            }

            .line {
              height: 1.25rem /* 100/80 */;
              position: relative;
              display: flex;
              align-items: center;
              justify-content: center;
              top: 0.525rem /* 42/80 */;

              .line-line {
                width: 2.25rem /* 180/80 */;
                height: 1.025rem /* 82/80 */;
              }

              .line-luminescence {
                width: 0.525rem /* 42/80 */;
                height: 0.05rem /* 4/80 */;
                position: absolute;
                right: 1.75rem /* 140/80 */;
                top: 0.1rem /* 8/80 */;
              }
            }
          }

          .fuwu {
            height: 1.25rem /* 100/80 */;
            position: absolute;
            top: 3.875rem /* 310/80 */;
            right: 7.425rem /* 594/80 */;
            display: flex;
            align-items: center;
            justify-content: center;

            .content {
              width: 2.75rem /* 220/80 */;
              height: 1.25rem /* 100/80 */;
              display: flex;
              align-items: center;
              position: relative;

              img:nth-child(1) {
                position: absolute;
                width: 2.75rem /* 220/80 */;
                height: 1.0125rem /* 81/80 */;
              }

              img:nth-child(2) {
                position: absolute;
                width: 0.5rem /* 40/80 */;
                height: 0.5rem /* 40/80 */;
                left: 0.4rem /* 32/80 */;
              }

              img:nth-child(3) {
                position: absolute;
                width: 0.3rem /* 24/80 */;
                height: 0.3rem /* 24/80 */;
                left: 0.6375rem /* 51/80 */;
                top: 0.575rem /* 46/80 */;
              }

              span {
                font-size: 0.25rem /* 20/80 */;
                font-family: Adobe Heiti Std;
                font-weight: normal;
                color: #1ee3f3;
                position: absolute;
                left: 1.125rem /* 90/80 */;
              }
            }

            .line {
              height: 1.25rem /* 100/80 */;
              position: relative;
              display: flex;
              align-items: center;
              justify-content: center;
              top: -0.475rem /* 38/80 */;

              .line-line {
                width: 2.25rem /* 180/80 */;
                height: 1.025rem /* 82/80 */;
              }

              .line-luminescence {
                width: 0.525rem /* 42/80 */;
                height: 0.05rem /* 4/80 */;
                position: absolute;
                right: 1.75rem /* 140/80 */;
                top: 1.1rem /* 88/80 */;
              }
            }
          }

          .fuwu_no {
            height: 1.25rem /* 100/80 */;
            position: absolute;
            top: 3.875rem /* 310/80 */;
            right: 7.425rem /* 594/80 */;
            display: flex;
            align-items: center;
            justify-content: center;

            .content {
              cursor: default;
              width: 2.75rem /* 220/80 */;
              height: 1.25rem /* 100/80 */;
              display: flex;
              align-items: center;
              position: relative;

              img:nth-child(1) {
                position: absolute;
                width: 2.75rem /* 220/80 */;
                height: 1.0125rem /* 81/80 */;
              }

              img:nth-child(2) {
                position: absolute;
                width: 0.5rem /* 40/80 */;
                height: 0.5rem /* 40/80 */;
                left: 0.4rem /* 32/80 */;
              }

              img:nth-child(3) {
                opacity: 0;
                position: absolute;
                width: 0.3rem /* 24/80 */;
                height: 0.3rem /* 24/80 */;
                left: 0.6375rem /* 51/80 */;
                top: 0.575rem /* 46/80 */;
              }

              span {
                font-size: 0.25rem /* 20/80 */;
                font-family: Adobe Heiti Std;
                font-weight: normal;
                color: #424242;
                position: absolute;
                left: 1.125rem /* 90/80 */;
              }
            }

            .line {
              height: 1.25rem /* 100/80 */;
              position: relative;
              display: flex;
              align-items: center;
              justify-content: center;
              top: -0.475rem /* 38/80 */;

              .line-line {
                width: 2.25rem /* 180/80 */;
                height: 1.025rem /* 82/80 */;
              }

              .line-luminescence {
                width: 0.525rem /* 42/80 */;
                height: 0.05rem /* 4/80 */;
                position: absolute;
                right: 1.75rem /* 140/80 */;
                top: 1.1rem /* 88/80 */;
              }
            }
          }

          .zichan {
            height: 1.25rem /* 100/80 */;
            position: absolute;
            top: 6.8rem /* 544/80 */;
            right: 8.8rem /* 704/80 */;
            display: flex;
            align-items: center;
            justify-content: center;

            .content {
              width: 2.75rem /* 220/80 */;
              height: 1.25rem /* 100/80 */;
              display: flex;
              align-items: center;
              position: relative;

              img:nth-child(1) {
                position: absolute;
                width: 2.75rem /* 220/80 */;
                height: 1.0125rem /* 81/80 */;
              }

              img:nth-child(2) {
                position: absolute;
                width: 0.5rem /* 40/80 */;
                height: 0.5rem /* 40/80 */;
                left: 0.4rem /* 32/80 */;
              }

              img:nth-child(3) {
                position: absolute;
                width: 0.3rem /* 24/80 */;
                height: 0.3rem /* 24/80 */;
                left: 0.6375rem /* 51/80 */;
                top: 0.575rem /* 46/80 */;
              }

              span {
                font-size: 0.25rem /* 20/80 */;
                font-family: Adobe Heiti Std;
                font-weight: normal;
                color: #1ee3f3;
                position: absolute;
                left: 1.125rem /* 90/80 */;
              }
            }

            .line {
              // height: 1.25rem /* 100/80 */;
              position: relative;
              display: flex;
              align-items: center;
              justify-content: center;
              top: -0.925rem /* 74/80 */;
              left: 1.125rem /* 90/80 */;

              .line-line {
                width: 2.25rem /* 180/80 */;
                // height: 1.025rem /* 82/80 */;
                position: absolute;
                // top: -20px;
              }

              .line-luminescence {
                width: 0.525rem /* 42/80 */;
                height: 0.05rem /* 4/80 */;
                position: absolute;
                right: 0.6rem /* 48/80 */;
                top: 0.925rem /* 74/80 */;
              }
            }
          }

          .zichan_no {
            height: 1.25rem /* 100/80 */;
            position: absolute;
            top: 6.8rem /* 544/80 */;
            right: 8.8rem /* 704/80 */;
            display: flex;
            align-items: center;
            justify-content: center;

            .content {
              cursor: default;
              width: 2.75rem /* 220/80 */;
              height: 1.25rem /* 100/80 */;
              display: flex;
              align-items: center;
              position: relative;

              img:nth-child(1) {
                position: absolute;
                width: 2.75rem /* 220/80 */;
                height: 1.0125rem /* 81/80 */;
              }

              img:nth-child(2) {
                position: absolute;
                width: 0.5rem /* 40/80 */;
                height: 0.5rem /* 40/80 */;
                left: 0.4rem /* 32/80 */;
              }

              img:nth-child(3) {
                opacity: 0;
                position: absolute;
                width: 0.3rem /* 24/80 */;
                height: 0.3rem /* 24/80 */;
                left: 0.6375rem /* 51/80 */;
                top: 0.575rem /* 46/80 */;
              }

              span {
                font-size: 0.25rem /* 20/80 */;
                font-family: Adobe Heiti Std;
                font-weight: normal;
                color: #424242;
                position: absolute;
                left: 1.125rem /* 90/80 */;
              }
            }

            .line {
              // height: 1.25rem /* 100/80 */;
              position: relative;
              display: flex;
              align-items: center;
              justify-content: center;
              top: -0.925rem /* 74/80 */;
              left: 1.125rem /* 90/80 */;

              .line-line {
                width: 2.25rem /* 180/80 */;
                // height: 1.025rem /* 82/80 */;
                position: absolute;
                // top: -20px;
              }

              .line-luminescence {
                width: 0.525rem /* 42/80 */;
                height: 0.05rem /* 4/80 */;
                position: absolute;
                right: 0.6rem /* 48/80 */;
                top: 0.925rem /* 74/80 */;
              }
            }
          }

          .yunwei {
            height: 1.25rem /* 100/80 */;
            position: absolute;
            top: -0.425rem /* 34/80 */;
            left: 9.1rem /* 728/80 */;
            display: flex;
            align-items: center;
            justify-content: center;

            .content {
              width: 2.75rem /* 220/80 */;
              height: 1.25rem /* 100/80 */;
              display: flex;
              align-items: center;
              position: relative;

              img:nth-child(1) {
                position: absolute;
                width: 2.75rem /* 220/80 */;
                height: 1.0125rem /* 81/80 */;
              }

              img:nth-child(2) {
                position: absolute;
                width: 0.5rem /* 40/80 */;
                height: 0.5rem /* 40/80 */;
                left: 0.4rem /* 32/80 */;
              }

              img:nth-child(3) {
                position: absolute;
                width: 0.3rem /* 24/80 */;
                height: 0.3rem /* 24/80 */;
                left: 0.6375rem /* 51/80 */;
                top: 0.575rem /* 46/80 */;
              }

              span {
                font-size: 0.25rem /* 20/80 */;
                font-family: Adobe Heiti Std;
                font-weight: normal;
                color: #1ee3f3;
                position: absolute;
                left: 1.125rem /* 90/80 */;
              }
            }

            .line {
              // height: 1.25rem /* 100/80 */;
              position: relative;
              display: flex;
              align-items: center;
              justify-content: center;
              top: 0.625rem /* 50/80 */;
              right: 1.125rem /* 90/80 */;

              .line-line {
                width: 2.25rem /* 180/80 */;
                // height: 1.025rem /* 82/80 */;
                position: absolute;
                // top: -20px;
              }

              .line-luminescence {
                width: 0.525rem /* 42/80 */;
                height: 0.05rem /* 4/80 */;
                position: absolute;
                transform: rotateY(180deg);
                left: 0.6rem /* 48/80 */;
                top: -0.575rem /* 46/80 */;
              }
            }
          }

          .yunwei_no {
            height: 1.25rem /* 100/80 */;
            position: absolute;
            top: -0.425rem /* 34/80 */;
            left: 9.1rem /* 728/80 */;
            display: flex;
            align-items: center;
            justify-content: center;

            .content {
              cursor: default;
              width: 2.75rem /* 220/80 */;
              height: 1.25rem /* 100/80 */;
              display: flex;
              align-items: center;
              position: relative;

              img:nth-child(1) {
                position: absolute;
                width: 2.75rem /* 220/80 */;
                height: 1.0125rem /* 81/80 */;
              }

              img:nth-child(2) {
                position: absolute;
                width: 0.5rem /* 40/80 */;
                height: 0.5rem /* 40/80 */;
                left: 0.4rem /* 32/80 */;
              }

              img:nth-child(3) {
                opacity: 0;
                position: absolute;
                width: 0.3rem /* 24/80 */;
                height: 0.3rem /* 24/80 */;
                left: 0.6375rem /* 51/80 */;
                top: 0.575rem /* 46/80 */;
              }

              span {
                font-size: 0.25rem /* 20/80 */;
                font-family: Adobe Heiti Std;
                font-weight: normal;
                color: #424242;
                position: absolute;
                left: 1.125rem /* 90/80 */;
              }
            }

            .line {
              // height: 1.25rem /* 100/80 */;
              position: relative;
              display: flex;
              align-items: center;
              justify-content: center;
              top: 0.625rem /* 50/80 */;
              right: 1.125rem /* 90/80 */;

              .line-line {
                width: 2.25rem /* 180/80 */;
                // height: 1.025rem /* 82/80 */;
                position: absolute;
                // top: -20px;
              }

              .line-luminescence {
                width: 0.525rem /* 42/80 */;
                height: 0.05rem /* 4/80 */;
                position: absolute;
                transform: rotateY(180deg);
                left: 0.6rem /* 48/80 */;
                top: -0.575rem /* 46/80 */;
              }
            }
          }

          .shuju {
            height: 1.25rem /* 100/80 */;
            position: absolute;
            top: 3.625rem /* 290/80 */;
            left: 9.575rem /* 766/80 */;
            display: flex;
            align-items: center;
            justify-content: center;

            .content {
              width: 2.75rem /* 220/80 */;
              height: 1.25rem /* 100/80 */;
              display: flex;
              align-items: center;
              position: relative;

              img:nth-child(1) {
                position: absolute;
                width: 2.75rem /* 220/80 */;
                height: 1.0125rem /* 81/80 */;
              }

              img:nth-child(2) {
                position: absolute;
                width: 0.5rem /* 40/80 */;
                height: 0.5rem /* 40/80 */;
                left: 0.4rem /* 32/80 */;
              }

              img:nth-child(3) {
                position: absolute;
                width: 0.3rem /* 24/80 */;
                height: 0.3rem /* 24/80 */;
                left: 0.6375rem /* 51/80 */;
                top: 0.575rem /* 46/80 */;
              }

              span {
                font-size: 0.25rem /* 20/80 */;
                font-family: Adobe Heiti Std;
                font-weight: normal;
                color: #1ee3f3;
                position: absolute;
                left: 1.125rem /* 90/80 */;
              }
            }

            .line {
              // height: 1.25rem /* 100/80 */;
              position: relative;
              display: flex;
              align-items: center;
              justify-content: center;
              top: -0.2rem /* 16/80 */;
              right: 1.125rem /* 90/80 */;

              .line-line {
                width: 2.25rem /* 180/80 */;
                position: absolute;
              }

              .line-luminescence {
                width: 0.525rem /* 42/80 */;
                height: 0.05rem /* 4/80 */;
                position: absolute;
                transform: rotateY(180deg);
                left: 0.6rem /* 48/80 */;
                top: 0.25rem /* 20/80 */;
              }
            }
          }

          .shuju_no {
            height: 1.25rem /* 100/80 */;
            position: absolute;
            top: 3.625rem /* 290/80 */;
            left: 9.575rem /* 766/80 */;
            display: flex;
            align-items: center;
            justify-content: center;

            .content {
              cursor: default;
              width: 2.75rem /* 220/80 */;
              height: 1.25rem /* 100/80 */;
              display: flex;
              align-items: center;
              position: relative;

              img:nth-child(1) {
                position: absolute;
                width: 2.75rem /* 220/80 */;
                height: 1.0125rem /* 81/80 */;
              }

              img:nth-child(2) {
                position: absolute;
                width: 0.5rem /* 40/80 */;
                height: 0.5rem /* 40/80 */;
                left: 0.4rem /* 32/80 */;
              }

              img:nth-child(3) {
                opacity: 0;
                position: absolute;
                width: 0.3rem /* 24/80 */;
                height: 0.3rem /* 24/80 */;
                left: 0.6375rem /* 51/80 */;
                top: 0.575rem /* 46/80 */;
              }

              span {
                font-size: 0.25rem /* 20/80 */;
                font-family: Adobe Heiti Std;
                font-weight: normal;
                color: #424242;
                position: absolute;
                left: 1.125rem /* 90/80 */;
              }
            }

            .line {
              // height: 1.25rem /* 100/80 */;
              position: relative;
              display: flex;
              align-items: center;
              justify-content: center;
              top: -0.2rem /* 16/80 */;
              right: 1.125rem /* 90/80 */;

              .line-line {
                width: 2.25rem /* 180/80 */;
                position: absolute;
              }

              .line-luminescence {
                width: 0.525rem /* 42/80 */;
                height: 0.05rem /* 4/80 */;
                position: absolute;
                transform: rotateY(180deg);
                left: 0.6rem /* 48/80 */;
                top: 0.25rem /* 20/80 */;
              }
            }
          }

          .quanxian {
            height: 1.25rem /* 100/80 */;
            position: absolute;
            top: 6.75rem /* 540/80 */;
            left: 8.775rem /* 702/80 */;
            display: flex;
            align-items: center;
            justify-content: center;

            .content {
              width: 2.75rem /* 220/80 */;
              height: 1.25rem /* 100/80 */;
              display: flex;
              align-items: center;
              position: relative;

              img:nth-child(1) {
                position: absolute;
                width: 2.75rem /* 220/80 */;
                height: 1.0125rem /* 81/80 */;
              }

              img:nth-child(2) {
                position: absolute;
                width: 0.5rem /* 40/80 */;
                height: 0.5rem /* 40/80 */;
                left: 0.4rem /* 32/80 */;
              }

              img:nth-child(3) {
                position: absolute;
                width: 0.3rem /* 24/80 */;
                height: 0.3rem /* 24/80 */;
                left: 0.6375rem /* 51/80 */;
                top: 0.575rem /* 46/80 */;
              }

              span {
                font-size: 0.25rem /* 20/80 */;
                font-family: Adobe Heiti Std;
                font-weight: normal;
                color: #1ee3f3;
                position: absolute;
                left: 1.125rem /* 90/80 */;
              }
            }

            .line {
              // height: 1.25rem /* 100/80 */;
              position: relative;
              display: flex;
              align-items: center;
              justify-content: center;
              top: -0.875rem /* 70/80 */;
              right: 1.125rem /* 90/80 */;

              .line-line {
                width: 2.25rem /* 180/80 */;
                position: absolute;
              }

              .line-luminescence {
                width: 0.525rem /* 42/80 */;
                height: 0.05rem /* 4/80 */;
                position: absolute;
                transform: rotateY(180deg);
                left: 0.6rem /* 48/80 */;
                top: 0.925rem /* 74/80 */;
              }
            }
          }

          .quanxian_no {
            height: 1.25rem /* 100/80 */;
            position: absolute;
            top: 6.75rem /* 540/80 */;
            left: 8.775rem /* 702/80 */;
            display: flex;
            align-items: center;
            justify-content: center;

            .content {
              cursor: default;
              width: 2.75rem /* 220/80 */;
              height: 1.25rem /* 100/80 */;
              display: flex;
              align-items: center;
              position: relative;

              img:nth-child(1) {
                position: absolute;
                width: 2.75rem /* 220/80 */;
                height: 1.0125rem /* 81/80 */;
              }

              img:nth-child(2) {
                position: absolute;
                width: 0.5rem /* 40/80 */;
                height: 0.5rem /* 40/80 */;
                left: 0.4rem /* 32/80 */;
              }

              img:nth-child(3) {
                opacity: 0;
                position: absolute;
                width: 0.3rem /* 24/80 */;
                height: 0.3rem /* 24/80 */;
                left: 0.6375rem /* 51/80 */;
                top: 0.575rem /* 46/80 */;
              }

              span {
                font-size: 0.25rem /* 20/80 */;
                font-family: Adobe Heiti Std;
                font-weight: normal;
                color: #424242;
                position: absolute;
                left: 1.125rem /* 90/80 */;
              }
            }

            .line {
              // height: 1.25rem /* 100/80 */;
              position: relative;
              display: flex;
              align-items: center;
              justify-content: center;
              top: -0.875rem /* 70/80 */;
              right: 1.125rem /* 90/80 */;

              .line-line {
                width: 2.25rem /* 180/80 */;
                position: absolute;
              }

              .line-luminescence {
                width: 0.525rem /* 42/80 */;
                height: 0.05rem /* 4/80 */;
                position: absolute;
                transform: rotateY(180deg);
                left: 0.6rem /* 48/80 */;
                top: 0.925rem /* 74/80 */;
              }
            }
          }

          .outermostLayer {
            width: 6.5rem /* 520/80 */;
            height: 6.5rem /* 520/80 */;
            // background: red;
            position: absolute;

            img {
              width: 100%;
              height: 100%;
            }
          }

          .secondFloor {
            width: 6.5rem /* 520/80 */;
            height: 6.5rem /* 520/80 */;
            position: absolute;
            display: flex;
            align-items: center;
            justify-content: center;

            img {
              width: 6.25rem /* 500/80 */;
              height: 6.25rem /* 500/80 */;
            }
          }

          .thirdFloor {
            width: 6.5rem /* 520/80 */;
            height: 6.5rem /* 520/80 */;
            position: absolute;
            display: flex;
            align-items: center;
            justify-content: center;

            img {
              width: 6rem /* 480/80 */;
              height: 6rem /* 480/80 */;
            }
          }

          .fourthFloor {
            width: 6.5rem /* 520/80 */;
            height: 6.5rem /* 520/80 */;
            position: absolute;
            display: flex;
            align-items: center;
            justify-content: center;

            img {
              width: 5.925rem /* 474/80 */;
              height: 5.925rem /* 474/80 */;
            }
          }

          .fifthFloor {
            width: 6.5rem /* 520/80 */;
            height: 6.5rem /* 520/80 */;
            position: absolute;
            display: flex;
            align-items: center;
            justify-content: center;

            img {
              width: 5.85rem /* 468/80 */;
              height: 5.85rem /* 468/80 */;
            }
          }

          .sixthFloor {
            width: 6.5rem /* 520/80 */;
            height: 6.5rem /* 520/80 */;
            position: absolute;
            display: flex;
            align-items: center;
            justify-content: center;

            img {
              width: 5rem /* 400/80 */;
              height: 5rem /* 400/80 */;
              -webkit-animation: internalRotation 10s linear infinite;
            }

            @keyframes internalRotation {
              0% {
                opacity: 1;
              }

              25% {
                opacity: 0.5;
              }

              50% {
                opacity: 0;
              }

              75% {
                opacity: 0.5;
              }

              100% {
                opacity: 1;
              }
            }
          }

          .seventhFloor {
            width: 6.5rem /* 520/80 */;
            height: 6.5rem /* 520/80 */;
            position: absolute;
            display: flex;
            align-items: center;
            justify-content: center;

            img {
              width: 5.25rem /* 420/80 */;
              height: 5.25rem /* 420/80 */;
              -webkit-animation: externalRotation 10s linear infinite;
            }

            @keyframes externalRotation {
              0% {
                opacity: 0;
              }

              25% {
                opacity: 0.5;
              }

              50% {
                opacity: 1;
              }

              75% {
                opacity: 0.5;
              }

              100% {
                opacity: 0;
              }
            }
          }

          .eighthFloorSim {
            width: 6.8rem /* 520/80 */;
            height: 6.8rem /* 520/80 */;
            position: absolute;
            display: flex;
            align-items: center;
            justify-content: center;

            .earthImg {
              border-radius: 50%;
              width: 100%;
              height: 100%;
              background: url('../gateway/images/homeCenter.png') no-repeat;
              background-position: 50%;
            }
          }

          .eighthFloor {
            width: 6.5rem /* 520/80 */;
            height: 6.5rem /* 520/80 */;
            position: absolute;
            display: flex;
            align-items: center;
            justify-content: center;

            .earthImg {
              border-radius: 50%;
              width: 100%;
              height: 100%;
              background: url('../gateway/images/homeCenter.png') no-repeat;
              background-position: 50%;
              font-size: 90% 90%;
            }

            .eighthFloor-earth2 {
              width: 360px;
              height: 360px;
              /* 360/80 */

              .earth {
                width: 100%;
                height: 100%;
              }
            }

            @keyframes loop {
              from {
                background-position: 0 0;
              }

              to {
                background-position: -841px 0;
              }

              /* 世界地图的大小为841*420，所以background-position-x: -841px */
            }
          }

          .iuminescence {
            width: 6.5rem /* 520/80 */;
            height: 6.5rem /* 520/80 */;
            position: absolute;
            display: flex;
            align-items: center;
            justify-content: center;

            img {
              width: 8rem /* 640/80 */;
              height: 8rem /* 640/80 */;
              position: relative;
              top: 2.25rem /* 180/80 */;
              right: 0.05rem /* 4/80 */;
            }
          }

          .text {
            text-align: center;
            position: absolute;
            z-index: 100;
            font-size: 0.45rem;
            //font-size: 0.3rem /* 24/80 */;;
            // color: #25dae1;
            font-weight: 600;
            background-image: -webkit-linear-gradient(bottom, #22d1e2, #2bf2d3);
            -webkit-background-clip: text;

            -webkit-text-fill-color: transparent;
            letter-spacing: 0.075rem /* 6/80 */;
            // display: flex;
            // align-items: center;
            // justify-content: center;
            top: 3.1rem /* 220/80 */;
          }

          .textTwo {
            text-align: center;
            position: absolute;
            z-index: 100;
            font-size: 0.5rem;
            //font-size: 0.3rem /* 24/80 */;;
            // color: #25dae1;
            font-weight: 600;
            background-image: -webkit-linear-gradient(bottom, #51ffe1, #41ebff);
            -webkit-background-clip: text;

            -webkit-text-fill-color: transparent;
            letter-spacing: 0.07rem /* 6/80 */;
            // display: flex;
            // align-items: center;
            // justify-content: center;
            top: 3.1rem /* 220/80 */;
          }

          .topLeft {
            width: 0.825rem /* 66/80 */;
            height: 0.825rem /* 66/80 */;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            top: 1.15rem /* 92/80 */;
            left: -2.625rem /* 210/80 */;

            img {
              position: absolute;
            }

            img:nth-child(1) {
              width: 0.825rem /* 66/80 */;
              height: 0.825rem /* 66/80 */;
            }

            img:nth-child(2) {
              width: 0.625rem /* 50/80 */;
              height: 0.625rem /* 50/80 */;
            }

            img:nth-child(3) {
              width: 0.25rem /* 20/80 */;
              height: 0.25rem /* 20/80 */;
            }
          }

          .leftMiddle {
            width: 0.825rem /* 66/80 */;
            height: 0.825rem /* 66/80 */;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            top: 2.275rem /* 182/80 */;
            left: -3.125rem /* 250/80 */;

            img {
              position: absolute;
            }

            img:nth-child(1) {
              width: 0.825rem /* 66/80 */;
              height: 0.825rem /* 66/80 */;
            }

            img:nth-child(2) {
              width: 0.625rem /* 50/80 */;
              height: 0.625rem /* 50/80 */;
            }

            img:nth-child(3) {
              width: 0.25rem /* 20/80 */;
              height: 0.25rem /* 20/80 */;
            }
          }

          .lowerLeft {
            width: 0.825rem /* 66/80 */;
            height: 0.825rem /* 66/80 */;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            top: 3.275rem /* 262/80 */;
            left: -2.3rem /* 184/80 */;

            img {
              position: absolute;
            }

            img:nth-child(1) {
              width: 0.825rem /* 66/80 */;
              height: 0.825rem /* 66/80 */;
            }

            img:nth-child(2) {
              width: 0.625rem /* 50/80 */;
              height: 0.625rem /* 50/80 */;
            }

            img:nth-child(3) {
              width: 0.25rem /* 20/80 */;
              height: 0.25rem /* 20/80 */;
            }
          }

          .topRight {
            width: 0.825rem /* 66/80 */;
            height: 0.825rem /* 66/80 */;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            top: -1.325rem /* 106/80 */;
            left: 2.625rem /* 210/80 */;

            img {
              position: absolute;
            }

            img:nth-child(1) {
              width: 0.825rem /* 66/80 */;
              height: 0.825rem /* 66/80 */;
            }

            img:nth-child(2) {
              width: 0.625rem /* 50/80 */;
              height: 0.625rem /* 50/80 */;
            }

            img:nth-child(3) {
              width: 0.25rem /* 20/80 */;
              height: 0.25rem /* 20/80 */;
            }
          }

          .rightMiddle {
            width: 0.825rem /* 66/80 */;
            height: 0.825rem /* 66/80 */;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            top: -0.225rem /* 18/80 */;
            left: 3.125rem /* 250/80 */;

            img {
              position: absolute;
            }

            img:nth-child(1) {
              width: 0.825rem /* 66/80 */;
              height: 0.825rem /* 66/80 */;
            }

            img:nth-child(2) {
              width: 0.625rem /* 50/80 */;
              height: 0.625rem /* 50/80 */;
            }

            img:nth-child(3) {
              width: 0.25rem /* 20/80 */;
              height: 0.25rem /* 20/80 */;
            }
          }

          .lowerRight {
            width: 0.825rem /* 66/80 */;
            height: 0.825rem /* 66/80 */;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            top: 0.8rem /* 64/80 */;
            left: 2.3rem /* 184/80 */;

            img {
              position: absolute;
            }

            img:nth-child(1) {
              width: 0.825rem /* 66/80 */;
              height: 0.825rem /* 66/80 */;
            }

            img:nth-child(2) {
              width: 0.625rem /* 50/80 */;
              height: 0.625rem /* 50/80 */;
            }

            img:nth-child(3) {
              width: 0.25rem /* 20/80 */;
              height: 0.25rem /* 20/80 */;
            }
          }
        }

        .center-inside-core-bottom {
          width: 100%;
          height: 30%;
          display: flex;
          flex-direction: column;
          align-items: center;
          position: relative; //不脱离文档流

          .baseTriangle {
            width: 3.95rem /* 316/80 */;
            height: 2.575rem /* 206/80 */;
            position: relative;
            // right: -0.35rem /* 28/80 */;
            // top: -0.125rem /* 10/80 */;
            display: flex;
            align-items: center;
            justify-content: center;

            .cube-wrap {
              display: flex;
              align-items: center;
              justify-content: center;
              position: relative;
            }
          }
        }
      }

      .center-inside-right {
        width: 33.3%;
        height: 100%;
        display: flex;
        flex-direction: column;
        position: relative; //不脱离文档流
        z-index: -2;
      }
    }
  }

  .bottom {
    width: 100%;
    height: 10%;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    span:nth-child(1) {
      font-size: 14px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #ffffff;
      opacity: 0.5;
      text-align: center;
    }

    span:nth-child(2) {
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: 100;
      color: #ffffff;
      opacity: 0.5;
    }
  }
}

@media (max-width: 1500px) {
  /*旋转所处的位置调整*/
  .elementTop {
    height: 15% !important;
  }

  /**旋转背景**/
  .back-opa {
    height: 100% !important;
    width: 100% !important;
  }

  /*旋转内圈*/
  .entrance-rule,
  .internal-rotate {
    height: 40vh !important;
    width: 40vh !important;
  }

  .internal-rotate {
    top: -14px !important;
  }
}

/*
	 * 鼠标悬浮时的图片效果
	 */
.planningHeader:hover {
  opacity: 1;
}

.planningCloud:hover {
  .p-entrance {
    background: url(/static/image/planningMap/boardActive.png) no-repeat center center;
    background-size: cover;
  }
}

.internal-rotate {
  background: url(/static/image/planningMap/secondsRotate.png);
  background-size: cover;
  left: -5px;
  /*//-7*/
  top: -17px;
  /*//20*/
  height: 290px;
  width: 290px;
}

/**
	 * 动画旋转效果
	 */
/*****************************顺时针*************************************/
.rotates-wise {
  position: absolute;
  transform-origin: 50% calc(50%);
}

.rotates-wise.rotateOne {
  animation: rotate-wise-one 6s normal infinite linear;
}

.rotates-wise.rotateTwo {
  animation: rotate-wise-two 20s normal infinite linear;
}

.rotates-wise.rotateThree {
  animation: rotate-wise-three 16s normal infinite linear;
}

.rotates-wise.rotateFour {
  animation: rotate-wise-three 15s normal infinite linear;
}

/**********************************逆时针*******************************/
.rotates-anti-wise {
  position: absolute;
  transform-origin: 50% 55%;
}

.rotates-anti-wise.rotateOne {
  animation: rotate-anti-wise-one 25s normal infinite linear;
}

.rotates-anti-wise.rotateTwo {
  animation: rotate-anti-wise-two 25s normal infinite linear;
}

.rotates-anti-wise.rotateThree {
  animation: rotate-anti-wise-three 25s normal infinite linear;
}

.rotates-anti-wise.rotateFour {
  animation: rotate-anti-wise-three 25s normal infinite linear;
}

/*********************************************外侧大圆****************************************************/
@keyframes rotate-wise-one {
  0% {
    transform: rotate(0deg);
  }

  50% {
    transform: rotate(180deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes rotate-wise-two {
  0% {
    transform: rotate(120deg);
  }

  50% {
    transform: rotate(300deg);
  }

  100% {
    transform: rotate(480deg);
  }
}

@keyframes rotate-wise-three {
  0% {
    transform: rotate(240deg);
  }

  50% {
    transform: rotate(420deg);
  }

  100% {
    transform: rotate(600deg);
  }
}

/*********************************************内侧小圆****************************************************/
@keyframes rotate-anti-wise-one {
  0% {
    transform: rotate(0deg);
  }

  50% {
    transform: rotate(-180deg);
  }

  100% {
    transform: rotate(-360deg);
  }
}

@keyframes rotate-anti-wise-two {
  0% {
    transform: rotate(120deg);
  }

  50% {
    transform: rotate(-60deg);
  }

  100% {
    transform: rotate(-240deg);
  }
}

@keyframes rotate-anti-wise-three {
  0% {
    transform: rotate(240deg);
  }

  50% {
    transform: rotate(60deg);
  }

  100% {
    transform: rotate(-120deg);
  }
}

/**************************************************** 不同内核浏览器**************************************************************************************/
/*********************************************外侧大圆****************************************************/
@-webkit-keyframes rotate-wise-one {
  0% {
    -webkit-transform: rotate(0deg);
  }

  50% {
    -webkit-transform: rotate(180deg);
  }

  100% {
    -webkit-transform: rotate(360deg);
  }
}

@-webkit-keyframes rotate-wise-two {
  0% {
    -webkit-transform: rotate(120deg);
  }

  50% {
    -webkit-transform: rotate(300deg);
  }

  100% {
    -webkit-transform: rotate(480deg);
  }
}

@-webkit-keyframes rotate-wise-three {
  0% {
    -webkit-transform: rotate(240deg);
  }

  50% {
    -webkit-transform: rotate(420deg);
  }

  100% {
    -webkit-transform: rotate(600deg);
  }
}

/*********************************************内侧小圆****************************************************/
@-webkit-keyframes rotate-anti-wise-one {
  0% {
    -webkit-transform: rotate(0deg);
  }

  50% {
    -webkit-transform: rotate(-180deg);
  }

  100% {
    -webkit-transform: rotate(-360deg);
  }
}

@-webkit-keyframes rotate-anti-wise-two {
  0% {
    -webkit-transform: rotate(120deg);
  }

  50% {
    -webkit-transform: rotate(-60deg);
  }

  100% {
    -webkit-transform: rotate(-240deg);
  }
}

@-webkit-keyframes rotate-anti-wise-three {
  0% {
    -webkit-transform: rotate(240deg);
  }

  50% {
    -webkit-transform: rotate(60deg);
  }

  100% {
    -webkit-transform: rotate(-120deg);
  }
}

/********************************************    Y轴旋转   ********************************************************************/
.y-axisRotation {
  position: absolute;
  transform-origin: 50% calc(50%);
}

.y-axisRotation.yrotateOne {
  animation: y-rotate-wise-one 16s normal infinite linear;
}

.y-axisRotation.yrotateTwo {
  animation: y-rotate-wise-two 16s normal infinite linear;
}

.y-axisRotation.yrotateThree {
  animation: y-rotate-wise-three 16s normal infinite linear;
}

.y-axisRotation.yrotateFour {
  animation: y-rotate-wise-three 15s normal infinite linear;
}

/**********************************逆时针*******************************/
.y-rotates-anti-wise {
  position: absolute;
  transform-origin: 50% 55%;
}

.y-rotates-anti-wise.rotateOne {
  animation: y-rotate-anti-wise-one 25s normal infinite linear;
}

.y-rotates-anti-wise.rotateTwo {
  animation: y-rotate-anti-wise-two 25s normal infinite linear;
}

.y-rotates-anti-wise.rotateThree {
  animation: y-rotate-anti-wise-three 25s normal infinite linear;
}

.y-rotates-anti-wise.rotateFour {
  animation: y-rotate-anti-wise-three 25s normal infinite linear;
}

@keyframes y-rotate-wise-one {
  // 0% {
  //   transform: rotateY(0deg);
  // }
  // 50% {
  //   transform: rotateY(180deg);
  // }
  // 100% {
  //   transform: rotateY(360deg);
  // }
  from {
    transform: rotateY(0deg);
  }

  to {
    transform: rotateY(360deg);
  }
}

@keyframes y-rotate-wise-two {
  // 0% {
  //   transform: rotateY(120deg);
  // }
  // 50% {
  //   transform: rotateY(300deg);
  // }
  // 100% {
  //   transform: rotateY(480deg);
  // }
  from {
    transform: rotateY(120deg);
  }

  to {
    transform: rotateY(480deg);
  }
}

@keyframes y-rotate-wise-three {
  // 0% {
  //   transform: rotate(240deg);
  // }
  // 50% {
  //   transform: rotate(420deg);
  // }
  // 100% {
  //   transform: rotate(600deg);
  // }
  from {
    transform: rotateY(240deg);
  }

  to {
    transform: rotateY(600deg);
  }
}

/********************************************   漂浮   ********************************************************************/
.float {
  animation: mymove 3s infinite;
  -moz-animation: mymove 3s infinite;
  /* Firefox */
  -webkit-animation: mymove 3s infinite;
  /* Safari and Chrome */
  -o-animation: mymove 3s infinite;
  /* Opera */
}

.float:hover {
  animation: none;
  -moz-animation: none;
  /* Firefox */
  -webkit-animation: none;
  /* Safari and Chrome */
  -o-animation: none;
  /* Opera */
}

@keyframes mymove {
  0% {
    top: 0px;
  }

  50% {
    top: 18px;
  }

  100% {
    top: 0px;
  }
}

@-moz-keyframes mymove

  /* Firefox */ {
  0% {
    top: 0px;
  }

  50% {
    top: 18px;
  }

  100% {
    top: 0px;
  }
}

@-webkit-keyframes mymove

  /* Safari and Chrome */ {
  0% {
    top: 0px;
  }

  50% {
    top: 18px;
  }

  100% {
    top: 0px;
  }
}

@-o-keyframes mymove

  /* Opera */ {
  0% {
    top: 0px;
  }

  50% {
    top: 18px;
  }

  100% {
    top: 0px;
  }
}

/******************************   旋转倒三角体    **********************************/
@-webkit-keyframes spin {
  from {
    -webkit-transform: rotateY(0);
  }

  to {
    -webkit-transform: rotateY(360deg);
  }
}

@keyframes spin {
  from {
    -webkit-transform: rotateY(0);
  }

  to {
    -webkit-transform: rotateY(360deg);
  }
}

.cube {
  position: relative;
  width: 2.25rem /* 180/80 */;
  -webkit-transform-style: preserve-3d;
  -webkit-animation: spin 10s infinite linear;
  -moz-transform-style: preserve-3d;
  -moz-animation: spin 10s infinite linear;
  -ms-transform-style: preserve-3d;
  -ms-animation: spin 10s infinite linear;
  transform-style: preserve-3d;
  animation: spin 10s infinite linear;
}

.cube div {
  position: absolute;
  width: 2.25rem /* 180/80 */;
  height: 2.25rem /* 180/80 */;
}

.cube div img {
  width: 100%;
  height: 100%;
}

.cube div.front-pane {
  -webkit-transform: rotateX(-30deg);
  -moz-transform: rotateX(-30deg);
  -ms-transform: rotateX(-30deg);
  transform-origin: center bottom;
  transform: rotateX(-30deg);
}

.cube div.back-pane {
  -webkit-transform: rotateX(30deg);
  -moz-transform: rotateX(30deg);
  -ms-transform: rotateX(30deg);
  transform-origin: center bottom;
  transform: rotateX(30deg);
}

.cube div.right-pane {
  -webkit-transform: rotateY(90deg) rotateX(30deg);
  -webkit-transform-origin: center bottom;
  -moz-transform: rotateY(90deg) rotateX(30deg);
  -moz-transform-origin: center bottom;
  -ms-transform: rotateY(90deg) rotateX(30deg);
  -ms-transform-origin: center bottom;
  transform: rotateY(90deg) rotateX(30deg);
  transform-origin: center bottom;
}

.cube div.left-pane {
  -webkit-transform: rotateY(90deg) rotateX(-30deg);
  -webkit-transform-origin: center bottom;
  -moz-transform: rotateY(90deg) rotateX(-30deg);
  -moz-transform-origin: center bottom;
  -ms-transform: rotateY(90deg) rotateX(-30deg);
  -ms-transform-origin: center bottom;

  transform: rotateY(90deg) rotateX(-30deg);
  transform-origin: center bottom;
}

.cube div.top-pane {
  transform: rotateX(-90deg);
  transform-origin: top center;
}

.popContainer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
}
</style>