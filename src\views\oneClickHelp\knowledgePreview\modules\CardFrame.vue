<template>
  <div class='bg'>
    <div class='head-wrapper'>
      <div class='head-img'  v-if='showHeadBgImg'></div>
      <div class='head-left'></div>
      <div class='head-right'></div>

      <div class='left'>
        <img class='img' src='/oneClickHelp/localDeviceInfo/head.png' v-if='showTitleImg'>
        <span class='title'>{{title}}</span>
      </div>
      <div class='right' v-if='showRefresh'  @click='handleRefresh' title='刷新'><a-icon type='reload' class='icon'/></div>
    </div>

    <div  class='body-wrapper'>
      <div class='body-left' :style='{width:leftWidth}'>
        <slot name='bodyLeft'></slot>
      </div>
      <div class='body-right' style='flex:1'>
        <slot name='bodyRight'></slot>
      </div>
    </div>
  </div>
</template>
<script>

export default {
  name: "CardFrame",
  props:{
    leftWidth:{
      type: String,
      required: false,
      default: '261px'
    },
    showRefresh: {
      type: Boolean,
      required: false,
      default: false
    },
    showHeadBgImg:{
      type:Boolean,
      required:false,
      default:false
    },
    showTitleImg:{
      type:Boolean,
      required:false,
      default:true
    },
    showFooter:{
      type:Boolean,
      required:false,
      default:false
    },
    title:{
      type:String,
      required:true,
    },
  },
  data() {
    return {}
  },
  methods:{
    handleRefresh(){
      this.$emit('handleRefresh')
    }
  }
}
</script>

<style scoped lang="less">
.bg {
  //border: 1px solid white;
  overflow: hidden;
  height: 100%;
  //background-image: url("/oneClickHelp/localDeviceInfo/bg.png"), linear-gradient(rgba(18, 41, 83, 0.50) 0%, rgba(41, 83, 139, 0.10) 100%);
  background-image: url("/oneClickHelp/localDeviceInfo/bg.png"), linear-gradient(180deg, rgba(18, 41, 83, 0) 0%, rgba(18, 41, 83, 0.5) 18%, rgba(41, 83, 139, 0.1) 100%);

  .head-wrapper {
    height: 0.625rem; //50px/80px
    background-color: rgba(76, 139, 255, 0.10);
    border: 1px solid rgba(47, 91, 255, 0.30);
    position: relative;
    padding-left: 0.225rem; //18px/80;
    padding-right: 0.225rem; //18px/80
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-flow: row nowrap;
    font-size: 0.2rem; //16px

    .head-img {
      position: absolute;
      content: '';
      height: 100%;
      //width: 100%;
      bottom: 0px;
      left: 0px;
      right: 0px;
      top: 0px;
      background-image: url("/oneClickHelp/localDeviceInfo/leftHeadBg.png");
      //background-size: cover;
      background-repeat: no-repeat;
      background-position: right center;
    }

    .head-left, .head-right {
      position: absolute;
      content: '';
      width: 100%;
      height: 100%;
      top: 0;
      bottom: 0;
      right: 0;
      left: 0
    }

    .head-left::before {
      position: absolute;
      content: '';
      top: 0;
      left: 0;
      width: 3px;
      height: 3px;
      background-color: #2F5BFF;
    }

    .head-left::after {
      position: absolute;
      content: '';
      bottom: 0;
      left: 0;
      width: 3px;
      height: 3px;
      background-color: #2F5BFF;
    }

    .head-right::before {
      position: absolute;
      content: '';
      top: 0;
      right: 0;
      width: 3px;
      height: 3px;
      background-color: #2F5BFF;
    }

    .head-right::after {
      position: absolute;
      content: '';
      bottom: 0;
      right: 0;
      width: 3px;
      height: 3px;
      background-color: #2F5BFF;
    }

    .left {

      .title {
        color: #ffffff;
        margin-left: 5px;
      }
    }

    .right {
      color: #ffffff;
      z-index: 9999;

      .icon {
        cursor: pointer;
      }
    }
  }

  .body-wrapper {
    height: calc(100% - 0.625rem); //calc(100% - 50px)
    //padding-top: 16px;
    display: flex;
    justify-content: start;
    align-items: start;
    flex-flow: row nowrap;


    .body-left {
      position: relative;
      height: 100%;
      overflow: auto;
      margin-right: 1px;
    }

    .body-left:before {
      position: absolute;
      content: '';
      height: 100%;
      width: 1px;
      right: 0;
      background-image: linear-gradient(#0A1424 0%, #176BEE 20%, #070F1B 100%)
    }

    .body-right {
      height: 100%;
      overflow-y: auto;
    }
  }
}

</style>