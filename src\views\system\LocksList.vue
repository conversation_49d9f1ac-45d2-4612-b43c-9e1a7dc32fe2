<template>
  <a-row :gutter='10' style='height: 100%' class='vScroll'>
    <a-col style='width: 100%; height: 100%; display: flex; flex-direction: column'>
      <!-- 查询区域 -->
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <div class='table-page-search-wrapper'>
          <!-- 搜索区域 -->
          <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
            <a-row :gutter='24' ref='row'>
              <a-col :span='spanValue'>
                <a-form-item label='用户名称'>
                  <a-input  :maxLength="maxLength" placeholder='请输入' v-model='queryParam.roleName' :allowClear='true' autocomplete='off'/>
                </a-form-item>
              </a-col>
              <a-col :span='colBtnsSpan()'>
                <span class='table-page-search-submitButtons'
                      :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button type='primary' class='btn-search btn-search-style' @click='searchQuery'>查询</a-button>
                  <a-button class='btn-reset btn-reset-style' @click='searchReset'>重置</a-button>
                  <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <a-table
          ref='table'
          class='table-style'
          bordered
          rowKey='id'
          :columns='columns'
          :dataSource='dataSource'
          :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
          :pagination='ipagination'
          :loading='loading'
        >
            <span slot='action' slot-scope='text, record' class='caozuo'>
              <a-popconfirm title='确定解锁吗?' @confirm='() => handleDelete1(record.id)'>
                <a>解锁</a>
              </a-popconfirm>

              <!-- <a-dropdown>
              <a class="ant-dropdown-link">
                更多 <a-icon type="down"/>
              </a>
              <a-menu slot="overlay">
                <a-menu-item>
                </a-menu-item>
                <a-menu-item>
                </a-menu-item>
                <a-menu-item>
                </a-menu-item>
              </a-menu>
            </a-dropdown> -->
            </span>
          <template slot='tooltip' slot-scope='text'>
            <a-tooltip placement='topLeft' :title='text' trigger='hover'>
              <div class='tooltip'>
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
      </a-card>
    </a-col>
  </a-row>
</template>
<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { deleteAction, postAction, getAction } from '@/api/manage'
import SelectUserModal from './modules/SelectUserModal'
import RoleModal from './modules/RoleModal'
import UserModal from './modules/UserModal'
import { filterObj } from '@/utils/util'
import UserRoleModal from './modules/UserRoleModal'
import moment from 'moment'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'

export default {
  name: 'LocksList',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components: {
    UserRoleModal,
    SelectUserModal,
    RoleModal,
    UserModal,
    moment
  },
  data() {
    return {
      maxLength:50,
      model: {},
      queryParam: {},
      dataSource: [],
      ipagination: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '30'],
        showTotal: (total, range) => {
          return range[0] + '-' + range[1] + ' 共' + total + '条'
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0
      },
      columns: [
        {
          title: '用户标识',
          dataIndex: 'username'
        },
        {
          title: '用户名称',
          dataIndex: 'realname'
        },
        {
          title: '锁定时间',
          dataIndex: 'locksTime'
        },
        {
          title: '操作',
          dataIndex: 'action',
          width: 150,
          fixed: 'right',
          align: 'center',
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: '/sys/role/list2',
        delete: '/sys/role/delete',
        list2: '/sys/user/userRoleList',
        addUserRole: '/sys/user/addSysUserRole',
        delete2: '/sys/role/updateLock',
        deleteBatch2: '/sys/user/deleteUserRoleBatch',
        exportXlsUrl: 'sys/role/exportXls',
        importExcelUrl: 'sys/role/importExcel'
      },
      formItemLayout: {
        labelCol: {
          style: 'width:80px'
        },
        wrapperCol: {
          style: 'width:calc(100% - 80px)'
        }
      }
    }
  },
  mounted() {

  },
  methods: {
    handleDelete1: function(id) {
      postAction(this.url.delete2, { id: id }).then((res) => {
        if (res.success) {
          this.loadData()
        } else {
          this.$message.error(res.message)
        }
      })
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
/*给table列设置宽度*/
::v-deep .ant-table-scroll .ant-table-thead > tr > th,
::v-deep .ant-table-scroll .ant-table-tbody > tr > td {
  /*用户标识*/

  &:nth-child(1) {
    min-width: 250px;
  }

  /*用户名称*/

  &:nth-child(2) {
    min-width: 250px;
  }

  /*锁定时间*/

  &:nth-child(3) {
    min-width: 250px;
  }
}

/*表头样式*/
::v-deep .ant-table-thead > tr > th {
  text-align: center;
  white-space: nowrap;
}

/*内容对齐方式、省略显示*/
::v-deep .ant-table-tbody > tr > td {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;

  &:nth-child(-n + 3) {
    text-align: center;
  }
}
</style>