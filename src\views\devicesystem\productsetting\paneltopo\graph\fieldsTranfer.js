// 节点属性与保存数据的对应
export const nodeProperties = {
    "id": "id",
    "sysOrgCode": "sysOrgCode",
    "nodePosition": "position",
    "nodeSize": "size",
    "nodeAttrs": "attrs",
    "nodeVisible": " visible",
    "nodeShape": "shape",
    "portMarkup": "portMarkup",
    "nodePorts": "ports",
    "portLabelMarkup": "portLabelMarkup",
    "nodeData": "data",
    "nodeZIndex": "zIndex",
    "nodeChildren": "children",
    "nodeParent": "parent",
    "nodeView": "view",
    "markUp": "markup",
    "nodeTools": "tools",
    "nodeAngle": "angle",
    "nodeConfig": "nodeConfig",
    "isReport": "bus_data",
}
// 连线属性与保存数据的对应
export const edgeProperties =   {
    "id": "id",
    "sysOrgCode": "sysOrgCode",
    "edgeShape": "shape",
    "edgeAttrs": "attrs",
    "edgeParent": "parent",
    "edgeZIndex": "zIndex",
    "markUp": "markup",
    "edgeView": "view",
    "edgeTools": "tools",
    "edgeData": "data",
    "edgeRouter": "router",
    "edgeConnector": "connector",
    "edgeVertices": "vertices",
    "edgeVisible": "visible",
    "edgeConfig": "edgeConfig",
    "edgeSource": "source",
    "edgeTarget": "target",
}







