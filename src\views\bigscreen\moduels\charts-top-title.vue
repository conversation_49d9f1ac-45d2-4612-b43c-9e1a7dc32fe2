<template>
  <div class="topTitle">
    <div><img :src="src" alt/></div>
    <div class="left_top_title">{{title}}</div>
  </div>
</template>

<script>
export default {
    props:['title','src']}
</script>

<style lang="less" scoped>
.topTitle {
  display: flex;
  align-items: center;
  font-size: 0.225rem;
}
.left_top_title {
  letter-spacing: 3px;
  height: 21px;
  font-size: 20px;
  line-height: 19px;
  font-family: 59--Regular;
  font-weight: 400;
  color: #ccfafe;
}
</style>