<template>
  <j-modal :title="title"
    :width="1200"
    :visible='visible'
    :maskClosable="false"
    :centered='true'
    :destroyOnClose="true"
    switchFullscreen
    :confirmLoading="confirmLoading"
    @cancel="handleCancel">
    <template slot="footer">
      <a-button key="close"
        @click="handleCancel"> 关闭 </a-button>
    </template>
    <assets-look-info ref="assetsLookInfo"
      :data="newdata"
      :tempId="tempId"
      :isFromAssetsUpdate="true"
      :isEditing="false"
      :renderStates="renderStates"></assets-look-info>
  </j-modal>
</template>

<script>

import AssetsLookInfo from '@/views/cmdb/assets/AssetsInfo.vue'
export default {
  name: 'assetsUpdateModal',
  components: {
    AssetsLookInfo
  },
  data() {
    return {
      title: '查看资产',
      visible: false,
      confirmLoading: false,
      newdata: {},
      renderStates: {
        showBaseInfo: true,
        showStateInfo: false,
        showDeviceFunction: false,
        showJournal: false,
        showAlarm: false,
        showDeviceAlarm: false,
      }
    }
  },
  props: {
    // 资产变更--临时变更id
    tempId: {
      type: String,
      default: '',
    }
  },
  methods: {
    init() {
      this.visible = true
    },
    handleCancel() {
      this.visible = false
    }
  }
}
</script>
<style scoped></style>
