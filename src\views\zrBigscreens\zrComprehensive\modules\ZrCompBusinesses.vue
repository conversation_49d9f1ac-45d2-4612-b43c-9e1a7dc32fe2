<template>
<div class='zr-comp-businesses'>
  <zr-bigscreen-title title='应用系统'></zr-bigscreen-title>
  <slot>
    <div class='businesses-content'>
      <div class='status-body'>
        <vue-seamless-scroll v-if='srollOption.autoPlay' :data='listData' class='scroll-warp' :class-option='srollOption' @click.native='goBusiness'>
          <ul class='scroll-list'  >
            <li class='scroll-list-item'  v-for='(item, index) in listData'  :key='index' :data-systemid='item.id' >
              <div class='item-name' :title='item.businessName' :style='{"--status-color":item.color}' >
                {{item.businessName}}
              </div>
              <div class='item-ip'>{{item.businessAddress}}</div>
              <div class='item-status' :style='{color:item.color}'>{{item.statusText}}</div>
            </li>
          </ul>
        </vue-seamless-scroll>
        <div v-else class='scroll-warp'>
          <ul  class='scroll-list' @click='goBusiness'>
            <li class='scroll-list-item' v-for='(item, index) in listData' :key='index' :data-systemid='item.id'>
              <div class='item-name' :title='item.businessName' :style='{"--status-color":item.color}' >
                {{item.businessName}}
              </div>
              <div class='item-ip'>{{item.businessAddress}}</div>
              <div class='item-status' :style='{color:item.color}'>{{item.statusText}}</div>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </slot>

</div>
</template>
<script>
import ZrBigscreenTitle from '@views/zrBigscreens/modules/ZrBigscreenTitle.vue'
import {businessStatus,systemList} from "@views/zrBigscreens/modules/zrUtil"
import vueSeamlessScroll from 'vue-seamless-scroll'
import resizeObserverMixin from '@views/statsCenter/com/resizeObserverMixin'
export default {
  name: 'BusinessStatus',
  components: { ZrBigscreenTitle,vueSeamlessScroll },
  mixins: [resizeObserverMixin],
  props:{
    systemList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      listData: [],
      srollOption: {
        step: 0.5, // 步长
        speed: 100, // 滚动速度
        timer: 3000,// 滚动时间间隔
        autoPlay: false,
        limitMoveNum:100000,
        singleHeight: 36 ,
      },
      maxNum: 0,
    }
  },
  created() {
  },
  mounted() {
    // this.$nextTick(()=>{
    //   this.setPlayState()
    // })
  },
  computed:{
  },
  watch:{
    systemList:{
      handler(newVal){
        this.listData = newVal
        this.mapList()
        this.setPlayState()
      },
      deep:true
    }
  },
  methods: {
    mapList() {
      this.listData = this.listData.map(el=>{
        let bs = businessStatus.find(item=>item.value == el.status)
        if(bs){
          el.color = bs.color;
          el.statusText = bs.label
        }
        return el
      })
    },
    // 屏幕变化回调
    resizeObserverCb(){
      this.setPlayState()
    },
    //设置滚动状态
    setPlayState(){
      if(this.listData.length){
        // let bounded = this.$refs.zrBusinessHardware.getBoundingClientRect()
        this.maxNum = 5
        if(this.maxNum>0 && this.maxNum<this.listData.length){
          this.srollOption.limitMoveNum = this.maxNum
          this.srollOption.autoPlay =true;
          return ;
        }
      }

      this.srollOption.autoPlay =false;
      this.srollOption.limitMoveNum = this.listData.length+1
    },
    goBusiness(e){
      const sysInfo = this.listData.find(el=>el.id == e.target.parentNode.dataset.systemid)
      if(sysInfo){
        this.$router.push({
          path: '/operationsView/business',
          query: {
            deptId:sysInfo.deptId,
            id:sysInfo.id,
          }
        })
      }
    },
  }
}
</script>

<style scoped lang='less'>
.zr-comp-businesses{
  height: 100%;
  .businesses-content{
    background: linear-gradient(to right, rgba(29, 78, 140, 0.3), rgba(29, 78, 140, 0.0));
    padding:0px 12px;
    width:100%;
    .status-body{
      width:100%;
      height: 186px;
    }
  }
}
.scroll-warp {
  height: 100%;
  max-width: 401px;
  overflow: hidden;
  .scroll-list {
    width: 100%;
    height: 100%;
    margin: 0px;
    padding: 0px;
  }

  .scroll-list-item {
    display: flex;
    align-items: center;
    color: rgba(237, 245, 255, 0.95);
    font-size: 14px;
    justify-content: space-between;
    height: 36px;
    cursor: pointer;
    .item-name {
      min-width: 40%;
      overflow: hidden;
      position: relative;
      padding-left: 0px;
      line-height: 1;
      opacity: 0.95;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      //&::before {
      //  content: '';
      //  position: absolute;
      //  left: 16px;
      //  top: 3.5px;
      //  width: 6px;
      //  height: 6px;
      //  border-radius: 50%;
      //  background: var(--status-color);
      //}
    }

    .item-ip {
      width: 40%;
      text-align: center;
      opacity: 0.95;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .item-status {
      width: 20%;
      text-align: center;
      opacity: 0.95;
    }
  }

  /*.scroll-list-odd {
    background: rgba(29, 78, 140, 0.25);
  }*/
}
</style>