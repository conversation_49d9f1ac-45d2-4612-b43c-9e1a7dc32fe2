<template>
  <a-spin :spinning='confirmLoading'>
    <j-form-container :disabled='formDisabled'>
      <a-form id='product-form' slot='detail' :form='form'>
        <a-row>
          <a-col :span='24'>
            <a-form-item :labelCol='labelCol' :wrapperCol='wrapperCol' class='two-words' label='标识'>
              <a-input v-decorator="['name', validatorRules.name]" :allowClear='true' :disabled='nameDisabled'
                autocomplete='off' placeholder='请输入标识'></a-input>
            </a-form-item>
          </a-col>
          <a-col :span='24'>
            <a-form-item :labelCol='labelCol' :wrapperCol='wrapperCol' class='two-words' label='名称'>
              <a-input v-decorator="['displayName', validatorRules.displayName]" :allowClear='true' autocomplete='off'
                placeholder='请输入名称'></a-input>
            </a-form-item>
          </a-col>
          <a-col :span='24'>
            <a-form-item :labelCol='labelCol' :wrapperCol='wrapperCol' label='产品分类' prop='productType'>
              <a-tree-select v-decorator="['assetsCategoryName', validatorRules.categoryName]"
                :getPopupContainer='(node) => node.parentNode' :tree-data='assetsCategoryTree' allowClear
                placeholder='请选择产品分类' style='width: 100%':dropdownStyle="{maxHeight: '400px',overflow: 'auto'}" @change='selectAssetsCategory' />
            </a-form-item>
          </a-col>
          <a-col :sm='10' :xs='24'>
            <a-form-item :labelCol='labelCol1' :wrapperCol='wrapperCol1' label='是否上线'>
              <a-switch v-model='online' checked-children='是' un-checked-children='否' @change='onlineChange' />
            </a-form-item>
          </a-col>
          <a-col :sm='12' :xs='24'>
            <a-form-item :labelCol='labelCol' :wrapperCol='wrapperCol' label='设备类型'>
              <a-radio-group v-model='productType'>
                <a-radio value='2'>网关子设备</a-radio>
                <a-radio value='1'>网关设备</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
          <a-col :span='24'>
            <a-form-item v-if='protocols && protocols.length && protocols.length > 0' :labelCol='labelCol'
              :wrapperCol='wrapperCol' label='传输协议'>
              <a-select v-decorator="[
                  'transferProtocol',
                  {
                    initialValue: initTransferProtocols,
                    rules: [{ required: true, message: '请选择传输协议' }],
                  },
                ]" :getPopupContainer='(node) => node.parentNode' optionFilterProp='label' mode='multiple'
                placeholder='请选择传输协议' style='width: 100%'
                @deselect='deselectTransferProtocol' @focusout @select='selectTransferProtocol'
              >
                <a-select-option v-for='(item,index) in protocols' :closable='false' :label='item.code' :key='item.id' :type='item.type'
                                 :value='item.id'>
                  {{ item.code }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span='24' v-if="!!terType">
            <a-form-item :labelCol='labelCol' :wrapperCol='wrapperCol' class='two-words' label='终端类型'>
              <a-select v-decorator="['typeValueOfTerminal']">
                <a-select-option :value="'0'">服务器</a-select-option>
                <a-select-option :value="'6'">桌面机</a-select-option>
                <a-select-option :value="'8'">笔记本</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col v-if='activeKey!==""&& selectedProtocolList && selectedProtocolList.length && selectedProtocolList.length > 0'
            :span='24'>
            <div style='width: 87%; margin: auto'>
              <a-tabs v-model='activeKey' style='width: 100%' @change='callback'>
                <a-tab-pane v-for='(item, index) in selectedProtocolList' v-if='item.collectType == 1' :key='item.transferProtocolId'
                  :force-render='true' :tab='item.transferProtocol' class='jobChange'>
                  <a-col :span='24' style='margin-top: 5px'>
                    <a-row>
                      <a-col :span='12'>
                        <a-form-item :key="'item.rate_' + item.transferProtocolId" :labelCol='labelCol3' :wrapperCol='wrapperCol3'
                          label='采集频率'>
                          <a-input-number :key="'rate_' + item.transferProtocolId" v-decorator="['item.rate_' + index,
                                {
                                  initialValue: initRateValue[index],
                                  rules: [{ required: true, message: '请输入采集频率!' }],
                                },
                              ]" :max='59' :min='1' placeholder='请输入采集频率' style='width: 90% !important;'
                            @change='changeRate($event, index)' />
                        </a-form-item>
                      </a-col>
                      <a-col :span='12'>
                        <a-form-item :key="'item.unit_' + item.transferProtocolId" :labelCol='labelCol4' :wrapperCol='wrapperCol4'
                          label='时间单位'>
                          <div style='white-space: nowrap'>
                            <a-radio-group :key="'unit_' + item.transferProtocolId"
                              v-decorator="['item.unit_' + index, { initialValue: initUnitValue[index] }]"
                              @change='changeUnit($event, index)'>
                              <a-radio :key='0' value='0'>秒</a-radio>
                              <a-radio :key='1' value='1'>分钟</a-radio>
                            </a-radio-group>
                            <a-popover title='说明'>
                              <template slot='content'>
                                <p>改变产品采集频率和JOB，会更新产品下所有设备的采集频率和JOB</p>
                              </template>
                              <a-icon type='question-circle' theme='twoTone'
                                style='font-size: 18px; line-height: 45px;margin-left: 10px' />
                            </a-popover>
                          </div>
                        </a-form-item>
                      </a-col>
                    </a-row>
                  </a-col>
                  <a-col v-if='jobItem && jobItem.length && jobItem.length > 0' :span='24'>
                    <a-form-item :key="'item.jobClassId_' + item.transferProtocolId" :labelCol='labelCol2' :wrapperCol='wrapperCol2'
                      label='JOB名称' style='font-weight: initial'>
                      <a-select :key="'jobClassId_' + item.transferProtocolId" v-decorator="[
                          'item.jobClassId_' + index,
                          {
                            initialValue: initJobIdClassValue[index],
                            rules: [{ required: true, message: '请选择job名称!' }],
                          },
                        ]" :getPopupContainer='getPopupContainer' allowClear option-label-prop='label'
                        optionFilterProp='children' placeholder='请选择job名称' show-search
                        @change='changeJob($event, index)'>
                        <a-select-option v-for='itemJob in jobItem' :key='itemJob.id' :label='itemJob.code'
                          :value='itemJob.id'>
                          {{ itemJob.code + '(' + itemJob.description + ')' }}
                        </a-select-option>
                      </a-select>
                    </a-form-item>
                  </a-col>
                </a-tab-pane>
              </a-tabs>
            </div>
          </a-col>
          <a-col :span='24' style='margin-top: 10px'>
            <a-form-item :labelCol='labelCol' :wrapperCol='wrapperCol' class='two-words' label='图标'>
              <j-image-upload
                v-decorator="['icon']"
                :accept="accept"
                default-remove
                :isMultiple='true'
                bizPath='image/productIcon'
                :handle-change-additional-fun="handleChangeAdditionalFun"
                :beforeUploadFun="beforeUploadFun">
              </j-image-upload>
            </a-form-item>
          </a-col>

          <a-col :span='24'>
            <a-form-item :labelCol='labelCol' :wrapperCol='wrapperCol' class='two-words' label='支持的型号'>
              <a-textarea v-decorator="['supportModel', validatorRules.supportModel]" :allowClear='true'
                placeholder='请输入支持的型号' rows='2' />
            </a-form-item>
          </a-col>

          <a-col :span='24'>
            <a-form-item :labelCol='labelCol' :wrapperCol='wrapperCol' class='two-words' label='描述'>
              <a-textarea v-decorator="['remark', validatorRules.remark]" :allowClear='true' placeholder='请输入描述'
                rows='4' />
            </a-form-item>
          </a-col>
          <a-col v-if='showFlowSubmitButton' :span='24' style='text-align: center'>
            <a-button @click='submitForm'>提 交</a-button>
            <a-button v-show='false' type='error' @click='info'></a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
import {ValidateOptionalFields,ValidateRequiredFields} from '@/utils/rules.js'
  import {
    getAction,
    httpAction
  } from '@/api/manage'
  import pick from 'lodash.pick'
  import JFormContainer from '@/components/jeecg/JFormContainer'
  import JImageUpload from '@/components/jeecg/JImageUpload'
  import JSearchSelectTag from '@/components/dict/JSearchSelectTag'
  import { checkAccept, checkBeforeUpload, compareFileSizes } from '@comp/yq/yqUpload/YqUploadCommonFuns'
  export default {
    name: 'ProductForm',
    components: {
      JFormContainer,
      JImageUpload,
      JSearchSelectTag
    },
    props: {
      // 流程表单data
      formData: {
        type: Object,
        default: () => {},
        required: false
      },
      // 表单模式：true流程表单 false普通表单
      formBpm: {
        type: Boolean,
        default: false,
        required: false
      },
      // 表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data() {
      let validatorNum = (rule, value, callback) => {
        if (value < 1 || value > 59) {
          callback(new Error('采集频率的值应在[1,59]范围内！'))
        }
      }
      return {
        accept:'image/jpg,image/png, image/jpeg, image/jfif, image/pjp, image/pjpeg',
        acceptTips:'jpg、png、jpeg、jfif、pjp、pjpeg',
        online: '',
        nameDisabled: false,
        value1: 1,
        dictOptions: [{
            value: 0,
            title: '是'
          },
          {
            value: -1,
            title: '否'
          }
        ],
        activeKey: '',
        form: this.$form.createForm(this),
        assetsCategoryId: '',
        assetsCategoryName: '',
        assetsCategoryTree: [], //产品分类下拉数据
        protocols: [],
        jobItem: [], //job名称下拉数据
        isOnline: '0',
        productType: '2',
        selectedProtocolList: [], //已选择的传输协议
        initTransferProtocols: [], //传输协议初始值
        initRateValue: [], //采集频率初始值
        initUnitValue: [], //采集频率单位初始值
        initJobIdClassValue: [], //job名称初始值
        proDeletProtocols:['COMMON','SPEC'],//禁止删除的协议
        model: {},
        terType: false,
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          }
        },
        labelCol1: {
          xs: {
            span: 24
          },
          sm: {
            span: 12
          }
        },
        labelCol2: {
          xs: {
            span: 24
          },
          sm: {
            span: 4
          }
        },
        labelCol3: {
          xs: {
            span: 24
          },
          sm: {
            span: 8
          }
        },
        labelCol4: {
          style: 'opacity:0',
          xs: {
            span: 24
          },
          sm: {
            span: 1
          }
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          }
        },
        wrapperCol1: {
          xs: {
            span: 24
          },
          sm: {
            span: 12
          }
        },
        wrapperCol2: {
          xs: {
            span: 24
          },
          sm: {
            span: 18
          }
        },
        wrapperCol3: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          }
        },
        wrapperCol4: {
          xs: {
            span: 24
          },
          sm: {
            span: 23
          }
        },
        confirmLoading: false,
        validatorRules: {
          name: {
            rules: [{
              required: true,
              pattern: /^([a-zA-Z][a-zA-Z0-9_]{4,63})$/,
              message: '以字母开头，可包含字母、数字或下划线，5-64个字符',
              trigger: 'blur'
            }]
          },
          displayName: {
            rules: [{
              required: true,
              validator: (rule, value, callback) =>ValidateRequiredFields(rule, value, callback,'名称',20,2)
            }]
          },
          categoryName: {
            rules: [{
              required: true,
              message: '请选择产品分类!'
            }]
          },
          transferProtocol: {
            rules: [{
              required: true,
              message: '请选择传输协议!'
            }]
          },
          supportModel: {
            rules: [{
              required: true,
              validator: (rule, value, callback) =>ValidateRequiredFields(rule, value, callback,'型号',255,1)
            }]
          },
          remark: {
            rules: [
              { required: false, validator: (rule, value, callback) =>ValidateOptionalFields(rule, value, callback,'描述',255)}
            ]
          }
        },
        url: {
          add: '/product/product/add',
          edit: '/product/product/edit',
          queryById: '/product/product/queryById',
          productTypeUrl: '/assetscategory/assetsCategory/selectAssetsCategoryTree', //资产类型接口
          queryDict: 'umpPwdManage/umpPwdManage/findSysDictItem', //根据字典code查询字典项
          querytransferProtocol: '/product/product/queryTransferProtocols', //传输协议接口
          jobItem: '/product/product/queryProductJobs' //JOB名称接口
        }
      }
    },
    computed: {
      formDisabled() {
        if (this.formBpm === true) {
          if (this.formData.disabled === false) {
            return false
          }
          return true
        }
        return this.disabled
      },
      showFlowSubmitButton() {
        if (this.formBpm === true) {
          if (this.formData.disabled === false) {
            return true
          }
        }
        return false
      }
    },
    created() {
      // 如果是流程中表单，则需要加载流程表单data
    },
    mounted() {
      this.getSelectTree()
      this.querytransferProtocol()
      this.getJobItem()
    },
    methods: {
      /**
       * 选择文件格式是否正确
       */
      beforeUploadFun(file) {
        let result = checkAccept(file, this.acceptTips,false)
        if (!result) {
          return result
        }
        return checkBeforeUpload(file, true, 10, 'MB',false)
      },
      /*文件发生改变后，附加处理方法，一次性上传多个文件，剔除列表中不满足条件的文件*/
      handleChangeAdditionalFun(fileList){
        let list = fileList
        if (fileList.length > 0) {
          list = fileList.filter((item) => {
            return compareFileSizes(true, 10, item.size, 'MB')
          }).filter((item) => {
            return checkAccept(item, this.acceptTips)
          })
        }
        return list
      },
      getPopupContainer(triggerNode) {
        return document.getElementById('product-form')
      },

      // info() {
      //   this.$message.error('请检查是否有job名称未选择');
      // },
      onlineChange(checked) {
        if (checked) {
          this.isOnline = 0
          this.online = true
        } else {
          this.isOnline = -1
          this.online = false
        }
      },
      callback(key) {},
      querytransferProtocol() {
        getAction(this.url.querytransferProtocol).then((res) => {
          if (res.success) {
            this.protocols = res.result
          }
        })
      },
      getJobItem() {
        getAction(this.url.jobItem).then((res) => {
          if (res.success) {
            this.jobItem = res.result
          }
        })
      },
      add() {
        this.edit({})
      },
      edit(record) {
        //标识，新建可输入，编辑不可输入
        this.nameDisabled = record.name != null && record.name != '' && record.name != undefined ? true : false
        this.form.resetFields()
        this.model = Object.assign({}, record)
        this.visible = true
        this.initRateValue = []
        this.initUnitValue = []
        this.initJobIdClassValue = []
        this.selectedProtocolList = []
        this.activeKey=''
        if (Object.keys(record).length !== 0) {
          if (record.selectedProtocolList && record.selectedProtocolList.length > 0) {
            for (let i = 0; i < record.selectedProtocolList.length; i++) {
              if (this.activeKey===''&&record.selectedProtocolList[i].collectType==1){
                this.activeKey=record.selectedProtocolList[i].transferProtocolId
              }
              if (record.selectedProtocolList[i].transferProtocol == 'UDP') {
                this.terType = true
              }
              this.initRateValue.push(record.selectedProtocolList[i].rate)
              this.initUnitValue.push(record.selectedProtocolList[i].unit)
              this.initJobIdClassValue.push(record.selectedProtocolList[i].jobClassId)
              this.initTransferProtocols.push(record.selectedProtocolList[i].transferProtocolId)
              let obj = JSON.parse(JSON.stringify(record.selectedProtocolList[i]))
              this.selectedProtocolList.push(obj)
            }
          }
          this.assetsCategoryId = record.assetsCategoryId
          this.productType = record.productType
          this.isOnline = record.isOnline
          this.online = this.isOnline == 0 ? true : false
          this.assetsCategoryName = record.assetsCategoryName
        }
        this.$nextTick(() => {
          this.form.setFieldsValue(
            pick(
              this.model,
              'assetsCategoryName',
              'transferProtocol',
              //'transferProtocolArray',
              'name',
              'displayName',
              'icon',
              'typeValueOfTerminal',
              'remark',
              'supportModel'
            )
          )
        })
      },
      //产品类型数据获取
      getSelectTree() {
        this.confirmLoading = true
        getAction(this.url.productTypeUrl)
          .then((res) => {
            this.assetsCategoryTree = res.result
          })
          .finally(() => {
            this.confirmLoading = false
          })
      },
      selectAssetsCategory(assetsCategoryId, data) {
        this.assetsCategoryId = assetsCategoryId
        this.assetsCategoryName = data[0]
      },
      submitForm() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.model.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'put'
            }
            let formData = Object.assign(this.model, values)
            let info = {
              id: formData.id,
              displayName: formData.displayName,
              name: formData.name,
              assetsCategoryId: this.assetsCategoryId,
              assetsCategoryName: this.assetsCategoryName,
              online: this.online,
              isOnline: this.isOnline,
              selectedProtocolList: this.selectedProtocolList,
              productType: this.productType,
              icon: formData.icon,
              typeValueOfTerminal: formData.typeValueOfTerminal,
              remark: formData.remark,
              supportModel: formData.supportModel,
              panelJson: formData.panelJson,
              createBy: formData.createBy,
              createTime: formData.createTime,
              cron: formData.cron,
              updateBy: formData.updateBy,
              updateTime: formData.updateTime
            }
            httpAction(httpurl, info, method)
              .then((res) => {
                if (res.success) {
                  that.$message.success(res.message)
                  that.$emit('ok')
                } else {
                  that.$message.warning(res.message)
                }
              })
              .finally(() => {
                that.confirmLoading = false
              })
          }
        })
      },
      popupCallback(row) {
        this.form.setFieldsValue(
          pick(
            row,
            //'collectType',
            'assetsCategoryId',
            'transferProtocol',
            'productType',
            'name',
            'displayName',
            'deviceCategoryId',
            'deviceCategoryName',
            'isOnline',
            'icon',
            'typeValueOfTerminal',
            'remark',
            'supportModel'
          )
        )
      },
      selectDeviceCategory(id, name) {
        let deviceCategoryId = id + ''
        this.deviceCategoryId = deviceCategoryId
        this.deviceCategoryName = name[0]
      },
      deselectTransferProtocol(value, option) {
        if (option.componentOptions.children[0].text.trim() == 'UDP') {
          this.terType = false
        }
        if (value&&this.prohibitDelProtocol(option.componentOptions.propsData.label)===false&&this.model.id){
          this.$message.warn(option.componentOptions.propsData.label+'是新增产品默认添加的传输协议，不能解绑')
          this.recoveryTransferProtocol()
          return
        }
        let that = this
        that.$confirm({
          title: '确认解绑',
          content: '是否确认解绑该协议并清除关联数据？',
          okText: '确认',
          cancelText: '取消',
          onOk() {
            let deleIndex=''
            let tempList = that.selectedProtocolList.filter((item, index) => {
              if (that.selectedProtocolList[index].transferProtocolId == value) {
                that.initRateValue.splice(index, 1)
                that.initUnitValue.splice(index, 1)
                that.initJobIdClassValue.splice(index, 1)
                if (value===that.activeKey){
                  deleIndex=index+''
                }
                return false
              }
              return true
            })
            that.selectedProtocolList = tempList
            if (deleIndex&&tempList.length>0){
              that.activeKey=that.activeNewTab(tempList,deleIndex)
            }else if (tempList.length == 0) {
              that.initRateValue = []
              that.initUnitValue = []
              that.initJobIdClassValue = []
              that.activeKey =''
            }
          },
          onCancel() {
            that.recoveryTransferProtocol()
          },
        })
      },
      activeNewTab(tempList,deleIndex) {
        if (!tempList[deleIndex] || tempList[deleIndex].collectType !== 1) {
          let exist = false
          let indx = 0
          for (let i = deleIndex; i > 0; i--) {
            if (tempList[i] && tempList[i].collectType == 1) {
              exist = true
              indx = i
              break
            }
          }
          if (!exist && deleIndex <= tempList.length) {
            for (let i = deleIndex - 1; i < tempList.length; i++) {
              if (tempList[i] && tempList[i].collectType == 1) {
                exist = true
                indx = i
                break
              }
            }
          }
          if (exist) {
            return tempList[indx].transferProtocolId
          } else {
            return ''
          }
        } else if (tempList[deleIndex] && tempList[deleIndex].collectType == 1) {
          return tempList[deleIndex].transferProtocolId
        }
      },
      prohibitDelProtocol(protocol){
        let canDelete=true
        if (this.proDeletProtocols.includes(protocol)){
          canDelete=false
        }
        return canDelete
      },
      recoveryTransferProtocol(){
        this.model.transferProtocol = []
        this.selectedProtocolList.forEach((ele) => {
          this.model.transferProtocol.push(ele.transferProtocolId)
        })
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.model,
            'transferProtocol'))
        });
      },
      selectTransferProtocol(value, option) {
        if (option.componentOptions.children[0].text.trim() == 'UDP') {
          this.terType = true
        }
        let type = option.data.attrs['type']
        let param = {
          transferProtocolId: value,
          transferProtocol: option.componentOptions.children[0].text.trim(),
          collectType: type,
          rate: type == 1 ? 5 : undefined,
          unit: type == 1 ? '1' : undefined,
          jobClassId: undefined
        }
        this.selectedProtocolList.push(param)
        this.activeKey =type==1? value:this.activeKey
        this.initRateValue.push(param.rate)
        this.initUnitValue.push(param.unit)
        this.initJobIdClassValue.push(param.jobClassId)
      },
      changeTransferProtocol(value, option) {
        if (value.length == 0) {
          this.selectedProtocolList = []
          this.initRateValue = []
          this.initUnitValue = []
          this.initJobIdClassValue = []
        }
      },
      changeRate(e, index) {
        this.selectedProtocolList[index].rate = e
      },
      changeUnit(e, index) {
        this.selectedProtocolList[index].unit = e.target.value
      },
      changeJob(e, index) {
        this.selectedProtocolList[index].jobClassId = e
      }
    }
  }
</script>
<style lang='less' scoped>
  @import '~@assets/less/scroll.less';

  ::v-deep .two-words>div>label {
    letter-spacing: 4px;
  }

  ::v-deep .two-words>div>label::after {
    letter-spacing: 0px;
  }

  ::v-deep .ant-upload-list-item-info>span {
    display: flex;
    justify-content: center;
  }

  ::v-deep .ant-upload-list-picture-card .ant-upload-list-item-thumbnail,
  ::v-deep .ant-upload-list-picture-card .ant-upload-list-item-thumbnail img {
    position: relative;
    display: flex;
    width: 90%;
    height: auto;
    left: 0px;
    top: 0px !important;
    align-items: center;
    justify-content: center;
  }

  .jobChange {
    background-color: #f5f5f5;
  }
</style>