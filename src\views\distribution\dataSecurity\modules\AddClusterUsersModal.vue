<template>
  <j-modal
    :title='title'
    :width='width'
    :centered='true'
    :visible='visible'
    :destroyOnClose='true'
    cancelText='关闭'
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @ok='handleOk'
    @cancel='handleCancel'
  >
    <a-spin :spinning='confirmLoading'>
      <j-form-container :disabled='disableSubmit'>
        <a-form-model ref='form' :model='model' :rules='validatorRules' slot='detail' v-bind='formItemLayout'>
          <a-row>
            <a-col :span='24'>
              <a-form-model-item label='用户名称' prop='userName'>
                <a-input v-model='model.userName' :allow-clear='true' placeholder='请输入用户名称'/>
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
            <a-form-model-item label='登录密码' prop='password'>
              <a-input type='password' placeholder='请输入登录密码' v-model="model.password"
                       :allowClear='true' autocomplete='off' />
            </a-form-model-item>
            </a-col>
              <a-col :span='24'>
            <a-form-model-item label='确认密码' prop='confirmPassword'>
              <a-input type='password' placeholder='请重新输入登录密码'
                       v-model="model.confirmPassword" :allowClear='true' autocomplete='off' />
            </a-form-model-item>
              </a-col>
          </a-row>
        </a-form-model>
      </j-form-container>
    </a-spin>
  </j-modal>
</template>
<script>
import { getAction, httpAction ,postAction} from '@api/manage'
export default {
  name: 'AddClusterUsersModal',
  data() {
    return {
      title: '新增',
      width: '600px',
      disableSubmit: false,
      visible: false,
      confirmLoading: false,
      formItemLayout: {
        labelCol: {
          xs:{span:24 },
          sm:{span:24},
          md:{span:4}
        },
        wrapperCol: {
          xs:{span:24 },
          sm:{span:24},
          md:{span:20}
        }
      },
      model: {},
      validatorRules: {
        userName: [
          { required: true,min: 1,max: 20,validator: this.validateUserName}
        ],
        password: [
          { required:true, min: 8,max:40,validator: this.validatePassword}
        ],
        confirmPassword: [
          { required: true,validator: this.validateConfirmPassword }
        ]
      },
      url: {
        add: '/distributedStorage/user/add'
      }
    }
  },
  methods: {
    validateUserName(rule, value, callback) {
      if (rule.required){
        if (!value){
          callback('请输入用户名称')
        }else {
          //let reg = /^[_a-zA-Z0-9]+$/
          let reg = /^[a-zA-Z]+$/

          let { min, max, fullField } = rule
          let reg2 = new RegExp(`^.{${min},${max}}$`)

          if (!reg.test(value)) {
            callback('只允许输入字母')
          } else if (!reg2.test(value)){
            callback(`长度${min}到${max}个字符`)
          } else {
            callback()
          }
        }
      }else {
        callback()
      }
    },
    validatePassword(rule, value, callback) {
      if (rule.required){
        if (!value){
          callback('请输入密码')
        }else {
          //let reg = /^[_a-zA-Z0-9]+$/
          let reg = /^[a-zA-Z0-9]+$/

          let { min, max, fullField } = rule
          let reg2 = new RegExp(`^.{${min},${max}}$`);

          if (!reg.test(value)) {
            callback('只允许输入字母、数字')
          } else if (!reg2.test(value)){
            callback(`长度${min}到${max}个字符`)
          } else {
            callback()
          }
        }
      }else {
        callback()
      }
    },
    validateConfirmPassword(rule, value, callback) {
      if (rule.required) {
        if (!value) {
          callback('请输入确认密码')
        } else if (value && this.model.password && this.model.password !== value) {
          callback('两次输入的密码不一样')
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
     add(val) {
      this.visible = true
      this.$nextTick(() => {
        this.model = {}
        this.model.clusterId=val
      })
    },
    handleOk() {
      let that = this
      that.$refs.form.validate((err, values) => {
        if (err) {
          that.confirmLoading = true
          let httpurl = that.url.add
          let method = 'post'

          let formData = JSON.parse(JSON.stringify(that.model))
          delete formData.confirmPassword
          //let data=JSON.stringify(formData)

/*          postAction(this.url.add,formData).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.$emit('ok')
              that.close()
            } else {
              that.$message.warning(res.message)
            }
            that.confirmLoading = false
          }).catch((err) => {
            that.$message.warning(err.message)
            that.confirmLoading = false
          })*/
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
                that.close()
              } else {
                that.$message.warning(res.message)
              }
              that.confirmLoading = false
            }).catch((err) => {
            that.$message.warning(err.message)
            that.confirmLoading = false
          })
        }
      })
    },
    handleCancel() {
      this.close()
    },
    close() {
      this.visible = false
    },
  }
}
</script>
<style scoped lang='less'>
@import '~@assets/less/normalModal.less';
</style>