<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    switchFullscreen
    :destroyOnClose="true"
    :centered='true'
    @ok="handleOk"
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @cancel="handleCancel"
    cancelText="关闭">
    <device-info-form ref="realForm" @ok="submitCallback" :disabled="disableSubmit" :scanTaskObject="scanTaskObject"></device-info-form>
  </j-modal>
</template>

<script>
  import DeviceInfoForm from './DeviceInfoForm'
  export default {
    name: 'DeviceInfoFormModal',
    components: {
      DeviceInfoForm
    },
    props: {
      scanTaskObject: {
        type: Object
      },
    },
    data () {
      return {
        title:'',
        width:'1000px',
        visible: false,
        disableSubmit: false
      }
    },
    methods: {
      add () {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.add();
        })
      },
      edit (record) {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.edit(record);
        })
      },
      close () {
        this.$emit('close');
        this.visible = false;
      },
      handleOk () {
        this.$refs.realForm.submitForm();
        this.$emit("refresh");
        // this.close();
      },
      submitCallback(e){
        this.$emit('ok',e);
        this.visible = false;
      },
      handleCancel () {
        this.close()
      }
    }
  }
</script>
<style lang="less" scoped>
@import '~@assets/less/normalModal.less';
</style>