<template>
  <a-row style="height: 100%; display: flex; overflow-x: auto">
    <a-col ref="print" id="printContent" class="abcdefg" :xl="12" :lg="12" :md="10" :sm="10" :xs="10"
      style="overflow-x: auto; min-width: 350px">
      <div>
        <p style="padding: 16px 24px; background-color: #fafafa; border-bottom: 1px solid #e8e8e8; font-size: 16px">
          Telnet连接测试
        </p>
      </div>
      <!--签字-->
      <a-col style="padding: 0 24px 24px">
        <a-form :form="form">
          <a-row>
            <a-col :span="24" class="aCol">
              <a-form-item label="IP地址" :labelCol="labelCol" :wrapperCol="wrapperCol" :required="true">
                <a-input placeholder="请输入IP地址" v-decorator="['ip', validatorRules.ip]" />
              </a-form-item>
            </a-col>
            <a-col :span="24" class="aCol">
              <a-form-item label="端  口" :labelCol="labelCol" :wrapperCol="wrapperCol" :required="true">
                <a-input placeholder="23" v-decorator="[
                    'port',
                    { initialValue: 23, rules: [{ required: true,message:'请输入端口', validator: this.port, trigger:
                    'blur' }] },
                  ]" />
              </a-form-item>
            </a-col>
            <a-col :span="24" class="aCol">
              <a-form-item label="超时时间" :labelCol="labelCol" :wrapperCol="wrapperCol" :required="true">
                <a-input placeholder="18000"
                  v-decorator="['timeout', { initialValue: 10000, rules: [{ required: true,message:'请输入超时时间' }] }]" />
              </a-form-item>
            </a-col>
            <a-col :span="24" class="aCol">
              <a-form-item label="用户名" :labelCol="labelCol" :wrapperCol="wrapperCol" :required="true">
                <a-input placeholder="请输入用户名"
                  v-decorator="['username', { rules: [{ required: true,message:'请输入用户名' }] }]" />
              </a-form-item>
            </a-col>
            <a-col :span="24" class="aCol">
              <a-form-item label="密码" :labelCol="labelCol" :wrapperCol="wrapperCol" :required="true">
                <a-input placeholder="请输入密码"
                  v-decorator="['password', { rules: [{ required: true,message:'请输入密码' }] }]" />
              </a-form-item>
            </a-col>
            <a-col :span="24" class="aCol">
              <a-form-item label="命令" :labelCol="labelCol" :wrapperCol="wrapperCol" :required="true">
                <a-input placeholder="请输入命令"
                  v-decorator="['command', { rules: [{ required: true,message:'请输入命令' }] }]" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row class="btnStyle">
            <a-col :span="24" :style="{ textAlign: 'center' }">
              <a-button type="shallow" @click="handelSubmit" :disabled="buttonDisadled"> 提交 </a-button>
              <a-button :style="{ marginLeft: '8px' }" @click="handleReset"> 重置 </a-button>
            </a-col>
          </a-row>
        </a-form>
      </a-col>
    </a-col>
    <a-col :span="12" class="contTwo" style="overflow-x: auto; min-width: 350px">
      <div class="returnDiv">
        <p class="returnTitle">Telnet连接测试</p>
        <div v-html="result" style="padding: 5px 16px 0 24px;height: calc(100% - 80px);overflow-y: auto;"></div>
      </div>
    </a-col>
  </a-row>
</template>
<script>
  import ACol from 'ant-design-vue/es/grid/Col'
  import ARow from 'ant-design-vue/es/grid/Row'
  import ATextarea from 'ant-design-vue/es/input/TextArea'
  import {
    getAction
  } from '@/api/manage'

  export default {
    components: {
      ATextarea,
      ARow,
      ACol,
    },
    name: 'Printgzsld',
    props: {
      reBizCode: {
        type: String,
        default: '',
      },
      paramIp: {
        type: String,
        default: '',
      },
    },
    watch: {
      paramIp: {
        handler(nv) {
          this.$nextTick(() => {
            this.form.setFieldsValue({
              ip: nv,
            })
          })
        },
        immediate: true,
      },
    },
    data() {
      return {
        form: this.$form.createForm(this),
        model: {},
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 8
          },
          md: {
            span: 7
          },
          lg: {
            span: 6
          },
          xl: {
            span: 5
          },
        },
        wrapperCol: {
          xs: {
            span: 23
          },
          sm: {
            span: 16
          },
          md: {
            span: 17
          },
          lg: {
            span: 18
          },
          xl: {
            span: 18
          },
        },
        buttonDisadled: false,
        url: '/connect/testTelnet',
        result: '',
        validatorRules: {
          ip: {
            rules: [{
                required: true,
                message: '请输入IP地址!'
              },
              {
                pattern: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
                message: '请输入正确的IP地址!',
              },
            ],
          },
        },
      }
    },
    created() {},
    methods: {
      port(rule, value, callback) {
        let reg = /^[0-9]*$/
        if (value) {
          if (!reg.test(value)) {
            callback('请输入正确的端口号')
          } else {
            callback()
          }
        } else {
          callback('端口号不能为空！')
        }
      },
      //提交
      handelSubmit() {
        this.form.validateFields((err, values) => {
          if (!err) {
            this.result = ''
            this.buttonDisadled = true

            let param = {
              ip: values.ip, // ip
              port: values.port, //端口
              timeout: values.timeout,
              username: values.username,
              usrPrompt: values.usrPrompt,
              password: values.password,
              pwdPrompt: values.pwdPrompt,
              loginPrompt: values.loginPrompt,
              commandPrompt: values.commandPrompt,
              command: values.command,
            }
            getAction(this.url, param).then((res) => {
              if (res.success) {
                this.result = res.result
                this.buttonDisadled = false
              }
            })
          }
        })
      },
      //刷新
      handleReset() {
        this.form.resetFields()
        this.form.setFieldsValue({
          port: 23,
        })
        this.result = ''
        this.buttonDisadled = false
      },
    },
  }
</script>
<style lang="scss" scoped>
  /*update_begin author:scott date:20191203 for:打印机打印的字体模糊问题 */
  * {
    color: #000000;
    -webkit-tap-highlight-color: #000000;
  }

  /*update_end author:scott date:20191203 for:打印机打印的字体模糊问题 */
  .importDiv {
    width: 60%;
    height: 10em;
    margin: 0 auto;
    border: 1px solid #d9d9d9;
    padding: 18px;
  }

  .returnDiv {
    height: 100%;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
  }

  .returnTitle {
    padding: 16px 24px;
    background-color: rgb(250, 250, 250);
    border-bottom: 1px solid rgb(232, 232, 232);
    font-size: 16px;
  }

  .leftSpan {
    width: 14%;
  }

  .abcdefg .ant-card-body {
    margin-left: 0%;
    margin-right: 0%;
    margin-bottom: 1%;
    border: 0px solid black;
    min-width: 800px;
    color: #000000 !important;
  }

  .explain {
    text-align: left;
    margin-left: 50px;
    color: #000000 !important;
  }

  .explain .ant-input,
  .sign .ant-input {
    font-weight: bolder;
  }

  .aCol {
    margin-bottom: 5px;
  }

  .aCol:first-child {
    margin-top: 10px;
  }

  .btnStyle {
    margin-top: 24px;
  }

  .explain div {
    margin-bottom: 10px;
  }

  /* you can make up upload button and sample style by using stylesheets */
  .ant-upload-select-picture-card i {
    font-size: 32px;
    color: #999;
  }

  .ant-upload-select-picture-card .ant-upload-text {
    margin-top: 8px;
    color: #666;
  }

  #printContent {
    height: 100%;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
  }

  .contTwo {
    margin-left: 16px;
    width: calc(100% - 50% - 16px);
    height: 100%;
  }
</style>