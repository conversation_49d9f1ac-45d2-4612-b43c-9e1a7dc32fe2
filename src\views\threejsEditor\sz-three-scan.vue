<template>
  <div ref="threeBox" id="threeScanBox" style="height: 100%">
    <div class="jigui-info-panel" v-if="panelData">
      <a-table rowKey="name" size="small" :showHeader="false" :pagination="false" :scroll="{ y: 150 }"
        :customRow="tableCustomRow" :columns="panelColumns" :data-source="panelData">
      </a-table>
    </div>
  </div>
</template>
<script>
  import * as THREE from 'three'
  import {
    Detector
  } from './threeUtils/Detector'
  import {
    OrbitControls
  } from 'three/examples/jsm/controls/OrbitControls'
  let renderer, controls, light
  import {
    createMixins
  } from './threeUtils/createMixins'
  import {
    jiguiMixins
  } from './threeUtils/jiguiMixins'
  import {
    clickMixins
  } from './threeUtils/clickMixins'
  import {
    serverMixins
  } from './threeUtils/serverMixins'
  import {
    httpAction,
    getAction,
    postAction
  } from '@/api/manage'
  import {
    getFileAccessHttpUrl
  } from '@/api/manage'
  export default {
    mixins: [createMixins, jiguiMixins, serverMixins, clickMixins],
    props: {
      threeJson: {
        type: String,
        default: '',
      },
      roomId: {
        type: String,
        default: '',
        required: true,
      },
      modelsArr: {
        type: Array,
        default: () => [],
        required: true,
      },
    },
    data() {
      return {
        boxH: 0,
        boxW: 0,
        serverArr: [],
        panelColumns: [{
            title: '名称',
            dataIndex: 'name',
            key: 'name',
            scopedSlots: {
              customRender: 'name'
            },
          },
          {
            title: '值',
            dataIndex: 'value',
            key: 'value',
            // width: 80,
          },
        ],
        panelData: null,
        clickState: false,
        i: 0,
        ani: null,
      }
    },
    created() {},
    mounted() {
      this.init()
    },
    beforeDestroy() {
      window.removeEventListener('click', this.onMouseClick)
      window.removeEventListener('dblclick', this.onMouseDblclick)
      window.removeEventListener('resize', this.onWindowResize)
    },
    methods: {
      async init() {
        await this.getAlarmLevelData()
        let rect = this.$refs.threeBox.getBoundingClientRect()
        this.boxH = rect.height
        this.boxW = rect.width
        this.resetScene()
        this.importScene()
        this.initCamera()
        this.initRenderer()
        this.initControls()
        this.initLight()
        this.animate()
        this.setJiguitServer()
        window.addEventListener('click', this.onMouseClick)
        window.addEventListener('dblclick', this.onMouseDblclick)
        window.addEventListener('resize', this.onWindowResize)
      },
      // 机柜信息 有告警的设备改变设备颜色
      tableCustomRow(record) {
        let color = '#000'
        return {
          style: {
            color: color,
          },
          class: 'notHover',
        }
      },
      // 同步机柜设备信息
      async setJiguitServer() {
        let devicesIds = []
        let devicesCodes = []
        let ddtem = {
          deviceId: '1524708935221895170',
          isEnable: 'true',
          status: 'down',
        }
        let param = {
          roomId: this.roomId,
          viewFlag: 3,
        }
        let promiseArr = []
        // 获取所有机柜信息
        await getAction('/topo/room/cabinet', param).then((res) => {
          if (res.code === 200) {
            res.result.forEach((cab) => {
              // 获取每个机柜的信息
              promiseArr.push(getAction('/topo/cabinet/info', {
                id: cab.id
              }))
            })
          }
        })
        await Promise.all(promiseArr).then((allRes) => {
          this.serverArr = allRes.map((el) => el.result)
        })
        scene.children.forEach((el) => {
          if (el.name === 'yq_group_jigui') {
            this.createJGWarnSign(el)
            let infoData = [{
              name: '设备个数',
              value: 0,
            }, ]
            let server = this.serverArr.find((server) => server.id === el.userData.id)
            if (server) {
              infoData.push({
                name: '机柜高度',
                value: `${server.layers}单元`,
              })
              if (server.devices.length > 0) {
                infoData[0].value = server.devices.length
                server.devices.forEach((dev) => {
                  if (dev.alarmStatus === 1) {
                    devicesIds.push(dev.id)
                    devicesCodes.push(dev.code)
                  }
                  let tem = {
                    name: `${dev.name}(IP)`,
                    value: dev.ip || '',
                    deviceId: dev.id,
                    deviceCode: dev.code,
                  }
                  infoData.push(tem)
                  this.createResource(dev, el)
                })
              }
            }
            el.userData.infoData = infoData
          }
        })
        this.initDeviceStatus(devicesCodes)
      },
      // 初始化设备的告警状态
      initDeviceStatus(devicesCodes) {
        // 清空vuex的设备在线离线数据
        this.$store.commit('CLEAR_STATUS_LIST')
        // 清空vuex的设备告警状态
        this.$store.commit('CLEAR_ALARM_LIST')
        if (devicesCodes.length <= 0) return
        postAction('/topo/device/initStatus', {
          deviceCode: devicesCodes.join(',')
        }).then((res) => {
          if (res.success) {
            if (res.result && res.result.length > 0) {
              res.result.forEach((el) => {
                const maxAlarm = el.alarmInfo.reduce((prev, curr, i, a) =>
                  curr.alarmLevel > prev.alarmLevel ? curr : prev
                )
                el.level = maxAlarm.alarmLevel
                el.templateName = el.alarmName
                el.deviceId = el.id
              })
              this.alarmInfos = res.result
              this.setJiguiLogo(res.result)
            }
          }
        })
      },
      removeok() {},
      resetScene() {
        scene = new THREE.Scene()
        let config = this.modelsArr.find((el) => el.modelCode === 'room_bg_config')
        if (config && Object.prototype.toString.call(config.userData) === "[object Object]") {
          let configData = config.userData
          if (configData.bgType === 'color') {
            // scene.background = new THREE.Color(configData.bgColor)
          } else if (configData.bgType === 'pic' && configData.bgPic) {
            let url = getFileAccessHttpUrl(configData.bgPic)
            scene.background = new THREE.TextureLoader().load(url)
          }
        }
        this.$store.commit('threejs/changeEditorStatus', false)
      },
      initCamera() {
        camera = new THREE.PerspectiveCamera(45, this.boxW / this.boxH, 1, 100000) //45
        camera.position.set(0, 1000, 1000)
        camera.lookAt(new THREE.Vector3(0, 0, 0))
      },
      initRenderer() {
        if (Detector.webgl) {
          renderer = new THREE.WebGLRenderer({
            antialias: true,
            alpha: true
          })
        } else {
          renderer = new THREE.CanvasRenderer()
        }
        let container = document.getElementById('threeScanBox')
        renderer.setSize(this.boxW, this.boxH)
        // renderer.setClearColor(0x4682b4, 0.6)
        //  renderer.setClearAlpha(0)
        container.appendChild(renderer.domElement)
      },

      initControls() {
        controls = new OrbitControls(camera, renderer.domElement)
        controls.enableDamping = true
        controls.dampingFactor = 0.5
        // 视角最小距离
        controls.minDistance = 100
        // 视角最远距离
        controls.maxDistance = 5000
        // 最大角度
        controls.maxPolarAngle = Math.PI / 2.2
      },
      initLight() {
        var directionalLight = new THREE.DirectionalLight(0xffffff, 0.3) //模拟远处类似太阳的光源
        directionalLight.color.setHSL(0.1, 1, 0.95)
        directionalLight.position.set(0, 200, 0).normalize()
        scene.add(directionalLight)

        var ambient = new THREE.AmbientLight(0xffffff, 1) //AmbientLight,影响整个场景的光源
        ambient.position.set(0, 0, 0)
        scene.add(ambient)
      },
      animate() {
        requestAnimationFrame(this.animate)
        renderer.render(scene, camera)
        this.update()
      },
      update() {
        controls.update()
      },
      importScene() {
        this.initModels(this.modelsArr)
      },
      onWindowResize() {
        if (this.$refs.threeBox) {
          let rect = this.$refs.threeBox.getBoundingClientRect()
          this.boxH = rect.height
          this.boxW = rect.width
        }
        camera.aspect = this.boxW / this.boxH
        camera.updateProjectionMatrix()
        renderer.setSize(this.boxW, this.boxH)
      },
    },
  }
</script>
<style lang="less" scoped>
  #threeScanBox {
    position: relative;

    .jigui-info-panel {
      width: 360px;
      max-height: 190px;
      background-color: rgba(0, 0, 0, 0.5);
      position: absolute;
      top: 20px;
      left: 20px;
      padding: 12px;
      border-radius: 8px;
    }
  }

  ::v-deep .ant-table-tbody {
    tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) {
      td {
        background: transparent;
      }
    }
  }

  ::v-deep .ant-table-tbody {
    tr:hover {
      td {
        td {
          background: transparent;
        }
      }
    }
  }
</style>