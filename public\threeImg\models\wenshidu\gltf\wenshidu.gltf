{"asset": {"version": "2.0", "generator": "babylon.js glTF exporter for 3dsmax 2019 v20240312.5"}, "scene": 0, "scenes": [{"nodes": [0]}], "nodes": [{"mesh": 0, "rotation": [0.0, -0.7071068, 0.0, 0.7071067], "scale": [1000.0, 1000.0, 1000.0], "name": "C<PERSON>"}], "meshes": [{"primitives": [{"attributes": {"POSITION": 1, "NORMAL": 2, "TEXCOORD_0": 3}, "indices": 0, "material": 0}], "name": "C<PERSON>"}], "accessors": [{"bufferView": 0, "componentType": 5123, "count": 468, "type": "SCALAR", "name": "accessorIndices"}, {"bufferView": 1, "componentType": 5126, "count": 188, "max": [7.14207954e-06, 5.0641378e-05, 2.49835484e-05], "min": [-7.752898e-06, 7.485886e-09, -2.49835484e-05], "type": "VEC3", "name": "accessorPositions"}, {"bufferView": 1, "byteOffset": 2256, "componentType": 5126, "count": 188, "type": "VEC3", "name": "accessorNormals"}, {"bufferView": 2, "componentType": 5126, "count": 188, "type": "VEC2", "name": "accessorUVs"}], "bufferViews": [{"buffer": 0, "byteLength": 936, "name": "bufferViewScalar"}, {"buffer": 0, "byteOffset": 936, "byteLength": 4512, "byteStride": 12, "name": "bufferViewFloatVec3"}, {"buffer": 0, "byteOffset": 5448, "byteLength": 1504, "byteStride": 8, "name": "bufferViewFloatVec2"}], "buffers": [{"uri": "wenshidu.bin", "byteLength": 6952}], "materials": [{"pbrMetallicRoughness": {"baseColorTexture": {"index": 0}, "metallicFactor": 0.0, "roughnessFactor": 0.450053632}, "name": "Material #221"}], "textures": [{"sampler": 0, "source": 0, "name": "DefaultMaterial_Base_Color.png"}], "images": [{"uri": "DefaultMaterial_Base_Color.png"}], "samplers": [{"magFilter": 9729, "minFilter": 9987}]}