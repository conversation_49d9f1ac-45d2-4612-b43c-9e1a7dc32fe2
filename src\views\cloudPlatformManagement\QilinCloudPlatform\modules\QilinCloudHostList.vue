<template>
  <div class='table-operator table-operator-style scroll'>
    <!-- table区域-begin -->
    <a-table
      style='margin-right: 1px'
      ref='table'
      bordered
      :rowKey='(record,index)=>{return index}'
      :loading='loading'
      :columns='columns'
      :dataSource='dataSource'
      :pagination='ipagination'
      :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
      @change='handleTableChange'>
      <template slot='tooltip' slot-scope='text'>
        <a-tooltip placement='topLeft' :title='text' trigger='hover'>
          <div class='tooltip'>
            {{ text }}
          </div>
        </a-tooltip>
      </template>
      <!-- 字符串超长截取省略号显示-->
      <span slot='templateContent' slot-scope='text'>
          <j-ellipsis :value='text' :length='25' />
        </span>

      <span slot='action' slot-scope='text, record' class='caozuo'>
          <a @click="submitForm(record, 'PowerOn')">开机</a>
          <a-divider type='vertical' />
          <a @click="submitForm(record, 'PowerOff')">关机</a>
        <!-- <a-divider type="vertical" />
        <a @click="submitForm(record, 'ResetVM')">重启</a> -->
        </span>
    </a-table>
  </div>
</template>

<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import JEllipsis from '@comp/jeecg/JEllipsis.vue'
import { getAction, postAction } from '@api/manage'
import {tableOperationColumnVisibility} from '@/mixins/tableOperationColumnVisibility'
export default {
  name: 'CloudHostList',
  mixins: [JeecgListMixin,tableOperationColumnVisibility],
  components: {
    JEllipsis
  },
  data() {
    return {
      // 表头
      columns: [
        {
          title: 'IP地址',
          dataIndex: 'ip'
        },
        {
          title: '设备名称',
          dataIndex: 'uname'
        },
        {
          title: '设备状态',
          dataIndex: 'status'
        },
        {
          title: 'CPU使用率（%）',
          dataIndex: 'cpuRate',
          customCell: () => {
            let cellStyle = 'text-align: right'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '内存使用率（%）',
          dataIndex: 'memRate',
          customCell: () => {
            let cellStyle = 'text-align: right'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '磁盘使用率（%）',
          dataIndex: 'diskRate',
          customCell: () => {
            let cellStyle = 'text-align: right'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '磁盘读速率（B/s）',
          dataIndex: 'diskRead',
          customCell: () => {
            let cellStyle = 'text-align:right'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '磁盘写速率（B/s）',
          dataIndex: 'diskWrite',
          customCell: () => {
            let cellStyle = 'text-align: right'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '网络入速率（B/s）',
          dataIndex: 'netIn',
          customCell: () => {
            let cellStyle = 'text-align: right'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '网络出速率（B/s）',
          dataIndex: 'netOut',
          customCell: () => {
            let cellStyle = 'text-align: right'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '操作',
          dataIndex: 'action',
          fixed: 'right',
          width: 100,
          align: 'center',
          scopedSlots: {
            customRender: 'action'
          }
        }
      ],
      url: {
        list: '/alarm/alarmTemplate/cloudByDevId',
      },
      disableMixinCreated: true,
      deviceInfo: {}
    }
  },
  watch: {
    deviceInfo: function(newVal, oldVal) {
      this.init(newVal)
    }
  },
  created() {
    this.setTabelOperationCol('operationColumnVisibility','cloudHostOperVis')
  },
  methods: {
    show(record) {
      this.deviceInfo = record
    },
    init(record) {
      this.queryParam.deviceId = record.id
      this.loadData()
    },
    handleDetailPage: function(record) {
      this.$parent.pButton2(1, record)
    },
    refreshVmware() {
      getAction('/alarm/alarmTemplate/refreshVmware', {
        ip: this.data.ip,
        username: 'root',
        password: 'vms1!123',
        vmwareName: 'ZABBIX-41'
      }).then((res) => {
        if (res.success) {
        }
      })
    },
    submitForm(values, data) {
      var unixtime = new Date().getTime()
      const that = this

      that.confirmLoading = true
      let httpurl = '/device/deviceInfo/execute'

      let formData = {}
      // formData.productId = this.record.productId
      formData.deviceId = this.queryParam.deviceId
      // formData.commandId = this.record.id
      formData.uId = values.uId
      formData.methodName = data
      formData.vmwareName = values.name
      formData.transferProtocol = 'QiLinCloud'
      postAction(httpurl, formData)
        .then((res) => {
          if (res.success) {
            that.$message.success(res.result)
            // if (data == 'PowerOnVM') {
            //   setTimeout(() => {
            //     this.refreshVmware()
            //   }, 5000)
            // } else if (data == 'PowerOffVM') {
            //   this.refreshVmware()
            // }
          } else {
            that.$message.warning(res.result)
          }
        })

        .finally(() => {
          that.confirmLoading = false
        })
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';

.scroll {
  height: 100%;
  overflow: hidden;
  overflow-y: auto;
}
</style>