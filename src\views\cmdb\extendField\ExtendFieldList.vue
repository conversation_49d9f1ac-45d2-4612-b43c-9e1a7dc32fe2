<template>
  <a-card :bordered="false" :bodyStyle="{ padding:'0'}">
    <!-- 操作按钮区域 -->
    <div class="table-operator table-operator-style">
      <a-button @click="handleAdd">新增</a-button>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel" style='text-align: center'>删除</a-menu-item>
        </a-menu>
        <a-button> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <a-table
        ref="table"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        @change="handleTableChange"
      >
        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 14px">无图片</span>
          <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width: 80px; font-size: 14px" />
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 14px">无文件</span>
          <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleDetail(record)" style="color: #409eff">查看</a>
          <a-divider type="vertical" />
          <a @click="handleEdit(record)" style="color: #409eff">编辑</a>
          <a-divider type="vertical" />
          <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
            <a style="color: #409eff">删除</a>
          </a-popconfirm>
        </span>
      </a-table>
    </div>
    <extend-field-modal ref="modalForm" @ok="modalFormOk"></extend-field-modal>
  </a-card>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import ExtendFieldModal from './modules/ExtendFieldModal'
import JSuperQuery from '@/components/jeecg/JSuperQuery.vue'
import { httpAction, getAction, deleteAction } from '@/api/manage'

export default {
  name: 'ExtendFieldList',
  mixins: [JeecgListMixin, mixinDevice],
  components: {
    ExtendFieldModal,
    JSuperQuery,
    getAction,
  },
  data() {
    return {
      description: '附加表单的字段管理页面',
      // 表头
      columns: [
        {
          title: '序号',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function (t, r, index) {
            return parseInt(index) + 1
          },
        },
        {
          title: '字段标识',
          dataIndex: 'code',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 50px;max-width:300px'
            return { style: cellStyle }
          },
        },
        {
          title: '字段名称',
          dataIndex: 'name',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 50px;max-width:300px'
            return { style: cellStyle }
          },
        },
        {
          title: '字段类型',
          dataIndex: 'type',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 50px;max-width:300px'
            return { style: cellStyle }
          },
        },
        {
          title: '字段描述',
          dataIndex: 'description',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 50px;max-width:400px'
            return { style: cellStyle }
          },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 147,
          scopedSlots: { customRender: 'action' },
        },
      ],
      url: {
        list: '/extendField/extendField/listAll',
        delete: '/extendField/extendField/delete',
        deleteBatch: '/extendField/extendField/deleteBatch',
        exportXlsUrl: '/extendField/extendField/exportXls',
        importExcelUrl: 'extendField/extendField/importExcel',
      },
      dictOptions: {},
      superFieldList: [],
      formId: '',
      disableMixinCreated:true
    }
  },
  created() {
    this.getSuperFieldList()
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
  },
  methods: {
    initDictConfig() {},
    edit(record) {
      //  let fromId = record.id;
      this.queryParam.fromId = this.formId = record.id
      this.loadData()
      //  getAction("/extendField/extendField/listAll", {fromId: fromId }).then(res => {
      // this.name = res.result.name,
      // this.code = res.result.code
      // })
    },
    handleAdd: function () {
      this.$refs.modalForm.add(this.formId)
      this.$refs.modalForm.title = '新增'
      this.$refs.modalForm.disableSubmit = false
    },
    getSuperFieldList() {
      let fieldList = []
      fieldList.push({ type: 'string', value: 'code', text: '字段标识', dictCode: '' })
      fieldList.push({ type: 'string', value: 'name', text: '字段名称', dictCode: '' })
      fieldList.push({ type: 'string', value: 'type', text: '字段类型', dictCode: '' })
      fieldList.push({ type: 'string', value: 'description', text: '字段描述', dictCode: '' })
      this.superFieldList = fieldList
    },
  },
}
</script>
<style lang="less" scoped>
@import '~@assets/less/common.less';
</style>
