import { ACCESS_TOKEN } from '@/store/mutation-types'
import store from '@/store/'
import Vue from 'vue'

export const WebsocketMixin = {
  data() {
    return {
      heartInterval: null,
      isOpen: false,
      heartCheckNum: 0,
      heartSendTime: null,
      heartEndTime: null,
      websock: null,
    }
  },
  mounted() {
    this.initWebSocket();
  },
  destroyed: function () {
    // 离开页面生命周期函数
    this.clearHeartBeat()
    // 前端关闭websocket当前连接
    this.websock.close()
    window.WEBSOCKET = null
  },
  methods: {
    initWebSocket: function () {
      // WebSocket与普通的请求所用协议有所不同，ws等同于http，wss等同于https
      var userId = this.$store.getters.userInfo.id
      var url =
        window._CONFIG['domianURL'].replace('https://', 'wss://').replace('http://', 'ws://') + '/websocket/' + userId
      if (this.websock) {
        this.websock = null
      }
      this.websock = new WebSocket(url)
      window.WEBSOCKET = this.websock
      this.websock.onopen = this.websocketOnopen
      this.websock.onerror = this.websocketOnerror
      this.websock.onmessage = this.websocketOnmessageMixin
      this.websock.onclose = this.websocketOnclose
    },
    websocketOnopen: function () {
      this.$store.commit('CHANGE_WEBSOCKET_STATUS', this.websock.readyState)
      // 向后台发送连接成功消息
      // this.websock.send('websocket 连接成功！')
      // 开启心跳检测
      this.isOpen = false
      this.heartCheckNum = 1
      this.websocketSend('HeartBeat')
      this.createHeartBeat()
    },
    websocketOnerror: function (e) {
      console.log('WebsocketMixin ws连接报错 === ', e)
      this.reconnect()
    },
    websocketOnclose: function (e) {
      // console.log('WebsocketMixin ws连接关闭 === ', e)
      this.$store.commit('CHANGE_WEBSOCKET_STATUS', this.websock.readyState)

    },
    websocketOnmessageMixin(e) {
      // 心跳检测 后台返回 "HeartBeat" 证明连接正常 不进行任何业务逻辑处理
      //长时间不返回证明连接有可能端开 需要重新建立
      // 只要有推送信息 isOpen 设为 true  真没websocket 处于连接状态
      this.isOpen = true

      if (e.data === 'HeartBeat') {
        return
      }
      var data = eval('(' + e.data + ')') //解析对象
      if (data.messageType === 'alarmStatus') {
        let alarmData = data.data;
        //设备在告警状态推送
        // console.log("mixin接收告警状态到推送消息 === ", e.data)
        let alarmIdx = store.getters.deviceAlarms.findIndex(el => el.deviceCode === alarmData.deviceCode);

        if (alarmIdx !== -1) {
          // 更新已有告警设备的告警信息
          let alarmItem = store.getters.deviceAlarms[alarmIdx];
          let alarmInfo = alarmItem.alarmInfo;
          let idx = alarmInfo.findIndex(el => el.alarmName === alarmData.temName)
          let tem = {
            alarmLevel: alarmData.alarmLevel,
            alarmName: alarmData.temName,
            alarmDisplay: JSON.parse(alarmData.alarmDisplay)
          }
          // console.log("设备有该告警 ==",idx)
          //有相同告警名称的告警跟换 否则添加进去
          if (idx !== -1) {
            alarmInfo.splice(idx, 1, tem)
          } else {
            alarmInfo.push(tem)
          }
          this.$store.dispatch({ type: 'updateAlarmList', index: alarmIdx, item: alarmItem })
        }
        else {
          //添加告警的设备
          let temInfo = {
            deviceCode: data.data.deviceCode,
            alarmInfo: [
              {
                alarmLevel: alarmData.alarmLevel,
                alarmName: alarmData.temName,
                alarmDisplay: JSON.parse(alarmData.alarmDisplay)
              }
            ],
          };
          this.$store.dispatch('addAlarmList', temInfo)
        }
      }
      else if (data.messageType === 'alarmClear') {
        // 清除拓扑图告警
        let clearData = data.data;
        this.$store.dispatch('delAlarmList', clearData)
        if (data.data.prompt) {
          let obj = {
            showTypeIcon: true,
            typeName: 'info',
            title: "告警解除",
            content: data.data.msgContent,
            duration: data.data.duration,
            position: data.data.position,
            showMore: 'none'
          }
          this.$yq_notification(obj)
        }
      }
      else if (data.messageType === 'statusChange') {
        //设备在线离线状态改变推送
        // console.log("mixin接收到在线离线推送消息 === ",e.data)
        // console.log("websocketMixin状态管理的数据 === ",this.$store.getters.deviceStatus)
        const info = store.getters.deviceStatus.find((ele) => {
          return ele.deviceCode === data.data.deviceCode
        })
        if (info) {
          info['status'] = data.data.status;
          info['isEnable'] = data.data.isEnable;
        } else {
          store.dispatch('addStatusDataList', data.data)
        }
      } else if (data.messageType === 'portStatus') {
        //设备端口状态推送
        /* 
        *暂时不考虑端口状态
         */
        // const info = store.getters.deviceStatus.find((ele) => {
        //   return ele.deviceCode === data.data.deviceCode
        // })
        // if (info) {
        //   info['portStatusList'] = data.data.portStatusList
        // } else {
        //   store.dispatch('addStatusDataList', data.data)
        // }
      }
    },
    websocketSend(text) {
      // 数据发送
      try {
        this.websock.send(text);
      } catch (err) {
      }
    },
    reconnect() {
      this.websock.close()
      this.clearHeartBeat()
      if (this.lockReconnect) return
      this.lockReconnect = true
      //没连接上会一直重连，设置延迟避免请求过多
      setTimeout(() => {
        console.info('websocketMixin 尝试重连...')
        this.initWebSocket()
        this.lockReconnect = false
      }, 5000)
    },
    createHeartBeat() {
      if (this.heartInterval) {
        this.clearHeartBeat()
      }
      this.heartInterval = setInterval(() => {
        //这里发送一个心跳，后端收到后，返回一个心跳消息，
        //onmessage拿到返回的心跳就说明连接正常
        //后台有消息推送不需要向后台发送心跳消息
        if (this.isOpen) {
          this.isOpen = false
          this.heartCheckNum = 0
          console.log('WebsocketMixin websocket 连接正常', this.heartCheckNum)
        } else {
          //范围时间内无返回消息向后台发送心跳信息
          if (this.heartCheckNum < 5) {
            this.heartCheckNum += 1
            this.websocketSend('HeartBeat')
          } else {
            //心跳检测超过五次重新连接
            console.log('心跳超时需要重新重新连接')
            this.reconnect()
          }
        }
      }, 1000 * 20)
    },
    clearHeartBeat() {
      clearInterval(this.heartInterval)
      this.heartInterval = null
    },
  }

}