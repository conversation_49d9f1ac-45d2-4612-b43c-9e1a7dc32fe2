<template>
  <div>
    <a-input-search v-model="userNames" placeholder="请先选择用户" readOnly unselectable="on" @search="onSearchDepUser">
      <a-button slot="enterButton" :disabled="disabled">选择用户</a-button>
    </a-input-search>
    <j-select-user-by-dep-modal
      ref="selectModal"
      :modal-width="modalWidth"
      :centered="true"
      :multi="multi"
      @ok="selectOK"
      :user-ids="value"
      @initComp="initComp"
    />
  </div>
</template>

<script>
import JSelectUserByDepModal from './modal/JSelectUserByDepModal'

export default {
  name: 'JSelectUserByDepReturnId',
  components: { JSelectUserByDepModal },
  props: {
    modalWidth: {
      type: String,
      default: '1300px',
      required: false,
    },
    pUserNames:{
      type: String,
      required: false,
    },
    value: {
      type: String,
      required: false,
    },
    disabled: {
      type: Boolean,
      required: false,
      default: false,
    },
    multi: {
      type: Boolean,
      default: true,
      required: false,
    },
  },
  data() {
    return {
      userIds: '',
      userNames: '',
    }
  },
  mounted() {
    this.userIds = this.value
    this.userNames=this.pUserNames
  },
  watch: {
    value(val) {
      this.userIds = val
    },
  },
  model: {
    prop: 'value',
    event: 'change',
  },
  methods: {
    initComp(userNames) {
      this.userNames = userNames
    },
    onSearchDepUser() {
      this.userIds = this.value
      this.userNames=this.pUserNames
      this.$refs.selectModal.showModal()
    },
    selectOK(rows, idstr) {
      // if (!rows) {
      if (rows.length<=0) {
        this.userNames = ''
        this.userIds = ''
      } else {
        let temp = ''
        let temp1 = ''
        for (let item of rows) {
          temp += ',' + item.realname
          temp1 += ',' + item.id
        }
        this.userNames = temp.substring(1)
        this.userIds = temp1.substring(1)
      }
      this.$emit('change', this.userIds,this.userNames)
    },
  },
}
</script>

<style scoped lang="less">
::v-deep .ant-modal-body {
  padding: 24px 48px 24px 48px;
}
::v-deep .ant-modal {
  padding: 24px;
}
.j-modal-box.fullscreen {
  margin: 0 !important;
  max-width: 100vw !important;
  ::v-deep .ant-modal {
    max-width: 100vw !important;
    margin: 0;
  }
}
@media (max-width: 1312px) {
  ::v-deep .ant-modal {
    top: 0px;
    width: 1200px !important;
    max-width: 1200px !important;
    margin: 0 !important;
  }
  .ant-modal {
    top: 0px;
    width: 1200px !important;
    max-width: 1200px !important;
    margin: 0 !important;
  }
}
</style>