/**能见度：公开、私有*/
export const visibility=[
  { label: '公开', value: '0',key:'private_0', checked: true },
  { label: '私有', value: '1',key:'private_1' , checked: false}
]
/**知识类型*/
export const knowledgeType=[
  { label: '文本知识', value: 0,key:'knowledgeType_0', checked: true },
  { label: '文档知识', value: 1,key:'knowledgeType_1' , checked: false}
]
/**审批状态：待修改、 待发布、 待审批、 已发布*/
export const approvalState=[
  { label: '待修改', value: '待修改' },
  { label: '待发布', value: '待发布' },
  { label: '待审批', value: '待审批' },
  { label: '已发布', value: '已发布' },
  { label: '已保存', value: '已保存' }
]
/*获取附件存储位置相对路径。及附件名称*/
export function  getFileNamesAndFilePaths(info) {
  let obj = {
    fileNames: '',
    filePaths: ''
  }
  if (!info || typeof info !== 'string') {
    return obj;
  }

  try {
    //新的格式：json串，含有相对路径和中文名
    const json = JSON.parse(info);
    if (typeof json === 'object' && json !== null) {
      obj.fileNames = json.originalFilename
      obj.filePaths = json.fileUrl
    } else {
      obj.fileNames = ''
      obj.filePaths = ''
    }
  } catch (e) {
    //兼顾以前：只有相对路径的字符串
    obj.fileNames = getFileNames(info)
    obj.filePaths = info
  }
  //console.log("回显，附件处理obj===",obj)
  return obj
}
/*获取附件名称-所有附件名称*/
function getFileNames(paths) {
  let fileNames = ''
  if (paths && paths.length > 0) {
    let pathsArr = paths.split(',')
    for (let i = 0; i < pathsArr.length; i++) {
      let path = pathsArr[i]
      if (path.lastIndexOf("\\") >= 0) {
        let reg = new RegExp("\\\\", "g");
        path = path.replace(reg, "/");
      }
      let name = path.substring(path.lastIndexOf("/") + 1);
      fileNames += i != pathsArr.length - 1 ? name + ',' : name
    }
  }
  return fileNames
}