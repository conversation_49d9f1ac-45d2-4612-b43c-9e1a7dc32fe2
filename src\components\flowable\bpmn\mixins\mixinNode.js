export default{
    watch: {
        'formData.initiator': function (val) {
            this.updateProperty('initiator', val, true)
        },
        'formData.category': function (val) {
            this.updateProperty('category', val, true)
        },
        'formData.formKey': function (val) {
            this.updateProperty('formKey', val, true)
        },
        'formData.buttons': function (val) {
            this.updateProperty('buttons', val, true)
        },
        'formData.async': function (val) {
            this.updateProperty('async', val, true)
        },
        'formData.conditionExpression': function (val) {
            if (val) {
                const newCondition = this.modeler.get('moddle').create('bpmn:FormalExpression', {body: `<![CDATA[${val}]]>`})
                this.updateProperties({conditionExpression: newCondition})
            } else {
                this.updateProperties({conditionExpression: null})
            }
        },
        'formData.skipExpression': function (val) {
            this.updateProperty('skipExpression', val, true)
        },
        'formData.assignee': function (val) {
            this.updateProperty('assignee', val, true)
        },
        'formData.candidateUsers': function (val) {
            this.updateProperty('candidateUsers', val, true)
        },
        'formData.candidateGroups': function (val) {
            this.updateProperty('candidateGroups', val, true)
        },
        'formData.dueDate': function (val) {
            this.updateProperty('dueDate', val, true)
        },
        'formData.priority': function (val) {
            this.updateProperty('priority', val, true)
        },
        'formData.isForCompensation': function (val) {
            this.updateProperty('isForCompensation', val)
        },
        'formData.triggerable': function (val) {
            this.updateProperty('triggerable', val, true)
        },
        'formData.class': function (val) {
            this.updateProperty('class', val, true)
        },
        'formData.autoStoreVariables': function (val) {
            this.updateProperty('autoStoreVariables', val, true)
        },
        'formData.exclude': function (val) {
            this.updateProperty('exclude', val, true)
        },
        'formData.ruleVariablesInput': function (val) {
            this.updateProperty('ruleVariablesInput', val, true)
        },
        'formData.rules': function (val) {
            this.updateProperty('rules', val, true)
        },
        'formData.resultVariable': function (val) {
            this.updateProperty('resultVariable', val, true)
        }
    },
}