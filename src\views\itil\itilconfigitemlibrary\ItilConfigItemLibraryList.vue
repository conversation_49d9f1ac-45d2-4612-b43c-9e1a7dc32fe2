<template>
  <a-row :gutter="10" style="height: 100%; display: flex">
    <a-col :xl="4" :lg="4" :md="6" :sm="6" :xs="8" style="height: 100%; overflow: hidden">
      <a-card :bordered="false" style="height: 100%; overflow-x: auto">
        <!-- 按钮操作区域 -->
        <div style="background: #fff; height: 100%">
          <a-input-search
            @search="onSearch"
            :allowClear="true"
            autocomplete="off"
            style="width: 100%; margin-bottom: 12px"
            placeholder="请输入类型名称"
          />
          <!-- 树-->
          <a-col :xs="24">
            <template>
              <a-dropdown :trigger="[this.dropTrigger]" @visibleChange="dropStatus">
                <span style="user-select: none">
                  <a-tree
                    multiple
                    @select="onSelect"
                    @check="onCheck"
                    @rightClick="rightHandle"
                    :selectedKeys="selectedKeys"
                    :checkedKeys="checkedKeys"
                    :treeData="departTree"
                    :checkStrictly="checkStrictly"
                    :expandedKeys="iExpandedKeys"
                    :autoExpandParent="autoExpandParent"
                    @expand="onExpand"
                  />
                </span>
                <!--新增右键点击事件,和增加添加和删除功能-->
                <a-menu slot="overlay">
                  <a-menu-item @click="handleAddType" key="1">添加</a-menu-item>
                  <a-menu-item @click="handleEditType">编辑</a-menu-item>
                  <a-menu-item @click="handleDeleteType" key="2">删除</a-menu-item>
                  <a-menu-item @click="closeDrop" key="3">取消</a-menu-item>
                </a-menu>
              </a-dropdown>
            </template>
          </a-col>
        </div>
      </a-card>
    </a-col>
    <!--右边-->
    <a-col :xl="20" :lg="20" :md="18" :sm="18" :xs="16" style="height: 100%">
      <a-row :gutter="10" style="height: 100%" class="vScroll zxw">
        <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
          <!-- 查询区域 -->
          <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
            <div class="table-page-search-wrapper">
              <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
                <a-row :gutter="24" ref="row">
                  <a-col :span="spanValue">
                    <a-form-item label="名称">
                      <a-input
                        placeholder="请输入"
                        autocomplete="off"
                        :allowClear="true"
                        v-model="queryParam.name"
                      ></a-input>
                    </a-form-item>
                  </a-col>
                  <a-col :span="spanValue">
                    <a-form-item label="状态">
                      <j-dict-select-tag
                        v-model="queryParam.state"
                        placeholder="请选择"
                        dictCode="itil_config_item_type_state"
                        allowClear
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="colBtnsSpan()">
                    <span
                      class="table-page-search-submitButtons"
                      :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                    >
                      <a-button type="primary" @click="searchQuery" class="btn-search-style">查询</a-button>
                      <a-button @click="searchReset" style="margin-left: 10px" class="btn-reset-style">重置</a-button>
                      <a v-if="isVisible" class="btn-updown-style" @click="doToggleSearch">
                        {{ toggleSearchStatus ? '收起' : '展开' }}
                        <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                      </a>
                    </span>
                  </a-col>
                </a-row>
              </a-form>
            </div>
            <!-- 查询区域-END -->
          </a-card>
          <a-card :bordered="false" style="flex: auto" class="core">
            <!-- 操作按钮区域 -->
            <div class="table-operator tableBottom">
              <a-button @click="handleAdd">新增</a-button>
              <a-button @click="handleExportXls('配置项库')">导出</a-button>
              <a-dropdown v-if="selectedRowKeys.length > 0">
                <a-menu slot="overlay" style='text-align: center'>
                  <a-menu-item key="1" @click="batchDel">删除</a-menu-item>
                </a-menu>
                <a-button> 批量操作 <a-icon type="down" /></a-button>
              </a-dropdown>
            </div>

            <!-- table区域-begin -->
            <div>
              <a-table
                ref="table"
                bordered
                rowKey="id"
                :columns="columns"
                :dataSource="dataSource"
                :pagination="ipagination"
                :loading="loading"
                :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
                class="j-table-force-nowrap"
                @change="handleTableChange"
              >
                <template slot="htmlSlot" slot-scope="text">
                  <div v-html="text"></div>
                </template>
                <template slot="imgSlot" slot-scope="text">
                  <span v-if="!text" style="font-size: 14px">无图片</span>
                  <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width: 80px; font-size: 14px" />
                </template>
                <template slot="fileSlot" slot-scope="text">
                  <span v-if="!text" style="font-size: 14px">无文件</span>
                  <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="uploadFile(text)">
                    下载
                  </a-button>
                </template>
                <span slot="action" slot-scope="text, record" class="caozuo">
                  <a @click="handleEdit(record)">编辑</a>
                  <a-divider type="vertical" />
                  <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                    <a>删除</a>
                  </a-popconfirm>
                </span>
                <template slot="tooltip" slot-scope="text">
                  <a-tooltip placement="topLeft" :title="text" trigger="hover">
                    <div class="tooltip">
                      {{ text }}
                    </div>
                  </a-tooltip>
                </template>
              </a-table>
            </div>

            <itilConfigItemLibrary-modal ref="modalForm" @ok="modalFormOk"></itilConfigItemLibrary-modal>
            <itilConfigItemTypeTree-modal ref="modalTypeForm" @ok="refresh"></itilConfigItemTypeTree-modal>
          </a-card>
        </a-col>
      </a-row>
    </a-col>
  </a-row>
</template>

<script>
// import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import ItilConfigItemTypeTreeModal from './modules/ItilConfigItemTypeTreeModal'
import ItilConfigItemLibraryModal from './modules/ItilConfigItemLibraryModal'
import { queryItilconfigItemtypeTreeList, deleteByConfigItemtypeId, searchByConfigItemtype } from '@/api/api'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'

export default {
  name: 'ItilConfigItemLibraryList',
  mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
  components: {
    ItilConfigItemLibraryModal,
    ItilConfigItemTypeTreeModal,
  },
  data() {
    return {
      formItemLayout: {
        labelCol: {
          style: 'width:70px',
        },
        wrapperCol: {
          style: 'width:calc(100% - 70px)'
        }
      },
      description: '配置项库管理页面',
      //树
      departTree: [],
      treeData: [],
      dropTrigger: '',
      selectedKeys: [],
      checkedKeys: [],
      checkStrictly: true,
      iExpandedKeys: [],
      autoExpandParent: true,
      currFlowId: '',
      currFlowName: '',
      rightClickSelectedBean: {},
      //查询
      queryParam: {
        configType: '',
      },
      // 表头
      columns: [
        {
          title: '编号',
          dataIndex: 'code',
        },
        {
          title: '名称',
          dataIndex: 'name',
          scopedSlots: { customRender: 'tooltip' },
        },
        {
          title: '类型',
          dataIndex: 'configType_dictText',
        },
        {
          title: '使用状态',
          dataIndex: 'state_dictText',
        },

        {
          title: '所属人',
          dataIndex: 'owner_dictText',
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
        },
        {
          title: '更新时间',
          dataIndex: 'updateTime',
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 147,
          scopedSlots: { customRender: 'action' },
        },
      ],
      url: {
        list: '/itilconfigitemlibrary/itilConfigItemLibrary/list',
        delete: '/itilconfigitemlibrary/itilConfigItemLibrary/delete',
        deleteBatch: '/itilconfigitemlibrary/itilConfigItemLibrary/deleteBatch',
        exportXlsUrl: '/itilconfigitemlibrary/itilConfigItemLibrary/exportXls',
        importExcelUrl: 'itilconfigitemlibrary/itilConfigItemLibrary/importExcel',
      },
      dictOptions: {},
      uid: this.$route.query.id,
    }
  },
  mounted() {
    this.refresh()
    if (this.uid) {
      this.handleAdd()
    }
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
    //可行性测试，根据文件路径动态加载组件
    LcDict: function () {
      var myComponent = () => import(`@/components/dict/JDictSelectTag`)
      return myComponent
    },
  },
  // created(){
  //   this.refresh();
  // },
  methods: {
    // loadData() {
    //   this.refresh();
    // },
    refresh() {
      this.loading = true
      this.loadTree()
    },
    //查询树
    loadTree() {
      var that = this
      that.treeData = []
      that.departTree = []
      queryItilconfigItemtypeTreeList().then((res) => {
        if (res.success) {
          //部门全选后，再添加部门，选中数量增多
          this.allTreeKeys = []
          for (let i = 0; i < res.result.length; i++) {
            let temp = res.result[i]
            that.treeData.push(temp)
            that.departTree.push(temp)
            that.setThisExpandedKeys(temp)
            that.getAllKeys(temp)
          }
          this.loading = false
        }
      })
    },
    setThisExpandedKeys(node) {
      if (node.children && node.children.length > 0) {
        this.iExpandedKeys.push(node.key)
        for (let a = 0; a < node.children.length; a++) {
          this.setThisExpandedKeys(node.children[a])
        }
      }
    },
    onSearch(value) {
      let that = this
      if (value) {
        searchByConfigItemtype({ typeName: value }).then((res) => {
          if (res.success) {
            that.departTree = []
            for (let i = 0; i < res.result.length; i++) {
              let temp = res.result[i]
              that.departTree.push(temp)
            }
          } else {
            that.$message.warning(res.message)
          }
        })
      } else {
        that.loadTree()
      }
    },
    onExpand(expandedKeys) {
      // if not set autoExpandParent to false, if children expanded, parent can not collapse.
      // or, you can remove all expanded children keys.
      this.iExpandedKeys = expandedKeys
      this.autoExpandParent = false
    },
    // 右键点击下拉框改变事件
    dropStatus(visible) {
      if (visible == false) {
        this.dropTrigger = ''
      }
    },
    // 右键操作方法
    rightHandle(node) {
      this.dropTrigger = 'contextmenu'
      this.rightClickSelectedKey = node.node.eventKey
      this.rightClickSelectedBean = node.node.dataRef
    },
    //选择树的方法
    onSelect(selectedKeys, e) {
      this.hiding = false
      let record = e.node.dataRef
      this.currSelected = Object.assign({}, record)
      this.model = this.currSelected
      this.selectedKeys = [record.key]
      this.model.parentId = record.parentId
      this.setValuesToForm(record)
      // this.$refs.departAuth.show(record.id);
    },
    // 触发onSelect事件时,为部门树右侧的form表单赋值
    setValuesToForm(record) {
      this.queryParam.configType = record.key
      this.loadData()
    },
    //树的添加类型
    handleAddType() {
      this.$refs.modalTypeForm.add(this.rightClickSelectedKey)
      this.$refs.modalTypeForm.title = '新增'
    },
    //树的编辑类型
    handleEditType() {
      this.$refs.modalTypeForm.edit(this.rightClickSelectedBean)
      this.$refs.modalTypeForm.title = '编辑'
    },
    //删除类型
    handleDeleteType() {
      var that = this
      this.$confirm({
        title: '确认删除',
        okText: '是',
        cancelText: '否',
        content: '是否删除此类型以及子节点数据吗?',
        onOk: function () {
          deleteByConfigItemtypeId({ id: that.rightClickSelectedKey }).then((resp) => {
            if (resp.success) {
              //删除成功后，去除已选中中的数据
              that.checkedKeys.splice(
                that.checkedKeys.findIndex((key) => key === that.rightClickSelectedKey),
                1
              )
              that.$message.success(resp.result)
              that.loadTree()
            } else {
              that.$message.warning('删除失败!')
            }
          })
        },
      })
    },
    getCurrSelectedTitle() {
      return !this.currSelected.title ? '' : this.currSelected.title
    },
    onCheck(checkedKeys, info) {
      this.hiding = false
      //this.checkedKeys = checkedKeys.checked
      // <!---- author:os_chengtgen -- date:20190827 --  for:切换父子勾选模式 =======------>
      if (this.checkStrictly) {
        this.checkedKeys = checkedKeys.checked
      } else {
        this.checkedKeys = checkedKeys
      }
      // <!---- author:os_chengtgen -- date:20190827 --  for:切换父子勾选模式 =======------>
    },
    // 右键店家下拉关闭下拉框
    closeDrop() {
      this.dropTrigger = ''
    },
    // <!---- author:os_chengtgen -- date:20190827 --  for:切换父子勾选模式 =======------>
    expandAll() {
      this.iExpandedKeys = this.allTreeKeys
    },
    closeAll() {
      this.iExpandedKeys = []
    },
    checkALL() {
      this.checkStriccheckStrictlytly = false
      this.checkedKeys = this.allTreeKeys
    },
    cancelCheckALL() {
      //this.checkedKeys = this.defaultCheckedKeys
      this.checkedKeys = []
    },
    switchCheckStrictly(v) {
      if (v == 1) {
        this.checkStrictly = false
      } else if (v == 2) {
        this.checkStrictly = true
      }
    },
    getAllKeys(node) {
      this.allTreeKeys.push(node.key)
      if (node.children && node.children.length > 0) {
        for (let a = 0; a < node.children.length; a++) {
          this.getAllKeys(node.children[a])
        }
      }
    },
    // <!---- author:os_chengtgen -- date:20190827 --  for:切换父子勾选模式 =======------>

    initDictConfig() {},
  },
}
</script>
<style lang="less" scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
/*表头样式*/
::v-deep .ant-table-thead > tr > th {
  text-align: center;
  white-space: nowrap;
}

/*内容对齐方式、省略显示*/
::v-deep .ant-table-tbody > tr > td {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;

  &:first-child,
  &:nth-child(2),
  &:nth-child(4),
  &:nth-child(5),
  &:nth-child(6),
  &:nth-child(7),
  &:nth-child(8) {
    text-align: center;
  }

  &:nth-child(3) {
    text-align: left;
  }
}
::v-deep .ant-table-tbody > tr > td {
  &:nth-child(3) {
    min-width: 100px;
    max-width: 200px;
  }
}
</style>
