<template>
  <div style="height:100%">
    <keep-alive exclude='AutoControlScriptDetail'>
      <component style="height:100%" :is="pageName" :data="data" />
    </keep-alive>
  </div>
</template>
<script>
  import AutoControlScriptList from './AutoControlScriptList'
  import AutoControlScriptDetail from './modules/AutoControlScriptDetail'
  import {
    getAction
  } from '@api/manage'
  export default {
    name: "AutoControlScriptMange",
    data() {
      return {
        isActive: 0,
        data: {},
      };
    },
    components: {
      AutoControlScriptList,
      AutoControlScriptDetail
    },
    created() {
      this.pButton1(0);
    },
    //使用计算属性
    computed: {
      pageName() {
        switch (this.isActive) {
          case 0:
            return "AutoControlScriptList";
          default:
            return "AutoControlScriptDetail";
        }
      }
    },
    methods: {
      pButton1(index) {
        this.isActive = index;
      },
      async pButton2(index, item) {
        this.isActive = index;
        this.data = item
      }
    }
  }
</script>