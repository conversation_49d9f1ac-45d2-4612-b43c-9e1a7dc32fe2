<template>
  <div>
    <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0', paddingTop: '24px' }">
      <div class="table-page-search-wrapper">
        <a-form layout="inline" @keyup.enter.native="searchQuery">
          <a-row :gutter="24" ref="row">
            <a-col :span="spanValue">
              <a-form-item label="模板名称">
                <a-input :maxLength="maxLength" placeholder="请输入模板名称"  v-model="queryParam.name" :allowClear='true' autocomplete='off'/>
              </a-form-item>
            </a-col>
            <a-col :span="colBtnsSpan()">
              <span class="table-page-search-submitButtons" :style="{ overflow: 'hidden' } || {}">
                <a-button type="primary" class="btn-search btn-search-style" @click="searchQuery">查询</a-button>
                   <a-button @click="searchReset" class="btn-reset btn-reset-style">重置</a-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="table-operator">
        <a-button @click="addTem">新增</a-button>
      </div>
    </a-card>
    <a-card
      :bordered="false"
      :bodyStyle="{ padding: '0' }"
      style="width: 100%; flex: auto; background-color: rgba(255, 255, 255, 0)"
    >
      <!-- table区域-begin -->
      <div class="topo-list-div">
        <a-row :gutter="16" type="flex" align="middle">
          <a-col v-for="pageTem in dataSource" :key="pageTem.id" v-bind="CardColLayout" style="margin-top: 20px">
            <a-card :title="pageTem.name" :style="{'box-shadow': pageTem.showType==='0'?'blue 0px 0px 8px':`0 3px 7px -1px rgb(0 0 0 / 16%)`}">
              <div
                class="template-pic"
                :style="{ height: '200px' }"
                @click="designTem(pageTem)"
              >
                <img src='/img/pageTem.png' style='width: 100%;height: 100%'>
              </div>
              <template slot="actions">
                <a-tooltip>
                  <template slot="title">
                    预览
                  </template>
                  <span @click="previewTem(pageTem)"> <a-icon type="eye" style='font-size: 20px;' /> </span>
                </a-tooltip>
                <a-dropdown>
                  <span @click="e => e.preventDefault()"> <a-icon type="setting" style='font-size: 20px;' /></span>
                  <a-menu slot="overlay" @click="deleteTem($event,pageTem)">
                    <a-menu-item>
                      <a name="edit">编辑</a>
                    </a-menu-item>
                    <a-menu-item>
                      <a name='delete'>删除</a>
                    </a-menu-item>
                    <a-menu-item>
                      <a name='default'>{{pageTem.showType==="0"?"取消默认":"设为默认"}}</a>
                    </a-menu-item>
                  </a-menu>
                </a-dropdown>

              </template>
            </a-card>
          </a-col>
        </a-row>
        <a-modal :title="title" :visible="visible" @ok="handleOk" @cancel="visible = !visible">
          <a-form>
            <a-form-item>
              <a-input v-model="templateName" placeholder="页面模板名称" :maxLength="20" />
            </a-form-item>
          </a-form>
        </a-modal>
      </div>
      <div class="pagination-div">
        <a-pagination
          show-quick-jumper
          show-size-changer
          :hideOnSinglePage="false"
          :default-current="ipagination.current"
          :total="ipagination.total"
          @change="onChange"
          :page-size="ipagination.pageSize"
          :pageSizeOptions="ipagination.pageSizeOptions"
          :show-total="(total) => `共 ${ipagination.total} 条`"
          @showSizeChange="onShowSizeChange"
          size="small"
        >
        </a-pagination>
      </div>
    </a-card>
    <custom-page-designer ref='cpd'></custom-page-designer>
  </div>
</template>

<script>
import { getAction, postAction, putAction, deleteAction } from '@/api/manage'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import CustomPageDesigner from '@views/customPages/models/CustomPageDesigner.vue'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
export default {
  name: 'app',
  mixins: [JeecgListMixin,YqFormSearchLocation],
  components: {
    CustomPageDesigner

  },
  data() {
    return {
      maxLength:50,
      CardColLayout: {
        xl: {
          span: 6,
        },
        lg: {
          span: 6,
        },
        md: {
          span: 8,
        },
        sm: {
          span: 12,
        },
        xs: {
          span: 24,
        },
      },
      editFlag: false,
      queryParam: {
        name: '',
        topoType: '0',
      },
      visible: false,
      title: '新增',
      templateId: '',
      templateName: '',
      templateInfo:null,
      dataSource: [],
      ipagination: {
        current: 1,
        pageSize: 8,
        pageSizeOptions: ['8', '16', '24'],
        showTotal: (total, range) => {
          return range[0] + '-' + range[1] + ' 共' + total + '条'
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0,
      },
      url: {
        list: '/custom/template/list',
        delete: '/custom/template/delete',
        add: '/custom/template/add',
        edit: '/custom/template/edit',
      },
      hoverHeight: '0px',
      clientHeight: '',
      staticDomainURL: '',
    }
  },
  created() {
    this.staticDomainURL = window._CONFIG['staticDomainURL'] + '/'
  },
  mounted() {
    this.clientHeight = `${document.documentElement.clientHeight}` //获取浏览器可视区域高度
    let that = this
    window.onresize = function () {
      that.clientHeight = `${document.documentElement.clientHeight}`
    }
  },
  methods: {
    loadData(arg) {
      if (!this.url.list) {
        this.$message.error('请设置url.list属性!')
        return
      }
      if (arg === 1) {
        this.ipagination.current = 1
      }
      var params = this.getQueryParams() //查询条件
      this.loading = true


      getAction(this.url.list, params).then((res) => {
        if (res.success && res.result) {
          this.dataSource = res.result.records;
          this.ipagination.total = res.result.total;
        }
        this.loading = false
      })
    },
    onShowSizeChange(current, pageSize) {
      this.ipagination.pageSize = pageSize
      this.ipagination.current = current
      this.loadData()
    },
    onChange(pageNumber, pageSize) {
      this.ipagination.pageSize = pageSize
      this.ipagination.current = pageNumber

      this.tabShow = false
      // this.ipagination.pageSize = 8
      // this.ipagination.pageSizeOptions = ['8', '16', '24']

      this.loadData()
    },
    handleOk() {
      if (!/^[a-zA-Z0-9\u4e00-\u9fa5~!@#$%^&*()_+`\-={}:";'<>?,.\/]{1,20}$/.test(this.templateName)) {
        this.$message.warning('拓扑名称应为2-20位字符!')
        return
      } else {
        if (this.title == '新增') {
          postAction(this.url.add, {
            name: this.templateName,
            showType: "1",
            templateData:"",
            image:"",
          }).then((res) => {
            if (res.code == 200) {
              this.loadData()
              this.visible = false
            } else {
              this.$message.warning(res.message)
            }
          })
        } else if (this.title == '修改') {
          putAction(this.url.edit, {
            id: this.templateId,
            name: this.templateName,
            showType: this.templateInfo.showType,
            templateData:this.templateInfo.templateData,
            image:this.templateInfo.image,
          }).then((res) => {
            if (res.code == 200) {
              this.loadData()
              this.visible = false
              this.templateId = '';
              this.templateInfo = null;
            } else {
              this.$message.warning(res.message)
            }
          })
        } else {
          this.visible = false
        }
        this.templateName = ''
      }
    },
    addTem() {
      this.visible = true
      this.title = '新增'
    },
    designTem(record) {
      this.$refs.cpd.show(record)
    },
    previewTem(record) {
     this.$refs.cpd.preview(record)
    },
    deleteTem(e,record) {
      var that = this
      if(e.domEvent.target.name === "delete"){
        this.$confirm({
          title: '确认删除',
          okText: '是',
          cancelText: '否',
          content: '是否删除数据?',
          onOk: function () {
            that.loading = true
            deleteAction(that.url.delete, {
              id: record.id,
            }).then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.loadData()
              } else {
                that.$message.warning(res.message)
              }
            })
          },
        })
      }
      else if(e.domEvent.target.name === "edit")
      {
        this.editName(record)
      }else if(e.domEvent.target.name === "default"){
          this.setDefault(record)
      }
    },
    editName(record) {
      this.title = '修改'
      this.templateInfo = record;
      this.templateName = record.name;
      this.templateId = record.id;
      this.visible = true
    },
    setDefault(record){
      getAction("/custom/template/updateShowType", {
        id: record.id,
        showType:record.showType==="0"?"1":"0",
      }).then((res) => {
        if (res.code == 200) {
          this.$message.success("设置成功！")
          this.loadData()
        } else {
          this.$message.warning(res.message)
        }
      })
    }
  },
}
</script>

<style lang="less" scoped>
@import '~@assets/less/common.less';

.table-container {
  //margin-top: 16px;
  background-color: #fff;
  //padding: 20px 24px 0px 24px;
}

.table-operator {
  margin-bottom: 10px;
}

.template-pic {
  cursor: pointer;
  width: 100%;
  height: 100%;
  margin: auto;
  border: 1px solid #e8e8e8;
  position: relative;
}

.pagination-div {
  display: flex;
  flex-direction: row-reverse;
  padding-bottom: 20px;
  margin-top: 20px;
}

::v-deep .ant-card-head-title {
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
}

::v-deep .ant-card-extra {
  clear: both;
  margin: 0px;
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  padding: 12px 0 !important;
}

::v-deep .ant-card-head {
  border-bottom: 0px solid #e8e8e8;
}

::v-deep .ant-card-actions {
  border-top: 0px solid #e8e8e8;
}

::v-deep .ant-card-bordered {
  width: 100%;
  // box-shadow: 0 3px 7px -1px rgb(0 0 0 / 16%) !important;
  position: relative;

  .ant-card-head-wrapper {
    justify-content: flex-end;
  }
}

::v-deep .ant-card-body {
  padding: 0px 22px 8px;
  width: 100%;
  margin: 0px;
}

</style>