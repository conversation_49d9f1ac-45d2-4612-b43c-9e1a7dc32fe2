<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <!-- 查询区域 -->
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span='spanValue'>
                <a-form-item label="级别名称">
                  <a-input :maxLength='maxLength' placeholder="请输入级别名称" v-model="queryParam.levelName" :allowClear='true' autocomplete='off' />
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label="告警等级">
                  <a-input :maxLength='maxLength' placeholder="请输入告警等级" v-model="queryParam.alarmLevel" :allowClear='true'
                    autocomplete='off' />
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label="告警声音">
                  <a-input :maxLength='maxLength' placeholder="请输入告警声音" v-model="queryParam.voice" :allowClear='true' autocomplete='off' />
                </a-form-item>
              </a-col>
              <a-col :span='colBtnsSpan()'>
                <span class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button class="btn-search btn-search-style" type="primary" @click="searchQuery">查询</a-button>
                  <a-button class="btn-reset btn-reset-style" @click="searchReset">重置</a-button>
                  <a v-if="isVisible" class='btn-updown-style' @click="doToggleSearch">
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>

      <a-card :bordered="false" style="width: 100%; flex: auto">
        <!-- 操作按钮区域 -->
        <div class="table-operator table-operator-style">
          <a-button class="btn-add" @click="handleAdd">新增</a-button>
          <a-dropdown v-if="selectedRowKeys.length > 0">
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item key="1" @click="batchDel">
                删除
              </a-menu-item>
            </a-menu>
            <a-button>批量操作
              <a-icon type="down" />
            </a-button>
          </a-dropdown>
        </div>

        <!-- table区域-begin -->
        <a-table ref="table" bordered :rowKey="(record,index)=>{return record.id}" :columns="columns"
          :dataSource="dataSource" :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}" :pagination="ipagination"
          :loading="loading" :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          @change="handleTableChange">
          <!-- 字符串超长截取省略号显示-->
          <span slot="templateContent" slot-scope="text">
            <j-ellipsis :value="text" :length="25" />
          </span>
          <span slot="action" slot-scope="text, record" class="caozuo" style="padding-top: 10px;padding-bottom: 10px;">
            <a @click="handleDetail(record)">查看</a>
            <a-divider type="vertical" />
            <a @click="handleEdit(record)">编辑</a>
            <a-divider type="vertical" />
            <a-popconfirm :title="delText" @visibleChange='visibleChange($event,record)' @confirm="() => handleDelete(record.id)">
              <a>删除</a>
            </a-popconfirm>
          </span>
          <template slot="voice" slot-scope="text,record,index">
            <div style="display:flex; justify-content: space-between;" v-if="record.alarmMode == '0'">
              <alarm-upload v-model="record.voice" :file-list='fileList' :showUploadList='true' name="file"
                :multiple="false">
              </alarm-upload>
              <yq-icon :type="iconName" style="font-size: 20px; cursor: pointer; color: #7da1ff;line-height:40px"
                @click.native="changeTipStatus(record,index)" />
              <audio muted="true" ref="musicTips" id="musicTips" :src="musicUrl + '/' + record.voice" controls
                :autoplay="isAutoPlay" style="display:none"></audio>
            </div>
          </template>
          <template slot="tooltip" slot-scope="text">
            <a-tooltip placement="topLeft" :title="text" trigger="hover">
              <div class='tooltip'>
                {{ text }}
              </div>
            </a-tooltip>
          </template>
          <template slot="nameColor" slot-scope="text,record">
            <div :style='{backgroundColor:record.color}'
              style='display:inline-block;color:#ffffff; border-radius: 10px; padding: 2px 10px;'>
              {{ text }}
            </div>
          </template>
        </a-table>
      </a-card>
      <!-- table区域-end -->
      <alarm-level-modal ref="modalForm" @ok='loadData'></alarm-level-modal>
    </a-col>
  </a-row>
</template>

<script>
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import yqIcon from '@/components/tools/SvgIcon'
  import alarmUpload from './alarmUpload'
  import {
   postAction,
    getAction
  } from '@/api/manage'
  import {
    YqFormSearchLocation
  } from '@/mixins/YqFormSearchLocation'
  import AlarmLevelModal from '@/views/alarmManage/alarmLevel/AlarmLevelModal'
  export default {
    name: 'AlarmLevel',
    mixins: [JeecgListMixin, YqFormSearchLocation],
    components: {
      AlarmLevelModal,
      alarmUpload,
      yqIcon
    },
    data() {
      return {
        maxLength:50,
        isAutoPlay: false,
        musicIndex: 11,
        musicUrl: window._CONFIG['staticDomainURL'],
        uploadAction: window._CONFIG['domianURL'] + "/sys/common/upload",
        fileList: null,
        iconName: 'notice_play',
        description: '告警等级设置界面',
        formItemLayout: {
          labelCol: {
            style: 'width:80px',
          },
          wrapperCol: {
            style: 'width:calc(100% - 80px)'
          }
        },
        // 表头
        columns: [{
            title: '级别名称',
            dataIndex: 'levelName',
            scopedSlots: {
              customRender: 'nameColor'
            },
            customCell: () => {
              let cellStyle = 'text-align: center'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '告警等级',
            dataIndex: 'alarmLevel',
            customCell: () => {
              let cellStyle = 'text-align: center'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '告警声音',
            dataIndex: 'voice',
            scopedSlots: {
              customRender: 'voice'
            },
          },
          {
            title: '级别描述',
            dataIndex: 'remark',
            scopedSlots: {
              customRender: 'tooltip'
            },
            customCell: () => {
              let cellStyle = 'text-align: left;min-width: 200px;max-width:400px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '操作',
            align: 'center',
            width: 180,
            fixed: 'right',
            dataIndex: 'action',
            scopedSlots: {
              customRender: 'action'
            },
          },
        ],
        url: {
          list: '/alarm/alarmLevel/list',
          delete: '/alarm/alarmLevel/delete',
          deleteBatch: '/alarm/alarmLevel/deleteBatch'
        },
        delText:"确定删除吗?"
      }
    },
    computed: {
      importExcelUrl: function () {
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
      },
    },
    mounted() {},
    methods: {
      changeTipStatus(record, index) {
        if (this.musicIndex != index) {
          this.musicIndex = index
          this.$refs.musicTips.muted = false
          this.$refs.musicTips.pause()
          this.$refs.musicTips.src = this.musicUrl + '/' + record.voice
          setTimeout(() => {
            this.$refs.musicTips.play()
          }, 200)
        } else {
          this.$refs.musicTips.pause()
          this.musicIndex = ''
        }
      },
      visibleChange(v,record){
        if(v){
          getAction("/alarm/alarmLevel/isRelatedTemplate",{levelName:record.levelName}).then( res =>{
            // console.log("shuju",res)
            if(res.success && res.result){
              this.delText="当前告警等级已被告警策略使用，删除可能会对告警策略造成影响! 确定删除吗?"
            }
            else{
              this.delText="确定删除吗?"
            }
          }).catch(err=>{
            this.delText="确定删除吗?"
          })
        }
      }
    },
  }
</script>
<style lang='less' scoped>
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';

  /deep/.ant-table-tbody .ant-table-row td {
    padding-top: 5px;
    padding-bottom: 5px;
  }
</style>