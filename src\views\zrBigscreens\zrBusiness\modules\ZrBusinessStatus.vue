<template>
<div class='zr-business-status'>
  <zr-bigscreen-title title='系统首页'></zr-bigscreen-title>
  <div class='slot-block'>
    <slot>
      <div class='status-content'>
        <div class='card-box'>
          <div class='status-header-bar'>
            <div :title="businessData.businessName" class='status-header-bar-left'>{{businessData.businessName}}</div>
            <div class='status-header-bar-right' :style='{background:status.color}'>
              {{ status.label}}
            </div>
          </div>
          <div class='status-body' :style='{ backgroundImage: `url(${businessImage})` }'>
          </div>

        </div>
      </div>
    </slot>
  </div>

</div>
</template>
<script>
import ZrBigscreenTitle from '@views/zrBigscreens/modules/ZrBigscreenTitle.vue'
import {businessStatus} from "@views/zrBigscreens/modules/zrUtil"
export default {
  name: 'BusinessStatus',
  components: { ZrBigscreenTitle },
  props: {
    businessData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
    }
  },
  created() {
  },
  mounted() {
  },
  computed:{
    status(){
      return businessStatus.find(el=>el.value === this.businessData.status) || {}
    },
    businessImage(){
      if(this.businessData.businessImage){
        return  window._CONFIG['downloadUrl']+"/"+ this.businessData.businessImage
      }
      return ""
    }
  },
  methods: {
  }
}
</script>

<style scoped lang='less'>
.zr-business-status{
  height: 100%;
  .slot-block{
    height: calc(100% - 51px);
    width: 100%;
    background: linear-gradient(to right, rgba(29, 78, 140, 0.3), rgba(29, 78, 140, 0.0));
  }
  .status-content{
    padding:12px 12px 24px;
    overflow: hidden;
    .card-box{
      max-width: 401px;
      .status-header-bar{
        width: 100%;
        //width: 417px;
        height: calc(38 / 19.2 * 1vw);
        max-height: 38px;
        background: rgba(94, 140, 199, 0.5);
        display: flex;
        justify-content: space-between;
        align-items: center;
        .status-header-bar-left{
          font-weight: 400;
          font-size: calc(16 / 19.2 * 1vw);
          color: #FEFEFF;
          opacity: 0.95;
          margin-left: calc(13 / 19.2 * 1vw);
          line-height: 1;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          @media (min-width: 1920px) {
            font-size: 16px;
          }
        }
        .status-header-bar-right{
          width: calc(53 / 19.2 * 1vw);
          max-width: 53px;
          height: calc(23 / 19.2 * 1vw);
          max-height: 23px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 400;
          font-size: calc(14 / 19.2 * 1vw);
          color: #FFFFFF;
          margin-right: calc(40 / 19.2 * 1vw);
          @media (min-width: 1920px) {
            font-size: 14px;
            margin-right: 40px;
          }
        }
      }
      .status-body{
        width:100%;
        height:0;
        padding-top: 52.25%;
        border: 1px solid rgba(244,244,244,0.69);
        background-size: 100% 100%;
      }
    }
  }
}
</style>