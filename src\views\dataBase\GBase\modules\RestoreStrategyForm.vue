<template>
  <a-spin :spinning='confirmLoading'>
    <j-form-container>
      <a-form :form='form' slot='detail'>
        <a-row :gutter='24'>
          <a-col :span='24'>
            <a-form-item label='任务名称' :labelCol='labelCol' :wrapperCol='wrapperCol' prop='taskName'>
              <a-input v-decorator="['taskName', validatorRules.taskName]" :allowClear='true' autocomplete='off'
                       placeholder='请输入任务名称'></a-input>
            </a-form-item>
          </a-col>
          <a-col :span='24'>
            <a-form-item label='备份任务' :labelCol='labelCol' :wrapperCol='wrapperCol' prop='backTaskId'>
              <a-select v-decorator="['backTaskId', validatorRules.backTaskId]" placeholder='请选择备份任务'
                        style='width: 100%' :allowClear='true' :options='backTaskOptions' @change='changeBackTask'>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span='24'>
            <a-form-item label='数据库' :labelCol='labelCol' :wrapperCol='wrapperCol' prop='targetDb'>
              <a-select v-decorator="['targetDb', validatorRules.targetDb]" placeholder='请选择数据资源库'
                        style='width: 100%' :allowClear='true' :options='dataBases' @change='changeDb' disabled>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span='24'>
            <a-form-item label='恢复级别' :labelCol='labelCol' :wrapperCol='wrapperCol' prop='recoveryLevel'>
              <a-select v-decorator="['recoveryLevel', validatorRules.recoveryLevel]" :options='backLevels'
                        placeholder='请选择恢复级别' :allowClear='true' disabled
                        style='width: 100%'>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span='24' v-if='form.getFieldValue("recoveryLevel")=="schema"'>
            <a-form-item label='待恢复模式' :labelCol='labelCol' :wrapperCol='wrapperCol' prop='recoveryLevelValue'>
              <a-input v-decorator="['recoveryLevelValue', validatorRules.backLevelValueS]" :allowClear='true'
                       autocomplete='off' disabled
                       placeholder='请输入待恢复模式'></a-input>
            </a-form-item>
          </a-col>
          <a-col :span='24' v-else-if='form.getFieldValue("recoveryLevel")=="table"'>
            <a-form-item label='待恢复表名' :labelCol='labelCol' :wrapperCol='wrapperCol' prop='recoveryLevelValue'>
              <a-input v-decorator="['recoveryLevelValue', validatorRules.backLevelValueT]" :allowClear='true'
                       autocomplete='off' disabled
                       placeholder='请输入待恢复表名'></a-input>
            </a-form-item>
          </a-col>
          <a-col :span='24'>
            <a-form-item label='恢复内容' :labelCol='labelCol' :wrapperCol='wrapperCol' prop='recoveryContent'>
              <a-select v-decorator="['recoveryContent', validatorRules.recoveryContent]" :options='backContents'
                        placeholder='请选择恢复内容' :allowClear='true'
                        style='width: 100%' disabled>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span='24' v-if='form.getFieldValue("targetDb")'>
            <a-form-item label='获取备份记录方式' :labelCol='labelCol' :wrapperCol='wrapperCol' prop='recoveryFrom'>
              <a-radio-group v-decorator="['recoveryFrom',{initialValue: '0'}]" @change='changeRecoveryFrom'>
                <a-radio value='1'> 自定义</a-radio>
                <a-radio value='0'> 最后一次执行记录</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
          <a-col :span='24' v-if='form.getFieldValue("targetDb") && form.getFieldValue("recoveryFrom")==="1"'>
            <a-form-item label='备份记录' :labelCol='labelCol' :wrapperCol='wrapperCol' prop='recoveryRecordId'>
              <a-select v-decorator="['recoveryRecordId', validatorRules.recoveryRecordId]" :options='recordList'
                        placeholder='请选择备份记录' :allowClear='true'
                        style='width: 100%'>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span='24'>
            <a-form-item label='执行方式' :labelCol='labelCol' :wrapperCol='wrapperCol' prop='executeType'>
              <a-radio-group v-decorator="['executeType',{initialValue: '1'}]">
                <a-radio value='1'> 手动</a-radio>
                <a-radio value='0'> 周期</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
          <a-col :span='24' v-if='form.getFieldValue("executeType")==="0"'>
            <a-form-item label='恢复频率' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <!-- cron表达式  -->
              <j-cron ref='innerVueCron' v-decorator="['executeCron', validatorRules.executeCron]" @change='setCorn'
                      :hideYear='true'></j-cron>
            </a-form-item>
          </a-col>
          <a-col :span='24'>
            <a-form-item label='状态' :labelCol='labelCol' :wrapperCol='wrapperCol' prop='taskStatus'>
              <a-radio-group v-decorator="['taskStatus',{initialValue: '1'}]">
                <a-radio value='1'> 启用</a-radio>
                <a-radio value='0'> 禁用</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
          <a-col :span='24'>
            <a-form-model-item label='描述' :labelCol='labelCol' :wrapperCol='wrapperCol' prop='description'>
              <a-textarea style='width: 100%' v-decorator="['description', validatorRules.description]"
                          :autoSize='{minRows:2,maxRows:4}'
                          :allow-clear='true' autocomplete='off' placeholder='请输入描述' />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
import {
  httpAction, getAction, deleteAction
} from '@/api/manage'
import pick from 'lodash.pick'
import JFormContainer from '@/components/jeecg/JFormContainer'
import JCron from '@/components/jeecg/JCron.vue'
import {
  JeecgListMixin
} from '@/mixins/JeecgListMixin'

export default {
  name: 'BfStrategyForm',
  mixins: [JeecgListMixin],
  components: {
    JFormContainer,
    JCron
  },
  props: {
    backLevels: {
      type: Array,
      default: () => {
        return []
      }
    }, backContents: {
      type: Array,
      default: () => {
        return []
      }
    },
    backFormats: {
      type: Array,
      default: () => {
        return []
      }
    }, dataBases: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      form: this.$form.createForm(this),
      model: {
        executeType: '1'
      },
      labelCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 5
        },
        md: {
          span: 4
        },
        lg: {
          span: 4
        }
      },
      wrapperCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 19
        },
        md: {
          span: 20
        },
        lg: {
          span: 20
        }
      },
      confirmLoading: false,
      validatorRules: {
        taskName: {
          rules: [{
            required: true,
            message: '请输入任务名称'
          },
            {
              min: 1,
              max: 30,
              message: '长度在1到30个字符',
              trigger: 'blur'
            }
          ]
        }, backTaskId: {
          rules: [{
            required: true,
            message: '请选择备份任务'
          }]
        }, targetDb: {
          rules: [{
            required: true,
            message: '请选择数据库'
          }]
        }, recoveryLevel: {
          rules: [{
            required: true,
            message: '请选择恢复级别'
          }]
        }, recoveryContent: {
          rules: [{
            required: true,
            message: '请选择恢复内容'
          }]
        }, recoveryRecordId: {
          rules: [{
            required: true,
            message: '请选择备份记录'
          }]
        }, executeType: {
          rules: [{
            required: true,
            message: '请选择执行方式'
          }]
        },  backLevelValueS: {
          rules: [{
            required: true,
            message: '请输入待恢复模式'
          },
            {
              min: 1,
              max: 100,
              message: '长度在1到100个字符',
              trigger: 'blur'
            }
          ]
        }, backLevelValueT: {
          rules: [{
            required: true,
            message: '请输入待恢复表名'
          },
            {
              min: 1,
              max: 100,
              message: '长度在1到100个字符',
              trigger: 'blur'
            }
          ]
        }, backFileName: {
          rules: [{
            required: true,
            message: '请输入备份目的文件名'
          },
            {
              min: 1,
              max: 50,
              message: '长度在1到50个字符',
              trigger: 'blur'
            }
          ]
        }, backFileSuffix: {
          rules: [{
            required: true,
            message: '请输入备份目的文件名后缀'
          },
            {
              min: 1,
              max: 30,
              message: '长度在1到30个字符',
              trigger: 'blur'
            }
          ]
        },
        executeCron: {
          initialValue: '0 0 0 * * ?',
          rules: [{
            required: true,
            validator: this.validateCorn
          }]
        },
        description: {
          rules: [{ required: false, min: 0, max: 255, message: '任务描述长度应在 0-255 之间' }]
        }
      },
      url: {
        add: '/gbase/task/add', // 添加任务
        edit: '/gbase/task/edit', // 编辑任务
        validateCorn: '/autoInspection/devopsAutoInspection/cronCheckSix' // 校验cron表达式
      },
      effectiveDate: null,
      taskId: undefined,
      effectiveStatus: '0', // 默认永久有效
      disableMixinCreated: true,
      recordList: [],
      lastRecordId: '',
      backTaskOptions: [],
      backTaskList: []
    }
  },
  created() {
    this.getBackTaskList()
  },
  computed: {},
  methods: {
    //获取备份任务列表
    getBackTaskList() {
      getAction('/gbase/task/list', { pageNo: 1, pageSize: -1, taskType: 0 }).then((res) => {
        if (res.success) {
          this.backTaskList = res.result.records
          this.backTaskOptions = res.result.records.map(el => {
            return { label: el.taskName, value: el.id }
          })
        }
      })
    },
    changeRecoveryFrom(e) {
      if (e.target.value === '1') {
        this.getRecordList(this.form.getFieldValue('backTaskId'))
      } else {
        this.getLastRecord(this.form.getFieldValue('backTaskId'))
      }
    },
    changeBackTask(value) {
      let backId = value
      let backInfo = this.backTaskList.find(el => el.id === value)
      let pickDta = {
        targetDb: backInfo.targetDb,
        recoveryLevel: backInfo.backLevel,
        recoveryLevelValue: backInfo.backLevelValue,
        recoveryContent: backInfo.backContent,
        recoveryRecordId: undefined,
      }
      this.form.setFieldsValue(pickDta)
      if(backInfo.levels !== "database"){
        this.$nextTick(() => {
          this.form.setFieldsValue({recoveryLevelValue:backInfo.backLevelValue})
        })
      }
      // console.log('backInfo', backInfo)
      if (this.form.getFieldValue('recoveryFrom') == 1) {
        this.getRecordList(backId)
      } else {
        this.getLastRecord(backId)
      }
    },
    changeDb(value) {
    },
    //获取最后的记录
    getLastRecord(id) {
      getAction('/gbase/task/lastRecord', { taskId: id })
        .then((res) => {
          if (res.success && res.result) {
            this.lastRecordId = res.result.id
          }
          console.log('获取lastRecord', res, this.lastRecordId)
        })
    },
    /*
   * 获取备份列表
   * */

    getRecordList(id) {
      getAction('/gbase/task/recordList', { pageNo: 1, pageSize: -1, recordType: 0, taskId: id }).then((res) => {
        if (res.success) {
          this.recordList = res.result.records.map(el => {
            return { label: el.backFile, value: el.id }
          })
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    closeForm() {
      this.$emit('closeForm')
    },
    add() {
      this.edit({})
    },
    edit(record) {
      this.queryParam.taskId = record.id
      this.form.resetFields()
      this.model = Object.assign({}, record)
      if (record.id) {
        this.$nextTick(() => {
          let formdata = pick(this.model, ['taskName', "backTaskId",'targetDb', 'recoveryLevel', 'recoveryContent', 'executeType', 'description'])
          this.formInit(formdata)
          this.$nextTick(() => {
            //初始备份内容
            if (record.recoveryLevel !== 'tabel') {
              this.form.setFieldsValue(
                { recoveryLevelValue: this.model.recoveryLevelValue }
              )
            }
            //初始备份记录方式
            this.form.setFieldsValue(
              { recoveryFrom: this.model.recoveryFrom }
            )
            //初始cron
            if (record.executeType === '0') {
              this.form.setFieldsValue({ executeCron: record.executeCron })
              this.form.setFieldsValue({ recoveryContent: record.recoveryContent })
            }
            //初始备份记录的
            this.$nextTick(() => {
              if (record.recoveryFrom === '0') {
                this.lastRecordId = record.recoveryRecordId
              } else {
                this.getRecordList(this.form.getFieldValue('backTaskId'))
                this.form.setFieldsValue({ recoveryRecordId: record.recoveryRecordId })
              }
            })

          })
        })
      }

    },
    formInit(pickData) {
      this.form.setFieldsValue(
        pickData
      )
    },
    //提交
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (
          !err
        ) {

          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }

          let formData = Object.assign(that.model, values)
          if(!formData.recoveryRecordId){
            this.$message.warning("备份任务没有执行过，没有备份执行记录")
            return
          }
          formData.taskType = '1'
          if (formData.recoveryFrom === '0') {
            formData.recoveryRecordId = this.lastRecordId
          }
          if (formData.executeType === '1') {
            formData.executeCron = ''
          }
          that.confirmLoading = true
          httpAction(httpurl, formData, method)
            .then((res) => {
              that.confirmLoading = false
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
                that.closeForm()
              } else {
                that.$message.warning(res.message)
              }
            })
            .catch((err) => {
              that.$message.warning(err.message)
              that.confirmLoading = false
            })
        }
      })
    },
    validateCorn(rule, value, callback) {
      if (rule.required) {
        if (value && value.length > 0) {
          getAction(this.url.validateCorn, {
            cronExpression: value
          }).then((res) => {
            if (res.success) {
              callback()
            } else {
              callback('cron表达式格式错误!')
            }
          })
        } else {
          callback('请输入cron表达式')
        }
      } else {
        callback()
      }
    },
    //周期执行
    setCorn(data) {
      if (data && data.target != null) {
        let dataList = data.target.value.split(' ')
        if (dataList[0] === '*') {
          this.$message.warning('请确认是否每秒都执行')
        }
      } else {
        let dataList = data.split(' ')
        if (dataList[0] === '*') {
          this.$message.warning('请确认是否每秒都执行')
        }
      }

      if (Object.keys(data).length === 0) {
        this.$message.warning('请输入cron表达式!')
      }
    }
  }
}
</script>
<style scoped lang='less'>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';

.colorBox {
  margin-top: 18px;
  margin-bottom: 18px;
}

.colorTotal {
  padding-left: 7px;
  border-left: 4px solid #1e3674;
}

/* 定义滚动条样式 */
::-webkit-scrollbar {
  width: 0.15rem
  /* 12/80 */;
  // height: 6px;
  //background-color: #222325;
}

/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track {
  box-shadow: inset 0 0 0px rgba(240, 240, 240, 0.5);
  // border-radius: 50%;
  background-color: #eaeaea;
  border-radius: 0.1rem
  /* 8/80 */;
}

/*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
  border-radius: 0.1rem
  /* 8/80 */;
  box-shadow: inset 0 0 0px rgba(240, 240, 240, 0.5);
  background-color: #d6d6d6;
}

::v-deep .ant-spin-nested-loading {
  padding-right: 13px;
}
</style>