<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <!-- 查询区域 -->
      <a-card :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="型号">
                  <a-input
                    placeholder="请输入型号"
                    :allowClear="true"
                    autocomplete="off"
                    v-model="queryParam.model"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="产品分类">
                  <j-tree-select-expand v-model="queryParam.productCategoryId" show-search treeNodeFilterProp="title"
                                        placeholder="请选择产品分类" dict="cmdb_assets_category,category_name,id" pidField="parent_id"
                                        condition='{"delflag":0,"category_state":"0","is_monitorable":"1"}' pidValue="0"
                                        />
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="所属期">
                  <a-select :allow-clear='true' v-model='queryParam.catalogue' placeholder="请选择所属期">
                    <a-select-option v-for='(item,index) in catalogueList' :key='"catalogue_"+index' :value='item.value' :label='item.text'>{{item.text}}</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="colBtnsSpan()">
                <span
                  class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                >
                  <a-button type="primary" @click="searchQuery">查询</a-button>
                  <a-button @click="searchReset" style="margin-left: 10px">重置</a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <!-- 查询区域-END -->

      <a-card :bordered="false" style="flex: auto" class="core">
        <!-- 操作按钮区域 -->
        <div class="table-operator">
          <a-button @click="handleAdd">新增</a-button>
          <a-button @click="handleExportXls('信创设备')">导出</a-button>
          <a-dropdown v-if="selectedRowKeys.length > 0">
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item key="1" @click="batchDel">删除</a-menu-item>
            </a-menu>
            <a-button> 批量操作 <a-icon type="down" /></a-button>
          </a-dropdown>
        </div>

        <!-- table区域-begin -->
        <div>
          <a-table
            ref="table"
            bordered
            rowKey="id"
            :columns="columns"
            :dataSource="dataSource"
            :pagination="ipagination"
            :loading="loading"
            :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
            class="j-table-force-nowrap"
            @change="handleTableChange"
          >
            <span slot="action" slot-scope="text, record">
              <a style="color: #409eff" @click="handleDetailPage(record)">查看</a>
              <span>
                 <a-divider type="vertical" />
                 <a style="color: #409eff" @click="handleEdit(record)">编辑</a>
              </span>
             <span >
               <a-divider type="vertical" />
               <a style="color: #409eff" @click="handleDelete(record)">删除</a>
             </span>
            </span>
          </a-table>
        </div>
        <innovated-device-modal ref="modalForm" @ok="modalFormOk"></innovated-device-modal>
        <!-- 下载模版 -->
        <iframe id="download" style="display: none"></iframe>
      </a-card>
    </a-col>
  </a-row>
</template>

<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import InnovatedDeviceModal from '@views/cmdb/InnovatedDevice/modules/InnovatedDeviceModal.vue'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
import { ajaxGetDictItems, getDictItemsFromCache } from '@api/api'
import { deleteAction, getAction } from '@api/manage'
export default {
  name: 'InnovatedDeviceList',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components: {
    InnovatedDeviceModal
  },
  data() {
    return {
      description: '新创设备管理页面',
      formItemLayout: {
        labelCol: {
          style: 'width:90px',
        },
        wrapperCol: {
          style: 'width:calc(100% - 90px)'
        }
      },
      catalogueList: [],
      disableMixinCreated: true,
      // 表头
      columns: [
        {
          title: '型号',
          dataIndex: 'model'
        },
        {
          title: '产品分类',
          dataIndex: 'productCategoryText'
        },
        {
          title: '供应商',
          dataIndex: 'supplierText'
        },
        {
          title: '所属期',
          dataIndex: 'catalogueText'
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 150,
          scopedSlots: { customRender: 'action' },
        },
      ],
      url: {
        list: '/itInnovate/cmdbItInnovate/list',
        deleteBatch: '/itInnovate/cmdbItInnovate/deleteBatch',
        downloadTemplateXlsUrl: '/itInnovate/cmdbItInnovate/downloadTemplate',
        importExcelUrl: '/itInnovate/cmdbItInnovate/importExcel',
        exportXlsUrl: '/itInnovate/cmdbItInnovate/exportXls'
      },
    }
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
    downloadTemplateXlsUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.downloadTemplateXlsUrl}`
    },
  },
  created() {
    this.initTopicDictData('innovatedDeviceCatalogue')
  },
  activated() {
    this.loadData()
  },
  methods: {
    loadData(arg) {
      if (!this.url.list) {
        this.$message.error('请设置url.list属性!')
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1
      }
      var params = this.getQueryParams() //查询条件
      this.loading = true
      getAction(this.url.list, params).then((res) => {
        if (res.success) {
          this.dataSource = res.result.records || res.result
          if(this.dataSource.length>0){
            this.dataSource.map((item)=>{
             item.catalogueText= this.getCatalogueText(item.catalogue)
            })
          }
          if (this.dataSource.length < 9) {
            this.clientHeight = false
          }
          this.ipagination.total = res.result.total
        }
        if (res.code === 510) {
          this.$message.warning(res.message)
        }
        this.loading = false
      })
    },
    getCatalogueText(t){
      let dict= this.catalogueList.filter((item)=>{
        if(item.value==t){
          return item
        }
      })
      return dict[0].text
    },
    initTopicDictData(dictCode) {
      //优先从缓存中读取字典配置
      if (getDictItemsFromCache(dictCode)) {
        this.catalogueList = getDictItemsFromCache(dictCode)
        return
      }

      //根据字典Code, 初始化字典数组
      ajaxGetDictItems(dictCode, null).then((res) => {
        if (res.success) {
          this.catalogueList = res.result
        }
      })
    },
    //excel模板
    handleTemplateXls() {
      const path = this.downloadTemplateXlsUrl
      document.getElementById('download').src = path
    },
    //删除
    handleDelete(record) {
      if (!this.url.deleteBatch) {
        this.$message.error('请设置url.delete属性!')
        return
      }
      var that = this
      this.$confirm({
        title: '确认删除',
        okText: '是',
        cancelText: '否',
        content: '是否删除选中数据?',
        onOk: function() {
          that.loading = true
          deleteAction(that.url.deleteBatch, {
            ids: record.id
          }).then((res) => {
            if (res.success) {
              //重新计算分页问题
              that.reCalculatePage(that.selectedRowKeys.length)
              that.$message.success(res.message)
              that.loadData()
            } else {
              that.$message.warning(res.message)
              that.loadData()
            }
          })
        }
      })
    },
  },
}
</script>
<style lang="less" scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
</style>
