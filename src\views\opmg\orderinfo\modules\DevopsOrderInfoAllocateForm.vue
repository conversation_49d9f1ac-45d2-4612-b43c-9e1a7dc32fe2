<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-col :span="24">
          <a-form-item label="责任人" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <j-select-user-by-dep
              v-decorator="['handlerUserId', validatorRules.handlerUserId]"
              :multi="multi"
            ></j-select-user-by-dep>
          </a-form-item>
        </a-col>
        <!-- <a-col :span="24">
          <a-form-item class="two-words" label="描述" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input v-decorator="['orderDescription']" :disabled="true"></a-input>
          </a-form-item>
        </a-col> -->
      </a-form>
    </j-form-container>
  </a-spin>
</template>
<script>
import { httpAction, getAction } from '@/api/manage'
import { validateDuplicateValue } from '@/utils/util'
import JFormContainer from '@/components/jeecg/JFormContainer'
import JDate from '@/components/jeecg/JDate'
import AInput from 'ant-design-vue/es/input/Input'
import pick from 'lodash.pick'
import JSelectUserByDep from '@/components/jeecgbiz/JSelectUserByDep'

export default {
  name: 'DevopsOrderInfoAllocateForm',
  components: {
    AInput,
    JFormContainer,
    JDate,
    JSelectUserByDep,
  },
  props: {
    //流程表单data
    formData: {
      type: Object,
      default: () => {},
      required: false,
    },
    //表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false,
    },
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false,
    },
  },
  data() {
    return {
      replaceFields: { title: 'name' },
      expandedKeys: [],
      backupsExpandedKeys: [],
      autoExpandParent: false,
      checkedKeys: [],
      selectedKeys: [],
      searchValue: '',
      searchStr: '',
      multi: false,
      form: this.$form.createForm(this),
      model: {},

      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      confirmLoading: false,
      validatorRules: {
        handlerUserId: {
          rules: [{ required: true, message: '请选择责任人!' }],
        },
      },
      url: {
        add: '/orderinfo/devopsOrderInfo/add',
        edit: '/orderinfo/devopsOrderInfo/distributionEdit',
        queryById: '/orderinfo/devopsOrderInfo/queryById',
      },
    }
  },

  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    },
  },
  created() {
    //如果是流程中表单，则需要加载流程表单data
    this.showFlowData()
  },

  methods: {
    onSearch() {
      this.searchValue = this.searchStr
      if (this.searchValue === '') {
        this.expandedKeys = []
      } else {
        this.expandedKeys = []
        this.backupsExpandedKeys = []
        const candidateKeysList = this.getkeyList(this.searchValue, this.treeData, [])
        candidateKeysList.forEach((item) => {
          const key = this.getParentKey(item, this.treeData)
          // eslint-disable-next-line no-shadow
          if (key && !this.backupsExpandedKeys.some((item) => item === key)) this.backupsExpandedKeys.push(key)
        })
        const { length } = this.backupsExpandedKeys
        // eslint-disable-next-line no-plusplus
        for (let i = 0; i < length; i++) {
          this.getAllParentKey(this.backupsExpandedKeys[i], this.treeData)
        }
        this.expandedKeys = this.backupsExpandedKeys.slice()
      }
    },
    // 获取节点中含有value的所有key集合
    getkeyList(value, tree, keyList) {
      // eslint-disable-next-line no-plusplus
      for (let i = 0; i < tree.length; i++) {
        const node = tree[i]
        if (node.name.indexOf(value) > -1) {
          keyList.push(node.key)
        }
        if (node.children) {
          this.getkeyList(value, node.children, keyList)
        }
      }
      return keyList
    },
    // 该递归主要用于获取key的父亲节点的key值
    getParentKey(key, tree) {
      let parentKey
      let temp
      // eslint-disable-next-line no-plusplus
      for (let i = 0; i < tree.length; i++) {
        const node = tree[i]
        if (node.children) {
          if (node.children.some((item) => item.key === key)) {
            parentKey = node.key
            // eslint-disable-next-line no-cond-assign
          } else if ((temp = this.getParentKey(key, node.children))) {
            parentKey = temp
          }
        }
      }
      return parentKey
    },
    // 获取该节点的所有祖先节点
    getAllParentKey(key, tree) {
      let parentKey
      if (key) {
        parentKey = this.getParentKey(key, tree)
        if (parentKey) {
          if (!this.backupsExpandedKeys.some((item) => item === parentKey)) {
            this.backupsExpandedKeys.push(parentKey)
          }
          this.getAllParentKey(parentKey, tree)
        }
      }
    },
    add() {
      this.edit({})
    },
    edit(record) {
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(
          pick(
            // this.model,
            'orderCategoryId',
            'orderDescription',
            'warningId',
            'confirmUserId',
            'orderState',
            'wamingCreateTime',
            'remarks',
            'allocTime',
            'responseSecond',
            'handlerResults',
            'handleEndTime',
            'handleSecond',
            'createBy',
            'createTime',
            'updateBy',
            'updateTime',
            'sysOrgCode'
          )
        )
      })
    },
    //渲染流程表单数据
    showFlowData() {
      if (this.formBpm === true) {
        let params = { id: this.formData.dataId }
        getAction(this.url.queryById, params).then((res) => {
          if (res.success) {
            this.edit(res.result)
          }
        })
      }
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let formData = Object.assign(this.model, values)
          formData.orderState = 1
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    popupCallback(row) {
      this.form.setFieldsValue(
        pick(
          row,
          'orderCategoryId',
          'warningId',
          'orderDescription',
          'confirmUserId',
          'orderState',
          'wamingCreateTime',
          'remarks',
          'handlerUserId',
          'allocTime',
          'responseSecond',
          'handlerResults',
          'handleEndTime',
          'handleSecond',
          'createBy',
          'createTime',
          'updateBy',
          'updateTime',
          'sysOrgCode'
        )
      )
    },
  },
}
</script>
<style lang="less" scoped>
::v-deep .two-words > div > label {
  letter-spacing: 4px;
}
::v-deep .two-words > div > label::after {
  letter-spacing: 0px;
}
</style>
