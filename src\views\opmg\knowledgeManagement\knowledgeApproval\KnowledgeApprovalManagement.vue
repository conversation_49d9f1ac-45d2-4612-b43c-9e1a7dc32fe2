<template>
  <div style="height:100%">
    <keep-alive exclude='knowledgeBaseInfo'>
      <component style="height:100%" :is="pageName" :data="data" :approval-view='true'/>
    </keep-alive>
  </div>
</template>
<script>
import knowledgeBaseList from './KnowledgeApprovalList.vue'
import knowledgeBaseInfo from '@views/opmg/knowledgeManagement/knowledgeBase/KnowledgeBaseInfo.vue'
export default {
  name: "KnowledgeApprovalManagement",
  data() {
    return {
      isActive: 0,
      data: {},
    };
  },
  components: {
    knowledgeBaseList,
    knowledgeBaseInfo
  },
  created() {
    this.pButton1(0);
  },
  //使用计算属性
  computed: {
    pageName() {
      switch (this.isActive) {
        case 0:
          return "knowledgeBaseList";
        default:
          return "knowledgeBaseInfo";
      }
    }
  },
  methods: {
    pButton1(index) {
      this.isActive = index;
    },
    pButton2(index, item) {
      this.isActive = index;
      this.data = item
    }
  }
}
</script>