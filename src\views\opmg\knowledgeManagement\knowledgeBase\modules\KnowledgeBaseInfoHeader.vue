<template>
  <div :class='[showOpmgKInfo?"opmg-know-info-header-wrapper":"one-click-help-header-wrapper",showShare&&device==="mobile"?"mobile-opmg-know-info-header-wrapper":""]'>
    <div class='title-wrapper' >
      <div class='header-title' :class='[showShare?"share-title-header":""]'>
        <p class='p-info-title'>
          <span class='span-title' :title='kInfo.title'>{{ kInfo.title }}</span>
          <span class='span-icon'>
             <knowledge-icon title='知识类型' class='knowledge-type' :knowledgeType="kInfo.knowledgeType" :label1="'文本知识'" :label2="'文档知识'"></knowledge-icon>
             <span class='span-status' title='能见度'>
             <a-icon :style='{color:kInfo.isPrivate===visibility[1].value?"#FE9400":"#4BD863"}' :type='kInfo.isPrivate===visibility[1].value?"lock":"global"'/>
                 {{kInfo.isPrivate===visibility[1].value?visibility[1].label:visibility[0].label}}
          </span>
             <span class='span-status' title='浏览量'>
             <a-icon :theme='"filled"' style='color:#4BD863;font-size:16px' type='eye'/>
             {{kInfo.pageViewCount}}
          </span>
          </span>
        </p>
      </div>
      <div class='header-back' v-if='showOpmgKInfo&&!showShare'>
        <img alt='' src='~@assets/return1.png' style='width: 20px; height: 20px; cursor: pointer' @click='getGo' />
      </div>
    </div>
    <a-row :gutter='24' class='info-btn' :class='[showShare?"share-info-btn":""]'>
      <a-col :lg='approvalView||showShare?24:14' :md='approvalView||showShare?24:14' :sm='24' :xl='approvalView||showShare?22:16' :xxl='approvalView||showShare?24:18'>
        <p class='p-info-product'>
          <span class='span-assets'>主题：{{ kInfo.topicName }}</span>
          <span class='span-assets'>创建人员：{{ kInfo.createBy }}</span>
          <span class='span-assets'>创建时间：{{ kInfo.createTime }}</span>
          <span class='span-assets'>更新时间：{{ kInfo.updateTime }}</span>
        </p>
      </a-col>
      <a-col v-if='!approvalView&&!showShare' :lg='10' :md='10' :sm='24' :xl='8' :xxl='6'>
        <div class='btn'>
          <!--                <a-input-group compact class='input-group'>
                             <a-button v-if='exportType==="word"' class='action-btn' @click='exportFile'>导出</a-button>
          &lt;!&ndash;                   <a-button v-else-if='exportType==="pdf"' class='action-btn' v-print="'#printContent'" @click='exportPdfFile'>导出</a-button>&ndash;&gt;
                             <a-button v-else-if='exportType==="pdf"' class='action-btn' @click='exportFile'>导出</a-button>
                              <a-select label-in-value :default-value="{ key: 'word' }" @change='changeExportType'>
                                <a-select-option value="word">word</a-select-option>
                                <a-select-option value="pdf">pdf</a-select-option>
                              </a-select>
                          </a-input-group>-->
          <a-button class='action-btn' @click='handleCollection'>
            <a-icon :theme="colStatus ==1 ? 'filled' : 'outlined'" class='btn-icon' type='star'/>
            收藏
          </a-button>
          <a-button class='action-btn' @click='handleShare'>
            <a-icon class='btn-icon' type='share-alt'/>
            分享
          </a-button>
          <img v-if='!showOpmgKInfo' src="/oneClickHelp/back.png" style='margin-left: 14px' width='32' height='32' @click='getGo'>
        </div>
      </a-col>
    </a-row>
  </div>
</template>
<script>
import knowledgeIcon from '@views/opmg/knowledgeManagement/knowledgeBase/modules/KnowledgeIcon.vue'
import {visibility} from '@views/opmg/knowledgeManagement/knowledgeBase/modules/dataListAndFunc'
import { mixinDevice } from '@/utils/mixin.js'
export default {
  name: "",
  mixins: [mixinDevice],
  components: { knowledgeIcon },
  props: {
    kInfo: {
      type: Object,
      required: false,
      default: {},
    },
    //收藏状态
    colStatus: {
      type: [String,Number],
      required: true
    },
    /**若是通过知识审批列表打卡查看，
     收藏、分享、打印、评论、关联、点赞、点踩都不可操作性，
     附件统统可以下载，同时告诉管理员，知识创建者设置的允许下载附件状态*/
    approvalView: {
      type: Boolean,
      required: false,
      default: false
    },
    /*用于区分运维中心(分享、知识审批、知识库的详情样式一样)和运维助手知识详情页面，采用不同的样式,默认采用运维中心知识详情样式*/
    showOpmgKInfo: {
      type: Boolean,
      required: false,
      default: true
    },
    /*区分是否是分享页面，默认不是分享页面*/
    showShare: {
      type: Boolean,
      required: false,
      default: false
    },
  },
  data() {
    return {
      visibility:visibility,
    }
  },
  methods:{
    handleCollection(){
      this.$emit('handleCollection')
    },
    handleShare(){
      this.$emit('handleShare')
    },
    //返回上一级
    getGo() {
      this.$emit('getGo')
    },
    /* changeExportType(value){
       this.exportType=value.key
     },
     exportFile(){
       switch (this.exportType){
         case 'word':
           this.exportWordFile()
           break;
         case 'pdf':
           this.exportPdfFile()
           break;
       }
     },
     exportWordFile() {
       getAction(this.url.wordExport,{id:this.kInfo.id}).then((res) => {
         if (!res.data) {
           this.$message.warning('文件下载失败')
           return
         }
         //存在中文需要解码
         let filename=decodeURIComponent(res.headers['content-disposition'].split(';')[1].split('=')[1])
         let typeString='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
         if (typeof window.navigator.msSaveBlob !== 'undefined') {
           window.navigator.msSaveBlob(new Blob([res.data], { type: typeString}),filename)
         }
         else {
           if(res.headers['content-type']===typeString){
             /!*const blob = new Blob([res.data], { type: typeString });
            // 使用FileSaver.js保存Blob对象为.doc文件
             FileSaver.saveAs(blob, `${filename}`);*!/

             let url = window.URL.createObjectURL(new Blob([res.data], { type: typeString }))
             let link = document.createElement('a')
             link.style.display = 'none'
             link.href = url
             link.setAttribute('download', `${filename}`)
             document.body.appendChild(link)
             link.click()
             document.body.removeChild(link) //下载完成移除元素
             window.URL.revokeObjectURL(url) //释放掉blob对象
           }
         }
       })
     },
     exportPdfFile(){
       getAction(this.url.pdfExport,{id:this.kInfo.id}).then((res) => {
         if(res.success){
           localStorage.setItem('knowledgeHtmlTitle', res.result.htmlTitle);
           localStorage.setItem('knowledgeHtmlString', res.result.htmlString);
           setTimeout(()=>{
             window.open(location.origin + "#/knowledgeManagement/knowledgeBase/knowledgePdf",'_blank')
           },500)

           /!*  const doc = new jsPDF();
            // doc.html(res.result.htmlString, {x:10, y:10}); // 在PDF中添加文本内容
            doc.html(res.result.htmlString, {callback: function (pdf) {
                // 保存PDF文件
                pdf.save(res.result.htmlTitle+".pdf");
              },
              }); // 在PDF中添加文本内容*!/

           // 生成Blob对象
           /!* const pdfBlob = doc.output('blob');

            // 创建Blob URL
            const blobURL = URL.createObjectURL(pdfBlob);

            // 创建一个隐藏的链接并设置下载属性
            const link = document.createElement('a');
            link.href = blobURL;
            link.download =res.result.htmlTitle+ '.pdf'; // 设置文件名
            link.style.display = 'none';

            // 将链接添加到DOM，并模拟点击下载
            document.body.appendChild(link);
            link.click();

            // 移除链接
            document.body.removeChild(link);*!/
         }
       })
     },*/
  }
}
</script>

<style scoped lang="less">
//视口宽度最大为768px时，改变导出、收藏、分享按钮对齐方式
@media (max-width: 768px){
  .opmg-know-info-header-wrapper{
    .info-btn{
      .btn{
        margin-top: 10px !important;
        justify-content: start !important;
        .input-group{
          text-align: left !important;
          white-space: nowrap !important;
        }
      }
    }
  }
}
//知识详情页面第一部分（顶部）样式
.opmg-know-info-header-wrapper {
  border-radius: 3px;
  padding: 24px;
  background: #fff;
  margin-bottom: 16px;

  .header-back {
    text-align: right;
    display: inline-block;
    width: 30px
  }

  .title-wrapper{
    display: flex;
    justify-content: space-between;
    align-items: start;
    flex-flow: row nowrap ;

    //非分享页面
    .header-title{
      display: inline-block;
      width:calc(100% - 30px)
    }
    //分享页面
    .share-title-header{
      width:100% !important;
    }
    .p-info-title {
      display: flex;
      flex-flow: row wrap;
      justify-content: start;
      align-items: center;
      margin-bottom:6px;
      font-size:18px;
      color: #000000;
      width: 100%;

      .span-title{
        max-width: 100%;
        display: inline-block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-right:12px;
      }
      .span-icon{
        display: flex;
        flex-flow: row wrap;
        justify-content: start;
        align-items: center;
        font-size:14px;
        color: rgba(0, 0, 0, 0.65);
        .knowledge-type{}
        .span-status {
          margin-left:12px;
        }
        .span-status img {
          width: 6px;
          height: 6px;
        }
      }
    }
  }

  .info-btn{
    display: flex !important;
    flex-direction: row;
    align-content: flex-end;
    justify-content: space-between;
    flex-wrap: wrap;
    align-items: flex-end;

    .p-info-product {
      margin-bottom: 0px;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.65);
      display: flex;
      justify-content:start;
      align-items: start;
      flex-flow: row wrap;

      .span-assets {
        margin-right: 50px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }

    .btn{
      display: flex;
      flex-flow: nowrap row;
      justify-content: end;
      align-items: end;
      margin-top: 0px;

      .input-group{
        width:132px;
        text-align: right;
        white-space: nowrap
      }
      .action-btn:not(:first-child){
        margin-left: 10px
      }
      .action-btn:hover,.action-btn:active,.action-btn:focus{
        color: #409eff;
        background: #ecf5ff !important;
        border-color: #b3d8ff !important;
      }

      .btn-icon{
        color:#409eff
      }
    }
  }
  .share-info-btn{
    justify-content: start !important;
  }
  //word、pdf导出按钮样式
  ::v-deep .ant-select {
    .ant-select-arrow {
      color: #409eff;
    }
  }
  ::v-deep .ant-select .ant-select-selection:hover,
  ::v-deep .ant-select .ant-select-selection:active,
  ::v-deep .ant-select .ant-select-selection:focus,
  ::v-deep .ant-select-focused .ant-select-selection,
  ::v-deep .ant-select-open .ant-select-selection{
    color: #409eff;
    background: #ecf5ff !important;
    border-color: #b3d8ff !important;
    box-shadow: none;
  }
}
.mobile-opmg-know-info-header-wrapper {
  border-radius: 3px;
  padding: 0.3rem;
  background: #fff;
  margin-bottom: 0.2rem;

  .header-back {
    text-align: right;
    display: inline-block;
    width: 0.375rem;//30/80
  }

  .title-wrapper{
    .header-title{
      width:100% !important;
      .p-info-title {
        margin-bottom:0.075rem;//6px
        font-size:0.225rem;// 18px
        color: #000000;

        .span-title{
          margin-right:0.15rem;// 12px/80
        }
        .span-icon{
          font-size:0.175rem;// 14px
          .knowledge-type{}
          .span-status {
            margin-left:0.15rem;// 12px/80
          }
          .span-status img {
            width: 0.075rem;//6px
            height: 0.075rem;//6px
          }
        }
      }
    }
  }
  .info-btn{
    .p-info-product {
      font-size: 0.175rem;

      .span-assets {
        margin-right: 0.625rem;//50px
      }
    }
  }

  //word、pdf导出按钮样式
  ::v-deep .ant-select {
    .ant-select-arrow {
      color: #409eff;
    }
  }
  ::v-deep .ant-select .ant-select-selection:hover,
  ::v-deep .ant-select .ant-select-selection:active,
  ::v-deep .ant-select .ant-select-selection:focus,
  ::v-deep .ant-select-focused .ant-select-selection,
  ::v-deep .ant-select-open .ant-select-selection{
    color: #409eff;
    background: #ecf5ff !important;
    border-color: #b3d8ff !important;
    box-shadow: none;
  }
}
.one-click-help-header-wrapper {
  padding: 0px 0px 24px 0px;

  .title-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: start;
    flex-flow: nowrap row;

    .header-title {
      display: inline-block;
      width: 100%;

      .p-info-title {
        display: flex;
        align-items: center;
        flex-flow: row wrap;
        justify-content: start;
        margin-bottom: 6px;
        font-family: PingFangSC-Medium;
        font-size: 18px;
        color: #000000;
        width: 100%;

        .span-title {
          max-width: 100%;
          font-size: 24px;
          color: #FFFFFF;
          letter-spacing: 0;
          font-weight: 500;
          display: inline-block;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          margin-right: 12px;
        }

        .span-icon {
          display: flex;
          flex-flow: row wrap;
          justify-content: start;
          align-items: center;
          font-size:14px;// 14px
          color: rgba(203, 203, 203, 0.77);

          .knowledge-type {}

          .span-status {
            margin-left: 12px;
            img {
              margin-right: 10px;
            }
          }
          .span-status img {
            width: 18px;
            height: 18px;
          }
        }
      }
    }
  }
  .info-btn{
    display: flex !important;
    flex-direction: row;
    align-content: flex-end;
    justify-content: space-between;
    flex-wrap: wrap;
    align-items: flex-end;

    .p-info-product {
      margin-bottom: 0px;
      font-size: 14px;
      color: #99A4AA;
      letter-spacing: 0.3px;
      font-weight: 400;

      display: flex;
      justify-content: start;
      align-items: start;
      flex-flow: row wrap;

      .span-assets {
        margin-right: 50px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }
    .btn {
      display: flex;
      flex-flow: nowrap row;
      justify-content: end;
      align-items: end;
      margin-top: 0px;

      .input-group {
        width: 132px;
        text-align: right;
        white-space: nowrap
      }

      .action-btn {
        height: 36px;
        background: rgba(64, 158, 255, 0.25);
        border: 1px solid #409EFF;
        border-radius: 4px;
        font-size: 14px;
        color: #FFFFFF;
        font-weight: 400;
        padding: 0 16px;

        &:not(:first-child) {
          margin-left: 14px
        }

        .btn-icon {
          margin-right: 3px;
        }
      }
    }
  }
}
</style>