<template>
  <a-row :gutter='10' style='height: 100%' class='vScroll'>
    <a-col style='width: 100%; height: 100%; display: flex; flex-direction: column'>
      <!-- 查询区域 -->
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <div class='table-page-search-wrapper-style'>
          <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
            <a-row :gutter='24' ref='row'>
              <a-col :span='spanValue'>
                <a-form-item label='下级单位'>
                  <a-tree-select placeholder='请选择下级单位' v-model='queryParam.childDepartCode' :allowClear='true'
                                 :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }"
                                 :treeData="parentTree" :replaceFields='{value:"departNameEn"}'></a-tree-select>
                </a-form-item>
              </a-col>
<!--              <a-col :span='spanValue'>-->
<!--                <a-form-item label='下级单位名称'>-->
<!--                  <a-input :maxLength='maxLength' placeholder='请输入下级单位名称' v-model='queryParam.childDepartName' :allowClear='true'-->
<!--                    autocomplete='off' />-->
<!--                </a-form-item>-->
<!--              </a-col>-->
<!--              <a-col :span='spanValue'>-->
<!--                <a-form-item label='下级单位标识'>-->
<!--                  <a-input :maxLength='maxLength' placeholder='请输入下级单位标识' v-model='queryParam.childDepartCode' :allowClear='true'-->
<!--                    autocomplete='off' />-->
<!--                </a-form-item>-->
<!--              </a-col>-->
<!--              <a-col :span='spanValue'>-->
<!--                <a-form-item label='下级单位所在地'>-->
<!--                  <a-input :maxLength='maxLength' placeholder='请输入下级单位所在地' v-model='queryParam.childLocation' :allowClear='true'-->
<!--                    autocomplete='off' />-->
<!--                </a-form-item>-->
<!--              </a-col>-->
              <a-col :span='colBtnsSpan()'>
                <span class='table-page-search-submitButtons'
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button type='primary' class='btn-search btn-search-style' @click='searchQuery'>查询</a-button>
                  <a-button class='btn-reset btn-reset-style' @click='searchReset'>重置</a-button>
                  <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <!-- 查询区域-END -->
      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <!-- table区域-begin -->
        <a-table ref='table' bordered rowKey='id' :columns='columns' :dataSource='dataSource'
          :scroll='dataSource.length>0?{x:"max-content"}:{}' :pagination='ipagination' :loading='loading'
          :rowSelection='{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }' @change='handleTableChange'>
          <span class="caozuo" slot='receiveCount' slot-scope='text, record'><a
              @click="countGo(record)">{{ text }}</a></span>
          <span class="caozuo" slot='receiveSuccessCount' slot-scope='text, record'>
            <a @click="successGo(record)">{{ text }}</a>
          </span>
          <span class='caozuo' slot='action' slot-scope='text, record'>
            <a @click='handleDetailPage(record)'>查看</a>
          </span>
          <template slot='tooltip' slot-scope='text'>
            <a-tooltip placement='topLeft' :title='text' trigger='hover'>
              <div class='tooltip'>
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
      </a-card>
    </a-col>
  </a-row>
</template>

<script>
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import {
    YqFormSearchLocation
  } from '@/mixins/YqFormSearchLocation'
  import { getAction } from '@api/manage'

  export default {
    name: 'reportingBtmList',
    mixins: [JeecgListMixin, YqFormSearchLocation],
    data() {
      return {
        // maxLength:50,
        description: '下级管理页面',
        formItemLayout: {
          labelCol: {
            style: 'width:115px'
          },
          wrapperCol: {
            style: 'width:calc(100% - 115px)'
          }
        },
        queryParam: {
          levelType: '0'
        },
        columns: [{
            title: '下级单位名称',
            dataIndex: 'childDepartName',
            customCell: () => {
              let cellStyle = 'text-align: center; min-width: 130px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '下级单位标识',
            dataIndex: 'childDepartCode',
            customCell: () => {
              let cellStyle = 'text-align: center; min-width: 130px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '下级平台标识',
            dataIndex: 'childPlatformCode',
            customCell: () => {
              let cellStyle = 'text-align: left; min-width: 150px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '下级单位所在地',
            dataIndex: 'childLocation',
            customCell: () => {
              let cellStyle = 'text-align: center; width: 180px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '接收该下级记录总数',
            dataIndex: 'receiveCount',
            customCell: () => {
              let cellStyle = 'text-align: center; min-width: 100px'
              return {
                style: cellStyle
              }
            },
            scopedSlots: {
              customRender: 'receiveCount'
            }
          },
          {
            title: '接收该下级记录成功数',
            dataIndex: 'receiveSuccessCount',
            customCell: () => {
              let cellStyle = 'text-align: center; width: 100px'
              return {
                style: cellStyle
              }
            },
            scopedSlots: {
              customRender: 'receiveSuccessCount'
            }
          },
          {
            title: '操作',
            dataIndex: 'action',
            customCell: () => {
              let cellStyle = 'text-align: center; width: 160px'
              return {
                style: cellStyle
              }
            },
            scopedSlots: {
              customRender: 'action'
            }
          }
        ],
        url: {
          list: '/dataReport/manage/list',
          delete: '/terminal/terminalDevice/delete',
          deleteBatch: '/terminal/terminalDevice/deleteBatch',
          departList: '/device/statis/queryMyDeptTreeList'
        },
        parentTree: [],
      }
    },
    created() {
      this.getDepart()
    },
    methods: {
      //获取单位数据
      getDepart() {
        getAction(this.url.departList).then((res) => {
          this.parentTree = res.result
        })
      },
      countGo(record) {
        this.$router.push({
          path: '/jkdjgl/reporting/logManage',
          query: {
            'logType': '1',
            'childDepartCode': record.childDepartCode,
          }
        })
      },
      successGo(record) {
        this.$router.push({
          path: '/jkdjgl/reporting/logManage',
          query: {
            'childDepartCode': record.childDepartCode,
            'logType': '1',
            'resultFlag': true,
          }
        })
      },
      searchReset() {
        this.queryParam = {
          levelType: '0'
        }
        this.loadData(1)
      },
    }
  }
</script>
<style lang='less' scoped>
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';
</style>