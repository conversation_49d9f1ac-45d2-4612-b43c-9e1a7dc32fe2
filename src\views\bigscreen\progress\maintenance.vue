<template>
  <div class="body_box">
    <div class="header">
      <div class="header-right">
        <div class="header-right-time">
          <span>选择日期:</span>
          <div class="time-range-span">
            <a-range-picker dropdownClassName="big-screen-range-picker" v-model="this.todayTime" size="small"
              @change="onChange" />
          </div>
        </div>
        <button class="left-top-top-right-time-search" @click="getData">
          <a> 查询 </a>
        </button>
        <button class="export" @click="exportPDF">
          <a> 导出 </a>
        </button>
      </div>
    </div>
    <div class="body big-screen-theme">
      <div class="top">
        <div class="top-left">
          <div class="top-left-top">
            <div class="title">
              <img src="@/assets/bigScreen/9.png" alt="" />
              <span>运维情况统计</span>
            </div>
          </div>
          <div class="top-left-body">
            <div class="top-left-body-table">
              <div v-if="maintenanceData.title && maintenanceData.title.length > 0"
                class="top-left-body-table-tableTitle">
                <span v-for="(item , index) in maintenanceData.title" :title="item" :key="'name_' + index">
                  {{ item }}
                </span>
              </div>
              <div class="top-left-body-table-table">
                <vue-seamless-scroll :data="maintenanceData.rows" :class-option="maintenance" class="seamless-warp">
                  <ul>
                    <li v-for="(row, index) in maintenanceData.rows" :key="index">
                      <span v-for="(column , idx) in row" :key="index + '_' + idx"> {{ column }} </span>
                    </li>
                  </ul>
                </vue-seamless-scroll>
              </div>
            </div>
          </div>
        </div>
        <div class="top-right">
          <div class="top-right-top">
            <div class="title">
              <img src="@/assets/bigScreen/9.png" alt="" />
              <span>使用频次统计</span>
            </div>
            <div class="top-right-top-body">
              <div class="top-right-top-body-line" id="frequencyUseLine"></div>
            </div>
          </div>
          <div class="top-right-bottom">
            <div class="title">
              <img src="@/assets/bigScreen/9.png" alt="" />
              <span>运维业务占比</span>
            </div>
            <div class="top-right-bottom-body">
              <div class="top-right-bottom-body-Pie" id="maintenancePic"></div>
            </div>
          </div>
        </div>
      </div>
      <div class="bottom">
        <div class="bottom-one">
          <div class="title">
            <img src="@/assets/bigScreen/9.png" alt="" />
            <span>设备告警TOP10</span>
          </div>
          <div class="bottom-one-body">
            <div class="bottom-one-body-Histogram" id="seriousWarningHistorgram"></div>
          </div>
        </div>
        <div class="bottom-two">
          <div class="title">
            <img src="@/assets/bigScreen/9.png" alt="" />
            <span>设备配置TOP10</span>
          </div>
          <div class="bottom-two-body">
            <div class="bottom-two-body-Histogram" id="resourcesConfigureHistorgram"></div>
          </div>
        </div>
        <div class="bottom-three">
          <div class="title">
            <img src="@/assets/bigScreen/9.png" alt="" />
            <span>资产配置TOP10</span>
          </div>
          <div class="bottom-three-body">
            <div class="bottom-three-body-Histogram" id="assetsConfigureHistorgram"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  import echarts from 'echarts'
  import moment from 'moment'
  import vueSeamlessScroll from 'vue-seamless-scroll'
  import {
    getAction
  } from '@/api/manage'

  export default {
    data() {
      return {
        url: {
          opsCountTable: '/data-analysis/ops/count/table',
          opsCountExport: '/data-analysis/ops/count/export',
          opsCountUse: '/data-analysis/ops/count/use',
          opsCountBiz: '/data-analysis/ops/count/biz',
          opsTopAlarm: '/data-analysis/ops/top/alarm',
          opsTopProduct: '/data-analysis/ops/top/product',
          opsTopAssets: '/data-analysis/ops/top/assets',
        },
        maintenanceData: [],
        time1: '',
        time2: '',
        todayTime: [],
      }
    },
    components: {
      vueSeamlessScroll,
    },
    computed: {
      maintenance() {
        return {
          step: 0.1, // 数值越大速度滚动越快
          limitMoveNum: 10, // 开始无缝滚动的数据量 this.dataList.length
          hoverStop: false, // 是否开启鼠标悬停stop
          direction: 1, // 0向下 1向上 2向左 3向右
          // openWatch: true, // 开启数据实时监控刷新dom
          singleHeight: 0, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
          // singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
          waitTime: 2, // 单步运动停止的时间(默认值1000ms)
        }
      },
    },
    created() {
      let startOfMonth = moment().startOf('month').format('YYYY-MM-DD')
      let today = moment().format('YYYY-MM-DD')
      this.time1 = startOfMonth
      this.time2 = today
      this.todayTime[0] = this.time1
      this.todayTime[1] = this.time2
    },
    mounted() {
      this.opsCountTable()
      this.opsCountUse()
      this.opsCountBiz()
      this.opsTopAlarm()
      this.opsTopProduct()
      this.opsTopAssets()
    },
    methods: {
      moment,
      getData() {
        this.opsCountTable()
        this.opsCountUse()
        this.opsCountBiz()
        this.opsTopAlarm()
        this.opsTopProduct()
        this.opsTopAssets()
      },
      // 告警轮播数据
      opsCountTable() {
        getAction(this.url.opsCountTable, {
          time1: this.time1,
          time2: this.time2
        }).then((res) => {
          if (res.code === 200) {
            this.maintenanceData = res.result
          }
        })
      },
      // 导出
      exportPDF() {
        getAction(this.url.opsCountExport, {
          time1: this.time1,
          time2: this.time2
        }).then((res) => {
          if (res.code === 200) {
            // window.location.href = `${window._CONFIG['downloadUrl']}/${res.result}`
            window.open(window._CONFIG['domianURL'] + '/sys/common/downloadFile/' + res.result)
            // window.open(window._CONFIG['downloadUrl'] + '/' + res.result)
          } else {
            this.$message.error(res.message)
          }
        })
      },

      onChange(dates, dateStrings) {
        this.todayTime = dateStrings
        this.time1 = dateStrings[0]
        this.time2 = dateStrings[1]
      },

      exportXlsx() {
        window.location.href =
          `${window._CONFIG['domianURL']}/${this.url.opsCountExport}?time1=${this.time1}&time2=${this.time2}`
      },

      // 使用频次折线图数据
      opsCountUse() {
        getAction(this.url.opsCountUse, {
          time1: this.time1,
          time2: this.time2
        }).then((res) => {
          if (res.code === 200) {
            this.useCount(res.result)
          }
        })
      },

      // 使用频次折线图
      useCount(data) {
        let xArr = []
        let yArr = []
        data.forEach((e) => {
          xArr.push(e.name)
          yArr.push(e.value)
        })
        let myChart = this.$echarts.init(document.getElementById('frequencyUseLine'))
        myChart.setOption({
          tooltip: {
            trigger: 'axis',
            transitionDuration: 0, //echart防止tooltip的抖动
          },
          xAxis: [{
            type: 'category',
            boundaryGap: false,
            data: xArr,
            axisLabel: {
              show: true,
              textStyle: {
                color: '#5189ba', //更改坐标轴文字颜色
              },
            },
          }, ],
          yAxis: [{
            type: 'value',
            axisLabel: {
              show: true,
              textStyle: {
                color: '#5189ba', //更改坐标轴文字颜色
              },
            },
            axisLine: {
              show: false,
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: ['#1c2a37'],
                width: 2,
                type: 'solid',
              },
            },
          }, ],
          grid: {
            top: 40,
            right: 40,
            bottom: 70,
            left: 40,
          },
          series: [{
            name: '使用频次',
            type: 'line',
            areaStyle: {},
            emphasis: {
              focus: 'series',
            },
            data: yArr,
            itemStyle: {
              normal: {
                areaStyle: {
                  type: 'default',
                  color: new echarts.graphic.LinearGradient(
                    0,
                    0,
                    0,
                    1, //变化度
                    //两种种由深及浅的颜色
                    [{
                        offset: 0,
                        color: '#187da9',
                      },
                      {
                        offset: 1,
                        color: '#14323f',
                      },
                    ]
                  ),
                },
                color: '#05ccf5', //改变折线点的颜色
              },
            },
            lineStyle: {
              color: '#04bbdb', //改变折线颜色
            },
          }, ],
        })
        window.addEventListener("resize", () => {
          myChart.resize();
        });
      },

      // 运维业务数据
      opsCountBiz() {
        getAction(this.url.opsCountBiz, {
          time1: this.time1,
          time2: this.time2
        }).then((res) => {
          if (res.code === 200) {
            this.bizCount(res.result)
          }
        })
      },

      // 运维业务饼状图
      bizCount(data) {
        let color = [];
        for (const item of data) {
          color.push(item.color)
        }
        let myChart = this.$echarts.init(document.getElementById('maintenancePic'))
        myChart.setOption({
          tooltip: {
            show: true,
            transitionDuration: 0, //echart防止tooltip的抖动
            // formatter: '{a}<br/>{b}:{c}({d}%)',
            formatter: '{b}: {c}次 ({d}%)',
          },
          label: {
            formatter(data) {
              return data.name + ':' + data.percent + '%'
            },
            color: '#fff',
          },
          legend: {
            type: 'scroll',
            pageIconColor: '#aaa', // 翻页按钮的颜色
            pageIconInactiveColor: '#2f4554', // 翻页按钮不激活时（即翻页到头时）的颜色
            pageTextStyle: { // 图例页信息的文字样式
              color: '#cbcbcb'
            },
            orient: 'horizontal',
            bottom: '8',
            left: '8',
            icon: 'circle',
            textStyle: {
              color: '#fff',
            },
          },
          color: color,
          series: [{
            hoverAnimation: false, // 取消掉饼图鼠标移上去时自动放大
            name: '',
            type: 'pie',
            radius: '50%',
            center: ["50%", "45%"],
            data: data,
          }, ],
        })
        window.addEventListener("resize", () => {
          myChart.resize();
        });
      },

      // 严重告警数据
      opsTopAlarm() {
        getAction(this.url.opsTopAlarm, {
          time1: this.time1,
          time2: this.time2
        }).then((res) => {
          if (res.code === 200) {
            this.seriousWarning(res.result)
          }
        })
      },

      // 严重告警柱状图
      seriousWarning(data) {
        let xArr = data.xAxis
        let yArr = []
        let color = data.color
        data.yAxis.forEach((e) => {
          let yAxis = {
            name: e.name,
            data: e.values,
            type: 'bar',
            barWidth: 10, //柱图宽度
            itemStyle: {
              normal: {
                barBorderRadius: [15, 15, 0, 0],
                label: {
                  show: false, //开启显示
                },
              },
            },
          }
          yArr.push(yAxis)
        })
        let myChart = this.$echarts.init(document.getElementById('seriousWarningHistorgram'))
        myChart.setOption({
          tooltip: {
            show: true,
            transitionDuration: 0, //echart防止tooltip的抖动
          },
          xAxis: {
            type: 'category',
            splitLine: {
              show: false
            }, //去除网格线
            show: true,
            data: xArr,
            axisLabel: {
              interval: 0
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: '#4f92bf',
              },
            },
          },
          yAxis: {
            type: 'value',
            splitLine: {
              show: true,
              lineStyle: {
                color: '#1e2e3b',
              },
            },
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false, //y轴线消失
              lineStyle: {
                //y轴字体颜色
                color: '#4f92bf',
              },
            },
          },

          grid: {
            top: 20,
            left: 30, // 调整这个属性
            right: 10,
            bottom: 30,
          },
          color: color,
          series: yArr
        })
        window.addEventListener("resize", () => {
          myChart.resize();
        });
      },

      // 资源配置数据
      opsTopProduct() {
        getAction(this.url.opsTopProduct, {
          time1: this.time1,
          time2: this.time2
        }).then((res) => {
          if (res.code === 200) {
            this.resourcesConfigure(res.result)
          }
        })
      },

      // 资源配置柱状图
      resourcesConfigure(data) {
        let xArr = []
        let yArr = []
        data.forEach((e) => {
          xArr.push(e.name)
          yArr.push(e.value)
        })
        let myChart = this.$echarts.init(document.getElementById('resourcesConfigureHistorgram'))
        myChart.setOption({
          tooltip: {
            show: true,
            transitionDuration: 0, //echart防止tooltip的抖动
          },
          xAxis: {
            type: 'category',
            splitLine: {
              show: false
            }, //去除网格线
            show: true,
            data: xArr,
            axisLabel: {
              interval: 0
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: '#4f92bf',
              },
            },
          },
          yAxis: {
            type: 'value',
            splitLine: {
              show: true,
              lineStyle: {
                color: '#1e2e3b',
              },
            },
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false, //y轴线消失
              lineStyle: {
                //y轴字体颜色
                color: '#4f92bf',
              },
            },
          },

          grid: {
            top: 20,
            left: 30, // 调整这个属性
            right: 10,
            bottom: 30,
          },
          series: [{
            data: yArr,
            type: 'bar',
            barWidth: 10, //柱图宽度
            itemStyle: {
              normal: {
                barBorderRadius: [15, 15, 0, 0],
                color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                    offset: 0,
                    color: '#3679fb',
                  },
                  {
                    offset: 1,
                    color: '#0cf6f7',
                  },
                ]),
                label: {
                  show: false, //开启显示
                },
              },
            },
          }, ],
        })
        window.addEventListener("resize", () => {
          myChart.resize();
        });
      },

      // 资产配置数据
      opsTopAssets() {
        getAction(this.url.opsTopAssets, {
          time1: this.time1,
          time2: this.time2
        }).then((res) => {
          if (res.code === 200) {
            this.assetsConfigure(res.result)
          }
        })
      },

      // 资产配置柱状图
      assetsConfigure(data) {
        let xArr = []
        let yArr = []
        data.forEach((e) => {
          xArr.push(e.name)
          yArr.push(e.value)
        })
        let myChart = this.$echarts.init(document.getElementById('assetsConfigureHistorgram'))
        myChart.setOption({
          tooltip: {
            show: true,
            transitionDuration: 0, //echart防止tooltip的抖动
          },
          xAxis: {
            type: 'category',
            splitLine: {
              show: false
            }, //去除网格线
            show: true,
            data: xArr,
            axisLabel: {
              interval: 0
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: '#4f92bf',
              },
            },
          },
          yAxis: {
            type: 'value',
            splitLine: {
              show: true,
              lineStyle: {
                color: '#1e2e3b',
              },
            },
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false, //y轴线消失
              lineStyle: {
                //y轴字体颜色
                color: '#4f92bf',
              },
            },
          },

          grid: {
            top: 20,
            left: 30, // 调整这个属性
            right: 10,
            bottom: 30,
          },
          series: [{
            data: yArr,
            type: 'bar',
            barWidth: 10, //柱图宽度
            itemStyle: {
              normal: {
                barBorderRadius: [15, 15, 0, 0],
                color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                    offset: 0,
                    color: '#3679fb',
                  },
                  {
                    offset: 1,
                    color: '#0cf6f7',
                  },
                ]),
                label: {
                  show: false, //开启显示
                },
              },
            },
          }, ],
        })
        window.addEventListener("resize", () => {
          myChart.resize();
        });
      },
    },
  }
</script>
<style lang="less" scoped>
  ::v-deep .ant-calendar-picker {
    width: 3.95rem
      /* 316/80 */
    ;
    height: 100%;

    .ant-calendar-picker-input.ant-input {
      background-color: #101217;
      color: #909090;
      height: 100%;
      display: flex;
      align-items: center;

      .ant-calendar-range-picker-separator {
        color: #feffff;
        line-height: 0.375rem
          /* 30/80 */
        ;
      }
    }
  }

  .body_box {
    width: 100%;
    height: 100%;
    padding: 0 0.2rem 0.1rem 0.2rem;
    display: flex;
    flex-direction: column;

    .header {
      width: 100%;
      height: 7%;
      display: flex;
      justify-content: flex-end;

      .header-right {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        margin-right: 0.65rem
          /* 52/80 */
        ;

        .header-right-time {
          font-size: 0.175rem
            /* 14/80 */
          ;
          font-family: PingFang SC;
          letter-spacing: 0px;
          font-weight: 100;
          color: #ffffff;
          display: flex;
          align-items: center;

          .time-range-span {
            margin-right: 0.4375rem
              /* 35/80 */
            ;
            margin-left: 0.2rem
              /* 16/80 */
            ;
          }
        }

        .left-top-top-right-time-search {
          width: 0.85rem
            /* 68/80 */
          ;
          height: 0.4rem
            /* 32/80 */
          ;
          background: none;
          border: 2px solid #8f9094;
          border-radius: 10%;
          margin-right: 0.25rem
            /* 20/80 */
        }

        a {
          color: #fff;
        }

        .export {
          width: 0.85rem
            /* 68/80 */
          ;
          height: 0.4rem
            /* 32/80 */
          ;
          background: #1187d1;
          border: 0px;
          border-radius: 10%;
        }
      }
    }
  }

  .title {
    height: 16%;
    display: flex;
    align-items: center;
    font-size: 0.225rem
      /* 18/80 */
    ;
    color: #45c5e0;
    padding-left: 0.15rem
      /* 12/80 */
    ;
    letter-spacing: 0.025rem
      /* 2/80 */
    ;

    img {
      width: 0.125rem
        /* 10/80 */
      ;
      height: 0.1625rem
        /* 13/80 */
      ;
      margin-right: 0.0875rem
        /* 7/80 */
      ;
    }
  }

  .body {
    width: 100%;
    height: 100%;
    padding: 0
      /* 20/80 */
      0.2rem 0.1rem 0.2rem;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .top {
      width: 100%;
      height: 68%;
      display: flex;
      justify-content: space-between;

      .top-left {
        width: 72.5%;
        background: #111217;
        border-radius: 0.075rem
          /* 6/80 */
        ;

        .top-left-top {
          width: 100%;
          height: 12%;
          display: flex;
          justify-content: space-between;

          .title {
            height: 100%;
            display: flex;
            align-items: center;
            font-size: 0.225rem
              /* 18/80 */
            ;
            color: #45c5e0;
            padding-left: 12px;
            letter-spacing: 4px;

            img {
              width: 10px;
              height: 13px;
              margin-right: 7px;
            }
          }

          .top-left-top-right {
            width: 48%;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            margin-right: 0.65rem
              /* 52/80 */
            ;

            .top-left-top-right-time {
              font-size: 0.175rem
                /* 14/80 */
              ;
              font-family: PingFang SC;
              letter-spacing: 0px;
              font-weight: 100;
              color: #ffffff;
              display: flex;
              align-items: center;

              .time-range-span {
                margin-right: 0.4375rem
                  /* 35/80 */
                ;
                margin-left: 0.2rem
                  /* 16/80 */
                ;
              }
            }

            .top-left-top-right-time-search {
              width: 0.85rem
                /* 68/80 */
              ;
              height: 0.4rem
                /* 32/80 */
              ;
              background: none;
              border: 2px solid #8f9094;
              border-radius: 10%;
              margin-right: 0.25rem
                /* 20/80 */
              ;

              a {
                color: #fff;
              }
            }

            .export {
              width: 0.85rem
                /* 68/80 */
              ;
              height: 0.4rem
                /* 32/80 */
              ;
              background: #1187d1;
              border: 0px;
              border-radius: 10%;

              a {
                color: #fff;
              }
            }
          }
        }

        .top-left-body {
          width: 100%;
          height: 88%;
          display: flex;
          justify-content: center;

          .top-left-body-table {
            width: 92%;
            height: 100%;
            overflow: hidden;

            .top-left-body-table-tableTitle {
              display: flex;
              height: 38px;
              align-items: center;
              color: #fff;
              background: #38393b;

              span {
                font-size: 16px;
                width: 100%;
                text-align: center;
                border-left: 1px solid #111217;
                /*显示省略号*/
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
              }

              span:nth-child(1) {
                border: none;
              }
            }

            .top-left-body-table-table {
              width: 100%;
              height: calc(100% - 38px);
              overflow: hidden;

              .seamless-warp {
                ul {
                  width: 100%;
                  height: 100%;
                  padding: 0;
                  margin: 0;
                  display: flex;
                  flex-direction: column;
                  justify-content: space-around;

                  li {
                    text-align: center;
                    height: 0.65rem
                      /* 52/80 */
                    ;
                    width: 100%;
                    line-height: 0.65rem
                      /* 52/80 */
                    ;
                    display: flex;
                    justify-content: space-around;
                    text-align: center;
                    font-size: 14px;
                    background: #111217;

                    span {
                      width: 100%;
                      color: rgba(255, 255, 255, 0.75);
                      border-right: 1px solid #2d2d37;
                      border-bottom: 1px solid #24242e;
                    }

                    span:nth-child(1) {
                      border-left: 1px solid #2d2d37;
                    }

                    span:nth-child(3) {
                      color: #0784d6;
                    }

                    span:nth-child(4) {
                      color: #08dfe1;
                    }

                    span:nth-child(5) {
                      color: #0ac540;
                    }

                    span:nth-child(6) {
                      color: #ffe303;
                    }
                  }

                  li:nth-child(2n + 0) {
                    background: #26272c;
                    text-align: center;
                  }
                }
              }
            }
          }
        }
      }

      .top-right {
        width: 27%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .top-right-top {
          height: 49%;
          width: 100%;
          background: #111217;
          border-radius: 0.075rem
            /* 6/80 */
          ;

          .top-right-top-body {
            height: 84%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;

            .top-right-top-body-line {
              width: 100%;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }
        }

        .top-right-bottom {
          height: 49%;
          width: 100%;
          background: #111217;
          border-radius: 0.075rem
            /* 6/80 */
          ;

          .top-right-bottom-body {
            width: 100%;
            height: 84%;
            display: flex;
            // align-items: center;
            justify-content: center;

            .top-right-bottom-body-Pie {
              width: 100%;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }
        }
      }
    }

    .bottom {
      width: 100%;
      height: 31%;
      display: flex;
      justify-content: space-between;

      .bottom-one {
        width: 32.5%;
        height: 100%;
        background: #111217;
        border-radius: 0.075rem
          /* 6/80 */
        ;

        .bottom-one-body {
          width: 100%;
          height: 86%;
          display: flex;
          align-items: center;
          justify-content: center;

          .bottom-one-body-Histogram {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }

      .bottom-two {
        width: 32.5%;
        height: 100%;
        background: #111217;
        border-radius: 0.075rem
          /* 6/80 */
        ;

        .bottom-two-body {
          width: 100%;
          height: 86%;
          display: flex;
          align-items: center;
          justify-content: center;

          .bottom-two-body-Histogram {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }

      .bottom-three {
        width: 32.5%;
        height: 100%;
        background: #111217;
        border-radius: 0.075rem
          /* 6/80 */
        ;

        .bottom-three-body {
          width: 100%;
          height: 86%;
          display: flex;
          align-items: center;
          justify-content: center;

          .bottom-three-body-Histogram {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }
    }
  }
</style>