<template>
  <a-row>
    <a-card style="height: 100%">
      <div class="colorBox">
        <span class="colorTotal">提审信息</span>
      </div>
      <a-col :span="24" style="float: none;">
        <table class="gridtable">
          <tr>
            <td class="leftTd" style="font-size:14px">终端名称：</td>
            <td class="rightTd" style="font-size:14px">{{ data.terminalName }}</td>
            <td class="leftTd" style="font-size:14px">终端IP：</td>
            <td class="rightTd" style="font-size:14px">{{ data.ip }}</td>
          </tr>
          <tr>
            <td class="leftTd" style="font-size:14px">MAC地址：</td>
            <td class="rightTd" style="font-size:14px">{{ data.macAddr }}</td>
            <td class="leftTd" style="font-size:14px">状态：</td>
            <td class="rightTd" style="font-size:14px">{{ data.status === 'approving'? '审批中': data.status === 'refuse'?'已驳回':'已通过'}}</td>
          </tr>
          <tr>
            <td class="leftTd" style="font-size:14px">单位：</td>
            <td class="rightTd" style="font-size:14px">{{ data.deptName }}</td>
            <td class="leftTd" style="font-size:14px">使用者：</td>
            <td class="rightTd" style="font-size:14px">{{ data.username }}</td>
          </tr>
          <tr>
            <td class="leftTd" style="font-size:14px">联系电话：</td>
            <td class="rightTd" style="font-size:14px">{{ data.phone }}</td>
            <td class="leftTd" style="font-size:14px">使用部门：</td>
            <td class="rightTd" style="font-size:14px">{{ data.userDepartment }}</td>
          </tr>
          <tr>
            <td class="leftTd" style="font-size:14px">管理人：</td>
            <td class="rightTd" style="font-size:14px">{{ data.administrator }}</td>
            <td class="leftTd" style="font-size:14px">管理部门：</td>
            <td class="rightTd" style="font-size:14px">{{ data.adminDepartment }}</td>
          </tr>
          <tr>
            <td class="leftTd" style="font-size:14px">地址：</td>
            <td class="rightTd" style="font-size:14px">{{ data.address }}</td>
            <td class="leftTd" style="font-size:14px">备注：</td>
            <td class="rightTd" style="font-size:14px">{{ data.remarks }}</td>
          </tr>
        </table>
      </a-col>
      <div class="colorBox" style="margin-top: 24px;">
        <span class="colorTotal">当前信息</span>
      </div>
      <a-col :span="24">
        <table class="gridtable">
          <tr>
            <td class="leftTd" style="font-size:14px">终端名称：</td>
            <td class="rightTd" style="font-size:14px">{{ info.name }}</td>
            <td class="leftTd" style="font-size:14px">终端IP：</td>
            <td class="rightTd" style="font-size:14px">{{ info.ip }}</td>
          </tr>
          <tr>
            <td class="leftTd" style="font-size:14px">MAC地址：</td>
            <td class="rightTd" style="font-size:14px">{{ info.macAddr }}</td>
            <td class="leftTd" style="font-size:14px">状态：</td>
            <td class="rightTd" style="font-size:14px">{{ info.enable === 1 ? '已激活':'未激活'}}</td>
          </tr>
          <tr>
            <td class="leftTd" style="font-size:14px">单位：</td>
            <td class="rightTd" style="font-size:14px">{{ info.deptName }}</td>
            <td class="leftTd" style="font-size:14px">使用者：</td>
            <td class="rightTd" style="font-size:14px">{{ info.username }}</td>
          </tr>
          <tr>
            <td class="leftTd" style="font-size:14px">联系电话：</td>
            <td class="rightTd" style="font-size:14px">{{ info.phone }}</td>
            <td class="leftTd" style="font-size:14px">使用部门：</td>
            <td class="rightTd" style="font-size:14px">{{ info.userDepartment }}</td>
          </tr>
          <tr>
            <td class="leftTd" style="font-size:14px">管理人：</td>
            <td class="rightTd" style="font-size:14px">{{ info.administrator }}</td>
            <td class="leftTd" style="font-size:14px">管理部门：</td>
            <td class="rightTd" style="font-size:14px">{{ info.adminDepartment }}</td>
          </tr>
          <tr>
            <td class="leftTd" style="font-size:14px">地址：</td>
            <td class="rightTd" style="font-size:14px">{{ info.address }}</td>
            <td class="leftTd" style="font-size:14px">备注：</td>
            <td class="rightTd" style="font-size:14px">{{ info.remarks }}</td>
          </tr>
        </table>
      </a-col>
    </a-card>
  </a-row>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
export default {
  name: 'TerminalAuditInfo',
  data() {
    return {
      url: {
        queryTerminalInfo: '/terminal/terminalDevice/queryById'
      },
      info: Object
    }
  },
  props: {
    data: {
      type: Object
    }
  },
  created() {
    this.getInfo()
  }, 
  watch:{
    data:{
      handler(nv,ov){
        if(nv.status === 'pass'){
          this.getInfo()
        }
      },
      deep:true
    }
  },
  methods: {
    getInfo() {
      let params = { id: this.data.terminalId }
      getAction(this.url.queryTerminalInfo, params).then(res => {
        if (res.success) {
          this.info = res.result
        }
      })
    }
  }
}
</script>

<style lang="less" scoped="scoped">
.colorBox {
  margin-bottom: 20px;
}
.colorTotal {
  padding-left: 7px;
  border-left: 4px solid #1e3674;
}
table.gridtable {
  font-family: verdana, arial, sans-serif;
  font-size: 11px;
  color: #606266;
  border-width: 1px;
  border-color: #e8e8e8;
  border-collapse: collapse;
  text-align: left;
  width: 100%;
  table-layout: fixed;
}
table.gridtable td {
  border-width: 1px;
  border-style: solid;
  border-color: #e8e8e8;
  word-wrap: break-word;
}
.leftTd {
  width: 17%;
  background-color: #fafafa;
  padding: 16px 24px;
  text-align: center;
}
.rightTd {
  width: 35%;
  padding: 16px 24px;
}
</style>