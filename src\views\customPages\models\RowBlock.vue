<template>
  <a-row v-if='record'
         :gutter='[record.gutter,record.gutterBottom]'
         :justify='record.justify'
         type='flex'
         @click.native.stop='blockClick(record.id)'
         :style="rowStyle"
         :class='{"row-selected":record.id===blockId}'
    >
    <a-empty v-if='record.cols.length === 0'>
      <span slot='description' style='color: rgb(42, 161, 152);'> 请添加列 </span>
    </a-empty>
    <col-block
      v-else
      v-for='item in record.cols'
      :key='item.id'
      :record='item'
      :edit='edit'
      :addedComponents='addedComponents'
      :height='record.height?Number(record.height):0'
      @setComponent='setComponent'>
    </col-block>
  </a-row>
</template>

<script>
import ColBlock from '@views/customPages/models/ColBlock.vue'

export default {
  name: 'RowBlock',
  components: { ColBlock },
  props: {
    record: {
      type: Object,
      default: () => {
        return null;
      }
    },
    blockId:{
      type:String,
      default:"",
    },
    edit:{
      type:Boolean,
      default:false,
    },
    //已添加的组件
    addedComponents:{
      type:Array,
      default:()=>{
        return [];
      } 
    }
  },
  data() {
    return {
      colRender:false,
    }
  },
  created() {
  },
  mounted() {
  },
  beforeDestroy() {
  },
  computed: {
    rowStyle() {
      return {
        margin:`0px`,
        // minHeight: `${this.record.height}px`,
        width: `100%`,
        backgroundColor: this.record.cols.length === 0?`#fff`:"transparent",
        border: this.edit?`1px solid rgb(204, 204, 204)`:"",
        // marginBottom:`${this.record.gutterBottom}px`,
        // overflow:"auto",
      }
    },
  },
  methods: {
    blockClick(recordId){
      if(!this.edit)return;
      this.$emit("setBlockId",recordId)
    },
    setComponent(item,col){
      this.$emit("setComponent", item,col)
    }
  }
}
</script>

<style scoped lang='less'>
//.row-empty{
//   display: flex;
//   align-items: center;
//   justify-content: center;
//   overflow: hidden;
//}
.row-selected{
  box-shadow: blue 0px 0px 8px;
}
/deep/.ant-empty{
  align-items: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
  width: 100%;
}
</style>