<template>
  <div>
    <v-chart :forceFit="true" :height="height" :data="data" :scale="scale">
      <v-coord type="polar" :startAngle="-202.5" :endAngle="22.5" :radius="0.75"></v-coord>
      <v-axis dataKey="value" :zIndex="2" :line="null" :label="axisLabel" :subTickCount="4"
        :subTickLine="axisSubTickLine" :tickLine="axisTickLine" :grid="null"></v-axis>
      <v-axis dataKey="1" :show="false"></v-axis>
      <v-series gemo="point" position="value*1" shape="pointer" color="#1890FF" :active="false"></v-series>
      <v-guide type="arc" :zIndex="0" :top="false" :start="arcGuide1Start" :end="arcGuide1End"
        :vStyle="arcGuide1Style"></v-guide>
      <v-guide type="arc" :zIndex="1" :start="arcGuide2Start" :end="arcGuide2End" :vStyle="arcGuide2Style"></v-guide>
      <v-guide type="html" :position="htmlGuidePosition" :html="htmlGuideHtml"></v-guide>
    </v-chart>
  </div>
</template>

<script>
import { registerShape } from 'viser-vue';

registerShape('point', 'pointer', {
  draw(cfg, container) {
    let point = cfg.points[0];
    point.x = point.x / 10
    point = this.parsePoint(point);
    const center = this.parsePoint({
      x: 0,
      y: 0,
    });
    // container.addShape('line', {
    //   attrs: {
    //     x1: center.x,
    //     y1: center.y,
    //     x2: point.x,
    //     y2: point.y,
    //     stroke: cfg.color,
    //     lineWidth: 3,
    //     lineCap: 'round',
    //   }
    // });
    // return container.addShape('circle', {
    //   attrs: {
    //     x: center.x,
    //     y: center.y,
    //     r: 9.75,
    //     stroke: cfg.color,
    //     lineWidth:2,
    //     fill: '#fff',
    //   }
    // });
  }
});

const scale = [{
  dataKey: 'value',
  min: 0,
  max: 10,
  tickInterval: 1,
  nice: false,
}];

/*  const data = [
    { value: 10 },
  ];*/

export default {
  props: {
    dataSource: {
      type: Array,
      default: () => []
    },
  },
  data() {
    return {
      height: 260,
      data: [],
      scale: scale,
      axisLabel: {
        offset: -10,
        textStyle: {
          fontSize: 12,
          textAlign: 'center',
          textBaseline: 'middle'
        }
      },
      axisSubTickLine: {
        length: -8,
        stroke: '#fff',
        strokeOpacity: 1,
      },
      axisTickLine: {
        length: -17,
        stroke: '#fff',
        strokeOpacity: 1,
      },
      arcGuide1Start: [0, 0.945],
      arcGuide1End: [10, 0.945],
      arcGuide1Style: {
        stroke: '#CBCBCB',
        lineWidth: 10,
      },
      arcGuide2Start: [0, 0.945],
      arcGuide2End: [],
      arcGuide2Style: {
        stroke: '#1890FF',
        lineWidth: 10,
      },
      htmlGuidePosition: ['50%', '100%'],
      htmlGuideHtml: ``,
    };
  },
  mounted() {
    this.updateChart(this.dataSource);
  },
  watch: {
    dataSource: {
      handler(newVal) {
        this.updateChart(newVal);
      },
      deep: true
    }
  },
  // ...existing code...
  methods: {
    updateChart(dataSource) {
      this.data = this.dataSource
      this.arcGuide2End = [this.data[0].value / 10, 0.945];
      this.htmlGuideHtml = `
        <div style="width: 300px;text-align: center;">
        </div>
      `;
    }
  }
};
// <p style="font-size: 14px;color: #545454;margin: 0;">使用率</p>
// <p style="font-size: 14px;color: #545454;margin: 0;">${this.data[0].value}%</p>
</script>
