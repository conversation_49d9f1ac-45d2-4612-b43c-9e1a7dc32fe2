<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll zxw">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <!-- 查询区域 -->
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="类型名称">
                  <a-input placeholder="请输入类型名称" :maxLength="maxLength" :allowClear="true" autocomplete="off" v-model="queryParam.categoryName">
                  </a-input>
                </a-form-item>
              </a-col>

              <a-col class="form-col" :span="spanValue">
                <a-form-item label="类型属性">
                  <a-select placeholder="请选择类型属性" :getPopupContainer="(node) => node.parentNode" :allowClear="true"
                    v-model="queryParam.isMonitorable">
                    <a-select-option v-for="item in statuslist" :key="item.code" :value="item.code">{{
                      item.name
                    }}</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="colBtnsSpan()">
                <span class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button type="primary" @click="searchQuery" class="btn-search-style">查询</a-button>
                  <a-button @click="searchReset" style="margin-left: 10px" class="btn-reset-style">重置</a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <!-- 查询区域-END -->

      <!-- 操作按钮区域 -->
      <a-card :bordered="false" style="flex: auto">
        <div class="table-operator">
          <a-button @click="handleAdd" v-has="'assetsCategory:add'">新增</a-button>
          <a-button @click="handleExportXls('资产类型')" v-has="'assetsCategory:export'">导出</a-button>
          <a-button @click="handleTemplateXls()" @mouseover="mouseOver" v-has="'assetsCategory:import'">下载模版</a-button>
          <a-upload v-has="'assetsCategory:import'" name="file" :showUploadList="false" :multiple="false"
            :headers="tokenHeader" :action="importExcelUrl" @change="handleImportExcel">
            <a-button>导入</a-button>
          </a-upload>
          <a-dropdown v-if="selectedRowKeys.length > 0" v-has="'assetsCategory:delete'">
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item key="1" @click="batchDel">删除 </a-menu-item>
            </a-menu>
            <a-button>
              批量操作
              <a-icon type="down" />
            </a-button>
          </a-dropdown>
        </div>

        <!-- table区域-begin -->
        <div>
          <a-table bordered ref="table" rowKey="id" :columns="columns" :dataSource="dataSource"
            :scroll='dataSource.length>0?{x:"max-content"}:{}' :pagination="ipagination" :loading="loading"
            :expandedRowKeys="expandedRowKeys" @change="handleTableChange" @expand="handleExpand" v-bind="tableProps">
            <template slot="htmlSlot" slot-scope="text">
              <div v-html="text"></div>
            </template>
            <template slot="imgSlot" slot-scope="text">
              <span v-if="!text" style="font-size: 14px; font-style: italic">无图片</span>
              <img v-else :src="getImgView(text)" height="25px" alt=""
                style="max-width: 80px; font-size: 14px; font-style: italic" />
            </template>

            <template slot="categoryState" slot-scope="text">
              <span v-if="text == 1" style="font-size: 14px; color: red">未启用</span>
              <span v-if="text == 0" style="font-size: 14px; color: green">启用</span>
            </template>

            <template slot="isMonitorable" slot-scope="text">
              <span v-if="text == 1">可监控</span>
              <span v-if="text == 0">不可监控</span>
            </template>

            <template slot="fileSlot" slot-scope="text">
              <span v-if="!text" style="font-size: 14px; font-style: italic">无文件</span>
              <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
                下载
              </a-button>
            </template>

            <span slot="action" slot-scope="text, record">
              <span>
                <a @click="handleDetailPage(record)">查看</a>
              </span>
              <span v-has="'assetsCategory:edit'">
                <a-divider type="vertical" />
                <a @click="handleEdit(record)">编辑</a>
              </span>
              <span v-has="'assetsCategory:delete'">
                <a-divider type="vertical" />
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDeleteNode(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </span>
            </span>
            <template slot="tooltip" slot-scope="text">
              <a-tooltip placement="topLeft" :title="text" trigger="hover">
                <div class="tooltip">
                  {{ text }}
                </div>
              </a-tooltip>
            </template>
          </a-table>
        </div>
        <cmdb-assets-category1-modal ref="modalForm" @ok="modalFormOk"></cmdb-assets-category1-modal>
        <!-- 下载模版 -->
        <iframe id="download" style="display: none" v-if="isDown == 1"></iframe>
      </a-card>
    </a-col>
  </a-row>
</template>

<script>
  import {
    getAction,
    deleteAction
  } from '@/api/manage'
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import CmdbAssetsCategory1Modal from './modules/CmdbAssetsCategory1Modal'
  import {
    filterMultiDictText
  } from '@/components/dict/JDictSelectUtil'
  import {
    filterObj
  } from '@/utils/util'
  import JSuperQuery from '@/components/jeecg/JSuperQuery.vue'
  import {
    YqFormSearchLocation
  } from '@/mixins/YqFormSearchLocation'

  export default {
    name: 'CmdbAssetsCategory1List',
    mixins: [JeecgListMixin, YqFormSearchLocation],
    props: {
      tableParams: {
        type: Object,
      },
    },
    components: {
      JSuperQuery,
      CmdbAssetsCategory1Modal,
    },
    data() {
      return {
        maxLength:50,
        disableMixinCreated:true,
        paginaData: {},
        isDown: 1,
        formItemLayout: {
          labelCol: {
            style: 'width:80px',
          },
          wrapperCol: {
            style: 'width:calc(100% - 80px)'
          }
        },
        description: '资产类型管理页面',
        // 表头
        columns: [{
            title: '类型名称',
            dataIndex: 'categoryName',
            customCell: () => {
            let cellStyle = 'text-align: left'
            return {
              style: cellStyle
            }
          }
          },
          {
            title: '类型编号',
            dataIndex: 'categoryCode'
          },
          {
            title: '描述',
            dataIndex: 'categoryDescribe',
            scopedSlots: {
              customRender: 'tooltip'
            },
            customCell: () => {
              let cellStyle = 'text-align: left;min-width: 100px;max-width:400px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '状态',
            dataIndex: 'categoryState',
            scopedSlots: {
              customRender: 'categoryState'
            }
          },
          {
            title: '类型属性',
            dataIndex: 'isMonitorable',
            scopedSlots: {
              customRender: 'isMonitorable'
            }
          },
          {
            title: '操作',
            dataIndex: 'action',
            fixed: 'right',
            align: 'center',
            width: 147,
            scopedSlots: {
              customRender: 'action'
            },
          },
        ],
        url: {
          list: '/category/cmdbAssetsCategory1/list',
          delete: '/category/cmdbAssetsCategory1/delete',
          deleteBatch: '/category/cmdbAssetsCategory1/deleteBatch',
          exportXlsUrl: '/category/cmdbAssetsCategory1/exportXls',
          importExcelUrl: 'category/cmdbAssetsCategory1/importExcel',
          findAllByPid: 'category/cmdbAssetsCategory1/findAllByPid',
          childList: '/assetscategory/assetsCategory/getChildList',
          getChildListBatch: '/assetscategory/assetsCategory/getChildListBatch',
          downloadUserTemplateUrl: '/category/cmdbAssetsCategory1/downloadUserTemplate',
          downloadTemplateXlsUrl: '/category/cmdbAssetsCategory1/downloadTemplate',
        },
        expandedRowKeys: [],
        hasChildrenField: 'hasChild',
        pidField: 'parentId',
        dictOptions: {},
        loadParent: false,
        superFieldList: [],
        statuslist: [{
            name: '可监控',
            code: '1',
          },
          {
            name: '不可监控',
            code: '0',
          },
        ],
      }
    },
    created() {
      if (this.data != null) {
        this.paginaData = this.data
      }
      this.getSuperFieldList()
    },
    computed: {
      importExcelUrl() {
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
      },
      downloadTemplateXlsUrl: function () {
        this.isDown == 1
        return `${window._CONFIG['domianURL']}/${this.url.downloadTemplateXlsUrl}`
      },
      tableProps() {
        let _this = this
        return {
          // 列表项是否可选择
          rowSelection: {
            selectedRowKeys: _this.selectedRowKeys,
            onChange: (selectedRowKeys) => (_this.selectedRowKeys = selectedRowKeys),
          }
        }
      }
    },
    activated() {
      this.loadData()
    },
    methods: {
      handleDetailPage: function (record) {
        this.isDown = 2
        this.$parent.pButton2(1,record)
      },
      mouseOver() {
        this.isDown = 1
      },
      //excel模板
      handleTemplateXls() {
        if (this.isDown = 1) {
          const path = this.downloadTemplateXlsUrl
          document.getElementById('download').src = path
        }
      },
      loadData(arg) {
        if (arg == 1) {
          this.ipagination.current = 1
        }
        // this.loading = true
        let params = this.getQueryParams()
        if (this.paginaData != null && this.paginaData.current != null && this.paginaData.pageSize) {
          this.ipagination.current = this.paginaData.current
          this.ipagination.pageSize = this.paginaData.pageSize
          params.pageNo = this.paginaData.current
          params.pageSize = this.paginaData.pageSize
        }
        params.hasQuery = 'true'
        getAction(this.url.list, params)
          .then((res) => {
            if (res.success) {
              let result = res.result
              if (Number(result.total) > 0) {
                this.ipagination.total = Number(result.total)
                this.dataSource = this.getDataByResult(res.result.records)
                return this.loadDataByExpandedRows(this.dataSource)
              } else {
                this.ipagination.total = 0
                this.dataSource = []
              }
            } else {
              this.$message.warning(res.message)
            }
          })
          .finally(() => {
            this.loading = false
          })
      },
      // 根据已展开的行查询数据（用于保存后刷新时异步加载子级的数据）
      loadDataByExpandedRows(dataList) {
        if (this.expandedRowKeys.length > 0) {
          return getAction(this.url.getChildListBatch, {
            parentIds: this.expandedRowKeys.join(',')
          }).then((res) => {
            if (res.success && res.result.records != []) {
              //已展开的数据批量子节点
              let records = res.result.records
              const listMap = new Map()
              for (let item of records) {
                let pid = item[this.pidField]
                if (this.expandedRowKeys.join(',').includes(pid)) {
                  let mapList = listMap.get(pid)
                  if (mapList == null) {
                    mapList = []
                  }
                  mapList.push(item)
                  listMap.set(pid, mapList)
                }
              }
              let childrenMap = listMap
              let fn = (list) => {
                if (list) {
                  list.forEach((data) => {
                    if (this.expandedRowKeys.includes(data.id)) {
                      data.children = this.getDataByResult(childrenMap.get(data.id))
                      fn(data.children)
                    }
                  })
                }
              }
              fn(dataList)
            }
          })
        } else {
          return Promise.resolve()
        }
      },
      getQueryParams(arg) {
        //获取查询条件
        let sqp = {}
        let param = {}
        if (this.superQueryParams) {
          sqp['superQueryParams'] = encodeURI(this.superQueryParams)
          sqp['superQueryMatchType'] = this.superQueryMatchType
        }
        if (arg) {
          param = Object.assign(sqp, this.isorter, this.filters)
        } else {
          param = Object.assign(sqp, this.queryParam, this.isorter, this.filters)
        }
        if (JSON.stringify(this.queryParam) === '{}' || arg) {
          param.hasQuery = 'false'
        } else {
          param.hasQuery = 'true'
        }
        param.field = this.getQueryField()
        param.pageNo = this.ipagination.current
        param.pageSize = this.ipagination.pageSize
        return filterObj(param)
      },
      searchReset() {
        //重置
        this.expandedRowKeys = []
        this.queryParam = {}
        this.loadData(1)
      },
      getDataByResult(result) {
        if (result) {
          return result.map((item) => {
            //判断是否标记了带有子节点
            if (item[this.hasChildrenField] == '1') {
              let loadChild = {
                id: item.id + '_loadChild',
                name: 'loading...',
                isLoading: true
              }
              item.children = [loadChild]
            }
            return item
          })
        }
      },
      handleExpand(expanded, record) {
        // 判断是否是展开状态
        if (expanded) {
          this.expandedRowKeys.push(record.id)
          if (record.children.length > 0 && record.children[0].isLoading === true) {
            let params = this.getQueryParams(1) //查询条件
            params[this.pidField] = record.id
            params.hasQuery = 'false'
            params.superQueryParams = ''
            getAction(this.url.childList, params).then((res) => {
              if (res.success) {
                if (res.result) {
                  record.children = this.getDataByResult(res.result)
                  this.dataSource = [...this.dataSource]
                } else {
                  record.children = ''
                  record.hasChildrenField = '0'
                }
              } else {
                this.$message.warning(res.message)
              }
            })
          }
        } else {
          let keyIndex = this.expandedRowKeys.indexOf(record.id)
          if (keyIndex >= 0) {
            this.expandedRowKeys.splice(keyIndex, 1)
          }
        }
      },
      handleAddChild(record) {
        this.loadParent = true
        let obj = {}
        obj[this.pidField] = record['id']
        this.$refs.modalForm.add(obj)
      },
      handleDeleteNode(id) {
        if (!this.url.delete) {
          this.$message.error('请设置url.delete属性!')
          return
        }
        var that = this
        deleteAction(that.url.delete, {
          id: id
        }).then((res) => {
          if (res.success) {
            this.searchReset()
          } else {
            that.$message.warning(res.message)
          }
        })
      },
      getSuperFieldList() {
        let fieldList = []
        fieldList.push({
          type: 'string',
          value: 'categoryName',
          text: '类型名称',
          dictCode: ''
        })
        fieldList.push({
          type: 'string',
          value: 'categoryCode',
          text: '类型编号',
          dictCode: ''
        })
        fieldList.push({
          type: 'string',
          value: 'categoryDescribe',
          text: '描述',
          dictCode: ''
        })
        fieldList.push({
          type: 'string',
          value: 'categorySerial',
          text: '序号',
          dictCode: ''
        })
        fieldList.push({
          type: 'string',
          value: 'categoryState',
          text: '状态（0启用;1未启用）',
          dictCode: ''
        })
        fieldList.push({
          type: 'int',
          value: 'isMonitorable',
          text: '状态（0不监控;1监控）',
          dictCode: ''
        })
        fieldList.push({
          type: 'string',
          value: 'parentId',
          text: '父级节点',
          dictCode: ''
        })
        this.superFieldList = fieldList
      },
    },
  }
</script>
<style lang="less" scoped>
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';
</style>