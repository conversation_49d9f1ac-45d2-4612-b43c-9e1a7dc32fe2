<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll zxw">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <!-- 查询区域 -->
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <div class="table-page-search-wrapper-style">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="名称">
                  <a-input
                    placeholder="请输入名称"
                    :allowClear="true"
                    autocomplete="off"
                    v-model="queryParam.commandName"
                    :maxLength="maxLength"
                  ></a-input>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="命令">
                  <a-input
                    placeholder="请输入命令"
                    :allowClear="true"
                    autocomplete="off"
                    v-model="queryParam.commandCode"
                    :maxLength="maxLength"
                  ></a-input>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="状态">
                  <a-select :getPopupContainer='(target) => target.parentNode'
                            :allowClear='true'
                            v-model='queryParam.disable'
                            placeholder='请选择状态'>
                    <a-select-option :key='0' value='0'>禁用</a-select-option>
                    <a-select-option :key='1' value='1'>启用</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="colBtnsSpan()">
                <span
                  class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                >
                  <a-button type="primary" @click="searchQuery" class="btn-search-style">查询</a-button>
                  <a-button @click="searchReset" style="margin-left: 10px" class="btn-reset-style">重置</a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <!-- 查询区域-END -->

      <a-card :bordered="false">
        <div class="table-operator">
          <a-button @click="handleAdd">新增</a-button>
          <a-dropdown v-if="selectedRowKeys.length > 0">
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item key="1" @click="batchDel">删除</a-menu-item>
            </a-menu>
            <a-button> 批量操作 <a-icon type="down" /></a-button>
          </a-dropdown>
        </div>

        <!-- table区域-begin -->
        <a-table
          ref="table"
          bordered
          rowKey="id"
          :columns="columns"
          :dataSource="dataSource"
          :pagination="ipagination"
          :loading="loading"
          :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          class="j-table-force-nowrap"
          @change="handleTableChange"
        >
          <template slot="htmlSlot" slot-scope="text">
            <div v-html="text"></div>
          </template>
          <template slot="imgSlot" slot-scope="text">
            <span v-if="!text" style="font-size: 14px">无图片</span>
            <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width: 80px; font-size: 14px" />
          </template>
          <template slot="fileSlot" slot-scope="text">
            <span v-if="!text" style="font-size: 14px">无文件</span>
            <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
              下载
            </a-button>
          </template>
          <template slot="disable" slot-scope="text">
            <span>{{text=='1'?'启用':'禁用' }}</span>
          </template>
          <template slot="tooltip" slot-scope="text">
            <a-tooltip placement="topLeft" :title="text" trigger="hover">
              <div class="tooltip">
                {{ text }}
              </div>
            </a-tooltip>
          </template>
          <span
            slot="action"
            slot-scope="text, record"
            class="caozuo"
            style="display: inline-block; white-space: nowrap; text-align: center"
          >
            <a @click="handleDetail(record)">查看</a>
            <a-divider type="vertical" />
            <a @click="handleEdit(record)">编辑</a>
            <a-divider type="vertical" />
            <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
              <a>删除</a>
            </a-popconfirm>
          </span>
        </a-table>
      </a-card>
      <sensitive-command-modal ref="modalForm" @ok="modalFormOk"></sensitive-command-modal>
    </a-col>
  </a-row>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import SensitiveCommandModal from './modules/SensitiveCommandModal.vue'
import JSuperQuery from '@/components/jeecg/JSuperQuery.vue'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'

export default {
  name: 'SensitiveCommandList',
  mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
  components: {
    SensitiveCommandModal,
    JSuperQuery,
  },
  data() {
    return {
      maxLength:50,
      formItemLayout: {
        labelCol: {
          style: 'width:50px',
        },
        wrapperCol: {
          style: 'width:calc(100% - 50px)',
        },
      },
      description: '软件管理表管理页面',
      // 表头
      columns: [
        {
          title: '名称',
          dataIndex: 'commandName',
        },
        {
          title: '命令',
          dataIndex: 'commandCode',
          scopedSlots: { customRender: 'tooltip' },
          customCell: () => {
            let cellStyle = 'text-align: left;max-width:300px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '状态',
          dataIndex: 'disable',
          scopedSlots: { customRender: 'disable' },
        },
        {
          title: '描述',
          dataIndex: 'description',
          scopedSlots: { customRender: 'tooltip' },
          customCell: () => {
            let cellStyle = 'text-align: left;max-width:300px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '创建时间',
          dataIndex: 'createTime'
        },
        {
          title: '操作',
          dataIndex: 'action',
          fixed: 'right',
          align: 'center',
          width: 147,
          scopedSlots: { customRender: 'action' },
        },
      ],
      url: {
        list: '/command/list',
        delete: '/command/delete',
        deleteBatch: '/command/deleteBatch'
      }
    }
  },
  created() {

  },
  mounted() {},
  computed: {
  },
  methods: {
  },
}
</script>
<style lang="less" scoped>
@import '~@assets/less/common.less';
</style>
