<template>
  <div style="margin-top:15px;">
    <!-- 查询区域 -->
    <div class='table-page-search-wrapper'>
      <a-form layout='inline'
        @keyup.enter.native='searchQuery'>
        <a-row :gutter='24'
          ref='row'>
          <a-col :span='spanValue'>
            <a-form-item label='标题'>
              <a-input placeholder='请输入标题'
                v-model='queryParam.title'
                :allowClear='true'
                autocomplete='off' :maxLength="maxLength"/>
            </a-form-item>
          </a-col>
          <a-col :span='colBtnsSpan()'>
            <span class='table-page-search-submitButtons'
              :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
              <a-button type='primary'
                class='btn-search btn-search-style'
                @click='searchQuery'>查询</a-button>
              <a-button class='btn-reset btn-reset-style'
                @click='searchReset'>重置</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <a-table ref="table"
      bordered
      rowKey="id"
      :columns="columns"
      :dataSource="dataSource"
      :pagination="ipagination"
      :loading="loading"
      class="j-table-force-nowrap"
      @change="handleTableChange">
      <span slot="action"
        slot-scope="text, record"
        class="caozuo"
        style="display: inline-block; white-space: nowrap; text-align: center">
        <a @click="handleDetailPage(record)">查看</a>
      </span>
      <template slot='tooltip'
        slot-scope='text'>
        <a-tooltip placement='topLeft'
          :title='text'
          trigger='hover'>
          <div class='tooltip'>
            {{ text }}
          </div>
        </a-tooltip>
      </template>
      <template slot='knowledgeType' slot-scope='text'>
        <knowledge-icon :knowledgeType="text"></knowledge-icon>
      </template>
    </a-table>
  </div>
</template>

<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
import knowledgeIcon from '@views/opmg/knowledgeManagement/knowledgeBase/modules/KnowledgeIcon.vue'
export default {
  name: 'myComments',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components: {
    knowledgeIcon
  },
  data() {
    return {
      maxLength:50,
      columns: [
        {
          title: '标题',
          dataIndex: 'title',
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'tooltip'
          }
        },
        {
          title: '主题名称',
          dataIndex: 'topicName',
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'tooltip'
          }
        },
        {
          title: '知识类型',
          dataIndex: 'knowledgeType',
          customCell: () => {
            let cellStyle = 'width:80px'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'knowledgeType'
          }
        },
        {
          title: '创建人员',
          dataIndex: 'createBy',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 80px;max-width:200px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '总评论(条)',
          dataIndex: 'commentCount',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 100px;max-width:300px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          customCell: () => {
            let cellStyle = 'text-align: center;width: 160px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '最近更新时间',
          dataIndex: 'updateTime',
          customCell: () => {
            let cellStyle = 'text-align: center;width: 160px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 150,
          scopedSlots: {
            customRender: 'action'
          }
        }
      ],
      url: {
        list: '/kbase/knowledges/usercomment',
      },
    }
  },
  activated() {
    console.log('激活我的评论')
    this.loadData()
  },
  methods: {
    // 查看
    handleDetailPage: function (record) {
      this.$emit('getRecord', record)
    }
  },
}

</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
</style>
