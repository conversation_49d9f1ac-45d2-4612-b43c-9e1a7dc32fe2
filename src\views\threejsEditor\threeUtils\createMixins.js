import * as THREE from 'three'
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js'
import modelJson from './model.json'
export const createMixins = {
  data() {
    return {
      wallMat: [],
      leftDoorMat: [],
      rightDoorMat: [],
      floor_material: null,
      door_left_material: null,
      door_right_material: null,
      window_material: null,
      fireBoxMat: [],
      fireBoxGMat: [],
      kongtiaoMat: [],
      kongtiaoBMat: [],
      electricMat: [],
      loader: null,
      gLoader: null
    }
  },
  created() {
    this.gLoader = new GLTFLoader().setPath('/threeImg/models/')
    this.loader = new THREE.TextureLoader()
    this.createDoorMaterial()
    this.createFloorMaterial()
    this.createWallMaterail()
    this.createFbMaterial()
    this.creatKgMaterial()
  },
  methods: {
    //获取颜色基础网格材质
    getColorMaterial(color) {
      return new THREE.MeshBasicMaterial({ color: color })
    },
    //获取加载图片基础网格材质
    getMeshBasicMaterial(img, config, repeat) {
      let loader = this.loader
      let texture = loader.load(this.imgBaseUrl + img)
      texture.minFilter = THREE.LinearFilter
      if (repeat) {
        texture.wrapS = texture.wrapT = THREE.RepeatWrapping
        texture.repeat.set(10, 10)
      }
      let materialConfig = {
        side: THREE.DoubleSide,
        transparent: true,
        map: texture
      }
      if (Object.prototype.toString.call(config) === '[object Object]') {
        Object.assign(materialConfig, config)
      }
      return new THREE.MeshBasicMaterial(materialConfig)
    },
    //获取精灵材质img
    getSpriteMaterial(img, color) {
      let loader = this.loader
      let texture = loader.load(this.imgBaseUrl + img)
      return new THREE.SpriteMaterial({
        map: texture,
        color: new THREE.Color(color) || 0xffffff
      })

    },
    //地板材质
    createFloorMaterial() {
      let floorimg = 'threeImg/floor.jpg'
      this.floor_material = this.getMeshBasicMaterial(floorimg, {}, true)
    },
    //门窗材质
    createDoorMaterial() {
      let emptyMat = new THREE.MeshBasicMaterial({
        transparent: true,
        opacity: 0,
        fog: false
      })
      let materialConfig = {
        opacity: 1.0,
        fog: false,
        side: THREE.DoubleSide
      }
      this.door_left_material = this.getMeshBasicMaterial('threeImg/door_left.png', materialConfig)
      this.door_right_material = this.getMeshBasicMaterial('threeImg/door_right.png', materialConfig)
      this.leftDoorMat = [
        emptyMat,
        emptyMat,
        emptyMat,
        emptyMat,
        this.door_left_material,
        this.door_right_material
      ]
      this.rightDoorMat = [
        emptyMat,
        emptyMat,
        emptyMat,
        emptyMat,
        this.door_right_material,
        this.door_left_material
      ]
      this.window_material = this.getMeshBasicMaterial('threeImg/window.png', materialConfig)
    },
    //灭火柜材质
    createFbMaterial() {
      this.fireBoxMat = [
        this.getColorMaterial(0x870205),
        this.getColorMaterial(0xEC1C24),
        this.getColorMaterial(0x870205),
        this.getColorMaterial(0xEC1C24),
        this.getMeshBasicMaterial('threeImg/firebox.png'),
        this.getColorMaterial(0xEC1C24)
      ]
      this.fireBoxGMat = [
        this.getColorMaterial(0xDEDEDC),
        this.getColorMaterial(0xDEDEDC),
        this.getColorMaterial(0xDEDEDC),
        this.getColorMaterial(0xDEDEDC),
        this.getMeshBasicMaterial('threeImg/fire-box.png'),
        this.getColorMaterial(0xDEDEDC)
      ]
    },
    //空调材质
    creatKgMaterial() {
      this.kongtiaoMat = [
        this.getColorMaterial(0xEEF5F9),
        this.getColorMaterial(0xEEF5F9),
        this.getColorMaterial(0xEEF5F9),
        this.getColorMaterial(0xEEF5F9),
        this.getMeshBasicMaterial('threeImg/kongtiao.jpg'),
        this.getColorMaterial(0xEEF5F9)
      ]
      this.kongtiaoBMat = [
        this.getColorMaterial(0x000000),
        this.getColorMaterial(0x000000),
        this.getColorMaterial(0x000000),
        this.getColorMaterial(0x000000),
        this.getMeshBasicMaterial('threeImg/kongtiaob.png'),
        this.getColorMaterial(0x000000)
      ]
      this.electricMat = [
        this.getColorMaterial(0xa1a192),
        this.getColorMaterial(0xa1a192),
        this.getColorMaterial(0xa1a192),
        this.getColorMaterial(0xa1a192),
        this.getMeshBasicMaterial('threeImg/electricBoxA.jpg'),
        this.getColorMaterial(0xa1a192)
      ]
    },
    //墙面材质
    createWallMaterail() {
      this.wallMat = [
        this.getColorMaterial(0xafc0ca),
        this.getColorMaterial(0xafc0ca),
        this.getColorMaterial(0xd6e4ec),
        this.getColorMaterial(0xd6e4ec),
        this.getColorMaterial(0xafc0ca),
        this.getColorMaterial(0xafc0ca)
      ]
    },
    //创建地面
    createFloor(obj) {
      var floorGeometry = new THREE.BoxGeometry(1, 1, 1)//width:2600 ,height:1400,depth:1,
      var floor = new THREE.Mesh(floorGeometry, this.floor_material)
      if (obj) {
        this.setModelInfo(obj, floor)

      } else {
        // floor.position.y = 10;
        // floor.rotation.x = Math.PI / 2;
        floor.scale.set(1200, 1, 1000)
        floor.name = 'yq_floor'
        floor.userData.name = '地板'
        return floor
      }


    },
    //创建墙对象
    createWallObject(obj) {
      var cubeGeometry = new THREE.BoxGeometry(1, 1, 1)//1000 ,100 , 10
      var cube = new THREE.Mesh(cubeGeometry, this.wallMat)
      if (obj) {
        this.setModelInfo(obj, cube)
      } else {
        // cube.position.x = x;
        cube.position.y = 60
        // cube.position.z = z;
        // cube.rotation.y += 0.5 * Math.PI;
        cube.scale.set(1000, 120, 10)
        cube.name = 'yq_wall'
        cube.userData = { name: '墙面' }
        return cube
      }
    },

    //创建左侧门
    createDoor_left(obj) {
      var doorgeometry = new THREE.BoxGeometry(1, 1, 1)
      var door = new THREE.Mesh(doorgeometry, this.leftDoorMat)
      if (obj) {
        this.setModelInfo(obj, door)
      } else {
        door.position.set(0, 50, 0)
        door.scale.set(90, 100, 2)
        door.rotation.y += 0 * Math.PI  //-逆时针旋转,+顺时针
        door.name = 'yq_door_left'
        door.userData.name = '门'
        return door
      }

    },

    //创建右侧门
    createDoor_right(obj) {
      var doorgeometry = new THREE.BoxGeometry(1, 1, 1)
      var door = new THREE.Mesh(doorgeometry, this.rightDoorMat)
      if (obj) {
        this.setModelInfo(obj, door)
      } else {
        door.position.set(0, 50, 0)
        door.scale.set(90, 100, 2)
        door.rotation.y += 0 * Math.PI
        door.name = 'yq_door_right'
        door.userData.name = '门'
        return door
      }

    },

    //创建窗户
    createWindow(obj) {
      var doorgeometry = new THREE.BoxGeometry(1, 1, 1)
      var windowMesh = new THREE.Mesh(doorgeometry, this.window_material)
      if (obj) {
        this.setModelInfo(obj, windowMesh)
      } else {
        windowMesh.position.set(0, 50, 0)
        windowMesh.scale.set(90, 100, 2)
        windowMesh.rotation.y += 0 * Math.PI
        windowMesh.name = 'yq_window'
        windowMesh.userData.name = '窗户'
        return windowMesh
      }

    },
    //创建灭火柜
    createFirebox(obj) {
      var doorgeometry = new THREE.BoxGeometry(1, 1, 1)
      var firebox = new THREE.Mesh(doorgeometry, this.fireBoxMat)
      if (obj) {
        this.setModelInfo(obj, firebox)
      } else {
        firebox.position.set(0, 60, 0)
        firebox.scale.set(80, 120, 30)
        firebox.rotation.y += 0 * Math.PI
        firebox.name = 'yq_firebox'
        firebox.userData.name = '灭火器'
        return firebox
      }

    },
    createFireboxG(obj) {
      var doorgeometry = new THREE.BoxGeometry(1, 1, 1)
      var firebox = new THREE.Mesh(doorgeometry, this.fireBoxGMat)
      if (obj) {
        this.setModelInfo(obj, firebox)
      } else {
        firebox.position.set(0, 80, 0)
        firebox.scale.set(80, 160, 50)
        firebox.rotation.y += 0 * Math.PI
        firebox.name = 'yq_firebox_g'
        firebox.userData.name = '灭火柜'
        return firebox
      }

    },
    // //创建空调
    createKongtiao(obj) {
      var ketGeo = new THREE.BoxGeometry(1, 1, 1)
      var ktMesh = new THREE.Mesh(ketGeo, this.kongtiaoMat)
      if (obj) {
        this.setModelInfo(obj, ktMesh)
      } else {
        ktMesh.position.set(0, 80, 0)
        ktMesh.scale.set(80, 160, 50)
        ktMesh.rotation.y += 0 * Math.PI
        ktMesh.name = 'yq_kongtiao'
        ktMesh.userData.name = '空调'
        return ktMesh
      }

    },
    //创建黑色空调
    createKongtiaoB(obj) {
      var ketGeo = new THREE.BoxGeometry(1, 1, 1)
      var ktMesh = new THREE.Mesh(ketGeo, this.kongtiaoBMat)
      if (obj) {
        this.setModelInfo(obj, ktMesh)
      } else {
        ktMesh.position.set(0, 80, 0)
        ktMesh.scale.set(80, 160, 50)
        ktMesh.rotation.y += 0 * Math.PI
        ktMesh.name = 'yq_kongtiao_b'
        ktMesh.userData.name = '空调_b'
        return ktMesh
      }

    },
    //创建电器柜
    createElectricBox(obj) {
      var ketGeo = new THREE.BoxGeometry(1, 1, 1)
      var ktMesh = new THREE.Mesh(ketGeo, this.electricMat)
      if (obj) {
        this.setModelInfo(obj, ktMesh)
      } else {
        ktMesh.position.set(0, 80, 0)
        ktMesh.scale.set(80, 160, 50)
        ktMesh.rotation.y += 0 * Math.PI
        ktMesh.name = 'yq_electric_box'
        ktMesh.userData.name = '电柜'
        return ktMesh
      }

    },
    setModelName(arr, id) {
      if (arr && arr.length > 0) {
        for (let i = 0; i < arr.length; i++) {
          arr[i].name = 'yq_' + arr[i].name
          arr[i].userData.groupId = id
          if (arr[i].children && arr[i].children.length > 0) {
            this.setModelName(arr[i].children, id)
          }
        }
      }
    },
    //创建烟感器
    createSmokeDetector(obj) {
      this.gLoader.load('yanganqi.glb', (gltf) => {
        let model = gltf.scene
        // console.log("烟感器 == ", model)
        model.name = 'yq_smoke_detector'
        this.setModelName(model.children, model.uuid)
        if (obj) {
          this.setModelInfo(obj, model)
        } else {
          model.userData.name = '烟感器'
          model.userData.glb = true;
          model.scale.x = 300
          model.scale.y = 300
          model.scale.z = 300
          scene.add(model)
          this.changeTransObj(model)
        }

      }, undefined, function(error) {
        console.error('加载烟感器模型失败', error)
      })
    },
    //创建烟感器
    createThermoHumidifier(obj) {
      this.gLoader.load('wenshidu.glb', (gltf) => {
        let model = gltf.scene
        model.name = 'yq_thermo_humidifier'
        this.setModelName(model.children, model.uuid)
        if (obj) {
          this.setModelInfo(obj, model)
        } else {
          model.userData.name = '温湿度'
          model.userData.glb = true;
          model.scale.x = 500
          model.scale.y = 500
          model.scale.z = 500
          scene.add(model)
          this.changeTransObj(model)
        }

      }, undefined, function(error) {
        console.error('加载温湿度模型失败', error)
      })
    },
    createWaterMonitor(obj) {
      this.gLoader.load('waterMonitor.glb', (gltf) => {
        let model = gltf.scene
        model.name = 'yq_water_monitor'
        this.setModelName(model.children, model.uuid)
        if (obj) {
          this.setModelInfo(obj, model)
        } else {
          model.userData.name = '积水监测'
          model.userData.glb = true;
          model.scale.x = 300
          model.scale.y = 300
          model.scale.z = 300
          scene.add(model)
          this.changeTransObj(model)
        }

      }, undefined, function(error) {
        console.error('加载温湿度模型失败', error)
      })
    },
    //初始化加载模型
    initModels(arr) {
      if (arr) {
        arr.forEach((el) => {
          this.createModels(el, el.modelCode)
        })
      }
    },
    //根据类型创建模型
    createModels(el, type) {
      let model = modelJson.find((item) => {
        return item.type === type
      })
      if(model) {
        this[model.create](el)
      }
    },
    //初始化加载模型数据
    setModelInfo(obj, model) {
      model.name = obj.name || obj.modelCode
      model.userData = obj.userData
      model.position.set(obj.position.x, obj.position.y, obj.position.z)
      model.scale.set(obj.scale.x, obj.scale.y, obj.scale.z)
      model.rotation.set(obj.rotation.x, obj.rotation.y, obj.rotation.z)
      // model.rotation.set(obj.rotation._x, obj.rotation._y, obj.rotation._z);
      scene.add(model)
    },
    initSceneModel(arr) {
      if (arr) {
        arr.forEach((el) => {
          this.createModels(el, el.name)
        })
      }
    },


    createCubeWall(width, height, depth, angle, material, x, y, z, name) {
      var cubeGeometry = new THREE.BoxGeometry(width, height, depth)
      var cube = new THREE.Mesh(cubeGeometry, material)
      cube.position.x = x
      cube.position.y = y
      cube.position.z = z
      cube.rotation.y += angle * Math.PI  //-逆时针旋转,+顺时针
      cube.name = name
      scene.add(cube)
    },


    returnDoorObject() {
      var cubeGeometry = new THREE.BoxGeometry(120, 80, 10)
      var cube = new THREE.Mesh(cubeGeometry, this.wallMat)
      // cube.position.x = x;
      cube.position.y = 40
      // cube.position.z = z;
      // cube.rotation.y += angle * Math.PI;
      cube.name = 'yq_door'
      return cube
    },
    //墙上挖门，通过两个几何体生成BSP对象
    createResultBsp(t) {//bsp, objects_cube
      let wall = this.createWallObject()
      let door = this.returnDoorObject()
      let BSP = new ThreeBSP(wall)
      let door_bsp = new ThreeBSP(door)
      BSP = BSP.subtract(door_bsp)
      // for (var i = 0; i < objects_cube.length; i++) {
      //     var less_bsp = new ThreeBSP(objects_cube[i]);
      //     BSP = BSP.subtract(less_bsp);
      // }
      let result = BSP.toMesh(this.wallMat)
      result.material.flatshading = THREE.FlatShading
      result.geometry.computeFaceNormals()  //重新计算几何体侧面法向量
      result.geometry.computeVertexNormals()
      result.material.needsUpdate = true  //更新纹理
      result.geometry.buffersNeedUpdate = true
      result.geometry.uvsNeedUpdate = true
      result.name = 'yq_wall_door'
      Object.assign(result.userData, t.userData)
      Object.assign(result.position, t.position)
      Object.assign(result.scale, t.scale)
      result.rotation.x = t.rotation.x
      result.rotation.y = t.rotation.y
      result.rotation.z = t.rotation.z
      return result
    },
    createResultBsp1(target) {//bsp, objects_cube
      let door = this.returnDoorObject()
      // Object.assign(door.scale,target.scale)
      Object.assign(door.position, target.position)
      door.rotation.x = target.rotation.x
      door.rotation.y = target.rotation.y
      door.rotation.z = target.rotation.z
      let BSP = new ThreeBSP(target)
      let door_bsp = new ThreeBSP(door)
      BSP = BSP.subtract(door_bsp)
      // for (var i = 0; i < objects_cube.length; i++) {
      //     var less_bsp = new ThreeBSP(objects_cube[i]);
      //     BSP = BSP.subtract(less_bsp);
      // }
      let result = BSP.toMesh(this.wallMat)
      result.material.flatshading = THREE.FlatShading
      result.geometry.computeFaceNormals()  //重新计算几何体侧面法向量
      result.geometry.computeVertexNormals()
      result.material.needsUpdate = true  //更新纹理
      result.geometry.buffersNeedUpdate = true
      result.geometry.uvsNeedUpdate = true
      result.name = 'yq_wall_door'
      Object.assign(result.scale, target.scale)
      //    Object.assign(result.position, target.position);
      //    result.rotation.x = target.rotation.x
      //   result.rotation.y = target.rotation.y
      //   result.rotation.z = target.rotation.z
      return result
    }
  }
}