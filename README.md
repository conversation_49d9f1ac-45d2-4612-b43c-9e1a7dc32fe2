npm# 问题记录  
## 1、大屏目录页分辨率显示错误  
需要需改 node_modules/lib-flexible/flexible.js 文件内 **function refreshRem()** （69行）
```js
function refreshRem(){
  var width = docEl.getBoundingClientRect().width;
  // 最小1366px，最大适配2560px
  if (width / dpr < 1366) {
    width = 1366 * dpr;
  } else if (width / dpr > 2560) {
    width = 2560 * dpr;
  }
  // 设置成192等份，设计稿时1920px的，这样1rem就是80px
  var rem = width / 24;
  docEl.style.fontSize = rem + 'px';
  flexible.rem = win.rem = rem;
}
```
**注意：** 由于每次重新拉取代码后都需要修改，现将flexible的实现迁移到 */utils/yq_flexible.js* 下，如后期涉及到flexible版本升级，注意同步更新到文件中。
## 2、视图管理 网络拓扑终端数量限制<1000报错问题
改动 node_modules/vue-virtual-scroller/dist/vue-virtual-scroller.esm.js 文件内itemsLimit
```js
var config = {
  itemsLimit: 100000
};
```
#各地市配置个性化定制
在public/config.js文件中添加各地市个性化定制内容
```js
 customization:{
  //遵义配置个性化定制
    cz_zunyi:{
      // 外网标识。通数据字典-终端内外网区分
      internetFlag: 1,
      // 外网网关code
      internetDataGateWayCode: ["gw-device-001", "gw-device-004"],
      // 内网访问外网grafana地址
      internetGrafanaURL: "http://**************:3000/d/YxYNY7M7x/show?orgId=1&kiosk&var-datasource=InfluxDB&var-host=",
      // 内网访问外网平台服务地址
      internetServiceURL: "http://**************:12380/insight-api/",
      //区县统计不显示的区县 中间用逗号隔开
      statisticsMunicipal:'市直',
    },
  //榆林临时配置个性化配置定制，用于数据中心首页（index.vue）设备总览显示
    cz_yulin:{
      tempData:{
           categoryName:"一期终端", 
           name:"一期终端",
           value:2300,
      }
    }
  //六盘水：变更部门管理经纬度
   cz_liupanshui:{
    departManage:{
      Longitude:'单机数',
        latitude:'专网机数'
    }
  },
}
```

## 运维助手地址
## http://localhost:3000/#/oneClickHelp/login?hostname=00:50:56:9a:2a:cc&cpuname=Loongson_5A0000&osname=KylinOS_V10AA

## 统计中心静态页面配置
1、 开启静态页面：在public/config.js配置 useStaticData: 1；
2、 静态页面配置：在public/statsCenter/mock/statsCenterMenus.json修改对应菜单的component字段值 
3、 页面数据配置：
    首页配置文件 public/statsCenter/mock/homeData.json
    评价分析配置文件 public/statsCenter/mock/evaluateData.json
    告警分析配置文件 public/statsCenter/mock/alarmData.json
    终端统计配置文件 public/statsCenter/mock/uniData.json 

## 统计中心标题和logo配置：
在public/config.js配置
    bigscreen: {
        bigScreenLogoUrl: '/configImg/logo.png', 
        bigScreenSysName: '智能运维监控平台'
    },