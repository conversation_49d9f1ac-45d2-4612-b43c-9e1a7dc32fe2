<template>
  <a-card :bordered="false">
    <div>
      <div class="colorBox">
        <span class="colorTotal">指标详情</span>
      </div>
      <div style="position: absolute; right: 24px; top: 18px; z-index: 10">
        <img src="~@assets/return1.png" @click="getGo" style="width: 20px; height: 20px; cursor: pointer" />
      </div>
    </div>

    <a-descriptions :column="{ xxl: 2, xl: 2, lg: 2, md: 2, sm: 2, xs: 2 }" bordered>
      <a-descriptions-item label="指标名称">{{ data.metricsName }}</a-descriptions-item>
      <a-descriptions-item label="指标类别">{{ data.metricsTypeId_dictText }}</a-descriptions-item>
      <a-descriptions-item label="指标标识">{{ data.metricsCode }}</a-descriptions-item>
      <a-descriptions-item label="考察要点">{{ data.investigatePoint }}</a-descriptions-item>
      <a-descriptions-item label="监控要点">{{ data.monitorPoint }}</a-descriptions-item>
      <a-descriptions-item label="现地核查要点">{{ data.localCheckPoint }}</a-descriptions-item>
      <a-descriptions-item label="指标备注">{{ data.metricsDesc }}</a-descriptions-item>
      <a-descriptions-item label="创建人">{{ data.createBy_dictText }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ data.createTime }}</a-descriptions-item>
    </a-descriptions>
  </a-card>
</template>

<script>
export default {
  name: 'data',
  data() {
    return {}
  },
  props: {
    data: {
      type: Object,
      required: false,
      default: () => {
        return {}
      },
    },
  },
  watch: {
    data: {
      handler(val) {
        this.data = val
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {},
  methods: {
    //返回上一级
    getGo() {
      this.$parent.pButton2(0)
    },
  },
}
</script>

<style scoped lang="less">
.colorBox {
  margin-top: 0;
  margin-bottom: 20px;
  .colorTotal {
    padding-left: 7px;
    border-left: 4px solid #1e3674;
  }
}

::v-deep .ant-descriptions-view {
  border-radius: 0px;
}

::v-deep .ant-descriptions-item-label {
  color: rgba(0, 0, 0, 0.45);
}
.content {
  color: rgba(0, 0, 0, 0.85) !important;
  word-break: break-all;
  white-space: normal;
  min-width: 260px;
}
::v-deep .ant-descriptions-item-content {
  .content;
}
::v-deep .ant-descriptions-bordered .ant-descriptions-item-label {
  text-align: center;
  color: rgba(0, 0, 0, 0.65);
  width: 15%;
}

::v-deep .ant-descriptions-bordered .ant-descriptions-item-content {
  width: 35%;
  .content;
}
</style>
