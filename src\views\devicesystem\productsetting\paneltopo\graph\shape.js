import { <PERSON>raph, <PERSON>, Node,Shape } from '@antv/x6'
import '@antv/x6-vue-shape'
import PanelElement from './PanelElement.vue'
Graph.registerVueComponent("PanelElement", PanelElement, true);
class boxsShape extends Shape.Rect {
  collapsed = false

  getTopPorts() {
    return this.getPortsByGroup('top')
  }

  getBottomPorts() {
    return this.getPortsByGroup('bottom')
  }

  getUsedInPorts(graph) {
    const incomingEdges = graph.getIncomingEdges(this) || []
    return incomingEdges.map((edge) => {
      const portId = edge.getTargetPortId()
      return this.getPort(portId)
    })
  }

  getNewInPorts(length) {
    return Array.from(
      {
        length,
      },
      () => {
        return {
          group: 'top',
        }
      },
    )
  }

  updateInPorts(graph) {
    const minNumberOfPorts = 2
    const ports = this.getTopPorts()
    const usedPorts = this.getUsedInPorts(graph)
    const newPorts = this.getNewInPorts(
      Math.max(minNumberOfPorts - usedPorts.length, 1),
    )

    if (
      ports.length === minNumberOfPorts &&
      ports.length - usedPorts.length > 0
    ) {
      // noop
    } else if (ports.length === usedPorts.length) {
      this.addPorts(newPorts)
    } else if (ports.length + 1 > usedPorts.length) {
      this.prop(
        ['ports', 'items'],
        this.getBottomPorts().concat(usedPorts).concat(newPorts),
        {
          rewrite: true,
        },
      )
    }
    return this
  }
  isCollapsed() {
    return this.collapsed
  }

  toggleButtonVisibility(visible) {
    this.attr('buttonGroup', {
      display: visible ? 'block' : 'none',
    })
  }

  toggleCollapse(collapsed) {
    const target = collapsed == null ? !this.collapsed : collapsed
    if (!target) {
      this.attr('buttonSign', {
        d: 'M 1 5 9 5 M 5 1 5 9',
        strokeWidth: 1.6,
      })
    } else {
      this.attr('buttonSign', {
        d: 'M 2 5 8 5',
        strokeWidth: 1.8,
      })
    }
    this.collapsed = target
  }
}

boxsShape.config({
  width: 60,
  height: 60,
  attrs: {
    body: {
      stroke: 'none',
      fill: 'rgba(95,149,255,0.05)'
    },
    image: {
      'xlink:href': require('@/assets/netdevice/box.png'),
      width: 60,
      height: 60,
    },
    label: {
      text: '机柜',
      refX: 0.5,
      refY: 0.99,
      textAnchor: "middle",
      textVerticalAnchor: "top",
    },

  },
  markup: [
    {
      tagName: 'rect',
      selector: 'body'
    },
    {
      tagName: 'image',
      selector: 'image'
    },
    {
      tagName: 'text',
      selector: 'label',
    },
    {
      tagName: 'foreignObject',
      selector: 'fo',
      children: [
        {
          ns: Dom.ns.xhtml,
          tagName: 'body',
          selector: 'foBody',
          children: [
            {
              tagName: 'div',
              selector: 'edit-text'
            }
          ]
        }
      ]
    }
  ],
  ports: {
    groups: {
      top: {
        position: 'top',
        attrs: {
          circle: {
            r: 3,
            magnet: true,
            stroke: '#5F95FF',
            strokeWidth: 1,
            fill: '#fff',
          }
        }
      },
      right: {
        position: 'right',
        attrs: {
          circle: {
            r: 3,
            magnet: true,
            stroke: '#5F95FF',
            strokeWidth: 1,
            fill: '#fff',
          }
        }
      },
      left: {
        position: 'left',
        attrs: {
          circle: {
            r: 3,
            magnet: true,
            stroke: '#5F95FF',
            strokeWidth: 1,
            fill: '#fff',
          }
        }
      },
      bottom: {
        position: 'bottom',
        attrs: {
          circle: {
            r: 3,
            magnet: true,
            stroke: '#5F95FF',
            strokeWidth: 1,
            fill: '#fff',
            // style: {
            //   visibility: 'hidden'
            // }
          }
        }
      },
    }
  },
  portMarkup: [
    {
      tagName: 'circle',
      selector: 'portBody',
    },
  ],
})
//正面板节点
export const panelNode = Graph.registerNode('panel-node', {
  inherit: 'rect',
  width:900,
  height:200,
  zIndex:1,
  data:{
  },
  markup: [
    {
      tagName: 'rect',
      selector: 'body'
    },
    {
      tagName: 'image',
      selector: 'image'
    },
    {
      tagName: 'text',
      selector: 'label'
    }
  ],
  attrs: {
    body: {
      refWidth: '100%',
      refHeight: '100%',
      strokeWidth: 1,
      fill: '#fafafa',
      stroke: '#E8E8E8',
    },
    image: {
      'xlink:href': "",
      refWidth: '100%',
      refHeight: '100%',
      preserveAspectRatio:"none",  
      // opacity:0,
    },
    // label: {
    //   text: '面板',
    //   fontSize: 14,
    //   fill: 'rgba(0,0,0,0.85)',
    //   refX: 0.5,
    //   refY: '100%',
    //   refY2: 4,
    //   textAnchor: 'middle',
    //   textVerticalAnchor: 'top',
    // },
  }
})

Graph.registerNode("panel-element", {
  inherit: "vue-shape",
  x: 200,
  y: 150,
  width:32,
  height:32,
  zIndex:3,
  angle:0,
  component: "PanelElement",
  attrs:{
    body: {
      refWidth: '100%',
      refHeight: '100%',
      strokeWidth: 2,
      stroke: '',
      // strokeDasharray:'10 4',
    },
    label: {
      text: '',
      fontSize: 14,
      fill: 'rgba(0,0,0,0.85)',
      refX: 0.5,
      refY: '100%',
      refY2: 0,
      textAnchor: 'middle',
      textVerticalAnchor: 'top',
    },
    foreignObject:{
      fontSize:32,
    },
  }
});

