<template>
  <div style='height: 100%'>
    <keep-alive exclude='SLADetails'>
      <component :is='pageName' :data='data'/>
    </keep-alive>
  </div>
</template>
<script>
import SLAList from './SLAList.vue'
import SLADetails from '@views/flowable/SLA/SLADetails.vue'
export default {
  name: 'SLAManage',
  props: {},
  data() {
    return {
      isActive: 0,
      data: {},
    }
  },
  components: {
    SLAList,
    SLADetails
  },
  created() {
    this.pButton1(0)
  },
  //使用计算属性
  computed: {
    pageName() {
      switch (this.isActive) {
        case 0:
          return 'SLAList'
          break
        default:
          return  'SLADetails'
          break
      }
    }
  },
  methods: {
    pButton1(index) {
      this.isActive = index
    },
    pButton2(index, item) {
      this.isActive = index
      this.data = item//告警信息
    }
  }
}
</script>