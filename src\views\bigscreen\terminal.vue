<template>
  <div class="body">
    <div class="body-left">
      <div class="body-left-core">
        <el-input placeholder="请输入" v-model="filterText"></el-input>
        <div class="allDepart" @click="clickAllDepart">全部单位</div>
        <el-tree class="filter-tree" :data="treeData" :props="defaultProps" default-expand-all
          :filter-node-method="filterNode" ref="tree" @node-click="clickTree"></el-tree>
      </div>
    </div>
    <div class="body-right">
      <div class="body-right-top">
        <div class="body-right-top-item">
          <div class="body-right-top-item-left">
            <img src="../../assets/bigScreen/41.png" alt />
          </div>
          <div class="body-right-top-item-right">
            <span>终端机数量</span>
            <span>{{ total }}</span>
          </div>
        </div>
        <div class="body-right-top-item">
          <div class="body-right-top-item-left">
            <img src="../../assets/bigScreen/42.png" alt />
          </div>
          <div class="body-right-top-item-right">
            <span>实时在线数</span>
            <span>{{ active }}</span>
          </div>
        </div>
        <div class="body-right-top-item">
          <div class="body-right-top-item-left">
            <img src="../../assets/bigScreen/44.png" alt />
          </div>
          <div class="body-right-top-item-right">
            <span>离线数量</span>
            <span>{{ offLine }}</span>
          </div>
        </div>
        <div class="body-right-top-item">
          <div class="body-right-top-item-left">
            <img src="../../assets/bigScreen/43.png" alt />
          </div>
          <div class="body-right-top-item-right">
            <span>在线率</span>
            <span>{{ active != null ? Number((active * 100) / total).toFixed() : 0 }} %</span>
          </div>
        </div>
        <div class="body-right-top-item">
          <div class="body-right-top-item-left">
            <img src="../../assets/bigScreen/43.png" alt />
          </div>
          <div class="body-right-top-item-right">
            <span>国产化率</span>
            <span>{{ nationalRate}} %</span>
          </div>
        </div>
      </div>
      <div class="body-right-bottom">
        <div class="body-right-bottom-title">
          <div class="body-right-bottom-title-item">
            <img src="../../assets/bigScreen/45.png" alt />
            <span>在线</span>
          </div>
          <div class="body-right-bottom-title-item">
            <img src="../../assets/bigScreen/46.png" alt />
            <span>离线</span>
          </div>
          <div class="body-right-bottom-title-item">
            <img src="../../assets/bigScreen/47.png" alt />
            <span>空闲</span>
          </div>
        </div>
        <!-- netType === 'double' -->
        <div v-if="netType === 'double'" class="body-right-bottom-core" ref="box" @scroll="scrollEvent">
          <a-collapse default-active-key="0" :bordered="false">
            <template #expandIcon="props">
              <a-icon type="caret-right" :rotate="props.isActive ? 90 : 0" />
            </template>
            <a-collapse-panel v-for="(item, index) in coreData" :key="index" :style="customStyle">
              <template slot="header">
                <span>
                  {{ item.departName }}（总数：{{ item.total }} 台
                  <span v-for="(net, netix) in item.networkTerminal"
                    :key="netix">&nbsp;&nbsp;{{ net.gatewayTypeName }}：{{ net.total }} 台</span>）
                </span>
              </template>
              <div class="body-right-bottom-core-item" v-for="(ite, inx) in item.networkTerminal" :key="inx">
                <div class="body-right-bottom-core-item-top">
                  <span>
                    {{ ite.gatewayTypeName }}（在线:<span style="color: #21cc0b">{{ ite.active }}</span>台 离线：
                    <span style="color: #00c8fe">{{ ite.offLine }}</span>
                    台 空闲：{{ ite.idle }} 台 ）
                  </span>
                </div>
                <div class="body-right-bottom-core-item-bottom" v-if="!ite.open">
                  <div class="body-right-bottom-core-item-bottom-left">
                    <div class="body-right-bottom-core-item-bottom-left-item" v-for="(im, ix) in ite.getTerminal"
                      :key="ix" @click="openGrafana(im)">
                      <img src='@/assets/bigScreen/45.png' v-if='im.status == 1 || im.status == 2' alt />
                      <img src='@/assets/bigScreen/46.png' v-else-if='im.status == 0' alt />
<!--                      <span :title='im.username'>{{ im.username }}</span>-->
                    </div>
                  </div>
                  <div class="body-right-bottom-core-item-bottom-right" v-if="ite.getTerminal.length > 0">
                    <a>
                      <img v-if="ite.getTerminal.length >= 25"
                        @click="openDoubleNet(index, inx, item.id, ite.gatewayCode)" src="../../assets/bigScreen/48.png"
                        alt />
                    </a>
                  </div>
                </div>
                <div class="body-right-bottom-core-item-bottom2" v-else>
                  <div class="bottom2-top">
                    <div class="bottom2-top-item" v-for="(gtl, ixl) in ite.getTerminal" :key="ixl"
                      @click="openGrafana(gtl)">
                      <img src="../../assets/bigScreen/45.png" v-if="gtl.status == 1 || gtl.status == 2" alt />
                      <img src="../../assets/bigScreen/46.png" v-else-if="gtl.status == 0" alt />
<!--                       <span :title="gtl.username">{{ gtl.username }}</span>-->
                    </div>
                  </div>
                  <div class="bottom2-bottom">
                    <el-pagination background layout="total, prev, pager, next" :page-size="168" :total="ite.total"
                      @current-change="handleCurrentChangeDoubleNet"></el-pagination>
                    <a>
                      <img @click="closeDoubleNet(index, inx)" src="../../assets/bigScreen/arrow-up.png" alt />
                    </a>
                  </div>
                </div>
              </div>
            </a-collapse-panel>
          </a-collapse>
        </div>
        <!-- netType === 'single' -->
        <div class="body-right-bottom-core" ref="box" @scroll="scrollEvent" v-if="netType === 'single'">
          <div class="body-right-bottom-core-item" v-for="(item, index) in coreData" :key="index">
            <div class="body-right-bottom-core-item-top">
              <span>
                {{ item.departName }}（在线: <span style="color: #21cc0b">{{ item.active }} </span> 台 &nbsp; 离线:
                <span style="color: #00c8fe">{{ item.offLine }} </span> 台 &nbsp; 空闲: {{ item.idle }} 台 ）
              </span>
            </div>
            <div class="body-right-bottom-core-item-bottom" v-if="!item.open">
              <div class="body-right-bottom-core-item-bottom-left">
                <div class="body-right-bottom-core-item-bottom-left-item" v-for="(ite, idx) in item.terminals"
                  :key="idx" @click="openGrafana(ite)">
                  <a-popover :getPopupContainer="(target) => target.parentNode">
                    <template slot="content">
                      <a-descriptions bordered size="small" :column="1">
                        <a-descriptions-item label="名称"> {{ ite.name }} </a-descriptions-item>
                        <a-descriptions-item label="最后一次开机时间"> {{ terminalInfo.onLastTime }} </a-descriptions-item>
                        <a-descriptions-item label="最后一次关机时间"> {{ terminalInfo.offLastTime }} </a-descriptions-item>
                        <a-descriptions-item label="开机次数"> {{terminalInfo.onCount}} </a-descriptions-item>
                        <a-descriptions-item label="开机总时长">{{ terminalInfo.onTotal}} </a-descriptions-item>
                      </a-descriptions>
                    </template>
                    <div @mouseenter="getTerminalInfo(ite)" class="body-right-bottom-core-item-bottom-left-item-content">
                      <img src="../../assets/bigScreen/45.png" v-if="ite.status == 1 || ite.status == 2" alt="" />
                      <img src="../../assets/bigScreen/46.png" v-else-if="ite.status == 0" alt="" />
<!--                       <span :title="ite.username">{{ ite.username }}</span>-->
                    </div>
                  </a-popover>
                </div>
              </div>
              <div class="body-right-bottom-core-item-bottom-right">
                <a>
                  <img v-if="item.terminals.length >= 25" @click="openSingleNet(index, item.id)"
                    src="../../assets/bigScreen/48.png" alt="" />
                </a>
              </div>
            </div>
            <div class="body-right-bottom-core-item-bottom2" v-else>
              <div class="bottom2-top">
                <div class="bottom2-top-item" v-for="(tems, ixl) in item.terminals" :key="ixl"
                  @click="openGrafana(tems)">
                  <img src="../../assets/bigScreen/45.png" v-if="tems.status == 1 || tems.status == 2" alt="" />
                  <img src="../../assets/bigScreen/46.png" v-else-if="tems.status == 0" alt="" />
                   <span :title="tems.username">{{ tems.username }}</span>
                </div>
              </div>
              <div class="bottom2-bottom">
                <el-pagination background layout="total, prev, pager, next" :page-size="168" :total="item.total"
                  @current-change="handleCurrentChangeSingleNet">
                </el-pagination>
                <a>
                  <img @click="closeSingleNet(index)" src="../../assets/bigScreen/arrow-up.png" alt="" />
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  import {
    deleteAction,
    getAction,
    putAction,
    httpAction,
    postAction
  } from '@/api/manage'
  import {
    queryConfigureDictItem,
    queryConfigureDictItems
  } from '@/api/api'

  export default {
    data() {
      return {
        total: 0,
        grafanaUrl: '',
        active: 0,
        idle: 0,
        offLine: 0,
        nationalRate: 0,
        filterText: '',
        treeData: [],
        defaultProps: {
          children: 'children',
          label: 'title',
          value: 'key',
        },
        url: {
          overview: '/data-analysis/terminal/overview',
          deptList: '/data-analysis/terminal/dept/list',
          terminalList: '/data-analysis/terminal/list',
          terminalDeptTreeList: '/data-analysis/terminal/deptTreeList',
          terminalStatusList: '/data-analysis/terminal/terminalStatusInfo',
          queryConfigureDict: '/configure/dictItem/getItemValue', //根据配置字典code查询字典项
        },
        coreData: [],
        netType: '',
        terminalData: [],
        subscript: null,
        companyId: '',
        gatewayCode: '',
        companyPage: 1,
        companyPages: '',
        terminalPage: 1,
        terminalTotal: '',
        pid: '',
        customStyle: 'margin-bottom: 16px;border: 0;overflow: hidden',
        clickIndex: 0,
        clickIndex1: 0,
        statusObj: {
          1: '在线',
          0: '离线',
        },
        terminalInfo: {},
        pop: false,
      }
    },
    watch: {
      filterText(val) {
        this.$refs.tree.filter(val)
      },
    },
    created() {},
    mounted() {
      this.companyPage = 1
      this.overview()
      this.deptList()
      this.terminalDeptTreeList()
    },
    methods: {
      // 获取设备信息
      getTerminalInfo(ite) {
        getAction(this.url.terminalStatusList, {
          uniqueCode: ite.uniqueCode,
        }).then((res) => {
          if (res.success) {
            this.terminalInfo = res.result
          }
        })
      },
      openGrafana(item) {
        let CZ_Data = window._CONFIG['customization']
        if (
          CZ_Data &&
          CZ_Data.cz_zunyi &&
          CZ_Data.cz_zunyi.internetDataGateWayCode &&
          CZ_Data.cz_zunyi.internetDataGateWayCode.length > 0
        ) {
          for (let i = 0; i < CZ_Data.cz_zunyi.internetDataGateWayCode.length; i++) {
            const CodeArray = CZ_Data.cz_zunyi.internetDataGateWayCode[i]
            if (CodeArray == item.gatewayCode) {
              let nameUrl = CZ_Data.cz_zunyi.internetGrafanaURL + item.uniqueCode
              window.open(nameUrl)
              return
            }
          }
        }

        if (!item.grafanaUrl) {
          this.$message.warning('grafana不可跳转')
        } else {
          //var ip = /\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}/
          //var ipStr = ip.exec(window.location.href)
          var ipStr = window.location.host // 获取当前页面地址
          queryConfigureDictItems({
            parentCode: 'grafanaProxy',
            childCode: 'url',
          }).then((res) => {
            if (res.success && res.result) {
              res.result.forEach(el=>{
                var arys = el.value.split('>')
                if (!!ipStr && ipStr === arys[0]) {
                  let grafanaUrl = arys[1] + item.grafanaUrl
                  window.open(grafanaUrl)
                }
              })

            }

          })
        }
      },
      // 获取初始终端机数量，活跃数量
      overview() {
        getAction(this.url.overview, {
          deptId: this.pid,
        }).then((res) => {
          if (res.code == 200) {
            if (res.result.total == undefined || res.result.total == null || res.result.total == '') {
              this.total = 0
            } else {
              this.total = res.result.total
            }
            if (res.result.active == undefined || res.result.active == null || res.result.active == '') {
              this.active = 0
            } else {
              this.active = res.result.active
            }
            if (res.result.idle == undefined || res.result.idle == null || res.result.idle == '') {
              this.idle = 0
            } else {
              this.idle = res.result.idle
            }
            if (res.result.offLine == undefined || res.result.offLine == null || res.result.offLine == '') {
              this.offLine = 0
            } else {
              this.offLine = res.result.offLine
            }
            if (res.result.nationalRate == undefined || res.result.nationalRate == null || res.result
              .nationalRate == '' || res.result.nationalRate == 'NaN') {
              this.nationalRate = 0
            } else {
              this.nationalRate = res.result.nationalRate
            }
          }
        })
      },

      // tree值
      terminalDeptTreeList() {
        getAction(this.url.terminalDeptTreeList).then((res) => {
          if (res.code == 200) {
            this.treeData = res.result
          }
        })
      },

      filterNode(value, data) {
        if (!value) return true
        return data.title.indexOf(value) !== -1
      },
      // 单机树节点的某一节点
      clickTree(value) {
        this.subscript = null
        this.terminalData = []
        this.pid = value.key
        this.overview()
        this.coreData = []
        this.companyPage = 1
        this.depListTwo()
      },
      clickAllDepart() {
        this.subscript = null
        this.terminalData = []
        this.pid = ''
        this.overview()
        this.coreData = []
        this.companyPage = 1
        this.depListTwo()
      },

      deptList() {
        if (this.companyPage == 1) {
          getAction(this.url.deptList, {
            pageNo: this.companyPage,
            pid: this.pid,
          }).then((res) => {
            if (res.code == 200) {
              this.coreData = res.result.records.filter((ele) => ele.departName !== '全部单位')
              this.netType = res.result.records[0].type
              this.companyPages = res.result.pages
            }
          })
        } else if (this.companyPage <= this.companyPages) {
          getAction(this.url.deptList, {
            pageNo: this.companyPage,
            pid: this.pid,
          }).then((res) => {
            if (res.code == 200) {
              res.result.records.forEach((e) => {
                this.coreData.push(e)
              })
            }
          })
        }
      },

      depListTwo() {
        getAction(this.url.deptList, {
          pageNo: this.companyPage,
          pid: this.pid,
        }).then((res) => {
          if (res.code == 200) {
            if (res.result.records != undefined) {
              this.coreData = res.result.records
              this.companyPages = res.result.pages
            } else {
              this.coreData = res.result
            }
          }
        })
      },

      //滚动条触发事件
      scrollEvent(e) {
        if (e.srcElement.scrollTop + e.srcElement.clientHeight + 1 > e.srcElement.scrollHeight) {
          this.companyPage += 1
          this.deptList()
        }
      },

      // 打开
      openDoubleNet(index, subInx, id, code) {
        // this.coreData[index]['networkTerminal'][subInx]['open'] = true
        //直接添加属性open 不会触发vue响应dom渲染，需要使用$set
        this.$set(this.coreData[index]['networkTerminal'][subInx], 'open', true)
        this.companyId = id
        this.gatewayCode = code
        this.clickIndex = index
        this.clickIndex1 = subInx
        // this.subscript = index
        // this.terminalPage = 1
        // this.terminalList()
      },
      openSingleNet(index, id) {
        // this.coreData[index]['networkTerminal'][subInx]['open'] = true
        //直接添加属性open 不会触发vue响应dom渲染，需要使用$set
        this.$set(this.coreData[index], 'open', true)
        this.clickIndex = index
        this.companyId = id
        // this.gatewayCode = code
        // this.subscript = index
        // this.terminalPage = 1
        // this.terminalList()
      },
      // 关闭
      closeDoubleNet(index, subInx) {
        this.coreData[index]['networkTerminal'][subInx]['open'] = false
        // this.subscript = null
        // this.companyId = ''
        // this.gatewayCode = ''
        // this.terminalData = []
        // this.terminalTotal = ''
      },
      closeSingleNet(index) {
        this.coreData[index]['open'] = false
        // this.subscript = null
        // this.companyId = ''
        // this.gatewayCode = ''
        // this.terminalData = []
        // this.terminalTotal = ''
      },

      // 指定单位终端列表
      terminalListDoubleNet() {
        getAction(this.url.terminalList, {
          pageNo: this.terminalPage,
          pageSize: 168,
          deptId: this.companyId,
          gatewayCode: this.gatewayCode,
        }).then((res) => {
          if (res.code == 200) {
            this.coreData[this.clickIndex]['networkTerminal'][this.clickIndex1].getTerminal = res.result.records
            this.terminalTotal = res.result.total
          }
        })
      },
      terminalListSingleNet() {
        getAction(this.url.terminalList, {
          pageNo: this.terminalPage,
          pageSize: 168,
          deptId: this.companyId,
        }).then((res) => {
          if (res.code == 200) {
            this.coreData[this.clickIndex].terminals = res.result.records
            this.terminalTotal = res.result.total
          }
        })
      },

      // 页码改变
      handleCurrentChangeDoubleNet(val) {
        this.terminalPage = val
        this.terminalListDoubleNet()
      },
      handleCurrentChangeSingleNet(val) {
        this.terminalPage = val
        this.terminalListSingleNet()
      },
    },
  }
</script>
<style lang='less' scoped>
  /deep/ .ant-popover-content .ant-popover-inner {
    background-color: rgba(100, 100, 100, .85);
  }

  /deep/ .ant-descriptions-item-label {
    color: rgba(255, 255, 255, .85);
    background-color: rgba(100, 100, 100, .85);
  }

  /deep/ .ant-descriptions-item-content {
    color: rgba(255, 255, 255, .85);
    background-color: rgba(100, 100, 100, .85);
  }

  .body {
    width: 22.75rem
      /* 1820/80 */
    ;
    height: 100%;
    padding: 0.25rem
      /* 20/80 */
      0.2rem 0.1125rem 0.2rem;
    display: flex;

    .body-left {
      width: 20%;
      height: 100%;
      background: #111217;
      border-radius: 0.075rem
        /* 6/80 */
      ;
      margin-right: 0.25rem
        /* 20/80 */
      ;

      .body-left-core {
        width: 100%;
        height: 100%;

        .el-tree {
          width: 100%;
          height: 88%;
          background: none;
          color: #d8d8d8;
          overflow: auto;
        }

        /deep/ .el-input {
          padding: 6%;
        }

        /deep/ .el-input__inner {
          background-color: #111217;
          color: #fff;
        }

        /deep/ .el-tree-node__content:hover {
          background: #202125;
        }

        /deep/ .el-tree-node__content {
          background: none;
        }

        /deep/ .el-tree-node__label {
          letter-spacing: 4px;
        }
      }
    }

    .body-right {
      height: 100%;
      width: 79%;
      background: #111217;
      padding: 0.55rem
        /* 44/80 */
      ;
      border-radius: 0.075rem
        /* 6/80 */
      ;

      .body-right-top {
        width: 100%;
        height: 1.125rem
          /* 90/80 */
        ;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .body-right-top-item {
          width: 19%;
          height: 100%;
          display: flex;
          align-items: center;
          background: #222325;
          border-radius: 6px;

          .body-right-top-item-left {
            width: 30%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;

            img {
              width: 0.5rem
                /* 40/80 */
              ;
              height: 0.5rem
                /* 40/80 */
              ;
            }
          }

          .body-right-top-item-right {
            width: 70%;
            height: 100%;
            display: flex;
            align-items: center;
            color: #f6f6f8;
            font-size: 0.2rem
              /* 16/80 */
            ;

            span:nth-child(2) {
              color: #01c8ff;
              font-size: 0.25rem
                /* 20/80 */
              ;
              margin-left: 0.375rem
                /* 30/80 */
              ;
            }
          }
        }
      }

      .body-right-bottom {
        height: 88%;
        width: 100%;
        display: flex;
        flex-direction: column;

        .body-right-bottom-title {
          width: 98%;
          height: 10%;
          display: flex;
          align-items: center;
          justify-content: flex-end;

          .body-right-bottom-title-item {
            font-size: 0.2rem
              /* 16/80 */
            ;
            margin-left: 0.375rem
              /* 30/80 */
            ;
            color: #f5f7f6;

            img {
              width: 0.375rem
                /* 30/80 */
              ;
              height: 0.375rem
                /* 30/80 */
              ;
              margin-right: 0.125rem
                /* 10/80 */
              ;
              overflow: auto;
            }
          }
        }

        .body-right-bottom-core {
          width: 100%;
          height: 90%;
          overflow: scroll;
          overflow-x: hidden;

          .body-right-bottom-core-item {
            width: 100%;

            .body-right-bottom-core-item-top {
              color: #fff;
              font-size: 0.2rem
                /* 16/80 */
              ;
              display: flex;
              align-items: center;
            }

            .body-right-bottom-core-item-bottom {
              width: 100%;
              display: flex;
              align-items: center;
              justify-content: space-between;
              margin: 0.15rem
                /* 12/80 */
                0;

              .body-right-bottom-core-item-bottom-left {
                width: 96%;
                overflow: hidden;
                display: flex;
                align-items: center;

                .body-right-bottom-core-item-bottom-left-item {
                  // width: 0.625rem /* 50/80 */;
                  height: 0.9rem
                    /* 64/80 */
                  ;
                  // display: flex;
                  // align-items: center;
                  // justify-content: center;
                  margin-right: 0.13rem
                    /* 10.24/80 */
                  ;
                  text-align: center;
                  display: flex;
                  flex-direction: column;
                  cursor: pointer;
                  .body-right-bottom-core-item-bottom-left-item-content{
                    text-align: center;
                    display: flex;
                    justify-content: start;
                    align-items: center;
                    flex-flow: column nowrap;
                    cursor: pointer;
                  }
                  img {
                    width: 0.5rem
                      /* 40/80 */
                    ;
                    height: 0.5rem
                      /* 40/80 */
                    ;
                    margin-bottom: 0.0875rem
                      /* 7/80 */
                    ;
                  }

                  span {
                    color: white;
                    opacity: 0.65;
                  }
                }
              }

              .body-right-bottom-core-item-bottom-right {
                width: 4%;
                display: flex;
                align-items: center;
                justify-content: center;
                height: 0.5rem
                  /* 40/80 */
                ;
              }
            }

            .body-right-bottom-core-item-bottom2 {
              width: 100%;
              margin: 0.15rem
                /* 12/80 */
                0;

              .bottom2-top {
                // height: 5rem /* 400/80 */;
                display: flex;
                flex-wrap: wrap;

                .bottom2-top-item {
                  //width: 0.625rem /* 50/80 */;
                  height: 0.8rem
                    /* 50/80 */
                  ;
                  //display: flex;
                  //align-items: center;
                  //justify-content: center;
                  margin-top: 0.125rem
                    /* 10/80 */
                  ;
                  margin-right: 0.13rem
                    /* 10.24/80 */
                  ;
                  text-align: center;
                  display: flex;
                  flex-direction: column;
                  cursor: pointer;

                  img {
                    width: 0.5rem
                      /* 40/80 */
                    ;
                    height: 0.5rem
                      /* 40/80 */
                    ;
                    margin-bottom: 0.0875rem
                      /* 7/80 */
                    ;
                  }

                  span {
                    color: #fff;
                    opacity: 0.65;
                  }
                }
              }

              .bottom2-bottom {
                height: 0.7rem;
                // background: skyblue;
                display: flex;
                align-items: center;
                justify-content: flex-end;

                a {
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  padding: 0 0.25rem
                    /* 20/80 */
                  ;

                  img {
                    width: 0.375rem
                      /* 30/80 */
                    ;
                    height: 0.375rem
                      /* 30/80 */
                    ;
                  }
                }

                /deep/ .el-pagination__total {
                  color: #fff !important;
                  font-size: 0.225rem
                    /* 18/80 */
                     !important;
                }
              }
            }
          }
        }
      }
    }
  }


  ::v-deep .allDepart {
    letter-spacing: 4px;
    font-size: 14px;
    white-space: nowrap;
    color: #d8d8d8 !important;
    box-sizing: border-box;
    height: 26px;
    align-items: center;
    padding-left: 24px;
  }

  ::v-deep .allDepart:hover {
    cursor: pointer;
    background-color: #202125;
  }

  ::v-deep .ant-collapse {
    background: #111217 !important;

    .ant-collapse-item {
      background-color: #040407 !important;
      border-radius: 0px !important;

      .ant-collapse-header {
        font-size: 0.2rem
          /* 16/80 */
        ;
        color: #fff;
      }

      .ant-collapse-content .ant-collapse-content-box .body-right-bottom-core-item {
        width: 100%;
        margin-left: 24px;

        .body-right-bottom-core-item-top {
          color: #fff;
          font-size: 0.2rem
            /* 16/80 */
          ;
          display: flex;
          align-items: center;
        }

        .body-right-bottom-core-item-bottom {
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin: 0.15rem
            /* 12/80 */
            0;

          .body-right-bottom-core-item-bottom-left {
            width: 96%;
            overflow: hidden;
            display: flex;
            align-items: center;

            .body-right-bottom-core-item-bottom-left-item {
              // width: 0.625rem /* 50/80 */;
              height: 0.9rem
                /* 64/80 */
              ;
              // display: flex;
              // align-items: center;
              // justify-content: center;
              margin-top: 0.125rem
                /* 10/80 */
              ;
              margin-right: 0.13rem
                /* 10.24/80 */
              ;
              text-align: center;
              display: flex;
              flex-direction: column;
              cursor: pointer;

              img {
                width: 0.5rem
                  /* 40/80 */
                ;
                height: 0.5rem
                  /* 40/80 */
                ;
                margin-bottom: 0.0875rem
                  /* 7/80 */
                ;
              }

              span {
                font-size: 12px;
                color: white;
                opacity: 0.65;
              }
            }
          }

          .body-right-bottom-core-item-bottom-right {
            width: 4%;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 0.5rem
              /* 40/80 */
            ;
          }
        }

        .body-right-bottom-core-item-bottom2 {
          width: 100%;
          margin: 0.15rem
            /* 12/80 */
            0;

          .bottom2-top {
            // height: 5rem /* 400/80 */;
            display: flex;
            flex-wrap: wrap;

            .bottom2-top-item {
              //width: 0.625rem /* 50/80 */;
              height: 0.8rem
                /* 64/80 */
              ;
              //display: flex;
              //align-items: center;
              //justify-content: center;
              margin-top: 0.125rem
                /* 10/80 */
              ;
              margin-right: 0.13rem
                /* 10.24/80 */
              ;
              text-align: center;
              display: flex;
              flex-direction: column;
              cursor: pointer;

              img {
                width: 0.5rem
                  /* 40/80 */
                ;
                height: 0.5rem
                  /* 40/80 */
                ;
                margin-bottom: 0.0875rem
                  /* 7/80 */
                ;
              }

              span {
                font-size: 12px;
                color: #fff;
                opacity: 0.65;
              }
            }
          }

          .bottom2-bottom {
            height: 0.7rem;
            // background: skyblue;
            display: flex;
            align-items: center;
            justify-content: flex-end;

            a {
              display: flex;
              align-items: center;
              justify-content: center;
              padding: 0 0.25rem
                /* 20/80 */
              ;

              img {
                width: 0.375rem
                  /* 30/80 */
                ;
                height: 0.375rem
                  /* 30/80 */
                ;
              }
            }

            /deep/ .el-pagination__total {
              color: #fff !important;
              font-size: 0.225rem
                /* 18/80 */
                 !important;
            }
          }
        }
      }
    }
  }

  /* 定义滚动条样式 */
  ::-webkit-scrollbar {
    width: 0.1rem
      /* 8/80 */
    ;
    // height: 6px;
    background-color: #222325;
  }

  /*定义滚动条轨道 内阴影+圆角*/
  ::-webkit-scrollbar-track {
    box-shadow: inset 0 0 0px rgba(240, 240, 240, 0.5);
    // border-radius: 50%;
    background-color: #1c1e22;
  }

  /*定义滑块 内阴影+圆角*/
  ::-webkit-scrollbar-thumb {
    border-radius: 0.05rem
      /* 4/80 */
    ;
    box-shadow: inset 0 0 0px rgba(240, 240, 240, 0.5);
    background-color: #2e3034;
  }
</style>