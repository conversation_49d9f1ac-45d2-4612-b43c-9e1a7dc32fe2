<template>
  <div style="margin-bottom: 10px">
    <a-card
      class="card1"
      style="width: 98.5%; border: 0; padding-left: 0; padding-right: 0"
      size="default"
    >
      <div slot="cover">
        <span
          class="coverSpan"
          style="
            padding-top: 1.1%;
            font-family: PingFangSC-Regular;
            font-size: 16px;
            color: rgba(0, 0, 0, 0.65);
            padding-left: 10px;
            border-bottom: 1px solid #f1f0f0;
            padding-bottom: 12px;
            display: block;
          "
        >
          <span style="border-left: 4px solid #1890ff; margin-right: 11px"></span>
          任务统计
        </span>
      </div>
      <a-row style="text-align: center">
        <a-col
          :sm="8"
          :xs="24"
          style="border-right: 2px dashed #e8e8e8; margin-top: 12px"
        >
          <p class="fontP">待处理的任务</p>
          <p
            class="fontNum"
            style="color: #ffc200"
          >{{ toDoNumber }}</p>
        </a-col>
        <a-col
          :sm="8"
          :xs="24"
          style="border-right: 2px dashed #e8e8e8; margin-top: 12px"
        >
          <p class="fontP">已处理的任务</p>
          <p
            class="fontNum"
            style="color: #0085ee"
          >{{ doneNumber }}</p>
        </a-col>
        <a-col
          :sm="8"
          :xs="24"
          style="margin-top: 12px"
        >
          <p class="fontP">逾期的任务</p>
          <p
            class="fontNum"
            style="color: #f30606"
          >{{ overdueAmount }}</p>
        </a-col>
      </a-row>
    </a-card>
    <div style="margin-top: 16px; width: 98.5%; border: 0; background: #fff; height: 400px">
      <div slot="cover">
        <span
          class="coverSpan"
          style="
            padding-top: 1.1%;
            font-family: PingFangSC-Regular;
            font-size: 16px;
            color: rgba(0, 0, 0, 0.65);
            padding-left: 10px;
            border-bottom: 1px solid #f1f0f0;
            padding-bottom: 12px;
            display: block;
          "
        >
          <span style="border-left: 4px solid #1890ff; margin-right: 11px"></span>
          待处理任务
        </span>
      </div>
      <a-row class="styleOver">
        <a-col
          :lg="12"
          :xl="8"
          :xs="24"
          v-for="(item, index) in data"
          :key="index"
          style="
            white-space: nowrap;
            padding: 2.15% 3% 26px 49px;
            border-right: 1px solid #f1f0f0;
            border-bottom: 1px solid #f1f0f0;
            height:170px
          "
        >
          <div>
            <p
              v-if="item.processType == 'sj'"
              @click="affairClick(1,item)"
              style="margin-bottom: 6px; font-weight: 600;cursor:pointer"
            >
              <span class="icon iconColor">
                <a-icon type="tag" />
              </span>
              <span style="font-family: PingFangSC-Medium; font-size: 18px; color: #000000">{{
                item.processTypeText
              }}</span>
            </p>
            <p
              v-else-if="item.processType == 'bg'"
              @click="affairClick(3,item)"
              style="margin-bottom: 6px; font-weight: 600;cursor:pointer"
            >
              <span class="icon iconColor1">
                <a-icon type="swap" />
              </span>
              <span style="font-family: PingFangSC-Medium; font-size: 18px; color: #000000">{{
                item.processTypeText
              }}</span>
            </p>
            <p
              v-else-if="item.processType == 'wtgl' || item.processType == 'ywwt'"
              @click="affairClick(2,item)"
              style="margin-bottom: 6px; font-weight: 600;cursor:pointer"
            >
              <span class="icon iconColor2">
                <a-icon type="question" />
              </span>
              <span style="font-family: PingFangSC-Medium; font-size: 18px; color: #000000">{{
                item.processTypeText
              }}</span>
            </p>
            <p
              v-else-if="item.processType == 'fb'"
              @click="affairClick(4,item)"
              style="margin-bottom: 6px; font-weight: 600; cursor:pointer"
            >
              <span class="icon iconColor3">
                <a-icon type="tag" />
              </span>
              <span style="font-family: PingFangSC-Medium; font-size: 18px; color: #000000">{{
                item.processTypeText
              }}</span>
            </p>
            <p
              v-else-if="item.processType == 'pzgl'"
              @click="affairClick(7,item)"
              style="margin-bottom: 6px; font-weight: 600;cursor:pointer"
            >
              <span
                class="icon iconColor4"
              >
                <a-icon type="setting" />
              </span>
              <span style="font-family: PingFangSC-Medium; font-size: 18px; color: #000000">{{
                item.processTypeText
              }}</span>
            </p>
            <p
              class="information-title"
              :title="item.title"
              style="font-family: PingFangSC-Regular; font-size: 16px; color: rgba(0, 0, 0, 0.65)"
            >
              {{ item.title }}
            </p>

            <div style="font-family: PingFangSC-Regular; margin-bottom: 6px; font-size: 14px; color: rgba(0, 0, 0, 0.45)">
              {{ item.code }}
            </div>
            <div style="font-family: PingFangSC-Regular; font-size: 14px; color: rgba(0, 0, 0, 0.45)">
              {{ item.applyTime }}
            </div>
          </div>
        </a-col>
      </a-row>
    </div>
    <a-card
      style="width: 98.5%; border: 0; margin-top: 16px; height: 327px"
      size="default"
    >
      <div slot="cover">
        <span
          class="coverSpan"
          style="
            padding-top: 1.1%;
            font-family: PingFangSC-Regular;
            font-size: 16px;
            color: rgba(0, 0, 0, 0.65);
            padding-left: 10px;
            border-bottom: 1px solid #f1f0f0;
            padding-bottom: 12px;
            display: block;
          "
        >
          <span style="border-left: 4px solid #1890ff; margin-right: 11px"></span>
          过去30天内
        </span>
      </div>
      <line-chart
        :chartChange="LineChartChange"
        :chartOption="LineChartData"
        :chartStyle="{ width: '100%', height: '230px' }"
      />
    </a-card>
  </div>
</template>
<script>
import LineChart from './LineChart'
import { getAction } from '@/api/manage'
export default {
  name: 'homeLeft',
  components: {
    LineChart,
  },
  data() {
    return {
      data: [],
      fields: ['zhengchang', 'aaa'],
      dataSource: [],
      LineChartData: {
        color: [
          'rgb(31,166,9)',
          'rgb(149,0,254)',
          'rgb(223,3,3)',
          'rgb(221,99,10)',
          'rgb(156,148,21)',
          'rgb(4,134,209)',
          'rgb(67,81,108)',
        ],
        xAxis: {
          type: 'category',
          data: [],
          axisLine: {
            lineStyle: {
              color: '#696969',
              width: 1, //这里是为了突出显示加上的
            },
          },
        },
        yAxis: {
          type: 'value',
          min: 0,
          splitNumber: 5,
          boundaryGap: [0.2, 0.2],
          splitLine: {
            //网格线
            lineStyle: {
              color: '#E3E3E3',
              type: 'dashed', //设置网格线类型 dotted：虚线   solid:实线
            },
            show: true, //隐藏或显示
          },
          axisLine: {
            lineStyle: {
              color: '#696969',
              width: 1, //这里是为了突出显示加上的
            },
          },
        },
        series: [
          {
            name: '正常',
            type: 'line',
            areaStyle: {
              color: '#c8b5e680',
            },
            data: [],
            smooth: true,
            itemStyle: {
              normal: {
                color: '#AD92D9',
                lineStyle: {
                  color: '#AD92D9',
                  width: 1,
                },
              },
            },
          },
          {
            name: '违反SLA',
            type: 'line',
            areaStyle: {
              color: '#8aceff80',
            },
            data: [],
            smooth: true,
            itemStyle: {
              normal: {
                color: '#0E9CFF',
                lineStyle: {
                  color: '#0E9CFF',
                  width: 1,
                },
              },
            },
          },
        ],
      },
      LineChartChange: false,
      url: {
        getTaskStatistics: '/workbench/getTaskStatistics',
        getEventTaskStatistics: '/workbench/getEventTaskStatistics',
      },
      toDoNumber: 0, //待办
      doneNumber: 0, //已办
      overdueAmount: 0, //预期
    }
  },
  methods: {
    affairClick(index,item) {
      if (index == '1') {
        this.$router.push({ path: '/event/todo', query: { id: item} })
      } else if (index == '2') {
        this.$router.push({ path: '/question/todo', query: { id: item } })
      } else if (index == '3') {
        this.$router.push({ path: '/change/todoManage', query: { id: item } })
      } else if (index == '4') {
        this.$router.push({ path: '/release/todoManage', query: { id: item } })
      } else if (index == '5') {
        this.$router.push({ path: '/itilconfigitemlibrary/ItilConfigItemLibraryList', query: { id: item } })
      } else if (index == '7') {
        this.$router.push({ path: '/itilconfigprocess/ItilConfigprocesstodoManage', query: { id: item } })
      }
    },
    initData() {
      getAction(this.url.getTaskStatistics).then((res) => {
        if (res.success) {
          if (res.result.toDoNumber == undefined || res.result.toDoNumber == null || res.result.toDoNumber == '') {
            this.toDoNumber = 0
          } else {
            this.toDoNumber = res.result.toDoNumber
          }
          if (res.result.doneNumber == undefined || res.result.doneNumber == null || res.result.doneNumber == '') {
            this.doneNumber = 0
          } else {
            this.doneNumber = res.result.doneNumber
          }
          if (
            res.result.overdueAmount == undefined ||
            res.result.overdueAmount == null ||
            res.result.overdueAmount == ''
          ) {
            this.overdueAmount = 0
          } else {
            this.overdueAmount = res.result.overdueAmount
          }
          this.data = res.result.taskVoList
        } else {
          this.$message.error(res.message)
        }
      })
    },
    handleLineData() {
      getAction(this.url.getEventTaskStatistics).then((res) => {
        if (res.success) {
          this.dataSource = res.result
          this.LineChartData.xAxis.data = this.dataSource.xAxisData
          this.LineChartData.series[0].data = this.dataSource.normal
          this.LineChartData.series[1].data = this.dataSource.abnormal
          // this.LineChartData.series.areaStyle = this.LineChartData.color;
          this.LineChartChange = !this.LineChartChange
        } else {
          this.$message.error(res.message)
        }
      })
    },
    tips() {
      // if(document.getElementById('aInp').value == ''){
      //   document.getElementsByClassName('aUi')[0].style.display = 'none'
      // }else{
      document.getElementsByClassName('aUi')[0].style.display = 'block'
      // }
    },

    // tipBlur(){
    //   document.getElementsByClassName('aUi')[0].style.display = 'none'
    // },
    LiValue(data) {
      this.searchVal = data
      document.getElementsByClassName('aUi')[0].style.display = 'none'
    },
  },
  mounted() {
    this.handleLineData()
    this.initData()
  },
  computed: {
    // 滚动区高度
    scrollerHeight: function () {
      return window.innerHeight - 278 + 'px' //自定义高度需求
    },
  },
}
</script>
<style scoped>
.card1 /deep/ .ant-card-body {
  padding-left: 0;
  padding-right: 0;
}

.styleOver {
  height: calc(100% - 49.19px);
  overflow: hidden;
  overflow-y: auto;
}

.fontP {
  font-family: PingFangSC-Medium;
  font-size: 18px;
  color: rgba(0, 0, 0, 0.65);
  margin-bottom: 9px;
}

.fontNum {
  font-family: PingFangSC-Medium;
  font-size: 32px;
  margin-bottom: 0;
}

.icon {
  margin-right: 10px;
  width: 24px;
  height: 24px;
  display: inline-block;
  border-radius: 100%;

  text-align: center;
  color: #fff;
  line-height: 24px;
}

.iconColor {
  background: #a894f6;
}

.iconColor1 {
  background: #009dff;
}

.iconColor2 {
  background: #ffc200;
}

.iconColor3 {
  background: #ff7d00;
}

.iconColor4 {
  background: #009e15;
}

.information-title {
  width: 100%;
  /*一定要设置宽度，或者元素内含的百分比*/
  overflow: hidden;
  /*溢出的部分隐藏*/
  white-space: nowrap;
  /*文本不换行*/
  text-overflow: ellipsis;
  /*ellipsis:文本溢出显示省略号（...）；clip：不显示省略标记（...），而是简单的裁切*/
}

@media (min-width: 1000px) and (max-width: 1680px) {
  /*.leftCont /deep/ .ant-card-body{*/
  /*  padding: 24px 7px!important;*/
  /*  padding-bottom: 24px!important;*/
  /*}*/
  .coverSpan {
    padding-top: 1.5% !important;
    font-size: 14px !important;
  }
}
</style>