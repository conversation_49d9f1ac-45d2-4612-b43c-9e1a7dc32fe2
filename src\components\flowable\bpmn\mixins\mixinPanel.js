import showConfig from './showConfig'

export default {
    props: {
        modeler: {
            type: Object,
            required: true
        },
        element: {
            type: Object,
            required: true
        },
        descriptor: {
            type: String,
            default: 'flowable'
        }
    },
    created(){
        let yqsm = this.$store.state.yqsm
        if(yqsm && yqsm.commandStackObj){
            this.updateActiveName(this.$store.state.yqsm.commandStackObj)
        }
    },
    watch: {
        //监听属性改变
        'formData.id': function (val) {
            this.updateProperty('id', val)
        },
        'formData.name': function (val) {
            this.updateProperty('name', val)
        },
        "formData.processCategory": function (val) {
            if (val === "") {
                val = null;
            }
            this.element.businessObject.$parent.targetNamespace = val;
        },
        "formData.tenantId": function (val) {
            this.$emit("setProcessInfo","tenantId",val)
        },
        "formData.candidateStarterUsers": function (val) {
            this.updateProperty("candidateStarterUsers", val, true);
        },
        "formData.candidateStarterGroups": function (val) {
            this.updateProperty("candidateStarterGroups", val, true);
        },
        'formData.documentation': function (val) {
            const documentations = this.element.businessObject.documentation
            // console.log("描述 === ",documentations)
            if(documentations && documentations.length > 0) {
                let oldval = documentations[0].text
                if (val !== oldval) {
                    const documentation = this.modeler.get('moddle').create('bpmn:Documentation', { text: val })
                    const newObjectList = []
                    newObjectList.push(documentation)
                    this.updateProperties({ documentation: newObjectList })
                }
            }
            else if(val){
                const documentation = this.modeler.get('moddle').create('bpmn:Documentation', { text: val })
                const newObjectList = []
                newObjectList.push(documentation)
                this.updateProperties({ documentation: newObjectList })
            }
        }
    },
    computed: {
        descriptorPrefix() {
            return this.descriptor + ':'
        },
        elementType() {
            const bizObj = this.element.businessObject
            return bizObj.eventDefinitions
                ? bizObj.eventDefinitions[0].$type
                : bizObj.$type
        },
        showConfig() {
            return showConfig[this.elementType] || {}
        }
    },
    methods: {
        updateActiveName(actionObj){
            // console.log("撤回啊啊 === ",actionObj)
           let properties =  actionObj.context.properties
            if(properties.extensionElements){
                let values = properties.extensionElements.values;
                let ModdleElement = values[values.length - 1];
                let $type = ModdleElement.$type;
               for(let k in  showConfig.collapseKeys){
                   if(showConfig.collapseKeys[k].includes($type)){
                    this.activeName = k;
                   }
               }
            }
            else{
                if(actionObj.context.properties){
                    let $type = Object.keys(actionObj.context.properties)[0]
                    for(let k in  showConfig.collapseKeys){
                        if(showConfig.collapseKeys[k].includes($type)){
                         this.activeName = k;
                        }
                    }
                }
                else{
                    console.log("撤回啊啊11 === ",actionObj.context.properties)
                }

            }
            this.$store.commit('SET_COMMANSTACKOBJ', null)
        },
        updateProperties(properties) {
            const modeling = this.modeler.get('modeling')
            modeling.updateProperties(this.element, properties)
        },
        updateProperty(propertyName, val, hasDescriptor) {
            if (val === '' && propertyName!=="name") {
                val = undefined
            }
            let properties = {}
            let copyname = propertyName
            if (hasDescriptor) {
                propertyName = this.descriptorPrefix + propertyName
            }
            properties[propertyName] = val
            const modeling = this.modeler.get('modeling')
            if(this.element.businessObject[copyname] !== val){
                // console.log("名称 === ",copyname)
                // console.log("fdjdkfd",this.element.businessObject[copyname],val)
                modeling.updateProperties(this.element, properties)
            }
        },
        getDocumentation() {
            let text = "";
            if (this.element.businessObject) {
                const documentations = this.element.businessObject.documentation
                text = (documentations && documentations.length > 0) ? documentations[0].text : ""
            }
            return text
        },
        convertDescriptorProperties(data) {
            // 移除 flowable: 或 camunda: 等前缀开头的变量，格式化数组
            for (const key in data) {
                if (key.indexOf(this.descriptorPrefix) === 0) {
                    const newKey = key.replace(this.descriptorPrefix, '')
                    data[newKey] = data[key]
                    delete data[key]
                }
            }
            return data
        },
        validate() {
            return new Promise((resolve, reject) => {
                // 验证表单
                for (let k in this.rules) {
                    if (!this.formData[k]) {
                        reject(this.rules[k][0].message)
                    }
                }
                resolve(this.formData)

            })
        },
        parseCDATA(str) {
            if (str) {
                const tmp = str.replace(/<!\[CDATA\[(.+)\]\]>/, '$1')
                const value = tmp.replace(/&lt;!\[CDATA\[(.+)\]\]&gt;/, '$1')
                const result = value.replaceAll("&amp;","&") //wangtao 20221017 暂时将&amp;转义字符还原为&，防止第二次保存的时候将&amp;中的&再次转义
                return result
            }
        }
    },

}
