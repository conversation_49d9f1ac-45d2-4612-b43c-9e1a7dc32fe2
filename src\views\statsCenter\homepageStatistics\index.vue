<template>
  <div class='home-wrapper'>
  <background-card>
    <a-row slot='big-screen-content' class='home-row'>
      <a-col class='home-col-side home-col-side-left'>
        <a-row class='side-row'>
          <a-col class='side-col side-col-top'>
            <device-status :adcode='adcode' :handle-refresh='refreshNum'></device-status>
          </a-col>
          <a-col class='side-col side-col-middle'>
            <usage-rate :adcode='adcode' :handle-refresh='refreshNum'></usage-rate>
          </a-col>
          <a-col class='side-col side-col-bottom'>
            <device-alarm :adcode='adcode' :handle-refresh='refreshNum'></device-alarm>
          </a-col>
        </a-row>
      </a-col>
      <a-col class='home-col-middle'>
        <map-area @changeMapADCode='changeMapADCode' @handleRefresh='handleRefresh'></map-area>
      </a-col>
      <a-col class='home-col-side home-col-side-right'>
        <a-row class='side-row'>
          <a-col class='side-col side-col-top'>
            <service-request :adcode='adcode' :handle-refresh='refreshNum'></service-request>
          </a-col>
          <a-col class='side-col side-col-middle'>
            <assets-statistic :adcode='adcode'  :handle-refresh='refreshNum'></assets-statistic>
          </a-col>
          <a-col class='side-col side-col-bottom'>
            <alarmscroll :adcode='adcode' :handle-refresh='refreshNum'></alarmscroll>
          </a-col>
        </a-row>
      </a-col>
    </a-row>
  </background-card>
  </div>
</template>
<script>
import backgroundCard from '@views/statsCenter/com/backgroundCard.vue'
import deviceStatus from '@views/statsCenter/homepageStatistics/modules/deviceStatus.vue'
import usageRate from '@views/statsCenter/homepageStatistics/modules/usageRate.vue'
import deviceAlarm from '@views/statsCenter/homepageStatistics/modules/deviceAlarm.vue'
import mapChart from '@views/statsCenter/homepageStatistics/modules/mapChart.vue'
import mapArea from '@views/statsCenter/homepageStatistics/modules/mapArea.vue'
import assetsStatistic from '@views/statsCenter/homepageStatistics/modules/assetsStatistic.vue'
import serviceRequest from '@views/statsCenter/homepageStatistics/modules/serviceRequest.vue'
import alarmscroll from '@views/statsCenter/homepageStatistics/modules/alarmscroll.vue'
export default {
  name: "homepageStatistics",
  components: {backgroundCard, deviceStatus, usageRate, deviceAlarm, mapArea,mapChart, assetsStatistic, serviceRequest, alarmscroll },
  data() {
    return {
      adcode: '',
      refreshNum:0,
    }
  },
  mounted() {

  },
  methods: {
    changeMapADCode(adcode) {
      this.adcode=adcode
    },
    handleRefresh(){
      this.refreshNum++
    }
  }
}

</script>

<style scoped lang="less">
.home-wrapper{
  //background:url(/statsCenter/homepage/bg.png) #101f36;
  width: 100%;
  height: 100%;
  padding: 0 0.2rem 0.475rem 0.2rem;//0 16px 38px 16px/80px
  overflow: auto;

  .home-row{
    height: 100%;
    width: 100%;
    display: flex;
    padding: 0 0.2rem;
    flex-flow: row nowrap;
    justify-content: center;
    align-items: start;

    .home-col-side{
      width: 5.25rem;//420/80px
      height: 100%;

      .side-row{
        height: 100%;
        display: flex;
        justify-content: space-around;
        align-items: center;
        flex-flow: column nowrap;

        .side-col{
          width: 100%;
          flex:1;
          //min-height:282px ;
          //background: rgb(42 43 52);
        }
        .side-col-top{
          margin:0 0 0.1rem 0
        }
        .side-col-middle{
          margin: 0.1rem 0
        }
        .side-col-bottom{
          margin: 0.1rem 0 0 0
        }
      }
    }
    .home-col-side-left{
      //padding:0 0.1rem 0 0
    }
    .home-col-side-right{
      //padding: 0 0 0 0.1rem
    }

    .home-col-middle{
      height: 100%;
      width: calc(100% - ( 5.25rem * 2));
      padding: 0 0.2rem
    }
  }
}
</style>