<template>
  <a-modal
    :title="title"
    :width="modalWidth"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭"
    wrapClassName="ant-modal-cust-warp"
    style="top:5%;height: 95%;overflow: auto"
  >
     <div>
         <a-spin :spinning="confirmLoading">
           <a-form ref="form" :model="form" :label-width="85">
            <a-form-item label="审批意见" prop="reason">
                <a-input type="textarea" v-model="form.comment" :rows="4" />
            </a-form-item>
            <a-form-item label="文件上传">
                <j-upload v-model="form.file" :number=5></j-upload>
            </a-form-item>
            <div v-show="form.type==1">
                <a-form-item label="驳回至">
                <a-select
                    v-model="form.backTaskKey"
                    :loading="backLoading"
                    @change="changeBackTask"
                >
                    <a-select-option v-for="(item, i) in backList" :key="i" :value="item.key">{{item.name}}</a-select-option>
                </a-select>
                </a-form-item>
                <a-form-item label="指定原节点审批人" prop="assignees" v-show="form.backTaskKey!=-1" :error="error">
                <a-select
                    v-model="form.assignees"
                    placeholder="请选择"
                    allowClear
                    mode="multiple"
                    :loading="userLoading"
                >
                    <a-select-option v-for="(item, i) in assigneeList" :key="i" :value="item.id">{{item.username}}</a-select-option>
                </a-select>
                </a-form-item>
            </div>
            <a-form-item label="消息通知">
                <a-checkbox v-model="form.sendMessage">站内消息通知</a-checkbox>
                <a-checkbox v-model="form.sendSms" disabled>短信通知</a-checkbox>
                <a-checkbox v-model="form.sendEmail" disabled>邮件通知</a-checkbox>
            </a-form-item>
        </a-form>
        </a-spin>


     </div>
  </a-modal>
</template>
<script>
import pick from "lodash.pick";
  import { mixinDevice } from '@/utils/mixin'
import JUpload from '@/components/jeecg/JUpload'
import { getAction, deleteAction, putAction, postAction } from '@/api/manage'
export default {
    name:'todoSendback',
    mixins:[mixinDevice],
    components:{
        JUpload
    },
    data(){
        return{
            isGateway: false,
            error: "",
            showAssign: false,
            submitLoading: false, // 添加或编辑提交状态
            modalTaskVisible: false,
            modalTaskTitle: "",
            assigneeList: [],
            userLoading: false,
            backLoading: false,
            backList: [
                {
                    key: "-1",
                    realname: "发起人"
                }
            ],
            form: {
                id: "",
                userId: "",
                procInstId: "",
                comment: "",
                file:"",
                type: 0,
                assignees: [],
                backTaskKey: "-1",
                sendMessage: true,
                sendSms: false,
                sendEmail: false
            },
            confirmLoading:false,
            modalWidth: '55%',
            form: this.$form.createForm(this),
            visible: false,
            title: '操作',
            url: {
                getBackList:'/actTask/getBackList/',
                passAll:'/actTask/passAll/',
                backAll:'/actTask/backAll/',
                getNextNode:'/activiti_process/getNextNode',
            }
        }
    },
    methods:{
        changeBackTask(v) {
      if (v == "-1") {
        return;
      }
      this.userLoading = true;
      getAction(this.url.getNode+v).then(res => {
        this.userLoading = false;
        if (res.success) {
          if (res.result.users && res.result.users.length > 0) {
            this.assigneeList = res.result.users;
            // 默认勾选
            let ids = [];
            res.result.users.forEach(e => {
              ids.push(e.username);
            });
            this.form.assignees = ids;
          }
        }
      });
    },
        edit(v) {
            this.form.id = v.id;
            this.form.procInstId = v.procInstId;
            this.form.procDefId = v.procDefId;
            this.form.priority = v.priority;
            this.form.type = 1;
            this.showAssign = false;
            this.visible = true;
            // 获取可驳回节点
            this.backList = [
                {
                key: "-1",
                realname: "发起人"
                }
            ];
            this.form.backTaskKey = "-1";
            this.backLoading = true;
            getAction(this.url.getBackList+v.procInstId).then(res => {
                this.backLoading = false;
                if (res.success) {
                res.result.forEach(e => {
                    this.backList.push(e);
                });
                }
            });
        },
        // 关闭弹框
        close() {
        this.$emit('close')
        this.visible = false
        this.current = 0
        },
        // 提交
        handleOk() {
            this.submitLoading = true;
            var formData = Object.assign({},this.form);
            formData.assignees = formData.assignees.join(",");
                if (formData.type == 1) {
                // 驳回
                if (formData.backTaskKey == "-1") {
                    // 驳回至发起人
                    postAction(this.url.back,formData).then(res => {
                    this.submitLoading = false;
                    if (res.success) {
                        this.$message.success("操作成功");
                        this.modalTaskVisible = false;
                        this.loadData();
                    }
                    });
                } else {
                    // 自定义驳回
                    if (formData.backTaskKey != "-1" && formData.assignees.length < 1) {
                    this.$message.error("请至少选择一个审批人")
                    this.submitLoading = false;
                    return;
                    } else {
                    this.error = "";
                    }
                    postAction(this.url.backToTask,formData).then(res => {
                    this.submitLoading = false;
                    if (res.success) {
                        this.$message.success("操作成功");
                        this.modalTaskVisible = false;
                        this.getDataList();
                    }
                    });
                }
            }
        },
        handleCancel() {
            this.close()
        },
        onIndex(index){
        },
        // tab
        callback(key) {
        },
        // 上传相关
        onCancel() {
            this.previewVisible = false
        },
        async handlePreview(file) {
            if (!file.url && !file.preview) {
                file.preview = await getBase64(file.originFileObj)
            }
            this.previewImage = file.url || file.preview
            this.previewVisible = true
        },
        handleChange({ fileList }) {
            this.fileList = fileList
        }
    }
}
</script>
<style scoped></style>
