<template>
  <svg width="100" height="30" version="1.1" xmlns="http://www.w3.org/2000/svg">
    <!-- <line stroke-dasharray="5, 5" x1="10" y1="10" x2="190" y2="10" />
    <line stroke-dasharray="5, 10" x1="10" y1="30" x2="190" y2="30" />
    <line stroke-dasharray="10, 5" x1="10" y1="50" x2="190" y2="50" />
    <line stroke-dasharray="5, 1" x1="10" y1="70" x2="190" y2="70" />
    <line stroke-dasharray="1, 5" x1="10" y1="90" x2="190" y2="90" />
    <line stroke-dasharray="0.9" x1="10" y1="110" x2="190" y2="110" />
    <line stroke-dasharray="15, 10, 5" x1="10" y1="130" x2="190" y2="130" />
    <line stroke-dasharray="15, 10, 5, 10" x1="10" y1="150" x2="190" y2="150" />
    <line stroke-dasharray="15, 10, 5, 10, 15" x1="10" y1="170" x2="190" y2="170" />
    <line stroke-dasharray="5, 5, 1, 5" x1="10" y1="190" x2="190" y2="190" /> -->
    <line :stroke-dasharray="dashType" x1="0" y1="15" x2="90" y2="15" />
  </svg>
</template>

<script>
export default {
    props:{
        dashType:{
            type:String,
            default:"5, 5"
        }
    },
    data(){
        return{
            arrTest:"5, 5"
        }
    }
}
</script>

<style>
line {
  stroke: black;
  stroke-width: 2;
}
</style>