<template>
  <div style="height:100%;">
    <keep-alive exclude='ItilInDepotDetails'>
      <component :is="pageName" :data="data" />
    </keep-alive >
  </div>
</template>
<script>
import ItilInDepotTable from './ItilInDepotTable'
import ItilInDepotDetails from './ItilInDepotDetails'
export default {
  name: "ItilInDepotList",
  data() {
    return {
      isActive: 0,
      data:{}
    };
  },
  components: {
    ItilInDepotTable,
    ItilInDepotDetails
  },
  created(){
    this.pButton1(0);
  },
  //使用计算属性
  computed: {
    pageName() {
      switch (this.isActive) {
        case 0:
          return "ItilInDepotTable";
          break;
        default:
          return "ItilInDepotDetails";
          break;
      }
    }
  },
  methods: {
    pButton1(index) {
      this.isActive = index;
    },
    pButton2(index,item) {
      this.isActive = index;
      this.data = item;
    }
  }
}
</script>