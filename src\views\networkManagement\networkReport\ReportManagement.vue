<template>
  <a-row :gutter='10' style='height: 100%;overflow: hidden;overflow-y: auto'>
    <a-col :span='24' class='ant-col-row'>
      <network-statistic></network-statistic>
    </a-col>
    <a-col :span='24' class='ant-col-row'>
      <a-row :gutter='[12,12]'>
        <a-col :xl='12' :lg='24' :md='24' class='col'>
          <device-statistic></device-statistic>
        </a-col>
        <a-col :xl='12' :lg='24' :md='24' class='col'>
          <recent-alarm-list></recent-alarm-list>
        </a-col>
      </a-row>
    </a-col>
    <a-col :span='24' class='chart'>
      <a-row :gutter='[12,12]'>
        <a-col :xxl='10' :xl='24' :lg='24' class='col'>
          <ip-usage-statistics></ip-usage-statistics>
        </a-col>
        <a-col :xxl='8' :xl='14' :lg='14' :md='24' class='col'>
          <config-backup></config-backup>
        </a-col>
        <a-col :xxl='6' :xl='10' :lg='10' :md='24' class='col'>
          <backup-changes></backup-changes>
        </a-col>
      </a-row>
    </a-col>
  </a-row>
</template>
<script>
import networkStatistic from './modules/NetworkStatistics.vue'
import deviceStatistic from './modules/NetDeviceStatistic.vue'
import recentAlarmList from './modules/RecentAlarmList.vue'
import IpUsageStatistics from './modules/IPUsageStatistics.vue'
import configBackup from './modules/ConfigBackupStatistics.vue'
import backupChanges from './modules/BackupChangesStatistic.vue'

export default {
  name: 'ReportManagement',
  components: {
    networkStatistic,
    deviceStatistic,
    recentAlarmList,
    IpUsageStatistics,
    configBackup,
    backupChanges
  },
  data() {
    return {}
  }
}
</script>
<style scoped lang='less'>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
.ant-col-row{
  margin-bottom: 9px;
  padding-left: 5px;
  padding-right: 5px;
}
.col{
  height: 390px;
}
@media screen and (min-width: 1600px) {
  .chart >.ant-row>.ant-col{
    padding: 6px 6px 0px !important;
  }
}
@media (min-width: 992px) and (max-width: 1600px) {
  .chart >.ant-row>.ant-col:not(:first-child){
    padding: 6px 6px 0px !important;
  }
}
@media screen and (max-width: 992px) {
  .chart >.ant-row>.ant-col:last-child{
    padding: 6px 6px 0px !important;
  }
}
</style>