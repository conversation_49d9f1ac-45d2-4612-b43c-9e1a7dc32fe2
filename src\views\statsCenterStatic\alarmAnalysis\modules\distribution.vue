<template>
  <card-frame :title='"告警级别分布"' :sub-title='""'>
    <div slot='bodySlot' style="height: 100%;width: 100%;" id="annular">
    </div>
  </card-frame>
</template>

<script>
  import echarts from 'echarts/lib/echarts'
  import cardFrame from '@views/statsCenter/com/cardFrame.vue'
  import {
    heightPixel,
    widthPixel
  } from '@views/statsCenter/com/calculatePixel'
  import {
    getAction
  } from '@/api/manage'
  export default {
    components: {
      cardFrame
    },
    data() {
      return {
        screenHeight: window.innerHeight,
        url: {
          level: 'data-analysis/alarm/count/level'
        }
      };
    },
    mounted() {
      this.getLevel()
      window.addEventListener('resize', this.handleResize, true)
    },
    methods: {
      handleResize() {
        this.screenHeight = window.innerHeight
        this.getLevel()
      },
      getLevel() {
        getAction(location.origin + "/statsCenter/mock/alarmData.json").then((res) => {
          this.annular(res.distribution)
        })
      },
      annular(data) {
        let name = data.name
        let value = data.value
        let color = data.color
        let num = data.num
        let all = num.reduce((a, b) => {
          return a + b;
        }, 0)
        let list = []
        for (let i in value) {
          list.push({
            value: value[i].value,
            name: name[i],
            itemStyle: {
              color: color[i]
            }
          }, {
            value: 0.01,
            name: '',
            label: {
              show: false
            },
            labelLine: {
              lineStyle: {
                color: 'rgba(0,0,0,0)'
              }
            },
            itemStyle: {
              labelLine: {
                show: false
              },
              color: 'rgba(0,0,0,.3)',
            }
          })
        }
        let myChart = this.$echarts.init(document.getElementById('annular'))
        myChart.setOption({
          color: color,
          title: {
            text: '告警',
            subtext: '级别',
            textStyle: {
              color: '#EBFDFD',
              fontSize: widthPixel(20),
              align: 'center',
              textShadowColor: '#fff',
              textShadowBlur: 5
            },
            subtextStyle: {
              fontSize: widthPixel(20),
              color: '#EBFDFD',
              fontWeight: 800,
              textShadowColor: '#fff',
              textShadowBlur: 5
            },
            left: 'center',
            top: this.screenHeight > 1000 ? '29%' : '27%',
          },
          legend: {
            type: 'scroll',
            selectedMode: false,
            itemWidth: 10,
            itemHeight: 10,
            bottom: 10,
            data: name,
            backgroundColor: 'rgba(107, 105, 105, .1)',
            padding: [0, 30, 20, 30],
            textStyle: {
              fontSize: 12,
              color: '#ffffff',
              padding: [27, 0, 0, 0],
              rich: {
                a0: {
                  color: color[0] ? color[0] : 'green',
                },
                a1: {
                  color: color[1] ? color[1] : 'yellow',
                },
                a2: {
                  color: color[2] ? color[2] : 'red',
                },
                a3: {
                  color: color[3] ? color[3] : 'pink',
                },
                a4: {
                  color: color[4] ? color[4] : 'orange',
                },
                a5: {
                  color: color[5] ? color[5] : 'red',
                },
                b: {
                  color: 'fff'
                },
              },
            },
            formatter: (name) => {
              let leValue = ''
              let leName = ''
              let leIndex = 0
              for (let index = 0; index < value.length; index++) {
                if (value[index].name == name) {
                  leValue = value[index].value
                  leName = value[index].name
                  leIndex = index < 6 ? index : 5
                }
              }
              if (all) {
                return `${leName} \n \n{a${leIndex}|${(leValue / all * 100).toFixed(0) + '%'}} ${leValue}`
              } else {
                return `${leName} \n \n{a${leIndex}|0%} ${leValue}`
              }
            },
          },
          series: [{
              type: 'pie',
              radius: ['44%', '50%'],
              center: ['50%', '37%'],
              data: list,
              hoverAnimation: false,
              minAngle: 5,
              label: {
                normal: {
                  rich: {
                    name: {
                      color: '#ffffff'
                    },
                  },
                  valueColor: color,
                  show: true,
                  position: 'outer',
                  padding: [0, 0, 0, 0],
                  fontSize: 12,
                  formatter: function (params) {
                    const formatter = `{name|${params.name}}   ${(params.percent).toFixed(0)}%`
                    return formatter
                  }
                }
              },
              labelLine: {
                length: 5,
                length2: 40,
                lineStyle: {
                  color: '#ffffff'
                }
              },
            }, {
              type: 'pie',
              radius: ['38%', '45%'],
              center: ['50%', '37%'],
              data: list,
              hoverAnimation: false,
              minAngle: 5,
              itemStyle: {
                color: color,
                opacity: 0.2,
              },
              label: {
                show: false,
              },
              labelLine: {
                show: false,
              },
            },
            {
              type: 'pie',
              radius: ['31%', '32%'],
              center: ['50%', '37%'],
              data: value,
              hoverAnimation: false,
              label: {
                show: false
              },
              itemStyle: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [{
                      offset: 0,
                      color: 'rgba(255, 255, 255, .6)',
                    },
                    {
                      offset: 0.25,
                      color: 'rgba(67, 216, 255, .2)',
                    },
                    {
                      offset: 0.75,
                      color: 'rgba(67, 216, 255, .2)',
                    },
                    {
                      offset: 1,
                      color: 'rgba(255, 255, 255, .3)',
                    },
                  ],
                  false
                ),
              },
            },
            {
              type: 'pie',
              radius: ['25%', '31%'],
              center: ['50%', '37%'],
              data: [{
                name: '',
                value: 0
              }],
              hoverAnimation: false,
              label: {
                show: false
              },
              itemStyle: {
                color: {
                  type: 'radial',
                  x: 0.5,
                  y: 0.5,
                  r: 0.5,
                  colorStops: [{
                      offset: 0,
                      color: 'rgba(134,198,245,0)', // 0% 处的颜色
                    },
                    {
                      offset: 0.6,
                      color: 'rgba(0,0,0,0)', // 50% 处的颜色
                    },
                    {
                      offset: 1,
                      color: 'rgba(49,191,230,0.2)', // 100% 处的颜色
                    },
                  ],
                  globalCoord: false, // 缺省为 false
                }
              },
            },
          ]
        })
        window.addEventListener("resize", () => {
          myChart.resize();
        });
      },

    }
  }
</script>

<style scoped lang="less">
</style>