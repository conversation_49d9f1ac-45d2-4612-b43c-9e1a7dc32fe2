<template>
  <a-form-item label="JSON对象" :labelCol="labelCol" :wrapperCol="wrapperCol">
    <a-table bordered :showHeader="true" :pagination="false" :row-key="
        (record, index) => {
          return index
        }
      " :columns="columns" :data-source="data" :scroll="data.length > 0 ? { x: 'max-content' } : {}">
      <template slot="name" slot-scope="text">
        {{ text }}
      </template>

      <span slot="action" slot-scope="text, record">
        <a @click="handleEdit(record)">编辑</a>
        <a-divider type="vertical" />
        <a-dropdown v-if="flowModel">
          <a class="ant-dropdown-link">更多
            <a-icon type="down" />
          </a>
          <a-menu slot="overlay">
            <a-menu-item>
              <a style="color: #409eff" @click="process(record)">{{
                record.chainId != '' && record.chainId != null ? '编辑流程' : '配置流程'
              }}</a>
            </a-menu-item>
            <a-menu-item v-if="record.chainId != '' && record.chainId != null">
              <a-popconfirm title="确定删除流程配置吗?" @confirm="() => processDel(record)">
                <a style="color: #409eff">删除流程</a>
              </a-popconfirm>
            </a-menu-item>
            <a-menu-item>
              <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                <a style="color: #409eff">删除</a>
              </a-popconfirm>
            </a-menu-item>
          </a-menu>
        </a-dropdown>
        <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)" v-if="!flowModel">
          <a>删除</a>
        </a-popconfirm>
      </span>
    </a-table>
    <proerty-metadata-modal :productInfo="productInfo" ref="modalForm" @ok="modalFormOk"></proerty-metadata-modal>
    <process-manage ref="processManage" @ok="modalFormOk2"></process-manage>
  </a-form-item>
</template>

<script>
  import {
    deleteAction,
    getAction,
    downFile,
    getFileAccessHttpUrl
  } from '@/api/manage'
  import ProertyMetadataModal from '@/views/devicesystem/modules/ProertyMetadataModal.vue'
  import processManage from '@/views/devicesystem/modules/processManage.vue'
  export default {
    name: 'objectMetadata',
    components: {
      processManage,
      ProertyMetadataModal: () => import('@/views/devicesystem/modules/ProertyMetadataModal.vue'),
    },
    props: {
      childrenDataList: Array,
      productInfo: {},
      pCode: '',
      flowModel: Boolean,
    },
    data() {
      return {
        records: {},
        labelCol: {
          xs: {
            span: 5
          },
          sm: {
            span: 5
          },
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          },
        },
        columns: [{
            title: '名称',
            dataIndex: 'name',
            scopedSlots: {
              customRender: 'name'
            },
          },
          {
            title: '属性标识',
            dataIndex: 'code',
          },
          {
            title: '操作',
            dataIndex: 'action',
            scopedSlots: {
              customRender: 'action'
            },
            align: 'center',
            width: '120px',
          },
        ],
        url: {
          // list:'/product/product/list',
          delete: '/product/proertyMetadata/delete',
          deleteChain: '/flow/chain/delete',
          queryChainList: '/flow/chain/queryChain',
        },
        data: [],
      }
    },
    watch: {
      dictCode: {
        immediate: true,
        handler() {
          this.initData()
        },
      },
      //监听
      childrenDataList: {
        handler: function (val, oldval) {
          if (val != oldval) {
            this.$nextTick(() => {
              this.initData()
            })
          }
        },
        immediate: true, //关键
        deep: true,
      },
    },
    methods: {
      getChain(data) {
        if (data && data.chainId != '' && data.chainId != null) {
          getAction(this.url.queryChainList, {
            chainId: data.chainId
          }).then((res) => {
            this.records = res.result
            this.$refs.processManage.edit(Object.assign({}, this.records), data)
          })
        }
      },
      process(info) {
        info.pCode = this.pCode
        if (info && info.chainId != null && info.chainId != '') {
          this.getChain(info)
        } else {
          this.records = {}
          this.$refs.processManage.edit(Object.assign({}, this.records), info)
        }
      },
      processDel(record) {
        if (record.id) {
          deleteAction(this.url.deleteChain, {
            chainId: record.chainId
          }).then((res) => {
            if (res.success) {
              record.topoDataJson = ''
              record.chainDesc = ''
              record.chainId = ''
              record.chainName = ''
              record.chain = ''
              record.params = ''
              this.$message.success(res.message)
            } else {
              this.$message.warning(res.message)
            }
            this.chainIdJudge = false
          })
        } else {
          this.$message.warning('未配置流程')
        }
      },
      initData() {
        this.data = this.childrenDataList
      },
      handleEdit(record) {
        const that = this
        that.$refs.modalForm.edit(record)
        that.$refs.modalForm.title = '编辑'
        that.$refs.modalForm.disableSubmit = false
        // this.$emit('func',  that.data);
      },
      modalFormOk(info) {
        if (undefined != info && undefined != info.id) {
          const that = this
          // 新增/修改 成功时，重载列表
          var addData = []
          for (var i = 0; i < that.data.length; i++) {
            if (that.data[i].id != info.id) {
              addData.push(that.data[i])
            }
          }
          addData.push(info)
          that.data = addData
          that.$emit('func', that.data)
        }
      },
      modalFormOk2(data) {
        this.getChain(data.chainId)
        // 新增/修改 成功时，重载列表
        // this.loadData()
      },
      handleDelete(id) {
        const that = this
        var addData = []
        deleteAction(that.url.delete, {
          id: id
        }).then((res) => {
          if (res.success) {
            that.$message.success(res.message)
          } else {
            that.$message.warning(res.message)
          }
        })
        for (var i = 0; i < this.data.length; i++) {
          if (this.data[i].id != id) {
            addData.push(this.data[i])
          }
        }
        that.data = addData
        that.$emit('func', that.data)
        // that.$emit('getTableData');
      },
    },
  }
</script>

<style>
</style>