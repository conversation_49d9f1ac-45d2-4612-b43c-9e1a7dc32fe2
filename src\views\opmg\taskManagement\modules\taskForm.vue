<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="disableSubmit">
      <a-form-model slot="detail" ref='form' :model="model" :rules='validatorRules' v-bind='formItemLayout'>
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="任务名称" prop="taskName">
              <a-input v-model="model.taskName" :allowClear="true" autocomplete="off"
                       placeholder="请输入任务名称" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="计划时间" prop="plannedTime">
              <a-range-picker
                style='width: 100%'
                :placeholder="['开始时间', '截止时间']"
                v-model="model.plannedTime"
                format="YYYY-MM-DD HH:mm"
                :show-time="showTime"
                :disabled-date="disablePastDates"
                @change="changePlannedTime"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="任务类型" prop="taskType">
              <j-dict-select-tag
                v-model="model.taskType"
                :label-in-value="true"
                placeholder="请选择任务类型"
                dictCode="taskType"
                @change="changeTaskType"></j-dict-select-tag>
            </a-form-model-item>
          </a-col>
          <a-col :span="24" v-if="model.taskType&&model.taskType!=='file_up'">
            <a-form-model-item label="升级包" prop="softwareUpgrade">
              <a-cascader
                v-model='model.softwareUpgrade'
                :field-names="{ label: 'label', value: 'value',text:'text',leaf:'isLeaf', children: 'items' }"
                :load-data='loadData'
                :multiple='true'
                :options='softwareOptions'
                change-on-select
                placeholder='请选择升级包'
                @change='onChangeSoftwarePath'>
                <template slot='displayRender' slot-scope='{labels, selectedOptions}'>
                  {{ softwareDisplayRender(selectedOptions) }}
                </template>
              </a-cascader>
            </a-form-model-item>
          </a-col>
          <template v-if="model.taskType&&extendFields&&extendFields.length>0">
            <template v-for='item in extendFields'>
              <a-col v-if="item.extendType" :key='item.extendCode' :span="24" v-bind="item.spanLayout">
                <a-form-model-item :prop="item.extendCode" v-bind='item.formItemLayout'>
                  <template v-if="item.extendType === 'switch'">
                    <span slot='label'>{{ item.extendName }}</span>
                    <a-switch checked-children='是' un-checked-children='否'
                              v-model='model[item.extendCode]'></a-switch>
                  </template>
                  <template v-else-if="item.extendType === 'upload'">
                  <span slot='label'>
                  <span>{{ item.extendName }}</span>
                  <a-popover title='说明' v-if="item.labelTips">
                    <template slot='content'>
                      <p v-html="item.labelTips"></p>
                    </template>
                    <a-icon style='margin-left:5px;font-size: 20px; line-height: 40px' theme='twoTone'
                            type='question-circle' />
                  </a-popover>
                </span>
                    <yq-file-upload
                      :is-require-md5="true"
                      :multiple="item.multiple"
                      :number="item.number"
                      :return-url="false"
                      :before-upload-fun="(file)=>checkBeforeUpload(file,true,item.maxFileSize,item.unit)"
                      v-model="model[item.extendCode]"
                      :get-file-json="item.customParsingFun"
                      @change="item.changeExtendFile($event,model)"
                    >
                    </yq-file-upload>
                  </template>
                  <template v-else-if="item.extendType === 'inputString'">
                    <span slot='label'>{{ item.extendName }}</span>
                    <a-input v-model="model[item.extendCode]" :placeholder="item.placeholder" :allowClear="true"
                             autocomplete="off" />
                  </template>
                </a-form-model-item>
              </a-col>
            </template>

          </template>
          <a-col :span="24">
            <a-form-model-item label="下发策略" prop="policyId">
              <a-select
                v-model="model.policyId"
                show-search
                :allow-clear='true'
                placeholder="请选择下发策略"
              >
                <a-select-option v-for='item in policyList' :key='"policy_" + item.id' :label='item.name'
                                 :value='item.id'>
                  {{ item.name }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="通知方式" prop="noticeType">
              <j-dict-select-tag v-model="model.noticeType" placeholder="请选择通知方式"
                                 dictCode="softwareTaskNotification"></j-dict-select-tag>
            </a-form-model-item>
          </a-col>
          <!--          <a-col :span="24" v-if="model.noticeType&&model.noticeType==3">
                      <a-form-model-item label="通知内容" prop="noticeContext">
                        <a-textarea placeholder="请输入通知内容"  v-model="model.noticeContext" :allow-clear='true'></a-textarea>
                      </a-form-model-item>
                    </a-col>-->
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

import moment from 'moment'
import dayjs from 'dayjs'
import {
  httpAction,
  getAction
} from '@/api/manage'
import JFormContainer from '@/components/jeecg/JFormContainer'
import JDate from '@/components/jeecg/JDate'
import JSelectUserByDep from '@comp/jeecgbiz/JSelectUserByDep.vue'
import JSelectTerminalByDep from '@comp/jeecgbiz/JSelectTerminalByDep.vue'
import { getExtendFields } from '@views/opmg/taskManagement/modules/extendFields'
import { getDictData } from '@views/opmg/taskManagement/modules/comFunctions'
import yqFileUpload from '@comp/yq/yqUpload/yqFileUpload.vue'
import { checkBeforeUpload } from '@comp/yq/yqUpload/YqUploadCommonFuns'

export default {
  name: 'strategyForm',
  components: {
    JSelectUserByDep,
    JFormContainer,
    JDate,
    JSelectTerminalByDep,
    yqFileUpload
  },
  props: {
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  data() {
    return {
      moment,
      model: {},
      showTime: { defaultValue: [moment(), moment('23:59', 'HH:mm')], format: 'HH:mm' },
      disableSubmit: false,
      confirmLoading: false,
      taskTypeList: [],
      softwareOptions: [],
      policyList: [],
      noticeList: [],
      extendFields: [],
      lastSoftwareType: '',
      formItemLayout: {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 24 },
          md: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 24 },
          md: { span: 16 }
        }
      },
      validatorRules: {
        taskName: [
          { required: true, message: '请输入任务名称', trigger: 'change' },
          { min: 1, max: 20, message: '任务名称长度应在【1-20】字符之间', trigger: 'change' }
        ],
        plannedTime: [
          // { required: true, message: '请选择任务计划时间', trigger: 'change'},
          { required: true, validator: this.validatePlannedTime }
        ],
        taskType: [
          { required: true, message: '请选择任务类型', trigger: 'change' }
        ],
        softwareUpgrade: [
          { required: true, validator: this.validateSoftwareUpgrade }
        ],
        policyId: [
          { required: true, message: '请选择下发策略', trigger: 'change' }
        ],
        noticeType: [
          { required: true, message: '请输入通知内容', trigger: 'change' }
        ],
        noticeContext: [
          { required: true, message: '请输入通知内容', trigger: 'change' },
          { min: 1, max: 200, message: '通知内容长度应在【1-200】字符之间', trigger: 'change' }
        ]
      },
      url: {
        add: '/software/softwareTask/add',
        querySoftware: '/software/softwareInfoManage/listByType',//根据任务类型获取软件
        softwareSublevel: '/software/upgradePackageManage/list',//根据软件获取软件下的升级包
        policy: '/software/policy/list'//获取策略
      }
    }
  },
  created() {
    this.initDictConfig()
    this.getPolicyList()
  },
  methods: {
    checkBeforeUpload,
    add() {
      this.edit({})
    },
    edit(record) {
      this.model = Object.assign({}, record)
      this.$nextTick(() => {
        this.$refs.form.clearValidate()
        this.visible = true
      })
    },
    submitForm() {
      const that = this
      // 触发表单验证
      that.$refs.form.validate((err, values) => {
        if (err && !that.confirmLoading) {
          that.confirmLoading = true
          let formData = {}
          //扩展字段数据
          if (that.extendFields && that.extendFields.length > 0) {
            formData['extendList'] = that.setExtendFieldsData()
          }
          //获取共有字段值（任务名称、计划时间（开始、结束）、任务类型、策略、通知方式、通知内容）
          let keys = ['taskName', 'plannedStartTime', 'plannedEndTime', 'taskType', 'policyId', 'noticeType', 'noticeContext']
          keys.forEach(key => {
            if (key in this.model) {
              formData[key] = this.model[key]
            }
          })
          if (this.model.softwareUpgrade && this.model.softwareUpgrade.length > 0) {
            formData['softwareId'] = this.model.softwareUpgrade[0]
            formData['softwarePatchId'] = this.model.softwareUpgrade[1]
          }
          let httpurl = that.url.add
          let method = 'post'
          httpAction(httpurl, formData, method).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.$emit('ok')
            } else {
              that.$message.warning(res.message)
            }
          })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    /**
     * 整理扩展字段数据
     * */
    setExtendFieldsData() {
      /*组装扩展字段数据*/
      const sourceArray = this.extendFields
      let extendArray = []
      if (sourceArray && sourceArray.length > 0) {
        const keys = ['extendName', 'extendCode', 'extendValue', 'extendType'] // 需要提取的属性列表
        for (let i = 0; i < sourceArray.length; i++) {
          let extendObj = {}
          let sourceObj = sourceArray[i]
          keys.forEach(key => {
            if (key in sourceObj) {
              extendObj[key] = sourceObj[key]
            }
          })
          if (Object.keys(sourceObj).includes('resultDataFun')) {
            extendObj.extendValue = sourceObj.resultDataFun(this.model[sourceObj.extendCode])
          } else {
            extendObj.extendValue = this.model[sourceObj.extendCode]
          }
          extendArray.push(extendObj)
        }
      }
      return extendArray.length > 0 ? extendArray : undefined
    },
    // 禁用当前日期之前的日期
    disablePastDates(current) {
      return current && current < moment().startOf('day')
    },
    /**
     *获取任务开始、结束计划时间
     * */
    changePlannedTime(date, dateString) {
      this.model.plannedStartTime = dateString[0]
      this.model.plannedEndTime = dateString[1]
    },
    /**
     * 自定义计划时间校验规则
     * */
    validatePlannedTime(rule, value, callback) {
      if (rule.required) {
        if (value) {
          const nowMinusTenMinutes = moment().subtract(10, 'minutes')
          console.log('nowMinusTenMinutes===', nowMinusTenMinutes)
          if (value[0] < nowMinusTenMinutes) {
            callback('开始时间不应晚于当前时间10min之多')
          } else if (value[1] < moment()) {
            callback('结束时间不可晚于当前时间')
          } else if (value[0] > value[1]) {
            callback('结束时间不可晚于开始时间')
          } else if (value[1] - value[0] < 3600000) {
            callback('结束时间与开始时间相差应大于等于1h')
          } else {
            callback()
          }
        } else {
          callback('请选择任务计划开始、结束时间')
        }
      } else {
        callback()
      }
    },
    /**
     * 切换任务类型，添加或者字段，并加载对应升级包数据，若是文件下发类型，还需显示拓展字段
     * @param {String} e - 任务类型取值
     * */
    async changeTaskType(e) {
      this.model.softwareUpgrade && this.model.softwareUpgrade.splice(0)
      this.extendFields = []

      /*设置扩展内容*/
      await this.setExtendItem(e)
      /*非下发文件类型时，需要加载对应升级包数据*/
      if (e && e !== 'file_up') {
        Promise.all([
          //获取升级包一级数据
          this.getRootSoftwarePath()
        ]).then((result) => {
          let proResult = result.every((item, index) => {
            if (index === 0) {
              this.softwareOptions = item.data
            }
            return item.success === true
          })
        })
      }
    },
    /**
     * 根据任务类型，设置扩展字段
     * @param {String} key - 任务类型value值，与extendFields属性对应
     * */
    async setExtendItem(key) {
      this.extendFields = await getExtendFields(key)
      if (this.extendFields && this.extendFields.length > 0) {
        this.extendFields.forEach(item => {
          if (!this.model.hasOwnProperty(item.extendCode)) {
            // 使用 $set 确保响应式更新
            this.$set(this.model, item.extendCode, item.extendValue)
            //this.model = { ...this.model, [item.extendCode]: item.extendValue };
          }
          // 更新验证规则
          this.$delete(this.validatorRules, item.extendCode)
          if (item.validatorRules) {
            this.$set(this.validatorRules, item.extendCode, item.validatorRules)
            //this.validatorRules = { ...this.validatorRules, [item.extendCode]: rules };
          }
        })
      }
    },
    /**
     * 获取升级包第一级数据
     */
    getRootSoftwarePath() {
      let that = this
      return new Promise(function(resolve, reject) {
        getAction(that.url.querySoftware, { taskType: that.model.taskType }).then((res) => {
          if (res.success) {
            let list = []
            if (res.result && res.result.length > 0) {
              res.result.map((item) => {
                let m = {
                  id: item.id,
                  text: item.softwareName,
                  label: that.getIconName('software', item.softwareName),
                  value: item.id,
                  type: 'software',
                  softwareType: item.softwareType,
                  softwareId: item.softwareId,
                  key: item.id,
                  isLeaf: false,
                  items: item.items && item.items.length > 0 ? item.items : undefined
                }
                list.push(m)
              })
            }
            resolve({
              success: true,
              data: list,
              message: res.message
            })
          } else {
            reject({
              success: false,
              data: undefined,
              message: res.message
            })
          }
        }).catch((res) => {
          reject({
            success: false,
            data: undefined,
            message: res.message
          })
        })
      })
    },
    /**
     *根据升级包类型，设置前置图标，用于区分软件和软件下的升级包
     * @param {String} type - 升级包类，取值software、subSoftware
     * @param {String} text - 每个节点的文本内容
     */
    getIconName(type, text) {
      let label = ''
      switch (type) {
        case 'software':
          label = <div style="display: inline-block;white-space: nowrap;">
            <a-icon type="folder" style="color:#409eff;padding-right:5px" />
            {text}</div>
          break
        case 'subSoftware':
          label = <div style="display: inline-block;white-space: nowrap">
            <a-icon type="file" style="color:#409eff;margin-right:5px" />
            {text}</div>
          break
      }
      return label
    },
    /*异步加载升级包分支数据*/
    async loadData(selectedOptions) {
      let that = this
      return new Promise(function(resolve, reject) {
        const targetOption = selectedOptions[selectedOptions.length - 1]
        if (targetOption.type == 'software') {
          targetOption.loading = true
          that.getSoftwareSublevelData(targetOption).then((res) => {
            if (res.success) {
              resolve(res)
            } else {
              that.$message.warning('发生异常，请稍后重试')
              reject(res)
            }
            targetOption.loading = false
          })
        } else {
          targetOption.loading = false
          targetOption.isLeaf = true
          targetOption.items = undefined
          resolve({
            data: targetOption,
            success: true,
            message: '操作成功'
          })
        }
      })
    },
    /* 根据当前所选产品名称id，获取该级下的子级数据*/
    getSoftwareSublevelData(targetOption) {
      let that = this
      return new Promise(function(resolve, reject) {
        getAction(that.url.softwareSublevel, { softwareId: targetOption.id, pageSize: -1 }).then((res) => {
          if (res.success) {
            if (res.result.records && res.result.records.length > 0) {
              targetOption.items = res.result.records.map((item) => {
                let param = {
                  id: item.id,
                  // text: item.patchName + "_" + item.patchVersion,
                  text: item.patchVersion,
                  // label: that.getIconName('subSoftware', item.patchName + "_" + item.patchVersion),
                  label: that.getIconName('subSoftware', item.patchVersion),
                  value: item.id,
                  type: 'subSoftware',
                  softwareType: targetOption.softwareType,
                  softwareId: item.softwareId,
                  key: item.id,
                  isLeaf: true,
                  items: item.items && item.items.length > 0 ? item.items : undefined
                }
                return param
              })
            } else {
              targetOption.items = undefined
              targetOption.isLeaf = true
            }
            targetOption.loading = false
            resolve({
              data: targetOption,
              success: true,
              message: '操作成功'
            })
          }
        }).catch(() => {
          targetOption.loading = false
          reject({
            data: targetOption,
            success: false,
            message: '操作失败'
          })
        })
      })
    },
    /**
     * 切换软件及升级包
     * @param {String} value - 当前所选节点value值（软件id或者软件升级包id）
     * @param {String} nodeData - 当前所选节点所有属性信息
     * */
    onChangeSoftwarePath(value, nodeData) {
      if (nodeData && nodeData.length > 0) {
        this.lastSoftwareType = nodeData[nodeData.length - 1].type


        /*        this.lastProductType = nodeData[nodeData.length - 1].type
        let proId = nodeData[nodeData.length - 1].id
        if (this.lastProductType == 'product') {
          //最新产品id和上次选择产品id不同时，重新加载数据
          if (proId != this.lastProductId) {
            //产品选择发生变化
            //1、重新获取设备名称下拉数据
            this.resertDeviceName()
            this.getDeviceList(proId)
            //2、重新获取指标数据
            this.resertAlarmRules()
            this.getSubjectIndex(proId).then((res) => {
              this.subjectIndexList = res.data
            })
          }
        } else {
          this.resertDeviceName()
          this.resertAlarmRules()
        }
        this.lastProductId = proId
      } else {
        this.lastProductType = ''
        this.lastProductId = ''
        this.resertDeviceName()
        this.resertAlarmRules()*/
      }
    },
    /**
     * 选择升级包后，设置输入框中的显示内容
     * @param ｛Array｝ selectedOptions - 选中数据
     * @returns - 返回输入框显示内容
     */
    softwareDisplayRender(selectedOptions) {
      let options = selectedOptions.map((item) => {
        return item.text
      })
      return options.length > 0 ? options.join('/') : undefined
    },
    /**
     * 校验升级包选择是否正确
     * @param rule
     * @param value
     * @param callback
     * 不能选择到应用名，只能选择到应用下的软件升级包
     */
    validateSoftwareUpgrade(rule, value, callback) {
      let that = this
      if (rule.required) {
        if (value && value.length > 0) {
          if (that.lastSoftwareType == 'subSoftware') {
            callback()
          } else if (that.lastSoftwareType == 'software') {
            callback('勿选择应用名，请选择到应用下的升级包')
          } else {
            callback()
          }
        } else {
          callback('请选择升级包')
        }
      } else {
        callback()
      }
    },
    /**
     * 调用加载字典数据方法
     */
    initDictConfig() {
      if (this.taskTypeList.length <= 0) {
        Promise.all([getDictData('taskType')]).then((res) => {
          this.taskTypeList = res[0]
        })
      }
    },
    /**
     * 获取下发策略数据
     * */
    getPolicyList() {
      getAction(this.url.policy, { pageSize: -1 }).then((res) => {
        if (res.success) {
          this.policyList = res.result.records
        } else {
          this.$message.warning(res.message)
          this.policyList = []
        }
      }).catch((err) => {
        this.$message.error(err.message)
        this.policyList = []
      })
    }
  }
}
</script>
<style lang="less" scoped>
::v-deep .two-words > div > label {
  letter-spacing: 4px;
}

::v-deep .two-words > div > label::after {
  letter-spacing: 0px;
}
</style>