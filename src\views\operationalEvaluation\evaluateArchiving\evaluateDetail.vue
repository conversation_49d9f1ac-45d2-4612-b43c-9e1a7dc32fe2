<template>
  <a-row :gutter='10' style='height: 100%' class='vScroll'>
    <a-col style='width: 100%; height: 100%; display: flex; flex-direction: column'>
      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <div>
          <div class="colorBox">
            <span class="colorTotal">历史版本</span>
          </div>
          <div style="position: absolute; right: 24px; top: 18px; z-index: 10">
            <img src="~@assets/return1.png" @click="getGo" style="width: 20px; height: 20px; cursor: pointer" />
          </div>
        </div>
        <a-table ref='table' bordered rowKey='id' :columns='columns' :dataSource='dataSource'
          :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}" :pagination='ipagination' :loading='loading'
          @change='handleTableChange'>
          <template slot="filePath" slot-scope="text,record,index">
            <div style="display:flex; justify-content: center;">
              <alarm-upload v-model="record.filePath" :file-list='fileList' :showUploadList='true' name="file"
                :multiple="false">
              </alarm-upload>
            </div>
          </template>
        </a-table>
      </a-card>
    </a-col>
  </a-row>
</template>

<script>
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import alarmUpload from '@/views/alarmManage/alarmLevel/alarmUpload.vue'
  import {
    YqFormSearchLocation
  } from '@/mixins/YqFormSearchLocation'

  export default {
    name: 'evaluateList',
    mixins: [JeecgListMixin, YqFormSearchLocation],
    components: {
      alarmUpload
    },
    props: {
      data: {
        type: Object
      }
    },
    data() {
      return {
        description: '评估归档管理页面',
        formItemLayout: {
          labelCol: {
            style: 'width:90px'
          },
          wrapperCol: {
            style: 'width:calc(100% - 90px)'
          }
        },
        queryParam: {},
        fileList: null,
        columns: [{
            title: '项目名称',
            dataIndex: 'projectId_dictText'
          },
          {
            title: '文件路径',
            dataIndex: 'filePath',
            scopedSlots: {
              customRender: 'filePath'
            },
          },
          {
            title: '报告生成时间',
            dataIndex: 'createTime',
            customCell: () => {
              let cellStyle = 'text-align: center;width:300px'
              return {
                style: cellStyle
              }
            }
          },
        ],
        url: {
          list: '/evaluate/reportInfo/pageList',
        },
        disableMixinCreated: true
      }
    },
    created() {
      this.queryParam = {
        projectId: this.data.id,
      };
     this.loadData();
    },
    mounted() {},
    methods: {
      //返回上一级
      getGo() {
        this.$parent.pButton2(0);
      },
    },
  }
</script>
<style lang='less' scoped>
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';

  .colorBox {
    margin-top: 10px;
    margin-bottom: 10px;

    .colorTotal {
      padding-left: 7px;
      border-left: 4px solid #1e3674;
    }
  }
</style>