<template>
  <a-modal
    ref="modal"
    :class="getClass(modalClass)"
    :style="getStyle(modalStyle)"
    :destroyOnClose="destroyOnClose"
    :visible="visible"
    :closable="closable"
    v-bind="_attrs"
    v-on="$listeners"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <slot></slot>

    <template v-if="!isNoTitle" slot="title">
      <div class='header-wrapper'>
        <div class='head-img'></div>
        <div class='left-border'></div>
        <div class='right-border'></div>
        <a-row class="yq-modal-title-row" type="flex">
          <a-col class="left">
            <img class='img' src='/oneClickHelp/localDeviceInfo/head.png' >
            <slot name="title">{{ title }}</slot>
          </a-col>
          <a-col v-if="switchFullscreen" class="right" @click="toggleFullscreen">
            <a-button class="ant-modal-close ant-modal-close-x" ghost type="link" :icon="fullscreenButtonIcon" />
          </a-col>
        </a-row>
      </div>
<!--      <a-row class="yq-modal-title-row" type="flex">
        <a-col class="left">
          <slot name="title">{{ title }}</slot>
        </a-col>
        <a-col v-if="switchFullscreen" class="right" @click="toggleFullscreen">
          <a-button class="ant-modal-close ant-modal-close-x" ghost type="link" :icon="fullscreenButtonIcon" />
        </a-col>
      </a-row>-->
    </template>

    <!-- 处理 scopedSlots -->
    <template v-for="slotName of scopedSlotsKeys" :slot="slotName">
      <slot :name="slotName"></slot>
    </template>

    <!-- 处理 slots -->
    <template v-for="slotName of slotsKeys" v-slot:[slotName]>
      <slot :name="slotName"></slot>
    </template>
  </a-modal>
</template>

<script>
import { getClass, getStyle } from '@/utils/props-util'
import { triggerWindowResizeEvent } from '@/utils/util'

export default {
  name: 'yqModal',
  props: {
    destroyOnClose: {
      type: Boolean,
      default: false,
    },
    title: String,
    // 可使用 .sync 修饰符
    visible: Boolean,
    // 是否全屏弹窗，当全屏时无论如何都会禁止 body 滚动。可使用 .sync 修饰符
    fullscreen: {
      type: Boolean,
      default: false,
    },
    // 是否允许切换全屏（允许后右上角会出现一个按钮）
    switchFullscreen: {
      type: Boolean,
      default: false,
    },
    // 点击确定按钮的时候是否关闭弹窗
    okClose: {
      type: Boolean,
      default: true,
    },
    // 是否设置背景色为灰色 created by weng 20210408
    grayBackcolor: {
      type: Boolean,
      default: false,
    },
    //是否显示右上方关闭按钮
    closable: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      // 内部使用的 slots ，不再处理
      usedSlots: ['title'],
      // 实际控制是否全屏的参数
      innerFullscreen: this.fullscreen,
      isGrayBackcolor: this.grayBackcolor,
    }
  },
  computed: {
    // 一些未处理的参数或特殊处理的参数绑定到 a-modal 上
    _attrs() {
      let attrs = { ...this.$attrs }
      // 如果全屏就将宽度设为 100%
      if (this.innerFullscreen) {
        attrs['width'] = '100%'
      }
      return attrs
    },
    modalClass() {
      return {
        'yq-modal-box': true,
        fullscreen: this.innerFullscreen,
        'no-title': this.isNoTitle,
        'no-footer': this.isNoFooter,
        'no-body-padding': this.isNoBodyPadding,
        'gray-bakcolor': this.isGrayBackcolor,
      }
    },
    modalStyle() {
      let style = {}
      // 如果全屏就将top设为 0
      if (this.innerFullscreen) {
        style['top'] = '0'
      }
      return style
    },
    isNoTitle() {
      return !this.title && !this.allSlotsKeys.includes('title')
    },
    isNoFooter() {
      return this._attrs['footer'] === null
    },
    isNoBodyPadding() {
      return this._attrs['bodyPadding'] === null
    },
    slotsKeys() {
      return Object.keys(this.$slots).filter((key) => !this.usedSlots.includes(key))
    },
    scopedSlotsKeys() {
      return Object.keys(this.$scopedSlots).filter((key) => !this.usedSlots.includes(key))
    },
    allSlotsKeys() {
      return Object.keys(this.$slots).concat(Object.keys(this.$scopedSlots))
    },
    // 切换全屏的按钮图标
    fullscreenButtonIcon() {
      return this.innerFullscreen ? 'fullscreen-exit' : 'fullscreen'
    },
  },
  watch: {
    visible() {
      if (this.visible) {
        this.innerFullscreen = this.fullscreen
      }
    },
    innerFullscreen(val) {
      this.$emit('update:fullscreen', val)
    },
  },
  methods: {
    getClass(clazz) {
      return { ...getClass(this), ...clazz }
    },
    getStyle(style) {
      return { ...getStyle(this), ...style }
    },

    close() {
      this.$emit('update:visible', false)
    },

    handleOk() {
      if (this.okClose) {
        this.close()
      }
    },
    handleCancel() {
      this.close()
    },

    /** 切换全屏 */
    toggleFullscreen() {
      this.innerFullscreen = !this.innerFullscreen
      triggerWindowResizeEvent()
    },
  },
}
</script>

<style lang="less">
.yq-modal-box {

  &.fullscreen {
    top: 0;
    left: 0;
    padding: 0;

    // 兼容1.6.2版本的antdv
    & .ant-modal {
      top: 0;
      padding: 0;
      height: 100vh;
      max-width: 100vw !important;
    }

    & .ant-modal-content {
      height: 100vh;
      border-radius: 0;

      & .ant-modal-body {
        /* title 和 footer 各占 55px */
        height: calc(100% - 55px - 55px);
        overflow: auto;
      }
    }

    &.no-title,
    &.no-footer {
      .ant-modal-body {
        height: calc(100% - 55px);
      }
    }

    &.no-title.no-footer {
      .ant-modal-body {
        height: 100%;
      }
    }

    &.no-body-padding {
      .ant-modal-body {
        padding: 0;
      }
    }
  }

  &.gray-bakcolor {
    & .ant-modal-content {
      & .ant-modal-body {
        background-color: #eee;
      }
    }
  }

  & .ant-modal{
    & .ant-modal-content {
      background-image: linear-gradient(180deg, #101B2F 1%, #050911 85%);
      border-width: 1px;
      border-style: solid;
      border-image: linear-gradient(to bottom, #0A368B, #2C2C2C);;
      border-image-slice: 1;

      & .ant-modal-header {
        padding: 0;
        background: rgba(39,99,208,0.10);
        border-bottom: 1px solid rgba(232, 232, 232, 0);
      }

      & .ant-modal-body {
      }

      & .ant-modal-footer{
        border-top: 1px solid #2C2C2C;

        & .ant-btn{
          background-color: transparent;
          border: 1px solid #DCDFE6;;
          border-radius: 4px;
          height: 32px;
          font-size: 14px;
          color: #ffffff;
          margin: 0 8px 0 16px;
          box-shadow: none;
        }

        & .ant-btn:hover, & .ant-btn:active{
          border: 1px solid #409EFF;
        }

        & .ant-btn-primary{
          background-color: #409EFF;
          border: 1px solid #409EFF;
          border-radius: 4px;
          height: 32px;
          font-size: 14px;
          color: #ffffff;
          margin: 0 8px 0 16px;
          box-shadow: none;
        }

        & .ant-btn-primary:hover,& .ant-btn-primary:active{
          background-color: #007dff;
          border: 1px solid #007dff;
        }
      }

      & .ant-modal-close {
        color: rgba(255, 255, 255, 0.65) !important;

        &:hover {
          color: #ffffff !important;
        }
      }
    }
  }

  .header-wrapper{
    position: relative;
    border-bottom:1px solid #132f65;

    .head-img {
      position: absolute;
      content: '';
      height: 100%;
      bottom: 0px;
      left: 0px;
      right: 0px;
      top: 0px;
      background-image: url("/oneClickHelp/localDeviceInfo/leftHeadBg.png");
      background-repeat: no-repeat;
      background-position: right center;
    }

    .left-border,.right-border{
      height: 54px;
      position: absolute;
      top:0;
      bottom:0;
      right:0;
      left: 0;
    }
    .left-border::before{
      position: absolute;
      content: '';
      top: 0;
      left: 0;
      width: 3px;
      height: 3px;
      background-color: #2F5BFF;
    }
    .left-border::after{
      position: absolute;
      content: '';
      bottom: 0;
      left: 0;
      width: 3px;
      height: 3px;
      background-color: #2F5BFF;
    }

    .right-border::before{
      position: absolute;
      content: '';
      top: 0;
      right: 0;
      width: 3px;
      height: 3px;
      background-color: #2F5BFF;
    }
    .right-border::after{
      position: absolute;
      content: '';
      bottom: 0;
      right: 0;
      width: 3px;
      height: 3px;
      background-color: #2F5BFF;
    }
  }

  .yq-modal-title-row {
    padding: 16px 24px 16px 16px;
    color:#ffffff;

    .left {
      width: calc(100% - 56px - 56px);

      img{
        margin-right: 10px;
      }
    }

    .right {
      width: 56px;
      position: inherit;

      .ant-modal-close {
        right: 56px;
        //color: rgba(0, 0, 0, 0.45);
        color: rgba(255, 255, 255, 0.65) !important;

        &:hover {
          //color: rgba(0, 0, 0, 0.75);
          color: #ffffff !important;
        }
      }
    }
  }
  ::-webkit-scrollbar {
    background-color: #000 !important;
  }
  ::-webkit-scrollbar-thumb {
    background-color: rgba(21, 85, 175, 0.4) !important;
  }
}

@media (max-width: 767px) {
  .yq-modal-box.fullscreen {
    margin: 0;
    max-width: 100vw;
  }
}
</style>