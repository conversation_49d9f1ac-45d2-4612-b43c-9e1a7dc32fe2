<template>
  <a-row :gutter='10' style='height: 100%' class='vScroll zxw'>
    <a-col style='width: 100%; height: 100%; display: flex; flex-direction: column'>
      <a-row class='lastBtn2' style='margin-left: 11px'>
        <!-- 操作按钮区域 -->
        <div class='table-operator'>
          <a-input
            style='width: 210px'
            placeholder='请输入版本'
            :allowClear='true'
            autocomplete='off'
            v-model='queryParam.patchVersion'
          ></a-input>
          <a-button style='margin-left: 10px' @click='searchQuery' class='btn-search-style'>查询</a-button>
          <a-button @click='searchReset' class='btn-reset-style'>重置</a-button>
          <a-button  @click='handleAdd'>新增</a-button>
<!--          <a-button  @click="handleExportXls('补丁管理表')">导出</a-button>-->
          <a-dropdown v-if='selectedRowKeys.length > 0'>
            <a-menu slot='overlay' style='text-align: center'>
              <a-menu-item key='1' @click='batchDel'>删除</a-menu-item>
            </a-menu>
            <a-button> 批量操作 <a-icon type='down' /></a-button>
          </a-dropdown>
        </div>
      </a-row>
      <!-- table区域-begin -->
      <a-table
        ref='table'
        bordered
        rowKey='id'
        :columns='columns'
        :dataSource='dataSource'
        :pagination='ipagination'
        :loading='loading'
        :rowSelection='{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }'
        class='j-table-force-nowrap'
        @change='handleTableChange'
      >
        <template slot='htmlSlot' slot-scope='text'>
          <div v-html='text'></div>
        </template>
        <template slot='imgSlot' slot-scope='text'>
          <span v-if='!text' style='font-size: 14px'>无图片</span>
          <img v-else :src='getImgView(text)' height='25px' alt='' style='max-width: 80px; font-size: 14px' />
        </template>
        <template slot='fileSlot' slot-scope='text'>
          <span v-if='!text' style='font-size: 14px'>无文件</span>
          <a-button v-else :ghost='true' type='primary' icon='download' size='small' @click='downloadFile(text)'>
            下载
          </a-button>
        </template>

        <span
          slot='action'
          slot-scope='text, record'
          class='caozuo'
          style='display: inline-block; white-space: nowrap; text-align: center'
        >
            <a style='color: #409eff' @click='handleDetailPage(record)'>查看</a>
            <a-divider type='vertical' />
            <a @click='handleEdit(record)'>编辑</a>
            <a-divider type='vertical' />
            <a-dropdown>
              <a class='ant-dropdown-link'>更多 <a-icon type='down' /></a>
              <a-menu slot='overlay'>
                <!-- <a-menu-item> </a-menu-item> -->
                 <a-menu-item v-if='record.patchType ==="0"'>
                  <a style='color: #409eff' @click='downloadSofware(record.file,record.fileOriginalName,record)'>下载升级包</a>
                </a-menu-item>
                <a-menu-item v-if='record.patchType ==="1"'>
                  <a style='color: #409eff' @click='downloadSofware(record.file,record.fileOriginalName,record)'>下载升级文件</a>
                </a-menu-item>
                <a-menu-item v-if='record.patchType ==="1"'>
                  <a style='color: #409eff' @click='downloadSofware(record.inst,record.instOriginalName,record)'>下载脚本</a>
                </a-menu-item>
                <a-menu-item v-if='record.patchType ==="1"'>
                  <a style='color: #409eff' @click='downloadSofware(record.preInst,record.preInstOriginalName,record)'>下载执行前脚本</a>
                </a-menu-item>
                <a-menu-item v-if='record.patchType ==="1"'>
                  <a style='color: #409eff' @click='downloadSofware(record.postInst,record.postInstOriginalName,record)'>下载执行后脚本</a>
                </a-menu-item>
                <a-menu-item>
                  <a-popconfirm title='确定删除吗?' @confirm='() => handleDelete(record.id)'>
                    <a style='color: #409eff'>删除</a>
                  </a-popconfirm>
                </a-menu-item>
              </a-menu>
            </a-dropdown>
          </span>
        <template slot='tooltip' slot-scope='text'>
          <a-tooltip placement='topLeft' :title='text' trigger='hover'>
            <div class='tooltip'>
              {{ text }}
            </div>
          </a-tooltip>
        </template>
      </a-table>
      <soft-patch-info-modal
        ref='modalForm'
        @ok='modalFormOk'
        :patchTypes='patchTypes'
        :auditStatus='auditStatus'
        :restarts='restarts'
        :dict-options='dictOptions'
        :cpu-list='cpuList'
        :enables='enables'
        :resource-type-list='resourceTypeList'
        :softwareId='softwareId'
      ></soft-patch-info-modal>
      <soft-patch-info-details
        ref='softPatchInfoDetails'
        :patchTypes='patchTypes'
        :auditStatus='auditStatus'
        :restarts='restarts'
        :dict-options='dictOptions'
        :cpu-list='cpuList'
        :enables='enables'
        :resource-type-list='resourceTypeList'
        @downloadSofware='downloadSofware'
      ></soft-patch-info-details>
    </a-col>
  </a-row>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import SoftPatchInfoModal from './modules/SoftPatchInfoModal'
import JSuperQuery from '@/components/jeecg/JSuperQuery.vue'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
import SoftPatchInfoDetails from '@views/opmg/softwarePoc/patch/modules/SoftPatchInfoDetails.vue'
import { ajaxGetDictItems } from '@api/api'
import { deleteAction } from '@/api/manage'
export default {
  name: 'PatchList',
  mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
  components: {
    SoftPatchInfoDetails,
    SoftPatchInfoModal,
    JSuperQuery,
  },
  props: {
    softwareId: {
      type:String,
      default:'',
      required:true
    }
  },
  data() {
    return {
      description: '升级包管理页面',
      // 表头
      columns: [
        // {
        //   title: '名称',
        //   dataIndex: 'patchName'
        // },
        {
          title: '版本',
          dataIndex: 'patchVersion'
        },{
          title: '升级类别',
          dataIndex: 'patchType',
          customRender: (text) => {
            return this.patchTypes.find(el=>el.value === text)?.text
          }
        },
        {
          title: '操作系统',
          dataIndex: 'patchOs',
          customRender: (text) => {
            let tem = [];
            if(text){
              let arr = text.split(',');
              for (let i = 0; i < arr.length; i++) {
                tem.push(this.dictOptions.find(el=>el.value === arr[i])?.text)
              }
            }
            return tem.join()
          }
        },
        {
          title: '架构',
          dataIndex: 'frawork',
          customRender: (text) => {

            let tem = [];
            if(text){
              let arr = text.split(',');
              for (let i = 0; i < arr.length; i++) {
                tem.push(this.cpuList.find(el=>el.value === arr[i])?.text)
              }
            }
            return tem.join()
          }
        },
        // {
        //   title: '补丁文件名称',
        //   dataIndex: 'patchFileNameText',
        // },
        {
          title: '是否启用',
          dataIndex: 'isEnable',
          customRender: (text) => {
            return this.enables.find(el=>el.value === text)?.text
          }
        },
        {
          title: '设备类型',
          dataIndex: 'resourceType',
          customRender: (text) => {
            let arr = text.split(',');
            let tem = [];
            arr.forEach(el=>{
              tem.push(this.resourceTypeList.find(item=>item.value === el)?.text)
            })
            return tem.join(",")
          }
        },
        {
          title: '升级特性',
          dataIndex: 'upgradeFeature',
          customCell: () => {
            let cellStyle = 'text-align: left; min-width: 100px;max-width:300px'
            return {
              style: cellStyle
            }
          },
          scopedSlots: { customRender: 'tooltip' }
        },
        {
          title: '操作',
          dataIndex: 'action',
          fixed: 'right',
          align: 'center',
          width: 147,
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: '/software/upgradePackageManage/list',
        delete: '/software/upgradePackageManage/delete',
        deleteBatch: '/patch/devopePatchInfo/deleteBatch',
        exportXlsUrl: '/patch/devopePatchInfo/exportXls',
        importExcelUrl: 'patch/devopePatchInfo/importExcel'
      },
      dictOptions: [],
      cpuList:[],
      patchTypes: [],
      restarts: [],
      auditStatus: [],
      enables:[],
      resourceTypeList:[],
      disableMixinCreated:true,
    }
  },
  created() {
    this.initDictData()
    this.queryParam.softwareId = this.softwareId
    this.loadData()
  },
  mounted() {
  },
  computed: {
    importExcelUrl: function() {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    }
  },
  methods: {
    //补丁详情
    handleDetailPage(record) {
      this.$refs.softPatchInfoDetails.show(record)
    },
    initDictConfig() {
    },
    //下载
    fontClick(path) {
      window.open(window._CONFIG['downloadUrl'] + '/' + path)
    },
    /*
    * 执行下载文件
    * filepath 文件路径
    * fileName 文件名称 用后台保存的原始文件名
    * data 软件包信息
    * */
    downloadSofware(filepath,fileName,data) {
      if(filepath && fileName){
        let url=window._CONFIG['domianURL'] + '/software/upgradePackageManage/download' + '/' + filepath
        this.downloadFileByURL(url,fileName, {id:data.id},this.refreshInfo)
      }
      else{
        this.$message.warning("没有文件路径！")
      }
    },
    initDictData() {
      //根据字典Code, 初始化字典数组
      ajaxGetDictItems('cpuArch', null).then((res) => {
        if (res.success) {
          this.cpuList = res.result
        }
      })
      ajaxGetDictItems('os_type', null).then((res) => {
        if (res.success) {
          this.dictOptions = res.result
        }
      })
      ajaxGetDictItems('upgrade_package_type', null).then((res) => {
        if (res.success) {
          this.patchTypes = res.result
        }
      })
      ajaxGetDictItems('isEnable', null).then((res) => {
        if (res.success) {
          this.enables = res.result
        }
      })
      ajaxGetDictItems('isRestart', null).then((res) => {
        if (res.success) {
          this.restarts = res.result
        }
      })
      ajaxGetDictItems('resources_type', null).then((res) => {
        if (res.success) {
          this.resourceTypeList = res.result
        }
      })

    },
    //重置
    searchReset() {
      this.queryParam = {}
      this.queryParam.softwareId = this.softwareId
      this.loadData(1)
    },
    modalFormOk() {
      this.loadData()
      this.refreshInfo();
    },
    refreshInfo() {
      this.$emit('refreshInfo')
    },
    handleDelete: function (id) {
      if (!this.url.delete) {
        this.$message.error('请设置url.delete属性!')
        return
      }
      var that = this
      deleteAction(that.url.delete, { id: id }).then((res) => {
        if (res.success) {
          //重新计算分页问题
          that.reCalculatePage(1)
          that.$message.success(res.message)
          that.loadData()
          this.refreshInfo()
        } else {
          that.$message.warning(res.message)
        }
      })
    },
    batchDel: function () {
      if (!this.url.deleteBatch) {
        this.$message.error('请设置url.deleteBatch属性!')
        return
      }
      if (this.selectedRowKeys.length <= 0) {
        this.$message.warning('请选择一条记录！')
        return
      } else {
        var ids = ''
        for (var a = 0; a < this.selectedRowKeys.length; a++) {
          ids += this.selectedRowKeys[a] + ','
        }
        var that = this
        this.$confirm({
          title: '确认删除',
          okText: '是',
          cancelText: '否',
          content: '是否删除选中数据?',
          onOk: function () {
            that.loading = true
            deleteAction(that.url.deleteBatch, { ids: ids })
              .then((res) => {
                if (res.success) {
                  //重新计算分页问题
                  that.reCalculatePage(that.selectedRowKeys.length)
                  that.$message.success(res.message)
                  that.loadData()
                  that.refreshInfo();
                  that.onClearSelected()
                } else {
                  that.$message.warning(res.message)
                }
              })
              .finally(() => {
                that.loading = false
              })
          }
        })
      }
    },
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';

.table-page-search-wrapper .ant-form-inline .ant-form-item {
  margin-bottom: 0 !important;
}

.ant-table-pagination.ant-pagination {
  margin: 16px 0 0 0 !important;
}

.btn-search-style{
  background: #ECF5FF;
  border: 1px solid #B3D8FF;
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: #409EFF;
  font-weight: 400;
}
</style>
