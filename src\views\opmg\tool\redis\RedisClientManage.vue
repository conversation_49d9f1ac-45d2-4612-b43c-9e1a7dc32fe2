<template>
  <div style='height:100%;'>
    <keep-alive exclude='RedisView'>
      <component :is='pageName' :obj='obj' />
    </keep-alive>
  </div>
</template>
<script>
import {
  getAction
} from '@api/manage'
import RedisClientList from './modules/RedisClientList'
import RedisView from './modules/RedisView'

export default {
  name: 'RedisClientManage',
  data() {
    return {
      isActive: 0,
      obj: {}
    }
  },

  components: {
    RedisClientList,
    RedisView
  },
  created() {
    this.pButton1(0)
  },
  //使用计算属性
  computed: {
    pageName() {
      switch (this.isActive) {
        case 0:
          return 'RedisClientList'
        default:
          return 'RedisView'
      }
    }
  },
  methods: {
    pButton1(index, item) {
      this.isActive = index
      this.obj = item
    },
    pButton2(index, item) {
      this.isActive = index
      this.data = item
    }
  }
}
</script>