{"list": [{"label": "运维保障制度建设情况", "type": "yq_divider", "options": {"showLabel": false}, "key": "yq_divider_1754716025771"}, {"type": "batch", "label": "运维保障制度清单", "list": [{"type": "input", "label": "制度编号", "icon": "icon-write", "options": {"type": "text", "width": "100%", "tableWidth": "100px", "defaultValue": "", "placeholder": "单位内部统一编制的唯一标识（如“YWZD-2024-001”）", "clearable": false, "maxLength": null, "addonBefore": "", "addonAfter": "", "hidden": false, "disabled": false, "linkageData": "[]", "changeFunc": "function(value, key,vm,http){}", "isEvaluationField": false, "showLabel": true}, "model": "input_1754716196982", "key": "input_1754716196982", "help": "", "rules": [{"required": false, "message": "必填项"}]}, {"type": "input", "label": "制度名称", "icon": "icon-write", "options": {"type": "text", "width": "100%", "tableWidth": "100px", "defaultValue": "", "placeholder": "如《XX系统运维管理总规章》等", "clearable": false, "maxLength": null, "addonBefore": "", "addonAfter": "", "hidden": false, "disabled": false, "linkageData": "[]", "changeFunc": "function(value, key,vm,http){}", "isEvaluationField": false, "showLabel": true}, "model": "input_1754716200453", "key": "input_1754716200453", "help": "", "rules": [{"required": false, "message": "必填项"}]}, {"type": "select", "label": "制度类型", "icon": "icon-xiala", "options": {"width": "100%", "tableWidth": "150px", "multiple": false, "disabled": false, "clearable": false, "hidden": false, "placeholder": "请选择", "linkageData": "[]", "changeFunc": "function(value, key,vm,http){}", "dynamicKey": "", "dynamic": "static", "ajaxData": {"url": "", "type": "GET", "params": "{}", "header": "{}", "callFunc": "function(res){\n          return res.data;\n        }"}, "options": [{"value": "管理规章", "label": "管理规章"}, {"value": "工作规范", "label": "工作规范"}, {"value": "操作手册", "label": "操作手册"}, {"value": "作业文件", "label": "作业文件"}, {"value": "其他", "label": "其他"}], "staticOptions": [{"value": "管理规章", "label": "管理规章"}, {"value": "工作规范", "label": "工作规范"}, {"value": "操作手册", "label": "操作手册"}, {"value": "作业文件", "label": "作业文件"}, {"value": "其他", "label": "其他"}], "showSearch": false, "isEvaluationField": false, "showLabel": true}, "model": "select_1754716386714", "key": "select_1754716386714", "help": "", "rules": [{"required": false, "message": "必填项"}]}, {"type": "checkbox", "label": "制度覆盖场景", "icon": "icon-duoxuan1", "options": {"width": "100%", "tableWidth": "150px", "disabled": false, "hidden": false, "defaultValue": [], "linkageData": "[]", "changeFunc": "function(value, key,vm,http){}", "dynamicKey": "", "dynamic": "static", "options": [{"value": "检查", "label": "检查"}, {"value": "测试", "label": "测试"}, {"value": "维护", "label": "维护"}, {"value": "登记", "label": "登记"}, {"value": "应急演练", "label": "应急演练"}, {"value": "其他", "label": "其他"}], "staticOptions": [{"value": "检查", "label": "检查"}, {"value": "测试", "label": "测试"}, {"value": "维护", "label": "维护"}, {"value": "登记", "label": "登记"}, {"value": "应急演练", "label": "应急演练"}, {"value": "其他", "label": "其他"}], "ajaxData": {"url": "", "type": "GET", "params": "{}", "header": "{}", "callFunc": "function(res){\n  \tif(res.success){\n    \treturn res.result\n    }else{\n    \treturn []\n    }\n  }"}, "isEvaluationField": false, "showLabel": true}, "model": "checkbox_1754716392037", "key": "checkbox_1754716392037", "help": "", "rules": [{"required": false, "message": "必填项"}]}, {"type": "date", "label": "发布时间", "icon": "icon-calendar", "options": {"width": "100%", "tableWidth": "150px", "defaultValue": "", "rangeDefaultValue": [], "range": false, "showTime": false, "disabled": false, "hidden": false, "clearable": false, "placeholder": "请选择", "rangePlaceholder": ["开始时间", "结束时间"], "format": "YYYY-MM-DD", "linkageData": "[]", "changeFunc": "function(value, key,vm,http){}", "isEvaluationField": false, "showLabel": true}, "model": "date_1754717010396", "key": "date_1754717010396", "help": "", "rules": [{"required": false, "message": "必填项"}]}, {"type": "radio", "label": "是否现行有效", "icon": "icon-danxuan-cuxia<PERSON><PERSON>", "options": {"width": "100%", "tableWidth": "150px", "disabled": false, "hidden": false, "defaultValue": "", "linkageData": "[]", "changeFunc": "function(value, key,vm,http){}", "dynamicKey": "", "dynamic": "static", "options": [{"value": "是", "label": "是"}, {"value": "否", "label": "否（已废止）"}, {"value": "修订中", "label": "修订中"}], "staticOptions": [{"value": "是", "label": "是"}, {"value": "否", "label": "否（已废止）"}, {"value": "修订中", "label": "修订中"}], "ajaxData": {"url": "", "type": "GET", "params": "{}", "header": "{}", "callFunc": "function(res){\n  \tif(res.success){\n    \treturn res.result\n    }else{\n    \treturn []\n    }\n  }"}, "isEvaluationField": false, "showLabel": true}, "model": "radio_1754717020073", "key": "radio_1754717020073", "help": "", "rules": [{"required": false, "message": "必填项"}]}, {"type": "radio", "label": "是否组织培训或宣贯", "icon": "icon-danxuan-cuxia<PERSON><PERSON>", "options": {"width": "100%", "tableWidth": "150px", "disabled": false, "hidden": false, "defaultValue": "", "linkageData": "[]", "changeFunc": "function(value, key,vm,http){}", "dynamicKey": "", "dynamic": "static", "options": [{"value": "是", "label": "是（有记录）"}, {"value": "否", "label": "否"}], "staticOptions": [{"value": "是", "label": "是（有记录）"}, {"value": "否", "label": "否"}], "ajaxData": {"url": "", "type": "GET", "params": "{}", "header": "{}", "callFunc": "function(res){\n  \tif(res.success){\n    \treturn res.result\n    }else{\n    \treturn []\n    }\n  }"}, "isEvaluationField": false, "showLabel": true}, "model": "radio_1754717057400", "key": "radio_1754717057400", "help": "", "rules": [{"required": false, "message": "必填项"}]}, {"type": "uploadFile", "label": "上传记录", "icon": "icon-upload", "options": {"defaultValue": "[]", "multiple": false, "disabled": false, "hidden": false, "drag": false, "downloadWay": "a", "dynamicFun": "", "width": "100%", "tableWidth": "200px", "limit": 3, "data": "{\"biz\":\"file\"}", "fileName": "file", "headers": {}, "action": "/sys/common/upload", "downloadFileUrl": "/sys/common/static/", "placeholder": "上传", "linkageData": "[]", "changeFunc": "function(value, key,vm,http){}", "showLabel": true}, "model": "uploadFile_1754717087251", "key": "uploadFile_1754717087251", "help": "", "rules": [{"required": false, "message": "必填项"}]}], "options": {"scrollY": 0, "disabled": false, "hidden": false, "showLabel": true, "hideSequence": false, "width": "100%", "rowKey": "id", "hideAddBtn": false, "hideOprCol": false, "linkageData": "[]", "changeFunc": "function(value, key,vm,http){}"}, "model": "batch_1754716462468", "key": "batch_1754716462468", "help": ""}, {"label": "运维保障技术手段应用情况", "type": "yq_divider", "options": {"showLabel": false}, "key": "yq_divider_1754716658309"}, {"type": "batch", "label": "运维保障系统清单", "list": [{"type": "input", "label": "系统名称", "icon": "icon-write", "options": {"type": "text", "width": "100%", "tableWidth": "100px", "defaultValue": "", "placeholder": "请输入", "clearable": false, "maxLength": null, "addonBefore": "", "addonAfter": "", "hidden": false, "disabled": false, "linkageData": "[]", "changeFunc": "function(value, key,vm,http){}", "isEvaluationField": false, "showLabel": true}, "model": "input_1754717114527", "key": "input_1754717114527", "help": "", "rules": [{"required": false, "message": "必填项"}]}, {"type": "select", "label": "系统类型", "icon": "icon-xiala", "options": {"width": "100%", "tableWidth": "150px", "multiple": false, "disabled": false, "clearable": false, "hidden": false, "placeholder": "请选择", "linkageData": "[]", "changeFunc": "function(value, key,vm,http){}", "dynamicKey": "", "dynamic": "static", "ajaxData": {"url": "", "type": "GET", "params": "{}", "header": "{}", "callFunc": "function(res){\n          return res.data;\n        }"}, "options": [{"value": "网络管理", "label": "网络管理"}, {"value": "计算资源监控", "label": "计算资源监控"}, {"value": "存储管理", "label": "存储管理"}, {"value": "应用性能监控", "label": "应用性能监控"}, {"value": "安全管理系统", "label": "安全管理系统"}, {"value": "动环监控系", "label": "动环监控系"}, {"value": "资产管理系统", "label": "资产管理系统"}, {"value": "其他", "label": "其他"}], "staticOptions": [{"value": "网络管理", "label": "网络管理"}, {"value": "计算资源监控", "label": "计算资源监控"}, {"value": "存储管理", "label": "存储管理"}, {"value": "应用性能监控", "label": "应用性能监控"}, {"value": "安全管理系统", "label": "安全管理系统"}, {"value": "动环监控系", "label": "动环监控系"}, {"value": "资产管理系统", "label": "资产管理系统"}, {"value": "其他", "label": "其他"}], "showSearch": false, "isEvaluationField": false, "showLabel": true}, "model": "select_1754717123369", "key": "select_1754717123369", "help": "", "rules": [{"required": false, "message": "必填项"}]}, {"type": "select", "label": "部署方式", "icon": "icon-xiala", "options": {"width": "100%", "tableWidth": "150px", "multiple": false, "disabled": false, "clearable": false, "hidden": false, "placeholder": "请选择", "linkageData": "[]", "changeFunc": "function(value, key,vm,http){}", "dynamicKey": "", "dynamic": "static", "ajaxData": {"url": "", "type": "GET", "params": "{}", "header": "{}", "callFunc": "function(res){\n          return res.data;\n        }"}, "options": [{"value": "自建", "label": "自建"}, {"value": "云平台", "label": "云平台"}, {"value": "第三方服务", "label": "第三方服务"}], "staticOptions": [{"value": "自建", "label": "自建"}, {"value": "云平台", "label": "云平台"}, {"value": "第三方服务", "label": "第三方服务"}], "showSearch": false, "isEvaluationField": false, "showLabel": true}, "model": "select_1754717125709", "key": "select_1754717125709", "help": "", "rules": [{"required": false, "message": "必填项"}]}, {"type": "radio", "label": "是否常态化运行", "icon": "icon-danxuan-cuxia<PERSON><PERSON>", "options": {"width": "100%", "tableWidth": "150px", "disabled": false, "hidden": false, "defaultValue": "", "linkageData": "[]", "changeFunc": "function(value, key,vm,http){}", "dynamicKey": "", "dynamic": "static", "options": [{"value": "是", "label": "是"}, {"value": "否", "label": "否"}, {"value": "间歇运行", "label": "间歇运行"}], "staticOptions": [{"value": "是", "label": "是"}, {"value": "否", "label": "否"}, {"value": "间歇运行", "label": "间歇运行"}], "ajaxData": {"url": "", "type": "GET", "params": "{}", "header": "{}", "callFunc": "function(res){\n  \tif(res.success){\n    \treturn res.result\n    }else{\n    \treturn []\n    }\n  }"}, "isEvaluationField": false, "showLabel": true}, "model": "radio_1754717128005", "key": "radio_1754717128005", "help": "", "rules": [{"required": false, "message": "必填项"}]}, {"type": "radio", "label": "是否与制度联动", "icon": "icon-danxuan-cuxia<PERSON><PERSON>", "options": {"width": "100%", "tableWidth": "150px", "disabled": false, "hidden": false, "defaultValue": "", "linkageData": "[]", "changeFunc": "function(value, key,vm,http){}", "dynamicKey": "", "dynamic": "static", "options": [{"value": "是", "label": "是（如告警触发工单）"}, {"value": "否", "label": "否"}, {"value": "部分联动", "label": "部分联动"}], "staticOptions": [{"value": "是", "label": "是（如告警触发工单）"}, {"value": "否", "label": "否"}, {"value": "部分联动", "label": "部分联动"}], "ajaxData": {"url": "", "type": "GET", "params": "{}", "header": "{}", "callFunc": "function(res){\n  \tif(res.success){\n    \treturn res.result\n    }else{\n    \treturn []\n    }\n  }"}, "isEvaluationField": false, "showLabel": true}, "model": "radio_1754717157229", "key": "radio_1754717157229", "help": "", "rules": [{"required": false, "message": "必填项"}]}, {"type": "input", "label": "责任人", "icon": "icon-write", "options": {"type": "text", "width": "100%", "tableWidth": "100px", "defaultValue": "", "placeholder": "请输入", "clearable": false, "maxLength": null, "addonBefore": "", "addonAfter": "", "hidden": false, "disabled": false, "linkageData": "[]", "changeFunc": "function(value, key,vm,http){}", "isEvaluationField": false, "showLabel": true}, "model": "input_1754717166419", "key": "input_1754717166419", "help": "", "rules": [{"required": false, "message": "必填项"}]}, {"type": "uploadFile", "label": "上传文件", "icon": "icon-upload", "options": {"defaultValue": "[]", "multiple": false, "disabled": false, "hidden": false, "drag": false, "downloadWay": "a", "dynamicFun": "", "width": "100%", "tableWidth": "200px", "limit": 3, "data": "{\"biz\":\"file\"}", "fileName": "file", "headers": {}, "action": "/sys/common/upload", "downloadFileUrl": "/sys/common/static/", "placeholder": "上传", "linkageData": "[]", "changeFunc": "function(value, key,vm,http){}", "showLabel": true}, "model": "uploadFile_1754717317090", "key": "uploadFile_1754717317090", "help": "", "rules": [{"required": false, "message": "必填项"}]}], "options": {"scrollY": 0, "disabled": false, "hidden": false, "showLabel": true, "hideSequence": false, "width": "100%", "rowKey": "id", "hideAddBtn": false, "hideOprCol": false, "linkageData": "[]", "changeFunc": "function(value, key,vm,http){}"}, "model": "batch_1754716654487", "key": "batch_1754716654487", "help": ""}, {"label": "制度落实与系统运行情况", "type": "yq_divider", "options": {"showLabel": false}, "key": "yq_divider_1754717294265"}, {"type": "table", "label": "表格布局", "trs": [{"tds": [{"colspan": 1, "rowspan": 1, "list": [{"type": "radio", "label": "近3个月是否开展日常巡检", "options": {"width": "100%", "tableWidth": "150px", "disabled": false, "hidden": false, "defaultValue": "", "linkageData": "[\n  {\n    \"fields\": [\"uploadFile_1754717668761\"],\n    \"handler\": \"async function(v,http) { return v?v.includes('是'):false; }\"\n  }\n]", "changeFunc": "function(value, key,vm,http){}", "dynamicKey": "", "dynamic": "static", "options": [{"value": "是", "label": "是"}, {"value": "否", "label": "否"}], "staticOptions": [{"value": "是", "label": "是"}, {"value": "否", "label": "否"}], "ajaxData": {"url": "", "type": "GET", "params": "{}", "header": "{}", "callFunc": "function(res){\n  \tif(res.success){\n    \treturn res.result\n    }else{\n    \treturn []\n    }\n  }"}, "isEvaluationField": true, "showLabel": true}, "model": "radio_1754717662222", "key": "radio_1754717662222", "help": "", "rules": [{"required": true, "message": "必填项"}]}]}, {"colspan": 1, "rowspan": 1, "list": [{"type": "uploadFile", "label": "巡检记录", "options": {"defaultValue": "[]", "multiple": false, "disabled": false, "hidden": false, "drag": false, "downloadWay": "a", "dynamicFun": "", "width": "100%", "tableWidth": "200px", "limit": 3, "data": "{\"biz\":\"file\"}", "fileName": "file", "headers": {}, "action": "/sys/common/upload", "downloadFileUrl": "/sys/common/static/", "placeholder": "上传", "linkageData": "[]", "changeFunc": "function(value, key,vm,http){}", "showLabel": true, "isEvaluationField": false}, "model": "uploadFile_1754717668761", "key": "uploadFile_1754717668761", "help": "", "rules": [{"required": false, "message": "必填项"}]}]}]}, {"tds": [{"colspan": 1, "rowspan": 1, "list": [{"type": "radio", "label": "近3个月是否开展系统测试", "icon": "icon-danxuan-cuxia<PERSON><PERSON>", "options": {"width": "100%", "tableWidth": "150px", "disabled": false, "hidden": false, "defaultValue": "", "linkageData": "[\n  {\n    \"fields\": [\"uploadFile_1754717683843\"],\n    \"handler\": \"async function(v, http) { return v === '是'; }\"\n  }\n]", "changeFunc": "function(value, key,vm,http){}", "dynamicKey": "", "dynamic": "static", "options": [{"value": "是", "label": "是"}, {"value": "否", "label": "否"}], "staticOptions": [{"value": "是", "label": "是"}, {"value": "否", "label": "否"}], "ajaxData": {"url": "", "type": "GET", "params": "{}", "header": "{}", "callFunc": "function(res){\n  \tif(res.success){\n    \treturn res.result\n    }else{\n    \treturn []\n    }\n  }"}, "isEvaluationField": true, "showLabel": true}, "model": "radio_1754717680995", "key": "radio_1754717680995", "help": "", "rules": [{"required": true, "message": "必填项"}]}]}, {"colspan": 1, "rowspan": 1, "list": [{"type": "uploadFile", "label": "测试记录", "options": {"defaultValue": "[]", "multiple": false, "disabled": false, "hidden": false, "drag": false, "downloadWay": "a", "dynamicFun": "", "width": "100%", "tableWidth": "200px", "limit": 3, "data": "{\"biz\":\"file\"}", "fileName": "file", "headers": {}, "action": "/sys/common/upload", "downloadFileUrl": "/sys/common/static/", "placeholder": "上传", "linkageData": "[]", "changeFunc": "function(value, key,vm,http){}", "showLabel": true}, "model": "uploadFile_1754717683843", "key": "uploadFile_1754717683843", "help": "", "rules": [{"required": false, "message": "必填项"}]}]}]}, {"tds": [{"colspan": 1, "rowspan": 1, "list": [{"type": "radio", "label": "近6个月是否开展应急演练", "icon": "icon-danxuan-cuxia<PERSON><PERSON>", "options": {"width": "100%", "tableWidth": "150px", "disabled": false, "hidden": false, "defaultValue": "", "linkageData": "[\n  {\n    \"fields\": [\"uploadFile_1754717768202\"],\n    \"handler\": \"async function(v, http) { return v === '是'; }\"\n  }\n]", "changeFunc": "function(value, key,vm,http){}", "dynamicKey": "", "dynamic": "static", "options": [{"value": "是", "label": "是"}, {"value": "否", "label": "否"}], "staticOptions": [{"value": "是", "label": "是"}, {"value": "否", "label": "否"}], "ajaxData": {"url": "", "type": "GET", "params": "{}", "header": "{}", "callFunc": "function(res){\n  \tif(res.success){\n    \treturn res.result\n    }else{\n    \treturn []\n    }\n  }"}, "isEvaluationField": true, "showLabel": true}, "model": "radio_1754717745053", "key": "radio_1754717745053", "help": "", "rules": [{"required": true, "message": "必填项"}]}]}, {"colspan": 1, "rowspan": 1, "list": [{"type": "uploadFile", "label": "应急演练方案和记录", "options": {"defaultValue": "[]", "multiple": false, "disabled": false, "hidden": false, "drag": false, "downloadWay": "a", "dynamicFun": "", "width": "100%", "tableWidth": "200px", "limit": 3, "data": "{\"biz\":\"file\"}", "fileName": "file", "headers": {}, "action": "/sys/common/upload", "downloadFileUrl": "/sys/common/static/", "placeholder": "上传", "linkageData": "[]", "changeFunc": "function(value, key,vm,http){}", "showLabel": true}, "model": "uploadFile_1754717768202", "key": "uploadFile_1754717768202", "help": "", "rules": [{"required": false, "message": "必填项"}]}]}]}, {"tds": [{"colspan": 1, "rowspan": 1, "list": [{"type": "radio", "label": "是否建立运维事件登记台账", "icon": "icon-danxuan-cuxia<PERSON><PERSON>", "options": {"width": "100%", "tableWidth": "150px", "disabled": false, "hidden": false, "defaultValue": "", "linkageData": "[\n  {\n    \"fields\": [\"uploadFile_1754717808154\"],\n    \"handler\": \"async function(v, http) { return v === '是'; }\"\n  }\n]", "changeFunc": "function(value, key,vm,http){}", "dynamicKey": "", "dynamic": "static", "options": [{"value": "是", "label": "是"}, {"value": "否", "label": "否"}], "staticOptions": [{"value": "是", "label": "是"}, {"value": "否", "label": "否"}], "ajaxData": {"url": "", "type": "GET", "params": "{}", "header": "{}", "callFunc": "function(res){\n  \tif(res.success){\n    \treturn res.result\n    }else{\n    \treturn []\n    }\n  }"}, "isEvaluationField": true, "showLabel": true}, "model": "radio_1754717804506", "key": "radio_1754717804506", "help": "", "rules": [{"required": true, "message": "必填项"}]}]}, {"colspan": 1, "rowspan": 1, "list": [{"type": "uploadFile", "label": "运维事件登记台账", "options": {"defaultValue": "[]", "multiple": false, "disabled": false, "hidden": false, "drag": false, "downloadWay": "a", "dynamicFun": "", "width": "100%", "tableWidth": "200px", "limit": 3, "data": "{\"biz\":\"file\"}", "fileName": "file", "headers": {}, "action": "/sys/common/upload", "downloadFileUrl": "/sys/common/static/", "placeholder": "上传", "linkageData": "[]", "changeFunc": "function(value, key,vm,http){}", "showLabel": true}, "model": "uploadFile_1754717808154", "key": "uploadFile_1754717808154", "help": "", "rules": [{"required": false, "message": "必填项"}]}]}]}, {"tds": [{"colspan": 1, "rowspan": 1, "list": [{"type": "radio", "label": "设备/资产是否按规范标记标签", "icon": "icon-danxuan-cuxia<PERSON><PERSON>", "options": {"width": "100%", "tableWidth": "150px", "disabled": false, "hidden": false, "defaultValue": "", "linkageData": "[\n  {\n    \"fields\": [\"uploadFile_1754717830618\"],\n    \"handler\": \"async function(v, http) { return v === '是'; }\"\n  }\n]", "changeFunc": "function(value, key,vm,http){}", "dynamicKey": "", "dynamic": "static", "options": [{"value": "是", "label": "是"}, {"value": "部分", "label": "部分"}, {"value": "否", "label": "否"}], "staticOptions": [{"value": "是", "label": "是"}, {"value": "部分", "label": "部分"}, {"value": "否", "label": "否"}], "ajaxData": {"url": "", "type": "GET", "params": "{}", "header": "{}", "callFunc": "function(res){\n  \tif(res.success){\n    \treturn res.result\n    }else{\n    \treturn []\n    }\n  }"}, "isEvaluationField": true, "showLabel": true}, "model": "radio_1754717827635", "key": "radio_1754717827635", "help": "", "rules": [{"required": true, "message": "必填项"}]}]}, {"colspan": 1, "rowspan": 1, "list": [{"type": "uploadFile", "label": "设备/资产规范标记标签记录", "options": {"defaultValue": "[]", "multiple": false, "disabled": false, "hidden": false, "drag": false, "downloadWay": "a", "dynamicFun": "", "width": "100%", "tableWidth": "200px", "limit": 3, "data": "{\"biz\":\"file\"}", "fileName": "file", "headers": {}, "action": "/sys/common/upload", "downloadFileUrl": "/sys/common/static/", "placeholder": "上传", "linkageData": "[]", "changeFunc": "function(value, key,vm,http){}", "showLabel": true}, "model": "uploadFile_1754717830618", "key": "uploadFile_1754717830618", "help": "", "rules": [{"required": false, "message": "必填项"}]}]}]}, {"tds": [{"colspan": 1, "rowspan": 1, "list": [{"type": "select", "label": "运维保障整体运行情况自评", "icon": "icon-xiala", "options": {"width": "100%", "tableWidth": "150px", "multiple": false, "disabled": false, "clearable": false, "hidden": false, "placeholder": "请选择", "linkageData": "[\n  {\n    \"fields\": [\"textarea_1754718185599\"],\n    \"handler\": \"async function(v, http) { return v === '存在问题'; }\"\n  }\n]", "changeFunc": "function(value, key,vm,http){}", "dynamicKey": "", "dynamic": "static", "ajaxData": {"url": "", "type": "GET", "params": "{}", "header": "{}", "callFunc": "function(res){\n          return res.data;\n        }"}, "options": [{"value": "良好", "label": "良好"}, {"value": "一般", "label": "一般"}, {"value": "存在问题", "label": "存在问题"}, {"value": "尚未运行", "label": "尚未运行"}], "staticOptions": [{"value": "良好", "label": "良好"}, {"value": "一般", "label": "一般"}, {"value": "存在问题", "label": "存在问题"}, {"value": "尚未运行", "label": "尚未运行"}], "showSearch": false, "isEvaluationField": true, "showLabel": true}, "model": "select_1754718156001", "key": "select_1754718156001", "help": "", "rules": [{"required": true, "message": "必填项"}]}]}, {"colspan": 1, "rowspan": 1, "list": [{"type": "textarea", "label": "存在的主要问题说明", "icon": "icon-edit", "options": {"width": "100%", "tableWidth": "200px", "minRows": 4, "maxRows": 6, "maxLength": null, "defaultValue": "", "clearable": false, "hidden": false, "disabled": false, "placeholder": "请输入", "linkageData": "[]", "changeFunc": "function(value, key,vm,http){}", "isEvaluationField": false, "showLabel": true}, "model": "textarea_1754718185599", "key": "textarea_1754718185599", "help": "", "rules": [{"required": false, "message": "必填项"}]}]}]}], "options": {"width": "100%", "bordered": true, "bright": false, "small": true, "customStyle": ""}, "key": "table_1754717658984"}], "config": {"layout": "vertical", "labelCol": {"xs": 4, "sm": 4, "md": 4, "lg": 4, "xl": 4, "xxl": 4}, "labelWidth": 100, "labelLayout": "flex", "wrapperCol": {"xs": 18, "sm": 18, "md": 18, "lg": 18, "xl": 18, "xxl": 18}, "hideRequiredMark": false, "customStyle": ""}, "hiddenFields": [], "hiddenData": []}