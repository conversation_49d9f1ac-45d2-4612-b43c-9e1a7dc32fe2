<template>
  <j-modal
    :title="title"
    width="500px"
    :visible="visible"
    :centered="true"
    switchFullscreen
    @cancel="handleCancel">
    <template slot="footer">
      <a-button key="back" @click="handleCancel">关闭</a-button>
      <a-button v-if="record.openType === 'url'" type="primary" @click="toHandle">去处理</a-button>
    </template>
    <a-card :bordered='false' class="daily-article" :loading="loading">
      <a-card-meta :title="record.titile" :description="'发布人：' + record.sender_dictText + '  发布时间： ' + record.sendTime"
        class="cardZstyle">
      </a-card-meta>

      <p v-html="record.msgContent" class="article-content" style="word-break: break-all"></p>
    </a-card>
  </j-modal>
</template>

<script>
  export default {
    name: 'ShowAnnouncement',
    components: {},
    data() {
      return {
        title: '通知消息',
        record: {},
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          },
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          },
        },
        visible: false,
        loading: false,
      }
    },
    created() {},
    methods: {
      detail(record) {
        this.visible = true
        this.record = record
      },
      handleCancel() {
        this.visible = false
      },
      toHandle() {
        if (this.record.openType === 'url') {
          this.visible = false
          //链接跳转
          this.$router.push({
            path: this.record.openPage
          })
        }
      },
    },
  }
</script>

<style lang="less">
@import '~@assets/less/normalModal.less';
  .daily-article {
  overflow-x: auto;
  border-bottom: 0;

  .ant-card-body {
    padding: 0px !important;
  }

  .ant-card-head {
    padding: 0 1rem;
  }

  .ant-card-meta {
    margin-bottom: 0.6rem;
  }

  .article-content {
    p {
      word-wrap: break-word;
      word-break: break-all;
      text-overflow: initial;
      white-space: normal;
      //font-size: 0.9rem !important;
      //margin-bottom: 0.8rem;
    }

    li {
      list-style: none;
    }

    ul {
      padding: 0;
    }
  }
}
  .cardZstyle {
    .ant-card-meta-title {
      font-weight: 600;
    }
  }
</style>