import Vue from 'vue'
const topo = {
  state: {
   viewTopoData:{},
   viewTopoNodes:[],
   viewTopoEdges:[],
  editTopoData:{},
  editTopoNodes:[],
  editTopoEdges:[],
  productTopoImg:{},
  },
  mutations: {
    ADD_TOPO_NODE: (state, record) => {
      state.editTopoNodes.push(record)
    },
  },
  actions: {
    addTopoNode({ commit }, record){
      commit('ADD_TOPO_NODE', record)
    }
  }
}
export default topo