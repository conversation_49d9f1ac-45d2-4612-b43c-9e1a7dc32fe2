<template>
  <a-row :gutter='10' style='height: 100%' class='vScroll'>
    <a-col>
      <div style='height: 100%; display: flex; flex-direction: column'>
        <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
          <!-- 查询区域 -->
          <div class='table-page-search-wrapper'>
            <a-form layout='inline' v-bind='formItemLayout'>
              <a-row :gutter='24' ref='row'>
                <a-col :span='spanValue'>
                  <a-form-item label='入库日期：'>
                    <a-range-picker
                      v-model='queryParam.warehousingTime'
                      @change='onChangePicker'
                      format='YYYY-MM-DD'
                      :placeholder="['开始日期', '截止日期']"
                      style='width: 100%'
                    />
                  </a-form-item>
                </a-col>
                <a-col :span='colBtnsSpan()'>
                  <span
                    class='table-page-search-submitButtons'
                    :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                  >
                    <a-button type='primary' @click='searchQuery'>查询</a-button>
                    <a-button @click='searchReset' style='margin-left: 10px'>重置</a-button>
                  </span>
                </a-col>
              </a-row>
            </a-form>
          </div>
          <!-- 查询区域-END -->
        </a-card>
        <a-card :bordered='false' style='flex: auto'>
          <!-- 操作按钮区域 -->
          <div class='table-operator'>
            <a-button @click='handleAdd' v-has="'inDepot:add'">新增</a-button>
            <a-dropdown v-if='selectedRowKeys.length > 0' v-has="'inDepot:delete'">
              <a-menu slot="overlay" style='text-align: center'>
                <a-menu-item key='1' @click='batchDel'>删除</a-menu-item>
              </a-menu>
              <a-button style='margin-left: 8px'>
                批量操作
                <a-icon type='down' />
              </a-button>
            </a-dropdown>
          </div>

          <!-- table区域-begin -->
          <div>
            <a-table
              ref='table'
              bordered
              rowKey='id'
              :columns='columns'
              :dataSource='dataSource'
              :scroll='dataSource.length>0?{x:"max-content"}:{}'
              :pagination='ipagination'
              :loading='loading'
              :rowSelection='{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }'
              class='j-table-force-nowrap'
              @change='handleTableChange'
            >
              <span slot='action' slot-scope='text, record'>
                <a @click='openDetail(record)'>查看</a>
              </span>
              <template slot='tooltip' slot-scope='text'>
                <a-tooltip placement='topLeft' :title='text' trigger='hover'>
                  <div class='tooltip'>
                    {{ text }}
                  </div>
                </a-tooltip>
              </template>
            </a-table>
          </div>
          <itil-in-depot-modal ref='modalForm' @ok='modalFormOk'></itil-in-depot-modal>
        </a-card>
      </div>
    </a-col>
  </a-row>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import ItilInDepotModal from './modules/ItilInDepotModal'
import { filterMultiDictText } from '@/components/dict/JDictSelectUtil'
import { getAction, downFile } from '@/api/manage'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'

export default {
  name: 'ItilInDepotList',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components: {
    ItilInDepotModal
  },
  data() {
    return {
      description: '入库表管理页面',
      // 表头
      columns: [
        {
          title: '入库单号',
          dataIndex: 'inCode'
        },
        {
          title: '入库日期',
          dataIndex: 'inTime',
          defaultSortOrder: 'descend'
        },
        {
          title: '入库类型',
          dataIndex: 'inType_dictText'
        },
        {
          title: '供应商',
          dataIndex: 'supplierName'
        },
        {
          title: '总金额(￥)',
          dataIndex: 'totalMoney'
        },
        {
          title: '入库人',
          dataIndex: 'inBy'
        },
        {
          title: '操作',
          dataIndex: 'action',
          fixed: 'right',
          align: 'center',
          width: 140,
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: '/itilInDepot/itilInDepot/list',
        delete: '/itilInDepot/itilInDepot/delete',
        deleteBatch: '/itilInDepot/itilInDepot/deleteBatch',
        exportXlsUrl: '/itilInDepot/itilInDepot/exportXls',
        importExcelUrl: 'itilInDepot/itilInDepot/importExcel'
      }
    }
  },
  activated() {
    this.loadData()
  },
  computed: {
    importExcelUrl: function() {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    }
  },
  methods: {
    onChangePicker(data, dateString) {
      this.queryParam.time1 = dateString[0]
      this.queryParam.time2 = dateString[1]
    },
    openDetail(record) {
      this.detailDisplay = true
      getAction('/itilInDepot/itilInDepot/details', { inId: record.id }).then((res) => {
        if (res.success) {
          this.cardData = res.result
          this.handleDetailPage(this.cardData)
        }
      })
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';




</style>