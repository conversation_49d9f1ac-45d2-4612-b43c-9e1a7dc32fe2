<template>
  <!-- <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    switchFullscreen
    @ok="handleOk"
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @cancel="handleCancel"
    cancelText="关闭"> -->
  <!-- </j-modal> -->
  <a-drawer
    ref="drawer"
    :title="title"
    :maskClosable="true"
    :destroyOnClose="true"
    width='auto'
    placement="right"
    :closable="true"
    @close="close"
    :wrapClassName="isLowBrower?'ant-drawer-no-animation':''"
    :visible="visible"

    style="overflow: auto;padding-bottom: 53px">
    <function-definition-form ref="realForm" @ok="submitCallback" :disabled="disableSubmit"></function-definition-form>
    </a-drawer>
</template>

<script>

import FunctionDefinitionForm from './FunctionDefinitionForm'
import {isLowBrower} from '@/utils/util';
export default {
  name: 'FunctionDefinitionModal',
  components: {
    FunctionDefinitionForm
  },
  props:{
    productInfo:{},
  },
  data () {
    return {
      title:'',
      width:800,
      visible: false,
      disableSubmit: false,
      isLowBrower:isLowBrower(),
    }
  },
  created() {
    console.log("低版本浏览器 === ",isLowBrower())
  },
  methods: {
    add (index) {
      console.log("drawer == ",this.$refs.drawer)
      this.visible=true
      this.$nextTick(()=>{
        this.$refs.realForm.productInfo=this.productInfo
        this.$refs.realForm.add(index);
      })
    },
    edit (record,index) {
      this.visible=true
      this.$nextTick(()=>{
        this.$refs.realForm.productInfo=this.productInfo
        this.$refs.realForm.edit(record,index);
      })
    },
    close () {
      this.$emit('close');
      this.$emit('ok');
      this.visible = false;
    },
    handleOk () {
      this.$refs.realForm.submitForm();
    },
    submitCallback(){
      this.$emit('ok');
      this.visible = false;
    },
    handleCancel () {
      this.close()
    }
  }
}
</script>
<style  scoped lang='less'>
::v-deep .ant-drawer-content-wrapper{
  min-width: 400px !important;
  max-width: 650px !important
}



</style>