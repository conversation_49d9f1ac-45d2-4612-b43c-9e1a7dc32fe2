<template>
  <div style="height:100%">
    <keep-alive exclude='KnowledgePreviewInfo'>
      <component style="height:100%" :is="pageName" :data="data"/>
    </keep-alive>
  </div>
</template>
<script>
import KnowledgePreviewList from './KnowledgePreviewList.vue'
import KnowledgePreviewInfo from '@views/oneClickHelp/knowledgeBase/KnowledgeBaseInfo.vue'
export default {
  name: "KnowledgePreviewManagement",
  data() {
    return {
      isActive: 0,
      data: {},
    };
  },
  components: {
    KnowledgePreviewList,
    KnowledgePreviewInfo
  },
  created() {
    this.pButton1(0);
  },
  //使用计算属性
  computed: {
    pageName() {
      switch (this.isActive) {
        case 0:
          return "KnowledgePreviewList";
        default:
          return "KnowledgePreviewInfo";
      }
    }
  },
  methods: {
    pButton1(index) {
      this.isActive = index;
    },
    pButton2(index, item) {
      this.isActive = index;
      this.data = item
    }
  }
}
</script>