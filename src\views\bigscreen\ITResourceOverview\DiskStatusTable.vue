<template>
  <div class='disk-status-table' ref='itDiskStatus'>
    <div class="header-right-time">
      <div class="time-range-span">
        <a-select v-model='deviceType'
                  style='width: 120px'
                  size='small'
                  placeholder='全部'
                  :getPopupContainer="(node) => node.parentNode"
                  :dropdownClassName='"custom-select-dropdown"'
                  :allow-clear='true'
                  @change='getDiskTops'>
          <a-select-option v-for="item in types" :value="item.value" :key="'t_'+item.value" :label="item.label">
            {{ item.label }}
          </a-select-option>
        </a-select>
    </div>
      <span class='time-range-label'>设备类型:</span>
    </div>
    <div class='disk-status-table-content'>
      <slot>
        <ul  class='scroll-list' style='height: 36px'>
          <li class='scroll-list-item'>
            <div class='item-time' style='text-align: center'>
              更新时间
            </div>
            <div class='item-name'>名称</div>
            <div class='item-free' >磁盘剩余</div>
            <div class='item-rate' >磁盘使用率(%)</div>
          </li>
        </ul>
        <vue-seamless-scroll v-if='srollOption.autoPlay' :data='listData' class='scroll-warp' :class-option='srollOption'>
          <ul class='scroll-list'>
            <li class='scroll-list-item' :class='{"scroll-list-odd":isOdd?index%2===0:index%2!==0}' v-for='(item, index) in listData'
                :key='index'>
              <div class='item-time' :title='item.updateTime' :style='{"--num":`"${index+1}"`}' >
                {{item.diskFree.createTime}}
              </div>
              <div class='item-name' :title='item.name'>{{item.name}}</div>
              <div class='item-free' :title='item.diskFree.value+item.diskFree.unit'>{{item.diskFree.value+item.diskFree.unit}}</div>
              <div class='item-rate' :title='item.diskRate'>{{item.diskRate}}</div>
            </li>
          </ul>
        </vue-seamless-scroll>
        <div v-else class='scroll-warp'>
          <ul  class='scroll-list'>
            <li class='scroll-list-item' :class='{"scroll-list-odd":isOdd?index%2===0:index%2!==0}' v-for='(item, index) in listData'
                :key='index'>
              <div class='item-time' :title='item.diskFree.createTime' :style='{"--num":`"${index+1}"`}' >
                {{item.diskFree.createTime}}
              </div>
              <div class='item-name' :title='item.name'>{{item.name}}</div>
              <div class='item-free' :title='item.diskFree.value+item.diskFree.unit'>{{item.diskFree.value+item.diskFree.unit}}</div>
              <div class='item-rate' :title='item.diskRate'>{{item.diskRate}}</div>
            </li>
          </ul>
        </div>
      </slot>
    </div>
  </div>
</template>
<script>
import vueSeamlessScroll from 'vue-seamless-scroll'
import resizeObserverMixin from '@views/statsCenter/com/resizeObserverMixin'
import { getAction } from '@api/manage'
export default {
  name: 'DiskStatusTable',
  components: {vueSeamlessScroll },
  mixins: [resizeObserverMixin],
  data() {
    return {
      listData: [
        {
          updateTime: '2023-08-10 10:00:00',
          "diskRate": 26.84,
          "name": "server249",
          "diskFree": {
            "unit": "--",
            "value": "--"
          }
        },
      ],
      srollOption: {
        step: 0.35, // 步长
        speed: 50, // 滚动速度
        timer: 3000,// 滚动时间间隔
        autoPlay: false,
        limitMoveNum:100000,
        // singleHeight: 36 ,
      },
      maxNum: 0,
      types: [
        { label: 'a', value: 'a' },
        { label: 'b', value: 'b' },
        { label: 'c', value: 'c' },
      ],
      deviceType:undefined,
    }
  },
  created() {
    this.getDeviceType()

  },
  mounted() {
    this.getDiskTops()
  },
  computed: {
    isOdd() {
      return this.listData.length % 2 === 0
    }
  },
  methods: {
    //
    getDeviceType(){
      this.types = []
      getAction('/assetscategory/assetsCategory/rootList',{
        pageNum:1,
        pageSize:-1,
      }).then(res=>{
        if(res.success && res.result && res.result.records){
          this.types = res.result.records.map(item=>({
            label:item.categoryName,
            value:item.categoryCode
          }))
        }
      }).catch(err=>{
        console.log("获取设备类型失败",err)
      })
    },
    // 获取磁盘占用Top20
    getDiskTops(){
      this.listData = []
      getAction('/openAPI/getDevDiskOccupyTop',{
        occupyTopCategoryCodes:this.deviceType
      }).then(res=>{
        if(res.success && res.result){
          this.listData = res.result
        }
      }).catch(err=>{
        console.log("获取磁盘占用Top5失败",err)
      }).finally(()=>{
        this.setPlayState()
      })
    },
    // 屏幕变化回调
    resizeObserverCb(){
      this.setPlayState()
    },
    //设置滚动状态
    setPlayState(){
      if(this.$refs.itDiskStatus && this.listData.length){
        let bounded = this.$refs.itDiskStatus.getBoundingClientRect()
        this.maxNum = Math.floor((bounded.height - 36 - 29 ) / 36)
        if(this.maxNum>0 && this.maxNum<this.listData.length){
          this.srollOption.limitMoveNum = this.maxNum
          this.srollOption.autoPlay =true;
          return ;
        }
      }

      this.srollOption.autoPlay =false;
      this.srollOption.limitMoveNum = this.listData.length+1
    }
  }
}
</script>



<style scoped lang='less'>
@import "~@assets/less/onclickStyle.less";
/deep/ .ant-select-arrow {
  color: rgba(255, 255, 255, 0.7);
}
/deep/ .ant-select-selection{
  border: 1px solid rgba(255, 255, 255, 0.7);
}
.disk-status-table{
  height: 100%;
  .disk-status-table-content {
    height: calc(100% - 29px);
    overflow: hidden;
    padding: 0 12px;
  }
}
.scroll-warp {
  height: 100%;
  overflow: hidden;
  width: 100%;

}
.scroll-list {
  width: 100%;
  height: 100%;
  margin: 0px;
  padding: 0px;
}

.scroll-list-item {
  display: flex;
  align-items: center;
  color: rgba(237, 245, 255, 0.95);
  font-size: 14px;
  justify-content: space-between;
  height: 36px;

  .item-time {
    min-width: 32%;
    overflow: hidden;
    position: relative;
    padding-left: 32px; /* 给圆点留出空间 */
    line-height: 1;
    opacity: 0.95;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center;
    &::before {
      content:var(--num);
      position: absolute;
      left: 12px;
      color: rgba(237, 245, 255, 0.95);
      font-size: 14px;
    }
  }

  .item-name {
    width: 25%;
    text-align: center;
    opacity: 0.95;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }


  .item-free{
    width:15%;
    text-align: center;
    opacity: 0.95;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .item-rate {
    width: 20%;
    text-align: center;
    opacity: 0.95;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.scroll-list-odd {
  background: rgba(29, 97, 140, 0.25);
}
.header-right-time {
  display: flex;
  align-items: center;
  flex-direction: row-reverse;
  margin-right: 12px;
  .time-range-label {
    font-weight: 400;
    font-size: 14px;
    color: #FFFFFF;
    opacity: 0.7;
    width: 62px;
  }
  .time-range-span {
    margin-left: 8px;
  }
  .report-btn{
    height: 30px;
    //line-height: 30px;
    color: rgba(255, 255, 255, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.7);
    padding:0 8px;
    border-radius: 4px;
    margin-left: 12px;
    display: flex;
    align-items: center;
    cursor: pointer;
    &:hover{
      color: rgba(255, 255, 255, 1);
      border: 1px solid rgba(255, 255, 255, 1);
    }
  }
}
</style>