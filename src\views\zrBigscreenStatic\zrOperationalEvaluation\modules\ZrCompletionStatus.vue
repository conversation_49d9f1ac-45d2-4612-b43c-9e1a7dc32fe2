<template>
  <div class='zr-completion-status'>
    <zr-bigscreen-title title='评估完成情况'>
    </zr-bigscreen-title>
    <div class='char-content'>
      <div id='completionStatusRadar' ref='radarChart'></div>
    </div>
  </div>
</template>
<script>
import ZrBigscreenTitle from '@views/zrBigscreens/modules/ZrBigscreenTitle.vue'
import resizeObserverMixin from '@views/statsCenter/com/resizeObserverMixin'
import { indicators,indicatorTypes } from '@views/zrBigscreens/modules/zrUtil'
export default {
  name: 'ZrCompletionStatus.vue',
  components: { ZrBigscreenTitle },
  mixins: [resizeObserverMixin],
  data() {
    return {
      chart: null,
      radarData:indicatorTypes,
    }
  },
  mounted() {
    this.chart = this.$echarts.init(this.$refs.radarChart)
    this.initRadarChart()
  },
  methods: {
    initRadarChart() {
      let option = {
        color: ['#8FB43F'],
        tooltip: {
          trigger: 'item',
        },
        radar: [
          {
            indicator: this.radarData.map(item => ({ name: item.name, max: 100,color: 'rgba(255, 255, 255, 1)'})),
            center: ['50%', '50%'],
            radius:"80%",
            startAngle: 90,
            splitNumber: 4,
            shape: 'polygon',
            nameGap: 5,
            axisLabel: {
              color: 'rgba(255, 255, 255, 1)',
            },
            splitArea: {
              areaStyle: {
                color: ['#0080CD', '#015199', '#023769', 'rgba(56,154,255,0.2)'],
                shadowColor: 'rgba(0, 0, 0, 0.2)',
                shadowBlur: 0
              }
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: 'rgba(211, 253, 250, 0.8)'
              }
            },
            splitLine: {
              show: false,
              lineStyle: {
                color: 'rgba(211, 253, 250, 0.8)'
              }
            }
          },
        ],
        series: [
          {
            type: 'radar',
            symbolSize: 8,
            emphasis: {
              lineStyle: {
                width: 4
              }
            },
            data: [
              {
                value: this.radarData.map(item => item.score),
                name: '评估完成情况'
              },
            ]
          }
        ]
      }
      this.chart.setOption(option)
    },
    resize() {
      if (this.chart) {
        this.chart.resize()
      }
    },
    resizeObserverCb() {
     this.resize()
    }
  }
}
</script>

<style scoped lang='less'>
.zr-completion-status {
  width: 100%;
  height: 100%;
}
.char-content {
  width: 100%;
  padding: 20px;
  height: calc(100% - 51px);
  position: relative;
  background: linear-gradient(to right, rgba(29, 78, 140, 0.3), rgba(29, 78, 140, 0.0));
  #completionStatusRadar {
    width: 100%;
    height: 100%;
  }
}
</style>