<template>
  <div class="driveBox">
    <div class="topImg">
      <div class="topTitle">驱动库</div>
      <div class='head-left'></div>
      <div class='head-right'></div>
    </div>
    <a-row :gutter="10" style="height: calc(100% - 50px)" class="vScroll">
      <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
        <!-- 查询区域 -->
        <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class="card-style">
          <div class="table-page-search-wrapper-style">
            <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
              <a-row :gutter="24" ref="row">
                <a-col :span="spanValue">
                  <a-form-item label="驱动名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input placeholder="请输入驱动名称" :allowClear="true" autocomplete="off" v-model="queryParam.driveName">
                    </a-input>
                  </a-form-item>
                </a-col>
                <a-col :span="spanValue">
                  <a-form-item label="驱动版本" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-select placeholder="请选择驱动版本" v-model="queryParam.driveCpu" @change="cpuChange"
                      :getPopupContainer="(node) => node.parentNode" :allowClear="true">
                      <a-select-option v-for="item in cpuList" :key="item.value" :value="item.value">
                        {{ item.text || item.label }}
                      </a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="spanValue">
                  <a-form-item label="操作系统" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-select placeholder="请输选择操作系统" v-model="queryParam.driveOs" @change="actionChange"
                      :getPopupContainer="(node) => node.parentNode" :allowClear="true">
                      <a-select-option v-for="item in dictOptions" :key="item.value" :value="item.value">
                        {{ item.text }}
                      </a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="colBtnsSpan()">
                  <span class="table-page-search-submitButtons"
                    :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                    <a-button type="primary" @click="searchQuery" class="btn-search-style">查询</a-button>
                    <a-button @click="searchReset" style="margin-left: 10px" class="btn-reset-style">重置</a-button>
                  </span>
                </a-col>
              </a-row>
            </a-form>
          </div>
        </a-card>
        <!-- 查询区域-END -->
        <div class="card-style2">
          <div class="lightXian"></div>
          <div class="contentBox">
            <div style="display: flex;justify-content: space-between;height: 48%;width: 100%;">
              <div class="libraryBox">
                <driver-library :cpuType="'鲲鹏'" :version="'V1.0.6'" :system="'麒麟V10'" :name="'龙芯声卡驱动'"></driver-library>
              </div>
              <div class="libraryBox">
                <driver-library :cpuType="'龙芯'" :version="'V1.0.6'" :system="'麒麟V10'" :name="'龙芯声卡驱动'"></driver-library>
              </div>
              <div class="libraryBox">
                <driver-library :cpuType="'飞腾'" :version="'V1.0.6'" :system="'麒麟V10'" :name="'龙芯声卡驱动'"></driver-library>
              </div>
              <div class="libraryBox">
                <driver-library :cpuType="'海光'" :version="'V1.0.6'" :system="'麒麟V10'" :name="'龙芯声卡驱动'"></driver-library>
              </div>
            </div>
            <div style="display: flex;justify-content: space-between;height: 48%;width: 100%;">
              <div class="libraryBox">
                <driver-library :cpuType="'兆芯'" :version="'V1.0.6'" :system="'麒麟V10'" :name="'龙芯声卡驱动'"></driver-library>
              </div>
              <div class="libraryBox">
                <driver-library :cpuType="'申威'" :version="'V1.0.6'" :system="'麒麟V10'" :name="'龙芯声卡驱动'"></driver-library>
              </div>
              <div class="libraryBox">
                <driver-library :cpuType="'Intel'" :version="'V1.0.6'" :system="'麒麟V10'" :name="'龙芯声卡驱动'">
                </driver-library>
              </div>
              <div class="libraryBox">
                <driver-library :cpuType="'AMD'" :version="'V1.0.6'" :system="'麒麟V10'" :name="'龙芯声卡驱动'">
                </driver-library>
              </div>
            </div>
          </div>
        </div>
      </a-col>
    </a-row>
  </div>
</template>

<script>
  import '@/assets/less/TableExpand.less'
  import {
    mixinDevice
  } from '@/utils/mixin'
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import {
    filterMultiDictText
  } from '@/components/dict/JDictSelectUtil'
  import JSuperQuery from '@/components/jeecg/JSuperQuery.vue'
  import driverLibrary from './modules/driverLibrary.vue'
  import {
    YqFormSearchLocation
  } from '@/mixins/YqFormSearchLocation'
  import {
    ajaxGetAreaItems,
    ajaxGetDictItems,
    getDictItemsFromCache
  } from '@/api/api'
  import {
    deleteAction,
    getAction,
    downFile,
    getFileAccessHttpUrl
  } from '@/api/manage'
  import {
    getHostNameLocal
  } from '@/utils/util'
  export default {
    name: 'driveList',
    mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
    components: {
      JSuperQuery,
      driverLibrary,
    },
    props: {
      terminal: {
        type: String,
        default: '',
      },
    },
    data() {
      return {
        description: '一键帮助驱动管理',
        formItemLayout: {
          md: {
            span: 6,
          },
          sm: {
            span: 12,
          },
        },
        labelCol: {
          xs: {
            span: 24,
          },
          sm: {
            span: 7,
          },
        },
        wrapperCol: {
          xs: {
            span: 24,
          },
          sm: {
            span: 16,
          },
        },
        url: {
          list: '/drive/driveInfo/list',
          getCpuOsType: '/drive/driveInfo/getCpuOsType',
          exportXlsUrl: '/patch/devopePatchInfo/exportXls',
          importExcelUrl: 'patch/devopePatchInfo/importExcel',
        },
        dictOptions: [],
        cpuList: [],
      }
    },

    created() {
      this.initDictData()
    },
    mounted() {},
    computed: {
      importExcelUrl: function () {
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
      },
    },
    methods: {
      initData() {
        let hostName = getHostNameLocal()
        if (hostName === null) {
          this.$message.warning('请检查是否已经添加终端信息？')
          return
        }
        this.queryParam.terminalCode = hostName
        this.loadData(1)
        this.getCpuOsType(hostName)
      },
      getCpuOsType(code) {
        getAction(this.url.getCpuOsType, {
          terminalCode: code,
        }).then((res) => {
          if (res.success) {
            this.queryParam.driveCpu = res.result.cpuType
            this.queryParam.driveOs = res.result.osType
          }
        })
      },
      loadData(arg) {
        if (!this.url.list) {
          this.$message.error('请设置url.list属性!')
          return
        }
        var params = this.getQueryParams() //查询条件
        getAction(this.url.list, params).then((res) => {
          if (res.success) {
            //author:weng    Date:20210402  for：if(res.result.total>0) 有错误，无查询结果时，页码显示有问题
            this.ipagination.total = res.result.total
          }
          if (res.code === 510) {
            this.$message.warning(res.message)
          }
        })
      },
      initDictData() {
        //根据字典Code, 初始化字典数组
        ajaxGetDictItems('cpuType', null).then((res) => {
          if (res.success) {
            this.cpuList = res.result
          }
        })
        ajaxGetDictItems('os_type', null).then((res) => {
          if (res.success) {
            this.dictOptions = res.result
          }
        })
      },
      actionChange(value) {
        this.$forceUpdate()
        this.queryParam.terminalCode = ''
        this.queryParam.driveOs = value
      },
      cpuChange(value) {
        this.$forceUpdate()
        this.queryParam.terminalCode = ''
        this.queryParam.driveCpu = value
      },
      searchQuery() {
        if (
          (this.queryParam.driveCpu != '' && this.queryParam.driveCpu != null) ||
          (this.queryParam.driveOs != '' && this.queryParam.driveOs != null)
        ) {
          this.queryParam.terminalCode = null
        }
        this.loadData(1)
      },
      searchReset() {
        let hostName = getHostNameLocal()
        if (hostName === null) {
          this.$message.warning('请检查是否已经添加终端信息？')
          return
        }
        this.queryParam = {}
        this.queryParam.terminalCode = hostName
        this.loadData(1)
      },
      //下载
      fontClick(path) {
        window.open(window._CONFIG['downloadUrl'] + '/' + path)
      },
    },
  }
</script>
<style lang="less" scoped>
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';
  @import '~@assets/less/onclickStyle.less';

  ::-webkit-scrollbar {
    display: none;
  }

  .topImg {
    height: 50px;
    width: 100%;
    background-image: url('../../../public/oneClickHelp/localDeviceInfo/leftHeadBg.png');
    background-repeat: no-repeat;
    background-position: right center;
    position: relative;

    .topTitle {
      color: white;
      font-size: 14px;
      height: 50px;
      line-height: 50px;
      padding-left: 22px;
    }

    .head-left::before {
      position: absolute;
      content: '';
      top: 0;
      left: 0;
      width: 3px;
      height: 3px;
      background-color: #2F5BFF;
    }

    .head-left::after {
      position: absolute;
      content: '';
      bottom: 0;
      left: 0;
      width: 3px;
      height: 3px;
      background-color: #2F5BFF;
    }

    .head-right::before {
      position: absolute;
      content: '';
      top: 0;
      right: 0;
      width: 3px;
      height: 3px;
      background-color: #2F5BFF;
    }

    .head-right::after {
      position: absolute;
      content: '';
      bottom: 0;
      right: 0;
      width: 3px;
      height: 3px;
      background-color: #2F5BFF;
    }
  }

  .lightXian {
    height: 1px;
    width: 98%;
    margin: 0 auto;
    margin-top: -10px;
    background-color: #0E4589;
  }

  .driveBox {
    height: 100%;
    width: 100%;
    background-image: url('../../assets/img/yunweiBackground.png');
    background-size: 100%;
    background-repeat: no-repeat;
    background-color: #091425;
  }

  .btn-search-style {
    background-color: #409eff;
    border: 1px solid #409eff;
    height: 28px;
    width: 73px;
  }

  .card-style {
    background-color: rgba(250, 250, 250, 0);
  }

  .card-style2 {
    background-color: rgba(250, 250, 250, 0);
  }

  .table-page-search-wrapper .ant-form-inline .ant-form-item {
    margin-bottom: 0 !important;
  }

  .contentBox {
    width: 100%;
    font-family: SourceHanSansCN-Regular;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.50);
    letter-spacing: 0;
    font-weight: 400;
    padding-right: 22px;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
  }

  .libraryBox {
    height: 366px;
    width: 24%;
    border-radius: 8px;
    margin-left: 22px;
    margin-top: 24px;
    background: rgba(30, 58, 95, 0.29);
  }
</style>