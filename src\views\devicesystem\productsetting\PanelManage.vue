<template>
  <div class="panel-container">
    <!-- <div class="colorBox">
      <span class="colorTotal">设备{{isPanel}}</span>
      <a-button @click="panelChange"></a-button>
      <a-dropdown v-if="contentFlag" :trigger="['click']">
        <a-menu slot="overlay" @click="handleMenuClick($event)">
          <a-menu-item :key="item.value" v-for="item in dictOptions">
            {{item.text}}
          </a-menu-item>
        </a-menu>
        <a-button class="btn-change"> 切换面板
          <a-icon type="down" />
        </a-button>
      </a-dropdown>
      <span style="margin-left: 20px; color: #409eff; cursor: pointer" @click="doedit">
        <a-icon type="edit" style="margin-right: 6px" />编辑
      </span>
      <span style="margin-left: 20px; color: #409eff; cursor: pointer" v-if="!contentFlag" @click="dosave">
        <a-icon type="save" style="margin-right: 6px" />保存
      </span>
      <span style="margin-left: 20px; color: #409eff; cursor: pointer" v-if="!contentFlag" @click="doback">
        <a-icon type="rollback" style="margin-right: 6px" />返回
      </span>
    </div> -->
    <!-- <div :class='contentFlag ? container : containerBig' :loading="loading">
      <vis-view ref="visView" :panelJson="panelJson" v-if="contentFlag"></vis-view>
      <vis-edit ref="visEdit" :productId="data.id" @ok="saveSuccess" v-else></vis-edit>
    </div> -->
    <!-- <div v-if="contentFlag">
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0', marginRight: '12px' }" class="card-style"
        style="width: 100%">
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="端口名称">
                  <a-input placeholder="请输入设备名称" v-model="queryParam.name" :allowClear="true" autocomplete="off" />
                </a-form-item>
              </a-col>
              <a-col :span="colBtnsSpan()">
                <span class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button type="primary" class="btn-search btn-search-style" @click="dosearch">查询</a-button>
                  <a-button class="btn-reset btn-reset-style" @click="searchReset">重置</a-button>
                  <a v-if="isVisible" class="btn-updown-style" @click="doToggleSearch">
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-table ref="table" bordered rowKey="id" :columns="columns" :dataSource="dataSource"
        :scroll='dataSource.length>0?{x:"max-content"}:{}' :pagination="ipagination" :loading="loading"
        @change="handleTableChange">
      </a-table>
    </div> -->
    <panel-topo
      v-if="!isEdit"
      v-model="panelType"
      ref="panelTopo"
      operate="show-edit"
      toolbar
      :productId="productId"
      @edit="doedit"
    ></panel-topo>
    <panel-topo-edit ref="panelTopoEdit" @close="isEdit = false"></panel-topo-edit>
  </div>
</template>
<script>
// import visview from './paneltopo/VisView.vue'
// import visedit from './paneltopo/VisEdit.vue'
import { ajaxGetDictItems, getDictItemsFromCache } from '@/api/api'
// import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
// import { JeecgListMixin } from '@/mixins/JeecgListMixinNoInit'
import PanelTopoEdit from './paneltopo/PanelTopoEdit.vue'
import PanelTopo from './paneltopo/PanelTopo'
export default {
  props: {
    obj: {
      type: Object,
    },
  },
  components: {
    PanelTopoEdit,
    PanelTopo,
    // "vis-view": visview,
    // "vis-edit": visedit,
  },
  mixins: [],
  data() {
    return {
      contentFlag: true, //是否展示拓扑详情页与编辑页
      loading: false,
      panel: '',
      isPanel: '正面板',
      dictOptions: [],
      records: {},
      container: 'div-table-container',
      containerBig: 'div-table-container-big',
      panelJson: '',
      oppositeJson: '',
      dataSource: [
        {
          name: '接口1',
          name1: '端口号1',
          name2: '端口状态1',
          name4: '接口速度1',
          name5: '管理状态1',
          id: 111,
        },
      ],
      url: {
        list: '',
      },
      columns: [
        {
          title: '接口名称',
          dataIndex: 'name',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 100px;max-width:350px'
            return {
              style: cellStyle,
            }
          },
        },
        {
          title: '端口号',
          dataIndex: 'name1',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 100px;max-width:350px'
            return {
              style: cellStyle,
            }
          },
        },
        {
          title: '端口状态',
          dataIndex: 'name2',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 100px;max-width:350px'
            return {
              style: cellStyle,
            }
          },
        },
        {
          title: '接口速度',
          dataIndex: 'name4',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 100px;max-width:350px'
            return {
              style: cellStyle,
            }
          },
        },
        {
          title: '管理状态',
          dataIndex: 'name5',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 100px;max-width:350px'
            return {
              style: cellStyle,
            }
          },
        },
      ],
      isEdit: false,
      panelType: '0',
      productId: '',
    }
  },

  created() {
    this.productId = this.obj.id || ''
  },
  mounted() {},
  methods: {
    show(record) {
      // this.records = record
      // let jsonData = JSON.parse(this.records.panelJson)
      // //根据字典Code, 初始化字典数组
      // ajaxGetDictItems('panelType', null).then((res) => {
      //   if (res.success) {
      //     this.dictOptions = res.result
      //     this.panel = this.dictOptions[0].value
      //     this.panelJson = jsonData[this.panel]
      //   }
      // })
    },
    handleMenuClick(e) {
      this.panel = e.key
      let jsonData = JSON.parse(this.records.panelJson)
      let ele = this.dictOptions.filter((item) => {
        return this.panel == item.value
      })
      this.isPanel = ele[0].text
      this.panelJson = jsonData[this.panel]
      this.$nextTick(() => {
        this.$refs.visView.show(this.panelJson)
      })
    },
    initDictData() {},
    //表单查询,点击查询按钮，默认查询第一页
    dosearch() {
      this.loadData(1)
    },
    doedit() {
      this.isEdit = true
      // console.log('this.recodds == ', this.obj)
      this.$refs.panelTopoEdit.edit(this.obj, this.panelType)
      // this.contentFlag = false
      // this.$nextTick(() => {
      //   this.$refs.visEdit.create(this.panelJson, JSON.parse(this.records.panelJson), this.panel)
      // })
    },
    dosave() {
      this.loading = true
      this.$refs.visEdit.save()
    },
    doback() {
      this.contentFlag = true
    },
    saveSuccess(json) {
      this.loading = false
      this.panelJson = json
      let jsonData = JSON.parse(this.records.panelJson)
      jsonData[this.panel] = json
      this.records.panelJson = JSON.stringify(jsonData)
      this.contentFlag = true
    },
    
  },
}
</script>
<style lang="less" scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';

.panel-container {
  height: calc(100% - 4px);
}

.colorBox {
  margin-bottom: 20px;
}

.colorTotal {
  padding-left: 7px;
}

.div-table-container {
  height: 300px;
  border-radius: 4px;
}

.div-table-container-big {
  height: 600px;
  border-radius: 4px;
}

.btn-change {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  width: 100px;
  height: 32px;
  font-family: PingFangSC-Regular;
  font-size: 13px;
  color: #747679;
  margin-left: 8px;
}
</style>