<template>
  <a-row style='height: 100%'>
    <a-col style='height: 100%; display: flex; flex-direction: column'>
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0', marginRight: '12px' }" class='card-style'>

        <!-- 查询区域 -->
        <div class='table-page-search-wrapper'>
          <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
            <a-row :gutter='24' ref='todoRow'>
              <a-col :span="spanValue">
                <a-form-item label="关键字">
                  <a-input v-model='queryParam.keyword' :maxLength='maxLength' :allow-clear='true' autocomplete='off'
                           placeholder='请输入关键字' />
                </a-form-item>
              </a-col>
              <a-col v-show="getVisible('processInstanceName')" :span="spanValue">
                <a-form-item :label="getTitle('processInstanceName')">
                  <a-input :maxLength='maxLength' v-model='queryParam.processInstanceName' :allow-clear='true' autocomplete='off'
                           placeholder='请输入业务标题' type='' />
                </a-form-item>
              </a-col>
              <a-col v-show="getVisible('name')" :span="spanValue">
                <a-form-item :label="getTitle('name')">
                  <a-input :maxLength='maxLength' :allow-clear='true' autocomplete='off' v-model='queryParam.taskName'
                           placeholder='请输入任务名称'
                           type='' />
                </a-form-item>
              </a-col>
              <a-col v-show="getVisible('createTime')" :span="spanValue">
                <a-form-item :label="getTitle('createTime')">
                  <a-range-picker :getCalendarContainer="node=> node.parentNode" style='width: 100%'
                                  v-model='queryParam.searchCreateTime' :placeholder="['开始时间', '结束时间']"
                                  format='YYYY-MM-DD HH:mm:ss'
                                  showTime @change='onCreatedTimeChange' />
                </a-form-item>
              </a-col>
              <a-col v-show="getVisible('dueDate')" :span="spanValue">
                <a-form-item :label="getTitle('dueDate')">
                  <a-range-picker :getCalendarContainer="node=> node.parentNode" style='width: 100%'
                                  v-model='queryParam.searchDueDate' :placeholder="['开始时间', '结束时间']"
                                  format='YYYY-MM-DD HH:mm:ss' showTime
                                  @change='onDueDateChange' />
                </a-form-item>
              </a-col>
              <a-col v-show="getVisible('ownerName')" :span="spanValue">
                <a-form-item :label="getTitle('ownerName')">
                  <a-select :getPopupContainer='node=>node.parentNode' :allow-clear='true'
                            v-model="queryParam.taskOwner"
                            show-search placeholder="请选择所有人" option-filter-prop="children"
                            :filter-option="filterOption">
                    <a-select-option v-for="(item, key) in userList" :key="key" :value="item.username">
                      <div style="display: inline-block; width: 100%" :title="item.realname">
                        {{ item.realname }}
                        <span style="font-size: 6px; color: rgba(0, 0, 0, 0.45);">{{
                            '(' + item.username + ')'
                          }}</span>
                      </div>
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col v-show="getVisible('assigneeName')" :span="spanValue">
                <a-form-item :label="getTitle('assigneeName')">
                  <a-select :getPopupContainer='node=>node.parentNode' :allow-clear='true'
                            v-model="queryParam.taskAssignee"
                            show-search placeholder="请选择执行人" option-filter-prop="children"
                            :filter-option="filterOption">
                    <a-select-option v-for="(item, key) in userList" :key="key" :value="item.username">
                      <div style="display: inline-block; width: 100%" :title="item.realname">
                        {{ item.realname }}
                        <span style="font-size: 6px; color: rgba(0, 0, 0, 0.45);">{{
                            '(' + item.username + ')'
                          }}</span>
                      </div>
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col v-show="getVisible('e')" :span="spanValue">

                <a-form-item :label="getTitle('assigneeName')">
                  <a-select :getPopupContainer='node=>node.parentNode' :allow-clear='true'
                            v-model="queryParam.taskAssignee"
                            show-search placeholder="请选择执行人" option-filter-prop="children"
                            :filter-option="filterOption">
                    <a-select-option v-for="(item, key) in userList" :key="key" :value="item.username">
                      <div style="display: inline-block; width: 100%" :title="item.realname">
                        {{ item.realname }}
                        <span style="font-size: 6px; color: rgba(0, 0, 0, 0.45);">{{
                            '(' + item.username + ')'
                          }}</span>
                      </div>
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col v-show="getVisible('overTime')" :span="spanValue">
                <a-form-item :label="getTitle('overTime')">
                  <a-select :allow-clear='true' autocomplete='off' v-model='queryParam.dueTimeout' placeholder='请选择'
                  >
                    <a-select-option value='1'>已超时</a-select-option>
                    <a-select-option value='2'>正常</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col v-show="getVisible('slaType')" :span="spanValue">
                <a-form-item label="SLA类型">
                  <a-select :allow-clear='true' autocomplete='off' v-model='queryParam.slaType' placeholder='请选择'
                  >
                    <a-select-option v-for="item in slaData" :label='item.title' :key="'slaType_'+item.value"
                                     :value="item.value">{{ item.title }}
                    </a-select-option>

                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <span class="table-page-search-submitButtons" style="overflow: hidden;">
                  <a-button icon="search" type="primary" @click="searchQuery">查询</a-button>
                  <a-button icon="reload" style="margin-left: 8px" @click="searchReset">重置</a-button>
                  <a v-if="queryItems.length>0" style="margin-left: 8px" @click="doToggleSearch">{{queryName}}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
        <!-- 查询区域-END -->

        <!--自定义查询项 -->
        <div v-if="toggleSearchStatus" class="custom-query-item">
          <a-checkbox-group v-model="settingQueryItems" :defaultValue="settingQueryItems" style="width:100%"
                            @change="onQuerySettingsChange">
            <a-row :gutter="24">
              <template v-for="(item,index) in queryItems">
                <a-col v-show='item.checked' :span='querySpanValue' class='col-checkbox'>
                  <a-checkbox :value="item.dataIndex">
                    <j-ellipsis :length="7" :value="item.title"></j-ellipsis>
                  </a-checkbox>
                </a-col>
              </template>
            </a-row>
          </a-checkbox-group>
        </div>
        <!-- 自定义查询项-END -->
      </a-card>

      <a-card :bordered='false' style='width: 100%; flex: auto'>

        <!--     操作按钮区域 -->
        <!--    <div class="table-operator">-->
        <!--      <a-dropdown v-if="selectedRowKeys.length > 0">-->
        <!--        <a-menu slot="overlay" style='text-align: center'>-->
        <!--          <a-menu-item key="1" @click="batchDel">-->
        <!--            删除-->
        <!--          </a-menu-item>-->
        <!--        </a-menu>-->
        <!--        <a-button style="margin-left: 8px"> 批量操作-->
        <!--          <a-icon type="down"/>-->
        <!--        </a-button>-->
        <!--      </a-dropdown>-->
        <!--    </div>-->

        <!-- table区域-begin -->
        <div>
          <!--      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
                  <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{
                    selectedRowKeys.length
                  }}</a>项
                  <a style="margin-left: 24px" @click="onClearSelected">清空</a>
                  <span style="float:right;">
                    <a @click="loadData()"><a-icon type="sync"/>刷新</a>
                    <a-divider type="vertical"/>
                    <a-popover placement="leftBottom" title="自定义列" trigger="click">
                      <template slot="content">
                        <a-checkbox-group v-model="settingColumns" :defaultValue="settingColumns" @change="onColSettingsChange">
                          <a-row style="width: 400px">
                            <template v-for="(item,index) in defColumns">
                              <template v-if="item.key!='rowIndex'&& item.dataIndex!='action'">
                                  <a-col :key="index" :span="12"><a-checkbox :value="item.dataIndex"><j-ellipsis
                                    :length="10" :value="item.title"></j-ellipsis></a-checkbox></a-col>
                              </template>
                            </template>
                          </a-row>
                        </a-checkbox-group>
                      </template>
                      <a><a-icon type="setting"/>设置</a>
                    </a-popover>
                  </span>
                </div>-->

          <a-table
            ref='table'
            bordered
            rowKey='id'
            :columns='columns'
            :dataSource='dataSource'
            :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
            :pagination='ipagination'
            :loading='loading'
            @change='handleTableChange'>
            <div slot='filterDropdown'>
              <a-card>
                <a-checkbox-group v-model='settingColumns' :defaultValue='settingColumns' @change='onColSettingsChange'>
                  <a-row style='width: 400px'>
                    <template v-for='(item,index) in defColumns'>
                      <template v-if="item.key!='rowIndex'&& item.dataIndex!='action'">
                        <a-col :span='12'>
                          <a-checkbox :value='item.dataIndex'>
                            <j-ellipsis :length='10' :value='item.title'></j-ellipsis>
                          </a-checkbox>
                        </a-col>
                      </template>
                    </template>
                  </a-row>
                </a-checkbox-group>
              </a-card>
            </div>

            <template slot='tooltip' slot-scope='text'>
              <a-tooltip placement='top'
                         :title='text'
                         trigger='hover'>
                <div class='tooltip'>
                  {{ text }}
                </div>
              </a-tooltip>
            </template>

            <template slot='tooltip2' slot-scope='text'>
              <a-tooltip placement='topLeft'
                         :title='text'
                         trigger='hover'>
                <div class='tooltip'>
                  {{ text }}
                </div>
              </a-tooltip>
            </template>
            <a-icon slot='filterIcon' :style="{ fontSize:'16px',color:  '#108ee9' }" type='setting' />

            <span slot='slaType' slot-scope='text, record'>
              <span v-for="item in slaData" :key="item.value">
                <span v-if="item.value===text">
                  {{item.title}}
                </span>
              </span>

            </span>
            <span slot='overTime' slot-scope='text, record'>
              <span v-if="text" style="color:red">{{text}}</span>
              <span v-else style="color:green">正常</span>

            </span>
            <span slot='action' slot-scope='text, record'>
              <a @click='btnView(record)'>查看</a>
              <span v-if="record.assignee==null||record.assignee==''">
                <a-divider type='vertical' />
                <a @click='btnClaim(record)'>认领并处理</a>
              </span>
              <span v-if="record.assignee===$store.getters.userInfo.username && (record.endTime==null || record.endTime=='')
                  && (record.claimTime!=null && record.claimTime!='')">
                <a-divider type='vertical' />
                <a v-if="record.endTime == null && record.assignee != null && record.assignee != ''"
                   @click='btnUnclaim(record)'>取消认领</a>
              </span>
              <span v-if="record.endTime==null&&record.assignee!=null&&record.assignee!=''">
                <a-divider type='vertical' />
                <a @click='btnExcuteTask(record)'>处理</a>
              </span>
              <!--         <a-dropdown>
                          <a class="ant-dropdown-link">更多 <a-icon type="down"/></a>
                          <a-menu slot="overlay">
                            <a-menu-item>
                              <a @click="btnView(record)">查看详情</a>
                            </a-menu-item>
                            <a-menu-item v-if="record.assignee==null||record.assignee==''">
                            <a @click="btnClaim(record)">认领并处理</a>
                            </a-menu-item>
                            <a-menu-item v-if="record.assignee===$store.getters.userInfo.username && (record.endTime==null || record.endTime=='')
                            && (record.claimTime!=null && record.claimTime!='')">
                            <a @click="btnUnclaim(record)">取消签收</a>
                            </a-menu-item>
                            <a-menu-item v-if="record.endTime==null&&record.assignee!=null&&record.assignee!=''">
                            <a @click="btnExcuteTask(record)">处理</a>
                            </a-menu-item>
                          </a-menu>
                        </a-dropdown>-->
            </span>

          </a-table>
        </div>
        <!-- table区域-end -->
      </a-card>

      <!-- 一查看详情区域 -->
      <process-instance-info-modal ref="processInstanceInfoModalForm" @ok="modalFormOk"></process-instance-info-modal>

      <!--    任务执行区域-->
      <execute-task v-if="dialogExecuteTaskVisible" :execute-task-id="executeTaskId" :modelKey="selectionRow.modelKey"
                    :processInstanceId.sync="processInstanceId" :selectRow="selectionRow"
                    :visible.sync="dialogExecuteTaskVisible"
                    @ok="modalFormOk"></execute-task>
    </a-col>
  </a-row>
</template>

<script>
import { ajaxGetDictItems } from '@api/api'
import JInput from '@/components/jeecg/JInput.vue'
import {
  initDictOptions
} from '@/components/dict/JDictSelectUtil'
import {
  JeecgListMixin
} from '@/mixins/JeecgListMixin'
import Vue from 'vue'

import {
  taskTodoApi
} from '@api/flowable'
import ProcessInstanceInfoModal from '../process-instance/modules/ProcessInstanceInfoModal'
import executeTask from './modules/executeTask'
import {
  YqFormSeniorSearchLocation
} from '@/mixins/YqFormSeniorSearchLocation'
import {
  getUserList
} from '@api/api'

export default {
  name: 'taskTodo',
  mixins: [JeecgListMixin, YqFormSeniorSearchLocation],
  components: {
    JInput,
    ProcessInstanceInfoModal,
    executeTask
  },
  props: {
    includeFilter: {
      required: false,
      type: String,
      description: '需要过滤的流程定义keys'
    }
  },

  data() {
    return {
      maxLength:50,
      isInit: false,
      formItemLayout: {
        labelCol: {
          style: 'width:80px'
        },
        wrapperCol: {
          style: 'width:calc(100% - 80px)'
        }
      },
      selectionRow: {},
      description: '我的待办',
      //字典数组缓存
      sexDictOptions: [],
      //列设置
      settingColumns: [],
      //表头
      columns: [{
        title: '序号',
        width: 60,
        dataIndex: '',
        key: 'rowIndex',
        isUsed: false,
        customCell: () => {
          let cellStyle = 'text-align:center;'
          return {
            style: cellStyle
          }
        },
        customRender: function(t, r, index) {
          return parseInt(index) + 1
        }
      },
        {
          title: '业务标题',
          dataIndex: 'processInstanceName',
          isUsed: true,
          customCell: () => {
            let cellStyle = 'text-align:center;min-width: 150px;max-width:300px'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'tooltip'
          }
        },
        {
          title: '任务名称',
          dataIndex: 'name',
          isUsed: true,
          customCell: () => {
            let cellStyle = 'text-align:center;min-width: 150px;max-width:300px'
            return {
              style: cellStyle
            }
          },
          sorter: true
        },
        {
          title: '开始时间',
          dataIndex: 'createTime',
          isUsed: true,
          customCell: () => {
            let cellStyle = 'text-align:center;width: 180px'
            return {
              style: cellStyle
            }
          },
          sorter: true
        },
        {
          title: 'SLA类型',
          dataIndex: 'slaType',
          isUsed: true, scopedSlots: {
            /*  filterDropdown: 'filterDropdown',
              filterIcon: 'filterIcon',*/
            customRender: 'slaType'
          },
          customCell: () => {
            let cellStyle = 'text-align:center;width:160px'
            return {
              style: cellStyle
            }
          }
        }, {
          title: '到期时间',
          dataIndex: 'dueDate',
          isUsed: true,
          customCell: () => {
            let cellStyle = 'text-align:center;width:160px'
            return {
              style: cellStyle
            }
          },
          sorter: true
        },
        {
          title: 'SLA状态',
          dataIndex: 'overTime',
          isUsed: true,
          scopedSlots: {
            customRender: 'overTime'
          },
          customCell: () => {
            let cellStyle = 'text-align:center;width: 20px;'
            return {
              style: cellStyle
            }
          },
          sorter: false
        },
        {
          title: '执行人',
          dataIndex: 'assigneeName',
          isUsed: true,
          customCell: () => {
            let cellStyle = 'text-align:center;width: 200px'
            return {
              style: cellStyle
            }
          },
          sorter: true
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          isUsed: false,
          width: 220,
          scopedSlots: {
            /*  filterDropdown: 'filterDropdown',
              filterIcon: 'filterIcon',*/
            customRender: 'action'
          }
        }
      ],
      url: {
        list: '/flowable/task/listTodo'
        // delete: '/test/jeecgDemo/delete',
        // deleteBatch: '/test/jeecgDemo/deleteBatch',
        // exportXlsUrl: '/test/jeecgDemo/exportXls'
      },
      processInstanceId: '',
      dialogExecuteTaskVisible: false,
      executeTaskId: '',
      userList: [],
      slaData: [],
      disableMixinCreated: true,
      rowName: 'todoRow',
      queryCacheName: 'todoQuerySettings',
      tableCacheName: 'todoColsettings'
    }
  },
  computed: {
    tasktimestimp() {
      return this.$store.getters.tasktimestimp
    }
  },
  watch: {
    tasktimestimp: {
      handler(nval, oval) {
        if (this.isInit) this.loadData()
      },
      deep: true,
      immediate: true
    }
  },

  created() {
    if (this.includeFilter) {
      this.queryParam = {
        processDefinitionKey: this.includeFilter
      }
    }
    this.loadData(1)
    this.getValueByCode(1)
    this.getColumns(this.columns)
    this.getuserList()
  },
  mounted() {
    this.isInit = true
  },
  methods: {
    getValueByCode() {
      //根据字典Code, 初始化字典数组
      ajaxGetDictItems('slaType').then((res) => {
        if (res.success && res.result) {
          this.slaData = res.result
        }
      })
    },
    searchReset() {
      if (this.includeFilter) {
        this.queryParam = {
          processDefinitionKey: this.includeFilter
        }
      } else {
        this.queryParam = {}
      }
      this.loadData(1)
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      )
    },
    getuserList() {
      let param = {
        pageSize: 10000
      }
      getUserList(param).then((res) => {
        if (res.success) {
          this.userList = res.result.records
        }
      })
    },
    onCreatedTimeChange: function(value, dateString) {
      this.queryParam.taskCreatedAfter = dateString[0]
      this.queryParam.taskCreatedBefore = dateString[1]
    },
    onDueDateChange: function(value, dateString) {
      this.queryParam.taskDueAfter = dateString[0]
      this.queryParam.taskDueBefore = dateString[1]
    },
    btnClaim(row) {
      let current = this
      taskTodoApi.claim({
        taskId: row.id
      }).then((res) => {
        if (res.success) {
          this.btnExcuteTask(row)
        } else {
          current.$message.error(res.message)
        }
        current.modalFormOk()
      })
    },
    btnUnclaim(row) {
      let current = this
      taskTodoApi.unclaim({
        taskId: row.id
      }).then((res) => {
        if (res.success) {
          current.$message.success(res.message)
        } else {
          current.$message.error(res.message)
        }
        current.modalFormOk()
      })
    },
    btnExcuteTask(row) {
      this.selectionRow = row
      this.executeTaskId = row.id
      this.processInstanceId = row.processInstanceId
      this.dialogExecuteTaskVisible = true
    },
    btnView(record) {
      this.$refs.processInstanceInfoModalForm.init(record.processInstanceId)
      this.$refs.processInstanceInfoModalForm.title = '流程实例信息'
      this.$refs.processInstanceInfoModalForm.disableSubmit = false
    },
    initDictConfig() {
      //初始化字典 - 性别
      initDictOptions('sex').then((res) => {
        if (res.success) {
          this.sexDictOptions = res.result
        }
      })
    },
    //列设置更改事件
    onColSettingsChange(checkedValues) {
      var key = this.$route.name + ':colsettings'
      Vue.ls.set(key, checkedValues, 7 * 24 * 60 * 60 * 1000)
      this.settingColumns = checkedValues
      const cols = this.defColumns.filter(item => {
        if (item.key == 'rowIndex' || item.dataIndex == 'action') {
          return true
        }
        if (this.settingColumns.includes(item.dataIndex)) {
          return true
        }
        return false
      })
      this.columns = cols
    },
    initColumns() {
      //权限过滤（列权限控制时打开，修改第二个参数为授权码前缀）
      //this.defColumns = colAuthFilter(this.defColumns,'testdemo:');

      var key = this.$route.name + ':colsettings'
      let colSettings = Vue.ls.get(key)
      if (colSettings == null || colSettings == undefined) {
        let allSettingColumns = []
        this.defColumns.forEach(function(item, i, array) {
          allSettingColumns.push(item.dataIndex)
        })
        this.settingColumns = allSettingColumns
        this.columns = this.defColumns
      } else {
        this.settingColumns = colSettings
        const cols = this.defColumns.filter(item => {
          if (item.key == 'rowIndex' || item.dataIndex == 'action') {
            return true
          }
          if (colSettings.includes(item.dataIndex)) {
            return true
          }
          return false
        })
        this.columns = cols
      }
    }
  }
}
</script>
<style scoped lang='less'>
@import '~@assets/less/common.less';
@import '~@assets/less/YQCommon.less';
@import '~@assets/less/scroll.less';
</style>