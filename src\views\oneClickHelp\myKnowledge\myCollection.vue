<template>
  <div style="margin-top:15px;">
    <!-- 查询区域 -->
    <div class='table-page-search-wrapper'>
      <a-form layout='inline' @keyup.enter.native='searchQuery'>
        <a-row :gutter='24' ref='row'>
          <a-col :span='6'>
            <a-form-item :label="'标\u3000题'">
              <a-input placeholder='请输入标题' v-model='queryParam.title' :allowClear='true' autocomplete='off'
                :maxLength="maxLength" />
            </a-form-item>
          </a-col>
          <a-col :span='6'>
            <span class='table-page-search-submitButtons'>
              <a-button type='primary' class='btn-search btn-search-style' @click='searchQuery'>查询</a-button>
              <a-button class='btn-reset btn-reset-style' @click='searchReset'>重置</a-button>
              <a-button v-if="selectedRowKeys.length > 0" class="btn-reset btn-reset-style"
                @click="cancelCollectBatch">取消</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <a-table size='middle' ref="table" rowKey="id" :columns="columns" :dataSource="dataSource" :pagination="ipagination"
      :loading="loading" :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      class="j-table-force-nowrap" @change="handleTableChange" :showHeader="false" :bordered="false" :locale='locale'>
      <span slot="action" slot-scope="text, record" class="caozuo"
        style="display: inline-block; white-space: nowrap; text-align: center">

        <span class="action-icon" @click="handleDetailPage(record)">
          <img src="../../../../public/oneClickHelp/look.png" alt />
        </span>
        <span class="action-icon" @click="handleCancelCollect(record.id)" style="margin-left: 32px;">
          <img src="../../../../public/oneClickHelp/delete.png" alt />
        </span>

      </span>
      <span slot="tooltip" slot-scope="text, record">
        <commonItem :record="record" @handleDetailPage="handleDetailPage"></commonItem>
      </span>
    </a-table>
  </div>
</template>

<script>
import Empty from '@/components/oneClickHelp/Empty.vue'
import commonItem from './modules/commonItem.vue'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
import {
  postParamsAction
} from '@/api/manage'
export default {
  name: 'myCollection',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components: {Empty, commonItem},
  data() {
    return {
      maxLength:50,
      locale: {
        emptyText: <Empty/>
      },
      columns: [
        {
          title: '标题',
          dataIndex: 'title',
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 300px;'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'tooltip'
          }
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 200,
          scopedSlots: {
            customRender: 'action'
          }
        }
      ],
      url: {
        list: '/kbase/knowledges/usercollect',
        uncollect: '/kbase/knowledges/uncollect',
        uncollectBatch: '/kbase/knowledges/uncollectBatch'
      },
    }
  },
  activated() {
    console.log('激活我的收藏')
    this.loadData()
  },
  methods: {
    // 批量取消收藏
    cancelCollectBatch: function () {
      let that = this;
      if (that.selectedRowKeys.length <= 0) {
        that.$message.warning('请选择一条记录！')
        return
      } else {
        var ids = ''
        for (var a = 0; a < this.selectedRowKeys.length; a++) {
          ids += this.selectedRowKeys[a] + ','
        }

        this.$confirm({
          title: '确认取消',
          okText: '是',
          cancelText: '否',
          content: '是否取消选中数据?',
          class: 'oneClickHelpConfirmModal',
          onOk: function () {
            that.loading = true
            postParamsAction(that.url.uncollectBatch, { knowledgeIds: ids })
              .then((res) => {
                if (res.success) {
                  that.$message.success(res.message)
                  that.loadData()
                  that.onClearSelected()
                } else {
                  that.$message.warning(res.message)
                }
              })
              .catch(() => {
                that.loading = false
              })
          }
        })
      }
    },
    //单条取消收藏
    handleCancelCollect(id) {
      var that = this
      that.$confirm({
        title: '确认取消',
        okText: '是',
        cancelText: '否',
        content: '是否取消选中数据?',
        class: 'oneClickHelpConfirmModal',
        onOk: function () {
          that.loading = true
          postParamsAction(that.url.uncollect, { knowledgeId: id }).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.loadData()
            } else {
              that.$message.warning(res.message)
              that.loadData()
            }
          })
            .catch(() => {
              that.loading = false
            })
        }
      })
    },
    // 查看
    handleDetailPage: function (record) {
      this.$emit('getRecord', record)
    }
  },
}

</script>
<style lang='less' scoped>
@import '~@assets/less/onclickStyle.less';
@import './modules/myKnowledge.less';
</style>
