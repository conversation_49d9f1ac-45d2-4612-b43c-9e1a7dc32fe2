<template>
  <j-modal :title="title"
           :visible="visible"
           :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
           :width="900"
           :centered="true"
           switchFullscreen
           :maskClosable='true'
           cancelText="关闭"
           @cancel="handleCancel"
           @ok='handleOk'
  >
    <a-form-model ref="form" :model="form" :rules="validatorRules" :label-col='labelCol' :wrapper-col='wrapperCol'>
      <a-row :gutter="24">
        <a-col v-bind='formItemLayout'>
          <a-form-model-item label="班次名称" prop="shiftName">
            <a-input v-model="form.shiftName" placeholder='请输入班次名称'/>
          </a-form-model-item>
        </a-col>
        <a-col v-bind='formItemLayout'>
          <a-form-model-item label="时间范围" prop="time">
            <a-row style='width: 100%;'>
              <a-col :xl='11' :lg='11' :sm='23' :xs='23'>
                <a-time-picker style='width: 100%'
                               placeholder='请选择开始时间'
                               v-model="form.start"
                               format="HH:mm"
                               @change="onChangeStartTime"
                />
              </a-col>
              <a-col :xl='2' :lg='2' :sm='1' :xs='1'>
                <div style="margin: 0 5px;text-align: center">~</div>
              </a-col>
              <a-col :xl='11' :lg='11' :sm='23' :xs='23'>
                <span v-if="show" style="color: red">次日</span>
                <a-time-picker :style="show?'width: 89%':'width: 100%'"
                               placeholder='请选择结束时间'
                               v-model="form.end"
                               format="HH:mm"
                               @change="onChangeEndTime"
                />
              </a-col>
            </a-row>

          </a-form-model-item>
        </a-col>
<!--        <a-col v-bind='formItemLayout'>
          <a-form-model-item label="提醒时间" prop="remind" required>
            <a-select
              :getPopupContainer='node=>node.parentNode'
              v-model='form.remind'
              :allow-clear='true'
              placeholder='请选择提醒时间'
            >
              <a-select-option value="0">不提醒</a-select-option>
              <a-select-option value="1">开始前</a-select-option>
              <a-select-option value="2">5分钟前</a-select-option>
              <a-select-option value="3">15分钟前</a-select-option>
              <a-select-option value="4">30分钟前</a-select-option>
              <a-select-option value="5">1小时前</a-select-option>
              <a-select-option value="6">2小时前</a-select-option>
              <a-select-option value="7">1天前</a-select-option>
              <a-select-option value="8">2天前</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>-->
        <a-col v-bind='formItemLayout'>
          <a-form-model-item label="颜色选择" required>
            <a-tooltip v-for="(item, index) in colorList" :key="index" class="setting-drawer-theme-color-colorBlock">
              <template slot="title">
                {{ item.key }}
              </template>
              <a-tag :color="item.color" @click="changeColor(item.color)">
                <a-icon type="check" v-if="item.color === form.color"></a-icon>
              </a-tag>
            </a-tooltip>
          </a-form-model-item>
        </a-col>
        <a-col v-bind='formItemLayout'>
          <a-form-model-item label="说明" prop="remarks">
            <a-textarea
              v-model="form.remarks"
              placeholder="请输入描述信息"
              :auto-size="{ minRows: 3, maxRows: 8 }"
            />
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
  </j-modal>
</template>

<script>
import {getAction, httpAction} from '@api/manage'
import {colorList} from '@comp/tools/setting'
import moment from 'moment'

export default {
  name: 'dutyShiftModal',
  data() {
    return {
      visible: false,
      show: false,
      title: '',
      disableSubmit: true,
      form: {
        color: "#1890FF",
        start: null,
        end: null,
        time: null,
        remind: null,
      },
      formItemLayout: {
        md: {span: 24},
        sm: {span: 24}
      },
      labelCol: {
        xs: {span: 24},
        sm: {span: 5},
        md: {span: 5},
        lg: {span: 5}
      },
      wrapperCol: {
        xs: {span: 24},
        sm: {span: 16},
        md: {span: 15},
        lg: {span: 16}
      },
      validatorRules: {
        shiftName: [
          {required: true, message: '请输入班次名称!'},
          {min: 1, max: 500, message: '班次名称应在[1-50]个字符之间', trigger: 'change'},
        ],
        color: [
          {required: true, message: '请选择颜色!'},
        ],
        time: [
          {required: true, validator: this.validateTime, trigger: 'change'},
        ],
        remarks: [
          {required: false},
          {min: 1, max: 200, message: '班次名称应在[1-200]个字符之间', trigger: 'change'},
        ]
      },
      colorList,
      moment,
    }
  },
  watch:{
    form(val){
      if (val.startTime===undefined&&val.endTime===undefined){
        this.show=false
        return
      }
      if (val.endTime&&val.startTime&&this.timeToSec(val.endTime+":00")-this.timeToSec(val.startTime+":00")>0){
        this.show=false
      }else{
        this.show=true
      }
    }
  },
  methods: {
    validateTime(rule, value, callback) {
      let isHasStartTime = this.form.startTime === undefined || this.form.startTime === "" || this.form.startTime === null
      let isHasEndTime = this.form.endTime === undefined || this.form.endTime === "" || this.form.endTime === null

      if (isHasStartTime && isHasEndTime) {
        callback(new Error("请选择时间范围"));
      }

      if (isHasStartTime) {
        callback(new Error("请选择开始时间"));
      }
      if (isHasEndTime) {
        callback(new Error("请选择结束时间"));
      }
      callback()
    },
    //获取不可选择的小时
    disabledStartHours() {
      if (this.form.end) {
        let hours = [];
        let hour = moment(this.form.end).hour();
        for (let i = hour + 1; i < 24; i++) {
          hours.push(i);
        }
        return hours
      }
    },
    //获取不可选择的分钟
    disabledStartMinutes(selectedHour) {
      if (this.form.end) {
        let minutes = [];
        let hour = moment(this.form.end).hour();
        let minute = moment(this.form.end).minute();

        if (selectedHour === hour) {
          for (let i = minute + 1; i < 60; i++) {
            minutes.push(i);
          }
        }
        return minutes
      }
    },
    //获取不可选择的小时
    disabledEndHours() {
      if (this.form.start) {
        let hours = [];
        let hour = moment(this.form.start).hour();
        for (let i = 0; i < hour; i++) {
          hours.push(i);
        }
        return hours
      }
    },
    //获取不可选择的分钟
    disabledEndMinutes(selectedHour) {
      if (this.form.start) {
        let minutes = [];
        let hour = moment(this.form.start).hour();
        let minute = moment(this.form.start).minute();
        if (selectedHour === hour) {
          for (let i = 0; i < minute; i++) {
            minutes.push(i);
          }
        }
        return minutes
      }
    },
    onChangeStartTime(date, dateString) {
      this.form.startTime = dateString;
      if (this.form.endTime===undefined||this.form.endTime===""){
        this.show=false
        return
      }
      if (this.timeToSec(this.form.endTime+":00")-this.timeToSec(this.form.startTime+":00")>0){
        this.show=false
      }else{
        this.show=true
      }
    },
    onChangeEndTime(date, dateString) {
      this.form.endTime = dateString;
      if (this.form.startTime===undefined||this.form.startTime===""){
        return
      }
      if (this.timeToSec(this.form.endTime+":00")-this.timeToSec(this.form.startTime+":00")>0){
        this.show=false
      }else{
        this.show=true
      }
    },
    /**
     * 将时分秒转为时间戳
     *
     * @param time
     * @returns {number}
     */
    timeToSec(time) {
      if (time !== null) {
        var s = "";
        var hour = time.split(":")[0];
        var min = time.split(":")[1];
        var sec = time.split(":")[2];
        s = Number(hour * 3600) + Number(min * 60) + Number(sec);
        return s;
      }
    },
    /**
     * 选择颜色
     * @param color
     */
    changeColor(color) {
      this.form.color = color
    },
    /**
     * 添加
     */
    add() {
      this.form = {color: "#1890FF"};
      this.visible = true
    },
    /**
     * 编辑
     *
     * @param item
     */
    edit(item) {
      this.form = {color: "#1890FF", skip: false};
      getAction("/duty/shift/queryById", {id: item.id}).then((res) => {
        if (res.success) {
          res.result.start = new Date("1997-02-11 " + res.result.startTime);
          res.result.end = new Date("1997-02-11 " + res.result.endTime);
          let formbrefore = JSON.parse(JSON.stringify(res.result));
          this.form = Object.assign({}, formbrefore);
          this.form.time = this.form.start && this.form.end ? this.form.start + '~' + this.form.end : null
          this.visible = true;
        } else {
          this.$message.warning(res.message);
        }
      })
    },
    handleCancel() {
      this.visible = false
    },
    handleOk() {
      this.$refs.form.validate(valid => {
        if (valid) {
          let url = "/duty/shift/add"
          if (this.form.id !== undefined) {
            url = "/duty/shift/edit"
          }
          httpAction(url, this.form, "post").then((res) => {
            if (res.success) {
              this.$message.success("操作成功")
              this.handleCancel()
              this.$emit("loadData")
              this.$emit("loadData1")
            } else {
              this.$message.warning(res.message)
            }
          })
        }
      })
    },
  }

}
</script>

<style lang="less" scoped>
@import '~@assets/less/normalModal.less';

.setting-drawer-theme-color-colorBlock {
  width: 19px;
  height: 19px;
  margin-top: 10px;
  float: left;
  margin-right: 8px;
  padding-left: 0px;
  padding-right: 0px;
  text-align: center;

}
</style>