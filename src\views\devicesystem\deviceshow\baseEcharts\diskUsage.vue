<template>
  <!-- 磁盘使用情况 -->
  <div style="height: 100%;width: 100%;">
    <recent-time @changeType="changeType" :selectedIndex="selectedIndex"></recent-time>
    <div ref="baseEcharts" style="height: 100%;width: 100%;"></div>
  </div>
</template>

<script>
import recentTime from './recentTime.vue'
import { chartMixins } from './chartMixins'
export default {
  name: 'diskUsage',
  mixins: [chartMixins],
  components: {
    recentTime
  },
  props: {
    chartData: {
      type: Object,
      default: () => {}
    },
    fontSizeObject: {
      type: Object,
      default: function () {
        return {
          legendFontSize: 8,
          xAxisFontSize: 8,
          yAxisFontSize: 10
        }
      }
    }
  },
  watch: {
    chartData: {
      handler(nVal, oVal) {
        this.$nextTick(() => {
          this.initData(nVal)
        })
      },
      deep: true,
      immediate: true
    }
  },
  data() {
    return {
      myChart: null,
      selectedIndex: 0
    }
  },
  methods: {
    changeType(index) {
      this.selectedIndex = index
      this.initData()
    },
    initData() {
      let colors = ['rgba(91,143,249,0.85)', 'rgba(255,179,0,0.85)']
      let xData = []
      let yData = []
      let xAllData = []
      let yAllData = [] // 30日磁盘使用量数据
      let yData2 = []
      let yAllData2 = [] // 30日磁盘剩余量数据
      let unit = '' // 获取磁盘使用量的单位
      if (this.chartData.diskUsed && this.chartData.diskUsed.length > 0) {
        // 磁盘使用量
        xAllData = this.chartData.diskUsed.map(item => item.time) // 处理x轴数据
        yAllData = this.chartData.diskUsed.map(item => item.value) // 处理y轴数据
        let result = this.chartData.diskUsed.find(item=>item.value.unit)
        if (result && result.value && result.value.value) {
          unit = result.value.unit
        }
      }
      if (this.chartData.diskFree && this.chartData.diskFree.length > 0) {
        // 磁盘剩余量
        xAllData = this.chartData.diskFree.map(item => item.time) // 处理x轴数据
        yAllData2 = this.chartData.diskFree.map(item => item.value) // 处理y轴数据
      }

      if (this.selectedIndex == 0 && xAllData.length > 6) {
        // 截取近七日数据
        xData = xAllData.slice(xAllData.length - 7, xAllData.length)
        yData = yAllData.slice(xAllData.length - 7, xAllData.length)
        yData2 = yAllData2.slice(xAllData.length - 7, xAllData.length)
      } else {
        // 全部数据
        xData = xAllData
        yData = yAllData
        yData2 = yAllData2
      }

      this.myChart = this.$echarts.init(this.$refs.baseEcharts)
      this.myChart.clear()
      let option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'none'
          },
          formatter: function(params) {
            let html = `${params[0].name}<br/>`
            params.map((item, i) => {
              if (item.value !== '' && item.value !== null && item.value !== undefined) {
                html += `${item.marker}${item.seriesName} ${item.value} ${item.data.unit || ''} <br/>`
              } else {
                html += `${item.marker}${item.seriesName}：无数据<br/>`
              }
            })
            return html
          }
        },
        color: colors,
        legend: {
          top: '2%',
          left: '7%',
          itemWidth: 7,
          itemHeight: 7,
          textStyle: {
            fontSize: this.fontSizeObject.legendFontSize
          }
        },
        dataZoom: [
          {
            xAxisIndex: [0],
            show: false, //是否显示滑动条，不影响使用
            start: 0, // 从头开始。
            endValue: 30,
            realtime: true, //是否实时更新
          },
          {
            type: 'inside',
            xAxisIndex: 0,
            zoomOnMouseWheel: true, //滚轮是否触发缩放
            moveOnMouseMove: true, //鼠标滚轮触发滚动
            moveOnMouseWheel: true
          }
        ],
        grid: {
          top: '18%',
          left: '6%',
          right: '7%',
          bottom: 4,
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: true,
          axisLine: {
            show: true,
            lineStyle: {
              color: 'rgba(0,0,0,0.15)'
            }
          },
          axisLabel: {
            textStyle: {
              color: 'rgba(0,0,0,0.45)',
              fontSize: this.fontSizeObject.xAxisFontSize
            }
          },
          axisTick: {
            show: false
          },
          data: xData
        },
        yAxis: {
          type: 'value',
          axisTick: {
            show: false
          },
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed',
              color: 'rgba(0,0,0,0.15)',
              width: 1
            }
          },
          axisLine: {
            show: false
          },
          axisLabel: {
            formatter: '{value}' + unit,
            textStyle: {
              color: 'rgba(0,0,0,0.45)',
              fontSize: this.fontSizeObject.xAxisFontSize
            }
          }
        },
        series: [
          {
            name: '磁盘使用量',
            type: 'bar',
            stack: '总量',
            barWidth: this.selectedIndex == 1 ? 9: 15,
            label: {
              show: false,
              position: 'inside',
              color: 'rgba(0,0,0,0.45)'
            },
            data: yData
          },
          {
            name: '磁盘剩余量',
            type: 'bar',
            stack: '总量',
            barWidth: this.selectedIndex == 1 ? 9: 15,
            label: {
              show: false,
              position: 'inside',
              color: 'rgba(0,0,0,0.45)'
            },
            data: yData2
          }
        ]
      }

      this.myChart.setOption(option)
    }
  }
}
</script>
