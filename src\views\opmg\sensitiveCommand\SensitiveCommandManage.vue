<template>
  <div style="height:100%">
    <component :is="pageName" :data="data"/>
  </div>
</template>
<script>
import SensitiveCommandList from './SensitiveCommandList.vue'
export default {
  name: "SensitiveCommandManage",
  data() {
    return {
      isActive: 0,
      data:{}
    };
  },
  components: {
    SensitiveCommandList
  },
  created(){
    this.pButton1(0);
  },
  //使用计算属性
  computed: {
    pageName() {
      switch (this.isActive) {
        // case 0:
        //   return "SensitiveCommandList";
        //   break;

        default:
          return "SensitiveCommandList";
          break;
      }
    }
  },
  methods: {
    pButton1(index) {
      this.isActive = index;
    },
    pButton2(index,item) {
      this.isActive = index;
      this.data = item;
    }
  }
}
</script>