<template>
  <j-modal :title='title' :width='width' :visible='visible' :destroyOnClose='true'
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }" :centered='true' @ok='handleOk' @cancel='handleCancel'
    cancelText='关闭'>
    <a-spin :spinning='confirmLoading'>
      <j-form-container :disabled='disableSubmit'>
        <a-form-model ref='form' slot='detail' :labelCol='labelCol' :wrapperCol='wrapperCol'>
          <a-row>
            <a-col :span='24'>
              <a-form-model-item label='解决方案'>
                <a-textarea style='width: 100%' v-model='answererContent' :allow-clear='true' autocomplete='off'
                  placeholder='请输入解决方案' />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </j-form-container>
    </a-spin>
  </j-modal>
</template>

<script>
  import {
    httpAction
  } from '@api/manage'

  export default {
    name: 'okModal',
    data() {
      return {
        title: '',
        width: '800px',
        visible: false,
        disableSubmit: false,
        confirmLoading: false,
        answererContent: '',
        data: {},
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          }
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          }
        }
      }
    },

    methods: {
      add(data) {
        this.data = data
      },
      handleOk() {
        this.data.answererContent = this.answererContent
        console.log(this.data, 'data');
        this.$emit('ok', this.data)
        this.visible = false
      },
      handleCancel() {
        this.answererContent = ''
        this.visible = false
      },
    }
  }
</script>
<style scoped lang='less'>
  @import '~@assets/less/normalModal.less';
</style>