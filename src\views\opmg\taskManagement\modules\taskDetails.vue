<template>
  <div class="page-box">
    <a-spin :spinning="loading" wrapperClassName="custom-ant-spin">
      <div class='header-info'>
        <div class='title-wrapper' v-if="headTopData&&headTopData.length > 0">
          <div class='header-title'>
            <p class='p-info-title'>
              <span class='span-title' :title='headTopData[0].value'>{{headTopData[0].value}}</span>
              <span  v-for="(item,index) in headTopData" :key="'headTopData_'+index">
                <span v-if="index>0" :title='item.label' class='span-icon'>
                   <yq-icon v-if="item.field!=='taskStatus_dictText'&&item.icon" :type="item.icon" style='color:#4793FE;margin-right: 5px'></yq-icon>
                   <span v-html="item.value"></span>
                </span>
              </span>
            </p  >
          </div>
          <div class='header-back'>
            <img alt='' src='~@assets/return1.png' style='width: 20px; height: 20px; cursor: pointer' @click='getGo' />
          </div>
        </div>
        <a-row :gutter='24' class='info-btn' >
          <a-col :xxl='22' :xl='20' :lg='18' :md='18' :sm='24' :xs="24" v-if="headBottomData&&headBottomData.length>0">
            <p class='p-info-product'>
              <span class='span-assets'>计划起止时间： {{headBottomData[0].value }} ~ {{headBottomData[1].value }} </span>
              <span class='span-assets' v-if="headBottomData[2]&&headBottomData[2].value">实际起止时间：{{headBottomData[2].value }} ~
                <span v-if="headBottomData[3]&&headBottomData[3].value">{{headBottomData[3].value }}</span>
                <span v-else>暂无</span>
              </span>
            </p>
          </a-col>
          <a-col :xxl='2' :xl='4'  :lg='6' :md='6' :sm='24' :xs="24">
            <div class='btn'>
              <a-button class='action-btn' @click='queryTaskInfo(info)'><a-icon class='btn-icon' type='reload'/>刷新</a-button>
              <!--<a-button class="action" type="link" icon="reload" :loading="loading" @click="queryTaskInfo(info)" >刷新</a-button>-->
            </div>
          </a-col>
        </a-row>
      </div>



      <div class="task-info">
        <div class="content-box">
          <descriptions
            :label-width="maxLabelWidth+'px'"
            :colon="false"
            padding="0 0 0 11px"
            paddingBottom="12px !important"
            title-margin-top="0px"
            :title-border-left-color="borderLeftColor"
            :items="basicData"
            :bordered="false"
            :title="'基本信息'"
            :column="{ xxl: 3, xl: 2, lg: 2, md: 2, sm: 1, xs: 1 }"
            @triggerParentMethod="downloadFile"
          >
          </descriptions>

          <div class="title-flag-wrapper" :style="{marginTop: '8px',marginBottom: '0px'}">
            <span class="title-flag" :style="{borderLeftColor:borderLeftColor}">实时统计信息</span>
          </div>
          <a-row :gutter="[84,0]" style="width:calc(100% - 31px);display:flex;flex-flow: row wrap;justify-content: start;align-items: center;margin-left: -31px">
            <a-col class="chart-col">
              <real-time-statistic-chart
                chartMarginBottom="0px"
                chartPadding="16px 24px"
                chartWidth="calc(100% - 0px - 50%)"
                :title-border-left-color="borderLeftColor"
                :show-title="false"
                :chart-ring-data="distributionData"
                :chart-middle-data="distributionRate"
              >
              </real-time-statistic-chart>
            </a-col>
            <a-col class="chart-col">
              <real-time-statistic-chart
                chartMarginBottom="0px"
                chartPadding="16px 24px"
                :title-border-left-color="borderLeftColor"
                :show-title="false"
                :chart-ring-data="executionData"
                :chart-middle-data="executionRate"
              >
              </real-time-statistic-chart>
            </a-col>
          </a-row>

          <terminal-list title-margin-top="32px" :title-border-left-color="borderLeftColor" ref="terminalManage" :show-back="false" :task-info="info"></terminal-list>
        </div>
      </div>
    </a-spin>
  </div>
</template>

<script>
import { downloadFile, getAction } from '@/api/manage'
import { getExtendFields } from '@views/opmg/taskManagement/modules/extendFields'
import descriptions from '@comp/descriptions/descriptions.vue'
import terminalList from '@views/opmg/taskManagement/modules/terminalList.vue'
import { getDictData, setStatusColor } from '@views/opmg/taskManagement/modules/comFunctions'
import knowledgeIcon from '@views/opmg/knowledgeManagement/knowledgeBase/modules/KnowledgeIcon.vue'
import YqIcon from '@comp/tools/SvgIcon'
import realTimeStatisticChart from '@views/opmg/taskManagement/modules/statisticPieChart.vue'
import { getFileAccessHttpUrl } from '@/api/manage';
export default {
  name: 'taskDetails',
  props: {
    taskInfo: {
      type: Object,
      required: false,
      default: () => {
        return {}
      }
    }
  },
  components: {
    knowledgeIcon,
    descriptions,
    terminalList,
    YqIcon,
    realTimeStatisticChart
  },
  data() {
    return {
      borderLeftColor:'#409EFF',
      loading:false,
      extendFields:[],
      info: {},
      headTopFields: [
        {
          field: 'taskName',
          label: '任务名称'
        },
        {
          field: 'taskType_dictText',
          label: '任务类型',
          icon:'taskType'
        },
        {
          field: 'noticeType_dictText',
          label: '通知方式',
          icon:'noticeMethod'
        },
        /*   {
             field: 'plannedStartTime',
             label: '计划开始时间'
           },
           {
             field: 'plannedEndTime',
             label: '计划结束时间'
           },
           {
             field: 'actualStartTime',
             label: '实际开始时间'
           },
           {
             field: 'actualEndTime',
             label: '实际结束时间'
           },*/
      ],
      taskStatusFields: [
        {
          field: 'taskStatus_dictText',
          label: '执行状态',
          vField: 'taskStatus',
        },
      ],
      headTopData:[],

      headBottomFields: [
        {
          field: 'plannedStartTime',
          label: '计划开始时间'
        },
        {
          field: 'plannedEndTime',
          label: '计划结束时间'
        },
        {
          field: 'actualStartTime',
          label: '实际开始时间'
        },
        {
          field: 'actualEndTime',
          label: '实际结束时间'
        },
      ],
      headBottomData:[],

      basicFields: [
        /*{
          field: 'taskName',
          label: '任务名称'
        },
        {
          field: 'plannedStartTime',
          label: '计划开始时间'
        },
        {
          field: 'plannedEndTime',
          label: '计划结束时间'
        },
        {
          field: 'actualStartTime',
          label: '实际开始时间'
        },
        {
          field: 'actualEndTime',
          label: '实际结束时间'
        },*/
        /*{
          field: 'taskType_dictText',
          label: '任务类型'
        },*/
     /*   {
          field: 'softwareId_dictText',
          label: '升级包名称',
          extendInfo: [
            {
              field: 'softwarePatchVersion',
            }
          ]
        },*/
        {
          field: 'policyId_dictText',
          label: '下发策略'
        },
        /*{
          field: 'noticeType_dictText',
          label: '通知方式'
        },*/
        /* {
           field: 'noticeContext',
           label: '通知内容'
         },*/
        /* {
          field: 'taskStatus',
          label: '执行状态'
        },*/
      ],
      basicData: [],
      maxLabelWidth:0,
      statisticFields: [
        {
          field: 'issued',
          label: '已下发数量'
        },
        {
          field: 'notIssued',
          label: '未下发数量'
        },
        {
          field: 'issuedRate',
          label: '下发率',
          unit:'%',
          comType:'progress'
        },
        {
          field: 'success',
          label: '执行成功数'
        },
        {
          field: 'failed',
          label: '执行失败数'
        },
        {
          field: 'successRate',
          label: '执行成功率',
          unit:'%',
          comType:'progress'
        }
      ],
      distributionData: [],
      distributionRate: [],
      executionData: [],
      executionRate: [],

      url: {
        queryById: '/software/softwareTask/queryById',//基本信息、状态、扩展字段
        countTask: '/software/softwareTask/countTask'//实时统计数据
      }
    }
  },
  watch: {
    taskInfo: {
      handler(val) {
        this.setEmpty()
        this.queryTaskInfo(val)
      },
      deep: true,
      immediate: true
    },
  },
  created() {
  },
  methods: {
    downloadFile(item){
      let url=getFileAccessHttpUrl(item.url)
      downloadFile(url,item.value)
    },
    /**
     * 获取任务基本信息和配置信息
     * */
    queryTaskInfo(info) {
      this.loading=true
      getAction(this.url.queryById, { id: info.id }).then(res => {
        this.setEmpty()
        if (res.code == 200 && res.result && res.result.records && res.result.records.length > 0) {
          this.info = JSON.parse(JSON.stringify(res.result.records[0]))
          this.init()
        }
      }).catch((err)=>{
        this.$message.error(err.message)
        this.loading=false
        this.setEmpty()
      })
    },
    setEmpty(){
      this.basicFields = [{field: 'policyId_dictText',label: '下发策略'}]
      this.headTopData = []
      this.headBottomData = []
      this.basicData = []

      this.distributionData= []
      this.distributionRate=[]
      this.executionData=[]
      this.executionRate=[]
    },
    /**
     * 初始化基本信息、配置信息、执行状态、实时统计信息
     * */
    async init() {
      if (this.info.id) {
        let taskStatusData = await this.getStatusDesItem(this.taskStatusFields)
        this.headTopData = this.getDescriptionsItem(this.headTopFields)
        this.headTopData.splice(1,0,...taskStatusData)

        this.headBottomData = this.getDescriptionsItem(this.headBottomFields)
        if(this.info.taskType!=='file_up') {
          let obj = {
            field: 'softwareId_dictText',
            label: '升级包名称',
            extendInfo: [
              {
                field: 'softwarePatchVersion',
              }
            ]
          }
          this.basicFields.unshift(obj)
        }
        this.basicData = this.getDescriptionsItem(this.basicFields)
        let configInfoData =await this.getConfigInfo(this.info)
        this.basicData= this.basicData.concat(configInfoData)
        this.maxLabelWidth= Math.max(...this.basicFields.map(obj => obj.label.length))*14+36

        let statisticData = await this.getStatisticData(this.statisticFields)
        this.distributionData= statisticData.slice(0,2)
        this.distributionRate= statisticData.slice(2,3)
        this.executionData= statisticData.slice(3,5)
        this.executionRate= statisticData.slice(5)

       this.distributionData[0].value>=this.distributionData[1].value?this.distributionData[0].line='bottom':this.distributionData[1].line='top'
       this.executionData[0].value>=this.executionData[1].value?this.executionData[0].line='bottom':this.executionData[1].line='top'

        this.loading=false
      }
    },
    //返回上一级
    getGo() {
      this.$parent.pButton2(0)
    },
    /**
     * 获取实时统计数据
     * */
    async getStatisticData(fields) {
      let data = []
      await getAction(this.url.countTask, { id: this.taskInfo.id }).then((res) => {
        if (res.success && res.result) {
          for (let i = 0; i < fields.length; i++) {
            let unit = fields[i].unit ? fields[i].unit : ''
            let item = {
              field:fields[i].field,
              label: fields[i].label,
              name:fields[i].label,
              value: res.result[fields[i].field],
              unit:unit
            }
            data.push(item)
          }
        }
      })
      return data
    },
    /**
     * 获取配置信息数据
     * */
    async getConfigInfo(info) {
      let data = []
      let exFields=await getExtendFields(info.taskType)
      if (exFields&&info.extendList&&info.extendList.length > 0) {
        data=info.extendList.map((item)=>{
          let obj=null
          for (let i=0;i<exFields.length;i++){
            let exF=exFields[i]
            if(exF.extendCode===item.extendCode&&exF.display){
              obj={label:item.extendName,value:item.extendValue,field:item.extendCode}
              if (exF.displayDataFun) {
                obj=Object.assign(obj,exF.displayDataFun(obj.value))
              }
              break
            }
          }
          return obj
        }).filter((it)=>it)
      }
      return data
    },
    /**
     * 整理、基本信息数据，以排除空数据     *
     * @param {Array} fields - 数组：元素包含字段英文名和中文名
     * */
    getDescriptionsItem(fields) {
      let data = []
      let obj = this.info
      for (let i = 0; i < fields.length; i++) {
        let field=fields[i].field
        let va=obj[field]+''
        if (va != '' && va != 'null') {
          let v=obj[field]
          let item={ label: fields[i].label,field:fields[i].field}
          if (fields[i].icon) {item['icon'] = fields[i].icon}
          if (fields[i].extendInfo){
            let val=fields[i].extendInfo.map((item)=>obj[item.field])
            if (val&&val.length>0){
              v+='_'+val.join('_')
            }
            item['value']=v
          }else {
            item['value']=obj[fields[i].field]
          }
          data.push(item)
        }
      }
      return data
    },
    /**
     * 整理执行状态数据，设置标签颜色
     * @param {Array} fields - 数组：元素包含字段英文名和中文名
     * */
    async getStatusDesItem(fields) {
      let data = []
      await getDictData('softwareTaskStatus').then((res) => {
        if (res.length > 0) {
          let dictList = setStatusColor(res)
          let obj = this.info
          data = fields.filter(m => obj[m.field] !== '' && obj[m.field] !== null).map((item) => {
            const v = obj[item.vField];
            const matchedDict = dictList.find(item => item.key === v);
            const vt = obj[item.field];
             const text = matchedDict
               ? `<span style="background-color:${matchedDict.color};padding:2px 6px;border-radius:3px;color:#fff">${vt}</span>`
               : vt;
            return {
              label: item.label,
              value: text,
              field:item.field
            }
          })
        } else {
          data = this.getDescriptionsItem(fields)
        }
      })
      return data
    }
  }
}
</script>

<style scoped lang='less'>
.card{
  border-radius: 3px;
  background: #fff;
  padding: 24px;
}
.page-box {
  height: 100% !important;
  width:100%;
  display: flex;
  flex-flow: column nowrap;
  overflow: hidden;
  overflow-y: auto;

  .header-info {
    .card;
    /*display: flex;
    justify-content: space-between;
    align-items: start;*/
    margin-bottom: 16px;
    .title-wrapper{
      display: flex;
      justify-content: space-between;
      align-items: start;
      .header-title{
        width:calc(100% - 30px) !important;
        .p-info-title {
          margin-bottom:6px;
          color: #000000;

          .span-title{
            font-size:18px;
            margin-right:12px;
          }
          .span-icon{
            font-size:14px;
            margin-left:20px;
          }
        }
      }
      .header-back {
        text-align: right;
        display: inline-block;
        width: 0.375rem;//30/80
      }
    }
    .info-btn{
      display: flex !important;
      flex-direction: row;
      align-content: flex-end;
      justify-content: space-between;
      flex-wrap: wrap;
      align-items: flex-end;

      .p-info-product {
        margin-bottom: 0px;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
        display: flex;
        justify-content:start;
        align-items: start;
        flex-flow: row wrap;

        .span-assets {
          margin-right: 50px;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }
      }

      .btn{
        display: flex;
        flex-flow: nowrap row;
        justify-content: end;
        align-items: end;
        margin-top: 0px;

        .input-group{
          width:132px;
          text-align: right;
          white-space: nowrap
        }
        .action-btn:not(:first-child){
          margin-left: 10px
        }
        .action-btn:hover,.action-btn:active,.action-btn:focus{
          color: #409eff;
          background: #ecf5ff !important;
          border-color: #b3d8ff !important;
        }

        .btn-icon{
          color:#409eff !important;
        }
      }
    }
  }

  .title-flag-wrapper{
    .title-flag{
      padding-left: 7px;
      font-size: 14px;
      border-left-width: 4px;
      border-left-style: solid;
      color: rgba(0,0,0,0.85);
    }
  }

/*  .top-spin .ant-spin-nested-loading{
    height: 100%;
    .ant-spin-container{
      height: 100%;
      display: flex;
      flex-flow: column nowrap;
    }
  }*/

  .task-info {
    .card;
    flex:1;
    overflow: auto;
    //overflow-x:auto ;
    .content-box {
      //height:100%;
      padding-right:1px;
      min-width:562px ;


      .chart-col{
        width: 562px;
        height: 225px;
        margin-top: 12px
      }
    }
  }
}
@media (max-width: 767px) {
  .btn{
    justify-content: start !important;
    align-items: start !important;
    margin-top: 6px !important;
  }
}
</style>
