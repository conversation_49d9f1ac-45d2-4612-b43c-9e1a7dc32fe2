<template>
  <a-modal title="导入模型" :width="800" :visible="visible" :footer="null" @cancel="handleCancel" cancelText="关闭">
    <div style="height: 30vh;padding-bottom:50px">
      <a-upload-dragger name="file" :action="API_BASE_URL + uploadUrl" :showUploadList="false" :headers="headers"
        :beforeUpload="beforeUpload" @change="handleChange">
        <p class="ant-upload-drag-icon">
          <a-icon type="inbox" />
        </p>
        <p class="ant-upload-text">点击或者拖拽到此区域上传</p>
        <p class="ant-upload-hint">只能上传.bpmn20.xml,.bpmn文件，且不超过512K</p>
      </a-upload-dragger>
    </div>
  </a-modal>
</template>

<script>
  import {
    postAction
  } from '@/api/manage'
  import {
    ACCESS_TOKEN
  } from '@/store/mutation-types'
  export default {
    name: 'UploadModel',
    data() {
      return {
        visible: false,
        uploadUrl: '/flowable/model/import',
        headers: {},
      }
    },
    created() {
      //给headers 设置token
      const token = this.$ls.get(ACCESS_TOKEN)
      this.headers = {
        'X-Access-Token': token
      }
      //获取全局 api url
      // console.log("全局api url === ",this.API_BASE_URL)
    },
    beforeDestroy() {},
    methods: {
      handleCancel() {
        this.visible = false
      },
      beforeUpload(file) {
        //上传前判断是否符合条件
        // 上传前格式与大小校验
        const fileName = file.name
        const isFileTypeOk =
          fileName.endsWith('.bpmn20.xml') ||
          fileName.endsWith('.bpmn') ||
          fileName.endsWith('.bar') ||
          fileName.endsWith('.zip')
        const isLt512 = file.size / 1024 / 512 < 1
        if (!isFileTypeOk) {
          this.$message.error('上传文件格式不正确')
        } else if (!isLt512) {
          this.$message.error('上传文件大小不能超过512K')
        }
        return isFileTypeOk && isLt512
      },
      doImport(fileObj) {
        //手动上传
        let formData = new FormData()
        formData.set('file', fileObj.file)
        // formData.set("tenantId", this.importTenantId);
        postAction('/flowable/model/import', formData).then(({
          msg
        }) => {
          this.$message.success('上传成功')
        })
      },
      handleChange(info) {
        //自动上传
        const status = info.file.status
        if (status !== 'uploading') {
          console.log(info.file, info.fileList)
        }
        if (status === 'done') {
          this.$message.success(`${info.file.name} 上传成功`)
          this.$emit("uploadOk")
          this.handleCancel()
        } else if (status === 'error') {
          this.$message.error(`${info.file.name} 上传失败`)
        }
      },
    },
  }
</script>

<style scoped lang="less">
</style>