<template>
  <div class="scene-info">
    <a-form :form="form" v-bind="formItemLayout" v-if="ok">
      <a-form-item label="机房名称">
        {{ sceneName }}
      </a-form-item>

      <a-form-item label="背景类型">
        <div>
          <a-radio-group v-model="form.bgType">
            <a-radio value="transparent"> 无背景 </a-radio>
            <a-radio value="color"> 背景色 </a-radio>
            <a-radio value="pic"> 背景图 </a-radio>
          </a-radio-group>
        </div>
      </a-form-item>
      <a-form-item label="背景颜色" v-if="form.bgType === 'color'">
        <a-input v-model="form.bgColor" type="color" style="width: 100%" />
      </a-form-item>
      <a-form-item label="背景图片" v-if="form.bgType === 'pic'">
        <a-upload
          :accept="accept"
          :beforeUpload="beforeUploadFun"
          name="file"
          :data="{ biz: bizPath }"
          :show-upload-list="true"
          :action="uploadUrl"
          list-type="picture-card"
          :headers="headers"
          :remove="removeFile"
          :fileList="form.fileList"
          @preview="handlePreview"
          @change="normFile"
        >
          <div v-if="form.bgPic === ''">
            <a-icon :type="loading ? 'loading' : 'plus'" />
            <div class="ant-upload-text">上传</div>
          </div>
        </a-upload>
      </a-form-item>
    </a-form>
    <a-modal :visible="previewVisible" :footer="null" @cancel="hidePrview">
      <img alt="预览" style="width: 100%" :src="previewImage" />
    </a-modal>
  </div>
</template>
<script>
import { ACCESS_TOKEN } from '@/store/mutation-types'
import { getFileAccessHttpUrl } from '@/api/manage'
import { checkAccept, checkBeforeUpload, compareFileSizes } from '@comp/yq/yqUpload/YqUploadCommonFuns'
export default {
  data() {
    return {
      accept:'image/jpg,image/png, image/jpeg, image/jfif, image/pjp, image/pjpeg',
      acceptTips:'jpg、png、jpeg、jfif、pjp、pjpeg',
      sceneName: '',
      formItemLayout: {
        labelCol: { span: 6 },
        wrapperCol: { span: 18 },
      },
      loading: false,
      headers: {},
      bizPath: 'temp',
      uploadUrl: window._CONFIG['domianURL'] + '/sys/common/upload',
      previewImage: '',
      previewVisible: false,
      form: {
        bgType: 'color',
        bgColor: '#4682b4',
        bgPic: '',
        fileList: [],
      },
      ok: false,
    }
  },
  beforeCreate() {},
  created() {
    this.sceneName = this.$store.state.threejs.sceneName
    this.initForm()
    const token = this.$ls.get(ACCESS_TOKEN)
    this.headers = { 'X-Access-Token': token }
  },
  mounted() {},
  watch: {
    form: {
      deep: true,
      handler() {this.changeStateConfig()},
    },
  },
  methods: {
    /**
     * 选择文件格式是否正确
     */
    beforeUploadFun(file) {
      let result = checkAccept(file, this.acceptTips,false)
      if (!result) {
        return result
      }
      return checkBeforeUpload(file, true, 10, 'MB',false)
    },
    /*文件发生改变后，附加处理方法，一次性上传多个文件，剔除列表中不满足条件的文件*/
    handleChangeAdditionalFun(fileList){
      let list = fileList
      if (fileList.length > 0) {
        list = fileList.filter((item) => {
          return compareFileSizes(true, 10, item.size, 'MB')
        }).filter((item) => {
          return checkAccept(item, this.acceptTips)
        })
      }
      return list
    },
    initForm() {
      let config = this.$store.state.threejs.bgConfig
      if (config) {
        Object.assign(this.form, config)
      } else {
        this.changeStateConfig()
      }
      // let keys = Object.keys(config)
      // keys.forEach((k) => {
      //   this.form[k] = config[k]
      // })
      // console.log('this.form == ', this.form)
      if (this.form.bgPic) {
        this.form.fileList = [
          {
            uid: '-1',
            name: this.form.bgPic,
            status: 'done',
            url: getFileAccessHttpUrl(this.form.bgPic),
          },
        ]
      } else {
        this.form.fileList = []
      }
      this.ok = true
    },
    changeStateConfig() {
      let keys = Object.keys(this.form)
      let tem = {}
      keys.forEach((k) => {
        if (k !== 'fileList') {
          tem[k] = this.form[k]
        }
      })
      this.$store.commit('threejs/SET_BG_CONGIG', tem)
    },
    typeChange(e) {},
    normFile(e) {
      // this.form.fileList = e.fileList
      this.form.fileList =this.handleChangeAdditionalFun(e.fileList)
      if (e.file.status === 'uploading') {
        this.loading = true
      } else if (e.file.status === 'done') {
        let res = e.file.response
        if (res && res.success) {
          this.form.bgPic = res.message
        }
        this.loading = false
        this.$message.success('上传成功！')
      } else if (e.file.status === 'error') {
        this.loading = false
        this.$message.error('上传失败！')
      }
    },
    removeFile(file) {
      // console.log('删除文件', file)
      this.form.bgPic = ''
      this.form.fileList.splice(0, 1)
    },
    handlePreview(file) {
      console.log('预览文件', file)
      if (file.url) {
        this.previewImage = file.url
      } else if (file.response) {
        this.previewImage = getFileAccessHttpUrl(file.response.message)
      }
      this.previewVisible = true
    },
    hidePrview() {
      this.previewVisible = false
    },
    nameChange() {
      this.$store.commit('threejs/changeSceneName', this.sceneName)
    },
  },
}
</script>
<style scoped>
.scene-info {
  height: calc(100vh - 80px);
  overflow-y: auto;
  padding: 0 20px;
}
/* .input-item {
  display: flex;
  align-items: center;
} */
</style>