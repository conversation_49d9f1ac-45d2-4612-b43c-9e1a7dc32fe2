<template>
  <j-modal :confirmLoading="confirmLoading" :footer="null" :maskClosable="false" :title="title"
           :visible="showProcessNodeEdit" :width="modelWidth" @cancel="handleCancel" @ok="handleOk">
    <a-spin :spinning="confirmLoading">
      <!-- <a-alert message="每个节点设置，如有修改都请保存一次，跳转节点后数据不会自动保存！" style="margin-bottom:10px" type="info" show-icon />
<a-tabs
default-active-key="1"
:tab-position="mode"
:style="{ height: '400px'}"
@prevClick="callback"
@nextClick="callback"
>
<a-tab-pane v-for="(item, i) in nodeList" :key="i" :tab="`${item.title}`"> Content of tab {{ item.title }} </a-tab-pane>
</a-tabs> -->
      <a-row>
        <a-col :md="5" :sm="5" :xs="5" style="border-right: 1px solid #999">
          <a-steps v-model="current" direction="vertical" size="small">
            <a-step v-for="(item, i) in nodeList" :key="i" :title="item.title" @click="change_steps(item, i)" />
          </a-steps>
        </a-col>
        <a-col :md="19" :sm="19" :xs="19">
          <a-form v-if="showProcessNodeEdit">
            <a-alert banner message="温馨提示：若节点配置指派下一处理人按钮时，节点为开始节点（节点ID为__initiator__）时，在开始节点指派处理人。反则在下个节点指派处理人！" />
            <span></span>
            <a-form-item :label-col="labelCol" :wrapper-col="wrapperCol" label="节点名称">
              <span class="nodespan">{{ editNode.title }}</span>
            </a-form-item>
            <a-form-item :label-col="labelCol" :wrapper-col="wrapperCol" label="节点类型">
              <span class="nodespan">{{ dictNodeType[editNode.type] }}</span>
            </a-form-item>
            <a-alert banner message="每个任务设置，如有修改都请保存一次，跳转节点后数据不会自动保存！" type="info" />
            <br />
            <a-form-item v-show="editNode.type == 1" :label-col="labelCol" :wrapper-col="wrapperCol" label="审批人员">
              <a-checkbox-group v-model="spryTypes" @change="spryType">
                <!-- 0角色 1用户 2部门 3发起人 4发起人的部门负责人-->
                <a-checkbox value="0">根据角色选择</a-checkbox>
                <a-checkbox value="1">直接选择人员</a-checkbox>
                <a-checkbox value="2">部门</a-checkbox>
                <a-checkbox value="3">
                  发起人
                  <a-tooltip placement="topLeft" title="自动获取发起人">
                    <a-icon type="exclamation-circle" />
                  </a-tooltip>
                </a-checkbox>
                <!--                    <a-checkbox value="4">-->
                <!--                      发起人的部门负责人-->
                <!--                      <a-tooltip-->
                <!--                        placement="topLeft"-->
                <!--                        title="自动获取发起人所在部门的负责人，即其上级领导。（如果其本身就是部门负责人，则指向发起人自己。）"-->
                <!--                      >-->
                <!--                        <a-icon type="exclamation-circle"/>-->
                <!--                      </a-tooltip>-->
                <!--                    </a-checkbox>-->
                <a-checkbox value="5">发起人部门</a-checkbox>
                <a-checkbox value="6">选择节点办理人</a-checkbox>

                <a-checkbox value="7" v-if="spry.nodeIds1">选择节点办理人部门</a-checkbox>
                <a-checkbox value="8" >服务商(针对指定办理人中列表查询)</a-checkbox>

              </a-checkbox-group>
            </a-form-item>
            <!--            0角色 1用户 2部门 3发起人 4发起人的部门负责人-->
            <a-form-item v-if="spryTypes.indexOf('0') > -1" :label-col="labelCol" :wrapper-col="wrapperCol"
                         label="选择角色">
              <j-select-role v-model="spry.roleIds" />
            </a-form-item>
            <a-form-item v-if="spryTypes.indexOf('4') > -1" :label-col="labelCol" :wrapper-col="wrapperCol"
                         label="选择角色">
              <j-select-role v-model="spry.roleId" />
            </a-form-item>
            <a-form-item v-if="spryTypes.indexOf('1') > -1" :label-col="labelCol" :wrapper-col="wrapperCol"
                         label="选择人员">
              <!--  通过部门选择用户控件 -->
              <j-select-user-by-dep v-model="spry.userIds" :multi="true"></j-select-user-by-dep>
            </a-form-item>
            <a-form-item v-if="spryTypes.indexOf('2') > -1" :label-col="labelCol" :wrapper-col="wrapperCol"
                         label="选择部门">
              <j-select-depart v-model="spry.departmentIds" :multi="true"></j-select-depart>
            </a-form-item>
            <a-form-item v-if="spryTypes.indexOf('6') > -1" :label-col="labelCol" :wrapper-col="wrapperCol"
                         label="选择节点">
              <a-select placeholder="请选择节点" v-model="spry.nodeIds1" mode="tags"
                        @change="handleChange"
                        :getPopupContainer="(node) => node.parentNode" >
                <a-select-option v-for="item in nodeList"  :key="item.id"
                                 :value="item.id">{{ item.title }}
                </a-select-option>
              </a-select>
            </a-form-item>
            <!--btn-->
            <a-form-item style='text-align: center'>
              <a-button style="margin-right: 12px" @click="closeNode">关闭</a-button>
              <a-button :disabled="editNode.type == 0 || editNode.type == 2 || confirmLoading" html-type="submit"
                        type="primary" @click="sprySubmit">提交并保存
              </a-button>
            </a-form-item>

          </a-form>
        </a-col>
      </a-row>
    </a-spin>
  </j-modal>
</template>

<script>
import {
  getAction,
  postAction
} from '@api/manage'
import JSelectUserByDepModalEnhance from '@/components/yq/modules/JSelectUserByDepModalEnhance'
import JSelectUserByDepEnhance from '@/components/yq/JSelectUserByDepEnhance'
import JSelectUserByDep from '@/components/jeecgbiz/JSelectUserByDep'
import JSelectRole from '@/components/jeecgbiz/JSelectRole'
import {
  ajaxGetDictItems,
  getDictItemsFromCache
} from '@api/api'
var baseUrl = '/flowable/processDefinition'
export default {
  name: 'NodeSettings',
  components: {
    JSelectUserByDepModalEnhance,
    JSelectUserByDepEnhance,
    JSelectUserByDep,
    JSelectRole
  },
  data() {
    return {

      actZNotification: '',
      ccToVos: [],
      userIds: '',

      //任务设置
      editNode: {},
      showProcessNodeEdit: false,
      title: '任务设置',
      modelWidth: '1000px',
      confirmLoading: false,
      mode: 'left',
      nodeList: [],
      current: 0,
      spryTypes: [],
      spry: {
        //选中的用户
        modelKey: '',
        userIds: '',
        roleIds: '',
        roleId: '',
        departmentIds: '',
        chooseSponsor: false,
        chooseDepHeader: false,
        chooseDep: false,
        chooseDepNode: false,
        chooseProvider: false,
        proDefVersion: null
      },
      // 表头
      labelCol: {
        xs: {
          span: 4
        },
        sm: {
          span: 4
        },
      },
      wrapperCol: {
        xs: {
          span: 20
        },
        sm: {
          span: 20
        },
      },
      dictNodeType: {
        0: '开始节点',
        1: '审批节点',
        2: '结束节点',
      },
      url: {
        getProcessNode: `${baseUrl}/getProcessNode`,
        editNodeUser: `${baseUrl}/editNodeUser`,
        // deleteBatch: "/test/jeecgDemo/deleteBatch",
        // exportXlsUrl: "/test/jeecgDemo/exportXls"
      },
      selectRow: {},
    }
  },
  created() {

  },
  methods: {
    handleChange(){
      this.$forceUpdate();
    },
    selectedUsersReset() {
      this.userIds = ''
      this.ccToVos = []
    },
    notificationSubmit() {
      if (!this.userIds) {
        this.$message.error('请选择抄送人再保存！')
        return
      }
      let requestBody = {
        nodeId: this.editNode.id,
        proDefVersion: this.selectRow.modelVersion,
        users: this.userIds,
        usernames: this.usernames,
        id: this.actZNotification,
        modelKey: this.selectRow.key,
      }
      postAction('/notification/actZNotification/add', requestBody).then(({
                                                                            success,
                                                                            message,
                                                                            result
                                                                          }) => {
        if (success) {
          this.$message.success(message)
          this.actZNotification = result
        } else {
          this.$message.error(message)
        }
      })

    },

    // 刷新抄送配置
    refreshNotification() {
      let requestBody = {
        nodeId: this.editNode.id,
        proDefVersion: this.selectRow.modelVersion,
        modelKey: this.selectRow.key
      }
      getAction('/notification/actZNotification/queryByNodeIdAndVersion', requestBody).then(({
                                                                                               success,
                                                                                               result
                                                                                             }) => {
        if (success) {
          if (result) {

            this.userIds = result.users
            this.usernames = result.usernames
            this.actZNotification = result.id
          } else {
            this.userIds = ''
            this.usernames = ''
            this.actZNotification = ''
          }
        }
      })
    },
    tableChange(key) {
      if (key === '2') {
        // this.$refs.selectModal.showModal()
        this.refreshNotification()
      }
    },
    selectedUserCallBack(users) {
      //debugger
      if (users.length > 0) {
        this.userIds = users.map(user => user.username).reduce((v1, v2) => {
          return v1 + ',' + v2
        })
        this.usernames = users.map(user => user.realname).reduce((v1, v2) => {
          return v1 + ',' + v2
        })
      }
    },

    initNodeData(row) {
      this.selectRow = row
      postAction(this.url.getProcessNode, {
        id: row.id,
        proDefVersion: row.modelVersion,
        modelKey: row.key
      }).then((res) => {
        if (res.success) {
          this.nodeList = res.result || []
          if (this.nodeList.length > 0) {
            this.editNode = res.result[0]
            this.showProcessNodeEdit = true
            this.spry.modelKey = row.key
            this.change_steps(this.editNode, this.current)
          }
        } else {
          this.$message.error(res.message)
        }
      })
    },
    /*编辑流程节点*/
    change_steps(node, index) {
      this.spryTypes = []
      this.current = index
      this.editNode = node
      /* 0角色 1用户 2部门 3发起人 4发起人的部门负责人 5发起人部门 6安可中心值班人 7运维团队值班人*/
      this.spry.chooseOperation = node.chooseOperation || false
      this.spry.chooseAnKe = node.chooseAnKe || false

      if (node.nodeIds!=null){
        this.spryTypes.push('6')
        this.spry.nodeIds1=node.nodeIds.split(",")
      }else{
        //this.spry.nodeIds1=undefined
        this.spry.nodeIds1=[]
      }
      this.spry.chooseDepNode = node.chooseDepNode || false
      if (this.spry.chooseDepNode) this.spryTypes.push('7')
      this.spry.chooseDep = node.chooseDep || false
      if (this.spry.chooseDep) this.spryTypes.push('5')
      this.spry.chooseDepHeader = node.chooseDepHeader || false
      if (this.spry.chooseDepHeader) this.spryTypes.push('4')
      if (node.role != null) {
        this.spry.roleId = node.role.roleName
      }
      this.spry.chooseSponsor = node.chooseSponsor || false
      if (this.spry.chooseSponsor) this.spryTypes.push('3')
      var userIds = []
      for (const user of node.users || []) {
        userIds.push(user.username)
      }
      this.spry.userIds = userIds.join(',')
      if (userIds.length > 0) this.spryTypes.push('1')
      var roleIds = []
      for (const role of node.roles || []) {
        roleIds.push(role.roleCode)
      }
      this.spry.roleIds = roleIds.join(',')
      if (roleIds.length > 0) this.spryTypes.push('0')
      var departmentIds = []
      for (const department of node.departments || []) {
        departmentIds.push(department.id)
      }
      this.spry.departmentIds = departmentIds.join(',')
      if (departmentIds.length > 0) this.spryTypes.push('2')


      this.spry.chooseProvider = node.chooseProvider || false
      if (this.spry.chooseProvider) this.spryTypes.push('8')
      this.refreshNotification()
    },
    sprySubmit() {
      var _this = this
      // if (this.spryTypes.length == 0) {
      //   _this.$message.error('必须选择审批人！')
      //   return
      // }
      _this.confirmLoading = true
      this.spry.nodeId = this.editNode.id
      this.spry.proDefVersion = this.selectRow.modelVersion
      this.spry.nodeIds = this.spry.nodeIds1&&this.spry.nodeIds1.length>0?this.spry.nodeIds1.join(','):''
      postAction(_this.url.editNodeUser, this.spry)
        .then((res) => {
          if (res.success) {
            _this.$message.success('操作成功')
            //缺少刷新
            this.getNodeData(this.selectRow)
            // _this.getData();
          } else {
            _this.$message.error(res.message)
          }
        })
        .finally(() => (_this.confirmLoading = false))
    },
    getNodeData(row) {
      this.selectRow = row
      this.confirmLoading = false
      postAction(this.url.getProcessNode, {
        id: row.id,
        proDefVersion: row.modelVersion,
        modelKey: row.key
      }).then((res) => {
        console.log(res.result)
        if (res.success) {
          this.nodeList = res.result || []
          if (this.nodeList.length > 0) {
            console.log(this.nodeList)
            this.showProcessNodeEdit = true
          }
        } else {
          this.$message.error(res.message)
        }
      })
    },
    spryType(types) {
      /* 0角色 1用户 2部门 3发起人 4发起人的部门负责人 */
      // this.spryTypes = types;
      if (this.spryTypes.indexOf('0') == -1) this.spry.roleIds = ''
      if (this.spryTypes.indexOf('1') == -1) this.spry.userIds = ''
      if (this.spryTypes.indexOf('2') == -1) this.spry.departmentIds = ''
      if (this.spryTypes.indexOf('6') == -1) this.spry.nodeIds1 = []
      //是否选中发起人
      this.spry.chooseSponsor = this.spryTypes.indexOf('3') > -1
      //是否选中发起人的部门领导
      this.spry.chooseDepHeader = this.spryTypes.indexOf('4') > -1
      //是否选中发起人的部门
      this.spry.chooseDep = this.spryTypes.indexOf('5') > -1
      this.spry.chooseDepNode = this.spryTypes.indexOf('7') > -1
      this.spry.chooseProvider = this.spryTypes.indexOf('8') > -1
    },

    closeNode() {
      (this.showProcessNodeEdit = false), (this.current = 0), (this.spryTypes = []), (this.spry = {})
    },
    handleOk() {},
    handleCancel() {
      this.closeNode()
    },
    close() {
      //this.$emit('close')
      this.showProcessNodeEdit = false
    },
  }

}
</script>

<style lang="less" scoped>
@import '~@assets/less/YQNormalModal.less';
</style>