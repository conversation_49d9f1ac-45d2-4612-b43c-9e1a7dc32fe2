<template>
    <a-row style="height: 100%;">
      <a-col ref="print" id="printContent" class="abcdefg" :span="12">
        <div>
          <p style="padding: 16px 24px;background-color: #fafafa;border-bottom:1px solid #e8e8e8;font-size: 16px;">JDBC连接测试</p>
        </div>
        <!--签字-->
        <a-col style='padding: 0 24px 24px'>
          <a-form :form="form">
            <a-row>
              <a-col :span="24" class="aCol">
                <a-form-item label="IP地址" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input placeholder="请输入IP地址"  v-decorator="['ip', validatorRules.ip]" class="aInp" ></a-input>
                </a-form-item>
              </a-col>
              <a-col :span="24"  class="aCol">
                <a-form-item label="端口" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input placeholder="请输入端口"  class="aInp" v-decorator="['port', validatorRules.port]"></a-input>
                </a-form-item>
              </a-col>
              <a-col :span="24" class="aCol">
                <a-form-item label="数据库类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <j-dict-select-tag placeholder="请选择数据库类型" v-decorator="['type', validatorRules.type]" :trigger-change="true" dictCode="db_type" class="aInp" />
                </a-form-item>
              </a-col>

              <a-col :span="24"  class="aCol">
                <a-form-item label="数据库实例" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input placeholder="请输入数据库实例"  class="aInp" v-decorator="['db', validatorRules.db]"></a-input>
                </a-form-item>
              </a-col>
              <a-col :span="24"  class="aCol">
                <a-form-item label="用户名" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input  class="aInp" v-decorator="['dbUName']"></a-input>
                </a-form-item>
              </a-col>
              <a-col :span="24"  class="aCol">
                <a-form-item label="密码" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input-password class="aInp" v-decorator="['dbPwd',{rules: [{ required: false,  validator:this.dbPwd ,trigger: 'blur'}]}]"></a-input-password>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row class="btnStyle">
              <a-col :span="24" :style="{ textAlign: 'center' }">
                <a-button type="shallow" @click="handelSubmit" :disabled = "buttonDisadled">
                  开始
                </a-button>
                <a-button :style="{ marginLeft: '8px' }" @click="handleReset">
                  重置
                </a-button>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
      </a-col>
      <a-col :span="12" class="contTwo">
        <div class="returnDiv">
          <p class="returnTitle">JDBC连接测试</p>
          <p v-html="result" style="height: auto;">{{result}}</p>
        </div>
      </a-col>
    </a-row>
  <!--</page-layout>-->
</template>
<script>
  import ACol from 'ant-design-vue/es/grid/Col'
  import ARow from 'ant-design-vue/es/grid/Row'
  import ATextarea from 'ant-design-vue/es/input/TextArea'
  import JDictSelectTag from "@/components/dict/JDictSelectTag"
  import {
    getAction
  } from '@/api/manage'

  export default {
    components: {
      ATextarea,
      ARow,
      ACol,
      JDictSelectTag
    },
    name: 'Printgzsld',
    props: {
      reBizCode: {
        type: String,
        default: ''
      },
      paramIp: {
        type: String,
        default: ''
      }
    },
    watch: {
      paramIp: {
        handler(nv) {
          this.$nextTick(()=>{
            this.form.setFieldsValue({
              ip: nv,
            })
          })
        },
        immediate: true
      }
    },
    data() {
      return {
        form: this.$form.createForm(this),
        labelCol: {
          xs: { span: 24 },
          sm:{span:8},
          md:{span:7},
          lg:{ span: 6 },
          xl: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 23 },
          sm: { span: 16 },
          md: { span: 17 },
          lg: { span: 18 },
          xl: { span: 18 },
        },
        url: '/connect/testJdbc',
        result: '',
        buttonDisadled:false,
        validatorRules: {
          ip: {
            rules: [
              { required: true, message: 'IP不能为空！' },
              {
                pattern: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
                message: '请输入正确的IP！'
              }
            ],
          },
          port: {
            rules: [
              { required: true, message: '端口不能为空！' },
            ],
          },
          type: {
            rules: [
              { required: true, message: '数据库类型不能为空！' },
            ],
          },
          db: {
            rules: [
              { required: true, message: '数据库实例不能为空！' },
            ],
          },
        },
      }
    },
    created() {},
    methods: {
      dbPwd(rule, value, callback) {
        let reg =/.*[\u4e00-\u9fa5]+.*$/;
        if(value){
          if(!reg.test(value)){
            callback();
          }
          else {
            callback('数据库密码不能为汉字!');
          }
        }
        else {
          callback();
        }
      },
      //提交方法
      handelSubmit() {
        this.form.validateFields((err, values) => {
          if (!err) {
            this.result = '';
            this.buttonDisadled=true
            //参数
            let param = {
              ip: values.ip, // ip
              type: values.type, //类型
              port: values.port, //端口
              db: values.db, //数据实例
              dbUName: values.dbUName, //用户名
              dbPwd: values.dbPwd //密码
            }
            let pingUrl = window._CONFIG['domianURL'] + this.url;
            this.$http.get(pingUrl, {
              params: Object.assign(param)
            }).then(response => {
              if (response.success) {
                this.result = response.result
                this.buttonDisadled=false
              }
            }, response => {});
          }
        })
      },
      //刷新
      handleReset() {
        this.form.resetFields()
      },
    }
  }
</script>
<style scoped>
  /*update_begin author:scott date:20191203 for:打印机打印的字体模糊问题 */
  * {
    color: #000000;
    -webkit-tap-highlight-color: #000000;
  }

  /*update_end author:scott date:20191203 for:打印机打印的字体模糊问题 */
  .importDiv {
    width: 60%;
    height: 14em;
    margin: 0 auto;
    border: 1px solid #d9d9d9;
    padding: 18px;
  }

  .returnDiv {
    height: 100%;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
  }

  .returnTitle {
    padding: 16px 24px;
    background-color: rgb(250, 250, 250);
    border-bottom: 1px solid rgb(232, 232, 232);
    font-size: 16px;
  }

  /* .returnDiv{
  width: 53%;
  height: 16em;
  margin: 0 auto;
  border: 1px solid #d9d9d9;
  padding: 18px;
  margin-top: 20px;
} */
  .leftSpan {
    width: 14%;
  }

  .abcdefg .ant-card-body {
    margin-left: 0%;
    margin-right: 0%;
    margin-bottom: 1%;
    border: 0px solid black;
    min-width: 800px;
    color: #000000 !important;
  }

  .explain {
    text-align: left;
    margin-left: 50px;
    color: #000000 !important;
  }

  .explain .ant-input,
  .sign .ant-input {
    font-weight: bolder;
  }

  .aCol {
    /* height: 45px; */
    margin-bottom: 5px;
  }

  .aCol:first-child {
    margin-top: 10px;
  }

  .btnStyle {
    margin-top: 24px;
  }

  .explain div {
    margin-bottom: 10px;
  }

  /* you can make up upload button and sample style by using stylesheets */
  .ant-upload-select-picture-card i {
    font-size: 32px;
    color: #999;
  }

  .ant-upload-select-picture-card .ant-upload-text {
    margin-top: 8px;
    color: #666;
  }

  .aInp {
  /*width: 92%;*/
  }

  #printContent {
    height: 100%;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
  }

  .contTwo {
    margin-left: 16px;
    width: calc(100% - 50% - 16px);
    height: 100%;
  }
</style>