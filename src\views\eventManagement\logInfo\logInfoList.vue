<template>
  <div class='log-page'>
    <!--上：查询-->
    <div class='table-page-search-wrapper header-wrapper' >
      <a-form layout='inline' @keyup.enter.native='searchQuery'>
        <a-row :gutter='[12,0]'>
          <a-col :sm='24' :md='12' :lg='4'>
            <a-form-item label='日志视图' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <!--日志来源组件-->
              <log-source
                v-model='queryParam.logAnalyzeId'
                ref='logSource'
                :placeholder='"请选择日志视图"'
                :data-url='"/logAnalyze/list"'
                :delete-url='"/logAnalyze/delete"'
                @editChange="loadQueryTags"
                @change='changeSource'>
              </log-source>
            </a-form-item>
          </a-col>
          <a-col :sm='24' :md='12' :lg='11' >
            <a-form-item label='筛选数据' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <query-group
                :log-analyze-id='queryParam.logAnalyzeId'
                :field-list='fieldList'
                :enableStatus='enableStatus'
                @changeActiveGroup='changeActiveGroup'
                @enableHandler='enableHandler'
                @invertAllQuery='invertAllQuery'
                @deleteAllQuery='deleteAllQuery'
                @completeAddingFilter='loadQueryTags'
                @customQuery='customQuery'>
              </query-group>
            </a-form-item>
          </a-col>
          <a-col :sm='24' :md='12' :lg='7'>
            <a-form-item label='时间' :labelCol='labelCol1' :wrapperCol='wrapperCol1'>
              <custom-range-time
                :timestamp='timestampField'
                @changeRangeTime='changeRangeTime'
              ></custom-range-time>
            </a-form-item>
          </a-col>
          <a-col :sm='24' :md='4' :lg='2' >
            <a-form-item label=''>
              <a-button type='primary' @click='refreshTalbe'>更新</a-button>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>

      <!--查询标签-->
      <div :class='{"query-tags-wrapper":enableStatus!==enableStatusEnum.noBoth}'>
        <query-tags
          ref='queryTags'
          :logAnalyzeId='queryParam.logAnalyzeId'
          :fieldList='fieldList'
          @delete='loadQueryTags'
          @setEnableStatus='setEnableStatus'>
        </query-tags>
      </div>
      <!--查询标签--end-->
    </div>
    <!--上：查询---end-->

    <!--下：-->
    <a-row class='field-table-wrapper' type="flex" justify="space-between">
      <!--左侧字段-->
      <a-col class='bottom-left-box'>
        <log-fields
          class='log-fields'
          :log-analyze-id='queryParam.logAnalyzeId'
          :field-list='fieldList'
          :timestamp='timestampField'
          :loading='loadFields'
          @deleteField='deleteTableDynamicField'
          @addField='addTableDynamicField'>
        </log-fields>
      </a-col>
      <!--左侧字段--end-->

      <!--右侧：列表-->
      <a-col class='bottom-right-box'>
        <div class='table-wrapper'>
          <logSeverityCountCard :countInfo="countInfo"></logSeverityCountCard>
          <a-table
            v-if='columns.length>0'
            ref='table'
            bordered
            :row-key='(record,index)=>{return index}'
            :columns='columns'
            :dataSource='dataSource'
            :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
            :pagination='ipagination'
            :loading='loading'
            @change='handleTableChange'>
          <span slot='action' class='caozuo' slot-scope='text, record'>
            <a @click='handleDetailPage(record)'>查看</a>
          </span>
            <template slot='tooltip' slot-scope='text'>
              <a-tooltip placement='topLeft' :title='text' trigger='hover' overlayClassName='log-document-class-name'>
                <div class='tooltip'>
                  {{ text }}
                </div>
              </a-tooltip>
            </template>
          </a-table>
          <div v-else class='table-empty-box'>
            <a-card class='empty-card-box'>
              <div class='top-tip'>没有有意义的搜索条件</div>
              <div class='bottom-tip'>
                <div class='bottom-tip-content'>
                  <div class='tip' >提供日志视图</div>
                  <div class='tip'>让搜索有据可依</div>
                </div>
                <yq-icon type='noSearchResult' style='font-size: 130px'></yq-icon>
              </div>
            </a-card>
          </div>
        </div>
      </a-col>
      <!--右侧：列表--end-->
    </a-row>
    <!--下：--end-->
  </div>
</template>

<script>
import moment from 'moment'
import '@assets/less/TableExpand.less'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import yqIcon from '@comp/tools/SvgIcon'
import logSource from '@views/eventManagement/logInfo/modules/logSource.vue'
import logFields from '@views/eventManagement/logInfo/modules/logFields.vue'
import queryGroup from '@views/eventManagement/logInfo/modules/queryGroup.vue'
import queryTags from '@views/eventManagement/logInfo/modules/queryTags.vue'
import {enableStatusEnum} from '@views/eventManagement/logInfo/modules/status.js'
import customRangeTime from '@views/eventManagement/logInfo/modules/customRangeTime.vue'
import { getAction } from '@api/manage'
import logSeverityCountCard from './modules/logSeverityCountCard.vue'
export default {
  name: 'AdapterTaskList',
  mixins: [JeecgListMixin],
  components: {
    yqIcon,
    logSource,
    logFields,
    queryGroup,
    queryTags,
    customRangeTime,
    logSeverityCountCard
  },
  data() {
    return {
      description: '日志信息页面',
      labelCol: {
        style: 'width:70px'
      },
      wrapperCol: {
        // style: 'width:calc(100% - 70px)'
      },
      labelCol1: {
        style: 'width:45px'
      },
      wrapperCol1: {
        // style: 'width:calc(100% - 70px)'
      },
      /**当前激活的查询组信息*/
      activeQueryGroup: null,
      enableStatusEnum: enableStatusEnum,
      /**当前禁用启用状态*/
      enableStatus: enableStatusEnum.noBoth,
      //左侧：字段数据
      loadFields: false,
      fieldList: [],
      /**手动输入的查询语句*/
      curstomQuery: '',
      rangeTimeQuery: '',

      //列表
      documentData: {},
      defaultDynamicField: 'document',
      timestampField: '',
      actionColumn: {
        title: '操作',
        dataIndex: 'action',
        fixed: 'right',
        width: 80,
        scopedSlots: { customRender: 'action' },
      },
      columns: [],
      url: {
        list: '/logAnalyze/queryLog',
        feilds: '/logAnalyze/getFields',//加载字段数据接口
        getLogSeverityCount: '/logAnalyze/logSeverityCount' // 日志分析-査询日志信息
      },
      disableMixinCreated: true,
      moment,
      countInfo: {}
    }
  },
  methods: {
    onSearch(value) {
      //console.log(value);
    },
    /*选择切换日志来源,重新加载字段数据、列表数据*/
    changeSource(logAnalyze) {
      this.getFiledList(logAnalyze)
      this.setTableColumn(logAnalyze)
    },
    /*根据日志来源id，获取所有字段*/
    getFiledList(logAnalyze) {
      this.fieldList = []
      if (logAnalyze&&logAnalyze.id) {
        this.loadFields = true
        getAction(this.url.feilds, { logAnalyzeId: logAnalyze.id }).then((res) => {
          if (res.success) {
            this.fieldList = res.result
          } else {
            this.$message.warning(res.message)
          }
          this.loadFields = false
        }).catch((err) => {
          this.$message.warning(err.message)
          this.loadFields = false
        })
      }
    },
    setTableColumn(logAnalyze){
      this.columns=[]
      this.queryParam.logAnalyzeId=''
      this.timestampField=''
      if (logAnalyze){
        this.queryParam.logAnalyzeId=logAnalyze.id
        //时间戳字段不为空，添加为第一列
        //console.log('logAnalyze.timestampFiled===',logAnalyze.timestampFiled)
        if (logAnalyze.timestampFiled){
          this.timestampField=logAnalyze.timestampFiled
          let firstColumn={
            title: logAnalyze.timestampFiled,
            dataIndex: logAnalyze.timestampFiled,
          }
          this.columns.push(firstColumn)
        }
        this.columns.push(this.actionColumn)
        this.addTableDynamicField(this.defaultDynamicField)
        this.loadData(1)
        this.getLogSeverityCount()
      }
    },
    getLogSeverityCount() {
      let params = this.queryParam
       getAction(this.url.getLogSeverityCount, params).then((res) => {
          if (res.success) {
            this.countInfo= res.result
          } else {
            this.$message.warning(res.message)
          }
        }).catch((err) => {
          this.$message.warning(err.message)
        })
    },
    addTableDynamicField(field){
      if (field!='document'){
        if (this.columns.length===3&&this.columns[1].dataIndex==="document"){
          this.columns.splice(1,1)
        }
      }
      let tHead={
         title: field === 'document' ? '文档' : this.getChineseLabel(field),
        dataIndex:field,
        scopedSlots: { customRender: 'tooltip' },
        customCell: () => {
          let cellStyle = 'text-align: left;max-width:800px'
          return { style: cellStyle }
        }
      }
      this.columns.splice(1,0,tHead)
    },
    // 获取中文标签
    getChineseLabel(key) {
      if (this.dataSource.length > 0) {
        const firstRecord = this.dataSource[0]
        // 检查是否有对应的mapping字段
        const mappingKey = `${key}_mapping`
        if (firstRecord && firstRecord[mappingKey]) {
          return firstRecord[mappingKey]
        }
      }
      // 如果没有mapping，返回原始key
      return key
    },
    deleteTableDynamicField(field){
      this.columns=this.columns.filter((item)=>{
        return item.dataIndex!==field
      })
      if (this.columns.length===2){
        this.addTableDynamicField(this.defaultDynamicField)
      }
    },
    handleDetailPage: function (record) {
      let showDocument=this.columns.length>=3&&this.columns[1].dataIndex!=="document"?false:true
      //console.log('showDocument===',showDocument)
      this.$parent.pButton2(1, record,showDocument)
    },
    /**************分组操作****************/
    /*切换选中查询分组后，重新获取分组信息*/
    changeActiveGroup(queryGroupInfo){
      this.activeQueryGroup=queryGroupInfo
      this.$refs.queryTags.initData(queryGroupInfo)
    },
    /**根据选定分组，加载查询条件数据*/
    loadQueryTags(){
      this.$refs.queryTags.initData(this.activeQueryGroup)
    },
    /**设置全部禁用、启用、删除、取反按钮操作性*/
    setEnableStatus(status){
      this.enableStatus=status
      this.loadData(1)
    },
    /**全部禁用、启用*/
    enableHandler(operation){
      this.$refs.queryTags.enableHandler(operation)
    },
    /**全部反向包括*/
    invertAllQuery(){
      this.$refs.queryTags.invertAllQuery()
    },
    /**全部删除*/
    deleteAllQuery(){
      this.$refs.queryTags.deleteAllQuery()
    },
    /**手动输入查询条件*/
    customQuery(query){
      this.curstomQuery=query
      this.loadDataWithCustomQuery()
    },

    /**选择时间*/
    changeRangeTime(timeQuery) {
      this.rangeTimeQuery=timeQuery
      this.loadDataWithCustomQuery()
    },
    loadDataWithCustomQuery(){
      this.queryParam.tempTerms=''
      if (this.curstomQuery&&this.rangeTimeQuery){
        this.queryParam.tempTerms=this.curstomQuery+" AND "+this.rangeTimeQuery
      }else if (this.curstomQuery){
        this.queryParam.tempTerms=this.curstomQuery
      }else if(this.rangeTimeQuery){
        this.queryParam.tempTerms=this.rangeTimeQuery
      }
      this.loadData(1)
      this.getLogSeverityCount();
    },

    refreshTalbe(){
      this.loadData(1)
      this.getLogSeverityCount();
    }
  }
}
</script>
<style lang='less'>
.log-document-class-name {
  max-width: 800px;

  .ant-tooltip-content{
    .ant-tooltip-inner{
      max-height: 300px;
      overflow: hidden;
      overflow-y: auto;
    }
  }
}
</style>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
.table-page-search-wrapper .ant-form-inline .ant-form-item{
  margin-bottom: 12px !important;
}
.log-page {
  height: 100%;
  background-color: #fff;
  min-height: 600px;
  display: flex;
  flex-flow: column nowrap;

  .header-wrapper {
    //display: flex;
    //flex-flow: row wrap;
    //justify-content: start;
    //align-items: center;
    padding: 24px 24px 0px;
    border-bottom: 1px solid #e8e8e8;

    .query-tags-wrapper {
      margin-bottom: 10px;
    }
  }

  .field-table-wrapper {
    flex: 1;
    overflow: hidden;

    .bottom-left-box {
      width: 300px;
      height: 100%;

      .log-fields{
        padding: 12px 0 24px 8px;
        height: 100%
      }
    }

    .bottom-right-box {
      width: calc(100% - 300px);
      height: 100%;

      .table-wrapper {
        border-left: 1px solid #e8e8e8;
        margin: 12px 0px 24px 0px;
        padding: 0px 24px 0px 16px;
        width: calc(100% - 0px);
        height: calc(100% - 12px - 24px);
        overflow: hidden;
        overflow-y: auto;


        .table-empty-box {
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;

          .empty-card-box {
            width: 450px;

            .top-tip {
              font-size: 24px;
              font-weight: bold;
              margin-bottom: 16px;
              color: #000
            }

            .bottom-tip {
              display: flex;
              flex-flow: row nowrap;
              justify-content: space-around;
              align-items: center;

              .bottom-tip-content {
                margin: 0 40px;

                .tip {
                  line-height: 32px;
                  font-size: 18px
                }
              }
            }
          }
        }
      }
    }
  }
}

/*.overlay {
  color: #409eff
}*/
</style>
