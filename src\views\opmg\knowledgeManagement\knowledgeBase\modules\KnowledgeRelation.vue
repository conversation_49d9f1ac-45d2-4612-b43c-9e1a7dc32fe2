<template>
  <div>
    <div class='add-relation'>
      <div class='table-operator table-operator-style relation' v-if='!approvalView&&kInfo.createByUserId===sysUserId'>
        <a-button @click='handleAdd' style='margin: 0px'>关联</a-button>
      </div>
    </div>
    <div class='relation-type' v-for='(item,index) in ralationList' :key='"relation_"+item.processDefinitionKey'>
      <div class='title'>关联{{item.title}}</div>
      <relation-list
        :ref='item.processDefinitionKey'
        :processDefinitionKey='item.processDefinitionKey'
        :k-info='kInfo'
        :approval-view='approvalView'>
      </relation-list>
    </div>
    <add-relation-list-modal ref='modalForm' :tree-data='ralationList' @ok='modalFormOk'></add-relation-list-modal>
  </div>
</template>
<script>
import relationList from '@views/opmg/knowledgeManagement/knowledgeBase/modules/RelationList'
import addRelationListModal from '@views/opmg/knowledgeManagement/knowledgeBase/modules/AddRelationListModal'
import { queryConfigureDictItems } from '@api/api'
export default {
  name: "KnowledgeRelation",
  props: {
    kInfo: {
      type: Object,
      required: true
    },
    /**若是通过知识审批列表打卡查看，
     收藏、分享、打印、评论、关联、点赞、点踩都不可操作性，
     附件统统可以下载，同时告诉管理员，知识创建者设置的允许下载附件状态*/
    approvalView:{
      type:Boolean,
      required:false,
      default:false
    }
  },
  components: { relationList,addRelationListModal },
  data() {
    return {
      sysUserId:this.$store.getters.userInfo.id,
      ralationList:[]
    }
  },
  created() {
    this.getItilProcessKey('itilProcessKey')
  },
  methods:{
    getItilProcessKey(dictParenCode) {
      queryConfigureDictItems({ parentCode: dictParenCode }).then((res) => {
        if (res.success){
          if (res.result&&res.result.length>0){
            for (let i=0;i<res.result.length;i++){
              let data=res.result[i]
              let index=data.name.lastIndexOf('_')
              if (index>-1){
                let obj=JSON.parse(data.value)
                let nodeDta={
                  title:data.name.slice(index+1),
                  classification:obj.classification,
                  processDefinitionKey:obj.processDefinitionKey
                }
                this.ralationList.push(nodeDta)
              }
            }
          }
        }
      }).catch((err) => {
        this.$message.warning(err.message)
      })
    },
    handleAdd: function () {
      this.$refs.modalForm.init(this.kInfo.id);
      this.$refs.modalForm.title = '知识关联';
      this.$refs.modalForm.disableSubmit = false;
    },
    modalFormOk(key){
      this.$refs[key][0].init()
    }
  }
}
</script>

<style scoped lang="less">
@import '~@assets/less/common.less';
.add-relation{
  .relation{
    text-align: right;
    margin: 0px !important;
  }
}

.relation-type{
  margin-bottom: 16px;

  .title{
    padding-left: 7px;
    border-left: 4px solid #1e3674;
    margin-bottom: 10px
  }
}
</style>