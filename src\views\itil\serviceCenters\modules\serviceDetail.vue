<template>
  <div style="height: 100%" class="vScroll zxw">
    <div class="div-info">
      <p class="p-info-title">
        <span class="p-info-title-name">{{ detailsData.name }}</span>
        <span style="text-align: right; float: right; padding-right: 26px">
          <img src="~@/assets/return1.png" alt="" @click="getGo" style="width: 20px; height: 20px; cursor: pointer" />
        </span>
      </p>
      <a-row class="p-info-product">
        <a-col :sm="24" :lg="12" class="span-product"
          >时间范围：{{ detailsData.startTime }}~{{ detailsData.endTime }}</a-col
        >
        <a-col :sm="24" :lg="12" style="white-space: nowrap; text-align: right">
          <el-button
            class="topBtn top-FBtn"
            @click="evaluateAdd(detailsData)"
            v-if="(detailsData.whButtonCheck = '1')"
            type="primary"
            size="small"
            plain
            >添加评价</el-button
          >
          <el-button class="topBtn top-Btn" @click="fontClick(detailsData.fileUrl)" size="small" plain
            >导出报告</el-button
          >
          <el-button class="topBtn top-Btn" @click="deleteRecord(detailsData)" size="small" plain>删除报告</el-button>
        </a-col>
      </a-row>
    </div>

    <!-- table区域-begin -->
    <a-row class="div-container">
      <a-col class="colorBox">
        <span class="colorTotal">基本信息</span>
      </a-col>
      <a-col class="cont" style="margin-bottom: 20px; overflow: hidden; overflow-x: auto">
        <table style="width: 100%" class="myTable">
          <tr>
            <td class="column columnStyle">报告状态</td>
            <td class="column" v-if="detailsData.status == 'end'" style="color: #4ee44e">已完成</td>
            <td class="column" v-else style="color: #0079fe">进行中</td>
            <td class="column columnStyle">报告名称</td>
            <td class="column">{{ detailsData.name }}</td>
          </tr>
          <tr>
            <td class="column columnStyle">项目名称</td>
            <td class="column">{{ detailsData.projectText }}</td>
            <td class="column columnStyle">时间范围</td>
            <td class="column">{{ detailsData.startTime }}~{{ detailsData.endTime }}</td>
          </tr>
          <tr>
            <td class="column columnStyle">运维供应商</td>
            <td class="column">{{ detailsData.operSupplierText }}</td>
            <td class="column columnStyle">更新时间</td>
            <td class="column">{{ detailsData.updateTime }}</td>
          </tr>
          <tr>
            <td class="column columnStyle">创建时间</td>
            <td class="column">{{ detailsData.createTime }}</td>
            <td class="column columnStyle">创建人员</td>
            <td class="column">{{ detailsData.createBy }}</td>
          </tr>
          <tr>
            <td class="column columnStyle">备注信息</td>
            <td colspan="3" class="column">{{ detailsData.remarks }}</td>
          </tr>
        </table>
      </a-col>

      <a-col class="colorBox">
        <span class="colorTotal">问题情况</span>
      </a-col>
      <a-col class="cont" style="margin-bottom: 20px">
        <a-table
          bordered
          :columns="columns1"
          :data-source="busData"
          :expanded-row-keys.sync="expandedRowKeys"
          :pagination="pagination"
          style="white-space: nowrap"
        >
          <span slot="priority" slot-scope="priority, record">
            <div v-if="record.priority == 0" style="color: #ffb300">低</div>
            <div v-if="record.priority == 1" style="color: #fc7611">中</div>
            <div v-if="record.priority == 2" style="color: #df1a1a">高</div>
          </span>
          <span slot="eventType" slot-scope="eventType, record">
            <div v-if="record.eventType == 'safe'">安全</div>
            <div v-if="record.eventType == 'network'">网络</div>
            <div v-if="record.eventType == 'cloud'">云</div>
            <div v-if="record.eventType == 'line'">线路</div>
            <div v-if="record.eventType == 'software'">软件</div>
            <div v-if="record.eventType == 'hardware'">硬件</div>
          </span>
           <span slot="status" slot-scope="status, record">
            <div v-if="record.status == 0">新建</div>
            <div v-if="record.status == 1">审批</div>
            <div v-if="record.status == 2">处理</div>
            <div v-if="record.status == 3">审核</div>
            <div v-if="record.status == 4">评价</div>
            <div v-if="record.status == 5">关闭</div>
            <div v-if="record.status == 6">退回</div>
          </span>
          <span slot="result" slot-scope="result, record">
            <div v-if="record.result == 0">未提交</div>
            <div v-if="record.result == 1">处理中</div>
            <div v-if="record.result == 2" style="color: #139b33">已通过</div>
            <div v-if="record.result == 3" style="color: #df1a1a">已退回</div>
          </span>
        </a-table>
      </a-col>

      <a-col class="colorBox">
        <span class="colorTotal">指标情况</span>
      </a-col>
      <a-col class="cont" style="margin-bottom: 20px">
        <a-table
          :expandIcon="expandIcon"
          bordered
          :columns="columns"
          :data-source="targetData"
          :expanded-row-keys.sync="expandedRowKeys"
          :pagination="pagination"
          style="white-space: nowrap"
        >
          <span slot="fileName" slot-scope="fileName, record">
            <a style="color: #0079fe" v-if="record.fileName != '-'">{{ record.fileName }}</a>
            <a v-else>{{ record.fileName }}</a>
          </span>
        </a-table>
      </a-col>

      <a-col class="colorBox">
        <span class="colorTotal">评价信息</span>
      </a-col>
      <a-col class="cont">
        <a-timeline>
          <a-timeline-item color="#0079fe" v-for="(item, index) in commentData" :key="index">
            <div class="title">
              {{ item.depName }}评价
              <div class="triangle-left"></div>
            </div>
            <div class="evaluateCont">
              <a-row>
                <a-col :span="24">
                  <a-col :span="12">
                    <a-avatar :src="userAvatar" style="margin-right: 6px; height: 28px; width: 28px" />
                    <span class="evalName">{{ item.createText }} </span>
                  </a-col>
                  <a-col class="evaluateTime" :span="12">
                    {{ item.createTime }}
                  </a-col>
                </a-col>
                <a-col class="evaluateDescribes" :span="24">
                  {{ item.describes }}
                </a-col>
                <a-col :span="24">
                  <a-rate style="padding-left: 40px" v-model="item.satisf" disabled />
                </a-col>
              </a-row>
            </div>
          </a-timeline-item>
        </a-timeline>
      </a-col>
    </a-row>
    <evaluateAdd ref="modalForm" @ok="modalFormOk"></evaluateAdd>
  </div>
</template>

<script>
import evaluateAdd from './evaluateAdd'
import { mapGetters } from 'vuex'
import { postAction, getAction, deleteAction, getFileAccessHttpUrl } from '@/api/manage'
export default {
  name: 'serviceDetail',
  components: {
    evaluateAdd,
  },
  props: {
    dataDetail: {
      type: Object,
    },
  },
  data() {
    return {
      userAvatar: '', //用户头像地址
      form: this.$form.createForm(this),
      title: '操作',
      showHeader: false,
      pagination: false,
      rateValue: 2,
      columns: [
        {
          title: '指标项',
          dataIndex: 'indexItemText',
          key: 'indexItemText',
        },
        {
          title: '指标描述',
          dataIndex: 'indexDescripText',
          key: 'indexDescripText',
        },
        {
          title: '服务时间',
          dataIndex: 'serviceTime',
          key: 'serviceTime',
        },
        {
          title: '服务方式',
          dataIndex: 'serviceModeText',
          key: 'serviceModeText',
        },
        {
          title: '服务次数',
          dataIndex: 'serviceCount',
          key: 'serviceCount',
        },
        {
          title: '附件材料',
          dataIndex: 'fileName',
          key: 'fileName',
          scopedSlots: { customRender: 'fileName' },
        },
        {
          title: '备注信息',
          dataIndex: 'remarks',
          key: 'remarks',
        },
      ],
      columns1: [
        {
          title: '标题',
          align: 'center',
          dataIndex: 'title',
        },
        {
          title: '类型',
          align: 'center',
          dataIndex: 'eventType',
          scopedSlots: { customRender: 'eventType' },
        },
        {
          title: '优先级',
          dataIndex: 'priority',
          align: 'center',
          scopedSlots: { customRender: 'priority' },
        },
        {
          title: '报告时间',
          align: 'center',
          dataIndex: 'createTime',
        },
        {
          title: '状态',
          dataIndex: 'status',
          align: 'center',
          scopedSlots: { customRender: 'status' },
          // scopedSlots: { customRender: "" }
        },
        {
          title: '结果',
          align: 'center',
          dataIndex: 'result',
          scopedSlots: { customRender: 'result' },
        },
        {
          title: '结束时间',
          align: 'center',
          dataIndex: 'updateTime',
        },
        {
          title: 'SLA响应',
          dataIndex: 'slaResponse',
          align: 'center',
          scopedSlots: { customRender: 'slaResponse' },
        },
        {
          title: 'SLA完成',
          dataIndex: 'slaAccomplish',
          align: 'center',
          scopedSlots: { customRender: 'slaAccomplish' },
        },
        {
          title: '驳回次数',
          dataIndex: 'backNum',
          align: 'center',
        },
      ],
      rowSelection: {
        onChange: (selectedRowKeys, selectedRows) => {},
        onSelect: (record, selected, selectedRows) => {},
        onSelectAll: (selected, selectedRows, changeRows) => {},
      },
      expandedRowKeys: [],
      detailsData: [],
      targetData: [],
      busData: [],
      commentData: [],
      opinionData: [],
      url: {
        getList: '/reportdetails/itilReportDetailsInfo/getList',
        messageList: '/reportassess/itilReportAssessInfo/getList',
        delete: '/report/itilReportInfo/delete',
        getInfo: '/sys/user/getInfo', //个人信息
        getBusList: '/busQuestion/getBusiness',
      },
    }
  },

  mounted() {
    this.detailsData = this.dataDetail
    this.getServiceList(this.detailsData.id)
    this.getMessageList(this.detailsData.id)
    this.getInfo()
    this.getAvatar()
    this.getBus()
  },
  watch: {
    dataDetail: function (val, oldVal) {
      this.detailsData = val
      this.getServiceList(val.id)
      this.getMessageList(val.id)
    },
  },
  methods: {
    // table的expandIcon属性，修改默认展开关闭按钮 子表无数据时不显示展开图标
    expandIcon(props) {
      if (props.record.children.length > 0) {
        if (props.expanded) {
          return (
            <a
              onClick={(e) => {
                props.onExpand(props.record, e)
              }}
            >
              <a-icon type="minus-square" style="margin-right: 10px;  color: #1e3674" />
            </a>
          )
        } else {
          return (
            <a
              onClick={(e) => {
                props.onExpand(props.record, e)
              }}
            >
              <a-icon type="plus-square" style="margin-right: 10px;  color: #1e3674 " />
            </a>
          )
        }
      } else {
        return (
          <span style="margin-right:10px">
            <a-icon type="none" />
          </span>
        )
      }
    },

    getAvatar() {
      getAction('/sys/user/getUserAvatar').then((res) => {
        if (res.success) {
          this.$store.commit('SET_AVATAR', res.result)
          this.$store.commit('SET_CA', true)
          this.$nextTick(() => {
            this.userAvatar = ''
            this.userAvatar = getFileAccessHttpUrl(res.result)
          })
        }
      })
      // return getFileAccessHttpUrl(this.avatar());
    },
    getInfo() {
      getAction(this.url.getInfo).then((res) => {
        if (res.code == 200) {
          this.avatar = res.result.avatar
        } else {
          this.$message.error(res.message)
        }
      })
    },
    deleteRecord(record) {
      if (!this.url.delete) {
        this.$message.error('请设置url.delete属性!')
        return
      }
      var that = this
      this.$confirm({
        title: '确认删除',
        okText: '是',
        cancelText: '否',
        content: '是否删除选中数据?',
        onOk: function () {
          that.loading = true
          deleteAction(that.url.delete, { id: record.id }).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.$parent.pButton3(0)
            } else {
              that.$message.warning(res.message)
            }
          })
        },
      })
    },
    //返回上一级
    getGo() {
      this.$parent.pButton2(0, '')
    },
    fontClick(path) {
      if (path == null || path == '' || path == undefined) {
        let that = this
        that.$message.warning('还未生成报告！')
        return
      } else {
        window.open(window._CONFIG['downloadUrl'] + '/' + path)
      }
    },
    modalFormOk() {
      this.detailsData.whButtonCheck = 1
      this.getServiceList(this.detailsData.id)
      this.getMessageList(this.detailsData.id)
    },
    evaluateAdd(e) {
      let that = this
      that.targetData = []
      this.busData = []
      ;(that.commentData = []), (that.opinionData = []), this.$refs.modalForm.add(e)
      this.$refs.modalForm.title = '添加评论'
      this.$refs.modalForm.disableSubmit = false
    },
    getServiceList(ids) {
      let that = this
      getAction(this.url.getList, { reportId: ids }).then((res) => {
        if (res.success) {
          that.targetData = res.result.records || res.result
        }
        if (res.code === 510) {
          that.$message.warning(res.message)
        }
      })
    },
    getBus() {
      getAction(this.url.getBusList, {
        time1: this.dataDetail.startTime,
        time2: this.dataDetail.endTime,
        targetId: this.dataDetail.projectId,
        deptCode: this.dataDetail.operSupplierId,
      }).then((res) => {
        if (res.success) {
          this.busData = res.result
        }
      })
    },
    //評論信息&&意見信息
    getMessageList(ids) {
      let that = this
      getAction(this.url.messageList, { reportId: ids }).then((res) => {
        if (res.success) {
          // that.targetData = res.result.records || res.result;
          //commentData:[],
          //opinionData:[],
          for (let i = 0; i < res.result.length; i++) {
            if (res.result[i].depType == 'b') {
              that.commentData.push(res.result[i])
            } else if (res.result[i].depType == 's') {
              that.opinionData.push(res.result[i])
            }
          }
        }
        if (res.code === 510) {
          that.$message.warning(res.message)
        }
      })
    },
  },
}
</script>

<style lang="less" scoped>
@import '~@assets/less/scroll.less';
.evalName {
  vertical-align: center;
  font-family: PingFangSC-Medium;
  font-size: 16px;
  color: #000;
  margin-left: 6px;
}
.evaluateCont {
  width: 100%;
  height: 120px;
  background: inherit;
  background-color: #f3f3f3;
  box-sizing: border-box;
  border: 1px solid #f3f3f3;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '微软雅黑', sans-serif;
  font-weight: 400;
  font-style: normal;
  color: #535353;
  text-align: left;
  margin: 20px 0 0 15px;
  padding: 14px;
}
.topBtn {
  margin-right: 8px;
  padding-left: 20px;
  padding-right: 20px;
}
.top-FBtn {
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: #409eff;
}
.top-Btn {
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: #737578;
}
.title {
  width: 139px;
  height: 24px;
  line-height: 24px;
  background: #1e3674;
  text-align: center;
  margin-left: 15px;
  font-family: PingFangSC-Medium;
  font-size: 14px;
  color: #ffffff;
  position: relative;
}
.triangle-left {
  width: 0;
  height: 0;
  border-top: 12px solid transparent;
  border-right: 8px solid #1e3674;
  border-bottom: 12px solid transparent;
  position: absolute;
  left: -6%;
  top: 0;
}
::v-deep .ant-timeline-item-head {
  border-color: #1e3674 !important;
  border: 5px solid transparent;
}
::v-deep .ant-timeline-item-tail {
  border-left: 2px dashed #e8e8e8;
}
.cont {
  padding: 0 24px;
}

::v-deep .ant-table-bordered .ant-table-thead > tr > th,
.ant-table-bordered .ant-table-tbody > tr > td {
  text-align: center;
}
.column {
  text-align: left;
  padding: 0 20px;
  font-family: PingFangSC-Regular;
  font-size: 14px;
  white-space: nowrap;
  color: rgba(0, 0, 0, 0.85) !important;
}
.columnStyle {
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65) !important;
  min-width: 80px;
  background: #fafafa;
  /* font-weight: 700; */
  font-style: normal;
  text-align: center;
  padding-left: 0px;
  padding-right: 0px;
}
.myTable {
  border-collapse: collapse;
  margin: 0 auto;
  text-align: center;
}

.myTable td,
.myTable th {
  border: 1px solid #e4e4e4;
  color: #666;
  height: 45px;
}
.colorBox {
  padding: 18px 26px;
}
.colorTotal {
  font-family: PingFangSC-Medium;
  color: rgba(0, 0, 0, 0.85);
  padding-left: 7px;
  border-left: 4px solid #1e3674;
  font-size: 16px;
  font-weight: 700;
  white-space: nowrap;
}
.form-row {
  display: flex;
  margin: 0px 0px !important;
  align-items: center;
  height: 69px;
  background-color: white;
}
.fontStyle1 {
  font-family: 'Font Awesome 5 Pro Solid', 'Font Awesome 5 Pro Regular', 'Font Awesome 5 Pro', sans-serif;
  font-weight: 900;
  font-style: normal;
  font-size: 16px;
  text-align: left;
  padding-left: 16px;
}
.fontStyle2 {
  font-family: Microsoft YaHei, sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 14px;
  text-align: right;
  padding-right: 16px;
}
.div-container {
  width: 100%;
  background-color: #ffffff;
  margin-top: 12px;
  position: relative;
  padding-bottom: 20px;
}

.div-info {
  position: relative;
  background-color: white;
  padding: 10px 0 10px 24px;
  border-radius: 3px;
}
.cls-div {
  position: absolute;
  top: 6px;
  right: 11px;
  cursor: pointer;
}
.p-info-title {
  line-height: 45px;
  height: 45px;
  margin-bottom: 0px;
  font-family: PingFangSC-Medium;
  font-size: 18px;
  color: #000000;
}
.p-info-title-name {
  font-family: PingFangSC-Medium;
  font-size: 18px;
  color: #000000;
  font-weight: 700;
}
.span-product {
  line-height: 45px;
  height: 45px;
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
  white-space: nowrap;
}
.p-info-product {
  margin-bottom: 20px;
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
}
.span-assets {
  margin-left: 119px;
}
.status {
  padding-left: 7px;
}

::v-deep .ant-table-row {
  text-align: center;
}
::v-deep .ant-table-column-title {
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
}
::v-deep .ant-table-row {
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: #333333;
}
.evaluateTime {
  text-align: right;
  padding-top: 10px;
  padding-right: 20px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
  font-family: PingFangSC-Regular;
}
.evaluateDescribes {
  padding-left: 40px;
  padding-top: 10px;
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.45);
}
</style>


