<template>
  <div style="height: 100%">
    <div class="scale-btns">
      <a-button-group>
        <a-button type="primary" @click="processZoomOut">
          <a-icon type="minus" />
        </a-button>
        <a-button style="width: 80px">{{ Math.floor(defaultZoom * 10 * 10) + '%' }}</a-button>
        <a-button type="primary" @click="processZoomIn">
          <a-icon type="plus" />
        </a-button>
      </a-button-group>
      <a-popover placement="top" style="margin-left: 10px" trigger="hover">
        <template slot="content">
          <span >复位</span>
        </template>
        <a-button type="default" @click="fitViewport">
          <a-icon type="vertical-align-middle" />
        </a-button>
      </a-popover>
    </div>
    <div class="containers" ref="container">
      <div
        id="bpmnCanvas"
        class="canvas"
        ref="bpmnCanvas"
        @mousewheel.prevent="rollProcessZoom"
      ></div>
    </div>
  </div>
</template>

<script>
import { getAction } from '@/api/manage'
import BpmnViewer from 'bpmn-js/lib/Viewer'
import MoveCanvasModule from 'diagram-js/lib/navigation/movecanvas'
import ModelingModule from 'bpmn-js/lib/features/modeling'

export default {
  props: {
    row: {
      type: String,
      default: {},
    },
    // pictureVisible: {
    //   type: Boolean,
    //   default: false,
    // },
  },
  data() {
    return {
      bpmnViewer: null,
      defaultZoom: 1,
      zoomStep: 0.1,
      highLightNodes: [],
      highLightLines: [],
      runningNodes: [],
    }
  },
  computed: {
    _pictureVisible: {
      get() {
        return this.pictureVisible
      },
      set(v) {
        this.$emit('changePictureVisible', v)
      },
    },
  },
  created() {},
  mounted() {
    console.log("执行了 == 121 ")
    this.getRealXml()
  },
  methods: {
    getRealXml() {
      //获取实例xml和节点信息
      getAction('/flowable/flows', { processInstanceId: this.row }).then((res) => {
        //console.log('获取的realxml', res)
        if (res.success) {
          this.highLightNodes = res.result.highLightedActivities
          //   .filter(
          //     (el) => el.includes('Activity_') || el.includes('StartEvent_')
          //   )
          this.runningNodes = res.result.runningActivityIdList
          this.initPic(res.result.editor)
          //console.log('djs-overlay==',document.getElementsByClassName('djs-overlay'))
        }
      })
    },
    initPic(xml) {
      //初始化流程图
      this.defaultZoom = 1
      let _this = this
      this.bpmnViewer && this.bpmnViewer.destroy()
      this.bpmnViewer = new BpmnViewer({
        container: document.getElementById('bpmnCanvas'),
        width: '100%',
        additionalModules: [
          MoveCanvasModule, // 移动整个画布
          ModelingModule, //设置元素和线颜色
        ],
      })
      this.bpmnViewer.importXML(xml, function (err) {
        if (err) {
          console.error(err)
        } else {
          const canvas = _this.bpmnViewer.get('canvas')
          canvas.zoom('fit-viewport', 'auto')
          _this.bpmnViewer.get('canvas').zoom(_this.defaultZoom)
          // importXmlSuccess();
          _this.changeNodeColor()
          _this.fitViewport()

          const eventBus = _this.bpmnViewer.get('eventBus')
          const overlays = _this.bpmnViewer.get('overlays')
          console.log('overlays==,',overlays)
          let timer=null
          eventBus.on('element.hover', (e) => {
            if (e.element.type == 'bpmn:UserTask') {
              const param = {
                nodeId: e.element.id,
                processInsId: _this.row,
              }
              getAction('/flowable/task/queryByNodeId', param).then((res) => {
                if (res.success) {
                  if (res.result.taskFlag) {
                    clearTimeout(timer)
                    overlays.clear()
                    const $overlayHtml =
                      `<div class='process-tooltips' style='border-radius: 5px;background-color: #fff;border:1px solid #e8e8e8;box-shadow: 2px 2px 20px 1px #dddde0;min-width:300px'>
                         <div style='height: 48px;line-height:48px;padding:0px 12px;border-bottom: 1px solid #d9d9d9;font-size: 16px'>详情</div>
                         <table style='margin: 12px;width: calc(100% - 24px)'>
                           <tr style='border: 1px solid #e8e8e8;width: 100%'>
                             <td style='background-color: #fafafa;border-right: 1px solid #e8e8e8;white-space: nowrap;padding: 8px'>任务名称</td>
                             <td style='padding: 8px'>` + _this.formatData(res.result.taskName) + `</td>
                           </tr>
                           <tr style='border: 1px solid #e8e8e8;width: 100%'>
                             <td style='background-color: #fafafa;border-right: 1px solid #e8e8e8;white-space: nowrap;padding: 8px'>执行人</td>
                             <td style='padding: 8px'>` + _this.formatData(res.result.assignee) + `</td></tr>
                           <tr style='border: 1px solid #e8e8e8;width: 100%'>
                             <td style='background-color: #fafafa;border-right: 1px solid #e8e8e8;white-space: nowrap;padding: 8px'>候选人</td>
                             <td style='padding: 8px'>` + _this.formatData(res.result.candidateUserNames) + `</td></tr>
                           <tr style='border: 1px solid #e8e8e8;width: 100%'>
                             <td style='background-color: #fafafa;border-right: 1px solid #e8e8e8;white-space: nowrap;padding: 8px'>开始时间</td>
                             <td style='padding:8px'>` + _this.formatData(res.result.createTime) + `</td></tr>
                           <tr style='border: 1px solid #e8e8e8;width: 100%'>
                             <td style='background-color: #fafafa;border-right: 1px solid #e8e8e8;white-space: nowrap;padding: 8px'>结束时间</td>
                             <td style='padding:8px'>` + _this.formatData(res.result.endTime) + `</td></tr>
                           <tr style='border: 1px solid #e8e8e8;width: 100%'>
                             <td style='background-color: #fafafa;border-right: 1px solid #e8e8e8;white-space: nowrap;padding: 8px'>审批意见</td>
                             <td style='padding:8px'>` + _this.formatData(res.result.commentFullMessage) + `</td>
                           </tr>
                         </table></div>`

                    overlays.add(e.element.id, {
                      position: {
                        top: e.element.height + 8,
                        left: -(e.element.width / 2 + (300 / 2 - e.element.width))
                      },
                      html: $overlayHtml,
                    })
                    let doc=document.getElementsByClassName('process-tooltips')[0].parentNode
                    doc.style.zIndex=1000
                    doc.addEventListener('mouseenter',()=>{
                      clearTimeout(timer)
                    })
                    doc.addEventListener('mouseleave',()=>{
                      if(timer!=null){
                        clearTimeout(timer)
                        timer=null
                      }
                      timer= setTimeout(()=>{
                        overlays.clear()
                        // console.log("退出")
                      },500)
                      })
                  }
                } else {
                  _this.$message.error(res.message)
                }
              })
            }
          })
          eventBus.on('element.out', () => {
            if(timer!=null){
              clearTimeout(timer)
              timer=null
            }
              timer= setTimeout(()=>{
                overlays.clear()
                // console.log("退出")
              },500)
          })
        }
      })
    },
    formatData(data){
      if(data != null){
        return data
      }
      return ''
    },

    changeNodeColor() {
      //根据返回高亮节点 改变节点颜色；
      let modeling = this.bpmnViewer.get('modeling')
      const elementRegistry = this.bpmnViewer.get('elementRegistry')
      this.highLightNodes.forEach((el) => {
        modeling.setColor(elementRegistry.get(el), {
          stroke: '#52c41a',
          //   fill: '#4CBCF0',
        })
      })
      this.runningNodes.forEach((el) => {
        modeling.setColor(elementRegistry.get(el), {
          stroke: '#1890FF',
          //   fill: '#4CBCF0',
        })
      })
    },
    pictureHandleOk() {
      this._pictureVisible = false
    },
    rollProcessZoom(event) {
      /* event.wheelDelta 获取滚轮滚动值并将滚动值叠加给缩放比zoom wheelDelta统一为±120，其中正数表示为向上滚动，负数表示向下滚动 */
      let zoom = 0
      zoom += event.wheelDelta / 12
      let newZoom = this.defaultZoom * 100 + zoom
      if (newZoom < 20) {
        newZoom = 20
      } else if (newZoom > 400) {
        newZoom = 400
      }
      this.defaultZoom = newZoom / 100
      this.bpmnViewer.get('canvas').zoom(this.defaultZoom)
    },
    processZoomIn() {
      let newZoom = Math.floor(this.defaultZoom * 100 + this.zoomStep * 100) / 100
      if (newZoom > 4) {
        return
        // throw new Error('不能大于4')
      }
      this.defaultZoom = newZoom
      this.bpmnViewer.get('canvas').zoom(this.defaultZoom)
    },
    processZoomOut() {
      let newZoom = Math.floor(this.defaultZoom * 100 - this.zoomStep * 100) / 100
      if (newZoom < 0.2) {
        return
        // throw new Error('不能小于0.2')
      }
      this.defaultZoom = newZoom
      this.bpmnViewer.get('canvas').zoom(this.defaultZoom)
    },
    // 让图能自适应屏幕
    fitViewport() {
      this.defaultZoom = 1
      this.zoom = this.bpmnViewer.get('canvas').zoom('fit-viewport', 'auto')
    },
    processReZoom() {
      this.defaultZoom = 1
      this.bpmnViewer.get('canvas').zoom('fit-viewport', 'auto')
    },
  },
}
</script>

<style lang="less" scoped>
.scale-btns {
  margin-bottom: 12px;
}
.containers {
  background-color: #ffffff;
  width: 100%;
  //非全屏模式下，流程图容器containers的高度设置如下
  height: calc(100vh - 100px - 56px - 24px - 1px - 60px - 32px - 12px - 1px - 24px - 56px - 24px);
  .canvas {
    width: 100%;
    height: 100%;
  }
  .tipBox {
    width: 300px;
    background: #fff;
    border-radius: 4px;
    border: 1px solid red;
    padding: 12px;
    // .ant-popover-arrow {
    //   display: none;
    // }
    // p {
    //   line-height: 28px;
    //   margin: 0;
    //   padding: 0;
    // }
  }
}
</style>
<style lang="less">
.bjs-powered-by {
  display: none;
}
.flowMsgPopover {
  display: none;
}
.highlight:not(.djs-connection) .djs-visual > :nth-child(1) {
  fill: rgba(251, 233, 209, 1) !important; /* color elements as green */
}
.highlight g.djs-visual > :nth-child(1) {
  stroke: rgb(125, 214, 125) !important;
}
.highlight-line g.djs-visual > :nth-child(1) {
  stroke: rgba(0, 190, 0, 1) !important;
}

@-webkit-keyframes dynamicNode {
  to {
    stroke-dashoffset: 100%;
  }
}
.highlight {
  .djs-visual {
    -webkit-animation: dynamicNode 18s linear infinite;
    -webkit-animation-fill-mode: forwards;
  }
}
.tipBox {
  .ant-popover-arrow {
    display: none;
  }
}
</style>