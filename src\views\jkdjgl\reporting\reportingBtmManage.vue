<template>
  <div style="height:100%;">
    <keep-alive exclude='reportingBtmDetail'>
      <component :is="pageName" style="height:100%" :data="data" />
    </keep-alive>
  </div>
</template>
<script>
  import reportingBtmList from './reportingBtmList'
  import reportingBtmDetail from './modules/reportingBtmDetail'
  export default {
    name: "reportingBtmManage",
    data() {
      return {
        isActive: 0,
        data: {}
      };
    },
    components: {
      reportingBtmList,
      reportingBtmDetail
    },
    created() {
      this.pButton1(0);
    },
    //使用计算属性
    computed: {
      pageName() {
        switch (this.isActive) {
          case 0:
            return "reportingBtmList";
            break;

          default:
            return "reportingBtmDetail";
            break;
        }
      }
    },
    methods: {
      pButton1(index) {
        this.isActive = index;
      },
      pButton2(index, item) {
        this.isActive = index;
        this.data = item;
      }
    }
  }
</script>