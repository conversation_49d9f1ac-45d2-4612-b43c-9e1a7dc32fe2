<template>
  <div class="config">
    <config-grid v-show="type === 'grid'" :globalGridAttr="globalGridAttr" :id="id"/>
    <config-node v-show="type === 'node'" :globalGridAttr="globalGridAttr" :id="id" :operate="operate"/>
    <config-edge v-show="type === 'edge'" :globalGridAttr="globalGridAttr" :id="id"/>
  </div>
</template>

<script>
import ConfigGrid from './ConfigGrid/processIndex.vue'
import ConfigNode from './ConfigNode/index.vue'
import ConfigEdge from './ConfigEdge/index.vue'
import FlowGraph from '../../graph'
import './index.less'
import { globalGridAttr } from '../../models/global'
import { nodeOpt } from './ConfigNode/method'
export default {
  name: 'Index',
  components: {
    ConfigGrid,
    ConfigNode,
    ConfigEdge
  },
  data() {
    return {
      type: 'grid',
      id: '',
      globalGridAttr: globalGridAttr,
    }
  },
  props:{
    operate:{
      type:String,
      default:"create",
      required:true
    }
  },
  mounted() {
    setTimeout(() => {
      this.boundEvent()
    }, 200)
  },
  methods: {
    boundEvent() {
      const { graph } = FlowGraph
      graph.on('blank:click', () => {
        this.type = 'grid'
        this.id=""
      })
    }
  }
}
</script>

<style lang="less" scoped>
::v-deep .ant-tabs{
  height: 100%;
  .ant-tabs-content{
    height:calc(100% - 65px);
    overflow-y:auto;
  }
}
</style>
