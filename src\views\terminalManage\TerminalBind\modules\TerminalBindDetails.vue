<template>
  <a-card style="height: 100%;">
    <a-row>
      <a-col :span="24">
        <span style="float: right;margin-bottom: 12px;"><img src="~@/assets/return1.png" alt="" @click="getGo"
            style="width: 20px;height: 20px;cursor: pointer"></span>
      </a-col>
      <a-col :span="24">
        <a-descriptions bordered :column='column'>
          <a-descriptions-item label="使用人">
            {{ data.bindUser }}
          </a-descriptions-item>
          <a-descriptions-item label="所属单位">
            {{ data.deptId_dictText }}
          </a-descriptions-item>
          <a-descriptions-item label="联系电话">
            {{ data.phone }}
          </a-descriptions-item>
          <a-descriptions-item label="CPU类型">
            {{ data.cpuType_dictText }}
          </a-descriptions-item>
          <a-descriptions-item label="操作系统">
            {{ data.osType_dictText }}
          </a-descriptions-item>
          <a-descriptions-item label="备注">
            {{data.description}}
          </a-descriptions-item>
        </a-descriptions>
      </a-col>
    </a-row>
  </a-card>
</template>

<script>
  export default {
    name: 'networkChildrenDetails',
    props: {
      data: {
        type: Object
      }
    },
    data() {
      return {
        column:{ xxl: 2, xl: 2, lg: 2, md: 2, sm: 1, xs: 1}
        }
    },
    mounted() {},
    methods: {
      //返回上一级
      getGo() {
        this.$parent.pButton2(0);
      }
    }
  }
</script>
<style scoped lang="less">
.content{
  color: rgba(0,0,0,0.85) !important;
  word-break: break-all;
  white-space: normal;
  min-width:260px;
}
::v-deep .ant-descriptions-item-content {
  .content
}
::v-deep .ant-descriptions-bordered .ant-descriptions-item-label {
  text-align: center;
  color: rgba(0,0,0,.65);
  width: 15%;
}

::v-deep .ant-descriptions-bordered .ant-descriptions-item-content {
  width: 35%;
  .content
}
</style>