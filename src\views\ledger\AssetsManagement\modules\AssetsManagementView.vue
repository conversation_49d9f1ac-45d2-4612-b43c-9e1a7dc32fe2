<template>
  <j-modal
    :title="title"
    :visible="visible"
    :centered="true"
    switchFullscreen
    :confirmLoading="confirmLoading"
    :destroyOnClose="true"
    :width="modalWidth"
    @cancel="handleCancel"
    :footer="null"
  >
    <a-tabs :animated="false" :activeKey="activeKey" @change="callback">
      <a-tab-pane tab="资产信息" key="1">
        <a-form :form="form" >
          <div style="text-align:center;font-size:18px;margin-bottom: 20px;">资产基本信息</div>
          <div v-if="formBaseData!=null">
            <a-row :gutter='24'>
              <a-col v-bind='formItemLayout'>
                <a-form-item :labelCol="labelCol"
                             :wrapperCol="wrapperCol"
                             label="资产名称">
                  <a-input disabled v-model="formBaseData.assetName" />
                </a-form-item>
              </a-col>
              <a-col v-bind='formItemLayout'>
                <a-form-item :labelCol="labelCol"
                             :wrapperCol="wrapperCol"
                             label="质保开始时间">
                  <a-input disabled v-model="formBaseData.warrantyStart" />
                </a-form-item>
              </a-col>
              <a-col v-bind='formItemLayout'>
                <a-form-item :labelCol="labelCol"
                             :wrapperCol="wrapperCol"
                             label="资产类型">
                  <!-- <a-select placeholder="请选择资产类型" disabled v-model="formBaseData.assetType">
                    <a-select-option v-for="item in assetType" :key="item.value"> {{ item.title }} </a-select-option>
                   
                  </a-select> -->
                  <j-dict-select-tag v-model="formBaseData.assetType"  disabled title="资产类型" dictCode="asset_type"/>
                </a-form-item>
              </a-col>
              <a-col v-bind='formItemLayout'>
                <a-form-item :labelCol="labelCol"
                             :wrapperCol="wrapperCol"
                             label="到保日期">
                  <a-input disabled v-model="formBaseData.warrantyEnd" />
                </a-form-item>
              </a-col>
              <a-col v-bind='formItemLayout'>
                <a-form-item :labelCol="labelCol"
                             :wrapperCol="wrapperCol"
                             label="资产编号">
                  <a-input disabled v-model="formBaseData.assetNo" />
                </a-form-item>
              </a-col>
              <a-col v-bind='formItemLayout'>
                <a-form-item :labelCol="labelCol"
                             :wrapperCol="wrapperCol"
                             label="保修单位">
                  <a-input disabled v-model="formBaseData.warrantyUnit" />
                </a-form-item>
              </a-col>
              <a-col v-bind='formItemLayout'>
                <a-form-item :labelCol="labelCol"
                             :wrapperCol="wrapperCol"
                             label="IP地址">
                  <a-input disabled v-model="formBaseData.ipAddress" />
                </a-form-item>
              </a-col>
              <a-col v-bind='formItemLayout'>
                <a-form-item :labelCol="labelCol"
                             :wrapperCol="wrapperCol"
                             label="保修联系人">
                  <a-input disabled v-model="formBaseData.warranties" />
                </a-form-item>
              </a-col>
              <a-col v-bind='formItemLayout'>
                <a-form-item :labelCol="labelCol"
                             :wrapperCol="wrapperCol"
                             label="资产状态">
                  <j-dict-select-tag v-model="formBaseData.assetStatus"  disabled title="资产类型" dictCode="asset_queryStatus"/>
                </a-form-item>
              </a-col>
              <a-col  v-bind='formItemLayout'>
                <a-form-item :labelCol="labelCol"
                             :wrapperCol="wrapperCol"
                             label="保修人电话">
                  <a-input disabled v-model="formBaseData.warrantyPhone" />
                </a-form-item>
              </a-col>
              <a-col v-bind='formItemLayout'>
                <a-form-item :labelCol="labelCol"
                             :wrapperCol="wrapperCol"
                             label="添加方式">
                  <a-select disabled v-model="formBaseData.addType">
                    <a-select-option value="1">新增</a-select-option>
                    <a-select-option value="2">原有</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col v-bind='formItemLayout'>
                <a-form-item :labelCol="labelCol"
                             :wrapperCol="wrapperCol"
                             label="生产厂商">
                  <a-input disabled v-model="formBaseData.producer" />
                </a-form-item>
              </a-col>
              <a-col v-bind='formItemLayout'>
                <a-form-item :labelCol="labelCol"
                             :wrapperCol="wrapperCol"
                             label="归属地">
                  <a-input disabled v-model="formBaseData.region" />
                </a-form-item>
              </a-col>
              <a-col v-bind='formItemLayout'>
                <a-form-item :labelCol="labelCol"
                             :wrapperCol="wrapperCol"
                             label="型号">
                  <a-input disabled v-model="formBaseData.model" />
                </a-form-item>
              </a-col>
              <a-col v-bind='formItemLayout'>
                <a-form-item :labelCol="labelCol"
                             :wrapperCol="wrapperCol"
                             label="使用单位">
                  <a-input disabled v-model="formBaseData.userUnit" />
                </a-form-item>
              </a-col>
              <a-col v-bind='formItemLayout'>
                <a-form-item :labelCol="labelCol"
                             :wrapperCol="wrapperCol"
                             label="使用人">
                  <a-input disabled v-model="formBaseData.userid" />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
          <a-divider />
          <div style="text-align:center;font-size:18px;margin-bottom: 20px;">资产变更</div>
          <div v-if="formChangeData!=null">
            <a-row :gutter='24'>
              <a-col v-bind='formItemLayout'>
                <a-form-item :labelCol="labelCol"
                             :wrapperCol="wrapperCol"
                             label="采购日期">
                  <a-input disabled v-model="formChangeData.purchaseDate" />
                </a-form-item>
              </a-col>
              <a-col v-bind='formItemLayout'>
                <a-form-item :labelCol="labelCol"
                             :wrapperCol="wrapperCol"
                             label="采购人">
                  <a-input disabled v-model="formChangeData.purchaseUser" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter='24' v-if="formChangeData.downDate!=null">
              <a-col v-bind='formItemLayout'>
                <a-form-item :labelCol="labelCol"
                             :wrapperCol="wrapperCol"
                             label="下发日期">
                  <a-input disabled v-model="formChangeData.downDate" />
                </a-form-item>
              </a-col>
              <a-col v-bind='formItemLayout'>
                <a-form-item :labelCol="labelCol"
                             :wrapperCol="wrapperCol"
                             label="属地">
                  <a-input disabled v-model="formChangeData.downPlace" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter='24' v-if="formChangeData.deployDate!=null">
              <a-col v-bind='formItemLayout'>
                <a-form-item :labelCol="labelCol"
                             :wrapperCol="wrapperCol"
                             label="部署日期">
                  <a-input disabled v-model="formChangeData.deployDate" />
                </a-form-item>
              </a-col>
              <a-col v-bind='formItemLayout'>
                <a-form-item :labelCol="labelCol"
                             :wrapperCol="wrapperCol"
                             label="单位">
                  <a-input disabled v-model="formChangeData.deployCompany" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter='24' v-if="formChangeData.onlineDate!=null">
              <a-col v-bind='formItemLayout'>
                <a-form-item :labelCol="labelCol"
                             :wrapperCol="wrapperCol"
                             label="上线日期">
                  <a-input disabled v-model="formChangeData.onlineDate" />
                </a-form-item>
              </a-col>
              <a-col v-bind='formItemLayout'>
                <a-form-item :labelCol="labelCol"
                             :wrapperCol="wrapperCol"
                             label="使用人">
                  <a-input disabled v-model="formChangeData.userid" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter='24' v-if="formChangeData.leaveDate!=null">
              <a-col v-bind='formItemLayout'>
                <a-form-item :labelCol="labelCol"
                             :wrapperCol="wrapperCol"
                             label="暂存日期">
                  <a-input disabled v-model="formChangeData.leaveDate" />
                </a-form-item>
              </a-col>
              <a-col v-bind='formItemLayout'>
                <a-form-item :labelCol="labelCol"
                             :wrapperCol="wrapperCol"
                             label="暂存到">
                  <a-input disabled v-model="formChangeData.leavePlace" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter='24' v-if="formChangeData.transferDate!=null">
              <a-col v-bind='formItemLayout'>
                <a-form-item :labelCol="labelCol"
                             :wrapperCol="wrapperCol"
                             label="转移扶贫日期">
                  <a-input disabled v-model="formChangeData.transferDate" />
                </a-form-item>
              </a-col>
              <a-col v-bind='formItemLayout'>
                <a-form-item :labelCol="labelCol"
                             :wrapperCol="wrapperCol"
                             label="转移到">
                  <a-input disabled v-model="formChangeData.transferPlace" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter='24' v-if="formChangeData.destructionDate!=null">
              <a-col v-bind='formItemLayout'>
                <a-form-item :labelCol="labelCol"
                             :wrapperCol="wrapperCol"
                             label="销毁日期">
                  <a-input disabled v-model="formChangeData.destructionDate" />
                </a-form-item>
              </a-col>
              <a-col v-bind='formItemLayout'>
                <a-form-item :labelCol="labelCol"
                             :wrapperCol="wrapperCol"
                             label="销毁备注">
                  <a-input disabled v-model="formChangeData.destructionRemarks" />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </a-form>
      </a-tab-pane>
      <a-tab-pane tab="状态变更" key="2" forceRender>
        <!-- 变更日志列表-->
        <log-list ref="logList"></log-list>
      </a-tab-pane>
    </a-tabs>
  </j-modal>
</template>
<script>
  import { queryById,queryChangeLedgerInfo } from '@api/AssetsManagement'
  import LogList from './LogList'
export default {
  name: 'AssetsManagementView',
  props: ['queryStatus', 'assetType'],
  components: {
    LogList
  },
  data() {
    return {
      title: '操作',
      visible: false,
      confirmLoading: false,
      /* 弹框宽 */
      modalWidth: '1000px',
      form: this.$form.createForm(this),
      id: "", //记录id
      activeKey: '1', //活动的tab页
      formBaseData: null, //基本信息
      formChangeData: null, //变更信息
      changeLogList:[], //变更日志列表
      formItemLayout:{
        md:{span:12},
        sm:{span:24}
      },
      labelCol: {
        //style: 'width:105px',
        xs: { span: 24 },
        sm: { span: 7 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      showHeader: false,
      columns: [
        { title: 'Name', dataIndex: 'name', key: 'name', width: 100 },
        { title: 'actionPeople', dataIndex: 'actionPeople', key: 'actionPeople', width: 150 },
        { title: 'status', dataIndex: 'status', key: 'status' },
        { title: 'time', dataIndex: 'time', key: 'time' }
      ]
    }
  },
  methods: {
    show(id) {
      this.id = id
      this.activeKey = '1'
      this.getBaseInfo()
    },
    //tab切换
    callback(key) {
      this.activeKey = key
      if(key==1){
        this.getBaseInfo()
      }else if(key==2){
        this.getLogList()
      }
    },
    
    getLogList(){
      this.$refs.logList.loadList(this.id)
    },
    
    //获取基本信息
    getBaseInfo(){
      queryById({id: this.id}).then(res => {
        if(res.success){
          this.formBaseData = res.result
          this.getChangeLedgerInfo(this.formBaseData.assetNo)
        }else{
          this.$message.error("获取基本信息失败")
        }
      })
    },
    //获取变更信息
    getChangeLedgerInfo(assetNo){
      queryChangeLedgerInfo({assetNo:assetNo}).then(res => {
        if(res.success){
          this.formChangeData = res.result
          if(!this.visible){
            this.visible = true
          }
        }else{
          this.$message.error("获取变更记录失败")
        }
      })
    },

    close() {
      this.$emit('close')
      this.visible = false
    },
   
    handleCancel() {
      this.close()
    },
    
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/normalModal.less';
.ant-calendar-picker {
  width: 100%;
}
::v-deep .ant-modal-body {
  padding: 0px 48px 24px 48px;
}
</style>
