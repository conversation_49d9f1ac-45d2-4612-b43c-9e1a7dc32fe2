<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    :destroyOnClose="true"
    :centered='true'
    switchFullscreen
    @ok="handleOk"
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @cancel="handleCancel"
    cancelText="关闭">
    <alarm-confirm-window-form ref="realForm" @ok="submitCallback" :disabled="disableSubmit"></alarm-confirm-window-form>
  </j-modal>
</template>

<script>

  import AlarmConfirmWindowForm from './OrderAlarmConfirmWindowForm'
  export default {
    name: 'AlarmConfirmWindowModal',
    components: {
      AlarmConfirmWindowForm
    },
    data () {
      return {
        title:'',
        width:1000,
        visible: false,
        disableSubmit: false
      }
    },
    methods: {
      show (ids) {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.show(ids);
        })
      },
      close () {
        this.$emit('close');
        this.visible = false;
      },
      handleOk () {
        this.$refs.realForm.submitForm();
      },
      submitCallback(){
        this.$emit('ok');
        this.visible = false;
      },
      handleCancel () {
        this.close()
      }
    }
  }
</script>
<style scoped lang='less'>
@import '~@assets/less/normalModal.less';
</style>