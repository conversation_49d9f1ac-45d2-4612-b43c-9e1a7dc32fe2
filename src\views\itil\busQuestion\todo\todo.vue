<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll zxw">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <!-- 查询区域 -->
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <div class="table-page-search-wrapper">
          <!-- 搜索区域 -->
          <a-form layout="inline" v-bind="formItemLayout">
            <!-- 上三选择 -->
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="类型">
                  <!-- v-model="" @change="" -->
                  <j-dict-select-tag v-model="queryParam.eventType" placeholder="请选择" dictCode="question_type" />
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="优先级">
                  <a-select
                    placeholder="请选择"
                    :getPopupContainer="(node) => node.parentNode"
                    allowClear
                    v-model="queryParam.priorityInt"
                  >
                    <a-select-option value="0">低</a-select-option>
                    <a-select-option value="1">中</a-select-option>
                    <a-select-option value="2">高</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="状态">
                  <a-select placeholder="请选择" allowClear v-model="queryParam.status">
                    <a-select-option value="0">新建</a-select-option>
                    <a-select-option value="1">审批</a-select-option>
                    <a-select-option value="2">处理</a-select-option>
                    <a-select-option value="3">审核</a-select-option>
                    <a-select-option value="4">评价</a-select-option>
                    <a-select-option value="5">关闭</a-select-option>
                    <a-select-option value="6">退回</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue" v-show="toggleSearchStatus">
                <a-form-item label="标题">
                  <a-input
                    placeholder="请输入"
                    autocomplete="off"
                    v-model="queryParam.title"
                    :allowClear="true"
                  ></a-input>
                </a-form-item>
              </a-col>
              <!-- 按钮区域 -->
              <a-col :span="colBtnsSpan()">
                <span
                  class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                >
                  <a-button type="primary" @click="searchQuery" style="margin-left: 10px" class="btn-search-style"
                    >查询</a-button
                  >
                  <a-button @click="searchReset" style="margin-left: 10px" class="btn-reset-style">重置</a-button>
                  <a @click="doToggleSearch" class="btn-updown-style" style="margin-left: 10px; color: #409eff">
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered="false" style="flex: auto" class="core">
        <!-- 下三按钮区 -->
        <a-row style="margin-bottom: 16px" class="table-operator">
          <a-col class="tableBottom">
            <a-button @click="handleExportXlsEven('问题我的待办')">批量导出</a-button>
          </a-col>
        </a-row>
        <!-- table区域-begin -->
        <div>
          <a-table
            ref="table"
            bordered
            rowKey="id"
            :dataSource="dataSource"
            :columns="columns"
            :pagination="ipagination"
            :loading="loading"
            :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
            @change="handleTableChange"
          >
            <span slot="priority" slot-scope="priority, record">
              <div v-if="record.priority == 0" style="color: #ffb300">低</div>
              <div v-if="record.priority == 1" style="color: #fc7611">中</div>
              <div v-if="record.priority == 2" style="color: #df1a1a">高</div>
            </span>
            <span slot="eventType" slot-scope="eventType, record">
              <div v-if="record.eventType == 'safe'">安全</div>
              <div v-if="record.eventType == 'network'">网络</div>
              <div v-if="record.eventType == 'cloud'">云</div>
              <div v-if="record.eventType == 'line'">线路</div>
              <div v-if="record.eventType == 'software'">软件</div>
              <div v-if="record.eventType == 'hardware'">硬件</div>
            </span>

            <span slot="status" slot-scope="status, record">
              <div v-if="record.status == 0">新建</div>
              <div v-if="record.status == 1">审批</div>
              <div v-if="record.status == 2">处理</div>
              <div v-if="record.status == 3">审核</div>
              <div v-if="record.status == 4">评价</div>
              <div v-if="record.status == 5" style="color: #139b33">关闭</div>
              <div v-if="record.status == 6" style="color: #df1a1a">退回</div>
            </span>
            <span slot="slaResponse" slot-scope="slaResponse, record">
              <div v-if="record.slaResponseType == '0'" style="color: #139b33">
                {{ record.slaResponse }}
              </div>
              <div v-if="record.slaResponseType == '1'" style="color: #df1a1a">
                {{ record.slaResponse }}
              </div>
            </span>

            <span slot="slaAccomplish" slot-scope="slaAccomplish, record">
              <div v-if="record.slaAccomplishType == '0'" style="color: #139b33">
                {{ record.slaAccomplish }}
              </div>
              <div v-if="record.slaAccomplishType == '1'" style="color: #df1a1a">
                {{ record.slaAccomplish }}
              </div>
            </span>
            <span slot="result" slot-scope="result, record">
              <div v-if="record.result == 0">未提交</div>
              <div v-if="record.result == 1">处理中</div>
              <div v-if="record.result == 2" style="color: #139b33">已通过</div>
              <div v-if="record.result == 3" style="color: #df1a1a">已退回</div>
            </span>
            <!-- 操作 -->
            <span slot="action" slot-scope="text, record" class="caozuo">
              <a @click="handleDispose(record)">处理</a>
              <a-divider type="vertical" />
              <a @click="handleDeatails(record)">查看</a>
              <a-divider type="vertical" v-if="record.status == 1" />
              <a @click="backTask(record)" v-if="record.status == 1">退回</a>
            </span>
            <template slot="tooltip" slot-scope="text">
              <a-tooltip placement="topLeft" :title="text" trigger="hover">
                <div class="tooltip">
                  {{ text }}
                </div>
              </a-tooltip>
            </template>
          </a-table>
        </div>

        <j-modal :title="modalTaskTitle" v-model="modalTaskVisible" :mask-closable="false" :width="500">
          <div v-if="modalTaskVisible">
            <a-form ref="form" :model="form" :label-width="85" :rules="formValidate">
              <a-form-item label="审批意见" prop="reason">
                <a-input type="textarea" :allowClear="true" autocomplete="off" v-model="form.comment" :rows="4" />
              </a-form-item>
              <a-form-item label="文件上传">
                <j-upload v-model="form.file" :number="5"></j-upload>
              </a-form-item>
              <a-form-item label="下一审批人" v-show="isGateway">
                <span>分支网关处暂不支持自定义选择下一审批人，将发送给下一节点所有人</span>
              </a-form-item>
              <div v-show="form.type == 1">
                <a-form-item label="驳回至">
                  <a-select
                    v-model="form.backTaskKey"
                    :allowClear="true"
                    :loading="backLoading"
                    @change="changeBackTask"
                  >
                    <a-select-option v-for="(item, i) in backList" :key="i" :value="item.key">{{
                      item.name
                    }}</a-select-option>
                  </a-select>
                </a-form-item>
                <a-form-item label="指定原节点审批人" prop="assignees" v-show="form.backTaskKey != -1" :error="error">
                  <a-select
                    v-model="form.assignees"
                    placeholder="请选择"
                    allowClear
                    mode="multiple"
                    :loading="userLoading"
                  >
                    <a-select-option v-for="(item, i) in assigneeList" :key="i" :value="item.id">{{
                      item.realname
                    }}</a-select-option>
                  </a-select>
                </a-form-item>
              </div>
              <a-form-item label="消息通知">
                <a-checkbox v-model="form.sendMessage">站内消息通知</a-checkbox>
                <a-checkbox v-model="form.sendSms" disabled>短信通知</a-checkbox>
                <a-checkbox v-model="form.sendEmail" disabled>邮件通知</a-checkbox>
              </a-form-item>
            </a-form>
          </div>
          <div slot="footer" style="text-align: right">
            <a-button type="text" @click="modalTaskVisible = false">取消</a-button>
            <a-button type="primary" :loading="submitLoading" @click="handelSubmit">提交</a-button>
          </div>
        </j-modal>
        <todoDispose ref="todoDispose" @ok="modalFormOk"></todoDispose>
        <todoDetails ref="todoDetails" @ok="modalFormOk"></todoDetails>
        <todoSendback ref="todoSendback" @ok="modalFormOk"></todoSendback>
      </a-card>
    </a-col>
  </a-row>
</template>
<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import todoDispose from './modules/todoDispose'
import todoDetails from './modules/todoDetails'
import todoSendback from './modules/todoSendback'
import JUpload from '@/components/jeecg/JUpload'
import { getAction, deleteAction, putAction, postAction, downFile } from '@/api/manage'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'

export default {
  name: 'questionTodo',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components: {
    todoDispose,
    todoDetails,
    todoSendback,
    JUpload,
  },
  data() {
    return {
      formItemLayout: {
        labelCol: {
          style: 'width:70px',
        },
        wrapperCol: {
          style: 'width:calc(100% - 70px)'
        }
      },
      isGateway: false,
      error: '',
      showAssign: false,
      submitLoading: false, // 添加或编辑提交状态
      modalTaskVisible: false,
      modalTaskTitle: '',
      assigneeList: [],
      userLoading: false,
      backLoading: false,
      backList: [
        {
          key: '-1',
          name: '发起人',
        },
      ],
      form: {
        id: '',
        userId: '',
        procInstId: '',
        comment: '',
        file: '',
        type: 0,
        assignees: [],
        backTaskKey: '-1',
        sendMessage: true,
        sendSms: false,
        sendEmail: false,
      },
      formValidate: {
        // 表单验证规则
      },

      // 表头
      columns: [
        {
          title: '编号',
          align: 'center',
          dataIndex: 'processNumber',
        },
        {
          title: '标题',
          align: 'center',
          dataIndex: 'title',
        },
        {
          title: '类型',
          align: 'center',
          dataIndex: 'eventType',
          scopedSlots: { customRender: 'eventType' },
        },
        {
          title: '优先级',
          dataIndex: 'priority',
          align: 'center',
          scopedSlots: { customRender: 'priority' },
        },
        {
          title: '状态',
          dataIndex: 'status',
          align: 'center',
          scopedSlots: { customRender: 'status' },
          // scopedSlots: { customRender: "" }
        },
        {
          title: '结果',
          align: 'center',
          dataIndex: 'result',
          scopedSlots: { customRender: 'result' },
        },
        {
          title: '报告时间',
          dataIndex: 'createTime',
          align: 'center',
        },
        {
          title: '申请部门',
          dataIndex: 'orgText',
          align: 'center',
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          scopedSlots: { customRender: 'action' },
        },
      ],
      url: {
        list: '/busQuestion/todoList',
        getBackList: '/actTask/getBackList/',
        passAll: '/actTask/passAll/',
        backAll: '/actTask/backAll/',
        back: '/actTask/backBasics',
        backToTask: '/actTask/backToTask',
        exportXlsUrl: '/busQuestion/todoExportXls',
        getUserDepType: '/sys/sysDepart/getUserDepType',
      },
    }
  },
  computed: {
    //可行性测试，根据文件路径动态加载组件
    LcDict: function () {
      var myComponent = () => import(`@/components/dict/JDictSelectTag`)
      return myComponent
    },
  },
  created() {
    getAction(this.url.getUserDepType, null).then((res) => {
      if (res.success) {
        if (res.result == 'N') {
          this.columns = [
            {
              title: '编号',
              dataIndex: 'processNumber',
            },
            {
              title: '标题',
              dataIndex: 'title',
            },
            {
              title: '类型',
              dataIndex: 'eventType',
              scopedSlots: { customRender: 'eventType' },
            },
            {
              title: '优先级',
              dataIndex: 'priority',
              scopedSlots: { customRender: 'priority' },
            },
            {
              title: '状态',
              dataIndex: 'status',
              scopedSlots: { customRender: 'status' },
              // scopedSlots: { customRender: "" }
            },
            {
              title: '结果',
              dataIndex: 'result',
              scopedSlots: { customRender: 'result' },
            },
            {
              title: 'SLA响应',
              dataIndex: 'slaResponse',
              scopedSlots: { customRender: 'slaResponse' },
            },
            {
              title: 'SLA完成',
              dataIndex: 'slaAccomplish',
              scopedSlots: { customRender: 'slaAccomplish' },
            },
            {
              title: '报告时间',
              dataIndex: 'createTime',
            },
            {
              title: '操作',
              dataIndex: 'action',
              align: 'center',
              scopedSlots: { customRender: 'action' },
            },
          ]
        }
      }
    })
  },
  mounted() {},
  methods: {
    handleExportXlsEven(fileName) {
      if (!fileName || typeof fileName != 'string') {
        fileName = '导出文件'
      }
      let param = this.getQueryParams()
      if (this.selectedRowKeys && this.selectedRowKeys.length > 0) {
        param['selections'] = this.selectedRowKeys.join(',')
      }
      downFile(this.url.exportXlsUrl, param).then((data) => {
        if (!data) {
          this.$message.warning('文件下载失败')
          return
        }
        if (typeof window.navigator.msSaveBlob !== 'undefined') {
          window.navigator.msSaveBlob(new Blob([data], { type: 'application/vnd.ms-excel' }), fileName + '.xls')
        } else {
          let url = window.URL.createObjectURL(new Blob([data], { type: 'application/vnd.ms-excel' }))
          let link = document.createElement('a')
          link.style.display = 'none'
          link.href = url
          link.setAttribute('download', fileName + '.xls')
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link) //下载完成移除元素
          window.URL.revokeObjectURL(url) //释放掉blob对象
        }
      })
    },
    handleDispose: function (v) {
      this.$refs.todoDispose.show(v)
      this.$refs.todoDispose.visible = true
    },
    handleDeatails: function (record) {
      this.$refs.todoDetails.edit(record)
      this.$refs.todoDetails.title = '问题详情'
      this.$refs.todoDetails.disableSubmit = false
    },
    backTask(v) {
      this.modalTaskTitle = '问题退回'
      this.form.id = v.id
      this.form.procInstId = v.procInstId
      this.form.procDefId = v.procDefId
      this.form.priority = v.priority
      this.form.type = 1
      this.showAssign = false
      this.modalTaskVisible = true
      // 获取可驳回节点
      this.backList = [
        {
          key: '-1',
          name: '发起人',
        },
      ]
      this.form.backTaskKey = '-1'
      this.backLoading = true
      getAction(this.url.getBackList + v.procInstId).then((res) => {
        this.backLoading = false

        if (res.success) {
          res.result.forEach((e) => {
            this.backList.push(e)
          })
        }
      })
    },
    handelSubmit() {
      this.submitLoading = true
      var formData = Object.assign({}, this.form)
      formData.assignees = formData.assignees.join(',')
      // 驳回
      if (formData.backTaskKey == '-1') {
        // 驳回至发起人
        postAction(this.url.back, formData).then((res) => {
          this.submitLoading = false
          if (res.success) {
            this.$message.success('操作成功')
            this.modalTaskVisible = false
            this.loadData()
          }
        })
      } else {
        // 自定义驳回
        if (formData.backTaskKey != '-1' && formData.assignees.length < 1) {
          this.$message.error('请至少选择一个审批人')
          this.submitLoading = false
          return
        } else {
          this.error = ''
        }
        postAction(this.url.backToTask, formData).then((res) => {
          this.submitLoading = false
          if (res.success) {
            this.$message.success('操作成功')
            this.modalTaskVisible = false
            this.loadData()
          }
        })
      }
    },
    changeBackTask(v) {
      if (v == '-1') {
        return
      }
      this.userLoading = true
      getAction(this.url.getNode + v).then((res) => {
        this.userLoading = false
        if (res.success) {
          if (res.result.users && res.result.users.length > 0) {
            this.assigneeList = res.result.users

            // 默认勾选
            let ids = []
            res.result.users.forEach((e) => {
              ids.push(e.username)
            })
            this.form.assignees = ids
          }
        }
      })
    },
  },
}
</script>
<style lang="less" scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';

/*表头样式*/
::v-deep .ant-table-thead > tr > th {
  text-align: center;
  white-space: nowrap;
}

/*内容对齐方式、省略显示*/
::v-deep .ant-table-tbody > tr > td {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;

  &:first-child,
  &:nth-child(2),
  &:nth-child(3),
  &:nth-child(4),
  &:nth-child(5),
  &:nth-child(6),
  &:nth-child(7),
  &:nth-child(8),
  &:nth-child(9),
  &:nth-child(10) {
    text-align: center;
  }
}
</style>