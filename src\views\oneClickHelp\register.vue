<template>
  <div class="main" id="ochRegister">
    <div class="min-right-bg">
      <!-- <img src="/oneClickHelp/right-bg1.png" alt="" /> -->
    </div>
    <div class="form-div">
      <div class="form-title">
        <img :src="logo" alt="" />
        {{ logoTitle }}
      </div>
      <div class="form-sub-title">
        <div class="line"></div>
        <div class="text">运维助手</div>
        <div class="line"></div>
      </div>
      <div class="form-box">
        <a-form :form="form" style="width: 100%" class="user-layout-login">
          <!-- 用户账号 -->
          <a-form-item label="用户账号" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input
              size="large"
              placeholder="请输入用户账号"
              v-decorator.trim="['username', validatorRules.username]"
              :readOnly="!!model.id"
              :allowClear="true"
              autocomplete="off"
            />
          </a-form-item>
          <!-- 密码 -->
          <a-form-item label="登录密码" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input
              size="large"
              type="password"
              placeholder="请输入登录密码"
              v-decorator="['password', validatorRules.password]"
              :allowClear="true"
              autocomplete="off"
            />
          </a-form-item>
          <!-- 确认密码 -->
          <a-form-item label="确认密码" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input
              size="large"
              type="password"
              @blur="handleConfirmBlur"
              placeholder="请重新输入登录密码"
              v-decorator="['confirmpassword', validatorRules.confirmpassword]"
              :allowClear="true"
              autocomplete="off"
            />
          </a-form-item>
          <!-- 用户姓名 -->
          <a-form-item label="用户姓名" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input
              size="large"
              placeholder="请输入用户姓名"
              v-decorator.trim="['realname', validatorRules.realname]"
              :allowClear="true"
              autocomplete="off"
            />
          </a-form-item>
          <!-- 联系方式 -->
          <a-form-item label="联系方式" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="phone" style="z-index: 1">
            <a-input
              size="large"
              placeholder="请输入联系方式"
              v-decorator="['phone', validatorRules.phone]"
              autocomplete="off"
              :allowClear="true"
            />
          </a-form-item>
          <!-- 单位 -->
          <a-form-model-item label="所属部门" prop="selecteddeparts" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-tree-select
              size="large"
              :getPopupContainer="(node) => node.parentNode"
              tree-node-filter-prop="title"
              v-decorator.trim="['selecteddeparts', validatorRules.selecteddeparts]"
              :replaceFields="replaceFields"
              :treeData="departList"
              show-search
              style="width: 100%"
              :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
              placeholder="请选择您所在的部门"
              allow-clear
              multiple
              @change="onChangeTree"
              @select="onSelect"
            >
            </a-tree-select>
          </a-form-model-item>
          <!-- 头像 -->
          <a-form-item class="two-words" :label="'头\u3000像'" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <j-image-upload
              bizPath="image/usesrIcon"
              class="avatar-uploader"
              text="上传"
              v-model="fileList"
            ></j-image-upload>
          </a-form-item>
          <a-form-item style="text-align: center" class="formBtn">
            <div class="login-btn-div">
              <a-button
                size="large"
                type="primary"
                htmlType="submit"
                class="login-button"
                :loading="confirmLoading"
                @click.stop.prevent="handleSubmit"
                >注&nbsp;&nbsp;册
              </a-button>
              <div class="register-btn" @click="goLogin">有账号,去登陆</div>
            </div>
          </a-form-item>
        </a-form>
      </div>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import { getAction, postAction } from '@/api/manage'
import JImageUpload from '@/components/jeecg/JImageUpload'
import { phoneValidator } from '@/mixins/phoneValidator'
import { mapActions, mapGetters, mapState } from 'vuex'
import { getHostNameLocal } from '@/utils/util'
import { duplicateCheck } from '@/api/api'

export default {
  name: 'oneClickHelpRegister',
  components: {
    JImageUpload,
  },
  mixins: [phoneValidator],
  data() {
    return {
      confirmDirty: false,
      userId: '', //保存用户id,
      validatorRules: {
        username: {
          rules: [
            {
              required: true,
              message: '请输入用户账号!',
            },
            {
              pattern: /^[a-zA-Z0-9_]+$/,
              message: '用户账号仅包含英文数字下划线',
            },
            { min: 2, max: 20, message: '用户账号在2-20个字符之间' },
            {
              validator: this.validateUsername,
            },
          ],
        },
        password: {
          rules: [
            {
              required: true,
              // pattern: /^(?=.*[a-zA-Z])(?=.*\d)(?=.*[~!@#$%^&*()_+`\-={}:";'<>?,./]).{8,}$/,
              message: '请输入密码！',
            },
            {
              validator: this.validateToNextPassword,
            },
          ],
        },
        confirmpassword: {
          rules: [
            {
              required: true,
              message: '请重新输入登录密码!',
            },
            {
              validator: this.compareToFirstPassword,
            },
          ],
        },
        realname: {
          rules: [
            { required: true, message: '请输入用户姓名!' },
            { min: 2, max: 20, message: '用户姓名在2-20位字符之间' },
          ],
        },
        selecteddeparts: {
          rules: [{ required: true, message: '请选择所属部门!' }],
        },
        phone: {
          rules: [
            {
              required: true,
              message: '请输入手机号！',
            },
            { validator: this.phone },
            // {
            //   validator: this.validatePhone,
            // },
          ],
        },
      },
      departIds: [], //负责部门id
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 19 },
      },
      confirmLoading: false,
      form: this.$form.createForm(this),
      url: {
        fileUpload: window._CONFIG['domianURL'] + '/sys/common/upload',
      },
      fileList: [],
      logo: window.config.oneClickHelp.helpLogoUrl,
      logoTitle: window.config.oneClickHelp.platformTitle,
      departList: [],
      replaceFields: {
        children: 'children',
        title: 'deptName',
        key: 'deptId',
        value: 'deptId',
      },
      pwdRuleUrl: 'umpPwdManage/umpPwdManage/list',
      hostName: '',
      centerScale: 1,
      resizeObserver: null,
      leftNum: 0,
      topNum: 0,
    }
  },
  created() {
    this.getPwdRuleData()
    this.getDepartList()
    this.hostName = getHostNameLocal()
  },
  mounted() {
   
  },
  computed: {
    uploadAction: function () {
      return this.url.fileUpload
    },
  },
  methods: {
    setScale() {
      if (window.innerWidth <= 1920 || window.innerHeight <= 1080) {
        let scaleW = window.innerWidth / 1920
        let scaleH = window.innerHeight / 1080
        // this.centerScale = Math.min(scaleH, scaleW)
        this.centerScale = scaleW
      } else {
        this.centerScale = 1
      }
      this.leftNum = (window.innerWidth - 1920 * this.centerScale) / 2
      this.topNum = (window.innerHeight - this.centerScale * 1080) / 2
    },
    //动态生成密码规则
    getPwdRuleData() {
      getAction(this.pwdRuleUrl).then((res) => {
        if (res.success) {
          let pwdRuleInfo = res.result.records[0]
          if (pwdRuleInfo) {
            this.setValidator(pwdRuleInfo)
          }
        }
      })
    },
    setValidator(info) {
      let capRegStr = ''
      let lowerRegStr = ''
      let numRegStr = ''
      let speRegStr = ''
      let cbStr = ''
      if (!!info.pwdMin) {
        cbStr = '密码至少由' + info.pwdMin + '位组成，包含'
      }
      if (info.capitalize) {
        capRegStr = '[A-Z]+'
        cbStr += '大写字母'
      }
      if (info.lowercase) {
        lowerRegStr = '[a-z]+'
        cbStr += cbStr.length > 2 + info.pwdMin.length ? '、小写字母' : '小写字母'
      }
      if (info.hasNum) {
        numRegStr = '[0-9]+'
        cbStr += cbStr.length > 2 + info.pwdMin.length ? '、数字' : '数字'
      }
      if (info.special) {
        speRegStr += "[`~!_@#$^&*()=|{}':;',\\[\\].<>/?~！@#￥……&*（）——|{}【】‘；：”“'。，、？]+"
        cbStr += cbStr.length > 2 + info.pwdMin.length ? '、特殊字符' : '特殊字符'
      }
      this.validatorRules.password.rules.push({
        validator: (rule, value, callback) => {
          let capRegEn = new RegExp(capRegStr)
          let lowerRegEn = new RegExp(lowerRegStr)
          let numRegEn = new RegExp(numRegStr)
          let speRegEn = new RegExp(speRegStr)
          if (
            value &&
            (value.length < parseInt(info.pwdMin) ||
              !capRegEn.test(value) ||
              !lowerRegEn.test(value) ||
              !numRegEn.test(value) ||
              !speRegEn.test(value))
          ) {
            callback(cbStr + '！')
          } else {
            callback()
          }
        },
      })
    },
    ...mapGetters(['avatar']),
    // 获取单位列表
    getDepartList() {
      getAction('/sys/sysDepart/queryAllTree').then((res) => {
        this.departList = res && res.length > 0 ? res : []
      })
    },
    onChangeTree(value) {},
    handleSubmit() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let formData = Object.assign(this.model, values)
          if (that.fileList != '') {
            formData.avatar = that.fileList
          } else {
            formData.avatar = null
          }
          if(formData.selecteddeparts && formData.selecteddeparts.length > 0){
            formData.selecteddeparts = formData.selecteddeparts.join()
          }else{
            formData.selecteddeparts = ""
          }
          
          // console.log('执行注册了 === ', formData)
          postAction('/sys/user/addByTerminal', formData)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.goLogin()
              } else {
                that.$message.warning(res.message)
              }
            })
            .catch((err) => {
              that.$message.warning('注册失败，请检查接口')
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    compareToFirstPassword(rule, value, callback) {
      const form = this.form
      if (value && value !== form.getFieldValue('password')) {
        callback('两次输入的密码不一样！')
      } else {
        callback()
      }
    },

    validateToNextPassword(rule, value, callback) {
      const form = this.form
      const confirmpassword = form.getFieldValue('confirmpassword')

      if (value && confirmpassword && value !== confirmpassword) {
        callback('两次输入的密码不一样！')
      }
      if (value && this.confirmDirty) {
        form.validateFields(['confirm'], { force: true })
      }
      callback()
    },

    handleConfirmBlur(e) {
      const value = e.target.value
      this.confirmDirty = this.confirmDirty || !!value
    },
    goLogin() {
      this.$router.push({ path: '/oneClickHelp/login'})
    },
    onSelect() {
      getAction('/sys/sysDepart/queryById', {
        id: arguments[0],
      }).then((res) => {
        if (res.success) {
          this.model.addrId = res.result.cityId
          this.model.deptAddress = res.result.address
        }
      })
    },
    validateUsername(rule, value, callback) {
      var params = {
        tableName: 'sys_users',
        fieldName: 'username',
        fieldVal: value,
        dataId: this.userId,
      }
      duplicateCheck(params).then((res) => {
        if (res.success) {
          callback()
        } else {
          callback('用户名已存在!')
        }
      })
    },
    validatePhone(rule, value, callback) {
      var params = {
        tableName: 'sys_users',
        fieldName: 'phone',
        fieldVal: value,
        dataId: this.userId,
      }
      duplicateCheck(params).then((res) => {
        if (res.success) {
          callback()
        } else {
          callback('手机号已存在!')
        }
      })
    },
  },
}
</script>

<style lang="less" scoped>
@import '~@assets/less/onclickStyle.less';
.main {
  width: 100%;
  height: 100%;
  background-color: #020a28;
  overflow: hidden;
  position: relative;
  .center {
    // width: 1920px;
    // height: 1080px;
    // background-color: #020a28;
    // transform-origin: top left;
    // background-image: url(/oneClickHelp/bg.png);
    // background-repeat: no-repeat;
    // background-size: 100% 100%;
    // position: absolute;
  }
}
.min-right-bg {
  position: absolute;
  right: 0px;
  top: 0px;
  height: 100%;
  width: 60%;
  background-image: url(/oneClickHelp/right-bg.png);
  background-position: center;
  background-size: auto 100%;
  background-repeat: no-repeat;
  background-color: #020a28;
  img {
    width: 100%;
    height: 100%;
  }
}
.form-div {
  min-width: 552px;
  position: absolute;
  height: 100%;
  left: 6%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}
.form-title {
  font-size: calc(44 / 19.2 * 1vw);
  font-weight: 400;
  color: #ffffff;
  opacity: 0.85;
  // margin-bottom: 30px;
  display: flex;
  align-items: center;
  img {
    width: calc(58 / 19.2 * 1vw);
    height: calc(58 / 19.2 * 1vw);
    margin-right: 10px;
  }
}
.form-sub-title {
  display: flex;
  align-items: center;
  margin-top: 20px;
  margin-bottom: 40px;
  .line {
    width: 92px;
    height: 1px;
    background: #86c8ff;
  }
  .text {
    font-size: calc(30 / 19.2 * 1vw);
    font-weight: normal;
    color: #86c8ff;
    letter-spacing: 5px;
    margin: 0px 10px;
    font-style: italic;
  }
}
.form-box {
  width: calc(552 / 19.2 * 1vw);
}
.ukey-tip {
  color: #fff;
  font-size: 18px;
  text-align: center;
  width: 100%;
  // font-style: italic;
}
.user-layout-login {
  width: 420px;
  label {
    font-size: 14px;
  }

  .forge-password {
    font-size: 14px;
  }
  .login-btn-div {
    position: relative;
  }
  button.login-button {
    padding: 0 15px;
    font-size: calc(20 / 19.2 * 1vw);
    height: calc(60 / 19.2 * 1vw);
    width: calc(214 / 19.2 * 1vw);
    // background-image: url(../../assets/login.png);
    // background-size: 100% 100%;
    // background-repeat: no-repeat;
    background: linear-gradient(0deg, #0576c5, #55d0fe);
    border: 0;
    border-radius: 50px;
  }

  .user-login-other {
    text-align: left;
    margin-top: 24px;
    line-height: 22px;

    .item-icon {
      font-size: 24px;
      color: rgba(0, 0, 0, 0.2);
      margin-left: 16px;
      vertical-align: middle;
      cursor: pointer;
      transition: color 0.3s;

      &:hover {
        color: #1e3674;
      }
    }

    .register {
      float: right;
    }
  }
  ::v-deep .ant-form-item-label > label {
    color: #fff;
  }
}
.authorization {
  color: #fff;
  .authorization-upload {
    width: 468px;
    height: 248px;
    background-image: url(../../assets/05.png);
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    letter-spacing: 2px;
    a {
      color: #fff;
    }
    a:hover {
      color: skyblue;
    }
  }
  .authorization-button {
    width: 100%;
    display: flex;
    justify-content: center;
    margin-top: 40px;

    .shouquan-button {
      padding: 0 15px;
      font-size: 20px;
      height: 60px;
      width: 46%;
      background-image: url(../../assets/login.png);
      background-repeat: no-repeat;
      background-color: transparent;
      border: 0;
      border-radius: 50px;
    }
  }
}
/deep/ .ant-upload-list-item-name {
  color: #fff !important;
}
/deep/ .anticon-paper-clip {
  color: #fff !important;
}
/deep/ .ant-input {
  background-color: transparent;
  color: #fff;
}
/deep/ .ant-input:focus {
  border-color: #fff;
}
/deep/ .ant-input-affix-wrapper:hover .ant-input:not(.ant-input-disabled) {
  border-color: #fff;
}
/deep/ .ant-input:hover {
  border-color: #fff;
}
/deep/ .has-error .ant-input-affix-wrapper .ant-input,
.has-error .ant-input-affix-wrapper .ant-input:hover {
  background-color: transparent;
}
/deep/ .ant-select-selection {
  background-color: transparent;
  color: #fff;
}
/deep/ .ant-select-selection:hover {
  border-color: #fff;
}
/deep/ .ant-select-dropdown {
  background-color: #030b2b;
  color: #fff;
}
/deep/ .ant-select-tree {
  color: #fff;
}
/deep/ .ant-select-tree li .ant-select-tree-node-content-wrapper {
  color: #fff;
}
/deep/.ant-select-tree-dropdown .ant-select-dropdown-search {
  background-color: #030b2b;
}
/deep/ .ant-select-tree-dropdown .ant-select-dropdown-search .ant-select-search__field {
  background-color: #030b2b;
}
/deep/ .ant-upload.ant-upload-select-picture-card {
  background-color: transparent;
}
/deep/ .ant-upload.ant-upload-select-picture-card:hover {
  border-color: #fff;
}
/deep/ .anticon {
  color: #fff;
}
/deep/ .ant-select-selection__choice__remove .anticon{
  color:#000;
}
/deep/ .ant-select-dropdown {
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
}
/deep/ .ant-select-tree li .ant-select-tree-node-content-wrapper.ant-select-tree-node-selected {
  background-color: transparent;
  color: #66ffff;
  background-image: linear-gradient(
    90deg,
    rgba(21, 85, 175, 0.6) 2%,
    rgba(21, 85, 175, 0.26) 43%,
    rgba(21, 85, 175, 0) 100%
  );
}
/deep/ .ant-select-tree li .ant-select-tree-node-content-wrapper:hover {
  background-color: transparent;
  color: #66ffff;
  background-image: linear-gradient(
    90deg,
    rgba(21, 85, 175, 0.6) 2%,
    rgba(21, 85, 175, 0.26) 43%,
    rgba(21, 85, 175, 0) 100%
  );
}
.register-btn {
  color: #fff;
  text-align: left;
  cursor: pointer;
  position: absolute;
  right: 0px;
  bottom: 0px;
  font-size: calc(18 / 19.2 * 1vw);
  letter-spacing: 3px;
  opacity: 0.7;
}
.register-btn:hover {
  opacity: 1;
}
</style>




<style scoped>
.valid-error .ant-select-selection__placeholder {
  color: #f5222d;
}
.codeStyle {
  position: absolute;
  top: 17px;
  right: 25px;
  border-left: 1px solid rgb(170, 170, 170);
  padding-left: 10px;
  width: 25%;
}
#username,
#password,
#inputCode {
  width: 100%;
  height: 64px;
  background-color: transparent;
  color: #fff;
  outline: none;
  border-inline: none;
  border: 1px solid #b2b4be;
  border-radius: 45px;
  text-align: center;
  margin-bottom: 4px;
  font-size: 24px;
  padding-top: 0;
}
#inputCode {
  padding: 0 104px 0 93px;
}
/*chrome浏览器input自动填充颜色设为透明  设置字体颜色*/
input:-webkit-autofill {
  -webkit-text-fill-color: #ffffff !important;
  transition: background-color 5000s ease-in-out 0s;
}
input:-internal-autofill-selected {
  background-color: transparent !important;
  color: rgb(0, 0, 0) !important;
  border: 1px solid #b2b4be;
  border-radius: 45px;
}
input::-webkit-input-placeholder {
  /* WebKit browsers */
  color: rgba(255, 255, 255, 0.45);
  font-size: 18px;
}
input:-moz-placeholder {
  /* Mozilla Firefox 4 to 18 */
  color: rgba(255, 255, 255, 0.45);
  font-size: 18px;
}
input::-moz-placeholder {
  /* Mozilla Firefox 19+ */
  color: rgba(255, 255, 255, 0.45);
  font-size: 18px;
}
input:-ms-input-placeholder {
  /* Internet Explorer 10+ */
  color: rgba(255, 255, 255, 0.45);
  font-size: 18px;
}
.code {
  font-size: 24px;
  color: #aaa;
  cursor: pointer;
  padding-bottom: 6px;
  line-height: 30px;
  text-align: center;
  user-select: none;
}
.formBtn {
  margin-top: 30px;
}
.has-error .ant-form-explain,
.has-error .ant-form-split {
  text-align: center;
}
.formBox {
  text-align: center;
}
@media (min-width: 1440px) and (max-width: 1620px) {
  #username,
  #password,
  #inputCode {
    height: 62px !important;
  }
  .formBtn {
    margin-top: 15px;
  }
}
@media (max-width: 1380px) {
  #username,
  #password,
  #inputCode {
    width: 90% !important;
    height: 57px !important;
  }
  .formBox {
    text-align: center;
  }
  .formBtn {
    margin-top: 15px;
  }
  .ant-form-item {
    margin-bottom: 15px !important;
  }
  button.login-button {
    font-size: 20px !important;
    height: 50px !important;
    width: 37% !important;
    background-size: 100% !important;
  }
  .codeStyle {
    position: absolute;
    top: 12px;
    right: 35px;
    border-left: 1px solid rgb(170, 170, 170);
    padding-left: 10px;
    width: 25%;
  }
}
</style>