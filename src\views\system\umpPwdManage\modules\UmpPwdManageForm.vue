<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-item label="超时时长/分钟" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number
                placeholder="请输入超时时长"
                :min="1"
                :size="11"
                v-decorator="['overTime', validatorRules.assetsCode]"
              />

              <!-- <a-input v-decorator="['overTime' ,validatorRules.assetsCode]" placeholder="请输入超时时长"></a-input> -->
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="密码最小位数" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number
                placeholder="请输入密码最小位数"
                :min="1"
                v-decorator="['pwdMin', validatorRules.assetsCode]"
              />
              <!-- <a-input v-decorator="['pwdMin', validatorRules.assetsCode]" placeholder="请输入密码最小位数"></a-input> -->
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="失误次数" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number
                placeholder="请输入失误次数"
                :min="1"
                v-decorator="['errorNum', validatorRules.assetsCode]"
              />
              <!-- <a-input v-decorator="['errorNum', validatorRules.assetsCode]" placeholder="请输入失误次数"></a-input> -->
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="自动解锁时间/分钟" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number
                placeholder="请输入自动解锁时间"
                :min="1"
                v-decorator="['unLock', validatorRules.assetsCode]"
              />
              <!-- <a-input v-decorator="['unLock', validatorRules.assetsCode]" placeholder="请输入自动解锁时间"></a-input> -->
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="密码更新时间/天" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number
                placeholder="密码更新时间"
                :min="1"
                v-decorator="['updateTimes', validatorRules.assetsCode]"
              />
              <!-- <a-input v-decorator="['unLock', validatorRules.assetsCode]" placeholder="请输入自动解锁时间"></a-input> -->
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="开始时间/HH:ss" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <!-- <a-input-number placeholder="密码更新时间" :min="1" v-decorator="['updateTime', validatorRules.assetsCode]" /> -->
              <a-input v-decorator="['startTime']" placeholder="请输入开始时间"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="结束时间/HH:ss" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <!-- <a-input-number placeholder="密码更新时间" :min="1" v-decorator="['updateTime', validatorRules.assetsCode]" /> -->
              <a-input v-decorator="['stopTime']" placeholder="请输入结束时间"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="开始ip段" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <!-- <a-input-number placeholder="密码更新时间" :min="1" v-decorator="['updateTime', validatorRules.assetsCode]" /> -->
              <a-input v-decorator="['startIp']" placeholder="请输入开始ip段/0.0.0.0"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="结束ip段" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <!-- <a-input-number placeholder="密码更新时间" :min="1" v-decorator="['updateTime', validatorRules.assetsCode]" /> -->
              <a-input v-decorator="['stopIp']" placeholder="请输入结束ip段/***************"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="MAC地址" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <!-- <a-input-number placeholder="密码更新时间" :min="1" v-decorator="['updateTime', validatorRules.assetsCode]" /> -->
              <a-input v-decorator="['mac']" placeholder="请输入mac地址/xx-xx-xx-xx-xx-xx"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="密码复杂度设置" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <font color="red">请最少勾选3个包含</font>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item class="two-words" label="是否包含大写" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-radio-group v-model="capitalize">
                <a-radio value="1"> 包含</a-radio>
                <a-radio value="0"> 不包含</a-radio>
              </a-radio-group>
            </a-form-item>
            <!-- <a-form-item label="是否包含大写 " :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag type="radio" v-decorator="['capitalize']" :trigger-change="true" dictCode="" placeholder="是否包含大写" />
            </a-form-item> -->
          </a-col>
          <a-col :span="24">
            <a-form-item class="two-words" label="是否包含小写" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-radio-group v-model="lowercase">
                <a-radio value="1"> 包含</a-radio>
                <a-radio value="0"> 不包含</a-radio>
              </a-radio-group>
            </a-form-item>
            <!-- <a-form-item label="是否包含小写" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag type="radio" v-decorator="['lowercase']" :trigger-change="true" dictCode="" placeholder="请选择是否包含小写" />
            </a-form-item> -->
          </a-col>
          <a-col :span="24">
            <a-form-item class="two-words" label="是否包含数字" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-radio-group v-model="number">
                <a-radio value="1"> 包含</a-radio>
                <a-radio value="0"> 不包含</a-radio>
              </a-radio-group>
            </a-form-item>
            <!-- <a-form-item label="是否包含数字" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag type="radio" v-decorator="['number']" :trigger-change="true" dictCode="" placeholder="请选择是否包含数字" />
            </a-form-item> -->
          </a-col>
          <a-col :span="24">
            <a-form-item class="two-words" label="是否包含特殊字符" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-radio-group v-model="special">
                <a-radio value="1"> 包含</a-radio>
                <a-radio value="0"> 不包含</a-radio>
              </a-radio-group>
            </a-form-item>

            <!-- <a-form-item label="是否包含特殊字符" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag type="radio" v-decorator="['special']" :trigger-change="true" dictCode="" placeholder="请选择是否包含特殊字符" />
            </a-form-item> -->
          </a-col>
          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import pick from 'lodash.pick'
import { validateDuplicateValue } from '@/utils/util'
import JFormContainer from '@/components/jeecg/JFormContainer'
import JDictSelectTag from '@/components/dict/JDictSelectTag'

export default {
  name: 'UmpPwdManageForm',
  components: {
    JFormContainer,
    JDictSelectTag,
  },
  props: {
    //流程表单data
    formData: {
      type: Object,
      default: () => {},
      required: false,
    },
    //表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false,
    },
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false,
    },
  },
  data() {
    return {
      form: this.$form.createForm(this),
      model: {},
      capitalize: '0',
      lowercase: '0',
      number: '0',
      special: '0',
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      confirmLoading: false,

      validatorRules: {
        assetsCode: {
          rules: [{ required: true, message: '请输入参数!' }],
        },
      },
      url: {
        add: '/umpPwdManage/umpPwdManage/add',
        edit: '/umpPwdManage/umpPwdManage/edit',
        queryById: '/umpPwdManage/umpPwdManage/queryById',
      },
    }
  },
  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    },
  },
  created() {
    //如果是流程中表单，则需要加载流程表单data
    this.showFlowData()
  },
  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      this.capitalize = record.capitalize.toString()
      this.lowercase = record.lowercase.toString()
      this.number = record.number.toString()
      this.special = record.special.toString()
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(
          pick(
            this.model,
            'overTime',
            'pwdMin',
            'errorNum',
            'unLock',
            // 'capitalize',
            // 'lowercase',
            // 'number',
            // 'special',
            'updateTimes',
            'stopTime',
            'startTime',
            'startIp',
            'stopIp',
            'mac'
          )
        )
      })
    },
    //渲染流程表单数据
    showFlowData() {
      if (this.formBpm === true) {
        let params = { id: this.formData.dataId }
        getAction(this.url.queryById, params).then((res) => {
          if (res.success) {
            this.edit(res.result)
          }
        })
      }
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let formData = Object.assign(this.model, values)

          formData.capitalize = this.capitalize
          formData.lowercase = this.lowercase
          formData.number = this.number
          formData.special = this.special

          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    popupCallback(row) {
      this.form.setFieldsValue(
        pick(
          row,
          'overTime',
          'pwdMin',
          'errorNum',
          'unLock',
          //'capitalize',
          //'lowercase',
          //'number',
          //'special',
          'updateTimes',
          'stopTime',
          'startTime',
          'startIp', 
          'stopIp',
          'mac'
        )
      )
    },
  },
}
</script>