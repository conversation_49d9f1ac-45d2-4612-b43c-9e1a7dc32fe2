<template>
  <div class='zr-bigscreen-title'>
    <slot>
    <span>{{ title }}</span>
    </slot>
  </div>
</template>
<script>
export default {
  name: 'ZrBigscreenTitle',
  props: {
    title: {
      type: String,
      default: ''
    }
  },
  data() {
    return {}
  },
  mounted() {
  },
  methods: {}
}
</script>


<style scoped lang='less'>
.zr-bigscreen-title {
  width: 100%;
  max-width: 448px;
  height: 51px;
  background-image: url(/zrBigScreen/zrTitleBg01.png);
  background-size: 448px 100%;
  font-family: Source Han Sans CN;
  font-weight: 400;
  font-size: 20px;
  color: #FFFFFF;
  padding-left: 32px;
  //padding-top: 2px;
  display: flex;
  align-items: center;
}
</style>