<template>
  <!-- 端口速率趋势 -->
  <div ref="baseEcharts" style="height: 100%;width: 100%;"></div>
</template>

<script>
import echarts from 'echarts'
import { chartMixins } from './chartMixins'
export default {
  name: 'portAllRate',
  mixins: [chartMixins],
  props: {
    chartData: {
      type: Array,
      default: () => []
    },
    fontSizeObject: {
      type: Object,
        default: function () {
        return {
          legendFontSize: 8,
          xAxisFontSize: 8,
          yAxisFontSize: 10
        }
      }
    }
  },
  watch: {
    chartData: {
      handler(nVal, oVal) {
        this.$nextTick(() => {
          this.initData(nVal)
        })
      },
      deep: true,
      immediate: true
    }
  },
  data() {
    return {
      myChart: null
    }
  },
  methods: {
    initData(data) {
      if (data.length <= 0) {
        return
      }
      let xData = data.map(item => item.time)
      let yData = [] // y轴数据
      let unit = '' // 获取单位
      yData = data.map(item => item.value) // 处理y轴数据
      // 获取单位
      let result = data.find(item=>item.value.unit)
      if (result && result.value && result.value.value) {
        unit = result.value.unit
      }
      // 截取近七日数据
      if (data.length > 6) {
        xData = xData.slice(data.length - 7, data.length)
        yData = yData.slice(data.length - 7, data.length)
      }

      this.myChart = this.$echarts.init(this.$refs.baseEcharts)
      this.myChart.clear()
      let option = {
        grid: {
          top: '18%',
          left: '6%',
          right: '7%',
          bottom: 4,
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            if (params[0].value !== '' && params[0].value !== null && params[0].value !== undefined){
              return `${params[0].name}<br> ${params[0].marker} ${params[0].seriesName}：${params[0].value} ${params[0]
              .data.unit || ''}`
            } else {
               return `${params[0].name}<br> ${params[0].marker} ${params[0].seriesName}：无数据`
            }
          }
        },
        legend: {
          top: 0,
          left: '7%',
          icon: 'rect',
          itemWidth: 14,
          itemHeight: 3,
          textStyle: {
            fontSize: this.fontSizeObject.legendFontSize
          }
        },
        xAxis: {
          type: 'category',
          boundaryGap: true,
          axisLine: {
            show: true,
            lineStyle: {
              color: 'rgba(0,0,0,0.15)'
            }
          },
          axisLabel: {
            textStyle: {
              color: 'rgba(0,0,0,0.45)',
              fontSize: this.fontSizeObject.xAxisFontSize
            }
          },
          axisTick: {
            show: false
          },
          data: xData
        },
        yAxis: {
          type: 'value',
          axisTick: {
            show: false
          },
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed',
              color: 'rgba(0,0,0,0.15)',
              width: 1
            }
          },
          axisLine: {
            show: false
          },
          axisLabel: {
            formatter: '{value}' + unit,
            textStyle: {
              color: 'rgba(0,0,0,0.45)',
              fontSize: this.fontSizeObject.yAxisFontSize
            }
          }
        },
        dataZoom: [
          {
            xAxisIndex: [0],
            show: false, //是否显示滑动条，不影响使用
            start: 0, // 从头开始。
            endValue: 30,
            realtime: true, //是否实时更新
          },
          {
            type: 'inside',
            xAxisIndex: 0,
            zoomOnMouseWheel: false, //滚轮是否触发缩放
            moveOnMouseMove: true, //鼠标滚轮触发滚动
            moveOnMouseWheel: true
          }
        ],
        series: [
          {
            data: yData,
            name: '总速率',
            type: 'line',
            smooth: true,
            showSymbol: false,
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: 'rgba(40, 136, 235,0.8)'
                },
                {
                  offset: 1,
                  color: 'rgba(255, 255, 255, 1)'
                }
              ])
            },
            lineStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 1,
                y2: 0,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(22, 192, 250, 1)' // 0% 处的颜色
                  },
                  {
                    offset: 0.15,
                    color: 'rgba(106, 37, 253, 1)' // 0% 处的颜色
                  },
                  {
                    offset: 0.25,
                    color: 'rgba(203, 35, 127, 1)' // 0% 处的颜色
                  },
                  {
                    offset: 0.75,
                    color: 'rgba(203, 35, 127, 1)' // 0% 处的颜色
                  },
                  {
                    offset: 0.85,
                    color: 'rgba(106, 37, 253, 1)' // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: 'rgba(22, 192, 250, 1)' // 0% 处的颜色
                  }
                ],
                globalCoord: false // 缺省为 false
              }
              // color: '#2888EB'
            },
            itemStyle: {
              // 折线拐点标志的样式
              normal: {
                color: '#2888EB',
                borderWidth: 1
              }
            }
          }
        ]
      }

      this.myChart.setOption(option)
    }
  }
}
</script>

<style scoped lang="less">
</style>