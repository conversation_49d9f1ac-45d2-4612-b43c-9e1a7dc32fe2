<template>
  <div style="height:100%;">
    <keep-alive exclude='ItilOutDepotDetails'>
      <component :is="pageName" :data="data" />
    </keep-alive >
  </div>
</template>
<script>
import ItilOutDepotTable from './ItilOutDepotTable'
import ItilOutDepotDetails from './ItilOutDepotDetails'
export default {
  name: "ItilOutDepotList",
  data() {
    return {
      isActive: 0,
      data:{}
    };
  },
  components: {
    ItilOutDepotTable,
    ItilOutDepotDetails
  },
  created(){
    this.pButton1(0);
  },
  //使用计算属性
  computed: {
    pageName() {
      switch (this.isActive) {
        case 0:
          return "ItilOutDepotTable";
          break;
        default:
          return "ItilOutDepotDetails";
          break;
      }
    }
  },
  methods: {
    pButton1(index) {
      this.isActive = index;
    },
    pButton2(index,item) {
      this.isActive = index;
      this.data = item;
    }
  }
}
</script>