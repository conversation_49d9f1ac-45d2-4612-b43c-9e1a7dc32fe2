<template>
  <div style="height: 100%">
    <a-form :form="form" style="height: 100%" class="vScroll zxw">
      <!-- table区域-begin -->
      <a-row class="div-container">
        <a-col class="colorBox">
          <span class="colorTotal">指标考核</span>
          <span style="text-align: right"
            ><img
              src="~@/assets/return1.png"
              alt=""
              @click="getGo"
              style="width: 20px; height: 20px; cursor: pointer; float: right"
          /></span>
        </a-col>
        <a-col>
          <a-row>
            <a-col :offset="6" :span="12" style="height: 100%">
              <a-col :span="24">
                <a-form-item label="指标项" style="white-space: nowrap">
                  <a-select
                    style="height: 35px"
                    placeholder="请选择指标项"
                    v-decorator="['indexItemId', { rules: [{ required: true, message: '指标项不能为空' }] }]"
                    @change="getType"
                    :allowClear="true"
                    :getPopupContainer="(node) => node.parentNode"
                    show-search
                    option-filter-prop="children"
                    :filter-option="filterOption"
                  >
                    <a-select-option v-for="(item, kk) in indexItemList" :key="kk" :value="item.id">
                      {{ item.title }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item label="  指标描述" style="word-wrap:break-word">
                  <a-table
                    rowKey="id"
                    :showHeader="showHeader"
                    :columns="columns"
                    :pagination="pagination"
                    :data-source="describeData"
                    bordered
                  >
                    <span slot="action" slot-scope="text, record">
                      <a-radio-group
                        name="radioGroup"
                        @change="handleRadio($event, record)"
                        style="display: flex; width: 140px"
                      >
                        <a-radio :value="1" style="color: green"> 完成 </a-radio>
                        <a-radio :value="2" style="color: red"> 未完成 </a-radio>
                      </a-radio-group>
                    </span>
                  </a-table>
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item label="服务时间" style="white-space: nowrap">
                  <a-date-picker
                    style="width: 100%"
                    v-decorator="['serviceTime', validatorRules.time]"
                    :show-time="{ defaultValue: moment('00:00:00', 'HH:mm:ss') }"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item label="服务方式" style="white-space: nowrap">
                  <j-dict-select-tag
                    :trigger-change="true"
                    v-decorator="['serviceMode', { rules: [{ required: true, message: '服务方式不能为空' }] }]"
                    placeholder="请选择服务方式"
                    dictCode="service_mode"
                    :allowClear="true"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item label="备注信息" style="white-space: nowrap">
                  <a-textarea
                    v-decorator="['remarks', { rules: [{ required: true, message: '备注信息不能为空' }] }]"
                    rows="4"
                    placeholder="请输入备注信息"
                  />
                </a-form-item>
              </a-col>
            </a-col>
          </a-row>
        </a-col>
        <a-col class="colorBox">
          <span class="colorTotal">附件材料</span>
        </a-col>
        <a-col>
          <a-row>
            <a-col style="width: 50%; height: 100%; margin: 0 auto">
              <a-col :span="24" style="position: relative">
                <a-form-item label="上传附件" style="white-space: nowrap">
                  <j-upload v-decorator="['fileId']" :number="1"></j-upload>
                </a-form-item>
                <div style="position: absolute; bottom: -11px; white-space: nowrap">不限制文件格式、不限制文件大小</div>
              </a-col>
            </a-col>
          </a-row>
        </a-col>
        <a-col style="padding: 88px; text-align: center">
          <el-button @click="handleOk" type="primary" style="height: 32px; line-height: 0px" plain>提 交</el-button>
          <el-button @click="getGo" plain style="margin-left: 30px; height: 32px; line-height: 0px">取 消</el-button>
        </a-col>
      </a-row>
    </a-form>
  </div>
</template>

<script>
import pick from 'lodash.pick'
import { postAction, getAction, deleteAction } from '@/api/manage'
import JUpload from '@/components/jeecg/JUpload'
import moment from 'moment'
export default {
  name: 'fillReport',
  components: {
    JUpload,
  },
  props: {
    dataDetail: {
      type: Object,
    },
  },

  data() {
    return {
      form: this.$form.createForm(this),
      title: '操作',
      showHeader: false,
      pagination: false,
      parentId: '',
      dictCodes: '',
      projectID: '',
      indexItemList: [],
      // labelCol: {
      //   xs: { span: 12 },
      //   sm: { span: 2 },
      // },
      wrapperCol: {
        xs: { span: 12 },
        sm: { span: 20 },
      },
      validatorRules: {
        name: {
          rules: [{ required: true, message: '请输入报告名称' }],
        },
        time: {
          rules: [{ required: true, message: '时间范围不能为空' }],
        },
        project: {
          rules: [{ required: true, message: '请选择项目' }],
        },
        supplier: {
          rules: [{ required: true, message: '请选择供应商' }],
        },
      },
      columns: [
        {
          title: '描述',
          dataIndex: 'detail',
        },
        {
          title: '操作',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' },
        },
      ],

      data: [
        {
          key: '1',
          detail: '检查设备运行情况',
          address: 'New York No. 1 Lake Park',
          delflag: '1',
        },
        {
          key: '2',
          detail: '收集硬件系统最新运行信息',
          address: 'London No. 1 Lake Park',
          delflag: '1',
        },
        {
          key: '3',
          detail: '核查硬件系统运行环境，包括机房温度、湿度和零地电压等',
          address: 'Sidney No. 1 Lake Park',
          delflag: '1',
        },
      ],
      fileId: '',
      url: {
        add: 'reportdetails/itilReportDetailsInfo/add',
        itilSyTargetDetail: '/itilSyTargetDetail/itilSyTargetDetail/list',
        queryItemList: '/itilSyTarget/itilSyTarget/indexItemList',
      },
      describeData: [],
      arrData: '',
    }
  },
  mounted() {
    this.projectID = this.dataDetail.projectId
    this.form.resetFields()
    this.model = Object.assign({})
    this.$nextTick(() => {
      this.form.setFieldsValue(
        pick(this.model, 'indexItemId', 'serviceMode', 'indexDescripId', 'fileId', 'serviceTime', 'remarks')
      )
    })

    this.parentId = this.dataDetail.id
    // this.dictCodes="itil_sy_target,title,id,project_id='"+this.dataDetail.projectId+"'";

    let params = {
      projectId: this.projectID,
    }
    getAction(this.url.queryItemList, params).then((res) => {
      if (res.success) {
        this.indexItemList = res.result
      } else {
        this.$message.warning(res.message)
      }
    })
  },
  watch: {
    dataDetail: function (val, oldVal) {
      this.parentId = val.id
    },
  },
  methods: {
    filterOption(input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
    },
    moment,
    range(start, end) {
      const result = []
      for (let i = start; i < end; i++) {
        result.push(i)
      }
      return result
    },
    handleRadio(e, ind) {
      this.arrData = ''
      this.describeData.forEach((item) => {
        if (ind.id == item.id) {
          item.flag = e.target.value
        }
        if (item.flag == 1 || 2) {
          this.arrData += item.id + ','
        }
      })
    },
    //返回上一级
    getGo() {
      this.$parent.pButton3(0)
    },
    getType(value) {
      let that = this
      if (value != undefined) {
        getAction(this.url.itilSyTargetDetail, { targetId: value }).then((res) => {
          if (res.success) {
            that.describeData = res.result.records || res.result
            this.$nextTick(() => {
              this.$forceUpdate()
            })
          }
          if (res.code === 510) {
            that.$message.warning(res.message)
          }
        })
      } else {
        that.describeData = []
      }
    },
    handleOk() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          let formData = Object.assign(this.model, values)
          let dt = formData.serviceTime._d

          if (that.arrData == '') {
            this.$message.info('请选择完成情况')
            return false
          }

          let params = {
            indexItemId: formData.indexItemId,
            serviceMode: formData.serviceMode,
            indexDescripId: that.arrData,
            fileId: formData.fileId,
            remarks: formData.remarks,
            serviceTime: formData.serviceTime.format('YYYY-MM-DD HH:mm:ss'),
            reportId: that.parentId,
          }

          postAction(that.url.add, params)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                this.form.resetFields()
                this.$parent.pButton3(0)
              } else {
                that.$message.warning(res.message)
                this.form.resetFields()
              }
            })
            .finally(() => {
              this.form.resetFields()
            })
        }
      })
    },
    rangeChange() {},
  },
}
</script>

<style lang="less">
@import '~@assets/less/scroll.less';
</style>
<style  scoped>
.colorBox {
  padding: 18px 26px;
}
.colorTotal {
  font-family: PingFangSC-Medium;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.85);
  padding-left: 7px;
  border-left: 4px solid #1e3674;
  font-weight: 700;
}
.form-row {
  display: flex;
  margin: 0px 0px !important;
  align-items: center;
  height: 69px;
  background-color: white;
}
.fontStyle1 {
  font-family: 'Font Awesome 5 Pro Solid', 'Font Awesome 5 Pro Regular', 'Font Awesome 5 Pro', sans-serif;
  font-weight: 900;
  font-style: normal;
  font-size: 16px;
  text-align: left;
  padding-left: 16px;
}
.fontStyle2 {
  font-family: Microsoft YaHei, sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 14px;
  text-align: right;
  padding-right: 16px;
}
.div-container {
  width: 100%;
  background-color: #ffffff;
  padding-bottom: 6px;
  position: relative;
}
::v-deep .ant-empty-normal {
  margin: 22px 0 0 0;
}
::v-deep .ant-col-sm-2 {
  width: 11%;
}
</style>