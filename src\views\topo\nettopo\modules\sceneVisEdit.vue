<template>
  <div class="wrap">
    <div id="nodeDel" style="display: none; border-radius: 5px; position: absolute; z-index: 100">
      <div>
        <a-button @click="nodeDel">删除</a-button>
      </div>
    </div>
    <!-- 左侧工具栏 -->
    <div class="dataTree" v-if="this.operate != 'show'">
      <a-input placeholder="输入关键字进行过滤" v-model="filterText"></a-input>
      <vue-easy-tree ref="veTree" node-key="id" :data="treeList" :props="replaceFields" height="100%"
                     :filter-node-method="filterNode">
        <div class="custom-tree-node" slot-scope="{ node, data }">
          <div :draggable="draggable" v-if="data.hasChild !== true" @mousedown="startDrag(node.label, $event, data)">
            <img v-if="data.icon != null && data.icon.length > 0" :src="picSrc + '/' + data.icon"
                 style="width: 20px; height: 20px" />
            <span>{{ node.label }}</span>
          </div>
          <div v-else>
            <span>{{ node.label }}</span>
          </div>
        </div>
      </vue-easy-tree>
    </div>
    <!-- 流程画板 -->
    <div class="panel" :style="{ width: operate === 'create' ? 'calc(100% - 580px)' : '100%' }">
      <div id="layoutContainer" ref="container" class="graphcontainer" style="flex: 1; width: 100% !important"></div>
    </div>
    <!--右侧工具栏-->
    <div class="config" v-if="this.operate != 'show'">
      <a-row>
        <div style="font-size: 18px">场景信息</div>
        <a-col :span="24">
          <a-form-item label="场景名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input placeholder="请输入场景名称" v-model="sceneName" :maxLength="20"></a-input>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="场景描述" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input placeholder="请输入场景描述" v-model="description" :maxLength="50"></a-input>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label='产品名称' :labelCol="labelCol" :wrapperCol="wrapperCol" :required="true">
            <a-cascader v-model='productId'
                        :field-names="{ label: 'label', value: 'id',text:'code',leaf:'isLeaf', children: 'items' }"
                        :load-data='loadData' :multiple='true' :options='productOptions' change-on-select placeholder='请选择产品名称'
                        @change='onChangeProduct' style="width:100%" @blur="selectBlur">
              <template slot='displayRender' slot-scope='{labels, selectedOptions}'>
                {{ productDisplayRender(selectedOptions) }}
              </template>
            </a-cascader>
          </a-form-item>
        </a-col>
        <a-col :span='24'>
          <a-form-item label='设备名称' :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-select :getPopupContainer='(node) => node.parentNode' v-model='deviceId' :allow-clear='true'
                      :maxTagCount='1' :show-search='true' mode='multiple' option-filter-prop='label' placeholder='请选择设备名称'
                      style="width:100%">
              <a-select-option v-for='item in deviceList' :key='item.id' :label='item.code' :value='item.id'>
                {{ item.code }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script>
import VueEasyTree from '@wchbrad/vue-easy-tree'
import {
  deleteAction,
  getAction,
  httpAction,
  postAction
} from '@/api/manage'
import FlowGraph from '@views/topo/nettopo/modules/graph/chainIndex'
import yqIcon from '@/components/tools/SvgIcon'
import store from '@/store/'
import {
  Graph,
  Color,
  Dom,
  Shape,
  Edge,
  NodeView,
  Addon,
  DataUri,
  Vector
} from '@antv/x6'
import uuidv1 from 'uuid/v1'
import {
  message
} from 'ant-design-vue'
import computed from '@/assets/cmpIcon/computed.svg'
import WebsocketMessageMixin from '@/mixins/WebsocketMessageMixin'
const {
  Dnd
} = Addon
export default {
  name: 'processEdit',
  components: {
    yqIcon,
    VueEasyTree,
    uuidv1,
    computed,
  },
  mixins: [WebsocketMessageMixin],
  props: {
    operate: {
      type: String,
      default: 'create',
    }, //当前页面为查看、编辑：show、create
  },
  data() {
    return {
      produIds: [],
      apiUrl: window._CONFIG['domianURL'],
      description: '',
      sceneName: '',
      id: '',
      productOptions: [],
      connectObj: [],
      deviceId: [],
      sceneData: [],
      deviceList: [],
      lastProductType: '', //产品名称所选的最后一级数据类型（category/product）
      lastProductId: '', //产品名称所选的最后一级数据id
      productId: [],
      formItemLayout: {
        md: {
          span: 12,
        },
        sm: {
          span: 24,
        },
      },
      picSrc: window._CONFIG['staticDomainURL'],
      draggable: true,
      filterText: '',
      dnd: Object,
      treeList: [],
      replaceFields: {
        label: 'name',
        key: 'id',
      },
      labelCol: {
        xs: {
          span: 24,
        },
        sm: {
          span: 6,
        },
      },
      wrapperCol: {
        xs: {
          span: 24,
        },
        sm: {
          span: 16,
        },
      },
      url: {
        add: '/autoControl/scene/add',
        edit: '/autoControl/scene/edit',
        tree: '/autoControl/scene/queryScriptTree',
        assets: '/sys/notice/template/assets', //获取产品第一级数据
        productSublevel: '/sys/notice/template/productList', //根据当前所选产品分类id，获取该级下的子级数据
        devList: '/sys/notice/template/devList', //根据产品id获取设备名称下拉数据
        queryById: '/autoControl/scene/queryById'
      },
    }
  },
  watch: {
    filterText(val) {
      this.$refs.veTree.filter(val)
    },
  },
  mounted() {
    this.getTree()
    this.getRootProductCategory()
  },
  methods: {
    getTree() {
      const generateSlotScopes = (data) => {
        for (let i = 0; i < data.length; i++) {
          if (data[i].id == null || data[i].id == '') {
            data[i].id = i
            data[i].children = data[i].child
          }
          if (data[i].scriptName != null) {
            data[i].name = data[i].scriptName
          }
          if (data[i].children) {
            generateSlotScopes(data[i].children)
          }
        }
      }
      getAction(this.url.tree).then((res) => {
        generateSlotScopes(res.result)
        this.treeList = res.result
      })
    },
    /*异步加载产品名称分支数据*/
    async loadData(selectedOptions) {
      let that = this
      return new Promise(function (resolve, reject) {
        const targetOption = selectedOptions[selectedOptions.length - 1]
        if (targetOption.type == 'category') {
          targetOption.loading = true
          that.getProductSublevelData(targetOption).then((res) => {
            if (res.success) {
              resolve(res)
            } else {
              that.$message.warning('发生异常，请稍后重试')
              reject(res)
            }
            targetOption.loading = false
          })
        } else {
          targetOption.loading = false
          targetOption.isLeaf = true
          targetOption.items = undefined
          resolve({
            data: targetOption,
            success: true,
            message: '操作成功'
          })
        }
      })
    },
    /*实现产品、设备数据回显*/
    getProductAndDeviceList(proIds) {
      let that = this
      that.setProductIds(proIds)
      //根据最后一项产品id获取设备下拉数据
      that.getDeviceList(that.lastProductId)
      if (!that.deviceId || that.deviceId.length == 0) {
        that.deviceId = []
      }
    },
    /* 根据当前所选产品名称id，获取该级下的子级数据*/
    getProductSublevelData(targetOption) {
      let that = this
      return new Promise(function (resolve, reject) {
        getAction(that.url.productSublevel, {
          assetsId: targetOption.id
        }).then((res) => {
          if (res.success) {
            if (res.result.length > 0) {
              targetOption.items = res.result.map((item) => {
                let param = {
                  id: item.id,
                  isLeaf: item.type == 'category' ? false : true,
                  label: that.getIconName(item.type, item.code),
                  loading: false,
                  items: undefined,
                  code: item.code,
                  type: item.type
                }
                return param
              })
            } else {
              targetOption.items = undefined
              targetOption.isLeaf = true
            }
            targetOption.loading = false
            resolve({
              data: targetOption,
              success: true,
              message: '操作成功'
            })
          }
        }).catch(() => {
          targetOption.loading = false
          reject({
            data: targetOption,
            success: false,
            message: '操作失败'
          })
        })
      })
    },
    /*解析产品名称数据，实现产品数据回显*/
    setProductIds(proIds) {
      let that = this
      if (proIds && proIds.length > 0) {
        let ids = proIds.split(',')
        that.productId = ids
        that.lastProductId = ids[ids.length - 1] //和proId是一样的
        that.lastProductType = 'product'
        //处理产品名称回显
        that.getProductName(ids, that.productOptions)
      }
    },
    /*编辑：根据产品级联id数组，获取对应分支的子级数据，用于回显产品名称*/
    getProductName(nodeIdsArr, items) {
      let that = this
      let node = null
      if (items && items.length > 0) {
        node = items.filter((item) => {
          if (item.id == nodeIdsArr[0]) {
            return item
          }
        })
      }
      if (node && node.length > 0) {
        that.loadData(node).then((res) => {
          if (res.success) {
            node.items = res.data.items
            if (node.items) {
              that.getProductName(nodeIdsArr.slice(1), node.items)
            }
          }
        })
      }
    },
    /*获取产品名称第一级数据*/
    getRootProductCategory() {
      let that = this
      return new Promise(function (resolve, reject) {
        getAction(that.url.assets).then((res) => {
          if (res.success) {
            res.result.map((item) => {
              item.label = that.getIconName(item.type, item.code)
              item.isLeaf = false
              item.items = item.items && item.items.length > 0 ? item.items : undefined
              return item
            })
            resolve({
              success: true,
              data: res.result,
              message: res.message
            })
          } else {
            reject({
              success: false,
              data: undefined,
              message: res.message
            })
          }
        }).catch((res) => {
          reject({
            success: false,
            data: undefined,
            message: res.message
          })
        })
      })
    },
    /*根据产品名称下拉数据每条类型，设置前置图标，用于区分产品分类和产品*/
    getIconName(type, code) {
      let label = ''
      switch (type) {
        case 'category':
          label = <div style='display: inline-block;white-space: nowrap;'>
            <a-icon type='folder' style='color:#409eff;padding-right:5px'/>
            {code}</div>
          break
        case 'product':
          label = <div style='display: inline-block;white-space: nowrap'>
            <a-icon type='file' style='color:#409eff;margin-right:5px'/>
            {code}</div>
          break
      }
      return label
    },
    selectBlur() {
      if (this.produIds.length == 1) {
        this.$message.warning('请勿选择产品分类，请选择产品!')
      }
    },
    onChangeProduct(value, nodeData) {
      this.produIds = nodeData
      if (nodeData && nodeData.length > 0) {
        this.lastProductType = nodeData[nodeData.length - 1].type
        let proId = nodeData[nodeData.length - 1].id
        if (this.lastProductType == 'product') {
          //最新产品id和上次选择产品id不同时，重新加载数据
          if (proId != this.lastProductId) {
            //产品选择发生变化
            //1、重新获取设备名称下拉数据
            this.resertDeviceName()
            this.getDeviceList(proId)
          }
        } else {
          this.resertDeviceName()
        }
        this.lastProductId = proId
      } else {
        this.lastProductType = ''
        this.lastProductId = ''
        this.resertDeviceName()
      }
    },
    /*根据产品id，获取设备下拉数据*/
    getDeviceList(productId) {
      let that = this
      getAction(that.url.devList, {
        productId: productId.toString()
      }).then((res) => {
        if (res.success) {
          that.deviceList = res.result
        }
      })
    },
    /* 改变产品名称，联动改变设备名称*/
    resertDeviceName() {
      this.deviceList = []
      this.deviceId = undefined
    },
    productDisplayRender(selectedOptions) {
      let options = selectedOptions.map((item) => {
        return item.code
      })
      return options.length > 0 ? options.join('/') : undefined
    },
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    show(id) {
      getAction(this.url.queryById, {
        id: id
      }).then((res) => {
        if (res.success) {
          if (res.result) {
            let record = res.result
            this.id = record.id
            if(record.deviceId && record.deviceId.length > 0){
              this.deviceId = record.deviceId.split(',')
            }
            if (record.productId && record.productId.length > 0) {
              this.productId = record.productId.split(',')
              this.getProductAndDeviceList(record.productId)
            }
            if (record.sceneData && record.sceneData != '') {
              this.sceneData = record.sceneData.split(',')
            }
            if (record.connectObj && record.connectObj.length > 0) {
              this.connectObj = record.connectObj.split(',')
            }
          }
        }else {
          this.$message.warning(res.message)
        }
      }).catch((err)=>{
        this.$message.warning(err.message)
      })
    },
    create(record) {
      let that = this
      this.$nextTick(() => {
        that.confirmLoading = true
        Promise.all([
          //获取产品一级数据
          that.getRootProductCategory(),
        ]).then((result) => {
          let proResult = result.every((item, index) => {
            if (index == 0) {
              that.productOptions = item.data
            }
            return item.success == true
          })
          if (proResult) {
            that.getProductAndDeviceList(record.productId)
          }
        })
      })
      if (record && record.id != null) {
        this.id = record.id
        this.show(record.id)
      }
      this.description = record.description
      this.sceneName = record.sceneName
      this.$store.commit('CLEAR_DATA_LIST')
      if (this.graph) {
        this.graph.dispose()
      }
      this.$nextTick(() => {
        this.initGraph('layoutContainer')
        if (
          record.topoDataJson != null &&
          record.topoDataJson !== ''
        ) {
          let topoObj = JSON.parse(record.topoDataJson)
          let xlink = 'xlink:href'
          topoObj.cells.forEach((item, i) => {
            if (topoObj.cells[i].attrs && topoObj.cells[i].attrs.image && topoObj.cells[i].attrs.image[
              xlink]) {
              topoObj.cells[i].attrs.image[xlink] = this.apiUrl + '/' + topoObj.cells[i].attrs.image[xlink]
            }
          })
          this.graph.fromJSON(topoObj)
          let nodes = this.graph.getNodes().filter((ele) => ele.shape === 'switch-node1')
          let nodeIds = []
          if (nodes.length > 0) {
            nodes.forEach((e) => nodeIds.push(e.data.id))
          }
        }
        this.visible = true
        //流程在画布中居中
        this.graph.centerContent()
      })
    },
    initGraph(container) {
      this.graph = FlowGraph.init(container)
      this.dnd = new Dnd({
        target: this.graph,
        scaled: false,
        animation: true,
        validateNode(droppingNode, options) {
          return true
        },
      })
      this.setup()
    },
    // 监听画布节点、边自定义事件
    setup() {
      this.graph.on('edge:connected', ({ isNew, edge }) => {
        if (isNew) {
          let source = edge.getSourceCell() //获取源节点信息
          let target = edge.getTargetCell() //获取目标节点信息
          let sourceId = source.store.data.data.id + '' + source.store.data.id
          let targetId = target.store.data.data.id + '' + target.store.data.id
          if (this.connectObj.length == 0) {
            this.connectObj.push(sourceId)
            this.connectObj.push(targetId)
          } else if (this.connectObj.length == 2) {
            if ((sourceId == this.connectObj[0] && targetId == this.connectObj[1]) ||
              (sourceId == this.connectObj[1] && targetId == this.connectObj[0]) ||
              (sourceId == this.connectObj[0] && targetId == this.connectObj[0]) ||
              (sourceId == this.connectObj[1] && targetId == this.connectObj[1])) {
              // 判断闭环连接
              this.$message.warning('请勿闭环连接')
              this.graph.removeEdge(edge)
              return
            } else if (sourceId == this.connectObj[1]) {
              this.connectObj.push(targetId)
            } else if (targetId == this.connectObj[0]) {
              this.connectObj.unshift(sourceId)
            }
          } else {
            let objJson = this.connectObj.join(',')
            if (sourceId == this.connectObj[this.connectObj.length - 1] && !objJson.includes(targetId)) {
              this.connectObj.push(targetId)
            } else if (targetId == this.connectObj[0] && !objJson.includes(sourceId)) {
              this.connectObj.unshift(sourceId)
            } else {
              this.$message.warning('串联连接，请勿闭环或并联')
              this.graph.removeEdge(edge)
              return
            }
          }
        }
      })
      //鼠标左键键单击事件
      this.graph.on('node:click', ({ e, x, y, node, view }) => {
        document.getElementById('nodeDel').style.display = 'block'
        document.getElementById('nodeDel').style.top = e.clientY - 50 + 'px'
        document.getElementById('nodeDel').style.left = e.clientX - 60 + 'px'
        document.getElementById('nodeDel').style.zIndex = 100
      })
      //鼠标单击空白处事件
      this.graph.on('blank:click', ({}) => {
        document.getElementById('nodeDel').style.display = 'none'
      })
    },
    // tree拖拽节点
    async startDrag(name, e, data) {
      if(data.children && data.children.length > 0)
      {
        return
      }
      //创建子拓扑节点
      let node = this.graph.createNode({
        shape: 'son-topo-node1',
        data: {
          code: data.key,
          id: data.id,
          name: name,
        },
        attrs: {
          label: {
            text: name,
          },
          image: {
            'xlink:href': this.picSrc + '/' + data.icon,
          },
        },
      })
      this.sceneData = (node.store.data.data.id)
      this.dnd.start(node, e)
    },
    checkLine(data){
      let isPass=true
      if(data.cells&&data.cells.length>1){
        let arr=data.cells
        let line=arr.filter(item=>item.shape&&item.shape==='edge')
        let liLength=line.length
        let total=data.cells.length
        if (liLength===0||(liLength*2+1)<total){isPass=false}
      }
      return isPass
    },
    save() {
      let connectData = []
      let topoJson = this.graph.toJSON()
     if(!this.checkLine(topoJson)) {
       this.$message.warning('有未串联的节点，请检查连线')
       return
     }

      let params = {}
      params.description = this.description
      params.sceneName = this.sceneName
      params.id = this.id
      params.deviceId = this.deviceId
      params.productId = this.productId
      if (this.connectObj.length == 0) {
        connectData = this.sceneData
      }
      else {
        this.connectObj.forEach((ele) => {
          connectData.push(ele.substring(0,19))
        })
      }
      params.sceneData = (connectData && Array.isArray(connectData) && connectData.length>1)? connectData.join(',') :
      connectData
      let xlink = 'xlink:href'
      topoJson.cells.forEach((item, i) => {
        if (topoJson.cells[i].position && topoJson.cells[i].position != null) {
          let images = topoJson.cells[i].attrs.image[xlink].split('/')
          images.forEach((ele, index) => {
            if (images[index] == 'sys') {
              images.splice(0, index)
              topoJson.cells[i].attrs.image[xlink] = images.join('/')
            }
          })
        }
      })
      let container = this.$refs.container.clientWidth
      topoJson.screenSize = container
      params.topoDataJson = JSON.stringify(topoJson)
      this.graph.toSVG((dataUri) => {
        params.topoSvg = dataUri
      })
      this.tipsBeforeSubmission(params)
    },
    tipsBeforeSubmission(mainData) {
      let that = this
      let devList = mainData.deviceId
      mainData.productId = that.lastProductId
      that.submitData(mainData)
    },
    /*提交数据*/
    submitData(mainData) {
      let that = this
      that.confirmLoading = true
      let httpurl = ''
      let method = ''
      httpurl += that.url.edit
      method = 'put'
      let formData = {
        ...mainData
      }
      formData.sceneData = formData.sceneData.length == 0 ? '' : formData.sceneData.length == 1 ? formData.sceneData.toString() : formData.sceneData
      if (formData.productId == null || formData.productId == '') {
        this.$message.warning('请选择产品')
        return
      }
      formData.connectObj = this.connectObj.join(',')
      if (formData.deviceId && formData.deviceId.length > 0) {
        formData.deviceId = formData.deviceId.join()
      } else if (formData.deviceId && formData.deviceId.length > 1) {
        formData.deviceId = formData.deviceId.join(',')
      } else {
        formData.deviceId = ''
      }
      httpAction(httpurl, formData, method)
        .then((res) => {
          if (res.success) {
            that.$message.success(res.message)
            that.$emit('ok')
          } else {
            that.$message.warning(res.message)
          }
          that.confirmLoading = false
        }).catch((res) => {
        that.$message.warning(res.message)
        that.confirmLoading = false
      })
    },
    nodeDel() {
      const {
        graph
      } = FlowGraph
      const cells = graph.getSelectedCells()
      if (cells.length) {
        graph.cut(cells)
      }
      document.getElementById('nodeDel').style.display = 'none'
      return false
    },
  },
}
</script>

<style lang="less" scoped>
.graphcontainer {
  height: 100% !important;
  width: 100% !important;
  flex: 1;
}

.wrap {
  height: 100% !important;
  display: flex;

  .dataTree {
    height: 100%;
    margin-right: 16px;
    border: 1px solid rgba(0, 0, 0, 0.08) !important;
    margin-right: 10px;
  }
}

.panel {
  width: calc(100% - 550px);
  height: 100%;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  background: linear-gradient(to bottom, rgb(193, 233, 241), rgb(204, 239, 245));
}

.config {
  width: 320px;
  margin-left: 16px;
}
</style>