<template>
  <a-modal
    :title="title"
    :width="modalWidth"
    :visible="visible"
    :centered="true"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭"
    wrapClassName="ant-modal-cust-warp"
    style="top: 5%; height: 95%; overflow: auto"
  >
    <a-tabs :animated="false" :activeKey="defaultActiveKey" @change="tabClick">
      <!-- @click="onIndex(index)" -->
      <a-tab-pane key="1" tab="基本信息">
        <table1 ref="table1"></table1>
      </a-tab-pane>
      <a-tab-pane key="2" tab="流程跟踪">
        <a-row style="position: relative">
          <img :src="imgUrl" />
          <a-spin size="large" fix v-if="loadingImg"></a-spin>
        </a-row>
      </a-tab-pane>
      <a-tab-pane key="3" tab="历史记录">
        <table2 ref="table2"></table2>
      </a-tab-pane>
      <a-tab-pane key="4" tab="关联配置项">
        <table3 ref="table3"></table3>
      </a-tab-pane>
      <a-tab-pane key="8" tab="关联事件">
        <table7 ref="table7"></table7>
      </a-tab-pane>
      <a-tab-pane key="5" tab="关联问题">
        <table4 ref="table4"></table4>
      </a-tab-pane>
      <a-tab-pane key="6" tab="关联变更">
        <table5 ref="table5"></table5>
      </a-tab-pane>
      <a-tab-pane key="9" tab="关联发布">
        <table8 ref="table8"></table8>
      </a-tab-pane>
      <!-- <a-tab-pane key="7" tab="处理结果">
        <table6></table6>
      </a-tab-pane> -->
    </a-tabs>
    <template slot="footer">
      <a-button @click="handleCancel">关闭</a-button>
    </template>
  </a-modal>
</template>
<script>
import table1 from '../details/table1'
import table2 from '../details/table2'
import table3 from '../details/table3'
import table4 from '../details/table4'
import table5 from '../details/table5'
import table6 from '../details/table6'
import table7 from '../details/table7'
import table8 from '../details/table8'

import pick from 'lodash.pick'
export default {
  name: 'itilConfigProcessDetails',
  components: {
    table1,
    table2,
    table3,
    table4,
    table5,
    table6,
    table7,
    table8,
  },
  data() {
    return {
      title: '操作',
      confirmLoading: false,
      defaultActiveKey: '1',
      /* 弹框宽 */
      modalWidth: '1200px',
      form: this.$form.createForm(this),
      visible: false,
      returnValue: {},
      returnValue1: {},
      loadingImg: false,
      imgUrl: '',
      url: {
        getHighlightImg: window._CONFIG['domianURL'] + `/activiti/models/getHighlightImg/`,
      },
    }
  },
  methods: {
    edit(record) {
      this.defaultActiveKey = '1'
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      this.returnValue = record
      setTimeout(() => {
        this.$refs.table1.getData(record.busId)
      }, 5)
      this.getHighlightImg(record.procInstId)
    },
    getHighlightImg(procInstId) {
      this.id = procInstId
      this.imgUrl = this.url.getHighlightImg + this.id + '?time=' + new Date()
    },
    // 关闭弹框
    close() {
      this.$emit('close')
      this.visible = false
      this.current = 0
    },
    // 提交
    handleOk() {},
    handleCancel() {
      this.close()
    },
    // tab
    tabClick(key) {
      if (key == '1') {
        this.defaultActiveKey = '1'
        setTimeout(() => {
          this.$refs.table1.getData(this.returnValue.busId)
        }, 5)
      } else if (key == '2') {
        this.defaultActiveKey = '2'
      } else if (key == '3') {
        this.defaultActiveKey = '3'
        setTimeout(() => {
          this.$refs.table2.getDataList(this.returnValue.procInstId)
        }, 5)
      } else if (key == '4') {
        this.defaultActiveKey = '4'
        setTimeout(() => {
          this.$refs.table3.getDataList(this.returnValue.busId)
        }, 5)
      } else if (key == '5') {
        this.defaultActiveKey = '5'
        setTimeout(() => {
          this.$refs.table4.getDataList(this.returnValue.busId)
        }, 5)
      } else if (key == '6') {
        this.defaultActiveKey = '6'
        setTimeout(() => {
          this.$refs.table5.getDataList(this.returnValue.busId)
        }, 5)
      } else if (key == '8') {
        this.defaultActiveKey = '8'
        setTimeout(() => {
          this.$refs.table7.getDataList(this.returnValue.busId)
        }, 5)
      } else if (key == '9') {
        this.defaultActiveKey = '9'
        setTimeout(() => {
          this.$refs.table8.getDataList(this.returnValue.busId)
        }, 5)
      }
    },
  },
}
</script>
<style lang="less" scoped>
::v-deep .ant-modal-body {
  padding: 24px 48px 24px 48px;
}
::v-deep .ant-modal {
  padding: 24px;
}
@media (max-width: 1212px) {
  ::v-deep .ant-modal {
    max-width: calc(100vw - 12px);
    margin: 0;
  }
}
.j-modal-box.fullscreen {
  margin: 0 !important;
  max-width: 100vw !important;
  ::v-deep .ant-modal {
    max-width: 100vw !important;
    margin: 0;
  }
}
</style>
