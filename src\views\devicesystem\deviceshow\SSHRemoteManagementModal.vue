<template>
  <j-modal
    :title="title"
    :width="width"
    :centered="true"
    :visible="visible"
    :switchFullscreen="false"
    :fullscreen="true"
    :footer="null"
    @ok="handleOk"
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @cancel="handleCancel"
    cancelText="关闭">
    <a-spin :spinning="confirmLoading" wrapperClassName="custom-ant-spin">
<!--      <div v-if="!connectionParams">正在获取连接信息...</div>-->
      <div style="height: 100%;width: 100%" id="ssh-terminal-container" ref="sshTerminalContainer"></div>
    </a-spin>
  </j-modal>
</template>

<script>
import { getAction } from '@api/manage'

export default {
  name: 'SSHRemoteManagementModal',
  components: {},
  data() {
    return {
      title: '',
      width: 800,
      visible: false,
      disableSubmit: false,
      confirmLoading: false,
      deviceInfo:null,
      terminalContainer:null,
      connectionParams:null,
      client:null,
      term:null,
      currentCommand:'',
      resizeTimeout:null,
      url: {
        queryById: '/device/deviceInfo/queryConnectInfo',
      },
    }
  },
  mounted() {
   // console.log('window===',window)
  },
  methods: {
    getConnectionParams(deviceId) {
      return new Promise((resolve, reject) => {
        getAction(this.url.queryById, { id: deviceId }).then((res) => {
          if (res.success && res.result) {
            this.connectionParams = {
              operate: 'connect',
              host: res.result.ip,//IP
              port: res.result.port,//端口号
              username: res.result.sshUsername,//用户名
              password: res.result.sshPassword//密码
            }
            resolve(true)
          } else {
            this.connectionParams = null
            this.$message.warning('获取连接信息失败')
            reject(false)
          }
        }).catch((err) => {
          this.$message.warning(err.message)
          reject(false)
        })
      })
    },
    add() {
      this.visible = true
      this.$nextTick(() => {

      })
    },
    edit(record) {
      this.visible = true
      this.confirmLoading=true
      this.deviceInfo=record
      this.getConnectionParams(record.id).then((res)=>{
        if (res){
          this.$nextTick(() => {
            this.terminalContainer = this.$refs.sshTerminalContainer
            this.$nextTick(() => {
              this.initTerminal()
            })
          })
        }
      })
    },
    close() {
      this.$emit('close');
      this.visible = false;
      if (this.client) {
        this.disconnect()
      }
    },
    handleOk() {},
    submitCallback() {
      this.$emit('ok');
      this.visible = false;
    },
    handleCancel() {
      this.close()
    },
    initTerminal() {
      if (!this.terminalContainer) return
      this.terminalContainer.innerHTML = '';

      if (!this.term) {
        this.term = new window.Terminal({
          cursorBlink: true,
          cursorStyle: 'block',
          scrollback: 800,//回滚
          tabStopWidth: 8,
          screenKeys: true
        })

        this.term.onData(key => {
          if (key === '\r' || key === '\n') {
            if (this.client) {
              this.client.sendEnterData(this.currentCommand)
              this.currentCommand = ''
            }
          } else {
            if (this.client) {
              this.client.sendClientData(key)
            }
          }
        })

        this.term.open(this.terminalContainer)

        this.setupWindowResizeListener()
        this.handleWindowResize()
      }
      this.connect()
    },
    setupWindowResizeListener() {
      // 添加新的监听器
      window.addEventListener('resize', this.handleWindowResize)
    },
    handleWindowResize(time=200) {
      //使用防抖避免频繁调整
      clearTimeout(this.resizeTimeout)
      this.resizeTimeout = setTimeout(() => {
        this.adjustTerminalSize()
      }, time)
    },
    adjustTerminalSize() {
      if (!this.term || !this.terminalContainer) return
      // 获取终端容器的实际可用尺寸
      // const containerWidth = this.terminalContainer.clientWidth
      // const containerHeight = this.terminalContainer.clientHeight
      //console.log('containerWidth===',containerWidth)
      //console.log('containerHeight===',containerHeight)

      const container = this.terminalContainer.getBoundingClientRect()
      const helperEl = document.querySelector('.xterm-char-measure-element')
      if (helperEl) {
        const charWidth = helperEl.getBoundingClientRect().width
        const charHeight = helperEl.getBoundingClientRect().height
        //console.log('charWidth===',charWidth)
        //console.log('charHeight===',charHeight)
        //console.log('container.width===',container.width)
        //console.log('container.height===',container.height)
        // 计算适合的列数和行数（保留一些边距）
        /*const cols = Math.max(10, Math.floor((containerWidth - 20) / charWidth))
        const rows = Math.max(5, Math.floor((containerHeight - 20) / charHeight))*/
        const cols = Math.max(10, Math.floor((container.width - 12) / charWidth))
        const rows = Math.max(5, Math.floor((container.height) / charHeight))
        //console.log('cols===',cols)
        //console.log('rows===',rows)
        this.term.resize(cols, rows)
      }
    },
    connect() {
      this.term.write('Connecting...\r\n')
      //设备信息
      //console.log('this.deviceInfo===',this.deviceInfo)
      this.client = new window.WSSHClient(this.deviceInfo.id)
      //console.log('this.client==',this.client)
      // 保存this引用用于回调
      const self = this

      this.client.connect({
        onError: function(error) {
          self.term.write('Error: ' + error + '\r\n')
          self.confirmLoading = false
        },
        onConnect: function() {
          self.client.sendInitData({
            operate: 'connect',
            ...self.connectionParams
          })
          self.confirmLoading = false
        },
        onClose: function() {
          //console.log('关闭  Connection closed')
          self.term.write('\r\nConnection closed\r\n')
          self.confirmLoading = false
        },
        onData: function(jsonData) {
          let obj = JSON.parse(jsonData)
          //console.log('得到回调数据obj===', obj)
          if (obj.messageType === 'ssh') {
            self.term.write(obj.data)
            self.term.scrollToBottom()
            setTimeout(() => {
              self.currentCommand = self.getCurrentCommand(self.term)
            }, 100)
          }
        }
      })
      this.confirmLoading = true
    },
    disconnect() {
      // 先移除旧的监听器
      window.removeEventListener('resize', this.handleWindowResize)

      if (this.client) {
        this.client.close()
        this.client = null
      }
      if (this.term) {
        this.term.dispose()
        this.term = null
      }
      this.currentCommand = ''
      this.connectionParams=null
    },
    getCurrentCommand(term) {
      if (!term) return ''
      //.active
      // term.refresh(0, term.rows - 1);// 强制刷新缓冲区
      const buffer = term.buffer;
      // const cursorY = buffer.cursorY;//存在光标位置不更新问题
      const cursorY = term._core.buffer.ybase + term._core.buffer.y;
     // console.log('光标位置cursorY==',cursorY)
      let line = buffer.getLine(cursorY)?.translateToString(true); // 获取当前行文本
      let returnLine = ''
      //console.log('获取当前行文本截取前line===',line)
      if (line && line.trim().indexOf('#') > -1) {
        let idx = line.trim().indexOf('#') + 1
        returnLine = line[idx] ? line.substring(idx).trim() : ''
      }
      //console.log('截取后全量命令:',returnLine);
      return returnLine;
    },
    getFullBufferText(term) {
      if (!term) return ''

      let content = '';
      for (let i = 0; i < term.rows; i++) {
        const line = term.buffer.getLine(i)?.translateToString(true);
        content += line + '\n';
      }
      return content;
    }
  }
}
</script>
<style scoped lang='less'>
::v-deep .j-modal-box .fullscreen .no-footer {
  ::v-deep .ant-modal {
    .ant-modal-content {
      .ant-modal-body {
        height: calc(100vh - 55px) !important;
        max-height:calc(100vh - 55px) !important;
      }
    }
  }
}


</style>