<template>
  <div class="password-field">
    <span>{{ displayPassword }}</span>
    <a-icon :type="showPassword ? 'eye' : 'eye-invisible'" @click="togglePassword" class="password-toggle-icon" />
  </div>
</template>

<script>
export default {
  name: 'PasswordField',
  props: {
    value: {
      type: String,
      required: true,
    },
    // 可选：自定义隐藏时的替换字符
    maskChar: {
      type: String,
      default: '*',
    },
    // 可选：初始状态
    initiallyVisible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      showPassword: this.initiallyVisible,
    }
  },
  computed: {
    displayPassword() {
      return this.showPassword ? this.value : this.value.replace(/./g, this.maskChar)
    },
  },
  methods: {
    togglePassword() {
      this.showPassword = !this.showPassword
      this.$emit('toggle', this.showPassword)
    },
  },
}
</script>

<style scoped>
.password-field {
  position: relative;
  width: 100%;
  display: flex;
  justify-content: space-between;
}
.password-toggle-icon {
  font-size: 18px;
  margin-left: 8px;
  cursor: pointer;
  vertical-align: middle;
}
</style>
