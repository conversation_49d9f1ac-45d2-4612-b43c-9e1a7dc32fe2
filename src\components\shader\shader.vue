<template>
  <a-tooltip overlayClassName="custom-tooltip-shader" :placement="placement" :trigger="trigger"
    @visibleChange="handleClickChange">
    <template slot="title">
      <chrome-picker ref="chromePickerBox" v-model="selectedColor" class="chromePickerBox" @change="changeColor"
        @input="changeColor">
      </chrome-picker>
    </template>
    <div>
      <div class="customColorBox" :style="{
        width: colorBoxWidth, height: colorBoxHeight,
        margin: colorBoxMargin,
        padding: colorBoxPadding, border: colorBoxBorder,
        borderRadius: colorBoxBorderRadius,
        backgroundColor: colorBoxBackgroundColor,
      }">
        <div v-if="!showTransBox" class="colorCard" :style="{ backgroundColor: cardColor }"></div>
        <div v-else class="transCard"></div>
      </div>
    </div>
  </a-tooltip>
</template>
<script>
import './shader.css'
import { Chrome } from 'vue-color';
import covertColor from 'color';
export default {
  name: "shader",
  props: {
    colorBoxWidth: {
      type: String,
      default: '30px'
    },
    colorBoxHeight: {
      type: String,
      default: '30px'
    },
    colorBoxBorder: {
      type: String,
      default: '1px solid #978b8b'
    },
    colorBoxBorderRadius: {
      type: String,
      default: '4px'
    },
    colorBoxBackgroundColor: {
      type: String,
      default: '#fff'
    },
    colorBoxPadding: {
      type: String,
      default: '4px'
    },
    colorBoxMargin: {
      type: String,
      default: '5px'
    },
    // hover/focus/click/contextmenu(右键）
    trigger: {
      type: String,
      required: false,
      default: 'click'
    },
    //topLeft','top' 'topRight', 'leftTop', 'left', 'leftBottom', 'rightTop', 'right', 'rightBottom','bottomLeft', 'bottom', 'bottomRight'
    placement: {
      type: String,
      required: false,
      default: 'bottomLeft'
    },
    /*具体支持的格式，可看本脚本convertToRGBA方法中的注释*/
    value: {
      type: [String, Object],
      required: true,
      default: () => {
        return 'rgba(255,0,0,1)'
      }
    }
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  components: { 'chrome-picker': Chrome },
  data() {
    return {
      showTransBox: false,
      cardColor: '',
      selectedColor: {
        rgba: {
          r: 0,
          g: 0,
          b: 0,
          a: 0
        }
      }
    }
  },
  watch: {
    value(val) {
      this.initPicker()
    }
  },
  mounted() {
    this.initPicker()
  },
  methods: {
    initPicker() {
      this.cardColor = this.convertToRGBA(this.value)
      this.selectedColor.rgba = this.cardColor
    },
    handleClickChange(e) {
      if (!e) {
        this.$emit('ok', this.cardColor)
      }
    },
    changeColor(colorObj) {
      this.showTransBox = colorObj.rgba.a <= 0 ? true : false
      let c = colorObj.rgba
      this.cardColor = `rgba(${c.r},${c.g},${c.b},${c.a})`
      this.$emit('change', this.cardColor)
      this.$emit('input', this.cardColor)
    },
    convertToRGBA(colorInput) {
      //hsva、hsv格式转换  注意对象、字符串格式
      /*colorInput='hsv(120, 100%, 100%)'//转换失败
      colorInput='hsva(120, 100%, 100%,0.5)'//转换失败
      colorInput='{h: 360,s: 1,  v: 1, alpha: 0.5 }'//转换失败
      colorInput={h: 360,s: 1,  v: 1, alpha: 0.5 }//转换成功
      colorInput={h: 360,s: 1,  v: 1}//转换成功*/

      //hsl、hsla格式转换   注意对象、字符串格式
      /* colorInput='hsl(0, 100%, 50%)'//转换成功
       colorInput='hsla(120, 100%, 100%,0.5)'//转换成功
       colorInput='{h: 360,s: 1,  l: 1, a: 0.5 }'//转换失败
       colorInput={h: 360,s: 1,  l: 1, alpha: 0.5 }//转换成功
       colorInput={h: 360,s: 1,  l: 1}//转换成功*/

      //单词颜色：类似red yellow
      // colorInput='red'//转换成功

      //十六进制：类似#fff  #fff000
      /* colorInput='#fff'//转换成功
       colorInput='#fff000'//转换成功
       colorInput='#fff000da'//转换成功*/

      //rgb、rgba  注意对象、字符串格式
      /*colorInput='rgb(1,1,255)'//转换成功
      colorInput='rgb(1,1,255,0.1)'//转换成功
      colorInput='rgb(1,1,255,50%)'//转换成功
      colorInput='{r:22,g:22,b:22,alpha:0.5}'//转换失败
      colorInput={r:22,g:22,b:22,alpha:0.5}//转换成功
      colorInput={r:22,g:22,b:22}//转换成功*/
      try {
        const color = covertColor(colorInput);
        let a = color.alpha()
        this.showTransBox = a <= 0 ? true : false
        //console.log('透明度a===',a)
        //return color.rgb().string();
        let v = color.rgb().color.join(',')
        //console.log('颜色值v===',v)
        //console.log(`rgba(${v},${a})`)
        return `rgba(${v},${a})`;//返回 rgba(r, g, b, a) 格式字符串
      } catch (error) {
        // console.error('无效的颜色值:', colorInput);
        return 'rgba(0, 0, 0, 0)'; // 默认返回白色
        this.showTransBox = true
      }
    }
  }
}
</script>

<style scoped lang="less">
.customColorBox {
  display: flex;

  .colorCard {
    width: 100%;
    height: 100%;
  }

  .transCard {
    width: 100%;
    height: 100%;
    background-color: transparent;
    background-image: linear-gradient(45deg, #ddd 25%, transparent 25%),
      linear-gradient(-45deg, #ddd 25%, transparent 25%),
      linear-gradient(45deg, transparent 75%, #ddd 75%),
      linear-gradient(-45deg, transparent 75%, #ddd 75%);
  }
}
</style>