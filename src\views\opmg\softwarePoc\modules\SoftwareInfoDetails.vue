<template>
  <a-card style='height: 100%; overflow-y: auto' :body-style='{padding:"32px"}'>
    <a-row>
<!--      <a-col :span='24'>
        <span style='float: right; margin-bottom: 12px'>
          <img src='~@/assets/return1.png' alt='' @click='getGo'
               style='width: 20px; height: 20px; cursor: pointer' />
        </span>
      </a-col>-->
      <soft-info ref='softInfo' :record='data' @getGo='getGo'></soft-info>
<!--      <div class='colorBox'>
        <span class="colorTotal">软件信息</span>
      </div>
      <a-col :span='24' style='overflow-x: auto'>
        <a-descriptions bordered>
          <a-descriptions-item label="名称">
            {{ data.softwareName }}
          </a-descriptions-item>
          <a-descriptions-item label="版本">
            {{data.version}}
          </a-descriptions-item>
          <a-descriptions-item label="软件类别">
            {{data.type}}
          </a-descriptions-item>
          <a-descriptions-item label="厂商">
            {{data.manufacture}}
          </a-descriptions-item>
          <a-descriptions-item label="适用平台">
            {{ data.softwareOs }}
          </a-descriptions-item>
          <a-descriptions-item label="cpu架构">
            {{ data.softwareCpu }}
          </a-descriptions-item>
          <a-descriptions-item label="描述">
            {{ data.softwareDescribe }}
          </a-descriptions-item>
        </a-descriptions>
      </a-col>-->
    </a-row>
    <div style='margin-top: 32px'>
      <div class='colorBox' style='margin-bottom: 12px'>
        <span class="colorTotal">软件升级包</span>
      </div>
      <patch-list :softwareId='data.id' @refreshInfo='refreshInfo'></patch-list>
    </div>

  </a-card>
</template>

<script>
import PatchList from '../patch/PatchList'
import SoftInfo from './SoftInfo'
import {
  httpAction,
  getAction
} from '@/api/manage'

export default {
  name: 'SoftwareInfoDetails',
  props: {
    data: {
      type: Object
    }
  },
  components: {
    PatchList,
    SoftInfo
  },
  data() {
    return {
      form: this.$form.createForm(this),
      model: {},
      labelCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 5
        }
      },
      wrapperCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 16
        }
      },
      url: {
        queryById: ' /software/softwareRepository/queryById'
      }
    }
  },
  mounted() {
  },
  methods: {
    //刷新软件信息
    refreshInfo() {
      this.$refs.softInfo.getDataById()
    },
    modalFormOk() {
      let params = {
        id: this.data.id
      }
      getAction(this.url.queryById, params).then((res) => {
        if (res.success) {
          this.data = res.result
        }
      })
    },
    //详情编辑
    handleDetailEdit: function(record) {
      this.$refs.modalForm.edit(record)
      this.$refs.modalForm.title = '编辑'
      this.$refs.modalForm.disableSubmit = false
    },
    //返回上一级
    getGo() {
      this.$parent.pButton2(0)
    }
  }
}
</script>
<style lang='less' scoped>
::v-deep .two-words > div > label {
  letter-spacing: 4px;
}

::v-deep .two-words > div > label::after {
  letter-spacing: 0px;
}

table.gridtable {
  font-family: verdana, arial, sans-serif;
  font-size: 14px;
  color: #606266;
  border-width: 1px;
  border-color: #e8e8e8;
  border-collapse: collapse;
  text-align: left;
  width: 100%;
}

table.gridtable td {
  border-width: 1px;
  border-style: solid;
  border-color: #e8e8e8;
  white-space: nowrap;
}

.leftTd {
  width: 17%;
  background-color: #fafafa;
  padding: 16px 24px;
  text-align: center;
}

.rightTd {
  width: 35%;
  padding: 16px 24px;
}
.colorBox {
  margin-bottom: 10px;
}

.colorTotal {
  padding-left: 7px;
  border-left: 4px solid #1e3674;
  font-size: 14px;
  font-weight: bold;
}
</style>