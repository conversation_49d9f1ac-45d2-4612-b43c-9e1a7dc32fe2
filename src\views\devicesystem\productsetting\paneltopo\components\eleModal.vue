<template>
  <j-modal
   :title="title" 
   :width="width" 
   :visible="visible"
   :destroyOnClose="true"
   :centered='true'
    @ok="handleOk" 
    :okButtonProps="{ class:'jee-hidden'}" 
    @cancel="handleCancel" 
    cancelText="关闭">
    <ele-form ref="eleForm" @ok="submitCallback"></ele-form>
  </j-modal>
</template>

<script>
  import eleForm from './eleForm'
  export default {
    name: 'eleModal',
    components: {
      eleForm
    },
    data() {
      return {
        title: '新增',
        width: '600px',
        visible: false,
      }
    },
    methods: {
      add() {
        this.visible = true
        this.$nextTick(() => {
          this.$refs.eleForm.add();
        })
      },
      edit(record) {
        this.visible = true
        this.$nextTick(() => {
          this.$refs.eleForm.edit(record);
        })
      },
      close() {
        this.$emit('close');
        this.visible = false;
      },
      handleOk() {
        this.$refs.eleForm.submitForm();
        this.$emit("");
      },
      submitCallback(e) {
        this.$emit('ok', e);
        this.visible = false;
      },
      handleCancel() {
        this.close()
      }
    }
  }
</script>
<style lang="less" scoped>
  @import '~@assets/less/normalModal.less';
</style>