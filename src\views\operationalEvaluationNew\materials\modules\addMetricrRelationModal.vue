<template>
  <!-- 配置指标与单位关系的模态框 -->
  <j-modal
    title="配置指标与单位关系"
    :visible="visible"
    width="1200px"
    :footer="null"
    :maskClosable="false"
    @cancel="handleCancel"
  >
    <!-- 关系配置容器 -->
    <div class="relation-config-container">
      <!-- 左侧指标选择区（单选） -->
      <div class="config-column metric-column">
        <div class="column-header">选择指标（单选）</div>
        <div class="column-content">
          <!-- 当指标树数据存在时渲染树组件 -->
          <a-tree
            v-if="metricsTreeData.length > 0"
            :tree-data="metricsTreeData"
            :replaceFields="{ key: 'id', title: 'title', children: 'children' }"
            :selectedKeys="selectedMetric ? [selectedMetric] : []"
            @select="handleMetricSelect"
            :selectable="true"
            :show-icon="true"
            :defaultExpandAll="true"
          >
           <template slot='title' slot-scope='{ title, dataRef }'>
              <a-icon v-if='dataRef && dataRef.info && dataRef.info.isFocus === 1' type="star" style="font-size: 10px; color: #f50; margin-right: 4px;" />
              {{ title }}
           </template>
          </a-tree>
        </div>
      </div>

      <!-- 右侧单位选择区 -->
      <div class="config-column dept-column">
        <div class="column-header">选择单位（多选）</div>
        <!-- 当选中指标后显示单位选择内容 -->
        <div class="column-content" v-if="selectedMetric">
          <div class="dept-transfer-container">
            <!-- 全部单位（树形结构+多选） -->
            <div class="dept-list">
              <div class="list-header">全部单位</div>
              <a-input-search v-model="deptSearchKey" placeholder="搜索单位" style="margin-bottom: 8px" />
              <!-- 多选框组，绑定当前选中的单位ID -->
              <a-checkbox-group v-model="tempSelectedDepts" style="width: 100%">
                <!-- 单位树组件 -->
                <a-tree
                  :tree-data="filteredDeptTree"
                  :checkedKeys="tempSelectedDepts"
                  @check="handleDeptCheck"
                  checkable
                  :checkStrictly="checkStrictly"
                  :multiply="true"
                  :defaultExpandAll="true"
                  :replaceFields="{ title: 'departName', key: 'id' }"
                  class="dept-tree"
                />
              </a-checkbox-group>
            </div>

            <!-- 操作按钮区域 -->
            <div class="dept-actions">
              <!-- 全选叶子节点按钮 -->
              <a-button type="primary" @click="selectAllLeafNodes" style="margin-bottom: 8px">全选</a-button>
              <!-- 清空选择按钮 -->
              <a-button type="danger" @click="clearAllSelection">清空选择</a-button>
            </div>

            <!-- 已选单位（平铺列表） -->
            <div class="dept-list">
              <div class="list-header">已选单位（{{ selectedDeptObjects.length }}）</div>
              <a-list :data-source="selectedDeptObjects" :split="false" class="dept-list-content">
                <a-list-item slot="renderItem" slot-scope="dept">
                  <a-checkbox :checked="true" @change="(e) => toggleDeptSelection(e, dept.id)">
                    {{ dept.departName }}
                  </a-checkbox>
                </a-list-item>
              </a-list>
            </div>
          </div>
        </div>
        <!-- 未选择指标时的提示信息 -->
        <div v-else class="empty-tip">请先选择左侧指标</div>
      </div>
    </div>

    <!-- 模态框底部操作按钮 -->
    <div class="modal-footer">
      <a-button @click="handleCancel">取消</a-button>
      <a-button type="primary" @click="handleSubmit">确定</a-button>
    </div>
  </j-modal>
</template>

<script>
import { queryDepartTreeList } from '@/api/api'
export default {
  name: 'addMetricrRelationModal',
  props: {
    // 指标树数据
    metricsTreeData: {
      type: Array,
      default: () => [],
    },
    // 初始的指标与单位关系数据
    initialRelations: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      // 模态框是否可见
      visible: false,
      // 当前选中的指标ID
      selectedMetric: null,
      // 单位搜索关键词
      deptSearchKey: '',
      tempSelectedDepts: [], // 存储已选单位ID
      currentRelation: null,
      // 部门树数据
      departmentTree: [],
      // 平铺的部门列表
      flatDeptList: [],
      // 所有叶子节点ID
      leafNodeIds: [],
      allTreeKeys: [], // 记录所有的部门id
      checkStrictly: true, // true 无父子关联，false 有父子关联
      relationMap: [], // 用于存放所有的关系
    }
  },
  created() {
    this.processDeptTree()
  },
  computed: {
    // 过滤后的单位树(根据搜索关键词)
    filteredDeptTree() {
      if (!this.deptSearchKey) {
        return this.departmentTree
      }
      const filterTree = (nodes) => {
        return nodes.filter((node) => {
          // 检查节点名称是否包含搜索关键词
          const match = node.departName.includes(this.deptSearchKey)
          if (node.children) {
            // 递归过滤子节点
            const children = filterTree(node.children)
            if (children.length > 0) {
              node.children = children
              return true
            }
          }
          return match
        })
      }
      // 深拷贝部门树数据进行过滤
      return filterTree(JSON.parse(JSON.stringify(this.departmentTree)))
    },
    // 已选单位对象列表
    selectedDeptObjects() {
      return this.tempSelectedDepts.map((id) => this.flatDeptList.find((dept) => dept.id === id)).filter(Boolean)
    },
  },
  methods: {
    // 获取部门树数据
    getDepartmentTree() {
      var that = this
      queryDepartTreeList().then((res) => {
        this.allTreeKeys = []
        for (let i = 0; i < res.result.length; i++) {
          let temp = res.result[i]
          that.departmentTree.push(temp)
          // 获取全部的部门ID(keys)
          that.getAllKeys(temp)
        }

        // 处理部门树数据
        this.processDeptTree()
      })
    },
    // 递归收集树中所有节点的key
    getAllKeys(node) {
      this.allTreeKeys.push(node.key)
      if (node.children && node.children.length > 0) {
        for (let a = 0; a < node.children.length; a++) {
          this.getAllKeys(node.children[a])
        }
      }
    },
    // 处理部门树数据，将树结构平铺并记录叶子节点ID
    processDeptTree() {
      const flatten = (nodes) => {
        let result = []
        nodes.forEach((node) => {
          const newNode = { ...node }
          if (node.isLeaf) {
            // 记录叶子节点ID
            this.leafNodeIds.push(node.id)
          }
          result.push(newNode)
          if (node.children) {
            // 递归处理子节点
            newNode.children = flatten(node.children)
            result = result.concat(newNode.children)
          }
        })
        return result
      }
      // 平铺的部门列表
      this.flatDeptList = flatten(this.departmentTree)
    },
    // 打开模态框
    open(metricId) {
      this.relationMap = JSON.parse(JSON.stringify(this.initialRelations))
      this.visible = true
      this.selectedMetric = metricId || null
      // 查找当前指标对应的关系
      this.currentRelation = this.relationMap.find((r) => r.metricsId === metricId) || null
      // 设置已选单位ID
      this.tempSelectedDepts = this.currentRelation ? [...this.currentRelation.deptIds] : []
      // 重置搜索关键词
      this.deptSearchKey = ''
    },
    // 关闭模态框
    close() {
      this.visible = false
    },
    handleMetricSelect(selectedKeys, { node }) {
      if (node.dataRef.type === 'metric') {
        // 保存当前指标的关系到relationMap
        if (this.selectedMetric) {
          this.updateRelationMap(this.selectedMetric, this.tempSelectedDepts)
        }

        // 切换到新选择的指标
        this.selectedMetric = selectedKeys[0]
        this.currentRelation = this.relationMap.find((r) => r.metricsId === this.selectedMetric) || null
        this.tempSelectedDepts = this.currentRelation ? [...this.currentRelation.deptIds] : []
      }
    },
    // 公共方法：更新relationMap中的关系
    updateRelationMap(metricId, selectedDepts) {
      const existingIndex = this.relationMap.findIndex((r) => r.metricsId === metricId)

      if (selectedDepts.length > 0) {
        // 如果有选中的单位，更新或添加关系
        const relation = {
          metricsId: metricId,
          deptIds: [...selectedDepts],
          metricName: this.findMetricName(metricId),
          deptNames: selectedDepts
            .map((id) => this.flatDeptList.find((dept) => dept.id === id)?.departName)
            .filter(Boolean),
        }
        if (existingIndex >= 0) {
          this.relationMap[existingIndex] = relation
        } else {
          this.relationMap.push(relation)
        }
      } else {
        // 如果没有选中的单位，从relationMap中删除该指标的关系
        if (existingIndex >= 0) {
          this.relationMap.splice(existingIndex, 1)
        }
      }
    },
    // 处理单位多选
    handleDeptCheck(checkedKeys) {
      if (this.checkStrictly) {
        // 父子无关联
        this.$set(this, 'tempSelectedDepts', checkedKeys.checked)
      } else {
        // 父子有关联
        this.$set(this, 'tempSelectedDepts', checkedKeys)
      }
    },
    // 切换单个单位选择状态
    toggleDeptSelection(e, deptId) {
      if (e.target.checked) {
        if (!this.tempSelectedDepts.includes(deptId)) {
          this.tempSelectedDepts = [...this.tempSelectedDepts, deptId]
        }
      } else {
        this.tempSelectedDepts = this.tempSelectedDepts.filter((id) => id !== deptId)
      }
    },
    // 全选叶子节点
    selectAllLeafNodes() {
      this.tempSelectedDepts = this.allTreeKeys
    },
    // 清空所有选择
    clearAllSelection() {
      this.tempSelectedDepts = []
    },
    // 取消操作
    handleCancel() {
      this.close()
    },
    handleSubmit() {
      if (this.selectedMetric) {
        const existingIndex = this.relationMap.findIndex((r) => r.metricsId === this.selectedMetric)
        // 保存当前指标的关系
        if (this.tempSelectedDepts.length > 0) {
          const relation = {
            metricsId: this.selectedMetric,
            deptIds: [...this.tempSelectedDepts],
            metricName: this.findMetricName(this.selectedMetric),
            deptNames: this.selectedDeptObjects.map((dept) => dept.departName),
          }
          if (existingIndex >= 0) {
            this.relationMap[existingIndex] = relation
          } else {
            this.relationMap.push(relation)
          }
        } else {
          // 如果没有选中的单位，从relationMap中删除该指标的关系
          if (existingIndex >= 0) {
            this.relationMap.splice(existingIndex, 1)
          }
        }
      }

      // 提交所有关系
      this.$emit('ok', this.relationMap)
      this.close()
    },
    // 查找指标名称
    findMetricName(metricsId) {
      const findInTree = (nodes) => {
        for (const node of nodes) {
          if (node.id === metricsId) return node.title
          if (node.children) {
            // 递归查找子节点
            const found = findInTree(node.children)
            if (found) return found
          }
        }
        return null
      }
      // 若未找到则返回指标ID
      return findInTree(this.metricsTreeData) || metricsId
    },
  },
  created() {
    // 组件创建时获取部门树数据
    this.getDepartmentTree()
  },
}
</script>

<style scoped lang="less">
.relation-config-container {
  display: flex;
  height: 500px;
  border: 1px solid #f0f0f0;

  .config-column {
    display: flex;
    flex-direction: column;
    border-right: 1px solid #f0f0f0;
    overflow: hidden;

    &:last-child {
      border-right: none;
    }

    .column-header {
      padding: 12px;
      font-weight: bold;
      background: #fafafa;
      border-bottom: 1px solid #f0f0f0;
    }

    .column-content {
      flex: 1;
      padding: 12px;
      overflow-y: auto;
    }
  }

  .metric-column {
    width: 300px;
    .metric-node {
      font-weight: 500;
      color: #000;
    }

    /deep/ .ant-tree-node-content-wrapper {
      width: 100%;
    }
  }

  .dept-column {
    width: calc(100% - 300px);
    .dept-transfer-container {
      display: flex;
      height: 100%;

      .dept-list {
        flex: 1;
        border: 1px solid #f0f0f0;
        border-radius: 4px;
        display: flex;
        flex-direction: column;
        overflow: auto;

        .list-header {
          padding: 8px;
          background: #fafafa;
          border-bottom: 1px solid #f0f0f0;
          font-weight: 500;
        }

        .dept-tree {
          flex: 1;
          overflow-y: auto;
          padding: 8px;

          /deep/ .ant-tree-checkbox {
            margin-right: 4px;
          }
        }

        .dept-list-content {
          flex: 1;
          overflow-y: auto;

          /deep/ .ant-list-item {
            padding: 8px 12px;
            cursor: pointer;

            &:hover {
              background: #f5f5f5;
            }
          }
        }
      }

      .dept-actions {
        display: flex;
        flex-direction: column;
        justify-content: center;
        padding: 0 12px;
        width: 120px;

        button {
          width: 100%;
        }
      }
    }

    .empty-tip {
      color: #999;
      text-align: center;
      margin-top: 100px;
    }
  }
}

.modal-footer {
  margin-top: 16px;
  text-align: right;

  button {
    margin-left: 8px;
  }
}
</style>
