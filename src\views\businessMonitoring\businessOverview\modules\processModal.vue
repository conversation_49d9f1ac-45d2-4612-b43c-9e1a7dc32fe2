<template>
  <j-modal
    :title='title'
    :width='width'
    :centered='true'
    :visible='visible'
    :destroyOnClose='true'
    switchFullscreen
    cancelText='关闭'
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }"
    @ok='handleOk'
    @cancel='handleCancel'
  >
    <a-spin :spinning='confirmLoading'>
      <j-form-container :disabled='disableSubmit'>
        <a-form-model ref='form' :model='model' :rules='validatorRules' :labelCol='labelCol' :wrapperCol='wrapperCol'
                      slot='detail'>
          <a-row :gutter='24'>
            <a-col :lg='12' :md='24'>
              <a-form-model-item label='设备名称' prop='deviceName'>
                <a-select :getPopupContainer='(node) => node.parentNode'
                          v-model='model.deviceName'
                          :autoClearSearchValue='true'
                          :allowClear='true'
                          placeholder='请输入或选择设备名称'
                          @change='deviceNameChange'
                >
                  <a-select-option v-for='(item,index) in topoDeviceList' :key='item.deviceCode'>
                    {{ item.name }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :lg='12' :md='24'>
              <a-form-model-item label='进程名称' prop='procName'>
                <a-select :getPopupContainer='node=>node.parentNode'
                          mode="SECRET_COMBOBOX_MODE_DO_NOT_USE"
                          show-search
                          v-model='model.procName'
                          option-filter-prop='children'
                          :filter-option='filterOption'
                          :autoClearSearchValue='true'
                          :allowClear='true'
                          placeholder='请输入或选择进程名称'
                >
                  <a-select-option v-for='(item,index) in procList' :key='index' :value='item'>
                    {{ item }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :lg='12' :md='24'>
              <a-form-model-item label='关键字' prop='procPath'>
                <a-input
                  v-model='model.procPath'
                  :allow-clear='true'
                  autocomplete='off'
                  placeholder='请输入进程关键字' />
              </a-form-model-item>
            </a-col>
            <a-col :lg='12' :md='24'>
              <a-form-model-item label='描述' prop='description'>
                <a-textarea
                  v-model='model.description'
                  :autoSize='{ minRows: 1, maxRows: 4 }'
                  :allow-clear='true'
                  autocomplete='off'
                  placeholder='请输入描述' />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </j-form-container>
    </a-spin>
  </j-modal>
</template>
<script>
import { httpAction } from '@api/manage'
import { ajaxGetDictItems, getDictItemsFromCache } from '@api/api'
import pick from 'lodash.pick'

export default {
  name: 'businessOverviewModal',
  props: {
    businessInfo: {
      type: Object,
      required: false,
      default: {}
    },
    topoDeviceList: {
      type: Array,
      required: false,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      title: '新增',
      width: '900px',
      disableSubmit: false,
      visible: false,
      confirmLoading: false,
      labelCol: {
        lg: { span: 6 },
        md: { span: 5 },
        sm: { span: 24 },
        xs: { span: 24 }
      },
      wrapperCol: {
        lg: { span: 17 },
        md: { span: 16 },
        sm: { span: 24 },
        xs: { span: 24 }
      },
      model: {},
      operatingSystem: [],
      validatorRules: {
        deviceName: [
          { required: true, message: '请输入设备名称' },
          { min: 1, max: 30, message: '设备名称长度应在 1-30 个字符之间' }
        ],
        procName: [
          { required: true, message: '请输入进程名称' },
          { min: 1, max: 30, message: '进程名称长度应在 1-30 个字符之间' }
        ],
        procPath: [
          { required: false, message: '请输入进程关键字' },
          { min: 1, max: 200, message: '关键字长度应在 1-200 个字符之间' }
        ],
        description: [
          { required: false, min: 1, max: 200, message: '描述长度应在 1-200 个字符之间' }
        ]
      },
      procList: [],
      url: {
        add: '/process/detection/add',
        edit: '/process/detection/edit',
        procList: '/process/detection/procList'
      }
    }
  },
  created() {
    // this.initDictData('operatingSystem', 'os_type')
  },
  methods: {
    changeDeviceName(value) {
      this.$nextTick(() => {
        this.model.deviceName = value
      })
    },
    initDictData(dictOption, dictCode) {
      if (dictCode != null && dictCode != '') {
        //优先从缓存中读取字典配置
        if (getDictItemsFromCache(dictCode)) {
          this[dictOption] = getDictItemsFromCache(dictCode)
          return
        }

        //根据字典Code, 初始化字典数组
        ajaxGetDictItems(dictCode, null).then((res) => {
          if (res.success) {
            this[dictOption] = res.result
          }
        })
      }
    },
    add() {
      this.edit({})
      this.procList = []
    },
    edit(record) {
      this.visible = true
      this.$nextTick(() => {
        this.model = JSON.parse(JSON.stringify(record))
      })
      this.deviceNameChange(record.deviceCode)

    },
    close() {
      this.visible = false
    },
    deviceNameChange(value) {
      httpAction(this.url.procList, { 'deviceCode': value }, 'get')
        .then((res) => {
          this.procList = res.result
        })

    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      )
    },
    handleOk() {
      this.$refs.form.validate((err, values) => {
        if (err) {
          let that = this
          let httpurl = ''
          let method = ''
          if (!that.model.id) {
            httpurl += that.url.add
            method = 'post'
          } else {
            httpurl += that.url.edit
            method = 'put'
          }
          let formData = JSON.parse(JSON.stringify(this.model))
          formData.businessId = this.businessInfo.id
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.close()
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
              that.confirmLoading = false
            })
            .catch((res) => {
              that.$message.warning(res.message)
              that.confirmLoading = false
            })
        }
      })
    },
    handleCancel() {
      this.close()
    }
  }
}
</script>
<style scoped lang='less'>
@import '~@assets/less/normalModal.less';
</style>
