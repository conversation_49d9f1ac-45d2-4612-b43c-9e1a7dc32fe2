<template>
  <j-modal
    width="100%"
    cancelText="关闭"
    :footer="null"
    :visible="visible"
    :destroyOnClose="true"
    :centered="true"
    :fullscreen="true"
    :closable="false"
    :bodyPadding="null"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <three-edit
      v-if='visible'
    :roomId="roomId" 
    :roomName="roomName"
    :modelsArr="modelsArr"
    @hideEditor="visible = false"
    ></three-edit>
  </j-modal>
</template>

<script>
import ThreeEdit from '../three-edit'
export default {
  components: {
    ThreeEdit,
  },
  props: {
    editorShow: {
      type: Boolean,
      default: false,
      required: true,
    },
    roomId: {
      type: String,
      default: '',
      required: true,
    },
     roomName: {
      type: String,
      default: '',
      required: true,
    },
    threeJson: {
      type: String,
      default: '',
    },
     modelsArr:{
      type:Array,
      default:()=>[],
      required: true,
    }
  },
  data() {
    return {
      // visible:false,
    }
  },
  created() {},
  mounted() {},
  computed: {
    visible: {
      get() {
        return this.editorShow
      },
      set(v) {
        this.$emit('hideEditor', v)
      },
    },
  },
  methods: {
    handleOk() {
    },
    handleCancel() {
    },
  },
}
</script>

<style>
</style>