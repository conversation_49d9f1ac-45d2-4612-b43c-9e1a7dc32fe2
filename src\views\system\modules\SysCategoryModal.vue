<template>
  <j-modal :title="title" :width="width" :centered='true' switchFullscreen :visible="visible"
    :confirmLoading="confirmLoading" @ok="handleOk" @cancel="handleCancel" :destroyOnClose="true" cancelText="关闭">
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">

        <a-form-item label="父级节点" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <j-tree-select-expand ref="treeSelect" :getPopupContainer="(node) => node.parentNode" placeholder="请选择父级节点"
            v-decorator="['pid', validatorRules.pid]" dict="sys_category,name,id" pidField="pid" pidValue="0">
          </j-tree-select-expand>
        </a-form-item>

        <a-form-item label="分类名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="[ 'name', validatorRules.name]" placeholder="请输入分类名称" :allowClear='true'
            autocomplete='off' />
        </a-form-item>
      </a-form>
    </a-spin>
  </j-modal>
</template>

<script>
  import {
    httpAction,
    getAction
  } from '@/api/manage'
  import pick from 'lodash.pick'
  import JTreeSelectExpand from '@/components/jeecg/JTreeSelectExpand'

  export default {
    name: "SysCategoryModal",
    components: {
      JTreeSelectExpand
    },
    data() {
      return {
        form: this.$form.createForm(this),
        title: "操作",
        width: 800,
        visible: false,
        model: {},
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          },
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          },
        },

        confirmLoading: false,
        validatorRules: {
          code: {
            rules: [{
              required: true,
              message: '请输入类型编码!'
            }, {
              validator: this.validateMyCode
            }]
          },
          pid: {},
          name: {
            rules: [{
              required: true,
              message: '请输入分类名称!'
            },
            {max:30,message: '分类名称长度不超过30个字符'}]
          }
        },
        url: {
          add: "/sys/category/add",
          edit: "/sys/category/edit",
          checkCode: "/sys/category/checkCode",
        },
        expandedRowKeys: [],
        pidField: "pid",
        subExpandedKeys: []

      }
    },
    created() {},
    methods: {
      add() {
        this.edit({});
      },
      edit(record) {
        this.form.resetFields();
        this.model = Object.assign({}, record);
        this.visible = true;
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.model,
            'pid',
            'name',
            // 'code'
          ))
        })
      },
      close() {
        this.$emit('close');
        this.visible = false;
      },
      handleOk() {
        const that = this;
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if (!this.model.id) {
              httpurl += this.url.add;
              method = 'post';
            } else {
              httpurl += this.url.edit;
              method = 'put';
            }
            let formData = Object.assign(this.model, values);
            httpAction(httpurl, formData, method).then((res) => {
              if (res.success) {
                that.$message.success(res.message);
                that.submitSuccess(formData)
              } else {
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
              that.close();
            })
          }

        })
      },
      handleCancel() {
        this.close()
      },
      popupCallback(row) {
        this.form.setFieldsValue(pick(row,
          'pid',
          'name',
          // 'code'
        ))
      },
      submitSuccess(formData) {
        if (!formData.id) {
          let treeData = this.$refs.treeSelect.getCurrTreeData()
          this.expandedRowKeys = []
          this.getExpandKeysByPid(formData[this.pidField], treeData, treeData)
          if (formData.pid && this.expandedRowKeys.length == 0) {
            this.expandedRowKeys = this.subExpandedKeys;
          }
          this.$emit('ok', formData, this.expandedRowKeys.reverse());
        } else {
          this.$emit('ok', formData);
        }
      },
      getExpandKeysByPid(pid, arr, all) {
        if (pid && arr && arr.length > 0) {
          for (let i = 0; i < arr.length; i++) {
            if (arr[i].key == pid) {
              this.expandedRowKeys.push(arr[i].key)
              this.getExpandKeysByPid(arr[i]['parentId'], all, all)
            } else {
              this.getExpandKeysByPid(pid, arr[i].children, all)
            }
          }
        }
      },
      validateMyCode(rule, value, callback) {
        let params = {
          pid: this.form.getFieldValue('pid'),
          code: value
        }
        getAction(this.url.checkCode, params).then((res) => {
          if (res.success) {
            callback()
          } else {
            callback(res.message)
          }
        })
      },


    }
  }
</script>
<style scoped lang='less'>
  @import '~@assets/less/normalModal.less';
</style>