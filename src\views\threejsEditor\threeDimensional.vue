<template>
  <a-spin style="height: 100%" :spinning="loading">
    <div style="height: 100%;overflow-x: auto;display: flex;align-items: center">
      <div style="height: 100%;width: 300px">
        <postion-tree
          ref="tree"
          :viewFlag="3"
          style="margin-right: 16px"
          @selected="nodeSeleted(arguments)"
          @delOk="delOk"
          @changeInfo='changeInfo'
        >
        </postion-tree>
      </div>
      <div class="right-col">
        <div
          v-if="contentFlag && selectedNodeType === 'room'"
          class="content-div"
          style="display: flex; flex-direction: column"
        >
          <a-card :bodyStyle="{ height: '100%' }" :bordered="false" style="height: 100%">
            <div class="table-page-search-wrapper">
              <div class="three-show-title">
                <div style='width: calc(100% - 70px);overflow-x: hidden'>
                  <div style='color: #000'>机房名称：{{ roomName }}</div>
                  <div>
<!--                    <span>机房ID：{{ roomId }}</span>-->
                    <span v-if='roomData.level'>等级：{{ roomData.level }}</span>
                    <a-divider v-if='roomData.floorArea' type="vertical" />
                    <span v-if='roomData.floorArea'>面积：{{ roomData.floorArea }}㎡</span>
                    <a-divider v-if='roomData.address' type="vertical" />
                    <span v-if='roomData.address'>地址：{{ roomData.address }}</span>
                  </div>
                </div>
                <a-button type="primary" @click="doedit">编辑</a-button>
              </div>
            </div>
            <div class="div-table-container" style="flex: auto">
              <three-scan v-if="!editorShow" :roomId="roomId" :modelsArr="modelsArr"></three-scan>
            </div>
          </a-card>
        </div>
        <div
          v-if="contentFlag && selectedNodeType === 'cabinet'"
          class="content-div"
          style="display: flex; flex-direction: column"
        >
          <a-card :bodyStyle="{ height: '100%' }" :bordered="false" style="height: 100%">
            <rack-edit ref="rackEdit" :cabinetId="cabinetId" :targetModel="targetModel" :viewFlag="3"></rack-edit>
          </a-card>
        </div>
      </div>
      <editor-modal
        :editorShow="modalshow"
        :roomId="roomId"
        :roomName="roomName"
        :modelsArr="modelsArr"
        @hideEditor="hideEditor"
      ></editor-modal>
    </div>
  </a-spin>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { getAction } from '@/api/manage'
//引入公共devicetree组件
import PostionTree from '@/components/tree/PostionTree.vue'
import RackEdit from '@/views/threejsEditor/modules/RackEdit'
import EditorModal from '@/views/threejsEditor/modules/editorModal'
import ThreeScan from '@/views/threejsEditor/three-scan'
import { tryToJSON } from '@/utils/util.js'
export default {
  name: 'threeDimensional',
  mixins: [mixinDevice],
  components: {
    PostionTree,
    EditorModal,
    ThreeScan,
    RackEdit,
  },
  data() {
    return {
      disableMixinCreated: true,
      nodeName: '',
      //查询条件，此参数名称与JeecgListMixin模块参数一致
      queryParam: {
        //查询参数
        name: '',
        status: '',
        categoryId: '',
      },
      firstTitle: '', //存储搜素tree的第一个title
      // 树
      assetsCategoryTree: [],
      treeData: [],
      expandedKeys: [],
      searchValue: '',
      autoExpandParent: true,
      dropTrigger: '',
      selectedKeys: [],
      selectedTitle: '',
      checkedKeys: [],
      checkStrictly: true,
      rightClickSelectedBean: {},
      url: {
        list: '/device/deviceInfo/deptAndPositionlist',
        roomInfo: '/topo/room/info',
      },
      name: '',
      status: '',
      categoryId: '', //选取tree的key
      selectedNodeType: '', //左侧tree选择的tree的类型
      selectedNodeId: '', //左侧tree选择的tree的id
      view: '3d', //info 传参 2d
      contentFlag: false, //是否为编辑机房页面。true为否，false为是
      roomData: {},
      roomId: '', //机房id
      cabinetId: '', //机柜id
      roomName: '',
      cabinetEditFlag: false, //机柜页面编辑状态标识
      loading: false,
      editorShow: false,
      threeJson: '',
      viewFlag: 3,
      targetModel: null,
      modelsArr: [],
      modalshow: false,
    }
  },
  methods: {
    //修改机房信息
    changeInfo(info) {
      console.log("修改信息 === ", info)
      this.roomName = info.name;
    },
    delOk() {
      this.contentFlag = false
    },
    cabinetEditOk() {
      this.cabinetEditFlag = false
      //this.$refs.tree.reloadTreeAfterCabinet()
    },
    reloadTree() {
      this.$refs.tree.reloadTree()
    },
    doedit() {
      this.contentFlag = true
      this.editorShow = true
      this.$nextTick(() => {
        this.modalshow = true
      })
    },
    hideEditor(v) {
      this.threeJson = ''
      this.editorShow = false
      this.modalshow = false

      getAction(this.url.roomInfo, { id: this.selectedNodeId, view: this.view })
        .then((res) => {
          if (res.success) {
            this.threeJson = res.result || ''
          } else {
            return false
          }
          this.loading = false
        })
        .catch((err) => {
          this.loading = false
        })
      this.$refs.tree.reloadTree()
    },
    // 右键操作方法
    rightHandle(node) {
      this.dropTrigger = 'contextmenu'
      this.rightClickSelectedKey = node.node.eventKey
      this.rightClickSelectedBean = node.node.dataRef
    },
    onExpand(expandedKeys) {
      // if not set autoExpandParent to false, if children expanded, parent can not collapse.
      // or, you can remove all expanded children keys.
      this.expandedKeys = expandedKeys
      this.autoExpandParent = false
    },
    //表单查询,点击查询按钮，默认查询第一页
    dosearch() {
      // this.nodeName = this.queryParam.name
      this.$refs.visView.showNode(this.queryParam.name)
    },
    async nodeSeleted(params) {
      //清空机房查询条件
      this.queryParam.name = ''
      this.selectedNodeType = ''
      this.cabinetEditFlag = false
      this.selectedNodeId = params[1]
      this.roomId = ''
      this.loading = true
      if (params[0] === 'room') {
        this.roomData = params[3]
        console.log("机房内信息",this.roomData)
        this.roomId = this.selectedNodeId
        await getAction(this.url.roomInfo, { id: this.roomId, view: this.view })
          .then((res) => {
            if (res.success) {
              this.modelsArr = res.result || []
              if (this.modelsArr.length > 0) {
                try {
                  this.modelsArr.forEach((el) => {
                    el.position = tryToJSON(el.position)
                    el.rotation = tryToJSON(el.rotation)
                    el.scale = tryToJSON(el.scale)
                    el.userData = tryToJSON(el.userData)
                    if (Object.prototype.toString.call(el.userData) === '[object Object]') {
                      el.userData.dataId = el.id
                    }
                  })
                } catch (error) {
                  this.modelsArr = []
                  this.$message.error('机房数据有错误！')
                  console.log('有报错 === ', error)
                }
                //  console.log('3d机房的json数据 == ', this.modelsArr)
              }
            } else {
              return false
            }
            this.loading = false
          })
          .catch((err) => {
           this.loading = false
          })
        this.roomName = params[2]
      } else if (params[0] === 'cabinet') {
        await getAction(this.url.roomInfo, { id: params[3].roomId, view: this.view })
          .then((res) => {
            if (res.success) {
              if (res.result) {
                let arr = res.result || []
                let models = arr.map((el) => {
                  el.userData = tryToJSON(el.userData)
                  return el
                })
                this.targetModel = models.find((el) => el.userData.id === this.selectedNodeId)
              }
            }
            this.loading = false
          })
          .catch((err) => {
           this.loading = false
          })
        this.cabinetId = this.selectedNodeId
      } else {
        this.loading = false
      }
      this.contentFlag = true
      this.selectedNodeType = params[0]
      // this.queryParam.momgDeptId = selectedKey;
      // this.loadData(1);
    },
  },
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
/*.form-row{*/
/*  display: flex;*/
/*  margin:0px 0px !important;*/
/*  align-items: center;*/
/*  height:60px;*/
/*  background-color: white;*/
/*}*/
/*.form-col{*/
/*  height:34px;*/
/*}*/
.div-table-container {
  background-color: #f3f3f3;
  /* margin: 0 16px 16px 16px; */
  height: calc(100% - 50px);
  /* overflow: scroll; */
  border-radius: 4px;
}

.table-page-search-wrapper {
  margin-bottom: 16px;

  .three-show-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}

.right-col {
  height: 100%;
  width: calc(100% - 300px);
  min-width: 800px;
  /*padding-left: 12px!important;*/
}

.content-div {
  width: 100%;
  height: 100%;
  background-color: white;
  border-radius: 3px;
}
/deep/ .ant-spin-container{
  height: 100%;
}
</style>
