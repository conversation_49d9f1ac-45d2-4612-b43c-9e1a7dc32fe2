<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    :destroyOnClose="true"
    :centered="true"
    switchFullscreen
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭"
    okText="保存并安排任务">
    <devops-auto-inspection-form
      ref="realForm"
      @ok="submitCallback"
      @closeForm='close'
      :disabled="disableSubmit"
    ></devops-auto-inspection-form>
  </j-modal>
</template>

<script>
import DevopsAutoInspectionForm from './DevopsAutoInspectionForm'
export default {
  name: 'DevopsAutoInspectionModal',
  components: {
    DevopsAutoInspectionForm,
  },
  data() {
    return {
      title: '',
      width: '800px',
      visible: false,
      disableSubmit: false,
    }
  },
  methods: {
    add() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.add()
      })
    },
    edit(record) {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.edit(record)
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
      //this.$refs.realForm.selectInspectionType()
    },
    handleOk() {
      this.$refs.realForm.submitForm()
    },
    submitCallback() {
      this.$emit('ok')
      this.visible = false
    },
    handleCancel() {
      //this.close()
      this.$nextTick(() => {
        this.$refs.realForm.closeForm()
      })
    },
  },
}
</script>
<style lang="less" scoped>
@import '~@assets/less/normalModal.less';
</style>