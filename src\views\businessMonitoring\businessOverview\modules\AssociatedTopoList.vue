<template>
  <j-modal
    :title="title"
    :visible="visible"
    :destroyOnClose="true"
    :centered='true'
    width="1000px"
    :maskClosable="true"
    @cancel="handleCancel"
  >
    <template slot="footer">
      <a-button @click="handleCancel">取消</a-button>
      <a-button v-if="step === 2" @click="stepBack" type="primary">上一步</a-button>
      <a-button @click="handleOk" type="primary" :loading="saveLoading">{{ stepName }}</a-button>
    </template>
    <div class="topo-box" v-if="step === 1">
      <div class='table-page-search-wrapper'>
        <a-form layout="inline" :labelCol="{span:6}" :wrapperCol="{span:18}">
          <a-row :gutter='24'>
            <a-col :xxl="8" :xl="10" :lg="12" :md="14" :sm="24">
              <a-form-item label="拓扑名称">
                <a-input
                  :allow-clear='true'
                  placeholder="请输入拓扑名称"
                  v-model="queryParam.name"/>
              </a-form-item>
            </a-col>
            <a-col :xxl="8" :xl="10" :lg="12" :md="10" :sm="24">
              <a-form-item>
                <span>
                  <a-button type="primary" class="btn-search btn-search-style" @click="searchQuery">查询</a-button>
                  <a-button type="default" class="btn-reset btn-reset-style" style='margin-left: 8px' @click="searchReset">重置</a-button>
                </span>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div v-if="dataSource.length > 0">
        <div class="topo-list-div" style="width: 100%">
          <a-row :gutter="16" type="flex" align="middle">
            <a-col v-for="topoInfo in dataSource" :key="topoInfo.id" v-bind="CardColLayout" style="margin-bottom: 20px">
              <a-card
                :title="topoInfo.topoName"
                :class="[topoInfo.id === activeId ? 'ant-card-active' : '']"
                @click="topoChoose(topoInfo)"
              >
                <div class="hover" :style="{ height: '170px' }" v-if="topoInfo.topoSvg" v-html="topoInfo.topoSvg"></div>
                <div
                  class="hover"
                  :style="{ height: '170px', display: 'flex', 'justify-content': 'center', 'align-items': 'center' }"
                  v-else
                >
                  <a-empty />
                </div>
              </a-card>
            </a-col>
          </a-row>
        </div>
        <div class="pagination-div">
          <a-pagination
            :hideOnSinglePage="false"
            :default-current="ipagination.current"
            :total="ipagination.total"
            @change="onPageChange"
            :page-size="ipagination.pageSize"
            :show-total="ipagination.showTotal"
            size="small"
          >
          </a-pagination>
        </div>
      </div>
      <div v-else>
        <a-empty />
      </div>
    </div>
    <div class="topo-box" v-else-if="step === 2">
      <div class='table-page-search-wrapper'>
        <a-form layout="inline" :labelCol="{span:6}" :wrapperCol="{span:18}">
          <a-row :gutter="24">
            <a-col :xxl="8" :xl="10" :lg="12" :md="14" :sm="24">
              <a-form-item label="设备名称">
                <a-input
                  :allow-clear='true'
                  placeholder="请输入设备名称"
                  v-model="deviceParams.name"
                />
              </a-form-item>
            </a-col>
            <a-col :xxl="8" :xl="10" :lg="12" :md="10" :sm="24">
              <a-form-item>
                <span style='white-space: nowrap'>
                  <a-button type="primary" class="btn-search btn-search-style" @click="searchDevice">查询</a-button>
                   <a-button type="default" class="btn-reset btn-reset-style" style='margin-left: 8px' @click="resetDevice">重置</a-button>
                </span>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>

      <a-table
        ref="table"
        bordered
        rowKey="id"
        :columns="deviceColumns"
        :dataSource="deviceSource"
        :scroll='dataSource.length>0?{x:"max-content"}:{}'
        :loading="loading"
        :row-selection="rowSelection"
        :pagination="tablePagination"
        @change="tableChange"
      >
      </a-table>
    </div>
  </j-modal>
</template>

<script>
import { getAction, postAction, putAction, deleteAction } from '@/api/manage'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'

export default {
  name: 'TopoEdit',
  components: {},
  mixins: [JeecgListMixin],
  data() {
    return {
      title: '关联拓扑',
      record: {},
      visible: false,
      disableSubmit: false,
      CardColLayout: {
        xl: {
          span: 8,
        },
        lg: {
          span: 8,
        },
        md: {
          span: 8,
        },
        sm: {
          span: 12,
        },
        xs: {
          span: 24,
        },
      },
      editFlag: false,
      queryParam: {
        name: '',
        topoType: '1',
      },
      topoId: '',
      topoName: '',
      disableMixinCreated: true,
      dataSource: [],
      ipagination: {
        current: 1,
        pageSize: 6,
        total: 0,
        showTotal: (total, range) => {
          return ' 共' + total + '个'
        },
      },
      url: {
        list: '/topo/topoInfo/list',
      },
      hoverHeight: '0px',
      clientHeight: '',
      staticDomainURL: '',
      activeId: '', //选中的拓扑id
      step: 1,
      deviceSource: [],
      loading: false,
      businessNode: '', //主业务系统设备code
      deviceParams: {
        topoId: '',
        name: '',
      },
      deviceColumns: [
        {
          title: '设备名称',
          dataIndex: 'name',

        },
        {
          title: '产品名称',
          dataIndex: 'productName',

        },
        {
          title: '通信协议',
          dataIndex: 'transferProtocol',

        },
        {
          title: '添加时间',
          dataIndex: 'createTime',

        },
        {
          title: '设备说明',
          dataIndex: 'description',

        },
      ],
      rowSelection: {
        type: 'radio',
        onChange: (rowKeys, rows) => {
          this.businessNode = rows[0].deviceCode
        },
      },
      tablePagination: {
        current: 1,
        pageSize: 8,
        total: 0,
        showQuickJumper: true,
        showTotal: (total, range) => {
          return ' 共' + total + '条'
        },
      },
      saveLoading: false,
    }
  },
  computed: {
    stepName() {
      return this.step === 1 ? '下一步' : '保存'
    },
    cardStyle() {
      return { 'box-shadow': ' 0px 0px 4px rgb(0 0 0 / 16%)' }
    },
    cardChooseStyle() {
      return { 'box-shadow': ' 0px 0px 4px rgb(0,121,254)' }
    },
  },
  methods: {
    show(businessId, topoId) {
      this.businessId = businessId
      this.activeId = topoId
      this.businessNode = ''
      this.visible = true
      this.step = 1
      this.ipagination.current = 1
      let timer = setTimeout(() => {
        clearTimeout(timer)
        this.loadData(1)
      }, 100)
    },
    close() {
      this.dataSource = []
      this.$emit('close')
      this.visible = false
    },
    tableChange(pagination, filters) {
      if (this.tablePagination.current !== pagination.current) {
        this.tablePagination.current = pagination.current
        this.getDeviceList()
      }
    },
    //关联拓扑 上一步
    stepBack() {
      this.tablePagination.current = 1
      this.businessNode = ''
      this.step = 1
    },
    //关联拓扑 下一步 保存
    handleOk() {
      if (this.step === 1) {
        if (!this.activeId) {
          this.$message.warning('请选择拓扑图')
          return
        }
        this.step = 2
        this.getDeviceList()
      } else if (this.step === 2) {
        this.saveRelationTopo()
      }
    },
    searchReset() {
      this.queryParam.name=''
      this.loadData(1)
    },
    handleCancel() {
      this.close()
    },
    submitCallback() {
      this.$emit('ok')
      this.visible = false
    },
    //保存关联拓扑
    saveRelationTopo() {
      if (this.businessNode === '') {
        this.$message.warning('请选择主业务系统设备')
        return
      }
      let saveData = {
        businessId: this.businessId,
        topoId: this.activeId,
        businessNode: this.businessNode,
      }
      this.saveLoading = true
      putAction('/business/info/relationTopo', saveData)
        .then((res) => {
          this.saveLoading = false
          if (res.success) {
            this.close()
            this.$emit("ok",this.activeId,this.businessNode)
            this.$message.success('保存成功')
          }

          console.log('保存关联拓扑获取的数据 === ', res)
        })
        .catch((err) => {
          this.saveLoading = false
          console.log('请求失败 === ', err)
        })
    },
    //查询设备
    searchDevice() {
      this.tablePagination.current = 1
      this.getDeviceList()
    },
    resetDevice(){
      this.deviceParams.name=''
      this.tablePagination.current = 1
      this.getDeviceList()
    },
    // 获取设备列表
    getDeviceList() {
      this.deviceParams.topoId = this.activeId
      this.deviceParams.pageNo = this.tablePagination.current
      this.deviceParams.pageSize = this.tablePagination.pageSize
      getAction('/business/info/topoDeviceList', this.deviceParams).then((res) => {
        if (res.success) {
          let data = res.result
          this.deviceSource = data.records
          this.tablePagination.total = data.total
        }
      })
    },
    //选择拓扑图
    topoChoose(topoInfo) {
      if (this.activeId === topoInfo.id) {
        return
      }
      this.activeId = topoInfo.id
    },
    //获取拓扑列表
    loadData(arg) {
      if (arg === 1) {
        this.ipagination.current = 1
      }
      var params = this.getQueryParams() //查询条件
      this.loading = true
      getAction(this.url.list, params).then((res) => {
        if (res.success) {
          this.dataSource = res.result.pageList.records
          this.$nextTick(() => {
            let tags = document.getElementsByTagName('image')
            // 修改土图片ip
            Array.from(tags).forEach((tag) => {
              let url = tag.getAttribute('xlink:href')
              let newUrl = ''
              if (url.startsWith('/insight-api/')) {
                const regx = /\/insight-api/
                newUrl = url.replace(regx, window._CONFIG['domianURL'])
              } else {
                const regx = /(https?\:\/\/[?:\d\w\.]+[?:\d\w]\/)insight-api/
                newUrl = url.replace(regx, window._CONFIG['domianURL'])
              }

              tag.setAttribute('xlink:href', newUrl)
            })
          })
          this.ipagination.total = res.result.pageList.total
          let timer = setTimeout(() => {
            let hoverEl = document.getElementsByClassName('hover')
            if (hoverEl && hoverEl[0]) {
              this.hoverHeight = (hoverEl[0].clientWidth / 5) * 3 + 'px'
            }
            clearTimeout(timer)
          }, 50)
        }
        if (res.code === 510) {
          this.$message.warning(res.message)
        }
        this.loading = false
      })
    },
    //监听拓扑图页面变化
    onPageChange(pageNumber, pageSize) {
      this.ipagination.pageSize = pageSize
      this.ipagination.current = pageNumber
      this.loadData()
    },
  },
}
</script>

<style lang="less" scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/normalModal.less';
.hover {
  cursor: pointer;
  width: 100%;
  height: 100%;
  margin: auto;
  // border: 1px solid #e8e8e8;
  position: relative;
  // background-color: #e8e8e8;
  border-radius: 10px;
  background-color: rgba(242, 242, 242, 1);
  // display: flex;
  // justify-content: center;
  // align-items: center;
}

.topobgimg {
  background: url('/assets/topobgImage/bgcolor-gray.png');
}

.topobgcolor {
  background-color: #f3f3f3;
}

::v-deep .hover > svg {
  width: 100% !important;
  height: 100% !important;
}

::v-deep .hover > svg > g {
  transform: matrix(1, 0, 0, 1, 1, 1) !important;
}

.pagination-div {
  display: flex;
  flex-direction: row-reverse;
  //   padding-bottom: 20px;
  //   margin-top: 20px;
}
::v-deep .ant-card {
  box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.16);
  -webkit-box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.16);
}

::v-deep .ant-card:hover {
  box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.5);
  -webkit-box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.5);
}
::v-deep .ant-card-active {
  -webkit-box-shadow: 0px 0px 4px rgba(0, 121, 254, 1);
  box-shadow: 0px 0px 4px rgba(0, 121, 254, 1);
}
::v-deep .ant-card-active:hover {
  -webkit-box-shadow: 0px 0px 4px rgba(0, 121, 254, 0.6);
  box-shadow: 0px 0px 4px rgba(0, 121, 254, 0.6);
}
::v-deep .ant-card-head-title {
  font-family: PingFangSC-Regular;
  font-size: 14px;
  font-weight: 650;
  color: rgba(0, 0, 0, 0.85);
  padding: 0px;
  line-height: 48px;
}

::v-deep .ant-card-extra {
  clear: both;
  margin: 0px;
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  padding: 12px 0 !important;
}

/*::v-deep .ant-modal-body {
  padding: 16px;
}*/

::v-deep .ant-card-head {
  border-bottom: 0px solid #e8e8e8;
  padding: 0px 10px;
  height: 48px;
  min-height: 48px;
}

::v-deep .ant-card-actions {
  border-top: 0px solid #e8e8e8;
}

::v-deep .ant-card-bordered {
  width: 100%;
  position: relative;

  .ant-card-head-wrapper {
    justify-content: flex-end;
  }
}

::v-deep .ant-card-body {
  padding: 0px 16px 16px;
  width: 100%;
  margin: 0px;

}

.span-toponame {
  cursor: pointer;
  position: absolute;
  top: 14px;
}
.topo-box {
  // height: 550px;
  // overflow-y: scroll;
  // overflow-x:hidden;
  padding: 10px;
}
</style>