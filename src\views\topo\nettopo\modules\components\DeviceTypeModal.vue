<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    :destroyOnClose="true"
    :closable="false"
    :maskClosable="false"
    @cancel="hide"
    cancelText="关闭"
  >
    <div style="height: 60vh">
      <device-tree-expand
        :inputFlag="true"
        @selected="deviceSeleted"
        :tree-url="'/assetscategory/assetsCategory/selectTree'"
        :arr-type="[null, 'product']"
        :icon-name="['menu', 'bars']"
        :btnIconName="'appstore'"
        :btnName="'全部设备'"
        :is-show-btn-icon="true"
        :is-show-icon="true"
      ></device-tree-expand>
    </div>
    <template slot="footer">
      <a-button v-if="footerType" @click="hide"> 取消 </a-button>
      <a-button v-if="footerType" type="primary" @click> 确定 </a-button>
      <div v-if="!footerType"></div>
    </template>
  </j-modal>
</template>

<script>
import DeviceTreeExpand from '@/components/tree/DeviceTreeExpand.vue'
export default {
  name: 'DeviceTypeModal',
  components: {
    DeviceTreeExpand,
  },
  data() {
    return {
      title: '选择设备类型',
      width: 600,
      visible: false,
      disableSubmit: false,
      showabled: false,
      data: null,
      scene: 'virtual',
    }
  },
  computed: {
    footerType() {
      return false
      // if (this.scene === 'virtual') {
      //   return false
      // }
      // return true
    },
  },
  methods: {
    show(scene) {
      this.scene = scene
      this.visible = true
    },
    hide() {
      this.visible = false
    },
    deviceSeleted(key, type, name) {
      if (type === 'product') {
        this.$emit('chooseType', name, type, key, this.scene)
        this.hide()
      }
    },
  },
}
</script>