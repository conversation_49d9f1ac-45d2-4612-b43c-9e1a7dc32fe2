<template>
  <a-card style="height: 100%;overflow: auto">
    <a-row>
      <a-col :span="24">
        <span style="margin-left: 10px;font-size: 16px; ">
          详情
        </span>
        <span style="float: right;margin-bottom: 12px;"><img src="~@/assets/return1.png" alt="" @click="getGo"
            style="width: 20px;height: 20px;cursor: pointer"></span>
      </a-col>
      <a-col :span="24">
        <table class="gridtable">
          <tr>
            <td class="leftTd">所属子网组</td>
            <td class="rightTd">{{ record.subnetGroupName }}</td>
            <td class="leftTd">所属子网</td>
            <td class="rightTd">{{ record.segmentName }}</td>
          </tr>
          <tr>
            <td class="leftTd">网段名称</td>
            <td class="rightTd">{{ record.segmentName }}</td>
            <td class="leftTd">开始IP</td>
            <td class="rightTd">{{record.startIp}}</td>
          </tr>
          <tr>
            <td class="leftTd">结束IP</td>
            <td class="rightTd">{{ record.endIp }}</td>
            <td class="leftTd">使用部门</td>
            <td class="rightTd">{{record.departName}}</td>
          </tr>
          <tr>
            <td class="leftTd">使用位置</td>
            <td class="rightTd">{{ record.location }}</td>
            <td class="leftTd">备注</td>
            <td class="rightTd">{{record.remark}}</td>
          </tr>
        </table>
      </a-col>
    </a-row>
  </a-card>
</template>

<script>
  import {
    httpAction,
    getAction
  } from '@/api/manage'
  import pick from 'lodash.pick'
  export default {
    name: 'segmentDetails',
    props: {
      data: {
        type: Object
      }
    },
    data() {
      return {
        form: this.$form.createForm(this),
        record: {},
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          },
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          },
        },
        confirmLoading: false,
        url: {
          queryById: '/devops/ip/segment/queryById',
        }
      }
    },
    watch: {
      data(newVal) {
        this.show(newVal)
      },
    },
    mounted() {
      this.show(this.data)
    },
    methods: {
      show(record) {
        getAction(this.url.queryById, {
          id: record.id
        }).then((res) => {
          if (res.success) {
            this.record = res.result
          }
        });
      },
      //返回上一级
      getGo() {
        this.$parent.pButton2(0)
      }
    }
  }
</script>
<style scoped>
  table.gridtable {
    font-family: verdana, arial, sans-serif;
    font-size: 14px;
    color: #606266;
    border-width: 1px;
    border-color: #e8e8e8;
    border-collapse: collapse;
    text-align: left;
    width: 100%;
  }

  table.gridtable td {
    border-width: 1px;
    border-style: solid;
    border-color: #e8e8e8;
  }

  .leftTd {
    width: 17%;
    background-color: #FAFAFA;
    padding: 16px 24px;
    text-align: center;
  }

  .rightTd {
    width: 35%;
    padding: 16px 24px;
  }
</style>