<template>
  <div class="table-module">
    <div class="table-title flex-between align-center">
      <div class="table-title-left  flex align-center">
        <div style="margin-right: 5px"><img src="@/assets/bigScreen/9.png" alt /></div>
        <div>网络设备统计</div>
      </div>
      <div class="table-title-right">
        <span>请选择日期：</span>
        <a-range-picker dropdownClassName="big-screen-range-picker" @change="timeChange"
          v-model="queryParam.searchCreateTime" format='YYYY-MM-DD HH:mm:ss'>
          <a-icon slot="suffixIcon" type="schedule" :style="{ color: '#FCFDFC' }" />
        </a-range-picker>
        <a-button class="table-title-btn" @click="searchQuery"> 查询 </a-button>
        <a-button class="table-title-btn" type="primary" @click="exportPDF"> 导出 </a-button>
      </div>
    </div>
    <div class="table-box" ref="tableBox">
      <a-table bordered rowKey='id' :pagination="false" :columns="columns" :data-source="dataSource"
        :scroll='dataSource.length > 0 ? {x:"max-content"} : {}'>
        <template slot="in" slot-scope="text">
          <span style="color: #0998e3">{{ text }}</span>
        </template>
        <template slot="out" slot-scope="text">
          <span style="color: #02d944">{{ text }}</span>
        </template>
      </a-table>
    </div>
  </div>
</template>

<script>
  import {
    getAction,
  } from '@/api/manage'
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  export default {
    mixins: [JeecgListMixin],
    data() {
      return {
        columns: [{
            title: '设备名称',
            dataIndex: 'name',
            align: "center",
          },
          {
            title: '设备类型',
            dataIndex: 'type',
            align: "center",
          },
          {
            title: 'IP地址',
            dataIndex: 'ip',
            align: "center",
          },
          {
            title: '运行时长',
            dataIndex: 'sysUptime',
            align: "center",
          },
          {
            title: '端口数',
            dataIndex: 'portNum',
            align: "center",
          },
          {
            title: '接收流量（MB）',
            dataIndex: 'in',
            align: "center",
            scopedSlots: {
              customRender: 'in'
            },
          },
          {
            title: '输出流量（MB）',
            dataIndex: 'out',
            align: "center",
            scopedSlots: {
              customRender: 'out'
            },
          },
        ],
        dataSource: [],
        start: '',
        end: '',
        url: {
          list: 'device/statistics/network',
          exportXlsUrl: 'device/statistics/exportPDF',
        },
      }
    },
    created() {
      // this.initData()
    },
    mounted() {},
    methods: {
      timeChange(value, data) {
        this.queryParam.searchCreateTime = data
        this.start = data[0]
        this.end = data[1]
      },
      // 导出
      exportPDF() {
        getAction(this.url.exportXlsUrl, {searchCreateTime: this.queryParam.searchCreateTime}).then((res) => {
          if (res.code === 200) {
            window.open(window._CONFIG['domianURL'] + '/sys/common/static/' + res.result + '?filename=网络设备报表.pdf')
          } else {
            this.$message.error(res.message)
          }
        })
      }
    },
  }
</script>

<style lang="less" scoped>
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';

  .table-module {
    height: 100%;
    background-color: #111217;
    padding: 16px;

    .table-title {
      height: 32px;

      .table-title-left {
        color: #45c5e0;
        font-size: 16px;
      }

      .table-title-btn {
        margin-left: 16px;
      }
    }

    .table-box {
      margin-top: 16px;
      width: 100%;
      height: calc(100% - 32px - 16px);
      // background-color: #fff;
    }
  }
</style>