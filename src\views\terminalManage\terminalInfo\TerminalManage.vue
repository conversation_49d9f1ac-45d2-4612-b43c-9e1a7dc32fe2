<template>
  <div style="height:100%;">
     <keep-alive exclude='TerminalInfo'>
        <component :is="pageName" style="height:100%" :data="data"/>
     </keep-alive>
  </div>
</template>
<script>
import TerminalList from './TerminalList'
import TerminalInfo from './modules/TerminalInfo'
export default {
  name: "TerminalManage",
  data() {
    return {
      isActive: 0,
      data:{}
    };
  },
  components: {
    TerminalList,
    TerminalInfo
  },
  created(){
    this.pButton1(0);
  },
  //使用计算属性
  computed: {
    pageName() {
      switch (this.isActive) {
        case 0:
          return "TerminalList";
          break;

        default:
          return "TerminalInfo";
          break;
      }
    }
  },
  methods: {
    pButton1(index) {
      this.isActive = index;
    },
    pButton2(index,item) {
      this.isActive = index;
      this.data = item;
    }
  }
}
</script>