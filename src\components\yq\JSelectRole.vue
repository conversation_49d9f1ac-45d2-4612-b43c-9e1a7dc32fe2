<template>
  <div>
    <a-select
      :getPopupContainer='(node) => node.parentNode'
      :allowClear='true'
      :placeholder='placeholder'
        v-model='value'
       :value='getValueSting'
      @change='handleInput'
    >
      <a-select-option v-for='(item, key) in roleOptions' :key='key' :value='item.id'>
      <span style='display: inline-block; width: 100%' :title="item.roleName">
        {{ item.roleName }}
      </span>
      </a-select-option>
    </a-select>
  </div>
</template>

<script>
export default {
  props: {
    placeholder: String,
    value: [String, Number],
    roleOptions: []
  },
  computed: {
    getValueSting() {
      if (this.value !== null || undefined || '') {
        return undefined
      } else {
        return this.value.toString()
      }
    }
  },
  methods: {
    handleInput(e) {
      this.$emit('input', e)
    }
  }

}

</script>
<style scoped lang='less'>

</style>