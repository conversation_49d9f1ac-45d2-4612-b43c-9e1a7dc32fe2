<template>
  <div class="wrap">
    <div class="relation-btn-div">
      <a-button class="add-btn" @click="savePNG" >导出</a-button>
    </div>
    <div id="largedgeDiv" class="edge-div">
      <div>
        <p>关系：<span>{{edgeRelation!=null?edgeRelation:''}}</span></p>
        <!-- <p>起始节点：<span>{{edgeConnectNode!=null?edgeConnectNode.source:''}}</span></p>
        <p>终止节点：<span>{{edgeConnectNode!=null?edgeConnectNode.target:''}}</span></p> -->
      </div>
    </div>
    <div class="content">
      <div class="panel">
        <!--流程图画板-->
        <div id="largeContainer" class="container"/>
      </div>
    </div>
  </div>
</template>

<script>
import './index.less'
import FlowGraph from './graph'
// Cell, Node,
import { Graph, Color, Dom, Shape, Edge, NodeView, Addon, DataUri } from '@antv/x6'
import dagre from 'dagre'
import { getAction, httpAction } from '@/api/manage'
import { globalGridAttr, globalGridAttr2 } from './models/global'

const { Dnd } = Addon

const tryToJSON = (val) => {
  try {
    return JSON.parse(val)
  } catch (error) {
    return val
  }
}
// 布局方向
const dir = 'TB' // LR RL TB BT

export default {
  name: 'enlargeRelationTopo',
  components: {
  },
  data() {
    return {
      operate:'create',//当前页面为查看、编辑：show、create
      type: 'grid',
      id: '',
      visible: false,
      graph: '',
      isReady: false,
      // 树数据
      assetsCategoryTree: [],
      draggable:true,
      url: {
        treeUrl: '/topo/device/tree',
        deviceInfo: '/topo/device/info',
        deviceChildren: '/topo/device/children',
        topoEdit: '/topo/topoInfo/edit',
        findAssetsRelation:'/assets/assets/findAssetsRelation'
      },
      replaceFields: {
        title: 'name',
        key: 'id'
      },
      picSrc: window._CONFIG['staticDomainURL'],
      dnd: Object,
      deviceInfo: null,
      topoInfo: null,
      copyTopoInfo:null,//topo信息的复制保存，用于子拓扑返回父拓扑时使用
      cellList:[],
      alarmInfo:{
        deviceName:'snmp01',
        templateName:'qqqq',
        level:'10'
      },
      globalGridAttr:Object.assign(globalGridAttr,globalGridAttr2),
      edgeConnectNode:null,
      edgeRelation:'',
      sonTopoFlag:false,//true标识此时的拓扑为子拓扑
      largeState:false,//放大状态,
    }
  },
  props:{
    assetsId:{
      type:String,
      default:'',
      required:false
    }
  },
  computed: {
    getAlarmInfo () {
      return this.$store.getters.alarmInfo
    }
  },

  mounted() {
    this.initGraph("largeContainer")
    this.findAssetsRelation()
  },
  methods: {
    savePNG(){
      this.graph.toPNG((dataUri) => {
            // 下载
            DataUri.downloadDataUri(dataUri, 'chartx.png')
          }, {
            backgroundColor: 'white',
            padding: {
              top: 20,
              right: 30,
              bottom: 40,
              left: 50
            },
            quality: 1
          })
    },
    show(assetsId){
      this.largeState = true
      this.$nextTick(()=>{
        this.initGraph("largeContainer")
        this.findAssetsRelation()
      })
    },
    findAssetsRelation(){
      getAction(this.url.findAssetsRelation,{assetsId:this.assetsId}).then(res => {
        if(res.success){
          this.createNodesAndEdges(res)
          this.layout()
          this.graph.zoomTo(0.8)
          this.graph.centerContent()
          this.setup()
        }
      })
    },
    createNodesAndEdges(res){
      let nodeList = []
      res.result.nodeList.forEach(ele=>{
        nodeList.push(this.createNode(ele.assetsName, ele.assetsId))
      })
      let edgeList = []
      if(res.result.relationList){
        res.result.relationList.forEach(ele=>{
          const sourceNode = nodeList.find(item=>item.data.id === ele.assetsIdOne)
          const targetNode = nodeList.find(item=>item.data.id === ele.assetsIdTwo)
          const relation = ele.relation || ele.relationReverse
          edgeList.push(this.createEdge(sourceNode, targetNode, relation))
        })
      }
      this.graph.resetCells([...nodeList, ...edgeList])
    },
    // 自动布局
    layout () {
      const nodes = this.graph.getNodes()
      const edges = this.graph.getEdges()
      const g = new dagre.graphlib.Graph()
      g.setGraph({ rankdir: dir, nodesep: 16, ranksep: 16 })
      g.setDefaultEdgeLabel(() => ({}))

      const width = 120
      const height = 60
      nodes.forEach((node) => {
        g.setNode(node.id, { width, height })
      })

      edges.forEach((edge) => {
        const source = edge.getSource()
        const target = edge.getTarget()
        g.setEdge(source.cell, target.cell)
      })

      dagre.layout(g)

      this.graph.freeze()

      g.nodes().forEach((id) => {
        const node = this.graph.getCell(id)
        if (node) {
          const pos = g.node(id)
          node.position(pos.x, pos.y)
        }
      })

      edges.forEach((edge) => {
        const source = edge.getSourceNode()
        const target = edge.getTargetNode()
        const sourceBBox = source.getBBox()
        const targetBBox = target.getBBox()

      })
      this.graph.unfreeze()
    },

    onClose() {
      this.graph.dispose()
      this.visible = false
    },

    initGraph(container) {
      this.graph = FlowGraph.init(container)
      //画布grid配置
      this.graph.drawGrid({
        type: this.globalGridAttr.type,
        args: [
          {
            color: this.globalGridAttr.color,
            thickness: this.globalGridAttr.thickness
          }
        ]
      })
      this.graph.setGridSize(this.globalGridAttr.size)
    },
    // 监听画布节点、边自定义事件
    setup() {
      //鼠标移入边事件
      this.graph.on('edge:mouseenter', ({ e, edge, view }) => {
        this.edgeRelation= edge.data.relation || ""
        if(this.edgeRelation != ""){
          document.getElementById('largedgeDiv').style.display = "block"
          document.getElementById('largedgeDiv').style.top = e.offsetY+80+"px"
          document.getElementById('largedgeDiv').style.left = e.offsetX+30+"px"
          document.getElementById('largedgeDiv').style.zIndex=100
        }
      })
      //鼠标移出边事件
      this.graph.on('edge:mouseleave',  () => {
        document.getElementById('largedgeDiv').style.display = "none"
      })
    },
    createEdge (source, target, relation) {
      return this.graph.createEdge({
        source: { cell: source.id },
        target: { cell: target.id },
        zIndex: -1,
        data:{
          relation:relation
        },
        attrs: {
          line: {
            stroke: '#181818',
            strokeWidth: 3,
            sourceMarker: null,
            targetMarker: null
          }
        }
      })
    },
    createNode(nodeName,nodeId){
      return this.graph.createNode({
        shape: 'ellipse',
        width: 120,
        height: 60,
        data:{
          id:nodeId,
          name:nodeName
        },
        attrs: {
          body: {
            fill: '#1890FF',
            stroke: '#737C83',
          },
          label:{
            text:nodeName,
            fill:'#F1F8FF'
          }
        },
      })
    },

  }
}

</script>

<style scoped lang="less">
.wrap{
  height:500px;
}
.panel {
  width: 100%;
  height: 100%;
}
.panel .toolbar {
  display: flex;
  align-items: center;
  height: 38px;
  background-color: #f7f9fb;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

.wrap .content {
  display: flex;
  height: calc(100% - 36px) !important;
}
::v-deep .sider {
  box-shadow: none;
  border: 1px solid rgba(0, 0, 0, 0.08) !important;
  margin-right: 10px;
}
.container {
  height: 100% !important;
  width: 100% !important;
}

::v-deep .x6-graph-scroller{
  width:100% !important;
}

::v-deep .x6-graph{
  box-shadow:none !important;
}
.edge-div{
  display:none;
  width: 200px;
  background-color: #FFF;
  border-radius: 5px;
  color: #595959;
  position: absolute;
  top: 405px;
  left: 775px;
  z-index: 100;
  p {
    margin-bottom: 0px;
    height: 30px;
    line-height: 30px;
    padding-left: 15px;
  }
}
.relation-btn-div{
  display:flex;
  justify-content:flex-start;
  padding-bottom:8px;
  .add-btn{
    height:28px;
    margin-right:16px;
    background: #ECF5FF;
    border: 1px solid #B3D8FF;
    border-radius: 4px;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #409EFF;
  }
}
</style>
