@font-color:rgba(255, 255, 255, 1);
@clear-icon:#ffffff;//清除按钮
@clear-icon-hover: #a2a3a4;
//@select-dropdown-bg:#082143;//select select-tree 下拉框背景
//@select-dropdown-bg:#101c31;//select select-tree 下拉框背景
@select-dropdown-bg: #111d34;//select select-tree 下拉框背景
//@select-dropdown-bg-hover: rgb(9, 47, 100);
@select-dropdown-bg-hover: linear-gradient(90deg, rgba(21, 85, 175, 0.6) 2%, rgba(21, 85, 175, 0.26) 43%, rgba(21, 85, 175, 0) 100%);;
//@select-dropdown-font-hover:#409eff;
@select-dropdown-font-hover:#66ffff;
@border:rgba(215,215,215,0.70);
@btn-bg: rgba(64, 158, 255, 0);
@btn-search-bg:#409eff;
@btn-search-bg-hover:#007dff;
@btn-reset-border: #DCDFE6;
@btn-reset-border-hover: #007dff;

//input组件
/deep/ .ant-input,
/deep/ .ant-input-affix-wrapper .ant-input,
/deep/ .ant-input-group .ant-input {
  background-color: rgba(255, 255, 255, 0) !important;
  color: @font-color !important;
  border:1px solid @border;
}
/deep/ .ant-input-clear-icon{
  color: @clear-icon;
}
/deep/ .ant-input-clear-icon:hover {
  color: @clear-icon-hover;
}
//校验未通过
/deep/ .has-error .ant-input,
/deep/ .has-error .ant-input:hover,
/deep/ .has-error .ant-input-affix-wrapper .ant-input,
/deep/ .has-error .ant-input-affix-wrapper .ant-input:hover,
/deep/ .has-error .ant-input-group .ant-input,
/deep/ .has-error .ant-input-group .ant-input:hover
{
  background-color: rgba(255, 255, 255, 0);
  color: @font-color !important;
  border-color: #f5222d;
}

//textarea组件清除按钮
/deep/ .ant-input-textarea-clear-icon{
  color: @clear-icon;
}
/deep/ .ant-input-textarea-clear-icon:hover{
  color: @clear-icon-hover;
}

//select组件
//select组件输入框---可编辑状态
/deep/ .ant-select-selection-selected-value,
/deep/ .ant-select-dropdown-menu-item {
  color: @font-color;
}
//select组件中清除按钮
/deep/ .ant-select-selection__clear {
  background: rgba(255, 255, 255, 0);
  color: @clear-icon;
}
/deep/ .ant-select-selection__clear:hover {
  color: @clear-icon-hover;
}

//select组件输入框---不可编辑样式
/deep/ .ant-select-disabled {
  color: rgba(0, 0, 0, 0.25);
}
/deep/ .ant-select-disabled .ant-select-selection {
  background: rgba(215, 215, 215, 0.4);
}
/deep/ .ant-select-disabled .ant-select-selection:hover,
/deep/ .ant-select-disabled .ant-select-selection:focus,
/deep/ .ant-select-disabled .ant-select-selection:active {
  border-color: #d9d9d9;
  box-shadow: none;
}
/deep/ .ant-select-disabled .ant-select-selection--multiple .ant-select-selection__choice {
  background: #f5f5f5;
  color: #aaa;
}
/deep/ .ant-select-disabled .ant-select-selection__choice__remove {
  color: rgba(0, 0, 0, 0.25);
}
/deep/ .ant-select-disabled .ant-select-selection__choice__remove:hover {
  color: rgba(0, 0, 0, 0.25);
}
//select组件下拉箭头
/deep/ .ant-select-arrow {
  color: rgba(255, 255, 255, 1);
}
//下拉选择框父级自定义不是body时的默认样式
/deep/ .ant-select-selection,
/deep/ .ant-select-dropdown,
/deep/ .ant-select-dropdown-menu-item {
  background-color: rgba(255, 255, 255, 0);
  border: 1px solid @border;
}

/deep/ .ant-select-dropdown-menu-item:hover {
  color:@select-dropdown-font-hover;
  background: @select-dropdown-bg-hover;
}

/deep/ .ant-select-dropdown,
/deep/ .ant-select-dropdown-menu-item {
  border: none
}

/deep/ .ant-select-dropdown-menu {
  background-color: @select-dropdown-bg;
  padding: 0;
}

/deep/ .ant-select-dropdown-menu-item-active,
/deep/ .ant-select-dropdown-menu-item-selected,
/deep/ .ant-select-dropdown-menu-item:hover:not(.ant-select-dropdown-menu-item-disabled) {
  background: @select-dropdown-bg-hover !important;
  color: @select-dropdown-font-hover !important;
}

//下拉选择框父级默认为body，需给select组件的属性dropdownClassName赋值为‘custom-select-dropdown’，并且是全局应用，下拉框样式才发生变化
.custom-select-dropdown{
  background-color:  @select-dropdown-bg !important;

  .ant-select-dropdown-menu-item {
    color: @font-color !important;
    background-color: rgba(255, 255, 255, 0) !important;
  }

  .ant-select-dropdown-menu-item-active,
  .ant-select-dropdown-menu-item-selected,
  .ant-select-dropdown-menu-item:hover:not(.ant-select-dropdown-menu-item-disabled) {
    background: @select-dropdown-bg-hover !important;
    color: @select-dropdown-font-hover !important;
  }
}


//select-tree组件
/deep/ .ant-select-tree-checkbox {
  color: rgba(0, 0, 0, 0.65);
}
/deep/ .ant-select-tree-checkbox-wrapper:hover .ant-select-tree-checkbox-inner,
/deep/ .ant-select-tree-checkbox:hover .ant-select-tree-checkbox-inner,
/deep/ .ant-select-tree-checkbox-input:focus + .ant-select-tree-checkbox-inner {
  //border-color: @primary-color;
}
/deep/ .ant-select-tree-checkbox-checked:after {
  border-radius: 2px;
  //border: 1px solid @primary-color;
}
/deep/ .ant-select-tree-checkbox-inner {
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  background-color: #fff;
}
/deep/ .ant-select-tree-checkbox-inner:after {
  border: 2px solid #fff;
  border-top: 0;
  border-left: 0;
}
/deep/ .ant-select-tree-checkbox-indeterminate .ant-select-tree-checkbox-inner:after {
  border: 0;
  //background-color: @primary-color;
}
/deep/ .ant-select-tree-checkbox-indeterminate.ant-select-tree-checkbox-disabled .ant-select-tree-checkbox-inner:after {
  border-color: rgba(0, 0, 0, 0.25);
}
/deep/ .ant-select-tree-checkbox-checked .ant-select-tree-checkbox-inner:after {
  border: 2px solid #fff;
  border-top: 0;
  border-left: 0;
}
/deep/ .ant-select-tree-checkbox-checked .ant-select-tree-checkbox-inner {
  //background-color: @primary-color;
  //border-color: @primary-color;
}
/deep/ .ant-select-tree-checkbox-disabled.ant-select-tree-checkbox-checked .ant-select-tree-checkbox-inner:after {
  border-color: rgba(0, 0, 0, 0.25);
}
/deep/.ant-select-tree-checkbox-disabled .ant-select-tree-checkbox-inner {
  border-color: #d9d9d9 !important;
  background-color: #f5f5f5;
}
/deep/.ant-select-tree-checkbox-disabled .ant-select-tree-checkbox-inner:after {
  border-color: #f5f5f5;
}
/deep/ .ant-select-tree-checkbox-disabled + span {
  color: rgba(0, 0, 0, 0.25);
}
/deep/ .ant-select-tree-checkbox-wrapper {
  color: @font-color;
}
/deep/ .ant-select-tree-checkbox-group {
  color: @font-color;
}
/deep/ .ant-select-tree {
  color: @font-color;
}
/deep/ .ant-select-tree li .ant-select-tree-node-content-wrapper {
  border-radius: 2px;
  color: @font-color;
  background-color: transparent;
}
/deep/ .ant-select-tree li .ant-select-tree-node-content-wrapper:hover {
  //background-color: color(~`colorPalette("@{primary-color}", 1)`);
  background: @select-dropdown-bg-hover;
  color: @select-dropdown-font-hover;
}
/deep/ .ant-select-tree li .ant-select-tree-node-content-wrapper.ant-select-tree-node-selected {
  //background-color: color(~`colorPalette("@{primary-color}", 2)`);
  background: @select-dropdown-bg-hover;
  color: @select-dropdown-font-hover;
}
/deep/ .ant-select-tree li span.ant-select-tree-switcher,
/deep/ .ant-select-tree li span.ant-select-tree-iconEle {
  border: 0 none;
}
/deep/ .ant-select-tree li span.ant-select-icon_loading .ant-select-switcher-loading-icon {
  //color: @primary-color;
}
/deep/ .ant-select-tree li span.ant-select-tree-switcher.ant-select-tree-switcher_open .ant-select-switcher-loading-icon,
/deep/ .ant-select-tree li span.ant-select-tree-switcher.ant-select-tree-switcher_close .ant-select-switcher-loading-icon {
  //color: @primary-color;
}
/deep/ li.ant-select-tree-treenode-disabled > span:not(.ant-select-tree-switcher),
/deep/ li.ant-select-tree-treenode-disabled > .ant-select-tree-node-content-wrapper,
/deep/ li.ant-select-tree-treenode-disabled > .ant-select-tree-node-content-wrapper span {
  color: rgba(0, 0, 0, 0.25);
}
/deep/ li.ant-select-tree-treenode-disabled > .ant-select-tree-node-content-wrapper:hover {
  background: transparent;
}
/deep/ .ant-select-tree-dropdown {
  color: @font-color !important;
  background-color: @select-dropdown-bg !important;
}
/deep/ .ant-select-tree-dropdown .ant-select-dropdown-search {
  background-color:@select-dropdown-bg !important;
}

/deep/ .ant-select-tree-dropdown .ant-select-dropdown-search .ant-select-search__field {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background-color: @select-dropdown-bg !important;
}
/deep/ .ant-select-tree-dropdown .ant-select-not-found {
  color: rgba(0, 0, 0, 0.25);
}

//radio组件
/deep/ .ant-radio-group {
  color:@font-color
  //color: rgba(0, 0, 0, 0.65);
}
/deep/ .ant-radio-wrapper {
  color:@font-color
  //color: rgba(0, 0, 0, 0.65);
}
/deep/ .ant-radio {
  color:@font-color
  //color: rgba(0, 0, 0, 0.65);
}
/deep/ .ant-radio-wrapper:hover .ant-radio .ant-radio-inner,
/deep/ .ant-radio:hover .ant-radio-inner,
/deep/ .ant-radio-focused .ant-radio-inner {
  //border-color: @primary-color;
}
/deep/ .ant-radio-checked:after {
  border-radius: 50%;
  //border: 1px solid @primary-color;
}
/deep/ .ant-radio-inner {
  border-width: 1px;
  border-style: solid;
  border-radius: 100px;
  border-color: #d9d9d9;
  background-color: #fff;
}
/deep/ .ant-radio-inner:after {
  border-radius: 8px;
  border-top: 0;
  border-left: 0;
  //background-color: @primary-color;
}
/deep/ .ant-radio-checked .ant-radio-inner {
  //border-color: @primary-color;
}
/deep/ .ant-radio-disabled .ant-radio-inner {
  border-color: #d9d9d9 !important;
  background-color: #f5f5f5;
}
/deep/ .ant-radio-disabled .ant-radio-inner:after {
  background-color: #ccc;
}
/deep/ .ant-radio-disabled + span {
  color: rgba(0, 0, 0, 0.25);
}
/deep/ .ant-radio-button-wrapper {
  color: rgba(0, 0, 0, 0.65);
  border: 1px solid #d9d9d9;
  border-left: 0;
  border-top-width: 1.02px;
  background: #fff;
}
/deep/ .ant-radio-button-wrapper a {
  color: rgba(0, 0, 0, 0.65);
}
/deep/ .ant-radio-button-wrapper:not(:first-child)::before {
  background-color: #d9d9d9;
}
/deep/ .ant-radio-button-wrapper:first-child {
  border-radius: 4px 0 0 4px;
  border-left: 1px solid #d9d9d9;
}
/deep/ .ant-radio-button-wrapper:last-child {
  border-radius: 0 4px 4px 0;
}
/deep/ .ant-radio-button-wrapper:first-child:last-child {
  border-radius: 4px;
}
/deep/ .ant-radio-button-wrapper:hover,
/deep/ .ant-radio-button-wrapper-focused {
  //color: @primary-color;
}
/deep/ .ant-radio-button-wrapper-checked {
  background: #fff;
  //border-color: @primary-color;
  //color: @primary-color;
  //box-shadow: -1px 0 0 0 @primary-color;
}
/deep/ .ant-radio-button-wrapper-checked::before {
  //background-color: @primary-color !important;
}
/deep/ .ant-radio-button-wrapper-checked:first-child {
  //border-color: @primary-color;
  box-shadow: none !important;
}
/deep/ .ant-radio-button-wrapper-checked:hover {
  //border-color: color(~`colorPalette("@{primary-color}", 5)`);
  //box-shadow: -1px 0 0 0 color(~`colorPalette("@{primary-color}", 5)`);
  //color: color(~`colorPalette("@{primary-color}", 5)`);
}
/deep/ .ant-radio-button-wrapper-checked:active {
  //border-color: color(~`colorPalette("@{primary-color}", 7)`);
  // box-shadow: -1px 0 0 0 color(~`colorPalette("@{primary-color}", 7)`);
  //color: color(~`colorPalette("@{primary-color}", 7)`);
}
/deep/ .ant-radio-group-solid .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
  //background: @primary-color;
  //border-color: @primary-color;
  color: #fff;
}
/deep/ .ant-radio-group-solid .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):hover {
  //border-color: color(~`colorPalette("@{primary-color}", 5)`);
  //background: color(~`colorPalette("@{primary-color}", 5)`);
  color: #fff;
}
/deep/ .ant-radio-group-solid .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):active {
  //border-color: color(~`colorPalette("@{primary-color}", 7)`);
  //background: color(~`colorPalette("@{primary-color}", 7)`);
  color: #fff;
}
/deep/ .ant-radio-button-wrapper-disabled {
  border-color: #d9d9d9;
  background-color: #f5f5f5;
  color: rgba(0, 0, 0, 0.25);
}
/deep/ .ant-radio-button-wrapper-disabled:first-child,
/deep/ .ant-radio-button-wrapper-disabled:hover {
  border-color: #d9d9d9;
  background-color: #f5f5f5;
  color: rgba(0, 0, 0, 0.25);
}
/deep/ .ant-radio-button-wrapper-disabled:first-child {
  border-left-color: #d9d9d9;
}
/deep/ .ant-radio-button-wrapper-disabled.ant-radio-button-wrapper-checked {
  color: #fff;
  background-color: #e6e6e6;
  border-color: #d9d9d9;
  box-shadow: none;
}


//form组件label样式
/deep/ .ant-form-item-label>label{
  color: @font-color;
}

/**查询按钮*/
.btn-search {
  background: @btn-search-bg !important;
  border-radius: 4px;
  width: 73px;
  height: 32px;
  font-size: 14px;
  color: #ffffff;
  margin: 0 8px 0 16px;
  box-shadow: none;
}
.btn-search:hover {
  background: @btn-search-bg-hover !important;
  box-shadow: none;
}
/**重置按钮*/
.btn-reset {
  border: 1px solid @btn-reset-border;
  border-radius: 4px;
  width: 73px;
  height: 32px;
  font-size: 14px;
  color: #ffffff;
  margin: 0 8px 0 16px;
  box-shadow: none;
}
.btn-reset:hover {
  border: 1px solid @btn-reset-border-hover;
  box-shadow: none;
}
/** 展开/收起的样式*/
.btn-updown{
  margin-left: 8px;
  color: @btn-search-bg;
}
/deep/ .ant-btn {
  background-color: @btn-bg;
}
/deep/ .ant-btn-primary {
  color: #fff;
  background-color: @btn-search-bg;
  border-color: @btn-search-bg;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);
  box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
}
/deep/ .ant-btn-primary:hover {
  background-color: @btn-search-bg-hover;
  border-color: @btn-search-bg-hover;
}
//table组件
/deep/ .ant-table {
  color: rgb(255, 255, 255);
}
/deep/ .ant-table table {
  border-collapse: collapse;
  border-radius: 4px 4px 0 0;
}
/deep/ .ant-table-thead > tr > th {
  background:  rgba(12,81,217,0.22);
  color: rgb(255, 255, 255);
  border-bottom: 1px solid rgba(122,149,194,0.24);
}
/deep/ .ant-table-thead > tr > th .anticon-filter,
/deep/ .ant-table-thead > tr > th .ant-table-filter-icon {
  color: #bfbfbf;
}
/deep/ .ant-table-thead > tr > th .ant-table-filter-selected.anticon-filter {
  //color: @primary-color;
}
/deep/ .ant-table-thead > tr > th .ant-table-column-sorter {
  color: #bfbfbf;
}
/deep/ .ant-table-thead > tr > th .ant-table-column-sorter-up.on,
/deep/ .ant-table-thead > tr > th .ant-table-column-sorter-down.on {
  //color: @primary-color;
}
/deep/ .ant-table-thead > tr > th.ant-table-column-has-actions:hover {
  background: #f5f5f5;
}
/deep/ .ant-table-thead > tr > th.ant-table-column-has-actions:hover .anticon-filter,
/deep/ .ant-table-thead > tr > th.ant-table-column-has-actions:hover .ant-table-filter-icon {
  background: #f5f5f5;
}
/deep/ .ant-table-thead > tr > th.ant-table-column-has-actions:hover .anticon-filter:hover,
/deep/ .ant-table-thead > tr > th.ant-table-column-has-actions:hover .ant-table-filter-icon:hover {
  color: rgba(0, 0, 0, 0.45);
  background: #ebebeb;
}
/deep/ .ant-table-thead > tr > th.ant-table-column-has-actions:hover .anticon-filter:active,
/deep/ .ant-table-thead > tr > th.ant-table-column-has-actions:hover .ant-table-filter-icon:active {
  color: rgba(0, 0, 0, 0.65);
}
/deep/ .ant-table-thead > tr > th.ant-table-column-has-actions .anticon-filter.ant-table-filter-open,
/deep/ .ant-table-thead > tr > th.ant-table-column-has-actions .ant-table-filter-icon.ant-table-filter-open {
  color: rgba(0, 0, 0, 0.45);
  background: #ebebeb;
}
/deep/ .ant-table-thead > tr > th.ant-table-column-has-actions:active .ant-table-column-sorter-up:not(.on),
/deep/ .ant-table-thead > tr > th.ant-table-column-has-actions:active .ant-table-column-sorter-down:not(.on) {
  color: rgba(0, 0, 0, 0.45);
}
/deep/ .ant-table-thead > tr > th .ant-table-column-sorters:before {
  background: transparent;
}
/deep/ .ant-table-thead > tr > th .ant-table-column-sorters:hover:before {
  background: rgba(0, 0, 0, 0.04);
}
/deep/ .ant-table-thead > tr:first-child > th:first-child {
  border-top-left-radius: 4px;
}
/deep/ .ant-table-thead > tr:first-child > th:last-child {
  border-top-right-radius: 4px;
}
/deep/ .ant-table-thead > tr:not(:last-child) > th[colspan] {
  border-bottom: 0;
}
/deep/ .ant-table-tbody > tr > td {
  border-bottom: 1px solid rgba(122,149,194,0.24);
}
/deep/ .ant-table-thead > tr.ant-table-row-hover > td,
/deep/ .ant-table-tbody > tr.ant-table-row-hover > td,
/deep/ .ant-table-thead > tr:hover > td,
/deep/ .ant-table-tbody > tr:hover > td {
  //background: color(~`colorPalette("@{primary-color}", 1)`);
}
/deep/ .ant-table-thead > tr:hover {
  background: none;
}
/deep/ .ant-table-footer {
  background: #fafafa;
  border-radius: 0 0 4px 4px;
  border-top: 1px solid rgba(122,149,194,0.24);
}
/deep/ .ant-table-footer:before {
  background: #fafafa;
}
/deep/ .ant-table.ant-table-bordered .ant-table-footer {
  border: 1px solid rgba(122,149,194,0.24);
}
/deep/ .ant-table-title {
  border-radius: 4px 4px 0 0;
}
/deep/ .ant-table.ant-table-bordered .ant-table-title {
  border: 1px solid rgba(122,149,194,0.24);
}
/deep/ .ant-table-title + .ant-table-content {
  border-radius: 4px 4px 0 0;
}
/deep/ .ant-table-bordered .ant-table-title + .ant-table-content,
/deep/ .ant-table-bordered .ant-table-title + .ant-table-content table,
/deep/ .ant-table-bordered .ant-table-title + .ant-table-content .ant-table-thead > tr:first-child > th {
  border-radius: 0;
}
/deep/ .ant-table-without-column-header .ant-table-title + .ant-table-content,
/deep/ .ant-table-without-column-header table {
  border-radius: 0;
}
/deep/ .ant-table-tbody > tr.ant-table-row-selected td {
  background: #fafafa;
}
/deep/ .ant-table-thead > tr > th.ant-table-column-sort {
  background: #f5f5f5;
}
/deep/ .ant-table-tbody > tr > td.ant-table-column-sort {
  background: rgba(0, 0, 0, 0.01);
}
/deep/ .ant-table-header {
  background: #fafafa;
}
/deep/ .ant-table-header table {
  border-radius: 4px 4px 0 0;
}
/deep/ .ant-table-loading .ant-table-body {
  background: #fff;
}
/deep/ .ant-table-bordered .ant-table-header > table,
/deep/ .ant-table-bordered .ant-table-body > table,
/deep/ .ant-table-bordered .ant-table-fixed-left table,
/deep/ .ant-table-bordered .ant-table-fixed-right table {
  border: 1px solid rgba(122,149,194,0.24);
  border-right: 0;
  border-bottom: 0;
}
/deep/ .ant-table-bordered.ant-table-empty .ant-table-placeholder {
  border-left: 1px solid rgba(122,149,194,0.24);
  border-right: 1px solid rgba(122,149,194,0.24);
}
/deep/ .ant-table-bordered.ant-table-fixed-header .ant-table-header > table {
  border-bottom: 0;
}
/deep/ .ant-table-bordered.ant-table-fixed-header .ant-table-body > table {
  border-top: 0;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
/deep/ .ant-table-bordered.ant-table-fixed-header .ant-table-body-inner > table {
  border-top: 0;
}
/deep/ .ant-table-bordered.ant-table-fixed-header .ant-table-placeholder {
  border: 0;
}
/deep/ .ant-table-bordered .ant-table-thead > tr:not(:last-child) > th {
  border-bottom: 1px solid rgba(122,149,194,0.24);
}
/deep/ .ant-table-bordered .ant-table-thead > tr > th,
/deep/ .ant-table-bordered .ant-table-tbody > tr > td {
  border-right: 1px solid rgba(122,149,194,0.24);
}
/deep/ .ant-table-placeholder {
  background: rgba(255, 255, 255, 0);
  border-bottom: 1px solid rgba(122,149,194,0.24);
  border-top:none;
  color: rgba(0, 0, 0, 0.45);
}
/deep/ .ant-table-filter-dropdown {
  background: #ffffff;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}
/deep/ .ant-table-filter-dropdown .ant-dropdown-menu {
  border: 0;
  box-shadow: none;
  border-radius: 4px 4px 0 0;
}
/deep/ .ant-table-filter-dropdown .ant-dropdown-menu-sub {
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}
/deep/ .ant-table-filter-dropdown .ant-dropdown-menu .ant-dropdown-submenu-contain-selected .ant-dropdown-menu-submenu-title:after {
  //color: @primary-color;
}
/deep/ .ant-table-filter-dropdown > .ant-dropdown-menu > .ant-dropdown-menu-item:last-child,
/deep/ .ant-table-filter-dropdown > .ant-dropdown-menu > .ant-dropdown-menu-submenu:last-child .ant-dropdown-menu-submenu-title {
  border-radius: 0;
}
/deep/ .ant-table-filter-dropdown-btns {
  border-top: 1px solid rgba(122,149,194,0.24);
}
/deep/ .ant-table-filter-dropdown-link {
  //color: @primary-color;
}
/deep/ .ant-table-filter-dropdown-link:hover {
  //color: color(~`colorPalette("@{primary-color}", 5)`);
}
/deep/ .ant-table-filter-dropdown-link:active {
  //color: color(~`colorPalette("@{primary-color}", 7)`);
}
/deep/ .ant-table-selection .anticon-down {
  color: #bfbfbf;
}
/deep/ .ant-table-selection-menu {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}
/deep/ .ant-table-selection-menu .ant-action-down {
  color: #bfbfbf;
}
/deep/ .ant-table-selection-down:hover .anticon-down {
  color: #666;
}
/deep/ .ant-table-row-expand-icon {
  border: 1px solid rgba(122,149,194,0.24);
  background: #fff;
}
/deep/ tr.ant-table-expanded-row,
/deep/ tr.ant-table-expanded-row:hover {
  background: #fbfbfb;
}
/deep/ .ant-table-fixed-header > .ant-table-content > .ant-table-scroll > .ant-table-body {
  background: #fff;
}
/deep/ .ant-table-fixed-left,
/deep/ .ant-table-fixed-right {
  border-radius: 0;
}
/deep/ .ant-table-fixed-left table,
/deep/ .ant-table-fixed-right table {
  background: rgba(255, 255, 255, 0);
}
/deep/ .ant-table-fixed-header .ant-table-fixed-left .ant-table-body-outer .ant-table-fixed,
/deep/ .ant-table-fixed-header .ant-table-fixed-right .ant-table-body-outer .ant-table-fixed {
  border-radius: 0;
}
/deep/ .ant-table-fixed-left {
  box-shadow: 6px 0 6px -4px rgba(0, 0, 0, 0.15);
}
/deep/ .ant-table-fixed-left,
/deep/ .ant-table-fixed-left table {
  border-radius: 4px 0 0 0;
}
/deep/ .ant-table-fixed-left .ant-table-thead > tr > th:last-child {
  border-top-right-radius: 0;
}
/deep/ .ant-table-fixed-right {
  box-shadow: -6px 0 6px -4px rgba(0, 0, 0, 0.15);
}
/deep/ .ant-table-fixed-right,
/deep/ .ant-table-fixed-right table {
  border-radius: 0 4px 0 0;
}
/deep/ .ant-table-fixed-right .ant-table-expanded-row {
  color: transparent;
}
/deep/ .ant-table-fixed-right .ant-table-thead > tr > th:first-child {
  border-top-left-radius: 0;
}
/deep/ .ant-table.ant-table-scroll-position-left .ant-table-fixed-left {
  box-shadow: none;
}
/deep/ .ant-table.ant-table-scroll-position-right .ant-table-fixed-right {
  box-shadow: none;
}

//table的size属性取值为middle时的样式
/deep/ .ant-table-middle {
  border: 1px solid rgba(122,149,194,0.24);
  border-radius: 4px;
}
/deep/ .ant-table-middle > .ant-table-content > .ant-table-header > table,
/deep/ .ant-table-middle > .ant-table-content > .ant-table-body > table,
/deep/ .ant-table-middle > .ant-table-content > .ant-table-scroll > .ant-table-header > table,
/deep/ .ant-table-middle > .ant-table-content > .ant-table-scroll > .ant-table-body > table,
/deep/ .ant-table-middle > .ant-table-content > .ant-table-fixed-left > .ant-table-header > table,
/deep/ .ant-table-middle > .ant-table-content > .ant-table-fixed-right > .ant-table-header > table,
/deep/ .ant-table-middle > .ant-table-content > .ant-table-fixed-left > .ant-table-body-outer > .ant-table-body-inner > table,
/deep/ .ant-table-middle > .ant-table-content > .ant-table-fixed-right > .ant-table-body-outer > .ant-table-body-inner > table {
  border: 0;
}
/deep/ .ant-table-middle > .ant-table-content > .ant-table-header > table > .ant-table-thead > tr > th,
/deep/ .ant-table-middle > .ant-table-content > .ant-table-body > table > .ant-table-thead > tr > th,
/deep/ .ant-table-middle > .ant-table-content > .ant-table-scroll > .ant-table-header > table > .ant-table-thead > tr > th,
/deep/ .ant-table-middle > .ant-table-content > .ant-table-scroll > .ant-table-body > table > .ant-table-thead > tr > th,
/deep/ .ant-table-middle > .ant-table-content > .ant-table-fixed-left > .ant-table-header > table > .ant-table-thead > tr > th,
/deep/ .ant-table-middle > .ant-table-content > .ant-table-fixed-right > .ant-table-header > table > .ant-table-thead > tr > th,
/deep/ .ant-table-middle > .ant-table-content > .ant-table-fixed-left > .ant-table-body-outer > .ant-table-body-inner > table > .ant-table-thead > tr > th,
/deep/ .ant-table-middle > .ant-table-content > .ant-table-fixed-right > .ant-table-body-outer > .ant-table-body-inner > table > .ant-table-thead > tr > th {
  background:rgba(12,81,217,0.22) !important;
  border-bottom: 1px solid rgba(122,149,194,0.24);
}
/deep/ .ant-table-middle > .ant-table-content .ant-table-header {
  background: rgba(12,81,217,0.22) !important;
}
/deep/ .ant-table-middle > .ant-table-content .ant-table-placeholder,
/deep/ .ant-table-middle > .ant-table-content .ant-table-row:last-child td {
  border-bottom: 0;
}
/deep/ .ant-table-middle.ant-table-bordered {
  border-right: 0;
}
/deep/ .ant-table-middle.ant-table-bordered .ant-table-title {
  border: 0;
  border-bottom: 1px solid rgba(122,149,194,0.24);
  border-right: 1px solid rgba(122,149,194,0.24);
}
/deep/ .ant-table-middle.ant-table-bordered .ant-table-content {
  border-right: 1px solid rgba(122,149,194,0.24);
}
/deep/ .ant-table-middle.ant-table-bordered .ant-table-footer {
  border: 0;
  border-top: 1px solid rgba(122,149,194,0.24);
  border-right: 1px solid rgba(122,149,194,0.24);
}
/deep/ .ant-table-middle.ant-table-bordered .ant-table-placeholder {
  border-left: 0;
  border-bottom: 0;
}
/deep/ .ant-table-middle.ant-table-bordered .ant-table-thead > tr > th:last-child,
/deep/ .ant-table-middle.ant-table-bordered .ant-table-tbody > tr > td:last-child {
  border-right: none;
}
/deep/ .ant-table-middle.ant-table-bordered .ant-table-fixed-left .ant-table-thead > tr > th:last-child,
/deep/ .ant-table-middle.ant-table-bordered .ant-table-fixed-left .ant-table-tbody > tr > td:last-child {
  border-right: 1px solid rgba(122,149,194,0.24);
}
/deep/ .ant-table-middle.ant-table-bordered .ant-table-fixed-right {
  border-right: 1px solid rgba(122,149,194,0.24);
}

//table的size属性取值为small时的样式
/deep/ .ant-table-small > .ant-table-title {
  border-bottom: 1px solid rgba(122,149,194,0.24);
}
/deep/ .ant-table-small > .ant-table-content > .ant-table-header > table,
/deep/ .ant-table-small > .ant-table-content > .ant-table-body > table,
/deep/ .ant-table-small > .ant-table-content > .ant-table-scroll > .ant-table-header > table,
/deep/ .ant-table-small > .ant-table-content > .ant-table-scroll > .ant-table-body > table,
/deep/ .ant-table-small > .ant-table-content > .ant-table-fixed-left > .ant-table-header > table,
/deep/ .ant-table-small > .ant-table-content > .ant-table-fixed-right > .ant-table-header > table,
/deep/ .ant-table-small > .ant-table-content > .ant-table-fixed-left > .ant-table-body-outer > .ant-table-body-inner > table,
/deep/ .ant-table-small > .ant-table-content > .ant-table-fixed-right > .ant-table-body-outer > .ant-table-body-inner > table {
  border: 0;
}
/deep/ .ant-table-small > .ant-table-content > .ant-table-header > table > .ant-table-thead > tr > th,
/deep/ .ant-table-small > .ant-table-content > .ant-table-body > table > .ant-table-thead > tr > th,
/deep/ .ant-table-small > .ant-table-content > .ant-table-scroll > .ant-table-header > table > .ant-table-thead > tr > th,
/deep/ .ant-table-small > .ant-table-content > .ant-table-scroll > .ant-table-body > table > .ant-table-thead > tr > th,
/deep/ .ant-table-small > .ant-table-content > .ant-table-fixed-left > .ant-table-header > table > .ant-table-thead > tr > th,
/deep/ .ant-table-small > .ant-table-content > .ant-table-fixed-right > .ant-table-header > table > .ant-table-thead > tr > th,
/deep/ .ant-table-small > .ant-table-content > .ant-table-fixed-left > .ant-table-body-outer > .ant-table-body-inner > table > .ant-table-thead > tr > th,
/deep/ .ant-table-small > .ant-table-content > .ant-table-fixed-right > .ant-table-body-outer > .ant-table-body-inner > table > .ant-table-thead > tr > th {
  background:rgba(12,81,217,0.22) !important;
  border-bottom: 1px solid rgba(12,81,217,0.22) !important;
}
/deep/ .ant-table-small > .ant-table-content .ant-table-header {
  background: rgba(12,81,217,0.22) !important;
}
/deep/ .ant-table-small > .ant-table-content .ant-table-placeholder,
/deep/ .ant-table-small > .ant-table-content .ant-table-row:last-child td {
  border-bottom: 0;
}
/deep/ .ant-table-small.ant-table-bordered {
  border-right: 0;
}
/deep/ .ant-table-small.ant-table-bordered .ant-table-title {
  border: 0;
  border-bottom: 1px solid rgba(122,149,194,0.24);
  border-right: 1px solid rgba(122,149,194,0.24);
}
/deep/ .ant-table-small.ant-table-bordered .ant-table-content {
  border-right: 1px solid rgba(122,149,194,0.24);
}
/deep/ .ant-table-small.ant-table-bordered .ant-table-footer {
  border: 0;
  border-top: 1px solid rgba(122,149,194,0.24);
  border-right: 1px solid rgba(122,149,194,0.24);
}
/deep/ .ant-table-small.ant-table-bordered .ant-table-placeholder {
  border-left: 0;
  border-bottom: 0;
}
/deep/ .ant-table-small.ant-table-bordered .ant-table-thead > tr > th:last-child,
/deep/ .ant-table-small.ant-table-bordered .ant-table-tbody > tr > td:last-child {
  border-right: none;
}
/deep/ .ant-table-small.ant-table-bordered .ant-table-fixed-left .ant-table-thead > tr > th:last-child,
/deep/ .ant-table-small.ant-table-bordered .ant-table-fixed-left .ant-table-tbody > tr > td:last-child {
  border-right: 1px solid rgba(122,149,194,0.24);
}
/deep/ .ant-table-small.ant-table-bordered .ant-table-fixed-right {
  border-right: 1px solid rgba(122,149,194,0.24);
}

/deep/ .ant-table-thead > tr.ant-table-row-hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td,
/deep/ .ant-table-tbody > tr.ant-table-row-hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td,
/deep/ .ant-table-thead > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td,
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: rgba(64, 158, 255, 0.2) !important;;
}
/deep/ .ant-table-pagination.ant-pagination {
  margin: 16px 0 0 0 !important;
}
/deep/ .ant-pagination-total-text{
  color:#ffffff
}
/deep/ .ant-pagination-disabled a,
/deep/ .ant-pagination-disabled:hover a,
/deep/ .ant-pagination-disabled:focus a,
/deep/ .ant-pagination-disabled .ant-pagination-item-link,
/deep/ .ant-pagination-disabled:hover .ant-pagination-item-link,
/deep/ .ant-pagination-disabled:focus .ant-pagination-item-link{
  color:#ffffff
}
/deep/ .ant-pagination-item-active a{
  color:#ffffff
}
/deep/ .ant-pagination-item-active{
  background: #409eff;
  border-color: #409eff;
}
//table右侧--操作
.caozuo,/deep/ .caozuo{
  display: inline-block;
  width: 100%;
  text-align: center;
  a {
    color: #409eff !important;
  }
}

/deep/ .ant-table-row-cell-break-word span a {
  color: #409eff;
}

/*table表头样式*/
/deep/ .ant-table-thead > tr > th {
  text-align: center;
  white-space: nowrap;
}

/*table表体内容对齐方式、省略显示*/
/deep/ .ant-table-tbody > tr > td {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

::v-deep .ant-empty-description{
  color:#ffffff !important;
}
//table列表中，省略显示部分提供提示功能的样式
/deep/ .tooltip{
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  width: 100% !important;
  height: 100% !important;
  line-height: 100% !important;
  cursor: default;
}

/************ 分页样式 ************/
/deep/ .ant-pagination {
  font-size: 12px;
  color: #C4C4C4;
  font-weight: 400;
}

/deep/ .ant-pagination-item a,
/deep/ .ant-pagination-prev a,
/deep/ .ant-pagination-next a,
/deep/ .ant-pagination-jump-prev .ant-pagination-item-container .ant-pagination-item-ellipsis,
/deep/ .ant-pagination-jump-next .ant-pagination-item-container .ant-pagination-item-ellipsis,
/deep/ .ant-pagination-jump-prev .ant-pagination-item-container .ant-pagination-item-link-icon,
/deep/ .ant-pagination-jump-next .ant-pagination-item-container .ant-pagination-item-link-icon,
/deep/ .ant-pagination-jump-next .ant-pagination-item-container:hover {
  color: #C4C4C4;
}

/deep/ .ant-pagination-item-active {
  background: transparent;
  border: transparent;
}

/deep/ .ant-pagination-item-active a {
  color: #409EFF !important;
}

/deep/ .ant-pagination-item a,
/deep/ .ant-pagination-prev a,
/deep/ .ant-pagination-next a,
/deep/ .ant-pagination-item-link {
  background: transparent;
  border: transparent;
  color: #C4C4C4;
}

/deep/ .ant-pagination.mini .ant-pagination-prev,
/deep/ .ant-pagination.mini .ant-pagination-next {
  margin: 0 10px;
}

/deep/ .ant-pagination.mini .ant-pagination-options-quick-jumper input {
  height: 28px;
  line-height: 28px;
  text-align: center;
  background: transparent;
  border-color: #C4C4C4 !important;
  color: #C4C4C4;
}

/deep/ .ant-select-sm .ant-select-selection--single,
/deep/ .ant-select-sm .ant-select-selection__rendered {
  height: 28px;
  line-height: 28px;
}

/deep/ .ant-select-selection,
/deep/ .ant-select-dropdown {
  color: #C4C4C4;
  background-color: transparent;
}

/deep/ .ant-pagination-options .ant-select,
/deep/ .ant-pagination-options .ant-select-dropdown-menu-item {
  font-size: 12px;
  color: #C4C4C4;
  background-color: transparent;
}


/***********复选框样式*************/
// 鼠标hover时候的颜色
/deep/ .ant-checkbox-wrapper:hover .ant-checkbox-inner,
/deep/ .ant-checkbox:hover .ant-checkbox-inner,
/deep/ .ant-checkbox-input:focus + .ant-checkbox-inner{
   border: 2px solid #409EFF !important;
}

// 设置默认的颜色
/deep/ .ant-checkbox{
  .ant-checkbox-inner{
    border: 2px solid #409EFF;
    background-color: transparent;
  }
}

// 设置选中的颜色
/deep/ .ant-checkbox-checked .ant-checkbox-inner,
/deep/ .ant-checkbox-indeterminate .ant-checkbox-inner {
    background-color: transparent;
    border: 2px solid #409EFF;
}

/deep/ .ant-checkbox-inner::after{
  left: 20%;
}

