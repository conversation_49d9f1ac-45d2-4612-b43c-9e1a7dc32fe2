<template>
  <!-- <a-card :bordered="false"> -->
  <!-- 查询区域 -->
  <div :class="['table-container']">
    <!-- 操作按钮区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24" ref='row'>
          <a-col :span='spanValue'>
            <a-form-item label="时间范围">
              <a-range-picker class='a-range-picker-choice-date'
                @change="onChange"
                v-model="queryParam.createTimeRange"
                format="YYYY-MM-DD"
                :placeholder="['开始时间', '结束时间']"
              />
            </a-form-item>
          </a-col>
          <a-col :span='colBtnsSpan()'>
                <span class="table-page-search-submitButtons"
                      :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
              <a-button type="primary" class="btn-search btn-search-style" @click="searchQuery" >查询</a-button>
              <a-button class="btn-reset btn-reset-style" @click="mySearchReset" >重置</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- table区域-begin -->
    <a-table
        style="margin-right: 1px"
        ref="table"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
        :pagination="ipagination"
        :loading="loading"
        @change="handleTableChange">
<!--        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"-->
<!--      >-->
        <template slot="tooltip" slot-scope="text">
          <a-tooltip placement="topLeft" trigger="hover" overlayClassName='platformTableTooltip'>
            <template>
              <div slot='title' v-html='JSON.parse(text)' id="textHtml"></div>
            </template>
            <div class="tooltip">
              {{JSON.parse(text)}}
            </div>
          </a-tooltip>
        </template>
        <!-- 字符串超长截取省略号显示-->
        <span slot="templateContent" slot-scope="text">
          <j-ellipsis :value="text" :length="25"/>
        </span>
        <template slot="isOnline" slot-scope="text">
          <span v-if="text === '1'" style="font-size: 14px;   color: green">已启用</span>
          <span v-if="text === '0'" style="font-size: 14px;   color: red">未启用</span>
        </template>
        <span slot="action" slot-scope="text, record" class='caozuo'>
          <a @click="handleDetailPage(record)">查看</a>
        </span>
      </a-table>
  </div>
</template>

<script>
  import {JeecgListMixin} from '@/mixins/JeecgListMixin'
  import JEllipsis from '@/components/jeecg/JEllipsis'
  import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'

  export default {
    name: 'JournalManage',
    mixins: [JeecgListMixin,YqFormSearchLocation],
    components: {JEllipsis},
    data() {
      return {
        // 表头
        columns: [
          {
            title: '类型',
            dataIndex: 'type',
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 100px'
              return { style: cellStyle }
            },
          },
          {
            title: '时间',
            dataIndex: 'createTime',
            customCell: () => {
              let cellStyle = 'text-align: center;width: 180px'
              return { style: cellStyle }
            },
          },
          {
            title: '内容',
            dataIndex: 'content',
            scopedSlots: { customRender: 'tooltip' },
            customCell: () => {
              let cellStyle = 'text-align: left;min-width: 200px;max-width:600px'
              return { style: cellStyle }
            },
          },
          {
            title: '操作',
            dataIndex: 'action',
            fixed:'right',
            width: 100,
            align: 'center',
            scopedSlots: {customRender: 'action'}
          }
        ],
        url: {
          list: '/alarm/alarmTemplate/findDeviceOperationLog' //设备操作日志分页展示接口
        },
        queryParam: {
          createTimeRange: [],
          keyWord: '',
        },
        disableMixinCreated:true
      }
    },
    props: {
      deviceInfo: {
        type: Object,
        default: () => {
        },
      },
    },
    watch: {
      deviceInfo(newVal,oldVal) {
        this.init(newVal)
      }
    },
    activated() {
      if (this.queryParam.deviceId) {
        this.loadData()
      }
    },
    methods: {
      init(record){
        this.queryParam = {}
        this.queryParam.deviceId = record.id
        this.loadData();
      },
      handleDetailPage: function (record) {
        this.$parent.pButton2(1, record);
      },
      //重置搜索框
      mySearchReset() {
        this.init(this.deviceInfo)
      },
      //时间
      onChange(date, dateString) {
        this.queryParam.alarmTime2_begin = dateString[0]
        this.queryParam.alarmTime2_end = dateString[1]
      }
    }
  }
</script>
<style lang="less" scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
.table-container {
  background-color: #fff;
  height: 100%;
  overflow: hidden;
  overflow-y: auto;
}
  /** Button按钮间距 */
  .ant-btn {
    margin-left: 3px;
  }

  .ant-card-body .table-operator {
    margin-bottom: 18px;
  }

  .ant-table-tbody .ant-table-row td {
    padding-top: 15px;
    padding-bottom: 15px;
  }

  .anty-row-operator button {
    margin: 0 5px;
  }

  .ant-btn-danger {
    background-color: #ffffff;
  }

  .ant-modal-cust-warp {
    height: 100%;
  }

  .ant-modal-cust-warp .ant-modal-body {
    height: calc(100% - 110px) !important;
    overflow-y: auto;
  }

  .ant-modal-cust-warp .ant-modal-content {
    height: 90% !important;
    // overflow-y: hidden;
  }

  .table-page-search-wrapper {
    background-color: #fff;
    padding: 15px 0 0 15px;
    margin-right: 1px;
  }



  .query-btn {
    background: #ecf5ff;
    border: 1px solid #b3d8ff;
    border-radius: 4px;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #409eff;
    width: 73px;
    height: 28px;
    cursor: pointer;
    margin: 0px;
  }

/*  .reset-btn {
    background: white;
    border-radius: 4px;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #737578;
    width: 73px;
    height: 28px;
    border: 1px solid #dcdfe6;
    cursor: pointer;
    margin: 0 0 0 10px;
    height: 32px;
  }*/

</style>
