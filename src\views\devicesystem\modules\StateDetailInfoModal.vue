<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    :centered='true'
    switchFullscreen
    :destroyOnClose="true"
    @ok="handleOk"
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @cancel="handleCancel"
    cancelText="关闭">
    <state-detail-info-form ref="realForm" @ok="submitCallback" :disabled="disableSubmit"></state-detail-info-form>
  </j-modal>
</template>

<script>
  import stateDetailInfoForm from './StateDetailInfoForm.vue'
  export default {
    name: 'stateDetailInfoModal',
    components: {
      stateDetailInfoForm
    },
    data () {
      return {
        title:'',
        width:1000,
        visible: false,
        disableSubmit: false
      }
    },
    methods: {
      edit (record) {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.edit(record);
        })
      },
      close () {
        this.$emit('close');
        this.visible = false;
      },
      handleOk () {
        this.close();
      },
      submitCallback(){
        this.$emit('ok');
        this.visible = false;
      },
      handleCancel () {
        this.close()
      }
    }
  }
</script>
<style scoped lang='less'>
@import '~@assets/less/normalModal.less';
</style>