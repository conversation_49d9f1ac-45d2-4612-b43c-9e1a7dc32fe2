<template>
  <div style='height: 100%'>
    <keep-alive  exclude='DeviceInfoModal'>
      <component :is="pageName" :data="data" :render-states='renderStates'/>
    </keep-alive>
  </div>
</template>

<script>
  import GatewayDeviceInfoList from './GatewayDeviceInfoList'
  // import deviceInfoModal from './deviceshow/DeviceInfoModal'
  import DeviceInfoModal from '@views/devicesystem/deviceshow/DeviceInfoModal'
  export default {
    name: "GatewayDeviceInfoLManage",
    data() {
      return {
        isActive: 0,
        data:{},
        renderStates:{
          showBaseInfo:true,
          showStateInfo:true,
          showDeviceFunction:true,
          showJournal:true,
          showAlarm:true,
          showDeviceAlarm:true,
        },
      };
    },
    inject: ['reload'],
    components: {
      GatewayDeviceInfoList,
      DeviceInfoModal
    },
    created(){
      this.pButton1(0);
    },
    //使用计算属性
    computed: {
      pageName() {
        switch (this.isActive) {
          case 0:
            return "GatewayDeviceInfoList";
            break;
          case 1:
            return "DeviceInfoModal";
            break;
        }
      }
    },
    methods: {
      pButton1(index,item) {
        this.isActive = index;
        this.data = item;
      },
      pButton2(index,item) {
        this.data = item;
        this.isActive = index
        // this.reload();
      }
    }
  }
</script>