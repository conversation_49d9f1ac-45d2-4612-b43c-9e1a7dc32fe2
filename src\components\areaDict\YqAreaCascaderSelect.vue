<template>
  <a-cascader :field-names="{ label: 'text', value: 'id', children: 'children' }"
    :getPopupContainer="(target) => target.parentNode" :options="dictOptions" :placeholder="placeholder"
    :expandTrigger="expandTrigger" :disabled="disabled" :allowClear="allowClear" :changeOnSelect="changeOnSelect"
    :value="showData" @change="onChange" />
</template>

<script>
  import {
    getAreaTree
  } from '@/api/api'
  import {
    getRootAreaId
  } from '@/components/dict/JDictSelectUtil'

  export default {
    name: "YqAreaCascaderSelect", //行政区划级联选择器
    props: {
      //根节点的父id，不指定将使用index.html中的全局配置
      pid: String,
      //输入框占位文本
      placeholder: String,
      //次级菜单的展开方式，可选 'click' 和 'hover'
      expandTrigger: {
        type: String,
        default: "click",
        required: false
      },
      //禁用
      disabled: <PERSON>ole<PERSON>,
      //是否支持清除
      allowClear: {
        type: Boolean,
        default: true,
        required: false
      },
      //当此项为 true 时，点选每级菜单选项值都会发生变化
      changeOnSelect: {
        type: Boolean,
        default: false,
        required: false
      },
      //指定选中项
      value: String,
    },
    data() {
      return {
        dictOptions: [],
        showData: []
      }
    },
    watch: {
      pid: {
        immediate: true,
        handler() {
          this.initDictData()
        },
      },
      value: {
        immediate: true,
        handler() {
          this.initShowData()
        },
      },
    },
    created() {},
    methods: {
      //初始化行政区划数据字典
      async initDictData() {
        //根据区域pid, 初始化行政区划树
        let pid = this.pid
        if (!pid) {
          //未设置根节点父id，则使用默认全局配置
          await getRootAreaId().then(res => {
            pid = res.data
          })
        }
        getAreaTree({
          pid: pid
        }).then((res) => {
          if (res.success) {
            if (res.result) {
              //删除子节点为空的children属性
              this.dictOptions = this.IterationDelateNullChildren(res.result)
              if (this.value) {
                //如果当前数据不为空，初始化显示数据
                this.initShowData()
              }
            }
          }
        })
      },
      //删除子节点为空的children属性
      IterationDelateNullChildren(arr) {
        if (arr.length) {
          for (let i in arr) {
            if (arr[i].children.length) {
              this.IterationDelateNullChildren(arr[i].children)
            } else {
              delete arr[i].children;
            }
          }
        }
        return arr
      },


      //初始化显示数据
      initShowData() {
        var array = []
        if (this.value) {
          array = this.findIndexArray(this.dictOptions, this.value, [])
        }
        if (array) {
          this.showData = array
        } else {
          this.showData = []
        }
      },

      findIndexArray(data, id, indexArray) {
        let arr = Array.from(indexArray)
        for (let i = 0, len = data.length; i < len; i++) {
          arr.push(data[i].pid)
          if (data[i].id === id) {
            arr.push(data[i].id)
            if (arr.length > 1) {
              arr.shift();
            }
            return arr
          }
          let children = data[i].children
          if (children && children.length) {
            let result = this.findIndexArray(children, id, arr)
            if (result) {
              return result
            }
          }
          arr.pop()
        }
        return false
      },

      onChange(e) {
        let val;
        val = e
        if (!e || e.length == 0) {
          this.showData = []
          this.$emit('change', "");
        } else {
          let cityStr = ''
          var end = val[val.length - 1]
          this.dictOptions.forEach((ele, i) => {
            if (this.dictOptions[i].id == end) {
              cityStr = this.dictOptions[i].text
            } else if (ele.children != null && ele.children.length > 0) {
              ele.children.forEach((item, index) => {
                if (ele.children[index].id == end) {
                  cityStr = ele.children[index].text
                }
              })
            }
          });
          this.$emit('change', end, cityStr);
        }

      },
      //手动设置选项
      setCurrentDictOptions(dictOptions) {
        this.dictOptions = dictOptions
      },
      //获取当前选项列表
      getCurrentDictOptions() {
        return this.dictOptions
      }
    },
    model: {
      prop: 'value',
      event: 'change'
    }
  }
</script>

<style scoped>
</style>