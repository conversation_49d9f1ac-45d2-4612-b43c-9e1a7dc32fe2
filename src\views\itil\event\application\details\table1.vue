<template>
  <div style="overflow: hidden; overflow-x: auto">
    <table class="gridtable">
      <tr>
        <td class="leftTd" style="font-size: 14px">编 码</td>
        <td class="rightTd" style="font-size: 14px">{{ data.processNumber }}</td>
        <td class="leftTd" style="font-size: 14px">标 题</td>
        <td class="rightTd" style="font-size: 14px">{{ data.title }}</td>
      </tr>

      <tr>
        <td class="leftTd" style="font-size: 14px">类 型</td>
        <td class="rightTd" style="font-size: 14px">
          <span>
            <div>{{ getEvent(data) }}</div>
          </span>
        </td>
        <td class="leftTd" style="font-size: 14px">优先级</td>
        <td class="rightTd" style="font-size: 14px">
          <span>
            <div v-if="data.priority == 0" style="color: #ffb300">低</div>
            <div v-if="data.priority == 1" style="color: #fc7611">中</div>
            <div v-if="data.priority == 2" style="color: #df1a1a">高</div>
          </span>
        </td>
      </tr>

      <tr>
        <td class="leftTd" style="font-size: 14px">创建人</td>
        <td class="rightTd" style="font-size: 14px">{{ data.createBy }}</td>
        <td class="leftTd" style="font-size: 14px">联系电话</td>
        <td class="rightTd" style="font-size: 14px">{{ data.contact }}</td>
      </tr>

      <tr>
        <td class="leftTd" style="font-size: 14px">状 态</td>
        <td class="rightTd" style="font-size: 14px">
          <span>
            <div v-if="data.status == 0">新建</div>
            <div v-if="data.status == 1">审批</div>
            <div v-if="data.status == 2">处理</div>
            <div v-if="data.status == 3">审核</div>
            <div v-if="data.status == 4">关闭</div>
            <div v-if="data.status == 5">退回</div>
          </span>
        </td>
        <td class="leftTd" style="font-size: 14px">创建时间</td>
        <td class="rightTd" style="font-size: 14px">{{ data.createTime }}</td>
      </tr>

      <tr>
        <td class="leftTd" style="font-size: 14px">SAL响应时间</td>
        <td class="rightTd" style="font-size: 14px">{{ data.slaResponse }}</td>
        <td class="leftTd" style="font-size: 14px">SAL完成时间</td>
        <td class="rightTd" style="font-size: 14px">{{ data.slaAccomplish }}</td>
      </tr>

      <tr>
        <td class="leftTd" style="font-size: 14px">实际响应时间</td>
        <td class="rightTd" style="font-size: 14px">{{ data.assignedTime }}</td>
        <td class="leftTd" style="font-size: 14px">实际完成时间</td>
        <td class="rightTd" style="font-size: 14px">{{ data.completeTime }}</td>
      </tr>

      <tr>
        <td class="leftTd" style="font-size: 14px">事件来源</td>
        <td class="rightTd" style="font-size: 14px">
          <span>
            <div v-if="data.eventSource == 0">手动创建</div>
            <div v-if="data.eventSource == 1">自动创建</div>
          </span>
        </td>
        <td class="leftTd"></td>
        <td class="rightTd"></td>
      </tr>

      <tr>
        <td class="leftTd" style="font-size: 14px">事件描述</td>
        <td class="rightTd" colspan="3" style="font-size: 14px">{{ data.describes }}</td>
      </tr>

      <tr>
        <td class="leftTd" style="font-size: 14px">附 件</td>
        <td class="rightTd" colspan="3" style="padding-bottom: 0; font-size: 14px">
          <div v-for="(item, index) in data.files" style="float: left; margin-left: 10px">
            <!--            {{item}}-->
            <div v-if="
                item.suffix == 'png' ||
                item.suffix == 'jpg' ||
                item.suffix == 'jpeg' ||
                item.suffix == 'gif' ||
                item.suffix == 'ico' ||
                item.suffix == 'bmp' ||
                item.suffix == 'pic' ||
                item.suffix == 'tif'
              ">
              <div class="orientation" style="float: none">
                <img :src="urla + item.url" alt="" id="urla" />
                <span class="font" @click="fontClick(item.url)">下载文件</span>
              </div>
            </div>
            <div v-else-if="item.suffix == 'pdf'">
              <div class="orientationFile" style="float: none">
                <img src="@/assets/img/pdf.png" alt="" id="urlas" />
                <span class="font1" @click="fontClick(item.url)">下载文件</span>
              </div>
            </div>
            <div v-else-if="item.suffix == 'doc'">
              <div class="orientationFile">
                <img src="@/assets/img/doc.png" alt="" id="urlas" />
                <span class="font1" @click="fontClick(item.url)">下载文件</span>
              </div>
            </div>
            <div v-else-if="item.suffix == 'docx'">
              <div class="orientationFile">
                <img src="@/assets/img/docx.png" alt="" id="urlas" />
                <span class="font1" @click="fontClick(item.url)">下载文件</span>
              </div>
            </div>
            <div v-else-if="item.suffix == 'txt'">
              <div class="orientationFile">
                <img src="@/assets/img/txt.png" alt="" id="urlas" />
                <span class="font1" @click="fontClick(item.url)">下载文件</span>
              </div>
            </div>
            <div v-else>
              <a @click="fontClick(item.url)" style="color: #40a9ff">{{ item.fileName }}</a>
            </div>
          </div>
        </td>
      </tr>
    </table>
    <div v-if="show == '1'">
      <div style="display: flex; justify-content: flex-end; margin: 30px 0 20px 0">
        <a-button type="primary" @click="buttonClick(2)"> 加入知识库 </a-button>
        <a-button type="primary" @click="buttonClick(1)" style="margin-left: 10px"> 提出问题 </a-button>
      </div>
    </div>
  </div>
</template>
<script>
  import JFormContainer from '@/components/jeecg/JFormContainer'
  import {
    httpAction,
    getAction,
    postAction, downloadFile
  } from '@/api/manage'
  import {
    ajaxGetDictItems
  } from '@/api/api'
  export default {
    // 基本信息
    name: 'table1',
    components: {
      JFormContainer,
    },
    data() {
      return {
        data: {},
        visible: false,
        valueNew: {},
        urla: window._CONFIG['downloadUrl'] + '/',
        returnValue: {},
        show: 0,
        typeOptions: [],
      }
    },
    created() {},
    methods: {
      getEvent(data, index) {
        let e1 = this.typeOptions.find((el) => data.eventType == el.value);
        if (e1 != null) {
          return e1.text
        } else {
          return "";
        }
      },
      getData(value, show) {
        // this.visible = true
        this.show = show
        this.returnValue = value
        getAction('/event/getEventDetails', {
          busId: this.returnValue.busId
        }).then((res) => {
          if (res.success) {
            this.data = res.result
          }
        })
        ajaxGetDictItems('event_Request_Type', null).then((res) => {
          if (res.success) {
            this.typeOptions = res.result
          }
        })
      },
      fontClick(path) {
        downloadFile(window._CONFIG['domianURL'] + '/sys/common/downloadFile/' + path,path)
      },
      buttonClick(index) {
        if (index == 1) {
          this.$router.push({
            path: '/question/application',
            query: {
              eventId: this.returnValue.busId
            }
          })
        } else {
          let formData = {}
          formData.busId = this.returnValue.busId
          formData.procInstId = this.returnValue.procInstId

          postAction('/event/saveUmpKnowledge', formData).then((res) => {
            this.uploading = false
            if (res.success) {
              this.$message.success(res.message)
            } else {
              this.$message.warning(res.message)
            }
          })
        }
      },
    },
  }
</script>
<style scoped>
  table.gridtable {
    white-space: nowrap;
    font-family: verdana, arial, sans-serif;
    font-size: 11px;
    color: #606266;
    border-width: 1px;
    border-color: #e8e8e8;
    border-collapse: collapse;
    text-align: left;
    width: 100%;
  }

  table.gridtable td {
    border-width: 1px;
    border-style: solid;
    border-color: #e8e8e8;
  }

  .leftTd {
    width: 17%;
    background-color: #fafafa;
    padding: 16px 24px;
    text-align: center;
  }

  .rightTd {
    width: 35%;
    padding: 16px 24px;
  }

  #urla {
    width: 100px;
    height: 100px;
  }

  #urlas {
    width: 100px;
    height: 100px;
  }

  .orientation {
    width: 100%;
    /*margin: 0 auto;*/
    position: relative;
    overflow: hidden;
  }

  .orientationFile {
    width: 100%;
    /*margin: 0 auto;*/
    position: relative;
    overflow: hidden;
  }

  .font {
    position: absolute;
    bottom: 0;
    background: rgba(0, 0, 0, 0.75);
    left: 0;
    width: 100%;
    height: 30%;
    color: #fff;
    line-height: 32px;
    cursor: pointer;
    transform: translateY(109%);
    transition: all 0.3s ease-out 0s;
    text-align: center;
  }

  .font1 {
    position: absolute;
    bottom: 0;
    background: rgba(0, 0, 0, 0.75);
    left: 0;
    width: 100%;
    height: 30%;
    color: #fff;
    line-height: 32px;
    cursor: pointer;
    transform: translateY(109%);
    transition: all 0.3s ease-out 0s;
    text-align: center;
  }

  .orientation:hover .font {
    transform: translateY(0%);
  }

  .orientationFile:hover .font1 {
    transform: translateY(0%);
  }
</style>