<template>
  <a-card style="height: 100%;overflow: auto">
    <a-row>
      <a-col :span="24">
        <span style="float: right;margin-bottom: 12px;"><img src="~@/assets/return1.png" alt="" @click="getGo"
            style="width: 20px;height: 20px;cursor: pointer"></span>
      </a-col>
      <a-col :span="24">
        <table class="gridtable">
          <tr>
            <td class="leftTd">合同编号</td>
            <td class="rightTd">{{ record.code }}</td>
            <td class="leftTd">合同名称</td>
            <td class="rightTd">{{ record.name }}</td>
          </tr>
          <tr>
            <td class="leftTd">合同类型</td>
            <td class="rightTd">{{ record.type }}</td>
            <td class="leftTd">合同金额</td>
            <td class="rightTd">{{record.amount}}</td>
          </tr>
          <tr>
            <td class="leftTd">合同描述</td>
            <td colspan='3' class="rightTd">{{ data.description }}</td>
          </tr>
        </table>
      </a-col>
    </a-row>
  </a-card>
</template>

<script>
export default {
  name: 'contractInfo',
  props: {
    data: {
      type: Object
    }
  },
  data() {
    return {
      record: {}
    }
  },
  mounted() {
    this.record = this.data
  },
  methods: {
    //返回上一级
    getGo() {
      this.$parent.pButton1(0)
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/lookPage.less';
</style>