<template>
  <a-row :gutter='10' style='height: 100%;' class='vScroll'>
    <a-col style='width:100%;height: 100%;display: flex;flex-direction: column'>
      <!-- 查询区域 -->
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0', marginRight: '12px' }" class='card-style'
        style='width: 100%'>
        <div class='table-page-search-wrapper'>
          <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
            <a-row :gutter='24' ref='row'>
              <a-col :span='spanValue'>
                <a-form-item label='名称'>
                  <a-input :maxLength='maxLength' placeholder='请输入名称' v-model='queryParam.name' :allowClear='true' autocomplete='off' />
                </a-form-item>
              </a-col>
<!--              <a-col :span='spanValue'>-->
<!--                <a-form-item label='状态'>-->
<!--                  <a-select v-model="queryParam.status" :allowClear='true' placeholder='请选择状态'>-->
<!--                    <a-select-option :value="1">正常</a-select-option>-->
<!--                    <a-select-option :value="2">禁用</a-select-option>-->
<!--                  </a-select>-->
<!--                </a-form-item>-->
<!--              </a-col>-->
              <a-col :span='colBtnsSpan()'>
                <span class='table-page-search-submitButtons'
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button type='primary' class='btn-search btn-search-style' @click='searchQuery'>查询</a-button>
                  <a-button class='btn-reset btn-reset-style' @click='searchReset'>重置</a-button>
                  <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <div class='table-operator table-operator-style'>
          <a-button @click="handleAdd">新增</a-button>
          <a-dropdown v-if="selectedRowKeys.length > 0">
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item key="1" @click="batchDel">
                删除
              </a-menu-item>
            </a-menu>
            <a-button>批量操作
              <a-icon type="down" />
            </a-button>
          </a-dropdown>
        </div>
        <a-table ref='table' bordered row-key='id' :columns='columns' :dataSource='dataSource'
          :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}" :pagination='ipagination' :loading='loading'
          :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }" @change='handleTableChange'>
          <span slot='action' class='caozuo' slot-scope='text, record'>
            <a @click="handleEdit(record)">编辑</a>
            <a-divider type="vertical" />
            <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
              <a>删除</a>
            </a-popconfirm>
          </span>
        </a-table>
      </a-card>
    </a-col>
    <migrate-modal ref='modalForm' @ok='modalFormOk'></migrate-modal>
  </a-row>
</template>

<script>
  import {
    mixinDevice
  } from '@/utils/mixin'
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import migrateModal from './modules/migrateModal.vue'
  import {
    YqFormSearchLocation
  } from '@/mixins/YqFormSearchLocation'
  import { deleteAction } from '@api/manage'
  export default {
    name: 'nodeMonitorList',
    mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
    components: {
      migrateModal,
    },
    data() {
      return {
        maxLength:50,
        description: '数据源管理页面',
        formItemLayout: {
          labelCol: {
            style: 'width:80px'
          },
          wrapperCol: {
            style: 'width:calc(100% - 80px)'
          }
        },
        dataSource: [],
        // 表头
        columns: [
          {
            title: '名称',
            dataIndex: 'name',
            customCell: () => {
              let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '连接Url',
            dataIndex: 'url'
          },
          // {
          //   title: '用户名',
          //   dataIndex: 'user',
          // },
          // {
          //   title: '密码',
          //   dataIndex: 'password',
          //   customRender: (text) => {
          //     return text ? '******' : ''
          //   }
          // },
          // {
          //   title: '类型',
          //   dataIndex: 'type',
          //   customCell: () => {
          //     let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
          //     return {
          //       style: cellStyle
          //     }
          //   }
          // },
          {
            title: '服务器地址',
            dataIndex: 'serverHost'
          },
          {
            title: '版本',
            dataIndex: 'version'
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            fixed: 'right',
            width: 140,
            scopedSlots: {
              customRender: 'action'
            }
          }
        ],
        url: {
          list: '/distributedStorage/transfer/datasource',
          delete: '/distributedStorage/transfer/datasource',
          deleteBatch: '/distributedStorage/transfer/datasource/batch',
        },
      }
    },
    mounted() {},
    methods: {
      // 批量删除
      batchDel: function () {
        if (!this.url.deleteBatch) {
          this.$message.error('请设置url.deleteBatch属性!')
          return
        }
        if (this.selectedRowKeys.length <= 0) {
          this.$message.warning('请选择一条记录！')
          return
        } else {
          var ids = ''
          for (var a = 0; a < this.selectedRowKeys.length; a++) {
            ids += this.selectedRowKeys[a] + ','
          }
          var that = this
          this.$confirm({
            title: '确认删除',
            okText: '是',
            cancelText: '否',
            content: '是否删除选中数据?',
            onOk: function () {
              that.loading = true
              deleteAction(that.url.deleteBatch, { datasourceIds: ids })
                .then((res) => {
                  if (res.success) {
                    //重新计算分页问题
                    that.reCalculatePage(that.selectedRowKeys.length)
                    that.$message.success(res.message)
                    that.loadData()
                    that.onClearSelected()
                  } else {
                    that.$message.warning(res.message)
                  }
                })
                .finally(() => {
                  that.loading = false
                })
            }
          })
        }
      },
      // 删除
      handleDelete: function (id) {
        if (!this.url.delete) {
          this.$message.error('请设置url.delete属性!')
          return
        }
        var that = this
        deleteAction(that.url.delete, { datasourceId: id }).then((res) => {
          if (res.success) {
            //重新计算分页问题
            that.reCalculatePage(1)
            that.$message.success(res.message)
            that.loadData()
          } else {
            that.$message.warning(res.message)
          }
        })
      },
    }
  }
</script>
<style lang='less' scoped>
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';

  .stateBox {
    margin-left: 20px;
  }

  .stateImg {
    vertical-align: middle
  }

  .alarmStatus {
    margin-left: 8px;
  }

  .overlay {
    color: #409eff
  }
</style>