<template>
  <a-spin :spinning="confirmLoading">
    <a-tabs ref="tabs" v-model="activeKey" @change="onTabChange">
      <a-tab-pane key="1" tab="基础信息">
        <j-form-container style="margin-top: 30px">
          <a-form :form="form" slot="detail">
            <a-row>
              <a-col :span="24">
                <a-form-item label="指标名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input
                    v-decorator.trim="['metricsName', validatorRules.metricsName]"
                    placeholder="请输入指标名称"
                    :allowClear="true"
                    autocomplete="off"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item label="指标类别" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-select
                    :getPopupContainer="(node) => node.parentNode"
                    v-decorator="['metricsTypeId', validatorRules.metricsTypeId]"
                    :allowClear="true"
                    placeholder="请选择指标类别"
                    :disabled="disabledCategory"
                  >
                    <a-select-option v-for="(item, index) in categoryList" :key="item.id">
                      {{ item.typeName }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item label="指标序号" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input-number
                    style="width: 200px"
                    :min='0' :max="9999999999999999" :precision='0' :step="1"
                    v-decorator="['sortOrder', validatorRules.sortOrder]"
                    placeholder="请输入指标序号"
                    :allowClear="true"
                    autocomplete="off"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item label="是否重点关注" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-radio-group v-decorator="['isFocus', { initialValue: 0 }]">
                    <a-radio :value="1">是</a-radio>
                    <a-radio :value="0">否</a-radio>
                  </a-radio-group>
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item label="考察要点" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-textarea
                    style="width: 100%"
                    v-decorator="['investigatePoint', validatorRules.investigatePoint]"
                    :autoSize="{ minRows: 2, maxRows: 4 }"
                    :allow-clear="true"
                    autocomplete="off"
                    placeholder="请输入考察要点"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item label="监控要点" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-textarea
                    style="width: 100%"
                    v-decorator="['monitorPoint', validatorRules.monitorPoint]"
                    :autoSize="{ minRows: 2, maxRows: 4 }"
                    :allow-clear="true"
                    autocomplete="off"
                    placeholder="请输入监控要点"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item label="现地核查要点" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-textarea
                    style="width: 100%"
                    v-decorator="['localCheckPoint', validatorRules.localCheckPoint]"
                    :autoSize="{ minRows: 2, maxRows: 4 }"
                    :allow-clear="true"
                    autocomplete="off"
                    placeholder="请输入现地核查要点"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item label="指标备注" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-textarea
                    style="width: 100%"
                    v-decorator="['metricsDesc', validatorRules.metricsDesc]"
                    :autoSize="{ minRows: 2, maxRows: 4 }"
                    :allow-clear="true"
                    autocomplete="off"
                    placeholder="请输入指标备注"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </j-form-container>
      </a-tab-pane>
      <a-tab-pane key="2" tab="表单配置" forceRender>
        <k-form-design ref="KForm" :toolbars="toolbars" @save="save($event, 'parent')" hideResetHint hideBackBtn />
      </a-tab-pane>
      <a-tab-pane key="3" tab="评估规则" forceRender>
        <div class="box-wrapper">
          <div v-if="generateStartFormVisible">
            <k-form-build
              ref="generateStartForm"
              :value="jsonData"
              :defaultValue="variables"
              :key="formKey"
            />
          </div>
          <div v-else>
            <div style="text-align: center; padding: 50px;">
              <a-spin size="large" />
              <div style="margin-top: 10px;">正在加载评估规则表单...</div>
            </div>
          </div>
        </div>
      </a-tab-pane>
    </a-tabs>
  </a-spin>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import pick from 'lodash.pick'
import JFormContainer from '@/components/jeecg/JFormContainer'
import {
  ValidateRequiredFields,
  ValidateOptionalFields
} from '@/utils/rules.js'
export default {
  name: 'metricsForm',
  components: {
    JFormContainer,
  },
  props: ['metricsTypeId'],
  data() {
    return {
      form: this.$form.createForm(this),
      model: {},
      userId: null,
      labelCol: {
        xs: {
          span: 24,
        },
        sm: {
          span: 5,
        },
      },
      wrapperCol: {
        xs: {
          span: 24,
        },
        sm: {
          span: 16,
        },
      },
      confirmLoading: false,
      validatorRules: {
        metricsName: {
          rules: [
            {
              required: true,
              validator: (rule, value, callback) =>ValidateRequiredFields(rule, value, callback,'指标名称',30,1)
            }
          ]
        },
        metricsCode: {
          rules: [
            {
              required: true,
              validator: (rule, value, callback) =>ValidateRequiredFields(rule, value, callback,'指标标识',50,1)
            }
          ]
        },
        metricsTypeId: {
          rules: [
            {
              required: true,
              message: '请选择指标类别',
            },
          ],
        },
        sortOrder: {
          rules: [
            {
              required: true,
              message: '请输入指标序号',
            }
          ]
        },
        investigatePoint: {
          rules: [
            { required: false, validator: (rule, value, callback) =>ValidateOptionalFields(rule, value, callback,'考察要点',255) },
          ],
        },
        monitorPoint: {
          rules: [
            { required: false, validator: (rule, value, callback) =>ValidateOptionalFields(rule, value, callback,'监控要点',255)},
          ],
        },
        localCheckPoint: {
          rules: [
            { required: false, validator: (rule, value, callback) =>ValidateOptionalFields(rule, value, callback,'现地核查要点',255) },
          ],
        },
        metricsDesc: {
          rules: [
            { required: false, validator: (rule, value, callback) =>ValidateOptionalFields(rule, value, callback,'指标备注',255)},
          ],
        },
      },
      url: {
        add: '/devops/metricsInfo/add',
        edit: '/devops/metricsInfo/edit',
        getCategoryList: '/evaluate/metricsType/list', // 获取全部类别
        queryByIdUrl: '/flowable/form/queryById', // 获取配置的评估规则数据
      },
      activeKey: '1',
      disabledCategory: false, // 设置禁用类别
      categoryList: [], // 获取指标类别
      generateStartFormVisible: false,
      toolbars: [
        // 'save',
        'preview',
        'importJson',
        'exportJson',
        'exportCode',
        'reset',
        'close',
        'undo',
        'redo',
        // 'toList',
      ],
      jsonData: { list: [] }, // 评估规则表单
      fieldOptions: null,
      formKey: Date.now(), // 用于强制重新渲染表单
      variables: {} // 确保 variables 初始化
    }
  },
  created() {
    this.getCategoryList()
  },
  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      this.form.resetFields()
      this.model = Object.assign({}, record)

      // 回显自定义表单数据
      setTimeout(() => this.$refs.KForm.setJSON(record.metricsForm ? record.metricsForm : undefined), 1000)

      // 获取评估规则的表单模版
      if (record.metricsRules) {
        // 如果已经保存了模版，获取保存的模版
        this.jsonData = JSON.parse(record.metricsRules)
        // 获取评估规则的表单数据
        this.getRuleValues()
      } else {
        // 从服务中心配置表单中获取通用模版
        this.getRulesFormData('evaluationRules')
      }

      if (!this.model.isFocus) {
        this.model.isFocus = 0
      }
      this.$nextTick(() => {
        this.form.setFieldsValue(
          pick(
            this.model,
            'metricsName',
            'metricsCode',
            'metricsTypeId',
            'sortOrder',
            'isFocus',
            'investigatePoint',
            'monitorPoint',
            'localCheckPoint',
            'metricsDesc'
          )
        )
      })
      if (this.metricsTypeId) {
        this.disabledCategory = true
        this.$nextTick(() => {
          this.form.setFieldsValue({ metricsTypeId: this.metricsTypeId })
        })
      }
    },
    submitForm() {
      // 获取自定义表单的配置
      this.$refs.KForm.handleSave()
    },
    async submitForm2(KFormJSON) {
      const that = this
      // 基础信息校验是否通过
      let hasError = false
      // 评估规则校验是否通过
      let rulesHasError = false
      let formData = {}
      // 触发基础信息表单验证
      that.form
        .validateFields((err, values) => {
          if (err) {
            formData = Object.assign(that.model, values)
            hasError = true
            that.$message.error('请完善基础信息')
            that.$nextTick(() => {
              that.activeKey = '1' // 切换到基础信息tab
            })
          }
          formData = Object.assign(that.model, values)
        })
        .catch(() => {
          hasError = true
        })
      if (hasError) {
        return
      }
      // 触发评估规则表单验证
      const formRef = this.$refs.generateStartForm
      if (formRef) {
        await formRef.getData().then(res => {
          // console.log("getData res === ", res)
          formData.rulesValues = res
        }).catch(err=>{
          this.$message.error(`请完善评估规则表单`)
          rulesHasError = true
          this.activeKey = '3'
        })
        // try {
        //   // rulesValues字段保存评估规则填写的数
        //   formData.rulesValues = await  formRef.getData()
        //   // return
        // } catch (err) {
        //   this.$message.error(`请完善评估规则表单`)
        //   rulesHasError = true
        //   // 切换到评估规则tab
        //   this.activeKey = '3'
        // }
      }
      if (rulesHasError) {
        return
      }

      that.confirmLoading = true
      let httpurl = ''
      let method = ''
      if (!that.model.id) {
        httpurl += that.url.add
        method = 'post'
      } else {
        httpurl += that.url.edit
        method = 'put'
      }
      // metricsForm指标的自定义表单的模版
      formData.metricsForm = KFormJSON
      // metricsRules存储规则的模板
      formData.metricsRules = JSON.stringify(that.jsonData)

      httpAction(httpurl, formData, method)
        .then((res) => {
          if (res.success) {
            that.$message.success(res.message)
            that.$emit('ok')
          } else {
            that.$message.warning(res.message)
          }
        })
        .finally(() => {
          that.confirmLoading = false
        })
    },
    // 获取全部类别
    getCategoryList() {
      getAction(this.url.getCategoryList, {}).then((res) => {
        if (res.success) {
          this.categoryList = res.result
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    save($event) {
      // console.log('保存表单内容==', $event)
      this.submitForm2($event)
    },
    // 获取评估规则的表单
    getRulesFormData(formKey) {
      if (!formKey) {
        this.$message.error('formKey is null')
        return
      }
      getAction(this.url.queryByIdUrl, {
        id: formKey,
      }).then((res) => {
        const data = res.result
        if (data != undefined && data != null) {
          // 设置获取的评估规则的数据
          this.jsonData = JSON.parse(data.formJson)
          // 获取评估规则的表单数据
          this.getRuleValues()
        }
      })
    },
    // 监听tab切换
    onTabChange(activeKey) {
      this.activeKey = activeKey
      if (activeKey === '3') {
        // 切换到评估规则tab时，更新评估字段选项
        this.$nextTick(() => {
          this.getEvaluationFields()
        })
      }
    },
    // 获取评估字段选项
    async getEvaluationFields() {
      try {
        // 获取当前表单设计的JSON
        const formDesignJson = this.$refs.KForm ? this.$refs.KForm.getValue() : {}

        const res = await httpAction('/evaluate/fieldInfo/getEvaluationFieldsByJson', {
          json: JSON.stringify(formDesignJson)
        }, 'post')

        if (res.success) {
          // 使用setOptions方法更新评估字段的选项
          if (this.$refs.generateStartForm) {
            // 如果没有评估字段，创建默认的评估字段
            let evaluationFields = res.result
            if (!evaluationFields || evaluationFields.length === 0) {
              evaluationFields = []
            }

            // 直接修改jsonData中的选项数据
            this.updateEvaluationFieldOptions(evaluationFields)
            this.$forceUpdate()
          }
        } else {
          // 即使接口失败，也要创建默认评估字段
          if (this.$refs.generateStartForm) {
            const defaultFields = []
            this.updateEvaluationFieldOptions(defaultFields)
            this.$forceUpdate()
          }
        }
      } catch (error) {
        console.error('获取评估字段失败:', error)
      }
    },

    // 更新评估字段选项的辅助方法
    updateEvaluationFieldOptions(options) {
      // 递归查找并更新field_key字段的选项
      const updateOptions = (list) => {
        list.forEach((item) => {
          if (item.model === 'field_key') {
            this.$set(item.options, 'options', options)
            this.$set(item.options, 'staticOptions', options)
            this.$set(item.options, 'dynamic', 'static')
          }

          // 处理嵌套结构
          if (item.list && item.list.length > 0) {
            updateOptions(item.list)
          }
          if (item.columns) {
            item.columns.forEach(col => {
              if (col.list) updateOptions(col.list)
            })
          }
        })
      }

      if (this.jsonData && this.jsonData.list) {
        updateOptions(this.jsonData.list)
      }

      // 为所有评估字段生成评估规则（只在需要时生成）
      if (options && options.length > 0) {
        // 检查是否需要生成或更新规则
        const needsRuleGeneration = this.shouldGenerateRules(options)

        if (needsRuleGeneration) {
          this.generateEvaluationRulesForAllFields(options)
        }
      } else {
        // 即使没有评估字段，也要确保有默认的评估规则
        if (!this.variables.evaluation_rules || this.variables.evaluation_rules.length === 0) {
          this.generateDefaultEvaluationRules()
        }
      }

      // 强制更新表单组件
      this.$nextTick(() => {
        if (this.$refs.generateStartForm) {
          this.$refs.generateStartForm.$forceUpdate()
        }
      })

      // 延迟再次强制更新，确保选项正确显示
      setTimeout(() => {
        if (this.$refs.generateStartForm) {
          this.$refs.generateStartForm.$forceUpdate()
        }
      }, 500)
    },
    getRuleValues() {
      getAction('/evaluate/fieldInfo/getRulesValue', {
        metricsId: this.model.id,
      }).then((res) => {
        this.variables = res.result ? res.result : {}

        // 保存已有的评估规则数据
        this.existingEvaluationRules = this.variables.evaluation_rules || []

        // 如果没有评估规则数据，生成默认数据
        if (!this.variables.evaluation_rules || this.variables.evaluation_rules.length === 0) {
          this.generateDefaultEvaluationRules()
        }
        this.generateStartFormVisible = true
        // 表单渲染完成后获取评估字段
        this.$nextTick(() => {
          this.getEvaluationFields()
        })
      }).catch((error) => {
        console.error('获取评估规则数据失败:', error)
        // 即使请求失败，也要生成默认数据
        this.variables = {}
        this.existingEvaluationRules = []
        this.generateDefaultEvaluationRules()
        this.generateStartFormVisible = true
        this.$nextTick(() => {
          this.getEvaluationFields()
        })
      })
    },

    // 生成默认的评估规则数据
    generateDefaultEvaluationRules() {
      // 如果已经有评估规则数据，不覆盖
      if (this.variables.evaluation_rules && this.variables.evaluation_rules.length > 0) {
        return
      }

      // 生成默认的A、B、C、D四个等级评估规则
      const defaultGradeRules = [
        {
          grade_level: 'A',
          comparison_operator: '',
          threshold_value: '',
          key: Date.now() + Math.random() * 1000 + 1
        },
        {
          grade_level: 'B',
          comparison_operator: '',
          threshold_value: '',
          key: Date.now() + Math.random() * 1000 + 2
        },
        {
          grade_level: 'C',
          comparison_operator: '',
          threshold_value: '',
          key: Date.now() + Math.random() * 1000 + 3
        },
        {
          grade_level: 'D',
          comparison_operator: '',
          threshold_value: '',
          key: Date.now() + Math.random() * 1000 + 4
        }
      ]

      // 先设置一个默认的评估规则，后续会根据评估字段更新
      this.variables.evaluation_rules = [
        {
          field_key: '', // 将在获取评估字段后填充
          rule_type: '',
          grade_rules: defaultGradeRules,
          rule_description: '',
          key: Date.now()
        }
      ]
    },

    // 判断是否需要生成评估规则
    shouldGenerateRules(evaluationFields) {
      // 如果没有现有规则，需要生成
      if (!this.variables.evaluation_rules || this.variables.evaluation_rules.length === 0) {
        return true
      }

      // 获取现有规则的字段列表
      const existingFieldKeys = this.variables.evaluation_rules.map(rule => rule.field_key).filter(Boolean)
      const evaluationFieldKeys = evaluationFields.map(field => field.value)

      // 检查是否有新的评估字段没有对应的规则
      const missingFields = evaluationFieldKeys.filter(fieldKey => !existingFieldKeys.includes(fieldKey))

      if (missingFields.length > 0) {
        return true
      }

      // 检查是否有无效的规则（对应的评估字段不存在）
      const invalidRules = existingFieldKeys.filter(fieldKey => !evaluationFieldKeys.includes(fieldKey))
      if (invalidRules.length > 0) {
        return true
      }
      return false
    },

    // 根据评估字段生成所有评估规则
    generateEvaluationRulesForAllFields(evaluationFields) {
      if (!evaluationFields || evaluationFields.length === 0) {
        return
      }

      // 确保 variables 对象存在
      if (!this.variables) {
        this.variables = {}
      }

      // 合并已有规则和新规则
      const mergedRules = this.mergeExistingAndNewRules(evaluationFields)

      // 设置生成的规则
      this.variables.evaluation_rules = mergedRules

      // 强制重新渲染表单组件
      this.formKey = Date.now()

      // 调用强制更新方法
      this.forceUpdateFormComponent()
    },

    // 合并已有的评估规则和新生成的规则
    mergeExistingAndNewRules(evaluationFields) {
      const mergedRules = []

      // 1. 首先添加所有已有的规则数据（完全保持原样，不做任何修改）
      if (this.existingEvaluationRules && this.existingEvaluationRules.length > 0) {
        this.existingEvaluationRules.forEach(existingRule => {
          // 验证已有规则是否对应有效的评估字段
          const correspondingField = evaluationFields.find(field => field.value === existingRule.field_key)

          if (correspondingField) {
            // 完全保留原有数据，不做任何修改
            mergedRules.push({ ...existingRule })
          }
          // 如果是无效字段，忽略该规则（不添加到结果中）
        })
      }

      // 2. 找出还没有规则的评估字段
      const existingFieldKeys = mergedRules.map(rule => rule.field_key)
      const missingFields = evaluationFields.filter(field =>
        !existingFieldKeys.includes(field.value)
      )

      // 3. 仅为缺失的字段新增规则行
      missingFields.forEach((field, index) => {
        const newRule = {
          field_key: field.value,
          rule_type: '',
          is_important: false,
          grade_rules: [
            {
              grade_level: 'A',
              comparison_operator: '',
              threshold_value: '',
              key: Date.now() + Math.random() * 1000 + 1
            },
            {
              grade_level: 'B',
              comparison_operator: '',
              threshold_value: '',
              key: Date.now() + Math.random() * 1000 + 2
            },
            {
              grade_level: 'C',
              comparison_operator: '',
              threshold_value: '',
              key: Date.now() + Math.random() * 1000 + 3
            },
            {
              grade_level: 'D',
              comparison_operator: '',
              threshold_value: '',
              key: Date.now() + Math.random() * 1000 + 4
            }
          ],
          rule_description: '',
          key: Date.now() + (mergedRules.length + index) * 1000
        }

        mergedRules.push(newRule)
      })
      return mergedRules
    },

    // 强制更新表单组件
    forceUpdateFormComponent() {
      this.$nextTick(() => {
        this.$forceUpdate()

        if (this.$refs.generateStartForm) {
          // 尝试重新设置表单数据
          if (this.$refs.generateStartForm.setData) {
            this.$refs.generateStartForm.setData(this.variables)
          }

          this.$refs.generateStartForm.$forceUpdate()
        }
      })

      // 延迟再次强制更新，确保数据正确显示
      setTimeout(() => {
        // 再次更新 formKey 强制重新渲染
        this.formKey = Date.now() + 1000

        this.$nextTick(() => {
          if (this.$refs.generateStartForm) {
            // 再次尝试设置数据
            if (this.$refs.generateStartForm.setData) {
              this.$refs.generateStartForm.setData(this.variables)
            }

            this.$refs.generateStartForm.$forceUpdate()
          }
        })
      }, 1000)
    },
  },
}
</script>
<style lang="less" scoped>
::v-deep .ant-tabs-bar {
  margin: 0 !important;
}
::v-deep .ant-tabs-nav {
  margin-left: 24px;
}
.box-wrapper {
  padding: 24px;
}
</style>
