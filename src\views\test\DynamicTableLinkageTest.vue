<template>
  <div class="dynamic-table-linkage-test">
    <a-card title="动态表格嵌套联动测试">
      <div class="test-controls" style="margin-bottom: 20px;">
        <a-space>
          <a-button type="primary" @click="loadFormConfig">加载表单配置</a-button>
          <a-button @click="getFormData">获取表单数据</a-button>
          <a-button @click="resetForm">重置表单</a-button>
          <a-button @click="showFormJson">查看表单JSON</a-button>
        </a-space>
      </div>
      
      <!-- 表单渲染区域 -->
      <div v-if="formConfig && Object.keys(formConfig).length > 0">
        <k-form-build
          ref="formBuild"
          :value="formConfig"
          :defaultValue="defaultFormData"
          @change="handleFormChange"
        />
      </div>
      
      <!-- 数据展示区域 -->
      <a-card title="表单数据" style="margin-top: 20px;" v-if="showData">
        <pre>{{ JSON.stringify(formData, null, 2) }}</pre>
      </a-card>
      
      <!-- JSON配置展示 -->
      <a-modal
        title="表单JSON配置"
        :visible="jsonModalVisible"
        :width="800"
        @ok="jsonModalVisible = false"
        @cancel="jsonModalVisible = false"
      >
        <pre style="max-height: 500px; overflow-y: auto;">{{ JSON.stringify(formConfig, null, 2) }}</pre>
      </a-modal>
    </a-card>
  </div>
</template>

<script>
import { getAction } from '@/api/manage'

export default {
  name: 'DynamicTableLinkageTest',
  data() {
    return {
      formConfig: {},
      defaultFormData: {},
      formData: {},
      showData: false,
      jsonModalVisible: false,
      // 预定义的表单配置
      predefinedConfig: {
        "list": [
          {
            "type": "batch",
            "label": "外层动态表格",
            "list": [
              {
                "type": "select",
                "label": "外层下拉框",
                "icon": "icon-xiala",
                "options": {
                  "width": "100%",
                  "tableWidth": "150px",
                  "multiple": false,
                  "disabled": false,
                  "clearable": false,
                  "hidden": false,
                  "placeholder": "请选择外层选项",
                  "linkageData": "[]",
                  "changeFunc": `function(value, key, vm, http) {
                    console.log('外层下拉框值变化:', value, key);
                    
                    // 定义内层下拉框的数据映射
                    const innerDataMap = {
                      'category1': [
                        { value: 'item1_1', label: '类别1-项目1' },
                        { value: 'item1_2', label: '类别1-项目2' },
                        { value: 'item1_3', label: '类别1-项目3' }
                      ],
                      'category2': [
                        { value: 'item2_1', label: '类别2-项目1' },
                        { value: 'item2_2', label: '类别2-项目2' },
                        { value: 'item2_3', label: '类别2-项目3' }
                      ],
                      'category3': [
                        { value: 'item3_1', label: '类别3-项目1' },
                        { value: 'item3_2', label: '类别3-项目2' }
                      ]
                    };
                    
                    // 更新内层下拉框的选项数据
                    if (value && innerDataMap[value]) {
                      try {
                        // 递归遍历表单配置，找到内层下拉框并更新其选项
                        const traverseAndUpdate = (list, targetValue) => {
                          list.forEach(item => {
                            if (item.type === 'batch' && item.model === 'batch_1754985503227') {
                              // 找到内层动态表格
                              item.list.forEach(innerItem => {
                                if (innerItem.type === 'select' && innerItem.model === 'select_1754985505711') {
                                  // 更新内层下拉框的选项
                                  innerItem.options.options = [...innerDataMap[targetValue]];
                                  innerItem.options.staticOptions = [...innerDataMap[targetValue]];
                                  console.log('内层下拉框数据已更新为:', innerDataMap[targetValue]);
                                }
                              });
                            } else if (item.list && Array.isArray(item.list)) {
                              traverseAndUpdate(item.list, targetValue);
                            }
                          });
                        };
                        
                        traverseAndUpdate(vm.value.list, value);
                        
                        // 强制更新视图
                        vm.$nextTick(() => {
                          vm.$forceUpdate();
                        });
                        
                      } catch (error) {
                        console.error('更新内层下拉框数据时出错:', error);
                      }
                    } else if (!value) {
                      // 如果外层下拉框清空，重置内层下拉框
                      try {
                        const resetInnerSelect = (list) => {
                          list.forEach(item => {
                            if (item.type === 'batch' && item.model === 'batch_1754985503227') {
                              item.list.forEach(innerItem => {
                                if (innerItem.type === 'select' && innerItem.model === 'select_1754985505711') {
                                  innerItem.options.options = [{ value: '', label: '请先选择外层选项' }];
                                  innerItem.options.staticOptions = [{ value: '', label: '请先选择外层选项' }];
                                }
                              });
                            } else if (item.list && Array.isArray(item.list)) {
                              resetInnerSelect(item.list);
                            }
                          });
                        };
                        
                        resetInnerSelect(vm.value.list);
                        vm.$forceUpdate();
                        
                      } catch (error) {
                        console.error('重置内层下拉框时出错:', error);
                      }
                    }
                  }`,
                  "dynamicKey": "",
                  "dynamic": "static",
                  "options": [
                    { "value": "category1", "label": "类别1" },
                    { "value": "category2", "label": "类别2" },
                    { "value": "category3", "label": "类别3" }
                  ],
                  "staticOptions": [
                    { "value": "category1", "label": "类别1" },
                    { "value": "category2", "label": "类别2" },
                    { "value": "category3", "label": "类别3" }
                  ],
                  "showSearch": false,
                  "isEvaluationField": false,
                  "showLabel": true
                },
                "model": "select_1754985501160",
                "key": "select_1754985501160",
                "help": "",
                "rules": [{ "required": false, "message": "必填项" }]
              },
              {
                "type": "batch",
                "label": "内层动态表格",
                "icon": "icon-biaoge",
                "list": [
                  {
                    "type": "select",
                    "label": "内层下拉框",
                    "icon": "icon-xiala",
                    "options": {
                      "width": "100%",
                      "tableWidth": "150px",
                      "multiple": false,
                      "disabled": false,
                      "clearable": false,
                      "hidden": false,
                      "placeholder": "请先选择外层选项",
                      "linkageData": "[]",
                      "changeFunc": "function(value, key, vm, http) { console.log('内层下拉框值变化:', value, key); }",
                      "dynamicKey": "",
                      "dynamic": "static",
                      "options": [{ "value": "", "label": "请先选择外层选项" }],
                      "staticOptions": [{ "value": "", "label": "请先选择外层选项" }],
                      "showSearch": false,
                      "isEvaluationField": false,
                      "showLabel": true
                    },
                    "model": "select_1754985505711",
                    "key": "select_1754985505711",
                    "help": "",
                    "rules": [{ "required": false, "message": "必填项" }]
                  }
                ],
                "options": {
                  "scrollY": 0,
                  "disabled": false,
                  "hidden": false,
                  "showLabel": true,
                  "hideSequence": false,
                  "width": "100%",
                  "rowKey": "id",
                  "hideAddBtn": false,
                  "hideOprCol": false,
                  "linkageData": "[]",
                  "changeFunc": "function(value, key, vm, http) { console.log('内层动态表格数据变化:', value, key); }"
                },
                "model": "batch_1754985503227",
                "key": "batch_1754985503227",
                "help": ""
              }
            ],
            "options": {
              "scrollY": 0,
              "disabled": false,
              "hidden": false,
              "showLabel": true,
              "hideSequence": false,
              "width": "100%",
              "rowKey": "id",
              "hideAddBtn": false,
              "hideOprCol": false,
              "linkageData": "[]",
              "changeFunc": "function(value, key, vm, http) { console.log('外层动态表格数据变化:', value, key); }"
            },
            "model": "batch_1754985498661",
            "key": "batch_1754985498661",
            "help": ""
          }
        ],
        "config": {
          "layout": "horizontal",
          "labelCol": { "xs": 4, "sm": 4, "md": 4, "lg": 4, "xl": 4, "xxl": 4 },
          "labelWidth": 100,
          "labelLayout": "flex",
          "wrapperCol": { "xs": 18, "sm": 18, "md": 18, "lg": 18, "xl": 18, "xxl": 18 },
          "hideRequiredMark": false,
          "customStyle": ""
        }
      }
    }
  },
  
  mounted() {
    this.loadFormConfig()
  },
  
  methods: {
    // 加载表单配置
    async loadFormConfig() {
      try {
        // 从JSON文件加载配置
        const response = await fetch('/evaluation-rules-with-data.json')
        if (response.ok) {
          this.formConfig = await response.json()
        } else {
          // 如果文件不存在，使用预定义配置
          this.formConfig = this.predefinedConfig
        }
        
        console.log('表单配置已加载:', this.formConfig)
        this.$message.success('表单配置加载成功')
      } catch (error) {
        console.error('加载表单配置失败:', error)
        // 使用预定义配置作为备选
        this.formConfig = this.predefinedConfig
        this.$message.warning('使用默认配置')
      }
    },
    
    // 获取表单数据
    async getFormData() {
      try {
        if (this.$refs.formBuild) {
          const data = await this.$refs.formBuild.getData()
          this.formData = data
          this.showData = true
          console.log('表单数据:', data)
          this.$message.success('获取表单数据成功')
        }
      } catch (error) {
        console.error('获取表单数据失败:', error)
        this.$message.error('获取表单数据失败')
      }
    },
    
    // 重置表单
    resetForm() {
      if (this.$refs.formBuild) {
        this.$refs.formBuild.reset()
        this.formData = {}
        this.showData = false
        this.$message.success('表单已重置')
      }
    },
    
    // 显示表单JSON
    showFormJson() {
      this.jsonModalVisible = true
    },
    
    // 表单变化处理
    handleFormChange(value, key) {
      console.log('表单字段变化:', key, value)
    }
  }
}
</script>

<style scoped>
.dynamic-table-linkage-test {
  padding: 20px;
}

.test-controls {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 16px;
}

pre {
  background-color: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.4;
}
</style>
