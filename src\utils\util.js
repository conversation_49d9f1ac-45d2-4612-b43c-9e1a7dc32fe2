import * as api from '@/api/api'
import { isURL } from '@/utils/validate'
import onlineCommons from '@jeecg/antd-online-mini'
import Vue from 'vue'
import { ACCESS_TOKEN } from '@/store/mutation-types'
import { getAction } from '@api/manage'
import store from '@/store'
export let statsMenus = [];
/*
* 获取统计中心的静态页面菜单
* */
export async function getStatsMenus() {
  if(statsMenus.length > 0) return
  await getAction(location.origin+"/statsCenter/mock/statsCenterMenus.json").then((res) => {
    statsMenus = res
  })
  // const headers = {'X-Access-Token': Vue.ls.get(ACCESS_TOKEN)}
  // await fetch(location.origin+"/statsCenter/mock/statsCenterMenus.json", {headers}).then((res) => {
  //   return res.json()
  // }).then(result=>{
  //   statsMenus = result
  //   // console.log("静态页面菜单 === ", statsMenus)
  // })
}


export function timeFix() {
  const time = new Date()
  const hour = time.getHours()
  return hour < 9 ? '早上好' : (hour <= 11 ? '上午好' : (hour <= 13 ? '中午好' : (hour < 20 ? '下午好' : '晚上好')))
}

export function welcome() {
  const arr = ['休息一会儿吧', '准备吃什么呢?', '要不要打一把 DOTA', '我猜你可能累了']
  let index = Math.floor((Math.random() * arr.length))
  return arr[index]
}

/**
 * 触发 window.resize
 */
export function triggerWindowResizeEvent() {
  let event = document.createEvent('HTMLEvents')
  event.initEvent('resize', true, true)
  event.eventType = 'message'
  window.dispatchEvent(event)
}

/**
 *手机号、座机号数据有效性校验
 * @param rule
 * @param value
 * @param callback
 * @returns {*}
 */
export function checkPhone(rule, value, callback) {
  let regPone = null
  // let mobile = /^((13|14|15|17|18)[0-9]{1}\d{8})$/ // 最新16手机正则
  //let mobile = /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/
  let mobile = /^((1)[1-9]{1}\d{9})$/ //手机
  let tel = /^((0\d{2,3}-\d{7,8})|(1[3584]\d{9}))$/ // 座机
  if (value !== null && value.charAt(0) === '0') { // charAt查找第一个字符方法，用来判断输入的是座机还是手机号
    regPone = tel
  } else if (value !== null && value.charAt(0) !== '0') {
    regPone = mobile
  }
  if (regPone === null) {
    return callback(
      new Error('请输入联系方式!')
    )
  } else if (!regPone.test(value)) {
    return callback(
      new Error("请输入正确的手机或座机号码!")
    )
  } else {
    callback()
  }
}

/**
 * 过滤对象中为空的属性
 * @param obj
 * @returns {*}
 */
export function filterObj(obj) {
  if (!(typeof obj == 'object')) {
    return;
  }

  for (let key in obj) {
    if (obj.hasOwnProperty(key)
      && (obj[key] == null || obj[key] == undefined || obj[key] === '')) {
      delete obj[key];
    }
  }
  return obj;
}

/**
 * 时间格式化
 * @param value
 * @param fmt
 * @returns {*}
 */
export function formatDate(value, fmt) {
  let regPos = /^\d+(\.\d+)?$/;
  if (regPos.test(value)) {
    //如果是数字
    let getDate = new Date(value);
    let o = {
      'M+': getDate.getMonth() + 1,
      'd+': getDate.getDate(),
      'h+': getDate.getHours(),
      'm+': getDate.getMinutes(),
      's+': getDate.getSeconds(),
      'q+': Math.floor((getDate.getMonth() + 3) / 3),
      'S': getDate.getMilliseconds()
    };
    if (/(y+)/.test(fmt)) {
      fmt = fmt.replace(RegExp.$1, (getDate.getFullYear() + '').substr(4 - RegExp.$1.length))
    }
    for (let k in o) {
      if (new RegExp('(' + k + ')').test(fmt)) {
        fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? (o[k]) : (('00' + o[k]).substr(('' + o[k]).length)))
      }
    }
    return fmt;
  } else {
    //TODO
    value = value.trim();
    return value.substr(0, fmt.length);
  }
}

export function generateBigscreenRouter(data) {
  // console.log("路由的数据 === ",data)
  // window.config.DataCenterType 数据中心布局类型 0-正常 1-内蒙定制
  let title = '数据中心'
  let normalCom = resolve => require(['@/components/layouts/BigScreenData'], resolve)
  if (window.config.DataCenterType === 1) {
    title = "统计中心"
    normalCom = resolve => require(["@/components/layouts/StatsCenterLayout"], resolve)
    if(window.config.useStaticData === 1){
      data = statsMenus;
      // console.log("统计中心data === ", data,statsMenus)
    }
  }
  if(getQueryStringRegExp("hideMenu") === "1"){
    normalCom = resolve => require(['@/components/layouts/ComponentLayout.vue'], resolve)
  }
  let indexRouter = [{
    path: '/',
    component: normalCom,
    meta: { title: title },
    redirect: (data[0].children && data[0].children.length > 0) ? data[0].children[0].path : data[0].path,
    children: [
      ...generateChildRouters(data)
    ]
  }, {
    "path": "*", "redirect": "/404", "hidden": true
  }]
  return indexRouter;
}


// 生成首页路由
export function generateIndexRouter(data) {
  let ochDatas = data.filter(el => el.path.startsWith("/oneClickHelp"))
  let normalData = data.filter(el => !el.path.startsWith("/oneClickHelp"))
  console.log("normalData === ",normalData)
  let layoutCom = resolve => require(['@/components/layouts/TabLayout'], resolve)
  if(getQueryStringRegExp("hideMenu") === "1"){
    layoutCom = resolve => require(['@/components/layouts/ComponentLayout.vue'], resolve)
  }else if(normalData.length === 1 && normalData[0].path === "/zrljView"){
    layoutCom = resolve => require(['@/components/layouts/BlankLayout.vue'], resolve)
  }
  let routes = {
    path: '/',
    name: 'Layout',
    //component: () => import('@/components/layouts/BasicLayout'),
    // component: resolve => require(['@/components/layouts/TabLayout'], resolve),
    component: layoutCom,
    meta: { title: '门户网站' },
    redirect: '/gateway/gateway',
    children: [
      ...generateChildRouters(normalData)
    ]
  }
  // if(data.length > 0 && data[0].path.startsWith("/oneClickHelp")){

  // }
  let ochRoutes = {
    path: '/',
    name: 'ochLayout',
    component: resolve => require(['@/views/oneClickHelp/layouts/Layout'], resolve),
    meta: { title: '运维助手' },
    redirect: '/oneClickHelp/index',
    children: [
      ...generateChildRouters(ochDatas)
    ]
  }
  let indexRouter = [routes, ochRoutes, {
    "path": "*", "redirect": "/404", "hidden": true
  }]
  return indexRouter;
}

// 生成嵌套路由（子路由）

function generateChildRouters(data) {
  const routers = [];
  for (let item of data) {
    let component = "";
    if (item.component.indexOf("layouts") >= 0) {
      component = "components/" + item.component;
    } else if (item.path === "/bpmn") {
      component = "components/" + item.component;
    } else if (item.path === "/task") {
      component = "components/" + item.component;
    }
    else {
      component = "views/" + item.component;
    }

    // eslint-disable-next-line
    let URL = (item.meta.url || '').replace(/{{([^}}]+)?}}/g, (s1, s2) => eval(s2)) // URL支持{{ window.xxx }}占位符变量
    if (isURL(URL)) {
      item.meta.url = URL;
    }

    let componentPath
    if (item.component == "modules/online/cgform/OnlCgformHeadList") {
      componentPath = onlineCommons.OnlCgformHeadList
    } else if (item.component == "modules/online/cgform/OnlCgformCopyList") {
      componentPath = onlineCommons.OnlCgformCopyList
    } else if (item.component == "modules/online/cgform/auto/OnlCgformAutoList") {
      componentPath = onlineCommons.OnlCgformAutoList
    } else if (item.component == "modules/online/cgform/auto/OnlCgformTreeList") {
      componentPath = onlineCommons.OnlCgformTreeList
    } else if (item.component == "modules/online/cgform/auto/erp/OnlCgformErpList") {
      componentPath = onlineCommons.OnlCgformErpList
    } else if (item.component == "modules/online/cgform/auto/tab/OnlCgformTabList") {
      componentPath = onlineCommons.OnlCgformTabList
    } else if (item.component == "modules/online/cgform/auto/innerTable/OnlCgformInnerTableList") {
      componentPath = onlineCommons.OnlCgformInnerTableList
    } else if (item.component == "modules/online/cgreport/OnlCgreportHeadList") {
      componentPath = onlineCommons.OnlCgreportHeadList
    } else if (item.component == "modules/online/cgreport/auto/OnlCgreportAutoList") {
      componentPath = onlineCommons.OnlCgreportAutoList
    } else {
      //componentPath = resolve => require(['@/' + component + '.vue'], resolve) //一次性加载
      componentPath = () => import('@/' + component + '.vue')  //按需加载
    }
    //根据路由配置获取配置参数和路径
    let pathObj = parseUrlParams(item.path, 3)
    let menu = {
      path:pathObj.pathname,
      name: item.name,
      redirect: item.redirect,
      component: componentPath,
      //component: resolve => require(['@/' + component+'.vue'], resolve),
      hidden: item.hidden,
      meta: {
        title: item.meta.title,
        icon: item.meta.icon,
        url: item.meta.url,
        permissionList: item.meta.permissionList,
        keepAlive: item.meta.keepAlive,
        /*update_begin author:wuxianquan date:20190908 for:赋值 */
        internalOrExternal: item.meta.internalOrExternal,
        /*update_end author:wuxianquan date:20190908 for:赋值 */
        componentName: item.meta.componentName,
        // 路由解析后的参数
        query: pathObj.params,
        originPath: item.path,
      }
    }
    if (item.alwaysShow) {
      menu.alwaysShow = true;
      menu.redirect = menu.path;
    }
    if (item.children && item.children.length > 0) {
      menu.children = [...generateChildRouters(item.children)];
    }
    //--update-begin----author:scott---date:20190320------for:根据后台菜单配置，判断是否路由菜单字段，动态选择是否生成路由（为了支持参数URL菜单）------
    //判断是否生成路由
    if (item.route && item.route === '0') {
    } else {
      routers.push(menu);
    }
    //--update-end----author:scott---date:20190320------for:根据后台菜单配置，判断是否路由菜单字段，动态选择是否生成路由（为了支持参数URL菜单）------
  }
  return routers
}
/**
 * 解析 URL 中的参数
 * @param {string} url 要解析的 URL
 * @returns {Object} 包含参数键值对的对象
 * type 1 返回参数键值对 2 返回路径段 3 两个都返回
 */
export function parseUrlParams(url, type) {
  let params = {};
  let pathname = "";
  try {
    const urlObj = new URL(url, window.location.origin);
    const searchParams = urlObj.searchParams;
    pathname = urlObj.pathname;
    for (const [key, value] of searchParams.entries()) {
      params[key] = value;
    }
  } catch (e) {
    params = {};
    pathname = url;
  }
  if (type === 1) {
    return params;
  } else if (type === 2) {
    return pathname;
  } else if (type === 3) {
    return { params, pathname };
  }
}

/**
 * 深度克隆对象、数组
 * @param obj 被克隆的对象
 * @return 克隆后的对象
 */
export function cloneObject(obj) {
  return JSON.parse(JSON.stringify(obj))
}

/*
*json字符串转对象
*/
export function tryToJSON(val) {
  try {
    return JSON.parse(val)
  } catch (error) {
    return val
  }
}
/**
 * 随机生成数字
 *
 * 示例：生成长度为 12 的随机数：randomNumber(12)
 * 示例：生成 3~23 之间的随机数：randomNumber(3, 23)
 *
 * @param1 最小值 | 长度
 * @param2 最大值
 * @return int 生成后的数字
 */
export function randomNumber() {
  // 生成 最小值 到 最大值 区间的随机数
  const random = (min, max) => {
    return Math.floor(Math.random() * (max - min + 1) + min)
  }
  if (arguments.length === 1) {
    let [length] = arguments
    // 生成指定长度的随机数字，首位一定不是 0
    let nums = [...Array(length).keys()].map((i) => (i > 0 ? random(0, 9) : random(1, 9)))
    return parseInt(nums.join(''))
  } else if (arguments.length >= 2) {
    let [min, max] = arguments
    return random(min, max)
  } else {
    return Number.NaN
  }
}

/**
 * 随机生成字符串
 * @param length 字符串的长度
 * @param chats 可选字符串区间（只会生成传入的字符串中的字符）
 * @return string 生成的字符串
 */
export function randomString(length, chats) {
  if (!length) length = 1
  if (!chats) chats = '0123456789qwertyuioplkjhgfdsazxcvbnm'
  let str = ''
  for (let i = 0; i < length; i++) {
    let num = randomNumber(0, chats.length - 1)
    str += chats[num]
  }
  return str
}

/**
 * 随机生成uuid
 * @return string 生成的uuid
 */
export function randomUUID() {
  let chats = '0123456789abcdef'
  return randomString(32, chats)
}

/**
 * 下划线转驼峰
 * @param string
 * @returns {*}
 */
export function underLine2CamelCase(string) {
  return string.replace(/_([a-z])/g, function (all, letter) {
    return letter.toUpperCase();
  });
}

/**
 * 判断是否显示办理按钮
 * @param bpmStatus
 * @returns {*}
 */
export function showDealBtn(bpmStatus) {
  if (bpmStatus != "1" && bpmStatus != "3" && bpmStatus != "4") {
    return true;
  }
  return false;
}

/**
 * 增强CSS，可以在页面上输出全局css
 * @param css 要增强的css
 * @param id style标签的id，可以用来清除旧样式
 */
export function cssExpand(css, id) {
  let style = document.createElement('style')
  style.type = "text/css"
  style.innerHTML = `@charset "UTF-8"; ${css}`
  // 清除旧样式
  if (id) {
    let $style = document.getElementById(id)
    if ($style != null) $style.outerHTML = ''
    style.id = id
  }
  // 应用新样式
  document.head.appendChild(style)
}


/** 用于js增强事件，运行JS代码，可以传参 */
// options 所需参数：
//    参数名         类型            说明
//    vm             VueComponent    vue实例
//    event          Object          event对象
//    jsCode         String          待执行的js代码
//    errorMessage   String          执行出错后的提示（控制台）
export function jsExpand(options = {}) {

  // 绑定到window上的keyName
  let windowKeyName = 'J_CLICK_EVENT_OPTIONS'
  if (typeof window[windowKeyName] != 'object') {
    window[windowKeyName] = {}
  }

  // 随机生成JS增强的执行id，防止冲突
  let id = randomString(16, 'qwertyuioplkjhgfdsazxcvbnm'.toUpperCase())
  // 封装按钮点击事件
  let code = `
    (function (o_${id}) {
      try {
        (function (globalEvent, vm) {
          ${options.jsCode}
        })(o_${id}.event, o_${id}.vm)
      } catch (e) {
        o_${id}.error(e)
      }
      o_${id}.done()
    })(window['${windowKeyName}']['EVENT_${id}'])
  `
  // 创建script标签
  const script = document.createElement('script')
  // 将需要传递的参数挂载到window对象上
  window[windowKeyName]['EVENT_' + id] = {
    vm: options.vm,
    event: options.event,
    // 当执行完成时，无论如何都会调用的回调事件
    done() {
      // 执行完后删除新增的 script 标签不会撤销执行结果（已产生的结果不会被撤销）
      script.outerHTML = ''
      delete window[windowKeyName]['EVENT_' + id]
    },
    // 当js运行出错的时候调用的事件
    error(e) {
      console.group(`${options.errorMessage || '用户自定义JS增强代码运行出错'}（${new Date()}）`)
      console.error(e)
      console.groupEnd()
    }
  }
  // 将事件挂载到document中
  script.innerHTML = code
  document.body.appendChild(script)
}


/**
 * 重复值验证工具方法
 *
 * 使用示例：
 * { validator: (rule, value, callback) => validateDuplicateValue('sys_fill_rule', 'rule_code', value, this.model.id, callback) }
 *
 * @param tableName 被验证的表名
 * @param fieldName 被验证的字段名
 * @param fieldVal 被验证的值
 * @param dataId 数据ID，可空
 * @param callback
 */
export function validateDuplicateValue(tableName, fieldName, fieldVal, dataId, callback) {
  if (fieldVal) {
    let params = { tableName, fieldName, fieldVal, dataId }
    api.duplicateCheck(params).then(res => {
      res['success'] ? callback() : callback(res['message'])
    }).catch(err => {
      callback(err.message || err)
    })
  } else {
    callback()
  }
}

/**
 * 根据编码校验规则code，校验传入的值是否合法
 *
 * 使用示例：
 * { validator: (rule, value, callback) => validateCheckRule('common', value, callback) }
 *
 * @param ruleCode 编码校验规则 code
 * @param value 被验证的值
 * @param callback
 */
export function validateCheckRule(ruleCode, value, callback) {
  if (ruleCode && value) {
    value = encodeURIComponent(value)
    api.checkRuleByCode({ ruleCode, value }).then(res => {
      res['success'] ? callback() : callback(res['message'])
    }).catch(err => {
      callback(err.message || err)
    })
  } else {
    callback()
  }
}

/**
 * 如果值不存在就 push 进数组，反之不处理
 * @param array 要操作的数据
 * @param value 要添加的值
 * @param key 可空，如果比较的是对象，可能存在地址不一样但值实际上是一样的情况，可以传此字段判断对象中唯一的字段，例如 id。不传则直接比较实际值
 * @returns {boolean} 成功 push 返回 true，不处理返回 false
 */
export function pushIfNotExist(array, value, key) {
  for (let item of array) {
    if (key && (item[key] === value[key])) {
      return false
    } else if (item === value) {
      return false
    }
  }
  array.push(value)
  return true
}

/**
 * 可用于判断是否成功
 * @type {symbol}
 */
export const succeedSymbol = Symbol()
/**
 * 可用于判断是否失败
 * @type {symbol}
 */
export const failedSymbol = Symbol()

/**
 * 使 promise 无论如何都会 resolve，除非传入的参数不是一个Promise对象或返回Promise对象的方法
 * 一般用在 Promise.all 中
 *
 * @param promise 可传Promise对象或返回Promise对象的方法
 * @returns {Promise<any>}
 */
export function alwaysResolve(promise) {
  return new Promise((resolve, reject) => {
    let p = promise
    if (typeof promise === 'function') {
      p = promise()
    }
    if (p instanceof Promise) {
      p.then(data => {
        resolve({ type: succeedSymbol, data })
      }).catch(error => {
        resolve({ type: failedSymbol, error })
      })
    } else {
      reject('alwaysResolve: 传入的参数不是一个Promise对象或返回Promise对象的方法')
    }
  })
}

/**
 * 简单实现防抖方法
 *
 * 防抖(debounce)函数在第一次触发给定的函数时，不立即执行函数，而是给出一个期限值(delay)，比如100ms。
 * 如果100ms内再次执行函数，就重新开始计时，直到计时结束后再真正执行函数。
 * 这样做的好处是如果短时间内大量触发同一事件，只会执行一次函数。
 *
 * @param fn 要防抖的函数
 * @param delay 防抖的毫秒数
 * @returns {Function}
 */
export function simpleDebounce(fn, delay = 100) {
  let timer = null
  return function () {
    let args = arguments
    if (timer) {
      clearTimeout(timer)
    }
    timer = setTimeout(() => {
      fn.apply(this, args)
    }, delay)
  }
}
/**
 * 改造后的简单实现防抖方法
 *
 * 防抖(debounce)函数在第一次触发给定的函数时，不立即执行函数，而是给出一个期限值(delay)，比如100ms。
 * 如果100ms内再次执行函数，就重新开始计时，直到计时结束后再真正执行函数。
 * 这样做的好处是如果短时间内大量触发同一事件，只会执行一次函数。
 * 添加了取消防抖
 * @param fn 要防抖的函数
 * @param delay 防抖的毫秒数
 * @returns {Function}
 */
export function addCancelDebounce(fn, delay = 100) {
  let timer = null;

  function debounced() {
    const args = arguments;
    if (timer) {
      clearTimeout(timer);
    }
    timer = setTimeout(() => {
      fn.apply(this, args);
    }, delay);
  }

  // 添加取消方法
  debounced.cancel = function() {
    if (timer) {
      clearTimeout(timer);
      timer = null;
    }
  };

  return debounced;
}
/**
 * 不用正则的方式替换所有值
 * @param text 被替换的字符串
 * @param checker  替换前的内容
 * @param replacer 替换后的内容
 * @returns {String} 替换后的字符串
 */
export function replaceAll(text, checker, replacer) {
  let lastText = text
  text = text.replace(checker, replacer)
  if (lastText !== text) {
    return replaceAll(text, checker, replacer)
  }
  return text
}

/**
 * 获取事件冒泡路径，兼容 IE11，Edge，Chrome，Firefox，Safari
 * 目前使用的地方：JEditableTable Span模式
 */
export function getEventPath(event) {
  let target = event.target
  let path = (event.composedPath && event.composedPath()) || event.path

  if (path != null) {
    return (path.indexOf(window) < 0) ? path.concat(window) : path
  }

  if (target === window) {
    return [window]
  }

  let getParents = (node, memo) => {
    memo = memo || []
    const parentNode = node.parentNode

    if (!parentNode) {
      return memo
    } else {
      return getParents(parentNode, memo.concat(parentNode))
    }
  }
  return [target].concat(getParents(target), window)
}

/**
 * 根据组件名获取父级
 * @param vm
 * @param name
 * @returns {Vue | null|null|Vue}
 */
export function getVmParentByName(vm, name) {
  let parent = vm.$parent
  if (parent && parent.$options) {
    if (parent.$options.name === name) {
      return parent
    } else {
      let res = getVmParentByName(parent, name)
      if (res) {
        return res
      }
    }
  }
  return null
}

/**
 * 使一个值永远不会为（null | undefined）
 *
 * @param value 要处理的值
 * @param def 默认值，如果value为（null | undefined）则返回的默认值，可不传，默认为''
 */
export function neverNull(value, def) {
  return value == null ? (neverNull(def, '')) : value
}

export function createPreImg() {
  let el = document.getElementById("preImg");
  if (el) return;
  let img = document.createElement('div');
  img.id = "preImg";
  img.style = "position: absolute; top: -9999px;backgroud:#000;\
              width:180px;line-height:36px ;text-align:center;\
              background:#fff;border:1px solid #000;"
  document.body.append(img)
}


export const uuidX6 = function () {
  var res = '';
  var template = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx';
  for (var i = 0, len = template.length; i < len; i += 1) {
    var s = template[i];
    var r = (Math.random() * 16) | 0;
    var v = s === 'x' ? r : s === 'y' ? (r & 0x3) | 0x8 : s;
    res += v.toString(16);
  }
  return res;
}

export const getMultipleURL = function (type) {
  let urlstr = window.config.multipleURL[type];
  if (!urlstr.startsWith("http")) {
    urlstr = document.location.origin + urlstr;
  }
  return urlstr
}

/**
 * 当前日期增加n年
 *
 * @param dateText
 * @param num
 * @returns {*}
 */
export function addYear(dateText, num) {

  var d1 = new Date(dateText);
  var d2 = new Date(d1);
  d2.setFullYear(d2.getFullYear() + num);
  d2.setDate(d2.getDate() - 1);
  //获取原日
  var day = doHandleMonth(d2.getDate());
  //获取原月份
  var month = doHandleMonth(d2.getMonth() + 1);
  var year = d2.getFullYear();
  return year + "-" + month + "-" + day
}

/**
 * 当前日期增加n月
 * @param dateStr 当前日期
 * @param num 月
 * @param type 0:2022-05-30   1：2022-05-30 11:11:11
 * @returns {string}
 */
export function commonAddMouth(dateStr, num, type) {
  var monthnum = 0;
  if (typeof (num) == "string") {
    monthnum = parseInt(num);
  } else {
    monthnum = num;
  }
  var date = new Date(dateStr);
  //获取原日
  var day = date.getDate();
  //获取原月份
  var month = date.getMonth();
  //设置增加月份
  date.setMonth(date.getMonth() + (monthnum * 1), 1);
  //获取增加的后的月份
  var Jmonth = date.getMonth() + 1;
  //获取增加的后的年份
  var Jyear = date.getFullYear();
  if (Jmonth == 4 || Jmonth == 6 || Jmonth == 9 || Jmonth == 11) {
    //小月
    if (day > 30) {
      day = 30;
    }
  } else if (Jmonth == 2) {
    //2月判断是否闰年
    if (((Jyear % 4) == 0) && ((Jyear % 100) != 0) || ((Jyear % 400) == 0)) {
      if (day > 29) {
        day = 29;
      } else {
        day = 28;
      }
    }
    if (day > 28) {
      day = 28
    }

  } else {
    //大月
    if (day > 31) {
      day = 31;
    }
  }
  var tHours = date.getHours();
  var tMinutes = date.getMinutes();
  var tSeconds = date.getSeconds();
  Jmonth = doHandleMonth(Jmonth);
  day = doHandleMonth(day);
  if (type == "0") {
    return Jyear + "-" + Jmonth + "-" + day;
  }
  return Jyear + "-" + Jmonth + "-" + day + " " + tHours + ":" + tMinutes + ":" + tSeconds;
}

export function getAccessToken() {
  return Vue.ls.get(ACCESS_TOKEN)
}


// 解析参数
// 自动获取地址栏的参数
export function getQueryStringRegExp(name,urlStr) {
  /*低版本不兼容*/
  // var reg = new RegExp('(^|\\?|&)' + name + '=(?<hostname>[^&]*)(\\s|&|$)', 'i')
  // if (reg.test(location.href)) {
  //   // let matchstr = RegExp.$2;
  //   let regArr = reg.exec(location.href);
  //   // let regArr = location.href.match(reg);
  //   if(regArr.groups)return;
  //   let matchstr = regArr.groups.hostname;
  //   if (matchstr.indexOf("%3A") !== -1) {
  //     matchstr = matchstr.replace(new RegExp("%3A", 'g'), ":")
  //   }
  //   return decodeURI(matchstr.replace(/\+/g, ' '))
  // }

  // const url = window.location.href;
  // const match = url.match(/[?&]paramName=([^&]+)/);
  // const paramValue = match && decodeURIComponent(match[1]);
  /*hash模式下不行*/
  // let urlParams = new URLSearchParams(window.location.search);
  // let paramValue = urlParams.get(name);

  const url = urlStr?urlStr:window.location.href;
  const paramsString = url.split('?')[1];
  if (paramsString) {
    const paramsArray = paramsString.split('&');
    const params = {};
    paramsArray.forEach(param => {
      if(param){
        const [key, value] = param.split('=');
        params[key] = decodeURIComponent(value);
      }
    });
    const paramValue = params[name] ||null;
    // console.log("返回 === ", paramValue)
    return paramValue
  }
  return null;
}
export function getHostNameLocal(){
  return Vue.ls.get("ONE_CLICK_HELP_MAC")
}
export function getBrowserInfo() {
  const ua = navigator.userAgent;
  const browser = {
    name: "",
    version: ""
  };
  if (ua.indexOf("Firefox") !== -1) {
    browser.name = "Firefox";
    browser.version = ua.split("Firefox/")[1].split(" ")[0];
  } else if (ua.indexOf("Opera") !== -1 || ua.indexOf("OPR") !== -1) {
    browser.name = "Opera";
    browser.version = ua.split("Opera|OPR/")[1].split(" ")[0];
  } else if (ua.indexOf("Trident") !== -1) {
    browser.name = "IE";
    browser.version = ua.split("rv:")[1];
  } else if (ua.indexOf("Edge") !== -1) {
    browser.name = "Edge";
    browser.version = ua.split("Edge/")[1].split(" ")[0];
  } else if (ua.indexOf("Chrome") !== -1) {
    browser.name = "Chrome";
    browser.version = ua.split("Chrome/")[1].split(" ")[0];
  } else if (ua.indexOf("Safari") !== -1) {
    browser.name = "Safari";
    browser.version = ua.split("Version/")[1].split(" ")[0];
  } else {
    browser.name = "unknown";
    browser.version = "0";
  }
  return browser;
}

export const isLowBrower = function () {
  const browser = getBrowserInfo();
  console.log("浏览器 === ", browser,parseFloat(browser.version))
  if (browser.name === "Chrome" && parseFloat(browser.version) < 70) {
    return true
  }  if (browser.name === "Firefox" && parseFloat(browser.version) < 60) {
    return true
  }
  else {
    return false
  }
}

// 扁平化树结构数据
export const flatTreeData = function (data, pid = null, flatData=[]) {
  for (let i = 0; i < data.length; i++) {
    let item = cloneObject(data[i])
    item.flatParentId = pid
    flatData.push(item)
    const parentId = item.id || item.key || item.value;
    if (data[i].children && data[i].children.length > 0) {
      flatTreeData(data[i].children, parentId,flatData);
    }

  }
  return flatData;
}
// 解析参数 自动获取地址栏的参数 格式为： /flowable/task-todo/index/key=eventProcess
export function getUrlParamValue(key) {
  // 将字符串按 / 分割成数组
  const urlString = window.location.href;
  const parts = urlString.split('/');
  // 遍历数组中的每个部分
  for (let i = 0; i < parts.length; i++) {
    const part = parts[i];
    // 检查当前部分是否以指定的键加上 = 开头
    if (part.startsWith(key + '=')) {
      // 提取等号后面的值
      const encodedValue = part.slice(key.length + 1);
      // 对提取的值进行 URL 解码
      return decodeURIComponent(encodedValue);
    }
  }
  // 如果没有找到匹配的键，返回 null
  return null;
}

// 生成比当前颜色较暗的颜色
export function darkenColor(color, amount) {
  // Convert hex color to RGB
  const hexToRgb = (hex) => {
    const bigint = parseInt(hex.slice(1), 16)
    return {
      r: (bigint >> 16) & 255,
      g: (bigint >> 8) & 255,
      b: bigint & 255
    }
  }

  // Convert RGB to hex
  const rgbToHex = (r, g, b) => {
    return (
      '#' +
      [r, g, b]
        .map((value) => {
          const hex = value.toString(16)
          return hex.length === 1 ? '0' + hex : hex
        })
        .join('')
    )
  }

  const { r, g, b } = hexToRgb(color)

  // Reduce brightness by the specified amount
  const darkenedR = Math.max(0, r - amount)
  const darkenedG = Math.max(0, g - amount)
  const darkenedB = Math.max(0, b - amount)

  return rgbToHex(darkenedR, darkenedG, darkenedB)
}

//生成当前颜色的对比色
export function invertColor(hex) {
  if (hex.indexOf('#') === 0) {
    hex = hex.slice(1);
  }
  // 处理3位hex颜色
  if (hex.length === 3) {
    hex = hex[0] + hex[0] + hex[1] + hex[1] + hex[2] + hex[2];
  }
  if (hex.length !== 6) {
    return '#000000';
  }
  // 转换为RGB
  const r = parseInt(hex.slice(0, 2), 16);
  const g = parseInt(hex.slice(2, 4), 16);
  const b = parseInt(hex.slice(4, 6), 16);
  // 取反
  const invertedR = (255 - r).toString(16).padStart(2, '0');
  const invertedG = (255 - g).toString(16).padStart(2, '0');
  const invertedB = (255 - b).toString(16).padStart(2, '0');

  return `#${invertedR}${invertedG}${invertedB}`;
}
