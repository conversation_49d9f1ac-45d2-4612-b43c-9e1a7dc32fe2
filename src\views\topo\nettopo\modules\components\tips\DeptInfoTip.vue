<template>
  <a-descriptions :column='1' size='small'>
    <a-descriptions-item label='运行状态' ><span :style='{color:status.color}'>{{status.title}}</span></a-descriptions-item>
    <a-descriptions-item label='网络联通'>{{status.title}}</a-descriptions-item>
    <a-descriptions-item label='业务数量'>{{showDevcInfo.businessCount}}</a-descriptions-item>
    <a-descriptions-item label='设备数量'>{{showDevcInfo.devCount}}</a-descriptions-item>
    <a-descriptions-item label='端口数量'>{{showDevcInfo.portNum}}</a-descriptions-item>
    <a-descriptions-item label='丢包率'>{{showDevcInfo.allLossPackageRate!==undefined&&showDevcInfo.allLossPackageRate!==null?showDevcInfo.allLossPackageRate + '%':'--'}}</a-descriptions-item>
  </a-descriptions>
</template>
<script>
export default {
  name: 'DeptInfoTip',
  props: {
    showDevcInfo: {
      type: Object,
      default: () => null
    },
    alarmInfo: {
      type: Array,
      default: () => null
    },
    alarmLevelList: [],
    onOffColors: {
      type: Object,
      default: () => {
        return { onLine: '#52c41a', offLine: '#8c8c8c' }
      }
    },
    alarmColor:{
      type:String,
      default:"#FFAE38"
    }
  },
  data() {
    return {
      curStatus:{},
    }
  },
  created() {
  },
  computed:{
    status(){
      if (this.showDevcInfo.status === 0) {
        return {
          title:'故障',
          color:this.onOffColors.offLine
        }
      } else if (this.showDevcInfo.status === 1) {
        if(this.alarmInfo>0){
          return {
            title:'不稳定',
            color:this.alarmColor
          }
        }else{
          return {
            title:'正常',
            color:this.onOffColors.onLine
          }
        }
      }
      return ""
    }
  },
  methods: {

  }
}
</script>



<style scoped lang='less'>

</style>