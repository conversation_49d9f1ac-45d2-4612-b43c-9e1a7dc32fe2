<template>
  <div>
    <div class="weeks">
      <span class="week">
        {{ currentWeek | localeWeekDay }}
      </span>
    </div>
    <div class="c-container">
      <!-- <div v-for="(obj, j) in days" :key="j">
        <div class="c-header">
          <div
            class="c-h-cell"
            v-for="(day, i) in obj.dayArr"
            :class="{ today: isToday(day) }"
            :key="i"
          ></div>
        </div>
      </div> -->
    </div>
  </div>
</template>
<script>
let $this;
export default {
  name: "MonthStyle",
  props: {
    days: {
      default: () => [],
    },
    currentWeek: {
      default: 1,
    },
  },
  data() {
    return {
      layWidth: 0,
      layHeight: 0,
    };
  },
  filters: {
    localeWeekDay(weekday) {
      let map = {
        1: "星期一",
        2: "星期二",
        3: "星期三",
        4: "星期四",
        5: "星期五",
        6: "星期六",
        7: "星期日",
      };
      return map[weekday];
    },
  },
  created() {
    $this = this;
  },
  mounted() {},
  methods: {
    isOtherMonth(item) {
      // let d = new Date()
      if (item.month === this.currentMonth && item.year === this.currentYear) {
        return false;
      } else {
        return true;
      }
    },
    isToday(item) {
      let d = new Date();
      if (
        item.month === d.getMonth() + 1 &&
        item.date === d.getDate() &&
        item.year === d.getFullYear()
      ) {
        return true;
      } else {
        return false;
      }
    },
  },
};
</script>
<style lang="less" scoped>
ul {
  padding: 0;
  margin: 0;
}
li {
  list-style: none;
}
.event-calender {
  width: 100%;
  margin: 0 auto;
}
.weeks {
  margin-top: 12px;
  display: flex;
  border: 1px solid #e1e4e7;
  border-left: none;
  border-bottom: none;
}
.week {
  height: 50px;
  line-height: 50px;
  flex: 1;
  text-align: center;
  border-left: 1px solid #e1e4e7;
  background: rgb(245, 248, 250);
}
.other-m {
  color: rgba(51, 71, 91, 0.45);
  background: rgb(245, 245, 245);
}
.today {
  color: red;
  background-color: #fcf8e3;
}
// .other-m-bg{

// }
.event-container {
  width: 100%;
  box-sizing: border-box;
  position: absolute;
  height: 0;
  .event-item {
    box-sizing: border-box;
    padding-top: 2px;
    position: absolute;
    .event-content {
      color: #FFFFFF;
      cursor: pointer;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      word-break: break-all;
      &.is-start {
        margin-left: 1px;
        border-top-left-radius: 12px;
        border-bottom-left-radius: 12px;
      }
      &.is-end {
        border-top-right-radius: 12px;
        border-bottom-right-radius: 12px;
      }
      .event-text {
        padding-left: 5px;
      }
    }
  }
}
.c-container {
  border: 1px solid #eee;
  border-top: none;
  box-sizing: border-box;
}
.c-header {
  display: flex;
  .c-h-cell {
    height: 180px;
    box-sizing: border-box;
    text-align: right;
    padding-right: 15px;
    flex: 1;
    border-top: 1px solid #e1e4e7;
    border-right: 1px solid #e1e4e7;
    &:last-child {
      border-right: none;
    }
  }
  .cell-day {
    display: inline-block;
    width: 100%;
    font-size: 16px;
    line-height: 45px;
    // cursor: pointer;
  }
}
.event-bg {
  position: relative;
  display: flex;
  .bg-cell {
    box-sizing: border-box;
    flex: 1;
    border-top: 1px solid #e1e4e7;
    border-right: 1px solid #e1e4e7;
    &:last-child {
      border-right: none;
    }
  }
}
</style>

