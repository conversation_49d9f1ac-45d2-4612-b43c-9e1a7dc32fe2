<template>
  <fm-making-form ref="makingFrom" style="height: 80vh" preview generate-code generate-json clearable>
    <template slot="action">
      <el-button v-permission="'flowable:form:save,flowable:form:update'" type="text" @click="back">返回</el-button>
      <el-button
        v-permission="'flowable:form:save,flowable:form:update'"
        type="text"
        icon="el-icon-upload"
        @click="btnSave"
        >保存</el-button
      >
    </template>
  </fm-making-form>
</template>
<script>
import { getAction, putAction } from '@/api/manage'
import { Message } from 'element-ui'

export default {
  name: 'FormEdit',
  data() {
    return {
      id: undefined,
      formData: {},
      defaultJson: { list: [], config: { labelWidth: 100, labelPosition: 'right', size: 'small' } },
    }
  },
  created() {
    if (this.$route.query.id) {
      this.id = this.$route.query.id
    }
    this.getFormData()
    this.$nextTick(() => {
      this.delFooter()
    })
  },
  methods: {
    getFormData() {
      if (!this.id) {
        Message.error('id is null')
        return
      }

      getAction('/flowableform/umpFlowableForm/queryById', { id: this.id }).then((res) => {
        if (res.success) {
          this.formData = res.result
          if (this.formData && this.formData.formJson) {
            setTimeout(() => this.$refs.makingFrom.setJSON(JSON.parse(this.formData.formJson)), 100)
          } else {
            setTimeout(() => this.$refs.makingFrom.setJSON(this.defaultJson), 100)
          }
        }
      })
    },
    back() {
      this.$router.go(-1)
    },
    btnSave() {
      var formJson = JSON.stringify(this.$refs.makingFrom.getJSON())
      var formData = {}
      formData.id = this.id
      formData.formJson = formJson
      // this.formData.formJson = JSON.stringify(this.$refs.makingFrom.getJSON())
      putAction('/flowableform/umpFlowableForm/edit', formData).then((res) => {
        if (res.success) {
          this.$message.success('保存成功！')
        } else {
          this.$message.error(res.message)
        }
      })
    },
    delFooter() {
      var foot = document.getElementsByClassName('el-footer')
      var foot0 = foot.item(0).remove(foot)
    },
  },
}
</script>