<template>
  <a-modal
    :title="title"
    :width="modalWidth"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭"
    wrapClassName="ant-modal-cust-warp"
    style="top:5%;height: 95%;overflow: auto"
  >
    <div style="margin-top: 25px;">
      <a-spin :spinning="confirmLoading">
        <a-form :form="form">
          <a-row>
            <a-col>
              <a-form-item label="类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input placeholder="请输入" v-decorator="['type', validatorNode.type]"></a-input>
              </a-form-item>
            </a-col>
            <a-col>
              <a-form-item label="事件来源" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-select placeholder="请选择" v-decorator="['source', validatorNode.source]">
                  <a-select-option value="1">1</a-select-option>
                  <a-select-option value="2">2</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>

            <a-col>
              <a-form-item label="优先级" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-select placeholder="请选择优先级" v-decorator="['priority', validatorNode.priority]">
                  <a-select-option value="1">1</a-select-option>
                  <a-select-option value="2">2</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col>
              <a-form-item label="联系电话" :labelCol="labelCol" :wrapperCol="wrapperCol" :required="required">
                <a-input placeholder="请输入" v-decorator="['contactWay', validatorNode.contactWay]"></a-input>
              </a-form-item>
            </a-col>
            <a-col>
              <a-form-item label="标题" :labelCol="labelCol" :wrapperCol="wrapperCol" :required="required">
                <a-input placeholder="请输入" v-decorator="['title', validatorNode.title]"></a-input>
              </a-form-item>
            </a-col>
            <a-col>
              <a-form-item label="事件描述" :labelCol="labelCol" :wrapperCol="wrapperCol" :required="required">
                <a-textarea placeholder="请输入" v-decorator="['description', validatorNode.description]"></a-textarea>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-spin>
      <a-tabs :animated="false" default-active-key="1" @change="callback">
        <a-tab-pane key="1" tab="附件">
          <div class="clearfix">
            <a-upload
              action="https://www.mocky.io/v2/5cc8019d300000980a055e76"
              list-type="picture-card"
              :file-list="fileList"
              @preview="handlePreview"
              @change="handleChange"
            >
              <div v-if="fileList.length < 8">
                <a-icon type="plus" />
                <div class="ant-upload-text">
                  Upload
                </div>
              </div>
            </a-upload>
            <a-modal :visible="previewVisible" :footer="null" @cancel="onCancel">
              <img alt="example" style="width: 100%" :src="previewImage" />
            </a-modal>
          </div>
        </a-tab-pane>
        <a-tab-pane key="2" tab="关联配置项" force-render>
          <a-table
            :columns="columns"
            :data-source="data"
            ref="table"
            size="middle"
            bordered
            rowKey="id"
          >
          </a-table>
        </a-tab-pane>
      </a-tabs>
    </div>
  </a-modal>
</template>
<script>
import pick from "lodash.pick";
export default {
  name: 'applyAdd',
  data() {
    return {
      title: '操作',
      confirmLoading: false,
      /* 弹框宽 */
      modalWidth: '55%',
      form: this.$form.createForm(this),
      visible: false,
      required: false,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      // 校验
      validatorNode: {
        type: {
          rules: [
            { required: true, message: '请输入类型!' },
            {
              min: 2,
              max: 30,
              message: '类型长度在 2 到 30 个字符',
              trigger: 'blur'
            }
          ]
        },
        priority: {
          rules: [{ required: true, message: '请选择优先级!', trigger: 'blur' }]
        },
        source: {
          rules: [{ required: true, message: '请选择事件来源!', trigger: 'blur' }]
        },
        contactWay: {
          rules: [
            { required: true, message: '请输入联系电话!' },
            {
              min: 2,
              max: 30,
              message: '联系电话长度在 2 到 30 个字符',
              trigger: 'blur'
            }
          ]
        },
        title: {
          rules: [
            { required: true, message: '请输入标题!' },
            {
              min: 2,
              max: 30,
              message: '标题长度在 2 到 30 个字符',
              trigger: 'blur'
            }
          ]
        },
        description: {
          rules: [
            { required: true, message: '请输入事件描述!' },
            {
              min: 2,
              max: 30,
              message: '事件描述长度在 2 到 80 个字符',
              trigger: 'blur'
            }
          ]
        }
      },
      // 上传相关
      previewVisible: false,
      previewImage: '',
      fileList: [
        // {
        //   uid: '-1',
        //   name: 'image.png',
        //   status: 'done',
        //   url: 'https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png',
        // }
      ],
      // 关联配置项
      columns: [
        {
          title: '序号',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function(t, r, index) {
            return parseInt(index) + 1
          }
        },
        {
          title: '分类',
          align: 'center',
          dataIndex: 'classify'
        },
        {
          title: '编号',
          align: 'center',
          dataIndex: 'uri'
        },
        {
          title: '名称',
          align: 'center',
          dataIndex: 'name'
        },
        {
          title: '状态',
          align: 'center',
          dataIndex: 'status'
        }
      ],
      data: [
        {
          id: '01',
          classify: '终端',
          uri: 'PZ202009100001',
          name: '台式电脑-124',
          status: '使用'
        },
        {
          id: '02',
          classify: '终端',
          uri: 'PZ202009100001',
          name: '台式电脑-124',
          status: '使用'
        },
        {
          id: '03',
          classify: '终端',
          uri: 'PZ202009100001',
          name: '台式电脑-124',
          status: '使用'
        },
        {
          id: '04',
          classify: '终端',
          uri: 'PZ202009100001',
          name: '台式电脑-124',
          status: '使用'
        },
        {
          id: '05',
          classify: '终端',
          uri: 'PZ202009100001',
          name: '台式电脑-124',
          status: '使用'
        },
        {
          id: '06',
          classify: '终端',
          uri: 'PZ202009100001',
          name: '台式电脑-124',
          status: '使用'
        },
        {
          id: '07',
          classify: '终端',
          uri: 'PZ202009100001',
          name: '台式电脑-124',
          status: '使用'
        },
        {
          id: '08',
          classify: '终端',
          uri: 'PZ202009100001',
          name: '台式电脑-124',
          status: '使用'
        },
        {
          id: '09',
          classify: '终端',
          uri: 'PZ202009100001',
          name: '台式电脑-124',
          status: '使用'
        },
        {
          id: '091',
          classify: '终端',
          uri: 'PZ202009100001',
          name: '台式电脑-124',
          status: '使用'
        },
        {
          id: '011',
          classify: '终端11111',
          uri: 'PZ202009100001',
          name: '台式电脑-124',
          status: '使用'
        }
      ]
    }
  },
  methods: {
    add() {
      this.edit({});
    },
    edit(record) {
      this.form.resetFields();
      this.model = Object.assign({}, record);
      this.visible = true;
      this.$nextTick(() => {
        this.form.setFieldsValue(
          pick(
            this.model,
            "id",
            "type",
            "source",
            "priority",
            "contactWay",
            "title",
            "description"
          )
        );
      });
    },
    // 关闭弹框
    close() {
      this.$emit('close')
      this.visible = false
      this.current = 0
    },
    // 提交
    handleOk() {
      let that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let formData = Object.assign(that.model, values)
          let params = {
            id: formData.id,
            type: formData.type,
            priority: formData.priority,
            source: formData.source,
            contactWay: formData.contactWay,
            title: formData.title,
            description: formData.description
          }
        }
      })
    },
    handleCancel() {
      this.close()
    },
    // tab
    callback(key) {
    },
    // 上传相关
    onCancel() {
      this.previewVisible = false
    },
    async handlePreview(file) {
      if (!file.url && !file.preview) {
        file.preview = await getBase64(file.originFileObj)
      }
      this.previewImage = file.url || file.preview
      this.previewVisible = true
    },
    handleChange({ fileList }) {
      this.fileList = fileList
    }
  }
}
</script>
<style scoped></style>
