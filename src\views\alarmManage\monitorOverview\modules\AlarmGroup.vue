<template>
 <!-- 告警分组统计 -->
  <div style="height: 100%;width: 100%;">
    <div v-if="chartData.length===0" style="padding-top:0.15rem;">
      <a-list :data-source="[]" />
    </div>
    <div v-else ref="alarmGroup" style="height: 100%;width: 100%;"></div>
  </div>
</template>
<script>
import { getAction } from '@/api/manage'
export default {
  data() {
    return {
      chartData: [],
      url: {
        list: '/monitor/Overview/alarm/group'
      }
    }
  },
  props: {
    timeData: {
      type: Object,
      default: () => {}
    }
  },
  watch: {
    timeData: {
      deep: true,
      handler(val) {
        this.getData()
      }
    }
  },
  mounted() {
    this.getData()
  },
  methods: {
    getData(startTime, endTime) {
      const params = {};
      if (this.timeData.startTime) {
        params.startTime = this.timeData.startTime;
      }
      if (this.timeData.endTime) {
        params.endTime = this.timeData.endTime;
      }
      getAction(this.url.list, params).then(res => {
        if (res.code == 200) {
          this.chartData = res.result
          if (this.chartData.length > 0) {
            this.$nextTick(() => {
              this.initChart(res.result)
            })
          } else {
            if (this.myChart) {
              this.myChart.dispose()
            }
          }
        }
      })
    },
    initChart(data) {
      if (data.length <= 0) {
        return
      }
      let xarr = []
      let yarr = []
      let yarrShodow = []
      data.forEach(e => {
        xarr.push(e.group_name || '')
        yarr.push(e.level_value)
        if (e.level_value && e.level_value > 0) {
          yarrShodow.push(e.level_value + 4)
        } else{
          yarrShodow.push(0)
        }
      })
      this.myChart = this.$echarts.init(this.$refs.alarmGroup)

      let option = {
        tooltip: {
          show: true,
          formatter: '{b} <br/>'+ '设备告警总数' + '{c} 条',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          top: '16%',
          left: '7%',
          right: '7%',
          bottom: '16%',
          // containLabel: true
        },
        legend: {
          show: true,
          icon: 'rect',
          top: 0,
          right: '7%',
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            color: 'rgba(0,0,0,0.65)',
            fontSize: 12
          }
        },
        dataZoom: [
          {
            id: 'dataZoomY',
            xAxisIndex: [0],
            show: false, //是否显示滑动条，不影响使用
            type: 'slider', // 这个 dataZoom 组件是 slider 型 dataZoom 组件
            startValue: 0, // 从头开始。
            endValue: 4,
            zoomLock: true,
            showDataShadow: false, //是否显示数据阴影 默认auto
            backgroundColor: 'rgba(255,255,255,0)',
            showDetail: false, //即拖拽时候是否显示详细数值信息 默认true
            realtime: true, //是否实时更新
            filterMode: 'filter',
            handleIcon: 'circle',
            handleStyle: {
              color: 'rgba(205,205,205,1)',
              borderColor: 'rgba(205,205,205,1)'
            },
            moveHandleSize: 0,
            brushSelect: false //刷选功能，设为false可以防止拖动条长度改变 ************（这是一个坑）
          },
          {
            type: 'inside',
            xAxisIndex: 0,
            zoomOnMouseWheel: false, //滚轮是否触发缩放
            moveOnMouseMove: true, //鼠标滚轮触发滚动
            moveOnMouseWheel: true
          }
        ],
        xAxis: [
          {
            type: 'category',
            data: xarr,
            axisTick: {
              show: false // 是否显示坐标轴轴线
            },
            axisLabel: {
              // 轴文字
              interval: 0, // 强制显示完整
              // 每行显示4个文字换行
              formatter: function(value) {
                var ret = '' // 拼接加 \n 返回的类目项
                var maxLength = 5 // 每项显示文字个数
                var valLength = value.length // X轴类目项的文字个数
                var rowN = Math.ceil(valLength / maxLength) // 类目项需要换行的行数
                if (rowN > 1) {
                  for (var i = 0; i < rowN; i++) {
                    var temp = '' // 每次截取的字符串
                    var start = i * maxLength // 开始截取的位置
                    var end = start + maxLength // 结束截取的位置
                    temp = value.substring(start, end) + '\n'
                    ret += temp // 拼接最终字符串
                  }
                  return ret
                } else {
                  return value
                }
              },
              textStyle: {
                color: 'rgba(0,0,0,0.4)',
                fontSize: 10
              }
            },
            splitLine: {
              show: false
            },
            boundaryGap: true,
            axisLine: {
              //坐标轴轴线相关设置。
              show: true,
              inside: false,
              lineStyle: {
                color: 'rgba(0,0,0,0.1)'
              }
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            min: 0,
            // splitNumber: 5,
            splitLine: { show: true },
            axisLabel: {
              //坐标轴刻度标签的相关设置。
              show: true,
              textStyle: {
                color: 'rgba(0,0,0,0.4)',
                fontSize: 10
              }
            },
            axisLine: {
              //坐标轴轴线相关设置。
              show: true,
              inside: false,
              lineStyle: {
                color: 'rgba(0,0,0,0.1)'
              }
            },
            axisTick: {
              show: false
            },
            splitLine: {
              lineStyle: {
                color: 'rgba(0,0,0,0.12)',
                type: 'dashed'
              }
            },
            show: true
          }
        ],
        series: [
          {
            name: '数量',
            type: 'bar',
            barWidth: 13,
            zlevel: 10,
            // barGap: '100%',
            data: yarr,
            itemStyle: {
              normal: {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: '#74CDFF'
                    },
                    {
                      offset: 1,
                      color: '#409EFF'
                    }
                  ]
                },
                barBorderRadius: [30, 30, 0, 0]
              }
            }
          },
          {
            name: '',
            type: 'bar',
            itemStyle: {
              normal: {
                color: 'rgba(64,158,255,0.1)'
              }
            },
            silent: true,
            barWidth: 28,
            barGap: '-157%',
            data: yarrShodow
          }
        ]
      }

      this.myChart.setOption(option)
      window.addEventListener('resize', () => {
        this.myChart.resize()
      })
    }
  }
}
</script>
<style scoped lang="less">
</style>