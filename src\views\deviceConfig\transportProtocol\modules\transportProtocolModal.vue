<template>
  <j-modal :title="title" :width="width" :visible="visible" :centered='true' switchFullscreen @ok="handleOk"
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }" :destroy-on-close="true" @cancel="handleCancel"
    cancelText="关闭">
    <transport-protocol-form ref="realForm" @ok="submitCallback" :disabled="disableSubmit">
    </transport-protocol-form>
  </j-modal>
</template>

<script>
  import transportProtocolForm from './transportProtocolForm'
  export default {
    name: 'transportProtocolModal',
    components: {
      transportProtocolForm
    },
    data() {
      return {
        title: '',
        width: 800,
        visible: false,
        disableSubmit: false
      }
    },
    methods: {
      add() {
        this.visible = true
        this.$nextTick(() => {
          this.$refs.realForm.add();
        })
      },
      edit(record) {
        this.visible = true
        this.$nextTick(() => {
          this.$refs.realForm.edit(record);
        })
      },
      close() {
        this.$emit('close');
        this.visible = false;
      },
      handleOk() {
        this.$refs.realForm.submitForm();
      },
      submitCallback() {
        this.$emit('ok');
        this.visible = false;
      },
      handleCancel() {
        this.close()
      }
    }
  }
</script>
<style lang="less" scoped>
  @import '~@assets/less/normalModal.less';
</style>