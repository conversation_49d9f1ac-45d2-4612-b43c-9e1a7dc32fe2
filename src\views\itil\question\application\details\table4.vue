<template>
    <a-table
        :columns="columns"
        :data-source="data"
        ref="table"
        size="middle"
        bordered
        rowKey="id"
    >
    </a-table>
</template>
<script>
import {getAction} from '@/api/manage'
export default {
  // 关联事件
  name: 'table4',
  data() {
    return {
      columns: [
        {
          title: '序号',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function(t, r, index) {
            return parseInt(index) + 1
          }
        },
        {
          title: '事件编号',
          align: 'center',
          dataIndex: 'processNumber'
        },
        {
          title: '事件标题',
          align: 'center',
          dataIndex: 'title'
        },
        {
          title: '事件类型',
          align: 'center',
          dataIndex: 'eventType'
        },
        {
          title: '事件状态',
          align: 'center',
          dataIndex: 'statusName'
        }
      ],
      data: [],
      url:{
        list:'/businessrelation/actZBusinessRelation/getEventList/',
      },
    }
  },
  methods: {
    getDataList(id) {
      getAction(this.url.list,{busId:id}).then(res => {
        if (res.success) {
          this.data = res.result;
          if (!res.result || res.result.length == 0) {
            this.$message.warning( "未找到关联事件数据");
          }
        }else {
          this.$message.error( res.message);
        }
      });
    },
  }
}
</script>
<style scoped></style>
