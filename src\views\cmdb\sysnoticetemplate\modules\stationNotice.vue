<template>
<div>
  <a-col :span='23' :key='"sendTo"'>
    <a-form-model-item class='two-words' label='收信人' prop='sendTo'  :rules='{required:personalizedObject.topic!=="processNotice",validator: customValidate}'>
      <a-select v-model='personalizedObject.sendTo' :allowClear='true' :getPopupContainer='(node) => node.parentNode'
                mode='multiple' option-filter-prop='children'
                placeholder='请选择收信人' show-search @change='changeValue'>
        <a-select-option v-for='item in usersList' :key='item.id' :value='item.id'>{{
            item.realName
          }}
        </a-select-option>
      </a-select>
    </a-form-model-item>
  </a-col>
  <a-col :span='1'>
    <a-popover title='参数说明'>
      <template slot='content' >
        <div style='width: 450px'>
          <p >1、流程类通知，收信人为非必填项；</p>
          <p >2、非流程类通知，收信人为必填项；</p>
          <p >3、通知为流程类通知时，若收信人非空，仅使用于模板测试，而在真正的流程办理过程中，通知会发送给流程定义中任务节点的审批人员，与模板配置的收信人无关；</p>
          <p >4、通知为非流程类通知时，收信人必填，在模板测试和实际应用过程中，通知都会发送给收信人。</p>
        </div>
      </template>
      <a-icon style='font-size: 20px; line-height: 40px' theme='twoTone' type='question-circle' />
    </a-popover>
  </a-col>
    <a-col :span='23' :key='"topic"'>
      <a-form-model-item class='two-words' label='订阅主题' prop='topic' :rules='{required:true,message: "请选择订阅主题！"}'>
        <a-select v-model='personalizedObject.topic' :allowClear='true' :getPopupContainer='(node) => node.parentNode'
                  placeholder='请选择订阅主题' @change='changeTopic($event)'>
          <a-select-option v-for='item in topicDict' :key='item.value' :value='item.value'>
            {{ item.text }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
    </a-col>
    <a-col :span='23'>
      <a-form-model-item label='是否语音播报' prop='speech'>
        <a-switch v-model='personalizedObject.speech' checked-children='是' un-checked-children='否'
          @change='switchSpeech'></a-switch>
      </a-form-model-item>
    </a-col>
    <a-col :span='23'>
      <a-form-model-item class='two-words' label='是否提示' prop='prompt'>
        <a-switch v-model='personalizedObject.prompt' checked-children='开' un-checked-children='关'
                  @change='switchValue'></a-switch>
      </a-form-model-item>
    </a-col>
    <a-col v-if='personalizedObject.prompt==true' :span='23' :key='"duration"'>
      <a-form-model-item label='提示保持时间' prop='duration'>
        <div style='display: flex;justify-content: start;align-items: center'>
          <a-input-number v-model='personalizedObject.duration' :min='0' :precision='0' :step='1' placeholder='请输入提示保持时间' @change='changeValue'/>
          <span style='margin-left: 10px'>/s(秒)</span>
          <a-popover title='参数说明'>
            <template slot='content'>
              <p>n=0秒：提示窗保持显示，手动操作才可关闭</p>
              <p>n>0秒：提示窗n秒后自动关闭</p>
            </template>
            <a-icon style='margin-left: 20px;font-size: 20px; line-height: 40px' theme='twoTone' type='question-circle' />
          </a-popover>
        </div>
      </a-form-model-item>
    </a-col>
    <a-col v-if='personalizedObject.prompt==true' :span='23' :key='"position"'>
      <a-form-model-item label='提示窗显示位置' prop='position'>
        <a-select v-model='personalizedObject.position' :getPopupContainer='(node) => node.parentNode'
                  placeholder='请选择提示窗显示位置' @change='changeValue'>
          <a-select-option v-for='(item,index) in tipPosition' :key='item.position' :value='item.position'>
            {{ item.rank }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
    </a-col>
    <a-col v-if='personalizedObject.prompt==true&&personalizedObject.topic==="processNotice"' :span='23' :key='"processRouter"'>
    <a-form-model-item label='目标页面' :rules='{required:true,message: "请选择需要跳转至的目标页面！"}' prop='processRouter'>
      <a-select v-model='personalizedObject.processRouter' :allowClear='true' :getPopupContainer='(node) => node.parentNode'
                placeholder='请选择目标页面' @change='changeValue'>
        <a-select-option v-for='item in targetPageDict' :key='item.value' :value='item.value'>
          {{ item.text }}
        </a-select-option>
      </a-select>
    </a-form-model-item>
  </a-col>
</div>
</template>

<script>
import { ajaxGetDictItems, getDictItemsFromCache } from '@api/api'

export default {
  name: 'stationNotice',
  props: {
    data: {
      type: Object,
      required: false,
      default: () => {
        let personalizedObject = {
          sendTo: undefined,
          topic: undefined,
          speech: false,
          prompt: false,
          duration: 5,
          position: 'bottomRight',//默认右下角
          processRouter:undefined
        }
        return personalizedObject
      }
    },
    usersList: {
      type: Array,
      required: true,
      default: []
    }
  },
  data() {
    return {
      tipPosition: [
        {
          position: 'bottomRight',
          rank: '右下角'
        },
        {
          position: 'topRight',
          rank: '右上角'
        },
        {
          position: 'topCenter',
          rank: '中上'
        },
        {
          position: 'bottomLeft',
          rank: '左下角'
        },
        {
          position: 'topLeft',
          rank: '左上角'
        }
      ],
      topicCode: 'station_notice_topic',
      topicDict: [],
      targetPageCode:'noticeAddress',
      targetPageDict: [],
      personalizedObject: {
        sendTo: undefined,
        topic: undefined,
        speech: false,
        prompt: false,
        duration: 5,
        position: 'bottomRight',//默认右下角
        processRouter:undefined
      },
      validatorRules: {
      /*  sendTo: [
          {required: true, validator: this.customValidate }
        ],*/
       /* topic: [
          { required: true, message: '请选择订阅主题！'}
        ],*/
      /*  processRouter:[
          { required: true, message: '请选择需要跳转至的目标页面！'}
        ],*/
      }
    }
  },
  watch: {
    data: {
      handler(val) {
        if (Object.keys(val).length > 0) {
          this.personalizedObject = val
        }
      },
      deep: true,
      immediate: true
    }
  },
  created() {
    this.initTopicDictData(this.topicCode,"topicDict")
    this.initTopicDictData(this.targetPageCode,"targetPageDict")
  },
  methods: {
    changeTopic(e){
      if(e!=='processNotice'){
        this.personalizedObject.processRouter=undefined
      }
      this.changeValue()
    },
    changeValue() {
      this.$emit('changeModelValue', this.personalizedObject)
    },
    switchValue(e) {
      if (e === false) {
        this.personalizedObject.duration = 5
        this.personalizedObject.position = 'bottomRight'
        this.personalizedObject.processRouter=undefined
      }
      this.changeValue()
    },
    switchSpeech(e) { },
    getTips(fullField) {
      let str = ''
      switch (fullField) {
        case 'sendTo' :
          str = '请选择收信人！'
          break
      }
      return str
    },
    customValidate(rule, value, callback) {
      if (rule.required) {
        if (value && value.length > 0) {
          callback()
        } else {
          callback(this.getTips(rule.fullField))
        }
      } else {
        callback()
      }
    },
    initTopicDictData(dictCode,list) {
        //优先从缓存中读取字典配置
     /*   if (getDictItemsFromCache(dictCode)) {
          this[list] = getDictItemsFromCache(dictCode)
          return
        }*/

        //根据字典Code, 初始化字典数组
        ajaxGetDictItems(dictCode, null).then((res) => {
          if (res.success) {
            this[list] = res.result
          }
        })
    }
  }
}
</script>

<style scoped>

</style>