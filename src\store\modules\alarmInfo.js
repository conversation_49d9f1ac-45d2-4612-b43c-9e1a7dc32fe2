import Vue from 'vue'
const alarmInfo = {
  state: {
    dataList:[]
  },
  mutations: {
    CLEAR_DATA_LIST:(state)=>{
      state.dataList = []
    },
    ADD_DATA_LIST: (state, record) => {
      state.dataList.push(record)
    },
    DEL_DATA_LIST:(state, record) => {
      state.dataList = state.dataList.filter(ele=> ele.deviceId !== record.deviceId)
    },
    UPDATE_DATA_LIST:(state, index,item) => {
      if(item){
         state.dataList.splice(index,1,item)
      }
      else {
       state.dataList.splice(index,1)
      }
    },
  },
  actions: {
    addDataList({ commit }, record){
      commit('ADD_DATA_LIST', record)
    },
    delDataList({ commit }, record){
      commit('DEL_DATA_LIST', record)
    },
    updateDataList({ commit }, index,item){
      commit('UPDATE_DATA_LIST', index,item)
    },
  }
}
export default alarmInfo