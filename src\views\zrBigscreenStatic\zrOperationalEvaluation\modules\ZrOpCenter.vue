<template>
  <div class='zr-op-center'>
    <div ref='opCenterContent' class='zr-op-center-content'>
      <img src='/zrBigScreen/opCenter.png' alt='Operational Center Background' style='width: 944px;'>
      <div class='level-text' :style='{"--hue":hues[levelText]}' @click='startComparing'>
        <span>{{levelText}}</span></div>
      <div class='line line-top'>
        <!--      <div class='dot'></div>-->
        <div class='line-1'></div>
        <div class='line-2  line-block' @click='categoryClick("wltl")'>
          <div class='line-shine'>
            <img  src='/zrBigScreen/lineTopIcon.png'>
          </div>
          <div class='line-info'>
            <div class='info-level'>{{indicatorTypes[0].level}}</div>
            <div class='info-text'>{{indicatorTypes[0].name}}</div>
          </div>
        </div>
      </div>
      <div class='line line-r-t'>
        <div class='line-1'></div>
        <div class='line-2 line-block' @click='categoryClick("yyfw")'>
          <div class='line-shine'>
            <img  src='/zrBigScreen/iconRT.png' alt='应用服务'>
          </div>
          <div class='line-info'>
            <div class='info-level'>{{indicatorTypes[1].level}}</div>
            <div class='info-text'>{{indicatorTypes[1].name}}</div>
          </div>
        </div>
      </div>
      <div class='line line-r-b'>
        <div class='line-1'></div>
        <div class='line-2 line-block' @click='categoryClick("sjzy")'>
          <div class='line-shine'>
            <img  src='/zrBigScreen/iconRB.png' alt='数据资源'>
          </div>
          <div class='line-info'>
            <div class='info-level'>{{indicatorTypes[2].level}}</div>
            <div class='info-text'>{{indicatorTypes[2].name}}</div>
          </div>
        </div>
      </div>
      <div class='line line-bottom'>
        <!--      <div class='dot'></div>-->
        <div class='line-1'></div>
        <div class='line-2 line-block' @click='categoryClick("ywbz")'>
          <div class='line-shine'>
            <img  src='/zrBigScreen/iconBottom.png' alt='运维保障'>
          </div>
          <div class='line-info'>
            <div class='info-level'>{{indicatorTypes[3].level}}</div>
            <div class='info-text'>{{indicatorTypes[3].name}}</div>
          </div>
        </div>
      </div>
      <div class='line line-l-b'>
        <div class='line-1'></div>
        <div class='line-2 line-block' @click='categoryClick("zlxy")'>
          <div class='line-shine'>
            <img  src='/zrBigScreen/iconLB.png' alt='质量效益'>
          </div>
          <div class='line-info'>
            <div class='info-level'>{{indicatorTypes[4].level}}</div>
            <div class='info-text'>{{indicatorTypes[4].name}}</div>
          </div>
        </div>
      </div>
      <div class='line line-l-t'>
        <div class='line-1'></div>
        <div class='line-2 line-block' @click='categoryClick("jzss")'>
          <div class='line-shine'>
            <img  src='/zrBigScreen/iconLT.png' alt='基础设施'>
          </div>
          <div class='line-info'>
            <div class='info-level'>{{indicatorTypes[5].level}}</div>
            <div class='info-text'>{{indicatorTypes[5].name}}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import resizeObserverMixin from '@views/statsCenter/com/resizeObserverMixin'
import {indicatorTypes,globalLevel } from '@views/zrBigscreens/modules/zrUtil'
export default {
  name: 'ZrOpCenter',
  mixins: [resizeObserverMixin],
  data() {
    return {
      indicatorTypes:window.zrIndicatorTypes||indicatorTypes,
      scaleNum: 1,
      levelText:window.zrGlobalLevel || globalLevel,
      hues:{
        A:100,
        B:185,
        C:400,
        D:360,
      }
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    //分类对比
    categoryClick(type){
      this.$emit("categoryComparing",type)
    },
    //整体对比
    startComparing(){
      this.$emit("startComparing")
    },
    // 监听页面缩放
    resizeObserverCb() {
      // 获取当前窗口的宽度和高度
      const width = window.innerWidth;
      const height = window.innerHeight;
      // 计算缩放比例
      this.scaleNum = Math.min(width / 1920, height / 1080,1);
      // 更新样式
      this.$refs.opCenterContent.style.transform = `scale(${this.scaleNum})`;
      this.$refs.opCenterContent.style.transformOrigin = 'center';
    }
  },
}
</script>

<style scoped lang='less'>
.zr-op-center {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  .zr-op-center-content {
    width: 944px;
    height: 591px;
    position: relative;
    .level-text{
      position: absolute;
      font-size: 120px;
      font-weight: bold;
      left: 46%;
      top: 25%;
      color: #fff;
      transform: translateZ(100);
      cursor: pointer;
      //--hue: 100;
      span {
        --blur-r: 0.15em;
        --main-sat: 50%;
        --shadow-sat: 70%;

        --main-light: 90%;
        --main-shadow-light: 50%;
        --mid-shadow-light: 40%;
        --dark-shadow-light: 30%;

        --main-shadow-color: hsl(
          var(--hue),
          var(--shadow-sat),
          var(--main-shadow-light)
        );

        --mid-shadow-color: hsl(
          var(--hue),
          var(--shadow-sat),
          var(--mid-shadow-light)
        );

        --dark-shadow-color: hsl(
          var(--hue),
          var(--shadow-sat),
          var(--dark-shadow-light)
        );

        --color: hsl(var(--hue), 74%, var(--main-light));

        --shadow: 0 0 var(--blur-r) var(--main-shadow-color),
        0 0 calc(var(--blur-r) * 2) var(--mid-shadow-color),
        0 0 calc(var(--blur-r) * 3) var(--main-shadow-color),
        0 0 calc(var(--blur-r) * 3.5) var(--main-shadow-color),
        0 0 calc(var(--blur-r) * 4) var(--main-shadow-color),
        0 0 calc(var(--blur-r) * 4.5) var(--dark-shadow-color);

        font-size: 120px;
        color: var(--color);
        text-shadow: var(--shadow);
      }
    }
    .line {
      position: absolute;

      .dot {
        width: 8px;
        height: 8px;
        background: #1EA2FF;
        border-radius: 50%;
        border: 1px solid #88F6FD;
      }

      .line-block {
        display: flex;
        align-items: end;
        min-width:120px;
        cursor: pointer;
        //box-shadow: 0 0 10px 0px rgba(255, 255,255, 0.5);
        .line-shine {
          width: 42px;
          height: 42px;
          background: url(/zrBigScreen/lineShine.png) no-repeat;
          background-size: 100% 100%;
          margin-right: 10px;
          img{
            width:16px;
            height:16px;
            position: absolute;
            top: 13px;
            left: 13px;
          }
        }
        .line-info {
          height: 46px;
          .info-level {
            font-weight: 500;
            font-size: 24px;
            color: #4DB6FF;
            line-height: 1;
            //margin-top:-12px;
            margin-bottom: 8px;
          }

          .info-text {
            font-weight: 400;
            font-size: 14px;
            color: #E5FFFE;
            line-height: 1
          }
        }
      }
    }

    .line-top {
      left: 58.9%;
      top: 25.5%;
      .line-1 {
        position: absolute;
        left: -160px;
        top: -53px;
        width: 168px;
        height: 61px;
        background: url(/zrBigScreen/lineTop.png) no-repeat;
        background-size: 100% 100%;
      }
      .line-2 {
        position: absolute;
        top: -105px;
        left: -160px;
      }
    }

    .line-r-t {
      left: 66.2%;
      top: 29%;
      .line-1 {
        position: absolute;
        top: -30px;
        width: 157px;
        height: 38px;
        background: url(/zrBigScreen/lineRight.png) no-repeat;
        background-size: 100% 100%;
      }
      .line-2 {
        position: absolute;
        top: -82px;
        left: 42px;
      }
    }

    .line-r-b {
      left: 73.4%;
      top: 56%;
      .line-1 {
        position: absolute;
        //left: -160px;
        top: -30px;
        width: 157px;
        height: 38px;
        background: url(/zrBigScreen/lineRight.png) no-repeat;
        background-size: 100% 100%;
      }
      .line-2 {
        position: absolute;
        top: -82px;
        left: 42px;
      }
    }

    .line-bottom {
      left: 63%;
      top: 81.1%;

      .line-1 {
        position: absolute;
        //left: -160px;
        //top:0px;
        width: 181px;
        height: 63px;
        background: url(/zrBigScreen/lineBottom.png) no-repeat;
        background-size: 100% 100%;
      }
      .line-2 {
        position: absolute;
        top: 8px;
        left: 85px;
      }
    }

    .line-l-b {
      left: 35%;
      top: 61%;

      .line-1 {
        position: absolute;
        left: -173px;
        top: 0px;
        width: 181px;
        height: 63px;
        background: url(/zrBigScreen/lineLB.png) no-repeat;
        background-size: 100% 100%;
      }
      .line-2 {
        position: absolute;
        top: 8px;
        left:-185px;
      }
    }

    .line-l-t {
      left: 34.5%;
      top: 32%;
      .line-1 {
        position: absolute;
        left: -149px;
        top: -32px;
        width: 157px;
        height: 40px;
        background: url(/zrBigScreen/lineLT.png) no-repeat;
        background-size: 100% 100%;
      }
      .line-2 {
        position: absolute;
        top: -85px;
        left:-157px;
      }
    }

  }
}
</style>