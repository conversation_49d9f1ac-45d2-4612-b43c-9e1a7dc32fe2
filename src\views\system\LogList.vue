<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll zhl zhll">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <!-- 查询区域 -->
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" style="width: 100%; flex: auto">
        <!--导航区域-->
        <div>
          <a-tabs :animated="false" defaultActiveKey="1" @change="callback">
            <a-tab-pane tab="登录日志" key="1"></a-tab-pane>
            <a-tab-pane tab="操作日志" key="2"></a-tab-pane>
          </a-tabs>
        </div>
        <!-- 查询区域 -->
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="日志内容">
                  <a-input :maxLength="maxLength" placeholder="请输入搜索关键词" :allowClear="true" v-model="queryParam.keyWord"/>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="创建时间">
                  <a-range-picker class="a-range-picker-choice-date" v-model="queryParam.createTimeRange"
                    format="YYYY-MM-DD" :placeholder="['开始时间', '结束时间']" @change="onDateChange" @ok="onDateOk" />
                </a-form-item>
              </a-col>
              <a-col :span="spanValue" v-show="tabKey === '2'">
                <a-form-item label="操作类型">
                  <j-dict-select-tag v-model="queryParam.operateType" placeholder="请选择操作类型" dictCode="operate_type" />
                </a-form-item>
              </a-col>
              <a-col :span="colBtnsSpan()">
                <span class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button type="primary" class="btn-search-style" @click="searchQuery" icon="search">查询</a-button>
                  <a-button type="primary" class="btn-reset-style" @click="searchReset" icon="reload">重置</a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
        <!-- 操作按钮区域 -->
        <div class='table-operator table-operator-style'>
          <a-button @click="handleExportXls(queryParam.logType==1?'登录日志':'操作日志')">导出</a-button>
          <a-dropdown v-if="selectedRowKeys.length > 0">
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item key="1" @click="batchDel">删除 </a-menu-item>
            </a-menu>
            <a-button>批量操作
              <a-icon type="down" />
            </a-button>
          </a-dropdown>
        </div>
        <!-- table区域-begin -->
        <a-table ref="table" rowKey="id" :columns="columns" :dataSource="dataSource"
          :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}" :pagination="ipagination" :loading="loading"
          style="white-space: nowrap" :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          @change="handleTableChange">
          <div v-show="queryParam.logType == 2" slot="expandedRowRender" slot-scope="record" style="margin: 0">
            <div style="margin-bottom: 5px;text-align: left">
              <a-badge status="success" style="vertical-align: middle" />
              <span>请求方法:{{ record.method }}</span>
            </div>
            <div style="text-align: left">
              <a-badge status="processing" style="vertical-align: middle" />
              <span>请求参数:{{ record.requestParam }}</span>
            </div>
            <div>
              <a-badge status="default" style="vertical-align: middle" />
              <span style="vertical-align: middle">返回结果:{{ record.responseResult }}</span>
            </div>
          </div>
          <!-- 字符串超长截取省略号显示-->
          <span style="white-space: nowrap" slot="logContent" slot-scope="text, record">
            <j-ellipsis :length="40" :value="text" />
          </span>
          <span slot="action" slot-scope="text, record">
            <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
              <a style="color: #409eff" v-has="'LogList:delete'">删除</a>
            </a-popconfirm>
          </span>
        </a-table>
        <!-- table区域-end -->
      </a-card>
    </a-col>
  </a-row>
</template>

<script>
  import {
    putParamsAction
  } from '@api/manage'
  import {
    filterObj
  } from '@/utils/util'
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import JEllipsis from '@/components/jeecg/JEllipsis'
  import {
    YqFormSearchLocation
  } from '@/mixins/YqFormSearchLocation'
  import {
    getAction
  } from '@/api/manage'

  export default {
    name: 'LogList',
    mixins: [JeecgListMixin, YqFormSearchLocation],
    components: {
      JEllipsis,
    },
    data() {
      return {
        maxLength:50,
        description: '这是日志管理页面',
        formItemLayout: {
          labelCol: {
            style: 'width:80px',
          },
          wrapperCol: {
            style: 'width:calc(100% - 80px)'
          }
        },
        // 查询条件
        queryParam: {
          ipInfo: '',
          createTimeRange: [],
          logType: '1',
          keyWord: '',
        },
        tabKey: '1',
        // 表头
        columns: [{
            title: '日志内容',
            align: 'left',
            dataIndex: 'logContent',
            scopedSlots: {
              customRender: 'logContent'
            },
            sorter: true,

            customCell: (record, rowIndex) => {
              let cellStyle = 'text-align: left; min-width: 10px;max-width:50px'
              return {
                style: cellStyle
              }
            },
          },
          {
            title: '操作人ID',
            dataIndex: 'userid',
            align: 'center',
            sorter: true,
          },
          {
            title: '操作人名称',
            dataIndex: 'username',
            align: 'center',
            sorter: true,
          },
          {
            title: 'IP',
            dataIndex: 'ip',
            align: 'center',
            sorter: true,
          },
          {
            title: '耗时(毫秒)',
            dataIndex: 'costTime',
            align: 'center',
            sorter: true,
          },
          {
            title: '日志类型',
            dataIndex: 'logType_dictText',
            /*customRender:function (text) {
              if(text==1){
                return "登录日志";
              }else if(text==2){
                return "操作日志";
              }else{
                return text;
              }
            },*/
            align: 'center',
          },
          {
            title: '创建时间',
            dataIndex: 'createTime',
            align: 'center',
            sorter: true,
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: 80,
            scopedSlots: {
              customRender: 'action'
            },
          },
        ],
        operateColumn: {
          title: '操作类型',
          dataIndex: 'operateType_dictText',
          align: 'center',
        },
        labelCol: {
          xs: {
            span: 1
          },
          sm: {
            span: 2
          },
        },
        wrapperCol: {
          xs: {
            span: 10
          },
          sm: {
            span: 16
          },
        },
        url: {
          list: '/sys/log/list',
          delete: '/sys/log/delete',
          deleteBatch: '/sys/log/DeleteBatch',
          exportXlsUrl: '/sys/log/exportXls',
          role: 'sys/role/hasButtonRole',
        },
      }
    },
    mounted() {
      this.getRole('LogList:delete')
    },
    methods: {
      getRole(value) {
        getAction(this.url.role, {
          role: value
        }).then((res) => {
          if (!res.result) {
            this.columns.splice(7, 1)
          }

        })
      },
      getQueryParams() {
        var param = Object.assign({}, this.queryParam, this.isorter)
        param.field = this.getQueryField()
        param.pageNo = this.ipagination.current
        param.pageSize = this.ipagination.pageSize
        delete param.createTimeRange // 时间参数不传递后台
        if (this.superQueryParams) {
          param['superQueryParams'] = encodeURI(this.superQueryParams)
          param['superQueryMatchType'] = this.superQueryMatchType
        }
        return filterObj(param)
      },

      // 重置
      searchReset() {
        var that = this
        var logType = that.queryParam.logType
        that.queryParam = {} //清空查询区域参数
        that.queryParam.logType = logType
        that.loadData(this.ipagination.current)
      },
      // 日志类型
      callback(key) {
        // 动态添加操作类型列
        this.queryParam = {}
        if (key == 2) {
          this.tabKey = '2'
          this.columns.splice(8, 0, this.operateColumn)
        } else if (this.columns.length == 9) {
          this.tabKey = '1'
          this.columns.splice(8, 1)
          this.queryParam.operateType = null
        }
        let that = this
        that.queryParam.logType = key
        that.selectedRowKeys = []
        that.loadData()

        //将查询、重置中原来的展开/收起功能写在此处
        this.doToggleSearch()
      },
      onDateChange: function (value, dateString) {
        this.queryParam.createTime_begin = dateString[0]
        this.queryParam.createTime_end = dateString[1]
      },
      onDateOk(value) {},
      handleDelete: function (id) {
        var that = this
        putParamsAction(that.url.delete, {
          id: id
        }).then((res) => {
          if (res.success) {
            //重新计算分页问题
            that.reCalculatePage(1)
            that.$message.success(res.message)
            that.loadData()
          } else {
            that.$message.warning(res.message)
          }
        })
      },
      batchDel: function () {
        if (this.selectedRowKeys.length <= 0) {
          this.$message.warning('请选择一条记录！')
          return
        } else {
          var ids = ''
          for (var a = 0; a < this.selectedRowKeys.length; a++) {
            ids += this.selectedRowKeys[a] + ','
          }
          var that = this
          this.$confirm({
            title: '确认删除',
            okText: '是',
            cancelText: '否',
            content: '是否删除选中数据?',
            onOk: function () {
              that.loading = true
              putParamsAction(that.url.deleteBatch, {
                  ids: ids
                })
                .then((res) => {
                  if (res.success) {
                    //重新计算分页问题
                    that.reCalculatePage(that.selectedRowKeys.length)
                    that.$message.success(res.message)
                    that.loadData()
                    that.onClearSelected()
                  } else {
                    that.$message.warning(res.message)
                  }
                })
                .finally(() => {
                  that.loading = false
                })
            }
          })
        }
      },
    },
  }
</script>
<style lang='less' scoped>
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';
</style>