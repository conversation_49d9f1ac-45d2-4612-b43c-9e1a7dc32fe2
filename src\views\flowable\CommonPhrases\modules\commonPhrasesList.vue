<template>
    <a-popover 
        v-model="visible"
        placement="leftBottom"
        :overlayClassName="overlayClassName"
        trigger="click"
        >
        <template slot="content">
            <div class="wrapper">
                <p v-for="(word, wIndex) in commnonPhrasesList"
                    :key="wIndex"
                    @click="chooseCommnonPhrase(wIndex)"
                    class="info">
                    <span v-if="word.languageInfo">{{ word.languageInfo }}</span>
                </p>

            </div>
        </template>
        <a-button type="primary"
           >
            选择常用语
        </a-button>
    </a-popover>
</template>

<script>
import { getAction } from '@/api/manage'
export default {
    name: 'commonPhrasesList',
    data() {
        return {
            visible: false,
            commnonPhrasesList: []
        }
    },
    props: {
        module: {
            type: String,
            default: ''
        },
        overlayClassName: {
            type: String,
            default: ''
        },
    },
    created() {
        this.getCommnonPhrases()
    },
    methods: {
        init() {
            this.visible = true;
        },
        // 获取常用语列表
        getCommnonPhrases() {
            getAction('/language/languageManage/queryAll', { module: this.module })
                .then((res) => {
                    if (res.result) {
                        this.commnonPhrasesList = res.result
                    }
                })
                .finally(() => {
                })
        },
        chooseCommnonPhrase(index) {
            this.visible = false;
            this.$emit('choose', this.commnonPhrasesList[index].languageInfo);
        },
    }
}
</script>
<style scoped lang="less">
.wrapper {
    max-width: 300px;
    max-height: 300px;
    padding-right: 10px;
    overflow: auto;

    .info {
        cursor: pointer;
    }
}
</style>
