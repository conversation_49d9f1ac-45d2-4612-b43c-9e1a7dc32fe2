<template>
  <!-- <keep-alive >
      <component :is="pageName" :dataDetail="data"/>
    </keep-alive> -->
  <div style="height:100%">
    <component
      :is="pageName"
      :dataDetail="data"
    />
  </div>
</template>
<script>
import serviceList from './serviceList'
import fillReport from './modules/fillReport'
import serviceDetail from './modules/serviceDetail'
export default {
  name: "serviceManage",
  data() {
    return {
      isActive: 0,
      data: {}
    };
  },
  inject: ['reload'],
  components: {
    serviceList,
    fillReport,
    serviceDetail
  },
  created() {
    this.pButton1(0);
  },
  //使用计算属性
  computed: {
    pageName() {
      switch (this.isActive) {
        case 0:
          return "serviceList";
          break;
        case 1:
          return "fillReport";
          break;
        default:
          return "serviceDetail";
          break;
      }
    }
  },
  methods: {
    pButton1(index) {
      this.isActive = index;
    },
    pButton2(index, item) {

      this.isActive = index;
      this.data = item;
    },
    pButton3(index) {
      this.isActive = index;
      this.reload();
    },

  }
}
</script>
<style scoped>
.div {
  display: flex;
  margin-right: 10px;
  height: 30px;
  line-height: 30px;
  padding-left: 25px;
  width: 6px;
}
</style>