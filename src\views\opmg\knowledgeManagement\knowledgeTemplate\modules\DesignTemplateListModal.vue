<template>
  <j-modal
    :title="title"
    :width="width"
    :centered='true'
    :visible="visible"
    switchFullscreen
    :destroyOnClose="true"
    @ok="handleOk"
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @cancel="handleCancel"
    cancelText="关闭" 
    >
    <design-template-list ref="realForm" @ok="submitCallback" :disabled="disableSubmit"></design-template-list>
  </j-modal>
</template>
<script>

  import DesignTemplateList from './DesignTemplateList'
  export default {
    name: 'DesignTemplateListModal',
    components: {
      DesignTemplateList
    },
    data () {
      return {
        title:'',
        width:1000,
        visible: false,
        disableSubmit: false
      }
    },
    methods: {
      edit1 (record) {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.edit(record);
        })
      },
      
      close () {
        this.$emit('close');
        this.visible = false;
      },
      handleOk () {
       this.close()
      },
      submitCallback(){
        this.$emit('ok');
        this.visible = false;
      },
      handleCancel () {
        this.close()
      }
    }
  }
</script>
<style lang="less" scoped>
@import '~@assets/less/normalModal.less';
</style>
