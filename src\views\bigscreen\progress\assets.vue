<template>
  <div class="body_box">
    <div class="header">
      <div class="header-right">
        <div class="header-right-time">
          <span>选择日期:</span>
          <div class="time-range-span">
            <a-range-picker dropdownClassName="big-screen-range-picker" v-model="this.todayTime" size="small"
              @change="onChange" />
          </div>
        </div>
        <button class="left-top-top-right-time-search" @click="getData">
          <a> 查询 </a>
        </button>
        <button class="export" @click="exportPDF">
          <a> 导出 </a>
        </button>
      </div>
    </div>
    <div class="body big-screen-theme" ref="imageDom">
      <div class="left">
        <div class="left-top">
          <div class="left-top-top">
            <div class="topTitle">
              <img src="@/assets/bigScreen/9.png" alt="" />
              <span>资产统计</span>
            </div>
          </div>
          <div class="left-top-body">
            <div class="left-top-body-leftTop">
              <div class="left-top-body-leftTop-left">
                <img src="@/assets/bigScreen/52.png" alt="" />
              </div>
              <div class="left-top-body-leftTop-right">
                <span>资产数量</span>
                <span>{{ assetsCount }}</span>
              </div>
            </div>
            <div class="left-top-body-rightTop">
              <div class="left-top-body-rightTop-left">
                <img src="@/assets/bigScreen/49.png" alt="" />
              </div>
              <div class="left-top-body-rightTop-right">
                <span>过期资产</span>
                <span>{{ assetsExpired }}</span>
              </div>
            </div>
            <div class="left-top-body-leftBottom">
              <div class="left-top-body-leftBottom-left">
                <img src="@/assets/bigScreen/50.png" alt="" />
              </div>
              <div class="left-top-body-leftBottom-right">
                <span>未过期资产</span>
                <span>{{ assetsNotExpire }}</span>
              </div>
            </div>
            <div class="left-top-body-rightBottom">
              <div class="left-top-body-rightBottom-left">
                <img src="@/assets/bigScreen/51.png" alt="" />
              </div>
              <div class="left-top-body-rightBottom-right">
                <span>即将过期资产</span>
                <span>{{ assetsSoonExpire }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="left-bottom">
          <div class="topTitle">
            <img src="@/assets/bigScreen/9.png" alt="" />
            <span>保修过期资产TOP10</span>
          </div>
          <div class="left-bottom-body">
            <div class="left-bottom-body-Historgram" id="expireTopHistorgram"></div>
          </div>
        </div>
      </div>
      <div class="right">
        <div class="right-top">
          <div class="topTitle">
            <img src="@/assets/bigScreen/9.png" alt="" />
            <span>资产分类统计</span>
          </div>
          <div class="right-top-body">
            <div class="right-top-body-Histogram" id="countHistogram"></div>
          </div>
        </div>
        <div class="right-bottom">
          <div class="topTitle">
            <img src="@/assets/bigScreen/9.png" alt="" />
            <span>资产分类占比</span>
          </div>
          <div class="right-bottom-body">
            <div class="right-bottom-body-Pie" id="categoryCountPic"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  import echarts from 'echarts'
  import {
    getAction
  } from '@/api/manage'

  export default {
    data() {
      return {
        assetsCount: '',
        assetsExpired: '',
        assetsNotExpire: '',
        assetsSoonExpire: '',
        todayTime: [],
        url: {
          count: '/data-analysis/cmdb/count1',
          countExport: '/data-analysis/cmdb/count/export',
          expireTop: '/data-analysis/cmdb/expire/top',
          cmdbCount: '/data-analysis/cmdb/count',
          categoryCount: '/data-analysis/cmdb/category/count',
        },
        time1: '',
        time2: '',
      }
    },
    created() {
      let moment = require('moment');
      let today = moment().format('YYYY-MM-DD')
      this.time1 = today
      this.time2 = today
      this.todayTime[0] = this.time1
      this.todayTime[1] = this.time2
    },
    mounted() {
      this.count()
      this.expireTop()
      this.cmdbCount()
      this.categoryCount()
    },
    methods: {
      getData() {
        this.count()
        this.expireTop()
        this.cmdbCount()
        this.categoryCount()
      },
      // 资产统计数据
      count() {
        getAction(this.url.count, {
          time1: this.time1,
          time2: this.time2
        }).then((res) => {
          if (res.code == 200) {
            this.assetsCount = res.result.assetsCount
            this.assetsExpired = res.result.assetsExpired
            this.assetsNotExpire = res.result.assetsNotExpire
            this.assetsSoonExpire = res.result.assetsSoonExpire
          }
        })
      },

      onChange(dates, dateStrings) {
        this.todayTime = dateStrings
        this.time1 = dateStrings[0]
        this.time2 = dateStrings[1]
      },

      // 导出
      exportPDF() {
        getAction(this.url.countExport, {
          time1: this.time1,
          time2: this.time2
        }).then((res) => {
          if (res.code == 200) {
            window.open(window._CONFIG['domianURL'] + '/sys/common/downloadFile/' + res.result)
          } else {
            this.$message.error(res.message)
          }
        })
      },

      // 保修过期资产数据
      expireTop() {
        getAction(this.url.expireTop, {
          time1: this.time1,
          time2: this.time2
        }).then((res) => {
          if (res.code == 200) {
            this.expireTopHistorgram(res.result)
          }
        })
      },

      // 保修过期资产柱状图
      expireTopHistorgram(data) {
        let xArr = []
        let yArr = []
        data.forEach((e) => {
          xArr.push(e.name)
          yArr.push(e.value)
        })

        let myChart = this.$echarts.init(document.getElementById('expireTopHistorgram'))
        myChart.setOption({
          title: {
            text: '保修期(天)',
            textStyle: {
              color: '#4f92bf',
              fontSize: '14',
              fontWeight: 'normal',
            },
          },
          tooltip: {
            show: true,
            transitionDuration: 0, //echart防止tooltip的抖动
          },
          xAxis: {
            type: 'category',
            splitLine: {
              show: false
            }, //去除网格线
            show: true,
            data: xArr,
            axisLine: {
              show: true,
              lineStyle: {
                color: '#4f92bf',
              },
            },
          },
          yAxis: {
            type: 'value',
            splitLine: {
              show: true,
              lineStyle: {
                color: '#223641',
              },
            },
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false, //y轴线消失
              lineStyle: {
                //y轴字体颜色
                color: '#4f92bf',
              },
            },
          },

          grid: {
            top: 40,
            left: 80, // 调整这个属性
            right: 10,
            bottom: 40,
          },
          series: [{
            data: yArr,
            type: 'bar',
            barWidth: 10, //柱图宽度
            itemStyle: {
              normal: {
                barBorderRadius: [10, 10, 10, 10],
                color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                    offset: 0,
                    color: '#3679fb',
                  },
                  {
                    offset: 1,
                    color: '#0cf6f7',
                  },
                ]),
              },
            },
          }, ],
        })
        window.addEventListener('resize', () => {
          myChart.resize()
        })
      },

      // 资产分类统计数据
      cmdbCount() {
        getAction(this.url.cmdbCount, {
          time1: this.time1,
          time2: this.time2
        }).then((res) => {
          if (res.code == 200) {
            this.countClassification(res.result, res.result.line)
          }
        })
      },

      // 资产分类统计柱状图
      countClassification(data, dataList) {
        let xArr = []
        let yArr1 = []
        let yArr2 = []
        dataList.forEach((e) => {
          xArr.push(e.name)
          yArr1.push(e.value1)
          yArr2.push(e.value2)
        })

        let myChart = this.$echarts.init(document.getElementById('countHistogram'))
        myChart.setOption({
          legend: {
            data: [data.value1, data.value2],
            left: 'right',
            textStyle: {
              color: '#edf1fc',
            },
          },
          tooltip: {
            show: true,
            transitionDuration: 0, //echart防止tooltip的抖动
          },
          xAxis: [{
            type: 'category',
            data: xArr,
            axisLine: {
              lineStyle: {
                //x轴字体颜色
                color: '#41759c',
              },
            },
          }, ],
          yAxis: [{
            type: 'value',
            axisLine: {
              show: false, //y轴线消失
              lineStyle: {
                //y轴字体颜色
                color: '#41759c',
              },
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: ['#1c2a37'],
                width: 2,
                type: 'solid',
              },
            },
          }, ],
          grid: {
            top: 40,
            right: 10,
            bottom: 40,
            left: 40,
          },
          series: [{
              name: data.value1,
              type: 'bar',
              data: yArr1,
              barWidth: 10, //柱图宽度
              itemStyle: {
                normal: {
                  color: '#259cfa',
                },
              },
            },
            {
              name: data.value2,
              type: 'bar',
              data: yArr2,
              barWidth: 10, //柱图宽度
              itemStyle: {
                normal: {
                  color: '#feb528',
                },
              },
            },
          ],
        })
        window.addEventListener('resize', () => {
          myChart.resize()
        })
      },

      // 资产分类占比数据
      categoryCount() {
        getAction(this.url.categoryCount, {
          time1: this.time1,
          time2: this.time2
        }).then((res) => {
          if (res.code == 200) {
            this.categoryCountPic(res.result)
          }
        })
      },

      // 资产分类占比饼图
      categoryCountPic(data) {
        let myChart = this.$echarts.init(document.getElementById('categoryCountPic'))
        myChart.setOption({
          tooltip: {
            show: false,
            transitionDuration: 0, //echart防止tooltip的抖动
          },
          label: {
            formatter(data) {
              let value = data.name + ':' + data.percent + '%'
              return value
            },
            color: '#fff',
          },
          legend: {
            orient: 'horizontal',
            top: 'bottom',
            left: 'center',
            icon: 'circle',
            textStyle: {
              color: '#fff',
            },
          },

          color: ['#1a7ede', '#64aec9', '#64aec9', '#5ebb9c', '#28a94f', '#28a94f', '#d7b015', '#c68251',
            '#ac3a5c'
          ],
          series: [{
            hoverAnimation: false, // 取消掉饼图鼠标移上去时自动放大
            type: 'pie',
            radius: '50%',
            data: data,
          }, ],
        })
        window.addEventListener('resize', () => {
          myChart.resize()
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  ::v-deep .ant-calendar-picker {
    width: 3.95rem
      /* 316/80 */
    ;
    height: 100%;

    .ant-calendar-picker-input.ant-input {
      background-color: #101217;
      color: #909090;
      height: 100%;
      display: flex;
      align-items: center;

      .ant-calendar-range-picker-separator {
        color: #feffff;
        line-height: 0.375rem
          /* 30/80 */
        ;
      }
    }
  }

  .body_box {
    width: 100%;
    height: 100%;
    padding: 0 0.2rem 0.1rem 0.2rem;
    display: flex;
    flex-direction: column;

    .header {
      width: 100%;
      height: 7%;
      display: flex;
      justify-content: flex-end;

      .header-right {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        margin-right: 0.65rem
          /* 52/80 */
        ;

        .header-right-time {
          font-size: 0.175rem
            /* 14/80 */
          ;
          font-family: PingFang SC;
          letter-spacing: 0px;
          font-weight: 100;
          color: #ffffff;
          display: flex;
          align-items: center;

          .time-range-span {
            margin-right: 0.4375rem
              /* 35/80 */
            ;
            margin-left: 0.2rem
              /* 16/80 */
            ;
          }
        }

        .left-top-top-right-time-search {
          width: 0.85rem
            /* 68/80 */
          ;
          height: 0.4rem
            /* 32/80 */
          ;
          background: none;
          border: 2px solid #8f9094;
          border-radius: 10%;
          margin-right: 0.25rem
            /* 20/80 */
        }

        a {
          color: #fff;
        }

        .export {
          width: 0.85rem
            /* 68/80 */
          ;
          height: 0.4rem
            /* 32/80 */
          ;
          background: #1187d1;
          border: 0px;
          border-radius: 10%;
        }
      }
    }
  }

  .topTitle {
    height: 16%;
    display: flex;
    align-items: center;
    font-size: 0.225rem
      /* 18/80 */
    ;
    color: #45c5e0;
    padding-left: 0.15rem
      /* 12/80 */
    ;
    letter-spacing: 0.025rem
      /* 2/80 */
    ;

    img {
      width: 0.125rem
        /* 10/80 */
      ;
      height: 0.1625rem
        /* 13/80 */
      ;
      margin-right: 0.0875rem
        /* 7/80 */
      ;
    }
  }

  .body {
    width: 100%;
    height: 100%;
    padding: 0
      /* 20/80 */
      0.2rem 0.1rem 0.2rem;
    display: flex;
    justify-content: space-between;
    background: #222224;

    .left {
      width: 49.7%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .left-top {
        width: 100%;
        height: 49.5%;
        background: #111217;
        border-radius: 0.075rem
          /* 6/80 */
        ;

        .left-top-top {
          width: 100%;
          height: 16%;
          display: flex;
          justify-content: space-between;

          .topTitle {
            height: 100%;
            display: flex;
            align-items: center;
            font-size: 0.225rem
              /* 18/80 */
            ;
            color: #45c5e0;
            padding-left: 12px;
            letter-spacing: 4px;

            img {
              width: 10px;
              height: 13px;
              margin-right: 7px;
            }
          }

          .left-top-top-right {
            width: 70%;
            display: flex;
            align-items: center;
            // justify-content: space-between;
            justify-content: flex-end;
            margin-right: 0.35rem
              /* 28/80 */
            ;

            .left-top-top-right-time {
              font-size: 0.175rem
                /* 14/80 */
              ;
              font-family: PingFang SC;
              letter-spacing: 0px;
              font-weight: 100;
              color: #ffffff;
              display: flex;
              align-items: center;

              .time-range-span {
                margin-right: 0.4375rem
                  /* 35/80 */
                ;
                margin-left: 0.2rem
                  /* 16/80 */
                ;
              }
            }

            .left-top-top-right-time-search {
              width: 0.85rem
                /* 68/80 */
              ;
              height: 0.4rem
                /* 32/80 */
              ;
              background: none;
              border: 2px solid #8f9094;
              border-radius: 10%;
              margin-right: 0.25rem
                /* 20/80 */
              ;

              a {
                color: #fff;
              }
            }

            .export {
              width: 0.85rem
                /* 68/80 */
              ;
              height: 0.4rem
                /* 32/80 */
              ;
              background: #1187d1;
              border: 0px;
              border-radius: 10%;

              // margin-right: 0.35rem /* 28/80 */;
              a {
                color: #fff;
              }
            }
          }
        }

        .left-top-body {
          width: 100%;
          height: 88%;
          padding: 0.125rem
            /* 10/80 */
            0.35rem 0.35rem 0.275rem
            /* 22/80 */
          ;
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;

          .left-top-body-leftTop {
            width: 49%;
            height: 44%;
            background: #202126;
            display: flex;

            .left-top-body-leftTop-left {
              width: 33%;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
            }

            .left-top-body-leftTop-right {
              width: 60%;
              height: 100%;
              display: flex;
              flex-direction: column;
              align-items: flex-start;
              justify-content: center;

              span:nth-child(1) {
                color: #f5f7f6;
                font-size: 0.25rem
                  /* 20/80 */
                ;
              }

              span:nth-child(2) {
                color: #49ccff;
                font-size: 0.425rem
                  /* 34/80 */
                ;
                letter-spacing: 4px;
                font-weight: 600;
              }
            }
          }

          .left-top-body-rightTop {
            width: 49%;
            height: 44%;
            background: #202126;
            display: flex;

            .left-top-body-rightTop-left {
              width: 33%;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
            }

            .left-top-body-rightTop-right {
              width: 60%;
              height: 100%;
              display: flex;
              flex-direction: column;
              align-items: flex-start;
              justify-content: center;

              span:nth-child(1) {
                color: #f5f7f6;
                font-size: 0.25rem
                  /* 20/80 */
                ;
              }

              span:nth-child(2) {
                color: #36fcef;
                font-size: 0.425rem
                  /* 34/80 */
                ;
                letter-spacing: 4px;
                font-weight: 600;
              }
            }
          }

          .left-top-body-leftBottom {
            width: 49%;
            height: 44%;
            background: #202126;
            display: flex;

            .left-top-body-leftBottom-left {
              width: 33%;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
            }

            .left-top-body-leftBottom-right {
              width: 60%;
              height: 100%;
              display: flex;
              flex-direction: column;
              align-items: flex-start;
              justify-content: center;

              span:nth-child(1) {
                color: #f5f7f6;
                font-size: 0.25rem
                  /* 20/80 */
                ;
              }

              span:nth-child(2) {
                color: #2947ff;
                font-size: 0.425rem
                  /* 34/80 */
                ;
                letter-spacing: 4px;
                font-weight: 600;
              }
            }
          }

          .left-top-body-rightBottom {
            width: 49%;
            height: 44%;
            background: #202126;
            display: flex;

            .left-top-body-rightBottom-left {
              width: 33%;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
            }

            .left-top-body-rightBottom-right {
              width: 60%;
              height: 100%;
              display: flex;
              flex-direction: column;
              align-items: flex-start;
              justify-content: center;

              span:nth-child(1) {
                color: #f5f7f6;
                font-size: 0.25rem
                  /* 20/80 */
                ;
              }

              span:nth-child(2) {
                color: #fdca01;
                font-size: 0.425rem
                  /* 34/80 */
                ;
                letter-spacing: 4px;
                font-weight: 600;
              }
            }
          }
        }
      }

      .left-bottom {
        width: 100%;
        height: 49.5%;
        background: #111217;
        border-radius: 0.075rem
          /* 6/80 */
        ;

        .left-bottom-body {
          width: 100%;
          height: 84%;
          display: flex;
          align-items: center;
          justify-content: center;

          .left-bottom-body-Historgram {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }
    }

    .right {
      width: 49.7%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .right-top {
        width: 100%;
        height: 49.5%;
        background: #111217;
        border-radius: 0.075rem
          /* 6/80 */
        ;

        .right-top-body {
          width: 100%;
          height: 84%;
          display: flex;
          align-items: center;
          justify-content: center;

          .right-top-body-Histogram {
            width: 98%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }

      .right-bottom {
        width: 100%;
        height: 49.5%;
        background: #111217;
        border-radius: 0.075rem
          /* 6/80 */
        ;

        .right-bottom-body {
          width: 100%;
          height: 84%;
          display: flex;
          // align-items: center;
          justify-content: center;

          .right-bottom-body-Pie {
            width: 100%;
            height: 92%;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }
    }
  }
</style>