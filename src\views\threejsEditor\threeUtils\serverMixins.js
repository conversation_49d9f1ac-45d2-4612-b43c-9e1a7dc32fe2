import * as THREE from 'three'
import { getAction } from '@/api/manage'
export const serverMixins = {
    data() {
        return {
            materials: [],
            matArrayN: [],
            matArrayW: [],
            matArrayR: [],
            imgBaseUrl: process.env.BASE_URL,
            equipment_top_material: null,
            equipment_front_material: null,
            alarmInfos: [],
            alarmLevelList: [],
            alarmMaterials: {},
            equipmentMaterials: {},
        }
    },
    created() {
        this.createEquipmentMaterials()
    },
    watch: {
        '$store.getters.deviceAlarms': {
            // immediate:true,
            deep: true,
            handler(e) {
                let alarmInfos = []
                e.forEach(el => {
                    const maxAlarm = el.alarmInfo.reduce((prev, curr, i, a) =>
                        curr.alarmLevel > prev.alarmLevel ? curr : prev
                    )
                    let tem = {
                        level: maxAlarm.alarmLevel + "",
                        templateName: maxAlarm.alarmName,
                        deviceCode: el.deviceCode
                    }
                    alarmInfos.push(tem)
                });
                this.alarmInfos = alarmInfos;
                this.setJiguiLogo(this.alarmInfos);
            },
        },
    },
    methods: {
        // 给机柜添加告警标识
        setJiguiLogo(alarmInfos) {
            // console.log("获取到告警信息 === ", alarmInfos)
            if (alarmInfos) {
                let jiguis = scene.children.filter(el => el.name === 'yq_group_jigui')
                jiguis.forEach(node => {
                    let level = 0;
                    let servers = node.children.filter(cnode => {
                        return cnode.name === "服务器"
                    })
                    if (servers.length > 0) {
                        servers.forEach(server => {
                            let alarmInfo = alarmInfos.find(aInfo => aInfo.deviceCode === server.userData.deviceCode);
                            if (alarmInfo) {
                                if (this.alarmMaterials['alarm_' + alarmInfo.level]) {
                                    server.material = this.alarmMaterials['alarm_' + alarmInfo.level]
                                }
                                else {
                                    server.material = this.getEquipmentMaterial(server.userData.u)
                                }
                                if (level < alarmInfo.level) {
                                    level = alarmInfo.level
                                }
                            }
                            else {
                                server.material = this.getEquipmentMaterial(server.userData.u)
                            }
                        })
                    }
                    let warnLogo = node.children.find(el => el.name === 'warnSign');
                    if (warnLogo) {
                        let alarm = this.alarmLevelList.find(el=>el.value == level)
                        if(alarm){
                            warnLogo.material.color.set(alarm.color)
                        }
                        warnLogo.visible = level !== 0;
                    }

                })
            }
        },
        createResource(info, g) {
            let matArr = this.getEquipmentMaterial(info.u)
            var col = 5 * info.layer;//层高 layer
            // var jgH = 3 * 42 / 2;//当前机柜上移的值
            var h = 5 * info.u;//资源U数，乘3，得绘制时资源高度
            var cab = new THREE.BoxGeometry(46, h - 2, 46);//创建资源时高度减0.5是为了资源间有空隙
            var cabMesh = new THREE.Mesh(cab, matArr);
            cabMesh.position.x = 0;
            cabMesh.position.y = col - h / 2 + 10;//层高-绘制资源高度的一半
            cabMesh.position.z = 0;
            cabMesh.name = "服务器";
            cabMesh.userData = {
                deviceId: info.id,
                deviceCode: info.code,
                name: info.name,
                u: info.u,
            }
            cabMesh.geometry.name = info.name;
            g.add(cabMesh);
        },

        // createServerMaterail(u) {
        //     let normal_material = new THREE.MeshBasicMaterial({ transparent: true, color: 0x1E3674 })
        //     let loader = new THREE.TextureLoader();
        //     let equipment_top = this.imgBaseUrl + "threeImg/equipment_top.jpg"
        //     if(u>=1 && u <=47){

        //     }
        //     let equipment_front = this.imgBaseUrl + "threeImg/resource/equipment_front-" + u + "U.png"
        //     let side = new THREE.MeshBasicMaterial({
        //         transparent: true,
        //         opacity: 1.0,
        //         map: loader.load(equipment_top)
        //     });
        //     let front = new THREE.MeshBasicMaterial({
        //         transparent: true,
        //         opacity: 1.0,
        //         map: loader.load(equipment_front)
        //     });
        //     let matArr = [front,side,side,side,side,side]
        //     // this.matArrayN = matArr
        //     return matArr

        // },
        createEquipmentMaterials() {
            let loader = new THREE.TextureLoader();
            let equipment_top = this.imgBaseUrl + "threeImg/equipment_top.jpg"
            let top_mat = new THREE.MeshBasicMaterial({
                transparent: true,
                opacity: 1.0,
                map: loader.load(equipment_top),
            });
            for (let i = 0; i < 47; i++) {
                let u = i + 1;
                let equipment_front = this.imgBaseUrl + "threeImg/resource/equipment_front-" + u + "U.png"
                let front_mat = new THREE.MeshBasicMaterial({
                    transparent: true,
                    opacity: 1.0,
                    map: loader.load(equipment_front)
                });
                this.equipmentMaterials["equi_" + u] = [front_mat, top_mat, top_mat, top_mat, top_mat, top_mat]
            }
            this.equipmentMaterials.normal = [top_mat, top_mat, top_mat, top_mat, top_mat, top_mat]

        },
        getEquipmentMaterial(u){
            if(u >= 1 && u <=47){
                return this.equipmentMaterials["equi_" + u]
            }else{
                return  this.equipmentMaterials.normal
            }
        },
        createAlarmMaterial() {
            this.alarmLevelList.forEach(el => {
                let tem_material = new THREE.MeshBasicMaterial({ transparent: true, color: el.color })
                let arr = new Array(6).fill(tem_material);
                this.alarmMaterials['alarm_' + el.value] = arr;
            })
        },
        // 获取设备告警级别列表
        async getAlarmLevelData() {
            await getAction("/alarm/alarmLevel/getLevelList").then((res) => {
                if (res.success) {
                    this.alarmLevelList = res.result;
                    this.createAlarmMaterial();
                }
            })
        },
    }
}