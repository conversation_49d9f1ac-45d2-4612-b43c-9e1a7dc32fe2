<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    :centered='true'
    @ok="handleOk"
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @cancel="handleCancel"
    cancelText="关闭">
    <device-classification-form ref="realForm" @ok="submitCallback" :disabled="disableSubmit">
    </device-classification-form>
  </j-modal>
</template>

<script>
  import deviceClassificationForm from './deviceClassificationForm.vue'
  export default {
    name: 'deviceClassificationModal',
    components: {
      deviceClassificationForm,
    },
    data() {
      return {
        title: '',
        width: '850px',
        visible: false,
        disableSubmit: false,
      }
    },

    methods: {
      add() {
        this.visible = true
        this.$nextTick(() => {
          this.$refs.realForm.add();
        })
      },
      edit(record) {
        this.visible = true
        this.$nextTick(() => {
          this.$refs.realForm.edit(record);
        })
      },
      close() {
        this.$emit('close');
        this.visible = false;
      },
      handleOk() {
        this.$refs.realForm.submitForm();
      },
      submitCallback() {
        this.$emit('ok');
        this.visible = false;
      },
      handleCancel() {
        this.close()
      }
    }
  }
</script>
<style scoped lang='less'>
  @import '~@assets/less/normalModal.less';
</style>