<template>
  <a-form-item label="文件类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
    <a-select default-value="请选择" @change="handleChange">
      <a-select-option value="">
        请选择
      </a-select-option>
      <a-select-option value="url">
        URL（链接）
      </a-select-option>
      <a-select-option value="base">
        Base64（Base64编码）
      </a-select-option>
      <a-select-option value="binary">
        Binary（二进制）
      </a-select-option>
    </a-select>
  </a-form-item>
</template>

<script>
  export default {
    name:'fileMetadata',
    data() {
      return {
        labelCol: {
          xs: { span: 5 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        },
        value: 3,
      }
    },
    methods: {
        handleChange(value) {
        },
    },
  }
</script>

<style>
</style>
