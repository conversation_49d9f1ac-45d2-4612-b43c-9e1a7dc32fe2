<template>
  <a-modal
    :maskClosable="false"
    :visible="moduleVisible"
    :width="1200"
    :centered='true'
    cancelText="关闭"
    title="发起流程"
    @cancel="handleCancel"
  >
    <a-spin :spinning='confirmLoading'>
    <div v-if="generateStartFormVisible" :style="{height: `${scrollHeight || '476'}px`}" class="form-wrapper">
      <k-form-build
        ref="generateStartForm"
        :dynamicData="dynamicData"
        :value="startFormJson"
        :defaultValue="formDefault"
        @submit="handleSubmit"
        @assetsHandler="assetsHandler"
      />
    </div>

    <div ref="bottomFixed">
      <a-divider style="margin: 0px 0px 20px" type="horizontal"></a-divider>
      <a-form>
        <a-row :gutter="24">
          <a-col v-bind="formItemLayout">
            <a-form-item v-if="showBusinessKey" :label-col="labelCol" :wrapper-col="wrapperCol" label="业务主键key:">
              <a-input v-model="businessKey" placeholder="请输入业务主键Key"></a-input>
            </a-form-item>
          </a-col>
          <a-col v-bind="formItemLayout">
            <a-form-item :label-col="labelCol" :wrapper-col="wrapperCol" label="抄送给" class="form-item">
              <j-select-user-by-dep-enhance
                  ref="JSelectUserByDepEnhance"
                  v-model="userIds"
                  :ccToVos.sync="ccToVos"
                  :multi="true"
                  :userIds.sync="userIds"
                ></j-select-user-by-dep-enhance>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row v-if="buttons.includes('users')||buttons.includes('noUsers')" style="padding-top:15px;">
          <a-form-item :label-col="labelCol" :wrapper-col="wrapperCol"  :required="buttons.includes('users')" label="指定办理人" class="form-item">
            <j-select-user-by-dep-enhance
              ref="assignNextNodeHandle"
              v-model="assignNextNode"
              :ccToVos.sync="nextCcToVos"
              :multi="true"
              :url="url"
              :userIds.sync="assignNextNode"
            ></j-select-user-by-dep-enhance>
          </a-form-item>
        </a-row>
      </a-form>
    </div>
    </a-spin>
    <template slot="footer">
      <a-button key="submit" type="primary" :loading="submitLoading" @click="doStartInstance"> 发起流程</a-button>
      <a-button key="draft" @click="addDdraft" v-if='showDdraft'> 暂存草稿</a-button>
      <a-button key="back" @click="handleCancel"> 关闭</a-button>
    </template>
     <assets-update-list ref="assetsUpdateModal" @setAssetsChange="setAssetsChange"></assets-update-list>
  </a-modal>
</template>

<script>
import JSelectUserByDepEnhance from '@/components/flowable/JSelectUserByDepEnhance'
import { getAction, postAction } from '@/api/manage'
import store from '@/store/'
import { httpAction } from '../../../../api/manage'
import fa from 'element-ui/src/locale/lang/fa'
export default {
  name: 'ProcessInstanceStart',
  components: {
    JSelectUserByDepEnhance,
    AssetsUpdateList:()=>import('@/views/cmdb/assets/assetsUpdate/AssetsUpdateList'),
  },
  props: {
    dialogStartInstanceVisible: {
      type: Boolean,
      default: function() {
        return false
      }
    },
    showDdraft: {
      type: Boolean,
      default: function() {
        return true
      }
    },
    processDefinition: {
      type: Object,
      default: function() {
        return {}
      }
    },
    formUrl: {
      type: String,
      default: function() {
        return '/flowable/processDefinition/renderedStartForm'
      }
    },
    startUrl: {
      type: String,
      default: function() {
        return '/business/actZBusiness/start'
      }
    },
    associationId: {
      type: String,
      default: function() {
        return ''
      }
    },
    method: {
      type: String,
      default: function() {
        return 'get'
      }
    },
    alarmHistory: {
      type: Object,
      default: function() {
        return {}
      }
    },
    dictKey: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      url:"/flowable/model/queryUsersByInitiator?processDefinitionId="+this.processDefinition.id,
      formItemLayout: {
        md: { span: 24 },
        sm: { span: 24 },
      },
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
        md: { span: 5 },
        lg: { span: 5 },
        xl: { span: 5 },
        xxl: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 18 },
        md: { span: 16 },
        lg: { span: 16 },
        xl: { span: 16 },
        xxl: { span: 16 },
      },
      dynamicKeys: [],
      moduleVisible: this.dialogStartInstanceVisible,
      generateStartFormVisible: false,
      showBusinessKey: false,
      businessKey: undefined,
      ccToVos: [],
      submitLoading: false,
      selectUserVisible: false,
      userIds: '',
      // 选择用户查询条件配置
      selectUserQueryConfig: [{ key: 'phone', label: '电话' }],
      dynamicData: {
        bpmnDepartTreeData: [],
        treeData:[],
      },
      buttons: [],
      assignNextNode: '',
      nextCcToVos: [],
      excludes: ['bpmnDepartTreeData'],
      formDefault: {},
      startFormJson:{},
      defaultUser:{
        name: '用户',
        width: 1250,
        displayKey: 'realname',
        returnKeys: ['id', 'username'],
        queryParamText: '账号',
        queryParamCode: 'username'
      },
      tempProcessInstanceId: null, // 临时流程id
      scrollHeight: null
    }
  },
  created() {
    this.initData()
    this.getTreeData()
  },
   mounted() {
    window.onresize = () => {
      this.getHeight();
    }
  },
  methods: {
    // 计算表单可滚动区域高度
    getHeight() {
      this.$nextTick(() => {
        const modalBody = window.innerHeight - 53 - 55 - 30 - 30; // .ant-modal-body弹框的高度
        this.fixedHeight = this.$refs.bottomFixed.offsetHeight; // 计算下半部分固定高度(意见、抄送、办理人)
        this.scrollHeight = modalBody - this.fixedHeight - 48; // 计算上半部分可滚动表单高度
      })
    },
    //打开资产编辑
    assetsHandler(e){
      this.$refs.assetsUpdateModal.init(e)
    },
    //监听到资产变化
    setAssetsChange(data, tempId) {
       console.log(data, tempId,'变更的资产')
      this.tempProcessInstanceId = tempId
      this.$refs.generateStartForm.setData({
        assetsChangeList:data,
      })
    },
    initData() {
      this.confirmLoading=true
      if (this.method==='get'){
        getAction(this.formUrl,{
          processDefinitionId: this.processDefinition.id,
          associationId: this.associationId,
          dictKey:this.dictKey
        } ).then((res) => {
          if (res.success) {
            this.handleFrom(res)
            this.confirmLoading=false
          }else{
            this.$message.error(res.message)
          }

        }).catch(()=>{
          this.confirmLoading=false
        })
      }else {
        this.alarmHistory.dictKey=this.dictKey
        httpAction(this.formUrl, this.alarmHistory,"post").then((res) => {
          if (res.success) {
            this.handleFrom(res)
            this.confirmLoading=false
          }else{
            this.$message.error(res.message)
          }
        }).catch((err)=>{
          this.confirmLoading=false
        })
      }
    },
    handleFrom(res){
      const data = res.result
      this.buttons = data.buttons ? data.buttons : []

      this.showBusinessKey = data.showBusinessKey
      if (data.renderedStartForm) {
        this.startFormJson = JSON.parse(data.renderedStartForm)
        // 获取表单中的动态数据

        var realname = store.getters.userInfo.realname
        let departId = ''
        if (store.getters.departs && store.getters.departs.length > 0) {
          departId = store.getters.departs[0].id
        }

        this.formDefault = {
          username:realname,
          bpmnUserName: realname,
          bpmnUserDepart: departId,
          userdepart: departId,
        }
        // console.log(this.formDefault)
        if (data.variables) {
          Object.assign(this.formDefault, data.variables);
        }
        // console.log(this.formDefault)
        this.generateStartFormVisible = true
      }

      // 计算底部固定元素高度
      this.getHeight();
    },
    getTreeData() {
      getAction('/sys/sysDepart/queryTreeList').then((res) => {
        this.dynamicData.bpmnDepartTreeData = res.result
        this.dynamicData.treeData = res.result
      })
    },
    handleSubmit() {},
    doSelectCcTo: function () {
      this.selectUserVisible = true
      //this.$refs.selectUser.loadData()
      //this.$refs.selectUser.title = '流程实例信息'
      //this.$refs.selectUser.disableSubmit = false
      //this.selectUserVisible = true
      // if (this.$refs.selectUser.treeData.length == 0) {
      //   this.$refs.selectUser.getTreeData()
      // }
    },
    addDdraft() {
      if (this.buttons.includes('users')) {
        if (!this.assignNextNode) {
          this.$message.error('未指定办理人')
          return
        }
      }
      let userArr = []
      if (this.ccToVos) {
        this.ccToVos.forEach(v => {
          userArr.push(v.username)
        })
      }
      if (this.$refs.generateStartForm) {
        this.$refs.generateStartForm.getFieldsValue().then((values) => {
          if (values && values != undefined) {
            Object.assign(values, { tempProcessInstanceId: this.tempProcessInstanceId,assignNextNode:this.assignNextNode,multiUser: userArr })
            let processInstanceFormData = JSON.stringify(values)
            //let formData = Object.assign(processInstanceFormData || {}, values)
            let formData = {}
            formData.procDefId = this.processDefinition.id
            formData.formValue = JSON.stringify(values)
            let formkey = this.processDefinition.formKey
            formData.formKey = formkey
            formData.procVersion = this.processDefinition.version
            formData.category = this.processDefinition.category
            postAction('/business/actZBusiness/add', formData).then((res) => {
              if (res.success) {
                this.$message.success(res.message)
              } else {
                this.$message.error('保存失败')
              }
              this.handleCancel()
            })
          }
        }).catch(error=>{
          console.log("获取表单数据报错",error)
        })
      } else {
        this.$message.error('保存失败，该流程为配置流程表单！')
      }
    },
    doStartInstance() {
      // this.$refs.generateStartForm.getFieldsValue().then(res=>{
      //   console.log("自定义表单值 ==== ",res)
      // })
      let userArr = []
      if (this.ccToVos) {
        this.ccToVos.forEach(v => {
          userArr.push(v.username)
        })
      }
      let formkey = this.processDefinition.formKey
      if (this.buttons.includes('users')) {
        if (!this.assignNextNode) {
          this.$message.error('未指定办理人')
          return
        }
      }
      if (this.$refs.generateStartForm) {
        this.$refs.generateStartForm
          .getData()
          .then((values) => {
            console.log(values)
            if (values && values != undefined) {
              if(values.changeManageClass && values.changeManageClass.includes("资产变更")){
                if(!values.assetsChangeList  || values.assetsChangeList.length == 0){
                  this.$message.error("未添加变更资产")
                  return
                }
              }

              Object.assign(values, { tempProcessInstanceId: this.tempProcessInstanceId })
              let processInstanceFormData = JSON.stringify(values)
              let realValues = Object.assign({ processInstanceFormData }, values)
              this.submitLoading = true
              postAction(this.startUrl, {
                processDefinitionId: this.processDefinition.id,
                associationId: this.associationId,
                businessKey: this.businessKey,
                values: realValues,
                ccUserIds: userArr,
                formKey: formkey,
                assignNextNode: this.assignNextNode,
                dictKey:this.dictKey
              }).then((res) => {
                if (res.code == 200) {
                  this.$message.success('发起成功')
                  this.handleCancel()
                  this.$emit("loadData",1)
                } else {
                  this.$message.error(res.message)
                }
                this.submitLoading = false
                //this.dialogStartProcessVisibleInChild = false
              })
            }
          })
          .catch((e) => {})
      } else {
        postAction('/business/actZBusiness/start', {
          processDefinitionId: this.processDefinition.id,
          businessKey: this.businessKey,
          ccUserIds: userArr,
          formKey: formkey,
          assignNextNode: this.assignNextNode,
        }).then((res) => {
          if (res.code == 200) {
            this.$message.success('发起成功')
            this.handleCancel()
          } else {
            this.$message.error(res.message)
          }

          //this.dialogStartProcessVisibleInChild = false
        })
      }
      // let that = this
      // processInstance.start({processDefinitionId: this.processDefinition.id,}).then((res) => {
      //     if (res.success) {
      //       that.$message.success(res.message)
      //       that.$emit('ok')
      //     } else {
      //       that.$message.warning(res.message)
      //     }
      //   })
      //   .finally(() => {
      //     this.moduleVisible = false
      //     this.$emit('update:dialogStartInstanceVisible', false)
      //   })
    },
    handleCancel() {
      this.moduleVisible = false
      this.$emit('update:dialogStartInstanceVisible', false)
    },
    close() {
      this.selectUserVisible = false
    },
    // 遍历解析Json
    getKeyValueFromJson(jsonObj, dynamicKey) {
      // 循环所有键
      for (let key in jsonObj) {
        //如果对象类型为object类型且数组长度大于0 或者 是对象 ，继续递归解析
        let element = jsonObj[key]
        if (
          (element !== null && element != undefined && element.length > 0 && typeof element == 'object') ||
          typeof element == 'object'
        ) {
          this.getKeyValueFromJson(element, dynamicKey)
        } else {
          //不是对象或数组、直接输出
          if (key === dynamicKey && element !== '') {
            this.dynamicKeys.push(element)
          }
        }
      }
    },
  },
}
</script>
<style lang='less' scoped>
@import '~@assets/less/YQNormalModal.less';
.form-item {
  margin-bottom: 0;
}
.form-wrapper {
  overflow:auto;
  margin-bottom: 10px;
  padding-bottom: 20px;
  padding-right: 24px;
}
/deep/ .ant-modal {
  padding-bottom: 0;
}
::-webkit-scrollbar {
  width: 6px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
}

</style>