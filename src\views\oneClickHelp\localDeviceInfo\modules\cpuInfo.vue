<template>
  <card-frame title='cpu'>
    <div slot='bodySlot' class='bodySlot' v-if='Object.keys(info).length >0&&info.infoArr.length>0&&info.trendXData.length>0&&info.trendYData.length>0'>
      <div class='content-item' v-for='(item ,index) in info.infoArr' :key='"content_"+index'>
        {{item.name}}：<span class='value'>{{item.value}}</span>
      </div>
      <div class='rate'>{{rate}}</div>
      <div id='chart_cpu' class='chart'></div>
    </div>
    <div slot='bodySlot' class='body-empty' v-else>
      <a-spin :spinning='loading' v-if='loading'  class='spin'></a-spin>
      <a-list :data-source='[]' :locale='locale' v-else />
    </div>
  </card-frame>
</template>
<script>
import cardFrame from '@views/oneClickHelp/localDeviceInfo/modules/CardFrame.vue'
import Empty from '@/components/oneClickHelp/Empty.vue'
import echarts from 'echarts/lib/echarts'

import 'echarts/lib/component/graphic'
export default {
  name: "cpuInfo",
  props: {
    info: {
      type: Object,
      required: true
    },
    loading: {
      type: Boolean,
      required: false,
      default:false
    }
  },
  components:{cardFrame, Empty},
  data() {
    return {
      rate:'',
      locale: {
        emptyText: <Empty/>
      }
    }
  },
  watch:{
    info:{
      handler(nValue){
        this.rate=''
        if(Object.keys(nValue).length>0&&nValue.trendXData.length>0&&nValue.trendYData.length>0){
          this.$nextTick(() => {
            this.trend(nValue)
          })
        }
      },
      deep:true,
      immediate:true
    }
  },
  mounted() {},
  methods: {
    trend(data) {
      let myChart = this.$echarts.init(document.getElementById('chart_cpu'))
      let option= {
        tooltip: {
          trigger: 'axis',
          padding: [8,8],
          backgroundColor: 'rgba(25,41,56,0.75)',
          borderColor: '#192938',
          width: 55,
          position: 'top',
          axisPointer: {
            type: 'line',
            lineStyle: {
              type: 'dotted',
              width: 0.5,
              color: 'rgba(255,255,255,0.78)',
              cap: 'none',
            },
          },
          textStyle: {
            color: 'rgba(255,255,255,0.78)',
            fontSize: 15,
          },
          formatter:(params)=>{
            this.rate=params[0].data+"%"
            let dotHtml = '<span style="display:inline-block;margin-right:5px;border-radius:100%;width:10px;height:10px;background-color:#0b68f3"></span>'
            return params[0].axisValue+`<br/>`+dotHtml+params[0].seriesName+":"+params[0].data+"%"
          }
        },
        grid: {
          top: '20px',
          left: '2%',
          right: '2%',
          bottom: '1%',
          // containLabel: true,
        },
        xAxis: [{
          type: 'category',
          boundaryGap: false,//两端不留白
          data: data.trendXData,
          axisLine: {
            show: true,
            lineStyle: {
              width: 1,
              color: "rgba(151,151,151,0.2)",
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            show: false
          },
        }],
        yAxis: [{
          type: 'value',
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
            lineStyle: {
              width: 1,
              color: "rgba(151,151,151,0.2)",
            },
          },
          axisLabel: {
            show: false,
          },
          splitLine: {
            show: false,
          },
        }],
        series: [{
          name: 'cpu使用率',
          type: 'line',
          smooth: true,
          symbol: 'circle',
          symbolSize: 1,
          showSymbol: false,
          itemStyle: {
            normal: {
              color: '#0b68f3'//symbol颜色
            }
          },
          lineStyle: {
            width: 1,
            color: '#287FFF'
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: '#438AFF',
              },
              {
                offset: 0.99,
                color: 'rgba(112,206,255,0.00)',
              },
            ]),
          },
          emphasis: {
            focus: 'series',
          },
          data: data.trendYData
        }]
      }
      myChart.setOption(option)
      window.addEventListener("resize", () => {
        myChart.resize();
      });
    },
  }
}
</script>

<style scoped lang="less">
.bodySlot{
  height:100%;
  padding: 0.5rem 0.65rem 0.4rem;//40px 52px 32px/80
  position: relative;

  .content-item{
    font-size: 0.2rem;
    margin-bottom:0.125rem;// 10/80
    display: inline-block;
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    color: rgba(255,255,255,0.65);

    .value{
      color: #FFFFFF;
    }
  }

  .rate{
    position: absolute;
    content: "";
    left:50%;
    top:1.025rem;//82px
    font-size: 0.5rem;
    text-align: center;
    color: #29B0FF;
    font-weight: 700;
    font-family: DIN-Medium
  }

  .chart{
    position: absolute;
    content: "";
    top:1.7rem;//136px
    left:0.65rem;//52px;
    right:0.65rem;//52px;
    bottom: 0.4rem;//32px
    height:calc(100% - 1.7rem - 0.4rem);// calc(100% - 136px - 32px);
    width:calc(100% - 0.65rem - 0.65rem)//calc(100% - 52px - 52px);
  }
}

</style>