<template>
  <a-form-item label="时间格式" :labelCol="labelCol" :wrapperCol="wrapperCol">
    <!-- <a-input-number id="inputNumber" v-model="value" :min="1" style="width:100%" @change="onChange" placeholder="默认格式：String类型的UTC时间戳（毫秒）"/> -->
    <a-select :getPopupContainer="(node) => node.parentNode" allowClear show-search v-model="formatValue"
      placeholder="默认格式：String类型的UTC时间戳（毫秒）" option-filter-prop="children" :filter-option="filterOption"
      @focus="handleFocus" @blur="handleBlur" @change="handleChange">
      <a-select-option value="string">
        String类型的UTC时间戳（毫秒）
      </a-select-option>
      <a-select-option value="time">
        yy-MM-dd
      </a-select-option>
      <a-select-option value="timeS">
        yy-MM-dd HH:mm:ss
      </a-select-option>
      <a-select-option value="timeE">
        yy-MM-dd HH:mm:ss EE
      </a-select-option>
      <a-select-option value="timeZ">
        yy-MM-dd HH:mm:ss zzz
      </a-select-option>
    </a-select>
  </a-form-item>
</template>

<script>
  export default {
    name: 'dateMetadata',
    props: ['typeFormatValue'],
    data() {
      return {
        formatValue: [],
        labelCol: {
          xs: {
            span: 5
          },
          sm: {
            span: 5
          }
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          }
        },
        value: '',
      }
    },
    mounted() {
      if (undefined != this.typeFormatValue && '' != this.typeFormatValue) {
        var obj = JSON.parse(this.typeFormatValue);
        this.formatValue = obj;
      }
    },
    methods: {
      handleChange(value) {
        this.value = value;
        this.$emit('changElemType', JSON.stringify(this.value));
      },
      handleBlur() {},
      handleFocus() {},
      filterOption(input, option) {
        return (
          option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
        );
      },
    },
  }
</script>

<style>
</style>