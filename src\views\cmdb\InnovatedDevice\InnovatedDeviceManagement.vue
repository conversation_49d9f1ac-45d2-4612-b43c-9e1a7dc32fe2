<template>
  <div style="height:100%;">
    <keep-alive exclude='InnovatedDeviceInfo'>
      <component :is="pageName" :data="data"/>
    </keep-alive >
  </div>
</template>
<script>
import innovatedDeviceList from './InnovatedDeviceList'
import innovatedDeviceInfo from './InnovatedDeviceInfo'
export default {
  name: "InnovatedDeviceManagement",
  data() {
    return {
      isActive: 0,
      data:{},
    };
  },
  components: {
    innovatedDeviceList,
    innovatedDeviceInfo
  },
  created(){
    this.pButton1(0);
  },
  //使用计算属性
  computed: {
    pageName() {
      switch (this.isActive) {
        case 0:
          return "innovatedDeviceList";
          break;
        default:
          return "innovatedDeviceInfo";
          break;
      }
    }
  },
  methods: {
    pButton1(index) {
      this.isActive = index;
    },
    pButton2(index,item) {
      this.isActive = index;
      this.data = item;
    }
  }
}
</script>