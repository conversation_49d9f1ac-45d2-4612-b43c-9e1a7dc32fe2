<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll zxw">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <!-- 查询区域 -->
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class="card-style">
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="标题">
                  <a-input
                    :maxLength='maxLength'
                    placeholder="请输入"
                    autocomplete="off"
                    :allowClear="true"
                    v-model="queryParam.titile"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="发布人">
                  <a-input
                    :maxLength='maxLength'
                    placeholder="请输入"
                    autocomplete="off"
                    :allowClear="true"
                    v-model="queryParam.sender"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="消息类型">
                  <a-select
                    :allowClear="true"
                    v-model="queryParam.msgCategory"
                    placeholder="请选择消息类型"
                  >
                    <a-select-option :value="'2'" :key="2">系统消息</a-select-option>
                    <a-select-option :value="'1'" :key="1">通知公告</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="colBtnsSpan()">
                <span
                  class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                >
                  <a-button type="primary" @click="searchQuery" class="btn-search-style">查询</a-button>
                  <a-button @click="searchReset" style="margin-left: 8px" class="btn-reset-style">重置</a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered="false" style="flex: auto" class="core">
        <div class="table-operator tableBottom">
          <a-button @click="readAll">全部标注已读</a-button>
        </div>
        <a-table
          ref="table"
          size="default"
          bordered
          rowKey="id"
          :columns="columns"
          :dataSource="dataSource"
          :pagination="ipagination"
          :loading="loading"
          @change="handleTableChange"
        >
          <span
            slot="action"
            slot-scope="text, record"
            class="caozuo"
            style="display: inline-block; white-space: nowrap; text-align: center"
          >
            <a @click="showAnnouncement(record)">查看</a>
          </span>
          <template slot="tooltip" slot-scope="text">
            <a-tooltip placement="topLeft" :title="text" trigger="hover">
              <div class="tooltip">
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
        <show-announcement ref="ShowAnnouncement"></show-announcement>
        <dynamic-notice ref="showDynamNotice" :path="openPath" :formData="formData" />
      </a-card>
    </a-col>
  </a-row>
</template>

<script>
import { filterObj } from '@/utils/util'
import { getAction, putAction } from '@/api/manage'
import ShowAnnouncement from '@/components/tools/ShowAnnouncement'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import DynamicNotice from '../../components/tools/DynamicNotice'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
import { setImgAllPath } from '@/utils/imagePathAboutTinymce'

export default {
  name: 'UserAnnouncementList',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components: {
    DynamicNotice,
    ShowAnnouncement,
  },
  data() {
    return {
      maxLength:50,
      formItemLayout: {
        labelCol: {
          style: 'width:70px',
        },
        wrapperCol: {
          style: 'width:calc(100% - 70px)'
        }
      },
      description: '系统通告表管理页面',
      queryParam: {
        msgCategory: undefined,
        sender: '',
        title: ''
      },
      columns: [
        {
          title: '标题',
          dataIndex: 'titile',
        },
        {
          title: '消息类型',
          dataIndex: 'msgCategory',
          customRender: function (text) {
            if (text == '1') {
              return '通知公告'
            } else if (text == '2') {
              return '系统消息'
            } else {
              return text
            }
          },
        },
        {
          title: '发布人',
          dataIndex: 'sender_dictText',
        },
        {
          title: '发布时间',
          dataIndex: 'sendTime',
        },
        {
          title: '优先级',
          dataIndex: 'priority',
          customRender: function (text) {
            if (text == 'L') {
              return '低'
            } else if (text == 'M') {
              return '中'
            } else if (text == 'H') {
              return '高'
            } else {
              return text
            }
          },
        },
        {
          title: '阅读状态',
          dataIndex: 'readFlag',
          customRender: function (text) {
            if (text == '0') {
              return '未读'
            } else if (text == '1') {
              return '已读'
            } else {
              return text
            }
          },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 130,
          fixed: 'right',
          scopedSlots: { customRender: 'action' },
        },
      ],
      url: {
        list: '/sys/sysAnnouncementSend/getMyAnnouncementSend',
        editCementSend: 'sys/sysAnnouncementSend/editByAnntIdAndUserId',
        readAllMsg: 'sys/sysAnnouncementSend/readAll',
      },
      loading: false,
      openPath: '',
      formData: '',
      disableMixinCreated: true
    }
  },
  watch:{
    '$route.params.msgCategory':{
      handler(val){
        this.queryParam.msgCategory =val?JSON.stringify(val):undefined;
        this.loadData()
      },
      deep:true,
      immediate:true
    }
  },
  methods: {
    loadData(arg) {
      if (!this.url.list) {
        this.$message.error('请设置url.list属性!')
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1
      }

      var params = this.getQueryParams() //查询条件
      this.loading = true
      getAction(this.url.list, params).then((res) => {
        if (res.success) {
          //update-begin---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
          this.dataSource = res.result.records || res.result

          if (this.dataSource.length>0){
            for (let i=0;i<this.dataSource.length;i++){
              let content=this.dataSource[i].msgContent
              if (content&&content.length>0){
                this.dataSource[i].msgContent=setImgAllPath(content)
              }
            }
          }
          if (this.dataSource.length < 9) {
            this.clientHeight = false
          }
          //author:weng    Date:20210402  for：if(res.result.total>0) 有错误，无查询结果时，页码显示有问题
          this.ipagination.total =res.result.total?res.result.total:0
          //update-end---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
        }
        if (res.code === 510) {
          this.$message.warning(res.message)
        }
        this.loading = false
      })
    },
    handleDetail: function (record) {
      this.$refs.sysAnnouncementModal.detail(record)
      this.$refs.sysAnnouncementModal.title = '查看'
    },
    showAnnouncement(record) {
      putAction(this.url.editCementSend, { anntId: record.anntId }).then((res) => {
        if (res.success) {
          this.loadData()
          // this.syncHeadNotic(record.anntId)
        }
      })
      if (record.openType === 'component') {
        this.openPath = record.openPage
        this.formData = { id: record.busId }
        this.$refs.showDynamNotice.detail()
      } else {
        this.$refs.ShowAnnouncement.detail(record)
      }
    },
    syncHeadNotic(anntId) {
      getAction('sys/annountCement/syncNotic', { anntId: anntId })
    },
    readAll() {
      var that = this
      that.$confirm({
        title: '确认操作',
        okText: '是',
        cancelText: '否',
        content: '是否全部标注已读?',
        onOk: function () {
          putAction(that.url.readAllMsg).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.loadData()
              // that.syncHeadNotic()
            }
          })
        },
      })
    },
  },
}
</script>
<style lang="less" scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
.ant-card-body .table-operator {
  margin-bottom: 18px;
}
.anty-row-operator button {
  margin: 0 5px;
}
.ant-btn-danger {
  background-color: #ffffff;
}
z .ant-modal-cust-warp {
  height: 100%;
}
.ant-modal-cust-warp .ant-modal-body {
  height: calc(100% - 110px) !important;
  overflow-y: auto;
}
.ant-modal-cust-warp .ant-modal-content {
  height: 90% !important;
  overflow-y: hidden;
}
/*表头样式*/
::v-deep .ant-table-thead > tr > th {
  text-align: center;
  white-space: nowrap;
}

/*内容对齐方式、省略显示*/
::v-deep .ant-table-tbody > tr > td {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;

  &:first-child,
  &:nth-child(2),
  &:nth-child(3),
  &:nth-child(4),
  &:nth-child(5),
  &:nth-child(6) {
    text-align: center;
  }
}
</style>