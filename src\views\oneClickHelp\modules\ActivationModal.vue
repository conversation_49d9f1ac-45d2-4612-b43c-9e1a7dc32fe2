<template>
  <yq-modal
    :title="title"
    :width="width"
    :visible="visible"
    :destroyOnClose="true"
    :centered="true"
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }"
    :cancelText="cancelText"
    :okText="okText"
    @ok="handleOk"
    @cancel="handleCancel"
  >

    <a-spin :spinning="confirmLoading">
      <j-form-container :disabled="disableSubmit">
        <a-form-model
          ref="form"
          slot="detail"
          :model="model"
          :rules="validatorRules"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
        >
          <a-row>
            <a-col :span="24">
              <a-form-model-item label="使用人" prop="realname">
                <a-input v-model="model.realname" placeholder="请输入使用人" :allow-clear="true" disabled/>
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item :label="'所属单位'" prop="deptId">
<!--                <a-tree-select
                  :getPopupContainer="(node) => node.parentNode"
                  tree-node-filter-prop="title"
                  v-model="model.deptId"
                  :replaceFields="replaceFields"
                  :treeData="departList"
                  show-search
                  style="width: 100%"
                  :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                  placeholder="请选择您所在的单位"
                  allow-clear
                  @change="onChangeTree"
                  @select="onSelect"
                >
                </a-tree-select>-->
                <a-select placeholder="请输选择cpu类型" v-model="model.deptId" :allowClear="true"
                          :getPopupContainer="(node) => node.parentNode"
                          :dropdownClassName='"custom-select-dropdown"'>
                  <a-select-option v-for="item in departList" :key="item.id" :value="item.id">
                    {{ item.departName }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item label="联系电话" prop="phone">
                <a-input placeholder="请输入您的联系电话" v-model="model.phone" disabled :allow-clear="true" />
              </a-form-model-item>
            </a-col>
<!--            <a-col :span="24">-->
<!--              <a-form-model-item label="使用部门" prop="userDepartment">-->
<!--                <a-input placeholder="请输入使用部门" v-model="model.userDepartment" :allow-clear="true" disabled/>-->
<!--              </a-form-model-item>-->
<!--            </a-col>-->
            <!-- <a-col :span='24'>
              <a-form-model-item label="行政区划" prop="addrId" >
                <yq-area-cascader-select
                  disabled
                  placeholder="请选择行政区划"
                  v-model="model.addrId"
                ></yq-area-cascader-select>
              </a-form-model-item>
            </a-col> -->
            <!--            <a-col :span="24">
                          <a-form-model-item label="详细地址" prop="addrDetail">
                            <a-input placeholder="请输入具体详细地址" v-model="model.addrDetail" :allow-clear="true" />
                          </a-form-model-item>
                        </a-col>-->
            <a-col :span="24" v-if='cpuname && terminalReal'>
              <a-form-model-item label="cpu类型" prop="cpuType">
                <a-input disabled placeholder="请输入cpu类型" v-model="model.cpuType" :allow-clear="true" />
              </a-form-model-item>
            </a-col>
            <a-col :span="24" v-else>
              <a-form-model-item label="cpu类型" prop="cpuType">
                <a-select placeholder="请输选择cpu类型" v-model="model.cpuType" :allowClear="true"
                          :getPopupContainer="(node) => node.parentNode"
                          :dropdownClassName='"custom-select-dropdown"'>
                  <a-select-option v-for="item in cpuTypeList" :key="item.value" :value="item.value">
                    {{ item.text || item.label }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="24" v-if='osname && terminalReal'>
              <a-form-model-item label="操作系统" prop="osType">
                <a-input disabled placeholder="请输入操作系统" v-model="model.osType" :allow-clear="true" />
              </a-form-model-item>
            </a-col>
            <a-col :span="24" v-else>
              <a-form-model-item label="操作系统" prop="osType">
                <a-select placeholder="请输选择操作系统" v-model="model.osType" :allowClear="true"
                          :getPopupContainer="(node) => node.parentNode"
                          :dropdownClassName='"custom-select-dropdown"'>
                  <a-select-option v-for="item in osTypeList" :key="item.value" :value="item.value">
                    {{ item.text }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item :label="'描\u3000述'" prop="description">
                <a-textarea
                  v-model="model.description"
                  :autoSize="{ minRows: 2, maxRows: 4 }"
                  :allow-clear="true"
                  autocomplete="off"
                  placeholder="请输入描述"
                />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </j-form-container>
    </a-spin>
  </yq-modal>
</template>

<script>
import { ajaxGetDictItems } from '@/api/api'
import YqAreaCascaderSelect from '@comp/areaDict/YqAreaCascaderSelect.vue'
import { phoneValidator } from '@/mixins/phoneValidator'
import { putAction, postAction, getAction } from '@/api/manage'
import { mapGetters } from 'vuex'
export default {
  name: 'ActivationModal',
  mixins: [phoneValidator],
  components: {
    YqAreaCascaderSelect,
  },
  props: {
    isLogin: {
      type: Boolean,
      default: false,
    },
    hostName: {
      type: String || null,
      default: '',
    },
  },
  data() {
    return {
      title: '',
      width: '800px',
      visible: false,
      disableSubmit: false,
      confirmLoading: false,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 24 },
        md: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 24 },
        md: { span: 16 },
      },
      model: {},
      departList: [],
      cpuTypeList: [],
      osTypeList: [],
      replaceFields: {
        children: 'children',
        title: 'deptName',
        key: 'deptId',
        value: 'deptId',
      },
      validatorRules: {
        deptId: [{ required: true, message: '请选择单位！' }],
        realname: [
          { required: true, message: '请输入使用人！' }
        ],
        phone: [{ required: true, message: '请输入联系电话！' }, { validator: this.phone }],
        // userDepartment: [{ required: true, message: '请输入使用部门！' }],
        // addrId: [
        //   { required: true, message: '请选择地区！' }
        // ],
        // addrDetail: [{ required: true, message: '请输入具体详细地址！' }],
        cpuType: [{ required: true, message: '请输选择cpu类型！' }],
        osType: [{ required: true, message: '请输选择操作系统！' }],
        description: [{ required: false, max: 200, message: '描述长度应在 200 个字符之内！' }],
      },
      okText: '确定',
      cancelText: '关闭',
      isEdit: false,
      flatDeparts: [],
      terminalInfo:{},
      terminalReal:null,
      cpuname:"",
      osname:"",
    }
  },
  created() {

    this.initDictData('cpuType', 'cpuTypeList')
    this.initDictData('os_type', 'osTypeList')
  },
  methods: {
    ...mapGetters(['departs']),
    //终端类型自动获取接口
    compareTerminalInfo(){
      let params = Object.assign({},this.model)
      //终端类型信息的源数据
      this.cpuname = this.terminalInfo.cpuname;
      this.osname = this.terminalInfo.osname;
      params.cpuname = this.cpuname;
      params.osname =  this.osname;
      params.uniqueCode = this.hostName
      params.uniqueCode = this.hostName
      putAction("/terminal/terminalDevice/translateCpuTypeAndOsType",params)
        .then(res=>{
          if(res.success && res.result){
            let result = res.result;
            //后台终端信息真是信息数据
            this.terminalReal = {
              cpuType : result.cpuType.newValue,
              osType : result.osType.newValue
            };
            let cpuValue = result.cpuType.newValue
            let osValue = result.osType.newValue
            if(cpuValue !== "其他"){
              let cpuInfo = this.cpuTypeList.find(el=>el.value === cpuValue)
              cpuValue = cpuInfo?cpuInfo.text:"其他（"+this.terminalInfo.cpuname+"）";
            }else{
              cpuValue = "其他（"+this.terminalInfo.cpuname+"）";
            }
            if(osValue !== "其他"){
              let osInfo = this.osTypeList.find(el=>el.value === osValue)
              osValue = osInfo?osInfo.text:"其他（"+this.terminalInfo.osname+"）";
            }else{
              osValue ="其他（"+this.terminalInfo.osname+"）";
            }
            this.$set(this.model,"cpuType",cpuValue)
            this.$set(this.model,"osType",osValue)
          }
        })
    },
    add() {
      this.visible = true
      let userInfo = this.$store.getters.userInfo
      let departs = this.$store.getters.departs
      let depart = {
        id:"",
        departName:""
      }
      this.getDepartList()
      if (departs && departs.length > 0) {
        depart = departs[0]
      }
      this.model = {
        deptId:depart.id,
        username: userInfo.username,
        realname:userInfo.realname,
        // userDepartment:depart.departName,
        phone:userInfo.phone,
        addrId: depart.cityId,
        description: '',
      }
      let terminalInfo =  sessionStorage.getItem("ONE_CLICK_HELP_TERMINAL");
      if(terminalInfo){
        this.terminalInfo = JSON.parse(terminalInfo)
        this.compareTerminalInfo();
      }
    },
    edit() {
      this.getDepartList()
      this.isEdit = true
      this.okText = '保存'
      this.cancelText = '解除绑定'
      getAction('/terminal/terminalDevice/getTerminalInfo', { uniqueCode: this.hostName }).then((res) => {
        if (res.success && res.result && res.result.records && res.result.records.length > 0) {
          this.model = res.result.records[0]
          this.model['realname'] = this.model.usernameText
          let terminalInfo = sessionStorage.getItem("ONE_CLICK_HELP_TERMINAL");
          if (terminalInfo) {
            this.terminalInfo = JSON.parse(terminalInfo)
            this.compareTerminalInfo();
          }
          this.visible = true
        } else {
          this.$message.warning('没获取到终端注册信息')
        }
      }).catch((err) => {
        this.$message.error(err.message)
      })
    },
    close() {
      this.visible = false
    },
    handleOk(e) {
      e.preventDefault()
      this.handleSubmit()
    },
    handleCancel(e) {
      if (e.target.innerText === '解除绑定') {
        getAction('/terminal/terminalDevice/unBindTerminal', { uniqueCode: this.hostName })
          .then((res) => {
            if (res.success) {
              this.$message.success('解除成功！')
              this.$store.dispatch('Logout').then(() => {
                this.$router.push({ path: '/oneClickHelp/login'})
              })
            } else {
              this.$message.success('解除失败！')
            }
          })
          .catch((err) => {
            this.$message.success('解除失败！')
          })
      }
      this.close()

      if (this.isLogin) {
        this.$store.dispatch('Logout').then(() => {
          location.reload()
        })
      }
    },
    initDictData(dictCode, list) {
      //根据字典Code, 初始化字典数组
      ajaxGetDictItems(dictCode, null).then((res) => {
        if (res.success) {
          this[list] = res.result
        }
      })
    },
    onChangeTree() {

    },
    getDepartList() {
      this.departList = this.departs()
      console.log("获取的部门 === >", this.departList)
      // getAction('/sys/sysDepart/queryAllTree').then((res) => {
      //   this.departList = res && res.length > 0 ? res : []
      //   this.getFlatDeparts(this.departList)
      // })
    },
    getFlatDeparts(list) {
      list.forEach((el) => {
        let tem = { id: el.deptId, name: el.deptName }
        this.flatDeparts.push(tem)
        if (el.children && el.children.length > 0) {
          this.getFlatDeparts(el.children)
        }
      })
    },
    //   提交按钮
    handleSubmit() {
      if(this.confirmLoading) return;
      this.$refs.form.validate((valid, values) => {
        if (valid) {
          let data = Object.assign({}, this.model)
          data.uniqueCode = this.hostName
          if(this.terminalReal){
            data.cpuType = this.terminalReal.cpuType
            data.osType = this.terminalReal.osType
          }
          data.cpuname = this.cpuname;
          data.osname = this.osname;
          this.confirmLoading = true;
          let clientType = sessionStorage.getItem('ONE_CLICK_CLIENTTYPE');
          if(clientType!==null && clientType!==undefined && clientType!==""){
            data.terminalType =  clientType;
          }
          else if(!this.isEdit){
            data.terminalType = 6;
          }
          let path = ''
          if (this.isEdit) {
            data.address = undefined;
            path = '/terminal/terminalDevice/updateTerminalInfo'
          } else {
            path = 'terminal/terminalDevice/updateStatus'
          }

          putAction(path, data)
            .then((res) => {
              if (res.code == 200) {
                this.$message.success(res.message)
                if(this.isEdit){
                  this.$store.commit("ONE_CLICK_HELP_CHANGE",true)
                }
                this.$emit('ok')
                this.close()
              } else {
                this.$message.error(res.message)
              }
              this.confirmLoading = false
            }).catch(err=>{
            this.confirmLoading = false
          })
        } else {
          console.log('表单有错 === ', valid)
        }
      })
    },
    onSelect() {
      getAction('/sys/sysDepart/queryById', {
        id: arguments[0]
      }).then((res) => {
        if (res.success) {
          this.model.addrId = res.result.cityId
          this.model.deptAddress = res.result.address
        }
      })
    },
  },
}
</script>
<style scoped lang="less">
@import "~@assets/less/onclickStyle.less";
@import "~@assets/less/normalModal.less";
</style>