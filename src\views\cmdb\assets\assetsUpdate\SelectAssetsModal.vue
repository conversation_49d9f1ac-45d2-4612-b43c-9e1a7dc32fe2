<template>
  <j-modal :title="title"
    :width="800"
    :visible='visible'
    :maskClosable='false'
    @ok='handleOk'
    @cancel="handleCancel"
    cancelText="关闭">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline"
        @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :md='10'
            :sm='12'
            :xs='24'>
            <a-form-item label="资产名称">
              <a-input placeholder="请输入资产名称"
                v-model="queryParam.assetsName"></a-input>
            </a-form-item>
          </a-col>
          <a-col :md='8'
            :sm='12'
            :xs='24'>
            <span style="float: left; overflow: hidden"
              class="table-page-search-submitButtons">
              <a-button type="primary"
                @click="searchQuery"
                icon="search">查询</a-button>
              <a-button type="primary"
                @click="searchReset"
                icon="reload"
                style="margin-left: 8px">重置</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <!-- table区域-begin -->
    <div>
      <a-table size="small"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource1"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange, type: 'radio' }"
        @change="handleTableChange">
      </a-table>
    </div>
    <!-- table区域-end -->
    <!-- 新增/编辑资产弹框 -->
    <assets-modal ref="modalForm"
    :tempId="tempId"
    :assetsOperateType="assetsOperateType"
    @addAssetsChangeOk="AddAssetsChange"></assets-modal>
  </j-modal>
</template>

<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import {
  postAction,getAction
} from '@/api/manage'

export default {
  name: 'SelectAssetsModal',
  mixins: [JeecgListMixin],
  components:{
    AssetsModal:()=>import('@/views/cmdb/assets/modules/AssetsModal')
  },
  props:{
    // 资产变更--临时变更id
    tempId: {
      type: String,
      default: '',
    }
  },
  watch: {
    visible(val){
      if(val){
        this.loadData()
      }
    }
  },
  data() {
    return {
      title: '添加已有资产',
      visible: false,
      columns: [
        {
          title: '资产编号',
          dataIndex: 'assetsCode',
          customCell: () => {
            let cellStyle = 'text-align: center'
            return {
              style: cellStyle,
            }
          },
        },
        {
          title: '资产名称',
          dataIndex: 'assetsName',
          customCell: () => {
            let cellStyle = 'text-align: center'
            return {
              style: cellStyle,
            }
          },
        },
        {
          title: '资产类型',
          dataIndex: 'assetsCategoryText',
          customCell: () => {
            let cellStyle = 'text-align: center'
            return {
              style: cellStyle,
            }
          },
        },
        {
          title: '供应商',
          dataIndex: 'producerName',
          customCell: () => {
            let cellStyle = 'text-align: center'
            return {
              style: cellStyle,
            }
          },
        },
        {
          title: '型号',
          dataIndex: 'assetsModel',
          customCell: () => {
            let cellStyle = 'text-align: center'
            return {
              style: cellStyle,
            }
          },
        },
        {
          title: '资产状态',
          dataIndex: 'statusName',
          customCell: () => {
            let cellStyle = 'text-align: center'
            return {
              style: cellStyle,
            }
          },
        },
        {
          title: '入库日期',
          dataIndex: 'storageTime',
          customRender: function (text) {
            return !text ? '' : text.length > 10 ? text.substr(0, 10) : text
          },
          customCell: () => {
            let cellStyle = 'text-align: center'
            return {
              style: cellStyle,
            }
          },
        }
      ],
      dataSource1: [],
      url: {
        list: '/device/deviceInfoAct/stockAssetsList',
      },
      disableMixinCreated: true,
      assetsOperateType: "editHasAssets" // 编辑已有资产
    }
  },
  created() {

  },
  methods: {
    handleOk() {
      if (this.selectionRows.length == 1) {
        this.$refs.modalForm.edit(this.selectionRows[0]);
        this.$refs.modalForm.title = '编辑';
        this.$refs.modalForm.disableSubmit = false;
      } else {
        this.$message.warning('请选择一条记录！')
      }
    },
    AddAssetsChange(data) {
      this.visible = false
      this.$emit('selectFinished', data);
    },
    loadData(arg) {
      if (!this.url.list) {
        this.$message.error('请设置url.list属性!')
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1
      }
      var params = this.getQueryParams() //查询条件
      params.tempProcessInstanceId=this.tempId
      this.loading = true
      getAction(this.url.list, params).then((res) => {
        if (res.success) {
          this.dataSource1 = res.result.records || res.result

          if (this.dataSource.length < 9) {
            this.clientHeight = false
          }
          this.ipagination.total = res.result.total
        }
        if (res.code === 510) {
          this.$message.warning(res.message)
        }
        this.loading = false
      })
    },
    close() {
      this.visible = false
    },
    handleCancel() {
      this.close()
          },
  },
}
</script>

<style scoped lang='less'>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
</style>