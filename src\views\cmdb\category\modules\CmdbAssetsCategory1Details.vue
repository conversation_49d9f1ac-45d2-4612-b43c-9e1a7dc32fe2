<template>
  <a-card class='lookTop'>
    <a-row>
      <a-col :span="24">
        <!--        <div style="display: flex; justify-content: space-between; margin-bottom: 16px">-->
        <div class='lookEditionWrapper'>
          <div class='lookEdition' @click="handleEdit(data)" v-has="'assetsCategory:edit'">
            <a-icon type="edit" class='editIcon' />编辑
          </div>
          <div style="display: inline-block;float: right; text-align: right; margin-bottom: 12px">
            <img src="~@/assets/return1.png" alt="" @click="getGo" class='returnImage' />
          </div>
        </div>
      </a-col>
      <a-col :span="24">
        <table class="gridtable">
          <tr>
            <td class="leftTd">类型名称</td>
            <td class="rightTd">{{ data.categoryName }}</td>
            <td class="leftTd">类型编号</td>
            <td class="rightTd">{{ data.categoryCode }}</td>
          </tr>
          <tr>
            <td class="leftTd">描述</td>
            <td class="rightTd">{{ data.categoryDescribe }}</td>
            <td class="leftTd">序号</td>
            <td class="rightTd">{{ data.categorySerial }}</td>
          </tr>
          <tr>
            <td class="leftTd">状态</td>
            <td class="rightTd">
              <span v-if="data.categoryState == '1'">未启用</span>
              <span v-else>启用</span>
            </td>
            <td class="leftTd">是否包含子级</td>
            <td class="rightTd">
              <span v-if="data.hasChild == '1'">是</span>
              <span v-else>否</span>
            </td>
          </tr>
          <tr>
            <td class="leftTd">父级节点</td>
            <td class="rightTd">{{ data.parentName }}</td>
            <td class="leftTd">类型属性</td>
            <td class="rightTd">
              <span v-if="data.isMonitorable == '0'">不可监控</span>
              <span v-else>可监控</span>
            </td>
          </tr>
        </table>
      </a-col>
    </a-row>
    <cmdb-assets-category1-modal ref="modalForm" @ok="query"></cmdb-assets-category1-modal>
  </a-card>
</template>
<script>
  import {
    httpAction,
    getAction
  } from '@/api/manage'
  import pick from 'lodash.pick'
  import {
    validateDuplicateValue
  } from '@/utils/util'
  import JFormContainer from '@/components/jeecg/JFormContainer'
  import JDictSelectTag from '@/components/dict/JDictSelectTag'
  import CmdbAssetsCategory1Modal from './CmdbAssetsCategory1Modal'

  export default {
    name: 'CmdbAssetsCategory1Details',
    components: {
      JFormContainer,
      JDictSelectTag,
      CmdbAssetsCategory1Modal,
    },
    props: {
      //流程表单data
      formData: {
        type: Object,
        default: () => {},
        required: false,
      },
      //表单模式：true流程表单 false普通表单
      formBpm: {
        type: Boolean,
        default: false,
        required: false,
      },
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false,
      },
      data: {
        type: Object,
      }
    },
    data() {
      return {
        form: this.$form.createForm(this),
        model: {},
        isDis: false,
        categoryState: '0',
        hasChild: '1',
        oldParentId: '',
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          },
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          },
        },
        confirmLoading: false,

        validatorRules: {
          assetsCode: {
            rules: [{
              required: true,
              message: '请输入资产编号!'
            }],
          },
          assetsName: {
            rules: [{
              required: true,
              message: '请输入资产名称!'
            }],
          },
          parentId: {
            rules: [{
              required: true,
              message: '请选择父节点!'
            }],
          },
        },
        url: {
          add: '/category/cmdbAssetsCategory1/add',
          edit: '/category/cmdbAssetsCategory1/edit',
          queryById: '/category/cmdbAssetsCategory1/queryById',
        },
        tSelects: '',
      }
    },
    mounted() {
      // setTimeout(() => {
      //   let tSelect = document.getElementById('tSelect');
      //   this.tSelects = tSelect.textContent;
      //   tSelect.style.display='none';
      // }, 100);
    },
    watch: {},
    computed: {
      formDisabled() {
        if (this.formBpm === true) {
          if (this.formData.disabled === false) {
            return false
          }
          return true
        }
        return this.disabled
      },
      showFlowSubmitButton() {
        if (this.formBpm === true) {
          if (this.formData.disabled === false) {
            return true
          }
        }
        return false
      },
    },
    created() {
      //如果是流程中表单，则需要加载流程表单data
      this.showFlowData()
    },
    methods: {
      //返回上一级
      getGo() {
        this.$parent.pButton1(0)
      },
      //渲染流程表单数据
      showFlowData() {
        if (this.formBpm === true) {
          let params = {
            id: this.formData.dataId
          }
          getAction(this.url.queryById, params).then((res) => {
            if (res.success) {
              this.edit(res.result)
            }
          })
        }
      },

      handleEdit: function (record) {
        this.$refs.modalForm.edit(record)
        this.$refs.modalForm.title = '编辑'
        this.$refs.modalForm.disableSubmit = false
      },

      query() {
        getAction(this.url.queryById, {
          id: this.data.id
        }).then((res) => {
          if (res.code == 200) {
            this.data = res.result
          }
        })
      },
    },
  }
</script>
<style scoped>
  @import '~@assets/less/lookPage.less';
</style>