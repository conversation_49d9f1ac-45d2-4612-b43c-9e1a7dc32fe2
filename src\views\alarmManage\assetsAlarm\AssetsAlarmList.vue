<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <!-- 查询区域 -->
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class="card-style">
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="设备名称">
                  <a-input :maxLength='maxLength' placeholder="请输入设备名称" v-model="queryParam.deviceName" autocomplete="off" :allowClear="true">
                  </a-input>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="产品名称">
                  <a-select placeholder="请选择产品名称" v-model="queryParam.productId"
                            :getPopupContainer="(node) => node.parentNode" :allowClear="true" :show-search='true'
                            option-filter-prop='label'>
                    <a-select-option v-for="item in productList" :label='item.proName' :key="item.proId"
                                     :value="item.proId">{{ item.proName }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="告警级别">
                  <a-select v-model='queryParam.alarmLevel' :allow-clear='true' placeholder='请选择告警级别'>
                    <a-select-option v-for='item in alarmLevelList' :key='item.value' :label='item.title'
                                     :value='item.value'>
                      {{ item.title }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue" v-show="toggleSearchStatus">
                <a-form-item label="告警状态">
                  <j-dict-select-tag v-model="queryParam.alarmStatus" placeholder="请选择告警状态" dictCode="alarm_status" />
                </a-form-item>
              </a-col>
              <a-col :span="spanValue" v-show="toggleSearchStatus">
                <a-form-item label="触发时间">
                  <a-range-picker class="a-range-picker-choice-date" @change="onChangeTime"
                                  v-model="queryParam.alarmTime2Range" format="YYYY-MM-DD" :placeholder="['开始时间', '截止时间']" />
                </a-form-item>
              </a-col>
              <a-col :span="spanValue" v-show="toggleSearchStatus">
                <a-form-item label="处理状态">
                  <a-select v-model='queryParam.handleStatus' :allow-clear='true' placeholder='请选择处理状态'>
                    <a-select-option v-for='item in handleStatusList' :key='item.value' :label='item.label'
                                     :value='item.value'>
                      {{ item.label }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue"  v-show="toggleSearchStatus">
                <a-form-item label="分组名称">
                  <a-select placeholder="请选择分组名称" v-model="queryParam.groupId"
                            :getPopupContainer="(node) => node.parentNode" :allowClear="true" :show-search='true'
                            option-filter-prop='label'>
                    <a-select-option v-for="item in groupList" :label='item.name' :key="item.id"
                                     :value="item.id">{{ item.name }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="colBtnsSpan()">
                <span class="table-page-search-submitButtons"
                      :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button class="btn-search btn-search-style" type="primary" @click="searchQuery">查询</a-button>
                  <a-button class="btn-reset btn-reset-style" @click="searchReset">重置</a-button>
                  <a v-if="isVisible" class="btn-updown-style" @click="doToggleSearch">
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered="false" style="width: 100%; flex: auto">
        <!-- 操作按钮区域 -->
        <div class="table-operator table-operator-style">
          <!--          <a-button class="btn-confirm" @click="batchConfirm()">确认</a-button>-->
          <a-dropdown v-if="selectedRowKeys.length > 0">
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item key="1" @click="batchChangeResponsibleUser('指定责任人')">
                处理
              </a-menu-item>
              <a-menu-item key="2"  @click="batchCloseAlarm">关闭 </a-menu-item>
            </a-menu>
            <a-button>批量操作
              <a-icon type="down" />
            </a-button>
          </a-dropdown>
        </div>
        <!-- table区域-begin -->
        <a-table
          ref="table"
          bordered
          :rowKey="(record) => { return record.id}"
          :columns="columns"
          :dataSource="dataSource" :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
          :pagination="ipagination"
          :loading="loading"
          :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange,  getCheckboxProps: getCheckboxProps}"
          @change="handleTableChange">
          <template slot="alarmStatus" slot-scope="text,record">
            <span>{{getAlarmStatus(record)}}</span>
          </template>
          <template slot="deviceName" slot-scope="text,record">
            <span style='color: #409eff;cursor: pointer' @click='handleViewDeviceInfo(record)'>{{text}}</span>
          </template>
          <template slot="alarmTemplateName" slot-scope="text,record">
            <span style='color: #409eff;cursor: pointer' @click='handleViewAlarmTemplateInfo(record)'>{{text}}</span>
          </template>
          <template slot="alarmLevel" slot-scope="text,record">
            <div :style='{backgroundColor:getAlarmColor(record)}'
                 style='display:inline-block;color:#ffffff; border-radius: 10px; padding: 2px 10px;'>
              {{ getAlarmTitle(record) }}
            </div>
          </template>
          <!--          <template slot="confirmStatus" slot-scope="text">
                      <span v-if="text == 1">已确认</span>
                      <span v-else>未确认</span>
                    </template>-->

          <template slot="handleStatus" slot-scope="text">
            <span >{{getHandlingStatus(text)}}</span>
          </template>

          <span slot="action" slot-scope="text, record" class="caozuo">
            <a @click="handleDetailPage(record)">查看</a>
            <a-divider type="vertical" />
            <a-dropdown :disabled='record.handleStatus!=handleStatusList[0].value&&record.flowHandleStatus!=handleStatusList[1].value' :class='{"dropdown-disabled":record.handleStatus!=handleStatusList[0].value&&record.flowHandleStatus!=handleStatusList[1].value}'>
                <a class="ant-dropdown-link">更多 <a-icon type="down"/></a>
                <a-menu slot="overlay" style="text-align: center">
                  <a-menu-item v-if='record.handleStatus==handleStatusList[0].value'>
                    <a href="javascript:void(0);" @click='changeResponsibleUser(record,"指定责任人")'>处理</a>
                  </a-menu-item>
                  <a-menu-item v-if='setUpgradeStatus(record)&&record.handleStatus==handleStatusList[0].value'>
                    <a href="javascript:void(0);" @click='changeAlarmLevel(record,"upgrade")'>升级</a>
                  </a-menu-item>
                   <a-menu-item v-if='setDegradationStatus(record)&&record.handleStatus==handleStatusList[0].value'>
                    <a href="javascript:void(0);" @click='changeAlarmLevel(record,"downgrade")'>降级</a>
                  </a-menu-item>
                  <a-menu-item v-if='record.handleStatus==handleStatusList[0].value'>
                    <a href="javascript:void(0);" @click='closeAlarm(record)'>关闭</a>
                  </a-menu-item>
                  <a-menu-item v-if='record.handleStatus==handleStatusList[0].value'>
                    <a href="javascript:void(0);" @click='confirmDelete(record.id)'>删除</a>
                  </a-menu-item>
                    <a-menu-item v-if='record.flowHandleStatus==handleStatusList[1].value'>
                    <a href="javascript:void(0);" @click="history(record,'查看进度')">查看进度</a>
                  </a-menu-item>
                </a-menu>
              </a-dropdown>
          </span>

          <template slot="tooltip" slot-scope="text">
            <a-tooltip placement="topLeft" :title="text" trigger="hover">
              <div class="tooltip">
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
      </a-card>
    </a-col>
    <!--指定责任人-->
    <responsible-person ref='modalForm' @ok='completeAssignment'></responsible-person>
    <!--查看进度-->
    <process-history-modal ref="processHistoryModal" @ok="modalFormOk"></process-history-modal>
  </a-row>
</template>

<script>
import {JeecgListMixin} from '@/mixins/JeecgListMixin'
import {getAction } from '@/api/manage'
import {YqFormSearchLocation} from '@/mixins/YqFormSearchLocation'
import responsiblePerson from '@views/alarmManage/modules/AssignResponsiblePerson.vue'
import {dataAndFunction} from '@views/alarmManage/modules/dataAndFunction'
import processHistoryModal from '@views/flowable/myProcess/modules/ProcessHistoryModal.vue'
export default {
  name: 'AssetsAlarmList',
  mixins: [JeecgListMixin, YqFormSearchLocation,dataAndFunction],
  components: {
    processHistoryModal,
    responsiblePerson},
  data() {
    return {
      maxLength:50,
      description: '告警信息管理人员页面',
      formItemLayout: {
        labelCol: {
          style: 'width:80px',
        },
        wrapperCol: {
          style: 'width:calc(100% - 80px)'
        }
      },
      //表头
      columns: [
        {
          title: '设备名称',
          dataIndex: 'deviceName',
          scopedSlots: {
            customRender: 'deviceName'
          },
        },
        {
          title: '告警名称',
          dataIndex: 'templateName',
          scopedSlots: {
            customRender: 'alarmTemplateName'
          },
        },
        {
          title: '产品名称',
          dataIndex: 'productName'
        },
        {
          title: '告警级别',
          dataIndex: 'alarmLevel',
          scopedSlots: {
            customRender: 'alarmLevel'
          }
        },
        {
          title: '告警状态',
          dataIndex: 'alarmStatus',
          scopedSlots: {
            customRender: 'alarmStatus'
          }
        },
        {
          title: '触发时间',
          dataIndex: 'alarmTime1'
        },
        {
          title: '重复次数',
          dataIndex: 'repeatTimes',
          customCell: () => {
            let cellStyle = 'text-align: right'
            return {
              style: cellStyle
            }
          },
        },
        {
          title: '处理状态',
          dataIndex: 'handleStatus',
          scopedSlots: {
            customRender: 'handleStatus'
          }
        },
        {
          title: '责任人',
          dataIndex: 'responsibleUser_dictText',
        },
        {
          title: '分组名称',
          dataIndex: 'groupName',
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 120,
          scopedSlots: {
            customRender: 'action'
          },
        },
      ],
      url: {
        list: '/alarm/alarmHistory/list',
        delete:'/alarm/alarmHistory/delete'
      },
      disableMixinCreated:true,
    }
  },
  created() {
    this.queryAllProducts()
    this.getAlarmLevelData()
    this.getGroupList()
    this.getDict('alarm_status','alarmStatusList')
  },
  activated() {
    this.loadData()
  },
  methods: {
    /**未处理可选，处理中，已关闭，处理完成的不可选*/
    getCheckboxProps (record) {
      return ({
        props: {
          disabled: this.getTableChecked(record.handleStatus),
          // topicName: record.topicName
        }
      })
    },
    /**完成指派*/
    completeAssignment(){
      this.reCalculatePage(this.selectedRowKeys.length)
      this.resetData()
    },
    /**重置加载列表*/
    resetData(){
      this.loadData()
      this.onClearSelected()
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
.caozuo .dropdown-disabled {
  color: rgba(0, 0, 0, 0.25) !important;
  cursor: default;
}
.confirm {
  color: rgba(0, 0, 0, 0.25) !important;
}
</style>