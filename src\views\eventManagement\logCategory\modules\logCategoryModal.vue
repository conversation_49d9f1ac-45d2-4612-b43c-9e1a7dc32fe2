<template>
  <j-modal
    :title="title"
    :width="modelWidth"
    :visible="visible"
    :centered="true"
    :confirmLoading="confirmLoading"
    switchFullscreen
    :destroyOnClose="true"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item label="类型名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            v-decorator="['categoryName', validatorRules.categoryName]"
            placeholder="请输入类型名称"
            :allowClear="true"
            autocomplete="off"
          />
        </a-form-item>
        <a-form-item label="类型编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            v-decorator="['categoryCode', validatorRules.categoryCode]"
            placeholder="请输入类型编号"
            :allowClear="true"
            autocomplete="off"
          />
        </a-form-item>
        <a-form-item class="two-words" label="描述" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            v-decorator="['categoryDescribe', validatorRules.categoryDescribe]"
            placeholder="请输入描述"
            :allowClear="true"
            autocomplete="off"
          />
        </a-form-item>
        <a-form-item class="two-words" label="序号" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input-number v-decorator="['categorySerial', validatorRules.categorySerial]" placeholder="请输入序号" />
        </a-form-item>
        <a-form-item label="父级节点" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-tree-select
            v-decorator="['parentId', validatorRules.parentId]"
            :load-data="onLoadData"
            :tree-data="treeData"
            :replaceFields="replaceFields"
            :dropdownMatchSelectWidth="true"
            :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
            allow-clear
            placeholder="请选择父级节点"
          />
        </a-form-item>
      </a-form>
    </a-spin>
  </j-modal>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import pick from 'lodash.pick'
import { validateDuplicateValue } from '@/utils/util'

export default {
  name: 'logCategoryModal',
  components: {},
  data() {
    return {
      assetsCategoryName: '',
      assetsCategoryIds: '',
      form: this.$form.createForm(this),
      title: '操作',
      modelWidth: '800px',
      visible: false,
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },

      confirmLoading: false,
      validatorRules: {
        categoryName: {
          rules: [
            { required: true, message: '请输入类型名称' },
            { min: 2, max: 20, message: '类型名称长度应在2到20个字符！', trigger: 'blur' },
          ],
        },
        categoryCode: {
          rules: [
            { required: true, message: '请输入类型编号' },
            { min: 0, max: 20, message: '类型编号长度应在0-20之间！  ' },
          ],
        },
        categoryDescribe: {
          rules: [
            { required: true, message: '请输入描述' },
            { min: 2, max: 50, message: '描述长度应在2-50之间！  ' },
          ],
        },
        categorySerial: {
          rules: [
            { required: true, message: '请输入序号' },
            // { max: 20, message: '序号长度应在0-20之间！  ' },
            { pattern: /^[1-9]{1}[0-9]*$/, message: '请输入大于0的整数' },
          ],
        },
      },
      url: {
        add: '/log/logCategory/add',
        edit: '/log/logCategory/edit',
        getChildrenList: '/log/logCategory/list', //点击节点加载子级节点
        getLogCategoryTree: '/log/logCategory/getLogCategoryTree', //获取分类树
      },
      expandedRowKeys: [],
      pidField: 'parentId',
      hasChild: 'has_child',
      replaceFields: {
        children: 'children',
        title: 'name',
        key: 'id',
        value: 'id',
      },
      treeData: [],
    }
  },
  created() {
   
  },
  mounted() {},
  methods: {
     // 加载树数据
    loadTreeData() {
      getAction(this.url.getLogCategoryTree).then(res => {
        if (res && res.result && Array.isArray(res.result)) {
          this.treeData = this.formatTreeData(res.result)
        }
      })
    },
    
        // 格式化树数据
    formatTreeData(data) {
      return data.map(item => {
        const node = {
          ...item,
          isLeaf: item.hasChild === '0',
          scopedSlots: { title: 'title' }
        }
        
        // 如果存在子节点，递归格式化
        if (item.children && item.children.length > 0) {
          node.children = this.formatTreeData(item.children)
        }
        
        return node
      })
    },
    
    // 异步加载子节点（仅当节点未加载过子节点时才调用）
    onLoadData(treeNode) {
      return new Promise(resolve => {
        // 如果已经有子节点数据或标记为叶子节点，直接返回
        if (treeNode.dataRef.children || treeNode.dataRef.isLeaf) {
          resolve()
          return
        }
        
        // 对于初始数据中没有的子节点，调用API加载
        getAction(this.url.getChildrenList, {
          pid: treeNode.dataRef.id
        }).then(res => {
          if (res.success && res.result) {
            const children = this.formatTreeData(res.result)
            this.$set(treeNode.dataRef, 'children', children)
            this.$set(treeNode.dataRef, 'isLeaf', children.length === 0)
            this.treeData = [...this.treeData]
          }
        }).finally(() => resolve())
      })
    },
    changeSelect(e) {},
    add(obj) {
      this.edit(obj)
    },
    edit(record) {
      this.loadTreeData()
      this.form.resetFields()
      this.model = Object.assign({}, record)
      if (this.model.parentId === '0') {
        this.model.parentId = ''
      }
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(
          pick(this.model, 'categoryName', 'categoryCode', 'categoryDescribe', 'categorySerial', 'parentId')
        )
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = false
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let formData = Object.assign(this.model, values)
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                this.$emit('ok')
                that.close()
              } else {
                that.$message.warning(res.message)
              }
              that.confirmLoading = false
            })
            .catch((err) => {
              that.$message.warning(err.message)
              that.confirmLoading = false
            })
            .finally(() => {
              this.$forceUpdate()
            })
        }
      })
    },
    handleCancel() {
      this.close()
    },
  },
}
</script>
<style lang="less" scoped>
@import '~@assets/less/normalModal.less';
</style>
