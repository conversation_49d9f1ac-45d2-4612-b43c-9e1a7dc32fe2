<template>
  <div>
    <a-row type="flex" justify="space-between">
      <a-col :xl="8" :md="11" :xs="24" style="margin-left: 20px;margin-bottom: 50px;" class="infoClass">
        <div v-if="record && record.hostInfo && record.hostInfo.name != null">
          <div>名称：</div>
          <p>{{record.hostInfo.name}}</p>
        </div>
        <div v-if="record && record.vmInfo && record.vmInfo.name != null">
          <div>名称：</div>
          <p>{{record.vmInfo.name}}</p>
        </div>
        <div v-if="record.clusterNum && record.clusterNum != 0">
          <div>集群数量：</div>
          <p>{{record.clusterNum}}个</p>
        </div>
        <div v-if="record.datacenterNum && record.datacenterNum != 0">
          <div>数据中心数量：</div>
          <p>{{record.datacenterNum}}个</p>
        </div>
        <div v-if="record.hostNum && record.hostNum != 0">
          <div>主机数量：</div>
          <p>{{record.hostNum}}个</p>
        </div>
        <div v-if="record.vmNum && record.vmNum != 0">
          <div>虚拟机数量：</div>
          <p>{{record.vmNum}}个</p>
        </div>
        <div v-if="record && record.hostInfo && record.hostInfo.cpuModel != null">
          <div>处理器型号：</div>
          <p>{{record.hostInfo.cpuModel}}</p>
        </div>
        <div v-if="record && record.vmInfo && record.vmInfo.cpuModel != null">
          <div>处理器型号：</div>
          <p>{{record.vmInfo.cpuModel}}</p>
        </div>
        <div v-if="record && record.hostInfo && record.hostInfo.cpuCores != null">
          <div>CPU核数：</div>
          <p>{{record.hostInfo.cpuCores}}</p>
        </div>
        <div v-if="record && record.vmInfo && record.vmInfo.cpuCores != null">
          <div>CPU核数：</div>
          <p>{{record.vmInfo.cpuCores}}</p>
        </div>
        <div v-if="record && record.hostInfo && record.hostInfo.osType != null">
          <div>操作系统类型：</div>
          <p>{{record.hostInfo.osType}}</p>
        </div>
        <div v-if="record && record.vmInfo && record.vmInfo.osType != null">
          <div>操作系统类型：</div>
          <p>{{record.vmInfo.osType}}</p>
        </div>
        <div v-if="record && record.hostInfo && record.hostInfo.vendor != null">
          <div>制造商：</div>
          <p>{{record.hostInfo.vendor}}</p>
        </div>
        <div v-if="record && record.vmInfo && record.vmInfo.vendor != null">
          <div>制造商：</div>
          <p>{{record.vmInfo.vendor}}</p>
        </div>
        <div v-if="record && record.hostInfo && record.hostInfo.model != null">
          <div>型号：</div>
          <p>{{record.hostInfo.model}}</p>
        </div>
        <div v-if="record && record.vmInfo && record.vmInfo.model != null">
          <div>型号：</div>
          <p>{{record.vmInfo.model}}</p>
        </div>
        <div v-if="record && record.hostInfo && record.hostInfo.ipAddress != null">
          <div>ip地址：</div>
          <p>{{record.hostInfo.ipAddress}}</p>
        </div>
        <div v-if="record && record.vmInfo && record.vmInfo.ipAddress != null">
          <div>ip地址：</div>
          <p>{{record.vmInfo.ipAddress}}</p>
        </div>
        <div v-if="record && record.hostInfo && record.hostInfo.ipAddress != null">
          <div>连接状态：</div>
          <p>{{record.hostInfo.powerState == 'POWERED_ON' ? '已连接' : '未连接'}}</p>
        </div>
        <div v-if="record && record.vmInfo && record.vmInfo.powerState != null">
          <div>连接状态：</div>
          <p>{{record.vmInfo.powerState == 'POWERED_ON' ? '已连接' : '未连接'}}</p>
        </div>
      </a-col>
    </a-row>
    <a-row>
      <div class="colorBox">
        <span class="colorTotal">性能计算</span>
      </div>
      <a-col :offset="1" :xl="10" :xs="24" style="height: 270px;">
        <div style="display: flex;flex-direction: column; align-items: center;">
          <p>CPU使用率</p>
          <a-progress type="circle" :percent="percentCPU" :width="180" :strokeWidth="10" />
        </div>
        <div class="progressClass" v-if="record && record.hostInfo && record.hostInfo.cpuTotal != null">
          <div>
            <p>可用：{{(record.hostInfo.cpuFree/1000).toFixed(2)}} GHz</p>
            <p>已用：{{(record.hostInfo.cpuUse/1000).toFixed(2)}} GHz</p>
          </div>
          <p>总容量：{{(record.hostInfo.cpuTotal/1000).toFixed(2)}} GHz</p>
        </div>
        <div class="progressClass" v-else-if="record && record.vmInfo && record.vmInfo.cpuTotal != null">
          <div>
            <p>可用：{{(record.vmInfo.cpuFree/1000).toFixed(2)}} GHz</p>
            <p>已用：{{(record.vmInfo.cpuUse/1000).toFixed(2)}} GHz</p>
          </div>
          <p>总容量：{{(record.vmInfo.cpuTotal/1000).toFixed(2)}} GHz</p>
        </div>
        <div class="progressClass" v-else>
          <div>
            <p>可用：{{(record.cpuFree/1000).toFixed(2)}} GHz</p>
            <p>已用：{{(record.cpuUse/1000).toFixed(2)}} GHz</p>
          </div>
          <p>总容量：{{(record.cpuTotal/1000).toFixed(2)}} GHz</p>
        </div>
        <div class="progressClass" v-if="record && record.vmInfo && record.vmInfo.cpuTotal != null">
          <div>
            <p>可用：{{(record.vmInfo.cpuFree/1000).toFixed(2)}} GHz</p>
            <p>已用：{{(record.vmInfo.cpuUse/1000).toFixed(2)}} GHz</p>
          </div>
          <p>总容量：{{(record.vmInfo.cpuTotal/1000).toFixed(2)}} GHz</p>
        </div>
        <div class="progressClass" v-if="!record.hostInfo && !record.vmInfo">
          <div>
            <p>可用：{{(record.cpuFree/1000).toFixed(2)}} GHz</p>
            <p>已用：{{(record.cpuUse/1000).toFixed(2)}} GHz</p>
          </div>
          <p>总容量：{{(record.cpuTotal/1000).toFixed(2)}} GHz</p>
        </div>
      </a-col>
      <a-col :offset="2" :xl="10" style="height: 270px;">
        <div style="display: flex;flex-direction: column; align-items: center;">
          <p>内存使用率</p>
          <a-progress type="circle" :percent="percentMeta" :width="180" :strokeWidth="10" />
        </div>
        <div class="progressClass" v-if="record && record.hostInfo && record.hostInfo.memTotal != null">
          <div>
            <p>可用：{{(record.hostInfo.memFree/1024/1024/1024).toFixed(2)}} GB</p>
            <p>已用：{{(record.hostInfo.memUse/1024).toFixed(2)}} GB</p>
          </div>
          <p>总容量：{{(record.hostInfo.memTotal/1024/1024/1024).toFixed(2)}} GB</p>
        </div>
        <div class="progressClass" v-else-if="record && record.vmInfo && record.vmInfo.memTotal != null">
          <div>
            <p>可用：{{(record.vmInfo.memFree/1024).toFixed(2)}} GB</p>
            <p>已用：{{(record.vmInfo.memUse/1024).toFixed(2)}} GB</p>
          </div>
          <p>总容量：{{(record.vmInfo.memTotal/1024).toFixed(2)}} GB</p>
        </div>
        <div class="progressClass" v-else>
          <div>
            <p>可用：{{(record.memFree/1024/1024/1024).toFixed(2)}} GB</p>
            <p>已用：{{(record.memUse/1024).toFixed(2)}} GB</p>
          </div>
          <p>总容量：{{(record.memTotal/1024/1024/1024).toFixed(2)}} GB</p>
        </div>
        <div class="progressClass" v-if="record && record.vmInfo && record.vmInfo.memTotal != null">
          <div>
            <p>可用：{{(record.vmInfo.memFree/1024/1024/1024).toFixed(2)}} GB</p>
            <p>已用：{{(record.vmInfo.memUse/1024).toFixed(2)}} GB</p>
          </div>
          <p>总容量：{{(record.vmInfo.memTotal/1024/1024/1024).toFixed(2)}} GB</p>
        </div>
        <div class="progressClass" v-if="!record.vmInfo && !record.hostInfo">
          <div>
            <p>可用：{{(record.memFree/1024/1024/1024).toFixed(2)}} GB</p>
            <p>已用：{{(record.memUse/1024).toFixed(2)}} GB</p>
          </div>
          <p>总容量：{{(record.memTotal/1024/1024/1024).toFixed(2)}} GB</p>
        </div>
      </a-col>
    </a-row>
  </div>
</template>

<script>
  import chartRing from '@/components/bigScreen/chartRing'
  export default {
    name: 'Abstract',
    components: {
      chartRing,
    },
    data() {
      return {
        activeKey: ['1'],
        relationship: ['1'],
        record: {},
        percentCPU: 0,
        percentMeta: 0,
        url: {
          progressData: 'device/deviceInfo/vcenterDetail',
        },
      }
    },
    watch: {
      activeKey(key) {},
      relationship(key) {},
    },
    methods: {
      show(record) {
        this.record = record
        if (this.record.cpuRate != null) {
          this.percentCPU = this.record.cpuRate
        } else if (this.record.hostInfo != null) {
          this.percentCPU = this.record.hostInfo.cpuUsage != null ? this.record.hostInfo.cpuUsage : 0
        } else {
          this.percentCPU = this.record.vmInfo != null && this.record.vmInfo.cpuUsage != null ? this.record.vmInfo
            .cpuUsage : 0
        }
        if (this.record.memRate != null) {
          this.percentMeta = this.record.memRate
        } else if (this.record.hostInfo != null) {
          this.percentMeta = this.record.hostInfo.memUsage != null ? this.record.hostInfo.memUsage : 0
        } else {
          this.percentMeta = this.record.vmInfo.memUsage != null ? this.record.vmInfo.memUsage : 0
        }
      },
    }
  }
</script>

<style>
  .infoClass>div {
    display: flex;
  }

  .infoClass>div>div {
    width: 100px;
    color: rgba(0, 0, 0, 0.85);
  }

  .progressClass {
    margin-left: 15px;
    margin-top: 10px;
    font-size: 10px;
    color: #999999;
    display: flex;
    justify-content: space-around;
  }

  .infoClass>div>p {
    color: rgba(0, 0, 0, 0.65);
  }

  .core-core-body {
    height: 80%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    .core-core-body-circularGraph {
      width: 100%;
      height: 100%;
    }
  }

  .colorBox {
    margin-bottom: 18px;
  }

  .colorTotal {
    padding-left: 7px;
    border-left: 4px solid #1e3674;
  }
</style>