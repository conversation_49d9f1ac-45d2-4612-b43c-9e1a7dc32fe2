<template>
  <a-row :gutter="10" style="height: 100%;" class="vScroll">
    <a-col style="width:100%;height: 100%;display: flex;flex-direction: column">
      <!-- 查询区域 -->
      <a-card
        :bordered="false"
        :bodyStyle="{ paddingBottom: '0', marginRight: '12px' }"
        class="card-style"
        style="width: 100%"
      >
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="分组名称">
                  <a-input
                    :maxLength="maxLength"
                    placeholder="请输入分组名称"
                    v-model="queryParam.groupName"
                    :allowClear="true"
                    autocomplete="off"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="运维人员">
                  <a-select
                    :getPopupContainer="node=>node.parentNode"
                    :allow-clear="true"
                    v-model="queryParam.userIds"
                    placeholder="请选择运维人员"
                  >
                    <a-select-option
                      v-for="(item, key) in userList"
                      :key="key"
                      :value="item.username"
                    >
                      <div style="display: inline-block; width: 100%" :title="item.realname">
                        {{ item.realname }}
                        <span
                          style="font-size: 12px; color: rgba(0, 0, 0, 0.45);"
                        >
                          {{
                          '(' + item.username + ')'
                          }}
                        </span>
                      </div>
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="colBtnsSpan()">
                <span
                  class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                >
                  <a-button
                    type="primary"
                    class="btn-search btn-search-style"
                    @click="searchQuery"
                  >查询</a-button>
                  <a-button class="btn-reset btn-reset-style" @click="searchReset">重置</a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered="false" style="width: 100%; flex: auto">
        <!-- 操作按钮区域 -->
        <div class="table-operator table-operator-style">
          <a-button @click="handleAdd">新增</a-button>
          <a-dropdown v-if="selectedRowKeys.length > 0">
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item key="1" @click="batchDel">删除</a-menu-item>
            </a-menu>
            <a-button>
              批量操作
              <a-icon type="down" />
            </a-button>
          </a-dropdown>
        </div>
        <a-table
          ref="table"
          bordered
          rowKey="id"
          :columns="columns"
          :dataSource="dataSource"
          :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          :pagination="ipagination"
          :loading="loading"
          @change="handleTableChange"
        >
          <template slot="tooltip" slot-scope="text">
            <a-tooltip placement="topLeft" :title="text" trigger="hover">
              <div class="tooltip">{{ text }}</div>
            </a-tooltip>
          </template>
          <template slot="bindCount" slot-scope="text, record">
            <span v-if="record.isMaxPrivilege==1">全部设备</span>
            <span v-else>{{text}}</span>
          </template>
          <span slot="action" slot-scope="text, record">
            <a @click="handleDetailPage(record)" style="color: #409eff">查看</a>
            <a-divider type="vertical" />
            <a @click="handleEdit(record)" style="color: #409eff">编辑</a>
            <a-divider type="vertical" />
            <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
              <a style="color: #409eff">删除</a>
            </a-popconfirm>
          </span>
        </a-table>
      </a-card>
      <add-group-modal ref="modalForm" @ok="modalFormOk" @close="close"></add-group-modal>
    </a-col>
  </a-row>
</template>

<script>
import { getUserList } from '@/api/api'
import { deleteAction } from '@api/manage'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
import addGroupModal from './modules/addGroupModal'
export default {
  name: 'groupManagement',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components: {
    addGroupModal
  },
  data() {
    return {
      maxLength:50,
      // 表头
      columns: [
        {
          title: '序号',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function(t, r, index) {
            return parseInt(index) + 1
          }
        },
        {
          title: '分组名称',
          dataIndex: 'groupName',
          scopedSlots: {
            customRender: 'tooltip'
          },
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '运维人员',
          dataIndex: 'userNames',
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 150px;max-width:350px'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'tooltip'
          }
        },
        {
          title: '描述',
          dataIndex: 'description',
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'tooltip'
          }
        },
        {
          title: '绑定数',
          dataIndex: 'bindCount',
          scopedSlots: {
            customRender: 'bindCount'
          }
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 147,
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: '/device/deviceGroup/list',
        deleteBatch: '/device/deviceGroup/deleteBatch'
      },
      userList: [] // 运维人员列表
    }
  },
  created() {
    this.getuserList()
  },
  methods: {
    close() {
      this.loadData()
    },
    handleDelete: function(id) {
      var that = this
      deleteAction(that.url.deleteBatch, { ids: id }).then(res => {
        if (res.success) {
          //重新计算分页问题
          that.reCalculatePage(1)
          that.$message.success(res.message)
          that.loadData()
        } else {
          that.$message.warning(res.message)
        }
      })
    },
    // 获取运维人员
    getuserList() {
      let param = {
        pageSize: 10000
      }
      getUserList(param).then(res => {
        if (res.success) {
          this.userList = res.result.records
        }
      })
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
</style>