{"asset": {"version": "2.0", "generator": "babylon.js glTF exporter for 3dsmax 2019 v20240312.5"}, "scene": 0, "scenes": [{"nodes": [0]}], "nodes": [{"mesh": 0, "scale": [60.0, 60.0, 60.0], "name": "detector"}], "meshes": [{"primitives": [{"attributes": {"POSITION": 1, "NORMAL": 2, "TEXCOORD_0": 3}, "indices": 0, "material": 0}, {"attributes": {"POSITION": 5, "NORMAL": 6, "TEXCOORD_0": 7}, "indices": 4, "material": 1}], "name": "detector"}], "accessors": [{"bufferView": 0, "componentType": 5123, "count": 17904, "type": "SCALAR", "name": "accessorIndices"}, {"bufferView": 1, "componentType": 5126, "count": 4122, "max": [0.001, 0.001497386, 0.001], "min": [-0.001, -1.15633014e-08, -0.001], "type": "VEC3", "name": "accessorPositions"}, {"bufferView": 1, "byteOffset": 49464, "componentType": 5126, "count": 4122, "type": "VEC3", "name": "accessorNormals"}, {"bufferView": 2, "componentType": 5126, "count": 4122, "type": "VEC2", "name": "accessorUVs"}, {"bufferView": 0, "byteOffset": 35808, "componentType": 5123, "count": 756, "type": "SCALAR", "name": "accessorIndices"}, {"bufferView": 1, "byteOffset": 98928, "componentType": 5126, "count": 263, "max": [0.0003766886, 0.0005593808, 0.000384938146], "min": [-0.00036846305, 0.000121671386, -0.000360213569], "type": "VEC3", "name": "accessorPositions"}, {"bufferView": 1, "byteOffset": 102084, "componentType": 5126, "count": 263, "type": "VEC3", "name": "accessorNormals"}, {"bufferView": 2, "byteOffset": 32976, "componentType": 5126, "count": 263, "type": "VEC2", "name": "accessorUVs"}], "bufferViews": [{"buffer": 0, "byteLength": 37320, "name": "bufferViewScalar"}, {"buffer": 0, "byteOffset": 37320, "byteLength": 105240, "byteStride": 12, "name": "bufferViewFloatVec3"}, {"buffer": 0, "byteOffset": 142560, "byteLength": 35080, "byteStride": 8, "name": "bufferViewFloatVec2"}], "buffers": [{"uri": "yanganqi.bin", "byteLength": 177640}], "materials": [{"pbrMetallicRoughness": {"baseColorTexture": {"index": 0}, "metallicFactor": 0.0, "roughnessFactor": 0.450053632}, "name": "Material #34"}, {"pbrMetallicRoughness": {"baseColorTexture": {"index": 1}, "metallicFactor": 0.0, "roughnessFactor": 0.450053632}, "name": "Material #35"}], "textures": [{"sampler": 0, "source": 0, "name": "matBody_basecolor.jpg"}, {"sampler": 0, "source": 1, "name": "matCore_basecolor.jpg"}], "images": [{"uri": "matBody_basecolor.jpg"}, {"uri": "matCore_basecolor.jpg"}], "samplers": [{"magFilter": 9729, "minFilter": 9987}]}