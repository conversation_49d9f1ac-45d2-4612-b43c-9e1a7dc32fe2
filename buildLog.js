const fs = require('fs'); // 引入文件系统
const exec = require('child_process').exec; // 开启一个子进程
// 封装命令
function execute(command) {
    return new Promise((resolve, reject) => {
        exec(command, function (err, stdout, stderr) {
            if (err != null) {
                resolve(err)
            } else {
                resolve(stdout)
            }
        })
    })
}

async function start() {
    // 执行npm打包命令
    console.log("开始获取git信息...")
    // const _build = await execute("npm run buildLog")
    // 执行git log命令 查看当前分支提交历史
    let vName = await execute("git name-rev --name-only HEAD")
    const _history = await execute("git show -s --format=%H")
    // 日志拼接
    let newStr = '当前分支：' + `${vName}` + '打包日期：' + new Date().toLocaleString() + '\n' + 'commit:' + _history
    // 将日志写入log.txt
    fs.writeFile('dist/InfoDescription.txt', newStr, 'utf-8', function (err) {
        if (err != null) {
            console.log("ERROR:", err)
        } else {
            console.log("git信息写入 打包完成")
        }
    })
}

// 执行任务
start()