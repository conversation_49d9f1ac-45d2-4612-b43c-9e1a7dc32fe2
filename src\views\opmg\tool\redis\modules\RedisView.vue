<template>
  <a-spin :spinning='confirmLoading' style="width: 100%;height:100%;">
  <a-row class='row-container' style='height:100%'>
    <a-col style='height:100%'>
      <a-card class='card1'>
        <a-col :span='12'>
          <div class="top">
            <div class="name">
              {{ obj ? obj.databaseAlias : '' }}
            </div>
            <div class="database">
              <span v-if="!editableStatus">
                db{{ database }}
                <span style="margin-left: 4px; color: #409eff; cursor: pointer" @click="editButton" >
                  <a-icon type="edit" style="margin-right: 6px"/>
                </span>
              </span>
              <span v-else class="edit"> db
                <a-input-group compact style='display: flex;margin-left: 4px;line-height:32px !important;'>
                  <a-input-number v-model="database" :min="0" :max="15" placeholder="请输入" style="width:80px" @keyup.enter="changeDataBase" />
                  <a-button icon='check' @click="changeDataBase"></a-button>
                  <a-button icon='close' @click="editableStatus = false"></a-button>
                </a-input-group>
              </span>
            </div>
            <a-popconfirm :title="`确定删除db${database}吗?`" @confirm="() => deleteDataBase()">
              <a-icon type="delete" :style="{ fontSize: '16px', color: '#409eff' }" class="icon"/>
            </a-popconfirm> 
          </div>
        </a-col>
        <a-col :span='12'>
          <div style='text-align: right;'>
            <img src='~@/assets/return1.png' alt='' @click='getGo' style='width: 20px;height: 20px;cursor: pointer'/>
          </div>
        </a-col>
      </a-card>
      <a-card class='info-card'>
        <template>
          <a-row style='height: 100%; display: flex; overflow-x: auto'>
            <a-col ref='print' id='printContent' :xl='12' :lg='12' :md='10' :sm='10' :xs='10' style="height: 100%;">
              <a-card class='card-chile' style="margin-right: 7px;">
                <a-input-search v-model="redisKey" style="margin-bottom: 8px" placeholder="请输入redisKey" @search="onSearch" />
                <a-tree
                  ref="treeRef"
                  :autoExpandParent="autoExpandParent"
                  :defaultExpandParent="defaultExpandParent"
                  :tree-data="treeData"
                  :replaceFields="{
                    children: 'children',
                    title: 'name',
                    key: 'key',
                    value: 'name'
                  }"
                  @select="onSelect"
                  show-icon
                  :icon="getIcon">
                </a-tree>
                <div v-if="!treeData || treeData.length==0" style="padding-top: 14.5%;">
                  <a-list :data-source="[]" />
                </div>
              </a-card>
            </a-col>
            <a-col :span='12' class='contTwo' style='overflow-x: auto; height:100%;'>
              <a-card class='card-chile' style="margin-left: 7px;">
                <div v-if="redisObj.redisKey" class="card-right">
                  <div class="title">
                    <div class="tag" :style="{background: getColor(redisObj.type)}">{{ redisObj.type }}</div>
                    <div class="name">{{ redisObj.redisKey }}</div>
                  </div>
                  <div class="subtitle">
                    <div class="lf">
                      <div class="size">Key Size: {{ redisObj.mUsage }}</div>
                      <div class="len">Length: {{ redisObj.len }}</div>
                      <div class="ttl">TTL: {{ redisObj.ttl }}</div>
                    </div>
                    <div class="rf">
                      <div class="action">
                        <!-- <a-icon type="redo" class="icon" :style="{ fontSize: '16px', color: '#000' }"/> -->
                        <a-popconfirm :title="`确定删除${redisObj.redisKey}吗?`" @confirm="() => delKey()">
                          <a-icon type="delete" class="icon" :style="{ fontSize: '16px', color: '#409eff' }"/>
                        </a-popconfirm>
                      </div>
                    </div>
                  </div>
                  <div class="line"></div>
                  <div class="content">{{ redisObj.value }}</div>
                </div>
                <div v-if="!redisObj.redisKey" class="card-right" style="padding-top: 20%;">
                  <a-list :data-source="[]" />
                </div>
              </a-card>
            </a-col>
          </a-row>
        </template>
      </a-card>
    </a-col>
  </a-row>
</a-spin>
</template>

<script>
import {
  httpAction,getAction, postAction, deleteAction
} from '@/api/manage'
import pick from 'lodash.pick'

export default {
  name: 'RedisView',
  components: {
    pick,
  },
  props: {
    obj: {
      type: Object
    }
  },
  data() {
    return {
      treeData:[],
      labelCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 8
        },
        md: {
          span: 7
        },
        lg: {
          span: 6
        },
        xl: {
          span: 5
        }
      },
      wrapperCol: {
        xs: {
          span: 23
        },
        sm: {
          span: 16
        },
        md: {
          span: 17
        },
        lg: {
          span: 18
        },
        xl: {
          span: 18
        }
      },
      confirmLoading: false,
      url: {
        createRedisConnection: '/redis/client/redisConnect', // 测试连接
        delDataBase: '/redis/client/flushdb', // 删除数据库
        redisTree: '/redis/client/redisTree', // 获取redisTree
        getKey: '/redis/client/getKey', // 获取某个key的内容
        delKey: '/redis/client/delKey' // 删除key
      },
      database: 0, // 默认显示当前数据库
      editableStatus: false, // 是否是可编辑状态
      autoExpandParent: true,
      defaultExpandParent: true,
      redisObj: {}, // 当前的key的内容
      redisKey: null // 记录当前的key
    }
  },
  created() {
    this.getTreeData()
  },
  methods: {
    // 设置颜色值
    getColor(type) {
      let color = '#0099FF'
      if (type=='string') {
        color = '#9933FF'
      }
      if (type=='hash') {
        color = '#0099FF'
      }
      if (type=='list') {
        color = '#66CC66'
      }
      if (type=='set') {
        color = '#CC6600'
      }
      if (type=='json') {
        color = '#999999'
      }
      if (type=='stream') {
        color = '#999966'
      }
      return color
    },
    // 自定义图标
    getIcon(props) {
      const { isLeaf, expanded } = props;
      if (isLeaf == 1) {
        if (props.type) {
          let color = this.getColor(props.type)
          return <span class="tag" style={"background:" + color}>{props.type}</span>
        } else {
          return ''
        }
      } else {
          return <a-icon type={expanded ? "folder-open" : "folder"} />;
      }
    },
    getGo() {
      this.$parent.pButton2(0, '')
    },
    getTreeData(){
      this.treeData = []
      let params = {
        rid: this.obj.id,
        db: this.database,
        redisKey: this.redisKey
      }
      this.confirmLoading = true
      httpAction(this.url.redisTree, params, 'get').then((res) => {
        if (res.success) {
          let data = []
          if (res.result.children && Array.isArray(res.result.children)) {
            data = res.result.children
            this.traverseAndConvertLeaf(data)
          }
          this.treeData = data
        } else {
          this.$message.warning(res.message)
        }
        this.confirmLoading = false
      }).catch(err=>{
        this.$message.warning(err.message)
        this.confirmLoading = false
      })
    },
    // 将返回的isLeaf 转换成boolen类型
    traverseAndConvertLeaf(nodes) {
      nodes.forEach(node => {
        if (node.hasOwnProperty('isLeaf') && node.isLeaf === 1) {
          node.isLeaf = true;
        } else {
          node.isLeaf = false;
        }
        if (node.children && Array.isArray(node.children)) {
          this.traverseAndConvertLeaf(node.children);
        }
      })
    },
    onSelect(selectedKeys, e) {
      const that = this
      let record = e.node.dataRef
      if (!record.key || !record.isLeaf) {
        return
      }
      let params = {
        redisKey: record.key,
        rid: that.obj.id,
        type: record.type || ''
      }
      that.confirmLoading = true
      httpAction(that.url.getKey, params, 'get').then((res) => {
        if (res.success) {
          that.redisObj = res.result
        } else {
          that.$message.warning(res.message)
        }
        that.confirmLoading = false
      }).catch(err=>{
        that.$message.warning(err.message)
        that.confirmLoading = false
      })
    },
    // 删除key
    delKey(){
      const that = this
      if (!this.redisObj.redisKey) {
        return
      }
      let params = {
        redisKey: this.redisObj.redisKey,
        rid: that.obj.id
      }
      that.confirmLoading = true
      deleteAction(that.url.delKey, params).then((res) => {
        if (res.success) {
          this.init()
          that.$message.success(res.message)
        } else {
          that.$message.warning(res.message)
        }
        that.confirmLoading = false
      }).catch(err=>{
        that.$message.warning(err.message)
        that.confirmLoading = false
      })
    },
    editButton() {
      // 编辑数据库
      this.editableStatus = true
    },
    init() {
      this.editableStatus = false
      this.redisObj = {}
      this.treeData = []
      this.redisKey = null
      this.getTreeData()
    },
    changeDataBase() {
      // 切换数据库
      let reg = /^(0|[1-9]|1[0-5])$/
      if (!reg.test(this.database)) {
        return this.$message.warn('请输入0-15之间的整数！')
      }
      this.editableStatus = false
      this.init()
    },
    deleteDataBase() {
    // 清空数据库
    const that = this
      if (!that.obj.id) {
        return
      }
      let params = {
        rid: that.obj.id
      }
      that.confirmLoading = true
      getAction(that.url.delDataBase, params).then((res) => {
        if (res.success) {
          this.init()
          that.$message.success(res.message)
        } else {
          that.$message.warning(res.message)
        }
        that.confirmLoading = false
      }).catch(err=>{
        that.$message.warning(err.message)
        that.confirmLoading = false
      })
      this.init()
    },
    onSearch(value) {
      if (value) {
        this.redisKey = value;
      } else {
        this.redisKey = null;
      }
      this.treeData = []
      this.getTreeData()
    },
  }
}
</script>
<style lang='less' scoped>
/deep/ .ant-tree-switcher.ant-tree-switcher-noop {
    width: 1px !important;
}
/deep/ .ant-tree li span.ant-tree-iconEle {
  display: inline;
}
/deep/ .ant-spin-container {
  height: 100%;
}
.tag {
  min-width: 52px;
  display: inline-block;
  border-radius: 4px;
  padding: 0 7px 1px 7px;
  background: #409EFF;
  color: #fff;
  margin-right: 10px;
  text-align: center;
}
.row-container {
  height: 100%;
}

.card1 {
  height: 77px;
  .top{
    display: flex;
    align-items: center;
    .name {
      font-size: 18px;
      font-weight: 600;
    }
    .database {
      font-size: 18px;
      font-weight: bold;
      margin-left: 20px;
      .edit {
        display: flex;
        align-items: center;
      }
    }
    .icon {
      margin-left: 20px;
      cursor: pointer;
    }
    
  }
}

.info-card {
  margin-top: 16px;
  height: calc(100% - 93px);
  overflow-y: auto;

  /deep/ .ant-card-body {
    height: 100%;
  }
}
.card-chile {
  width:calc(100% - 7px);
  height: 100%;
  overflow-y: auto;

  /deep/ .ant-card-body {
    height: 100%;
  }
}
.card-right{
  font-size: 16px;
    .title{
      display: flex;
      align-items: center;
      .name {
        font-size: 16px;
      }
    }
    .subtitle {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 15px;
      .lf {
        display: flex;
        align-items: center;
        color: rgba(0,0,0,0.55);
        font-size: 15px;
        .size {
          margin-right: 25px;
        }
        .len {
          margin-right: 25px;
        }
      }
      .rf {
        display: flex;
        align-items: center;
        .icon {
          margin-left: 15px;
          cursor: pointer;
        }
      }
    }
    .line {
      width: 100%;
      height: 1px;
      background: #eee;
      margin-top: 15px;
    }
    .action {
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }
    .content {
      margin-top: 20px;
      word-break: break-all;
      padding-bottom: 20px;
    }
}
</style>