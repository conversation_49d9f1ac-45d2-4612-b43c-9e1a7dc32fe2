<template>
  <div v-if='kInfo.filesArr&&kInfo.filesArr.length>0' :class='[showOpmgKInfo?"opmg-know-file":"one-click-help-file"]'>
    <!--知识审批界面显示-->
    <div v-if='approvalView' class='download-tip'>
      <span>知识创建者设置附件是否允许下载：</span>
      <yq-icon v-if='canDownload' :type='"allowDownload"' style='font-size: 30px' title='允许下载'></yq-icon>
      <yq-icon v-else :type='"notAllowDownload"' style='font-size: 30px' title='不允许下载'></yq-icon>
    </div>

    <!-- 附件：文本知识附件-->
    <div v-if='kInfo.knowledgeType!=1' class='text-wrapper'>
      <div v-for='(item,index) in kInfo.filesArr'
           :key='"file-item_"+index'
            class='file-item'
           :class='{"file-item-download":canDownload||approvalView||(!showShare&&sysUserId===kInfo.createByUserId)}'
           @click='downloadFile(item,showShare)'>
        <a-icon type='paper-clip'></a-icon>
        <span class='file-item-name' :title='item.fileName+item.suffixName'>{{item.fileName}}</span>
        <span :title='item.fileName+item.suffixName'>{{item.suffixName}}</span>
        <a-icon
          v-if='item.indexFile'
          :title='item.indexFile==="1"?"该文件已索引":"该文件未索引"'
          :type='item.indexFile==="1"?"check-circle":"minus-circle"'
          :style='{marginLeft:"4px",color:item.indexFile==="1"?"#4bd863":"#D81E06"}'/>
      </div>
    </div>

    <!--附件：文档知识附件-->
    <div v-else-if='kInfo.filesArr.length>0' class='file-wrapper'>
      <a-row class='file-row' type="flex">
        <!--          文件目录-->
        <a-col class='file-list' :style='{width:openFileMenu?"332px":"0px"}'>
          <div v-for='(item,index) in kInfo.filesArr'
               :key='"file-list-item_"+index'
               :class='{"file-list-item-selected":item.isSelected===true}'
               :title='item.fileName+item.suffixName'
               class='file-list-item' @click='selectFile(item,index)'>
            <div class='file-list-item-name'>{{item.fileName}}</div>
            <div>{{item.suffixName}}</div>
          </div>
        </a-col>
        <!--          预览文件-->
        <a-col class='view-wrapper' :style='{width:openFileMenu?"calc(100% - 332px)":"100%"}'>
          <div class='file-action-wrapper'>
            <div class='open-file-menu-wrapper'>
              <div class='action-btn'
                        :title='openFileMenu?"收起":"展开"'
                        @click='openFileMenu=!openFileMenu'>
                <a-icon class='icon' :type='openFileMenu?"menu-fold":"menu-unfold"' style="font-size:15px;color:#99A4AA;"/>
              </div>
            </div>
            <div class='download-fullscreen-wrapper'>
              <a-button v-if='approvalView || canDownload || (!showShare&&sysUserId === kInfo.createByUserId)'
                        class='action-btn'
                        icon='cloud-download'
                        title='下载'
                        @click='downloadFile(currFileview,showShare)'>
              </a-button>
              <a-button class='action-btn' icon='fullscreen' title='独立显示' @click='newPageDisplay(currFileview)'>
              </a-button>
            </div>
          </div>
          <div class='view-area'>
            <!--            无格式文本-->
            <div v-if='!kkfileviewUrl||kkfileviewUrl=="null"'  class='unformatted-content'>
              <!--                  png、jpg、jpeg-->
              <div v-if='imgUrl' class='unformatted-img'>
                <img :src="imgUrl"/>
              </div>
              <!--                  索引为空-->
              <div v-else-if='!unformattedContent' class='unformatted-null'>该文件未被索引，请下载查看文件内容</div>
              <!--                 索引非空-->
<!--              <div v-else class='unformatted-text'>{{unformattedContent}}</div>-->
              <div v-else class='unformatted-text'  v-html='unformattedContent'></div>
            </div>
            <!--            在线预览-->
            <div v-else class='iframe-box' ref='iframeParentNode'>
              <div id="loadingMessage"  ref='loadingMessage' v-show='isLoading==true' class="loading-dots" style="">
                <span>正在加载，请稍等</span><div></div><div></div><div></div>
              </div>
              <!--              <iframe class='iframe'></iframe>-->
            </div>
          </div>
        </a-col>
      </a-row>
    </div>
  </div>
</template>
<script>

import YqIcon from '@comp/tools/SvgIcon'
import {
  knowledgeAttachmentPreviewMixins
} from '@views/opmg/knowledgeManagement/knowledgeBase/modules/knowledgeAttachmentPreviewMixins'
export default {
  name: "AttachmentPreview",
  mixins: [knowledgeAttachmentPreviewMixins],
  components: { YqIcon },
  props: {
    kInfo: {
      type: Object,
      required: false,
      default: {},
    },
    /**若是通过知识审批列表打卡查看，
     收藏、分享、打印、评论、关联、点赞、点踩都不可操作性，
     附件统统可以下载，同时告诉管理员，知识创建者设置的允许下载附件状态*/
    approvalView: {
      type: Boolean,
      required: false,
      default: false
    },
    canDownload: {
      type: Boolean,
      required: false,
      default: false
    },
    kkfileviewUrl: {
      type: [String],
      required: true
    },
    /*用于区分运维中心和运维助手知识详情页面，采用不同的样式*/
    showOpmgKInfo: {
      type: Boolean,
      required: false,
      default: true
    },
    /*区分是否是分享页面，默认不是分享页面*/
    showShare: {
      type: Boolean,
      required: false,
      default: false
    },
  }
}
</script>

<style lang='less' scoped>
/**********运维小助手深色模式下的样式***********/
.one-click-help-file {
  margin: 24px 0px 14px 0px;
  font-size: 14px;
  background-color: transparent;

  .download-tip {
    display: flex;
    justify-content: start;
    align-items: center;
    flex-flow: row wrap;
    margin-bottom: 10px;
  }

  .text-wrapper {
    .file-item{
      padding: 0 6px;
      border-radius: 2px;
      display: flex;
      flex-flow: row nowrap;
      justify-content: start;
      align-items: center;

      .file-item-name{
        display: inline-block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    .file-item:hover {
      background: #ecf5ff !important;
    }
    .file-item-download {
      color: #409eff
    }

    .file-item-download:hover {
      cursor: pointer;
      //background: #ecf5ff !important;
    }
  }

  .file-wrapper {
    border: 1px solid rgba(104,135,183,0.22);
    background: transparent;
    height: 900px;

    .file-row {
      height: 100%;
      display: flex;
      flex-flow: row nowrap;

      .file-list {
        border-right: 1px solid rgba(104,135,183,0.22);
        background: transparent;
        //width: 200px;
        height: 100%;
        //overflow: auto;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;

        .file-list-item {
          border-bottom: 1px solid rgba(104,135,183,0.22);;
          padding: 17px;
          width: 100%;
          color: #99A4AA ;
          font-weight: 400;
          display: flex;
          cursor: pointer;

          .file-list-item-name {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }

        .file-list-item-selected {
          color: #fff;
          font-weight: 500;
          border-left: 4px solid #409eff;
          background: rgba(21,85,175,0.24);
        }
      }

      .file-list::-webkit-scrollbar {
        display: none;
        width: 0px;
      }

      .view-wrapper {
        //width: calc(100% - 200px);
        height: 100%;
        position: relative;
        overflow: hidden;

        .file-action-wrapper{
          display: flex;
          justify-content: space-between;
          align-items: center;
          flex-flow: row nowrap;
          padding: 14px;
          //margin-left: 6px;
          background: transparent;

          .open-file-menu-wrapper{
            .action-btn {
              border: none;

              .icon{
                font-size: 24px;
                color: #409eff;
              }
            }
            .action-btn:hover, .action-btn:active, .action-btn:focus {
              color: #409eff;
              cursor: pointer;
            }
          }
          .download-fullscreen-wrapper {
            text-align: right;
            margin-left: 6px;
            .action-btn {
              background: #132540;
              border: 1px solid #132540;
              color: #939393;
              border-radius: 2px;
              margin: 0px 4px;
              height: 28px;
              width: 28px;
            }

            .action-btn:hover, .action-btn:active, .action-btn:focus {
              color: #409eff !important;
              border: 1px solid #409EFF;
              cursor: pointer;
            }

            .btn-icon {
              color: #409eff
            }
          }

        }

        .view-area {
          height: calc(100% - 56px);

          .unformatted-content {
            margin: 6px 0px 0px 6px;
            overflow: auto;
            height: calc(100% - 6px);

            .unformatted-img {}

            .unformatted-null {
              margin: 50px 0;
              text-align: center;
              font-size: 18px;
              font-weight: bold;
            }

            .unformatted-text {
              word-break: break-all;
              white-space: normal;
            }
          }

          .iframe-box {
            height: 100%;
            width: 100%;
            position: relative;

            .loading-dots {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 200px;
              text-align: center;
              padding: 50px;
              font-size: 18px;
              font-weight: bold;
              z-index: 1;
              display: -ms-flexbox;
              display: flex;
              flex-flow: row nowrap;
              -ms-flex-pack: center;
              justify-content: center;
              -ms-flex-align: start;
              align-items: center;
            }
            .loading-dots div {
              width: 4px;
              height: 4px;
              margin: 0 2px;
              background-color: #000;
              border-radius: 50%;
              //animation: bounce 0.4s infinite alternate;
            }

            .loading-dots div:nth-child(1) {
              animation-delay: 0s;
            }

            .loading-dots div:nth-child(2) {
              animation-delay: 0.1s;
            }

            .loading-dots div:nth-child(3) {
              animation-delay: 0.3s;
            }

            @keyframes bounce {
              to {
                opacity: 0.3;
                transform: translateY(-8px);
              }
            }

            .iframe {
              height: 100%;
              width: 100%;
              border: none;
              background-color: rgba(247, 247, 247, 0.75)
            }
          }
        }
      }
    }
  }
}
.opmg-know-file {
  margin: 24px 0px 14px 0px;
  font-size: 14px;

  .download-tip {
    display: flex;
    justify-content: start;
    align-items: center;
    flex-flow: row wrap;
    margin-bottom: 10px;
  }

  .text-wrapper {
    .file-item{
      padding: 0 6px;
      border-radius: 2px;
      display: flex;
      flex-flow: row nowrap;
      justify-content: start;
      align-items: center;

      .file-item-name{
        display: inline-block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    .file-item:hover {
      background: #ecf5ff !important;
    }
    .file-item-download {
      color: #409eff
    }

    .file-item-download:hover {
      cursor: pointer;
      //background: #ecf5ff !important;
    }
  }

  .file-wrapper {
    border: 1px solid #E9E9E9;
    border-radius: 2px;
    background: rgba(247, 247, 247, 0.75);
    height: 900px;

    .file-row {
      height: 100%;
      display: flex;
      flex-flow: row nowrap;

      .file-list {
        border-right: 1px solid #E9E9E9;
        background: #fff;
        //width: 200px;
        height: 100%;
        //overflow: auto;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
        box-shadow: 3px 0px 6px 0px #E9E9E9;

        .file-list-item {
          border-bottom: 1px solid #E9E9E9;
          padding: 17px;
          width: 100%;
          color: rgba(0, 0, 0, 0.45);
          font-weight: 400;
          display: flex;
          cursor: pointer;

          .file-list-item-name {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }

        .file-list-item-selected {
          color: rgba(0, 0, 0, 0.85);
          font-weight: 500;
          border-left: 4px solid #409eff;
          box-shadow: 0 1px 6px #ccc, 1px 0 2px #ccc;
        }
      }

      .file-list::-webkit-scrollbar {
        display: none;
        width: 0px;
      }

      .view-wrapper {
        //width: calc(100% - 200px);
        height: 100%;
        position: relative;
        overflow: hidden;

        .file-action-wrapper{
          display: flex;
          justify-content: space-between;
          align-items: center;
          flex-flow: row nowrap;
          padding: 14px 14px 13px 14px;
          background: #fff;
          border-bottom: 1px solid #E9E9E9;

          .open-file-menu-wrapper{
            .action-btn {
              border: none;

              .icon{
                font-size: 24px;
                color: #409eff;
              }
            }
            .action-btn:hover, .action-btn:active, .action-btn:focus {
              color: #409eff;
              cursor: pointer;
            }
          }
          .download-fullscreen-wrapper {
            text-align: right;
            .action-btn {
              background: #FFFFFF;
              border: none;
              color: #99A4AA;
              margin: 0px 4px;
              height: 28px;
              width: 28px;
            }

            .action-btn:hover, .action-btn:active, .action-btn:focus {
              color: #409eff;
              background: #ecf5ff !important;
              border-color: #b3d8ff !important;
              cursor: pointer;
            }

            .btn-icon {
              color: #409eff
            }
          }
        }

        .view-area {
          height: calc(100% - 57px);

          .unformatted-content {
            margin: 6px 0px 0px 6px;
            overflow: auto;
            height: calc(100% - 6px);

            .unformatted-img {
            }

            .unformatted-null {
              margin: 50px 0;
              text-align: center;
              font-size: 18px;
              font-weight: bold;
            }

            .unformatted-text {
              word-break: break-all;
              white-space: normal;
            }
          }

          .iframe-box {
            height: 100%;
            width: 100%;
            position: relative;

            .loading-dots {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 200px;
              text-align: center;
              padding: 50px;
              font-size: 18px;
              font-weight: bold;
              z-index: 1;
              display: -ms-flexbox;
              display: flex;
              flex-flow: row nowrap;
              -ms-flex-pack: center;
              justify-content: center;
              -ms-flex-align: start;
              align-items: center;
            }

            .loading-dots div {
              width: 4px;
              height: 4px;
              margin: 0 2px;
              background-color: #000;
              border-radius: 50%;
              //animation: bounce 0.4s infinite alternate;
            }

            .loading-dots div:nth-child(1) {
              animation-delay: 0s;
            }

            .loading-dots div:nth-child(2) {
              animation-delay: 0.1s;
            }

            .loading-dots div:nth-child(3) {
              animation-delay: 0.3s;
            }

            @keyframes bounce {
              to {
                opacity: 0.3;
                transform: translateY(-8px);
              }
            }

            .iframe {
              height: 100%;
              width: 100%;
              border: none;
              background-color: rgba(247, 247, 247, 0.75)
            }
          }
        }
      }
    }
  }
}
</style>