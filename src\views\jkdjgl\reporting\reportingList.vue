<template>
  <a-row :gutter='10' style='height: 100%' class='vScroll'>
    <a-col style='width: 100%; height: 100%; display: flex; flex-direction: column'>
      <!-- 查询区域 -->
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <div class='table-page-search-wrapper-style'>
          <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
            <a-row :gutter='24' ref='row'>
              <a-col :span='spanValue'>
                <a-form-item label='上级单位'>
                  <a-tree-select placeholder='请选择上级单位' v-model='queryParam.parentDepartCode' :allowClear='true'
                                 :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }"
                                 :treeData="parentTree" :replaceFields='{value:"departNameEn"}'></a-tree-select>
                </a-form-item>
              </a-col>
<!--              <a-col :span='spanValue'>-->
<!--                <a-form-item label='单位名称'>-->
<!--                  <a-input :maxLength='maxLength' placeholder='请输入单位名称' v-model='queryParam.parentDepartName' :allowClear='true'-->
<!--                           autocomplete='off' />-->
<!--                </a-form-item>-->
<!--              </a-col>-->
<!--              <a-col :span='spanValue'>-->
<!--                <a-form-item label='单位标识'>-->
<!--                  <a-input :maxLength='maxLength' placeholder='请输入单位标识' v-model='queryParam.parentDepartCode' :allowClear='true'-->
<!--                           autocomplete='off' />-->
<!--                </a-form-item>-->
<!--              </a-col>-->
<!--              <a-col :span='spanValue'>-->
<!--                <a-form-item label='本级单位名称'>-->
<!--                  <a-input :maxLength='maxLength' placeholder='请输入本级单位名称' v-model='queryParam.selfDepartName' :allowClear='true'-->
<!--                           autocomplete='off' />-->
<!--                </a-form-item>-->
<!--              </a-col>-->
              <a-col :span='colBtnsSpan()'>
                <span class='table-page-search-submitButtons'
                      :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button type='primary' class='btn-search btn-search-style' @click='searchQuery'>查询</a-button>
                  <a-button class='btn-reset btn-reset-style' @click='searchReset'>重置</a-button>
                  <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <!-- 查询区域-END -->
      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <!-- 操作按钮区域 -->
        <div class='table-operator table-operator-style'>
          <a-button @click='handleAdd' v-has="'terminal:add'">新增</a-button>
          <a-dropdown>
            <a-menu slot='overlay' style='text-align: center'>
              <a-menu-item key='1'>
                <a @click='fullReportHandle("device")'>设备</a>
              </a-menu-item>
              <a-menu-item key='2'>
                <a @click='fullReportHandle("terminal")'>终端</a>
              </a-menu-item>
              <a-menu-item key='3'>
                <a @click='fullReportHandle("depart")'>单位</a>
              </a-menu-item>
            </a-menu>
            <a-button>
              全量上报
              <a-icon type='down' />
            </a-button>
          </a-dropdown>
          <a-dropdown v-if='selectedRowKeys.length > 0'>
            <a-menu slot='overlay' style='text-align: center'>
              <a-menu-item key='1' @click='batchDel'>删除</a-menu-item>
            </a-menu>
            <a-button> 批量操作
              <a-icon type='down' />
            </a-button>
          </a-dropdown>
        </div>
        <!-- table区域-begin -->
        <a-table ref='table' bordered rowKey='id' :columns='columns' :dataSource='dataSource'
                 :scroll='dataSource.length>0?{x:"max-content"}:{}' :pagination='ipagination' :loading='loading'
                 :rowSelection='{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }'
                 @change='handleTableChange'>
          <span class='caozuo' slot='pushCount' slot-scope='text, record'><a
            @click='countGo(record)'>{{ text }}</a></span>
          <span class='caozuo' slot='pushSuccessCount' slot-scope='text, record'>
            <a @click='successGo(record)'>{{ text }}</a>
          </span>
          <span class='caozuo' slot='action' slot-scope='text, record'>
            <a @click='handleDetailPage(record)'>查看</a>
            <a-divider type='vertical' />
             <a-dropdown>
                <a-menu slot='overlay'>
                   <a-menu-item key='5'>
                       <a @click='handleEdit(record)'>编辑</a>
                  </a-menu-item>
                  <a-menu-item key='4'>
                     <a-popconfirm title='确定删除吗?' @confirm='() => handleDelete(record.id)'>
                        <a>删除</a>
                      </a-popconfirm>
                  </a-menu-item>
                </a-menu>
                 <a>更多</a>
              </a-dropdown>
          </span>
          <template slot='tooltip' slot-scope='text'>
            <a-tooltip placement='topLeft' :title='text' trigger='hover'>
              <div class='tooltip'>
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
      </a-card>
      <!-- 表单区域 -->
      <reporting-modal ref='modalForm' @ok='modalFormOk'></reporting-modal>
    </a-col>
  </a-row>
</template>

<script>
import {
  JeecgListMixin
} from '@/mixins/JeecgListMixin'
import reportingModal from './modules/reportingModal'
import {
  deleteAction, getAction
} from '@/api/manage'
import {
  YqFormSearchLocation
} from '@/mixins/YqFormSearchLocation'

export default {
  name: 'reportingList',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components: {
    reportingModal
  },
  data() {
    return {
      // maxLength:50,
      description: '上级管理页面',
      formItemLayout: {
        labelCol: {
          style: 'width:100px'
        },
        wrapperCol: {
          style: 'width:calc(100% - 100px)'
        }
      },
      queryParam: {
        levelType: '1'
      },
      columns: [
        {
        title: '上级单位名称',
        dataIndex: 'parentDepartName',
        customCell: () => {
          let cellStyle = 'text-align: left; min-width: 50px;max-width:300px'
          return {
            style: cellStyle
          }
        }
      },
        {
          title: '上级单位标识',
          dataIndex: 'parentDepartCode',
          customCell: () => {
            let cellStyle = 'text-align: center; min-width: 110px'
            return {
              style: cellStyle
            }
          }
        }, {
          title: '上级平台标识',
          dataIndex: 'parentPlatformCode',
          customCell: () => {
            let cellStyle = 'text-align: center; min-width: 110px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '上级服务地址',
          dataIndex: 'parentServerAddress',
          customCell: () => {
            let cellStyle = 'text-align: center; min-width: 110px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '数据汇聚请求路径',
          dataIndex: 'parentRequestUrl',
          scopedSlots: {
            customRender: 'tooltip'
          },
          customCell: () => {
            let cellStyle = 'text-align: left; min-width: 150px;max-width:300px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '本级单位名称',
          dataIndex: 'selfDepartName',
          customCell: () => {
            let cellStyle = 'text-align: center; min-width: 130px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '本级单位标识',
          dataIndex: 'selfDepartCode',
          customCell: () => {
            let cellStyle = 'text-align: center; min-width: 130px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '本级平台标识',
          dataIndex: 'selfPlatformCode',
          customCell: () => {
            let cellStyle = 'text-align: left; min-width: 180px;max-width:280px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '本级单位所在地',
          dataIndex: 'selfLocation',
          customCell: () => {
            let cellStyle = 'text-align: center; width: 150px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '向上级推送记录总数',
          dataIndex: 'pushCount',
          customCell: () => {
            let cellStyle = 'text-align: center; min-width: 110px'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'pushCount'
          }
        },
        {
          title: '向上级推送记录成功数',
          dataIndex: 'pushSuccessCount',
          customCell: () => {
            let cellStyle = 'text-align: center; width: 100px'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'pushSuccessCount'
          }
        },
        {
          title: '操作',
          dataIndex: 'action',
          fixed: 'right',
          customCell: () => {
            let cellStyle = 'text-align: center; width: 160px'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'action'
          }
        }
      ],
      url: {
        list: '/dataReport/manage/list',
        delete: '/dataReport/manage/deleteBatch',
        deleteBatch: '/dataReport/manage/deleteBatch',
        departList: '/device/statis/queryMyDeptTreeList'
      },
      parentTree: [],
    }
  },
  created() {
    this.getDepart()
  },
  methods: {
    //获取单位数据
    getDepart() {
      getAction(this.url.departList).then((res) => {
        this.parentTree = res.result
      })
    },
    //全量上报
    fullReportHandle(dataType) {
      getAction("/data/reportAndConverge/fullReport",{dataType:dataType}).then((res) => {
        if(res.success){
          this.$message.success(res.result)
        }else{
          this.$message.error(res.message)
        }
      })
    },
    countGo(record) {
      this.$router.push({
        path: '/jkdjgl/reporting/logManage',
        query: {
          'logType': '0',
          'parentDepartCode': record.parentDepartCode
        }
      })
    },
    successGo(record) {
      this.$router.push({
        path: '/jkdjgl/reporting/logManage',
        query: {
          // 'childDepartCode': record.childDepartCode,
          'logType': '0',
          'resultFlag': true,
          'parentDepartCode': record.parentDepartCode
        }
      })
    },
    searchReset() {
      this.queryParam = {
        levelType: '1'
      }
      this.loadData(1)
    },
    handleDelete: function(id) {
      var that = this
      deleteAction(that.url.delete, {
        ids: id
      }).then((res) => {
        if (res.success) {
          //重新计算分页问题
          that.reCalculatePage(1)
          that.$message.success(res.message)
          that.loadData()
        } else {
          that.$message.warning(res.message)
        }
      })
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
</style>