<template>
  <div style="height:100%">
    <component style="height:100%" :is="pageName" :data="data" />
  </div>
</template>
<script>
//   import nodeMonitorDetail from './modules/nodeMonitorDetail'
  import migrateList from './migrateList'
  export default {
    name: "migrateManage",
    data() {
      return {
        isActive: 0,
        data: {},
      };
    },
    components: {
    //   nodeMonitorDetail,
      migrateList
    },
    created() {
      this.pButton1(0);
    },
    //使用计算属性
    computed: {
      pageName() {
        switch (this.isActive) {
          case 0:
            return "migrateList";
          default:
            return "nodeMonitorDetail";
        }
      }
    },
    methods: {
      pButton1(index, item) {
        this.isActive = index;
        this.data = item;
      },
      pButton2(index, item) {
        this.isActive = index;
        this.data = item
      }
    }
  }
</script>