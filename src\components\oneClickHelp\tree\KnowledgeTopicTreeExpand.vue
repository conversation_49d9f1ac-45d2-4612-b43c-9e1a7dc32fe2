<template>
  <div style='height: 100%; border-radius: 3px;display: flex;flex-direction: column'>
    <div style='margin:16px 14px 14px 14px' v-if='inputFlag'>
      <a-input-search @change='onChange'  v-model='inputSearch' style='width: 100%'
                      :placeholder='placeholder' />
    </div>
    <!-- 树-->
    <div class='hScroll vScroll' style='flex:auto;margin:9px 14px 14px 14px'>
      <template>
        <a-dropdown :trigger='[this.dropTrigger]' @visibleChange='dropStatus'>
          <a-tree
            @select='onSelect'
            :selectedKeys='selectedKeys'
            :checkedKeys='checkedKeys'
            :treeData='treeData'
            :checkStrictly='checkStrictly'
            :expandedKeys='expandedKeys'
            @expand='onExpand'
            style='user-select: none'
            :showLine='isShowLine'
            :showIcon='isShowIcon'>
            <template slot='title' slot-scope='{title ,type}'>
              <a-icon v-if='isShowIcon' :type='setIcon(type)' style='color:#409eff' />
              <span v-if='title.indexOf(searchValue) > -1 && title !== firstTitle'>
                {{ title.substr(1, title.indexOf(searchValue)) }}
                <span style='color: #f50'>{{ searchValue }}</span>
                {{ title.substr(title.indexOf(searchValue) + searchValue.length) }}
              </span>
              <span v-else-if='title.indexOf(searchValue) > -1 && title === firstTitle'>
                <span style='background-color: #bae7ff; color: #f50'>{{ title }}</span>
              </span>
              <span v-else>{{ title }}</span>
            </template>
            <span slot='customSwitcherIcon' slot-scope='{isLeaf}'>
              <span class='custom-switcher-icon-leaf' v-if='isLeaf'></span>
              <span class='custom-switcher-icon-noleaf' v-else></span>
            </span>
          </a-tree>
        </a-dropdown>
      </template>
    </div>
  </div>
</template>
<script>
import { getAction } from '@api/manage'
//通过Key获取对应地title
const getTitleByKey = (key, tree) => {
  let selectTitle
  for (let i = 0; i < tree.length; i++) {
    const node = tree[i]
    if (node.key === key) {
      selectTitle = node.title
      break
    }
    if (node.children) {
      selectTitle = getTitleByKey(key, node.children)
    }
  }
  return selectTitle
}
//子节点匹配，获取父节点，用于展开tree
const getParentKey = (key, tree) => {
  let parentKey
  for (let i = 0; i < tree.length; i++) {
    const node = tree[i]
    if (node.children) {
      if (node.children.some((item) => item.key === key)) {
        parentKey = node.key
      } else if (getParentKey(key, node.children)) {
        parentKey = getParentKey(key, node.children)
      }
    }
  }
  return parentKey
}
//生成tree节点的数组[{ key, title: node.title }]
const dataList = []
const generateList = (data) => {
  for (let i = 0; i < data.length; i++) {
    const node = data[i]
    dataList.push({
      key: node.key,
      title: node.title,
      isLeaf: node.children ? false : true
    })
    if (node.children) {
      generateList(node.children)
    }
  }
}

//为tree生成对应地title slot
const generateTitleSlotScopes = (data) => {
  for (let i = 0; i < data.length; i++) {
    // var info = data[i];
    data[i].scopedSlots = {
      title: 'title',
      // switcherIcon: 'customSwitcherIcon'
    }
    let keys = Object.keys(data[i])
    if (data[i].children) {
      generateTitleSlotScopes(data[i].children)
    }
  }
}

export default {
  name: 'KnowledgeTopicTreeExpand',
  data() {
    return {
      firstTitle: '', //存储搜素tree的第一个title
      inputSearch: '',
      // 树
      treeData: [],
      expandedKeys: [],
      searchValue: '',
      dropTrigger: '',
      selectedKeys: [],
      checkedKeys: [],
      checkStrictly: true
      // curSelectedNode: undefined,
    }
  },
  props: {
    fieldKey: {
      type: String,
      default: 'key',
      required: false
    },
    placeholder: {
      type: String,
      default: '请输入类型名称',
      required: false
    },
    //加载所有资源按钮默认不显示，根据调用需求，设置是否显示
    isShowAllBtn: {
      type: Boolean,
      default: false,
      required: false
    },
    //同isShowAllBtn配合使用，添加最高节点（根节点）
    rootNode: {
      type: Object,
      required: false,
      default: () => {
        let rootNode = {
          id: 'root',
          title: '根节点'
        }
        return rootNode
      }
    },
    //判断节点是否具备可操作的权限值要求，进而控制该节点的disabled属性值，若无权限要求，disabled默认取值为false
    operPermission:{
      type: String,
      default: '',
      required: false
    },
    //树节点是否显示图标
    isShowIcon: {
      type: Boolean,
      default: true,
      required: false
    },
    //是否显示连线
    isShowLine: {
      type: Boolean,
      default: true,
      required: false
    },
    //树节点type值数组，要与后端传的type值一一对应
    arrType: {
      type: Array,
      default: null,
      required: false
    },
    //根据type值，树节点应用不用的icon
    iconName: {
      type: Array,
      default: null,
      required: false
    },
    treeUrl: {
      type: String,
      default: '/assetscategory/assetsCategory/selectTree',
      required: false
    },
    // 加载数据所需传参
    params: {
      type: Object,
      default: () => {
        return {}
      },
      required: false
    },
    //是否显示搜索框
    inputFlag: {
      type: Boolean,
      default: true,
      required: false
    },
    //默认展开的节点
    defaultExpandedKeys: {
      type: Array,
      required: false,
      default: []
    },
    //默认选中的节点
    defaultSelectedKeys: {
      type: Array,
      required: false,
      default: []
    }
  },
  watch: {},
  mounted() {
    this.loadTree()
  },
  methods: {
    setIcon(type) {
      let name = ''
      if (this.arrType && this.arrType.length > 0) {
        for (let i = 0; i < this.arrType.length; i++) {
          if (this.arrType[i] == type) {
            name = this.iconName[i]
            break
          }
        }
      }
      return name
    },
    setNodeDisabled(data){
      for (let i = 0; i < data.length; i++) {
        let keys = Object.keys(data[i])
        let obj=data[i]
        data[i].disabled =this.operPermission&& keys.includes(this.operPermission) ? !obj[this.operPermission] : false
        if (data[i].children) {
          generateTitleSlotScopes(data[i].children)
        }
      }
    },
    // 查询树
    loadTree() {
      var that = this
      that.treeData = []
      getAction(this.treeUrl, this.params).then((res) => {
        if (res.success) {
          let tempTreeData = []
          if (res.result.length > 0) {
            //添加最高的唯一的根节点
            if (this.isShowAllBtn) {
              let temRootNode = JSON.parse(JSON.stringify(this.rootNode))
              temRootNode.children = res.result
              tempTreeData.push(temRootNode)
            }
            //默认展开指定节点
            if (this.defaultExpandedKeys.length > 0) {
              this.expandedKeys = JSON.parse(JSON.stringify(this.defaultExpandedKeys))
            }
            //默认选中节点
            if (this.defaultSelectedKeys.length > 0) {
              this.selectedKeys = JSON.parse(JSON.stringify(this.defaultSelectedKeys))
            }
            generateTitleSlotScopes(tempTreeData)
            that.setNodeDisabled(tempTreeData)
            that.treeData = [...tempTreeData]
          }
          this.loading = false
          generateList(that.treeData)
        }
      })
    },
    //暂时废弃
    onSearch(value) {
      let that = this
      if (value) {
        searchByConfigItemtype({
          typeName: value
        }).then((res) => {
          if (res.success) {
            that.treeData = []
            for (let i = 0; i < res.result.length; i++) {
              let temp = res.result[i]
              that.treeData.push(temp)
            }
          } else {
            that.$message.warning(res.message)
          }
        })
      } else {
        that.loadTree()
      }
    },
    // 右键点击下拉框改变事件
    dropStatus(visible) {
      if (visible == false) {
        this.dropTrigger = ''
      }
    },
    // 选择树的方法
    onSelect(selectedKeys, e) {
      //当前节点被选中，连着第二次点击时，selectedKeys会变为[],有点类似取消选中，
      if (selectedKeys.length > 0) {
        this.selectedKeys = selectedKeys
        this.firstTitle = ''
        let nodeCode = ''
        let nodetype = ''
        if (this.isShowAllBtn && selectedKeys[0] == this.rootNode.key) {
          /*  nodeCode = ''
            nodetype= ''*/
        } else {
          nodeCode = e.selectedNodes[0].data.props.dataRef[this.fieldKey]
          nodetype = e.selectedNodes[0].data.props.dataRef.type
        }
        let title = e.selectedNodes[0].data.props.dataRef.title
        // this.curSelectedNode = e.node
        //向父组件弹射抛值
        this.$emit('selected', nodeCode, nodetype, title)
      }
    },
    //tree的查询框输入时，默认选中匹配的第一个（用firstTitle表示）
    onChange(e) {
      const value = e.target.value
      this.searchValue = value
      //查询框第一个匹配的node对应的key
      let firstSearchedKey = ''
      const expandedKeys = dataList
        .map((item) => {
          if (item.title.indexOf(value) > -1) {
            //查询框第一个匹配的node对应的key
            if (firstSearchedKey == '') {
              firstSearchedKey = item[this.fieldKey]
            }
            return getParentKey(item[this.fieldKey], this.treeData)
          }
          return null
        })
        .filter((item, i, self) => item && self.indexOf(item) === i)
      Object.assign(this, {
        expandedKeys,
        searchValue: value
      })
      if (this.expandedKeys.length > 0 && value.trim().length > 0) {
        this.firstTitle = getTitleByKey(firstSearchedKey, this.treeData)
        this.selectedKeys = [firstSearchedKey]
        //向父组件弹射抛值
        this.$emit('selected', firstSearchedKey)
      }
      if (value.trim().length == 0) {
        //查询设备信息,此情况下，没有分类被选中
        this.firstTitle = ''
        this.selectedKeys = []
        //向父组件弹射抛值
        this.$emit('selected')
      }
    },
    onCheck(checkedKeys, e) {
      // this.checkedKeys = checkedKeys.checked
      // <!---- author:os_chengtgen -- date:20190827 --  for:切换父子勾选模式 =======------>
      if (this.checkStrictly) {
        this.checkedKeys = checkedKeys.checked
      } else {
        this.checkedKeys = checkedKeys
      }
      // <!---- author:os_chengtgen -- date:20190827 --  for:切换父子勾选模式 =======------>
    },
    // 右键操作方法
    rightHandle(node) {
      this.dropTrigger = 'contextmenu'
      this.rightClickSelectedKey = node.node.eventKey
      this.rightClickSelectedBean = node.node.dataRef
    },
    onExpand(expandedKeys) {
      this.expandedKeys = expandedKeys
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/scroll.less';

/*垂直滚动条*/
.vScroll::-webkit-scrollbar {
  margin-right: -24px !important;
}

/*水平滚动条*/
.hScroll::-webkit-scrollbar {
  margin-right: -24px !important;
  margin-left: -24px !important;
}

/*.custom-switcher-icon-noleaf {
  display: inline-block;
  margin-left: 1px;
  width: 12px;
  height: 12px;
  transform: rotate(45deg);
  background: #2E2E2E;
  border: 1px solid rgba(201, 201, 201, 0.47);
}

.custom-switcher-icon-leaf {
  display: inline-block;
  position: absolute;
  content: ' ';
  left: 12px;
  width: 1px;
  //height: 100%;
  margin: 0px 0 0;
  background-color: rgb(201, 201, 201, 0.47);
}*/

/*  ::v-deep .ant-tree.ant-tree-show-line li span.ant-tree-switcher{
    background-color: rgba(255, 255, 255, 0.06) !important;
  }*/

::v-deep .ant-tree, .ant-tree-show-line, .ant-dropdown-trigger {
  li {
    .ant-tree-switcher {
      background-color: rgba(255, 255, 255, 0) !important;
      color: rgba(201, 201, 201, 0.6) !important;
    }

    .ant-tree-node-content-wrapper {
      .ant-tree-title {
        span {
          color: #ffffff !important;

          span {
            color: #ffffff !important;
          }
        }
      }
    }
  }
}

::v-deep .ant-tree.ant-tree-show-line li:not(:last-child)::before {
  border-left: 1px solid rgba(201, 201, 201, 0.6) !important;
}

::v-deep .ant-tree li .ant-tree-node-content-wrapper.ant-tree-node-selected, ::v-deep .ant-tree li .ant-tree-node-content-wrapper:hover {
  background-color: rgba(64, 158, 255, 0.2) !important;
  color: #409EFF !important;

  .ant-tree-title {
    span {
      color: #409EFF !important;

      span {
        color: #409EFF !important;
      }
    }
  }
}
</style>