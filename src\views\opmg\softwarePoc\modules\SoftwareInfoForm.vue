<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container >
      <a-form :form="form" slot="detail" :labelCol="labelCol" :wrapperCol="wrapperCol">
        <a-row>
          <a-col :span="12">
            <a-form-item label="软件名称">
              <a-input v-decorator="['softwareName',validatorRules.softwareName]" placeholder="请输入软件名称" :allowClear="true"
                       autocomplete="off" />
            </a-form-item>
          </a-col>
<!--          <a-col :span="12">
            <a-form-item label="版本">
              <a-input v-decorator="['softwareVersion',validatorRules.softwareVersion]" placeholder="请输入版本(三段式如:1.0.0)"
                       :allowClear="true" autocomplete="off">
              </a-input>
            </a-form-item>
          </a-col>-->
          <a-col :span="12">
            <a-form-item label="软件类别">
              <j-dict-select-tag
                v-decorator="['softwareType',validatorRules.softwareType]" 
                placeholder='请选择软件类别'
                dictCode='softwareCategory'
                :allowClear="true"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="软件简介">
              <a-input v-decorator="['softwareBrief',validatorRules.softwareBrief]" placeholder="请输入软件简介" :allowClear="true"
                       autocomplete="off" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="开发商" >
              <a-input v-decorator="['softwareProducer',validatorRules.softwareProducer]" placeholder="请输入开发商" :allowClear="true"
                       autocomplete="off" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="操作系统" >
              <a-select mode='multiple' v-decorator="['softwareOsType',validatorRules.softwareOsType]" :allowClear="true" placeholder="请选择操作系统">
                <a-select-option v-for="(item, key) in dictOptions" :key="key" :value="item.value">
                  {{ item.text }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="cpu架构" >
              <a-select mode='multiple' v-decorator="['softwareCpuType',validatorRules.softwareCpuType]" :allowClear="true" placeholder="请选择cpu架构">
                <a-select-option v-for="(item, key) in cpuList" :key="key" :value="item.value">
                  {{ item.text }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label='图标' :labelCol='{xs: {span: 24},sm: {span: 4}}' :wrapperCol='{xs: {span: 24},sm: {span: 20}}'>
              <j-upload
                v-decorator="['softwareLogo',validatorRules.softwareLogo]"
                fileType='image'
                :multiple='false'
                :number='1'
                :bizPath='bizPath'
                :size='1024*1024'
                :accept='logoAccept'
                :text='"上传图片\n长宽比1/1"'
              >
              </j-upload>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label='软件截图' :labelCol='{xs: {span: 24},sm: {span: 4}}' :wrapperCol='{xs: {span: 24},sm: {span: 20}}' >
              <j-upload
                v-decorator="['softwareScreenshot',validatorRules.softwareScreenshot]"
                fileType='image'
                :number='3'
                :bizPath='bizPath'
                :size='3*1024*1024'
                :accept='logoAccept'
                :text='"上传图片\n长宽比16/9"'
              >
              </j-upload>
            </a-form-item>
          </a-col>
          <a-col :span="24" >
            <a-form-item label="描述" :labelCol='{xs: {span: 24},sm: {span: 4}}' :wrapperCol='{xs: {span: 24},sm: {span: 20}}'>
              <a-textarea
                :autoSize="{ minRows: 3, maxRows: 6 }"
                v-decorator="['softwareDescribe', validatorRules.softwareDescribe]"
                placeholder="请输入描述"
                :allowClear="true"
                autocomplete="off" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
import {
  httpAction,
  getAction
} from '@/api/manage'
import pick from 'lodash.pick'
import {
  ajaxGetAreaItems,
  ajaxGetDictItems,
  getDictItemsFromCache
} from '@/api/api'
export default {
  name: 'softwareForm',
  props: {
    //类型
    types: {
      type:Array,
      default: () => [],
      required: true
    }
  },
  data() {
    return {
      form: this.$form.createForm(this),
      model: {},
      dictOptions: [],
      cpuList: [],
      effectList:[],
      resourceTypeList: [],
      fildUrl: window._CONFIG['domianURL'],
      labelCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 8
        },
      },
      wrapperCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 16
        },
      },
      bizPath: 'softwarePic',
      confirmLoading: false,
      logoAccept:'.jpg,.png',
      validatorRules: {
        softwareName: {
          rules: [{
            required: true,
            message: '请输入软件名称!'
          },
            {
              max: 10,
              message: '软件名称长度不应超过 10 个字符!'
            }]
        },
        softwareProducer: {
          rules: [{
            required: true,
            message: '请输入开发商!'
          },
            {
              max: 30,
              message: '开发商名称长度不应超过 30 个字符!'
            }]
        },
        softwareBrief: {
          rules: [{
            required: true,
            message: '请输入软件简介!'
          },
            {
              max: 30,
              message: '软件简介长度不应超过 30 个字符!'
            }]
        },
        softwareVersion: {
          rules: [{
            required: true,
            message: '请输入软件版本!'
          },
            {
              // pattern: /(^[1-9][0-9]*$)|(^(([1-9][0-9]*)+\.)+(([1-9][0-9]*)+)$)/,//限制第一位不能为0
              pattern: /(^[0-9]*$)|(^([0-9]+\.)+([0-9]+)$)/, //对第一位没有限制，只要是数字就行
              message: '请输入正确的软件版本,如：xxx或者x.xx.x'
            }
          ]
        },
        softwareType: {
          rules: [{
            required: true,
            message: '请选择软件类别!'
          }]
        },
        softwareOsType: {
          rules: [{
            required: true,
            message: '请选择操作系统!'
          }]
        },
        softwareCpuType: {
          rules: [{
            required: true,
            message: '请选择cpu架构!'
          }]
        },
        softwareLogo: {
          rules: [{
            required: true,
            message: '请上传软件图标!'
          }]
        }, softwareScreenshot: {
          rules: [{
            required: true,
            message: '请上传软件截图!'
          }]
        },
        softwareDescribe:{
          rules: [{
            required: false,
            max:200,
            message: '描述长度不应超过 200 个字符!'
          }]
        },
      },
      url: {
        add: "/software/softwareInfoManage/add",
        edit: "/software/softwareInfoManage/edit",
        queryById: "/software/softwareInfoManage/queryById"
      }
    }
  },
  computed: {
  },
  created() {
  },
  mounted() {
    this.initDictData()
  },
  methods: {
    initDictData() {
      //根据字典Code, 初始化字典数组
      ajaxGetDictItems('cpuArch', null).then((res) => {
        if (res.success) {
          this.cpuList = res.result
        }
      })
      ajaxGetDictItems('os_type', null).then((res) => {
        if (res.success) {
          this.dictOptions = res.result
        }
      })
      ajaxGetDictItems('valid_status', null).then((res) => {
        if (res.success) {
          this.effectList = res.result
        }
      })
      ajaxGetDictItems('resources_type', null).then((res) => {
        if (res.success) {
          this.resourceTypeList = res.result
        }
      })
    },
    add() {
      this.edit({});
    },
    edit(record) {
      this.form.resetFields();
      this.model = Object.assign({}, record);
      this.model.softwareOsType = this.model.softwareOsType.split(',');
      this.model.softwareCpuType = this.model.softwareCpuType.split(',');
      this.visible = true;
      this.$nextTick(() => {
        this.form.setFieldsValue(pick(this.model, 'softwareName','softwareType', 'softwareBrief', 'softwareOsType', 'softwareCpuType','softwareProducer',
          'softwareLogo','softwareScreenshot', 'softwareDescribe'))
      })
    },
    submitForm() {
      const that = this;
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true;
          let httpurl = '';
          let method = '';
          if (!this.model.id) {
            httpurl += this.url.add;
            method = 'post';
          } else {
            httpurl += this.url.edit;
            method = 'put';
          }
          let formData = Object.assign(this.model, values);
          formData.softwareCpuType = formData.softwareCpuType.join();
          formData.softwareOsType = formData.softwareOsType.join();

          httpAction(httpurl, formData, method).then((res) => {
            if (res.success) {
              that.$message.success(res.message);
              that.$emit('ok');
            } else {
              that.$message.warning(res.message);
            }
            that.confirmLoading = false;
          }).catch((err) => {
            that.confirmLoading = false;
            that.$message.warning(err.message);
          })
        }
      })
    },
  }
}
</script>
<style lang="less" scoped >
/deep/ .ant-upload-text {
  white-space: break-spaces;
}
</style>