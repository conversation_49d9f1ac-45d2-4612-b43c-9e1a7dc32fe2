<template>
  <a-card>
    <a-col :span="24" style="margin-bottom: 5px">
      <div style="text-align: right">
        <img src="~@/assets/return1.png" alt="" @click="getGo" style="width: 20px; height: 20px; cursor: pointer" />
      </div>
    </a-col>
    <table class="gridtable">
      <tr>
        <td class="leftTd">工单名称</td>
        <td class="rightTd">{{ data.orderName }}</td>
        <td class="leftTd">故障类型</td>
        <td class="rightTd">{{ data.orderCategoryText }}</td>
      </tr>
      <tr>
        <td class="leftTd">告警确认人</td>
        <td class="rightTd">{{ data.confirmUserName }}</td>
        <td class="leftTd">工单状态</td>
        <td class="rightTd">{{ data.orderStateName }}</td>
      </tr>
      <tr>
        <td class="leftTd">责任人</td>
        <td class="rightTd">{{ data.handlerUserName }}</td>
        <td class="leftTd">故障触发时间</td>
        <td class="rightTd">{{ data.wamingCreateTime }}</td>
      </tr>
      <tr>
        <td class="leftTd">工单创建时间</td>
        <td class="rightTd">{{ data.createTime }}</td>
        <td class="leftTd">工单分配时间</td>
        <td class="rightTd">{{ data.allocTime }}</td>
      </tr>
      <tr>
        <td class="leftTd">响应时长(s)</td>
        <td class="rightTd">{{ data.responseSecondStr }}</td>
        <td class="leftTd">办结时间</td>
        <td class="rightTd">{{ data.handleEndTime }}</td>
      </tr>
      <tr>
        <td class="leftTd">处理时长(s)</td>
        <td class="rightTd">{{ data.handleSecondStr }}</td>
        <td class="leftTd">故障描述</td>
        <td class="rightTd">{{ data.orderDescription }}</td>
      </tr>
      <tr>
        <td class="leftTd">处理结果</td>
        <td class="rightTd" colspan="3">{{ data.handlerResults }}</td>
      </tr>
    </table>
    <a-tabs :animated="false" defaultActiveKey="1">
      <a-tab-pane tab="历史" key="1">
        <History-Info-Form ref="HistoryInfoForm"></History-Info-Form>
      </a-tab-pane>
    </a-tabs>
  </a-card>
</template>
<script>
import JFormContainer from '@/components/jeecg/JFormContainer'
import { httpAction, getAction, postAction } from '@/api/manage'
import HistoryInfoForm from './HistoryInfoForm.vue'
export default {
  // 基本信息
  name: 'OrderInfoForm',
  components: {
    JFormContainer,
    HistoryInfoForm,
  },
  props: {
    data: {
      type: Object,
    },
  },
  data() {
    return {
      /*data: {},*/
      visible: false,
      valueNew: {},
      returnValue: {},
      show: '0',
      urla: window._CONFIG['downloadUrl'] + '/',
    }
  },
  methods: {
    /*edit (record) {
        this.data = record;

      },*/
    //返回上一级
    getGo() {
      this.$parent.pButton2(0)
    },
  },
  mounted() {
    this.$refs.HistoryInfoForm.getDataList(this.data.id)
  },
}
</script>
<style scoped>
table.gridtable {
  font-family: verdana, arial, sans-serif;
  font-size: 14px;
  color: #606266;
  border-width: 1px;
  border-color: #e8e8e8;
  border-collapse: collapse;
  text-align: left;
  width: 100%;
}
table.gridtable td {
  border-width: 1px;
  border-style: solid;
  border-color: #e8e8e8;
}
.leftTd {
  width: 17%;
  background-color: #fafafa;
  padding: 16px 24px;
  text-align: center;
}
.rightTd {
  width: 35%;
  padding: 16px 24px;
  overflow:hidden;
  word-break:break-all
}
#urla {
  width: 100px;
  height: 100px;
}
#urlas {
  width: 100px;
  height: 100px;
}
.orientation {
  width: 100%;
  /*margin: 0 auto;*/
  position: relative;
  overflow: hidden;
}
.orientationFile {
  width: 100%;
  /*margin: 0 auto;*/
  position: relative;
  overflow: hidden;
}
.font {
  position: absolute;
  bottom: 0;
  background: rgba(0, 0, 0, 0.75);
  left: 0;
  width: 100%;
  height: 30%;
  color: #fff;
  line-height: 32px;
  cursor: pointer;
  transform: translateY(109%);
  transition: all 0.3s ease-out 0s;
  text-align: center;
}
.font1 {
  position: absolute;
  bottom: 0;
  background: rgba(0, 0, 0, 0.75);
  left: 0;
  width: 100%;
  height: 30%;
  color: #fff;
  line-height: 32px;
  cursor: pointer;
  transform: translateY(109%);
  transition: all 0.3s ease-out 0s;
  text-align: center;
}
.orientation:hover .font {
  transform: translateY(0%);
}
.orientationFile:hover .font1 {
  transform: translateY(0%);
}
</style>