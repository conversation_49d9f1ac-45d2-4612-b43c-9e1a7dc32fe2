<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-item label="类型名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                v-decorator="['typeName', validatorRules.typeName]"
                placeholder="请输入类型名称"
                autocomplete="off"
                :allowClear="true"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="排序" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number v-decorator="['sort',validatorRules.sort]" placeholder="请输入排序"></a-input-number>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="描述" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <!-- <a-input v-decorator="['remarks']" placeholder="请输入备注信息"></a-input> -->
              <a-textarea
                v-decorator="['describes',validatorRules.describes]"
                placeholder="请输入描述"
                :auto-size="{ minRows: 3, maxRows: 10 }"
                autocomplete="off"
                :allowClear="true"
              />
            </a-form-item>
          </a-col>
          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import pick from 'lodash.pick'
import { validateDuplicateValue } from '@/utils/util'
import JFormContainer from '@/components/jeecg/JFormContainer'

export default {
  name: 'ItilStockInfoForm',
  components: {
    JFormContainer,
  },
  props: {
    //流程表单data
    formData: {
      type: Object,
      default: () => {},
      required: false,
    },
    //表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false,
    },
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false,
    },
  },
  data() {
    return {
      form: this.$form.createForm(this),
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      confirmLoading: false,
      url: {
        add: '/itilDepotType/itilDepotType/add',
        edit: '/itilDepotType/itilDepotType/edit',
        queryById: '/itilStockInfo/itilStockInfo/queryById',
      },
      code: '',
      replaceFields: {
        key: 'id',
        value: 'id',
        title: 'typeName',
      },
      treeData: [],
      pid: '',
      type: '',
      validatorRules: {
        typeName: {
          rules: [
            { required: true, message: '请输入类型名称' }
          ]
        },
        sort: {
          rules: [
            { required: true, message: '请输入排序号' },
            { pattern: /^[1-9]{1}[0-9]*$/, message: '请输入大于0的整数' }
          ]
        },
        describes: {
          rules: [
            { required: false, message: '' },
            { minRows: 3, maxRows: 10, message: '行数在3~10行内' },
          ]
        },
      },
    }
  },
  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    },
  },
  created() {
    //如果是流程中表单，则需要加载流程表单data
    this.showFlowData()
  },
  methods: {
    add(id) {
      this.getTreeData()
      this.edit({})
      this.pid = id
      this.type = 'add'
    },
    edit(record) {
      this.type = 'edit'
      this.form.resetFields()
      this.pid = record
      getAction('/itilDepotType/itilDepotType/queryById', { id: record }).then((res) => {
        if (res.success) {
          this.model = Object.assign({}, res.result)
          this.visible = true
          this.$nextTick(() => {
            this.form.setFieldsValue(pick(this.model, 'typeName', 'sort', 'describes'))
          })
        }
      })
      // this.model = Object.assign({}, record)
    },
    //渲染流程表单数据
    showFlowData() {
      if (this.formBpm === true) {
        let params = { id: this.formData.dataId }
        getAction(this.url.queryById, params).then((res) => {
          if (res.success) {
            this.edit(res.result)
          }
        })
      }
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        let formData = Object.assign(this.model, values)
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let formData = Object.assign(this.model, values)
          let data = {}
          if (this.type == 'edit') {
            data.id = this.pid
            data.typeName = formData.typeName
            data.describes = formData.describes
            data.sort = formData.sort
          } else if (this.type == 'add') {
            data.pid = this.pid
            data.typeName = formData.typeName
            data.describes = formData.describes
            data.sort = formData.sort
          }
          // let data = {
          //   id: this.pid,
          //   typeName: formData.typeName,
          //   describes: formData.describes,
          //   sort: formData.sort,
          // }
          httpAction(httpurl, data, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },

    getTreeData() {
      this.treeData = []
      getAction('/itilStockInfo/itilStockInfo/tree').then((res) => {
        if (res.success) {
          this.treeData.push(res.result)
        }
      })
    },
    onLoadData(treeNode) {
      return new Promise((resolve) => {
        if (treeNode.dataRef.children) {
          resolve()
          return
        }
        let param = {
          id: treeNode.$vnode.key,
        }
        getAction('/itilStockInfo/itilStockInfo/tree', param).then((res) => {
          if (res.success) {
            treeNode.dataRef.children = [...res.result]
            // this.treeData = [...res.result]
          }
          resolve()
        })
      })
    },
  },
}
</script>