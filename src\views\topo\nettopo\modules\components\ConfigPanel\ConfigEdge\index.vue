<template>
  <a-tabs v-if="curCell" :animated="false" v-model="activeKey">
    <a-tab-pane tab="连线配置" key="1">
      <a-row align="middle">
        <a-col :span="8">连线宽度</a-col>
        <a-col :span="16">
          <a-input-number
            style="width: 100%"
            v-model="edgeConfig.strokeWidth"
            :min="1"
            :max="5"
            @change="onStrokeWidthChange"
          />
        </a-col>
      </a-row>
      <a-row align="middle" v-if="isConnecting">
        <a-col :span="8">连线箭头</a-col>
        <a-col :span="16">
          <a-select style="width: 100%" v-model="edgeConfig.edgeMarker" @change="onEdgeMarkerChange">
            <a-select-option :value="idx" v-for="(arrow, idx) in globalGridAttr.edgeMarkerArr" :key="idx">
              {{ arrow }}
            </a-select-option>
          </a-select>
        </a-col>
      </a-row>
      <a-row align="middle" v-if="isConnecting">
        <a-col :span="8">连线类型</a-col>
        <a-col :span="16">
          <a-select style="width: 100%" v-model="edgeConfig.edgeType" @change="onEdgeTypeChange">
            <a-select-option :value="idx" v-for="(item, idx) in globalGridAttr.edgeTypeArr" :key="idx">{{
              item
            }}</a-select-option>
          </a-select>
        </a-col>
      </a-row>
      <a-row align="middle">
        <a-col :span="8">连线样式</a-col>
        <a-col :span="16">
          <a-select style="width: 100%" v-model="edgeConfig.edgeDash" @change="onEdgeDashChange">
            <a-select-option :value="dash" v-for="(dash, idx) in globalGridAttr.edgeDashArr" :key="idx">
              <line-dash :dash-type="dash"></line-dash>
            </a-select-option>
          </a-select>
        </a-col>
      </a-row>
       <div v-if="isConnecting">
      <a-row align="middle">
        <a-col :span="8">连线属性</a-col>
        <a-col :span="16">
          <a-select
            mode="multiple"
            style="width: 100%"
            allowClear
            :value="edgeConfig.edgeProperties"
            @change="onPropertiesChange"
          >
            <a-select-option :value="item.value" v-for="item in globalGridAttr.edgeProperties" :key="item.value">
              {{item.text}}
            </a-select-option>
          </a-select>
        </a-col>
      </a-row>
     
        <a-row align="middle">
          <a-col :span="8">连线标签</a-col>
          <a-col :span="16">
            <a-input v-model="edgeConfig.label" :disabled="size > 1" style="width: 100%" @change="onLabelChange('lable')" />
          </a-col>
        </a-row>
        <a-row align="middle">
          <a-col :span="8">标签文字大小</a-col>
          <a-col :span="16">
            <a-input-number
              style="width: 100%"
              v-model="edgeConfig.labelSize"
              :min="8"
              :max="36"
              @change="onLabelChange('size')"
            />
          </a-col>
        </a-row>
        <a-row align="middle">
          <a-col :span="8">标签文字颜色</a-col>
          <a-col :span="16">
             <Shader v-model="edgeConfig.labelColor" @change="onLabelChange('color')"></Shader>
          </a-col>
        </a-row>
        <a-row align="middle">
          <a-col :span="8">标签背景颜色</a-col>
          <a-col :span="16">
              <Shader v-model="edgeConfig.labelBg" @change="onLabelChange('bgColor')"></Shader>
          </a-col>
        </a-row>
      </div>
      <a-row align="middle" v-if="edgeConfig.edgeDash !=='0'">
        <a-col :span="6">虚线流动</a-col>
        <a-col :span="18">
          <a-switch checkedChildren="开" unCheckedChildren="关"  v-model="edgeConfig.flow" @change='onFlowChange' />
        </a-col>
      </a-row>
      <div v-if="showPos">
        <a-row align="middle">
          <a-col :span="8">连线颜色</a-col>
          <a-col :span="16">
             <Shader v-model="edgeConfig.strokeColor" @change="onStrokeChange"></Shader>
          </a-col>
        </a-row>
        <div>
          <a-row align="middle">
            <a-col :span="8">起始位置:</a-col>
          </a-row>
          <a-row align="middle" style="width: 100%">
            <a-col :span="24">
              <a-input
                type="number"
                :addonBefore="'X'"
                style="width: calc(50% - 5px); margin-right: 10px"
                v-model="edgeConfig.startPos.x"
                @change="onPositionChange($event, 'x')"
              />
              <a-input
                type="number"
                :addonBefore="'Y'"
                style="width: calc(50% - 5px)"
                v-model="edgeConfig.startPos.y"
                @change="onPositionChange($event, 'y')"
              />
            </a-col>
          </a-row>
        </div>
        <div>
          <a-row align="middle">
            <a-col :span="8">结束位置:</a-col>
          </a-row>
          <a-row align="middle" style="width: 100%">
            <a-col :span="24">
              <a-input
                type="number"
                :addonBefore="'X'"
                style="width: calc(50% - 5px); margin-right: 10px"
                v-model="edgeConfig.endPos.x"
                @change="onPositionChange($event, 'x')"
              />
              <a-input
                type="number"
                :addonBefore="'Y'"
                style="width: calc(50% - 5px)"
                v-model="edgeConfig.endPos.y"
                @change="onPositionChange($event, 'y')"
              />
            </a-col>
          </a-row>
        </div>
      </div>
    </a-tab-pane>
    <a-tab-pane tab="业务属性" key="2" v-if="size === 1 && isConnecting">
      <a-row align="middle">
        <a-col :span="8">from</a-col>
        <a-col :span="16">
          <a-input v-model="infoData.fromDeviceName" style="width: 100%" disabled />
        </a-col>
      </a-row>
      <a-row align="middle">
        <a-col :span="8"></a-col>
        <a-col :span="16">
          <div class="swap-btn">
            <span @click="swapClick"
              ><a-icon type="swap" style="font-size: 32px; color: #1890ff; transform: rotate(90deg)"
            /></span></div
        ></a-col>
      </a-row>

      <a-row align="middle">
        <a-col :span="8">to</a-col>
        <a-col :span="16">
          <a-input v-model="infoData.toDeviceName" style="width: 100%" disabled />
        </a-col>
      </a-row>
      <a-row align="middle">
        <a-col :span="8">起始端口</a-col>
        <a-col :span="16">
          <a-select style="width: 100%" v-model="infoData.fromPort" @change="onFromPortChange">
            <a-select-option :value='item.value'  v-for="(item, idx) in fromPorts" :key="'from_'+idx">
              {{ item.label }}
            </a-select-option>
          </a-select>
        </a-col>
      </a-row>
      <a-row align="middle">
        <a-col :span="8">目标端口</a-col>
        <a-col :span="16">
          <a-select style="width: 100%" v-model="infoData.toPort" @change="onToPortChange">
            <a-select-option :value='item.value'  v-for="(item, idx) in toPorts" :key="'to_'+idx">{{ item.label }}</a-select-option>
          </a-select>
        </a-col>
      </a-row>
      <a-row align="middle">
        <a-col :span="8">连接类型</a-col>
        <a-col :span="16">
          <a-select style="width: 100%" v-model="infoData.ljlx" @change="onLjlxChange">
            <a-select-option :value='item.value'  v-for="(item, idx) in ljlxArr" :key="'ljlx_'+idx">{{ item.label }}</a-select-option>
          </a-select>
        </a-col>
      </a-row>
    </a-tab-pane>
  </a-tabs>
</template>

<script>
import FlowGraph from '../../../graph'
import LineDash from '../LineDash.vue'
import {getAction} from '@api/manage'
import Shader from '@/components/shader/shader.vue';
export default {
  name: 'Index',
  components: {
    LineDash,
    Shader,
  },
  props: {
    globalGridAttr: {
      type: Object,
      default: null,
      required: true,
    },
    id: {
      type: String,
      default: '',
    },
    size: {
      type: Number,
      default: 0,
      required: true,
    },
  },
  data() {
    return {
      curCell: null,
      markerArr: [
        //箭头类型控制数据
        ['', ''],
        ['classic', ''],
        ['', 'classic'],
        ['classic', 'classic'],
      ],
      typeArr: [
        //连线类型控制数据
        { c: 'normal', r: 'normal' },
        { c: 'normal', r: 'manhattan' },
        { c: 'smooth', r: 'normal' },
        // { c: 'multi-smooth', r: 'normal' },
      ],
      edgeConfig: {
        strokeWidth: 2,
        edgeMarker: 0,
        edgeType: 0,
        edgeDash: '0',
        label: '',
        labelSize: 14,
        labelColor: '#000000',
        labelBg: '#e5e5e5',
        startPos: { x: 0, y: 0 },
        endPos: { x: 0, y: 0 },
        strokeColor: '#000',
        edgeProperties:[],
        flow:false,
      },
      infoData: {
        //业务属性数据
        fromDevice: '',
        fromDeviceName: '',
        fromPort: '',
        toDevice: '',
        toDeviceName: '',
        toPort: '',
        ljlx:1,
      },
      activeKey: '1',
      toPorts: [],
      fromPorts: [],
      sourceNode: null,
      targetNode: null,
      selectEdges: [],
      cellType: '',
      endDragNode: null,
      startDragNode: null,
      ljlxArr:[
        {label:'单链路',value:1},
        {label:'双链路',value:2},
      ]
    }
  },
  created() {},
  watch: {
    size: {
      immediate: true,
      handler() {
        const { graph } = FlowGraph
        this.selectEdges = graph.getSelectedCells()
        if (this.selectEdges.length > 0) {
          this.curCell = this.selectEdges[this.selectEdges.length - 1]
          this.cellType = this.curCell.data.edgeType
          this.activeKey = '1'
          this.initEdgeConfig()
          if (this.isConnecting && this.size === 1) {
            this.initEdgeInfo()
          }

          // console.log('有选中连线 === ', this.size, this.curCell)
        }
      },
    },
  },
  computed: {
    isConnecting() {
      let temEdge = this.selectEdges.find((el) => el.data.edgeType !== 'connecting')
      return temEdge === undefined
    },
    showPos() {
      let temEdge = this.selectEdges.find((el) => el.data.edgeType === 'connecting')
      return this.size === 1 && temEdge === undefined
    },
  },
  beforeDestroy() {
    this.removeDragNode()
  },
  methods: {
    createDragNode(point, type) {
      const { graph } = FlowGraph
      let node = graph.createNode({
        shape: 'edgeDragNode',
        width: 10,
        height: 10,
        x: point.x - 5,
        y: point.y - 5,
        zIndex: 1000,
        data: {
          dragType: type,
          nodeType: 'edgeDrag',
        },
      })
      return graph.addNode(node)
    },
    removeDragNode() {
      const { graph } = FlowGraph
      if (this.endDragNode) {
        this.endDragNode.off('change:position')
        graph.removeNode(this.endDragNode)
      }
      if (this.startDragNode) {
        this.startDragNode.off('change:position')
        graph.removeNode(this.startDragNode)
      }
    },
    //初始化连线配置
    initEdgeConfig() {
      this.edgeConfig.strokeWidth = this.curCell.attr('line/strokeWidth')
      let s = this.curCell.attr('line/sourceMarker/name') || ""
      let t = this.curCell.attr('line/targetMarker/name') || ""
      this.edgeConfig.edgeMarker = this.markerArr.findIndex((el) => el[0] === s && el[1] === t)
      let router = this.curCell.getRouter()
      let connector = this.curCell.getConnector()
      this.edgeConfig.edgeType = this.typeArr.findIndex((el) => el.r === router.name && el.c === connector.name)
      this.edgeConfig.edgeDash = this.curCell.attr('line/strokeDasharray')
      this.edgeConfig.flow = this.curCell.data.flow;
      // 标签
      let labels = this.curCell.getLabels()[0]
      if (labels) {
        if(this.size ===1){
           this.edgeConfig.label = labels.attrs.text.text
        }
        else{
           this.edgeConfig.label = ""
        }
        this.edgeConfig.labelSize = labels.attrs.text.fontSize
        this.edgeConfig.labelColor = labels.attrs.text.fill
        this.edgeConfig.labelBg = labels.attrs.rect.fill
      } else {
        this.edgeConfig.label = ''
        this.edgeConfig.labelSize = 14
        this.edgeConfig.labelColor = this.globalGridAttr.topoConfig.labelColor
        this.edgeConfig.labelBg = '#e5e5e5'
      }
      //画线的位置 和 拖拽节点
      if (this.showPos) {
        this.edgeConfig.strokeColor = this.curCell.attr('line/stroke')
        let startPos = this.curCell.getSourcePoint()
        let endPos = this.curCell.getTargetPoint()
        this.edgeConfig.startPos.x = Math.floor(startPos.x)
        this.edgeConfig.startPos.y = Math.floor(startPos.y)
        this.edgeConfig.endPos.x = Math.floor(endPos.x)
        this.edgeConfig.endPos.y = Math.floor(endPos.y)
        this.endDragNode = this.createDragNode(this.edgeConfig.endPos, 'end')
        this.startDragNode = this.createDragNode(this.edgeConfig.startPos, 'start')
        this.endDragNode.on('change:position', ({current}) => {
          this.edgeConfig.endPos.x = current.x+5;
          this.edgeConfig.endPos.y = current.y+5;
           this.curCell.setTarget(this.edgeConfig.endPos);
        })
        this.startDragNode.on('change:position', ({current}) => {
           this.edgeConfig.startPos.x = current.x+5;
          this.edgeConfig.startPos.y = current.y+5;
           this.curCell.setSource(this.edgeConfig.startPos);
        })
      } else {
        this.removeDragNode()
      }
    },
    //设置连线的业务属性
    initEdgeInfo(isSwap) {
      this.sourceNode = this.curCell.getSourceNode()
      this.targetNode = this.curCell.getTargetNode()
      let data = this.curCell.data
      if(data.edgeProperties === undefined){
        data.edgeProperties = this.globalGridAttr.topoConfig.edgeProperties;
        this.edgeConfig.edgeProperties =  data.edgeProperties;
      }else{
        this.edgeConfig.edgeProperties =  data.edgeProperties;
      }
      if (!data.fromDevice || !data.toDevice) {
        data.fromDevice = this.sourceNode.data.deviceCode || this.sourceNode.data.deviceId
        data.fromDeviceName = this.sourceNode.attr('label/text')
        data.toDevice = this.targetNode.data.deviceCode || this.targetNode.data.deviceId
        data.toDeviceName = this.targetNode.attr('label/text')
      }
      Object.assign(this.infoData, data)
      if(!isSwap){
        this.getPanleInfo(this.sourceNode, this.targetNode)
      }
      // console.log('初始化连线信息 == ', data)
    },
    //获取目标节点和起始节点面板信息
    getPanleInfo(source,target) {
      let p1 = getAction("/product/panelInfo/queryByProductId", { productId: source.data.productId });
      let p2 = getAction("/product/panelInfo/queryByProductId", { productId: target.data.productId });
      Promise.all([p1, p2]).then((res) => {
        if(res[0].success &&  res[0].result &&  res[0].result.nodeList){
            this.fromPorts = res[0].result.nodeList.filter(el=>el.nodeShape === 'panel-element').map(el=>{
              let attrs = JSON.parse(el.nodeAttrs);
              let data = JSON.parse(el.nodeData);
              return {
                label:data.portDesc || attrs?.label.text,
                value: el.id,
              }
            })
        }
        if(res[1].success &&  res[1].result &&  res[1].result.nodeList){
            this.toPorts = res[1].result.nodeList.filter(el=>el.nodeShape === 'panel-element').map(el=>{
              let attrs = JSON.parse(el.nodeAttrs);
              let data = JSON.parse(el.nodeData);
              return {
                label:data.portDesc || attrs?.label.text,
                value: el.id,
              }
            })
        }

      })
    },
    // 转换业务数据
    swapClick() {
      let source = this.curCell.getSource();
      let target = this.curCell.getTarget();
      this.curCell.setSource(target);
      this.curCell.setTarget(source);
      this.curCell.data.fromDevice = "";
      this.curCell.data.toDevice = "";
      let temData = this.fromPorts;
      this.fromPorts = this.toPorts;
      this.toPorts = temData
      let temPort =  this.curCell.data.fromPort
      this.curCell.data.fromPort = this.curCell.data.toPort;
      this.curCell.data.toPort = temPort;
      this.initEdgeInfo(true)
      // console.log("转换数据了",this.curCell.data,this.infoData)
    },
    //连线粗细变化
    onStrokeWidthChange(val) {
      this.edgeConfig.strokeWidth = val
      this.selectEdges.forEach((el) => {
        el.attr('line/strokeWidth', val)
      })
    },
    //监听链接类型变化
    onLjlxChange(val) {
      this.selectEdges.forEach((el) => {
        el.data.ljlx = val;
        this.infoData.ljlx = val
      })
    },
    //监听目标端口变化
    onToPortChange(val) {
      // console.log("监听目标端口变化 == ",val)
      this.selectEdges.forEach((el) => {
        el.data.toPort = val;
        this.infoData.toPort = val;
      })
    },
    //监听起始端口变化
    onFromPortChange(val) {
      // console.log("监听起始端口变化 == ",val)
      this.selectEdges.forEach((el) => {
        el.data.fromPort = val;
        this.infoData.fromPort = val;
      })
    },
    //连线箭头变化
    onEdgeMarkerChange(val) {
      let markerVal = this.markerArr[val]
      this.selectEdges.forEach((el) => {
        markerVal[0]? el.attr('line/sourceMarker/name', markerVal[0]): el.attr('line/sourceMarker', "")
        markerVal[1]?el.attr('line/targetMarker/name', markerVal[1]):el.attr('line/targetMarker',"")
      })
    },
    //连线类型变化
    onEdgeTypeChange(val) {
      let typeVal = this.typeArr[val]
      this.selectEdges.forEach((el) => {
        el.setConnector({ name: typeVal.c })
        el.setRouter({ name: typeVal.r })
      })
    },
    //连线样式变化
    onEdgeDashChange(val) {
      this.selectEdges.forEach((el) => {
        el.attr('line/strokeDasharray', val)
        if(val === "0"){
          el.attr('line/style/animation', '')
          el.data.flow = false;
          this.edgeConfig.flow = false;
        }else if(this.edgeConfig.flow){
          el.data.flow = true;
          el.attr('line/style/animation', 'yq-topo-dash-line 30s infinite linear')
        }

      })
    },
    //虚线流动动画
    onFlowChange(val){
      this.selectEdges.forEach((el) => {
        el.data.flow = val;
        el.attr('line/style/animation', val?'yq-topo-dash-line 30s infinite linear':'')
      })
    },
    //联系配置
    onPropertiesChange(val) {
      this.edgeConfig.edgeProperties = val;
      this.curCell.data.edgeProperties = val;
    },
    //连线颜色变化
    onStrokeChange(val) {
      this.selectEdges.forEach((el) => {
        el.attr('line/stroke', this.edgeConfig.strokeColor)
      })
    },
    //连线起始位置
    onPositionChange() {
      this.selectEdges.forEach((el) => {
        this.startDragNode.position(Number(this.edgeConfig.startPos.x)-5,Number(this.edgeConfig.startPos.y)-5)
        this.endDragNode.position(Number(this.edgeConfig.endPos.x)-5,Number(this.edgeConfig.endPos.y)-5)
        el.setSource({ x: Number(this.edgeConfig.startPos.x), y: Number(this.edgeConfig.startPos.y) })
        el.setTarget({ x: Number(this.edgeConfig.endPos.x), y: Number(this.edgeConfig.endPos.y) })
      })
    },
    //连线标签变化
    onLabelChange(type) {
      this.selectEdges.forEach((el) => {
        let labels = el.getLabelAt(0);
        let labelColor = this.edgeConfig.labelColor;
        let label = this.edgeConfig.label;
        let labelSize = this.edgeConfig.labelSize;
        let labelBg = this.edgeConfig.labelBg;
        if(labels){
          let attr = labels.attrs;
          if(type !== "bgColor"){
             labelBg = attr.rect.fill;
          }
          if(type !== "color"){
             labelColor = attr.text.fill;
          }
           if(type !== "lable"){
            label = attr.text.text;
           }
           if(type !== "size"){
            labelSize = attr.text.fontSize
           }
          
          
        }

        // console.log("节点的labels == ",labels)
        el.setLabels([
          {
            attrs: {
              text: {
                fill: labelColor,
                text: label,
                fontSize: labelSize,
                opacity: 1,
              },
              rect: {
                fill: labelBg,
                opacity: 1,
              },
            },
            position: {
              distance: 0.5,
            },
          },
        ])
      })
    },
  },
}
</script>

<style lang="less" scoped>
.swap-btn {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>