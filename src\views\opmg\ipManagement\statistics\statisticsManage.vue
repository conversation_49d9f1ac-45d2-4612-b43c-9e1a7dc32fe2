<template>
  <a-row style="background: #f0f2f5; height: 100%; display: flex">
    <a-col class="listTree">
      <div class='tree-box'>
        <a-spin :spinning="loading" style="height: 100%">
          <a-tree v-if='treeData&&treeData.length>0' :tree-data="treeData" @select="onSelect"
                  :replace-fields="{children:'children', title:'title', key:'id'}"
                  :default-expanded-keys="[this.treeData[0].id]" :default-selected-keys="[this.treeData[0].id]">
          </a-tree>
          <div v-else style="height: 100%;display: flex;align-items: center;justify-content: center;">
            <a-empty></a-empty>
          </div>
        </a-spin>
      </div>
    </a-col>
    <a-col class="listCont">
      <div v-if="breadPath" class="title">
        {{ breadPath }}
      </div>
      <div :class="[breadPath?'has-title':'no-title']">
        <statistics-list  ref="statisticsList" :treeFlag="treeFlag">
        </statistics-list>
      </div>
    </a-col>
  </a-row>
</template>
<script>
  import statisticsList from './statisticsList'
  import {
    getAction
  } from '@/api/manage'
  export default {
    name: 'statisticsManage',
    components: {
      statisticsList
    },
    data() {
      return {
        treeFlag: {},
        treeData: [],
        loading:false,
        breadPath:'',
        url: {
          querySegmentList: '/devops/ip/analysis/querySegmentList'
        }
      }
    },
    created() {
      this.getList()
    },
    methods: {
      onSelect(value, e) {
        let node = e.selectedNodes[0].data.props
        this.treeFlag = {
          id: node.id,
          type: node.type,
          title: node.title,
        }
        let bread=[]
        this.breadPath=''
        this.findNodeAndParents(this.treeData,node.id,bread)
        for (let i=0;i<bread.length;i++){
          this.breadPath+=bread[i].title
          if (i!=bread.length-1){
            this.breadPath+="/"
          }
        }
      },
      // 辅助递归函数
      findNodeAndParents(list, nodeId, breadcrumb) {
        for (let i = 0; i < list.length; i++) {
          let currentNode = list[i];

          if (currentNode.id === nodeId) {
            breadcrumb.unshift({ id: currentNode.id, title: currentNode.title });
            return true;
          }

          if (currentNode.children && currentNode.children.length > 0) {
            let found = this.findNodeAndParents(currentNode.children, nodeId, breadcrumb);

            if (found) {
              breadcrumb.unshift({ id: currentNode.id, title: currentNode.title });
              return true;
            }
          }
        }
      },
      getList() {
        this.loading=true
        this.treeData=[]
        this.treeFlag={}
        getAction(this.url.querySegmentList).then((res) => {
          if (res.success&&res.result&&res.result.length>0){
            this.treeData = res.result
            this.treeFlag = {
              id: this.treeData[0].id,
              type: this.treeData[0].type,
              title: this.treeData[0].title,
            }
            this.breadPath=this.treeData[0].title
          }else {
            this.treeData=[]
            this.treeFlag={}
          }
          this.loading=false
        }).catch((err)=>{
          this.$message.error(err.message)
          this.loading = false
          this.treeData=[]
          this.treeFlag={}
        })
      }
    }
  }
</script>
<style scoped lang="less">
  .listTree {
    width: 300px;
    height: 100%;
    background-color: #ffffff;
    padding: 14px 24px 14px 10px;
    overflow: hidden;

    .tree-box {
      height: 100%;
      overflow-x: auto;
      white-space: nowrap;
    }
  }

  .listCont {
    width: calc(100% - 300px - 16px);
    margin-left: 16px;
    height: 100%;
    overflow: auto;
    //overflow-y: auto;
    //min-width: 992px;
  }
  .title{
    min-width: 800px;
    background-color: #fff;
    border-radius: 3px;
    padding: 24px;
    margin-bottom: 16px;
    font-size: 18px;
    color:#000000
  }
  .has-title{
    min-width: 800px;
    height: calc(100% - 75px - 16px)
  }
  .no-title{
    min-width: 800px;
    height: 100%;
  }
  ::v-deep .ant-spin-container{
    height: 100%;
  }
</style>