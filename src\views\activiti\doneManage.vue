<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll zxw">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <!-- 查询区域 -->
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="任务名称" prop="name">
                  <a-input
                    type="text"
                    v-model="searchForm.name"
                    :allowClear="true"
                    autocomplete="off"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="colBtnsSpan()">
                <span
                  class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                >
                  <a-button type="primary" @click="searchQuery" class="btn-search-style">查询</a-button>
                  <a-button @click="searchReset" class="btn-reset-style">重置</a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card style="flex: auto" class="core">
        <a-row>
          <a-table
            bordered
            :loading="loading"
            rowKey="id"
            :dataSource="data"
            :pagination="ipagination"
            @change="handleTableChange"
            ref="table"
          >
            <a-table-column title="序号" :width="70" align="center">
              <template slot-scope="t, r, i">
                <span> {{ i + 1 }} </span>
              </template>
            </a-table-column>
            <a-table-column title="任务名称" dataIndex="name" :width="150" align="center">
              <template slot-scope="t, r, i">
                <span> {{ t }} </span>
              </template>
            </a-table-column>
            <a-table-column title="所属流程" dataIndex="processName" :width="150" align="center">
              <template slot-scope="t, r, i">
                <span> {{ t }} </span>
              </template>
            </a-table-column>
            <a-table-column title="委托代办人" dataIndex="owner" :width="130" align="center">
              <template slot-scope="t, r, i">
                <span> {{ t }} </span>
              </template>
            </a-table-column>
            <a-table-column title="流程发起人" dataIndex="applyer" :width="130" align="center">
              <template slot-scope="t, r, i">
                <span> {{ t }} </span>
              </template>
            </a-table-column>
            <a-table-column
              title="优先级"
              dataIndex="priority"
              :width="100"
              align="center"
              key="priority"
              :sorter="(a, b) => a.priority - b.priority"
            >
              <template slot-scope="t">
                <span v-if="t == 0" style="color: #ffb300"> 低 </span>
                <span v-else-if="t == 1" style="color: #fc7611"> 中 </span>
                <span v-else-if="t == 2" style="color: #df1a1a"> 高 </span>
                <span v-else> 无 </span>
              </template>
            </a-table-column>
            <a-table-column title="审批操作" dataIndex="deleteReason" :width="100" align="center">
              <template slot-scope="t">
                <span> {{ t }} </span>
              </template>
            </a-table-column>
            <a-table-column title="审批意见" dataIndex="comment" :width="100" align="center">
              <template slot-scope="t">
                <j-ellipsis :value="t" :length="10" />
              </template>
            </a-table-column>
            <a-table-column
              title="耗时"
              dataIndex="duration"
              :width="100"
              align="center"
              key="duration"
              :sorter="(a, b) => a.duration - b.duration"
            >
              <template slot-scope="t">
                <span> {{ millsToTime(t) }} </span>
              </template>
            </a-table-column>
            <a-table-column title="创建时间" dataIndex="createTime" :width="200" align="center">
              <template slot-scope="t">
                <span> {{ t }} </span>
              </template>
            </a-table-column>
            <a-table-column title="操作" dataIndex="action" :width="200" align="center" class="caozuo">
              <template slot-scope="t, r, i">
                <a href="javascript:void(0);" @click="detail(r)">表单数据</a>
                <a-divider type="vertical" />
                <a href="javascript:void(0);" @click="history(r)">审批历史</a>
                <a-divider type="vertical" />
                <a-popconfirm title="确定删除吗?" @confirm="() => remove(r)">
                  <a>删除</a>
                </a-popconfirm>
              </template>
            </a-table-column>
            <template slot="tooltip" slot-scope="text">
              <a-tooltip placement="topLeft" :title="text" trigger="hover">
                <div class="tooltip">
                  {{ text }}
                </div>
              </a-tooltip>
            </template>
          </a-table>
        </a-row>
      </a-card>
      <!---->
      <a-modal title="审批历史" v-model="modalLsVisible" :mask-closable="false" :width="'80%'" :footer="null">
        <div v-if="modalLsVisible">
          <component :is="historicDetail" :procInstId="procInstId"></component>
        </div>
      </a-modal>
      <!--流程表单-->
      <a-modal :title="lcModa.title" v-model="lcModa.visible" :footer="null" :maskClosable="false" width="80%">
        <component
          :disabled="lcModa.disabled"
          v-if="lcModa.visible"
          :is="lcModa.formComponent"
          :processData="lcModa.processData"
          :isNew="lcModa.isNew"
          @close=";(lcModa.visible = false), (lcModa.disabled = false)"
        ></component>
      </a-modal>
      <!--流程详情-->
      <start-process ref="startCode"></start-process>
    </a-col>
  </a-row>
</template>

<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { activitiMixin } from '@/views/activiti/mixins/activitiMixin'
import { getAction, deleteAction, putAction, postAction } from '@/api/manage'
import StartProcess from '../../components/activiti/StartProcess'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
export default {
  name: 'done-manage',
  mixins: [activitiMixin, JeecgListMixin, YqFormSearchLocation],
  components: { StartProcess },
  data() {
    return {
      scroll: { x: '120%', y: '100%' },
      openSearch: true,
      openTip: true,
      loading: true, // 表单加载状态
      selectCount: 0, // 多选计数
      selectList: [], // 多选数据
      searchForm: {
        // 搜索框对应data对象
        name: '',
        pageNumber: 1, // 当前页数
        pageSize: 10, // 页面大小
        sort: 'createTime', // 默认排序字段
        order: 'desc', // 默认排序方式
      },
      modalType: 0, // 添加或编辑标识
      modalVisible: false, // 添加或编辑显示
      modalTitle: '', // 添加或编辑标题
      form: {
        // 添加或编辑表单对象初始化数据
        reason: '',
      },
      formValidate: {
        // 表单验证规则
      },
      submitLoading: false, // 添加或编辑提交状态
      data: [], // 表数据
      total: 0, // 表数据总数
      deleteId: '',
      url: {
        doneList: '/actTask/doneList',
        deleteHistoricTask: '/actTask/deleteHistoric/',
      },
      modalLsVisible: false,
      procInstId: '',
      lcModa: {
        title: '',
        disabled: false,
        visible: false,
        formComponent: null,
        isNew: false,
      },
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    loadData() {},
    init() {
      this.getDataList()
    },
    getDataList() {
      this.loading = true
      const name = this.searchForm.name
      putAction(this.url.doneList + '?name=' + name).then((res) => {
        this.loading = false
        if (res.success) {
          this.data = res.result || []
        } else {
          this.$message.error(res.message)
        }
      })
    },
    searchQuery() {
      this.searchForm.pageNumber = 1
      this.searchForm.pageSize = 10
      this.getDataList()
    },
    searchReset() {
      this.searchForm.name = ''
      this.searchForm.pageNumber = 1
      this.searchForm.pageSize = 10
      // 重新加载数据
      this.getDataList()
    },
    handelCancel() {
      this.modalVisible = false
    },
    detail(r) {
      if (!r.formKey) {
        this.$message.warning('该流程信息未配置表单，请联系开发人员！')
        return
      }
      this.$refs.startCode.initData(r)
      this.$refs.startCode.type = false
      this.$refs.startCode.title = '查看流程业务信息：' + r.processName
    },
    history(v) {
      if (!v.procInstId) {
        this.$message.error('流程实例ID不存在')
        return
      }
      this.procInstId = v.procInstId
      this.modalLsVisible = true
    },
    remove(v) {
      postAction(this.url.deleteHistoricTask + v.id).then((res) => {
        if (res.success) {
          this.$message.success('操作成功')
          this.getDataList()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    handleTableChange(pagination, filters, sorter) {
      //分页、排序、筛选变化时触发
      //TODO 筛选
      if (Object.keys(sorter).length > 0) {
        this.isorter.column = sorter.field
        this.isorter.order = 'ascend' == sorter.order ? 'asc' : 'desc'
      }
      this.ipagination = pagination
      // this.loadData();
    },
  },
  watch: {},
}
</script>
<style lang="less" scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
</style>