<template>
  <a-card style="height: 100%; width: 100%" class="vScroll">
    <a-card title="产品授权信息" style="border-radius: 4px" bordered :headStyle="tstyle">
      <template slot='extra' v-if='authType === 1' >
        <div style='width: 240px;display: flex;align-items: center'>
          <authorization-select ref='authorizationSelect' data-url='/license/list' @change='selectedProductChange'></authorization-select>
        </div>
      </template>
      <div style="padding: 0 10px 0">
        <a-row>
          <a-col>
            <div class="authorization">
              <div class="authorization-upload">
                <a-upload name="file" :multiple="true" :show-upload-list='showUploadList' :action="uploadUrl" @change="handleChange">
                  <a-button> <a-icon type="upload" /> 上传许可 </a-button>
                </a-upload>
              </div>
              <div class="authorization-button" v-if="this.fileList != ''">
                <a-button @click="showModal" htmlType="submit" class="shouquan-button" :disabled="fileList.length != 1"
                  >确认授权
                </a-button>
                <a-modal v-model="visible" title="安装授权" @ok="authorizationButton">
                  <p>请确认是否继续安装授权？</p>
                </a-modal>
              </div>
            </div>
          </a-col>
        </a-row>
        <a-row style="overflow: hidden; overflow-x: auto; height: 100%; width: 100%; padding-bottom: 10px;margin-bottom: 10px;">
          <a-col style="height: 100%">
            <div class='colorBox'>
              <span class='colorTotal'>授权证书详细信息</span>
            </div>
            <table class="gridtable" style="height: 100%; width: 100%" :data-source="detailInfo">
              <tr>
                <td class="leftTd">产品名称</td>
                <td class="rightTd">{{ detailInfo.product }}</td>
                <td class="leftTd">版本号</td>
                <td class="rightTd">{{ detailInfo.version }}</td>
              </tr>
              <tr>
                <td class="leftTd">授权IP</td>
                <td class="rightTd">{{ detailInfo.ipList}}</td>
                <td class="leftTd">授权MAC</td>
                <td class="rightTd">{{ detailInfo.macList}}</td>
              </tr>
              <tr  v-if='authType === 1'>
                <td class="leftTd">版本状态</td>

                <td class="rightTd" v-if="detailInfo.formal">正式版</td>
                <td class="rightTd" v-else>试用版</td>

                <td class="leftTd">剩余有效时间（天）</td>
                <td class="rightTd">{{ detailInfo.days }}</td>
              </tr>
              <tr>
                <td class="leftTd">授权设备数量（台/套）</td>
                <td class="rightTd">{{ detailInfo.clientNum }}</td>
                <td class="leftTd">设备已占用授权数量（台/套）</td>
                <td class="rightTd">{{ detailInfo.deviceNumber }}</td>
              </tr>
              <tr>
                <td class="leftTd">授权生成时间</td>
                <td class="rightTd">{{ detailInfo.creatTime }}</td>
                <td class="leftTd">证书安装截止时间</td>
                <td class="rightTd">{{ detailInfo.installedExpiration }}</td>
              </tr>
              <tr>
                <td class="leftTd">授权单位</td>
                <td class="rightTd" colspan="3">{{ detailInfo.info }}</td>
              </tr>
            </table>
          </a-col>
        </a-row>

        <a-row style="overflow: hidden; overflow-x: auto; height: 100%; width: 100%; padding-bottom: 10px;margin-bottom: 10px;"  v-if='authType === 1'>
          <a-col style="height: 100%">
            <div class='colorBox'>
              <span class='colorTotal'>服务器和安装包版本信息</span>
            </div>
            <table class="gridtable" style="height: 100%; width: 100%" :data-source="versionInfo">
              <tr>
                <td class="leftTd">IP地址</td>
                <td class="rightTd">{{ versionInfo.ipList}}</td>
                <td class="leftTd">MAC地址</td>
                <td class="rightTd">{{ versionInfo.macList}}</td>
              </tr>
              <tr>
                <td class="leftTd">版本分支名称</td>
                <td class="rightTd">{{ versionInfo.branch}}</td>
                 <td class="leftTd">版本打包时间</td>
                <td class="rightTd">{{ versionInfo.buildTime}}</td>
              </tr>
              <tr>
                <td class="leftTd">版本提交ID</td>
                <td class="rightTd">{{ versionInfo.commitId}}</td>
                 <td class="leftTd">版本提交时间</td>
                <td class="rightTd">{{ versionInfo.commitTime}}</td>
              </tr>
            </table>
          </a-col>
        </a-row>
        <a-row style="overflow: hidden; overflow-x: auto; height: 100%; width: 100%; padding-bottom: 10px;margin-bottom: 10px;"  v-if='authType === 1'>
          <a-col style="height: 100%">
            <div class='colorBox'>
              <span class='colorTotal'>告警配置</span>
            </div>
            <table class="gridtable" style="height: 100%; width: 100%" :data-source="versionInfo">
              <tr>
                <td class="leftTd">剩余授权时间</td>
                <td class="rightTd">{{ alarmConfig.days}}</td>
                <td class="leftTd">剩余授权设备数量</td>
                <td class="rightTd">{{ alarmConfig.devNum}}</td>
              </tr>
              <tr>
                <td class="leftTd">cron表达式</td>
                <td class="rightTd">{{ alarmConfig.taskCron}}</td>
                <td class="leftTd">开启状态</td>
                <td class="rightTd">{{ alarmConfig.taskStatus}}</td>
              </tr>
            </table>
          </a-col>
        </a-row>
        <a-row  v-if='authType === 1'>
          <a-col style="height: 100%">
            <div class='colorBox'>
              <span class='colorTotal'>告警列表</span>
            </div>
            <authorization-alarms ref='authorizationAlarm' :record='selectedProduct' :license-id='selectedProduct&&selectedProduct.id?selectedProduct.id:""' ></authorization-alarms>
          </a-col>
        </a-row>
      </div>
    </a-card>
  </a-card>
</template>

<script>
import { getAction } from '@/api/manage'
import AuthorizationSelect from '@views/system/modules/AuthorizationSelect.vue'
import AuthorizationAlarms from '@views/system/modules/AuthorizationAlarms.vue'
export default {
  name: 'AuthorizationList',
  components: { AuthorizationAlarms, AuthorizationSelect },
  data() {
    return {
      tstyle: {
        color: 'rgba(0,0,0,0.85)',
        'font-size': '16px',
        'font-family': 'PingFangSC-Medium',
        'font-weight': 'bold',
        'background-color': '#f5f5f5',
        'line-height': '30px',
      },
      url: {
        list: 'license/getDetails',
        serverInfo: 'license/getServerInfo',
      },
      detailInfo: [],
      versionInfo: [],
      fileList: [],
      showUploadList:true,
      // uploadUrl: `${window._CONFIG['domianURL']}/license/importLicense`,
      visible: false,
      selectedProduct:{},
      authType:0,
    }
  },
  created() {
    this.authType = window.config.authType || 0
    if(this.authType == 0){
      this.initAuthorizationInfo()
    }

  },
  computed:{
    uploadUrl(){
      if(this.selectedProduct && this.selectedProduct.licenseName !== "local" && this.selectedProduct.licenseAddress){
        return this.selectedProduct.licenseAddress+"/insight-api/"+"license/importLicense"
      }else{
        return `${window._CONFIG['domianURL']}/license/importLicense`
      }
    },
    alarmConfig(){
      if(this.selectedProduct.alarmRule){
        let rules = JSON.parse(this.selectedProduct.alarmRule)
        console.log("当前产品 === ",this.selectedProduct)
        return {
          days:rules.days.bound +`（${rules.days.unit}）`,
          devNum:rules.devNum.bound +`（${rules.devNum.unit}）`,
          taskCron:this.selectedProduct.taskCron,
          taskStatus:this.selectedProduct.taskStatus==="-1"?"关闭":"开启"
        }
      }else{
        return {}
      }
    }
  },
  methods: {
    //获取接口上下文路径；
    getContextStr(){
      let apiUrl = window._CONFIG['domianURL'];
      let httpStrs = apiUrl.split("//");
      if(httpStrs.length > 0){
        let host = httpStrs[httpStrs.length-1];
        let strs = host.split("/")
        if(strs.length > 1){
          return "/"+strs[strs.length-1]+"/"
        }

      }
      return ""
    },
    //初始化授权信息
    initAuthorizationInfo(){
      this.getDetailInfo();
      this.getVersionInfo()
    },
    //获取授权信息地址
    getAuthorizationUrl(path){
      let url = path
      let contextStr = this.getContextStr();
      if(this.selectedProduct && this.selectedProduct.licenseAddress && this.selectedProduct.licenseName !== "local"){
        url = this.selectedProduct.licenseAddress+contextStr+url
      }
      return url;
    },
    //获取授权详细信息
    getDetailInfo() {
      let url = this.getAuthorizationUrl(this.url.list)
      getAction(url).then((res) => {
        this.detailInfo = res
      })
    },
    //获取授权版本信息
    getVersionInfo() {
      let url = this.getAuthorizationUrl( this.url.serverInfo)
      getAction(url).then((res) => {
        this.versionInfo = res
      })
    },

    //上传授权
    handleChange(info) {
      if (info.file.status !== 'uploading') {
        if (info.fileList.length > 1) {
          info.fileList.shift()
        }
      }
      if (info.file.response) {
        if(info.file.response.status === 200){
          if (info.file.status === 'done') {
            this.$message.success(`${info.file.name} 文件上传成功`)
            this.showUploadList = true
          } else if (info.file.status === 'removed') {
            this.$message.success(`${info.file.name} 文件删除成功!`)
            this.showUploadList = false
          }
        }else {
          this.$message.error(`${info.file.name} 文件上传失败!`)
          info.fileList.shift()
        }
      }
      this.fileList = info.fileList
    },
    authorizationButton() {
      this.visible = true
      let url = this.getAuthorizationUrl('license/installLicense')
      getAction(url).then((res) => {
        if (res.status == 200) {
          this.$message.success(res.message)
          this.initAuthorizationInfo()
          this.fileList=[]
          this.showUploadList=false
          this.visible = false
        } else {
          this.$message.error(res.message)
        }
      })
    },
    showModal() {
      this.visible = true
    },
    handleOk(e) {
      this.visible = false
    },
    //监听当前授权产品的变化
    selectedProductChange(e){
      this.selectedProduct = e;
      this.showUploadList = false;
      this.fileList=[];
      this.initAuthorizationInfo()
    }
  },
}
</script>

<style  lang="less"  scoped>
@import '~@assets/less/scroll.less';
table.gridtable {
  font-family: verdana, arial, sans-serif;
  font-size: 14px;
  color: #606266;
  border-width: 1px;
  border-color: #e8e8e8;
  border-collapse: collapse;
  text-align: left;
  width: 100%;
}
table.gridtable td {
  border-width: 1px;
  border-style: solid;
  border-color: #e8e8e8;
}
.leftTd {
  width: 17%;
  background-color: #fafafa;
  padding: 8px 24px;
  text-align: center;
}
.rightTd {
  width: 35%;
  padding: 8px 24px;
}
::v-deep .ant-card-body {
  height: 100%;
}
//上传
.authorization {
  display: flex;
  color: #fff;
  .authorization-upload {
    margin-top: 5px;
    margin-bottom: 16px;
    color: #3f9efd;
    letter-spacing: 1px;
    padding-right: 20px;
  }
  .authorization-button {
    height: 30px;
    .shouquan-button {
      margin-top: 5px;
      padding: 0 25px;
      margin-bottom: 16px;
      border-radius: 3px;
    }
  }
  ::v-deep .ant-upload-list-item-card-actions {
    right: -20px;
  }
}
.colorBox {
  margin-bottom: 18px;
}

.colorTotal {
  padding-left: 7px;
  border-left: 4px solid #1e3674;
}
</style>
