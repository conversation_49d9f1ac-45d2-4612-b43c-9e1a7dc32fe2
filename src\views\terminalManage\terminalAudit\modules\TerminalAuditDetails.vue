<template>
  <div style="overflow-y: auto;">
    <div class="div-info">
      <div class="p-info-title">
        <span>{{ data.terminalName }}</span>
        <span style="text-align: right;float: right;padding-right: 26px;">
          <img
            src="~@/assets/return1.png"
            alt
            @click="getGo"
            style="width: 20px;height: 20px;cursor: pointer"
          />
        </span>
      </div>
      <div class="title-bottom">
        <div class="p-info-product">
          <span :class="['span-img',{'green-bg':data.status==='pass', 'blue-bg':data.status==='approving', 'red-bg':data.status==='refuse'}]"></span><span class="span-status">{{ data.status==='pass'?'已通过': data.status==='approving'?'审批中':'已驳回' }}</span>
        </div>
        <div v-if="data.status==='approving'">
          <a-button class="pass-btn"  @click="handlePass">通过</a-button>
          <a-button class="refuse-btn" @click="handleRefuse">驳回</a-button>
        </div>
      </div>
      
    </div>
    <TerminalAuditInfo :data="data"></TerminalAuditInfo>
  </div>
</template>

<script>
import TerminalAuditInfo from './TerminalAuditInfo'
import { httpAction, getAction, putAction, deleteAction } from '@/api/manage'
export default {
  name: 'TerminalAuditDetails',
  components: {
    TerminalAuditInfo
  },
  data(){
    return {
      url:{
        edit: 'approve/momgTerminalApprove/edit',
      }
    }
  },
  props: {
    data: {
      type: Object,
    }
  },
  methods:{
    //返回上一级
    getGo(){
      this.$parent.pButton2(0);
    },
    handlePass(){
      putAction(this.url.edit,{id:this.data.id, status:'pass'}).then(res=>{
        if(res.success){
          this.$message.success("审批通过!")
          // this.loadData()
          this.data.status = 'pass'
        }
      })
    },
    handleRefuse(){
      putAction(this.url.edit,{id:this.data.id, status:'refuse'}).then(res=>{
        if(res.success){
          this.$message.success("审批完成!")
          // this.loadData()
          this.data.status = 'refuse'
        }
      })
    },
  }
}
</script>

<style scoped lang="less">
.ant-modal-body {
    background-color: #eee;
  }
  ::v-deep .ant-card-body{
    padding:20px 24px;
  }
  .div-info {
    position: relative;
    background-color: white;
    height: 100px;
    margin-bottom: 16px;
    padding: 10px 0 0 24px;
    border-radius: 3px;
  }
  .p-info-title {
    line-height: 40px;
    height: 40px;
    margin-bottom: 0px;
    font-family: PingFangSC-Medium;
    font-size: 18px;
    color: #000000;
  }
  .title-bottom{
    margin-right: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .p-info-product {
    line-height: 45px;
    height: 45px;
    margin-bottom: 0px;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
  }
  .span-img{
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 50%;
  }
  .green-bg{
    background-color:#12ce66;
  }
  .blue-bg{
    background-color:#409eff;
  }
  .red-bg{
    background-color:#df1a1a;
  }
  .pass-btn{
    margin-right:8px;
    color: #409eff;
    background: #EDF5FE;
    border-color: #b3d8ff;
  }
  .pass-btn:hover{
    color: #fff !important;
    background-color: #409eff !important;
    border-color: #409eff !important;
  }
  .refuse-btn:hover{
    color: #409eff;
    background: #fff;
    border-color: #b3d8ff;
  }
  .span-status {
    margin-left: 6px;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
  }
</style>