<template>
  <div class="box">
    <div v-if="system == 'windows'" class="lxImg"></div>
    <div v-else-if="system == 'CentOs'" class="ftImg"></div>
    <div v-else-if="system == 'Ubuntu'" class="hgImg"></div>
    <div v-else-if="system == 'KylinOS'" class="zxImg"></div>
    <div v-else-if="system == '中科方德'" class="swImg"></div>
    <div v-else-if="system == 'UOS'" class="tongxinImg"></div>
    <div v-else-if="system == 'NeoKylin'" class="zhongbiaoqilinImg"></div>
    <div v-else class="otherImg">{{ system }}</div>
    <div class="boxContent">
      <div class="leftBox">
        <a-tooltip overlayClassName='oneClickHelpTooltip'>
          <template slot="title">
            {{name}}
          </template>
          名称：{{ name }}
        </a-tooltip>
      </div>
      <div class="rightBox">
        <a-tooltip overlayClassName='oneClickHelpTooltip'>
          <template slot="title">
            {{version}}
          </template>
          驱动版本：{{ version }}
        </a-tooltip>
      </div>
    </div>
    <div class="boxContent" style="margin-top: 10px;">
      <div class="leftBox">
        <a-tooltip overlayClassName='oneClickHelpTooltip'>
          <template slot="title">
            {{system}}
          </template>
          操作系统：{{ system }}
        </a-tooltip>
      </div>
      <div class="rightBox">
        <a-tooltip overlayClassName='oneClickHelpTooltip'>
          <template slot="title">
            {{cpuType}}
          </template>
          CPU架构：{{ cpuType }}
        </a-tooltip>
      </div>
    </div>
    <div class="driverDown">
      <a style="color: #409eff;font-size: 16px;" @click="fontClick(record.driveFile,record)">下载(下载次数 :
        {{ record.downloadCount }} )</a>
      <img src="../../../assets/img/xiazai.png" style="height: 13px;width: 14px;margin-left: 10px;cursor: pointer;"
        @click="fontClick(record.driveFile,record)">
    </div>
  </div>
</template>

<script>
import { downFile, downloadFile } from '../../../api/manage'
import Vue from 'vue'

  export default {
    props: {
      src: {
        type: String,
        default: '',
      },
      name: {
        type: String,
        default: '',
      },
      version: {
        type: String,
        default: '',
      },
      system: {
        type: String,
        default: '',
      },
      cpuType: {
        type: String,
        default: '',
      },
      record: {
        type: Object,
        default: '',
      },
    },
    data() {
      return {

      };
    },
    methods: {
      //下载
      fontClick(path, data) {
        if(path){
          let arr = path.split("/")
          let fileName = ""
          if(arr.length > 0){
            fileName = arr[arr.length - 1]
          }
          // window.open(window._CONFIG['pathUrl'] + '/' + path + '?id=' + data.id)
          this.downloadFile(window._CONFIG['pathUrl'] + '/' + path + '?id=' + data.id,fileName)
        }
        else{
          this.$message.warning("没有驱动文件！")
        }

      },
      downloadFile(url, fileName, parameter) {
        return downFile(url, parameter).then((data) => {
          if (!data || data.size === 0) {
            Vue.prototype['$message'].warning('文件下载失败')
            return
          }
          if (typeof window.navigator.msSaveBlob !== 'undefined') {
            window.navigator.msSaveBlob(new Blob([data]), fileName)
          } else {
            let url = window.URL.createObjectURL(new Blob([data]))
            let link = document.createElement('a')
            link.style.display = 'none'
            link.href = url
            link.setAttribute('download', fileName)
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link) //下载完成移除元素
            window.URL.revokeObjectURL(url) //释放掉blob对象
            this.$emit('loadData')
          }
        })
      }
    }
  }
</script>

<style scoped lang="less">
  .box {
    padding: 0 20px;
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    border-radius: 8px;
    border: 1px solid rgba(250, 250, 250, 0);

    .boxContent {
      display: flex;
      justify-content: space-around;
      width: 100%;
      font-family: SourceHanSansCN-Regular;
      font-size: 14px;
      color: rgba(255, 255, 255, 0.50);
      letter-spacing: 0;
      font-weight: 400;

      .leftBox {
        width: 60%;
        padding-right: 15px;
        overflow: hidden;
        /*溢出的部分隐藏*/
        white-space: nowrap;
        /*文本不换行*/
        text-overflow: ellipsis;

        /*ellipsis:文本溢出显示省略号（...）；clip：不显示省略标记（...），而是简单的裁切*/
      }

      .rightBox {
        width: 40%;
        overflow: hidden;
        /*溢出的部分隐藏*/
        white-space: nowrap;
        /*文本不换行*/
        text-overflow: ellipsis;
        /*ellipsis:文本溢出显示省略号（...）；clip：不显示省略标记（...），而是简单的裁切*/
      }
    }

    .driverDown {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      margin-top: 25px;
    }
  }

  .box:hover {
    border: 1px solid #2180ED;

    .tongxinImg {
      height: 70px;
      width: 200px;
      margin: 100px auto 70px auto;
      background-image: url('/oneClickHelp/CPUImg/tongxin_light.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }

    .lxImg {
      height: 50px;
      width: 160px;
      margin: 120px auto 70px auto;
      background-image: url('/oneClickHelp/CPUImg/windows_light.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }

    .ftImg {
      height: 120px;
      width: 130px;
      margin: 75px auto 45px auto;
      background-image: url('/oneClickHelp/CPUImg/centerOs_light.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }

    .hgImg {
      height: 120px;
      width: 210px;
      margin: 75px auto 45px auto;
      background-image: url('/oneClickHelp/CPUImg/ubuntu_light.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }

    .zxImg {
      height: 80px;
      width: 200px;
      margin: 90px auto 70px auto;
      background-image: url('/oneClickHelp/CPUImg/yinheqilin_light.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }

    .swImg {
      height: 100px;
      width: 225px;
      margin: 80px auto 60px auto;
      background-image: url('/oneClickHelp/CPUImg/zhongkefangde_light.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }

    .zhongbiaoqilinImg {
      height: 163px;
      width: 156px;
      margin: 47px auto 30px auto;
      background-image: url('/oneClickHelp/CPUImg/zhongbiaoqilin_light.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }

    .otherImg {
      height: 53px;
      color: #0083FF;
      text-align: center;
      margin: 105px auto 82px auto;
      font-size: 30px;
      font-weight: 600;
      letter-spacing: 1px;
      font-family: 'Times New Roman', Times, serif;
    }
  }

  .toolClass {
    background-color: rgba(250, 250, 250, 0);
    color: rgba(250, 250, 250, .7);
  }

  .tongxinImg {
    height: 70px;
    width: 200px;
    margin: 100px auto 70px auto;
    background-image: url('/oneClickHelp/CPUImg/tongxin.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }

  .lxImg {
    height: 50px;
    width: 160px;
    margin: 120px auto 70px auto;
    background-image: url('/oneClickHelp/CPUImg/windows.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }

  .ftImg {
    height: 120px;
    width: 130px;
    margin: 75px auto 45px auto;
    background-image: url('/oneClickHelp/CPUImg/centerOs.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }

  .hgImg {
    height: 120px;
    width: 210px;
    margin: 75px auto 45px auto;
    background-image: url('/oneClickHelp/CPUImg/ubuntu.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }

  .zxImg {
    height: 80px;
    width: 200px;
    margin: 90px auto 70px auto;
    background-image: url('/oneClickHelp/CPUImg/yinheqilin.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }

  .swImg {
    height: 100px;
    width: 225px;
    margin: 80px auto 60px auto;
    background-image: url('/oneClickHelp/CPUImg/zhongkefangde.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }

  .zhongbiaoqilinImg {
    height: 163px;
    width: 156px;
    margin: 47px auto 30px auto;
    background-image: url('/oneClickHelp/CPUImg/zhongbiaoqilin.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }

  .otherImg {
    height: 53px;
    text-align: center;
    margin: 105px auto 82px auto;
    font-size: 30px;
    font-weight: 600;
    letter-spacing: 1px;
    font-family: 'Times New Roman', Times, serif;
  }
</style>