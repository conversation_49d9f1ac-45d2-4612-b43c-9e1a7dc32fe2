<template>
  <j-modal :title="title" :width="width" :centered="true" :visible="visible" :destroyOnClose="true" switchFullscreen
    cancelText="关闭" :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }" @ok="handleOk" @cancel="handleCancel"
    :fullscreen="true">
    <a-spin :spinning="confirmLoading">
      <j-form-container>
        <a-form-model ref="form" slot="detail" :model="model" :rules="validatorRules" v-bind="formItemLayout">
          <a-row>
            <a-col :span="24" style="height: 700px;">
              <a-form-model-item label="模板内容" prop="projectReportText">
                <v-md-editor v-if="['markdown'].includes(model.textType)" v-model="model.projectReportText"
                  height="1000px" right-toolbar="preview" mode="edit" :left-toolbar="leftToolBar" />
                <j-editor v-else v-model="model.projectReportText" />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </j-form-container>
    </a-spin>
  </j-modal>
</template>
<script>
  import VMdEditor from '@kangc/v-md-editor'
  import {
    httpAction
  } from '@/api/manage'
  import githubTheme from '@kangc/v-md-editor/lib/theme/github.js'
  import hljs from 'highlight.js'
  VMdEditor.use(githubTheme, {
    Hljs: hljs
  })
  export default {
    name: 'preview',
    components: {
      VMdEditor,
    },
    props: {},
    data() {
      return {
        form: this.$form.createForm(this),
        title: '编辑模版',
        width: '1000px',
        disableSubmit: false,
        visible: false,
        confirmLoading: false,
        formItemLayout: {
          labelCol: {
            xs: {
              span: 24,
            },
            sm: {
              span: 3,
            },
          },
          wrapperCol: {
            xs: {
              span: 24,
            },
            sm: {
              span: 20,
            },
          },
        },
        labelCol: {
          lg: {
            span: 6
          },
          md: {
            span: 5
          },
          sm: {
            span: 24
          },
          xs: {
            span: 24
          },
        },
        wrapperCol: {
          lg: {
            span: 17
          },
          md: {
            span: 16
          },
          sm: {
            span: 24
          },
          xs: {
            span: 24
          },
        },
        model: {
          projectReportText: '',
        },
        validatorRules: {
          projectReportText: [{
            required: true,
            message: '请输入模板内容!',
          }, ],
        },
        url: {
          add: '/devops/reportTemplate/add',
          edit: '/devops/projectInfo/edit',
        }
      }
    },
    created() {},
    methods: {
      edit(record) {
        this.visible = true
        this.$nextTick(() => {
          this.model = JSON.parse(JSON.stringify(record))

        })
      },
      show(record) {
        this.visible = true
        this.edit(record)
      },
      close() {
        this.visible = false
      },
      handleCancel() {
        this.close()
      },
      handleOk() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.model.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'put'
            }
            let formData = Object.assign(this.model, values)
            httpAction(httpurl, formData, method)
              .then((res) => {
                if (res.success) {
                  that.$message.success(res.message)
                  that.$emit('ok')
                  this.close()
                } else {
                  that.$message.warning(res.message)
                }
              })
              .finally(() => {
                that.confirmLoading = false
              })
          }
        })
      },
    },
  }
</script>
<style scoped lang="less">
  /deep/ .tox-tinymce {
    height: 750px !important;
  }
</style>