<template>
  <a-card :bordered='false'>
    <div class='action'>
      <span class='edit'>

      </span>
      <span class='return'>
        <img src='~@/assets/return1.png' alt='' @click='getGo'>
      </span>
    </div>
    <a-descriptions :column='{ xxl: 2, xl: 2, lg: 2, md: 2, sm: 1, xs: 1 }' bordered>
      <a-descriptions-item label='设备名称' v-if='data.name'>{{ data.name }}</a-descriptions-item>
      <a-descriptions-item label='设备唯一标识' v-if='data.deviceCode'>{{ data.deviceCode}}</a-descriptions-item>
      <a-descriptions-item label='IP地址' v-if='data.ip'>{{ data.ip }}</a-descriptions-item>
      <a-descriptions-item label='产品分类' v-if='data.categoryId_dictText'>{{ data.categoryId_dictText}}</a-descriptions-item>
      <a-descriptions-item label='所属单位' v-if='data.deptId_dictText'>{{ data.deptId_dictText}}</a-descriptions-item>
      <a-descriptions-item label='所在位置' v-if='data.location'>{{ data.location}}</a-descriptions-item>
      <a-descriptions-item label='使用人' v-if='data.username'>{{ data.username }}</a-descriptions-item>
      <a-descriptions-item label='是否启用' v-if='data.enable'>{{ data.enable==1?'已启用':'未启用' }}</a-descriptions-item>
      <a-descriptions-item label='描述' v-if='data.description'>{{ data.description }}</a-descriptions-item>
    </a-descriptions>
  </a-card>
</template>

<script>
export default {
  name: 'data',
  data() {
    return {}
  },
  props: {
    data: {
      type: Object,
      required: false,
      default: () => {
        return {}
      }
    }
  },
  mounted() {
    console.log(this.data, 'data');
  },
  methods: {
    //返回上一级
    getGo() {
      this.$parent.pButton2(0)
    }
  }
}
</script>

<style scoped lang='less'>
.action {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-flow: row nowrap;
  margin-bottom: 12px;

  .edit {
    margin-left: 10px;

    .icon {
      color: #409eff;
      margin-right: 6px
    }
  }

  .return {
    img {
      width: 20px;
      height: 20px;
      cursor: pointer
    }
  }
}

::v-deep .ant-descriptions-view {
  border-radius: 0px;
}

::v-deep .ant-descriptions-bordered .ant-descriptions-item-label {
  background-color: rgb(250, 250, 250);
  text-align: center;
  width: 17%;
}

::v-deep .ant-descriptions-item-label,
.ant-descriptions-item-content {
  color: rgb(96, 98, 102) !important;
}

::v-deep .ant-descriptions-bordered .ant-descriptions-item-content {
  word-break: break-word;
  white-space: normal;
}
</style>