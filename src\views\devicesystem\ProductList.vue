<template>
  <a-spin :spinning="spinning">
  <a-row style="height: 100%; margin-left: -16px; margin-right: -16px" class="vScroll">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <!-- 查询区域 -->
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" style="margin-left: 16px; margin-right: 16px">
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row" style='margin-bottom: 0px'>
              <a-col :span="spanValue">
                <a-form-item label="产品名称">
                  <a-input :maxLength='maxLength' placeholder="请输入产品名称" v-model="queryParam.displayName" :allowClear="true" autocomplete="off">
                  </a-input>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="产品分类">
                  <a-tree-select allowClear :getPopupContainer="(node) => node.parentNode"
                    v-model="queryParam.assetsCategoryId" placeholder="请选择产品分类" @change="selectAssetsCategory"
                    :tree-data="assetsCategoryTree" :dropdownStyle="{maxHeight: '400px',overflow: 'auto'}"/>
                </a-form-item>
              </a-col>
              <a-col :span="colBtnsSpan()">
                <span class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button type="primary" class="btn-search btn-search-style" @click="searchQuery">查询</a-button>
                  <a-button class="btn-reset btn-reset-style" @click="searchReset">重置</a-button>
                  <a v-if="isVisible" class="btn-updown-style" @click="doToggleSearch">
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
        <div class="table-operator table-operator-style">
          <a-button @click="handleAdd">新增</a-button>
          <a-dropdown >
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item key='1' @click='handleCopy'>复制</a-menu-item>
              <a-menu-item key='2' @click='handleExport'>导出</a-menu-item>
              <a-menu-item key='3' @click='handleImport'>导入</a-menu-item>
            </a-menu>
            <a-button> 产品备份
              <a-icon type='down' />
            </a-button>
          </a-dropdown>
        </div>
      </a-card>
      <!-- 查询区域-END -->
      <a-card :bordered="false" :bodyStyle="{ padding: '0' }"
        style="width: 100%; flex: auto; background-color: rgba(255, 255, 255, 0)">
        <!-- table区域-begin -->
        <div class="div-table-container">
          <a-row :gutter="16" type="flex" align="middle" style="margin-left: 8px; margin-right: 8px">
            <a-col v-bind="CardColLayout" v-for="(item, index) in dataSource" :key="index">
              <div class="gutter-box cardCont">
                <div class="cardContOne" style="display: inline-block; white-space: nowrap; width: 100%">
                  <div class="oneDivImg">
                    <img style="height: 100%; width: 100%" :src="item.mini" alt="" />
                  </div>
                  <div class="oneDivP">
                    <a-tooltip placement="topLeft" :title="item.displayName" trigger="hover">
                      <p class="oPFir">{{ item.displayName }}</p>
                    </a-tooltip>
                    <a-tooltip placement="topLeft" :title="item.name" trigger="hover">
                      <p class="oPTwo">{{ item.name }}</p>
                    </a-tooltip>
                  </div>
                </div>

                <a-tooltip placement="topLeft" :title="item.supportModel" trigger="hover">
                  <div class='cardContTwo'>
                    型号：{{ item.supportModel }}</div>
                </a-tooltip>

                <a-row class="cardContThree">
                  <a-col :span="8" class="cardContThreeChild oColor">是否上线</a-col>
                  <a-col :span="8" class="cardContThreeChild oColor">设备类型</a-col>
                  <a-col :span="8" class="cardContThreeChild oColor">产品分类</a-col>
                  <a-col :span="8" class="cardContThreeChild tColor" v-if="item.isOnline == '0'">
                    <span class="box"></span>
                    是
                  </a-col>
                  <a-col :span="8" class="cardContThreeChild tColor" v-else>
                    <span class="boxRed"></span>
                    否
                  </a-col>
                  <a-col :span="8" class="cardContThreeChild tColor" v-if="item.productType == '1'">
                    <div
                      style="width: 100%; height: 100%; white-space: nowrap; text-overflow: ellipsis; overflow: hidden">
                      网关设备
                    </div>
                  </a-col>
                  <a-col :span="8" class="cardContThreeChild tColor" v-else>
                    <a-tooltip placement="top" title="网关子设备" trigger="hover">
                      <div style="
                          width: 100%;
                          height: 100%;
                          white-space: nowrap;
                          text-overflow: ellipsis;
                          overflow: hidden;
                        ">
                        网关子设备
                      </div>
                    </a-tooltip>
                  </a-col>
                  <a-col :span="8" class="cardContThreeChild tColor">
                    <a-tooltip placement="top" :title="item.assetsCategoryName" trigger="hover">
                      <div style="
                          width: 100%;
                          height: 100%;
                          white-space: nowrap;
                          text-overflow: ellipsis;
                          overflow: hidden;
                        ">
                        {{ item.assetsCategoryName }}
                      </div>
                    </a-tooltip>
                  </a-col>
                </a-row>
                <a-row class="cardContFlour">
                  <a-col :span="8" class="cardContFlourCol aCol" @click="handleEdit(item)">
                    <a-icon type="edit" class="tCon" />
                    编辑
                  </a-col>
                  <a-col :span="8" class="cardContFlourCol aCol" @click="handleDetailPages(item)">
                    <a-icon type="setting" class="tCon" />
                    设置
                  </a-col>
                  <a-col :span="8" class="cardContFlourCol aCol" @click="deleteRecord(item)">
                    <a-icon type="delete" class="tCon" />
                    删除
                  </a-col>
                </a-row>
              </div>
            </a-col>
          </a-row>
        </div>
        <div style="text-align: right; position: relative; margin: 30px 30px 30px auto">
          <a-pagination show-quick-jumper show-size-changer :hideOnSinglePage="false"
            :current="ipagination.current" :total="ipagination.total" @change="onChange"
            :page-size="ipagination.pageSize" :pageSizeOptions="ipagination.pageSizeOptions"
            :show-total="(total) => `共 ${ipagination.total} 条`" @showSizeChange="onShowSizeChange" size="small">
          </a-pagination>
        </div>
      </a-card>
      <product-modal ref="modalForm" @ok="modalFormOk"></product-modal>
      <product-copy-modal ref="modalCopyForm" @ok="modalFormOk"></product-copy-modal>
      <!--<product-edit ref="modalEdit" @ok="modalFormOk"></product-edit>-->
    </a-col>
  </a-row>
  </a-spin>
</template>

<script>
  import '@/assets/less/TableExpand.less'
  import {
    mixinDevice
  } from '@/utils/mixin'
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import ProductModal from './modules/ProductModal'
  import ProductCopyModal from './modules/product/ProductCopyModal'
  /*import ProductEdit from './modules/ProductEdit'*/
  import JSuperQuery from '@/components/jeecg/JSuperQuery.vue'
  import {
    getAction,
    deleteAction
  } from '@/api/manage'
  import JTreeSelect from '@/components/jeecg/JTreeSelect'
  import {
    YqFormSearchLocation
  } from '@/mixins/YqFormSearchLocation'

  export default {
    name: 'ProductList',
    mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
    components: {
      ProductModal,
      JSuperQuery,
      //ProductEdit,
      JTreeSelect,
      ProductCopyModal
    },
    data() {
      return {
        maxLength:50,
        spinning: false,
        formItemLayout: {
          labelCol: {
            style: 'width:90px',
          },
          wrapperCol: {
            style: 'width:calc(100% - 90px)'
          }
        },
        CardColLayout: {
          xl: {
            span: 6,
          },
          lg: {
            span: 8,
          },
          md: {
            span: 12,
          },
          sm: {
            span: 24,
          },
          xs: {
            span: 24,
          },
        },
        description: 'product管理页面',
        assetsCategoryTree: [],
        assetsCategoryName: '',
        searchOptions: [],
        ipagination: {
          current: 1,
          pageSize: 8,
          pageSizeOptions: ['8', '16', '24'],
          showTotal: (total, range) => {
            return range[0] + '-' + range[1] + ' 共' + total + '条'
          },
          showQuickJumper: true,
          showSizeChanger: true,
          total: 0,
        },
        url: {
          list: '/product/product/list',
          delete: '/product/product/delete',
          deleteBatch: '/product/product/deleteBatch',
          exportXlsUrl: '/product/product/exportXls',
          importExcelUrl: 'product/product/importExcel',
          productTypeUrl: '/assetscategory/assetsCategory/selectAssetsCategoryTree',
        },
        dictOptions: {},
        superFieldList: [],
        tabShow: true,
      }
    },

    created() {
      this.getSuperFieldList()
      this.getSelectTree()
    },
    computed: {
      importExcelUrl: function () {
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
      },
    },
    mounted() {},
    methods: {
      loadData(arg) {
        if (!this.url.list) {
          this.$message.error('请设置url.list属性!')
          return
        }
        if (arg === 1) {
          this.ipagination.current = 1
        }
        var params = this.getQueryParams() //查询条件
        this.loading = true
        getAction(this.url.list, params).then((res) => {
          if (res.success) {
            this.dataSource = res.result.records || res.result
            /*  if (this.dataSource && this.dataSource.length > 0) {
                for (let i = 0; i < this.dataSource.length; i++) {
                  let arr = []
                  let selectedProtocolArray = this.dataSource[i].selectedProtocolList;
                  if (selectedProtocolArray && selectedProtocolArray.length > 0) {
                    for (let k = 0; k < selectedProtocolArray.length; k++) {
                      if (selectedProtocolArray[k].transferProtocolId != null && selectedProtocolArray[k]
                        .transferProtocolId != undefined) {
                        arr.push(selectedProtocolArray[k].transferProtocolId)
                      }
                    }
                  }
                  this.dataSource[i].transferProtocol = arr;
                }
              }*/
            this.ipagination.total = res.result.total
          }
          if (res.code === 510) {
            this.$message.warning(res.message)
          }
          this.loading = false
        })
      },
      //删除
      deleteRecord(record) {
        if (!this.url.delete) {
          this.$message.error('请设置url.delete属性!')
          return
        }
        var that = this
        this.$confirm({
          title: '确认删除',
          okText: '是',
          cancelText: '否',
          content: '是否删除选中数据?',
          onOk: function () {
            that.loading = true
            deleteAction(that.url.delete, {
              id: record.id,
            }).then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.loadData()
              } else {
                that.$message.warning(res.message)
              }
            })
          },
        })
      },
      onShowSizeChange(current, pageSize) {
        this.ipagination.pageSize = pageSize
        this.ipagination.current = current
        this.loadData()
      },
      onChange(pageNumber, pageSize) {
        this.ipagination.pageSize = pageSize
        this.ipagination.current = pageNumber

        this.tabShow = false
        // this.ipagination.pageSize = 8
        //this.ipagination.pageSizeOptions = ['8', '16', '24']
        // } else {
        //   this.tabShow = true
        //   this.ipagination.pageSize = 7
        //   this.ipagination.pageSizeOptions = ['7', '15', '23']

        this.loadData()
      },
      initDictConfig() {},
      /* getSelectTree() {
           getAction('/device/deviceCategory/selectTree').then((res) => {
             this.searchOptions = res.result
           })
         },*/
      getSelectTree() {
        getAction(this.url.productTypeUrl).then((res) => {
          this.assetsCategoryTree = res.result
        })
      },
      getSuperFieldList() {
        let fieldList = []
        fieldList.push({
          type: 'string',
          value: 'name',
          text: '名称',
          dictCode: '',
        })
        fieldList.push({
          type: 'string',
          value: 'displayName',
          text: '中文名',
          dictCode: '',
        })
        fieldList.push({
          type: 'sel_search',
          value: 'deviceCategoryId',
          text: '设备分类',
          dictTable: '',
          dictText: '',
          dictCode: '',
        })
        fieldList.push({
          type: 'string',
          value: 'isOnline',
          text: '是否上线',
          dictCode: '',
        })
        fieldList.push({
          type: 'string',
          value: 'icon',
          text: '图标',
          dictCode: '',
        })
        fieldList.push({
          type: 'string',
          value: 'remark',
          text: '描述',
          dictCode: '',
        })
        this.superFieldList = fieldList
      },
      handleShowEdit(record) {
        this.$refs.modalEdit.add(record)
        this.$refs.modalEdit.title = record.displayName
        this.$refs.modalEdit.disableSubmit = false
      },
      selectAssetsCategory(assetsCategoryId, data) {
        this.assetsCategoryId = assetsCategoryId
        this.assetsCategoryName = data[0]
      },
      handleDetailPages: function (record) {
        this.$parent.pButton1(1, record)
      },
      handleCopy: function () {
        this.$refs.modalCopyForm.init();
        this.$refs.modalCopyForm.realForm = 'copy';
        this.$refs.modalCopyForm.title = '产品复制';
        this.$refs.modalCopyForm.okText = '复制';
        this.$refs.modalCopyForm.disableSubmit = false;
      },
      handleExport: function () {
        this.$refs.modalCopyForm.init()
        this.$refs.modalCopyForm.realForm='export';
        this.$refs.modalCopyForm.title = '产品导出'
        this.$refs.modalCopyForm.okText = '导出';
        this.$refs.modalCopyForm.disableSubmit = false
      },
      handleImport: function () {
        this.$refs.modalCopyForm.init();
        this.$refs.modalCopyForm.realForm='import';
        this.$refs.modalCopyForm.title = '产品导入'
        this.$refs.modalCopyForm.okText = '导入';
        this.$refs.modalCopyForm.disableSubmit = false
      },
    },
  }
</script>
<style lang='less' scoped>
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';

  .cardAdd,
  .cardCont {
    //width: calc(100% / 4 - 12px);
    width: 390px;
    position: relative;
    background: #ffffff;
    -webkit-box-shadow: 0 3px 7px -1px rgba(0, 0, 0, 0.16);
    //box-shadow: 0 3px 7px -1px rgba(0, 0, 0, 0.16);
    box-shadow: 0px 3px 7px -1px rgba(0, 0, 0, 0.16);
    /*border-bottom-style: solid;*/
    border-radius: 2px;
    margin: 16px 0 0px 0px;
    height: 235px;
  }

  /* .cardAdd {
  margin-left: 0!important;
} */
  .cardDiv {
    text-align: center;
    position: absolute;
    top: 30%;
    left: 29%;
    right: 31%;
    bottom: 74px;
  }

  .cardDiv img {
    width: 61px;
    height: 61px;
    margin-bottom: 17px;
  }

  .cardDiv div {
    font-family: PingFangSC-Regular;
    font-size: 16px;
    color: #d7d7d7;
  }

  /* .ant-row div {
  background: transparent;
  border: 0;
} */
  .gutter-box {
    width: 100%;
  }

  .cardCont {
    position: relative;
  }

  .cardContOne {
    overflow: hidden;
    padding: 22px 0px 0 26px;
  }

  .cardContOne div {
    float: left;
  }

  /*.cardContOne div img {
  width: 38px;
  height: 37px;
  margin-right: 15px;
  margin-top: 10px;
}*/

  .oneDivImg {
    width: 38px;
    height: 37px;
    margin-right: 15px;
    margin-top: 10px;
  }

  /*
.cardContOne div p {
  margin-bottom: 8px;
}*/

  .oneDivP {
    width: calc(100% - 70px);
    margin-bottom: 8px;
  }

  .oPFir {
    font-family: PingFangSC-Regular;
    font-size: 16px;
    color: rgba(0, 0, 0, 0.85);
    margin-bottom: 8px;

    white-space: nowrap;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    cursor: default;
  }

  .oPTwo {
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
    margin-bottom: 8px;

    white-space: nowrap;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    cursor: default;
  }

  /*.oPFir_0 {
  font-family: PingFangSC-Regular;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.85);
  margin-bottom: 8px;

  display: block;
  white-space: nowrap;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  cursor: default;
}

.oPTwo_0 {
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
  margin-bottom: 8px;

  display: block;
  white-space: nowrap;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  cursor: default;
}*/

  .box {
    width: 8px;
    height: 8px;
    background: #13a40a;
    border-radius: 50%;
    display: inline-block;
    margin-right: 5px;
  }

  .boxRed {
    width: 8px;
    height: 8px;
    background: red;
    border-radius: 50%;
    display: inline-block;
    margin-right: 5px;
  }
  .cardContTwo{
    position: relative;
    margin: -20px 0px -10px 25px;
    padding: 0px 5px 0px 0px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
  }
  .cardContThree {
    padding: 26px 0 0 0;
    margin-bottom: 0px;
  }

  .cardContThreeChild {
    text-align: center;
    margin-bottom: 9px;
  }

  .oColor {
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
  }

  .tColor {
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
  }

  .cardContFlour {
    position: absolute;
    bottom: 0;
    width: 100%;
    text-align: center;
    padding: 14px 0 14px 0;
    //background: #f3f3f3;
    color: #409eff;
    background: rgba(236, 245, 255, 0.66);
    border-top: 1px solid #dadada;
    border-radius: 0 0 2px 2px;
    border-radius: 0px 0px 2px 2px;
    margin-bottom: 0px;
  }

  .cardContFlour .cardContFlourCol {
    border-right: 2px solid #dadada;
  }

  .cardContFlour div:nth-child(3) {
    border: none;
  }

  .tCon {
    margin-right: 8px;
  }

  .aCol {
    cursor: pointer;
  }
</style>