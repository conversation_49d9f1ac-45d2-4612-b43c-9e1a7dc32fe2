<template>
  <j-modal :title="title" :width="modalWidth" :visible="visible" :centered="true" switchFullscreen
           :destroyOnClose="true" @ok="handleOk" @cancel="handleCancel" cancelText="关闭">
    <a-spin :spinning="confirmLoading">
      <a-form-model ref="form" :model='model' :rules='validatorRules' v-bind='formItemLayout'>
        <a-form-model-item label="服务商账号" prop='userName'>
          <a-input placeholder="请输入服务商账号" v-model="model.userName" :allowClear="true"
                   autocomplete="off" :disabled="!!model.id" />
        </a-form-model-item>
        <a-form-model-item label="服务商名称" prop='providerName'>
          <a-input placeholder="请输入服务商名称" v-model="model.providerName" :allowClear="true" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="服务优先级" prop='priority'>
          <a-input-number style='width: 150px' v-model="model.priority" :min="1"
            placeholder='请输入服务优先级'>
          </a-input-number>
        </a-form-model-item>
        <a-form-model-item   label='服务单位' ref='departIds' prop='departIds'>
          <j-select-depart
            placeholder="请点击选服务单位"
            v-model='model.departIds'
            :multi='true'
            @blur="() => {$refs.departIds.onFieldBlur()}"
            @change='changeDeparts'>
          </j-select-depart>
        </a-form-model-item>
        <a-form-model-item label="联系电话" prop='phone'>
          <a-input placeholder="请输入联系电话" v-model="model.phone" :allowClear="true" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="详细地址" prop='providerAddress'>
          <a-input placeholder="请选择详细地址" v-model="model.providerAddress" :allowClear="true" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="描述" prop='providerDesc'>
          <a-textarea placeholder="请输入描述" v-model="model.providerDesc" :allowClear="true" autocomplete="off" />
        </a-form-model-item>
      </a-form-model>
    </a-spin>
  </j-modal>
</template>

<script>
import {duplicateCheck} from '@/api/api'
import {httpAction, getAction} from '@/api/manage'

export default {
  name: 'serviceModal',
  mixins: [],
  components: {},
  data() {
    return {
      title: '',
      modalWidth: '800px',
      visible: false,
      confirmLoading: false,
      model: {},
      formItemLayout: {
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          }
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          }
        }
      },
      validatorRules: {
        userName: [
          { required: true, message: '请输入服务商账号' },
          { min: 2, max: 30, message: '服务商账号长度在2-30个字符之间' },
          { validator: this.validateUsername,trigger:'blur' }
        ],
        providerName: [
          { required: true, message: '请输入服务商名称' },
          { min: 2, max: 30, message: '服务商名称长度在2-30个字符之间' }
        ],
        providerDesc: [
          { required: false ,max: 200, message: '描述长度在200个字符内'}
        ],
        priority: [
          { required: true,  message: '请输入优先级' }
        ],
        departIds: [
          { required: true, message: '请选择服务单位'}
        ],
        providerAddress: [
          {required: true, message: '请输入地址'},
          { max: 100, message: '地址长度在100个字符内'}
        ],
        phone: [
          {required: true,message: '请输入联系电话'},
          { validator: this.validatePhone,trigger:'blur' }
        ]
      },
      url: {
        add: '/serviceProvider/add',
        edit: '/serviceProvider/edit',
        userDepartList: '/serviceProvider/depart/departListByProviderId'
      }
    }
  },
  methods: {
    changeDeparts(e){
      //console.log('部门：',this.model.departIds)
    },
    validateUsername(rule, value, callback) {
      var params = {
        tableName: 'sys_users',
        fieldName: 'username',
        fieldVal: value,
        dataId: this.model.userId
      }
      duplicateCheck(params).then((res) => {
        if (res.success) {
          callback()
        } else {
          callback('服务商账号已存在!')
        }
      })
    },
    //添加
    add() {
      this.edit({})
    },
    //编辑
    edit(record) {
      this.visible = true
      this.model =JSON.parse(JSON.stringify(record))
      if (this.model.id){
        this.loadCheckedDeparts()
      }
    },

    //关闭
    close() {
      this.visible = false
    },
    /*获取选择的服务商单位信息*/
    loadCheckedDeparts() {
      this.confirmLoading=true
      getAction(this.url.userDepartList, {
        providerId: this.model.id,
        pageSize: -1
      }).then((res) => {
        if (res.success&&res.result&&res.result.records&&res.result.records.length>0){
          let idStr = res.result.records.map(item => item.id).join(',')
          this.$set(this.model,'departIds',idStr)
        }else {
          this.$set(this.model,'departIds','')
          if (!res.success) {
            this.$message.warning(res.message)
          }
        }
        this.confirmLoading=false
      }).catch((err)=>{
        this.$message.error(err.message)
        this.$set(this.model,'departIds','')
        this.confirmLoading=false
      })
    },

    validatePhone(rule, value, callback) {
      let reg = /(^(0[0-9]{2,3}\-)?([2-9][0-9]{6,7})+(\-[0-9]{1,4})?$)|(^((\d3)|(\d{3}\-))?(1[123456789]\d{9})$)/
      if (!value || reg.test(value)) {
        var params = {
          tableName: 'sys_users',
          fieldName: 'phone',
          fieldVal: value,
          dataId: this.model.userId
        }
        getAction('/sys/duplicate/checkForUser', params).then((res) => {
          if (res.success) {
            callback()
          } else {
            callback('手机号已存在!')
          }
        })
      } else {
        callback('请输入正确的手机号或座机号')
      }
    },
    //确认
    handleOk() {
      const that = this
      // 触发表单验证
      this.$refs.form.validate((err, values) => {
        if (err && !that.confirmLoading) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!that.model.id) {
            httpurl += that.url.add
            method = 'post'
          } else {
            httpurl += that.url.edit
            method = 'put'
          }
          let formData =JSON.parse(JSON.stringify(that.model))
          formData.providerPhone = that.model.phone
          formData.departs = that.model.departIds
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
                that.close()
              } else {
                that.$message.warning(res.message)
              }
              that.confirmLoading = false
            })
            .catch((err) => {
              that.$message.error(err.message)
              that.confirmLoading = false
            })
        }
      })
    },
    //取消
    handleCancel() {
      this.close()
    }
  }
}
</script>

<style lang="less" scoped>
@import '~@assets/less/normalModal.less';
</style>