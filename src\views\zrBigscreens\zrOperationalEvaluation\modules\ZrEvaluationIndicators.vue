<template>
  <div class='zr-evaluation-indictor'>
    <zr-bigscreen-title title='各项指标评估情况'>
    </zr-bigscreen-title>
    <div class='char-content'>
      <div class='indicator-chart' ref='zrEvalIndeictChart'></div>
    </div>
  </div>
</template>
<script>
  import ZrBigscreenTitle from '@views/zrBigscreens/modules/ZrBigscreenTitle.vue'
  import resizeObserverMixin from '@views/statsCenter/com/resizeObserverMixin'
  import {
    indicators
  } from '@views/zrBigscreens/modules/zrUtil'
  export default {
    name: 'ZrEvaluationIndicators',
    components: {
      ZrBigscreenTitle
    },
    mixins: [resizeObserverMixin],
    props: {
      metricsInfoList: {
        type: Array,
        default: () => []
      }
    },
    data() {
      return {
        chart: null,
        radarData: [],
        scoreMap: {
          'A': 100,
          'B': 80,
          'C': 60,
          'D': 45,
        }
      }
    },
    watch: {
      metricsInfoList:{
        handler(newVal, oldVal){
          this.initRadarChart()
        },
        immediate:false,
        deep:true
      }
    },
    mounted() {
      this.chart = this.$echarts.init(this.$refs.zrEvalIndeictChart)
      this.initRadarChart()
    },
    methods: {
      setRadarData(record) {
        this.radarData = [];
        this.metricsInfoList.forEach((e) => {
          this.radarData.push({
            name: e.metricsName,
            score: e.metricsResult ? this.scoreMap[e.metricsResult] : 0,
            level: e.metricsResult != null ? e.metricsResult : ''
          })
        })
      },
      initRadarChart() {
        if(this.chart == null) return
        this.setRadarData();
        if(this.radarData.length == 0){
          this.chart.clear();
          return
        }
        let option = {
          color: ['#1D67FF'],
          tooltip: {
            trigger: 'item',
            position: ['-51%', '0%'],
            formatter: (params) => {
              let tData = params.data.radarData;
              let str = '';
              tData.forEach(item => {
                str += `<span style='color:#ccc'>${item.name}</span>：等级 ${item.level || "--"}<br/>`;
              });
              return `${params.data.name}<br/>${str}`;
            }
          },
          radar: [{
            indicator: this.radarData.map(item => ({
              text: item.name,
              max: 100
            })),
            center: ['50%', '50%'],
            radius: "75%",
            startAngle: 90,
            splitNumber: 7,
            shape: 'polygon',
            nameGap: 10,
            name: {
              color: 'rgba(255, 255, 255, 0.7)',
              fontSize: 12,
              formatter: (value) => {
                return value.length > 6 ? value.slice(0, 6) + '...' : value;
              }
            },
            splitArea: {
              areaStyle: {
                color: ['#051322', '#1E4B82', '#051322', '#1E4B82', '#051322', '#1E4B82', '#051322'],
                shadowColor: 'rgba(0, 0, 0, 0.2)',
                shadowBlur: 0
              }
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: 'rgba(48,115,196,0.5)'
              }
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: 'rgba(64,149,255,0.6)'
              }
            }
          }, ],
          series: [{
            type: 'radar',
            symbolSize: 8,
            areaStyle: {
              normal: {
                color: 'rgba(64,149,255,0.6)',
              }
            },
            data: [{
                radarData: this.radarData,
                value: this.radarData.map(item => item.score),
                name: '各项指标评估情况',
                label: {
                  show: true,
                  color: '#fff',
                  fontSize: 10,
                  distance: 2,
                  position: 'bottom',
                  formatter: (params) => {
                    let tData = params.data.radarData;
                    return tData[params.dimensionIndex].level
                  }
                }
              },

            ]
          }]
        }
        this.chart.setOption(option)
      },
      resize() {
        if (this.chart) {
          this.chart.resize()
        }
      },
      resizeObserverCb() {
        this.resize()
      }
    }
  }
</script>

<style scoped lang='less'>
  .zr-evaluation-indictor {
    width: 100%;
    height: 100%;
  }

  .char-content {
    width: 100%;
    padding: 5px;
    height: calc(100% - 51px);
    position: relative;
    background: linear-gradient(to right, rgba(29, 78, 140, 0.3), rgba(29, 78, 140, 0.0));

    .indicator-chart {
      width: 100%;
      height: 100%;
    }
  }
</style>