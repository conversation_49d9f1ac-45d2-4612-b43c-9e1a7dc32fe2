<template>
  <a-row :gutter='10' style='height: 100%' class='vScroll'>
    <a-col style='width: 100%; height: 100%; display: flex; flex-direction: column'>
      <!-- 查询区域 -->
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <div class='table-page-search-wrapper'>
          <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
            <a-row :gutter='24' ref='row'>
              <a-col :span='spanValue'>
                <a-form-item label='对接名称'>
                  <a-input :maxLength='maxLength' placeholder='请输入对接名称' v-model='queryParam.systemName' :allowClear='true'
                           autocomplete='off' />
                </a-form-item>
              </a-col>
              <a-col :span='colBtnsSpan()'>
                <span class='table-page-search-submitButtons'
                      :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button class='btn-search btn-search-style' type='primary' @click='searchQuery'>查询</a-button>
                  <a-button class='btn-reset btn-reset-style' @click='searchReset'>重置</a-button>
                  <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>

      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <!-- 操作按钮区域 -->
        <div class='table-operator table-operator-style'>
          <a-button class='btn-add' @click='handleAdd'>新增</a-button>
          <a-dropdown v-if='selectedRowKeys.length > 0'>
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item key='1' @click='batchDel'>删除</a-menu-item>
            </a-menu>
            <a-button>批量操作
              <a-icon type='down' />
            </a-button>
          </a-dropdown>
        </div>

        <!-- table区域-begin -->
        <a-table ref='table' bordered :rowKey='(record,index)=>{return record.id}' :columns='columns'
                 :dataSource='dataSource' :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
                 :pagination='ipagination'
                 :loading='loading' :rowSelection='{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }'
                 @change='handleTableChange'>
          <!-- 字符串超长截取省略号显示-->
          <template slot='index' slot-scope='text,record,index'>
            <span>{{ index + 1 }}</span>
          </template>
<!--          <template slot='dataType' slot-scope='text,record,index'>-->
<!--            <span>{{ ['设备', '告警'][text] }}</span>-->
<!--          </template>-->
<!--          <template slot='interface' slot-scope='text,record,index'>-->
<!--            <span>{{ [record.deviceUrl, record.alarmUrl][record.dataType] }}</span>-->
<!--          </template>-->
          <span slot='templateContent' slot-scope='text'>
            <j-ellipsis :value='text' :length='25' />
          </span>
          <span slot='action' slot-scope='text, record' class='caozuo' style='padding-top: 10px;padding-bottom: 10px;'>
            <a @click='handleDetail(record)'>查看</a>
            <a-divider type='vertical' />
            <a @click='handleEdit(record)'>编辑</a>
            <a-divider type='vertical' />
            <a-popconfirm title='确定删除吗?' @confirm='() => handleDelete(record.id)'>
              <a>删除</a>
            </a-popconfirm>
          </span>
          <template slot='tooltip' slot-scope='text'>
            <a-tooltip placement='topLeft' :title='text' trigger='hover'>
              <div class='tooltip'>
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
      </a-card>
      <!-- table区域-end -->
      <jkdj-add ref='modalForm' @ok='loadData'></jkdj-add>
      <jkdj-info ref='jkdjInfo'></jkdj-info>
    </a-col>
  </a-row>
</template>

<script>
import {
  JeecgListMixin
} from '@/mixins/JeecgListMixin'
import JkdjAdd from './model/JkdjAdd.vue'
import {
  deleteAction,
  getAction,
  putAction
} from '@/api/manage'
import {
  YqFormSearchLocation
} from '@/mixins/YqFormSearchLocation'
import JkdjInfo from '@views/jkdjgl/model/JkdjInfo.vue'

export default {
  name: 'jkdjglManageList',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components: {
    JkdjInfo,
    JkdjAdd
  },
  data() {
    return {
      maxLength:50,
      formItemLayout: {
        labelCol: {
          style: 'width:80px'
        },
        wrapperCol: {
          style: 'width:calc(100% - 80px)'
        }
      },
      // 表头
      columns: [
        {
          title: '序号',
          dataIndex: 'index',
          scopedSlots: {
            customRender: 'index'
          },
          customCell: () => {
            let cellStyle = 'width:60px;text-align: center'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '对接名称',
          dataIndex: 'systemName',
          scopedSlots: {
            customRender: 'systemName'
          }
        },
        {
          title: '采集模式',
          dataIndex: 'collectMode',
          customRender:(text)=>{
            return ['推模式', '拉模式'][text]
          }
        },
        {
          title: '对接系统标识',
          dataIndex: 'systemCode'
        },
        {
          title: '身份识别码',
          dataIndex: 'accessToken',
          scopedSlots: {
            customRender: 'accessToken'
          }
        },
        {
          title: '采集数',
          children:[
            {
              title: '总数',
              dataIndex: 'deviceTotal'
            },
            {
              title: '成功',
              dataIndex: 'deviceSuccess'
            },
            {
              title: '失败',
              dataIndex: 'deviceFailed'
            }
          ]
        },
        {
          title: '告警数',
          children:[
            {
              title: '总数',
              dataIndex: 'alarmTotal',
              /*customCell: () => {
                let cellStyle = 'text-align: center'
                return {
                  style: cellStyle
                }
              }*/
            },
            {
              title: '成功',
              dataIndex: 'alarmSuccess',
             /* customCell: () => {
                let cellStyle = 'text-align: center'
                return {
                  style: cellStyle
                }
              }*/
            },

            {
              title: '失败',
              dataIndex: 'alarmFailed',
             /* customCell: () => {
                let cellStyle = 'text-align: center'
                return {
                  style: cellStyle
                }
              }*/
            }
          ]
        },
        // {
        //   title: '数据类型',
        //   dataIndex: 'dataType',
        //   scopedSlots: {
        //     customRender: 'dataType'
        //   }
        // },
        // {
        //   title: '服务地址',
        //   dataIndex: 'urlPrefix',
        //   scopedSlots: {
        //     customRender: 'urlPrefix'
        //   }
        // },
        {
          title: '数据源',
          dataIndex: 'requestUrl',
          customRender:(text,record)=>{
            if(record.collectMode === 0){
              return text;
            }else{
              return record.abutmentTask?record.abutmentTask.requestUrl:"";
            }
          },
          scopedSlots: {
            customRender: 'requestUrl'
          }
        },
        {
          title: '描述',
          dataIndex: 'description',
          scopedSlots: {
            customRender: 'tooltip'
          },
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 200px;max-width:400px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '操作',
          align: 'center',
          width: 180,
          fixed: 'right',
          dataIndex: 'action',
          scopedSlots: {
            customRender: 'action'
          }
        }
      ],
      url: {
        list: '/abutment/system/list',
        delete: '/abutment/system/deleteBatch',
        deleteBatch: '/abutment/system/deleteBatch'
      }
    }
  },
  computed: {
    importExcelUrl: function() {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    }
  },
  mounted() {
  },
  methods: {
    handleDelete: function(id) {
      if (!this.url.delete) {
        this.$message.error('请设置url.delete属性!')
        return
      }
      var that = this
      deleteAction(that.url.delete, { ids: id }).then((res) => {
        if (res.success) {
          //重新计算分页问题
          that.reCalculatePage(1)
          that.$message.success(res.message)
          that.loadData()
        } else {
          that.$message.warning(res.message)
        }
      })
    },
    handleDetail(record) {
      this.$refs.jkdjInfo.show(record)
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';

/deep/ .ant-table-tbody .ant-table-row td {
  padding-top: 5px;
  padding-bottom: 5px;
}
</style>