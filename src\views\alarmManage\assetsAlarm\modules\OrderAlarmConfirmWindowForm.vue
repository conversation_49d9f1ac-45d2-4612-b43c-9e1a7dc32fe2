<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-row :gutter='24'>
          <a-col :span="24">
            <a-form-item label="故障类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag v-decorator="['errorType',validatorRules.errorType]" :trigger-change="true"
                placeholder="请选择故障类型" dictCode="	fault_type" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="期望完成日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-date-picker style="width: 100%" placeholder="请选择期望完成日期"
                v-decorator="['resolveDate', { initialValue: !model.resolveDate ? null : moment(model.resolveDate, dateFormat) }]"
                :getCalendarContainer="(node) => node.parentNode" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="故障描述" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-textarea v-decorator="['errorDesc',validatorRules.errorDesc]" placeholder="请输入故障描述"
                :auto-size="{ minRows: 5, maxRows: 5 }" :allowClear='true' autocomplete='off'></a-textarea>
            </a-form-item>
          </a-col>
          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
  import {httpAction} from '@/api/manage'
  import JFormContainer from '@/components/jeecg/JFormContainer'
  import JDictSelectTag from '@/components/dict/JDictSelectTag.vue'
  import JSuperQuery from '@/components/jeecg/JSuperQuery.vue'
  import moment from "moment"
  export default {
    name: 'AlarmConfirmWindowForm',
    components: {
      JFormContainer,
      JDictSelectTag,
      JSuperQuery,
    },
    props: {
      //流程表单data
      formData: {
        type: Object,
        default: () => {},
        required: false
      },
      //表单模式：true流程表单 false普通表单
      formBpm: {
        type: Boolean,
        default: false,
        required: false
      },
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data() {
      return {
        dateFormat: 'YYYY-MM-DD',
        //cpuArch:'',
        form: this.$form.createForm(this),
        model: {},
        ids: '',
        /*        record:{},
                allDepts:'',
                dept:{},*/
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          },
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          },
        },
        confirmLoading: false,
        validatorRules: {
          errorType: {
            rules: [{
              required: true,
              message: '请选择故障类型!'
            }]
          },
          errorDesc: {
            rules: [{
              required: true,
              message: '请输入故障描述!'
            }]
          }
        },
        url: {}
      }
    },
    computed: {
      formDisabled() {
        if (this.formBpm === true) {
          if (this.formData.disabled === false) {
            return false
          }
          return true
        }
        return this.disabled
      },
      showFlowSubmitButton() {
        if (this.formBpm === true) {
          if (this.formData.disabled === false) {
            return true
          }
        }
        return false
      },
    },
    created() {},
    methods: {
      moment,
      show(ids) {
        this.ids = ids;
      },
      submitForm() {
        const that = this;
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true;
            if (!values.resolveDate) {
              values.resolveDate = ''
            } else {
              values.resolveDate = values.resolveDate.format(this.dateFormat)
            }
            let httpUrl = '/alarm/alarmHistory/confirmBatch';
            let method = 'put';
            let formData = Object.assign(this.model, values);
            formData.ids = this.ids
            httpAction(httpUrl, formData, method).then((res) => {
              if (res.success) {
                that.$message.success(res.message);
                that.$emit('ok');
              } else {
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
        })
      },
    }
  }
</script>