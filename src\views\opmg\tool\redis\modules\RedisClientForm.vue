<template>
  <a-spin :spinning='confirmLoading'>
    <j-form-container>
      <a-form :form='form' slot='detail'>
        <a-row>
          <a-col :span="24">
            <a-form-item label='IP地址' :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['host', validatorRules.host]" :allowClear='true' autocomplete='off'
                       placeholder='请输入连接地址'></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label='端  口' :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input placeholder='请输入端口号' v-decorator="['port', validatorRules.port]"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span='24'>
            <a-form-item label='连接名' :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input placeholder='请输入连接名' v-decorator="['databaseAlias', validatorRules.databaseAlias]"
                       class='aInp'></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label='用户名' :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input placeholder='请输入用户名' v-decorator="['username']">
              </a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label='密码' :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input placeholder='请输入密码' v-decorator="['password']"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label='超时值(S)' :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input placeholder='30' v-decorator="['timeout', { initialValue: '30' }]">
              </a-input>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
import {
  httpAction,
} from '@/api/manage'
import pick from 'lodash.pick'

export default {
  name: 'RedisClientForm',
  components: {},
  data() {
    return {
      form: this.$form.createForm(this),
      model: {},
      labelCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 4
        },
      },
      wrapperCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 18
        },
      },
      confirmLoading: false,
      validatorRules: {
        host: {
          rules: [{
            required: true,
            message: '请输入IP地址'
          },
            {
              pattern: /^((25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))\.){3}(25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))$/,
              message: '请输入正确的IP地址!'
            }
          ]
        },
        port: {
          initialValue: '6379',
          rules: [{
            required: true,
            message: '请输入端口号'
          }
          ]
        },
        databaseAlias: {
          initialValue: '',
          rules: [{
            required: true,
            message: '请输入连接名'
          }
          ]
        }
      },
      url: {
        add: '/redis/client/add',
        edit: '/redis/client/edit',
        redisConnect: '/redis/client/redisConnect'
      },
    }
  },
  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      this.$nextTick(() => {
        this.formInit(this.model)
      })
    },
    formInit(pickData) {
      this.form.setFieldsValue(
        pick(
          pickData,
          'host',
          'port',
          'databaseAlies',
          'username',
          'password',
          'timeout'
        )
      )
    },
    //提交
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let formData = Object.assign(this.model, values)
          console.log(httpurl,formData)
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
              that.confirmLoading = false
            })
            .catch(err => {
              that.$message.warning(err.message)
              that.confirmLoading = false
            })
        }
      })
    },
    redisConnect() {
      const that = this
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let formData = Object.assign(this.model, values)
          console.log(formData)
          httpAction(this.url.redisConnect, formData, 'get').then((res) => {
            if (res.success) {
              that.$message.success('redis连接成功')
            } else {
              that.$message.warning(res.message)
            }
            that.confirmLoading = false
          })
          .catch(err => {
            that.$message.warning(err.message)
            that.confirmLoading = false
          })
        }
      })
    }
  }
}
</script>
<style scoped lang='less'>
</style>