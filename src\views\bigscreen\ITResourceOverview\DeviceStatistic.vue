<template>
  <a-row :gutter="24" class="card-box">
    <a-col :xl="6" :lg="6" :md="12" :xs="24" class="device-card">
      <div class="device-content all">
        <div class="name">设备总数</div>
        <div class="number all">{{staData.all}}</div>
        <div class="percent all">
          <span class="label">正常状态占比</span><a-icon type="caret-up" class="caret-up" /><span>{{staData.normalRate}}</span>
        </div>
        <img src="../../../assets/bigScreen/ITResource/all-icon.png" alt="" class="icon" />
      </div>
    </a-col>
    <a-col :xl="6" :lg="6" :md="12" :xs="24" class="device-card">
      <div class="device-content online">
        <div class="name">在线设备</div>
        <div class="number online">{{staData.on}}</div>
        <div class="percent online">
          <span class="label">在线占比</span><a-icon type="caret-up" class="caret-up" /><span>{{staData.onRate}}</span>
        </div>
        <img src="../../../assets/bigScreen/ITResource/online-icon.png" alt="" class="icon" />
      </div>
    </a-col>
    <a-col :xl="6" :lg="6" :md="12" :xs="24" class="device-card">
      <div class="device-content offline">
        <div class="name">离线设备</div>
        <div class="number offline">{{staData.out}}</div>
        <div class="percent offline">
          <span class="label">离线占比</span><a-icon type="caret-up" class="caret-up" /><span>{{staData.outRate}}</span>
        </div>
        <img src="../../../assets/bigScreen/ITResource/offline-icon.png" alt="" class="icon" />
      </div>
    </a-col>
    <a-col :xl="6" :lg="6" :md="12" :xs="24" class="device-card">
      <div class="device-content unenable">
        <div class="name">未启用设备</div>
        <div class="number unenable">{{staData.disable}}</div>
        <div class="percent unenable">
          <span class="label">未启用占比</span><a-icon type="caret-up" class="caret-up" /><span>{{staData.disableRate}}</span>
        </div>
        <img src="../../../assets/bigScreen/ITResource/unenable-icon.png" alt="" class="icon" />
      </div>
    </a-col>
  </a-row>
</template>

<script>
import { getAction } from '@api/manage'

export default {
  name: 'CardTitle',
  props: {
    title: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      staData:{
        all:'暂无数据',
        normalRate:'暂无数据',
        on:'暂无数据',
        onRate:'暂无数据',
        out:'暂无数据',
        outRate:'暂无数据',
        disable:'暂无数据',
        disableRate:'暂无数据'
      },
      url:{
        showCounts: '/openAPI/showCounts'
      }
    }
  },
  mounted() {
    this.getCounts()
  },
  methods:{
    getCounts(){
      getAction(this.url.showCounts).then((res) => {
        if (res.success && res.result) {
         let obj=res.result
          const newObj = Array.isArray(obj) ? [] : {};
          for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
              const value = obj[key];
              // 处理null值 - 保持null不变
              if (value === null) {
                newObj[key] = '暂无数据';
              }
              // 处理数字类型（包括0） - 转为字符串
              else if (typeof value === 'number') {
               let strNum = String(value);
                newObj[key] =key.includes('Rate')? strNum+'%':strNum
              }
              // 其他类型保持不变
              else {
                newObj[key] =value
              }
            }
          }
          this.staData=newObj
        } else {
          this.$message.warning(res.message)
        }
      }).catch((err) => {
        this.$message.warning(err.message)
      })
    }
  }
}
</script>

<style lang="less" scoped>
.card-box {
  width: 100%;
  // height: 100%;
  height: calc(100% - 0.425rem); // 34/80px
  margin-left: 0 !important;
  margin-right: 0 !important;
  padding: 0.25rem 0 0.0625rem 0.15rem; // 20/80px 10/80px 5/80px 10/80px

  .device-card {
    padding: 0.125rem 0.175rem !important; // 10/80px 14/80px
    height: 100%;
    // height: 1.7125rem; // 137/80px
    position: relative;

    .icon {
      width: 1.5375rem; // 123/80px
      height: 1.125rem; // 90/80px
      position: absolute;
      bottom: 0.125rem; // 10/80px
      right: 0;
      z-index: 1;
    }

    .device-content {
      position: relative;
      z-index: 0;
      width: 100%;
      height: 100%;
      background-image: url('../../../assets/bigScreen/ITResource/all.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      border-radius: 0.075rem; // 6/80px
      padding: 0.2rem 0 0.2rem 0.375rem; // 16/80px 0 16/80px 30/80px
      display: flex;
      flex-direction: column;
      justify-content: center;

      &.all {
        background-image: url('../../../assets/bigScreen/ITResource/all.png');
      }
      &.online {
        background-image: url('../../../assets/bigScreen/ITResource/online.png');
      }
      &.offline {
        background-image: url('../../../assets/bigScreen/ITResource/offline.png');
      }
      &.unenable {
        background-image: url('../../../assets/bigScreen/ITResource/unenable.png');
      }

      .name {
        font-family: Microsoft YaHei;
        font-weight: 400;
        font-size: 0.1875rem; // 15/80px
        color: #ffffff;
        line-height: 0.35rem; // 28/80px
        padding-left: 0.0375rem; // 3/80px
        opacity: 0.85;
      }

      .number {
        font-family: DIN;
        font-weight: bold;
        font-size: 0.45rem; // 36/80px
        color: #ffffff;
        font-style: italic;

        &.all {
          background: linear-gradient(0deg, #48a4e5 7.9833984375%, #d2fffe 84.6435546875%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        &.online {
          background: linear-gradient(0deg, #00ffff 7.9833984375%, #d2fffe 84.6435546875%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        &.offline {
          background: linear-gradient(0deg, #fbce60 7.1533203125%, #d2fffe 84.6435546875%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        &.unenable {
          background: linear-gradient(0deg, #48a4e5 7.9833984375%, #d2fffe 84.6435546875%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }

      .caret-up {
        font-size: 0.175rem; // 14/80px
        margin-right: 0.0875rem; // 7/80px
      }

      .percent {
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 0.175rem; // 14/80px
        color: #00d1ff;
        line-height: 0.35rem; // 28/80px
        padding-left: 0.0375rem; // 3/80px
        white-space: nowrap;

        &.all {
          color: #00d1ff;
        }
        &.online {
          color: #00ffff;
        }
        &.offline {
          color: #fbce60;
        }
        &.unenable {
          color: #00d1ff;
        }

        .label {
          font-family: Source Han Sans CN;
          font-weight: 300;
          font-size: 0.175rem; // 14/80px
          color: #ffffff;
          line-height: 0.35rem; // 28/80px
          opacity: 0.55;
          margin-right: 0.15rem; // 12/80px
        }
      }
    }
  }
}
</style>
