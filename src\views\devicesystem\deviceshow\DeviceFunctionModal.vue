<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    :centered='true'
    switch-fullscreen
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭"
    okText='执行'
  >
    <device-function-form ref="realForm" :deviceInfo='deviceInfo' @ok="submitCallback" @close="handleCancel"></device-function-form>
  </j-modal>
</template>

<script>
  import DeviceFunctionForm from './DeviceFunctionForm'
  export default {
    inject: ['reload'],
    name: 'DeviceFunctionModal',
    props:{
      deviceInfo:{}
    },
    data() {
      return {
        title: '详情',
        width: 800,
        record: {},
        status: '',
        visible: false,
        dataSource: [],
        url: {}
      }
    },
    components: {
      DeviceFunctionForm
    },
    methods: {
      add(){
        this.show({})
      },
      edit(record){
        this.show(record)
      },

      show(record) {
        this.visible = true
        this.record = record
        this.$nextTick(() => {
          this.$refs.realForm.show(record)
        })
      },
      close() {
        this.$emit('close')
        this.visible = false
      },
      handleOk() {
        this.$refs.realForm.submitForm()
        this.close()
      },
      submitCallback() {
        this.$emit('ok')
        this.visible = false
      },
      handleCancel() {
        this.close()
      }
    }
  }
</script>

<style scoped>

</style>