<template>
  <div class="layout-header">
    <div class="rotate-logo" @click="goGateway">
      <img src="/oneClickHelp/layout/out.png" alt="" class="outImg" :class="{'out-ani':simpleModel}" />
      <img src="/oneClickHelp/layout/inside.png" alt="" class="insideImg" :class="{'inside-ani':simpleModel}" />
      <img :src="logoUrl" alt="" class="logo-img" />
    </div>

    <div class="title" @click="goGateway">{{ pageTitle }}</div>
    <div class="header-right">
      <div class="user-menu">
        <div class="user-menu-con">
          <a-dropdown>
            <span class="name-text">
              <span style="margin-right: 8px">{{ nameStr }}</span>
              <a-icon type="caret-down" />
            </span>
            <a-menu slot="overlay">
              <a-menu-item v-if="userCenterState" key="12" @click="goUserCenter">
                <a-icon type="user" class="icon-item" />
                <span>个人中心</span>
              </a-menu-item>
              <a-menu-item key="4" @click="updatePassword">
                <a-icon type="setting" class="icon-item" />
                <span>密码修改</span>
              </a-menu-item>
              <a-menu-item key="11" @click="updateActivation">
                <a-icon type="form" class="icon-item" />
                <span>修改注册信息</span>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
          <span style="margin-right: 8px">
            <a-avatar v-if="userAvatar" shape="circle" :size="28" :src="userAvatar" />
            <a-avatar v-else shape="circle" :size="28" src="/oneClickHelp/layout/vatar.png" />
          </span>
        </div>
      </div>

      <!-- <img class="btn-icon" src="/oneClickHelp/layout/Refresh.png" alt="" @click="refresh" /> -->
<!--      <img class="btn-icon" src="/oneClickHelp/layout/left.png" alt="" title='返回首页' @click="goGateway" />-->
<!--      <a-icon type='appstore' class="btn-icon" title='返回首页' @click="goGateway"></a-icon>-->
       <span class='return-home' title='返回首页' @click="goGateway">
         <yq-icon type='oneClickHelpReturn' class="return-icon"></yq-icon>
       </span>
       <span style="margin-right: 24px; color: #fff;cursor: pointer;">
        <och-header-notice class="action" @toMyAnnouncement="toMyAnnouncement" />
      </span>
      <div class="phoneClass">：{{ tel }}</div>
      <img src="/oneClickHelp/layout/Hotline.png" alt="" />
      <div class="phoneClass">：{{ terminalName }}</div>
      <img src="/oneClickHelp/layout/terminal.png" alt="" />
    </div>
    <user-password ref="userPassword"></user-password>
    <activation-modal ref="activation" :hostName="hostName"></activation-modal>
  </div>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex'
import { getFileAccessHttpUrl, getAction } from '@/api/manage'
import UserPassword from '@/components/oneClickHelp/UserPassword'
import ActivationModal from '@/views/oneClickHelp/modules/ActivationModal.vue'
import { path } from '@antv/x6/lib/registry/marker/main'
import OchHeaderNotice from './OchHeaderNotice.vue'
import yqIcon from '@/components/tools/SvgIcon'
// import { WebsocketMixin } from '@/mixins/WebsocketMixin'
// import WebsocketMessageMixin from '@/mixins/WebsocketMessageMixin'
export default {
  name: 'layoutHeader',
  props: {
    hostName: {
      type: String || null,
      default: '',
    },
  },
  components: {
    UserPassword,
    ActivationModal,
    OchHeaderNotice,
    yqIcon
  },
  // mixins: [WebsocketMixin, WebsocketMessageMixin],
  data() {
    return {
      userAvatar: '',
      tel: window.config.oneClickHelp.telphone,
      terminalName: '',
      nameStr: '',
      userCenterState: false,
      logoUrl:window.config.oneClickHelp.pageLogo,
      pageTitle:window.config.oneClickHelp.pageTitle,
    }
  },
  computed: {
    ...mapState({
      permissionMenuList: (state) => state.user.permissionList,
    }),
    simpleModel(){
      return window.config.simpleModel === 0
    }
  },
  created() {
    this.setUserCenterState(this.permissionMenuList)
    this.getAvatar()
    this.getTerminalInfo()
  },
  mounted() {
  },

  watch: {
    '$store.state.app.userInfoChange'(v) {
      if (v) {
        this.getAvatar()
        this.$store.commit('USER_INFO_CHANGE', false)
      }
    },
  },
  methods: {
    ...mapGetters(['nickname', 'avatar', 'userInfo']),
    userMenuClick() {},
    //修改绑定信息
    updateActivation() {
      if (this.hostName === null || this.hostName === '') {
        this.$message.warning('没有终端信息，无法修改信息！')
        return
      }
      this.$refs.activation.title = '修改绑定信息'
      this.$refs.activation.edit()
    },
    goUserCenter() {
      this.$router.push({ path: '/oneClickHelp/userCenter' })
    },
    updatePassword() {
      let username = this.userInfo().username
      this.$refs.userPassword.oneClickHelp = true
      this.$refs.userPassword.show(username)
    },
    getAvatar() {
      this.nameStr = this.userInfo().realname
      getAction('/sys/user/getUserAvatar').then((res) => {
        if (res.success) {
          this.$store.commit('SET_AVATAR', res.result)
          this.$nextTick(() => {
            this.userAvatar = ''
            this.userAvatar = getFileAccessHttpUrl(res.result)
          })
        }
      })
    },
    getTerminalInfo() {
      getAction('/terminal/terminalDevice/getTerminalInfo', { uniqueCode: this.hostName }).then((res) => {
        if (res.success&&res.result&&res.result.records&&res.result.records.length>0) {
          this.terminalName =res.result.records[0].name
        }else {
          this.$message.warning('操作失败')
        }
      }).catch((err)=>{
        this.$message.error(err.message)
      })
    },
    refresh() {
      window.location.reload()
    },
    goGateway() {
      if (this.hostName === null || this.hostName === '') {
        this.$message.warning('请检查是否已经添加终端信息？')
        return
      }
      this.$router.push({ path: '/oneClickHelp/index' })
    },
    //判断是否有个人中心页面
    setUserCenterState(menus) {
      menus.forEach((el) => {
        if (el.path === '/oneClickHelp/userCenter') {
          this.userCenterState = true
        } else if (!this.userCenterState && el.children && el.children.length > 0) {
          this.setUserCenterState(el.children)
        }
      })
    },

    toMyAnnouncement(){

    },
  },
}
</script>

<style lang="less" scoped>
.rotate-logo {
  position: absolute;
  top: 6px;
  left: calc(16 / 19.2 * 1vw);
  width: 88px;
  height: 88px;
  cursor: pointer;
}
.insideImg {
  top: 7.5%;
  left: 7.5%;
  width: 85%;
  height: 85%;
  position: absolute;
  // transform-origin: 50% calc(50%);
}
.inside-ani{
  animation: rotate-wise-one 6s normal infinite linear;
}

.logo-img {
  position: absolute;
  width: 63%;
  height: 63%;
  top: 18.5%;
  left: 18.5%;
}
.outImg {
  position: absolute;
  // top: 10px;
  // left: 10px;
  width: 100%;
  height: 100%;
  transform-origin: 50% calc(50%);
}
.out-ani{
  animation: rotate-anti-wise-one 6s normal infinite linear;
}
@keyframes rotate-wise-one {
  0% {
    transform: rotate(0deg);
  }

  50% {
    transform: rotate(180deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
@keyframes rotate-anti-wise-one {
  0% {
    transform: rotate(0deg);
  }

  50% {
    transform: rotate(-180deg);
  }

  100% {
    transform: rotate(-360deg);
  }
}

.layout-header {
  width: 100%;
  height: 61px;
  background-image: url(/oneClickHelp/layout/headerBg1.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  position: relative;

  .title {
    position: absolute;
    width: 20%;
    left: 4%;
    top: 0px;
    line-height: 57px;
    // font-family: SourceHanSansCN-Heavy;
    font-size: calc(32 / 19.2 * 1vw);
    color: #ffffff;
    letter-spacing: 9px;
    text-align: center;
    font-weight: 500;
    cursor: pointer;
  }

  .header-right {
    position: absolute;
    top: 7px;
    right: 0px;
    height: 50px;
    width: 70%;
    display: flex;
    align-items: center;
    flex-direction: row-reverse;
    background-image: linear-gradient(
      77deg,
      rgba(6, 16, 57, 0.65) 0%,
      rgba(2, 16, 71, 0.65) 35%,
      rgba(4, 29, 113, 0.65) 88%
    );

    .return-home{
      cursor: pointer;
      margin-right: 38px;
      width: 16px;
      height: 16px;

      .return-icon {
        color:#ffffff;
        font-size: 16px;
      }
    }

    .return-home:hover {
      .return-icon {
        color:#66ffff;
      }
    }
  }

  .phoneClass {
    color: white;
    font-size: 14px;
    font-weight: 300;
    letter-spacing: 1px;
    margin-left: 10px;
    margin-right: 20px;
  }

  .user-menu {
    background: url(/oneClickHelp/layout/userBg.png);
    min-width: 135px;
    height: 100%;
    background-repeat: no-repeat;
    background-size: 100% 100%;

    .user-menu-con {
      display: flex;
      flex-direction: row-reverse;
      height: 100%;
      align-items: center;
      padding: 0 8px 0px 27px;

      .name-text {
        font-size: 12px;
        color: #ffffff;
        letter-spacing: 0;
        text-align: center;
        line-height: 12px;
        font-weight: 400;
      }
    }

    // opacity: 1;
    // background-color: linear-gradient(90deg, rgba(14, 95, 255, 0) 0%, rgba(14, 95, 255, 0.59) 35%, #0e5fff 95%);
  }
}
.ant-dropdown-menu {
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
}
.ant-dropdown-menu-item,
.ant-dropdown-menu-submenu-title {
  color: #fff;
}
.ant-dropdown-menu-item:hover,
.ant-dropdown-menu-submenu-title:hover {
  background-color: transparent;
  color: #66ffff;
  background-image: linear-gradient(
    90deg,
    rgba(21, 85, 175, 0.6) 2%,
    rgba(21, 85, 175, 0.26) 43%,
    rgba(21, 85, 175, 0) 100%
  );
}

</style>