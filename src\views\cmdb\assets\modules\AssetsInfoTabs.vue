<template>
  <div class="tabs-container">
    <a-tabs :animated="false" class="device-tabs" :active-key="defaultActiveKey" default-active-key="1"
            @change="callback">
      <a-tab-pane key="1" tab="基础信息" v-if='curRenderStates.showBaseInfo'>
        <base-Info class='com' :editabled="isEditing" :isFromAssetsUpdate="isFromAssetsUpdate" ref="baseInfo" @getDeviceInfo='getDeviceInfo' @changeAssetsBaseInfo='changeAssetsBaseInfo'></base-Info>
      </a-tab-pane>
      <a-tab-pane key="2" tab="状态信息" :force-render="false"  v-if='showTab("showStateInfo")'>
        <state-Info class='com state-Info' ref="stateInfo" :deviceInfo="deviceInfo"></state-Info>
      </a-tab-pane>
      <a-tab-pane key="3" tab="设备功能" :force-render="false" v-if='showTab("showDeviceFunction")'>
        <device-function-list class='com' ref="deviceFunctionList"></device-function-list>
      </a-tab-pane>
      <a-tab-pane key="4" tab="日志管理" :force-render="false" v-if='showTab("showJournal")'>
        <journal-management class='com' ref="journalManagement"></journal-management>
      </a-tab-pane>
      <a-tab-pane key='5' tab='虚拟机管理' :force-render='false' v-if='showTab("showVirtualMachine")' >
        <virtual-machine-list ref='virtualMachineList'></virtual-machine-list>
      </a-tab-pane>
      <a-tab-pane key='6' tab='云主机管理' :force-render='false' v-if='showTab("showCloudHost")'>
        <cloud-host-list ref='cloudHostlist'></cloud-host-list>
      </a-tab-pane>
      <a-tab-pane key="7" tab="告警策略" :force-render="false" v-if='showTab("showAlarm")' >
        <alarm-management class='com' ref="alarmManagement"></alarm-management>
      </a-tab-pane>
      <a-tab-pane key="8" tab="设备面板" :force-render="false"  v-if='showTab("showVisView")'>
        <!-- <vis-view class='com' ref="visView" :panelJson="deviceInfo.panelJson"></vis-view> -->
      </a-tab-pane>
      <a-tab-pane key="9" tab="设备告警" :force-render="false"  v-if='showTab("showDeviceAlarm")' >
        <device-alarm-management class='com' ref="deviceAlarmManagement"></device-alarm-management>
      </a-tab-pane>
      <a-tab-pane key="10" tab="备份还原" :force-render="false"  v-if='showTab("showBacResConfig")'>
        <bac-res-config class='com' ref="bacResConfig"></bac-res-config>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script>
import baseInfo from '@views/cmdb/assets/modules/AssetsBaseInfo.vue'
import stateInfo from '@views/devicesystem/deviceshow/StateInfo.vue'
import alarmManagement from '@views/devicesystem/deviceshow/AlarmManagement.vue'
import deviceAlarmManagement from '@views/devicesystem/deviceshow/DeviceAlarmManagement.vue'
// import visview from '@views/devicesystem/productsetting/paneltopo/VisView.vue'
import journalManagement from '@views/devicesystem/deviceshow/JournalManagement.vue'
import deviceFunctionList from '@views/devicesystem/deviceshow/DeviceFunctionList.vue'
import {getAction} from '@/api/manage'
import virtualMachineList from '@views/vmware/ESXI/modules/VirtualMachineList.vue'
import cloudHostList from '@views/cloudPlatformManagement/cloudPlatform/modules/CloudHostList.vue'
import bacResConfig from '@views/networkManagement/networkDevice/modules/BackupAndRestoreConfig.vue'
export default {
  name: 'Tabs',
  components: {
    baseInfo,
    stateInfo,
    deviceFunctionList,
    journalManagement,
    alarmManagement,
    virtualMachineList,
    cloudHostList,
    deviceAlarmManagement,
    // 'vis-view': visview,
    bacResConfig
  },
  data() {
    return {
      defaultActiveKey: '1',
      record: {},//资产基本信息
      deviceInfo: {},
      curRenderStates: {
        showBaseInfo: false,
        showStateInfo: false,
        showDeviceFunction: false,
        showJournal: false,
        showVirtualMachine: false,
        showCloudHost: false,
        showAlarm: false,
        showVisView: false,
        showDeviceAlarm: false,
        showBacResConfig: false
      }
    }
  },
  props: {
    isEditing: {
      type: Boolean,
      default: true,
      required: false,
    },
    assetsInfo: {
      type: Object,
      required: false,
      default: () => {
        return {}
      }
    },
    renderStates: {
      type: Object,
      required: false,
      default: {}
    },
    // from资产变更
    isFromAssetsUpdate: {
      type: Boolean,
      default: false,
    }
  },
  watch: {
    assetsInfo: function(newVal, oldVal) {
      this.show(newVal)
    }
  },
  mounted() {
    Object.assign(this.curRenderStates, this.renderStates)
  },
  methods: {
    show(record) {
      this.record = JSON.parse(JSON.stringify(record)) //资产基础信息
      this.defaultActiveKey='1'
      this.callback(this.defaultActiveKey)
    },
    callback(key) {
      this.defaultActiveKey = key
      let that = this
      setTimeout(function() {
        if (key == '1') {
          that.$refs.baseInfo && that.$refs.baseInfo.show(that.record)
        } else if (key == '2') {
          that.$refs.stateInfo && that.$refs.stateInfo.show(that.deviceInfo)
        } else if (key == '3') {
          that.$refs.deviceFunctionList && that.$refs.deviceFunctionList.show(that.deviceInfo)
        } else if (key == '4') {
          that.$refs.journalManagement && that.$refs.journalManagement.show(0, that.deviceInfo)
        } else if (key == '5') {
          that.$refs.virtualMachineList && that.$refs.virtualMachineList.show(that.deviceInfo)
        } else if (key == '6') {
          that.$refs.cloudHostlist && that.$refs.cloudHostlist.show(that.deviceInfo)
        } else if (key == '7') {
          that.$refs.alarmManagement && that.$refs.alarmManagement.show(0, that.deviceInfo)
        } else if (key == '8') {
          that.$refs.visView && that.$refs.visView.show(that.deviceInfo.panelJson)
        } else if (key == '9') {
          that.$refs.deviceAlarmManagement && that.$refs.deviceAlarmManagement.show(0, that.deviceInfo)
        } else if (key == '10') {
          that.$refs.bacResConfig && that.$refs.bacResConfig.show(that.deviceInfo)
        }
      }, 100)
    },

    showTab(tabActiveStatus) {
      if (Object.keys(this.deviceInfo).length > 0 && this.curRenderStates[tabActiveStatus]) {
        return true
      } else {
        return false
      }
    },
    getDeviceInfo(info) {
      this.deviceInfo = info
    },
    changeAssetsBaseInfo(info) {
      this.$emit('update:assetsInfo', info)
    }
  }
}
</script>

<style lang='less' scoped>
.tabs-container {
  height: 100%;
  //height: calc(100% - 135px);
}

.ant-tabs-bar {
  margin-bottom: 24px !important;
}

::v-deep .ant-tabs {
  height: 100%;
  background-color: white;
  padding: 8px 24px 24px;
  border-radius: 3px;

  .ant-tabs-content {
    height: calc(100% - 60px);
    .ant-tabs-tabpane-active {
      height: 100%;
      /* overflow-y: auto;
       overflow-x: hidden;*/
    }
  }
  .com{
    height: 100%;
    /* overflow-y: auto;
     overflow-x: hidden;*/
  }
  .state-Info{
    //margin-right: 12px;
  }
}

</style>