<template>
    <a-row style="height: 100%;">
      <a-col ref="print" id="printContent" class="abcdefg" :span="12">
        <div>
          <p style="padding: 16px 24px;background-color: #fafafa;border-bottom:1px solid #e8e8e8;font-size: 16px;">PING命令测试</p>
        </div>
        <!--签字-->
        <a-col>
          <a-form :form='form'>
            <a-row>
              <a-col :span="24" class="aCol">
                <a-form-item label="IP地址" :labelCol="labelCol" :wrapperCol="wrapperCol" :required="true">
                  <a-input placeholder="请输入IP地址" v-decorator="['ip', validatorRules.ip]" class="aInp" ></a-input>
                </a-form-item>
              </a-col>
              <a-col :span="24"  class="aCol">
                <a-form-item label="发送包数量" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input placeholder="161" class="aInp"  v-decorator="['pingCount', {initialValue:4,rules: [{ required: false, validator:this.pingCount ,trigger: 'blur'}]}]"/>
                </a-form-item>
              </a-col>

              <a-col :span="24"  class="aCol">
                <a-form-item label="超时设置" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input placeholder="2000" class="aInp" v-decorator="['timeOut', {initialValue:100 ,rules: [{ required: false, validator:this.timeOut ,trigger: 'blur'}]}]"/>
                  <samp >(ms)</samp>
                </a-form-item>
              </a-col>
              <a-col :span="24"  class="aCol">
                <a-form-item label="数据包大小" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input placeholder="0" class="aInp" v-decorator="['pingPacklen', {initialValue:32,rules: [{ required: false, validator:this.pingPacklen ,trigger: 'blur'}]}]"/>
                  <samp >(byte)</samp>
                </a-form-item>
              </a-col>

            </a-row>
            <a-row class="btnStyle">
              <a-col :span="24" :style="{ textAlign: 'center' }">
                <a-button type="shallow" @click="handelSubmit" :disabled = "buttonDisadled">
                  开始
                </a-button>
                <a-button :style="{ marginLeft: '8px' }" @click="handleReset">
                  重置
                </a-button>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
      </a-col>
      <a-col :span="12" class="contTwo">
          <!--        <a-descriptions layout="vertical" bordered  >-->
          <!--          <a-descriptions-item label="PING命令测试"  style="" class="returnDiv">-->
        <div class="returnDiv">
          <p class="returnTitle">PING命令测试</p>
          <div class='command-line' ref='commondLine' @scroll='commandScroll'>
            <p v-html="result" style="padding: 5px 16px 0 24px;">{{result}}</p>
          </div>

        </div>
          <!--          </a-descriptions-item>-->
          <!--        </a-descriptions>-->
      </a-col>
    </a-row>
  <!--</page-layout>-->
</template>
<script>
import ACol from 'ant-design-vue/es/grid/Col'
import ARow from 'ant-design-vue/es/grid/Row'
import ATextarea from 'ant-design-vue/es/input/TextArea'
import { getAction } from '@/api/manage'
import  WebsocketMessageMixin  from '@/mixins/WebsocketMessageMixin'
export default {
  components: {
    ATextarea,
    ARow,
    ACol
  },
  name: 'Printgzsld',
  mixins: [WebsocketMessageMixin],
  props: {
    reBizCode: {
      type: String,
      default: ''
    },
    paramIp: {
      type: String,
      default:''
    }
  },
  watch:{
    paramIp:{
      handler(nv){
        this.ip = nv
      },
      immediate:true
    }
  },
  data() {
    return {
      form: this.$form.createForm(this),
        model: {},
      labelCol: {
        xs: { span: 24 },
        sm:{span:8},
        md:{span:7},
        lg:{ span: 6 },
        xl: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 23 },
        sm: { span: 16 },
        md: { span: 17 },
        lg: { span: 18 },
        xl: { span: 18 },
      },
        url: '/connect/testPing',
        result: '',
        buttonDisadled: false,
        validatorRules: {
        ip: {
          rules: [
            { required: true, message: '请输入IP地址!' },
            {
              pattern: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
              message: '请输入正确的IP地址!',
            },
          ],
        },
      },
    }
  },
  created() {
  },
  methods: {
    pingCount(rule, value, callback) {
      let reg = /^[0-9]*$/;
      if (value) {
        if (!reg.test(value)) {
          callback('请输入正确的包数量');
        } else {
          callback();
        }
      } else {
        callback();
      }
    },
    timeOut(rule, value, callback) {
      let reg = /^[0-9]*$/;
      if (value) {
        if (!reg.test(value)) {
          callback('请输入正确的超时值');
        } else {
          callback();
        }
      } else {
        callback();
      }
    },
    pingPacklen(rule, value, callback) {
      let reg = /^[0-9]*$/;
      if (value) {
        if (!reg.test(value)) {
          callback('请输入正确的数据包大小');
        } else {
          callback();
        }
      } else {
        callback();
      }
    },
    websocketOnmessage(e) {
      if (e.data === 'HeartBeat') {
        return
      }
      let data = eval('(' + e.data + ')')
      if(data.messageType === "pingResult"){
        this.result += data.data.respondStr;
        this.$nextTick(()=>{
          let  commondLine = this.$refs.commondLine
          if(commondLine){
            let bound = commondLine.getBoundingClientRect()
            let h = bound?bound.height:0;
            if(commondLine.scrollHeight - h > 0){
              commondLine.scrollTop = commondLine.scrollHeight - h
            }
          }
        })
      }
    },
    //提交方法
    handelSubmit() {
      this.form.validateFields((err, values) => {
        if (!err) {
        this.result = '';
        this.buttonDisadled = true;
        // 参数 
        let param = {
          ip: values.ip, // ip
          pingCount: values.pingCount,
          timeOut: values.timeOut,
          pingPacklen: values.pingPacklen
        }
        let pingUrl = window._CONFIG['domianURL'] + this.url;

        this.$http.get(pingUrl, {
          params: Object.assign(param)
        }).then(response => {
          // if (response.success) {
          //   this.result = response.result
          // }
          this.buttonDisadled = false;
        }, response => {
        });
      }})
    },
    //刷新
    handleReset() {
      this.form.resetFields()
      this.form.setFieldsValue({
        ip: '', // ip
        pingCount: 4,
        timeOut: 100,
        pingPacklen: 32,
      })
      this.buttonDisadled = false;
      this.result = '';
    },
    commandScroll(e){
      console.log("监听到了 === ",e)
    }
  }
}
</script>
<style scoped>
/*update_begin author:scott date:20191203 for:打印机打印的字体模糊问题 */
* {
  color: #000000;
  -webkit-tap-highlight-color: #000000;
}
/*update_end author:scott date:20191203 for:打印机打印的字体模糊问题 */
.importDiv {
  width: 60%;
  height: 12em;
  margin: 0 auto;
  border: 1px solid #d9d9d9;
  padding: 18px;
}
.returnDiv{
  height: 100%;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
}
.returnTitle{
  padding: 16px 24px;
  background-color: rgb(250, 250, 250);
  border-bottom: 1px solid rgb(232, 232, 232);
  font-size: 16px;
}
/* .returnDiv{
  width: 53%;
  height: 16em;
  margin: 0 auto;
  border: 1px solid #d9d9d9;
  padding: 18px;
  margin-top: 20px;
} */
.leftSpan {
  width: 14%;
}
.abcdefg .ant-card-body {
  margin-left: 0%;
  margin-right: 0%;
  margin-bottom: 1%;
  border: 0px solid black;
  min-width: 800px;
  color: #000000 !important;
}
.explain {
  text-align: left;
  margin-left: 50px;
  color: #000000 !important;
}
.explain .ant-input,
.sign .ant-input {
  font-weight: bolder;
}
.aCol {
  /* height: 45px; */
  margin-bottom: 5px;
}
.aCol:first-child{
  margin-top: 10px;
}
.btnStyle{
  margin-top: 24px;
}
.explain div {
  margin-bottom: 10px;
}
/* you can make up upload button and sample style by using stylesheets */
.ant-upload-select-picture-card i {
  font-size: 32px;
  color: #999;
}

.ant-upload-select-picture-card .ant-upload-text {
  margin-top: 8px;
  color: #666;
}
.aInp {
  width: 75%;
}
#printContent{
  height: 100%;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
}

.contTwo{
  margin-left: 16px;
  width: calc(100% - 50% - 16px);
  height: 100%;
}
.command-line{
  height: calc(100% - 80px);
  overflow-y: auto;

}
.command-line::-webkit-scrollbar{
  display: none;
}

</style>