<template>
  <a-form-item label="组件参数" :labelCol="labelCol" :wrapperCol="wrapperCol">
    <a-table
      bordered
      :showHeader="true"
      :pagination="false"
      :row-key="
        (record, index) => {
          return index
        }
      "
      :columns="columns"
      :data-source="data"
      :scroll="data.length > 0 ? { x: 'max-content' } : {}"
    >
      <template slot="name" slot-scope="text">
        {{ text }}
      </template>
    </a-table>
    <params-Model-Module :productInfo="productInfo" ref="modalForm" @ok="modalFormOk"></params-Model-Module>
  </a-form-item>
</template>

<script>
import { deleteAction, getAction, downFile, getFileAccessHttpUrl } from '@/api/manage'
import paramsModelModule from './paramsModelModule.vue'
export default {
  name: 'comParams',
  components: {
    paramsModelModule: () => import('./paramsModelModule.vue'),
  },
  props: {
    childrenDataList: Array,
    productInfo: {},
  },
  data() {
    return {
      labelCol: {
        xs: { span: 5 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      columns: [
        {
          title: '中文名',
          dataIndex: 'paramName',
          scopedSlots: { customRender: 'name' },
        },
        {
          title: '参数名',
          dataIndex: 'paramCode',
        },
        {
          title: '操作',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' },
          align: 'center',
          width: '120px',
        },
      ],
      url: {
        // list:'/product/product/list',
        delete: '/product/proertyMetadata/delete',
      },
      data: [],
    }
  },
  watch: {
    dictCode: {
      immediate: true,
      handler() {
        this.initData()
      },
    },
    //监听
    childrenDataList: {
      handler: function (val, oldval) {
        if (val != oldval) {
          this.$nextTick(() => {
            this.initData()
          })
        }
      },
      immediate: true, //关键
      deep: true,
    },
  },
  methods: {
    initData() {
      this.data = this.childrenDataList
    },
    handleEdit(record, index) {
      const that = this
      that.$refs.modalForm.edit(record, index)
      that.$refs.modalForm.title = '编辑'
      that.$refs.modalForm.disableSubmit = false
    },
    modalFormOk(info) {
      let infoData = JSON.parse(info)
      if (infoData != null && infoData.paramCode != null) {
        const that = this
        // 新增/修改 成功时，重载列表
        for (var i = 0; i < that.data.length; i++) {
          if (that.data[i].paramCode == infoData.paramCode) {
            that.data[i].dataType = infoData.dataType
            that.data[i].paramCode = infoData.paramCode
            that.data[i].paramName = infoData.paramName
            that.data[i].tips = infoData.tips
            that.data[i].judge = infoData.judge
            that.data[i].isSelect = infoData.isSelect
            break
          }
        }
        that.$emit('func', that.data)
      }
    },
    handleDelete(id) {
      this.data.splice(id, 1)
    },
  },
}
</script>

<style>
</style>
