<template>
  <!--  <a-card>-->
  <a-row style="height: 100%" class="vScroll zxw">
    <a-col :md="16" :sm="24" :xs="24">
        <homeLeft></homeLeft>
      </a-col>
    <a-col :md="8" :sm="24" :xs="24" style="height: 100%">
      <homeRight></homeRight>
    </a-col>
  </a-row>
  <!--  </a-card>-->
</template>

<script>
import homeLeft from './home/<USER>'
import homeRight from './home/<USER>'

export default {
  name: 'Analysis',
  components: {
    homeLeft,
    homeRight,
  },
  data() {
    return {}
  },
  created() {},
  methods: {},
}
</script>
<style lang="less" scoped>
@import '~@assets/less/scroll.less';
</style>