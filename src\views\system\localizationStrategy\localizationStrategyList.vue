<template>
  <a-card :bordered="false" style="height: 100%;">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind='formItemLayout'>
        <a-row :gutter="24" ref='row'>
          <a-col :span='spanValue'>
            <a-form-item label="策略名称">
              <a-input :maxLength="maxLength" v-model="queryParam.policyName" allowClear placeholder="请输入策略名称" />
            </a-form-item>
          </a-col>
          <a-col :span='spanValue'>
            <a-form-item label="启用状态">
              <a-select v-model="queryParam.isEnable" style="width: 100%" placeholder='请选择启用状态'>
                <a-select-option key="1">
                  启用
                </a-select-option>
                <a-select-option key="0">
                  未启用
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span='spanValue'>
            <span class='table-page-search-submitButtons' style='overflow: hidden;'>
              <a-button icon='search' type='primary' @click='searchQuery'>查询</a-button>
              <a-button icon='reload' style='margin-left: 8px' @click='searchReset'>重置</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!--自定义查询项 -->
    <div v-if='toggleSearchStatus' class='custom-query-item'>
      <a-checkbox-group v-model='settingQueryItems' :defaultValue='settingQueryItems' style='width:100%'
        @change='onQuerySettingsChange'>
        <a-row :gutter="24">
          <template v-for='(item, index) in queryItems'>
            <a-col v-show='item.checked' :span='querySpanValue' class='col-checkbox'>
              <a-checkbox :disabled='item.disabled' :value='item.dataIndex'>
                <j-ellipsis :length='10' :value='item.title'></j-ellipsis>
              </a-checkbox>
            </a-col>
          </template>
        </a-row>
      </a-checkbox-group>
    </div>
    <!-- 自定义查询项-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button icon="plus" type="primary" @click="handleAdd" v-has='"codeRule:add"'>新增</a-button>
      <!--      <a-button icon="download" type="default" @click="handleExportXls('编码生成规则')">导出</a-button>
            <a-upload :action="importExcelUrl" :headers="tokenHeader" :multiple="false" :showUploadList="false" name="file"
                      @change="handleImportExcel">
              <a-button icon="import" type="default">导入</a-button>
            </a-upload>-->
      <a-dropdown v-if="selectedRowKeys.length > 0" v-has='"codeRule:delete"'>
        <a-menu slot="overlay" style='text-align: center'>
          <a-menu-item key="b1" @click="batchDel(false)">删除</a-menu-item>
          <a-menu-item key="b2" @click="enableHandler(0)">禁用</a-menu-item>
          <a-menu-item key="b3" @click="enableHandler(1)">启用</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作
          <a-icon type="down" />
        </a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <a-table ref="table" bordered rowKey="id" size="middle" :columns="columns" :dataSource="dataSource"
        :loading="loading" :pagination="ipagination"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }" :scroll="{ x: inWidth }"
        @change="handleTableChange">
        <template slot="isEnable" slot-scope="text, record">
          <a-tag :color="text == 1 ? 'green' : 'red'">
            {{ text == 1 ? '启用' : '未启用' }}
          </a-tag>
        </template>
        <!-- 资源分类 -->
        <template slot="resourceCategoryIds" slot-scope="text, record">
          <a-tooltip>
            <template slot="title">
              <a-tag :color="cate.tagColor" v-for="cate in flatCategoryData.filter(e => text.indexOf(e.key) != -1)"
                :key="cate.key">
                {{ cate.title }}
              </a-tag>
            </template>
            <a-tag :color="cate.tagColor" v-for="cate in flatCategoryData.filter(e => text.indexOf(e.key) != -1)"
              :key="cate.key">
              {{ cate.title }}
            </a-tag>
          </a-tooltip>

        </template>
        <!-- 标签 -->
        <template slot="tags" slot-scope="text, record">
          <a-tooltip>
            <template slot="title">
              <a-tag :color="tag.tagColor" v-for="tag in tagList.filter(e => text.indexOf(e.tagKey) != -1)"
                :key="tag.id">
                {{ tag.tagName }}
              </a-tag>
            </template>
            <a-tag :color="tag.tagColor" v-for="tag in tagList.filter(e => text.indexOf(e.tagKey) != -1)" :key="tag.id">
              {{ tag.tagName }}
            </a-tag>
          </a-tooltip>
        </template>
        <template slot="policyMatchRule" slot-scope="text, record">
          <a-tooltip>
            <template slot="title">
              {{ getMatchRuleText(text, record) }}
            </template>
            <div style="width:100%;white-space: nowrap; overflow: hidden; text-overflow: ellipsis;" :style="{maxWidth:inWidth?'300px':''}">
              {{ getMatchRuleText(text, record) }}
            </div>
          </a-tooltip>

        </template>
        <span slot="action" slot-scope="text, record">
          <a @click="handleDetailPage(record)">查看</a>
          <span>
            <a-divider type="vertical" />
            <a-dropdown>
              <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
              <a-menu slot="overlay">
                <a-menu-item>
                  <a @click="handleEdit(record)">编辑</a>
                </a-menu-item>
                <a-menu-item>
                  <a @click="enableHandler(record.isEnable, record.id)">{{ record.isEnable == 1 ? "禁用" : "启用" }}</a>
                </a-menu-item>
                <a-menu-item>
                  <a @click="batchDel(record.id)">删除</a>
                  <!-- 下面的代码有问题 -->
                  <!--  <a-popconfirm placement="topLeft"
                    @confirm="() => handleDelete(record.id)">
                    <template slot="title">
                      <div>确定删除吗？</div>
                      <div>
                        <a-checkbox @click.stop.prevent="checkboxClick(record)" v-model="record.deltags" >同时删除设备已标记的标签
                        </a-checkbox>
                      </div>
                    </template>
                    <a>删除</a>
                  </a-popconfirm> -->
                </a-menu-item>
              </a-menu>
            </a-dropdown>
          </span>
        </span>
      </a-table>
    </div>

    <localization-strategy-modal :deviceTypes='deviceTypes' :logicTypes="logicTypes" :ruleOperators="ruleOperators"
      :ruleTypes="ruleTypes" ref="modalForm" @ok="modalFormOk">
    </localization-strategy-modal>
  </a-card>
</template>

<script>

import '@assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import LocalizationStrategyModal from './modules/LocalizationStrategyModal'
import { YqFormSeniorSearchLocation } from '@/mixins/YqFormSeniorSearchLocation'
import { getAction, deleteAction, putAction } from '@api/manage'
import { ajaxGetDictItems, assetsCategoryTreeList } from '@api/api'
import flattenDeep from 'lodash/flattenDeep'
import { flatTreeData } from '@/utils/util'
export default {
  name: 'localizationStrategyList',
  mixins: [JeecgListMixin, mixinDevice, YqFormSeniorSearchLocation],
  components: {
    LocalizationStrategyModal
  },
  data() {
    return {
      maxLength:50,
      description: '国产化设备测率管理页面',
      hadCodeList: [],
      // 表头
      columns: [
        {
          title: '序号',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          isUsed: false,
          customRender: function (t, r, index) {
            return parseInt(index) + 1
          }
        },
        {
          title: '策略名称',
          align: 'center',
          dataIndex: 'policyName',
          ellipsis: true,
          width: 200
        },
        {
          title: '资源类型',
          align: 'center',
          dataIndex: 'resourceType',
          customRender: (t, r) => {
            return this.deviceTypes.find(item => item.value == t)?.text
          }
        },
        {
          title: '资源分类',
          align: 'center',
          dataIndex: 'resourceCategoryIds',
          ellipsis: true,
          scopedSlots: { customRender: 'resourceCategoryIds' }
        },
        {
          title: '标签',
          align: 'center',
          dataIndex: 'tags',
          ellipsis: true,
          scopedSlots: { customRender: 'tags' }
        },
        /*  {
           title: '匹配基准',
           align: 'center',
           dataIndex: 'policyMatchBasis',
           customRender: (t, r) =>{
             return this.logicTypes.find(item => item.value == r.policyMatchBasis)?.label
           }
         }, */
        {
          title: '基准公式',
          align: 'center',
          dataIndex: 'policyMatchBasisFormulas',
          // width: 300,
          ellipsis: true,
        },
        {
          title: '匹配规则',
          align: 'center',
          dataIndex: 'policyMatchRule',
          scopedSlots: { customRender: 'policyMatchRule' }
          /*  customRender: (t, r) => {
             let tem = t ? JSON.parse(t) : ""
             if (tem) {
               let temValue = Object.values(tem)
               return temValue.map((item, index) => {
                 let opertorText = this.ruleOperators.find(rule => rule.value == item.ruleOperator)?.text
                 let typeText = this.ruleTypes.find(rule => rule.value == item.ruleType)?.text
                 return `${item.ruleTag}：${typeText}信息字段 ${item.ruleKey} ${opertorText} ${item.ruleValue}`
               }).join(" ; ")
             } else {
               return ""
             }

           } */
        },
        {
          title: '启用状态',
          align: 'center',
          dataIndex: 'isEnable',
          scopedSlots: { customRender: 'isEnable' }
        },
        {
          title: '策略描述',
          align: 'center',
          dataIndex: 'policyDesc',
          ellipsis: true,
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 150,
          isUsed: false,
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: '/tags/utlTagAutoPolicy/list',
        delete: '/tags/utlTagAutoPolicy/delete',
        deleteBatch: '/tags/utlTagAutoPolicy/deleteBatch',
        enableBatch: '/tags/utlTagAutoPolicy/enableBatch',
        exportXlsUrl: '',
        importExcelUrl: ''
      },
      deviceTypes: [],
      logicTypes: [{ value: 'and', label: '和（同时满足）' }, { value: 'or', label: '或（只要有一个满足）' }, { value: 'self', label: '自定义表大式' }],
      ruleOperators: [],
      ruleTypes: [],
      tagList: [],
      flatCategoryData: [],
      iscleanDeviceTags: true,//是否同时删除标签
      delVisible: false,//删除单条的弹框是否显示
    }
  },
  created() {
    this.getTagList()
    this.getCategoryTreeData()
    this.getDictData()
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
    inWidth() {
      let maxW = this.$store.state.app.maxScreenWidth
      return maxW != -1 && maxW <= 1400
    }
  },
  methods: {
    //规则汉化
    getMatchRuleText(t, r) {
      let tem = t ? JSON.parse(t) : ""
      if (tem) {
        let temValue = Object.values(tem)
        return temValue.map((item, index) => {
          let opertorText = this.ruleOperators.find(rule => rule.value == item.ruleOperator)?.text
          let typeText = this.ruleTypes.find(rule => rule.value == item.ruleType)?.text
          return `${item.ruleTag}：${typeText}信息字段 ${item.ruleKey} ${opertorText} ${item.ruleValue}`
        }).join(" ; ")
      } else {
        return ""
      }
    },
    getDictData() {
      //规则匹配模式的字典
      ajaxGetDictItems('innovation_rule_operator').then(res => {
        if (res.success) {
          this.ruleOperators = res.result
        }
      })
      //规则类型的字典
      ajaxGetDictItems('innovation_rule_types').then(res => {
        if (res.success) {
          this.ruleTypes = res.result
        }
      })
      //策略资源类型的字典
      ajaxGetDictItems('innovation_resource_type').then(res => {
        if (res.success) {
          this.deviceTypes = res.result
        }
      })
    },
    // 批量删除
    batchDel: function (sigleid) {
      this.iscleanDeviceTags = false;//默认不删除标签
      let ids = ''
      if (sigleid) {
        ids = sigleid
      } else {
        if (!this.url.deleteBatch) {
          this.$message.error('请设置url.deleteBatch属性!')
          return
        }
        if (this.selectedRowKeys.length <= 0) {
          this.$message.warning('请选择一条记录！')
          return
        }

        for (var a = 0; a < this.selectedRowKeys.length; a++) {
          ids += this.selectedRowKeys[a] + ','
        }
      }
      var that = this
      this.$confirm({
        title: sigleid ? '确定要删除这条策略吗？' : '确定要删除选中的策略吗？',
        okText: '确定',
        cancelText: '取消',
        content: h => {
          return <div>
            <a-checkbox onChange={(e) => that.onCheckboxChange(e)}>同时删除设备已标记的标签</a-checkbox>
          </div>
        },
        onOk: function () {
          that.loading = true
          deleteAction(that.url.deleteBatch, { ids: ids, iscleanDeviceTags: that.iscleanDeviceTags })
            .then((res) => {
              if (res.success) {
                //重新计算分页问题
                that.reCalculatePage(that.selectedRowKeys.length)
                that.$message.success(res.message)
                that.loadData()
                that.onClearSelected()
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.loading = false
            })
        }
      })

    },
    //批量禁用启用
    enableHandler(status, sigleid) {
      let stu = status
      let ids = ""
      //单个删除去反状态
      if (sigleid) {
        ids = sigleid
        stu = status == 1 ? 0 : 1
      } else {
        {
          if (!this.url.enableBatch) {
            this.$message.error('请设置修改状态的地址!')
            return
          }
          if (this.selectedRowKeys.length <= 0) {
            this.$message.warning('请选择一条记录！')
            return
          }

          for (var a = 0; a < this.selectedRowKeys.length; a++) {
            ids += this.selectedRowKeys[a] + ','
          }
        }
      }
      putAction(this.url.enableBatch, { ids: ids, isEnable: stu }).then(res => {
        if (res.success) {
          this.$message.success("操作成功")
          this.loadData()
        } else {
          this.$message.success("操作失败")
        }
      }).catch(err => {
        console.log("请求出错")
      })
    },
    handleDetailPage: function (record, index = 1) {
      const params = {
        record: record,
        deviceTypes: this.deviceTypes,
        logicTypes: this.logicTypes,
        ruleOperators: this.ruleOperators,
        ruleTypes: this.ruleTypes,
        tagList: this.tagList,
        flatCategoryData: this.flatCategoryData,
      }
      this.$parent.pButton2(index, params)
    },
    //监听是否删除标签的勾选
    onCheckboxChange(e) {
      this.iscleanDeviceTags = e.target.checked
    },
    checkboxClick(record) {
      console.log("监听点击勾选", record)
    },
    //获取标签列表
    getTagList() {
      getAction('/utl/taginfo/list', { pageSize: -1, pageNum: 1 }).then(res => {
        if (res.success && res.result && res.result.records) {
          this.tagList = res.result.records
        }
      })
    },
    //获取资源分类数据
    getCategoryTreeData() {
      assetsCategoryTreeList({ i: "all" }).then(res => {
        if (res.success) {
          let tem = res.result || []
          this.flatCategoryData = flatTreeData(tem)
        }
      }).catch(err => {

      })
    },
  }
}
</script>
<style scoped lang='less'>
@import '~@assets/less/common.less';
@import '~@assets/less/YQCommon.less';
</style>