.big-screen-theme {
  ::-webkit-scrollbar {
    display: none;
  }

  @big-scree-color: #00c7ff;
  @module-backcolor: #101115;

  .ant-calendar-picker {
    width: 3.95rem
      /* 316/80 */
    ;
    height: 100%;

    .ant-calendar-picker-input.ant-input {
      background-color: #101217;
      color: #909090;
      height: 100%;
      display: flex;
      align-items: center;

      .ant-calendar-range-picker-separator {
        color: #feffff;
        line-height: 0.375rem
          /* 30/80 */
        ;
      }
    }
  }

  .ant-calendar-picker-clear:hover {
    color: #ffffff !important;
  }

  .ant-calendar-picker-clear {
    color: rgb(255 255 255) !important;
    background: #111217 !important;
  }

  .ant-btn {
    background-color: transparent;
    color: #fff;
  }

  .ant-btn:hover {
    border: 1px solid @big-scree-color;
    color: @big-scree-color
  }

  .ant-btn-primary {
    background-color: #0F88D1;
    color: #fff;
  }

  .ant-btn-primary:hover {
    background-color: @big-scree-color;
    border: 1px solid transparent;
    color: #fff;
  }

  .ant-table {
    border-left: 1px solid #2C2B35;
    border-top: 1px solid #2C2B35;
  }

  .ant-table-thead>tr:first-child>th:first-child {
    border-radius: 0px;
  }

  .ant-table-thead>tr:first-child>th:last-child {
    border-radius: 0px;
  }

  .ant-table-bordered .ant-table-header>table,
  .ant-table-bordered .ant-table-body>table,
  .ant-table-bordered .ant-table-fixed-left table,
  .ant-table-bordered .ant-table-fixed-right table {
    border: none;
  }

  // .ant-table-bordered .ant-table-header > table, .ant-table-bordered .ant-table-body > table, .ant-table-bordered .ant-table-fixed-left table, .ant-table-bordered .ant-table-fixed-right table{
  //   border: 1px solid #2C2B35;
  // }
  .ant-table-bordered .ant-table-thead>tr>th,
  .ant-table-bordered .ant-table-tbody>tr>td {
    border-right: 1px solid #2C2B35;
    border-bottom: 1px solid #2C2B35;
  }

  .ant-table-thead>tr>th {
    background-color: #38393B;
    color: #fff;
    padding: 8px
  }

  .ant-table-thead>tr.ant-table-row-hover:not(.ant-table-expanded-row):not(.ant-table-row-selected)>td,
  .ant-table-tbody>tr.ant-table-row-hover:not(.ant-table-expanded-row):not(.ant-table-row-selected)>td,
  .ant-table-thead>tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected)>td,
  .ant-table-tbody>tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected)>td {
    background-color: #38393B;

  }

  .ant-table-tbody>tr>td {
    background-color: #101115;
    padding: 8px;
    color: #fff;
  }

  .ant-table-tbody>.gray-row>td {
    background-color: #25262B;
  }

  .ant-table-fixed-header .ant-table-scroll .ant-table-header {
    margin-bottom: 0 !important;
    background-color: @module-backcolor;

  }

  .ant-table-bordered.ant-table-empty .ant-table-placeholder {
    border: 1px solid #2C2B35;
  }

  .ant-table-placeholder {
    background-color: transparent;
    border: none;


  }

  .ant-empty-description {
    color: #8c8c8c !important;
  }
}

.big-screen-range-picker {
  .ant-calendar-column-header {
    color: #8c8c8c;
  }

  .ant-calendar {
    background-color: #222224;
    border: none;
  }

  .ant-calendar-date {
    color: #fff;
  }

  .ant-calendar-last-month-cell .ant-calendar-date,
  .ant-calendar-next-month-btn-day .ant-calendar-date {
    color: #5c5c5c;
  }

  .ant-calendar-today .ant-calendar-date {
    color: #294ba0 !important;
    border-color: #294ba0 !important;
  }

  .ant-calendar-input-wrap {
    border-bottom: 1px solid #969696;
  }

  .ant-calendar-range .ant-calendar-in-range-cell:before {
    background: #1890ff;
  }



  .ant-calendar-range .ant-calendar-input,
  .ant-calendar-range .ant-calendar-time-picker-input {
    color: #fff
  }

  .ant-calendar-range .ant-calendar-body,
  .ant-calendar-range .ant-calendar-month-panel-body,
  .ant-calendar-range .ant-calendar-year-panel-body,
  .ant-calendar-range .ant-calendar-decade-panel-body {
    border-top: 1px solid #969696;
  }

  .ant-calendar-range .ant-calendar-input,
  .ant-calendar-range .ant-calendar-time-picker-input {
    background-color: #222224;
  }

  .ant-calendar-header .ant-calendar-century-select,
  .ant-calendar-header .ant-calendar-decade-select,
  .ant-calendar-header .ant-calendar-year-select,
  .ant-calendar-header .ant-calendar-month-select {
    color: #fff;
  }

  .ant-calendar-next-month-btn:hover::before,
  .ant-calendar-next-year-btn:hover::before,
  .ant-calendar-next-year-btn:hover::after,
  .ant-calendar-prev-month-btn:hover::before,
  .ant-calendar-prev-year-btn:hover::before,
  .ant-calendar-prev-year-btn:hover::after {
    border-top: 1.5px solid #fff;
    border-left: 1.5px solid #fff;
  }

  .ant-calendar-cell {
    color: #fff;
  }



  .ant-calendar-year-panel {
    background-color: #222224;

    .ant-calendar-year-panel-cell .ant-calendar-year-panel-year {
      color: #fff;
    }

    .ant-calendar-year-panel-cell-disabled .ant-calendar-year-panel-year,
    .ant-calendar-year-panel-next-decade-cell .ant-calendar-year-panel-year,
    .ant-calendar-year-panel-last-decade-cell .ant-calendar-year-panel-year {
      color: #5c5c5c;
    }

    .ant-calendar-year-panel-prev-decade-btn:hover::before,
    .ant-calendar-year-panel-prev-decade-btn:hover::after,
    .ant-calendar-year-panel-next-decade-btn:hover::before,
    .ant-calendar-year-panel-next-decade-btn:hover::after {
      border-top: 1.5px solid #fff;
      border-left: 1.5px solid #fff;
    }

    .ant-calendar-year-panel-decade-select {
      color: #fff;
    }
  }

  .ant-calendar-decade-panel {
    background-color: #222224;

    .ant-calendar-decade-panel-cell .ant-calendar-decade-panel-decade {
      color: #fff;
    }

    .ant-calendar-decade-panel-cell-disabled .ant-calendar-decade-panel-decade,
    .ant-calendar-decade-panel-next-decade-cell .ant-calendar-decade-panel-decade,
    .ant-calendar-decade-panel-last-decade-cell .ant-calendar-decade-panel-decade {
      color: #5c5c5c;
    }

    .ant-calendar-decade-panel-prev-century-btn:hover::before,
    .ant-calendar-decade-panel-prev-century-btn:hover::after,
    .ant-calendar-decade-panel-next-century-btn:hover::before,
    .ant-calendar-decade-panel-next-century-btn:hover::after {
      border-top: 1.5px solid #fff;
      border-left: 1.5px solid #fff;
    }

    .ant-calendar-decade-panel-century {
      color: #fff;
    }
  }

}
// 运维助手消息通知弹窗样式
.yq-och-notice {
  cursor: pointer;
  color: #fff;
  background-color: transparent;
  background-image: url("/oneClickHelp/localDeviceInfo/bg.png"), linear-gradient(180deg, rgba(18, 41, 83, 1) 0%, rgba(18, 41, 83, 1) 18%, rgba(41, 83, 139, 1) 100%);
  .ant-notification-notice-message {
    color: rgba(255, 255, 255, .7);
  }

  .ant-notification-notice-close {
    color: rgba(255, 255, 255, .7);
  }
}
//运维助手modal.confirm和modal.error组件样式
.oneClickHelpConfirmModal,.oneClickHelpErrorModal {
  .ant-modal {
    .ant-modal-content {
      background-image: linear-gradient(180deg, #101B2F 1%, #050911 85%);
      border-width: 1px;
      border-style: solid;
      -o-border-image: linear-gradient(to bottom, #0A368B, #2C2C2C);
      border-image: linear-gradient(to bottom, #0A368B, #2C2C2C);
      border-image-slice: 1;

      .ant-modal-body{
        .ant-modal-confirm-title,.ant-modal-confirm-content{
          color:#ffffff
        }

        .ant-btn{
          background-color: transparent;
          border: 1px solid #DCDFE6;;
          border-radius: 4px;
          height: 32px;
          font-size: 14px;
          color: #ffffff;
          margin: 0 8px 0 16px;
          box-shadow: none;
        }
        .ant-btn:hover,.ant-btn:active{
          border: 1px solid #409EFF;
        }

        .ant-btn-primary{
          background-color: #409EFF;
          border: 1px solid #409EFF;
          border-radius: 4px;
          height: 32px;
          font-size: 14px;
          color: #ffffff;
          margin: 0 8px 0 16px;
          box-shadow: none;
        }

       .ant-btn-primary:hover,.ant-btn-primary:active{
          background-color: #007dff;
          border: 1px solid #007dff;
        }
      }
    }
  }
}
//运维助手tooltip组件样式
.oneClickHelpTooltip{
  color: rgb(255, 255, 255) !important;
  .ant-tooltip-inner {
    color: #ffffff !important;
    background-color: #409eff !important;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }
  .ant-tooltip-arrow {
    border-color: transparent;
    border-style: solid;
    width: 16px;
    height: 16px;
    bottom:-5px
  }
  .ant-tooltip-arrow:before {
    background-color: #409eff !important;
    width: 8px;
    height: 8px;
  }
  .ant-tooltip-placement-top .ant-tooltip-arrow,
  .ant-tooltip-placement-topLeft .ant-tooltip-arrow,
  .ant-tooltip-placement-topRight .ant-tooltip-arrow {
    border-width: 5px 5px 0;
    border-top-color: #409eff !important ;
  }
  .ant-tooltip-placement-right .ant-tooltip-arrow,
  .ant-tooltip-placement-rightTop .ant-tooltip-arrow,
  .ant-tooltip-placement-rightBottom .ant-tooltip-arrow {
    border-width: 5px 5px 5px 0;
    border-right-color: #409eff !important;
  }
  .ant-tooltip-placement-left .ant-tooltip-arrow,
  .ant-tooltip-placement-leftTop .ant-tooltip-arrow,
  .ant-tooltip-placement-leftBottom .ant-tooltip-arrow {
    border-width: 5px 0 5px 5px;
    border-left-color: #409eff !important;
  }
  .ant-tooltip-placement-bottom .ant-tooltip-arrow,
  .ant-tooltip-placement-bottomLeft .ant-tooltip-arrow,
  .ant-tooltip-placement-bottomRight .ant-tooltip-arrow {
    border-width: 0 5px 5px;
    border-bottom-color: #409eff !important;
  }
}

//运维助手popconfirm组件样式
.oneClickHelpPopconfirm{
  .ant-popover-content > .ant-popover-arrow, .ant-popover-placement-topLeft > .ant-popover-content > .ant-popover-arrow, .ant-popover-placement-topRight > .ant-popover-content > .ant-popover-arrow {
    border-right-color:#050911 !important;
    border-bottom-color:#050911 !important;
    box-shadow:1px 1px 1px #2C2C2C
  }
  .ant-popover-inner{
    background-image: linear-gradient(180deg, #101B2F 1%, #050911 85%);
    border-width: 1px;
    border-style: solid;
    -o-border-image: linear-gradient(to bottom, #0A368B, #2C2C2C);
    border-image: linear-gradient(to bottom, #0A368B, #2C2C2C);
    border-image-slice: 1;

    .ant-popover-message{
      color: #ffffff;
    }
    .ant-popover-buttons{
      .ant-btn{
        background-color: transparent;
        border: 1px solid #DCDFE6;
        color: #ffffff;
        box-shadow: none;
      }
      .ant-btn:hover,.ant-btn:active{
        border: 1px solid #409EFF;
      }

      .ant-btn-primary{
        background-color: #409EFF;
        border: 1px solid #409EFF;
        color: #ffffff;
        box-shadow: none;
      }

      .ant-btn-primary:hover,.ant-btn-primary:active{
        background-color: #007dff;
        border: 1px solid #007dff;
      }
    }
  }
}

//监控中心--日志信息--时间范围选择
.log-info-range-picker .ant-tag {
  cursor: pointer;
  font-size: 14px !important;
  color: #409EFF !important;
  background-color: #ecf5ff !important;
  background: #ECF5FF !important;
  border: 1px solid #B3D8FF !important;
  border-radius: 4px !important;
  border-radius: 4px !important;
  padding: 0 12px !important;
}
//监控平台--table列表tooltip提示组件样式
.platformTableTooltip{
  max-width: none !important;

  .ant-tooltip-inner {
   overflow: auto;
    max-width: calc(100vw * 0.5);
    max-height: calc(100vh * 0.5);
  }
}
/*监控平台--spin样式,其属性wrapperClassName赋值为custom-ant-spin，以下样式生效*/
.custom-ant-spin{
  height: 100%;
  >.ant-spin-container{
    height: 100%;
    display: flex;
    flex-flow: column nowrap;
  }
}

