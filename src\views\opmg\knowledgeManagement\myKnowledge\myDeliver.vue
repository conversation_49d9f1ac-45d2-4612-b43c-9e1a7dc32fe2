<template>
  <div style='margin-top:15px;'>
    <!-- 查询区域 -->
    <div class='table-page-search-wrapper'>
      <a-form layout='inline' @keyup.enter.native='searchQuery'>
        <a-row :gutter='24' ref='row'>
          <a-col :span='spanValue'>
            <a-form-item label='标题'>
              <a-input placeholder='请输入标题' v-model='queryParam.title' :allowClear='true' autocomplete='off'
                :maxLength="maxLength" />
            </a-form-item>
          </a-col>
          <a-col :span='spanValue'>
            <a-form-item label='能见度'>
              <a-select :allowClear='true' v-model='queryParam.isPrivate' placeholder='请选择能见度'>
                <a-select-option v-for='item in visibility' :value='item.value' :key="'visibility_' + item.value"
                                 :label='item.label'>
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span='spanValue'>
            <a-form-item label='审批状态'>
              <a-select :allowClear='true' v-model='queryParam.state' placeholder='请选择审批状态'>
                <a-select-option v-for='item in approvalState' :value='item.value' :key="'approvalState_' + item.value"
                                 :label='item.label'>
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span='spanValue' v-show='toggleSearchStatus'>
            <a-form-item label='知识类型'>
              <a-select :allowClear='true' v-model='queryParam.knowledgeType' placeholder='请选择知识类型'>
                <a-select-option :value='0' label='文本'>文本</a-select-option>
                <a-select-option :value='1' label='文档'>文档</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span='colBtnsSpan()'>
            <span class='table-page-search-submitButtons'
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
              <a-button type='primary' class='btn-search btn-search-style' @click='searchQuery'>查询</a-button>
              <a-button class='btn-reset btn-reset-style' @click='searchReset'>重置</a-button>
              <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
              </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class='table-operator table-operator-style'>
      <a-button class='btn-add' @click='handleAdd' v-has="'myKnowledge:add'">
        新增
      </a-button>
      <a-dropdown v-if="selectedRowKeys.length > 0&& $yqHasPermission('myKnowledge:delete')">
        <a-menu slot="overlay" style='text-align: center'>
          <a-menu-item key='1' @click='batchDel'>删除</a-menu-item>
        </a-menu>
        <a-button>批量操作
          <a-icon type='down' />
        </a-button>
      </a-dropdown>
    </div>

    <a-table ref='table' bordered rowKey='id' :columns='columns' :dataSource='dataSource' :pagination='ipagination'
             :loading='loading' :rowSelection='{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }'
             class='j-table-force-nowrap' @change='handleTableChange'>
      <template slot='isPrivate' slot-scope='text'>
        <!--        <a-icon :theme='"filled"' :type='text==="1"?"lock":"unlock"' :style='{color:text==="1"?"#FE9400":"#4BD863"}'>-->
        <a-icon :type='text===visibility[1].value?"lock":"global"'
                :style='{color:text===visibility[1].value?"#FE9400":"#4BD863"}'></a-icon>
        {{ text === visibility[1].value ? visibility[1].label : visibility[0].label }}
      </template>

      <template slot='knowledgeType' slot-scope='text'>
        <knowledge-icon :knowledgeType="text"></knowledge-icon>
      </template>

      <span slot='action' slot-scope='text, record' class='caozuo'>
        <a @click='handleDetailPage(record)'>查看</a>
         <a-divider type='vertical' />
        <a-dropdown
          :class="{'dropdown-disabled':!$yqHasPermission('myKnowledge:edit')&&!$yqHasPermission('myKnowledge:delete')}"
          :disabled="!$yqHasPermission('myKnowledge:edit')&&!$yqHasPermission('myKnowledge:delete')">
          <a class='ant-dropdown-link'>更多 <a-icon type='down' /></a>
                 <a-menu slot='overlay'>
                  <a-menu-item v-if="$yqHasPermission('myKnowledge:edit')">
                  <a @click='handleEdit(record)'>编辑</a>
                  </a-menu-item>
                  <a-menu-item v-if="$yqHasPermission('myKnowledge:delete')">
                    <a @click='confirmDelete(record.id)'>删除</a>
                  </a-menu-item>
                   <!--                   除了审批状态的判断，发布按钮其他权限同编辑一样-->
                  <a-menu-item
                    v-if="record.isPrivate!='1'&&(record.state==approvalState[0].value||record.state==approvalState[1].value)&&$yqHasPermission('myKnowledge:edit')">
                    <a @click='publish(record.id)'>发布</a>
                  </a-menu-item>
                </a-menu>
              </a-dropdown>
      </span>

      <template slot='tooltip' slot-scope='text'>
        <a-tooltip placement='topLeft' :title='text' trigger='hover'>
          <div class='tooltip'>
            {{ text }}
          </div>
        </a-tooltip>
      </template>
    </a-table>
    <!-- 新增知识弹框 -->
    <add-knowledge-modal ref='modalForm' @ok='modalFormOk'></add-knowledge-modal>
  </div>
</template>

<script>
import {JeecgListMixin} from '@/mixins/JeecgListMixin'
import {YqFormSearchLocation} from '@/mixins/YqFormSearchLocation'
import addKnowledgeModal from '@views/opmg/knowledgeManagement/knowledgeBase/modules/AddKnowledgeModal.vue'
import knowledgeIcon from '@views/opmg/knowledgeManagement/knowledgeBase/modules/KnowledgeIcon.vue'
import {deleteAction, putParamsAction} from '@/api/manage'
import { approvalState, visibility } from '@views/opmg/knowledgeManagement/knowledgeBase/modules/dataListAndFunc'
export default {
  name: 'myDeliver',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components: {
    addKnowledgeModal,
    knowledgeIcon
  },
  data() {
    return {
      maxLength:50,
      visibility: visibility,
      approvalState: approvalState,
      columns: [{
        title: '标题',
        dataIndex: 'title',
        customCell: () => {
          let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
          return {
            style: cellStyle
          }
        },
        scopedSlots: {
          customRender: 'tooltip'
        }
      },
        {
          title: '主题名称',
          dataIndex: 'topicName',
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'tooltip'
          }
        },
        {
          title: '能见度',
          dataIndex: 'isPrivate',
          customCell: () => {
            let cellStyle = 'width:100px'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'isPrivate'
          }
        },
        {
          title: '知识类型',
          dataIndex: 'knowledgeType',
          customCell: () => {
            let cellStyle = 'width:80px'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'knowledgeType'
          }
        },
        {
          title: '创建人员',
          dataIndex: 'createBy'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          customCell: () => {
            let cellStyle = 'width: 160px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '审批状态',
          dataIndex: 'state'
        },
        /* {
           title: '最近更新时间',
           dataIndex: 'updateTime',
           customCell: () => {
             let cellStyle = 'width: 160px'
             return {
               style: cellStyle
             }
           }
         },*/
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 150,
          scopedSlots: {
            customRender: 'action'
          }
        }
      ],
      url: {
        list: '/kbase/knowledges/userCreated',
        delete: '/kbase/knowledges/delete',
        deleteBatch: '/kbase/knowledges/deleteBatch',
        publish: '/kbase/knowledges/publish'
      }
    }
  },
  activated() {
    this.loadData()
  },
  methods: {
    handleAdd: function() {
      this.$refs.modalForm.add()
      this.$refs.modalForm.title = '新增'
      this.$refs.modalForm.disableSubmit = false
    },
    // 查看
    handleDetailPage: function(record) {
      this.$emit('getRecord', record)
    },
    publish(id) {
      putParamsAction(this.url.publish, { knowledgeId: id }).then((res) => {
        if (res.success) {
          this.loadData()
        } else {
          this.$message.warning(res.message)
        }
      }).catch((err) => {
        this.$message.warning(err.message)
      })
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
.caozuo .dropdown-disabled {
  color: rgba(0, 0, 0, 0.25) !important;
  cursor: default;
}
</style>