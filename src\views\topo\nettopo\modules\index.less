.wrap {
  width: 100%;
  height: 100%;
  .header {
    display: flex;
    justify-content: space-between;
    height: 48px;
    line-height: 48px;
    padding-left: 16px;
    padding-right: 32px;
    background: #fff;
    box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.1);
    position: relative;
    color: rgba(0, 0, 0, 0.45);
  }
  .content {
    display: flex;
    height: calc(100% - 48px);
    .sider {
      position: relative;
      width: 290px;
      //height: 100%;
      height: 100%;
      padding:10px;
      border: 1px solid grey;
    }
    .panel {
      height: 100%;
      .toolbar {
        display: flex;
        align-items: center;
        height: 38px;
        background-color: #f7f9fb;
        border-bottom: 1px solid rgba(0, 0, 0, 0.08);
      }
    }
    .config {
      box-sizing: border-box;
      width: 290px;
      height: 100%;
      padding: 0 10px;
      border-left: 1px solid rgba(0, 0, 0, 0.08);
    }
  }
}
// 调整边界
// .ant-drawer-body{
//   padding: 0;
// }
//解决左侧遮罩的问题
.x6-widget-dnd{
  z-index: 1000;
}

// 左侧动画
@keyframes stroke {
  100% {
    stroke-dashoffset: -400;
  }
}
.animate-text1,
.animate-text2,
.animate-text3,
.animate-text4 {
  font-weight: bold;
  fill: none;
  stroke-width: 2px;
  stroke-dasharray: 90 310;
  animation: stroke 3s infinite linear;
}
.animate-text1 {
  stroke: #873bf4;
  text-shadow: 0 0 2px #873bf4;
  animation-delay: -1.5s;
}
.animate-text2 {
  stroke: #ff6e06;
  text-shadow: 0 0 2px #ff6e06;
  animation-delay: -3s;
}
