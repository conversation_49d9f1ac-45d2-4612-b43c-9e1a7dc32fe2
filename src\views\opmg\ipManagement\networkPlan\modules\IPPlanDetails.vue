<template>
  <a-card style='height: 100%;overflow: auto'>
    <a-row>
      <a-col :span='24'>
        <span style='margin-left: 10px;font-size: 16px; '>详情</span>
        <span style='float: right;margin-bottom: 12px;'><img src='~@/assets/return1.png' alt='' @click='getGo'
                                                             style='width: 20px;height: 20px;cursor: pointer'></span>
      </a-col>
      <a-col :span='24'>
        <div class='colorBox'>
          <span class='colorTotal'>IP信息</span>
        </div>
        <a-descriptions :column='{ xxl: 2, xl: 2, lg: 2, md: 2, sm: 2, xs: 2 }' bordered>
          <a-descriptions-item label='所属子网组'>{{ record.subnetGroupName }}</a-descriptions-item>
          <a-descriptions-item label='所属子网'>{{ record.subnetName }}</a-descriptions-item>
          <a-descriptions-item label='所属网段'>{{ record.segmentName }}</a-descriptions-item>
          <a-descriptions-item label='IP地址'>{{ record.ipAddress }}</a-descriptions-item>
          <a-descriptions-item label='MAC地址'>{{ record.macAddress }}</a-descriptions-item>
          <a-descriptions-item label='使用人'>{{ record.userName }}</a-descriptions-item>
          <a-descriptions-item label='是否启用'>{{ record.enabled == 1 ? '是' : '否' }}</a-descriptions-item>
          <a-descriptions-item label='是否扫描'>{{ record.scan == 1 ? '是' : '否' }}</a-descriptions-item>
          <a-descriptions-item label='联系电话'>{{ record.phone }}</a-descriptions-item>
          <a-descriptions-item label='备注'>{{ record.remark }}</a-descriptions-item>
        </a-descriptions>
      </a-col>
      <a-col :span='24' v-if='dataSource&&dataSource.length>0'>
        <div class='colorBox'>
          <span class='colorTotal'>变更记录</span>
        </div>
        <a-table ref='table'
                 bordered
                 :row-key='(record, index) => {return record.id}' :columns='columns'
                 :dataSource='dataSource'
                 :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
                 :pagination='ipagination'
                 :loading='loading'
                 @change='handleTableChange'>
          <template slot='tooltip' slot-scope='text'>
            <a-tooltip placement='topLeft' :title='text' trigger='hover'>
              <div class='tooltip'>
                {{ text }}
              </div>
            </a-tooltip>
          </template>
          <template slot='state' slot-scope='text'>
              <span>
                {{ text=='1'?"是":"否" }}
              </span>
          </template>
        </a-table>
      </a-col>
      <a-col :span='24' v-if='record.extendList && record.extendList.length > 0'>
        <div class='colorBox'>
          <span class='colorTotal'>附加字段</span>
        </div>
        <a-descriptions :column='{ xxl: 2, xl: 2, lg: 2, md: 2, sm: 2, xs: 2 }' bordered>
          <a-descriptions-item v-for='(item,index) in record.extendList' :key="'extendList_'+index" :label='item.name'>
            {{ item.value }}
          </a-descriptions-item>
        </a-descriptions>
      </a-col>
    </a-row>
  </a-card>
</template>

<script>
import { getAction } from '@/api/manage'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'

export default {
  name: 'IPPlanDetails',
  props: {
    data: {
      type: Object
    }
  },
  mixins: [JeecgListMixin],
  data() {
    return {
      record: {},
      disableMixinCreated: true,
      columns: [
        {
          title: '使用人',
          dataIndex: 'userName'
        },
        {
          title: 'IP地址',
          dataIndex: 'ipAddress'
        },
        {
          title: 'MAC地址',
          dataIndex: 'macAddress'
        },
        {
          title: '联系电话',
          dataIndex: 'phone'
        },
        {
          title: '是否启用',
          dataIndex: 'enabled',
          scopedSlots: {
            customRender: 'state'
          }
        },
        {
          title: '是否扫描',
          dataIndex: 'scan',
          scopedSlots: {
            customRender: 'state'
          }
        },
        {
          title: 'ip管理操作内容',
          dataIndex: 'operation'
        }
      ],
      url: {
        queryById: '/devops/ip/plan/queryById',
        list: '/devops/ip/plan/history/list'
      }
    }
  },
  watch: {
    data: {
      handler(nVal) {
        if (nVal && nVal.id) {
          this.show(this.data)
          this.queryParam.planId = nVal.id
          this.loadData(1)
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    show(record) {
      getAction(this.url.queryById, {
        id: record.id
      }).then((res) => {
        if (res.success) {
          this.record = res.result
        }
      })
    },
    //返回上一级
    getGo() {
      this.$parent.pButton2(0)
    }
  }
}
</script>
<style scoped lang='less'>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';

.colorBox {
  margin-top: 10px;
  margin-bottom: 10px;
}

.colorTotal {
  padding-left: 7px;
  border-left: 4px solid #1e3674;
}

::v-deep .ant-descriptions-view {
  border-radius: 0px;
}

::v-deep .ant-descriptions-bordered .ant-descriptions-item-label {
  background-color: rgb(250, 250, 250);
  text-align: center;
  width: 17%;
}

::v-deep .ant-descriptions-item-label,
.ant-descriptions-item-content {
  color: rgb(96, 98, 102) !important;
}

::v-deep .ant-descriptions-bordered .ant-descriptions-item-content {
  word-break: break-word;
  white-space: normal;
}
</style>