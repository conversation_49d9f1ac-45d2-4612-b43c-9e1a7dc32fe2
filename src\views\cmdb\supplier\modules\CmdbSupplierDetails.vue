<template>
  <div class='lookTop'>
    <a-card>
    <a-row>
      <a-col :span="24">
        <div class='lookEditionWrapper'>
          <div  class='lookEdition' @click="handleEditSupplier(supplierInfo)" v-has="'supplier:edit'">
            <a-icon type="edit"  class='editIcon'/>编辑
          </div>
          <div class='lookReturn'>
            <img src='../../../../assets/return1.png' alt="" @click="getGo" class='returnImage' />
          </div>
        </div>
      </a-col>
      <a-col :span="24" style='margin-bottom: 20px'>
        <a-descriptions :column="{ xxl: 2, xl: 2, lg: 2, md: 2, sm: 1, xs: 1 }" bordered>
          <a-descriptions-item label="供应商名称" v-if="supplierInfo.name">{{ supplierInfo.name }}</a-descriptions-item>
          <a-descriptions-item label="联系人名称" v-if="supplierInfo.contactName">{{ supplierInfo.contactName }}</a-descriptions-item>
          <a-descriptions-item label="联系人邮箱" v-if="supplierInfo.contactEmail">{{ supplierInfo.contactEmail }}</a-descriptions-item>
          <a-descriptions-item label="联系人电话" v-if="supplierInfo.contactTel">{{ supplierInfo.contactTel }}</a-descriptions-item>
          <a-descriptions-item label="网 站" v-if="supplierInfo.website">{{ supplierInfo.website}}</a-descriptions-item>
        </a-descriptions>
      </a-col>
      <a-col :span="24">
        <div class="table-operator">
          <a-button @click="handleAddDevice">新增</a-button>
          <a-dropdown v-if="selectedRowKeys.length > 0">
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item key="1" @click="batchDel">删除</a-menu-item>
            </a-menu>
            <a-button> 批量操作 <a-icon type="down" /></a-button>
          </a-dropdown>
          <div>
            <a-table
              ref="table"
              bordered
              rowKey="id"
              :columns="columns"
              :dataSource="dataSource"
              :pagination="ipagination"
              :loading="loading"
              :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
              class="j-table-force-nowrap"
              @change="handleTableChange"
            >
              <span slot="action" slot-scope="text, record">
              <span>
                <a style="color: #409eff" @click="handleEditDevice(record)">编辑</a>
              </span>
             <span >
               <a-divider type="vertical" />
               <a style="color: #409eff" @click="handleDeleteDevice(record)">删除</a>
             </span>
            </span>
            </a-table>
          </div>
        </div>
      </a-col>
    </a-row>
  </a-card>
    <innovated-device-modal ref="modalForm" @ok="modalFormOk"></innovated-device-modal>
    <cmdb-supplier-modal ref="supplierModal" @ok="querySupplierInfo"></cmdb-supplier-modal>
  </div>
</template>

<script>
import { deleteAction, getAction } from '@api/manage'
import CmdbSupplierModal from './CmdbSupplierModal.vue'
import InnovatedDeviceModal from '@views/cmdb/InnovatedDevice/modules/InnovatedDeviceModal.vue'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
export default {
  name: 'CmdbSupplierDetails',
  mixins: [JeecgListMixin],
  components: {
    InnovatedDeviceModal,
    CmdbSupplierModal,
  },
  props: {
    data: {
      type: Object,
    },
  },
  data() {
    return {
      disableMixinCreated: true,
      supplierInfo:{},
      columns: [
        {
          title: '型号',
          dataIndex: 'model'
        },
        {
          title: '产品分类',
          dataIndex: 'productCategoryText'
        },
        {
          title: '所属期',
          dataIndex: 'catalogueText'
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 150,
          scopedSlots: { customRender: 'action' },
        },
      ],
      url: {
        querySupplierInfoById: '/supplier/cmdbSupplier/queryById',
        list:'/itInnovate/cmdbItInnovate/list',
        deleteBatch: '/itInnovate/cmdbItInnovate/deleteBatch',
      },
    }
  },
  mounted() {
    this.supplierInfo=this.data
    this.queryParam.supplier=this.supplierInfo.id
    this.loadData(1)
  },
  methods: {
    //返回上一级
    getGo() {
      this.$parent.pButton1(0)
    },
    handleEditSupplier: function (record) {
      this.$refs.supplierModal.edit(record)
      this.$refs.supplierModal.title = '编辑'
      this.$refs.supplierModal.disableSubmit = false
    },
    querySupplierInfo() {
      getAction(this.url.querySupplierInfoById, { id: this.data.id }).then((res) => {
        if (res.code == 200) {
          this.supplierInfo = res.result
          this.queryParam.supplier=this.supplierInfo.id
          this.loadData(1)
        }
      })
    },
    //添加信创设备
    handleAddDevice: function () {
      this.$refs.modalForm.add({supplier:this.supplierInfo.id});
      this.$refs.modalForm.title = '新增';
      this.$refs.modalForm.disableSubmit = false;
      this.$refs.modalForm.disableSupplier=true
    },
    //编辑信创设备
    handleEditDevice: function (record) {
      this.$refs.modalForm.edit(record);
      this.$refs.modalForm.title = '编辑';
      this.$refs.modalForm.disableSubmit = false;
      this.$refs.modalForm.disableSupplier=true
    },
    //删除信创设备
    handleDeleteDevice(record) {
      if (!this.url.deleteBatch) {
        this.$message.error('请设置url.delete属性!')
        return
      }
      var that = this
      this.$confirm({
        title: '确认删除',
        okText: '是',
        cancelText: '否',
        content: '是否删除选中数据?',
        onOk: function() {
          that.loading = true
          deleteAction(that.url.deleteBatch, {
            ids: record.id
          }).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.loadData()
            } else {
              that.$message.warning(res.message)
              that.loadData()
            }
          })
        }
      })
    },
  },
}
</script>
<style lang='less' scoped>
@import '~@assets/less/lookPage.less';
@import '~@assets/less/common.less';
::v-deep .ant-descriptions-item-content {
   word-break: break-all;
}

</style>