<template>
  <div class='table-operator table-operator-style scroll'>
    <!-- table区域-begin -->
    <a-table
      style='margin-right: 1px'
      ref='table'
      bordered
      :rowKey='(record,index)=>{return index}'
      :loading='loading'
      :columns='columns'
      :dataSource='dataSource'
      :pagination='ipagination'
      :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
      @change='handleTableChange'>
      <template slot='tooltip' slot-scope='text'>
        <a-tooltip placement='topLeft' :title='text' trigger='hover'>
          <div class='tooltip'>
            {{ text }}
          </div>
        </a-tooltip>
      </template>
      <!-- 字符串超长截取省略号显示-->
      <span slot='templateContent' slot-scope='text'>
          <j-ellipsis :value='text' :length='25' />
        </span>

      <span slot='action' slot-scope='text, record' class='caozuo'>
          <a @click="submitForm(record, 'PowerOn')">开机</a>
          <a-divider type='vertical' />
          <a @click="submitForm(record, 'PowerOff')">关机</a>
        <!-- <a-divider type="vertical" />
        <a @click="submitForm(record, 'ResetVM')">重启</a> -->
        </span>
    </a-table>
  </div>
</template>

<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import {tableOperationColumnVisibility} from '@/mixins/tableOperationColumnVisibility'
import JEllipsis from '@comp/jeecg/JEllipsis.vue'
import { getAction, postAction } from '@api/manage'

export default {
  name: 'CloudHostList',
  mixins: [JeecgListMixin,tableOperationColumnVisibility],
  components: {
    JEllipsis
  },
  data() {
    return {
      // 表头
      columns: [
        {
          title: 'IP',
          dataIndex: 'ip'
        },
        {
          title: '设备名称',
          dataIndex: 'awname'
        },
        {
          title: '当前CPU使用核数',
          dataIndex: 'cpuRateOs',
          customCell: () => {
            let cellStyle = 'text-align: right'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '当前内存使用数量',
          dataIndex: 'memRateOs',
          customCell: () => {
            let cellStyle = 'text-align: right'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '设备状态',
          dataIndex: 'status'
        },
        {
          title: '数据中心名称',
          dataIndex: 'regionKey',
          scopedSlots: {
            customRender: 'tooltip'
          },
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 120px;max-width:400px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '设备描述',
          dataIndex: 'description',
          scopedSlots: {
            customRender: 'tooltip'
          },
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 150px;max-width:400px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '项目名称',
          dataIndex: 'projectName',
          scopedSlots: {
            customRender: 'tooltip'
          },
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 150px;max-width:400px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '部门名称',
          dataIndex: 'domainName',
          scopedSlots: {
            customRender: 'tooltip'
          },
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 150px;max-width:400px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '操作',
          dataIndex: 'action',
          fixed: 'right',
          width: 100,
          align: 'center',
          scopedSlots: {
            customRender: 'action'
          }
        }
      ],
      url: {
        list: '/alarm/alarmTemplate/cloudByDevId',
      },
      disableMixinCreated: true,
      deviceInfo: {}
    }
  },
  watch: {
    deviceInfo: function(newVal, oldVal) {
      this.init(newVal)
    }
  },
  created() {
    this.setTabelOperationCol('operationColumnVisibility','cloudHostOperVis')
  },
  methods: {
    show(record) {
      this.deviceInfo = record
    },
    init(record) {
      this.queryParam.deviceId = record.id
      this.loadData()
    },
    handleDetailPage: function(record) {
      this.$parent.pButton2(1, record)
    },
    refreshVmware() {
      getAction('/alarm/alarmTemplate/refreshVmware', {
        ip: this.data.ip,
        username: 'root',
        password: 'vms1!123',
        vmwareName: 'ZABBIX-41'
      }).then((res) => {
        if (res.success) {
        }
      })
    },
    submitForm(values, data) {
      var unixtime = new Date().getTime()
      const that = this

      that.confirmLoading = true
      let httpurl = '/device/deviceInfo/execute'

      let formData = {}
      // formData.productId = this.record.productId
      formData.deviceId = this.queryParam.deviceId
      // formData.commandId = this.record.id
      formData.uId = values.uId
      formData.methodName = data
      formData.vmwareName = values.name
      formData.transferProtocol = 'CLOUD'
      postAction(httpurl, formData)
        .then((res) => {
          if (res.success) {
            that.$message.success(res.result)
            // if (data == 'PowerOnVM') {
            //   setTimeout(() => {
            //     this.refreshVmware()
            //   }, 5000)
            // } else if (data == 'PowerOffVM') {
            //   this.refreshVmware()
            // }
          } else {
            that.$message.warning(res.result)
          }
        })

        .finally(() => {
          that.confirmLoading = false
        })
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';

.scroll {
  height: 100%;
  overflow: hidden;
  overflow-y: auto;
}
</style>