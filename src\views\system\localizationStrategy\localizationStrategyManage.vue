<template>
    <div style="height:100%">
      <component :is="pageName" :data="data" />
    </div>
  </template>
  <script>
    import localizationStrategyList from './localizationStrategyList'
    import LocalizationStrategyDetail from './modules/LocalizationStrategyDetail'
    export default {
      name: "DevopePatchInfoManage",
      data() {
        return {
          isActive: 0,
          data: {}
        };
      },
      components: {
        localizationStrategyList,
        LocalizationStrategyDetail
      },
      created() {
        this.pButton1(0);
      },
      //使用计算属性
      computed: {
        pageName() {
          switch (this.isActive) {
            case 0:
              return "localizationStrategyList";
              break;
  
            default:
              return "LocalizationStrategyDetail";
              break;
          }
        }
      },
      methods: {
        pButton1(index) {
          this.isActive = index;
        },
        pButton2(index, item) {
          this.isActive = index;
          this.data = item;
        }
      }
    }
  </script>