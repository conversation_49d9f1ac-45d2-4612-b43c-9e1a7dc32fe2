<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <!-- 操作按钮区域 -->
      <a-card :bordered="false" style="width: 100%; flex: auto">
        <div class="table-operator table-operator-style">
          <a-button @click="handleAdd">新增</a-button>
          <a-dropdown v-if="selectedRowKeys.length > 0">
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item key="1" @click="batchDel">
                删除
              </a-menu-item>
            </a-menu>
            <a-button>
              批量操作
              <a-icon type="down" />
            </a-button>
          </a-dropdown>
        </div>
        <!-- table区域-begin -->
        <a-table
          ref="table"
          rowKey="id"
          bordered
          :columns="columns"
          :dataSource="dataSource"
          :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
          :pagination="ipagination"
          :loading="loading"
          :expandedRowKeys="expandedRowKeys"
          @change="handleTableChange"
          @expand="handleExpand"
          v-bind="tableProps"
        >
          <span class="caozuo" slot="action" slot-scope="text, record">
            <a @click="handleEdit(record)">编辑</a>
            <a-divider type="vertical" />
            <a @click="handleAddChild(record)">新增下级</a>
            <a-divider type="vertical" />
            <a-popconfirm title="确定删除吗?" @confirm="() => handleDeleteNode(record.categoryTreeCode)" placement="topLeft">
              <a>删除</a>
            </a-popconfirm>
          </span>
           <template slot="categoryName" slot-scope="text">
           <span>{{text}}</span>
          </template>
          <template slot="tooltip" slot-scope="text">
            <a-tooltip placement="topLeft" :title="text" trigger="hover">
              <div class="tooltip">
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
      </a-card>
      <log-Category-modal ref="modalForm" @ok="modalFormOk"></log-Category-modal>
      <!-- 下载模版 -->
      <iframe id="download" style="display: none"></iframe>
    </a-col>
  </a-row>
</template>

<script>
import { getAction, deleteAction } from '@api/manage'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import logCategoryModal from './modules/logCategoryModal'
import JSuperQuery from '@comp/jeecg/JSuperQuery.vue'

export default {
  name: 'logCategoryList',
  mixins: [JeecgListMixin],
  components: {
    logCategoryModal,
    JSuperQuery,
  },
  data() {
    return {
      description: '日志分类列表页面',
      // 表头
      columns: [
        {
          title: '类型名称',
          dataIndex: 'categoryName' ,
          scopedSlots: { customRender: 'categoryName' },
          customCell: () => {
            let cellStyle = 'text-align: left'
            return { style: cellStyle }
          },
        },
        {
          title: '类型编号',
          dataIndex: 'categoryCode'
        },
        {
          title: '描述',
          dataIndex: 'categoryDescribe',
          scopedSlots: { customRender: 'tooltip' },
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 100px;max-width:400px'
            return { style: cellStyle }
          },
        },
        {
          title: '操作',
          dataIndex: 'action',
          fixed: 'right',
          width: 180,
          align: 'center',
          scopedSlots: { customRender: 'action' },
        },
      ],
      url: {
        list: '/log/logCategory/list',
        getChildListBatch: '/log/logCategory/childList',
        delete: '/log/logCategory/delete',
        deleteBatch: '/log/logCategory/deleteBatch'
      },
      expandedRowKeys: [],
      hasChildrenField: 'hasChild',
      pidField: 'parentId',
      dictOptions: {},
      loadParent: false
    }
  },
  created() {},
  computed: {
    tableProps() {
      let _this = this
      return {
        // 列表项是否可选择
        rowSelection: {
          selectedRowKeys: _this.selectedRowKeys,
          onChange: (selectedRowKeys) => (_this.selectedRowKeys = selectedRowKeys),
        },
      }
    },
  },
  methods: {
    loadData(arg) {
      if (arg == 1) {
        this.ipagination.current = 1
      }
      this.loading = true
      let params = this.getQueryParams()
      getAction(this.url.list, params)
        .then((res) => {
          if (res.success) {
            let result = res.result
            if (Number(result.total) > 0) {
              this.ipagination.total = Number(result.total)
              this.dataSource = this.getDataByResult(res.result.records)
              return this.loadDataByExpandedRows(this.dataSource)
            } else {
              this.ipagination.total = 0
              this.dataSource = []
            }
          } else {
            this.$message.warning(res.message)
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 根据已展开的行查询数据（用于保存后刷新时异步加载子级的数据）
    loadDataByExpandedRows(dataList) {
      if (this.expandedRowKeys.length > 0) {
        return getAction(this.url.getChildListBatch, { parentIds: this.expandedRowKeys.join(',') }).then((res) => {
          if (res.success && res.result.length > 0) {
            //已展开的数据批量子节点
            let records = res.result
            const listMap = new Map()
            for (let item of records) {
              let pid = item[this.pidField]
              if (this.expandedRowKeys.join(',').includes(pid)) {
                let mapList = listMap.get(pid)
                if (mapList == null) {
                  mapList = []
                }
                mapList.push(item)
                listMap.set(pid, mapList)
              }
            }
            let childrenMap = listMap
            let fn = (list) => {
              if (list) {
                list.forEach((data) => {
                  if (this.expandedRowKeys.includes(data.id)) {
                    data.children = this.getDataByResult(childrenMap.get(data.id))
                    fn(data.children)
                  }
                })
              }
            }
            fn(dataList)
          }
        })
      } else {
        return Promise.resolve()
      }
    },
    searchReset() {
      //重置
      this.expandedRowKeys = []
      this.queryParam = {}
      this.loadData(1)
    },
    getDataByResult(result) {
      if (result) {
        return result.map((item) => {
          //判断是否标记了带有子节点
          if (item[this.hasChildrenField] == '1') {
            let loadChild = { id: item.id + '_loadChild', name: 'loading...', isLoading: true }
            item.children = [loadChild]
          }
          return item
        })
      }
    },
    handleExpand(expanded, record) {
      // 判断是否是展开状态
      if (expanded) {
        this.expandedRowKeys.push(record.id)
        if (record.children.length > 0 && record.children[0].isLoading === true) {
          let params = this.getQueryParams(1) //查询条件
          params.parentIds = record.id
          getAction(this.url.getChildListBatch, params).then((res) => {
            if (res.success) {
              if (res.result) {
                record.children = this.getDataByResult(res.result)
                this.dataSource = [...this.dataSource]
              } else {
                record.children = ''
                record.hasChildrenField = '0'
              }
            } else {
              this.$message.warning(res.message)
            }
          })
        }
      } else {
        let keyIndex = this.expandedRowKeys.indexOf(record.id)
        if (keyIndex >= 0) {
          this.expandedRowKeys.splice(keyIndex, 1)
        }
      }
    },
    handleAddChild(record) {
      this.loadParent = true
      let obj = {}
      obj[this.pidField] = record['id']
      this.$refs.modalForm.add(obj)
    },
    handleDeleteNode(treeCode) {
      if (!this.url.delete) {
        this.$message.error('请设置url.delete属性!')
        return
      }
      var that = this
      deleteAction(that.url.delete, { treeCode: treeCode }).then((res) => {
        if (res.success) {
          this.searchReset()
        } else {
          that.$message.warning(res.message)
        }
      })
    },
    batchDel: function () {
      if (this.selectedRowKeys.length <= 0) {
        this.$message.warning('请选择一条记录！')
        return
      } else {
        // 递归查找所有选中的节点（包括子级）的 categoryTreeCode
        const getAllCategoryCodes = (data, selectedIds) => {
          let codes = []
          data.forEach(item => {
            if (selectedIds.includes(item.id)) {
              codes.push(item.categoryTreeCode) // 当前节点的 categoryTreeCode
            }
            // 如果有子节点，递归查找
            if (item.children && item.children.length > 0) {
              codes = codes.concat(getAllCategoryCodes(item.children, selectedIds))
            }
          })
          return codes
        }
        // 获取所有选中的 categoryCode（包括子级）
        const categoryTreeCodes = getAllCategoryCodes(this.dataSource, this.selectedRowKeys)
        const treeCodes = categoryTreeCodes.join(',')
        var that = this
        this.$confirm({
          title: '确认删除',
          okText: '是',
          cancelText: '否',
          content: '是否删除选中数据?',
          onOk: function () {
            that.loading = true
            deleteAction(that.url.deleteBatch, { treeCodes: treeCodes })
              .then((res) => {
                if (res.success) {
                  that.searchReset()
                  //重新计算分页问题
                  that.reCalculatePage(that.selectedRowKeys.length)
                  that.$message.success(res.message)
                  that.loadData()
                  that.onClearSelected()
                } else {
                  that.$message.warning(res.message)
                }
              })
              .finally(() => {
                that.loading = false
              })
          }
        })
      }
    },
  },
}
</script>
<style lang='less' scoped>
.pro-icon{
  display: inline-block;
  padding:3px;
  margin-right: 5px;
  background: #F0F0F0;
  img{
    width:20px;height:20px
  }
}
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
</style>
