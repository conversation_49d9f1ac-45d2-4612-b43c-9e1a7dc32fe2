<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    :destroyOnClose="true"
    switchFullscreen
    @ok="handleOk"
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }"
    @cancel="handleCancel"
    cancelText="关闭"
  >
    <device-info-modal
      ref="realForm"
      @ok="submitCallback"
      :data="data"
      :render-states="renderStates"
      hideBack
    ></device-info-modal>
  </j-modal>
</template>

<script>
export default {
  name: 'TopoDeviceModal',
  components: {
    DeviceInfoModal:()=>import('@views/devicesystem/deviceshow/DeviceInfoModal.vue'),
  },
  data() {
    return {
      title: '',
      width: 1200,
      visible: false,
      disableSubmit: false,
      showabled: false,
      data: null,
      renderStates: {
        showBaseInfo: true,
        showStateInfo: true,
        showDataAnalysis: true,
        showDeviceFunction: true,
        showJournal: true,
        showAlarm: true,
        showDeviceAlarm: true,
      },
    }
  },
  methods: {
    add() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.add()
      })
    },
    edit(record) {
      this.visible = true
      this.data = record
      this.$nextTick(() => {
        this.$refs.realForm.backFlag = false
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      this.close()
    },
    submitCallback() {
      this.$emit('ok')
      this.visible = false
    },
    handleCancel() {
      this.close()
    },
  },
}
</script>