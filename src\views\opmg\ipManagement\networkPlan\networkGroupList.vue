<template>
  <a-col :xl='20' :lg='18' :md='16' :sm='14' :xs='14' style='height: 100%; overflow: hidden; overflow-y: auto'>
    <a-row style='height: 100%; margin-left: 16px; margin-right: 4px'>
      <a-col style='width: 100%; height: 100%; display: flex; flex-direction: column'>
        <!-- 查询区域 -->
        <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0', marginRight: '12px' }" class='card-style'
          style='width: 100%'>
          <div class='table-page-search-wrapper'>
            <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
              <a-row :gutter='24' ref='row'>
                <a-col :span='spanValue'>
                  <a-form-item label='子网组名称'>
                    <a-input placeholder='请输入子网组名称' v-model='queryParam.subnetGroupName' :allowClear='true'
                      autocomplete='off' :maxLength="maxLength"/>
                  </a-form-item>
                </a-col>
                <a-col :span='spanValue'>
                  <a-form-item label='使用部门'>
                    <a-input placeholder='请输入使用部门' v-model='queryParam.departName' :allowClear='true'
                      autocomplete='off' :maxLength="maxLength"/>
                  </a-form-item>
                </a-col>
                <a-col :span='spanValue'>
                  <a-form-item label='使用位置'>
                    <a-input placeholder='请输入使用位置' v-model='queryParam.location' :allowClear='true' autocomplete='off'
                      :maxLength="maxLength" />
                  </a-form-item>
                </a-col>
                <a-col :span='colBtnsSpan()'>
                  <span class='table-page-search-submitButtons'
                    :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                    <a-button type='primary' class='btn-search btn-search-style' @click='searchQuery'>查询</a-button>
                    <a-button class='btn-reset btn-reset-style' @click='searchReset'>重置</a-button>
                    <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                      {{ toggleSearchStatus ? '收起' : '展开' }}
                      <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                    </a>
                  </span>
                </a-col>
              </a-row>
            </a-form>
          </div>
        </a-card>
        <a-card :bordered='false' style='width: 100%; flex: auto'>
          <div class='table-operator table-operator-style'>
            <a-button @click='handleAdd'>添加子网组</a-button>
            <a-dropdown v-if='selectedRowKeys.length > 0'>
              <a-menu slot="overlay" style='text-align: center'>
                <a-menu-item key='1' @click='batchDel'>删除</a-menu-item>
              </a-menu>
              <a-button> 批量操作
                <a-icon type='down' />
              </a-button>
            </a-dropdown>
          </div>
          <a-table ref='table' bordered :row-key='(record, index) => {return record.id}' :columns='columns'
            :dataSource='dataSource' :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
            :pagination='ipagination' :loading='loading'
            :rowSelection='{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }' @change='handleTableChange'>
            <span slot='action' class='caozuo' slot-scope='text, record'>
              <a @click='handleDetailPage1(record)'>查看</a>
              <a-divider type='vertical' />
              <a-dropdown>
                <a class='ant-dropdown-link'>更多
                  <a-icon type='down' /></a>
                <a-menu slot='overlay'>
                  <a-menu-item>
                    <a @click='handleEdit(record)' style="color: #409eff">编辑</a>
                  </a-menu-item>
                  <a-menu-item>
                    <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                      <a style="color: #409eff">删除</a>
                    </a-popconfirm>
                  </a-menu-item>
                </a-menu>
              </a-dropdown>
            </span>
          </a-table>
        </a-card>
      </a-col>
    </a-row>
    <network-plan-modal ref='modalForm' @ok='modalFormOk'> </network-plan-modal>
  </a-col>
</template>

<script>
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import networkPlanModal from './modules/networkPlanModal.vue'
  import {
    getAction
  } from '@/api/manage'
  import {
    YqFormSearchLocation
  } from '@/mixins/YqFormSearchLocation'
  export default {
    mixins: [JeecgListMixin, YqFormSearchLocation],
    components: {
      networkPlanModal
    },
    data() {
      return {
        maxLength:50,
        formItemLayout: {
          labelCol: {
            style: 'width:80px'
          },
          wrapperCol: {
            style: 'width:calc(100% - 80px)'
          }
        },
        // 子网组表头
        columns: [{
            title: '子网组名称',
            dataIndex: 'subnetGroupName',
            customCell: () => {
              let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '子网组简称',
            dataIndex: 'subnetGroupNickname',
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 150px;max-width:300px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '使用部门',
            dataIndex: 'departName',
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 100px;max-width:300px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '使用位置',
            dataIndex: 'location',
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 100px;max-width:300px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            fixed: 'right',
            width: 140,
            scopedSlots: {
              customRender: 'action'
            }
          }
        ],
        url: {
          list: '/devops/ip/subnetGroup/list',
          delete: '/devops/ip/subnetGroup/delete',
          deleteBatch: '/devops/ip/subnetGroup/deleteBatch',
        },
      };
    },
    methods: {
      loadData(arg) {
        if (!this.url.list) {
          this.$message.error('请设置url.list属性!')
          return
        }
        //加载数据 若传入参数1则加载第一页的内容
        if (arg === 1) {
          this.ipagination.current = 1
        }

        var params = this.getQueryParams() //查询条件
        this.loading = true
        getAction(this.url.list, params).then((res) => {
          if (res.success) {
            //update-begin---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
            this.dataSource = res.result.records || res.result
            if (this.dataSource.length < 9) {
              this.clientHeight = false
            }
            //author:weng    Date:20210402  for：if(res.result.total>0) 有错误，无查询结果时，页码显示有问题
            this.ipagination.total = res.result.total ? res.result.total : 0
            //update-end---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
          }
          if (res.code === 510) {
            this.$message.warning(res.message)
          }
          this.loading = false
        })
        this.$emit('refresh')
      },
      handleDetailPage1(record) {
        this.$emit('detail', 1, record)
      },
    }
  }
</script>

<style scoped lang="less">
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';
</style>