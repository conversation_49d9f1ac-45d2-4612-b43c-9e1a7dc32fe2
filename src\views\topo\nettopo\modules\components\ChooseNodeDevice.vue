<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    :destroyOnClose="true"
    :closable="false"
    :maskClosable="false"
    @cancel="hide"
    @ok="handleOk"
    cancelText="取消"
  >
    <div>
      <a-input placeholder="输入关键字进行过滤" v-model="filterText"></a-input>
      <vue-easy-tree
        ref="veTree"
        node-key="id"
        :data="assetsCategoryTree"
        :props="replaceFields"
        height="calc(60vh - 50px)"
        :filter-node-method="filterNode"
      >
        <div class="custom-tree-node" slot-scope="{ node, data }">
          <div v-if="data.nodeType === 'device'" @click="chooseDevice(data)">
            <img
              v-if="data.icon != null && data.icon.length > 0"
              :src="setUrl(data.icon)"
              style="width: 20px; height: 20px; margin-right: 8px"
            />
            <span :style="{ color: selectedDevice && selectedDevice.code === data.code ? '#1890ff' : '' }">{{
              node.label
            }}</span>
          </div>
          <div v-else-if="data.code === 'virtual'" @click="chooseDevice(data)">
            <span :style="{ color: selectedDevice && selectedDevice.nodeType ==='virtual' ? '#1890ff' : '' }">{{ node.label }}</span>
          </div>
          <div v-else>
            <span>{{ node.label }}</span>
          </div>
        </div>
      </vue-easy-tree>
    </div>
  </j-modal>
</template>
<script>
import VueEasyTree from '@wchbrad/vue-easy-tree'
import { getAction, postAction, httpAction } from '@/api/manage'
export default {
  components: {
    VueEasyTree,
  },
  data() {
    return {
      title: '选择节点设备',
      width: 600,
      visible: false,
      disableSubmit: false,
      assetsCategoryTree: [],
      filterText: '',
      replaceFields: {
        label: 'name',
        key: 'id',
      },
      draggable: true,
      picSrc: window._CONFIG['staticDomainURL'],
      url: {
        treeUrl: '/topo/device/tree',
      },
      selectedDevice: null,
    }
  },
  created() {
    this.loadTree()
  },
  watch: {
    filterText(val) {
      this.$refs.veTree.filter(val)
    },
  },
  methods: {
    show() {
      this.selectedDevice = null
      this.visible = true
    },
    hide() {
      this.visible = false
    },
    handleOk(key, type, name) {
      if (this.selectedDevice) {
        if (this.selectedDevice.nodeType === 'virtual') {
          this.$emit('chooseVirtual')
        } else {
          this.$emit('ok', this.selectedDevice)
        }
        this.hide()
      } else {
        this.$message.warning('请选择节点设备！')
      }
    },
    // 加载设备树列表
    loadTree() {
      var that = this
      that.assetsCategoryTree = []
      //为tree生成对应地title slot
      const generateSlotScopes = (data) => {
        for (let i = 0; i < data.length; i++) {
          data[i].scopedSlots = {
            title: 'title',
          }
          if (data[i].children) {
            generateSlotScopes(data[i].children)
          }
        }
      }
      getAction(this.url.treeUrl).then((res) => {
        if (res.success) {
          generateSlotScopes(res.result)
          that.assetsCategoryTree = res.result.filter((el) => {
            if (el.children.length <= 0) {
              return false
            } else if (el.children.find((cel) => cel.nodeType === 'topo')) {
              return false
            }
            return true
          })
          let virtual = {
            code: 'virtual',
            icon: '',
            id: 'virtual-device',
            internetFlag: 2,
            name: '虚拟设备',
            nodeType: 'virtual',
          }
          this.assetsCategoryTree.push(virtual)
          // console.log('设备树 === ', that.assetsCategoryTree)
        }
      })
    },
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    chooseDevice(data) {
      this.selectedDevice = data
      // console.log('选择的设备 === ', this.selectedDevice)
    },
    setUrl(url) {
      if (url.includes('http')) {
        return url
      } else {
        return this.picSrc + '/' + url
      }
    }
  },
}
</script>

<style lang="less" scoped>
.node-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
}

.group-div {
  width: calc(50% - 4px);
  height: 36px;
  border: 1px solid #1296db;
  border-radius: 4;
  display: flex;
  align-items: center;
  margin-top: 8px;
  .img-div {
    // margin: auto;
    width: 24px;
    height: 24px;
    margin: 0 10px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .label {
    font-weight: 700;
  }
}

.text-div {
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 36px;
}
</style>