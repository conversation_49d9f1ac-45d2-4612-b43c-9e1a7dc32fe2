<template>
  <a-spin :spinning='confirmLoading'>
    <j-form-container>
      <a-form :form='form' slot='detail'>
        <a-row :gutter='24'>
          <a-col :span='12'>
            <a-form-item label='任务名称' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <a-input v-decorator="['taskName', validatorRules.taskName]" :allowClear='true' autocomplete='off'
                       placeholder='请输入任务名称'></a-input>
            </a-form-item>
          </a-col>
          <a-col :span='12'>
            <a-form-item label='数据库' :labelCol='labelCol' :wrapperCol='wrapperCol' prop='targetDb'>
              <a-select v-decorator="['targetDb', validatorRules.targetDb]" placeholder='请选择数据资源库'
                        style='width: 100%' :allowClear='true' :options='dataBases'>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span='12'>
            <a-form-item label='备份级别' :labelCol='labelCol' :wrapperCol='wrapperCol' prop='backLevel'>
              <a-select v-decorator="['backLevel', validatorRules.backLevel]" :options='backLevels'
                        placeholder='请选择备份级别' :allowClear='true'
                        style='width: 100%'>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span='12' v-if='form.getFieldValue("backLevel")=="schema"'>
            <a-form-item label='待备份模式' :labelCol='labelCol' :wrapperCol='wrapperCol' prop='backLevelValue'>
              <a-input v-decorator="['backLevelValue', validatorRules.backLevelValueS]" :allowClear='true'
                       autocomplete='off'
                       placeholder='请输入待备份模式'></a-input>
            </a-form-item>
          </a-col>
          <a-col :span='12' v-else-if='form.getFieldValue("backLevel")=="table"'>
            <a-form-item label='待备份表名' :labelCol='labelCol' :wrapperCol='wrapperCol' prop='backLevelValue'>
              <a-input v-decorator="['backLevelValue', validatorRules.backLevelValueT]" :allowClear='true'
                       autocomplete='off'
                       placeholder='请按schema.table格式输入待备份表名'>
                <a-tooltip slot='suffix' title='此处填写 *.tableName 表示转储数据库下所有模式下的特定表，schema.tableName 表示转储特定模式中的特定表'>
                  <a-icon type='info-circle' style='color: #0ABBF6' />
                </a-tooltip>
              </a-input>
            </a-form-item>
          </a-col>
          <a-col :span='12'>
            <a-form-item label='备份内容' :labelCol='labelCol' :wrapperCol='wrapperCol' prop='backContent'>
              <a-select v-decorator="['backContent', validatorRules.backContent]" :options='backContents'
                        placeholder='请选择备份内容' :allowClear='true'
                        style='width: 100%'>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span='12'>
            <a-form-item label='备份格式' :labelCol='labelCol' :wrapperCol='wrapperCol' prop='backFormat'>
              <a-select v-decorator="['backFormat', validatorRules.backFormat]" :options='backFormats'
                        placeholder='请选择备份格式'
                        style='width: 100%'>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span='12'>
            <a-form-item label='备份路径' :labelCol='labelCol' :wrapperCol='wrapperCol' prop='backFilePath'>
              <a-input v-decorator="['backFilePath', validatorRules.backFilePath]" :allowClear='true' autocomplete='off'
                       placeholder='请输入备份路径'>
                <a-tooltip slot='suffix' title='此处要求填写gbase用户拥有读写权限的文件路径'>
                  <a-icon type='info-circle' style='color: #0ABBF6' />
                </a-tooltip>
              </a-input>
            </a-form-item>
          </a-col>
          <a-col :span='12'>
            <a-form-item label='备份文件名' :labelCol='labelCol' :wrapperCol='wrapperCol' prop='backFileName'>
              <a-input v-decorator="['backFileName', validatorRules.backFileName]" :allowClear='true' autocomplete='off'
                       placeholder='请输入备份目的文件名'>
                <a-tooltip slot='suffix' title='无需填写文件拓展名，例如：backUp_test_0924'>
                  <a-icon type='info-circle' style='color: #0ABBF6' />
                </a-tooltip>
              </a-input>
            </a-form-item>
          </a-col>
          <a-col :span='12'>
            <a-form-item label='备份文件名后缀' :labelCol='labelCol' :wrapperCol='wrapperCol' prop='backFileSuffix'>
              <div style='display: flex;align-items: center'>
                <a-select v-decorator="['backFileSuffix', validatorRules.backFileSuffix]" :options='backFileSuffixs'
                          placeholder='请选择备份文件名后缀'
                          style='width: 100%'>

                </a-select>
                <span style='margin-left: 8px'>
                   <a-tooltip slot='suffix' title='备份文件名的命名策略，支持在文件名称末尾追加时间戳或日期串等内容'>
                  <a-icon type='info-circle' style='color: #0ABBF6' />
                </a-tooltip>
                </span>
              </div>

<!--              <a-input v-decorator="['backFileSuffix', validatorRules.backFileSuffix]" :allowClear='true'-->
<!--                       autocomplete='off'-->
<!--                       placeholder='请输入备份目的文件名后缀'></a-input>-->
            </a-form-item>
          </a-col>

          <a-col :span='12'>
            <a-form-item label='执行方式' :labelCol='labelCol' :wrapperCol='wrapperCol' prop='executeType'>
              <a-radio-group v-decorator="['executeType',{initialValue: '1'}]">
                <a-radio value='1'> 手动</a-radio>
                <a-radio value='0'> 周期</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
          <a-col :span='12' v-if='form.getFieldValue("executeType")=="0"'>
            <a-form-item label='备份频率' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <!-- cron表达式  -->
              <j-cron ref='innerVueCron' v-decorator="['executeCron', validatorRules.executeCron]" @change='setCorn'
                      :hideYear='true'></j-cron>
            </a-form-item>
          </a-col>
          <a-col :span='12'>
            <a-form-item label='状态' :labelCol='labelCol' :wrapperCol='wrapperCol' prop='taskStatus'>
              <a-radio-group v-decorator="['taskStatus',{initialValue: '1'}]">
                <a-radio value='1'> 启用</a-radio>
                <a-radio value='0'> 禁用</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
          <a-col :span='12'>
              <a-form-item label='响应等待时间' :labelCol='labelCol' :wrapperCol='wrapperCol' prop='shellAwaitTime'>
                <a-input-number style='width: 100%'  v-decorator="['shellAwaitTime', validatorRules.shellAwaitTime]" :min='1000'
                                :allow-clear='true'
                                autocomplete='off'
                                :formatter='value => `${value}ms`'
                                :parser="value => value.replace('ms', '')"
                                placeholder='响应等待时间' />
              </a-form-item>
        </a-col>
          <a-col :span='12'>
            <a-form-model-item label='描述' :labelCol='labelCol' :wrapperCol='wrapperCol' prop='description'>
              <a-textarea style='width: 100%' v-decorator="['description', validatorRules.description]"
                          :autoSize='{minRows:2,maxRows:4}'
                          :allow-clear='true' autocomplete='off' placeholder='请输入描述' />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
import {
  httpAction, getAction, deleteAction
} from '@/api/manage'
import pick from 'lodash.pick'
import JFormContainer from '@/components/jeecg/JFormContainer'
import JCron from '@/components/jeecg/JCron.vue'
import {
  JeecgListMixin
} from '@/mixins/JeecgListMixin'
import { ajaxGetDictItems } from '@api/api'

const _ = require('lodash/object')
export default {
  name: 'BfStrategyForm',
  mixins: [JeecgListMixin],
  props: {
    backLevels: {
      type: Array,
      default: () => {
        return []
      }
    }, backContents: {
      type: Array,
      default: () => {
        return []
      }
    },
    backFormats: {
      type: Array,
      default: () => {
        return []
      }
    }, dataBases: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  components: {
    JFormContainer,
    JCron
  },
  data() {
    return {
      form: this.$form.createForm(this),
      model: { },
      labelCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 5
        },
        md: {
          span: 6
        },
        lg: {
          span: 6
        }
      },
      wrapperCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 19
        },
        md: {
          span: 18
        },
        lg: {
          span: 18
        }
      },
      confirmLoading: false,
      validatorRules: {
        taskName: {
          rules: [{
            required: true,
            message: '请输入任务名称'
          },
            {
              min: 1,
              max: 30,
              message: '长度在1到30个字符',
              trigger: 'blur'
            }
          ]
        }, targetDb: {
          rules: [{
            required: true,
            message: '请选择数据库'
          }]
        }, backLevel: {
          rules: [{
            required: true,
            message: '请选择备份级别'
          }]
        }, backContent: {
          rules: [{
            required: true,
            message: '请选择备份内容'
          }]
        }, backFormat: {
          rules: [{
            required: true,
            message: '请选择备份格式'
          }]
        }, executeType: {
          rules: [{
            required: true,
            message: '请选择执行方式'
          }]
        }, backLevelValueS: {
          rules: [{
            required: true,
            message: '请输入待备份模式'
          },
            {
              min: 1,
              max: 100,
              message: '长度在1到100个字符',
              trigger: 'blur'
            }
          ]
        }, backLevelValueT: {
          rules: [{
            required: true,
            message: '请输入待备份表名'
          },
            {
              min: 1,
              max: 100,
              message: '长度在1到100个字符',
              trigger: 'blur'
            }
          ]
        },backFileSuffix: {
          rules: [{
            required: true,
            message: '请选择备份文件名后缀'
          }]
        },shellAwaitTime: {
          initialValue: 5000,
          rules: [{
            required: true,
            message: '请输入Shell响应等待时间'
          }]
        },
        executeCron: {
          initialValue: '0 0 0 * * ?',
          rules: [{
            required: true,
            validator: this.validateCorn
          }]
        },
        backFilePath: {
          initialValue: '/opt/gbase/database/tmp',
          rules: [{
            required: true,
            message: '请输入备份路径'
          },
            {
              min: 1,
              max: 500,
              message: '长度在1到500个字符',
              trigger: 'blur'
            }
          ]
        }, backFileName: {
          initialValue: '',
          rules: [{
            required: true,
            message: '请输入备份文件名'
          },
            {
              min: 1,
              max: 500,
              message: '长度在1到500个字符',
              trigger: 'blur'
            }
          ]
        },
        description: {
          rules: [{ required: false, min: 0, max: 255, message: '任务描述长度应在 0-255 之间' }]
        }
      },
      url: {
        add: '/gbase/task/add', // 添加任务
        edit: '/gbase/task/edit', // 编辑任务
        validateCorn: '/autoInspection/devopsAutoInspection/cronCheckSix' // 校验cron表达式
      },
      effectiveDate: null,
      taskId: undefined,
      effectiveStatus: '0', // 默认永久有效
      disableMixinCreated: true,
      backFileSuffixs:[],
    }
  },
  created() {
    this.getDictData()
  },
  methods: {
    //获取字典值
    getDictData() {
      ajaxGetDictItems('backFileSuffixFormat', null).then((res) => {
        this.backFileSuffixs = res.result
      })
    },
    closeForm() {
      this.$emit('closeForm')
    },
    add() {
      this.edit({})
    },
    edit(record) {
      this.queryParam.taskId = record.id
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      if(record.id){
        this.$nextTick(() => {
          this.form.setFieldsValue({ executeType: this.model.executeType })
          this.$nextTick(() => {
            let formdata = pick(this.model, ['taskName', 'targetDb', 'backLevel', 'backContent', 'backFormat', 'backFilePath', 'backFileName', 'backFileSuffix', 'shellAwaitTime', 'description'])
            console.log('modelmodle === >', formdata)
            this.formInit(formdata)
            if(this.model.executeType === "0"){
              this.form.setFieldsValue({  executeCron: this.model.executeCron })
            }
            if (this.model.backLevel !== 'tabel') {
              this.$nextTick(() => {
                this.form.setFieldsValue({ backLevelValue: this.model.backLevelValue })
              })
            }
          })


        })
      }

    },
    formInit(pickData) {
      this.form.setFieldsValue(
        pickData
      )
    },
    //提交
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (
          !err
        ) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }

          let formData = Object.assign(that.model, values)
          formData.taskType = '0'
          httpAction(httpurl, formData, method)
            .then((res) => {
              that.confirmLoading = false
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
                that.closeForm()
              } else {
                that.$message.warning(res.message)
              }
            })
            .catch((err) => {
              that.$message.warning(err.message)
              that.confirmLoading = false
            })
        }
      })
    },
    validateCorn(rule, value, callback) {
      if (rule.required) {
        if (value && value.length > 0) {
          getAction(this.url.validateCorn, {
            cronExpression: value
          }).then((res) => {
            if (res.success) {
              callback()
            } else {
              callback('cron表达式格式错误!')
            }
          })
        } else {
          callback('请输入cron表达式')
        }
      } else {
        callback()
      }
    },
    //周期执行
    setCorn(data) {
      if (data && data.target != null) {
        let dataList = data.target.value.split(' ')
        if (dataList[0] === '*') {
          this.$message.warning('请确认是否每秒都执行')
        }
      } else {
        let dataList = data.split(' ')
        if (dataList[0] === '*') {
          this.$message.warning('请确认是否每秒都执行')
        }
      }

      if (Object.keys(data).length === 0) {
        this.$message.warning('请输入cron表达式!')
      }
    }
  }
}
</script>
<style scoped lang='less'>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';

.colorBox {
  margin-top: 18px;
  margin-bottom: 18px;
}

.colorTotal {
  padding-left: 7px;
  border-left: 4px solid #1e3674;
}

/* 定义滚动条样式 */
::-webkit-scrollbar {
  width: 0.15rem
  /* 12/80 */;
  // height: 6px;
  //background-color: #222325;
}

/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track {
  box-shadow: inset 0 0 0px rgba(240, 240, 240, 0.5);
  // border-radius: 50%;
  background-color: #eaeaea;
  border-radius: 0.1rem
  /* 8/80 */;
}

/*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
  border-radius: 0.1rem
  /* 8/80 */;
  box-shadow: inset 0 0 0px rgba(240, 240, 240, 0.5);
  background-color: #d6d6d6;
}

::v-deep .ant-spin-nested-loading {
  padding-right: 13px;
}
</style>