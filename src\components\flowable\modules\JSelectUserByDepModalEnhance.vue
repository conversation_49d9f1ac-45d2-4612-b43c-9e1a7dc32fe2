<template>
  <j-modal
    :width="modalWidth"
    :visible="visible"
    :title="title"
    :centered='true'
    switchFullscreen
    wrapClassName='limit-height-modal'
    @ok="handleSubmit"
    @cancel="close"
    cancelText="关闭">
     <a-row class='row'>
      <a-col :xl='6' :lg='24' :md="24" :sm="24" class='col'>
        <a-card :bordered="false" class='card'>
          <!-- 全部 -->
          <div style='margin-bottom:5px;'>
            <div class='loadAllBtn' @click='loadAll' style='user-select: none'
              :style='{
                backgroundColor: selectedDepIds.length == 0 ? "#f1f1f1" : "#fff",
                color: selectedDepIds.length == 0 ? primaryColor : "rgba(0, 0, 0, 0.65)"
              }'>
              <a-icon type="appstore" />
              全部
            </div>
          </div>
          <!--组织机构-->
          <a-directory-tree v-if="refresh" selectable :selectedKeys="selectedDepIds" :checkStrictly="true" :treeData="departTree" :expandAction="false"
            @select="onDepSelect" :load-data="onLoadDepartment" />
        </a-card>
      </a-col>
      <a-col :xl='12' :lg='24' :md="24" :sm="24" class='col'>
        <a-card :bordered="false" class='card'>
          <a-form layout='inline'>
            <a-row :gutter='24' ref='row'>
              <a-col >
                <a-form-item label="用户名称">
                  <a-input-search  placeholder="请输入用户名称" v-model="queryParam.realname" @search="onSearch"/>
                </a-form-item>
                <a-form-item label='选择角色' v-if="showRole">
                  <a-select
                    :getPopupContainer='(target) => target.parentNode'
                    style="width: 200px"
                    placeholder='请选择用户角色'
                    v-model='queryParam.roleId'
                    :allowClear='true'
                    @change='changeRoles'
                  >
                    <a-select-option
                      v-for='(role, roleindex) in roleList'
                      :key="'role_'+roleindex.toString()"
                      :value='role.id'
                      :label='role.roleName'
                    >
                      {{ role.roleName }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
                <a-form-item>
                  <a-button @click="searchReset(1)" icon="redo">重置</a-button>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
          <!--用户列表-->
          <a-table ref="table" size="middle" rowKey="id" :columns="columns" :dataSource="dataSource"
            :pagination="ipagination"
            :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
            :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange,type: getType}"
            :loading="loading" @change="handleTableChange">
          </a-table>
        </a-card>
      </a-col>
      <a-col :xl='6' :lg='24' :md="24" :sm="24" class='col'>
        <a-card :bordered="false" class='card'>
          <a-button @click="clearSelection" type="primary" style="margin-bottom: 32px;">清空</a-button>
          <!--用户列表-->
          <a-table ref="selectedTable" :scroll="selectionRows.length > 0 ? { x: 'max-content' } : {}" size="middle" rowKey="id" :columns="selectedColumns"
            :dataSource="selectionRows"  :pagination="false" :loading="loading" @change="handleTableChange">
            <span slot="action" slot-scope="text,record">
              <a @click="deleteRow(record)">移除</a>

            </span>
          </a-table>
        </a-card>
      </a-col>
    </a-row>
  </j-modal>
</template>
<script>
import {
  filterObj
} from '@/utils/util'
import {
  queryDepartTreeSync,
  queryUserByDepId,
  queryall
} from '@/api/api'
import {
  getAction
} from '@/api/manage'

export default {
  name: 'JSelectUserByDepModalEnhance',
  components: {},
  props: {
    modalWidth: {
      type: Number,
      default: 1250,
      required: false
    },
    multi: {
      type: Boolean,
      default: true,
      required: false
    },
    store: {
      type: String,
      default: 'username',
      required: false
    },
    url: {
      type: String,
      default: '/sys/user/queryUserComponentData',  // /serviceProvider/user/queryUserComponentData接口是服务商工程师转办，只转办给自己服务商下面其他工程师。接口是/sys/user/queryUserComponentData  查询所有用户
      required: false
    },
    text: {
      type: String,
      default: 'realname',
      required: false
    },
    userIds: {
      type: String,
      required: false
    },
    tips: {
      type: String,
      required: false,
      default: '请先选择用户',
    }
  },
  data() {
    return {
      primaryColor: this.$store.getters.color,
      queryParam: {
        username: '',
      },
      columns: [{
        title: '用户账号',
        align: 'center',
        dataIndex: 'username',
        width: '150px',
      },
        {
          title: '用户姓名',
          align: 'center',
          dataIndex: 'realname',
          width: '150px',
        },
        // {
        //   title: '性别',
        //   align: 'center',
        //   dataIndex: 'sex',
        //   width: '50px',
        //   customRender: function (text) {
        //     if (text === 1) {
        //       return '男'
        //     } else if (text === 2) {
        //       return '女'
        //     } else {
        //       return text
        //     }
        //   }
        // },
        {
          title: '部门',
          align: 'center',
          dataIndex: 'orgCodeTxt',
          width: '150px',
        },
        {
          title: '手机',
          align: 'center',
          dataIndex: 'phone',
          width: '130px',
        },
      ],
      selectedColumns: [{
        title: '用户账号',
        align: 'center',
        dataIndex: 'username'
      },
        {
          title: '用户姓名',
          align: 'center',
          dataIndex: 'realname'
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          scopedSlots: {
            customRender: 'action'
          },
        },
      ],
      scrollTrigger: {},
      dataSource: [],
      selectionRows: [],
      selectedRowKeys: [],
      selectUserRows: [],
      selectUserIds: [],
      title: '根据部门选择用户',
      ipagination: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '30'],
        showTotal: (total, range) => {
          return range[0] + '-' + range[1] + ' 共' + total + '条'
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0
      },
      isorter: {
        column: 'createTime',
        order: 'desc'
      },
      selectedDepIds: [],
      roleList: [],
      showRole:false,
      departTree: [],
      visible: false,
      refresh: false,
      form: this.$form.createForm(this),
      loading: false,
      expandedKeys: [],
      labelCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 4
        },
      },
      wrapperCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 11
        },
      },
    }
  },
  computed: {
    // 计算属性的 getter
    getType: function () {
      return this.multi == true ? 'checkbox' : 'radio'
    }
  },
  watch: {
    userIds: {
      immediate: true,
      handler() {
        this.initUserNames()
      }
    },
  },
  created() {
    // 该方法触发屏幕自适应
    //this.resetScreenSize()
    if (this.url!=='/sys/user/queryUserComponentData'){
      this.columns.splice(2, 0, {
        title: '角色',
        align: 'center',
        dataIndex: 'roleNames',
        width: '150px',
      });
    }
  },
  methods: {
    changeRoles(){
      this.loadData(1)
    },
    /**
     * 获取角色
     */
    initialRoleList() {
      this.roleList = []
      return new Promise((resolve, reject) => {
        queryall().then((res) => {
          if (res.success) {
            this.roleList = res.result
            resolve({
              success: true,
              message: res.message,
              data: res.result
            })
          } else {
            reject({
              success: false,
              message: res.message,
              data: []
            })
          }
        }).catch((err) => {
          reject({
            success: false,
            message: err.message,
            data: []
          })
        })
      })
    },
    clearSelection() {
      this.selectionRows = []
      this.selectedRowKeys = []
    },
    deleteRow(record, index, indent, expanded) {
      this.selectionRows.splice(this.selectionRows.indexOf(record), 1)
      this.selectedRowKeys.splice(this.selectedRowKeys.indexOf(record.id), 1)
    },
    initUserNames() {
      if (this.userIds) {
        // 这里最后加一个 , 的原因是因为无论如何都要使用 in 查询，防止后台进行了模糊匹配，导致查询结果不准确
        let values = this.userIds.split(',') + ','
        let param = {
          [this.store]: values
        }
        getAction('/sys/user/getMultiUser', param).then((list) => {
          this.selectionRows = []
          let selectedRowKeys = []
          let textArray = []
          if (list && list.length > 0) {
            for (let user of list) {
              textArray.push(user[this.text])
              selectedRowKeys.push(user['id'])
              this.selectionRows.push(user)
            }
          }
          this.selectedRowKeys = selectedRowKeys
          this.$emit('initComp', textArray.join(','))
        })

      } else {
        // JSelectUserByDep组件bug issues/I16634
        this.$emit('initComp', '')
        // 前端用户选择单选无法置空的问题 #2610
        this.selectedRowKeys = []
      }
    },
    async loadData(arg) {
      if (arg === 1) {
        this.ipagination.current = 1
      }
      let params = this.getQueryParams() //查询条件
      this.loading = true
      getAction(this.url, params).then(res => {
        if (res.success) {
          this.dataSource = res.result.records
          this.ipagination.total = res.result.total
        }else{
          this.dataSource=[]
          this.ipagination.total = 0
          this.$message.warn("获取指定办理人"+res.message)
        }
      }).finally(() => {
        this.loading = false
      })
    },
    // 触发屏幕自适应
    resetScreenSize() {
      let screenWidth = document.body.clientWidth
      if (screenWidth < 500) {
        this.scrollTrigger = {
          x: 800
        }
      } else {
        this.scrollTrigger = {}
      }
    },
    showModal() {
      // 打开弹框后获取可分配用户信息的接口
      this.visible = true
      this.refresh = true
      this.queryDepartTree()
      this.initUserNames()
      this.loadData(1)
      this.form.resetFields()
      if (this.url!=='/sys/user/queryUserComponentData'){
        this.initialRoleList()
        this.showRole=true
      }
    },
    getQueryParams() {
      let param = Object.assign({}, this.queryParam, this.isorter)
      param.field = this.getQueryField()
      param.pageNo = this.ipagination.current
      param.pageSize = this.ipagination.pageSize
      param.departId = this.selectedDepIds.join(',')
      return filterObj(param)
    },
    getQueryField() {
      let str = 'id,'
      for (let a = 0; a < this.columns.length; a++) {
        str += ',' + this.columns[a].dataIndex
      }
      return str
    },
    searchReset(num) {
      let that = this
      that.selectedDepIds = []
      if (num !== 0) {
        that.queryParam = {}
        that.loadData(1)
      }
    },
    close() {
      this.selectedRowKeys = []
      this.selectionRows = []
      this.searchReset(0)
      this.refresh = false
      this.visible = false
    },
    handleTableChange(pagination, filters, sorter) {
      //TODO 筛选
      if (Object.keys(sorter).length > 0) {
        this.isorter.column = sorter.field
        this.isorter.order = 'ascend' === sorter.order ? 'asc' : 'desc'
      }
      this.ipagination = pagination
      this.loadData()
    },
    handleSubmit() {
      let that = this
      that.getSelectUserRows()
      //console.log("that.selectUserRows==",that.selectUserRows)
      if (that.selectUserRows.length > 0) {
        that.$emit('ok', that.selectUserRows)
        that.searchReset(0)
        that.close()
      } else {
        // 去掉选择用户必填校验
        that.$emit('ok', that.selectUserRows)
        that.close()
      }
    },
    //获取选择用户信息
    getSelectUserRows() {
      this.selectUserRows = []
      for (let row of this.selectionRows) {
        if (this.selectedRowKeys.includes(row.id)) {
          this.selectUserRows.push(row)
        }
      }
      this.selectUserIds = this.selectUserRows.map(row => row.username).join(',')
    },
    // 点击树节点,筛选出对应的用户
    onDepSelect(selectedDepIds) {
      if (selectedDepIds[0] != null) {
        if (this.selectedDepIds[0] !== selectedDepIds[0]) {
          this.selectedDepIds = [selectedDepIds[0]]
        }
        this.loadData(1)
      }
    },
    onSelectChange(selectedRowKeys, selectionRows) {
      this.selectedRowKeys = selectedRowKeys
      this.pushIfNotExist(this.selectionRows, selectionRows, 'id')
      this.selectionRows=this.removeIfNotExist(this.selectionRows, this.selectedRowKeys, 'id')
    },
    pushIfNotExist(array, value, key) {
      for (let val of value) {
        let isExist = false
        if (array.length > 0) {
          array.filter((item, index) => {
            if (item[key] === val[key]) {
              isExist = true
            }
          })
        }
        if (!isExist) {
          array.push(val)
        }
      }
    },
    removeIfNotExist(array, value, key) {
      let temArr=[]
      for (let id of value) {
        if (array.length > 0) {
          array.filter((item, index) => {
            if (item[key] === id) {
              temArr.push(item)
            }
          })
        }
      }
      return temArr
    },
    onSearch() {
      this.loadData(1)
    },
    // 根据选择的id来查询用户信息
    initQueryUserByDepId(selectedDepIds) {
      this.loading = true
      return queryUserByDepId({
        id: selectedDepIds.toString()
      }).then((res) => {
        if (res.success) {
          this.dataSource = res.result
          this.ipagination.total = res.result.length
        }
      }).finally(() => {
        this.loading = false
      })
    },
    queryDepartTree() {
      //update-begin-author:taoyan date:20211202 for: 异步加载部门树 https://github.com/jeecgboot/jeecg-boot/issues/3196
      this.expandedKeys = []
      this.departTree = []
      queryDepartTreeSync().then((res) => {
        if (res.success) {
          for (let i = 0; i < res.result.length; i++) {
            let temp = res.result[i]
            this.departTree.push(temp)
          }
        }
      })
    },
    loadAll() {
      this.searchReset(1)
    },
    onLoadDepartment(treeNode) {
      return new Promise(resolve => {
        queryDepartTreeSync({
          pid: treeNode.dataRef.id
        }).then((res) => {
          if (res.success) {
            //判断chidlren是否为空，并修改isLeaf属性值
            if (res.result.length == 0) {
              treeNode.dataRef['isLeaf'] = true

            } else {
              treeNode.dataRef['children'] = res.result
            }
          }
        })
        resolve()
      })
    },
    //update-end-author:taoyan date:20211202 for: 异步加载部门树 https://github.com/jeecgboot/jeecg-boot/issues/3196
    modalFormOk() {
      this.loadData()
    }
  }
}
</script>

<style scoped lang='less'>
@import '~@assets/less/limitModalHeight.less';
//@import '~@assets/less/normalModal.less';

/*.ant-table-tbody .ant-table-row td {
  padding-top: 10px;
  padding-bottom: 10px;
}

#components-layout-demo-custom-trigger .trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color .3s;
}*/
.row{
  background-color: #ececec;
}
@media (min-width: 1200px) {
  .row{
    height: 100%;
    //max-width: 100%;
    overflow: auto !important;

    .col{
      padding: 10px;
      height: 100%;
      overflow: hidden !important;

      .card{
        height: 100%;
        overflow: auto !important;
      }
      &:nth-child(2){
        padding: 10px 0;
      }
    }
  }
}
@media (max-width: 1199px) {
  .row {
    height: auto;
    overflow: hidden !important;

    .col {
      padding: 10px 10px 0px;
      height: auto;
      overflow: hidden !important;

      .card {
        height: auto;
        overflow: hidden !important;

        &:nth-child(1) {
          ::v-deep .ant-card-body {
            overflow-x: auto;
          }
        }
      }

      &:last-child {
        padding: 10px
      }
    }
  }
}
::v-deep .ant-table-body{
  overflow-x: auto !important;
}
.loadAllBtn:hover {
  background-color: #fff;
  color: #409eff !important;
  cursor: pointer;
}

.loadAllBtn {
  padding: 5px;
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1.5;
  padding-left: 34px;
}
</style>
