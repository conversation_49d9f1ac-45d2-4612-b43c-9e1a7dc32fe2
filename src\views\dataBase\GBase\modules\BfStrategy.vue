<template>
  <a-row :gutter='10' style='height: 100%' class='vScroll zxw'>
    <a-col style='width: 100%; height: 100%; display: flex; flex-direction: column'>
      <!-- 查询区域 -->
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <div class='table-page-search-wrapper'>
          <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
            <a-row :gutter='24' ref='row'>
              <a-col :span='spanValue'>
                <a-form-item label='任务名称'>
                  <a-input
                    :maxLength='maxLength'
                    placeholder='请输入任务名称'
                    :allowClear='true'
                    autocomplete='off'
                    v-model='queryParam.taskName'
                  ></a-input>
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label='数据库'>
                  <a-select v-model='queryParam.targetDb' placeholder='请选择数据资源库'
                            style='width: 100%' :allowClear='true' :options='dataBases'>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label='执行方式'>
                  <a-select v-model='queryParam.executeType' placeholder='请选择执行方式'
                            style='width: 100%' :allowClear='true' :options='executeTypes'>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span='spanValue' v-show='toggleSearchStatus'>
                <a-form-item label='状态'>
                  <a-select v-model='queryParam.taskStatus' placeholder='请选择状态'
                            style='width: 100%' :allowClear='true' :options='taskStatusArr'>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span='colBtnsSpan()'>
                    <span :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                          class='table-page-search-submitButtons'>
                      <a-button class='btn-search btn-search-style' type='primary' @click='searchQuery'>查询</a-button>
                      <a-button class='btn-reset btn-reset-style' @click='searchReset'>重置</a-button>
                      <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                        {{ toggleSearchStatus ? '收起' : '展开' }}
                        <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                      </a>
                    </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>

      <a-card :bordered='false' style='flex: auto' class='core'>
        <a-row class='lastBtn2'>
          <div class='table-operator'>
            <a-button @click='handleAdd'>新增</a-button>
            <a-dropdown v-if='selectedRowKeys.length > 0'>
              <a-menu slot='overlay' style='text-align: center'>
                <a-menu-item key='1' @click='batchDel'>删除</a-menu-item>
              </a-menu>
              <a-button> 批量操作
                <a-icon type='down' />
              </a-button>
            </a-dropdown>
          </div>
        </a-row>
        <!-- table区域-begin -->
        <a-table
          ref='table'
          bordered
          rowKey='id'
          :columns='columns'
          :dataSource='dataSource'
          :scroll='dataSource.length>0?{x:"max-content"}:{}'
          :pagination='ipagination'
          :loading='loading'
          :rowSelection='{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }'
          @change='handleTableChange'
        >
          <template slot='htmlSlot' slot-scope='text'>
            <div v-html='text'></div>
          </template>
          <template slot='imgSlot' slot-scope='text'>
            <span v-if='!text' style='font-size: 14px'>无图片</span>
            <img v-else :src='getImgView(text)' height='25px' alt='' style='max-width: 80px; font-size: 14px' />
          </template>
          <template slot='tooltip' slot-scope='text'>
            <a-tooltip placement='topLeft' :title='text' trigger='hover'>
              <div class='tooltip'>
                {{ text }}
              </div>
            </a-tooltip>
          </template>
          <template slot='index' slot-scope='text,record,index'>
            <span>{{ index + 1 }}</span>
          </template>
          <!-- 状态渲染模板 -->
          <template slot='taskStatus' slot-scope='text'>
            <a-tag v-if="text==='0'" color='orange'>已禁用</a-tag>
            <a-tag v-if="text==='1'" color='green'>已启用</a-tag>
          </template>
          <!--          最后一次执行结果-->
          <template slot='lastResult' slot-scope='text'>
            <a-tag v-if='text===false' color='red'>失败</a-tag>
            <a-tag v-else-if='text===true' color='green'>成功</a-tag>
            <span v-else>--</span>
          </template>
          <template slot='fileSlot' slot-scope='text'>
            <span v-if='!text' style='font-size: 14px'>无文件</span>
            <a-button v-else :ghost='true' type='primary' icon='download' size='small' @click='downloadFile(text)'>
              下载
            </a-button>
          </template>
          <!--操作-->
          <span slot='action' slot-scope='text, record'>
          <a @click='resumeJob(record)' v-if="record.taskStatus==='0'">启用</a>
          <a @click='pauseJob(record)' v-if="record.taskStatus==='1'">禁用</a>

          <a-divider type='vertical' />
          <a-dropdown>
            <a class='ant-dropdown-link'>更多 <a-icon type='down' /></a>
            <a-menu slot='overlay'>
              <a-menu-item v-if="record.executeType==='1'">
                <a  @click='executeImmediately(record)'>立即执行</a>
              </a-menu-item>
              <a-menu-item >
                <a  @click='openRecord(record)'>执行记录</a>
              </a-menu-item>
              <a-menu-item>
                <a @click='handleEdit(record)'>编辑</a>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title='确定删除吗?' @confirm='() => handleDelete(record.id)'>
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>


        </a-table>
      </a-card>
      <BfStrategyModal
        ref='modalForm'
        @ok='modalFormOk'
        :back-levels='backLevels'
        :back-contents='backContents'
        :back-formats='backFormats'
        :data-bases='dataBases'
      ></BfStrategyModal>
      <execute-records ref='executeRecords'></execute-records>
    </a-col>
  </a-row>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import BfStrategyModal from './BfStrategyModal'
import ViewConfigModal from '@views/networkManagement/networkDevice/modules/ViewConfigModal.vue'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
import { getAction, deleteAction, putAction } from '@/api/manage'
import { strategyData } from '@views/dataBase/GBase/mock/strategy'
import { ajaxGetDictItems } from '@api/api'
import ExecuteRecords from '@views/dataBase/GBase/modules/ExecuteRecords.vue'
export default {
  name: 'BfStrategy',
  mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
  components: {
    BfStrategyModal,
    ViewConfigModal,
    ExecuteRecords
  },
  data() {
    return {
      maxLength:50,
      description: '数据库备份任务管理页面',
      // 表头
      columns: [
        {
          title: '序号',
          dataIndex: 'index',
          scopedSlots: {
            customRender: 'index'
          },
          customCell: () => {
            let cellStyle = 'width:60px;text-align: center'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '任务名称',
          dataIndex: 'taskName',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 180px;'
            return { style: cellStyle }
          }
        }, {
          title: '数据库',
          dataIndex: 'targetDb',
          customRender: (text) => {
            return this.dataBases.find((item) => item.value === text)?.label
          },
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 180px;'
            return { style: cellStyle }
          }
        }, {
          title: '备份级别',
          dataIndex: 'backLevel',
          customRender: (text, record) => {
            if (text === 'database') {
              return this.backLevels.find((item) => item.value === text)?.text
            } else {
              let level = this.backLevels.find((item) => item.value === text)?.text
              return level + `（${record.backLevelValue})`
            }
          },
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 180px;'
            return { style: cellStyle }
          }
        }, {
          title: '备份内容',
          dataIndex: 'backContent',
          customRender: (text) => {
            return this.backContents.find((item) => item.value === text)?.text
          },
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 180px;'
            return { style: cellStyle }
          }
        }, {
          title: '备份格式',
          dataIndex: 'backFormat',
          customRender: (text) => {
            return this.backFormats.find((item) => item.value === text)?.text
          },
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 180px;'
            return { style: cellStyle }
          }
        }, {
          title: '备份路径',
          dataIndex: 'backFilePath',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 180px;'
            return { style: cellStyle }
          }
        }, {
          title: '备份目的文件名',
          dataIndex: 'backFileName',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 180px;'
            return { style: cellStyle }
          }
        }, {
          title: '备份目的文件名后缀',
          dataIndex: 'backFileSuffix',
          customRender: (text) => {
            return this.backFileSuffixs.find((item) => item.value === text)?.text
          },
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 180px;'
            return { style: cellStyle }
          }
        },
        {
          title: '执行方式',
          dataIndex: 'executeType',
          customRender: (text, record) => {
            if (text == 1) {
              return '手动'
            } else {
              return `周期（${record.executeCron}）`
            }
          },
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 180px;'
            return { style: cellStyle }
          }
        },
        // {
        //   title: '备份频率',
        //   dataIndex: 'executeCron',
        //   customCell: () => {
        //     let cellStyle = 'text-align: center;min-width: 180px;'
        //     return { style: cellStyle }
        //   }
        // },
        {
          title: '最近一次执行结果',
          dataIndex: 'lastResult',
          scopedSlots: { customRender: 'lastResult' },
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 180px;'
            return { style: cellStyle }
          }
        },
        {
          title: '状态',
          align: 'center',
          dataIndex: 'taskStatus',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 100px;max-width:300px'
            return { style: cellStyle }
          },
          scopedSlots: { customRender: 'taskStatus' }
        },
        {
          title: '添加时间',
          dataIndex: 'createTime',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 100px;max-width:260px'
            return { style: cellStyle }
          }
        },
        {
          title: '操作',
          dataIndex: 'action',
          fixed: 'right',
          align: 'center',
          width: 180,
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: '/gbase/task/list', // 任务列表
        delete: '/gbase/task/deleteBatch', // 删除任务
        deleteBatch: '/gbase/task/deleteBatch', // 批量删除任务
        exportXlsUrl: '',
        importExcelUrl: '',
        pause: '/gbase/task/edit',  // 禁用
        resume: '/gbase/task/edit', // 启用
        execute: '/gbase/task/enableOneShot' // 执行
      },
      dictOptions: {},
      disableMixinCreated: true,
      backLevels: [],
      backContents: [],
      backFormats: [],
      dataBases: [],
      backFileSuffixs: [],
      executeTypes: [
        {
          label: '手动',
          value: '1'
        }, {
          label: '周期',
          value: '0'
        }
      ], taskStatusArr: [
        {
          label: '启用',
          value: '1'
        }, {
          label: '禁用',
          value: '0'
        }
      ]
    }
  },
  created() {
    this.getDatabases()
    this.getDictData()
    // 0：备份  1：恢复
    this.queryParam.taskType = 0
    this.loadData()
  },
  mounted() {
  },
  computed: {
    importExcelUrl: function() {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    }
  },
  methods: {
    /*
    * 执行记录
    * */
    openRecord(record) {
      this.$refs.executeRecords.open(record)
    },
    /*
   * 获取数据库
   * */
    getDatabases() {
      getAction('gbase/manage/list', { pageNo: 1, pageSize: -1 }).then((res) => {
        if (res.success) {
          this.dataBases = res.result.records.map(el => {
            return { label: el.dbInstance, value: el.id }
          })
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    /*
   * 字典值
   * */
    getDictData() {
      ajaxGetDictItems('back_level', null).then((res) => {
        this.backLevels = res.result
      })
      ajaxGetDictItems('back_content', null).then((res) => {
        this.backContents = res.result
      })
      ajaxGetDictItems('back_format', null).then((res) => {
        this.backFormats = res.result
      })
      ajaxGetDictItems('backFileSuffixFormat', null).then((res) => {
        this.backFileSuffixs = res.result
      })
    },
    /*
    * 重置
    * */
    searchReset() {
      this.queryParam = {}
      this.queryParam.taskType = 0
      this.loadData(1)
    },
    handleDelete: function(id) {
      var that = this
      deleteAction(that.url.deleteBatch, { ids: id }).then((res) => {
        if (res.success) {
          //重新计算分页问题
          that.reCalculatePage(1)
          that.$message.success(res.message)
          that.loadData()
        } else {
          that.$message.warning(res.message)
        }
      })
    },
    pauseJob: function(record) {
      var that = this
      //禁用定时任务
      let params = {
        ...record,
        taskStatus: 0
      }
      this.$confirm({
        title: '确认禁用',
        okText: '是',
        cancelText: '否',
        content: '是否禁用选中任务?',
        onOk: function() {
          putAction(that.url.pause, params).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.loadData()
              that.onClearSelected()
            } else {
              that.$message.warning(res.message)
            }
          })
        }
      })

    },
    resumeJob: function(record) {
      var that = this
      //恢复定时任务
      let params = {
        ...record,
        taskStatus: 1
      }
      this.$confirm({
        title: '确认启用',
        okText: '是',
        cancelText: '否',
        content: '是否启用选中任务?',
        onOk: function() {
          putAction(that.url.resume, params).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.loadData()
              that.onClearSelected()
            } else {
              that.$message.warning(res.message)
            }
          })
        }
      })
    },
    executeImmediately(record) {
      var that = this
      //立即执行定时任务
      // let params = {
      //   id: record.id,
      //   strategyName: record.strategyName,
      //   executeCron: record.executeCron,
      //   strategyStatus: record.strategyStatus,
      //   description: record.description
      // }
      this.$confirm({
        title: '确认提示',
        okText: '是',
        cancelText: '否',
        content: '是否立即执行任务?',
        onOk: function() {
          console.log('执行')
          putAction(that.url.execute, record).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.loadData()
              that.onClearSelected()
            } else {
              that.$message.warning(res.message)
            }
          })
        }
      })
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
</style>
