<template>
  <card-frame :title="title" :titleBgPath="'/statsCenter/evaluate/title1.png'">
    <div slot="bodySlot" class="empty-wrapper" v-if="chartData.length===0">
      <a-spin :spinning="loading" v-if="loading" class="spin"></a-spin>
      <a-list :data-source="[]" v-else />
    </div>
    <div slot="bodySlot" v-else style="height: 100%;width: 100%;" ref="pieChart"></div>
  </card-frame>
</template>

<script>
import cardFrame from '@views/statsCenter/com/cardFrame.vue'
import { getAction } from '@/api/manage'
export default {
  components: {
    cardFrame
  },
  data() {
    return {
      loading: false,
      chartData: [],
      time1: '',
      time2: '',
      url: {
        typeCount: '/data-analysis/order/evaluation'
      }
    }
  },
  props: {
    title: ''
  },
  created() {
    this.typeCount()
  },
  methods: {
    typeCount() {
      this.loading = true
      getAction(this.url.typeCount, {
        time1: this.time1,
        time2: this.time2
      })
        .then(res => {
          if (res.code == 200) {
            if (res.result && res.result.length > 0) {
            let count = 0
            res.result.map(item => {
              count += item.value
            })
            if (count > 0) {
              this.chartData = res.result
              this.$nextTick(() => {
                this.pieChart(res.result)
              })
            }
            }
          } else {
            this.$message.warning(res.message)
          }
          this.loading = false
        })
        .catch(err => {
          this.$message.warning(err.message)
          this.loading = false
        })
    },
    pieChart(data) {
      let sum = 0
      let data1 = []
      data.forEach(item => {
        sum += Number(item.value)
        data1.push(item, {
          name: '',
          value: 0,
          labelLine: {
            show: false,
            lineStyle: {
              color: 'transparent'
            }
          },
          itemStyle: {
            color: 'transparent'
          }
        })
      })
      const color = [
        '#F7E520',
        '',
        '#46FDFF',
        '',
        '#74CBFF',
        '',
        '#1EABFB',
        '',
        '#0080FF',
        '',
        '#d0405b',
        '',
        '#be71b5'
      ]
      let myChart = this.$echarts.init(this.$refs.pieChart)
      myChart.setOption({
        tooltip: {
          show: true,
          formatter: function(params) {
            if (params.name) {
              return params.name + '： ' + params.percent + '%'
            }
          },
          transitionDuration: 0 //echart防止tooltip的抖动
        },
        label: {
          show: false
        },
        title: {
          text: sum,
          left: '29%',
          top: '39%',
          itemGap: 10,
          textAlign: 'center',
          textStyle: {
            color: '#fff',
            fontSize: 36,
            fontWeight: 600
          },
          subtext: '评价总数',
          subtextStyle: {
            color: '#9FA5AD',
            fontSize: 15,
            fontWeight: 500
          }
        },
        legend: {
          show: true, // 是否显示图例
          // orient: 'vertical',
          orient: 'horizontal',
          y: 'center',
          right: 50,
          itemGap: 20,
          icon: 'rect',
          selectedMode: false,
          itemWidth: 10, // 图例宽度
          itemHeight: 10, // 图例高度
          color: '#fff',
          textStyle: {
            //图例文字的样式
            rich: {
              a: {
                color: '#9FA5AD',
                fontSize: 14
              },
              b: {
                color: '#fff',
                fontSize: 16,
                padding: [0, 20]
              }
            }
          },
          formatter: function(name) {
            var target
            data.map((item, index) => {
              if (item.name == name) {
                target = `{a|${name}} {b|${item.value}}`
              }
            })
            return target
          }
        },
        series: [
          {
            type: 'pie',
            radius: ['55%', '60%'],
            center: ['30%', '50%'],
            hoverAnimation: false, // 取消掉饼图鼠标移上去时自动放大
            animation: false,
            minAngle: 5,
            label: {
              show: false
            },
            itemStyle: {
              normal: {
                color: function(params) {
                  return color[params.dataIndex]
                }
              }
            },
            data: data1,
            z: 1
          },
          {
            type: 'pie',
            radius: ['44%', '49%'],
            center: ['30%', '50%'],
            minAngle: 5,
            hoverAnimation: false, // 取消掉饼图鼠标移上去时自动放大
            animation: false,
            labelLine: {
              show: false
            },
            label: {
              show: false
            },
            itemStyle: {
              normal: {
                color: '#272B36'
              }
            },
            data: data1,
            z: 666
          }
        ]
      })
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    }
  }
}
</script>

<style scoped lang="less">
</style>