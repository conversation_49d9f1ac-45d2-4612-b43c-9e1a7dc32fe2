//业务系统数据
window.ZrBusinesses = [
  {
    id: 1,
    businessName: '智能运维监控系统',
    ip: '**************',
    status: 0,
    topoId: '1844672598126452738',
    positionInfo: '10楼001机房--3号机柜',
    deviceName: '服务器A',
    bg: '/zrBigScreen/sysBgs/demo.jpg'
  },
  {
    id: 2,
    businessName: '预约管理系统',
    ip: '*************',
    status: 1,
    topoId: '1844672598126452738',
    positionInfo: '05楼B区--1号机柜',
    deviceName: '交换机X1',
    bg: '/zrBigScreen/sysBgs/yglxt.jpg'
  },
  {
    id: 3,
    businessName: '运行评估系统',
    ip: '************',
    status: 0,
    topoId: '1844672598126452738',
    positionInfo: '08楼测试室--5号机柜',
    deviceName: '存储设备S1',
    bg: '/zrBigScreen/sysBgs/yxpgxt.jpg'
  },
  {
    id: 4,
    businessName: '智能调度系统',
    ip: '*************',
    status: 2,
    topoId: '1844672598126452738',
    positionInfo: '02楼核心区域--2号机柜',
    deviceName: '防火墙F2',
    bg: '/zrBigScreen/sysBgs/znddxt.jpg'
  }
  /* {
     id: 5,
     businessName: '后勤服务系统',
     ip: '************',
     status: 0,
     topoId: "1930151035842088962",
     positionInfo: "03楼运维间--4号机柜",
     deviceName: "路由器R1",
     bg:"/zrBigScreen/sysBgs/hqfwxt.jpg",
   },
   {
     id: 6,
     businessName: '人力资源管理系统',
     ip: '**************',
     status: 0,
     topoId: "1767153342930358274",
     positionInfo: "07楼数据中心--6号机柜",
     deviceName: "负载均衡L1",
     bg:"/zrBigScreen/sysBgs/rlzyglxt.jpg",
   }*/
]

window.ZrOrgTopoId = '1844672598126452738' //组织架构拓扑图ID

// 组织架构部门间关系数据
window.ZrDeparttLinks = [
  { source: 'dept-combat-east-1', target: 'dept-logistics-east-1' },
  { source: 'dept-combat-east-1', target: 'dept-equipment-east-1' }
]
//// 组织架构数据
window.ZrOrganizations = {
  id: 'major-theater',
  name: 'LJ本级节点',
  level: 1,
  status: 0,
  deviceCode: 'd_000',
  day: 45,
  devices: 35,
  score: 98,
  city: '南京',
  // topoId: "1814526692903936001",
  children: [
    // 东部战区
    {
      id: 'east-theater',
      name: '东部战区',
      level: 2,
      status: 0,
      deviceCode: 'dt_001',
      city: '福州',
      day: 45,
      devices: 32,
      score: 98,
      children: [
        {
          id: 'group-army-east-1',
          name: '71集团军',
          level: 3,
          status: 0,
          deviceCode: 'ga_east_001',
          city: '徐州',
          day: 41,
          devices: 8,
          score: 91,
          children: [
            {
              id: 'dept-combat-east-1',
              name: '作战部',
              level: 4,
              status: 0,
              deviceCode: 'dc_east_001',
              day: 20,
              devices: 32,
              score: 98
            },
            {
              id: 'dept-logistics-east-1',
              name: '后勤部',
              level: 4,
              status: 1,
              deviceCode: 'dl_east_001',
              day: 0,
              devices: 32,
              score: 98
            },
            {
              id: 'dept-equipment-east-1',
              name: '装备部',
              level: 4,
              status: 0,
              deviceCode: 'de_east_001',
              day: 31,
              devices: 32,
              score: 98
            },
            {
              id: 'dept-intelligence-east-1',
              name: '情报部',
              level: 4,
              status: 2,
              deviceCode: 'di_east_001',
              day: 0,
              devices: 32,
              score: 98
            },
            {
              id: 'dept-training-east-1',
              name: '训练部',
              level: 4,
              status: 0,
              deviceCode: 'dt_east_001',
              day: 41,
              devices: 32,
              score: 98
            }
          ]
        },
        {
          id: 'group-army-east-2',
          name: '72集团军',
          level: 3,
          status: 0,
          deviceCode: 'ga_east_002',
          city: '湖州',
          day: 45,
          devices: 32,
          score: 98,
          children: [
            {
              id: 'dept-combat-east-2',
              name: '作战部',
              level: 4,
              status: 1,
              deviceCode: 'dc_east_002',
              day: 0,
              devices: 32,
              score: 98
            },
            {
              id: 'dept-logistics-east-2',
              name: '后勤部',
              level: 4,
              status: 0,
              deviceCode: 'dl_east_002',
              day: 20,
              devices: 32,
              score: 98
            },
            {
              id: 'dept-equipment-east-2',
              name: '装备部',
              level: 4,
              status: 0,
              deviceCode: 'de_east_002',
              day: 20,
              devices: 32,
              score: 98
            }
          ]
        },
        {
          id: 'group-army-south-3',
          name: '73集团军',
          level: 3,
          status: 0,
          deviceCode: 'ga_south_003',
          day: 20,
          devices: 32,
          score: 94,
          city: '厦门',
          children: [
            {
              id: 'dept-combat-south-3',
              name: '作战部',
              level: 4,
              status: 0,
              deviceCode: 'dc_south_003',
              day: 20,
              devices: 32,
              score: 98
            },
            {
              id: 'dept-logistics-south-3',
              name: '后勤部',
              level: 4,
              status: 0,
              deviceCode: 'dl_south_003',
              day: 20,
              devices: 32,
              score: 98
            },
            {
              id: 'dept-equipment-south-3',
              name: '装备部',
              level: 4,
              status: 0,
              deviceCode: 'de_south_003',
              day: 20,
              devices: 32,
              score: 98
            },
            {
              id: 'dept-training-south-3',
              name: '训练部',
              level: 4,
              status: 0,
              deviceCode: 'dt_south_003',
              day: 20,
              devices: 32,
              score: 98
            }
          ]
        }
      ]
    },
    //北京
    {
      id: 'beijing',
      name: '北京',
      level: 2,
      status: 0,
      deviceCode: 'd_001',
      city: '北京',
      day: 45,
      devices: 35,
      score: 98,
      children: []
    },
    //新疆
    {
      id: 'xujia',
      name: '新疆',
      level: 2,
      status: 0,
      deviceCode: 'd_002',
      city: '乌鲁木齐',
      day: 45,
      devices: 35,
      score: 98,
      children: []
    },
    // 南部战区
    {
      id: 'south-theater',
      name: '南部战区',
      level: 2,
      status: 2,
      deviceCode: 'dt_002',
      city: '南宁',
      day: 0,
      devices: 32,
      score: 68,
      children: [
        {
          id: 'group-army-south-1',
          name: '74集团军',
          level: 3,
          status: 0,
          deviceCode: 'ga_south_001',
          city: '惠州',
          day: 45,
          devices: 32,
          score: 98,
          children: [
            {
              id: 'dept-combat-south-1',
              name: '作战部',
              level: 4,
              status: 0,
              deviceCode: 'dc_south_001',
              day: 20,
              devices: 32,
              score: 98
            },
            {
              id: 'dept-logistics-south-1',
              name: '后勤部',
              level: 4,
              status: 2,
              deviceCode: 'dl_south_001',
              day: 0,
              devices: 32,
              score: 98
            },
            {
              id: 'dept-equipment-south-1',
              name: '装备部',
              level: 4,
              status: 0,
              deviceCode: 'de_south_001',
              day: 20,
              devices: 32,
              score: 98
            },
            {
              id: 'dept-intelligence-south-1',
              name: '情报部',
              level: 4,
              status: 0,
              deviceCode: 'di_south_001',
              day: 20,
              devices: 32,
              score: 98
            }
          ]
        },
        {
          id: 'group-army-south-2',
          name: '75集团军',
          level: 3,
          status: 1,
          deviceCode: 'ga_south_002',
          city: '昆明',
          day: 0,
          devices: 32,
          score: 85,
          children: [
            {
              id: 'dept-combat-south-2',
              name: '作战部',
              level: 4,
              status: 0,
              deviceCode: 'dc_south_002',
              day: 20,
              devices: 32,
              score: 98
            },
            {
              id: 'dept-logistics-south-2',
              name: '后勤部',
              level: 4,
              status: 0,
              deviceCode: 'dl_south_002',
              day: 20,
              devices: 32,
              score: 98
            },
            {
              id: 'dept-equipment-south-2',
              name: '装备部',
              level: 4,
              status: 1,
              deviceCode: 'de_south_002',
              day: 0,
              devices: 32,
              score: 98
            },
            {
              id: 'dept-training-south-2',
              name: '训练部',
              level: 4,
              status: 1,
              deviceCode: 'dt_south_002',
              day: 0,
              devices: 32,
              score: 98
            }
          ]
        }

      ]
    },


    // 西部战区
    {
      id: 'west-theater',
      name: '西部战区',
      level: 2,
      status: 0,
      deviceCode: 'dt_003',
      city: '兰州',
      day: 45,
      devices: 32,
      score: 90,
      children: [
        {
          id: 'group-army-west-1',
          name: '76集团军',
          level: 3,
          status: 2,
          deviceCode: 'ga_west_001',
          city: '西宁',
          day: 0,
          devices: 32,
          score: 70,
          children: [
            {
              id: 'dept-combat-west-1',
              name: '作战部',
              level: 4,
              status: 0,
              deviceCode: 'dc_west_001',
              day: 20,
              devices: 32,
              score: 98
            },
            {
              id: 'dept-logistics-west-1',
              name: '后勤部',
              level: 4,
              status: 0,
              deviceCode: 'dl_west_001',
              day: 20,
              devices: 32,
              score: 98
            },
            {
              id: 'dept-equipment-west-1',
              name: '装备部',
              level: 4,
              status: 0,
              deviceCode: 'de_west_001',
              day: 20,
              devices: 32,
              score: 98
            }
          ]
        },
        {
          id: 'group-army-west-2',
          name: '77集团军',
          level: 3,
          status: 0,
          deviceCode: 'ga_west_002',
          city: '成都',
          day: 45,
          devices: 32,
          score: 92,
          children: [
            {
              id: 'dept-combat-west-2',
              name: '作战部',
              level: 4,
              status: 1,
              deviceCode: 'dc_west_002',
              day: 0,
              devices: 32,
              score: 98
            },
            {
              id: 'dept-logistics-west-2',
              name: '后勤部',
              level: 4,
              status: 0,
              deviceCode: 'dl_west_002',
              day: 20,
              devices: 32,
              score: 98
            },
            {
              id: 'dept-equipment-west-2',
              name: '装备部',
              level: 4,
              status: 1,
              deviceCode: 'de_west_002',
              day: 0,
              devices: 32,
              score: 98
            },
            {
              id: 'dept-intelligence-west-2',
              name: '情报部',
              level: 4,
              status: 2,
              deviceCode: 'di_west_002',
              day: 0,
              devices: 32,
              score: 98
            }
          ]
        }
        /* {
           id: 'group-army-west-3',
           name: '西部集团3',
           level: 2,
           status: 0,
           deviceCode: "ga_west_003",
           children: [
             { id: 'dept-combat-west-3', name: '作战部', level: 4, status: 0, deviceCode: "dc_west_003" },
             { id: 'dept-logistics-west-3', name: '后勤部', level: 4, status: 0, deviceCode: "dl_west_003" },
             { id: 'dept-equipment-west-3', name: '装备部', level: 4, status: 0, deviceCode: "de_west_003" },
             { id: 'dept-training-west-3', name: '训练部', level: 4, status: 0, deviceCode: "dt_west_003" }
           ]
         }*/
      ]
    },
    // 北部战区
    {
      id: 'north-theater',
      name: '北部战区',
      level: 2,
      status: 0,
      deviceCode: 'dt_004',
      city: '济南',
      day: 45,
      devices: 32,
      score: 98,
      children: [
        {
          id: 'group-army-north-1',
          name: '78集团军',
          level: 3,
          status: 1,
          deviceCode: 'ga_north_001',
          city: '哈尔滨',
          day: 0,
          devices: 16,
          score: 72,
          children: [
            {
              id: 'dept-combat-north-1',
              name: '作战部',
              level: 4,
              status: 0,
              deviceCode: 'dc_north_001',
              day: 20,
              devices: 32,
              score: 98
            },
            {
              id: 'dept-logistics-north-1',
              name: '后勤部',
              level: 4,
              status: 0,
              deviceCode: 'dl_north_00',
              day: 20,
              devices: 32,
              score: 98
            },
            {
              id: 'dept-equipment-north-1',
              name: '装备部',
              level: 4,
              status: 0,
              deviceCode: 'de_north_001',
              day: 20,
              devices: 32,
              score: 98
            }
          ]
        },
        {
          id: 'group-army-north-2',
          name: '79集团军',
          level: 3,
          status: 0,
          deviceCode: 'ga_north_002',
          city: '辽阳',
          day: 45,
          devices: 32,
          score: 91,
          children: [
            {
              id: 'dept-combat-north-2',
              name: '作战部',
              level: 4,
              status: 0,
              deviceCode: 'dc_north_002',
              day: 20,
              devices: 32,
              score: 98
            },
            {
              id: 'dept-logistics-north-2',
              name: '后勤部',
              level: 4,
              status: 0,
              deviceCode: 'dl_north_002',
              day: 20,
              devices: 32,
              score: 98
            },
            {
              id: 'dept-equipment-north-2',
              name: '装备部',
              level: 4,
              status: 0,
              deviceCode: 'de_north_002',
              day: 20,
              devices: 32,
              score: 98
            },
            {
              id: 'dept-intelligence-north-2',
              name: '情报部',
              level: 4,
              status: 0,
              deviceCode: 'di_north_002',
              day: 20,
              devices: 32,
              score: 98
            }
          ]
        },
        {
          id: 'group-army-north-3',
          name: '80集团军',
          level: 3,
          status: 0,
          deviceCode: 'ga_north_003',
          city: '潍坊',
          day: 45,
          devices: 32,
          score: 98,
          children: [
            {
              id: 'dept-combat-north-3',
              name: '作战部',
              level: 4,
              status: 0,
              deviceCode: 'dc_north_003',
              day: 20,
              devices: 32,
              score: 98
            },
            {
              id: 'dept-logistics-north-3',
              name: '后勤部',
              level: 4,
              status: 0,
              deviceCode: 'dl_north_003',
              day: 20,
              devices: 32,
              score: 98
            },
            {
              id: 'dept-equipment-north-3',
              name: '装备部',
              level: 4,
              status: 0,
              deviceCode: 'de_north_003',
              day: 20,
              devices: 32,
              score: 98
            },
            {
              id: 'dept-training-north-3',
              name: '训练部',
              level: 4,
              status: 0,
              deviceCode: 'dt_north_003',
              day: 20,
              devices: 32,
              score: 98
            }
          ]
        }
      ]
    },
    //西藏
    {
      id: 'xizang',
      name: '西藏',
      level: 2,
      status: 0,
      deviceCode: 'd_003',
      city: '拉萨',
      day: 45,
      devices: 35,
      score: 98,
      children: []
    },
    // 中部战区
    {
      id: 'central-theater',
      name: '中部战区',
      level: 2,
      status: 0,
      deviceCode: 'dt_005',
      city: '石家庄',
      day: 45,
      devices: 32,
      score: 98,
      children: [
        {
          id: 'group-army-central-1',
          name: '81集团军',
          level: 3,
          status: 0,
          deviceCode: 'ga_central_001',
          city: '张家口',
          day: 45,
          devices: 32,
          score: 98,
          children: [
            {
              id: 'dept-combat-central-1',
              name: '作战部',
              level: 4,
              status: 0,
              deviceCode: 'dc_central_001',
              day: 20,
              devices: 32,
              score: 98
            },
            {
              id: 'dept-logistics-central-1',
              name: '后勤部',
              level: 4,
              status: 1,
              deviceCode: 'dl_central_001',
              day: 0,
              devices: 32,
              score: 98
            },
            {
              id: 'dept-equipment-central-1',
              name: '装备部',
              level: 4,
              status: 0,
              deviceCode: 'de_central_001',
              day: 20,
              devices: 32,
              score: 98
            },
            {
              id: 'dept-intelligence-central-1',
              name: '情报部',
              level: 4,
              status: 2,
              deviceCode: 'di_central_001',
              day: 0,
              devices: 32,
              score: 98
            }
          ]
        },
        {
          id: 'group-army-central-2',
          name: '82集团军',
          level: 3,
          status: 0,
          deviceCode: 'ga_central_002',
          city: '保定',
          day: 45,
          devices: 32,
          score: 98,
          children: [
            {
              id: 'dept-combat-central-2',
              name: '作战部',
              level: 4,
              status: 0,
              deviceCode: 'dc_central_002',
              day: 20,
              devices: 32,
              score: 98
            },
            {
              id: 'dept-logistics-central-2',
              name: '后勤部',
              level: 4,
              status: 0,
              deviceCode: 'dl_central_002',
              day: 20,
              devices: 32,
              score: 98
            },
            {
              id: 'dept-equipment-central-2',
              name: '装备部',
              level: 4,
              status: 0,
              deviceCode: 'de_central_002',
              day: 20,
              devices: 32,
              score: 98
            },
            {
              id: 'dept-training-central-2',
              name: '训练部',
              level: 4,
              status: 0,
              deviceCode: 'dt_central_002',
              day: 20,
              devices: 32,
              score: 98
            }
          ]
        },
        {
          id: 'group-army-central-3',
          name: '83集团军',
          level: 3,
          status: 0,
          deviceCode: 'ga_central_003',
          city: '新乡',
          day: 45,
          devices: 32,
          score: 98,
          children: [
            {
              id: 'dept-combat-central-3',
              name: '作战部',
              level: 4,
              status: 1,
              deviceCode: 'dc_central_003',
              day: 20,
              devices: 32,
              score: 98
            },
            {
              id: 'dept-logistics-central-3',
              name: '后勤部',
              level: 4,
              status: 1,
              deviceCode: 'dl_central_003',
              day: 20,
              devices: 32,
              score: 98
            },
            {
              id: 'dept-equipment-central-3',
              name: '装备部',
              level: 4,
              status: 0,
              deviceCode: 'de_central_003',
              day: 20,
              devices: 32,
              score: 98
            }
          ]
        }
      ]
    }

  ]
}

//地图城市坐标
window.ZrMapCities = {
  '北京': [116.4074, 39.9042],
  '上海': [121.4648, 31.2891],
  '包头': [110.3467, 41.4899],
  '乌鲁木齐': [87.9236, 43.5883],
  '拉萨': [91.1865, 30.1465],
  '重庆': [107.7539, 30.1904],
  '大连': [122.2229, 39.4409],
  '南昌': [116.0046, 28.6633],
  '南京': [118.8062, 31.9208],
  '福州': [119.4543, 25.9222],
  '徐州': [117.5208, 34.3268],
  '湖州': [120.0868, 30.8944],
  '厦门': [118.1689, 24.6478],
  '南宁': [108.479, 23.1152],
  '广州': [113.5107, 23.2196],
  '惠州': [114.4107, 23.1136],
  '昆明': [102.9199, 25.4663],
  '柳州': [109.3799, 24.9774],
  '兰州': [103.5901, 36.3043],
  '成都': [103.9526, 30.7617],
  '西宁': [101.4038, 36.8207],
  '济南': [117.1582, 36.8701],
  '长春': [125.8154, 44.2584],
  '沈阳': [123.1238, 42.1216],
  '哈尔滨': [127.9688, 45.368],
  '辽阳': [123.1724, 41.2739],
  '潍坊': [119.1618, 36.7068],
  '石家庄': [114.4995, 38.1006],
  '保定': [115.0488, 39.0948],
  '张家口': [114.8875, 40.8244],
  '新乡': [113.9268, 35.3030],
  '郑州': [113.4668, 34.6234],
  '邯郸': [114.4775, 36.535],
  '崇州': [103.67, 30.63] ,
}

//综合视图状态数据
window.ZrStatusInfo = [
  {
    name: '稳定运行',
    type:"wdyx",
    value: 1000,
    unit: '天',
    show: true
  },
  {
    name: '连续运行',
    type:"lxyx",
    value: 120,
    unit: '天',
    show: true
  },
  {
    name: '运行状态',
    type:"yxzt",
    value: 0,
    show: true
  },
]

//全局评估等级
window.zrGlobalLevel = 'B'
//评估指标数据
window.zrIndicators = [
  { name: "机密网节点覆盖范围", score: 23, level: "D" ,type:"wltl"},
  { name: "机密级子网和终端配备", score: 88, level: "B" ,type:"wltl"},
  { name: "与部本级数据处理中心机密域通联状况", score: 67, level: "C",type:"wltl" },
  { name: "内部级和机密级网络通联状况", score: 54, level: "D",type:"wltl"  },
  { name: "通联质量", score: 12, level: "D",type:"wltl" },

  { name: "主数据处理中心（节点）运行", score: 96, level: "A",type:"jzss" },
  { name: "云平台运行", score: 45, level: "D" ,type:"jzss"},
  { name: "网络密码装备和安全防护设备运行", score: 78, level: "C",type:"jzss" },

  { name: "系统部署", score: 99, level: "A" ,type:"yyfw"},
  { name: "信息集成平台与统一身份认证", score: 34, level: "D" ,type:"yyfw"},
  { name: "公共服务应用运行", score: 76, level: "C" ,type:"yyfw"},
  { name: "业务系统应用", score: 18, level: "D" ,type:"yyfw"},
  { name: "迭代升级", score: 85, level: "B" ,type:"yyfw"},

  { name: "体系架构", score: 62, level: "C" ,type:"sjzy"},
  { name: "基础数据采集", score: 49, level: "D" ,type:"sjzy"},
  { name: "动态数据流转", score: 27, level: "D" ,type:"sjzy"},
  { name: "综合分析产", score: 91, level: "A" ,type:"sjzy"},
  { name: "体系融入", score: 39, level: "D" ,type:"sjzy"},

  { name: "制度机制建设", score: 73, level: "C",type:"ywbz" },
  { name: "运维队伍编配管理", score: 15, level: "D" ,type:"ywbz"},
  { name: "运维人员能力水平", score: 82, level: "B" ,type:"ywbz"},
  { name: "经费投入", score: 58, level: "D" ,type:"ywbz" },

  { name: "推广应用效果", score: 41, level: "D" ,type:"zlxy"},
  { name: "用户满意度", score: 99, level: "A" ,type:"zlxy"}
]
//评估指标类数据
window.zrIndicatorTypes = [
  { name: '基础设施', score: 99,level: 'A' ,id:"jzss",type:"category"},
  { name: '数据资源', score: 80,level: 'A' ,id:"sjzy",type:"category"},
  { name: '网络通联', score: 88,level: 'B',id:"wltl" ,type:"category"},
  { name: '应用服务', score: 81,level: 'B' ,id:"yyfw",type:"category"},
  { name: '运维保障', score: 74,level: 'C' ,id:"ywbz",type:"category"},
  { name: '质量效益', score: 60,level: 'D' ,id:"zlxy",type:"category"}
]
window.zrIndicatorCompares = [
  { name: '基础设施', score: 80,level: 'B' ,id:"jzss" },
  { name: '数据资源', score: 100,level: 'A' ,id:"sjzy" },
  { name: '网络通联', score: 80,level: 'B' ,id:"wltl" },
  { name: '应用服务', score: 75,level: 'C' ,id:"yyfw" },
  { name: '运维保障', score: 80,level: 'B' ,id:"ywbz" },
  { name: '质量效益', score: 100,level: 'A' ,id:"zlxy" }
]

  //网路拓扑图关注信息
window.zrNetWorkFocus = "高带宽利用率警告>80、高延迟警告>30"
//综合视图关注信息
window.zrCompFocus = "南部战区（故障）"
//节点视图关注信息
window.zrNodeFocus = "天融信防火墙（故障）、智能调度系统（故障）"
//业务视图关注信息
window.zrBusinessFocus = "天融信防火墙（故障）"

//基础支撑软件层
window.ZrSoftwares = [
  {
    'businessName': '达梦数据库',
    'ip': '************',
    'status': 0
  },  {
    'businessName': '缓存服务',
    'ip': '************',
    'status': 0
  },  {
    'businessName': '消息队列',
    'ip': '************',
    'status': 1
  }
]
//基础支撑硬件层
window.ZrHardwares = [
  {
    'businessName': '核心交换机',
    'ip': '*************',
    'status': 1
  },  {
    'businessName': '天融信入侵检测',
    'ip': '************',
    'status': 0
  },  {
    'businessName': '天融信漏洞扫描',
    'ip': '************',
    'status': 0
  },  {
    'businessName': '天融信堡垒机',
    'ip': '************',
    'status': 0
  },{
    'businessName': '天融信防火墙',
    'ip': '************',
    'status': 2
  },
]
//部署位置：
window.ZrPosition = "10楼001机房"
//管理人员
window.ZrManagers = [
  {
    'realName': '赵云',
    'department': '设备管理处',
    'phone': "***********"
  }
]
//运维人员
window.ZrOperators = [
  {
    'businessName': '数据库',
    'realName': '张飞',
    'phone': "***********"
  },
  {
    'businessName': '服务器集群',
    'realName': '李晓峰',
    'phone': "***********"
  },
  {
    'businessName': '网络架构',
    'realName': '王思远',
    'phone': "***********"
  },
  {
    'businessName': '安全防护',
    'realName': '赵明轩',
    'phone': "***********"
  },
]

//数据中心的地图
window.ZrDatacenterMap = "china"
