<template>
  <a-modal :confirmLoading='confirmLoading' :destroyOnClose="true" :title='title' :visible='visible' :width='800'
    :maskClosable='false' cancelText='关闭' @cancel='handleCancel' @ok='handleOk'>
    <template #footer>
      <a-button key='back' @click='handleCancel'>关闭</a-button>
      <a-button v-show='!disableSubmit' key='submit' :loading='confirmLoading' type='primary' @click='handleOk'>确定
      </a-button>
    </template>
    <a-spin :spinning='confirmLoading'>
      <a-form-model ref='form' :model='model' :rules='validatorRules' :label-col='labelCol' :wrapper-col='wrapperCol'>
        <a-row :gutter='24'>
          <a-col v-bind='formItemLayout'>
            <a-form-model-item hasFeedback label='常用语' prop='languageInfo'>
              <a-input :allow-clear='true' autocomplete='off' :disabled="disableSubmit" v-model='model.languageInfo'
                placeholder='请输入常用语' />
            </a-form-model-item>
          </a-col>
          <a-col v-bind='formItemLayout'>
            <a-form-model-item hasFeedback label='所属模块' prop='module'>
              <a-select :allow-clear='true' :disabled="disableSubmit" v-model='model.module' placeholder='请选择所属模块'>
                <a-select-option :value="item.value" v-for="item in dictOptions" :key="item.value">{{item.text}}</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col v-bind='formItemLayout'>
            <a-form-model-item hasFeedback label='序号' prop='orderBy'>
              <a-input-number :disabled="disableSubmit" :min="0" v-model='model.orderBy' placeholder='请输入序号' />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </a-spin>
  </a-modal>
</template>

<script>
  import {
    addLanguage,
    ediLanguage
  } from '@/api/flowable'
  import {
    ajaxGetDictItems
  } from '@/api/api'
  import {
    getAction
  } from '@api/manage'
  import {
    YqFormVerification
  } from '@/mixins/YqFormVerification'
  import {
    filterDictTextByCache,
    initDictOptions
  } from '@comp/dict/JDictSelectUtil'

  export default {
    name: 'CommonPhrasesForm',
    mixins: [YqFormVerification],
    data() {
      return {
        title: '操作',
        visible: false,
        disableSubmit: false,
        editFlag: false,
        model: {
          languageInfo: undefined,
          orderBy: 0
        },
        languageType: [],
        dictOptions: [],
        formItemLayout: {
          md: {
            span: 24
          },
          sm: {
            span: 24
          }
        },
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          },
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          },
        },
        confirmLoading: false,
        form: this.$form.createForm(this),
        validatorRules: {
          languageInfo: [{
              required: true,
              message: '请输入常用语!'
            },
            {
              min: 1,
              max: 100,
              message: '常用语应在[1-100]个字符之间!',
              trigger: 'change'
            }
          ],
          module: [{
            required: true,
            message: '请选择所属模块!'
          }],
        },
      }
    },
    created() {
      this.getType()
      this.initDictData()
    },
    methods: {
      initDictData() {
        //根据字典Code, 初始化字典数组
        ajaxGetDictItems('Affiliation_module', null).then((res) => {
          if (res.success) {
            this.dictOptions = res.result
          }
        })
      },
      getType() {
        //获取常用语用户类型
        initDictOptions('language_type').then((res) => {
          if (res.success) {
            this.languageType = res.result
          }
        })
      },
      // 添加表单
      add() {
        this.visible = true
      },
      //编辑表单
      edit(record) {
        this.editFlag = true;
        this.model = record
        this.visible = true
      },
      //提交/确认表单数据
      handleOk() {
        const that = this
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true
            let obj
            if (!this.model.createTime) {
              obj = addLanguage(this.model)
            } else {
              obj = ediLanguage(this.model)
            }
            obj.then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.confirmLoading = false
                that.handleCancel()
              } else {
                that.$message.warning(res.message)
              }
            })
          }
        })
      },
      resetModel() {
        this.model = {
          languageInfo: undefined,
          orderBy: 0
        }
      },
      handleCancel() {
        this.resetModel()
        this.editFlag = false;
        this.close()
      },
      //关闭函数
      close() {
        this.$emit('ok')
        this.visible = false
      }
    }
  }
</script>
<style scoped lang='less'>
  @import '~@assets/less/YQNormalModal.less';
</style>