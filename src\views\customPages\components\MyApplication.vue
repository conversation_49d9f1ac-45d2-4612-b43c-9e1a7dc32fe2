<template>
  <a-card :loading='loadingMyProcess' style='width: 100%; height: 100%'>
    <div slot='title'>
      <a-icon type='container' />
      我的申请 【<a slot='extra' @click='goPage'>{{ dataSourceCount3 }}</a>】
      <a-tooltip title='刷新'>
        <a-icon type='sync' style='color: #ccc; cursor: pointer; font-size: 14px' @click='loadDataMyProcess' />
      </a-tooltip>
    </div>
    <a slot='extra' @click='goPage'>更多
      <a-icon type='double-right' />
    </a>
    <a-table :body-style="{ overflowX: 'auto' }"
             rowKey='id'
             :columns='columnsmy'
             bordered
             :dataSource='dataSource3'
             :pagination='false'
             size='middle'>
              <span slot='status' slot-scope='status'>
                <a-tag v-if='status == 1' color='#108ee9'> 处理中</a-tag>
                <a-tag v-if='status == 2' color='#f5222d'> 已结束</a-tag>
                <a-tag v-if='status == 3'> 已撤销</a-tag>
                 <a-tag color='#fa8c16' v-if='status == 4'> 待评价</a-tag>
                <a-tag color='#87d068' v-if='status == 5'> 已评价</a-tag>
              </span>
      <!-- 字符串超长截取省略号显示-->
      <span slot='name' slot-scope='text'>
                <j-ellipsis :value='text' :length='12'></j-ellipsis>
              </span>
      <span slot='action' slot-scope='text, record'>
           <template v-if='record.status == 1'>
            <a href='javascript:void(0);' @click='withdraw(record)'>撤销</a>
            <a-divider type='vertical' />
                <a-dropdown>
                <a class='ant-dropdown-link'>更多
                  <a-icon type='down' /></a>
                <a-menu slot='overlay'>
                  <a-menu-item >
                    <a href='javascript:void(0);' @click='showReturn(record)'>退回</a>
                  </a-menu-item>
                  <a-menu-item>
                    <a href='javascript:' @click="history(record,'查看进度')">查看进度</a>
                  </a-menu-item>
                  <a-menu-item>
                    <a href='javascript:void(0);' @click='detail(record)'>表单数据</a>
                  </a-menu-item>
                </a-menu>
              </a-dropdown>
          </template>
            <template v-if='record.status !==1'>
            <a href='javascript:void(0);' @click="history(record,'审批历史')">审批历史</a>
               <a-divider type='vertical' />
               <a-dropdown>
                <a class='ant-dropdown-link'>更多
                  <a-icon type='down' /></a>
                <a-menu slot='overlay'>
                  <a-menu-item>
                   <a href='javascript:void(0);' @click='detail(record)'>表单数据</a>
                  </a-menu-item>
                  <a-menu-item v-if='record.status ===4'>
                   <a href='javascript:void(0);' @click='evaluate(record,false)'>评价</a>
                  </a-menu-item>
                  <a-menu-item v-if='record.status ===5'>
                    <a href='javascript:void(0);' @click='evaluateLook(record)'>查看评价</a>
                  </a-menu-item>
                </a-menu>
              </a-dropdown>


          </template>

      </span>
    </a-table>
    <process-history-modal ref='processHistoryModal'></process-history-modal>
    <evaluation-modal ref='evaluationModal' @ok='loadDataMyProcess'></evaluation-modal>
    <process-modal ref='processModal'></process-modal>
    <a-modal title='确认撤回' v-model='modalCancelVisible' :mask-closable='false' :width='500'>
      <a-form ref='delForm' v-model='cancelForm' :label-width='70' v-if='modalCancelVisible'>
        <a-form-item label='撤回原因' prop='reason'>
          <a-input v-model='cancelForm.reason' :allow-clear='true' :rows='4' autocomplete='off' type='textarea' />
        </a-form-item>
      </a-form>
      <div slot='footer' style='text-align: right'>
        <a-button type='text' @click='modalCancelVisible = false'>取消</a-button>
        <a-button type='primary' :disabled='submitLoading' @click='handelSubmitCancel'>提交</a-button>
      </div>
    </a-modal>
    <div v-if="taskBackNodesVisible">
      <task-back-nodes
        ref="taskBackNodesRef"
        :visible.sync="taskBackNodesVisible"
        :execute-task-id="taskId"
        @backTaskFinished="handleBackTask"
      />
    </div>
  </a-card>
</template>

<script>
import { deleteAction, getAction,postAction} from '@api/manage'
import evaluationModal from '@views/flowable/myProcess/modules/EvaluationModal.vue'
import ProcessHistoryModal from '@views/flowable/myProcess/modules/ProcessHistoryModal.vue'
import ProcessModal from '@views/flowable/myProcess/modules/ProcessModal.vue'
import TaskBackNodes from '../../flowable/task-todo/modules/TaskBackNodes.vue'
import { putAction } from '../../../api/manage'

export default {
  name: 'MyApplication',
  components: { TaskBackNodes, ProcessModal, ProcessHistoryModal, evaluationModal },
  data() {
    return {
      loadingMyProcess: false,
      dataSource3: [],
      dataSourceCount3: 0,
      columnsmy: [
        {
          title: '业务标题',
          width: '23%',
          dataIndex: 'name',
          scopedSlots: {
            customRender: 'name'
          },
          align: 'center'
        },
        {
          title: '当前审批环节',
          width: '20%',
          dataIndex: 'currTaskName',
          align: 'center'
        },
        {
          title: '状态',
          width: '8%',
          dataIndex: 'status',
          scopedSlots: {
            customRender: 'status'
          },
          align: 'center'
        },
        {
          title: '提交申请时间',
          width: '20%',
          align: 'center',
          dataIndex: 'startTime'
        },
        {
          title: '操作',
          width: '25%',
          dataIndex: 'action',
          scopedSlots: {
            customRender: 'action'
          },
          align: 'center'
        }
      ],
      modalCancelVisible: false,
      cancelForm: {
        reason: '撤回申请',
        cascade: false,
        processInstanceId: null
      },
      submitLoading: false,
      url: {
        myProcessList: '/flowable/processInstance/listStartedByMe',
        withdrowUrl: '/flowable/processInstance/delete',
        cancel: '/business/actZBusiness/cancel'
      },
      taskBackNodesVisible: false,
      taskId:"",
    }
  },
  created() {
    this.loadDataMyProcess()
  },
  mounted() {
  },
  computed: {
    tasktimestimp() {
      return this.$store.getters.tasktimestimp
    }
  },
  watch: {
    tasktimestimp: {
      handler(nval, oval) {
        this.loadDataMyProcess()
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    loadDataMyProcess() {
      if (!this.url.myProcessList) {
        this.$message.error('请设置url.myProcessList!')
        return
      }
      this.loadingMyProcess = true
      getAction(this.url.myProcessList, {
        pageNo: 1,
        pageSize: 6
      }).then((res) => {
        if (res.success) {
          this.dataSource3 = res.result.records || res.result
          this.dataSourceCount3 = res.result.total
        } else {
          this.$message.warning(res.message)
        }
        this.loadingMyProcess = false
      })
    },
    handelSubmitCancel() {
      this.submitLoading = true
      postAction(this.url.cancel, this.cancelForm)
        .then((res) => {
          console.log(res)
          if (res.code == 200) {
            this.$message.success('撤销成功,表单已退回至草稿')
          } else {
            this.$message.error('撤销失败' + res.message)
          }
          this.modalCancelVisible = false
          this.resetCancleForm()
          this.loadDataMyProcess()
        })
        .finally(() => (this.submitLoading = false))
    },
    evaluateLook(record) {
      this.$refs.evaluationModal.edit(record.id)
      this.$refs.evaluationModal.title = '查看评价'
      this.$refs.evaluationModal.disableSubmit = false
    },
    //点击评价
    evaluate(record) {
      this.$refs.evaluationModal.add(record.id)
      this.$refs.evaluationModal.title = '评价'
      this.$refs.evaluationModal.disableSubmit = false
    },
    //撤回
    withdraw(record) {
      //this.cancelForm.id = v.id
      this.cancelForm.procInsId = record.id
      this.modalCancelVisible = true
    },
    //退回
    showReturn(record) {
      this.$confirm({
        title: '确认退回',
        content: '确定要退回此任务吗？',
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          getAction('/flowable/task/getTaskIdByProcessInstanceId', { processInstanceId: record.id })
            .then(res => {
              const { result } = res
              if (result) {
                this.taskId = result;
                this.taskBackNodesVisible = true;
              }

            })
            .catch(error => {
              // 处理异步请求失败的情况
              console.error('请求失败:', error);
              this.$message.error('请求失败，请重试');
            });
        },
        onCancel: () => {
          // 用户取消操作
        }
      });
    },
    handleBackTask(backNode) {
      putAction('/flowable/task/back', {
        taskId: this.taskId,
        activityId: backNode.nodeId,
        activityName: backNode.nodeName,
        userId: backNode.userId,
        message: this.message
      }).then(res => {
        this.$message.success(res.message)
        this.taskBackNodesVisible = false
        this.$store.commit('TASK_TIMESTIMP', res.timestamp)
        this.$emit('close')
        this.init(this.processInstanceId,this.isAddKnowledge=true)
      })
    },
    resetCancleForm() {
      this.cancelForm = {
        reason: '撤回申请',
        cascade: false,
        processInstanceId: null
      }
    }, //查看进度
    history(record, title) {
      if (!record.id) {
        this.$message.error('流程实例ID不存在')
        return
      }
      record.state = record.endTime
      this.$refs.processHistoryModal.init(record.id)
      this.$refs.processHistoryModal.title = title
      this.$refs.processHistoryModal.disableSubmit = false
    },
    detail(record) {
      if (!record.id) {
        this.$message.error('申请不存在')
        return
      }
      this.$refs.processModal.init(record)
    },
    goPage(arg) {
      this.$router.push({
        path: '/flowable/myProcess'
      })
    }
  }
}
</script>


<style scoped lang='less'>
/deep/ .ant-card-body {
  height: calc(100% - 56px);
  overflow-y: auto;
}
</style>