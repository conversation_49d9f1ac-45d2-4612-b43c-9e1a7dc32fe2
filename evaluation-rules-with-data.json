{"list": [{"type": "batch", "label": "外层动态表格", "list": [{"type": "select", "label": "外层下拉框", "icon": "icon-xiala", "options": {"width": "100%", "tableWidth": "150px", "multiple": false, "disabled": false, "clearable": false, "hidden": false, "placeholder": "请选择外层选项", "linkageData": "[]", "changeFunc": "function(value, key, vm, http) {\n  console.log('外层下拉框值变化:', value, key);\n  \n  // 定义内层下拉框的数据映射\n  const innerDataMap = {\n    'category1': [\n      { value: 'item1_1', label: '类别1-项目1' },\n      { value: 'item1_2', label: '类别1-项目2' },\n      { value: 'item1_3', label: '类别1-项目3' }\n    ],\n    'category2': [\n      { value: 'item2_1', label: '类别2-项目1' },\n      { value: 'item2_2', label: '类别2-项目2' },\n      { value: 'item2_3', label: '类别2-项目3' }\n    ],\n    'category3': [\n      { value: 'item3_1', label: '类别3-项目1' },\n      { value: 'item3_2', label: '类别3-项目2' }\n    ]\n  };\n  \n  // 获取当前行的数据\n  const currentRowData = vm.model || {};\n  console.log('当前行数据:', currentRowData);\n  \n  // 更新当前行的内层下拉框选项\n  if (value && innerDataMap[value]) {\n    try {\n      // 通过vm.$parent访问batch组件\n      const batchVm = vm.$parent;\n      if (batchVm && batchVm.dataSources) {\n        // 计算内层下拉框的key（同一行的内层下拉框）\n        const innerSelectKey = key.replace('select_1754985501160', 'select_1754985505711');\n        \n        // 更新内层下拉框的数据源\n        batchVm.dataSources[innerSelectKey] = [...innerDataMap[value]];\n        \n        // 强制更新batch组件\n        batchVm.$forceUpdate();\n        \n        console.log('内层下拉框数据已更新:', innerSelectKey, innerDataMap[value]);\n      }\n    } catch (error) {\n      console.error('更新内层下拉框数据时出错:', error);\n    }\n  } else if (!value) {\n    // 重置内层下拉框\n    try {\n      const batchVm = vm.$parent;\n      if (batchVm && batchVm.dataSources) {\n        const innerSelectKey = key.replace('select_1754985501160', 'select_1754985505711');\n        batchVm.dataSources[innerSelectKey] = [{ value: '', label: '请先选择外层选项' }];\n        batchVm.$forceUpdate();\n        console.log('内层下拉框已重置:', innerSelectKey);\n      }\n    } catch (error) {\n      console.error('重置内层下拉框时出错:', error);\n    }\n  }\n}", "dynamicKey": "", "dynamic": "static", "ajaxData": {"url": "", "type": "GET", "params": "{}", "header": "{}", "callFunc": "function(res){\n          return res.data;\n        }"}, "options": [{"value": "category1", "label": "类别1"}, {"value": "category2", "label": "类别2"}, {"value": "category3", "label": "类别3"}], "staticOptions": [{"value": "category1", "label": "类别1"}, {"value": "category2", "label": "类别2"}, {"value": "category3", "label": "类别3"}], "showSearch": false, "isEvaluationField": false, "showLabel": true}, "model": "select_1754985501160", "key": "select_1754985501160", "help": "", "rules": [{"required": false, "message": "必填项"}]}, {"type": "batch", "label": "内层动态表格", "icon": "icon-biaoge", "list": [{"type": "select", "label": "内层下拉框", "icon": "icon-xiala", "options": {"width": "100%", "tableWidth": "150px", "multiple": false, "disabled": false, "clearable": false, "hidden": false, "placeholder": "请先选择外层选项", "linkageData": "[]", "changeFunc": "function(value, key, vm, http) {\n  console.log('内层下拉框值变化:', value, key);\n  \n  // 监听外层下拉框的更新事件\n  if (!vm._innerSelectEventListenerAdded) {\n    vm._innerSelectEventListenerAdded = true;\n    \n    // 监听更新事件\n    document.addEventListener('updateInnerSelect', function(event) {\n      const detail = event.detail;\n      // 检查是否是当前行的更新事件\n      const currentRowKey = key.replace('select_1754985505711', 'select_1754985501160');\n      if (detail.outerKey === currentRowKey) {\n        console.log('收到内层下拉框更新事件:', detail);\n        \n        // 更新当前下拉框的选项\n        if (vm.$parent && vm.$parent.$refs && vm.$parent.$refs.batchTable) {\n          // 通过父组件更新选项\n          const batchComponent = vm.$parent.$refs.batchTable;\n          if (batchComponent && batchComponent.dataSources) {\n            batchComponent.dataSources[key] = detail.innerOptions;\n            batchComponent.$forceUpdate();\n          }\n        }\n        \n        // 直接更新组件的数据源\n        if (vm.localDataSource) {\n          vm.localDataSource = detail.innerOptions;\n          vm.$forceUpdate();\n        }\n      }\n    });\n    \n    // 监听重置事件\n    document.addEventListener('resetInnerSelect', function(event) {\n      const detail = event.detail;\n      const currentRowKey = key.replace('select_1754985505711', 'select_1754985501160');\n      if (detail.outerKey === currentRowKey) {\n        console.log('收到内层下拉框重置事件');\n        \n        // 重置选项\n        const resetOptions = [{ value: '', label: '请先选择外层选项' }];\n        if (vm.$parent && vm.$parent.$refs && vm.$parent.$refs.batchTable) {\n          const batchComponent = vm.$parent.$refs.batchTable;\n          if (batchComponent && batchComponent.dataSources) {\n            batchComponent.dataSources[key] = resetOptions;\n            batchComponent.$forceUpdate();\n          }\n        }\n        \n        if (vm.localDataSource) {\n          vm.localDataSource = resetOptions;\n          vm.$forceUpdate();\n        }\n      }\n    });\n  }\n}", "dynamicKey": "", "dynamic": "static", "ajaxData": {"url": "", "type": "GET", "params": "{}", "header": "{}", "callFunc": "function(res){\n          return res.data;\n        }"}, "options": [{"value": "", "label": "请先选择外层选项"}], "staticOptions": [{"value": "", "label": "请先选择外层选项"}], "showSearch": false, "isEvaluationField": false, "showLabel": true}, "model": "select_1754985505711", "key": "select_1754985505711", "help": "", "rules": [{"required": false, "message": "必填项"}]}], "options": {"scrollY": 0, "disabled": false, "hidden": false, "showLabel": true, "hideSequence": false, "width": "100%", "rowKey": "id", "hideAddBtn": false, "hideOprCol": false, "linkageData": "[]", "changeFunc": "function(value, key, vm, http) {\n  console.log('内层动态表格数据变化:', value, key);\n  // 内层动态表格的变化回调\n}"}, "model": "batch_1754985503227", "key": "batch_1754985503227", "help": ""}], "options": {"scrollY": 0, "disabled": false, "hidden": false, "showLabel": true, "hideSequence": false, "width": "100%", "rowKey": "id", "hideAddBtn": false, "hideOprCol": false, "linkageData": "[]", "changeFunc": "function(value, key, vm, http) {\n  console.log('外层动态表格数据变化:', value, key);\n  // 外层动态表格的变化回调\n}"}, "model": "batch_1754985498661", "key": "batch_1754985498661", "help": ""}], "config": {"layout": "horizontal", "labelCol": {"xs": 4, "sm": 4, "md": 4, "lg": 4, "xl": 4, "xxl": 4}, "labelWidth": 100, "labelLayout": "flex", "wrapperCol": {"xs": 18, "sm": 18, "md": 18, "lg": 18, "xl": 18, "xxl": 18}, "hideRequiredMark": false, "customStyle": ""}}