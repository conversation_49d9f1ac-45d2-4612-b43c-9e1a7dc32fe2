{"list": [{"type": "batch", "label": "外层动态表格", "list": [{"type": "select", "label": "外层下拉框", "icon": "icon-xiala", "options": {"width": "100%", "tableWidth": "150px", "multiple": false, "disabled": false, "clearable": false, "hidden": false, "placeholder": "请选择外层选项", "linkageData": "[]", "changeFunc": "function(value, key, vm, http) {\n  console.log('外层下拉框值变化:', value, key);\n  \n  // 定义内层下拉框的数据映射\n  const innerDataMap = {\n    'category1': [\n      { value: 'item1_1', label: '类别1-项目1' },\n      { value: 'item1_2', label: '类别1-项目2' },\n      { value: 'item1_3', label: '类别1-项目3' }\n    ],\n    'category2': [\n      { value: 'item2_1', label: '类别2-项目1' },\n      { value: 'item2_2', label: '类别2-项目2' },\n      { value: 'item2_3', label: '类别2-项目3' }\n    ],\n    'category3': [\n      { value: 'item3_1', label: '类别3-项目1' },\n      { value: 'item3_2', label: '类别3-项目2' }\n    ]\n  };\n  \n  // 更新内层下拉框的选项数据\n  if (value && innerDataMap[value]) {\n    try {\n      // 递归遍历表单配置，找到内层下拉框并更新其选项\n      const traverseAndUpdate = (list, targetValue) => {\n        list.forEach(item => {\n          if (item.type === 'batch' && item.model === 'batch_1754985503227') {\n            // 找到内层动态表格\n            item.list.forEach(innerItem => {\n              if (innerItem.type === 'select' && innerItem.model === 'select_1754985505711') {\n                // 更新内层下拉框的选项\n                innerItem.options.options = [...innerDataMap[targetValue]];\n                innerItem.options.staticOptions = [...innerDataMap[targetValue]];\n                console.log('内层下拉框数据已更新为:', innerDataMap[targetValue]);\n              }\n            });\n          } else if (item.list && Array.isArray(item.list)) {\n            traverseAndUpdate(item.list, targetValue);\n          }\n        });\n      };\n      \n      traverseAndUpdate(vm.value.list, value);\n      \n      // 强制更新视图\n      vm.$nextTick(() => {\n        vm.$forceUpdate();\n      });\n      \n    } catch (error) {\n      console.error('更新内层下拉框数据时出错:', error);\n    }\n  } else if (!value) {\n    // 如果外层下拉框清空，重置内层下拉框\n    try {\n      const resetInnerSelect = (list) => {\n        list.forEach(item => {\n          if (item.type === 'batch' && item.model === 'batch_1754985503227') {\n            item.list.forEach(innerItem => {\n              if (innerItem.type === 'select' && innerItem.model === 'select_1754985505711') {\n                innerItem.options.options = [{ value: '', label: '请先选择外层选项' }];\n                innerItem.options.staticOptions = [{ value: '', label: '请先选择外层选项' }];\n              }\n            });\n          } else if (item.list && Array.isArray(item.list)) {\n            resetInnerSelect(item.list);\n          }\n        });\n      };\n      \n      resetInnerSelect(vm.value.list);\n      vm.$forceUpdate();\n      \n    } catch (error) {\n      console.error('重置内层下拉框时出错:', error);\n    }\n  }\n}", "dynamicKey": "", "dynamic": "static", "ajaxData": {"url": "", "type": "GET", "params": "{}", "header": "{}", "callFunc": "function(res){\n          return res.data;\n        }"}, "options": [{"value": "category1", "label": "类别1"}, {"value": "category2", "label": "类别2"}, {"value": "category3", "label": "类别3"}], "staticOptions": [{"value": "category1", "label": "类别1"}, {"value": "category2", "label": "类别2"}, {"value": "category3", "label": "类别3"}], "showSearch": false, "isEvaluationField": false, "showLabel": true}, "model": "select_1754985501160", "key": "select_1754985501160", "help": "", "rules": [{"required": false, "message": "必填项"}]}, {"type": "batch", "label": "内层动态表格", "icon": "icon-biaoge", "list": [{"type": "select", "label": "内层下拉框", "icon": "icon-xiala", "options": {"width": "100%", "tableWidth": "150px", "multiple": false, "disabled": false, "clearable": false, "hidden": false, "placeholder": "请先选择外层选项", "linkageData": "[]", "changeFunc": "function(value, key, vm, http) {\n  console.log('内层下拉框值变化:', value, key);\n  // 可以在这里添加内层下拉框变化后的逻辑\n}", "dynamicKey": "", "dynamic": "static", "ajaxData": {"url": "", "type": "GET", "params": "{}", "header": "{}", "callFunc": "function(res){\n          return res.data;\n        }"}, "options": [{"value": "", "label": "请先选择外层选项"}], "staticOptions": [{"value": "", "label": "请先选择外层选项"}], "showSearch": false, "isEvaluationField": false, "showLabel": true}, "model": "select_1754985505711", "key": "select_1754985505711", "help": "", "rules": [{"required": false, "message": "必填项"}]}], "options": {"scrollY": 0, "disabled": false, "hidden": false, "showLabel": true, "hideSequence": false, "width": "100%", "rowKey": "id", "hideAddBtn": false, "hideOprCol": false, "linkageData": "[]", "changeFunc": "function(value, key, vm, http) {\n  console.log('内层动态表格数据变化:', value, key);\n  // 内层动态表格的变化回调\n}"}, "model": "batch_1754985503227", "key": "batch_1754985503227", "help": ""}], "options": {"scrollY": 0, "disabled": false, "hidden": false, "showLabel": true, "hideSequence": false, "width": "100%", "rowKey": "id", "hideAddBtn": false, "hideOprCol": false, "linkageData": "[]", "changeFunc": "function(value, key, vm, http) {\n  console.log('外层动态表格数据变化:', value, key);\n  // 外层动态表格的变化回调\n}"}, "model": "batch_1754985498661", "key": "batch_1754985498661", "help": ""}], "config": {"layout": "horizontal", "labelCol": {"xs": 4, "sm": 4, "md": 4, "lg": 4, "xl": 4, "xxl": 4}, "labelWidth": 100, "labelLayout": "flex", "wrapperCol": {"xs": 18, "sm": 18, "md": 18, "lg": 18, "xl": 18, "xxl": 18}, "hideRequiredMark": false, "customStyle": ""}}