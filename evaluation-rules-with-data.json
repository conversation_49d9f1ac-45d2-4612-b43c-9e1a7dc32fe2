{"list": [{"type": "batch", "label": "外层动态表格", "list": [{"type": "select", "label": "外层下拉框", "icon": "icon-xiala", "options": {"width": "100%", "tableWidth": "150px", "multiple": false, "disabled": false, "clearable": false, "hidden": false, "placeholder": "请选择外层选项", "linkageData": "[]", "changeFunc": "function(value, key, vm, http) {\n  console.log('外层下拉框值变化:', value, key);\n  \n  // 定义内层下拉框的数据映射\n  const innerDataMap = {\n    'category1': [\n      { value: 'item1_1', label: '类别1-项目1' },\n      { value: 'item1_2', label: '类别1-项目2' },\n      { value: 'item1_3', label: '类别1-项目3' }\n    ],\n    'category2': [\n      { value: 'item2_1', label: '类别2-项目1' },\n      { value: 'item2_2', label: '类别2-项目2' },\n      { value: 'item2_3', label: '类别2-项目3' }\n    ],\n    'category3': [\n      { value: 'item3_1', label: '类别3-项目1' },\n      { value: 'item3_2', label: '类别3-项目2' }\n    ]\n  };\n  \n  // 使用全局事件机制来更新内层下拉框\n  if (value && innerDataMap[value]) {\n    // 创建全局事件总线（如果不存在）\n    if (!window.formEventBus) {\n      window.formEventBus = new Vue();\n    }\n    \n    // 发送更新事件\n    window.formEventBus.$emit('updateInnerSelect', {\n      outerKey: key,\n      outerValue: value,\n      innerOptions: innerDataMap[value],\n      timestamp: Date.now()\n    });\n    \n    console.log('已发送内层下拉框更新事件:', innerDataMap[value]);\n  } else if (!value) {\n    // 发送重置事件\n    if (!window.formEventBus) {\n      window.formEventBus = new Vue();\n    }\n    \n    window.formEventBus.$emit('resetInnerSelect', {\n      outerKey: key,\n      timestamp: Date.now()\n    });\n    \n    console.log('已发送内层下拉框重置事件');\n  }\n}", "dynamicKey": "", "dynamic": "static", "ajaxData": {"url": "", "type": "GET", "params": "{}", "header": "{}", "callFunc": "function(res){\n          return res.data;\n        }"}, "options": [{"value": "category1", "label": "类别1"}, {"value": "category2", "label": "类别2"}, {"value": "category3", "label": "类别3"}], "staticOptions": [{"value": "category1", "label": "类别1"}, {"value": "category2", "label": "类别2"}, {"value": "category3", "label": "类别3"}], "showSearch": false, "isEvaluationField": false, "showLabel": true}, "model": "select_1754985501160", "key": "select_1754985501160", "help": "", "rules": [{"required": false, "message": "必填项"}]}, {"type": "batch", "label": "内层动态表格", "icon": "icon-biaoge", "list": [{"type": "select", "label": "内层下拉框", "icon": "icon-xiala", "options": {"width": "100%", "tableWidth": "150px", "multiple": false, "disabled": false, "clearable": false, "hidden": false, "placeholder": "请先选择外层选项", "linkageData": "[]", "changeFunc": "function(value, key, vm, http) {\n  console.log('内层下拉框值变化:', value, key);\n  \n  // 设置事件监听器（只设置一次）\n  if (!vm._eventListenersSet) {\n    vm._eventListenersSet = true;\n    \n    // 创建全局事件总线（如果不存在）\n    if (!window.formEventBus) {\n      window.formEventBus = new Vue();\n    }\n    \n    // 监听更新事件\n    window.formEventBus.$on('updateInnerSelect', function(data) {\n      console.log('内层下拉框收到更新事件:', data);\n      \n      // 检查是否是当前行的事件（通过key匹配）\n      const expectedOuterKey = key.replace('select_1754985505711', 'select_1754985501160');\n      if (data.outerKey === expectedOuterKey) {\n        console.log('匹配当前行，开始更新内层下拉框选项');\n        \n        // 获取batch组件并更新数据源\n        const batchVm = vm.$parent;\n        if (batchVm && batchVm.dataSources) {\n          batchVm.dataSources[key] = [...data.innerOptions];\n          batchVm.$forceUpdate();\n          console.log('内层下拉框选项已更新:', data.innerOptions);\n        }\n      }\n    });\n    \n    // 监听重置事件\n    window.formEventBus.$on('resetInnerSelect', function(data) {\n      console.log('内层下拉框收到重置事件:', data);\n      \n      const expectedOuterKey = key.replace('select_1754985505711', 'select_1754985501160');\n      if (data.outerKey === expectedOuterKey) {\n        console.log('匹配当前行，开始重置内层下拉框');\n        \n        const batchVm = vm.$parent;\n        if (batchVm && batchVm.dataSources) {\n          batchVm.dataSources[key] = [{ value: '', label: '请先选择外层选项' }];\n          batchVm.$forceUpdate();\n          console.log('内层下拉框已重置');\n        }\n      }\n    });\n  }\n}", "dynamicKey": "", "dynamic": "static", "ajaxData": {"url": "", "type": "GET", "params": "{}", "header": "{}", "callFunc": "function(res){\n          return res.data;\n        }"}, "options": [{"value": "", "label": "请先选择外层选项"}], "staticOptions": [{"value": "", "label": "请先选择外层选项"}], "showSearch": false, "isEvaluationField": false, "showLabel": true}, "model": "select_1754985505711", "key": "select_1754985505711", "help": "", "rules": [{"required": false, "message": "必填项"}]}], "options": {"scrollY": 0, "disabled": false, "hidden": false, "showLabel": true, "hideSequence": false, "width": "100%", "rowKey": "id", "hideAddBtn": false, "hideOprCol": false, "linkageData": "[]", "changeFunc": "function(value, key, vm, http) {\n  console.log('内层动态表格数据变化:', value, key);\n  // 内层动态表格的变化回调\n}"}, "model": "batch_1754985503227", "key": "batch_1754985503227", "help": ""}], "options": {"scrollY": 0, "disabled": false, "hidden": false, "showLabel": true, "hideSequence": false, "width": "100%", "rowKey": "id", "hideAddBtn": false, "hideOprCol": false, "linkageData": "[]", "changeFunc": "function(value, key, vm, http) {\n  console.log('外层动态表格数据变化:', value, key);\n  // 外层动态表格的变化回调\n}"}, "model": "batch_1754985498661", "key": "batch_1754985498661", "help": ""}], "config": {"layout": "horizontal", "labelCol": {"xs": 4, "sm": 4, "md": 4, "lg": 4, "xl": 4, "xxl": 4}, "labelWidth": 100, "labelLayout": "flex", "wrapperCol": {"xs": 18, "sm": 18, "md": 18, "lg": 18, "xl": 18, "xxl": 18}, "hideRequiredMark": false, "customStyle": ""}}