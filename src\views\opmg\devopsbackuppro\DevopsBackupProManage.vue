<template>
  <div style="height:100%">
      <component :is="pageName" :data="data"/>
  </div>
</template>
<script>
  import DevopsBackupProList from './DevopsBackupProList'
  import DevopsBackupProDetails from './modules/DevopsBackupProDetails'
  export default {
    name: "DevopsIpManage",
    data() {
      return {
        isActive: 0,
        data:{}
      };
    },
    components: {
      DevopsBackupProList,
      DevopsBackupProDetails
    },
    created(){
      this.pButton1(0);
    },
    //使用计算属性
    computed: {
      pageName() {
        switch (this.isActive) {
          case 0:
            return "DevopsBackupProList";
            break;

          default:
            return "DevopsBackupProDetails";
            break;
        }
      }
    },
    methods: {
      pButton1(index) {
        this.isActive = index;
      },
      pButton2(index,item) {
        this.isActive = index;
        this.data = item;
      }
    }
  }
</script>