<template>
  <card-frame :title="'设备状态监控'" :sub-title="''">
    <div slot="bodySlot" class="empty-wrapper" v-if="Object.keys(chartData).length === 0">
      <a-spin :spinning="loading" v-if="loading" class="spin"></a-spin>
      <a-list :data-source="[]" v-else />
    </div>
<!--    <div ref='chart-box' class="chart-box" slot="bodySlot" v-else>
      <div class='chart-content'>
       <div class='circle circle-left' :style='{width:circleSize+"px",height:circleSize+"px"}'>
         <annular-pie :data='chartData.data1' :max='max' :title='"设备\n状态"'></annular-pie>
       </div>
        <div class='rect rect-right'>
          <div class='data-wrapper'>
            <div class='data-one'>
              <div class='count'>10000000000000000000000000000</div>
              <div class='desc desc-one'>在线</div>
            </div>
            <div class='data-two'>
              <div class='count'>965000000000000000000</div>
              <div class='desc desc-one'>离线</div>
            </div>
          </div>

        </div>
      </div>
      <div class='chart-content'>
        <div class='rect rect-left'>
          <div class='data-wrapper'>
            <div class='data-one'>
              <div class='count'>6220000000000000000000000</div>
              <div class='desc desc-two'>告警</div>
            </div>
            <div class='data-two'>
              <div class='count'>133400000000000000000000000</div>
              <div class='desc desc-two'>正常</div>
            </div>
          </div>
        </div>
        <div class='circle circle-right' :style='{width:circleSize+"px",height:circleSize+"px"}'>
          <annular-pie
            :data='chartData.data2'
            :max='max'
            :back-ground-pie-color='backGroundPieColor'
            :bar-color='barColor'
            :dot-color='dotColor'
            :title='"告警\n状态"'
            :title-color='titleColor'
          />
        </div>
      </div>
    </div>-->
    <div ref='chart-box' class="chart-box" slot="bodySlot" v-else>
      <div
        class='chart-content'
        :style='{height:circleSize+"px"}'
        v-for='(item,index) in rectData'
        :key='"rect_"+index'>
        <div class='circle' :class='index===0?"circle-left":"circle-right"' :style='{width:circleSize+"px",height:"100%"}'>
          <annular-pie v-if='index===0'
            :data='chartData.data1'
            :max='max'
            :title='rectTitle[index]' >
          </annular-pie>
          <annular-pie v-else
                       :data='chartData.data2'
                       :max='max'
                       :back-ground-pie-color='backGroundPieColor'
                       :bar-color='barColor'
                       :dot-color='dotColor'
                       :title='rectTitle[index]'
                       :title-color='titleColor'>
          </annular-pie>
        </div>
        <div class='rect' :class='index===0?"rect-right":"rect-left"'>
          <div class='data-wrapper'>
            <div :class='idx===0?"data-one":"data-two"' v-for='(m,idx) in item' :key='"rectData_"+index+"_"+idx'>
              <div class='count'>{{m.value}}</div>
              <div class='desc' :class='index===0?"desc-one":"desc-two"'>{{m.name}}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </card-frame>
</template>
<script>
import { getAction } from '@api/manage'
import cardFrame from '@views/statsCenter/com/cardFrame.vue'
import 'echarts-gl'
import echarts from 'echarts'
import { heightPixel, widthPixel } from '@views/statsCenter/com/calculatePixel'
import resizeObserverMixin from '@views/statsCenter/com/resizeObserverMixin'
import { queryConfigureDictItem } from '@/api/api'
import annularPie from '@views/statsCenter/homepageStatistics/modules/annularPie.vue'
export default {
  name: 'deviceStatus',
  components: { cardFrame, annularPie },
  mixins: [resizeObserverMixin],
  props: {
    adcode: {
      type: String,
      required: false,
      default: '',
    },
    handleRefresh: {
      type: Number,
      required: false,
      default: 0,
    }
  },
  data() {
    return {
      loading: false,
      topAreaId: '',

      circleSize: '',

      url: {
        status: '/data-analysis/index/getDeviceStatusCountByCity',
      },
      rectData: [],
      rectTitle:["设备\n状态","告警\n状态"],
      rectDataType: [
        { name: '在线', field: 'active' },
        { name: '离线', field: 'offLine' },
        { name: '告警', field: 'alarm' },
        { name: '正常', field: 'normal' }
      ],

      chartData: {},
      max: 0,
      chartDataType: [
        { name: '在线', field: 'active' },
        { name: '告警', field: 'alarm' }
      ],
      backGroundPieColor: ['#08284c', '#0d3b4f'],
      barColor: ['rgba(81,252,241,0)', 'rgba(81,252,241,0.7)', 'rgb(81,252,241)'],
      dotColor: '#4cf5ff',
      titleColor: '#47E5EF'
    }
  },
  watch: {
    adcode: {
      handler(nVal, oVal) {
        this.deviceStatus(nVal)
      },
      immediate: true,
      deep: true
    },
    handleRefresh: {
      handler(nVal, oVal) {
        this.deviceStatus(this.adcode)
      }
    }
  },
  created() {
    this.getCircleAndFontSize()
    this.setLeftOrRightOffset()
  },
  destroyed() {
    window.removeEventListener('resize', this.setCirleAndRectSize)
  },
  mounted() {
    window.addEventListener('resize', this.setCirleAndRectSize)
  },
  methods: {
    setCirleAndRectSize() {
      this.$nextTick(() => {
        this.getCircleAndFontSize()
        this.setLeftOrRightOffset()
      })
    },

    setLeftOrRightOffset() {
      document.body.style.setProperty('--rectOffsetSize', this.circleSize / 2 + "px")
      document.body.style.setProperty('--gradientPosition', (this.circleSize / 2 - 3) + "px")
      document.body.style.setProperty('--circleTopOffset', 'calc((100% - (' + `${this.circleSize + "px"}` + ')) /2)}')

      let cSize = this.circleSize + 'px'
      document.body.style.setProperty('--rectHeight', 'calc(' + `${cSize}` + ' * 67 / 104)')

      let rectH = (this.circleSize * 67 / 104) + 'px'
      document.body.style.setProperty('--rectTop', 'calc((100% - ' + `${rectH}` + ') / 2)')
    },

    getCircleAndFontSize() {
      let w104 = widthPixel(104)
      let h104 = heightPixel(104)
      this.circleSize = w104 >= h104 ? h104 : w104
    },

    deviceStatus(adcode) {
      this.loading = true
      this.chartData = {}
      this.rectData = {}
      if (!this.topAreaId) {
        this.getConfigureDict().then((res) => {
          if (res.success) {
            let code = adcode ? adcode : this.topAreaId
            this.drawPie(code)
          } else {
            this.$message.warning(res.message)
            this.loading = false
          }
        }).catch((err) => {
          this.$message.warning(err.message)
          this.loading = false
        })
      } else {
        let code = adcode ? adcode : this.topAreaId
        this.drawPie(code)
      }
    },

    getConfigureDict() {
      return new Promise((resolve, reject) => {
        queryConfigureDictItem({ parentCode: 'project_cityId', childCode: 'id' }).then((res) => {
          if (res.success) {
            this.topAreaId = res.result
            resolve({
              success: true,
              message: res.message
            })
          } else {
            reject({
              success: false,
              message: res.message
            })
          }
        }).catch((err) => {
          reject({
            success: false,
            message: err.message
          })
        })
      })
    },

    drawPie(adCode) {
      getAction(this.url.status, { cityId: adCode }).then((res) => {
        // console.log("设备状态联调 === ",res)
        this.chartData = {}
        this.rectData = {}
        if (res.success) {
          let tempData = null
          if(adCode){
            tempData= res.result
          }
          else if (res.result.length > 0) {
            for (let i = 0; i < res.result.length; i++) {
              let m = res.result[i]
              if (adCode == m.areaId) {
                tempData = m
                break
              }
            }

          }
          if (tempData) {
              //获取饼图数据
              let chartValues = []
              for (let i = 0; i < this.chartDataType.length; i++) {
                let key = this.chartDataType[i].field
                if (tempData.hasOwnProperty(key)) {
                  let obj = {
                    name: this.chartDataType[i].name,
                    value: tempData[key] ? parseFloat(tempData[key]) : 0
                  }
                  chartValues.push(obj)
                }
              }
              if (chartValues.length > 0) {
                this.max = tempData.total
                let nValue = parseFloat(this.max) - chartValues.slice(1)[0].value
                let nObj = { name: '正常', value: nValue }
                this.chartData = { data1: chartValues.slice(0, 1), data2: [nObj] }
              }

              //获取条形框数据
              let rectValues = []
              for (let i = 0; i < this.rectDataType.length; i++) {
                let key = this.rectDataType[i].field
                if (tempData.hasOwnProperty(key)) {
                  let obj = {
                    name: this.rectDataType[i].name,
                    value: tempData[key] ? parseFloat(tempData[key]) : 0
                  }
                  rectValues.push(obj)
                }
              }
              if (rectValues.length > 0) {
                let nValue = parseFloat(this.max) - rectValues[2].value
                let nObj = { name: '正常', value: nValue }
                rectValues.push(nObj)
                this.rectData = [rectValues.slice(0, 2), rectValues.slice(2)]
              }
            }

        } else {
          this.$message.warning(res.message)
        }
        this.loading = false
      })
        .catch((err) => {
          this.$message.warning(err.message)
          this.loading = false
        })
    }
  }
}
</script>

<style scoped lang='less'>
.chart-box {
  height: 100%;
  width: 100%;
  display: flex;
  flex-flow: column nowrap;
  justify-content: center;
  padding: 0 0.3rem 0 0.25rem;

  .chart-content{
    position: relative;
    width: 100%;
    display: flex;
    justify-content: start;
    align-items: center;
    flex-flow: row nowrap;

    .circle{
      position: absolute;
      content: '';
      top: var(--circleTopOffset);
     /* width:1.3rem;// 104/80
      height:1.3rem;*/

      z-index: 9999;
    }
    .circle-left{
      left:0
    }
    .circle-right{
     right:0
    }
    .rect{
      position: absolute;
      content: '';
      //top: 22%;
      top:  var(--rectTop);
      width: calc(100% - var(--rectOffsetSize));
      //height: 56%;
      height: var(--rectHeight);

      .data-wrapper{
        width: calc(100% - var(--rectOffsetSize));
        height: 100%;
        //background: green;

        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-flow: row nowrap;

        .data-one,.data-two{
          flex: 1;
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          flex-flow: column nowrap;
          overflow: hidden;

          .count{
            width: 100%;
            padding:0 0.2rem;
            color:#fff;
            font-family: DIN-Medium;
            font-size: 0.2375rem;//19/80
            height: 0.25rem;
            line-height: 0.25rem;
            text-align: center;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
          .desc{
            width: 100%;
            font-size:0.1625rem;// 13/80
            font-weight: 600;
            text-align: center;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
          .desc-one{
            color: #6DD1FF;
          }
          .desc-two{
            color: #4CF5FF;
          }
        }
        .data-one{
          position: relative;
        }
        .data-one:before{
          position: absolute;
          content: "";
          width: 1px;
          right: 0;
          top:0;
          height: 100%;
          background-image: linear-gradient(to bottom, transparent 0,transparent 15%, rgba(98, 162, 205, 0.59) 50% ,transparent 85%,transparent 100%);
        }
      }
    }

    .rect-right{
     /* left:0.65rem;// 52/80
      border-radius: 0 0.375rem 0.375rem 0;//30/80
      background: radial-gradient(circle at left,
      transparent 0,transparent 0.62rem , rgba(81, 188, 255, 0.02) 0.62rem, rgba(81, 188, 255, 0.4) 100% );*/

      border-radius: 0 calc(var(--rectHeight) / 2) calc(var(--rectHeight) / 2) 0;//30/80
      left:var(--rectOffsetSize);
      background: radial-gradient(circle at left,
      transparent 0,transparent var(--gradientPosition) , rgba(81, 188, 255, 0.02) var(--gradientPosition), rgba(81, 188, 255, 0.4) 100% );

      .data-wrapper{
        //margin-left: 0.65rem;
        margin-left: var(--rectOffsetSize);
      }
    }
    .rect-left{
      /*right:0.65rem;// 52/80
     background: radial-gradient(circle at right,
     transparent 0,transparent 0.62rem , rgba(41, 255, 255, 0.02) 0.62rem, rgba(41, 255, 255, 0.4) 100% );
     border-radius: 0.375rem 0 0 0.375rem;//30/80*/

      border-radius: calc(var(--rectHeight) / 2) 0 0 calc(var(--rectHeight) / 2);//30/80
      right:var(--rectOffsetSize);
      background: radial-gradient(circle at right,
      transparent 0,transparent var(--gradientPosition) , rgba(41, 255, 255, 0.02) var(--gradientPosition), rgba(41, 255, 255, 0.4) 100% );

      .data-wrapper{
       //margin-right: 0.65rem;
        margin-right: var(--rectOffsetSize);
      }
    }
  }
}
</style>