<template>
  <a-row :gutter='10' style='height: 100%;' class='vScroll'>
    <a-col style='width:100%;height: 100%;display: flex;flex-direction: column'>
      <!-- 查询区域 -->
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0', marginRight: '12px' }" class='card-style'
              style='width: 100%'>
        <div class='table-page-search-wrapper'>
          <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
            <a-row :gutter='24' ref='row'>
              <a-col :span='spanValue'>
                <a-form-item label='设备名称'>
                  <a-input :maxLength='maxLength' placeholder='请输入设备名称' v-model='queryParam.deviceName' :allowClear='true'
                           autocomplete='off' />
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label='预约时间'>
                  <a-range-picker
                    :placeholder="['开始时间', '结束时间']"
                    format='yyyy-MM-dd'
                    v-model='queryParam.appointmentTime'
                    style='width: 100%'
                  ></a-range-picker>
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label='预约状态'>
                  <a-select placeholder="请选择预约状态" :getPopupContainer="(node) => node.parentNode" :allowClear="true"
                            v-model="queryParam.status">
                    <a-select-option v-for="item in statusList" :key="item.value" :value="item.value">{{
                        item.title
                      }}</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span='spanValue' v-show="toggleSearchStatus">
                <a-form-item label='支撑项目'>
                  <a-select  style="width: 100%" v-model='queryParam.supportingProject' placeholder='请选择支撑项目' :options="projects"></a-select>
                </a-form-item>
              </a-col>
              <a-col :span='colBtnsSpan()'>
                <span class='table-page-search-submitButtons'
                      :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button type='primary' class='btn-search btn-search-style' @click='searchQuery'>查询</a-button>
                  <a-button class='btn-reset btn-reset-style' @click='searchReset'>重置</a-button>
                  <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <div class="table-operator">
          <a-button @click="handleAdd">预约</a-button>
        </div>
        <a-table
          ref='table'
          bordered
          :row-key='(record,index)=>{return record.id}' :columns='columns'
          :dataSource='dataSource'
          :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
          :pagination='ipagination'
          :loading='loading'
          @change='handleTableChange'>
          <template slot='tooltip' slot-scope='text'>
            <a-tooltip placement='topLeft' :title='text' trigger='hover'>
              <div class='tooltip'>
                {{ text }}
              </div>
            </a-tooltip>
          </template>
          <template slot='isPrivate' slot-scope='text'>
            <a-icon :type='text===visibility[1].value?"lock":"global"' :style='{color:text===visibility[1].value?"#FE9400":"#4BD863"}'></a-icon>
            {{ text===visibility[1].value?visibility[1].label:visibility[0].label }}
          </template>
          <template slot='status' slot-scope='text,record'>
           <span style="font-size: 12px;padding:2px 4px;border-radius:3px;color:#fff" :style="{backgroundColor:record.status===1?'#c1bfbf':'#4BD863'}">{{text}}</span>
          </template>
          <template slot='knowledgeType' slot-scope='text'>
            <knowledge-icon :knowledgeType="text"></knowledge-icon>
          </template>
          <span slot='action' class='caozuo' slot-scope='text, record'>
            <a @click='handleDetailPage(record)'>查看</a>
            <span v-if="record.status==0">
               <a-divider type='vertical'/>
               <a @click='handleDelay(record)' class='overlay'>延期</a>
            </span>
          </span>
        </a-table>
      </a-card>
    </a-col>
<!--    <knowledge-approval-modal ref='modalForm' @ok='modalFormOk'></knowledge-approval-modal>-->
    <per-modal ref='modalForm' @ok='modalFormOk'></per-modal>
    <PerDevicesModal ref='perdevicesmodal'></PerDevicesModal>
  </a-row>
</template>

<script>
import '@assets/less/TableExpand.less'
import {JeecgListMixin} from '@/mixins/JeecgListMixin'
import {YqFormSearchLocation} from '@/mixins/YqFormSearchLocation'
import {visibility} from '@views/opmg/knowledgeManagement/knowledgeBase/modules/dataListAndFunc'
import knowledgeIcon from '@views/opmg/knowledgeManagement/knowledgeBase/modules/KnowledgeIcon.vue'
import {listData,statusData} from '@views/laboratoryManagement/personalAppointment/modules/perAppointmentData.js'
import PerModal from '../modules/PerModal.vue'
import PerDevicesModal from '../modules/PerDevicesModal.vue'
import {projects} from '../modules/projects'
export default {
  name: 'KnowledgeApprovalList',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components: {
    knowledgeIcon,
    PerModal,
    PerDevicesModal
  },
  data() {
    return {
      maxLength:50,
      description: '个人预约中心列表页面',
      disableMixinCreated:true,
      formItemLayout: {
        labelCol: {
          style: 'width:70px'
        },
        wrapperCol: {
          style: 'width:calc(100% - 70px)'
        }
      },
      statusList:statusData,
      // 表头
      columns: [
        {
          title: '设备名称',
          dataIndex: 'deviceName',
          /*  customCell: () => {
            let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'tooltip'
          }*/
        },
        {
          title: '预约开始时间',
          dataIndex: 'startTime'
        },
        {
          title: '预约结束时间',
          dataIndex: 'endTime'
        },
        {
          title: '使用时长',
          dataIndex: 'duration'
        },
        {
          title: '预约状态',
          dataIndex: 'statusText',
          scopedSlots: {
            customRender: 'status'
          }
        },
        {
          title: '支撑项目',
          dataIndex: 'supportingProject',
          customRender: (text) => {
            return this.projects.find((item) => item.value === text).label
          }
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 150,
          scopedSlots: {
            customRender: 'action'
          }
        }
      ],
      url: {
        //list: '/kbase/knowledges/list/review'
      },
      projects,
    }
  },
  mounted() {
    this.dataSource=listData
  },
  methods: {
    handleAdd() {
      this.$refs.perdevicesmodal.show();
    },
    handleDelay(record){
     this.$refs.modalForm.edit(record);
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
.overlay {
  color: #409eff
}
</style>