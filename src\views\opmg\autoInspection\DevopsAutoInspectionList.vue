<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll zxw">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <!-- 查询区域 -->
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <div class="table-page-search-wrapper-style">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="任务名称">
                  <a-input
                    placeholder="请输入任务名称"
                    :allowClear="true"
                    autocomplete="off"
                    v-model="queryParam.taskName" :maxLength="50"
                  ></a-input>
                </a-form-item>
              </a-col>

              <a-col :span="colBtnsSpan()">
                <span
                  class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                >
                  <a-button type="primary" @click="searchQuery" class="btn-search-style">查询</a-button>
                  <a-button @click="searchReset" style="margin-left: 10px" class="btn-reset-style">重置</a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>

      <a-card :bordered="false" style="flex: auto" class="core">
        <a-row class="lastBtn2">
          <div class="table-operator">
            <a-button @click="handleAdd">新增</a-button>
            <a-button @click="handleExportXls('智能巡检任务表')">导出</a-button>
            <a-dropdown v-if="selectedRowKeys.length > 0">
              <a-menu slot="overlay" style='text-align: center'>
                <a-menu-item key="1" @click="batchDel">删除</a-menu-item>
              </a-menu>
              <a-button> 批量操作 <a-icon type="down" /></a-button>
            </a-dropdown>
          </div>
        </a-row>
        <!-- table区域-begin -->
        <a-table
          ref="table"
          bordered
          rowKey="id"
          :columns="columns"
          :dataSource="dataSource"
          :scroll='dataSource.length>0?{x:"max-content"}:{}'
          :pagination="ipagination"
          :loading="loading"
          :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          @change="handleTableChange"
        >
          <template slot="htmlSlot" slot-scope="text">
            <div v-html="text"></div>
          </template>
          <template slot="imgSlot" slot-scope="text">
            <span v-if="!text" style="font-size: 14px">无图片</span>
            <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width: 80px; font-size: 14px" />
          </template>
          <template slot="fileSlot" slot-scope="text">
            <span v-if="!text" style="font-size: 14px">无文件</span>
            <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
              下载
            </a-button>
          </template>

          <span slot="action" slot-scope="text, record">
          <a @click="resumeJob(record)" v-if="record.taskStatus==='-1'">启动</a>
          <a @click="pauseJob(record)" v-if="record.taskStatus==='0'">停止</a>

          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item><a @click="executeImmediately(record)">执行一次</a></a-menu-item>
              <a-menu-item><a @click="handleEdit(record)">编辑</a></a-menu-item>
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>

          <template slot="tooltip" slot-scope="text">
            <a-tooltip placement="topLeft" :title="text" trigger="hover">
              <div class="tooltip">
                {{ text }}
              </div>
            </a-tooltip>
          </template>
          <!-- 状态渲染模板 -->
          <template slot="customRenderStatus" slot-scope="taskStatus">
            <a-tag v-if="taskStatus==='0'" color="green">已启动</a-tag>
            <a-tag v-if="taskStatus==='-1'" color="orange">已暂停</a-tag>
          </template>
        </a-table>
      </a-card>
      <devops-auto-inspection-modal ref="modalForm" @ok="modalFormOk"></devops-auto-inspection-modal>
    </a-col>
  </a-row>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import DevopsAutoInspectionModal from './modules/DevopsAutoInspectionModal'
import { filterMultiDictText } from '@/components/dict/JDictSelectUtil'
import JSuperQuery from '@/components/jeecg/JSuperQuery.vue'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
import { getAction } from '@/api/manage'
export default {
  name: 'DevopsAutoInspectionList',
  mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
  components: {
    DevopsAutoInspectionModal,
    JSuperQuery,
  },
  data() {
    return {
      description: '智能巡检任务表管理页面',
      // 表头
      columns: [
        {
          title: '任务名称',
          dataIndex: 'taskName',
          customCell: () => {
            let cellStyle = 'text-align: center'
            return { style: cellStyle }
          },
        },
        /*{
          title: '任务执行类型',
          dataIndex: 'taskexecuteTypeText',
          customCell: () => {
            let cellStyle = 'text-align: center'
            return { style: cellStyle }
          },
        },*/
        {
          title: '任务添加时间',
          dataIndex: 'createTime',
          customCell: () => {
            let cellStyle = 'text-align: center'
            return { style: cellStyle }
          },
        },
        // {
        //   title: '是否推送',
        //   dataIndex: 'pushType',
        //   customRender: (text) => (text ? filterMultiDictText(this.dictOptions['pushType'], text) : ''),
        //   customCell: () => {
        //     let cellStyle = 'text-align: center'
        //     return { style: cellStyle }
        //   },
        // },
        // {
        //   title: '推送地址',
        //   dataIndex: 'pushAddress',
        //   customCell: () => {
        //     let cellStyle = 'text-align: center'
        //     return { style: cellStyle }
        //   },
        // },
        {
          title: '任务状态',
          align:"center",
          dataIndex: 'taskStatus',
          scopedSlots: { customRender: 'customRenderStatus' },
          /*filterMultiple: false,
          filters: [
            { text: '已启动', value: '0' },
            { text: '已暂停', value: '-1' },
          ]*/
        },
        {
          title: '执行次数',
          dataIndex: 'executeCounts',
          customCell: () => {
            let cellStyle = 'text-align: right'
            return { style: cellStyle }
          },
        },
        {
          title: '创建人',
          dataIndex: 'createByName',
          customCell: () => {
            let cellStyle = 'text-align: center'
            return { style: cellStyle }
          },
        },
        {
          title: '操作',
          dataIndex: 'action',
          fixed: 'right',
          align: 'center',
          width: 147,
          scopedSlots: { customRender: 'action' },
        },
      ],
      url: {
        list: '/autoInspection/devopsAutoInspection/list',
        delete: '/autoInspection/devopsAutoInspection/delete',
        deleteBatch: '/autoInspection/devopsAutoInspection/deleteBatch',
        exportXlsUrl: '/autoInspection/devopsAutoInspection/exportXls',
        importExcelUrl: 'autoInspection/devopsAutoInspection/importExcel',
        pause: "/autoInspection/devopsAutoInspection/pause",
        resume: "/autoInspection/devopsAutoInspection/resume",
        execute: "/autoInspection/devopsAutoInspection/execute"
      },
      dictOptions: {},
    }
  },
  created() {
    // this.$set(this.dictOptions, 'pushType', [
    //   { text: '是', value: 'Y' },
    //   { text: '否', value: 'N' },
    // ])
  },
  mounted() {},
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
  },
  methods: {
    pauseJob: function(record){
      var that = this;
      //暂停定时任务
      this.$confirm({
        title:"确认暂停",
        okText: '是',
        cancelText: '否',
        content:"是否暂停选中任务?",
        onOk: function(){
          getAction(that.url.pause,{id:record.id}).then((res)=>{
            if(res.success){
              that.$message.success(res.message);
              that.loadData();
              that.onClearSelected();
            }else{
              that.$message.warning(res.message);
            }
          });
        }
      });

    },
    resumeJob: function(record){
      var that = this;
      //恢复定时任务
      this.$confirm({
        title:"确认启动",
        okText: '是',
        cancelText: '否',
        content:"是否启动选中任务?",
        onOk: function(){
          getAction(that.url.resume,{id:record.id}).then((res)=>{
            if(res.success){
              that.$message.success(res.message);
              that.loadData();
              that.onClearSelected();
            }else{
              that.$message.warning(res.message);
            }
          });
        }
      });
    },
    executeImmediately(record){
      var that = this;
      //立即执行定时任务
      this.$confirm({
        title:"确认提示",
        okText: '是',
        cancelText: '否',
        content:"是否立即执行任务?",
        onOk: function(){
          getAction(that.url.execute,{id:record.id}).then((res)=>{
            if(res.success){
              that.$message.success(res.message);
              that.loadData();
              that.onClearSelected();
            }else{
              that.$message.warning(res.message);
            }
          });
        }
      });
    }
  },
}
</script>
<style lang="less" scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
</style>
