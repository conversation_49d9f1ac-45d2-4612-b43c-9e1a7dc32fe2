<template>
  <div>
    <div class="refresh" @click="refresh">
      <a-icon type="redo" style="font-size: 0.25; color: rgba(255, 255, 255, 0.5)"></a-icon>
    </div>
    <div slot="bodySlot" class="empty-wrapper" v-if="chartData.length === 0">
      <a-spin :spinning="loading" v-if="loading" class="spin"></a-spin>
      <a-list :data-source="[]" v-else />
    </div>
    <div v-else class="chart-wrapper">
      <div slot="bodySlot" style="height: 100%; width: 60%" ref="pieChart" class="chart"></div>
      <div class="legend-pagination">
        <a-carousel class="legend-carousel">
          <div v-for="(page, pageIndex) in rotatingData" :key="pageIndex" class="legend-wrapper">
            <div
              v-for="(item, itemIndex) in page"
              :key="pageIndex + '_' + itemIndex"
              class="legend-item"
              @click="handleLegendClick(item)"
            >
              <div class="legend-item-content">
                <div class="color-indicator" :style="{ background: colors[itemIndex % colors.length] }"></div>
                <span class="legend-name">{{ item.name }}</span>
              </div>
              <div class="legend-value">
                {{ item.total > 0 && item.on > 0 ? ((item.on / item.total) * 100).toFixed(1) + '%' : '0%' }}
              </div>
            </div>
            <div class="legend-item"></div>
            <div class="legend-item"></div>
            <div class="legend-item"></div>
            <div class="legend-item"></div>
          </div>
        </a-carousel>
      </div>
    </div>
  </div>
</template>

<script>
import { getAction } from '@/api/manage'
export default {
  data() {
    return {
      loading: false,
      chartData: [],
      time1: '',
      time2: '',
      url: {
        showCountByCategory: '/openAPI/showCountByCategory',
      },
      currentPage: 1,
      legendPageSize: 6, // 每页显示6个图例
      colors: ['#0080FF', '#1da6f4', '#74CaFF', '#45feff', '#35ffce', '#04ffa1', '#F7E520', '#be71b5', '#d0405b'],
    }
  },
  created() {
    this.showCountByCategory()
  },
  computed: {
    // 计算轮播的图例数据
    rotatingData() {
      const pageSize = 6
      const pages = []
      this.chartData.forEach((item, index) => {
        const pageIndex = Math.floor(index / pageSize)
        if (!pages[pageIndex]) pages[pageIndex] = []
        pages[pageIndex].push(item)
      })
      return pages
    },
  },
  methods: {
    // 获取所有分类
    showCountByCategory() {
      this.loading = true
      getAction(this.url.showCountByCategory)
        .then((res) => {
          if (res.code == 200) {
            if (res.result && res.result.length > 0) {
              let count = 0
              res.result.map((item) => {
                count += item.total
              })
              if (count > 0) {
                this.chartData = res.result
                this.$nextTick(() => {
                  this.pieChart(this.chartData)
                })
              }
            }
          }
          this.loading = false
        })
        .catch((err) => {
          this.$message.warning(err.message)
          this.loading = false
        })
    },
    // 刷新数据
    refresh() {
      this.pieChart(this.chartData)
      // this.showCountByCategory()
    },
    // 点击分类
    handleLegendClick(item) {
      if (item) {
        const filtered = this.chartData.filter((d) => d.name === item.name)
        this.pieChart(filtered)
      } else {
        this.pieChart(this.chartData)
      }
    },
    pieChart(data) {
      let data1 = [] // series数据
      let totalOn = 0 // 总在线数
      let totalOut = 0 // 总离线数
      let totalAll = 0 // 总设备数
      data.forEach((item) => {
        totalOn += item.on
        totalOut += item.out
        totalAll += item.total
      })
      data1 = [
        { name: '在线率', value: totalOn },
        { name: '离线率', value: totalOut },
      ]
      const color = ['#00ffff', '#fbce60']
      let myChart = this.$echarts.init(this.$refs.pieChart)
      myChart.setOption({
        tooltip: {
          show: true,
          formatter: function (params) {
            if (params.seriesIndex === 0) {
              if (params.name) {
                return `${params.name}<br/>数量: ${params.value}<br/>占比: ${params.percent}%`
              }
            } else {
              if (params.name) {
                return `${params.name}<br/>数量: ${params.value}<br/>`
              }
            }
          },
          transitionDuration: 0, //echart防止tooltip的抖动
        },
        label: {
          show: false,
        },
        title: {
          text: totalAll,
          left: '48%',
          top: '39%',
          itemGap: 10,
          textAlign: 'center',
          textStyle: {
            color: '#fff',
            fontSize: 24,
            fontWeight: 600,
          },
          subtext: '总计',
          subtextStyle: {
            color: '#9FA5AD',
            fontSize: 14,
            fontWeight: 500,
          },
        },
        series: [
          {
            type: 'pie',
            radius: ['55%', '60%'],
            center: ['50%', '50%'],
            hoverAnimation: false, // 取消掉饼图鼠标移上去时自动放大
            animation: false,
            minAngle: 5,
            label: {
              show: true,
              formatter: '{d}%',
            },
            itemStyle: {
              normal: {
                color: function (params) {
                  return color[params.dataIndex]
                },
              },
            },
            data: data1,
            z: 1,
          },
        ],
      })
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    },
  },
}
</script>
<style lang="less" scoped>
.refresh {
  position: absolute;
  right: 0.5rem; // 40/80px
  top: 0.25rem; // 20/80px
  z-index: 9;
  cursor: pointer;
}
.chart-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
}
.chart {
  width: 60%;
  height: 100%;
}
.legend-pagination {
  width: 40%;
  height: 100%;
  display: flex;
  justify-content: center;
  margin-top: 0.15rem; // 12/80px
  color: #fff;
}

.pagination-dot {
  width: 0.5rem; // 8/80px
  height: 0.5rem; // 8/80px
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  margin: 0 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.pagination-dot.active {
  background: #fff;
  transform: scale(1.2);
}
.legend-carousel {
  width: 100%;
  height: 100%;
  color: #fff;
  .legend-wrapper {
    width: 100%;
    // height: 100%;
    display: flex !important;
    justify-content: center;
    flex-wrap: wrap;
    .legend-item {
      width: 50%;
      height: auto;
      cursor: pointer;
      .legend-item-content {
        display: flex;
        align-items: center;
        .color-indicator {
          width: 0.125rem; // 10/80px
          height: 0.125rem; // 10/80px
          margin-right: 0.1rem; // 8/80px
          background: #007df9;
          margin-right: 0.1rem; // 8/80px
        }
        .legend-name {
          font-size: 0.175rem; // 14/80px
          font-weight: 500;
          width: calc(100% - 0.225rem); // 18/80px
          text-align: left;
          white-space: nowrap;
          // 单行超出省略号
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }

      .legend-value {
        font-size: 0.175rem; // 14/80px
        font-weight: 500;
        margin-left: 0.225rem; // 18/80px
        margin-bottom: 0.0875rem; // 7/80px
        text-align: left;
      }
    }
  }
}
::v-deep .ant-carousel .slick-slider {
  text-align: center;
  height: 100%;
  overflow: hidden;
  .slick-list {
    height: 100%;

    .slick-track {
      height: 100%;

      .slick-slide {
        height: 100%;
      }
      .slick-slide > div {
        height: 100%;
        display: flex;
        align-items: center;
      }
    }
  }
}
::v-deep .ant-carousel .slick-dots li button {
  height: 4px;
}
</style>
