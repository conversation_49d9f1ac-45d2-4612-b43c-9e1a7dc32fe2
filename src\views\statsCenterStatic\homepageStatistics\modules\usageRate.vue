<template>
  <card-frame :title='"终端资源占用排行TOP5"' :sub-title='""'>
    <div slot='bodySlot' class='empty-wrapper' v-if='chartData.length===0'>
      <a-spin :spinning='loading' v-if='loading' class='spin'></a-spin>
      <a-list :data-source='[]' v-else />
    </div>
    <div slot='bodySlot' id='home-usage-rate' v-else>
      <a-carousel arrows style='height: 100%' :autoplay='true' :dotPosition='"top"'>
        <a slot="customPaging" slot-scope="props">
          <span>{{chartData[props.i].title}}</span>
        </a>
<!--        <div slot="prevArrow"  slot-scope="props" class="custom-slick-arrow" style="left: 10px;zIndex: 1">
          <a-icon type="left-circle" />
        </div>
        <div slot="nextArrow" slot-scope="props" class="custom-slick-arrow" style="right: 10px">
          <a-icon type="right-circle" />
        </div>-->
        <horizontal-bar-chart
          class='bar-chart'
          v-for='(item,index) in chartData'
          :key='"bar_chart_"+index'
          :chart-data='item.data'
          :tip-description='item.description'
          :max='100'
          :unit='"%"'>
        </horizontal-bar-chart>
      </a-carousel>
    </div>
  </card-frame>
</template>
<script>

import { getAction } from '@api/manage'
import cardFrame from '@views/statsCenter/com/cardFrame.vue'
import horizontalBarChart from '@views/statsCenter/com/horizontalBarChart.vue'
export default {
  name: "deviceAlarm",
  props: {
    adcode: {
      type: String,
      required: false,
      default: ''
    },
    handleRefresh: {
      type: Number,
      required: false,
      default: 0,
    }
  },
  components: { cardFrame, horizontalBarChart },
  data() {
    return {
      loading: false,
      defaultActiveKey: '1',
      chartData: [],
      timer: null,
      url: {
        rate: '/data-analysis/index/getResourceOccupyTop5',
      }
    }
  },
  watch: {
    adcode: {
      handler(nVal, oVal) {
        //this.getUsageRate(nVal)
        this.getMockJson()
      },
      immediate: true,
      deep: true
    },
    handleRefresh: {
      handler(nVal, oVal) {
        //this.getUsageRate(this.adcode)
        this.getMockJson()
      }
    }
  },
  methods: {
    callback(key) {
      let that = this
      that.defaultActiveKey = key
    },
    getUsageRate(adcode) {
      this.loading = true
      this.chartData = []
      let code = adcode ? adcode : null
      getAction(this.url.rate, { cityId: code }).then((res) => {
        this.chartData = []
        if (res.success) {
          if (res.result.cpuRate && res.result.cpuRate.length > 0) {
            let obj = {
              title: 'CPU',
              description: 'CPU使用率',
              data: res.result.cpuRate.reverse()
            }
            this.chartData.push(obj)
          }
          if (res.result.memUtilizRate && res.result.memUtilizRate.length > 0) {
            let obj = {
              title: 'RAM',
              description: 'RAM使用率',
              data: res.result.memUtilizRate.reverse()
            }
            this.chartData.push(obj)
          }
        } else {
          this.$message.warning(res.message)
        }
        this.loading = false
      }).catch((err) => {
        this.$message.warning(err.message)
        this.loading = false
      })
    },
    getMockJson(){
      this.loading = true
      this.chartData = []
      getAction(location.origin+"/statsCenter/mock/homeData.json").then((res) => {
        if(res){
          this.chartData = []
          let newData=res.usageRateData
          if (newData.cpuRate && newData.cpuRate.length > 0) {
            let obj = {
              title: 'CPU',
              description: 'CPU使用率',
              data: newData.cpuRate.reverse()
            }
            this.chartData.push(obj)
          }
          if (newData.memUtilizRate && newData.memUtilizRate.length > 0) {
            let obj = {
              title: 'RAM',
              description: 'RAM使用率',
              data: newData.memUtilizRate.reverse()
            }
            this.chartData.push(obj)
          }
          this.loading = false
        }
        this.loading=false
      }).catch((err)=>{
        this.loading=false
      })
    }
  }
}
</script>

<style scoped lang="less">
#home-usage-rate{
  height: 100%;

  .tabs-wrapper{
   height: 100% !important;

   ::v-deep .ant-tabs-bar{
      display: none !important;
    }
    ::v-deep .ant-tabs-content{
      height: 100% !important;
    }
  }
}

::v-deep .ant-carousel .slick-slider {
  text-align: center;
  height: 100%;
  line-height: 100%;
  background: rgba(54, 77, 121, 0);
  overflow: hidden;

  .slick-list{
    height:100%;

    .slick-track{
      height: 100%;

      .slick-slide{
        height: 100%;
      }
      .slick-slide>div{
        height: 100%;

        .bar-chart{
          height: 100%;
          width: 100%;
          padding-top: 0.1rem;

          & :nth-child(2){
            text-align: left;
          }
        }
      }
    }
  }
}
::v-deep .ant-carousel .slick-dots {
  height: 0.2rem;
  line-height: 0.2rem;
  top: 0.1rem;
  text-align: right;
  padding-right: 0.175rem ;//14/80

  li {
    span {
      font-size: 0.175rem ;//14/80
      padding: 0 0.1rem;
      color: #9FA5AD
    }
  }

  .slick-active {
    span {
      color: #03FFFF
    }
  }
}

::v-deep .ant-carousel .custom-slick-arrow {
  width:0.3125rem;// 25/80
  height: 0.3125rem;
  font-size: 0.3125rem;
  color: #fff;
  background-color: rgba(31, 45, 61, 0.11);
  opacity: 0.3;
}
::v-deep .ant-carousel .custom-slick-arrow:before {
  display: none;
}
::v-deep .ant-carousel .custom-slick-arrow:hover {
  opacity: 0.5;
}

::v-deep .ant-carousel .slick-slide h3 {
  color: #fff;
}



</style>