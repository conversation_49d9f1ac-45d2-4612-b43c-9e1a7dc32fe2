<template>
  <a-row :gutter='10' style='height: 100%' class='vScroll'>
    <a-col style='width: 100%; height: 100%; display: flex; flex-direction: column'>
      <!-- 查询区域 -->
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0'}" class='card-style'>
        <div class='table-page-search-wrapper'>
          <a-form layout='inline' v-bind='formItemLayout' @keyup.enter.native='searchQuery'>
            <a-row ref='row' :gutter='24'>
              <a-col :span="spanValue">
                <a-form-item label="产品分类">
                  <a-tree-select allowClear :getPopupContainer="(node) => node.parentNode"
                                 :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }"
                                 v-model="queryParam.categoryId" placeholder="请选择产品分类"
                                 :tree-data="assetsCategoryTree" />
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label='设备名称'>
                  <a-input :maxLength='maxLength' v-model='queryParam.name' :allowClear='true' autocomplete='off'
                           placeholder='请输入设备名称' />
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label='IP地址'>
                  <a-input :maxLength='maxLength' v-model='queryParam.ip' :allowClear='true' autocomplete='off'
                           placeholder='请输入IP地址' />
                </a-form-item>
              </a-col>
              <a-col :span='colBtnsSpan()'>
                    <span :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                          class='table-page-search-submitButtons'>
                      <a-button class='btn-search btn-search-style' type='primary' @click='dosearch'>查询</a-button>
                      <a-button class='btn-reset btn-reset-style' @click='searchReset'>重置</a-button>
                      <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                        {{ toggleSearchStatus ? '收起' : '展开' }}
                        <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                      </a>
                    </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <div class='table-operator table-operator-style'>
          <a-button @click='handleAdd'>新增</a-button>
          <a-button @click="handleExportXls('设备上报')">导出</a-button>
          <a-button @click="handleTemplateXls">下载模版</a-button>
          <a-upload
            ref="importUploader"
            name="file"
            :accept="accept"
            :showUploadList="false"
            :multiple="false"
            :headers="tokenHeader"
            :action="importExcelUrl"
            @change="changeImportExcel"
          >
            <a-button @click.stop="openDepart">导入</a-button>
          </a-upload>
          <a-dropdown v-if='selectedRowKeys.length > 0'>
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item key='1' @click='batchDel'>删除</a-menu-item>
            </a-menu>
            <a-button> 批量操作
              <a-icon type='down' />
            </a-button>
          </a-dropdown>

        </div>
        <a-table
          ref='table'
          class="terminal-table"
          :columns='columns'
          :dataSource='dataSource'
          :loading='loading'
          :pagination='ipagination'
          :row-key='(record, index) => {return record.id}'
          :rowSelection='{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }'
          :scroll="dataSource.length > 0 ? { x: true } : {}"
          bordered
          @change='handleTableChange'>
          <template slot='enable' slot-scope='text, record'>
            <span>{{ text == 1 ? '已启用' : '未启用' }}</span>
          </template>

          <span slot='action' slot-scope='text, record' class='caozuo'>
            <a @click='handleDetailPage(record)'>查看</a>
            <a-divider type='vertical' />
            <a-dropdown>
              <a class='ant-dropdown-link'>更多<a-icon type='down' /></a>
              <a-menu slot='overlay'>
                <a-menu-item>
                  <a class='overlay' @click='handleEdit(record)'>编辑</a>
                </a-menu-item>
                <a-menu-item>
                  <a class='overlay' @click='handleDelete(record.id)'>删除</a>
                </a-menu-item>
              </a-menu>
            </a-dropdown>
          </span>
          <template slot='tooltip' slot-scope='text'>
            <a-tooltip :title='text' placement='topLeft' trigger='hover'>
              <div class='tooltip'>
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
      </a-card>
    </a-col>
    <device-reporting-modal ref='modalForm' @ok='modalFormOk'></device-reporting-modal>
    <j-select-depart
      ref="departSelectModal"
      v-model="importDeptId"
      :showInput="false"
      :modal-width="500"
      :multi="false"
      :rootOpened="false"
      :tree-action='false'
      @change="selectedDepartId"
    >
    </j-select-depart>
    <!-- 下载模版 -->
    <iframe id="download" style="display: none" ></iframe>
  </a-row>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import deviceReportingModal from './modules/deviceReportingModal.vue'
import JSelectDepartModal from '@comp/jeecgbiz/modal/JSelectDepartModal.vue'
import jSelectDepart from '@comp/jeecgbiz/JSelectDepart.vue'
import {
  getAction,
  postAction,
  deleteAction
} from '@/api/manage'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
import Vue from 'vue'
import { checkAccept, compareFileSizes } from '@comp/yq/yqUpload/YqUploadCommonFuns'
import { ACCESS_TOKEN } from '@/store/mutation-types'
import { Modal } from 'ant-design-vue'
import store from '@/store'

export default {
  name: 'deviceReportingList',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components: {
    deviceReportingModal,
    JSelectDepartModal,
    jSelectDepart
  },
  data() {
    return {
      excelfileList: [],
      accept: '.xlsx',
      description: '设备导入管理页面',
      maxLength: 50,
      assetsCategoryTree: [],
      importDeptId: '',
      formItemLayout: {
        labelCol: { style: 'width:80px' },
        wrapperCol: {
          style: 'width:calc(100% - 80px)'
        }
      },
      columns: [
        {
          title: '设备名称',
          dataIndex: 'name'
        },
        {
          title: '设备唯一标识',
          dataIndex: 'deviceCode'
        },
        {
          title: 'IP地址',
          dataIndex: 'ip'
        },
        {
          title: '产品分类',
          dataIndex: 'categoryId_dictText'
        },
        {
          title: '所属单位',
          dataIndex: 'deptId_dictText'
        },
        {
          title: '所在位置',
          dataIndex: 'location'
        },
        {
          title: '使用人',
          dataIndex: 'username'
        },
        {
          title: '是否启用',
          dataIndex: 'enable',
          scopedSlots: {
            customRender: 'enable'
          },
        },
        {
          title: '添加时间',
          dataIndex: 'createTime'
        },
        {
          title: '描述',
          dataIndex: 'description',
          scopedSlots: {
            customRender: 'tooltip'
          },
          customCell: () => {
            let cellStyle = 'text-align: left;max-width:400px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '操作',
          dataIndex: 'action',
          fixed: 'right',
          width: 140,
          scopedSlots: {
            customRender: 'action'
          }
        }
      ],
      url: {
        list: '/device/brigadeInfo/list',
        delete: '/device/brigadeInfo/delete',
        deleteBatch: '/device/brigadeInfo/deleteBatch',
        exportXlsUrl: '/device/brigadeInfo/exportXls',
        importExcelUrl: 'device/brigadeInfo/importExcel',
        downloadTemplateXlsUrl: '/device/brigadeInfo/downloadTemplate',
        productTypeUrl: '/assetscategory/assetsCategory/selectAssetsCategoryTree'//获取产品分类下拉数据
      },
      disableMixinCreated: true,
    }
  },
  computed: {
    importExcelUrl: function() {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}?deptId=${this.importDeptId}`
    },
    downloadTemplateXlsUrl: function() {
      return `${window._CONFIG['domianURL']}/${this.url.downloadTemplateXlsUrl}`
    },
  },
  created() {
    this.getSelectTree()
  },
  activated() {
    this.loadData()
  },
  methods: {
    openDepart() {
      // 先打开部门选择窗口
      this.$refs.departSelectModal.openModal()
      return false; // 阻止默认上传行为
    },
    selectedDepartId(value) {
      // this.importDeptId = value
      // 手动触发文件选择对话框
      this.$nextTick(() => {
        this.$refs.importUploader.$el.querySelector('input[type="file"]').click();
      });
    },
    changeImportExcel(info) {
      this.importDeptId = ''
      this.handleImportExcel(info)
    },
    //excel模板
    handleTemplateXls() {
      const path = this.downloadTemplateXlsUrl
      document.getElementById('download').src = path
    },
    /*获取产品分类下拉数据*/
    getSelectTree() {
      getAction(this.url.productTypeUrl).then((res) => {
        this.assetsCategoryTree = res.result
      })
    },
    //删除
    deleteRecord(record) {
      if (!this.url.deleteBatch) {
        this.$message.error('请设置url.delete属性!')
        return
      }
      var that = this
      this.$confirm({
        title: '确认删除',
        okText: '是',
        cancelText: '否',
        content: '是否删除选中数据?',
        onOk: function() {
          that.loading = true
          deleteAction(that.url.deleteBatch, {
            ids: record.id
          }).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.loadData()
            } else {
              that.$message.warning(res.message)
              that.loadData()
            }
          })
        }
      })
    },
    //表单查询,点击查询按钮，默认查询第一页
    dosearch() {
      this.loadData(1)
    },
    searchReset() {
      this.queryParam = {}
      this.loadData(1)
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';

.overlay {
  color: #409eff
}

</style>