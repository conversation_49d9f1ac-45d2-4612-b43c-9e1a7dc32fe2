<template>
  <div style="height:100%">
    <keep-alive exclude='softwareTaskDetails'>
      <component :is="pageName" style="height:100%" :task-info="data" :show-back="true"/>
    </keep-alive>
  </div>
</template>
<script>
  import softwareTaskList from './taskList'
  import softwareTaskDetails from './modules/taskDetails'
  import taskTerminalList from '@views/opmg/taskManagement/modules/terminalList.vue'
  export default {
    name: "softwareTaskManage",
    data() {
      return {
        isActive: 0,
        data:{}
      }
    },
    components: {
      softwareTaskList,
      softwareTaskDetails,
      taskTerminalList,
    },
    created(){
      this.pButton1(0);
    },
    //使用计算属性
    computed: {
      pageName() {
        switch (this.isActive) {
          case 0:
            return "softwareTaskList";
            break;
          case 1:
            return "taskTerminalList";
            break;
          default:
            return "softwareTaskDetails"
        }
      }
    },
    methods: {
      pButton1(index) {
        this.isActive = index;
      },
      pButton2(index,item) {
        this.isActive = index;
        this.data = item;
      }
    }
  }
</script>