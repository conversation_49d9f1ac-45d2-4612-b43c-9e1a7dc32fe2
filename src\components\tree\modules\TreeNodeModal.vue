<template>
  <a-modal :title='title' :width='width' :visible='visible' :confirmLoading='confirmLoading' switchFullscreen
    @ok='handleOk' @cancel='handleCancel' :destroyOnClose='true' cancelText='关闭'>
    <a-spin :spinning='confirmLoading'>
      <a-form :form='form'>
        <!-- <a-form-item label="父级节点" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <j-tree-select
            ref="treeSelect"
            placeholder="请选择父级节点"
            v-decorator="['pid']"
            dict="itil_config_item_type,type_name,id"
            pidField="pid"
            pidValue=""
            hasChildField="has_child">
          </j-tree-select>
        </a-form-item>-->
        <a-form-item label='类型' :labelCol='labelCol' :wrapperCol='wrapperCol'>
          <j-dict-select-tag :disabled='disabled' v-decorator="['type', validatorRules.type]" :triggerChange='true'
            placeholder='请选择类型' dictCode='roomNodeType' />
        </a-form-item>
        <a-form-item label='名称' :labelCol='labelCol' :wrapperCol='wrapperCol'>
          <a-input v-decorator="['name', validatorRules.name]" placeholder='请输入名称'></a-input>
        </a-form-item>
        <div v-if="form.getFieldValue('type') == 'room'">
          <a-form-item label='机房等级' :labelCol='labelCol' :wrapperCol='wrapperCol'>
            <j-dict-select-tag v-decorator="['level', validatorRules.level]" :triggerChange='true' placeholder='请选择机房等级'
              dictCode='ZB_JFDJ' />
          </a-form-item>
          <a-form-item :labelCol='labelCol' :wrapperCol='wrapperCol' label='占地面积'>
            <a-input-number v-decorator="['floorArea']" :min='0' :max='1000000' :allow-clear='true' autocomplete='off'
              style='width:180px' />&nbsp;m²
          </a-form-item>
          <a-form-item label='机房地址' :labelCol='labelCol' :wrapperCol='wrapperCol'>
            <a-input v-decorator="['address']" placeholder='请输入机房地址'></a-input>
          </a-form-item>
          <!-- <a-form-item label="删除标记" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input-number v-decorator="['delType']" placeholder="请输入删除标记" style="width: 100%"/>
          </a-form-item>-->
          <a-form-item label='是否上报' :labelCol='labelCol' :wrapperCol='wrapperCol'>
            <a-switch :disabled="reportDisabled" v-decorator="['isReport', { valuePropName: 'checked' }]"
                checkedChildren='是' unCheckedChildren='否'></a-switch>
              <a-tooltip>
                <template slot="title">
                  是否在国办数据上报中使用机房数据
                </template>
                <span>
                  <a-icon type="info-circle" style="font-size: 16px;margin-left: 8px;" theme="twoTone" />
                </span>
                
              </a-tooltip>

          </a-form-item>
        </div>

      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
import { httpAction, postAction } from '@/api/manage'
import pick from 'lodash.pick'
import { validateDuplicateValue } from '@/utils/util'
import JTreeSelect from '@/components/jeecg/JTreeSelect'
import JDictSelectTag from '@/components/dict/JDictSelectTag.vue'

//通过Key获取对应地title
const getRepeatTitle = (value, tree) => {
  let isRepeat = false
  for (let i = 0; i < tree.length; i++) {
    const node = tree[i]
    if (node.name === value) {
      isRepeat = true
      break
    }
    if (node.children) {
      isRepeat = getRepeatTitle(value, node.children)
    }
  }
  return isRepeat
}

export default {
  name: 'treeNodeModal',
  props: {
    nodeType: {
      type: String,
      required: false,
      default: ''
    }
  },
  components: {
    JTreeSelect,
    JDictSelectTag
  },
  data() {
    return {
      form: this.$form.createForm(this),
      title: '操作',
      width: 800,
      visible: false,
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      reportDisabled: false,
      confirmLoading: false,
      url: {
        add: '/topo/room/add',
        edit: '/topo/room/rename'
      },
      expandedRowKeys: [],
      pidField: 'pid',
      disabled: false,
      validatorRules: {
        type: {
          rules: [{ required: true, message: '请选择类型!' }]
        },
        name: {
          rules: [{ required: true, message: '请输入名称!' },
          { min: 2, message: '名称长度应在 2-20 之间！', trigger: 'blur' },
          { max: 20, message: '名称长度应在 2-20 之间！', trigger: 'blur' }]
        },
        level: {
          rules: [{ required: true, message: '请选择机房等级!' }]
        }
      },
      treeInfo: []
    }
  },
  created() {
  },
  mounted() {

  },
  methods: {
    // onblur(e){
    //   // e.target.value = e.target.value.trim()
    //   const { value } = e.target;
    //   let flag = getRepeatTitle(value, this.treeInfo)
    //   if(flag){
    //     this.$message.warning("名称已重复!")
    //   }
    // },
    add(obj, treeInfo) {
      this.treeInfo = [...treeInfo]
      this.disabled = false
      this.form.resetFields()
      this.model = Object.assign({}, {})
      this.visible = true
      this.model.pid = obj != null ? obj.toString() : ''
      if (this.nodeType) {
        this.model.type = this.nodeType
        this.disabled = true
      }
      this.$nextTick(() => {
        this.form.setFieldsValue(pick(this.model, 'name', 'type'))
      })
    },
    edit(record, treeInfo) {
      this.treeInfo = [...treeInfo]
      this.disabled = true
      this.form.resetFields()
      this.model = Object.assign({}, record)
      // console.log("编辑 === ", this.model)
      this.visible = true
      this.$nextTick(() => {
        this.model.isReport = record.isReport && record.isReport == 1 ? true : false
        this.form.setFieldsValue(pick(this.model, 'name', 'type'))
        if (this.model.type === "room") {
          this.$nextTick(() => {
            this.form.setFieldsValue(pick(this.model, 'level', 'floorArea', 'address', 'isReport'))
          })
        }
      })
    },
    close() {
      this.visible = false
    },
    handleOk() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let formData = Object.assign(this.model, values)
          formData.isReport = that.model.isReport == true ? 1 : 0
          httpAction(httpurl, formData, method).then(res => {
            if (res.success) {
              that.$message.success(res.message)
              formData.id = res.result;
              let param = {
                ...formData,
                method,
              }
              this.$emit("refresh", param)
              that.close()
            } else {
              that.$message.warning(res.message)
            }
            that.confirmLoading = false
          }).catch(error => {
            that.confirmLoading = false
          })
        }
      })
    },
    handleCancel() {
      this.close()
    },
    popupCallback(row) {
      this.form.setFieldsValue(pick(row, 'name', 'type'))
    },
    submitSuccess(formData, flag) {
      if (!formData.id) {
        let treeData = this.$refs.treeSelect.getCurrTreeData()
        this.expandedRowKeys = []
        this.getExpandKeysByPid(formData[this.pidField], treeData, treeData)
        this.$emit('ok', formData, this.expandedRowKeys.reverse())
      } else {
        this.$emit('ok', formData, flag)
      }
    },
    getExpandKeysByPid(pid, arr, all) {
      if (pid && arr && arr.length > 0) {
        for (let i = 0; i < arr.length; i++) {
          if (arr[i].key == pid) {
            this.expandedRowKeys.push(arr[i].key)
            this.getExpandKeysByPid(arr[i]['parentId'], all, all)
          } else {
            this.getExpandKeysByPid(pid, arr[i].children, all)
          }
        }
      }
    }
  }
}
</script>