<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container>
      <a-form-model ref="form" slot="detail" :model="model" :rules="validatorRules">
        <!--基础设置-->
        <div class="colorBox">
          <span class="colorTotal">基础设置</span>
        </div>
        <a-row justify="space-between" type="flex">
          <a-col :span="12">
            <a-form-model-item label="告警名称" prop="name" v-bind="formItemLayout0">
              <a-input v-model="model.name" :allow-clear="true" autocomplete="off" placeholder="请输入告警名称" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="告警描述" prop="remark" v-bind="formItemLayout0">
              <a-textarea
                v-model="model.remark"
                :autoSize="{ minRows: 1, maxRows: 6 }"
                :allow-clear="true"
                autocomplete="off"
                placeholder="请输入告警描述"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="产品名称" prop="productId" v-bind="formItemLayout0">
              <a-cascader
                v-model="model.productId"
                :disabled="!showFlag"
                :field-names="{ label: 'label', value: 'id', text: 'code', leaf: 'isLeaf', children: 'items' }"
                :load-data="loadData"
                :multiple="true"
                :options="productOptions"
                change-on-select
                placeholder="请选择产品名称"
                @change="onChangeProduct"
              >
                <template slot="displayRender" slot-scope="{ labels, selectedOptions }">
                  {{ productDisplayRender(selectedOptions) }}
                </template>
              </a-cascader>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="设备名称" prop="deviceIdList" v-bind="formItemLayout0">
              <a-select
                :getPopupContainer="(node) => node.parentNode"
                v-model="model.deviceIdList"
                :allow-clear="true"
                :disabled="!showFlag"
                :maxTagCount="3"
                :maxTagTextLength="19"
                :show-search="true"
                mode="multiple"
                option-filter-prop="label"
                placeholder="请选择设备名称"
              >
                <a-select-option v-for="item in deviceList" :key="item.id" :label="item.code" :value="item.id">
                  {{ item.code }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>

        <!--规则设置-->
        <div class="colorBox">
          <span class="colorTotal">规则设置</span>
        </div>
        <div>
          <a-row class="trigger-row">
            <a-col :span="24">
              <a-form-model-item style="margin-bottom: 0px" prop="triggerType" v-bind="formItemLayout0">
                <a-radio-group v-model="model.triggerType">
                  <a-radio :value="'0'">满足任意规则触发</a-radio>
                  <a-radio :value="'1'">满足所有规则触发</a-radio>
                </a-radio-group>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-tabs v-model="activeKey" :hide-add="false" default-active-key="0" type="editable-card" @edit="onEdit">
            <a-tab-pane
              v-for="(item, index) in model.alarmRules"
              :key="item.key"
              :closable="item.closable"
              :force-render="true"
              :tab="item.title"
            >
              <a-row justify="space-between" type="flex">
                <!--指标规则设置-->
                <a-col :span="24" class="group-box">
                  <span class="dot"></span>
                  <span class="group">指标条件</span>
                </a-col>

                <a-col :span="13">
                  <a-form-model-item
                    :prop="'alarmRules[' + index + '].subjectIndexArr'"
                    :rules="[{ required: true, validator: subjectIndexValidate }]"
                    label="指标"
                    v-bind="formItemLayout1"
                  >
                    <a-cascader
                      v-model="item.subjectIndexArr"
                      :field-names="{ label: 'name', text: 'name', value: 'code', children: 'proertyMetadataList' }"
                      :options="subjectIndexList"
                      change-on-select
                      placeholder="请选择指标"
                      @change="(value, selectedOptions) => changeSubjectIndex(value, selectedOptions, index)"
                    />
                  </a-form-model-item>
                </a-col>
                <a-col :span="10">
                  <a-form-model-item
                    ref="conditions"
                    :prop="'alarmRules[' + index + '].conditions'"
                    :rules="[{ required: true, message: '请选择条件', trigger: 'change' }]"
                    label="条件"
                    v-bind="formItemLayout0"
                  >
                    <div>
                      <a-select
                        @blur="
                          () => {
                            $refs.conditions[index].onFieldBlur()
                          }
                        "
                        @change="
                          () => {
                            $refs.conditions[index].onFieldChange()
                          }
                        "
                        :getPopupContainer="(node) => node.parentNode"
                        v-model="item.conditions"
                        :allow-clear="true"
                        placeholder="请选择条件"
                        style="width: calc(100% - 25px)"
                      >
                        <a-select-option
                          v-for="item in item.conditionsList"
                          :key="index + '_conditionsList' + item.value"
                          :label="item.title"
                          :value="item.value"
                        >
                          {{ item.title }}
                        </a-select-option>
                      </a-select>
                      <a-popover>
                        <template slot="content">
                          <p>表达式示例:x为固定占位符,表示当前规则的指标值,不可修改;并且表达式必须返回真或假.</p>
                          <p>区间(5,10]: x &gt; 5 && x &lt;= 10</p>
                          <p>数学计算: x + 10 &gt; 5</p>
                          <p>字符串包含:string.contains(x,'abc'),即x是否包含'123'</p>
                        </template>
                        <span style="display: inline-block; width: 25px; padding-left: 5px">
                          <a-icon style="fontsize: 20px; text-align: right" theme="twoTone" type="question-circle" />
                        </span>
                      </a-popover>
                    </div>
                  </a-form-model-item>
                </a-col>
                <a-col :span="13">
                  <a-form-model-item
                    :prop="'alarmRules[' + index + '].specifyKey'"
                    :rules="[{ required: false, message: '请选择子级指标匹配键!' }]"
                    label="子级指标匹配键"
                    v-bind="formItemLayout1"
                  >
                    <a-select
                      :getPopupContainer="(node) => node.parentNode"
                      v-model="item.specifyKey"
                      :allow-clear="true"
                      placeholder="请选择子级指标匹配键"
                    >
                      <a-select-option
                        v-for="item in item.specifyKeyList"
                        :key="item.code"
                        :label="item.name"
                        :value="item.code"
                      >
                        {{ item.name }}
                      </a-select-option>
                    </a-select>
                  </a-form-model-item>
                </a-col>
                <a-col :span="10" style="margin-bottom: 16px">
                  <a-form-model-item
                    :prop="'alarmRules[' + index + '].specifyValue'"
                    :rules="[
                      {required:false, validator: (rule, value, callback) =>ValidateOptionalRuleFields(rule, value, callback,'子级指标匹配值',100) }
                    ]"
                    label="子级指标匹配值"
                    v-bind="formItemLayout0"
                  >
                    <a-input v-model="item.specifyValue" placeholder="子级指标匹配值" />
                  </a-form-model-item>
                </a-col>
                <!--告警规则设置-->
                <a-col :span="24" class="group-box">
                  <span class="dot"></span>
                  <span class="group">告警条件</span>
                  <a-popover title="告警条件说明">
                    <template slot="content">
                      <p>触发次数: 数据包连续n次到达配置的阈值,产生告警</p>
                      <p>升级次数: 当前级别的告警重复n次后自动升级为更高一级告警</p>
                    </template>
                    <a-icon style="font-size: 20px" theme="twoTone" type="question-circle" />
                  </a-popover>
                </a-col>
                <a-col
                  v-for="(items, idx) in model.alarmRules[index].contentArr"
                  :key="'alarmRules_' + index + '_' + idx"
                  :span="24"
                >
                  <a-row justify="space-between" type="flex">
                    <a-col :span="2">
                      <a-form-model-item>
                        <span style="color: rgba(0, 0, 0, 0.85)">
                          <a-checkbox
                            v-model="items.checked"
                            style="margin-right: 12px"
                            @change="changeContentRulesCheckbox($event, items, index, idx)"
                          />
                          <span :title="items.title">{{ setAlarmLevelName(items.title, 6) }}</span>
                        </span>
                      </a-form-model-item>
                    </a-col>
                    <a-col :span="8" v-if="item.levelValueComType == 'default'">
                      <a-form-model-item
                        :prop="'alarmRules[' + index + '].contentArr[' + idx + '].value'"
                        :rules="[
                          { required: items.checked,validator: (rule, value, callback) =>validateDynamicRuleValue(rule, value, callback,'阈值',100,1)},
                        ]"
                        label="阈值"
                        v-bind="formItemLayout2"
                      >
                        <a-input v-model="items.value" :disabled="!items.checked" placeholder="请输入" />
                      </a-form-model-item>
                    </a-col>
                    <a-col :span="8" v-else-if="item.levelValueComType == 'choiceUnitCom'">
                      <a-form-model-item
                        :prop="'alarmRules[' + index + '].contentArr[' + idx + '].value'"
                        :rules="[{ required: items.checked, min: 1, max: 200, validator: validateLevelValue }]"
                        label="阈值"
                        v-bind="formItemLayout2"
                      >
                        <a-input-group compact>
                          <a-input
                            style="width: calc(100% - 60px) !important"
                            :disabled="!items.checked"
                            v-model="items.value"
                            placeholder="请输入"
                            @change="calculateTargetValue(items)"
                          />
                          <a-select
                            v-model="items.originUnit"
                            :disabled="!items.checked"
                            @change="calculateTargetValue(items)"
                          >
                            <a-select-option
                              v-for="(unit, i) in item.levelValueUnitList"
                              :key="unit + '_' + i"
                              :label="unit"
                              :value="unit"
                            >
                              {{ unit }}
                            </a-select-option>
                          </a-select>
                        </a-input-group>
                      </a-form-model-item>
                    </a-col>
                    <!--                    <a-col :span='10'>
                                          <a-form-model-item :prop="'alarmRules['+index+'].contentArr['+idx+'].value'"
                                                             :rules="[{required: items.checked,min: 1, max: 200,validator:validateLevelValue,trigger:['change','blur']}]"
                                                             label='阈值'
                                                             v-bind='formItemLayout2'>
                                            <a-input  v-if='item.levelValueComType=="default"'  v-model='items.value' :disabled='!items.checked'
                                                      placeholder='请输入阈值' />
                                            <a-input-group compact  v-else-if='item.levelValueComType=="choiceUnitCom"'>
                                              <a-input
                                                style="width:calc(100% - 60px) !important;"
                                                :disabled='!items.checked'
                                                v-model='items.value'
                                                placeholder='请输入阈值'
                                                @change='calculateTargetValue(items)'/>
                                              <a-select
                                                :getPopupContainer='(node) => node.parentNode'
                                                v-model='items.originUnit'
                                                :disabled='!items.checked'
                                                @change='calculateTargetValue(items)'>
                                                <a-select-option v-for='(unit,i) in item.levelValueUnitList' :key='unit+"_"+i' :label='unit'  :value='unit'>
                                                  {{unit}}
                                                </a-select-option>
                                              </a-select>
                                            </a-input-group>
                                          </a-form-model-item>
                                        </a-col>-->
                    <a-col :span="5">
                      <a-form-model-item
                        :prop="'alarmRules[' + index + '].contentArr[' + idx + '].times'"
                        :rules="[
                          { required: items.checked, message: '请输入触发次数' },
                          { pattern: /^[1-9]{1}[0-9]*$/, message: '请输入≥1的整数' },
                        ]"
                        label="触发次数"
                        v-bind="formItemLayout10"
                      >
                        <a-input-number v-model="items.times" :disabled="!items.checked" placeholder="请输入" />
                      </a-form-model-item>
                    </a-col>
                    <a-col :span="5">
                      <!--最高级的告警不需要升级-->
                      <a-form-model-item
                        v-if="idx !== model.alarmRules[index].contentArr.length - 1"
                        :prop="'alarmRules[' + index + '].contentArr[' + idx + '].changeTimes'"
                        :rules="[
                          { required: false, message: '请输入升级次数!' },
                          { pattern: /^[1-9]{1}[0-9]*$/, message: '请输入≥1的整数!' },
                        ]"
                        label="升级次数"
                        v-bind="formItemLayout10"
                      >
                        <a-input-number v-model="items.changeTimes" :disabled="!items.checked" placeholder="请输入" />
                      </a-form-model-item>
                    </a-col>
                  </a-row>
                </a-col>
              </a-row>
            </a-tab-pane>
          </a-tabs>
        </div>

        <!--自恢复设置-->
        <div class="colorBox" style="margin-top: 18px">
          <span class="colorTotal">自恢复设置</span>
        </div>
        <a-row justify="space-between" type="flex">
          <a-col :span="12">
            <a-form-model-item label="脚本场景" prop="sceneId" v-bind="formItemLayout6">
              <a-select
                :getPopupContainer="(node) => node.parentNode"
                v-model="model.sceneId"
                :allow-clear="true"
                :show-search="true"
                option-filter-prop="label"
                placeholder="请选择脚本场景"
              >
                <a-select-option v-for="item in sceneList" :key="item.id" :label="item.sceneName" :value="item.id">
                  {{ item.sceneName }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>

        <!--消息推送设置-->
        <div class="colorBox" style="display: flex">
          <div class="colorTotal">消息通知设置</div>
          <div style="margin: 0 10px">
            <a-popover title="消息通知规则">
              <template slot="content">
                <p>留观次数: 触发n次告警后通知，0次为立即触发</p>
                <p>重复通知间隔: 每间隔n次告警通知一次，-1为不重复只首次通知，0为无间隔</p>
              </template>
              <a-icon style="font-size: 20px" theme="twoTone" type="question-circle" />
            </a-popover>
          </div>
          <a-switch
            v-model="model.noticeStatus"
            checked-children="开"
            un-checked-children="关"
            @change="changeNoticeStatus"
          />
        </div>
        <div v-if="model.noticeStatus">
          <a-row justify="space-between" type="flex">
            <a-col :span="24" class="notice">
              <a-row
                v-for="(item, index) in model.pushRulesArr"
                :key="'model.pushRulesArr[' + index + ']'"
                :gutter="12"
              >
                <a-col :span="3">
                  <a-form-model-item>
                    <span style="color: rgba(0, 0, 0, 0.85)">
                      <a-checkbox
                        v-model="item.checked"
                        style="margin-left: 6px; margin-right: 12px"
                        @change="changePushRulesCheckbox($event, item)"
                      />
                      <span :title="item.title">{{ setAlarmLevelName(item.title, 6) }}</span>
                    </span>
                  </a-form-model-item>
                </a-col>
                <a-col :span="6">
                  <a-form-model-item
                    :key="'pushRulesArr[' + index + '].noticeVisitsTimes'"
                    :prop="'pushRulesArr[' + index + '].noticeVisitsTimes'"
                    :rules="[{ required: item.checked, message: '请输入留观次数' }]"
                    label="留观次数"
                    v-bind="formItemLayout3"
                  >
                    <a-input-number
                      :key="'pushRulesArr[' + index + '].noticeVisitsTimes1'"
                      v-model="item.noticeVisitsTimes"
                      :disabled="!item.checked"
                      :min="0"
                      placeholder="请输入"
                    />
                  </a-form-model-item>
                </a-col>
                <a-col :span="8">
                  <a-form-model-item
                    :key="'pushRulesArr[' + index + '].noticeIntervalTimes'"
                    :prop="'pushRulesArr[' + index + '].noticeIntervalTimes'"
                    :rules="[{ required: item.checked, message: '请输入重复通知间隔次数' }]"
                    label="重复通知间隔(次)"
                    v-bind="formItemLayout4"
                  >
                    <a-input-number
                      :key="'pushRulesArr[' + index + '].noticeIntervalTimes1'"
                      v-model="item.noticeIntervalTimes"
                      :disabled="!item.checked"
                      :min="-1"
                      placeholder="请输入"
                    />
                  </a-form-model-item>
                </a-col>
                <a-col :span="7">
                  <a-form-model-item
                    :key="'pushRulesArr[' + index + '].noticeTemplateIds'"
                    :prop="'pushRulesArr[' + index + '].noticeTemplateIds'"
                    :rules="[{ required: item.checked, message: '请选择通知模板' }]"
                    label="通知模板"
                    v-bind="formItemLayout5"
                  >
                    <a-select
                      :key="'pushRulesArr[' + index + '].noticeTemplateIds1'"
                      v-model="item.noticeTemplateIds"
                      :allow-clear="true"
                      :disabled="!item.checked"
                      :maxTagCount="1"
                      :maxTagTextLength="4"
                      mode="multiple"
                      placeholder="请选择"
                      :getPopupContainer="(node) => node.parentNode"
                      @change="changeLevelNoticeTemplate($event, index)"
                    >
                      <a-select-option
                        v-for="(items, idx) in noticeTemplateList"
                        :key="'[' + index + ']noticeTemplateList[' + idx + ']'"
                        :value="items.type"
                      >
                        {{ items.code }}
                      </a-select-option>
                    </a-select>
                  </a-form-model-item>
                </a-col>
              </a-row>
            </a-col>
            <a-col :span="24" style="margin-top: 16px">
              <a-row :gutter="24" justify="start" type="flex">
                <a-col :span="12">
                  <a-form-model-item label="生效时间" prop="effectiveDate" v-bind="formItemLayout6">
                    <a-select
                      v-model="model.effectiveDate"
                      :getPopupContainer="(node) => node.parentNode"
                      :allow-clear="true"
                      :maxTagCount="7"
                      :maxTagTextLength="3"
                      mode="multiple"
                      placeholder="请选择"
                    >
                      <a-select-option v-for="(item, index) in effectiveDateList" :key="index" :value="item.value">
                        {{ item.label }}
                      </a-select-option>
                    </a-select>
                  </a-form-model-item>
                </a-col>
                <a-col :span="6">
                  <a-form-model-item label="开始时间" prop="beginTime" v-bind="formItemLayout7">
                    <a-time-picker
                      v-model="model.beginTime"
                      format="HH:mm"
                      placeholder="请选择"
                      style="width: 100%"
                      valueFormat="HH:mm"
                      @change="changeBeginTime"
                    />
                  </a-form-model-item>
                </a-col>
                <a-col :span="6">
                  <a-form-model-item label="结束时间" prop="endTime" v-bind="formItemLayout7">
                    <a-time-picker
                      v-model="model.endTime"
                      format="HH:mm"
                      placeholder="请选择"
                      style="width: 100%"
                      valueFormat="HH:mm"
                      @change="changeEndTime"
                    />
                  </a-form-model-item>
                </a-col>
              </a-row>
              <a-row :gutter="24" justify="start" type="flex">
                <a-col :span="9">
                  <a-form-model-item label="告警沉默周期(分钟)" prop="silentTime" v-bind="formItemLayout8">
                    <div>
                      <a-input-number
                        v-model="model.silentTime"
                        :min="1"
                        :step="1"
                        :precision='0'
                        placeholder="请输入"
                        style="width: calc(100% - 25px) !important"
                      />
                      <a-popover>
                        <template slot="content">
                          <p>首次告警后，指定时间内不再推送告警通知，单位为分钟</p>
                        </template>
                        <span style="display: inline-block; width: 25px; padding-left: 5px">
                          <a-icon style="fontsize: 20px; text-align: right" theme="twoTone" type="question-circle" />
                        </span>
                      </a-popover>
                    </div>
                  </a-form-model-item>
                </a-col>
                <a-col :span="6">
                  <a-form-model-item label="告警解除通知" prop="noAlarmNotice" v-bind="formItemLayout9">
                    <a-switch
                      v-model="model.noAlarmNotice"
                      checked-children="开"
                      un-checked-children="关"
                      @change="ChangeNoAlarmNotice"
                    />
                  </a-form-model-item>
                </a-col>
                <a-col v-if="model.noAlarmNotice" :span="9">
                  <a-form-model-item label="告警解除模板" prop="noAlarmNoticeId" v-bind="formItemLayout8">
                    <a-select
                      v-model="model.noAlarmNoticeId"
                      :getPopupContainer="(node) => node.parentNode"
                      :allow-clear="true"
                      :max-tag-count="1"
                      placeholder="请选择"
                    >
                      <a-select-option v-for="(item, index) in noticeTemplateList" :key="index" :value="item.type">
                        {{ item.code }}
                      </a-select-option>
                    </a-select>
                  </a-form-model-item>
                </a-col>
              </a-row>
            </a-col>
          </a-row>
        </div>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>
<script>
import { getAction, httpAction } from '@/api/manage'
import { ajaxGetDictItems, getDictItemsFromCache } from '@/api/api'
import moment from 'moment'
import { alarmLevelValueUnit } from '@comp/alarmTemplate/alarmLevelValueUnit'
import {ValidateOptionalFields,ValidateRequiredFields} from '@/utils/rules.js'
export default {
  name: 'AlarmTemplateDetail',
  mixins: [alarmLevelValueUnit],
  data() {
    return {
      /* bsStr: '',*/
      //布局
      confirmLoading: false,
      formItemLayout0: {
        labelCol: { span: 8 },
        wrapperCol: { span: 16 },
      },
      formItemLayout1: {
        labelCol: { span: 7 },
        wrapperCol: { span: 16 },
      },
      formItemLayout2: {
        labelCol: { span: 4 },
        wrapperCol: { span: 20 },
      },
      formItemLayout3: {
        labelCol: { span: 10 },
        wrapperCol: { span: 14 },
      },
      formItemLayout4: {
        labelCol: { span: 12 },
        wrapperCol: { span: 12 },
      },
      formItemLayout5: {
        labelCol: { span: 9 },
        wrapperCol: { span: 15 },
      },

      formItemLayout6: {
        labelCol: { span: 4 },
        wrapperCol: { span: 20 },
      },
      formItemLayout7: {
        labelCol: { span: 10 },
        wrapperCol: { span: 14 },
      },
      formItemLayout8: {
        labelCol: { span: 9 },
        wrapperCol: { span: 15 },
      },
      formItemLayout9: {
        labelCol: { span: 16 },
        wrapperCol: { span: 6 },
      },
      formItemLayout10: {
        labelCol: { span: 10 },
        wrapperCol: { span: 14 },
      },
      //表单校验规则
      validatorRules: {
        name: [
          {
            required: true,validator: (rule, value, callback) =>ValidateRequiredFields(rule, value, callback,'告警名称',100,1)
          }
        ],
        remark: [
          {
            required: true,
            validator: (rule, value, callback) =>ValidateRequiredFields(rule, value, callback,'告警描述',200,1)
          }
        ],
        productId: [{ required: true, validator: this.productIdValidate }],
        sceneId: [{ required: false, message: '请选择脚本场景' }],
        triggerType: [
          {
            required: true,
            message: '请选择触发条件',
          },
        ],
        effectiveDate: [{ required: true, message: '请选择生效时间' }],
        beginTime: [{ required: true, validator: this.beginTimeValidate }],
        endTime: [
          {
            required: true,
            validator: this.endTimeValidate,
          },
        ],
        noAlarmNoticeId: [
          {
            required: true,
            message: '请选择告警解除模板',
          },
        ],
      },

      model: {
        name: '',
        remark: '',
        productId: [],
        deviceIdList: undefined,
        alarmRules: [],
        sceneId: undefined,
        triggerType: '0',
        noticeStatus: false,
        pushRulesArr: [],
        effectiveDate: [2, 3, 4, 5, 6, 7, 1],
        /*   beginTime: null,
           endTime: null,*/
        beginTime: moment().startOf('day').format('HH:mm'),
        endTime: moment().endOf('day').format('HH:mm'),
        silentTime: '',
        noAlarmNotice: false,
        noAlarmNoticeId: '',
      },
      productOptions: [],
      lastProductType: '', //产品名称所选的最后一级数据类型（category/product）
      lastProductId: '', //产品名称所选的最后一级数据id
      deviceList: [], //设备名称下拉数据
      newTabIndex: 0,
      activeKey: 0,
      tabTitle: '规则',
      subjectIndexList: undefined, //指标下拉数据
      conditionDicts: [], //条件字典值
      contents: [], //告警级别（一般、严重等）
      sceneList: [], //脚本场景下拉数据
      noticeTemplateList: [], //通知模板下拉数据
      //通知生效时间下拉数据
      effectiveDateList: [
        { label: '周一', value: '2' },
        { label: '周二', value: '3' },
        { label: '周三', value: '4' },
        { label: '周四', value: '5' },
        { label: '周五', value: '6' },
        { label: '周六', value: '7' },
        { label: '周日', value: '1' },
      ],
      url: {
        productId: '/alarm/alarmTemplate/selectProAndCategoryByProId', //设备信息-查看-告警策略--新增：请求产品名称productId数据
        rule: '/alarm/alarmTemplate/findTemAndRule', //请求规则
        add: '/alarm/alarmTemplate/add', //新增提交接口
        edit: '/alarm/alarmTemplate/edit', //编辑提交接口
        subjectIndex: '/alarm/alarmTemplate/findRuleBySubjectIndex', //通过产品id获取指标下拉数据的接口
        childrenSubjectIndex: '/product/proertyMetadata/childrenList', //通过所选指标最后一级是否是array类型，获取子级指标匹配键下拉数据
        assets: '/sys/notice/template/assets', //获取产品第一级数据
        productSublevel: '/sys/notice/template/productList', //根据当前所选产品分类id，获取该级下的子级数据
        devList: '/sys/notice/template/devList', //根据产品id获取设备名称下拉数据
        noticeTemplateList: '/sys/notice/template/templateList', // 通知模板下拉数据
        alarmLevel: '/alarm/alarmLevel/getLevelList', //获取告警级别数据
        getValueByUnitConvert: '/alarm/alarmTemplate/getValueByUnitConvert',
        scene: '/autoControl/task/getSceneList', //脚本场景下拉数据
      },
    }
  },
  props: {
    showFlag: {
      type: Boolean,
      default: true,
    },
    deviceInfo: {
      type: Object,
      default: () => {},
    },
  },
  created() {
    this.getSceneList()
  },
  methods: {
    moment,
    //*****************初始化数据*****************
    /*设备信息--查看--告警策略：新增时请求设备所属产品的productId，其他时候不需要走这个接口*/
    getProductId(id) {
      let that = this
      return new Promise(function (resolve, reject) {
        // if(!that.model.id&&!that.showFlag) {
        getAction(that.url.productId, { productId: id })
          .then((res) => {
            if (res.success) {
              resolve({
                success: true,
                data: res.result,
                message: res.message,
              })
            } else {
              reject({
                success: false,
                data: undefined,
                message: res.message,
              })
            }
          })
          .catch((res) => {
            reject({
              success: false,
              data: undefined,
              message: res.message,
            })
          })
        // }
        /*    else {
            resolve({
              success: true,
              data: undefined,
              message: '操作成功'
            })
          }*/
      })
    },
    /*获取产品名称第一级数据*/
    getRootProductCategory() {
      let that = this
      return new Promise(function (resolve, reject) {
        getAction(that.url.assets)
          .then((res) => {
            if (res.success) {
              res.result.map((item) => {
                item.label = that.getIconName(item.type, item.code)
                item.isLeaf = false
                item.items = item.items && item.items.length > 0 ? item.items : undefined
                return item
              })
              resolve({
                success: true,
                data: res.result,
                message: res.message,
              })
            } else {
              reject({
                success: false,
                data: undefined,
                message: res.message,
              })
            }
          })
          .catch((res) => {
            reject({
              success: false,
              data: undefined,
              message: res.message,
            })
          })
      })
    },
    /*根据产品名称下拉数据每条类型，设置前置图标，用于区分产品分类和产品*/
    getIconName(type, code) {
      let label = ''
      switch (type) {
        case 'category':
          label = (
            <div style="display: inline-block;white-space: nowrap;">
              <a-icon type="folder" style="color:#409eff;padding-right:5px" />
              {code}
            </div>
          )
          break
        case 'product':
          label = (
            <div style="display: inline-block;white-space: nowrap">
              <a-icon type="file" style="color:#409eff;margin-right:5px" />
              {code}
            </div>
          )
          break
      }
      return label
    },
    /*
     * 获取条件字典值
     * @param dictCode 字典名
     */
    getConditionDictData(dictCode) {
      let that = this
      return new Promise(function (resolve, reject) {
        //优先从缓存中读取字典配置
        if (getDictItemsFromCache(dictCode)) {
          resolve({
            success: true,
            data: getDictItemsFromCache(dictCode),
            message: '操作成功',
          })
          return
        }
        //根据字典Code, 初始化字典数组
        ajaxGetDictItems(dictCode, null)
          .then((res) => {
            if (res.success) {
              resolve({
                success: true,
                data: res.result,
                message: res.message,
              })
            } else {
              reject({
                success: false,
                data: [],
                message: res.message,
              })
            }
          })
          .catch((res) => {
            reject({
              success: false,
              data: [],
              message: res.message,
            })
          })
      })
    },
    /*获取告警级别内容*/
    getAlarmContents() {
      let that = this
      return new Promise(function (resolve, reject) {
        getAction(that.url.alarmLevel)
          .then((res) => {
            if (res.success) {
              let params = []
              for (let i = 0; i < res.result.length; i++) {
                let item = res.result[i]
                let param = {
                  /* title: item.levelName,
                 level: item.alarmLevel*/
                  title: item.title,
                  level: item.value,
                }
                params.push(param)
              }
              resolve({
                success: true,
                data: params,
                message: res.message,
              })
            } else {
              reject({
                success: false,
                data: [],
                message: res.message,
              })
            }
          })
          .catch((res) => {
            reject({
              success: false,
              data: [],
              message: res.message,
            })
          })
      })
    },
    /*获取通知模板下拉数据*/
    getNoticeTemplateList() {
      let that = this
      let route = this.$route.path
      return new Promise(function (resolve, reject) {
        getAction(that.url.noticeTemplateList, { business: route })
          .then((res) => {
            if (res.success) {
              resolve({
                success: true,
                data: res.result && res.result.length > 0 ? res.result : [],
                message: res.message,
              })
            } else {
              reject({
                success: false,
                data: [],
                message: res.message,
              })
            }
          })
          .catch((res) => {
            reject({
              success: false,
              data: [],
              message: res.message,
            })
          })
      })
    },
    /**获取脚本场景下拉数据*/
    getSceneList() {
      let that = this
      that.sceneList = []
      getAction(that.url.scene)
        .then((res) => {
          if (res.success) {
            that.sceneList = res.result
          } else {
            that.$message.warning(res.message)
          }
        })
        .catch((err) => {
          that.$message.warning(err.message)
        })
    },

    //*****************表单：新增、编辑、提交*****************
    /*新增表单*/
    addAlarm() {
      this.edit({})
    },
    /*
    编辑：表单回显
    设备信息--查看--告警策略：
       1、原来需求：产品、设备名称不显示
       2、最新需求：产品、设备名称显示不可编辑
    告警管理--告警策略：产品、设备名称显示可编辑
     */
    edit(record) {
      this.$nextTick(() => {
        let that = this
        that.confirmLoading = true
        Promise.all([
          //获取产品一级数据
          that.getRootProductCategory(),
          //获取条件下拉数据
          that.getConditionDictData('compareCondition'),
          //获取通知模板下拉数据
          that.getNoticeTemplateList(),
          //获取告警级别
          that.getAlarmContents(),
        ])
          .then((result) => {
            let proResult = result.every((item, index) => {
              if (index == 0) {
                that.productOptions = item.data
              } else if (index == 1) {
                that.conditionDicts = item.data
              } else if (index == 2) {
                that.noticeTemplateList = item.data
              } else if (index == 3) {
                that.contents = JSON.parse(JSON.stringify(item.data))
                let obj = JSON.parse(JSON.stringify(item.data))
                that.resetPushRules(obj)
              }
              return item.success == true
            })
            if (proResult) {
              that.model = Object.assign(that.model, record)
              if (!record.sceneId) {
                delete that.model.sceneId
              }
              that.initialNoticeValue(record)
              that.getPushRulesByLevel(record.pushRule)
              that.model.alarmRules = []
              //编辑，record.id不为空
              if (!!record.id) {
                //旧的需求：编辑两套逻辑
                /* //编辑：设备信息--查看--告警策略，父组件通过deviceInfo传递产品id
              if (!that.showFlag) {
                /!*!//需隐藏产品名称、设备名称时设备信息告警策略回显逻辑
                that.lastProductType = 'product'
                 that.lastProductId = that.deviceInfo.productId
                 that.getSubjectIndex(that.lastProductId).then((res) => {
                   that.subjectIndexList = res.data
                   that.getRulesData(that.model.id)
                 })*!/
              }
              //编辑：告警管理--告警策略
              else {
                that.getProductAndDeviceList(record.productId)
                //根据最后一项产品id获取指标下拉数据
                let proIds= record.productId.split(',')
                that.getSubjectIndex(proIds[proIds.length - 1]).then((res) => {
                  if (res.success) {
                    that.subjectIndexList = res.data
                    that.getRulesData(that.model.id)
                  }
                })
              }*/

                //最新编辑一套逻辑
                that.getProductAndDeviceList(record.productId)
                //根据最后一项产品id获取指标下拉数据
                let proIds = record.productId.split(',')
                that.getSubjectIndex(proIds[proIds.length - 1]).then((res) => {
                  if (res.success) {
                    that.subjectIndexList = res.data
                    that.getRulesData(that.model.id)
                  }
                })
              }
              //设备信息--查看--告警策略--新增告警
              else if (!that.showFlag) {
                let proId = that.deviceInfo.productId
                that.getSubjectIndex(proId).then((res) => {
                  that.subjectIndexList = res.data
                  that.add()
                })
                that.getProductId(proId).then((res) => {
                  that.getProductAndDeviceList(res.data)
                  that.model.deviceIdList = []
                  that.model.deviceIdList.push(that.deviceInfo.id)
                  that.confirmLoading = false
                })
              }
              //告警管理--告警策略--新增：不主动加载指标数据，而是通过选择产品名称加载指标数据
              else {
                that.add()
                that.confirmLoading = false
              }
            } else {
              //加载基础数据异常
              //that.$message.warning('发生异常，请稍后重试')
              that.confirmLoading = false
            }
          })
          .catch((err) => {
            that.confirmLoading = false
            that.$message.warning('发生异常，请稍后重试')
          })
      })
    },
    /*处理消息推送设置中某些变量的初始值。确保前端组件正常显示*/
    initialNoticeValue(record) {
      let that = this
      that.model.beginTime = !!record.beginTime ? record.beginTime : moment().startOf('day').format('HH:mm')
      that.model.endTime = !!record.endTime ? record.endTime : moment().endOf('day').format('HH:mm')
      that.model.silentTime = !!record.silentTime ? record.silentTime : null
      if (record.effectiveDate) {
        let start = record.effectiveDate.indexOf('[') + 1
        let end = record.effectiveDate.indexOf(']')
        that.model.effectiveDate = record.effectiveDate.substring(start, end).split(',')
      } else {
        that.model.effectiveDate = ['2', '3', '4', '5', '6', '7', '1']
      }
      that.model.noAlarmNotice = !!record.noAlarmNotice ? true : false
      that.model.noAlarmNoticeId = !!record.noAlarmNoticeId ? record.noAlarmNoticeId : undefined
    },
    /*实现产品、设备数据回显*/
    getProductAndDeviceList(proIds) {
      let that = this
      that.setProductIds(proIds)
      //根据最后一项产品id获取设备下拉数据
      that.getDeviceList(that.lastProductId)
      if (!that.model.deviceIdList || that.model.deviceIdList.length == 0) {
        that.model.deviceIdList = []
      }
    },
    /*解析产品名称数据，实现产品数据回显*/
    setProductIds(proIds) {
      let that = this
      let ids = proIds.split(',')
      that.model.productId = ids
      that.lastProductId = ids[ids.length - 1] //和proId是一样的
      that.lastProductType = 'product'
      //处理产品名称回显
      that.getProductName(ids, that.productOptions)
    },
    /* 提交表单数据*/
    submitForm() {
      const that = this
      // 触发表单验证
      that.$refs.form.validate((err, values) => {
        if (err) {
          if (!that.hasAlarmContent()) {
            that.$message.warning('要求每项规则至少选择填写一种告警级别！')
            return
          }

          if (!that.checkAlarmRepeatability()) {
            that.$message.warning('规则条件有完全相同的，请修改！')
            return
          }
          if (that.model.noticeStatus) {
            if (!that.checkAlarmNoticeIsNull()) {
              that.$message.warning('请给已选告警条件设置对应等级的消息通知！')
              return
            }
            let tip = this.checkAlarmNoticeIsExist()
            if (tip) {
              that.$message.warning(tip)
              return
            }
          }

          let mainData = JSON.parse(JSON.stringify(that.model))
          mainData.alarmRules = that.createRuleObject()
          mainData.pushRule = that.createPushRuleObject(mainData.pushRulesArr)
          delete mainData.pushRulesArr
          that.tipsBeforeSubmission(mainData)
        }
      })
    },
    /**
     提交前判断告警绑定产品、设备情况给出提示信息
     1、告警管理--告警策略：
     (1)设备名称为空：该产品下所有设备都会被告警；
     (2)选择一个或者多个设备只针对选择的设备告警
     2、设备信息--查看--告警策略：
     (1)产品名称、设备名称显示置灰，不可编辑
     (2)所选设备个数大于1时，编辑或删除告警给出提示：有其他设备绑定了该告警，如果删除会连同一起被删除，如果不删除，请在此新建针对该设备的告警，如果该设备不需要该告警，请到告警管理--告警策略中找到该告警，编辑设备名称取消选择该设备
     */
    tipsBeforeSubmission(mainData) {
      let that = this
      if (!mainData.sceneId) {
        mainData.sceneId = ''
      }
      mainData.effectiveDate = '[' + mainData.effectiveDate + ']' + ''
      let devList = mainData.deviceIdList
      //告警管理--告警策略
      if (that.showFlag) {
        /*if (!devList || devList.length == 0) { //若未选择设备，传产品productId
          mainData.productId = that.lastProductId
        } else { //若选择了设备，不传产品productId
          mainData.productId = ''
        }*/
        that.setFieldsValue(mainData)
        that.submitData(mainData)
      }
      //设备信息--查看--告警策略
      else {
        //新增
        if (!that.model.id) {
          that.setFieldsValue(mainData)
          that.submitData(mainData)
        }
        //编辑
        else {
          if (!devList || devList.length == 0 || devList.length > 1) {
            that.$confirm({
              title: '确认提交',
              type: 'warning',
              okText: '确定',
              cancelText: '取消',
              content: '有其他设备绑定了此告警，确认后此告警只会绑定当前设备,确认提交吗？',
              onOk: function () {
                that.model.deviceIdList = []
                that.model.deviceIdList.push(that.deviceInfo.id)
                let mData = JSON.parse(JSON.stringify(that.model))
                mData.productId = ''
                mData.effectiveDate = '[' + mData.effectiveDate + ']' + ''
                delete mData.pushRulesArr
                that.setFieldsValue(mData)
                that.submitData(mData)
              },
            })
          } else {
            mainData.productId = ''
            that.setFieldsValue(mainData)
            that.submitData(mainData)
          }
        }
      }
    },
    /*检查每项规则是否至少选中一种规则*/
    hasAlarmContent() {
      let that = this
      for (let i = 0; i < that.model.alarmRules.length; i++) {
        let temp = that.model.alarmRules[i]
        let status = temp.contentArr.every((item) => {
          return item.checked == false
        })
        if (status) {
          return false
        }
      }
      return true
    },
    /*检查规则之间告警设置是否有重复的*/
    checkAlarmRepeatability() {
      let that = this
      // 分组的函数
      const grouped = {}
      that.model.alarmRules.forEach((item) => {
        // 生成唯一的键
        const key = `${item.subjectIndexArr.join('-')}-${item.conditions}-${item.specifyKey}-${item.specifyValue}`

        if (!grouped[key]) {
          grouped[key] = []
        }
        // 将 item 添加到对应的分组中
        grouped[key].push(item)
      })

      // 处理分组，删除长度小于等于1的分组
      for (const key in grouped) {
        if (grouped.hasOwnProperty(key)) {
          if (grouped[key].length <= 1) {
            delete grouped[key] // 删除该分组
          }
        }
      }
      for (const key in grouped) {
        let newGrouped = {}
        for (let i = 0; i < grouped[key].length; i++) {
          let m = grouped[key][i].contentArr

          for (let k = 0; k < m.length; k++) {
            let t = m[k]
            // 生成唯一的键
            if (t.checked) {
              const keyValue = `${t.title}-${t.level}-${t.value}-${t.originUnit}-${t.times}-${t.changeTimes}`
              if (!newGrouped[keyValue]) {
                newGrouped[keyValue] = []
              }
              newGrouped[keyValue].push(t)
            }
          }
        }
        for (let k in newGrouped) {
          if (newGrouped[k].length > 1) {
            return false
          }
        }
      }
      return true
    },
    /*打开了消息通知配置，判断是否有告警通知配置*/
    checkAlarmNoticeIsNull() {
      let isChecked = false
      if (this.model.pushRulesArr && this.model.pushRulesArr.length > 0) {
        for (let i = 0; i < this.model.pushRulesArr.length; i++) {
          let m = this.model.pushRulesArr[i]
          if (m.checked) {
            isChecked = true
            break
          }
        }
        return isChecked
      }
    },
    /*打开了消息通知配置，且有了告警通知配置，判断已选告警通知配置是否是已选的告警条件*/
    checkAlarmNoticeIsExist() {
      let tip = ''
      let alarmNotice = this.model.pushRulesArr.filter((item) => item.checked === true).map((item) => item.level)
      let contentArr = this.model.alarmRules.map((item) => item.contentArr)
      let cArr = []
      for (let i = 0; i < contentArr.length; i++) {
        let m = contentArr[i].filter((item) => item.checked === true).map((item) => item.level)
        if (m && m.length > 0) {
          m.map((level) => {
            if (!cArr.includes(level)) {
              cArr.push(level)
            }
          })
        }
      }

      for (let i = 0; i < alarmNotice.length; i++) {
        let an = alarmNotice[i]
        if (!cArr.includes(an)) {
          tip = '请不要对未勾选的告警条件设置消息通知。'
          break
        }
      }
      return tip
    },
    /*整理需要提交的规则数据*/
    createRuleObject() {
      let that = this
      let tempAlarmRules = []
      for (let k = 0; k < that.model.alarmRules.length; k++) {
        let ruleItem = that.model.alarmRules[k]
        let tempContentArr = []
        for (let i = 0; i < ruleItem.contentArr.length; i++) {
          let item = ruleItem.contentArr[i]
          if (item.checked) {
            let obj = {
              title: item.title,
              level: item.level,
              value: item.targetUnit && item.originStep ? item.targetValue : item.value,
              oldValue: item.value,
              unit: item.originUnit,
              times: item.times,
              changeTimes: item.changeTimes,
              noticeTemplateIds: that.model.pushRulesArr[i].noticeTemplateIds,
            }
            tempContentArr.push(obj)
          }
        }
        let newRuleItem = {
          subjectIndex: ruleItem.subjectIndexArr.join(','),
          conditions: ruleItem.conditions,
          specifyKey: ruleItem.specifyKey ? ruleItem.specifyKey : '',
          specifyValue: ruleItem.specifyValue,
          contents: JSON.stringify(tempContentArr),
          metadataId: ruleItem.metadataId,
          alarmTemplateId: ruleItem.alarmTemplateId,
          id: ruleItem.id,
        }
        tempAlarmRules.push(newRuleItem)
      }
      return tempAlarmRules
    },
    /*整理需要提交的推送规则数据*/
    createPushRuleObject() {
      let that = this
      let tempAlarmRules = []
      if (that.model.noticeStatus) {
        for (let k = 0; k < that.model.pushRulesArr.length; k++) {
          let item = that.model.pushRulesArr[k]
          if (item.checked) {
            let obj = {
              title: item.title,
              level: item.level,
              noticeVisitsTimes: item.noticeVisitsTimes,
              noticeIntervalTimes: item.noticeIntervalTimes,
              noticeTemplateIds: item.noticeTemplateIds,
              noticeTemplateNames: item.noticeTemplateNames,
            }
            tempAlarmRules.push(obj)
          }
        }
      }
      return JSON.stringify(tempAlarmRules)
    },
    /* 将消息推送数据处理成后端要求的值*/
    setFieldsValue(mainData) {
      let that = this
      mainData.remark = that.setValue(mainData.remark)
      mainData.silentTime = that.setValue(mainData.silentTime)
      mainData.noAlarmNoticeId = that.setValue(mainData.noAlarmNoticeId)
      mainData.noticeTemplateName = that.setValue(mainData.noticeTemplateName)
    },
    setValue(value) {
      return value != null && value != undefined ? value : ''
    },
    /*提交数据*/
    submitData(mainData) {
      let that = this
      that.confirmLoading = true
      let httpurl = ''
      let method = ''
      if (!that.model.id) {
        httpurl += that.url.add
        method = 'post'
      } else {
        httpurl += that.url.edit
        method = 'put'
      }
      let formData = { ...mainData }
      formData.productId = that.model.productId[that.model.productId.length - 1]
      httpAction(httpurl, formData, method)
        .then((res) => {
          if (res.success) {
            that.$message.success(res.message)
            that.$emit('ok')
          } else {
            that.$message.warning(res.message)
          }
          that.confirmLoading = false
        })
        .catch((res) => {
          that.$message.warning(res.message)
          that.confirmLoading = false
        })
    },
    //****************产品名称***********************
    /**
     * 选择产品名称
     * @param value
     * @param nodeData
     */
    onChangeProduct(value, nodeData) {
      if (nodeData && nodeData.length > 0) {
        this.lastProductType = nodeData[nodeData.length - 1].type
        let proId = nodeData[nodeData.length - 1].id
        if (this.lastProductType == 'product') {
          //最新产品id和上次选择产品id不同时，重新加载数据
          if (proId != this.lastProductId) {
            //产品选择发生变化
            //1、重新获取设备名称下拉数据
            this.resetDeviceName()
            this.getDeviceList(proId)
            //2、重新获取指标数据
            this.resetAlarmRules()
            this.getSubjectIndex(proId).then((res) => {
              this.subjectIndexList = res.data
            })
          }
        } else {
          this.resetDeviceName()
          this.resetAlarmRules()
        }
        this.lastProductId = proId
      } else {
        this.lastProductType = ''
        this.lastProductId = ''
        this.resetDeviceName()
        this.resetAlarmRules()
      }
    },
    /* 改变产品名称，联动改变设备名称*/
    resetDeviceName() {
      this.deviceList = []
      this.model.deviceIdList = undefined
    },
    /*改变产品名称后，联动改变指标和条件*/
    resetAlarmRules() {
      this.subjectIndexList = []
      this.model.alarmRules.map((item, index) => {
        item.subjectIndexArr = []
        item.conditionsList = []
        item.conditions = undefined
        item.specifyKey = undefined
        item.specifyKeyList = []
        item.specifyValue = ''
        item.metadataId = ''
      })
    },
    /*异步加载产品名称分支数据*/
    async loadData(selectedOptions) {
      let that = this
      return new Promise(function (resolve, reject) {
        const targetOption = selectedOptions[selectedOptions.length - 1]
        if (targetOption.type == 'category') {
          targetOption.loading = true
          that.getProductSublevelData(targetOption).then((res) => {
            if (res.success) {
              resolve(res)
            } else {
              that.$message.warning('发生异常，请稍后重试')
              reject(res)
            }
            targetOption.loading = false
          })
        } else {
          targetOption.loading = false
          targetOption.isLeaf = true
          targetOption.items = undefined
          resolve({
            data: targetOption,
            success: true,
            message: '操作成功',
          })
        }
      })
    },
    /* 根据当前所选产品名称id，获取该级下的子级数据*/
    getProductSublevelData(targetOption) {
      let that = this
      return new Promise(function (resolve, reject) {
        getAction(that.url.productSublevel, { assetsId: targetOption.id })
          .then((res) => {
            if (res.success) {
              if (res.result.length > 0) {
                targetOption.items = res.result.map((item) => {
                  let param = {
                    id: item.id,
                    isLeaf: item.type == 'category' ? false : true,
                    label: that.getIconName(item.type, item.code),
                    loading: false,
                    items: undefined,
                    code: item.code,
                    type: item.type,
                  }
                  return param
                })
                // that.productOptions = [...that.productOptions]
              } else {
                targetOption.items = undefined
                targetOption.isLeaf = true
              }
              targetOption.loading = false
              resolve({
                data: targetOption,
                success: true,
                message: '操作成功',
              })
            }
          })
          .catch(() => {
            targetOption.loading = false
            reject({
              data: targetOption,
              success: false,
              message: '操作失败',
            })
          })
      })
    },
    /*编辑：根据产品级联id数组，获取对应分支的子级数据，用于回显产品名称*/
    getProductName(nodeIdsArr, items) {
      let that = this
      let node = null
      if (items && items.length > 0) {
        node = items.filter((item) => {
          if (item.id == nodeIdsArr[0]) {
            return item
          }
        })
      }
      if (node && node.length > 0) {
        that.loadData(node).then((res) => {
          if (res.success) {
            node.items = res.data.items
            if (node.items) {
              that.getProductName(nodeIdsArr.slice(1), node.items)
            }
          }
        })
      }
    },
    /*
     * 选择产品名称后，获取输入框中的显示数据
     * @param selectedOptions
     * @returns {*|undefined}
     */
    productDisplayRender(selectedOptions) {
      let options = selectedOptions.map((item) => {
        return item.code
      })
      return options.length > 0 ? options.join('/') : undefined
    },
    /**
     * 校验产品名称选择是否正确
     * @param rule
     * @param value
     * @param callback
     * 不能选择到产品分类，只能选择到产品
     */
    productIdValidate(rule, value, callback) {
      let that = this
      if (rule.required) {
        if (value && value.length > 0) {
          if (that.lastProductType == 'product') {
            callback()
          } else if (that.lastProductType == 'category') {
            callback('勿选择产品分类，请选择产品')
          } else {
            callback()
          }
        } else {
          callback('请选择产品')
        }
      } else {
        callback()
      }
    },
    /*根据产品id，获取设备下拉数据*/
    getDeviceList(productId) {
      let that = this
      getAction(that.url.devList, { productId: productId.toString() }).then((res) => {
        if (res.success) {
          that.deviceList = res.result
        }
      })
    },
    /*根据产品id，获取对应指标下拉数据*/
    getSubjectIndex(id) {
      let that = this
      return new Promise(function (resolve, reject) {
        getAction(that.url.subjectIndex, { id: id }).then((res) => {
          if (res.success) {
            if (res.result.length <= 0) {
              reject({
                data: undefined,
                success: false,
                message: '当前产品关联的告警指标不存在！',
              })
            }
            let subjectIndexList = res.result
            that.setSubjectIndexList(subjectIndexList)
            resolve({
              data: subjectIndexList,
              success: true,
              message: res.message,
            })
          } else {
            reject({
              data: undefined,
              success: false,
              message: res.message,
            })
          }
        })
      })
    },
    /* 递归处理指标下拉数据的子级*/
    setSubjectIndexList(list) {
      let that = this
      for (let i = 0; i < list.length; i++) {
        let param = {
          code: list[i].code,
          name: list[i].name,
          id: list[i].id,
          dataType: list[i].dataType,
          originStep: list[i].originStep,
          originUnit: list[i].unit,
          proertyMetadataList: list[i].proertyMetadataList,
        }
        list[i] = param
        let child = param.proertyMetadataList
        if (child == null || child == '' || child.length == 0) {
          list[i].proertyMetadataList = undefined
        } else if (child.length > 0) {
          that.setSubjectIndexList(child)
        }
      }
    },
    /*编辑：通过告警策略列表id，获取规则初始化数据*/
    getRulesData(alarmId) {
      var that = this
      that.model.alarmRules = []
      if (alarmId != undefined && alarmId != null && alarmId != '') {
        getAction(that.url.rule, { id: alarmId }).then((res) => {
          if (res.success) {
            let rules = res.result
            for (let i = 0; i < rules.length; i++) {
              let subjectIndexArr = rules[i].subjectIndex.split(',')
              rules[i].conditionsList = []
              rules[i].specifyKeyList = []
              let contentArr = that.getContentTitleByLevel(JSON.parse(rules[i].contents))
              let specifyKey = rules[i].specifyKey
              specifyKey = specifyKey == undefined || specifyKey == null || specifyKey == '' ? undefined : specifyKey
              let item = {
                key: i,
                title: that.tabTitle + (i + 1),
                closable: true,
                subjectIndexArr: subjectIndexArr,
                conditions: rules[i].conditions,
                conditionsList: rules[i].conditionsList,
                specifyKey: specifyKey,
                specifyKeyList: rules[i].specifyKeyList,
                specifyValue: rules[i].specifyValue,
                contentArr: contentArr,
                metadataId: rules[i].metadataId,
                alarmTemplateId: rules[i].alarmTemplateId,
                id: rules[i].id,
                levelValueComType: 'default',
                levelValueUnitList: [],
              }
              that.model.alarmRules.push(item)
              that.getListByLeafSubjectIndex(subjectIndexArr, that.subjectIndexList, that.model.alarmRules[i])
            }
            if (that.model.alarmRules.length == 1) {
              that.model.alarmRules[0].closable = false
            }
            that.newTabIndex = that.model.alarmRules.length
            that.activeKey = that.model.alarmRules.length - 1
            that.confirmLoading = false
          }
          if (res.code === 510) {
            that.$message.warning(res.message)
          }
        })
      }
    },
    /*编辑：通过对比level，整合告警级别数组*/
    getContentTitleByLevel(list) {
      let that = this
      let newList = []
      for (let i = 0; i < that.contents.length; i++) {
        let isContinue = true
        let item = { title: that.contents[i].title, level: that.contents[i].level }
        for (let k = 0; k < list.length; k++) {
          if (item.level == list[k].level) {
            item.value = list[k].oldValue + ''
            item.times = list[k].times
            item.changeTimes = list[k].changeTimes
            item.checked = true
            item.originUnit = list[k].unit
            item.targetUnit = ''
            item.targetValue = list[k].value + ''
            item.originStep = ''
            newList.push(item)
            isContinue = false
            break
          }
        }
        if (!isContinue) {
          continue
        } else {
          item.value = ''
          item.times = ''
          item.changeTimes = ''
          item.checked = false
          item.originUnit = undefined
          item.targetUnit = ''
          item.targetValue = ''
          item.originStep = ''
          newList.push(item)
        }
      }
      return newList
    },
    /*编辑：通过对比level，整合消息推送设置中告警设置*/
    getPushRulesByLevel(pushRulesJson) {
      let that = this
      if (pushRulesJson) {
        let recordRules = JSON.parse(pushRulesJson)
        if (recordRules.length > 0) {
          that.model.pushRulesArr.map((item) => {
            recordRules.map((member) => {
              if (item.level == member.level) {
                item.checked = true
                item.noticeVisitsTimes = member.noticeVisitsTimes
                item.noticeIntervalTimes = member.noticeIntervalTimes
                item.noticeTemplateIds = member.noticeTemplateIds
                item.noticeTemplateNames = member.noticeTemplateNames
              }
            })
          })
        }
      }
    },
    //*************************规则*********************
    onEdit(targetKey, action) {
      if (action === 'add') {
        this.add()
      } else if (action === 'remove') {
        this.remove(targetKey)
      }
    },
    /*添加规则 */
    add() {
      let that = this
      const alarmRules = that.model.alarmRules
      //const activeKey = ++that.newTabIndex
      const activeKey = `${this.newTabIndex++}`
      alarmRules.push({
        title: that.tabTitle + (alarmRules.length + 1),
        contentArr: that.addAlarmContentFeilds(),
        key: activeKey,
        subjectIndexArr: undefined,
        conditions: undefined,
        specifyKey: undefined,
        specifyKeyList: [],
        specifyValue: '',
        conditionsList: [],
        closable: true,
        levelValueComType: 'default',
        levelValueUnitList: [],
        alarmTemplateId: !!that.model.id ? that.model.id : '',
        id: '',
      })

      alarmRules[0].closable = alarmRules.length === 1 ? false : true
      that.activeKey = activeKey
    },
    /*告警级别添加属性*/
    addAlarmContentFeilds() {
      let that = this
      let tempContents = []
      for (let i = 0; i < that.contents.length; i++) {
        let param = JSON.parse(JSON.stringify(that.contents[i]))
        param.times = ''
        param.changeTimes = ''
        param.value = ''
        param.checked = false
        param.targetValue = ''
        param.originStep = ''
        param.targetUnit = ''
        param.originUnit = undefined
        tempContents.push(param)
      }
      return tempContents
    },
    /*删除规则*/
    remove(targetKey) {
      let activeKey = this.activeKey
      let lastIndex = ''
      this.model.alarmRules.forEach((pane, i) => {
        if (pane.key === targetKey) {
          lastIndex = i - 1
        }
      })
      let rules = this.model.alarmRules.filter((pane) => pane.key != targetKey)
      for (let i = 0; i < rules.length; i++) {
        rules[i].title = this.tabTitle + (i + 1)
      }
      if (rules.length === 1) {
        rules[0].closable = false
      }
      if (rules.length && activeKey == targetKey) {
        if (lastIndex >= 0) {
          activeKey = rules[lastIndex].key
        } else {
          activeKey = rules[0].key
        }
      } else {
        //当activeKey不等于targetKey时，删除不知道为何tabs不刷新,只能改变key值，强制刷新
      }
      this.model.alarmRules = rules
      this.activeKey = activeKey
    },
    /**
     * 选择指标
     * 数据来源：该数据来自于产品管理--物模型--属性定义--数据列表
     * 操作条件：只有在属性定义中，该数据条的告警指标为true时，在此处才能选中该指标，同时加载条件数据，反之不能选中，不加载条件数据     *
     */
    changeSubjectIndex(codeArr, selectedOptions, index) {
      let that = this
      let perRule = that.model.alarmRules[index]
      perRule.subjectIndexArr = codeArr.length > 0 ? codeArr : undefined
      perRule.conditions = undefined
      perRule.conditionsList = []
      perRule.specifyKey = undefined
      perRule.specifyKeyList = []
      perRule.specifyValue = ''
      perRule.metadataId = ''
      perRule.levelValueComType = 'default'
      perRule.levelValueUnitList = []
      for (let i = 0; i < perRule.contentArr.length; i++) {
        perRule.contentArr[i].value = ''
        perRule.contentArr[i].times = ''
        perRule.contentArr[i].changeTimes = ''
        perRule.contentArr[i].checked = false
        perRule.contentArr[i].targetUnit = ''
        perRule.contentArr[i].originStep = ''
        perRule.contentArr[i].originUnit = undefined
        perRule.contentArr[i].targetValue = '' //记录根据当前输入值，请求接口计算得到的转换值
      }

      if (codeArr.length > 0) {
        perRule.metadataId = selectedOptions[selectedOptions.length - 1].id
        that.getListByLeafSubjectIndex(codeArr, that.subjectIndexList, that.model.alarmRules[index])
      }
    },
    /**
     * 校验指标：必须选择到叶子节点
     * @param rule
     * @param value
     * @param callback
     */
    subjectIndexValidate(rule, value, callback) {
      if (rule.required) {
        if (value && value.length > 0) {
          let startIndex = rule.fullField.indexOf('[')
          let endIndex = rule.fullField.indexOf(']')
          let index = rule.fullField.slice(startIndex + 1, endIndex)
          let tips = this.selectLeafSubjectIndex(value, this.subjectIndexList)
          if (tips) {
            callback(tips)
          } else {
            callback()
          }
        } else {
          callback('请选择指标')
        }
      }
    },
    selectLeafSubjectIndex(value, subjectIndexList) {
      let that = this
      let code = value[0]
      for (let i = 0; i < subjectIndexList.length; i++) {
        let temp = subjectIndexList[i]
        if (code == temp.code) {
          if (!temp.proertyMetadataList) {
            return ''
          } else {
            if (value.length > 1) {
              that.selectLeafSubjectIndex(value.slice(1), temp.proertyMetadataList)
            } else {
              return '请选择到叶子节点'
            }
          }
        }
      }
    },
    /*通过叶子节点指标获得条件、子级指标匹配键下拉数据*/
    getListByLeafSubjectIndex(codeArr, list, rules, pNodeData) {
      let that = this
      let code = codeArr[0]
      for (let i = 0; i < list.length; i++) {
        let temp = list[i]
        if (code == temp.code) {
          if (!temp.proertyMetadataList) {
            that.getConditionListByLeafSubjectIndex(temp, rules.conditionsList)
            // 根据是否有进制和单位配置，初始化阈值所用组件及单位
            rules.levelValueComType =
              that.getNotEmpty(temp.originStep) && that.getNotEmpty(temp.originUnit) ? 'choiceUnitCom' : 'default'
            if (rules.levelValueComType == 'choiceUnitCom') {
              rules.levelValueUnitList = that.getUnitList(temp.originUnit)
              let temUnitList = JSON.parse(JSON.stringify(rules.levelValueUnitList))
              temUnitList = temUnitList.map((item) => {
                return item.toUpperCase().trim()
              })
              for (let i = 0; i < rules.contentArr.length; i++) {
                rules.contentArr[i].targetUnit = temp.originUnit //记录初始单位
                //所得后端的单位大小写法可能和前端不一样，所以统一转大写，找到对应单位
                if (rules.contentArr[i].originUnit && rules.contentArr[i].checked) {
                  let oldUnit = rules.contentArr[i].originUnit.toUpperCase().trim()
                  for (let j = 0; j < temUnitList.length; j++) {
                    if (oldUnit === temUnitList[j]) {
                      rules.contentArr[i].originUnit = rules.levelValueUnitList[j]
                      break
                    }
                  }
                }
                // 告警级别处于未选定状态，阈值不能编辑，需给单位赋一个初始值
                else {
                  rules.contentArr[i].originUnit = rules.levelValueUnitList[0]
                }
                rules.contentArr[i].originStep = temp.originStep //记录转换进制
              }
            }
            //父级的dataType是array时加载子级指标匹配键的下拉数据
            if (pNodeData && pNodeData.dataType === 'array') {
              that.getSpecifyKeyListByLeafSubjectIndex(pNodeData.id).then((res) => {
                rules.specifyKeyList = res.data
              })
            }
            return
          } else {
            if (codeArr.length > 1) {
              let arr = codeArr.slice(1)
              let currNodeData = {
                dataType: list[i].dataType,
                id: list[i].id,
                code: list[i].code,
                name: list[i].name,
                originStep: list[i].originStep,
                originUnit: list[i].unit,
              }
              that.getListByLeafSubjectIndex(arr, temp.proertyMetadataList, rules, currNodeData)
            } else {
              return
            }
          }
        }
      }
    },

    getNotEmpty(text) {
      if (text != null && text != '' && text != undefined) {
        return true
      } else {
        return false
      }
    },
    getConditionListByLeafSubjectIndex(subject, conList) {
      let that = this
      let type = subject.dataType
      that.conditionDicts.map((item) => {
        if (type === 'object' || type === 'array') {
          if (item.value === 'contain' || item.value === 'notContain') {
            conList.push(item)
          }
        } else if (type === 'text') {
          if (item.value !== '>' && item.value !== '<') {
            conList.push(item)
          }
        } else if (type === 'int' || type === 'long' || type === 'float' || type === 'double') {
          if (item.value !== 'contain' && item.value !== 'notContain') {
            conList.push(item)
          }
        } else {
          conList.push(item)
        }
      })
    },
    /*根据父级指标code，获取子级所有指标*/
    getSpecifyKeyListByLeafSubjectIndex(id) {
      let that = this
      return new Promise(function (resolve, reject) {
        getAction(that.url.childrenSubjectIndex, { id: id }).then((res) => {
          let specifyKeyList = []
          if (res.success) {
            if (res.result.length > 0) {
              res.result.map((item) => {
                let param = {
                  id: item.id,
                  code: item.code,
                  name: item.name,
                  dataType: item.dataType,
                  originStep: item.originStep,
                  originUnit: item.unit,
                }
                specifyKeyList.push(param)
              })
            }
            resolve({
              success: true,
              data: specifyKeyList,
              message: '操作成功',
            })
          } else {
            reject({
              success: false,
              data: specifyKeyList,
              message: '操作失败',
            })
          }
        })
      })
    },
    /*改变告警级别选择状态，联动改变阈值和连续出现次数*/
    changeContentRulesCheckbox(e, item, index, idx) {
      if (!e.target.checked) {
        item.value = ''
        item.times = ''
        item.changeTimes = ''
      }
    },
    setAlarmLevelName(title, num) {
      let str = title
      if (title.length > num) {
        str = title.substr(0, num) + '...'
      }
      return str
    },
    /*   alarmLevelValidate(rule, value, callback) {
         let that = this
        let val= value.toString()
         if(rule.required){
           if (val && val.length > 0) {
             if (value <=0) {
               callback('阈值应是≥1的整数');
             }
             let fieldArr=rule.fullField.split('.')
             let index_1= that.getAlarmIndex(fieldArr[0])
             let index_2= that.getAlarmIndex(fieldArr[1])
             let hasArr=that.model.alarmRules[index_1].contentArr
             let noHasArr=hasArr.filter((item,index)=>{
                return index!=index_2&&item.checked?true:false
             })
             let tips=''
             if(noHasArr.length>0){
               for(let i=0;i<noHasArr.length;i++) {
                 let item = noHasArr[i]
                if(hasArr[index_2].value == item.value){
                   tips='阈值不能相等'
                   break
                 }
                else if (hasArr[index_2].level < item.level) {
                   if (hasArr[index_2].value > item.value) {
                     tips='级别低的阈值应<级别高的阈值'
                     break
                   }
                 } else if(hasArr[index_2].level > item.level){
                   if (hasArr[index_2].value < item.value) {
                     tips='级别高的阈值应>级别低的阈值'
                     break
                   }
                 }
               }
               if(tips){
                 callback(tips);
               }
               else {
                 callback();
               }
             }
             else{
               callback();
             }
           }
           else {
             callback(' 请输入阈值!')
           }
         }else {
           callback()
         }
       },

    getAlarmIndex(field) {
      let startIndex = field.indexOf('[') + 1
      let num = field.substr(startIndex, field.length - 1 - startIndex)
      return Number(num)
    },*/
    /*改变阈值*/
    calculateTargetValue(levelItem) {
      if (
        levelItem.value.length < 1 ||
        !!isNaN(levelItem.value) ||
        levelItem.value[levelItem.value.length - 1] == '.'
      ) {
        // this.$message.warning('请输入纯数字')
        return
      }
      getAction(this.url.getValueByUnitConvert, {
        step: levelItem.originStep,
        originUnit: levelItem.originUnit,
        origin: levelItem.value,
        targetUnit: levelItem.targetUnit,
      })
        .then((res) => {
          if (res.success) {
            levelItem.targetValue = res.result
          } else {
            this.$message.warning(res.message)
          }
        })
        .catch((err) => {
          this.$message.warning(err.message)
        })
    },
    /*校验阈值*/
    validateLevelValue(rule, value, callback) {
      if (rule.required) {
        if (!value || value.length < 1) {
          callback('请输入阈值')
        } else if (!!isNaN(value) || value[value.length - 1] == '.') {
          callback('请输入数字')
        } else if (value.length > rule.max || value.length < rule.min) {
          callback(`阈值长度应在 ${rule.min}-${rule.max} 之间`)
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    /**校验非必填项*/
    ValidateOptionalRuleFields(rule, value, callback,displayName,max=255,min){
      ValidateOptionalFields(rule, value, callback,displayName,max)
    },
    validateDynamicRuleValue(rule, value, callback,displayName,max=255,min) {
      if (rule.required) {
        if (!value) {
          callback(`请输入${displayName}!`)
        }
        const trimmedValue = value.trim()
        if (trimmedValue==='') {
          callback(`${displayName}不能全为空白字符`)
        }
        if (value !== trimmedValue) {
          callback(`${displayName}首尾不能包含空白字符！`)
        }

        if (min && max && (value.length > max || value.length < min)) {
          callback(`${displayName}长度应在 ${min}-${max} 个字符之间！`)
        } else if (max && value.length > max) {
          callback(`${displayName}长度不能超出 ${max} 个字符！`)
        } else if (min && value.length < min) {
          callback(`${displayName}长度不能少于 ${min} 个字符！`)
        }
        callback()
      }else {
        ValidateOptionalFields(rule, value, callback,displayName,max)
      }
    },
    //*************************消息推送设置*********************
    changeNoticeStatus() {
      let that = this
      //that.resetPushRules(that.model.pushRulesArr)
      that.model.effectiveDate = ['2', '3', '4', '5', '6', '7', '1']
      that.model.beginTime = moment().startOf('day').format('HH:mm')
      that.model.endTime = moment().endOf('day').format('HH:mm')
      that.model.silentTime = ''
      that.model.noAlarmNotice = false
      that.model.noAlarmNoticeId = undefined
    },
    resetPushRules(pushRulesArr) {
      let that = this
      let rules = pushRulesArr
      if (rules.length > 0) {
        rules.map((item) => {
          item.noticeVisitsTimes = 0
          item.noticeIntervalTimes = -1
          item.noticeTemplateIds = []
          item.noticeTemplateNames = ''
          item.checked = false
        })
        that.model.pushRulesArr = rules
      }
    },
    changePushRulesCheckbox(e, item) {
      if (!e.target.checked) {
        item.noticeVisitsTimes = 0
        item.noticeIntervalTimes = -1
        item.noticeTemplateIds = []
        item.noticeTemplateNames = ''
      }
    },
    changeLevelNoticeTemplate(e, index) {
      let that = this
      let names = ''
      if (e) {
        for (let i = 0; i < e.length; i++) {
          let param = that.noticeTemplateList.filter((item) => {
            if (item.type == e[i]) {
              return true
            }
          })
          names += param[0].code + ','
        }
      }
      names = names ? names.substr(0, names.length - 1) : ''
      that.model.pushRulesArr[index].noticeTemplateNames = names
    },
    beginTimeValidate(rule, value, callback) {
      let that = this
      if (value && value.length > 0) {
        let bTime = this.calcTime(value)
        if (this.model.endTime) {
          let eTime = this.calcTime(this.model.endTime)
          if (eTime - bTime > 0) {
            callback()
          } else {
            callback('开始时间应小于结束时间')
          }
        } else {
          callback()
        }
      } else {
        callback('请选择开始时间')
      }
    },
    endTimeValidate(rule, value, callback) {
      let that = this
      if (value && value.length > 0) {
        let eTime = this.calcTime(value)
        if (this.model.beginTime) {
          let bTime = this.calcTime(this.model.beginTime)
          if (eTime - bTime > 0) {
            callback()
          } else {
            callback('结束时间应大于开始时间')
          }
        } else {
          callback()
        }
      } else {
        callback('请选择结束时间')
      }
    },
    calcTime(time) {
      let arr = time.split(':')
      // return arr[0] * 60 * 60 + arr[1] * 60 + arr[2]
      return arr[0] * 60 * 60 + arr[1] * 60
    },
    changeBeginTime() {
      this.$refs.form.validateField('endTime')
    },
    changeEndTime() {
      this.$refs.form.validateField('beginTime')
    },
    ChangeNoAlarmNotice(e) {
      this.model.noAlarmNoticeId = undefined
    },

  },
}
</script>
<style lang="less" scoped>
@import '~@assets/less/common.less';

::v-deep .ant-row-flex > .ant-col {
  text-align: left !important;
}

::v-deep .ant-tabs.ant-tabs-card > .ant-tabs-bar .ant-tabs-tab-active {
  background: #fff;
  border-color: #e8e8e8 !important;
}

.colorBox {
  margin-bottom: 18px;
}

.colorTotal {
  padding-left: 7px;
  border-left: 4px solid #1e3674;
}

.group-box {
  margin-bottom: 10px;
  display: flex;
  justify-content: start;
  align-items: center;

  .dot {
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #1e3674;
    margin-right: 7px;
  }

  .group {
    margin-right: 8px;
  }
}

.ant-form-item-control {
  line-height: 0px;
}

/** Tab页面行间距 */
.ant-tabs-content .ant-form-item {
  margin-bottom: 5px;
}

::v-deep .notice .ant-form-item {
  margin-bottom: 5px;
}

.ant-row-flex > .ant-col {
  text-align: center;
}

::v-deep .ant-input-number {
  width: 100% !important;
}

.trigger-row {
  margin-bottom: 0px;
}

::v-deep .ant-form-explain {
  color: #f5222d;
  text-align: left;
}
</style>
