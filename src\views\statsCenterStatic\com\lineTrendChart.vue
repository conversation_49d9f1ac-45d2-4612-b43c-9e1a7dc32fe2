<template>
    <div ref="lineTrendChart"></div>
</template>
<script>
import echarts from 'echarts/lib/echarts'
import { heightPixel, widthPixel } from '@views/statsCenter/com/calculatePixel'
import fa from 'element-ui/src/locale/lang/fa'

export default {
  props: {
    xData: {
      type: Array,
      required: true
    },
    chartData: {
      type: Array,
      required: true
    },
    showLegend:{
      type: Boolean,
      required: false,
      default:true
    },
    //当showLegend为true时，必须给legendData传值
    legendData: {
      type: Array,
      required: false
    },
    colorList:{
      type: Array,
      required: true
    },
    unit:{
      type:String,
      required:false,
      default: ''
    },
    max:{
      type:Number,
      required:false,
      default: 100
    }
  },
  data() {
    return {
      myChart:null
    }
  },
  watch:{
    chartData:{
      handler(nVal,oVal){
        this.$nextTick(()=>{
          this.drawLineTrendChart()
        })
      },
      deep:true,
      immediate:true
    }
  },
  methods: {
    // 趋势分析折线图
    drawLineTrendChart() {
      let seriesOption = []
      let h25=heightPixel(25)
      let w2=widthPixel(2)
      let w5=widthPixel(5)
      let w9=widthPixel(9)
      let w10=widthPixel(10)
      let w14=widthPixel(14)
      let w12=widthPixel(12)
      let w20=widthPixel(20)
      let w22=widthPixel(22)
      let w24=widthPixel(32)

      this.chartData.forEach((ele, i) => {
        seriesOption.push({
          name: ele.name,
          type: 'line',
          symbol: 'circle',
          smooth: true,
          lineStyle: {
            normal: {
              width: w2,
              color: this.colorList[i].start, // 线条颜色
            },
            borderColor: this.colorList[i].start,
          },
          itemStyle: {
            color: this.colorList[i].start,
            borderColor: this.colorList[i].start,
            borderWidth: w2,
          },
          tooltip: {
            show: true,
          },
          areaStyle: {
            //区域填充样式
            normal: {
              //线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是‘true’，则该四个值是绝对像素位置。
              color: new echarts.graphic.LinearGradient(
                0,0,0,1,
                [{
                  offset: 0,
                  color: this.colorList[i].start,
                },
                  {
                    offset: 1,
                    color: this.colorList[i].end,
                  },
                ],
                false
              ),
              shadowColor: this.colorList[i].end, //阴影颜色
              shadowBlur: w20 //shadowBlur设图形阴影的模糊大小。配合shadowColor,shadowOffsetX/Y, 设置图形的阴影效果。
            },
          },
          data: ele.data,
        })
      })
      let myChart = this.$echarts.init(this.$refs.lineTrendChart)
       myChart.setOption({
         grid: {
           top: w20,
           left: '10%',
           right: w24,
           bottom: h25,
         },
        tooltip: {
          show: true,
          trigger: 'axis',
          transitionDuration: 0, //echart防止tooltip的抖动
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'line', // 默认为直线，可选为：'line' | 'shadow'
            lineStyle: {
              type: 'dotted',
              color: '#ffffff'
            }
          },
          formatter: (value)=>{
            let tips = value[0].axisValue + "</br>"
            let dataTxt = ''
            for (let i = 0; i < value.length; i++) {
              let vTxt=''
              if(value[i].value!=undefined&&value[i].value!=null&&value[i].value!=''){
                vTxt=`${value[i].seriesName} ${value[i].value}${this.unit}`
              }else {
                vTxt=`${value[i].seriesName} 无数据`
              }
              let txt =`<span style='text-align:left;font-size:${w14+"px"};height: ${w22+"px"};line-height: ${w22+"px"};display: inline-block'>
                          <span style='display: inline-block;width: ${w10+"px"};height: ${w10+"px"};background: ${this.colorList[i].start};border-radius: 100%;margin-right: ${w5+"px"}'>
                          </span>${vTxt}</span>`
              dataTxt+=(i != value.length - 1) ? txt+"</br>" : txt
            }
            return tips + dataTxt
          }
        },
        legend: {
          show:this.showLegend,
          left: 'right',
          data: this.legendData,
          icon: 'rect',
          color: '#fff',
          itemWidth: w9,
          itemHeight: w2,
          textStyle: {
            color: '#fff',
          },
        },
        xAxis: [{
          type: 'category',
          boundaryGap: false,
          axisLine: {
            //坐标轴轴线相关设置。数学上的x轴
            show: true,
            lineStyle: {
              color: '#1f313d',
            },
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#9FA5AD', //更改坐标轴文字颜色
              fontSize:w12,
              fontWeight: 300
            },
          },
          splitLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          data: this.xData,
        }],
        yAxis: [{
          nameTextStyle: {
            color: 'rgba(250,250,250,.6)',
            fontSize: w12,
            padding:w10 ,
          },
          min: 0,
          max:this.max,
          splitLine: {
            show: true,
            lineStyle: {
              color: ['#1c2a37'],
              width: w2,
              type: 'Dashed',
            }
          },
          axisLine: {
            show: false,
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#9FA5AD', //更改坐标轴文字颜色
              fontSize: w14,
              fontWeight: 400
            }
          },
          axisTick: {
            show: false
          }
        }],
        series: seriesOption,
      })
      window.addEventListener("resize", () => {
        myChart.resize();
      })
    }
  }
}
</script>

<style scoped lang="less">
</style>