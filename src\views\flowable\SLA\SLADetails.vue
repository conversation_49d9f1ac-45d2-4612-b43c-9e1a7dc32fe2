<template>
  <a-card :bordered='false'>
    <div class='action'>
      <span class='return'>
        <img src='~@/assets/return1.png' alt='' @click='getGo'>
      </span>
    </div>
    <a-descriptions :column='{ xxl: 2, xl: 2, lg: 2, md: 2, sm: 2, xs: 2 }' bordered>
      <a-descriptions-item label='所属流程'>{{ record.processName }}</a-descriptions-item>
      <a-descriptions-item label='流程编码'>{{ record.processKey }}</a-descriptions-item>
      <a-descriptions-item label='SLA类型'>{{ record.slaType_dictText }}</a-descriptions-item>
      <a-descriptions-item v-for='(item,index) in record.dynamicColumns' :key='"dynamicColumns_"+index' :label='item.title'>{{ record[item.dataIndex] }}</a-descriptions-item>
      <a-descriptions-item label='创建时间'>{{ record.createTime }}</a-descriptions-item>
    </a-descriptions>
    <adapter-task-modal ref='modalForm' @ok='modalFormOk'></adapter-task-modal>
  </a-card>
</template>
<script>

import adapterTaskModal from '@views/deviceConfig/adapterTaskManagement/modules/AdapterTaskModal.vue'
import { getAction } from '@api/manage'
export default {
  name: 'SLADetails',
  props: {
    data: {
      type: Object
    }
  },
  components:{adapterTaskModal},
  data() {
    return {
      record:{},
      url:{
        queryById:'/device/productJob/queryById'
      }
    }
  },

  watch:{
    data:{
      handler(nVal,oVal){
        this.record=JSON.parse(JSON.stringify(nVal))
      },
      deep:true,
      immediate: true,
    }
  },
  methods: {
    //返回上一级
    getGo() {
      this.$parent.pButton1(0)
    },
    doEdit(record) {
      this.$refs.modalForm.edit(record)
      this.$refs.modalForm.title = '编辑'
      this.$refs.modalForm.disableSubmit = false
    },
    modalFormOk(){
      getAction(this.url.queryById,{id:this.record.id}).then((res)=>{
        if(res.success){
          this.record=JSON.parse(JSON.stringify(res.result))
        }else {
          this.$message.warning(res.message)
        }
      }).catch((err)=>{
        this.$message.warning(err.message)
      })
    }
  }
}
</script>

<style scoped lang='less'>
.action {
  display: flex;
  justify-content: right;
  align-items: center;
  flex-flow: row nowrap;
  margin-bottom: 12px;

  .return {
    img {
      width: 20px;
      height: 20px;
      cursor: pointer
    }
  }
}

::v-deep .ant-descriptions-view {
  border-radius: 0px;
}

::v-deep .ant-descriptions-bordered .ant-descriptions-item-label {
  background-color: rgb(250, 250, 250);
  text-align: center;
  width: 17%;
}

::v-deep .ant-descriptions-item-label,
.ant-descriptions-item-content {
  color: rgb(96, 98, 102) !important;
}

::v-deep .ant-descriptions-bordered .ant-descriptions-item-content {
  word-break: break-word;
  white-space: normal;
}
</style>