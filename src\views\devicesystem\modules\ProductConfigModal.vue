<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    switchFullscreen
    :centered='true'
    :destroyOnClose='true'
    @ok="handleOk"
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @cancel="handleCancel"
    cancelText="关闭">
    <config-form :productInfo='productInfo' ref="realForm" @ok="submitCallback" :disabled="disableSubmit"></config-form>
  </j-modal>
</template>

<script>

import configForm from './ProductConfigForm.vue'
export default {
  name: 'ProductConfigModal',
  props:{
    productInfo:{}
  },
  components: {
    configForm
  },
  data () {
    return {
      title:'',
      width:'1000px',
      visible: false,
      disableSubmit: false
    }
  },
  methods: {
    add () {
      this.visible=true
      this.$nextTick(()=>{
        this.$refs.realForm.add();
      })
    },
    edit (record) {
      this.visible=true
      this.$nextTick(()=>{
        this.$refs.realForm.edit(record);
      })
    },
    close () {
      this.$emit('close');
      this.visible = false;
    },
    handleOk () {
      this.$refs.realForm.submitForm();
    },
    submitCallback(){
      this.$emit('ok');
      this.visible = false;
    },
    handleCancel () {
      this.close()
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/normalModal.less';
</style>