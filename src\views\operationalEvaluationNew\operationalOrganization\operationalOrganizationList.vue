<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class="card-style">
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="报告名称">
                  <a-input
                    :maxLength='maxLength'
                    :allowClear="true"
                    autocomplete="off"
                    v-model="queryParam.projectName"
                    placeholder="请输入报告名称"
                  />
                </a-form-item>
              </a-col>
<!--              <a-col :span="spanValue">
                <a-form-item label="关联指标">
                  <a-tree-select
                    v-model="queryParam.metricsId"
                    tree-node-filter-prop="title"
                    :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }"
                    :replaceFields="replaceFields"
                    :treeData="treeData"
                    style="width: 100%"
                    placeholder="请选择指标"
                    allow-clear
                    @change="onChangeMetricsTree">
                  </a-tree-select>
                </a-form-item>
              </a-col>-->

              <a-col :span="colBtnsSpan()">
                <span
                  class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                >
                  <a-button type="primary" class="btn-search btn-search-style" @click="searchQuery">查询</a-button>
                  <a-button class="btn-reset btn-reset-style" @click="searchReset">重置</a-button>
                  <a v-if="isVisible" class="btn-updown-style" @click="doToggleSearch">
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered="false" style="width: 100%; flex: auto">
        <a-table
          ref="table"
          bordered
          rowKey="id"
          :columns="columns"
          :dataSource="dataSource"
          :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
          :pagination="ipagination"
          :loading="loading"
          @change="handleTableChange"
        >
<!--        <a-table
          ref="table"
          bordered
          rowKey="id"
          :columns="columns"
          :dataSource="dataSource"
          :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
          :pagination="ipagination"
          :loading="loading"
          :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          @change="handleTableChange"
        >-->
          <span class="caozuo" slot="action" slot-scope="text, record">
            <a @click="handleDetailPage(record, '')">查看</a>
            <span v-if="record.status == '2'">
              <a-divider type="vertical" />
              <a-dropdown>
                <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
                <a-menu slot="overlay">
                  <a-menu-item v-if="record.status == '2'">
                    <a @click="openReportList(record)">生成报告</a>
                  </a-menu-item>
                <a-menu-item v-if="!!record.projectReportText" v-has="'report:edit'">
                  <a @click="openEdit(record)">编辑报告</a>
                </a-menu-item>
                <a-menu-item v-if="!!record.projectReportText">
                  <a @click="openPreview(record)">预览报告</a>
                </a-menu-item>
                <!-- <a-sub-menu title="导出报告" v-if="!!record.ProjectReportText"> -->
                  <a-menu-item v-if="!!record.projectReportText">
                    <a @click="handleEvaluateRport(record, 'word')">导出WORD</a>
                  </a-menu-item>
                  <a-menu-item v-if="!!record.projectReportText">
                    <a @click="handleEvaluateRport(record, 'pdf')">导出PDF</a>
                  </a-menu-item>
                <!-- </a-sub-menu> -->
                  <a-menu-item v-if="record.status == '2' && record.projectResult && record.projectResult.length > 0">
                  <!-- 确认本次报告下所有单位均已完成填报并已生成汇总评估报告 -->
                    <a v-if="!!record.projectReportText" @click="endTask(record)">结束</a>
                  </a-menu-item>
              </a-menu>
            </a-dropdown>
            </span>
          </span>
          <template slot="tooltip" slot-scope="text">
            <a-tooltip placement="topLeft" :title="text" trigger="hover">
              <div class="tooltip">
                {{ text }}
              </div>
            </a-tooltip>
          </template>
          <template slot="status" slot-scope="text, record">
            <a-tag class="tag" :color="statusConfig(record.status).color">
              {{ statusConfig(record.status).text }}
            </a-tag>
          </template>
          <template slot="evaluationCycle" slot-scope="text, record">
            {{ record.startTime }} - {{ record.endTime }}
          </template>
          <template slot="completionRate" slot-scope="text, record">
            <span v-if="record.completeDeptNum + record.unCompleteDeptNum > 0">
              {{ Math.round((record.completeDeptNum / (record.completeDeptNum + record.unCompleteDeptNum)) * 100).toFixed(0) }}%
            </span>
            <span v-else>0%</span>
          </template>
          <template slot="completeDeptNum" slot-scope="text, record">
            <span
              v-if="text > 0"
              @click="handleDetailPage(record, '2')"
              style="color: #409eff; cursor: pointer; font-weight: bold"
              >{{ text }}</span
            >
            <span v-else>0</span>
          </template>
          <template slot="unCompleteDeptNum" slot-scope="text, record">
            <span
              v-if="text > 0"
              @click="handleDetailPage(record, '1')"
              style="color: #409eff; cursor: pointer; font-weight: bold"
              >{{ text }}</span>
            <span v-else>0</span>
          </template>
        </a-table>
      </a-card>
      <templateListModal ref="templateListModal" @ok="modalFormOk"></templateListModal>
      <preview ref="preview" @ok="modalFormOk"> </preview>
      <openPreview ref="allPreview"> </openPreview>
    </a-col>
  </a-row>
</template>

<script>
import { downloadFile } from '@/api/manage'
import '@/assets/less/TableExpand.less'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { getAction, putAction } from '@/api/manage'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
import templateListModal from '@views/operationalEvaluationNew/operationalOrganization/modules/templateListModal.vue'
import preview from '@views/operationalEvaluationNew/operationalOrganization/modules/preview.vue'
import openPreview from '@views/operationalEvaluationNew/operationalOrganization/modules/openPreview.vue'
import { getStatusConfig } from '@/views/operationalEvaluationNew/modules/statusConfig'
export default {
  name: 'operationalOrganizationList',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components: {
    templateListModal,
    preview,
    openPreview
  },
  data() {
    return {
      maxLength: 50,
      disableMixinCreated:true,
      formItemLayout: {
        labelCol: {
          style: 'width:90px',
        },
        wrapperCol: {
          style: 'width:calc(100% - 90px)',
        },
      },
      // 表头
      columns: [
        {
          title: '报告名称',
          dataIndex: 'projectName'
        },
        {
          title: '关联单位',
          dataIndex: 'deptNameStr',
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 150px;max-width:400px'
            return {
              style: cellStyle,
            }
          },
          scopedSlots: {
            customRender: 'tooltip',
          }
        },
     /*   {
          title: '评估指标',
          dataIndex: 'metricsNameStr',
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
            return {
              style: cellStyle,
            }
          },
          scopedSlots: {
            customRender: 'tooltip',
          },
        },*/
        {
          title: '状态',
          dataIndex: 'status',
          scopedSlots: {
            customRender: 'status',
          },
        },
        {
          title: '评估周期',
          dataIndex: 'evaluationCycle',
          scopedSlots: {
            customRender: 'evaluationCycle',
          },
        },
        {
          title: '填报完成度',
          dataIndex: 'completionRate',
          scopedSlots: {
            customRender: 'completionRate',
          },
        },
        {
          title: '已填报单位数',
          dataIndex: 'completeDeptNum',
          scopedSlots: {
            customRender: 'completeDeptNum',
          },
        },
        {
          title: '未填报单位数',
          dataIndex: 'unCompleteDeptNum',
          scopedSlots: {
            customRender: 'unCompleteDeptNum',
          },
        },
        {
          title: '发起人',
          dataIndex: 'sender_dictText',
        },
        {
          title: '操作',
          dataIndex: 'action',
          fixed: 'right',
          width: 100,
          scopedSlots: {
            customRender: 'action',
          },
        },
      ],
      replaceFields: {
        children: 'children',
        title: 'title',
        key: 'id',
        value: 'id'
      },
      treeData: [],
      url: {
        list: '/devops/projectInfo/pageList',
        edit: '/devops/projectInfo/edit',
        getMetricsTree: '/evaluate/metricsType/treeNew'
      },
    }
  },
  created() {
    this.getMetricsTree()
  },
  activated() {
    this.loadData(1)
  },
  mounted() {},
  methods: {
    // 获取填报状态的颜色配置
    statusConfig(status) {
      return getStatusConfig(status)
    },
    handleDetailPage: function (record,type) {
      record.previewStatus=type
      this.$parent.pButton2(1, record)
    },
    // 打开生成报告列表
    openReportList(record) {
      this.$refs.templateListModal.show(record)
    },
    // 打开编辑报告弹窗
    openEdit(record) {
    this.$refs.preview.show(record)
    },
    // 打开预览报告弹窗
    openPreview(record) {
    this.$refs.allPreview.show(record)
    },
    getMetricsTree() {
      getAction(this.url.getMetricsTree).then((res) => {
        if (res.success) {
          // 遍历树数据并设置selectable属性
          const setSelectable = (nodes) => {
            return nodes.map(node => {
              node.selectable = node.type === 'metric';
              if (node.children) {
                node.children = setSelectable(node.children);
              }
              return node;
            });
          };
          this.treeData = setSelectable(res.result);
        }
      })
    },
    onChangeMetricsTree(e) {
      this.queryParam.metricsId = e
    },
    // 结束操作
    endTask(record) {
      var that = this
      that.$confirm({
        content: '确认结束任务',
        okText: '是',
        cancelText: '否',
        onOk: () => {
          putAction(that.url.edit, {
            ...record,
            status: '3',
          }).then((res) => {
            if (res.success) {
              //重新计算分页问题
              that.reCalculatePage(1)
              that.$message.success(res.message)
              that.loadData()
            } else {
              that.$message.warning(res.message)
            }
          })
        },
      })
    },
    //查看评估报告
    handleEvaluateRport(record, type) {
      const ext = type === 'word' ? 'docx' : 'pdf'
      let fileName = `${record.projectName}评估报告.${ext}`
      downloadFile('/devops/projectInfo/RichTextExportReport', fileName, {
        id: record.id,
        exportType: type,
      })
        .then((res) => {})
        .catch((err) => {
          this.$message.error('获取评估报告失败')
        })
    },
  },
}
</script>
<style lang="less" scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
</style>