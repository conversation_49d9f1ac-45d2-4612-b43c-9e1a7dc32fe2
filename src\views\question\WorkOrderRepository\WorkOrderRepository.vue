<template>
  <a-row :gutter="10">
    <a-col>
      <a-card :bordered="false">
        <!-- 查询区域 -->
        <div class="table-page-search-wrapper">
          <!-- 搜索区域 -->
          <a-form layout="inline">
            <a-row :gutter="24">
              <a-col :span="6">
                <a-form-item label="问题类型">
                   <a-select
                            placeholder="请选择"
                            @change="handleChange"
                            v-model="queryParam.questionType"
                            :allowClear="true">
                    <a-select-option
                      v-for="(item,index) in QuestionType"
                      :key="index"
                      :value="item.value">{{item.text}}</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="问题名称">
                  <a-input placeholder="请输入问题名称"
                           v-model="queryParam.name" />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <!-- 按钮区域 -->
                <a-button type="primary"
                          @click="searchQuery">查询</a-button>

                <a-button type="primary" @click="mySearchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              </a-col>
            </a-row>
          </a-form>
        </div>
        <!-- table区域 -->
        <!-- <a-button type="primary"
                  icon="plus"
                  style="margin-bottom: 8px"
                  @click="handleAdd">添加</a-button>
        <a-dropdown v-if="selectedRowKeys.length > 0">
          <a-menu slot="overlay" style='text-align: center'>
            <a-menu-item key="1" @click="batchDel">
              删除
            </a-menu-item>
          </a-menu>
          <a-button style="margin-left: 8px">
            批量操作
            <a-icon type="down" />
          </a-button>
        </a-dropdown> -->
        <div>
          <div class="ant-alert ant-alert-info"
               style="margin-bottom: 16px;">
            <i class="anticon anticon-info-circle ant-alert-icon"></i>已选择&nbsp;<a style="font-weight: 600">{{
              selectedRowKeys.length
            }}</a>项&nbsp;&nbsp;
            <a style="margin-left: 24px"
               @click="onClearSelected">清空</a>
          </div>
          <a-table :columns="columns"
                   rowKey="id"
                   bordered
                   :pagination="ipagination"
                   :dataSource="dataSource"
                   :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }">
            <template slot="enclosure" slot-scope="text">
            <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
            <a-button
              v-else
              :ghost="true"
              type="primary"
              icon="download"
              size="small"
              @click="downloadRowFile(text)">
              {{text}}
            </a-button>
            </template>
            <span slot="questionType" slot-scope="text, record">
              <span v-for="dict in QuestionType" v-if="dict.value==record.questionType">
                {{dict.text}}
              </span>
            </span>
            <span slot="createBy" slot-scope="text, record">
              <span v-for="dict in UserList" v-if="dict.loginname==record.createBy">
                {{dict.name}}
              </span>
            </span>
            <span slot="isCommon">
              <a-switch checkedChildren="开"
                        unCheckedChildren="关"
                        defaultChecked />
            </span>
            <!-- <span slot="action"  slot-scope="text, record">
                <a @click="handleEdit(record)">编辑</a>
            </span> -->
          </a-table>
        </div>
      </a-card>
    </a-col>
  </a-row>
</template>
<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { ajaxGetDictItems,getUserList } from '@api/api'
export default {
  name: 'OperationsRepository',
  mixins: [JeecgListMixin],
  components: {
  },
  data() {
    return {
      // 表头
      columns: [
        {
          title: '问题名称',
          align: 'center',
          dataIndex: 'name'
        },
        {
          title: '问题描述',
          align: 'center',
          dataIndex: 'question'
        },
        {
          title: '问题类型',
          align: 'center',
          dataIndex: 'questionType',
          scopedSlots: { customRender: 'questionType' }
        },
        {
          title: '解决方案',
          align: 'center',
          dataIndex: 'solution'
        },
        {
          title: '创建人',
          align: 'center',
          dataIndex: 'createBy',
          scopedSlots: { customRender: 'createBy' }
        },
        {
          title: '创建时间',
          align: 'center',
          dataIndex: 'createTime'
        },
        {
          title: '常见问题',
          align: 'center',
          dataIndex: 'isCommon',
          scopedSlots: { customRender: 'isCommon' }
        },
        ],
        url: {
          list: '/question/knowledge/orderList',
        },

        //资产类型
        QuestionType: [],

        UserList:[]

    }
  },
  methods: {
    handleChange(value) {
    },
    downloadRowFile(text){
        if(!text){
          this.$message.warning("未知的文件")
          return;
        }
        if(text.indexOf(",")>0){
          text = text.substring(0,text.indexOf(","))
        }
        window.open(window._CONFIG['downloadUrl']+"/"+text);
      },
    //刷新
    mySearchReset() {
      this.queryParam = {}
      this.loadData();
    },
    getQuestionType(code){
      let that = this
		  ajaxGetDictItems(code, null).then(res => {
		    if(res.success){

		      that.QuestionType = res.result
		    }else{
		      that.$message.error("资产类型字典信息获取失败")
		    }

		  })
    },
    getUserLists(){
      let that = this
		  getUserList(null).then(res => {
		    if(res.success){

		      that.UserList = res.result.records;
		    }else{
		      that.$message.error("用户信息获取失败")
		    }

		  })
    }
  },
  mounted() {
    this.getQuestionType('question_type');
    this.getUserLists();

  }
}
</script>
<style scoped>
body {
  height: auto;
  overflow: hidden !important;
}
.title {
  text-align: center;
  font-weight: 700;
  font-style: normal;
  font-size: 23px;
  margin-bottom: 34px;
  margin-top: 10px;
}
.table-header span {
  margin-bottom: 16px;
  margin-right: 20px;
  display: inline-block;
  color: red;
}
.table-operator .ant-btn {
  padding: 0 32px !important;
}
</style>
