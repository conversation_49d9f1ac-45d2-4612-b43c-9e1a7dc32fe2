import echarts from 'echarts'
import { getAction } from '@/api/manage'
// import { mapConfig } from 'public/static/map/mapConfig'
let mapConfig = null;
export async function getMapConfig() {
  try {
    if (mapConfig) {
      return mapConfig;
    }

    if (!location?.origin) {
      console.warn('Invalid location origin');
      return [];
    }

    const configUrl = `${location.origin}/static/map/mapConfig.json`;
    mapConfig = await getAction(configUrl);
    return mapConfig || [];
  } catch (error) {
    console.error('Failed to get map config:', error);
    return [];
  }
}


// 已注册的地图缓存
const registeredMaps = {}

/**
 * 注册地图数据
 * @param {string} mapName 地图名称
 * @returns {Promise} 返回Promise
 */
export async function registerMap(mapName) {
  // 1. 检查是否已注册
  if (registeredMaps[mapName]) return true

  // 2. 查找配置
  await getMapConfig()
  const mapInfo = mapConfig.find((item) => item.value === mapName)
  if (!mapInfo) {
    console.error(`地图配置不存在: ${mapName}`)
    return false
  }

  // 3. 动态加载地图文件
  try {
    const res = await getAction(location.origin + mapInfo.jsonUrl)
    if (res) {
      echarts.registerMap(mapName, res)
      registeredMaps[mapName] = true // 记录已注册地图
      return true
    }
  } catch (error) {
    console.error(`地图加载失败: ${mapName}`, error)
    return false
  }
}


