<template>
  <a-row style='height: 100%'>
    <a-col :xl='4' :lg='6' :md='8' :sm='10' :xs='10' style='height: 100%; background: #fff'>
      <device-tree-expand :inputFlag='false'
                          :bottom-tree-margin="'20px 14px 14px 14px'"
                          @selected='treeSeletedSearch'
                          :tree-url="'/knowledges/topic/selectTreeForKnowledges'"
                          :btnIconName="'appstore'"
                          :is-show-all-btn='true'
                          :btnName="'全部主题'"
                          :fieldKey='"id"'
                          :is-show-btn-icon='true'
                          :is-show-icon='false'>
      </device-tree-expand>
    </a-col>
    <a-col :xl='20' :lg='18' :md='16' :sm='14' :xs='14' style='height: 100%; overflow: hidden; overflow-y: auto'>
      <a-row style='height: 100%; margin-left: 16px; margin-right: 4px'>
        <a-col style='width: 100%; height: 100%; display: flex; flex-direction: column'>
          <!-- 查询区域 -->
          <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0', marginRight: '12px' }" class='card-style'
                  style='width: 100%'>
            <div class='table-page-search-wrapper'>
              <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
                <a-row :gutter='24' ref='row'>
                  <a-col :span='spanValue'>
                    <a-form-item label='标题'>
                      <a-input placeholder='请输入标题' v-model='queryParam.title' :allowClear='true'
                               autocomplete='off' :maxLength="maxLength"/>
                    </a-form-item>
                  </a-col>
                  <a-col :span='spanValue'>
                    <a-form-item label='能见度'>
                      <a-select :allowClear='true' v-model='queryParam.isPrivate' placeholder='请选择能见度'>
                        <a-select-option v-for='item in visibility' :value='item.value' :key="'visibility_'+item.value"
                                         :label='item.label'>
                          {{ item.label }}
                        </a-select-option>
                      </a-select>
                    </a-form-item>
                  </a-col>
                  <a-col :span='spanValue'>
                    <a-form-item label='知识类型'>
                      <a-select :allowClear='true' v-model='queryParam.knowledgeType' placeholder='请选择知识类型'>
                        <a-select-option :value='0' label='文本'>文本</a-select-option>
                        <a-select-option :value='1' label='文档'>文档</a-select-option>
                      </a-select>
                    </a-form-item>
                  </a-col>
                  <a-col :span='colBtnsSpan()'>
                    <span class='table-page-search-submitButtons'
                          :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                      <a-button type='primary' class='btn-search btn-search-style' @click='searchQuery'>查询</a-button>
                      <a-button class='btn-reset btn-reset-style' @click='searchReset'>重置</a-button>
                      <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                        {{ toggleSearchStatus ? '收起' : '展开' }}
                        <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                      </a>
                    </span>
                  </a-col>
                </a-row>
              </a-form>
            </div>
          </a-card>
          <a-card :bordered='false' style='width: 100%; flex: auto'>
            <div class='table-operator table-operator-style'>
              <a-button @click='handleAdd' v-has="'knowledgeBase:add'">新增</a-button>
              <a-dropdown v-if="selectedRowKeys.length > 0&& $yqHasPermission('knowledgeBase:delete')">
                <a-menu slot="overlay" style='text-align: center'>
                  <a-menu-item key='1' @click='batchDel'>删除</a-menu-item>
                </a-menu>
                <a-button> 批量操作
                  <a-icon type='down' />
                </a-button>
              </a-dropdown>
            </div>
            <a-table
              ref='table'
              bordered
              :row-key='(record, index) => {return record.id}'
              :columns='columns'
              :dataSource='dataSource'
              :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
              :pagination='ipagination'
              :loading='loading'
              :rowSelection='{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }'
              @change='handleTableChange'>
              <template slot='htmlSlot' slot-scope='text' width='500'>
                <div v-html='text'></div>
              </template>
              <template slot='imgSlot' slot-scope='text'>
                <span v-if='!text' style='font-size: 14px'>无图片</span>
                <img v-else :src='getImgView(text)' height='25px' alt='' style='max-width: 80px; font-size: 14px' />
              </template>

              <span slot='action' class='caozuo' slot-scope='text, record'>
                <a @click='handleDetailPage(record)'>查看</a>
                <a-divider type='vertical' />
                <a-dropdown
                  :class="{'dropdown-disabled':!$yqHasPermission('knowledgeBase:edit')&&!$yqHasPermission('knowledgeBase:delete')&&sysUserId!==record.createByUserId}"
                  :disabled="!$yqHasPermission('knowledgeBase:edit')&&!$yqHasPermission('knowledgeBase:delete')&&sysUserId!==record.createByUserId">
                 <a class='ant-dropdown-link'>更多 <a-icon type='down' /></a>
                 <a-menu slot='overlay'>
                  <a-menu-item v-if="$yqHasPermission('knowledgeBase:edit')||sysUserId===record.createByUserId">
                  <a @click='handleEdit(record)'>编辑</a>
                  </a-menu-item>
                  <a-menu-item v-if="$yqHasPermission('knowledgeBase:delete')||sysUserId===record.createByUserId">
                    <a @click='confirmDelete(record.id)'>删除</a>
                  </a-menu-item>
<!--                   除了审批状态的判断，发布按钮其他权限同编辑一样-->
                  <a-menu-item v-if="record.isPrivate!='1'&&(record.state==approvalState[0].value||record.state==approvalState[1].value)&&($yqHasPermission('knowledgeBase:edit')||sysUserId===record.createByUserId)">
                    <a @click='publish(record.id)'>发布</a>
                  </a-menu-item>
                </a-menu>
              </a-dropdown>
              </span>
              <template slot='tooltip' slot-scope='text'>
                <a-tooltip placement='topLeft' :title='text' trigger='hover'>
                  <div class='tooltip'>
                    {{ text }}
                  </div>
                </a-tooltip>
              </template>

              <template slot='isPrivate' slot-scope='text'>
                <!--                <a-icon :theme='"filled"' :type='text==="1"?"lock":"unlock"' :style='{color:text==="1"?"#FE9400":"#4BD863"}'></a-icon>-->
                <a-icon :type='text===visibility[1].value?"lock":"global"' :style='{color:text===visibility[1].value?"#FE9400":"#4BD863"}'></a-icon>
                {{ text === visibility[1].value ? visibility[1].label : visibility[0].label }}
              </template>

              <template slot='knowledgeType' slot-scope='text'>
                <knowledge-icon :knowledgeType="text"></knowledge-icon>
              </template>
            </a-table>
          </a-card>
        </a-col>
      </a-row>
    </a-col>
    <add-knowlege-modal ref='modalForm' @ok='modalFormOk'></add-knowlege-modal>
  </a-row>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import addKnowlegeModal from '@views/opmg/knowledgeManagement/knowledgeBase/modules/AddKnowledgeModal.vue'
import knowledgeIcon from '@views/opmg/knowledgeManagement/knowledgeBase/modules/KnowledgeIcon.vue'
import JDictSelectTag from '@/components/dict/JDictSelectTag.vue'
import DeviceTreeExpand from '@/components/tree/DeviceTreeExpand.vue'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
import { visibility,approvalState } from '@views/opmg/knowledgeManagement/knowledgeBase/modules/dataListAndFunc'
import { putParamsAction} from '@api/manage'

export default {
  name: 'KnowledgeBaseList',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components: {
    addKnowlegeModal,
    knowledgeIcon,
    JDictSelectTag,
    'device-tree-expand': DeviceTreeExpand
  },
  data() {
    return {
      maxLength:50,
      description: '设备表管理页面',
      sysUserId: this.$store.getters.userInfo.id,
      formItemLayout: {
        labelCol: { style: 'width:70px' },
        wrapperCol: {
          style: 'width:calc(100% - 70px)'
        }
      },
      visibility: visibility,
      approvalState:approvalState,
      // 表头
      columns: [
        {
          title: '标题',
          dataIndex: 'title',
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'tooltip'
          }
        },
        {
          title: '主题名称',
          dataIndex: 'topicName',
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'tooltip'
          }
        },
        {
          title: '能见度',
          dataIndex: 'isPrivate',
          customCell: () => {
            let cellStyle = 'width:80px'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'isPrivate'
          }
        },
        {
          title: '知识类型',
          dataIndex: 'knowledgeType',
          customCell: () => {
            let cellStyle = 'width:80px'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'knowledgeType'
          }
        },
        {
          title: '创建人员',
          dataIndex: 'createBy'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          customCell: () => {
            let cellStyle = 'width: 160px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '审批状态',
          dataIndex: 'state'
        },
        /* {
           title: '最近更新时间',
           dataIndex: 'updateTime',
           customCell: () => {
             let cellStyle = 'text-align: center;width: 160px'
             return {
               style: cellStyle
             }
           }
         },*/
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 150,
          scopedSlots: {
            customRender: 'action'
          }
        }
      ],
      url: {
        list: '/kbase/knowledges/list',
        delete: '/kbase/knowledges/delete',
        deleteBatch: '/kbase/knowledges/deleteBatch',
        publish:'/kbase/knowledges/publish'
      },
      disableMixinCreated: true
    }
  },
  activated() {
    this.loadData()
  },
  // mounted() {
  //   this.loadData(1)
  // },
  methods: {
    treeSeletedSearch(id = '') {
      this.queryParam.knowledgeTopicId = id
      this.loadData(1)
    },
    handleDetailPage: function(record) {
      this.$parent.pButton2(1, record)
    },
    publish(id){
      putParamsAction(this.url.publish,{knowledgeId:id}).then((res)=>{
        if (res.success){
          this.loadData()
        }else {
          this.$message.warning(res.message)
        }
      }).catch((err)=>{
        this.$message.warning(err.message)
      })
    },
    searchReset() {
      delete this.queryParam.title
      delete this.queryParam.isPrivate
      delete this.queryParam.knowledgeType
      this.loadData(1)
    },
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
.caozuo .dropdown-disabled {
  color: rgba(0, 0, 0, 0.25) !important;
  cursor: default;
}
</style>
