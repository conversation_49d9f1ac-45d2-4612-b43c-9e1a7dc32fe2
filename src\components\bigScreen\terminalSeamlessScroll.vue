<template>
  <div class="container">
    <div class="core-core-tableTitle">
      <span style="width: 35%;">{{titleL.firstTitle}}</span>
      <span style="width: 20%;">{{titleL.secondTitle}}</span>
      <span style="width: 15%;">{{titleL.thirdTitle}}</span>
      <span style="width: 15%;">{{titleL.fourthTitle}}</span>
      <span style="width: 15%;">{{titleL.fifthTitle}}</span>
    </div>
    <div class="core-core-table" ref="scrollBox"  @mousewheel="handleMouseWheel">
      <vue-seamless-scroll
      :data="cityTerminal"
      :class-option="warning"
      ref="seamlessDiv"
      class="seamless-warp">
        <ul>
          <li v-for="item in cityTerminal"
          :key="item.id"
          >
            <span style="width: 35%;" :title="item.name">{{setName(item.name)}}</span>
            <span style="width: 20%;" :title="item.sumCount">{{ item.sumCount }}</span>
            <span style="width: 15%;" :title="item.onCount">{{ item.onCount }}</span>
            <span style="width: 15%;" :title="item.offCount">{{ item.offCount }}</span>
            <span style="width: 15%;" :title="item.rate">{{ item.rate }}%</span>
          </li>
        </ul>
      </vue-seamless-scroll>
    </div>
  </div>
</template>
<script>
import vueSeamlessScroll from 'vue-seamless-scroll'
import { deleteAction, getAction, putAction, httpAction } from '@/api/manage'
export default {
  name: 'seamlessScroll',
  components: {
    vueSeamlessScroll
  },
   props: {
    titleL: {
      type: Object,
      default: () => {},
      required: true
    },
    dataUrl: {
      type: String,
      default: '',
      required: false
    },
     dataRes:{
      type:Array,
       default:[],
       required:false,
     }
  },
   data() {
    return {
      cityTerminal: []
    }
  },
  computed: {
    warning() {
      return {
        step: 0.3, // 数值越大速度滚动越快
        limitMoveNum: 4, // 开始无缝滚动的数据量 this.dataList.length
        hoverStop: true, // 是否开启鼠标悬停stop
        direction: 1, // 0向下 1向上 2向左 3向右
        // openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 0, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
        // singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
        waitTime: 0 // 单步运动停止的时间(默认值1000ms)
      }
    }
  },
  watch: {
    dataUrl: {
      handler(nv) {
        this.geCityTerminal(nv)
      },
      immediate: false
    },
    dataRes: {
      handler(nv) {
        this.$nextTick(()=>{
          this.geCityTerminal(nv);
        })
      },
      immediate: true
    }
  },


  methods: {
    //单位名称无数据时,返回单位name为‘无单位’
    setName(name){
      return name=name?name:'无单位'
    },

    scrollClick(item){
    },
    // 告警轮播数据
    geCityTerminal(url) {
      if(this.dataRes.length>0){
        this.cityTerminal=this.dataRes;
        return;
      }
      if(url.length>0){
        getAction(url).then(res => {
          if (res.code == 200) {
            this.cityTerminal = this.cityTerminal.concat(res.result)
          }
        })
      }
    },
    handleMouseWheel(e){
      if(Math.abs(this.$refs.seamlessDiv.yPos)<this.$refs.seamlessDiv.realBoxHeight/2||e.deltaY<0){
        this.$refs.seamlessDiv.yPos-=e.deltaY;
        this.$refs.seamlessDiv.yPos=this.$refs.seamlessDiv.yPos>0?0:this.$refs.seamlessDiv.yPos;
      }
    }
  }
}
</script>
<style scoped lang="less">
.container {
  height: calc(100% - 24px);
  width:100%;
  background-color: #131419;
  // padding: 0 0.25rem
  padding-top: 13px;
  .core-core-tableTitle {
    display: flex;
    height: 30px;
    width:100%;
    align-items: center;
    padding-bottom: 13px;
    border-bottom: 1px solid #1d44a9;
    span {
      font-size: 0.2rem /* 16/80 */;
      text-align: center;
      color: #00c4f6;
    }
  }
  .core-core-table {
    width: 100%;
    height: calc(100% - 30px);
    overflow: hidden;
    .seamless-warp {
      // width: 100%;
      ul {
        width: 100%;
        padding: 0;
        margin: 0;
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        li {
          text-align: center;
          height: 0.525rem /* 42/80 */;
          width: 100%;
          line-height: 0.525rem /* 42/80 */;
          display: flex;
          justify-content: space-around;
          text-align: center;
          font-size: 14px;
          // padding: 5px 0;
          span {
            color: rgba(255, 255, 255, 0.75);
            overflow: hidden; /*溢出的部分隐藏*/
            white-space: nowrap; /*文本不换行*/
            text-overflow: ellipsis; /*ellipsis:文本溢出显示省略号（...）；clip：不显示省略标记（...），而是简单的裁切*/
          }
        }
        li:nth-child(even){
          background: #1f2533;
          text-align: center;
        }
      }
    }
  }
}
</style>