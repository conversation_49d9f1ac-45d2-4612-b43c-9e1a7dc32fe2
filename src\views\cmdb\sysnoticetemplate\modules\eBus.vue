<template>
  <div>
    <a-col :span='23'>
      <a-form-model-item label='消息类型' prop='remindType' :rules='validatorRules.remindType'>
        <j-dict-select-tag v-model='personalizedObject.remindType' placeholder='请选择消息类型' dictCode='remindType' />
      </a-form-model-item>
    </a-col>
    <a-col :span='23'>
      <a-form-model-item class='two-words' label='收信人' prop='sendTo' :rules='validatorRules.sendTo'>
        <a-select v-model='personalizedObject.sendTo' :allowClear='true' :getPopupContainer='(node) => node.parentNode'
                  mode='multiple' option-filter-prop='children' :maxTagCount='2'
                  :maxTagTextLength="4"
                  placeholder='请选择收信人' show-search @change='changeValue'>
          <a-select-option v-for='item in usersList' :key='item.id' :value='item.id'>{{
              item.realName
            }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
    </a-col>
  </div>
</template>

<script>
export default {
  name: 'http',
  props: {
    data: {
      type: Object,
      required: false,
      default: () => {
        let personalizedObject = {
          remindType: '',
          sendTo: undefined,
        }
        return personalizedObject
      }
    },
    usersList: {
      type: Array,
      required: true,
      default: []
    }
  },
  data() {
    return {
      personalizedObject: {
        remindType: '',
        sendTo: undefined,
      },
      validatorRules: {
        remindType: [
          { required: true, message: '请选择消息类型!' }
        ],
        sendTo: [
          { required: false, validator: this.customValidate }
        ],
      }
    }
  },
  watch: {
    data: {
      handler(val) {
        if (Object.keys(val).length > 0) {
          this.personalizedObject = val
        }
      },
      deep: true,
      immediate: true,
    }
  },
  methods: {
    changeValue() {
      this.$emit('changeModelValue', this.personalizedObject)
    },
    getTips(fullField) {
      let str = ''
      switch (fullField) {
        case 'remindType':
          str = '请选择收信人！'
          break
      }
      return str
    },
    customValidate(rule, value, callback) {
      if (rule.required) {
        if (value && value.length > 0) {
          callback()
        } else {
          callback(this.getTips(rule.fullField))
        }
      } else {
        callback()
      }
    }
  }
}
</script>