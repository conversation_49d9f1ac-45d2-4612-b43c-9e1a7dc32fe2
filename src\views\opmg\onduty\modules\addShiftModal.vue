<template>
  <j-modal :visible='visible' :title='title'
           @cancel='handleCancel'
           :centered="true"
           @ok='handleOk'
           cancelText='关闭'
           :width='900'
           switchFullscreen>
    <a-form-model ref='form' :model='form' :rules='validatorRules' :label-col='labelCol' :wrapper-col='wrapperCol'>
      <a-row :gutter='24'>
        <a-col v-bind='formItemLayout'>
          <a-form-model-item label='日期' prop='dateList'>
            <a-date-picker @change='onChange' v-model='form.dateList'
                           style='width: 100%;'
                           :disabledDate="disabledBeforeToday"
                           format='YYYY-MM-DD' />
          </a-form-model-item>
        </a-col>

        <a-col v-bind='formItemLayout'>
          <a-form-model-item label='班次' prop='id'>
            <a-select
              :getPopupContainer='node=>node.parentNode'
              placeholder='请选择班次信息'
              v-model='form.id'
              style='width: 100%;'
              :allow-clear='true'
            >
              <a-select-option v-for='(data,i) in dataSource' :key='i' :value='data.id'>
                {{ data.shiftName }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>

        <a-col v-bind='formItemLayout'>
          <a-form-model-item label='值班人' prop='userId'>
            <j-select-multi-user @change='changeUser' v-model='form.userId' :url='url'
                                 :query-config="selectUserQueryConfig"
                                 :multiple='false'
                                 :default='defaultUser'></j-select-multi-user>
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
  </j-modal>
</template>

<script>
import { getAction } from '@api/manage'
import moment from 'moment'
import { queryConfigureDictItem } from '@api/api'

export default {
  name: 'shiftForm',
  props: {
    dataSource: {
      type: Array,
      default: []
    }
  },
  data() {
    return {
      title:'',
      // 选择用户查询条件配置
      selectUserQueryConfig: [],
      formItemLayout: {
        md: { span: 24 },
        sm: { span: 24 }
      },
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
        md: { span: 5 },
        lg: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
        md: { span: 15 },
        lg: { span: 16 }
      },
      defaultUser: {
        name: '值班人员',
        width: 1200,
        displayKey: 'realname',
        returnKeys: ['id', 'username'],
        queryParamText: '姓名'
      },
      number: 0,
      visible: false,
      form: {},
      validatorRules: {
        id: [{ required: true, message: '请选择班次!' }],
        dateList: [{ required: true, message: '请选择日期!' }],
        userId: [{ required: true, message: '请选择值班人!' }],
      },
      url: { list: '/sys/user/queryUsersRolesList'},
      isEdit: false,
    }
  },
  mounted() {
    queryConfigureDictItem({
      parentCode: 'dutyRole',
      childCode: "roleId"
    }).then((res) => {
      if (res.success && res.result) {
        this.url.list = "/sys/user/queryUsersRolesList?roleId="+res.result
        this.$forceUpdate()
      }
    })
   // this.getRoles()
  },
  methods:{
    disabledBeforeToday(current) {
      return current && current < moment().subtract(1, 'days').endOf('day');
    },
    getRoles(){
      getAction("/sys/role/queryall").then((res) => {
        if (res.success){
          this.selectUserQueryConfig.push({ key: 'roleId', label: '角色',placeholder:"请选择角色",roleOptions:res.result })
        }
      })
    },
    handleCancel() {
      this.isEdit = false
      this.form.id=this.form.recordId
      this.$refs.form.resetFields();
      this.visible = false
    },
    handleOk() {
      this.$refs.form.validate(valid => {
        if (valid) {
          let path = '/shiftUser/record/addRecords'
          if(this.isEdit){
            path = "/shiftUser/record/updateRecords"
          }
          getAction(path, this.form).then((res) => {
            if (res.success) {
              this.$message.success('操作成功')
              this.handleCancel()
              this.$emit('refreshPage')
            } else {
              this.$message.warning(res.message)
            }
          })
        }
      })
    },
    openVisible(time, data={},title) {
      this.form = data
      if(data.recordId && data.shiftId){
        this.form.id = data.shiftId;
        this.isEdit = true;
      }
      this.title=title
      this.$set(this.form, 'dateList', moment(time))
      this.form.startDate = this.isEdit?time+' 00:00:00':time
      this.visible = true
    },
    /**
     * 时间选择
     * @param date
     * @param dateString
     */
    onChange(date, dateString) {
      this.form.startDate = dateString
    },
    changeUser(text) {
      this.number = text.split(',').length
    }
  }
}
</script>
<style scoped lang='less'>
@import '~@assets/less/normalModal.less';
</style>