<template>
  <div class="wrap">
    <div  class="edge-div" v-if='showEdgeDiv' :style='{top:edgeTop,left:edgeLeft,zIndex:edgeIndex}'>
      <div>
        <p>
          关系：<span>{{ edgeRelation != null ? edgeRelation : '' }}</span>
        </p>
        <!-- <p>终止节点：<span>{{edgeConnectNode!=null?edgeConnectNode.target:''}}</span></p> -->
      </div>
    </div>
    <div v-if='showEditDialog'>
      <div class="edit-list-position node-edit-list" :style='{top:nodeTop,left:nodeLeft}'>
        <ul class="edit-list">
          <li v-if='addShow' @click='addAssets'>
            <div>
              <a-icon type="plus-square" style='margin-right: 5px;' />添 加
            </div>
          </li>
          <li v-if='delShow' @click='deleteAssetsRelation'><div><a-icon type="delete" style='margin-right: 5px;'/>删 除</div></li>
        </ul>
      </div>
    </div>
    <div class="content">
      <div class="panel">
        <!--流程图画板-->
        <div :id="id" class="container" />
      </div>
    </div>
  </div>
</template>

<script>
import './index.less'
import FlowGraph from './graph'
// Cell, Node,
import { Graph, Color, Dom, Shape, Edge, NodeView, Addon } from '@antv/x6'
import dagre from 'dagre'
import { getAction, httpAction,deleteAction } from '@/api/manage'
import { globalGridAttr, globalGridAttr2 } from './models/global'

const { Dnd } = Addon

const tryToJSON = (val) => {
  try {
    return JSON.parse(val)
  } catch (error) {
    return val
  }
}
// 布局方向
const dir = 'TB' // LR RL TB BT

export default {
  name: 'relationTopo',
  components: {},
  props: {
    assetsId: {
      type: String,
      default: '',
      required: false,
    },
    showDelAdd: {
      type: Boolean,
      default: false,
      required: false,
    },
    refresh: {
      type: Boolean,
      default: false,
      required: false,
    },
    id: {
      type: String,
      default: '',
      required: true,
    },
  },
  data() {
    return {
      nodeTop: '0',
      nodeLeft: '0',
      edgeTop: '0',
      edgeLeft: '0',
      edgeIndex:0,
      screenWidth: '',
      screenHeight: '',
      assetsRelationId: '',
      addShow:false,
      delShow:false,
      operate: 'create', //当前页面为查看、编辑：show、create
      type: 'grid',
      visible: false,
      showEditDialog: false,
      showEdgeDiv: false,
      graph: '',
      isReady: false,
      // 树数据
      assetsCategoryTree: [],
      draggable: true,
      url: {
        treeUrl: '/topo/device/tree',
        deviceInfo: '/topo/device/info',
        deviceChildren: '/topo/device/children',
        topoEdit: '/topo/topoInfo/edit',
        findAssetsRelation: '/assets/assets/findAssetsRelation',
        deleteAssetsRelation: '/assets/assets/deleteAssetsRelation',
      },
      replaceFields: {
        title: 'name',
        key: 'id',
      },
      picSrc: window._CONFIG['staticDomainURL'],
      dnd: Object,
      deviceInfo: null,
      topoInfo: null,
      copyTopoInfo: null, //topo信息的复制保存，用于子拓扑返回父拓扑时使用
      cellList: [],
      alarmInfo: {
        deviceName: 'snmp01',
        templateName: 'qqqq',
        level: '10',
      },
      globalGridAttr: Object.assign(globalGridAttr, globalGridAttr2),
      edgeConnectNode: null,
      edgeRelation: '',
      sonTopoFlag: false, //true标识此时的拓扑为子拓扑
    }
  },

  computed: {
    getAlarmInfo() {
      return this.$store.getters.alarmInfo
    },
    doRefresh() {
      return this.refresh
    },
  },
  watch: {
    doRefresh: {
      immediate: true,
      handler(val) {
        if (val) {
          this.graph.dispose()
          this.initGraph(this.id)
          this.findAssetsRelation()
        }
      },
    },
    assetsId:{
      immediate: true,
      handler(val) {
        if (val) {
          this.$nextTick(()=>{
            if(this.graph){
              this.graph.dispose()
            }
            this.initGraph(this.id)
            this.findAssetsRelation()
            this.screenWidth = document.body.clientWidth
            this.screenHeight = document.body.clientHeight
          })
        }
      }
    }
  },
  mounted() {
   /* this.initGraph(this.id)
    this.findAssetsRelation()
    this.screenWidth = document.body.clientWidth
    this.screenHeight = document.body.clientHeight*/

    window.addEventListener('resize', () => {
      return (() => {
        this.graph.dispose()
        this.initGraph(this.id)
        this.findAssetsRelation()
        this.screenWidth = document.body.clientWidth
        this.screenHeight = document.body.clientHeight
      })()
    })
   /* window.onresize = () => {
      return (() => {
        this.graph.dispose()
        this.initGraph(this.id)
        this.findAssetsRelation()
        this.screenWidth = document.body.clientWidth
        this.screenHeight = document.body.clientHeight
      })()
    }*/
  },
  destroyed() {
    window.removeEventListener('resize', () => {
      return (() => {
        this.graph.dispose()
        this.initGraph(this.id)
        this.findAssetsRelation()
        this.screenWidth = document.body.clientWidth
        this.screenHeight = document.body.clientHeight
      })()
    })
  },
  methods: {
    show() {
      this.$nextTick(() => {
        this.initGraph(this.id)
        this.findAssetsRelation()
      })
    },
    findAssetsRelation() {
      getAction(this.url.findAssetsRelation, {
        assetsId: this.assetsId,
      }).then((res) => {
        if (res.success) {
          this.createNodesAndEdges(res)
          this.layout()
          this.graph.zoomTo(0.8)
          this.graph.centerContent()
          this.setup()
        }
      })
    },
    createNodesAndEdges(res) {
      let nodeList = []
      res.result.nodeList.forEach((ele) => {
        nodeList.push(this.createNode(ele.assetsName, ele.assetsId,ele.assetsRelationId))
      })
      this.$emit('transmit', nodeList)
      let edgeList = []
      if (res.result.relationList) {
        res.result.relationList.forEach((ele) => {
          const sourceNode = nodeList.find((item) => item.data.id === ele.assetsIdOne)
          const targetNode = nodeList.find((item) => item.data.id === ele.assetsIdTwo)
          const relation = ele.relation || ele.relationReverse
          edgeList.push(this.createEdge(sourceNode, targetNode, relation))
        })
      }
      this.graph.resetCells([...nodeList, ...edgeList])
    },
    // 自动布局
    layout() {
      const nodes = this.graph.getNodes()
      const edges = this.graph.getEdges()
      const g = new dagre.graphlib.Graph()
      g.setGraph({
        rankdir: dir,
        nodesep: 16,
        ranksep: 16,
      })
      g.setDefaultEdgeLabel(() => ({}))

      const width = 120
      const height = 60
      nodes.forEach((node) => {
        g.setNode(node.id, {
          width,
          height,
        })
      })

      edges.forEach((edge) => {
        const source = edge.getSource()
        const target = edge.getTarget()
        g.setEdge(source.cell, target.cell)
      })

      dagre.layout(g)

      this.graph.freeze()

      g.nodes().forEach((id) => {
        const node = this.graph.getCell(id)
        if (node) {
          const pos = g.node(id)
          node.position(pos.x, pos.y)
        }
      })

      edges.forEach((edge) => {
        const source = edge.getSourceNode()
        const target = edge.getTargetNode()
        const sourceBBox = source.getBBox()
        const targetBBox = target.getBBox()
      })
      this.graph.unfreeze()
    },

    onClose() {
      this.graph.dispose()
      this.visible = false
    },

    initGraph(container) {
      this.graph = FlowGraph.init(container)
      //画布grid配置
      this.graph.drawGrid({
        type: this.globalGridAttr.type,
        args: [
          {
            color: this.globalGridAttr.color,
            thickness: this.globalGridAttr.thickness,
          },
        ],
      })
      this.graph.setGridSize(this.globalGridAttr.size)
      // this.graph.centerContent()
    },
    // 监听画布节点、边自定义事件
    setup() {
      //鼠标移入边事件
      this.graph.on('edge:mouseenter', ({ e, edge, view }) => {
        this.edgeRelation = edge.data.relation || ''
        if (this.edgeRelation != '') {
          this.edgeTop=e.offsetY + 70 + 'px'
          this.edgeLeft= e.offsetX + 25 + 'px'
          this.edgeIndex= 100
          this.showEdgeDiv=true
        }
      })
      //鼠标移出边事件
      this.graph.on('edge:mouseleave', () => {
        this.showEdgeDiv=false
      })
      //鼠标右击
      this.graph.on('node:contextmenu', ({ e, node, view }) => {
       if (node.data&&this.showDelAdd){
         if (node.data.assetsRelationId!==null){
           this.addShow=false
           this.delShow=true
           this.assetsRelationId=node.data.assetsRelationId
         }else{
           this.addShow=true
           this.delShow=false
         }
         this.nodeTop = e.clientY - 60 + 'px'
         this.nodeLeft = e.clientX+ 'px'
         this.showEditDialog=true
         this.graph.resetSelection(node);
       }
      })
      this.graph.on('cell:click', ({ e, node, view }) => {
        this.showEditDialog=false
      })
      this.graph.on('node:click', ({ e, node, view }) => {
        this.showEditDialog=false
      })
      this.graph.on('edge:click', ({ e, node, view }) => {
        this.showEditDialog=false
      })
      this.graph.on('blank:click', ({ e, node, view }) => {
        this.showEditDialog=false
      })


    },
    createEdge(source, target, relation) {
      return this.graph.createEdge({
        source: {
          cell: source.id,
        },
        target: {
          cell: target.id,
        },
        zIndex: -1,
        data: {
          relation: relation,
        },
        attrs: {
          line: {
            stroke: '#A2A2A2',
            strokeWidth: 3,
            sourceMarker: null,
            targetMarker: null,
          },
        },
      })
    },
    createNode(nodeName, nodeId,id) {
      return this.graph.createNode({
        shape: 'ellipse',
        width: 120,
        height: 60,
        data: {
          id: nodeId,
          name: nodeName,
          assetsRelationId:id,
        },
        attrs: {
          body: {
            fill: '#EDF5FE',
            stroke: '#C5DDFB',
          },
          label: {
            text: nodeName,
            fill: '#080808',
          },
        },
      })
    },
    deleteAssetsRelation(){
      this.showEditDialog=false
      var that = this
      this.$confirm({
        title: '确认删除',
        okText: '是',
        cancelText: '否',
        content: '是否删除该节点信息?',
        onOk: function () {
          deleteAction(that.url.deleteAssetsRelation, { id: that.assetsRelationId }).then((res) => {
            if (res.success) {
              that.findAssetsRelation()
              that.$message.success(res.message)
            } else {
              that.$message.warning(res.message)
            }
          })
        },
      })
    },
    addAssets(){
      this.$emit('addAssets')
      this.showEditDialog=false
    },
  },
}
</script>

<style scoped lang="less">
.node-edit-list {
  width: 100px;
}
.edit-list-position {
  position: fixed;
  border: solid 1px #dcdcdc;
  border-radius: 6px;
  background: #fff;
  z-index: 100;
}

.edit-list {
  list-style: none;
  padding: 0px 20px;
  margin: 0px;
  li {
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    height: 30px;
    line-height: 30px;
    font-size: 14px;
  }
  li :hover {
    color: #4b90de;
  }
}
.wrap {
  height: 100%;
  width: 100%;
}

.largeWrap {
  height: 500px;

  .content {
    display: flex;
    height: 100% !important;
  }
}

.panel {
  width: 100%;
  height: 100%;
}

.panel .toolbar {
  display: flex;
  align-items: center;
  height: 38px;
  background-color: #f7f9fb;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

.wrap .content {
  display: flex;
  height: 100% !important;
}

::v-deep .sider {
  box-shadow: none;
  border: 1px solid rgba(0, 0, 0, 0.08) !important;
  margin-right: 10px;
}

.container {
  height: 100% !important;
}

::v-deep .x6-graph-scroller {
  width: 100% !important;
}

::v-deep .x6-graph {
  box-shadow: none !important;
}

.edge-div {
  width: 200px;
  background-color: #fff;
  border-radius: 5px;
  color: #595959;
  position: absolute;
  top: 405px;
  left: 775px;
  z-index: 100;

  p {
    margin-bottom: 0px;
    height: 30px;
    line-height: 30px;
    padding-left: 15px;
  }
}

.alarm-div {
  display: none;
  width: 200px;
  background-color: #fff;
  border-radius: 5px;
  position: absolute;
  z-index: 100;

  h4 {
    height: 30px;
    line-height: 30px;
    padding-left: 15px;
    margin-bottom: 0px;
    color: #1f366e;
  }

  p {
    height: 30px;
    line-height: 30px;
    padding-left: 15px;
    color: #565656;
  }
}
</style>