<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <!-- 查询区域 -->
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span='spanValue'>
                <a-form-item label="资产名称">
                  <a-input placeholder="请输入资产名称"  :maxLength="maxLength" v-model="queryParam.assetsName" :allowClear='true' autocomplete='off'/>
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label="资产类型">
                  <a-input :maxLength="maxLength" placeholder="请输入资产类型" v-model="queryParam.assetsCategoryName"  :allowClear='true' autocomplete='off'/>
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label="供应商名称">
                  <a-input placeholder="请输入供应商名称" :maxLength="maxLength" v-model="queryParam.supplierName" :allowClear='true' autocomplete='off'/>
                </a-form-item>
              </a-col>
              <a-col :span='colBtnsSpan()'>
                <span
                  class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                >
                  <a-button class="btn-search btn-search-style" @click="dosearch" type="primary">查询</a-button>
                  <a-button class="btn-reset btn-reset-style" @click="doreset">重置</a-button>
                  <a v-if="isVisible" class='btn-updown-style' @click="doToggleSearch">
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <!-- 查询区域-END -->
      <a-card :bordered="false" style="width: 100%; flex: auto">
        <!-- 操作按钮区域 -->
        <div class="table-operator table-operator-style">
          <!--<a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>-->
          <a-button @click="batchDel">删除</a-button>
          <a-button @click="handleExportXls('资产预警')">导出</a-button>
        </div>

        <!-- table区域-begin -->
        <a-table
          ref="table"
          bordered
          :rowKey="(record,index)=>{return record.id}"
          :columns="columns"
          :dataSource="dataSource"
          :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
          :pagination="ipagination"
          :loading="loading"
          :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          @change="handleTableChange"
        >
          <template slot="htmlSlot" slot-scope="text">
            <div v-html="text"></div>
          </template>
          <template slot="imgSlot" slot-scope="text">
            <span v-if="!text" style="font-size: 14px; font-style: italic">无图片</span>
            <img
              v-else
              :src="getImgView(text)"
              height="25px"
              alt=""
              style="max-width: 80px; font-size: 14px; font-style: italic"
            />
          </template>
          <template slot="fileSlot" slot-scope="text">
            <span v-if="!text" style="font-size: 14px; font-style: italic">无文件</span>
            <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
              下载
            </a-button>
          </template>
          <template slot="alarmType" slot-scope="text">
            <span v-if="text == 1">已过期</span>
            <span v-else style="color: red">即将过期</span>
          </template>
          <span class='caozuo' slot="action" slot-scope="text, record">
            <a @click="handleDetailPage(record)">查看</a>
            <a-divider type="vertical" />
            <a @click="deleteRecord(record)">删除</a>
          </span>
          <template slot="tooltip" slot-scope="text">
            <a-tooltip placement="topLeft" :title="text" trigger="hover">
              <div class='tooltip'>
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
      </a-card>
    </a-col>
  </a-row>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import AlarmAssetsModal from './modules/AlarmAssetsModal'
import { httpAction, getAction, deleteAction } from '@/api/manage'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'

export default {
  name: 'AlarmAssetsList',
  mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
  components: {
    AlarmAssetsModal
  },
  data() {
    return {
      maxLength:50,
      formItemLayout: {
        labelCol: {
          style: 'width:80px',
        },
        wrapperCol: {
          style: 'width:calc(100% - 80px)'
        }
      },
      description: '资产预警管理页面',
      // 表头
      columns: [
        {
          title: '预警类型',
          dataIndex: 'alarmType',
          scopedSlots: { customRender: 'alarmType' },
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 100px;max-width:250px'
            return { style: cellStyle }
          }
        },
        {
          title: '资产编号',
          dataIndex: 'assetsCode',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 100px;max-width:300px'
            return { style: cellStyle }
          }
        },
        {
          title: '资产名称',
          dataIndex: 'assetsName',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 100px;max-width:300px'
            return { style: cellStyle }
          }
        },
        {
          title: ' 资产类型',
          dataIndex: 'assetsCategoryName',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 100px;max-width:250px'
            return { style: cellStyle }
          }
        },
        {
          title: ' 供应商名称',
          dataIndex: 'supplierName',
          scopedSlots: { customRender: 'tooltip' },
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 100px;max-width:400px'
            return { style: cellStyle }
          }
        },
        {
          title: '型号',
          dataIndex: 'assetsModel',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 150px;max-width:250px'
            return { style: cellStyle }
          }
        },
        {
          title: '入库日期',
          dataIndex: 'storageTime',
          customRender: function (text) {
            return !text ? '' : text.length > 10 ? text.substr(0, 10) : text
          },
          customCell: () => {
            let cellStyle = 'text-align:center ;min-width: 100px;max-width:300px'
            return { style: cellStyle }
          }
        },
        {
          title: '质保剩余天数',
          dataIndex: 'qualityRemain',
          customCell: () => {
            let cellStyle = 'text-align:right ;min-width: 100px;max-width:300px'
            return { style: cellStyle }
          }
        },
        /*{
            title:'保质期',
            dataIndex: 'qualityTerm',
            customCell: () => {
            let cellStyle = 'text-align:center ;min-width: 100px;max-width:300px'
            return { style: cellStyle }
          }
          },
          {
            title:'质保开始日期',
            dataIndex: 'startQualityTime',
            customCell: () => {
            let cellStyle = 'text-align:center ;min-width: 100px;max-width:300px'
            return { style: cellStyle }
          }
            customRender:function (text) {
              return !text?"":(text.length>10?text.substr(0,10):text)
            }
          },*/
        {
          title: '操作',
          dataIndex: 'action',
          fixed: 'right',
          align:'center',
          width: 160,
          scopedSlots: { customRender: 'action' },
        },
      ],
      url: {
        list: '/alarmAssets/alarmAssets/list',
        delete: '/alarmAssets/alarmAssets/delete',
        deleteBatch: '/alarmAssets/alarmAssets/deleteBatch',
        exportXlsUrl: '/alarmAssets/alarmAssets/exportXls',
        importExcelUrl: 'alarmAssets/alarmAssets/importExcel',
      }
    }
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
  },
 activated() {
    this.loadData()
 },
  methods: {
    handleDetailPage: function (record) {
      this.$parent.pButton1(1, record)
    },
    //表单查询,点击查询按钮，默认查询第一页
    dosearch() {
      this.loadData(1)
    },
    //表单重置
    doreset() {
      //重置form表单，不重置tree选中节点
      this.queryParam = {}
      this.loadData(1)
    },
    handleDetail: function (record) {
      this.$refs.modalForm.edit(record)
      this.$refs.modalForm.title = '详情'
      this.$refs.modalForm.disableSubmit = true
    },
    //删除
    deleteRecord(record) {
      if (!this.url.deleteBatch) {
        this.$message.error('请设置url.delete属性!')
        return
      }
      var that = this
      this.$confirm({
        title: '确认删除',
        okText: '是',
        cancelText: '否',
        content: '是否删除选中数据?',
        onOk: function () {
          that.loading = true
          deleteAction(that.url.deleteBatch, { ids: record.id }).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.loadData()
            } else {
              that.$message.warning(res.message)
            }
          })
        },
      })
    },
  },
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
</style>