<template>
  <a-row :gutter='10' style='height: 100%;' class='vScroll'>
    <a-col style='width:100%;height: 100%;display: flex;flex-direction: column'>
      <!-- 查询区域 -->
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0', marginRight: '12px' }" class='card-style'
              style='width: 100%'>
        <div class='table-page-search-wrapper'>
          <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
            <a-row :gutter='24' ref='row'>
              <a-col :span='spanValue'>
                <a-form-item label='设备名称'>
                  <a-input :maxLength='maxLength' placeholder='请输入设备名称' v-model='queryParam.name' :allowClear='true'
                           autocomplete='off' />
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label='IP地址'>
                  <a-input :maxLength='maxLength' placeholder='请输入IP地址' v-model='queryParam.ip' :allowClear='true'
                           autocomplete='off' />
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label='在线状态'>
                  <j-dict-select-tag v-model='queryParam.status' placeholder='请选择设备状态' dictCode='device_status' />
                </a-form-item>
              </a-col>
              <a-col :span='spanValue' v-show='toggleSearchStatus'>
                <a-form-item label='告警状态'>
                  <j-dict-select-tag v-model='queryParam.alarmStatus' placeholder='请选择告警状态'
                                     dictCode='device_alarm_status' />
                </a-form-item>
              </a-col>
              <a-col :span='spanValue' v-show='toggleSearchStatus'>
                <a-form-item label='启用状态'>
                  <j-dict-select-tag v-model='queryParam.enable' placeholder='请选择启用状态'
                                     dictCode='device_enable_status' />
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'  v-show='toggleSearchStatus'>
                <a-form-item label="产品名称">
                  <a-select v-model="queryParam.productId" placeholder="请选择产品名称"  :show-search='true'
                            :getPopupContainer='(node) => node.parentNode' option-filter-prop='label' :allow-clear='true'>
                    <a-select-option v-for="item in productList" :label='item.proName' :value='item.proId'
                                     :key="item.proId">
                      {{ item.proName }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span='colBtnsSpan()'>
                <span class='table-page-search-submitButtons'
                      :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button type='primary' class='btn-search btn-search-style' @click='dosearch'>查询</a-button>
                  <a-button class='btn-reset btn-reset-style' @click='searchReset'>重置</a-button>
                  <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <div class='table-operator table-operator-style'>
          <a-button type='primary' @click="handleExport('安全设备信息表')">导出</a-button>
          <a-popover title='监控状态说明'>
            <template slot='content'>
              <div style='display:flex;'>
                <div>
                  <span>在线：</span>
                  <img src='../../../assets/bigScreen/28.png' alt='' class='stateImg' />
                </div>
                <div class='stateBox'>
                  <span>离线：</span>
                  <img src='../../../assets/bigScreen/57.png' alt='' class='stateImg' />
                </div>
                <div class='stateBox'>
                  <span>告警：</span>
                  <img src='../../../assets/bigScreen/56.png' alt='' class='stateImg' />
                </div>
                <div class='stateBox'>
                  <span style='margin-left: 5px'>禁用：</span>
                  <a-icon type='stop' theme='twoTone' two-tone-color='#eb2f96' style='font-size:16px'
                          class='stateImg' />
                  <span style='margin-left: 5px'></span>
                </div>
              </div>
            </template>
            <a-icon type='question-circle' theme='twoTone' style='font-size: 18px' />
          </a-popover>
        </div>
        <a-table
          ref='table'
          bordered
          :row-key='(record,index)=>{return record.id}'
          :columns='columns'
          :dataSource='dataSource'
          :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
          :pagination='ipagination'
          :loading='loading'
          @change='handleTableChangePage'>
          <template slot='htmlSlot' slot-scope='text' width='500'>
            <div v-html='text'></div>
          </template>
          <template slot='imgSlot' slot-scope='text'>
            <span v-if='!text' style='font-size: 14px'>无图片</span>
            <img v-else :src='getImgView(text)' height='25px' alt='' style='max-width: 80px; font-size: 14px' />
          </template>

          <template slot='status' slot-scope='text, record'>
                <span v-if='record.enable == 1'>
                  <img v-if='record.status==1' src='../../../assets/bigScreen/28.png' alt='' class='stateImg' />
                  <img v-else src='../../../assets/bigScreen/57.png' alt='' class='stateImg' />
                  <img v-if='record. alarmStatus==1' src='../../../assets/bigScreen/56.png' alt=''
                       class='stateImg alarmStatus' />
                </span>
            <span v-else>
                  <a-icon type='stop' theme='twoTone' two-tone-color='#eb2f96' style='font-size: 16px'
                          class='stateImg ' />
                </span>
            <span style='margin-left: 10px'>{{ text }}</span>
          </template>

          <span slot='action' class='caozuo' slot-scope='text, record'>
            <a @click='handleDetailPage(record)'>查看</a>
          </span>
          <template slot='Enable' slot-scope='text,record'>
            <div v-if='record.enable === 1'>
              <!-- <a-icon type="check-circle" theme="twoTone" two-tone-color="#52c41a" /> -->
              <a-icon type='pause-circle' theme='twoTone' two-tone-color='#52c41a' style='font-size:20px' />
              <!-- <span style="margin-left: 5px">{{record.name}}</span> -->
            </div>
            <div v-else>
              <!-- <a-icon type="stop" theme="twoTone" two-tone-color="#eb2f96"/> -->
              <a-icon type='play-circle' theme='twoTone' two-tone-color='#eb2f96' style='font-size:20px' />
              <!-- <span style="margin-left: 5px">{{record.name}}</span> -->
            </div>
          </template>

          <template slot='webManageUrl' slot-scope='text'>
            <a @click='openWebManageUrl(text)' class='overlay'>{{text}}</a>
          </template>
          <template slot='tooltip' slot-scope='text'>
            <a-tooltip placement='topLeft' :title='text' trigger='hover'>
              <div class='tooltip'>
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
      </a-card>
    </a-col>
  </a-row>
</template>

<script>
import '@assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { getAction } from '@api/manage'
import JDictSelectTag from '@comp/dict/JDictSelectTag.vue'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
export default {
  name: 'SafetyDeviceList',
  mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
  components: {
    JDictSelectTag
  },
  data() {
    return {
      maxLength:50,
      description: '虚拟化管理页面',
      formItemLayout: {
        labelCol: {style: 'width:80px'},
        wrapperCol: {
          style: 'width:calc(100% - 80px)'
        }
      },
      productList:[],
      // 表头
      columns: [
        {
          title: '设备名称',
          dataIndex: 'name',
          scopedSlots: { customRender: 'status' },
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
            return { style: cellStyle }
          }
        },
        {
          title: 'IP地址',
          dataIndex: 'ip',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 150px;max-width:300px'
            return { style: cellStyle }
          }
        },
        {
          title: '产品名称',
          dataIndex: 'productName',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 150px;max-width:300px'
            return { style: cellStyle }
          }
        },
        {
          title: 'web管理地址',
          dataIndex: 'webManageUrl',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 150px;max-width:300px'
            return { style: cellStyle }
          },
          scopedSlots: { customRender: 'webManageUrl' },
        },
        {
          title: '设备说明',
          dataIndex: 'description',
          scopedSlots: { customRender: 'tooltip' },
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 120px;max-width:400px'
            return { style: cellStyle }
          }
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 140,
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: 'net/device/getDeviceByCategoryDict',
        exportXlsUrl: '/net/device/devByCategoryDictExportXls',
        queryAllProducts: '/net/device/productListByCategoryDict', // 产品名称查询
      },
      disableMixinCreated: true,
      queryParam:{
        dictCode : 'Security'
      },
      queryDictCode:'Security',
      exportFields:["name","status","alarmStatus","enable" ,"ip", "productName","webManageUrl","description"],
    }
  },
  computed: {
    importExcelUrl: function() {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    }
  },
  created() {
    this.queryAllProducts()
  },
  activated() {
    this.queryParam.dictCode = this.queryDictCode
    if(this.queryParam.exportFields){
      delete this.queryParam.exportFields
    }
    this.loadData()
  },
  mounted() {
    // this.loadData(1)
  },
  methods: {
    //获取所有产品信息
    queryAllProducts() {
      if (!this.url.queryAllProducts) {
        this.$message.error('请设置url.queryAllProducts!')
        return
      }
      getAction(this.url.queryAllProducts,{dictCode:this.queryParam.dictCode}).then((res) => {
        this.productList = []
        if (res.success) {
          if (res.result.length > 0) {
            res.result.map((item) => {
              let param = {
                proId: item.id,
                proName: item.displayName,
              }
              this.productList.push(param)
            })
          }
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    handleDetailPage: function(record) {
      this.$parent.pButton2(1, record)
    },
    openWebManageUrl(url){
      window.open(url,'_blank')
    },
    handleTableChangePage(pagination, filters, sorter){
      if(this.queryParam.exportFields){
        delete this.queryParam.exportFields
      }
      this.handleTableChange(pagination, filters, sorter)
    },
    //表单查询,点击查询按钮，默认查询第一页
    searchQuery() {
      if(this.queryParam.exportFields){
        delete this.queryParam.exportFields
      }
      this.loadData(1)
    },
    dosearch() {
      if(this.queryParam.exportFields){
        delete this.queryParam.exportFields
      }
      this.loadData(1)
    },
    searchReset() {
      this.queryParam = {}
      this.queryParam.dictCode =this.queryDictCode
      this.loadData(1)
    },
    handleExport(fileName){
      this.queryParam.exportFields=this.exportFields.join();
      this.handleExportXls(fileName)
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';

.stateBox {
  margin-left: 20px;
}

.stateImg {
  vertical-align: middle
}

.alarmStatus {
  margin-left: 8px;
}

.overlay {
  color: #409eff
}
</style>