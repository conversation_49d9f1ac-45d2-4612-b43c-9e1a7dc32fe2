<template>
  <div class="oneClickHelp" ref="bodyBox">
    <div class="header" ref="headerBox">
      <img v-show="logoShow" :src="logoUrl" alt="" />
      <div>
        <span>运维助手</span>
      </div>
    </div>
    <div class="oneClickHelp-body" :style="{height:centerH}">
      <a-tabs default-active-key="1" size="large">
        <a-tab-pane key="1" tab="人工服务">
          <div class="tabBody" :style="{height:tabBodyH}">
            <artificial-Services></artificial-Services>
          </div>
        </a-tab-pane>
        <!-- <a-tab-pane key="2" tab="常见问题">
          <div class="tabBody" :style="{height:tabBodyH}">
            <common-Problem></common-Problem>
          </div>
        </a-tab-pane> -->
        <a-tab-pane key="3" tab="我的问题">
          <div class="tabBody" :style="{height:tabBodyH}">
            <my-Question></my-Question>
          </div>
        </a-tab-pane>
        <a-tab-pane key="4" tab="驱动管理">
          <div class="tabBody" :style="{height:tabBodyH}">
            <drive-management :terminal="terminalCode"></drive-management>
          </div>
        </a-tab-pane>
        <a-tab-pane key="5" tab="知识库">
          <div class="tabBody knowledgeTab" :style="{height:tabBodyH}">
            <knowledge-search :terminal="terminalCode"></knowledge-search>
          </div>
        </a-tab-pane>
        <a-tab-pane key="6" tab="我的知识">
          <div class="tabBody knowledgeTab" :style="{height:tabBodyH}">
            <my-knowledge></my-knowledge>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>
    <div ref="copyright" class="oneClickHelp-bottom">{{loginSysName}}</div>
  </div>
</template>
<script>
  import artificialServices from './artificialServices.vue'
  import commonProblem from './commonProblem.vue'
  import driveManagement from './driveManagement.vue'
  import myQuestion from './myQuestion.vue'
  import knowledgeSearch from '@views/opmg/knowledgeManagement/knowledgeSearch/knowledgeSearch.vue'
  import myKnowledge from '@views/opmg/knowledgeManagement/myKnowledge/myKnowledge.vue'
  import { getHostNameLocal } from '../../utils/util'
  export default {
    components: {
      artificialServices,
      commonProblem,
      driveManagement,
      myQuestion,
      knowledgeSearch,
      myKnowledge
    },
    data() {
      return {
        terminalCode: '',
        logoShow: window._CONFIG['logoShow'] === 1 ? true : false,
        centerH: "80%",
        tabBodyH: '520px',
        logoUrl: process.env.NODE_ENV === 'production' ? window._CONFIG['helpLogoUrl'] : require(
          '@/assets/logo-white2.png'),
        loginSysName: window._CONFIG['loginSysName']
      }
    },
    mounted() {
      let rect = this.$refs.bodyBox.getBoundingClientRect()
      let hb = this.$refs.headerBox.getBoundingClientRect();
      let cb = this.$refs.copyright.getBoundingClientRect();
      let ch = rect.height - hb.height - cb.height - 16 * 3;
      this.centerH = ch + "px"
      this.tabBodyH = ch - 55 - 16 - 16 + "px"
      this.getHost()
    },
    methods: {
      getHost() {
        this.terminalCode = getHostNameLocal()
      },
      // 自动获取地址栏的参数
      getQueryStringRegExp(name) {
        var reg = new RegExp('(^|\\?|&)' + name + '=([^&]*)(\\s|&|$)', 'i')
        if (reg.test(location.href)) {
          return unescape(RegExp.$2.replace(/\+/g, ' '))
        }

        return null
      },
    },
  }
</script>
<style lang="less" scoped>
  ::v-deep .ant-tabs-nav-container {
    margin-top: 0px;
  }

  ::v-deep .body-wrapper-r {
    height: calc(100vh - 280px);
  }

  ::v-deep .knowledgeTab .ant-row:nth-child(1) {
    background-color: #f5f5f5;
  }

  .oneClickHelp {
    width: 100%;
    height: 100%;
    background: #e4e4e4;

    .header {
      width: 100%;
      height: 0.75rem;
      background: #fff;
      padding: 0 15%;
      display: flex;
      align-items: center;

      img {
        width: 0.45rem
          /* 36/80 */
        ;
        height: 0.45rem
          /* 36/80 */
        ;
      }

      div {
        display: flex;
        flex-direction: column;
        padding-left: 0.2rem
          /* 16/80 */
        ;

        span:nth-child(1) {
          font-size: 0.3rem
            /* 24/80 */
          ;
          font-weight: 600;
          letter-spacing: 2px;
          color: #1e3676;
        }

        span:nth-child(2) {
          font-size: 0.15rem
            /* 12/80 */
          ;
        }
      }
    }

    .oneClickHelp-body {
      margin: 16px auto;
      width: 70%;
      /* height: 80%;*/
      background: #fff;

      .tabBody {
        width: 100%;
        // height: 100%;
        /* padding: 0 30px;*/
        // padding-top: 16px;
      }
    }

    .oneClickHelp-bottom {
      text-align: center;
      color: #8a8a8a;
    }
  }
</style>