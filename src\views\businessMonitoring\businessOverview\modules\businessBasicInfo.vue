<template>
  <div>
    <a-descriptions bordered  :column="2">
      <a-descriptions-item label='业务名称' >{{ getContext(record.businessName) }}</a-descriptions-item>
      <a-descriptions-item label='业务状态' >{{ getContext(record.restHeartbeat? record.restHeartbeat.responseResult:'') }}</a-descriptions-item>
      <a-descriptions-item label='业务类型' >{{ getContext(record.businessTypeText)}}</a-descriptions-item>
      <a-descriptions-item label='健康等级' >{{ getContext(record.healthLevel) }}</a-descriptions-item>
      <a-descriptions-item label='响应代码' >{{ getContext(record.restHeartbeat? record.restHeartbeat.responseCode:'') }}</a-descriptions-item>
      <a-descriptions-item label='响应时间'>{{ getContext(record.restHeartbeat? record.restHeartbeat.responseTime:'')}}</a-descriptions-item>
      <a-descriptions-item label='访问地址' >{{ getContext(record.businessAddress) }}</a-descriptions-item>
<!--      中软不需要该功能-->
<!--      <a-descriptions-item label='可用监控' :span="2">{{avaMoniNum}}个任务-->
<!--        <a v-if='avaMoniNum' @click='jumpRouter(routerInfo)' style='margin-left: 10px;color: #40a9ff'>详情</a>-->
<!--      </a-descriptions-item>-->
      <a-descriptions-item label='业务描述' >

        {{ getContext(record.businessRemark) }}
      </a-descriptions-item>
      <a-descriptions-item label='单位'>{{ getContext(record.deptId_dictText) }}</a-descriptions-item>
      <a-descriptions-item label='图片'>
        <a v-if="getContext(record.businessImage)" @click="previewFun(record.businessImage)">下载</a>
        <span v-else>--</span>
      </a-descriptions-item>
    </a-descriptions>
  </div>
</template>

<script>
import { getAction,downloadFile, getFileAccessHttpUrl } from '@api/manage'
export default {
  name:'businessBasicInfo',
  data() {
    return {
      record: {},
      businessTypeList: [],
      avaMoniNum:0,
      routerInfo:{
          routerName: 'businessMonitoring-availableMonitoring-availableMonitoringManage',
          redirect: 'businessMonitoring/availableMonitoring/availableMonitoringManage',
          component: 'businessMonitoring/availableMonitoring/availableMonitoringManage',
          params: {
            bizInfoName: '/business/availability/queryMumByBizId',
          }
        },
      url:{
        avaMoniNum:'/business/availability/queryMumByBizId'
      }
    }
  },

  methods: {
    show(record) {
      this.record = record
      getAction(this.url.avaMoniNum,{id:record.id}).then(res=>{
        if(res.success){
          this.avaMoniNum=res.result
        }
        else {
          this.$message.warning(res.message)
        }
      }).catch((err)=>{
        this.$message.warning(err.message)
      })
    },
    getContext(text){
      if(text==null||text==undefined||text==''){
        return '--'
      }
      else {
        return text
      }
    },
    jumpRouter(routerInfo){
      routerInfo.params.bizInfoName=this.record.businessName
      this.$router.push({
        name: routerInfo.routerName,
        path: routerInfo.redirect,
        params: routerInfo.params
      });
    },
    previewFun(imgeUrl){
      let url = getFileAccessHttpUrl(imgeUrl)
      downloadFile(url,imgeUrl)
    }
  }
}
</script>

<style scoped>
::v-deep .ant-descriptions-view {
  border-radius: 0px;
}

::v-deep .ant-descriptions-bordered .ant-descriptions-item-label {
  background-color: rgb(250, 250, 250);
  text-align: center;
  width: 17%;
}

::v-deep .ant-descriptions-item-label,
.ant-descriptions-item-content {
  color: rgb(96, 98, 102) !important;
}

::v-deep .ant-descriptions-bordered .ant-descriptions-item-content {
  width: 35%;
  word-break: break-all;
}
</style>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
</style>
