<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll zhl zhll">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <!-- 查询区域 -->
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }">
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="设备名称">
                  <a-input :maxLength='maxLength' placeholder="请输入设备名称" v-model="queryParam.name" :allowClear='true' autocomplete='off'/>
                </a-form-item>
              </a-col>
              <a-col :span="colBtnsSpan()">
                <span
                  class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                >
                  <a-button type="primary" class="btn-search btn-search-style" @click="dosearch">查询</a-button>
                  <a-button class="btn-reset btn-reset-style" @click="searchReset">重置</a-button>
                  <a v-if="isVisible" class="btn-updown-style" @click="doToggleSearch">
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <!-- 查询区域-END -->

      <!-- table区域-begin -->
      <div style="width: 100%; flex: auto; background-color: rgba(255, 255, 255, 0)">
        <!--网关无内容，提示暂无数据     -->
        <div class="div-empty-container" v-show="showTips">
          <div class="ant-empty ant-empty-normal">
            <div class="ant-empty-image">
              <svg width="64" height="41" viewBox="0 0 64 41" xmlns="http://www.w3.org/2000/svg">
                <g transform="translate(0 1)" fill="none" fill-rule="evenodd">
                  <ellipse fill="#F5F5F5" cx="32" cy="33" rx="32" ry="7" />
                  <g fill-rule="nonzero" stroke="#D9D9D9">
                    <path
                      d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"
                    />
                    <path
                      d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z"
                      fill="#FAFAFA"
                    />
                  </g>
                </g>
              </svg>
            </div>
            <p class="ant-empty-description" style="color: rgba(0, 0, 0, 0.35) !important">暂无数据</p>
          </div>
        </div>
        <div class="div-table-container" v-show="!showTips">
          <a-row :gutter="[16, 16]" type="flex" align="middle">
            <a-col v-bind="CardColLayout" v-for="(item, index) in dataSource" :key="index">
              <div class="gutter-box cardCont">
                <div class="cardContOne">
                  <div>
                    <img src="~@assets/bigIcon.svg" alt />
                  </div>
                  <div>
                    <a-tooltip placement="topLeft">
                      <template slot="title">
                        <span >{{ item.name }}</span>
                      </template>
                      <p
                        class="oPFir"
                        style="
                          color: #409eff;
                          width: 2.25rem;
                          overflow: hidden;
                          text-overflow: ellipsis;
                          white-space: nowrap;
                          cursor: pointer;
                        "
                        @click='showGatewayInfo(item)'
                      >
                        {{ item.name }}
                      </p>
                    </a-tooltip>
                    <p class="oPTwo">{{ item.productName }}</p>
                  </div>
                  <span style="position: absolute; right: 0; top: 3px; width: 40px">
                    <!-- 批量绑定网关子设备 -->
                    <a-icon type="plus" class="add-icon" @click="handleAddChild(item.id)" />
                    <!-- 批量解绑网关子设备 -->
                    <a-icon type="fullscreen" class="fullscreen-icon" @click="getSelfChild(item.id, item.name)" />
                  </span>
                  <div style="position: absolute; right: 45px; bottom: 0; width: 50px">
                    <p class="oPTwo" style="margin-right: 79px; width: 100%; text-align: right" v-if="item.status == 1">
                      <span class="box"></span>
                      <span>在线</span>
                    </p>
                    <p
                      class="oPTwo"
                      style="margin-right: 79px; width: 100%; text-align: right"
                      v-else-if="item.status == 2"
                    >
                      <span class="boxReds"></span>
                      <span>告警</span>
                    </p>
                    <p class="oPTwo" style="margin-right: 79px; width: 100%; text-align: right" v-else>
                      <span class="boxReds"></span>
                      <span>离线</span>
                    </p>
                  </div>
                </div>
                <div style='display: flex' v-if='item.jvmInfo'>
                  <div style='margin-right: 12px' v-for='jvm in item.jvmInfo' :key='jvm.code' v-if='jvm'>{{jvm.name}}：{{jvm.display[0].value+jvm.display[0].unit}}</div>
                </div>
                <gateway-device-info-card
                  ref="gatewayCard"
                  :parentId="item.id"
                  :changed="changed"
                ></gateway-device-info-card>
              </div>
            </a-col>
          </a-row>
        </div>
        <div style="text-align: right; margin-top: 16px" v-show="!showTips">
          <a-pagination
            show-quick-jumper
            show-size-changer
            :hideOnSinglePage="true"
            :default-current="ipagination.current"
            :total="ipagination.total"
            @change="onChange"
            :page-size="ipagination.pageSize"
            :pageSizeOptions="ipagination.pageSizeOptions"
            :show-total="(total) => `共 ${ipagination.total} 条`"
            @showSizeChange="onShowSizeChange"
            size="small"
          ></a-pagination>
        </div>
      </div>
      <gateway-device-info-modal ref="gatewayChild" @ok="modalFormOk"></gateway-device-info-modal>
      <gateway-child-device-info-modal ref="selfGatewayChild" @ok="refreshData"></gateway-child-device-info-modal>
    </a-col>
  </a-row>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
// import DeviceInfoModal from './modules/DeviceInfoModal'
import DeviceInfo from './deviceshow/DeviceInfoModal'
import GatewayDeviceInfoCard from './modules/GatewayDeviceInfoCard'
import GatewayDeviceInfoModal from './modules/GatewayDeviceInfoModal'
import GatewayChildDeviceInfoModal from './modules/GatewayChildDeviceInfoModal'
import JSuperQuery from '@/components/jeecg/JSuperQuery.vue'
import { queryAssetsCategoryTreeList } from '@/api/device'
import { httpAction, getAction, deleteAction } from '@/api/manage'
//引入公共devicetree组件
// import DeviceTree from '@/components/tree/DeviceTree.vue'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
export default {
  name: 'GatewayDeviceInfoList',
  mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
  components: {
    GatewayDeviceInfoModal,
    GatewayChildDeviceInfoModal,
    DeviceInfo,
    JSuperQuery,
    getAction,
    // 'device-tree': DeviceTree,
    GatewayDeviceInfoCard,
  },
  data() {
    return {
      maxLength:50,
      CardColLayout: {
        xxl: { span: 6 },
        xl: { span: 8 },
        lg: { span: 8 },
        md: { span: 12 },
        sm: { span: 12 },
        xs: { span: 24 },
      },
      showTips: false,
      changed: false,
      //设备数量
      deviceInfo: {
        allNum: '',
        outNum: '',
        onNum: '',
        disabledNum: '',
      },
      ipagination: {
        current: 1,
        pageSize: 8,
        pageSizeOptions: ['8', '16', '24'],
        showTotal: (total, range) => {
          return range[0] + '-' + range[1] + ' 共' + total + '条'
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0,
      },
      description: '设备表管理页面',
      firstTitle: '', //存储搜素tree的第一个title
      // 树
      assetsCategoryTree: [],
      treeData: [],
      expandedKeys: [],
      searchValue: '',
      autoExpandParent: true,
      dropTrigger: '',
      selectedKeys: [],
      selectedTitle: '',
      checkedKeys: [],
      checkStrictly: true,
      // iExpandedKeys: [],
      currFlowId: '',
      currFlowName: '',
      rightClickSelectedBean: {},
      batchEnable: 1,
      // 表头
      columns: [
        {
          title: '设备名称',
          dataIndex: 'name',
        },
        {
          title: '产品名称',
          dataIndex: 'productName',
        },
        {
          title: '关联资产',
          dataIndex: 'assetsName',
        },
        {
          title: '通信协议',
          dataIndex: 'transferProtocol',
        },
        {
          title: '添加时间',
          dataIndex: 'createTime',
        },
        {
          title: '监控状态',
          dataIndex: 'status',
          scopedSlots: { customRender: 'status' },
        },
        {
          title: '设备说明',
          dataIndex: 'description',
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 147,
          scopedSlots: { customRender: 'action' },
        },
      ],
      url: {
        list: '/gateway/deviceInfo/list',
        delete: '/gateway/deviceInfo/delete',
        deleteBatch: '/gateway/deviceInfo/deleteBatch',
        deviceEnable: '/gateway/deviceInfo/updateEnable',
        batchEnable: '/gateway/deviceInfo/batchUpdateEnable',
        deviceInfoUrl: '/gateway/deviceInfo/showCounts',
      },
      dataSource: [], //table数据
      dictOptions: {},
      superFieldList: [],
      name: '',
      status: '',
      statuslist: [
        {
          name: '在线',
          code: '1',
        },
        {
          name: '离线',
          code: '0',
        },
        {
          name: '告警',
          code: '2',
        },
      ],
      categoryId: '', //选取tree的key
      childsData: '',
    }
  },
  // created() {
  // this.getSuperFieldList();
  // },
  created() {
    //this.getDeviceInfoMap();
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
  },
  mounted() {
    // this.loadData()
  },
  methods: {
    showGatewayInfo(item){
      this.$parent.pButton2(1, item)
    },
    refreshData() {
      this.loadData()
      this.changed = true
    },
    loadData(arg) {
      //
      // alert(this.url.list);
      if (!this.url.list) {
        this.$message.error('请设置url.list属性!')
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1
      }
      var params = this.getQueryParams() //查询条件
      this.loading = true
      let that = this
      getAction(that.url.list, params).then((res) => {
        if (res.success) {
          //update-begin---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------

          that.dataSource = res.result.records || res.result
          //author:weng    Date:20210402  for：if(res.result.total>0) 有错误，无查询结果时，页码显示有问题
          that.ipagination.total = res.result.total
          // if(res.result.total>0)
          // {
          //   this.ipagination.total = res.result.total;
          // }
          //update-end---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
          //
          // that.getChildData()
        } else {
          this.showTips = true
        }
        if (res.code === 510) {
          that.$message.warning(res.message)
        }
        that.loading = false
      })
    },

    onShowSizeChange(current, pageSize) {
      this.ipagination.pageSize = pageSize
      this.ipagination.current = current
      this.loadData()
    },
    onChange(pageNumber, pageSize) {
      this.ipagination.pageSize = pageSize
      this.ipagination.current = pageNumber
      if (pageNumber != 1) {
        this.tabShow = false
        this.ipagination.pageSize = 8
        this.ipagination.pageSizeOptions = ['8', '16', '24']
      } else {
        this.tabShow = true
        this.ipagination.pageSize = 7
        this.ipagination.pageSizeOptions = ['7', '15', '23']
      }
      this.loadData()
    },
    handleAdd: function () {
      this.$refs.modalForm.add()
      this.$refs.modalForm.title = '新增'
      this.$refs.modalForm.disableSubmit = false
      this.loadData()
    },
    initDictConfig() {},
    handleAddChild(id) {
      this.$refs.gatewayChild.title = '未绑定设备列表'
      this.$refs.gatewayChild.show(id)
      //this.$refs.gatewayCard.getChildData()
    },
    getSelfChild(id, name) {
      this.changed = false
      this.$refs.selfGatewayChild.title = name + '已绑定设备列表'
      this.$refs.selfGatewayChild.show(id)
    },
    //查看详情

    handleCard() {
      this.$refs.gatewayCard.getChildData()
    },

    //删除
    deleteRecord(record) {
      if (!this.url.delete) {
        this.$message.error('请设置url.delete属性!')
        return
      }
      var that = this
      this.$confirm({
        title: '确认删除',
        okText: '是',
        cancelText: '否',
        content: '是否删除选中数据?',
        onOk: function () {
          that.loading = true
          deleteAction(that.url.deleteBatch, { ids: record.id }).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.loadData()
            } else {
              that.$message.warning(res.message)
            }
          })
        },
      })
    },
    //勾选时进行状态判断
    onSelectChange(selectedRowKeys, selectionRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectionRows = selectionRows
      let disableFlag = this.selectionRows.some((ele) => ele.enable === 0)
      let ableFlag = this.selectionRows.some((ele) => ele.enable === 1)
      if (disableFlag && ableFlag) {
        this.batchEnable = 2
      } else if (disableFlag) {
        this.batchEnable = 0
      } else {
        this.batchEnable = 1
      }
    },
    //设备禁用/启用
    deviceEnable(record) {
      let enable = record.enable
      getAction(this.url.deviceEnable, { enable: enable === 1 ? 0 : 1, deviceId: record.id }).then((res) => {
        if (res.success) {
          if (enable === 1) {
            this.$message.success('禁用成功!')
          } else {
            this.$message.success('启用成功!')
          }
          // this.ipagination.cur
          this.loadData()
        }
      })
    },
    //批量禁用、启用
    batchEnableOperate(enable) {
      if (!this.url.batchEnable) {
        this.$message.error('请设置url.batchConfirm属性!')
        return
      }
      if (this.selectedRowKeys.length <= 0) {
        this.$message.warning('请选择一条记录！')
        return
      } else {
        var ids = ''
        for (var a = 0; a < this.selectedRowKeys.length; a++) {
          ids += this.selectedRowKeys[a] + ','
        }
        var that = this
        this.$confirm({
          title: '确认操作',
          okText: '是',
          cancelText: '否',
          content: '是否确定修改选中数据?',
          onOk: function () {
            that.loading = true
            getAction(that.url.batchEnable, { ids: ids, enable: enable })
              .then((res) => {
                if (res.success) {
                  that.$message.success(res.message)
                  that.loadData()
                  that.onClearSelected()
                } else {
                  that.$message.warning(res.message)
                }
              })
              .finally(() => {
                that.loading = false
              })
          },
        })
      }
    },
    refresh() {
      this.loading = true
    },
    getDeviceInfoMap(flag = false) {
      getAction(this.url.deviceInfoUrl).then((res) => {
        if (res.success) {
          this.deviceInfo.allNum = res.result.all
          this.deviceInfo.disabledNum = res.result.disable
          this.deviceInfo.onNum = res.result.on
          this.deviceInfo.outNum = res.result.out
          if (flag) {
            this.$message.success('同步成功')
          }
        }
      })
    },
    //查询对应资产类型或产品下的所有设备的信息
    selectDevice(option = '', type = null) {
      this.option = option
      this.type = type
      getAction(this.url.list, { option: this.option, type: this.type, name: this.name, status: this.status }).then(
        (res) => {
          if (res.success) {
            this.dataSource = res.result.records
            this.ipagination.total = res.result.total
          }
        }
      )
    },
    //重新加载数据
    reloadData() {
      getAction(this.url.list, { option: this.option, type: this.type, name: this.name, status: this.status }).then(
        (res) => {
          if (res.success) {
            this.dataSource = res.result.records
            if (res.result.total) {
              this.ipagination.total = res.result.total
            }
          }
        }
      )
    },
    // 触发onSelect事件时,为部门树右侧的form表单赋值
    setValuesToForm(record) {
      this.queryParam.configType = record.key
      this.loadData()
    },
    onCheck(checkedKeys, e) {
      this.hiding = false
      // this.checkedKeys = checkedKeys.checked
      // <!---- author:os_chengtgen -- date:20190827 --  for:切换父子勾选模式 =======------>
      if (this.checkStrictly) {
        this.checkedKeys = checkedKeys.checked
      } else {
        this.checkedKeys = checkedKeys
      }
      // <!---- author:os_chengtgen -- date:20190827 --  for:切换父子勾选模式 =======------>
    },
    // 右键操作方法
    rightHandle(node) {
      this.dropTrigger = 'contextmenu'
      this.rightClickSelectedKey = node.node.eventKey
      this.rightClickSelectedBean = node.node.dataRef
    },
    onExpand(expandedKeys) {
      // if not set autoExpandParent to false, if children expanded, parent can not collapse.
      // or, you can remove all expanded children keys.
      this.expandedKeys = expandedKeys
      this.autoExpandParent = false
    },
    getSuperFieldList() {
      let fieldList = []
      fieldList.push({ type: 'string', value: 'name', text: '名称', dictCode: '' })
      fieldList.push({ type: 'string', value: 'productId', text: '产品id', dictCode: '' })
      fieldList.push({ type: 'string', value: 'categoryId', text: '类型id', dictCode: '' })
      fieldList.push({ type: 'string', value: 'assetsId', text: '资产id', dictCode: '' })
      fieldList.push({ type: 'string', value: 'deviceCode', text: '设备唯一标识', dictCode: '' })
      fieldList.push({ type: 'string', value: 'description', text: '描述', dictCode: '' })
      this.superFieldList = fieldList
    },
    //表单查询,点击查询按钮，默认查询第一页
    dosearch() {
      this.loadData(1)
    },
    treeSeletedSearch(option = '', type = '') {
      this.queryParam.option = option
      this.queryParam.type = type
      this.loadData(1)
    },
    //表单重置
    doreset() {
      //重置form表单，不重置tree选中节点
      this.queryParam.name = ''
      this.queryParam.status = ''
    },
    handleDetailPage: function (record) {
      this.$parent.pButton1(1, record)
    },
  },
}
</script>
<style scoped>
@import '~@assets/less/common.less';
.form-row {
  display: flex;
  margin: 0px 0px !important;
  align-items: center;
  height: 69px;
  background-color: white;
  padding: 0 12px;
}
.form-col {
  height: 34px;
}
.div-table-container {
  /*height: calc(100% - 85px);*/
}
.gutter-example {
  background-color: #ececec;
  margin-bottom: 10px;
}
.gutter-row {
  padding-right: 0px !important;
}
.posi-col {
  position: relative;
}
.sync-img {
  position: absolute;
  top: 8px;
  right: 19px;
  cursor: pointer;
}
.gutter-example >>> .ant-row > div {
  background: #f0f2f5;
  border: 0;
}
.gutter-box {
  background: white;
  padding: 21px 0;
}
.p-device-status {
  text-align: center;
  height: 30px;
  line-height: 30px;
  margin-bottom: 0px;
}
.span-title {
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
}
.span-num {
  font-family: PingFangSC-Medium;
  font-size: 24px;
}
.color-blue {
  color: #409eff;
}
.color-green {
  color: #139b33;
}
.color-red {
  color: #df1a1a;
}
.color-grey {
  color: #868686;
}
.table-operator {
  margin-bottom: 10px;
}
.cardAdd,
.cardCont {
  width: calc(100% / 4 - 12px);
  position: relative;
  background: #ffffff;
  -webkit-box-shadow: 0 3px 7px -1px rgba(0, 0, 0, 0.16);
  box-shadow: 0 3px 7px -1px rgba(0, 0, 0, 0.16);
  border-radius: 2px;
  border-radius: 2px;
  margin: 16px 0 0px 0px;
  height: 5.25rem /* 420/80 */;
}
.cardAdd {
  /* margin-left: 0!important; */
}
.cardDiv {
  text-align: center;
  position: absolute;
  top: 35%;
  left: 29%;
  right: 31%;
  bottom: 74px;
}
.cardDiv img {
  width: 61px;
  height: 61px;
  margin-bottom: 17px;
}
.cardDiv div {
  font-family: PingFangSC-Regular;
  font-size: 16px;
  color: #d7d7d7;
}
.gutter-example >>> .ant-row > div {
  background: transparent;
  border: 0;
}
.gutter-box {
  width: 100%;
}
.cardCont {
  position: relative;
  padding-top: 0.325rem /* 26/80 */;
  padding-right: 0.325rem /* 26/80 */;
  padding-bottom: 0px;
  padding-left: 0.325rem /* 26/80 */;
}
.cardContOne {
  overflow: hidden;
  /* padding: 22px 0px 0 0px; */
  position: relative;
}
.cardContOne div {
  float: left;
}
.cardContOne div img {
  width: 50px;
  height: 0.75rem /* 60/80 */;
  margin-right: 15px;
}

.cardContOne div p {
  margin-bottom: 0.1rem /* 8/80 */;
}
.add-icon {
  font-size: 16px;
  text-align: right;
  margin-bottom: -2px;
  cursor: pointer;
  margin-right: 8px;
}
.fullscreen-icon {
  font-size: 16px;
  text-align: right;
  margin-bottom: -2px;
  cursor: pointer;
}
.oPFir {
  font-family: PingFangSC-Regular;
  font-size: 0.2rem /* 16/80 */;
  color: rgba(0, 0, 0, 0.85);
}
.oPTwo {
  font-family: PingFangSC-Regular;
  font-size: 0.175rem /* 14/80 */;
  color: rgba(0, 0, 0, 0.65);
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.box {
  width: 8px;
  height: 8px;
  background: #13a40a;
  border-radius: 50%;
  display: inline-block;
  margin-right: 7px;
  margin-bottom: 1px;
}
.boxReds {
  width: 8px;
  height: 8px;
  background: red;
  border-radius: 50%;
  display: inline-block;
  margin-right: 7px;
  margin-bottom: 1px;
}
.boxRed {
  width: 8px;
  height: 8px;
  background: red;
  border-radius: 50%;
  display: inline-block;
  margin-right: 7px;
  margin-bottom: 1px;
}
.cardContTwo {
  padding: 26px 0px 0 0px;
}
.cardContTwoChild {
  text-align: center;
}
.oColor {
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
  padding: 10px 0px;
  border-bottom: 1px solid #dddddd;
  position: relative;
}
.tColor {
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
}
.cardContThree {
  width: 100%;
  text-align: center;
  padding: 14px 0 14px 0;
  border-radius: 0 0 2px 2px;
  border-radius: 0px 0px 2px 2px;
}
.cardContThree .cardContThreeCol {
  border-right: 1px solid #dadada;
}
.tCon {
  margin-right: 8px;
}

.div-empty-container {
  background-color: white;
  padding: 20px;
  margin-top: 16px;
  margin-bottom: 16px;
}

@media screen and (max-width: 1700px) {
  .oPTwo {
    width: 78%;
  }
}
@media screen and (max-width: 1550px) {
  .oPTwo {
    width: 50%;
  }
}
@media screen and (max-width: 1420px) {
  .oPTwo {
    width: 48%;
  }
}
</style>
