<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container>
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-item label="所属子网组" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-select v-decorator="['subnetGroupId', validatorRules.subnetGroupId]" :allowClear="true"
                @change="groupChange" placeholder="请选择所属子网组">
                <a-select-option v-for="item in groupList" :key="item.id">{{item.title}}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="所属子网" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-select v-decorator="['subnetId', validatorRules.subnetId]" :allowClear="true" placeholder="请选择所属子网">
                <a-select-option v-for="item in childList" :key="item.id">{{item.title}}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="网段名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['segmentName',validatorRules.segmentName]" :allowClear="true" autocomplete="off"
                placeholder="请输入网段名称">
              </a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="起始IP" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['startIp',validatorRules.startIp]" :allowClear="true" autocomplete="off"
                placeholder="请输入起始IP"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="终止IP" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['endIp',validatorRules.endIp]" :allowClear="true" autocomplete="off"
                placeholder="请输入终止IP"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="使用部门" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-select-depart v-model="model.departId"></j-select-depart>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="使用位置" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['location',validatorRules.location]" :allowClear="true" autocomplete="off"
                placeholder="请输入使用位置"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="备注" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-textarea :autoSize="{ minRows: 1, maxRows: 4 }" v-decorator="['remark',validatorRules.remark]"
                :allowClear="true" autocomplete="off" placeholder="请输入备注"></a-textarea>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
  import {
    httpAction,
    getAction
  } from '@/api/manage'
  import pick from 'lodash.pick'
  import JFormContainer from '@/components/jeecg/JFormContainer'
  import JDictSelectTag from '@/components/dict/JDictSelectTag'
  import JSelectDepart from '@/components/jeecgbiz/JSelectDepart'

  export default {
    name: 'segmentForm',
    components: {
      JFormContainer,
      JDictSelectTag,
      JSelectDepart
    },
    data() {
      return {
        form: this.$form.createForm(this),
        model: {},
        groupList: [],
        childList: [],
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          },
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          },
        },
        confirmLoading: false,
        validatorRules: {
          subnetGroupId: {
            rules: [{
              required: true,
              message: '请选择子网组'
            }]
          },
          subnetId: {
            rules: [{
              required: true,
              message: '请选择所属子网'
            }, ],
          },
          segmentName: {
            rules: [{
                required: true,
                message: '请输入网段名称'
              },
              {
                min: 2,
                message: '网段名称长度应在 2-20 之间！',
                trigger: 'blur'
              },
              {
                max: 20,
                message: '网段名称长度应在 2-20 之间！',
                trigger: 'blur'
              }
            ],
          },
          startIp: {
            rules: [{
              required: true,
              message: '请输入起始IP'
            }, {
              pattern: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
              message: '请输入正确的IP地址'
            }],
          },
          endIp: {
            rules: [{
              required: true,
              message: '请输入终止IP'
            }, {
              pattern: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
              message: '请输入正确的IP地址'
            }],
          },
          location: {
            rules: [{
              max: 50,
              message: '地址长度不超过50字符'
            }]
          },
          remark: {
            rules: [{
              max: 200,
              message: '备注长度不超过200字符'
            }]
          },
        },
        url: {
          add: '/devops/ip/segment/add',
          edit: '/devops/ip/segment/edit',
          queryById: '/devops/ip/segment/queryById',
          group: '/devops/ip/queryTree'
        },
      }
    },
    created() {
      this.getGroup()
    },
    methods: {
      getGroup() {
        getAction(this.url.group, {
          id: 0,
          type: 0
        }).then((res) => {
          this.groupList = res.result
        })
      },
      getChild(id) {
        getAction(this.url.group, {
          id: id,
          type: 1
        }).then((res) => {
          this.childList = res.result
        })
      },
      groupChange(value) {
        let childId = value
        this.getChild(childId)
      },
      show(record) {
        getAction(this.url.queryById, {
          id: record.id
        }).then((res) => {
          if (res.success) {
            this.getChild(res.result.subnetGroupId)
            this.edit(res.result);
          }
        });
      },
      add({}, data) {
        this.getChild(data.id)
        this.edit({}, data)
      },
      edit(record, data) {
        this.form.resetFields()
        this.model = Object.assign({}, record)
        if (data != null) {
          this.model.subnetGroupId = data.id
          this.model.subnetId = data.children[0].id
        }
        this.visible = true
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.model, 'subnetGroupId', 'segmentName',
            'remark', 'location', 'departId', 'endIp', 'startIp', 'subnetId'))
        })
      },
      submitForm() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.model.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'put'
            }
            let formData = Object.assign(this.model, values)
            httpAction(httpurl, formData, method)
              .then((res) => {
                if (res.success) {
                  that.$message.success(res.message)
                  that.$emit('ok')
                } else {
                  that.$message.warning(res.message)
                }
              })
              .finally(() => {
                that.confirmLoading = false
              })
          }
        })
      },
      popupCallback(row) {
        this.form.setFieldsValue(pick(row, 'subnetGroupId', 'segmentName', 'remark', 'location', 'departId', 'endIp',
          'startIp',
          'subnetId'))
      },
    },
  }
</script>