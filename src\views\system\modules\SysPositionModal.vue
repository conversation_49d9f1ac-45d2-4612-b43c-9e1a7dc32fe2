<template>
  <j-modal :title="title" :width="800" :visible="visible" :maskClosable="false" :centered='true' switchFullscreen
    :confirmLoading="confirmLoading" @ok="handleOk" @cancel="handleCancel" cancelText="关闭">

    <a-spin :spinning="confirmLoading">
      <a-form :form="form">

        <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="职务编码">
          <a-input placeholder="请输入职务编码" v-decorator="['code', validatorRules.code]" :disabled="readOnly"
            :allowClear='true' autocomplete='off' />
        </a-form-item>
        <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="职务名称">
          <a-input placeholder="请输入职务名称" v-decorator="['name', validatorRules.name]" :allowClear='true'
            autocomplete='off' />
        </a-form-item>
        <a-form-item class="two-words" :labelCol="labelCol" :wrapperCol="wrapperCol" label="职级">
          <j-dict-select-tag  placeholder="请选择职级" :triggerChange="true"
            dictCode="position_rank" v-decorator="['postRank', validatorRules.postRank]" />
        </a-form-item>
      </a-form>
    </a-spin>
  </j-modal>
</template>

<script>
  import pick from 'lodash.pick'
  import {
    httpAction
  } from '@/api/manage'
  import {
    duplicateCheck
  } from '@/api/api'
  import JDictSelectTag from '@/components/dict/JDictSelectTag'

  let validatorCodeTimer = null

  export default {
    name: 'SysPositionModal',
    components: {
      JDictSelectTag
    },
    data() {
      return {
        title: '操作',
        visible: false,
        model: {},
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          },
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          },
        },
        confirmLoading: false,
        form: this.$form.createForm(this),
        validatorRules: {
          code: {
            rules: [{
                required: true,
                message: '请输入职务编码'
              },{max:30,message:'职务编码长度不能超过30个字符'},
              {
                validator: (rule, value, callback) => {
                  // 函数消抖的简单实现，防止一段时间内发送多次请求
                  if (validatorCodeTimer) {
                    // 停止上次开启的定时器
                    clearTimeout(validatorCodeTimer)
                  }
                  validatorCodeTimer = setTimeout(() => {
                    duplicateCheck({
                      tableName: 'sys_position',
                      fieldName: 'code',
                      fieldVal: value,
                      dataId: this.model.id
                    }).then((res) => {
                      if (res.success) {
                        callback()
                      } else {
                        callback(res.message)
                      }
                    }).catch(console.error)
                  }, 300)
                }
              }
            ]
          },
          name: {
            rules: [{
              required: true,
              message: '请输入职务名称'
            },{max:20,message:'职务名称长度不能超过20个字符'}]
          },
          postRank: {
            rules: [{
              required: true,
              message: '请选择职级'
            }]
          },
        },
        url: {
          add: '/sys/position/add',
          edit: '/sys/position/edit',
        },
        readOnly: false
      }
    },
    created() {},
    methods: {
      add() {
        this.edit({})
      },
      edit(record) {
        this.form.resetFields()
        this.model = Object.assign({}, record)
        this.visible = true
        if (record.id) {
          this.readOnly = true
        } else {
          this.readOnly = false
        }
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.model,
            'code',
            'name',
            'postRank',
            // 'companyId'
          ))
        })
      },
      close() {
        this.$emit('close')
        this.visible = false
      },
      handleOk() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.model.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'put'
            }

            let formData = Object.assign(this.model, values)
            httpAction(httpurl, formData, method).then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            }).finally(() => {
              that.confirmLoading = false
              that.close()
            })
          }
        })
      },
      handleCancel() {
        this.close()
      },
    }
  }
</script>

<style lang="less" scoped>
  @import '~@assets/less/normalModal.less';

  ::v-deep .two-words>div>label {
    letter-spacing: 4px;
  }

  ::v-deep .two-words>div>label::after {
    letter-spacing: 0px;
  }
</style>