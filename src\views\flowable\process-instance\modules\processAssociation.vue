<template>
  <a-card :bordered="false">
    <!-- table区域-begin -->
    <div>
      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        :scroll='{x:true}'
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        @change="handleTableChange"
      >
      <span slot="status" slot-scope="status">
          <a-tag color="#108ee9"  v-if="status == 1"> 处理中</a-tag>
          <a-tag color="#f5222d" v-if="status == 2"> 已结束</a-tag>
          <a-tag v-if="status == 3"> 已撤回</a-tag>
          <a-tag color="#fa8c16" v-if="status == 4"> 待评价</a-tag>
          <a-tag color="#87d068" v-if="status == 5"> 已评价</a-tag>
        </span>
        <div slot="filterDropdown">
          <a-card>
            <a-checkbox-group @change="onColSettingsChange" v-model="settingColumns" :defaultValue="settingColumns">
              <a-row style="width: 400px">
                <template v-for="(item, index) in defColumns">
                  <template v-if="item.key != 'rowIndex' && item.dataIndex != 'action'">
                    <a-col :span="12" :key="index"
                    ><a-checkbox :value="item.dataIndex"
                    ><j-ellipsis :value="item.title" :length="10"></j-ellipsis></a-checkbox
                    ></a-col>
                  </template>
                </template>
              </a-row>
            </a-checkbox-group>
          </a-card>
        </div>
        <a-icon slot="filterIcon" type="setting" :style="{ fontSize: '16px', color: '#108ee9' }" />

        <span slot="action" slot-scope="text, record">
          <template v-if="record.status == 1">
            <a href="javascript:" @click="history(record,'查看进度')">查看进度</a>
            <a-divider type="vertical" />
            <a href="javascript:void(0);" @click="detail(record)">表单数据</a>
          </template>


          <template v-if="record.status !==1">
            <a href="javascript:void(0);" @click="history(record,'审批历史')">审批历史</a>
            <a-divider type="vertical" />
            <a href="javascript:void(0);" @click="detail(record)">表单数据</a>
          </template>

          <template v-if="record.status ===5">
             <a-divider type="vertical" />
            <a href="javascript:void(0);" @click="evaluateLook(record)">查看评价</a>
          </template>

        </span>
      </a-table>
    </div>
    <evaluation-modal ref="evaluationModal" @ok="searchReset"></evaluation-modal>
    <process-history-modal ref="processHistoryModal" @ok="modalFormOk"></process-history-modal>
    <process-modal ref="processModal"></process-modal>
  </a-card>
</template>
<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import Vue from 'vue'
import ProcessHistoryModal from '../../myProcess/modules/ProcessHistoryModal.vue'
import {getAction, postAction} from '@api/manage'
import ProcessModal from '../../myProcess/modules/ProcessModal.vue'
import { YqFormSeniorSearchLocation } from '@/mixins/YqFormSeniorSearchLocation'
import evaluationModal from '../../myProcess/modules/EvaluationModal.vue'

export default {
  name: 'myApplication',
  mixins: [JeecgListMixin,YqFormSeniorSearchLocation],
  components: { ProcessHistoryModal, ProcessModal,evaluationModal },
  props: {
    includeFilter:{
      required: false,
      type:String,
      description:'需要过滤的流程定义keys'
    },
    processInstanceId:{
      required: false,
      type:String,
      default:""
    },
    showType:{
      required: false,
      type:String,
      default:"1"
    }
  },
  data() {
    return {
      formItemLayout: {
        labelCol: {
          style: 'width:105px',
        },
        wrapperCol: {
          style: 'width:calc(100% - 105px)'
        }
      },
      queryParam: {},
      //表头
      columns: [],
      //列设置
      settingColumns: [],
      //列定义
      defColumns: [
        {
          title: '序号',
          dataIndex: '',
          key: 'rowIndex',
          isUsed:false,
          customCell:() =>{
            let cellStyle = 'text-align:center;width:60px'
            return {style:cellStyle}
          },
          customRender: function (t, r, index) {
            return parseInt(index) + 1
          },
        },
        {
          title: '业务标题',
          dataIndex: 'name',
          isUsed: true,
          customCell: () => {
            let cellStyle = 'text-align:center'
            return { style: cellStyle }
          },
          sorter: true

        },
        {
          title: '所属流程',
          dataIndex: 'processDefinitionName',
          isUsed:true,
          customCell:() =>{
            let cellStyle = 'text-align:center'
            return {style:cellStyle}
          },
          sorter: true
        },
        {
          title: '流程版本',
          dataIndex: 'processDefinitionVersion',
          isUsed:false,
          customRender: (text) => {
            //字典值替换通用方法
            return 'v' + text
          },
          customCell:() =>{
            let cellStyle = 'text-align:center'
            return {style:cellStyle}
          },
        },
        // {
        //   title: '申请人',
        //   dataIndex: 'startUserId',
        // },
        {
          title: '当前审批环节',
          dataIndex: 'currTaskName',
          isUsed:false,
          customCell:() =>{
            let cellStyle = 'text-align:center'
            return {style:cellStyle}
          },
        },
        {
          title: '状态',
          dataIndex: 'status',
          isUsed:true,
          scopedSlots: { customRender: 'status' },
          customCell:() =>{
            let cellStyle = 'text-align:center;width:100px'
            return {style:cellStyle}
          },
        },
        {
          title: '提交申请时间',
          dataIndex: 'startTime',
          isUsed:true,
          customCell:() =>{
            let cellStyle = 'text-align:center;width:160px'
            return {style:cellStyle}
          },
          sorter: true
        },
        {
          title: '结束时间',
          dataIndex: 'endTime',
          isUsed:true,
          customCell:() =>{
            let cellStyle = 'text-align:center;width:160px'
            return {style:cellStyle}
          },
          sorter: true
        },
        {
          title: '操作',
          dataIndex: 'action',
          align:'center',
          width:210,
          isUsed:false,
          fixed:'right',
          scopedSlots: {
            /*  filterDropdown: 'filterDropdown',
              filterIcon: 'filterIcon',*/
            customRender: 'action',
          },
        },
      ],
      modalCancelVisible: false,
      cancelForm: {
        reason: '撤回申请',
        cascade: false,
        procInsId: null,
      },
      isview:false,
      submitLoading: false,
      url: {
        child: '/flowable/processInstance/getProcessAssociationChildById',
        parent: '/flowable/processInstance/getProcessAssociationParentById',
        cancel: '/business/actZBusiness/cancel',
      },
    }
  },
  computed:{
    tasktimestimp(){
      return this.$store.getters.tasktimestimp
    }
  },
  watch:{
    tasktimestimp :{
      handler(nval, oval){
        this.loadData();
      },
      deep:true,
      immediate:true
    }
  },
  created() {
    this.loadData(1)

    this.initColumns()
    this.getColumns(this.columns)
  },
  methods: {
    evaluateLook(record){
      this.$refs.evaluationModal.edit(record.id)
      this.$refs.evaluationModal.title = '查看评价'
      this.$refs.evaluationModal.disableSubmit = false
    },
    loadData(arg) {
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1
      }
      var params = this.getQueryParams()//查询条件
      params.processInstanceId=this.processInstanceId
      this.loading = true
      let url=""
      if (this.showType==='1'){
         url=this.url.child
        params.primaryId=this.processInstanceId

      }else  if (this.showType==='2'){
        url=this.url.parent
        params.associationId=this.processInstanceId
      }
      getAction(url, params).then((res) => {
        if (res.success) {
          //update-begin---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
          this.dataSource = res.result.records || res.result
          if (res.result.total) {
            this.ipagination.total = res.result.total
          } else {
            this.ipagination.total = 0
          }
          //update-end---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
        } else {
          this.$message.warning(res.message)
        }
      }).finally(() => {
        this.loading = false
      })
    },
    //点击评价
    evaluate(record){
      this.$refs.evaluationModal.add(record.id)
      this.$refs.evaluationModal.title = '评价'
      this.$refs.evaluationModal.disableSubmit = false
    },

    onEndTimeChange: function (value, dateString) {
      // console.log(dateString[0], dateString[1])
      this.queryParam.finishedAfter = dateString[0]
      this.queryParam.finishedBefore = dateString[1]
    },
    onAppllicationTimeChange: function (value, dateString) {
      // console.log(dateString[0], dateString[1])
      this.queryParam.startedAfter = dateString[0]
      this.queryParam.startedBefore = dateString[1]
    },
    //撤回
    withdraw(record) {
      //this.cancelForm.id = v.id
      this.cancelForm.procInsId = record.id
      this.modalCancelVisible = true
    },
    resetCancleForm() {
      this.cancelForm = {
        reason: null,
        cascade: false,
        processInstanceId: null,
      }
    },
    detail(record){
      if (!record.id) {
        this.$message.error('申请不存在')
        return
      }
      this.$refs.processModal.init(record)
    },
    //查看进度
    history(record,title) {
      if (!record.id) {
        this.$message.error('流程实例ID不存在')
        return
      }
      record.state = record.endTime
      this.$refs.processHistoryModal.init(record.id)
      this.$refs.processHistoryModal.title = title
      this.$refs.processHistoryModal.disableSubmit = false
    },
    //列设置更改事件
    onColSettingsChange(checkedValues) {
      var key = this.$route.name + ':colsettings'
      Vue.ls.set(key, checkedValues, 7 * 24 * 60 * 60 * 1000)
      this.settingColumns = checkedValues
      const cols = this.defColumns.filter((item) => {
        if (item.key == 'rowIndex' || item.dataIndex == 'action') {
          return true
        }
        if (this.settingColumns.includes(item.dataIndex)) {
          return true
        }
        return false
      })
      this.columns = cols
    },
    initColumns() {
      //权限过滤（列权限控制时打开，修改第二个参数为授权码前缀）
      //this.defColumns = colAuthFilter(this.defColumns,'testdemo:');

      var key = this.$route.name + ':colsettings'
      let colSettings = Vue.ls.get(key)
      if (colSettings == null || colSettings == undefined) {
        let allSettingColumns = []
        this.defColumns.forEach(function (item, i, array) {
          allSettingColumns.push(item.dataIndex)
        })
        this.settingColumns = allSettingColumns
        this.columns = this.defColumns
      } else {
        this.settingColumns = colSettings
        const cols = this.defColumns.filter((item) => {
          if (item.key == 'rowIndex' || item.dataIndex == 'action') {
            return true
          }
          if (colSettings.includes(item.dataIndex)) {
            return true
          }
          return false
        })
        this.columns = cols
      }
    },
  },
}
</script>
<style scoped lang='less'>
@import '~@assets/less/common.less';
@import '~@assets/less/YQCommon.less';
</style>
