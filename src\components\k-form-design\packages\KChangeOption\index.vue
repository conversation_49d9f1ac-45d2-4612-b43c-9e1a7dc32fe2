<template>
  <div class="option-change-container">
    <a-row v-if="type === 'option' || type === 'tab'" :gutter="8">
      <div class="option-change-box" v-for="(val, index) in value" :key="index">
        <a-col :span="9"><a-input v-model="val.label" placeholder="名称" /></a-col>
        <a-col :span="9"><a-input v-model="val.value" placeholder="值" /></a-col>
        <a-col :span="6"
          ><div @click="handleDelete(index)" class="option-delete-box">
            <a-icon type="delete" /></div
        ></a-col>
      </div>
      <a-col :span="24"><a @click="handleAdd">添加</a></a-col>
    </a-row>

    <a-row v-if="type === 'rules'" :gutter="8">
      <span v-for="(val, index) in value" :key="index">
        <div class="option-change-box" v-if="index !== 0">
          <a-col :span="18"><a-input v-model="val.message" placeholder="提示信息" /></a-col>
          <a-col :span="18"><a-input v-model="val.pattern" placeholder="正则表达式pattern" /></a-col>
          <a-col :span="6"
            ><div @click="handleDelete(index)" class="option-delete-box">
              <a-icon type="delete" /></div
          ></a-col>
        </div>
      </span>
      <a-col :span="24"><a @click="handleAddRules">增加校验</a></a-col>
    </a-row>
    <a-row v-else-if="type === 'colspan'" :gutter="8">
      <div class="option-change-box" v-for="(val, index) in value" :key="index">
        <a-col :span="18"><a-input-number style="width: 100%" :max="24" v-model="val.span" placeholder="名称" /></a-col>
        <a-col :span="6"
          ><div @click="handleDelete(index)" class="option-delete-box">
            <a-icon type="delete" /></div
        ></a-col>
      </div>
      <a-col :span="24"><a @click="handleAddCol">添加</a></a-col>
    </a-row>
    <a-modal
      title="树选择器选项配置"
      :visible="visible"
      width="70vw"
      @cancel="submitTreeOptions"
      @ok="submitTreeOptions"
    >
      <a-button type="primary" style="margin-bottom: 12px" @click="addOption(dataSource)"> 新增 </a-button>
      <a-table
        :row-key="'id'"
        :columns="columns"
        :data-source="dataSource"
        :pagination="false"
        keyboard="false"
        :scroll="{ x: 240, y: 400 }"
      >
        <template slot="action" slot-scope="text, record">
          <a @click="editOption(record)">编辑</a>
          <a-divider type="vertical" />
          <a-dropdown :trigger="['click']">
            <a @click="(e) => e.preventDefault()">更多</a>
            <a-menu slot="overlay" @click="moreAction($event, record)">
              <a-menu-item key="del"> 删除 </a-menu-item>
              <a-menu-item key="addChild"> 添加下级 </a-menu-item>
            </a-menu>
          </a-dropdown>
        </template>
      </a-table>
    </a-modal>
    <a-modal
      :title="optionTitle"
      :visible="optionVisible"
      width="50vw"
      okText="保存"
      @cancel="hideOptionForm"
      @ok="submitOptionForm"
    >
      <div>
        <a-form-model ref="ruleForm" :model="optionForm" :rules="rules">
          <a-form-model-item label="名称" prop="label">
            <a-input v-model="optionForm.label" />
          </a-form-model-item>
          <a-form-model-item label="值" prop="value">
            <a-input v-model="optionForm.value" />
          </a-form-model-item>
        </a-form-model>
      </div>
    </a-modal>
  </div>
</template>
<script>
/*
 * author kcz
 * date 2019-11-20
 * description 修改多选、下拉、单选等控件options的组件，添加移除校验规制的组件
 */
const columns = [
  {
    title: '名称',
    dataIndex: 'label',
    key: 'label',
    ellipsis: true,
  },
  {
    title: '值',
    dataIndex: 'value',
    key: 'value',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 120,
    key: 'action',
    align: 'center',
    scopedSlots: { customRender: 'action' },
  },
]
import { isArray } from 'lodash'
import {setOption } from '../KFormDesign/config/formItemsConfig'
export default {
  name: 'KChangeOption',
  props: {
    value: {
      type: Array,
      required: true,
    },
    type: {
      type: String,
      default: 'option',
    },
  },
  data() {
    return {
      dataSource: [],
      visible: false,
      columns,
      optionTitle: '编辑选项',
      optionVisible: false,
      labelCol: { span: 4 },
      wrapperCol: { span: 14 },
      optionForm: {
        label: '',
        value: '',
      },
      targetOption: null,
      addArr: null,
      rules: {
        label: [
          { required: true, message: '请输入名称', trigger: 'blur' },
          { max: 100, message: '最多100个字符', trigger: 'blur' },
          { validator: this.charRule, trigger: 'blur' },
        ],
        value: [
          { required: true, message: '请输入值', trigger: 'blur' },
          { max: 100, message: '最多100个字符', trigger: 'blur' },
          { validator: this.valueRule, trigger: 'blur' },
        ],
      },
    }
  },
  created() {
    this.dataSource = this.value
    this.visible = this.type === 'treeOptions'
  },
  methods: {
    valueRule(rule, value, callback) {
      let reg = /^[\/_\-a-zA-Z0-9\u4E00-\u9FA5]+$/
      if (value !== '' && !reg.test(value)) {
        callback('只允许汉字、字母、数字、下划线、短划线')
      } else {
        callback()
      }
    },
    charRule(rule, value, callback) {
      let reg = /^[\/_\-a-zA-Z0-9\u4E00-\u9FA5]+$/
      if (value !== '' && !reg.test(value)) {
        callback('只允许汉字、字母、数字、下划线、短划线')
      } else {
        callback()
      }
    },
    showOptionForm() {
      this.optionVisible = true
    },
    hideOptionForm() {
      this.optionVisible = false
    },
    submitOptionForm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let idstr = this.targetOption.id
          let idarr = idstr.split('-')
          idarr.pop()
          let pobj = this.dataSource
          let temid = ''
          //获取本机数组
          if (idarr.length > 0) {
            idarr.forEach((el) => {
              if (temid === '') {
                temid += el
              } else {
                temid = temid + '-' + el
              }
              let temObj = pobj.find((cel) => cel.id === temid)
              if (temObj) {
                pobj = temObj.children
              }
            })
          }
          //检查是否有相同数据
          let hasObj = pobj.find((el) => {
            let has = false
            if (el.id !== this.targetOption.id) {
              for (let k in this.optionForm) {
                if (this.optionForm[k] === el[k] && !has) {
                  has = true
                }
              }
            }
            return has
          })
          if (hasObj === undefined) {
            for (let k in this.optionForm) {
              this.targetOption[k] = this.optionForm[k]
            }
            if (this.addArr) {
              this.addArr.push(this.targetOption)
              this.addArr = null
            }
            this.hideOptionForm()
          } else {
            this.$message.warning('有重复数据！')
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    addOption(arr, pid) {
      let id = arr.length + 1
      if (arr.length > 0) {
        arr.forEach((el) => {
          let idarr = el.id.split('-')
          let elId = Number(idarr[idarr.length - 1])
          if (id < elId) {
            id = elId + 1
          }
        })
      }
      if (pid) {
        id = pid + '-' + id
      }
      let newOption = setOption(id)

      this.addArr = arr
      this.editOption(newOption)
    },
    delOption(record) {
      let idstr = record.id
      let idarr = idstr.split('-')
      idarr.pop()
      let pobj = this.dataSource
      let temid = ''
      if (idarr.length > 0) {
        idarr.forEach((el) => {
          if (temid === '') {
            temid += el
          } else {
            temid = temid + '-' + el
          }
          let temObj = pobj.find((cel) => cel.id === temid)
          if (temObj) {
            pobj = temObj.children
          }
        })
      }
      let idx = pobj.findIndex((el) => el.id === record.id)
      pobj.splice(idx, 1)
    },
    editOption(record) {
      this.targetOption = record
      // this.optionForm = record
      for (let k in this.optionForm) {
        this.optionForm[k] = record[k]
      }
      this.showOptionForm()
      // console.log('编辑选选项配置', record)
    },
    moreAction(e, record) {
      if (e.key === 'del') {
        this.$confirm({
          title: '确定要删除这个选项吗？',
          content: '',
          okText: '确定',
          okType: 'danger',
          cancelText: '取消',
          onOk: () => {
            this.delOption(record)
          },
        })
      } else if (e.key === 'addChild') {
        this.addOption(record.children, record.id)
        // console.log('选项配置更多', e, record)
      }
    },
    submitTreeOptions() {
      this.visible = false
      this.$emit('hideTreeOptions')
    },
    handleAdd() {
      // 添加
      const addData = [
        ...this.value,
        {
          value: `${this.value.length + 1}`,
          label: '选项' + (this.value.length + 1),
          list: this.type === 'tab' ? [] : undefined,
        },
      ]
      this.$emit('input', addData)
    },
    handleAddCol() {
      // 添加栅格Col
      const addData = [
        ...this.value,
        {
          span: 12,
          list: [],
        },
      ]
      this.$emit('input', addData)
    },
    handleAddRules() {
      const addData = [
        ...this.value,
        {
          pattern: '',
          message: '',
        },
      ]
      this.$emit('input', addData)
    },
    handleDelete(deleteIndex) {
      // 删除
      this.$emit(
        'input',
        this.value.filter((val, index) => index !== deleteIndex)
      )
    },
  },
}
</script>
<style lang="less" scoped>
.option-change-container {
  width: calc(100% - 8px);
}
.option-change-box {
  height: 38px;
  padding-bottom: 6px;
  .option-delete-box {
    margin-top: 3px;
    background: #ffe9e9;
    color: #f22;
    width: 32px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    border-radius: 50%;
    overflow: hidden;
    transition: all 0.3s;
    &:hover {
      background: #f22;
      color: #fff;
    }
  }
}
</style>
