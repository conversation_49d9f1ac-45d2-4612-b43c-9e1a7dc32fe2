<template>
  <a-config-provider :locale="locale">
    <a-form
      v-if="typeof value.list !== 'undefined' && typeof value.config !== 'undefined' && allInit"
      class="k-form-build-9136076486841527"
      :layout="value.config.layout"
      :hideRequiredMark="value.config.hideRequiredMark"
      :form="form"
      @submit="handleSubmit"
      :style="value.config.customStyle"
    >
      <buildBlocks
        ref="buildBlocks"
        v-for="(record, index) in value.list"
        :record="record"
        :dynamicData="defaultDynamicData"
        :config="config"
        :disabled="disabled"
        :formConfig="value.config"
        :validatorError="validatorError"
        :key="index"
        :hiddenFields="hiddenFields"
        @reset="reset"
        @change="handleChange"
        @changeFields="changeFieldsHandler"
        @btnFunc="btnFunc"
      />
    </a-form>
  </a-config-provider>
</template>
<script>
/*
 * author kcz
 * date 2019-11-20
 * description 将json数据构建成表单
 */
import buildBlocks from './buildBlocks'
import zhCN from 'ant-design-vue/lib/locale-provider/zh_CN'
import { getAction, postAction, httpAction } from '@/api/manage'
// import moment from "moment";
export default {
  name: 'KFormBuild',
  components: {
    buildBlocks,
  },
  props: {
    value: {
      type: Object,
      required: true,
    },
    dynamicData: {
      type: Object,
      default: () => {
        return {}
      },
    },
    config: {
      type: Object,
      default: () => ({}),
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    outputString: {
      type: Boolean,
      default: false,
    },
    defaultValue: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      locale: zhCN,
      form: this.$form.createForm(this, { onFieldsChange: this.onFieldsChange }),
      validatorError: {},
      defaultDynamicData: {},
      allInit: false,
      proArr: [],
      modelArr: [],
      optionsObj: {},
      hiddenFields: [],
      fieldsDefaults: {},
    }
  },
  created() {
    this.allInit = true
    this.getProArr(this.value.list)
    // this.initDynamicData()
  },
  mounted() {
    if (Object.keys(this.defaultValue).length > 0) {
      
      this.setData(this.defaultValue)
    }
  },
  computed: {
    getDynamicData() {
      return typeof this.dynamicData === 'object' && Object.keys(this.dynamicData).length
        ? this.dynamicData
        : window.$kfb_dynamicData || this.defaultDynamicData
    },
  },
  watch: {},
  methods: {
    // 获取字典值
    initDynamicData() {
      // console.log('查看数据配置项 === ', this.value)
      if (this.value === undefined) return
      this.proArr = []
      if (typeof this.dynamicData === 'object' && Object.keys(this.dynamicData).length) {
        // console.log('获取的数据 === ', this.dynamicData, this.proArr)
        this.defaultDynamicData = JSON.parse(JSON.stringify(this.dynamicData))
      }
      this.getProArr(this.value.list)
      if (window.$kfb_dynamicData) {
        this.defaultDynamicData = window.$kfb_dynamicData
        this.allInit = true
        return
      }
      let prosum = 0
      if (this.proArr.length > 0 && this.$allDynamicFunc) {
        this.proArr.forEach((el) => {
          if (this.defaultDynamicData[el.code] === undefined) {
            if (el.type === 'treeSelect' || el.type === 'cascader') {
              let loadTreeData = this.$allDynamicFunc.loadTreeData
              if (loadTreeData && typeof loadTreeData === 'function') {
                loadTreeData({ pcode: el.code, async: false }).then((res) => {
                  // console.log('获取树状数据', res)
                  if (res.success) {
                    this.defaultDynamicData[el.code] = res.result
                  }
                  prosum += 1
                  if (prosum === this.proArr.length) {
                    this.allInit = true
                  }
                })
              } else {
                prosum += 1
                if (prosum === this.proArr.length) {
                  this.allInit = true
                }
              }
            } else {
              let getDictItemsFromCache = this.$allDynamicFunc.getDictItemsFromCache
              let ajaxGetDictItems = this.$allDynamicFunc.ajaxGetDictItems
              if (
                getDictItemsFromCache &&
                typeof getDictItemsFromCache === 'function' &&
                getDictItemsFromCache(el.code)
              ) {
                this.defaultDynamicData[el.code] = getDictItemsFromCache(el.code)
                prosum += 1
                if (prosum === this.proArr.length) {
                  this.allInit = true
                }
              } else if (ajaxGetDictItems && typeof ajaxGetDictItems === 'function') {
                ajaxGetDictItems(el.code).then((res) => {
                  // console.log("获取数据字典 == ",res)
                  if (res.success) {
                    this.defaultDynamicData[el.code] = res.result
                  }
                  prosum += 1
                  if (prosum === this.proArr.length) {
                    this.allInit = true
                  }
                })
              } else {
                prosum += 1
                if (prosum === this.proArr.length) {
                  this.allInit = true
                }
              }
            }
          }
        })
      } else {
        this.allInit = true
      }
    },
    getProArr(list) {
      if (list === undefined) return
      list.forEach((el) => {
        if (el.model && typeof el.model === 'string' && this.modelArr.includes(el.model) === false) {
          this.modelArr.push(el.model)
          this.optionsObj[el.model] = el.options
        }

        if (el.type === 'card') {
          // console.log("有卡片",el)
          if (el.list && el.list.length > 0) {
            this.getProArr(el.list)
          }
        } else if (el.type === 'table') {
          // console.log("有table",el)
          if (el.trs && el.trs.length > 0) {
            el.trs.forEach((tr) => {
              if (tr.tds && tr.tds.length > 0) {
                tr.tds.forEach((td) => {
                  if (td.list && td.list.length > 0) {
                    this.getProArr(td.list)
                  }
                })
              }
            })
          }
        } else if (el.type === 'grid') {
          // console.log("有栅格",el)
          if (el.columns && el.columns.length > 0) {
            el.columns.forEach((cel) => {
              if (cel.list && cel.list.length > 0) {
                this.getProArr(cel.list)
              }
            })
          }
        } else if (el.type === 'tabs') {
          // console.log("有tab",el)
          if (el.columns && el.columns.length > 0) {
            el.columns.forEach((col) => {
              if (col.list && col.list.length > 0) {
                this.getProArr(col.list)
              }
            })
          }
        } else if (el.type === 'divider') {
          // console.log('有分割线', el)
        } else if (el.type === 'batch') {
          // console.log("有动态表格",el)
          if (el.list && el.list.length > 0) {
            this.getProArr(el.list)
          }
        } else if (el.options && el.options.dynamic && el.options.dynamicKey) {
          let keystr = el.options.dynamicKey
          if (this.defaultDynamicData[keystr] === undefined) {
            this.proArr.push({
              type: el.type,
              code: keystr,
            })
          }
        }
      })
    },
    // 提交按钮出发
    handleSubmit(e) {
      // 提交按钮触发，并触发submit函数，返回getData函数
      e.preventDefault()
      this.$emit('submit', this.getData)
    },
    reset() {
      // 重置只可输入的表单
      for (const [key, value] of Object.entries(this.defaultValue)) {
        if (this.isEmptyValue(value)) {
          this.form.resetFields([key,[]]);
        }
      }
    },
    isEmptyValue(value) {
      return value == null || value === '';
    },
    //获取表单数据需要校验字段时调用的方法；
    getData() {
      // 提交函数，提供父级组件调用
      return new Promise((resolve, reject) => {
        try {
          this.form.validateFieldsAndScroll((err, values) => {
            if (err) {
              reject(err)
              /**
               * @author: lizhichao<<EMAIL>>
               * @Description: 多容器校验时，提供error返回给多容器进行判断。
               */
              this.validatorError = err
              return
            }
            this.validatorError = {}
            this.$refs.buildBlocks.forEach((item) => {
              if (!item.validationSubform()) {
                reject(err)
              }
            })
            if (this.outputString) {
              // 需要所有value转成字符串
              for (const key in values) {
                const type = typeof values[key]
                if (type === 'string' || type === 'undefined') {
                  continue
                } else if (type === 'object') {
                  values[key] = `k-form-design#${type}#${JSON.stringify(values[key])}`
                } else {
                  values[key] = `k-form-design#${type}#${String(values[key])}`
                }
              }
              resolve(values)
            } else {
              //在返回数据中获取添加隐藏数据
              this.modelArr.forEach((el) => {
                if (values[el] === undefined) {
                  let op = this.optionsObj[el]
                  if (op && op.hidden && op.defaultValue !== undefined) {
                    values[el] = op.defaultValue
                  } else {
                    values[el] = null
                  }
                }
              })
              resolve(values)
            }
          })
        } catch (err) {
          console.error(err)
          reject(err)
        }
      })
    },
    //获取表单数据不需要校验调用方法
    getFieldsValue(){
        return new Promise((resolve, reject) => {
           try {  
            let values = this.form.getFieldsValue();
            resolve(values)
           }catch(err){
            console.error(err)
            reject(err)
           }
        })
    },
    setData(defaultValue) {
      let jsonKeys = Object.keys(defaultValue)
      let json = {}
      jsonKeys.forEach((key) => {
        if (this.modelArr.includes(key)) {
          json[key] = defaultValue[key]
          let options = this.optionsObj[key]
          this.changeFieldsStatus(options, json[key])
          if (options && options.changeFunc) {
            this.executeCallback(json[key], key, options.changeFunc)
          }
        }
      })
      if (Object.keys(json).length === 0) return
      this.form.setFieldsValue(json)
      // console.log(this.modelArr,json)
      // return new Promise((resolve, reject) => {
      //   try {
      //     if (this.outputString) {
      //       // 将非string数据还原
      //       for (const key in json) {
      //         if (!json[key].startsWith('k-form-design#')) {
      //           continue
      //         }
      //         const array = json[key].split('#')
      //         if (array[1] === 'object') {
      //           json[key] = JSON.parse(array[2])
      //         } else if (array[1] === 'number') {
      //           json[key] = Number(array[2])
      //         } else if (array[1] === 'boolean') {
      //           json[key] = Boolean(array[2])
      //         }
      //       }
      //       this.form.setFieldsValue(json)
      //     } else {
      //       this.form.setFieldsValue(json)
      //     }
      //     resolve(true)
      //   } catch (err) {
      //     console.error(err)
      //     reject(err)
      //   }
      // })
    },

    // 批量设置某个option的值
    setOptions(fields, optionName, value) {
      fields = new Set(fields)

      // 递归遍历控件树
      const traverse = (array) => {
        array.forEach((element) => {
          if (fields.has(element.model)) {
            if (optionName === 'rules') {
              this.$set(element, optionName, value)
            } else {
              this.$set(element.options, optionName, value)
            }
          }
          if (element.type === 'grid' || element.type === 'tabs') {
            // 栅格布局 and 标签页
            element.columns.forEach((item) => {
              traverse(item.list)
            })
          } else if (element.type === 'card' || element.type === 'batch') {
            // 卡片布局 and  动态表格
            traverse(element.list)
          } else if (element.type === 'table') {
            // 表格布局
            element.trs.forEach((item) => {
              item.tds.forEach((val) => {
                traverse(val.list)
              })
            })
          }
        })
      }
      traverse(this.value.list)
    },
    // 隐藏表单字段
    hide(fields) {
      this.setOptions(fields, 'hidden', true)
      this.$forceUpdate()
    },
    // 显示表单字段
    show(fields) {
      this.setOptions(fields, 'hidden', false)
      this.$forceUpdate()
    },
    // 禁用表单字段
    disable(fields) {
      this.setOptions(fields, 'disabled', true)
    },
    // 启用表单字段
    enable(fields) {
      this.setOptions(fields, 'disabled', false)
    },
    //监听字段变化
    handleChange(value, key, callback) {
      // 触发change事件
      this.executeCallback(value, key, callback)
      this.$emit('change', value, key)
    },
    //执行控件的回调函数
    executeCallback(value, key, callback) {
      if (callback) {
        let updateFunc = Function('"use strict";return (' + callback + ')')()
        if (typeof updateFunc === 'function') {
          updateFunc(value, key, this, httpAction)
        }
      }
    },
    //控件值变化执行联动函数
    changeFieldsHandler(e) {
      if (e.isDefault) {
        let keys = Object.keys(this.defaultValue)
        if (keys.includes(e.model)) return
      }
      this.changeFieldsStatus(e.options, e.value)
    },
    //字段联动数据改变
    changeFields(e) {
      if (e.type === 'hide') {
        setTimeout(() => {
          this.hide(e.fields)
        }, 100)
      } else if (e.type === 'show') {
        this.show(e.fields)
      }
    },
    //按钮点击事件e
    btnFunc(e) {
      let btnFunc = Function('"use strict";return (' + e + ')')()
      if (typeof btnFunc === 'function') {
        btnFunc(this, httpAction)
      }
    },
    parseJsonString(dataString, type = 1) {
      try {
        return JSON.parse(dataString)
      } catch (error) {
        console.log('解析参数报错', error)
        return type === 1 ? {} : []
      }
    },
    //执行联动数据
    async changeFieldsStatus(options, v) {
      if (options && options.linkageData) {
        let linkageData = this.parseJsonString(options.linkageData, 2)
        if (!Array.isArray(linkageData)) return
        linkageData.forEach((el) => {
          if (el.fields && el.fields.length > 0) {
            // console.log('执行了q === ', el, v)
            let type = 'hide'
            if (el.handler) {
              let handler = Function('"use strict";return (' + el.handler + ')')()
              if (typeof handler === 'function') {
                handler(v, httpAction).then((res) => {
                  // console.log('*******&&&&***** ', res)
                  type = res ? 'show' : 'hide'
                  this.changeFields({ fields: el.fields, type: type })
                })
              }
            } else {
              type = el.value == v ? 'show' : 'hide'
              this.changeFields({ fields: el.fields, type: type })
            }
          }
        })
      }
    },
  },
}
</script>
