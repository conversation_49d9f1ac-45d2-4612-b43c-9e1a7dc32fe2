<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="12">
            <a-form-item label="软件名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['softwareName',validatorRules.softwareName]" placeholder="请输入软件名称" :allowClear="true"
                autocomplete="off" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="软件版本" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['softwareVersion',validatorRules.softwareVersion]" placeholder="请输入软件版本(必须是数字)"
                :allowClear="true" autocomplete="off">
              </a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="操作系统" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-select v-decorator="['softwareOs',validatorRules.softwareOs]" :allowClear="true" placeholder="请选择操作系统">
                <a-select-option v-for="(item, key) in dictOptions" :key="key" :value="item.value">
                  {{ item.text }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="cpu架构" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-select v-decorator="['softwareCpu',validatorRules.softwareCpu]" :allowClear="true" placeholder="请选择cpu架构">
                <a-select-option v-for="(item, key) in cpuList" :key="key" :value="item.value">
                  {{ item.text }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12" style="height:70px">
            <a-form-item label="软件文件" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-upload
                v-decorator="['softwareFile', validatorRules.softwareFile]"
                :trigger-change="true"
                :number="1"
                :bizPath="bizPath"
                :accept='".tar,.tgz,.zip,.rar,.jar,.class,.java,.c,.cpp,.h,.so,.7z,.rpm,.deb"'
                :uploadAction="fildUrl+'/kbase/knowledges/template/upload2Minio'">
              </j-upload>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="描述" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-textarea
                :autoSize="{ minRows: 1, maxRows: 4 }"
                v-decorator="['softwareDescribe', validatorRules.softwareDescribe]"
                placeholder="请输入描述"
                :allowClear="true"
                autocomplete="off" />
            </a-form-item>
          </a-col>
          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
  import {
    httpAction,
    getAction
  } from '@/api/manage'
  import pick from 'lodash.pick'
  import {
    ajaxGetAreaItems,
    ajaxGetDictItems,
    getDictItemsFromCache
  } from '@/api/api'
  export default {
    name: 'softwareForm',
    props: {
      //流程表单data
      formData: {
        type: Object,
        default: () => {},
        required: false
      },
      //表单模式：true流程表单 false普通表单
      formBpm: {
        type: Boolean,
        default: false,
        required: false
      },
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data() {
      return {
        form: this.$form.createForm(this),
        model: {},
        dictOptions: [],
        cpuList: [],
        fildUrl: window._CONFIG['domianURL'],
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 6
          },
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 17
          },
        },
        bizPath: 'softwarePackage',
        confirmLoading: false,
        accept:'',
        softwareFileNameSuffix: [
          '.tar',
          '.tbz',
          '.tgz',
          '.zip',
          '.rar',
          '.jar',
          '.class',
          '.java',
          '.c',
          '.cpp',
          '.h',
          '.so',
          '.7z',
          '.rpm',
          '.deb',
        ],
        validatorRules: {
          softwareName: {
            rules: [{
              required: true,
              message: '请输入软件名称!'
            },
              {
                max: 50,
                message: '软件名称长度不应超过 50 个字符!'
              }]
          },
          softwareVersion: {
            rules: [{
                required: true,
                message: '请输入软件版本!'
              },
              {
                // pattern: /(^[1-9][0-9]*$)|(^(([1-9][0-9]*)+\.)+(([1-9][0-9]*)+)$)/,//限制第一位不能为0
                pattern: /(^[0-9]*$)|(^([0-9]+\.)+([0-9]+)$)/, //对第一位没有限制，只要是数字就行
                message: '请输入正确的软件版本,如：xxx或者x.xx.x'
              }
            ]
          },
          softwareOs: {
            rules: [{
              required: true,
              message: '请选择操作系统!'
            }]
          },
          softwareCpu: {
            rules: [{
              required: true,
              message: '请选择cpu架构!'
            }]
          },
          softwareFile: {
            rules: [{
              required: true,
              message: '请上传软件文件!'
            }]
          },
          softwareDescribe:{
            rules: [{
              required: false,
              max:255,
              message: '描述长度不应超过 255 个字符!'
            }]
          },
        },
        url: {
          add: "/software/softwareRepository/add",
          edit: "/software/softwareRepository/edit",
          queryById: "/software/softwareRepository/queryById"
        }
      }
    },
    computed: {
      formDisabled() {
        if (this.formBpm === true) {
          if (this.formData.disabled === false) {
            return false
          }
          return true
        }
        return this.disabled
      },
      showFlowSubmitButton() {
        if (this.formBpm === true) {
          if (this.formData.disabled === false) {
            return true
          }
        }
        return false
      }
    },
    created() {
      this.accept=this.softwareFileNameSuffix.join(",")
      //如果是流程中表单，则需要加载流程表单data
      this.showFlowData();
    },
    mounted() {
      this.initDictData()
    },
    methods: {
      initDictData() {
        //根据字典Code, 初始化字典数组
        ajaxGetDictItems('cpuArch', null).then((res) => {
          if (res.success) {
            this.cpuList = res.result
          }
        })
        ajaxGetDictItems('os_type', null).then((res) => {
          if (res.success) {
            this.dictOptions = res.result
          }
        })
      },
      add() {
        this.edit({});
      },
      edit(record) {
        this.form.resetFields();
        this.model = Object.assign({}, record);
        this.visible = true;
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.model, 'softwareName', 'softwareVersion', 'softwareOs', 'softwareCpu',
            'softwareFile', 'softwareDescribe'))
        })
      },
      //渲染流程表单数据
      showFlowData() {
        if (this.formBpm === true) {
          let params = {
            id: this.formData.dataId
          };
          getAction(this.url.queryById, params).then((res) => {
            if (res.success) {
              this.edit(res.result);
            }
          });
        }
      },
      submitForm() {
        const that = this;
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if (!this.model.id) {
              httpurl += this.url.add;
              method = 'post';
            } else {
              httpurl += this.url.edit;
              method = 'put';
            }

            //进行软件文件的筛选
            if ('' != values.softwareFile && null != values.softwareFile && undefined != values.softwareFile) {
              var f = true
              for (var i = 0; i < that.softwareFileNameSuffix.length; i++) {
                var suffix = that.softwareFileNameSuffix[i]
                var index = values.softwareFile.lastIndexOf('.')
                var fileSuffix = values.softwareFile.substring(index, values.softwareFile.length)
                if (that.softwareFileNameSuffix[i] == fileSuffix || index == -1) {
                  f = false
                }
              }
              if (f) {
                var str = ''
                for (var i = 0; i < that.softwareFileNameSuffix.length; i++) {
                  str = str + '[' + that.softwareFileNameSuffix[i] + ']' + '、'
                }
                that.$message.warning('请选择以' + str.substring(0, str.length - 1) + '为后缀的软件文件')
                // that.updateModelFileName()
                that.confirmLoading = false
                return
              }
            }

            let formData = Object.assign(this.model, values);
            httpAction(httpurl, formData, method).then((res) => {
              if (res.success) {
                that.$message.success(res.message);
                that.$emit('ok');
              } else {
                that.$message.warning(res.message);
              }
              that.confirmLoading = false;
            }).catch((err) => {
              that.confirmLoading = false;
              that.$message.warning(err.message);
            })
          }

        })
      },
      popupCallback(row) {
        this.form.setFieldsValue(pick(row, 'softwareName', 'softwareVersion', 'softwareOs', 'softwareCpu',
          'softwareFile', 'softwareDescribe'))
      },
      updateModelFileName() {
        this.model.softwareFile = null
        this.form.setFieldsValue(pick(this.model, 'softwareName', 'softwareVersion', 'softwareOs', 'softwareCpu',
          'softwareFile', 'softwareDescribe'))
      },
    }
  }
</script>