<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    switchFullscreen
    :centered='true'
    :destroyOnClose='true'
    @ok="handleOk"
    :okButtonProps="{ style: { display: 'none' }}"
    @cancel="handleCancel"
    cancelText="关闭"
  >
    <gateway-child-device-info-form
      ref="realForm"
      @ok="submitCallback"
      :disabled="disableSubmit"
    ></gateway-child-device-info-form>
  </j-modal>
</template>

<script>
import GatewayChildDeviceInfoForm from './GatewayChildDeviceInfoForm'
export default {
  name: 'GatewayChildDeviceInfoModal',
  components: {
    GatewayChildDeviceInfoForm,
  },
  data() {
    return {
      title: '',
      width: '1000px',
      visible: false,
      disableSubmit: false,
    }
  },
  methods: {
    show(id) {
      // alert('-----id----' + id)
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.show(id)
      })
    },
    add() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.add()
      })
    },
    edit(record) {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.edit(record)
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      // this.$refs.realForm.submitForm()
      this.$emit('ok')
      this.close()
    },
    submitCallback() {
      this.$emit('ok')
      this.visible = false
    },
    handleCancel() {
      this.$emit('ok')
      this.close()
    },
  },
}
</script>
<style lang='less' scoped>
@import '~@assets/less/normalModal.less';
</style>