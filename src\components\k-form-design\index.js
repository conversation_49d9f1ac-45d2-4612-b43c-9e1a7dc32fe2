import Vue from 'vue'
import KFormDesign from './packages/index'
import YqDivider from "./packages/customComponents/YqDivider"
import { ACCESS_TOKEN} from '@/store/mutation-types'
Vue.use(KFormDesign);
KFormDesign.setFormDesignConfig({
  title: '自定义表单设计',
  list: [
    {
      label: "分割线2",
      type: "yq_divider",
      icon: "icon-fengexian",
      component: YqDivider,
      options: {
        showLabel: false,
      },
    }
  ],
  // window._CONFIG['domianURL'] + 
  uploadFile:'/sys/common/upload',
  uploadImage: '/sys/common/upload',
  downloadFileUrl:  "/sys/common/static/",
  downloadImageUrl:  "/sys/common/static/",
  uploadFileName: "file",
  uploadImageName: "file",
  uploadFileData: { biz: "file" },
  uploadImageData: { biz: "image" },
  uploadFileHeaders: {},
  uploadImageHeaders: {},
});

import {ajaxGetDictItems, getDictItemsFromCache } from '@/api/api'
Vue.prototype.$formFuncOptions = [
  {
    label: "获取字典数据",
    key: "getDataDict",
  },
  {
    label: "获取分类数据",
    key: "getTreeData",
  }
]
Vue.prototype.$allDynamicFunc = {
  ajaxGetDictItems,
  getDictItemsFromCache
}
Vue.prototype.$dynamicData = {}
//自定义表单获取token的方法
Vue.prototype.$getAccessToken = function(){
  return Vue.ls.get(ACCESS_TOKEN)
}