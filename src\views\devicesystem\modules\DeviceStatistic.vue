<template>
  <div>
    <a-row :gutter='[12,12]' class='row-example'>
      <a-col class='gutter-row' :xxl='colSpan.xxl' :xl='colSpan.xl' :lg='colSpan.lg' :md='colSpan.md' :sm='colSpan.sm' :xs='colSpan.xs' v-for='(item,index) in deviceInfo' :key='index'>
        <div class='gutter-box'>
          <a-tooltip title='同步' class='sync-img' v-if='index===3&&showSync'>
            <a-icon type='swap' width='3em' height='3em' @click='executeSync(true)' />
          </a-tooltip>
          <div class='gutter-box-left'>
            <div class='gutter-box-left-top'>
              <span>{{ item.count }}</span>
              <span>台</span>
            </div>
            <div class='gutter-box-left-bottom'>
              <div class='dian'></div>
              {{ item.type }}
            </div>
          </div>
          <div class='gutter-box-right'>
            <img v-if='item.img' :src='item.img' alt='' />
            <a-icon v-if='item.icon' :type='item.icon' class='icon'/>
          </div>
        </div>
      </a-col>
    </a-row>
  </div>
</template>

<script>
export default {
  name: 'DeviceStatistic',
  props: {
    showSync: {
      type: Boolean,
      required: false,
      default: () => {
        return true
      }
    },
    deviceInfo: {
      type: Array,
      required: true,
    },
    colSpan: {
      type: Object,
      required: false,
      default: () => {
        return {
          xxl: 6,
          xl: 6,
          lg: 12,
          md: 12,
          sm:24,
          xs: 24
        }
      }
    }
  },
  data() {
    return {
      description: '统计',
    }
  },
  methods: {
    executeSync(){
      this.$emit('executeSync')
    },
  }
}
</script>
<style lang='less' scoped>
.row-example> :first-child>.gutter-box{
  background-color: #aecbff;
  /* 不支持线性的时候显示 */
  background-image: linear-gradient(to right, #aecbff, #5d95fc);
}
.row-example> :nth-child(2)>.gutter-box{
  background-color: #c3fcc0;
  background-image: linear-gradient(to right, #c3fcc0, #29ca7f);
}
.row-example> :nth-child(3)>.gutter-box{
  background-color: #fdbac5;
  background-image: linear-gradient(to right, #fdbac5, #f44967);
}
.row-example> :last-child>.gutter-box{
  background-color: #b8eaff;
  background-image: linear-gradient(to right, #b8eaff, #25a8df);
}

.gutter-row {
  .gutter-box {
    position: relative;
    border-radius: 3px;
    padding: 5px 5px 5px 0px;
    box-shadow: 2px 2px 4px 1px rgba(0, 0, 0, 0.2);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-flow: row nowrap;

    .gutter-box-left {
      padding: 12px 20px;
      overflow: hidden;

      .gutter-box-left-top {
        display: flex;
        align-items: flex-end;

        span:nth-child(1) {
          font-family: 'Eurostile-Bold';
          font-size: 0.4rem
          /* 32/80 */;
          color: #ffffff;
          overflow: hidden;
          display: inline-block;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        span:nth-child(2) {
          font-family: 'HYk2gj';
          font-size: 14px;
          color: rgba(255, 255, 255, 0.85);
          line-height: 38px;
          margin-left: 11px;
        }
      }

      .gutter-box-left-bottom {
        font-family: 'Microsoft YaHei';
        //font-size: 0.2rem;
        font-size: 16px;
        color: rgba(255, 255, 255, 0.85);
        display: flex;
        align-items: center;
        white-space: nowrap;
        justify-content: start;
        text-align: start;

        .dian {
          width: 6px;
          height: 6px;
          background: #fff;
          margin-right: 8px;
        }
      }
    }

    .gutter-box-right {
      position: absolute;
      right: 5px;
      bottom: 5px;
      //align-self: end;

      img {
        width: 74px;
        height: 74px;
        opacity: 0.2;
      }
      .icon{
        font-size: 70px;
        color: rgba(255, 255, 255, 0.12);
      }
    }
  }
}

.sync-img {
  position: absolute;
  top: 12px;
  right: 12px;
  cursor: pointer;
  color: #fff;
  z-index: 100;
}
</style>