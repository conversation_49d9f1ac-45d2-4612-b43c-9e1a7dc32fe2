<template>
  <a-spin :spinning='confirmLoading'>
    <j-form-container :disabled='disabled'>
      <a-form-model ref='form' slot='detail' :model='model' :rules='validatorRules'>
        <div class='one-wrapper'>
          <div class='colorBox'>
            <span class='colorTotal'>基本信息</span>
          </div>
          <a-row>
            <a-col :span='24'>
              <a-form-model-item class='two-words' label='名称' :labelCol='labelCol' :wrapperCol='wrapperCol'
                                 prop='taskName'>
                <a-input
                  v-model='model.taskName'
                  placeholder='请输入名称'
                  :allowClear='true'
                  autocomplete='off'
                />
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
              <a-form-model-item label='协议类型' :labelCol='labelCol' :wrapperCol='wrapperCol' prop='transferProtocolId'>
                <a-select
                  :getPopupContainer="(node) => node.parentNode"
                  v-model='model.transferProtocolId'
                  placeholder="请选择传输协议"
                  allowClear
                >
                  <a-select-option v-for="item in productInfo.selectedProtocolList" :key="item.transferProtocolId">
                    {{ item.transferProtocol }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item label='是否默认' :labelCol='labelCol' :wrapperCol='wrapperCol' prop='isDefault'>
                <a-radio-group v-model="model.isDefault" :options='radioOption'/>
              </a-form-model-item>
            </a-col>
          </a-row>
        </div>
        <div class='two-wrapper'>
          <div class='colorBox'>
            <span class='colorTotal'>备份配置</span>
          </div>
          <a-divider orientation='left' dashed class='command-title'>配置信息</a-divider>
          <a-row>
            <a-col :span='24'>
              <a-form-model-item label='命令' class='two-words' :labelCol='labelCol' :wrapperCol='wrapperCol' prop='textBackCommand'>
                <command-input :showRealValue="true" v-model='model.textBackCommand'  :node-id='"text-back-editor"'  @changeCommand='changeCommand($event,"textBack")'></command-input>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-divider orientation='left' dashed class='command-title'>文件备份&nbsp;
            <a-popover title="备份还原命令格式说明">
              <template slot="content">
                <div style="display: flex">
                  <div>
                    <span>
                      当前命令通过换行分割两种不同执行方式：
                    </span>
                    <br>
                    <span>
                      第一种: 命令以#开头，表示当前行的命令均在远程网络设备执行；
                    </span>
                    <br>
                    <span>
                      第二种: 命令以$开头，表示当前行的命令均在配置字典“localSSH”配置的服务器执行；
                    </span>
                  </div>
                </div>
              </template>
              <a-icon
                type="question-circle"
                theme="twoTone"
                style="font-size: 18px; line-height: 44px; margin-left: -5px"
              /> </a-popover>
            </a-divider>

          <a-row>
            <a-col :span='24'>
              <a-form-model-item label='命令' class='two-words' :labelCol='labelCol' :wrapperCol='wrapperCol' prop='fileBackCommand'>
                <command-input :showRealValue="true" v-model='model.fileBackCommand' :node-id='"file-back-editor"' @changeCommand='changeCommand($event,"fileBack")'></command-input>
              </a-form-model-item>
            </a-col>
          </a-row>
        </div>
        <div class='three-wrapper'>
          <div class='colorBox'>
            <span class='colorTotal'>还原配置</span>
          </div>
          <a-divider orientation='left' dashed class='command-title'>文件还原&nbsp;
            <a-popover title="备份还原命令格式说明">
              <template slot="content">
                <div style="display: flex">
                  <div>
                    <span>
                      当前命令通过换行分割两种不同执行方式：
                    </span>
                    <br>
                    <span>
                      第一种: 命令以#开头，表示当前行的命令均在远程网络设备执行；
                    </span>
                    <br>
                    <span>
                      第二种: 命令以$开头，表示当前行的命令均在配置字典“localSSH”配置的服务器执行；
                    </span>
                  </div>
                </div>
              </template>
              <a-icon
                type="question-circle"
                theme="twoTone"
                style="font-size: 18px; line-height: 44px; margin-left: -5px"
              /> </a-popover>
          </a-divider>
          <a-row>
            <a-col :span='24'>
              <a-form-model-item label='命令' class='two-words' :labelCol='labelCol' :wrapperCol='wrapperCol' prop='fileRecoveryCommand'>
                <command-input :showRealValue="true" v-model='model.fileRecoveryCommand' :node-id='"file-recovery-editor"' @changeCommand='changeCommand($event,"fileRecovery")'></command-input>
              </a-form-model-item>
            </a-col>
          </a-row>
        </div>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import JFormContainer from '@/components/jeecg/JFormContainer'
import JDictSelectTag from '@/components/dict/JDictSelectTag'
import commandInput from '@views/devicesystem/modules/CommandInput.vue'
import {ValidateRequiredFields} from '@/utils/rules.js'
export default {
  name: 'ProductConfigForm',
  components: {
    JFormContainer,
    JDictSelectTag,
    commandInput
  },
  props: {
    // 表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    },
    productInfo: {}
  },
  data() {
    return {
      confirmLoading: false,
      protocolTypeList: [
        {
          value: 1,
          text: 'ssh'
        },
        {
          value: 2,
          text: 'telnet'
        }
      ],
      radioOption: [
        { label: '是', value: 'Y', key:'default_1', checked: true },
        { label: '否', value: 'N', key:'default_2', checked: false },
      ],
      model: {
        taskName: '',
        transferProtocolId: undefined,
        isDefault: 'Y',
        textBackCommand: '',
        fileBackCommand: '',
        fileRecoveryCommand: ''
      },
      labelCol: {
        xs: {span: 24},
        sm: {span: 4}
      },
      wrapperCol: {
        xs: {span: 24},
        sm: {span: 19}
      },
      validatorRules: {
        taskName: [
          { required: true, trigger:'blur', validator: (rule, value, callback) =>ValidateRequiredFields(rule, value, callback,'名称',50,2) },
        ],
        transferProtocolId: [
          { required: true, message: '请选择协议类型!',trigger: 'blur' }
        ],
        textBackCommand: [
          { required: true, validator: (rule, value, callback) =>ValidateRequiredFields(rule, value, callback,'备份配置信息命令',255,1),trigger:'blur' },
        ],
        fileBackCommand: [
          { required: true,validator: (rule, value, callback) =>ValidateRequiredFields(rule, value, callback,'备份文件备份命令',255,1) ,trigger:'blur'}
        ],
        fileRecoveryCommand: [
          { required: true,validator: (rule, value, callback) =>ValidateRequiredFields(rule, value, callback,'文件还原命令',255,1),trigger:'blur' }
        ]
      },
      url: {
        add: '/net/device/productConfigureManageAdd',
        edit: '/net/device/productConfigureManageEdit'
      }
    }
  },

  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      record.isDefault = record.isDefault || 'Y'
      this.$nextTick(() => {
        this.$refs.form.resetFields()
        this.model = Object.assign({}, record)
        this.visible = true
      })
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.$refs.form.validate((err, values) => {
        if (!!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          // let formData = Object.assign(this.model, values)
          // let formData =JSON.stringify(this.model)
          let formData =this.model
          formData.productId=this.productInfo.id
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    changeCommand(value,type){
      switch (type){
        case "textBack":
          this.$refs.form.validateField("textBackCommand",this.validateTextBack)
          break;
        case "fileBack":
          this.$refs.form.validateField("fileBackCommand",this.validateFileBack)
          break;
        case "fileRecovery":
          this.$refs.form.validateField("fileRecoveryCommand",this.validateFileRecovery)
          break;
      }
    },
    /*是否默认*/
    changeDefault(value) {
      this.model.isDefault = value.target.value
    },
    validateTextBack(rule, value, callback){
      ValidateRequiredFields(rule, value, callback,'备份配置信息命令',255,1)
     /* if(rule.required){
        if(value.length>0){
           callback()
        }else{
          callback('请输入配置信息命令！')
        }
      }
      else {
        callback()
      }*/
    },
    validateFileBack(rule, value, callback){
      ValidateRequiredFields(rule, value, callback,'备份文件备份命令',255,1)
     /* if(rule.required){
        if(value.length>0){
          callback()
        }else{
          callback('请输入文件备份命令！')
        }
      }
      else {
        callback()
      }*/
    },
    validateFileRecovery(rule, value, callback){
      ValidateRequiredFields(rule, value, callback,'文件还原命令',255,1)
    /*  if(rule.required){
        if(value.length>0){
          callback()
        }else{
          callback('请输入文件还原命令！')
        }
      }
      else {
        callback()
      }*/
    }
  }
}
</script>
<style lang='less' scoped>
.colorBox {
  margin-bottom: 18px;
}

.colorTotal {
  padding-left: 7px;
  border-left: 4px solid #1e3674;
}

.command-title {
  font-size: 14px;
  margin:3px 0px !important;
}

::v-deep .two-words > div > label {
  letter-spacing: 4px;
}

::v-deep .two-words > div > label::after {
  letter-spacing: 0px;
}
</style>