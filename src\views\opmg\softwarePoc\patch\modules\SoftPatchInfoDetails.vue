<template>
  <j-modal
    ref='softDetailmodal'
    :title="title"
    :width="width"
    :visible="visible"
    :centered="true"
    :footer='null'
    switchFullscreen
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }"
    @cancel="hide"
    cancelText="关闭">
  <div style='overflow-y: auto;' :style='{height:isFullscreen? "calc(100vh - 150px)":"75vh"}'>
    <div class='colorBox' style='margin-bottom: 12px'>
      <span class="colorTotal">升级包信息</span>
    </div>
    <a-row>
      <a-col :span="24" style="overflow-x: auto">
        <a-descriptions bordered>
          <a-descriptions-item label="版本">
            {{ record.patchVersion }}
          </a-descriptions-item>
          <a-descriptions-item label="升级类别">
            {{ record.patchTypeText }}
          </a-descriptions-item>
          <a-descriptions-item label="操作系统">
            {{ patchOsText }}
          </a-descriptions-item>
          <a-descriptions-item label="架构">
            {{ fraworkText  }}
          </a-descriptions-item>
          <a-descriptions-item label="是否启用">
            {{ enablesText }}
          </a-descriptions-item>
          <a-descriptions-item label="设备类型">
            {{ resourceTypeText }}
          </a-descriptions-item>
          <a-descriptions-item label="升级包" v-if='record.patchType==="0"'>
            <span class='file-name' @click='downloadSofware(record.file,record.fileOriginalName,record)'>{{ record.fileOriginalName }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="升级文件" v-if='record.patchType==="1"'>
            <span class='file-name' @click='downloadSofware(record.file,record.fileOriginalName,record)'>{{ record.fileOriginalName }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="升级脚本" v-if='record.patchType==="1"'>
            <span class='file-name' @click='downloadSofware(record.inst,record.instOriginalName,record)'>{{ record.instOriginalName }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="升级前脚本" v-if='record.patchType==="1"'>
            <span class='file-name' @click='downloadSofware(record.preInst,record.preInstOriginalName,record)'>{{ record.preInstOriginalName }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="升级后脚本" v-if='record.patchType==="1"'>
            <span class='file-name' @click='downloadSofware(record.postInst,record.postInstOriginalName,record)'>{{ record.postInstOriginalName }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="升级特性">
            {{ record.upgradeFeature }}
          </a-descriptions-item>
        </a-descriptions>
      </a-col>
    </a-row>
    <div style='margin-top: 24px'>
      <div class='colorBox' style='margin-bottom: 12px'>
        <span class="colorTotal">成功升级的终端</span>
      </div>
      <soft-patch-success v-if='visible' :id='record.id'></soft-patch-success>
    </div>
  </div>
  </j-modal>

</template>

<script>
import SoftPatchSuccess from '@views/opmg/softwarePoc/patch/modules/SoftPatchSuccess.vue'
export default {
  name: 'SoftPatchInfoDetails',
  props: {
    patchTypes: {
      type: Array,
      default: () => [],
      required: false
    },
    auditStatus: {
      type:Array,
      default: () => [],
      required: false,
    },
    restarts: {
      type:Array,
      default: () => [],
      required: false,
    },
    dictOptions: {
      type: Array,
      default: () => [],
      required: false
    },
    cpuList: {
      type: Array,
      default: () => [],
      required: false
    },
    enables: {
      type: Array,
      default: () => [],
      required: false
    },
    resourceTypeList: {
      type: Array,
      default: () => [],
      required: false
    },
  },
  components: {
    SoftPatchSuccess
  },
  data() {
    return {
      form: this.$form.createForm(this),
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      url: {
        queryById: '/patch/devopePatchInfo/queryById',
      },
      title: '详情',
      width: "75%",
      visible: false,
      disableSubmit: false,
      record: {},
      patchOsText:"",
      fraworkText:"",
      resourceTypeText:"",
      enablesText:"",
    }
  },
  mounted() {},
  computed: {
    isFullscreen(){
      return this.$refs.softDetailmodal?this.$refs.softDetailmodal.isFullscreen:false;
    },
  },
  methods: {
    show(record) {
      Object.assign(this.record,record)
      this.record.patchTypeText = this.patchTypes.find(el=>el.value === this.record.patchType)?.text
      this.patchOsText = this.getPatchOsText() || ""
      this.fraworkText = this.getFraworkText() || ""
      this.resourceTypeText = this.getResourceTypeText() || ""
      this.enablesText = this.enables.find(el=>el.value === record.isEnable)?.text
      this.visible = true;
    },
    hide() {
      this.record = {};
      this.visible = false;
    },
    getPatchOsText() {
      let tem = [];
      let text = this.record.patchOs
      if(text){
        let arr = text.split(',');
        for (let i = 0; i < arr.length; i++) {
          tem.push(this.dictOptions.find(el=>el.value === arr[i])?.text)
        }
      }
      return tem.join()
    },
    getFraworkText() {
      let tem = [];
      let text = this.record.frawork
      if(text){
        let arr = text.split(',');
        for (let i = 0; i < arr.length; i++) {
          tem.push(this.cpuList.find(el=>el.value === arr[i])?.text)
        }
      }
      return tem.join()
    },
    getResourceTypeText() {
      let text = this.record.resourceType
      let arr = text.split(',')
      let tem = [];
      arr.forEach(el=>{
        tem.push(this.resourceTypeList.find(item=>item.value === el)?.text)
      })
      return tem.join()
    },
    //返回上一级
    getGo() {
      this.$parent.pButton2(0)
    },
    downloadSofware(url,name,record) {
      this.$emit('downloadSofware',url,name,record)
    },
  },
}
</script>
<style lang="less"  scoped>

.colorBox {
  margin-bottom: 10px;
}

.colorTotal {
  padding-left: 7px;
  border-left: 4px solid #1e3674;
  font-size: 14px;
  font-weight: bold;
}
.file-name{
  color: @primary-color;
  cursor: pointer;
}
</style>