<template>
  <a-table ref="table" bordered :rowKey="(record,index)=>{return record.id}" :columns="columns"
           :dataSource="dataSource" :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}" :pagination="ipagination"
           :loading="loading" @change="handleTableChange">
    <!-- 字符串超长截取省略号显示-->
    <template slot="index" slot-scope="text,record,index">
      <span>{{index+1}}</span>
    </template>
    <template slot="alarmLevel" slot-scope="text,record,index">
      <span >{{alarmLevels[text]?alarmLevels[text].levelName:text}}</span>
    </template>
    <span slot="templateContent" slot-scope="text">
            <j-ellipsis :value="text" :length="25" />
          </span>
    <template slot="tooltip" slot-scope="text">
      <a-tooltip placement="topLeft" :title="text" trigger="hover">
        <div class='tooltip'>
          {{ text }}
        </div>
      </a-tooltip>
    </template>
  </a-table>
</template>

<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
import {getAction} from '@api/manage'

export default {
  name: 'JkdjAlarms',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  props:{
    record:{
      type:Object,
      default:()=>{return {}},
      required:true,
    }
  },
  data(){
    return{
      columns: [
        {
          title: '序号',
          dataIndex: 'index',
          scopedSlots: {
            customRender: 'index'
          },
          width: 80,
          customCell: () => {
            let cellStyle = 'text-align: center'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '设备名称',
          dataIndex: 'deviceName',
          scopedSlots: {
            customRender: 'deviceName'
          },
          customCell: () => {
            let cellStyle = 'text-align: center'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '告警名称',
          dataIndex: 'alarmName',
          scopedSlots: {
            customRender: 'alarmName'
          },
          customCell: () => {
            let cellStyle = 'text-align: center'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '告警信息',
          dataIndex: 'alarmMessage',
          customCell: () => {
            let cellStyle = 'text-align: center'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '告警级别',
          dataIndex: 'alarmLevel',
          scopedSlots: {
            customRender: 'alarmLevel'
          },
        },
        // {
        //   title: '描述',
        //   dataIndex: 'description',
        //   scopedSlots: {
        //     customRender: 'description'
        //   },
        //   customCell: () => {
        //     let cellStyle = 'text-align: left;min-width: 200px;max-width:400px'
        //     return {
        //       style: cellStyle
        //     }
        //   }
        // },
        // {
        //   title: '操作',
        //   align: 'center',
        //   width: 180,
        //   fixed: 'right',
        //   dataIndex: 'action',
        //   scopedSlots: {
        //     customRender: 'action'
        //   },
        // },
      ],
      url: {
        list: '/abutment/system/getAlarm',
      },
      disableMixinCreated:true,
      alarmLevels:{},
    }
  },
  created() {
    // this.queryParam.systemId = this.record.id;
    this.getAlarmList()
  },
  mounted() {
  },
  methods:{
    getAlarmList(){
      this.alarmLevels = {}
      getAction("/alarm/alarmLevel/list").then(res=>{

        if(res.success && res.result && res.result.records){
          res.result.records.forEach(el=>{
            this.alarmLevels[el.alarmLevel] = el;
          })
        }
      }).finally(()=>{
        console.log("获取到了告警了列表 == ", this.alarmLevels)
        this.loadData()
      })
    }
  }
}
</script>



<style scoped lang='less'>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';

/deep/.ant-table-tbody .ant-table-row td {
  padding-top: 5px;
  padding-bottom: 5px;
}
</style>