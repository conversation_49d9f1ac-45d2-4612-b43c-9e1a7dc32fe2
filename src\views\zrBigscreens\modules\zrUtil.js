export const StatusInfo = window.ZrStatusInfo || []
export const businessStatus = [
  {
    value: 0,
    label: '正常',
    color: '#1BCB3C'
  },
  {
    value: 1,
    label: '不稳定',
    color: '#FFAE38'
  },
  {
    value: 2,
    label: '故障',
    color: '#FF1D42'
  }
]
export const unitLevels = [
  {
    value: 1,
    label: '一级单位',
  },
  {
    value: 2,
    label: '二级单位',
  },
  {
    value: 3,
    label: '三级单位',
  }
]

export const indicators = window.zrIndicators ||[
  { name: "机密网节点覆盖范围", score: 23, level: "D" ,type:"wltl"},
  { name: "机密级子网和终端配备", score: 88, level: "B" ,type:"wltl"},
  { name: "与部本级数据处理中心机密域通联状况", score: 67, level: "C",type:"wltl" },
  { name: "内部级和机密级网络通联状况", score: 54, level: "D",type:"wltl"  },
  { name: "通联质量", score: 12, level: "D",type:"wltl" },

  { name: "主数据处理中心（节点）运行", score: 96, level: "A",type:"jzss" },
  { name: "云平台运行", score: 45, level: "D" ,type:"jzss"},
  { name: "网络密码装备和安全防护设备运行", score: 78, level: "C",type:"jzss" },

  { name: "系统部署", score: 99, level: "A" ,type:"yyfw"},
  { name: "信息集成平台与统一身份认证", score: 34, level: "D" ,type:"yyfw"},
  { name: "公共服务应用运行", score: 76, level: "C" ,type:"yyfw"},
  { name: "业务系统应用", score: 18, level: "D" ,type:"yyfw"},
  { name: "迭代升级", score: 85, level: "B" ,type:"yyfw"},

  { name: "体系架构", score: 62, level: "C" ,type:"sjzy"},
  { name: "基础数据采集", score: 49, level: "D" ,type:"sjzy"},
  { name: "动态数据流转", score: 27, level: "D" ,type:"sjzy"},
  { name: "综合分析产", score: 91, level: "A" ,type:"sjzy"},
  { name: "体系融入", score: 39, level: "D" ,type:"sjzy"},

  { name: "制度机制建设", score: 73, level: "C",type:"ywbz" },
  { name: "运维队伍编配管理", score: 15, level: "D" ,type:"ywbz"},
  { name: "运维人员能力水平", score: 82, level: "B" ,type:"ywbz"},
  { name: "经费投入", score: 58, level: "D" ,type:"ywbz" },

  { name: "推广应用效果", score: 41, level: "D" ,type:"zlxy"},
  { name: "用户满意度", score: 99, level: "A" ,type:"zlxy"}
]
export const indicatorTypes = window.zrIndicatorTypes|| [
  { name: '基础设施', score: 99,level: 'A' ,id:"jzss",type:"category"},
  { name: '数据资源', score: 80,level: 'A' ,id:"sjzy",type:"category"},
  { name: '网络通联', score: 88,level: 'B',id:"wltl" ,type:"category"},
  { name: '应用服务', score: 81,level: 'B' ,id:"yyfw",type:"category"},
  { name: '运维保障', score: 74,level: 'C' ,id:"ywbz",type:"category"},
  { name: '质量效益', score: 60,level: 'D' ,id:"zlxy",type:"category"}
]

//基础支撑硬件层
export const hardwareList = window.ZrHardwares || [
  {
    'businessName': '服务器',
    'ip': '************',
    'status': 0
  },  {
    'businessName': '核心交换机',
    'ip': '*************',
    'status': 0
  },
  {
    'businessName': '天融信入侵检测',
    'ip': '************',
    'status': 0
  }, {
    'businessName': '天融信漏洞扫描',
    'ip': '************',
    'status': 0
  }, {
    'businessName': '天融信堡垒机',
    'ip': '************',
    'status': 0
  },{
    'businessName': '天融信防火墙',
    'ip': '************',
    'status': 2
  },
]
//基础支撑软件层
export const softwareList = window.ZrSoftwares || [
  {
    'businessName': '达梦数据库',
    'ip': '************',
    'status': 0
  },  {
    'businessName': '缓存服务',
    'ip': '************',
    'status': 0
  },  {
    'businessName': '消息队列',
    'ip': '************',
    'status': 0
  }
]

//管理人员配置
export const managerList = window.ZrManagers || [
  {
    'realName': '赵子龙',
    'department': '设备管理处',
    'phone': "***********"
  }
]

//运维人员配置
export const operatorList = window.ZrOperators ||  [
  {
    'businessName': '数据库',
    'realName': '张译德',
    'phone': "***********"
  },
  {
    'businessName': '服务器集群',
    'realName': '李晓峰',
    'phone': "***********"
  },
  {
    'businessName': '网络架构',
    'realName': '王思远',
    'phone': "***********"
  },
  {
    'businessName': '安全防护',
    'realName': '赵明轩',
    'phone': "***********"
  },
]
export const globalLevel = window.zrGlobalLevel || 'A'
export const systemList = window.ZrBusinesses || [
  {
    id: 1,
    businessName: '智能运维监控系统',
    ip: '**************',
    status: 0,
    topoId: "1930151035842088962",
    positionInfo: "10楼001机房--3号机柜",
    deviceName: "服务器A",
    bg:"/zrBigScreen/sysBgs/demo.jpg"
  },
  {
    id: 2,
    businessName: '预约管理系统',
    ip: '*************',
    status: 1,
    topoId: "1778382285377327105",
    positionInfo: "05楼B区--1号机柜",
    deviceName: "交换机X1",
    bg:"/zrBigScreen/sysBgs/yglxt.jpg"
  },
  {
    id: 3,
    businessName: '运行评估系统',
    ip: '************',
    status: 0,
    topoId: "1930151035842088962",
    positionInfo: "08楼测试室--5号机柜",
    deviceName: "存储设备S1",
    bg:"/zrBigScreen/sysBgs/yxpgxt.jpg"
  },
  {
    id: 4,
    businessName: '智能调度系统',
    ip: '*************',
    status: 2,
    topoId: "1767153342930358274",
    positionInfo: "02楼核心区域--2号机柜",
    deviceName: "防火墙F2",
    bg:"/zrBigScreen/sysBgs/znddxt.jpg",
  },
  {
    id: 5,
    businessName: '后勤服务系统',
    ip: '************',
    status: 0,
    topoId: "1930151035842088962",
    positionInfo: "03楼运维间--4号机柜",
    deviceName: "路由器R1",
    bg:"/zrBigScreen/sysBgs/hqfwxt.jpg",
  },
  {
    id: 6,
    businessName: '人力资源管理系统',
    ip: '**************',
    status: 0,
    topoId: "1767153342930358274",
    positionInfo: "07楼数据中心--6号机柜",
    deviceName: "负载均衡L1",
    bg:"/zrBigScreen/sysBgs/rlzyglxt.jpg",
  }
]
export const  mapBackIcon = 'path://M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm104 316.9c0 10.2-4.9 19.9-13.2 25.9L457.4 512l145.4 105.2c8.3 6 13.2 15.6 13.2 25.9V690c0 6.5-7.4 10.3-12.7 6.5l-246-178a7.95 7.95 0 0 1 0-12.9l246-178c5.3-3.8 12.7 0 12.7 6.5v46.8z'
export const simpleBackIcon = 'path://M793 242H366v-74c0-6.7-7.8-10.5-13-6.3l-246 178c-4.1 3.2-4.1 9.4 0 12.6l246 178c5.3 4.2 13 .4 13-6.3v-74h415v470H241v-60h511V242z';