<template>
  <div style="height:100%;">
      <component :is="pageName" style="height:100%" :data="data"/>
  </div>
</template>
<script>
  import TerminalAuditList from './TerminalAuditList'
  import TerminalAuditDetails from './modules/TerminalAuditDetails'
  export default {
    name: "TerminalAuditManage",
    data() {
      return {
        isActive: 0,
        data:{}
      };
    },
    components: {
      TerminalAuditList,
      TerminalAuditDetails
    },
    created(){
      this.pButton1(0);
    },
    //使用计算属性
    computed: {
      pageName() {
        switch (this.isActive) {
          case 0:
            return "TerminalAuditList";
            break;

          default:
            return "TerminalAuditDetails";
            break;
        }
      }
    },
    methods: {
      pButton1(index) {
        this.isActive = index;
      },
      pButton2(index,item) {
        this.isActive = index;
        this.data = item;
      }
    }
  }
</script>