<template>
  <a-row :gutter='10' style='height: 100%;;overflow: hidden' class='vScroll zhl zhll'>
    <a-col style='width:100%;height: 100%;display: flex;flex-direction: column'>
      <div class="gutter-example">
        <a-row :gutter="16" >
          <a-col :lg="6" :md="12" :xs="24" class='col-example'>
            <a-row class="row-example">
              <a-col :span='24' class="gutter-row col-one">
                <div class="gutter-box-one ">
                  <div class="gutter-box-one-left-top">
                    <span>分发总数</span>
                  </div>
                  <div class="gutter-box-one-left-bottom">
                    <span>{{ statisticsInfo.planNum }}</span>
                  </div>
                </div>
              </a-col>
            </a-row>
          </a-col>

          <a-col  :lg="6" :md="12" :xs="24" class='col-example'>
            <a-row class="row-example">
              <a-col :span='24' class="gutter-row col-four">
                <div class="gutter-box-three">
                  <div class="gutter-box-three-left-top">
                    <span>注册总数</span>
                  </div>
                  <div class="gutter-box-three-left-bottom">
                    <span>{{ statisticsInfo.allNum }}</span>
                  </div>
                </div>
              </a-col>
            </a-row>
          </a-col>

          <a-col  :lg="6" :md="12" :xs="24" class='col-example'>
            <a-row class="row-example">
              <a-col :span='24' class="gutter-row col-three">
                <div class="gutter-box-three">
                  <div class="gutter-box-three-left-top">
                    <span>已开机（联网超过30分钟）</span>
                  </div>
                  <div class="gutter-box-three-left-bottom">
                    <span>{{ statisticsInfo.openMoreTY }}</span>
                  </div>
                </div>
              </a-col>
            </a-row>
          </a-col>

          <a-col  :lg="6" :md="12" :xs="24" class='col-example'>
            <a-row class="row-example">
              <a-col :span='24' class="gutter-row col-two">
                <div class="gutter-box-two">
                  <div class="gutter-box-two-left-top">
                    <span>已开机（联网不足30分钟）</span>
                  </div>
                  <div class="gutter-box-two-left-bottom">
                    <span>{{ statisticsInfo.openLessTY }}</span>
                  </div>
                </div>
              </a-col>
            </a-row>
          </a-col>
        </a-row>
      </div>
      <!-- 查询区域 -->
      <!-- <div class="table-page-search-wrapper">
        <a-form layout="inline" @keyup.enter.native="searchQuery">
          <a-row :gutter="24" class="form-row">
             <a-col
              class="form-col"
              :span="6"
            >
              <a-form-item label="单位">
                <j-tree-select
                  v-model="queryParam.deptId"
                  placeholder="请选择单位"
                  dict="momg_dept,name,id"
                  pidField="pid"
                  pidValue="0"
                  :multiple="true"
                  hasChildField="has_child"
                />
              </a-form-item>
            </a-col>
            <a-col class="form-col" :span="6">
              <a-form-item label="单位">
                <a-tree-select
                  v-model="searchKey"
                  tree-node-filter-prop="title"
                  :replaceFields="replaceFields"
                  :treeData="selectOption"
                  show-search
                  :searchValue="bsearchKey"
                  style="width: 100%"
                  multiple
                  :maxTagCount='1'
                  :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                  placeholder="请选择单位"
                  allow-clear
                  @change="onChangeTree"
                  @search="onSearch"
                  @select="onSelect"
                >
                </a-tree-select>
              </a-form-item>
            </a-col>
             <a-col class="form-col" :span="6">
              <a-form-item label="单位名称">
                <a-select placeholder="请选择单位" v-model="queryParam.deptName" :allowClear="true">
                  <a-select-option
                    v-for="(item, index) in selectOption"
                    :key="index"
                    :value="item.name"
                  >{{ item.name }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
             <a-col class="form-col" :span="6">
              <a-form-item label="统计时间">
                  <a-range-picker
                    v-model="queryParam.warehousingTime"
                    @change="onChangePicker"
                    format="YYYY-MM-DD"
                    :placeholder="['开始时间', '截止时间']"
                  />
                </a-form-item>
            </a-col>
             <a-col class="form-col" :span="8">
              <a-form-item>
                <a-button type="primary" class="btn-search" @click="dosearch">查询</a-button>
                <a-button class="btn-reset" @click="doreset" style="margin-left: 8px">重置</a-button>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>  -->

      <a-card :bordered='false' style='width:100%;flex: auto'>
          <div class='table-operator table-operator-style'>
            <a-button class='btn-add' @click="handleExportXls('区县在线统计')">导出</a-button>
          </div>
          <a-table
            ref='table'
            bordered
            rowKey='id'
            :columns='columns'
            :dataSource='dataSource'
            :scroll='dataSource.length > 0 ? { x:"max-content"} : {}'
            :pagination='ipagination'
            :loading='loading'
            @change='handleTableChange'
          >
            <template slot='tooltip' slot-scope='text'>
              <a-tooltip placement='topLeft' :title='text' trigger='hover'>
                <div class='tooltip'>
                  {{ text }}
                </div>
              </a-tooltip>
            </template>
          </a-table>
      </a-card>
    </a-col>
  </a-row>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import JSuperQuery from '@/components/jeecg/JSuperQuery.vue'
import { queryAssetsCategoryTreeList } from '@/api/device'
import { httpAction, getAction, deleteAction } from '@/api/manage'
import JDictSelectTag from '@/components/dict/JDictSelectTag.vue'
import { getRootAreaId } from '@/components/dict/JDictSelectUtil'

export default {
  name: 'OnlineStatistics',
  mixins: [JeecgListMixin, mixinDevice],
  components: {
    JSuperQuery,
    JDictSelectTag
  },
  data() {
    return {
      disableMixinCreated: true,
      searchKey: undefined,
      bsearchKey: '',
      value: undefined,
      replaceFields: {
        children: 'children',
        title: 'deptName',
        key: 'deptId',
        value: 'deptId'
      },
      //tree
      batchEnable: 1,
      description: '设备表管理页面',
      firstTitle: '', //存储搜素tree的第一个title
      // 树
      assetsCategoryTree: [],
      treeData: [],
      expandedKeys: [],
      searchValue: '',
      autoExpandParent: true,
      dropTrigger: '',
      selectedKeys: [],
      selectedTitle: '',
      checkedKeys: [],
      checkStrictly: true,
      // iExpandedKeys: [],
      currFlowId: '',
      currFlowName: '',
      rightClickSelectedBean: {},
      // 表头
      columns: [
        { title: '区县', dataIndex: 'cityName',scopedSlots: { customRender: 'tooltip' },
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 150px;max-width:400px'
            return { style: cellStyle }
          }},
        { title: '时间', dataIndex: 'createTime',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 100px;max-width:220px'
            return { style: cellStyle }
          }},
        { title: '网络类型', dataIndex: 'gatewayType_dictText', key: '15',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 100px;max-width:150px'
            return { style: cellStyle }
          } },
        // { title: '注册总数',dataIndex: 'count'},
        // {title: '计划数量',dataIndex: 'planNumber'},
        { title: '未开机', dataIndex: 'offCount', scopedSlots: { customRender: 'offCount' } ,
          customCell: () => {
            let cellStyle = 'text-align: right;min-width: 120px;max-width:300px'
            return { style: cellStyle }
          }},
        { title: '已开机（联网超过30分钟）', dataIndex: 'yesThreeCount',
          customCell: () => {
            let cellStyle = 'text-align: right;min-width: 120px;max-width:300px'
            return { style: cellStyle }
          }},
        { title: '已开机（联网不足30分钟）', dataIndex: 'noThreeCount',
          customCell: () => {
            let cellStyle = 'text-align: right;min-width: 120px;max-width:300px'
            return { style: cellStyle }
          }},
        { title: '有效使用数', dataIndex: 'effectiveCount' ,
          customCell: () => {
            let cellStyle = 'text-align: right;min-width: 100px;max-width:300px'
            return { style: cellStyle }
          } }
      ],
      url: {
        list: '/device/statis/findCityStatisGroup',
        getStatisticsNum: '/device/statis/findDeviceStatisDay',
        exportXlsUrl: '/device/statis/findCityStatisGroupExcel',
        importExcelUrl: 'device/deviceInfo/importExcel',
        getUrl: '/device/deviceInfo/getUrl'
      },
      dataSource: [], //table数据
      dictOptions: {},
      superFieldList: [],
      name: '',
      status: '',
      statuslist: [
        {
          name: '在线',
          code: '1'
        },
        {
          name: '离线',
          code: '0'
        },
        {
          name: '告警',
          code: '2'
        }
      ],
      categoryId: '', //选取tree的key
      selectOption: [],
      statisticsInfo: {
        allNum: 0,
        openLessTY: 0,
        openMoreTY: 0,
        planNum: 0
      }
    }
  },

  async created() {
    await getRootAreaId().then(res => {
      this.queryParam.cityId = res.data
     })
    // this.deptSelect()
    this.loadData()
    //初始化字典配置 在自己页面定义
    this.initDictConfig()
    this.select()
    this.getStatisticsNum()
  },
  computed: {
    importExcelUrl: function() {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    }
  },
  methods: {
    select() {
      getAction('/sys/sysDepart/queryAllTree').then((res) => {
        for (let i = 0; i < res.length; i++) {
          let temp = res[i]
          this.selectOption.push(temp)
        }
      })
    },

    onChangeTree(value) {
      this.value = value
      // this.queryParam.deptId = value.join(",")
    },
    onSearch(e) {
      this.bsearchKey = e
    },
    onSelect() {
    },
    onChangePicker(value, dateString) {
      this.queryParam.startTime = dateString[0]
      this.queryParam.endTime = dateString[1]
    },
    getStatisticsNum() {
      getAction(this.url.getStatisticsNum).then((res) => {
        if (res.code == 200) {
          this.statisticsInfo.allNum = res.result ? res.result.deviceTotle : 0
          this.statisticsInfo.openLessTY = res.result ? res.result.onNotPassCount : 0
          this.statisticsInfo.openMoreTY = res.result ? res.result.onPassCount : 0
          this.statisticsInfo.planNum = res.result ? res.result.planNumber : 0
          // this.selectOption = res.result
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // deptSelect() {
    //   getAction('device/momgDept/queryAllDepts').then(res => {
    //     if (res.code == 200) {
    //       this.selectOption = res.result
    //     } else {
    //       this.$message.error(res.message)
    //     }
    //   })
    // },
    //表单查询,点击查询按钮，默认查询第一页
    dosearch() {
      if (!this.searchKey && this.bsearchKey) {
        this.searchKey = this.bsearchKey
      }
      if (Array.isArray(this.searchKey)) {
        this.queryParam.deptId = this.searchKey.join(',')
      } else if (typeof this.searchKey === 'string') {
        this.queryParam.deptId = this.searchKey
        // this.searchKey = ""
      }

      this.bsearchKey = ''
      this.loadData(1)
    },
    //表单重置
    doreset() {
      //重置form表单，不重置tree选中节点
      this.queryParam = {}
      this.searchKey = undefined
      this.bsearchKey = ''
      this.loadData(1)
      this.type = undefined
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
.col-example{
  margin-bottom: 16px;
}

.col-one {
  padding-left: 0px !important;
  border-radius: 3px;
  background-image: linear-gradient(90deg, #5d95fc 5%, #aecbff 97%);

}
.gutter-box-one {
  height: 1.2875rem /* 103/80 */;
  margin: 0.2rem /* 16/80 */ 0.3rem /* 24/80 */;
  padding: 0.125rem /* 10/80 */ 0.125rem;
  background: rgba(255, 255, 255, 0.1);

  .gutter-box-one-left-top {
    height: 50%;
    display: flex;
    justify-content: center;

    span {
      font-family: MicrosoftYaHei;
      font-size: 0.2rem /* 16/80 */;
      color: rgba(255, 255, 255, 0.85);
      display: flex;
      align-items: center;
    }
  }

  .gutter-box-one-left-bottom {
    height: 50%;
    display: flex;
    justify-content: center;

    span {
      font-family: Eurostile-Bold;
      font-size: 0.5rem /* 40/80 */;
      color: #ffffff;
      display: flex;
      align-items: center;
    }
  }
}

.col-two {
  background-image: linear-gradient(90deg, #29ca7f 4%, #c3fcc0 98%);
  padding-left: 0px !important;
  margin-right: 12px !important;
  border-radius: 3px;
}

.gutter-box-two {
  height: 1.2875rem /* 103/80 */;
  margin: 0.2rem /* 16/80 */ 0.3rem /* 24/80 */;
  padding: 0.125rem /* 10/80 */ 0px;
  background: rgba(255, 255, 255, 0.1);

  .gutter-box-two-left-top {
    height: 50%;
    display: flex;
    justify-content: center;

    span {
      font-family: MicrosoftYaHei;
      font-size: 0.2rem /* 16/80 */;
      color: rgba(255, 255, 255, 0.85);
      display: flex;
      align-items: center;
    }
  }

  .gutter-box-two-left-bottom {
    height: 50%;
    display: flex;
    justify-content: center;

    span {
      font-family: Eurostile-Bold;
      font-size: 0.5rem /* 40/80 */;
      color: #ffffff;
      display: flex;
      align-items: center;
    }
  }
}

.col-three {
  background-image: linear-gradient(90deg, #f44967 5%, #fdbac5 93%);
  padding-left: 0!important;
  margin-right: 16px !important;
  border-radius: 3px;
}

.col-four {
  background-image: linear-gradient(90deg, #49f46e 5%, #fdbac5 93%);
  padding-left: 0!important;
  margin-right: 16px !important;
  border-radius: 3px;
}

.gutter-box-three {
  height: 1.2875rem /* 103/80 */;
  margin: 0.2rem /* 16/80 */ 0.3rem /* 24/80 */;
  padding: 0.125rem /* 10/80 */ 0px;
  background: rgba(255, 255, 255, 0.1);

  .gutter-box-three-left-top {
    height: 50%;
    display: flex;
    justify-content: center;

    span {
      font-family: MicrosoftYaHei;
      font-size: 0.2rem /* 16/80 */;
      color: rgba(255, 255, 255, 0.85);
      display: flex;
      align-items: center;
    }
  }

  .gutter-box-three-left-bottom {
    height: 50%;
    display: flex;
    justify-content: center;

    span {
      font-family: Eurostile-Bold;
      font-size: 0.5rem /* 40/80 */;
      color: #ffffff;
      display: flex;
      align-items: center;
    }
  }
}

.div-table-container {
  /* padding: 18px 30px 18px 24px; */
  /* background-color: white; */
  //margin-top: 16px;
  /* margin-right: -9px; */
  /* height: calc(100% - 158px); */
}

.gutter-example {
  width: 100%;
}

.row-example {
  margin: 0px !important;
}

.gutter-row {
  padding-right: 0px !important;
  //margin-right: 10px;
  //margin-bottom: 16px;
}
.gutter-row-four {
  padding-right: 0px !important;
  border-radius: 3px;
  margin-bottom: 16px;
}

.posi-col {
  position: relative;
}

.sync-img {
  position: absolute;
  top: 14px;
  right: 20px;
  cursor: pointer;
  color: #fff;
  z-index: 100;
}

.p-device-status {
  text-align: center;
  height: 30px;
  line-height: 30px;
  margin-bottom: 0px;
}

.span-title {
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
}

.span-num {
  font-family: PingFangSC-Medium;
  font-size: 24px;
}

.color-blue {
  color: #409eff;
}

.color-green {
  color: #139b33;
}

.color-red {
  color: #df1a1a;
}

.color-grey {
  color: #868686;
}

/*.ant-row .ant-col-3 .ant-card .ant-card-body {
  height: 810px !important;
}*/
/*表头样式*/
::v-deep .ant-table-thead > tr > th {
  text-align: center;
  white-space: nowrap;
}

/*内容对齐方式、省略显示*/
::v-deep .ant-table-tbody > tr > td {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

</style>

