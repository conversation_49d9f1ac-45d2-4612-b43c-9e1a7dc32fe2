<template>
  <div :gutter='10' class='stratery-terminals'>
    <div style='width: 100%; height: 100%; display: flex; flex-direction: column'>
      <!-- 查询区域 -->
      <a-card :bordered='false' :bodyStyle="{ padding: '0' }" >
        <div >
          <a-row type='flex' :gutter='12' >
            <a-col :xxl='6' :xl='8' :lg='12' :sm='24'>
              <div class='s-item'>
                <div class='s-label'>终端名称：</div>
                <div class='s-wrapper'>
                  <a-input placeholder='请输入名称' v-model='queryParam.name' :allowClear='true' autocomplete='off'
                    :maxLength="50" />
                </div>
              </div>
            </a-col>
            <a-col :xxl='5' :xl='8' :lg='12' :sm='24'>
              <div class='s-item'>
                <div class='s-label'>操作系统：</div>
                <div class='s-wrapper'>
                  <a-select
                    style='width: 100%'
                    :getPopupContainer='(node) => node.parentNode'
                            allowClear
                            v-model='osTypeValues'
                            placeholder='请选择操作系统'
                            @change='setQueryParam($event,"osType")'>
                    <a-select-option v-for='i in osTypeList' :key='"osType_"+i.value' :value='i.value'>
                      {{ i.text }}
                    </a-select-option>
                  </a-select>
                </div>
              </div>
            </a-col>
            <a-col :xxl='5' :xl='8' :lg='12' :sm='24'>
              <div class='s-item'>
                <div class='s-label'>cpu架构：</div>
                <div class='s-wrapper'>
                  <j-dict-select-tag style='width: 100%' v-model='queryParam.cpuArch' placeholder='请选择cpu架构' dictCode='cpuArch' />
                </div>
              </div>
            </a-col>
            <a-col :xxl='6' :xl='8' :lg='12' :sm='24'>
              <div class='s-item'>
                <div class='s-label'>所属单位：</div>
                <div class='s-wrapper'>
                  <a-tree-select
                    style='width: 100%'
                    :getPopupContainer='(node) => node.parentNode'
                    v-model='searchedDepKey'
                    tree-node-filter-prop='title'
                    :replaceFields='replaceFields'
                    :treeData='departsTreeData'
                    show-search
                    :searchValue='bSearchedDepKey'
                    multiple
                    :maxTagCount='1'
                    :dropdownMatchSelectWidth="true"
                    :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                    placeholder='请选择所属单位'
                    allow-clear
                    @change='onChangeDeparts'
                    @search='onSearchDeparts'
                    @select='onSelectDeparts'>
                  </a-tree-select>
                </div>
              </div>
            </a-col>
            <a-col  :xxl='2' :xl='8' :lg='12' :sm='24'>
              <a-button  class='save-btn' icon="search" @click='dosearch'></a-button>
              <a-button style='margin-left: 5px' class='save-btn' icon="sync"  @click='doreset'></a-button>
            </a-col>
          </a-row>
        </div>
      </a-card>
      <!-- 查询区域-END -->
      <a-card :bordered='false' style='width: 100%; flex: auto' :bodyStyle="{ padding: '0' }">
        <!-- table区域-begin -->
        <a-table
          ref='table'
          size='small'
          bordered
          :rowKey='(record,index)=>record.id'
          :columns='columns'
          :dataSource='dataSource'
          :scroll='dataSource.length>0?{x:"max-content"}:{}'
          :pagination='ipagination'
          :loading='loading'
          :rowSelection='{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }'
          @change='handleTableChange'
        >
        </a-table>
      </a-card>
    </div>
  </div>
</template>
<script>
import { getUserList } from '@/api/api'
import Vue from 'vue'
import '@/assets/less/TableExpand.less'
import { mixinDevice} from '@/utils/mixin'
import {JeecgListMixin} from '@/mixins/JeecgListMixin'
import { ajaxGetDictItem,getDictItemsFromCache} from '@/api/api'
import {httpAction,getAction,deleteAction} from '@/api/manage'
import barRateChart from '@views/terminalManage/terminalInfo/modules/barRateChart.vue'
//引入公共devicetree组件
import TerminalDeptTree from '@/components/tree/TerminalDeptTree.vue'
import YqAreaCascaderSelect from '@/components/areaDict/YqAreaCascaderSelect'
import {YqFormSearchLocation} from '@/mixins/YqFormSearchLocation'
export default {
  name: 'StrategyTerminals',
  mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
  components: { YqAreaCascaderSelect },
  props: {
    terminals:{
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      description: '终端信息页面',
      formItemLayout: {
        labelCol: {
          style: 'width:90px'
        },
        wrapperCol: {
          style: 'width:calc(100% - 90px)'
        }
      },
      //单位tree
      searchedDepKey: undefined,
      bSearchedDepKey: '',
      departsTreeData: [],
      replaceFields: {
        children: 'children',
        title: 'deptName',
        key: 'deptId',
        value: 'deptId'
      },
      osTypeList:[],
      // platformList:[],
      osTypeValues:undefined,
      cpuTypeList:[],
      cpuTypeValues:undefined,
      terminalTypeList: [],
      terminalValues:undefined,
      batchEnable: 1,
      //表头
      columns: [
        {
          title: '序号',
          dataIndex: '',
          disabled:false,
          key: 'rowIndex',
          width:60,
          customRender: (t, r, index) => {
            return parseInt(index) + 1
          }
        },
        {
          title: '终端名称',
          dataIndex: 'name',
          customCell: () => {
            let cellStyle = 'text-align: center'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '操作系统',
          dataIndex: 'osType_dictText',
          disabled:false,
          checked:true,
          key: '2'
        },
        {
          title: 'cpu架构',
          dataIndex: 'cpuArch',
          disabled:false,
          checked:true,
          key: '16'
        },
        {
          title: '所属单位',
          dataIndex: 'deptId_dictText',
          checked:true,
          disabled:false,
          key: '4',
          customCell: () => {
            let cellStyle = 'text-align: center'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'tooltip'
          }
        },
      ],
      //刷新列表操作设置
      refreshKey: 0,
      dragItemIndex:0,
      dragDom:null,
      isDown: 1,
      url: {
        list: '/terminal/terminalDevice/list',
        delete: '/terminal/terminalDevice/delete',
        edit: '/terminal/terminalDevice/edit',
        deleteBatch: '/terminal/terminalDevice/deleteBatch',
        exportXlsUrl: '/terminal/terminalDevice/exportXls',
        // getPlatform: '/dataReport/manage/list',
      },
      disableMixinCreated: true,
    }
  },
  created() {
    this.queryParam.isSimple=true;
    this.selectedRowKeys = JSON.parse(JSON.stringify(this.terminals))
    this.initDictConfig()
    this.loadDepartsData()
    this.loadData(1)
  },
  computed: {
  },
  methods: {
    initDictConfig() {
      this.getDictData('resources_type','terminalTypeList')
      this.getDictData('cpuType','cpuTypeList')
      this.getDictData('os_type','osTypeList')
    },
    getDictData(dictCode,list) {
      ajaxGetDictItem(dictCode, null).then((res) => {
        if (res.success) {
          this[list]=res.result
        }
      })
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      )
    },
    loadDepartsData() {
      getAction('/sys/sysDepart/queryAllTree').then((res) => {
        for (let i = 0; i < res.length; i++) {
          let temp = res[i]
          this.departsTreeData.push(temp)
        }
      })
    },
    onChangeDeparts(value) {
      // this.queryParam.deptId = value.join(",")
    },
    onSearchDeparts(e) {
      this.bSearchedDepKey = e
    },
    onSelectDeparts() {
    },

    setQueryParam(e,field) {
      this.queryParam[field] = Array.isArray(e)?e.join(','):e
    },

    //勾选时进行状态判断
    onSelectChange(selectedRowKeys, selectionRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectionRows = selectionRows
      this.$emit('change', selectedRowKeys)
    },

    //表单查询,点击查询按钮，默认查询第一页
    dosearch() {
      if (!this.searchedDepKey && this.bSearchedDepKey) {
        this.searchedDepKey = this.bSearchedDepKey
      }
      if (Array.isArray(this.searchedDepKey)) {
        this.queryParam.deptId = this.searchedDepKey.join(',')
      } else if (typeof this.searchedDepKey === 'string') {
        this.queryParam.deptId = this.searchedDepKey
        // this.searchedDepKey = ""
      }
      this.bSearchedDepKey = ''
      this.loadData(1)
    },
    //重置
    doreset() {
      this.queryParam = {}
      this.searchedDepKey = undefined
      this.bSearchedDepKey = ''
      this.osTypeValues=undefined
      this.cpuTypeValues=undefined
      this.terminalValues=undefined
      this.queryParam.isSimple=true;
      this.loadData(1)
    },
  }
}
</script>
<style scoped lang='less'>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
.stratery-terminals{
  width: 100%;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
}
.save-btn{
  background: #ECF5FF;
  border: 1px solid #B3D8FF;
  border-radius: 4px;
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: #409EFF;
  font-weight: 400;
}
.ant-btn.save-btn:hover, .ant-btn.save-btn:focus {
  color: #409EFF;
  background-color: #fff;
  border: 1px solid #B3D8FF;
}
.s-item{
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  .s-label{
    text-align: right;
    width: 90px;
  }
  .s-wrapper{
    flex: 1;
  }
}
</style>