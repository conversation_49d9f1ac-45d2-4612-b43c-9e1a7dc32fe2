<template>
  <div style='background-color: #FFFFFF;height: 100%; border-radius: 3px;display: flex;flex-direction: column'>
    <div style='margin:20px 14px 14px 14px'>
      <a-input-search @change='onChange' v-if='inputFlag' v-model='inputSearch' style='width: 100%'
        :placeholder='placeholder' />
    </div>
    <!-- 树-->
    <div class='hScroll vScroll' style='flex:auto;margin:5px 14px 14px 14px'>
      <template>
        <a-dropdown :trigger='[this.dropTrigger]' @visibleChange='dropStatus'>
          <a-tree @select='onSelect' :selectedKeys='selectedKeys' :defaultSelectedKeys="defaultSelectedKeys"
            :checkedKeys='checkedKeys' :treeData='assetsCategoryTree' :checkStrictly='checkStrictly'
            :expandedKeys='expandedKeys' @expand='onExpand' style='user-select: none' :showIcon='true'
            :defaultExpandedKeys="defaultExpandedKeys">
            <template slot='title' slot-scope='{ title}'>
              <span v-if='title.indexOf(searchValue) > -1 && title !== firstTitle'>
                {{ title.substr(1, title.indexOf(searchValue)) }}
                <span style='color: #f50'>{{ searchValue }}</span>
                {{ title.substr(title.indexOf(searchValue) + searchValue.length) }}
              </span>
              <span v-else-if='title.indexOf(searchValue) > -1 && title === firstTitle'>
                <span style='background-color: #bae7ff; color: #f50'>{{ title }}</span>
              </span>
              <span v-else>{{ title }}</span>
            </template>
          </a-tree>
        </a-dropdown>
      </template>
    </div>
  </div>
</template>
<script>
  import {
    queryAssetsCategoryTreeList
  } from '@/api/device'
  import {
    getAction,
  } from '@/api/manage'

  //通过Key获取对应地title
  const getTitleByKey = (key, tree) => {
    let selectTitle
    for (let i = 0; i < tree.length; i++) {
      const node = tree[i]
      if (node.key === key) {
        selectTitle = node.title
        break
      }
      if (node.children) {
        selectTitle = getTitleByKey(key, node.children)
      }
    }
    return selectTitle
  }
  //子节点匹配，获取父节点，用于展开tree
  const getParentKey = (key, tree) => {
    let parentKey
    for (let i = 0; i < tree.length; i++) {
      const node = tree[i]
      if (node.children) {
        if (node.children.some((item) => item.key === key)) {
          parentKey = node.key
        } else if (getParentKey(key, node.children)) {
          parentKey = getParentKey(key, node.children)
        }
      }
    }
    return parentKey
  }
  //生成tree节点的数组[{ key, title: node.title }]
  const dataList = []
  const generateList = (data) => {
    for (let i = 0; i < data.length; i++) {
      const node = data[i]
      dataList.push({
        key: node.id,
        title: node.title
      })
      if (node.children) {
        generateList(node.children)
      }
    }
  }

  //为tree生成对应地title slot
  const generateTitleSlotScopes = (data) => {
    for (let i = 0; i < data.length; i++) {
      let keys = Object.keys(data[i])
      data[i].disabled = keys.includes('userWithPermission') ? !data[i].userWithPermission : false
      if (data[i].children) {
        generateTitleSlotScopes(data[i].children)
      }
    }
  }
  export default {
    name: 'ip-tree',
    data() {
      return {
        firstTitle: '', //存储搜素tree的第一个title
        inputSearch: '',
        // 树
        option: '',
        type: '',
        assetsCategoryTree: [],
        expandedKeys: [],
        searchValue: '',
        dropTrigger: '',
        selectedKeys: [],
        selectedTitle: '',
        checkedKeys: [],
        checkStrictly: true,
        defaultExpandedKeys: [],
        defaultSelectedKeys: [],
        curSelectedNode: undefined
      }
    },
    props: {
      fieldKey: {
        type: String,
        default: 'key',
        required: false
      },
      placeholder: {
        type: String,
        default: '请输入类型名称',
        required: false
      },
      //树节点type值数组，要与后端传的type值一一对应
      arrType: {
        type: Array,
        default: null,
        required: false
      },
      //根据type值，树节点应用不用的icon
      iconName: {
        type: Array,
        default: null,
        required: false
      },
      treeUrl: {
        type: String,
        default: '/assetscategory/assetsCategory/selectTree',
        required: false
      },
      params: {
        type: Object,
        default: () => {
          return {}
        },
        required: false
      },
      //是否显示搜索框
      inputFlag: {
        type: Boolean,
        default: true,
        required: false
      }
    },
    watch: {},
    created() {
      this.loadTree()
    },
    methods: {
      setIcon(type) {
        let name = '';
        if (this.arrType.length > 0) {
          for (let i = 0; i < this.arrType.length; i++) {
            if (this.arrType[i] == type) {
              name = this.iconName[i];
              break;
            }
          }
        }
        return name
      },
      // 查询树
      loadTree(treeKey) {
        var that = this
        that.assetsCategoryTree = []
        getAction(this.treeUrl, this.params).then((res) => {
          if (res.success) {
            // 部门全选后，再添加部门，选中数量增多
            this.allTreeKeys = []
            if (res.result.length > 0) {
              generateTitleSlotScopes(res.result)
              that.assetsCategoryTree = [...res.result]
            }
            this.loading = false
            generateList(that.assetsCategoryTree)
            // that.defaultExpandedKeys.push(that.assetsCategoryTree[0].id)
            // that.defaultSelectedKeys.push(that.assetsCategoryTree[0].children[0].id)
            if (treeKey != null && treeKey != '') {
              this.selectedKeys = treeKey
            }
          }
        })
      },
      // 右键点击下拉框改变事件
      dropStatus(visible) {
        if (visible == false) {
          this.dropTrigger = ''
        }
      },
      // 选择树的方法
      onSelect(selectedKeys, e) {
        //当前节点被选中，连着第二次点击时，selectedKeys会变为[],有点类似取消选中，
        if (selectedKeys.length > 0) {
          this.selectedKeys = selectedKeys
          this.firstTitle = ''
          this.option = e.selectedNodes[0].data.props.dataRef[this.fieldKey]
          this.type = e.selectedNodes[0].data.props.dataRef.type
          let title = e.selectedNodes[0].data.props.dataRef.title
          this.curSelectedNode = e.node
          //向父组件弹射抛值
          this.$emit('selected', this.option, e.selectedNodes[0].data.props)
        }
      },
      //tree的查询框输入时，默认选中匹配的第一个（用firstTitle表示）
      onChange(e) {
        const value = e.target.value
        this.searchValue = value
        //查询框第一个匹配的node对应的key
        let firstSearchedKey = ''
        const expandedKeys = dataList.map((item) => {
          if (item.title.indexOf(value) > -1) {
            //查询框第一个匹配的node对应的key
            if (firstSearchedKey == '') {
              firstSearchedKey = item[this.fieldKey]
            }
            return getParentKey(item[this.fieldKey], this.assetsCategoryTree)
          }
          return null
        }).filter((item, i, self) => item && self.indexOf(item) === i)
        Object.assign(this, {
          expandedKeys,
          searchValue: value,
        })
        if (this.expandedKeys.length > 0 && value.trim().length > 0) {
          this.firstTitle = getTitleByKey(firstSearchedKey, this.assetsCategoryTree)
          this.selectedKeys = [firstSearchedKey]
          //向父组件弹射抛值
          this.$emit('selected', firstSearchedKey)
        }
        if (value.trim().length == 0) {
          //查询设备信息,此情况下，没有分类被选中
          this.firstTitle = ''
          this.selectedKeys = []
          //向父组件弹射抛值
          this.$emit('selected')
        }
      },
      onCheck(checkedKeys, e) {
        this.hiding = false
        // this.checkedKeys = checkedKeys.checked
        // <!---- author:os_chengtgen -- date:20190827 --  for:切换父子勾选模式 =======------>
        if (this.checkStrictly) {
          this.checkedKeys = checkedKeys.checked
        } else {
          this.checkedKeys = checkedKeys
        }
        // <!---- author:os_chengtgen -- date:20190827 --  for:切换父子勾选模式 =======------>
      },
      // 右键操作方法
      rightHandle(node) {
        this.dropTrigger = 'contextmenu'
        this.rightClickSelectedKey = node.node.eventKey
        this.rightClickSelectedBean = node.node.dataRef
      },
      onExpand(expandedKeys) {
        this.expandedKeys = expandedKeys
      }
    }
  }
</script>
<style lang='less' scoped>
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';

  /*垂直滚动条*/
  .vScroll::-webkit-scrollbar {
    margin-right: -24px !important;
  }

  /*水平滚动条*/
  .hScroll::-webkit-scrollbar {
    margin-right: -24px !important;
    margin-left: -24px !important;
  }
</style>