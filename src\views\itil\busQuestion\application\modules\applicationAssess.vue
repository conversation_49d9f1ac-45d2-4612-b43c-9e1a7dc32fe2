<template>
  <a-drawer
    :width="modalWidth"
    :title="title"
    placement="right"
    :closable="false"
    :destroyOnClose="true"
    :visible="visible"
    :after-visible-change="afterVisibleChange"
    @close="onClose"
  >
    <a-form ref="form" :model="form" :label-width="85">
      <div>
        <a-form-item label="审核结果" prop="reason">
          <a-radio-group name="radioGroup" :default-value="0" v-model="form.type">
            <a-radio :value="0">
              通过
            </a-radio>
            <a-radio :value="1">
              驳回
            </a-radio>

          </a-radio-group>
        </a-form-item>
        <a-form-item label="审批意见" prop="reason">
            <a-input type="textarea" v-model="form.comment" :rows="4" />
        </a-form-item>
        <a-form-item label="满意度" >
           <a-rate v-model="form.rateValue" />
        </a-form-item>
      </div>
    </a-form>
    <div
        :style="{
          position: 'absolute',
          right: 0,
          bottom: 0,
          width: '100%',
          borderTop: '1px solid #e9e9e9',
          padding: '10px 16px',
          background: '#fff',
          textAlign: 'right',
          zIndex: 1,
        }"
      >
        <a-button :style="{ marginRight: '8px' }" @click="onClose">
          取消
        </a-button>
        <a-button type="primary" @click="handelSubmit">
          提交
        </a-button>
      </div>
  </a-drawer>
</template>
<script>
import { queryDepartTreeList } from '@/api/api'
import JUpload from '@/components/jeecg/JUpload'
import JSelectUserByDep from '@/components/jeecgbiz/JSelectUserByDep'
import { getAction, deleteAction, putAction, postAction } from '@/api/manage'
import LSelectUserByDep from '@/components/jeecgbiz/LSelectUserByDep'
// import JSelectUserByDep from '@/components/jeecgbiz/JSelectUserByDep'
export default {
  components:{JSelectUserByDep,JUpload,LSelectUserByDep},
  name: 'applicationAssess',
  data() {
    return {
      form: {
        id: "",
        type:"",
        comment:'',
        rateValue:0

      },
      rateValue:0,
      title: '评价',
      visible: false,
       modalWidth: '25%',
      url:{
        todoList:'/actTask/todoList',
        pass:'/question/pass',
        back:'/question/back',
        backToTask:'/question/backToTask',
        delegate:'/actTask/delegate',
        getNextNode:'/activiti_process/getNextNode',
        getNode:'/activiti_process/getNode/',
        getBackList:'/actTask/getBackList/',
        passAll:'/actTask/passAll/',
        backAll:'/actTask/backAll/',
        assess:'/question/assess'
      },
    }
  },
  created() {
      // this.loadTree()
  },
  methods: {
    afterVisibleChange(val) {
    },
    handelSubmit() {

      this.submitLoading = true;
      var formData = Object.assign({},this.form);
      this.adopt(formData);
    },
    //通过
    adopt(formData){

        postAction(this.url.assess,formData).then(res => {
          this.submitLoading = false;
          if (res.success) {
            this.$message.success("操作成功");
            this.visible = false;
            this.$emit('ok')
          }
        });
    },

    show(v) {

      this.form.id = v.id;
      this.form.rateValue = 0;
      this.form.type = 0;
      this.form.comment='';

      //this.getAssignees();
    },
    //下级审批人

     //获取驳回节点
     backTask() {

      // 获取可驳回节点
      this.backList = [];
      this.form.backTaskKey = this.processDefinition.assignedNodeId;
      this.backLoading = true;
      getAction(this.url.getBackList+this.processDefinition.procInstId).then(res => {
        this.backLoading = false;
        if (res.success) {
          res.result.forEach(e => {

            if(e.key === this.processDefinition.assignedNodeId){
              this.backList.push(e);
            }

          });
        }
      });
      this.changeBackTask(this.form.backTaskKey);
    },
     onClose() {
      this.visible = false
    },
    setAssignees(){
      this.form.assignees = this.assignedUser;
    },
    onExpand(expandedKeys) {
      // if not set autoExpandParent to false, if children expanded, parent can not collapse.
      // or, you can remove all expanded children keys.
      this.expandedKeys = expandedKeys
      this.autoExpandParent = false
    },
    onCheck(checkedKeys) {
      this.checkedKeys = checkedKeys
    },
    onSelect(selectedKeys, info) {
      this.selectedKeys = selectedKeys
    },
    buttonClick(){
      this.$router.push({ path: '/change/apply', query: {questionId:this.processDefinition.busId}});
    }
  },

  watch: {},
  mounted() {

  }
}
</script>
<style scoped>
.drawer-bootom-button {
  position: absolute;
  bottom: 0;
  width: 100%;
  border-top: 1px solid #e8e8e8;
  padding: 10px 16px;
  text-align: right;
  left: 0;
  background: #fff;
  border-radius: 0 0 2px 2px;
}
</style>
