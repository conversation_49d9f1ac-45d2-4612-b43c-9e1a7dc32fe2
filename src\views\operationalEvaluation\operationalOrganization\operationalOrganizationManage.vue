<template>
    <div style="height:100%">
      <component :is="pageName" :data="data" />
    </div>
  </template>
  <script>
    import operationalOrganizationList from './operationalOrganizationList'
    import OperationalOrganizationDetail from './modules/OperationalOrganizationDetail'
    export default {
      name: "operationalOrganizationManage",
      data() {
        return {
          isActive: 0,
          data: {}
        };
      },
      components: {
        operationalOrganizationList,
        OperationalOrganizationDetail
      },
      created() {
        this.pButton1(0);
      },
      //使用计算属性
      computed: {
        pageName() {
          switch (this.isActive) {
            case 0:
              return "operationalOrganizationList";
              break;
  
            default:
              return "OperationalOrganizationDetail";
              break;
          }
        }
      },
      methods: {
        pButton1(index) {
          this.isActive = index;
        },
        pButton2(index, item) {
          this.isActive = index;
          this.data = item;
        }
      }
    }
  </script>