<template>
  <j-modal :title="title" :width="width" :centered="true" :visible="visible" :destroyOnClose="true" switchFullscreen
    cancelText="关闭" :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }" @ok="handleOk" @cancel="handleCancel">
    <a-spin :spinning="confirmLoading">
      <j-form-container>
        <a-form-model ref="form" slot="detail" :model="model" :rules="validatorRules" v-bind="formItemLayout">
          <a-row>
            <a-col :span='23'>
              <a-form-model-item class='two-words' label='模板名称' prop='templateName'>
                <a-input v-model='model.templateName' :allowClear='true' autocomplete='off' placeholder='请输入模板名称' />
              </a-form-model-item>
            </a-col>
            <a-col :span='23'>
              <a-form-model-item label='导入文件' prop='method'>
                <a-radio-group :default-value="'0'" v-model='model.method' @change='fucusContent'>
                  <a-radio :value="'1'">
                    是
                  </a-radio>
                  <a-radio :value="'0'">
                    否
                  </a-radio>
                </a-radio-group>
              </a-form-model-item>
            </a-col>
            <a-col :span='23' v-if='model.method==1'>
              <a-form-model-item prop='files' :key='"files"'>
                <span slot='label'>
                  <span>选择文件</span>
                  <a-popover title='说明'>
                    <template slot='content'>
                      <p>文件不能超过20MB</p>
                      <p>支持文件格式：.docx</p>
                      <p>限制只可选择一个文件</p>
                    </template>
                    <a-icon style='margin-left:5px;font-size: 20px; line-height: 40px' theme='twoTone'
                      type='question-circle' />
                  </a-popover>
                </span>
                <word-upload v-model='model.tempFiles' :upload-action='url.importFile' :file-list='fileList'
                  :is-show-upload-icon='false' :text='"选择文件"' :multiple="false" :number='1' :accept='accept'
                  :showUploadList='false' :before-upload="beforeUpload" @change='changeImportFile'>
                </word-upload>
              </a-form-model-item>
            </a-col>
            <a-col :span="23">
              <a-form-model-item label="模板内容" prop="template">
                <v-md-editor v-if="['markdown'].includes(model.textType)" v-model="model.template" height="300px"
                  right-toolbar="preview" mode="edit" :left-toolbar="leftToolBar" />
                <j-editor v-else v-model="model.template" />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </j-form-container>
    </a-spin>
  </j-modal>
</template>
<script>
  import VMdEditor from '@kangc/v-md-editor'
  import wordUpload from '@views/opmg/knowledgeManagement/knowledgeBase/modules/WordUpload.vue'
  import {
    setImgAllPath
  } from '@/utils/imagePathAboutTinymce'
  import {
    httpAction
  } from '@/api/manage'
  import githubTheme from '@kangc/v-md-editor/lib/theme/github.js'
  import hljs from 'highlight.js'
  import { ValidateRequiredFields } from '@/utils/rules'
  VMdEditor.use(githubTheme, {
    Hljs: hljs
  })
  export default {
    name: 'addTemplateModal',
    components: {
      VMdEditor,
      wordUpload,
    },
    props: {},
    data() {
      return {
        form: this.$form.createForm(this),
        title: '新增模版',
        width: '800px',
        disableSubmit: false,
        visible: false,
        confirmLoading: false,
        fileList: null,
        accept: '.docx',
        formItemLayout: {
          labelCol: {
            xs: {
              span: 24,
            },
            sm: {
              span: 5,
            },
          },
          wrapperCol: {
            xs: {
              span: 24,
            },
            sm: {
              span: 18,
            },
          },
        },
        labelCol: {
          lg: {
            span: 6
          },
          md: {
            span: 5
          },
          sm: {
            span: 24
          },
          xs: {
            span: 24
          },
        },
        wrapperCol: {
          lg: {
            span: 17
          },
          md: {
            span: 16
          },
          sm: {
            span: 24
          },
          xs: {
            span: 24
          },
        },
        model: {
          templateName: '',
          template: '',
          method: '0',
          filePaths: ''
        },
        validatorRules: {
          templateName: [{
            required: true,
            validator: (rule, value, callback) =>ValidateRequiredFields(rule, value, callback,'模板名称',20,1)
          }],
          template: [{
            required: true,
            message: '请输入模板内容!',
          }],
        },
        url: {
          add: '/devops/reportTemplate/add',
          edit: '/devops/reportTemplate/edit',
          importFile: window._CONFIG['domianURL'] + '/kbase/knowledges/template/word2Html',
        }
      }
    },
    created() {},
    methods: {
      add() {
        this.edit({})
      },
      edit(record) {
        this.visible = true
        this.$nextTick(() => {
          this.model = JSON.parse(JSON.stringify(record))

        })
      },
      show(record) {
        this.visible = true
      },
      fucusContent(value) {
        this.model.method = value.target.value
      },
      changeImportFile(path, files) {
        if (files.length > 0) {
          if (files[0].success) {
            this.$message.success(files[0].message)
            this.model.template = setImgAllPath(files[0].result)
          } else {
            this.$message.error(files[0].message)
          }
        }
      },
      beforeUpload(file) {
        var testmsg = file.name.substring(file.name.lastIndexOf('.') + 1)
        const extension = this.accept.includes(testmsg)
        if (!extension) {
          this.$message.error('上传文件只能是' + this.accept + '格式')
        }
        return extension;
      },
      close() {
        this.visible = false
      },
      handleCancel() {
        this.close()
      },
      handleOk() {
        const that = this
        // 触发表单验证
        that.$refs.form.validate((err, values) => {
          if (err) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.model.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'put'
            }
            let formData = Object.assign(this.model, values)
            httpAction(httpurl, formData, method)
              .then((res) => {
                if (res.success) {
                  that.$message.success(res.message)
                  that.$emit('ok')
                  this.close()
                } else {
                  that.$message.warning(res.message)
                }
              })
              .finally(() => {
                that.confirmLoading = false
              })
          }
        })
      },
    },
  }
</script>
<style scoped lang="less"></style>