<template>
  <div style='height: 100%;overflow: hidden'>
    <a-row>
      <a-col :xxl='6' :xl='12' :lg='24' class='col-1'>
        <div class='card-item'>
          <img class='card-item-left' src='/oneClickHelp/localDeviceInfo/status.png'></img>
          <div class='card-item-right'>
            <div class='title'>当前状态</div>
            <div class='value' :title='staData.statusText' :style='{color:staData.color}'>{{staData.statusText}}</div>
          </div>
        </div>
      </a-col>
      <a-col :xxl='6' :xl='12' :lg='24' class='col-2'>
        <div class='card-item'>
          <img class='card-item-left' src='/oneClickHelp/localDeviceInfo/powerCount.png'></img>
          <div class='card-item-right'>
            <div class='title'>昨日开机次数</div>
            <div class='value-wrapper' :title='staData.onCount+"次"' v-if='staData.onCount!="暂无数据"'>
              <div class='value'>{{staData.onCount}}</div>
              <div class='count-unit'>次</div>
            </div>
            <div class='value value-font-size' v-else>{{staData.onCount}}</div>
          </div>
        </div>
      </a-col>
      <a-col :xxl='6' :xl='12' :lg='24' class='col-3'>
        <div class='card-item'>
          <img class='card-item-left' src='/oneClickHelp/localDeviceInfo/totalDuration.png'></img>
          <div class='card-item-right'>
            <div class='title'>昨日总时长</div>
            <div class='value-wrapper' :title='staData.onTotal_text' v-if='staData.onTotal_text!="暂无数据"'>
              <div class='value'>{{staData.onTotal_text}}</div>
              <div class='total-unit'></div>
            </div>
            <div class='value value-font-size' v-else>{{staData.onTotal_text}}</div>
          </div>
        </div>
      </a-col>
      <a-col :xxl='6' :xl='12' :lg='24' class='col-4'>
        <div class='card-item'>
          <img class='card-item-left' src='/oneClickHelp/localDeviceInfo/powerTime.png'></img>
          <div class='card-item-right'>
            <div class='title'>最近开机时间</div>
            <div class='value' :title='info.lastOn' style='font-size: 0.25rem' v-if='staData.lastOn!="暂无数据"'>{{info.lastOn.split(' ')[1]}}</div>
            <div class='value value-font-size' v-else>{{staData.lastOn}}</div>
          </div>
        </div>
      </a-col>
    </a-row>
  </div>
</template>
<script>
import { ajaxGetDictItems } from '@api/api'

export default {
  name: "InfoStatistic",
  props: {
    info: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      staData:{
        status: '',
        color:'#a4a8a4',
        statusText:'离线',
        onCount: '暂无数据',
        onTotal:'暂无数据',
        onTotal_text:"暂无数据",
        lastOn: '暂无数据'
      },
      deviceStatusList:[]
    }
  },
  created() {
    this.initDictData()
  },
  watch: {
    info: {
      handler(nValue) {
        if (Object.keys(nValue).length > 0) {
          console.log("nvaalue === ",nValue)
          this.staData = nValue
          this.deviceStatusList.map((item) => {
            if (item.value == this.staData.status) {
              this.staData.statusText = item.text
            }else if(!this.staData.status){
              this.staData.statusText ='离线'
            }
            this.staData.color = item.value == '1' ? '#20D925' : '#a4a8a4'
          })
        } else {
          this.staData.color = '#a4a8a4'
          this.staData.statusText = '离线'
          this.staData.onCount = '暂无数据'
          this.staData.onTotal = '暂无数据'
          this.staData.lastOn = '暂无数据'
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    getDuration(text) {
      if (text.indexOf('.')>-1) {
        return text.split('.')[0]
      } else {
        return text
      }
    },
    initDictData() {
      //根据字典Code, 初始化字典数组
      ajaxGetDictItems('device_status', null).then((res) => {
        if (res.success) {
          this.deviceStatusList = res.result
        }
      })
    },
  }
}
</script>

<style scoped lang="less">
.card-item{
  padding: 0.2rem 0.275rem 0.3125rem 0.5rem;//16 30 25 40/80
  background-image: linear-gradient(1deg, rgba(92,148,255,0.00) 0%, rgba(0,36,124,0.27) 38%, rgba(32,71,218,0.60) 100%);
  border: 1px solid rgba(47,91,255,0.42);
  position: relative;
  display: flex;
  flex-flow: row nowrap;
  justify-content: start;
  align-items: center;

  .card-item-left{
    width:1.225rem;//98px/80px;
    height:1.1875rem;//95px/80px;
  }
  .card-item-right{
    padding-left: 0.3rem;//24/80
    padding-top: 0.275rem;//22/80
    overflow: hidden;

    .title{
      font-size: 0.2rem;//16px
      color: #ffffff;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .value{
      color:#fff;
      font-size: 0.425rem;//28/80
      line-height:0.425rem;//51/80
      font-family: zhengku;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-top: 0.2rem;
    }
    .value-font-size{
      font-size: 0.3rem;//24/80
    }
    .value-wrapper {
      white-space: nowrap;
      display: flex;
      flex-flow: row nowrap;
      justify-content: start;
      align-items: end;
      line-height: 0.4rem;

      .count-unit {
        color: #ffffff;
        font-size: 0.2rem;
        margin-left: 0.2rem;
        align-self: end;
      }

      .total-unit {
        color: #ffffff;
        font-family: zhengku;
        font-size: 0.425rem; //34/80
      }
    }
  }
}
.card-item::before{
  position: absolute;
  content: "";
  top:0;
  left: 5px;
  right: 5px;
  width: calc(100% - 10px);
  height: 2px;
  background-color:#0099FB;
}

.card-item-one{
  .img{
    background-image: url("/oneClickHelp/localDeviceInfo/status.png");
  }
}

@media (min-width: 1601px) {
  .col-1{
    padding: 0 0.1rem 0 0;
  }
  .col-2{
    padding: 0 0.1rem 0 0.1rem;
  }
  .col-3{
    padding: 0 0.1rem 0 0.1rem;
  }
  .col-4{
    padding:0 0 0 0.1rem;
  }
}
@media (max-width: 1600px) and (min-width:1201px) {
  .col-1{
    padding: 0 0.1rem 0.1rem 0;
  }
  .col-2{
    padding: 0 0 0.1rem 0.1rem;
  }
  .col-3{
    padding: 0.1rem 0.1rem 0 0;
  }
  .col-4{
    padding:0.1rem 0 0 0.1rem;
  }
}
@media (max-width: 1200px){
  .col-1{
    padding: 0 0 0.1rem 0;
  }
  .col-2{
    padding: 0.1rem 0 0.1rem 0;
  }
  .col-3{
    padding: 0.1rem 0 0.1rem 0;
  }
  .col-4{
    padding:0.1rem 0 0 0;
  }
}
</style>