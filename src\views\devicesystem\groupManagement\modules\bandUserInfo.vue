<template>
  <div>
    <div>
      <div class="headerBox">
        <div class="colorBox">
          <span class="colorTotal">运维人员</span>
        </div>
      </div>
      <a-table
        ref="table"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        @change="handleTableChange"
      >
        <template slot="tooltip" slot-scope="text">
          <a-tooltip placement="topLeft" :title="text" trigger="hover">
            <div class="tooltip">{{ text }}</div>
          </a-tooltip>
        </template>
      </a-table>
    </div>
  </div>
</template>
<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { getAction } from '@/api/manage'
export default {
  name: 'bandUserModal',
  mixins: [JeecgListMixin],
  data() {
    return {
      record: {},
      url: {
        getUser: '/sys/user/listByCondition'
      },
      dataSource: [],
      disableMixinCreated:true,
      columns: [
        {
          title: '运维人员',
          dataIndex: 'realname',
          customHeaderCell: () => ({ style: { textAlign: 'center' } }),
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 150px;max-width:300px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '联系电话',
          dataIndex: 'phone',
          customHeaderCell: () => ({ style: { textAlign: 'center' } }),
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 150px;max-width:300px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '账号',
          dataIndex: 'username',
          customHeaderCell: () => ({ style: { textAlign: 'center' } }),
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 150px;max-width:300px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '所属部门',
          dataIndex: 'orgCodeTxt',
          customHeaderCell: () => ({ style: { textAlign: 'center' } }),
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 150px;max-width:300px'
            return {
              style: cellStyle
            }
          }
        }
      ]
    }
  },
  props: {
    data: {
      type: Object
    }
  },
  methods: {
    // 获取运维人员
    loadData() {
      if (!this.data.userIds) {
        return false
      }
      getAction(this.url.getUser, {
        username: this.data.userIds,
        pageSize: this.ipagination.pageSize,
        pageNo: this.ipagination.current
      }).then(res => {
        if (res.success && res.result) {
          this.dataSource = res.result.records || res.result
          this.ipagination.total = res.result.total ? res.result.total : 0
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.headerBox {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.colorBox {
  font-size: 14px;
  margin-bottom: 10px;

  .colorTotal {
    padding-left: 7px;
    border-left: 4px solid #1e3674;
  }
}
</style>