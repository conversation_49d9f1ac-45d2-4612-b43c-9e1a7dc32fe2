<template>
<div>
    <a-col :span='23'>
      <a-form-model-item class='two-words' label='服务' prop='service' :rules='validatorRules.service'>
        <a-input v-model='personalizedObject.service' :allowClear='true' autocomplete='off' placeholder='请输入服务' @change='changeValue'/>
      </a-form-model-item>
    </a-col>
</div>
</template>

<script>
export default {
  name: 'http',
  props: {
    data: {
      type: Object,
      required: false,
      default: () => {
        let personalizedObject = {
          service: ''
        }
        return personalizedObject
      }
    }
  },
  data() {
    return {
      personalizedObject: {
        service: ''
      },
      validatorRules: {
        service: [
          { required: false, validator: this.customValidate }
        ]
      }
    }
  },
  watch: {
    data: {
      handler(val) {
        if (Object.keys(val).length > 0) {
          this.personalizedObject = val
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    changeValue() {
      this.$emit('changeModelValue', this.personalizedObject)
    },
    getTips(fullField) {
      let str = ''
      switch (fullField) {
        case 'service':
          str = '请输入服务！'
          break
      }
      return str
    },
    customValidate(rule, value, callback) {
      if (rule.required) {
        if (value && value.length > 0) {
          callback()
        } else {
          callback(this.getTips(rule.fullField))
        }
      } else {
        callback()
      }
    }
  }
}
</script>