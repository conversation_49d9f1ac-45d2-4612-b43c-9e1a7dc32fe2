<template>
    <a-table
        :columns="columns"
        :data-source="data"
        ref="table"
        size="middle"
        bordered
        rowKey="id"
    >
    </a-table>
</template>
<script>
export default {
  // 关联变更
  name: 'table5',
  data() {
    return {
      columns: [
        {
          title: '序号',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function(t, r, index) {
            return parseInt(index) + 1
          }
        },
        {
          title: '变更编号',
          align: 'center',
          dataIndex: ''
        },
        {
          title: '变更标题',
          align: 'center',
          dataIndex: ''
        },
        {
          title: '变更类型',
          align: 'center',
          dataIndex: ''
        },
        {
          title: '变更状态',
          align: 'center',
          dataIndex: ''
        }
      ],
      data: []
    }
  }
}
</script>
<style scoped></style>
