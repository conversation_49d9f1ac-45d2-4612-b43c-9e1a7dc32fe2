<template>
  <div class="main">
    <div class="min-right-bg">
      <!-- <img src="/oneClickHelp/right-bg.png" alt="" /> -->
    </div>
    <div class="form-div">
      <div class="form-title">
        <img :src="logo" alt="" />
        {{logoTitle}}
      </div>
      <div class="form-sub-title">
        <div class="line"></div>
        <div class="text">运维助手</div>
        <div class="line"></div>
      </div>
      <a-form v-if="authorizedOrNot === true" :form="form" class="user-layout-login" ref="formLogin" id="formLogin">
        <a-form-item class="formBox">
          <a-input
            size="large"
            v-decorator="['username', validatorRules.username, { validator: this.handleUsernameOrEmail }]"
            type="text"
            autocomplete="off"
            disableautocomplete
            placeholder="请输入您的登录账号"
          >
          </a-input>
        </a-form-item>
        <a-form-item class="formBox">
          <a-input
            v-decorator="['password', validatorRules.password]"
            size="large"
            type="password"
            autocomplete="off"
            disableautocomplete
            placeholder="请输入您的登录密码"
          >
          </a-input>
        </a-form-item>
        <a-row :gutter="0">
          <a-col :span="24">
            <a-form-item style="position: relative" class="formBox">
              <a-input
                v-decorator="['inputCode', validatorRules.inputCode]"
                size="large"
                type="text"
                @change="inputCodeChange"
                placeholder="请输入验证码"
                autoComplete="off"
              >
              </a-input>
            </a-form-item>
            <div class="codeStyle">
              <img
                v-if="requestCodeSuccess"
                style="margin-top: 2px"
                :src="randCodeImage"
                @click="handleChangeCheckCode"
              />
              <img v-else style="width: 100%" src="../../assets/checkcode.png" @click="handleChangeCheckCode" />
            </div>
          </a-col>
        </a-row>
        <a-form-item style="text-align: center" class="formBtn">
          <div class="login-btn-div">
            <a-button
              size="large"
              type="primary"
              htmlType="submit"
              class="login-button"
              :loading="loginLoading"
              @click.stop.prevent="certificate"
              :disabled="loginDisabled"
              >登&nbsp;&nbsp;录
            </a-button>
            <div class="register-btn" @click="goRegister">注&nbsp;册</div>
          </div>
        </a-form-item>
      </a-form>
    </div>
    <div v-if="authorizedOrNot === false" class="authorization">
      <div class="authorization-upload">
        <a-upload name="file" :multiple="true" :action="uploadUrl" @change="handleChange">
          <a> 未授权或者授权过期（点击此处上传授权证书）11111111 </a>
        </a-upload>
      </div>
      <div class="authorization-button">
        <a-button
          size="large"
          type="primary"
          htmlType="submit"
          class="shouquan-button"
          :disabled="fileList.length != 1"
          @click.stop.prevent="authorizationButton"
          >授&nbsp;&nbsp;权
        </a-button>
      </div>
    </div>
    <activation-modal ref="activation" :hostName="hostName" isLogin @ok="loginSuccess"></activation-modal>
    <user-password ref="userPassword" @close='passwordClose'></user-password>
  </div>
</template>

<script>
//import md5 from "md5"
import api from '@/api'
import { mapActions } from 'vuex'
import { timeFix,getHostNameLocal } from '@/utils/util'
import Vue from 'vue'
import { ACCESS_TOKEN,USER_INFO, PLATFORM_TYPE, ONE_PLATFORM_FLAG, ENCRYPTED_STRING } from '@/store/mutation-types'
import { getAction } from '@/api/manage'
import { phoneValidator } from '@/mixins/phoneValidator'

import ActivationModal from './modules/ActivationModal.vue'
import UserPassword from '@/components/oneClickHelp/UserPassword'
export default {
  components: {ActivationModal,UserPassword},
  mixins: [phoneValidator],
  data() {
    return {
      customActiveKey: 'tab1',
      loginLoading: false, //登录加载中
      loginDisabled: false, //登录按钮是否可操作
      // login type: 0 email, 1 username, 2 telephone
      loginType: 0,
      requiredTwoStepCaptcha: false,
      stepCaptchaVisible: false,
      form: this.$form.createForm(this),
      encryptedString: {
        key: '',
        iv: '',
      },
      state: {
        time: 60,
        smsSendBtn: false,
      },
      validatorRules: {
        username: {
          rules: [{ required: true, message: '请输入登录账号!' }, { validator: this.handleUsernameOrEmail }],
        },
        password: { rules: [{ required: true, message: '请输入登录密码!', validator: 'click' }] },
        mobile: { rules: [{ validator: this.mobilePhone }] },
        captcha: { rule: [{ required: true, message: '请输入验证码!' }] },
        inputCode: { rules: [{ required: true, message: '请输入验证码!' }] },
      },
      fileList: [],
      key: '',
      verifiedCode: '',
      inputCodeContent: '',
      inputCodeNull: true,
      currentUsername: '',
      currdatetime: '',
      randCodeImage: '',
      requestCodeSuccess: false,
      authorizedOrNot: true,
      authorization: 'authorization-text',
      uploadUrl: `${window._CONFIG['domianURL']}/license/importLicense`,
      UKey: window.config.UKey,
      logo: window.config.oneClickHelp.helpLogoUrl,
      logoTitle: window.config.oneClickHelp.platformTitle,
      hostName:"",
    }
  },
  created() {
    //this.currdatetime = new Date().getTime()
    Vue.ls.remove(ACCESS_TOKEN)
    Vue.ls.remove("ONE_CLICK_HELP_INFO");
    this.getRouterData()
    this.handleChangeCheckCode()
   this.hostName = getHostNameLocal()
  },
  methods: {
    ...mapActions([ 'OneClickHelpLogin']),

    handleChange(info) {
      if (info.file.status !== 'uploading') {
        if (info.fileList.length > 1) {
          info.fileList.shift()
        }
      }
      if (info.file.response) {
        if (info.file.response.status === 200) {
          if (info.file.status === 'done') {
            this.$message.success(`${info.file.name} 文件上传成功`)
          } else if (info.file.status === 'removed') {
            this.$message.success(`${info.file.name} 文件删除成功!`)
          }
        } else {
          this.$message.error(`${info.file.name} 文件上传失败`)
          info.fileList.shift()
        }
      }
      this.fileList = info.fileList
    },

    authorizationButton() {
      getAction('license/installLicense').then((res) => {
        if (res.status == 200) {
          this.$message.success(res.message, '请登录')
          this.authorizedOrNot = true
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // handler
    handleUsernameOrEmail(rule, value, callback) {
      const regex = /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+((\.[a-zA-Z0-9_-]{2,3}){1,2})$/
      if (regex.test(value)) {
        this.loginType = 0
      } else {
        this.loginType = 1
      }
      callback()
    },
    handleTabClick(key) {
      this.customActiveKey = key
    },
    /*登录*/
    certificate() {
      getAction('license/licenseTest').then((res) => {
        if (res.code == 200) {
          this.handleSubmit()
        } else {
          this.$error({
            title: '提示',
            content: '授权已失效,请联系管理员!',
          })
        }
      })
    },
    /*登录后，向后端提交登录信息*/
    handleSubmit() {
      let that = this
      let loginParams = {}
      that.loginLoading = true
      that.loginDisabled = true
      if(this.hostName === "" || this.hostName === null){
        this.$message.error("没有终端信息！")
        return;
      }
      // 使用账户密码登录
       that.form.validateFields(
          ['username', 'password', 'inputCode', 'rememberMe'],
          { force: true },
          (err, values) => {
            if (!err) {
              loginParams.hostName = this.hostName
              loginParams.username = values.username
              // update-begin- --- author:scott ------ date:20190805 ---- for:密码加密逻辑暂时注释掉，有点问题
              //loginParams.password = md5(values.password)
              //loginParams.password = encryption(values.password,that.encryptedString.key,that.encryptedString.iv)
              loginParams.password = values.password
              loginParams.remember_me = values.rememberMe
              // update-begin- --- author:scott ------ date:20190805 ---- for:密码加密逻辑暂时注释掉，有点问题
              loginParams.captcha = that.inputCodeContent
              loginParams.checkKey = that.currdatetime
              // todo 设置验证码失效提醒，具体时间根据后端randomImage接口内设置的redis有效时间有关
              if (new Date().getTime() - that.currdatetime > 60 * 1000) {
                that.requestFailed({ message: '验证码超时，请刷新后重试' })
                that.loginLoading = false
                that.loginDisabled = false
                this.handleChangeCheckCode()
                return
              }
              that
                .OneClickHelpLogin(loginParams)
                .then((res) => {
                  // console.log("登录成功 === ",res)
                  if(res.success && res.result &&  res.result.token){
                    let terminalInfo = res.result.terminalInfo;
                    if(terminalInfo === this.hostName ){
                      this.loginSuccess()
                    }else{
                      this.$refs.activation.title = "绑定终端设备"
                      this.$refs.activation.add()
                    }
                  }else{
                    if(res.result.multi_depart === 3){
                      this.updatePassword()
                      return
                    }
                    this.$message.error("登录失败！")
                  }
                })
                .catch((err) => {
                  that.requestFailed(err)
                })
              that.loginLoading = false
              that.loginDisabled = true
            } else {
              that.loginLoading = false
              that.loginDisabled = false
            }
          }
        )

    },
    handleChangeCheckCode() {
      this.currdatetime = new Date().getTime()
      if (this.form.getFieldValue('inputCode') != null || this.form.getFieldValue('inputCode') != '') {
        this.form.setFieldsValue({
          inputCode: '',
        })
      }
      getAction(`/sys/randomImage/${this.currdatetime}`)
        .then((res) => {
          if (res.success) {
            this.randCodeImage = res.result
            this.requestCodeSuccess = true
          } else {
            this.$message.error(res.message)
            this.requestCodeSuccess = false
          }
        })
        .catch(() => {
          this.requestCodeSuccess = false
        })
    },
    async loginSuccess() {
      var userPlatforms = []
      let infoData = {
        hostName : this.hostName,
        username : this.form.getFieldValue("username"),
      }
      this.$ls.set("ONE_CLICK_HELP_INFO",infoData)
      let userInfo = this.$store.getters.userInfo;
      userInfo.terminalInfo = this.hostName
      Vue.ls.set(USER_INFO, userInfo, 7 * 24 * 60 * 60 * 1000)
      this.$router.push({ path: '/oneClickHelp/index' }).catch(() => {})
      this.$notification.success({
        message: '欢迎',
        description: `${timeFix()}，欢迎回来`,
      })
      // await getAction('/sys/permission/getUserPlatformTypeByToken')
      //   .then((res) => {
      //     if (res.success) {
      //       if (res.result == '') {
      //         this.loginLoading = false
      //         this.loginDisabled = false
      //         alert('当前用户没有菜单权限，请联系管理员分配权限')
      //       } else {
      //         userPlatforms = [...res.result.split(',')]
      //         Vue.ls.set(ONE_PLATFORM_FLAG, false)

      //       }
      //     } else {
      //       this.loginLoading = false
      //       this.loginDisabled = false
      //       alert(err.message)
      //     }
      //   })
      //   .catch((err) => {
      //     this.loginLoading = false
      //     this.loginDisabled = false
      //     alert(err.message)
      //   })
    },
    ...mapActions(['GetPermissionList']),

    requestFailed(err) {
      this.$notification['error']({
        message: '登录失败',
        description: ((err.response || {}).data || {}).message || err.message || '请求出现错误，请稍后再试',
        duration: 4,
      })
      this.loginLoading = false
      this.loginDisabled = false
    },
    inputCodeChange(e) {
      this.inputCodeContent = e.target.value
    },
    getRouterData() {
      this.$nextTick(() => {
        if (this.$route.params.username) {
          this.form.setFieldsValue({
            username: this.$route.params.username,
          })
        }
        if (this.$route.query.authorizedOrNot) {
          getAction('license/licenseTest').then((res) => {
            if (res.status == 200 || res.code == 200) {
              //this.handleSubmit()
            } else {
              this.$message.error(res.message)
              this.authorizedOrNot = false
            }
          })
        }
      })
    },
    goRegister() {
      this.$router.push({ path: '/oneClickHelp/register'})
    },
    updatePassword() {
      let userInfo = this.$store.getters.userInfo
      let username = userInfo.username
      this.$refs.userPassword.oneClickHelp = true
      this.$refs.userPassword.show(username)
    },
    passwordClose(){
      window.location.reload()
    }
  },
}
</script>

<style lang="less" scoped>

.main {
  background-color: #020a28;
}
.min-right-bg {
  position: absolute;
  right: 0px;
  top: 0px;
  height: 100%;
  width: 60%;
  background-image:url(/oneClickHelp/right-bg.png);
  background-position: center;
  background-size: auto 100%;
  background-repeat: no-repeat;
  background-color: #020a28;
  img {
    width: 100%;
    height: 100%;
  }
}
.form-div {
  min-width: 552px;
  position: absolute;
  height: 100%;
  left: 8%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}
.form-title {
  font-size: 44px;
  font-weight: 400;
  color: #ffffff;
  opacity: 0.85;
  // margin-bottom: 30px;
  display: flex;
  align-items: center;
  img {
    width: 58px;
    height: 58px;
    margin-right: 10px;
  }
}
.form-sub-title {
  display: flex;
  align-items: center;
  margin-top: 20px;
  margin-bottom: 80px;
  .line {
    width: 92px;
    height: 1px;
    background: #86c8ff;
  }
  .text {
    font-size: 31px;
    font-weight: normal;
    color: #86c8ff;
    letter-spacing: 5px;
    margin: 0px 10px;
    font-style: italic;
  }
}
.ukey-tip {
  color: #fff;
  font-size: 18px;
  text-align: center;
  width: 100%;
  // font-style: italic;
}
.user-layout-login {
  width: 420px;
  label {
    font-size: 14px;
  }

  .forge-password {
    font-size: 14px;
  }
  .login-btn-div {
    position: relative;
  }
  button.login-button {
    padding: 0 15px;
    font-size: 20px;
    height: 60px;
    width: 214px;
    // background-image: url(../../assets/login.png);
    // background-size: 100% 100%;
    // background-repeat: no-repeat;
    background: linear-gradient(0deg, #0576c5, #55d0fe);
    border: 0;
    border-radius: 50px;
  }

  .user-login-other {
    text-align: left;
    margin-top: 24px;
    line-height: 22px;

    .item-icon {
      font-size: 24px;
      color: rgba(0, 0, 0, 0.2);
      margin-left: 16px;
      vertical-align: middle;
      cursor: pointer;
      transition: color 0.3s;

      &:hover {
        color: #1e3674;
      }
    }

    .register {
      float: right;
    }
  }
}
.authorization {
  color: #fff;
  .authorization-upload {
    width: 468px;
    height: 248px;
    background-image: url(../../assets/05.png);
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    letter-spacing: 2px;
    a {
      color: #fff;
    }
    a:hover {
      color: skyblue;
    }
  }
  .authorization-button {
    width: 100%;
    display: flex;
    justify-content: center;
    margin-top: 40px;

    .shouquan-button {
      padding: 0 15px;
      font-size: 20px;
      height: 60px;
      width: 46%;
      background-image: url(../../assets/login.png);
      background-repeat: no-repeat;
      background-color: transparent;
      border: 0;
      border-radius: 50px;
    }
  }
}
/deep/ .ant-upload-list-item-name {
  color: #fff !important;
}
/deep/ .anticon-paper-clip {
  color: #fff !important;
}
.register-btn {
  color: #fff;
  text-align: left;
  cursor: pointer;
  position: absolute;
  right: 0px;
  bottom: 0px;
  font-size: 18px;
  letter-spacing: 3px;
  opacity: 0.7;
}
.register-btn:hover {
  opacity: 1;
}
</style>




<style scoped>
.valid-error .ant-select-selection__placeholder {
  color: #f5222d;
}
.codeStyle {
  position: absolute;
  top: 17px;
  right: 25px;
  border-left: 1px solid rgb(170, 170, 170);
  padding-left: 10px;
  width: 25%;
}
#username,
#password,
#inputCode {
  width: 100%;
  height: 64px;
  background-color: transparent;
  color: #fff;
  outline: none;
  border-inline: none;
  border: 1px solid #b2b4be;
  border-radius: 45px;
  text-align: center;
  margin-bottom: 4px;
  font-size: 24px;
  padding-top: 0;
}
#inputCode {
  padding: 0 104px 0 93px;
}
/*chrome浏览器input自动填充颜色设为透明  设置字体颜色*/
input:-webkit-autofill {
  -webkit-text-fill-color: #ffffff !important;
  transition: background-color 5000s ease-in-out 0s;
}
input:-internal-autofill-selected {
  background-color: transparent !important;
  color: rgb(0, 0, 0) !important;
  border: 1px solid #b2b4be;
  border-radius: 45px;
}
input::-webkit-input-placeholder {
  /* WebKit browsers */
  color: rgba(255, 255, 255, 0.45);
  font-size: 18px;
}
input:-moz-placeholder {
  /* Mozilla Firefox 4 to 18 */
  color: rgba(255, 255, 255, 0.45);
  font-size: 18px;
}
input::-moz-placeholder {
  /* Mozilla Firefox 19+ */
  color: rgba(255, 255, 255, 0.45);
  font-size: 18px;
}
input:-ms-input-placeholder {
  /* Internet Explorer 10+ */
  color: rgba(255, 255, 255, 0.45);
  font-size: 18px;
}
.code {
  font-size: 24px;
  color: #aaa;
  cursor: pointer;
  padding-bottom: 6px;
  line-height: 30px;
  text-align: center;
  user-select: none;
}
.formBtn {
  margin-top: 30px;
}
.has-error .ant-form-explain,
.has-error .ant-form-split {
  text-align: center;
}
.formBox {
  text-align: center;
}
@media (min-width: 1440px) and (max-width: 1620px) {
  #username,
  #password,
  #inputCode {
    height: 62px !important;
  }
  .formBtn {
    margin-top: 15px;
  }
}
@media (max-width: 1380px) {
  #username,
  #password,
  #inputCode {
    width: 90% !important;
    height: 57px !important;
  }
  .formBox {
    text-align: center;
  }
  .formBtn {
    margin-top: 15px;
  }
  .ant-form-item {
    margin-bottom: 15px !important;
  }
  button.login-button {
    font-size: 20px !important;
    height: 50px !important;
    width: 37% !important;
    background-size: 100% !important;
  }
  .codeStyle {
    position: absolute;
    top: 12px;
    right: 35px;
    border-left: 1px solid rgb(170, 170, 170);
    padding-left: 10px;
    width: 25%;
  }
}
</style>
