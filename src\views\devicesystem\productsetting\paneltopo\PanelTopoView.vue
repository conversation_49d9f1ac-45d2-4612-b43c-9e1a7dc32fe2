<template>
  <div class="panel-view">
    <panel-topo
      ref="panelTopo"
      operate="show"
      v-model="panelType"
      toolbar
      :productId="productId"
      :deviceInfo="deviceInfo"
      portList
    ></panel-topo>
  </div>
</template>

<script>
import PanelTopo from './PanelTopo'

export default {
  name: 'PanelTopoShow',
  components: {
    PanelTopo,
  },
  props: {
    productId: {
      type: String,
      default: '',
      required: true,
    },
    deviceInfo: {
      type: Object,
      default: () => null,
      required: false,
    },
  },
  data() {
    return {
      panelType: '0',
      listVisible:false,
    }
  },
  watch: {},
  created() {},
  mounted() {},
  methods: {
   
  },
}
</script>

<style scoped lang="less">
.panel-view {
  width: 100%;
  height: 100%;
}
</style>