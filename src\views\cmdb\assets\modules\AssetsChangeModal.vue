<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    :centered="true"
    :destroyOnClose="true"
    switchFullscreen
    @ok="handleOk"
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }"
    @cancel="handleCancel"
    cancelText="关闭"
  >
    <assets-status ref="realForm" @ok="submitCallback" :disabled="disableSubmit"></assets-status>
  </j-modal>
</template>

<script>
import AssetsStatus from './AssetsStatus'
import AFormItem from 'ant-design-vue/es/form/FormItem'
import { httpAction, getAction } from '@/api/manage'
import ARow from 'ant-design-vue/es/grid/Row'

export default {
  name: 'AssetsChangeModal',
  components: {
    ARow,
    AFormItem,
    AssetsStatus,
  },
  data() {
    return {
      title: '',
      width: 800,
      visible: false,
      disableSubmit: false,
    }
  },
  computed: {},
  methods: {
    add() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.add()
      })
    },
    edit(record, categoryId) {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.batchChange(record, categoryId)
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      this.$nextTick(() => {
        this.$refs.realForm.submitForm()
      })
    },
    submitCallback() {
      this.$emit('ok')
      this.visible = false
    },
    handleCancel() {
      this.close()
    },
  },
}
</script>
<style lang="less" scoped>
@import '~@assets/less/normalModal.less';
.p-info-title {
  font-family: PingFangSC-Medium;
  font-size: 18px;
  color: #000000;
}

.span-status {
  div {
    margin: 0;
    margin-top: 10px;
  }
}

.span-status div {
  margin: 0;
}

.div-info {
  margin-top: 12px;
}
</style>
