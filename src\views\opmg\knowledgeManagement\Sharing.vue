<template>
  <div :key='this.shareId' style='background-color: #f0f2f5;'>
    <div v-if='pageLoading' class='loading-knowledge-wrapper'>
      <a-spin :spinning='pageLoading' tip="加载中..." class='loading-spin'></a-spin>
    </div>
    <!--    知识详情页-->
    <div v-else-if='!pageLoading&&isShowKnowledge' class='knowledge-wrapper'>
      <!--      pc端知识详情-->
      <div v-if='device==="desktop"' class='desktop-knowledge-wrapper'>
        <a-row class='row-wrapper'>
          <a-col :span='24'>
            <!--          <div class='header-wrapper card-wrapper'>
                        <div class='title'>
                          <div style='display: inline-block;width:calc(100% - 30px)'>
                            <p class='p-info-title'>
                              <span class='span-title'>{{ kInfo.title }}</span>
                              <span class='span-status' title='能见度'>
            &lt;!&ndash;                  <a-icon :theme='"filled"' :type='kInfo.isPrivate==="1"?"lock":"unlock"' :style='{color:kInfo.isPrivate==="1"?"#FE9400":"#4BD863"}'></a-icon>&ndash;&gt;
                              <a-icon :type='kInfo.isPrivate==="1"?"lock":"global"' :style='{color:kInfo.isPrivate==="1"?"#FE9400":"#4BD863"}'></a-icon>
                              {{kInfo.isPrivate==="1"?visibility[1].label:visibility[0].label}}
                            </span>
                              <span class='span-status' title='浏览量'>
                              <a-icon :theme='"filled"' type='eye'  style='color:#4BD863'></a-icon>
                             {{kInfo.pageViewCount}}
                            </span>
                            </p>
                          </div>
                        </div>
                        <a-row :gutter='24' class='info-btn'>
                          <a-col :span='24'>
                            <p class='p-info-product'>
                              <span class='span-assets'>主题：{{ kInfo.topicName }}</span>
                              <span class='span-assets'>创建人员：{{ kInfo.createBy }}</span>
                              <span class='span-assets'>创建时间：{{ kInfo.createTime }}</span>
                              <span class='span-assets'>更新时间：{{ kInfo.updateTime }}</span>
                            </p>
                          </a-col>
                        </a-row>
                      </div>-->
            <knowledge-base-info-header
              :approval-view='false'
              :col-status='colStatus'
              :k-info='kInfo'
              :show-share='true'
            >
            </knowledge-base-info-header>
          </a-col>
          <a-col :span='24'>
            <!--          <div class='body-wrapper card-wrapper'>
                        <div class='body-info'>
                          <div class='plan-info'>
                            &lt;!&ndash;富文本、模板&ndash;&gt;
                            <div id='printContent' class='plan' v-html='kInfo.plan'></div>
                            &lt;!&ndash; 附件&ndash;&gt;
                            <attachment-preview :k-info='kInfo' :can-download='canDownload' :kkfileview-url='kkfileviewUrl'></attachment-preview>
                          </div>
                        </div>
                      </div>-->
            <knowledge-base-info-body
              :approvalView='false'
              :canDownload='canDownload'
              :k-info='kInfo'
              :kkfileview-url='kkfileviewUrl'
              :like-status='likeStatus'
              :show-opmg-k-info='true'
              :show-share='true'
              :unlike-status='unlikeStatus'>
            </knowledge-base-info-body>
          </a-col>
        </a-row>
      </div>
      <!--      移动端知识详情-->
      <div v-else-if='device==="mobile"' class='mobile-knowledge-wrapper'>
        <a-row class='row-wrapper'>
          <a-col :span='24'>
            <knowledge-base-info-header
              :approval-view='false'
              :col-status='colStatus'
              :k-info='kInfo'
              :show-share='true'
            >
            </knowledge-base-info-header>
          </a-col>
          <a-col :span='24'>
            <mobile-knowledge-base-info-body
              :canDownload='canDownload'
              :k-info='kInfo'
            >
            </mobile-knowledge-base-info-body>
          </a-col>
        </a-row>
      </div>
    </div>
    <!--    登录-->
    <div v-else class='share-login-wrapper'>
      <!--    pc登录-->
      <div v-if='device==="desktop"' class='desktop-share-wrapper'>
        <a-row class='login-wrapper'>
          <a-col :span="24" class="left-wrapper">
            <div class="main">
              <div class='top-wrapper'>
                <div class="top-line"></div>
                <div class="top-content">
                  <div class='icon-bg-wrapper'>
                    <div class='icon-bg'>
                      <a-icon v-if='!avatar' class='no-has-icon' type='user'></a-icon>
                      <img v-else class='has-icon' :src='avatar' alt />
                    </div>
                  </div>
                  <div class="user-info-wrapper">
                    <div class='user-info'>{{ userName }}</div>
                    <div class="login-name">
                      {{ title }}知识分享
                    </div>
                  </div>
                </div>
              </div>

              <a-form-model ref="form">
                <a-form-model-item class="formBox">
                  <a-input
                    id="code"
                    v-model="password"
                    autocomplete="off"
                    disableautocomplete
                    placeholder="请输入提取码"
                    size="large"
                    type="text"
                  />
                </a-form-model-item>
              </a-form-model>
              <a-button
                :disabled="disabled"
                :loading="loading"
                class="confirm-button"
                htmlType="submit"
                size="large"
                type="primary"
                @click.stop.prevent="getKnowledgeInfo"
              >确&nbsp;&nbsp;认
              </a-button>
            </div>
          </a-col>
<!--          <a-col :span="14" class="right-wrapper">-->
<!--            <div class="right-pic"></div>-->
<!--          </a-col>-->
          <a-col :span="24" class="bottom-wrapper">
            <div v-html="loginSysName"></div>
          </a-col>
        </a-row>
      </div>

      <!--    移动登录-->
      <div v-else-if='device==="mobile"' class='mobile-share-wrapper'>
        <div class='top-wrapper'>
          <div class='icon-bg-wrapper'>
            <div class='icon-bg'>
              <a-icon v-if='!avatar' class='no-has-icon' type='user'></a-icon>
              <img v-else class='has-icon' :src='avatar' alt />
            </div>
          </div>
          <div class='user-info'>{{ userName }}</div>
        </div>
        <div class='bottom-wrapper'>
          <div class='bottom-input-wrapper'>
            <yq-icon class='bottom-icon' type='mobileKnowledgeShare'></yq-icon>
            <a-input class='bottom-input' placeholder="请输入提取码" v-model='password'></a-input>
          </div>
          <div class='bottom-confirm-wrapper' @click.stop.prevent="getKnowledgeInfo">
            <span><a-icon v-if='loading' type='loading' style='margin-right: 0.3rem' />确&emsp;认</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import YqIcon from '@comp/tools/SvgIcon'
import { getAction, getFileAccessHttpUrl } from '@api/manage'
import { visibility } from '@views/opmg/knowledgeManagement/knowledgeBase/modules/dataListAndFunc'
import attachmentPreview from '@views/opmg/knowledgeManagement/knowledgeBase/modules/AttachmentPreview.vue'
import { knowledgeInfoMixins } from '@views/opmg/knowledgeManagement/knowledgeBase/modules/knowledgeInfoMixins'
import knowledgeBaseInfoHeader from '@views/opmg/knowledgeManagement/knowledgeBase/modules/KnowledgeBaseInfoHeader.vue'
import knowledgeBaseInfoBody from '@views/opmg/knowledgeManagement/knowledgeBase/modules/KnowledgeBaseInfoBody.vue'
import mobileKnowledgeBaseInfoBody
  from '@views/opmg/knowledgeManagement/knowledgeBase/modules/MobileKnowledgeBaseInfoBody.vue'
import { mixinDevice } from '@/utils/mixin.js'

export default {
  name: 'KnowledgeSharing',
  mixins: [knowledgeInfoMixins, mixinDevice],
  components: {
    YqIcon,
    attachmentPreview,
    knowledgeBaseInfoHeader,
    knowledgeBaseInfoBody,
    mobileKnowledgeBaseInfoBody
  },
  data() {
    return {
      visibility: visibility,
      logoShow: window._CONFIG['logoShow'] === 0 ? false : true,
      title: window._CONFIG['loginName'],
      desc: window._CONFIG['loginDesc'],
      loginUrl: process.env.NODE_ENV === 'production' ? window._CONFIG['loginUrl'] : require('@assets/logo2.png'),
      loginNameUrl: process.env.NODE_ENV === 'production' ? window._CONFIG['loginNameUrl'] : require(
        '@assets/logoName.png'),
      loginBottomUrl: process.env.NODE_ENV === 'production' ? window._CONFIG['loginBottomUrl'] : require(
        '@assets/logoBottom.png'),
      loginSysName: window._CONFIG['loginSysName'],

      isShowKnowledge: false,
      pageLoading: false,
      loading: false,
      disabled: false,
      // canDownload: false,
      password: '',
      shareId: '',
      avatar: '',
      userName: '',
      // kInfo: {},
      url: {
        getKInfo: '/kbase/knowledges/share/check/',//通过分享id和提取码获取知识信息
        userInfo: '/kbase/knowledges/share/avatar'
      }
    }
  },
  computed: {},
  watch: {
    '$route.query.shareId': {
      handler(val) {
        if (val.length > 0) {
          this.getUserInfo(val)
          this.initShareKnowledgeInfo()
        }
      }
    }
    /*  'Vue.ls.get(ACCESS_TOKEN)':{
        handler(val){
          console.log('ACCESS_TOKEN===',val)
        }
      }*/
  },
  mounted() {
    let that = this
    if (that.$route.query && that.$route.query.shareId) {
      that.getUserInfo(that.$route.query.shareId)
      that.initShareKnowledgeInfo()
    }
  },
  methods: {
    getUserInfo(shareId) {
      if (!this.isShowKnowledge) {
        getAction(this.url.userInfo, { id: shareId }).then(res => {
          if (res.success) {
            this.avatar = res.result.avatarOfShareBy ? getFileAccessHttpUrl(res.result.avatarOfShareBy) : ''
            this.userName = res.result.shareBy ? res.result.shareBy : ''
          }
        })
      }
    },
    initShareKnowledgeInfo() {
      if (this.kkfileviewUrl === '' && this.device === 'desktop') {
        this.getKkfileviewURL().then((res) => {
          this.kkfileviewUrl = res
          this.refreshKnowledgeInfo()
        })
      } else {
        this.refreshKnowledgeInfo()
      }
    },
    getKnowledgeInfo() {
      let that = this
      if (!that.loading) {
        that.loading = true
        that.disabled = true
        getAction(that.url.getKInfo + that.shareId, { password: that.password }).then((res) => {
          if (res.success) {
            that.initShareKInfo(res.result)
            sessionStorage.setItem('knowledgeSharing_' + that.shareId, that.password)
            that.isShowKnowledge = true
          } else {
            that.$message.warning(res.message)
            that.isShowKnowledge = false
          }
          that.loading = false
          that.disabled = false
          that.pageLoading = false
        }).catch((err) => {
          that.$message.warning(err.message)
          that.loading = false
          that.disabled = false
          that.pageLoading = false
          that.isShowKnowledge = false
        })
      }
    },
    refreshKnowledgeInfo() {
      let that = this
      that.password = ''
      that.shareId = that.$route.query.shareId
      let pw = sessionStorage.getItem('knowledgeSharing_' + that.shareId)
      if (pw && pw.length > 0) {
        that.password = pw
        that.pageLoading = true
        that.isShowKnowledge = true
        that.getKnowledgeInfo()
      } else {
        that.pageLoading = false
        that.isShowKnowledge = false
      }
    }
    /*    /!*导出*!/
        downloadFile(obj) {
          if (this.canDownload) {
            downloadFile(obj.filePath,obj.fileName)
          } else {
            this.$message.info('作者设置文件不允许下载')
          }
        },*/
  }
}
</script>

<style lang='less' scoped>
.loading-knowledge-wrapper {
  height: 100vh;
  background-color: #f0f2f5;
  color: rgba(0, 0, 0, 0.65);
  overflow: hidden;
  overflow-y: auto;

  .loading-spin {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-flow: column nowrap;
    width: 100%;
    height: 100%;
    text-align: center;
    ::v-deep .ant-spin-dot-spin{
      display: inline-block;
      margin-bottom: 12px;
    }
  }
}

.knowledge-wrapper {
  height: 100vh;
  color: rgba(0, 0, 0, 0.65);

  .desktop-knowledge-wrapper {
    height: 100%;
    background-color: #f0f2f5;
    overflow: hidden;
    overflow-y: auto;

    @media (min-width: 1600px) {
      .row-wrapper {
        width: 70%;
        margin: 16px auto;
        padding: 0px;
      }
    }
    @media (min-width: 1200px) and (max-width: 1600px) {
      .row-wrapper {
        width: 75%;
        margin: 16px auto;
        padding: 0px;
      }
    }
    @media (min-width: 992px) and (max-width: 1200px) {
      .row-wrapper {
        width: 80%;
        margin: 16px auto;
        padding: 0px;
      }
    }
    @media (max-width: 992px) {
      .row-wrapper {
        width: 100%;
        padding: 16px;
        margin: 0px;
      }
    }
  }

  .mobile-knowledge-wrapper {
    height: 100%;
    background-color: #f0f2f5;
    overflow: hidden;
    overflow-y: auto;

    .row-wrapper {
      margin: 0.1875rem auto; //15px/80px
      padding: 0px;
    }
  }
}

.share-login-wrapper {
  height: 100vh;

  .desktop-share-wrapper {
    overflow: hidden;
    position: relative;
    width: 100%;
    height: 100%;
    background: url("~@/assets/knowledge/bg.png") no-repeat center center;
    background-size: 100% 100%;

    .login-wrapper {
      overflow: auto;
      height: 100%;
      display: flex;
      flex-flow: column nowrap;
      justify-content: space-between;

      .left-wrapper {
        flex: 1;
        display: flex;
        min-height: 10rem;//700px
        .main {
          margin-top: 2rem;
          margin-left: 1.425rem; // 114px;
          .top-wrapper {
            margin-bottom: 1.825rem; // 146px

            .top-line {
              width:40px;// 40px;
              height:12px;// 12px;
              background-color: #306EF3;
              margin-bottom:1.125rem;// 90px
            }

            .top-content {
              display: flex;
              justify-content: start;
              align-items: center;

              .icon-bg-wrapper {
                margin-right:0.425rem;// 34px;

                .icon-bg {
                  width:0.975rem;// 78px;
                  line-height:0.975rem;// 78px;
                  height:0.975rem;// 78px;
                  border-radius: 50%;
                  border: 1px solid rgba(255, 0, 0, 0);
                  overflow: hidden;

                  .has-icon{
                    height: 100%;
                    width: 100%;
                  }
                }
              }

              .user-info-wrapper {
                .user-info {
                  font-size:0.5rem;// 40px;
                  color: #000
                }

                .login-name {
                  font-size:0.275rem;// 22px;
                  color: #9A9A9A;
                  letter-spacing: 1px
                }
              }
            }
          }

          .formBox {
            text-align: center;
            margin-bottom: 0.575rem !important; // 46px !important;
            width: 5.7rem; // 456px/80px;
            #code {
              width: 100%;
              height: 1rem; // 80px;
              background: #FFFFFF;
              border: 1px solid rgba(0, 0, 0, 0.15);
              border-radius: 0.5rem; // 40px;
              font-size: 0.275rem !important; // 22px;
              text-align: center;
              color:#9A9A9A !important
            }

            #code:hover {
              color: #000 !important;
            }
          }

          button.confirm-button {
            margin-bottom:1rem;// 80px;
            width: 5.7rem; // 456px/80px;
            font-size: 0.4rem; // 32px;
            height: 1rem; // 80px;
            background-image: linear-gradient(180deg, #00A7FF 0%, #3677FF 100%);
            box-shadow: 0 0.0625rem 0.125rem 0.025rem rgba(137, 169, 213, 0.50); //0 5px 10px 2px
            border-radius: 0.5rem; // 40px;
            text-align: center;
            border: none !important;
            color: #FFFFFF;
            letter-spacing: 0.46px;
            font-weight: 500;
          }
        }
      }

      //.right-wrapper {
      //  height: calc(100% - 0.5rem);
      //  text-align: right;
      //  padding:0.75rem 0.25rem 1.25rem 0.25rem; //60px 100px 100px 0;
      //  .right-pic {
      //    height: 100%;
      //    background: url("~@/assets/knowledge/rightPic.png") no-repeat 40% 0;
      //    background-size: contain;
      //  }
      //}

      .bottom-wrapper {
        //display: flex;
        //justify-content: end;
        //align-items: center;
        //flex-flow: row nowrap;
        height: 0.5rem;
        //align-self: flex-end;
        padding-bottom: 0.2rem; // 16px;
        width: 100%;
        text-align: center;
        font-size: 0.175rem; // 14px;
        color: #9A9A9A;
        letter-spacing: 0.77px;
      }
    }


    ::v-deep input:-webkit-autofill {
      border-radius: 0.6rem; // 48px;
      -webkit-text-fill-color: #ffffff !important;
      border: 0.5px solid rgba(0, 0, 0, 0.45);
      transition: background-color 5000s ease-in-out 0s;
    }

    ::v-deep input:-internal-autofill-selected {
      background-color: #FFFFFF !important;
      color: rgb(0, 0, 0) !important;
      border: 0.5px solid rgba(0, 0, 0, 0.45);
      border-radius: 0.2rem; // 40px;
    }

    ::v-deep input::-webkit-input-placeholder {
      font-size:0.275rem;
      color: #9A9A9A;
      letter-spacing: 0.31px;
    }

    ::v-deep input:-moz-placeholder {
      font-size: 0.275rem; // 22px;
      color: #9A9A9A;
      letter-spacing: 0.31px;
    }

    ::v-deep input::-moz-placeholder {
      font-size: 0.275rem; // 22px;
      color: #9A9A9A;
      letter-spacing: 0.31px;
    }

    ::v-deep input:-ms-input-placeholder {
      font-size: 0.275rem; // 22px;
      color: #9A9A9A;
      letter-spacing: 0.31px;
    }

    ::v-deep .has-error .ant-form-explain,
    ::v-deep .has-error .ant-form-split {
      text-align: center;
    }
  }
  .desktop-share-wrapper::after{
    position: absolute;
    content:'';
    right: 0.25rem;
    top: 0.75rem;
    width: 60%;
    height: 77.6%;
    min-width: 600px;
    min-height: 400px;
   // max-width: 1152px;
   // max-height: 838px;
    max-width: 1600px;
    max-height: 1300px;
    background: url("~@/assets/knowledge/rightPic.png") no-repeat 40% 0;
    background-size: contain;
  }


  .mobile-share-wrapper {
    height: 100vh;
    background: #fff;

    .top-wrapper {
      height: 50vh;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-flow: column nowrap;

      .icon-bg-wrapper {
        height: 2.875rem;
        width: 2.875rem;
        background: #F8FCFF;
        border-radius: 1.4375rem;
        padding: 0.3rem;

        .icon-bg {
          width: 100%;
          height: 100%;
          border-radius: 50%;
          box-shadow: 0 0 0.1rem 0 rgba(26, 87, 135, 0.19);
          //background:rgba(26,87,135,0.19) ;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 1.5rem;
          color: #CCCCCC;

          .has-icon {
            background-color: rgba(26, 87, 135, 0.05);
            width: 100%;
            height: 100%;
            border-radius: 50%;
          }
        }
      }

      .user-info {
        font-size: 0.45rem;
        color: rgba(0, 0, 0, 0.85);
        letter-spacing: 0.005rem;
        font-weight: 400;
      }
    }

    .bottom-wrapper {
      height: 50%;
      display: flex;
      flex-flow: column nowrap;
      justify-content: start;
      align-items: center;

      .bottom-input-wrapper {
        width: calc(100% - 0.875rem - 0.875rem);
        background: #EBEEF0;
        margin: 0 0.875rem;
        height: 1.2rem;
        border-radius: 0.6rem;
        padding: 0.3rem 0.575rem;
        display: flex;
        flex-flow: row nowrap;
        justify-content: space-between;
        align-items: center;

        .bottom-icon {
          font-size: 0.375rem;
          color: #CCCCCC
        }

        ::v-deep .ant-input, ::v-deep .ant-input:focus, ::v-deep .ant-input:active, .bottom-input, .bottom-input:focus, .bottom-input:active {
          margin-left: 0.575rem;
          flex: 1;
          background: rgba(255, 255, 255, 0);
          border: none !important;
          font-size: 0.35rem;
          color: #9A9A9A !important;
          letter-spacing: 0.05rem;
          box-shadow: none !important;
        }

        ::v-deep.ant-input::-moz-placeholder, ::v-deep.ant-input:-ms-input-placeholder, ::v-deep.ant-input::-webkit-input-placeholder {
          color: #9A9A9A;
        }
      }

      .bottom-confirm-wrapper {
        margin: 0.7rem 2.875rem 0;
        padding: 0.3rem 1rem 0.3rem 1rem;
        height: 1.15rem;
        background-image: linear-gradient(180deg, #00A7FF 0%, #3677FF 100%);
        box-shadow: 0 0.0625rem 0.125rem 0.025rem rgba(137, 169, 213, 0.50);
        border-radius: 0.575rem;
        font-size: 0.4rem;
        color: #FFFFFF;
        font-weight: 500;
        text-align: center;
        white-space: nowrap;
      }
    }
  }
}
</style>