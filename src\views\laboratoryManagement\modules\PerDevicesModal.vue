<template>
  <j-modal
    ref='perModal'
    title="预约"
    :width="width"
    :visible="visible"
    :destroyOnClose="true"
    switchFullscreen
    :centered='true'
    @ok="handleOk"
    @cancel="hide"
    cancelText="关闭">
    <div style='height: 75vh;overflow-y: auto;overflow-x: hidden'>
<device-reserve-list></device-reserve-list>
    </div>
  </j-modal>
</template>
<script>
import DeviceReserveList from '../deviceReserveManagement/DeviceReserveList.vue'

export default {
  name: 'PerDevicesModal',
  components: { DeviceReserveList },
  data(){
    return {
      title: '',
      width: '75vw',
      visible: false,
    }
  },
  created() {
  },
  mounted() {

  },
  methods: {
      show(data){
        this.visible = true
      },
    handleOk() {
      this.hide()
    },
    hide() {
      this.visible = false
    }
  },
}
</script>



<style scoped lang='less'>

</style>