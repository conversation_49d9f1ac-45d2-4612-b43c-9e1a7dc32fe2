<template>
  <div style="height:100%">
      <component :is="pageName" :data="data"/>
  </div>
</template>
<script>
  import OverviewOrderFromList from './OverviewOrderFromList'
  import OverviewOrderFromDetails from './modules/OverviewOrderFromDetails'
  export default {
    name: "OverviewOrderFromManage",
    data() {
      return {
        isActive: 0,
        data:{}
      };
    },
    components: {
      OverviewOrderFromList,
      OverviewOrderFromDetails
    },
    created(){
      this.pButton1(0);
    },
    //使用计算属性
    computed: {
      pageName() {
        switch (this.isActive) {
          case 0:
            return "OverviewOrderFromList";
            break;

          default:
            return "OverviewOrderFromDetails";
            break;
        }
      }
    },
    methods: {
      pButton1(index) {
        this.isActive = index;
      },
      pButton2(index,item) {
        this.isActive = index;
        this.data = item;
      }
    }
  }
</script>