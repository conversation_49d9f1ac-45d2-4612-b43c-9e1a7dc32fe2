<template>
  <div style="margin-bottom: 8px">
    <a-button-group >
      <a-button icon="arrow-left" @click="handleUndo">撤回</a-button>
      <a-button icon="arrow-right" @click="handleRedo">重做</a-button>
      <a-button icon="zoom-in" @click="handleZoom(0.2)">放大</a-button>
      <a-button icon="redo" @click="handleZoom(0)">还原</a-button>
      <a-button icon="zoom-out" @click="handleZoom(-0.2)">缩小</a-button>
      <a-button icon="upload" @click="importXml">导入</a-button>
      <a-upload action="" :before-upload="openBpmn" style="display: none">
        <a-button ref="importBtn" icon="folder-opened" />
      </a-upload>
      <a-button icon="download" @click="handleExportXmlAction">XML</a-button>
      <a-button icon="download" @click="handleExportSvgAction">SVG </a-button>
      <a-button icon="eye" @click="handlePreviewXml">预览</a-button>
      <a-button icon="delete" @click="handleClear">清空</a-button>
      <a-button icon="save" @click="handleSave" :loading="loading">保存 </a-button>
      <a-button icon="rollback" onClick="javascript :history.go(-1);">返回列表 </a-button>
    </a-button-group>
    <a-modal :visible.sync="xmlVisible" title="预览" :width="'90%'" centered @cancel="xmlVisible = false">
      <vue-ace-editor
        v-model="process.xml"
        @init="editorInit"
        lang="xml"
        theme="chrome"
        width="100%"
        height="calc(100vh - 214px)"
        :options="{ wrap: true, readOnly: true }"
      >
      </vue-ace-editor>
      <span slot="footer">
        <a-button icon="el-icon-document" type="primary" v-clipboard:copy="process.xml" v-clipboard:success="onCopy"
          >复 制</a-button
        >
        <a-button icon="el-icon-close" @click="xmlVisible = false">关闭</a-button>
      </span>
    </a-modal>
  </div>
</template>
<script>
import VueAceEditor from 'vue2-ace-editor'
import { putAction } from '@/api/manage'
export default {
  components: {
    VueAceEditor,
  },
  props:{
  },
  data() {
    return {
      loading:false,
      scale: 1,
      process: {
        xml: '',
        svg: '',
      },
      xmlVisible: false,
      idTest: /^[a-z_][\w-.]*$/i,
      modelData: {},
    }
  },
  created() {
    this.process = this.$parent.process
    this.modelData = this.$parent.modelData
  },
  mounted() {},
  methods: {
    editorInit: function () {
      require('brace/ext/language_tools')
      require('brace/mode/xml')
      require('brace/theme/chrome')
    },
    // 复制成功
    onCopy() {
      this.$message.success('内容复制成功')
    },
    // 撤回
    handleUndo() {
      if(this.$parent.commandIdx === -1){
        this.$message.warning("已经没有了！")
        return
      };
      this.$parent.commandAction="undo";
      this.$parent.bpmnModeler.get('commandStack').undo()
    },
    // 重做
    handleRedo() {
       if(this.$parent.commandIdx === this.$parent.commandLen-1){
         this.$message.warning("已经没有了！");
         return
       };
      this.$parent.commandAction="redo";
      this.$parent.bpmnModeler.get('commandStack').redo()
    },
    // 流程图放大缩小
    handleZoom(radio) {
      const newScale = !radio
        ? 1.0 // 不输入radio则还原
        : this.scale + radio <= 0.2 // 最小缩小倍数
        ? 0.2
        : this.scale + radio
      this.$parent.bpmnModeler.get('canvas').zoom(newScale)
      this.scale = newScale
    },
    // 导入
    importXml() {
      this.$refs.importBtn.$el.click()
    },
    // 导入文件选择完成
    openBpmn(file) {
      const reader = new FileReader()
      reader.readAsText(file, 'utf-8')
      reader.onload = () => {
        this.$parent.createNewDiagram(reader.result)
      }
      return false
    },
    // 导出XML文件
    handleExportXmlAction() {
      const _this = this
      this.$parent.bpmnModeler.saveXML({ format: true }, function (err, xml) {
        xml = _this.replaceLtAndGt(xml)
        _this.download('xml', xml, _this.getProcess().name + '.bpmn20.xml')
      })
    },
    // 导出SVG文件
    handleExportSvgAction() {
      const _this = this
      this.$parent.bpmnModeler.saveSVG({ format: true }, function (err, svg) {
        _this.download('svg', svg, _this.getProcess().name + '.bpmn20.svg')
      })
    },
    // 预览
    handlePreviewXml() {
      this.$parent.bpmnModeler.saveXML({ format: true }, (err, xml) => {
        this.process.xml = this.replaceLtAndGt(xml)
        this.xmlVisible = true
      })
    },
    // 清空
    handleClear() {
      this.$parent.createNewDiagram()
    },

    replaceLtAndGt(xml) {
      xml = xml.replace(/&lt;/g, '<')
      xml = xml.replace(/&gt;/g, '>')
      return xml
    },
    /**
     * 下载xml/svg
     *  @param  type  类型  svg / xml
     *  @param  data  数据
     *  @param  name  文件名称
     */
    download(type, data, name) {
      let dataTrack = ''
      const a = document.createElement('a')
      switch (type) {
        case 'xml':
          dataTrack = 'bpmn'
          break
        case 'svg':
          dataTrack = 'svg'
          break
        default:
          break
      }
      name = name || `diagram.${dataTrack}`
      a.setAttribute('href', `data:application/bpmn20-xml;charset=UTF-8,${encodeURIComponent(data)}`)
      a.setAttribute('target', '_blank')
      a.setAttribute('dataTrack', `diagram:download-${dataTrack}`)
      a.setAttribute('download', name)
      document.body.appendChild(a)
      a.click()
      URL.revokeObjectURL(a.href) // 释放URL 对象
      document.body.removeChild(a)
    },
    getProcessElement() {
      return this.$parent.bpmnModeler.getDefinitions().rootElements[0]
    },
    getProcess() {
      const element = this.getProcessElement()
      return {
        id: element.id,
        name: element.name,
        category: element.$parent.targetNamespace,
      }
    },
    handleSave() {
      const _this = this
      if (this.$parent.$refs._bpmnPanel) {
        let p = this.$parent.$refs._bpmnPanel.validate()
        if (p) {
          p.then(() => {
            const processId = this.getProcess().id
            if (!this.idTest.test(processId)) {
              this.$message.error('流程标识key格式不正确')
              return
            }
            _this.loading=true
            this.$parent.bpmnModeler.saveXML({ format: true }, (err, xml) => {
              xml = _this.replaceLtAndGt(xml)
              const process = _this.getProcess()
              _this.process.xml = xml
              _this.modelData.editor = xml
              _this.modelData.key = process.id
              _this.modelData.name = process.name
              _this.modelData.category = process.category
              // flowable/model/saveModelEditor
              let url = "flowable/model/saveModelEditor"
             if( _this.$parent.isDeployed){
               url = '/flowable/model/saveOrUpdateModelEditor'
             }
              putAction(url, _this.modelData).then((res) => {
                if(res.success){
                   _this.$parent.isDeployed = false;
                  this.$message.success('保存成功')
                }else {
                  this.$message.error(res.message)
                }
                _this.loading=false
              })

              // _this.$emit("save", _this.modelData);
            })
          }).catch((e) =>{
            this.$message.error(e)
            _this.loading=false
          })
        }else{
          this.$message.warning("保存失败")
          _this.loading=false
        }
      }
    },
  },
}
</script>