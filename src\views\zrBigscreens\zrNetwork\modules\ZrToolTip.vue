<template>
  <a-tooltip
    trigger='click'
    overlayClassName='panel-tip-card'
    :visible='visible'
    :placement='placement'
    :get-popup-container='getPopupContainer'
    >
    <template slot='title'>
      <div style='min-width: 300px' @mouseenter='enterTipCard' @mouseleave='leaveTipCard'>
        <div v-if="tipData && tipData.shape==='node'">
          <div class='node-title'>
            <!--          <img :src="nodeIcon" style="height: 18px;margin-right: 5px" />-->
            <span>{{ tipData.name }}</span>
          </div>
          <div style='margin-left:12px;margin-top: 8px'>
            <a-descriptions :column='1' size='small'>
              <a-descriptions-item label='运行状态' ><span :style='{color:curStatus.color}'>{{curStatus.label}}</span></a-descriptions-item>
              <a-descriptions-item label='网络联通'>正常</a-descriptions-item>
              <a-descriptions-item label='业务数量'>{{Math.floor(Math.random() * 101)}}</a-descriptions-item>
              <a-descriptions-item label='设备数量'>{{Math.floor(Math.random() * 200)}}</a-descriptions-item>
              <a-descriptions-item label='端口信息'>8091</a-descriptions-item>
              <a-descriptions-item label='端口流量'>{{(Math.random() * 1000).toFixed(2) + ' Mbps'}}</a-descriptions-item>
              <a-descriptions-item label='丢包率'>{{(Math.random() * 5).toFixed(2) + '%'}}</a-descriptions-item>
            </a-descriptions>
          </div>
        </div>
        <div v-else-if="tipData && tipData.shape==='edge'">
          <div v-if='tipData.server'>
            <div class='node-title'>
              <span>服务端：</span>
              <!--            <div class="rect-icon">
                          </div>-->
              <!--            <img :src="serverIcon" style="height: 18px;margin-right: 5px" />-->
              <span>{{  }}</span>
            </div>
            <a-descriptions :column='4' layout='vertical' size='small'>
              <a-descriptions-item label='状态1'>
              </a-descriptions-item>
              <a-descriptions-item label='状态2'></a-descriptions-item>
            </a-descriptions>
          </div>
          <!--        <div v-if="!tipData.client&&!tipData.server">暂无数据</div>-->
        </div>
      </div>

    </template>
    <span class='panel-tip-node'
          :style='`left:${left}px;top:${top}px;width:${width}px;height:${height}px;z-index:${tipZindex}`'
          @click='tipClick'> </span>
  </a-tooltip>
</template>

<script>
export default {
  name: 'ZrToolTip',
  props: {
    left: {
      type: Number,
      default: 0,
      require: false
    },
    top: {
      type: Number,
      default: 0,
      require: false
    },
    width: {
      type: Number,
      default: 32,
      require: false
    },
    height: {
      type: Number,
      default: 32,
      require: false
    },
    visible: {
      type: Boolean,
      default: false,
      require: false
    },
    tipZindex: {
      type: Number,
      default: -1,
      require: false
    },
    tipData: {
      type: Object,
      default: () => {
        return null
      },
      require: false
    },
    status: {
      type: Array,
      default: () => {
        return []
      },
      require: false
    }
  },
  data() {
    return {}
  },
  created() {

  },
  mounted() {
  },
  computed: {
    placement() {
      if (this.left < window.screen.width / 3) {
        if (this.top >= (window.innerHeight * 2) / 3) {
          return 'topLeft'
        } else if (this.top < window.innerHeight / 3) {
          return 'bottomLeft'
        } else {
          return 'right'
        }
      } else if (this.left > (window.screen.width * 2) / 3) {
        if (this.top >= (window.innerHeight * 2) / 3) {
          return 'topRight'
        } else if (this.top < window.innerHeight / 3) {
          return 'bottomRight'
        } else {
          return 'left'
        }
      } else {
        if (this.top >= window.innerHeight / 2) {
          return 'top'
        } else {
          return 'bottom'
        }
      }
    },
    curStatus() {
      return this.status.find(item => item.value === this.tipData.status) || {}
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.$nextTick(() => {
          this.$emit('enterTipCard')
        })
      } else {
        // this.$emit('hide')
      }
    }
  },
  methods: {
    tipClick() {
      this.$emit('cellClick')
    },
    getPopupContainer(trigger) {
      return trigger.parentElement
    },
    enterTipCard() {
      this.$emit('enterTipCard')
    },
    leaveTipCard() {
      this.$emit('leaveTipCard')
    }
  }
}
</script>
<style lang='less'>
.panel-tip-card {
  max-width: unset;
  .ant-tooltip-content{
    .ant-tooltip-inner{
      //background: rgba(0,14,40,0.8)
      background-image: url(/zrBigScreen/tipBg.png);
      background-color:transparent;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      padding: 24px 22px;
    }
  }
  .ant-descriptions-view {
    width: auto;

    table {
      width: auto
    }

    .ant-descriptions-item-label {
      color: #fff;
    }

    .ant-descriptions-item-content {
      color: #fff;
    }
  }

  .detail-btn {
    text-align: center;
    margin-bottom: 8px;
    cursor: pointer;
  }
}
</style>
<style lang='less' scoped>
.panel-tip-node {
  position: fixed;
  z-index: -1;
  display: block;
  width: 32px;
  height: 32px;
  background: transparent;

}
.node-title {
  font-size: 21px;
  height: 48px;
  width: 100%;
  padding-left: 32px;
  display: flex;
  align-items: center;
  color: #FFFFFF;
  font-weight: bold;
  background-image: url(/zrBigScreen/tipTitleBg.png);
  background-size: auto;
  background-repeat: no-repeat;
  .rect-icon {
    width: 8px;
    height: 8px;
    border-radius: 1px;
    background: @primary-color;
    margin-right: 8px;
  }
}
</style>