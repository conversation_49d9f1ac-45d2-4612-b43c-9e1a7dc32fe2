<template>
  <j-modal :title="title"
           :width="modalWidth"
           :visible="visible"
           switchFullscreen
           :centered="true"
           :destroyOnClose="true"
           :confirmLoading='confirmLoading'
           @cancel="handleCancel"
           @ok="handleOk"
  >
       <a-form :form="form">
      <a-row :gutter='24'>
        <a-col v-bind='formItemLayout'>
          <a-form-item class="two-words" :labelCol="labelCol" :wrapperCol="wrapperCol" label="地区">
            <yq-area-cascader-select :disabled="readOnly" placeholder="请选择归属地" v-decorator="['region', formValidator.region]"></yq-area-cascader-select>
          </a-form-item>
        </a-col>
        <a-col v-bind='formItemLayout'>
          <a-form-item class="two-words" :labelCol="labelCol" :wrapperCol="wrapperCol" label="单位">
            <a-input :allowClear="true" autocomplete="off" :disabled="disabledEdit||readOnly" placeholder="请输入" v-decorator="['company', formValidator.company]" />
          </a-form-item>
        </a-col>
        <a-col v-bind='formItemLayout'>
          <a-form-item class="two-words" :labelCol="labelCol" :wrapperCol="wrapperCol" label="日期">
            <a-month-picker style="width: 100%;" format="YYYY-MM" :disabled="disabledEdit||readOnly" placeholder="请选择年月" v-decorator="['time', formValidator.time]" />
          </a-form-item>
        </a-col>
        <a-col v-bind='formItemLayout'>
          <a-form-item class="two-words" :labelCol="labelCol" :wrapperCol="wrapperCol" label="类型">
            <!-- <j-dict-select-tag  v-model="assets" title="资产类型" dictCode="asset_type"/> -->
            <j-dict-select-tag type="list" v-decorator="['assets',validatorRules.assets]" :disabled="readOnly" :trigger-change="true" dictCode="asset_type" placeholder="请选择资产类型"/>
          </a-form-item>
        </a-col>
        <a-col v-bind='formItemLayout'>
          <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="采购数">
            <a-input :allowClear="true" autocomplete="off" :disabled="readOnly" placeholder="请输入采购数" v-decorator="['purchaseNum', formValidator.number]" />
          </a-form-item>
        </a-col>
        <a-col v-bind='formItemLayout'>
          <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="下发数">
            <a-input :allowClear="true" autocomplete="off" :disabled="readOnly" placeholder="请输入下发数" v-decorator="['downNum', formValidator.number]" />
          </a-form-item>
        </a-col>
        <a-col v-bind='formItemLayout'>
          <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="部署数">
            <a-input :allowClear="true" autocomplete="off" :disabled="readOnly" placeholder="请输入部署数" v-decorator="['deployNum', formValidator.number]" />
          </a-form-item>
        </a-col>
        <a-col v-bind='formItemLayout'>
          <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="上线数">
            <a-input :allowClear="true" autocomplete="off" :disabled="readOnly" placeholder="请输入上线数" v-decorator="['onlineNum', formValidator.number]" />
          </a-form-item>
        </a-col>
        <a-col v-bind='formItemLayout'>
          <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="替换数">
            <a-input :allowClear="true" autocomplete="off" :disabled="readOnly" placeholder="请输入替换数" v-decorator="['replaceNum', formValidator.number]" />
          </a-form-item>
        </a-col>
        <a-col v-bind='formItemLayout'>
          <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="暂存数">
            <a-input :allowClear="true" autocomplete="off" :disabled="readOnly" placeholder="请输入暂存数" v-decorator="['depositNum', formValidator.number]" />
          </a-form-item>
        </a-col>
        <a-col v-bind='formItemLayout'>
          <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="转移数">
            <a-input :allowClear="true" autocomplete="off" :disabled="readOnly" placeholder="请输入转移数" v-decorator="['transferNum', formValidator.number]" />
          </a-form-item>
        </a-col>
        <a-col v-bind='formItemLayout'>
          <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="销毁数">
            <a-input :allowClear="true" autocomplete="off" :disabled="readOnly" placeholder="请输入销毁数" v-decorator="['destructionNum', formValidator.number]" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <template slot="footer">
      <a-button v-if="readOnly" key="back" @click="handleCancel">关闭</a-button>
      <a-button v-else key="back" @click="handleCancel">取消</a-button>
      <a-button v-show="!readOnly" key="submit" type="primary" :loading="confirmLoading" @click="handleOk">
        确定
      </a-button>
    </template>
  </j-modal>
</template>
<script>
  import pick from 'lodash.pick'
  import moment from "moment"
  import { ledgerMonthAdd, ledgerMonthEdit } from '@api/AssetsManagement'
  import JDictSelectTag from '@/components/dict/JDictSelectTag.vue'
  import YqAreaCascaderSelect from '@/components/areaDict/YqAreaCascaderSelect'
export default {
  name: 'MonthlyMagazineEdit',
  props: ['assetType'],
   components: {
    JDictSelectTag,
    YqAreaCascaderSelect
  },
  data() {
    return {
      title: '操作',
      visible: false,
      confirmLoading: false,
      /* 弹框宽 */
      modalWidth: '800px',
      form: this.$form.createForm(this),
      formItemLayout: {
        md: { span: 12 },
        sm: { span: 24 }
      },
      labelCol: {
        //style: 'width:105px',
        xs: { span: 24 },
        sm: { span: 5 },
        md:{span: 8},
        lg:{span: 6},
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 18 },
        md:{span: 16},
        lg:{span: 16},
      },
      
      labelColOne: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperColone: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      //禁止编辑
      disabledEdit: false,
      //只读
      readOnly: false,
      // assets:'',
      validatorRules: {
        assets:{
          rules:[
            {required:true,message:'请选择巡检类型'},
          ]
        },
      },
      formValidator:{
        region: {
        	rules: [{
        			required: true,
        			message: '必填!'
        		},
        		{
        			min: 2,
        			max: 30,
        			message: '长度在 2 到 30 个字符',
        			trigger: 'blur'
        		}
        	]
        },
        company: {
        	rules: [{
        			required: true,
        			message: '必填!'
        		},
        		{
        			min: 2,
        			max: 30,
        			message: '长度在 2 到 30 个字符',
        			trigger: 'blur'
        		}
        	]
        },
        assets: {
        	rules: [{
        			required: true,
        			message: '必填!'
        		},
        		{
        			min: 2,
        			max: 30,
        			message: '长度在 2 到 30 个字符',
        			trigger: 'blur'
        		}
        	]
        },
        time: {
        	rules: [{
        			required: true,
        			message: '必选!'
        		}
        	]
        },
        number: {
        	rules: [{
        			required: true,
        			message: '必填!'
        		},
            {
            	pattern: /^(0|[1-9][0-9]*)$/,
              message:'请输入自然数'
            }
        	]
        },
      }
    }
  },
  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      this.form.resetFields();
      this.model = Object.assign({}, record)
      if(record.id){
        this.disabledEdit = true
      }else{
        this.disabledEdit = false
      }
      this.readOnly = false
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(pick(this.model,'region','company','assets','purchaseNum','downNum',
        'deployNum','onlineNum','replaceNum','depositNum','transferNum','destructionNum'))
        this.form.setFieldsValue({time: this.model.time ? moment(this.model.time) : null}) //时间格式化
      });

    },
    view(record) {
      this.form.resetFields();
      this.model = Object.assign({}, record)
      //只读
      this.readOnly = true
      this.disabledEdit = true
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(pick(this.model,'region','company','time','assets','purchaseNum','downNum',
        'deployNum','onlineNum','replaceNum','depositNum','transferNum','destructionNum'))
        this.form.setFieldsValue({time: this.model.time ? moment(this.model.time) : null}) //时间格式化
      });
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          // if('' == that.assets || null == that.assets){
          //   that.$message.warning("请选择类型");
          //   that.confirmLoading = false;
          //   return;
          // }
          that.confirmLoading = true
          let formData = Object.assign(this.model, values)
          // formData.assets = that.assets;
          delete formData.assetsName
          //时间格式化
          formData.time = formData.time ? formData.time.format('YYYY-MM') : null;
          let obj;
          if(!this.model.id){
            obj=ledgerMonthAdd(formData);
          }else{
            obj=ledgerMonthEdit(formData);
          }
          obj.then((res)=>{
            if(res.success){
              that.$message.success(res.message);
              if(res.result == null){
                that.$emit('ok',null);
              }else{
                that.$emit('ok',res.result);
              }
            }else{
              that.$message.warning(res.message);
            }
          }).finally(() => {
            that.confirmLoading = false;
            that.close();
          })
        }
      })
    },
    handleCancel() {
      this.close()
    }
  }
}
</script>
<style scoped lang="less">
@import '~@assets/less/normalModal.less';
</style>
