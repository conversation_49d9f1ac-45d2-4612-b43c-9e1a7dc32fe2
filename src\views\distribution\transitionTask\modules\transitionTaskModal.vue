<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    :destroyOnClose="true"
    :centered="true"
    switchFullscreen
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭"
    okText="保存">
    <transitionTaskForm
      ref="realForm"
      :sources="sources"
      :rules="rules"
      @ok="submitCallback"
      @closeForm='close'
      :disabled="disableSubmit"
    ></transitionTaskForm>
  </j-modal>
</template>

<script>
import transitionTaskForm from './transitionTaskForm'
export default {
  name: 'transitionTaskModal',
  components: {
    transitionTaskForm,
  },
  props: {
    rules:{
      type:Array,
      default:() => {
        return []
      }
    },
    sources:{
      type:Array,
      default:() => {
        return []
      }
    },
  },
  data() {
    return {
      title: '',
      width: '1000px',
      visible: false,
      disableSubmit: false,
    }
  },
  methods: {
    add() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.add()
      })
    },
    edit(record) {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.edit(record)
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
      //this.$refs.realForm.selectInspectionType()
    },
    handleOk() {
      this.$refs.realForm.submitForm()
    },
    submitCallback() {
      this.$emit('ok')
      this.visible = false
    },
    handleCancel() {
      //this.close()
      this.$nextTick(() => {
        this.$refs.realForm.closeForm()
      })
    },
  },
}
</script>
<style lang="less" scoped>
@import '~@assets/less/normalModal.less';
</style>