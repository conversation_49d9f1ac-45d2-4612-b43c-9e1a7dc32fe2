import { Graph, Addon, FunctionExt, Shape } from '@antv/x6'

export default class FlowGraph {
  // public static graph: Graph
  // private static stencil: Addon.Stencil

  static init (container) {
    // 创建画布
    this.graph = new Graph({
      container: document.getElementById(container),
      grid: true,
      // 画布调整
      snapline: true,
      history: true,
      autoResize: true,
      clipboard: {
        enabled: true
      },
      keyboard: {
        enabled: true
      },
      interacting:{
        nodeMovable:true
      },
      //配置节点的可移动区域
      // translating: {
      //   restrict: true,//节点移动时无法超过画布区域
      // },
      // 画布调整
      scroller: {
        enabled: false,
        pageVisible: true,
        pageBreak: false,//是否显示分页符, 显示时会产生分页白色虚线
        pannable: false
      },
      // interacting: false,
      selecting: {
        enabled: true,
        rubberband: true, // 启用框选
        movable: true,
        showNodeSelectionBox: true
      },
      connecting: {
        anchor: 'center',
        connectionPoint: 'anchor',
        allowBlank: false,
        highlight: true,
        snap: true,
        createEdge() {
          return new Shape.Edge({
            attrs: {
              line: {
                stroke: '#5F95FF',
                strokeWidth: 1,
                targetMarker: {
                  name: 'classic',
                  size: 8
                }
              }
            },
            router: {
              name: 'manhattan'
            },
          })
        },
        validateConnection({ sourceView, targetView, sourceMagnet, targetMagnet }) {
          if (sourceView === targetView) {
            return false
          }
          if (!sourceMagnet) {
            return false
          }
          if (!targetMagnet) {
            return false
          }
          return true
        }
      },
      embedding: {
        enabled: true,
        findParent ({ node }) {
          const bbox = node.getBBox()
          return this.getNodes().filter((node) => {
            // 只有 data.parent 为 true 的节点才是父节点
            const data = node.getData()
            if (data && data.parent) {
              const targetBBox = node.getBBox()
              return bbox.isIntersectWithRect(targetBBox)
            }
            return false
          })
        }
      },
      highlighting: {
        magnetAvailable: {
          name: 'stroke',
          args: {
            padding: 4,
            attrs: {
              strokeWidth: 4,
              stroke: 'rgba(223,234,255)'
            }
          }
        }
      },
    })
    return this.graph
  }

  // 销毁
  static destroy () {
    this.graph.dispose()
  }
}
