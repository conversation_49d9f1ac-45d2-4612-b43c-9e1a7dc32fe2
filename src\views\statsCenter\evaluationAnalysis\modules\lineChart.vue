<template>
  <card-frame :title="title" :titleBgPath="'/statsCenter/evaluate/title2.png'">
    <div slot="bodySlot" style="height: 100%;width: 100%;" id="dayCountLine"></div>
  </card-frame>
</template>

<script>
import echarts from 'echarts'
import cardFrame from '@views/statsCenter/com/cardFrame.vue'
import { getAction } from '@/api/manage'
export default {
  components: {
    cardFrame
  },
  data() {
    return {
      time1: '',
      time2: '',
      url: {
        dayCount: '/data-analysis/order/day/count'
      }
    }
  },
  props: {
    title: ''
  },
  mounted() {
    this.dayCount()
  },
  methods: {
    // 服务请求数据统计折线图数据
    dayCount() {
      getAction(this.url.dayCount, {
        time1: this.time1,
        time2: this.time2
      }).then(res => {
        if (res.code == 200) {
          this.dayCountLine(res.result)
        }
      })
    },

    // 服务请求数据统计折线图
    dayCountLine(data) {
      let xArr = []
      let yArr = []
      data.forEach(e => {
        xArr.push(e.name)
        yArr.push(e.value)
      })
      let myChart = this.$echarts.init(document.getElementById('dayCountLine'))
      myChart.setOption({
        tooltip: {
          show: true,
          trigger: 'axis',
          transitionDuration: 0 //echart防止tooltip的抖动
        },
        legend: {
          data: ['服务请求数量'],
          selectedMode:false,
          icon: 'rect',
          top: '10',
          right: '0',
          itemWidth: 12,
          itemHeight: 2,
          textStyle: {
            color: '#9FA5AD',
            fontSize: 14
          }
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: false,
            data: xArr,
            axisTick: {
              show: false //隐藏X轴刻度
            },
            axisLine: {
              lineStyle: {
                color: '#9FA5AD' // 更改x坐标轴颜色
              }
            },
            axisLabel: {
              show: true,
              showMaxLabel: false,
              align: 'left',
              textStyle: {
                color: '#9FA5AD', //更改坐标轴文字颜色
                padding: [5, 0, 0, -3]
              }
            }
          }
        ],
        yAxis: [
          {
            minInterval: 1,
            type: 'value',
            name: '数量(个)',
            nameTextStyle: {
              color: 'rgba(159, 165, 173, 0.7)',
              fontSize: 12,
              padding: [0, 0, 0, -20]
            },
            axisLabel: {
              show: true,
              textStyle: {
                fontSize: 12,
                color: 'rgba(159, 165, 173, 0.7)' //更改坐标轴文字颜色
              }
            },
            axisLine: {
              show: false
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: ['rgba(193, 201, 211, 0.2)'],
                width: 1,
                type: 'dashed'
              }
            }
          }
        ],
        grid: {
          top: 60,
          right: 5,
          bottom: 50,
          left: 40
        },
        series: [
          {
            name: '服务请求数量',
            type: 'line',
            areaStyle: {},
            emphasis: {
              focus: 'series'
            },
            symbol: 'circle', // 实心折点
            symbolSize: 7, // 折点大小
            data: yArr,
            itemStyle: {
              normal: {
                areaStyle: {
                  type: 'default',
                  color: new echarts.graphic.LinearGradient(
                    0,
                    0,
                    0,
                    1, //面积变化度
                    //两种种由深及浅的颜色
                    [
                      {
                        offset: 0,
                        color: 'rgba(6,117,226,0.44)'
                      },
                      {
                        offset: 1,
                        color: 'rgba(65,160,254,0)'
                      }
                    ]
                  )
                },
                color: '#0281FF' //改变折线点的颜色
              }
            },
            lineStyle: {
              color: {
                colorStops: [
                  {
                    offset: 0,
                    color: '#021B56' // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: '#45E4F3' // 100% 处的颜色
                  }
                ]
              } //改变折线颜色
            }
          }
        ]
      })
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    }
  }
}
</script>

<style scoped lang="less">
</style>