<template>
 <custom-page :page-type='pageType'></custom-page>
</template>

<script>

  import CustomPage from '@views/customPages/models/CustomPage.vue'

  export default {
    name: 'Statistics',
    components: {
      CustomPage
    },
    data(){
      return{
        pageType:"ywgzt",
      }
    },
    created() {

    },
    mounted() {
    },
    methods:{
      reset(){

      }
    }
  }
</script>

<style lang='less' scoped>
</style>