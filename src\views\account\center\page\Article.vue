<template>
  <a-list>
    <a-list-item>
      <a-card :bordered="false">
        <div class="account-center-avatarHolder"></div>
        <div class="account-center-detail">
          <p><i class="title"></i>{{ '用户名称:' + this.realname }}</p>
          <p><i class="title"></i>{{ '电话:' + this.phone }}</p>
          <p><i class="title"></i>{{ '职务:' + this.post }}</p>
          <p><i class="group"></i>{{ '邮箱:' + this.email }}</p>
          <p><i class="group"></i>{{ '生日:' + this.createTime }}</p>
          <a-radio-group>
            <a-radio v-if="this.sex == 1">男</a-radio>
            <a-radio v-if="this.sex == 2">女</a-radio>
          </a-radio-group>
        </div>
        <button>保存</button>

        <a-divider />
      </a-card>
    </a-list-item>
  </a-list>
</template>

<script>
import AList from 'ant-design-vue/es/list'
import AListItem from 'ant-design-vue/es/list/Item'

export default {
  name: 'Article',
  components: {
    AList,
    AListItem,
  },

  data() {
    return {
      tagInputVisible: false,
      tagInputValue: '',
      post: '',
      departName: '',
      email: '',
      username: '',
      phone: '',
      signature: '',
      sex: '',
      realname: '',
      createTime: '',

      noTitleKey: 'app',
      url: {
        getInfo: '/sys/user/getInfo', //个人信息
        updateUserInfo: '/sys/user/updateUserInfo', //编辑个人信息
      },
    }
  },
  mounted() {
    this.getInfo()
  },

  methods: {
    getInfo() {
      this.$http.get(this.url.getInfo).then((res) => {
        if (res.code == 200) {
          this.post = res.result.post
          this.departName = res.result.orgCode
          this.email = res.result.email
          this.username = res.result.username
          this.phone = res.result.phone
          this.createTime = res.result.birthday
          this.realname = res.result.realname
          this.sex = res.result.sex
        } else {
          this.$message.error(res.message)
        }
      })
    },
  },
}
</script>