<template>
  <div style="height:100%">
      <component :is="pageName" :data="data"/>
  </div>
</template>
<script>
  import DevopsSoftwareInfoList from './DevopsSoftwareInfoList'
  import DevopsSoftwareInfoDetails from './modules/DevopsSoftwareInfoDetails'
  export default {
    name: "DevopsSoftwareInfoManage",
    data() {
      return {
        isActive: 0,
        data:{}
      };
    },
    components: {
      DevopsSoftwareInfoList,
      DevopsSoftwareInfoDetails
    },
    created(){
      this.pButton1(0);
    },
    //使用计算属性
    computed: {
      pageName() {
        switch (this.isActive) {
          case 0:
            return "DevopsSoftwareInfoList";
            break;

          default:
            return "DevopsSoftwareInfoDetails";
            break;
        }
      }
    },
    methods: {
      pButton1(index) {
        this.isActive = index;
      },
      pButton2(index,item) {
        this.isActive = index;
        this.data = item;
      }
    }
  }
</script>