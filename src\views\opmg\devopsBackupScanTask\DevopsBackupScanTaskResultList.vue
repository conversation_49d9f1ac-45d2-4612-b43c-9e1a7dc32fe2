<template>
   <a-modal
    :title="title"
    :width="width"
    :visible="visible"
    :confirmLoading="confirmLoading"
    switchFullscreen
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @cancel="handleCancel"
    cancelText="关闭">
    <template slot="footer">
      &nbsp;
    </template>

      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        class="j-table-force-nowrap"
        @change="handleTableChange">

        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 14px;font-style: italic;">无图片</span>
          <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width:80px;font-size: 14px;font-style: italic;"/>
        </template>
      </a-table>
  </a-modal>
</template>

<script>

  import '@/assets/less/TableExpand.less'
  import { mixinDevice } from '@/utils/mixin'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'

  export default {
    name: 'DevopsBackupScanTaskResultList',
    mixins:[JeecgListMixin, mixinDevice],
    components: {},
    data () {
      return {
        description: '扫描任务结果管理页面',
        // 表头
        columns: [
          {
            title:'任务名称',
            align:"center",
            dataIndex: 'taskName'
          },
         /* {
            title:'备份类型',
            align:"center",
            dataIndex: 'conTypeText'
          },*/
          {
            title:'操作类型',
            align:"center",
            dataIndex: 'operateType_dictText'
          },
          {
            title:'备份策略/备份任务',
            align:"center",
            dataIndex: 'backupNameText'
          },
          {
            title:'备份时间',
            align:"center",
            dataIndex: 'backupTime'
          },
          {
            title:'扫描机器',
            align:"center",
            dataIndex: 'resourceIp'
          },
          {
            title:'扫描路径',
            align:"center",
            dataIndex: 'fileAdd'
          },
          {
            title:'状态',
            align:"center",
            dataIndex: 'state'
          }
        ],
        url: {
          list: "/devopsbackupscantaskresult/devopsBackupScanTaskResult/list",
        },
        title:'',
        width:1000,
        visible: false,
        confirmLoading: false,
        disableSubmit: false,
      }
    },
    computed: {
      importExcelUrl: function(){
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
      },
    },
    methods: {
      edit (record) {
        this.queryParam.scanTaskId = record;
        this.loadData(1);
        this.visible=true
      },
      initDictConfig(){
      },
      handleCancel () {
        this.close()
      },
      close() {
        this.$emit('close')
        this.visible = false
      }
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less';
</style>