<template>
  <card-frame :title='"设备告警"' :sub-title='""'>
    <div slot='bodySlot' class='empty-wrapper' v-if='listData.length===0'>
      <a-spin :spinning='loading' v-if='loading' class='spin'></a-spin>
      <a-list :data-source='[]' v-else />
    </div>
    <div slot='bodySlot' id='home-alarm-scroll' v-else>
      <seamless-carousel-list
        :list-data='listData'
        :column-width='columnWidth'
        :column-name='columnName'
        :overlayClassName='"oneClickHelpTooltip"'
        style='padding-top: 0.4rem'>
      </seamless-carousel-list>
    </div>
  </card-frame>
</template>
<script>
import { getAction } from '@api/manage'
import cardFrame from '@views/statsCenter/com/cardFrame.vue'
import seamlessCarouselList from '@views/statsCenter/com/seamlessCarouselList.vue'
export default {
  name: "alarmscroll",
  components:{cardFrame,seamlessCarouselList},
  props: {
    adcode: {
      type: String,
      required: false,
      default: ''
    },
    handleRefresh:{
      type: Number,
      required: false,
      default: 0,
    }
  },
  data(){
    return{
      loading: false,
      columnName: ["告警时间","设备名称","告警级别", "告警名称"],
      columnWidth:['25%','25%','20%','30%'],
      listData:[],
      url: {
        list: '/data-analysis/index/deviceAlarmRotate',
      },
    }
  },
  watch: {
    adcode: {
      handler(nVal, oVal) {
        this.alarmList(nVal)
      },
      deep:true,
      immediate:true
    },
    handleRefresh: {
      handler(nVal, oVal) {
        this.alarmList(this.adcode)
      }
    }
  },
  methods:{
    // 告警轮播数据
    alarmList(adCode) {
     this.loading = true
      this.listData=[]
      getAction(this.url.list,{adCode:adCode}).then((res) => {
        this.listData=[]
        if (res.success) {
          for (let i=0;i<res.result.length;i++){
            let item=res.result[i]
            let m=[item.alarmTime1,item.deviceName,item.alarmLevel,item.templateName]
            this.listData.push(m)
          }
        }else {
          this.$message.warning(res.message)
        }
        this.loading=false
      }).catch((err) => {
        this.$message.warning(err.message)
        this.loading = false
      })
    }
  }
}
</script>

<style lang='less' scoped>
#home-alarm-scroll{
  height: 100%;
}
</style>