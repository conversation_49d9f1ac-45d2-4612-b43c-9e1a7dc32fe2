<template>
  <j-modal title="重新设定密码" width="800px" :visible="visible" :centered='true' switchFullscreen
    :confirmLoading="confirmLoading" :destroyOnClose="true" @ok="handleSubmit" @cancel="handleCancel" cancelText="关闭">
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">

        <a-form-item label="用户账号" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input placeholder="请输入用户账号" v-decorator="[ 'username', {}]" :readOnly="true" />
        </a-form-item>

        <a-form-item label="登录密码" :labelCol="labelCol" :wrapperCol="wrapperCol" hasFeedback>
          <a-input type="password" placeholder="请输入登录密码" v-decorator="[ 'password', validatorRules.password]" />
        </a-form-item>

        <a-form-item label="确认密码" :labelCol="labelCol" :wrapperCol="wrapperCol" hasFeedback>
          <a-input type="password" @blur="handleConfirmBlur" placeholder="请重新输入登录密码"
            v-decorator="[ 'confirmpassword', validatorRules.confirmpassword]" />
        </a-form-item>

      </a-form>
    </a-spin>
  </j-modal>
</template>

<script>
  import {
    changePassword
  } from '@/api/api'
  import {
    getAction
  } from '@/api/manage'
  export default {
    name: "PasswordModal",
    data() {
      return {
        visible: false,
        confirmLoading: false,
        confirmDirty: false,
        validatorRules: {
          password: {
            rules: [{
              required: true,
              message: '请输入新密码!',
            }, {
              validator: this.validateToNextPassword,
            }],
          },
          confirmpassword: {
            rules: [{
              required: true,
              message: '请重新输入新密码!',
            }, {
              validator: this.compareToFirstPassword,
            }],
          },
        },

        model: {},

        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          },
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          },
        },
        form: this.$form.createForm(this),
        pwdRuleUrl: 'umpPwdManage/umpPwdManage/list',
        pwdRuleInfo: null
      }
    },
    created() {
      this.getPwdRuleData()
    },
    methods: {
      getPwdRuleData() {
        getAction(this.pwdRuleUrl).then(res => {
          if (res.success) {
            this.pwdRuleInfo = res.result.records[0]
            this.setValidator(this.pwdRuleInfo)
          }
        })
      },
      setValidator(info) {
        let capRegStr = ''
        let lowerRegStr = ''
        let numRegStr = ''
        let speRegStr = ''
        let cbStr = ''
        if (!!info.pwdMin) {
          cbStr = '密码至少由' + this.pwdRuleInfo.pwdMin + '位组成，包含'
        }
        if (info.capitalize) {
          capRegStr = "[A-Z]+"
          cbStr += "大写字母"
        }
        if (info.lowercase) {
          lowerRegStr = "[a-z]+"
          cbStr += cbStr.length > (4 + this.pwdRuleInfo.pwdMin.length) ? "、小写字母" : "小写字母"
        }
        if (info.hasNum) {
          numRegStr = "[0-9]+"
          cbStr += cbStr.length > (4 + this.pwdRuleInfo.pwdMin.length) ? "、数字" : "数字"
        }
        if (info.special) {
          speRegStr += "[`~!@#$^&*()=|{}':;',\\[\\].<>/?~！@#￥……&*（）——|{}【】‘；：”“'。，、？]+"
          cbStr += cbStr.length > (4 + this.pwdRuleInfo.pwdMin.length) ? "、特殊字符" : "特殊字符"
        }
        this.validatorRules.password.rules.push({
          validator: (rule, value, callback) => {
            let capRegEn = new RegExp(capRegStr)
            let lowerRegEn = new RegExp(lowerRegStr)
            let numRegEn = new RegExp(numRegStr)
            let speRegEn = new RegExp(speRegStr)
            if (value && (value.length < parseInt(this.pwdRuleInfo.pwdMin) || !capRegEn.test(value) || !lowerRegEn
                .test(value) || !numRegEn.test(value) || !speRegEn.test(value))) {
              callback(cbStr + "！");
            } else {
              callback()
            }
          }
        })
      },
      show(username) {
        this.form.resetFields();
        this.visible = true;
        this.model.username = username;
        this.$nextTick(() => {
          this.form.setFieldsValue({
            username: username
          });
        });
      },
      close() {
        this.$emit('close');
        this.visible = false;
        this.disableSubmit = false;
        this.selectedRole = [];
      },
      handleSubmit() {
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            this.confirmLoading = true;
            let formData = Object.assign(this.model, values);
            changePassword(formData).then((res) => {
              if (res.success) {
                this.$message.success(res.message);
                this.$emit('ok');
              } else {
                this.$message.warning(res.message);
              }
            }).finally(() => {
              this.confirmLoading = false;
              this.close();
            });
          }
        })
      },
      handleCancel() {
        this.close()
      },
      validateToNextPassword(rule, value, callback) {
        const form = this.form;
        const confirmpassword = form.getFieldValue('confirmpassword');
        if (value && confirmpassword && value !== confirmpassword) {
          callback('两次输入的密码不一样！');
        }
        if (value && this.confirmDirty) {
          form.validateFields(['confirm'], {
            force: true
          })
        }
        callback();
      },
      compareToFirstPassword(rule, value, callback) {
        const form = this.form;
        if (value && value !== form.getFieldValue('password')) {
          callback('两次输入的密码不一样！');
        } else {
          callback()
        }
      },
      handleConfirmBlur(e) {
        const value = e.target.value
        this.confirmDirty = this.confirmDirty || !!value
      }
    }
  }
</script>
<style scoped lang='less'>
  ::v-deep .ant-modal-body {
    padding: 24px 48px 24px 48px;
  }

  ::v-deep .ant-modal {
    padding: 24px;
  }

  @media (max-width: 812px) {
    ::v-deep .ant-modal {
      max-width: calc(100vw - 12px);
      margin: 0;
    }
  }

  .j-modal-box.fullscreen {
    margin: 0 !important;
    max-width: 100vw !important;

    ::v-deep .ant-modal {
      max-width: 100vw !important;
      margin: 0;
    }
  }
</style>