<!--
 * @Descripttion: 
 * @Author: kcz
 * @Date: 2021-05-02 16:04:02
 * @LastEditors: kcz
 * @LastEditTime: 2021-05-14 21:24:50
-->
<template>
  <a-form-item
    :class="{ 'evaluation-field-item': record.options.isEvaluationField }"
    :label-col="
      formConfig.layout === 'horizontal'
        ? formConfig.labelLayout === 'flex'
          ? { style: `width:${formConfig.labelWidth}px` }
          : formConfig.labelCol
        : {}
    "
    :wrapper-col="
      formConfig.layout === 'horizontal'
        ? formConfig.labelLayout === 'flex'
          ? { style: 'width:auto;flex:1' }
          : formConfig.wrapperCol
        : {}
    "
    :style="
      formConfig.layout === 'horizontal' && formConfig.labelLayout === 'flex'
        ? { display: 'flex' }
        : {}
    "
  >
  <span
      slot="label"
      v-if="record.options.showLabel === undefined || (record.options.showLabel && !record.options.hidden)"
      :class="{ 'evaluation-field-label': record.options.isEvaluationField }"
      :style="record.options.isEvaluationField ? 'color: #1890ff; font-weight: 600;' : ''"
    >
      <span v-if="record.options.isEvaluationField" class="evaluation-indicator">★</span>
      {{record.label}}
    </span>
    <component
      :record="record"
      :style="`width:${record.options.width}`"
      @change="handleChange"
      :disabled="disabled"
      :dynamicData="dynamicData"
      :label="record.label"
      :height="
        typeof record.options.height !== 'undefined'
          ? record.options.height
          : ''
      "
      v-decorator="[
        record.model,
        {
          initialValue: record.options.defaultValue,
          rules: record.rules
        }
      ]"
      :is="customComponent"
    ></component>
  </a-form-item>
</template>
<script>
export default {
  name: "customComponent",
  props: ["record", "formConfig", "disabled", "dynamicData"],
  computed: {
    customComponent() {
      // 计算需要显示的组件
      const customComponentList = {};
      if (window.$customComponentList) {
        // 将数组映射成json
        window.$customComponentList.forEach(item => {
          customComponentList[item.type] = item.component;
        });
      }
      return customComponentList[this.record.type];
    }
  },
  methods: {
    handleChange(value, key) {
      this.$emit("change", value, key);
    }
  }
};
</script>

<style lang="less" scoped>
/* 评估指标字段样式 */
.evaluation-field-item {
  position: relative;
  border-left: 3px solid #1890ff;
  padding-left: 8px;
  background: linear-gradient(90deg, rgba(24, 144, 255, 0.05) 0%, rgba(24, 144, 255, 0.01) 100%);
  border-radius: 4px;
  margin-bottom: 16px;
}

.evaluation-field-item::before {
  content: '';
  position: absolute;
  left: -3px;
  top: 0;
  bottom: 0;
  width: 3px;
  background: linear-gradient(180deg, #1890ff 0%, #40a9ff 100%);
  border-radius: 2px;
}

.evaluation-field-label {
  position: relative;
}

.evaluation-indicator {
  color: #faad14;
  font-size: 14px;
  margin-right: 4px;
  font-weight: bold;
  text-shadow: 0 0 2px rgba(250, 173, 20, 0.3);
}
</style>
