<template>
  <div style='height: 100%'>
    <a-row style='height: 100%; overflow: hidden; overflow-y: auto'>
      <a-col :span='24'>
        <knowledge-base-info-header
          :col-status='colStatus'
          :approval-view='approvalView'
          :k-info='kInfo'
          @handleCollection='handleCollection'
          @handleShare='handleShare'
          @getGo='getGo'
        ></knowledge-base-info-header>
      </a-col>
      <a-col :span='24'>
        <knowledge-base-info-body
          :k-info='kInfo'
          :show-opmg-k-info='true'
          :approvalView='approvalView'
          :show-share='false'
          :canDownload='canDownload'
          :unlike-status='unlikeStatus'
          :like-status='likeStatus'
          :kkfileview-url='kkfileviewUrl'>
        </knowledge-base-info-body>
      </a-col>
    </a-row>
    <add-share-modal ref='addShareModal'></add-share-modal>
  </div>
</template>

<script>
import addShareModal from '@views/opmg/knowledgeManagement/knowledgeBase/modules/AddShareModal.vue'
import { knowledgeInfoMixins } from '@views/opmg/knowledgeManagement/knowledgeBase/modules/knowledgeInfoMixins'
import knowledgeBaseInfoHeader from '@views/opmg/knowledgeManagement/knowledgeBase/modules/KnowledgeBaseInfoHeader.vue'
import knowledgeBaseInfoBody from '@views/opmg/knowledgeManagement/knowledgeBase/modules/KnowledgeBaseInfoBody.vue'
export default {
  name: 'KnowledgeBaseInfo',
  mixins: [knowledgeInfoMixins],
  props: {
    data: {
      type: Object,
      required:false,
      default:{},
    },
/*    fromtype: {
      type: Number,
      required:false,
      default: 0,
    },*/
    /**若是通过知识审批列表打卡查看，
     收藏、分享、打印、评论、关联、点赞、点踩都不可操作性，
     附件统统可以下载，同时告诉管理员，知识创建者设置的允许下载附件状态*/
    approvalView:{
      type:Boolean,
      required:false,
      default:false
    }
  },
  components:{
    knowledgeBaseInfoBody,
    addShareModal,
    knowledgeBaseInfoHeader
  },
  data() {
    return {
      url:{
        approvalViewKInfo:'/kbase/knowledges/queryByIdForReview',//知识审批页面查看知识详情：通过知识id获取知识信息
      }
    }
  },
  
  watch: {
    data: {
        handler() {
             if ( this.kkfileviewUrl==='') {
              this.getKkfileviewURL().then((res) => {
                this.kkfileviewUrl = res
                this.initKnowledge()
              })
            }else {
              this.initKnowledge()
            }
        },
        deep:true,
        immediate:true
      }
  },
  // activated() {
  //   if ( this.kkfileviewUrl===''){
  //     this.getKkfileviewURL().then((res)=>{
  //       this.kkfileviewUrl=res
  //       this.initKnowledge()
  //     })
  //   }else {
  //     this.initKnowledge()
  //   }
  // },

  methods: {
    initKnowledge(){
      let infoUrl=this.approvalView==true?this.url.approvalViewKInfo:this.url.kInfo
      if (this.approvalView==true){
        this.init(this.url.approvalViewKInfo)
      }else {
        this.init()
      }
    }
  }
}
</script>

<style lang='less' scoped>
</style>