<template>
  <a-card style="height: 100%">
    <a-row>
      <a-col :span="24">
        <span style="float: right; margin-bottom: 12px">
          <img src="~@/assets/return1.png" alt @click="getGo" style="width: 20px; height: 20px; cursor: pointer" />
        </span>
      </a-col>
      <a-col :span="24">
        <table class="gridtable">
          <tr>
            <td class="leftTd">巡检人</td>
            <td class="rightTd">{{ data.inspectUser }}</td>
            <td class="leftTd">巡检时间</td>
            <td class="rightTd">{{ data.inspectTime }}</td>
          </tr>
          <tr>
            <td class="leftTd">巡检地点</td>
            <td class="rightTd">{{ data.inspectPlace }}</td>
            <td class="leftTd">巡检文件</td>
            <td class="rightTd">{{ data.inspectFilePath }}</td>
          </tr>
          <tr>
            <td class="leftTd">巡检内容</td>
            <td class="rightTd" colspan="3">{{ data.inspectContent }}</td>
          </tr>
        </table>
      </a-col>
    </a-row>
  </a-card>
</template>

<script>
import OverviewOrderFromInfoModal from './OverviewOrderFromInfoModal'
import { httpAction, getAction } from '@/api/manage'
export default {
  name: 'OverviewOrderFromInfoModal',
  props: {
    data: {
      type: Object,
    },
  },
  components: {
    OverviewOrderFromInfoModal,
  },
  data() {
    return {
      form: this.$form.createForm(this),
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      url: {
        queryById: '/software/devopsSoftwareInfo/queryById',
      },
    }
  },
  methods: {
    //返回上一级
    getGo() {
      this.$parent.pButton2(0)
    },
  },
}
</script>
<style lang="less" scoped>
::v-deep .two-words > div > label {
  letter-spacing: 4px;
}
::v-deep .two-words > div > label::after {
  letter-spacing: 0px;
}
table.gridtable {
  font-family: verdana, arial, sans-serif;
  font-size: 14px;
  color: #606266;
  border-width: 1px;
  border-color: #e8e8e8;
  border-collapse: collapse;
  text-align: left;
  width: 100%;
}
table.gridtable td {
  border-width: 1px;
  border-style: solid;
  border-color: #e8e8e8;
}
.leftTd {
  width: 17%;
  background-color: #fafafa;
  padding: 16px 24px;
  text-align: center;
}
.rightTd {
  width: 35%;
  padding: 16px 24px;
  word-break: break-all
}
</style>