<template>
  <a-modal
    :title="title"
    :width="modalWidth"
    :visible="visible"
    :confirmLoading="confirmLoading"
    :destroyOnClose="true"
    @ok="handelSubmit"
    @cancel="handleCancel"
    cancelText="关闭"
    wrapClassName="ant-modal-cust-warp"
    style="top: 5%; height: 80%; overflow: auto"
  >
    <a-form ref="form" :model="form">
      <div v-if="form.status == 1 && nextNodeParam.handleType == 0" style="padding-top: 0px">
        <a-form-item label="标题" prop="assignees" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input placeholder="请输入" v-model="programmeForm.title" />
        </a-form-item>
        <a-form-item label="方案描述" prop="assignees" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-textarea v-model="programmeForm.describe" placeholder="请输入" :auto-size="{ minRows: 3, maxRows: 5 }" />
        </a-form-item>
      </div>
      <div v-if="form.status == 2 && nextNodeParam.handleType == 1">
        <!-- <a-row>
          <a-col :span="6">
            <a-button type="primary" @click="buttonClick()">
              提出变更
            </a-button>
          </a-col>
        </a-row> -->
        <a-form-item label="结果" prop="assignees" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-radio-group v-model="handleForm.handleResult">
            <a-radio :value="1"> 成功解决 </a-radio>
            <a-radio :value="2"> 未成功解决 </a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="标题" prop="assignees" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input placeholder="请输入" v-model="handleForm.title" />
        </a-form-item>
        <a-form-item label="处理描述" prop="assignees" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-textarea v-model="handleForm.describe" placeholder="请输入" :auto-size="{ minRows: 3, maxRows: 5 }" />
        </a-form-item>
        <a-form-item label="文件上传" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <j-upload v-model="form.file"></j-upload>
        </a-form-item>
      </div>

      <div v-if="form.status == 3">
        <a-form-item label="审核结果" prop="reason" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-radio-group name="radioGroup" :default-value="0" v-model="form.type" @change="onChange">
            <a-radio :value="0"> 通过 </a-radio>
            <a-radio :value="1"> 驳回 </a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="审批意见" prop="reason" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input type="textarea" v-model="form.comment" :rows="4" />
        </a-form-item>
        <a-form-item
          label="满意度"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          v-if="nextNodeParam.handleType == 1 && form.type == 0"
        >
          <a-rate v-model="rateValue" />
        </a-form-item>
        <a-form-item
          label="责任人"
          prop="assignees"
          v-show="showAssign"
          :error="error"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
        >
          <a-select v-model="form.assignedUser" placeholder="请选择" allowClear mode="default" :loading="userLoading">
            <a-select-option v-for="(item, i) in users" :key="i" :value="item.username">{{
              item.realname
            }}</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="文件上传" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <j-upload v-model="form.file"></j-upload>
        </a-form-item>

        <a-form-item label="下一审批人" v-show="isGateway" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <span>分支网关处暂不支持自定义选择下一审批人，将发送给下一节点所有人</span>
        </a-form-item>
        <div v-show="form.type == 1">
          <a-form-item label="驳回至" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-select v-model="form.backTaskKey" :loading="backLoading" @change="changeBackTask">
              <a-select-option v-for="(item, i) in backList" :key="i" :value="item.key">{{
                item.name
              }}</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item
            label="指定原节点审批人"
            prop="assignees"
            v-show="form.backTaskKey != -1"
            :error="error"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-select v-model="form.assignees" placeholder="请选择" allowClear mode="multiple" :loading="userLoading">
              <a-select-option v-for="(item, i) in assigneeList" :key="i" :value="item.username">{{
                item.realname
              }}</a-select-option>
            </a-select>
          </a-form-item>
        </div>
      </div>
      <div v-if="form.status == 4">
        <a-form-item label="审核结果" prop="reason" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-radio-group name="radioGroup" :default-value="0" v-model="form.type" @change="onChange">
            <a-radio :value="0"> 通过 </a-radio>
            <a-radio :value="1"> 驳回 </a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="评价意见" prop="reason" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input type="textarea" v-model="form.comment" :rows="4" />
        </a-form-item>
        <a-form-item
          label="满意度"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          v-if="nextNodeParam.handleType == 1 && form.type == 0"
        >
          <a-rate v-model="rateValue" />
        </a-form-item>
        <div v-show="form.type == 1">
          <a-form-item label="驳回至" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-select v-model="form.backTaskKey" :loading="backLoading" @change="changeBackTask">
              <a-select-option v-for="(item, i) in backList" :key="i" :value="item.key">{{
                item.name
              }}</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item
            label="指定原节点审批人"
            prop="assignees"
            v-show="form.backTaskKey != -1"
            :error="error"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-select v-model="form.assignees" placeholder="请选择" allowClear mode="multiple" :loading="userLoading">
              <a-select-option v-for="(item, i) in assigneeList" :key="i" :value="item.username">{{
                item.realname
              }}</a-select-option>
            </a-select>
          </a-form-item>
        </div>
      </div>
      <a-form-item label="消息通知" prop="assignees" :labelCol="labelCol" :wrapperCol="wrapperCol">
        <a-checkbox v-model="form.sendMessage">站内消息通知</a-checkbox>
        <!-- <a-checkbox v-model="form.sendSms" disabled>短信通知</a-checkbox>
        <a-checkbox v-model="form.sendEmail" disabled>邮件通知</a-checkbox> -->
      </a-form-item>
    </a-form>
    <div v-if="form.status == 1 && nextNodeParam.handleType == 0">
      <a-tabs :animated="false" default-active-key="1">
        <a-tab-pane key="1" tab="关联配置项" force-render>
          <!-- 查询区域 -->
          <div class="table-page-search-wrapper">
            <a-form layout="inline" @keyup.enter.native="searchQuery">
              <a-row :gutter="24">
                <a-col :xl="8" :md="12" :sm="24">
                  <a-form-item label="名称">
                    <a-input placeholder="请输入名称" v-model="queryParam.name"></a-input>
                  </a-form-item>
                </a-col>
                <a-col :xl="8" :md="12" :sm="24">
                  <a-form-item label="编号">
                    <a-input placeholder="请输入编号" v-model="queryParam.code"></a-input>
                  </a-form-item>
                </a-col>
                <a-col :xl="8" :md="12" :sm="24" style="padding-left: 24px;padding-bottom: 10px">
                  <a-button type="primary" @click="searchQuery">查询</a-button>
                  <a-button type="primary" @click="searchReset" style="margin-left: 15px">重置</a-button>
                </a-col>
              </a-row>
            </a-form>
          </div>
          <a-table
            ref="table"
            size="middle"
            bordered
            rowKey="id"
            :columns="columns"
            :dataSource="dataSource"
            :pagination="ipagination"
            :loading="loading"
            :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
            class="j-table-force-nowrap"
            @change="handleTableChange"
          >
          </a-table>
        </a-tab-pane>
      </a-tabs>
    </div>

    <div
      :style="{
        position: 'absolute',
        right: 0,
        bottom: 0,
        width: '100%',
        borderTop: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
        textAlign: 'right',
        zIndex: 1,
      }"
    >
      <a-button :style="{ marginRight: '8px' }" @click="onClose"> 取消 </a-button>
      <a-button type="primary" :disabled="submitLoading" @click="handelSubmit"> 提交 </a-button>
    </div>
  </a-modal>
</template>
<script>
import { queryDepartTreeList } from '@/api/api'
import JUpload from '@/components/jeecg/JUpload'
import JSelectUserByDep from '@/components/jeecgbiz/JSelectUserByDep'
import { getAction, deleteAction, putAction, postAction } from '@/api/manage'
import LSelectUserByDep from '@/components/jeecgbiz/LSelectUserByDep'
// import JSelectUserByDep from '@/components/jeecgbiz/JSelectUserByDep'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
export default {
  components: { JSelectUserByDep, JUpload, LSelectUserByDep },
  name: 'todoDispose',
  mixins: [JeecgListMixin],
  data() {
    return {
      submitLoading: false,
      confirmLoading: false,
      ipagination: {
        current: 1,
        pageSize: 5,
        pageSizeOptions: ['5', '10', '20'],
        showTotal: (total, range) => {
          return ' 共' + total + '条'
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0,
      },
      form: {
        id: '',
        userId: '',
        procInstId: '',
        comment: '',
        file: '',
        type: 0,
        assignees: [],
        backTaskKey: '',
        sendMessage: true,
        sendSms: false,
        sendEmail: false,
        status: 1,
        handleUser: '',
        assignedUser: '',
      },
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      rateValue: 0,
      handleForm: {
        handleResult: '',
        title: '',
        describe: '',
      },
      programmeForm: {
        title: '',
        describe: '',
      },
      nextNodeParam: {
        procDefId: '',
        currActId: '',
        users: [],
        status: '',
        handleType: '',
      },
      formValidate: {
        // 表单验证规则

        handleResult: [
          {
            required: true,
            message: '请输入名称!',
          },
        ],
      },
      backList: [
        {
          key: '-1',
          realname: '发起人',
        },
      ],
      error: '',
      showAssign: false,
      userLoading: false,
      backLoading: false,
      selectCount: 0, // 多选计数
      selectList: [], // 多选数据
      assigneeList: [],
      users: [],
      isGateway: false,
      assignedUser: '', //提出解决方案人员
      title: '问题处理',
      visible: false,
      autoExpandParent: true,
      modalWidth: '60%',
      checkedKeys: [],
      todoId: '',
      loading: false,
      owner: '', //问题指派人
      // 关联配置项
      columns: [
        {
          title: '序号',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function (t, r, index) {
            return parseInt(index) + 1
          },
        },
        {
          title: '编号',
          align: 'center',
          dataIndex: 'code',
        },
        {
          title: '资产名称',
          align: 'center',
          dataIndex: 'name',
        },
        {
          title: '单位名称',
          align: 'center',
          dataIndex: 'unitName',
        },
        {
          title: '项目名称',
          align: 'center',
          dataIndex: 'projectName',
        },
        {
          title: '合同名称',
          align: 'center',
          dataIndex: 'contractName',
        },
      ],
      dataSource: [],
      queryParam: {},
      url: {
        todoList: '/actTask/todoList',
        pass: '/busQuestion/pass',
        back: '/busQuestion/back',
        backToTask: '/busQuestion/backToTask',
        delegate: '/actTask/delegate',
        getNextNode: '/activiti_process/getNextNode',
        getNode: '/activiti_process/getNode/',
        getBackList: '/actTask/getBackList/',
        passAll: '/actTask/passAll/',
        backAll: '/actTask/backAll/',
        list: '/itilconfigitemlibrary/itilConfigItemLibrary/syList',
        getUsers: '/busQuestion/getUsers',
      },
    }
  },
  created() {
    // this.loadTree()
  },
  methods: {
    changeBackTask(v) {
      if (v == '-1') {
        return
      }
      this.userLoading = true
      getAction(this.url.getNode + v).then((res) => {
        this.userLoading = false
        if (res.success) {
          if (res.result.users && res.result.users.length > 0) {
            this.assigneeList = res.result.users
            // 默认勾选
            let ids = []
            res.result.users.forEach((e) => {
              ids.push(e.username)
            })
            this.form.assignees = ids
          }
        }
      })
    },

    afterVisibleChange(val) {},
    showDrawer() {
      this.visible = true
    },
    onClose() {
      this.visible = false
    },
    handelSubmit() {
      this.submitLoading = true
      var formData = Object.assign({}, this.form)
      //提出解决方案
      if (formData.status == 1) {
        formData.assignees = formData.handleUser
        formData.programmeForm = JSON.stringify(this.programmeForm)
      }
      //
      // if(formData.status == 2){
      //   formData.assignees = this.owner;
      //   formData.programmeForm = JSON.stringify(this.programmeForm);
      // }
      //审核处理方案并且同意
      if (formData.status == 3 && this.form.type == 0) {
        formData.assignees = this.owner
        formData.rateValue = this.rateValue
      }
      //审核处理方案并且驳回
      if (formData.status == 3 && this.form.type == 1) {
        formData.assignees = this.form.assignees.join(',')
        formData.rateValue = this.rateValue
      }
      //审核处理方案并且同意
      if (formData.status == 4 && this.form.type == 0) {
        formData.assignees = this.owner
        formData.rateValue = this.rateValue
      }
      //审核处理方案并且驳回
      if (formData.status == 4 && this.form.type == 1) {
        formData.assignees = this.form.assignees.join(',')
        formData.rateValue = this.rateValue
      }
      //处理问题
      if (formData.status == 2 && this.nextNodeParam.handleType == 1) {
        formData.assignees = this.assignedUser
        formData.handleForm = JSON.stringify(this.handleForm)
      }
      if (formData.type == 0) {
        this.adopt(formData)
      } else if (formData.type == 1) {
        this.reject(formData)
      }
    },
    //通过
    adopt(formData) {
      // 通过
      // if (this.showAssign && formData.assignees.length < 1) {
      //   this.$message.error("请至少选择一个审批人")
      //   this.submitLoading = false;
      //   return;
      // } else {
      //   this.error = "";
      // }

      formData.itilConfigIds = this.selectedRowKeys.join(',')
      postAction(this.url.pass, formData).then((res) => {
        this.submitLoading = false
        if (res.success) {
          this.$message.success('操作成功')
          this.visible = false
          this.$emit('ok')
        }
      })
    },
    // 驳回
    reject(formData) {
      // 驳回
      if (formData.backTaskKey == '-1') {
        // 驳回至发起人
        postAction(this.url.back, formData).then((res) => {
          this.submitLoading = false
          if (res.success) {
            this.$message.success('操作成功')
            this.visible = false
            this.$emit('ok')
          }
        })
      } else {
        // 自定义驳回
        if (formData.backTaskKey != '-1' && formData.assignees.length < 1) {
          this.$message.error('请至少选择一个审批人')
          this.submitLoading = false
          return
        } else {
          this.error = ''
        }
        postAction(this.url.backToTask, formData).then((res) => {
          this.submitLoading = false
          if (res.success) {
            this.$message.success('操作成功')
            this.visible = false
            this.$emit('ok')
          }
        })
      }
    },

    show(v) {
      this.queryParam.busId = v.busId
      this.queryParam.projectId = v.projectId
      this.processDefinition = v
      this.form.id = v.id
      this.form.procInstId = v.procInstId
      this.form.priority = v.priority
      this.form.type = 0
      this.form.assignedUser = ''
      this.rateValue = 0
      this.modalTaskVisible = true
      this.userLoading = true
      this.form.procDefId = v.procDefId
      this.visible = true
      this.form.status = v.status
      this.nextNodeParam.procDefId = v.procDefId
      this.nextNodeParam.currActId = v.key
      this.nextNodeParam.users = v.users
      this.nextNodeParam.status = v.status
      this.nextNodeParam.handleType = v.handleType
      this.owner = v.owner
      this.assignedUser = v.assignedUser
      this.form.file = ''
      this.getAssignees()
      this.loadData(null)

      //this.getAssignees();
    },
    //下级审批人
    getAssignees() {
      if (this.processDefinition.status == 3 && this.processDefinition.handleType == 0) {
        this.showAssign = true
        getAction(this.url.getUsers, null).then((res) => {
          this.userLoading = false
          if (res.success) {
            this.users = res.result
          }
        })
      }
    },
    //获取驳回节点
    backTask() {
      // 获取可驳回节点
      this.backList = []
      this.form.backTaskKey = this.processDefinition.assignedNodeId
      this.backLoading = true
      getAction(this.url.getBackList + this.processDefinition.procInstId).then((res) => {
        this.backLoading = false
        if (res.success) {
          res.result.forEach((e) => {
            if (e.key === this.processDefinition.assignedNodeId) {
              this.backList.push(e)
            }
          })
        }
      })
      this.changeBackTask(this.form.backTaskKey + this.processDefinition.busId)
    },
    onChange(e) {
      var value = e.target.value
      if (0 == value) {
        this.getAssignees()
      } else {
        this.showAssign = false
        this.backTask()
      }
    },
    setAssignees() {
      this.form.assignees = this.assignedUser
    },
    onExpand(expandedKeys) {
      // if not set autoExpandParent to false, if children expanded, parent can not collapse.
      // or, you can remove all expanded children keys.
      this.expandedKeys = expandedKeys
      this.autoExpandParent = false
    },
    onCheck(checkedKeys) {
      this.checkedKeys = checkedKeys
    },
    onSelect(selectedKeys, info) {
      this.selectedKeys = selectedKeys
    },
    buttonClick() {
      this.$router.push({ path: '/change/apply', query: { questionId: this.processDefinition.busId } })
    },
    handleCancel() {
      this.close()
    },
    // 关闭弹框
    close() {
      this.$emit('close')
      this.visible = false
      this.current = 0
    },
  },
  watch: {},
  mounted() {},
}
</script>
<style scoped>
.drawer-bootom-button {
  position: absolute;
  bottom: 0;
  width: 100%;
  border-top: 1px solid #e8e8e8;
  padding: 10px 16px;
  text-align: right;
  left: 0;
  background: #fff;
  border-radius: 0 0 2px 2px;
}
.ant-drawer-body {
  padding: 12px !important;
}
</style>
