<template>
  <div>
    <div v-if="showTitle" class="title-flag-wrapper"
         :style="{marginTop: titleMarginTop,marginBottom:titleMarginBottom}">
      <span class="title-flag" :style="{borderLeftColor:titleBorderLeftColor}">{{ title }}</span>
    </div>

    <div
      v-if="chartMiddleData&&chartMiddleData.length>0&&chartRingData&&chartRingData.length>0"
      :style="{height:chartHeight,padding:chartPadding,backgroundColor:backgroundColor,marginLeft:chartMarginLeft,marginRight:chartMarginRight,marginTop:chartMarginTop,marginBottom:chartMarginBottom}"
      class="chart-wrapper">
      <div ref="realTimeStatistic" :style="{width:chartWidth}" style="height: 100%"></div>
      <div :style="{width:`calc(100% - ${chartMarginLeft} - ${chartMarginRight} - ${chartWidth})`}"
          class="items-wrapper">
        <div v-for="(item,index) in chartRingData" :key="'realTimeStatistic_'+index" class="item-wrapper"
             :class="{'item-bottom-border':item.line&&item.line=='bottom','item-top-border':item.line&&item.line=='top',}" :title="item.name+':'+item.value">
          <span class="item-name">{{ item.name }} :</span>
          <span class="item-value">{{ item.value }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import echarts from 'echarts/lib/echarts'

export default {
  name: 'statisticPieChart',
  props: {
    //标题名
    title: {
      type: String,
      required: false,
      default: '基本信息'
    },
    //是否显示标题
    showTitle: {
      type: Boolean,
      required: false,
      default: true
    },
    //标题左侧边框颜色
    titleBorderLeftColor: {
      type: String,
      required: false,
      default: '#1e3674'
    },
    //标题上外边距
    titleMarginTop: {
      type: String,
      required: false,
      default: '20px'
    },
    //标题下外边距
    titleMarginBottom: {
      type: String,
      required: false,
      default: '10px'
    },
    chartHeight: {
      type: String,
      default: '230px',
      require: false
    },
    chartPadding: {
      type: String,
      default: '24px',
      require: false
    },
    chartMarginTop:{
      type: String,
      default: '0px',
      require: false
    },
    chartMarginBottom:{
      type: String,
      default: '0px',
      require: false
    },
    chartMarginLeft:{
      type: String,
      default: '0px',
      require: false
    },
    chartMarginRight:{
      type: String,
      default: '0px',
      require: false
    },
    backgroundColor: {
      type: String,
      default: '#f8f9fd',
      require: false
    },
    totalWidth: {
      type: String,
      default: '100%',
      require: false
    },
    chartWidth: {
      type: String,
      default: '50%',
      require: false
    },
    chartMiddleData: {
      type: Array,
      require: false,
      default: () => {
        return [
          { name: '执行下发率', value: '73',unit:'%' }
        ]
      }
    },
    chartRingData: {
      type: Array,
      require: false,
      default: () => {
        return [
          { name: '已下发数量', value: 362, unit: '个' },
          { name: '未下发数量', value: 20, unit: '个' }
        ]
      }
    },
    lineLocation:{
      type: String,
      required: false,
      default: 'top'
    },
    colorList: {
      type: Array,
      require: false,
      default: () => {
        return [['#205BFD', '#76B2FF'], ['#D7E2FC', '#D7E2FC']]
      }
    }
  },
  data() {
    return {}
  },
  watch: {
    chartRingData: {
      handler(val) {
        this.initChart()
      }
    }
  },
  mounted() {
    this.initChart()
  },
  methods: {
    initChart() {
      if (this.chartMiddleData&&this.chartMiddleData.length>0&&this.chartRingData&&this.chartRingData.length>0){
        this.$nextTick(() => {
          let doc = this.$refs.realTimeStatistic
          let myChart = this.$echarts.init(doc)
          const colorList = this.colorList
          let option = {
            // 环形图中间默认显示文字
            title: {
              show: true,
              text: this.chartMiddleData[0].value + this.chartMiddleData[0].unit,
              top: '33%',
              x: 'center', //水平安放位置，默认为'left'，可选为：'center' | 'left' | 'right' | {number}（x坐标，单位px）
              y: 'center',
              itemGap: 6, //主副标题之间的距离

              subtext: this.chartMiddleData[0].name,
              textStyle: {
                fontFamily: 'DIN-Medium',
                fontSize: 34,
                color: '#1B5FF8'
              },
              subtextStyle: {//副标题文本样式{"color": "#aaa"}
                align: 'center',
                fontSize: 16,
                // fontWeight: "bold",
                color: 'rgba(0,0,0,0.85)'
              }
            },
            tooltip: {
              // 悬浮的位置可以调整，总距离左边10px
              position: (point) => {
                return [point[0] + 10, point[1] + 10]
              }
              /* formatter: (param) => {
                let { name, value ,unit} = param.data
                let result = `<div>${name}：${value}(${unit})</div>`
                return result
              }*/
            },
            legend: {
              show: false,
              orient: 'vertial',
              itemWidth: 16,
              itemHeight: 16,
              icon: 'circle',
              right: 150,
              bottom: '40%',
              data: this.chartRingData,
              textStyle: {
                color: 'rgba(0,0,0,0.65)'
              },
              formatter: (name) => {
                const item = this.chartRingData.find(item => item.name === name)
                return `${name}：${item.value} ${item.unit}`
              }
            },
            series: [
              {
                z:1,
                type: 'pie',
                radius: ['65%', '88%'],
                center: ['50%', '50%'], // 图形位置
                label: { // 鼠标悬浮具体数据显示
                  show: false
                },
                data: [
                  {
                    value: this.chartRingData[0].value,
                    name: this.chartRingData[0].name,
                    itemStyle: {
                      color: new echarts.graphic.RadialGradient(0.41, 0.33, 0.65,
                        [
                          { offset: 0, color: this.colorList[0][0] },
                          { offset: 1, color: this.colorList[0][1] }
                        ]),
                      shadowOffsetX: 0,
                      shadowOffsetY: 2,
                      shadowBlur: 6, //波浪的阴影范围
                      shadowColor: 'rgba(131,180,255,0.50)', //阴影颜色
                    }
                  },
                  {
                    value: this.chartRingData[1].value,
                    name:this.chartRingData[1].name,
                    itemStyle: {
                      color: new echarts.graphic.RadialGradient(0.41, 0.33, 0.65,
                        [
                          { offset: 0, color: this.colorList[1][0] },
                          { offset: 1, color: this.colorList[1][1] }
                        ]),
                      shadowOffsetX: 0,
                      shadowOffsetY: 0,
                      shadowBlur: 0, //波浪的阴影范围
                      shadowColor: 'rgba(131,180,255,0)', //阴影颜色
                    }
                  }
                ]
              }
            ]
          }
          myChart.setOption(option)

          window.addEventListener('resize', () => {
            myChart.resize()
          })
        })
      }
    }
  }
}
</script>

<style scoped lang="less">
.title-flag-wrapper {
  .title-flag {
    padding-left: 7px;
    font-size: 14px;
    border-left-width: 4px;
    border-left-style: solid;
    color: rgba(0, 0, 0, 0.85);
  }
}

.chart-wrapper {
  display: flex;
  flex-flow: row nowrap;
  justify-content: center;
  align-items: center;
  width: 100%;
}
.items-wrapper{
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: start;
  flex-flow: column nowrap;
   overflow: hidden;

  .item-wrapper {
    max-width: calc(100% - 40px);
    padding: 10px 0;
    //padding: 10px 0 10px 50px;
    margin-left: 40px;
    white-space: nowrap;
    overflow: hidden;
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
    justify-content: start;

    .item-name {
      display: inline-block;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.45);
      letter-spacing: 0.5px;
      font-weight: 400;
      margin-right: 8px;
    }

    .item-value {
      display: inline-block;
      font-family: DIN-Medium;
      font-size: 16px;
      color: #000000;
      letter-spacing: 0.57px;
      font-weight: 500;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;

    }
  }

  .item-top-border {
    border-top: 1px dashed #DADCDF;
  }
  .item-bottom-border {
    border-bottom: 1px dashed #DADCDF;
  }
}

</style>