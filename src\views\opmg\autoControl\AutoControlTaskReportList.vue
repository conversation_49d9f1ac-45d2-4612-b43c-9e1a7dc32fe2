<template>
  <a-row :gutter='10' style='height: 100%' class='vScroll'>
    <a-col style='width:100%;height: 100%;display: flex;flex-direction: column'>
      <!-- 查询区域 -->
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0', marginRight: '12px' }" class='card-style'
        style='width: 100%'>
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind='formItemLayout'>
            <a-row :gutter="24" ref="row">
              <a-col :span='spanValue'>
                <a-form-item label="作业名称">
                  <a-input placeholder="请输入作业名称" v-model="queryParam.taskName" allowClear autocomplete='off'
                    :maxLength="50"></a-input>
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label="场景名称">
                  <a-input placeholder="请输入场景名称" v-model="queryParam.sceneName" allowClear autocomplete='off'
                    :maxLength="50">
                  </a-input>
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label="执行结果">
                  <a-select placeholder="请选择执行结果" v-model="queryParam.successFlag" allowClear
                    :getPopupContainer='(node) => node.parentNode'>
                    <a-select-option value="false">失败</a-select-option>
                    <a-select-option value="true">成功</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span='spanValue' v-show="toggleSearchStatus">
                <a-form-item label="执行时间">
                  <a-range-picker class="a-range-picker-choice-date" @change="onChangeTime"
                    v-model="queryParam.timeRange" format="YYYY-MM-DD" :placeholder="['开始时间', '截止时间']" />
                </a-form-item>
              </a-col>
              <a-col :span='colBtnsSpan()'>
                <span class='table-page-search-submitButtons'
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button type='primary' class='btn-search btn-search-style' @click='searchQuery'>查询</a-button>
                  <a-button class='btn-reset btn-reset-style' @click='searchReset'>重置</a-button>
                  <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <!-- table区域-begin -->
      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <div class="table-operator table-operator-style" v-if="selectedRowKeys.length > 0">
          <a-dropdown>
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item key="1" @click="batchDel">删除</a-menu-item>
            </a-menu>
            <a-button> 批量操作
              <a-icon type="down" />
            </a-button>
          </a-dropdown>
        </div>

        <a-table ref="table" bordered rowKey="id" :columns="columns" :dataSource="dataSource" :pagination="ipagination"
          :loading="loading" class="j-table-force-nowrap"
          :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }" @change="handleTableChange">
          <span slot="action" slot-scope="text, record">
            <a @click="logLook(record.executeLog,'查看日志')">查看日志</a>
            <a-divider type='vertical' />
            <a @click="jbLook(record.scriptContext,'查看脚本')">查看脚本</a>
            <a-divider type='vertical' />
            <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
              <a style="color: #409eff">删除</a>
            </a-popconfirm>
          </span>
          <template slot='tooltip' slot-scope='text'>
            <div>
              {{ text.substring(0,30) }}
            </div>
          </template>
          <template slot='successFlag' slot-scope='text'>
            <span>{{ text == 'true' ? '成功' :text == 'false' ? '失败':''}}</span>
          </template>
        </a-table>
      </a-card>
      <a-modal v-model="modelShow" :title="modelName" @ok="modelOk" width="1200px" :footer="null">
        <div v-html="modelContent" style="max-height: 600px;overflow: hidden;overflow-y: auto;"></div>
      </a-modal>
      <!-- table区域-end -->
      <!-- 表单区域 -->
      <auto-control-task-report-modal ref="modalForm" @ok="modalFormOk"></auto-control-task-report-modal>
    </a-col>
  </a-row>
</template>

<script>
  import '@/assets/less/TableExpand.less'
  import AutoControlTaskReportModal from './modules/AutoControlTaskReportModal'
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import {
    YqFormSearchLocation
  } from '@/mixins/YqFormSearchLocation'

  export default {
    name: "AutoControlTaskReportList",
    mixins: [JeecgListMixin, YqFormSearchLocation],
    components: {
      AutoControlTaskReportModal
    },
    data() {
      return {
        description: '自动化控制历史作业管理页面',
        formItemLayout: {
          labelCol: {
            style: 'width:100px'
          },
          wrapperCol: {
            style: 'width:calc(100% - 100px)'
          }
        },
        modelShow: false,
        modelName: '',
        modelContent: '',
        // 表头
        columns: [{
            title: '产品名称',
            align: "center",
            dataIndex: 'productName',
            customCell: () => {
              let cellStyle = 'text-align: left; min-width: 100px;max-width:300px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '设备名称',
            align: "center",
            dataIndex: 'deviceName',
            customCell: () => {
              let cellStyle = 'text-align: left; min-width: 80px;max-width:300px'
              return {
                style: cellStyle
              }
            }
          }, {
            title: '作业名称',
            align: "center",
            dataIndex: 'taskName',
            customCell: () => {
              let cellStyle = 'text-align: left; min-width: 80px;max-width:300px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '场景名称',
            align: "center",
            dataIndex: 'sceneName',
            customCell: () => {
              let cellStyle = 'text-align: left; min-width: 80px;max-width:300px'
              return {
                style: cellStyle
              }
            }
          }, {
            title: '执行状态',
            align: "center",
            dataIndex: 'executeStatus',
            customCell: () => {
              let cellStyle = 'text-align: left; min-width: 80px;max-width:300px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '执行结果',
            align: "center",
            dataIndex: 'successFlag',
            scopedSlots: {
              customRender: 'successFlag'
            },
            width: 100,
            customCell: () => {
              let cellStyle = 'text-align: center;'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '执行时间',
            align: "center",
            dataIndex: 'createTime',
            scopedSlots: {
              customRender: 'tooltip'
            },
            width: 250,
          },
          /*{
            title: '操作日志',
            align: "center",
            dataIndex: 'executeLog',
            scopedSlots: {
              customRender: 'tooltip'
            },
            width: 250,
          },
          {
            title: '配置文本',
            align: "center",
            dataIndex: 'scriptContext',
            scopedSlots: {
              customRender: 'tooltip'
            },
            width: 250,
          },*/
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: 150,
            scopedSlots: {
              customRender: 'action'
            }
          }
        ],
        url: {
          list: "/autoControl/task/report/list",
          delete: "/autoControl/task/report/delete",
          deleteBatch: "/autoControl/task/report/deleteBatch",
          exportXlsUrl: "/autoControl/task/report/exportXls",
          importExcelUrl: "/autoControl/task/report/importExcel",
        },
      }
    },
    computed: {
      importExcelUrl: function () {
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
      }
    },
    methods: {
      jbLook(text, name) {
        console.log(text);
        this.modelShow = true
        this.modelName = name
        this.modelContent = text
      },
      logLook(text, name) {
        this.modelShow = true
        this.modelName = name
        this.modelContent = text
      },
      modelOk() {
        this.modelShow = false
      },
      getHtml(value) {
        let textString = value.replace(/<br\s*\/?>/g, "\n")
        return textString
      },
      /**改变时间*/
      onChangeTime(date, dateString) {
        this.queryParam.startTime = dateString[0]
        this.queryParam.endTime = dateString[1]
      }
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less';
</style>