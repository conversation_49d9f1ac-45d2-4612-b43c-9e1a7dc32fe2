import { nodeProperties, edgeProperties } from '../graph/fieldsTranfer'
import { getAction } from '@/api/manage'

const tryToJSON = (val) => {
  try {
    return JSON.parse(val)
  } catch (error) {
    return val
  }
}
export default {
  data() {
    return {}
  },
  watch: {
    // 拓扑图背景图改变
    'globalGridAttr.topoConfig.bgimg'(path) {
      if (this.globalGridAttr.topoConfig.bgType === '2') {
        this.graph.clearBackground()
        this.graph.drawBackground({
          image: path.includes('http') ? path : window._CONFIG['staticDomainURL'] + '/' + path,
          size: '100% 100%'
        })
      }
    },
    'globalGridAttr.topoConfig.bgColor'() {
      this.setTopoBg()
    },
    //拓扑图背景类型改变
    'globalGridAttr.topoConfig.bgType'() {
      this.setTopoBg()
    },
    //拓扑线条状态改变
    'globalGridAttr.topoConfig.edgeStatus'() {
      let edges = this.graph.getEdges()
      edges.forEach(edge => {
        this.setEdgeStatus(edge)
      })
    },
    'globalGridAttr.topoConfig.unboundColor'(e) {
      let nodes = this.graph.getNodes()
      nodes.forEach(node => {
        let { nodeType, portIndex } = node.getData()
        if (nodeType === 'panelElement') {
          //未绑定颜色变化同步图标颜色
          if (this.operate === 'create' && portIndex === '') {
            node.setData({ iconColor: this.globalGridAttr.topoConfig.unboundColor })
          }
        }
      })
    },
    'globalGridAttr.topoConfig.boundColor'(e) {
      let nodes = this.graph.getNodes()
      nodes.forEach(node => {
        let { nodeType, portIndex } = node.getData()
        if (nodeType === 'panelElement') {
          //未绑定颜色变化同步图标颜色
          if (this.operate === 'create' && portIndex !== '') {
            node.setData({ iconColor: this.globalGridAttr.topoConfig.boundColor })
          }
        }
      })
    }
  },
  methods: {
    //将保存数据转换成节点连线
    dataTransferCell(nodeList, edgeList = []) {
      let nodes = []
      let edges = []
      //节点转换
      nodeList.forEach(el => {
        let keys = Object.keys(nodeProperties)
        let tem = {busData:{}}
        keys.forEach(k => {
          if (el[k] !== null) {
            if(nodeProperties[k] === "bus_data"){
              tem.busData[k] = el[k]
            }else{
              tem[nodeProperties[k]] = el[k] && typeof el[k] === 'string' ? tryToJSON(el[k]) : el[k]
            }

            if (nodeProperties[k] === 'attrs') {
              let attr = tem[nodeProperties[k]]
              if (attr.image && attr.image['xlink:href']) {
                if (tem[nodeProperties[k]]['image']['xlink:href'].includes('http')) {
                  // minio 全路径
                } else {
                  tem[nodeProperties[k]]['image']['xlink:href'] = this.apiUrl + '/' + tem[nodeProperties[k]]['image']['xlink:href']
                }

              }
            }
          }
        })
        //生产面板时动态修改元素图标
        if (tem.data.nodeType === 'panelElement' && tem.data.typeCode) {
          let eletype = this.panelElementTypes.find(el => el.typeCode === tem.data.typeCode)
          tem.data.typeIcon = eletype ? eletype.typeIcon : ''
        }
        tem.data = Object.assign(tem.data, tem.busData)
        nodes.push(tem)
      })
      let cnodes = this.graph.parseJSON({ nodes: nodes })
      this.graph.addNodes(cnodes, { silent: false })

      //连线转换
      edgeList.forEach(el => {
        let keys = Object.keys(edgeProperties)
        let tem = {}
        keys.forEach(k => {
          if (el[k] !== null) {
            tem[edgeProperties[k]] = el[k] && typeof el[k] === 'string' ? tryToJSON(el[k]) : el[k]
          }
        })
        edges.push(tem)
      })
      let cedges = this.graph.parseJSON({ edges: edges })
      this.graph.addEdges(cedges, { silent: false })
    },
    //将节点连线属性转换成要保存的数据
    cellTransferData(cells) {
      let obj = {
        nodeList: [],
        edgeList: []
      }
      cells.forEach(el => {
        if (el.shape !== 'edge') {
          obj.nodeList.push(this.setPropertice(nodeProperties, el))
        } else {
          obj.edgeList.push(this.setPropertice(edgeProperties, el))
        }
      })

      return obj
    },
    setPropertice(properties, cell) {
      let temData = { topoId: this.topoInfo.id }
      let keys = Object.keys(properties)
      keys.forEach(k => {
        if (properties[k]  === 'bus_data') {
          temData[k] = cell.data[k]===undefined?null:cell.data[k]
        } else if (properties[k] && cell[properties[k]]) {
          if (typeof cell[properties[k]] === 'object') {
            temData[k] = JSON.stringify(cell[properties[k]])
          } else {
            temData[k] = cell[properties[k]]
          }
        } else {
          temData[k] = null
        }
      })
      return temData
    },
    //保存拓扑图时 同步拓扑图配置信息
    saveTopoConfig() {
      this.topoInfo.topoName = this.globalGridAttr.topoName
      this.topoInfo.topoType = this.value
      this.topoInfo.topoConfig = JSON.stringify(this.globalGridAttr.topoConfig)
      this.topoInfo.connectInfo = JSON.stringify(this.globalGridAttr.connectInfo)
      this.topoInfo.alarmTemplateId = this.globalGridAttr.alarmTemplateId
    },
    initConfig() {
      this.setTopoConfig()
      this.setTopoInfo()
      if (this.operate === 'show') {
        let nodes = this.graph.getNodes()
        nodes.forEach(el => {
          if (el.data.typeCode === 'RJ45') {
            el.setData({ iconColor: this.globalGridAttr.topoConfig.downColor })
          }
        })
      }
      // this.setTopoBg();
    },
    //设置拓扑信息
    setTopoInfo() {
      this.globalGridAttr.topoName = this.topoInfo.topoName
      this.globalGridAttr.showType = this.topoInfo.showType
      this.globalGridAttr.topoType = this.topoInfo.topoType
    },
    // 设置拓扑图配置
    setTopoConfig() {
      //节点连线的配置
      let data = this.topoInfo.topoConfig ? tryToJSON(this.topoInfo.topoConfig) : null
      this.globalGridAttr.setTopoConfig(data)
    },
    //设置 snmp 的参数
    setConnectInfo() {
      //snmp连接参数
      this.globalGridAttr.setConnectInfo()
      this.globalGridAttr.alarmTemplateId = ''
      getAction('/product/panelInfo/queryAllByProductId', { productId: this.productId })
        .then(res => {
          if (res.success && res.result && res.result.length > 0) {
            this.globalGridAttr.alarmTemplateId = res.result[0].alarmTemplateId
            let info = tryToJSON(res.result[0].connectInfo)
            this.globalGridAttr.setConnectInfo(info)
          }
        })
    },
    //设置拓扑背景
    setTopoBg() {
      let topoConfig = this.globalGridAttr.topoConfig
      //背景图片
      if (topoConfig.bgType === '2' && !!topoConfig.bgimg && !this.topoBgByTheme) {
        // console.log("背景路径", topoConfig.bgimg)
        this.graph.clearBackground()
        this.graph.drawBackground({
          image: topoConfig.bgimg.includes('http') ? topoConfig.bgimg : window._CONFIG['staticDomainURL'] + '/' + topoConfig.bgimg,
          size: '100% 100%'
        })
      } //设置背景色

      if (topoConfig.bgType === '1' && !!topoConfig.bgColor && !this.topoBgByTheme) {
        this.graph.clearBackground()
        this.graph.drawBackground({
          color: topoConfig.bgColor
        })
      }
    }
    // 大量节点测试

  }
}