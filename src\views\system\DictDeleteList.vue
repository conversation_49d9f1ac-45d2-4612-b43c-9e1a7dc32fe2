<template>
  <j-modal
    :width="modalWidth"
    :centered='true'
    :visible="visible"
    :maskClosable="false"
    @cancel="handleCancel">
    <template slot="footer">
      <a-button @click="handleCancel">关闭</a-button>
    </template>
    <a-table
      ref="table"
      rowKey="id"
      size="middle"
      :columns="columns"
      :loading="loading"
      :dataSource="dataSource"
      :scroll='dataSource.length>0?{x:"max-content"}:{}'
      :pagination="false">
      <span slot="action" slot-scope="text, record">
        <a @click="handleBack(record.id)"><a-icon type="redo"/>字典取回</a>
        <a-divider type="vertical"/>
        <a @click="handleDelete(record.id)"><a-icon type="scissor"/>彻底删除</a>
      </span>
    </a-table>

  </j-modal>


</template>

<script>
  import { getAction,deleteAction,putAction } from '@/api/manage'
  export default {
    name: "DictDeleteList",
    data () {
      return {
        modalWidth: '1000px',
        // modalStyle: { 'top': '20px'},
        title: '操作',
        visible: false,
        loading: false,
        dataSource:[],
        columns:[
          {
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            customRender: function (t, r, index) {
              return parseInt(index) + 1;
            },
            customCell: () => {
              let cellStyle = 'text-align: center; min-width: 100px;max-width:200px'
              return { style: cellStyle }
            },
          },
          {
            title: '字典名称',
            dataIndex: 'dictName',
            customCell: () => {
              let cellStyle = 'text-align: center; min-width: 100px;max-width:500px'
              return { style: cellStyle }
            },
          },
          {
            title: '字典编号',
            dataIndex: 'dictCode',
            customCell: () => {
              let cellStyle = 'text-align: center; min-width: 100px;max-width:500px'
              return { style: cellStyle }
            },
          },
          {
            title: '描述',
            dataIndex: 'description',
            customCell: () => {
              let cellStyle = 'text-align: center; min-width: 100px;max-width:500px'
              return { style: cellStyle }
            },
          },
          {
            title: '操作',
            dataIndex: 'action',
            scopedSlots: {customRender: 'action'},
            customCell: () => {
              let cellStyle = 'text-align: center; min-width: 100px;max-width:400px'
              return { style: cellStyle }
            },
          }
        ]
      }
    },

    methods: {
      handleCancel(){
        this.visible = false
        //回收站字典列表刷新
        this.$emit("refresh")
      },
      show(){
        this.visible = true
        this.loadData();
      },
      loadData(){
        this.loading = true
        getAction("/sys/dict/deleteList").then(res=>{
          this.loading = false
          if(res.success){
            this.dataSource = res.result
          }else{
            this.$message.warning(res.message)
          }
        })
      },
      handleBack(id){
        putAction("/sys/dict/back/"+id).then(res=>{
          if(res.success){
            this.$message.success(res.message)
            this.loadData();
          }else{
            this.$message.warning(res.message)
          }
        })
      },
      handleDelete(id){
        this.$confirm({
          title: '彻底删除字典',
          okText: '是',
          cancelText: '否',
          content: (<div>
            <p>是否彻底删除这个字典项？</p>
            <p style="color:red;">注意：彻底删除后将无法恢复，请谨慎操作！</p>
            </div>),
          centered: false,
          onOk: () => {
          var that = this;
          deleteAction("/sys/dict/deletePhysic/"+id).then((res) => {
            if (res.success) {
              this.$message.success(res.message)
              this.loadData();
            } else {
              that.$message.warning(res.message);
            }
          });
        },
      })
      }

    }
  }
</script>

<style scoped lang='less'>
/*表头样式*/
::v-deep .ant-table-thead > tr > th {
  text-align: center;
  white-space: nowrap;
}

/*内容对齐方式、省略显示*/
::v-deep .ant-table-tbody > tr > td {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}
::v-deep .ant-modal-body {
  padding: 24px 48px 24px 48px;
}

::v-deep .ant-modal {
  padding: 24px;
}

@media (max-width: 1012px) {
  ::v-deep .ant-modal {
    max-width: calc(100vw - 12px);
    margin: 0;
  }
}
.j-modal-box.fullscreen {
  margin: 0 !important;
  max-width: 100vw !important;
  ::v-deep .ant-modal {
    max-width: 100vw !important;
    margin: 0;
  }
}
</style>