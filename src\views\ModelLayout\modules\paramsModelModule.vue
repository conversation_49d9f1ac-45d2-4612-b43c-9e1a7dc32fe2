<template>
  <a-drawer
    :title="title"
    :width="drawerWidth"
    @close="handleCancel"
    :visible="visible"
    :confirmLoading="confirmLoading"
  >
    <div :style="{ width: '100%', border: '1px solid #e9e9e9', padding: '10px 16px', background: '#fff' }">
      <a-spin :spinning="confirmLoading">
        <a-form :form="form">
          <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="中文名">
            <a-input
              placeholder="请输入中文名"
              v-decorator="['paramName', validatorRules.paramName]"
              :readOnly="disableSubmit"
              autocomplete="off"
              :allowClear="true"
            />
          </a-form-item>
          <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="参数名">
            <a-input
              placeholder="请输入参数名"
              v-decorator="['paramCode', validatorRules.paramCode]"
              :readOnly="disableSubmit"
              autocomplete="off"
              :allowClear="true"
            />
          </a-form-item>
          <a-form-item
            label="数据类型"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
            :required="true"
            :help="typeData"
            :validateStatus="typeStatus"
          >
            <a-select v-model="dataType" @change="dataTypeChoose" placeholder="请选择数据类型" autocomplete="off">
              <a-select-opt-group>
                <span slot="label">基本类型</span>
                <a-select-option value="int">int(整型)</a-select-option>
                <a-select-option value="long">long(长整数型)</a-select-option>
                <a-select-option value="float">float(单精度浮点型)</a-select-option>
                <a-select-option value="double">double(双精度浮点型)</a-select-option>
                <a-select-option value="text">text(字符串)</a-select-option>
                <a-select-option value="bool">bool(布尔型)</a-select-option>
              </a-select-opt-group>
              <a-select-opt-group>
                <span slot="label">其他类型</span>
                <!-- <a-select-option value="date">date(时间型)</a-select-option> -->
                <a-select-option value="array">array(数组型)</a-select-option>
              </a-select-opt-group>
            </a-select>
          </a-form-item>
          <a-form-item label="是否必填" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-switch
              checked-children="是"
              un-checked-children="否"
              defaultChecked
              @change="judgeChange"
              v-model="judge"
            />
          </a-form-item>
          <a-form-item
            label="是否为选择框"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
            v-if="this.dataType == 'text' || this.dataType == 'array'"
          >
            <a-switch
              checked-children="是"
              un-checked-children="否"
              defaultChecked
              @change="selectChange"
              v-model="isSelect"
            />
          </a-form-item>
          <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="提示">
            <a-input
              placeholder="请输入提示"
              v-decorator="['tips']"
              :readOnly="disableSubmit"
              autocomplete="off"
              :allowClear="true"
            />
          </a-form-item>
        </a-form>
      </a-spin>
      <a-row :style="{ textAlign: 'right' }">
        <a-button :style="{ marginRight: '8px' }" @click="handleCancel"> 关闭 </a-button>
        <a-button :disabled="disableSubmit" @click="handleOk" type="primary">确定</a-button>
      </a-row>
    </div>
  </a-drawer>
</template>

<script>
import { addModel, editModel } from '@/api/api'
import pick from 'lodash.pick'
import { getAction, deleteAction, httpAction } from '@/api/manage'

export default {
  name: 'paramsModelModule',
  data() {
    return {
      index: 0,
      typeData: '',
      typeStatus: '',
      judge: false,
      isSelect: false,
      dataType: '',
      drawerWidth: 700,
      title: '操作',
      visible: false,
      disableSubmit: false,
      model: {},
      parentId: '',
      localMenuType: '0',
      show: true, //根据菜单类型，动态显示隐藏表单元素
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      validatorRules: {
        paramName: {
          rules: [
            { required: true, message: '请输入名称' },
            { max: 20, message: '名称长度不超过20个字符！' },
          ],
        },
        paramCode: {
          rules: [
            { required: true, message: '请输入标识' },
            { min: 0, max: 20, message: '标识长度应在0-20之间！' },
          ],
        },
      },
      url: {
        add: '/flow/cmp/add',
        edit: '/flow/cmp/edit',
      },
      confirmLoading: false,
      form: this.$form.createForm(this),
    }
  },
  methods: {
    dataTypeChoose(dataType) {
      this.dataType = dataType
      this.typeData = ''
      this.typeStatus = ''
    },
    judgeChange(judge) {
      this.judge = judge
    },
    selectChange(val) {
      this.isSelect = val
    },
    add() {
      // 默认值
      this.edit({})
    },
    edit(record, index) {
      this.index = index
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.dataType = record.dataType != null ? record.dataType : ''
      this.judge = record.judge != null ? record.judge : false
      this.isSelect = record.isSelect != null ? record.isSelect : false
      this.visible = true
      let fieldsVal = pick(this.model, 'paramName', 'paramCode', 'tips')
      this.$nextTick(() => {
        this.form.setFieldsValue(fieldsVal)
      })
    },
    close() {
      this.$emit('close')
      this.disableSubmit = false
      this.visible = false
    },
    handleOk() {
      if (this.dataType == '' || this.dataType == null || this.dataType == undefined) {
        this.typeData = '请选择数据类型'
        this.typeStatus = 'error'
      }
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          let formData = Object.assign(this.model, values)
          formData.dataType = this.dataType != undefined ? this.dataType : ''
          formData.judge = this.judge
          formData.isSelect = this.isSelect
          that.confirmLoading = true
          let dataForm = JSON.stringify(formData)
          that.$emit('ok', dataForm, this.index)
          that.visible = false
          that.confirmLoading = false
        }
      })
    },
    handleCancel() {
      this.close()
    },
  },
}
</script>

<style lang='less' scoped>
::v-deep .two-words > div > label {
  letter-spacing: 4px;
}
::v-deep .two-words > div > label::after {
  letter-spacing: 0px;
}
@media (max-width: 700px) {
  ::v-deep .ant-drawer-content-wrapper {
    max-width: 100vw;
    margin: 0;
  }
}
</style>
