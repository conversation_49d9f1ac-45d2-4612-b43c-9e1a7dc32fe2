<template>
  <j-modal
    :title="title"
    :visible="visible"
    :destroyOnClose="true"
    fullscreen
    @cancel="handleCancel"
    cancelText="关闭"
  >
   <template slot="footer">
      <a-button @click="handleCancel">关闭</a-button>
    </template>
    <vis-edit v-if="visible" ref="visEdit" operate="show" canvasId="asFullScreen" :lighterSetting="lighterSetting"></vis-edit>
  </j-modal>
</template>

<script>
import VisEdit from '@/views/topo/nettopo/modules/VisEdit'
export default {
  name: 'associateFullsreen',
  components: {
    VisEdit,
  },
  data() {
    return {
      title: '拓扑图查看',
      width: 800,
      record: {},
      visible: false,
      disableSubmit: false,
      lighterSetting: {
        isZoom: false, // 是否设置画布的缩放级别
        isCenter:false, // 是否将指定的点与视口中心对齐
        fillColor: '#ff7500' // 指定的节点填充的颜色
      }
    }
  },
  methods: {
    show(id, businessInfo) {
      this.title = businessInfo.businessName
      this.visible = true
        this.$nextTick(() => {
          this.$refs.visEdit.createTopo(id ,false, businessInfo.businessNode)
        })
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleCancel() {
      this.close()
    },
  },
}
</script>

<style lang="less" scoped>

</style>
