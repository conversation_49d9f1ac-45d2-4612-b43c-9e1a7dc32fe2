<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container>
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-item class="two-words" label="标识" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['name', validatorRules.name]" placeholder="请输入标识" :allowClear="true"
                autocomplete="off"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item class="two-words" label="名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['displayName', validatorRules.displayName]" placeholder="请输入名称" :allowClear="true"
                autocomplete="off"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="产品模板" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-select show-search optionFilterProp="children" allowClear option-label-prop="label"
                v-decorator="['productModel', validatorRules.productModel]" placeholder="请选择产品模板">
                <a-select-option v-for="item in productList" :value="item.id" :key="item.id" :label="item.displayName">
                  {{ item.displayName }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
  import {httpAction,getAction} from '@/api/manage'
  import pick from 'lodash.pick'
  import {ValidateRequiredFields} from '@/utils/rules.js'
  export default {
    name: 'ProductCopyForm',
    components: {},
    props: {
      // 流程表单data
      formData: {
        type: Object,
        default: () => {},
        required: false,
      },
      // 表单模式：true流程表单 false普通表单
      formBpm: {
        type: Boolean,
        default: false,
        required: false,
      },
      // 表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false,
      },
    },
    data() {
      return {
        form: this.$form.createForm(this),
        productList: [],
        model: {},
        labelCol: {
          xs: {
            span: 24,
          },
          sm: {
            span: 5,
          },
        },
        wrapperCol: {
          xs: {
            span: 24,
          },
          sm: {
            span: 16,
          },
        },
        confirmLoading: false,
        validatorRules: {
          name: {
            rules: [
              { required: true, message: '请输入标识' },
              {
                pattern: /^([a-zA-Z][a-zA-Z0-9_]{4,63})$/,
                message: '字母开头，可包含字母、数字或下划线，5-64个字符',
                trigger: 'blur',
              }],
          },
          displayName: {
            rules: [{
              required: true,
              validator: (rule, value, callback) =>ValidateRequiredFields(rule, value, callback,'名称',20,2)}
            ]
          },
          productModel: {
            rules: [{
              required: true,
              message: '请选择产品模板',
            }]
          }
        },
        url: {
          copyForProductModel: '/product/product/copyForProductModel',
          productList: '/product/product/productList'
        },
      }
    },
    created() {},
    methods: {
      init() {
        this.getProductList();
        this.form.resetFields()
        this.visible = true
        this.$nextTick(() => {
          this.form.setFieldsValue(
            pick(
              'name',
              'displayName',
              'productModel'
            )
          )
        })
      },
      getProductList() {
        getAction(this.url.productList)
          .then((res) => {
            this.productList = res.result
          });
      },
      submitForm() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            this.$emit('loading', true)
            that.confirmLoading = true
            httpAction(this.url.copyForProductModel, values, 'post')
              .then((res) => {
                if (res.success) {
                  that.$message.success(res.message)
                  that.$emit('ok')
                } else {
                  that.$message.warning(res.message)
                }
              })
              .finally(() => {
                this.$emit('loading', false)
                that.confirmLoading = false
              });
          }
        })
      },
    }
  }
</script>
<style lang='less' scoped>
  @import '~@assets/less/scroll.less';

  ::v-deep .two-words>div>label {
    letter-spacing: 4px;
  }

  ::v-deep .two-words>div>label::after {
    letter-spacing: 0px;
  }

  ::v-deep .ant-upload-list-item-info>span {
    display: flex;
    justify-content: center;
  }

  ::v-deep .ant-upload-list-picture-card .ant-upload-list-item-thumbnail,
  ::v-deep .ant-upload-list-picture-card .ant-upload-list-item-thumbnail img {
    position: relative;
    display: flex;
    width: 90%;
    height: auto;
    left: 0px;
    top: 0px !important;
    align-items: center;
    justify-content: center;
  }

  .jobChange {
    background-color: #f5f5f5;
  }
</style>