<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container>
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-item label="指标名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                v-decorator="['metricsName', validatorRules.metricsName]"
                placeholder="请输入指标名称"
                :allowClear="true"
                autocomplete="off"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="指标类别" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-select
                :getPopupContainer="(node) => node.parentNode"
                v-decorator="['metricsTypeId', validatorRules.metricsTypeId]"
                :allowClear="true"
                placeholder="请选择指标类别"
                :disabled='disabledCategory'
                @change="categoryChange"
              >
                <a-select-option v-for="(item, index) in categoryList" :key="item.id">
                  {{ item.typeName }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="指标备注" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-textarea
                style="width: 100%"
                v-decorator="['groupDesc', validatorRules.groupDesc]"
                :autoSize="{ minRows: 2, maxRows: 4 }"
                :allow-clear="true"
                autocomplete="off"
                placeholder="请输入指标备注"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import pick from 'lodash.pick'
import JFormContainer from '@/components/jeecg/JFormContainer'
import {
  ValidateRequiredFields,
  ValidateOptionalFields
} from '@/utils/rules.js'
export default {
  name: 'metricsForm',
  components: {
    JFormContainer,
  },
  props: ['metricsTypeId'],
  data() {
    return {
      form: this.$form.createForm(this),
      model: {},
      userId: null,
      labelCol: {
        xs: {
          span: 24,
        },
        sm: {
          span: 5,
        },
      },
      wrapperCol: {
        xs: {
          span: 24,
        },
        sm: {
          span: 16,
        },
      },
      confirmLoading: false,
      validatorRules: {
        metricsName: {
          rules: [
            {
              required: true,
              validator: (rule, value, callback) =>ValidateRequiredFields(rule, value, callback,'指标名称',30,1)
            }
          ]
        },
        metricsTypeId: {
          rules: [
            {
              required: true,
              message: '请选择指标类别',
            },
          ],
        },
        groupDesc: {
          rules: [
            { required: false,validator: (rule, value, callback) =>ValidateOptionalFields(rule, value, callback,'指标备注',255)}
          ]
        }
      },
      url: {
        add: '/evaluate/metricsInfo/add',
        edit: '/evaluate/metricsInfo/edit',
        getCategoryList: '/evaluate/metricsType/list', // 获取全部类别
      },
      disabledCategory: false, // 设置禁用类别
      categoryList: [], // 获取指标类别
    }
  },
  created() {
    this.getCategoryList()
  },
  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.$nextTick(() => {
        this.form.setFieldsValue(pick(this.model, 'metricsName', 'metricsTypeId', 'groupDesc'))
      })
      if (this.metricsTypeId) {
        this.disabledCategory = true
        this.$nextTick(() => {
          this.form.setFieldsValue({ metricsTypeId: this.metricsTypeId })
        })
      }
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let formData = Object.assign(this.model, values)
          console.log('formData==', formData)

          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    getCategoryList() {
      getAction(this.url.getCategoryList, {}).then((res) => {
        if (res.success) {
          this.categoryList = res.result
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    categoryChange() {},
  },
}
</script>
