<template>
  <a-tabs :animated="false" v-model="activeKey" class="ele-attr">
    <a-tab-pane tab="样式配置" key="1">
      <div v-if="activeKey == 1" class="tab-con">
        <!-- <a-row align="middle">
          <a-col :span="6">显示弹窗</a-col>
          <a-col :span="18">
            <a-switch checkedChildren="开" unCheckedChildren="关" defaultChecked v-model="globalGridAttr.infoPopup" />
          </a-col>
        </a-row> -->
        <!-- <a-row align="middle">
          <a-col :span="6">节点样式</a-col>
          <a-col :span="18">
            <a-select
              style="width: 100%"
              :value="topoConfig.shapeType"
              @change="
                (val) => {
                  topoConfig.shapeType = val
                }
              "
            >
              <a-select-option :value="idx" v-for="(shape, idx) in globalGridAttr.shapeTypes" :key="idx">
                {{ shape }}
              </a-select-option>
            </a-select>
          </a-col>
        </a-row> -->
        <a-row align="middle">
          <a-col :span="6">文字颜色</a-col>
          <a-col :span="18">
            <a-input type="color" v-model="topoConfig.labelColor" style="width: 100%" />
          </a-col>
        </a-row>
        <a-row align="middle">
          <a-col :span="6">连接</a-col>
          <a-col :span="18">
            <a-input type="color" v-model="topoConfig.upColor" style="width: 100%" />
          </a-col>
        </a-row>
        <a-row align="middle">
          <a-col :span="6">关闭</a-col>
          <a-col :span="18">
            <a-input type="color" v-model="topoConfig.downColor" style="width: 100%" />
          </a-col>
        </a-row>
        <a-row align="middle">
          <a-col :span="6">未绑定</a-col>
          <a-col :span="18">
            <a-input type="color" v-model="topoConfig.unboundColor" style="width: 100%" />
          </a-col>
        </a-row>
        <a-row align="middle">
          <a-col :span="6">已绑定</a-col>
          <a-col :span="18">
            <a-input type="color" v-model="topoConfig.boundColor" style="width: 100%" />
          </a-col>
        </a-row>
        <a-row align="middle">
          <a-col :span="6">告警</a-col>
          <a-col :span="18">
            <a-input type="color" v-model="topoConfig.alarmColor" style="width: 100%" />
          </a-col>
        </a-row>
        <!--   <div v-if="!isSimple">
             <a-row align="middle" >
          <a-col :span="6">连线动画</a-col>
          <a-col :span="14">
            <a-switch
              checkedChildren="开"
              unCheckedChildren="关"
              v-model="topoConfig.edgeAni"
              @change="animationChange"
            />
          </a-col>
        </a-row>
        <a-row align="middle">
          <a-col :span="6">动画时长</a-col>
          <a-col :span="18">
            <a-input-number 
            style="width:100%"
            v-model="topoConfig.aniTime" 
            :min="3" 
            :max="10" 
            @change="aniTimeChange" />
          </a-col>
        </a-row>
        </div> -->
        <!-- <a-row align="middle">
          <a-col :span="6">背景类型</a-col>
          <a-col :span="18">
            <a-radio-group name="radioGroup" :value="topoConfig.bgType" @change="radioChange">
              <a-radio :value="'1'"> 调色板 </a-radio>
              <a-radio :value="'2'"> 图片 </a-radio>
            </a-radio-group>
          </a-col>
        </a-row>
        <a-row align="middle" v-if="topoConfig.bgType === '1'">
          <a-col :span="6">背景颜色</a-col>
          <a-col :span="18">
            <a-input
              type="color"
              v-model="topoConfig.bgColor"
              style="width: 100%"
            />
          </a-col>
        </a-row> -->
      </div>
    </a-tab-pane>
    <a-tab-pane tab="业务属性" key="2" :forceRender="false">
      <div v-if="activeKey == 2">
        <div class="colorBox">
          <span class="colorTotal">SNMP连接参数</span>
        </div>
        <a-row align="middle">
          <a-col :span="6">IP地址</a-col>
          <a-col :span="18">
            <a-input v-model="connectInfo.ip" placeholder="请填写" style="width: 100%">
              <a-tooltip slot="suffix" title="SNMP V1/V2/V3版本 必填项">
                <a-icon type="info-circle" theme="twoTone" />
              </a-tooltip>
            </a-input>
          </a-col>
        </a-row>
        <a-row align="middle">
          <a-col :span="6">端口号</a-col>
          <a-col :span="18">
            <a-input v-model="connectInfo.port" placeholder="请填写" style="width: 100%">
              <a-tooltip slot="suffix" title="SNMP V1/V2/V3版本 必填项">
                <a-icon type="info-circle" theme="twoTone" />
              </a-tooltip>
            </a-input>
          </a-col>
        </a-row>
        <a-row align="middle">
          <a-col :span="6">SNMP版本</a-col>
          <a-col :span="18">
            <a-input v-model="connectInfo.version" placeholder="请填写" style="width: 100%">
              <a-tooltip slot="suffix" title="SNMP 【V2/V3】版本 必填项（参考值V2、V3）">
                <a-icon type="info-circle" theme="twoTone" />
              </a-tooltip>
            </a-input>
          </a-col>
        </a-row>
        <a-row align="middle">
          <a-col :span="6">团体名</a-col>
          <a-col :span="18">
            <a-input v-model="connectInfo.community" placeholder="请填写" style="width: 100%">
              <a-tooltip slot="suffix" title="SNMP 【V2】版本 必填项">
                <a-icon type="info-circle" theme="twoTone" />
              </a-tooltip>
            </a-input>
          </a-col>
        </a-row>
        <a-row align="middle">
          <a-col :span="6">用户名</a-col>
          <a-col :span="18">
            <a-input v-model="connectInfo.username" placeholder="请填写" style="width: 100%">
              <a-tooltip slot="suffix" title="SNMP 【V3】版本 必填项">
                <a-icon type="info-circle" theme="twoTone" />
              </a-tooltip>
            </a-input>
          </a-col>
        </a-row>
        <a-row align="middle">
          <a-col :span="6">安全级别</a-col>
          <a-col :span="18">
            <a-input v-model="connectInfo.snmpAuthLevel" placeholder="请填写" style="width: 100%">
              <a-tooltip slot="suffix" title="SNMP 【V3】版本 必填项 安全级别：noAuthNoPriv；AuthNoPriv；AuthPriv">
                <a-icon type="info-circle" theme="twoTone" />
              </a-tooltip>
            </a-input>
          </a-col>
        </a-row>
        <a-row align="middle">
          <a-col :span="6">认证协议</a-col>
          <a-col :span="18">
            <a-input v-model="connectInfo.sAuth" placeholder="请填写" style="width: 100%">
              <a-tooltip slot="suffix" title="SNMP 【V3】版本 必填项 认证协议：MD5；SHA；SHA-256；SHA-512">
                <a-icon type="info-circle" theme="twoTone" />
              </a-tooltip>
            </a-input>
          </a-col>
        </a-row>
        <a-row align="middle">
          <a-col :span="6">认证密码</a-col>
          <a-col :span="18">
            <a-input v-model="connectInfo.sAuth_passwd" placeholder="请填写" style="width: 100%">
              <a-tooltip slot="suffix" title="SNMP 【V3】版本 必填项">
                <a-icon type="info-circle" theme="twoTone" />
              </a-tooltip>
            </a-input>
          </a-col>
        </a-row>
        <a-row align="middle">
          <a-col :span="6">加密协议</a-col>
          <a-col :span="18">
            <a-input v-model="connectInfo.spriv" placeholder="请填写" style="width: 100%">
              <a-tooltip slot="suffix" title="SNMP 【V3】版本 必填项 加密协议：DES；3DES；AES128；AES192；AES256">
                <a-icon type="info-circle" theme="twoTone" />
              </a-tooltip>
            </a-input>
          </a-col>
        </a-row>
        <a-row align="middle">
          <a-col :span="6">加密密码</a-col>
          <a-col :span="18">
            <a-input v-model="connectInfo.spriv_passwd" placeholder="请填写" style="width: 100%">
              <a-tooltip slot="suffix" title="SNMP 【V3】版本 必填项">
                <a-icon type="info-circle" theme="twoTone" />
              </a-tooltip>
            </a-input>
          </a-col>
        </a-row>
        <a-row align="middle">
          <a-col :span="6">OID示例</a-col>
          <a-col :span="18">
            <a-input v-model="connectInfo.oid" placeholder="请填写" style="width: 100%">
              <a-tooltip slot="suffix" title="OID 必填">
                <a-icon type="info-circle" theme="twoTone" />
              </a-tooltip>
            </a-input>
          </a-col>
        </a-row>
        <div class="colorBox">
          <span class="colorTotal">告警模板</span>
        </div>
        <a-row align="middle">
          <a-col :span="6">告警模板</a-col>
          <a-col :span="18">
            <a-select style="width: 100%" placeholder="请选择" v-model="globalGridAttr.alarmTemplateId"  allowClear>
              <a-select-option :value="item.id" v-for="item in alarmTemplates" :key="item.id">
                {{ item.name }}
              </a-select-option>
            </a-select>
          </a-col>
        </a-row>
      </div>
    </a-tab-pane>
  </a-tabs>
</template>

<script>
import FlowGraph from '../../../graph'
// 连线图案组件
import LineDash from '../LineDash.vue'
import { getAction } from '@/api/manage'
export default {
  name: 'Index',
  components: {
    LineDash,
  },
  props: {
    globalGridAttr: {
      type: Object,
      default: null,
      required: true,
    },
    productId: {
      type: String,
      default: '',
      required: true,
    },
  },
  data() {
    return {
      activeKey: '1',
      topoConfig: {},
      isSimple: false,
      connectInfo: {},
      alarmTemplates: [],
      alarmTemplateId: '',
    }
  },
  computed: {
    topoBgImg() {
      if (this.topoConfig.bgimg) {
        return this.topoConfig.bgimg
        // return window._CONFIG['staticDomainURL'] + '/' + this.topoConfig.bgimg
      } else {
        return ''
      }
    },
  },
  created() {
    this.isSimple = window.config.simpleModel !== 0
    this.topoConfig = this.globalGridAttr.topoConfig
    this.connectInfo = this.globalGridAttr.connectInfo
    this.getAlarmTemplates()
  },
  mounted() {},

  methods: {
    alarmTemplateChange(e) {
      this.globalGridAttr.alarmTemplateId = e
      console.log(`baocuna == ${e}`)
    },
    getAlarmTemplates() {
      let param = {
        productId: this.productId,
        pageNo: 1,
        pageSize: -1,
      }
      getAction('/alarm/alarmTemplate/list', param).then((res) => {
        if (res.success && res.result) {
          this.alarmTemplates = res.result.records
        }
      })
    },
    aniTimeChange(e) {},
    animationChange(e) {
      this.topoConfig.edgeAni = e
    },
    edgeStatusChange(e) {
      this.topoConfig.edgeStatus = e
    },
    flickerChange(e) {
      this.globalGridAttr.flicker = e
    },
    changeImgList(path, status) {
      if (!!path) {
        this.globalGridAttr.bgImgList = path.split(',')
        let imgListLength = this.globalGridAttr.bgImgList.length
        let imgUrl = this.globalGridAttr.bgImgList[imgListLength - 1]
        // this.topoConfig.bgimg = imgUrl
      } else {
        this.globalGridAttr.bgImgList = []
        // this.globalGridAttr.bgimg = ''
        // const { graph } = FlowGraph
        // graph.updateBackground({
        //   color: this.topoConfig.bgColor,
        // })
      }
    },
    changeImg(fileUrl) {
      if (this.topoConfig.bgType === '2') {
        this.topoConfig.bgimg = fileUrl
      }
    },
    radioChange(e) {
      let val = e.target.value
      this.topoConfig.bgType = val
    },
    topoBgChange(e) {
      //  const { graph } = FlowGraph
      //   graph.updateBackground({
      //     color: this.topoConfig.bgColor,
      //   })
    },
  },
}
</script>

<style lang="less" scoped>
.bg-img {
  width: 100%;
  height: 100%;
  cursor: pointer;
}
.tab-con {
  height: 100%;
  overflow-y: auto;
}
.ant-tabs {
  height: 100%;
}
::v-deep .ant-upload-list-picture-card-container {
  width: 100% !important;
}

::v-deep .ant-upload-list-picture-card .ant-upload-list-item {
  width: 100% !important;
}

::v-deep .ant-upload.ant-upload-select-picture-card {
  width: 100% !important;
}
.colorBox {
  margin-bottom: 18px;
}

.colorTotal {
  padding-left: 7px;
  border-left: 4px solid #1e3674;
}
</style>