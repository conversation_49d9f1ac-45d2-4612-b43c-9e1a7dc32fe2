<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formCode">
      <a-form :form="form" slot="detail" :labelCol="labelCol" :wrapperCol="wrapperCol">

        <div  key='device-items'>
           <!--设备参数---start-->
          <div class="colorBox">
            <span class="colorTotal">设备参数</span>
          </div>
          <a-row :gutter="32">
            <a-col :span='12'>
              <a-form-item label="产品名称:">
                <a-tree-select
                  v-decorator="['productId', validatorRules.productId]"
                  :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                  :allow-clear="true"
                  show-search
                  tree-node-filter-prop='title'
                  placeholder="请选择产品名称"
                  @select="selectProductName"
                  @change="changeProductName"
                  :tree-data="productList"
                  :tree-default-expanded-keys='[productNodeCatId]'
                  tree-icon
                >
                </a-tree-select>
              </a-form-item>
            </a-col>
            <a-col :span='12'>
              <a-form-item label="设备名称" >
                <a-input
                  v-decorator="['name', validatorRules.name]"
                  placeholder="请输入设备名称"
                  :allowClear="true"
                  autocomplete="off"
                />
              </a-form-item>
            </a-col>
            <a-col :span='12'>
              <a-form-item label="设备标识">
                <a-input
                  v-decorator="['deviceCode', validatorRules.deviceCode]"
                  placeholder="请输入设备唯一标识"
                  :allowClear="true"
                  autocomplete="off"
                  :disabled="canEditDevCode"
                />
              </a-form-item>
            </a-col>
            <a-col :span='12'>
              <a-form-item label="占用容量">
                <a-input
                  v-decorator="['capacity', validatorRules.capacity]"
                  placeholder="请输入放置设备所要占用的容量"
                  :allow-clear="true"
                  autocomplete="off"
                  :maxLength="10"
                  suffix="U位"
                />
              </a-form-item>
            </a-col>
            <a-col :span='12'>
              <a-form-item label="规格">
                <a-input
                  v-decorator="['specifications', validatorRules.specifications]"
                  placeholder="请输入设备的长宽高 (例:120*90*80)"
                  :allowClear="true"
                  autocomplete="off"
                  suffix="mm"
                />
              </a-form-item>
            </a-col>
            <a-col :span='12'>
              <a-form-item label="所属单位">
                <a-tree-select v-decorator="['momgDeptId']" :getPopupContainer="(node) => node.parentNode"
                  tree-node-filter-prop="title" :replaceFields="replaceFields" :treeData="departTreeData" show-search
                  :searchValue="bsearchKey" style="width: 100%"
                  :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }" placeholder="请选择所属单位" allow-clear
                  @change="onChangeTree" @search="onSearch">
                </a-tree-select>
              </a-form-item>
            </a-col>
            <a-col :span='12'>
              <a-form-item label="管理人">
                <j-select-user-by-dep v-decorator="['manager', {initialValue: '', rules: validatorRules.manager.rules} ]" :multi='false' btnName="人员"></j-select-user-by-dep>
              </a-form-item>
            </a-col>
            <a-col :span='12'>
              <a-form-item label="运维人">
                <j-select-user-by-dep v-decorator="['operator',{initialValue: '', rules: validatorRules.operator.rules}]" :multi='false' btnName="人员"></j-select-user-by-dep>
              </a-form-item>
            </a-col>
            <a-col :span='12'>
              <a-form-item label="设备说明">
                <a-textarea v-decorator="['description', validatorRules.description]"
                            :autoSize='{minRows:2,maxRows:4}' placeholder="请输入设备说明"
                            :allowClear="true" />
              </a-form-item>
            </a-col>
            <a-col :span='12'>
              <a-form-item label="是否重点关注">
                <a-switch
                  v-decorator="['isFocus', { valuePropName: 'checked',initialValue: false,}]" checkedChildren="是" unCheckedChildren="否" @change="changeIsFocus">
                </a-switch>
              </a-form-item>
            </a-col>
            <a-col :span='12' v-if='displaySyncAssets'>
              <a-form-item label="同步至资产">
                <a-switch v-model='syncAssets' :default-checked='true' checkedChildren="是" unCheckedChildren="否" @change='syncAssetsFun'></a-switch>
              </a-form-item>
            </a-col>

          </a-row>
          <!--设备参数---end-->

          <!-- 设备位置--start -->
          <div class="colorBox">
            <span class="colorTotal">设备位置</span>
          </div>
          <a-row :gutter="32">
            <a-col :span='12'>
              <a-form-item label="机房" >
                <a-tree-select
                  v-decorator="['roomId', validatorRules.roomId]"
                  :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                  :allow-clear="true"
                  show-search
                  tree-node-filter-prop='title'
                  placeholder="请选择机房"
                  :replace-fields="{children:'children', title:'name', key:'id', value: 'id',icon:'icon' }"
                  @select="selectRoom"
                  @change="changeRoom"
                  :tree-data="roomList"
                  tree-icon
                >
                </a-tree-select>
              </a-form-item>
              <a-form-item label="U位">
                <a-select
                  v-decorator="['layerPool',{rules:  [{required: roomNodeType==='room', validator: this.validateLayerPool, trigger: 'blur'}]}]"
                  :allow-clear='true'
                  :maxTagCount='4'
                  :show-search='true'
                  mode='multiple'
                  option-filter-prop='label'
                  placeholder='请选择U'
                >
                  <a-select-option v-for='(item,index) in layerPoolList' :key='"layerPool_"+index' :label='item' :value='item'>
                    {{ item}}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span='12'>
              <a-form-item label="机柜">
                <a-select
                  v-decorator="['cabinetId',{rules: [{ required: roomNodeType==='room', validator: this.validateCabinet, trigger: 'blur' }]}]"
                  :allow-clear='true'
                  :show-search='true'
                  option-filter-prop='label'
                  placeholder='请选择机柜'
                  @change='changeCabinet'
                >
                  <a-select-option v-for='item in cabinetList' :key='"cabinet_"+item.id' :label='item.name' :value='item.id'>
                    {{ item.name }}
                  </a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="所在位置">
                <a-input
                  v-decorator="['location', validatorRules.location]"
                  placeholder="请填写所在位置"
                  :allowClear="true"
                  autocomplete="off"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <!-- 设备位置---end -->
        </div>

        <!--连接参数---start-->
        <div v-if="collectInfoList.length > 0">
          <div v-for="(item, index) in collectInfoList" :key="index" >
            <div class="colorBox">
              <span class="colorTotal">{{ item.key }}连接参数</span>
            </div>
            <a-row :gutter="32">
                <a-col :span="12" v-for="(item1, idx) in item.value" :key="idx">
                  <a-form-item :label="item1.displayName">
                    <div style='white-space: nowrap'>
                      <a-input
                        v-if="item1.controlType == 'inputString'"
                        v-decorator="[
                            item1.connectName + '_' + idx + '_' + index,
                            {
                              initialValue: item1.connectValue,
                              rules: [{required: item1.isRequired=='1'?true:false,
                              validator:(rule, value, callback)=>validateConnectValue(rule, value, callback,item1.validatorRule,item1.displayName) }
                              ],
                            },
                          ]"
                        :placeholder="'请输入'+item1.displayName"
                        :allowClear="true"
                        autocomplete="off"
                        @change="changeConParam($event, index, idx)"
                      />
                      <j-dict-select-tag
                        v-if="item1.controlType == 'inputSelect'"
                        v-decorator="[
                            item1.connectName + '_' + idx + '_' + index,
                            {
                              initialValue: item1.connectValue,
                              rules: [{ required: item1.isRequired=='1'?true:false, message: '请输入' + item1.displayName }],
                            },
                          ]"
                        :dictCode="item1.dictType"
                        :placeholder="'请选择'+item1.displayName"
                        :trigger-change="true"
                        @change="changeConParam1($event, index, idx)"
                        :allowClear="true"
                      />
                      <a-input-password
                        v-if="item1.controlType == 'inputPassword'"
                        v-decorator="[
                            item1.connectName + '_' + idx + '_' + index,
                            {
                              initialValue: item1.connectValue,
                              rules: [{ required: item1.isRequired=='1'?true:false,
                               validator:(rule, value, callback)=>validateConnectValue(rule, value, callback,item1.validatorRule,item1.displayName) }],
                            },
                          ]"
                        :placeholder="'请输入'+item1.displayName"
                        :allowClear="true"
                        autocomplete="off"
                        @change="changeConParam($event, index, idx)"
                      >
                      </a-input-password>
                      <a-input-number
                        v-if="item1.controlType == 'inputNumber'"
                        v-decorator="[
                            item1.connectName + '_' + idx + '_' + index,
                            {
                              initialValue: item1.connectValue,
                              rules: [{ required: item1.isRequired=='1'?true:false,
                              validator:(rule, value, callback)=>validateConnectValue(rule, value, callback,item1.validatorRule,item1.displayName)}],
                            },
                          ]"
                        :placeholder="'请输入'+item1.displayName"
                        :allowClear="true"
                        autocomplete="off"
                        @change="changeConParam1($event, index, idx)"
                      />
                      <a-popover title="备注" v-if="item1.description != null && item1.description != ''">
                        <template slot="content">
                          <p>{{ item1.description }}</p>
                        </template>
                        <a-icon type="question-circle" theme="twoTone" style="font-size: 18px; line-height: 45px;margin-left: 10px" />
                      </a-popover>
                    </div>
                  </a-form-item>
                </a-col>
            </a-row>
          </div>
        </div>
        <!--连接参数---end-->

        <!--资产信息---start-->
        <div v-if="syncAssets"  key='assets-items'>
          <div class="colorBox">
            <span class="colorTotal">资产信息</span>
          </div>
          <a-row :gutter="32">
            <a-col :span='12'>
              <a-form-item label="资产类型">
                <j-tree-select-expand
                  :disabled='true'
                  v-decorator="['assetsCategoryId', validatorRules.assetsCategoryId]"
                  placeholder="请选择资产类型"
                  dict="cmdb_assets_category,category_name,id"
                  pidField="parent_id"
                  condition='{"delflag":0,"category_state":"0"}'
                  pidValue="0"
                />
              </a-form-item>
              <a-form-item label="供应商">
                <a-select
                  :getPopupContainer="(node) => node.parentNode"
                  :allowClear="true"
                  v-decorator="['producerId', validatorRules.producerId]"
                  placeholder="请选择供应商"
                  @change='changeProducer'
                >
                  <a-select-option v-for="item in supplierList" :key="item.id" :value="item.id"
                  >{{ item.name }}
                  </a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="入库日期">
                <j-date
                  placeholder="请选择入库日期"
                  v-decorator="['storageTime',{
                     rules: [{ required: assetsInfo.isWarning==='1', message: '请选择入库日期' }]
                  }]"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span='12'>
              <a-form-item label="资产编号:">
                <a-input
                  :disabled="assetsAuto"
                  v-decorator="['assetsCode',{initialValue: autoAssetsCode,rules: validatorRules.assetsCode.rules}]"
                  :allowClear="true"
                  autocomplete="off"
                  placeholder="请输入资产编号"
                />
              </a-form-item>
              <a-form-item class="two-words" label="型号">
                <a-select   :getPopupContainer="(node) => node.parentNode"
                            v-decorator="['assetsModel',validatorRules.assetsModel]"
                            show-search
                            mode="SECRET_COMBOBOX_MODE_DO_NOT_USE"
                            :autoClearSearchValue="true"
                            :allowClear="true"
                            placeholder="请选择或输入型号">
                  <a-select-option  v-for='(item,index) in assetsModelList' :key='"assetesModel_"+index' :value='item.model' :label='item.model'>
                    {{item.model}}
                  </a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="是否预警">
                <a-radio-group v-decorator="['isWarning',{initialValue:'0'}]"  @change='changeWarning($event)'>
                  <a-radio value="1" key='1'>是</a-radio>
                  <a-radio value="0" key='0'>否</a-radio>
                </a-radio-group>
              </a-form-item>
            </a-col>
            <a-col :span='12' v-if="assetsInfo.isWarning === '1'">
                <a-form-item label="质保开始日期">
                  <j-date
                    placeholder="请选择质保开始日期"
                    v-decorator="['startQualityTime', validatorRules.startQualityTime]"
                    :trigger-change="true"
                    style="width: 100%"
                  />
                </a-form-item>
                <a-form-item label="保修单位">
                  <a-input rules: v-decorator="['repairFac', validatorRules.repairFac]" placeholder='请输入保修单位'
                           :allowClear='true' autocomplete='off' style='width: 100%' />
                </a-form-item>
                <a-form-item label="保修联系人" >
                  <a-input
                    v-decorator="['warrantyConnect', validatorRules.warrantyConnect]"
                    placeholder="请输入保修联系人"
                    :allowClear="true"
                    autocomplete="off"
                    style="width: 100%"
                  />
                </a-form-item>
            </a-col>
            <a-col :span='12' v-if="assetsInfo.isWarning === '1'">
                <a-form-item label="质保期限(月)">
                  <a-input-number
                    :min="1"
                    :max="1000"
                    v-decorator="['qualityTerm', validatorRules.qualityTerm]"
                    placeholder="请输入质保期限(月)"
                    style="width: 100%"
                  />
                </a-form-item>
                <a-form-item label="保修单位电话" >
                  <a-input
                    v-decorator="['repairPhone', validatorRules.repairPhone]"
                    placeholder="请输入保修单位电话"
                    :allowClear="true"
                    autocomplete="off"
                    style="width: 100%"
                  />
                </a-form-item>
                <a-form-item label="保修人电话">
                  <a-input
                    v-decorator="['warrantyPhone', validatorRules.warrantyPhone]"
                    placeholder="请输入保修人电话"
                    :allowClear="true"
                    autocomplete="off"
                    style="width: 100%"
                  />
                </a-form-item>
            </a-col>
          </a-row>
        </div>
        <!--资产信息---end-->

        <!--附加字段---start-->
        <div v-if="syncAssets&&additionalFieldList.length>0">
          <div class="colorBox">
            <span class="colorTotal">附加字段</span>
          </div>
          <a-row :gutter="32">
            <a-col :span='12' v-for="item in additionalFieldList" :key="'additionalField_'+item.id">
              <a-form-item :label="item.name" >
                <a-input-number
                  v-if="item.type == '计数器'"
                  :allowClear="true"
                  autocomplete="off"
                  :min="-99999999999"
                  :max="99999999999"
                  :step="1"
                  :precision="10"
                  v-decorator="[
                  `addCodes[${item.id}]`,
                  {
                    initialValue: item.value,
                    rules: [
                      {
                        required: item.isInput === 1,
                        validator:(rule, value, callback)=>validateConnectValue(rule, value, callback,null,item.name)
                      }
                    ]
                  }
                ]"
                  :placeholder="'请输入'+item.name"
                />
                <a-input
                  v-else-if="item.type == '单行文本'"
                  :allowClear="true"
                  autocomplete="off"
                  v-decorator="[
                  `addCodes[${item.id}]`,
                  {
                    initialValue: item.value,
                    rules: [
                      {
                        required: item.isInput === 1,
                        validator:(rule, value, callback)=>validateConnectValue(rule, value, callback,null,item.name)
                      }
                    ]
                  }
                ]"
                  :placeholder="'请输入'+item.name"
                  type="text"
                />
                <a-textarea
                  :allowClear="true"
                  autocomplete="off"
                  v-else-if="item.type == '文本文档'||item.type == '多行文本'"
                  v-decorator="[
                  `addCodes[${item.id}]`,
                  {
                    initialValue: item.value,
                    rules: [
                      {
                        required: item.isInput === 1,
                        validator:(rule, value, callback)=>validateConnectValue(rule, value, callback,null,item.name)}
                    ]
                  }
                ]"
                  :placeholder="'请输入'+item.name"
                />
                <j-dict-select-tag
                  v-else-if="item.type == '下拉框'"
                  type="list"
                  v-decorator="[
                  `addCodes[${item.id}]`,
                  {
                    validateTrigger: ['change', 'blur'],
                    initialValue: item.value,
                    rules: [
                      {
                        required: item.isInput === 1,
                        whitespace: true,
                        message: '请选择' + item.name,
                      }
                    ]
                  }
                ]"
                  :trigger-change="true"
                  :dictCode="item.dictType"
                  :placeholder="'请选择'+item.name"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </div>
        <!--附加字段---end-->
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
import {ValidateOptionalFields,ValidateRequiredFields} from '@/utils/rules.js'
import { httpAction, getAction } from '@/api/manage'
import pick from 'lodash.pick'
import { queryPostionTreeList } from '@api/device'
import { phoneValidator} from '@/mixins/phoneValidator'
import { queryConfigureDictItem } from '@api/api'
import JSelectUserByDepNoRight from '@/components/flowable/JSelectUserByDepNoRight'
import JSelectUserByDep from '@comp/jeecgbiz/JSelectUserByDep.vue'
export default {
  name: 'DeviceInfoForm',
  mixins: [phoneValidator],
  components: { JSelectUserByDep, JSelectUserByDepNoRight },
  props: {
    // 流程表单data
    formData: {
      type: Object,
      default: () => {
      },
      required: false,
    },
    // 表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false,
    },
    // 表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false,
    },
    scanTaskObject: {
      type: Object
    }
  },
  data() {
    return {
      form: this.$form.createForm(this),
      confirmLoading: false,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 24 },
        md: { span: 6 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 24 },
        md: { span: 16 },
      },
      formCode: false,
      replaceFields: {
        children: 'children',
        title: 'deptName',
        key: 'deptId',
        value: 'deptId',
      },
      departTreeData: [],
      bsearchKey: '',
      defaultProductExpandedKeys: [],//产品分类默认展开的节点
      productNodeCatId: '',//记录最后一级产品分类id
      productNodeType: undefined,//记录当前产品名称所选节点类型（产品分类、产品）
      productList: [],//产品分类及产品树结构数据
      collectInfoList: [],//连接参数
      canEditDevCode: false,//控制设备标识是否可编辑
      roomNodeType: '',//房间物理位置所选的最后一级数据类型（notRoom/room）
      roomList: [],//房间物理位置数据
      cabinetList: [],//房间里的机柜
      layerPoolList: [],//U下拉数据
      hisCabinetId: undefined,//编辑情况下，记录开始的机柜id
      hisLayerPoolList: [],//编辑情况下，记录开始U的下拉数据
      displaySyncAssets: false,////是否显示同步至资产按钮
      syncAssets: false,//是否同步至资产
      additionalFieldList: [],//附加字段
      supplierList: [],//供应商下拉数据
      assetsModelList: [],//资产型号下拉数据
      autoAssetsCode: '',//自动生成的资产编号
      assetsAuto: window._CONFIG['assetsAuto'],//配置：资产编号是否自动生产
      validatorRules: {
        productId: {
          rules: [
            { required: true, validator: this.validateProduct, trigger: 'blur' }
          ]
        },
        name: {
          rules: [
            { required: true,validator: (rule, value, callback) =>ValidateRequiredFields(rule, value, callback,'名称',50,2) }
          ]
        },
        deviceCode: {
          rules: [
            {
              required: true,
              pattern: /^[a-zA-Z0-9-.:]{4,32}$/,
              message: '可包含大小写字母、数字、英文横线、英文句号、英文冒号,4-32个字符之间'
            }
          ]
        },
        manager: {
          rules: [
            { required: false, message: '请选择管理人' }
          ]
        },
        operator: {
          rules: [
            { required: false, message: '请选择运维人' }
          ]
        },
        capacity: {
          rules: [
            {
              required: false, pattern: /^[1-9]{1}[0-9]*$/, message: '容量可为空、或请输入≥1的整数'
            }
          ]
        },
        specifications: {
          rules: [
            {
              required: false, pattern: /^[1-9]\d*\*[1-9]\d*\*[1-9]\d*$/, message: '请输入正确格式，例：120*90*80'
            }
          ]
        },
        roomId: {
          rules: [
            { required: false, validator: this.validateRoom, trigger: 'blur' }
          ]
        },
        description: {
          rules: [
            { required: false, validator: (rule, value, callback) =>ValidateOptionalFields(rule, value, callback,'设备说明',200) },
           ]
        },
        location: {
          rules: [
            {
              required: false,
              validator: (rule, value, callback) => ValidateOptionalFields(rule, value, callback, '所在位置', 25)
            }
          ]
        },
        assetsCode: {
          rules: [
            { required: true,validator: (rule, value, callback) =>ValidateRequiredFields(rule, value, callback,'资产编号',20,2)},            ,
          ]
        },
        producerId: {
          rules: [
            { required: true, message: '请选择供应商' }
          ]
        },
        assetsModel: {
          rules: [
            { required: true, validator: (rule, value, callback) =>ValidateRequiredFields(rule, value, callback,'型号',100,2) }
          ]
        },
        assetsCategoryId: {
          rules: [
            { required: true, message: '请选择资产类型' }
          ]
        },
        startQualityTime: {
          rules: [
            { required: true, message: '请选择质保开始日期' },
          ]
        },
        qualityTerm: {
          rules: [
            { required: true, message: '请输入质保期限(月)' },
          ]
        },
        repairFac: {
          rules: [
            { required: true, pattern: /^[\u4E00-\u9FA50-9a-zA-Z]{1,50}$/,  message: '保修单位由50个以内的中英文、数字组成' }
          ]
        },
        repairPhone: {
          rules: [
            { required: true, validator: this.requiredPhone}
          ]
        },
        warrantyConnect: {
          rules: [
            { required: true,pattern: /^[\u4E00-\u9FA5]{1,10}$/, message: '保修联系人由10个以内的汉字组成' },
          ],
        },
        warrantyPhone: {
          rules: [
            { required: true, validator: this.requiredPhone }
          ]
        }
      },
      url: {
        add: '/device/deviceInfo/addDevice',
        edit: '/device/deviceInfo/editDevice',
        queryById: '/device/deviceInfo/queryById',
        queryAllProduct: '/assetscategory/assetsCategory/selectTree', //获取所有的产品
        queryCollectInfoList: '/device/deviceInfo/selectConnectInfo',//1、新增，通过产品id.获取连接参数；2、编辑，通过设备id.获取连接参数
        cabinet: '/topo/room/cabinet',//根据机房id，获取机柜
        layerPool: '/topo/cabinet/getFreeLayerList',//根据机柜id，获取机柜剩余u
        supplier: '/supplier/cmdbSupplier/queryAllSupplier',//获取供应商下拉数据
        findCodeName: '/extendField/extendField/findCodeName',//切换产品时，通过产品分类/资产类型的id获取附加字段
        findCodeValue: '/extendField/extendField/findCodeValue',//编辑：通过产品分类/资产类型的id,及资产信息id获取附加字段
        queryAssetsAndPositionInfo: '/device/deviceInfo/selectAssetsAndPositionInfo',//编辑，根据设备id，获取资产信息和设备位置信息
        generateCode: '/CodeRuleSetting/codeRuleSetting/generateCodeByBiz', //自动生成资产编号
        querAssetsModelList: '/itInnovate/cmdbItInnovate/list',//根据产品类型/资产类型，供应商获取资产型号下拉数据
        // getPlatformCode: '/data/reportAndConverge/getPlatformCode',
      },
      deviceInfo: {},
      assetsInfo: {}
    }
  },
  created() {
    this.getDepartList()
  },
  methods: {
    changeIsFocus(value) {
      //console.log('value===',value)
    },
    // getPlatformCode(id) {
    //   getAction(this.url.getPlatformCode).then((res) => {
    //     if (res.success) {
    //       if (res.result == id) {
    //         this.formCode = true
    //       }
    //     }
    //   })
    // },
    getDepartList() {
      getAction('/sys/sysDepart/queryAllTree').then((res) => {
        for (let i = 0; i < res.length; i++) {
          let temp = res[i]
          this.departTreeData.push(temp)
        }
      })
    },
    onChangeTree(value) {
    },
    onSearch(e) {
      this.bsearchKey = e
    },

    /***************设备新增、编辑初始化***************/
    add() {
      this.edit({})
    },
    edit(record) {
      // this.getPlatformCode(record.platformCode)
      this.confirmLoading = true
      this.deviceInfo = JSON.parse(JSON.stringify(record))
      Promise.all([this.queryAllProduct(), this.loadRoomTree(), this.getAllSupplier()]).then((res) => {
        let pass = res.every((item, index) => {
          if (!item.success) {
            this.$message.warning(item.message)
            this.confirmLoading = false
          }
          return item.success == true
        })
        if (pass) {
          this.visible = true
          this.form.resetFields()
          this.canEditDevCode = record.id ? true : false
          this.productNodeType = record.id ? 'product' : undefined
          //新增
          if (!record.id) {
            this.assetsInfo["isWarning"] = '0'
            this.autoAssetsCode = ''

            if (this.assetsAuto) {
              Promise.all([this.getGenerateAssetsCode(), this.getConfigDictAboutDisSyncAssets(), this.getConfigDictAboutSyncAssets()]).then((res1) => {
                this.confirmLoading = false
              }).catch(() => {
                this.confirmLoading = false
              })
            } else {
              Promise.all([this.getConfigDictAboutDisSyncAssets(), this.getConfigDictAboutSyncAssets()]).then((res1) => {
                this.confirmLoading = false
              }).catch(() => {
                this.confirmLoading = false
              })
            }
          }
          //编辑
          else if (record.id) {
            this.deviceInfo = JSON.parse(JSON.stringify(record))
            this.productNodeType = 'product'
            this.productNodeCatId = record.categoryId
            this.deviceInfo.momgDeptId = record.momgDeptId || undefined
            this.deviceInfo.isFocus = record.isFocus == 1 ? true : false
            // console.log('this.deviceInfo.isFocus===',this.deviceInfo.isFocus)
            this.$nextTick(() => {
              this.form.setFieldsValue(pick(this.deviceInfo,
                'productId', 'name', 'deviceCode', 'manager', 'operator', 'isFocus', 'description', 'momgDeptId', 'capacity', 'location',
                'specifications'))
            })

            Promise.all([this.getConfigDictAboutDisSyncAssets(), this.getCollectInfoListByDeviceId(record.id),
              this.getAssetsAndPositionInfo(record.id)]).then((res2) => {
              this.confirmLoading = false
            }).catch(() => {
              this.confirmLoading = false
            })
          }
        }
      })
    },
    /*新增：获取配置字典，设置是否默认同步、关联设备*/
    getConfigDictAboutSyncAssets() {
      return new Promise((resolve, reject) => {
        queryConfigureDictItem({
          parentCode: 'assetDeviceAutosyn',
          childCode: 'syncAsset'
        }).then((res) => {
          if (res.success) {
            this.syncAssets = res.result == '1' ? true : false
            resolve({ success: true, message: res.message })
          } else {
            this.syncAssets = false
            resolve({ success: true, message: '操作成功' })
          }
        }).catch((err) => {
          this.syncAssets = false
          resolve({ success: true, message: '操作成功' })
        })
      })
    },
    /*获取配置字典，是否显示同步至资产切换按钮*/
    getConfigDictAboutDisSyncAssets() {
      return new Promise((resolve, reject) => {
        queryConfigureDictItem({
          parentCode: 'assetDeviceDisplayAutosyn',
          childCode: 'displaySyncAsset'
        }).then((res) => {
          if (res.success) {
            this.displaySyncAssets = res.result == '1' ? true : false
            resolve({ success: true, message: res.message })
          } else {
            this.displaySyncAssets = false
            resolve({ success: true, message: '操作成功' })
          }
        }).catch((err) => {
          this.displaySyncAssets = false
          resolve({ success: true, message: '操作成功' })
        })
      })
    },
    /*获取产品名称的下拉框数据源*/
    queryAllProduct() {
      return new Promise((resolve, reject) => {
        getAction(this.url.queryAllProduct)
          .then((res) => {
            if (res.success) {
              this.productList = res.result
              this.setProductNodeIcon(this.productList, '')
              resolve({ success: true, message: res.message })
            } else {
              reject({ success: false, message: res.message })
            }
          }).catch((err) => {
          reject({ success: false, message: err.message })
        })
      })
    },
    /*设置产品分类、产品节点的图标*/
    setProductNodeIcon(data, pid = '') {
      if (data.length && data.length > 0) {
        for (let i = 0; i < data.length; i++) {
          data[i] = {
            ...data[i],
            categoryId: data[i].type != 'product' ? data[i].value : pid,
            isLeaf: data[i].children.length == 0,
            icon:
              data[i].type != 'product' ? (
                <a-icon type='folder' style='color:#409eff' />
              ) : (
                <a-icon type='file' style='color:#409eff' />
              ),
          }
          if (data[i].children.length > 0) {
            this.setProductNodeIcon(data[i].children, data[i].value)
          }
        }
      }
    },
    /*获取机房树*/
    loadRoomTree() {
      return new Promise((resolve, reject) => {
        var that = this
        that.roomList = []
        queryPostionTreeList().then((res) => {
          if (res.success) {
            if (res.result && res.result.length > 0) {
              that.roomList = res.result
              that.setRoomNodeIcon(that.roomList)
              resolve({ success: true, message: res.message })
            } else {
              resolve({ success: true, message: res.message })
            }
          } else {
            reject({ success: false, message: res.message })
          }
        }).catch((err) => {
          reject({ success: false, message: err.message })
        })
      })
    },
    /*设置机房、非机房节点的图标*/
    setRoomNodeIcon(data) {
      if (data.length && data.length > 0) {
        for (let i = 0; i < data.length; i++) {
          data[i].icon = data[i].type === 'room' ?
            (<a-icon type='home' style='color:#409eff' />) :
            (<a-icon type='environment' style='color:#409eff' />)
          data[i].isLeaf = !data[i].children || data[i].children.length === 0 ? true : false
          if (data[i].children && data[i].children.length > 0) {
            this.setRoomNodeIcon(data[i].children)
          }
        }
      }
    },

    /*获取供应商下拉数据*/
    getAllSupplier() {
      this.supplierList = []
      return new Promise((resolve, reject) => {
        getAction(this.url.supplier).then((res) => {
          if (res.success) {
            this.supplierList = res.result ? res.result : []
            resolve({ success: true, message: res.message })
          } else {
            reject({ success: false, message: res.message })
          }
        }).catch((err) => {
          reject({ success: false, message: err.message })
        })
      })
    },

    /*新增时，若配置了自动生成资产编号，需要调用此方法*/
    getGenerateAssetsCode() {
      return new Promise((resolve, reject) => {
        getAction(this.url.generateCode, { bizName: 'asset_code_rule' }).then((res) => {
          if (res.success) {
            this.autoAssetsCode = res.result
            resolve({ success: true, message: res.message })
          } else {
            this.$message.warning(res.message)
            reject({ success: false, message: res.message })
          }
        }).catch((err) => {
          this.$message.warning(err.message)
          reject({ success: false, message: err.message })
        })
      })
    },
    /*编辑情况下，通过设备id.获取连接参数*/
    getCollectInfoListByDeviceId(deviceId) {
      return new Promise((resolve, reject) => {
        getAction(this.url.queryCollectInfoList, { deviceId: deviceId }).then((res) => {
          if (!!res) {
            this.collectInfoList = res
            resolve({ success: true, message: '请求成功' })
          } else {
            reject({
              success: false,
              message: '请求失败'
            })
          }
        }).catch((err) => {
          reject({
            success: false,
            message: '请求失败'
          })
        })
      })
    },

    /*获取资产信息、设备物理位置信息*/
    getAssetsAndPositionInfo(deviceId) {
      this.syncAssets = false
      return new Promise((resolve, reject) => {
        getAction(this.url.queryAssetsAndPositionInfo, { deviceId: deviceId }).then((res) => {
          if (res.success) {
            Promise.all([this.getDevicePositionInfo(res.result.cabinet2device),
              this.getAdditionalFieldByCarIdAndAssetsId(res.result.assetsInfo)]).then((res1) => {
              let result = res1.every((item, index) => {
                return item.success == true
              })
              if (result) {
                if (res.result.assetsInfo && Object.keys(res.result.assetsInfo)) {
                  this.assetsInfo = JSON.parse(JSON.stringify(res.result.assetsInfo))
                  this.autoAssetsCode = this.assetsInfo.assetsCode
                  this.getAllSupplier().then((res) => {
                    if (res.success) {
                      this.getAssetsModelList(this.assetsInfo.assetsCategoryId, this.assetsInfo.producerId)
                    } else {
                      this.$message.warning(res.message)
                    }
                  }).catch((err) => {
                    this.$message.warning(err.message)
                  })
                }
                //回显资产基本信息
                this.setAssetsInfo(res.result.assetsInfo)
                resolve({ success: true, message: '请求成功' })
              } else {
                this.$message.warning(res.message)
                reject({ success: false, message: '请求失败' })
              }
            }).catch((err1) => {
              this.$message.warning(err1.message)
              reject({ success: false, message: err1.message })
            })
          } else {
            this.$message.warning(res.message)
            reject({ success: false, message: res.message })
          }
        }).catch((err) => {
          this.$message.warning(err.message)
          reject({ success: false, message: err.message })
        })
      })
    },
    /*编辑：处理回显设备物理位置信息*/
    getDevicePositionInfo(positionInfo) {
      this.hisCabinetId = undefined
      this.hisLayerPoolList = []
      return new Promise((resolve, reject) => {
        if (positionInfo && Object.keys(positionInfo)) {
          let roomId = positionInfo.roomId
          if (roomId) {
            this.getCabinetList(roomId).then((res) => {
              let cabinetId = positionInfo.cabinetId
              if (cabinetId) {
                this.roomNodeType = 'room'
                this.hisCabinetId = cabinetId//记录开始的机柜id，用于机柜的校验逻辑判断
                let layerPool = positionInfo.layerPool ? positionInfo.layerPool : []
                this.hisLayerPoolList = JSON.parse(JSON.stringify(layerPool))

                //加载u，判断该机房下是否有其他空闲u，若有，将回显数据加入u下拉数据中，若没有，下拉数据直接为回显数据
                this.getLayerPoolList(cabinetId).then((res1) => {
                  if (this.hisLayerPoolList.length > 0) {
                    this.hisLayerPoolList.map((item) => {
                      this.layerPoolList.push(item)
                    })
                    this.layerPoolList = this.layerPoolList.sort((a, b) => {
                      return parseInt(a) - parseInt(b)
                    })
                  }
                  let posInfo = {
                    roomId: roomId,
                    cabinetId: cabinetId,
                    layerPool: positionInfo.layerPool ? positionInfo.layerPool : [],
                  }
                  this.$nextTick(() => {
                    this.form.setFieldsValue(pick(posInfo, 'roomId', 'cabinetId', 'layerPool'))
                  })
                  resolve({ success: true, message: '请求成功' })
                })
              }
            }).catch((err) => {
              reject({ success: false, message: err.message })
            })
          }
        } else {
          resolve({ success: true, message: '请求成功' })
        }
      })
    },
    /*编辑：处理回显资产相关信息*/
    setAssetsInfo(assetsInfo) {
      if (assetsInfo && Object.keys(assetsInfo)) {
        this.syncAssets = true
        this.assetsInfo.id = assetsInfo.id
        this.assetsInfo["isWarning"] = assetsInfo.isWarning
        if (!assetsInfo.producerId) {
          assetsInfo.producerId = undefined
        }
        if (!assetsInfo.assetsModel) {
          assetsInfo.assetsModel = undefined
        }
        this.$nextTick(() => {
          if (this.assetsInfo.isWarning === '1') {
            this.form.setFieldsValue(pick(assetsInfo,
              'assetsCode', 'assetsCategoryId', 'producerId', 'assetsModel',
              'storageTime', 'isWarning', 'startQualityTime', 'qualityTerm',
              'repairFac', 'repairPhone', 'warrantyConnect', 'warrantyPhone'))
          } else {
            this.form.setFieldsValue(pick(assetsInfo,
              'assetsCode', 'assetsCategoryId', 'producerId', 'assetsModel', 'storageTime', 'isWarning'
            ))
          }
        })
      }
    },
    /*编辑：根据资产类型id和资产id获取附加字段*/
    getAdditionalFieldByCarIdAndAssetsId(assetsInfo) {
      return new Promise((resolve, reject) => {
        if (assetsInfo && Object.keys(assetsInfo)) {
          this.additionalFieldList = []
          if (assetsInfo.assetsCategoryId && assetsInfo.id) {
            let paramObj = {
              assetsCategoryId: assetsInfo.assetsCategoryId.trim(),
              assetsId: assetsInfo.id.trim(),
            }
            getAction(this.url.findCodeValue, paramObj).then((res) => {
              if (res.success) {
                this.additionalFieldList = res.result
                resolve({ success: true, message: res.message })
              } else {
                reject({ success: false, message: res.message })
              }
            }).catch((err) => {
              reject({ success: false, message: err.message })
            })
          }
        } else {
          resolve({ success: true, message: '请求成功' })
        }
      })
    },


    /***************设备信息交互***************/
    /*选择产品*/
    selectProductName(e, node) {
      if (!!e) {
        this.productNodeType = node.dataRef.type
        this.productNodeCatId = node.dataRef.categoryId
        this.$nextTick(() => {
          if (this.syncAssets) {
            this.form.setFieldsValue(pick({ assetsCategoryId: this.productNodeCatId }, 'assetsCategoryId'))
            this.changeAssetsCategory(this.productNodeCatId)
          }
        })
        if (node.dataRef.type != 'product') {
          this.productNodeType = 'category'
          this.collectInfoList = []
          return
        }
        this.deviceInfo["productName"] = node.dataRef.title
        this.getCollectInfoListByproductId(e.trim())
      }
    },
    /*清除产品名称选项时，将记录节点类型变量制空*/
    changeProductName(value) {
      if (!value) {
        this.productNodeType = undefined
        this.productNodeCatId = ''
        this.form.setFieldsValue(pick({ assetsCategoryId: undefined }, 'assetsCategoryId'))
        this.collectInfoList = []
        this.additionalFieldList = []
      }
    },
    /*新增情况下，通过产品id.获取连接参数*/
    getCollectInfoListByproductId(productId) {
      let paramObj = { productId: productId }
      getAction(this.url.queryCollectInfoList, paramObj)
        .then((res) => {
          if (!!res && res.length > 0) {
            res.forEach((ele) => {
              if (ele.value && ele.value.length > 0) {
                ele.value.forEach((val) => {
                  // 配置了扫描任务，从“设备入库”操作按钮跳转过来，设置默认值
                  if (this.scanTaskObject && this.scanTaskObject.taskParam && this.scanTaskObject.transferProtocol.toLowerCase() == ele.key.toLowerCase()) {
                    let obj1 = JSON.parse(this.scanTaskObject.taskParam)
                    for (let key in obj1) {
                      if (val.connectCode == key) {
                        val['connectValue'] = obj1[key];
                      }
                      if (val.connectCode == 'ip' && this.scanTaskObject.ip) {
                        val['connectValue'] = this.scanTaskObject.ip;
                      }
                    }
                  } else if (val.defaultValue != null && val.defaultValue.length > 0) {
                    // 设置默认值
                    val.connectValue = val.defaultValue
                  }
                })
              }
            })
            this.collectInfoList = res
          } else {
            this.collectInfoList = []
          }
        }).catch((err) => {
        this.$message.warning(err.message)
      })
    },
    //检验产品名称是否选择正确
    validateProduct(rule, value, callback) {
      if (rule.required) {
        if (value) {
          if (this.productNodeType === 'category') {
            callback('勿选择产品分类名称，请选择产品名称')
          } else {
            callback()
          }
        } else {
          callback('请选择产品名称')
        }
      } else {
        callback()
      }
    },

    /*机房为空时，将机房、u数据制空*/
    changeRoom(value) {
      if (!value) {
        this.roomNodeType = undefined
        this.cabinetList = []
        this.layerPoolList = []
        this.form.setFieldsValue(pick({ cabinetId: undefined, layerPool: [] }, 'cabinetId', 'layerPool'))
      }
    },
    /*选择机房加载机柜数据*/
    selectRoom(e, node) {
      if (!!e) {
        this.roomNodeType = node.dataRef.type
        if (node.dataRef.type != 'room') {
          this.roomNodeType = 'notRoom'
          return
        }
        this.$nextTick(() => {
          this.cabinetList = []
          this.layerPoolList = []
          this.form.setFieldsValue(pick({ cabinetId: undefined, layerPool: [] }, 'cabinetId', 'layerPool'))
          this.getCabinetList(e)
        })
      }
    },
    /*根据机房id获取机柜下拉数据*/
    getCabinetList(roomId) {
      this.cabinetList = []
      return new Promise((resolve, reject) => {
        let paramObj = {
          roomId: roomId.trim(),
          viewFlag: 3, //3D：3  拓扑 2
        }
        getAction(this.url.cabinet, paramObj)
          .then((res) => {
            if (res.success) {
              this.cabinetList = res.result
              resolve({
                success: true,
                message: res.message
              })
            } else {
              reject({
                success: false,
                message: res.message
              })
            }
          }).catch((err) => {
          reject({
            success: false,
            message: err.message
          })
        })
      })
    },

    //校验机房是否选择正确
    validateRoom(rule, value, callback) {
      if (value) {
        if (this.roomNodeType === 'notRoom') {
          return callback(new Error('请选择到机房'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    /*选择机柜加载u数据*/
    changeCabinet(value) {
      this.layerPoolList = []
      this.form.setFieldsValue(pick({ layerPool: [] }, 'layerPool'))
    },
    /*根据机柜id获取u下拉数据*/
    getLayerPoolList(cabinetId) {
      return new Promise((resolve, reject) => {
        getAction(this.url.layerPool, { cabinetId: cabinetId }).then((res) => {
          if (res.success) {
            this.layerPoolList = res.result
            resolve({
              success: true,
              message: res.message
            })
          } else {
            reject({
              success: false,
              message: res.message
            })
          }
        }).catch((err) => {
          reject({
            success: false,
            message: err.message
          })
        })
      })
    },
    /*校验机柜*/
    validateCabinet(rule, value, callback) {
      if (rule.required) {
        if (value && value.length > 0) {
          this.getLayerPoolList(value).then((res) => {
            if (res.success) {
              if (this.hisCabinetId === value && this.hisLayerPoolList.length > 0) {
                this.hisLayerPoolList.map((item) => {
                  this.layerPoolList.push(item)
                })
                this.layerPoolList = this.layerPoolList.sort((a, b) => {
                  return parseInt(a) - parseInt(b)
                })
                callback()
              } else if (this.hisCabinetId !== value && this.layerPoolList.length === 0) {
                callback('该机柜没有空闲的u可选，请选择其他机柜')
              } else {
                callback()
              }
            } else {
              this.$message.warning(res.message)
              callback('请选择机柜！')
            }
          }).catch((err) => {
            this.$message.warning(err.message)
            callback('请选择机柜！')
          })
        } else {
          callback('请选择机柜！')
        }
      } else {
        callback()
      }
    },
    /*校验U位*/
    validateLayerPool(rule, value, callback) {
      if (rule.required) {
        if (value && value.length > 0) {
          let arr = JSON.parse(JSON.stringify(value))
          if (arr.length < 2) {
            callback()
          } else {
            let newArr = arr.sort((a, b) => {
              return a - b
            })
            let isTips = false
            for (let i = 0; i < newArr.length - 1; i++) {
              if (newArr[i + 1] - newArr[i] > 1) {
                isTips = true
                break
              }
            }
            if (isTips) {
              callback('请选择编号连续的U位')
            } else {
              callback()
            }
          }
        } else {
          callback('请选择U位')
        }
      } else {
        callback()
      }
    },
    /*是否同步至资产*/
    syncAssetsFun(value) {
      if (value) {
        if (this.productNodeCatId) {
          this.$nextTick(() => {
            this.form.setFieldsValue(pick({ assetsCategoryId: this.productNodeCatId }, 'assetsCategoryId'))
            if (this.supplierList.length === 0) {
              this.getAllSupplier().then((res) => {
                if (res.success) {
                  this.changeAssetsCategory(this.productNodeCatId)
                } else {
                  this.$message.warning(res.message)
                }
              }).catch((err) => {
                this.$message.warning(err.message)
              })
            } else {
              this.changeAssetsCategory(this.productNodeCatId)
            }

            if (this.assetsAuto && !this.autoAssetsCode) {
              this.getGenerateAssetsCode().then((res1) => {
                if (res1.success) {
                  this.$nextTick(() => {
                    this.form.setFieldsValue(pick({ assetsCode: this.autoAssetsCode }, 'assetsCode'))
                  })
                }
              })
            }
          })
        }
      }
    },

    /*改变连接参数方法*/
    changeConParam(e, pidx, idx) {
      this.collectInfoList[pidx].value[idx].connectValue = e.target.value
    },
    /*改变连接参数方法1*/
    changeConParam1(e, pidx, idx) {
      this.collectInfoList[pidx].value[idx].connectValue = e
    },
    validateConnectValue(rule, value, callback,validatorRule,displayName,maxLength=255) {
      if (rule.required) {
        if (!value) {
         return  callback(`请输入${displayName}!`)
        }
        const trimmedValue = value.trim()
        if (trimmedValue==='') {
          return callback(`${displayName}不能全为空白字符`)
        }
        if (value !== trimmedValue) {
          return callback(`${displayName}首尾不能包含空白字符！`)
        }
        if (validatorRule) {
          if (new RegExp(validatorRule).test(value)) {
            return callback()
          } else {
            return callback('请输入正确格式的值')
          }
        } else if (value.length > maxLength) {
          return callback(displayName + '长度不能超出 ' + maxLength + ' 个字符')
        } else {
          return callback()
        }
      }else {
        return ValidateOptionalFields(rule, value, callback,displayName,maxLength)
      /*  if (value && value.trim()==='') {
          callback('非必填项不能只输入空格')
        }else if(value&&value.length > maxLength){
          callback(displayName + '长度不应超出 ' + maxLength + ' 个字符')
        }else {
          callback()
        }*/
      }
    },

    /***************资产信息交互***************/

    /*改变资产类型，加载对应附加字段*/
    changeAssetsCategory(e) {
      this.additionalFieldList = []
      this.getAdditionalFieldByCarId(e)
      let supplierId = this.form.getFieldValue('producerId')
      this.getAssetsModelList(e, supplierId)
    },
    /*根据所选产品分类（资产类型）获取附加字段*/
    getAdditionalFieldByCarId(e) {
      if (!!e) {
        getAction(this.url.findCodeName, { assetsCategoryId: e.trim() }).then((res) => {
          if (res.success) {
            this.additionalFieldList = res.result
          } else {
            this.$message.warning(res.message)
          }
        }).catch((err) => {
          this.$message.warning(err.message)
        })
      }
    },
    /*选择供应商，加载资产型号下拉数据*/
    changeProducer(e) {
      this.getAssetsModelList(this.productNodeCatId, e)
    },
    /*是否预警*/
    changeWarning(value) {
      this.assetsInfo["isWarning"] = value.target.value
    },
    /*资产型号交互*/
    // changeAssetsModel(value){
    //   this.$nextTick(() => {
    //     if(value){
    //       this.form.setFieldsValue(pick({ assetsModel: value }, 'assetsModel'))
    //     }
    //   })
    // },
    /*通过产品类型，供应商获取资产型号下拉数据*/
    getAssetsModelList(productId, supplierId) {
      this.assetsModelList = []
      if (productId && supplierId) {
        let param = {
          productCategoryId: productId,
          supplier: supplierId,
          pageSize: '-1',
          current: '1'
        }
        getAction(this.url.querAssetsModelList, param).then((res) => {
          if (res.success) {
            this.assetsModelList = res.result.records
          } else {
            this.$message.warning(res.message)
          }
        }).catch((err) => {
          this.$message.warning(err.message)
        })
      }
    },

    /***************提交***************/
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err && !that.confirmLoading) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.deviceInfo.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'post'
          }
          //提交数据：设备、连接参数、资产（基本信息和附加字段）
          let formData = {
            deviceInfo: this.getDeviceInfo(values),
            templateList: this.getTemplateList(),
            assetsInfo: this.getAssetsInfoObject(values)
          }
          httpAction(httpurl, formData, method)
            .then((res) => {
              that.confirmLoading = false
              if (res.success) {
                if (!this.scanTaskObject) {
                  that.$message.success(res.message)
                }
                /*//查看中基本信息编辑需要传参，以方便更新其他tab界面有关设备的信息
                that.$emit('ok', {
                  deviceInfo: formData.deviceInfo
                })*/
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            }).catch((err) => {
            that.confirmLoading = false
            that.$message.warning(err.message)
          })
        }
      })
    },
    /*整理设备信息数据*/
    getDeviceInfo(values) {
      let info = JSON.parse(JSON.stringify(this.deviceInfo))
      info.productId = values.productId
      info.name = values.name
      info.deviceCode = values.deviceCode
      info.manager = values.manager,
        info.operator = values.operator
      info.isFocus = values.isFocus ? 1 : 0,
        info.description = values.description
      info.specifications = values.specifications
      info.capacity = values.capacity
      info.location = values.location
      info.momgDeptId = values.momgDeptId || ''
      //新增时把产品分类id传给后台
      if (!info.categoryId) {
        info.categoryId = this.productNodeCatId
      }
      // 设备入库的ip传给后台
      if (this.scanTaskObject && this.scanTaskObject.ip) {
        info['ip'] = this.scanTaskObject.ip
      }

      if (this.roomNodeType === 'room') {
        Object.assign(info, {
          positionInfo: {
            cabinetId: values.cabinetId,
            layerPool: values.layerPool
          }
        })
      }
      return info
    },
    /*整理链接参数信息*/
    getTemplateList() {
      const that = this
      let paramTemplate = []
      if (that.collectInfoList.length > 0) {
        that.collectInfoList.forEach((ele) => {
          ele.value.forEach((item) => {
            paramTemplate.push({
              deviceId: item.deviceId,
              connectCode: item.connectCode,
              connectValue: item.connectValue,
              persistentconf: item.persistentconf,
              templateId: item.templateId,
              transferProtocol: item.transferProtocol,
              transferProtocolId: item.transferProtocolId
            })
          })
        })
      }
      return paramTemplate
    },
    /*整理资产信息*/
    getAssetsInfoObject(values) {
      let info = undefined
      if (this.syncAssets) {
        info = JSON.parse(JSON.stringify(this.assetsInfo))
        Object.assign(info, {
          assetsCode: values.assetsCode,
          assetsName: values.name,
          assetsCategoryId: values.assetsCategoryId,
          producerId: values.producerId ? values.producerId : '',
          assetsModel: values.assetsModel ? values.assetsModel : '',
          storageTime: values.storageTime,
          isWarning: values.isWarning,
          startQualityTime: values.isWarning === '1' ? values.startQualityTime : '',
          qualityTerm: values.isWarning === '1' ? values.qualityTerm : '',
          repairFac: values.isWarning === '1' ? values.repairFac : '',
          repairPhone: values.isWarning === '1' ? values.repairPhone : '',
          warrantyConnect: values.isWarning === '1' ? values.warrantyConnect : '',
          warrantyPhone: values.isWarning === '1' ? values.warrantyPhone : '',
          extendValue: this.getAdditionalFieldObject(values)
        })
      }
      return info
    },
    /*整理链接参数信息*/
    getAdditionalFieldObject(values) {
      //参数模板数据拼装
      let extendValue = []
      this.additionalFieldList.forEach((ele) => {
        extendValue.push({
          name: ele.name,
          value: values.addCodes[ele.id] || null,
          type: ele.type,
          id: ele.id,
        })
      })
      return extendValue.length > 0 ? extendValue : undefined
    }
  },
}
</script>

<style lang='less' scoped='scoped'>
.colorBox {
  margin-bottom: 18px;
}

.colorTotal {
  padding-left: 7px;
  border-left: 4px solid #1e3674;
}

/*.lineClass {
  border-top: 1px solid #000;
}*/

/*.ant-form-item {
  margin-bottom: 12px;
}*/
::v-deep .ant-input-number {
  width: 100%;
}
::v-deep .ant-form{
  .ant-form-item{
    .ant-form-item-label{
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}
/*.btn-assets {
  width: 101px;
  height: 28px;
  background: #ecf5ff;
  border: 1px solid #b3d8ff;
  border-radius: 4px;
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: #409eff;
  margin-bottom: 15px;
}

table.gridtable {
  font-family: verdana, arial, sans-serif;
  font-size: 11px;
  color: #606266;
  border-width: 1px;
  border-color: #e8e8e8;
  border-collapse: collapse;
  text-align: left;
  width: 100%;
  table-layout: fixed;
}

table.gridtable td {
  border-width: 1px;
  border-style: solid;
  border-color: #e8e8e8;
  word-wrap: break-word;
}

.leftTd {
  width: 17%;
  background-color: #fafafa;
  padding: 16px 24px;
  text-align: center;
}

.rightTd {
  width: 35%;
  padding: 16px 24px;
}

.assets-row {
  padding-right: 0.1625rem !* 13/80 *!;
}

!* 定义滚动条样式 *!
::-webkit-scrollbar {
  width: 0.15rem !* 12/80 *!;
  // height: 6px;
  //background-color: #222325;
}

!*定义滚动条轨道 内阴影+圆角*!
::-webkit-scrollbar-track {
  box-shadow: inset 0 0 0px rgba(240, 240, 240, 0.5);
  // border-radius: 50%;
  background-color: #eaeaea;
  border-radius: 0.1rem !* 8/80 *!;
}

!*定义滑块 内阴影+圆角*!
::-webkit-scrollbar-thumb {
  border-radius: 0.1rem !* 8/80 *!;
  box-shadow: inset 0 0 0px rgba(240, 240, 240, 0.5);
  background-color: #d6d6d6;
}

@media (max-width: 768px) {
  .param-tips{
    margin-top: 29px;
  }
}
@media (min-width: 768px) {
  .param-tips{
    margin-top: 0px;
  }
}*/
</style>
