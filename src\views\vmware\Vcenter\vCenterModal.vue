<template>
  <a-row style="height: 100%">
    <a-col :xl="24" :lg="24" :md="24" :sm="24" :xs="24" style="height: 100%; overflow: hidden; overflow-y: auto">
      <a-row style="height: 100%">
        <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
          <!-- 查询区域 -->
          <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0', marginRight: '12px' }" class="card-style"
            style="width: 100%">
            <div class="table-page-search-wrapper">
              <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
                <a-row :gutter="24" ref="row">
                  <a-col :span="spanValue">
                    <a-form-item label="设备名称">
                      <a-input :maxLength='maxLength' placeholder="请输入设备名称" v-model="queryParam.name" :allowClear="true" autocomplete="off" />
                    </a-form-item>
                  </a-col>
                  <a-col :span="spanValue">
                    <a-form-item label="监控状态">
                      <j-dict-select-tag v-model="queryParam.status" placeholder="请选择监控状态" dictCode="device_status" />
                    </a-form-item>
                  </a-col>
                  <a-col :span="spanValue">
                    <a-form-item label="标签">
                      <a-select
                        :getPopupContainer="(target) => target.parentNode"
                        mode="multiple"
                        showSearch
                        :allowClear="true"
                        :maxTagCount='1'
                        :maxTagTextLength="5"
                        v-model="queryParam.tagKeys"
                        placeholder="请选择标签"
                        optionFilterProp="children">
                        <a-select-option v-for="(item, index) in taginfoSelectData" :key="index" :value="item.tagKey">
                          {{ item.tagName }}
                        </a-select-option>
                      </a-select>
                    </a-form-item>
                  </a-col>
                  <a-col :span="colBtnsSpan()">
                    <span class="table-page-search-submitButtons"
                      :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                      <a-button type="primary" class="btn-search btn-search-style" @click="dosearch">查询</a-button>
                      <a-button class="btn-reset btn-reset-style" @click="searchReset">重置</a-button>
                      <a v-if="isVisible" class="btn-updown-style" @click="doToggleSearch">
                        {{ toggleSearchStatus ? '收起' : '展开' }}
                        <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                      </a>
                    </span>
                  </a-col>
                </a-row>
              </a-form>
            </div>
          </a-card>
          <a-card :bordered="false" style="width: 100%; flex: auto">
            <div class="table-operator table-operator-style">
              <a-button @click="handleExportXls('Vcenter表','vcenter')">导出</a-button>
              <a-dropdown v-if='selectedRowKeys.length > 0'>
                <a-menu slot="overlay" style='text-align: center'>
                  <a-menu-item key='1' @click='batchDel'>删除</a-menu-item>
                </a-menu>
                <a-button> 批量操作<a-icon type='down' /></a-button>
              </a-dropdown>
              <a-button v-if='selectedRowKeys.length > 0' @click="batchEnableOperate(1)">启用</a-button>
              <a-button v-if='selectedRowKeys.length > 0' @click="batchEnableOperate(0)">禁用</a-button>
              <a-popover title="监控状态说明">
                <template slot="content">
                  <div style="display:flex;">
                    <div>
                      <span>在线：</span>
                      <img src="@/assets/bigScreen/28.png" alt="" class="stateImg" />
                    </div>
                    <div class="stateBox">
                      <span>离线：</span>
                      <img src="@/assets/bigScreen/57.png" alt="" class="stateImg" />
                    </div>
                    <div class="stateBox">
                      <span>告警：</span>
                      <img src="@/assets/bigScreen/56.png" alt="" class="stateImg" />
                    </div>
                    <div class="stateBox">
                      <span style="margin-left: 5px">禁用：</span>
                      <a-icon type="stop" theme="twoTone" two-tone-color="#eb2f96" style="font-size:16px"
                        class="stateImg" />
                      <span style="margin-left: 5px"></span>
                    </div>
                  </div>
                </template>
                <a-icon type="question-circle" theme="twoTone" style="font-size: 18px" />
              </a-popover>
            </div>
            <a-table ref="table" bordered :row-key="(record,index)=>{return record.id}" :columns="columns"
              :dataSource="dataSource" :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
              :pagination="ipagination" :loading="loading"
              :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
              @change="handleTableChange">
              <template slot="status" slot-scope="text,record">
                <span v-if="record.status == 1 && record.enable === 1">
                  <img src="@/assets/bigScreen/28.png" alt="" class="stateImg" />
                  <a style="margin-left:10px;color: rgb(41, 142, 224);" @click="linkTo(record)">{{text}}</a>
                </span>
                <span v-if="record.status == 2 && record.enable === 1">
                  <img src="@/assets/bigScreen/56.png" alt="" class="stateImg" />
                  <a style="margin-left:10px;color: rgb(41, 142, 224);" @click="linkTo(record)">{{text}}</a>
                </span>
                <span v-if="record.status == 0 && record.enable === 1">
                  <img src="@/assets/bigScreen/57.png" alt="" class="stateImg" />
                  <a style="margin-left:10px;color: rgb(41, 142, 224);" @click="linkTo(record)">{{text}}</a>
                </span>
                <!--  禁用状态下设备为离线状态 -->
                <span v-if="record.enable != 1" style="color: #df1a1a">
                  <a-icon type="stop" theme="twoTone" two-tone-color="#eb2f96" style="font-size:16px"
                    class="stateImg" />
                  <a style="margin-left:10px;color: rgb(41, 142, 224);" @click="linkTo(record)">{{text}}</a>
                </span>
              </template>
              <template slot="Enable" slot-scope="text,record">
                <div v-if="record.enable === 1">
                  <a-icon type="pause-circle" theme="twoTone" two-tone-color="#52c41a" style="font-size:20px" />
                </div>
                <div v-else>
                  <a-icon type="play-circle" theme="twoTone" two-tone-color="#eb2f96" style="font-size:20px" />
                </div>
              </template>
              <template slot="tooltip" slot-scope="text">
                <a-tooltip placement="topLeft" :title="text" trigger="hover">
                  <div class="tooltip">
                    {{ text }}
                  </div>
                </a-tooltip>
              </template>
              <template slot="tagInfoList" slot-scope="text">
                <a-popover title="标签">
                  <template slot="content">
                    <div v-for="item in text" style="margin: 5px 0">
                      <span
                        :style="{'background-color':item.tagColor, color:'white','border-radius':'10px',padding:'2px 10px'}">
                        {{item.tagName}}
                      </span>
                    </div>
                  </template>
                  <a-icon type="environment" />
                </a-popover>
              </template>
            </a-table>
          </a-card>
        </a-col>
      </a-row>
    </a-col>
  </a-row>
</template>

<script>
  import '@/assets/less/TableExpand.less'
  import {
    mixinDevice
  } from '@/utils/mixin'
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import JSuperQuery from '@/components/jeecg/JSuperQuery.vue'
  import {
    queryAssetsCategoryTreeList
  } from '@/api/device'
  import {
    httpAction,
    getAction,
    postAction,
    deleteAction
  } from '@/api/manage'
  import JDictSelectTag from '@/components/dict/JDictSelectTag.vue'
  import {
    YqFormSearchLocation
  } from '@/mixins/YqFormSearchLocation'
  import difference from 'lodash/difference';

  var deviceForTag = '';
  export default {
    name: 'DeviceInfoList',
    mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
    props: {
      tableParams: {
        type: Object,
      },
    },
    components: {
      JSuperQuery,
     JDictSelectTag
    },
    data() {
      return {
        maxLength:50,
        taginfoSelectData: [],
        showSearch: true,
        formItemLayout: {
          labelCol: {
            style: 'width:80px',
          },
          wrapperCol: {
            style: 'width:calc(100% - 80px)'
          }
        },
        //设备数量
        deviceInfo: {
          allNum: '',
          outNum: '',
          onNum: '',
          disabledNum: '',
        },
        batchEnable: 1,
        description: '设备表管理页面',
        // 表头
        columns: [{
            title: '设备名称',
            dataIndex: 'name',
            scopedSlots: {
              customRender: 'status'
            },
            customCell: () => {
              let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
              return {
                style: cellStyle
              }
            },
          },
          {
            title: '产品名称',
            dataIndex: 'productName',
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 150px;max-width:300px'
              return {
                style: cellStyle
              }
            },
          },
          {
            title: '通信协议',
            dataIndex: 'transferProtocol',
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 100px;max-width:300px'
              return {
                style: cellStyle
              }
            },
          },
          {
            title: '所属网关',
            dataIndex: 'gatewayName',
            customRender: (text, record, index) => {
              if (record.gatewayName == '' || record.gatewayName == null || record.gatewayName == undefined) {
                return "未绑定网关"
              } else {
                return text
              }
            },
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 100px;max-width:300px'
              return {
                style: cellStyle
              }
            },
          },
          {
            title: '添加时间',
            dataIndex: 'createTime',
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 80px;max-width:300px'
              return {
                style: cellStyle
              }
            },
          },
          {
            title: '标签',
            dataIndex: 'tagInfoList',
            scopedSlots: {
              customRender: 'tagInfoList'
            },
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 100px;max-width:180px;'
              return {
                style: cellStyle
              }
            },
          },
          {
            title: '关联资产',
            dataIndex: 'assetsName',
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 110px;max-width:300px'
              return {
                style: cellStyle
              }
            },
          },
          {
            title: '设备说明',
            dataIndex: 'description',
            scopedSlots: {
              customRender: 'tooltip'
            },
            customCell: () => {
              let cellStyle = 'text-align: left;min-width: 120px;max-width:400px'
              return {
                style: cellStyle
              }
            },
          },
        ],
        url: {
          list: '/device/deviceInfo/vcenterList',
          exportXlsUrl: '/device/deviceInfo/exportXls',
          deleteBatch: '/device/deviceInfo/deleteBatchDevice',
          importExcelUrl: 'device/deviceInfo/importExcel',
          batchEnable: '/device/deviceInfo/batchUpdateEnable',
          deviceInfoUrl: '/device/deviceInfo/showCounts',
        },
        dataSource: [], //table数据
        name: '',
        status: '',
        queryParam: {
          vType: 'Vcenter'
        }
      }
    },
    computed: {
      importExcelUrl: function () {
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
      },
    },
    watch: {
      tableParams: {
        handler(val) {
          if (val.ipagination) {
            this.ipagination = Object.assign({}, val.ipagination)
          }
          if (val.queryParam) {
            this.queryParam = Object.assign({}, val.queryParam)
          }
          this.loadData()
        },
        deep: true,
        immediate: true,
      }
    },
    created() {
      this.getDeviceInfoMap()
      this.queryAllTags()
    },
    methods: {
      linkTo(record) {
        let param = {
          data: record,
          ipagination: this.ipagination,
          queryParam: this.queryParam
        }
        this.$parent.pButton2(1, param)
      },
      queryAllTags() {
        getAction('/device/deviceInfo/taginfo4Select').then((res) => {
          if (res.success) {
            this.taginfoSelectData = res.result
          }
        })
      },

      filterOption(inputValue, option) {
        return option.tagName.indexOf(inputValue) > -1;
      },
      getDeviceInfoMap(flag = false) {
        getAction(this.url.deviceInfoUrl).then((res) => {
          if (res.success) {
            this.deviceInfo.allNum = res.result.all
            this.deviceInfo.disabledNum = res.result.disable
            this.deviceInfo.onNum = res.result.on
            this.deviceInfo.outNum = res.result.out
            if (flag) {
              this.$message.success('同步成功')
            }
          }
        })
      },
      //批量禁用、启用
      batchEnableOperate(enable) {
        if (!this.url.batchEnable) {
          this.$message.error('请设置url.batchConfirm属性!')
          return
        }
        if (this.selectedRowKeys.length <= 0) {
          this.$message.warning('请选择一条记录！')
          return
        }
        var that = this
        this.$confirm({
          title: '确认操作',
          okText: '是',
          cancelText: '否',
          content: '是否确定修改选中数据?',
          onOk: function() {
            that.loading = true
            getAction(that.url.batchEnable, {
              ids: that.selectedRowKeys.join(','),
              enable: enable
            })
              .then((res) => {
                if (res.success) {
                  that.$message.success(res.message)
                  that.loadData()
                  that.onClearSelected()
                } else {
                  that.$message.warning(res.message)
                }
                that.loading = false
              }).catch((err) => {
              that.$message.warning(err.message)
              that.loading = false
            })
          }
        })
      },
      //表单查询,点击查询按钮，默认查询第一页
      dosearch() {
        this.loadData(1)
      },
      //表单重置
      searchReset() {
        //重置form表单，不重置tree选中节点
        if (this.queryParam.option) {
          let type = this.queryParam.type
          let option = this.queryParam.option
          this.queryParam = {}
          this.queryParam.type = type
          this.queryParam.option = option
        } else {
          this.queryParam = {}
        }
        this.queryParam.vType = 'Vcenter'
        this.loadData(1)
      },
    },
  }
</script>
<style lang='less' scoped>
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';


  .gutter-example {
    //margin-bottom: 16px;
    //width: 100%;
    margin-left: -16px;
    //margin-right: -16px;
  }

  .gutter-row {
    padding-right: 0px !important;
    margin-bottom: 16px;
  }

  ::v-deep .gutter-example .ant-row>div {
    background: transparent;
    border: 0;
  }

  .row-example {
    margin: 0px !important;
    margin-left: 4px !important;
  }

  .ant-table-row-cell-break-word span a {
    color: #409eff !important;
  }

  /*.ant-row .ant-col-3 .ant-card .ant-card-body {
  height: 810px !important;
}*/
  /*操作--设置按钮背景色*/
  ::v-deep .ant-table-filter-icon {
    background-color: #e5e5e5;
  }

  .stateBox {
    margin-left: 20px;
  }

  .stateImg {
    vertical-align: middle
  }
  //修改选择框多选溢出
  /deep/ .table-page-search-wrapper .ant-form-inline .ant-form-item .ant-form-item-control-wrapper{
    width: calc(100% - 80px);
  }
  /deep/ .ant-select-selection--multiple{
    height: 32px;
    overflow-y: auto;
  }
</style>