<template>
    <a-row :gutter="10" style="height: 100%;" class="vScroll">
    <a-col style="width:100%;height: 100%;display: flex;flex-direction: column">
      <a-card :bordered="false" style="width: 100%; flex: auto">
        <div>
          <div class="colorBox">
            <span class="colorTotal">设备详情</span>
          </div>
          <div style="position: absolute; right: 24px; top: 18px; z-index: 10">
            <img
              src="~@assets/return1.png"
              @click="getGo"
              style="width: 20px; height: 20px; cursor: pointer"
            />
          </div>
        </div>
        <a-card :bordered='false' class='card2' :bodyStyle="{ padding: '0'}">
          <div class='box config'>
            <a-descriptions :column='{ xxl: 2, xl: 2, lg: 2, md: 2, sm: 2, xs: 2 }' bordered>
              <a-descriptions-item label='设备名称'>{{ data.deviceName }}</a-descriptions-item>
              <a-descriptions-item label='设备类型'>
                <span>{{ deviceTypeList[data.deviceType].name }}</span>
              </a-descriptions-item>
              <a-descriptions-item label='设备状态'>{{ data.deviceStatus == 0?'空闲':'占用' }}</a-descriptions-item>
            </a-descriptions>
          </div>
        </a-card>
        <div class="colorBox">
          <span class="colorTotal">预约记录表</span>
        </div>
        <a-table
          ref="table"
          bordered
          rowKey="id"
          :columns="columns"
          :dataSource="data.reservedList"
          :pagination="ipagination"
          :loading="loading"
          @change="handleTableChange"
        >
          <template slot="tooltip" slot-scope="text">
            <a-tooltip placement="topLeft" :title="text" trigger="hover">
              <div class="tooltip">{{ text }}</div>
            </a-tooltip>
          </template>
          <template slot='reservedStatus' slot-scope='text,record'>
           <span style="font-size: 12px;padding:2px 4px;border-radius:3px;color:#fff" :style="{backgroundColor:record.reservedStatus===1?'#4BD863':'#c1bfbf'}">
            {{record.reservedStatus===1?'进行中':'已完成'}}
          </span>
          </template>
        </a-table>
      </a-card>
    </a-col>
  </a-row>
</template>
<script>
import { getAction, putAction } from '@/api/manage'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import {deviceTypes, projects} from '../modules/projects'
export default {
  name: "DeviceReserveDetail",
  mixins: [JeecgListMixin],
  data(){
    return{
      // 表头
      columns: [
        {
          title: '预约人',
          dataIndex: 'reservedUser',
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'tooltip'
          }
        },
        {
          title: '预约状态',
          dataIndex: 'reservedStatus',
          scopedSlots: {
            customRender: 'reservedStatus'
          },
        },
        {
          title: '预约开始时间',
          dataIndex: 'reservedStartTime',
        },
        {
          title: '预约结束时间',
          dataIndex: 'reservedEndTime',
        },
        {
          title: '支撑项目',
          dataIndex: 'supportingProject',
          customRender: (text) => {
            return projects.find((item) => item.value === text).label
          }
        }
      ],
      url: {
        list: '/kbase/knowledges/list/review'
      },
      deviceTypeList: deviceTypes,
      disableMixinCreated:true,
    }
  },
  props: {
    data: {
      type: Object
    }
  },
  mounted() {
    console.log('data==', this.data)
  },
  methods:{
    //返回上一级
    getGo() {
      this.$parent.pButton2(0)
    }
  }
}
</script>

<style scoped lang="less">
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
.colorBox {
  margin-top: 10px;
  margin-bottom: 10px;
  .colorTotal {
    padding-left: 7px;
    border-left: 4px solid #1e3674;
  }
}
</style>