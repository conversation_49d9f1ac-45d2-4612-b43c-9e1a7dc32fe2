<template>
  <div style='height: 100% !important;width:100%;display: flex;flex-direction: column'>
    <div class='header-info' v-if="showOperation&&terminalInfo.deviceStatus==1&&($yqHasPermission('terminal:shut-down')||$yqHasPermission('terminal:reboot'))">
      <div class="btn-box">
        <a-button class="btn" type="default" v-if="$yqHasPermission('terminal:shut-down')" @click="shutdownOrRebootFun('shutdown')">关机</a-button>
        <a-button class="btn" type="default" v-if="$yqHasPermission('terminal:reboot')" @click="shutdownOrRebootFun('reboot')">重启</a-button>
      </div>
      <div class="header-back">
        <img src='~@assets/return1.png' alt='' @click='getGo'/>
      </div>
    </div>
    <div class="terminal-info">
      <div class="terminal-name-box">
        <div class="terminal-name">
          <span class="title-box"> {{terminalInfo.name}}</span>
          <span class='span-status'>
<!--          <span v-if='terminalInfo.enable==1'>-->
          <span :class="[terminalInfo.deviceStatus == 1?'online':'offline']" >{{ terminalInfo.deviceStatus==1?"在线":"离线"}}</span>
          <span v-if='terminalInfo.alarmStatus === 1' class='alarm-status'>告警</span>
            <!--          </span>-->
            <!--          <span v-else class="offline">{{ deviceInfo.enableName}}</span>-->
        </span>
        </div>
        <div class="header-back"  v-if="showOperation&&(terminalInfo.deviceStatus!=1||( !$yqHasPermission('terminal:shut-down')&& !$yqHasPermission('terminal:reboot')))">
          <img src='~@assets/return1.png' alt='' @click='getGo'/>
        </div>
      </div>

      <div class="content-box">
        <div  class="colorBox">
          <span class="colorTotal" v-if="belongingData&&belongingData.length&&belongingData.length>0">所属信息</span>
          <span class='action' v-if="showOperation && $yqHasPermission('terminal:edit')" @click="handleDetailEdit(terminalInfo)">
            <a-icon class="icon" type='edit'/>编辑
          </span>
          <span class='action' v-if="grafanaUrl"  @click="openGrafana">
            <a-icon class="icon" type="unordered-list"/>历史数据
          </span>
        </div>
        <descriptions
          :column="{ xxl: 2, xl: 2, lg: 2, md: 2, sm: 1, xs: 1 }"
          :items="belongingData"
          :padding="'0 0 0 11px'"
          :show-title="false"
          :bordered="false"
          :letterSpacing="4"
          :limitedLength="4"
          ></descriptions>
        <descriptions
          :items="basicData"
          :title="'基本信息'"
          :column="{ xxl: 2, xl: 2, lg: 2, md: 2, sm: 1, xs: 1 }"
        ></descriptions>
        <state-info v-if="terminalInfo.deviceId" ref="stateInfo" :show-scroll="false"></state-info>
      </div>
    </div>

    <terminal-device-modal
      ref='modalForm'
      @ok='modalFormOk'
    ></terminal-device-modal>
  </div>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import TerminalDeviceModal from './TerminalDeviceModal'
import descriptions from '@comp/descriptions/descriptions.vue'
import stateInfo from '@views/devicesystem/deviceshow/StateInfo.vue'
import { ajaxGetDictItem, queryConfigureDictItems } from '@api/api'

export default {
  name: 'TerminalInfo',
  props:{
    showOperation: {
      type: Boolean,
      required:false,
      default:true
    },
    data: {
      type: Object,
      required:false,
      default:()=>{
        return {}
      }
    }
  },
  components: {
    stateInfo,
    descriptions,
    TerminalDeviceModal
  },
  data() {
    return {
      terminalTypeList:[],
      cpuTypeList:[],
      osTypeList:[],
      terminalInfo: {},
      grafanaUrl: '',
      belongingFields: [
        {
          field: 'usernameText',
          label: '使用人'
        },
        {
          field: 'phone',
          label: '联系电话'
        },
        {
          field: 'deptId_dictText',
          label: '单位'
        },
        {
          field: 'deptId_dictText',
          label: '行政区划'
        },
        {
          field: 'address',
          label: '地址'
        },
        {
          field: 'administrator',
          label: '管理人'
        },
        {
          field: 'adminDepartment',
          label: '管理部门'
        },
        {
          field: 'positionTag',
          label: '位置'
        },
        {
          field: 'addrDetail',
          label: '详细地址'
        }
      ],
      belongingData: [],
      basicFields: [
        {
          field: 'name',
          label: '终端名称'
        },
        {
          field: 'osType_dictText',
          label: '操作系统'
        },
        {
          field: 'cpuType_dictText',
          label: 'cpu类型'
        },
        {
          field: 'cpuArch',
          label: 'cpu架构'
        },
        {
          field: 'sysVersion',
          label: '操作系统版本'
        },
        // {
        //   field: 'sysVersion',
        //   label: '系统更新时间'
        // },
        {
          field: 'sysUptimeStr',
          label: '开机时长'
        },
        {
          field: 'ip',
          label: 'IP地址'
        },
        {
          field: 'macAddr',
          label: 'MAC地址'
        },
        {
          field: 'SN',
          label: 'sn'
        },
        {
          field: 'macIp',
          label: '网络Mac和IP'
        },
        {
          field: 'enable_dictText',
          label: '激活状态'
        },
        {
          field: 'terminalType_dictText',
          label: '终端类型'
        },
        {
          field: 'gatewayName',
          label: '所属网关'
        },
        {
          field: 'gatewayType_dictText',
          label: '网络类型'
        },
        {
          field: 'description',
          label: '描述'
        }
      ],
      basicData: [],
      url: {
        queryTerminalInfo: '/terminal/terminalDevice/queryById',
        //queryGrafanaUrl: '/device/deviceInfo/getUrlByDeviceCode',
        queryGrafanaUrl: '/device/deviceInfo/getUrl',
        shutdownOrReboot:'/terminal/terminalDevice/sendCommand',
      }
    }
  },
  watch:{
    data:{
      handler(val){
        this.init(val)
      },
      deep:true,
      immediate:true
    },
    '$store.getters.deviceStatus': {
      // immediate:true,
      deep: true,
      handler(e) {
        for (let i = 0; i < e.length; i++) {
          let status=e[i].status==='up'?1:0
          let info=this.terminalInfo
          if (e[i].deviceCode === info.uniqueCode&&info.deviceStatus!=status) {
            this.modalFormOk()
            break
          }
        }
      }
    }
  },
  created() {
    this.initDictConfig()
  },
mounted() {
  console.log(this.showOperation )
  console.log(this.$yqHasPermission('terminal:shut-down'))
  console.log(this.$yqHasPermission('terminal:reboot'))
},
  methods: {
    initDictConfig() {
      this.getDictData('resources_type','terminalTypeList')
      this.getDictData('cpuType','cpuTypeList')
      this.getDictData('os_type','osTypeList')
    },
    getDictData(dictCode,list) {
      ajaxGetDictItem(dictCode, null).then((res) => {
        if (res.success) {
          this[list]=res.result
        }
      })
    },
    getDictText(field,list){
      let txt=field+'_dictText'

      if (! Object.keys(this.terminalInfo).includes(txt)){
        let m=list.filter((item)=>{
          return item.value==this.terminalInfo[field].value
        })
        this.terminalInfo[field]=m.value
      }
    },

    modalFormOk() {
      let params = { id: this.terminalInfo.id }
      getAction(this.url.queryTerminalInfo, params).then((res) => {
        if (res.success) {
          let info=res.result.records&&res.result.records.length>0&&res.result.records[0]|| {}
          this.init(info)
        }
      })
    },
    init(info){
      this.terminalInfo =JSON.parse(JSON.stringify(info))
      if (this.terminalInfo.id){
        //this.getDictText('terminalType',this.terminalTypeList)
        //this.getDictText('cpuType',this.cpuTypeList)
        // this.getDictText('osType',this.osTypeList)
        if (this.terminalInfo.deviceId){
          this.$nextTick(()=>{
            let deviceInfo={
              id:info.deviceId,
              internetFlag:info.internetFlag?info.internetFlag:''
            }
            this.$refs.stateInfo&&this.$refs.stateInfo.show(deviceInfo)
          })
        }
        this.belongingData= this.getDescriptionsItem(this.belongingFields)
        this.basicData=this.getDescriptionsItem(this.basicFields)
        this.getGrafanaUrl()
      }
    },
    //详情编辑
    handleDetailEdit: function(record) {
      this.$refs.modalForm.edit(record)
      this.$refs.modalForm.title = '编辑'
      this.$refs.modalForm.disableSubmit = false
    },
    //返回上一级
    getGo() {
      this.$parent.pButton2(0)
    },
    /**
     * 整理所属信息、基本信息数据，以排除空数据     *
     * @param {Array} fields - 数组：元素包含字段英文名和中文名
    * */
    getDescriptionsItem(fields){
      let data=[]
      let obj=this.terminalInfo
      for (let i = 0; i < fields.length; i++) {
        let field=fields[i].field
        let va=obj[field]+''
        if (va!=''&&va!='null'){
          let item={
            label:fields[i].label,
            value:obj[fields[i].field]
          }
          data.push(item)
        }
      }
      return data
    },
    /**
     * 获取grafana部门地址
     * */
    getGrafanaUrl(){
      getAction(this.url.queryGrafanaUrl, { id: this.terminalInfo.deviceId }).then((res) => {
        if (res.success) {
          this.grafanaUrl = res.result
        }
      })
    },
    openGrafana() {
      let CZ_Data = window._CONFIG['customization']
      if (CZ_Data && CZ_Data.cz_zunyi && CZ_Data.cz_zunyi.internetFlag == record.internetFlag) {
        let grafanaUrl = CZ_Data.cz_zunyi.internetGrafanaURL + record.deviceCode
        window.open(grafanaUrl)
      } else {
        if (this.grafanaUrl) {
          // var ipStr = window.location.href.split('/')
          var ipStr = window.location.host // 获取当前页面地址
          queryConfigureDictItems({
            parentCode: 'grafanaProxy',
          }).then((res) => {
            let activeTip=true
            if (res.success && res.result && res.result.length > 0) {
              for (let i = 0; i < res.result.length; i++) {
                let el = res.result[i]
                var arys = el.value.split('>')
                if (!!ipStr && ipStr === arys[0]) {
                  activeTip = false
                  let openUrl = arys[1] +this.grafanaUrl
                  window.open(openUrl)
                  return
                }
              }
            }
            if (activeTip){
              this.$message.warning('grafana请求路径匹配异常')
            }
          }).catch((err) => {
            this.$message.warning(err.message)
          })
        }
      }
    },
    /**
     * 终端：关机、重启
     * @param {String} command - 针对终端的操作命令（取值：shutdown、reboot）
     * */
    shutdownOrRebootFun(command){
      getAction(this.url.shutdownOrReboot,{uniqueCode:this.terminalInfo.uniqueCode,command:command}).then((res) => {
        if (res.success){
          this.$message.success(res.message)
          this.modalFormOk()
        }else {
          this.$message.warning(res.message)
        }
      }).catch((err)=>{
        this.$message.warning(err.message)
      })
    },
  }
}
</script>

<style scoped lang='less'>
.card {
  border-radius: 3px;
  background: #fff;
  padding: 24px;
}

.header-info {
  .card;
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: start;

  .btn-box{
    margin-right: 16px;
    .btn{
      height: 28px;
      margin-right:14px
    }
  }
}

.terminal-info {
  .card;
  flex: 1;
  height: 50%;

  .terminal-name-box{
     display: flex;
    justify-content: space-between;
    align-items: start;
    flex-flow: row nowrap;
    height: 30px;

    .terminal-name{
      .title-box{
        font-size: 20px;
        color: #000000;
        font-weight: 500
      }

      .span-status{
        color: #ffffff;
        margin-left: 16px;
        font-size: 14px;

        .tag{
          padding: 2px 8px;
          border-radius: 2px;
        }
        .online{
          .tag;
          background: #27C11E;
        }
        .offline{
          .tag;
          background: lightgrey
        }
        .alarm-status{
          .tag;
          background: #c1ab1e
        }
      }
    }
  }

  .content-box {
    height:calc(100% - 30px);
    padding-right:1px;
    overflow: hidden !important;
    overflow-y: auto !important;

    .colorBox {
      margin-top: 20px;
      margin-bottom: 10px;

      .colorTotal {
        padding-left: 7px;
        font-size: 14px;
        border-left: 4px solid #1e3674;
        margin-right: 12px;
      }

      .action{
        color: #4b90de;
        font-size: 14px;
        cursor: pointer;
        margin-right: 12px;
        .icon{
          color: #4b90de;
          margin-right: 4px;
        }
        a{
          color: #4b90de !important;
        }
      }
    }

  }
}

.header-back {
  img{
    width: 20px;
    height: 20px;
    cursor: pointer
  }
}
</style>
