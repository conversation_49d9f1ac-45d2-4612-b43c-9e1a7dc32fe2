import Vue from 'vue'
import Vuex from 'vuex'

import app from './modules/app'
import user from './modules/user'
import permission from './modules/permission'
import enhance from './modules/enhance'
import online from './modules/online'
import getters from './getters'
import topo from './modules/topo'
import alarmInfo from './modules/alarmInfo'
import deviceStatus from './modules/deviceStatus'
import  moduleThree  from "./modules/threeState"

Vue.use(Vuex)

export default new Vuex.Store({
  modules: {
    app,
    user,
    permission,
    enhance,
    online,
    topo,
    alarmInfo,
    deviceStatus,
    threejs:moduleThree
  },
  state: {
    themeOK: false,
    userChange: false,
  },
  mutations: {

  },
  actions: {

  },
  getters
})
