<template>
  <div class='search-box' id='observed-element'>
    <div class="inner">
        <div class="header-box">
          <div class="title">{{ knowledgeSearchName }}</div>
          <img class="plane-img" src="~@assets/img/paper-plane.png" />
        </div>
        <div class="input-box">
          <a-input-group compact style="height:100%;">
            <a-input v-model="value" placeholder="请输入关键字" style="" class="a-input"
                     @pressEnter="goSearchResultPage(0)" />
            <a-button type="primary" class="btn" style="" @click="goSearchResultPage(0)">
              <a-icon type="search"></a-icon>
              搜索
            </a-button>
          </a-input-group>
          <!-- <div class="line"></div> -->
        </div>
      </div>
  </div>
</template>
<script>
import { mapActions } from 'vuex'
import Vue from 'vue'
import { PLATFORM_TYPE,RESET_MENUS } from '@/store/mutation-types'
import { generateBigscreenRouter, generateIndexRouter } from '@/utils/util'
import store from '@/store'
import router from '@/router'
import { queryConfigureDictItem } from '@api/api'
export default {
  name: "WorkplaceKnowledgeSearch",
  components: {  },
  data() {
    return {
      value: '',
      knowledgeSearchName: '信创联合运维知识库',
    }
  },
  created() {
    // 根据配置字典获取知识搜索大标题（客户端）
    this.getKnowledgeSearchName('client')
  },
  mounted() {
   this.calcWidth()
  },
  methods:{
    /*从配置字典中获取知识搜索的大标题*/
    getKnowledgeSearchName(type) {
      queryConfigureDictItem({
        parentCode: 'knowledgeSearchName',
        childCode: type
      }).then((res) => {
        if (res.success && res.result) {
          this.knowledgeSearchName = res.result
        }
      }).catch(() => {

      })
    },
    calcWidth(){
      const elementToObserve = document.getElementById('observed-element');
      //创建一个ResizeObserver实例并定义回调函数
      const resizeObserver = new ResizeObserver(entries => {
        const { width, height } = entries[0].contentRect;
        let minWidth = 300
        let maxWidth = 1500

        let minTFontSize = 28
        let maxTFontSize = 48

        let minImgWidth = 95
        let maxImgWidth = 145

        let minBtnWidth = 112
        let maxBtnWidth = 195

        let minTop=30
        let maxTop=60

        let tFontSize = minTFontSize
        let imgWidth = minImgWidth
        let btnWidth = minBtnWidth
        let top=minTop

        if (width <= minWidth) {
          tFontSize = minTFontSize
          imgWidth = minImgWidth
          btnWidth = minBtnWidth
          top=minTop
        } else if (width > maxWidth) {
          tFontSize = maxTFontSize
          imgWidth = maxImgWidth
          btnWidth = maxBtnWidth
          top=maxTop
        } else {
          let fsk = (maxTFontSize - minTFontSize) / (maxWidth - minWidth)
          tFontSize = fsk * (width - minWidth) + minTFontSize

          let imgk = (maxImgWidth - minImgWidth) / (maxWidth - minWidth)
          imgWidth = imgk * (width - minWidth) + minImgWidth

          let btnk = (maxBtnWidth - minBtnWidth) / (maxWidth - minWidth)
          btnWidth = btnk * (width - minWidth) + minBtnWidth

          let topk = (maxTop - minTop) / (maxWidth - minWidth)
          top = topk * (width - minWidth) + minTop
        }

        let imgHeight = 323 * imgWidth / minWidth
        elementToObserve.style.setProperty("--titleFontSize", tFontSize + "px")
        elementToObserve.style.setProperty("--imgWidth", imgWidth + "px")
        elementToObserve.style.setProperty("--imgHeight", imgHeight + "px")
        elementToObserve.style.setProperty("--btnWidth", btnWidth + "px")
        elementToObserve.style.setProperty("--top", top + "px")
      });

      //开始观察元素
      resizeObserver.observe(elementToObserve);
    },
    ...mapActions(['GetMenuPermissions']),
    jumpRouter(routerInfo) {
      let that = this
      let params = {
        url: routerInfo.redirect,
        // component: routerInfo.component,
      }
      that
        .GetMenuPermissions(params)
        .then((res) => {
          if (res.success) {
            sessionStorage.setItem(PLATFORM_TYPE, res.platformType)
            const menuData = res.menu
            let constRoutes = []
            if (res.platformType === 4|| res.platformType === 8) {
              constRoutes = generateBigscreenRouter(menuData)
            } else {
              constRoutes = generateIndexRouter(menuData)
            }
            // 添加主界面路由
            store
              .dispatch('UpdateAppRouter', {
                constRoutes,
              })
              .then(() => {
                // 根据roles权限生成可访问的路由表
                // 动态添加可访问路由表
                router.addRoutes(store.getters.addRouters)
                that.$store.commit(RESET_MENUS,true)

                that.$router.push({
                  name: routerInfo.routerName,
                  path: routerInfo.redirect,
                  params: routerInfo.params,
                })
              })
          } else {
            alert(res.message)
          }
        })
        .catch((err) => {
          alert(err.message)
        })
    },

    goSearchResultPage(key) {
      if (key) {
        this.value = key;
      }
      if (!this.value.trim()) {
        this.$message.info("请输入搜索内容")
        return false
      }
      console.log("this.$route==",this.$route)
      console.log("this.$router==",this.$router)
      console.log("this.permissionList==",this.$store.state.user.permissionList)
      let routInfo={
        actionMenu: ['知识搜索'],
        targetMemuName: '知识管理/知识搜索',
        routerName: 'knowledgeManagement-knowledgeSearch-knowledgeSearch',
        redirect: '/knowledgeManagement/knowledgeSearch/knowledgeSearch',
        component:'opmg/knowledgeManagement/knowledgeSearch/knowledgeSearch',
        params: {
          routeName:this.$route.name,
          routePath:this.$route.path,
          routeFlag:"ywgzt",
          value: this.value
        }
      }
      this.jumpRouter(routInfo)
    },
  }
}
</script>

<style scoped lang="less">
.over {
  /* 溢出用省略号*/
  display: -webkit-box;
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
}

.search-box {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 24px;
  overflow: auto;

  inner-box{
    width: 100%;
    height: 100%;
  }

  .inner {
    height: 100%;
    min-width: 350px;
    min-height:200px;

    display: flex;
    flex-flow: column nowrap;
    justify-content: center;
    align-items: center;

    .header-box {
      display: flex;
      justify-content: center;
      align-items: center;

      .title {
        /*38/64*/
        font-size: var(--titleFontSize);
        //font-size: 0.59375rem;
        color: rgba(51, 51, 51, 0.85);
        letter-spacing: 3px;
        text-align: center;
        font-weight: 550;
      }

      .plane-img {
        ///*114/64*/
        //width: 1.78125rem;
        ///*75/64*/
        //height: 1.171875rem;

        width:var(--imgWidth);
        height:var(--imgHeight);
        position: relative;
        top: -22px;
        margin-left: 5px;
      }
    }

    .input-box {
      width: 100%;
      //margin-top: 60px;
      margin-top:var(--top);
      height: 54px;
      position: relative;

      .a-input {
        //width: calc(100% - 2.4375rem);
        width: calc(100% - var(--btnWidth));
        height: 100%;
        text-indent: 2em;
        border: 1px solid #E7E7E7;
        box-shadow: 2px 2px 6px 1px rgba(22, 27, 33, 0.16);
        border-radius: 4px;
        font-size: 18px;
      }

      .btn {
        /*156/64*/
        width: var(--btnWidth);
        //width: 2.4375rem;
        height: 100%;
        background: #409EFF;
        border-color: #409EFF;
        font-size: 18px;
        letter-spacing: 5px;
      }

      .line {
        position: absolute;
        top: 0;
        bottom: 0;
        margin: auto;
        left: 24px;
        width: 3px;
        height: 25px;
        background-color: #364d80;
        border-radius: 3px;
        z-index: 1;
      }
    }

    .keyword-box {
      min-height: 120px;

      .hot {
        width: 100%;
        font-size: 0.25rem;
        color: #F50909;
        letter-spacing: 0;
        text-align: left;
        padding-top: 30px;
      }

      .keyword {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        margin-top: 13px;

        .tag {
          background: #F1F1F1;
          border-radius: 4px;
          /*14/64*/
          font-size: 0.21875rem;
          color: rgba(0, 0, 0, 0.65);
          letter-spacing: 0.52px;
          font-weight: 400;
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          height: 30px;
          padding: 0 10px;
          margin-right: 10px;
          margin-bottom: 5px;
          cursor: pointer;
        }
      }
    }
  }
}
</style>