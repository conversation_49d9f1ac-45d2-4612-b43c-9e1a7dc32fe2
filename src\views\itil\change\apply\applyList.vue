<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll zhl zhll">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <!-- 查询区域 -->
        <div class="table-page-search-wrapper">
          <!-- 搜索区域 -->
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <!-- 上三选择 -->
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="类型">
                  <j-dict-select-tag v-model="queryParam.eventType" placeholder="请选择" dictCode="change_type" />
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="优先级">
                  <a-select placeholder="请选择" allowClear v-model="queryParam.priorityInt">
                    <a-select-option value="0">低</a-select-option>
                    <a-select-option value="1">中</a-select-option>
                    <a-select-option value="2">高</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="状态">
                  <a-select placeholder="请选择" allowClear v-model="queryParam.status">
                    <a-select-option value="0">新建</a-select-option>
                    <a-select-option value="1">审批</a-select-option>
                    <a-select-option value="2">处理</a-select-option>
                    <a-select-option value="6">实施</a-select-option>
                    <a-select-option value="3">审核</a-select-option>
                    <a-select-option value="4">关闭</a-select-option>
                    <!-- <a-select-option value="5">退回</a-select-option> -->
                  </a-select>
                </a-form-item>
              </a-col>

              <a-col :span="spanValue" v-show="toggleSearchStatus">
                <a-form-item label="标题">
                  <a-input placeholder="请输入" v-model="queryParam.title" :allowClear="true"></a-input>
                </a-form-item>
              </a-col>
              <!-- 按钮区域 -->
              <a-col :span="colBtnsSpan()">
                <span
                  class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                >
                  <a-button type="primary" class="btn-search btn-search-style" @click="searchQuery">查询</a-button>
                  <a-button @click="searchReset" class="btn-reset btn-reset-style">重置</a-button>
                  <a v-if="isVisible" class="btn-updown-style" @click="doToggleSearch">
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered="false" style="width: 100%; flex: auto">
        <!-- 下三按钮区 -->
        <div class="table-operator table-operator-style">
          <a-button @click="chooseProcess">新增</a-button>
          <!-- <a-button type="primary" style="margin-left: 8px">删除</a-button> -->
          <!-- <a-button type="primary" style="margin-left: 8px">批量导出</a-button> -->
        </div>
        <!-- table区域-begin -->
        <a-table
          ref="table"
          bordered
          rowKey="id"
          :columns="columns"
          :dataSource="dataSource"
          :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
          :pagination="ipagination"
          :loading="loading"
          :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          @change="handleTableChange"
        >
          <span slot="status" slot-scope="status, record">
            <div v-if="record.status == 0">新建</div>
            <div v-if="record.status == 1">审批</div>
            <div v-if="record.status == 2">处理</div>
            <div v-if="record.status == 3">审核</div>
            <div v-if="record.status == 4">关闭</div>
            <div v-if="record.status == 5">退回</div>
            <div v-if="record.status == 6">实施</div>
          </span>
          <span slot="result" slot-scope="result, record">
            <div v-if="record.result == 0">未提交</div>
            <div v-if="record.result == 1">处理中</div>
            <div v-if="record.result == 2" style="color: #139b33">已通过</div>
            <div v-if="record.result == 3" style="color: #df1a1a">已驳回</div>
          </span>
          <span slot="priority" slot-scope="priority, record">
            <div v-if="record.priority == 0" style="color: #ffb300">低</div>
            <div v-if="record.priority == 1" style="color: #fc7611">中</div>
            <div v-if="record.priority == 2" style="color: #df1a1a">高</div>
          </span>
          <span slot="slaResponse" slot-scope="slaResponse, record">
            <div v-if="record.slaResponseType == '0'" style="color: #139b33">
              {{ record.slaResponse }}
            </div>
            <div v-if="record.slaResponseType == '1'" style="color: #df1a1a">
              {{ record.slaResponse }}
            </div>
          </span>

          <span slot="slaAccomplish" slot-scope="slaAccomplish, record">
            <div v-if="record.slaAccomplishType == '0'" style="color: #139b33">
              {{ record.slaAccomplish }}
            </div>
            <div v-if="record.slaAccomplishType == '1'" style="color: #df1a1a">
              {{ record.slaAccomplish }}
            </div>
          </span>
          <span slot="eventType" slot-scope="eventType, record">
            <div v-if="record.eventType == 'cg'">常规变更</div>
            <div v-if="record.eventType == 'jj'">紧急变更</div>
          </span>
          <!-- 操作 -->
          <span slot="action" slot-scope="text, record" class="caozuo">
            <!-- <a @click="handleDeatails(record)">查看详情</a> -->
            <a v-if="record.status == 0" @click="apply(record)">提交申请</a>
            <a v-if="record.status != 0" @click="handleDeatails(record)">查看</a>
            <a-divider v-if="record.status == 0" type="vertical" />
            <a v-if="record.status == 0" @click="handleEdit(record)">编辑</a>
            <a-divider v-if="record.status == 0" type="vertical" />
            <a-popconfirm title="确定删除吗?" @confirm="() => remove(record)">
              <a v-if="record.status == 0">删除</a>
            </a-popconfirm>
          </span>
          <template slot="tooltip" slot-scope="text">
            <a-tooltip placement="topLeft" :title="text" trigger="hover">
              <div class="tooltip">
                {{ text }}
              </div>
            </a-tooltip>
          </template>
          <template slot="tooltip" slot-scope="text">
            <a-tooltip placement="topLeft" :title="text" trigger="hover">
              <div class="tooltip">
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
      </a-card>
      <!--提交申请表单-->
      <a-modal title="提交申请" v-model="modalVisible" :mask-closable="false" :width="500" :footer="null">
        <div v-if="modalVisible">
          <a-form-item label="选择审批人" v-show="showAssign">
            <!-- <a-select
            style="width: 100%"
            v-model="form.assignees"
            placeholder="请选择"
            mode="multiple"
            :allowClear="true"
          >
            <a-select-option
              v-for="(item, i) in assigneeList"
              :key="i"
              :value="item.username"
            >{{item.realname}}</a-select-option>
          </a-select> -->
            <l-select-user-by-dep v-model="form.assignees" :multi="false"></l-select-user-by-dep>
          </a-form-item>
          <a-form-item label="下一审批人" v-show="isGateway">
            <a-alert type="info" showIcon message="分支网关处不支持自定义选择下一审批人，将自动下发给所有可审批人。"
              >，将发送给下一节点所有人</a-alert
            >
          </a-form-item>
          <a-form-item label="消息通知">
            <a-checkbox v-model="form.sendMessage">站内消息通知</a-checkbox>
            <!-- <a-checkbox v-model="form.sendSms" disabled>短信通知</a-checkbox>
          <a-checkbox v-model="form.sendEmail" disabled>邮件通知</a-checkbox> -->
          </a-form-item>
          <div slot="footer" style="text-align: right">
            <a-button type="text" @click="modalVisible = false">取消</a-button>
            <div style="display: inline-block; width: 20px"></div>
            <a-button type="primary" :disabled="submitLoading" @click="applySubmit">提交</a-button>
          </div>
        </div>
      </a-modal>
      <applyAdd ref="applyAdd" @ok="modalFormOk"></applyAdd>
      <!-- <applyEdit ref="applyEdit" @ok="modalFormOk"></applyEdit> -->
      <applyDetails ref="applyDetails" @ok="modalFormOk"></applyDetails>
    </a-col>
  </a-row>
</template>
<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import applyAdd from './modules/applyAdd'

// import applyEdit from './modules/applyEdit'
import applyDetails from './modules/applyDetails'
import LSelectUserByDep from '@/components/jeecgbiz/LSelectUserByDep'
import { deleteAction, getAction, downFile, postAction } from '@/api/manage'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
export default {
  name: 'applyList',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components: {
    applyAdd,
    // applyEdit,
    applyDetails,
    LSelectUserByDep,
  },
  data() {
    return {
      description: '我的申请',
      formItemLayout: {
        labelCol: {
          style: 'width:80px',
        },
        wrapperCol: {
          style: 'width:calc(100% - 80px)'
        }
      },
      // 表头
      form: {
        priority: 0,
        assignees: [],
        sendMessage: true,
      },
      modalVisible: false,
      submitLoading: false,
      isGateway: false,
      assigneeList: [],
      columns: [
        // {
        //     title: "#",
        //     dataIndex: "",
        //     key: "rowIndex",
        //     width: 60,
        //     align: "center",
        //     customRender: function(t, r, index) {
        //         return parseInt(index) + 1;
        //     }
        // },
        {
          title: '编号',
          dataIndex: 'processNumber',
        },
        {
          title: '标题',
          dataIndex: 'title',
          scopedSlots: { customRender: 'tooltip' },
        },
        {
          title: '类型',
          dataIndex: 'eventType',
          scopedSlots: { customRender: 'eventType' },
        },
        {
          title: '优先级',
          dataIndex: 'priority',
          scopedSlots: { customRender: 'priority' },
        },
        {
          title: '状态',
          dataIndex: 'status',
          scopedSlots: { customRender: 'status' },
        },
        {
          title: '结果',
          dataIndex: 'result',
          scopedSlots: { customRender: 'result' },
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
        },
        {
          title: 'SLA响应',
          dataIndex: 'slaResponse',
          scopedSlots: { customRender: 'slaResponse' },
        },
        {
          title: 'SLA完成',
          dataIndex: 'slaAccomplish',
          scopedSlots: { customRender: 'slaAccomplish' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 250,
          scopedSlots: { customRender: 'action' },
        },
      ],
      url: {
        list: '/change/listData',
        getFirstNode: '/actProcessIns/getFirstNode',
        applyBusiness: '/actBusiness/apply',
        delByIds: '/actBusiness/delByIds',
      },
      uid: this.$route.query.id,
      eventId: this.$route.query.eventId,
      questionId: this.$route.query.questionId,
    }
  },
  mounted() {
    if (this.uid) {
      this.chooseProcess()
    }
  },
  computed: {
    //可行性测试，根据文件路径动态加载组件
    LcDict: function () {
      var myComponent = () => import(`@/components/dict/JDictSelectTag`)
      return myComponent
    },
  },
  methods: {
    //添加流程
    chooseProcess() {
      let v = { formKey: 'change_form' }
      if (!v.formKey) {
        this.$message.warning('该流程信息未配置表单，请联系开发人员！')
        return
      }
      this.$refs.applyAdd.title = '新建变更'
      this.$refs.applyAdd.initData(v, this.eventId, this.questionId)
    },
    //编辑
    handleEdit(r) {
      if (!r.formKey) {
        this.$message.warning('该流程信息未配置表单，请联系开发人员！')
        return
      }
      this.$refs.applyAdd.initData(r, this.eventId)
      this.$refs.applyAdd.title = '编辑'
    },
    //删除
    remove(r) {
      let ids = []
      postAction(this.url.delByIds, { ids: r.id }).then((res) => {
        if (res.success) {
          this.$message.success(res.message)
          this.loadData()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    //提交申请弹框
    apply(v) {
      if (!v.procDefId || v.procDefId == 'null') {
        this.$message.error('流程定义为空')
        return
      }
      this.form.id = v.id
      this.form.procDefId = v.procDefId
      this.form.title = v.title

      this.form.assignees = ''
      this.modalVisible = true
      this.isGateway = false
      this.form.firstGateway = true
      this.showAssign = true
      // 加载审批人
      // getAction(this.url.getFirstNode, { procDefId: v.procDefId }).then((res) => {
      //   if (res.success) {
      //     if (res.result.type == 3 || res.result.type == 4) {
      //       this.isGateway = true
      //       this.modalVisible = true
      //       this.form.firstGateway = true
      //       this.showAssign = false
      //       this.error = ''
      //       return
      //     }
      //     this.form.firstGateway = false
      //     this.isGateway = false
      //     if (res.result.users && res.result.users.length > 0) {
      //       this.error = ''
      //       this.assigneeList = res.result.users
      //       // 默认勾选
      //       let ids = []
      //       res.result.users.forEach((e) => {
      //         ids.push(e.username)
      //       })
      //       this.form.assignees = ids
      //       this.showAssign = true
      //     } else {
      //       this.form.assignees = []
      //       this.showAssign = true
      //       this.error = '审批节点未分配候选审批人员，请联系管理员！'
      //     }
      //     if (this.error) {
      //       this.$message.error(this.error)
      //       return
      //     }
      //     this.modalVisible = true
      //   } else {
      //     this.$message.error(res.message)
      //   }
      // })
    },
    //申请提交
    applySubmit() {
      if (this.showAssign && this.form.assignees.length < 1) {
        this.error = '请至少选择一个审批人'
        this.$message.error(this.error)
        return
      } else {
        this.error = ''
      }
      this.submitLoading = true
      var params = Object.assign({}, this.form)
      // params.assignees = params.assignees.join(',')
      postAction(this.url.applyBusiness, params)
        .then((res) => {
          if (res.success) {
            this.$message.success('操作成功')
            this.loadData()
            this.modalVisible = false
          } else {
            this.$message.error(res.message)
          }
        })
        .finally(() => (this.submitLoading = false))
    },
    //  handleAdds: function () {
    //   this.$refs.modalForm.add();
    //   this.$refs.modalForm.title = "新建事件";
    //   this.$refs.modalForm.disableSubmit = false;
    // },
    // handleEdit: function(record) {
    // this.$refs.applyEdit.edit(record);
    // this.$refs.applyEdit.title = "编辑事件";
    // this.$refs.applyEdit.disableSubmit = false;
    // },
    handleDeatails: function (record) {
      this.$refs.applyDetails.edit(record)
      this.$refs.applyDetails.title = '事件详情'
      this.$refs.applyDetails.disableSubmit = false
    },
  },
}
</script>
<style lang="less" scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
/*给table列设置宽度*/
::v-deep .ant-table-scroll .ant-table-thead > tr > th,
::v-deep .ant-table-scroll .ant-table-tbody > tr > td {
  /*编号*/

  &:nth-child(2) {
    min-width: 100px;
    max-width: 300px;
  }

  /*标题*/

  &:nth-child(3) {
    min-width: 100px;
    max-width: 300px;
  }

  /*类型*/

  &:nth-child(4) {
    min-width: 50px;
    max-width: 150px;
  }

  /*优先级*/

  &:nth-child(5) {
    min-width: 50px;
    max-width: 100px;
  }

  /*状态*/

  &:nth-child(6) {
    min-width: 50px;
    max-width: 100px;
  }
  /*结果*/

  &:nth-child(7) {
    min-width: 50px;
    max-width: 300px;
  }
  /*创建时间*/

  &:nth-child(8) {
    min-width: 100px;
    max-width: 300px;
  }
  /*SLA响应*/

  &:nth-child(9) {
    min-width: 100px;
    max-width: 300px;
  }
  /*SLA完成*/

  &:nth-child(10) {
    min-width: 100px;
    max-width: 300px;
  }
}

/*表头样式*/
::v-deep .ant-table-thead > tr > th {
  text-align: center;
  white-space: nowrap;
}

/*内容对齐方式、省略显示*/
::v-deep .ant-table-tbody > tr > td {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;

  &:first-child,
  &:nth-child(2),
  &:nth-child(4),
  &:nth-child(5),
  &:nth-child(6),
  &:nth-child(8),
  &:nth-child(7) {
    text-align: center;
  }
  &:nth-child(3) {
    text-align: left;
  }
  &:nth-child(9),
  &:nth-child(10) {
    text-align: right;
  }
}
</style>
