<template>
  <div
    v-show="tipShow"
    ref="tip"
    class="tip-div"
    :style="{  top: top + 'px', left: left + 'px' }"
    @mouseenter="enterTip"
    @mouseleave="leaveTip"
  >
    <!-- 拓扑图弹窗展示内容 -->
    <keep-alive>
      <component v-show="tipType" :is="tipType" v-bind="bindData" :globalGridAttr='globalGridAttr' ref='topoTip'></component>
    </keep-alive>
  </div>
</template>

<script>
import TestLink from './TestLink.vue'
import DeviceInfoTip from './DeviceInfoTip'
import EdgeTip from './EdgeTip'
import AppDeviceTip from './AppDeviceTip'
export default {
  components: {
    TestLink,
    DeviceInfoTip,
    EdgeTip,
    AppDeviceTip
  },
  name: 'TopoTip',
  props: {
    width: {
      type: Number,
      default: 200,
    },
    globalGridAttr: {
      type: Object,
      default: null,
      required: true,
    },
  },
  data() {
    return {
      tipShow: false,
      top: 500,
      left: 300,
      cell: null,
      nodeId: '',
      tipType: '',
      bindData: {},
      isEnter: false,
      hideTimer: null,
    }
  },
  computed: {},
  methods: {
    /* 显示弹窗
    *弹窗必传参数：
    type:弹窗显示内容的组件类型；
    cell:节点/边
    pos:鼠标位置数据
    */
    show(param) {
      this.tipType = param.type
      this.bindData = param
      // 判断当前节点的弹窗是否显示
      if (this.cell && this.cell.id == param.cell.id && this.tipShow && this.tipType === param.type) return
      this.cell = param.cell
      this.bindData.cell = param.cell;
      this.tipShow = true
      // 改变弹窗的位置 将弹窗移动到鼠标位置
      this.$nextTick(() => {
        let parentBox = this.$parent.$refs.container.getBoundingClientRect()
        let box = this.$refs.tip.getBoundingClientRect()
        let disX = 0
        let disY = 30
        if (this.cell.isEdge()) {
          disY = 10
        }
        // 判断弹窗是否超出拓扑图的范围
        if (param.pos.offsetX + box.width < parentBox.width) {
          this.left = param.pos.offsetX - disX
        } else if (param.pos.offsetX + box.width > parentBox.width) {
          this.left = param.pos.offsetX - box.width
        } else {
          this.left = param.pos.offsetX - box.width / 2 - disX
        }
        if (param.pos.offsetY + box.height < parentBox.height) {
          this.top = param.pos.offsetY + disY
        } else if (param.pos.offsetY + box.height > parentBox.height) {
          this.top = param.pos.offsetY - box.height - disY
        } else {
          this.top = param.pos.offsetY - box.height - disY
        }
        if(this.$refs.topoTip.init){
          this.$refs.topoTip.init(param.vEdge)
        }

      })
    },
    // 鼠标移入提示弹窗
    enterTip() {
      this.isEnter = true
    },
    // 鼠标移出提示弹窗
    leaveTip() {
      this.isEnter = false
      this.hide()
    },
    /*
    *隐藏弹窗
    *immediately 是否立即隐藏弹窗 不做延时判断
    */
    hide(immediately) {
      if (immediately) {
        this.tipType = ''
        this.tipShow = false
        this.top = -10000
        this.left = -10000
        return
      }
      this.hideTimer = setTimeout(() => {
        if (!this.isEnter) {
          if (this.$parent.enterNode && this.cell && this.cell.id === this.$parent.enterNode.id) {
          } else {
            this.tipType = ''
            this.tipShow = false
            this.top = -10000
            this.left = -10000
          }
        }
        this.clearHideTimer()
      }, 100)
    },
    // 清除弹窗隐藏定时器
    clearHideTimer() {
      if (this.hideTimer) {
        clearTimeout(this.hideTimer)
        this.hideTimer = null
      }
    },
  },
}
</script>

<style scoped lang="less">
.tip-div {
  min-height: 20px;
  min-width: 200px;
  //overflow: hidden;
  background-color: rgba(29, 78, 140, 0.8);
  border-radius: 5px;
  position: absolute;
  z-index: 1000;
  padding: 10px;
  top: 0px;
}
::v-deep .ant-descriptions-item-content {
  max-width: 230px !important;
}
</style>