<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24"> </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <!-- <a-button type="primary" icon="download" @click="handleExportXls('编辑生成')">导出</a-button>
      <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl" @change="handleImportExcel">
        <a-button type="primary" icon="import">导入</a-button>
      </a-upload> -->
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay" style='text-align: center'>
          <a-menu-item key="1" @click="batchDel">删除</a-menu-item>
        </a-menu>
        <a-button> 批量操作 <a-icon type="down"/></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>

      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        class="j-table-force-nowrap"
        @change="handleTableChange"
      >
        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 14px; ">无图片</span>
          <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width:80px;font-size: 14px; " />
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 14px; ">无文件</span>
          <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="uploadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>

          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down"/></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>
      </a-table>
    </div>

    <sysSerialNumber-modal ref="modalForm" @ok="modalFormOk"></sysSerialNumber-modal>
  </a-card>
</template>

<script>
// import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import SysSerialNumberModal from './modules/SysSerialNumberModal'

export default {
  name: 'SysSerialNumberList',
  mixins: [JeecgListMixin, mixinDevice],
  components: {
    SysSerialNumberModal
  },
  data() {
    return {
      description: '编辑生成管理页面',
      // 表头
      columns: [
        {
          title: '#',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function(t, r, index) {
            return parseInt(index) + 1
          }
        },
        {
          title: '模块名称',
          align: 'center',
          dataIndex: 'moduleName'
        },
        {
          title: '模块编码',
          align: 'center',
          dataIndex: 'moduleCode'
        },
        {
          title: '序列号模板',
          align: 'center',
          dataIndex: 'configTemplet'
        },
        {
          title: '当前序列号',
          align: 'center',
          dataIndex: 'maxSerial'
        },
        {
          title: '当前序列号最大值',
          align: 'center',
          dataIndex: 'preMaxNum'
        },
        {
          title: '是否自动增长',
          align: 'center',
          dataIndex: 'isAutoIncrement'
        },

        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          // fixed:"right",
          width: 147,
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: '/sysserialnumber/sysSerialNumber/list',
        delete: '/sysserialnumber/sysSerialNumber/delete',
        deleteBatch: '/sysserialnumber/sysSerialNumber/deleteBatch'
        // exportXlsUrl: "/sysserialnumber/sysSerialNumber/exportXls",
        // importExcelUrl: "sysserialnumber/sysSerialNumber/importExcel",
      },
      dictOptions: {}
    }
  },
  computed: {
    importExcelUrl: function() {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    }
  },
  methods: {
    initDictConfig() {}
  }
}
</script>
<style scoped>
/* @import '~@assets/less/common.less'; */
</style>
