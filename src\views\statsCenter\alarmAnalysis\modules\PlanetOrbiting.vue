<template>
  <div class="palnet-box">
    <div class="section3" :class="{ 'ani-paused': paused }" :style="{ transform: `scale(${scaleNum})` }">
      <div class="u_fr">
        <div class="ui_base u_p3d">
          <div class="ball_c">
            <img class="triangle-up" src="/statsCenter/alarm/triangle-up.png" alt="" />
            <span class="all-title">告警总数</span>
            <div class="all-num">
              <animate-number
                :from="startNum"
                :to="allNum"
                duration="5000"
                :key="'an_' + allNum"
                :animateEnd="animateEnd"
              >
              </animate-number>
            </div>
            <img class="triangle-down" src="/statsCenter/alarm/triangle-down.png" alt="" />
          </div>
          <div class="base u_p3d">
            <div class="ball_base u_p3d ball_1">
              <div class="ball">
                <div class="ball-info">
                  <img class="product-image" src="/statsCenter/alarm/safe.png" alt="" />
                  <div class="pro-info">
                    <span v-if="alarmData[5]">{{ alarmData[5].name }}：{{ alarmData[5].value }}</span>
                  </div>
                  <img class="zuo-image" src="/statsCenter/alarm/zuo-2.png" alt="" />
                </div>
              </div>
            </div>
            <div class="ball_base u_p3d ball_2">
              <div class="ball">
                <div class="ball-info">
                  <img class="product-image" src="/statsCenter/alarm/desktop_planet.png" alt="" />
                  <div class="pro-info">
                    <span v-if="alarmData[4]">{{ alarmData[4].name }}：{{ alarmData[4].value }}</span>
                  </div>
                  <img class="zuo-image" src="/statsCenter/alarm/zuo-1.png" alt="" />
                </div>
              </div>
            </div>
            <div class="ball_base u_p3d ball_3">
              <div class="ball">
                <div class="ball-info">
                  <img class="product-image" src="/statsCenter/alarm/middare.png" alt="" />
                  <div class="pro-info">
                    <span v-if="alarmData[3]">{{ alarmData[3].name }}：{{ alarmData[3].value }}</span>
                  </div>
                  <img class="zuo-image" src="/statsCenter/alarm/zuo-2.png" alt="" />
                </div>
              </div>
            </div>
            <div class="ball_base u_p3d ball_4">
              <div class="ball">
                <div class="ball-info">
                  <img class="product-image" src="/statsCenter/alarm/database_planet.png" alt="" />
                  <div class="pro-info">
                    <span v-if="alarmData[2]">{{ alarmData[2].name }}：{{ alarmData[2].value }}</span>
                  </div>
                  <img class="zuo-image" src="/statsCenter/alarm/zuo-1.png" alt="" />
                </div>
              </div>
            </div>
            <div class="ball_base u_p3d ball_5">
              <div class="ball">
                <div class="ball-info">
                  <img class="product-image" src="/statsCenter/alarm/network.png" alt="" />
                  <div class="pro-info">
                    <span v-if="alarmData[1]">{{ alarmData[1].name }}：{{ alarmData[1].value }}</span>
                  </div>
                  <img class="zuo-image" src="/statsCenter/alarm/zuo-2.png" alt="" />
                </div>
              </div>
            </div>
            <div class="ball_base u_p3d ball_6">
              <div class="ball">
                <div class="ball-info">
                  <img class="product-image" src="/statsCenter/alarm/server_planet.png" alt="" />
                  <div class="pro-info">
                    <span v-if="alarmData[0]">{{ alarmData[0].name }}：{{ alarmData[0].value }}</span>
                  </div>
                  <img class="zuo-image" src="/statsCenter/alarm/zuo-1.png" alt="" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import resizeObserverMixin from '@views/statsCenter/com/resizeObserverMixin'
export default {
  mixins: [resizeObserverMixin],
  props: {
    alarmData: {
      type: Array,
      default: () => [],
    },
    allNum: {
      type: Number,
      default: 0,
    },
    // 极简模式暂停旋转动画
    paused: {
      typeof: Boolean,
      default: false,
    },
  },
  data() {
    return {
      startNum: 0,
    }
  },
  computed: {},
  mounted() {},

  methods: {
    animateEnd(e) {
      this.startNum = e
    },
  },
}
</script>

<style scoped lang="less">
.palnet-box {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  position: relative;
}
.section3 {
  position: absolute;
  width: calc(623px * 1.3);
  height: calc(310px * 1.3);
  // margin: 40px auto 0px;
  /* background-color: aliceblue; */
   background: url(/statsCenter/alarm/cir-bg.png) 50% 50% no-repeat;
  // background: url(/statsCenter/alarm/cir-a.png) 50% 50% no-repeat, url(/statsCenter/alarm/cir-a-1.png) 50% 50% no-repeat,
  //   url(/statsCenter/alarm/cir-b.png) 50% 50% no-repeat, url(/statsCenter/alarm/cir-b-1.png) 50% 50% no-repeat;
}

.u-fr {
}

.u_p3d {
  -webkit-transform-style: preserve-3d;
  -ms-transform-style: preserve-3d;
  -o-transform-style: preserve-3d;
  -moz-transform-style: preserve-3d;
  transform-style: preserve-3d;
}

.section3 .ui_base {
  position: relative;
  /* background-color: #fff; */
  /* transform: scale(0.6); */
  transform-origin: center;
  /* width: 400px;
    height: 400px; */
  -webkit-perspective: 1000px;
  -moz-perspective: 1000px;
  -ms-perspective: 1000px;
  -o-perspective: 1000px;
  perspective: 1000px;
  -webkit-perspective-origin: 50% 0%;
  -moz-perspective-origin: 50% 0%;
  -o-perspective-origin: 50% 0%;
  -ms-perspective-origin: 50% 0%;
  perspective-origin: 50% 0%;
  left: 240px;
  top: 30px;
}

.section3 .ball_c {
  -webkit-transform-origin: 50% 50%;
  -moz-transform-origin: 50% 50%;
  -ms-transform-origin: 50% 50%;
  -o-transform-origin: 50% 50%;
  transform-origin: 50% 50%;
  position: absolute;
  width: 223px;
  height: 245px;
  background: url(/statsCenter/alarm/zt.png) no-repeat;
  left: 60px;
  top: -20px;

  .triangle-up {
    position: absolute;
    top: 60px;
    left: 20px;
  }

  .triangle-down {
    position: absolute;
    top: 168px;
    left: 90px;
  }

  .all-title {
    position: absolute;
    top: 85px;
    left: 72px;
    font-size: 23px;
    font-family: Adobe Heiti Std;
    font-weight: normal;
    color: #19fdff;
  }

  .all-num {
    position: absolute;
    width: 240px;
    text-align: center;
    top: 118px;
    left: 0px;
    font-size: 32px;
    font-family: zhengku;
    color: #e4d759;

    background: linear-gradient(180deg, #e4d759 0%, #ffffff 86.8896484375%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-weight: 400;
  }
}

.ball-info {
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  backface-visibility: hidden;
}

.pro-info {
  height: 18px;
  margin-top: -5px;
  color: #fff;
  text-align: center;
}

.product-image {
  width: 60px;
  height: 60px;
}

.zuo-image {
  margin-top: -30px;
  /* position: absolute; */
  width: 153px;
  height: 45px;
}

.section3 .base {
  /* -webkit-transform: rotateX(80deg) rotateY(-10deg);
    -moz-transform: rotateX(80deg) rotateY(-10deg);
    -o-transform: rotateX(80deg) rotateY(-10deg);
    -ms-transform: rotateX(80deg) rotateY(-10deg); */
  transform: rotateX(80deg) rotateY(10deg);
  position: relative;
  width: 350px;
  height: 350px;
  // background-color: red;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  top: 0px;
  left: 0px;
}

.section3 .ball_base {
  -webkit-transform-origin: 180px 0px;
  -moz-transform-origin: 180px 0px;
  -ms-transform-origin: 180px 0px;
  -o-transform-origin: 180px 0px;
  transform-origin: 180px 0px;
  position: absolute;
  top: 176px;
  left: -5px;
  width: 225px;
  height: 127px;
  // background-color: blue;
}

.section3 .ball_1 {
  -webkit-transform: rotateX(-90deg) rotateY(0deg) translateY(-70px);
  -o-transform: rotateX(-90deg) rotateY(0deg) translateY(-70px);
  -moz-transform: rotateX(-90deg) rotateY(0deg) translateY(-70px);
  -ms-transform: rotateX(-90deg) rotateY(0deg) translateY(-70px);
  transform: rotateX(-90deg) rotateY(0deg) translateY(-70px);
}

.section3 .ball_2 {
  -webkit-transform: rotateX(-90deg) rotateY(60deg) translateY(-70px);
  -moz-transform: rotateX(-90deg) rotateY(60deg) translateY(-70px);
  -o-transform: rotateX(-90deg) rotateY(60deg) translateY(-70px);
  -ms-transform: rotateX(-90deg) rotateY(60deg) translateY(-70px);
  transform: rotateX(-90deg) rotateY(60deg) translateY(-70px);
}

.section3 .ball_3 {
  -webkit-transform: rotateX(-90deg) rotateY(120deg) translateY(-70px);
  -o-transform: rotateX(-90deg) rotateY(120deg) translateY(-70px);
  -ms-transform: rotateX(-90deg) rotateY(120deg) translateY(-70px);
  -moz-transform: rotateX(-90deg) rotateY(120deg) translateY(-70px);
  transform: rotateX(-90deg) rotateY(120deg) translateY(-70px);
}

.section3 .ball_4 {
  -webkit-transform: rotateX(-90deg) rotateY(180deg) translateY(-70px);
  -moz-transform: rotateX(-90deg) rotateY(180deg) translateY(-70px);
  -ms-transform: rotateX(-90deg) rotateY(180deg) translateY(-70px);
  -o-transform: rotateX(-90deg) rotateY(180deg) translateY(-70px);
  transform: rotateX(-90deg) rotateY(180deg) translateY(-70px);
}

.section3 .ball_5 {
  -webkit-transform: rotateX(-90deg) rotateY(240deg) translateY(-70px);
  -moz-transform: rotateX(-90deg) rotateY(240deg) translateY(-70px);
  -o-transform: rotateX(-90deg) rotateY(240deg) translateY(-70px);
  -ms-transform: rotateX(-90deg) rotateY(240deg) translateY(-70px);
  transform: rotateX(-90deg) rotateY(240deg) translateY(-70px);
}

.section3 .ball_6 {
  -webkit-transform: rotateX(-90deg) rotateY(300deg) translateY(-70px);
  -ms-transform: rotateX(-90deg) rotateY(300deg) translateY(-70px);
  -o-transform: rotateX(-90deg) rotateY(300deg) translateY(-70px);
  -moz-transform: rotateX(-90deg) rotateY(300deg) translateY(-70px);
  transform: rotateX(-90deg) rotateY(300deg) translateY(-70px);
}

.section3 .ball_1 .ball {
  -webkit-transform: rotateY(10deg) rotateZ(10deg);
  -moz-transform: rotateY(10deg) rotateZ(10deg);
  -o-transform: rotateY(10deg) rotateZ(10deg);
  -ms-transform: rotateY(10deg) rotateZ(10deg);
  transform: rotateY(10deg) rotateZ(10deg);
}

.section3 .ball_2 .ball {
  -webkit-transform: rotateY(-50deg) rotateZ(10deg);
  -o-transform: rotateY(-50deg) rotateZ(10deg);
  -ms-transform: rotateY(-50deg) rotateZ(10deg);
  -moz-transform: rotateY(-50deg) rotateZ(10deg);
  transform: rotateY(-50deg) rotateZ(10deg);
}

.section3 .ball_3 .ball {
  -webkit-transform: rotateY(-110deg) rotateZ(10deg);
  -o-transform: rotateY(-110deg) rotateZ(10deg);
  -moz-transform: rotateY(-110deg) rotateZ(10deg);
  -ms-transform: rotateY(-110deg) rotateZ(10deg);
  transform: rotateY(-110deg) rotateZ(10deg);
}

.section3 .ball_4 .ball {
  -webkit-transform: rotateY(-170deg) rotateZ(10deg);
  -o-transform: rotateY(-170deg) rotateZ(10deg);
  -moz-transform: rotateY(-170deg) rotateZ(10deg);
  -ms-transform: rotateY(-170deg) rotateZ(10deg);
  transform: rotateY(-170deg) rotateZ(10deg);
}

.section3 .ball_5 .ball {
  -webkit-transform: rotateY(-230deg) rotateZ(10deg);
  -o-transform: rotateY(-230deg) rotateZ(10deg);
  -moz-transform: rotateY(-230deg) rotateZ(10deg);
  -ms-transform: rotateY(-230deg) rotateZ(10deg);
  transform: rotateY(-230deg) rotateZ(10deg);
}

.section3 .ball_6 .ball {
  -webkit-transform: rotateY(-290deg) rotateZ(10deg);
  -o-transform: rotateY(-290deg) rotateZ(10deg);
  -ms-transform: rotateY(-290deg) rotateZ(10deg);
  -moz-transform: rotateY(-290deg) rotateZ(10deg);
  transform: rotateY(-290deg) rotateZ(10deg);
}

.section3 .ball {
  -webkit-transition: all 2s ease-out 0ms;
  -o-transition: all 2s ease-out 0ms;
  -moz-transition: all 2s ease-out 0ms;
  -ms-transition: all 2s ease-out 0ms;
  transition: all 2s ease-out 0ms;
  -webkit-transform-origin: 50% 50%;
  -o-transform-origin: 50% 50%;
  -ms-transform-origin: 50% 50%;
  -moz-transform-origin: 50% 50%;
  transform-origin: 50% 50%;
  position: absolute;
  left: -200px;
  top: -30px;
  overflow: hidden;
}

.section3 .ball_1 .ball {
  -webkit-transition-delay: 1100ms;
  -o-transition-delay: 1100ms;
  -moz-transition-delay: 1100ms;
  -ms-transition-delay: 1100ms;
  transition-delay: 1100ms;
}

.section3 .ball_2 .ball {
  -webkit-transition-delay: 900ms;
  -moz-transition-delay: 900ms;
  -o-transition-delay: 900ms;
  -ms-transition-delay: 900ms;
  transition-delay: 900ms;
}

.section3 .ball_3 .ball {
  -webkit-transition-delay: 700ms;
  -o-transition-delay: 700ms;
  -ms-transition-delay: 700ms;
  -moz-transition-delay: 700ms;
  transition-delay: 700ms;
}

.section3 .ball_4 .ball {
  -webkit-transition-delay: 500ms;
  -o-transition-delay: 500ms;
  -ms-transition-delay: 500ms;
  -moz-transition-delay: 500ms;
  transition-delay: 500ms;
}

.section3 .ball_5 .ball {
  -webkit-transition-delay: 300ms;
  -moz-transition-delay: 300ms;
  -ms-transition-delay: 300ms;
  -o-transition-delay: 300ms;
  transition-delay: 300ms;
}

.section3 .ball_6 .ball {
  -webkit-transition-delay: 100ms;
  -o-transition-delay: 100ms;
  -ms-transition-delay: 100ms;
  -moz-transition-delay: 100ms;
  transition-delay: 100ms;
}

.section3 .ball_1 .ball {
  -webkit-animation: cir1 28s linear 0s infinite;
  -o-animation: cir1 28s linear 0s infinite;
  -ms-animation: cir1 28s linear 0s infinite;
  -moz-animation: cir1 28s linear 0s infinite;
  animation: cir1 28s linear 0s infinite;
}

.section3 .ball_2 .ball {
  -webkit-animation: cir2 28s linear 0s infinite;
  -o-animation: cir2 28s linear 0s infinite;
  -ms-animation: cir2 28s linear 0s infinite;
  -moz-animation: cir2 28s linear 0s infinite;
  animation: cir2 28s linear 0s infinite;
}

.section3 .ball_3 .ball {
  -webkit-animation: cir3 28s linear 0s infinite;
  -o-animation: cir3 28s linear 0s infinite;
  -ms-animation: cir3 28s linear 0s infinite;
  -moz-animation: cir3 28s linear 0s infinite;
  animation: cir3 28s linear 0s infinite;
}

.section3 .ball_4 .ball {
  -webkit-animation: cir4 28s linear 0s infinite;
  -o-animation: cir4 28s linear 0s infinite;
  -ms-animation: cir4 28s linear 0s infinite;
  -moz-animation: cir4 28s linear 0s infinite;
  animation: cir4 28s linear 0s infinite;
}

.section3 .ball_5 .ball {
  -webkit-animation: cir5 28s linear 0s infinite;
  -o-animation: cir5 28s linear 0s infinite;
  -ms-animation: cir5 28s linear 0s infinite;
  -moz-animation: cir5 28s linear 0s infinite;
  animation: cir5 28s linear 0s infinite;
}

.section3 .ball_6 .ball {
  -webkit-animation: cir6 28s linear 0s infinite;
  -o-animation: cir6 28s linear 0s infinite;
  -ms-animation: cir6 28s linear 0s infinite;
  -moz-animation: cir6 28s linear 0s infinite;
  animation: cir6 28s linear 0s infinite;
}

.section3 .base {
  -webkit-animation: cir 28s linear 0s infinite;
  -o-animation: cir 28s linear 0s infinite;
  -ms-animation: cir 28s linear 0s infinite;
  -mozanimation: cir 28s linear 0s infinite;
  animation: cir 28s linear 0s infinite;
}
.ani-paused {
  .base {
    animation-play-state: paused;
    .ball_1 {
      .ball {
        animation-play-state: paused;
      }
    }
    .ball_2 {
      .ball {
        animation-play-state: paused;
      }
    }
    .ball_3 {
      .ball {
        animation-play-state: paused;
      }
    }
    .ball_4 {
      .ball {
        animation-play-state: paused;
      }
    }
    .ball_5 {
      .ball {
        animation-play-state: paused;
      }
    }
    .ball_6 {
      .ball {
        animation-play-state: paused;
      }
    }
  }
}
.ani-paused:hover {
  .base {
    animation-play-state: running;
    .ball_1 {
      .ball {
        animation-play-state: running;
      }
    }
    .ball_2 {
      .ball {
        animation-play-state: running;
      }
    }
    .ball_3 {
      .ball {
        animation-play-state: running;
      }
    }
    .ball_4 {
      .ball {
        animation-play-state: running;
      }
    }
    .ball_5 {
      .ball {
        animation-play-state: running;
      }
    }
    .ball_6 {
      .ball {
        animation-play-state: running;
      }
    }
  }
}
@-webkit-keyframes cir1 {
  0% {
    transform: rotateY(-360deg) rotateZ(-10deg);
  }

  100% {
    transform: rotateY(0deg) rotateZ(-10deg);
  }
}

@keyframes cir1 {
  0% {
    transform: rotateY(-360deg) rotateZ(-10deg);
  }

  100% {
    transform: rotateY(0deg) rotateZ(-10deg);
  }
}

@-webkit-keyframes cir2 {
  0% {
    -webkit-transform: rotateY(-420deg) rotateZ(-10deg);
  }

  100% {
    -webkit-transform: rotateY(-60deg) rotateZ(-10deg);
  }
}

@keyframes cir2 {
  0% {
    -webkit-transform: rotateY(-420deg) rotateZ(-10deg);
  }

  100% {
    -webkit-transform: rotateY(-60deg) rotateZ(-10deg);
  }
}

@-webkit-keyframes cir3 {
  0% {
    transform: rotateY(-480deg) rotateZ(-10deg);
  }

  100% {
    transform: rotateY(-120deg) rotateZ(-10deg);
  }
}

@keyframes cir3 {
  0% {
    transform: rotateY(-480deg) rotateZ(-10deg);
  }

  100% {
    transform: rotateY(-120deg) rotateZ(-10deg);
  }
}

@-webkit-keyframes cir4 {
  0% {
    transform: rotateY(-540deg) rotateZ(-10deg);
  }

  100% {
    transform: rotateY(-180deg) rotateZ(-10deg);
  }
}

@keyframes cir4 {
  0% {
    transform: rotateY(-540deg) rotateZ(-10deg);
  }

  100% {
    transform: rotateY(-180deg) rotateZ(-10deg);
  }
}

@-webkit-keyframes cir5 {
  0% {
    -webkit-transform: rotateY(-600deg) rotateZ(-10deg);
  }

  100% {
    -webkit-transform: rotateY(-240deg) rotateZ(-10deg);
  }
}

@keyframes cir5 {
  0% {
    transform: rotateY(-600deg) rotateZ(-10deg);
  }

  100% {
    transform: rotateY(-240deg) rotateZ(-10deg);
  }
}

@-webkit-keyframes cir6 {
  0% {
    transform: rotateY(-660deg) rotateZ(-10deg);
  }

  100% {
    transform: rotateY(-300deg) rotateZ(-10deg);
  }
}

@keyframes cir6 {
  0% {
    transform: rotateY(-660deg) rotateZ(-10deg);
  }

  100% {
    transform: rotateY(-300deg) rotateZ(-10deg);
  }
}

@keyframes cir {
  0% {
    transform: rotateX(80deg) rotateY(10deg) rotateZ(0deg);
  }

  100% {
    transform: rotateX(80deg) rotateY(10deg) rotateZ(360deg);
  }
}

@-webkit-keyframes cir {
  0% {
    -webkit-transform: rotateX(80deg) rotateY(10deg) rotateZ(0deg);
  }

  100% {
    -webkit-transform: rotateX(80deg) rotateY(10deg) rotateZ(360deg);
  }
}
</style>