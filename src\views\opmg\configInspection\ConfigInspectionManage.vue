<template>
  <div style="height: 100%">
    <component :is="pageName" :data="data" />
  </div>
</template>
<script>
import ConfigInspectionList from './ConfigInspectionList.vue'
import ConfigInspectionDetails from './modules/ConfigInspectionDetails.vue'
export default {
  name: 'ConfigInspectionManage',
  data() {
    return {
      isActive: 0,
      data: {},
    }
  },
  components: {
    ConfigInspectionList,
    ConfigInspectionDetails,
  },
  created() {
    this.pButton1(0)
  },
  //使用计算属性
  computed: {
    pageName() {
      switch (this.isActive) {
        case 0:
          return 'ConfigInspectionList'
          break

        default:
          return 'ConfigInspectionDetails'
          break
      }
    },
  },
  methods: {
    pButton1(index) {
      this.isActive = index
    },
    pButton2(index, item) {
      this.isActive = index
      this.data = item
    },
  },
}
</script>