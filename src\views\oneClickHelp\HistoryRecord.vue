<template>
  <!-- <a-table
    :columns="columns"
    :data-source="data"
    ref="table"
    size="middle"
    bordered
    rowKey="id"
  > -->
  <a-table
    ref="table"
    size="middle"
    bordered
    rowKey="id"
    :columns="columns"
    :dataSource="data"
    :pagination="ipagination"
    :loading="loading"
    :destroyOnClose="true"
    class="j-table-force-nowrap"
  >
  </a-table>
</template>
<script>
import { getAction } from '@/api/manage'
export default {
  // 关联配置项
  name: 'HistoryRevord',
  data() {
    return {
      columns: [
        {
          title: '操作人',
          align: 'center',
          dataIndex: 'handleUser',
        },
        {
          title: '操作内容',
          align: 'center',
          dataIndex: 'handleContent',
        },
        {
          title: '操作时间',
          align: 'center',
          dataIndex: 'createTime',
        },
      ],
      data: [],
      loading: false,
      ipagination: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '30'],
        showTotal: (total, range) => {
          return range[0] + '-' + range[1] + ' 共' + total + '条'
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0,
      },
      url: { list: '/question/question/getHistoryLogs' },
    }
  },
  methods: {
    getDataList(id) {
      getAction(this.url.list, {questionId: id}).then((res) => {
        if (res.success) {
          this.loading = false
          this.data = res.result.records
          if (!res.result.records || res.result.records.length == 0) {
            this.$message.warning('未找到历史数据')
          }
        } else {
          this.$message.error(res.message)
        }
      })
    },
  },
}
</script>
<style scoped></style>
