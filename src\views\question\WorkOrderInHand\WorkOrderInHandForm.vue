<template>
  <div>
    <a-card>
      <a-row>
        <a-col :span="24" style="margin-bottom: 5px">
          <div style="text-align: right">
            <img src="~@/assets/return1.png" alt="" @click="getGo" style="width: 20px; height: 20px; cursor: pointer" />
          </div>
        </a-col>
      </a-row>
      <table class="gridtable">
        <tr>
          <td class="leftTd">问题类型</td>
          <td class="rightTd">{{ data.questionTypeText }}</td>
          <td class="leftTd">提问时间</td>
          <td class="rightTd">{{ data.createTime }}</td>
        </tr>
        <tr>
          <td class="leftTd">问题描述</td>
          <td class="rightTd" style="word-break: break-all">{{ data.question }}</td>
          <td class="leftTd">地区</td>
          <td class="rightTd">{{ data.regionName }}</td>
        </tr>
        <tr>
          <td class="leftTd">提问人</td>
          <td class="rightTd">{{ data.quizzer }}</td>
          <td class="leftTd">联系电话</td>
          <td class="rightTd">{{ data.contact }}</td>
        </tr>
        <tr>
          <td class="leftTd">提问人地址</td>
          <td class="rightTd">{{ data.ip }}</td>

          <td class="leftTd">分配人</td>
          <td class="rightTd">{{ data.confirmor }}</td>
        </tr>
        <tr>
          <td class="leftTd">处理人</td>
          <td class="rightTd">{{ data.answerer }}</td>
          <td class="leftTd">分配时间</td>
          <td class="rightTd">{{ data.confirmTime }}</td>
        </tr>
        <tr>
          <td class="leftTd">处理时间</td>
          <td class="rightTd">{{ data.answerTime }}</td>
          <td class="leftTd">解决方案</td>
          <td class="rightTd">{{ data.answererContent }}</td>
        </tr>
      </table>
    </a-card>
  </div>
</template>
<script>
export default {
  name: 'WorkOrderDisposeView', //处理工单查看
  props: ['data'],
  data() {
    return {}
  },
  methods: {
    //返回上一级
    getGo() {
      this.$parent.pButton2(0)
    },
  },
}
</script>
<style  scoped>
table.gridtable {
  font-family: verdana, arial, sans-serif;
  font-size: 14px;
  color: #606266;
  border-width: 1px;
  border-color: #e8e8e8;
  border-collapse: collapse;
  text-align: left;
  width: 100%;
}
table.gridtable td {
  border-width: 1px;
  border-style: solid;
  border-color: #e8e8e8;
}
.leftTd {
  width: 17%;
  background-color: #fafafa;
  padding: 16px 24px;
  text-align: center;
}
.rightTd {
  width: 35%;
  padding: 16px 24px;
}
</style>
