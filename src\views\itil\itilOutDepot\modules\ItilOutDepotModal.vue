<template>
  <j-modal
    title="新增出库单"
    :width="width"
    :visible="visible"
    :centered='true'
    :destroyOnClose='true'
    @ok="handleOk"
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @cancel="handleCancel"
    cancelText="关闭">
    <itil-out-depot-form ref="realForm" @ok="submitCallback" :disabled="disableSubmit"></itil-out-depot-form>
  </j-modal>
</template>

<script>

  import ItilOutDepotForm from './ItilOutDepotForm'
  export default {
    name: 'ItilOutDepotModal',
    components: {
      ItilOutDepotForm
    },
    data () {
      return {
        title:'',
        width:1100,
        visible: false,
        disableSubmit: false
      }
    },
    methods: {
      add () {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.add();
        })
      },
      edit (record) {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.edit(record);
        })
      },
      close () {
        this.$emit('close');
        this.visible = false;
      },
      handleOk () {
        this.$refs.realForm.submitForm();
      },
      submitCallback(){
        this.$emit('ok');
        this.visible = false;
      },
      handleCancel () {
        this.close()
      }
    }
  }
</script>
<style lang="less" scoped>
@import '~@assets/less/limitModalWidth.less';
</style>