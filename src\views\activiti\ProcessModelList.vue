<template xmlns:a-col='http://www.w3.org/1999/html'>
  <a-row :gutter="10" style="height: 100%" class="vScroll zhl zhll">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <!-- 查询区域 -->
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="流程名称">
                  <a-input placeholder="请输入" v-model="queryParam.lcmc"></a-input>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="流程标识">
                  <a-input placeholder="请输入" v-model="queryParam.lckey"></a-input>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="是否最新">
                  <a-switch checkedChildren="是" unCheckedChildren="否" defaultChecked v-model="queryParam.zx" />
                </a-form-item>
              </a-col>
              <a-col :span="colBtnsSpan()">
                <span
                  class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                >
                  <a-button type="primary" class="btn-search btn-search-style" @click="searchQuery">查询</a-button>
                  <a-button class="btn-reset btn-reset-style" @click="searchReset">重置</a-button>
                  <a v-if="isVisible" class="btn-updown-style" @click="doToggleSearch">
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>

      <a-card :bordered="false" style="width: 100%; flex: auto">
        <!-- table区域-begin -->
        <a-table
          :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
          bordered
          ref="table"
          rowKey="id"
          :dataSource="dataSource"
          :pagination="ipagination"
          :loading="loading"
          @change="handleTableChange"
        >
          <a-table-column title="" :width="50" fixed="left" key="a" align="center">
            <template slot-scope="t, r, i">
              <span>{{ i + 1 }}</span>
            </template>
          </a-table-column>
          <a-table-column title="流程名称" data-index="name" :width="210" align="center" fixed="left">
            <template slot-scope="t, r, i">
              <span>{{ t }}</span>
            </template>
          </a-table-column>
          <a-table-column title="流程标识" data-index="processKey">
            <template slot-scope="t, r, i">
              <span>{{ t }}</span>
            </template>
          </a-table-column>
          <a-table-column title="版本" data-index="version" key="asa" :sorter="(a, b) => a.version - b.version">
            <template slot-scope="t, r, i">
              <span>v.{{ t }}</span>
            </template>
          </a-table-column>
          <a-table-column
            title="所属分类"
            data-index="categoryId"
            key="categoryId_"
            :filters="lcTypeF"
            @filter="filter_categoryId"
          >
            <template slot-scope="t, r, i">
              <span>{{ filterDictText(dictOptions, t) }}</span>
            </template>
          </a-table-column>
          <a-table-column title="流程图片" data-index="diagramName">
            <template slot-scope="t, r, i">
              <span style="color: #409eff; cursor: pointer" @click="showResource(r)">{{ t }}</span>
            </template>
          </a-table-column>
          <a-table-column title="状态" data-index="status">
            <template slot-scope="t, r, i">
              <span v-if="t == 1" style="color: #139b33">已启用</span>
              <span v-if="t != 1" style="color: #999">未启用</span>
            </template>
          </a-table-column>
          <a-table-column title="备注说明" data-index="description">
            <template slot-scope="t, r, i">
              <span>{{ t }}</span>
            </template>
          </a-table-column>
          <a-table-column title="部署时间" data-index="createTime">
            <template slot-scope="t, r, i">
              <span>{{ t }}</span>
            </template>
          </a-table-column>
          <a-table-column title="更新时间" data-index="updateTime">
            <template slot-scope="t, r, i">
              <span>{{ t }}</span>
            </template>
          </a-table-column>
          <a-table-column title="操作" data-index align="center" :width="160" fixed="right" class="caozuo">
            <template slot-scope="t, r, i">
              <a href="javascript:void(0);" @click="edit(r)" style="color: #409eff">编辑</a>
              <a-divider type="vertical" />
              <a href="javascript:void(0);" v-if="r.status != 1" @click="editStatus(1, r)">启用</a>
              <a href="javascript:void(0);" v-if="r.status == 1" @click="editStatus(0, r)">禁用</a>
              <a-divider type="vertical" />
              <a-dropdown>
                <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
                <a-menu slot="overlay">
                  <a-menu-item>
                    <a href="javascript:void(0);" @click="getNodeData(r)" style="color: #409eff">节点设置</a>
                  </a-menu-item>
                  <a-menu-item>
                    <a href="javascript:void(0);" @click="convertToModel(r)" style="color: #409eff">转为模型</a>
                  </a-menu-item>
                  <!-- <a-menu-item>
                  <a href="javascript:void(0);" @click="edit(r)" style="color:#409eff">编辑</a>
                </a-menu-item> -->
                  <a-menu-item>
                    <a href="javascript:void(0);" @click="remove(r)" style="color: #409eff">删除</a>
                  </a-menu-item>
                </a-menu>
              </a-dropdown>
            </template>
          </a-table-column>
        </a-table>
        <!-- table区域-end -->
        <!--编辑-->
        <a-modal
          :confirmLoading="confirmLoading"
          title="编辑流程"
          :visible="editObj.visible"
          @ok="editObjOk"
          @cancel="editObj.visible = false"
        >
          <a-form :form="editForm" v-if="editObj.visible">
            <a-form-item :label-col="labelCol" :wrapper-col="wrapperCol" label="流程分类">
              <component
                :is="LcDict"
                :trigger-change="true"
                v-decorator="[
                  'categoryId',
                  { initialValue: editObj.categoryId, rules: [{ required: true, message: '不能为空' }] },
                ]"
                placeholder="请选择流程分类"
                dictCode="bpm_process_type"
              ></component>
            </a-form-item>
            <a-form-item :label-col="labelCol" :wrapper-col="wrapperCol" label="关联表单">
              <a-select
                @change="change_routeName"
                placeholder="请选择关联表单"
                :trigger-change="true"
                v-decorator="[
                  'formKey',
                  { initialValue: editObj.formKey, rules: [{ required: true, message: '不能为空' }] },
                ]"
              >
                <a-select-option value>请选择</a-select-option>
                <a-select-option v-for="(item, i) in formList" :key="i" :value="item.formKey">
                  <span style="display: inline-block; width: 100%" :title="item.formName">{{ item.formName }}</span>
                </a-select-option>
              </a-select>
              <a href="javascrip:void(0)" @click="detailDialog()" style="color: #409eff">预览表单</a>
              <code-detail-dialog ref="modalCode" />
            </a-form-item>
            <a-form-item :label-col="labelCol" :wrapper-col="wrapperCol" label="角色授权">
              <j-select-role
                placeholder="不选择则所有人可用"
                v-decorator="['roles', { initialValue: editObj.roles, rules: [] }]"
              />
            </a-form-item>
            <a-form-item :label-col="labelCol" :wrapper-col="wrapperCol" label="备注描述">
              <a-textarea
                v-decorator="['description', { initialValue: editObj.description, rules: [] }]"
                placeholder="备注描述"
                :autoSize="{ minRows: 3, maxRows: 5 }"
              />
            </a-form-item>
          </a-form>
        </a-modal>
        <!--节点设置-->
        <a-modal
          title="编辑流程节点"
          width="900px"
          :maskClosable="false"
          :confirmLoading="confirmLoading"
          :visible="showProcessNodeEdit"
          :footer="null"
          @cancel="closeNode"
        >
          <a-row>
            <a-col :md="4" :sm="4" style="border-right: 1px solid #999">
              <!--          选择流程节点-->
              <a-steps direction="vertical" :current="current" size="small">
                <template v-for="(item, i) in nodeList">
                  <a-step
                    style="cursor: pointer"
                    :title="item.title"
                    :description="item.description"
                    :status="i == current ? 'process' : 'wait'"
                    @click="change_steps(item, i)"
                  />
                </template>
              </a-steps>
            </a-col>
            <a-col :md="20" :sm="20">
              <a-alert message="温馨提示：若流程运行至未分配审批人员的审批节点时，流程将自动中断取消！" banner />
              <span></span>
              <a-form :form="nodeForm" v-if="showProcessNodeEdit">
                <a-form-item :label-col="labelCol" :wrapper-col="wrapperCol" label="节点名称">
                  <span class="nodespan">{{ editNode.title }}</span>
                </a-form-item>
                <a-form-item :label-col="labelCol" :wrapper-col="wrapperCol" label="节点类型">
                  <span class="nodespan">{{ dictNodeType[editNode.type] }}</span>
                </a-form-item>
                <a-alert
                  type="info"
                  message="每个节点设置，如有修改都请保存一次，跳转节点后数据不会自动保存！"
                  banner
                />
                <br />
                <a-form-item
                  :label-col="labelCol"
                  :wrapper-col="wrapperCol"
                  label="审批人员"
                  v-show="editNode.type == 1"
                >
                  <a-checkbox-group @change="spryType" v-model="spryTypes">
                    <!-- 0角色 1用户 2部门 3发起人 4发起人的部门负责人-->
                    <a-checkbox value="0">根据角色选择</a-checkbox>
                    <a-checkbox value="1">直接选择人员</a-checkbox>
                    <a-checkbox value="2">部门负责人</a-checkbox>
                    <a-checkbox value="3">
                      发起人
                      <a-tooltip placement="topLeft" title="自动获取发起人">
                        <a-icon type="exclamation-circle" />
                      </a-tooltip>
                    </a-checkbox>
                    <a-checkbox value="4">
                      发起人的部门负责人
                      <a-tooltip
                        placement="topLeft"
                        title="自动获取发起人所在部门的负责人，即其上级领导。（如果其本身就是部门负责人，则指向发起人自己。）"
                      >
                        <a-icon type="exclamation-circle" />
                      </a-tooltip>
                    </a-checkbox>
                    <a-checkbox value="5">发起人部门</a-checkbox>
                  </a-checkbox-group>
                </a-form-item>
                <!--            0角色 1用户 2部门 3发起人 4发起人的部门负责人-->
                <a-form-item
                  :label-col="labelCol"
                  :wrapper-col="wrapperCol"
                  label="选择角色"
                  v-if="spryTypes.indexOf('0') > -1"
                >
                  <j-select-role v-model="spry.roleIds" />
                </a-form-item>
                <a-form-item
                  :label-col="labelCol"
                  :wrapper-col="wrapperCol"
                  label="选择角色"
                  v-if="spryTypes.indexOf('4') > -1"
                >
                  <j-select-role v-model="spry.roleId" />
                </a-form-item>
                <a-form-item
                  :label-col="labelCol"
                  :wrapper-col="wrapperCol"
                  label="选择人员"
                  v-if="spryTypes.indexOf('1') > -1"
                >
                  <!--  通过部门选择用户控件 -->
                  <j-select-user-by-dep v-model="spry.userIds" :multi="true"></j-select-user-by-dep>
                </a-form-item>
                <a-form-item
                  :label-col="labelCol"
                  :wrapper-col="wrapperCol"
                  label="选择部门"
                  v-if="spryTypes.indexOf('2') > -1"
                >
                  <j-select-depart v-model="spry.departmentIds" :multi="true"></j-select-depart>
                </a-form-item>
                <!--btn-->
                <a-form-item :wrapper-col="{ span: 12, offset: 5 }">
                  <a-button
                    @click="sprySubmit"
                    type="primary"
                    html-type="submit"
                    :disabled="editNode.type == 0 || editNode.type == 2 || confirmLoading"
                    >提交并保存</a-button
                  >

                  <a-button @click="closeNode" style="margin-left: 10px">关闭</a-button>
                </a-form-item>
              </a-form>
            </a-col>
          </a-row>
        </a-modal>
        <!--查看图片-->
        <a-modal :title="viewTitle" width="90%" :visible="viewImage" :footer="null" @cancel="viewImage = false">
          <div style="min-height: 400px">
            <img :src="diagramUrl" :alt="viewTitle" />
          </div>
        </a-modal>
        <!--流程表单 预览-->
        <!-- <a-modal
      :title="lcModa.title"
      v-model="lcModa.visible"
      :footer="null"
      :maskClosable="false"
      width="80%"
    >
      <component :is="lcModa.formComponent" :disabled="true"></component>
    </a-modal> -->
      </a-card>
    </a-col>
  </a-row>
</template>

<script>
import { filterObj } from '@/utils/util'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { activitiMixin } from '@/views/activiti/mixins/activitiMixin'
import JEllipsis from '@/components/jeecg/JEllipsis'
import { deleteAction, getAction, downFile, postAction } from '@/api/manage'
import pick from 'lodash.pick'
import JTreeSelect from '@/components/jeecg/JTreeSelect'
import { initDictOptions, filterDictText } from '@/components/dict/JDictSelectUtil'
import JSelectUserByDep from '@/components/jeecgbiz/JSelectUserByDep'
import JSelectRole from '@/components/jeecgbiz/JSelectRole'
import JSelectDepart from '@/components/jeecgbiz/JSelectDepart'
import CodeDetailDialog from './CodeDetailDialog'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
export default {
  name: 'ProcessModelList',
  mixins: [activitiMixin, JeecgListMixin, YqFormSearchLocation],
  components: {
    JEllipsis,
    JSelectUserByDep,
    JSelectRole,
    JSelectDepart,
    JTreeSelect,
    CodeDetailDialog,
  },
  data() {
    return {
      formItemLayout: {
        labelCol: {
          style: 'width:90px',
        },
        wrapperCol: {
          style: 'width:calc(100% - 90px)'
        }
      },
      viewImage: false,
      viewTitle: '',
      diagramUrl: '',
      /*编辑流程*/
      editObj: {
        visible: false,
      },
      editForm: this.$form.createForm(this),

      description: '已部署模型',
      // 查询条件
      queryParam: {
        createTimeRange: [],
        keyWord: '',
      },
      tabKey: '1',
      // 表头
      labelCol: {
        xs: { span: 4 },
        sm: { span: 4 },
      },
      wrapperCol: {
        xs: { span: 20 },
        sm: { span: 20 },
      },
      confirmLoading: false,
      current: 0,
      nodeForm: this.$form.createForm(this),
      dictOptions: [],
      nodeList: [],
      editNode: {},
      selectRow: {},
      showProcessNodeEdit: false,
      dictNodeType: {
        0: '开始节点',
        1: '审批节点',
        2: '结束节点',
      },
      spryTypes: [],
      url: {
        list: '/activiti_process/exampleList',
        img: '/activiti/models/export',
        updateStatus: '/activiti_process/updateStatus',
        delByIds: '/activiti_process/delByIds',
        convertToModel: '/activiti_process/convertToModel',
        updateInfo: '/activiti_process/updateInfo',
        getProcessNode: '/activiti_process/getProcessNode',
        editNodeUser: '/activiti_process/editNodeUser',
        formList: '/flowableform/umpFlowableForm/list',
      },
      spry: {
        //选中的用户
        userIds: '',
        roleIds: '',
        roleId: '',
        departmentIds: '',
        chooseSponsor: false,
        chooseDepHeader: false,
        chooseDep: false,
      },
      lcModa: {
        title: '流程表单预览',
        visible: false,
        formComponent: null,
      },
      lcTypeF: [],
      dataList: [],
      formList: [],
    }
  },
  computed: {
    //可行性测试，根据文件路径动态加载组件
    LcDict: function () {
      var myComponent = () => import(`@/components/dict/JDictSelectTag`)
      return myComponent
    },
  },
  mounted() {},
  methods: {
    initDictConfig() {
      //初始化字典 - 流程分类
      initDictOptions('bpm_process_type').then((res) => {
        if (res.success) {
          var lcTypes = []
          this.dictOptions = res.result || []
          for (const dict of this.dictOptions) {
            lcTypes.push({ text: dict.text, value: dict.value })
          }
          this.lcTypeF = lcTypes
        }
      })
    },
    getFormList() {
      getAction(this.url.formList, { pageSize: 100 }).then((res) => {
        if (res.success) {
          this.formList = res.result.records
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    filterDictText(dictOptions, text) {
      if (dictOptions instanceof Array) {
        for (let dictItem of dictOptions) {
          if (text === dictItem.value) {
            return dictItem.text
          }
        }
      }
      return text
    },
    closeNode() {
      ;(this.showProcessNodeEdit = false), (this.current = 0), (this.spryTypes = []), (this.spry = {})
    },
    /*编辑流程节点*/

    change_steps(node, index) {
      this.spryTypes = []
      this.current = index
      this.editNode = node
      /* 0角色 1用户 2部门 3发起人 4发起人的部门负责人 5发起人部门*/
      this.spry.chooseDep = node.chooseDep || false
      if (this.spry.chooseDep) this.spryTypes.push('5')
      this.spry.chooseDepHeader = node.chooseDepHeader || false
      if (this.spry.chooseDepHeader) this.spryTypes.push('4')
      if (node.role != null) {
        this.spry.roleId = node.role.roleName
      }
      this.spry.chooseSponsor = node.chooseSponsor || false
      if (this.spry.chooseSponsor) this.spryTypes.push('3')
      var userIds = []
      for (const user of node.users || []) {
        userIds.push(user.username)
      }
      this.spry.userIds = userIds.join(',')
      if (userIds.length > 0) this.spryTypes.push('1')
      var roleIds = []
      for (const role of node.roles || []) {
        roleIds.push(role.roleCode)
      }
      this.spry.roleIds = roleIds.join(',')
      if (roleIds.length > 0) this.spryTypes.push('0')
      var departmentIds = []
      for (const department of node.departments || []) {
        departmentIds.push(department.id)
      }
      this.spry.departmentIds = departmentIds.join(',')
      if (departmentIds.length > 0) this.spryTypes.push('2')
    },
    spryType(types) {
      /* 0角色 1用户 2部门 3发起人 4发起人的部门负责人*/
      // this.spryTypes = types;
      if (this.spryTypes.indexOf('0') == -1) this.spry.roleIds = ''
      if (this.spryTypes.indexOf('1') == -1) this.spry.userIds = ''
      if (this.spryTypes.indexOf('2') == -1) this.spry.departmentIds = ''
      //是否选中发起人
      this.spry.chooseSponsor = this.spryTypes.indexOf('3') > -1
      //是否选中发起人的部门领导
      this.spry.chooseDepHeader = this.spryTypes.indexOf('4') > -1
      //是否选中发起人的部门
      this.spry.chooseDep = this.spryTypes.indexOf('5') > -1
    },
    sprySubmit() {
      var _this = this
      if (this.spryTypes.length == 0) {
        _this.$message.error('必须选择审批人！')
        return
      }
      _this.confirmLoading = true
      this.spry.nodeId = this.editNode.id
      postAction(_this.url.editNodeUser, this.spry)
        .then((res) => {
          if (res.success) {
            _this.$message.success('操作成功')
            //缺少刷新
            this.getNodeData(this.selectRow)
            // _this.getData();
          } else {
            _this.$message.error(res.message)
          }
        })
        .finally(() => (_this.confirmLoading = false))
    },
    getNodeData(row) {
      this.selectRow = row
      var _this = this
      _this.confirmLoading = false
      postAction(_this.url.getProcessNode, {
        id: row.id,
      }).then((res) => {
        if (res.success) {
          // 转换null为""
          _this.nodeList = res.result || []
          if (_this.nodeList.length > 0) {
            _this.editNode = _this.nodeList[0]
            _this.showProcessNodeEdit = true
          }
        } else {
          _this.$message.error(res.message)
        }
      })
    },
    /*编辑流程信息*/
    edit(row) {
      this.getFormList()
      this.editObj = Object.assign(this.editObj, row)
      this.editObj.visible = true
    },
    editObjOk() {
      var _this = this
      this.editForm.validateFields((err, values) => {
        if (!err) {
          let formData = Object.assign(this.editObj, values)
          this.confirmLoading = true
          postAction(this.url.updateInfo, formData)
            .then((res) => {
              if (res.success) {
                _this.$message.success('操作成功')
                _this.loadData()
                _this.editObj.visible = false
              } else {
                _this.$message.error(res.message)
              }
            })
            .finally(() => (_this.confirmLoading = false))
        }
      })
    },
    change_routeName() {
      this.$nextTick(() => {
        let formKey = this.editForm.getFieldValue('formKey')
        var route = this.getFormComponent(formKey)
        this.editObj.businessTable = route.businessTable
        this.editObj.formKey = route.formKey
      })
    },
    detailDialog() {
      let keys = this.editForm.getFieldValue('formKey')
      this.$refs.modalCode.see(keys)
      this.$refs.modalCode.title = '预览表单'
    },
    convertToModel(row) {
      let that = this
      this.$confirm({
        title: '确认转化',
        okText: '确定',
        cancelText: '取消',
        content: '您确认要转化流程 ' + row.name + ' 为模型?',
        loading: true,
        onOk: () => {
          postAction(`${that.url.convertToModel}`, { id: row.id }).then((res) => {
            if (res.success) {
              setTimeout(function () {
                that.$message.success('转化成功')
              }, 300)
            } else {
              that.$message.error(res.message)
            }
          })
        },
      })
    },
    remove(row) {
      var _this = this
      _this.$confirm({
        title: '确认删除',
        okText: '确定',
        cancelText: '取消',
        content: '您确认要删除流程 ' + row.name + ' ?',
        loading: true,
        onOk: () => {
          postAction(_this.url.delByIds, { ids: row.id }).then((res) => {
            if (res.success) {
              _this.$message.success('操作成功')
              _this.loadData()
            } else {
              _this.$message.error(res.message)
            }
          })
        },
      })
    },
    editStatus(status, row) {
      var _this = this
      let operation = ''
      if (status == 0) {
        operation = '不启用'
      } else {
        operation = '启用'
      }
      this.$confirm({
        title: '确认' + operation + '?',
        okText: '确定',
        cancelText: '取消',
        content: `您确认要${operation}流程${row.name}?`,
        onOk() {
          let params = {
            status: status,
            id: row.id,
          }
          postAction(_this.url.updateStatus, params).then((res) => {
            if (res.success) {
              _this.$message.success(res.message)
              _this.loadData()
            } else {
              _this.$message.error(res.message)
            }
          })
        },
        onCancel() {},
      })
    },
    /*查看流程图片*/
    showResource(row) {
      this.viewTitle = '流程图片预览(' + row.diagramName + ')'
      this.diagramUrl = window._CONFIG['domianURL'] + `${this.url.img}?id=${row.id}`
      this.viewImage = true
    },
    /*删除模型*/
    deletelc(y, row) {
      if (y) {
        getAction(this.url.delete + row.id).then((res) => {
          if (res.success) {
            this.$message.success(res.message)
          } else {
            this.$message.error(res.message)
          }
          this.loadData()
        })
      }
    },
    handleTableChange(pagination, filters, sorter) {
      //分页、排序、筛选变化时触发
      //TODO 筛选
      if (Object.keys(sorter).length > 0) {
        this.isorter.column = sorter.field
        this.isorter.order = 'ascend' == sorter.order ? 'asc' : 'desc'
      }
      this.ipagination = pagination
      /*if (Object.keys(filters).length>0&&this.dataList.length>0){
          for (const filterField in filters) {
            let fiterVals = filters[filterField]||[];

          }
        }*/
      // this.loadData();
    },
    loadData(arg) {
      if (!this.url.list) {
        this.$message.error('请设置url.list属性!')
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1
      }
      var params = this.getQueryParams() //查询条件
      this.loading = true
      getAction(this.url.list, params).then((res) => {
        if (res.success) {
          let records = res.result || []
          this.dataSource = records
          this.dataList = records
          this.ipagination.total = records.length
        }
        if (res.code === 510) {
          this.$message.warning(res.message)
        }
        this.loading = false
      })
    },
    getQueryParams() {
      var param = Object.assign({}, this.queryParam, this.isorter)
      delete param.createTimeRange // 时间参数不传递后台
      return filterObj(param)
    },

    // 重置
    searchReset() {
      var that = this
      var logType = that.queryParam.logType
      that.queryParam = {} //清空查询区域参数
      that.queryParam.logType = logType
      that.loadData(this.ipagination.current)
    },
    onDateChange: function (value, dateString) {
      this.queryParam.createTime_begin = dateString[0]
      this.queryParam.createTime_end = dateString[1]
    },
    onDateOk(value) {},

    filter_categoryId(v, r) {
      return r.categoryId == v
    },
  },
}
</script>
<style lang="less" scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
.nodespan {
  color: #999;
}
.ant-checkbox-wrapper + .ant-checkbox-wrapper {
  margin-left: 0;
  margin-right: 8px;
}
.span-style {
  display: inline-block;
  width: 100% !important;
  height: 100% !important;
  line-height: 100% !important;
  white-space: nowrap;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}
/*给table列设置宽度*/
::v-deep .ant-table-scroll .ant-table-thead > tr > th,
::v-deep .ant-table-scroll .ant-table-tbody > tr > td {
  /*流程标识*/
  &:nth-child(3) {
    min-width: 100px;
    max-width: 300px;
  }

  /*版本*/

  &:nth-child(4) {
    min-width: 50px;
    max-width: 100px;
  }

  /*所属分类*/

  &:nth-child(5) {
    min-width: 150px;
    max-width: 300px;
  }

  /*流程图片*/

  &:nth-child(6) {
    min-width: 100px;
    max-width: 400px;
  }

  /*状态*/

  &:nth-child(7) {
    min-width: 100px;
    max-width: 300px;
  }
  /*备注说明*/

  &:nth-child(8) {
    min-width: 50px;
    max-width: 500px;
  }
  /*部署时间*/

  &:nth-child(9) {
    min-width: 150px;
    max-width: 200px;
  }
  /*更新时间*/

  &:nth-child(10) {
    min-width: 150px;
    max-width: 200px;
  }
}

/*表头样式*/
::v-deep .ant-table-thead > tr > th {
  text-align: center;
  white-space: nowrap;
}

/*内容对齐方式、省略显示*/
::v-deep .ant-table-tbody > tr > td {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  text-align: center !important;

  &:nth-child(-n + 5)，&:nth-child(7) {
    span {
      .span-style;
      text-align: center !important;
    }
  }
  &:nth-child(6),
  &:nth-child(8) {
    span {
      .span-style;
      text-align: left !important;
    }
  }
  &:nth-child(9),
  &:nth-child(10) {
    span {
      .span-style;
      text-align: right !important;
    }
  }
}
</style>