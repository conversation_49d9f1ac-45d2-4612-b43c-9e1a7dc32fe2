<template>
  <a-card :bordered='false'>
    <div class='action'>
      <span class='edit'>
        详情信息
      </span>
      <span class='return'>
        <img src='~@/assets/return1.png' alt='' @click='getGo'>
      </span>
    </div>
    <a-descriptions :column='{ xxl: 2, xl: 2, lg: 2, md: 2, sm: 2, xs: 2 }' bordered>
      <a-descriptions-item label='上级单位名称' v-if='data.parentDepartName'>{{ data.parentDepartName }}</a-descriptions-item>
      <a-descriptions-item label='上级单位标识' v-if='data.parentDepartCode'>{{ data.parentDepartCode }}</a-descriptions-item>
      <a-descriptions-item label='上级平台标识' v-if='data.parentDepartCode'>{{ data.parentPlatformCode }}</a-descriptions-item>
      <a-descriptions-item label='上级服务地址' v-if='data.parentServerAddress'>{{ data.parentServerAddress }}
      </a-descriptions-item>
      <a-descriptions-item label='数据汇聚请求路径' v-if='data.parentRequestUrl'>{{ data.parentRequestUrl }}
      </a-descriptions-item>
      <a-descriptions-item label='本级单位名称' v-if='data.selfDepartName'>{{ data.selfDepartName }}</a-descriptions-item>
      <a-descriptions-item label='本级单位标识' v-if='data.selfDepartCode'>{{data.selfDepartCode}}</a-descriptions-item>
      <a-descriptions-item label='本级平台标识' v-if='data.selfPlatformCode'>{{data.selfPlatformCode}}</a-descriptions-item>
      <a-descriptions-item label='本级单位所在地' v-if='data.selfLocation'>{{data.selfLocation}}</a-descriptions-item>
      <a-descriptions-item label='接收记录总数' v-if='data.receiveCount != null'>{{data.receiveCount}}</a-descriptions-item>
      <a-descriptions-item label='接收记录成功数' v-if='data.receiveSuccessCount != null'>{{data.receiveSuccessCount}}
      </a-descriptions-item>
    </a-descriptions>
  </a-card>
</template>

<script>
  export default {
    name: 'data',
    data() {
      return {}
    },
    props: {
      data: {
        type: Object,
        required: false,
        default: () => {
          return {}
        }
      }
    },
    watch: {
      data: {
        handler(val) {
          this.data = val
        },
        deep: true,
        immediate: true
      }
    },
    mounted() {
      console.log(this.data, 'data');

    },
    methods: {
      //返回上一级
      getGo() {
        this.$parent.pButton2(0)
      }
    }
  }
</script>

<style scoped lang='less'>
  .action {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-flow: row nowrap;
    margin-bottom: 12px;

    .edit {
      margin-left: 10px;

      .icon {
        color: #409eff;
        margin-right: 6px
      }
    }

    .return {
      img {
        width: 20px;
        height: 20px;
        cursor: pointer
      }
    }
  }

  ::v-deep .ant-descriptions-view {
    border-radius: 0px;
  }

  ::v-deep .ant-descriptions-bordered .ant-descriptions-item-label {
    background-color: rgb(250, 250, 250);
    text-align: center;
    width: 17%;
  }

  ::v-deep .ant-descriptions-item-label,
  .ant-descriptions-item-content {
    color: rgb(96, 98, 102) !important;
  }

  ::v-deep .ant-descriptions-bordered .ant-descriptions-item-content {
    word-break: break-word;
    white-space: normal;
  }
</style>