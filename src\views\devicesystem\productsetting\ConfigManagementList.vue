<template>
  <div class='zhl'>
    <!-- {{ productId }} -->
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24"> </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator table-operator-style">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot='overlay' style='text-align: center'>
          <a-menu-item key='1' @click='batchDel'>删除</a-menu-item>
        </a-menu>
        <a-button> 批量操作
          <a-icon type='down' />
        </a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <a-table
        ref="table"
        bordered
        :row-key="(record,index)=>{return record.id}"
        :columns="columns"
        :dataSource="dataSource"
        :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        @change="handleTableChange"
      >
        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="controlType" slot-scope="text">
          <div v-if="text == 'inputString'">文本输入框</div>
          <div v-if="text == 'inputNumber'">数字输入框</div>
          <div v-if="text == 'inputPassword'">密码输入框</div>
          <div v-if="text == 'inputSelect'">选择框</div>
        </template>
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 14px; ">无图片</span>
          <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width:80px;font-size: 14px; " />
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 14px; ">无文件</span>
          <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record" class="caozuo">
            <a @click="handleDetail(record)">查看</a>
            <a-divider type="vertical" />
            <a @click="handleEdit(record)">编辑</a>
            <a-divider type="vertical" />
            <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
              <a>删除</a>
            </a-popconfirm>

          <!-- <a>删除</a> -->
          </span>
        <template slot='tooltip' slot-scope='text'>
          <a-tooltip placement='topLeft' :title='text' trigger='hover'>
              <span class='tooltip'>
                {{ text }}
              </span>
          </a-tooltip>
        </template>
      </a-table>
    </div>
     <product-config-modal ref="modalForm" :productInfo='record' @ok="modalFormOk"></product-config-modal>
  </div>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { httpAction, getAction, deleteAction } from '@/api/manage'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import productConfigModal from '../modules/ProductConfigModal.vue'
export default {
  name: 'ConnectInfo',
  mixins: [JeecgListMixin, mixinDevice],
  components: {
    productConfigModal,
  },
  data() {
    return {
      productId: '',
      record: {},
      // 表头
      columns: [
        {
          title: '名称',
          dataIndex: 'taskName',
          customCell: () => {
            let cellStyle = 'text-align: center'
            return { style: cellStyle }
          },
        },
        {
          title: '协议类型',
          dataIndex: 'transferProtocol',
          customCell: () => {
            let cellStyle = 'text-align: center'
            return { style: cellStyle }
          },
        },
        {
          title: '是否默认',
          dataIndex: 'isDefault',
          customCell: () => {
            let cellStyle = 'text-align: center'
            return { style: cellStyle }
          },
          customRender: function (text) {
            if (text == 'Y') {
              return '是'
            } else if (text == 'N') {
              return '否'
            } else {
              return text
            }
          }
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 200,
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: '/net/device/configureManageList',
        delete: '/net/device/productConfigureManageDelete',
        deleteBatch: '/net/device/productConfigureManageDeleteBatch'
      },
      disableMixinCreated: true
    }
  },
  methods: {
    show(record) {
      this.$nextTick(()=>{
        this.record = record
        this.queryParam.productId = this.record.id
        this.ipagination.pageSize=10
        this.ipagination.current=1
        this.loadData()
      })
    },
    handleDelete: function (id) {
      if (!this.url.delete) {
        this.$message.error('请设置url.delete属性!')
        return
      }
      var that = this
      deleteAction(that.url.deleteBatch, { ids: id }).then((res) => {
        if (res.success) {
          //重新计算分页问题
          that.reCalculatePage(1)
          that.$message.success(res.message)
          that.loadData()
        } else {
          that.$message.warning(res.message)
        }
      })
    },
  }
}
</script>

<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
</style>
