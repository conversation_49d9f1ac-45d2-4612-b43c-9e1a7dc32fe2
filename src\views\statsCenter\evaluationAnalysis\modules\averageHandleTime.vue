<template>
  <card-frame :title="'平均处理时间TOP10'" :titleBgPath="'/statsCenter/evaluate/title1.png'">
    <div slot="bodySlot" class="empty-wrapper" v-if="chartData.length===0">
      <a-spin :spinning="loading" v-if="loading" class="spin"></a-spin>
      <a-list :data-source="[]" v-else />
    </div>
    <div slot="bodySlot" v-else class="chart-wrapper bg">
      <column-chart :chart-data="chartData" class="chart-wrapper" :unit="'h'"></column-chart>
    </div>
  </card-frame>
</template>

<script>
import { getAction } from '@api/manage'
import cardFrame from '@views/statsCenter/com/cardFrame.vue'
import columnChart from '@views/statsCenter/com/columnChart.vue'

export default {
  name: 'averageHandleTime',
  components: {
    cardFrame
  },
  components: { cardFrame, columnChart },

  data() {
    return {
      loading: false,
      chartData: [],
      time1: '',
      time2: '',
      url: {
        handleAverageTop: '/data-analysis/order/handle/average/top'
      }
    }
  },
  created() {
    this.handleAverageTop()
  },
  methods: {
    // 平均处理时间柱状图数据
    handleAverageTop() {
      this.loading = true
      getAction(this.url.handleAverageTop, {
        time1: this.time1,
        time2: this.time2
      })
        .then(res => {
          if (res.code == 200) {
            this.loading = false
            this.chartData = res.result
          } else {
            this.$message.warning(res.message)
            this.loading = false
          }
        })
        .catch(err => {
          this.$message.warning(err.message)
          this.loading = false
        })
    }
  }
}
</script>

<style scoped lang="less">
.chart-wrapper {
  width: 100%;
  height: 100%;
}
.bg {
  background-color: rgba(0, 23, 34, 0.36);
  position: relative;
  top: -0.09rem;
}
</style>