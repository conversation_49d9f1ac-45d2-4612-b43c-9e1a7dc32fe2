<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container>
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-item label="字段名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['name', validatorRules.name]" :allowClear="true" autocomplete="off"
                placeholder="请输入字段名称"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="字段code" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['code', validatorRules.code]" :allowClear="true" autocomplete="off"
                placeholder="请输入字段code"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="字段类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag type="list" v-decorator="['type', validatorRules.type]" :trigger-change="true"
                dictCode="type_code" placeholder="请选择字段类型" />
            </a-form-item>
          </a-col>
          <a-col :span="24" v-if="showDictcode">
            <a-form-item label="数据字典" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-select :getPopupContainer="(node) => node.parentNode"
                v-decorator="['dictType', validatorRules.assetsDictcode]" optionFilterProp="children"
                placeholder="请选择数据字典" allowClear showSearch>
                <a-select-option v-for="item in this.dictList" :key="item.dictCode" :value="item.dictCode">
                  {{ item.dictName }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="字段描述" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['remark',validatorRules.remark]" :allowClear="true" autocomplete="off"
                placeholder="请输入字段描述"></a-input>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
  import {
    httpAction,
    getAction
  } from '@/api/manage'
  import pick from 'lodash.pick'
  import JFormContainer from '@/components/jeecg/JFormContainer'
  import JDictSelectTag from '@/components/dict/JDictSelectTag'

  export default {
    name: 'additionalForm',
    components: {
      JFormContainer,
      JDictSelectTag
    },
    data() {
      return {
        form: this.$form.createForm(this, {
          onFieldsChange: this.onFieldsChange
        }),
        model: {},
        showDictcode: false,
        dictList: [],
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          },
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          },
        },
        confirmLoading: false,
        validatorRules: {
          name: {
            rules: [{
              required: true,
              message: '请输入字段名称'
            }, {
              min: 2,
              max: 50,
              message: '字段名称长度应在2-50个字符之间'
            }],
          },
          code: {
            rules: [{
              required: true,
              message: '请输入字段code'
            }, {
              min: 2,
              max: 20,
              message: '字段code长度应在2-20个字符之间'
            }],
          },
          type: {
            rules: [{
              required: true,
              message: '请选择字段类型'
            }]
          },
          assetsDictcode: {
            rules: [{
              required: true,
              message: '请输入字典类型'
            }],
          },
          remark: {
            rules: [{
              max: 200,
              message: '字段描述长度不超过200字符'
            }]
          },
        },
        url: {
          add: '/devops/ip/planExtend/add',
          edit: '/devops/ip/planExtend/edit',
        },
      }
    },
    created() {
      this.getDict()
    },
    methods: {
      getDict() {
        getAction('sys/dict/getAllDict').then((res) => {
          if (res.success) {
            this.dictList = res.result
          }
        })
      },
      // 监听表单字段变化
      onFieldsChange(props, fields) {
        // 表单type为下拉框 显示字典类型
        if (fields.type) {
          this.showDictcode = fields.type.value === '下拉框'
        }
      },
      add() {
        this.edit({})
      },
      edit(record) {
        if (record.type == '下拉框') {
          this.showDictcode = true
        }
        this.form.resetFields()
        this.model = Object.assign({}, record)
        this.visible = true
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.model, 'departId', 'name', 'code',
            'type', 'dictType', 'remark'))
        })
      },
      submitForm() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.model.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'put'
            }
            let formData = Object.assign(this.model, values)
            httpAction(httpurl, formData, method)
              .then((res) => {
                if (res.success) {
                  that.$message.success(res.message)
                  that.$emit('ok')
                } else {
                  that.$message.warning(res.message)
                }
              })
              .finally(() => {
                that.confirmLoading = false
              })
          }
        })
      },
      popupCallback(row) {
        this.form.setFieldsValue(pick(row, 'departId', 'name', 'code', 'type', 'dictType', 'remark'))
      },
    },
  }
</script>