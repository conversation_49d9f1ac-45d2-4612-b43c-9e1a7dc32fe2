<template>
  <div class="page-box">
    <div class="body">
      <a-row :gutter="16" class="row-class">
        <a-col :span="6" class="col-class">
          <div class="core">
            <div class="core-top" ref="coreTop">
              <chart-title :title="'终端总览'"></chart-title>
              <div class="core-top-body" v-if="popHeight">
                <a-carousel autoplay>
                  <div :style="{ height: popHeight + 'px' }" v-for="(res, idx) in resourcesList" :key="idx">
                    <rainbow-bubble :bubbleData="res"></rainbow-bubble>
                  </div>
                </a-carousel>
              </div>
            </div>
            <div class="core-middle">
              <chart-title :title="'单位在线率排名TOP5'"></chart-title>
              <div class="core-middle-body" ref="unitOnlinePercent" id="unitOnlinePercent"></div>
            </div>
            <div class="core-bottom">
              <chart-title :title="'单位在线轮播'"></chart-title>
<!--              <seamless-scroll :titleL="titleL" :dataUrl="url.cityTerminal"> </seamless-scroll>-->
              <seamless-scroll :titleL="titleL" :dataRes="dataRes"> </seamless-scroll>
            </div>
          </div>
        </a-col>
        <a-col :span="12" class="col-class">
          <div class="center-map">
            <chart-title :title="'地图'"></chart-title>
            <div class="map-box" id="echartsMap"></div>
          </div>
        </a-col>
        <a-col :span="6" class="col-class">
          <div class="right">
            <div class="right-top">
              <chart-title :title="'单位在线数量TOP5'"></chart-title>
              <!-- <div class="chart-con">
                <rainbow-pie :chartId="'rainbowPie'"></rainbow-pie>
             </div> -->
              <div class="right-top-body" ref="unitOnline" id="unitOnline">
                <div class="left-div">
                  <div class="left-top-div" v-if="DeviceOnlineFive.length > 4">
                    <span class="title-span">TOP5 {{ DeviceOnlineFive[4].name || '' }}</span
                    ><span class="number-span">{{ DeviceOnlineFive[4].onCount || '' }}</span>
                  </div>
                  <div class="left-center-div" v-if="DeviceOnlineFive.length > 3">
                    <span class="title-span">TOP4 {{ DeviceOnlineFive[3].name || '' }}</span
                    ><span class="number-span">{{ DeviceOnlineFive[3].onCount || '' }}</span>
                  </div>
                  <div class="left-bottom-div" v-if="DeviceOnlineFive.length > 2">
                    <span class="number-span">TOP3 {{ DeviceOnlineFive[2].onCount || '' }}</span
                    ><span class="title-span">{{ DeviceOnlineFive[2].name || '' }}</span>
                  </div>
                </div>
                <div class="img-div"><img src="@/assets/bigScreen/circle-bg.png" alt /></div>
                <div class="right-div">
                  <div class="right-top-div" v-if="DeviceOnlineFive.length > 0">
                    <span class="title-span">TOP1 {{ DeviceOnlineFive[0].name }}</span>
                    <span class="number-span">{{ DeviceOnlineFive[0].onCount }}</span>
                  </div>
                  <div class="right-bottom-div" v-if="DeviceOnlineFive.length > 1">
                    <span class="number-span">TOP2 {{ DeviceOnlineFive[1].onCount }}</span
                    ><span class="title-span">{{ DeviceOnlineFive[1].name }}</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="right-core">
              <chart-title :title="'区县在线率排名TOP5'"></chart-title>
              <div class="right-core-body">
                <div class="right-core-body-bar" id="cityOnRateFive"></div>
              </div>
            </div>
            <div class="right-bottom">
              <chart-title :title="'10日内在线趋势图'"></chart-title>
              <div class="right-bottom-core">
                <div class="right-bottom-core-lineChart" id="onlineTrendLineChart"></div>
              </div>
            </div>
          </div>
        </a-col>
      </a-row>
    </div>
  </div>
</template>
<script>
import echarts from 'echarts'
import 'echarts/lib/component/graphic'
import '../../../node_modules/echarts/map/js/china' // 引入中国地图数据
import vueSeamlessScroll from 'vue-seamless-scroll'
import { deleteAction, getAction, putAction, httpAction } from '@/api/manage'
import SeamlessScroll from '@/components/bigScreen/terminalSeamlessScroll.vue'
import ChartTitle from './moduels/chart-title.vue'
import RainbowBubble from './moduels/rainbow-bubble.vue'
import RainbowPie from './echartsModule/rainbow-pie.vue'
import { ajaxGetDictItems } from '@api/api'
// 地图数据
// const mapJson = require('./json/echarts.json')
const mapJson = window.echarts
echarts.registerMap('citymap', mapJson)

// window.dict.features[0].value

export default {
  components: {
    RainbowBubble,
    RainbowPie,
    ChartTitle,
    vueSeamlessScroll,
    'seamless-scroll': SeamlessScroll,
  },
  data() {
    return {
      title: window._CONFIG['systemName'],
      cz_zunyi: window._CONFIG['customization'],
      dataRes:[],
      url: {
        cityTerminal: '/terminal/terminalDevices/gitDeviceRate', //六盘水各单位终端信息
        onlineTrendUrl: '/terminal/terminalDevices/tenTrend', //10日内在线趋势图
        resourceStatistic: '/terminal/terminalDevices/getResources', //资源总览
        cityOnRate: '/terminal/terminalDevices/findCityFive', //各区县使用率
        getCity: '/terminal/terminalDevices/getCity', //地图地区数据
        getResByCity: '/terminal/terminalDevices/getResByCity', //地图鼠标悬浮接口
        //unitOnlineTop5: '/terminal/terminalDevices/gitDeviceRateFive', //单位在线率前五
        //gitDeviceCountFive: '/terminal/terminalDevices/gitDeviceCountFive', //单位在线数量前五
        getDeviceRate:'/terminal/terminalDevices/getDeviceRate',
      },
      resources: [],
      resourcesList: [],
      cityRes: [],
      cityVal: '',
      DeviceRateFive: [],
      DeviceOnlineFive: [],
      //六盘水配置
      titleL: {
        firstTitle: '单位名称',
        secondTitle: '终端总数',
        thirdTitle: '在线数',
        fourthTitle: '离线数',
        fifthTitle: '在线率',
      },
      popHeight: 0,
      //遵义
      // titleL:{
      //   firstTitle:'地区名称',
      //   secondTitle:'终端总数',
      //   thirdTitle:'在线数',
      //   fourthTitle:'离线数',
      //   fifthTitle:'在线率'
      // },
    }
  },
  watch:{
    cityRes:{
      immediate:false,
      handler(newValue,oldValue){
        if(Array.isArray(newValue)){
          let fiveCountry=[],countrys=[];
          if(this.cz_zunyi&&this.cz_zunyi.cz_zunyi&&this.cz_zunyi.cz_zunyi.statisticsMunicipal){
            countrys=newValue.filter(p=>this.cz_zunyi.cz_zunyi.statisticsMunicipal.indexOf(p.cityName)<0);
          }else{
            countrys=newValue;
          }
          countrys=countrys.map(p=> ({name:p.cityName,rate:p.onRate.replace('%','')})).sort((a,b)=>b.rate-a.rate);
          fiveCountry=countrys.slice(0,5);
          this.setCityOnRate(fiveCountry);
        }

      },
    }
  },
  created() {},
  mounted() {
    this.getHeight()
    //资源总览
    this.resourceStatistic();
    //单位在线率、在线轮播、在线数排行
    this.getStatisticalParam();
    //单位在线率排名TOP5
    //this.getUnitOnlineTop5()
    //地图
    this.echart_map()
    //单位在线数量TOP5
    //this.gitDeviceCountFive()
    //区县在线率排名TOP5
    //this.getCityOnRate()
    //10日内在线趋势图
    this.getOnlineTrendEcharts()

    this.cityVal = setInterval(() => {
      this.getResByCity()
    }, 60000)
  },
  computed: {
    warning() {
      return {
        step: 0.1, // 数值越大速度滚动越快
        limitMoveNum: 2, // 开始无缝滚动的数据量 this.dataList.length
        hoverStop: false, // 是否开启鼠标悬停stop
        direction: 1, // 0向下 1向上 2向左 3向右
        // openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 86, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
        // singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
        waitTime: 2, // 单步运动停止的时间(默认值1000ms)
      }
    },
  },
  beforeDestroy() {
    clearInterval(this.cityVal)
  },
  methods: {
    getHeight() {
      let rect = this.$refs.coreTop.getBoundingClientRect()
      // let rectt = this.$refs.coreTopTitle.getBoundingClientRect()
      this.popHeight = rect.height - 24 - 8
    },
    // 资源总览数据
    resourceStatistic() {
      getAction(this.url.resourceStatistic).then((res) => {
        if (res.success) {
          // res.result.net = [
          //   {
          //     count: 0,
          //     countNumber: 6659,
          //     countStatistics: '6659',
          //     gatewayCode: null,
          //     gatewayName: '政务网终端离线数',
          //     gatewayStatistics: '政务网终端总数',
          //     gatewayType: "政务网",
          //   },
          // ]
          if(!Array.isArray(res.result.net)){
            res.result.net = [];
          }
          this.resourcesList = [res.result.net1].concat(res.result.net)
         // this.resourcesList = isArray(res.result.net) ?  res.result.net1.concat(res.result.net) :res.result.net1

          // this.resources.push({ id: '0', data: [...res.result.net1] })
          // var list = []
          // if (!!res.result.net) {
          //   for (var i = 0; i < res.result.net.length; i = i + 2) {
          //     list.splice(0, list.length)
          //     list.push(res.result.net[i])
          //     if (i + 1 < res.result.net.length) {
          //       list.push(res.result.net[i + 1])
          //     }
          //     this.resources.push({ id: i + 1 + '', data: [...list] })
          //   }
          // }
        }
      })
    },
    //单位在线率、在线轮播、在线数排行
    getStatisticalParam(){
      ajaxGetDictItems('terminal_statistical_param', null).then((res) => {
        if (res.success) {
          let terminalStatisticalParam=0;
          if(res.result&&res.result.length>0){
            terminalStatisticalParam=res.result[0].value;
          }
          getAction(this.url.getDeviceRate).then((res) => {
            if (res.success) {
              let result=res.result;
              let deviceNum=[],deviceOnline=[],deviceTableDatas=[];
              if(result.length>0){
                deviceOnline=result.filter(device=> device.sumCount>terminalStatisticalParam&&device.name);
                deviceOnline.sort((a,b)=>b.rate-a.rate);
                deviceNum=result.filter(device=> device.name);
                deviceNum=deviceNum.sort((a,b)=>b.onCount-a.onCount);
                deviceTableDatas=result.sort((a,b)=>b.sumCount-a.sumCount)
              }
              let  deviceOnlineFive=deviceOnline.slice(0,5);
              let deviceNumFive=deviceNum.slice(0,5);
              //单位在线率排名
              this.setEchartsUnitOnlineTop5(deviceOnlineFive);
              //单位在线数量
              this.DeviceOnlineFive =deviceNumFive;
              //单位在线率轮播
              this.dataRes=deviceTableDatas;

            }
          });
        }
      })
    },
    //单位在线率排名TOP5
   /* getUnitOnlineTop5() {
      getAction(this.url.unitOnlineTop5).then((res) => {
        if (res.success) {
          this.DeviceRateFive = res.result
          this.setEchartsUnitOnlineTop5(res.result)
        }
      })
    },*/
    //单位在线率排名TOP5
    setEchartsUnitOnlineTop5(data) {
      var dataSource = data
      var dataName = []
      var dataValue = []
      var dataNum = []
      for (var key in dataSource) {
        dataName.push(dataSource[key]['name'])
        dataValue.push(dataSource[key]['rate'])
        dataNum.push(100)
      }
      // dataName = ['test1', 'test2', 'test3', 'test4', 'test5']
      // dataValue = [80, 75, 60, 55, 40]
      // dataNum = [100, 100, 100, 100, 100]
      let myChart = this.$echarts.init(document.getElementById('unitOnlinePercent'))
      myChart.setOption({
        backgroundColor: '#131419',
        //图标位置
        grid: {
          top: 16,
          left: 8,
          right: 50,
          bottom: 8,
        },
        xAxis: {
          show: false,
          type: 'value',
        },
        yAxis: [
          {
            type: 'category',
            show: true,
            data: dataName,
            inverse: true,
            axisLine: {
              show: false,
            },
            splitLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              show: false,
              color: '#fff',
            },
          },
          {
            type: 'category',
            show: true,
            inverse: true,
            axisTick: 'none',
            axisLine: 'none',
            data: dataValue,
            axisLabel: {
              textStyle: {
                fontSize: 12,
                color: '#019BDC',
              },
              formatter: function (value) {
                return value + '%'
              },
            },
          },
        ],
        series: [
          {
            name: '地区',
            type: 'bar',
            yAxisIndex: 0,
            data: dataValue,
            zlevel: 1,
            barWidth: 7,
            itemStyle: {
              normal: {
                barBorderRadius: 30,
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                  {
                    offset: 0,
                    //color: 'rgb(57,89,255,1)'
                    color: '#385FFC',
                  },
                  {
                    offset: 1,
                    //color: 'rgb(46,200,207,1)'
                    color: '#2FBED3',
                  },
                ]),
              },
            },
            label: {
              normal: {
                color: '#fff',
                show: true,
                position: [0, '-15px'],
                textStyle: {
                  fontSize: 12,
                },
                formatter: function (a, b) {
                  return a.name
                },
              },
            },
          },
          {
            name: '背景',
            type: 'bar',
            yAxisIndex: 1,
            barGap: '-100%',
            data: dataNum,
            barWidth: 10,
            itemStyle: {
              normal: {
                color: 'rgba(24,31,68,1)',
                barBorderRadius: 30,
              },
            },
          },
        ],
      })
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    },
    // 地图
    echart_map() {
      let myChart = this.$echarts.init(document.getElementById('echartsMap'))
      //城市经纬度数据
      var geoCoordMap = window.longitudeLatitude
      var GZData = []
      var option = {
        series: [],
        geo: {
          type: 'scatter',
          coordinateSystem: 'bmap',
          tooltip: {
            show: true,
          },
          roam: true,
          show: true,
          zoom: 1.2,
          scaleLimit: { min: 1, max: 12 }, // 缩放级别
          map: 'citymap',
          // 定义样式
          itemStyle: {
            // 普通状态下的样式
            normal: {
              borderColor: '#07919e',
              areaColor: '#1c2f59',
            },
            // 高亮状态下的样式,默认黄色
            emphasis: {
              areaColor: '#1f2533',
            },
          },
          label: {
            normal: {
              //静态的时候展示样式
              show: false, //是否显示地图区的名称
              textStyle: {
                color: '#39fdf4',
              },
            },
            emphasis: {
              //动态展示的样式
              show: true, //是否显示地图区的名称
              color: '#39fdf4',
            },
          },
        },
      }
      var that = this
      // 使用刚指定的配置项和数据显示图表。
      myChart.setOption(option)
      window.addEventListener('resize', () => {
        myChart.resize()
      })
      var getCity = getAction(this.url.getCity).catch(() => {
        return Promise.resolve('getCity')
      })
      var getResByCity = getAction(this.url.getResByCity).catch(() => {
        return Promise.resolve('getResByCity')
      })
      Promise.all([getCity, getResByCity]).then((data) => {
        if (!!data[0].success && !!data[1].success) {
          //城市流线数据
          GZData = data[0].result
          that.cityRes = data[1].result
          const convertData = function (data) {
            var res = []
            for (var i = 0; i < data.length; i++) {
              var geoCoord = geoCoordMap[data[i].cityName]
              if (geoCoord) {
                res.push({
                  name: data[i].cityName,
                  value: geoCoord.concat(data[i].cityId),
                })
              }
            }
            return res
          }
          var color = ['#c5f80e']
          var series = []
          ;[['cityMap', GZData]].forEach(function (item, i) {
            series.push(
              {
                name: item[0],
                type: 'scatter',
                coordinateSystem: 'geo',
                zlevel: 2,
                symbolSize: 20, //六盘水需要放开此配置
                rippleEffect: {
                  brushType: 'stroke',
                },
                label: {
                  normal: {
                    show: true,
                    position: 'left',
                    formatter: '{b}',
                    color: '#fff',
                  },
                },
                itemStyle: {
                  color: '#0F94DF',
                },
                data: convertData(item[1]),
              },
              {
                name: '点',
                type: 'scatter',
                coordinateSystem: 'geo',
                symbol: 'image://' + require('@/assets/img/map-pin.png'),
                symbolOffset: [0, '-65%'],
                symbolSize: [100, 60],
                label: {
                  normal: {
                    show: true,
                    textStyle: {
                      color: '#D8D9DB',
                      lineHeight: 20,
                      fontSize: 12,
                    },
                    formatter: function (params) {
                      var dataset = params.data
                      var cityRateInfo = that.cityRes.find((ele) => ele.cityId === dataset.value[2])
                      var rtStr = ''
                      var AllCount = ''
                      var onRate = ''
                      return ' 终端总数：' + cityRateInfo.AllCount + '\n 在线率：' + cityRateInfo.onRate
                    },
                  },
                },
                itemStyle: {
                  normal: {
                    color: '#F62157', //标志颜色
                  },
                },
                zlevel: 6,
                data: convertData(item[1]),
              }
            )
          })
          myChart.setOption({
            series: series,
          })
        }
      })
    },

    getResByCity() {
      getAction(this.url.getResByCity).then((res) => {
        if (res.success) {
          this.cityRes = res.result
          // this.echart_map()
        }
      })
    },
    //10日内在线趋势图
    getOnlineTrendEcharts() {
      getAction(this.url.onlineTrendUrl).then((res) => {
        if (res.success) {
          let result=res.result.map((item)=>{
            item.offCount=item.sumCount-item.onCount;
            return item;
          });
          this.onlineTrendLineChart(result)
        }
      })
    },
    onlineTrendLineChart(list) {
      // list = [
      //   { createTime: '11:18', onCount: 30, offCount: 10 },
      //   { createTime: '11:18', onCount: 30, offCount: 10 },
      //   { createTime: '11:18', onCount: 30, offCount: 10 },
      //   { createTime: '11:18', onCount: 30, offCount: 10 },
      //   { createTime: '11:18', onCount: 30, offCount: 10 },
      // ]
      let time = list.map((item) => item.createTime.substring(5))
      let title = ['在线数', '离线数']
      let value1 = list.map((item) => item.onCount)
      let value2 = list.map((item) => item.offCount)
      let myChart = this.$echarts.init(document.getElementById('onlineTrendLineChart'))
      myChart.setOption({
        tooltip: {
          show: true,
          trigger: 'axis',
          transitionDuration: 0, //echart防止tooltip的抖动
        },
        // legend: {
        //   left: 'right',
        //   data: title,
        //   color: '#fff',
        //   textStyle: {
        //     color: '#fff',
        //   },
        // },
        grid: {
          top: 16,
          left: 36,
          right: 8,
          bottom: 8,
          // containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: false,
            axisLine: {
              //坐标轴轴线相关设置。数学上的x轴
              show: true,
              lineStyle: {
                color: '#1f313d',
              },
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: '#41759c', //更改坐标轴文字颜色
              },
            },
            splitLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            data: time,
          },
        ],
        yAxis: [
          {
            nameTextStyle: {
              color: '#7ec7ff',
              fontSize: 16,
              padding: 10,
            },
            min: 0,
            splitLine: {
              show: true,
              lineStyle: {
                color: ['#1c2a37'],
                width: 2,
                type: 'solid',
              },
            },
            axisLine: {
              show: false,
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: '#5189ba', //更改坐标轴文字颜色
              },
            },
            axisTick: {
              show: false,
            },
          },
        ],
        series: [
          {
            name: '在线数',
            type: 'line',
            symbol: 'circle', // 默认是空心圆（中间是白色的），改成实心圆
            showAllSymbol: true,
            lineStyle: {
              normal: {
                width: 1,
                color: '#2c81ff', // 线条颜色
              },
              borderColor: 'rgba(0,0,0,.4)',
            },
            itemStyle: {
              color: '#2c81ff',
              borderColor: '#2c81ff',
              borderWidth: 2,
            },
            tooltip: {
              show: true,
            },
            areaStyle: {
              //区域填充样式
              normal: {
                //线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是‘true’，则该四个值是绝对像素位置。
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: 'rgba(25,163,223,.3)',
                    },
                    {
                      offset: 1,
                      color: 'rgba(25,163,223, 0)',
                    },
                  ],
                  false
                ),
                shadowColor: 'rgba(25,163,223, 0.5)', //阴影颜色
                shadowBlur: 20, //shadowBlur设图形阴影的模糊大小。配合shadowColor,shadowOffsetX/Y, 设置图形的阴影效果。
              },
            },
            data: value1,
          },
          {
            name: '离线数',
            type: 'line',
            symbol: 'circle', // 默认是空心圆（中间是白色的），改成实心圆
            showAllSymbol: true,

            lineStyle: {
              normal: {
                width: 1,
                color: '#06cdf6', // 线条颜色
              },
              borderColor: 'rgba(0,0,0,.4)',
            },
            itemStyle: {
              color: '#06cdf6',
              borderColor: '#06cdf6',
              borderWidth: 2,
            },
            tooltip: {
              show: true,
            },
            areaStyle: {
              //区域填充样式
              normal: {
                //线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是‘true’，则该四个值是绝对像素位置。
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: 'rgba(10,219,250,.3)',
                    },
                    {
                      offset: 1,
                      color: 'rgba(10,219,250, 0)',
                    },
                  ],
                  false
                ),
                shadowColor: 'rgba(10,219,250, 0.5)', //阴影颜色
                shadowBlur: 20, //shadowBlur设图形阴影的模糊大小。配合shadowColor,shadowOffsetX/Y, 设置图形的阴影效果。
              },
            },
            data: value2,
          },
        ],
      })
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    },
    //单位在线数量TOP5
   /* gitDeviceCountFive() {
      getAction(this.url.gitDeviceCountFive).then((res) => {
        if (res.success) {
          this.DeviceOnlineFive = res.result
          this.DeviceOnlineFive[0].name=this.DeviceOnlineFive[0].name?this.DeviceOnlineFive[0].name:"无单位"
        }
      })
    },*/



    //区县在线率排名TOP5
    getCityOnRate() {
      getAction(this.url.cityOnRate).then((res) => {
        if (res.success) {
          this.setCityOnRate(res.result)
        }
      })
    },
    setCityOnRate(list) {
      let myChart = this.$echarts.init(document.getElementById('cityOnRateFive'))
      // list = [
      //   { name: 'test1', rate: 100 },
      //   { name: 'test2', rate: 50 },
      //   { name: 'test3', rate: 50 },
      //   { name: 'test4', rate: 50 },
      //   { name: 'test5', rate: 50 },
      // ]
      let yName = list.map((item) => item.name)
      let xData = list.map((item) => item.rate)
      let dataList = list.map((item) => {
        return { name: item.name, value: item.rate }
      })
      myChart.setOption({
        backgroundColor: '#131419',
        xAxis: {
          splitLine: {
            show: false,
          },
          axisLabel: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
        },
        grid: {
          containLabel: true,
          left: 30,
          top: 0,
          right: 100,
          bottom: 0,
        },
        yAxis: [
          {
            inverse: true,
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              margin: 10,
              textStyle: {
                fontSize: 12,
                color: '#fff',
              },
            },
            data: yName,
          },
        ],
        series: [
          {
            //内
            type: 'bar',
            barWidth: 14,
            legendHoverLink: false,
            symbolRepeat: true,
            silent: true,
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 1,
                y2: 0,
                colorStops: [
                  {
                    offset: 0,
                    color: '#00abee', // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: '#62E6F6', // 100% 处的颜色
                  },
                ],
              },
            },
            data: dataList,
            z: 1,
            animationEasing: 'elasticOut',
          },
          {
            // 背景
            type: 'pictorialBar',
            animationDuration: 0,
            symbolRepeat: 'fixed',
            symbolMargin: '20%',
            symbol: 'roundRect',
            symbolSize: [6, 18],
            itemStyle: {
              normal: {
                color: '#12272A',
              },
            },
            label: {
              normal: {
                show: true,
                position: 'right',
                offset: [0, 2],
                distance: 30,
                textStyle: {
                  color: '#7AF8FF',
                  fontSize: 12,
                },
                formatter: function (a, b) {
                  return `${a.value}%`
                },
              },
            },
            data: xData,
            z: 0,
            animationEasing: 'elasticOut',
          },
          {
            //分隔
            type: 'pictorialBar',
            itemStyle: {
              color: '#000',
            },
            symbolRepeat: 'fixed',
            symbolMargin: 4,
            symbol: 'roundRect',
            symbolClip: true,
            symbolSize: [2, 18],
            symbolPosition: 'start',
            symbolOffset: [0, 0],
            data: dataList,
            z: 2,
            animationEasing: 'elasticOut',
          },
        ],
      })
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    },

    formatter: function (num) {
      if (!num) {
        return ''
      }
      return num.toFixed(2) //小数点后几位，数字就是几小数点后几位
    },
  },
}
</script>
<style lang="less" scoped>
.page-box {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  padding-left: 16px;
}
.body {
  width: 100%;
  height: calc(100% - 32px);
  .center-map {
    height: 100%;
    background: #131419;
    padding: 8px;
    .map-box {
      width: 100%;
      height: calc(100% - 24px);
    }
  }
  .row-class {
    width: 100%;
    height: 100%;
  }
  .col-class {
    height: 100%;
  }
  .core {
    height: 100%;
    // display: flex;
    // flex-direction: column;
    // justify-content: space-between;
    .core-top {
      width: 100%;
      height: 27%;
      background: #131419;
      padding: 8px;
      margin-bottom: 16px;
      .core-top-body {
        color: #fff;
        overflow: hidden;
      }
    }
    .core-bottom {
      width: 100%;
      height: calc(46% - 16px);
      background: #131419;
      padding: 8px;
    }
    .core-middle {
      height: calc(27% - 16px);
      width: 100%;
      margin-bottom: 16px;
      background: #131419;
      overflow: hidden;
      padding: 8px;
      .core-middle-body {
        width: 100%;
        height: calc(100% - 24px);
      }
    }
  }
  .right {
    height: 100%;
    // display: flex;
    // flex-direction: column;
    // justify-content: space-between;
    .right-top {
      width: 100%;
      height: 27%;
      background: #131419;
      margin-bottom: 16px;
      padding: 8px;
      .right-top-body {
        height: calc(100% - 24px);
        display: flex;
        align-items: center;
        justify-content: center;
        .left-div {
          width: 20%;
          height: 100%;
          display: flex;
          flex-direction: column;
          text-align: center;
          .left-top-div {
            height: 35%;
            display: flex;
            flex-direction: column;
            .title-span {
              font-size: 12px;
              color: #e3e3e3;
            }
            .number-span {
              font-size: 0.225rem /* 18/80 */;
              color: #01fffc;
              font-style: italic;
            }
          }
          .left-center-div {
            height: 30%;
            display: flex;
            flex-direction: column;
            .title-span {
              font-size: 12px;
              color: #e3e3e3;
            }
            .number-span {
              font-size: 0.225rem /* 18/80 */;
              color: #01fffc;
              font-style: italic;
            }
          }
          .left-bottom-div {
            height: 35%;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            .title-span {
              font-size: 12px;
              color: #e3e3e3;
            }
            .number-span {
              font-size: 0.225rem /* 18/80 */;
              color: #01fffc;
              font-style: italic;
            }
          }
        }
        .img-div {
          width: 60%;
          height: 80%;
          img {
            width: 100%;
            height: 100%;
          }
        }
        .right-div {
          width: 20%;
          height: 100%;
          display: flex;
          flex-direction: column;
          text-align: center;
          .right-top-div {
            height: 50%;
            display: flex;
            flex-direction: column;
            .title-span {
              font-size: 12px;
              color: #e3e3e3;
            }
            .number-span {
              font-size: 0.225rem /* 18/80 */;
              color: #01fffc;
              font-style: italic;
            }
          }
          .right-bottom-div {
            height: 46%;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            .title-span {
              font-size: 12px;
              color: #e3e3e3;
            }
            .number-span {
              font-size: 0.225rem /* 18/80 */;
              color: #01fffc;
              font-style: italic;
            }
          }
        }
      }
    }
    .right-core {
      width: 100%;
      height: calc(27% - 16px);
      background: #131419;
      margin-bottom: 16px;
      padding: 8px;
      .right-core-body {
        height: calc(100% - 24px);
        .right-core-body-bar {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
    .right-bottom {
      height: calc(46% - 16px);
      width: 100%;
      background: #131419;
      overflow: hidden;
      padding: 8px;
      .right-bottom-core {
        width: 100%;
        height: calc(100% - 24px);
        .right-bottom-core-lineChart {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
  }
}
.topTitle,
.topTitleCore,
.rightBottomTitle,
.leftEchartsTitle {
  height: 20%;
  display: flex;
  align-items: center;
  font-size: 0.225rem /* 18/80 */;
  color: #45c5e0;
  padding-left: 0.15rem /* 12/80 */;
  letter-spacing: 0.025rem /* 2/80 */;
  img {
    width: 0.125rem /* 10/80 */;
    height: 0.1625rem /* 13/80 */;
    margin-right: 0.0875rem /* 7/80 */;
  }
}
.topTitleCore {
  height: 14%;
}
.rightBottomTitle {
  height: 8%;
}
.first-title {
  padding-top: 0.125rem /* 10/80 */;
}
.leftEchartsTitle {
  height: 6%;
}
.chart-con {
  width: 100%;
  height: calc(100% - 24px);
}
</style>