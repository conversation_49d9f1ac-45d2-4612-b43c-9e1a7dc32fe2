<template>
  <a-spin class='scroll' :spinning="confirmLoading">
    <div>
      <!--设备基础信息-->
      <div class="colorBox1">
        <div class="colorBox">
          <span class="colorTotal">基本信息</span>
          <span v-if="showabled" style="margin-left: 20px; color: #409eff; cursor: pointer" @click="doEdit(deviceInfo)">
            <a-icon type="edit" style="margin-right: 6px" />编辑
          </span>
          <span style="margin-left: 20px; color: #409eff; cursor: pointer" v-if="grafanaUrl != null">
            <a-icon type="unordered-list" style="margin-right: 6px" /><a :href="grafanaUrl" target="_blank"
              style="color: rgb(64, 158, 255)">历史数据</a>
          </span>
        </div>
        <a-descriptions :column="{ xxl: 2, xl: 2, lg: 2, md: 2, sm: 2, xs: 2 }" bordered>
          <a-descriptions-item label="产品名称" v-if="deviceInfo.productName">{{ deviceInfo.productName }}</a-descriptions-item>
          <a-descriptions-item label="机柜" v-if="positionInfo.cabinetName">{{ positionInfo.location }} {{ positionInfo.cabinetName }}
          </a-descriptions-item>
          <a-descriptions-item label="U位" v-if="positionInfo.layerPool">{{ positionInfo.layerPool }}</a-descriptions-item>
          <a-descriptions-item label="所属单位" v-if="deviceInfo.momgDeptName">{{ deviceInfo.momgDeptName != null ? deviceInfo.momgDeptName : '' }}
          </a-descriptions-item>
          <a-descriptions-item label="管理人" v-if="deviceInfo.manager_dictText">{{ deviceInfo.manager_dictText}}
          </a-descriptions-item>
          <a-descriptions-item label="运维人" v-if="deviceInfo.operator_dictText">{{ deviceInfo.operator_dictText}}
          </a-descriptions-item>
          <a-descriptions-item label="是否重点关注" >
            <span v-if="deviceInfo.isFocus==1" style="color:rgb(64, 222, 90)">是</span>
            <span v-else>否</span>
          </a-descriptions-item>
          <a-descriptions-item label="占用容量（U位）" v-if="deviceInfo.capacity">{{ deviceInfo.capacity }} </a-descriptions-item>
          <a-descriptions-item label="规格（mm）" v-if="deviceInfo.specifications">{{ deviceInfo.specifications }}</a-descriptions-item>
          <a-descriptions-item label="所在位置" v-if="deviceInfo.location">{{ deviceInfo.location }}</a-descriptions-item>
          <a-descriptions-item label="设备说明" v-if="deviceInfo.description">{{ deviceInfo.description }}</a-descriptions-item>
          <a-descriptions-item label="健康等级" v-if="deviceInfo.healthyDegree">{{ deviceInfo.healthyDegree }}</a-descriptions-item>
        </a-descriptions>
      </div>
      <!--连接参数-->
      <div v-if="collectInfoList.length > 0" style="margin-top: 24px; margin-right:1px">
        <div class="colorBox1" v-for='(cInfoItem,idx) in collectInfoList' :key="'collectInfo_'+idx">
          <div class='colorBox'>
            <span class="colorTotal">{{cInfoItem.key}}连接参数</span>
          </div>
          <a-descriptions v-if="cInfoItem.value.length > 0" :column="{ xxl: 2, xl: 2, lg: 2, md: 2, sm: 2, xs: 2 }"
            bordered>
            <a-descriptions-item v-for="(item, index) in cInfoItem.value" :key="'collectInfoItem_'+index"
              :label="item.displayName">
              <!--           拉模式可设置采集频率-->
              <div v-if='index===0&&cInfoItem.type=="1"&&item.displayName==="采集频率"'>
                <div v-if='!cInfoItem.frequency' class='frequency'>
                  <span>{{item.basicRate}}{{item.basicUnit==1?"分":"秒"}}</span>
                  <span class='edit' @click='editfrequency(idx)'>
                    <a-icon type='edit' class='edit-icon' />
                    编辑
                  </span>
                </div>
                <div v-else class='frequency'>
                  <a-input-group compact style='width: auto'>
                    <a-input-number v-model='item.currRate' :step='1' :min='1' :max='59' :precision='0'
                      style='width:120px' />
                    <a-select v-model='item.currUnit'>
                      <a-select-option value="0">秒</a-select-option>
                      <a-select-option value="1">分</a-select-option>
                    </a-select>
                  </a-input-group>
                  <div class='btn'>
                    <span @click='save(idx)'>保存</span>
                    <span @click='cancleFrequency(idx)'>取消</span>
                  </div>
                </div>
              </div>
              <div v-else> {{ item.connectValue }}</div>
            </a-descriptions-item>
            <!--            <a-descriptions-item v-for="(item, index) in cInfoItem.value" :key="'collectInfoItem_'+index" :label="item.displayName" :span='cInfoItem.value.length % 2 === 1?2:1'>
              {{ item.connectValue }}
            </a-descriptions-item>-->
          </a-descriptions>
        </div>
      </div>
      <!--资产信息-->
      <div style="margin-top: 24px; margin-right:1px" v-if='Object.keys(assetsInfo).length>0'>
        <div class="colorBox1">
          <div class='colorBox'>
            <span class="colorTotal">资产信息</span>
          </div>
          <a-descriptions :column="{ xxl: 2, xl: 2, lg: 2, md: 2, sm: 2, xs: 2 }" bordered>
            <a-descriptions-item label="资产编号">{{assetsInfo.assetsCode }}</a-descriptions-item>
            <a-descriptions-item label='资产类型'>{{assetsInfo.assetsCategoryText }}</a-descriptions-item>
            <a-descriptions-item label='供应商'>{{assetsInfo.producerName }}</a-descriptions-item>
            <a-descriptions-item label='型号'>{{assetsInfo.assetsModel }}</a-descriptions-item>
            <a-descriptions-item label='入库日期'>{{assetsInfo.storageTime }}</a-descriptions-item>
            <a-descriptions-item label='是否预警'>{{assetsInfo.isWarning?"是":"否" }}</a-descriptions-item>

            <template v-if='assetsInfo.isWarning==="1"'>
              <a-descriptions-item label="质保开始日期">{{assetsInfo.startQualityTime }}</a-descriptions-item>
              <a-descriptions-item label='质保期限(月)'>{{assetsInfo.qualityTerm }}</a-descriptions-item>
              <a-descriptions-item label='保修单位'>{{assetsInfo.repairFac }}</a-descriptions-item>
              <a-descriptions-item label='保修单位电话'>{{assetsInfo.repairPhone }}</a-descriptions-item>
              <a-descriptions-item label='保修联系人'>{{assetsInfo.warrantyConnect }}</a-descriptions-item>
              <a-descriptions-item label='保修人电话'>{{assetsInfo.warrantyPhone}}</a-descriptions-item>
            </template>
          </a-descriptions>
        </div>
      </div>
      <!--附加字段-->
      <div v-if="additionalFieldList.length > 0" style="margin-top: 24px; margin-right:1px">
        <div class="colorBox1">
          <div class='colorBox'>
            <span class="colorTotal">附加字段</span>
          </div>
          <a-descriptions :column="{ xxl: 2, xl: 2, lg: 2, md: 2, sm: 2, xs: 2 }" bordered>
            <a-descriptions-item v-for='(addItem,idx) in additionalFieldList'
              :span='additionalFieldList.length % 2 === 1?2:1' :key="'additionalField_'+idx" :label="addItem.name">
              {{ addItem.value }}
            </a-descriptions-item>
          </a-descriptions>
        </div>
      </div>
    </div>
    <device-info-modal ref="modalForm" @ok="modalFormOk"></device-info-modal>
  </a-spin>
</template>

<script>
  import {
    postAction,
    getAction
  } from '@/api/manage'
  import pick from 'lodash.pick'
  import {
    queryConfigureDictItem,
    queryConfigureDictItems
  } from '@/api/api'
  import DeviceInfoModal from '../modules/DeviceInfoFormModal.vue'
  import fa from 'element-ui/src/locale/lang/fa'

  export default {
    name: 'BaseInfo',
    components: {
      DeviceInfoModal,
    },
    props: {
      // 流程表单data
      /* formData: {
        type: Object,
        default: () => {},
        required: false,
      },*/
      // 表单模式：true流程表单 false普通表单
      /*formBpm: {
        type: Boolean,
        default: false,
        required: false,
      },*/
      editabled: {
        type: Boolean,
        default: false,
        required: false,
      },
    },
    data() {
      return {
        confirmLoading: false,
        showabled: true, //控制是否显示编辑按钮

        deviceInfo: {},
        positionInfo: {
          roomName: '',
          cabinetName: '',
          layerpool: ''
        },
        collectInfoList: [],
        assetsInfo: {},
        additionalFieldList: [], //附加字段

        grafanaUrl: '',
        url: {
          queryCollectInfoList: '/device/deviceInfo/selectConnectInfo', //通过设备id.获取连接参数
          queryAssetsAndPositionInfo: '/device/deviceInfo/selectAssetsAndPositionInfo', //获取资产信息和设备位置信息
          findCodeValue: '/extendField/extendField/findCodeValue', //通过产品分类/资产类型的id,及资产信息的id获取附加字段
          collectRate: 'device/deviceInfo/selectDevCollectRate', //通过设备deviceCode，获取拉模式设备的采集频率
          saveRate: '/device/deviceInfo/updateDevCollectRate' //通过设备deviceCode，保存拉模式设备的采集频率
        }
      }
    },

    watch: {
      editabled: {
        handler(nv) {
          if (!nv) {
            this.showabled = nv
          }
        },
        immediate: true,
      },
      deviceInfo(val, oldVal) {
        this.init(val)
      }
    },
    // computed: {
    //   formDisabled() {
    //     if (this.formBpm === true) {
    //       if (this.formData.disabled === false) {
    //         return false
    //       }
    //       return true
    //     }
    //     return this.disabled
    //   },
    // },
    methods: {
      show(record) {
        this.deviceInfo = record
      },
      init(record) {
        this.grafanaUrl = record.selfUrl
        this.disabled = true
        //this.getCollectInfoListByDeviceId(record.id)
        this.getCollectInfoListAndCollectRate(record)
        this.getAssetsAndPositionInfo(record.id)

        let CZ_Data = window._CONFIG['customization']
        if (CZ_Data && CZ_Data.cz_zunyi && CZ_Data.cz_zunyi.internetFlag == record.internetFlag) {
          this.grafanaUrl = CZ_Data.cz_zunyi.internetGrafanaURL + record.deviceCode
        } else {
          if (this.grafanaUrl != null) {
            // var ipStr = window.location.href.split('/')
            var ipStr = window.location.host // 获取当前页面地址
            queryConfigureDictItems({
              parentCode: 'grafanaProxy',
              childCode: 'url'
            }).then((res) => {
              if (res.success && res.result) {
                res.result.forEach(el => {
                  var arys = el.value.split('>')
                  if (!!ipStr && ipStr === arys[0]) {
                    this.grafanaUrl = arys[1] + this.grafanaUrl
                  }
                })
              }
            })
          }
        }
      },
      strRegx(str) {
        str = str.replace(/(\d{3})\d*(\d{4})/, '$1****$2')
        alert(str)
      },
      modalFormOk(e) {
        // let refreshDeviceInfo = JSON.parse(JSON.stringify(this.deviceInfo))
        // Object.assign(refreshDeviceInfo, e.deviceInfo)
        // this.$emit('changeDeviceBaseInfo', refreshDeviceInfo)
        this.$emit('changeDeviceBaseInfo')
      },
      //编辑
      doEdit(record) {
        this.$refs.modalForm.edit(record)
        this.$refs.modalForm.title = '编辑'
        this.$refs.modalForm.disableSubmit = false
      },

      /*获取连接参数*/
      getCollectInfoListByDeviceId(id) {
        this.collectInfoList = []
        return new Promise((resolve, reject) => {
          if (!!id) {
            let paramObj = {
              deviceId: id.trim(),
            }
            getAction(this.url.queryCollectInfoList, paramObj).then((res) => {
              if (!!res) {
                this.collectInfoList = res && res.length > 0 ? res : []
                if (this.collectInfoList.length > 0) {
                  this.collectInfoList.forEach((ele) => {
                    ele['frequency'] = false
                    ele.value.forEach((e) => {
                      if (e.controlType == 'inputPassword') {
                        // 某地要求全部不展示，先临时处理
                        e.connectValue = '******'
                      }
                      if (e.dictItemList != null && e.dictItemList.length > 0) {
                        e.dictItemList.forEach((item) => {
                          if (e.connectValue == item.value) {
                            e.connectValue = item.text
                          }
                        })
                      }
                    })
                  })
                  resolve({
                    success: true,
                    message: '操作成功'
                  })
                } else {
                  reject({
                    success: false,
                    message: '操作失败'
                  })
                }
              }
            }).catch(() => {
              reject({
                success: false,
                message: '操作失败'
              })
            })
          }
        })
      },

      getCollectInfoListAndCollectRate(deviceInfo) {
        this.getCollectInfoListByDeviceId(deviceInfo.id).then((res) => {
          if (res.success) {
            // console.log('this.collectInfoList===',JSON.stringify(this.collectInfoList) )
            let arr = this.collectInfoList.filter((item) => item.type == 1)
            if (arr.length > 0) {
              let pro = arr.map((item) => {
                return item.key
              })
              getAction(this.url.collectRate, {
                deviceCode: deviceInfo.deviceCode,
                protocol: pro.join(',')
              }).then((result) => {
                if (result && result.length > 0) {
                  this.collectInfoList.map((ite, index) => {
                    result.map((m) => {
                      if (m.key == ite.key) {
                        ite.value.splice(0, 0, {
                          displayName: '采集频率',
                          basicRate: parseInt(m.value.basicRate),
                          basicUnit: m.value.basicUnit,
                          currRate: parseInt(m.value.basicRate),
                          currUnit: m.value.basicUnit,
                          rateInfo: m.value
                        })
                        let obj = {
                          frequency: false,
                          value: ite.value,
                          type: ite.type,
                          key: ite.key
                        }
                        this.collectInfoList.splice(index, 1, obj)
                      }
                    })
                  })
                }
              })
            }
          }
        })
      },
      /*获取资产、物理位置、附加字段*/
      getAssetsAndPositionInfo(deviceId) {
        this.assetsInfo = {}
        this.positionInfo = {
          roomName: '',
          cabinetName: '',
          layerpool: ''
        }
        getAction(this.url.queryAssetsAndPositionInfo, {
          deviceId: deviceId
        }).then((res) => {
          if (res.success) {
            let posInfo = res.result.cabinet2device
            if (posInfo && Object.keys(posInfo)) {
              Object.assign(this.positionInfo, posInfo)
            }

            let assInfo = res.result.assetsInfo
            if (assInfo && Object.keys(assInfo)) {
              this.assetsInfo = assInfo
            }
            this.getAdditionalFieldByCarIdAndAssetsId(res.result.assetsInfo)
          } else {
            this.$message.warning(res.message)
          }
        }).catch((err) => {
          this.$message.warning(res.message)
        })
      },

      /*通过产品分类/资产类型的id,及资产信息的id获取附加字段*/
      getAdditionalFieldByCarIdAndAssetsId(assetsInfo) {
        this.additionalFieldList = []
        if (assetsInfo && Object.keys(assetsInfo)) {
          if (assetsInfo.assetsCategoryId && assetsInfo.id) {
            let paramObj = {
              assetsCategoryId: assetsInfo.assetsCategoryId.trim(),
              assetsId: assetsInfo.id.trim(),
            }
            getAction(this.url.findCodeValue, paramObj).then((res) => {
              if (res.success) {
                this.additionalFieldList = res.result
              } else {
                this.$message.warning(res.message)
              }
            }).catch((err) => {
              this.$message.warning(err.message)
            })
          }
        }
      },
      /*编辑或取消编辑采集频率*/
      editfrequency(index) {
        let obj = {
          frequency: this.collectInfoList[index].frequency === true ? false : true,
          key: this.collectInfoList[index].key,
          value: this.collectInfoList[index].value,
          type: this.collectInfoList[index].type
        }
        this.collectInfoList.splice(index, 1, obj)
      },
      cancleFrequency(index) {
        let va = this.collectInfoList[index].value[0]
        this.collectInfoList[index].value[0].currUnit = va.basicUnit
        this.collectInfoList[index].value[0].currRate = va.basicRate
        let obj = {
          frequency: this.collectInfoList[index].frequency === true ? false : true,
          key: this.collectInfoList[index].key,
          value: this.collectInfoList[index].value,
          type: this.collectInfoList[index].type
        }
        this.collectInfoList.splice(index, 1, obj)
      },
      save(index) {
        let lis = this.collectInfoList[index].value[0].rateInfo
        lis.basicUnit = this.collectInfoList[index].value[0].currUnit
        lis.basicRate = this.collectInfoList[index].value[0].currRate

        let obj = {
          frequency: this.collectInfoList[index].frequency === true ? false : true,
          key: this.collectInfoList[index].key,
          value: this.collectInfoList[index].value,
          type: this.collectInfoList[index].type
        }
        this.collectInfoList.splice(index, 1, obj)
        postAction(this.url.saveRate, {
          deviceCode: this.deviceInfo.deviceCode,
          templateList: [lis]
        }).then((res) => {
          //console.log('保存res===',res)
          if (res.success) {
            this.collectInfoList[index].value[0].basicRate = lis.basicRate
            this.collectInfoList[index].value[0].basicUnit = lis.basicUnit
            let obj = {
              frequency: false,
              key: this.collectInfoList[index].key,
              value: this.collectInfoList[index].value,
              type: this.collectInfoList[index].type
            }
            this.collectInfoList.splice(index, 1, obj)
          }
        })
      }
    }
  }
</script>

<style lang='less' scoped='scoped'>
  .scroll {
    height: 100%;
    overflow: hidden;
    overflow-y: auto;
  }

  ::v-deep .ant-descriptions-view {
    border-radius: 0px;
  }

  ::v-deep .ant-descriptions-bordered .ant-descriptions-item-label {
    background-color: rgb(250, 250, 250);
    text-align: center;
    width: 17%;
  }

  ::v-deep .ant-descriptions-item-label,
  .ant-descriptions-item-content {
    color: rgb(96, 98, 102) !important;
  }

  ::v-deep .ant-descriptions-bordered .ant-descriptions-item-content {
    width: 35%;
  }

  .colorBox1 {
    margin-bottom: 20px;
    margin-right: 1px;
  }

  .colorBox {
    margin-bottom: 10px;
  }

  .colorTotal {
    padding-left: 7px;
    border-left: 4px solid #1e3674;
  }

  ::v-deep .ant-descriptions {
    width: 100%;
  }

  .frequency {
    display: flex;
    flex-flow: row nowrap;
    justify-content: start;
    align-items: center;

    .edit {
      color: rgb(64, 158, 255);
      cursor: pointer;
      margin-left: 24px;

      .edit-icon {
        margin-right: 6px;
      }
    }

    .btn {
      display: flex;
      flex-flow: row nowrap;
      justify-content: start;
      align-items: center;
      margin-left: 24px;

      & span {
        white-space: nowrap;
        cursor: pointer;
        color: rgb(64, 158, 255);
        margin: 0 3px;
      }
    }
  }
</style>