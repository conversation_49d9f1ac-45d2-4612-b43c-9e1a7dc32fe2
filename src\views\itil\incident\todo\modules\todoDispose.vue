<template>
  <a-drawer
    :width="modalWidth"
    :title="title"
    placement="right"
    :closable="false"
    :visible="visible"
    :after-visible-change="afterVisibleChange"
    @close="onClose"
  >
    <p>请选择指派对象</p>

    <a-tree checkable
        style="margin-bottom: 53px;"
        @expand="onExpand"
        :treeData="gData"
        :checkedKeys="checkedKeys"
        @check="onCheck">
    </a-tree>
    <div
        :style="{
          position: 'absolute',
          right: 0,
          bottom: 0,
          width: '100%',
          borderTop: '1px solid #e9e9e9',
          padding: '10px 16px',
          background: '#fff',
          textAlign: 'right',
          zIndex: 1,
        }"
      >
        <a-button :style="{ marginRight: '8px' }" @click="onClose">
          取消
        </a-button>
        <a-button type="primary" @click="onClose">
          提交
        </a-button>
      </div>
  </a-drawer>
</template>
<script>
import { queryDepartTreeList } from '@/api/api'
const c = [
//   {
//     title: "全部",
//     id: "001",
//     uri: "01",
//     disPlayName: "全部",
//     key: "01",
//     children: []
//   }
];
export default {
  name: 'todoDispose',
  data() {
    return {
      title: '事件处理',
      visible: false,
      autoExpandParent: true,
      modalWidth: '25%',
      checkedKeys: [],
      gData: c,
      todoId: '',
      loading: false
    }
  },
  created() {
      this.loadTree()
  },
  methods: {
    afterVisibleChange(val) {
    },
    showDrawer() {
      this.visible = true
    },
    onClose() {
      this.visible = false
    },
    show(id) {
      this.todoId = id
      this.visible = true
    },
    loadTree() {
      var that = this
      that.treeData = []
      that.departTree = []
      queryDepartTreeList().then(res => {
        if (res.success) {
            for (let i = 0; i < res.result.length; i++) {
              let temp = res.result[i]
              that.gData.push(temp)
            }
          this.loading = false
        }
      })
    },
    onExpand(expandedKeys) {
      // if not set autoExpandParent to false, if children expanded, parent can not collapse.
      // or, you can remove all expanded children keys.
      this.expandedKeys = expandedKeys
      this.autoExpandParent = false
    },
    onCheck(checkedKeys) {
      this.checkedKeys = checkedKeys
    },
    onSelect(selectedKeys, info) {
      this.selectedKeys = selectedKeys
    }
  },
  watch: {},
  mounted() {
      
  }
}
</script>
<style scoped>
.drawer-bootom-button {
  position: absolute;
  bottom: 0;
  width: 100%;
  border-top: 1px solid #e8e8e8;
  padding: 10px 16px;
  text-align: right;
  left: 0;
  background: #fff;
  border-radius: 0 0 2px 2px;
}
</style>
