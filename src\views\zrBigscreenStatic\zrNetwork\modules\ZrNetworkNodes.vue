<template>
  <div class='zr-national-nodes' ref='zrNationalNodes'>
    <zr-bigscreen-title title='节点运行状态'>
    </zr-bigscreen-title>
    <div class='national-nodes-content'>
      <slot>
        <div class='nodes-statics scroll-list-item'>
          <div class='item-name'>
            名称
          </div>
          <div class='item-status'>状态</div>
          <div class='item-day'>稳定运行天数</div>
<!--          <div class='item-device'>设备数量</div>-->
        </div>
        <vue-seamless-scroll v-if='srollOption.autoPlay' :data='listData' class='scroll-warp'
                             :class-option='srollOption'>
          <ul class='scroll-list'>
            <li class='scroll-list-item' :class='{"scroll-list-odd":index%2!==0}' v-for='(item, index) in listData'
                :key='index'>
              <div class='item-name' :title='item.name' :style='{"--status-color":item.color}'>
                {{ item.name }}
              </div>
              <div class='item-status' :style='{color:item.color}'>{{ item.statusText }}</div>
              <div class='item-day'>{{ item.day }}</div>
<!--              <div class='item-device'>{{ item.devices }}</div>-->
            </li>
          </ul>
        </vue-seamless-scroll>
        <div v-else class='scroll-warp'>
          <ul class='scroll-list'>
            <li class='scroll-list-item' :class='{"scroll-list-odd":index%2!==0}' v-for='(item, index) in listData'
                :key='index'>
              <div class='item-name' :title='item.name' :style='{"--status-color":item.color}'>
                {{ item.name }}
              </div>
              <div class='item-status' :style='{color:item.color}'>{{ item.statusText }}</div>
              <div class='item-day'>{{ item.day }}</div>
            </li>
          </ul>
        </div>
      </slot>
    </div>
  </div>
</template>
<script>
import ZrBigscreenTitle from '@views/zrBigscreens/modules/ZrBigscreenTitle.vue'
import vueSeamlessScroll from 'vue-seamless-scroll'
import resizeObserverMixin from '@views/statsCenter/com/resizeObserverMixin'
import { businessStatus } from '@views/zrBigscreens/modules/zrUtil'
import {organizations} from '@views/zrBigscreens/modules/zrOrganizations'
import { flatTreeData } from '@/utils/util'
export default {
  name: 'ZrNetworkNodes',
  components: { ZrBigscreenTitle, vueSeamlessScroll },
  mixins: [resizeObserverMixin],
  props:{
    nodes:{
      type:Object,
      default:()=>{return {}}
    }
  },
  data() {
    return {
      listData: [],
      srollOption: {
        step: 0.5, // 步长
        speed: 100, // 滚动速度
        timer: 3000,// 滚动时间间隔
        autoPlay: false,
        limitMoveNum: 10000,
        singleHeight: 36 ,
      },
      maxNum: 0,
      colors:[
        '#55A7F4', // 正常
        '#F4A655', // 异常
        '#F45555'  // 故障
      ]
    }
  },
  created() {

  },
  mounted() {
    this.$nextTick(()=>{
      this.setPlayState()
    })

  },
  watch:{
    nodes:{
      handler(newVal, oldVal){
        this.listData = flatTreeData([this.nodes], null, [])
        this.mapList()
        if (this.maxNum>0 && this.maxNum < this.listData.length) {
          this.srollOption.limitMoveNum = this.maxNum
          this.srollOption.autoPlay = true
        }else{
          this.srollOption.autoPlay = false
        }
      },
      immediate:true,
      deep:true
    }
  },
  methods: {
    mapList() {
      this.listData = this.listData.map(el => {
        let bs = businessStatus.find(item => item.value == el.status)
        if (bs) {
          el.color = bs.color
          el.statusText = bs.label
        }
        return el
      }).sort((a, b)=>{
        return a.day - b.day;
      })
    },
    // 屏幕变化回调
    resizeObserverCb() {
      this.setPlayState()
    },
    //设置滚动状态
    setPlayState() {
      if (this.$refs.zrNationalNodes && this.listData.length) {
        let bounded = this.$refs.zrNationalNodes.getBoundingClientRect()
        this.maxNum = Math.floor((bounded.height - 38 - 116) / 36)
        if (this.maxNum>0 && this.maxNum < this.listData.length) {
          this.srollOption.limitMoveNum = this.maxNum
          this.srollOption.autoPlay = true
          return
        }
      }
      this.srollOption.autoPlay = false
      this.srollOption.limitMoveNum = this.listData.length + 1
    }
  }
}
</script>


<style scoped lang='less'>
.zr-national-nodes {
  height: 100%;

  .national-nodes-content {
    height: calc(100% - 51px);
    overflow: hidden;
    padding: 12px 12px 0;
    background: linear-gradient(to right, rgba(29, 78, 140, 0.3), rgba(29, 78, 140, 0.0));
    margin-top: -5.5px;
  }
}
.nodes-statics{
  background: rgba(94, 140, 199, 0.5);
  color: rgba(237, 245, 255, 0.95);
}
.scroll-warp {
  height: 100%;
  overflow: hidden;

  .scroll-list {
    width: 100%;
    height: 100%;
    margin: 0px;
    padding: 0px;
  }


}
.scroll-list-item {
  display: flex;
  align-items: center;
  color: rgba(237, 245, 255, 0.95);
  font-size: 14px;
  justify-content: space-between;
  height: 36px;
  .item-name {
    width: 30%;
    overflow: hidden;
    position: relative;
    padding: 0 8px;
    line-height: 1;
    opacity: 0.95;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .item-day {
    width: 35%;
    text-align: center;
    opacity: 0.95;
  }

  .item-status {
    width: 15%;
    text-align: left;
    opacity: 0.95;
  }
  .item-device{
    width: 20%;
    text-align: center;
    opacity: 0.95;

  }
}

.scroll-list-odd {
  background: rgba(29, 78, 140, 0.25);
}
</style>