<template>
  <div style="height:100%;">
    <keep-alive exclude='perAppointmentDetails'>
      <component :is="pageName" style="height:100%" :data="data"/>
    </keep-alive>
  </div>
</template>
<script>
import perAppointmentList from './perAppointmentList'
import perAppointmentDetails from './perAppointmentDetails'
export default {
  name: "perAppointmentManage",
  data() {
    return {
      isActive: 0,
      data:{}
    };
  },
  components: {
    perAppointmentList,
    perAppointmentDetails
  },
  created(){
    this.pButton1(0);
  },
  //使用计算属性
  computed: {
    pageName() {
      switch (this.isActive) {
        case 0:
          return "perAppointmentList";
          break;

        default:
          return "perAppointmentDetails";
          break;
      }
    }
  },
  methods: {
    pButton1(index) {
      this.isActive = index;
    },
    pButton2(index,item) {
      this.isActive = index;
      this.data = item;
    }
  }
}
</script>