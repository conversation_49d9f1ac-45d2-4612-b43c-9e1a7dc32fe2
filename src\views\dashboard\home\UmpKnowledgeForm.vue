<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-item label="类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <!-- <a-input v-decorator="['type']" placeholder="请输入类型"></a-input> -->
              <component
                :is="LcDict"
                v-decorator="['type']"
                :trigger-change="true"
                placeholder=""
                dictCode="knowledge_event_type"
              ></component>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="标题" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['title', { rules: [{ required: true, message: '请输入标题!' }] }]" placeholder=""></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="描述" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <!-- <a-input v-decorator="['description']" placeholder="请输入描述"></a-input> -->
              <a-textarea
                  v-decorator="['description', { rules: [{ required: true, message: '请输入描述!' }] }]"
                  placeholder=""
                  :auto-size="{ minRows: 3, maxRows: 5 }"
                />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="解决方案" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <!-- <a-input v-decorator="['plan']" placeholder="请输入解决方案"></a-input> -->
              <a-textarea
                  v-decorator="['plan']"
                  placeholder=""
                  :auto-size="{ minRows: 3, maxRows: 5 }"
                />
            </a-form-item>
          </a-col>
          <!-- <a-col :span="24" >
            <a-form-item label="这条数据的类型 1、事件，2、问题" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number v-decorator="['recordType']" :default-value="3"/>
            </a-form-item>
          </a-col> -->
          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import pick from 'lodash.pick'
  import { validateDuplicateValue } from '@/utils/util'
  import JFormContainer from '@/components/jeecg/JFormContainer'

  export default {
    name: 'UmpKnowledgeForm',
    components: {
      JFormContainer,
    },
    props: {
      //流程表单data
      formData: {
        type: Object,
        default: ()=>{},
        required: false
      },
      //表单模式：true流程表单 false普通表单
      formBpm: {
        type: Boolean,
        default: false,
        required: false
      },
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        form: this.$form.createForm(this),
        model: {},
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        validatorRules: {
        },
        url: {
          add: "/knowledge/add",
          edit: "/knowledge/edit",
          queryById: "/knowledge/queryById"
        }
      }
    },
    computed: {
       //可行性测试，根据文件路径动态加载组件
      LcDict: function () {
        var myComponent = () => import(`@/components/dict/JDictSelectTag`)
        return myComponent
      },
      formDisabled(){
        if(this.formBpm===true){
          if(this.formData.disabled===false){
            return false
          }
          return true
        }
        return this.disabled
      },
      showFlowSubmitButton(){
        if(this.formBpm===true){
          if(this.formData.disabled===false){
            return true
          }
        }
        return false
      }
    },
    created () {
      //如果是流程中表单，则需要加载流程表单data
      this.showFlowData();
    },
    methods: {
      add () {
        this.edit({});
      },
      edit (record) {
        this.form.resetFields();
        this.model = Object.assign({}, record);
        this.visible = true;
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.model,'type','title','description','plan','recordType'))
        })
      },
      //渲染流程表单数据
      showFlowData(){
        if(this.formBpm === true){
          let params = {id:this.formData.dataId};
          getAction(this.url.queryById,params).then((res)=>{
            if(res.success){
              this.edit (res.result);
            }
          });
        }
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            let formData = Object.assign(this.model, values);
            formData.recordType =1;
            httpAction(httpurl,formData,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
         
        })
      },
      popupCallback(row){
        this.form.setFieldsValue(pick(row,'type','title','description','plan','recordType'))
      },
    }
  }
</script>