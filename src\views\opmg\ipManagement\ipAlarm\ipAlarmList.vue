<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <!-- 查询区域 -->
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class="card-style">
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="任务名称">
                  <a-input placeholder="请输入任务名称" v-model="queryParam.auditTaskName" autocomplete="off"
                    :allowClear="true" :maxLength="50">
                  </a-input>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="IP地址">
                  <a-input placeholder="请输入IP地址" v-model="queryParam.ipAddress" autocomplete="off" :allowClear="true"
                    :maxLength="50">
                  </a-input>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="处理状态">
                  <a-select v-model='queryParam.handleStatus' :allow-clear='true' placeholder='请选择处理状态'>
                    <a-select-option v-for='item in handleStatusList' :key='item.value' :value="item.value" :label='item.label' >{{item.label}} </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue" v-show="toggleSearchStatus">
                <a-form-item label="告警时间">
                  <a-range-picker class="a-range-picker-choice-date" @change="onChange"
                    v-model="queryParam.alarmTime2Range" format="YYYY-MM-DD" :placeholder="['开始时间', '截止时间']" />
                </a-form-item>
              </a-col>
              <a-col :span="colBtnsSpan()">
                <span class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button class="btn-search btn-search-style" type="primary" @click="searchQuery">查询</a-button>
                  <a-button class="btn-reset btn-reset-style" @click="searchReset">重置</a-button>
                  <a v-if="isVisible" class="btn-updown-style" @click="doToggleSearch">
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered="false" style="width: 100%; flex: auto">
        <!-- 操作按钮区域 -->
        <div class="table-operator table-operator-style">
          <a-dropdown v-if="selectedRowKeys.length > 0">
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item key="1" @click="batchDel">删除</a-menu-item>
            </a-menu>
            <a-button>批量操作
              <a-icon type="down" />
            </a-button>
          </a-dropdown>
        </div>
        <!-- table区域-begin -->
        <a-table ref="table" bordered :rowKey="(record) => { return record.id}" :columns="columns"
          :dataSource="dataSource" :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}" :pagination="ipagination"
          :loading="loading" :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          @change="handleTableChange">
          <template slot="alarmLevel" slot-scope="text,record">
            <div :style='{backgroundColor:getAlarmColor(text,record)}'
              style='display:inline-block;color:#ffffff; border-radius: 10px; padding: 2px 10px;'>
              {{ getAlarmTitle(text,record) }}
            </div>
          </template>
          <template slot="confirmStatus" slot-scope="text">
            <span v-if="text == 1">已完成</span>
            <span v-else-if="text == 2">已关闭</span>
            <span v-else>未处理</span>
          </template>
          <!-- 字符串超长截取省略号显示-->
          <span slot="templateContent" slot-scope="text">
            <j-ellipsis :value="text" :length="25" />
          </span>

          <span slot="action" slot-scope="text, record" class="caozuo">
            <a @click="handleDetailPage(record)">查看</a>
            <a-divider type="vertical" />
            <a-dropdown>
                <a class="ant-dropdown-link">更多 <a-icon type="down"/></a>
                <a-menu slot="overlay">
                  <a-menu-item v-if='record.handleStatus==handleStatusList[0].value'>
                    <a href="javascript:void(0);" style="color: #409eff" @click='launchOrder(record)'>处理</a>
                  </a-menu-item>
                  <a-menu-item v-if='record.handleStatus==handleStatusList[0].value'>
                    <a href="javascript:void(0);" style="color: #409eff" @click='closeAlarm(record)'>关闭</a>
                  </a-menu-item>
                  <a-menu-item >
                    <a href="javascript:void(0);" style="color: #409eff" @click='confirmDelete(record.id)'>删除</a>
                  </a-menu-item>
                </a-menu>
              </a-dropdown>
          </span>
          <template slot="tooltip" slot-scope="text">
            <a-tooltip placement="topLeft" :title="text" trigger="hover">
              <div class="tooltip">
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
      </a-card>
      <!-- 表单区域 -->
      <Process-instance-start v-if='dialogStartInstanceVisible'
        :dialogStartInstanceVisible.sync='dialogStartInstanceVisible' :process-definition='processDefinition'
        :associationId='associationId' :alarmHistory='alarmHistory' :formUrl='formUrl' :startUrl='startUrl' :dict-key='"alarmIPConfirm"'
        :showDdraft='false' method='post' @loadData='loadData'>
      </Process-instance-start>
    </a-col>
  </a-row>
</template>

<script>
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import JEllipsis from '@/components/jeecg/JEllipsis'
  import {
    deleteAction,
    getAction,
    putAction
  } from '@/api/manage'
  import JDictSelectTag from '@/components/dict/JDictSelectTag.vue'
  import {
    YqFormSearchLocation
  } from '@/mixins/YqFormSearchLocation'
  import ProcessInstanceStart from '@/views/flowable/process-instance-start/module/ProcessInstanceStart.vue'
  import {
    queryConfigureDictItem
  } from '@/api/api'

  export default {
    name: 'AssetsAlarmList',
    mixins: [JeecgListMixin, YqFormSearchLocation],
    components: {
      ProcessInstanceStart
    },
    data() {
      return {
        formItemLayout: {
          labelCol: {
            style: 'width:80px',
          },
          wrapperCol: {
            style: 'width:calc(100% - 80px)'
          }
        },
        handleStatusList: [
          {
            value: '0',
            label: '未处理'
          },
          {
            value: '1',
            label: '已完成'
          }
        ],
        // 表头
        columns: [
          {
            title: '审计任务',
            dataIndex: 'auditTaskName'
          },
          {
            title: 'IP地址',
            dataIndex: 'ipAddress'
          },
          {
            title: '告警时间',
            dataIndex: 'alarmTime'
          },
          {
            title: '审计策略',
            dataIndex: 'auditStrategyText'
          },
          {
            title: '处理状态',
            dataIndex: 'handleStatus',
            scopedSlots: {
              customRender: 'confirmStatus'
            }
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: 160,
            scopedSlots: {
              customRender: 'action'
            },
          },
        ],
        url: {
          list: '/devops/ip/auditTaskAlarm/list',
          delete: '/devops/ip/auditTaskAlarm/delete', //删除接口
          deleteBatch: '/devops/ip/auditTaskAlarm/deleteBatch', //批量删除接口
          alarmClose: '/devops/ip/auditTaskAlarm/alarmClose', //关闭
          alarmTemplateInfo: '/alarm/alarmTemplate/queryById', //通过告警策略模板id，查找策略信息
        },
        processDefinitionKey: "",
        associationId: "",
        formUrl: "",
        startUrl: "",
        dialogStartInstanceVisible: false,
        processDefinition: undefined,
        alarmHistory: {},
        disableMixinCreated: true,
      }
    },
    //将初始化加载流程实例修改为点击处理按钮时加载流程实例
    // created() {
    //   this.getProcessDefinitionKey()
    // },
    activated() {
      this.loadData()
    },
    methods: {
      //时间
      onChange(date, dateString) {
        this.queryParam.startTime = dateString[0]
        this.queryParam.endTime = dateString[1]
      },
      /**获取流程定义key,并根据key,初始化流程相关变量*/
      getProcessDefinitionKey() {
        queryConfigureDictItem({
          parentCode: 'businessToProcesKey',
          childCode: 'alarmIPProcessKey',
        }).then((res) => {
          if (res.success) {
            this.processDefinitionKey = res.result
          } else {
            this.processDefinitionKey = "eventProcessShenzhen"
          }
          this.initFlowData()
        })
      },
      /**初始化流程相关变量*/
      initFlowData() {
        getAction('/flowable/processDefinition/queryByKey', { processDefinitionKey: this.processDefinitionKey })
          .then((res) => {
            if (res.success) {
              this.processDefinition = res.result
              this.formUrl = "/association/process/renderedStartForm"
              this.startUrl = "/association/process/start"
            } else {
              this.$message.warning(res.message)
            }
          }).catch((err) => {
          this.$message.warning(err.message)
        })
      },
      /**处理。，打开流程表单*/
      launchOrder(data) {
        //初始化
        this.getProcessDefinitionKey()
        if (this.processDefinition.id && this.processDefinition.id.length > 0 && this.processDefinitionKey.length > 0) {
          this.associationId = data.id
          this.alarmHistory = data
          this.alarmHistory.processDefinitionId = this.processDefinition.id
          this.dialogStartInstanceVisible = true
        }
      },
      /**关闭告警*/
      closeAlarm(record) {
        let that = this
        that.$confirm({
          title: '确认关闭',
          okText: '是',
          cancelText: '否',
          content: '是否关闭告警?',
          onOk: function() {
            that.loading = true
            getAction(that.url.alarmClose, { id: record.id })
              .then((res) => {
                if (res.success) {
                  that.$message.success(res.message)
                  that.loadData()
                } else {
                  that.$message.warning(res.message)
                  that.loading = false
                }
              }).catch((err) => {
              that.$message.warning(err.message)
              that.loading = false
            })
          }
        })
      }
    }
  }
</script>
<style lang='less' scoped>
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';

  .caozuo .dropdown-disabled {
    color: rgba(0, 0, 0, 0.25) !important;
    cursor: default;
  }
  .confirm {
    color: rgba(0, 0, 0, 0.25) !important;
  }
</style>