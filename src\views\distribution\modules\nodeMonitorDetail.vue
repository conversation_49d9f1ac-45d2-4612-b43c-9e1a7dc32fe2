<template>
  <a-card style="height: 100%">
    <a-row>
      <a-col :span="24">
        <span style="margin-left: 10px;font-size: 18px;">{{ data.nodeName }}</span>
        <span style="float: right; margin-bottom: 12px">
          <img src="~@/assets/return1.png" alt @click="getGo" style="width: 20px; height: 20px; cursor: pointer" />
        </span>
      </a-col>
      <a-col :span="24">
        <table class="gridtable">
          <tr>
            <td class="leftTd">磁盘总数</td>
            <td class="rightTd">{{ dataSource.length > 0 ? dataSource.length:0 }}</td>
            <td class="leftTd">磁盘在线数</td>
            <td class="rightTd">{{ dataSource.length > 0 ? dataSource.length : 0 }}</td>
          </tr>
          <tr>
            <td class="leftTd">总存储容量</td>
            <td class="rightTd">{{ totalStorageCapacity }}</td>
            <td class="leftTd">可用存储容量</td>
            <td class="rightTd">{{ allAvailableStorageCapacity }}</td>
          </tr>
          <tr>
            <td class="leftTd">内存使用量</td>
            <td class="rightTd">{{ memUsageString }}</td>
          </tr>
        </table>
        <a-table ref='table' :columns='columns' :dataSource='dataSource' bordered style="margin-top: 32px;"
          :pagination="false">
        </a-table>
      </a-col>
    </a-row>
  </a-card>
</template>

<script>
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import {
    getAction
  } from '@/api/manage'
  export default {
    name: 'nodeMonitorDetail',
    mixins: [JeecgListMixin],
    props: {
      data: {
        type: Object,
      },
    },
    data() {
      return {
        form: this.$form.createForm(this),
        model: {},
        cpuUsage: 0,
        memUsageString: '',
        diskNum: 0,
        onlineDiskNum: 0,
        totalStorageCapacity: 0,
        allAvailableStorageCapacity: 0,
        queryParam: {
          nodeIp: '',
          nodePort: '',
        },
        columns: [{
          title: ' 磁盘名称',
          dataIndex: 'diskName',
        }, {
          title: '磁盘使用量',
          dataIndex: 'diskUsedString',
          customCell: () => {
            let cellStyle = 'text-align: left'
            return {
              style: cellStyle
            }
          }
        }, {
          title: '空闲inode数',
          dataIndex: 'freeINode',
          customCell: () => {
            let cellStyle = 'text-align: right'
            return {
              style: cellStyle
            }
          }
        }, {
          title: '磁盘延迟(ms)',
          dataIndex: 'diskLatency',
          customCell: () => {
            let cellStyle = 'text-align: right'
            return {
              style: cellStyle
            }
          }
        }, {
          title: '磁盘错误数',
          dataIndex: 'diskErrorNum',
          customCell: () => {
            let cellStyle = 'text-align: right'
            return {
              style: cellStyle
            }
          }
        }, {
          title: 'IO操作等待数',
          dataIndex: 'requestsWaitNum',
          customCell: () => {
            let cellStyle = 'text-align: right'
            return {
              style: cellStyle
            }
          }
        }, ],
        url: {
          list: 'distributedStorage/node/info',
        },
      }
    },
    created() {},
    methods: {
      loadData() {
        this.queryParam.nodePort = this.data.nodePort
        this.queryParam.nodeIp = this.data.nodeIp
        var params = this.getQueryParams() //查询条件
        this.loading = true
        getAction(this.url.list, params).then((res) => {
          if (res.success && res.result) {
            this.dataSource = res.result.diskInfos
            this.memUsageString = res.result.memUsageString
            this.cpuUsage = res.result.cpuUsage
            this.allAvailableStorageCapacity = res.result.allAvailableStorageCapacity
            this.totalStorageCapacity = res.result.totalStorageCapacity
            this.onlineDiskNum = res.result.onlineDiskNum
            this.diskNum = res.result.diskNum
            if (this.dataSource.length < 9) {
              this.clientHeight = false
            }
            this.ipagination.total = res.result.total ? res.result.total : 0
          } else {
            this.$message.warning(res.message)
          }
          this.loading = false
        })
      },
      //返回上一级
      getGo() {
        this.$parent.pButton2(0)
      },
    },
  }
</script>
<style lang="less" scoped>
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';

  ::v-deep .two-words>div>label {
    letter-spacing: 4px;
  }

  ::v-deep .two-words>div>label::after {
    letter-spacing: 0px;
  }

  table.gridtable {
    font-family: verdana, arial, sans-serif;
    font-size: 14px;
    color: #606266;
    border-width: 1px;
    border-color: #e8e8e8;
    border-collapse: collapse;
    text-align: left;
    width: 100%;
  }

  table.gridtable td {
    border-width: 1px;
    border-style: solid;
    border-color: #e8e8e8;
  }

  .leftTd {
    width: 17%;
    background-color: #fafafa;
    padding: 16px 24px;
    text-align: center;
  }

  .rightTd {
    width: 35%;
    padding: 16px 24px;
  }
</style>