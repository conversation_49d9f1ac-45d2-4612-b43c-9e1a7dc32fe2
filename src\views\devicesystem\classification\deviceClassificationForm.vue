<template>
  <a-spin :spinning='confirmLoading'>
    <j-form-container>
      <a-form-model ref='form' slot='detail' :model='model' :rules='validatorRules'>
        <div class='colorBox'>
          <span class='colorTotal'>基础参数</span>
        </div>
        <a-row>
          <a-col :sm='24' :md='12'>
            <a-form-model-item label='类型名称' prop='typeName' v-bind='formItemLayout'>
              <a-input v-model='model.typeName' :allow-clear='true' autocomplete='off' placeholder='请输入类型名称' />
            </a-form-model-item>
          </a-col>
          <a-col :sm='24' :md='12'>
            <a-form-model-item label='类型标识' prop='typeCode' v-bind='formItemLayout'>
              <a-input v-model='model.typeCode' :allow-clear='true' autocomplete='off' placeholder='请输入类型标识' />
            </a-form-model-item>
          </a-col>
          <a-col :sm='24' :md='12'>
            <a-form-model-item label='描述' prop='description' v-bind='formItemLayout'>
              <a-textarea
                v-model='model.description'
                :auto-size='{ minRows: 1, maxRows: 6 }'
                :allowClear='true'
                autocomplete='off'
                placeholder='请输入描述信息'
             />
            </a-form-model-item>
          </a-col>
          <a-col :sm='24' :md='12'>
            <a-form-model-item label='是否使用' prop='isUsed' v-bind='formItemLayout'>
              <a-switch unCheckedChildren='否' checkedChildren='是' v-model='model.isUsed'></a-switch>
            </a-form-model-item>
          </a-col>
        </a-row>
        <div class='colorBox'>
          <span class='colorTotal'>绑定参数</span>
        </div>
        <a-row>
          <a-col :sm='24' :md='12'>
            <a-form-model-item label='产品名称' prop='productId' v-bind='formItemLayout'>
              <a-tree-select
                v-model="model.productId"
                :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                allowClear
                show-search
                tree-node-filter-prop='title'
                placeholder="请选择产品名称"
                :tree-data="productList"
                :multiple="true"
                tree-icon
                tree-checkable
                :show-checked-strategy="SHOW_PARENT"
                :maxTagCount='5'
                @change="changeProductName">
              </a-tree-select>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>
<script>
  import {getAction, httpAction} from '@/api/manage'
  import { TreeSelect } from 'ant-design-vue';
  const SHOW_PARENT = TreeSelect.SHOW_PARENT;
  export default {
    name: 'deviceClassificationModal',
    components:{TreeSelect},
    data() {
      return {
        title: "操作",
        width: 800,
        visible: false,
        confirmLoading: false,
        formItemLayout: {
          labelCol: {
            xs:{span:24},
            sm:{span:5},
            md:{span:7}
          },
          wrapperCol: {
            xs:{span:24},
            sm:{span:18},
            md:{span:17}
          }
        },
        SHOW_PARENT,
        productList: [],
        childrenList: [],
        model: {
          typeName: '',
          description: '',
          typeCode: '',
          productId: [],
          isUsed: false,
        },
        //表单校验规则
        validatorRules: {
          typeName: [
            {required: true,message: '请输入类型名称'},
            {min: 1, max: 100,message: '类型名称长度应在 1-100 之间'}
          ],
          typeCode: [
            { required: true,message: '请输入类型标识'},
            {min: 1, max: 100,message: '类型标识长度应在 1-100 之间'}
          ],
          description: [
            {required: false, min: 1, max: 200, message: '描述长度应在 1-200 之间'}
          ],
          productId: [
            {required: true,message: '请选择产品名称'}
          ],
        },

        url: {
          add: '/product/categoryType/add', //新增提交接口
          edit: '/product/categoryType/edit', //编辑提交接口
          queryAllProduct: '/assetscategory/assetsCategory/selectTree', //获取所有的产品
        }
      }
    },
    created() {
      this.queryAllProduct()
    },
  methods: {
      // 此方法遍历所有的树节点，返回ids所包含的项
      findMatchingObjects(arr, ids) {
        let result = [];
        for (let item of arr) {
          if (ids.includes(item.id)) {
            result.push(item);
          }
          if (item.children && item.children.length > 0) {
            result = result.concat(this.findMatchingObjects(item.children, ids));
          }
        }
        return result;
      },
      /*清除产品名称选项时，将childrenList清空*/
      changeProductName(value, text, option) {
        // 遍历筛选出所有符合条件的对象
        var matchingObjects = this.findMatchingObjects(this.productList, value);
        // 构造所需的数据
        if (matchingObjects.length > 0) {
          this.childrenList = matchingObjects.map((item) => {
            return { childId: item.id, childType: item.type || 'category' }
          })
        }
      },
      /*获取产品名称的下拉框数据源*/
      queryAllProduct() {
        this.confirmLoading=true
        return new Promise((resolve, reject) => {
          getAction(this.url.queryAllProduct)
            .then((res) => {
              if (res.success) {
                this.productList = res.result
                this.setProductNodeIcon(this.productList, '')
                resolve({ success: true, message: res.message })
              } else {
                reject({ success: false, message: res.message })
              }
              this.confirmLoading=false
            }).catch((err) => {
            this.confirmLoading=false
            reject({ success: false, message: err.message })
          })
        })
      },
      /*设置产品分类、产品节点的图标*/
      setProductNodeIcon(data, pid = '') {
        if (data.length && data.length > 0) {
          for (let i = 0; i < data.length; i++) {
            data[i] = {
              ...data[i],
              categoryId: data[i].type != 'product' ? data[i].value : pid,
              isLeaf: data[i].children.length == 0,
              icon:
                data[i].type != 'product' ? (
                  <a-icon type='folder' style='color:#409eff' />
                ) : (
                  <a-icon type='file' style='color:#409eff' />
                ),
            }
            if (data[i].children.length > 0) {
              this.setProductNodeIcon(data[i].children, data[i].value)
            }
          }
        }
      },
      handleCancel() {
        this.visible = false;
      },

      add() {
        this.edit({
          typeName: '',
          description: '',
          typeCode: '',
          isUsed:0,
          productId:[]
        })
      },
      edit(record) {
        this.visible = true
        this.model.id=''
        this.childrenList=[]
        this.$nextTick(() => {
          let that = this
          that.$refs.form.clearValidate()
          that.model=Object.assign(that.model,JSON.parse(JSON.stringify(record)))
          that.model.productId=[]
          that.model.isUsed =record.isUsed == 0 ?true:false
          if (record.childrenList && record.childrenList.length > 0) {
            that.childrenList = record.childrenList
            record.childrenList.forEach((ele) => {
              that.model.productId.push(ele.childId)
            })
          }
        })
      },
      /* 提交表单数据*/
      submitForm() {
        const that = this
        // 触发表单验证
        that.$refs.form.validate((err, values) => {
          if (err) {
            let mainData = JSON.parse(JSON.stringify(that.model))
            mainData.isUsed = mainData.isUsed == true ? 0 : 1
            delete mainData.productId
            mainData.childrenList = that.childrenList
            that.submitData(mainData)
          }
        })
      },
      /*提交数据*/
      submitData(mainData) {
        let that = this
        that.confirmLoading = true
        let httpurl = ''
        let method = ''
        if (!that.model.id) {
          httpurl += that.url.add
          method = 'post'
        } else {
          httpurl += that.url.edit
          method = 'put'
        }
        let formData = {
          ...mainData
        }
        httpAction(httpurl, formData, method)
          .then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.$emit('ok')
            } else {
              that.$message.warning(res.message)
            }
            that.confirmLoading = false
          }).catch((res) => {
            that.$message.warning(res.message)
            that.confirmLoading = false
          })
      },
    }
  }
</script>
<style lang='less' scoped>
  .colorBox {
    margin-bottom: 18px;
  }

  .colorTotal {
    padding-left: 7px;
    border-left: 4px solid #1e3674;
  }
</style>