<template>
  <a-modal destroyOnClose width="50%" :visible="visible" :footer='null'  @cancel='hide'>
      <user-info></user-info>
  </a-modal>
</template>

<script>
import UserInfo from "./Index.vue"
export default {
  components:{
    UserInfo,
  },
  data(){
    return{
      visible:false,
    }
  },
  created() {
  },
  mounted() {
  },
  methods:{
    show(){
      this.visible = true;
    },
    hide(){
      this.visible = false;
    }
  }
}
</script>


<style scoped lang='less'>

</style>