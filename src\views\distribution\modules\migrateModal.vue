<template>
  <j-modal :title='title' :width='width' :visible='visible' :destroyOnClose='true'
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }" :centered='true' @ok='handleOk' @cancel='handleCancel'
    cancelText='关闭'>
    <a-spin :spinning='confirmLoading'>
      <j-form-container :disabled='disableSubmit'>
        <a-form-model ref='form' slot='detail' :model='model' :rules='validatorRules' :labelCol='labelCol'
          :wrapperCol='wrapperCol'>
          <a-row>
            <a-col :span="24">
              <a-form-model-item label='名称' prop='name'>
                <a-input style='width: 100%' v-model='model.name' :allow-clear='true' autocomplete='off'
                  placeholder='请输入名称' />
              </a-form-model-item>
            </a-col>
<!--            <a-col :span="12">-->
<!--              <a-form-model-item label='类型' prop='type'>-->
<!--                <a-select v-model='model.type' @change=statusChange placeholder='请选择类型'>-->
<!--                  <a-select-option value="0">用户数据源</a-select-option>-->
<!--                  <a-select-option value="1">终端</a-select-option>-->
<!--                </a-select>-->
<!--              </a-form-model-item>-->
<!--            </a-col>-->
            <a-col :span="24">
              <a-form-model-item label='连接Url' prop='url'>
                <a-input style='width: 100%' v-model='model.url' :allow-clear='true' autocomplete='off'
                         placeholder='请输入连接Url'>
                  <a-tooltip slot='suffix' title='格式如：http:///*************:8080'>
                    <a-icon type='info-circle' style='color: #0ABBF6' />
                  </a-tooltip>
                </a-input>
              </a-form-model-item>
            </a-col>

            <a-col :span="24">
              <a-form-model-item label='用户名' prop='user'>
                <a-input style='width: 100%' v-model='model.user' :allow-clear='true' autocomplete='off'
                  placeholder='请输入用户名' />
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item label='用户密码' prop='password'>
                <a-input-password style='width: 100%' v-model='model.password' :allow-clear='true' autocomplete='off'
                  placeholder='请输入用户密码' />
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item label='服务器地址' prop='serverHost'>
                <a-input style='width: 100%' v-model='model.serverHost' :allow-clear='true' autocomplete='off'
                         placeholder='请输入服务器地址' >
                  <a-tooltip slot='suffix' title='一个或多个ip,多个ip用,隔开 如：*************,*************'>
                    <a-icon type='info-circle' style='color: #0ABBF6' />
                  </a-tooltip>
                </a-input>
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item label='版本' prop='version'>
                <a-input style='width: 100%' v-model='model.version' :allow-clear='true' autocomplete='off'
                         placeholder='请输入版本' />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row type="flex" justify="end">
            <a-col :span="4">
              <a-button type="primary" @click="OnlineTest">测试连通性</a-button>
            </a-col>
          </a-row>
        </a-form-model>
      </j-form-container>
    </a-spin>
  </j-modal>
</template>

<script>
import {
  getAction,
  httpAction
} from '@api/manage'
  import YqUpload from '@comp/jeecg/yqUpload.vue'

  export default {
    name: 'migrateModal',
    components: {
      YqUpload
    },
    data() {
      return {
        title: '',
        width: '50%',
        visible: false,
        disableSubmit: false,
        confirmLoading: false,
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 3
          }
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 21
          }
        },
        model: {
          name: '',
          version: '',
          // type: 'miniO',
          user: '',
          password: '',
          url: '',
          serverHost: '',
        },
        validatorRules: {
          name: [
            {
              required: true,
              message: '请输入名称！'
            },
            {
              min: 0,
              max: 50,
              message: '名称长度应在1-50之间！'
            },
          ],
          type: [{
            required: false,
            message: '请选择类型！'
          }],
          url: [
            {
            required: true,
            message: '请输入连接Url！'
          },
            {
              pattern: /^(http(s?)(:)\/\/((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)|localhost)(:\d{1,5})?)$/,
              message: '请输入正确的连接Url！'
            }
          ],  user: [{
            required: true,
            message: '请输入用户名！'
          },{
            min: 0,
            max: 30,
            message: '用户名长度应在1-30之间！'
          }],
          password: [{
            required: true,
            message: '请输入用户密码！'
          },{
            min: 0,
            max: 50,
            message: '密码长度应在1-50之间！'
          }],
          serverHost: [{
            required: false,
            message: '请输入服务器地址！'
          },{
            pattern: /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?))(,(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?))*$/,
            message: '请输入正确的服务器地址！'
          }],
          version:[
            {
              min: 0,
              max: 30,
              message: '版本长度应在1-30之间！'
            }
          ]
        },
        url: {
          add: '/distributedStorage/transfer/datasource',
          edit: '/distributedStorage/transfer/datasource'
        }
      }
    },

    methods: {
      statusChange(value) {},
      OnlineTest(e) {
        this.$refs.form.validate((valid)=>{
          if(valid){
            getAction("/distributedStorage/transfer/isOnline",this.model).then((res)=>{
              if(res.success){
                this.$message.success(res.message)
              }else{
                this.$message.error(res.message)
              }
            })
          }
        })

      },
      add() {
        this.edit(this.model)
      },
      edit(record) {
        this.visible = true
        this.$nextTick(() => {
          this.model = Object.assign(this.model, record)
          this.$forceUpdate()
        })
      },
      close() {
        this.model = {
          name: '',
          version: '',
          // type: 'miniO',
          user: '',
          password: '',
          url: '',
          serverHost: '',
          },
        this.loading = false
        this.visible = false
      },
      handleOk() {
        const that = this
        that.$refs.form.validate((valid, value) => {
          if (valid) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!that.model.id) {
              httpurl += that.url.add
              method = 'post'
            } else {
              httpurl += that.url.edit
              method = 'put'
            }
            let formData = {
              ...that.model
            }
            httpAction(httpurl, formData, method)
              .then((res) => {
                if (res.success) {
                  that.$message.success(res.message)
                  that.$emit('ok')
                  that.close()
                } else {
                  that.$message.warning("操作失败")
                }
                that.confirmLoading = false
              }).catch((res) => {
                that.$message.warning("操作失败")
                that.confirmLoading = false
              })
          }
        })
      },
      submitCallback() {
        this.$emit('ok')
        this.visible = false
      },
      handleCancel() {
        this.close()
      }
    }
  }
</script>
<style scoped lang='less'>
  @import '~@assets/less/normalModal.less';
</style>