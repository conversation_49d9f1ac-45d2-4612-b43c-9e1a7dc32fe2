<template>
  <a-card  style="height: 100%;">
    <div style="text-align: right;margin-bottom: 12px;" >
      <img src="~@/assets/return1.png" alt="" @click="getGo" style="width: 20px;height: 20px;cursor: pointer">
    </div>
    <j-form-container disabled>
        <a-row>
          <a-col :span="24">
            <a-form-item class="two-words" label="名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-model="name" ></a-input>
            </a-form-item>
          </a-col>
          <div v-if="type === 'email' || type === 'dingding_robot'">
            <a-col :span="24" v-for="item in extendList" :key="item.id">
              <a-form-item :label="item.name" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input v-if="type === 'email'" v-model="item.value" placeholder="请输入" />
                <a-input v-if="type === 'dingding_robot'" v-model="item.value" placeholder="请输入" />
              </a-form-item>
            </a-col>
          </div>
<!--          <dev v-if="mold === 'email'">-->
<!--            <a-col :span="24" v-for="item in emailList" :key="item.id">-->
<!--              <a-form-item :label="item.name" :labelCol="labelCol" :wrapperCol="wrapperCol">-->
<!--                <a-input v-model="item.value" placeholder="请输入参数">-->
<!--                  {{ item.value }}-->
<!--                </a-input>-->
<!--              </a-form-item>-->
<!--            </a-col>-->
<!--          </dev>-->

<!--          <dev v-if="mold === 'dingding_robot'">-->
<!--            <a-col :span="24" v-for="item in dingdingList" :key="item.id">-->
<!--              <a-form-item :label="item.name" :labelCol="labelCol" :wrapperCol="wrapperCol">-->
<!--                <a-input v-model="item.value" placeholder="请输入参数">-->
<!--                  {{ item.value }}-->
<!--                </a-input>-->
<!--              </a-form-item>-->
<!--            </a-col>-->
<!--          </dev>-->
        </a-row>
    </j-form-container>
  </a-card>
</template>

<script>
  import { validateDuplicateValue } from '@/utils/util'
  import { httpAction, getAction } from '@/api/manage'
  export default {
    name: 'SysNoticeConfigDetail',
    components: {
    },
    props: {
      data:{
        type:Object
      }
    },
    data() {
      return {
        form: this.$form.createForm(this),
        model: {},
        addFlag: false,
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        url: {
          add: '/sys/notice/config/add',
          edit: '/sys/notice/config/edit',
          queryById: '/sys/notice/config/queryById',
          list: '/sysNoticeExtend/sysNoticeExtend/findUserExtend',
        },

        dataSource: [],
        couponList: [
          {
            id: 'email',
            name: '邮箱',
          },
          {
            id: 'dingding_robot',
            name: '钉钉机器人',
          },
          {
            id: 'weixin',
            name: '微信公众号',
          },
          {
            id: 'shengshitong',
            name: '盛事通',
          },
        ],
        emailList: [
          { name: '服务器地址', code: 'url', value: '' },
          { name: '端口号', code: 'port', value: '' },
          { name: '发件人', code: 'from', value: '' },
          { name: '用户名', code: 'username', value: '' },
          { name: '密码', code: 'password', value: '' },
        ],
        dingdingList: [
          { name: 'Webhook', code: 'webhook', value: '' },
          { name: '密钥', code: 'secret', value: '' },
        ],
        weixinList: [{ name: '收件人', code: 'from', value: '' }],
        type: '',
        name: '',
        extendList: [],
        code: '',
        value: '',
        extendList1: [],
        mold: '',
      }
    },
    computed: {
      formDisabled() {
        if (this.formBpm === true) {
          return this.formData.disabled !== false;
        }
        return this.disabled
      },
      showFlowSubmitButton() {
        if (this.formBpm === true) {
          if (this.formData.disabled === false) {
            return true
          }
        }
        return false
      },
    },
    created() {
      //如果是流程中表单，则需要加载流程表单data
      this.showFlowData()
    },
    mounted(){
      this.mold = this.data.type;
      this.name = this.data.name;
      this.queryById()
    },
    watch:{
      data:function(val,oldVal){
        this.mold = val.type;
        this.name = val.name;
        this.queryById()
      }
    },
    methods: {
      queryById() {
        getAction(this.url.queryById, { id: this.data.id }).then((res) => {
          if (res.success) {
            //this.result = res.result.records
            this.extendList = res.result.extend
            this.type = res.result.type
          }
        })
      },
      //返回上一级
      getGo(){
        this.$parent.pButton2(0);
      },
      addPro() {
        this.extendList1.push({
          name: this.name,
          value: this.value,
        })
      },
      //渲染流程表单数据
      showFlowData() {
        if (this.formBpm === true) {
          let params = { id: this.formData.dataId }
          getAction(this.url.queryById, params).then((res) => {
            if (res.success) {
              this.edit(res.result)
            }
          })
        }
      },
      popupCallback(row) {
        this.form.setFieldsValue(pick(row, 'name', 'type'))
      },
    },
  }
</script>
<style scoped>
  @import '~@assets/less/common.less';
  .form-row {
    display: flex;
    margin: 0px 0px !important;
    align-items: center;
    height: 60px;
    background-color: white;
  }
  .form-col {
    height: 34px;
  }
  .div-table-container {
    padding: 10px 5px 0 10px;
    background-color: white;
    margin-top: 10px;
    margin-right: -9px;
    height: calc(100% - 150px);
  }
  .gutter-example {
    background-color: #ececec;
    margin-bottom: 10px;
  }
  .gutter-row {
    padding-right: 0px !important;
  }
  .gutter-example >>> .ant-row > div {
    background: transparent;
    border: 0;
  }
  .gutter-box {
    background: white;
    padding: 5px 0;
  }
  .p-device-status {
    text-align: center;
    height: 30px;
    line-height: 30px;
    margin-bottom: 0px;
  }
  .span-title {
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
  }
  .span-num {
    font-family: PingFangSC-Medium;
    font-size: 24px;
  }
  .color-blue {
    color: #409eff;
  }
  .color-green {
    color: #139b33;
  }
  .color-red {
    color: #df1a1a;
  }
  .color-grey {
    color: #868686;
  }
  .table-page-search-wrapper {
    margin-right: -9px;
  }
  ::v-deep .two-words > div > label{
    letter-spacing:4px;
  }
  ::v-deep .two-words > div > label::after{
    letter-spacing:0px;
  }

</style>
