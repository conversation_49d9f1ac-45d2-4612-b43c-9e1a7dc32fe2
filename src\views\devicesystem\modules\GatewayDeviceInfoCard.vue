<template>
  <div class="container-div">
    <a-row
      class="cardContTwo"
      v-for="(childItem,childIndex) in childListData.records"
      :key="childIndex"
    >
      <a-col :span="24" class="cardContTwoChild oColor">
        <a-col :span="14" style="text-align: left;">
          <img src="~@assets/littleIcon.svg" alt />

          <a-tooltip placement="topLeft">
            <template slot="title">
              <span>{{ childItem.childDevice.name }}</span>
            </template>

            <span class="fontStyle">{{childItem.childDevice.name}}</span>
          </a-tooltip>
        </a-col>
        <a-col
          :span="10"
          style="position: absolute;right: 0;bottom: 23%;text-align: right;"
          class="warningStyle"
        >
          <span v-if="childItem.childDevice.status == 1">
            <span class="box"></span>
            <span>在线</span>
          </span>
          <span v-else-if="childItem.childDevice.status == 2">
            <span class="boxReds"></span>
            <span>告警</span>
          </span>
          <span v-else>
            <span class="boxRed"></span>
            <span>离线</span>
          </span>
          <a-divider type="vertical" />
          <a class="a-untie" @click="getUntie(parentId,childItem.childDevice.id)">解绑</a>
        </a-col>
      </a-col>
    </a-row>
    <div class="page-div">
      <a-pagination
        size="small"
        :total="ipagination.total"
        @change="onChange"
        :page-size="ipagination.pageSize"
        :default-current="ipagination.current"
      />
    </div>
  </div>
</template>
<script>
import { httpAction, postAction, getAction, deleteAction } from '@/api/manage'
export default {
  name: 'GatewayDeviceInfoCard',
  data() {
    return {
      childListData: [],
      dataSource: [],
      url: {
        unbund: '/gateway/deviceInfo/Unbundling', //解绑
        childUrl: '/gateway/deviceInfo/queryGatewayChildById'
      },
      ipagination: {
        current: 1,
        pageSize: 5,
        pageSizeOptions: ['5', '10', '15'],
        showTotal: (total, range) => {
          return range[0] + '-' + range[1] + ' 共' + total + '条'
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0
      }
    }
  },
  props: {
    parentId: {
      type: String,
      required: false
    },
    changed: {
      type: Boolean,
      required: false
    }
  },
  watch: {
    parentId: {
      immediate: true,
      handler() {
        this.getChildData()
      }
    },
    changed: {
      immediate: true,
      handler() {
        this.getChildData()
      }
    }
  },
  // mounted(){
  // },
  methods: {
    getChildData(arg) {
      let parent = ''
      if (!arg) {
        parent = this.parentId
      } else {
        parent = arg
      }
      let params = {
        gatewayIds: parent,
        pageNo: this.ipagination.current,
        pageSize: this.ipagination.pageSize
      }
      getAction(this.url.childUrl, params).then(res => {
        if (res.success) {
          this.childListData = res.result[parent]
          this.ipagination.total = this.childListData.total
          if (this.childListData && this.childListData.records.length < 1 && this.childListData.total > 0) {
            let pageNum = this.childListData.current - 1
            this.onChange(pageNum,this.childListData.size)
          }
        } else {
          this.dataSource = []
        }
      })
    },
    getUntie(parentId, childId) {
      let params = {
        gatewayId: parentId,
        ids: childId
      }
      let that = this
      that.$confirm({
        title: '确认操作',
        okText: '是',
        cancelText: '否',
        content: '是否确定解绑选中的设备?',
        onOk: function() {
          postAction(that.url.unbund, params).then(res => {
            if (res.success) {
              that.$message.success(res.message)
              that.getChildData()
            } else {
              that.$message.warning(res.message)
            }
          })
        }
      })
    },
    onChange(pageNumber, pageSize) {
      this.ipagination.pageSize = pageSize
      this.ipagination.current = pageNumber
      this.getChildData(this.parentId)
    }
  }
}
</script>
<style scoped>
.container-div {
  margin-top: 0.125rem /* 10/80 */;
}
.cardContTwoChild div img {
  width: 0.3125rem /* 25/80 */;
  height: 0.3125rem /* 25/80 */;
}
.oColor {
  font-family: PingFangSC-Regular;
  font-size: 0.175rem /* 14/80 */;
  color: rgba(0, 0, 0, 0.65);
  padding: 0.125rem /* 10/80 */ 0px;
  border-bottom: 1px solid #dddddd;
  position: relative;
}
.box {
  width: 8px;
  height: 8px;
  background: #13a40a;
  border-radius: 50%;
  display: inline-block;
  margin-right: 7px;
  margin-bottom: 1px;
}
.boxRed {
  width: 8px;
  height: 8px;
  background: rgb(139, 138, 138);
  border-radius: 50%;
  display: inline-block;
  margin-right: 7px;
  margin-bottom: 1px;
}
.boxReds {
  width: 8px;
  height: 8px;
  background: red;
  border-radius: 50%;
  display: inline-block;
  margin-right: 7px;
  margin-bottom: 1px;
}
.cardContThree {
  width: 100%;
  text-align: center;
  padding: 14px 0 14px 0;
  border-radius: 0 0 2px 2px;
  border-radius: 0px 0px 2px 2px;
}
.cardContThree .cardContThreeCol {
  border-right: 1px solid #dadada;
}
.fontStyle {
  padding-left: 5px;
  position: absolute;
  top: 20%;
  width: 95%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
@media (min-width: 1480px) and (max-width: 1550px) {
  .fontStyle {
    width: 82%;
  }
}
@media screen and (max-width: 1479px) {
  .warningStyle {
    width: 45% !important;
  }
  .fontStyle {
    width: 74%;
  }
}
.a-untie {
  font-family: PingFangSC-Regular;
  font-size: 0.175rem /* 14/80 */;
  color: #409eff;
  cursor: pointer;
}
.page-div {
  text-align: right;
  margin-top: 0.2rem /* 16/80 */;
}
</style>