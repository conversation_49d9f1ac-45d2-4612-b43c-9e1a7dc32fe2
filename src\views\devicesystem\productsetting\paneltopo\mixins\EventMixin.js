export default {
  data() {
    return {

    }
  },
  methods: {
    // 监听画布节点、边自定义事件
    setup() {
      // this.graph.on('blank:click', () => {
      // })
      // this.graph.on('cell:click', ({ x, y, cell }) => {
      // })

      //鼠标移除节点事件
      /*  this.graph.on('node:mouseleave', () => {
         document.getElementById('port').style.display = 'none'
       }) */
      if (this.operate === "create") {
        //监听节点添加
        this.graph.on('node:added', ({ node, index }) => {
          let { nodeType, portIndex } = node.getData();
          if (nodeType === "panelNode") {
            node.toBack();
          }
          if (nodeType === "panelElement") {
            //监听端口节点添加未绑定 改变图标的颜色
            if (this.operate === "create" && portIndex === "") {
              node.setData({ iconColor: this.globalGridAttr.topoConfig.unboundColor })
            }
          }
        })
        //监听节点被选中时
        this.graph.on('cell:selected', ({ cell, options }) => {
          // 款选取消面板节点选中
          if(options.batch && cell.shape === "panel-node"){
            this.graph.unselect(cell)
          }
          let cells = this.graph.getSelectedCells()
          this.selects = []
          this.$nextTick(() => {
            this.selects = cells
          })
        })
        // 监听节点取消被选中
        this.graph.on('cell:unselected', ({ cell }) => {
          this.selects = this.graph.getSelectedCells()
        })
        // 监听节点鼠标右键点击
        this.graph.on('cell:contextmenu', ({ cell }) => {
          if (cell.data && cell.data.typeCode === "RJ45" && cell.data.batchId === "") {
            this.templateNode = cell;
            let size = cell.size()
            this.$refs.extendForm.show(size.width)
          }
        })
        // 监听节点鼠标点击事件
        this.graph.on('cell:click', ({ cell }) => {
          // console.log("监听到了点击事件", cell)
          // if (cell.data && cell.data.typeCode === "RJ45" && cell.data.batchId === "") {
          //   this.templateNode = cell;
          //   let size = cell.size()
          //   this.$refs.extendForm.show(size.width)
          // }
        })
      }
      else if (this.operate === "show") {
        // 鼠标移入节点
        this.graph.on('node:mouseenter', ({ e, node, view }) => {
          if (node.data.portIndex) {
            let portInfo = this.panelPortInfo.find(el => el.index.value === node.data.portIndex)
            if (portInfo) {
              this.portInfo = portInfo;
              let size = node.size()
              let scale = this.graph.scale()
              this.tipWidth = size.width * scale.sx;
              this.tipHeight = size.height * scale.sy;
              let tp = this.graph.localToClient(node.position())
              this.tipLeft = tp.x ;
              this.tipTop = tp.y ;
             
            }

          }
        })
        this.graph.on('node:mouseleave', ({ e, node, view }) => {
          // console.log("从端口移除")
          // if (node.data.portIndex) {
          //   this.tipLeft = -10000;
          //   this.tipTop = -10000;
          //   this.tipWidth = 0;
          //   this.tipHeight = 0;

          // }
        })
      }

    },
    toolTipHide() {
      this.tipLeft = -10000;
      this.tipTop = -10000;
      this.tipWidth = 0;
      this.tipHeight = 0;
    }
  }
}