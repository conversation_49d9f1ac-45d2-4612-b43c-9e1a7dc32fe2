<template>
  <a-row :gutter='10' style='height: 100%' class='vScroll'>
    <a-col style='width: 100%; height: 100%; display: flex; flex-direction: column'>
      <!-- 查询区域 -->
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <div class='table-page-search-wrapper-style'>
          <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
            <a-row :gutter='24' ref='row'>
              <a-col :span='spanValue'>
                <a-form-item label='操作系统'>
                  <a-select :getPopupContainer='(node) => node.parentNode' allowClear v-model='osTypeValues'
                            mode='multiple'
                            :maxTagCount='1'
                            :maxTagTextLength="6"
                            placeholder='请选择操作系统' @change='setQueryParam($event,"osType")'>
                    <a-select-option v-for='i in osTypeList' :key='"osType_"+i.value' :value='i.value'>
                      {{ i.text }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label='cpu类型'>
                  <a-select :getPopupContainer='(node) => node.parentNode' allowClear v-model='cpuTypeValues'
                            mode='multiple'
                            :maxTagCount='1'
                            :maxTagTextLength="6"
                            placeholder='请选择cpu类型'
                            @change='setQueryParam($event,"cpuType")'>
                    <a-select-option v-for='i in cpuTypeList' :key='"cpuType_"+i.value' :value='i.value'>
                      {{ i.text }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label='cpu架构'>
                  <j-dict-select-tag v-model='queryParam.cpuArch' placeholder='请选择cpu架构' dictCode='cpuArch' />
                </a-form-item>
              </a-col>
              <a-col :span='spanValue' v-show='toggleSearchStatus'>
                <a-form-item label='激活状态'>
                  <j-dict-select-tag v-model='queryParam.enable' placeholder='请选择激活状态'
                                     dictCode='terminal_enable' />
                </a-form-item>
              </a-col>
              <a-col :span='spanValue' v-show='toggleSearchStatus'>
                <a-form-item label='终端名称'>
                  <a-input :maxLength='maxLength' placeholder='请输入名称' v-model='queryParam.name' :allowClear='true' autocomplete='off' />
                </a-form-item>
              </a-col>
              <a-col :span='spanValue' v-show='toggleSearchStatus'>
                <a-form-item label='终端类型'>
                  <a-select :getPopupContainer='(node) => node.parentNode' allowClear v-model='terminalValues'
                            mode='multiple'
                            :maxTagCount='1'
                            :maxTagTextLength="6"
                            placeholder='请选择终端类型' @change='setQueryParam($event,"terType")'>
                    <a-select-option v-for='i in terminalTypeList' :key='"terminalType_"+i.value' :value='i.value'>
                      {{ i.text }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span='spanValue' v-show='toggleSearchStatus'>
                <a-form-item class='two-words' label='行政区划'>
                  <yq-area-cascader-select
                    placeholder='请选择行政区划'
                    v-model='queryParam.addrId'>
                  </yq-area-cascader-select>
                </a-form-item>
              </a-col>
              <!-- <a-col v-show='toggleSearchStatus' :span='spanValue'>
                <a-form-item label='所属平台'>
                  <a-select v-model='queryParam.platformCode' :allowClear='true'
                    :getPopupContainer='(target) => target.parentNode' optionFilterProp='children' placeholder='请选择所属平台'
                    showSearch>
                    <a-select-option v-for='(item, index) in platformList' :key='index' :value='item.selfPlatformCode'>
                      {{ item.selfDepartName }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>-->
              <a-col :span='spanValue' v-show='toggleSearchStatus'>
                <a-form-item label='单位'>
                  <a-tree-select
                    :getPopupContainer='(node) => node.parentNode'
                    v-model='searchedDepKey'
                    tree-node-filter-prop='title'
                    :replaceFields='replaceFields'
                    :dropdownMatchSelectWidth="true"
                    :treeData='departsTreeData'
                    show-search
                    :searchValue='bSearchedDepKey'
                    multiple
                    :maxTagCount='1'
                    :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                    placeholder='请选择单位'
                    allow-clear
                    @change='onChangeDeparts'
                    @search='onSearchDeparts'
                    @select='onSelectDeparts'>
                  </a-tree-select>
                </a-form-item>
              </a-col>
              <a-col :span='spanValue' v-show='toggleSearchStatus'>
                <a-form-item label='网络MacIP'>
                  <a-input :maxLength='maxLength' placeholder='请输入mac或ip用逗号隔开' v-model='queryParam.macOrIp' :allowClear='true'
                           autocomplete='off' />
                </a-form-item>
              </a-col>
              <a-col :span='spanValue' v-show='toggleSearchStatus'>
                <a-form-item label='设备状态'>
                  <j-dict-select-tag v-model='queryParam.deviceStatus' placeholder='请选择设备状态'
                                     dictCode='device_status' />
                </a-form-item>
              </a-col>
              <a-col :span='spanValue' v-show='toggleSearchStatus'>
                <a-form-item label='使用人'>
                  <a-select v-model="queryParam.username" :getPopupContainer='node=>node.parentNode' :allow-clear='true'
                            placeholder="请选择使用人" show-search option-filter-prop="children"
                            :filter-option="filterOption">
                    <a-select-option v-for="(item, key) in userList" :key="key" :value="item.username">
                      <div :title="item.realname">{{ item.realname }}</div>
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <!--              <a-col :span='spanValue' v-show='toggleSearchStatus'>-->
              <!--                <a-form-item label='使用部门'>-->
              <!--                  <a-input :maxLength='maxLength' placeholder='请输入使用部门' v-model='queryParam.userDepartment' :allowClear='true'-->
              <!--                           autocomplete='off' />-->
              <!--                </a-form-item>-->
              <!--              </a-col>-->
              <a-col :span='spanValue' v-show='toggleSearchStatus'>
                <a-form-item label='管理人'>
                  <a-input :maxLength='maxLength' placeholder='请输入管理人' v-model='queryParam.administrator' :allowClear='true'
                           autocomplete='off' />
                </a-form-item>
              </a-col>
              <a-col :span='spanValue' v-show='toggleSearchStatus'>
                <a-form-item label='管理部门'>
                  <a-input :maxLength='maxLength' placeholder='请输入管理部门' v-model='queryParam.adminDepartment' :allowClear='true'
                           autocomplete='off' />
                </a-form-item>
              </a-col>
              <a-col :span='spanValue' v-show='toggleSearchStatus'>
                <a-form-item label='网络类型'>
                  <j-dict-select-tag v-model='queryParam.gatewayType' placeholder='请选择网络类型'
                                     dictCode='terminal_network' />
                </a-form-item>
              </a-col>
              <a-col :span='spanValue' v-show='toggleSearchStatus'>
                <a-form-item label='SN'>
                  <a-input :maxLength='maxLength' placeholder='请输入SN' v-model='queryParam.Sn' :allowClear='true' autocomplete='off' />
                </a-form-item>
              </a-col>
              <a-col :span='colBtnsSpan()'>
                <span class='table-page-search-submitButtons'
                      :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button type='primary' class='btn-search btn-search-style' @click='dosearch'>查询</a-button>
                  <a-button class='btn-reset btn-reset-style' @click='doreset'>重置</a-button>
                  <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <!-- 查询区域-END -->
      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <!-- 操作按钮区域 -->
        <div class='table-operator table-operator-style'>
          <a-button @click='handleAdd' v-has="'terminal:add'">新增</a-button>
          <a-dropdown v-if='selectedRowKeys.length > 0'>
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item key='1' @click='batchDel'>删除</a-menu-item>
            </a-menu>
            <a-button> 批量操作
              <a-icon type='down' />
            </a-button>
          </a-dropdown>
          <!--<a-button class="btn-enable" @click="batchEnabled(1)" v-if="batchEnable === 0">启用</a-button>-->
          <!--<a-button class="btn-enable" @click="batchEnabled(0)" v-if="batchEnable === 1">停用</a-button>-->
          <a-button @click='handleTemplateXls()' @mouseover='mouseOver' v-has="'terminal:downloadTemplate'">下载模版
          </a-button>
          <a-upload name='file' :showUploadList='false' :multiple='false' :headers='tokenHeader'
                    :action='importExcelUrl' @change='handleImportExcel'>
            <a-button v-has="'terminal:importExcel'">导入</a-button>
          </a-upload>
          <a-button @click="handleExport('终端信息表')" v-has="'terminal:exportXls'">导出</a-button>
          <a-popover title='终端状态说明' placement='right'>
            <template slot='content'>
              <div style='display:flex;'>
                <div>
                  <span>在线：</span>
                  <img src='../../../assets/bigScreen/28.png' alt='' class='stateImg' />
                </div>
                <div class='stateBox'>
                  <span>离线：</span>
                  <img src='../../../assets/bigScreen/57.png' alt='' class='stateImg' />
                </div>
                <div class='stateBox'>
                  <span>告警：</span>
                  <img src='../../../assets/bigScreen/56.png' alt='' class='stateImg' />
                </div>
              </div>
            </template>
            <a-icon type='question-circle' theme='twoTone' style='font-size:20px;margin-left:10px;' />
          </a-popover>
        </div>
        <!-- table区域-begin -->
        <a-table
          ref='table'
          class="terminal-table"
          bordered
          style="min-width: 100%"
          :rowKey='(record,index)=>record.id'
          :columns='columns'
          :dataSource='dataSource'
          :scroll='dataSource.length>0?{x:"max-content"}:{}'
          :pagination='ipagination'
          :loading='loading'
          :rowSelection='{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }'
          @change='handleTableChange'
        >
          <div slot='filterDropdown'>
            <a-card>
              <div style='text-align: right;font-size: small;color: #AAAAAC'>拖拽可进行排序</div>
              <a-divider style='margin: 5px 0px 5px 0px'></a-divider>
              <a-checkbox-group
                v-model='settingColumns'
                @change='onColSettingsChange'
                :key="refreshKey">
                <a-row style='width: 170px;max-height:350px;overflow-y: auto'>
                  <a-col v-for='(item, idx) in defColumns'
                         v-if="item.key!=='rowIndex'&&item.dataIndex !== 'action'"
                         :span='24' :key='"defColumns_"+idx' @drop='drop($event,idx)' @dragover='allowDrop($event)'>
                    <div draggable='true' @dragstart='dragStart($event,idx)' @dragend='dragEnd($event,idx)'
                         :key="'defColumns_draggable_'+idx">
                      <a-checkbox :disabled='item.disabled' :value='item.dataIndex' :key='"defColumns_checkbox_"+idx'>
                        {{ item.title }}
                      </a-checkbox>
                    </div>
                  </a-col>
                </a-row>
              </a-checkbox-group>
            </a-card>
          </div>
          <a-icon slot='filterIcon' type='setting' :style="{ fontSize: '16px', color: '#108ee9' }"
                  title='动态列表显示' />
          <template slot='htmlSlot' slot-scope='text'>
            <div v-html='text'></div>
          </template>
          <template slot='imgSlot' slot-scope='text'>
            <span v-if='!text' style='font-size: 14px'>无图片</span>
            <img v-else :src='getImgView(text)' height='25px' alt='' style='max-width: 80px; font-size: 14px' />
          </template>
          <template slot='status' slot-scope='text'>
            <span v-if='text == 1'>已激活</span>
            <span v-else>未激活</span>
          </template>
          <span class='caozuo' slot='action' slot-scope='text, record'>
            <a @click='handleDetailPage(record)'>查看</a>
            <span
              v-if="$yqHasPermission('terminal:edit')||$yqHasPermission('terminal:delete')||($yqHasPermission('terminal:shut-down')&&record.deviceStatus==1)||($yqHasPermission('terminal:reboot')&&record.deviceStatus==1)">
              <a-divider type="vertical" />
              <a-dropdown>
                <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
                <a-menu slot="overlay">
                  <a-menu-item v-has="'terminal:edit'">
                    <a @click='handleEdit(record)'>编辑</a>
                  </a-menu-item>
                  <a-menu-item v-has="'terminal:delete'">
                    <a @click='deleteRecord(record)'>删除</a>
                  </a-menu-item>
                  <a-menu-item v-if="$yqHasPermission('terminal:shut-down')&&record.deviceStatus==1">
                    <a @click='shutdownOrRebootFun(record.uniqueCode,"shutdown")'>关机</a>
                  </a-menu-item>
                  <a-menu-item v-if="$yqHasPermission('terminal:reboot')&&record.deviceStatus==1">
                    <a @click='shutdownOrRebootFun(record.uniqueCode,"reboot")'>重启</a>
                  </a-menu-item>
                </a-menu>
              </a-dropdown>
            </span>
          </span>
          <template slot='equipment' slot-scope='text,record'>
            <img v-if="record.deviceStatus == 1" src='../../../assets/bigScreen/28.png' alt=''
                 style='width:16px; height:16px;margin-right: 4px' />
            <img v-else src='../../../assets/bigScreen/57.png' alt=''
                 style='width:16px; height:16px;margin-right: 4px' />
            <img v-if="record.alarmStatus==1" src='../../../assets/bigScreen/56.png' alt=''
                 style='width:16px; height:16px;margin-right: 4px' />
            <span>{{ record.name }}</span>
          </template>
          <template slot='tooltip' slot-scope='text'>
            <a-tooltip placement='topLeft' :title='text' trigger='hover'>
              <div class='tooltip'>
                {{ text }}
              </div>
            </a-tooltip>
          </template>
          <template slot='number' slot-scope='text,record,index'>
            <div>{{ calcNumber(index) }}</div>
          </template>
          <template slot='cpuRate' slot-scope='text,record,index'>
            <div v-if="text">
              <div class="rate-box">
                <span class="end-box">{{text}}%</span>
              </div>
              <a-progress :stroke-color="{'0%': '#8E76EA','100%': '#73A0FA'}" :percent="text"></a-progress>
            </div>
            <span v-else="text">--</span>
          </template>
          <template slot='memUtilizRate' slot-scope='text,record,index'>
            <div v-if="text">
              <div class="rate-box">
                <span class="start-box" :title="(record.memUsedStr?record.memUsedStr:'--')+'/'+(record.memTotalStr?record.memTotalStr:'--')"
                      v-if="record.memUsedStr||record.memTotalStr">{{record.memUsedStr? record.memUsedStr:"--"}}/{{record.memTotalStr?record.memTotalStr:"--"}}</span>
                <span class="end-box">{{record.memUtilizRateStr}}%</span>
              </div>
              <a-progress :stroke-color="{'0%': '#76EABC','100%': '#73A0FA'}" :percent="text"></a-progress>
            </div>
            <span v-else="text">--</span>
          </template>
          <template slot='diskRate' slot-scope='text,record,index'>
            <div v-if="text">
              <div class="rate-box">
                <span class="start-box" :title="(record.diskUsedStr?record.diskUsedStr:'--')+'/'+(record.diskTotalStr?record.diskTotalStr:'--')" v-if="record.diskUsedStr||record.diskTotalStr">{{record.diskUsedStr?record.diskUsedStr:'--'}}/{{record.diskTotalStr?record.diskTotalStr:'--'}}</span>
                <span class="end-box">{{record.diskRateStr}}%</span>
              </div>
              <a-progress :stroke-color="{'0%': '#76D0EA','100%': '#73A0FA'}" :percent="text"></a-progress>
            </div>
            <span v-else="text">--</span>
          </template>
        </a-table>
      </a-card>
      <!-- 表单区域 -->
      <!--<jeecgDemo-modal ref="modalForm" @ok="modalFormOk"></jeecgDemo-modal>-->
      <terminal-device-modal ref='modalForm' @ok='modalFormOk'></terminal-device-modal>
      <!-- 下载模版 -->
      <iframe id='download' style='display: none' v-if='isDown == 1'></iframe>
    </a-col>
  </a-row>
</template>

<script>
import { getUserList } from '@/api/api'
import Vue from 'vue'
import '@/assets/less/TableExpand.less'
import { mixinDevice} from '@/utils/mixin'
import {JeecgListMixin} from '@/mixins/JeecgListMixin'
import TerminalDeviceModal from './modules/TerminalDeviceModal'
import {getAction,deleteAction} from '@/api/manage'
import {ajaxGetDictItems} from '@/api/api'
//引入公共devicetree组件
import TerminalDeptTree from '@/components/tree/TerminalDeptTree.vue'
import YqAreaCascaderSelect from '@/components/areaDict/YqAreaCascaderSelect'
import {YqFormSearchLocation} from '@/mixins/YqFormSearchLocation'
import {addCancelDebounce} from '@/utils/util'

export default {
  name: 'TerminalList',
  mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
  components: {
    TerminalDeviceModal,
    TerminalDeptTree,
    YqAreaCascaderSelect
  },
  data() {
    return {
      maxLength:50,
      description: '终端信息页面',
      formItemLayout: {
        labelCol: {
          style: 'width:90px'
        },
        wrapperCol: {
          style: 'width:calc(100% - 90px)'
        }
      },
      //单位tree
      searchedDepKey: undefined,
      bSearchedDepKey: '',
      departsTreeData: [],
      replaceFields: {
        children: 'children',
        title: 'deptName',
        key: 'deptId',
        value: 'deptId'
      },
      userList: [],
      osTypeList: [],
      // platformList:[],
      osTypeValues: undefined,
      cpuTypeList: [],
      cpuTypeValues: undefined,
      terminalTypeList: [],
      terminalValues: undefined,
      batchEnable: 1,
      disableMixinCreated:true,
      exportFields:'',
      //表头
      columns: [],
      //列设置
      settingColumns: [],
      defColumns: [
        {
          title: '序号',
          dataIndex: '',
          fixed: 'left',
          disabled: false,
          key: 'rowIndex',
          width: 60,
          scopedSlots: {
            customRender: 'number'
          }
        },
        {
          title: '终端名称',
          dataIndex: 'deviceStatus_dictText',
          checked: true,
          disabled: false,
          key: '1',
          fixed: 'left',
          scopedSlots: {
            customRender: 'equipment'
          },
          customCell: () => {
            let cellStyle = 'text-align: left'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '操作系统',
          dataIndex: 'osType_dictText',
          disabled: false,
          checked: false,
          key: '2'
        },
        {
          title: 'cpu类型',
          dataIndex: 'cpuType_dictText',
          disabled: false,
          checked: false,
          key: '3'
        },
        {
          title: '单位',
          dataIndex: 'deptId_dictText',
          checked: false,
          disabled: false,
          key: '4',
          customCell: () => {
            let cellStyle = 'text-align: left;max-width:300px'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'tooltip'
          }
        },
        {
          title: '使用人',
          dataIndex: 'usernameText',
          disabled: false,
          checked: true,
          key: '5'
        },
        {
          title: '管理人',
          dataIndex: 'administrator',
          disabled: false,
          checked: false,
          key: '6'
        },
        {
          title: '管理部门',
          dataIndex: 'adminDepartment',
          disabled: false,
          checked: false,
          key: '7',
          customCell: () => {
            let cellStyle = 'text-align: left;max-width:300px'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'tooltip'
          }
        },
        {
          title: '联系电话',
          dataIndex: 'phone',
          disabled: false,
          checked: true,
          key: '8'
        },
        {
          title: '终端类型',
          dataIndex: 'terminalType_dictText',
          disabled: false,
          checked: false,
          key: '9'
        },
        {
          title: '激活状态',
          dataIndex: 'enable_dictText',
          disabled: false,
          checked: false,
          key: '10'
        },
        {
          title: '网络类型',
          dataIndex: 'gatewayType_dictText',
          disabled: false,
          checked: false,
          key: '11'
        },
        {
          title: 'SN',
          dataIndex: 'sn',
          disabled: false,
          checked: false,
          key: '12'
        },
        {
          title: 'IP',
          dataIndex: 'ip',
          disabled: false,
          checked: false,
          key: '13'
        },
        {
          title: 'MAC地址',
          dataIndex: 'macAddr',
          disabled: false,
          checked: false,
          key: '14'
        },
        /* {
           title: 'cpu架构',
           dataIndex: 'cpuArch',
           disabled:false,
           checked:true,
           key: '15'
         },*/
        {
          title: 'cpu信息',
          dataIndex: 'cpuNameStr',
          disabled: false,
          checked: true,
          key: '16'
        },
        {
          title: 'cpu利用率',
          dataIndex: 'cpuRateStr',
          disabled: false,
          checked: true,
          key: '17',
          customCell: () => {
            let cellStyle = 'text-align: center; min-width: 180px'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'cpuRate'
          }
        },
        /*{
          title: '内存总容量',
          dataIndex: 'memTotalStr',
          disabled: false,
          checked: true,
          key: '18'
        },
        {
          title: '内存空闲容量',
          dataIndex: 'memFreeStr',
          disabled: false,
          checked: false,
          key: '19'
        },*/
        {
          title: '内存利用率',
          dataIndex: 'memUtilizRateStr',
          disabled: false,
          checked: true,
          key: '20',
          customCell: () => {
            let cellStyle = 'text-align: center; min-width: 250px'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'memUtilizRate'
          }
        },
        {
          title: '系统信息',
          dataIndex: 'sysVersion',
          disabled: false,
          checked: true,
          key: '21'
        },
        {
          title: '磁盘利用率',
          dataIndex: 'diskRateStr',
          disabled: false,
          checked: true,
          key: '22',
          customCell: () => {
            let cellStyle = 'text-align: center; min-width: 250px'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'diskRate'
          }
        },
        /*{
          title: '磁盘空闲容量',
          dataIndex: 'diskFreeStr',
          disabled: false,
          checked: false,
          key: '23'
        },
        {
          title: '磁盘总容量',
          dataIndex: 'diskTotalStr',
          disabled: false,
          checked: false,
          key: '24'
        },*/
        {
          title: '开机时长',
          dataIndex: 'sysUptimeStr',
          disabled: false,
          checked: true,
          key: '25'
        },
        {
          title: '操作',
          dataIndex: 'action',
          disabled: false,
          key: 'operation',
          fixed: 'right',
          width: 160,
          scopedSlots: {
            filterDropdown: 'filterDropdown',
            filterIcon: 'filterIcon',
            customRender: 'action'
          }
        }
      ],
      //刷新列表操作设置
      refreshKey: 0,
      dragItemIndex: 0,
      dragDom: null,
      isDown: 1,
      debounceCalWidth:null,
      url: {
        list: '/terminal/terminalDevice/list',
        delete: '/terminal/terminalDevice/delete',
        edit: '/terminal/terminalDevice/edit',
        deleteBatch: '/terminal/terminalDevice/deleteBatch',
        shutdownOrReboot: '/terminal/terminalDevice/sendCommand',
        importExcelUrl: '/terminal/terminalDevice/importExcel',
        downloadTemplateXlsUrl: '/terminal/terminalDevice/downloadTemplate',
        exportXlsUrl: '/terminal/terminalDevice/exportXls'
        // getPlatform: '/dataReport/manage/list',
      }
    }
  },
  created() {
    // this.loadAllMomgDept()
    this.getuserList()
    this.initColumns()
    this.initDictConfig()
    this.loadDepartsData()
    // this.getPlatform()
  },
  watch: {
    '$store.getters.deviceStatus': {
      // immediate:true,
      deep: true,
      handler(e) {
        // console.log("终端信息 *** 监听到了在线离线状态 === ", e)
        let isReload = false
        for (let i = 0; i < e.length; i++) {
          let status = e[i].status === 'up' ? 1 : 0
          for (let j = 0; j < this.dataSource.length; j++) {
            let data = this.dataSource[j]
            if (e[i].deviceCode === data.uniqueCode && status != data.deviceStatus) {
              isReload = true
              break
            }
          }
          if (isReload) {
            this.loadData()
            break
          }
        }
      }
    }
  },
  computed: {
    importExcelUrl: function() {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
    downloadTemplateXlsUrl: function() {
      return `${window._CONFIG['domianURL']}/${this.url.downloadTemplateXlsUrl}`
    }
  },
  mounted() {
    this.debounceCalWidth=addCancelDebounce(this.setColumnsWidth.bind(this),500)
    window.addEventListener("resize", this.debounceCalWidth)
  },
  beforeDestroy() {
    if (this.debounceCalWidth) {
      window.removeEventListener("resize", this.debounceCalWidth);
      this.debounceCalWidth.cancel(); // 取消计时器
    }
  },
  activated() {
    this.loadData()
  },
  methods: {
    handleExport(fileName) {
      this.exportFields = this.columns.filter((item) => item.key !== 'rowIndex' && item.dataIndex !== 'action').map(item => {
        return item.dataIndex
      })
      this.setExportField('deviceStatus_dictText',['deviceStatus','alarmStatus','name'])
      this.setExportField('osType_dictText',['osType'])
      this.setExportField('cpuType_dictText',['cpuType'])
      this.setExportField('deptId_dictText',['deptId'])
      this.setExportField('terminalType_dictText',['terminalType'])
      this.setExportField('enable_dictText',['enable'])
      this.setExportField('gatewayType_dictText',['gatewayType'])

      this.queryParam.exportFields =this.exportFields.join(',')
      this.handleExportXls(fileName)
    },
    setExportField(oldField,arrNewField,deleCount=1){
      let indx =this.exportFields.indexOf(oldField)
      if (indx > -1) {
        this.exportFields.splice(indx, deleCount,...arrNewField)
      }
    },
    // getPlatform() {
    //   let params = {
    //     levelType: '0',
    //     pageNo: 1,
    //     pageSize: -1
    //   }
    //   getAction(this.url.getPlatform, params).then((res) => {
    //     if (res.success) {
    //       this.platformList = res.result.records
    //     }
    //   })
    //  },
    initDictConfig() {
      this.getDictData('resources_type', 'terminalTypeList')
      this.getDictData('cpuType', 'cpuTypeList')
      this.getDictData('os_type', 'osTypeList')
    },
    getDictData(dictCode, list) {
      ajaxGetDictItems(dictCode, null).then((res) => {
        if (res.success) {
          this[list] = res.result
        }
      })
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      )
    },
    getuserList() {
      let param = {
        pageSize: 10000
      }
      getUserList(param).then((res) => {
        if (res.success) {
          this.userList = res.result.records
        }
      })
    },
    loadDepartsData() {
      getAction('/sys/sysDepart/queryAllTree').then((res) => {
        for (let i = 0; i < res.length; i++) {
          let temp = res[i]
          this.departsTreeData.push(temp)
        }
      })
    },
    onChangeDeparts(value) {
      // this.queryParam.deptId = value.join(",")
    },
    onSearchDeparts(e) {
      this.bSearchedDepKey = e
    },
    onSelectDeparts() {
    },
    setQueryParam(e, field) {
      this.queryParam[field] = e.join(',')
    },

    //excel模板
    handleTemplateXls() {
      if (this.isDown == 1) {
        const path = this.downloadTemplateXlsUrl
        document.getElementById('download').src = path
      }
    },

    //设备禁用/启用
    deviceEnable(record) {
      let enable = record.enable
      getAction('/terminal/terminalDevice/updateEnable', {
        enable: enable === 1 ? 0 : 1,
        terminalId: record.id
      }).then(
        (res) => {
          if (res.success) {
            if (enable === 1) {
              this.$message.success('禁用成功!')
            } else {
              this.$message.success('启用成功!')
            }
            this.loadData()
          }
        }
      )
    },
    //批量禁用、启用
    batchEnabled(enable) {
      if (this.selectedRowKeys.length <= 0) {
        this.$message.warning('请选择一条记录！')
        return
      } else {
        var ids = ''
        for (var a = 0; a < this.selectedRowKeys.length; a++) {
          ids += this.selectedRowKeys[a] + ','
        }
        var that = this
        this.$confirm({
          title: '确认操作',
          okText: '是',
          cancelText: '否',
          content: '是否确定修改选中数据?',
          onOk: function() {
            that.loading = true
            getAction('/terminal/terminalDevice/batchUpdateEnable', {
              ids: ids,
              enable: enable
            })
              .then((res) => {
                if (res.success) {
                  that.$message.success(res.message)
                  that.loadData()
                  that.onClearSelected()
                } else {
                  that.$message.warning(res.message)
                }
              })
              .finally(() => {
                that.loading = false
              })
          }
        })
      }
    },
    //勾选时进行状态判断
    onSelectChange(selectedRowKeys, selectionRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectionRows = selectionRows
      let disableFlag = this.selectionRows.some((ele) => ele.enable === 0)
      let ableFlag = this.selectionRows.some((ele) => ele.enable === 1)
      if (disableFlag && ableFlag) {
        this.batchEnable = 2
      } else if (disableFlag) {
        this.batchEnable = 0
      } else {
        this.batchEnable = 1
      }
    },
    handleDetailPage: function(record) {
      this.isDown = 2
      this.$parent.pButton2(1, record)
    },
    handleAdd: function() {
      this.$refs.modalForm.add()
      this.$refs.modalForm.title = '新增'
    },
    //删除
    deleteRecord(record) {
      if (!this.url.delete) {
        this.$message.error('请设置url.delete属性!')
        return
      }
      var that = this
      this.$confirm({
        title: '确认删除',
        okText: '是',
        cancelText: '否',
        content: '是否删除选中数据?',
        onOk: function() {
          that.loading = true
          deleteAction(that.url.deleteBatch, {
            ids: record.id
          }).then((res) => {
            if (res.success) {
              //重新计算分页问题
              that.reCalculatePage(1)
              that.$message.success(res.message)
              that.loadData()
            } else {
              that.$message.warning(res.message)
            }
          })
        }
      })
    },
    /**
     * 终端：关机、重启
     * @param {String} uniqueCode - 某个终端的标识（唯一）
     * @param {String} command - 针对终端的操作命令（取值：shutdown、reboot）
     * */
    shutdownOrRebootFun(uniqueCode, command) {
      getAction(this.url.shutdownOrReboot, { uniqueCode: uniqueCode, command: command }).then((res) => {
        if (res.success) {
          this.$message.success(res.message)
          // this.loadData()
        } else {
          this.$message.warning(res.message)
        }
      }).catch((err) => {
        this.$message.warning(err.message)
      })
    },
    //查询
    dosearch() {
      if (!this.searchedDepKey && this.bSearchedDepKey) {
        this.searchedDepKey = this.bSearchedDepKey
      }
      if (Array.isArray(this.searchedDepKey)) {
        this.queryParam.deptId = this.searchedDepKey.join(',')
      } else if (typeof this.searchedDepKey === 'string') {
        this.queryParam.deptId = this.searchedDepKey
        // this.searchedDepKey = ""
      }
      this.bSearchedDepKey = ''
      this.loadData(1)
    },
    //重置
    doreset() {
      this.queryParam = {}
      this.searchedDepKey = undefined
      this.bSearchedDepKey = ''
      this.osTypeValues = undefined
      this.cpuTypeValues = undefined
      this.terminalValues = undefined
      this.loadData(1)
    },
    mouseOver() {
      this.isDown = 1
    },
    calcNumber(index) {
      return (parseInt(this.ipagination.current) - 1) * parseInt(this.ipagination.pageSize) + (parseInt(index) + 1)
    },
    loadData(arg) {
      if (!this.url.list) {
        this.$message.error('请设置url.list属性!')
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1
      }

      var params = this.getQueryParams() //查询条件
      this.loading = true
      getAction(this.url.list, params).then((res) => {
        if (res.success && res.result) {
          this.dataSource = res.result.records || res.result
          if (this.dataSource.length < 9) {
            this.clientHeight = false
          }
          if (this.dataSource&&this.dataSource.length>0){
            this.dataSource.map(item=>{
              item.cpuRateStr= item.cpuRateStr?item.cpuRateStr.slice(0,item.cpuRateStr.length-1)*1:''
              item.memUtilizRateStr= item.memUtilizRateStr?item.memUtilizRateStr.slice(0,item.memUtilizRateStr.length-1)*1:''
              item.diskRateStr= item.diskRateStr?item.diskRateStr.slice(0,item.diskRateStr.length-1)*1:''
            })
          }
          this.ipagination.total = res.result.total ? res.result.total : 0
          this.debounceCalWidth()
        }
        if (res.code === 510) {
          this.$message.warning(res.message)
        }
        this.loading = false
      })
    },
    initColumns() {
      //获取默认列表的缓存dataIndex数据集合
      var key1 = this.$route.name + ':allCols'
      let allCols = Vue.ls.get(key1)
      // 提取 defColumns 的第一个和最后一个元素
      const firstItem = JSON.parse(JSON.stringify(this.defColumns[0]))
      const lastItem = JSON.parse(JSON.stringify(this.defColumns[this.defColumns.length - 1]))

      if (allCols) {
        let indexArr = []
        // 根据缓存顺序重新排序 defColumns
        const sortedColumns = allCols
          .map(dataIndex => this.defColumns.find(item => item.dataIndex === dataIndex))
          .filter(item => item) // 过滤掉 undefined（未找到的项）
        indexArr = sortedColumns.map(item => item.dataIndex)
        // 将第一个元素插入到排序后的数组开头
        sortedColumns.unshift(firstItem)

        // 过滤出未在缓存中出现的列
        const remainingColumns = this.defColumns.filter(
          item => !allCols.includes(item.dataIndex) && item.key !== firstItem.key && item.dataIndex !== lastItem.dataIndex
        )
        if (remainingColumns.length > 0) {
          let indexRemainIndexArr = remainingColumns.map(item => item.dataIndex)
          indexArr.push(...indexRemainIndexArr)
        }
        Vue.ls.set(key1, indexArr)
        // 将剩余列和最后一个元素插入到排序后的数组末尾
        if (remainingColumns.length > 0) {
          sortedColumns.push(...remainingColumns)
        }
        sortedColumns.push(lastItem)

        // 更新 defColumns
        this.defColumns = sortedColumns
      } else {
        //缓存数据集合为空时，按照data中定义的defColumns列表顺序写缓存
        let tempDataIndex = []
        this.defColumns.forEach(item => {
          if (item.dataIndex !== 'action' && item.key !== 'rowIndex') {
            tempDataIndex.push(item.dataIndex)
          }
        })
        Vue.ls.set(key1, tempDataIndex)
      }

      //从缓存中获取列表中显示的dataIndex
      var key2 = this.$route.name + ':colsettings'
      let colSettings = Vue.ls.get(key2)
      //已选显示列表缓存dataIndex数据集合为空
      if (!colSettings || colSettings.length == 0) {
        let allSettingColumns = []
        let columns = []
        this.defColumns.forEach(function(item, i, array) {
          delete item.fixed
          //item.disabled=false
          if (item.checked) {
            allSettingColumns.push(item.dataIndex)
            columns.push(item)
          }
        })
        this.settingColumns = allSettingColumns
        Vue.ls.set(key2, allSettingColumns)
        this.columns = columns
        this.columns.unshift(firstItem)
        this.columns.push(lastItem)
      }
      //已选列表缓存dataIndex数据集合不为空，设置table要显示的列
      else {
        this.settingColumns = this.defColumns.filter(
          item => colSettings.includes(item.dataIndex) && item.key !== firstItem.key && item.dataIndex !== lastItem.dataIndex).map(item => item.dataIndex)
        Vue.ls.set(key2, this.settingColumns)

        this.columns = this.defColumns.filter((item) => {
          delete item.fixed
          if (item.dataIndex === 'action' || item.key === 'rowIndex') {
            return true
          }
          if (colSettings.includes(item.dataIndex)) {
            item.disabled = colSettings.length <= 1//只有一个字段时，不可点击取消
            item.checked = true
            return true
          } else {
            item.checked = false
            return false
          }
        })
      }
      this.columns[0]['fixed'] = 'left'
      this.columns[1]['fixed'] = 'left'
      this.columns[this.columns.length - 1]['fixed'] = 'right'
    },
    //列设置更改事件
    onColSettingsChange(checkedValues) {
      let tempColumns = []
      this.defColumns.forEach((item) => {
        delete item.fixed
        if (item.key === 'rowIndex' || item.dataIndex === 'action') {
          tempColumns.push(item)
        }
        if (checkedValues.includes(item.dataIndex)) {
          item.disabled = checkedValues.length <= 1
          item.checked = true
          tempColumns.push(item)
        } else {
          if (item.checked) {
            item.checked = false
          }
          if (item.disabled) {
            item.disabled = false
          }
        }
      })
      tempColumns[0]['fixed'] = 'left'
      tempColumns[1]['fixed'] = 'left'
      //this.columns[2]['fixed'] = 'left'
      tempColumns[tempColumns.length - 1]['fixed'] = 'right'
      this.columns.splice(0, this.columns.length, ...tempColumns)
      //将已选并在列表显示的字段dataIndex写入缓存中
      var key = this.$route.name + ':colsettings'
      // Vue.ls.set(key, this.settingColumns, 7 * 24 * 60 * 60 * 1000)
      Vue.ls.set(key, this.settingColumns)
      this.setColumnsWidth()
    },
    dragStart(event, index) {
      this.dragItemIndex = index
      this.dragDom = event.currentTarget.cloneNode(true)
    },
    allowDrop(event) {
      event.preventDefault()
    },
    drop(event, index) {
      event.preventDefault()
      //默认列表重新进行排序
      if (index !== this.dragItemIndex) {
        let temp = this.defColumns[this.dragItemIndex]
        this.defColumns.splice(this.dragItemIndex, 1)
        this.defColumns.splice(index, 0, temp)
      }
    },
    dragEnd(event, index) {
      //event.preventDefault()
      //table列显示顺序同步拖拽后的顺序
      let tempSettingDataIndex = []
      let tempAllDataIndex = []
      let cols = this.defColumns.filter(item => {
        delete item.fixed
        if (item.key === 'rowIndex' || item.dataIndex === 'action') {
          return true
        } else {
          tempAllDataIndex.push(item.dataIndex)
        }
        if (this.settingColumns.includes(item.dataIndex)) {
          tempSettingDataIndex.push(item.dataIndex)
          return true
        }
        return false
      })
      this.settingColumns.splice(0, this.settingColumns.length, ...tempSettingDataIndex) //按照排序重新保存选中显示列表
      cols[0]['fixed'] = 'left'
      cols[1]['fixed'] = 'left'
      //cols[2]['fixed'] = 'left'
      cols[cols.length - 1]['fixed'] = 'right'
      this.columns.splice(0, this.columns.length, ...cols)
      var key1 = this.$route.name + ':colsettings' //浏览器缓存重新记录排序后的选中列表
      //Vue.ls.set(key1, tempColumns, 7 * 24 * 60 * 60 * 1000)//保留7天缓存
      Vue.ls.set(key1, this.settingColumns)

      var key2 = this.$route.name + ':allCols' //浏览器缓存重新记录排序后的所有列表
      Vue.ls.set(key2, tempAllDataIndex)
      this.dragDom.remove()
      ++this.refreshKey
      this.setColumnsWidth()
    },
    /*获取滚动区列宽，用于设置对应固定列的宽*/
    setColumnsWidth() {
      this.$nextTick(() => {
        if (this.columns && this.columns.length > 0) {
          let table = document.querySelector('.terminal-table')
          if(table) {
            let scrollThs = table.querySelector('.ant-table-scroll .ant-table-thead tr').querySelectorAll('th')
            let leftFixedThs = table.querySelector('.ant-table-fixed-left .ant-table-thead tr').querySelectorAll('th')
            for (let i = 0; i < leftFixedThs.length; i++) {
              leftFixedThs[i].setAttribute('data-index', i); // 添加 data-index 属性
              leftFixedThs[i].style.setProperty(`--height-${i}`, scrollThs[i].offsetHeight + 'px');
              leftFixedThs[i].style.setProperty(`--width-${i}`, scrollThs[i].offsetWidth + 'px');
            }
          }
        }
      })
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
/* 为每个 th 元素设置独立的样式 */
::v-deep .ant-table-fixed-left .ant-table-thead tr th[data-index="0"] {
  height: var(--height-0) !important;
  width: var(--width-0) !important;
}

::v-deep .ant-table-fixed-left .ant-table-thead tr th[data-index="1"] {
  height: var(--height-1) !important;
  width: var(--width-1) !important;
}
::v-deep .ant-table-fixed-left .ant-table-thead tr th[data-index="2"] {
  height: var(--height-2) !important;
  width: var(--width-2) !important;
}

/*操作--设置按钮背景色*/
::v-deep .ant-table-filter-icon {
  background-color: #e5e5e5;
}

.stateBox {
  margin-left: 20px;
}

.stateImg {
  vertical-align: middle
}

/*::v-deep .ant-progress-show-info .ant-progress-outer{
  margin-right:0 !important;
  padding:0 !important;
  width: calc(100% - 55px);

  .ant-progress-inner {
    background-color: #E8E8E8;
    .ant-progress-bg {
      height: 10px !important;
    }
  }
}

::v-deep .ant-progress-text{
  width:55px !important;
  font-size: 14px !important;
}*/

::v-deep .ant-progress-show-info .ant-progress-outer{
  margin-right:0 !important;
  padding:0 !important;
  width:100%;

  .ant-progress-inner {
    display: block !important;
    width: 100% !important;
    background-color: #E8E8E8;
    .ant-progress-bg {
      height: 10px !important;
    }
  }
}

::v-deep .ant-progress-text {
  display: none;
}

.rate-box{
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-flow: row nowrap;
  font-size: 14px;

  .start-box{
    flex:3;
    text-align:start;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .end-box{
    flex:1;
    text-align:end
  }
}
</style>
