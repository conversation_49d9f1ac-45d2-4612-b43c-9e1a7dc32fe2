<template>
  <a-row :gutter="10" style="height: 100%;" class="vScroll">
    <a-col style="width:100%;height: 100%;display: flex;flex-direction: column">
      <!-- 查询区域 -->
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0', marginRight: '12px' }" class="card-style"
        style="width: 100%">
        <div v-if='orderList.length>0' class="orderBox">
          <div v-for="item in orderList" class="order_box">
            <div class="order_name">{{ item.name }}</div>
            <div class="order_bottom">
              <span class="order_value">{{ item.value }}</span>
              <span class="order_unit">{{ item.unit }}</span>
            </div>
          </div>
        </div>
        <a-empty v-else description="暂无数据"></a-empty>
      </a-card>
      <a-card :bordered="false" style="width: 100%; flex: auto">
        <!-- 操作按钮区域 -->
        <div class="table-operator table-operator-style">
          <a-button @click="downloadFileByURL('/data-analysis/order/exportXlsDept','工单单位统计报表.xlsx')">导出</a-button>
        </div>
        <a-table ref="table" bordered rowKey="departId" :columns="columns" :dataSource="dataSource" :pagination="ipagination"
          :loading="loading" @change="handleTableChange">
          <span slot="percent" slot-scope="text">{{ text + '%' }}</span>
        </a-table>
      </a-card>
    </a-col>
  </a-row>
</template>

<script>
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import {
    getAction
  } from '@/api/manage'
  import {
    YqFormSeniorSearchLocation
  } from '@/mixins/YqFormSeniorSearchLocation'
  import {
    YqFormSearchLocation
  } from '@/mixins/YqFormSearchLocation'
  export default {
    name: 'departOrderStatis',
    mixins: [JeecgListMixin, YqFormSearchLocation, YqFormSeniorSearchLocation],
    data() {
      return {
        orderList: [],
        // 表头
        columns: [{
            title: '单位名称',
            dataIndex: 'departId_dictText',
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 130px;max-width:300px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '共产生工单',
            dataIndex: 'allCount',
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 150px;max-width:350px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '已处理工单',
            dataIndex: 'completedCount',
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 150px;max-width:350px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '平均处理时间(h)',
            dataIndex: 'averageHandleTime',
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 150px;max-width:350px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '及时分配率',
            dataIndex: 'allocTimelyRate',
            scopedSlots: {
              customRender: 'percent'
            },
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 150px;max-width:350px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '及时处理率',
            dataIndex: 'handTimelyRate',
            scopedSlots: {
              customRender: 'percent'
            },
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 110px;max-width:350px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '解决率',
            dataIndex: 'resolutionRate',
            scopedSlots: {
              customRender: 'percent'
            },
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 110px;max-width:350px'
              return {
                style: cellStyle
              }
            }
          },
        ],
        url: {
          list: '/data-analysis/order/getListDeptStatistics',
          exportXlsUrl: '/data-analysis/order/exportXlsDept',
          Statistics: '/serviceProvider/serviceProviderStatistics',

        },
      }
    },
    created() {
      this.getAllDeptStatistics()
    },
    mounted() {},
    methods: {
      //获取统计信息
      getAllDeptStatistics() {
        getAction("/data-analysis/order/getAllDeptStatistics").then((res) => {
          if(res.success && res.result){
            this.orderList = res.result
          }
        })
      },
    }
  }
</script>
<style lang='less' scoped>
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';

  .orderBox {
    width: 100%;
    display: flex;
    justify-content: space-around;

    .order_box {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      .order_name {
        font-size: 18px;
        color: rgba(0, 0, 0, 0.65);
        font-weight: 500;
      }

      .order_bottom {
        margin-bottom: 10px;

        .order_value {
          font-size: 40px;
          color: #409EFF;
          font-weight: 700;
        }

        .order_unit {
          font-size: 14px;
          color: rgba(0, 0, 0, 0.65);
          font-weight: 500;
          margin-left: 5px;
        }
      }
    }
  }
</style>