<template xmlns:background-color="http://www.w3.org/1999/xhtml">
  <a-row :gutter="10" style="height: 100%">
    <a-col :span="12" style='height: 100%'>
      <a-card :bordered="false">
        <!-- 按钮操作区域 -->
        <a-row style="margin-left: 14px" class="table-operator">
          <a-col>
            <a-button @click="handleAdd(1)">新增</a-button>
            <a-button @click="handleAdd(2)">新增下级</a-button>
            <a-button title="删除多条数据" @click="batchDel">批量删除</a-button>
            <!--<a-button @click="refresh" type="default" icon="reload" :loading="loading">刷新</a-button>-->
          </a-col>
        </a-row>
        <div style="background: #fff; padding-left: 16px; height: 100%; margin-top: 5px">
          <a-input-search :max-length='maxLength' @search="onSearch" style="width: 100%; margin-top: 10px" placeholder="请输入部门名称" />
          <!-- 树-->
          <a-col>
            <template>
              <a-dropdown style='width: 100%' :trigger="[this.dropTrigger]" @visibleChange="dropStatus">
                <div style="user-select: none">
                  <a-tree style='width: 100%; margin-top: 10px; height:calc(100vh - 312px);overflow: auto'
                    :getPopupContainer='(node) => node.parentNode' checkable multiple @select="onSelect"
                    @check="onCheck" @rightClick="rightHandle" :selectedKeys="selectedKeys" :checkedKeys="checkedKeys"
                    :treeData="departTree" :checkStrictly="checkStrictly" :expandedKeys="iExpandedKeys"
                    :autoExpandParent="autoExpandParent" @expand="onExpand" />
                </div>
                <!--新增右键点击事件,和增加添加和删除功能-->
                <a-menu slot="overlay">
                  <a-menu-item @click="handleAdd(3)" key="1">添加</a-menu-item>
                  <a-menu-item @click="handleDelete" key="2">删除</a-menu-item>
                  <a-menu-item @click="closeDrop" key="3">取消</a-menu-item>
                </a-menu>
              </a-dropdown>
            </template>
          </a-col>
        </div>
      </a-card>
      <!---- author:os_chengtgen -- date:20190827 --  for:切换父子勾选模式 =======------>
      <div class="drawer-bootom-button">
        <a-dropdown :trigger="['click']" placement="topCenter">
          <a-menu slot="overlay">
            <a-menu-item key="1" @click="switchCheckStrictly(1)">父子关联</a-menu-item>
            <a-menu-item key="2" @click="switchCheckStrictly(2)">取消关联</a-menu-item>
            <a-menu-item key="3" @click="checkALL">全部勾选</a-menu-item>
            <a-menu-item key="4" @click="cancelCheckALL">取消全选</a-menu-item>
            <a-menu-item key="5" @click="expandAll">展开所有</a-menu-item>
            <a-menu-item key="6" @click="closeAll">合并所有</a-menu-item>
          </a-menu>
          <a-button> 树操作
            <a-icon type="up" />
          </a-button>
        </a-dropdown>
      </div>
      <!---- author:os_chengtgen -- date:20190827 --  for:切换父子勾选模式 =======------>
    </a-col>
    <a-col :span="12" style='height: 100%'>
      <a-tabs :animated="false" defaultActiveKey="1" style='height: 100%;overflow: hidden;overflow-y: auto'>
        <a-tab-pane tab="基本信息" key="1">
          <a-card :bordered="false" v-if="selectedKeys.length > 0">
            <a-form :form="form">
              <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="上级单位">
                <a-tree-select :getPopupContainer='(node) => node.parentNode' style="width: 100%"
                  :dropdownStyle="{ maxHeight: '200px', overflow: 'auto' }" :treeData="treeData" :disabled="disable"
                  v-model="model.parentId" placeholder="无">
                </a-tree-select>
              </a-form-item>
              <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="节点类型">
                <a-select placeholder="请选择节点类型" :allowClear='true' @change="typeChange"
                  v-decorator="['nodeType', { initialValue: '0', rules: [{ required: true, message: '请选择节点类型' }]}]">
                  <a-select-option v-for="item in typeList" :value="item.value">{{ item.title }}</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item class="two-words" label="行政区划" :labelCol="labelCol" :wrapperCol="wrapperCol"
                v-if="model.nodeType == '1'">
                <yq-area-cascader-select ref="YqAreaCascaderSelect" placeholder="请选择行政区划" v-decorator="['cityId', validatorRules.cityId]"
                  @change="cityChange">
                </yq-area-cascader-select>
              </a-form-item>
              <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="单位名称">
                <a-input placeholder="请输入单位/部门名称" v-decorator="['departName', validatorRules.departName]"
                  :allowClear='true' autocomplete='off' />
              </a-form-item>
              <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="单位别名">
                <a-input placeholder="请输入单位别名" v-decorator="['code', validatorRules.code]" :allowClear='true'
                         autocomplete='off' />
              </a-form-item>
              <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="单位标识">
                <a-input placeholder="请输入单位/部门标识" v-decorator="['departNameEn', validatorRules.departNameEn]"
                  :allowClear='true' autocomplete='off' />
              </a-form-item>
<!--              <a-form-item v-if="!model.parentId" :labelCol="labelCol" :wrapperCol="wrapperCol" label="所属平台标识">
                <a-input placeholder="请输入所属平台标识" v-decorator="['platformCode',validatorRules.platformCode]" :allowClear='true'
                         autocomplete='off' />
              </a-form-item>-->
              <a-form-item :required="true" :labelCol="labelCol" :wrapperCol="wrapperCol" label="单位编码">
                <a-input disabled placeholder="请输入单位编码" v-model='model.orgCode' :allowClear='true' autocomplete='off' />
              </a-form-item>

              <!--              <a-form-item :required="true" :labelCol="labelCol" :wrapperCol="wrapperCol" label='组织级别'>
                  <j-dict-select-tag v-model='model.orgLevel' placeholder='请选择组织级别'
                                     dictCode='orgLevel' />
                </a-form-item>-->
              <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="单位类型">
<!--                <template v-if="orgCategoryDisabled">
                  <a-radio-group v-decorator="['orgCategory', validatorRules.orgCategory]" placeholder="请选择单位类型">
                    <a-radio value="1"> 一级部门 </a-radio>
                  </a-radio-group>
                </template>-->
                <template>
                  <a-radio-group v-decorator="['orgCategory', validatorRules.orgCategory]" placeholder="请选择单位类型">
                    <a-radio v-for='item in deptTypes' :value="item.value">
                      {{item.title}}
                    </a-radio>
<!--                    <a-radio value="2"> 部门 </a-radio>
                    <a-radio value="3"> 保障部 </a-radio>
                    <a-radio value="5"> 委办局 </a-radio>
                    <a-radio value="4"> 运维供应商 </a-radio>
                    <a-radio value="6"> 服务商 </a-radio>-->
                  </a-radio-group>
                </template>
              </a-form-item>
              <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="排序">
                <a-input-number v-decorator="['departOrder', { initialValue: 0 }]" :min="1" />
              </a-form-item>
              <a-form-item class="two-words" label="行政区划" :labelCol="labelCol" :wrapperCol="wrapperCol"
                v-if="model.nodeType == '0'">
                <yq-area-cascader-select ref="YqAreaCascaderSelect" placeholder="请选择行政区划" v-decorator="['cityId', validatorRules.cityId]" @change="setCityId">
                </yq-area-cascader-select>
              </a-form-item>
              <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="地址">
                <a-input placeholder="请输入地址" v-decorator="['address', { initialValue: '' }]" :allowClear='true'
                  autocomplete='off' />
              </a-form-item>
              <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="联系人">
                <a-input placeholder="请输入联系人" v-decorator="['contacts', { initialValue: '' }]" :allowClear='true'
                  autocomplete='off' />
              </a-form-item>
              <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="联系人电话">
                <a-input placeholder="请输入手机号" v-decorator="['mobile',validatorRules.mobile, { initialValue: '' }]"
                  :allowClear='true' autocomplete='off' />
              </a-form-item>
              <a-form-item :label="longitude" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input
                  v-decorator="['longitude', { initialValue: '',rules:[{ pattern:/^[^a-zA-Z\u4e00-\u9fa5]*$/, message: '请输入正确的经度' }] }]"
                  :placeholder="'请输入'+longitude" :allowClear='true' autocomplete='off' />
              </a-form-item>
              <a-form-item :label="latitude" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input
                  v-decorator="['latitude', { initialValue: '',rules:[{ pattern:/^[^a-zA-Z\u4e00-\u9fa5]*$/, message: '请输入正确的纬度' }]  }]"
                  :placeholder="'请输入'+latitude" :allowClear='true' autocomplete='off' />
              </a-form-item>
              <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="计划数量">
                <a-input placeholder="请输入计划数量" v-decorator="['planNumber', { initialValue: '',rules:[{ pattern:/^\d*$/, message: '请输入数字' }]
                  }]" :allowClear='true' autocomplete='off' />
              </a-form-item>
<!--              <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="业务CODE">
                <a-input placeholder="请输入CODE" v-decorator="['code', { initialValue: '' }]" :allowClear='true'
                  autocomplete='off' />
              </a-form-item>-->
              <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="备注">
                <a-textarea placeholder="请输入备注" v-decorator="['memo', { initialValue: '' }]" :allowClear='true'
                  autocomplete='off' />
              </a-form-item>
            </a-form>
            <div class="anty-form-btn">
              <a-button @click="emptyCurrForm" type="default" htmlType="button" icon="sync">重置</a-button>
              <a-button @click="submitCurrForm" type="primary" htmlType="button" icon="form" :disabled="formCode">保存
              </a-button>
            </div>
          </a-card>
          <a-card v-else>
            <a-empty>
              <span slot="description"> 请先选择一个部门! </span>
            </a-empty>
          </a-card>
        </a-tab-pane>
        <!-- <a-tab-pane tab="部门权限" key="2" forceRender>
          <depart-auth-modal ref="departAuth" />
        </a-tab-pane> -->
      </a-tabs>
    </a-col>
    <depart-modal ref="departModal" @ok="loadTree"></depart-modal>
  </a-row>
</template>
<script>
  import {
    getAction
  } from '@/api/manage'
  import DepartModal from './modules/DepartModal'
  import pick from 'lodash.pick'
  import {
    queryDepartTreeList,
    ajaxGetDictItems,
    searchByKeywords,
    deleteByDepartId
  } from '@/api/api'
  import {
    httpAction,
    deleteAction
  } from '@/api/manage'
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import DepartAuthModal from './modules/DepartAuthModal'
  import JTreeSelect from '@/components/jeecg/JTreeSelect'
  import YqAreaCascaderSelect from '@/components/areaDict/YqAreaCascaderSelect'
  import JDictSelectTag from '@comp/dict/JDictSelectTag.vue'
  import {
    phoneValidator
  } from '@/mixins/phoneValidator'
  import {flatTreeData} from '@/utils/util'
  // 表头
  const columns = [
    {
      title: '节点类型',
      dataIndex: 'nodeType',
    }, {
      title: '单位名称',
      dataIndex: 'departName',
    },
    {
      title: '单位类型',
      align: 'center',
      dataIndex: 'orgType',
    },
    {
      title: '联系人',
      dataIndex: 'contacts',
    },
    {
      title: '手机号',
      dataIndex: 'mobile',
    },
    // {
    //   title: '传真',
    //   dataIndex: 'fax',
    // },
    {
      title: '地址',
      dataIndex: 'address',
    },
    {
      title: '排序',
      align: 'center',
      dataIndex: 'departOrder',
    },
    {
      title: '计划数量',
      dataIndex: 'planNumber',
    },
    {
      title: '经度',
      dataIndex: 'longitude',
    },
    {
      title: '纬度',
      dataIndex: 'latitude',
    },
    {
      title: '归属地',
      dataIndex: 'cityId',
    },
    {
      title: '操作',
      align: 'center',
      dataIndex: 'action',
      scopedSlots: {
        customRender: 'action'
      },
    },
  ]
  export default {
    name: 'DepartList',
    mixins: [JeecgListMixin, phoneValidator],
    components: {
      DepartAuthModal,
      DepartModal,
      JTreeSelect,
      YqAreaCascaderSelect,
      JDictSelectTag,
    },
    data() {
      return {
        maxLength:50,
        formCode: false,
        longitude: "经度",
        latitude: "纬度",
        iExpandedKeys: [],
        loading: false,
        autoExpandParent: true,
        currFlowId: '',
        currFlowName: '',
        disable: true,
        treeData: [],
        visible: false,
        departTree: [],
        rightClickSelectedKey: '',
        rightClickSelectedOrgCode: '',
        hiding: true,
        model: {},
        dropTrigger: '',
        depart: {},
        columns: columns,
        disableSubmit: false,
        checkedKeys: [],
        selectedKeys: [],
        autoIncr: 1,
        currSelected: {},
        typeList: [],
        allTreeKeys: [],
        checkStrictly: true,

        form: this.$form.createForm(this),
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          },
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          },
        },
        graphDatasource: {
          nodes: [],
          edges: [],
        },
        validatorRules: {
          departName: {
            rules: [{
              required: true,
              message: '请输入单位/部门名称!'
            },{
              pattern: /^(?!\s*$).+/,
              message: '单位/部门名称不能为空或仅包含空格'
            },{
              max: 300,
              message: '单位/部门名称长度不能超出300个字符'
            }]
          },
          code: {
            rules: [{
              required: true,
              message: '请输入单位/部门别名!'
            },{
              pattern: /^(?!\s*$).+/,
              message: '别名不能为空或仅包含空格'
            },{
              max: 50,
              message: '别名长度不能超出50个字符'
            }]
          },
          departNameEn: {
            rules: [{
              required: true,
              message: '请输入单位/部门标识!'
            },{
              pattern: /^[^\s]+$/,
              message: '单位标识不能包含空格'
            },{
              max: 150,
              message: '单位标识不能超出150个字符'
            }]
          },
         /* platformCode: {
            rules: [{
              required: true,
              message: '请输入平台所属标识!'
            }]
          },*/
          orgCategory: {
            rules: [{
              required: true,
              message: '请输入单位类型!'
            }]
          },
          mobile: {
            rules: [{
              validator: this.mobilePhone
            }]
          },
          cityId: {
            rules: [{
              required: true,
              message: '请输入行政区划!'
            }]
          },
          // orgLevel: {
          //   rules: [{
          //     required: true,
          //     message: '请选择组织级别!'
          //   }]
          // },
        },
        url: {
          delete: '/sys/sysDepart/delete',
          edit: '/sys/sysDepart/edit',
          deleteBatch: '/sys/sysDepart/deleteBatch',
          exportXlsUrl: 'sys/sysDepart/exportXls',
          importExcelUrl: 'sys/sysDepart/importExcel',
          // getPlatformCode: '/data/reportAndConverge/getPlatformCode',
        },
        orgCategoryDisabled: false,
        deptTypes: [],
      }
    },
    computed: {
      importExcelUrl: function () {
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
      },
    },
    created() {
      this.currFlowId = this.$route.params.id
      this.currFlowName = this.$route.params.name
      this.getDictData('depart_type_flag')
      this.getDeptTypes('DEPARTMENT_CATEGORIES')
      // this.loadTree()
    },
    mounted() {
      this.getLongitudeAndLatitudeInfo()
    },
    methods: {
      typeChange(e) {
        this.model.nodeType = e
        if (e == '1') {
          this.$nextTick(() => {
            this.form.setFieldsValue({
              departName: this.model.departName,
              departNameEn: this.model.departNameEn,
              // platformCode: this.model.platformCode,
              orgCategory: this.model.orgCategory,
              departOrder: this.model.departOrder,
              mobile: this.model.mobile,
              address: this.model.address,
              memo: this.model.memo,
              code: this.model.code,
              contacts: this.model.contacts,
              planNumber: this.model.planNumber,
              cityId: this.model.cityId,
              longitude: this.model.longitude,
              latitude: this.model.latitude,
            })
          })
        } else {
          this.$nextTick(() => {
            this.form.setFieldsValue({
              departName: '',
              departNameEn: this.model.departNameEn,
              // platformCode: this.model.platformCode,
              orgCategory: this.model.orgCategory,
              departOrder: this.model.departOrder,
              mobile: this.model.mobile,
              address: this.model.address,
              memo: this.model.memo,
              code: this.model.code,
              contacts: this.model.contacts,
              planNumber: this.model.planNumber,
              cityId: this.model.cityId,
              longitude: this.model.longitude,
              latitude: this.model.latitude,
            })
          })
        }
      },
      setCityId(value, name) {
        this.currSelected['cityId'] = value
        this.form.setFieldsValue({
          cityId: value
        })
      },
      cityChange(value, name) {
        if (name != null) {
          this.$nextTick(() => {
            this.currSelected['cityId'] = value
            this.form.setFieldsValue({
              departName: name,
              cityId: value
            })
          })
        }
      },
      getDictData(dictCode) {
        ajaxGetDictItems(dictCode, null).then((res) => {
          if (res.success) {
            this.typeList = res.result
          }
        })
      },
      getDeptTypes(dictCode) {
        ajaxGetDictItems(dictCode, null).then((res) => {
          if (res.success) {
            this.deptTypes = res.result
          }
        })
      },
      // getPlatformCode(id) {
      //   getAction(this.url.getPlatformCode).then((res) => {
      //     if (res.success) {
      //       if (res.result == id) {
      //         this.formCode = true
      //       }
      //     }
      //   })
      // },
      getLongitudeAndLatitudeInfo() {
        this.$nextTick(() => {
          //获取六盘水经度配置个性化定制内容
          this.CZ_Data = window._CONFIG['customization'];
          if (this.CZ_Data && this.CZ_Data.cz_liupanshui && this.CZ_Data.cz_liupanshui.departManage &&
            this.CZ_Data.cz_liupanshui.departManage.Longitude && this.CZ_Data.cz_liupanshui.departManage.latitude) {
            this.longitude = this.CZ_Data.cz_liupanshui.departManage.Longitude
            this.latitude = this.CZ_Data.cz_liupanshui.departManage.latitude
          } else {
            //默认设置
            this.longitude = "经度"
            this.latitude = '纬度'
          }
        })
      },
      loadData() {
        this.refresh()
      },
      loadTree() {
        var that = this
        that.treeData = []
        that.departTree = []
        queryDepartTreeList().then((res) => {
          if (res.success) {
            //部门全选后，再添加部门，选中数量增多
            this.allTreeKeys = []
            for (let i = 0; i < res.result.length; i++) {
              let temp = res.result[i]
              that.treeData.push(temp)
              that.departTree.push(temp)
              that.setThisExpandedKeys(temp)
              that.getAllKeys(temp)
            }
            this.loading = false
          }
        })
      },
      setThisExpandedKeys(node) {
        if (node.children && node.children.length > 0) {
          this.iExpandedKeys.push(node.key)
          for (let a = 0; a < node.children.length; a++) {
            this.setThisExpandedKeys(node.children[a])
          }
        }
      },
      refresh() {
        this.loading = true
        this.loadTree()
      },
      // 右键操作方法
      rightHandle(node) {
        this.dropTrigger = 'contextmenu'
        this.rightClickSelectedKey = node.node.eventKey
        this.rightClickSelectedOrgCode = node.node.dataRef.orgCode
      },
      onExpand(expandedKeys) {
        // if not set autoExpandParent to false, if children expanded, parent can not collapse.
        // or, you can remove all expanded children keys.
        this.iExpandedKeys = expandedKeys
        this.autoExpandParent = false
      },
      backFlowList() {
        this.$router.back(-1)
      },
      // 右键点击下拉框改变事件
      dropStatus(visible) {
        if (visible == false) {
          this.dropTrigger = ''
        }
      },
      // 右键店家下拉关闭下拉框
      closeDrop() {
        this.dropTrigger = ''
      },
      addRootNode() {
        this.$refs.nodeModal.add(this.currFlowId, '')
      },
      batchDel: function () {
        if (this.checkedKeys.length <= 0) {
          this.$message.warning('请选择一条记录！')
        } else {
          var ids = ''
          for (var a = 0; a < this.checkedKeys.length; a++) {
            ids += this.checkedKeys[a] + ','
          }
          var that = this
          this.$confirm({
            title: '确认删除',
            okText: '是',
            cancelText: '否',
            content: '是否删除所选中的 ' + this.checkedKeys.length + ' 条数据，以及子节点数据？',
            onOk: function () {
              deleteAction(that.url.deleteBatch, {
                ids: ids
              }).then((res) => {
                if (res.success) {
                  that.$message.success(res.message)
                  that.loadTree()
                  that.onClearSelected()
                } else {
                  that.$message.warning(res.message)
                }
              })
            },
          })
        }
      },
      onSearch(value) {
        let that = this
        if (value) {
          searchByKeywords({
            keyWord: value
          }).then((res) => {
            if (res.success) {
              that.departTree = []
              for (let i = 0; i < res.result.length; i++) {
                let temp = res.result[i]
                that.departTree.push(temp)
              }
            } else {
              that.$message.warning(res.message)
            }
          })
        } else {
          that.loadTree()
        }
      },
      nodeModalOk() {
        this.loadTree()
      },
      nodeModalClose() {},
      hide() {
        this.visible = false
      },
      onCheck(checkedKeys, info) {
        this.hiding = false
        //this.checkedKeys = checkedKeys.checked
        // <!---- author:os_chengtgen -- date:20190827 --  for:切换父子勾选模式 =======------>
        if (this.checkStrictly) {
          this.checkedKeys = checkedKeys.checked
        } else {
          this.checkedKeys = checkedKeys
        }
        // <!---- author:os_chengtgen -- date:20190827 --  for:切换父子勾选模式 =======------>
      },
      onSelect(selectedKeys, e) {
        let record = e.node.dataRef
        this.hiding = false
        this.model = this.currSelected = Object.assign({}, record)
        this.$nextTick(() => {
          this.selectedKeys = [record.key]
          this.model.parentId = record.parentId
          this.model.orgCode = record.orgCode
          this.model.nodeType = record.nodeType
          this.setValuesToForm(record)
        })
        // this.$refs.departAuth.show(record.id)
      },
      // 触发onSelect事件时,为部门树右侧的form表单赋值
      setValuesToForm(record) {
        if (record.orgCategory == '1') {
          this.orgCategoryDisabled = true
        } else {
          this.orgCategoryDisabled = false
        }
        this.$nextTick(() => {
          // this.form.getFieldDecorator('fax', {
          //   initialValue: ''
          // })
          this.form.setFieldsValue(
            pick(
              record,
              'nodeType',
              'departName',
              'departNameEn',
              // 'platformCode',
              'orgCategory',
              'departOrder',
              'mobile',
              // 'fax',
              'address',
              'contacts',
              'memo',
              'code',
              'planNumber',
              'cityId',
              'longitude',
              'latitude'
            )
          )
        })
      },
      getCurrSelectedTitle() {
        return !this.currSelected.title ? '' : this.currSelected.title
      },
      onClearSelected() {
        this.hiding = true
        this.checkedKeys = []
        this.currSelected = {}
        this.form.resetFields()
        this.selectedKeys = []
        // this.$refs.departAuth.departId = ''
      },
      handleNodeTypeChange(val) {
        this.currSelected.nodeType = val
      },
      notifyTriggerTypeChange(value) {
        this.currSelected.notifyTriggerType = value
      },
      receiptTriggerTypeChange(value) {
        this.currSelected.receiptTriggerType = value
      },
      submitCurrForm() {
        // <!-- start 为了解决左侧树默认的cityId 和右侧行政区划组件的cityId 匹配不上时,需要把当前表单的已有cityId清掉 -->
        const option = this.$refs.YqAreaCascaderSelect.getCurrentDictOptions()
        if (option && Array.isArray(option)) {
          //将区划数据树形结构数据扁平化
          let flatOptions = flatTreeData(option)
          const isMatch = flatOptions.some(item => item.id === this.currSelected.cityId);
          if (!isMatch) {
            this.currSelected.cityId = null
            this.form.setFieldsValue({
              cityId: null
            });
          }
        }
        // <!-- end -->

        this.form.validateFields((err, values) => {
          if (!err) {
            if (!this.currSelected.id) {
              this.$message.warning('请点击选择要修改部门!')
              return
            }

            let formData = Object.assign(this.currSelected, values)
            httpAction(this.url.edit, formData, 'put').then((res) => {
              if (res.success) {
                this.$message.success('保存成功!')
                this.loadTree()
              } else {
                this.$message.error(res.message)
              }
            })
          }
        })
      },
      emptyCurrForm() {
        this.form.resetFields()
      },
      nodeSettingFormSubmit() {
        this.form.validateFields((err, values) => {
          if (!err) {}
        })
      },
      openSelect() {
        this.$refs.sysDirectiveModal.show()
      },
      handleAdd(num) {
        if (num == 1) {
          this.$refs.departModal.add()
          this.$refs.departModal.title = '新增'
        } else if (num == 2) {
          let key = this.currSelected.key
          if (!key) {
            this.$message.warning('请先点击选中上级部门！')
            return false
          }
          this.$refs.departModal.add(this.selectedKeys)
          this.$refs.departModal.title = '新增'
        } else {
          this.$refs.departModal.add(this.rightClickSelectedKey)
          this.$refs.departModal.title = '新增'
        }
      },
      handleDelete() {
        var that = this
        this.$confirm({
          title: '确认删除',
          okText: '是',
          cancelText: '否',
          content: '是否删除此部门以及子节点数据?',
          onOk: function () {
            deleteByDepartId({
              id: that.rightClickSelectedKey
            }).then((resp) => {
              if (resp.success) {
                //删除成功后，去除已选中中的数据
                that.checkedKeys.splice(
                  that.checkedKeys.findIndex((key) => key === that.rightClickSelectedKey),
                  1
                )
                that.$message.success('删除成功!')
                that.loadTree()
                //删除后同步清空右侧基本信息内容
                let orgCode = that.form.getFieldValue('model.orgCode')
                if (orgCode && orgCode === that.rightClickSelectedOrgCode) {
                  that.onClearSelected()
                }
              } else {
                that.$message.warning('删除失败!')
              }
            })
          },
        })
      },
      selectDirectiveOk(record) {
        this.nodeSettingForm.setFieldsValue({
          directiveCode: record.directiveCode
        })
        this.currSelected.sysCode = record.sysCode
      },
      getFlowGraphData(node) {
        this.graphDatasource.nodes.push({
          id: node.id,
          text: node.flowNodeName,
        })
        if (node.children.length > 0) {
          for (let a = 0; a < node.children.length; a++) {
            let temp = node.children[a]
            this.graphDatasource.edges.push({
              source: node.id,
              target: temp.id,
            })
            this.getFlowGraphData(temp)
          }
        }
      },
      // <!---- author:os_chengtgen -- date:20190827 --  for:切换父子勾选模式 =======------>
      expandAll() {
        this.iExpandedKeys = this.allTreeKeys
      },
      closeAll() {
        this.iExpandedKeys = []
      },
      checkALL() {
        this.checkStriccheckStrictlytly = false
        this.checkedKeys = this.allTreeKeys
      },
      cancelCheckALL() {
        //this.checkedKeys = this.defaultCheckedKeys
        this.checkedKeys = []
      },
      switchCheckStrictly(v) {
        if (v == 1) {
          this.checkStrictly = false
        } else if (v == 2) {
          this.checkStrictly = true
        }
      },
      getAllKeys(node) {
        this.allTreeKeys.push(node.key)
        if (node.children && node.children.length > 0) {
          for (let a = 0; a < node.children.length; a++) {
            this.getAllKeys(node.children[a])
          }
        }
      },
      // <!---- author:os_chengtgen -- date:20190827 --  for:切换父子勾选模式 =======------>
    }
  }
</script>
<style lang="less" scoped>
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';

  .ant-card-body .table-operator {
    margin: 15px;
  }

  .anty-form-btn {
    width: 100%;
    text-align: center;
  }

  .anty-form-btn button {
    margin: 0 5px;
  }

  .anty-node-layout .ant-layout-header {
    padding-right: 0;
  }

  .header {
    padding: 0 8px;
  }

  .header button {
    margin: 0 3px;
  }

  //.ant-modal-cust-warp {
  //  height: 100%;
  //}
  //
  //.ant-modal-cust-warp .ant-modal-body {
  //  height: calc(100% - 110px) !important;
  //  overflow-y: auto;
  //}

  //.ant-modal-cust-warp .ant-modal-content {
  //  height: 90% !important;
  //  overflow-y: hidden;
  //}

  #app .desktop {
    height: auto !important;
  }

  /** Button按钮间距 */
  .ant-btn {
    margin-left: 3px;
  }

  .drawer-bootom-button {
    /*position: absolute;*/
    bottom: 0;
    width: 100%;
    border-top: 1px solid #e8e8e8;
    padding: 10px 16px;
    text-align: left;
    left: 0;
    background: #fff;
    border-radius: 0 0 2px 2px;
  }
</style>