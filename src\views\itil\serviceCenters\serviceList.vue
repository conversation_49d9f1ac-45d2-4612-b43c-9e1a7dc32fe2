<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll zxw">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <!-- 查询区域 -->
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }">
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="状态">
                  <j-dict-select-tag
                    v-model="queryParam.status"
                    :allowClear="true"
                    placeholder="请选择状态"
                    dictCode="service_type"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="日期">
                  <a-range-picker
                    v-model="rangePickerDateFC"
                    @change="rangeChange"
                    format="MM-DD"
                    class="a-range-picker-choice-date"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="colBtnsSpan()">
                <span
                  class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                >
                  <a-button type="primary" @click="dosearch">查询</a-button>
                  <a-button type="default" @click="searchReset" style="margin-left: 8px">重置</a-button>
                </span>
              </a-col>
            </a-row>

            <a-row style="padding: 0px 14px 15px 8px" class="table-operator">
              <a-col :span="6" class="tableBottom">
                <a-button style="padding: 0 22px; margin-top: 10px" @click="handleAdd" class="addBtn"
                  >新增报告</a-button
                >
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <!-- 查询区域-END -->

      <!-- table区域-begin -->
      <div class="div-table-container">
        <a-row :gutter="32">
          <a-col
            class="gutter-row"
            :xxl="6"
            :lg="8"
            :md="12"
            :sm="24"
            :xs="24"
            style="margin-bottom: 16px"
            v-for="(item, index) in dataSource"
            :key="index"
          >
            <div class="gutter-box cardCont">
              <div class="cardContOne">
                <div>
                  <p class="oPFir name_OPFir">
                    {{ item.name }}
                  </p>
                </div>
                <div style="position: absolute; right: 0px; bottom: 0">
                  <p
                    class="oPFir"
                    style="font-size: 14px; text-align: right; color: #4bd863"
                    v-if="item.status == 'end'"
                  >
                    已完成
                  </p>
                  <p class="oPFir" style="font-size: 14px; text-align: right; color: #0079fe" v-else>进行中</p>
                </div>
              </div>
              <a-row class="cardContTwo" style="height: 133px">
                <a-col :span="24" class="cardContTwoChild oColor" style="text-align: left">
                  <div class="cardLittleOne">时间范围：{{ item.startTime }}~{{ item.endTime }}</div>
                  <div class="cardLittleTwo">运维供应商：{{ item.operSupplierText }}</div>
                </a-col>
              </a-row>
              <a-row class="cardContThree">
                <a-col
                  :span="6"
                  class="cardContThreeCol"
                  @click="evaluateAdd(item)"
                  v-if="item.buttonPer == '1' && item.whButtonCheck == 0"
                >
                  评价
                </a-col>
                <a-col
                  :span="6"
                  class="cardContThreeCol"
                  @click="evaluateAlert(item)"
                  v-else-if="item.buttonPer == '1' && item.whButtonCheck == 1"
                >
                  评 价
                </a-col>
                <a-col
                  :span="6"
                  class="cardContThreeCol"
                  @click="handleFillReport(item)"
                  v-else-if="item.buttonPer == '0'"
                >
                  填 报
                </a-col>

                <a-col :span="6" class="cardContThreeCol" @click="handleDetailPage(item)"> 查 看 </a-col>
                <a-col :span="6" class="cardContThreeCol" @click="fontClick(item.fileUrl)"> 导 出 </a-col>
                <a-col :span="6" @click="deleteRecord(item)"> 删 除 </a-col>
              </a-row>
            </div>
          </a-col>
        </a-row>
        <div style="text-align: right; margin-top: 16px; padding-bottom: 20px" v-if="dataSource.length > 0">
          <a-pagination
            show-quick-jumper
            show-size-changer
            :default-current="ipagination.current"
            :total="ipagination.total"
            @change="onChange"
            :page-size="ipagination.pageSize"
            :pageSizeOptions="ipagination.pageSizeOptions"
            :show-total="(total) => `共 ${ipagination.total} 条`"
            @showSizeChange="onShowSizeChange"
            size="small"
          >
          </a-pagination>
        </div>
      </div>
    </a-col>
    <!--    <fill-report ref="" @ok=""></fill-report>-->
    <service-add ref="modalForm" @ok="modalFormOk"></service-add>
    <evaluate-add ref="evaluateForm" @ok="modalFormOk"></evaluate-add>
  </a-row>
</template>

<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { httpAction, getAction, deleteAction } from '@/api/manage'
// import fillReport from './modules/fillReport'
import serviceAdd from './modules/serviceAdd'
import evaluateAdd from './modules/evaluateAdd'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'

export default {
  name: 'serviceList',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components: {
    evaluateAdd,
    getAction,
    // fillReport,
    serviceAdd,
  },
  data() {
    return {
      ItemLayout: {
        labelCol: {
          style: 'width:70px',
        },
      },
      ipagination: {
        current: 1,
        pageSize: 8,
        pageSizeOptions: ['8', '16', '24'],
        showTotal: (total, range) => {
          return range[0] + '-' + range[1] + ' 共' + total + '条'
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0,
      },
      rangePickerDateFC: [],
      queryParam: {
        endTime: '',
        status: '',
        startTime: '',
      },
      // 表头
      columns: [
        {
          title: '标识',
          align: 'center',
          dataIndex: 'name',
        },
        {
          title: '状态',
          align: 'center',
          dataIndex: 'status',
        },
      ],
      url: {
        list: '/report/itilReportInfo/list',
        delete: '/report/itilReportInfo/delete',
      },
    }
  },
  // computed: {
  //   importExcelUrl: function() {
  //     return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
  //   }
  // },
  mounted() {
    this.loadData()
  },
  methods: {
    loadData(arg) {
      if (!this.url.list) {
        this.$message.error('请设置url.list属性!')
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1
      }
      var params = this.getQueryParams() //查询条件
      this.loading = true
      let that = this
      getAction(that.url.list, params).then((res) => {
        if (res.success) {
          that.dataSource = res.result.records || res.result
          that.ipagination.total = res.result.total
        }
        if (res.code === 510) {
          that.$message.warning(res.message)
        }
        that.loading = false
      })
    },
    searchReset() {
      this.queryParam = {}
      this.rangeFC()
      this.loadData(1)
    },
    // 默认值
    rangeFC() {
      this.rangePickerDateFC = []
    },
    fontClick(path) {
      if (path == null || path == '' || path == undefined) {
        let that = this
        that.$message.warning('还未生成报告！')
        return
      } else {
        window.open(window._CONFIG['downloadUrl'] + '/' + path)
      }
    },
    onShowSizeChange(current, pageSize) {
      this.ipagination.pageSize = pageSize
      this.ipagination.current = current
      this.loadData()
    },
    onChange(pageNumber, pageSize) {
      this.ipagination.pageSize = pageSize
      this.ipagination.current = pageNumber
      this.loadData()
    },
    //日期
    rangeChange(dates, dateStrings) {
      this.queryParam.startTime = dateStrings[0]
      this.queryParam.endTime = dateStrings[1]
    },
    //填报
    handleFillReport(record) {
      if (record.status == 'end') {
        let that = this
        that.$message.warning('已经完成考核，不可以继续填报！')
      } else {
        this.$parent.pButton2(1, record)
      }
    },
    //删除
    deleteRecord(record) {
      if (!this.url.delete) {
        this.$message.error('请设置url.delete属性!')
        return
      }
      var that = this
      this.$confirm({
        title: '确认删除',
        okText: '是',
        cancelText: '否',
        content: '是否删除选中数据?',
        onOk: function () {
          that.loading = true
          deleteAction(that.url.delete, { id: record.id }).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.loadData()
            } else {
              that.$message.warning(res.message)
            }
          })
        },
      })
    },
    handleDetailPage: function (record) {
      this.$parent.pButton2(2, record)
    },
    //查询
    dosearch() {
      this.loadData(1)
    },
    //重置
    doreset() {},
    //评价
    evaluateAdd(e) {
      this.$refs.evaluateForm.add(e)
      this.$refs.evaluateForm.title = '添加评论'
      this.$refs.evaluateForm.disableSubmit = false
    },
    //评价弹出
    evaluateAlert() {
      this.$message.warning('无操作权限！')
    },
  },
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
.form-row {
  margin: 0px 0px !important;
  height: 55px;
  background-color: white;
}
.form-col {
  text-align: left;
  height: 34px;
}
.div-table-container {
  background-color: #f0f2f5;
  margin-top: 16px;
  //padding: 6px 4px 0 4px;
  overflow: hidden;
  overflow-y: auto;
}
.gutter-example {
  background-color: #ececec;
  margin-bottom: 10px;
}
.gutter-row {
  padding-right: 0px !important;
}

.posi-col {
  position: relative;
}
.sync-img {
  position: absolute;
  top: 8px;
  right: 19px;
  cursor: pointer;
}
.gutter-example /deep/ .ant-row > div {
  background: #f0f2f5;
  border: 0;
}
.gutter-box {
  background: white;
  padding: 21px 0;
}
.p-device-status {
  text-align: center;
  height: 30px;
  line-height: 30px;
  margin-bottom: 0px;
}
.span-title {
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
}
.span-num {
  font-family: PingFangSC-Medium;
  font-size: 24px;
}
.color-blue {
  color: #409eff;
}
.color-green {
  color: #139b33;
}
.color-red {
  color: #df1a1a;
}
.color-grey {
  color: #868686;
}
.table-page-search-wrapper {
  background-color: #fff;
}

.table-operator {
  margin-bottom: 10px;
}
.cardAdd,
.cardCont {
  width: calc(100% / 4 - 12px);
  position: relative;
  background: #ffffff;
  -webkit-box-shadow: 0 3px 7px -1px rgba(0, 0, 0, 0.16);
  box-shadow: 0px 2px 4px 2px rgba(0, 0, 0, 0.16);
  border-radius: 2px;
  height: 100%;
  cursor: pointer;
}

.cardDiv {
  text-align: center;
  position: absolute;
  top: 35%;
  left: 29%;
  right: 31%;
  bottom: 74px;
}
.cardDiv img {
  width: 61px;
  height: 61px;
  margin-bottom: 17px;
}
.cardDiv div {
  font-family: PingFangSC-Regular;
  font-size: 16px;
  color: #d7d7d7;
}
.gutter-example /deep/ .ant-row > div {
  background: transparent;
  border: 0;
}
.gutter-box {
  width: 100%;
}
.cardCont {
  position: relative;
  padding: 26px 0px 0 0px;
}
.cardContOne {
  overflow: hidden;
  position: relative;
  margin: 0 26px;
}
.cardContOne div {
  float: left;
}
.cardContOne div img {
  width: 50px;
  height: 60px;
  margin-right: 15px;
}

.cardContOne div p {
  margin-bottom: 8px;
}
.oPFir {
  font-family: PingFangSC-Regular;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.85);
}
.name_OPFir {
  width: 300px;
  max-width: 85%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.oPTwo {
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
}
.box {
  width: 8px;
  height: 8px;
  background: #389e0d;
  border-radius: 50%;
  display: inline-block;
  margin-right: 7px;
  margin-bottom: 1px;
}
.boxReds {
  width: 8px;
  height: 8px;
  background: red;
  border-radius: 50%;
  display: inline-block;
  margin-right: 7px;
  margin-bottom: 1px;
}
.boxRed {
  width: 8px;
  height: 8px;
  background: red;
  border-radius: 50%;
  display: inline-block;
  margin-right: 7px;
  margin-bottom: 1px;
}
.cardContTwo {
  margin: 0 26px;
  height: 100%;
}
.cardContTwoChild {
  text-align: center;
  height: 100%;
  position: relative;
}
.cardLittleOne {
  position: absolute;
  top: 40px;
  color: rgba(0, 0, 0, 0.65);
  font-family: PingFangSC-Regular;
  font-size: 14px;
}
.cardLittleTwo {
  position: absolute;
  bottom: 36px;
  color: rgba(0, 0, 0, 0.65);
  font-family: PingFangSC-Regular;
  font-size: 14px;
}
.oColor {
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
  padding: 10px 0px;
  position: relative;
}
.tColor {
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
}
.cardContThree {
  width: 100%;
  text-align: center;
  padding: 12px 0 12px 0;
  border-radius: 0px 0px 2px 2px;
  background: #f3f3f3;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  border-top: 1px solid #dadada;
}
.cardContThree .cardContThreeCol {
  border-right: 1px solid #dadada;
}
.tCon {
  margin-right: 8px;
}
::v-deep .gutter-row ant-col ::v-deep ant-col-6 {
  margin-top: 0;
}
</style>