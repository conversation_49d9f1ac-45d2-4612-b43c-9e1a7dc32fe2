import * as THREE from 'three'
import { httpAction, getAction, postAction } from '@/api/manage'

export const clickMixins = {
  data() {
    return {
      lastEvent: null,
      lastEl: null
    }
  },
  methods: {
    onMouseClick(event) {
      if (this.clickState) return
      this.clickState = true
      let clickTimer = setTimeout(() => {
        this.clickState = false
        clearTimeout(clickTimer)
      }, 100)
      var intersectsAll = this.getIntersects(event)
      let intersects = intersectsAll.filter(el => el.object.name !== '')
      if (intersects.length === 0) {
        this.panelData = null
        return
      }
      let clicktarget = intersects[0].object
      if (clicktarget.name === '服务器') {
        let code = clicktarget.userData.deviceCode
        postAction('/topo/device/status', { deviceCode: code }).then(res => {
          if (res.success) {
            let alarmInfo = this.alarmInfos.find(el => el.deviceCode === code)
            this.panelData = [
              { name: '设备名称', value: res.result.name },
              { name: '设备IP', value: res.result.ip },
              { name: '设备状态', value: res.result.status == 0 ? '离线' : '在线' }
            ]
            if (alarmInfo && alarmInfo.templateName) {
              let alarm = this.alarmLevelList.find(el => el.value == alarmInfo.level)
              let alarmLevel = alarm ? alarm.name : ''
              this.panelData.push(
                { name: '告警名称', value: alarmInfo.templateName },
                { name: '告警级别', value: alarmLevel }
              )
            }
          }
        })

      }
      else if (clicktarget.parent && clicktarget.parent.name === 'yq_group_jigui') {
        let ud = clicktarget.parent.userData
        this.panelData = [
          { name: '机柜名称', value: ud.name },
          ...ud.infoData
        ]
      }
      else if (clicktarget.userData.groupId) {//glb 对象点击
        let groupTarget = scene.children.find((el) => {
          return el.uuid === clicktarget.userData.groupId
        })
        if(groupTarget.userData.deviceCode){  this.getDataByCode(groupTarget.userData.deviceCode)}

      } else if (clicktarget.userData.deviceCode) { //动环设备点击
        let code = clicktarget.userData.deviceCode
        if(code){
          this.getDataByCode(code)
        }
      } else {
        this.panelData = null
      }

    },
    getDataByCode(code) {
      getAction('/topo/room/status', { deviceCode: code }).then(res => {
        if (res.success) {
          // console.log("获取的设备信息 == ", res)
          this.panelData = [
            { name: '设备名称', value: res.result.name }
          ]
          if (res.result.dataInfo && res.result.dataInfo.length > 0) {
            res.result.dataInfo.forEach(el => {
              this.panelData.push(
                { name: el.name, value: el.value[0].value }
              )
            })
          }
        }
      })
    },
    onMouseDblclick(event) {
      // 获取 raycaster 和所有模型相交的数组，其中的元素按照距离排序，越近的越靠前
      var intersectsAll = this.getIntersects(event)
      // console.log("点击了 === ", intersectsAll)
      let intersects = intersectsAll.filter(el => el.object.name !== '')
      if (intersects.length === 0) return
      // if(intersects[0].object.name === "") return;
      if (this.$store.state.threejs.isEditor) {
        let outerMesh = scene.children.find(el => {
          return el.name === 'yq_outerMesh'
        })
        if (outerMesh) scene.remove(outerMesh)
        let clicktarget = intersects.find((el => {
          return el.object.name && el.object.name.includes('yq_')
        }))
        // console.log("点击了 === ", clicktarget)
        if (clicktarget) {
          let targetObj
          if (clicktarget.object.name === 'yq_jigui' || clicktarget.object.userData.groupId) {
            let groupTarget = clicktarget.object.parent
            if (clicktarget.object.userData.groupId) {
              groupTarget = scene.children.find((el) => {
                return el.uuid === clicktarget.object.userData.groupId
              })
            }
            targetObj = this.createOutMesh(groupTarget)
            scene.add(targetObj)
          } else {
            targetObj = clicktarget.object
          }
          this.changeTransObj(targetObj)
        }
      } else {
        let clicktarget = intersects[0].object
        if (clicktarget) {
          if (clicktarget.name === '服务器') return
          if (clicktarget.parent && clicktarget.parent.name === 'yq_group_jigui') {
            let door = clicktarget.parent.children.find(el => {
              return el.userData.meshType === 'jigui_door'
            })
            if (door) {
              let depth = clicktarget.parent.userData.depth || clicktarget.parent.userData.jiguiZ
              if (door.rotation.y === 0) {

                door.position.z = -depth / 2
                door.geometry.translate(0, 0, depth / 2)
                door.rotateY(0.5 * Math.PI)
              } else {
                door.position.z = 0
                door.geometry.translate(0, 0, -depth / 2)
                door.rotateY(-0.5 * Math.PI)
              }
            }
          }


        }


      }
    },
    onDocumentMouseMove(event) {

      var intersectsAll = this.getIntersects(event)
      let intersects = intersectsAll.filter(el => el.object.name !== '' && el.object.name !== 'warnSign' && el.object.name !== 'yq_floor')
      let curEl = null
      if (intersects.length > 0) {
        let SELECTED = intersects[0].object
        curEl = SELECTED
      }
      if (this.lastEl != curEl) {
        if (this.tipTimer) {
          clearTimeout(this.tipTimer)
          this.tipTimer = null
        }
        if (curEl) {
          this.tipTimer = setTimeout(() => {
            this.tipLeft = (this.lastEvent.offsetX - 8)
            this.tipTop = (this.lastEvent.offsetY - 8)
            if (curEl.userData && curEl.userData.deviceCode) {
              this.tipText = curEl.userData.name || curEl.userData.deviceCode
            } else if (curEl.userData && curEl.userData.groupId) {
              let groupTarget = scene.children.find((el) => {
                return el.uuid === curEl.userData.groupId
              })
              this.tipText = groupTarget.userData.name || groupTarget.name
            } else if (curEl.parent && curEl.parent.name === 'yq_group_jigui') {
              this.tipText = curEl.parent.userData.name || curEl.parent.name
            } else {
              this.tipText = curEl.userData.name || curEl.name
            }
            this.tipShow = true
          }, 300)
        }

      }

      this.lastEvent = event
      this.lastEl = curEl
      if (curEl == null) {
        this.tipShow = false
      }

    },
    getIntersects(event) {
      event.preventDefault()
      // 声明 raycaster 和 mouse 变量
      var raycaster = new THREE.Raycaster()
      var mouse = new THREE.Vector2()
      // 通过鼠标点击位置,计算出 raycaster 所需点的位置,以屏幕为中心点,范围 -1 到 1
      if (this.$store.state.threejs.isEditor) {
        mouse.x = (event.offsetX / (window.innerWidth - 300)) * 2 - 1
        mouse.y = -(event.offsetY / window.innerHeight) * 2 + 1
      } else {
        mouse.x = (event.offsetX / this.boxW) * 2 - 1
        mouse.y = -(event.offsetY / this.boxH) * 2 + 1
      }


      //通过鼠标点击的位置(二维坐标)和当前相机的矩阵计算出射线位置
      raycaster.setFromCamera(mouse, camera)

      // 获取与射线相交的对象数组，其中的元素按照距离排序，越近的越靠前
      var intersects = raycaster.intersectObjects(scene.children, true)

      //返回选中的对象
      return intersects
    },

    changeMaterial(object) {
      var material = new THREE.MeshLambertMaterial({
        color: 0xffffff * Math.random(),
        transparent: object.material.transparent ? false : true,
        opacity: 0.8
      })
      object.material = material
    },
    onKeyDown(event) {
      switch (event.keyCode) {
        case 13:
          this.initCamera()
          this.initControls()
          break
      }
    }
  }
}