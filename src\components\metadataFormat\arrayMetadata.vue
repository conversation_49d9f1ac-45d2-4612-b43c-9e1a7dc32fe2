<template>
  <a-form-item label="元素类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
    <a-select
      show-search
      labelInValue
      v-decorator="['formatValue']" placeholder="请选择元素类型" @change="onChange"
      option-filter-prop="children"
      :filter-option="filterOption"
      @focus="handleFocus"
      @blur="handleBlur"
    >
        <a-select-opt-group>
            <span slot="label">基本类型</span>
            <a-select-option value="int">int(整型)</a-select-option>
            <a-select-option value="long">long(长整数型)</a-select-option>
            <a-select-option value="float">float(单精度浮点型)</a-select-option>
            <a-select-option value="double">double(双精度浮点型)</a-select-option>
            <a-select-option value="text">text(字符串)</a-select-option>
            <a-select-option value="bool">bool(布尔型)</a-select-option>
        </a-select-opt-group>
        <a-select-opt-group>
            <span slot="label">其他类型</span>
            <a-select-option value="date">date(时间型)</a-select-option>
            <a-select-option value="enum">enum(枚举)</a-select-option>
            <a-select-option value="object">object(结构体)</a-select-option>
            <a-select-option value="file">file(文件)</a-select-option>
            <a-select-option value="password">password(密码)</a-select-option>
            <a-select-option value="table">table(表格)</a-select-option>
            <a-select-option value="geoPoint">geoPoint(地理位置)</a-select-option>
        </a-select-opt-group>
    </a-select>
  </a-form-item>

</template>

<script>
  export default {
    name:'arrayMetadata',
    props:['typeFormatValue'],
    data() {
      return {
        value: {},
        labelCol: {
          xs: { span: 5 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        },
      }
    },
    methods: {
      onChange(value) {
        this.value = value;
        this.$emit('changeFormatValue', JSON.stringify(value));
      },
      
    },
    mounted(){
     
    }
  }
</script>

<style>
</style>
