<template>
  <div class="stats-header">
    <img v-if="logoShow" class='left-logo' :src='logoUrl' :style='{height: "calc(36 / 19.2 * 1vw)"}'>
    <div class="logo" @click="goGateway" :content="title" :style="{'font-size': fontSize + 'px'}">
     <span> {{ title }}</span>
    </div>
    <div class="tab tab-left">
      <div class="time"></div>
      <div class="menu menu-left menu-left-first">
        <span v-if="menus[0]" class="menu-text" :class="{ 'menu-selected': path === menus[0].path }"
              @click="changeMenu(menus[0])">
          {{ menus[0].meta.title }}
        </span>
      </div>
      <div class="menu menu-left">
        <span v-if="menus[1]" class="menu-text" :class="{ 'menu-selected': path === menus[1].path }"
              @click="changeMenu(menus[1])">
          {{ menus[1].meta.title }}
        </span>
      </div>
    </div>
    <div class="tab tab-right">
      <div class="menu menu-right">
        <span v-if="menus[2]" class="menu-text" :class="{ 'menu-selected': path === menus[2].path }"
              @click="changeMenu(menus[2])">{{ menus[2].meta.title }}</span>
      </div>
      <div class="menu menu-right">
        <span v-if="menus[3]" class="menu-text" :class="{ 'menu-selected': path === menus[3].path }"
              @click="changeMenu(menus[3])">{{ menus[3].meta.title }}</span>
      </div>
      <div class="date">
        <div class="date-time">{{ timeStr }}</div>
        <div class="date-date">
          <span style="margin-right: 5px">{{ dayStr }}</span>
          <span class="date-str">{{ dateStr }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getAction } from '@/api/manage';
import { PLATFORM_TYPE } from '@/store/mutation-types';
import { generateIndexRouter, generateBigscreenRouter } from '@/utils/util'
import store from '@/store'
import router from '@/router'
import {
  mapActions,
  mapState
} from 'vuex'
import moment from 'moment'
import Vue from 'vue'
export default {
  data() {
    return {
      title: window._CONFIG['bigScreenSysName'],
      logoUrl:window.config.bigscreen.bigScreenLogoUrl,
      logoShow:window.config.logoShow,
      menus: [],
      timeStr: '',
      dateStr: '',
      dayStr: '',
      aniFrame: null,
      fontSize: 37,
      maxWidth: 440
    }
  },
  watch: {
    title() {
      this.adjustFontSize()
    }
  },
  computed: {
    ...mapState({
      permissionMenuList: (state) => state.user.permissionList,
      userInfo: (state) => state.user.info,
    }),
    path() {
      return this.$route.path
    },
    textWidth() {
      // 创建临时元素来获取文本宽度
      const tempEl = document.createElement('span')
      tempEl.style.fontSize = this.fontSize + 'px'
      tempEl.style.visibility = 'hidden'
      tempEl.innerHTML = this.title
      document.body.appendChild(tempEl)
      const width = tempEl.offsetWidth
      document.body.removeChild(tempEl)
      return width
    }
  },
  created() {
    this.menus = this.permissionMenuList
    this.getDateAndTime()
  },
  mounted() {
    this.adjustFontSize()
  },
  beforeDestroy() {},
  methods: {
    ...mapActions(['Logout', 'GetPermissionList', 'saveSysTypeMenu']),
    adjustFontSize() {
      // 当文本宽度超过容器最大宽度时，调整字体大小
      if (this.textWidth > this.maxWidth && this.fontSize > 12) {
        this.fontSize--
        this.adjustFontSize()
      }
    },
    getDateAndTime() {
      this.dateStr = moment().format('L')
      this.timeStr = moment().format('HH:mm')
      this.dayStr = moment().format('dddd')
      window.requestAnimationFrame(this.getDateAndTime)
    },
    goGateway() {
      let st = window._CONFIG['system_Type']
      if (st === '2') {
        getAction('/sys/permission/getUserPlatformTypeByToken').then((res) => {
          if (res.success && res.result) {
            let tem = res.result.split(',')
            getAction('/sys/permission/platformTypeList').then((sres) => {
              if (sres.success && sres.result) {
                let plat = sres.result[0].value
                if (tem.includes(plat)) {
                  this.entrancePlanning(plat)
                } else {
                  this.entrancePlanning(tem[0])
                }
              }
            })
          }
        })
      }else if (st === '0'){
        this.$router.push({
          path: '/gateway'
        })
      }
      else {
        this.entrancePlanning(1)
      }

    },
    changeMenu(menu) {
      if (menu.path === this.path) return
      this.$router.push({
        path: menu.path
      })
    },
    entrancePlanning(index) {
      sessionStorage.setItem(PLATFORM_TYPE, index)
      const that = this
      that.GetPermissionList(index).then((res) => {
        if (res == 1) {
          return
        }
        const menuData = res.result.menu
        var redirect = ''
        if (menuData && menuData.length > 0) {
          if (menuData[0].children && menuData[0].children.length > 0) {
            redirect = menuData[0].children[0].path
          } else {
            redirect = menuData[0].path
          }
        } else {
          return
        }

        let constRoutes = []
        if (index === 4) {
          constRoutes = generateBigscreenRouter(menuData)
        } else {
          constRoutes = generateIndexRouter(menuData)
        }
        // 添加主界面路由
        store
          .dispatch('UpdateAppRouter', {
            constRoutes,
          })
          .then(() => {
            // 根据roles权限生成可访问的路由表
            // 动态添加可访问路由表
            router.addRoutes(store.getters.addRouters)
            this.$router.push({
              path: redirect,
            })
          })
      })
    },
  },
}
</script>

<style lang="less" scoped>
.stats-header {
  height: calc(135 / 19.2 * 1vw);
  color: #fff;
  // display: flex;
  // align-items: center;
  background-image: url(/statsCenter/layout/head.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  position: relative;
  .left-logo {
    position: absolute;
    left: 8px;
    top: calc(16 / 19.2 * 1vw);;
  }
  .logo {
    white-space: nowrap;
    width: calc(480 / 19.2 * 1vw);
    line-height: calc(42 / 19.2 * 1vw);
    // font-size: calc(37 / 19.2 * 1vw);
    font-weight: 500;
    cursor: pointer;
    position: absolute;
    top: calc(20 / 19.2 * 1vw);
    left: calc((100vw - 480 / 19.2 * 1vw) / 2);
    text-align: center;
    color: #fff;
    letter-spacing: calc(5 / 19.2 * 1vw);
    text-shadow: 0px 2px 14px rgba(0, 121, 177, 0.88);
    font-family: Adobe Heiti Std;
    span::before {
      content: attr(content);
      position: absolute;
      inset: 0;
      transform: rotatex(180deg) translatey(15px);
      transform-origin: 50% 100%;
      // filter: blur(2px);
      mask: linear-gradient(transparent 30%, rgba(255, 255, 255, 0.2) 90%);
    }
  }

  .tab {
    height: calc(46 / 19.2 * 1vw);
    width: calc(500 / 19.2 * 1vw);
    top: calc(11 / 19.2 * 1vw);
    position: absolute;
    display: flex;
    align-items: center;
    color: #c3e1ff;

    .menu {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: calc(20 / 19.2 * 1vw);
      position: relative;
      letter-spacing: 3px;

      .menu-text {
        cursor: pointer;
        position: relative;
        width: calc(174 / 19.2 * 1vw);
        text-align: center;
        font-family: SourceHanSansCN-Regular;

        &:hover {
          font-size: calc(21 / 19.2 * 1vw);
        }
      }

      .menu-selected {
        color: #fff;
        font-weight: 500;
        backface-visibility: hidden;
        z-index: 100;

        &::after {
          position: absolute;
          z-index: -1;
          content: '';
          height: calc(80 / 19.2 * 1vw);
          width: 100%;
          background-image: url(/statsCenter/layout/select.png);
          background-repeat: no-repeat;
          background-size: 100% 100%;
          top: calc(-8 / 19.2 * 1vw);
          left: 0px;
        }

        &:hover {
          font-size: calc(20 / 19.2 * 1vw);
        }
      }
    }
  }

  .tab-left {
    .time {
      width: 20%;
      display: flex;
      align-items: center;
      font-size: calc(14 / 19.2 * 1vw);
      height: 100%;
      margin-left: calc(28 / 19.2 * 1vw);
    }

    .menu-left {
      width: 40%;

      &::before {
        position: absolute;
        content: '';
        left: 0;
        height: 100%;
        width: 13px;
        background-image: url(/statsCenter/layout/menuLine.png);
        background-repeat: no-repeat;
        background-size: 100% 100%;
      }
    }

    .menu-left-first::before {
      width: 0px;
    }
  }

  .tab-right {
    right: 0;

    .menu-right {
      width: 35%;

      &::before {
        position: absolute;
        content: '';
        right: 0;
        height: 100%;
        width: 13px;
        background-image: url(/statsCenter/layout/menuLine.png);
        background-repeat: no-repeat;
        background-size: 100% 100%;
      }
    }

    .date {
      width: 30%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;

      height: 100%;
      width: 30%;

      .date-time {
        font-size: calc(20 / 19.2 * 1vw);
        font-family: DIN-Medium;
        line-height: 1;
      }

      .date-date {
        font-size: calc(12 / 19.2 * 1vw);
        font-family: SourceHanSansCN-Regular;
      }
    }
  }

  .menu-item {
    cursor: pointer;
    margin-left: 30px;
  }

  .menu-active {
    color: aqua;
  }
}
</style>