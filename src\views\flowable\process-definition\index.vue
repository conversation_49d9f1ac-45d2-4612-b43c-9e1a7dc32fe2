<template>
  <a-row style='height: 100%'>
    <a-col style='height: 100%; display: flex; flex-direction: column'>
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0', marginRight: '12px' }" class='card-style'>
        <!-- 查询区域 -->
        <div class='table-page-search-wrapper'>
          <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
            <a-row :gutter='24'>
              <a-col :span='spanValue'>
                <a-form-model-item label='版本'>
                  <a-switch v-model='queryParam.latestVersion' checked-children='最新' un-checked-children='全部' />
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter='24' ref='row'>
              <!--            <a-col :xl="6" :lg="7" :md="8" :sm="24">-->
              <!--              <a-form-item label="流程定义id">-->
              <!--                <j-input type="" :maxLength='maxLength' placeholder="请输入流程定义id" v-model="queryParam.processDefinitionId"></j-input>-->
              <!--              </a-form-item>-->
              <!--            </a-col>-->
              <a-col v-show="getVisible('name')" :span='spanValue'>
                <a-form-item :label="getTitle('name')">
                  <a-input :maxLength='maxLength' :allow-clear='true' autocomplete='off' v-model='queryParam.processDefinitionName'
                    placeholder='请输入流程名称' type='' />
                </a-form-item>
              </a-col>
              <a-col v-show="getVisible('key')" :span='spanValue'>
                <a-form-item :label="getTitle('key')">
                  <a-input :maxLength='maxLength' :allow-clear='true' autocomplete='off' v-model='queryParam.processDefinitionKey'
                    placeholder='请输入流程编码' type='' />
                </a-form-item>
              </a-col>
              <a-col v-show="getVisible('category')" :span='spanValue'>
                <a-form-item :label="getTitle('category')">
                  <j-dict-select-tag v-model='queryParam.processDefinitionCategory' placeholder='请选择流程分类'
                    dictCode='bpm_process_type' @change='changeModelCategory' />
                </a-form-item>
              </a-col>
              <a-col v-show="getVisible('suspended')" :span='spanValue'>
                <a-form-item :label="getTitle('suspended')">
                  <a-select placeholder='请选择流程状态' :allow-clear='true' :getPopupContainer='(node) => node.parentNode'
                    v-model='queryParam.state'>
                    <a-select-option value='2'>激活</a-select-option>
                    <a-select-option value='3'>挂起</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <!--          <a-col v-show="getVisible('formKey')" :span='spanValue'>
                <a-form-item :label="getTitle('formKey')">
                  <a-input
                    :maxLength='maxLength'
                    :allow-clear='true'
                    autocomplete='off'
                    v-model='queryParam.formKey'
                    placeholder='请输入表单编码' type='' />
                </a-form-item>
              </a-col>-->
              <a-col :span='spanValue'>
                <span class='table-page-search-submitButtons' style='overflow: hidden;'>
                  <a-button icon='search' type='primary' @click='searchQuery'>查询</a-button>
                  <a-button icon='reload' style='margin-left: 8px' @click='searchReset'>重置</a-button>
                  <a v-if='queryItems.length>0' style='margin-left: 8px' @click='doToggleSearch'>{{ queryName }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
        <!-- 查询区域-END -->

        <!--自定义查询项 -->
        <div v-if='toggleSearchStatus' class='custom-query-item'>
          <a-checkbox-group v-model='settingQueryItems' :defaultValue='settingQueryItems' style='width:100%'
            @change='onQuerySettingsChange'>
            <a-row :gutter='24'>
              <template v-for='(item,index) in queryItems'>
                <a-col v-show='item.checked' :span='querySpanValue' class='col-checkbox'>
                  <a-checkbox :disabled='item.disabled' :value='item.dataIndex'>
                    <j-ellipsis :length='7' :value='item.title'></j-ellipsis>
                  </a-checkbox>
                </a-col>
              </template>
            </a-row>
          </a-checkbox-group>
        </div>
        <!-- 自定义查询项-END -->
      </a-card>

      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <!-- 操作按钮区域 -->
        <div class='table-operator'>
          <a-upload name='file' :showUploadList='false' :multiple='false' :headers='tokenHeader' :action='importBpmn'
            @change='handleImportExcel'>
            <!--<a-button type="primary" icon="import">导入</a-button>-->
          </a-upload>
        </div>

        <!-- table区域-begin -->
        <div>
          <!--        <div class="ant-alert ant-alert-info" style="margin-bottom: 16px">
                    <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择
                    <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
                    <a style="margin-left: 24px" @click="onClearSelected">清空</a>
                    <span style="float: right">
                    <a @click="loadData()"><a-icon type="sync" />刷新</a>
                    <a-divider type="vertical" />
                    <a-popover title="自定义列" trigger="click" placement="leftBottom">
                      <template slot="content">
                        <a-checkbox-group @change="onColSettingsChange" v-model="settingColumns" :defaultValue="settingColumns">
                          <a-row style="width: 400px">
                            <template v-for="(item, index) in defColumns">
                              <template v-if="item.key != 'rowIndex' && item.dataIndex != 'action'">
                                <a-col :span="12" :key="index"
                                ><a-checkbox :value="item.dataIndex"
                                ><j-ellipsis :value="item.title" :length="10"></j-ellipsis></a-checkbox
                                ></a-col>
                              </template>
                            </template>
                          </a-row>
                        </a-checkbox-group>
                      </template>
                      <a><a-icon type="setting" />设置</a>
                    </a-popover>
                  </span>
                  </div>-->

          <a-table ref='table' bordered rowKey='id' :columns='columns'
            :dataSource='dataSource' :pagination='ipagination' :loading='loading' @change='handleTableChange'>
            <div slot='filterDropdown'>
              <a-card>
                <a-checkbox-group @change='onColSettingsChange' v-model='settingColumns' :defaultValue='settingColumns'>
                  <a-row style='width: 400px'>
                    <template v-for='(item, index) in defColumns'>
                      <template v-if="item.key != 'rowIndex' && item.dataIndex != 'action'">
                        <a-col :span='12' :key='index'>
                          <a-checkbox :value='item.dataIndex'>
                            <j-ellipsis :value='item.title' :length='10'></j-ellipsis>
                          </a-checkbox>
                        </a-col>
                      </template>
                    </template>
                  </a-row>
                </a-checkbox-group>
              </a-card>
            </div>
            <a-icon slot='filterIcon' type='setting' :style="{ fontSize: '16px', color: '#108ee9' }" />

            <span slot='action' slot-scope='text, record'>
              <a @click='handleDetail(record)'>查看</a>
              <a-divider type='vertical' />
              <a-dropdown :overlayStyle="{ overflowY: 'hidden', boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)' }">
                <a class='ant-dropdown-link'>更多
                  <a-icon type='down' /></a>
                <a-menu slot='overlay'>
                  <!-- <a-menu-item @click="handleCopy(record)">复制</a-menu-item> -->
                  <a-menu-item @click='handleImage(record)'>流程图</a-menu-item>
                  <a-menu-item @click='getNodeData(record)'>任务设置</a-menu-item>
                  <a-menu-item @click='handleAuthorization(record)'>流程授权</a-menu-item>
                  <a-menu-item @click='btnExport(record)'>导出</a-menu-item>
                  <a-menu-item @click='switchState(record)'>{{ record.suspended ? '激活' : '挂起' }}</a-menu-item>
                  <a-menu-item>
                    <a-popconfirm title="确定删除定义吗?" @confirm="handleDelete(record.id)">
                      <a>删除定义</a>
                    </a-popconfirm>
                  </a-menu-item>
                  <a-menu-item>
                    <a-popconfirm title="确定删除对应实例吗?" @confirm="handleDeleteInstance(record.id)">
                      <a>删除对应实例</a>
                    </a-popconfirm>
                  </a-menu-item>
                  <a-menu-item>
                    <a-popconfirm title="确定删除定义及实例吗?" @confirm="handleDelete(record.id, true)">
                      <a>删除定义及实例</a>
                    </a-popconfirm>
                  </a-menu-item>
                  <a-menu-item @click='jump(record.id)'>实例列表</a-menu-item>
                </a-menu>
              </a-dropdown>
            </span>
          </a-table>
        </div>
        <!-- table区域-end -->
      </a-card>

      <!-- 表单区域 -->
      <process-definition-module ref='modalForm' @ok='modalFormOk'></process-definition-module>

      <!--流程图区域-->
      <process-picture ref='processPicture' :row='chooseRow' :pictureVisible='pictureVisible'
        @changePictureVisible='pictureHandleCancel'></process-picture>
      <!--流程授权-->
      <process-definition-authorization-module ref='processAuthorizationModal' />
      <node-settings ref='nodeSettingsForm'></node-settings>
    </a-col>
  </a-row>
</template>

<script>
  import {
    module
  } from '@/api/flowable'
  import {
    downloadFile
  } from '@/api/manage'
  import JSuperQuery from '@/components/jeecg/JSuperQuery.vue'
  import JInput from '@/components/jeecg/JInput.vue'
  import {
    filterDictTextByCache
  } from '@/components/dict/JDictSelectUtil'
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import Vue from 'vue'
  import {
    filterObj
  } from '@/utils/util'
  import {
    deleteAction,
    getAction
  } from '@api/manage'
  import ProcessDefinitionModule from './modules/ProcessDefinitionModule'
  import ProcessPicture from './modules/ProcessPicture'
  import ProcessDefinitionAuthorizationModule from './modules/ProcessDefinitionAuthorizationModule'
  import NodeSettings from '@views/flowable/process-definition/modules/NodeSettings'
  import {
    YqFormSeniorSearchLocation
  } from '@/mixins/YqFormSeniorSearchLocation'

  var baseUrl = '/flowable/processDefinition'

  export default {
    name: 'ProcessDefinition',
    mixins: [JeecgListMixin, YqFormSeniorSearchLocation],
    components: {
      NodeSettings,
      ProcessDefinitionModule,
      ProcessDefinitionAuthorizationModule,
      JSuperQuery,
      JInput,
      ProcessPicture
    },
    data() {
      return {
        maxLength:50,
        formItemLayout: {
          labelCol: {
            style: 'width:100px'
          },
          wrapperCol: {
            style: 'width:calc(100% - 100px)'
          }
        },
        queryParam: {
          latestVersion: true,
          state: undefined
        },
        // 流程图
        pictureVisible: false,
        picturePath: '',

        description: '单表示例列表',
        //字典数组缓存
        sexDictOptions: [],
        importBpmn: `${window._CONFIG['domianURL']}/flowable/processDefinition/import`,
        //列设置
        settingColumns: [],
        //列定义
        columns: [{
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            isUsed: false,
            customCell: () => {
              let cellStyle = 'text-align:center;width:60px'
              return {
                style: cellStyle
              }
            },
            customRender: function (t, r, index) {
              return parseInt(index) + 1
            }
          },
          {
            title: '流程名称',
            dataIndex: 'name',
            isUsed: true,
            customCell: () => {
              let cellStyle = 'text-align:center'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '流程编码',
            dataIndex: 'key',
            isUsed: true,
            customCell: () => {
              let cellStyle = 'text-align:center'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '流程分类',
            dataIndex: 'category',
            isUsed: true,
            customCell: () => {
              let cellStyle = 'text-align:center'
              return {
                style: cellStyle
              }
            },
            customRender: (text) => {
              return filterDictTextByCache('bpm_process_type', text)
            }
          },
          {
            title: '版本',
            dataIndex: 'version',
            isUsed: false,
            customCell: () => {
              let cellStyle = 'text-align:center'
              return {
                style: cellStyle
              }
            },
            customRender: (v) => {
              return 'v' + v
            }
          },
          {
            title: '模型版本',
            dataIndex: 'modelVersion',
            isUsed: false,
            customCell: () => {
              let cellStyle = 'text-align:center'
              return {
                style: cellStyle
              }
            },
            customRender: (v) => {
              return 'v' + v
            }
          },
          {
            title: '流程状态',
            dataIndex: 'suspended',
            isUsed: true,
            customCell: () => {
              let cellStyle = 'text-align:center;width:100px'
              return {
                style: cellStyle
              }
            },
            customRender: (v) => {
              return v ? '挂起' : '激活'
            }
          },
          {
            //不支持查询
            title: '表单编码',
            dataIndex: 'formKey',
            isUsed: false,
            customCell: () => {
              let cellStyle = 'text-align:center'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: 150,
            isUsed: false,
            fixed: 'right',
            scopedSlots: {
              /*  filterDropdown: 'filterDropdown',
                filterIcon: 'filterIcon',*/
              customRender: 'action'
            }
          }
        ],
        url: {
          list: `${baseUrl}/list`,
          delete: `${baseUrl}/delete`,
          deleteInstance: `${baseUrl}/deleteInstance`,
          getProcessNode: `${baseUrl}/getProcessNode`,
          editNodeUser: `${baseUrl}/editNodeUser`
          // deleteBatch: "/test/jeecgDemo/deleteBatch",
          // exportXlsUrl: "/test/jeecgDemo/exportXls"
        },
        latestVersionOptions: [{
          label: '最新版本',
          value: 'true'
        }],
        suspendedOptions: [{
          label: '挂起的',
          value: 'true'
        }],
        activeOptions: [{
          label: '激活的',
          value: 'true'
        }],
        dialogProcessImageVisible: false,
        selectedProcessDefinitionId: '',
        processAuthorizationVisible: false,
        selectedProcessDefinitionName: '',
        chooseRow: {}
      }
    },
    methods: {
      changeModelCategory(value) {
        if (!value) {
          this.queryParam.processDefinitionCategory = undefined
        }
      },
      searchReset() {
        this.queryParam = {
          latestVersion: true,
          state: undefined
        }
        this.loadData(1)
      },
      handleAuthorization(row) {
        if (!row.id) {
          this.$message.error('流程实例ID不存在')
          return
        }
        this.$refs.processAuthorizationModal.init(row)
      },

      getNodeData(row) {
        this.selectRow = row
        this.$refs.nodeSettingsForm.title = '任务设置'
        this.$refs.nodeSettingsForm.initNodeData(row)
      },
      callback(val) {
      },
      btnExport(row) {
        downloadFile('/flowable/processDefinition/xml', row.name + '-v' + row.version + '.bpmn20.xml', {
          processDefinitionId: row.id
        })
      },
      handleImage(row) {
        getAction('/flowable/processDefinition/xml', {
          processDefinitionId: row.id
        }).then((res) => {
          this.chooseRow = row
          this.pictureVisible = true
          this.$nextTick(() => {
            this.$refs.processPicture.initPic(res)
          })
        })

        // this.picturePath = window._CONFIG['domianURL'] + '/flowable/processDefinition/image?processDefinitionId=' + row.id
        // this.selectedProcessDefinitionName = row.name
      },

      pictureHandleCancel(e) {
        this.pictureVisible = false
      },
      getQueryParams() {
        //高级查询器
        let sqp = {}
        if (this.superQueryParams) {
          sqp['superQueryParams'] = encodeURI(this.superQueryParams)
          sqp['superQueryMatchType'] = this.superQueryMatchType
        }
        var param = Object.assign(sqp, this.queryParam, this.isorter, this.filters)

        param.field = this.getQueryField()
        param.pageNo = this.ipagination.current
        param.pageSize = this.ipagination.pageSize
        delete param.birthdayRange //范围参数不传递后台
        return filterObj(param)
      },
      initDictConfig() {},
      onetomany: function () {
        // this.$refs.jeecgDemoTabsModal.add();
        // this.$refs.jeecgDemoTabsModal.title = "编辑";
      },
      //跳转单据页面
      jump(processDefinitionId) {
        this.$router.push({
          path: '/flowable/processInstance',
          query: {
            processDefinitionId: processDefinitionId
          }
        })
      },
      onBirthdayChange: function (value, dateString) {
        this.queryParam.birthday_begin = dateString[0]
        this.queryParam.birthday_end = dateString[1]
      },
      //列设置更改事件
      onColSettingsChange(checkedValues) {
        var key = this.$route.name + ':colsettings'
        Vue.ls.set(key, checkedValues, 7 * 24 * 60 * 60 * 1000)
        this.settingColumns = checkedValues
        const cols = this.defColumns.filter((item) => {
          if (item.key == 'rowIndex' || item.dataIndex == 'action') {
            return true
          }
          if (this.settingColumns.includes(item.dataIndex)) {
            return true
          }
          return false
        })
        this.columns = cols
      },
      initColumns() {
        //权限过滤（列权限控制时打开，修改第二个参数为授权码前缀）
        //this.defColumns = colAuthFilter(this.defColumns,'testdemo:');

        var key = this.$route.name + ':colsettings'
        let colSettings = Vue.ls.get(key)
        if (colSettings == null || colSettings == undefined) {
          let allSettingColumns = []
          this.defColumns.forEach(function (item, i, array) {
            allSettingColumns.push(item.dataIndex)
          })
          this.settingColumns = allSettingColumns
          this.columns = this.defColumns
        } else {
          this.settingColumns = colSettings
          const cols = this.defColumns.filter((item) => {
            if (item.key == 'rowIndex' || item.dataIndex == 'action') {
              return true
            }
            if (colSettings.includes(item.dataIndex)) {
              return true
            }
            return false
          })
          this.columns = cols
        }
      },
      switchState(record) {
        let param = {
          processDefinitionId: record.id
        }
        if (record.suspended) {
          module.activate(param).then((res) => {
            if (res.success) {
              this.$message.success(res.message)
              this.loadData()
            } else {
              this.$message.warning(res.message)
            }
          })
        } else {
          module.suspend(param).then((res) => {
            if (res.success) {
              this.$message.success(res.message)
              this.loadData()
            } else {
              this.$message.warning(res.message)
            }
          })
        }
      },
      handleDeleteInstance(id) {
        this.loading = true;
        let param = {
          processDefinitionId: id
        }
        deleteAction(this.url.deleteInstance, param).then((res) => {
          if (res.success) {
            this.$message.success("删除成功")
          } else {
            this.$message.warning(res.message)
          }
          this.loading = false;
          this.loadData()
        })
      },
      handleDelete(id, batch) {
        let param = {
          processDefinitionId: id
        }
        if (batch) {
          param.cascade = true
        }
        deleteAction(this.url.delete, param).then((res) => {
          if (res.success) {
            this.$message.success(res.message)
            this.loadData()
          } else {
            this.$message.warning(res.message)
          }
        })
      }
    },
    created() {
      this.getColumns(this.columns)
    }
  }
</script>
<style scoped lang='less'>
  @import '~@assets/less/common.less';
  @import '~@assets/less/YQCommon.less';

  //在全屏模式下，实时流程图容器containers的高度样式如下；至于非全屏模式下，containers高度样式在流程图组件中设定
  .j-modal-box.fullscreen {
    ::v-deep .containers {
      height: calc(100vh - 56px - 24px - 32px - 12px - 24px - 56px) !important;
    }
  }
</style>