<template>
  <div>
    <div v-if="knowledgeType==1" style="display:flex;align-items:center;justify-content:center;">
      <img
        src="~@assets/knowledge/wendang.png"
        style="width:15px;height:15px;background-color:#1890FF;padding:3px;border-radius:2px;"
      />
      <span>&nbsp;&nbsp;{{label2}}</span>
    </div>
    <div v-else style="display:flex;align-items:center;justify-content:center;">
      <img
        src="~@assets/knowledge/wenben.png"
        style="width:15px;height:15px;background-color:#40de5a;padding:3px;border-radius:2px;"
      />
      <span>&nbsp;&nbsp;{{label1}}</span>
    </div>
  </div>
</template>
<script>
export default {
  name: 'KnowledgeIcon',
  data() {
    return {}
  },
  props: {
    knowledgeType: {
      type: Number,
      default: 0
    },
    label2:{
      type: String,
      default: '文档'
    },
    label1:{
      type: String,
      default: '文本'
    }
  }
}
</script>
<style lang="less" scoped>
</style>