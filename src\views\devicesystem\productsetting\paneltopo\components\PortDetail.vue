<template>
  <j-modal width="80%" :title="title" :visible="visible" :footer="null" :zIndex="1001" @cancel="hide">
    <div style="height: 60vh; overflow: auto">
      <a-descriptions bordered v-if="portInfo">
        <a-descriptions-item v-for="item in portInfo" :key="item.code" :label="item.name">
          {{ item.value + (item.unit != null ? item.unit :'')  }}
        </a-descriptions-item>
      </a-descriptions>
    </div>
  </j-modal>
</template>
<script>
  export default {
    data() {
      return {
        visible: false,
        portInfo: null,
        title: "端口详情"
      }
    },
    methods: {
      show(info) {
        this.portInfo = info || {}
        this.title = "端口详情【" + (info ? info.portDesc.value + "】" : "")
        this.visible = true
      },
      hide() {
        this.visible = false
      },
    },
  }
</script>

<style>
</style>