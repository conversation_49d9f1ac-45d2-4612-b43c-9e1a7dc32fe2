<template>
  <j-modal :title='title' :width='width' :visible='visible' :destroyOnClose='true'
           switchFullscreen :centered='true'
           :okButtonProps="{ style: { display: 'none' }}"
           @cancel='handleCancel'
           cancelText='关闭'>
    <div>
<!--      <div :class="['table-container']">-->
<!--        &lt;!&ndash; table区域-begin &ndash;&gt;-->
<!--        <div style='overflow-y: auto'>-->
          <a-table ref='table' :columns='columns' :dataSource='dataSource' :loading='loading' :pagination='ipagination'
                   :rowKey='(record,index)=>{return index}' :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
                   bordered
                   @change='handleTableChange'>
            <!-- 字符串超长截取省略号显示-->
            <span slot='templateContent' slot-scope='text'>
          <j-ellipsis :length='25' :value='text' />
        </span>
            <template slot='name' slot-scope='text'>
              <!-- <a style="color: rgb(23, 87, 204);" @click="linkTo(text)">{{ text }}</a> -->
              <span>{{ text }}</span>
            </template>
            <span slot='action' slot-scope='text, record' class='caozuo'>
                <a @click='handleDetailPage(record)'>查看</a>
              </span>
          </a-table>
<!--        </div>-->
<!--      </div>-->
    </div>
    <zabbix-host-detail ref='zabbixHostDetail'></zabbix-host-detail>
  </j-modal>
</template>
<script>
import { getAction, postAction } from '@api/manage'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import JEllipsis from '@comp/jeecg/JEllipsis.vue'
import yqIcon from '@comp/tools/SvgIcon'
import { zabbixFields } from '@views/jkdjgl/model/zabbixFields'
import ZabbixHostDetail from '@views/jkdjgl/model/ZabbixHostDetail.vue'

export default {
  name: 'ZabbixHostList',
  mixins: [JeecgListMixin],
  components: {
    ZabbixHostDetail,
    JEllipsis,
    yqIcon
  },
  data() {
    return {
      title: '主机列表',
      width: '1200px',
      visible: false,
      disableSubmit: false,
      confirmLoading: false,
      record: {},
      datatypeList: [],
      ipagination: {
        current: 1,
        pageSize: 15,
        pageSizeOptions: ['10', '20', '50'],
        showTotal: (total, range) => {
          return ' 共' + total + '条'
        },
        showQuickJumper: false,
        showSizeChanger: false,
        total: 0
      },
      columns: [
        {
          title: '序号',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function(t, r, index) {
            return parseInt(index) + 1
          }
        },
        {
          title: '主机名称',
          dataIndex: 'name',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 200px'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'name'
          }
        },
        {
          title: '主机ID',
          dataIndex: 'hostid',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 90px'
            return {
              style: cellStyle
            }
          }
        },
        // CPU和内存后期会修改，前端处理单位换算
        {
          title: '描述',
          dataIndex: 'description',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 90px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '主机来源',
          dataIndex: 'flags',
          customRender: function(t, r, index) {
            return zabbixFields['flags'][t]
          },
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 90px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '清单填充模式',
          dataIndex: 'inventory_mode',
          customRender: function(t, r, index) {
            return zabbixFields['inventory_mode'][t]
          },
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 90px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '身份验证算法',
          dataIndex: 'ipmi_authtype',
          customRender: function(t) {
            return zabbixFields['ipmi_authtype'][t]
          },
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 90px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '密码',
          dataIndex: 'ipmi_password',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 90px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '权限级别',
          dataIndex: 'ipmi_privilege',
          customRender: function(t) {
            return zabbixFields['ipmi_privilege'][t]
          },
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 90px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '用户名',
          dataIndex: 'ipmi_username',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 90px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '有效维护状态',
          dataIndex: 'maintenance_status',
          customRender: function(t) {
            return zabbixFields['maintenance_status'][t]
          },
          customCell: () => {
            let cellStyle = 'text-align: center;width: 80px'
            return {
              style: cellStyle
            }
          },
        }, {
          title: '有效维护类型',
          dataIndex: 'maintenance_type',
          customRender: function(t) {
            return zabbixFields['maintenance_type'][t]
          },
          customCell: () => {
            let cellStyle = 'text-align: center;width: 80px'
            return {
              style: cellStyle
            }
          },
        },
        {
          title: '有效维护开始时间',
          dataIndex: 'maintenance_from',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 60px;'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '监视主机代理ID',
          dataIndex: 'proxy_hostid',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 60px;'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '主机的状态',
          dataIndex: 'status',
          customRender: function(t) {
            return zabbixFields['status'][t]
          },
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 60px;'
            return {
              style: cellStyle
            }
          }
        }, {
          title: '与主机的连接',
          dataIndex: 'tls_connect',
          customRender: function(t) {
            return zabbixFields['tls_connect'][t]
          },
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 60px;'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '证书颁发者',
          dataIndex: 'tls_issuer',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 60px;'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '证书主题',
          dataIndex: 'tls_subject',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 60px;'
            return {
              style: cellStyle
            }
          }
        },
        // {
        //   title: 'PSK标识',
        //   dataIndex: 'tls_psk_identity',
        //   customCell: () => {
        //     let cellStyle = 'text-align: center;min-width: 60px;'
        //     return {
        //       style: cellStyle
        //     }
        //   }
        // },
        // {
        //   title: '预共享密钥',
        //   dataIndex: 'tls_psk',
        //   customCell: () => {
        //     let cellStyle = 'text-align: center;min-width: 60px;'
        //     return {
        //       style: cellStyle
        //     }
        //   }
        // },
        // {
        //   title: '开机时间',
        //   dataIndex: 'runTime',
        //   customCell: () => {
        //     let cellStyle = 'text-align: center;min-width: 200px;max-width:800px'
        //     return {
        //       style: cellStyle
        //     }
        //   }
        // },
        {
          title: '操作',
          dataIndex: 'action',
          display:true,
          disabled:false,
          align: 'center',
          fixed: 'right',
          width: 140,
          scopedSlots: {
            filterDropdown: 'filterDropdown',
            filterIcon: 'filterIcon',
            customRender: 'action'
          }
        }
      ],
      url: {
        list: '/device/deviceInfo/getZabbixHostList'
      },
      option: {},
      queryParam: {
        deviceCode: ''
      },
      disableMixinCreated: true,
      zabbixFields
    }
  },
  created() {
  },
  mounted() {

  },
  methods: {
    show(record) {
      this.queryParam.deviceCode = record.deviceCode
      this.loadData(1)
      this.visible = true
    },
    handleCancel() {
      this.close()
    },
    handleOk() {
      this.close()
    },
    close() {
      this.visible = false
    },
    loadData(arg, data, id) {
      if (!this.url.list) {
        this.$message.error('请设置url.list属性!')
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1
      }
      var params = this.getQueryParams() //查询条件
      this.loading = true
      getAction(this.url.list, params).then((res) => {
        this.dataSource = res
        this.loading = false
      })
    },
    handleTableChange(pagination, filters, sorter) {
      //分页、排序、筛选变化时触发
      //TODO 筛选
      if (Object.keys(sorter).length > 0) {
        this.isorter.column = sorter.field
        this.isorter.order = 'ascend' === sorter.order ? 'asc' : 'desc'
      }
      this.ipagination = pagination
    },
    handleDetailPage(record){
        this.$refs.zabbixHostDetail.show(record)
    }
  },
}
</script>

<style scoped lang='less'>
@import '~@assets/less/normalModal.less';

/*/deep/ .ant-table-tbody .ant-table-row td {
  padding-top: 5px;
  padding-bottom: 5px;
}

.colorBox1 {
  margin-bottom: 20px;
  margin-right: 1px;
}

.colorBox {
  margin-bottom: 10px;
}

.colorTotal {
  padding-left: 7px;
  border-left: 4px solid #1e3674;
}

::v-deep .ant-descriptions {
  width: 100%;
}

.ant-btn {
  margin-left: 3px;
}

.ant-card-body .table-operator {
  margin-bottom: 18px;
}

.ant-table-tbody .ant-table-row td {
  padding-top: 15px;
  padding-bottom: 15px;
}

.anty-row-operator button {
  margin: 0 5px;
}

.ant-btn-danger {
  background-color: #ffffff;
}

.ant-modal-cust-warp {
  height: 100%;
}

.ant-modal-cust-warp .ant-modal-body {
  height: calc(100% - 110px) !important;
  overflow-y: auto;
}

.ant-modal-cust-warp .ant-modal-content {
  height: 90% !important;
  // overflow-y: hidden;
}

.table-page-search-wrapper {
  background-color: #fff;
  padding: 15px 0 0 15px;
}

.table-container {
  background-color: #fff;
  padding-right: 24px;
}

.query-btn {
  background: #ecf5ff;
  border: 1px solid #b3d8ff;
  border-radius: 4px;
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: #409eff;
  width: 73px;
  height: 28px;
  cursor: pointer;
  margin: 0px;
}

.yq-icon {
  font-size: 24px;
}*/
</style>