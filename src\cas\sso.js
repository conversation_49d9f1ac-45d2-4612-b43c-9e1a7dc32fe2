import Vue from 'vue'

import { ACCESS_TOKEN } from "@/store/mutation-types"
import store from '@/store'
import {  getAction } from '@/api/manage'
/**
 * 单点登录
 */
const init = (callback) => {
  if (window._CONFIG['casSSo'] == 'true') {
    let token = Vue.ls.get(ACCESS_TOKEN);
    let st = getUrlParam("ticket");
    let sevice = "http://" + window.location.host + "/gateway/gateway";
    if (token) {
      loginSuccess(callback);
    } else {
      if (st) {
        validateSt(st, sevice, callback);
        // getAction('license/licenseTest').then((res) => {
        //   if (res.code == 200) {
        //     validateSt(st, sevice, callback);
        //   } else {
        //     store.dispatch('setAuthorized',false)
        //   }
        // })
      } else {
        let serviceUrl = encodeURIComponent(sevice);
        window.location.href = window._CONFIG['casPrefixUrl'] + "/login?service=" + serviceUrl;
      }
    }
  }else{
    callback && callback()
  }
};
const SSO = {
  init: init
};

function getUrlParam(paraName) {
  let url = document.location.toString();
  let arrObj = url.split("?");

  if (arrObj.length > 1) {
    let arrPara = arrObj[1].split("&");
    let arr;

    for (let i = 0; i < arrPara.length; i++) {
      arr = arrPara[i].split("=");

      if (arr != null && arr[0] == paraName) {
        return arr[1];
      }
    }
    return "";
  }
  else {
    return "";
  }
}

function validateSt(ticket,service,callback){
      let params = {
        ticket: ticket,
        service:service
      };
      store.dispatch('ValidateLogin',params).then(res => {
        //this.departConfirm(res)
        if(res.success){
          loginSuccess(callback);
        }else{
          let sevice = "http://"+window.location.host+"/";
          let serviceUrl = encodeURIComponent(sevice);
          window.location.href = window._CONFIG['casPrefixUrl']+"/login?service="+serviceUrl;
        }
      }).catch((err) => {
      });
  }

function loginSuccess (callback) {
  callback();
}
export default SSO;