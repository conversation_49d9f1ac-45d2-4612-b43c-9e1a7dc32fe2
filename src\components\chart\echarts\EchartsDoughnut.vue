<template>
  <div style='width: 100%; height: 100%'>
    <div v-show='!isEmpty' :id='id' style='width: 100%; height: 100%'></div>
    <a-empty v-if='isEmpty' style='width: 100%; height: 100%'></a-empty>
  </div>
</template>

<script>
/*环形图表*/
import echarts from 'echarts'
import { getAction} from '@/api/manage'
export default {
  name: 'EchartsDoughnut',
  props: {
    id: {
      type:String,
      default:'echartsPie',
    },
    url: {
      type:String,
      default:'',
    },
    chartData: {
      type:Array,
        default: () => {
          return []
        }
    },
    colors: {
      type: Array,
      default: () => {
        return ['#5ab1ef', '#b6a2de', '#67e0e3', '#2ec7c9','#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc']
      },
    }
  },
  data() {
    return {
      chart: null,
      sourceData:[],
    }
  },
  mounted() {
    if(this.url){
      this.getChartData()
    }else{
      this.sourceData = this.chartData
      if(!this.isEmpty) {
        this.$nextTick(()=>{
          this.initChart(this.chartData)
        })
      }
    }
  },
  computed: {
    isEmpty() {
      return this.sourceData.length === 0
    }
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    window.removeEventListener('resize', this.changeResize)
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    getChartData(){
      getAction(this.url).then((res) => {
        if (res.success) {
          this.initChart()
        }
      })
    },
    initChart(data) {
      let el = document.getElementById(this.id)
      if(el === null)return;
      this.chart = echarts.init(el)
      if(this.chart === null)return;
      this.chart.setOption({
        tooltip: {
          trigger: 'item',
        },
        legend: {
          bottom: '0',
          left: 'center',
          type:'scroll',
        },
        series: [
          {
            color: this.colors,
            name: '访问来源',
            type: 'pie',
            radius: ['35%', '65%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2,
            },
            label: {
              show: false,
              position: 'center',
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '12',
                fontWeight: 'bold',
              },
            },
            labelLine: {
              show: false,
            },
            data: data,
            animationType: 'scale',
            animationEasing: 'exponentialInOut',
            animationDelay: function () {
              return Math.random() * 100;
            },
          },
        ],
      })
      window.addEventListener('resize', this.changeResize)
    },
    changeResize(){
      if (this.chart){
        this.chart.resize()
      }
    },
    refresh(data){
      this.chart.setOption({
        series: [{
          data:data
        }]
      })
    },
  }
}
</script>

<style lang="less" scoped>

</style>