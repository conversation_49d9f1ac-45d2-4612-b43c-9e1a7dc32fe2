<template>
  <a-table
    :columns="columns"
    :data-source="data"
    ref="table"
    size="middle"
    bordered
    rowKey="id"
    :scroll="{ x: 3500 }"
  >
  </a-table>
</template>
<script>
export default {
  // 基本信息
  name: 'table1',
  data() {
    return {
      columns: [
        // {
        //     title: '序号',
        //     dataIndex: '',
        //     key: 'rowIndex',
        //     width: 60,
        //     align: 'center',
        //     customRender: function(t, r, index) {
        //         return parseInt(index) + 1
        //     }
        // },
        {
          title: '编号',
          align: 'center',
          dataIndex: ''
        },
        {
          title: '标题',
          align: 'center',
          dataIndex: 'title'
        },
        {
          title: '类型',
          align: 'center',
          dataIndex: ''
        },
        {
          title: '优先级',
          align: 'center',
          dataIndex: ''
        },
        {
          title: '创建人',
          align: 'center',
          dataIndex: ''
        },
        {
          title: '创建时间',
          align: 'center',
          dataIndex: ''
        },
        {
          title: '联系电话',
          align: 'center',
          dataIndex: ''
        },
        {
          title: '状态',
          align: 'center',
          dataIndex: ''
        },
        {
          title: 'SLA创建时间',
          align: 'center',
          dataIndex: ''
        },
        {
          title: 'SLA完成时间',
          align: 'center',
          dataIndex: ''
        },
        {
          title: '事件所属人',
          align: 'center',
          dataIndex: ''
        },
        {
          title: '当前处理人',
          align: 'center',
          dataIndex: ''
        },
        {
          title: '实际响应时间',
          align: 'center',
          dataIndex: ''
        },
        {
          title: '实际完成时间',
          align: 'center',
          dataIndex: ''
        },
        {
          title: '事件来源',
          align: 'center',
          dataIndex: ''
        },
        {
          title: '事件描述',
          align: 'center',
          dataIndex: ''
        },
        {
          title: '附件',
          align: 'center',
          dataIndex: ''
        }
      ],
      data: [
        {
          id: '1',
          title: '嗯嗯嗯'
        }
      ]
    }
  }
}
</script>
<style scoped></style>
