<template>
  <div id="userLayout" :class="['user-layout-wrapper', device]">
    <div class="container">
      <div
        :class="['container_box', {'ten_percent':title.length < 12, 'six_percent':title.length < 15 && title.length >= 12, 'four_percent':title.length >= 15}]">
        <div class="top">
          <div class="header">
            <img v-show="logoShow" :src="loginUrl" class="logo" alt="logo">
            <span class="title">
              {{title}}
              <!-- <img :src="loginNameUrl" class="logo" alt="logo"> -->
            </span>
          </div>
          <div class="desc">
            <div class="left-desc"></div>
            <div class="center-desc" v-html="desc"></div>
            <div class="right-desc"></div>
            <!-- <img :src="loginBottomUrl" class="logo logoBottom" alt="logo"> -->
          </div>
        </div>

        <route-view></route-view>
      </div>
      <div class="footer">
        <div class="copyright" v-html="loginSysName">
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import RouteView from "@/components/layouts/RouteView"
  import {
    mixinDevice
  } from '@/utils/mixin.js'

  export default {
    name: "UserLayout",
    components: {
      RouteView
    },
    mixins: [mixinDevice],
    data() {
      return {
        logoShow: window._CONFIG['logoShow'] === 0 ? false : true,
        title: window._CONFIG['loginName'],
        desc: window._CONFIG['loginDesc'],
        loginUrl: window._CONFIG['loginUrl'],
        loginNameUrl:  window._CONFIG['loginNameUrl'],
        loginBottomUrl: window._CONFIG['loginBottomUrl'],
        loginSysName: window._CONFIG['loginSysName']
      }
    },
    mounted() {
      document.body.classList.add('userLayout')
    },
    beforeDestroy() {
      document.body.classList.remove('userLayout')
    },
  }
</script>

<style lang="less" scoped>
  #userLayout.user-layout-wrapper {
    height: 100%;

    &.mobile {
      .container {
        .main {
          max-width: 368px;
          width: 98%;
        }
      }
    }

    .container {
      width: 100%;
      min-height: 100%;
      background: #f0f2f5 url(~@/assets/bg.png) no-repeat 50%;
      background-size: 100% 100%;
      padding: 110px 0 144px;
      position: relative;

      .ten_percent {
        left: 10%;
      }

      .six_percent {
        left: 6%;
      }

      .four_percent {
        left: 4%;
      }

      .container_box {
        position: absolute;
        top: 20%;
      }

      a {
        text-decoration: none;
      }

      .top {
        text-align: center;

        .header {
          height: 54px;
          line-height: 54px;

          // .badge {
          //   position: absolute;
          //   display: inline-block;
          //   line-height: 1;
          //   vertical-align: middle;
          //   margin-left: -12px;
          //   margin-top: -10px;
          //   opacity: 0.8;
          // }

          .logo {
            vertical-align: top;
            border-style: none;
            height: 100%;
          }

          .title {
            position: relative;
            height: 100%;
            font-size: 0.6625rem
              /* 53/80 */
            ;
            font-family: Adobe Heiti Std;
            font-weight: normal;
            color: #FFFFFF;
            line-height: 54px;
            letter-spacing: 3px;
          }
        }

        .desc {
          color: rgba(0, 0, 0, 0.45);
          margin-top: 0.4rem
            /* 35/80 */
          ;
          margin-bottom: 1.125rem
            /* 90/80 */
          ;
          display: flex;
          justify-content: center;
          align-items: center;

          .left-desc {
            width: 1.45rem
              /* 116/80 */
            ;
            height: 1px;
            background: #74AFDF;
            margin-right: 0.0625rem
              /* 5/80 */
            ;
          }

          .center-desc {
            height: 0.3625rem
              /* 29/80 */
            ;
            font-size: 0.3875rem
              /* 31/80 */
            ;
            font-family: Adobe Heiti Std;
            font-weight: normal;
            font-style: italic;
            color: #86C8FF;
            line-height: 0.35rem
              /* 28/80 */
            ;
            letter-spacing: 0.025rem
              /* 2/80 */
            ;
          }

          .right-desc {
            width: 1.45rem
              /* 116/80 */
            ;
            height: 0.0125rem
              /* 1/80 */
            ;
            background: #74AFDF;
            margin-left: 0.0625rem
              /* 5/80 */
            ;
          }
        }
      }

      .main {
        min-width: 467px;
        width: 467px;
        margin: 0 auto;
      }

      .footer {
        position: absolute;
        width: 100%;
        bottom: 0;
        padding: 0 16px;
        margin: 48px 0 24px;
        text-align: center;

        .links {
          margin-bottom: 8px;
          font-size: 14px;

          a {
            color: rgba(0, 0, 0, 0.45);
            transition: all 0.3s;

            &:not(:last-child) {
              margin-right: 40px;
            }
          }
        }

        .copyright {
          color: rgba(255, 255, 255, 0.45);
          font-size: 14px;
        }
      }
    }
  }
</style>
<style scoped>
  @media (min-width: 1440px) and (max-width: 1620px) {
    .header {
      height: 54px !important;
      line-height: 44px;
    }

    .logo {
      height: 100%;
    }

    .title {
      font-size: 0.6625rem
        /* 53/80 */
      ;
    }

    .logoBottom {
      width: 88%;
    }

    .desc {
      margin-bottom: 50px !important;
    }
  }

  @media (max-width: 1380px) {
    .header {
      height: 54px !important;
      line-height: 44px;
    }

    .logo {
      height: 100%;
    }

    .logoBottom {
      width: 80%;
    }

    .desc {
      margin-bottom: 34px !important;
      margin-top: 22px !important;
    }
  }
</style>