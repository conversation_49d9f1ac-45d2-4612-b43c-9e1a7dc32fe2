<template>
  <j-modal :title="title" :width="width" :centered="true" :visible="visible" :destroyOnClose="true" switchFullscreen
    cancelText="关闭" @cancel="handleCancel" @ok="handleOk">
    <a-spin :spinning="confirmLoading">
      <div class='plan' v-html='content'></div>
    </a-spin>
  </j-modal>
</template>
<script>
  export default {
    name: 'openPreview',
    props: {},
    data() {
      return {
        form: this.$form.createForm(this),
        title: '预览模版',
        width: '1000px',
        disableSubmit: false,
        visible: false,
        confirmLoading: false,
        formItemLayout: {
          labelCol: {
            xs: {
              span: 24,
            },
            sm: {
              span: 3,
            },
          },
          wrapperCol: {
            xs: {
              span: 24,
            },
            sm: {
              span: 20,
            },
          },
        },
        labelCol: {
          lg: {
            span: 6
          },
          md: {
            span: 5
          },
          sm: {
            span: 24
          },
          xs: {
            span: 24
          },
        },
        wrapperCol: {
          lg: {
            span: 17
          },
          md: {
            span: 16
          },
          sm: {
            span: 24
          },
          xs: {
            span: 24
          },
        },
        content: '',
      }
    },
    created() {},
    methods: {
      show(record) {
        this.visible = true
        this.content = record.projectReportText
      },
      close() {
        this.visible = false
      },
      handleCancel() {
        this.close()
      },
      handleOk() {
        this.close()
      },
    },
  }
</script>
<style scoped lang="less">
  .plan {
    background: #F7F7F7;
    font-size: 14px;
    padding: 16px;
    border-radius: 2px;
    white-space: normal;
    word-break: break-all;
    overflow-x: auto;
  }
</style>