<template>
  <div>
    <!-- 抽屉 -->
    <a-drawer title="字典列表" :width="screenWidth" @close="onClose" :visible="visible">
      <!-- 抽屉内容的border -->
      <div
        :style="{
          padding: '15px 10px',
          border: '1px solid #e9e9e9',
          background: '#fff',
        }"
      >
        <div class="table-page-search-wrapper">
          <a-form layout="inline" :form="form" @keyup.enter.native="searchQuery">
            <a-row :gutter="24">
              <a-col :md="12" :sm="24">
                <a-form-item label="名称" :labelCol="{span: 8}" :wrapperCol="{span: 15}">
                  <a-input placeholder="请输入"
                           :maxLength="maxLength"
                           v-model="queryParam.itemText"
                           :allowClear='true'
                           autocomplete='off'/>
                </a-form-item>
              </a-col>
              <!-- <a-col :md="12" :sm="24">
                <a-form-item label="状态" :labelCol="{span: 8}" :wrapperCol="{span: 15}">
                  <a-select placeholder="请选择"
                            v-model="queryParam.status"
                            :getPopupContainer="(node) => node.parentNode"
                            :allowClear='true'
                  >
                    <a-select-option value="1">正常</a-select-option>
                    <a-select-option value="0">禁用</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col> -->
              <a-col :span='12'>
                <span style="float: center" class="table-page-search-submitButtons">
                  <a-button type="primary" @click="searchQuery">搜索</a-button>
                  <a-button @click="searchReset" style="margin-left: 10px">重置</a-button>
                </span>
              </a-col>
            </a-row>
            <a-row>
              <a-col :md="2" :sm="24" class="table-operator">
                <a-button style="margin-bottom: 10px" @click="handleAdd">新增</a-button>
              </a-col>
            </a-row>
          </a-form>
        </div>
        <div>
          <a-table
            ref="table"
            rowKey="id"
            size="middle"
            :columns="columns"
            :dataSource="dataSource"
            :scroll='dataSource.length>0?{x:"max-content"}:{}'
            :pagination="ipagination"
            :loading="loading"
            @change="handleTableChange"
            :rowClassName="getRowClassname"
          >
            <template slot="tooltip" slot-scope="text">
              <a-tooltip placement="top" :title="text" trigger="hover">
                <div class="tooltip">{{ text }}</div>
              </a-tooltip>
            </template>
            <span slot="action" slot-scope="text, record" class="caozuo">
              <a @click="handleEdit(record)">编辑</a>
              <a-divider type="vertical" />
              <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                <a>删除</a>
              </a-popconfirm>
            </span>
            <!-- <template slot="status" slot-scope="text">
              <span v-if="text == '1'">正常</span>
              <span v-if="text == '0'">禁用</span>
            </template> -->
          </a-table>
        </div>
      </div>
    </a-drawer>
    <dict-item-modal ref="modalForm" @ok="modalFormOk"></dict-item-modal>
    <!-- 字典数据 -->
  </div>
</template>


<script>
import pick from 'lodash.pick'
import { filterObj } from '@/utils/util'
import DictItemModal from './modules/ConfigureDictItemModal'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'

export default {
  name: 'ConfigureDictItemList',
  mixins: [JeecgListMixin],
  components: { DictItemModal },
  data() {
    return {
      maxLength:50,
      columns: [
        {
          title: '名称',
          dataIndex: 'itemText',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 50px;max-width:250px'
            return { style: cellStyle }
          },
        },
        {
          title: '数据值',
          dataIndex: 'itemValue',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 50px;max-width:250px'
            return { style: cellStyle }
          },
        },
        {
          title: '描述',
          dataIndex: 'description',
          scopedSlots: { customRender: 'tooltip' },
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 50px;max-width:200px'
            return {
              style: cellStyle
            }
          }
        },
        // {
        //   title: '状态',
        //   dataIndex: 'status',
        //   scopedSlots: { customRender: 'status' },
        //   customCell: () => {
        //     let cellStyle = 'text-align: center;min-width: 50px;max-width:300px'
        //     return { style: cellStyle }
        //   },
        // },
        {
          title: '操作',
          dataIndex: 'action',
          fixed: 'right',
          width: 100,
          scopedSlots: { customRender: 'action' },
        },
      ],
      queryParam: {
        dictId: '',
        dictName: '',
        itemText: '',
        delFlag: '1',
        status: [],
      },
      title: '操作',
      visible: false,
      screenWidth: "800px",
      model: {},
      dictId: '',
      status: 1,
      form: this.$form.createForm(this),
      validatorRules: {
        itemText: { rules: [{ required: true, message: '请输入名称!' }] },
        itemValue: { rules: [{ required: true, message: '请输入数据值!' }] },
      },
      url: {
        list: '/configure/dictItem/list',
        delete: '/configure/dictItem/delete',
        deleteBatch: '/configure/dictItem/deleteBatch',
      },
    }
  },
  methods: {
    add(dictId) {
      this.dictId = dictId
      this.edit({})
    },
    edit(record) {
      if (record.id) {
        this.dictId = record.id
      }
      this.queryParam = {}
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.model.dictId = this.dictId
      this.model.status = this.status
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(pick(this.model, 'itemText', 'itemValue'))
      })
      // 当其它模块调用该模块时,调用此方法加载字典数据
      this.loadData()
    },

    getQueryParams() {
      var param = Object.assign({}, this.queryParam)
      param.dictId = this.dictId
      param.field = this.getQueryField()
      param.pageNo = this.ipagination.current
      param.pageSize = this.ipagination.pageSize
      if (this.superQueryParams) {
        param['superQueryParams'] = encodeURI(this.superQueryParams)
        param['superQueryMatchType'] = this.superQueryMatchType
      }
      return filterObj(param)
    },

    // 添加字典数据
    handleAdd() {
      this.$refs.modalForm.add(this.dictId)
      this.$refs.modalForm.title = '新增'
    },
    showDrawer() {
      this.visible = true
    },
    onClose() {
      this.visible = false
      this.form.resetFields()
      this.ipagination.current = 1
      this.dataSource = []
    },
    //增加样式方法返回值,设置禁用背景颜色
    getRowClassname(record) {
      if (record.status == 0) {
        return 'data-rule-invalid'
      }
    }
  },
}
</script>
<style lang="less" scoped>
/deep/ .data-rule-invalid {
  background: #f4f4f4;
  color: #bababa;
}
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
@media (max-width: 800px) {
  ::v-deep .ant-drawer-content-wrapper {
    max-width: 100vw;
    margin: 0;
  }
}
</style>