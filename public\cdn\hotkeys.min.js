/*! hotkeys-js v3.13.3 | MIT © 2023 kenny wong <<EMAIL>> https://jaywcjlove.github.io/hotkeys-js */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).hotkeys=t()}(this,function(){"use strict";var e="undefined"!=typeof navigator&&0<navigator.userAgent.toLowerCase().indexOf("firefox");function y(e,t,n,o){e.addEventListener?e.addEventListener(t,n,o):e.attachEvent&&e.attachEvent("on".concat(t),()=>{n(window.event)})}function u(t,e){var n=e.slice(0,e.length-1);for(let e=0;e<n.length;e++)n[e]=t[n[e].toLowerCase()];return n}function h(e){var t=(e=(e="string"!=typeof e?"":e).replace(/\s/g,"")).split(",");let n=t.lastIndexOf("");for(;0<=n;)t[n-1]+=",",t.splice(n,1),n=t.lastIndexOf("");return t}const o={backspace:8,"\u232b":8,tab:9,clear:12,enter:13,"\u21a9":13,return:13,esc:27,escape:27,space:32,left:37,up:38,right:39,down:40,del:46,delete:46,ins:45,insert:45,home:36,end:35,pageup:33,pagedown:34,capslock:20,num_0:96,num_1:97,num_2:98,num_3:99,num_4:100,num_5:101,num_6:102,num_7:103,num_8:104,num_9:105,num_multiply:106,num_add:107,num_enter:108,num_subtract:109,num_decimal:110,num_divide:111,"\u21ea":20,",":188,".":190,"/":191,"`":192,"-":e?173:189,"=":e?61:187,";":e?59:186,"'":222,"[":219,"]":221,"\\":220},m={"\u21e7":16,shift:16,"\u2325":18,alt:18,option:18,"\u2303":17,ctrl:17,control:17,"\u2318":91,cmd:91,command:91},p={16:"shiftKey",18:"altKey",17:"ctrlKey",91:"metaKey",shiftKey:16,ctrlKey:17,altKey:18,metaKey:91},g={16:!1,18:!1,17:!1,91:!1},k={};for(let e=1;e<20;e++)o["f".concat(e)]=111+e;let w=[],v=!1,t="all";const O=[],K=e=>o[e.toLowerCase()]||m[e.toLowerCase()]||e.toUpperCase().charCodeAt(0);function i(e){t=e||"all"}function d(){return t||"all"}function b(n){if(void 0===n)Object.keys(k).forEach(e=>delete k[e]);else if(Array.isArray(n))n.forEach(e=>{e.key&&s(e)});else if("object"==typeof n)n.key&&s(n);else if("string"==typeof n){for(var o=arguments.length,r=Array(1<o?o-1:0),i=1;i<o;i++)r[i-1]=arguments[i];let[e,t]=r;"function"==typeof e&&(t=e,e=""),s({key:n,scope:e,method:t,splitKey:"+"})}}const s=e=>{let{key:t,scope:r,method:i,splitKey:s="+"}=e;h(t).forEach(e=>{var e=e.split(s),t=e.length,n=e[t-1],n="*"===n?"*":K(n);if(k[n]){r=r||d();const o=1<t?u(m,e):[];k[n]=k[n].filter(e=>{return!((!i||e.method===i)&&e.scope===r&&function(e,t){var n=e.length<t.length?t:e,o=e.length<t.length?e:t;let r=!0;for(let e=0;e<n.length;e++)~o.indexOf(n[e])||(r=!1);return r}(e.mods,o))})}})};function x(t,n,o,e){if(n.element===e){let e;if(n.scope===o||"all"===n.scope){e=0<n.mods.length;for(const r in g)Object.prototype.hasOwnProperty.call(g,r)&&(!g[r]&&~n.mods.indexOf(+r)||g[r]&&!~n.mods.indexOf(+r))&&(e=!1);(0!==n.mods.length||g[16]||g[18]||g[17]||g[91])&&!e&&"*"!==n.shortcut||(n.keys=[],n.keys=n.keys.concat(w),!1===n.method(t,n)&&(t.preventDefault?t.preventDefault():t.returnValue=!1,t.stopPropagation&&t.stopPropagation(),t.cancelBubble)&&(t.cancelBubble=!0))}}}function C(n,t){var o=k["*"];let r=n.keyCode||n.which||n.charCode;if(E.filter.call(this,n)){if(93!==r&&224!==r||(r=91),~w.indexOf(r)||229===r||w.push(r),["ctrlKey","altKey","shiftKey","metaKey"].forEach(e=>{var t=p[e];n[e]&&!~w.indexOf(t)?w.push(t):!n[e]&&~w.indexOf(t)?w.splice(w.indexOf(t),1):"metaKey"!==e||!n[e]||3!==w.length||n.ctrlKey||n.shiftKey||n.altKey||(w=w.slice(w.indexOf(t)))}),r in g){g[r]=!0;for(const e in m)m[e]===r&&(E[e]=!0);if(!o)return}for(const a in g)Object.prototype.hasOwnProperty.call(g,a)&&(g[a]=n[p[a]]);n.getModifierState&&(!n.altKey||n.ctrlKey)&&n.getModifierState("AltGraph")&&(~w.indexOf(17)||w.push(17),~w.indexOf(18)||w.push(18),g[17]=!0,g[18]=!0);var i=d();if(o)for(let e=0;e<o.length;e++)o[e].scope===i&&("keydown"===n.type&&o[e].keydown||"keyup"===n.type&&o[e].keyup)&&x(n,o[e],i,t);if(r in k)for(let e=0;e<k[r].length;e++)if(("keydown"===n.type&&k[r][e].keydown||"keyup"===n.type&&k[r][e].keyup)&&k[r][e].key){var s=k[r][e],l=s["splitKey"],c=s.key.split(l),f=[];for(let e=0;e<c.length;e++)f.push(K(c[e]));f.sort().join("")===w.sort().join("")&&x(n,s,i,t)}}}function E(e,t,n){w=[];var o=h(e);let r=[],i="all",s=document,l=0,c=!1,f=!0,a="+",p=!1,d=!1;for(void 0===n&&"function"==typeof t&&(n=t),"[object Object]"===Object.prototype.toString.call(t)&&(t.scope&&(i=t.scope),t.element&&(s=t.element),t.keyup&&(c=t.keyup),void 0!==t.keydown&&(f=t.keydown),void 0!==t.capture&&(p=t.capture),"string"==typeof t.splitKey&&(a=t.splitKey),!0===t.single)&&(d=!0),"string"==typeof t&&(i=t),d&&b(e,i);l<o.length;l++)e=o[l].split(a),r=[],1<e.length&&(r=u(m,e)),(e="*"===(e=e[e.length-1])?"*":K(e))in k||(k[e]=[]),k[e].push({keyup:c,keydown:f,scope:i,mods:r,shortcut:o[l],method:n,key:o[l],splitKey:a,element:s});void 0!==s&&(t=s,!~O.indexOf(t))&&window&&(O.push(s),y(s,"keydown",e=>{C(e,s)},p),v||(v=!0,y(window,"focus",()=>{w=[]},p)),y(s,"keyup",t=>{C(t,s);{let e=t.keyCode||t.which||t.charCode;var n=w.indexOf(e);if(n<0||w.splice(n,1),t.key&&"meta"==t.key.toLowerCase()&&w.splice(0,w.length),(e=93!==e&&224!==e?e:91)in g){g[e]=!1;for(const o in m)m[o]===e&&(E[o]=!1)}}},p))}var n={getPressedKeyString:function(){return w.map(e=>{return n=e,Object.keys(o).find(e=>o[e]===n)||(t=e,Object.keys(m).find(e=>m[e]===t))||String.fromCharCode(e);var t,n})},setScope:i,getScope:d,deleteScope:function(e,t){var n;let o;e=e||d();for(const r in k)if(Object.prototype.hasOwnProperty.call(k,r))for(n=k[r],o=0;o<n.length;)n[o].scope===e?n.splice(o,1):o++;d()===e&&i(t||"all")},getPressedKeyCodes:function(){return w.slice(0)},getAllKeyCodes:function(){const r=[];return Object.keys(k).forEach(e=>{k[e].forEach(e=>{var{key:e,scope:t,mods:n,shortcut:o}=e;r.push({scope:t,shortcut:o,mods:n,keys:e.split("+").map(e=>K(e))})})}),r},isPressed:function(e){return"string"==typeof e&&(e=K(e)),!!~w.indexOf(e)},filter:function(e){var t=(e=e.target||e.srcElement)["tagName"];let n=!e.isContentEditable&&("INPUT"!==t&&"TEXTAREA"!==t&&"SELECT"!==t||e.readOnly)?!0:!1;return n},trigger:function(t){let n=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"all";Object.keys(k).forEach(e=>{k[e].filter(e=>e.scope===n&&e.shortcut===t).forEach(e=>{e&&e.method&&e.method()})})},unbind:b,keyMap:o,modifier:m,modifierMap:p};for(const r in n)Object.prototype.hasOwnProperty.call(n,r)&&(E[r]=n[r]);if("undefined"!=typeof window){const l=window.hotkeys;E.noConflict=e=>(e&&window.hotkeys===E&&(window.hotkeys=l),E),window.hotkeys=E}return E});