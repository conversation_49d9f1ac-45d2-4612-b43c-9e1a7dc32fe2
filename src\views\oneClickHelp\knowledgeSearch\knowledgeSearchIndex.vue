<template>
  <div class="container">
    <card-frame :title="'知识搜索'" showHeadBgImg :showFooter="true" :showTitleImg="true">
      <a-row :gutter="24" style="height: 100%" slot="bodySlot">
        <a-col style='width: 100%; height: 100%; display: flex; flex-direction: column'>
          <div style='width: 100%; flex: auto'>
            <a-col :xs="{ span: 22, offset: 1 }" :sm="{ span: 18, offset: 3 }" :md="{ span: 16, offset: 4 }" :lg="{ span: 13, offset: 6 }"
                   style="height: 100%;padding:0;">
              <div class="inner">
                <div class="header-box">
                  <div class="title">{{ knowledgeSearchName }}</div>
                </div>
                <div class="input-box">
                  <a-input-group compact style="height: 100%">
                    <a-input v-model="value" :maxLength="50" placeholder="请输入" class="a-input"
                      @pressEnter="goSearchResultPage(0)" style="border-radius: 4px 0 0 4px;" />
                    <a-button type="primary" class="btn" @click="goSearchResultPage(0)">
                      <a-icon type="search" style="font-size: 22px"></a-icon>
                      搜索
                    </a-button>
                  </a-input-group>
                </div>
              </div>
              <div class="out">
                <!-- 热门知识 -->
                <hot-knowledge :theme="'theme2'" :showIcon="true" @goDetail="goKnowledgeDetail" style="flex:1;margin-right: 24px;overflow: hidden">
                  <img slot="prefixIcon" style="height: 18px; width: 18px;margin-right:10px" src="/oneClickHelp/localDeviceInfo/head.png">
                  <div slot="top">
                    TOP
                  </div>
                </hot-knowledge>
                <!-- 搜索历史 -->
                <history-knowledge ref="history" :theme="'theme2'" :historyList="historyList" @goSearchResultPage="goSearchResultPage"  @reloadKSearchHistory="getSearchHistoryList" style="flex: 1;overflow: hidden">
                  <img slot="prefixIcon" style="height: 18px; width: 18px;margin-right:10px" src="/oneClickHelp/localDeviceInfo/head.png">
                </history-knowledge>
              </div>
            </a-col>
          </div>
        </a-col>
      </a-row>
    </card-frame>
  </div>
</template>

<script>
import cardFrame from '@/views/oneClickHelp/localDeviceInfo/modules/CardFrame.vue'
import hotKnowledge from '@/views/opmg/knowledgeManagement/knowledgeSearch/modules/hotKnowledge'
import historyKnowledge from '@views/opmg/knowledgeManagement/knowledgeSearch/modules/historyKnowledge.vue'
import { knowledgeSearchIndexMixins } from '@/views/opmg/knowledgeManagement/knowledgeSearch/modules/knowledgeSearchIndexMixins'
export default {
  name: 'knowledgeSearchIndex',
  mixins: [knowledgeSearchIndexMixins],
  components: {
    cardFrame,
    hotKnowledge,
    historyKnowledge
  },
  data() {
    return {
      value: '',
      defaultName: '信创联合运维知识库', // 设置默认值
    }
  },
  created() {
    // 根据配置字典获取知识搜索大标题（客户端）
    this.getKnowledgeSearchName('client')
  },
  methods: {},
}
</script>
<style scoped lang="less">
.over {
  /* 溢出用省略号*/
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
}

.container {
  height: 100%;
  width: 100%;

  .inner {
    width: 100%;
    margin-top: 18%;

    .header-box {
      display: flex;
      justify-content: center;
      align-items: center;

      .title {
        font-family: BDZYJT--GB1-0;
        /*38/64*/
        font-size: 40px;
        color: #ffffff;
        letter-spacing: 4px;
        text-align: center;
        font-weight: 400;
        white-space: nowrap;
      }

      .plane-img {
        /*114/64*/
        width: 1.78125rem;
        /*75/64*/
        height: 1.171875rem;

        position: relative;
        top: -22px;
        margin-left: 5px;
      }
    }

    .input-box {
      width: 100%;
      margin-top: 60px;
      height: 62px;
      position: relative;

      .a-input {
        width: calc(100% - 154px);
        height: 100%;
        text-indent: 2em;
        border: 1px solid #e7e7e7;
        box-shadow: 2px 2px 6px 1px rgba(22, 27, 33, 0.16);
        border-radius: 4px;
        font-size: 18px;
      }

      .btn {
        /*156/64*/
        width: 154px;
        height: 100%;
        background: #409eff;
        border-color: #409eff;
        font-family: PingFangSC-Regular;
        font-size: 24px;
        color: #ffffff;
        letter-spacing: 0.08px;
        font-weight: 400;
      }

      .line {
        position: absolute;
        top: 0;
        bottom: 0;
        margin: auto;
        left: 24px;
        width: 3px;
        height: 25px;
        background-color: #364d80;
        border-radius: 3px;
        z-index: 1;
      }
    }

    .keyword-box {
      min-height: 120px;

      .hot {
        width: 100%;
        font-size: 0.25rem;
        color: #f50909;
        letter-spacing: 0;
        text-align: left;
        padding-top: 30px;
      }

      .keyword {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        margin-top: 13px;

        .tag {
          background: #f1f1f1;
          border-radius: 4px;
          /*14/64*/
          font-size: 0.21875rem;
          color: rgba(0, 0, 0, 0.65);
          letter-spacing: 0.52px;
          font-weight: 400;
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          height: 30px;
          padding: 0 10px;
          margin-right: 10px;
          margin-bottom: 5px;
          cursor: pointer;
        }
      }
    }
  }

  .out {
    width: 100%;
    padding-top: 5%;
    padding-bottom: 5%;
    display: flex;
    justify-content: space-between;

    ::-webkit-scrollbar {
      display: none;
    }

    .history {
      width: 50%;
      height: 100%;

      .title {
        height: 34px;
        line-height: 34px;
        margin-left: 10px;
        font-family: BDZYJT--GB1-0;
        font-size: 16px;
        color: #ffffff;
        letter-spacing: 0;
        font-weight: 400;
      }

      .wrapper {
        height: 100%;
        overflow-y: auto;

        .item {
          font-size: 14px;
          color: #ffffff;
          padding: 5px 16px 5px 0;
          white-space: nowrap;
          overflow: hidden;
          cursor: pointer;
          display: flex;
          justify-content: space-between;

          .time {
            font-size: 13px;
            color: #9195a3;
          }

          .name {
            overflow: hidden;
            width: 48%;
            flex: auto;
          }

          &:hover {
            color: #66ffff;
          }
        }
      }
    }
  }
}
</style>