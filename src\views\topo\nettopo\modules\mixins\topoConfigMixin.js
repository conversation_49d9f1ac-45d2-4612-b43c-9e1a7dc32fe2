import { nodeProperties, edgeProperties } from '../models/topoTranfer'

const tryToJSON = (val) => {
  try {
    return JSON.parse(val)
  } catch (error) {
    return val
  }
}
export default {
  data() {
    return {}
  },
  watch: {
    // 拓扑图背景图改变
    'globalGridAttr.topoConfig.bgimg'(path) {
      if (this.globalGridAttr.topoConfig.bgType === '2') {
        this.graph.clearBackground()
        this.graph.drawBackground({
          image: path.includes('http') ? path : window._CONFIG['staticDomainURL'] + '/' + path,
          size: '100% 100%'
        })
      }
    },
    'globalGridAttr.topoConfig.bgColor'() {
      this.setTopoBg()
    },
    //拓扑图背景类型改变
    'globalGridAttr.topoConfig.bgType'() {
      this.setTopoBg()
    },
    //拓扑线条状态改变
    'globalGridAttr.topoConfig.edgeStatus'() {
      let edges = this.graph.getEdges()
      edges.forEach(edge => {
        this.setEdgeStatus(edge)
      })
    }
  },
  methods: {
    //应用拓扑图 保存检测
    checkTopoBeforeTopo() {
      if (this.topoInfo.topoType === '1') {
        let nodes = this.graph.getNodes()
        let edges = this.graph.getEdges()
        let nodeIndex = nodes.findIndex(el => el.data.nodeType === 'app_auto_node')
        let edgeIndex = edges.findIndex(el => el.data.nodeType === 'app_auto_edge')
        return nodeIndex !== -1 || edgeIndex !== -1
      } else {
        return false
      }
    },
    //将保存数据转换成节点连线
    dataTransferCell(nodeList, edgeList) {
      let nodes = []
      let edges = []
      //节点转换
      nodeList.forEach(el => {
        let keys = Object.keys(nodeProperties)
        let tem = {}
        keys.forEach(k => {
          if (el[k] !== null) {
            tem[nodeProperties[k]] = el[k] && typeof el[k] === 'string' ? tryToJSON(el[k]) : el[k]
            if (nodeProperties[k] === 'attrs') {
              let attr = tem[nodeProperties[k]]
              if (attr.image && attr.image['xlink:href']) {
                let rurl = this.apiUrl || window.location.origin
                let xlink = tem[nodeProperties[k]]['image']['xlink:href']
                if (xlink.startsWith('http://') || xlink.startsWith('https://')) {
                  const regx = /(http(s?)(:)\/\/((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)|localhost)(:\d{1,5})?)(\/insight-api)?/
                  tem[nodeProperties[k]]['image']['xlink:href'] = xlink.replace(regx, rurl)
                } else {
                  tem[nodeProperties[k]]['image']['xlink:href'] = rurl + '/' + xlink
                }
              }
              if(attr.label && this.labelColor){
                attr.label.fill= this.labelColor
              }
            }
            if (k === 'nodeDeviceInfo') {
              // 这里把nodeData和nodeDeviceInfo的数据融合一下，因为旧版本设备信息是用Json字符串形式保存在nodeData中，设备信息变更之后，数据不是最新的数据。
              // 现在改成了从nodeDeviceInfo获取，重新查了一遍,获取的是最新的设备信息
              tem['data'] = Object.assign(tem['data'], tem[nodeProperties[k]])
            }
          }
        })
        nodes.push(tem)
      })
      // console.log("nodes",nodes)
      //连线转换
      edgeList.forEach(el => {
        let keys = Object.keys(edgeProperties)
        let tem = {}
        keys.forEach(k => {
          if (el[k] !== null) {
            tem[edgeProperties[k]] = el[k] && typeof el[k] === 'string' ? tryToJSON(el[k]) : el[k]
          }
        })
        edges.push(tem)
      })
      // console.log("edges",edges)
      // 优化后的边过滤逻辑
      const nodeIdSet = new Set(nodes.map(node => node.id))
      const filterEdges = edges.filter(edge =>
        edge.target.cell &&
        edge.source.cell &&
        nodeIdSet.has(edge.target.cell) &&
        nodeIdSet.has(edge.source.cell)
      )
      // console.log("filterEdges",filterEdges)
      this.graph.fromJSON({nodes,edges:filterEdges}, { silent: false })
      //此处处理群组节点的展开收起，子节点隐藏
      this.graph.getNodes().forEach(el => {
        if (el.data.nodeType === "group") {
          let groupNodes = el.getChildren()
          if (el.data.collapsed) {
            groupNodes.forEach(node => {
              node.hide()
            })
          }
        }
      })
    },
    //将节点连线属性转换成要保存的数据
    cellTransferData(cells) {
      let obj = {
        nodeList: [],
        edgeList: []
      }
      cells.forEach(el => {
        if (el.shape !== 'edge') {
          obj.nodeList.push(this.setPropertice(nodeProperties, el))
        } else {
          obj.edgeList.push(this.setPropertice(edgeProperties, el))
        }
      })

      return obj
    },
    setPropertice(properties, cell) {
      let temData = { topoId: this.topoInfo.id }
      let keys = Object.keys(properties)
      keys.forEach(k => {
        if (k === 'nodeDeviceInfo') {
          temData[k] = null
        } else if (properties[k] === 'bus_data') {
          //分值保存必须为数值
          if (k === 'nodeScore') {
            temData[k] = cell.data ? (cell.data[k] || 0) : 0
          } else {
            temData[k] = cell.data ? (cell.data[k] || null) : null
          }

        } else if (properties[k] && cell[properties[k]]) {
          if (typeof cell[properties[k]] === 'object') {
            temData[k] = JSON.stringify(cell[properties[k]])
          } else {
            temData[k] = cell[properties[k]]
          }
        } else {
          temData[k] = null
        }
      })
      return temData
    },
    //保存拓扑图时 同步拓扑图配置信息
    saveTopoConfig() {
      this.topoInfo.topoName = this.globalGridAttr.topoName
      this.topoInfo.showType = this.globalGridAttr.showType
      this.topoInfo.imgUrlList = this.globalGridAttr.bgImgList
      this.topoInfo.deptId = this.globalGridAttr.deptId || ""
      this.topoInfo.topoConfig = JSON.stringify(this.globalGridAttr.topoConfig)
    },
    initConfig() {
      this.setTopoConfig()
      this.setTopoInfo()
      this.setTopoBg()
    },
    //设置拓扑信息
    setTopoInfo() {
      this.globalGridAttr.topoName = this.topoInfo.topoName
      this.globalGridAttr.showType = this.topoInfo.showType
      this.globalGridAttr.topoType = this.topoInfo.topoType
      this.globalGridAttr.deptId = this.topoInfo.deptId
    },
    // 设置拓扑图配置
    setTopoConfig() {
      //节点连线的配置
      let data = this.topoInfo.topoConfig ? JSON.parse(this.topoInfo.topoConfig) : null
      this.globalGridAttr.setTopoConfig(data)
      //背景图列表
      this.globalGridAttr.bgImgList = this.topoInfo.imgUrlList
    },
    //设置拓扑背景
    setTopoBg() {
      let topoConfig = this.globalGridAttr.topoConfig
      //背景图片
      if (topoConfig.bgType === '2' && !!topoConfig.bgimg && !this.topoBgByTheme) {
        // console.log("背景路径", topoConfig.bgimg)
        this.graph.clearBackground()
        this.graph.drawBackground({
          image: topoConfig.bgimg.includes('http') ? topoConfig.bgimg : window._CONFIG['staticDomainURL'] + '/' + topoConfig.bgimg,
          size: '100% 100%'
        })
      } //设置背景色

      if (topoConfig.bgType === '1' && !!topoConfig.bgColor && !this.topoBgByTheme) {
        this.graph.clearBackground()
        this.graph.drawBackground({
          color: topoConfig.bgColor
        })
      }
      if (topoConfig.bgType === '0') {
        this.graph.clearBackground()
        this.graph.drawBackground({
          color: 'transparent'
        })
      }
    },
    // 大量节点测试
    testLargeNumberNode() {
      // console.log("将数据转换成节点 === ", nodeList)

      // nodeList = []
      // edgeList =[]
      // for(let i=0,len=1000;i<len;i++){
      //     let test = {
      //         "id": "test_"+i,
      //         "createBy": null,
      //         "createTime": "2023-07-07 15:15:35",
      //         "updateBy": null,
      //         "updateTime": null,
      //         "sysOrgCode": null,
      //         "topoId": "1677200308641587201",
      //         "nodePosition": JSON.stringify({x:120*(i%20),y:120*Math.floor(((i+1)/20+1))}),
      //         "nodeSize": "{\"width\":80,\"height\":80}",
      //         "nodeAttrs": "{\"body\":{\"fill\":\"green\"},\"image\":{\"xlink:href\":\"sys/common/static/default/server.png\",\"opacity\":1},\"label\":{\"text\":\"交换机1\"}}",
      //         "nodeVisible": null,
      //         "nodeShape": "switch-node",
      //         "portMarkup": "[{\"tagName\":\"circle\",\"selector\":\"portBody\"}]",
      //         "nodePorts": "{\"groups\":{\"top\":{\"position\":\"top\",\"attrs\":{\"circle\":{\"r\":3,\"magnet\":true,\"stroke\":\"#5F95FF\",\"strokeWidth\":1,\"fill\":\"#fff\",\"style\":{\"visibility\":\"visible\"}}}},\"right\":{\"position\":\"right\",\"attrs\":{\"circle\":{\"r\":3,\"magnet\":true,\"stroke\":\"#5F95FF\",\"strokeWidth\":1,\"fill\":\"#fff\",\"style\":{\"visibility\":\"visible\"}}}},\"left\":{\"position\":\"left\",\"attrs\":{\"circle\":{\"r\":3,\"magnet\":true,\"stroke\":\"#5F95FF\",\"strokeWidth\":1,\"fill\":\"#fff\",\"style\":{\"visibility\":\"visible\"}}}},\"bottom\":{\"position\":\"bottom\",\"attrs\":{\"circle\":{\"r\":3,\"magnet\":true,\"stroke\":\"#5F95FF\",\"strokeWidth\":1,\"fill\":\"#fff\",\"style\":{\"visibility\":\"visible\"}}}}},\"items\":[]}",
      //         "portLabelMarkup": null,
      //         "nodeData": "{\"deviceId\":\"1676906722976870402\",\"deviceCode\":\"Switch1\",\"deviceName\":\"交换机1\",\"deviceType\":\"\",\"devicePanel\":\"\",\"nodeType\":\"\",\"status\":0}",
      //         "nodeZIndex": 5,
      //         "nodeChildren": null,
      //         "nodeParent": null,
      //         "nodeView": null,
      //         "markUp": null,
      //         "nodeTools": null,
      //         "nodeAngle": null,
      //         "nodeConfig": null,
      //         "deviceCode": "Switch1",
      //         "deviceType": null,
      //         "devicePanel": null
      //     }

      //     nodeList.push(test)
      // }
    }

  }
}