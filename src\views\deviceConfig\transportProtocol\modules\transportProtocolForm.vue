<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container>
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-item label="协议名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['name', validatorRules.name]" :allowClear="true" autocomplete="off"
                placeholder="请输入协议名称"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="协议标识" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['code', validatorRules.code]" :allowClear="true" autocomplete="off"
                placeholder="请输入协议标识"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="协议类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-radio-group v-decorator="['type', { initialValue: '0' }]">
                <a-radio value="0">推模式</a-radio>
                <a-radio value="1">拉模式</a-radio>
                <a-radio value="2">其他</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="描述" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-textarea v-decorator="['description', validatorRules.description]" :allowClear="true"
                autocomplete="off" placeholder="请输入描述" :auto-size="{ minRows: 2, maxRows: 4 }" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>
<script>
  import {
    httpAction,
    getAction
  } from '@/api/manage'
  import pick from 'lodash.pick'
  export default {
    name: 'transportProtocolForm',
    data() {
      return {
        form: this.$form.createForm(this),
        model: {},
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          },
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          },
        },
        confirmLoading: false,
        validatorRules: {
          name: {
            rules: [{
              required: true,
              message: '请输入协议名称!'
            },
            { min: 2, max: 255, message: '协议名称长度应在 2-255 之间' }],
          },
          code: {
            rules: [{
              required: true,
              message: '请输入协议标识!'
            },
            { min: 2, max: 255, message: '协议标识长度应在 2-255 之间' }]
          },
          description: {
            rules: [{ required: false,min:0,max:255, message:'描述信息字符长度不能超过255个字符',trigger: 'change'}]
          },
        },
        url: {
          add: '/device/productTransfer/add',
          edit: '/device/productTransfer/edit',
          queryById: '/device/productTransfer/queryById',
        },
      }
    },
    created() {},
    methods: {
      add() {
        this.edit({})
      },
      edit(record) {
        this.form.resetFields()
        this.model = Object.assign({}, record)
        this.visible = true
        this.$nextTick(() => {
          this.form.setFieldsValue(
            pick(
              this.model,
              'name',
              'code',
              'type',
              'description',
            )
          )
        })
      },
      submitForm() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.model.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'put'
            }
            let formData = Object.assign(this.model, values)
            httpAction(httpurl, formData, method)
              .then((res) => {
                if (res.success) {
                  that.$message.success(res.message)
                  that.$emit('ok')
                } else {
                  that.$message.warning(res.message)
                }
                that.confirmLoading = false
              })
              .catch((err) => {
                that.$message.warning(err.message)
                that.confirmLoading = false
              })
          }
        })
      }
    }
  }
</script>