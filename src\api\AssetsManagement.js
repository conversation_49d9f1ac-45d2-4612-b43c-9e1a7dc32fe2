import { getAction, deleteAction, putAction, postAction } from '@/api/manage'

const addRecord = params => postAction('/ledger/ledgerManage/add', params)
const editRecord = params => putAction('/ledger/ledgerManage/edit', params)
const queryById = params => getAction('/ledger/ledgerManage/queryById', params)

//变更记录查询
const queryChangeById = params => getAction('/ledger/ledgerChange/queryById', params)
const queryChangeLedgerInfo = params => getAction('/ledger/ledgerChange/ledgerInfo', params)
//变更记录修改
const ledgerChangeEdit = params => putAction('/ledger/ledgerChange/edit', params)
//状态修改
const ledgerEditStatus = params => putAction('/ledger/ledgerManage/editStatus', params)



//月报添加
const ledgerMonthAdd = params => postAction('/ledger/ledgerMonth/add', params)
//月报修改
const ledgerMonthEdit = params => putAction('ledger/ledgerMonth/edit', params)


//运维知识库添加
const knowledgeAdd = params => postAction('/question/knowledge/add', params)
//运维知识库修改
const knowledgeedit = params => putAction('/question/knowledge/edit', params)






export {
    addRecord,
    editRecord,
    queryChangeLedgerInfo,
    ledgerChangeEdit,
    ledgerEditStatus,
    queryById,
    queryChangeById,
    ledgerMonthAdd,
    ledgerMonthEdit,
    knowledgeAdd,
    knowledgeedit
}