<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    :destroyOnClose="true"
    :centered="true"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭"
  >
    <a-form id="components-form-demo-validate-other" :form="form" v-bind="formItemLayout">
      <!-- <a-form-item label="端口类型">
        <a-radio-group v-decorator="['bandwidth']">
          <a-radio v-for="item in bandwidths" :key="item.key" :value="item.value"> {{item.text}} </a-radio>
        </a-radio-group>
      </a-form-item> -->
      <a-form-item label="行列">
        <div style="display: flex">
          <a-form-item>
            <a-input-number v-decorator="['row', { initialValue: 1 }]" :min="1" :max="1000"  @change="rowChange"/>
            <span class="ant-form-text"> 行 </span>
          </a-form-item>
          <a-form-item>
            <a-input-number v-decorator="['column', { initialValue: 1 }]" :min="1" :max="1000" />
            <span class="ant-form-text"> 列 </span>
          </a-form-item>
        </div>
      </a-form-item>
      <a-form-item label="初始值">
        <a-input-number v-decorator="['startIndex', { initialValue: 1 }]" :min="1" :max="1000" />
      </a-form-item>
      <a-form-item label="排列开始位置">
        <a-radio-group v-decorator="['interfacePos', { initialValue: 'upper' }]" button-style="solid">
          <a-radio-button value="upper"> 上 </a-radio-button>
          <a-radio-button value="below"> 下 </a-radio-button>
        </a-radio-group>
      </a-form-item>
      <a-form-item label="排列顺序">
        <a-radio-group v-decorator="['interfaceOrder', { initialValue: '123' }]" button-style="solid">
          <a-radio-button value="123"> 123 </a-radio-button>
          <a-radio-button value="321"> 321 </a-radio-button>
          <a-radio-button value="135"> 135 </a-radio-button>
          <a-radio-button value="531"> 531 </a-radio-button>
        </a-radio-group>
      </a-form-item>
      <a-form-item label="方向">
        <a-radio-group v-decorator="['direction', { initialValue: 'up' }]" button-style="solid">
          <a-radio-button value="up"> 上 </a-radio-button>
          <a-radio-button value="down">下 </a-radio-button>
          <a-radio-button value="up_down" v-if="doubleRow"> 上下 </a-radio-button>
          <a-radio-button value="down_up" v-if="doubleRow"> 下上 </a-radio-button>
        </a-radio-group>
      </a-form-item>
        <a-form-item label="图标大小">
        <a-input-number v-decorator="['eleSize']" :min="8" :max="60" />
      </a-form-item>
    </a-form>
  </j-modal>
</template>

<script>
import { bandwidths } from '../models/global'
export default {
  name: 'ExtendForm',
  data() {
    return {
      title: '端口排列设置',
      width: '600px',
      visible: false,
      formItemLayout: {
        labelCol: { span: 6 },
        wrapperCol: { span: 14 },
      },
      doubleRow:false,
      bandwidths,
    }
  },
  beforeCreate() {
    this.form = this.$form.createForm(this, { name: 'validate_other' })
  },
  methods: {
    show(eleSize) {
      this.visible = true
      this.$nextTick(()=>{
         // this.form.setFieldsValue({bandwidth:"1000M"})
         this.form.setFieldsValue({eleSize:eleSize})
      })
     
    },
    close() {
      this.doubleRow = false;
      this.visible = false
    },
    handleOk() {
      let values = this.form.getFieldsValue()
      this.$emit('batchCreateInterface',values)
      this.close()
    },
    handleCancel() {
      this.$emit('close')
      this.close()
    },
    rowChange(e){
      this.form.setFieldsValue({direction:'up'});
      this.doubleRow = e===2;
    }
  },
}
</script>
<style lang="less" scoped>
@import '~@assets/less/normalModal.less';
</style>