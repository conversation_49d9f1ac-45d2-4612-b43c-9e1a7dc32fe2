<template>
  <div class='zr-national-devices' ref='zrNationalDevices'>
    <zr-bigscreen-title title='设备状态'>
    </zr-bigscreen-title>
    <div class='national-devices-content'>
      <slot>
        <div class='devices-statics'>
          <div class='devices-statics-item' v-for='(item, index) in statics' :key='index'>
            <div class='devices-statics-item-label'>{{ item.label }}</div>
            <div class='devices-statics-item-value' :style='{color:colors[index]}'>{{ item.value }}</div>
          </div>
        </div>
        <vue-seamless-scroll v-if='srollOption.autoPlay' :data='listData' class='scroll-warp'
                             :class-option='srollOption'>
          <ul class='scroll-list'>
            <li class='scroll-list-item' :class='{"scroll-list-odd":index%2!==0}' v-for='(item, index) in listData'
                :key='index'>
              <div class='item-name' :title='item.businessName' :style='{"--status-color":item.color}'>
                {{ item.businessName }}
              </div>
              <div class='item-ip'>{{ item.ip }}</div>
              <div class='item-status' :style='{color:item.color}'>{{ item.statusText }}</div>
            </li>
          </ul>
        </vue-seamless-scroll>
        <div v-else class='scroll-warp'>
          <ul class='scroll-list'>
            <li class='scroll-list-item' :class='{"scroll-list-odd":index%2!==0}' v-for='(item, index) in listData'
                :key='index'>
              <div class='item-name' :title='item.businessName' :style='{"--status-color":item.color}'>
                {{ item.businessName }}
              </div>
              <div class='item-ip'>{{ item.ip }}</div>
              <div class='item-status' :style='{color:item.color}'>{{ item.statusText }}</div>
            </li>
          </ul>
        </div>
      </slot>
    </div>
  </div>
</template>
<script>
import ZrBigscreenTitle from '@views/zrBigscreens/modules/ZrBigscreenTitle.vue'
import vueSeamlessScroll from 'vue-seamless-scroll'
import resizeObserverMixin from '@views/statsCenter/com/resizeObserverMixin'
import { businessStatus } from '@views/zrBigscreens/modules/zrUtil'

export default {
  name: 'ZrNationalDevices',
  components: { ZrBigscreenTitle, vueSeamlessScroll },
  mixins: [resizeObserverMixin],
  data() {
    return {
      listData: [
        {
          'businessName': '数据库',
          'ip': '***************',
          'status': 0
        }, {
          'businessName': '缓存服务',
          'ip': '*************',
          'status': 0
        }, {
          'businessName': '消息队列',
          'ip': '***********',
          'status': 0
        }, {
          'businessName': '负责均衡',
          'ip': '***********',
          'status': 2
        },

      ],
      srollOption: {
        step: 0.5, // 步长
        speed: 100, // 滚动速度
        timer: 3000,// 滚动时间间隔
        autoPlay: false,
        limitMoveNum: 10000,
        singleHeight: 36 ,
      },
      maxNum: 0,
      statics:[
        { label: '设备数量', value: 23},
        { label: '不稳定', value: 4},
        { label: '故障', value: 2}
      ],
      colors:[
        '#55A7F4', // 正常
        '#F4A655', // 异常
        '#F45555'  // 故障
      ]
    }
  },
  created() {
    this.mapList()
  },
  mounted() {
    this.$nextTick(()=>{
      this.setPlayState()
    })

  },
  methods: {
    mapList() {
      this.listData = this.listData.map(el => {
        let bs = businessStatus.find(item => item.value == el.status)
        if (bs) {
          el.color = bs.color
          el.statusText = bs.label
        }
        return el
      })
    },
    // 屏幕变化回调
    resizeObserverCb() {
      this.setPlayState()
    },
    //设置滚动状态
    setPlayState() {
      if (this.$refs.zrNationalDevices && this.listData.length) {
        let bounded = this.$refs.zrNationalDevices.getBoundingClientRect()
        this.maxNum = Math.floor((bounded.height - 38 - 116) / 36)
        if (this.maxNum>0 && this.maxNum < this.listData.length) {
          this.srollOption.limitMoveNum = this.maxNum
          this.srollOption.autoPlay = true
          return
        }
      }
      this.srollOption.autoPlay = false
      this.srollOption.limitMoveNum = this.listData.length + 1
    }
  }
}
</script>


<style scoped lang='less'>
.zr-national-devices {
  height: 100%;

  .national-devices-content {
    height: calc(100% - 38px);
    overflow: hidden;
    padding: 0 15px;
  }
}
.devices-statics{
  padding:30px 0;
  display: flex;
  .devices-statics-item {
    width: calc(100% / 3);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    color:#EDF5FF;
    font-size: 14px;
    .devices-statics-item-label {
      margin-bottom: 10px;
      line-height: 1;
    }
    .devices-statics-item-value {
      font-weight: bold;
      font-size: 32px;
      color: #55A7F4;
      line-height: 1;
    }
  }
}
.scroll-warp {
  height: 100%;
  overflow: hidden;

  .scroll-list {
    width: 100%;
    height: 100%;
    margin: 0px;
    padding: 0px;
  }

  .scroll-list-item {
    display: flex;
    align-items: center;
    color: rgba(237, 245, 255, 0.95);
    font-size: 14px;
    justify-content: space-between;
    height: 36px;

    .item-name {
      width: 40%;
      overflow: hidden;
      position: relative;
      padding-left: 32px; /* 给圆点留出空间 */
      line-height: 1;
      opacity: 0.95;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      &::before {
        content: '';
        position: absolute;
        left: 16px;
        top: 3.5px;
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background: var(--status-color);
      }
    }

    .item-ip {
      width: 40%;
      text-align: left;
      opacity: 0.95;
    }

    .item-status {
      width: 20%;
      text-align: left;
      opacity: 0.95;
    }
  }

  .scroll-list-odd {
    background: rgba(29, 78, 140, 0.25);
  }
}
</style>