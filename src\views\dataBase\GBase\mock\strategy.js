export  const strategyData = [
  {
    key: 1,
    strategyName: '全量备份策略',
    database: 'insight-410',
    strategyType: '1',
    strategyMode: '0',
    strategyCycle: '0 0 0 * * ?',
    immediateExecute: '是',
    strategyStatus: "1",
    strategyPath: '/backup/path1',
    period: '无限期',
    createTime: '2023-04-01 12:00:00',
   
  },
  {
    key: 2,
    strategyName: '增量备份策略',
    database: 'insight-414',
    strategyType: '0',
    strategyMode: '1',
    strategyCycle: '0 0 0 * * ?',
    immediateExecute: '否',
    strategyStatus: "1",
    strategyPath: '/backup/path2',
    period: '30天',
    createTime: '2023-04-02 12:00:00',
   
  },
  {
    key: 10,
    strategyName: '月增量备份策略',
    database: 'apm',
    strategyType: '0',
    strategyMode: '0',
    strategyCycle: '0 0 0 * * ?',
    immediateExecute: '是',
    strategyStatus: "0",
    strategyPath: '/backup/path10',
    period: '无限期',
    createTime: '2023-04-10 12:00:00',
   
  }
];

export  const restoreStrategyData = [
  {
    key: 1,
    strategyName: 'insight-410恢复策略',
    database: 'insight-410',
    strategyType: '1',
    strategyMode: '0',
    strategyCycle: '0 0 0 * * ?',
    immediateExecute: '是',
    strategyStatus: "1",
    strategyPath: '/backup/path1',
    period: '无限期',
    createTime: '2023-04-01 12:00:00',

  },
  // {
  //   key: 2,
  //   strategyName: '增量备份策略',
  //   database: 'insight-414',
  //   strategyType: '0',
  //   strategyMode: '1',
  //   strategyCycle: '0 0 0 * * ?',
  //   immediateExecute: '否',
  //   strategyStatus: "1",
  //   strategyPath: '/backup/path2',
  //   period: '30天',
  //   createTime: '2023-04-02 12:00:00',
  //
  // },
  // {
  //   key: 10,
  //   strategyName: '月增量备份策略',
  //   database: 'apm',
  //   strategyType: '0',
  //   strategyMode: '0',
  //   strategyCycle: '0 0 0 * * ?',
  //   immediateExecute: '是',
  //   strategyStatus: "0",
  //   strategyPath: '/backup/path10',
  //   period: '无限期',
  //   createTime: '2023-04-10 12:00:00',
  //
  // }
];
export  const records = [
  {
    "index": 1,
    "strategyName": "全量备份策略",
    "database": "insight-410",
    "result": 1,
    "content": "执行成功，无错误。",
    "startTime": "2023-04-01 12:00:00",
    "endTime": "2023-04-0112:05:00"
  },
  {
    "index": 2,
    "strategyName": "全量备份策略",
    "database": "insight-410",
    "result": 0,
    "content": "执行失败，数据库连接错误。",
    "startTime": "2023-04-02 13:30:00",
    "endTime": "2023-04-02 13:31:00"
  },
  {
    "index": 3,
    "strategyName": "增量备份策略",
    "database": "insight-414",
    "result": 1,
    "content": "成功更新数据。",
    "startTime": "2023-04-03 09:45:00",
    "endTime": "2023-04-03 09:46:00"
  },
  {
    "index": 4,
    "strategyName": "增量备份策略",
    "database": "insight-414",
    "result": 0,
    "content": "执行超时，请重试。",
    "startTime": "2023-04-04 15:15:00",
    "endTime": "2023-04-04 15:20:00"
  },
  {
    "index": 5,
    "strategyName": "增量备份策略",
    "database": "insight-414",
    "result": 1,
    "content": "数据验证通过，执行成功。",
    "startTime": "2023-04-05 10:00:00",
    "endTime": "2023-04-05 10:02:00"
  },
  {
    "index": 6,
    "strategyName": "增量备份策略",
    "database": "insight-414",
    "result": 0,
    "content": "SQL错误，请检查查询语句。",
    "startTime": "2023-04-06 14:30:00",
    "endTime": "2023-04-06 14:31:00"
  },
  {
    "index": 7,
    "strategyName": "全量备份策略",
    "database": "insight-410",
    "result": 1,
    "content": "备份完成，无错误。",
    "startTime": "2023-04-07 08:00:00",
    "endTime": "2023-04-07 08:30:00"
  },
  {
    "index": 8,
    "strategyName": "insight-410恢复策略",
    "database": "insight-410",
    "result": 0,
    "content": "资源不足，操作失败。",
    "startTime": "2023-04-08 11:15:00",
    "endTime": "2023-04-08 11:16:00"
  },
  {
    "index": 9,
    "strategyName": "insight-410恢复策略",
    "database": "insight-410",
    "result": 1,
    "content": "数据迁移成功。",
    "startTime": "2023-04-09 16:00:00",
    "endTime": "2023-04-09 16:30:00"
  },
  {
    "index": 10,
    "strategyName": "insight-410恢复策略",
    "database": "insight-410",
    "result": 0,
    "content": "权限不足，无法执行。",
    "startTime": "2023-04-10 12:45:00",
    "endTime": "2023-04-10 12:46:00"
  }
]