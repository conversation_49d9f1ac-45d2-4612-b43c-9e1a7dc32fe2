<template>
  <a-row class="row-container">
    <a-col style="height:100%">
     <a-card class="card1">
      <a-col :span="12">
        <section style="font-family: PingFangSC-Medium;font-size: 18px;font-weight: 600;">{{ obj?obj.displayName:"" }} </section>
      </a-col>
      <a-col :span="12">
        <div style="text-align: right;" ><img src="~@/assets/return1.png" alt="" @click="getGo" style="width: 20px;height: 20px;cursor: pointer"></div>
      </a-col>
    </a-card>
     <a-card class="info-card">
      <Tabs ref="productTabs" :obj="obj"></Tabs>
    </a-card>
    </a-col>
  </a-row>
</template>

<script>
  import Tabs from '../productsetting/Tabs'
  export default {
    name: 'ProductEdit',
    components: {
      Tabs
    },
    props:{
      obj:{
        type:Object
      }
    },
    data () {
      return {
        title: '',
        width: 800,
        visible: false,
        disableSubmit: false,
      }
    },
    mounted(){
    },
    methods: {
      getGo(){
        this.$refs.productTabs.defaultActiveKey='1'
       this.$parent.pButton2(0,'');
      },
    }
  }
</script>
<style lang="less" scoped>
.row-container{
  height:100%;
}
.card1{
  height:77px;
}
.info-card{
  margin-top: 16px; 
  height:calc(100% - 93px);
  overflow-y:auto;
  /deep/ .ant-card-body{
    height: 100%;
  }
}
</style>