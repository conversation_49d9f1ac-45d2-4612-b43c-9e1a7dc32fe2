<template>
  <card-frame :title="'消息中心'" showHeadBgImg :showFooter="true" :showTitleImg="true">
    <div class="contentBox" slot="bodySlot">
      <div class="leftList">
        <div class="leftSearch">
          <a-input
            placeholder="请输入标题"
            autocomplete="off"
            :allowClear="true"
            v-model="queryParam.titile"
            @change="searchQuery" :maxLength="50"
          ></a-input>
          <a-popover trigger="click" placement="right" overlayClassName="och-notice-filter">
            <template slot="content">
              <div style="width: 230px">
                <div class="filter-item">
                  <a-input
                    placeholder="请输入发布人"
                    autocomplete="off"
                    :allowClear="true"
                    v-model="queryParam.sender"
                    @change="searchQuery"
                  ></a-input>
                </div>
                <div class="filter-item">
                  <a-select
                    :getPopupContainer="(node) => node.parentNode"
                    style="width: 100%"
                    :allowClear="true"
                    v-model="queryParam.msgCategory"
                    placeholder="请选择消息类型"
                    @change="searchQuery"
                  >
                    <a-select-option :value="'2'" :key="2">系统消息</a-select-option>
                    <a-select-option :value="'1'" :key="1">通知公告</a-select-option>
                  </a-select>
                </div>
                <div class="filter-item">
                  <div style="display: flex; flex-direction: row-reverse">
                    <a-button type="info" size="small" class="cancel-btn" @click="reset">重置</a-button>
                  </div>
                </div>
              </div>
            </template>
            <span class="filter-btn">
              <a-icon type="filter" style="font-size: 18px; color: #d7d7d7" />
            </span>
          </a-popover>
        </div>
        <div class="list-scroll" @scroll="scrollEvent">
          <div class="list-contents">
            <div
              v-for="item in dataSource"
              :key="item.id"
              class="leftBox"
              :class="{ 'left-box-active': infoData && infoData.id === item.id }"
              @click="goData(item)"
            >
              <div class="leftTop">
                <div style="display: flex; align-items: center">
                  <div class="lanDian" v-if="item.readFlag == 0"></div>
                  <div class="huiDian" v-else></div>
                  <div class="leftTitle">
                    {{ item.titile }}
                  </div>
                  <div class="priorityH" v-if="item.priority == 'H'">高</div>
                  <div class="priorityM" v-if="item.priority == 'M'">中</div>
                </div>
                <div class="leftTime">{{ item.sendTime.slice(5, 16) }}</div>
              </div>
              <div class="leftBottom">
                <div class="leftType">消息类型：{{ item.msgCategory == 2 ? '系统消息' : '通知公告' }}</div>
                <div class="leftPeople">发布人：{{ item.sender_dictText }}</div>
              </div>
            </div>
            <div class="more">
              <div class="more-inner">
                <div class="more-line"></div>
                <span class="more-btn" @click="moreClick">
                  {{ isOver ? '没有更多了，点击重新加载' : '更多' }}
                </span>
                <div class="more-line"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="lightXian"></div>
      <div v-if="dataSource.length>0" class="rightBox">
        <div class="rightTitle">
          {{ infoData.titile }}
        </div>
        <div class="rightTime" v-if="infoData.sender_dictText">
          {{ '发布人:' + infoData.sender_dictText + '&nbsp;&nbsp;&nbsp;' + '发布时间:' + infoData.sendTime }}
        </div>
        <div style="border: 1px dashed rgba(17, 74, 163, 0.33); margin-top: 20px"></div>
        <div class="rightContent" v-html="infoData.msgContent"></div>
      </div>
       <!-- 暂无数据 -->
      <div v-if="dataSource.length==0 && loading == false" class="rightBox">
        <empty style="padding-bottom: 8%;"/>
      </div>
    </div>
  </card-frame>
</template>

<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { postAction, putAction, httpAction, getAction, deleteAction } from '@/api/manage'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
import cardFrame from '../oneClickHelp/localDeviceInfo/modules/CardFrame.vue'
import Empty from '@/components/oneClickHelp/Empty.vue'
export default {
  components: {
    cardFrame,
    Empty
  },
  mixins: [JeecgListMixin, YqFormSearchLocation],
  data() {
    return {
      infoData: {},
      queryParam: {
        titile: '',
        msgCategory: undefined,
        sender: '',
      },
      url: {
        list: '/sys/sysAnnouncementSend/getMyAnnouncementSend',
      },
      pages: 0,
      searchTimer: null,
      scrollTimer:null,
    }
  },
  created() {
    // this.loadData()
  },
  computed: {
    isOver() {
      return (
        (this.pages > 0 && this.ipagination.current > this.pages) || (this.pages === 0 && this.dataSource.length === 0)
      )
    },
  },
  mounted() {},
  methods: {
    loadData(arg) {
      if (!this.url.list) {
        this.$message.error('请设置url.list属性!')
        return
      }
      if (this.pages > 0 && this.isOver) {
        this.$message.warning('没有更多了！')
        return
      }
      if (arg === 1) {
        this.ipagination.current = 1
      }
      var params = this.getQueryParams() //查询条件
      this.loading = true
      getAction(this.url.list, params).then((res) => {
        if (res.success) {
          this.dataSource = this.dataSource.concat(res.result.records || res.result)
          if (this.ipagination.current === 1 && this.dataSource[0]) {
            this.goData(this.dataSource[0])
          }

          this.ipagination.total = res.result.total
          this.pages = res.result.pages
          if (this.ipagination.current <= this.pages) {
            this.ipagination.current += 1
          }
        }
        if (res.code === 510) {
          this.$message.warning(res.message)
        }
        this.loading = false
      })
    },
    searchQuery() {
      if (this.searchTimer) {
        clearTimeout(this.searchTimer)
        this.searchTimer = null
      }
      this.searchTimer = setTimeout(() => {
        this.dataSource = []
        this.infoData = {}
        this.pages = 0
        this.loadData(1)
      }, 500)
    },
    reset() {
      this.queryParam.sender = ''
      this.queryParam.msgCategory = undefined
      this.queryParam.titile = ''
      this.searchQuery()
    },
    goData(record) {
      this.infoData = record
      if (record.readFlag === '0') {
        putAction('sys/sysAnnouncementSend/editByAnntIdAndUserId', { anntId: record.anntId }).then((res) => {
          if (res.success) {
            let idx = this.dataSource.findIndex((el) => el.id === record.id)
            this.dataSource[idx].readFlag = '1'
          }
        })
      }
    },
    scrollEvent(e) {
      //scrollHeight  scrollTop clientHeight
      if(this.scrollTimer){
        return;
      }
      this.scrollTimer = setTimeout(() => {
        let t = e.target
        let dis = t.scrollHeight - t.clientHeight
        // console.log('监听到滚动事件 === ', dis, t.scrollTop)
        if (!this.loading && dis <= t.scrollTop + 100) {
          this.loadData()
        }
        clearTimeout(this.scrollTimer)
        this.scrollTimer = null;
      }, 100)

      
    },
    moreClick() {
      if (this.isOver) {
        this.pages = 0
        this.dataSource = []
        this.loadData(1)
      } else {
        this.loadData()
      }
    },
  },
}
</script>
<style lang="less">
</style>
<style lang="less" scoped>
@import '~@assets/less/onclickStyle.less';

::-webkit-scrollbar {
  display: none;
}

.contentBox {
  width: 100%;
  height: 100%;
  display: flex;

  .leftList {
    height: 100%;
    width: 332px;
    padding-top: 10px;
    overflow: auto;

    .leftSearch {
      width: 100%;
      padding: 1px 0 1px 20px;
      display: flex;
      margin-bottom: 15px;
      .filter-btn {
        margin-left: 12px;
        margin-right: 12px;
        margin-top: 8px;
      }
    }
    .list-scroll {
      height: calc(100% - 55px);
      overflow: auto;
    }
    .leftBox {
      height: 85px;
      width: 100%;
      padding: 16px 20px 16px 0;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      border: 0.5px solid rgba(29, 44, 68, 0.7);
      user-select: none;
      cursor: pointer;
      position: relative;
      .leftTop {
        display: flex;
        justify-content: space-between;
        padding-left: 20px;

        .lanDian {
          height: 5px;
          width: 5px;
          border-radius: 2px;
          background: #409eff;
        }

        .huiDian {
          height: 5px;
          width: 5px;
          border-radius: 2px;
          background: rgba(250, 250, 250, 0);
        }

        .priorityH {
          height: 16px;
          width: 16px;
          font-family: PingFangSC-Regular;
          font-size: 12px;
          color: #ffffff;
          letter-spacing: 0.69px;
          font-weight: 400;
          background-color: #dd3c3c;
          margin-left: 10px;
          display: flex;
          justify-content: center;
          align-items: center;
        }

        .priorityM {
          height: 16px;
          width: 16px;
          font-family: PingFangSC-Regular;
          font-size: 12px;
          color: #ffffff;
          letter-spacing: 0.69px;
          font-weight: 400;
          background-color: #eb9b45;
          margin-left: 10px;
          display: flex;
          justify-content: center;
          align-items: center;
        }

        .leftTitle {
          font-family: SourceHanSansCN-Regular;
          font-size: 16px;
          color: #ffffff;
          letter-spacing: 0.91px;
          font-weight: 400;
          margin-left: 8px;
        }

        .leftTime {
          font-family: PingFangSC-Regular;
          font-size: 12px;
          color: rgba(255, 255, 255, 0.43);
          letter-spacing: 0.69px;
          font-weight: 400;
        }
      }

      .leftBottom {
        display: flex;
        padding-left: 33px;

        .leftType {
          font-family: PingFangSC-Regular;
          font-size: 12px;
          color: #99a4aa;
          letter-spacing: 0.69px;
          font-weight: 400;
        }

        .leftPeople {
          font-family: PingFangSC-Regular;
          font-size: 12px;
          color: #99a4aa;
          letter-spacing: 0.69px;
          font-weight: 400;
          margin-left: 20px;
        }
      }
    }
    .more {
      height: 85px;
      width: 100%;
      padding: 16px 20px 16px 0;
      display: flex;
      align-items: center;
      justify-content: center;

      .more-inner {
        display: flex;
        align-items: center;
        .more-btn {
          margin: 12px;
          cursor: pointer;
          color: rgba(255, 255, 255, 0.7);
        }
        .more-line {
          border: 1px dashed rgba(17, 74, 163, 0.33);
          width: 60px;
        }
      }
    }
    .left-box-active {
      background: rgba(21, 85, 175, 0.24);
      &::before {
        content: '';
        position: absolute;
        left: 0px;
        height: 100%;
        width: 5px;
        background-color: #5081f9;
      }
    }

    .leftBox:hover {
      background: rgba(21, 85, 175, 0.24);
    }

    .leftBox:visited {
      // background: rgba(21, 85, 175, 0.24);
      background: pink;
    }
  }

  .lightXian {
    height: 100%;
    width: 1px;
    background: linear-gradient(to bottom, #0c1a32 0%, rgb(20, 100, 219, 0.8) 50%, #0b1931 100%);
  }

  .rightBox {
    height: 100%;
    width: calc(100% - 333px);
    color: #ffffff;
    padding-top: 30px;

    .rightTitle {
      font-family: PingFangSC-Semibold;
      font-size: 24px;
      color: #ffffff;
      letter-spacing: 1.37px;
      font-weight: 600;
      margin-left: 60px;
    }

    .rightTime {
      font-family: PingFangSC-Regular;
      font-size: 14px;
      color: #99a4aa;
      letter-spacing: 0.57px;
      font-weight: 400;
      margin-left: 60px;
      margin-top: 10px;
    }

    .rightContent {
      font-family: PingFangSC-Regular;
      font-size: 14px;
      color: #ffffff;
      letter-spacing: 0.57px;
      line-height: 32px;
      font-weight: 400;
      margin-left: 60px;
      margin-top: 34px;
    }
  }
}
.filter-item {
  margin-top: 12px;
}
/deep/ .ant-input {
  border: 1px solid rgba(215, 215, 215, 0.7);
  border-radius: 4px;
}
/deep/ .ant-select-selection {
  border: 1px solid rgba(215, 215, 215, 0.7);
  border-radius: 4px;
}
/deep/ .ant-select-selection:hover {
  border-color: #144e90;
}
/deep/ .ant-select-dropdown {
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
}
.cancel-btn {
  // background: #144e90;
  border-radius: 4px;
  // border: none;
  color: #fff;
}
</style>