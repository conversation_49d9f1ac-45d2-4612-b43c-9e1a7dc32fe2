import FlowGraph from '../../../graph/index'

export function nodeOpt (id, globalGridAttr) {
  let curCel = null
  if (id) {
    const { graph } = FlowGraph
    const cell = graph.getCellById(id)
    if (!cell || !cell.isNode()) {
      return
    }
    curCel = cell
    globalGridAttr.nodeLabel = cell.attr('label/text')//节点名称
    globalGridAttr.nodeX = cell.position().x//x位置
    globalGridAttr.nodeY = cell.position().y
    globalGridAttr.nodeWidth = cell.size().width//节点宽
    globalGridAttr.nodeHeight = cell.size().height//节点高
    globalGridAttr.nodeImg = cell.attr('image/xlink:href')//节点图片
    globalGridAttr.nodeStrokeWidth = cell.attr('body/strokeWidth')
    globalGridAttr.nodeFill = cell.attr('body/fill')
    // globalGridAttr.nodeFontSize = cell.attr('text/fontSize')
    // globalGridAttr.nodeColor = cell.attr('text/fill') 
    globalGridAttr.nodeUsers = cell.attr('approve/users')
    globalGridAttr.nodeIp = cell.getData().ip
    globalGridAttr.productModel = cell.getData().productModel
    globalGridAttr.productName = cell.getData().productName
    globalGridAttr.manufacturer = cell.getData().manufacturer
    globalGridAttr.imgList = cell.getData().imgList
    globalGridAttr.portList = cell.getPorts()
  }
  return curCel
}
