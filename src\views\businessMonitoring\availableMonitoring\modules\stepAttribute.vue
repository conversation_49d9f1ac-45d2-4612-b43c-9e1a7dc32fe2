<template>
  <div>
    <a-form-model ref='form' :model='model' :rules='validatorRules' v-bind='formItemLayout'>
      <a-row>
        <a-col :span='21' v-if='attribute.name.visible' key='attr-name'>
          <a-form-model-item label='名称' prop='name'>
            <a-input
              v-model='model.name'
              :allow-clear='true'
              autocomplete='off'
              placeholder='请输入名称'
              @change='changeInputValue($event,"name")'
            />
          </a-form-model-item>
        </a-col>
        <a-col :span='24' v-if='attribute.interaction.visible' key='attr-interaction'>
          <a-row>
            <a-col :span='21'>
              <a-form-model-item label='交互方式' prop='interaction'>
                <a-select
                  style='calc(100% - 25px)'
                  v-model='model.interaction'
                  :allow-clear='true'
                  autocomplete='off'
                  placeholder='请选择交互方式'
                  @change='changeInteraction($event)'
                >
                  <a-select-option v-for='item in interactionInfoList' :key='"inter"+item.id'
                                   :label='item.componentName' :value='item.componentCode'>
                    {{ item.componentName }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span='3' v-if='comDescription!==""' class='inter-des'>
              <a-popover placement='top'>
                <template slot='content' style='margin-left: 20px'>
                  <p>{{ comDescription }}</p>
                </template>
                <a-icon style='fontSize:20px;margin-left: 12px' theme='twoTone' type='question-circle' />
              </a-popover>
            </a-col>
          </a-row>
        </a-col>
        <a-col :span='21' v-if='attribute.locator.visible' key='attr-locator'>
          <a-form-model-item label='定位器' prop='locator'>
            <a-select
              v-model='model.locator'
              :allow-clear='true'
              autocomplete='off'
              placeholder='请选择定位器！'
              @change='changeSelectedValue($event,"locator")'
            >
              <a-select-option v-for='item in locatorList' :key='"loc"+item.value' :label='item.title'
                               :value='item.value'>
                {{ item.title }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span='21' v-if='attribute.value.visible' key='attr-value'>
          <a-form-model-item label='目标值' prop='value'>
            <a-textarea
              v-model='model.value'
              :autoSize="{ minRows: 1, maxRows: 8 }"
              :allow-clear='true'
              autocomplete='off'
              placeholder='请输入目标值'
              @change='changeInputValue($event,"value")' />
          </a-form-model-item>
        </a-col>
        <a-col :span='21' v-if='attribute.sendValue.visible' key='attr-sendValue'>
          <a-form-model-item label='键入值' prop='sendValue'>
            <a-textarea
              v-model='model.sendValue'
              :autoSize="{ minRows: 1, maxRows: 8 }"
              :allow-clear='true'
              autocomplete='off'
              placeholder='请输入键入值'
              @change='changeInputValue($event,"sendValue")' />
          </a-form-model-item>
        </a-col>
        <a-col :span='21' v-if='attribute.infoType.visible' key='attr-infoType'>
          <a-form-model-item label='信息类型' prop='infoType'>
            <a-select
              v-model='model.infoType'
              :allow-clear='true'
              autocomplete='off'
              placeholder='请选择信息类型'
              @change='changeSelectedValue($event,"infoType")'>
              <a-select-option v-for='item in infoTypeList' :key='"mes"+item.value' :label='item.title'
                               :value='item.value'>
                {{ item.title }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span='21' v-if='attribute.ruleConditions.visible' key='attr-ruleConditions'>
          <a-form-model-item label='判断符号' prop='ruleConditions'>
            <a-select
              v-model='model.ruleConditions'
              :allow-clear='true'
              autocomplete='off'
              placeholder='请选择判断符号'
              @change='changeSelectedValue($event,"ruleConditions")'>
              <a-select-option v-for='item in ruleConditionsList' :key='"sym"+item.value' :label='item.title'
                               :value='item.value'>
                {{ item.title }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span='21' v-if='attribute.expectedValue.visible' key='attr-expectedValue'>
          <a-form-model-item label='预期值' prop='expectedValue'>
            <a-input
              v-model='model.expectedValue'
              :allow-clear='true'
              autocomplete='off'
              placeholder='请输入预期值'
              @change='changeInputValue($event,"expectedValue")' />
          </a-form-model-item>
        </a-col>
        <a-col :span='21' v-if='attribute.timeOutInSeconds.visible' key='attr-timeOutInSeconds'>
          <a-form-model-item label='超时时间' prop='timeOutInSeconds'>
            <div>
              <a-input-number
                style='width:50%'
                v-model='model.timeOutInSeconds'
                :min='0'
                :precision='0'
                placeholder='请输入超时时间'
                @change='changeInputNumberValue($event,"timeOutInSeconds")' />
              <span style='margin-left: 10px'>秒(s)</span>
            </div>
          </a-form-model-item>
        </a-col>
        <a-col :span='21' v-if='attribute.message.visible' key='attr-message'>
          <a-form-model-item label='描述' prop='message'>
            <a-textarea
              v-model='model.message'
              :auto-size='{ minRows: 2, maxRows: 6 }'
              :allowClear='true'
              autocomplete='off'
              placeholder='请输入描述信息'
              @change='changeInputValue($event,"message")' />
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
  </div>
</template>
<script>
import { ajaxGetDictItems, getDictItemsFromCache } from '@api/api'
import { getAction } from '@api/manage'

export default {
  name: 'stepAttribute',
  props: {
    interactionInfoList: [],
    locatorList: [],
    infoTypeList: [],
    ruleConditionsList: []
  },
  data() {
    return {
      formItemLayout: {
        labelCol: {
          lg: { span: 24 },
          xl: { span: 6 }
        },
        wrapperCol: {
          lg: { span: 24 },
          xl: { span: 18 }
        }
      },
      comDescription: '',
      attribute: {
        //步骤名称
        name: {
          visible: true,
          validation: false,
          label: '名称'
        },
        //交互方式
        interaction: {
          visible: true,
          validation: false,
          label: '交互方式'
        },
        //定位器
        locator: {
          visible: false,
          validation: false,
          label: '定位器'
        },
        //目标值
        value: {
          visible: false,
          validation: false,
          label: '目标值'
        },
        //键入值
        sendValue: {
          visible: false,
          validation: false,
          label: '键入值'
        },
        //获取信息类型
        infoType: {
          visible: false,
          validation: false,
          label: '获取信息类型'
        },
        //规则判断条件
        ruleConditions: {
          visible: false,
          validation: false,
          label: '规则判断条件'
        },
        //预期值
        expectedValue: {
          visible: false,
          validation: false,
          label: '预期值'
        },
        //超时时间（秒）
        timeOutInSeconds: {
          visible: false,
          validation: true,
          label: '超时时间'
        },
        //提示信息
        message: {
          visible: true,
          validation: true,
          label: '描述'
        }
      },
      validatorRules: {
        name: [
          { required: true, min: 1, max: 100, validator: this.validateRequiredInput }
        ],
        interaction: [
          { required: true, validator: this.validateRequiredSelected }
        ],
        locator: [
          { required: false, validator: this.validateRequiredSelected }
        ],
        value: [
          { required: false, min: 1, validator: this.validateRequiredInput }
        ],
        sendValue: [
          { required: false, min: 1, validator: this.validateRequiredInput }
        ],
        infoType: [
          { required: false, validator: this.validateRequiredSelected }
        ],
        ruleConditions: [
          { required: false, validator: this.validateRequiredSelected }
        ],
        expectedValue: [
          { required: false, min: 1, validator: this.validateRequiredInput }
        ],
        timeOutInSeconds: [
          { required: false, validator: this.validateRequiredInput }
        ],
        message: [
          { required: false, max: 500, validator: this.validateRequiredInput }
        ]
      },
      model: {
        timeOutInSeconds: 10
      }
    }
  },
  methods: {
    /*父组件切换步骤调用该方法：
    1、重置属性可见性、必填性、校验状态
    2、获取数据并回显数据
    3、触发表单校验函数，更新属性校验状态*/
    switchAttribute(attribute) {
      let param = {
        timeOutInSeconds: 10
      }
      this.model = Object.assign(param, attribute)
      this.resetReuired(['name', 'interaction'])
      this.resetVisible(['name', 'interaction', 'message'])
      this.resetValidateStatus(['timeOutInSeconds', 'message'])

      this.setVisibleAndRequired()

      this.$nextTick(() => {
        this.setValidateStatus()
      })
    },
    /*设置字段必填性和可见性*/
    setVisibleAndRequired() {
      this.comDescription = ''
      if (this.model.interaction) {
        const targetObject = this.interactionInfoList.find(obj => obj.componentCode === this.model.interaction)
        this.comDescription = targetObject.description
        let curConfig = JSON.parse(targetObject.attributesConfig)
        for (let config of curConfig) {
          const { key, is_show, required } = config
          this.attribute[key].visible = is_show
          for (let rule of this.validatorRules[key]) {
            if (rule.required != undefined) {
              rule.required = required
            }
          }
        }
      }
    },
    /*重置校验状态后，触发表单校验方法，更新属性校验状态*/
    setValidateStatus() {
      this.$refs.form.validate((err, values) => {
        /* for (let key in this.attribute){
           if (this.attribute[key].visible){
             let keys=Object.keys(values)
             if(keys.length>0){
              let k= keys.find(ke=>ke==key)
               this.attribute[key].validation=k?false:true
             }
           }
         }*/
      })
    },

    /*测试使用：父组件主动触发校验
    validateFun() {
      this.$refs.form.validate((err, values) => {
        let newModel={}
        for (let key in this.attribute){
          if (this.attribute[key].visible){
            newModel[key]=this.model[key]
          }
        }
        newModel.validation=err
        this.$emit('ok',newModel)
      })
    },*/
    /*改变文本内容事件*/
    changeInputValue(value, key) {
      let vaObj = {}
      vaObj[key] = value.target.value
      this.$emit('ok', vaObj)
    },
    /*改变数字文本内容事件*/
    changeInputNumberValue(value, key) {
      let vaObj = {}
      vaObj[key] = value
      this.$emit('ok', vaObj)
    },
    /*改变交互方式事件*/
    changeSelectedValue(value, key) {
      let vaObj = {}
      vaObj[key] = value ? value : undefined
      this.$emit('ok', vaObj)
    },
    /*改变交互方式事件*/
    changeInteraction(value) {
      this.resetReuired(['name', 'interaction'])
      this.resetVisible(['name', 'interaction', 'message'])
      // this.resetValidateStatus(["message"])
      let interactionTxt = ''
      this.comDescription = ''
      if (value) {
        const targetObject = this.interactionInfoList.find(obj => obj.componentCode === value)
        this.comDescription = targetObject.description
        interactionTxt = targetObject.componentName
        let curConfig = JSON.parse(targetObject.attributesConfig)
        for (let config of curConfig) {
          const { key, is_show, required } = config
          this.attribute[key].visible = is_show
          for (let rule of this.validatorRules[key]) {
            if (rule.required != undefined) {
              rule.required = required
            }
          }
        }
      }
      this.$emit('ok', {
        interaction: value ? value : undefined,
        interactionTxt: interactionTxt
      })
    },
    /*重置字段可见性*/
    resetVisible(attrs) {
      for (let attr in this.attribute) {
        let k = attrs.find(key => key === attr)
        this.attribute[attr].visible = k ? true : false
      }
    },
    /*重置字段必填性*/
    resetReuired(attrs) {
      for (let rules in this.validatorRules) {
        let k = attrs.find(key => key === rules)
        for (let rule of this.validatorRules[rules]) {
          if (rule.required !== undefined) {
            rule.required = k ? true : false
          }
        }
      }
    },
    /*重置字段校验状态*/
    resetValidateStatus(attrs) {
      for (let attr in this.attribute) {
        let k = attrs.find(key => key === attr)
        this.attribute[attr].validation = k ? true : false
      }
    },
    /*输入类型组件校验方法*/
    validateRequiredInput(rule, value, callback) {
      let that = this
      let status = false
      let tip = ''
      let { min, max, fullField } = rule
      let { label } = that.attribute[fullField]
      let length = value === null || value === undefined || value.toString().replace(/\s+/g, '') === '' ? 0 : value.toString().length

      if (rule.required) {
        if (length > 0) {
          tip = that.getValidateTip(min, max, length, label)
          status = tip ? false : true

        } else {
          tip = `请输入${label}!`
        }
      } else {
        tip = that.getValidateTip(min, max, length, label)
        status = tip ? false : true
      }
      that.attribute[fullField].validation = status

      if (status) {
        that.sendValidateStatus()
        callback()
      } else {
        that.$emit('ok', { validation: false })
        callback(tip)
      }
    },
    /*选择类型组件校验方法*/
    validateRequiredSelected(rule, value, callback) {
      let that = this
      let status = false
      let tip = ''
      let { fullField } = rule
      let { label } = that.attribute[fullField]
      if (rule.required) {
        if (value) {
          status = true
        } else {
          tip = `请选择${label}!`
        }
      } else {
        status = true
      }
      that.attribute[fullField].validation = status

      if (status) {
        that.sendValidateStatus()
        callback()
      } else {
        that.$emit('ok', { validation: false })
        callback(tip)
      }
    },
    /*输入类型组件校验提示方法*/
    getValidateTip() {
      if (arguments[0] && arguments[1] && (arguments[0] > arguments[2] || arguments[1] < arguments[2])) {
        return `${arguments[3]}长度应在${arguments[0]}-${arguments[1]}之间！`
      } else if (arguments[0] && arguments[0] > arguments[2]) {
        return `${arguments[3]}长度应大于${arguments[0]}`
      } else if (arguments[1] && arguments[1] < arguments[2]) {
        return `${arguments[3]}长度应小于${arguments[1]}`
      } else {
        return ''
      }
    },
    /*向父组件传表单校验状态：
    1、都为true校验成功，
    2、有一项为false，校验失败*/
    sendValidateStatus() {
      let that = this
      let pass = true
      for (let key in that.attribute) {
        if (that.attribute[key].visible && !that.attribute[key].validation) {
          pass = false
          break
        }
      }
      that.$emit('ok', { validation: pass })
    }
  }
}
</script>

<style scoped lang='less'>
.inter-des{
  height: 40px;line-height: 40px
}
@media (max-width: 1200px){
 .inter-des{
   margin-top: 29px;
 }
}
</style>