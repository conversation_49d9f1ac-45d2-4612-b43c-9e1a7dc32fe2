<template>
  <div>
    <a-row :gutter="10" justify="center" type="flex"  style="margin-bottom: 10px">
      <a-col :lg="12" :sm="24" :xs="24">
        <my-todo></my-todo>
      </a-col>
      <a-col :lg="12" :sm="24" :xs="24" >
        <my-pending-review></my-pending-review>
      </a-col>
    </a-row>
    <a-row :gutter="10" justify="center" type="flex" style="margin-bottom: 10px">
      <a-col :lg="12" :sm="24" :xs="24" >
        <my-application></my-application>
      </a-col>
      <a-col :lg="12" :sm="24" :xs="24" >
        <my-draft></my-draft>
      </a-col>
    </a-row>
  </div>
</template>

<script>
  import MyTodo from '@views/customPages/components/MyTodo.vue'
  import MyPendingReview from '@views/customPages/components/MyPendingReview.vue'
  import MyApplication from '@views/customPages/components/MyApplication.vue'
  import MyDraft from '@views/customPages/components/MyDraft.vue'
  export default {
    name: 'Statistics',
    components: {
      MyDraft,
      MyApplication,
      MyPendingReview,
      MyTodo,
    },
  }
</script>

<style lang='less' scoped>
  @import '~@assets/less/common.less';
  ::v-deep .ant-table-small>.ant-table-content>.ant-table-body {
    margin: 0px !important;
  }

  ::v-deep .ant-card-body {
    padding: 12px !important;
  }
</style>