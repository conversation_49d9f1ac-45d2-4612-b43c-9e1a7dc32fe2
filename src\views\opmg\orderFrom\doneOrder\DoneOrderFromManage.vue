<template>
  <div style="height:100%">
      <component :is="pageName" :data="data"/>
  </div>
</template>
<script>
  import DoneOrderFromList from './DoneOrderFromList'
  import DoneOrderFromDetails from './modules/DoneOrderFromDetails'
  export default {
    name: "DoneOrderFromManage",
    data() {
      return {
        isActive: 0,
        data:{}
      };
    },
    components: {
      DoneOrderFromList,
      DoneOrderFromDetails
    },
    created(){
      this.pButton1(0);
    },
    //使用计算属性
    computed: {
      pageName() {
        switch (this.isActive) {
          case 0:
            return "DoneOrderFromList";
            break;

          default:
            return "DoneOrderFromDetails";
            break;
        }
      }
    },
    methods: {
      pButton1(index) {
        this.isActive = index;
      },
      pButton2(index,item) {
        this.isActive = index;
        this.data = item;
      }
    }
  }
</script>