<template>
  <div style="height:100%">
    <component style="height:100%" :is="pageName" :data="data" />
  </div>
</template>
<script>
  import serviceList from './serviceList'
  import serviceDetail from './modules/serviceDetail'
  export default {
    name: "serviceMange",
    data() {
      return {
        isActive: 0,
        data: {},
      };
    },
    components: {
      serviceList,
      serviceDetail
    },
    created() {
      this.pButton1(0);
    },
    //使用计算属性
    computed: {
      pageName() {
        switch (this.isActive) {
          case 0:
            return "serviceList";
          default:
            return "serviceDetail";
        }
      }
    },
    methods: {
      pButton1(index) {
        this.isActive = index;
      },
      async pButton2(index, item) {
        this.isActive = index;
        this.data = item
      }
    }
  }
</script>