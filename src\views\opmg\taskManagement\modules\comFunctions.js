import { ajaxGetDictItems } from '@api/api'

/**
 * 获取字典数据
 * @param {String} dictCode - 字典名称
 */
export function getDictData(dictCode) {
  return new Promise((resolve, reject) => {
    ajaxGetDictItems(dictCode, null).then((res) => {
      if (res.success) {
        resolve(res.result)
      } else {
        this.$message.warning(res.message)
        reject([])
      }
    }).catch((err) => {
      this.$message.error(err.message)
      reject([])
    })
  })
}

/**
 * 这里预制的颜色和软件下发任务状态字典值是一次对应的，如果字典改了，这里也需要改
 * */
const preColors = [
  '#6C757D'
  , '#007BFF'
  , '#FFC107'
  , '#28A745'
  , '#DC3545']

/**
 * 根据任务类型，匹配对应的颜色，用于设置背景色
 * @param ｛Array｝ list - 任务类型数据
 * */
export function setStatusColor(list) {
  let resList = []
  for (let i = 0; i < list.length; i++) {
    let m = {
      key: list[i].value,
      color: preColors[i]
    }
    resList.push(m)
  }
  return resList
}

/**
 * 这里预制了终端任务执行状态图标和颜色
 * */
export const terStatusColorAndIcon = [
  { color: '#d8d8d8', icon: 'notStarted',img:'',title:'未开始' },
  { color: '#007BFF', icon: 'executing',img:'',title:'执行中' },
  { color: '#FFC107', icon: '' ,img:''},
  { color: '#DC3545', icon: 'execFailure',img:'',title:'执行失败'},
  { color: '#28A745', icon: 'execSuccess',img:'' ,title:'执行成功'},
  { color: '#d8d8d8',icon:'', img: require('@assets/taskDistribution/mismatched.png') ,title:'升级包不匹配'},
 ]

export function getOneStatusColor(taskStatus, dictCode) {
  getDictData(dictCode).then((res) => {
    if (res.length > 0) {
      let color = ''
      let m = res.filter((item, index) => {
        if (taskStatus == item.value) {
          color = preColors[index]
        }
      })
      return color
    }
  })
}

// 动态生成颜色值的函数
function generateRandomColor() {
  return `#${Math.floor(Math.random() * 16777215).toString(16).padStart(6, '0')}` // 随机16进制颜色
}