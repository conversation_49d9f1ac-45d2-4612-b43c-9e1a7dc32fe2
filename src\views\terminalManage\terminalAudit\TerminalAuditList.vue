<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <!-- 查询区域 -->
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <div class="table-page-search-wrapper-style">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="单位">
                  <a-tree-select
                    v-model="searchKey"
                    tree-node-filter-prop="title"
                    :replaceFields="replaceFields"
                    :treeData="selectOption"
                    show-search
                    :searchValue="bsearchKey"
                    style="width: 100%"
                    multiple
                    :maxTagCount='1'
                    :dropdownMatchSelectWidth="true"
                    :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                    placeholder="请选择单位"
                    allow-clear
                    @change="onChangeTree"
                    @search="onSearch"
                    @select="onSelect">
                  </a-tree-select>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="使用部门">
                  <a-input :maxLength='maxLength' v-model="queryParam.userDepartment" placeholder="请输入使用部门" :allowClear='true' autocomplete='off'></a-input>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="状态">
                  <a-select placeholder="请选择状态" v-model="queryParam.status" :allowClear="true">
                    <a-select-option :key="0" value="pass">已通过 </a-select-option>
                    <a-select-option :key="1" value="approving">审批中 </a-select-option>
                    <a-select-option :key="2" value="refuse">已驳回 </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="colBtnsSpan()">
                <span
                  class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                >
                  <a-button type="primary" class="btn-search btn-search-style" @click="dosearch">查询</a-button>
                  <a-button class="btn-reset btn-reset-style" @click="doreset">重置</a-button>
                  <a v-if="isVisible" class="btn-updown-style" @click="doToggleSearch">
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <!-- 查询区域-END -->
      <a-card :bordered="false" style="width: 100%; flex: auto">
        <!-- 操作按钮区域 -->
        <div class="table-operator table-operator-style" v-if="showFlag">
          <a-button @click="batchOperate('pass')">通过</a-button>
          <a-button @click="batchOperate('refuse')">驳回</a-button>
        </div>
        <!-- table区域-begin -->
        <a-table
          ref="table"
          bordered
          rowKey="id"
          :columns="columns"
          :dataSource="dataSource"
          :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
          :pagination="ipagination"
          :loading="loading"
          :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          @change="handleTableChange"
        >
          <template slot="status" slot-scope="text">
            <span style="color: #409eff" v-if="text == 'approving'">审核中</span>
            <span style="color: #12ce66" v-else-if="text == 'pass'">已通过</span>
            <span style="color: #df1a1a" v-else>已驳回</span>
          </template>
          <span class="caozuo" slot="action" slot-scope="text, record">
            <a @click="handleDetailPage(record)">查看</a>
            <a-divider type="vertical" v-if="record.status == 'approving'" />
            <a @click="handlePass(record)" v-if="record.status == 'approving'">通过</a>
            <a-divider type="vertical" v-if="record.status == 'approving'" />
            <a @click="handleRefuse(record)" v-if="record.status == 'approving'">驳回</a>
          </span>
          <template slot="tooltip" slot-scope="text">
            <a-tooltip placement="topLeft" :title="text" trigger="hover">
              <div class="tooltip">
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
      </a-card>
      <terminal-device-modal ref="modalForm" @ok="modalFormOk"></terminal-device-modal>
    </a-col>
  </a-row>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixinNoInit'
import TerminalDeviceModal from '../terminalInfo/modules/TerminalDeviceModal'
import JSuperQuery from '@/components/jeecg/JSuperQuery.vue'
import { queryAssetsCategoryTreeList } from '@/api/device'
import { httpAction, getAction, putAction, deleteAction } from '@/api/manage'
import JDictSelectTag from '@/components/dict/JDictSelectTag.vue'
//引入公共devicetree组件
import TerminalDeptTree from '@/components/tree/TerminalDeptTree.vue'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'

export default {
  name: 'TerminalList',
  mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
  components: {
    TerminalDeviceModal,
    JSuperQuery,
    JDictSelectTag,
    TerminalDeptTree,
  },
  data() {
    return {
      maxLength:50,
      formItemLayout: {
        labelCol: {
          style: 'width:90px',
        },
        wrapperCol: {
          style: 'width:calc(100% - 90px)'
        }
      },
      searchKey: undefined,
      bsearchKey: '',
      value: undefined,
      replaceFields: {
        children: 'children',
        title: 'deptName',
        key: 'deptId',
        value: 'deptId',
      },
      //tree
      selectOption: [],
      isDown: 1,
      // 表头
      columns: [
        {
          title: '终端名称',
          dataIndex: 'terminalName',
          fixed: 'left',
          width: 200,
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 200px;max-width:300px'
            return { style: cellStyle }
          }
        },
        {
          title: '终端IP',
          dataIndex: 'ip',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 130px;max-width:200px'
            return { style: cellStyle }
          }
        },
        {
          title: 'MAC地址',
          dataIndex: 'macAddr',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 150px;max-width:220px'
            return { style: cellStyle }
          }
        },
        {
          title: '状态',
          dataIndex: 'status',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 100px;max-width:150px'
            return { style: cellStyle }
          }
        },
        {
          title: '单位',
          dataIndex: 'deptName',
          scopedSlots: { customRender: 'tooltip' },
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
            return { style: cellStyle }
          }
        },
        {
          title: '使用者',
          dataIndex: 'username',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 100px;max-width:150px'
            return { style: cellStyle }
          }
        },
        {
          title: '使用部门',
          dataIndex: 'userDepartment',
          scopedSlots: { customRender: 'tooltip' },
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 100px;max-width:300px'
            return { style: cellStyle }
          }
        },
        {
          title: '提审时间',
          dataIndex: 'createTime',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 150px;max-width:200px'
            return { style: cellStyle }
          }
        },
        {
          title: '操作',
          dataIndex: 'action',
          fixed: 'right',
          align: 'center',
          width: 160,
          scopedSlots: { customRender: 'action' },
        },
      ],
      url: {
        list: 'approve/momgTerminalApprove/list',
        edit: 'approve/momgTerminalApprove/edit',
      },
      showFlag: true,
      selectedRowKeys: [],
      loading: false,
    }
  },
  created() {
    // this.deptSelect()
    this.select()
  },
  mounted() {
    this.loadData(1)
  },
  methods: {
    select() {
      getAction('/sys/sysDepart/queryAllTree').then((res) => {
        for (let i = 0; i < res.length; i++) {
          let temp = res[i]
          this.selectOption.push(temp)
        }
      })
    },

    onChangeTree(value) {
      this.value = value
      // this.queryParam.deptId = value.join(",")
    },
    onSearch(e) {
      this.bsearchKey = e
    },
    onSelect() {},
    // deptSelect() {
    //   getAction('device/momgDept/queryAllDepts').then(res => {
    //     if (res.code == 200) {
    //       this.selectOption = res.result
    //     } else {
    //       this.$message.error(res.message)
    //     }
    //   })
    // },
    checkStatus() {},
    handlePass(info) {
      putAction(this.url.edit, { id: info.id, status: 'pass' }).then((res) => {
        if (res.success) {
          this.$message.success('审批通过!')
          this.loadData()
        }
      })
    },
    handleRefuse(info) {
      putAction(this.url.edit, { id: info.id, status: 'refuse' }).then((res) => {
        if (res.success) {
          this.$message.success('审批完成!')
          this.loadData()
        }
      })
    },
    //批量禁用、启用
    batchOperate(status) {
      if (this.selectedRowKeys.length <= 0) {
        this.$message.warning('请选择一条记录！')
        return
      } else {
        var ids = this.selectedRowKeys.join(',')
        var that = this
        this.$confirm({
          title: '通过操作',
          okText: '确定',
          cancelText: '取消',
          content: '确定审批通过选中数据?',
          onOk: function () {
            that.loading = true
            getAction('approve/momgTerminalApprove/updateStatus', { ids: ids, status: status })
              .then((res) => {
                if (res.success) {
                  that.$message.success(res.message)
                  that.loadData()
                  that.onClearSelected()
                } else {
                  that.$message.warning(res.message)
                }
              })
              .finally(() => {
                that.loading = false
              })
          },
        })
      }
    },
    onClearSelected() {
      this.selectedRowKeys = []
    },
    //勾选时进行状态判断
    onSelectChange(selectedRowKeys, selectionRows) {
      this.selectedRowKeys = selectedRowKeys
      this.showFlag = !selectionRows.some((ele) => ele.status !== 'approving')
    },
    handleDetailPage: function (record) {
      this.isDown == 2
      this.$parent.pButton2(1, record)
    },
    //表单查询,点击查询按钮，默认查询第一页
    dosearch() {
      if (!this.searchKey && this.bsearchKey) {
        this.searchKey = this.bsearchKey
      }
      if (Array.isArray(this.searchKey)) {
        this.queryParam.deptId = this.searchKey.join(',')
      } else if (typeof this.searchKey === 'string') {
        this.queryParam.deptId = this.searchKey
        // this.searchKey = ""
      }
      this.bsearchKey = ''
      this.loadData(1)
    },
    //表单重置
    doreset() {
      //重置form表单，不重置tree选中节点
      this.queryParam = {}
      this.loadData(1)
      this.searchKey = undefined
      this.bsearchKey = ''
    },
  },
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
</style>
