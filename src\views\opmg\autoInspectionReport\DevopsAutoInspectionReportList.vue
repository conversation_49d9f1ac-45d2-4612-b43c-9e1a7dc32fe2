<template>
  <a-card :bordered="false" style="height: 100%" class="vScroll zxw">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24" ref="row">
          <a-col :span="spanValue">
            <a-form-item label="报告名称">
              <a-input
                placeholder="请输入"
                :allowClear="true"
                autocomplete="off"
                v-model="queryParam.fileName" :maxLength="50"
              ></a-input>
            </a-form-item>
          </a-col>

          <a-col :span="colBtnsSpan()">
                <span
                  class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                >
                  <a-button type="primary" @click="searchQuery" class="btn-search-style">查询</a-button>
                  <a-button @click="searchReset" style="margin-left: 10px" class="btn-reset-style">重置</a-button>
                </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <!-- <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button> -->
      <a-button @click="handleExportXls('自动检查报告')">导出</a-button>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay" style='text-align: center'>
          <a-menu-item key="1" @click="batchDel">删除</a-menu-item>
        </a-menu>
        <a-button> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <a-table
        ref="table"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        class="j-table-force-nowrap"
        @change="handleTableChange"
      >
        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 14px">无图片</span>
          <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width: 80px; font-size: 14px" />
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 14px">无文件</span>
          <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record" class="caozuo">
          <a @click="downloadAIFile(record.fileUrl)">下载</a>
        </span>
        <template slot="tooltip" slot-scope="text">
          <a-tooltip placement="topLeft" :title="text" trigger="hover">
            <div class="tooltip">
              {{ text }}
            </div>
          </a-tooltip>
        </template>
      </a-table>
    </div>

    <devops-auto-inspection-report-modal ref="modalForm" @ok="modalFormOk"></devops-auto-inspection-report-modal>
  </a-card>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { getFileAccessHttpUrl } from '@/api/manage'
import DevopsAutoInspectionReportModal from './modules/DevopsAutoInspectionReportModal'
import JSuperQuery from '@/components/jeecg/JSuperQuery.vue'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
import {downloadFile} from '@/api/manage'

export default {
  name: 'DevopsAutoInspectionReportList',
  mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
  components: {
    DevopsAutoInspectionReportModal,
    JSuperQuery,
  },
  data() {
    return {
      description: '自动检查报告管理页面',
      // 表头
      columns: [
        {
          title: '任务名称',
          dataIndex: 'taskName',
        },
        {
          title: '报告名称',
          dataIndex: 'fileName',
        },
        {
          title: '任务开始时间',
          dataIndex: 'taskstartTime',
        },
        {
          title: '报告生成时间',
          dataIndex: 'createTime',
        },
        {
          title: '任务结束时间',
          dataIndex: 'taskendTime',
        },
        {
          title: '任务制定人',
          dataIndex: 'taskCreateUser',
        },
        {
          title: '下载',
          dataIndex: 'action',
          fixed: 'right',
          align:'center',
          width: 147,
          scopedSlots: { customRender: 'action' },
        },
      ],
      url: {
        list: '/autoInspectionReport/devopsAutoInspectionReport/list',
        delete: '/autoInspectionReport/devopsAutoInspectionReport/delete',
        deleteBatch: '/autoInspectionReport/devopsAutoInspectionReport/deleteBatch',
        exportXlsUrl: '/autoInspectionReport/devopsAutoInspectionReport/exportXls',
        importExcelUrl: 'autoInspectionReport/devopsAutoInspectionReport/importExcel',
      },
      dictOptions: {},
      superFieldList: [],
    }
  },
  created() {
    this.getSuperFieldList()
  },
  mounted() {},
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
  },
  methods: {
    initDictConfig() {},
    getSuperFieldList() {
      let fieldList = []
      fieldList.push({ type: 'datetime', value: 'createTime', text: '报告生成时间' })
      fieldList.push({ type: 'string', value: 'reportType', text: '报表类型', dictCode: '' })
      fieldList.push({ type: 'string', value: 'taskId', text: '智能巡检任务id', dictCode: '' })
      fieldList.push({ type: 'date', value: 'taskstartTime', text: '任务开始时间' })
      fieldList.push({ type: 'date', value: 'taskendTime', text: '任务结束时间' })
      fieldList.push({ type: 'string', value: 'fileId', text: '报告文件id', dictCode: '' })
      this.superFieldList = fieldList
    },
    downloadAIFile(text) {
      if (!text) {
        this.$message.warning("没有报告文件路径！")
        return
      }
      let url = window._CONFIG['domianURL'] + '/sys/common/downloadFile/' + text
      downloadFile(url,text)
    },
  },
}
</script>
<style lang="less" scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
::v-deep .ant-table-thead > tr > th {
  text-align: center;
}
::v-deep .ant-table-tbody > tr > td {
  &:nth-child(2),
  &:nth-child(3),
  &:nth-child(4),
  &:nth-child(5),
  &:nth-child(6) {
    text-align: center;
  }
}
</style>
