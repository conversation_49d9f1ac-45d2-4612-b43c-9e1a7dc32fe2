<template>
  <a-row :gutter='10' style='height: 100%' class='vScroll'>
    <a-col style='width: 100%; height: 100%; display: flex; flex-direction: column'>
      <!-- 查询区域 -->
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0', marginRight: '12px' }" class='card-style'
        style='width: 100%'>
        <div class='table-page-search-wrapper'>
          <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
            <a-row :gutter='24' ref='row'>
              <a-col :span='spanValue'>
                <a-form-item label='字段名称'>
                  <a-input placeholder='请输入字段名称' v-model='queryParam.name' :allowClear='true' autocomplete='off'
                    :maxLength="maxLength" />
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label='字段标识'>
                  <a-input placeholder='请输入字段标识' v-model='queryParam.code' :allowClear='true' autocomplete='off'
                    :maxLength="maxLength" />
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label='字段类型'>
                  <j-dict-select-tag type="list" v-model="queryParam.type" dictCode="type_code" placeholder="请选择字段类型" />
                </a-form-item>
              </a-col>
              <a-col :span='colBtnsSpan()'>
                <span class='table-page-search-submitButtons'
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button type='primary' class='btn-search btn-search-style' @click='searchQuery'>查询</a-button>
                  <a-button class='btn-reset btn-reset-style' @click='searchReset'>重置</a-button>
                  <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <div class='table-operator table-operator-style'>
          <a-button @click='handleAdd'>新增</a-button>
          <a-dropdown v-if='selectedRowKeys.length > 0'>
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item key='1' @click='batchDel'>删除</a-menu-item>
            </a-menu>
            <a-button> 批量操作
              <a-icon type='down' />
            </a-button>
          </a-dropdown>
        </div>
        <a-table ref='table' bordered :row-key='(record, index) => {return record.id}' :columns='columns'
          :dataSource='dataSource' :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}" :pagination='ipagination'
          :loading='loading' :rowSelection='{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }'
          @change='handleTableChange'>
          <span slot='action' class='caozuo' slot-scope='text, record'>
            <a @click='handleDetailPage(record)'>查看</a>
            <a-divider type='vertical' />
            <a-dropdown>
              <a class='ant-dropdown-link'>更多
                <a-icon type='down' /></a>
              <a-menu slot='overlay'>
                <a-menu-item>
                  <a @click='handleEdit(record)' style="color: #409eff">编辑</a>
                </a-menu-item>
                <a-menu-item>
                  <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                    <a style="color: #409eff">删除</a>
                  </a-popconfirm>
                </a-menu-item>
              </a-menu>
            </a-dropdown>
          </span>
        </a-table>
      </a-card>
      <additional-modal ref='modalForm' @ok='modalFormOk'> </additional-modal>
    </a-col>
  </a-row>
</template>

<script>
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import additionalModal from './modules/additionalModal.vue'
  import {
    YqFormSearchLocation
  } from '@/mixins/YqFormSearchLocation'
  export default {
    mixins: [JeecgListMixin, YqFormSearchLocation],
    components: {
      additionalModal
    },
    data() {
      return {
        maxLength:50,
        formItemLayout: {
          labelCol: {
            style: 'width:80px'
          },
          wrapperCol: {
            style: 'width:calc(100% - 80px)'
          }
        },
        // 子网组表头
        columns: [{
            title: '字段名称',
            dataIndex: 'name',
            customCell: () => {
              let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '字段标识',
            dataIndex: 'code',
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 150px;max-width:300px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '字段类型',
            dataIndex: 'type',
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 100px;max-width:300px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '字段描述',
            dataIndex: 'remark',
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 100px;max-width:300px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            fixed: 'right',
            width: 140,
            scopedSlots: {
              customRender: 'action'
            }
          }
        ],
        url: {
          list: '/devops/ip/planExtend/list',
          delete: '/devops/ip/planExtend/delete',
          deleteBatch: '/devops/ip/planExtend/deleteBatch',
        },
      };
    },
    methods: {}
  }
</script>

<style scoped lang="less">
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';
</style>