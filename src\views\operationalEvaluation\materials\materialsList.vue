<template>
  <a-row :gutter='10' style='display: flex;flex-flow: column nowrap;height: 100%;'>
    <a-col style='width:100%;height: 100%;display: flex;flex-direction: column'>
      <a-card
        :bordered='false'
        :bodyStyle="{paddingBottom:'0'}"
        class='card-style'>
        <div class='table-page-search-wrapper'>
          <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
            <a-row :gutter='24' ref='row'>
              <a-col :span='spanValue'>
                <a-form-item label='项目名称'>
                  <a-input
                    :maxLength='maxLength'
                    :allowClear='true'
                    autocomplete='off'
                    v-model='queryParam.projectName'
                    placeholder='请输入项目名称'
                  />
                </a-form-item>
              </a-col>

              <a-col :span='colBtnsSpan()'>
               <span class='table-page-search-submitButtons'
                     :style="toRight && { float: 'right', overflow: 'hidden' } || {} ">
                  <a-button type='primary' class='btn-search btn-search-style' @click='searchQuery'>查询</a-button>
                  <a-button class='btn-reset btn-reset-style' @click='searchReset'>重置</a-button>
                  <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                   <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
               </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>

      <a-card :bordered='false' style='flex: auto'>
        <div class='table-operator table-operator-style'>
          <a-button @click="handleAdd()">新增</a-button>
          <a-dropdown v-if="selectedRowKeys.length > 0">
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item key="1" @click="batchDel"> 删 除</a-menu-item>
            </a-menu>
            <a-button>
              批量操作
              <a-icon type="down" />
            </a-button>
          </a-dropdown>
        </div>

        <a-table
          ref='table'
          bordered
          rowKey='id'
          :columns='columns'
          :dataSource='dataSource'
          :scroll='dataSource.length > 0 ? { x:"max-content"} : {}'
          :pagination='ipagination'
          :loading='loading'
          :rowSelection='{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }'
          @change='handleTableChange'>
          <span class='caozuo' slot='action' slot-scope='text, record'>
            <a @click='handleDetail(record)'>查看</a>
            <a-divider type="vertical" />
            <a @click="handleEdit(record)">编辑</a>
            <a-divider type="vertical" />
            <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
               <a>删除</a>
            </a-popconfirm>
            <a-divider type="vertical" />
            <a @click="handleDetailPage(record,1)">佐证资料</a>
          </span>
          <template slot='tooltip' slot-scope='text'>
            <a-tooltip placement='topLeft' :title='text' trigger='hover'>
              <div class='tooltip'>
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
      </a-card>
      <materials-modal ref="modalForm" @ok="modalFormOk"></materials-modal>
    </a-col>
  </a-row>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { getAction } from '@/api/manage'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
import materialsModal from '@views/operationalEvaluation/materials/modules/materialsModal.vue'
export default {
  name: 'materialsList',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components:{materialsModal},
  data() {
    return {
      maxLength: 50,
      formItemLayout: {
        labelCol: {
          style: 'width:90px'
        },
        wrapperCol: {
          style: 'width:calc(100% - 90px)'
        }
      },
      // 表头
      columns: [
        {
          title: '项目名称',
          dataIndex: 'projectName'
        },
        {
          title: '发起人',
          dataIndex: 'sender_dictText'
        },
        {
          title: '评估时间',
          dataIndex: 'evaluateTime',
          /*customCell: () => {
            let cellStyle = 'text-align: left;min-width: 100px;max-width:400px'
            return { style: cellStyle }
          }*/
        },
        {
          title: '操作',
          dataIndex: 'action',
          fixed: 'right',
          width: 240,
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: '/evaluate/projectInfo/pageList',
        delete:'/evaluate/projectInfo/delete',
        deleteBatch: '/evaluate/projectInfo/deleteBatch',
      },
    }
  },
  mounted() {

  },
  methods: {

  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import "~@assets/less/scroll.less";

</style>

