<template>
  <div style="margin-top:15px;">
    <!-- 查询区域 -->
    <div class='table-page-search-wrapper'>
      <a-form layout='inline'
        @keyup.enter.native='searchQuery'>
        <a-row :gutter='24'
          ref='row'>
          <a-col :span='6'>
            <a-form-item :label="'标\u3000题'">
              <a-input placeholder='请输入标题'
                v-model='queryParam.title'
                :allowClear='true'
                autocomplete='off' :maxLength="maxLength"/>
            </a-form-item>
          </a-col>
          <a-col :span='6'>
            <span class='table-page-search-submitButtons'
              :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
              <a-button type='primary'
                class='btn-search btn-search-style'
                @click='searchQuery'>查询</a-button>
              <a-button class='btn-reset btn-reset-style'
                @click='searchReset'>重置</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <a-table ref="table"
      size='middle'
      rowKey="id"
      :columns="columns"
      :dataSource="dataSource"
      :pagination="ipagination"
      :loading="loading"
      class="j-table-force-nowrap"
      @change="handleTableChange"
      :showHeader="false"
      :bordered="false"
      :locale='locale'>
      <span slot="action"
        slot-scope="text, record"
        class="caozuo"
        style="display: inline-block; white-space: nowrap; text-align: center">
        <span class="action-icon" @click="handleDetailPage(record)">
          <img src="../../../../public/oneClickHelp/look.png" alt />
        </span>
      </span>
      <span slot="tooltip" slot-scope="text, record">
        <commonItem :record="record" @handleDetailPage="handleDetailPage"></commonItem>
      </span>
      <span slot="commentCount"
        slot-scope="text, record"
        >
        <span>总评论:{{ record.commentCount || 0 }}条</span>
        </span>
    </a-table>
  </div>
</template>

<script>
import Empty from '@/components/oneClickHelp/Empty.vue'
import commonItem from './modules/commonItem.vue'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
export default {
  name: 'myComments',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components: {Empty, commonItem},
  data() {
    return {
      maxLength:50,
      locale: {
        emptyText: <Empty/>
      },
      columns: [
        {
          title: '标题',
          dataIndex: 'title',
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 300px;'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'tooltip'
          }
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 100,
          scopedSlots: {
            customRender: 'action'
          }
        },
        {
          title: '总评论(条)',
          dataIndex: 'commentCount',
          align: 'center',
          width: 200,
          scopedSlots: {
            customRender: 'commentCount'
          }
        }
      ],
      url: {
        list: '/kbase/knowledges/usercomment',
      },
    }
  },
  activated() {
    console.log('激活我的评论')
    this.loadData()
  },
  methods: {
    // 查看
    handleDetailPage: function (record) {
      this.$emit('getRecord', record)
    }
  },
}

</script>
<style lang='less' scoped>
@import '~@assets/less/onclickStyle.less';
@import './modules/myKnowledge.less';
</style>
