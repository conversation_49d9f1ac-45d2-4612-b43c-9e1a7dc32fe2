<template>
  <a-row style='height: 100%'>
    <a-col style='height: 100%; display: flex; flex-direction: column'>
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0', marginRight: '12px' }" class='card-style'>
        <!-- 查询区域 -->
        <div class="table-page-search-wrapper">
          <!-- 搜索区域 -->
          <a-form layout="inline" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="问题描述">
                  <a-input :maxLength='maxLength' :allowClear="true" autocomplete="off" placeholder="请输入问题描述"
                           v-model="queryParam.question" />
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="问题类型">
                  <a-select placeholder="请选择" :getPopupContainer="(node) => node.parentNode"
                            v-model="queryParam.questionType" :allowClear="true">
                    <a-select-option v-for="(item, index) in IssueType" :key="index" :value="item.label">
                      {{ item.label }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="提问人">
                  <a-input :maxLength='maxLength' :allowClear="true" autocomplete="off" placeholder="请输入提问人"
                           v-model="queryParam.quizzer" />
                </a-form-item>
              </a-col>
              <a-col :span="spanValue" v-show="toggleSearchStatus">
                <a-form-item label="时间范围">
                  <a-range-picker :getCalendarContainer="node=> node.parentNode" format="YYYY-MM-DD HH:mm:ss" showTime
                    v-model="queryParam.searchCreateTime" :placeholder="['开始时间', '结束时间']" @change="onCreateTimeChange"
                    style="width: 100%" />
                </a-form-item>
              </a-col>
              <a-col :span="spanValue" v-show="toggleSearchStatus">
                <a-form-item label="工单状态">
                  <a-select placeholder="请选择" :getPopupContainer="(node) => node.parentNode" v-model="queryParam.status"
                    :allowClear="true">
                    <a-select-option v-for="(item, index) in WorkOrderStatus" :key="index" :value="item.value">
                      {{ item.text }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>

<!--              <a-col :span="spanValue" v-show="toggleSearchStatus">
                <a-form-item label="关键字">
                  <a-input :maxLength='maxLength' :allowClear="true" autocomplete="off" placeholder="请输入关键字"
                    v-model="queryParam.queryKeywords" />
                </a-form-item>
              </a-col>-->
              <a-col :span="colBtnsSpan()">
                <span class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button type="primary" style="margin-left: 8px; margin-bottom: 8px" @click="searchQuery"
                    class="btn-search-style">查询</a-button>
                  <a-button @click="searchReset" style="margin-left: 10px" class="btn-reset-style">重置</a-button>
                  <a v-if="isVisible" @click="doToggleSearch" class="btn-updown-style"
                    style="margin-left: 10px; color: #409eff">
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>

      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <!--        <div class="table-operator">
          <a-button type="primary" @click="addCommonQues">添加为常见问题</a-button>
        </div>-->

        <!-- table区域 -->
        <div>
          <a-table :columns="columns" rowKey="id" bordered :pagination="ipagination" :dataSource="dataSource"
            @change="handleTableChange" :loading="loading"
            :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }">
            <template slot='tooltip' slot-scope='text'>
              <a-tooltip :title='text' placement='topLeft' trigger='hover'>
                <div class='tooltip'>
                  {{ text }}
                </div>
              </a-tooltip>
            </template>
            <span slot="status" slot-scope="text, record">
              <div v-if="record.status === '待确认'">
                <a-dropdown>
                  <a style="color: #409eff;">{{text}}</a>
                  <a-menu slot='overlay'>
                    <a-menu-item @click="getOk(record)">直接确认</a-menu-item>
                    <a-menu-item @click="handleStatus(record)">发起工单</a-menu-item>
                  </a-menu>
                  <a class="status-link" @click.prevent>{{ record.status }}</a>
                </a-dropdown>
              </div>
              <div v-else>
                <span class="status-default">{{ record.status }}</span>
              </div>
            </span>
          </a-table>
        </div>
      </a-card>

      <StayAllotModal ref="StayAllotModal" @ok="modalFormOk"></StayAllotModal>
      <okModal ref="okModal" @ok="questionOk"></okModal>
      <StayNotarizeModal ref="StayNotarizeModal" @ok="modalFormOk"></StayNotarizeModal>
      <RequestAllotModal ref="RequestAllotModal" @ok="modalFormOk"></RequestAllotModal>
      <!-- 表单区域 -->
      <!--      <Process-instance-start v-if='dialogStartInstanceVisible'
                              :dialogStartInstanceVisible.sync='dialogStartInstanceVisible'
                              :process-definition='processDefinition'
                              :associationId='associationId'
                              :formUrl='formUrl'
                              :startUrl='startUrl'
                              :showDdraft='false'
                              :dict-key='"questionConfirm"'
                              @loadData='loadData'
      >
      </Process-instance-start>-->
      <Process-instance-start v-if='dialogStartInstanceVisible'
        :dialogStartInstanceVisible.sync='dialogStartInstanceVisible' :process-definition='processDefinition'
        :associationId='associationId' :alarmHistory='alarmHistory' :formUrl='formUrl' :startUrl='startUrl'
        method='post' :showDdraft='false' :dict-key='"questionConfirm"' @loadData='loadData'>
      </Process-instance-start>
    </a-col>
  </a-row>
</template>
<script>
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import okModal from './modules/okModal'
  import StayAllotModal from './modules/StayAllotModal'
  import StayNotarizeModal from './modules/StayNotarizeModal'
  import RequestAllotModal from './modules/RequestAllotModal'
  import {
    ajaxGetDictItems
  } from '@api/api'
  import {
    httpAction,
    getAction,
  } from '@/api/manage'
  import {
    YqFormSearchLocation
  } from '@/mixins/YqFormSearchLocation'
  import ProcessInstanceStart from '../../flowable/process-instance-start/module/ProcessInstanceStart.vue'
  import {
    queryConfigureDictItem
  } from '../../../api/api'
  export default {
    name: 'AllocationIssue',
    mixins: [JeecgListMixin, YqFormSearchLocation],
    components: {
      ProcessInstanceStart,
      StayAllotModal, //待分配
      StayNotarizeModal, //待确认
      RequestAllotModal, //请求分配
      okModal,
    },
    data() {
      return {
        maxLength:50,
        processDefinitionKey: "",
        associationId: "",
        formUrl: "",
        startUrl: "",
        dialogStartInstanceVisible: false,
        processDefinition: undefined,
        alarmHistory: {},
        // 表头
        columns: [{
            title: '问题描述',
            dataIndex: 'question',
            scopedSlots: {
              customRender: 'tooltip'
            },
            customCell: () => {
              let cellStyle = 'text-align: left;max-width:350px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '问题类型',
            dataIndex: 'questionTypeText',
          },
          {
            title: '提问人',
            dataIndex: 'quizzer',
          },
          {
            title: '提问时间',
            dataIndex: 'createTime',
            sorter: true,
          },
          {
            title: '联系电话',
            dataIndex: 'contact',
          },
          {
            title: '提问人地址',
            dataIndex: 'region',
          },
          {
            title: '状态',
            dataIndex: 'status',
            scopedSlots: {
              customRender: 'status'
            },
            // sorter: true,
          },
          {
            title: '确认人',
            dataIndex: 'confirmor',
          },
          {
            title: '确认时间',
            dataIndex: 'confirmTime',
            sorter: true,
          },
        ],
        url: {
          list: '/question/question/list',
          delete: '/question/question/delete',
          deleteBatch: '/question/question/deleteBatch',
          getDictCodeChild: '/sys/dict/getDictCodeChild',
          addCommonQues: '/question/question/addCommonQues',
        },
        //工单状态
        WorkOrderStatus: [],
        //问题类型
        IssueType: [],
      }
    },
    created() {
      this.getProcessDefinitionKey()
      this.getDicData()
    },
    methods: {
      onCreateTimeChange: function (value, dateString) {
        this.queryParam.startTime = dateString[0]
        this.queryParam.endTime = dateString[1]
      },
      getOk(record) {
        this.$refs.okModal.add(record)
        this.$refs.okModal.visible = true
        this.$refs.okModal.title = '问题确认'
      },
      questionOk(data) {
        this.confirmQuestion(data)
      },
      /** 处理直接确认 */
      confirmQuestion(record) {
        const question = {
          id: record.id,
          answererContent: record.answererContent
        };
        httpAction('/question/question/confirm', question, 'put')
          .then((res) => {
            if (res.success) {
              this.$message.success(res.message);
              this.loadData(); // 刷新数据
            } else {
              this.$message.warning(res.message);
            }
          });
      },
      addCommonQues() {
        var that = this
        var ids = ''
        if (this.selectedRowKeys.length == 0) {
          that.$message.warning('请选择问题！')
          return
        }
        for (var a = 0; a < that.selectedRowKeys.length; a++) {
          ids += that.selectedRowKeys[a] + ','
        }
        let method = 'post'
        let formData = {
          ids: ids
        }
        that.$confirm({
          title: '确认信息',
          okText: '确认',
          cancelText: '取消',
          content: '确认将选中的问题加为常见问题?',
          onOk: function () {
            that.loading = true
            httpAction(that.url.addCommonQues, formData, method)
              .then((res) => {
                if (res.success) {
                  that.$message.success(res.message)
                } else {
                  that.$message.warning(res.message)
                }
              })
              .finally(() => {
                that.loading = false
              })
          },
        })
      },
      /*确认气泡******************/
      confirm(e) {
        this.$message.success('确定成功')
      },
      cancel(e) {
        this.$message.error('确认取消')
      },
      handleAffirm(id) {},
      /**获取流程定义key,并根据key,初始化流程相关变量*/
      getProcessDefinitionKey() {
        queryConfigureDictItem({
          parentCode: 'businessToProcesKey',
          childCode: 'clientQuestionProcessKey',
        }).then((res) => {
          if (res.success) {
            this.processDefinitionKey = res.result
          } else {
            this.processDefinitionKey = "eventProcessShenzhen"
          }
          this.initFlowData()
        })
      },
      /**初始化流程相关变量*/
      initFlowData() {
        getAction('/flowable/processDefinition/queryByKey', {
            processDefinitionKey: this.processDefinitionKey
          })
          .then((res) => {
            if (res.success) {
              this.processDefinition = res.result
              this.formUrl = "/association/process/renderedStartForm"
              // this.startUrl= "/alarm/alarmHistory/start"
              this.startUrl = "/association/process/start"
            } else {
              this.$message.warning(res.message)
            }
          }).catch((err) => {
            this.$message.warning(err.message)
          })
      },
      /*确认气泡**********end */
      /*handleStatus: function (record) {
        getAction('/flowable/processDefinition/queryByKey', { processDefinitionKey: this.processDefinitionKey })
          .then((res) => {
            if (res.success) {
              this.processDefinition = res.result
              this.associationId = record.id
                this.formUrl= "/question/question/renderedStartForm"
              this.startUrl= "/question/question/start"
              this.dialogStartInstanceVisible = true
            } else {
              this.$message.warning(res.message)
            }
          })
      },*/
      /**处理发起工单*/
      handleStatus(data) {
        if (this.processDefinition.id && this.processDefinition.id.length > 0 && this.processDefinitionKey.length > 0) {
          this.associationId = data.id
          this.alarmHistory = data
          this.alarmHistory.processDefinitionId = this.processDefinition.id
          this.dialogStartInstanceVisible = true
        }
      },

      handleEdit(record) {
        this.$refs.StayAllotModal.edit(record)
        this.$refs.StayAllotModal.title = '工单分配'
        this.$refs.StayAllotModal.disableSubmit = true
      },
      //字典获取工单状态&&问题类型选择框内容--------------------------------------------
      getDicData() {
        this.getWorkOrderStatus('WorkOrder_status')
        this.getIssueType()
      },
      getWorkOrderStatus(code) {
        ajaxGetDictItems(code, null).then((res) => {
          let temp = res.result
          for (let i = 0; i < temp.length; i++) {
            this.WorkOrderStatus.push({
              text: temp[i].text,
              value: temp[i].text == '全部' ? '' : temp[i].text
            })
          }
        })
      },
      getIssueType(code) {
        let params = {
          dictCode: 'serviceRequestClass',
        }
        getAction(this.url.getDictCodeChild, params).then((res) => {
          if (res.success) {
            this.IssueType = res.result
          }
        })
      },
      //字典获取工单状态&&问题类型选择框内容-----------------------------------------end
    },
    beforeMount() {
      this.signoutShow = sessionStorage.getItem('signoutShow')
    },
    // beforeUpdate() {
    //   sessionStorage.setItem(arr1)
    // },
  }
</script>
<style lang="less" scoped>
  @import '~@assets/less/common.less';
  @import '~@assets/less/YQCommon.less';

  /* @import '~@assets/less/scroll.less'; */
  .status-link {
    cursor: pointer;
    color: #409eff;
  }

  .status-default {
    cursor: context-menu;
    color: #9b9b9b;
  }
</style>