import Vue from 'vue'
import axios from 'axios'
import store from '@/store'
import { VueAxios } from './axios'
import { Modal, notification } from 'ant-design-vue'
import { ACCESS_TOKEN, TENANT_ID } from "@/store/mutation-types"
import fa from 'element-ui/src/locale/lang/fa'

/**
 * 【指定 axios的 baseURL】
 * 如果手工指定 baseURL: '/jeecg-boot'
 * 则映射后端域名，通过 vue.config.js
 * @type {*|string}
 */
let apiBaseUrl = window._CONFIG['domianURL'];
// 创建 axios 实例
const service = axios.create({
  //baseURL: '/jeecg-boot',
  baseURL: apiBaseUrl, // api base_url
  timeout: 60000 // 请求超时时间
})

let  existingModal=false
const err = (error) => {
  if (error.response) {
    let that = this;
    let data = error.response.data
    const token = Vue.ls.get(ACCESS_TOKEN)
    switch (error.response.status) {
      case 403:
        notification.error({ message: '系统提示', description: '拒绝访问', duration: 4 })
        break
      case 500:
        // update-begin- --- author:liusq ------ date:20200910 ---- for:处理Blob情况----
        let type = error.response.request.responseType;
        if (type === 'blob') {
          blobToJson(data);
          break;
        }
        // update-end- --- author:liusq ------ date:20200910 ---- for:处理Blob情况----
        //notification.error({ message: '系统提示', description:'Token失效，请重新登录!',duration: 4})
        if (token && data.message.includes("Token失效")) {
          // update-begin- --- author:scott ------ date:20190225 ---- for:Token失效采用弹框模式，不直接跳转----
          if(!existingModal){
            existingModal=true
          }
          else {
            return
          }
          let isKShare=window.location.href.indexOf('#/knowledgeManagement/Sharing?shareId=')
          if (isKShare>0){
            notification.info({
              message: '很抱歉，登录已过期，请重新输入提取码',
              duration: 3
            })
            console.log('Token失效')
            store.dispatch('Logout').then(() => {
              Vue.ls.remove(ACCESS_TOKEN)
              window.location.reload()
            })
          }else {
            Modal.error({
              title: '登录已过期',
              content: '很抱歉，登录已过期，请重新登录',
              okText: '重新登录',
              mask: false,
              class:window.location.href.indexOf('#/oneClickHelp')>0?'oneClickHelpErrorModal':'',
              onOk: () => {
                store.dispatch('Logout').then(() => {
                  Vue.ls.remove(ACCESS_TOKEN)
                  try {
                    //let path = window.document.location.pathname
                    let path = location.href
                    if (path.indexOf('/user/login') === -1) {
                      window.location.reload()
                    }
                  } catch (e) {
                    window.location.reload()
                  }
                  existingModal=false
                })
              }
            })
            // update-end- --- author:scott ------ date:20190225 ---- for:Token失效采用弹框模式，不直接跳转----
          }
        }
        break
      case 404:
        notification.error({ message: '系统提示', description: '很抱歉，资源未找到!', duration: 4 })
        break
      case 504:
        notification.error({ message: '系统提示', description: '网络超时' })
        break
      case 401:
        notification.error({ message: '系统提示', description: '未授权，请重新登录', duration: 4 })
        if (token) {
          store.dispatch('Logout').then(() => {
            setTimeout(() => {
              window.location.reload()
            }, 1500)
          })
        }
        break
      default:
        notification.error({
          message: '系统提示',
          description: data.message,
          duration: 4
        })
        break
    }
  }
  return Promise.reject(error)
};

// request interceptor
service.interceptors.request.use(config => {
  if (config.url === "/server/getProcess") {
    config.timeout = 10000
  }
  const token = Vue.ls.get(ACCESS_TOKEN)
  if (token) {
    config.headers['X-Access-Token'] = token // 让每个请求携带自定义 token 请根据实际情况自行修改
  }
  //update-begin-author:taoyan date:2020707 for:多租户
  let tenantid = Vue.ls.get(TENANT_ID)
  if (!tenantid) {
    tenantid = 0;
  }
  config.headers['tenant_id'] = tenantid
  //update-end-author:taoyan date:2020707 for:多租户
  if (config.method == 'get') {
    if (config.url && config.url.indexOf("sys/dict/getDictItems") < 0) {
      config.params = {
        _t: Date.parse(new Date()) / 1000,
        ...config.params
      }
    }
  }
  return config
}, (error) => {
  return Promise.reject(error)
})

// response interceptor
service.interceptors.response.use((response) => {
  if(response.headers['file-name']){
    return response
  }else {
    return response.data
  }
}, err)

const installer = {
  vm: {},
  install(Vue, router = {}) {
    Vue.use(VueAxios, router, service)
  }
}
/**
 * Blob解析
 * @param data
 */
function blobToJson(data) {
  let fileReader = new FileReader();
  let token = Vue.ls.get(ACCESS_TOKEN);
  fileReader.onload = function () {
    try {
      let jsonData = JSON.parse(this.result);  // 说明是普通对象数据，后台转换失败
      if (jsonData.status === 500) {
        if (token && jsonData.message.includes("Token失效")) {
          let isKShare=window.location.href.indexOf('#/knowledgeManagement/Sharing?shareId=')
          if (isKShare>0){
            notification.info({
              message: '很抱歉，登录已过期，请重新输入提取码',
              duration: 3
            })
            console.log('Token失效')
            store.dispatch('Logout').then(() => {
              Vue.ls.remove(ACCESS_TOKEN)
              window.location.reload()
            })
          }else {
            Modal.error({
              title: '登录已过期',
              content: '很抱歉，登录已过期，请重新登录',
              okText: '重新登录',
              mask: false,
              class:window.location.href.indexOf('#/oneClickHelp')>0&&window.location.href.indexOf('hostname')>0?'oneClickHelpErrorModal':'',
              onOk: () => {
                store.dispatch('Logout').then(() => {
                  Vue.ls.remove(ACCESS_TOKEN)
                  window.location.reload()
                })
              }
            })
          }
        }
      }
    } catch (err) {
      // 解析成对象失败，说明是正常的文件流
    }
  };
  fileReader.readAsText(data)
}

export {
  installer as VueAxios,
  service as axios
}