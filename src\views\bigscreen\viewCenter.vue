<template>
  <div class="viewCenter cece">
    <div class="content-container">
      <router-view />
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
     
    }
  },
  components: {
   
  },
  created() {
   
  },
  mounted() {
   
  },
  methods: {
   
  },
}
</script>
<style lang="less" scoped>
.viewCenter {
  color: #fff;
  height: calc(100% - 32px);
  width: 100%;
  margin-top: 16px;
}
.title-container {
  display: flex;
  justify-content: center;
  height: 5%;
}
.title {
  width: 100px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 15px;
  color: white;
}
.content-container {
  height: 100%;
  width: 100%;

  background: #222224;
  padding: 0 16px;
}
.net-content {
  position: relative;
  // width: 22.375rem /* 1790/80 */;
  width: 100%;
  height: 100%;
  background-color: #111217;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.big-screen-div {
  width: 86%;
  height: 100%;
}
.topo-list {
  width: 14%;
  height: 96%;
  padding-right: 16px;
  overflow-y: auto;
}
.list-div {
  text-align: center;
  cursor: pointer;
  span {
    font-size: 14px;
    color: #4e4f53;
  }
}
.select-span {
  color: #aaaaac !important;
}
.img-div {
  width: 100%;
  height: 100%;
  border: 1px solid #4e4f53;
  position: relative;
}
::v-deep .img-div > svg {
  width: 100% !important;
  height: 100% !important;
}
::v-deep .img-div > svg > g {
  transform: matrix(1, 0, 0, 1, 1, 1) !important;
}
.select-img {
  border: 1px solid #aaaaac;
}
.room-content {
  width: 100%;
  height: 100%;
}
.threeD-content,
.twoD-content {
  width: 100%;
  height: 100%;
}
.threeD-content {
  position: relative;
}
.room-select {
  position: absolute;
  top: 30px;
  left: 60px;
  width: 185px;
}
.room-title-p {
  color: #64e0ea;
  font-size: 27px;
  margin-bottom: 7px;
}
.room-title-p-english {
  color: #dd7a1f;
  font-size: 10px;
  margin-bottom: 10px;
}
.room-position {
  color: #63d7de;
  font-size: 12px;
}
.title-div1 {
  border: none;
  height: 1px;
  background-color: #38747c;
}
.title-div2 {
  border: none;
  height: 2px;
  width: 80%;
  background-color: #4ca5ad;
  margin-top: 0.05rem; /* 4/80 */
}
.position-bottom-div {
  height: 2px;
  width: 55%;
  background-color: #39747c;
}
.borderBottom {
  border-bottom: 3px solid #0585aa;
}
::v-deep .container canvas {
  height: 100% !important;
  width: 100% !important;
}

::v-deep .ant-select {
  color: #63d7de !important;
  font-size: 27px;
  .ant-select-selection {
    background-color: #101117 !important;
    border: 1px solid #101117 !important;
    box-shadow: none !important;
  }
}
::v-deep .ant-select-selection:hover {
  border-color: #101117 !important;
}

::v-deep .ant-select-arrow {
  color: #63d7de !important;
  right: 5px !important;
}

.pagination-div {
  display: flex;
  flex-direction: row-reverse;
}
</style>
