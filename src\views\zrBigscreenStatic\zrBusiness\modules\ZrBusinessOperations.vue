<template>
  <div class='zr-business-support' ref='zrBusinessSupport'>
    <zr-bigscreen-title title='运维人员'>
    </zr-bigscreen-title>
    <div class='business-support-content'>
      <slot>
        <vue-seamless-scroll v-if='srollOption.autoPlay' :data='listData' class='scroll-warp'
                             :class-option='srollOption'>
          <ul class='scroll-list'>
            <li class='scroll-list-item' :class='{"scroll-list-odd":index%2!==0}' v-for='(item, index) in listData'
                :key='index'>
              <div class='item-name' :title='item.businessName' :style='{"--status-color":item.color}'>
                {{ item.businessName }}
              </div>
              <div class='item-worker' :title='item.realName'>{{ item.realName }}</div>
              <div class='item-phone' >{{ item.phone }}</div>
            </li>
          </ul>
        </vue-seamless-scroll>
        <div v-else class='scroll-warp'>
          <ul class='scroll-list'>
            <li class='scroll-list-item' :class='{"scroll-list-odd":index%2!==0}' v-for='(item, index) in listData'
                :key='index'>
              <div class='item-name' :title='item.businessName' :style='{"--status-color":item.color}'>
                {{ item.businessName }}
              </div>
              <div class='item-worker' :title='item.realName'>{{ item.realName }}</div>
              <div class='item-phone'>{{ item.phone }}</div>
            </li>
          </ul>
        </div>
      </slot>
    </div>
  </div>
</template>
<script>
import ZrBigscreenTitle from '@views/zrBigscreens/modules/ZrBigscreenTitle.vue'
import vueSeamlessScroll from 'vue-seamless-scroll'
import resizeObserverMixin from '@views/statsCenter/com/resizeObserverMixin'
import { businessStatus,operatorList } from '@views/zrBigscreens/modules/zrUtil'

export default {
  name: 'ZrBusinessOperations',
  components: { ZrBigscreenTitle, vueSeamlessScroll },
  mixins: [resizeObserverMixin],
  data() {
    return {
      listData:operatorList,
      srollOption: {
        step: 0.5, // 步长
        speed: 100, // 滚动速度
        timer: 3000,// 滚动时间间隔
        autoPlay: false,
        limitMoveNum: 10000,
        singleHeight: 36 ,
      },
      maxNum: 0
    }
  },
  created() {
    this.mapList()
  },
  mounted() {
    this.$nextTick(()=>{
      this.setPlayState()
    })

  },
  methods: {
    mapList() {
      this.listData = this.listData.map(el => {
        let bs = businessStatus.find(item => item.value == el.status)
        if (bs) {
          el.color = bs.color
          el.statusText = bs.label
        }
        return el
      })
    },
    // 屏幕变化回调
    resizeObserverCb() {
      this.setPlayState()
    },
    //设置滚动状态
    setPlayState() {
      if (this.$refs.zrBusinessSupport && this.listData.length) {
        let bounded = this.$refs.zrBusinessSupport.getBoundingClientRect()
        this.maxNum = Math.floor((bounded.height - 51) / 36)
        if (this.maxNum>0 && this.maxNum < this.listData.length) {
          this.srollOption.limitMoveNum = this.maxNum
          this.srollOption.autoPlay = true
          return
        }
      }
      this.srollOption.autoPlay = false
      this.srollOption.limitMoveNum = this.listData.length + 1
    }
  }
}
</script>


<style scoped lang='less'>
.zr-business-support {
  height: 100%;

  .business-support-content {
    height: calc(100% - 51px);
    overflow: hidden;
    padding: 0 12px;
    background: linear-gradient(to right, rgba(29, 78, 140, 0.3), rgba(29, 78, 140, 0.0));
  }
}

.scroll-warp {
  height: 100%;
  overflow: hidden;
  max-width:410px;
  .scroll-list {
    width: 100%;
    height: 100%;
    margin: 0px;
    padding: 0px;
  }

  .scroll-list-item {
    display: flex;
    align-items: center;
    color: rgba(237, 245, 255, 0.95);
    font-size: 14px;
    justify-content: space-between;
    height: 36px;
    padding:0 calc(25 / 19.2 * 1vw);
    .item-name {
      width: 45%;
      //width: calc(100% - 270px);
      overflow: hidden;
      position: relative;
      //padding-left: 32px;
      line-height: 1;
      opacity: 0.95;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      //&::before {
      //  content: '';
      //  position: absolute;
      //  left: 16px;
      //  top: 3.5px;
      //  width: 6px;
      //  height: 6px;
      //  border-radius: 50%;
      //  background: var(--status-color);
      //}
    }

    .item-worker {
      width: 25%;
      //max-width: 180px;
      text-align: left;
      opacity: 0.95;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .item-phone {
      width: 30%;
      text-align: center;
      opacity: 0.95;
    }
  }

  .scroll-list-odd {
    background: rgba(29, 78, 140, 0.25);
  }
}
</style>