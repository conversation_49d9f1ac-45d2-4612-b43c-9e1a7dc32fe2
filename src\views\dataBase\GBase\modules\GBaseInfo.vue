<template>
  <j-modal :title='title' :width='width' :visible='visible' :destroyOnClose='true'
           :okButtonProps="{ class:{'jee-hidden': disableSubmit} }" :centered='true'
           :footer='null'
           @cancel='handleCancel'
           cancelText='关闭'>
    <div style='height: 75vh;overflow-y: auto'>
      <a-tabs v-model='curKey' @change="tabChange">

        <a-tab-pane key="1" tab="基本信息">
          <div style='width:100%;padding:0 5px'>
            <a-descriptions :column='{ xxl: 2, xl: 2, lg: 2, md: 2, sm: 2, xs: 2 }' bordered>
              <a-descriptions-item label='IP'>{{ record.dbIp }}</a-descriptions-item>
              <a-descriptions-item label='端口'>{{ record.dbPort }}</a-descriptions-item>
              <a-descriptions-item label='数据库名称'>{{ record.dbInstance }}</a-descriptions-item>
              <a-descriptions-item label='数据库用户名'>{{ record.dbUsername }}</a-descriptions-item>
              <a-descriptions-item label='数据库密码'>{{ record.dbPassword }}</a-descriptions-item>
              <a-descriptions-item label='SSH用户名'>{{ record.sshUsername }}</a-descriptions-item>
              <a-descriptions-item label='SSH密码'>{{ record.sshPassword }}</a-descriptions-item>
<!--              <a-descriptions-item label='秘钥文件'>{{ record.sshkey }}</a-descriptions-item>-->
              <a-descriptions-item label='SSH超时时间'>{{ record.sshTimeout }}</a-descriptions-item>
              <a-descriptions-item label='描述'>{{ record.description }}</a-descriptions-item>
            </a-descriptions>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>
  </j-modal>
</template>
<script>
import { ajaxGetDictItem } from '@api/api'
export default {
  name: 'JkdjInfo',
  components: {  },
  data() {
    return {
      title: '详情',
      width: '70vw',
      visible: false,
      disableSubmit: false,
      confirmLoading: false,
      record: {},
      datatypeList:[],
      curKey:"1",
    }
  },
  created() {

  },
  mounted() {

  },
  computed: {

  },
  methods: {
    // getDatatype(){
    //   ajaxGetDictItem("jkdj_data_type").then((res) => {
    //     if(res.success){
    //       this.datatypeList = res.result
    //     }
    //   })
    // },
    show(record) {
      this.record = record
      this.visible = true
    },
    handleCancel() {
      this.close()
    },
    handleOk() {
      this.close()
    },
    close() {
      this.visible = false
    },
    tabChange(){

    },
  }
}
</script>


<style scoped lang='less'>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';

/deep/.ant-table-tbody .ant-table-row td {
  padding-top: 5px;
  padding-bottom: 5px;
}
.colorBox1 {
  margin-bottom: 20px;
  margin-right: 1px;
}

.colorBox {
  margin-bottom: 10px;
}

.colorTotal {
  padding-left: 7px;
  border-left: 4px solid #1e3674;
}

::v-deep .ant-descriptions {
  width: 100%;
}
</style>