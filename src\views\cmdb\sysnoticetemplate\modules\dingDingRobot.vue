<template>
  <a-col :span='23'>
    <a-form-model-item class='two-words' label='标题' prop='subject' :rules='validatorRules.subject'>
      <a-input v-model='personalizedObject.subject' :allowClear='true' autocomplete='off' placeholder='请输入标题' @change='changeValue'/>
    </a-form-model-item>
  </a-col>
</template>

<script>
export default {
  name: 'dingDingRobot',
  data(){
    return{
      personalizedObject:{
        subject:''
      },
      validatorRules: {
        subject: [
          { required: true, validator: this.customValidate }
        ],
      },
    }
  },
  props: {
    data: {
      type: Object,
      required: false,
      default: () => {
        let personalizedObject = {
          subject: ''
        }
        return personalizedObject
      }
    }
  },

  watch:{
    data: {
      handler(val) {
        if (Object.keys(val).length > 0){
          this.personalizedObject = val
        }
      },
      deep: true,
      immediate: true,
    }
  },
  methods:{
    changeValue(e){
      this.$emit('changeModelValue', this.personalizedObject)
    },
    customValidate(rule, value, callback) {
      if (rule.required) {
        if (value && value.length > 0) {
          if(value.length<2||value.length>20){
            callback('标题长度应在 2-20 之间！')
          }else {
            callback()
          }
        } else {
          callback('请输入标题！')
        }
      } else {
        callback()
      }
    },
  }
}
</script>