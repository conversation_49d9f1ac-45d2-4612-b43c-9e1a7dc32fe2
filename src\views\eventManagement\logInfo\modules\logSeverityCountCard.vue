<template>
  <div style="padding: 0 0 10px 0">
    <a-row :gutter="[12, 12]" class="row-example">
      <a-col
        class="gutter-row"
        :xxl="colSpan.xxl"
        :xl="colSpan.xl"
        :lg="colSpan.lg"
        :md="colSpan.md"
        :sm="colSpan.sm"
        :xs="colSpan.xs"
        v-for="(item, index) in severityData"
        :key="index"
      >
        <div class="gutter-box" :class="`severity-${item.type}`">
          <div class="gutter-box-left">
            <div class="gutter-box-left-top">
              <span>{{ item.count }}</span>
            </div>
            <div class="gutter-box-left-bottom">
              <div class="dian"></div>
              {{ item.type }}
            </div>
          </div>
          <div class="gutter-box-right">
            <img v-if="item.img" :src="item.img" alt="" />
            <a-icon v-if="item.icon" :type="item.icon" class="icon" />
          </div>
        </div>
      </a-col>
    </a-row>
  </div>
</template>

<script>
export default {
  name: 'DeviceStatistic',
  props: {
    countInfo: {
      type: Object,
      required: true,
      default: () => {},
    },
    colSpan: {
      type: Object,
      required: false,
      default: () => {
        return {
          xxl: 3,
          xl: 3,
          lg: 4,
          md: 4,
          sm: 12,
          xs: 24,
        }
      },
    },
  },
  data() {
    return {}
  },
  watch: {
    countInfo: {
      handler(newVal, oldVal) {
        // 当 countInfo 变化时执行的操作
        console.log('countInfo 发生变化:', newVal)
      },
      deep: true,
      immediate: true,
    },
  },
  computed: {
    severityData() {
      return [
        { type: 'emerg', count: this.countInfo.emerg || 0 },
        { type: 'alert', count: this.countInfo.alert || 0 },
        { type: 'crit', count: this.countInfo.crit || 0 },
        { type: 'err', count: this.countInfo.err || 0 },
        { type: 'warning', count: this.countInfo.warning || 0 },
        { type: 'notice', count: this.countInfo.notice || 0 },
        { type: 'info', count: this.countInfo.info || 0 },
        { type: 'debug', count: this.countInfo.debug || 0 },
      ]
    },
  },
  methods: {},
}
</script>
<style lang="less" scoped>
// 新增8种日志级别的颜色样式
.row-example {
  .severity-emerg {
    background-color: #ff4d4f;
    background-image: linear-gradient(to right, #ff4d4f, #cf1322);
  }
  .severity-alert {
    background-color: #ff7a45;
    background-image: linear-gradient(to right, #ff7a45, #f0582e);
  }
  .severity-crit {
    background-color: #ffa940;
    background-image: linear-gradient(to right, #ffa940, #d46b08);
  }
  .severity-err {
    background-color: #ffc53d;
    background-image: linear-gradient(to right, #ffc53d, #f39e0b);
  }
  .severity-warning {
    background-color: #ffe600;
    background-image: linear-gradient(to right, #ffe600, #dfca14);
  }
  .severity-notice {
    // 通知 - 绿色
    background-color: #73d13d;
    background-image: linear-gradient(to right, #73d13d, #389e0d);
  }
  .severity-info {
    // 信息 - 蓝色
    background-color: #69c0ff;
    background-image: linear-gradient(to right, #69c0ff, #096dd9);
  }
  .severity-debug {
    // 调试 - 青色
    background-color: #70eee6;
    background-image: linear-gradient(to right, #70eee6, #48b4ad);
  }
}

.gutter-row {
  .gutter-box {
    position: relative;
    border-radius: 3px;
    box-shadow: 2px 2px 4px 1px rgba(0, 0, 0, 0.2);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-flow: row nowrap;

    .gutter-box-left {
      padding: 8px 20px 12px 20px;
      overflow: hidden;

      .gutter-box-left-top {
        display: flex;
        align-items: flex-end;

        span:nth-child(1) {
          font-family: 'Eurostile-Bold';
          font-size: 0.32rem /* 32/80 */;
          color: #ffffff;
          overflow: hidden;
          display: inline-block;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        span:nth-child(2) {
          font-family: 'HYk2gj';
          font-size: 14px;
          color: rgba(255, 255, 255, 0.85);
          line-height: 38px;
          margin-left: 11px;
        }
      }

      .gutter-box-left-bottom {
        font-family: 'Microsoft YaHei';
        //font-size: 0.2rem;
        font-size: 16px;
        color: rgba(255, 255, 255, 0.85);
        display: flex;
        align-items: center;
        white-space: nowrap;
        justify-content: start;
        text-align: start;

        .dian {
          width: 6px;
          height: 6px;
          background: #fff;
          margin-right: 8px;
        }
      }
    }

    .gutter-box-right {
      position: absolute;
      right: 5px;
      bottom: 5px;
      //align-self: end;

      img {
        width: 74px;
        height: 74px;
        opacity: 0.2;
      }
      .icon {
        font-size: 70px;
        color: rgba(255, 255, 255, 0.12);
      }
    }
  }
}

.sync-img {
  position: absolute;
  top: 12px;
  right: 12px;
  cursor: pointer;
  color: #fff;
  z-index: 100;
}
</style>
