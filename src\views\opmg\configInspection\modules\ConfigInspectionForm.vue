<template>
  <a-spin :spinning="confirmLoading">
    <div class="colorBox">
      <span class="colorTotal">巡检参数</span>
    </div>
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :sm="12" :xs="24">
            <a-form-item label="巡检任务名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['taskName', validatorRules.taskName]" :allowClear="true" autocomplete="off"
                       placeholder="请输入巡检任务名称" @change="changeTaskName"></a-input>
            </a-form-item>
          </a-col>
<!--          <a-col :sm="12" :xs="24">
            <a-form-item label="巡检类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag type="list" v-decorator="['inspectionType', validatorRules.inspectionValue]"
                                 :trigger-change="true" dictCode="inspection_type" placeholder="请选择巡检类型"
                                 @change="selectInspectionType" />
            </a-form-item>
          </a-col>-->
          <a-col :sm="12" :xs="24">
            <a-form-item label="是否开启推送" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-switch v-model="pushType" @click="changePushingType"></a-switch>
            </a-form-item>
          </a-col>
          <a-col :sm="12" :xs="24">
            <a-form-model-item label="通知模板" prop="pushAddress" v-bind="formItemLayout0">
              <a-select
                v-decorator="['pushAddress', { initialValue: undefined, rules: [{ required: pushType, message: '请选择通知模板' }]}]"
                :allow-clear="true" :disabled="!pushType" :show-search="true"
                option-filter-prop="label" placeholder="请选择通知模板">
                <a-select-option v-for="item in noticeTemplateList" :key="'noticeTemplate_'+item.type" :label="item.code"
                                 :value="item.type">
                  {{ item.code }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :sm="12" :xs="24">
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="状态">
              <j-dict-select-tag type="radio" v-decorator="[ 'taskStatus', {'initialValue':0}]"
                                 :trigger-change="true" dictCode="quartz_status" />
            </a-form-item>
          </a-col>
          <a-col :sm="12" :xs="24">
            <a-form-item label="周期执行" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <!-- cron表达式  -->
              <j-cron ref="innerVueCron" v-decorator="['taskexecuteTime',{ initialValue: '0 0 0 * * ? *', rules: [{ required: true,validator:this.validateCorn }]}]"
                      @change="setCorn"></j-cron>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>

    <!--      <a-divider />-->
    <div class="colorBox">
      <span class="colorTotal">产品模型选择</span>
    </div>

    <!--设备选择为空时，提示暂无数据     -->
    <div class="ant-empty ant-empty-normal" v-show="!isShowTable">
      <div class="ant-empty-image">
        <svg width="64" height="41" viewBox="0 0 64 41" xmlns="http://www.w3.org/2000/svg">
          <g transform="translate(0 1)" fill="none" fill-rule="evenodd">
            <ellipse fill="#F5F5F5" cx="32" cy="33" rx="32" ry="7" />
            <g fill-rule="nonzero" stroke="#D9D9D9">
              <path
                d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z" />
              <path
                d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z"
                fill="#FAFAFA" />
            </g>
          </g>
        </svg>
      </div>
      <p class="ant-empty-description" style="color: rgba(0, 0, 0, 0.35) !important">暂无数据</p>
    </div>

    <!--   巡检任务 新增表格 -->
    <div v-show="isShowTable">
      <div class="table-operator">
        <a-button @click="handleAddDevice" type="primary" :key="refreshKey">新增</a-button>
      </div>
      <a-table size="middle" bordered :row-key="
          (record, index) => {
            return record.id
          }
        " :columns="columns" :dataSource="dataSource" :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
               :pagination="ipagination" @change="handleTableChange">
        <span slot="action" slot-scope="text, record">
          <a @click="deleteTablbeData(record)">删除</a>
        </span>
      </a-table>
    </div>
    <config-selection-modal ref="modalForm" @loadTableData="loadTableData" @ok="tableOK"></config-selection-modal>
  </a-spin>
</template>

<script>
import {
  httpAction,
  getAction,
  deleteAction
} from '@/api/manage'
import pick from 'lodash.pick'
import { JeecgListMixin} from '@/mixins/JeecgListMixin'
import ConfigSelectionModal from './ConfigSelectionModal.vue'
import YqAreaCascaderSelect from '@comp/areaDict/YqAreaCascaderSelect.vue'
import { ValidateRequiredFields } from '@/utils/rules'
export default {
  name: 'ConfigInspectionForm',
  mixins: [JeecgListMixin],
  components: {
    YqAreaCascaderSelect,
    ConfigSelectionModal,
  },
  //表单模式：true流程表单 false普通表单
  formBpm: {
    type: Boolean,
    default: false,
    required: false,
  },
  //表单禁用
  disabled: {
    type: Boolean,
    default: false,
    required: false,
  },
  data() {
    return {
      form: this.$form.createForm(this),
      model: {},
      deviceList: [],
      labelCol: {
        xs: {
          span: 24,
        },
        sm: {
          span: 12,
        },
        md: {
          span: 10,
        },
        lg: {
          span: 8,
        },
      },
      wrapperCol: {
        xs: {
          span: 24,
        },
        sm: {
          span: 12,
        },
        md: {
          span: 14,
        },
        lg: {
          span: 16,
        },
      },
      confirmLoading: false,
      formItemLayout0: {
        labelCol: {
          span: 8
        },
        wrapperCol: {
          span: 16
        },
      },
      validatorRules: {
        taskName: {
          rules: [{
            required: true,
            validator: (rule, value, callback) => ValidateRequiredFields(rule, value, callback, '巡检任务名称', 30, 2)
          }]
        },
        inspectionValue: {
          rules: [{
            required: true,
            message: '请选择巡检类型',
          }]
        },
        taskexecuteTime: {
          rules: [{
            required: true,
            validator: this.validateCorn
          }]
        },
        // pushAddress: {
        //   rules: [{required: true, pattern: /^([a-z0-9_\.-]+)@([\da-z\.-]+)\.([a-z\.]{2,6})$/, message: '请输入正确的邮箱地址'}]
        // },
      },
      url: {
        add: '/inspection/metadataInspection/add',
        edit: '/inspection/metadataInspection/edit',
        queryById: '/inspection/metadataInspection/queryById',
        list: '/inspection/metadataInspection/getBindProductList',
        delete: '/inspection/metadataInspection/unbindProduct',
        noticeTemplateList: '/sys/notice/template/templateList', // 通知模板下拉数据,
        validateCorn: '/autoInspection/devopsAutoInspection/cronCheck'
      },
      oldTaskName: undefined,
      oldInspectionType: undefined,
      oldPushType: false,
      oldPushAddress: undefined,
      oldeExecuteTime: undefined,

      taskId: undefined,
      taskName: undefined,
      inspectionType: undefined,
      pushType: false,
      // pushAddress: undefined,
      executeTime: undefined,

      isShowTable: true,
      disableMixinCreated: true,
      columns: [
        {
          title: '模型名称',
          align: 'center',
          dataIndex: 'displayName',
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          scopedSlots: {
            customRender: 'action',
          },
        },
      ],
      dataSource: [],
      refreshKey: 0,
      validateStatus: 'success',
      tips: '',
      noticeTemplateList: [], //通知模板下拉数据
    }
  },
  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        return this.formData.disabled !== false;
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    },
  },
  created() {
    this.dataSource = []
    //如果是流程中表单，则需要加载流程表单data
    this.showFlowData()
  },
  mounted() {},
  methods: {
    tableOK(idList, list) {
      if(list&&list.length>0){
        list.forEach((ele) => {
          this.dataSource.push(ele)
        })
        let ids=this.dataSource.map((m)=>{return m.id})
        this.model.relatedIds=JSON.stringify(ids)
      }
    },
    validateCorn(rule, value, callback) {
      if (rule.required) {
        if (value && value.length > 0) {
          if (value.split(' ').length>7){
            callback('cron表达式格式错误!')
          }else{
            this.getValidateCornTips(value).then((res)=>{
              callback(res)
            }).catch((err)=>{
              callback(err)
            })
          }
        } else {
          callback('请输入或选择cron表达式')
        }
      } else {
        if (value && value.length > 0) {
          if (value.split(' ').length>7){
            callback('cron表达式格式错误!')
          }else{
            this.getValidateCornTips(value).then((res)=>{
              callback(res)
            }).catch((err)=>{
              callback(err)
            })
          }
        } else {
          callback()
        }
      }
    },

    getValidateCornTips(value){
      return new Promise((resolve, reject)=>{
        getAction(this.url.validateCorn, {
          cronExpression: value
        }).then((res) => {
          if (res.success) {
            resolve()
          } else {
            this.$message.warning(res.message)
            reject('cron表达式格式错误!')
          }
        }).catch((err)=>{
          this.$message.warning(err.message)
          reject('cron表达式格式错误!')
        })
      })
    },
    closeForm() {
      let that = this
      if (
        this.oldTaskName != this.taskName ||
        this.oldInspectionType != this.inspectionType ||
        this.oldeExecuteTime != this.executeTime
      ) {
        this.$confirm({
          title: '确认删除',
          type: 'warning',
          okText: '确认',
          cancelText: '取消',
          content: '有数据修改后未提交，确定直接关闭?',
          onOk: function () {
            that.$emit('closeForm')
          },
        })
      } else {
        that.$emit('closeForm')
      }
    },

    add() {
      this.edit({})
    },
    edit(record) {
      this.initColumns(record.inspectionType)
      //获取通知模板下拉数据
      this.getNoticeTemplateList()
      this.taskId = record.id
      this.taskName = record.taskName
      this.oldTaskName = record.taskName
      this.inspectionType = record.inspectionType
      this.oldInspectionType = record.inspectionType
      this.pushType = !!(record.pushType && record.pushType === 'Y')
      this.executeTime = record.taskexecuteTime
      this.oldeExecuteTime = record.taskexecuteTime

      if (null != this.taskId && undefined != this.taskId && '' != this.taskId) {
        this.loadTableData()
      } else {
        this.dataSource = []
      }
      // if (null != this.inspectionType && undefined != this.inspectionType) {
      //   this.isShowTable = true
      // } else {
      //   this.isShowTable = false
      // }
      this.refreshKey++
      this.form.resetFields()
      this.model = Object.assign({}, record)
      if(!this.model.pushAddress||this.model.pushAddress.length===0){
        this.model.pushAddress=undefined
      }
      this.visible = true
      this.$nextTick(() => {
        this.formInit(this.model)
      })
    },
    handleTableChange(pagination, filters, sorter) {
      //分页、排序、筛选变化时触发
      //TODO 筛选
      if (Object.keys(sorter).length > 0) {
        this.isorter.column = sorter.field
        this.isorter.order = 'ascend' == sorter.order ? 'asc' : 'desc'
      }
      this.ipagination = pagination
      if(this.taskId){
        this.loadData()
      }
    },
    formInit(pickData) {
      this.form.setFieldsValue(
        pick(
          pickData,
          'taskName',
          // 'reportType',
          'taskexecuteTime',
          // +'pushType',
          'pushAddress',
          // 'taskEndTime',
          'taskStatus',
          // 'executeCounts',
          'inspectionType'
        )
      )
    },

    /*获取通知模板下拉数据*/
    getNoticeTemplateList() {
      this.confirmLoading = true
      let route = this.$route.fullPath
      getAction(this.url.noticeTemplateList, {
        business: route
      })
        .then((res) => {
          this.noticeTemplateList = res.result
        })
        .finally(() => {
          this.confirmLoading = false
        })
    },

    //提交
    submitForm() {
      const that = this
      // 触发表单验证
      // let isError = this.validatePushAddress(this.pushType, this.pushAddress)
      this.form.validateFields((err, values) => {
        if (
          !err
          //  && !isError
        ) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }

          let formData = Object.assign(this.model, values)
          formData.relatedIds = this.model.relatedIds != null ? this.model.relatedIds : "[]"
          formData.id = that.taskId
          if (typeof formData.pushType === 'undefined' || formData.pushType === false) {
            formData.pushType = 'N'
          } else if (formData.pushType === true) {
            formData.pushType = 'Y'
          }
          if (!formData.pushAddress) {
            formData.pushAddress = '';
          }
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.oldTaskName = that.taskName
                that.oldInspectionType = that.inspectionType
                that.oldeExecuteTime = that.executeTime

                if (!this.model.id) {
                  that.taskId = res.result
                  that.queryParam.id = that.taskId
                }
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    //改变任务名称
    changeTaskName(val) {
      this.taskName = val.target.value
    },
    //巡检报告推送
    changePushingType(checked) {
      this.isPush = checked
      this.pushType = checked
      this.model.pushType = checked
      if (checked == false) {
        this.form.resetFields('pushAddress')
        this.model.pushAddress = undefined
        this.pushAddress = undefined
      }
    },
    //校验推动地址
    // validatePushAddress(isValidate, pushAddress) {
    //   let isError = false
    //   this.tips = ''
    //   this.validateStatus = 'success'
    //   if (isValidate) {
    //     let regPone = /^([a-z0-9_\.-]+)@([\da-z\.-]+)\.([a-z\.]{2,6})$/
    //     if (!regPone.test(pushAddress)) {
    //       this.tips = '请输入正确的邮箱地址'
    //       this.validateStatus = 'error'
    //       isError = true
    //     }
    //   }
    //   return isError
    // },
    //改变推送地址
    // changePushAddress(val) {
    //   this.pushAddress = val.target.value
    //   this.validatePushAddress(this.pushType, this.pushAddress)
    // },
    //选择巡检类型
    selectInspectionType(checked) {
      this.deviceList = []
      this.isShowTable = true
      this.inspectionType = checked
      if (checked == undefined) {
        this.isShowTable = false
      } else {
        this.isShowTable = true
      }
      this.initColumns(this.inspectionType)
      if (this.taskId) {
        this.loadTableData()
      } else {
        this.clear()
      }
    },
    //根据巡检类型，初始化table字段
    initColumns(inspectionType) {
      if (inspectionType == '1') {
        this.columns = [{
          title: '产品名称',
          align: 'center',
          dataIndex: 'categoryName',
        },
          {
            title: '设备名称',
            align: 'center',
            dataIndex: 'name',
          },
          {
            title: 'IP',
            align: 'center',
            dataIndex: 'ip',
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            scopedSlots: {
              customRender: 'action',
            },
          },
        ]
      } else if (inspectionType == '2') {
        this.columns = [{
          title: '应用名称',
          align: 'center',
          dataIndex: 'name',
        },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            scopedSlots: {
              customRender: 'action',
            },
          },
        ]
      }
    },
    //table中操作删除数据
    deleteTablbeData(record) {
      if (null == this.taskId || '' == this.taskId) {
        this.dataSource.forEach((ele, index) => {
          if (this.dataSource[index].id == record.id) {
            this.dataSource.splice(index, 1)
          }
          let ids=this.dataSource.map((m)=>{return m.id})
          this.model.relatedIds=JSON.stringify(ids)
        })
      } else {
        let param = {
          id: this.taskId,
          productId: record.id,
        }
        deleteAction(this.url.delete, param).then((res) => {
          if (res.success) {
            this.$message.success(res.message)
            this.loadTableData()
          } else {
            this.$message.warning(res.message)
          }
        })
      }
    },
    //新增添加设备
    handleAddDevice: function () {
      let record = {
        id: this.taskId,
        inspectionType: this.inspectionType
      }
      this.$refs.modalForm.edit(record, this.model.relatedIds)
      this.$refs.modalForm.title = '产品模型添加'
      this.$refs.modalForm.disableSubmit = false
    },
    //向后端请求table数据
    loadTableData() {
      this.queryParam.id = this.taskId
      this.queryParam.inspectionType = this.inspectionType
      this.loading = true
      this.loadData()
    },
    //周期执行
    setCorn(data) {
      if (data && data.target != null) {
        let dataList = data.target.value.split(' ')
        if (dataList[0] === '*') {
          this.$message.warning('请确认是否每秒都执行')
        }
      } else {
        let dataList = data.split(' ')
        if (dataList[0] === '*') {
          this.$message.warning('请确认是否每秒都执行')
        }
      }
      this.$nextTick(() => {
        this.model.cronExpression = data;
      })

      if (Object.keys(data).length === 0) {
        this.$message.warning('请输入cron表达式!');
      }
    },
    //巡检类型未选择时或者选择类型后又切换至未选择时，设备选择不显示，且在未确定前，原来已新增的数据清空
    clear: function () {
      this.dataSource = []
    },
    //渲染流程表单数据
    showFlowData() {
      if (this.formBpm === true) {
        let params = {
          id: this.formData.dataId,
        }
        getAction(this.url.queryById, params).then((res) => {
          if (res.success) {
            this.edit(res.result)
          }
        })
      }
    },
    popupCallback(row) {
      this.formInit(row)
    },
  },
}
</script>
<style scoped lang='less'>
.colorBox {
  margin-top: 25px;
  margin-bottom: 18px;
}

.colorTotal {
  padding-left: 7px;
  border-left: 4px solid #1e3674;
}

/* 定义滚动条样式 */
::-webkit-scrollbar {
  width: 0.15rem
  /* 12/80 */
;
  // height: 6px;
  //background-color: #222325;
}

/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track {
  box-shadow: inset 0 0 0px rgba(240, 240, 240, 0.5);
  // border-radius: 50%;
  background-color: #eaeaea;
  border-radius: 0.1rem
  /* 8/80 */
;
}

/*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
  border-radius: 0.1rem
  /* 8/80 */
;
  box-shadow: inset 0 0 0px rgba(240, 240, 240, 0.5);
  background-color: #d6d6d6;
}

::v-deep .ant-spin-nested-loading {
  padding-right: 13px;
}
</style>