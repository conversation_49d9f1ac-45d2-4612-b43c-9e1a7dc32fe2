<template>
  <div class='zr-business-view' ref='zrBusinessView'>
    <div class='business-view-left'>
      <zr-national-nodes></zr-national-nodes>
<!--      <div class='business-view-left-top' ref='lefTop'>
        <zr-national-op-status></zr-national-op-status>
      </div>
      <div  class='business-view-left-bottom' style='height: calc(100% - 298px - 20px);'>
        <zr-national-devices ref='map'></zr-national-devices>
      </div>-->
    </div>
    <div class='business-view-center' >
      <zr-status-info></zr-status-info>
     <div class='map-box'>
       <yq-chart-map-migrate  ref='map'></yq-chart-map-migrate>
     </div>
      <div class='legend-block'>
        <zr-focus-info></zr-focus-info>
        <div class='legend-block-list'>
          <div class='legend-block-list-item' v-for='item in unitLevels' :key='"u_"+item.value'>
            <div v-if='item.value===1' class='legend-block-list-item-icon level-circle' ></div>
            <div v-if='item.value===2' class='legend-block-list-item-icon level-rect'></div>
            <div v-if='item.value===3' class='legend-block-list-item-icon level-diamond'></div>
            <div class='legend-block-list-item-text'>{{item.label}}</div>
          </div>
          <div class='legend-block-list-item' v-for='item in businessStatus' :key='"s_"+item.value'>
            <div class='legend-block-list-item-icon' :style='{backgroundColor:item.color}'></div>
            <div class='legend-block-list-item-text'>{{item.label}}</div>
          </div>
        </div>
      </div>
    </div>
    <div class='business-view-right'>
      <zr-national-businesses></zr-national-businesses>
    </div>
  </div>
</template>
<script>
import resizeObserverMixin from '@views/statsCenter/com/resizeObserverMixin'
import { businessStatus,unitLevels } from '@views/zrBigscreenStatic/modules/zrUtil'
import ZrNationalOpStatus from '@views/zrBigscreenStatic/zrCompNational/modules/ZrNationalOpStatus.vue'
import ZrNationalDevices from '@views/zrBigscreenStatic/zrCompNational/modules/ZrNationalDevices.vue'
import ZrNationalNodes from '@views/zrBigscreenStatic/zrCompNational/modules/ZrNationalNodes.vue'
import ZrStatusInfo from '@views/zrBigscreenStatic/zrCompNational/modules/ZrStatusInfo.vue'
import ZrFocusInfo from '@views/zrBigscreenStatic/zrCompNational/modules/ZrFocusInfo.vue'
import ZrNationalBusinesses from '@views/zrBigscreenStatic/zrCompNational/modules/ZrNationalBusinesses.vue'
import YqChartMapMigrate from '@views/zrBigscreenStatic/zrCompNational/modules/YqChartMapMigrate.vue'
export default {
  name: 'businessIndex',
  components: {
    ZrNationalBusinesses,
    ZrNationalDevices,
    ZrNationalOpStatus,
    YqChartMapMigrate,
    ZrNationalNodes,
    ZrStatusInfo,
    ZrFocusInfo
  },
  mixins: [resizeObserverMixin],
  data() {
    return {
      centerH: '',
      mapH: '',
      centerPd: "",
      legndBottom:"",
      businessStatus,
      unitLevels
    }
  },
  mounted() {
  },
  methods: {
    //监听页面缩放 更新中间区域高度
    resizeObserverCb() {
      let hScaleValue = window.innerHeight / 1080
      this.centerH = `calc(100% -  (70px * ${hScaleValue}))`
      this.centerPd = `calc(120px * ${hScaleValue})`
      this.legndBottom = `calc(60px * ${hScaleValue})`
      this.$nextTick(()=>{
        if(this.$refs.map && this.$refs.map.resize) {
          this.$refs.map.resize()
        }
      })
    }
  }
}
</script>

<style scoped lang='less'>
.zr-business-view {
  padding-left: calc(33 / 19.2 * 1vw);
  padding-right: calc(33 / 19.2 * 1vw);
  display: flex;
  align-items: center;
  justify-content: space-around;
  height: 100%;

  .business-view-left {
    width: 25%;
    height: 100%;
    //background-image: url(/zrBigScreen/zrBusiness/businessLeftBg.png);
    //background-size: 100% 100%;
    //background-repeat: no-repeat;
    padding: 0px;
    display: flex;
    flex-direction: column;

    .business-view-left-bottom {
      display: flex;
      flex-direction: column;

      .business-view-left-middle {
        height: 50%
      }

      .business-view-left-bottom {
        height: calc(50% - 8px);
      }
    }
  }

  .business-view-center {
    width: 50%;
    height: 100%;
    //background-image: url(/zrBigScreen/zrBusiness/businessCenterBg.png);
    //background-size: 100% 100%;
    //background-repeat: no-repeat;
    padding: 12px;
    position: relative;
    .map-box{
      height: calc(100% - 110px);
    }
    .status-info {
      font-size: 20px;
      font-weight: bold;
      color: #E3E7EF;
      text-align: center;
    }

    .legend-block {
      position: absolute;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width:100%;
      .legend-block-list {
        display: flex;
        .legend-block-list-item {
          display: flex;
          align-items: center;
          font-weight: 400;
          font-size: 14px;
          padding: 0 20px;
          color: #E3E7EF;
          .legend-block-list-item-icon{
            width:14px;
            height: 14px;
            margin-right: 12px;
            background-color: #ffffff;
          }
          .level-circle {
            border-radius: 50%;
          }
          .level-rect {
            width: 14px;
            height: 14px;
          }
          .level-diamond {
            width: 14px;
            height: 14px;
            transform: rotate(45deg);
          }
        }
      }
    }
  }

  .business-view-right {
    width: 25%;
    height: 100%;
    //background-image: url(/zrBigScreen/zrBusiness/businessRightBg.png);
    //background-size: 100% 100%;
    //background-repeat: no-repeat;
    padding: 0px;
    display: flex;
    flex-direction: column;
  }
}
</style>