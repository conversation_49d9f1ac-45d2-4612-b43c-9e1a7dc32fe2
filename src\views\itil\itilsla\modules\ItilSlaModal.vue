<template>
  <a-modal
    :title='title'
    :width='width'
    :visible='visible'
    :centered="true"
    :confirmLoading='confirmLoading'
    switchFullscreen
    @ok='handleOk'
    @cancel='handleCancel'
    cancelText='关闭'>
    <a-spin :spinning='confirmLoading'>
      <a-form :form='form'>
        <a-row>
          <a-col :span=24>
            <a-form-item label='流程类型' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <!-- <a-input v-decorator="['pathType']" placeholder="请输入流程类型"></a-input> -->
              <component
                :is='LcDict'
                :trigger-change='true'
                v-decorator="[ 'pathType', { rules: [{ required: true, message: '请选择流程类型' }] },]"
                placeholder='请选择流程类型'
                dictCode='bpm_process_type'
              ></component>
            </a-form-item>
          </a-col>
          <a-col :span=24>
            <a-form-item label='优先级' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <!-- <a-input v-decorator="['priority']" placeholder="请输入优先级"></a-input> -->
              <component
                :is='LcDict'
                :trigger-change='true'
                v-decorator="[ 'priority', { rules: [{ required: true, message: '请选择优先级' }] },]"
                placeholder='请选择优先级'
                dictCode='priority'
              ></component>
            </a-form-item>
          </a-col>
          <!--        <a-form-item label="SLA响应时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    &lt;!&ndash; <a-input v-decorator="['responseTime', { rules: [{ required: true, message: '不能为空' }] },]" placeholder="请输入SLA响应时间"></a-input> &ndash;&gt;
                    <a-row>
                      <a-col :span="12">
                        <a-input-number placeholder="请响应时间" style="width:100px" :min="0" v-decorator="[ 'responseDtime',validatorRules.responseDtime]" /> 天（d）
                      </a-col>
                      <a-col :span="12">
                        <a-input-number placeholder="请响应时间" style="width:100px" :min="0" v-decorator="[ 'responseHtime',validatorRules.responseHtime]" /> 小时（h）
                      </a-col>
                    </a-row>
                  </a-form-item>
                  <a-form-item label="SLA完成时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-row>
                      <a-col :span="12">
                        <a-input-number placeholder="完成时间" style="width:100px" :min="0" v-decorator="[ 'completeDtime',validatorRules.completeDtime]" /> 天（d）
                      </a-col>
                      <a-col :span="12">
                        <a-input-number placeholder="完成时间" style="width:100px" :min="0" v-decorator="[ 'completeHtime',validatorRules.completeHtime]" /> 小时（h）
                      </a-col>
                    </a-row>
                  </a-form-item>-->
          <a-col :span=24 style='white-space: nowrap'>
            <a-form-item label='SLA响应时间' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <a-input-number placeholder='响应时间' style='width:100px' :min='0'
                              v-decorator="[ 'responseDtime',validatorRules.responseDtime]" />
              天（d）
            </a-form-item>
            <a-form-item label='         ' :labelCol='labelCol' :wrapperCol='wrapperCol' :colon='false'>
              <a-input-number placeholder='请响应时间' style='width:100px' :min='0'
                              v-decorator="[ 'responseHtime',validatorRules.responseHtime]" />
              小时（h）
            </a-form-item>
          </a-col>
          <a-col :span=24 style='white-space: nowrap'>
            <a-form-item  label='SLA完成时间' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <a-input-number placeholder='完成时间' style='width:100px' :min='0' v-decorator="[ 'completeDtime',validatorRules.completeDtime]" />
              天（d）
            </a-form-item>
            <a-form-item  label='         ' :labelCol='labelCol' :wrapperCol='wrapperCol' :colon='false'>
              <a-input-number placeholder='完成时间' style='width:100px' :min='0' v-decorator="[ 'completeHtime',validatorRules.completeHtime]" />
              小时（h）
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>

import { httpAction } from '@/api/manage'
import pick from 'lodash.pick'
import { validateDuplicateValue } from '@/utils/util'


export default {
  name: 'ItilSlaModal',
  components: {},
  data() {
    return {
      form: this.$form.createForm(this),
      title: '操作',
      width: '800px',
      visible: false,
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      confirmLoading: false,
      validatorRules: {
        responseDtime: { initialValue: 0.0 },
        responseHtime: { initialValue: 0.0 },
        completeDtime: { initialValue: 0.0 },
        completeHtime: { initialValue: 0.0 }
      },
      url: {
        add: '/itilsla/itilSla/add',
        edit: '/itilsla/itilSla/edit'
      }
    }
  },
  computed: {

    //可行性测试，根据文件路径动态加载组件
    LcDict: function() {
      var myComponent = () => import(`@/components/dict/JDictSelectTag`)
      return myComponent
    }
  },
  created() {
  },
  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(pick(this.model, 'pathType', 'priority', 'responseDtime', 'completeDtime', 'responseHtime', 'completeHtime'))
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let formData = Object.assign(this.model, values)
          httpAction(httpurl, formData, method).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.$emit('ok')
            } else {
              that.$message.warning(res.message)
            }
          }).finally(() => {
            that.confirmLoading = false
            that.close()
          })
        }

      })
    },
    handleCancel() {
      this.close()
    },
    popupCallback(row) {
      this.form.setFieldsValue(pick(row, 'pathType', 'priority', 'responseDtime', 'completeDtime', 'responseHtime', 'completeHtime'))
    }


  }
}
</script>
<style lang="less" scoped>
::v-deep .ant-modal-body {
  padding: 24px 48px 24px 48px;
}
::v-deep .ant-modal {
  padding: 24px;
}
@media (max-width: 812px) {
  ::v-deep .ant-modal {
    max-width: calc(100vw - 12px);
    margin: 0;
  }
}
</style>