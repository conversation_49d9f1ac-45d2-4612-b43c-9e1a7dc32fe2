<template>
<div id="config-container" style='height: 100%;line-height:21px '>
  <textarea :id="nodeId"></textarea>
</div>
</template>
<script>
// import dedent from 'dedent'
import CodeMirror from 'codemirror'
import 'codemirror/lib/codemirror.css'
import 'codemirror/mode/shell/shell.js'
/*import 'codemirror/addon/hint/show-hint.js'
import 'codemirror/addon/hint/anyword-hint.js'
import 'codemirror/addon/hint/show-hint.css'*/
export default {
  name: 'CommandInput',
  model:{
    prop:'cmdStr',
    event:'changeCommand'
  },
  props:['nodeId','cmdStr','showRealValue'],
  data() {
    return {
      textEditor:null,
    }
  },
  watch:{
    cmdStr(val){
      if(val&&val.length>0){
        this.textEditor.setValue(val);
      }
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init(){
      let that=this
      let container=document.getElementById(that.nodeId)
      that.textEditor = CodeMirror.fromTextArea(container, {
        tabSize: 4,
        // styleActiveLine: true,
        lineNumbers: true,
        lineWrapping: true,
        // line: true,
        mode: 'text/x-sh',
        readOnly: false,
      });

      // var deviceConfig =dedent+`${that.cmdStr}`; // 调用获取设备配置信息的函数
      var deviceConfig =that.cmdStr // 调用获取设备配置信息的函数
      that.textEditor.setValue(deviceConfig);
      that.editorEvents()
    },
    editorEvents() {
      // 代码输入的双向绑定
      this.textEditor.on('change', (editor) => {
        let cmdStr = editor.getValue()
       let tempStr=cmdStr.replace(/\s+/g,"");
        if (!this.showRealValue&&tempStr.length<=0&&cmdStr.length==0) {
          this.$emit('changeCommand', tempStr)
        }
      })
      this.textEditor.on('blur', (editor) => {
        let cmdStr= editor.getValue()
        let tempStr=cmdStr.replace(/\s+/g,"");
        if (!this.showRealValue){
          this.$emit('changeCommand',tempStr.length>0? cmdStr:tempStr)
        }else {
          this.$emit('changeCommand',cmdStr)
        }
      })
    }
  }
}
</script>
<style scoped lang='less'>
@import '~@assets/less/normalModal.less';

::v-deep .CodeMirror {
  border: 1px solid #ddd;
  background: #fff !important;
  color: #000 !important;
  height: 126px;
  width: 100%;

  div {
    color: #000 !important;
  }

  span {
    color: #000 !important;
  }

  .CodeMirror-linenumber {
    padding: 0 !important;
  }
}
</style>