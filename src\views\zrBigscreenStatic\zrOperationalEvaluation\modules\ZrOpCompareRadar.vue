<template>
  <div class='char-content'>
    <div id='compareRadar' ref='radarChart'></div>
  </div>
</template>
<script>
import ZrBigscreenTitle from '@views/zrBigscreens/modules/ZrBigscreenTitle.vue'
import resizeObserverMixin from '@views/statsCenter/com/resizeObserverMixin'
import{mapBackIcon,simpleBackIcon} from '@views/zrBigscreens/modules/zrUtil'
export default {
  name: 'ZrOpCompareRadar',
  components: { ZrBigscreenTitle },
  mixins: [resizeObserverMixin],
  props:{
    category:{
      type:String,
      default:''
    }
  },
  data() {
    return {
      chart: null,
      radarData:window.zrIndicatorTypes || [
        { name: '基础设施', score: 99,level: 'A' ,id:"jzss",type:"category"},
        { name: '数据资源', score: 80,level: 'A' ,id:"sjzy",type:"category"},
        { name: '网络通联', score: 88,level: 'B',id:"wltl" ,type:"category"},
        { name: '应用服务', score: 81,level: 'B' ,id:"yyfw",type:"category"},
        { name: '运维保障', score: 74,level: 'C' ,id:"ywbz",type:"category"},
        { name: '质量效益', score: 60,level: 'D' ,id:"zlxy",type:"category"}
      ],
      radarData1:window.zrIndicatorCompares || [
        { name: '基础设施', score: 80,level: 'B' ,id:"jzss" },
        { name: '数据资源', score: 100,level: 'A' ,id:"sjzy" },
        { name: '网络通联', score: 80,level: 'B' ,id:"wltl" },
        { name: '应用服务', score: 75,level: 'C' ,id:"yyfw" },
        { name: '运维保障', score: 80,level: 'B' ,id:"ywbz" },
        { name: '质量效益', score: 100,level: 'A' ,id:"zlxy" }
      ],
    }
  },
  mounted() {
    this.chart = this.$echarts.init(this.$refs.radarChart)
    this.initRadarChart()
  },
  methods: {
    // 初始化雷达图
    initRadarChart() {
      let that = this;
      let option = {
        color: ['#89F7FE', '#1C7BFF'],
        legend: {
          top:20,
          textStyle:{
            color:'#fff'
          },
          data: ['2025-07', '2024-12']
        },
        toolbox: {
          show: true,
          orient: 'vertical',
          left: 'right',
          top: 'top',
          feature: {
            myBack: {
              show: true,
              title: "返回",
              iconStyle: {
                color: "rgba(29, 78, 140, 1)",
                borderWidth: 0,
              },
              emphasis:{
                iconStyle: {
                  color: "rgba(29, 78, 140, 1)",
                },
              },
              icon: mapBackIcon,
              onclick: function() {
                that.backMapHandler()
              }
            }
          }
        },
        //工具按钮
        tooltip: {
          trigger: 'item',
          formatter: (params) => {
            let tData = params.data.radarData;
            if(!tData){return ""}
            let str = '';
            tData.forEach(item => {
              str += `<span style='color:#ccc'>${item.name}</span>： ${item.level}<br/>`;
            });
            return `${params.data.name}<br/>${str}`;
          }
        },
        radar: [
          {
            //指标
            indicator: this.radarData.map(item => ({ name: item.name, max: 100,color:this.category===item.id?"rgba(250, 226, 70, 0.9)":'rgba(255, 255, 255, 1)'})),
            center: ['50%', '50%'],
            radius:"75%",
            startAngle: 90,
            splitNumber: 4,
            shape: 'polygon',
            nameGap: 5,
            axisLabel: {
              color: 'rgba(255, 255, 255, 1)',
            },
            splitArea: {
              areaStyle: {
                color: ['#0080CD', '#015199', '#023769', 'rgba(56,154,255,0.2)'],
                shadowColor: 'rgba(0, 0, 0, 0.2)',
                shadowBlur: 0
              }
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: 'rgba(211, 253, 250, 0.8)'
              }
            },
            splitLine: {
              show: false,
              lineStyle: {
                color: 'rgba(211, 253, 250, 0.8)'
              }
            }
          },
        ],
        series: [
          {
            type: 'radar',
            symbolSize: 8,
            emphasis: {
              lineStyle: {
                width: 4
              }
            },
            data: [
              {
                radarData:this.radarData,
                value: this.radarData.map(item => item.score),
                name: '2025-07'
              },
              {
                radarData:this.radarData1,
                value: this.radarData1.map(item => item.score),
                name: '2024-12'
              },
            ]
          }
        ]
      }
      this.chart.setOption(option)
    },
    backMapHandler() {
      this.$emit('cancelCompare')
    },
    resize() {
      if (this.chart) {
        this.chart.resize()
      }
    },
    resizeObserverCb() {
     this.resize()
    }
  }
}
</script>

<style scoped lang='less'>
.char-content {
  width: 100%;
  height: 100%;
  position: relative;
  #compareRadar {
    width: 100%;
    height: 100%;
  }
}
</style>