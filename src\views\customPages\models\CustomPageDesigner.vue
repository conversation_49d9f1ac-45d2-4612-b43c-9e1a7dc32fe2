<template>
  <a-modal
    :footer='null'
    :visible='visible'
    width='100%'
    destroyOnClose
    :dialog-style="{ top: '0px',padding:'0px',height:'100%' }"
    :closable='false'
    :bodyStyle='{height:"100%",padding:"0px"}'
    dialogClass='custom-designer-modal'
  >
    <div class='custom-designer'>
      <div class='custom-designer-top'>
        <div class='pageName'>
          <span class='fix-text'>{{edit?"编辑":""}}自定义页面: </span>
          <div contenteditable='false' class='name-text'>{{templateInfo?templateInfo.name:"" }}</div>
        </div>
        <div style='flex: 1'></div>
<!--        <a-button class='operate-btn' icon='eye' @click='previewSet' v-if='!isPreview && edit'>-->
<!--          预览-->
<!--        </a-button>-->
        <a-button class='operate-btn' icon='save' @click='save' v-if='!isPreview'>
          保存
        </a-button>
        <a-button class='operate-btn' icon='close' @click='hide'>
          关闭
        </a-button>
      </div>
      <div class='custom-designer-content' :style='{paddingTop:isPreview?"66px":"50px"}'>
        <div class='attr-bar' v-if='!isPreview'>
          <custom-designer-bar
            ref='bar'
            :block-id='blockId'
            :selectRow='selectRow'
            @addRow='addRow'
            @delRow='delRow'
            @addCol='addCol'
            @delCol='delCol'
            @setBlockId='setBlockId'
          ></custom-designer-bar>
        </div>
        <div class='factory-area' @click='viewClick' ref='factoryArea'>
          <custom-designer-view
            :edit='edit'
            :row-list='rowList'
            :block-id='blockId'
            @setBlockId='setBlockId'
            @setComponent='setComponent'
          ></custom-designer-view>
        </div>
      </div>
    </div>
  </a-modal>
</template>
<script>
import CustomDesignerBar from '@views/customPages/models/CustomDesignerBar.vue'
import CustomDesignerView from '@views/customPages/models/CustomDesignerView.vue'
import { getAction, putAction ,postAction } from '@api/manage'

export default {
  name: 'CustomPageDesigner',
  components: { CustomDesignerView, CustomDesignerBar },
  props: {
    reset: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      visible: false,
      rowList: [],
      blockId: '',
      selectRow: null,
      templateInfo:null,
      isPreview:false,
      edit:true,
      userPageEdit:false,
    }
  },
  created() {
    if (this.$ls.get('CUSTOM_PAGE_TEST')) {
      this.$ls.remove('CUSTOM_PAGE_TEST')
    }
  },
  mounted() {

  },
  watch: {
    blockId: {
      immediate: false,
      handler() {
        if (this.blockId) {
          this.selectRow = this.rowList.find(el => el.id === this.blockId)
          this.$refs.bar.activeKey = 'attr'
        } else {
          this.selectRow = null
          this.$refs.bar.activeKey = 'grid'
        }
      }
    }
  },
  methods: {
    //设置列的组件
    setComponent(item, col) {
      let row = this.rowList.find(el => el.id === col.rowId)
      if (row) {
        let colItem = row.cols.find(el => el.id === col.id)
        if (colItem) {
          colItem.componentName = item.componentName
          colItem.componentPath = item.componentPath
        }
      }


    },
    //添加行
    addRow(row) {
      this.rowList.push(row)
      this.$nextTick(() => {
        let factoryArea = this.$refs.factoryArea
        factoryArea.scrollBy({
          top: factoryArea.scrollHeight - factoryArea.scrollTop,
          left: 0,
          behavior: 'smooth'
        })
      })

    },
    //删除行
    delRow() {
      let idx = this.rowList.findIndex(el => el.id === this.blockId)
      this.rowList.splice(idx, 1)
      this.selectRow = null
      this.blockId = ''
    },
    // 添加列
    addCol(col) {
      if (this.selectRow) {
        this.selectRow.cols.push(col)
      }
    },
    //删除列
    delCol(colId) {
      if (this.selectRow) {
        let colIndex = this.selectRow.cols.findIndex(el => el.id === colId)
        this.selectRow.cols.splice(colIndex, 1)
      }

    },
    //行列区域空白处点击
    viewClick(e) {
      this.blockId = ''
    },
    //选择行
    setBlockId(blockId) {
      this.blockId = blockId || ''
    },
    //显示页面编辑器 编辑页面模板
    show(pageInfo) {
      this.userPageEdit = false;
      this.templateInfo = pageInfo;
      getAction('/custom/template/queryById', { id: pageInfo.id })
        .then(res => {
          if (res.success && res.result && res.result.templateData) {
            this.rowList = JSON.parse(res.result.templateData)
            this.visible = true
          } else {
            this.$message.warning('没有该模板数据！')
            this.hide()
          }
        }).catch(err=>{
        this.$message.warning('获取该模板数据失败！')
        this.hide()
      })


    },
    //隐藏页面编辑器
    hide() {
      this.visible = false
      this.blockId = ''
      this.rowList = [];
      this.selectRow = null;
      this.isPreview = false;
      this.edit = true;
      this.$emit('reset')
    },
    //编辑用户的页面配置
    editUserPage(list,pageInfo,pageName){
      this.userPageEdit = true;
      this.templateInfo = pageInfo;
      this.templateInfo.name = pageName;
      this.rowList = list;
      this.visible = true;

    },
    //保存数据;
    save() {
      if(this.userPageEdit){
        //保存用户页面数据
        postAction("/user/pageInfo/add", {
          pageType: "ywgzt",
          pageData:JSON.stringify(this.rowList),
        }).then((res) => {
          if (res.code == 200) {
            this.$message.success("保存成功！")
          } else {
            this.$message.warning(res.message)
          }
        })

      }else{
        //保存模板数据
        putAction("/custom/template/edit", {
          id: this.templateInfo.id,
          name: this.templateInfo.name,
          showType: this.templateInfo.showType,
          templateData:JSON.stringify(this.rowList),
          image:this.templateInfo.image,
        }).then((res) => {
          if (res.code == 200) {
            this.$message.success("保存成功！")
          } else {
            this.$message.warning(res.message)
          }
        })
      }

    },
    //预览编辑页面
    previewSet(){
      this.isPreview = true;
      this.edit = false;
    },
    preview(pageInfo) {
        this.isPreview = true;
        this.edit = false;
        this.show(pageInfo)
    }
  }
}
</script>


<style scoped lang='less'>
/deep/ .custom-designer-modal {
  .ant-modal-content {
    height: 100%;
  }
}

.custom-designer {
  background: #fff;
  height: 100%;

  .custom-designer-top {
    display: flex;
    align-items: center;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 50px;
    padding: 0 24px;
    z-index: 9;
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, .24);

    .pageName {
      display: flex;
      align-items: center;
      font-size: 17px;

      .fix-text {
        font-weight: 700;
        margin-right: 10px;
      }

      .name-text {
        outline: 1px solid transparent;
        box-sizing: border-box;
        max-width: 240px;
        margin-top: 1px;
        padding: 0 10px;
        border-bottom: 1px dashed #9e9e9e;
        cursor: pointer;
      }

    }

    .operate-btn {
      outline: none;
      position: relative;
      display: inline-block;
      font-weight: 400;
      white-space: nowrap;
      text-align: center;
      background-image: none;
      background-color: transparent;
      border: 1px solid transparent;
      cursor: pointer;
      transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
      user-select: none;
      touch-action: manipulation;
      line-height: 1.5714285714285714;
      color: rgba(0, 0, 0, 0.88);
      font-size: 14px;
      height: 32px;
      padding: 4px 15px;
      border-radius: 6px;

      &:hover {
        color: rgba(0, 0, 0, 0.88);
        background-color: rgba(0, 0, 0, 0.06);
      }
    ;
    }
  }

  .custom-designer-content {
    box-sizing: border-box;
    display: flex;
    height: 100%;
    padding-top: 50px;

    .attr-bar {
      box-sizing: border-box;
      width: 270px;
      background-color: #fff;
      padding: 10px;
      overflow: auto;
    }

    .factory-area {
      box-sizing: border-box;
      justify-content: center;
      //padding: 0 8px 0;
      background-color: #f5f5f5;
      flex: 1 1 0%;
      overflow: auto;
      //width: calc(100% - 270px);
      min-height: calc(100% - 50px);
    }
  }
}
</style>