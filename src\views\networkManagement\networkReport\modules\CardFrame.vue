<template>
  <a-card
    class='tabs-card'
    :body-style="{paddingTop: '24px', paddingRight: '24px', paddingLeft: '24px', paddingBottom: '24px' }"
    :bordered='false'
    :head-style="{ paddingLeft: '12px', paddingRight: '12px', height: '44px' }"
  >
    <div slot='title' style='height: 100%'>
      <slot name='titleSlot' class='title'></slot>
    </div>
    <div slot='extra' style='padding: 0px'>
      <slot name='extraSlot'></slot>
    </div>
    <slot name='bodySlot'></slot>
  </a-card>
</template>
<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import yqIcon from '@comp/tools/SvgIcon'

export default {
  name: 'CardFrame',
}
</script>

<style scoped lang='less'>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
.tabs-card{
  height: 100%;
}
::v-deep .ant-card-head-wrapper {
  height: 100%;

  .ant-card-head-title {
    height: 100%;

    .title {
      height: 100%;
      display: flex;
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
      margin-top: 2px;
      align-items: center;

      .icon{
        color: #409eff;
        font-size: 30px;
      }
      .text{
        margin-left: 8px
      }
    }
  }
}
::v-deep .ant-card-body{
  height: calc(100% - 48px);

  .body-height{
    height: 100%
  }
  .body-empty{
    display: flex;
    justify-content: center;
    align-items: center
  }
}
</style>