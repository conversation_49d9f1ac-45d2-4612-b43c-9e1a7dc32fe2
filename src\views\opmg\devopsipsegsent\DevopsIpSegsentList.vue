<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll zxw">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <!-- 查询区域 -->
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class="card-style">
        <div class="table-page-search-wrapper-style">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="IP网段">
                  <a-input
                    placeholder="请输入"
                    autocomplete="off"
                    :allowClear="true"
                    v-model="queryParam.ipSegsent"
                  ></a-input>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="备注">
                  <a-input
                    placeholder="请输入"
                    autocomplete="off"
                    :allowClear="true"
                    v-model="queryParam.remarks"
                  ></a-input>
                </a-form-item>
              </a-col>
              <a-col :span="colBtnsSpan()">
                <span
                  class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                >
                  <a-button type="primary" @click="searchQuery" class="btn-search-style">查询</a-button>
                  <a-button @click="searchReset" style="margin-left: 10px" class="btn-reset-style">重置</a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <!-- 查询区域-END -->

      <a-card :bordered="false" style="flex: auto" class="core">
        <a-row class="lastBtn2">
          <!-- 操作按钮区域 -->
          <div class="table-operator">
            <a-button @click="handleAdd">新增</a-button>
            <a-dropdown v-if="selectedRowKeys && selectedRowKeys.length > 0">
              <a-menu slot="overlay" style='text-align: center'>
                <a-menu-item key="1" @click="batchDel">删除</a-menu-item>
              </a-menu>
              <a-button> 批量操作 <a-icon type="down" /></a-button>
            </a-dropdown>
          </div>
        </a-row>
        <!-- table区域-begin -->
        <a-table
          ref="table"
          bordered
          rowKey="id"
          :columns="columns"
          :dataSource="dataSource"
          :pagination="ipagination"
          :loading="loading"
          :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          class="j-table-force-nowrap"
          @change="handleTableChange"
        >
          <template slot="htmlSlot" slot-scope="text">
            <div v-html="text"></div>
          </template>
          <template slot="imgSlot" slot-scope="text">
            <span v-if="!text" style="font-size: 14px">无图片</span>
            <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width: 80px; font-size: 14px" />
          </template>
          <template slot="fileSlot" slot-scope="text">
            <span v-if="!text" style="font-size: 14px">无文件</span>
            <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
              下载
            </a-button>
          </template>

          <span
            slot="action"
            slot-scope="text, record"
            class="caozuo"
            style="display: inline-block; white-space: nowrap; text-align: center"
          >
            <a-dropdown>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
            </a-dropdown>
          </span>
          <template slot="tooltip" slot-scope="text">
            <a-tooltip placement="topLeft" :title="text" trigger="hover">
              <div class="tooltip">
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
      </a-card>

      <devops-ip-segsent-modal ref="modalForm" @ok="modalFormOk"></devops-ip-segsent-modal>
    </a-col>
  </a-row>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import DevopsIpSegsentModal from './modules/DevopsIpSegsentModal'
import JSuperQuery from '@/components/jeecg/JSuperQuery.vue'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'

export default {
  name: 'DevopsIpSegsentList',
  mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
  components: {
    DevopsIpSegsentModal,
    JSuperQuery,
  },
  data() {
    return {
      formItemLayout: {
        labelCol: {
          style: 'width:70px',
        },
        wrapperCol: {
          style: 'width:calc(100% - 70px)'
        }
      },
      description: 'ip监控配置管理页面',
      // 表头
      columns: [
        {
          title: 'IP网段',
          dataIndex: 'ipSegsent',
        },
        {
          title: '备注',
          dataIndex: 'remarks',
        },
        {
          title: '执行cron',
          dataIndex: 'executeCron',
        },
        {
          title: '创建人',
          dataIndex: 'createByName',
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
        },
        {
          title: '操作',
          dataIndex: 'action',
          fixed: 'right',
          align: 'center',
          width: 147,
          scopedSlots: { customRender: 'action' },
        },
      ],
      url: {
        list: '/devopsipsegsent/devopsIpSegsent/list',
        delete: '/devopsipsegsent/devopsIpSegsent/delete',
        deleteBatch: '/devopsipsegsent/devopsIpSegsent/deleteBatch',
        exportXlsUrl: '/devopsipsegsent/devopsIpSegsent/exportXls',
        importExcelUrl: 'devopsipsegsent/devopsIpSegsent/importExcel',
      },
      dictOptions: {},
      superFieldList: [],
    }
  },
  created() {
    this.getSuperFieldList()
  },
  mounted() {},
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
  },
  methods: {
    initDictConfig() {},
    getSuperFieldList() {
      let fieldList = []
      fieldList.push({ type: 'string', value: 'ipSegsent', text: 'ip网段', dictCode: '' })
      fieldList.push({ type: 'string', value: 'remarks', text: '备注', dictCode: '' })
      fieldList.push({ type: 'string', value: 'executeCron', text: '执行cron', dictCode: '' })
      this.superFieldList = fieldList
    },
  },
}
</script>
<style lang="less" scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
.table-page-search-wrapper .ant-form-inline .ant-form-item {
  margin-bottom: 0 !important;
}
.ant-table-pagination.ant-pagination {
  margin: 16px 0 0 0 !important;
}

::v-deep .ant-table-thead > tr > th {
  text-align: center;
  white-space: nowrap;
}

/*内容对齐方式、省略显示*/
::v-deep .ant-table-tbody > tr > td {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;

  &:nth-child(2),
  &:nth-child(3),
  &:nth-child(4),
  &:nth-child(5),
  &:nth-child(6) {
    text-align: center;
  }
}
</style>
