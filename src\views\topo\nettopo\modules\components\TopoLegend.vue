<template>
<div class='legend-list'>
  <div class='legend-item'>
    <div class='legend-color' :style="{background: onOffColors.onLine}"></div>
    <div class='legend-text'>在线</div>
  </div>
  <div class='legend-item'>
    <div class='legend-color' :style="{background: onOffColors.offLine}"></div>
    <div class='legend-text'>离线</div>
  </div>
  <div class='legend-item' v-for='(el, index) in alarmLevelList' :key='index'>
    <div class='legend-color' :style="{background: el.color}"></div>
    <div class='legend-text'>{{ el.title }}</div>
  </div>
  <div class='legend-item'>
    <div class='legend-arrow' :style="{color: lineColors.connected}">
      <a-icon type="line" style='font-size: 12px'/>
      <a-icon type="arrow-right"  style='font-size: 12px' />
    </div>
    <div class='legend-text'>连接正常</div>
  </div>
  <div class='legend-item'>
    <div class='legend-arrow' :style="{color: lineColors.notConnected}">
      <a-icon type="line" style='font-size: 12px'/>
      <a-icon type="arrow-right"  style='font-size: 12px' />
    </div>
    <div class='legend-text'>连接异常</div>
  </div>
</div>
</template>
<script>
export default {
  name: 'topoLegend',
  props: {
    alarmLevelList: {
      type:Array,
      default: () => []
    },
    onOffColors: {
      type: Object,
      default: () => ({ onLine: '#52c41a', offLine: '#8c8c8c' })
    },
    lineColors: {
      type: Object,
      default: () => ({ connected: '#52c41a', notConnected: '#ff4d4f' })
    },
  },
  data() {
    return {
    }
  },
  created() {}

}
</script>

<style scoped lang='less'>
.legend-list{
  padding: 8px;
  background-color: rgba(255,255,255,0.4);
  border-radius: 5px;
  //display: flex;
  .legend-item{
    display: flex;
    align-items: center;
    margin-right: 12px;
    margin-bottom: 12px;
    .legend-color{
      width: 10px;
      height: 10px;
      margin-right: 5px;
      border-radius: 50%;
    }
    .legend-arrow{
      display: flex;
      align-items: center;
      width: 28px;
    }
    .legend-text{
      font-size: 12px;
    }
  }

}
</style>