<template>
  <a-row :gutter='10' style='height: 100%;' class='vScroll'>
    <a-col style='width:100%;height: 100%;display: flex;flex-direction: column'>
      <!-- 查询区域 -->
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0', marginRight: '12px' }" class='card-style'
              style='width: 100%'>
        <div class='table-page-search-wrapper'>
          <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
            <a-row :gutter='24' ref='row'>
              <a-col :span='spanValue'>
                <a-form-item label='标题'>
                  <a-input placeholder='请输入标题' v-model='queryParam.title' :allowClear='true'
                           autocomplete='off' :maxLength="maxLength"/>
                </a-form-item>
              </a-col>
<!--              <a-col :span='spanValue'>
                <a-form-item label='能见度' >
                  <a-select :allowClear='true' v-model='queryParam.isPrivate' placeholder='请选择能见度'>
                    <a-select-option v-for="item in visibility" :value="item.value" :key="'visibility_'+item.value" :label="item.label">
                      {{ item.label }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>-->
              <a-col :span='colBtnsSpan()'>
                <span class='table-page-search-submitButtons'
                          :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button type='primary' class='btn-search btn-search-style' @click='searchQuery'>查询</a-button>
                  <a-button class='btn-reset btn-reset-style' @click='searchReset'>重置</a-button>
                  <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                        {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <a-table
          ref='table'
          bordered
          :row-key='(record,index)=>{return record.id}' :columns='columns'
          :dataSource='dataSource'
          :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
          :pagination='ipagination'
          :loading='loading'
          @change='handleTableChange'>
          <template slot='tooltip' slot-scope='text'>
            <a-tooltip placement='topLeft' :title='text' trigger='hover'>
              <div class='tooltip'>
                {{ text }}
              </div>
            </a-tooltip>
          </template>
          <template slot='isPrivate' slot-scope='text'>
            <a-icon :type='text===visibility[1].value?"lock":"global"' :style='{color:text===visibility[1].value?"#FE9400":"#4BD863"}'></a-icon>
            {{ text===visibility[1].value?visibility[1].label:visibility[0].label }}
          </template>
          <template slot='knowledgeType' slot-scope='text'>
            <knowledge-icon :knowledgeType="text"></knowledge-icon>
          </template>
          <span slot='action' class='caozuo' slot-scope='text, record'>
            <a @click='handleDetailPage(record)'>查看</a>
            <a-divider type='vertical'/>
            <a @click='handleApproval(record)' class='overlay'>审批</a>
          </span>
        </a-table>
      </a-card>
    </a-col>
    <knowledge-approval-modal ref='modalForm' @ok='modalFormOk'></knowledge-approval-modal>
  </a-row>
</template>

<script>
import '@assets/less/TableExpand.less'
import {JeecgListMixin} from '@/mixins/JeecgListMixin'
import knowledgeApprovalModal from '@views/opmg/knowledgeManagement/knowledgeApproval/modules/KnowledgeApprovalModal.vue'
import {YqFormSearchLocation} from '@/mixins/YqFormSearchLocation'
import {visibility} from '@views/opmg/knowledgeManagement/knowledgeBase/modules/dataListAndFunc'
import knowledgeIcon from '@views/opmg/knowledgeManagement/knowledgeBase/modules/KnowledgeIcon.vue'

export default {
  name: 'KnowledgeApprovalList',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components: {
    knowledgeIcon,
    knowledgeApprovalModal
  },
  data() {
    return {
      maxLength:50,
      description: '知识审批列表页面',
      formItemLayout: {
        labelCol: {
          style: 'width:70px'
        },
        wrapperCol: {
          style: 'width:calc(100% - 80px)'
        }
      },
      visibility: visibility,
      // 表头
      columns: [
        {
          title: '标题',
          dataIndex: 'title',
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'tooltip'
          }
        },
        {
          title: '主题名称',
          dataIndex: 'topicName',
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'tooltip'
          }
        },
        {
          title: '知识类型',
          dataIndex: 'knowledgeType',
          customCell: () => {
            let cellStyle = 'width:80px'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'knowledgeType'
          }
        },
        {
          title: '能见度',
          dataIndex: 'isPrivate',
          customCell: () => {
            let cellStyle = 'width:80px'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'isPrivate'
          }
        },
        {
          title: '创建人员',
          dataIndex: 'createBy'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          customCell: () => {
            let cellStyle = 'center;width: 160px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '审批状态',
          dataIndex: 'state',
          customCell: () => {
            let cellStyle = 'width:120px'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'state'
          }
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 140,
          scopedSlots: {
            customRender: 'action'
          }
        }
      ],
      url: {
        list: '/kbase/knowledges/list/review'
      },
    }
  },
  methods: {
    handleApproval(record){
      this.$refs.modalForm.approval(record);
      this.$refs.modalForm.title = '知识审批';
      this.$refs.modalForm.disableSubmit = false;
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
.overlay {
  color: #409eff
}
</style>