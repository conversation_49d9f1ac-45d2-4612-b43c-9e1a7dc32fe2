<template>
  <div class='table-operator table-operator-style scroll'>
    <!-- table区域-begin -->
    <a-table
      style='margin-right: 1px'
      ref='table'
      bordered
      :columns='columns'
      :dataSource='dataSource'
      :loading='loading'
      :pagination='ipagination'
      :rowKey='(record,index)=>{return index}'
      :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
      @change='handleTableChange'>
       <template slot='tooltip' slot-scope='text'>
          <a-tooltip :title='text' placement='topLeft' trigger='hover'>
            <div class='tooltip'>
              {{ text }}
            </div>
          </a-tooltip>
       </template>
        <!-- 字符串超长截取省略号显示-->
        <span slot='templateContent' slot-scope='text'>
          <j-ellipsis :length='25' :value='text' />
        </span>

        <template slot='powerState' slot-scope='text, record,index'>
          <span :key='record.rowIndex'>
            <div v-if='record.loading===true'>
              <a-spin />
            </div>
            <div v-else>
              <yq-icon :style='{color:record.powerColor,fontSize:"35px"}' :type='record.powerIcon' />
            </div>
          </span>
        </template>

        <span slot='action' slot-scope='text, record' class='caozuo yq-icon'>
<!--      <a v-if='record.power==="POWERED_OFF"' @click="submitForm(record, 'PowerOnVM')">开机</a>
          <span v-if='record.power==="POWERED_ON"'>
            <a @click="submitForm(record, 'PowerOffVM')">关机</a>
            <a-divider type='vertical' />
            <a @click="submitForm(record, 'ResetVM')">重启</a>
          </span>-->
            <span @click="powerAction(record,'POWERED_OFF','PowerOnVM')">
                <yq-icon type='on' :style='{color:powerStyle(record,"POWERED_OFF")}' title='开机' />
            </span>
            <a-divider type='vertical' />
            <span @click="powerAction(record,'POWERED_ON','PowerOffVM')">
              <yq-icon type='off' :style='{color:powerStyle(record,"POWERED_ON")}' title='关机' />
              <a-divider type='vertical' />
            </span>
            <span @click="powerAction(record,'POWERED_ON','ResetVM')">
             <yq-icon type='restart' :style='{color:powerStyle(record,"POWERED_ON")}' title='重启' />
          </span>
        </span>
      </a-table>
  </div>
</template>

<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import JEllipsis from '@comp/jeecg/JEllipsis.vue'
import { getAction, postAction } from '@api/manage'
import yqIcon from '@comp/tools/SvgIcon'
import { queryConfigureDictItem } from '@api/api'
import {tableOperationColumnVisibility} from '@/mixins/tableOperationColumnVisibility'

export default {
  name: 'virtualMachineList',
  mixins: [JeecgListMixin,tableOperationColumnVisibility],
  components: {
    JEllipsis,
    yqIcon
  },
  data() {
    return {
      // 表头
      columns: [
        {
          title: '名称',
          dataIndex: 'vmName'
        },
        {
          title: 'IP',
          dataIndex: 'ip'
        },
        {
          title: 'CPU使用率',
          dataIndex: 'vmwareCpuUsage',
          customCell: () => {
            let cellStyle = 'text-align: right'
            return { style: cellStyle }
          }
        },
        {
          title: '内存使用率',
          dataIndex: 'vmwareMemRate',
          customCell: () => {
            let cellStyle = 'text-align: right'
            return { style: cellStyle }
          }
        },
        {
          title: '电源状态',
          dataIndex: 'power',
          scopedSlots: { customRender: 'powerState' }
        },
        {
          title: '运行时间',
          dataIndex: 'sysUpTime'
        },
        {
          title: '操作',
          dataIndex: 'action',
          fixed: 'right',
          width: 150,
          align: 'center',
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: '/alarm/alarmTemplate/vmwareByDevId',
        execute:'/device/deviceInfo/execute',
        vmwareInfo:'/alarm/alarmTemplate/getVmwareInfo',
        // vmwareInfo:'/alarm/alarmTemplate/refreshVmware'
      },
      disableMixinCreated: true,
      deviceInfo:{}
    }
  },
  watch: {
    deviceInfo(newVal, oldVal) {
      this.init(newVal)
    }
  },
  created() {
    this.setTabelOperationCol('operationColumnVisibility','virtualMachineOperVis')
  },
  methods: {
    show(record){
      this.deviceInfo=record
    },
    init(record){
      this.queryParam.deviceId = record.id
      this.loadData()
    },
    handleDetailPage: function(record) {
      this.$parent.pButton2(1, record)
    },
    powerStyle(record, powerState) {
      if (record.power === powerState) {
        return '#409eff !important'
      }
      return '#dcdfe6'
    },
    powerAction(record, powerState, actionState) {
      if (record.loading === false) {
        if (record.power === powerState) {
          this.submitForm(record, actionState)
        }
      } else {
        this.$message.info('有命令未执行完成，请稍等！')
      }
    },
    loadData(arg) {
      if (!this.url.list) {
        this.$message.error('请设置url.list属性!')
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1
      }
      var params = this.getQueryParams()//查询条件
      this.loading = true
      getAction(this.url.list, params).then((res) => {
        if (res.success) {
          let data = res.result.records || res.result
          if (data.length > 0) {
            data.map((item, index) => {
              item.loading = false
              item.rowIndex = 'row_' + index
              item.powerIcon = item.power == 'POWERED_ON' ? 'on_status' : 'off_status'
              item.powerColor = item.power == 'POWERED_ON' ? '#1dff02' : '#3b3a3a'
            })
          }
          this.dataSource = data
          this.ipagination.total = data.length
        }
        if (res.code === 510) {
          this.$message.warning(res.message)
        }
        this.loading = false
      })
    },
    refreshVmware(record, message) {
      getAction(this.url.vmwareInfo, {
        deviceId: this.data.id,
        ip: this.data.ip,
        vmwareName: record.vmName
      }).then((res) => {
        if (res.success) {
          if (res.result.vmwareinfo.length > 0) {
            let data = res.result.vmwareinfo
            if (data[0].value === record.vmName) {
              this.$message.success(message)
              record.ip = data[1].value ? data[1].value : ''
              record.vmwareCpuUsage = data[2].value ? data[2].value : ''
              record.vmwareMemRate = data[3].value ? data[3].value : ''
              record.power = data[4].value
              record.sysUpTime = data[5].value ? data[5].value : ''
              record.powerIcon = record.power == 'POWERED_ON' ? 'on_status' : 'off_status'
              record.powerColor = record.power == 'POWERED_ON' ? '#1dff02' : '#3b3a3a'
              record.loading = false
              return
            }
          }
        }
      }).catch((err) => {
        this.$message.error(err + "")
        this.$message.warning('发生异常，请稍后尝试刷新页面或重新操作')
        record.loading = false
      })
    },
    submitForm(record, actionState) {
      var unixtime = new Date().getTime()
      const that = this
      record.loading = true
      let formData = {}
      formData.deviceId = this.queryParam.deviceId
      formData.methodName = actionState
      formData.vmwareName = record.vmName
      formData.transferProtocol = 'Vmware'
      this.info()
      postAction(this.url.execute, formData)
        .then((res) => {
          if (res.success && res.result.indexOf('成功') !== -1) {
            //that.$message.success(res.result)
            //命令执行完毕后，后需要一段时间从虚拟主机中获取数据，所以加了定时
            setTimeout(() => {
              this.refreshVmware(record, res.result)
            }, 5000)
          } else {
            record.loading = false
            that.$message.warning(res.result)
          }
        }).catch((err) => {
        this.$message.error(err + "")
        this.$message.warning('发生异常，请稍后尝试刷新页面或重新操作')
        record.loading = false
      })
    },
    info() {
      const h = this.$createElement;
      const modal=this.$info({
        title: '提示',
        content: h('div', {}, [h('p', '命令已开始执行，请耐心等待！')]),
        okButtonProps:{style:{display:'none'}},
      });
     setTimeout(() => {
        modal.destroy();
      },  2000);
    },

  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
.scroll{
  height: 100%;
  overflow: hidden;
  overflow-y: auto;
}
.spin-icon {
  font-size: 24px;
  color: #409eff;
}

.yq-icon {
  font-size: 24px;

  & > span > i {
    cursor: pointer;
  }
}
</style>
