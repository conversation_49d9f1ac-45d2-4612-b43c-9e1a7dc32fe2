<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll zxw">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <!-- 查询区域 -->
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="消息标题">
                  <j-input
                    placeholder="请输入"
                    autocomplete="off"
                    :allowClear="true"
                    v-model="queryParam.esTitle"
                  ></j-input>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="发送内容">
                  <j-input
                    placeholder="请输入"
                    autocomplete="off"
                    :allowClear="true"
                    v-model="queryParam.esContent"
                  ></j-input>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="接收人">
                  <a-input
                    placeholder="请输入"
                    autocomplete="off"
                    :allowClear="true"
                    v-model="queryParam.esReceiver"
                  ></a-input>
                </a-form-item>
              </a-col>
              <a-col :span="colBtnsSpan()">
                <span
                  class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                >
                  <a-button type="primary" @click="searchQuery" class="btn-search-style">查询</a-button>
                  <a-button @click="searchReset" style="margin-left: 10px" class="btn-reset-style">重置</a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered="false" style="flex: auto" class="core">
        <!-- 操作按钮区域 -->
        <div class="table-operator tableBottom">
          <a-button @click="handleAdd" v-show="show">新增</a-button>
          <a-button v-show="show" @click="handleExportXls('消息')">导出</a-button>
          <a-upload
            v-show="show"
            name="file"
            :showUploadList="false"
            :multiple="false"
            :headers="tokenHeader"
            :action="importExcelUrl"
            @change="handleImportExcel"
          >
            <a-button>导入</a-button>
          </a-upload>
          <a-dropdown v-if="selectedRowKeys.length > 0">
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item key="1" @click="batchDel">删除</a-menu-item>
            </a-menu>
            <a-button>
              批量操作
              <a-icon type='down'/>
            </a-button>
          </a-dropdown>
        </div>
        <!-- table区域-begin -->
        <div>
          <a-table
            ref="table"
            bordered
            rowKey="id"
            :columns="columns"
            :dataSource="dataSource"
            :pagination="ipagination"
            :loading="loading"
            :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
            @change="handleTableChange"
          >
            <!-- 字符串超长截取省略号显示-->
            <span slot="esContent" slot-scope="text">
              <j-ellipsis :value="text" :length="10" />
            </span>
            <span
              class="caozuo"
              slot="action"
              slot-scope="text, record"
              style="display: inline-block; white-space: nowrap; text-align: center"
            >
              <a href="javascript:;" @click="handleDetail(record)">详情</a>
              <a-divider v-show="show" type="vertical" />
              <a v-show="show" @click="handleEdit(record)">编辑</a>
              <a-divider type="vertical" />
              <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                <a>删除</a>
              </a-popconfirm>
            </span>
            <template slot="tooltip" slot-scope="text">
              <a-tooltip placement="topLeft" :title="text" trigger="hover">
                <div class="tooltip">
                  {{ text }}
                </div>
              </a-tooltip>
            </template>
          </a-table>
        </div>
        <!-- table区域-end -->
        <!-- 表单区域 -->
        <sysMessage-modal ref="modalForm" @ok="modalFormOk"></sysMessage-modal>
      </a-card>
    </a-col>
  </a-row>
</template>

<script>
import SysMessageModal from './modules/SysMessageModal'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import JEllipsis from '@/components/jeecg/JEllipsis'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'

export default {
  name: 'SysMessageList',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components: {
    JEllipsis,
    SysMessageModal,
  },
  data() {
    return {
      description: '消息管理页面',
      // 新增修改按钮是否显示
      show: false,
      // 表头
      columns: [
        {
          title: '消息标题',
          dataIndex: 'esTitle',
        },
        {
          title: '发送内容',
          dataIndex: 'esContent',
          scopedSlots: { customRender: 'esContent' },
        },
        {
          title: '接收人',
          dataIndex: 'esReceiver',
        },
        {
          title: '发送次数',
          dataIndex: 'esSendNum',
        },
        {
          title: '发送状态',
          dataIndex: 'esSendStatus_dictText',
        },
        {
          title: '发送时间',
          dataIndex: 'esSendTime',
        },
        {
          title: '发送方式',
          dataIndex: 'esType_dictText',
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          scopedSlots: { customRender: 'action' },
        },
      ],
      url: {
        list: '/sys/message/sysMessage/list',
        delete: '/sys/message/sysMessage/delete',
        deleteBatch: '/sys/message/sysMessage/deleteBatch',
        exportXlsUrl: 'sys/message/sysMessage/exportXls',
        importExcelUrl: 'sys/message/sysMessage/importExcel',
      },
    }
  },
  mounted() {},
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
  },
  methods: {},
}
</script>
<style lang="less" scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
/** Button按钮间距 */
.ant-btn {
  margin-left: 3px;
}

.ant-card-body .table-operator {
  margin-bottom: 8px;
}

.ant-table-tbody .ant-table-row td {
  padding-top: 15px;
  padding-bottom: 15px;
}

.anty-row-operator button {
  margin: 0 5px;
}

.ant-btn-danger {
  background-color: #ffffff;
}

.ant-modal-cust-warp {
  height: 100%;
}

.ant-modal-cust-warp .ant-modal-body {
  height: calc(100% - 110px) !important;
  overflow-y: auto;
}

.ant-modal-cust-warp .ant-modal-content {
  height: 90% !important;
  overflow-y: hidden;
}
.tableBottom button:hover {
  color: #fff !important;
  background-color: #409eff !important;
  border-color: #409eff !important;
}

.tableBottom button:focus {
  color: #409eff;
  background: #ecf5ff;
  border-color: #b3d8ff;
}

.tableBottom button {
  color: #409eff;
  background: #ecf5ff;
  border-color: #b3d8ff;
}
/*表头样式*/
::v-deep .ant-table-thead > tr > th {
  text-align: center;
  white-space: nowrap;
}

/*内容对齐方式、省略显示*/
::v-deep .ant-table-tbody > tr > td {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;

  &:first-child,
  &:nth-child(2),
  &:nth-child(3),
  &:nth-child(4),
  &:nth-child(5),
  &:nth-child(6),
  &:nth-child(7),
  &:nth-child(8),
  &:nth-child(9) {
    text-align: center;
  }
}
</style>