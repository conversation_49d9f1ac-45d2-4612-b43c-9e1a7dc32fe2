<template>
  <j-modal
    :title='title'
    :width='modelWidth'
    :visible='visible'
    :centered='true'
    :confirmLoading='confirmLoading'
    switchFullscreen
    :destroyOnClose='true'
    @ok='handleOk'
    @cancel='handleCancel'
    cancelText='关闭'
  >
    <a-spin :spinning='confirmLoading'>
      <a-form :form='form'>
        <a-form-item label='类型名称' :labelCol='labelCol' :wrapperCol='wrapperCol'>
          <a-input v-decorator="['categoryName', validatorRules.categoryName]"
                   placeholder='请输入类型名称'
                   :allowClear='true'
                   autocomplete='off'/>
        </a-form-item>
        <a-form-item label='类型编号' :labelCol='labelCol' :wrapperCol='wrapperCol'>
          <a-input v-decorator="['categoryCode', validatorRules.categoryCode]"
                   placeholder='请输入类型编号'
                   :allowClear='true'
                   autocomplete='off'/>
        </a-form-item>
        <a-form-item class='two-words' label='描述' :labelCol='labelCol' :wrapperCol='wrapperCol'>
          <a-input
            v-decorator="['categoryDescribe', validatorRules.categoryDescribe]"
            placeholder='请输入描述'
            :allowClear='true'
            autocomplete='off'
          />
        </a-form-item>
        <a-form-item class='two-words' label='序号' :labelCol='labelCol' :wrapperCol='wrapperCol'>
          <a-input-number
            v-decorator="['categorySerial', validatorRules.categorySerial]"
            placeholder='请输入序号'
          />
        </a-form-item>
        <a-form-item label='父级节点' :labelCol='labelCol' :wrapperCol='wrapperCol'>
          <j-tree-select-expand
            v-decorator="['parentId',validatorRules.parentId]"
            placeholder='请选择父级节点'
            dict='cmdb_assets_category,category_name,id'
            pidField='parent_id'
            condition='{"delflag":0,"is_monitorable":1}'
            pidValue='0'
            :current-node='model.id'
            :hasChildField='hasChild'
            @change='changeSelect($event)'
          />
        </a-form-item>
      </a-form>
    </a-spin>
  </j-modal>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import pick from 'lodash.pick'
import { validateDuplicateValue } from '@/utils/util'

export default {
  name: 'AssetsCategoryModal',
  components: {},
  data() {
    return {
      dis: true,
      assetsCategoryTree: [],
      assetsCategoryName: '',
      assetsCategoryIds: '',
      form: this.$form.createForm(this),
      title: '操作',
      modelWidth: '800px',
      visible: false,
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },

      confirmLoading: false,
      validatorRules: {
        categoryName: {
          rules: [
            { required: true, message: '请输入类型名称' },
            { min: 2, max: 20, message: '类型名称长度应在2到20个字符！', trigger: 'blur' }
          ]
        },
        categoryCode: {
          rules: [
            { required: true, message: '请输入类型编号' },
            { min: 0, max: 20, message: '类型编号长度应在0-20之间！  ' }
          ]
        },
        categoryDescribe: {
          rules: [
            { required: true, message: '请输入描述' },
            { min: 2, max: 50, message: '描述长度应在2-50之间！  ' }
          ]
        },
        categorySerial: {
          rules: [
            { required: true, message: '请输入序号' },
            // { max: 20, message: '序号长度应在0-20之间！  ' },
            { pattern: /^[1-9]{1}[0-9]*$/, message: '请输入大于0的整数' }
          ]
        }
        /* isMonitorable: {
           rules: [{ required: true, message: '请输入是否可监控，1：可监控，0：不可监控' }],
         },*/
      },
      url: {
        add: '/assetscategory/assetsCategory/add',
        edit: '/assetscategory/assetsCategory/edit',
        productTypeUrl: '/assetscategory/assetsCategory/selectAssetsCategoryTree' //资产类型接口
      },
      expandedRowKeys: [],
      pidField: 'parentId',
      hasChild: 'has_child'
    }
  },
  created() {
  },
  mounted() {
    this.getSelectTree()
  },
  methods: {
    changeSelect(e) {
    },
    add(obj) {
      this.edit(obj)
      /*  this.visible = true;
        this.model.parentId = obj?obj.parentId:null;
        this.$nextTick(() => {
          this.form.setFieldsValue(
            pick(
              this.model,
              'categoryName',
              'categoryCode',
              'categoryDescribe',
              'categorySerial',
              'parentId',
            )
          )
        })*/
    },
    edit(record) {
      if (record) {
        this.dis = true
      } else {
        this.dis = false
      }
      getAction(this.url.productTypeUrl).then((res) => {
        this.assetsCategoryTree = res.result
        this.form.resetFields()
        this.model = Object.assign({}, record)
        // var that=this
        // let treeData = that.$refs.treeSelect.getCurrTreeData()

        if (this.model.parentId === '0') {
          this.model.parentId = ''
        }
        this.visible = true
        //不知道谁在这里加了categoryState、isMonitorable，但是上面没在此之前用v-decorator注册这两个字段，导致前台报错
        /* this.$nextTick(() => {
           this.form.setFieldsValue(
             pick(
               'categoryName',
               'categoryCode',
               'categoryDescribe',
               'categorySerial',
               'categoryState',
               'parentId',
               'isMonitorable'
             )
           )
         })*/
        this.$nextTick(() => {
          this.form.setFieldsValue(
            pick(
              this.model,
              'categoryName',
              'categoryCode',
              'categoryDescribe',
              'categorySerial',
              'parentId'
            )
          )
        })
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = false
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let old_pid = this.model[this.pidField]
          let formData = Object.assign(this.model, values)
          let new_pid = this.model[this.pidField]
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                this.$emit('ok')
                that.close()
              } else {
                that.$message.warning(res.message)
              }
              that.confirmLoading = false
            }).catch((err)=>{
            that.$message.warning(err.message)
            that.confirmLoading = false
          })
            .finally(() => {
              this.$forceUpdate()
            })
        }
      })
    },
    handleCancel() {
      this.close()
    },
    popupCallback(row) {
      this.form.setFieldsValue(
        pick(row, 'categoryName', 'categoryCode', 'categoryDescribe', 'categorySerial', 'categoryState', 'parentId')
      )
    },
    submitSuccess(formData, flag) {
      if (!formData.id) {
        let treeData = this.$refs.treeSelect.getCurrTreeData()
        this.expandedRowKeys = []
        this.getExpandKeysByPid(formData[this.pidField], treeData, treeData)
        this.$emit('ok', formData, this.expandedRowKeys.reverse())
      } else {
        this.$emit('ok', formData, flag)
      }
    },
    getExpandKeysByPid(pid, arr, all) {
      if (pid && arr && arr.length > 0) {
        for (let i = 0; i < arr.length; i++) {
          if (arr[i].key == pid) {
            this.expandedRowKeys.push(arr[i].key)
            this.getExpandKeysByPid(arr[i]['parentId'], all, all)
          } else {
            this.getExpandKeysByPid(pid, arr[i].children, all)
          }
        }
      }
    },

    selectAssetsCategory(assetsCategoryId, data) {
      this.assetsCategoryId = assetsCategoryId
      this.assetsCategoryName = data[0]
    },
    getSelectTree() {
      getAction(this.url.productTypeUrl).then((res) => {
        this.assetsCategoryTree = res.result
      })
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/normalModal.less';
</style>