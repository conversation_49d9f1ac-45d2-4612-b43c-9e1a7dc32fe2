<template>
  <a-card style="height: 100%;overflow: auto">
    <a-row>
      <a-col :span="24">
        <span style="margin-left: 10px;font-size: 16px; ">
          详情
        </span>
        <span style="float: right;margin-bottom: 12px;"><img src="~@/assets/return1.png" alt="" @click="getGo"
            style="width: 20px;height: 20px;cursor: pointer"></span>
      </a-col>
      <a-col :span="24">
        <table class="gridtable">
          <tr>
            <td class="leftTd">子网组名称</td>
            <td class="rightTd">{{ data.subnetGroupName }}</td>
            <td class="leftTd">子网组简称</td>
            <td class="rightTd">{{ data.subnetGroupNickname }}</td>
          </tr>
          <tr>
            <td class="leftTd">使用部门</td>
            <td class="rightTd">{{ data.departName }}</td>
            <td class="leftTd">使用位置</td>
            <td class="rightTd">{{data.location}}</td>
          </tr>
          <tr>
            <td class="leftTd">备注</td>
            <td colspan='3' class="rightTd">{{ data.remark }}</td>
          </tr>
        </table>
      </a-col>
    </a-row>
  </a-card>
</template>

<script>
  import {
    httpAction,
    getAction
  } from '@/api/manage'
  import pick from 'lodash.pick'
  export default {
    name: 'networkPlanDetails',
    props: {
      data: {
        type: Object
      }
    },
    data() {
      return {
        form: this.$form.createForm(this),
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          },
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          },
        },
        confirmLoading: false,
      }
    },
    methods: {
      //返回上一级
      getGo() {
        this.$parent.pButton2(0);
      }
    }
  }
</script>
<style scoped>
  table.gridtable {
    font-family: verdana, arial, sans-serif;
    font-size: 14px;
    color: #606266;
    border-width: 1px;
    border-color: #e8e8e8;
    border-collapse: collapse;
    text-align: left;
    width: 100%;
  }

  table.gridtable td {
    border-width: 1px;
    border-style: solid;
    border-color: #e8e8e8;
  }

  .leftTd {
    width: 17%;
    background-color: #FAFAFA;
    padding: 16px 24px;
    text-align: center;
  }

  .rightTd {
    width: 35%;
    padding: 16px 24px;
  }
</style>