<template>
  <j-modal
    :title="title"
    :width="modalWidth"
    :visible="visible"
    :confirmLoading="confirmLoading"
    :destroyOnClose="true"
    :centered='true'
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭"
  >
    <div style="padding-top: 0px">
      <fm-generate-form :data="startFormJson" ref="generateStartForm" :value="variables" :remote="remoteFuncs" disabled>
      </fm-generate-form>
      <a-tabs :animated="false" default-active-key="1" @change="callback">
        <a-tab-pane key="1" tab="附件">
          <div class="clearfix">
            <j-upload v-model="files" :number="5" style="padding-top: 20px"></j-upload>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>
  </j-modal>
</template>
<script>
import { getAction, postAction,putAction } from '@/api/manage'
import JUpload from '@/components/jeecg/JUpload'
import { ajaxGetDictItem, ajaxGetDictItems, getProgectList } from '@/api/api'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'

export default {
  name: 'eventApplicationAdd',
  mixins: [JeecgListMixin],
  components: {
    JUpload,
  },
  data() {
    return {
      title: '操作',
      fFormDisplayStatus:0,
      strAlarmConfirmFormData:'question_type',
      strBackendInterface:'/question/add',

      //sy_question_form为沈阳专用流程表单，其他项目可用question_form
      processDefinition: { formKey: 'sy_question_form' },
      /* 弹框宽 */
      modalWidth: '1000px',
      form: this.$form.createForm(this),
      visible: false,
      required: false,
      startFormJson: undefined,
      variables: undefined,
      disabledValue: true,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      confirmLoading: false,
      // 上传相关
      previewVisible: false,
      previewImage: '',
      files: '',
      //remoteFuncs:{},
      remoteFuncs: {
        getType:(resolve)=>{
          //获取告警确认表单问题类型信息，question_type_sy代表沈阳问题类型，其他项目使用question_type
          ajaxGetDictItems(this.strAlarmConfirmFormData, null).then((res) => {
            if (res.success) {
              const options = res.result
              resolve(options)
            }
          })
        },
        getProgect:(resolve) =>{
          getProgectList(null).then((res) => {
            if (res.success) {
              const options = res.result
              resolve(options)
            }
          })
        },
      },

      // 关联配置项
      columns: [
        {
          title: '序号',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function (t, r, index) {
            return parseInt(index) + 1
          },
        },
        {
          title: '分类',
          align: 'center',
          dataIndex: 'configType_dictText',
        },
        {
          title: '编号',
          align: 'center',
          dataIndex: 'code',
        },
        {
          title: '名称',
          align: 'center',
          dataIndex: 'name',
        },
        {
          title: '单位名称',
          align: 'center',
          dataIndex: 'unitName',
        },
        {
          title: '项目名称',
          align: 'center',
          dataIndex: 'projectName',
        },
        {
          title: '合同名称',
          align: 'center',
          dataIndex: 'contractName',
        },
        {
          title: '状态',
          align: 'center',
          dataIndex: 'state_dictText',
        },
      ],
      dataSource: [],
      url: {
        list: '/itilconfigitemlibrary/itilConfigItemLibrary/list',
      },
    }
  },
  created() {
    this.variables = null

  },
  mounted() {
    this.getFormDisplayStatusValue();
  },
  methods: {
    getFormDisplayStatusValue(){
      //从字典中获取项目栏显示状态
      ajaxGetDictItem('form_display_status', null).then(res => {
        if (res.success) {
          this.fFormDisplayStatus = res.result[0].value
          this.initStringData();
        }
      })
    },
    //根据数据字典表单显示状态value值，区分沈阳项目和其他项目的表单数据字符串
    initStringData(){
      if (this.fFormDisplayStatus==0) {
        this.processDefinition.formKey = 'question_form';
        this.strAlarmConfirmFormData = 'question_type';
        this.strBackendInterface = '/question/add';
      }
      else {
        this.processDefinition.formKey='sy_question_form';
        this.strAlarmConfirmFormData='question_type_sy';
        this.strBackendInterface='/busQuestion/add';
      }
      // this.setFormData();
    },
    //初始化表单数据。
    setFormData(){
      if (this.processDefinition.formKey) {
        getAction('/flowableform/umpFlowableForm/queryByKey', {
          key: this.processDefinition.formKey,
          tableId: null,
        }).then((res) => {
          if (res.success) {
            var formData = res.result
            if (formData && formData.formJson) {
              this.startFormJson = JSON.parse(formData.formJson)
              this.variables = null
            }
          }
        })
      }
    },
    show(ids) {
      //设备告警页面传来的告警历史ID
      this.ids = ids
      let v = { formKey: this.processDefinition.formKey }
      if (!v.formKey) {
        this.$message.warning('该流程信息未配置表单，请联系开发人员！')
        return
      }
      // alert('this.v...'+v)
      // alert('this.eventId...'+this.eventId)
      // alert('this.uid...'+this.uid)
      this.add(v, this.eventId, this.uid)
      setTimeout(() => {
        // this.$refs.modal.width = '55.2%'
        this.modalWidth = '1000px'
      }, 300)
    },

    add(v, eventId, uid) {
      this.eventId = eventId
      this.processDefinition = v
      this.selectedRowKeys = []
      this.variables = null
      this.visible = true
      this.files = '' //文件
      this.uid = uid
      if (this.processDefinition.tableId) {
        this.getData()
      }
      // if (null != v.busId) {
      //   getAction('/businessrelation/actZBusinessRelation/list', { processId: this.processDefinition.busId }).then(
      //     (res) => {
      //       if (res.success) {
      //         this.selectedRowKeys = res.result.itilConfigIds
      //         this.files = res.result.fileUrlList
      //       }
      //     }
      //   )
      // }
    },
    //获取数据
    getData() {
      if (this.processDefinition.formKey) {
        getAction('/flowableform/umpFlowableForm/queryByKey', {
          key: this.processDefinition.formKey,
          tableId: this.processDefinition.tableId,
        }).then((res) => {
          if (res.success) {
            var formData = res.result
            if (formData && formData.formJson) {
              // this.startFormJson = JSON.parse(formData.formJson)
              this.variables = JSON.parse(formData.formValue)
            }
          }
        })
      }
    },
    // 关闭弹框
    close() {
      this.$emit('close')
      this.visible = false
      this.current = 0
    },
    // 提交
    handleOk() {
      if (this.$refs.generateStartForm) {
        this.$refs.generateStartForm
          .getData()
          .then((values) => {
            if (values && values != undefined) {
              let formData = Object.assign(this.data || {}, values)
              formData.form_value = JSON.stringify(values)
              //Object.assign({processInstanceFormData}, values)
              formData.filedNames = 'form_value' + ',' + 'form_key'
              formData.form_key = this.processDefinition.formKey
              formData.itilConfigIds = this.selectedRowKeys
              formData.file = this.files
              formData.depCode = this.depCode
              formData.eventId = this.eventId //绑定事件ID
              //确认，/busQuestion/add为沈阳后端调用接口，其他项目后端调用接口是/question/add
              postAction(this.strBackendInterface, formData).then((res) => {
                this.uploading = false
                if (res.success) {
                  putAction('/alarm/alarmHistory/confirmBatch',{ids : this.ids}).then((res) =>{
                    if(res.success){
                      this.$message.success('确认成功！')
                    }else{
                      this.$message.warning(res.message)
                    }
                  })
                  this.visible = false
                  this.$emit('ok')
                } else {
                  this.$message.warning(res.message)
                  this.visible = false
                  this.$emit('ok')
                }
              })
            }
          })
          .catch((e) => {})
      }
    },
    handleCancel() {
      this.close()
    },
    // tab
    callback(key) {
    },
    // 上传相关
    onCancel() {
      this.previewVisible = false
    },
    async handlePreview(file) {
      if (!file.url && !file.preview) {
        file.preview = await getBase64(file.originFileObj)
      }
      this.previewImage = file.url || file.preview
      this.previewVisible = true
    },
    handleChange({ fileList }) {
      this.fileList = fileList
    },
  },
}
</script>
<style scoped lang='less'>
@import '~@assets/less/limitModalWidth.less';
</style>
