<template>
  <a-row style='height: 100%'>
    <a-col :xl='4' :lg='6' :md='8' :sm='10' :xs='10' style='height: 100%; background: #fff'>
      <device-tree-load ref="tree" :inputFlag='true' @selected='treeSeletedSearch' :placeholder="'请输入查询内容'"
        :tree-url="treeUrl" :params="urlParams" :btnIconName="'appstore'" :is-show-all-btn='true' :btnName="'IP地址规划'"
        :is-show-btn-icon='true' :field-key="'key'"></device-tree-load>
    </a-col>
    <!-- 子网组 -->
    <network-group-list v-if="ipShow == 5" @detail="detail" @refresh="refresh"></network-group-list>
    <!-- 子网 -->
    <network-children-list v-if="ipShow == 0" @detail="detail" :groupId="groupId" @refresh="refresh"
      :topData="superOrg">
    </network-children-list>
    <!-- 网段 -->
    <segment-list v-if="ipShow == 1" @detail="detail" :subnetId="groupId" @refresh="refresh" :topData="superOrg">
    </segment-list>
    <!-- IP -->
    <IP-plan-list v-if="ipShow == 2" @detail="detail" :info="{'segmentId':groupId,'IPPlanName':ipName}"
      @refresh="refresh" :topData="superOrg">
    </IP-plan-list>
  </a-row>
</template>

<script>
  import '@/assets/less/TableExpand.less'
  import IPPlanList from './IPPlanList.vue'
  import segmentList from './segmentList.vue'
  import networkChildrenList from './networkChildrenList.vue'
  import networkGroupList from './networkGroupList.vue'
  import {
    httpAction,
    getAction,
    postAction,
    deleteAction
  } from '@/api/manage'
  //引入公共devicetree组件
  import DeviceTreeLoad from '@/components/tree/DeviceTreeLoad.vue'
  export default {
    name: 'networkPlanList',
    components: {
      networkGroupList,
      IPPlanList,
      segmentList,
      networkChildrenList,
      'device-tree-load': DeviceTreeLoad,
    },
    data() {
      return {
        description: '网络规划管理页面',
        ipShow: 5,
        ipName: '',
        groupId: '',
        treeKey: '',
        superOrg: {},
        formItemLayout: {
          labelCol: {
            style: 'width:80px'
          },
          wrapperCol: {
            style: 'width:calc(100% - 80px)'
          }
        },
        treeUrl: '/devops/ip/queryTreeList',
        getSuperOrg: 'devops/ip/getSuperOrg',
        urlParams: {
          id: 0,
          type: 0
        },
      }
    },
    created() {},
    mounted() {},
    methods: {
      detail(type, record) {
        this.$parent.pButton2(type, record)
      },
      treeSeletedSearch(value, type = '', data, key) {
        this.getData(value, type)
        this.treeKey = key
        this.groupId = value
        if (type == 3) {
          // 当选中IP 的时候
          this.ipName = data.title
          this.ipShow = 2
        } else {
          this.ipName = ''
          this.ipShow = type != null && type != '' ? type : 5
        }
      },
      refresh() {
        this.$refs.tree.loadTree(this.treeKey)
      },
      getData(value, type) {
        getAction(this.getSuperOrg, {
          id: value,
          type: type
        }).then((res) => {
          this.superOrg = this.assignIdFromKey(res.result);
        })
      },
      assignIdFromKey(obj) {
        if (obj && typeof obj === 'object') {
            // 如果对象有 key 属性,且 id 为 null, 把key的值全部复制给id
            if ('key' in obj && obj.id === null) {
              obj.id = obj.key;
            }
            
            // 递归处理子节点
            if (Array.isArray(obj.children)) {
              obj.children.forEach(child => this.assignIdFromKey(child));
            }
        }
        return obj;
      }
    }
  }
</script>
<style lang='less' scoped>
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';

  .stateBox {
    margin-left: 20px;
  }

  .stateImg {
    vertical-align: middle;
  }

  .alarmStatus {
    margin-left: 8px;
  }

  .overlay {
    color: #409eff
  }
</style>