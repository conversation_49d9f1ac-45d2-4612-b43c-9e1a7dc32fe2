<template>
  <div style="height:100%;overflow: hidden;overflow-y: auto"> 
    <component :is="pageName" style="height:100%" :data="data"/>
  </div>
</template>
<script>
import materialsList from './materialsList'
import UnitSubmissionList from '@views/operationalEvaluationNew/operationalOrganization/modules/UnitSubmissionList.vue'
export default {
  name: "materialsManagement",
  data() {
    return {
      isActive: 0,
      data:{}
    };
  },
  components: {
    materialsList,
    UnitSubmissionList
  },
  created(){
    this.pButton1(0);
  },
  //使用计算属性
  computed: {
    pageName() {
      switch (this.isActive) {
        case 0:
          return "materialsList";
          break;
        case 2:
          return "UnitSubmissionList";
          break;
        default:
          return "UnitSubmissionList";
          break;
      }
    }
  },
  methods: {
    pButton1(index) {
      this.isActive = index;
    },
    pButton2(index,item) {
      this.isActive = index;
      this.data = item;
    }
  }
}
</script>