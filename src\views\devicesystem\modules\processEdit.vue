<template>
  <div class="wrap">
    <div id="nodeDel" style="display: none; border-radius: 5px; position: absolute; z-index: 100">
      <div>
        <a-button @click="nodeDel">删除</a-button>
      </div>
    </div>
    <div id="edgeDiv" class="edge-div">
      <div>
        <p>组件描述：</p>
        <p>{{ cmpDesc }}</p>
      </div>
    </div>
    <!-- 左侧工具栏 -->
    <div class="dataTree">
      <a-input placeholder="输入关键字进行过滤" v-model="filterText"></a-input>
      <vue-easy-tree
        ref="veTree"
        node-key="value"
        :data="processTree"
        :props="replaceFields"
        height="100%"
        :filter-node-method="filterNode"
      >
        <div class="custom-tree-node" slot-scope="{ node, data }">
          <div
            :draggable="draggable"
            v-if="data.hasChild !== true"
            @mouseenter="suspension($event, data)"
            @mouseleave="cmpLeave"
            @mousedown="startDrag(node.label, $event, data)"
          >
            <!-- <yq-icon v-if="data.icon != null && data.icon != ''" :type="iconName(data)" style="font-size: 20px" /> -->
            <img
              v-if="data.icon != null && data.icon.length > 0"
              :src="picSrc + '/' + data.icon"
              style="width: 20px; height: 20px"
            />
            <span>{{ node.label }}</span>
          </div>
          <div v-else>
            <span>{{ node.label }}</span>
          </div>
        </div>
      </vue-easy-tree>
    </div>
    <!-- 流程画板 -->
    <div class="panel">
      <div id="layoutContainer" ref="container" class="graphcontainer" style="flex: 1; width: 100% !important" />
    </div>
    <!--右侧工具栏-->
    <div class="config">
      <a-row>
        <div style="font-size: 18px">流程信息</div>
        <a-col :span="24">
          <a-form-item label="流程标识" :labelCol="labelCol" :wrapperCol="wrapperCol" :required="true">
            <a-input placeholder="请输入流程标识" v-model="chainId"></a-input>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="流程名称" :labelCol="labelCol" :wrapperCol="wrapperCol" :required="true">
            <a-input placeholder="请输入流程名称" v-model="chainName"></a-input>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="协议" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <div>{{ protocol }}</div>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="流程描述" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input placeholder="请输入流程描述" v-model="chainDesc"></a-input>
          </a-form-item>
        </a-col>
      </a-row>
    </div>
    <div class="consoleClass">
      <a-collapse expand-icon-position="right" @change="collChange">
        <a-collapse-panel key="1" header="控制台">
          <a-card style="min-height: 400px">
            <a-card-grid style="width: 50%; height: 470px">
              <a-tabs default-active-key="1" @change="callback">
                <a-tab-pane key="1" tab="连接参数" force-render>
                  <a-form>
                    <div v-for="(item, index) in collectInfoList" :key="index" class="collectInfoBox">
                      <a-row :gutter="24">
                        <a-col v-bind="formItemLayout" v-for="(item1, idx) in item.value" :key="idx">
                          <a-form-item
                            :label="item1.displayName"
                            :labelCol="labelCol"
                            :wrapperCol="wrapperCol"
                            :required="true"
                          >
                            <a-input
                              v-if="item1.controlType == 'inputString'"
                              v-model="item1.connectValue"
                              :placeholder="'请输入' + item1.displayName"
                              :allowClear="true"
                              autocomplete="off"
                              @change="itemChange($event, index, idx)"
                            ></a-input>
                            <j-dict-select-tag
                              v-if="item1.controlType == 'inputSelect'"
                              v-model="item1.connectValue"
                              :dictCode="item1.dictType"
                              placeholder="请选择"
                              :trigger-change="true"
                              @change="selectChange($event, index, idx)"
                              :allowClear="true"
                            />
                            <a-input-password
                              v-if="item1.controlType == 'inputPassword'"
                              v-model="item1.connectValue"
                              placeholder="请输入"
                              :allowClear="true"
                              autocomplete="off"
                              @change="itemChange($event, index, idx)"
                            >
                            </a-input-password>
                            <a-input-number
                              v-if="item1.controlType == 'inputNumber'"
                              v-model="item1.connectValue"
                              placeholder="请输入"
                              :allowClear="true"
                              autocomplete="off"
                              @change="nunberChange($event, index, idx)"
                            ></a-input-number>
                          </a-form-item>
                        </a-col>
                      </a-row>
                    </div>
                  </a-form>
                </a-tab-pane>
                <a-tab-pane key="2" tab="流程参数">
                  <vue-json-editor
                    v-model="resultInfo"
                    :showBtns="false"
                    :mode="'tree'"
                    lang="zh"
                    @json-change="onJsonChange"
                    @json-save="onJsonSave"
                    @has-error="onError"
                  />
                </a-tab-pane>
              </a-tabs>
              <a-button type="primary" @click="getDeBug" class="debugClass">调试</a-button>
            </a-card-grid>
            <a-card-grid style="width: 50%; height: 470px">
              <div style="height: 420px; overflow: auto" v-html="content" ref="content"></div>
            </a-card-grid>
          </a-card>
          <a-popover slot="extra" title="流程参数说明" placement="bottomRight">
            <template slot="content">
              <p>function:计算组件用到的参数,当calculate为true时必须配置</p>
              <p>calculate:判断指标是否需要计算</p>
              <p>type:指标的数据类型</p>
              <p>parent:该属性的上级属性,没有上级属性时无此字段</p>
              <p>key:对应物模型中设置的指标code</p>
              <p>param:对应组件需要用到的参数</p>
              <p>resultKey:返回结果的key</p>
              <p>code:对应节点的唯一标识</p>
            </template>
            <a-icon type="question-circle" theme="twoTone" style="margin-right: 20px; font-size: 18px" />
          </a-popover>
          <a-icon slot="extra" type="delete" @click="consoleDel" />
        </a-collapse-panel>
      </a-collapse>
    </div>
    <cmp-Config ref="cmpConfig" @ok="cmpConfigOk"></cmp-Config>
  </div>
</template>

<script>
import VueEasyTree from '@wchbrad/vue-easy-tree'
import { deleteAction, getAction, httpAction, postAction } from '../../../api/manage'
import FlowGraph from '../../topo/nettopo/modules/graph/chainIndex'
import cmpConfig from './cmpConfig'
import yqIcon from '@/components/tools/SvgIcon'
import store from '@/store/'
import vueJsonEditor from 'vue-json-editor-fix-cn'
import { Graph, Color, Dom, Shape, Edge, NodeView, Addon, DataUri, Vector } from '@antv/x6'
import uuidv1 from 'uuid/v1'
import { message } from 'ant-design-vue'
import computed from '@/assets/cmpIcon/computed.svg'
import  WebsocketMessageMixin  from '@/mixins/WebsocketMessageMixin'
const { Dnd } = Addon
export default {
  name: 'processEdit',
  components: {
    yqIcon,
    VueEasyTree,
    cmpConfig,
    vueJsonEditor,
    uuidv1,
    computed,
  },
  mixins: [WebsocketMessageMixin],
  data() {
    let cache = new Map()
    return {
      curCel: '',
      preservation: null,
      calculateJudge: false,
      parentId: '',
      chainJson: {
        lineStart: {},
        topoModelData: {},
      },
      apiUrl: window._CONFIG['domianURL'],
      configOverview: [],
      configOverviewOther: [],
      cmpName: '',
      AgreementJudgment: false,
      group: '',
      topoData: {},
      chainEdit: '',
      formItemLayout: {
        md: {
          span: 12,
        },
        sm: {
          span: 24,
        },
      },
      paramChain: '',
      isPass: false,
      metadataId: '',
      ifLastParam: '',
      ifLastLoop: '',
      elseLastParam: '',
      elseLastLoop: '',
      content: '',
      deBugContent: '',
      judge: '',
      loop: '',
      ifLoopList: [],
      elseLoopList: [],
      ifCondition: [],
      elseCondition: [],
      hasJsonFlag: true,
      resultInfo: null,
      jsonData: '',
      activeKey: ['1'],
      cmpCode: '',
      cmpCodes: [],
      infoData: '',
      cmpGroup: '',
      paramInfo: {},
      tagCache: cache,
      randomNum: '',
      code: '',
      haveTime: null,
      cmpDesc: '',
      id: '',
      chain_id: '',
      chainId: '',
      chainName: '',
      chainDesc: '',
      protocol: '',
      topoInfo: null,
      productId: '',
      picSrc: window._CONFIG['staticDomainURL'],
      isReady: false,
      draggable: true,
      filterText: '',
      dnd: Object,
      processTree: [],
      collectInfoList: [],
      chainParamVo: {
        chain: '',
        chainParam: '',
        chainId: '',
      },
      replaceFields: {
        label: 'title',
        key: 'value',
      },
      labelCol: {
        xs: {
          span: 24,
        },
        sm: {
          span: 6,
        },
      },
      wrapperCol: {
        xs: {
          span: 24,
        },
        sm: {
          span: 16,
        },
      },
      url: {
        add: '/flow/chain/add',
        edit: '/flow/chain/edit',
        deBug: 'flow/chain/deBug',
        deBugBegin: 'flow/chain/deBugBegin',
        queryCmp: '/flow/cmp/queryCmp',
        queryCmpTree: '/flow/cmp/queryCmpTree',
        queryCmpParam: 'flow/chain/queryCmpParam',
        removeCmpParamCache: '/flow/chain/removeCmpParamCache',
        queryProductChain: '/flow/chain/queryProductChain', //获取本产品所有流程的参数
        queryRelaTemplate: '/device/deviceInfo/selectConnectInfo', // 获取连接参数
      },
    }
  },
  watch: {
    filterText(val) {
      this.$refs.veTree.filter(val)
    },
  },
  created() {},
  mounted() {

  },
  beforeDestroy() {
    this.consoleDel()
    if (this.chainId != null && this.chainId !== '') {
      this.removeCmpParamCache()
    } else {
      this.$emit('close', false)
    }
  },
  methods: {
    getWebsocketMessage() {},
    onLabelChange(e) {
      let cell = this.graph.getCellById(e.nodeId)
      cell.setAttrs({
        label: {
          text: e.nodeLabel,
        },
      })
    },
    // 退出页面时清除缓存
    removeCmpParamCache() {
      deleteAction(this.url.removeCmpParamCache, {
        chainId: this.chainId,
      }).then((res) => {
        if (res.success) {
        }
      })
    },
    queryProductChain(proId, mateId) {
      getAction(this.url.queryProductChain, {
        productId: proId,
        metadataId: mateId,
      }).then((res) => {
        if (res.success) {
          res.result.forEach((ele) => {
            this.configOverviewOther.push(ele)
          })
        }
      })
    },
    // 获取svg类型
    iconName(data) {
      if (data.icon != null && data.icon != '') {
        let firstIcon = data.icon.split('_')
        return firstIcon[0]
      }
    },
    websocketOnmessage: function (e) {
       console.log('processEdit监听到了信息 == ', e)
      var data = eval('(' + e.data + ')') //解析对象
      if (data.messageType === 'flowDebug') {

        this.deBugContent += data.data
      }
    },
    getCollet() {
      getAction(this.url.queryRelaTemplate, {
        productId: this.productId,
      })
        .then((res) => {
          if (!!res) {
            res.forEach((ele) => {
              if (ele.value.defaultValue != null && ele.value.defaultValue.length > 0) {
                ele.value.connectValue = ele.value.defaultValue
              }
            })
            this.collectInfoList = res
          }
        })
        .finally(() => {
          this.confirmLoading = false
        })
    },
    itemChange(e, pidx, idx) {
      this.collectInfoList[pidx].value[idx].connectValue = e.target.value
    },
    nunberChange(e, pidx, idx) {
      this.collectInfoList[pidx].value[idx].connectValue = e
    },
    selectChange(e, pidx, idx) {
      this.collectInfoList[pidx].value[idx].connectValue = e
    },
    callback(key) {},
    getDeBug() {
      if ((this.resultInfo.value == '' || this.resultInfo.value == null) && this.calculateJudge == false) {
        this.$message.warning('请确认组件是否配置')
        return
      }
      let data = Object.values(this.resultInfo.function)
      let collectData = this.collectInfoList[0].value
      let connect = {}
      for (let i = 0; i < data.length; i++) {
        if (data[i].param != undefined && data[i].param == '' && data.param == null) {
          this.$message.warning('请确认组件是否配置')
          return
        }
      }
      for (let i = 0; i < collectData.length; i++) {
        if (collectData[i].connectValue == '' || collectData[i].connectValue == null) {
          this.$message.warning('请检查连接参数是否有未填项')
          return
        } else {
          this.$set(connect, collectData[i].connectCode, collectData[i].connectValue)
        }
      }
      this.createChains()
      if (!this.isPass) {
        return
      }
      this.chainParamVo.chainParam = JSON.stringify(this.resultInfo)
      this.chainParamVo.chain = this.paramChain
      this.chainParamVo.connectParam = JSON.stringify(connect)
      this.chainParamVo.productId = this.productId
      postAction(this.url.deBugBegin, this.chainParamVo).then((res) => {
        if (res.success) {
          this.content = '流程链条:' + this.chainParamVo.chain + '<br/>开始接收日志...<br/>' + this.deBugContent
        } else {
          this.content = res.message
        }
        this.$nextTick(() => {
          let scrollElem = this.$refs.content
          scrollElem.scrollTo({
            top: scrollElem.scrollHeight,
            behavior: 'smooth',
          })
        })
      })
    },
    collChange(value) {
      if (value.length > 0) {
        this.checkJson()
      }
    },
    onJsonChange(value) {
      // 实时保存
      this.onJsonSave(value)
    },
    onJsonSave(value) {
      this.resultInfo = value
      this.hasJsonFlag = true
    },
    onError(value) {
      this.hasJsonFlag = false
    },

    // 检查json
    checkJson() {
      if (this.hasJsonFlag == false) {
        this.$message.warning('json格式错误')
        return false
      }
    },
    consoleDel() {
      this.content = ''
      this.deBugContent = ''
      // event.stopPropagation()
    },
    consoleSearch() {},
    getProcessList(protocol) {
      let that = this
      that.processTree = []
      //为tree生成对应地title slot
      const generateSlotScopes = (data) => {
        for (let i = 0; i < data.length; i++) {
          data[i].scopedSlots = {
            title: 'title',
          }
          if (data[i].children) {
            generateSlotScopes(data[i].children)
          }
        }
      }
      getAction(this.url.queryCmpTree, {
        protocol: protocol,
      }).then((res) => {
        if (res.success) {
          generateSlotScopes(res.result)
          that.processTree = res.result
        }
      })
    },
    getTag(tagPrefix, index) {
      let tag = tagPrefix + index
      for (let [key, value] of this.tagCache) {
        if (tag === value) {
          return this.getTag(tagPrefix, index + 1)
        }
      }
      return tag
    },
    filterNode(value, data) {
      if (!value) return true
      return data.title.indexOf(value) !== -1
    },
    create(record, info) {
      let formData = {
        function: {},
        calculate: false,
        key: '',
        type: '',
        value: '',
        code: '',
      }
      if (info.pid != null) {
        formData.parent = ''
        this.parentId = info.pid
      }
      if (record.chainJson != '' && record.chainJson != null) {
        this.chainJson = JSON.parse(record.chainJson)
      }
      this.resultInfo = formData
      this.preservation = formData
      this.paramChain = record.chain
      this.chainParamVo.chain = record.chain
      this.chainParamVo.chainParam = record.params
      this.chainParamVo.protocol = info.transferProtocol
      if (record.params) {
        this.resultInfo = JSON.parse(record.params)
        this.preservation = JSON.parse(record.params)
        let funData = Object.values(this.preservation.function)
        for (let i = 0; i < funData.length; i++) {
          if (funData[i].target && funData[i].target.length > 0) {
            funData[i].target.forEach((ele) => {
              this.configOverview.push(ele)
            })
          }
        }
        this.calculateJudge = this.preservation.calculate
        this.topoData = JSON.parse(record.params)
        if (this.topoData.function.calculateOriginal != null) {
          this.AgreementJudgment = true
        }
      }
      this.paramInfo = JSON.parse(JSON.stringify(info))
      if (record.chainId == '' || record.chainId == null) {
        this.paramInfo.chainId = uuidv1()
        this.chainParamVo.chainId = uuidv1()
      } else {
        this.chainId = record.chainId
        this.chainParamVo.chainId = record.chainId
      }
      this.chainName = record.chainName
      this.chainDesc = record.chainDesc
      this.protocol = info.transferProtocol
      this.productId = info.productId
      let cache = record.tagCache
      if (cache) {
        this.tagCache = new Map(JSON.parse(cache))
      }
      this.id = record.id
      this.metadataId = info.id
      this.queryProductChain(this.productId, this.metadataId)
      this.$store.commit('CLEAR_DATA_LIST')
      this.topoInfo = record
      this.getProcessList(this.protocol)
      if (this.graph) {
        this.graph.dispose()
      }
      this.$nextTick(() => {
        this.initGraph('layoutContainer')
        if (
          this.topoInfo.topoDataJson != null &&
          this.topoInfo.topoDataJson !== '' &&
          this.topoInfo.topoDataJson !== undefined
        ) {
          let topoObj = JSON.parse(this.topoInfo.topoDataJson)
          let xlink = 'xlink:href'
          topoObj.cells.forEach((item, i) => {
            if (topoObj.cells[i].attrs && topoObj.cells[i].attrs.image && topoObj.cells[i].attrs.image[xlink]) {
              topoObj.cells[i].attrs.image[xlink] = this.apiUrl + '/' + topoObj.cells[i].attrs.image[xlink]
            }
          })
          this.graph.fromJSON(topoObj)
          let nodes = this.graph.getNodes().filter((ele) => ele.shape === 'switch-node1')
          let nodeIds = []
          if (nodes.length > 0) {
            nodes.forEach((e) => nodeIds.push(e.data.id))
          }
        }
        this.visible = true
        //流程在画布中居中
        this.graph.centerContent()
      })
      this.getCollet()
    },
    initGraph(container) {
      this.graph = FlowGraph.init(container)
      this.dnd = new Dnd({
        target: this.graph,
        scaled: false,
        animation: true,
        validateNode(droppingNode, options) {
          return true
        },
      })
      this.setup()
      this.isReady = true
    },
    nodeDel() {
      const { graph } = FlowGraph
      const cells = graph.getSelectedCells()
      if (cells.length) {
        graph.cut(cells)
      }
      this.$delete(this.resultInfo.function, cells[0].store.data.data.tag)
      this.$delete(this.preservation.function, cells[0].store.data.data.tag)
      this.$delete(this.topoData, cells[0].store.data.data.tag)
      this.$delete(this.chainJson.lineStart, cells[0].store.data.data.tag)
      this.$delete(this.chainJson.topoModelData, cells[0].store.data.data.tag)
      for (let tag of Object.keys(this.topoData)) {
        if (tag.includes(cells[0].store.data.data.tag)) {
          delete this.topoData[tag]
        }
      }
      for (let tag of Object.keys(this.chainJson.topoModelData)) {
        if (tag.includes(cells[0].store.data.data.tag)) {
          delete this.chainJson.topoModelData[tag]
        }
      }
      if (cells[0].store.data.data.group == 3) {
        this.AgreementJudgment = false
      }
      document.getElementById('nodeDel').style.display = 'none'
      return false
    },
    // 监听画布节点、边自定义事件
    setup() {
      this.graph.on('edge:connected', ({ isNew, edge }) => {
        if (isNew) {
          let source = edge.getSourceCell() //获取源节点信息
          let target = edge.getTargetCell() //获取目标节点信息
          if (target.store.data.data.group == 3) {
            this.$message.warning('协议组件不能为目标节点')
            this.graph.removeEdge(edge)
            return
          } else {
            let edgrSource = {}
            let edgrTarget = {}
            let relation = {}
            this.$set(edgrSource, 'tag', source.store.data.data.tag)
            this.$set(edgrSource, 'type', 'component')
            this.$set(edgrSource, 'group', source.store.data.data.group)
            this.$set(edgrTarget, 'tag', target.store.data.data.tag)
            this.$set(edgrTarget, 'type', 'component')
            this.$set(edgrTarget, 'group', target.store.data.data.group)
            this.$set(relation, 'source', source.store.data.data.tag)
            this.$set(relation, 'target', target.store.data.data.tag)
            this.$set(relation, 'type', 'relation')
            this.$set(this.topoData, source.store.data.data.tag, edgrSource)
            this.$set(this.topoData, target.store.data.data.tag, edgrTarget)
            this.$set(this.topoData, source.store.data.data.tag + '_' + target.store.data.data.tag, relation)
            this.$set(
              this.chainJson.topoModelData,
              source.store.data.data.tag + '_' + target.store.data.data.tag,
              relation
            )
          }
          if (source.store.data.data.group != 3) {
            this.$delete(this.chainJson.lineStart, source.store.data.data.tag)
          }
          this.$delete(this.chainJson.lineStart, target.store.data.data.tag)
          let data = Object.values(this.chainJson.topoModelData)
          for (let i = 0; i < data.length; i++) {
            if (data[i].cmpType == 3) {
              this.AgreementJudgment = true
              break
            } else {
              this.AgreementJudgment = false
            }
          }
        }
      })
      //鼠标左键键单击事件
      this.graph.on('node:click', ({ e, x, y, node, view }) => {
        document.getElementById('edgeDiv').style.display = 'none'
        document.getElementById('nodeDel').style.display = 'block'
        document.getElementById('nodeDel').style.top = e.clientY - 50 + 'px'
        document.getElementById('nodeDel').style.left = e.clientX - 60 + 'px'
        document.getElementById('nodeDel').style.zIndex = 100
        this.cmpName = node.store.data.data.name
      })
      //鼠标单击空白处事件
      this.graph.on('blank:click', ({}) => {
        document.getElementById('edgeDiv').style.display = 'none'
        document.getElementById('nodeDel').style.display = 'none'
      })
      // 鼠标按下事件
      this.graph.on('node:mousedown', ({}) => {
        document.getElementById('edgeDiv').style.display = 'none'
      })
      //鼠标双击事件
      this.graph.on('node:dblclick', ({ e, x, y, node, view }) => {
        getAction(this.url.queryCmp, {
          cmpId: node.store.data.data.id,
        }).then((res) => {
          this.cmpGroup = res.result.cmpGroup
        })
        if (this.chainId != null && this.chainId != '') {
          this.paramInfo.chainId = this.chainId
        }
        document.getElementById('nodeDel').style.display = 'none'
        if (this.chainId != '' && this.chainId != null) {
          if (this.cmpGroup == 3) {
            getAction(this.url.queryCmpParam, {
              cmpId: node.store.data.data.id,
              chainId: this.chainId,
              cmpTag: node.store.data.id,
            }).then((res) => {
              if (res.success) {
                this.$refs.cmpConfig.edit(
                  node.store.data,
                  this.paramInfo,
                  res.result,
                  this.cmpGroup,
                  this.configOverview,
                  this.configOverviewOther
                )
                this.$refs.cmpConfig.title = '组件配置'
                this.$refs.cmpConfig.disableSubmit = false
                this.$refs.cmpConfig.visible = true
              }
            })
          } else {
            getAction(this.url.queryCmpParam, {
              cmpId: node.store.data.data.id,
              chainId: this.chainId,
              cmpTag: node.store.data.data.tag,
            }).then((res) => {
              if (res.success) {
                this.$refs.cmpConfig.edit(
                  node.store.data,
                  this.paramInfo,
                  res.result,
                  this.cmpGroup,
                  this.configOverview,
                  this.configOverviewOther
                )
                this.$refs.cmpConfig.title = '组件配置'
                this.$refs.cmpConfig.disableSubmit = false
                this.$refs.cmpConfig.visible = true
              }
            })
          }
        } else {
          if (this.cmpGroup == 3) {
            getAction(this.url.queryCmpParam, {
              cmpId: node.store.data.data.id,
              chainId: this.paramInfo.chainId,
              cmpTag: node.store.data.id,
            }).then((res) => {
              if (res.success) {
                this.$refs.cmpConfig.edit(
                  node.store.data,
                  this.paramInfo,
                  res.result,
                  this.cmpGroup,
                  this.configOverview,
                  this.configOverviewOther
                )
                this.$refs.cmpConfig.title = '组件配置'
                this.$refs.cmpConfig.disableSubmit = false
                this.$refs.cmpConfig.visible = true
              }
            })
          } else {
            getAction(this.url.queryCmpParam, {
              cmpId: node.store.data.data.id,
              chainId: this.paramInfo.chainId,
              cmpTag: node.store.data.data.tag,
            }).then((res) => {
              if (res.success) {
                this.$refs.cmpConfig.edit(
                  node.store.data,
                  this.paramInfo,
                  res.result,
                  this.cmpGroup,
                  this.configOverview,
                  this.configOverviewOther
                )
                this.$refs.cmpConfig.title = '组件配置'
                this.$refs.cmpConfig.disableSubmit = false
                this.$refs.cmpConfig.visible = true
              }
            })
          }
        }
      })
      //鼠标移出节点事件
      this.graph.on('node:mouseleave', () => {
        document.getElementById('edgeDiv').style.display = 'none'
      })
      //鼠标移入节点事件
      this.graph.on('node:mouseenter', ({ e, node, view }) => {
        getAction(this.url.queryCmp, {
          cmpId: node.store.data.data.id,
        }).then((res) => {
          this.cmpDesc = res.result.cmpDesc
          this.code = res.result.code
          this.cmpGroup = res.result.cmpGroup
          if (res.result.cmpGroup == 3) {
            this.cmpCode = res.result.code
          }
        })
        // let timer = !this.haveTime
        // this.haveTime = setTimeout(() => {
        //   this.haveTime = null
        // }, 1000)
        // if (timer) {
        const winHeight = window.screen.height
        if (e.clientY + 350 > winHeight) {
          document.getElementById('edgeDiv').style.display = 'block'
          document.getElementById('edgeDiv').style.top = e.clientY - 200 + 'px'
          document.getElementById('edgeDiv').style.left = e.clientX - 200 + 'px'
        } else {
          document.getElementById('edgeDiv').style.display = 'block'
          document.getElementById('edgeDiv').style.top = e.clientY + 10 + 'px'
          document.getElementById('edgeDiv').style.left = e.clientX + 10 + 'px'
        }
        // }
      })
    },
    // 鼠标悬浮查看信息
    suspension(e, data) {
      getAction(this.url.queryCmp, {
        cmpId: data.value,
      }).then((res) => {
        this.cmpDesc = res.result.cmpDesc
      })
      document.getElementById('edgeDiv').style.display = 'block'
      document.getElementById('edgeDiv').style.top = e.clientY + 'px'
      document.getElementById('edgeDiv').style.left = e.clientX + 100 + 'px'
    },
    // 鼠标离开
    cmpLeave() {
      document.getElementById('edgeDiv').style.display = 'none'
    },
    // tree拖拽节点
    async startDrag(name, e, data) {
      if (data.group == 3 && this.AgreementJudgment == true) {
        this.$message.warning('当前已存在协议组件')
        return
      }
      //创建子拓扑节点
      let tagPrefix = data.key + 'Node'
      let tag = this.getTag(tagPrefix, 1)
      let node = this.graph.createNode({
        shape: 'son-topo-node1',
        data: {
          code: data.key,
          id: data.value,
          name: name,
          tag: tag,
          group: data.group,
        },
        attrs: {
          label: {
            text: name,
          },
          image: {
            'xlink:href': this.picSrc + '/' + data.icon,
          },
          // image: { 'xlink:href': require('@/assets/cmpIcon/computed.svg') },
        },
      })
      let that = this
      that.chainJson.topoModelData = this.chainJson.topoModelData
      this.dnd = new Dnd({
        target: this.graph,
        scaled: false,
        animation: true,
        validateNode(droppingNode, options) {
          if (data.group == 3) {
            that.$set(that.resultInfo.function, 'calculateOriginal', {
              target: [],
            })
            that.$set(that.preservation.function, 'calculateOriginal', {
              target: [],
            })
            that.$set(that.resultInfo, 'code', data.key)
            that.$set(that.preservation, 'code', data.key)
            that.$set(that.chainJson.lineStart, droppingNode.store.data.data.tag, {
              tag: tag,
              cmpType: data.group,
              code: data.key,
              type: 'component',
              breakpoint: false,
            })
            that.$set(that.chainJson.topoModelData, droppingNode.store.data.data.tag, {
              tag: tag,
              cmpType: data.group,
              code: data.key,
              type: 'component',
              breakpoint: false,
            })
            if (!!that.resultInfo.function.calculateOriginal) {
              that.AgreementJudgment = true
            }
          } else {
            that.$set(that.resultInfo.function, droppingNode.store.data.data.tag, {
              param: '',
              resultKey: '',
              code: data.key,
            })
            that.$set(that.preservation.function, droppingNode.store.data.data.tag, {
              param: '',
              resultKey: '',
              code: data.key,
            })
            that.$set(that.chainJson.lineStart, droppingNode.store.data.data.tag, {
              tag: tag,
              cmpType: data.group,
              code: data.key,
              type: 'component',
              breakpoint: false,
            })
            that.$set(that.chainJson.topoModelData, droppingNode.store.data.data.tag, {
              tag: tag,
              cmpType: data.group,
              code: data.key,
              type: 'component',
              breakpoint: false,
            })
          }
          return true
        },
      })
      this.chainJson.topoModelData = that.chainJson.topoModelData
      this.dnd.start(node, e)
      this.tagCache.set(node.id, tag)
    },
    cmpConfigOk(info, tag, identification) {
      if (identification != '' && identification != null) {
        this.paramInfo.chainId = identification
      }
      let infos = JSON.parse(info)
      this.onLabelChange(infos)
      this.calculateJudge = infos.calculate
      if (infos.resultKey != undefined && infos.resultKey != null && infos.resultKey != '') {
        this.configOverview.push(infos.resultKey)
      }
      if (infos.function) {
        if (infos.function.calculateOriginal.target.length > 0) {
          infos.function.calculateOriginal.target.forEach((ele) => {
            this.configOverview.push(ele)
          })
        }
        this.resultInfo.function.calculateOriginal.target = infos.function.calculateOriginal.target
        this.resultInfo.calculate = infos.calculate
        this.resultInfo.key = infos.key
        this.resultInfo.type = infos.type
        this.resultInfo.value = infos.value
        this.preservation.function.calculateOriginal.target = infos.function.calculateOriginal.target
        this.preservation.calculate = infos.calculate
        this.preservation.key = infos.key
        this.preservation.type = infos.type
        this.preservation.value = infos.value
        if (infos.parent != undefined) {
          this.resultInfo.parent = infos.parent
          this.preservation.parent = infos.parent
        }
      } else {
        this.$delete(this.resultInfo.function, tag)
        this.$set(this.resultInfo.function, tag, infos)
        this.$delete(this.preservation.function, tag)
        this.$set(this.preservation.function, tag, infos)
      }
      this.configOverview = Array.from(new Set(this.configOverview))
      this.chainParamVo.chainParam = JSON.stringify(this.resultInfo)
    },
    save() {
      if (this.chainId === '' || this.chainId == null || this.chainName === '' || this.chainName == null) {
        this.$message.warning('请确认流程标识或流程名称是否填写')
        return
      }
      if (this.AgreementJudgment == false) {
        this.$message.warning('请确认是否添加起始组件')
        return
      }
      if ((this.preservation.value == '' || this.preservation.value == null) && this.calculateJudge == false) {
        this.$message.warning('请确认组件是否配置')
        return
      }
      let data = Object.values(this.preservation.function)
      for (let i = 0; i < data.length; i++) {
        if (data[i].param != undefined && data[i].param == '' && data.param == null) {
          this.$message.warning('请确认组件是否配置')
          return
        }
      }
      this.createChains()
      if (!this.isPass) {
        return
      }
      let topoJson = this.graph.toJSON()
      let xlink = 'xlink:href'
      topoJson.cells.forEach((item, i) => {
        if (topoJson.cells[i].position && topoJson.cells[i].position != null) {
          let images = topoJson.cells[i].attrs.image[xlink].split('/')
          images.forEach((ele, index) => {
            if (images[index] == 'sys') {
              images.splice(0, index)
              topoJson.cells[i].attrs.image[xlink] = images.join('/')
            }
          })
        }
      })
      let container = this.$refs.container.clientWidth
      let params = {}
      topoJson.screenSize = container
      params.topoDataJson = JSON.stringify(topoJson)
      params.chainId = this.chainId
      params.chainName = this.chainName
      params.chainDesc = this.chainDesc
      params.protocol = this.protocol
      if (this.parentId != '' && this.parentId != null) {
        params.parentId = this.parentId
      }
      params.productId = this.productId
      params.chain = this.paramChain
      params.chainJson = JSON.stringify(this.chainJson)
      params.params = JSON.stringify(this.preservation)
      params.metadataId = this.metadataId
      params.id = this.id
      this.chainParamVo.chain = params.chain
      this.chainParamVo.chainId = params.chainId
      this.chainParamVo.chainParam = params.params
      let loading = this.$loading({
        background: 'rgba(0,0,0,0.6)',
      })
      loading.visible = true
      httpAction(this.url.edit, params, 'put')
        .then((res) => {
          loading.visible = false
          if (res.success) {
            this.$message.success(res.message)
            this.id = res.result.id
            this.$emit('ok', params)
          } else {
            this.$message.warning(res.message)
          }
        })
        .finally(() => {
          loading.visible = false
        })
    },
    createChains() {
      let cmps = Object.entries(this.chainJson.topoModelData).filter(([key, value]) => {
        return value.type === 'component'
      })

      let rels = Object.entries(this.chainJson.topoModelData).filter(([key, value]) => {
        return value.type === 'relation'
      })

      if (cmps.length === 0) {
        this.$message.warning('未配置任何组件')
        return
      }
      //检查是否为一条链路
      if (Object.values(this.chainJson.lineStart).length !== 1) {
        this.$message.warning('有多个起始节点')
        return
      }
      let start = Object.values(this.chainJson.lineStart)[0]
      if (start.cmpType !== '3') {
        this.$message.warning('起始节点应该为协议组件')
        return
      }

      this.isPass = this.checkNodeRule(cmps, rels)
      //开始拼接
      if (this.isPass) {
        let res = this.createChain(start, new Map(cmps), rels)
        this.paramChain = 'THEN(' + res.chain + ');'
      }
    },

    //检查节点关系是否符合规则
    checkNodeRule(cmps, rels) {
      console.log('rels', rels)
      console.log('cmps', cmps)
      let endNum = 0
      for (let [tag, value] of cmps) {
        let cmpType = value.cmpType

        //该组件的前驱组件
        let precursor = rels.filter(([key, value]) => {
          return key.endsWith(tag)
        })

        //该组件的后继组件
        let successor = rels.filter(([key, value]) => {
          return key.startsWith(tag)
        })

        let precursorNum = precursor.length
        let successorNum = successor.length

        switch (cmpType) {
          case '3':
            if (precursorNum !== 0 || successorNum > 1) {
              this.$message.warning('起始节点应该为协议组件')
              return false
            }
            break
          case '1':
          case '2':
            if (precursorNum < 1 || precursorNum > 2 || successorNum > 1) {
              this.$message.warning(`[tag: ${tag}]: 该节点的连接关系不符合普通组件的规则`)
              return false
            }
            break
          case '4':
            if (precursorNum < 1 || precursorNum > 2 || successorNum < 1 || successorNum > 2) {
              this.$message.warning(`[tag: ${tag}]: 该节点的连接关系不符合判断组件的规则`)
              return false
            }
            break
          case '5':
            if (precursorNum < 1 || precursorNum > 2 || successorNum < 1 || successorNum > 2) {
              this.$message.warning(`[tag: ${tag}]: 该节点的连接关系不符合循环组件的规则`)
              return false
            }
            break
          case '6':
            endNum++
            if (precursorNum < 1 || precursorNum > 2 || successorNum !== 0) {
              this.$message.warning(`[tag: ${tag}]: 该节点的连接关系不符合结束组件的规则`)
              return false
            }
            break
        }

        if (precursorNum === 2) {
          value.breakpoint = true
        }
      }
      if (endNum < 1) {
        this.$message.warning('缺少结束节点')
        return false
      }
      return true
    },

    //生成链条
    createChain(start, cmps, rels) {
      let res = {
        chain: '',
        next: null,
      }
      let next
      if (start == null) {
        return res
      }
      let cmpType = start.cmpType
      let code = start.code
      let tag = start.tag
      let nextNodes = rels.filter(([key, value]) => {
        return value.source === tag
      })

      if (nextNodes.length === 0 && cmpType === '3') {
        //无后继且为协议组件
        res.chain = `${code}`
        return res
      } else if (nextNodes.length === 0 && cmpType !== '3') {
        //无后继且不为协议组件
        res.chain = `${code}.tag("${tag}")`
        if (start.breakpoint) {
          res.next = start
        }
        return res
      } else if (cmpType === '3') {
        let [sk, sv] = nextNodes[0]
        next = cmps.get(sv.target)
        res.chain = code + ',' + this.createChain(next, cmps, rels).chain
        return res
      } else if (cmpType === '1' || cmpType === '2' || cmpType === '6') {
        let [sk, sv] = nextNodes[0]
        next = cmps.get(sv.target)
        res.chain = `${code}.tag("${tag}")`
        console.log(sk, next)
        if (next.breakpoint) {
          res.next = next
        } else if (start.breakpoint) {
          res.next = start
        } else {
          let res1 = this.createChain(next, cmps, rels)
          res.chain += `,${res1.chain}`
          res.next = res1.next
        }
        console.log('res', res)
        return res
      } else if (cmpType === '4') {
        if (nextNodes.length === 2) {
          let [sk1, sv1] = nextNodes[0]
          let [sk2, sv2] = nextNodes[1]
          let start1 = cmps.get(sv1.target)
          let start2 = cmps.get(sv2.target)
          let res1 = this.createChain(start1, cmps, rels)
          let res2 = this.createChain(start2, cmps, rels)
          if (res1.next === res2.next) {
            let res3 = this.createChain(res1.next, cmps, rels)
            res.chain = `IF(${code}.tag("${tag}"),THEN(${res1.chain}),THEN(${res2.chain}))`
            if (res3.chain !== '') {
              res.chain += `,${res3.chain}`
            }
            if (res3.next != null) {
              res.next = res3.next
            }
          }
          return res
        } else {
          let [sk, sv] = nextNodes[0]
          next = cmps.get(sv.target)
          let chain = this.createChain(next, cmps, rels).chain
          res.chain = `IF(${code}.tag("${tag}"),THEN(${chain}))`
          return res
        }
      } else if (cmpType === '5') {
        let [sk1, sv1] = nextNodes[0]
        let [sk2, sv2] = nextNodes[1]
        let start1 = cmps.get(sv1.target)
        let start2 = cmps.get(sv2.target)
        let res1 = this.createChain(start1, cmps, rels)
        let res2 = this.createChain(start2, cmps, rels)
        console.log('res1', res1)
        console.log('res2', res2)
        if (res1.next != null) {
          res.chain = `FOR(${code}.tag("${tag}")).DO(THEN(${res1.chain}))`
          let res3 = this.createChain(res1.next, cmps, rels)
          if (res3.chain !== '') {
            res.chain += `,${res3.chain}`
          }
          if (res3.next != null) {
            res.next = res3.next
          }
        } else if (res2.next != null) {
          res.chain = `FOR(${code}.tag("${tag}")).DO(THEN(${res2.chain}))`
          let res3 = this.createChain(res2.next, cmps, rels)
          if (res3.chain !== '') {
            res.chain += `,${res3.chain}`
          }
          if (res3.next != null) {
            res.next = res3.next
          }
        }
        console.log('res5', res)
        return res
      }
      return res
    },
  },
}
</script>

<style lang="less" scoped>
.edge-div {
  display: none;
  background-color: #fff;
  border-radius: 5px;
  color: #595959;
  position: absolute;
  top: 405px;
  left: 775px;
  z-index: 100;

  p {
    margin-bottom: 0px;
    height: 30px;
    line-height: 30px;
    padding: 0px 10px;
  }
}

.graphcontainer {
  height: 100% !important;
  width: 100% !important;
  flex: 1;
}

.wrap {
  height: 100% !important;
  display: flex;

  .dataTree {
    height: 100%;
    margin-right: 16px;
    border: 1px solid rgba(0, 0, 0, 0.08) !important;
    margin-right: 10px;
  }
}

.panel {
  width: calc(100% - 550px);
  height: 100%;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  background: linear-gradient(to bottom, rgb(193, 233, 241), rgb(204, 239, 245));
}

.config {
  width: 320px;
  margin-left: 16px;
}

.consoleClass {
  max-height: 600px;
  overflow: auto;
  width: 100%;
  float: left;
  position: fixed;
  bottom: 55px;
  margin-left: -24px;

  p {
    max-height: 200px;
    overflow-y: auto;
  }
}

/deep/ .jsoneditor-vue .jsoneditor-outer {
  height: 300px;
}

.collectInfoBox {
  height: 330px;
  overflow: hidden;
  overflow-y: auto;
}

.subClass {
  margin-left: 88%;
  background-color: #409eff;
  color: white;
  border: none;
}

.debugClass {
  background-color: #409eff;
  color: white;
  border: none;
  position: absolute;
  bottom: 15px;
}

/deep/ .cmpClass {
  height: 30px;
  width: 30px;
}
::v-deep .jsoneditor-poweredBy,
::v-deep .jsoneditor-modes {
  display: none;
}
</style>