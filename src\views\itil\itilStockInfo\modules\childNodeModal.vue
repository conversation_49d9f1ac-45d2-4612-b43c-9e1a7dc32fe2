<template>
  <j-modal
    :title="title"
    :width="width"
    :centered='true'
    :visible="visible"
    switchFullscreen
    :maskClosable="true"
    @ok="handleOk"
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }"
    @cancel="handleCancel"
    cancelText="关闭"
  >
    <child-node-form ref="realForm" @ok="submitCallback" :disabled="disableSubmit"></child-node-form>
  </j-modal>
</template>

<script>
import childNodeForm from './childNodeForm'
export default {
  name: 'ItilStockInfoModal',
  components: {
    childNodeForm,
  },
  data() {
    return {
      title: '',
      width: 800,
      visible: false,
      disableSubmit: false,
    }
  },
  methods: {
    add(id) {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.add(id)
      })
    },
    edit(record) {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.edit(record)
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      this.$refs.realForm.submitForm()
    },
    submitCallback() {
      this.$emit('ok')
      this.visible = false
    },
    handleCancel() {
      this.close()
    },
  },
}
</script>
<style lang='less' scoped>
@import '~@assets/less/normalModal.less';
</style>