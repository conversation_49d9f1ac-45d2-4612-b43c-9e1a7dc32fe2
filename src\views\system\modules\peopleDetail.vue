<template>
  <a-card style="height: 100%;">
    <a-row>
      <a-col :span="24">
        <span style="float: right;margin-bottom: 12px;"><img src="~@/assets/return1.png" alt="" @click="getGo"
            style="width: 20px;height: 20px;cursor: pointer"></span>
      </a-col>
      <!-- <div class='colorBox'>
        <span class="colorTotal">服务商信息</span>
      </div> -->
      <a-col :span="24">
        <table class="gridtable">
          <tr>
            <td class="leftTd">用户账号</td>
            <td class="rightTd">{{ data.username }}</td>
            <td class="leftTd">用户姓名</td>
            <td class="rightTd">{{ data.realname }}</td>
          </tr>
          <tr>
            <td class="leftTd">工号</td>
            <td class="rightTd">{{ data.workNo }}</td>
            <td class="leftTd">服务优先级</td>
            <td class="rightTd">{{ data.priority }}</td>
          </tr>
          <tr>
            <td class="leftTd">邮箱</td>
            <td class="rightTd">{{ data.email }}</td>
            <td class="leftTd">手机号</td>
            <td class="rightTd">{{ data.phone }}</td>
          </tr>
          <tr>
            <td class="leftTd">座机</td>
            <td class="rightTd">{{ data.telephone }}</td>
            <td class="leftTd">头像</td>
            <td class="rightTd">
              <a-avatar shape='square' :src='getAvatarView(data.avatar)' icon='user' />
            </td>
          </tr>
        </table>
      </a-col>
    </a-row>
  </a-card>
</template>

<script>
  import {
    getFileAccessHttpUrl,
    getAction
  } from '@/api/manage'
  export default {
    name: 'networkPlanDetails',
    props: {
      data: {
        type: Object
      }
    },
    data() {
      return {
        form: this.$form.createForm(this),
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          },
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          },
        },
        confirmLoading: false,
      }
    },
    mounted() {},
    methods: {
      getAvatarView: function (avatar) {
        return getFileAccessHttpUrl(avatar)
      },
      //返回上一级
      getGo() {
        this.$parent.pButton2(0);
      }
    }
  }
</script>
<style scoped>
  table.gridtable {
    font-family: verdana, arial, sans-serif;
    font-size: 14px;
    color: #606266;
    border-width: 1px;
    border-color: #e8e8e8;
    border-collapse: collapse;
    text-align: left;
    width: 100%;
  }

  table.gridtable td {
    border-width: 1px;
    border-style: solid;
    border-color: #e8e8e8;
  }

  .leftTd {
    width: 17%;
    background-color: #FAFAFA;
    padding: 16px 24px;
    text-align: center;
  }

  .rightTd {
    width: 35%;
    padding: 16px 24px;
  }

  .colorBox {
    margin-bottom: 10px;
  }

  .colorTotal {
    padding-left: 7px;
    border-left: 4px solid #1e3674;
  }
</style>