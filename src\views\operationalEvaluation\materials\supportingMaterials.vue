<template>
  <div class="material-page-box">
    <div class='top-box' ref="topBox" v-if='projectInfo&&projectInfo.length>0'>
      <div class="header-left">
        <p class='header-left-title'>
          <span>{{ projectInfo[0].content }}</span>
        </p>
        <p class='header-left-bottom'>
          <span class='assets-code'>发起人：<span style="color: #000">{{ projectInfo[1].content  }}</span></span>
          <span class='assets-code'>评估时间：<span style="color: #000">{{ projectInfo[2].content  }}</span></span>
        </p>
      </div>
      <div class="header-right" style='width: 21px'>
          <span class='header-back'>
            <img src='~@assets/return1.png' alt='' @click='goBack'  style='width: 20px; height: 20px; cursor: pointer' />
          </span>
      </div>
    </div>

    <div class="bottom-box">
      <div class="bottom-box-content">
        <div class="bottom-left-content" :style="{height:`calc(100vh - 59px - ${topHeight} - 16px - 16px - 16px - 24px - 24px)`,minHeight: '450px'}">
          <device-tree-expand
            :inputFlag='false'
            :inputSearchMargin="'0px'"
            :bottomTreeMargin="'0px 14px 0px 14px'"
            @selected='treeSeletedSearch'
            :tree-url="'/evaluate/metricsType/tree'"
            :btnIconName="'appstore'"
            :is-show-all-btn='true'
            :btnName="'全部材料'"
            :fieldKey='"id"'
            :is-show-btn-icon='true'
            :is-show-icon='false'>
          </device-tree-expand>
        </div>
        <div class="bottom-right-content" :style="{height:`calc(100vh - 59px - ${topHeight} - 16px - 16px - 16px - 24px - 24px)`,minHeight: '450px'}">
          <div class='table-page-search-wrapper'>
            <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
              <a-row :gutter='24' ref='row'>
                <a-col :span='spanValue'>
                  <a-form-item label='名称'>
                    <a-input
                      :maxLength='maxLength'
                      :allowClear='true'
                      autocomplete='off'
                      v-model='queryParam.materialName'
                      placeholder='请输入名称'
                    />
                  </a-form-item>
                </a-col>

                <a-col :span='colBtnsSpan()'>
               <span class='table-page-search-submitButtons'
                     :style="toRight && { float: 'right', overflow: 'hidden' } || {} ">
                  <a-button type='primary' class='btn-search btn-search-style' @click='searchQuery'>查询</a-button>
                  <a-button class='btn-reset btn-reset-style' @click='searchReset'>重置</a-button>
                  <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                   <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
               </span>
                </a-col>
              </a-row>
            </a-form>
          </div>
          <div style='flex: auto'>
            <div class='table-operator table-operator-style'>
              <a-button @click="handleAdd">新增</a-button>
              <a-dropdown v-if="selectedRowKeys.length > 0">
                <a-menu slot="overlay" style='text-align: center'>
                  <a-menu-item key="1" @click="batchDel"> 删 除</a-menu-item>
                </a-menu>
                <a-button>
                  批量操作
                  <a-icon type="down" />
                </a-button>
              </a-dropdown>
            </div>
            <a-table
              ref='table'
              bordered
              rowKey='id'
              :columns='columns'
              :dataSource='dataSource'
              :scroll='dataSource.length > 0 ? { x:"max-content"} : {}'
              :pagination='ipagination'
              :loading='loading'
              :rowSelection='{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }'
              @change='handleTableChange'>
          <span class='caozuo' slot='action' slot-scope='text, record'>
            <a @click='handleDetail(record)'>查看</a>
            <a-divider type="vertical" />
            <a-dropdown>
            <a class="ant-dropdown-link">更多
              <a-icon type="down" />
            </a>
            <a-menu slot="overlay">
              <a-menu-item >
               <a @click="handleEdit(record)">编辑</a>
              </a-menu-item>
              <a-menu-item >
              <a @click="confirmDelete(record.id)">删除</a>
              </a-menu-item>
              <a-menu-item >
             <a @click="handleExport(record,'word')">word导出</a>
              </a-menu-item>
              <a-menu-item >
             <a @click="handleExport(record,'pdf')">pdf导出</a>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
          </span>
              <template slot='tooltip' slot-scope='text'>
                <a-tooltip placement='topLeft' :title='text' trigger='hover'>
                  <div class='tooltip'>
                    {{ text }}
                  </div>
                </a-tooltip>
              </template>
            </a-table>
          </div>
        </div>
      </div>
    </div>

    <supporting-materials-modal ref="modalForm" @ok="modalFormOk"></supporting-materials-modal>
  </div>
</template>

<script>
import DeviceTreeExpand from '@/components/tree/DeviceTreeExpand.vue'
import '@/assets/less/TableExpand.less'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { deleteAction, downFile, getAction } from '@/api/manage'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
import supportingMaterialsModal from '@views/operationalEvaluation/materials/modules/supportingMaterialsModal.vue'

export default {
  name: 'supportingMaterials',
  props: {
    data: {
      type: Object,
      required: true
    }
  },
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components: {DeviceTreeExpand, supportingMaterialsModal },
  data() {
    return {
      maxLength: 50,
      disableMixinCreated: true,
      formItemLayout: {
        labelCol: {
          style: 'width:50px'
        },
        wrapperCol: {
          style: 'width:calc(100% - 50px)'
        }
      },
      uploading:false,
      topHeight:'100px',
      projectInfo:[
        {
          icon:'folder',
          field:'projectName',
          title:"项目名称",
          content:''
        },
        {
          icon:'folder',
          field:'sender_dictText',
          title:"发起人",
          content:''
        },
        {
          icon:'folder',
          field:'evaluateTime',
          title:"评估时间",
          content:''
        },
      ],
      // 表头
      columns: [
        {
          title: '名称',
          dataIndex: 'materialName'
        },
        {
          title: '类型',
          dataIndex: 'materialType'
        },
        {
          title: '指标',
          dataIndex: 'metricsId_dictText'
        },
        {
          title: '说明',
          dataIndex: 'materialDesc',
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 100px;max-width:400px'
            return { style: cellStyle }
          }
        },
        {
          title: '创建人',
          dataIndex: 'createBy_dictText'
        },
        {
          title: '创建日期',
          dataIndex: 'createTime'
        },
        {
          title: '操作',
          dataIndex: 'action',
          fixed: 'right',
          width: 120,
          scopedSlots: { customRender: 'action' }
        }
      ],

      url: {
        list: '/evaluate/materialInfo/pageList',
        delete:'/evaluate/materialInfo/delete',
        deleteBatch:'evaluate/materialInfo/deleteBatch',
        exportInfoAndFile:'/evaluate/materialInfo/exportInfoAndFile'
      },
    }
  },
  watch:{
    data: {
      handler(val){
        if (val){
          this.projectInfo.map((item)=>{
            item.content=val[item.field]
            return item
          })
          this.queryParam.projectId=val.id
          this.queryParam.materialType=''
          this.queryParam.metricsId=undefined
          this.loadData(1)
        }
      },
      deep:true,
      immediate:true
    }
  },
  mounted() {
    window.addEventListener('resize', this.getHeight)
    this.$nextTick(() => {
      this.getHeight()
    })
  },
  methods: {
    getHeight() {
      let rect = this.$refs.topBox.getBoundingClientRect()
      this.topHeight =rect.height+'px'
    },
    treeSeletedSearch(option = '', type = '',title='') {
      if (type){
        this.queryParam.metricsTypeId=type==='category'?option:''
        this.queryParam.metricsId=type==='category'?'':option
        this.queryParam.metricsId_dictText=type==='category'?'':title
      }else {
        this.queryParam.metricsTypeId=''
        this.queryParam.metricsId=undefined
        this.queryParam.metricsId_dictText=''
      }
      this.loadData(1)
    },
    handleAdd() {
      let obj={
        projectId:this.data.id,
        metricsId:this.queryParam.metricsId||undefined,
        metricsId_dictText:this.queryParam.metricsId_dictText||''
      }
      this.$refs.modalForm.add(obj);
      this.$refs.modalForm.title = '新增';
      this.$refs.modalForm.disableSubmit = false;
    },
    handleDetail: function (record) {
      let obj={
        projectId:this.data.id,
        metricsId:this.queryParam.metricsId||undefined,
        metricsId_dictText:this.queryParam.metricsId_dictText||''
      }
      this.$refs.modalForm.edit(record,  obj)
      this.$refs.modalForm.title = '详情'
      this.$refs.modalForm.disableSubmit = true
    },
    handleEdit: function (record) {
      let obj={
        projectId:this.data.id,
        metricsId:this.queryParam.metricsId||undefined,
        metricsId_dictText:this.queryParam.metricsId_dictText||undefined
      }
      this.$refs.modalForm.edit(record,  obj);
      this.$refs.modalForm.title = '编辑';
      this.$refs.modalForm.disableSubmit = false;
    },
    handleExport(record,type){
      if (!this.uploading){
        this.uploading = true
        downFile(this.url.exportInfoAndFile, {id:record.id,exportType:type}).then((data) => {
          if (!data) {
            this.$message.warning('文件下载失败')
            this.uploading = false
            return
          }
          if (typeof window.navigator.msSaveBlob !== 'undefined') {
            window.navigator.msSaveBlob(new Blob([data], { type: 'application/vnd.ms-excel' }), fileName + '.excel')
          }
          else {
            if (data.type == 'multipart/form-data') {
              let url = window.URL.createObjectURL(new Blob([data], { type: 'multipart/form-data' }))
              let link = document.createElement('a')
              link.style.display = 'none'
              link.href = url
              link.setAttribute('download', record.materialName + '详情.zip')
              document.body.appendChild(link)
              link.click()
              // 清理资源
              setTimeout(() => {
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
              }, 100)
            }
            else {
              this.$message.error('导出失败')
            }
          }
          this.uploading = false
        }).catch(()=>{
          this.uploading = false
        })
      }
    },
    goBack() {
      this.$parent.pButton1(0)
    },
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import "~@assets/less/scroll.less";
.material-page-box {
  //height: 100%;
  //background-color: #fff;

  display: flex;
  flex-flow: column nowrap;
  //overflow: hidden;
  .top-box {
    display: flex;
    justify-content: space-between;
    align-items: start;
    margin-bottom: 16px;
    background-color: #fff;
    border-radius: 2px;
    padding: 24px;

    .header-left {
      width: calc(100% - 21px);
      margin-right: 16px;
      margin-bottom: -12px;

      .header-left-title {
        margin-bottom: 10px;
        font-family: PingFangSC-Medium;
        font-size: 18px;
        color: #000000;
      }

      .header-left-bottom {
        margin-bottom: 0px;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
        display: flex;
        justify-content: start;
        align-items: start;
        flex-flow: row wrap;

        .assets-code {
          margin-right: 119px;
          display: flex;
          height: 32px;
          line-height: 32px;
        }

        .assets-status-box {
          display: flex;
          justify-content: space-between;
          flex-flow: row nowrap;
          align-items: center;
          white-space: nowrap;
          height: 32px;
          line-height: 32px;
          font-size: 14px;

          .assets-status {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.65);
          }

          .status {
            padding-left: 7px;
          }
        }
      }
    }
  }

/*  .top-box{
    !*padding-bottom: 12px;
    border-bottom: 1px solid #e8e8e8;*!
    padding: 12px 24px 0;

    .back-box{
      text-align: right;
      margin-bottom: 12px;
      img {
        width: 20px;
        height: 20px;
        cursor: pointer;
      }
    }

    .project-info-box{
    }
  }*/
  .bottom-box{
    background-color: #fff;
    border-radius: 2px;
    padding-top: 24px;
    padding-bottom: 24px;
    flex: 1;
    display: flex;
    flex-flow: column nowrap;
    justify-content: start;
    align-items: flex-start;
    //margin-top:24px;

    .bottom-box-content{
      flex: 1;
      width: 100%;
      //min-height: 300px;
      display: flex;
      flex-flow: row nowrap;
      justify-content: start;
      align-items: flex-start;

      .bottom-left-content{
        width: 288px;
        height: 100%;
        margin-right: 12px;
        border-right: 1px solid #e8e8e8;
      }
      .bottom-right-content{
        height: 100%;
        width: calc(100% - 300px - 1px);
        margin-right: 1px;
        padding-right: 23px;
        //margin-bottom: 24px;
        overflow-y: auto;
      }
    }
  }


  .title-box {
    font-size: 14px;
    margin-bottom: 10px;
    .title-text {
      padding-left: 7px;
      border-left: 4px solid #1e3674;
    }
  }
}
</style>

