<template>
  <a-card style='height: 100%' class='vScroll zxw'>
    <!-- <div>密码规则设置</div> -->
    <div class='table-page-search-wrapper'>
      <j-form-container style='height: 100%'>
        <a-form layout='inline' :form='form' slot='detail' style='height: 100%'>
          <a-row>
            <a-col :span='24'>
              <div class='title-row'>密码规则设置</div>
            </a-col>
          </a-row>
          <a-row class='second-row'>
            <a-col :md='12' :sm='24' :xs='24'>
              <a-form-item label='超时时长(分钟)' :labelCol='labelCol' :wrapperCol='wrapperCol'>
                <a-input placeholder='请输入超时时长' type='number' :min='1' autocomplete='off' :allowClear='true'
                  v-decorator="['overTime', validatorRules.overTime]" />
              </a-form-item>
            </a-col>
            <a-col :md='12' :sm='24' :xs='24'>
              <a-form-item label='密码最小位数' :labelCol='labelCol' :wrapperCol='wrapperCol'>
                <a-input placeholder='请输入密码最小位数' type='number' autocomplete='off' :allowClear='true' :min='minNumber'
                  v-decorator="['pwdMin', validatorRules.pwdMin]" />
              </a-form-item>
            </a-col>
            <a-col :md='12' :sm='24' :xs='24'>
              <a-form-item label='锁账户错误次数' :labelCol='labelCol' :wrapperCol='wrapperCol'>
                <a-input placeholder='请输入失误次数' type='number' :min='1' autocomplete='off' :allowClear='true'
                  v-decorator="['errorNum', validatorRules.errorNum]" />
              </a-form-item>
            </a-col>
            <a-col :md='12' :sm='24' :xs='24'>
              <a-form-item label='自动解锁(分钟)' :labelCol='labelCol' :wrapperCol='wrapperCol'>
                <a-input placeholder='请输入自动解锁时间' type='number' autocomplete='off' :allowClear='true' :min='1'
                  v-decorator="['unLock', validatorRules.unLock]" />
              </a-form-item>
            </a-col>
            <a-col :md='12' :sm='24' :xs='24'>
              <a-form-item label='密码更新时间(天)' :labelCol='labelCol' :wrapperCol='wrapperCol'>
                <a-input placeholder='密码更新时间' type='number' autocomplete='off' :allowClear='true' :min='1'
                  v-decorator="['updateTimes', validatorRules.updateTimes]" />
              </a-form-item>
            </a-col>
            <a-col :md='12' :sm='24' :xs='24'>
              <a-form-item label='密码复杂度设置' :labelCol='labelCol' :wrapperCol='wrapperCol'>
                <j-checkbox v-decorator="['complexPwd', validatorRules.complexPwd]" :options='options'
                  @change='onChange'></j-checkbox>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span='24'>
              <div class='horizontal'></div>
            </a-col>
          </a-row>
          <a-row class='second-row'>
            <a-col :md='12' :sm='24' :xs='24'>
              <a-form-item label='开始时间(HH:ss)' :labelCol='labelCol' :wrapperCol='wrapperCol'>
                <a-input v-decorator="['startTime']" autocomplete='off' :allowClear='true' placeholder='请输入开始时间' />
              </a-form-item>
            </a-col>
            <a-col :md='12' :sm='24' :xs='24'>
              <a-form-item label='结束时间(HH:ss)' :labelCol='labelCol' :wrapperCol='wrapperCol'>
                <a-input v-decorator="['stopTime']" autocomplete='off' :allowClear='true' placeholder='请输入结束时间' />
              </a-form-item>
            </a-col>
            <a-col :md='12' :sm='24' :xs='24'>
              <a-form-item label='开始ip段(0.0.0.0)' :labelCol='labelCol' :wrapperCol='wrapperCol'>
                <a-input v-decorator="['startIp']" autocomplete='off' :allowClear='true'
                  placeholder='请输入开始ip段/0.0.0.0' />
              </a-form-item>
            </a-col>
            <a-col :md='12' :sm='24' :xs='24'>
              <a-form-item label='结束ip段(***************)' :labelCol='labelCol' :wrapperCol='wrapperCol'>
                <a-input v-decorator="['stopIp']" autocomplete='off' :allowClear='true'
                  placeholder='请输入结束ip段/***************' />
              </a-form-item>
            </a-col>
            <a-col :md='12' :sm='24' :xs='24'>
              <a-form-item label='MAC地址' :labelCol='labelCol' :wrapperCol='wrapperCol'>
                <a-input v-decorator="['mac']" autocomplete='off' :allowClear='true'
                  placeholder='请输入mac地址/xx-xx-xx-xx-xx-xx' />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span='24' style='text-align: center; margin-top: 0.45rem;margin-bottom: 0.45rem /* 20/80 */'>
              <a-button @click='submitForm' class='submit-btn'>提交</a-button>
              <a-button @click='doreset' class='reset-btn' style='margin-left: 8px'>重 置</a-button>
            </a-col>
          </a-row>
        </a-form>
      </j-form-container>
    </div>

  </a-card>
</template>

<script>
  import {
    httpAction,
    getAction
  } from '@/api/manage'
  import pick from 'lodash.pick'
  import {
    validateDuplicateValue
  } from '@/utils/util'
  import JFormContainer from '@/components/jeecg/JFormContainer'
  import JDictSelectTag from '@/components/dict/JDictSelectTag'
  import JCheckbox from '@/components/jeecg/JCheckbox'

  export default {
    name: 'UmpPwdManageForm',
    components: {
      JFormContainer,
      JDictSelectTag,
      JCheckbox
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data() {
      return {
        form: this.$form.createForm(this),
        minNumber: 6,
        model: {},
        labelCol: {
          style: 'width:200px;float:left'
          // md:{span:7},
          // xs: { span: 24 },
          // sm: { span: 10 },
        },
        wrapperCol: {
          // md: { span: 13 },
          // xs: { span: 24 },
          // sm: { span: 12 }
        },
        confirmLoading: false,
        isThreePowers: 0,
        validatorRules: {
          overTime: {
            rules: [{
              required: true,
              message: '请输入超时时长!'
            }]
          },
          pwdMin: {
            rules: [{
              required: true,
              message: '请输入密码位数!'
            }]
          },
          errorNum: {
            rules: [{
              required: true,
              message: '请输入锁账户错误次数!'
            }]
          },
          unLock: {
            rules: [{
              required: true,
              message: '请输入自动解锁!'
            }]
          },
          updateTimes: {
            rules: [{
              required: true,
              message: '请输入密码更新时间!'
            }]
          },
          complexPwd: {
            rules: [{
              required: true,
              message: '请选择密码复杂度!'
            }]
          }
        },
        url: {
          list: '/umpPwdManage/umpPwdManage/list',
          add: '/umpPwdManage/umpPwdManage/add',
          edit: '/umpPwdManage/umpPwdManage/edit',
          queryById: '/umpPwdManage/umpPwdManage/queryById',
          isThreePowers: '/sys/isThreePowers', //三员是否开启
        },
        options: [{
            label: '大写字母',
            value: 'capitalize'
          },
          {
            label: '小写字母',
            value: 'lowercase'
          },
          {
            label: '数字',
            value: 'hasNum'
          },
          {
            label: '特殊字符',
            value: 'special'
          }
        ]
      }
    },
    computed: {},
    created() {
      this.loadData()
      this.getIsThreePowers()
    },
    methods: {
      getIsThreePowers() {
        getAction(this.url.isThreePowers).then((res) => {
          if (res.code == 200) {
            this.isThreePowers = res.result
            if (this.isThreePowers) {
              this.minNumber = 10
            }
          } else {
            this.$message.error(res.message)
          }
        })
      },
      getFormFieldValue(field) {
        return this.form.getFieldValue(field)
      },
      onChange(Values) {},
      loadData() {
        getAction(this.url.list).then((res) => {
          if (res.success) {
            this.setForm(res.result.records[0] || {})
          }
        })
      },
      // add() {
      //   this.edit({})
      // },
      setForm(record) {
        let complexPwds = []
        if (record.capitalize === 1) {
          complexPwds.push('capitalize')
        }
        if (record.lowercase === 1) {
          complexPwds.push('lowercase')
        }
        if (record.hasNum === 1) {
          complexPwds.push('hasNum')
        }
        if (record.special === 1) {
          complexPwds.push('special')
        }
        this.form.resetFields()
        this.model = Object.assign({}, record)
        this.model = {
          ...this.model,
          complexPwd: complexPwds.join(',')
        }
        this.visible = true
        this.$nextTick(() => {
          this.form.setFieldsValue(
            pick(
              this.model,
              'overTime',
              'pwdMin',
              'errorNum',
              'unLock',
              'updateTimes',
              'stopTime',
              'startTime',
              'startIp',
              'stopIp',
              'mac',
              'complexPwd'
            )
          )
        })
      },
      doreset() {
        this.form.resetFields()
        // this.form.setFieldsValue(
        //   pick(
        //     {},
        //     'overTime',
        //     'pwdMin',
        //     'errorNum',
        //     'unLock',
        //     'updateTimes',
        //     'stopTime',
        //     'startTime',
        //     'startIp',
        //     'stopIp',
        //     'mac',
        //     'complexPwd'
        //   )
        // )
      },
      submitForm() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.model.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'put'
            }
            const complexPwd = this.getFormFieldValue('complexPwd')
            let formData = Object.assign(this.model, values)
            formData.capitalize = complexPwd.includes('capitalize') ? 1 : 0
            formData.lowercase = complexPwd.includes('lowercase') ? 1 : 0
            formData.hasNum = complexPwd.includes('hasNum') ? 1 : 0
            formData.special = complexPwd.includes('special') ? 1 : 0
            let complexPwdLength = []
            complexPwdLength = complexPwd.split(',')
            if (this.isThreePowers) {
              if (complexPwdLength.length < 2) {
                this.$message.warning('密码复杂度设置应至少选择两项')
                return
              }
              if (formData.pwdMin < 10) {
                this.$message.warning('密码最小位数不小于10')
                return
              }
            }
            httpAction(httpurl, formData, method)
              .then((res) => {
                if (res.success) {
                  that.$message.success(res.message)
                  // that.$emit('ok')
                } else {
                  that.$message.warning(res.message)
                }
              })
              .finally(() => {
                that.confirmLoading = false
              })
          }
        })
      },
      popupCallback(row) {
        this.form.setFieldsValue(
          pick(
            row,
            'overTime',
            'pwdMin',
            'errorNum',
            'unLock',
            'updateTimes',
            'stopTime',
            'startTime',
            'startIp',
            'stopIp',
            'mac'
          )
        )
      }
    }
  }
</script>
<style lang='less' scoped>
  @import '~@assets/less/scroll.less';

  .ant-card {
    height: 100%;
    border-radius: 3px !important;
  }

  ::v-deep .ant-card-body {
    //height: 100%;
    padding: 22px 23px 30px !important;

    >div {
      height: 100%;
      border: 1px solid #dedede;
      border-radius: 4px;
    }
  }

  .title-row {
    height: 48px;
    background: #f5f5f5;
    border-bottom: 1px solid #dedede;
    padding: 13px 0 13px 24px;
    font-family: PingFangSC-Medium;
    font-size: 16px;
    color: rgba(0, 0, 0, 0.85);
  }

  .second-row {
    margin-top: 24px;
    padding-left: 20px;
    padding-right: 20px;
  }

  .submit-btn {
    color: #409eff;
    background: #ecf5ff;
    border-color: #b3d8ff;
  }

  .submit-btn:hover {
    color: #fff !important;
    background-color: #409eff !important;
    border-color: #409eff !important;
  }

  .reset-btn:hover {
    color: #409eff;
    background: #fff;
    border-color: #b3d8ff;
  }

  .horizontal {
    border-bottom: 1px solid #dedede;
    margin-top: 0.3125rem;
    margin-bottom: 0.3125rem
      /* 25/80 */
    ;
  }
</style>