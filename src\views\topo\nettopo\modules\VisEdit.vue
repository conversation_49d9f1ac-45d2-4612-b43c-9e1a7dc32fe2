<template>
  <div class="wrap">
    <div class="content">
      <!--左侧工具栏-->
      <dep-node-list ref='depNodeTree' v-if="operate === 'create' && topoInfo.topoType==='2'" @dragNode="dragNode"></dep-node-list>
      <node-list ref='nodeTree' v-else-if="operate === 'create' && topoInfo.topoType" @dragNode="dragNode"></node-list>
      <!-- 中间拓扑图面板 -->
      <div
        :class="toolbar ? 'panel' : 'panel-show'"
        :style="{ width: operate === 'create' ? 'calc(100% - 580px)' : '100%' }"
      >
        <div v-if="spinning" class="spin-block" :class="{ 'spin-block-theme': topoBgByTheme }">
          <a-spin
            style="position: absolute; width: 100%; top: 40vh; z-index: 10000"
            :tip="spinTip"
            :spinning="spinning"
          ></a-spin>
        </div>

        <!-- 拓扑图工具栏 -->
        <div class="toolbarView" v-if="toolbar && isReady">
          <div class="toolbar" v-if="toggleToolBar">
            <a-tooltip placement="bottom" v-if="operate === 'show'" >
              <template slot="title">
                <span>{{ title }}</span>
              </template>
              <div class="topoTitle">{{title}}</div>
            </a-tooltip>
            <tool-bar
              ref="toolBar"
              :topoInfo="topoInfo"
              :operate="operate"
              :showMiniMap="true"
              :toggleMiniMap="toggleMiniMap"
              @auto="autoClick"
              @importTopo="importTopo"
              @drawLine="drawLine"
              @changeMiniMap="changeMiniMap"
              style="margin-right:0"
            />
          </div>
          <div class="arrow" @click="fold"><a-icon :type="toggleToolBar?'left':'right'" /></div>
        </div>
        <!--网路拓扑显示图例-->
        <div v-if='netTopo && legendShow' class='legend' :style='{top:toolbar?"48px":"3px"}'>
          <topo-legend :topoInfo="topoInfo" :alarm-level-list="alarmLevelList" :on-off-colors="onOffColors" :line-colors="lineColors"></topo-legend>
        </div>
        <!-- 拓扑图容器 -->
        <div :id="canvasId" ref="container" class="graphcontainer" style="flex: 1; width: 100% !important"></div>
        <!-- 保存拓扑缩放和位置信息 -->
        <div class="save-fit-info" v-if='saveTopoInPage' title="保存拓扑缩放比例和位置信息" @click="saveFitInfo"><a-icon type="save"  /></div>
        <!-- 小地图导航 -->
        <div class="minimapWrap" v-show="toggleMiniMap">
          <div class="title">导航<a-icon type="close"  @click="changeMiniMap(false)"/></div>
          <div id="minimap"></div>
        </div>
        <!-- 提示弹窗组件 -->
        <topo-tip ref="topoTip" :globalGridAttr='globalGridAttr'></topo-tip>
        <zr-tool-tip
            :left='tipLeft'
            :top='tipTop'
            :width='tipWidth'
            :height='tipHeight'
            :tipData='tipData'
            :visible='tipShow'
            :tipZindex='tipZindex'
            @hide='toolTipHide'
            @cellClick='cellClick'
            @enterTipCard='enterTipCard'
            @leaveTipCard='leaveTipCard'
        ></zr-tool-tip>
        <!-- 子拓扑返回按钮 -->
        <div
          @click="goParentTopo"
          class="parent-topo-btn"
          :style="{ top: toolbar ? '60px' : '20px' }"
          v-if="parentTopos.length > 0"
        >
          <a-button type="primary" shape="circle" icon="rollback" />
        </div>
      </div>
      <!--右侧工具栏-->
      <div class="config" v-if="operate === 'create'">
        <config-panel v-if="isReady" :operate="operate" @bindDevice='bindDevice' />
      </div>
    </div>
    <!-- 设备详情 -->
    <topo-device-modal ref="modalForm"></topo-device-modal>
    <!-- 虚拟设备设备类型选择弹窗-->
    <device-type-modal v-if='operate === "create"' ref="chooseType" @chooseType="setVirtualType"></device-type-modal>
    <!-- 应用拓扑图业务应用选择 -->
    <business-type-modal v-if='topoInfo.topoType==="1"&&operate === "create"' ref="businessType" @ok="appAutoCreate"></business-type-modal>
    <!-- 选择设备弹窗 -->
    <choose-node-device
      v-if='operate === "create"'
      ref="chooseNodeDevice"
      @ok="resetNodeDevice"
      @chooseVirtual="chooseVirtual"
    ></choose-node-device>
  </div>
</template>

<script>
// 样式文件，可以根据需要自定义样式或主题
import '@wchbrad/vue-easy-tree/src/assets/index.scss'
import './index.less'

import {Addon} from '@antv/x6'
const { Dnd } = Addon
import FlowGraph from './graph'
import { getAction, postAction, httpAction, uploadAction } from '@/api/manage'
import { globalGridAttr } from './models/global'
//设备信息详情
import TopoDeviceModal from './components/TopoDeviceModal'
//虚拟设备类型选择
import DeviceTypeModal from './components/DeviceTypeModal'
//业务类型弹窗
import BusinessTypeModal from './components/BusinessTypeModal'
//选择节点设备
import ChooseNodeDevice from './components/ChooseNodeDevice'
//工具栏
import ToolBar from './components/ToolBar/index.vue'
//右侧面板
import ConfigPanel from './components/ConfigPanel/index.vue'
//信息弹窗
import TopoTip from './components/tips/TopoTip.vue'
import ZrToolTip from './components/tips/ZrToolTip.vue'
//左侧节点资源
import NodeList from './components/nodeList/NodeList'
import { uuidX6,invertColor } from '@/utils/util'

// 拓扑图注册事件的方法
import cellEventMixin from './mixins/cellEventMixin'
//拓扑图创建节点
import createTopoMixin from './mixins/createTopoMixin'
// 改变节点边线状态的方法
import setCellStatus from './mixins/setCellStatus'
// 自动生成拓扑图
import autoTopoMixin from './mixins/autoTopoMixin'
// 拓扑图配置设置
import topoConfigMixin from './mixins/topoConfigMixin'
//拓扑图例
import TopoLegend from '@views/topo/nettopo/modules/components/TopoLegend.vue'
import DepNodeList from './components/nodeList/DepNodeList.vue'
const tryToJSON = (val) => {
  try {
    return JSON.parse(val)
  } catch (error) {
    return val
  }
}

export default {
  name: 'VisEdit',
  components: {
    DepNodeList,
    ToolBar,
    ConfigPanel,
    TopoDeviceModal,
    TopoTip,
    ZrToolTip,
    NodeList,
    DeviceTypeModal,
    BusinessTypeModal,
    ChooseNodeDevice,
    TopoLegend
    // StatusInfo,
  },
  props: {
    //隐藏拓扑图背景 用页面做背景
    topoBgByTheme: {
      type: Boolean,
      default: false,
    },
    // 要使用标签颜色
    labelColor: {
      type: String,
      default: '',
    },
    operate: {
      type: String,
      default: 'create',
    }, //当前页面为查看、编辑：show、create
    toolbar: {
      type: Boolean,
      default: false,
    },
    canvasId: {
      type: String,
      default: 'layoutContainer',
    },
    // 判断是否在业务概览中使用拓扑图
    isBusiness: {
      type: Boolean,
      default: false,
    },
    // 拓扑图标题
    title: {
      type: String,
      default: '',
    },
    // 需高亮的设备的配置参数
    lighterSetting: {
      type:Object,
      default: () => {},
    },
    saveTopoInPage: {
      type: Boolean,
      default: false
    },
    legendShow: {
      type: Boolean,
      default: false
    },
    blurId:{
      type: String,
      default: ''
    },
    scene:{
      type: String,
      default: ''
    }
  },
  invertColor:"",
  mixins: [cellEventMixin, createTopoMixin, setCellStatus, autoTopoMixin, topoConfigMixin],
  data() {
    return {
      type: 'grid',
      id: '',
      visible: false,
      graph: null,
      isReady: false,
      url: {
        deviceInfo: '/topo/device/info',
        deviceStatusUrl: '/topo/device/status',
        topoEdit: '/topo/topoInfo/edit',
        getTopoById: '/topo/topoInfo/queryById',
        getFocusTopo: '/topo/topoInfo/getFocusTopo', // 获取设备上下五层拓扑
        getStatus: '/topo/device/initStatus',
        deviceDetailInfo: '/device/deviceInfo/queryById',
        deviceTypeUrl: '/topo/device/selectNetType',
      },
      picSrc: window._CONFIG['staticDomainURL'],
      apiUrl: window._CONFIG['domianURL'],
      dnd: Object,
      deviceInfo: null,
      topoInfo: {},
      copyTopoInfo: null, //topo信息的复制保存，用于子拓扑返回父拓扑时使用
      cellList: [],
      alarmInfo: null,
      globalGridAttr: globalGridAttr,
      edgeConnectNode: {},
      showDevcInfo: {
        name: '',
        ip: '',
        mem: '',
        cpu: '',
      }, //气泡展示设备信息
      nodeIp: '',
      alarmNodes: [],
      cellId: '',
      cellType: 'grid',
      spinning: false,
      spinTip: 'Loading...',
      statusTop: 0,
      statusRight: 0,
      parentTopos: [],
      toggleMiniMap: false, // 切换小地图导航的显示隐藏
      toggleToolBar: true,
      saveId: '',
      topoRect:{},
      blurCell:null,
    }
  },
  created() {
  },
  mounted() {
    // let topoContent = this.$refs.container.getBoundingClientRect();
    // console.log("拓扑图区域 ==== ",topoContent)
    // console.log("屏幕",window.screen)
    // this.statusRight = window.screen.width - topoContent.left - topoContent.width;
    // this.statusTop = topoContent.top;
    // 暂不设置边线动画
  },
  watch: {
    "blurId":{
      immediate:true,
      handler(newVal){
        if(newVal && this.graph){
          if(this.blurCell){
            this.setNodeBlur(this.blurCell,0)
          }
          this.blurCell = this.graph.getCellById(newVal)
          // this.blurCell.attr('body/stroke', '#fff')
          // this.blurCell.attr('body/strokeWidth', 5)
          this.setNodeBlur(this.blurCell,20)
        }else{
          if(this.blurCell){
            this.setNodeBlur(this.blurCell,0)
            this.blurCell = null
          }
        }
      }
    },
    '$store.getters.deviceAlarms': {
      // immediate:true,
      deep: true,
      handler() {
        this.getAlarmInfoTip(this.$store.getters.deviceAlarms)
      },
    },
    '$store.getters.deviceStatus': {
      // immediate:true,
      deep: true,
      handler(e) {
        // console.log("监听到了在线离线 === ",e)
        if (this.graph) {
          this.getDeviceStatusTip()
        }
      },
    },
  },
  computed:{
    appTopo(){
      return this.topoInfo && this.topoInfo.topoType === '1'
    },
    netTopo(){
      return this.topoInfo && this.topoInfo.topoType === '0'
    }
  },
  beforeDestroy() {
    this.onClose()
  },
  methods: {
    /*
     *根据拖拽的节点信息 生成拓扑图的节点
     *type为节点类型
     *data 节点信息
     *e拖拽事件对象
     * createTopoNode 方法定义在createTopoMixin中
     */
    dragNode(e, type, data) {
      //判断画布中是否已存在该设备节点，存在则无法拖入画布中
      if (type === 'device') {
        const flag = this.graph.getNodes().some((ele) => {
          return ele.getData().deviceCode === data.code
        })
        if (flag) {
          this.toDeviceLocation(type, data)
          return
        }
      } else if (type === 'topo') {
        const flag = this.graph.getNodes().some((ele) => {
          return ele.getData().deviceId === data.id
        })
        if (flag) {
          this.toDeviceLocation(type, data)
          return
        }
      }else if(type === "dept"){
        const flag = this.graph.getNodes().some((ele) => {
          return ele.getData().departId === data.id
        })
      }
      // console.log("创建",type,data)
      let node = this.createTopoNode(type, data)
      this.dnd.start(node, e)
    },
    toDeviceLocation(type, data) {
      // 点击树设备节点定位到画布中的设备，并高亮显示
      let node;
      if (type === 'device') {
        node =  this.graph.getNodes().find((ele) => {
          return ele.getData().deviceCode == data.code
        })
      }
      if (type === 'topo') {
        node =  this.graph.getNodes().find((ele) => {
          return ele.getData().deviceId == data.id
        })
      }
      const pos = node.position() // 获取选中的节点的位置
      this.graph.resetSelection(node) // 设置当前节点为选中状态
      this.graph.centerPoint(pos.x || 0, pos.y || 0) //  使指定的点与视口中心对齐
      this.graph.zoomTo(1)
    },
    //子拓扑返回显示父拓扑
    goParentTopo() {
      if (this.parentTopos.length > 0 && this.isReady) {
        this.isReady = false
        let topoId = this.parentTopos.splice(this.parentTopos.length - 1, 1)[0]
        this.createTopo(topoId, true)
      }
    },
    //展示子拓扑图
    showSonTopo(cell) {
      //只允许查看页面实现子拓扑节点跳转
      if (this.operate === 'show' && this.isReady) {
        this.isReady = false
        this.parentTopos.push(this.topoInfo.id)
        this.createTopo(cell.getData().deviceId, true)
      }
    },
    // 设备节点告警状态
    getAlarmInfoTip(deviceAlarms) {
      // console.log('监听到了告警信息 === ', deviceAlarms)
      if (this.graph) {
        const nodes = this.graph.getNodes()
        nodes.forEach((node) => {
          let alarm = deviceAlarms.find((ele) => {
            return node.data && node.data.deviceCode == ele.deviceCode
          })
          if (alarm) {
            let level = 0
            alarm.alarmInfo.forEach((ael) => {
              if (ael.alarmLevel > level) {
                level = ael.alarmLevel
              }
            })
            // console.log("设备的告警状态 === ",level)
            this.setNodeAlarm(node, level)

          } else {
            node.setData({
              alarmNode: false,
            })
          }
        })
      }
    },
    // 根据后台推送的设备状态修改设备状态
    getDeviceStatusTip() {
      // console.log('监听到设备状态改变 === ', this.$store.getters.deviceStatus)
      let statusArr = this.$store.getters.deviceStatus
      let nodes = this.graph.getNodes()
      let edges = this.graph.getEdges()
      statusArr &&
        statusArr.forEach((ele) => {
          const node = nodes.find((val) => {
            if (!!val.data && val.data.deviceCode) {
              return val.data.deviceCode == ele.deviceCode
            }
          })
          if (!!node) {
            // up 在线 // down 离线
            let status = 0
            if (ele.status === 'up') {
              status = 1
            }
            // 告警状态下不考虑离线在线
            if (node.data.alarmNode !== true && node.data.status !== status) {
              this.setNodeOnAndOff(node, status)
              //更新对应节点连线的状态
              let edges = this.graph.getConnectedEdges(node);
              if(edges && edges.length > 0){
                edges.forEach((edge) => {
                  this.setEdgeStatus(edge)
                })
                this.setAllEdgesAni(edges)
              }
            }
          }
        })
    },
    /*
     *getTopoById 通过id获取拓扑图信息
     *id 拓扑图的id
     *totpoDataType 获取渲染拓扑图数据类型 0或不传 不返回,  1 json,  2 node/edge,  3 两种数据都返回
     */
    async getTopoById(id) {
      await getAction(this.url.getTopoById, {
        id: id,
        topoDataType: 2,
      }).then((res) => {
        if (res.success) {
          this.topoInfo = res.result
        }
      })
    },
    /*
     *getFocusTopo 通过id和deviceCode获取拓扑图信息
     *此处是用于展示指定设备上下五层拓扑图
     */
    async getFocusTopo(id, deviceCodeValue) {
      await getAction(this.url.getFocusTopo, {
        topoId: id,
        deviceCode: deviceCodeValue,
        max: 5
      }).then(res => {
        if (res.success) {
          this.topoInfo.edgeList = res.result.edgeList
          this.topoInfo.nodeList = res.result.nodeList
          this.topoInfo.topoConfig = res.result.topoConfig
        }
      })
    },
    /*
     *生成拓扑图
     *id 为拓扑图的id
     *isSwitch
     *deviceCodeValue表示当前拓扑图中需要高亮的设备的code
     *contextDeviceType为从设备信息>拓扑视图标签页跳转过来,表示该拓扑图存在关联设备,展示该设备的上下五层拓扑
     */
    async createTopo(id, isSwitch, deviceCodeValue, contextDeviceType) {
      if (!isSwitch) {
        this.parentTopos = []
      }
      this.spinTip = '正在加载...'
      this.spinning = true
     this.onClose()
      // 获取告警级别列表
      await this.getAlarmLevelData()
      if (deviceCodeValue && contextDeviceType === 'fiveTopo') {
        await this.getFocusTopo(id, deviceCodeValue)
      } else {
        await this.getTopoById(id)
      }

      // 如果已存在画布 销毁已存在的画布

      let timer = setTimeout(() => {
        clearTimeout(timer)
        timer = null
        // 初始化画布
        this.initGraph(this.canvasId)
        let containerBox = this.$refs.container.getBoundingClientRect()
        // 判断当前拓扑图是否有保存的节点边的序列化
        if (this.topoInfo.topoDataJson != null && this.topoInfo.topoDataJson != '') {
          let topoObj = JSON.parse(this.topoInfo.topoDataJson)
          console.log('item === ', topoObj)
          let xlink = 'xlink:href'
          topoObj.cells.forEach((item, i) => {
            if (item.attrs && item.attrs.image && item.attrs.image[xlink]) {
              item.attrs.image[xlink] = item.attrs.image[xlink].includes('http')
                ? item.attrs.image[xlink]
                : this.apiUrl + '/' + item.attrs.image[xlink]
            }
          })
          this.graph.fromJSON(topoObj)
        } else if (this.topoInfo.nodeList && this.topoInfo.edgeList) {
          this.dataTransferCell(this.topoInfo.nodeList, this.topoInfo.edgeList)
        }
        // 初始化拓扑图的配置
        this.initConfig()
       //初始化拓扑渲染区域
        this.resizeTopo()
        // 初始化设备状态
        this.initDeviceStatus()
        this.setup()
        if (deviceCodeValue) {
          this.lighter(deviceCodeValue)
        }
        if(this.blurCell){
          this.setNodeBlur(this.blurCell,20)
        }
        this.spinning = false
        this.getExceptionNodes()
      }, 200)
    },
    //导入拓扑图
    importTopo() {
      let topoObj = this.$refs.toolBar.ImportJSON
      let edges = topoObj.cells.filter((el) => el.shape === 'edge')
      let xlink = 'xlink:href'
      // let nodes = topoObj.cells.filter(el=>el.shape !== "edge");
      topoObj.cells.map((el) => {
        let newId = uuidX6()
        if (el.shape !== 'edge') {
          edges.forEach((ed) => {
            if (ed.source.cell === el.id) {
              ed.source.cell = newId
            }
            if (ed.target.cell === el.id) {
              ed.target.cell = newId
            }
          })
        }
        el.id = newId
        if (el.attrs.image) {
          el.attrs.image[xlink] = el.attrs.image[xlink].includes('http')
            ? el.attrs.image[xlink]
            : this.apiUrl + '/' + el.attrs.image[xlink]
        }
        return el
      })
      console.log('导入的家json', topoObj)

      this.graph.fromJSON(topoObj)
      this.initDeviceStatus()
      // this.initConfig()
      this.graph.centerContent()
    },
    // 初始化设备状态
    initDeviceStatus() {
      // 清空vuex的设备在线离线数据
      this.$store.commit('CLEAR_STATUS_LIST')
      // 清空vuex的设备告警状态
      this.$store.commit('CLEAR_ALARM_LIST')
      let nodes = this.graph.getNodes().filter((el) => el.data.nodeType === 'device' || el.data.nodeType === 'dept')
      let edges = this.graph.getEdges()
      // 设定边线的层级
      edges.forEach((edge) => {
        edge.zIndex = 3
      })
      let codes = []
      nodes.forEach((e) => {
       /* if (e.data.isVirtual) {
          e.attr('body/fill', this.onOffColors.onLine)
          e.attr('body/opacity', 1)
        }*/
        if (e.data.deviceCode) {
          codes.push(e.data.deviceCode)
        }else{
          //初始化状态，没有设备的节点默认时在线状态
          this.setNodeOnAndOff(e, 1)
        }
      })
      //没有绑定设备的节点时
      if (codes.length <= 0) {
        //设置连线状态和动画
        edges.forEach((edge) => {
          this.setEdgeStatus(edge)
        })
        let edgeStatustimer = setTimeout(()=>{
          this.setAllEdgesAni(edges)
          clearTimeout(edgeStatustimer)
          edgeStatustimer = null;
        },500)
        return
      }
      // 获取设备状态
      postAction(this.url.getStatus, {
        deviceCode: codes.join(','),
      }).then((res) => {
        // console.log('获取设备的初始状态信息 === ', res.result)
        if (res.success) {
          // 设置节点的状态
          nodes.forEach((el) => {
            let stdata = res.result.find((st) => st.deviceCode === el.data.deviceCode)
            if (stdata) {
              this.setNodeOnAndOff(el, stdata.status)
            } else {
              this.setNodeOnAndOff(el, 1)
            }
          })
          let alarmInfo = []
          res.result.forEach((e, inx) => {
            // 判断是否有告警告警信息
            let tem = {}
            if (e.alarmInfo && e.alarmInfo.length > 0) {
              alarmInfo.push(e)
            }
          })
          //初始化设备的告警状态
          alarmInfo.forEach((el, idx) => {
            const flag = this.$store.getters.alarmInfo.some((ele) => {
              return ele.deviceCode === el.deviceCode
            })
            if (!flag) {
              this.$store.dispatch('addAlarmList', el)
            }
          })
          // this.setStatusAni()
          // 设置边线的状态
          edges.forEach(el=>{
            this.setEdgeStatus(el)
          })
          let edgeStatustimer = setTimeout(()=>{
            this.setAllEdgesAni(edges)
            clearTimeout(edgeStatustimer)
            edgeStatustimer = null;
          },500)
        }
      })
    },
    // 清除定时器
    clearStatusInterval() {
      if (this.statusInterval) {
        clearInterval(this.statusInterval)
        this.statusInterval = null
      }
    },
    onClose() {
      this.clearStatusInterval()
      if (this.graph && this.graph.dispose) {
        this.clearNodesAni();
        let edges = this.graph.getEdges()
        edges.forEach(el => {
          this.clearEdgeAni(el)
        })
        this.graph.clearCells()
        this.graph.dispose()
        this.graph = null
      }
    },
    /*
     *请求设备状态时是否需要判断内外网设备类型
     * needChecked 是否需要判断
     *window._CONFIG.customization 根据不同的项目设置请求IP 查看config配置文件
     *  */
    async getInternetType(idArr, needChecked) {
      if (
        needChecked &&
        window._CONFIG.customization &&
        window._CONFIG.customization.cz_zunyi &&
        this.url.deviceTypeUrl
      ) {
        let pArr = []
        let urlArr = []
        idArr.forEach((el) => {
          pArr.push(
            getAction(this.url.deviceTypeUrl, {
              deviceCode: el,
            })
          )
        })
        return Promise.all(pArr).then((data) => {
          data.forEach((res, idx) => {
            let url = this.url.deviceStatusUrl
            if (res.success) {
              if (res.result === window._CONFIG.customization.cz_zunyi.internetFlag) {
                url = window._CONFIG.customization.cz_zunyi.internetServiceURL + url
              }
            }
            urlArr.push(
              postAction(url, {
                deviceCode: idArr[idx],
              })
            )
          })
          return Promise.all(urlArr)
        })
      } else {
        let pArr = []
        idArr.forEach((el) => {
          pArr.push(
            postAction(this.url.deviceStatusUrl, {
              deviceCode: el,
            })
          )
        })
        return Promise.all(pArr)
      }
    },
    initGraph(container) {
      this.graph = FlowGraph.init(container, this.operate === 'create')
      this.dnd = new Dnd({
        target: this.graph,
        scaled: false,
        animation: true,
        validateNode(droppingNode, options) {
          return true
        },
      })
      // 给拓扑图边线注册监听事件

      this.isReady = true
    },

    //获取设备详情
    getDeviceInfo(code) {
      // console.log('获取设备详情 === ', code)
      getAction(this.url.deviceInfo, {
        deviceCode: code,
      }).then((res) => {
        if (res.success) {
          return res.result
        }
      })
    },
    // 保存拓扑图
    save() {
      if (!/^[a-zA-Z0-9\u4e00-\u9fa5~!@#$%^&*()_+`\-={}:";'<>?,.\/]{2,20}$/.test(this.globalGridAttr.topoName)) {
        this.$message.warning('拓扑名称应为2-20位字符!')
        this.spinning = false
        return
      }
      if (this.checkTopoBeforeTopo()) {
        this.$message.warning('还有节点没有确认，无法保存！')
        this.spinning = false
        return
      }
      this.clearNodesAni();
      let snodes = this.graph.getNodes()
      let topoConfig = this.globalGridAttr.topoConfig
      Object.assign(topoConfig, { minX: '', maxX: '', minY: '', maxY: '' })
      snodes.forEach((el) => {
        let pos = el.position()
        if (topoConfig.minX === '' || pos.x < topoConfig.minX) {
          topoConfig.minX = pos.x
        }
        if (topoConfig.maxX === '' || pos.x > topoConfig.maxX) {
          topoConfig.maxX = pos.x
        }
        if (topoConfig.minY === '' || pos.y < topoConfig.minY) {
          topoConfig.minY = pos.y
        }
        if (topoConfig.maxY === '' || pos.y > topoConfig.maxY) {
          topoConfig.maxY = pos.y
        }
        if (el.data.nodeType === 'device') {
          el.setAttrs({
            image: {
              opacity: 1,
            },
            body: {
              opacity: 0,
              fill: 'none',
            },
          })
        }
      })
      let edges = this.graph.getEdges()
      edges.forEach((edge) => {
        if (edge.data.edgeType === 'connecting') {
          this.clearEdgeAni(edge)
          edge.attr('line/stroke', '#e5e5e5')
        }
      })
      let topoJson = this.graph.toJSON()
      let xlink = 'xlink:href'
      topoJson.cells.forEach((item, i) => {
        // console.log('immages ==== ', item)
        if (item.attrs && item.attrs.image && item.attrs.image[xlink]) {
         /* let images = item.attrs.image[xlink].split('/')
          images.forEach((ele, index) => {
            if (images[index] == 'sys') {
              images.splice(0, index)
            } else {
              return
            }
          })
          item.attrs.image[xlink] = images.join('/')*/
          const regx = /(http(s?)(:)\/\/((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)|localhost)(:\d{1,5})?)(\/insight-api)?\//
          let newUrl = item.attrs.image[xlink].replace(regx, "")
          item.attrs.image[xlink] = newUrl
        }
      })
      let container = this.$refs.container.clientWidth
      topoJson.screenSize = container
      //记录中心点位和放大级别;
      let area=this.graph.getArea();
      let center = this.graph.getContentArea().getCenter()
      let zoom = this.graph.zoom()
      let translate = this.graph.translate()
      let topoContent = this.$refs.container.getBoundingClientRect()
      let fitInfo = {area,center,zoom,translate,topoContent}
      topoConfig.fitInfo = fitInfo;
      //生成后台保存的节点数据
      let nodeAndEdge = this.cellTransferData(topoJson.cells)
      Object.assign(this.topoInfo, nodeAndEdge)
      //保存拓扑图 同步配置信息
      this.saveTopoConfig()
      this.topoInfo.topoDataJson = JSON.stringify(topoJson)
      // console.log('生成的保存json === ', this.topoInfo)
      //  拓扑图背景列表保存
      // let htmlstr = document.getElementById(this.canvasId).innerHTML;
      // this.topoInfo.topoSvg = htmlstr
      this.graph.toSVG((dataUri) => {
        this.topoInfo.topoSvg = dataUri
      })

      // this.graph.toPNG((dataUri) => {
      //
      //   this.topoInfo.topoImg = dataUri
      //   console.log('保存的html === ', this.topoInfo.topoImg)
      // })
      httpAction(this.url.topoEdit, this.topoInfo, 'put')
        .then((res) => {
          this.spinning = false
          if (res.success) {
            this.$message.success(res.message)
            this.$emit('ok')
          } else {
            this.$message.warning(res.message)
          }
        })
        .finally(() => {
          this.spinning = false
        })
    },
    // 切换小地图显示隐藏
    changeMiniMap(value) {
      this.toggleMiniMap = value
    },
    fold(){
      // 折叠展开toolbar
      this.toggleToolBar = !this.toggleToolBar
    },
    // 如果有关联设备, 高亮显示设备
    lighter(deviceCodeValue) {
      let nodes = this.graph.getNodes()
      nodes.forEach(el => {
        if (el.data.deviceCode == deviceCodeValue) {
          if (this.lighterSetting.isCenter) {
            // 是否让当前选中的节点与视口中心对齐
            const pos = el.position() // 获取选中的节点的位置
            this.graph.centerPoint(pos.x || 0, pos.y || 0)
          }
          if (this.lighterSetting.isZoom) {
            // 是否设置画布的缩放级别
            this. graph.zoomTo(1)
          }
          el.addTools([
            {
              // 高亮当前节点
              name: 'boundary',
              args: {
                padding: 15,
                attrs: {
                  fill: this.lighterSetting.fillColor || '#7c68fc',
                  stroke: '#333',
                  strokeWidth: 0.5,
                  fillOpacity: this.lighterSetting.fillOpacity || 0.2,
                }
              }
            }
          ])
        }
      })
    },
    resizeTopo() {
      if(!this.graph)return ;
      this.topoRect = this.$refs.container.getBoundingClientRect()
      let cg = this.topoInfo.topoConfig?JSON.parse(this.topoInfo.topoConfig):"";
      let fitInfo = this.$ls.get(this.saveId);
      // console.log("获取到拓扑信息 === ",this.saveTopoInPage,cg,fitInfo)
      if (this.saveTopoInPage && fitInfo) {
        let area = fitInfo.area;
        let zoom = fitInfo.zoom;
        let x=area.x+area.width/2;
        let y=area.y+area.height/2;
        this.graph.zoomTo(zoom);
        this.graph.transform.centerPoint(x,y);
      }
      else if(cg && !cg.zoomFit  && cg.fitInfo){
        fitInfo = cg.fitInfo
        let area = fitInfo.area;
        let zoom = fitInfo.zoom;
        let x=area.x+area.width/2;
        let y=area.y+area.height/2;
        this.graph.zoomTo(zoom);
        this.graph.transform.centerPoint(x,y);
      }
      else {
        // 缩放画布内容，使画布内容充满视口
        // let rect = this.$refs.container.getBoundingClientRect()
        // let topoConfig = this.globalGridAttr.topoConfig
        // let topoWidth = topoConfig.maxX - topoConfig.minX + 50
        // let topoHeight = topoConfig.maxY - topoConfig.minY + 50
        // if (rect.width > topoWidth && rect.height > topoHeight) {
        //   this.graph.centerContent()
        // } else {
        //   this.graph.zoomToFit({ padding: 50 })
        // }
        let padding = cg?.fitPadding || 0
        // console.log("cg.fitPadding",cg.fitPadding,padding)
        this.graph.zoomToFit({ padding: padding })
      }
    },
    //节点绑定设备后
    bindDevice(node){
      this.getInternetType([node.data.deviceCode]).then(res=>{
        // console.log("获取到的设备绑定信息 === ",res)
        if(res[0] && res[0].success && res[0].result){
          this.setNodeOnAndOff(node,res[0].result.status)
        }else{
          this.setNodeOnAndOff(node,0)
        }

      }).catch(err=>{
        this.setNodeOnAndOff(node,0)
      }).finally(()=>{
        let connectedEdges = this.graph.getConnectedEdges(node)
        connectedEdges.forEach(el=>{
          this.setEdgeStatus(el)
        })
      })
    },
    //节点状态切换
    getExceptionNodes(){
      if((this.netTopo || this.appTopo) && this.graph){
        let nodes = this.graph.getNodes().filter(el=>el.data.status==0 || el.data.alarmNode).map(el=>{
          if(el.data.status == 0){
            return {
              name:el.attrs.label.text,
              statusText:"故障",
              status:2,
            }
          }else{
            return {
              name:el.attrs.label.text,
              statusText:"不稳定",
              status:1,
            }
          }
        }).sort((a,b)=>{
          return b.status - a.status
        })
        this.$emit("emitExceptionNodes",nodes)
      }
    }
  },
}
</script>
<style>

.parent-topo-btn {
  position: absolute;
  top: 20px;
  right: 20px;
}
.my-scroller::-webkit-scrollbar {
  display: none;
}
@keyframes yq-topo-dash-line {
  to {
    stroke-dashoffset: -1000
  }
}
</style>
<style scoped lang="less">
::v-deep .el-tree {
  max-height: calc(100% - 190px);
  overflow-y: auto;
  border: 1px solid rgba(0, 0, 0, 0.08);
  margin: 5px 0;
}
.toolbarView{
  display: flex;
  align-items: center;
  position: absolute;
  top: 3px;
  left: 3px;
  z-index: 999;
  background-color: #fff;
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 6px;
  .arrow {
    width:38px;
    height:38px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
}
.legend{
  position: absolute;
  left: 3px;
  top:54px;
  z-index: 999;
}
.panel {
  height: 100%;
  display: flex;
  position: relative;
  flex-direction: column;
  .toolbar {
    display: flex;
    align-items: center;
    height: 38px;
    background-color: #fff !important;
    border: none !important;
    .topoTitle {
      max-width: 200px;
      color: #000;
      font-size: 16px;
      font-weight: 600;
      padding: 0 5px 0 20px;
      /**单行超出省略号**/
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  .graphcontainer {
    height: calc(100% - 38px) !important;
    width: 100% !important;
    flex: 1 !important;
  }
}
.panel-show {
  width: 100%;
  height: 100%;
  display: flex;
  position: relative;
  flex-direction: column;
  .graphcontainer {
    height: 100% !important;
    width: 100% !important;
    flex: 1 !important;
  }
}

.wrap .content {
  display: flex;
  height: 100% !important;
}
.spin-block {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 10000;
  background: rgba(255, 255, 255, 0.6);
}
.spin-block-theme {
  background: rgba(0, 0, 0, 0.2);
}
::v-deep .sider {
  box-shadow: none;
  border: 1px solid rgba(0, 0, 0, 0.08) !important;
  margin-right: 10px;
}

::v-deep .x6-graph-scroller {
  width: 100% !important;
}

::v-deep .x6-graph {
  box-shadow: none !important;
}
//保存当前位置和比例
.save-fit-info{
  position: absolute;
  z-index: 10000;
  top: 14px;
  right:0;
  color:#fff;
  cursor: pointer;
  font-size:  0.5rem;
  color:rgba(255,255,255,0.4);
  &:hover{
    color:rgba(255,255,255,1);
  }
}
/* 小地图导航的样式 */
.minimapWrap {
    position: absolute;
    bottom: 0;
    right: 0;
    z-index: 10;
    background: #fdfdfd;
    transition: all .3s ease;
    border: 1px solid #999;
    .title {
      display: flex;
      align-items: center;
      padding: 0 10px;
      background: #1E3674;
      justify-content: space-between;
      color: #fff;
    }
  }
  /* 修改框选选中的样式 */
  ::v-deep .x6-widget-selection-inner {
    border: 3px dashed #409eff;
    background: rgba(224,80,10,.1);
  }
</style>