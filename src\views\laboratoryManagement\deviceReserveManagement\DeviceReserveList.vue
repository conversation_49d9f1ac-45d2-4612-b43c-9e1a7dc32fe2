<template>
  <a-row :gutter="10" style="height: 100%;" class="vScroll">
    <a-col style="width:100%;height: 100%;display: flex;flex-direction: column">
      <!-- 查询区域 -->
      <a-card
        :bordered="false"
        :bodyStyle="{ paddingBottom: '0', marginRight: '12px' }"
        class="card-style"
        style="width: 100%"
      >
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="设备名称">
                  <a-input
                    :maxLength='maxLength'
                    placeholder="请输入设备名称"
                    v-model="queryParam.deviceName"
                    :allowClear="true"
                    autocomplete="off"
                  />
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label='设备类型'>
                  <a-select placeholder="请选择设备类型" :getPopupContainer="(node) => node.parentNode" :allowClear="true"
                            v-model="queryParam.deviceType">
                    <a-select-option v-for="item in deviceTypeList" :key="item.id" :value="item.id">{{
                        item.name
                      }}</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label='设备状态'>
                  <a-select placeholder="请选择设备状态" :getPopupContainer="(node) => node.parentNode" :allowClear="true"
                            v-model="queryParam.deviceStatus">
                    <a-select-option v-for="item in statusList" :key="item.value" :value="item.value">{{
                        item.title
                      }}</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="colBtnsSpan()">
                <span
                  class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                >
                  <a-button
                    type="primary"
                    class="btn-search btn-search-style"
                    @click="searchQuery"
                  >查询</a-button>
                  <a-button class="btn-reset btn-reset-style" @click="searchReset">重置</a-button>
                  <a v-if="isVisible" class="btn-updown-style" @click="doToggleSearch">
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered="false" style="width: 100%; flex: auto">
        <a-table
          ref="table"
          bordered
          :row-key="(record,index)=>{return record.id}"
          :columns="columns"
          :dataSource="dataSource"
          :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
          :pagination="ipagination"
          :loading="loading"
          @change="handleTableChange"
        >
          <template slot="tooltip" slot-scope="text">
            <a-tooltip placement="topLeft" :title="text" trigger="hover">
              <div class="tooltip">{{ text }}</div>
            </a-tooltip>
          </template>
          <template slot="deviceType" slot-scope="text">
            {{ deviceTypeList[text].name }}
          </template>
          <span slot="action" class="caozuo" slot-scope="text, record">
            <a @click="handleDetailPage(record)">查看</a>
            <a-divider v-if="record.deviceStatus==0" type='vertical'/>
            <a v-if="record.deviceStatus==0" @click='handleReserve(record)' class='overlay'>预约</a>
          </span>
        </a-table>
      </a-card>
    </a-col>
    <per-modal ref='modalForm' @ok='modalFormOk'></per-modal>
    <!-- <knowledge-approval-modal ref='modalForm' @ok='modalFormOk'></knowledge-approval-modal> -->
  </a-row>
</template>
<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import PerModal from '@views/laboratoryManagement/modules/PerModal.vue'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
import {listData, statusData, deviceTypeData}from './deviceReserveData'
import {deviceTypes}from '../modules/projects'
export default {
  name: 'DeviceReserveList',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components: {
    PerModal
  },
  data() {
    return {
      maxLength:50,
      description: '设备预约管理列表页面',
      disableMixinCreated:true,
      formItemLayout: {
        labelCol: {
          style: 'width:70px'
        },
        wrapperCol: {
          style: 'width:calc(100% - 70px)'
        }
      },
      // 表头
      columns: [
        {
          title: '设备名称',
          dataIndex: 'deviceName',
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'tooltip'
          }
        },
        {
          title: '设备类型',
          dataIndex: 'deviceType',
          scopedSlots: {
            customRender: 'deviceType'
          }
        },
        {
          title: '设备状态',
          dataIndex: 'deviceStatus',
          customRender: (text, record, index) => {
            if (record.deviceStatus==0) {
              return "空闲"
            } else {
              return '占用'
            }
          },
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 100px;max-width:300px'
            return { style: cellStyle }
          }
        },
        {
          title: '已预约总次数',
          dataIndex: 'appointmentsTotalNumber',
          customCell: () => {
            let cellStyle = 'width:80px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '已预约总时长(天)',
          dataIndex: 'totalReservedDuration'
        },
        {
          title: '当前使用人',
          dataIndex: 'currentUser',
          customRender: (text, record, index) => {
            if (record.deviceStatus==0) {
              return "-"
            } else {
              return text
            }
          },
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 100px;max-width:300px'
            return { style: cellStyle }
          }
        },
        {
          title: '当前预约完成时间',
          dataIndex: 'reserveEndTime',
          customCell: () => {
            let cellStyle = 'center;width: 160px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 140,
          scopedSlots: {
            customRender: 'action'
          }
        }
      ],
      url: {
        // list: '/kbase/knowledges/list/review'
      },
      statusList:statusData,
      deviceTypeList: deviceTypes
    }
  },
  mounted() {
    this.dataSource=listData
  },
  methods: {
    handleReserve(record){
      // 预约
      this.$refs.modalForm.edit(record);
    }
  }
}
</script>

<style scoped lang="less">
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
</style>