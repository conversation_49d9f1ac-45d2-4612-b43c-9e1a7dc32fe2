<template>
  <a-form-item label="布尔值" :labelCol="labelCol" :wrapperCol="wrapperCol">
      <a-row>
          <a-col :span="11">
                <a-input value="true" disabled></a-input>
          </a-col>
           <a-col :span="2" style="text-align:center;">
               ~
           </a-col>
          <a-col :span="11">
                <a-input value="是"></a-input>
          </a-col>

          <a-col :span="11">
                <a-input value="false" disabled></a-input>
          </a-col>
           <a-col :span="2" style="text-align:center;">
               ~
           </a-col>
          <a-col :span="11">
                <a-input value="否"></a-input>
          </a-col>
      </a-row>
  </a-form-item>

</template>

<script>
  export default {
    name:'boolMetadata',
    props:['typeFormatValue'],
    data() {
      return {
        value: {},
        labelCol: {
          xs: { span: 5 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        },
        formatValue:[]
      }
    },
    methods: {
      onChange(value) {
        this.value = value;
        this.$emit('changeFormatValue', JSON.stringify(value));
      }
    },
    mounted(){
      if(this.typeFormatValue){
        this.formatValue = JSON.parse(this.typeFormatValue);
      }
    }
  }
</script>

<style>
</style>
