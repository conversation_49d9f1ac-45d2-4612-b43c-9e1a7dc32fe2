<template>
  <a-spin :spinning='confirmLoading'>
    <j-form-container>
      <a-form-model ref='form' slot='detail' :model='model' :rules='validatorRules' v-bind='formItemLayout'>
        <a-row>
          <a-col :span='23'>
            <a-form-model-item label='通知配置名称' prop='noticeConfigId'>
              <a-select v-model='model.noticeConfigId' :allowClear='true' :getPopupContainer='(node) => node.parentNode'
                placeholder='请选择通知配置名称' @change='ChangeNoticeConfigType'>
                <a-select-option v-for='(item, index) in NoticeConfig' :key='item.id' :value='item.id'>
                  {{ item.configName }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span='23'>
            <a-form-model-item class='two-words' label='模板名称' prop='templateName'>
              <a-input v-model='model.templateName' :allowClear='true' autocomplete='off' placeholder='请输入模板名称' />
            </a-form-model-item>
          </a-col>

          <a-col :span='23'>
            <a-form-model-item label='关联业务' prop='business'>
              <a-select v-model='model.business' placeholder='请选择关联业务' mode='multiple' :maxTagCount='1'
                :getPopupContainer='(node) => node.parentNode'>
                <a-select-option v-for="(item1, key) in dictOptions" :key="item1.value" :value="item1.value">
                  {{ item1.title }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <component :is='pageName' ref='perComponent' :data='curDataObject' :users-list='usersList'
            @changeModelValue='changeModelValue' />
          <a-col :span='23'>
            <a-form-model-item label='模板文本类型' prop='textType'>
              <a-radio-group v-model="model.textType">
                <a-radio value="richText">
                  富文本
                </a-radio>
                <a-radio value="markdown">
                  Markdown
                </a-radio>
              </a-radio-group>
              <a-tooltip placement='top' title='钉钉发送和企业微信发送支持Markdown格式的文本'>
                <a-icon style='font-size: 20px; line-height: 40px; margin-left: 10px' theme='twoTone'
                  type='question-circle' />
              </a-tooltip>
            </a-form-model-item>
          </a-col>
          <a-col :span='23'>
            <a-form-model-item label='模板内容' prop='content'>
              <v-md-editor v-if="['markdown'].includes(model.textType)" v-model="model.content" height="300px"
              right-toolbar="preview" mode="edit" :left-toolbar="leftToolBar" />
              <j-editor v-else v-model='model.content' />
            </a-form-model-item>
          </a-col>
          <a-col :span='1'>
            <a-popover title='参数说明'>
              <template slot='content'>
                <p>使用${abc}的格式做为模板参数</p>
                <p><a href='http://sdyqdev.bus.yuanqiao.tech:12312/confluence/pages/viewpage.action?pageId=21299241'
                    target='_blank'>参考文档</a></p>
              </template>
              <a-icon style='font-size: 20px; line-height: 40px' theme='twoTone' type='question-circle' />
            </a-popover>
          </a-col>
          <a-col v-if='showFlowSubmitButton' :span='23' style='text-align: center'>
            <a-button @click='submitForm'>提 交</a-button>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>
import { setImgAllPath, setImgRelativePath } from '@/utils/imagePathAboutTinymce'
import { getAction, httpAction } from '@/api/manage'
import { ajaxGetDictItems, getDictItemsFromCache } from '@/api/api'
import dingDingRobot from './dingDingRobot.vue'
import weiXin from './weiXin.vue'
import email from './email.vue'
import messageTelecom from './messageTelecom.vue'
import http from './http.vue'
import message from './message.vue'
import stationNotice from './stationNotice.vue'
import eBus from './eBus.vue'
//markdown编辑器 的依赖引入
import VMdEditor from '@kangc/v-md-editor'
import '@kangc/v-md-editor/lib/style/base-editor.css'
import githubTheme from '@kangc/v-md-editor/lib/theme/github.js'
import '@kangc/v-md-editor/lib/theme/style/github.css'
import hljs from 'highlight.js'
VMdEditor.use(githubTheme, { Hljs: hljs })
export default {
  name: 'SysNoticeTemplateForm',
  components: {
    dingDingRobot,
    weiXin,
    email,
    message,
    stationNotice,
    http,
    messageTelecom,
    eBus,
    VMdEditor,
  },
  props: {
    // content: {
    //   type: String,
    //   default: () => '',
    //   required: true,
    // },

    //流程表单data
    formData: {
      type: Object,
      default: () => { },
      required: false
    },
    //表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false
    },
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  data() {
    return {
      confirmLoading: false,
      pageName: '',
      dictOptions: [],
      formItemLayout: {
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          }
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 18
          }
        }
      },
      curDataObject: {},
      personalizedDataObject: {
        /*        'dingding_robot':{
                  subject:''
                },
                'weixin': {
                  subject: '',
                  sendTo: undefined,
                  weixinExtendsList: []
                },
                'email':{
                  files:[],
                  subject:'',
                  sendTo:undefined
                },
                'message': {
                  subject: '',
                  signName: '',
                  sendTo: undefined
                },
                'stationNotice':{
                  sendTo:undefined,
                  topic:undefined,
                  prompt:false,
                  duration:5,
                  position:'bottomRight'}*/
      },
      model: {
        noticeConfigId: undefined,
        noticeConfigType: '',
        templateName: '',
        content: '',
        business: undefined,
        textType: 'richText', // 富文本还是markdown
      },
      validatorRules: {
        noticeConfigId: [{
          required: true,
          message: '请选择通知配置名称!'
        }],
        templateName: [{
          required: true,
          message: '请输入模板名称!'
        },
        {
          min: 2,
          max: 20,
          message: '模板名称长度应在 2-20 之间！'
        }
        ],
        content: [{
          required: false,
          message: '请输入模板内容!'
        }]
      },
      url: {
        add: '/sys/notice/template/add',
        edit: '/sys/notice/template/edit',
        queryById: '/sys/notice/template/queryById',
        findAll: '/sys/notice/config/findAll',
        list: '/sysNoticeExtend/sysNoticeExtend/findUserExtend',

        alarm: '/sys/notice/template/alarm', // 第一层
        level: '/sys/notice/template/level', // 第2层
        assets: '/sys/notice/template/assets', // 第3层
        productList: '/sys/notice/template/productList', // 第4层  参数：assetsId
        devList: '/sys/notice/template/devList', // 第5层 参数 productId
      },
      NoticeConfig: [],
      usersList: [],
      options: [],
      hierarchy: '',
      leftToolBar: "undo redo clear | h bold italic strikethrough quote | ul ol table hr | link image code",
    }
  },
  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    }
  },
  created() {
    this.getUserList()


    //如果是流程中表单，则需要加载流程表单data
    //this.showFlowData()
    //this.getAlarm()
  },
  watch: {
    options() { }
  },
  mounted() {
    // this.wangEditorDetail = 'wangEditorDetail默认值'
  },
  methods: {
    initDictData(dictCode) {
      return new Promise((resolve, reject) => {
        //根据字典Code, 初始化字典数组
        ajaxGetDictItems(dictCode, null).then((res) => {
          if (res.success) {
            resolve({
              success: true,
              message: '操作成功',
              data: res.result
            })
          } else {
            reject({
              success: false,
              message: res.message,
              data: []
            })
          }
        }).catch((err) => {
          reject({
            success: false,
            message: err.message,
            data: []
          })
        })
      })
    },
    getUserList() {
      getAction(this.url.list).then((res) => {
        this.usersList = res.result
      })
    },
    getNoticeConfig() {
      let that = this
      return new Promise(function (resolve, reject) {
        getAction(that.url.findAll).then((res) => {
          if (res.success) {
            resolve({
              success: true,
              message: '操作成功',
              data: res.result
            })
          } else {
            reject({
              success: false,
              message: res.message,
              data: []
            })
          }
        }).catch((res) => {
          reject({
            success: false,
            message: res.message,
            data: []
          })
        })
      })
    },

    add() {
      this.edit({})
    },
    edit(record) {
      Promise.all([this.getNoticeConfig(), this.initDictData('notice_business')]).then((result) => {
        let proResult = result.every((item, index) => {
          if (index == 0) {
            this.NoticeConfig = item.data
          } else if (index == 1) {
            this.dictOptions = item.data
          }
          return item.success == true
        })
        if (proResult) {
          if (record.id != null && record.id != '' && record.id != undefined) {
            getAction(this.url.queryById, {
              id: record.id
            }).then((res) => {
              if (res.success) {
                this.model.id = res.result.id
                this.model.noticeConfigId = res.result.noticeConfigId
                this.model.noticeConfigType = this.matchNoticeConfigType(res.result.noticeConfigId)
                this.model.templateName = res.result.templateName

                this.model.business = res.result.business && res.result.business.length > 0 ? JSON.parse(res
                  .result.business) : undefined
                this.curDataObject = {}
                this.pageName = this.getPageName(this.model.noticeConfigType)
                this.$nextTick(() => {
                  this.changeModelData(this.model.noticeConfigType)
                  let personalizedData = JSON.parse(res.result.template)
                  this.model.content = setImgAllPath(personalizedData.content)
                  this.$set(this.model,"textType", personalizedData.textType || 'richText')
                  // this.$nextTick(()=>{
                  this.setPersonalizedData(this.model.noticeConfigType, personalizedData)
                  // })
                })
              }
            })
          }
        }
      }).catch((err) => {
        this.$message.warning(err.message)
      })
    },
    ChangeNoticeConfigType(e) {
      this.$refs.form.clearValidate()
      this.model.noticeConfigType = this.matchNoticeConfigType(e)
      this.curDataObject = {}
      this.pageName = this.getPageName(this.model.noticeConfigType)
      this.$nextTick(() => {
        this.changeModelData(this.model.noticeConfigType)
      })
    },
    matchNoticeConfigType(id) {
      let m = this.NoticeConfig.filter((item, index) => {
        return item.id == id ? true : false
      })
      let type = m && m.length > 0 && m[0].id ? m[0].configType : ''
      return type
    },
    getPageName(noticeConfigType) {
      switch (noticeConfigType) {
        case 'dingding_robot':
        case 'wxWork':
          return 'dingDingRobot'
        case 'weixin':
          return 'weiXin'
        case 'email':
          return 'email'
        case 'message':
          return 'message'
        case 'stationNotice':
          return 'stationNotice'
        case 'message_telecom':
          return 'messageTelecom'
        case 'http':
          return 'http'
        case 'eBus':
          return 'eBus'
        default:
          return ''
      }
    },
    changeModelData(noticeConfigType) {
      let base = {
        noticeConfigId: this.model.noticeConfigId,
        noticeConfigType: noticeConfigType,
        templateName: this.model.templateName,
        content: this.model.content,
        business: this.model.business,
        textType: this.model.textType || 'richText'
      }
      if (this.model.id) {
        base.id = this.model.id
      }
      if (noticeConfigType) {
        this.model = Object.assign(base, this.getPersonalizedData(noticeConfigType))
      } else {
        this.model = base
        this.personalizedDataObject = {}
      }
    },
    getPersonalizedData(noticeConfigType) {
      let type = '\'' + noticeConfigType + '\''
      if (this.personalizedDataObject[type] == undefined || this.personalizedDataObject == null) {
        //this.personalizedDataObject[type] = this.$refs.perComponent[noticeConfigType]
        this.personalizedDataObject[type] = this.$refs.perComponent.personalizedObject
      }
      this.curDataObject = this.personalizedDataObject[type]
      return this.personalizedDataObject[type]
    },
    setPersonalizedData(noticeConfigType, record) {
      let type = '\'' + noticeConfigType + '\''
      for (let k in this.personalizedDataObject[type]) {
        this.personalizedDataObject[type][k] = record[k]
        if (k == 'weixinExtendsList') {
          this.personalizedDataObject[type][k] = this.$refs.perComponent.setPersonalizedData(record[k])
        }
      }
      this.curDataObject = this.personalizedDataObject[type]
      Object.assign(this.model, this.personalizedDataObject[type])
    },
    getArrData(obj, key, emptyValue = undefined) {
      if (obj[key] && obj[key].length > 0) {
        let arr = obj[key].split(',')
        obj[key] = arr
      }
      // else {
      //   obj[key]=emptyValue
      // }
    },
    changeModelValue(value) {
      let type = '\'' + this.model.noticeConfigType + '\''
      Object.assign(this.personalizedDataObject[type], value)
      Object.assign(this.model, this.personalizedDataObject[type])
    },
    submitForm() {
      const that = this
      // 触发表单验证
      that.$refs.form.validate((err, values) => {
        if (err) {
          let type = '\'' + this.model.noticeConfigType + '\''
          let obj = Object.assign({}, this.personalizedDataObject[type])
          obj.content = setImgRelativePath(that.model.content)
          if (this.model.weixinExtendsList && this.model.weixinExtendsList.length > 0) {
            let arr = []
            this.model.weixinExtendsList.map((item) => {
              arr.push(item.value)
            })
            obj.weixinExtendsList = arr
          }
          obj.textType = that.model.textType
          let bus = that.model.business
          let formData = {
            business: bus && bus.length > 0 ? JSON.stringify(bus) : '',
            noticeConfigId: that.model.noticeConfigId,
            templateName: that.model.templateName,
            template: JSON.stringify(obj)
          }
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
            formData.id = this.model.id
          }

          that.confirmLoading = true
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
              that.confirmLoading = false
            }).catch((err) => {
              that.$message.warning(err.message)
              that.confirmLoading = false
            })
        }
      })
    },
    getDataString(obj, key) {
      if (obj[key] && obj[key].length > 0) {
        let str = obj[key].join(',')
        obj[key] = str
      }
      /*else {
        obj[key]=''
      }*/
    },


    getAlarm() {
      getAction(this.url.alarm).then((res) => {
        this.options = res.result
        for (let i = 0; i < this.options.length; i++) {
          this.options[i].isLeaf = false
        }
      })
    },
    wangEditorChange(val) { },
    //渲染流程表单数据
    showFlowData() {
      if (this.formBpm === true) {
        let params = {
          id: this.formData.dataId
        }
        getAction(this.url.queryById, params).then((res) => {
          if (res.success) {
            this.edit(res.result)
          }
        })
      }
    },
    initOptions(bs) {
      if (bs && bs.type) {
        getAction(this.url.alarm).then((res) => {
          this.options = res.result
          for (let i = 0; i < this.options.length; i++) {
            this.options[i].isLeaf = false
          }
          let targetOption = this.options.find((el) => {
            return el.type === bs.type
          })
          if (targetOption) {
            this.getLevel(targetOption, bs)
          } else {
            this.isOk = true
          }
        })
      } else {
        this.isOk = true
      }
    },
    getLevel(targetOption, bs) {
      getAction(this.url.level).then((res) => {
        if (res.success) {
          targetOption.loading = false
          if (res.result != []) {
            targetOption.items = res.result
            for (let i = 0; i < targetOption.items.length; i++) {
              targetOption.items[i].isLeaf = false
            }
            this.options = [...this.options]
          }

          if (bs && bs.alarm_level) {
            let tp = targetOption.items.find((el) => {
              return el.type === bs.alarm_level
            })
            this.getAssets(tp, bs)
          } else {
            this.isOk = true
          }
        }
      })
    },
    getAssets(targetOption, bs) {
      getAction(this.url.assets).then((res) => {
        if (res.success) {
          targetOption.loading = false
          targetOption.items = res.result

          for (let i = 0; i < targetOption.items.length; i++) {
            targetOption.items[i].isLeaf = false
          }
          this.options = [...this.options]
          if (bs && bs.product_category_ids.length > 0) {
            let tp = targetOption.items.find((el) => {
              return el.type === bs.product_category_ids[0]
            })
            this.productList = bs.product_category_ids[0]
            this.getProductLis(tp, bs)
          } else {
            this.isOk = true
          }
        }
      })
    },
    getProductLis(targetOption, bs) {
      getAction(this.url.productList, {
        assetsId: this.productList
      }).then((res) => {
        if (res.success) {
          targetOption.loading = false
          if (res.result != []) {
            targetOption.items = res.result
            for (let i = 0; i < targetOption.items.length; i++) {
              targetOption.items[i].isLeaf = false
            }
            this.options = [...this.options]
          }
          if (bs && bs.product_ids.length > 0) {
            this.devList = bs.product_ids[0]
            let tp = targetOption.items.find((el) => {
              return el.type === bs.product_ids[0]
            })
            this.getDevList(tp, bs)
          } else {
            this.isOk = true
          }
        }
      })
    },
    getDevList(targetOption, bs) {
      getAction(this.url.devList, {
        productId: this.devList
      }).then((res) => {
        if (res.success) {
          targetOption.loading = false
          if (res.result != []) {
            targetOption.items = res.result
            this.options = [...this.options]
          }
          if (bs) {
            this.isOk = true
          }
        }
      })
    },
    loadData(selectedOptions) {
      const targetOption = selectedOptions[selectedOptions.length - 1]
      // const test = this.options[this.options.length - 1]
      targetOption.loading = true
      if (this.hierarchy == 1) {
        this.getLevel(targetOption)
      } else if (this.hierarchy == 2) {
        this.getAssets(targetOption)
      } else if (this.hierarchy == 3) {
        this.getProductLis(targetOption)
      } else if (this.hierarchy == 4) {
        this.getDevList(targetOption)
      }
    }
  }
}
</script>
<style lang='less' scoped>
::v-deep .two-words>div>label {
  letter-spacing: 4px;
}

::v-deep .two-words>div>label::after {
  letter-spacing: 0px;
}
</style>