import Vue from 'vue'
import { USER_INFO, ENHANCE_PRE, KNOWLEDGE_SHARE_IDS,USER_DEPART } from '@/store/mutation-types'
const getters = {
  device: state => state.app.device,
  theme: state => state.app.theme,
  color: state => state.app.color,
  token: state => state.user.token,
  avatar: state => {state.user.avatar = Vue.ls.get(USER_INFO).avatar; return state.user.avatar},
  username: state => state.user.username,
  nickname: state => {state.user.realname = Vue.ls.get(USER_INFO).realname; return state.user.realname},
  welcome: state => state.user.welcome,
  permissionList: state => state.user.permissionList,
  userInfo: state => {state.user.info = Vue.ls.get(USER_INFO); return state.user.info},
  departs: state => {state.user.departs = Vue.ls.get(USER_DEPART); return state.user.departs},
  addRouters: state => state.permission.addRouters,
  onlAuthFields: state => {return state.online.authFields },
  enhanceJs:(state) => (code) => {
    state.enhance.enhanceJs[code] = Vue.ls.get(ENHANCE_PRE+code);
    return state.enhance.enhanceJs[code]
  },
  alarmInfo:state => state.alarmInfo.dataList,
  deviceStatus:state => state.deviceStatus.dataList,
  deviceAlarms:state=>state.deviceStatus.alarmList,
  appInfo:state => state.app,
  tasktimestimp: state => state.app.tasktimestimp
}

export default getters
