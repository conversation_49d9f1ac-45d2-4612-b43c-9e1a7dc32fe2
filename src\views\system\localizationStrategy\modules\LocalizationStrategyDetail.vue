<template>
    <a-card style="height: 100%;" :bodyStyle="{ padding: 0 }">
        <a-page-header :title="info.policyName">
            <template slot="tags">
                <a-tag :color="info.isEnable == 1 ? 'green' : 'red'" style="margin-top: 3px;">
                    {{ info.isEnable == 1 ? '启用' : '未启用' }}
                </a-tag>
            </template>
            <template slot="extra">
                <span>
                    <img src="~@/assets/return1.png" alt="" @click="getGo"
                        style="width: 20px; height: 20px; cursor: pointer" />
                </span>
            </template>
            <a-descriptions  :column="3" bordered>
                <a-descriptions-item label="资源类型">
                    {{ resourceType }}
                </a-descriptions-item>
                <a-descriptions-item label="资源分类">
                    <a-tag :color="cate.tagColor" v-for="cate in cateList" :key="cate.key">
                        {{ cate.title }}
                    </a-tag>
                </a-descriptions-item>
                <a-descriptions-item label="标签">
                    <a-tag :color="tag.tagColor" v-for="tag in tags" :key="tag.id">
                        {{ tag.tagName }}
                    </a-tag>
                </a-descriptions-item>
                <a-descriptions-item label="匹配基准">
                    {{ MatchBasis }}
                </a-descriptions-item>
                <a-descriptions-item label="基准公式">
                    {{ info.policyMatchBasisFormulas }}
                </a-descriptions-item>
                <a-descriptions-item label="策略描述">
                    {{ info.policyDesc }}
                </a-descriptions-item>
                <a-descriptions-item label="匹配规则">
                   <div v-html="rules"></div>
                </a-descriptions-item>
            </a-descriptions>
        </a-page-header>
    </a-card>
</template>
<script>
export default {
    name: 'LocalizationStrategyDetail',
    props: {
        data: {
            type: Object,
            default: () => { return }
        }
    },
    data() {
        return {
            title: 'Title',
            width: '70%',
            visible: false,
            disableSubmit: false,
            info: {}
        }
    },
    created() {
        this.info = this.data.record;
    },
    computed: {
        resourceType() {
            return this.data.deviceTypes.find(item => item.value == this.info.resourceType)?.text;
        },
        cateList() {
            return this.data.flatCategoryData.filter(e => this.info.resourceCategoryIds.indexOf(e.key) != -1)
        },
        tags() {
            return this.data.tagList.filter(e => this.info.tags.indexOf(e.tagKey) != -1)
        },
        MatchBasis() {
            return this.data.logicTypes.find(item => item.value == this.info.policyMatchBasis)?.label;
        },
        rules() {
            let t = this.info.policyMatchRule
            let tem = t ? JSON.parse(t) : ""
            if (tem) {
                let temValue = Object.values(tem)
                return temValue.map((item, index) => {
                    let opertorText = this.data.ruleOperators.find(rule => rule.value == item.ruleOperator)?.text
                    let typeText = this.data.ruleTypes.find(rule => rule.value == item.ruleType)?.text
                    return `${item.ruleTag}：${typeText}信息字段 ${item.ruleKey} ${opertorText} ${item.ruleValue}`
                }).join("<br/>")
            } else {
                return ""
            }
        }
    },
    methods: {
        getGo() {
            this.$parent.pButton2(0)
        },
    }
}
</script>