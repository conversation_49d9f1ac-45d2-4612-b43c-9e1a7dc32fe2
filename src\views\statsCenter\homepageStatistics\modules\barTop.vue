<template>
  <div ref="handleTopHistogram"></div>
</template>

<script>
import echarts from 'echarts'
import { widthPixel } from '@views/statsCenter/com/calculatePixel'
export default {
  name: 'columnChart',
  props: {
    unit: {
      type: String,
      default: ''
    },
    tipDescription:  {
      type: String,
      default: ''
    },
    chartData: {
      type: Array,
      required:true
    }
  },
  watch: {
    chartData: {
      handler(nVal, oVal) {
        this.$nextTick(() => {
          this.handleTopHistogram(nVal)
        })
      },
      deep: true,
      immediate: true
    }
  },
  data() {
    return {
      myChart: null
    }
  },
  methods: {
    //计算图表网格偏移
    setGridLeft(strLen){
      let left=widthPixel(12)
      let offset=left*(strLen+2)
      return offset
    },
    handleTopHistogram(data) {
      if (data.length <= 0) {
        return
      }
      let xData = data.map(item => item.name)
      let yData = data.map(item => item.value)
      this.myChart = this.$echarts.init(this.$refs.handleTopHistogram)
      //this.myChart.clear()

      let w4= widthPixel(4)
      let w5= widthPixel(5)
      let w6 = widthPixel(6)
      let w8 = widthPixel(8)
      let w10 = widthPixel(10)
      let w12 = widthPixel(12)
      let w14 = widthPixel(14)
      let w20 = widthPixel(20)
      let w22 = widthPixel(22)
      let right = this.setGridLeft((data[0].value + "").length)

      this.myChart.setOption(
        {
          grid: {
            top: w14,
            left: w20,
            bottom: '0',
            right:right,
            containLabel: true
          },
        tooltip: {
          show:true,
          trigger: 'axis',
          axisPointer: {
            type: 'none'
          },
          formatter:(params)=>{
            if(this.tipDescription){
              let dataTxt=`<span style="display: inline-block;width: ${w10+"px"};height: ${w10+"px"};background: #0281FF;border-radius: 100%;margin-right: ${w5+"px"}"></span>`
              return params[0].name+"</br>" +dataTxt +this.tipDescription+ " "+params[0].value+this.unit
            }else {
              return '{b}：{c}' + this.unit
            }
          }
        },
        xAxis: {
          splitLine: { show: false },
          axisLabel: { show: false },
          axisTick: { show: false },
          axisLine: { show: false },
          type: 'value'
        },
        yAxis: [
          {
            type: 'category',
            inverse: true, //排序
            axisLabel: {
              show: false
            },
            splitLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            },
            data: xData
          }
        ],
        series: [
          {
            type: 'bar',
            zlevel: 1,
            barWidth: 4,
            label: {
              normal: {
                show: true,
                position: 'right',
                padding:[0,w4],
                textStyle: {
                  fontSize: w12,
                  color: '#9FA5AD'
                },
                formatter: '{c}' + ' ' + this.unit
              }
            },
            labelLine: {
              show: false
            },
            data: yData,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,0,1,0,[
                    {
                      offset: 0,
                      color: '#01FFFF' // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: '#0281FF' // 100% 处的颜色
                    }
                  ],false)
              }
            }
          },
          {
            // 顶部圆点
            type: 'scatter',
            emphasis: {
              scale: false
            },
            symbolSize: w6,
            symbolOffset: [0, 0],
            itemStyle: {
              color: '#0281FF',
            },
            data: yData
          },
          {
            // 配置label标签
            z: 1,
            show: true,
            type: 'bar',
            barGap: '-100%',
            barWidth: w4,
            itemStyle: {
              borderRadius: w4,
              color: 'transparent'
            },
            label: {
              show: true,
              align: 'left',
              verticalAlign: 'bottom',
              position: 'left',
              fontSize: w12,
              color: '#fff',
              padding: [0, 0, w8, w10],
              /*formatter: function(params) {
                return "TOP"+(params.dataIndex+1)+" "+" "+xData[params.dataIndex];
              }*/
              formatter: function(params) {
                return xData[params.dataIndex]
              }
            },
            data: yData
          }
        ]
      })
      window.addEventListener('resize', this.changeResize)
    },
    changeResize(){
      if (this.myChart){
        this.myChart.resize()
      }
    }
  },
  destroyed() {
    if (this.myChart) {
      window.removeEventListener('resize',  this.changeResize)
    }
  }
}
</script>

<style scoped lang="less">
</style>