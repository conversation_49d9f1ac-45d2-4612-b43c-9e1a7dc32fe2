<template>
  <div style="height:100%">
    <keep-alive exclude='deviceReserveDetail'>
        <component style="height:100%" :is="pageName" :data="data" />
    </keep-alive>
  </div>
</template>
<script>
  import deviceReserveDetail from './DeviceReserveDetail'
  import deviceReserveList from './DeviceReserveList'
  export default {
    name: "deviceReserveManagement",
    data() {
      return {
        isActive: 0,
        data: {},
      };
    },
    components: {
      deviceReserveDetail,
      deviceReserveList
    },
    created() {
      this.pButton1(0);
    },
    //使用计算属性
    computed: {
      pageName() {
        switch (this.isActive) {
          case 0:
            return "deviceReserveList";
          default:
            return "deviceReserveDetail";
        }
      }
    },
    methods: {
      pButton1(index, item) {
        this.isActive = index;
        this.data = item;
      },
      pButton2(index, item) {
        this.isActive = index;
        this.data = item
      }
    }
  }
</script>