<template>
  <j-modal
    :title='title'
    :width='width'
    :centered='true'
    :visible='visible'
    :destroyOnClose='true'
    switchFullscreen
    cancelText='关闭'
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @ok='handleOk'
    @cancel='handleCancel'
  >
    <a-spin :spinning='confirmLoading'>
      <j-form-container :disabled='disableSubmit'>
        <a-form-model ref='form' :model='model' :rules='validatorRules' slot='detail' v-bind='formItemLayout'>
          <a-row>
            <a-col :span='24'>
              <a-form-model-item :label='fromKnowledgeBase?`分类名称`:`主题名称`' prop='topicName'>
                <a-input
                  v-model='model.topicName'
                  :allow-clear='true'
                  autocomplete='off'
                  :placeholder='fromKnowledgeBase?`请输入分类名称`:`请输入主题名称`'
                />
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
              <a-form-model-item :label='fromKnowledgeBase?`分类编码`:`主题编码`' prop='topicCode'>
                <a-input
                  v-model='model.topicCode'
                  :allow-clear='true'
                  autocomplete='off'
                  :placeholder='fromKnowledgeBase?`请输入分类编码`:`请输入主题编码`'
                />
              </a-form-model-item>
            </a-col>
            <a-col :span='24' v-if='!fromKnowledgeBase'>
              <a-form-model-item label='流程' prop='processDefinitionKeys'>
                <a-select
                  :getPopupContainer='(target) => target.parentNode'
                  mode='multiple'
                  style='width: 100%'
                  placeholder='请选择流程'
                  v-model='model.processDefinitionKeys'
                  :allowClear='true'
                >
                  <a-select-option
                    :disabled='process.disabled'
                    v-for='(process, index) in processDefinitionList'
                    :key="'process_'+index.toString()"
                    :value='process.key'
                    :label='process.name'
                  >
                    {{ process.name }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
              <a-form-model-item prop='authority'>
                <span slot='label'>
                  <span>权限控制</span>
                  <a-popover title='说明'>
                    <template slot='content'>
                      <p>部分可见时，角色、人员、部门不能全为空</p>
                    </template>
                    <a-icon style='margin-left:5px;font-size: 20px; line-height: 40px' theme='twoTone' type='question-circle' />
                  </a-popover>
                </span>
                <a-radio-group v-model='model.isPublic' default-value='1' :options='authorityOption' @change='changeAuthority' />
              </a-form-model-item>
            </a-col>
            <a-col :span='24' v-show='model.isPublic==="0"'>
              <a-form-model-item
                ref='roles'
                label='选择角色'
                prop='roles'>
                <a-select
                  style='width: 100%'
                  :getPopupContainer='(target) => target.parentNode'
                  mode='multiple'
                  placeholder='请选择用户角色'
                  v-model='model.roles'
                  :allowClear='true'
                  @blur="() => {$refs.roles.onFieldBlur()}"
                  @change='changeroles'
                >
                  <a-select-option
                    v-for='(role, roleIndex) in roleList'
                    :key="'role_'+roleIndex"
                    :value='role.roleCode'
                    :label='role.roleName'
                  >
                    {{ role.roleName }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span='24' v-show='model.isPublic==="0"'>
              <a-form-model-item
                ref='users'
                label='选择人员'
                prop='users'>
                <!--  通过部门选择用户控件 -->
                <j-select-user-by-dep v-model='model.users' :multi='true'  @blur="() => {$refs.users.onFieldBlur()}"
                                      @change='changeroles'></j-select-user-by-dep>
              </a-form-model-item>
            </a-col>
            <a-col :span='24' v-show='model.isPublic==="0"'>
              <a-form-model-item
                ref='departments'
                label='选择部门'
                prop='departments'>
                <j-select-depart v-model='model.departments' :multi='true'
                                 @blur="() => {$refs.departments.onFieldBlur()}"
                                 @change='changeroles'
                ></j-select-depart>
              </a-form-model-item>
            </a-col>
            <a-col :span='24' v-if='!fromKnowledgeBase'>
              <a-form-model-item label='序号' prop='topicSerial'>
                <a-input-number  style='width: 200px' :min='1' :max="99999999999999999999" :precision='0' :step='1'  v-model='model.topicSerial' placeholder='请输入序号' />
              </a-form-model-item>
            </a-col>
            <a-col :span='24' v-if='!fromKnowledgeBase'>
              <a-form-model-item label='父级节点' prop='parentId'>
                <a-tree-select
                  v-model='model.parentId'
                  style='width: 100%'
                  :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                  :replaceFields="{key:'id',value:'id',title:'title',children:'children'}"
                  :tree-data='topicList'
                  placeholder='请选择父节点'
                  :allow-clear='true'>
                </a-tree-select>
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
              <a-form-model-item label='描述' prop='topicDescription'>
                <a-textarea
                  v-model='model.topicDescription'
                  :auto-size='{ minRows: 2, maxRows: 5 }'
                  :allowClear='true'
                  autocomplete='off'
                  placeholder='请输入描述信息' />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </j-form-container>
    </a-spin>
  </j-modal>
</template>
<script>
import { getAction, httpAction } from '@api/manage'
import JSelectRole from '@comp/jeecgbiz/JSelectRole.vue'
import JSelectUserByDep from '@comp/jeecgbiz/JSelectUserByDep.vue'
import JSelectDepart from '@comp/jeecgbiz/JSelectDepart.vue'

import { queryall } from '@api/api'
import { ValidateOptionalFields } from '@/utils/rules'

export default {
  name: 'TopicModal',
  components: { JSelectDepart, JSelectUserByDep, JSelectRole },
  props: {
    // 是否来自知识库添加，用于控制字段显示
    fromKnowledgeBase: {
      type: Boolean,
      default: false
    },
    // 知识库的父节点ID
    knowledgeParentId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      title: '新增',
      width: '800px',
      disableSubmit: false,
      visible: false,
      confirmLoading: false,
      formItemLayout: {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 24 },
          md: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 24 },
          md: { span: 16 }
        }
      },
      authorityOption: [
        { label: '公开', value: '1' },
        { label: '部分可见', value: '0' }],
      model: {
        parentId: undefined,
        isPublic: '1',
        roles:[],
        users: '',
        departments: '',
        topicSerial:1,
        topicName: '',
        topicCode:'',
        processDefinitionKeys:[],
        topicDescription:'',
      },
      topicList: [],
      processDefinitionList: [],
      roleList: [],
      roles: [],
      validatorRules: {
        topicName: [
          { required: true, min: 2, max: 20, validator: (rule, value, callback)=>this.validateTopicNameAndCode(rule, value, callback,'主题名称'), trigger: 'blur' }],
        topicCode: [
          { required: true, min: 2, max: 50,  validator: (rule, value, callback)=>this.validateTopicNameAndCode(rule, value, callback,'主题编码'), trigger: 'blur' }],
        roles: [
          { required: false, validator: this.customValidate, trigger: 'change' }
        ],
        users: [
          { required: false,  validator: this.customValidate, trigger: 'change' }
        ],
        departments: [
          { required: false,  validator: this.customValidate, trigger: 'change'}
        ],
        topicSerial: [
          { required: false,  message: '请输入序号', trigger: 'change'}
        ],
        topicDescription: [
          { required: false,validator: (rule, value, callback) =>ValidateOptionalFields(rule, value, callback,'描述',200) }
        ]
      },
      url: {
        add: '/knowledges/topic/add',
        edit: '/knowledges/topic/edit',
        topicTree: '/knowledges/topic/selectTree',
        topicCodeDuplicate: '/knowledges/topic/codeIsDuplicate',
        topicNameDuplicate: '/knowledges/topic/nameIsDuplicate',
        processDefinitions: '/flowable/processDefinition/list',
        bindedProcessDefinitionKey: '/knowledges/topic/getAllBindedProcessDefinitionKey'
      }
    }
  },
  created() {
    this.initialRoleList()
    this.getFlowData()
  },
  methods: {
    changeroles(e){
      console.log('人员：',this.model.users)
      console.log('角色：',this.model.roles)
      console.log('部门：',this.model.departments)
    },
    /**
     * 设置流程下拉选择节点的disabled属性值
     * 一个流程只能绑定一个主题
     * 一个主题可绑定多个流程
     * */
    setProcessDefinitionList(bindedList, selectedKeys) {
      if (this.processDefinitionList.length > 0) {
        this.processDefinitionList = this.processDefinitionList.filter((processItem) => {
          processItem.disabled = false
          let tempBindedList = []
          //从已绑定中排除当前主题已绑定的流程，方便在当前主题中，对已选流程进行取消操作
          if (bindedList.length > 0) {
            tempBindedList = bindedList.filter((item) => {
              if (selectedKeys.includes(item)) {
                return false
              } else {
                return true
              }
            })
          }
          //非当前主题绑定的流程不可选
          for (let i = 0; i < tempBindedList.length; i++) {
            if (tempBindedList[i] === processItem.key) {
              processItem.disabled = true
            }
          }
          return true
        })
      }
    },
    /**
     * 获取所属流程下拉数据
     */
    getFlowData() {
      getAction(this.url.processDefinitions, { pageNo: 1, pageSize: 10000, latestVersion: true }).then((res) => {
        if (res.success) {
          this.processDefinitionList = res.result.records || res.result
          for (let i = 0; i < this.processDefinitionList.length; i++) {
            this.processDefinitionList[i].disabled = false
          }
        } else {
          this.$message.warning(res.message)
        }
      }).catch((err) => {
        this.$message.warning(err.message)
      })
    },
    /**
     * 获得已绑定流程的key值
     * */
    getAllBindedProcessDefinitionKey() {
      return new Promise((resolve, reject) => {
        getAction(this.url.bindedProcessDefinitionKey).then((res) => {
          if (res.success) {
            resolve({
              success: true,
              message: res.message,
              data: res.result
            })
          } else {
            reject({
              success: false,
              message: res.message
            })
          }
        }).catch((err) => {
          reject({
            success: false,
            message: err.message
          })
        })
      })
    },
    /**
     * 获取已有主题
     */
    getTopicList() {
      this.topicList = []
      return new Promise((resolve, reject) => {
        getAction(this.url.topicTree).then((res) => {
          if (res.success) {
            if (res.result && res.result.children) {
              this.topicList = res.result.children
              this.setNodeDisabled(this.topicList)
            }
            resolve({
              success: true,
              message: res.message
            })
          } else {
            reject({
              success: false,
              message: res.message
            })
          }
        }).catch((err) => {
          reject({
            success: false,
            message: err.message
          })
        })
      })
    },
    /**用户没有权限的节点不可选*/
    setNodeDisabled(data){
      for (let i = 0; i < data.length; i++) {
        let keys=Object.keys(data[i])
        data[i].disabled=keys.includes('userWithPermission') ? !data[i].userWithPermission:false
        if (data[i].children) {
          this.setNodeDisabled(data[i].children)
        }
      }
    },
    /**
     * 获取角色
     */
    initialRoleList() {
      this.roleList = []
      return new Promise((resolve, reject) => {
        queryall().then((res) => {
          if (res.success) {
            this.roleList = res.result
            resolve({
              success: true,
              message: res.message,
              data: res.result
            })
          } else {
            reject({
              success: false,
              message: res.message,
              data: []
            })
          }
        }).catch((err) => {
          reject({
            success: false,
            message: err.message,
            data: []
          })
        })
      })
    },
    /**
     * 校验主题名称和编码
     */
    validateTopicNameAndCode(rule, value, callback,displayName) {
      let { min, max,fullField} = rule
      if (!value) {
         callback(`请输入${displayName}!`)
      }
      const trimmedValue = value.trim()
      if (trimmedValue === '') {
         callback(`${displayName}不能全为空白字符`)
      }
      if (value !== trimmedValue) {
         callback(`${displayName}首尾不能包含空白字符！`)
      }
      if (min && max && (value.length > max || value.length < min)) {
         callback(`${displayName}长度应在 ${min}-${max} 个字符之间！`)
      } else if (max && value.length > max) {
         callback(`${displayName}长度不能超出 ${max} 个字符！`)
      } else if (min && value.length < min) {
         callback(`${displayName}长度不能少于 ${min} 个字符！`)
      }

      let param = {
        topicParentId:this.model.parentId || '',
        topicId:this.model.id || ''
      }
      param[fullField]=value
      let inf=fullField+'Duplicate'
      getAction( this.url[inf], param).then((res) => {
        if (res.success) {
          callback()
        } else {
          callback(res.message)
        }
      }).catch((err)=>{
        callback(err.message)
      })
    },
    /**
     * 部分可见时，检验角色、人员、部门
     */
    customValidate(rule, value, callback) {
      if(this.model.isPublic==="0"){
        if ((!this.model.roles||this.model.roles.length==0)&&(!this.model.users||this.model.users==='')&&(!this.model.departments||this.model.departments==='')){
          callback('部分可见时，角色、人员、部门不能全为空')
        }else {
          callback()
        }
      }else {
        callback()
      }
    },
    add(id) {
      // 如果来自知识库，使用知识库的父节点ID
      const parentId = this.fromKnowledgeBase ? this.knowledgeParentId : id
      this.edit({
        parentId: parentId,
        isPublic: '1',
        roles:[],
        users: '',
        departments: '',
        topicSerial:1,
        topicName: '',
        topicCode:'',
        processDefinitionKeys:[],
        topicDescription:'',
      })
    },
    edit(record) {
      this.visible = true

      Promise.all([this.getAllBindedProcessDefinitionKey(), this.getTopicList()]).then((res) => {
          this.$nextTick(() => {
          Object.assign(this.model,JSON.parse(JSON.stringify(record)))
          if (this.model.parentId === 'root') {
            this.model.parentId = undefined
          }
          if (this.model.id) {
            let keys = record.processDefinitionKeys
            if (keys && keys.length > 0) {
              this.model.processDefinitionKeys = keys.split(',')
              this.setProcessDefinitionList(res[0].data, this.model.processDefinitionKeys)
            } else {
              this.model.processDefinitionKeys = []
              this.setProcessDefinitionList(res[0].data, [])
            }
            this.setTreeNodeStatus(this.model.id, this.topicList)
            if (Object.keys(this.model.authority).length > 0) {
              let aty = this.model.authority
              this.model.roles=aty.roles && aty.roles.length > 0?aty.roles.split(','):[]
              this.model.users = aty.users && aty.users.length > 0 ? aty.users : ''
              this.model.departments = aty.departments && aty.departments.length > 0 ? aty.departments : ''
            }
          } else {
            this.setProcessDefinitionList(res[0].data, [])
          }
          })
      }).catch(err=>{
        this.$message.warning(err.message)
      })
    },
    /**
     *编辑情况下，父节点不能选自己
     */
    setTreeNodeStatus(id, list) {
      for (let i = 0; i < list.length; i++) {
        let m = list[i]
        // m.disabled=!m.userWithPermission
        if (m.id === id) {
          m.disabled = true
          if (m.children && m.children.length > 0) {
            this.setChildrenNodeStatus(m.children)
          }
          return
        } else {
          if (m.children && m.children.length > 0) {
            this.setTreeNodeStatus(id, m.children)
          }
        }
      }
    },
    setChildrenNodeStatus(list) {
      for (let i = 0; i < list.length; i++) {
        list[i].disabled = true
        if (list[i].children && list[i].children.length > 0) {
          this.setChildrenNodeStatus(list[i].children)
        }
      }
    },
    close() {
      this.visible = false

    },
    handleOk() {
      let that = this
      that.$refs.form.validate((err, values) => {
        if (err && !that.confirmLoading) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!that.model.id) {
            httpurl += that.url.add
            method = 'post'
          } else {
            httpurl += that.url.edit
            method = 'put'
          }

          let formData = JSON.parse(JSON.stringify(that.model))
          let keys = this.model.processDefinitionKeys
          formData.processDefinitionKeys = keys && keys.length > 0 ? keys.join(',') : ''
          if (formData.isPublic === '0') {
            let authority = {
              users: formData.users,
              roles: formData.roles && formData.roles.length > 0 ? formData.roles.join(',') : undefined,
              departments: formData.departments
            }
            formData.authority = authority
          } else {
            formData.authority = {}
          }
          formData.parentId = this.model.parentId ? formData.parentId : 'root'
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                // 如果来自知识库，触发特定的回调
                if (that.fromKnowledgeBase) {
                  that.$emit('knowledgeBaseOk', res.result)
                } else {
                  that.$emit('ok')
                }
                that.close()
              } else {
                that.$message.warning(res.message)
              }
              that.confirmLoading = false
            }).catch((err) => {
            that.$message.warning(err.message)
            that.confirmLoading = false
          })
        }
      })
    },
    handleCancel() {
      this.model = {
        parentId: undefined,
        isPublic: '1',
        roles:[],
        users: '',
        departments: '',
        topicSerial:1,
        topicName: '',
        topicCode:'',
        processDefinitionKeys:[],
        topicDescription:'',
      }
      this.close()
    },
    changeAuthority(value) {
      if (value.target.value === '1') {
          this.model.users = ''
          this.model.roles = []
          this.model.departments = ''
      }
    }
  }
}
</script>
<style scoped lang='less'>
@import '~@assets/less/normalModal.less';

</style>