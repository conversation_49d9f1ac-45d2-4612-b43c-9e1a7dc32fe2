<template>
  <a-row :gutter='10' style='height: 100%' class='vScroll'>
    <a-col style='width: 100%; height: 100%; display: flex; flex-direction: column'>
      <!-- 计划标题 -->
      <!-- 查询区域 -->
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <div class='table-page-search-wrapper'>
          <!-- 搜索区域 -->
          <a-form layout='inline' v-bind='formItemLayout'>
            <a-row :gutter='24' ref='row'>
              <a-col :span='spanValue'>
                <a-form-item label='年份'>
                  <yq-year-picker
                    style="width: 100%;"
                    placeholder="请选择年份"
                    v-model='queryParam.year'
                    :allowClear='true'
                    autocomplete='off'>
                  </yq-year-picker>
                </a-form-item>
              </a-col>
              <!-- <a-col :span="6">
                <a-form-item label="省份">
                  <a-input placeholder="请输入省份"
                           v-model="queryParam.parentRegion"
                           :allowClear="true" />
                </a-form-item>
              </a-col> -->
              <a-col :span='spanValue'>
                <a-form-item label='地区'>
                  <a-input placeholder='请输入' :maxLength="maxLength" v-model='queryParam.region' :allowClear='true' autocomplete="off"/>
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label='单位'>
                  <a-input placeholder='请输入' :maxLength="maxLength" v-model='queryParam.company'  :allowClear='true' autocomplete="off"/>
                </a-form-item>
              </a-col>

              <a-col :span='colBtnsSpan()'>
                <span
                  class='table-page-search-submitButtons'
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                >
                  <!-- 按钮区域 -->
                  <a-button class='btn-search btn-search-style' type='primary' @click='searchQuery'>查询</a-button>
                  <a-button class='btn-reset btn-search-style' @click='mySearchReset'>重置</a-button>
                  <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
                <!-- <a-upload name="file"
                          :showUploadList="false"
                          :multiple="false"
                          :headers="tokenHeader"
                          :action="url.importExcelUrl"
                          @change="handleImportExcel">

                  <a-button type="primary"
                            icon="import"
                            >导入</a-button>
                </a-upload> -->
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>

      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <div class='table-operator table-operator-style' style='margin-bottom: 8px'>
          <a-button @click='handleAdd'>新增</a-button>
          <a-upload
            name='file'
            :showUploadList='false'
            :multiple='false'
            :headers='tokenHeader'
            :action='importExcelUrl'
            @change='handleImportExcel'
          >
            <a-button>导入</a-button>
          </a-upload>
          <a-button @click="handleExportXls('资产计划导出表')">导出</a-button>
          <!-- @click="handleExportXls" -->
          <a-button @click='handleTemplateXls()'>下载模版</a-button>
          <!-- <a-button type="primary"
                          icon="close-circle"
                          >删除</a-button> -->
          <a-dropdown v-if='selectedRowKeys.length > 0'>
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item key='1' @click='batchDel'>删除</a-menu-item>
            </a-menu>
            <a-button>
              批量操作
              <a-icon type='down' />
            </a-button>
          </a-dropdown>
        </div>

        <!-- table区域 -->
        <a-table
          :columns='columns'
          rowKey='id'
          bordered
          :pagination='ipagination'
          :dataSource='dataSource'
          :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
          :loading='loading'
          @change='handleTableChange'
          :rowSelection='{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }'
        >
          <template slot='region' slot-scope='text, record'>
            <span v-for='dict in sysAreaList' v-if='dict.id == record.region'>
              <a-tooltip placement='topLeft' :title='dict.text' trigger='hover'>
                <div class='tooltip'>
                 {{ dict.text }}
                </div>
              </a-tooltip>
            </span>
          </template>
          <span
            slot='action'
            slot-scope='text, record'
            class='caozuo'
          >
            <a @click='handleEdit(record)'>编辑</a>
            <a-divider type='vertical' />
            <a-popconfirm title='确定删除吗?' @confirm='() => handleDelete(record.id)'>
              <a>删除</a>
            </a-popconfirm>
          </span>
          <template slot='tooltip' slot-scope='text'>
            <a-tooltip placement='topLeft' :title='text' trigger='hover'>
              <span class='tooltip'>
                {{ text }}
              </span>
            </a-tooltip>
          </template>
        </a-table>
      </a-card>
      <StandingBookModal ref='modalForm' @ok='modalFormOk'></StandingBookModal>
      <StandingBookEditModal ref='StandingBookEditModal' @ok='modalFormOk'></StandingBookEditModal>
      <!-- 下载模版 -->
      <iframe id='download' style='display: none'></iframe>
    </a-col>
  </a-row>
</template>
<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { ledgerPlanList } from '@/api/StandingBook'
import JDate from '@/components/jeecg/JDate'
import StandingBookModal from './modules/StandingBookModal'
import StandingBookEditModal from './modules/StandingBookEditModal'
import { getAction } from '@/api/manage'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
import yqYearPicker from '@comp/yq/YqYearPicker.vue'

export default {
  name: 'StandingBook',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components: {
    yqYearPicker,
    StandingBookModal,
    StandingBookEditModal,
    JDate
  },
  data() {
    return {
      maxLength:50,
      formItemLayout: {
        labelCol: {
          style: 'width:60px'
        },
        wrapperCol: {
          style: 'width:calc(100% - 60px)'
        }
      },
      size: 'default',
      // 表头
      columns: [
        {
          title: '省份',
          dataIndex: 'parentRegion'
        },
        {
          title: '地区',
          dataIndex: 'region',
          scopedSlots: { customRender: 'region' },
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 100px;max-width:400px'
            return { style: cellStyle }
          },
        },
        {
          title: '单位',
          dataIndex: 'company',
          scopedSlots: { customRender: 'tooltip' },
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 200px;max-width:300px'
            return { style: cellStyle }
          },
        },
        {
          title: '年份',
          dataIndex: 'year',
          customCell: () => {
            let cellStyle = 'text-align: center'
            return { style: cellStyle }
          },
        },
        {
          title: '总数',
          children: [
            {
              title: '总应下发数',
              dataIndex: 'totalDown',
              customCell: () => {
                let cellStyle = 'text-align: right'
                return { style: cellStyle }
              },
            },
            {
              title: '总应替换数',
              dataIndex: 'totalReplace',
              customCell: () => {
                let cellStyle = 'text-align: right'
                return { style: cellStyle }
              },
            }
          ]
        },
        {
          title: '服务器',
          children: [
            {
              title: '服务器应下发数',
              dataIndex: 'serverDown',
              customCell: () => {
                let cellStyle = 'text-align: right'
                return { style: cellStyle }
              },
            },
            {
              title: '服务器应替换数',
              dataIndex: 'serverReplace',
              customCell: () => {
                let cellStyle = 'text-align: right'
                return { style: cellStyle }
              },
            }
          ]
        },
        {
          title: '终端',
          children: [
            {
              title: '终端应下发数',
              dataIndex: 'terminalDown',
              customCell: () => {
                let cellStyle = 'text-align: right'
                return { style: cellStyle }
              },
            },
            {
              title: '终端应替换数',
              dataIndex: 'terminalReplace',
              customCell: () => {
                let cellStyle = 'text-align: right'
                return { style: cellStyle }
              },
            }
          ]
        },
        {
          title: '打印机',
          children: [
            {
              title: '打印机应下发数',
              dataIndex: 'printerDown',
              customCell: () => {
                let cellStyle = 'text-align: right'
                return { style: cellStyle }
              },
            },
            {
              title: '打印机应替换数',
              dataIndex: 'printerReplace',
              customCell: () => {
                let cellStyle = 'text-align: right'
                return { style: cellStyle }
              },
            }
          ]
        },
        {
          title: '数据库',
          children: [
            {
              title: '数据库应下发数',
              dataIndex: 'dataDown',
              customCell: () => {
                let cellStyle = 'text-align: right'
                return { style: cellStyle }
              },
            },
            {
              title: '数据库应替换数',
              dataIndex: 'dataReplace',
              customCell: () => {
                let cellStyle = 'text-align: right'
                return { style: cellStyle }
              },
            }
          ]
        },
        {
          title: '应用服务器',
          children: [
            {
              title: '应用服务器应下发数',
              dataIndex: 'appDown',
              customCell: () => {
                let cellStyle = 'text-align: right'
                return { style: cellStyle }
              },
            },
            {
              title: '应用服务应替换数',
              dataIndex: 'appReplace',
              customCell: () => {
                let cellStyle = 'text-align: right'
                return { style: cellStyle }
              },
            }
          ]
        },
        // {
        //   title: '操作',
        //   align: 'center',
        //   dataIndex: 'action',
        //   scopedSlots: { customRender: 'action' },
        //   width: '5%',
        //   fixed: 'right'
        // },
        {
          title: '操作',
          fixed: 'right',
          align:'center',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' },
          width: 160
        }
      ],
      url: {
        list: '/ledger/ledgerPlan/list',
        delete: '/ledger/ledgerPlan/delete',
        deleteBatch: '/ledger/ledgerPlan/deleteBatch',
        exportXlsUrl: '/ledger/ledgerPlan/exportXls',
        importExcelUrl: '/ledger/ledgerPlan/importExcel',
        downloadTemplateXlsUrl: '/ledger/ledgerPlan/downloadTemplate',
        getSysAreaList: '/sys/dict/getSysAreaList'
      },
      sysAreaList: []
    }
  },
  computed: {
    importExcelUrl: function() {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
    downloadTemplateXlsUrl: function() {
      return `${window._CONFIG['domianURL']}/${this.url.downloadTemplateXlsUrl}`
    }
  },
  mounted() {
    this.getSysAreaList()
  },
  methods: {
    handleChange(value) {
    },
    //日期调用方法
    getCurrentStyle(current, today) {
      const style = {}
      if (current.date() === 1) {
        style.border = '1px solid #1890ff'
        style.borderRadius = '50%'
      }
      return style
    },
    handleEdit: function(record) {
      this.$refs.StandingBookEditModal.edit(record)
      this.$refs.StandingBookEditModal.title = '编辑'
      this.$refs.StandingBookEditModal.disableSubmit = false
    },
    onChangeyear(date, dateString) {
    },
    //重置搜索框
    mySearchReset() {
      //地区查询条件
      ;(this.queryParamRegion = null),
        //单位查询条件
        (this.queryParamCompany = null),
        (this.startValue = null)
      this.endValue = null
      this.queryParam = {}
      this.loadData(1)
    },
    //excel模板
    handleTemplateXls() {
      const path = this.downloadTemplateXlsUrl
      document.getElementById('download').src = path
    },
    getSysAreaList() {
      getAction(this.url.getSysAreaList, null).then((res) => {
        if (res.success) {
          this.sysAreaList = res.result
        }
      })
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
</style>
