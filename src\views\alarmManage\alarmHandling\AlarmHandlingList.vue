<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <!-- 查询区域 -->
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class="card-style">
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="设备名称">
                  <a-input :maxLength='maxLength' placeholder="请输入设备名称" v-model="queryParam.deviceName"
                           autocomplete="off"
                           :allowClear="true">
                  </a-input>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="产品名称">
                  <a-select placeholder="请选择产品名称" v-model="queryParam.productId"
                            :getPopupContainer="(node) => node.parentNode" :allowClear="true" :show-search='true'
                            option-filter-prop='label'>
                    <a-select-option v-for="item in productList" :label='item.proName' :key="item.proId"
                                     :value="item.proId">{{ item.proName }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="告警级别">
                  <a-select v-model='queryParam.alarmLevel' :allow-clear='true' placeholder='请选择告警级别'>
                    <a-select-option v-for='item in alarmLevelList' :key='item.value' :label='item.title'
                                     :value='item.value'>
                      {{ item.title }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue" v-show="toggleSearchStatus">
                <a-form-item label="告警状态">
                  <j-dict-select-tag v-model="queryParam.alarmStatus" placeholder="请选择告警状态"
                                     dictCode="alarm_status" />
                </a-form-item>
              </a-col>
              <a-col :span="spanValue" v-show="toggleSearchStatus">
                <a-form-item label="触发时间">
                  <a-range-picker class="a-range-picker-choice-date" @change="onChangeTime"
                                  v-model="queryParam.alarmTime2Range" format="YYYY-MM-DD"
                                  :placeholder="['开始时间', '截止时间']" />
                </a-form-item>
              </a-col>
              <a-col :span="spanValue" v-show="toggleSearchStatus">
                <a-form-item label="处理状态">
                  <a-select v-model='queryParam.flowHandleStatus' :allow-clear='true' placeholder='请选择处理状态'>
                    <a-select-option v-for='item in handleStatusList' :key='item.value' :label='item.label'
                                     :value='item.value'>
                      {{ item.label }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue" v-show="toggleSearchStatus">
                <a-form-item label="分组名称">
                  <a-select placeholder="请选择分组名称" v-model="queryParam.groupId"
                            :getPopupContainer="(node) => node.parentNode" :allowClear="true" :show-search='true'
                            option-filter-prop='label'>
                    <a-select-option v-for="item in groupList" :label='item.name' :key="item.id"
                                     :value="item.id">{{ item.name }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="colBtnsSpan()">
                <span class="table-page-search-submitButtons"
                      :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button class="btn-search btn-search-style" type="primary" @click="searchQuery">查询</a-button>
                  <a-button class="btn-reset btn-reset-style" @click="searchReset">重置</a-button>
                  <a v-if="isVisible" class="btn-updown-style" @click="doToggleSearch">
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered="false" style="width: 100%; flex: auto">
        <!-- 操作按钮区域 -->
        <div class="table-operator table-operator-style">
          <a-dropdown v-if="selectedRowKeys.length > 0&&(canBatchClaim||canBatchClose||canBatchChangeResponsibleUser)">
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item v-if='canBatchClaim' key="1" @click="batchClaim">
                认领
              </a-menu-item>
              <a-menu-item v-if='canBatchChangeResponsibleUser' key="2"
                           @click="batchChangeResponsibleUser('转移责任人','transferBatch')">
                转移责任人
              </a-menu-item>
              <a-menu-item v-if='canBatchClose' key="3" @click="batchCloseAlarm">
                关闭
              </a-menu-item>
            </a-menu>
            <a-button>批量操作
              <a-icon type="down" />
            </a-button>
          </a-dropdown>
        </div>
        <!-- table区域-begin -->
        <a-table
          ref="table"
          bordered
          :rowKey="(record) => { return record.id}"
          :columns="columns"
          :dataSource="dataSource"
          :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
          :pagination="ipagination"
          :loading="loading"
          :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChangeKeysAndRows,getCheckboxProps:getCheckboxProps }"
          @change="handleTableChange">
          <template slot="alarmStatus" slot-scope="text,record">
            <span>{{ getAlarmStatus(record) }}</span>
          </template>
          <template slot="deviceName" slot-scope="text,record">
            <span style='color: #409eff;cursor: pointer' @click='handleViewDeviceInfo(record)'>{{ text }}</span>
          </template>
          <template slot="alarmTemplateName" slot-scope="text,record">
            <span style='color: #409eff;cursor: pointer' @click='handleViewAlarmTemplateInfo(record)'>{{ text }}</span>
          </template>
          <template slot="alarmLevel" slot-scope="text,record">
            <div :style='{backgroundColor:getAlarmColor(record)}'
                 style='display:inline-block;color:#ffffff; border-radius: 10px; padding: 2px 10px;'>
              {{ getAlarmTitle(record) }}
            </div>
          </template>
          <!--          <template slot="confirmStatus" slot-scope="text">
                      <span v-if="text == 1">已确认</span>
                      <span v-else>未确认</span>
                    </template>-->
          <template slot="flowHandleStatus" slot-scope="text">
            <span>{{ getHandlingStatus(text) }}</span>
          </template>
          <span slot="action" slot-scope="text, record" class="caozuo">
            <a @click="handleDetailPage(record)">查看</a>
            <a-divider type="vertical" />
            <!--当processType==='simple'时不禁用，其他情况下：不是未处理状态，或者责任人不为空且责任人不是当前登录人时，更多不可操作-->
            <a-dropdown
              :disabled='processType!=="simple"&&(record.flowHandleStatus!=handleStatusList[0].value||(record.responsibleUser&&record.responsibleUser.length>0&&record.responsibleUser!==sysUser?true:false)) || record.flowHandleStatus==="2"'
              :class='{"dropdown-disabled":processType!=="simple"&&(record.flowHandleStatus!=handleStatusList[0].value||(record.responsibleUser&&record.responsibleUser.length>0&&record.responsibleUser!==sysUser?true:false)) || record.flowHandleStatus==="2"}'>
                <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
                <a-menu slot="overlay" style="text-align: center">
                  <!--责任人为空，且是未处理状态，可认领-->
                  <a-menu-item
                    v-if='!record.responsibleUser||(record.responsibleUser&&record.responsibleUser.length==0)'>
                    <a href="javascript:void(0);" @click='claim(record)'>认领</a>
                  </a-menu-item>
                  <!-- 开始处理按钮：processType为simple且责任人是当前登录人且是未处理状态时显示 -->
                  <a-menu-item
                    v-if='processType==="simple"&&record.responsibleUser===sysUser && record.flowHandleStatus==="0"'>
                    <a href="javascript:void(0);" @click='handleStartProcess(record)'>处理</a>
                  </a-menu-item>
                  <!-- 完成处理按钮：processType为simple且责任人是当前登录人且是处理中状态时显示 -->
                  <a-menu-item
                    v-if='processType==="simple"&&record.responsibleUser===sysUser && record.flowHandleStatus==="1"'>
                    <a href="javascript:void(0);" @click='handleCompleteProcess(record)'>完成处理</a>
                  </a-menu-item>
                  <!--责任人是当前登录人、且是未处理状态，可处理-->
                  <a-menu-item
                    v-if='processType==="flowable"&&record.responsibleUser&&record.responsibleUser===sysUser'>
                    <a href="javascript:void(0);" @click='launchOrder(record)'>处理</a>
                  </a-menu-item>
                  <!--责任人是当前登录人、且是未处理状态，可转接责任人-->
                  <a-menu-item
                    v-if='record.responsibleUser&&record.responsibleUser===sysUser&&record.flowHandleStatus==="0"'>
                    <a href="javascript:void(0);"
                       @click='changeResponsibleUser(record,"转移责任人","transfer")'>转移责任人</a>
                  </a-menu-item>
                  <!--责任人是当前登录人、未处理状态且告警级别小于最大级别时，可升级-->
                  <a-menu-item
                    v-if='setUpgradeStatus(record)&&record.responsibleUser&&record.responsibleUser===sysUser&&record.flowHandleStatus==="0"'>
                    <a href="javascript:void(0);" @click='changeAlarmLevel(record,"upgrade")'>升级</a>
                  </a-menu-item>
                  <!--责任人是当前登录人、未处理状态且告警级别大于最小级别时，可升级-->
                   <a-menu-item
                     v-if='setDegradationStatus(record)&&record.responsibleUser&&record.responsibleUser===sysUser&&record.flowHandleStatus==="0"'>
                    <a href="javascript:void(0);" @click='changeAlarmLevel(record,"downgrade")'>降级</a>
                  </a-menu-item>
                  <!--责任人是当前登录人、且是未处理状态，可关闭-->
                  <a-menu-item
                    v-if='record.responsibleUser&&record.responsibleUser===sysUser&&record.flowHandleStatus==="0"'>
                    <a href="javascript:void(0);" @click='closeAlarm(record)'>关闭</a>
                  </a-menu-item>
                </a-menu>
              </a-dropdown>
          </span>
          <template slot="tooltip" slot-scope="text">
            <a-tooltip placement="topLeft" :title="text" trigger="hover">
              <div class="tooltip">
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
      </a-card>
      <!-- 表单区域 -->
      <Process-instance-start
        v-if='dialogStartInstanceVisible'
        :dialogStartInstanceVisible.sync='dialogStartInstanceVisible'
        :process-definition='processDefinition'
        :associationId='associationId'
        :alarmHistory='alarmHistory'
        :formUrl='formUrl'
        :startUrl='startUrl'
        :showDdraft='false'
        :dict-key='"alarmConfirm"'
        method='post'
        @loadData='resetData'>
      </Process-instance-start>
      <assign-responsible-person ref="modalForm" :user-name='sysUser' @ok='resetData'></assign-responsible-person>

      <!-- 添加开始处理的弹窗 -->
      <a-modal
        title="开始处理"
        :visible="startProcessVisible"
        @ok="submitStartProcess"
        @cancel="startProcessVisible = false"
        :confirmLoading="confirmLoading"
      >
        <a-form :form="startProcessForm">
          <a-form-item label="故障类型" :labelCol="{ span: 5 }" :wrapperCol="{ span: 16 }">
            <j-dict-select-tag v-decorator="['errorType',{ rules: [{ required: true, message: '请选择故障类型' }] }]"
                               :trigger-change="true"
                               placeholder="请选择故障类型" dictCode="fault_type" />
          </a-form-item>
          <a-form-item label="故障描述" :labelCol="{ span: 5 }" :wrapperCol="{ span: 16 }">
            <a-textarea
              v-decorator="['errorDesc', { rules: [{ required: true, message: '请输入故障描述' }] }]"
              :rows="4"
              placeholder="请输入故障描述"
            />
          </a-form-item>
        </a-form>
      </a-modal>

      <!-- 添加完成处理的弹窗 -->
      <a-modal
        title="完成处理"
        :visible="completeProcessVisible"
        @ok="submitCompleteProcess"
        @cancel="completeProcessVisible = false"
        :confirmLoading="confirmLoading"
      >
        <a-form :form="completeProcessForm">
          <a-form-item label="处理结果" :labelCol="{ span: 5 }" :wrapperCol="{ span: 16 }">
            <a-input
              v-decorator="['handleResult', { rules: [{ required: true, message: '请输入处理结果' }] }]"
              placeholder="请输入处理结果"
            />
          </a-form-item>
          <a-form-item label="故障原因" :labelCol="{ span: 5 }" :wrapperCol="{ span: 16 }">
            <a-input
              v-decorator="['errorReason', { rules: [{ required: true, message: '请输入故障原因' }] }]"
              placeholder="请输入故障原因"
            />
          </a-form-item>
          <a-form-item label="备注" :labelCol="{ span: 5 }" :wrapperCol="{ span: 16 }">
            <a-textarea
              v-decorator="['remark', { rules: [{ required: false, message: '请输入备注' }] }]"
              :rows="4"
              placeholder="请输入备注"
            />
          </a-form-item>
        </a-form>
        <template slot="footer">
          <a-button @click="completeProcessVisible = false">取消</a-button>
          <a-button type="primary" @click="submitCompleteProcess" :loading="confirmLoading">确定</a-button>
          <a-button type="default" @click="handleAddToKnowledgeBase">加入知识库</a-button>
        </template>
      </a-modal>

      <!-- 加入知识库弹窗 -->
      <add-knowledge-modal
        ref="addKnowledgeModal"
        @ok="handleKnowledgeModalOk">
      </add-knowledge-modal>
    </a-col>
  </a-row>
</template>

<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { getAction, postAction } from '@/api/manage'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
import ProcessInstanceStart from '../../flowable/process-instance-start/module/ProcessInstanceStart.vue'
import { dataAndFunction } from '@views/alarmManage/modules/dataAndFunction'
import assignResponsiblePerson from '@views/alarmManage/modules/AssignResponsiblePerson.vue'
import { getDictItemsFromCache, queryConfigureDictItem } from '@api/api'
import AddKnowledgeModal from '@views/opmg/knowledgeManagement/knowledgeBase/modules/AddKnowledgeModal.vue'

export default {
  name: 'AlarmHandlingList',
  mixins: [JeecgListMixin, YqFormSearchLocation, dataAndFunction],
  components: {
    ProcessInstanceStart,
    assignResponsiblePerson,
    AddKnowledgeModal
  },
  data() {
    return {
      maxLength: 50,
      description: '告警处理页面',
      formItemLayout: {
        labelCol: {
          style: 'width:80px'
        },
        wrapperCol: {
          style: 'width:calc(100% - 80px)'
        }
      },
      // 表头
      columns: [
        {
          title: '设备名称',
          dataIndex: 'deviceName',
          scopedSlots: {
            customRender: 'deviceName'
          }
        },
        {
          title: '告警名称',
          dataIndex: 'templateName',
          scopedSlots: {
            customRender: 'alarmTemplateName'
          }
        },
        {
          title: '产品名称',
          dataIndex: 'productName'
        },
        {
          title: '告警级别',
          dataIndex: 'alarmLevel',
          scopedSlots: {
            customRender: 'alarmLevel'
          }
        },
        {
          title: '告警状态',
          dataIndex: 'alarmStatus',
          scopedSlots: {
            customRender: 'alarmStatus'
          }
        },
        {
          title: '触发时间',
          dataIndex: 'alarmTime1'
        },
        {
          title: '重复次数',
          dataIndex: 'repeatTimes',
          customCell: () => {
            let cellStyle = 'text-align: right'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '处理状态',
          dataIndex: 'flowHandleStatus',
          scopedSlots: {
            customRender: 'flowHandleStatus'
          }
        },
        {
          title: '责任人',
          dataIndex: 'responsibleUser_dictText'
        },
        {
          title: '分组名称',
          dataIndex: 'groupName'
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 120,
          scopedSlots: {
            customRender: 'action'
          }
        }
      ],
      url: {
        list: '/alarm/alarmHistory/list',
        claim: '/alarm/alarmHistory/claim',
        claimBatch: '/alarm/alarmHistory/claimBatch',
        startProcess: '/alarm/alarmHistory/startHandle',
        completeProcess: '/alarm/alarmHistory/completeHandle'
      },
      disableMixinCreated: true,
      startProcessVisible: false,
      completeProcessVisible: false,
      confirmLoading: false,
      currentRecord: null,
      processType: ''
    }
  },
  created() {
    this.startProcessForm = this.$form.createForm(this)
    this.completeProcessForm = this.$form.createForm(this)
    this.queryAllProducts()
    this.getAlarmLevelData()
    this.getGroupList()
    this.getProcessDefinitionKey()
    this.getDict('alarm_status', 'alarmStatusList')
    this.getProcessType()
  },
  activated() {
    this.loadData()
  },
  methods: {
    getProcessType() {
      queryConfigureDictItem({
        parentCode: 'AlarmProcessMethod',
        childCode: 'processType'
      }).then((res) => {
        if (res.success && res.result) {
          this.processType = res.result
        } else {
          this.processType = 'flowable'
        }
      })
    },
    /**未处理可选，处理中，已关闭，处理完成的不可选*/
    getCheckboxProps(record) {
      let res = record.responsibleUser
      let bo = res && res.length > 0 && res !== this.sysUser ? true : false
      return ({
        props: {
          disabled: this.getTableChecked(record.flowHandleStatus) || bo
          // topicName: record.topicName
        }
      })
    },
    /**认领后，重新加载数据，更新已选数据的责任人和处理状态，设置批量认领、转移责任人、关闭的按钮状态*/
    claim(record) {
      getAction(this.url.claim, { id: record.id }).then(res => {
        if (res.success) {
          this.$message.success(res.message)
          this.resetData()
        } else {
          this.$message.warning(res.message)
        }
      }).catch(err => {
        this.$message.warning(err.message)
      })
    },
    /**批量认领后，重新加载列表，清除已选数据，重置批量认领、批量关闭按钮状态*/
    batchClaim() {
      getAction(this.url.claimBatch, { ids: this.selectedRowKeys.join(',') }).then(res => {
        if (res.success) {
          this.$message.success(res.message)
          this.resetData()
        } else {
          this.$message.warning(res.message)
        }
      }).catch(err => {
        this.$message.warning(err.message)
      })
    },
    /**重置取消列表选择，批量操作*/
    resetData() {
      this.loadData()
      this.allSelectedRows = []
      this.onClearSelected()
      this.canBatchClose = false
      this.canBatchClaim = false
      this.canBatchChangeResponsibleUser = false
    },
    /**列表框选事件*/
    onSelectChangeKeysAndRows(selectedRowKeys, selectionRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectionRows = selectionRows

      this.updateAllSelectionRows(selectedKeys, selectionRows)
      this.setBatchClaimStatus()
      this.setBatchCloseStatus()
      this.setBatchChangeResponsibleUserStatus()
    },
    // 开始处理
    handleStartProcess(record) {
      this.currentRecord = record
      this.startProcessVisible = true
      this.startProcessForm.resetFields()
    },

    // 提交开始处理
    submitStartProcess() {
      this.startProcessForm.validateFields((err, values) => {
        if (!err) {
          this.confirmLoading = true
          const params = {
            alarmId: this.currentRecord.id,
            errorType: values.errorType,
            errorDesc: values.errorDesc
          }

          postAction(this.url.startProcess, params).then(res => {
            if (res.success) {
              this.$message.success('开始处理成功')
              this.startProcessVisible = false
              this.loadData()
            } else {
              this.$message.error(res.message || '开始处理失败')
            }
          }).finally(() => {
            this.confirmLoading = false
          })
        }
      })
    },

    // 完成处理
    handleCompleteProcess(record) {
      this.currentRecord = record
      this.completeProcessVisible = true
      this.completeProcessForm.resetFields()
    },

    // 提交完成处理
    submitCompleteProcess() {
      this.completeProcessForm.validateFields((err, values) => {
        if (!err) {
          this.confirmLoading = true
          const params = {
            alarmId: this.currentRecord.id,
            handleResult: values.handleResult,
            errorReason: values.errorReason,
            remark: values.remark
          }

          postAction(this.url.completeProcess, params).then(res => {
            if (res.success) {
              this.$message.success('完成处理成功')
              this.completeProcessVisible = false
              this.loadData()
            } else {
              this.$message.error(res.message || '完成处理失败')
            }
          }).finally(() => {
            this.confirmLoading = false
          })
        }
      })
    },

    // 加入知识库
    async handleAddToKnowledgeBase() {
      // 获取完成处理表单的数据
      this.completeProcessForm.validateFields(async (err, values) => {
        if (!err) {
          // 获取开始处理时的故障描述
          const startProcessData =await this.getStartProcessData()
          console.log(startProcessData)
          // 准备知识库数据
          const knowledgeData = {
            title: `${this.currentRecord.deviceName}-${this.currentRecord.templateName}故障处理`,
            plan: this.buildKnowledgeContent(startProcessData, values),
            knowledgeType: 0,
            isPrivate: '0',
            method: '0',
            otherConfig: 'allowComment,allowDownload'
          }

          // 获取父主题ID（这里可以根据实际业务逻辑来确定）
          const parentTopicId = await this.getParentTopicId()

          // 设置AddKnowledgeModal的父主题ID
          this.$refs.addKnowledgeModal.parentTopicId = parentTopicId

          // 重新获取主题列表（基于父ID）
          this.$refs.addKnowledgeModal.getTopicList()

          // 打开知识库弹窗
          this.$refs.addKnowledgeModal.edit(knowledgeData)
        } else {
          this.$message.warning('请先完善处理信息')
        }
      })
    },

    // 获取父主题ID
    async getParentTopicId() {
      try {
        const res = await queryConfigureDictItem({
          parentCode: 'alarmKnowledgeTheme',
          childCode: 'id'
        })
        if (res.success && res.result) {
          console.log(res.result)
          return res.result // 返回实际结果
        } else {
          this.$message.warning('未配置告警知识库主题配置')
          return null // 或抛出错误
        }
      } catch (error) {
        this.$message.error('请求失败')
        console.error(error)
        return null
      }
    },

    // 获取开始处理时的数据
    async getStartProcessData() {
      const res = await getAction('/alarm/alarmHistory/listProcess', { alarmId: this.currentRecord.id });
      if (res.success) {
        const title=   await this.getTitleByCode(res.result.errorType)
        let startData = { errorType: title, errorDesc: res.result.errorDesc }
        return startData
      } else {
        this.$message.warning(res.message)
        return null
      }


    },
    async getTitleByCode(value) {
      const res = await getDictItemsFromCache("fault_type");
      if (res){
        const title = res.find(item => item.value === value)?.title;
        return title;
      }
    },

    // 构建知识库内容
    buildKnowledgeContent(startData, completeData) {
      let content = '<h3>故障处理记录</h3>'
      content += '<h4>基本信息</h4>'
      content += `<p><strong>设备名称：</strong>${this.currentRecord.deviceName}</p>`
      content += `<p><strong>告警名称：</strong>${this.currentRecord.templateName}</p>`
      content += `<p><strong>产品名称：</strong>${this.currentRecord.productName}</p>`
      content += `<p><strong>告警级别：</strong>${this.getAlarmTitle(this.currentRecord)}</p>`
      content += `<p><strong>触发时间：</strong>${this.currentRecord.alarmTime1}</p>`

      if (startData.errorType) {
        content += '<h4>故障信息</h4>'
        content += `<p><strong>故障类型：</strong>${startData.errorType}</p>`
      }

      if (startData.errorDesc) {
        content += `<p><strong>故障描述：</strong>${startData.errorDesc}</p>`
      }

      content += '<h4>处理结果</h4>'
      content += `<p><strong>处理结果：</strong>${completeData.handleResult}</p>`
      content += `<p><strong>故障原因：</strong>${completeData.errorReason}</p>`

      if (completeData.remark) {
        content += `<p><strong>备注：</strong>${completeData.remark}</p>`
      }

      return content
    },

    // 知识库弹窗确认回调
    handleKnowledgeModalOk() {
      this.$message.success('已成功加入知识库')
      // 可以在这里添加其他逻辑，比如关闭完成处理弹窗
      this.completeProcessVisible = false
      this.loadData()
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';

.caozuo .dropdown-disabled {
  color: rgba(0, 0, 0, 0.25) !important;
  cursor: default;
}

.confirm {
  color: rgba(0, 0, 0, 0.25) !important;
}
</style>