<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <a-card :bordered="false" style="width: 100%; flex: auto;overflow-x:hidden;">
        <a-spin :spinning="confirmLoading" style="border: 1px solid #dedede;">
          <a-form-model ref="form" :model="model" :rules="validatorRules">
            <a-row class="title-row">配置应用</a-row>
            <a-row class="second-row">
              <a-col :span="24">
                <a-form-model-item
                  label="选择终端"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  prop="uniqueCode"
                >
                  <a-select
                    mode="multiple"
                    placeholder="请选择终端"
                    :allowClear="true"
                    v-model="model.uniqueCode"
                    @deselect="deselect"
                    @select="select"
                  >
                    <a-select-option
                      v-for="(item, index) in terminalList"
                      :key="index"
                      :value="item.unique_code"
                      :label="item.name"
                      :text="item.name"
                    >{{ item.name }}</a-select-option>
                  </a-select>
                </a-form-model-item>
              </a-col>
              <a-col :span="24">
                <a-form-model-item
                  label="设置应用名称"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  required
                >
                  <a-table :columns="columns" :data-source="coreTable" bordered :pagination="false">
                    <template slot="status" slot-scope="text, record">
                      <a-radio-group @change="onChange" v-model="record.status">
                        <a-radio :value="0">展示应用</a-radio>
                        <a-radio :value="1">过滤应用</a-radio>
                      </a-radio-group>
                    </template>
                    <template slot="applicationName" slot-scope="text, record">
                      <a-textarea
                        v-model="record.applicationName"
                        placeholder="请输入应用名称，用逗号分割"
                        style="margin: 8px;"
                      />
                    </template>
                  </a-table>
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col :span="24" style="text-align: center; margin: 0.45rem;">
                <a-button @click="submitForm" class="submit-btn">保 存</a-button>
              </a-col>
            </a-row>
          </a-form-model>
        </a-spin>
      </a-card>
    </a-col>
  </a-row>
</template>

<script>
import { httpAction, getAction, postAction } from '@/api/manage'
import pick from 'lodash.pick'
export default {
  name: 'ConfigurationApplication',
  data() {
    return {
      // 表单数据
      model: {
        uniqueCode: ''
      },
      form: this.$form.createForm(this),
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 12 }
      },
      confirmLoading: false,
      url: {
        add: '/terminal/application/add',
        queryTerminalList: '/terminal/application/queryInfoName',
        queryDetail: '/terminal/application/queryDetail'
      },
      //表单校验规则
      validatorRules: {
        uniqueCode: [
          {
            required: true,
            message: '请选择！'
          }
        ]
      },
      terminalList: [], // 终端列表
      columns: [
        {
          title: '序号',
          dataIndex: '',
          key: 'rowIndex',
          width: 70,
          align: 'center',
          customRender: function(t, r, index) {
            return parseInt(index) + 1
          }
        },
        {
          title: '终端名称',
          align: 'center',
          dataIndex: 'terminalName'
        },
        {
          title: '应用展示方式',
          align: 'center',
          dataIndex: 'status',
          scopedSlots: { customRender: 'status' }
        },
        {
          title: '应用名称',
          align: 'center',
          dataIndex: 'applicationName',
          scopedSlots: { customRender: 'applicationName' }
        }
      ],
      coreTable: []
    }
  },
  created() {
    this.getDetail()
    this.queryTerminalList()
  },
  methods: {
    getDetail() {
      getAction(this.url.queryDetail).then(res => {
        if (res.success && res.result.length > 0) {
          let list = []
          let defaultValue = []
          res.result.map(item => {
            defaultValue.push(item.uniqueCode)
            list.push({
              applicationName: item.applicationName,
              uniqueCode: item.uniqueCode,
              status: item.status,
              terminalName: item.terminalName || ''
            })
          })
          this.model.uniqueCode = defaultValue
          this.coreTable = list
        }
      })
    },
    queryTerminalList() {
      getAction(this.url.queryTerminalList).then(res => {
        if (res.success) {
          this.terminalList = res.result
        }
      })
    },
    deselect(value, option) {
      // 删除调用
      for (let i = 0; i < this.coreTable.length; i++) {
        if (this.coreTable[i].uniqueCode.indexOf(value) > -1) {
          this.coreTable.splice(i, 1)
          break
        }
      }
    },
    select(value, option) {
      // 选中调用
      this.coreTable.push({ terminalName: option.data.attrs.text, uniqueCode: value, applicationName: '', status: 0 })
    },
    onChange(e) {},
    submitForm() {
      const that = this
      // 触发表单验证
      that.$refs.form.validate((err, values) => {
        if (err) {
          that.confirmLoading = true
          httpAction(this.url.add, this.coreTable, 'post')
            .then(res => {
              that.confirmLoading = false
              if (res.success) {
                that.$message.success(res.message)
              } else {
                that.$message.warning(res.message)
              }
            })
            .catch(() => {
              that.confirmLoading = false
            })
        }
      })
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';

.title-row {
  height: 48px;
  background: #f5f5f5;
  border-bottom: 1px solid #dedede;
  padding: 13px 0 13px 24px;
  font-family: PingFangSC-Medium;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.85);
}

.second-row {
  margin-top: 24px;
}

.submit-btn {
  color: #409eff;
  background: #ecf5ff;
  border-color: #b3d8ff;
}

.submit-btn:hover {
  color: #fff !important;
  background-color: #409eff !important;
  border-color: #409eff !important;
}
</style>