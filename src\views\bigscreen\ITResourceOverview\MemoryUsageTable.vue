<template>
  <div class='memory-usage-table' ref='itMemoryUsage'>
    <!--    <div class="header-right-time">
          <div class="time-range-span">
            <a-select v-model='deviceType'
                      style='width: 120px'
                      size='small'
                      placeholder='请选择'
                      :getPopupContainer="(node) => node.parentNode"
                      :dropdownClassName='"custom-select-dropdown"'
                      :allow-clear='false'>
              <a-select-option v-for="item in types" :value="item.value" :key="'t_'+item.value" :label="item.label">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </div>
          <span class='time-range-label'>设备类型:</span>
        </div>-->
    <div class='memory-usage-table-content'>
      <slot>
        <ul class='scroll-list' style='height: 36px;margin-bottom: 12px'>
          <li class='scroll-list-item'>
            <div class='scroll-list-item-inner' style='background: transparent;color:rgba(255,255,255,0.7);'>
              <div class='item-sort' :style='{"--num":``}'>
                序号
              </div>
              <div class='item-name'>设备名称</div>
              <div class='item-free'>内存总量</div>
              <div class='item-rate'>使用率(%)</div>
            </div>

          </li>
        </ul>
        <vue-seamless-scroll v-if='srollOption.autoPlay' :data='listData' class='scroll-warp'
                             :class-option='srollOption'>
          <ul class='scroll-list'>
            <li class='scroll-list-item' v-for='(item, index) in listData'
                :key='index'>
              <div class='scroll-list-item-inner' :class='`scroll-list-item-${index}`'>
                <div class='item-sort' :title='item.updateTime' :style='{"--num":`"${index+1}"`}'>
                  <div class='item-sort-line'>
                    <img v-if='index===0' class='triangle' src='@/assets/bigScreen/ITResource/triangle.png'>
                  </div>{{ index + 1 }}
                </div>
                <div class='item-name' :title='item.name'>{{ item.name }}</div>
                <div class='item-free' :title='item.memTotal.value+item.memTotal.unit'>
                  {{ item.memTotal.value + item.memTotal.unit }}
                </div>
                <div class='item-rate' :title='item.memRate'>{{ item.memRate }}</div>
              </div>
            </li>
          </ul>
        </vue-seamless-scroll>
        <div v-else class='scroll-warp'>
          <ul class='scroll-list'>
            <li class='scroll-list-item' v-for='(item, index) in listData'
                :key='index'>
              <div class='scroll-list-item-inner' :class='`scroll-list-item-${index}`'>
                <div class='item-sort' :title='item.updateTime' :style='{"--num":`"${index+1}"`}'>
                  <div class='item-sort-line'>
                    <img v-if='index===0' class='triangle' src='@/assets/bigScreen/ITResource/triangle.png'>
                  </div>{{ index + 1 }}
                </div>
                <div class='item-name' :title='item.name'>{{ item.name }}</div>
                <div class='item-free' :title='item.memTotal.value+item.memTotal.unit'>
                  {{ item.memTotal.value + item.memTotal.unit }}
                </div>
                <div class='item-rate' :title='item.memRate'>{{ item.memRate }}</div>
              </div>
            </li>
          </ul>
        </div>
      </slot>
    </div>
  </div>
</template>
<script>
import vueSeamlessScroll from 'vue-seamless-scroll'
import resizeObserverMixin from '@views/statsCenter/com/resizeObserverMixin'
import { getAction } from '@api/manage'
export default {
  name: 'MemoryUsageTable',
  components: { vueSeamlessScroll },
  mixins: [resizeObserverMixin],
  data() {
    return {
      listData: [],
      srollOption: {
        step: 0.35, // 步长
        speed: 50, // 滚动速度
        timer: 3000,// 滚动时间间隔
        autoPlay: false,
        limitMoveNum: 100000
        // singleHeight: 36 ,
      },
      maxNum: 0,
      types: [
        { label: 'a', value: 'a' },
        { label: 'b', value: 'b' },
        { label: 'c', value: 'c' }
      ],
      deviceType: ''
    }
  },
  created() {
    this.getMemoryUsage()
  },
  mounted() {
    this.$nextTick(() => {
      this.setPlayState()
    })
  },
  methods: {
    getMemoryUsage() {
      this.listData = []
      getAction('/openAPI/getDevMemoryOccupyTop').then(res=>{
        if(res.success && res.result && res.result.length){
          // console.log("获取到的内存使用率Top5",res)
          this.listData = res.result
        }
      }).catch(err=>{
        console.log("获取内存使用率Top5失败",err)
      })
    },
    // 屏幕变化回调
    resizeObserverCb() {
      this.setPlayState()
    },
    //设置滚动状态
    setPlayState() {
      if (this.$refs.itMemoryUsage && this.listData.length) {
        let bounded = this.$refs.itMemoryUsage.getBoundingClientRect()
        this.maxNum = Math.floor((bounded.height - 48) / 36)
        if (this.maxNum > 0 && this.maxNum < this.listData.length) {
          this.srollOption.limitMoveNum = this.maxNum
          this.srollOption.autoPlay = true
          return
        }
      }

      this.srollOption.autoPlay = false
      this.srollOption.limitMoveNum = this.listData.length + 1
    }
  }
}
</script>


<style scoped lang='less'>
@import "~@assets/less/onclickStyle.less";

/deep/ .ant-select-arrow {
  color: rgba(255, 255, 255, 0.7);
}

/deep/ .ant-select-selection {
  border: 1px solid rgba(255, 255, 255, 0.7);
}

.memory-usage-table {
  height: 100%;

  .memory-usage-table-content {
    height: calc(100% - 0px);
    overflow: hidden;
    padding: 0 12px;
  }
}

.scroll-warp {
  height: 100%;
  overflow: hidden;
  width: 100%;

}

.scroll-list {
  width: 100%;
  height: 100%;
  margin: 0px;
  padding: 0px;
}

.scroll-list-item {
  &::marker {
    content: '';
  }

  padding: 4px;

  .item-sort {
    width: 32px;
    overflow: hidden;
    position: relative;
    line-height: 1;
    opacity: 0.95;
    display: flex;
    align-items: center;
    .item-sort-line{
      width: 3px;
      height: 33px;
      background: #9D9D9D;
      margin-right: 14px;
    }
  }

  .item-name {
    width: calc((100% - 32px) * 0.45);
    text-align: center;
    opacity: 0.95;
    line-height: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .scroll-list-item-inner {
    display: flex;
    align-items: center;
    color: rgba(255, 255, 255, 0.85);
    font-size: 14px;
    justify-content: space-between;
    height: 33px;
    background: linear-gradient(90deg, rgba(79,84,87,0.23) 0%, rgba(79,84,87,0.01) 98%);
  }

  .scroll-list-item-0 {
    background: linear-gradient(90deg, rgba(255, 71, 28, 0.23) 0%, rgba(255, 71, 28, 0.01) 98%);
    .item-sort-line{
      background: rgba(255, 71, 28, 0.8);
      position: relative;
      .triangle{
        position: absolute;
        left: 6px;
        top: 11px;
        width: 7px;
        height: 11px;
      }
    }
  }

  .scroll-list-item-1 {
    background: linear-gradient(90deg, rgba(216, 125, 5, 0.23) 0%, rgba(216, 125, 5, 0.01) 98%);
    .item-sort-line{
      background: rgba(255, 144, 0, 0.8);
    }
  }

  .scroll-list-item-2 {
    background: linear-gradient(90deg, rgba(213, 183, 5, 0.23) 0%, rgba(213, 183, 5, 0.01) 98%);
    .item-sort-line{
      background: rgba(255, 216, 0, 0.8);
    }
  }

  .item-free {
    width: calc((100% - 32px) * 0.25);
    line-height: 1;
    text-align: center;
    opacity: 0.95;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .item-rate {
    width: calc((100% - 32px) * 0.25);
    line-height: 1;
    text-align: center;
    opacity: 0.95;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.scroll-list-odd {
  background: rgba(29, 97, 140, 0.25);
}

.header-right-time {
  display: flex;
  align-items: center;
  flex-direction: row-reverse;
  margin-right: 12px;

  .time-range-label {
    font-weight: 400;
    font-size: 14px;
    color: #FFFFFF;
    opacity: 0.7;
    width: 62px;
  }

  .time-range-span {
    margin-left: 8px;
  }

  .report-btn {
    height: 30px;
    //line-height: 30px;
    color: rgba(255, 255, 255, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.7);
    padding: 0 8px;
    border-radius: 4px;
    margin-left: 12px;
    display: flex;
    align-items: center;
    cursor: pointer;

    &:hover {
      color: rgba(255, 255, 255, 1);
      border: 1px solid rgba(255, 255, 255, 1);
    }
  }
}
</style>