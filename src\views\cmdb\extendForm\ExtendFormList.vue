<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll zxw">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <!-- 查询区域 -->
      <a-card :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="表单名称">
                  <a-input
                    :maxLength="maxLength"
                    placeholder="请输入表单名称"
                    :allowClear="true"
                    autocomplete="off"
                    v-model="queryParam.name"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="资产类型">
                  <j-tree-select-expand
                    placeholder="请选择资产类型"
                    v-model="assetsCategoryId"
                    dict="cmdb_assets_category,category_name,id"
                    pidField="parent_id"
                    condition='{"delflag":0,"category_state":"0"}'
                    pidValue="0"
                    @change="changeSelected($event)"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="colBtnsSpan()">
                <span
                  class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                >
                  <a-button type="primary" @click="dosearch">查询</a-button>
                  <a-button @click="doreset" style="margin-left: 8px">重置</a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <!-- 查询区域-END -->
      <a-card :bordered="false" style="flex: auto">
        <!-- 操作按钮区域 -->
        <div class="table-operator">
          <a-button @click="handleAdd" v-has="'extendFields:add'">新增</a-button>
          <a-button @click="handleExportXls('附加表单')" v-has="'extendFields:export'">导出</a-button>
          <a-button @click="handleTemplateXls()" v-has="'extendFields:import'">下载模版</a-button>

          <a-upload v-has="'extendFields:import'"
            name="file"
            :showUploadList="false"
            :multiple="false"
            :headers="tokenHeader"
            :action="importExcelUrl"
            @change="handleImportExcel"
          >
            <a-button>导入</a-button>
          </a-upload>
          <!-- 高级查询区域 -->
          <!-- <j-super-query :fieldList="superFieldList" ref="superQueryModal" @handleSuperQuery="handleSuperQuery"></j-super-query> -->
          <a-dropdown v-if="selectedRowKeys.length > 0" v-has="'extendFields:delete'">
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item key="1" @click="batchDel">删除 </a-menu-item>
            </a-menu>
            <a-button>
              批量操作
              <a-icon type="down" />
            </a-button>
          </a-dropdown>
        </div>

        <!-- table区域-begin -->
        <div>
          <a-table
            bordered
            ref="table"
            rowKey="id"
            :columns="columns"
            :dataSource="dataSource"
            :pagination="ipagination"
            :loading="loading"
            :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
            @change="handleTableChange"
          >
            <template slot="htmlSlot" slot-scope="text">
              <div v-html="text"></div>
            </template>
            <template slot="imgSlot" slot-scope="text">
              <span v-if="!text" style="font-size: 14px">无图片</span>
              <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width: 80px; font-size: 14px" />
            </template>
            <template slot="fileSlot" slot-scope="text">
              <span v-if="!text" style="font-size: 14px">无文件</span>
              <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
                下载
              </a-button>
            </template>

            <span slot="action" slot-scope="text, record">
              <a @click="handleEdit1(record)">设计</a>
              <span>
                 <a-divider type="vertical" />
                 <a @click="handleDetailPage(record)">查看</a>
              </span>
              <span v-has="'extendFields:edit'">
                 <a-divider type="vertical" />
                 <a @click="handleEdit(record)">编辑</a>
              </span>
             <span v-has="'extendFields:delete'">
                <a-divider type="vertical" />
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
              </a-popconfirm>
             </span>
            </span>
            <template slot="tooltip" slot-scope="text">
              <a-tooltip placement="topLeft" :title="text" trigger="hover">
                <div class="tooltip">
                  {{ text }}
                </div>
              </a-tooltip>
            </template>
          </a-table>
        </div>
        <extend-field-list-modal ref="fieldForm"></extend-field-list-modal>
        <extend-form-modal ref="modalForm" @ok="modalFormOk"></extend-form-modal>

        <!-- 下载模版 -->
        <iframe id="download" style="display: none"></iframe>
      </a-card>
    </a-col>
  </a-row>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import ExtendFormModal from './modules/ExtendFormModal'
import JSuperQuery from '@/components/jeecg/JSuperQuery.vue'
import ExtendFieldListModal from '../extendField/ExtendFieldListModal.vue'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
export default {
  name: 'ExtendFormList',
  mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
  components: {
    ExtendFormModal,
    JSuperQuery,
    'extend-field-list-modal': ExtendFieldListModal,
  },
  data() {
    return {
      description: ' 附加表单管理页面',
      maxLength:50,
      // 表头
      columns: [
        {
          title: '表单名称',
          dataIndex: 'name',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 50px;max-width:300px'
            return { style: cellStyle }
          },
        },
        {
          title: '表单编码',
          dataIndex: 'code',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 50px;max-width:300px'
            return { style: cellStyle }
          },
        },
        {
          title: '资产类型',
          dataIndex: 'categoryName',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 50px;max-width:300px'
            return { style: cellStyle }
          },
        },
        {
          title: '描述',
          dataIndex: 'description',
          scopedSlots: { customRender: 'tooltip' },
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 50px;max-width:400px'
            return { style: cellStyle }
          },
        },
        {
          title: '操作',
          dataIndex: 'action',
          fixed: 'right',
          align: 'center',
          width: 200,
          scopedSlots: { customRender: 'action' },
        },
      ],
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      url: {
        list: '/extendForm/extendForm/list',
        delete: '/extendForm/extendForm/delete',
        deleteBatch: '/extendForm/extendForm/deleteBatch',
        exportXlsUrl: '/extendForm/extendForm/exportXls',
        importExcelUrl: 'extendForm/extendForm/importExcel',
        downloadUserTemplateUrl: '/extendForm/extendForm/downloadUserTemplate',
        downloadTemplateXlsUrl: '/extendForm/extendForm/downloadTemplate',
      },
      assetsCategoryId: '',
    }
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
    downloadTemplateXlsUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.downloadTemplateXlsUrl}`
    },
  },
  methods: {
    changeSelected(e) {
      this.assetsCategoryId = e
      this.queryParam.assetsCategoryId = e
    },
    //表单查询,点击查询按钮，默认查询第一页
    dosearch() {
      this.loadData(1)
    },
    //表单重置
    doreset() {
      //重置form表单，不重置tree选中节点
      this.queryParam = {}
      this.assetsCategoryId = ''
      this.loadData(1)
    },

    //excel模板
    handleTemplateXls() {
      const path = this.downloadTemplateXlsUrl
      document.getElementById('download').src = path
    },
    handleEdit1: function (record) {
      this.$refs.fieldForm.edit1(record)
      this.$refs.fieldForm.title = '设计'
      this.$refs.fieldForm.disableSubmit = false
    },
  }
}
</script>
<style lang="less" scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
::v-deep .ant-table-thead > tr > th {
  text-align: center;
  white-space: nowrap;
}
::v-deep .ant-table-tbody > tr > td {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}
</style>
