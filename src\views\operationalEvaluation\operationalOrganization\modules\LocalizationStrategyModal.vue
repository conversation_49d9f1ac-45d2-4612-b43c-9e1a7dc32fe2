<template>
  <j-modal
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    :title="title"
    :visible="visible"
    :width="width"
    cancelText="关闭"
    switchFullscreen
    :maskClosable='false'
    @cancel="handleCancel"
    @ok="handleOk">

    <template slot="footer">
      <a-button key="close" type='default' @click="handleCancel"> 关闭 </a-button>
      <a-button key="back" type="primary" :loading="confirmLoading" :disabled="confirmLoading" v-show="!disableSubmit" @click="handleOk"> 确认 </a-button>
    </template>
    <code-rule-setting-form 
    v-bind="$attrs" 
    ref="realForm" 
    :disabled="disableSubmit"
    @loadingHandler="loadingHandler" 
    @ok="submitCallback"></code-rule-setting-form>
  </j-modal>
</template>

<script>
import CodeRuleSettingForm from './LocalizationStrategyForm'

export default {
  name: 'LocalizationStrategyModal',
  components: {
    CodeRuleSettingForm
  },
 /*  props: {
    deviceTypes: {
      type: Array,
      default: () => []
    }, 
    logicTypes: {
      type: Array,
      default: () => []
    }, 
  }, */
  data() {
    return {
      title: '',
      width: '70%',
      visible: false,
      disableSubmit: false,
      confirmLoading: false,
    }
  },
  methods: {
    add() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.add()
      })
    },
    edit(record) {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.edit(record)
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      // console.log('handleOk')
      this.$refs.realForm.submitForm()
    },
    submitCallback() {
      this.$emit('ok')
      this.visible = false
    },
    handleCancel() {
      this.close()
    },
    /**
     * 控制loading状态
     * @param {Boolean} loading
     */
    loadingHandler(loading) {
      this.confirmLoading = loading
    }
  }
}
</script>
<style scoped lang='less'>
@import '~@assets/less/YQNormalModal.less';
</style>