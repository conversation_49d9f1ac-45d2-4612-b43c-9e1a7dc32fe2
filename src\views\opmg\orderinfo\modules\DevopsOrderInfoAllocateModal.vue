<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    :destroyOnClose="true"
    :centered="true"
    switchFullscreen
    @ok="handleOk"
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }"
    @cancel="handleCancel"
    cancelText="关闭"
  >
    <devops-order-info-allocate-form
      ref="DevopsOrderInfoAllocateForm"
      @ok="submitCallback"
      :disabled="disableSubmit"
    ></devops-order-info-allocate-form>
  </j-modal>
</template>

<script>
import DevopsOrderInfoAllocateForm from './DevopsOrderInfoAllocateForm'

export default {
  name: 'DevopsOrderInfoAllocateModal',
  components: {
    DevopsOrderInfoAllocateForm,
  },
  data() {
    return {
      title: '',
      width: '896px',
      visible: false,
      disableSubmit: false,
    }
  },
  methods: {
    add() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.DevopsOrderInfoAllocateForm.add()
      })
    },
    edit(record) {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.DevopsOrderInfoAllocateForm.edit(record)
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      this.$refs.DevopsOrderInfoAllocateForm.submitForm()
    },
    submitCallback() {
      this.$emit('ok')
      this.visible = false
    },
    handleCancel() {
      this.close()
    },
  },
}
</script>
<style lang="less" scoped>
::v-deep .ant-modal-body {
  padding: 24px 48px 24px 48px;
}
::v-deep .ant-modal {
  padding: 24px;
}
@media (max-width: 908px) {
  ::v-deep .ant-modal {
    max-width: calc(100vw - 12px);
    margin: 0;
  }
}
.j-modal-box.fullscreen {
  margin: 0 !important;
  max-width: 100vw !important;
  ::v-deep .ant-modal {
    max-width: 100vw !important;
    margin: 0;
  }
}
</style>