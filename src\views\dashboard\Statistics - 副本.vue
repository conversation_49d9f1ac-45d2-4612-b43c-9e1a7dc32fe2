<template>
  <div>
    <a-spin :spinning="loadingSpin" style="height: calc(100vh - 75px);">
      <a-row :gutter="10" justify="center" type="flex" style="" style="margin-bottom: 10px">
        <a-col :lg="12" :sm="24" :xs="24">
          <a-card :loading="loading" style="width: 100%; height: 100%">
            <div slot="title">
              <a-icon type="clock-circle" />
              我的待办【<a slot="extra" @click="goPage(1)">{{ dataSourceCount1 }}</a>】
              <a-tooltip title="刷新">
                <a-icon type="sync" style="color: #ccc; cursor: pointer; font-size: 14px" @click="loadTodoList" />
              </a-tooltip>
            </div>
            <a slot="extra" @click="goPage(1)">更多
              <a-icon type="double-right" />
            </a>
            <a-table :body-style="{ overflowX: 'auto' }" size="middle" rowKey="id" bordered :columns="columnstodo" :dataSource="dataSource1"
              :scroll="setscorll(dataSource1)" :pagination="false">
              <span slot="action" slot-scope="text, record">
                <a @click="btnView(record)">查看</a>
                <span v-if="record.assignee == null || record.assignee == ''">
                  <a-divider type="vertical" />
                  <a @click="btnClaim(record)">认领并处理</a>
                </span>
                <span v-if="
                    record.assignee === $store.getters.userInfo.username &&
                    (record.endTime == null || record.endTime == '') &&
                    record.claimTime != null &&
                    record.claimTime != ''
                  ">
                  <a-divider type="vertical" />
                  <a @click="btnUnclaim(record)">取消认领</a>
                </span>
                <span v-if="record.endTime == null && record.assignee != null && record.assignee != ''">
                  <a-divider type="vertical" />
                  <a @click="btnExcuteTask(record)">处理</a>
                </span>
              </span>
            </a-table>
          </a-card>
        </a-col>
        <a-col :lg="12" :sm="24" :xs="24" >
          <a-card :loading="loadingToRead" style="width: 100%; height: 100%">
            <div slot="title">
              <a-icon type="read" />
              我的待阅 【<a slot="extra" @click="goPage(2)">{{ dataSourceCount2 }}</a>】
              <a-tooltip title="刷新">
                <a-icon type="sync" style="color: #ccc; cursor: pointer; font-size: 14px" @click="loadToRead" />
              </a-tooltip>
            </div>
            <a slot="extra" @click="goPage(2)">更多
              <a-icon type="double-right" />
            </a>
            <a-table :body-style="{ overflowX: 'auto' }" size="middle" rowKey="id" bordered :columns="columnstoread" :dataSource="dataSource2"
              :scroll="setscorll(dataSource2)" :pagination="false">
              <span slot="action" slot-scope="text, record">
                <a @click="btnViewToRead(record)">查看</a>
              </span>
            </a-table>
          </a-card>
        </a-col>
      </a-row>
      <a-row :gutter="10" justify="center" type="flex" style="margin-bottom: 10px">
        <a-col :lg="12" :sm="24" :xs="24" >
          <a-card :loading="loadingMyProcess" style="width: 100%; height: 100%">
            <div slot="title">
              <a-icon type="container" />
              我的申请 【<a slot="extra" @click="goPage(3)">{{ dataSourceCount3 }}</a>】
              <a-tooltip title="刷新">
                <a-icon type="sync" style="color: #ccc; cursor: pointer; font-size: 14px" @click="loadDataMyProcess" />
              </a-tooltip>
            </div>
            <a slot="extra" @click="goPage(3)">更多
              <a-icon type="double-right" />
            </a>
            <a-table :body-style="{ overflowX: 'auto' }" rowKey="id" :columns="columnsmy" bordered :dataSource="dataSource3" :pagination="false"
              :scroll="setscorll(dataSource3)" size="middle">
              <span slot="status" slot-scope="status">
                <a-tag v-if="status == 1" color="#108ee9"> 处理中</a-tag>
                <a-tag v-if="status == 2" color="#f5222d"> 已结束</a-tag>
                <a-tag v-if="status == 3"> 已撤回</a-tag>
                 <a-tag color="#fa8c16" v-if="status == 4"> 待评价</a-tag>
                <a-tag color="#87d068" v-if="status == 5"> 已评价</a-tag>
              </span>
              <!-- 字符串超长截取省略号显示-->
              <span slot="name" slot-scope="text">
                <j-ellipsis :value="text" :length="12"></j-ellipsis>
              </span>
              <span slot="action" slot-scope="text, record">
           <template v-if="record.status == 1">
            <a href="javascript:void(0);" @click="withdraw(record)">撤回</a>
            <a-divider type="vertical" />
            <a href="javascript:" @click="history(record,'查看进度')">查看进度</a>
            <a-divider type="vertical" />
            <a href="javascript:void(0);" @click="detail(record)">表单数据</a>
          </template>

          <template v-if="record.status ===4">
            <a href="javascript:void(0);" @click="evaluate(record,false)">评价</a>
            <a-divider type="vertical" />
          </template>
          <template v-if="record.status !==1">
            <a href="javascript:void(0);" @click="history(record,'审批历史')">审批历史</a>
            <a-divider type="vertical" />
            <a href="javascript:void(0);" @click="detail(record)">表单数据</a>
          </template>

          <template v-if="record.status ===5">
            <a-divider type="vertical" />
            <a href="javascript:void(0);" @click="evaluateLook(record)">查看评价</a>
          </template>

              </span>
            </a-table>
          </a-card>
        </a-col>
        <a-col :lg="12" :sm="24" :xs="24" >
          <a-card :loading="loadingBusiness" style="width: 100%; height: 100%">
            <div slot="title">
              <a-icon type="calendar" />
              我的草稿 【<a slot="extra" @click="goPage(4)">{{ dataSourceCount4 }}</a>】
              <a-tooltip title="刷新">
                <a-icon type="sync" style="color: #ccc; cursor: pointer; font-size: 14px" @click="loadBusiness" />
              </a-tooltip>
            </div>
            <a slot="extra" @click="goPage(4)">更多
              <a-icon type="double-right" />
            </a>
            <a-table :body-style="{ overflowX: 'auto' }" rowKey="id" :columns="columnsbusiness" bordered :dataSource="dataSource4" :pagination="false"
              :scroll="setscorll(dataSource4)" size="middle">
              <span slot="action" slot-scope="text, record">
                <a-popconfirm title="确定提交申请吗?" @confirm="() => handleApply(record.id)">
                  <a>提交申请</a>
                </a-popconfirm>
                <a-divider type="vertical" />
                <a @click="handleEdit(record)">编辑</a>
                <a-divider type="vertical" />
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDeleteBusiness(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </span>
            </a-table>
          </a-card>
        </a-col>
      </a-row>
    </a-spin>
    <evaluation-modal ref="evaluationModal" @ok="loadDataMyProcess"></evaluation-modal>
    <process-instance-info-modal ref="processInstanceInfoModalForm"></process-instance-info-modal>
    <!--    任务执行区域-->
    <execute-task v-if="dialogExecuteTaskVisible" :execute-task-id="executeTaskId"
      :processInstanceId.sync="processInstanceId" :selectRow="selectionRow" :visible.sync="dialogExecuteTaskVisible"
      @ok="loadTodoList"></execute-task>

    <process-history-modal ref="processHistoryModal"></process-history-modal>
    <process-modal ref="processModal"></process-modal>
    <business-edit ref="businessEdit" :isview="isview"></business-edit>
    <a-modal title="确认撤回" v-model="modalCancelVisible" :mask-closable="false" :width="500">
      <a-form ref="delForm" v-model="cancelForm" :label-width="70" v-if="modalCancelVisible">
        <a-form-item label="撤回原因" prop="reason">
          <a-input v-model="cancelForm.reason" :allow-clear="true" :rows="4" autocomplete="off" type="textarea" />
        </a-form-item>
      </a-form>
      <div slot="footer" style="text-align: right">
        <a-button type="text" @click="modalCancelVisible = false">取消</a-button>
        <a-button type="primary" :disabled="submitLoading" @click="handelSubmitCancel">提交</a-button>
      </div>
    </a-modal>
  </div>
</template>

<script>
  import {
    deleteAction,
    getAction,
    postAction
  } from '@/api/manage'
  import ProcessInstanceInfoModal from '@/views/flowable/process-instance/modules/ProcessInstanceInfoModal.vue'
  import executeTask from '@/views/flowable/task-todo/modules/executeTask.vue'
  import ProcessHistoryModal from '@/views/flowable/myProcess/modules/ProcessHistoryModal.vue'
  import ProcessModal from '@/views/flowable/myProcess/modules/ProcessModal.vue'
  import BusinessEdit from '@/views/flowable/process-business/modules/BusinessEdit.vue'
  import {
    taskTodoApi
  } from '@api/flowable'
  import {
    filterDictTextByCache
  } from '@/components/dict/JDictSelectUtil'
 import evaluationModal from '../flowable/myProcess/modules/EvaluationModal.vue'
  export default {
    name: 'Statistics',
    components: {
      ProcessInstanceInfoModal,
      executeTask,
      ProcessHistoryModal,
      ProcessModal,
      BusinessEdit,
      evaluationModal
    },
    data() {
      return {
        loading: false,
        loadingToRead: false,
        loadingMyProcess: false,
        loadingBusiness: false,
        loadingSpin: false,
        dataSource1: [],
        dataSource2: [],
        dataSource3: [],
        dataSource4: [],
        dataSourceCount1: 0,
        dataSourceCount2: 0,
        dataSourceCount3: 0,
        dataSourceCount4: 0,
        columnstodo: [
          {
            title: '流程实例名称',
            width: '26%',
            dataIndex: 'processInstanceName',
            customCell: () => {
              let cellStyle = 'text-align:center'
              return {
                style: cellStyle
              }
            },
          },
          {
            title: '任务名称',
            width: '20%',
            dataIndex: 'name',
          },
          {
            title: '开始时间',
            width: '25%',
            dataIndex: 'createTime',
            customCell: () => {
              let cellStyle = 'text-align:center'
              return {
                style: cellStyle
              }
            },
          },
          {
            title: '操作',
            width: '25%',
            dataIndex: 'action',
            scopedSlots: {
              customRender: 'action'
            },
            customCell: () => {
              let cellStyle = 'text-align:center'
              return {
                style: cellStyle
              }
            },
          },
        ],
        columnstoread: [
          {
            title: '流程实例名称',
            width: '31%',
            dataIndex: 'name',
            customCell: () => {
              let cellStyle = 'text-align:center'
              return {
                style: cellStyle
              }
            },
          },
          {
            title: '发起人',
            width: '15%',
            dataIndex: 'startUserName',
            customCell: () => {
              let cellStyle = 'text-align:center'
              return {
                style: cellStyle
              }
            },
          },
          {
            title: '开始时间',
            align: 'center',
            width: '25%',
            dataIndex: 'startTime',
          },
          {
            title: '操作',
            width: '25%',
            dataIndex: 'action',
            scopedSlots: {
              customRender: 'action'
            },
            customCell: () => {
              let cellStyle = 'text-align:center'
              return {
                style: cellStyle
              }
            },
          },
        ],
        columnsmy: [
          {
            title: '业务标题',
            width: '23%',
            dataIndex: 'name',
            scopedSlots: {
              customRender: 'name'
            },
          },
          {
            title: '当前审批环节',
            width: '20%',
            dataIndex: 'currTaskName',
            customCell: () => {
              let cellStyle = 'text-align:center'
              return {
                style: cellStyle
              }
            },
          },
          {
            title: '状态',
            width: '8%',
            dataIndex: 'status',
            scopedSlots: {
              customRender: 'status'
            },
            customCell: () => {
              let cellStyle = 'text-align:center'
              return {
                style: cellStyle
              }
            },
          },
          {
            title: '提交申请时间',
            width: '20%',
            align: 'center',
            dataIndex: 'startTime',
          },
          {
            title: '操作',
            width: '25%',
            dataIndex: 'action',
            scopedSlots: {
              customRender: 'action'
            },
            customCell: () => {
              let cellStyle = 'text-align:center'
              return {
                style: cellStyle
              }
            },
          },
        ],
        columnsbusiness: [
          {
            title: '所属流程',
            width: '31%',
            dataIndex: 'processName',
            customCell: () => {
              let cellStyle = 'text-align:center'
              return {
                style: cellStyle
              }
            },
          },
          {
            title: '流程类型',
            dataIndex: 'processType',
            width: '15%',
            customRender: (text) => {
              //字典值替换通用方法
              return filterDictTextByCache('bpm_process_type', text)
            },
            customCell: () => {
              let cellStyle = 'text-align:center'
              return {
                style: cellStyle
              }
            },
          },
          {
            title: '创建时间',
            align: 'center',
            width: '25%',
            dataIndex: 'createTime',
          },
          {
            title: '操作',
            dataIndex: 'action',
            width: '25%',
            scopedSlots: {
              customRender: 'action'
            },
            customCell: () => {
              let cellStyle = 'text-align:center'
              return {
                style: cellStyle
              }
            },
          },
        ],
        url: {
          todoList: '/flowable/task/listTodo',
          toReadList: '/flowable/processInstance/listCcToMe',
          myProcessList: '/flowable/processInstance/listStartedByMe',
          businessList: '/business/actZBusiness/list',
          withdrowUrl: '/flowable/processInstance/delete',
          applyUrl: '/business/actZBusiness/sbApply',
          deleteBusiness: '/business/actZBusiness/delete',
        },
        selectionRow: {},
        executeTaskId: null,
        processInstanceId: null,
        dialogExecuteTaskVisible: false,
        modalCancelVisible: false,
        cancelForm: {
          reason: '撤回申请',
          cascade: false,
          processInstanceId: null,
        },
        submitLoading: false,
        isview: undefined,
      }
    },
    computed: {
      tasktimestimp() {
        return this.$store.getters.tasktimestimp
      },
    },
    watch: {
      tasktimestimp: {
        handler(nval, oval) {
          this.loadTodoList()
          this.loadDataMyProcess()
        },
        deep: true,
        immediate: true,
      },
    },
    created() {
      this.loadTodoList()
      this.loadToRead()
      this.loadDataMyProcess()
      this.loadBusiness()
    },
    methods: {
      evaluateLook(record) {
        this.$refs.evaluationModal.edit(record.id)
        this.$refs.evaluationModal.title = '查看评价'
        this.$refs.evaluationModal.disableSubmit = false
      },
      //点击评价
      evaluate(record) {
        this.$refs.evaluationModal.add(record.id)
        this.$refs.evaluationModal.title = '评价'
        this.$refs.evaluationModal.disableSubmit = false
      },
      setscorll(dataSource1) {
        // return 'auto'
      },
      loadTodoList() {
        if (!this.url.todoList) {
          this.$message.error('请设置url.todoList属性!')
          return
        }
        this.loading = true
        getAction(this.url.todoList, {
          pageNo: 1,
          pageSize: 6
        }).then((res) => {
          if (res.success) {
            this.dataSource1 = res.result.records || res.result
            this.dataSourceCount1 = res.result.total
          } else {
            this.$message.warning(res.message)
          }
          this.loading = false
        })
      },
      loadToRead() {
        if (!this.url.toReadList) {
          this.$message.error('请设置url.toReadList!')
          return
        }
        this.loadingToRead = true
        getAction(this.url.toReadList, {
          pageNo: 1,
          pageSize: 6
        }).then((res) => {
          if (res.success) {
            this.dataSource2 = res.result.records || res.result
            this.dataSourceCount2 = res.result.total
          } else {
            this.$message.warning(res.message)
          }
          this.loadingToRead = false
        })
      },
      loadDataMyProcess() {
        if (!this.url.myProcessList) {
          this.$message.error('请设置url.myProcessList!')
          return
        }
        this.loadingMyProcess = true
        getAction(this.url.myProcessList, {
          pageNo: 1,
          pageSize: 6
        }).then((res) => {
          if (res.success) {
            this.dataSource3 = res.result.records || res.result
            this.dataSourceCount3 = res.result.total
          } else {
            this.$message.warning(res.message)
          }
          this.loadingMyProcess = false
        })
      },
      loadBusiness() {
        if (!this.url.businessList) {
          this.$message.error('请设置url.businessList!')
          return
        }
        this.loadingBusiness = true
        getAction(this.url.businessList, {
          pageNo: 1,
          pageSize: 6
        }).then((res) => {
          if (res.success) {
            this.dataSource4 = res.result.records || res.result
            this.dataSourceCount4 = res.result.total
          } else {
            this.$message.warning(res.message)
          }
          this.loadingBusiness = false
        })
      },
      //签收
      btnClaim(row) {
        let current = this
        taskTodoApi.claim({
          taskId: row.id
        }).then((res) => {
          current.loadTodoList()
          this.btnExcuteTask(row)
        })
      },
      //取消签收
      btnUnclaim(row) {
        let current = this
        taskTodoApi.unclaim({
          taskId: row.id
        }).then((res) => {
          current.$message.success(res.message)
          current.loadTodoList()
        })
      },
      //处理
      btnExcuteTask(row) {
        this.selectionRow = row
        this.executeTaskId = row.id
        this.processInstanceId = row.processInstanceId
        this.dialogExecuteTaskVisible = true
      },
      btnView(record) {
        console.log(record)
        this.$refs.processInstanceInfoModalForm.init(record.processInstanceId)
        this.$refs.processInstanceInfoModalForm.title = '流程实例信息'
        this.$refs.processInstanceInfoModalForm.disableSubmit = false
      },
      btnViewToRead(record) {
        this.$refs.processInstanceInfoModalForm.init(record.id)
        this.$refs.processInstanceInfoModalForm.title = '流程实例信息'
        this.$refs.processInstanceInfoModalForm.disableSubmit = false
      },
      //撤回
      withdraw(record) {
        //this.cancelForm.id = v.id
        this.cancelForm.processInstanceId = record.id
        this.modalCancelVisible = true
      },
      handelSubmitCancel() {
        this.submitLoading = true
        deleteAction(this.url.withdrowUrl, this.cancelForm)
          .then((res) => {
            console.log(res)
            if (res.code == 200) {
              this.$message.success('撤回成功,表单已退回至草稿')
            } else {
              this.$message.error('删除失败')
            }
            this.modalCancelVisible = false
            this.resetCancleForm()
            this.loadDataMyProcess()
            this.loadBusiness()
          })
          .finally(() => (this.submitLoading = false))
      },
      resetCancleForm() {
        this.cancelForm = {
          reason: '撤回申请',
          cascade: false,
          processInstanceId: null,
        }
      },
      //查看进度
      history(record, title) {
        if (!record.id) {
          this.$message.error('流程实例ID不存在')
          return
        }
        record.state = record.endTime
        this.$refs.processHistoryModal.init(record.id)
        this.$refs.processHistoryModal.title = title
        this.$refs.processHistoryModal.disableSubmit = false
      },
      detail(record) {
        if (!record.id) {
          this.$message.error('申请不存在')
          return
        }
        this.$refs.processModal.init(record)
      },
      handleApply(id) {
        postAction(this.url.applyUrl, {
          id: id
        }).then((res) => {
          if (res.success) {
            this.$message.success(res.message)
          } else {
            this.$message.error('提交失败！')
          }
          this.loadBusiness()
          this.loadDataMyProcess()
        })
      },
      handleEdit(record) {
        if (!record.id) {
          this.$message.error('申请不存在')
          return
        }
        this.isview = true
        this.$refs.businessEdit.init(record)
      },
      handleDeleteBusiness(id) {
        if (!this.url.deleteBusiness) {
          this.$message.error('请设置url.deleteBusiness!')
          return
        }
        var that = this
        deleteAction(that.url.deleteBusiness, {
          id: id
        }).then((res) => {
          if (res.success) {
            //重新计算分页问题
            that.$message.success(res.message)
            that.loadBusiness()
          } else {
            that.$message.warning(res.message)
          }
        })
      },
      goPage(arg) {
        switch (arg) {
          case 1:
            this.$router.push({
              path: '/flowable/taskTodo'
            })
            break
          case 2:
            this.$router.push({
              path: '/flowable/taskRead'
            })
            break
          case 3:
            this.$router.push({
              path: '/flowable/myProcess'
            })
            break
          case 4:
            this.$router.push({
              path: '/flowable/processBusiness'
            })
            break
        }
      },
    },
  }
</script>

<style lang='less' scoped>
  @import '~@assets/less/common.less';
  //@import '~@assets/less/YQCommon.less';

  ::v-deep .ant-table-small>.ant-table-content>.ant-table-body {
    margin: 0px !important;
  }

  ::v-deep .ant-card-body {
    padding: 12px !important;
  }
</style>