<template>
  <a-tabs v-if="curCel" :animated="false" v-model="activeKey" class="ele-attr" @change='tabChange'>
    <a-tab-pane tab="节点配置" key="1">
      <div v-if="activeKey == 1" class="tab-con">
        <a-row align="middle" v-if="cellShape !== 'text-block' && size === 1 && showLable">
          <a-col :span="8">节点名称:</a-col>
          <a-col :span="16">
            <a-input type="text" :value="nodeConfig.label" style="width: 100%" @change="onLabelChange" />
          </a-col>
        </a-row>
        <a-row align="middle" v-else-if="size === 1 && showLable" style="margin-top: 12px">
          <a-col :span="8">文本内容:</a-col>
          <a-col :span="16">
            <a-textarea type="text" :value="nodeConfig.label" style="width: 100%" @change="onLabelChange" />
          </a-col>
        </a-row>
        <a-row align="middle" style="margin-top: 12px" v-if="showLable">
          <a-col :span="8">文字大小:</a-col>
          <a-col :span="16">
            <a-input-number
              v-model="nodeConfig.labelSize"
              :min="8"
              :max="60"
              @change="onNodeFontSizeChange"
              style="width: 100%"
            />
          </a-col>
        </a-row>
        <a-row align="middle" v-if="showLabelPos && showLable">
          <a-col :span="8">文字位置</a-col>
          <a-col :span="16">
            <a-select style="width: 100%" v-model="nodeConfig.labelPos" @change="onNodeLablePos">
              <a-select-option :value="0">下</a-select-option>
              <a-select-option :value="1">上</a-select-option>
              <a-select-option :value="2">右</a-select-option>
              <a-select-option :value="3">左</a-select-option>
            </a-select>
          </a-col>
        </a-row>
        <a-row align="middle" v-if="showLable">
          <a-col :span="8">文字颜色</a-col>
          <a-col :span="16">
            <a-input
              type="color"
              v-model="nodeConfig.labelColor"
              style="width: 100%"
              @change="onLabelColorChange"
            />
          </a-col>
        </a-row>

        <!-- 端口节点大小 -->
        <div v-if="showElementSize">
          <a-row v-if="batchOrNo" align="middle" style="margin-top: 12px">
            <a-col :span="8">图标大小:</a-col>
            <a-col :span="16">
              <a-input-number
                v-model="nodeConfig.eleSize"
                :min="8"
                :max="60"
                @change="onElementSizeChange"
                style="width: 100%"
              />
            </a-col>
          </a-row>
          <!-- <a-row align="middle" style="margin-top: 12px">
          <a-col :span="8">图标角度:</a-col>
          <a-col :span="16">
            <a-input-number
              v-model="nodeConfig.eleAngle"
              :min="-360"
              :max="360"
              @change="onElementAngleChange"
              style="width: 100%"
            />
          </a-col>
        </a-row> -->
          <a-row align="middle" v-if="showLabelPos">
            <a-col :span="8">图标方向</a-col>
            <a-col :span="16">
              <a-select style="width: 100%" v-model="nodeConfig.direction" @change="onNodeDirection">
                <a-select-option :value="0">上</a-select-option>
                <a-select-option :value="180">下</a-select-option>
                <a-select-option :value="90">右</a-select-option>
                <a-select-option :value="-90">左</a-select-option>
              </a-select>
            </a-col>
          </a-row>
        </div>
        <div v-if="showNodeSize">
          <a-row align="middle">
            <a-col :span="8">节点大小:</a-col>
          </a-row>
          <a-row align="middle">
            <a-col :span="24">
              <div style="display: flex; justify-content: space-between">
                    <span class="ant-input-group-wrapper" style="width: calc(50% - 5px)">
                      <span class="ant-input-wrapper ant-input-group">
                        <span class="ant-input-group-addon">宽</span>
                        <a-input-number
                          style="width: 100%"
                          :disabled="groupSizeDisabled"
                          v-model="nodeConfig.size.width"
                          :min="30"
                          @change="onSizeChange($event, 'width')"
                        />
                      </span>
                    </span>
                <span class="ant-input-group-wrapper" style="width: calc(50% - 5px)">
                      <span class="ant-input-wrapper ant-input-group">
                        <span class="ant-input-group-addon">高</span>
                        <a-input-number
                          style="width: 100%"
                          :disabled="groupSizeDisabled"
                          :min="30"
                          v-model="nodeConfig.size.height"
                          @change="onSizeChange($event, 'height')"
                        />
                      </span>
                    </span>
              </div>
            </a-col>
          </a-row>
        </div>
        <div v-if="size === 1">
          <a-row align="middle">
            <a-col :span="8">节点位置:</a-col>
          </a-row>
          <a-row align="middle" style="width: 100%">
            <a-col :span="24">
              <a-input
                type="number"
                :addonBefore="'X'"
                style="width: calc(50% - 5px); margin-right: 10px"
                v-model="nodeConfig.position.x"
                @change="onPositionChange($event, 'x')"
              />
              <a-input
                type="number"
                :addonBefore="'Y'"
                style="width: calc(50% - 5px)"
                v-model="nodeConfig.position.y"
                @change="onPositionChange($event, 'y')"
              />
            </a-col>
          </a-row>
        </div>
        <div v-if="showBgType">
          <a-row align="middle">
            <a-col :span="6">背景类型</a-col>
            <a-col :span="18">
              <a-radio-group name="radioGroup" :value="nodeConfig.bgType" @change="bgTypeChange">
                <a-radio value="color"> 背景色 </a-radio>
                <a-radio value="picture"> 背景图 </a-radio>
              </a-radio-group>
            </a-col>
          </a-row>
          <a-row align="middle" v-if="nodeConfig.bgType === 'picture'">
            <a-col :span="24">
              <a-form-item class="two-words" label="背景图">
                <j-image-upload
                  v-model="nodeConfig.bgImage"
                  isMultiple
                  :number="1"
                  @change="bgImageChange"
                ></j-image-upload>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row align="middle" v-else>
            <a-col :span="8">背景色</a-col>
            <a-col :span="16">
              <a-input type="color" v-model="nodeConfig.bgColor" style="width: 100%" @change="bgColorChange" />
            </a-col>
          </a-row>
        </div>

        <!-- <div v-if="showRadius">
        <a-row align="middle">
          <a-col :span="8">节点圆角:</a-col>
        </a-row>
        <a-row align="middle">
          <a-col :span="24">
            <div style="display: flex; justify-content: space-between">
              <span class="ant-input-group-wrapper" style="width: calc(50% - 5px)">
                <span class="ant-input-wrapper ant-input-group">
                  <span class="ant-input-group-addon">X</span>
                  <a-input-number
                    style="width: 100%"
                    :disabled="groupSizeDisabled"
                    v-model="nodeConfig.radius.rx"
                    :min="0"
                    @change="onRadiusChange($event, 'rx')"
                  />
                </span>
              </span>
              <span class="ant-input-group-wrapper" style="width: calc(50% - 5px)">
                <span class="ant-input-wrapper ant-input-group">
                  <span class="ant-input-group-addon">Y</span>
                  <a-input-number
                    style="width: 100%"
                    :disabled="groupSizeDisabled"
                    :min="0"
                    v-model="nodeConfig.radius.ry"
                    @change="onRadiusChange($event, 'ry')"
                  />
                </span>
              </span>
            </div>
          </a-col>
        </a-row>
      </div> -->

        <a-row align="middle" v-if="size === 1 && showIcon">
          <a-col :span="8">图标:</a-col>
          <a-col :span="14">
            <img :src="nodeImg" style="width: 50%; height: 50%" alt="无图片" />
          </a-col>
        </a-row>
      </div>
    </a-tab-pane>
    <a-tab-pane tab="业务属性" key="2" v-if="showLable">
      <a-spin :spinning="spinning" style='height: 100%' wrapperClassName='pannel-node'>
        <div v-if="activeKey == 2" class="tab-con">
          <div v-if="showBandwidth">

            <a-row align="middle" v-if="size === 1">
              <a-col :span="8">物理端口</a-col>
              <a-col :span="16">
                <a-select
                  style="width: 100%"
                  allowClear
                  v-model="portIndex"
                  placeholder="请选择"
                  @change="onPortDescChange"
                >
                  <a-select-option v-for="item in physicalPorts" :key="item.index" :value="item.index">{{
                      item.portDesc
                    }}</a-select-option>
                </a-select>
              </a-col>
            </a-row>
            <a-row align="middle">
              <a-col :span="8">端口带宽</a-col>
              <a-col :span="16">
                <a-input placeholder="请输入带宽" v-model="bandwidth" @change="onBandwidthChange" />
              </a-col>
            </a-row>
            <a-row align="middle">
              <a-col :span="8">是否上报</a-col>
              <a-col :span="16">
                <j-dict-select-tag
                  style="width: 100%"
                  v-model="isReport"
                  :triggerChange='false'
                  placeholder='请选是否上报'
                  dictCode='ZB_REPORT_STATUS'
                  @change="reportChange"
                />
              </a-col>
            </a-row>
            <!-- <a-row align="middle" v-if="size === 1">
              <a-col :span="24" style="margin-bottom: 8px">虚拟端口</a-col>
              <a-col :span="24">
                <a-select
                  style="width: 100%"
                  mode="multiple"
                  allowClear
                  v-model="virtualPorts"
                  placeholder="请选择"
                  @change="onVirtualChange"
                >
                  <a-select-option v-for="item in virtualPortList" :key="'vir_' + item.index" :value="item.index">{{
                    item.portDesc
                  }}</a-select-option>
                </a-select>
              </a-col>
            </a-row> -->
          </div>
        </div>
      </a-spin>
    </a-tab-pane>
  </a-tabs>
</template>

<script>
import FlowGraph from '../../../graph/index'
import { httpAction, getAction } from '@/api/manage'
import JImageUpload from '@/components/jeecg/JImageUpload'
export default {
  name: 'Index',
  components: {
    JImageUpload,
  },
  props: {
    globalGridAttr: {
      type: Object,
      default: null,
      required: true,
    },
    id: {
      type: String,
      default: '',
    },
    size: {
      type: Number,
      default: 0,
      required: true,
    },
    productId: {
      type: String,
      default: '',
      required: true,
    },
  },
  data() {
    return {
      activeKey: '1',
      curCel: null,
      iconAdd: '/topo/device/icon/add',
      iconUpdate: 'product/product/updateIcons',
      downloadUrl: window._CONFIG['downloadUrl'] + '/',
      cellShape: '',
      nodeType: '',
      nodeConfig: {
        label: '',
        labelSize: 14,
        labelColor: '#000000',
        labelPos: 0,
        groupButtonHide: false,
        textNodeBorder: false,
        position: { x: 0, y: 0 },
        size: { width: 0, height: 0 },
        radius: { rx: 0, ry: 0 },
        groupLine: 'solid',
        bgType: '',
        bgImage: '',
        bgColor: '',
        eleSize: 14,
        eleAngle: 0,
        direction: 0,
      },
      imgList: [],
      nodeImg: '',
      nodePanel: '',
      nodeIp: '',
      posArr: [
        { refx: 0.5, refy: '100%', anchor: 'middle', vAnchor: 'top' },
        { refx: 0.5, refy: 0, anchor: 'middle', vAnchor: 'bottom' },
        { refx: '100%', refy: 0.5, anchor: 'start', vAnchor: 'middle' },
        { refx: -4, refy: 0.5, anchor: 'end', vAnchor: 'middle' },
      ],
      selectNodes: [],
      virtualData: null,
      nodeScore: 0,
      bandwidth: '',
      isReport: '',
      portIndex: '',
      virtualPorts: [],
      physicalPortList: [],
      spinning: false,
      bindIndexs:[],
    }
  },
  created() {
    if(this.showLable){
      this.activeKey = this.globalGridAttr.nodeActiveKey
    }else{
      this.globalGridAttr.nodeActiveKey = this.activeKey = "1"
    }
    if(this.activeKey == 2){
      this.getPhysicalPortList()
    }
  },
  computed: {
    showLable(){
      return this.curCel.shape !== "panel-node"
    },
    physicalPorts() {
      return this.physicalPortList.filter((el) => el.portType === '6' && !this.bindIndexs.includes(el.index))
    },
    virtualPortList() {
      return this.physicalPortList.filter((el) => el.portType === '0')
    },
    showBandwidth() {
      let hasNode = this.selectNodes.find((el) => el.data.typeCode !== 'RJ45')
      return hasNode === undefined
    },
    showBgType() {
      return this.curCel.shape === 'panel-node'
    },
    isVirtual() {
      return this.curCel.data.isVirtual
    },
    showIcon() {
      return ['device', 'topo'].includes(this.nodeType)
    },
    showNodeSize() {
      // if (this.size === 1) {
      //   return true
      // } else {
      //   let hasNode = this.selectNodes.find((el) => el.data.nodeType !== 'panel-node')
      //   return hasNode === undefined
      // }
      let hasNode = this.selectNodes.find((el) => el.data.nodeType !== 'panelNode')
      return hasNode === undefined
    },
    showElementSize() {
      let hasNode = this.selectNodes.find((el) => el.data.nodeType !== 'panelElement')
      return hasNode === undefined
    },
    batchOrNo() {
      let hasBatchNode = this.selectNodes.find((el) => el.data.batchId !== '')
      let batchId
      if (hasBatchNode) {
        batchId = hasBatchNode.data.batchId
      }
      let noBatchNode = this.selectNodes.find((el) => (batchId && el.data.batchId !== batchId) || !el.data.batchId)
      if (hasBatchNode && noBatchNode) {
        return false
      } else {
        return true
      }
    },
    showRadius() {
      return this.showNodeSize && this.curCel.shape !== 'circle-node'
    },
    showLabelPos() {
      let hasNode = this.selectNodes.find((el) => el.shape === 'networkGroupNode' || el.shape === 'text-block')
      return hasNode === undefined
    },
    groupSizeDisabled() {
      if (this.curCel && this.cellShape === 'networkGroupNode' && this.curCel.data.collapsed) {
        return true
      }
      return false
    },
  },
  watch: {
    size: {
      immediate: true,
      handler() {
        const { graph } = FlowGraph
        this.selectNodes = graph.getSelectedCells()
        if (this.size > 0) {
          if (this.curCel) {
            this.curCel.off('change:position')
          }
          this.curCel = this.selectNodes[this.size - 1]
          this.cellShape = this.curCel.shape
          this.nodeType = this.curCel.data.nodeType
          // console.log('选中的节点', this.cellShape, this.curCel);
          this.activeKey = '1'
          this.initNodeConfig()
          //监听选中节点位置移动
          this.curCel.on('change:position', (e) => {
            Object.assign(this.nodeConfig.position, this.curCel.position())
          })
        }
      },
    },
  },
  beforeDestroy() {
    if (this.curCel) {
      this.curCel.off('change:position')
    }
    this.curCel = null
    this.cellShape = ''
  },
  methods: {
    tabChange(key) {
      this.globalGridAttr.nodeActiveKey = this.activeKey;
      if(this.activeKey == 2){
        this.getPhysicalPortList()
      }
    },
    getPhysicalPortList() {
      const { graph } = FlowGraph;
      let nodes = graph.getNodes();
      nodes.forEach(el=>{
        if(el.data.portIndex && el.data.portIndex !== this.curCel.data.portIndex){
          this.bindIndexs.push(el.data.portIndex);
        }
      })
      if (this.globalGridAttr.physicalPortList.length > 0) {
        this.physicalPortList = this.globalGridAttr.physicalPortList.slice()
        return
      }
      let params = {
        productId: this.productId,
        connectStr: JSON.stringify(this.globalGridAttr.connectInfo),
      }
      this.spinning = true
      getAction('/net/device/getPhysicalPortList', params)
        .then((res) => {
          if (res.success && res.result) {
            this.physicalPortList = res.result
            this.globalGridAttr.physicalPortList = res.result
            // console.log('获取到的端口数据 === ', this.globalGridAttr.physicalPortList)
          }
          this.spinning = false
        })
        .catch((error) => {
          this.spinning = false
          console.log('获取失败', error)
        })
    },
    onVirtualChange(e) {
      this.curCel.data.virtualPorts = e
    },
    onPortDescChange(e) {
      let index = e === undefined ? '' : e
      let config = this.globalGridAttr.topoConfig
      let color = e === undefined ? config.unboundColor : config.boundColor
      let portInfo = this.physicalPortList.find((el) => el.index == e)
      this.bandwidth = portInfo ? portInfo.bandWidth : '';
      let portDesc = portInfo ? portInfo.portDesc : ''
      this.curCel.setData({ portIndex: index, iconColor: color, portDesc: portDesc,bandwidth: this.bandwidth })
    },
    reportChange(e) {
      this.selectNodes.forEach((el) => {
        el.setData({ isReport: e })
      })
    },
    onBandwidthChange(e) {
      console.log("dfdfdfd",e.target.value,this.bandwidth)
      this.selectNodes.forEach((el) => {
        el.setData({ bandwidth: this.bandwidth })
      })
    },
    onNodeDirection(e) {
      this.selectNodes.forEach((el) => {
        el.setData({ angle: e })
      })
    },
    onElementAngleChange(e) {
      this.selectNodes.forEach((el) => {
        //  el.rotate(e, { absolute: true })
        el.setData({ angle: e })
      })
    },
    onElementSizeChange(e) {
      let noBatchNode = this.selectNodes.find((el) => !el.data.batchId)
      if (noBatchNode === undefined) {
        const { graph } = FlowGraph
        let batchId = this.selectNodes[0].data.batchId
        let nodes = graph.getNodes()
        let batchNodes = nodes.filter((el) => el.data.batchId === batchId)
        let firstNode = batchNodes.find((el) => el.data.batchPos.isFirst)
        let pos
        let row
        if (firstNode) {
          pos = firstNode.position()
          row = firstNode.data.batchPos.row
        }
        batchNodes.forEach((el) => {
          el.resize(Number(e), Number(e))
          el.attr('foreignObject/fontSize', Number(e))
          if (pos) {
            let size = el.size()
            let batchPos = el.data.batchPos
            let { x, y } = batchPos
            if (row === 2) {
              el.position(pos.x + x * size.width, pos.y + y * size.height)
            } else {
              el.position(pos.x + x * size.width, pos.y + y * size.height + y * 10)
            }
          }
        })
        return
      }
      this.selectNodes.forEach((el) => {
        el.resize(Number(e), Number(e))
        el.attr('foreignObject/fontSize', Number(e))
      })
    },
    bgTypeChange(e) {
      let val = e.target.value
      this.nodeConfig.bgType = val
      if (val === 'color') {
        this.curCel.attr('image/xlink:href', '')
      } else if (val === 'picture') {
        let url = this.nodeConfig.bgImage ? this.downloadUrl + this.nodeConfig.bgImage : ''
        this.curCel.attr('image/xlink:href', url)
      }
      this.curCel.setData({ bgType: val })
    },
    bgImageChange(e) {
      this.curCel.attr('image/xlink:href', this.downloadUrl + e)
      this.curCel.setData({ bgImage: e })
    },
    bgColorChange(e) {
      this.curCel.attr('body/fill', e.target.value)
      this.curCel.setData({ bgColor: e.target.value })
    },
    //监听节点分值
    onNodeWeightChange() {
      this.curCel.data.nodeScore = this.nodeScore
    },
    //初始化节点配置内容
    initNodeConfig() {
      if (this.cellShape === 'text-block') {
        // 文字节点 内容 字体样式
        this.nodeConfig.label = this.curCel.attr('label/text')
        this.nodeConfig.labelSize = this.curCel.attr('label/style/fontSize')
        this.nodeConfig.labelColor = this.curCel.attr('label/style/color')
        this.nodeConfig.textNodeBorder = this.curCel.attrs.body.stroke === 'none'
      } else {
        // 文本样式
        this.nodeConfig.label = this.curCel.attr('label/text')
        this.nodeConfig.labelSize = this.curCel.attr('label/fontSize')
        this.nodeConfig.labelColor = this.curCel.attr('label/fill')
      }
      //群组节点按钮显示
      if (this.cellShape === 'networkGroupNode') {
        this.nodeConfig.groupButtonHide =
          this.curCel.attr('button/stroke') === 'none' &&
          this.curCel.attr('button/fill') === 'none' &&
          this.curCel.attr('buttonSign/stroke') === 'none'
      }
      //面板节点的背景色
      if (this.cellShape === 'panel-node') {
        this.nodeConfig.bgColor = this.curCel.attr('body/fill')
        this.nodeConfig.bgImage = this.curCel.data.bgImage
        this.nodeConfig.bgType = this.curCel.data.bgType
        if (this.nodeConfig.bgType === 'picture') {
          let url = this.nodeConfig.bgImage ? this.downloadUrl + this.nodeConfig.bgImage : ''
          this.curCel.attr('image/xlink:href', url)
        }
      }
      if (this.cellShape === 'panel-element') {
        this.nodeConfig.eleSize = this.curCel.size().width
        // this.nodeConfig.eleAngle = this.curCel.getAngle();
        // this.nodeConfig.eleAngle = this.curCel.data.angle;
        this.nodeConfig.direction = this.curCel.data.angle
        this.bandwidth = this.curCel.data.bandwidth
        this.portIndex = this.curCel.data.portIndex || undefined
        this.isReport = this.curCel.data.isReport || undefined
        this.virtualPorts = this.curCel.data.virtualPorts
      }
      //节点位置
      Object.assign(this.nodeConfig.position, this.curCel.position())
      //节点大小
      Object.assign(this.nodeConfig.size, this.curCel.size())
      if (this.showLabelPos) {
        let refX = this.curCel.attr('label/refX')
        let refY = this.curCel.attr('label/refY')
        this.nodeConfig.labelPos = this.posArr.findIndex((el) => el.refx === refX && el.refy === refY)
      }
      if (this.showIcon) {
        // console.log('设备节点', this.curCel)
        this.nodeImg = this.curCel.attr('image/xlink:href')
        if (this.curCel.getData().deviceCode) {
          getAction('/topo/device/info', { deviceCode: this.curCel.getData().deviceCode }).then((res) => {
            if (res.success) {
              // console.log('节点图片', this.curCel)
              this.imgList = res.result.icons
              this.nodeIp = res.result.ip
            }
          })
        } else if (this.curCel.getData().isVirtual && this.curCel.getData().productId) {
          //虚拟节点获取产品类的图标
          getAction('/product/product/list', {
            displayName: this.curCel.getData().productName,
            column: 'createTime',
            order: 'desc',
            field: 'id',
            pageNo: 1,
            pageSize: 8,
          }).then((res) => {
            if (res.success) {
              this.virtualData = res.result.records.find((el) => el.id === this.curCel.getData().productId)
              console.log('虚拟节点的信息 === ', this.virtualData)
              let icons = this.virtualData.icon.split(',')
              this.imgList = icons
            }
          })
        }
      }
      if (this.showRadius) {
        this.nodeConfig.radius.rx = this.curCel.attr('body/rx')
        this.nodeConfig.radius.ry = this.curCel.attr('body/ry')
      }
      // 节点的节点分值
      this.nodeScore = this.curCel.data.nodeScore || 0
    },
    //监听节点内容变化
    onLabelChange(e) {
      const val = e.target.value
      this.nodeConfig.label = val
      this.curCel.attr('label/text', val)
    },
    //文字大小变化
    onNodeFontSizeChange(val) {
      this.selectNodes.forEach((el) => {
        if (el.shape === 'text-block') {
          el.attr('label/style/fontSize', val)
        } else {
          el.attr('label/fontSize', val)
        }
      })
    },
    //文字颜色变化
    onLabelColorChange(e) {
      let val = e.target.value
      this.selectNodes.forEach((el) => {
        if (el.shape === 'text-block') {
          el.attr('label/style/color', val)
        } else {
          el.attr('label/fill', val)
        }
      })
    },
    // 文字位置变化
    onNodeLablePos(e) {
      let posArr = this.posArr[e]
      this.selectNodes.forEach((el) => {
        el.attr('label/refX', posArr.refx)
        el.attr('label/refY', posArr.refy)
        el.attr('label/textAnchor', posArr.anchor)
        el.attr('label/textVerticalAnchor', posArr.vAnchor)
      })
    },
    //节点位置变化
    onPositionChange(e, str) {
      const val = parseInt(e.target.value)
      let pos = this.nodeConfig.position
      this.curCel.position(Number(pos.x), Number(pos.y))
    },
    // 节点大小变化
    onSizeChange(e, str) {
      const val = parseInt(e)
      let size = this.nodeConfig.size
      this.selectNodes.forEach((el) => {
        if (el.shape === 'networkGroupNode') {
          el.setData({ nodeWidth: Number(size.width) })
          el.setData({ nodeHeight: Number(size.height) })
        }
        el.resize(Number(size.width), Number(size.height))
      })
    },
    onRadiusChange(e, str) {
      const val = parseInt(e)
      // console.log('圆角改变了', e)
      let size = this.nodeConfig.radius
      this.curCel.attr('body/' + str, val)
    },
    //隐藏群组按钮
    groupButtonChange(e) {
      if (this.globalGridAttr.groupButtonHide) {
        this.curCel.attr('button/stroke', 'none')
        this.curCel.attr('button/fill', 'none')
        this.curCel.attr('buttonSign/stroke', 'none')
      } else {
        this.curCel.attr('button/stroke', '#ccc')
        this.curCel.attr('button/fill', '#f5f5f5')
        this.curCel.attr('buttonSign/stroke', '#808080')
      }
    },
    //文字节点连线变化
    textBorderChange(e) {
      if (this.globalGridAttr.textNodeBorder) {
        this.curCel.attr('body/stroke', 'none')
      } else {
        this.curCel.attr('body/stroke', '#8c8c8c')
      }
    },
    //群组连线改变
    groupLineChange(e) {
      if (this.nodeConfig.groupLine === 'dashed') {
        this.curCel.attr('body/strokeDasharray', '10 4')
      } else {
        this.curCel.attr('body/strokeDasharray', '')
      }
    },
    //节点图标列表变化
    changeImgList(path, status) {
      this.imgList = path.split(',')
      let imgListLength = this.imgList.length
      let imgUrl = this.imgList[imgListLength - 1]
      this.nodeImg = imgUrl
      this.curCel.attr('image/xlink:href', imgUrl)
      if (status === 'removed') {
        this.updateIcon(path)
      } else {
        this.addIcon(imgUrl, path)
      }

      // this.imgList = path.split(',')
      // let imgListLength = this.imgList.length
      // let imgUrl = this.imgList[imgListLength - 1]
      // this.nodeImg = window._CONFIG['staticDomainURL'] + '/' + imgUrl
      // this.curCel.attr('image/xlink:href', window._CONFIG['staticDomainURL'] + '/' + imgUrl)
      // if (status === 'removed') {
      //   this.updateIcon(path)
      // } else {
      //   this.addIcon(imgUrl, path)
      // }
    },
    //选中图标
    changeImg(fileUrl) {
      this.nodeImg = fileUrl
      this.curCel.attr('image/xlink:href', fileUrl)
      // this.nodeImg = window._CONFIG['staticDomainURL'] + '/' + fileUrl
      // this.curCel.attr('image/xlink:href', window._CONFIG['staticDomainURL'] + '/' + fileUrl)
    },
    addIcon(fileUrl, path) {
      if (this.curCel && this.curCel.getData().deviceId) {
        httpAction(this.iconAdd, { deviceId: this.curCel.getData().deviceId, icon: fileUrl }, 'post').then((res) => {
          if (res.result) {
          }
        })
      } else if (this.curCel.getData().isVirtual && this.curCel.getData().productId) {
        if (this.virtualData) {
          this.virtualData.icon = path
        }
        this.updateProductIcons()
      }
    },
    updateIcon(fileUrl) {
      if (this.curCel.getData().isVirtual && this.curCel.getData().productId) {
        this.virtualData.icon = fileUrl
        this.updateProductIcons()
      } else {
        httpAction(
          this.iconUpdate,
          { deviceId: this.curCel ? this.curCel.getData().deviceId : '', icon: fileUrl },
          'post'
        ).then((res) => {
          if (res.result) {
          }
        })
      }
    },
    // 虚拟设备 更新产品类的图标
    updateProductIcons() {
      httpAction('/product/product/edit', this.virtualData, 'put').then((res) => {
        if (res.success) {
          console.log('产品更新成功 === ', res)
        }
      })
    },
  },
}
</script>

<style lang="less" scoped>
.ant-tabs {
  height: 100%;
}

.tab-con {
  height: 100%;
  width: 100%;
  overflow-y: auto;
  overflow-x: hidden;
}
.img-list {
  // height:380px;
  // overflow-y: auto;
  display: flex;
  justify-content: center;
}
::v-deep .ant-upload-list-item-info > span {
  display: flex;
  justify-content: center;
}
::v-deep .ant-upload-list-picture-card .ant-upload-list-item-thumbnail,
::v-deep .ant-upload-list-picture-card .ant-upload-list-item-thumbnail img {
  position: relative;
  display: flex;
  width: 90%;
  height: auto;
  left: 0px;
  top: 0px !important;
  align-items: center;
  justify-content: center;
}
.port-item {
  margin-bottom: 5px;
  display: flex;
  justify-content: space-around;
  span {
    width: 160px;
  }
}
::v-deep .ant-tabs-tabpane{
  height: 100%;
}
.pannel-node {
  .ant-spin-container{
    height: 100%;
  }
}
</style>
