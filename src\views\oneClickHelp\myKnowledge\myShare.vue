<template>
  <div style="margin-top:15px;">
    <!-- 查询区域 -->
    <div class='table-page-search-wrapper'>
      <a-form layout='inline'
        @keyup.enter.native='searchQuery'>
        <a-row :gutter='24' ref='row'>
          <a-col :span='6'>
            <a-form-item :label="'标\u3000题'">
              <a-input placeholder='请输入标题'
                v-model='queryParam.title'
                :allowClear='true'
                autocomplete='off' :maxLength="maxLength"/>
            </a-form-item>
          </a-col>
          <a-col :span='6'>
            <span class='table-page-search-submitButtons'
              :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
              <a-button type='primary'
                class='btn-search btn-search-style'
                @click='searchQuery'>查询</a-button>
              <a-button class='btn-reset btn-reset-style'
                @click='searchReset'>重置</a-button>
                <a-button v-if="selectedRowKeys.length > 0" class='btn-reset btn-reset-style'
                @click='batchDel'>删除</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <a-table ref="table"
      size='middle'
      :rowKey="(record)=>{return record.shareId}"
      :columns="columns"
      :dataSource="dataSource"
      :pagination="ipagination"
      :loading="loading"
      :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      class="j-table-force-nowrap"
      @change="handleTableChange"
      :showHeader="false"
      :bordered="false"
      :locale='locale'>
      <span slot="tooltip" slot-scope="text, record">
        <commonItem :type="'share'" :record="record" @handleDetailPage="handleDetailPage"></commonItem>
      </span>
      <span slot="action"
        slot-scope="text, record"
        class="caozuo">
        <span class="action-icon" @click="handleDetailPage(record)">
          <img src="../../../../public/oneClickHelp/look.png" alt />
        </span>
        <span class="action-icon" @click="handleSharing(record)" style="margin-left: 32px;position:relative;top:2px;">
          <a-icon type="share-alt"></a-icon>
        </span>
        <span class="action-icon" @click="handleDelete(record.shareId)" style="margin-left: 32px;">
          <img src="../../../../public/oneClickHelp/delete.png" alt />
        </span>
      </span>
      <span slot="shareStatus" slot-scope="text">
        <span :style='{color:text?"#ff0000":"#4BD863"}'>{{!!text?'已失效':'未失效'}}</span>
      </span>
    </a-table>
    <add-share-modal ref='addShareModal'></add-share-modal>
  </div>
</template>

<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
import addShareModal from '@views/oneClickHelp/knowledgeBase/modules/AddShareModal.vue'
import Empty from '@/components/oneClickHelp/Empty.vue'
import commonItem from './modules/commonItem.vue'
import { getAction, deleteAction  } from '@/api/manage'
export default {
  name: 'myShare',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components:{
    addShareModal,
    Empty,
    commonItem
  },
  data() {
    return {
      maxLength:50,
      locale: {
        emptyText: <Empty/>
      },
      columns: [
        {
          title: '标题',
          dataIndex: 'title',
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'tooltip'
          }
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 200,
          scopedSlots: {
            customRender: 'action'
          }
        },
        {
          title: '失效状态',
          dataIndex: 'shareExpired',
          align: 'center',
          width: 200,
          scopedSlots: {
            customRender: 'shareStatus'
          }
        },
      ],
      url: {
        list: '/kbase/knowledges/usershared',
        delete:'/kbase/knowledges/share/delete',
        deleteBatch:'/kbase/knowledges/share/deleteBatch'
      },
    }
  },
  activated() {
    console.log('激活我的分享')
    this.loadData()
  },
  methods: {
    // 查看
    handleDetailPage: function (record) {
      this.$emit('getRecord', record)
    },
    handleSharing(record){
      this.$refs.addShareModal.edit(record)
      this.$refs.addShareModal.title='知识分享'
      this.$refs.addShareModal.disableSubmit=false
    },
    handleDelete: function (id) {
      if (!this.url.delete) {
        this.$message.error('请设置url.delete属性!')
        return
      }
      var that = this
      this.$confirm({
          title: '确认删除',
          okText: '是',
          cancelText: '否',
          content: '是否删除选中数据?',
          class: 'oneClickHelpConfirmModal',
          onOk: function () {
            that.loading = true

            deleteAction(that.url.delete, { id: id }).then((res) => {
              if (res.success) {
                //重新计算分页问题
                that.reCalculatePage(1)
                that.$message.success(res.message)
                that.loadData()
              } else {
                that.$message.warning(res.message)
              }
            })
            .catch(() => {
              that.loading = false
            })
          }
        })
    },
    batchDel: function () {
      if (!this.url.deleteBatch) {
        this.$message.error('请设置url.deleteBatch属性!')
        return
      }
      if (this.selectedRowKeys.length <= 0) {
        this.$message.warning('请选择一条记录！')
        return
      } else {
        var ids = ''
        for (var a = 0; a < this.selectedRowKeys.length; a++) {
          ids += this.selectedRowKeys[a] + ','
        }
        var that = this
        this.$confirm({
          title: '确认删除',
          okText: '是',
          cancelText: '否',
          content: '是否删除选中数据?',
          class: 'oneClickHelpConfirmModal',
          onOk: function () {
            that.loading = true
            deleteAction(that.url.deleteBatch, { ids: ids })
              .then((res) => {
                if (res.success) {
                  //重新计算分页问题
                  that.reCalculatePage(that.selectedRowKeys.length)
                  that.$message.success(res.message)
                  that.loadData()
                  that.onClearSelected()
                } else {
                  that.$message.warning(res.message)
                }
              })
              .catch(() => {
                that.loading = false
              })
          }
        })
      }
    },
  },
}

</script>
<style lang='less' scoped>
@import '~@assets/less/onclickStyle.less';
@import './modules/myKnowledge.less';
</style>
