<template>
  <j-modal
    :centered='true'
    :confirmLoading="confirmLoading"
    :destroyOnClose='true'
    :maskClosable='true'
    :title="title"
    :visible='visible'
    :width="1300"
    cancelText="关闭"
    switch-fullscreen
    wrapClassName='limit-height-modal'
    @cancel="handleCancel"
    @ok='handleOk'
  >
    <a-row class='row'>
      <a-col :lg="5" :md='24' class='col'>
        <a-card class='card'>
          <a-tree
            :default-selected-keys="defaultSelectedkeys"
            :replaceFields="{children:'children', title:'title', key:'processDefinitionKey' }"
            :selectedKeys='selectedNodeKeys'
            :showIcon='false'
            :tree-data='treeData'
            style='user-select: none'
            @select="onSelect">
         </a-tree>
        </a-card>
      </a-col>
      <a-col :lg='19' :md="24" class='col'>
        <a-card class='card'>
          <div class='table-wrapper'>
            <div class='table-page-search-wrapper'>
              <a-form layout='inline' @keyup.enter.native='searchQuery'>
                <a-row ref='row' :gutter='16'>
                  <a-col :lg='9' :md='12' :sm='24'>
                    <a-form-item label='标题'  >
                      <a-input :maxLength='maxLength' :key='"title_"+processDefinitionKey' v-model='queryParam.title' :allowClear='true'
                               autocomplete='off' placeholder='请输入标题'/>
                    </a-form-item>
                  </a-col>
<!--                  <a-col :lg='9' :md='12' :sm='24'>
                    <a-form-item :key='"searchClassification_"+processDefinitionKey' label='类型'>
                      <a-select v-model="queryParam.searchClassification"  :allowClear='true' :getPopupContainer='(node) => node.parentNode' placeholder="请选择类型">
                        <a-select-option v-for="(item, index) in dictOptions" :key="'dict_'+index" :value="item.value">
                          {{ item.text}}
                        </a-select-option>
                      </a-select>
                    </a-form-item>
                  </a-col>-->
                  <a-col :lg='6' :md='12' :sm='24'>
                <span class='table-page-search-submitButtons'>
                  <a-button class='btn-search btn-search-style' type='primary' @click='searchQuery'>查询</a-button>
                  <a-button class='btn-reset btn-reset-style' @click='searchReset'>重置</a-button>
                </span>
                  </a-col>
                </a-row>
              </a-form>
            </div>
            <a-table
              ref='table'
              :columns='columns'
              :dataSource='dataSource'
              :loading='loading'
              :pagination='ipagination'
              :row-key='(record, index) => {return record.id}'
              :rowSelection='{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }'
              :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
              bordered
              @change='handleTableChange'>
            <span slot='action' slot-scope='text, record' class='caozuo'>
              <a @click="btnView(record.id)">查看</a>
            </span>
            </a-table>
          </div>
        </a-card>
      </a-col>
    </a-row>
    <!-- 表单区域 -->
    <process-instance-info-modal ref="processInstanceInfoModalForm" @ok="modalFormOk"></process-instance-info-modal>
  </j-modal>
</template>
<script>

import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { ajaxGetDictItems ,queryConfigureDictItems} from '@api/api'
import { getAction, postAction, postParamsAction } from '@api/manage'
import ProcessInstanceInfoModal from '@views/flowable/process-instance/modules/ProcessInstanceInfoModal'
export default {
  name: 'AddRelationListModal',
  mixins: [JeecgListMixin],
  components: { ProcessInstanceInfoModal },
  props: {
    treeData: {
      type: Array,
      required: false,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      maxLength:50,
      title: '操作',
      visible: false,
      confirmLoading: false,
      disableMixinCreated: true,
      knowledgeId: '',
      selectedNodeKeys: [],
      defaultSelectedkeys: [],
      processDefinitionKey: '',
      dictCode: '',
      dictOptions: [],
      columns: [
        {
          title: '编号',
          dataIndex: 'id',
        },
        {
          title: '标题',
          dataIndex: 'title',
        },
       /* {
          title: '类型',
          dataIndex: 'classification'
        },
        {
          title: '优先级',
          dataIndex: 'priority',
        },*/
        {
          title: '创建人员',
          dataIndex: 'startUser'
        },
        {
          title: '创建时间',
          dataIndex: 'startTime',
        },
        {
          title: '操作',
          dataIndex: 'action',
          customCell: () => {
            let cellStyle = 'text-align: center;width:100px'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'action'
          }
        }
      ],
      url: {
        list: '/kbase/relation',
      },
    }
  },
  methods: {
    init(knowledgeId) {
      this.visible = true
      this.$nextTick(() => {
        this.dictCode = this.treeData.length > 0 ? this.treeData[0].classification : ''
        this.initDictData(this.dictCode)
        this.knowledgeId = knowledgeId
        this.processDefinitionKey = this.treeData[0].processDefinitionKey
        this.defaultSelectedkeys = [this.processDefinitionKey]
        this.selectedNodeKeys = [this.processDefinitionKey]
        this.queryParam = {}
        this.queryParam.knowledgeId = knowledgeId
        this.queryParam.processDefinitionKey = this.treeData[0].processDefinitionKey
        this.ipagination.pageSize = 10
        this.ipagination.current = 1
        this.selectedRowKeys = []
        this.selectionRows = []
        this.loadData(1)
      })
    },
    close() {
      this.visible = false
      this.confirmLoading=false
    },
    handleOk() {
      if(this.confirmLoading===false){
        if (this.selectedRowKeys.length <= 0) {
          this.$message.warning('请至少选择一条记录')
          return
        }

        let param = {
          knowledgeId: this.queryParam.knowledgeId,
          processInstanceIds: this.selectedRowKeys.join(','),
          processDefinitionKey: this.queryParam.processDefinitionKey
        }
        this.confirmLoading = true
        postParamsAction(this.url.list, param).then((res) => {
          if (res.success) {
            this.$emit('ok', this.queryParam.processDefinitionKey)
            this.close()
          } else {
            this.$message.warning(res.message)
          }
          this.confirmLoading = false
        }).catch((err) => {
          this.$message.warning(err.message)
          this.confirmLoading = false
        })
      }
    },
    handleCancel() {
      this.close()
    },
    onSelect(e, option) {
      this.$nextTick(() => {
        if (e.length > 0 && this.selectedNodeKeys[0] !== e[0]) {
          this.selectedNodeKeys = e
          this.selectedRowKeys = []
          this.selectionRows = []
          this.processDefinitionKey = e[0]
          this.dictCode = option.node.dataRef.classification
          this.initDictData(this.dictCode)
          this.queryParam.processDefinitionKey = this.processDefinitionKey
          delete this.queryParam.searchClassification
          this.loadData(1)
        }
      })
    },
    searchReset() {
      this.selectedRowKeys = []
      this.selectionRows = []
      this.queryParam = {}
      this.queryParam.knowledgeId = this.knowledgeId
      this.queryParam.processDefinitionKey = this.processDefinitionKey
      this.$nextTick(() => {
        // delete this.queryParam.searchClassification
        this.loadData(1)
      })
    },
    btnView: function(id) {
      if (!id) {
        this.$message.error('流程实例ID不存在')
        return
      }
      this.$refs.processInstanceInfoModalForm.init(id, false);
      this.$refs.processInstanceInfoModalForm.title = "流程实例信息";
      this.$refs.processInstanceInfoModalForm.disableSubmit = false;
    },
    initDictData(dictCode) {
      if (dictCode && dictCode.length > 0) {
        ajaxGetDictItems(dictCode, null).then((res) => {
          if (res.success) {
            this.dictOptions = res.result ? res.result : []
          }
        })
      }
    }
  }
}
</script>
<style lang="less" scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/limitModalHeight.less';
::v-deep .ant-tree li span.ant-tree-switcher, .ant-tree li span.ant-tree-iconEle{
  display: none !important;
}

.row{
  background-color: #ececec;
  width: 100%;
}
@media (min-width: 968px) {
  .row{
    height: 100%;
    //max-width: inherit;
    overflow: auto !important;

    .col{
      padding: 10px;
      height: 100%;
      overflow: hidden !important;

      .card{
        height: 100%;
        overflow: auto !important;
      }
      &:nth-child(2){
        padding: 10px 10px 10px 0px;
      }
    }
  }
}
@media (max-width: 967px) {
  .row{
    height: auto;
    overflow: hidden !important;

    .col{
      padding: 10px 10px 0px;
      height: auto;
      overflow: hidden !important;

      .card{
        height: auto;
        overflow: hidden !important;
      }
      &:last-child{
        padding: 10px
      }
    }
  }
}
</style>