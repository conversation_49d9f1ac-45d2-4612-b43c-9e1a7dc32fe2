<template>
  <a-row style='height: 100%'
         class='vScroll'>
    <a-col style='width: 100%; height: 100%; display: flex; flex-direction: column'>
      <a-card :bordered='false'
              class='core'
              style='width: 100%; flex: auto'>
        <a-tabs :animated='false'
                :active-key='defaultActiveKey'
                @change='callback'>
          <a-tab-pane key='1' v-if='!isOneClickHelp'
                      tab='我的发布' forceRender>
          </a-tab-pane>
          <a-tab-pane key='2'
                      tab='我的收藏'>

          </a-tab-pane>
          <a-tab-pane key='3'
                      tab='我的评论'>
          </a-tab-pane>
          <a-tab-pane key='4'
                      tab='我的分享'>

          </a-tab-pane>
        </a-tabs>
        <my-deliver v-if='defaultActiveKey === "1"'  ref='myDeliver' @getRecord='changePage'></my-deliver>
        <my-collection v-else-if='defaultActiveKey === "2"' ref='myCollection' @getRecord='changePage'></my-collection>
        <my-comments v-else-if='defaultActiveKey === "3"' ref='myComments' @getRecord='changePage'></my-comments>
        <my-share v-else-if='defaultActiveKey === "4"' ref='myShare' @getRecord='changePage'></my-share>
      </a-card>
    </a-col>
  </a-row>
</template>

<script>
import myDeliver from './myDeliver'
import myComments from './myComments'
import myCollection from './myCollection'
import myShare from './myShare'
import { getQueryStringRegExp } from '@/utils/util'

export default {
  name: 'myKnowledgeList',
  components: {
    myDeliver,
    myComments,
    myCollection,
    myShare
  },
  data() {
    return {
      defaultActiveKey: '1',
      isOneClickHelp: false
    }
  },
  created() {
    let hostName = window.location.href
    if (hostName.includes('oneClickHelp')) {
      this.isOneClickHelp = true
      this.defaultActiveKey = '2'
    }
  },
  methods: {
    callback(key) {
      let that = this
      that.defaultActiveKey = key
    },
    // 获取父组件的方法,切换组件
    changePage(record) {
      this.$parent.pButton2(1, record)
    }
  }
}
</script>
<style scoped></style>