<template>
  <a-card :bordered="false" style="width: 100%; flex: auto">
    <div>
      <div class="colorBox">
        <span class="colorTotal">设备详情</span>
      </div>
      <div style="position: absolute; right: 24px; top: 18px; z-index: 10">
        <img
          src="~@assets/return1.png"
          @click="getGo"
          style="width: 20px; height: 20px; cursor: pointer"
        />
      </div>
    </div>
    <a-card :bordered='false' class='card2' :bodyStyle="{ padding: '0'}">
      <div class='box config'>
        <a-descriptions :column='{ xxl: 2, xl: 2, lg: 2, md: 2, sm: 2, xs: 2 }' bordered>
          <a-descriptions-item label='设备名称'>{{ data.deviceName }}</a-descriptions-item>
          <a-descriptions-item label='预约开始时间'>{{ data.startTime }}</a-descriptions-item>
          <a-descriptions-item label='预约结束时间'>{{ data.endTime }}</a-descriptions-item>
          <a-descriptions-item label='申请人'>{{ data.applyUser }}</a-descriptions-item>
          <a-descriptions-item label='使用时长'>{{ data.duration }}</a-descriptions-item>
          <a-descriptions-item label='审核状态'>{{ data.statusText}}</a-descriptions-item>
          <a-descriptions-item label='支撑项目'>{{  projects.find((item) => item.value === data.supportingProject).label}}</a-descriptions-item>
        </a-descriptions>
      </div>
    </a-card>
  </a-card>

</template>
<script>
import { deviceTypes, projects } from '../modules/projects'

export default {
  name: 'appointmentApprovalDetails',
  props: {
    data: {
      type: Object,
      defqault: () => {
        return {}
      }
    }
  },
  data() {
    return {
      deviceTypeList: deviceTypes,
      projects,
    }
  },
  created() {
    console.log("设备锡尼希 === >", this.data)
  },
  methods:{
    //返回上一级
    getGo() {
      this.$parent.pButton2(0)
    }
  }
}
</script>

<style scoped lang='less'>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
.colorBox {
  margin-top: 10px;
  margin-bottom: 10px;
  .colorTotal {
    padding-left: 7px;
    border-left: 4px solid #1e3674;
  }
}
</style>