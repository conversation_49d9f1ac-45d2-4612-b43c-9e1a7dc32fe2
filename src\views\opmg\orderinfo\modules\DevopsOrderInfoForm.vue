<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-item label="工单名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['orderName', validatorRules.orderName]" :allowClear="true" autoComplete="off"
                placeholder="请输入工单名称"></a-input>
            </a-form-item>
          </a-col>

          <a-col :span="24">
            <a-form-item label="故障类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag type="list" v-decorator="['orderCategoryId', validatorRules.orderCategoryId]"
                :trigger-change="true" dictCode="fault_type" placeholder="请选择故障类型" @change="handleChange" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="故障时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date placeholder="请选择故障时间" v-decorator="['wamingCreateTime']" :show-time="true"
                date-format="YYYY-MM-DD HH:mm:ss" :trigger-change="true" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="故障描述" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-textarea placeholder="请输入故障描述" :auto-size="{ minRows: 2, maxRows: 6 }"
                v-decorator="['orderDescription', validatorRules.orderDescription]" />
            </a-form-item>
          </a-col>

          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>
<script>
  import {
    httpAction,
    getAction
  } from '@/api/manage'
  import pick from 'lodash.pick'
  import {
    validateDuplicateValue
  } from '@/utils/util'
  import JFormContainer from '@/components/jeecg/JFormContainer'
  import JDate from '@/components/jeecg/JDate'
  import JTreeSelect from '@/components/jeecg/JTreeSelect'
  import JDictSelectTag from '@/components/dict/JDictSelectTag'

  export default {
    name: 'DevopsOrderInfoForm',
    components: {
      JFormContainer,
      JDate,
      JTreeSelect,
      JDictSelectTag,
    },
    props: {
      //流程表单data
      formData: {
        type: Object,
        default: () => {},
        required: false,
      },
      //表单模式：true流程表单 false普通表单
      formBpm: {
        type: Boolean,
        default: false,
        required: false,
      },
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false,
      },
    },
    data() {
      return {
        form: this.$form.createForm(this),
        model: {},
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 7
          },
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 14
          },
        },
        confirmLoading: false,
        condition: '{"delflag": 0}',
        validatorRules: {
          orderName: {
            rules: [{
              required: true,
              message: '工单名称长度应在2-20位!',
              min: 2,
              max: 20
            }],
          },
          orderCategoryId: {
            rules: [{
              required: true,
              message: '请选择故障类型!'
            }],
          },
          orderDescription: {
            rules: [{
              min: 0,
              max: 100,
              message: '故障描述应在100字符之内'
            }],
          },
        },
        url: {
          add: '/orderinfo/devopsOrderInfo/add',
          edit: '/orderinfo/devopsOrderInfo/edit',
          queryById: '/orderinfo/devopsOrderInfo/queryById',
        },
      }
    },
    computed: {
      formDisabled() {
        if (this.formBpm === true) {
          if (this.formData.disabled === false) {
            return false
          }
          return true
        }
        return this.disabled
      },
      showFlowSubmitButton() {
        if (this.formBpm === true) {
          if (this.formData.disabled === false) {
            return true
          }
        }
        return false
      },
    },
    created() {
      //如果是流程中表单，则需要加载流程表单data
      this.showFlowData()
    },
    methods: {
      handleChange() {},
      add() {
        this.edit({})
      },
      edit(record) {
        this.form.resetFields()
        this.model = Object.assign({}, record)
        this.visible = true
        this.$nextTick(() => {
          this.form.setFieldsValue(
            pick(
              this.model,
              'orderCategoryId',
              'warningId',
              'confirmUserId',
              'orderDescription',
              'orderState',
              'wamingCreateTime',
              'remarks',
              'handlerUserId',
              'allocTime',
              'responseSecond',
              'handlerResults',
              'handleEndTime',
              'handleSecond',
              'createBy',
              'createTime',
              'updateBy',
              'updateTime',
              'sysOrgCode'
            )
          )
        })
      },
      //渲染流程表单数据
      showFlowData() {
        if (this.formBpm === true) {
          let params = {
            id: this.formData.dataId
          }
          getAction(this.url.queryById, params).then((res) => {
            if (res.success) {
              this.edit(res.result)
            }
          })
        }
      },
      submitForm() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.model.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'put'
            }
            let formData = Object.assign(this.model, values)
            httpAction(httpurl, formData, method)
              .then((res) => {
                if (res.success) {
                  that.$message.success(res.message)
                  that.$emit('ok')
                } else {
                  that.$message.warning(res.message)
                }
              })
              .finally(() => {
                that.confirmLoading = false
              })
          }
        })
      },
      popupCallback(row) {
        this.form.setFieldsValue(
          pick(
            row,
            'orderCategoryId',
            'orderDescription',
            'warningId',
            'confirmUserId',
            'orderState',
            'wamingCreateTime',
            'remarks',
            'handlerUserId',
            'allocTime',
            'responseSecond',
            'handlerResults',
            'handleEndTime',
            'handleSecond',
            'createBy',
            'createTime',
            'updateBy',
            'updateTime',
            'sysOrgCode'
          )
        )
      },
    },
  }
</script>