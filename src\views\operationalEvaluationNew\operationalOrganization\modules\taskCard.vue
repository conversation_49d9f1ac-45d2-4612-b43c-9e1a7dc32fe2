<template>
  <div>
    <div class="task-card">
      <div class="title">
        <span class="name">{{ nodeInfo.title }}</span>
        <a-tag class="tag" :color="statusConfig.color">{{ statusConfig.text }}</a-tag>
      </div>
      <img src="@/assets/return1.png" alt="" @click="getGo" class="img" />
    </div>
    <a-row v-if="nodeInfo.type==='project'" :gutter="16">
      <a-col v-if="nodeInfo.info.sender" :xs="24" :sm="24" :md="24" :lg="12" :xl="6" :xxl="3">
        <div class="col-div" :title="'发起人：'+nodeInfo.info.sender">发起人：{{ nodeInfo.info.sender }}</div>
      </a-col>
      <a-col v-if="nodeInfo.info.createTime" :xs="24" :sm="24" :md="24" :lg="12" :xl="8" :xxl="5">
        <div class="col-div" :title="'创建时间：'+nodeInfo.info.createTime">创建时间：{{ nodeInfo.info.createTime }}</div>
      </a-col>
      <a-col v-if="nodeInfo.info.startTime" :xs="24" :sm="24" :md="24" :lg="12" :xl="10" :xxl="8">
        <div class="col-div" :title="'评估周期：'+nodeInfo.info.startTime+' - '+nodeInfo.info.endTime">
          评估周期：{{ nodeInfo.info.startTime }} - {{ nodeInfo.info.endTime }}
        </div>
      </a-col>

      <a-col :xs="24" :sm="24" :md="24" :lg="12" :xl="24" :xxl="5">
        <div class="col-div">
          <span>评估结果：</span>
          <span v-if="!projectResEdiStatus">
                {{ nodeInfo.result || '--' }}
              <span v-if="taskInfo.info.status==2" v-has="'reportResult:edit'" style="margin-left: 8px; color: #409eff; cursor: pointer"
                    @click="editResult('project','projectResult')">
                <a-icon type="edit" style="margin-right: 6px" />编辑
              </span>
            </span>
          <a-input-group v-else-if="taskInfo.info.status==2&&projectResEdiStatus" compact
                         style='display: inline-block;line-height:32px !important;'>
            <a-select
              v-model='projectResult'
              style="width: 100px"
              placeholder='请选择'
            >
              <a-select-option v-for="item in resultData" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
            <a-button icon='check' @click="changeProjectResult"></a-button>
            <a-button icon='close' @click="closeEditResult('project')"></a-button>
          </a-input-group>
        </div>
      </a-col>
      <a-col :span="24" v-if="nodeInfo.metricsTypeResultObj">
        <a-row>
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12" :xxl="6">
            <div class="col-div" :title="'等级A的类别数量：'+nodeInfo.metricsTypeResultObj.aNum">等级A的类别数量：{{ nodeInfo.metricsTypeResultObj.aNum}}</div>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12" :xxl="6">
            <div class="col-div" :title="'等级B的类别数量：'+nodeInfo.metricsTypeResultObj.bNum">等级B的类别数量：{{ nodeInfo.metricsTypeResultObj.bNum}}</div>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12" :xxl="6">
            <div class="col-div" :title="'等级C的类别数量：'+nodeInfo.metricsTypeResultObj.cNum">等级C的类别数量：{{ nodeInfo.metricsTypeResultObj.cNum}}</div>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12" :xxl="6">
            <div class="col-div" :title="'等级D的类别数量：'+nodeInfo.metricsTypeResultObj.dNum">等级D的类别数量：{{ nodeInfo.metricsTypeResultObj.dNum}}</div>
          </a-col>
        </a-row>
      </a-col>
    </a-row>

    <a-row v-if="nodeInfo.type==='category'" :gutter="16">
      <a-col :xs="24" :sm="24" :md="nodeInfo.info.typeDesc?24:12" :lg="nodeInfo.info.typeDesc?8:12"
             :xl="nodeInfo.info.typeDesc?6:12" :xxl="nodeInfo.info.typeDesc?4:6">
        <div class="col-div" :title="'是否重点关注：'+(nodeInfo.info.isFocus=='1'?'是':'否')">
          是否重点关注：{{ nodeInfo.info.isFocus == '1' ? '是' : '否' }}
        </div>
      </a-col>
      <a-col v-if="nodeInfo.info.typeDesc" :xs="24" :sm="24" :md="24" :lg="16" :xl="18" :xxl="20">
        <div class="col-div" :title="'描述：'+nodeInfo.info.typeDesc">描述：{{ nodeInfo.info.typeDesc }}</div>
      </a-col>
      <a-col :xs="24" :sm="24" :md="nodeInfo.info.typeDesc?24:12"
             :lg="nodeInfo.info.typeDesc?24:12" :xl="nodeInfo.info.typeDesc?24:12" :xxl="nodeInfo.info.typeDesc?24:6">
        <div class="col-div">
          <span>评估结果：</span>
          <span v-if="!metricsTypeResEdiStatus">
            {{ nodeInfo.result || '--' }}
            <span v-if="taskInfo.info.status==2" v-has="'reportResult:edit'" style="margin-left: 8px; color: #409eff; cursor: pointer"
                    @click="editResult('metricsType','evaluateResult')">
              <a-icon type="edit" style="margin-right: 6px" />编辑
            </span>
          </span>
          <a-input-group v-else-if="taskInfo.info.status==2&&metricsTypeResEdiStatus" compact style='display: inline-block;line-height:32px !important;'>
            <a-select
              v-model='metricsTypeResult'
              style="width: 100px"
              placeholder='请选择'
            >
              <a-select-option v-for="item in resultData" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
            <a-button icon='check' @click="changeResultAboutMetric('metricsType')"></a-button>
            <a-button icon='close' @click="closeEditResult('metricsType')"></a-button>
          </a-input-group>
        </div>
      </a-col>
      <a-col :span="24" v-if="catEvaluateResult">
        <a-row>
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12" :xxl="6">
            <div class="col-div" :title="'等级A的指标数量：'+catEvaluateResult.aNum">等级A的指标数量：{{ catEvaluateResult.aNum}}</div>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12" :xxl="6">
            <div class="col-div" :title="'等级B的指标数量：'+catEvaluateResult.bNum">等级B的指标数量：{{ catEvaluateResult.bNum}}</div>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12" :xxl="6">
            <div class="col-div" :title="'等级C的指标数量：'+catEvaluateResult.cNum">等级C的指标数量：{{ catEvaluateResult.cNum}}</div>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12" :xxl="6">
            <div class="col-div" :title="'等级D的指标数量：'+catEvaluateResult.dNum">等级D的指标数量：{{ catEvaluateResult.dNum}}</div>
          </a-col>
        </a-row>
      </a-col>
    </a-row>

    <a-row v-if="nodeInfo.type==='metric'" :gutter="16">
      <a-col v-if="nodeInfo.info.metricsCode" :xs="24" :sm="24" :md="24" :lg="12" :xl="12" :xxl="12">
        <div class="col-div" :title="'指标标识：'+nodeInfo.info.metricsCode">指标标识：{{ nodeInfo.info.metricsCode }}
        </div>
      </a-col>
      <a-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12" :xxl="12">
        <div class="col-div" :title="'是否重点关注：'+(nodeInfo.info.isFocus=='1'?'是':'否')">
          是否重点关注：{{ nodeInfo.info.isFocus == '1' ? '是' : '否' }}
        </div>
      </a-col>
      <a-col v-if="nodeInfo.info.investigatePoint" :xs="24" :sm="24" :md="24" :lg="12" :xl="12" :xxl="12">
        <div class="col-div" :title="'考察要点：'+nodeInfo.info.investigatePoint">
          考察要点：{{ nodeInfo.info.investigatePoint }}
        </div>
      </a-col>
      <a-col v-if="nodeInfo.info.monitorPoint" :xs="24" :sm="24" :md="24" :lg="12" :xl="12" :xxl="12">
        <div class="col-div" :title="'监控要点：'+nodeInfo.info.monitorPoint">监控要点：{{ nodeInfo.info.monitorPoint }}
        </div>
      </a-col>
      <a-col v-if="nodeInfo.info.localCheckPoint" :xs="24" :sm="24" :md="24" :lg="12" :xl="12" :xxl="12">
        <div class="col-div" :title="'现地核查要点：'+nodeInfo.info.localCheckPoint">
          现地核查要点：{{ nodeInfo.info.localCheckPoint }}
        </div>
      </a-col>
      <a-col v-if="nodeInfo.info.metricsDesc" :xs="24" :sm="24" :md="24" :lg="12" :xl="12" :xxl="12">
        <div class="col-div" :title="'指标备注：'+nodeInfo.info.metricsDesc">指标备注：{{ nodeInfo.info.metricsDesc }}
        </div>
      </a-col>
      <a-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" :xxl="24">
        <div class="col-div">
          <span>评估结果：</span>
          <span v-if="!metricsResEdiStatus">
            {{ nodeInfo.result || '--' }}
            <span v-if="taskInfo.info.status==2" v-has="'reportResult:edit'" style="margin-left: 8px; color: #409eff; cursor: pointer"
                  @click="editResult('metrics','evaluateResult')">
              <a-icon type="edit" style="margin-right: 6px" />编辑
            </span>
          </span>
          <a-input-group v-else-if="taskInfo.info.status==2&&metricsResEdiStatus" compact
                         style='display: inline-block;line-height:32px !important;'>
            <a-select
              v-model='metricsResult'
              style="width: 100px"
              placeholder='请选择'
            >
              <a-select-option v-for="item in resultData" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
            <a-button icon='check' @click="changeResultAboutMetric('metrics')"></a-button>
            <a-button icon='close' @click="closeEditResult('metrics')"></a-button>
          </a-input-group>
        </div>
      </a-col>
    </a-row>
  </div>
</template>
<script>
import { getStatusConfig } from '@views/operationalEvaluationNew/modules/statusConfig'
import { putAction } from '@api/manage'

export default {
  name: '',
  props: {
    taskInfo: {
      type: Object,
      required: false,
      default: () => {
      }
    },
    nodeInfo: {
      type: Object,
      required: false,
      default: () => {
      }
    },
    catEvaluateResult: {
      type: Object,
      required: false,
      default: () => {
      }
    }
  },
  computed: {
    statusConfig() {
      return getStatusConfig(this.taskInfo.previewStatus)
    }
  },
  data() {
    return {
      resultData: [
        {
          value: 'A',
          label: 'A'
        },
        {
          value: 'B',
          label: 'B'
        },
        {
          value: 'C',
          label: 'C'
        },
        {
          value: 'D',
          label: 'D'
        }
      ],
      projectResEdiStatus: false,
      projectResult: undefined,

      metricsTypeResEdiStatus: false,
      metricsTypeResult: undefined,

      metricsResEdiStatus: false,
      metricsResult: undefined,
      url: {
        projectResultEdit: '/devops/projectInfo/edit',
        metricResultEdit: '/evaluate/metricsType/editEvaluateResult'
      }
    }
  },
  methods: {
    //返回上一级
    getGo() {
      this.$emit('getGo')
    },
    editResult(type, key) {
      this[type+"Result"] = this.nodeInfo.result || undefined
      this[type + 'ResEdiStatus'] = true
    },
    changeProjectResult() {
      this.$emit('startEditing')
      let data = {
        id: this.nodeInfo.id,
        projectResult: this.projectResult
      }
      putAction(this.url.projectResultEdit, data).then((res) => {
        if (res.success) {
          this.$message.success(res.message)
          this.$emit('completeEditing')
        } else {
          this.$message.warning(res.message)
        }
        this.projectResEdiStatus = false
      }).catch((err) => {
        this.$message.warning(err.message)
        this.projectResEdiStatus = false
      })
    },
    changeResultAboutMetric(type) {
      this.$emit('startEditing')
      let data = {
        projectId: this.taskInfo.id,
        evaluateResult:this[type + 'Result']
      }
      data[type + 'Id'] = this.nodeInfo.id

      putAction(this.url.metricResultEdit, data).then((res) => {
        if (res.success) {
          this.$message.success(res.message)
        } else {
          this.$message.warning(res.message)
        }
        this[type + 'ResEdiStatus'] = false
        this.$emit('completeEditing')
      }).catch((err) => {
        this.$message.warning(err.message)
        this[type + 'ResEdiStatus'] = false
      })
    },
    closeEditResult(type) {
      this[type + 'ResEdiStatus'] = false
    }
  }
}
</script>

<style scoped lang="less">
.task-card {
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;

  .title {
    width: calc(100% - 20px - 16px);
    display: flex;
    align-items: center;
    justify-content: start;
    flex-flow: row nowrap;

    .name {
      overflow: hidden;
      white-space: nowrap;
      word-break: keep-all;
      text-overflow: ellipsis;
      font-size: 18px;
      color: #000;
      // display: flex;
      // align-items: center;
    }

    .tag {
      margin-left: 15px;
    }
  }

  .img {
    width: 20px;
    height: 20px;
    cursor: pointer
  }
}

.col-div {
  overflow: hidden;
  white-space: nowrap;
  word-break: keep-all;
  text-overflow: ellipsis;
  padding: 4px 0;
}
</style>
