<template>
  <j-modal
    :confirmLoading='confirmLoading'
    :maskClosable='false'
    switchFullscreen
    :visible='visible'
    :width='800'
    cancelText='关闭'
    title='设置'
    destroyOnClose
    @cancel='handleCancel'
    @ok='handleSubmit'
  >
    <div v-if='visible'>
      <div class='quick-menu-selected'>
        <div v-if='selectedNodes.length > 0'>
          <a-tag v-for='item in selectedNodes' :key='item.id' color="cyan" closable @close='delQuickMenu(item)'>
            {{ item.name }}
          </a-tag>
        </div>
        <div v-else>按照需要设置快捷入口，最多六个！</div>
      </div>
      <div style='height: 60vh;overflow-y: auto'>
        <a-tree
          :checkedKeys='selectedKeys'
          :replace-fields='replaceFields'
          :tree-data='menuList'
          :defaultExpandedKeys='defaultExpandedKeys'
          checkable
          @check='onCheck'
        >
          <div slot='checkable' slot-scope='{checkable,title,id,path}' class='custom-tree-node'></div>
        </a-tree>
      </div>

    </div>
  </j-modal>
</template>

<script>
import {getAction} from '@api/manage'

export default {
  name: 'quickMenu',
  props: {
    menuList:{
      type:Array,
      default:()=>[],
    }
  },
  data() {
    return {
      visible: false,
      confirmLoading: false,
      replaceFields: { key: 'id', value: 'id' },
      selectedNodes: [],
      selectedKeys: [],
      defaultExpandedKeys:[],
    }
  },
  created() {

  },
  watch:{
    selectedNodes(){
      this.$emit("selectedLen",this.selectedNodes.length >= 6?1:0)
    }
  },
  methods: {
    show(nodes) {
      if (nodes && nodes.length > 0) {
        this.selectedKeys = []
        this.selectedNodes = []
        for (let i = 0; i < nodes.length; i++) {
          this.selectedKeys.push(nodes[i].id)
          this.selectedNodes.push(nodes[i])
        }
      }
      this.defaultExpandedKeys = [this.menuList[0].id]
      if(this.menuList[0].children && this.menuList[0].children.length > 0){
        this.defaultExpandedKeys.push(this.menuList[0].children[0].id)
      }
      this.visible = true;
    },
    onCheck(checkedKeys, e) {
      this.selectedKeys = checkedKeys
      if (e.checked) {
        this.selectedNodes.push(e.node.$options.propsData.dataRef)
      } else {
        let newList = this.selectedNodes.filter((item, index) => {
          if (checkedKeys.includes(item.id)) {
            return true
          }
          return false
        })
        this.selectedNodes = newList
      }
    },
    //获取用户菜单信息，并处理成树结构数据

    handleClick(event) {
      event.stopPropagation()
    },

    handleSubmit() {
      this.$emit('ok', this.selectedNodes)
      this.handleClear()
    },
    handleCancel() {
      this.handleClear()
    },
    handleClear() {
      this.visible = false
    },
    delQuickMenu(menu){
      let mIdx = this.selectedNodes.findIndex(el=>el.id === menu.id);
      if(mIdx != -1){
        this.selectedNodes.splice(mIdx,1)
      };
      let idx = this.selectedKeys.findIndex(el=>el === menu.id);
      if(idx != -1){
        this.selectedKeys.splice(idx,1)
      }
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/YQNormalModal.less';
.quick-menu-selected{
  padding:8px;
}
</style>