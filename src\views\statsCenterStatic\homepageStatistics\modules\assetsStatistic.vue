<template>
  <card-frame :title='"终端国产化趋势分析"' :sub-title='""'>
    <div slot='bodySlot' id='home-assets-statistic'>
     <div class='action'>
       <span class='left-btn' >
         <span
           class='btn'
           :class='selectedLBtnKey==index?"btn-active":""'
           v-for='(item,index) in leftBtnArr[selectedRBtnKey]'
           :key='"leftBtnArr_"+index'
           @click='changeTime(index)'>
           {{item.description}}
         </span>
       </span>
       <span class='right-btn'>
         <span>单位:</span>
         <span
           class='btn'
           :class='selectedRBtnKey==index?"btn-active":""'
           v-for='(item,index) in rightBtrArr'
           :key='"rightBtrArr_"+index'
           @click='changeUnit(index)'>
           {{item}}
         </span>
       </span>
     </div>
      <div class='empty-wrapper' v-if='Object.keys(staData).length===0'>
        <a-spin :spinning='loading' v-if='loading' class='spin'></a-spin>
        <a-list :data-source='[]' v-else />
      </div>
      <line-trend-chart
        class='trend-chart'
        :color-list='colorList'
        :chart-data='staData.chartData'
        :x-data='staData.xData'
        :legend-data='staData.legendData'
        :show-legend='false'
        :unit='"%"' v-else>
      </line-trend-chart>
    </div>
  </card-frame>
</template>
<script>
import { getAction } from '@api/manage'
import cardFrame from '@views/statsCenter/com/cardFrame.vue'
import FoldLine from '@views/statsCenter/alarmAnalysis/modules/foldLine.vue'
import lineTrendChart from '@views/statsCenter/com/lineTrendChart.vue'
import moment from 'moment'
export default {
  name: "assetsStatistic",
  components: { FoldLine, cardFrame, lineTrendChart },
  props: {
    adcode: {
      type: String,
      required: false,
      default: ''
    },
    handleRefresh: {
      type: Number,
      required: false,
      default: 0,
    }
  },
  data() {
    return {
      loading: false,
      selectedLBtnKey: 0,
      selectedRBtnKey: 0,
      leftBtnArr: [
        [{
          description: '近7天',
          startTime: moment().startOf('day').subtract(1, 'weeks').format('YYYY-MM-DD HH:mm:ss'),
          endTime: moment().endOf('day').subtract(1, 'days').format('YYYY-MM-DD HH:mm:ss'),
        },
          {
            description: '近30天',
            startTime: moment().startOf('day').subtract(30, 'days').format('YYYY-MM-DD HH:mm:ss'),
            endTime: moment().endOf('day').subtract(1, 'days').format('YYYY-MM-DD HH:mm:ss'),
          }],
        [{
          description: '近半年',
          startTime: moment().startOf('month').subtract(5, 'months').format('YYYY-MM'),
          endTime: moment().endOf('month').subtract(0, 'months').format('YYYY-MM'),
        },
          {
            description: '近一年',
            startTime: moment().startOf('month').subtract(11, 'months').format('YYYY-MM'),
            endTime: moment().endOf('month').subtract(0, 'months').format('YYYY-MM'),
          }],
      ],
      rightBtrArr: ['天', '月'],

      colorList: [
        {
          start: 'rgba(27, 150, 255, 0.67)',
          end: 'rgba(3,255,255,0)'
        },
        {
          start: '#03FFFF',
          end: 'rgba(3,255,255,0)'
        },
        {
          start: '#36FF89',
          end: 'rgba(54,255,137,0)'
        }],
      staData: {},
      url: {
        trendUrl: '/data-analysis/index/overviewNationalByCity',
      }
    }
  },
  watch: {
    adcode: {
      handler(nVal, oVal) {
        //this.getLocalizationTate(nVal)
        this.getMockJson()
      },
      deep: true,
      immediate: true
    },
    handleRefresh: {
      handler(nVal, oVal) {
        //this.getLocalizationTate(this.adcode)
        this.getMockJson()
      }
    }
  },
  methods: {
    changeUnit(index) {
      this.selectedRBtnKey = index
      this.selectedLBtnKey = 0
      //this.getLocalizationTate(this.adcode)
    },
    changeTime(index) {
      this.selectedLBtnKey = index
      //this.getLocalizationTate(this.adcode)
    },

    //趋势分析折线图数据
    getLocalizationTate(adcode) {
      this.staData = {}
      this.loading = true
      let st = this.leftBtnArr[this.selectedRBtnKey][this.selectedLBtnKey].startTime
      let et = this.leftBtnArr[this.selectedRBtnKey][this.selectedLBtnKey].endTime
      let flag = this.selectedRBtnKey === 0 ? 'day' : 'month'

      getAction(this.url.trendUrl, {
        cityId: adcode, startTime: st, endTime: et, flag: flag
      }).then((res) => {
        this.staData = {}
        if (res.success) {
          if (res.result && res.result.length > 0) {
            this.staData = {
              legendData: ['国产化率'],
              xData: [],
              chartData: [{ name: '国产化率', data: [] }]
            }
            let xValues = []
            let values = []
            let hasValue = false
            for (let i = 0; i < res.result.length; i++) {
              let item = res.result[i]
              let len = flag === 'day' ? 10 : 7
              xValues.push(item.createTime.slice(0, len))
              values.push(item.nationalRate)
              if (item.nationalRate != null && item.nationalRate != undefined && item.nationalRate != '') {
                hasValue = true
              }
            }
            if (hasValue) {
              this.staData.xData = xValues
              this.staData.chartData[0].data = values
            } else {
              this.staData = {}
            }
          }
        } else {
          this.$message.warning(res.message)
        }
        this.loading = false
      }).catch((err) => {
        this.$message.warning(err.message)
        this.loading = false
      })
    },
    getMockJson(){
      this.staData = {}
      this.loading = true
      getAction(location.origin+"/statsCenter/mock/homeData.json").then((res) => {
        if(res){
          let st = this.leftBtnArr[this.selectedRBtnKey][this.selectedLBtnKey].startTime
          let et = this.leftBtnArr[this.selectedRBtnKey][this.selectedLBtnKey].endTime
          let flag = this.selectedRBtnKey === 0 ? 'day' : 'month'
          let result=res.assetsStatisticData
          if (result && result.length > 0) {
            this.staData = {
              legendData: ['国产化率'],
              xData: [],
              chartData: [{ name: '国产化率', data: [] }]
            }
            let xValues = []
            let values = []
            let hasValue = false
            for (let i = 0; i < result.length; i++) {
              let item = result[i]
              let len = flag === 'day' ? 10 : 7
              xValues.push(item.createTime.slice(0, len))
              values.push(item.nationalRate)
              if (item.nationalRate != null && item.nationalRate != undefined && item.nationalRate != '') {
                hasValue = true
              }
            }
            if (hasValue) {
              this.staData.xData = xValues
              this.staData.chartData[0].data = values
            } else {
              this.staData = {}
            }
          }
        }
        this.loading = false
      }).catch((err)=>{
        this.loading = false
      })
    }
  }
}
</script>

<style scoped lang="less">
#home-assets-statistic{
  height: 100% !important;

  .action{
    height: 0.4rem;
    padding:0.075rem 0.275rem 0 0.175rem;//6/80  22/80  14/80
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-flow: row nowrap;
    color: rgba(255, 255, 255, .6);
    font-weight: 500;
    font-size:0.175rem ;//14/80

    .left-btn{
      & :first-child{
        margin-right:0.425rem;//34/80
      }
    }
    .right-btn{
      & :nth-child(2){
        margin: 0 0.225rem;//18/80
      }
    }

    .btn{
      cursor: pointer;
    }
    .btn:hover{
      color: #27BDFF;
    }
    .btn-active{
      color:#27BDFF !important;
    }
  }

  .trend-chart,.empty-wrapper{
    height: calc(100% - 0.4rem) !important;
    width: 100%;
  }
}
</style>