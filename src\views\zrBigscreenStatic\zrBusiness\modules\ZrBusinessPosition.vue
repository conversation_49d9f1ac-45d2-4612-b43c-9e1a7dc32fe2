<template>
  <div class='zr-business-position'>
    <zr-bigscreen-title title='部署位置'></zr-bigscreen-title>
   <div class='panel-box'>
     <slot>
       <a-carousel autoplay :dots='false' :afterChange='afterChange'>
         <div class='position-content' v-for='(item,index) in posInfo' :key='index' >
           <div class='position-pos'>
             <img src='/zrBigScreen/pos.png' alt=''>
           </div>
           <div class='position-info' :title='item.position'>
             {{ item.position}}
           </div>
           <div class='device-name'>
             {{ item.deviceName }}
           </div>
         </div>
       </a-carousel>

     </slot>
   </div>

  </div>
</template>
<script>
import ZrBigscreenTitle from '@views/zrBigscreens/modules/ZrBigscreenTitle.vue'
import { getAction} from '@/api/manage'
export default {
  name: 'ZrBusinessPosition',
  components: { ZrBigscreenTitle },
  props: {
    topoId: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      posInfo: []
    }
  },
  created() {
  },
  mounted() {
    this.getDeviceByTopo()
  },
  methods: {
    afterChange(e){
      this.$emit("changeBlur", this.posInfo[e].id)
    },
    getDeviceByTopo() {
      if (this.topoId) {
        getAction('/topo/topoInfo/queryById', {
          id: this.topoId,
          topoDataType: 2
        }).then((res) => {
          if (res.success) {
            this.posInfo = res.result.nodeList.map(item=>{
              if(!item.nodeDeviceInfo){
                // console.log('获取到拓扑图数据 === ', item)
              }
              const nodeData = item.nodeData?JSON.parse(item.nodeData):null
              const namestr = nodeData?.deviceName || "虚拟机"
              return {
                id: item.id,
                deviceName:nodeData?.deviceName || "虚拟机",
                deviceCode:nodeData?.deviceCode || "",
                position:window.ZrPosition || namestr+"的位置",
                nodeType:nodeData.nodeType
              }
            }).filter(item=>item.nodeType == "device")
            if(this.posInfo[0]){
              this.$emit("changeBlur", this.posInfo[0].id)
            }

          }
        })
      }
    }
  }
}
</script>

<style scoped lang='less'>
.zr-business-position {
  height: 100%;
  .panel-box{
    height: calc(100% - 51px);
    background: linear-gradient(to right, rgba(29, 78, 140, 0.3), rgba(29, 78, 140, 0.0));
  }
  .position-content {
    padding: 45px 35px;
    position: relative;
    .position-pos {
      position: absolute;
      top: 51px;
      left: 10px;

      img {
        width: 18px;
        height: 23px;
      }
    }

    .position-info {
      //font-family: Source Han Sans CN;
      font-weight: 500;
      font-size: calc(30 / 19.2 * 1vw);
      color: #89DAFF;
      opacity: 0.95;
      line-height: 30px;
      max-height: 60px;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      text-overflow: ellipsis;
      @media (min-width: 1920px) {
        font-size: 30px;
      }
    }

    .device-name {
      margin-top: 41px;
      //font-family: Source Han Sans CN;
      font-weight: 500;
      font-size: calc(30 / 19.2 * 1vw);
      color: #89DAFF;
      opacity: 0.95;
      line-height: 1;
      @media (min-width: 1920px) {
        font-size: 30px;
      }
    }

  }
}
</style>