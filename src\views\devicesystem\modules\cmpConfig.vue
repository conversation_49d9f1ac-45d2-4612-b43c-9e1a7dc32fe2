<template>
  <j-modal :title="title" :width="modelWidth" :visible="visible" :centered="true" :confirmLoading="confirmLoading"
    switchFullscreen :destroyOnClose="true" @ok="handleOk" @cancel="handleCancel" cancelText="关闭">
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <div style="padding-left: 7px; border-left: 4px solid #1e3674; margin: 0 0 10px 15px"
          v-if="this.paramList.length > 0 || this.cmpGroup == '3'">
          {{ label1 }}
        </div>
        <a-row>
          <a-col :span="22">
            <a-form-item label="组件别名" :labelCol="labelCol" :wrapperCol="wrapperCol1">
              <a-input type="text" v-model="nodeLabel" style="width: 100%" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row v-if="this.cmpGroup == '3'">
          <a-col :span="10" :offset="1">
            <a-form-item label="属性标识" :labelCol="labelCol1" :wrapperCol="wrapperCol">
              <div>{{ key }}</div>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="数据类型" :labelCol="labelCol1" :wrapperCol="wrapperCol">
              <div>{{ type }}</div>
            </a-form-item>
          </a-col>
          <a-col :span="10" :offset="1">
            <a-form-item label="上级属性标识" :labelCol="labelCol1" :wrapperCol="wrapperCol"
              v-if="this.parent != null && this.parent != undefined">
              <div>{{ parent }}</div>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row v-for="(item, index) in paramList" :key="index">
          <a-col :span="22">
            <a-form-item :label="item.paramName" :required="item.judge" :labelCol="labelCol" :wrapperCol="wrapperCol1">
              <!-- 输入框临时调整为文本域 -->
              <a-textarea v-if="(item.dataType == 'text' || item.dataType == 'array') && item.isSelect == false"
                v-decorator="[
                  item.paramValue + '_' + index,
                  {
                    initialValue: item.paramValue,
                    rules: [{ required: item.judge, message: '请输入' + item.paramName }],
                  },
                ]" :placeholder="'请输入' + item.paramName" :allowClear="true" autocomplete="off"
                @change="inputValueChange($event, index)" autosize />
              <a-select v-if="(item.dataType == 'text' || item.dataType == 'array') && item.isSelect == true"
                v-decorator="[
                  item.paramValue + '_' + index,
                  {
                    initialValue: item.paramValue,
                    rules: [{ required: item.judge, message: '请选择' + item.paramName }],
                  },
                ]" :placeholder="'请选择' + item.paramName" :allowClear="true" mode="tags" autocomplete="off"
                @change="paramValueChange($event, index)">
                <a-select-option v-for="(item, index) in configOverview" :key="index" :value="item">
                  {{ item }}
                </a-select-option>
              </a-select>
              <a-input-number v-if="
                  item.dataType == 'int' ||
                  item.dataType == 'long' ||
                  item.dataType == 'float' ||
                  item.dataType == 'double'
                " v-decorator="[
                  item.paramValue + '_' + index,
                  {
                    initialValue: item.paramValue,
                    rules: [{ required: item.judge, message: '请输入' + item.paramName }],
                  },
                ]" @change="numberValueChange($event, index)" />
              <a-switch v-decorator="[
                  item.paramValue + '_' + index,
                  {
                    initialValue: false,
                    rules: [{ required: item.judge, message: '请选择' + item.paramName }],
                  },
                ]" checked-children="是" un-checked-children="否" defaultChecked v-if="item.dataType == 'bool'"
                @change="booleanValueChange($event, index)" />
            </a-form-item>
          </a-col>
          <a-col :span="2" v-if="item.tips">
            <a-popover :title="item.paramName + '说明'">
              <template slot="content">
                <p>{{ item.tips }}</p>
              </template>
              <a-icon type="question-circle" theme="twoTone" style="font-size: 20px; line-height: 40px" />
            </a-popover>
          </a-col>
        </a-row>
        <div style="padding-left: 7px; border-left: 4px solid #1e3674; margin: 0 0 10px 15px">{{ label2 }}</div>
        <a-row v-if="this.cmpGroup == '3'">
          <a-col :span="22">
            <a-form-item label="指令" :labelCol="labelCol" :wrapperCol="wrapperCol1">
              <a-textarea v-model="value" placeholder="请输入指令" :allowClear="true" autosize autocomplete="off" />
            </a-form-item>
          </a-col>
          <a-col :span="2">
            <a-popover title="指令说明">
              <template slot="content">
                <p>获取数据的指令</p>
                <p>snmp为oid，例：*******.********.3.1.2</p>
                <p>jdbc为sql，例：select * from sys</p>
              </template>
              <a-icon type="question-circle" theme="twoTone" style="font-size: 20px; line-height: 40px" />
            </a-popover>
          </a-col>
          <a-col :span="24">
            <a-form-item :label="item.paramName" :required="item.judge" :labelCol="labelCol" :wrapperCol="wrapperCol"
              v-for="(item, index) in paramList" :key="index">
              <a-input v-decorator="[item.paramValue + '_' + index, { initialValue: item.paramValue }]"
                :placeholder="'请输入' + item.paramName" :allowClear="true" autocomplete="off"
                @change="contentChange($event, index)" />
            </a-form-item>
          </a-col>
        </a-row>

        <div style="padding-left: 7px; border-left: 4px solid #1e3674; margin: 0 0 10px 15px"
          v-if="this.cmpGroup == '3'">
          依赖配置
        </div>
        <a-row v-if="this.cmpGroup == '3'">
          <a-col :span="22">
            <a-form-item label="依赖指标" :labelCol="labelCol" :wrapperCol="wrapperCol1">
              <a-select v-model="target" placeholder="请输入依赖指标" :allowClear="true" autocomplete="off" mode="tags"
                option-label-prop="value">
                <a-select-option v-for="(item, index) in configOverviewOther" :key="index" :value="item.value">
                  {{ item.title }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="2">
            <a-popover title="依赖指标说明">
              <template slot="content">
                <p>
                  本流程计算时所依赖的其他指标，须为被依赖的指标配置流程后才可以选择添加（若当前指标参与计算，也须添加到依赖指标）
                </p>
              </template>
              <a-icon type="question-circle" theme="twoTone" style="font-size: 20px; line-height: 40px" />
            </a-popover>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="22">
            <a-form-item label="结果Key值" :labelCol="labelCol" :wrapperCol="wrapperCol1" v-if="this.cmpGroup != '3'">
              <div>
                <a-input v-decorator="[
                  'resultKey',
                  {
                    initialValue: resultKey,
                    rules: [{ required: true, message: '请输入结果Key值' }],
                  },
                ]" placeholder="请输入结果Key值" :allowClear="true" autocomplete="off" />
              </div>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-spin>
  </j-modal>
</template>

<script>
  import {
    httpAction,
    getAction
  } from '@/api/manage'
  import pick from 'lodash.pick'
  import {
    validateDuplicateValue
  } from '@/utils/util'
  import {
    deleteAction,
    postAction
  } from '../../../api/manage'

  export default {
    name: 'cmpConfig',
    components: {},
    data() {
      return {
        nodeId: '',
        nodeLabel: '',
        configOverview: [],
        configOverviewOther: [],
        formValue: {},
        cmpGroup: '',
        codeName: '',
        code: '',
        cmpTag: '',
        formDataCmpTag: '',
        target: [],
        chainId: '',
        cmpId: '',
        resultKey: '',
        paramList: [],
        key: '',
        type: '',
        parent: '',
        value: '',
        form: this.$form.createForm(this),
        title: '操作',
        modelWidth: '800px',
        visible: false,
        model: {},
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          },
        },
        labelCol1: {
          xs: {
            span: 24
          },
          sm: {
            span: 8
          },
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          },
        },
        wrapperCol1: {
          xs: {
            span: 24
          },
          sm: {
            span: 18
          },
        },

        confirmLoading: false,
        url: {
          saveCmpParam: '/flow/chain/saveCmpParam',
          queryCmpParam: 'flow/chain/queryCmpParam',
        },
        expandedRowKeys: [],
        pidField: 'parentId',
        hasChild: 'has_child',
      }
    },
    computed: {
      label1() {
        return this.cmpGroup == '3' ? '组件信息' : '入参配置'
      },
      label2() {
        return this.cmpGroup == '3' ? '协议指令配置' : '组件输出'
      },
    },
    mounted() {},
    methods: {
      paramValueChange(value, index) {
        this.paramList[index].paramValue = value
      },
      inputValueChange(value, index) {
        this.paramList[index].paramValue = value.target.value
      },
      numberValueChange(e, index) {
        this.paramList[index].paramValue = e
      },
      booleanValueChange(e, index) {
        this.paramList[index].paramValue = e
      },
      contentChange(e, pidx) {
        this.paramList[pidx].paramValue = e.target.value
      },
      edit(record, info, data, cmpGroup, configOverview, configOverviewOther) {
        this.nodeId = record.id
        this.nodeLabel = record.attrs.label.text
        this.configOverview = configOverview
        this.configOverviewOther = configOverviewOther
        this.parent = info.pCode
        this.resultKey = !!data.resultKey ? data.resultKey : record.data.tag + 'Result'
        this.type = info.dataType
        this.key = info.code
        if (this.parent != null && this.parent != '') {
          this.configOverviewOther.unshift({
            title: this.parent + '.' + this.key + '(本流程)',
            value: this.parent + '.' + this.key,
          })
          this.configOverview.unshift(this.parent + '.' + this.key)
        } else {
          this.configOverviewOther.unshift({
            title: this.key + '(本流程)',
            value: this.key,
          })
          this.configOverview.unshift(this.key)
        }
        this.configOverview = Array.from(new Set(this.configOverview))
        this.configOverviewOther = Array.from(new Set(this.configOverviewOther))
        this.code = data.cmpCode
        if (data.function && data.function.calculateOriginal && data.function.calculateOriginal.target.length > 0) {
          this.target = data.function.calculateOriginal.target
        } else {
          this.target = []
        }
        this.value = data.paramValue
        this.cmpGroup = cmpGroup
        this.cmpId = record.data.id
        this.chainId = info.chainId
        let map = new Map()
        for (let item of this.configOverviewOther) {
          if (!map.has(item.value)) {
            map.set(item.value, item)
          }
        }
        this.configOverviewOther = [...map.values()]
        if (data.paramModel && data.paramModel.length > 0) {
          data.paramModel.forEach((ele) => {
            if (ele.dataType == 'text')
              if (ele.paramValue != null && ele.paramValue.includes(',')) {
                ele.paramValue = ele.paramValue.split(',')
              }
          })
          this.paramList = data.paramModel
          this.paramList.forEach((ele) => {
            ele.isSelect = ele.isSelect ? ele.isSelect : false
          })
        } else {
          this.paramList = []
        }
        this.cmpTag = record.data.tag
        this.formDataCmpTag = record.id
      },
      close() {
        this.visible = false
      },
      handleOk() {
        if (this.cmpGroup === '3') {
          if (
            (this.value === '' || this.value == null) && (this.target === [] || this.target.length === 0)
          ) {
            this.$message.warning('指令或依赖指标至少输入一项')
            return
          }
        }
        this.form.validateFields((err, values) => {
          if (!err) {
            let httpurl = ''
            let method = ''
            httpurl += this.url.saveCmpParam
            method = 'post'
            this.confirmLoading = false
            if (this.cmpGroup != 3) {
              let formData = {}
              formData.nodeLabel = this.nodeLabel
              formData.nodeId = this.nodeId
              formData.resultKey = values.resultKey
              formData.code = this.code
              formData.param = {}
              if (this.paramList.length > 0) {
                this.paramList.forEach((ele) => {
                  if (ele.dataType == 'text') {
                    this.$set(formData.param, ele.paramCode, !!ele.paramValue ? ele.paramValue.toString() : "")
                  } else {
                    this.$set(formData.param, ele.paramCode, ele.paramValue)
                  }
                })
              }
              let info = {
                cmpId: this.cmpId,
                chainId: this.chainId,
                cmpTag: this.cmpTag,
                cmpParam: JSON.stringify(formData),
              }
              httpAction(httpurl, info, method)
                .then((res) => {
                  if (res.success) {
                    this.$message.success(res.message)
                    this.$emit('ok', info.cmpParam, info.cmpTag, info.chainId)
                  } else {
                    this.$message.warning(res.message)
                  }
                })
                .finally(() => {
                  this.confirmLoading = false
                  this.close()
                  this.$forceUpdate()
                })
            } else {
              let formData = {
                function: {
                  calculateOriginal: {
                    target: [],
                  },
                },
              }
              formData.key = this.key
              formData.type = this.type
              formData.parent = this.parent
              if (this.target.length > 0) {
                formData.calculate = true
              } else {
                formData.calculate = false
              }
              formData.value = this.value
              formData.nodeLabel = this.nodeLabel
              formData.nodeId = this.nodeId
              formData.function.calculateOriginal.target = this.target
              let info = {
                cmpId: this.cmpId,
                chainId: this.chainId,
                cmpTag: this.formDataCmpTag,
                cmpParam: JSON.stringify(formData),
              }
              httpAction(httpurl, info, method)
                .then((res) => {
                  if (res.success) {
                    this.$message.success(res.message)
                    this.$emit('ok', info.cmpParam)
                  } else {
                    this.$message.warning(res.message)
                  }
                })
                .finally(() => {
                  this.confirmLoading = false
                  this.close()
                  this.$forceUpdate()
                })
            }
          }
        })
      },
      handleCancel() {
        this.close()
      },
    },
  }
</script>
<style lang='less' scoped>
  @import '~@assets/less/normalModal.less';
</style>