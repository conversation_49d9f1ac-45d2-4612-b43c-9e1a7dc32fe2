<template>
  <div style="height:100%;">
    <keep-alive exclude='logDetail'>
      <component :is="pageName" style="height:100%" :data="data" />
    </keep-alive>
  </div>
</template>
<script>
  import logList from './logList'
  import logDetail from './modules/logDetail'
  export default {
    name: "logManage",
    data() {
      return {
        isActive: 0,
        data: {}
      };
    },
    components: {
      logList,
      logDetail
    },
    created() {
      this.pButton1(0);
    },
    //使用计算属性
    computed: {
      pageName() {
        switch (this.isActive) {
          case 0:
            return "logList";
            break;

          default:
            return "logDetail";
            break;
        }
      }
    },
    methods: {
      pButton1(index) {
        this.isActive = index;
      },
      pButton2(index, item) {
        this.isActive = index;
        this.data = item;
      }
    }
  }
</script>