<template>
  <a-card style="height:100%; overflow: hidden; overflow-y: auto">
    <a-row>
      <a-col :span="24">
        <span style="margin-left:10px;color: #409eff;cursor: pointer" @click="handleDetailEdit(data)">
          <a-icon type="edit" style="margin-right: 6px;"/>编辑
        </span>
        <span style="float: right;margin-bottom: 12px;" ><img src="~@/assets/return1.png" alt="" @click="getGo" style="width: 20px;height: 20px;cursor: pointer"></span>
      </a-col>
      <a-col :span="24">
        <table class="gridtable">
          <tr>
            <td class="leftTd">策略名称</td> <td class="rightTd">{{ data.proName }}</td>
            <td class="leftTd">备份文件名称</td> <td  class="rightTd">{{ data.fileName }}</td>
          </tr>
          <tr>
            <td class="leftTd">执行cron码</td> <td class="rightTd">{{ data.taskCron }}</td>
            <td class="leftTd">文件类型</td> <td class="rightTd">{{ data.whCondense_dictText }}</td>
          </tr>
        </table>
      </a-col>
    </a-row>
    <devops-backup-pro-modal ref="modalForm" @ok="modalFormOk"></devops-backup-pro-modal>
  </a-card>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import pick from 'lodash.pick'
  import { validateDuplicateValue } from '@/utils/util'
  import JFormContainer from '@/components/jeecg/JFormContainer'
  import JDictSelectTag from "@/components/dict/JDictSelectTag"
  import JCron from "@/components/jeecg/JCron.vue";
  import DevopsBackupProModal from './DevopsBackupProModal'
  export default {
    name: 'DevopsBackupProDetails',
    components: {
      JFormContainer,
      JDictSelectTag,
      DevopsBackupProModal
    },
    props: {
      //流程表单data
      formData: {
        type: Object,
        default: ()=>{},
        required: false
      },
      //表单模式：true流程表单 false普通表单
      formBpm: {
        type: Boolean,
        default: false,
        required: false
      },
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      },
      data:{
        type:Object
      }
    },
    data () {
      return {
        form: this.$form.createForm(this),
        model: {},
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        url: {
          queryById: "/devopsbackuppro/devopsBackupPro/queryById"
        }
      }
    },
    computed: {
    },
    created () {
      //如果是流程中表单，则需要加载流程表单data
      // this.showFlowData();

    },
    mounted(){
      // var input = document.getElementById('jCron').getElementsByTagName("input");
      // input.disabled = true;
    },
    methods: {
      modalFormOk(){
        let params = {id:this.data.id};
        getAction(this.url.queryById,params).then((res)=>{
          if(res.success){

            this.data = res.result;
          }
        });
      },
      //详情编辑
      handleDetailEdit: function (record) {

        this.$refs.modalForm.edit(record)
        this.$refs.modalForm.title = '编辑'
        this.$refs.modalForm.disableSubmit = false
      },
      //返回上一级
      getGo(){
        this.$parent.pButton2(0,'');
      }
    }
  }
</script>
<style lang="less" scoped>
/deep/ .ant-descriptions-item-label {
  text-align: center !important;
}
table.gridtable {
  font-family: verdana,arial,sans-serif;
  font-size:14px;
  color:#606266;
  border-width: 1px;
  border-color: #e8e8e8;
  border-collapse: collapse;
  text-align: left;
  width: 100%;
}
table.gridtable td {
  border-width: 1px;
  border-style: solid;
  border-color: #e8e8e8;
}
.leftTd{
  width: 17%;
  background-color: #FAFAFA;
  padding: 16px 24px;
  text-align: center;
}
.rightTd{
  width: 35%;
  padding: 16px 24px;
}
</style>