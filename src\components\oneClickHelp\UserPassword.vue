<template>
  <yq-modal
    :title="title"
    :width="modalWidth"
    :visible="visible"
    :centered="true"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <div class="reset-div" v-if="reset">密码已到期，请修改密码</div>
        <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="旧密码">
          <a-input
            type="password"
            placeholder="请输入旧密码"
            v-decorator="['oldpassword', validatorRules.oldpassword]"
          />
        </a-form-item>

        <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="新密码" hasFeedback>
          <a-input
            type="password"
            placeholder="请输入新密码"
            v-decorator="['password', validatorRules.password]"/>
        </a-form-item>

        <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="确认新密码" hasFeedback>
          <a-input
            type="password"
            @blur="handleConfirmBlur"
            placeholder="请确认新密码"
            v-decorator="['confirmpassword', validatorRules.confirmpassword]"
          />
        </a-form-item>
      </a-form>
    </a-spin>
  </yq-modal>
</template>

<script>
import { putAction, getAction } from '@/api/manage'
import { mapActions } from 'vuex'
import { getHostNameLocal } from '@/utils/util'
export default {
  name: 'UserPassword',
  data() {
    return {
      title: '修改密码',
      modalWidth: 800,
      visible: false,
      confirmLoading: false,
      validatorRules: {
        oldpassword: {
          rules: [
            {
              required: true,
              message: '请输入旧密码!',
            },
          ],
        },
        password: {
          rules: [
            {
              required: true,
              message: '请输入新密码!',
            },
            {
              validator: this.validateToNextPassword,
            },
          ],
        },
        confirmpassword: {
          rules: [
            {
              required: true,
              message: '请重新输入新密码!',
            },
            {
              validator: this.compareToFirstPassword,
            },
          ],
        },
      },
      confirmDirty: false,
      labelCol: {
        xs: {
          span: 24,
        },
        sm: {
          span: 5,
        },
      },
      wrapperCol: {
        xs: {
          span: 24,
        },
        sm: {
          span: 16,
        },
      },

      form: this.$form.createForm(this),
      url: 'sys/user/updatePassword',
      pwdRuleUrl: 'umpPwdManage/umpPwdManage/list',
      username: '',
      pwdRuleInfo: null,
      oneClickHelp: false,
    }
  },
  props: {
    reset: {
      type: Boolean,
      default: false,
      required: false,
    },
  },
  created() {
    this.getPwdRuleData()
  },
  methods: {
    getPwdRuleData() {
      getAction(this.pwdRuleUrl).then((res) => {
        if (res.success) {
          this.pwdRuleInfo = res.result.records[0]
          this.setValidator(this.pwdRuleInfo)
        }
      })
    },
    setValidator(info) {
      let capRegStr = ''
      let lowerRegStr = ''
      let numRegStr = ''
      let speRegStr = ''
      let cbStr = ''
      if (!!info.pwdMin) {
        cbStr = '密码至少由' + this.pwdRuleInfo.pwdMin + '位组成，包含'
      }
      if (info.capitalize) {
        capRegStr = '[A-Z]+'
        cbStr += '大写字母'
      }
      if (info.lowercase) {
        lowerRegStr = '[a-z]+'
        cbStr += cbStr.length > 2 + Number(this.pwdRuleInfo.pwdMin) ? '、小写字母' : '小写字母'
      }
      if (info.hasNum) {
        numRegStr = '[0-9]+'
        cbStr += cbStr.length > 2 + Number(this.pwdRuleInfo.pwdMin) ? '、数字' : '数字'
      }
      if (info.special) {
        speRegStr += "[`~!_@#$^&*()=|{}':;',\\[\\].<>/?~！@#￥……&*（）——|{}【】‘；：”“'。，、？]+"
        cbStr += cbStr.length > 2 + Number(this.pwdRuleInfo.pwdMin) ? '、特殊字符' : '特殊字符'
      }
      this.validatorRules.password.rules.push({
        validator: (rule, value, callback) => {
          let capRegEn = new RegExp(capRegStr)
          let lowerRegEn = new RegExp(lowerRegStr)
          let numRegEn = new RegExp(numRegStr)
          let speRegEn = new RegExp(speRegStr)
          if (
            value &&
            (value.length < parseInt(this.pwdRuleInfo.pwdMin) ||
              !capRegEn.test(value) ||
              !lowerRegEn.test(value) ||
              !numRegEn.test(value) ||
              !speRegEn.test(value))
          ) {
            callback(cbStr + '！')
          } else {
            callback()
          }
        },
      })
    },
    show(uname) {
      if (!uname) {
        this.$message.warning('当前系统无登录用户!')
        return
      } else {
        this.username = uname
        this.form.resetFields()
        this.visible = true
      }
    },
    handleCancel() {
      this.close()
    },
    close() {
      this.$emit('close')
      this.visible = false
      this.disableSubmit = false
      this.selectedRole = []
    },
    ...mapActions(['Logout']),
    handleOk() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let params = Object.assign(
            {
              username: this.username,
            },
            values
          )
          putAction(this.url, params)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.close()
                return that.Logout({}).then(() => {
                  if (this.oneClickHelp) {
                    let hostName = getHostNameLocal()
                    this.$router.push({ path: '/oneClickHelp/index' })
                  } else {
                    window.location.href = '/'
                  }
                })
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    validateToNextPassword(rule, value, callback) {
      const form = this.form
      const confirmpassword = form.getFieldValue('confirmpassword')
      if (value && confirmpassword && value !== confirmpassword) {
        callback('两次输入的密码不一样！')
      }
      if (value && this.confirmDirty) {
        form.validateFields(['confirm'], {
          force: true,
        })
      }
      callback()
    },
    compareToFirstPassword(rule, value, callback) {
      const form = this.form
      if (value && value !== form.getFieldValue('password')) {
        callback('两次输入的密码不一样！')
      } else {
        callback()
      }
    },
    handleConfirmBlur(e) {
      const value = e.target.value
      this.confirmDirty = this.confirmDirty || !!value
    },
  },
}
</script>

<style lang="less" scoped>
@import "~@assets/less/onclickStyle.less";
@import "~@assets/less/normalModal.less";

.reset-div {
  text-align: center;
  margin-bottom: 16px;
  color: red;
  font-size: 16px;
}
</style>