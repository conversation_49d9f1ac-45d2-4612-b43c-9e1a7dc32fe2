<template>
  <div ref="threeBox" :id="elementId" style="height: 100%">
    <div class="jigui-info-panel" v-if="panelData">
      <a-table
        rowKey="name"
        size="small"
        :showHeader="false"
        :pagination="false"
        :scroll="{ y: 150 }"
        :customRow="tableCustomRow"
        :columns="panelColumns"
        :data-source="panelData"
      >
      </a-table>
    </div>
    <a-tooltip :visible="tipShow">
      <template slot="title" >
       {{tipText}}
      </template>
      <div class="jigui-info-tip" style='position: absolute;width: 1px;height: 1px;background: red' :style='{top: tipTop + "px", left: tipLeft + "px"}'></div>
    </a-tooltip>

  </div>
</template>
<script>

import * as THREE from 'three'
import { Detector } from './threeUtils/Detector'

import threeInitMixin from '@views/threejsEditor/threeUtils/threeInitMixin'
import { createMixins } from './threeUtils/createMixins'
import { jiguiMixins } from './threeUtils/jiguiMixins'
import { clickMixins } from './threeUtils/clickMixins'
import { serverMixins } from './threeUtils/serverMixins'
import { httpAction, getAction, postAction } from '@/api/manage'
import { getFileAccessHttpUrl } from '@/api/manage'
export default {
  mixins: [threeInitMixin,createMixins, jiguiMixins, serverMixins, clickMixins],
  props: {
    threeJson: {
      type: String,
      default: '',
    },
    roomId: {
      type: String,
      default: '',
      required: true,
    },
    modelsArr: {
      type: Array,
      default: () => [],
      required: true,
    },
    isEditor: {
      type: Boolean,
      default: false,
    },
    elementId:{
      type: String,
      default: 'threeScanBox',
    },
  },
  data() {
    return {
      boxH: 0,
      boxW: 0,
      serverArr: [],
      panelColumns: [
        {
          title: '名称',
          dataIndex: 'name',
          key: 'name',
          scopedSlots: { customRender: 'name' },
        },
        {
          title: '值',
          dataIndex: 'value',
          key: 'value',
          // width: 80,
        },
      ],
      panelData: null,
      clickState: false,
      i: 0,
      ani: null,
      movementSpeed: 1,
      tipTop: 0,
      tipLeft: 0,
      tipTimer:null,
      tipShow:false,
      tipText:"",
      container:null,
    }
  },
  created() {},
  mounted() {
    this.init()
  },
  beforeDestroy() {
    if(this.controls){
      this.controls.dispose()
    }

    this.destroyThreejs()
  },
  methods: {
    async init() {
      this.container = document.getElementById(this.elementId)
      await this.getAlarmLevelData()
      if (this.$refs.threeBox) {
        let rect = this.$refs.threeBox.getBoundingClientRect()
        this.boxH = rect.height
        this.boxW = rect.width
      }
      this.$store.commit('threejs/changeEditorStatus', this.isEditor)
      this.initScene()
      this.importScene()
      this.initRenderer()
      this.initCamera()
      this.initControls()
      this.initLight()
      this.animate()
      this.setJiguitServer()
      if(this.container){
        this.container.removeEventListener('click', this.onMouseClick)
        this.container.removeEventListener('dblclick', this.onMouseDblclick)
        this.container.removeEventListener('resize', this.onWindowResize)
        this.container.removeEventListener('mousemove',this.onDocumentMouseMove);
        this.container.addEventListener('click', this.onMouseClick)
        this.container.addEventListener('dblclick', this.onMouseDblclick)
        this.container.addEventListener('resize', this.onWindowResize)
        this.container.addEventListener('mousemove',this.onDocumentMouseMove, false);
      }
    },
    // 机柜信息 有告警的设备改变设备颜色
    tableCustomRow(record) {
      let color = '#000'
      return {
        style: {
          color: color,
        },
        class: 'notHover',
      }
    },
    // 同步机柜设备信息
    async setJiguitServer() {
      let devicesIds = []
      let devicesCodes = []
      let ddtem = {
        deviceId: '1524708935221895170',
        isEnable: 'true',
        status: 'down',
      }
      let param = {
        roomId: this.roomId,
        viewFlag: 3,
      }
      let promiseArr = []
      // 获取所有机柜信息
      await getAction('/topo/room/cabinet', param).then((res) => {
        if (res.code === 200) {
          res.result.forEach((cab) => {
            // 获取每个机柜的信息
            promiseArr.push(getAction('/topo/cabinet/info', { id: cab.id }))
          })
        }
      })
      await Promise.all(promiseArr).then((allRes) => {
        this.serverArr = allRes.map((el) => el.result)
      })
      scene.children.forEach((el) => {
        if (el.name === 'yq_group_jigui') {
          this.createJGWarnSign(el)
          let infoData = [
            {
              name: '设备个数',
              value: 0,
            },
          ]
          let server = this.serverArr.find((server) => server.id === el.userData.id)
          if (server) {
            infoData.push({
              name: '机柜高度',
              value: `${server.layers}单元`,
            })
            if (server.devices.length > 0) {
              infoData[0].value = server.devices.length
              server.devices.forEach((dev) => {
                if (dev.alarmStatus === 1) {
                  devicesIds.push(dev.id)
                  devicesCodes.push(dev.code)
                }
                let tem = {
                  name: `${dev.name}(IP)`,
                  value: dev.ip || '',
                  deviceId: dev.id,
                  deviceCode: dev.code,
                }
                infoData.push(tem)
                this.createResource(dev, el)
              })
            }
          }
          el.userData.infoData = infoData
        }
      })
      this.initDeviceStatus(devicesCodes)
    },
    // 初始化设备的告警状态
    initDeviceStatus(devicesCodes) {
      // 清空vuex的设备在线离线数据
      this.$store.commit('CLEAR_STATUS_LIST')
      // 清空vuex的设备告警状态
      this.$store.commit('CLEAR_ALARM_LIST')
      if (devicesCodes.length <= 0) return
      postAction('/topo/device/initStatus', { deviceCode: devicesCodes.join(',') }).then((res) => {
        if (res.success) {
          if (res.result && res.result.length > 0) {
            res.result.forEach((el) => {

              const maxAlarm = el.alarmInfo?el.alarmInfo.reduce((prev, curr, i, a) =>
                curr.alarmLevel > prev.alarmLevel ? curr : prev
              ):{};
              el.level = maxAlarm.alarmLevel
              el.templateName = el.alarmName
              el.deviceId = el.id
            })
            this.alarmInfos = res.result
            this.setJiguiLogo(res.result)
          }
        }
      })
    },


  },
}
</script>
<style lang="less" scoped>
#threeScanBox {
  position: relative;
  .jigui-info-panel {
    width: 360px;
    max-height: 190px;
    background-color: rgba(0, 0, 0, 0.5);
    position: absolute;
    top: 20px;
    left: 20px;
    // opacity: 0.5;
    padding: 12px;
    border-radius: 8px;
    // overflow-y: scroll;
  }
}

::v-deep .ant-table-tbody {
  tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) {
    td {
      background: transparent;
    }
  }
}
::v-deep .ant-table-tbody {
  tr:hover {
    td {
      td {
        background: transparent;
      }
    }
  }
}
</style>