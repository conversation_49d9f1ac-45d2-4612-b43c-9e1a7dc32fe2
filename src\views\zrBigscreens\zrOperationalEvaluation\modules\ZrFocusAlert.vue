<template>
  <div class='zr-focus-alert'>
    <zr-bigscreen-title title='重点关注提醒'>
    </zr-bigscreen-title>
    <div class='zr-focus-alert-content'>
      <div style='height: 5%'></div>
      <div class='focus-alert-type' style='height: 28%'>
        <div class='chart-title'>
          <div class='title-symbol'></div>
          <div class='title-text'>类型</div>
        </div>
        <div class='chart-content'>
          <zr-rank-bar :rank-data='alertData'></zr-rank-bar>
        </div>
      </div>
      <div class='focus-alert-indictor' style='height: 60%'>
        <div class='chart-title'>
          <div class='title-symbol'></div>
          <div class='title-text'>指标</div>
        </div>
        <div class='chart-content'>
          <zr-rank-bar :rank-data='indicators'></zr-rank-bar>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  import ZrBigscreenTitle from '@views/zrBigscreens/modules/ZrBigscreenTitle.vue'
  import ZrRankBar from '@views/zrBigscreens/zrOperationalEvaluation/modules/ZrRankBar.vue'
  // import {
  //   indicators
  // } from '@views/zrBigscreens/modules/zrUtil'
  export default {
    name: 'ZrFocusAlert',
    components: {
      ZrBigscreenTitle,
      ZrRankBar
    },
    data() {
      return {
        chart: null,
        alertData: [],
        indicators: [],
        scoreMap: {
          'A': 100,
          'B': 80,
          'C': 60,
          'D': 45,
        }
      }
    },
    created() {
    },
    mounted() {},
    methods: {
      show(record) {
        const metricsTypeList = record.metricsTypeList || []
        const metricsInfoList = record.metricsInfoList || []
        this.alertData = []
        this.indicators = []
        metricsTypeList.forEach((e) => {
          if (e.isFocus) {
            this.alertData.push({
              name: e.typeName,
              score: e.metricsResult ?(this.scoreMap[e.metricsResult] || 0) : 0,
              level: e.metricsTypeResult != null ? e.metricsTypeResult : '--'
            })
          }
        })
        metricsInfoList.forEach((e) => {
          if (e.isFocus) {
            this.indicators.push({
              name: e.metricsName,
              score: e.metricsResult ?(this.scoreMap[e.metricsResult] || 0) : 0,
              level: e.metricsResult != null ? e.metricsResult : '--'
            })
          }
        })
      },
    }
  }
</script>

<style scoped lang='less'>
  .zr-focus-alert {
    width: 100%;
    height: 100%;
  }

  .zr-focus-alert-content {
    padding: 0 24px;
    height: calc(100% - 51px);
    background: linear-gradient(to right, rgba(29, 78, 140, 0.3), rgba(29, 78, 140, 0.0));

    .chart-title {
      display: flex;
      align-items: center;

      .title-symbol {
        width: 4px;
        height: 16px;
        background: #00D2FF;
        margin-right: 9px;
      }

      .title-text {
        font-weight: 400;
        font-size: 14px;
        color: #FFFFFF;
      }
    }

    .chart-content {
      padding: 10px 0;
      height: calc(100% - 22px);
    }
  }
</style>