<template>
  <div class='data-security-page'>
    <a-row class='row-wrapper' type='flex' justify='space-between'>
      <a-col :lg='6' :md='8' :sm='10' :xl='4' :xs='10' class="left-col-wrapper">
        <ClusterFields
          @change-field='setCluster'>
        </ClusterFields>
      </a-col>

      <a-col :lg='18' :md='16' :sm='14' :xl='20' :xs='14' class="right-col-wrapper">
        <a-row class="right-row">
          <a-col :span='12' class="right-col-one">
            <a-button type='primary' @click='addClusterUsers'>新增</a-button>
            <div class='table-wrapper'>
              <a-table
                ref='table'
                bordered
                :row-key='(record,index)=>{return index}'
                :columns='columns'
                :dataSource='dataSource'
                :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
                :pagination='false'
                :loading='loading'
                rowKey='name'
                :row-selection="{ selectedRowKeys: selectedUser, onChange: onSelectUserChange,type: 'radio' }"
                @change='handleTableChange'>

                <span slot='action' class='caozuo' slot-scope='text, record'>
                  <a @click='handleDelete(record.name)'>删除</a>
                </span>
                <template slot='tooltip' slot-scope='text'>
                  <a-tooltip placement='topLeft' :title='text' trigger='hover'
                             overlayClassName='data-security-document-class-name'>
                    <div class='tooltip'>
                      {{ text }}
                    </div>
                  </a-tooltip>
                </template>
              </a-table>
            </div>
          </a-col>

          <a-col :span='12' class="right-col-two">
            <a-button type='primary' @click='openAuthorizeModal'>修改策略</a-button>
            <div class='table-wrapper'>
              <a-table
                ref='table'
                bordered
                :row-key='(record,index)=>{return index}'
                :columns='authoritysColumns'
                :dataSource='authoritys'

                :pagination='false'
                :loading='aLoading'
                @change='handleTableChange'>
                <span slot='action' class='caozuo' slot-scope='text, record'>
                  <a @click='authorizeHandle(record)'>授权</a>
                  <a-divider type='vertical' />
                  <a @click='UnbindHandle(record)'>解绑</a>
                </span>
                <template slot='tooltip' slot-scope='text'>
                  <a-tooltip placement='topLeft' :title='text' trigger='hover'
                             overlayClassName='data-security-document-class-name'>
                    <div class='tooltip'>
                      {{ text }}
                    </div>
                  </a-tooltip>
                </template>
              </a-table>
            </div>
          </a-col>
        </a-row>
      </a-col>
    </a-row>
    <data-security-auth-modal ref='authModal' @ok='getUserAuthority'></data-security-auth-modal>
    <add-cluster-users-modal ref="addClusterUsersModal" @ok="loadData"></add-cluster-users-modal>
  </div>
</template>

<script>
import moment from 'moment'
import { mixinDevice } from '@/utils/mixin'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
import '@assets/less/TableExpand.less'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import yqIcon from '@comp/tools/SvgIcon'
import ClusterFields from '@views/distribution/dataSecurity/modules/ClusterFields.vue'
import { deleteAction, getAction } from '@api/manage'
import DataSecurityAuthModal from '@views/distribution/dataSecurity/modules/DataSecurityAuthModal.vue'
import addClusterUsersModal from '@views/distribution/dataSecurity/modules/AddClusterUsersModal.vue'

export default {
  name: 'dataSecurityManage',
  mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
  components: {
    DataSecurityAuthModal,
    yqIcon,
    ClusterFields,
    addClusterUsersModal
  },
  data() {
    return {
      description: '数据安全管理',
      /**当前禁用启用状态*/
      //左侧：字段数据
      loadFields: false,
      fieldList: [],
      columns: [
        {
          title: '用户名称',
          dataIndex: 'name',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 180px;'
            return { style: cellStyle }
          }
        },
        {
          title: '操作',
          align: 'center',
          width: 70,
          dataIndex: 'action',
          scopedSlots: {
            customRender: 'action'
          }
        }
      ],
      authoritysColumns: [
        {
          title: '策略名称',
          dataIndex: 'policyName',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 180px;'
            return { style: cellStyle }
          }
        }
      ],
      url: {
        list: '',
        feilds: ''
      },
      disableMixinCreated: true,
      cluster: '',
      clusterAuthorities: [
        {
          username: 'admin',
          role: 'admin',
          authority: `{"Version":"2012-10-17","Statement":[{"Effect":"Allow","Action":["admin:*"]},{"Effect":"Allow","Action":["kms:*"]},{"Effect":"Allow","Action":["s3:*"],"Resource":["arn:aws:s3:::*"]}]}`
        }, {
          username: 'provider1',
          role: 'admin',
          authority: `{"Version":"2012-10-17","Statement":[{"Effect":"Allow","Action":["admin:*"]},{"Effect":"Allow","Action":["kms:*"]},{"Effect":"Allow","Action":["s3:*"],"Resource":["arn:aws:s3:::*"]}]}`
        }
      ],
      selectedUser: [],
      authoritys: [],
      aLoading: false
    }
  },
  created() {
    // this.loadData()
  },
  watch: {
    cluster(val) {
      this.selectedUser = []
      this.dataSource = []
      this.authoritys = []
      this.loadData()
    }
  },
  methods: {
    //添加集群用户
    addClusterUsers() {
      if (this.cluster && this.cluster.id) {
        this.$refs.addClusterUsersModal.add(this.cluster.id)
      } else {
        this.$message.warning('当前集群为空，无法创建用户')
      }
    },
    //删除用户
    handleDelete: function(id) {
      var that = this
      let params = {
        clusterId: this.cluster.id,
        userName: id
      }
      deleteAction('/distributedStorage/user', params).then((res) => {
        if (res.success) {
          //重新计算分页问题
          that.reCalculatePage(1)
          that.$message.success(res.message)
          that.loadData()
        } else {
          that.$message.warning(res.message)
        }
      })
    },
    //打开泉下能弹窗
    openAuthorizeModal() {
      if (this.selectedUser.length == 0) {
        this.$message.warning('请选择用户')
        return
      }
      this.$refs.authModal.open(this.cluster.id, this.authoritys, this.selectedUser[0])
    },
    //监听用户选择
    onSelectUserChange(selectedRowKeys, selectedRows) {
      this.selectedUser = selectedRowKeys
      this.getUserAuthority()
    },
    loadData(cluster) {
      var params = this.getQueryParams() //查询条件
      params.clusterId = this.cluster.id
      this.loading = true
      getAction('distributedStorage/user', params).then(res => {
        this.$nextTick(() => {
          this.dataSource = res.result ? res.result : []
        })
      }).catch((err) => {
        this.dataSource = []
        this.$message.warning(err.message)
      }).finally(() => {
        this.ipagination.total = this.dataSource.length
        this.loading = false
      })
    },
    //获取用户权限
    getUserAuthority() {
      this.authoritys = []
      let params = { clusterId: this.cluster.id, userName: this.selectedUser[0] }
      this.aLoading = true
      getAction('distributedStorage/user/authority', params).then(res => {
        if (res.success) {
          this.authoritys = res.result
        }
      }).finally(() => {
        this.aLoading = false
      })
    },
    setCluster(cluster) {
      this.cluster = cluster
    },
    onSearch(value) {
    },
    changeSource(logAnalyze) {
    },
    authorizeHandle(record) {
      this.$refs.authModal.add(record)
    },
    UnbindHandle(record) {

    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';

.data-security-page {
  height: 100%;

  .row-wrapper {
    height: 100%;

    .left-col-wrapper {
      height: 100%;
      background: #fff;
      padding: 16px;
      border-radius: 3px;

      .data-security-fields {
        //padding: 12px 0 24px 8px;
        height: 100%
      }
    }

    .right-col-wrapper {
      height: 100%;
      overflow: hidden;

      .right-row {
        margin-left: 16px;
        background: #fff;
        border-radius: 3px;
        height: 100%;
        padding: 24px;

        .right-col-one {
          padding-right: 6px;
          height: 100%;
        }
        .right-col-two {
          padding-left: 6px;
          height: 100%;
        }
      }
    }

    .table-wrapper {
      margin-top: 12px;
      height: calc(100% - 12px - 32px);
      overflow: auto !important;
      //overflow-y: auto;
      ::v-deep .ant-table-scroll{
        overflow: unset !important;
        .ant-table-body{
          overflow: unset !important;
        }
      }
    }
  }
}

/*.overlay {
  color: #409eff
}*/
</style>
