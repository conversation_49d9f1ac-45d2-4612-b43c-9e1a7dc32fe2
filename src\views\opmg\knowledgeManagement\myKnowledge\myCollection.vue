<template>
  <div style="margin-top:15px;">
    <!-- 查询区域 -->
    <div class='table-page-search-wrapper'>
      <a-form layout='inline'
        @keyup.enter.native='searchQuery'>
        <a-row :gutter='24'
          ref='row'>
          <a-col :span='spanValue'>
            <a-form-item label='标题'>
              <a-input placeholder='请输入标题'
                v-model='queryParam.title'
                :allowClear='true'
                autocomplete='off' :maxLength="maxLength" />
            </a-form-item>
          </a-col>
          <a-col :span='colBtnsSpan()'>
            <span class='table-page-search-submitButtons'
              :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
              <a-button type='primary'
                class='btn-search btn-search-style'
                @click='searchQuery'>查询</a-button>
              <a-button class='btn-reset btn-reset-style'
                @click='searchReset'>重置</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 操作按钮区域 -->
    <div class='table-operator table-operator-style'>
      <a-button @click="cancelCollectBatch">
        <a-icon type="star"></a-icon>
        批量取消</a-button>
    </div>
    <a-table ref="table"
      bordered
      rowKey="id"
      :columns="columns"
      :dataSource="dataSource"
      :pagination="ipagination"
      :loading="loading"
      :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      class="j-table-force-nowrap"
      @change="handleTableChange">
      <span slot="action"
        slot-scope="text, record"
        class="caozuo"
        style="display: inline-block; white-space: nowrap; text-align: center">
        <a @click="handleDetailPage(record)">查看</a>
        <a-divider type='vertical' />
        <a-popconfirm title="确定取消吗?"
          @confirm="() => handleCancelCollect(record.id)">
          <a>取消</a>
        </a-popconfirm>
      </span>
      <template slot='tooltip'
        slot-scope='text'>
        <a-tooltip placement='topLeft'
          :title='text'
          trigger='hover'>
          <div class='tooltip'>
            {{ text }}
          </div>
        </a-tooltip>
      </template>
      <template slot='knowledgeType' slot-scope='text'>
        <knowledge-icon :knowledgeType="text"></knowledge-icon>
      </template>
    </a-table>
  </div>
</template>

<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
import knowledgeIcon from '@views/opmg/knowledgeManagement/knowledgeBase/modules/KnowledgeIcon.vue'
import {
  postParamsAction
} from '@/api/manage'
export default {
  name: 'myCollection',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components: {
    knowledgeIcon
  },
  data() {
    return {
      maxLength:50,
      // selectedRowKeys:[],
      columns: [
        {
          title: '标题',
          dataIndex: 'title',
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'tooltip'
          }
        },
        {
          title: '主题名称',
          dataIndex: 'topicName',
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'tooltip'
          }
        },
        {
          title: '知识类型',
          dataIndex: 'knowledgeType',
          customCell: () => {
            let cellStyle = 'width:80px'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'knowledgeType'
          }
        },
        {
          title: '创建人员',
          dataIndex: 'createBy',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 80px;max-width:200px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          customCell: () => {
            let cellStyle = 'text-align: center;width: 160px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '最近更新时间',
          dataIndex: 'updateTime',
          customCell: () => {
            let cellStyle = 'text-align: center;width: 160px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 150,
          scopedSlots: {
            customRender: 'action'
          }
        }
      ],
      url: {
        list: '/kbase/knowledges/usercollect',
        uncollect: '/kbase/knowledges/uncollect',
        uncollectBatch: '/kbase/knowledges/uncollectBatch'
      },
    }
  },
  activated() {
    console.log('激活我的收藏')
    this.loadData()
  },
  methods: {
    // 批量取消收藏
    cancelCollectBatch: function () {
      let that = this;
      if (that.selectedRowKeys.length <= 0) {
        that.$message.warning('请选择一条记录！')
        return
      } else {
        var ids = ''
        for (var a = 0; a < this.selectedRowKeys.length; a++) {
          ids += this.selectedRowKeys[a] + ','
        }

        this.$confirm({
          title: '确认取消',
          okText: '是',
          cancelText: '否',
          content: '是否取消选中数据?',
          onOk: function () {
            that.loading = true
            postParamsAction(that.url.uncollectBatch, { knowledgeIds: ids })
              .then((res) => {
                if (res.success) {
                  that.$message.success(res.message)
                  that.loadData()
                  that.onClearSelected()
                } else {
                  that.$message.warning(res.message)
                }
              })
              .finally(() => {
                that.loading = false
              })
          }
        })
      }
    },
    //单条取消收藏
    handleCancelCollect(id) {
      var that = this
      postParamsAction(that.url.uncollect, { knowledgeId: id }).then((res) => {
        if (res.success) {
          that.$message.success(res.message)
          that.loadData()
        } else {
          that.$message.warning(res.message)
          that.loadData()
        }
      })
    },
    // 查看
    handleDetailPage: function (record) {
      this.$emit('getRecord', record)
    }
  },
}

</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
</style>
