<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    :destroyOnClose="true"
    :centered="true"
    switchFullscreen
    :footer="null"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭"
    okText="保存">
    <div style='min-height: 80vh'>
      <transitionDetail :job-id="jobId"></transitionDetail>
    </div>
  </j-modal>
</template>

<script>
import transitionDetail from './transitionDetail.vue'
export default {
  name: 'transitionDetailModal',
  components: {
    transitionDetail,
  },
  data() {
    return {
      title: '执行详情',
      width: '80vw',
      visible: false,
      disableSubmit: false,
      record:{},
      jobId:"",
    }
  },
  methods: {
    add() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.add()
      })
    },
    open(record) {
      this.jobId = record.id;
      this.visible = true
    },
    close() {
      this.$emit('close')
      this.visible = false
      //this.$refs.realForm.selectInspectionType()
    },
    handleOk() {
      return
      this.$refs.realForm.submitForm()
    },
    submitCallback() {
      this.$emit('ok')
      this.visible = false
    },
    handleCancel() {
      this.close()
    },
  },
}
</script>
<style lang="less" scoped>
@import '~@assets/less/normalModal.less';
</style>