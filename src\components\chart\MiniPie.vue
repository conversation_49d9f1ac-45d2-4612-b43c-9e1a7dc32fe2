<template>
  <div>
    <v-chart :forceFit="true" :height="plotHeight" padding="0" :data="c1Data" :scale="scale">
      <v-coord type="theta"></v-coord>
      <v-tooltip :showTitle="false"></v-tooltip>
      <v-stack-interval position="percent" color="x"></v-stack-interval>
    </v-chart>
  </div>
</template>

<script>
const c1Data = [
  {
    item: 'aaa',
    count: 12.5,
  },
  {
    item: 'vvv',
    count: 27.5
  },
  {
    item: 'ccc',
    count: 60
  }
];
const DataSet = require('@antv/data-set');
export default {
  props: {
    dataSource: {
      type: Array,
      default: () => []
    },
    plotHeight: {
      type: Number,
      default: 80
    },
  },

  data() {
    return {
      c1Data,
      scale: [{
        dataKey: 'percent',
        min: 0,
        formatter: function formatter(text) {
          ;
          var Num = text * 100
          return Num + '%';
        }
      }],
    }
  },
  mounted() {
    this.c1Data = this.dataSet(this.dealData(this.dataSource))
  },
  watch: {
    dataSource: {
      handler(newVal) {
        this.c1Data = this.dataSet(this.dealData(newVal))
      },
      deep: true
    }
  },
  methods: {
    dealData(values) {
      const data = values.map((value, i) => {
        return {
          x: value.item,
          y: value.count
        }
      });
      return data;
    },
    dataSet(data) {
      const dv = new DataSet.View().source(data);
      dv.transform({
        type: 'percent',
        field: 'y',
        dimension: 'x',
        as: 'percent'
      });
      return dv
    }
  },
};
</script>
