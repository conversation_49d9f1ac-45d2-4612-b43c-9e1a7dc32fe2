<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll zxw">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <!-- 查询区域 -->
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="关系类型">
                  <a-input
                    :maxLength='maxLength'
                    placeholder="请输入关系类型"
                    :allowClear="true"
                    autocomplete="off"
                    v-model="queryParam.relation"
                  ></a-input>
                </a-form-item>
              </a-col>

              <a-col :span="colBtnsSpan()">
                <span
                  class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                >
                  <a-button type="primary" @click="searchQuery">查询</a-button>
                  <a-button @click="searchReset" style="margin-left: 10px">重置</a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <!-- 查询区域-END -->

      <a-card :bordered="false" style="flex: auto">
        <!-- 操作按钮区域 -->
        <div class="table-operator">
          <a-button @click="handleAdd" v-has="'assetsRelation:add'">新增</a-button>
          <a-button @click="handleExportXls('关系类型')" v-has="'assetsRelation:export'">导出</a-button>
          <a-button @click="handleTemplateXls()" v-has="'assetsRelation:import'">下载模版</a-button>
          <a-upload
            v-has="'assetsRelation:import'"
            name="file"
            :showUploadList="false"
            :multiple="false"
            :headers="tokenHeader"
            :action="importExcelUrl"
            @change="handleImportExcel"
          >
            <a-button>导入</a-button>
          </a-upload>
          <!-- 高级查询区域 -->
          <!-- <j-super-query :fieldList="superFieldList" ref="superQueryModal" @handleSuperQuery="handleSuperQuery"></j-super-query> -->
          <a-dropdown v-if="selectedRowKeys.length > 0" v-has="'assetsRelation:delete'">
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item key="1" @click="batchDel">删除</a-menu-item>
            </a-menu>
            <a-button> 批量操作 <a-icon type="down" /></a-button>
          </a-dropdown>
        </div>

        <!-- table区域-begin -->
        <div>
          <a-table
            ref="table"
            size="middle"
            bordered
            rowKey="id"
            :columns="columns"
            :dataSource="dataSource"
            :pagination="ipagination"
            :loading="loading"
            :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
            @change="handleTableChange"
          >
            <template slot="htmlSlot" slot-scope="text">
              <div v-html="text"></div>
            </template>
            <template slot="imgSlot" slot-scope="text">
              <span v-if="!text" style="font-size: 14px">无图片</span>
              <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width: 80px; font-size: 14px" />
            </template>
            <template slot="fileSlot" slot-scope="text">
              <span v-if="!text" style="font-size: 14px">无文件</span>
              <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
                下载
              </a-button>
            </template>

            <span slot="action" slot-scope="text, record">
              <a @click="handleEdit(record)" v-has="'assetsRelation:edit'">编辑</a>
              <span v-has="'assetsRelation:delete'">
                 <a-divider type="vertical" />
                 <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                   <a>删除</a>
              </a-popconfirm>
              </span>
            </span>
            <template slot="tooltip" slot-scope="text">
              <a-tooltip placement="topLeft" :title="text" trigger="hover">
                <div class="tooltip">
                  {{ text }}
                </div>
              </a-tooltip>
            </template>
          </a-table>
        </div>

        <cmdb-relation-modal ref="modalForm" @ok="modalFormOk"></cmdb-relation-modal>
        <iframe id="download" style="display: none"></iframe>
      </a-card>
    </a-col>
  </a-row>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import CmdbRelationModal from './modules/CmdbRelationModal'
import { filterMultiDictText } from '@/components/dict/JDictSelectUtil'
import JSuperQuery from '@/components/jeecg/JSuperQuery.vue'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'

export default {
  name: 'CmdbRelationList',
  mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
  components: {
    CmdbRelationModal,
    JSuperQuery,
  },
  data() {
    return {
      maxLength:50,
      description: '关系类型管理页面',
      // 表头
      columns: [
        {
          title: '关系类型',
          dataIndex: 'relation',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width:100px;max-width:300px'
            return { style: cellStyle }
          },
        },
        {
          title: '反向关系类型',
          dataIndex: 'relationReverse',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width:120px;max-width:300px'
            return { style: cellStyle }
          },
        },
        {
          title: '描述',
          dataIndex: 'descs',
          scopedSlots: { customRender: 'tooltip' },
          customCell: () => {
            let cellStyle = 'text-align: left;min-width:100px;max-width:400px'
            return { style: cellStyle }
          },
        },
        {
          title: '操作',
          dataIndex: 'action',
          fixed: 'right',
          align: 'center',
          width: 147,
          scopedSlots: { customRender: 'action' },
        },
      ],
      url: {
        list: '/relation/cmdbRelation/list',
        delete: '/relation/cmdbRelation/delete',
        deleteBatch: '/relation/cmdbRelation/deleteBatch',
        exportXlsUrl: '/relation/cmdbRelation/exportXls',
        importExcelUrl: '/relation/cmdbRelation/importExcel',
        downloadTemplateXlsUrl: '/relation/cmdbRelation/downloadTemplate',
      }
    }
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
    downloadTemplateXlsUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.downloadTemplateXlsUrl}`
    },
  },
  methods: {
    //excel模板
    handleTemplateXls() {
      const path = this.downloadTemplateXlsUrl
      document.getElementById('download').src = path
    }
  }
}
</script>
<style lang="less" scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
</style>
