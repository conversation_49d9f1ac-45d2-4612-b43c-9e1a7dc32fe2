<template>
  <div class="content">
    <!-- 历史告警 -->
    <a-table
      ref="table"
      bordered
      :columns="columns"
      :dataSource="dataSource"
      :pagination="false"
      :loading="loading"
      :rowKey="(record, index) => record.id"
      :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
    >
      <template slot="alarmLevel" slot-scope="text,record">
        <span class="level" :style="{backgroundColor:getAlarmColor(record)}"></span>
        {{ getAlarmTitle(record) }}
      </template>
      <template slot="tooltip" slot-scope="text">
        <a-tooltip placement="topLeft" :title="text" trigger="hover">
          <div class="tooltip">{{ text }}</div>
        </a-tooltip>
      </template>
    </a-table>
    <!-- <div class="right-body">
      <div class="right-tableTitle">
        <span>告警时间</span>
        <span>设备名称</span>
        <span>告警级别</span>
        <span>告警名称</span>
      </div>
      <div class="right-table">
        <div class="seamless-warp">
          <div v-for="(array, index) in warningData" :key="index" class="tr">
            <div class="td">{{ array.create_time || ''}}</div>
            <div class="td">{{ array.device_name || ''}}</div>
            <div class="td">{{ array.levelName || ''}}</div>
            <div class="td">{{ array.template_name || ''}}</div>
          </div>
        </div>
      </div>
    </div>-->
  </div>
</template>
<script>
import { getAction } from '@/api/manage'
import { dataAndFunction } from '@views/alarmManage/modules/dataAndFunction'
export default {
  mixins: [dataAndFunction],
  data() {
    return {
      url: {
        list: '/alarm/alarmHistory/list'
      },
      loading: false,
      dataSource: [],
      columns: [
        {
          title: '告警名称',
          dataIndex: 'templateName',
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
            return {
              style: cellStyle
            }
          },
          scopedSlots: { customRender: 'tooltip' }
        },
        {
          title: '设备名称',
          dataIndex: 'deviceName',
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
            return {
              style: cellStyle
            }
          },
          scopedSlots: { customRender: 'tooltip' }
        },
        {
          title: '告警级别',
          dataIndex: 'alarmLevel',
          scopedSlots: {
            customRender: 'alarmLevel'
          },
          customCell: () => {
            let cellStyle = 'text-align: center;width:160px;'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '告警时间',
          dataIndex: 'alarmTime1',
          customCell: () => {
            let cellStyle = 'text-align: center;width:180px;'
            return {
              style: cellStyle
            }
          }
        }
      ]
    }
  },
  props: {
    timeData: {
      type: Object,
      default: () => {}
    }
  },
  watch: {
    timeData: {
      deep: true,
      handler(val) {
        this.getData()
      }
    }
  },
  mounted() {
    this.getAlarmLevelData()
    this.getData()
  },
  methods: {
    getData(startTime, endTime) {
      this.loading = true
      getAction(this.url.list, {
        alarmTime2_begin: this.timeData.startTime?this.timeData.startTime:null,
        alarmTime2_end: this.timeData.endTime?this.timeData.endTime:null,
        pageSize: 10,
        pageNo: 1
      }).then(res => {
        if (res.code == 200) {
          this.loading = false
          this.dataSource = res.result.records
        }
      })
    }
  }
}
</script>
<style scoped lang="less">
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
.level {
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 3px;
  margin-right: 7px;
  margin-bottom: 2px;
}
::v-deep .ant-table-thead > tr > th, ::v-deep .ant-table-tbody > tr > td{
  padding: 0.15rem 0.2rem;
}
// .right-body {
//   width: 100%;
//   height: 100%;
//   padding: 0 0.125rem /* 10/80 */;

//   .right-tableTitle {
//     display: flex;
//     height: 0.575rem; //46/80
//     align-items: center;
//     background: #edf1f4;
//     border: 1px solid #e8e8e8;

//     span {
//       font-size: 0.175rem; //14/80
//       color: rgba(0, 0, 0, 0.85);
//       width: 100%;
//       text-align: center;
//     }
//   }

//   .right-table {
//     width: 100%;
//     height: calc(100% - 0.375rem); //30/80
//     overflow-x: auto;

//     .seamless-warp {
//       width: 100%;
//       height: 100%;

//       .tr {
//         align-items: center;
//         width: 100%;
//         height: 0.575rem; //46/80
//         line-height: 0.525rem;
//         display: flex;
//         justify-content: space-around;
//         border-top: 1px solid #e8e8e8;
//         border-left: 1px solid #e8e8e8;
//         border-right: 1px solid #e8e8e8;

//         .td {
//           width: 25%;
//           color: #909399;
//           font-size: 0.175rem; //14/80
//           text-align: center;
//           border-right: 1px solid #e8e8e8;
//           &:last-child {
//             border-right: none !important;
//           }
//         }
//         &:first-child {
//           border-top: none !important;
//         }
//         &:last-child {
//           border-bottom: 1px solid #e8e8e8;
//         }
//       }
//     }
//   }
// }
</style>