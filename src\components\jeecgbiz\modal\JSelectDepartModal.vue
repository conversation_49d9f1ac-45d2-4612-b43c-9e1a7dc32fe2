<template>
  <j-modal
    title='选择部门'
    :width='modalWidth'
    :visible='visible'
    :confirmLoading='confirmLoading'
    @ok='handleSubmit'
    @cancel='handleCancel'
    switchFullscreen
    cancelText='关闭'>
    <a-spin tip='Loading...' :spinning='false'>
      <a-input-search style='margin-bottom: 1px' placeholder='请输入内容按回车进行搜索' @search='onSearch' />
      <a-tree
        checkable
        class='my-dept-select-tree'
        :treeData='treeData'
        :checkStrictly='checkStrictly'
        @check='onCheck'
        @select='onSelect'
        @expand='onExpand'
        :autoExpandParent='autoExpandParent'
        :expandedKeys='expandedKeys'
        :checkedKeys='checkedKeys'
        :selectedKeys='[]'
      >
        <template slot='title' slot-scope='{title}'>
          <span v-if='title.indexOf(searchValue) > -1'>
            {{ title.substr(0, title.indexOf(searchValue)) }}
            <span style='color: #f50'>{{ searchValue }}</span>
            {{ title.substr(title.indexOf(searchValue) + searchValue.length) }}
          </span>
          <span v-else>{{ title }}</span>
        </template>
      </a-tree>
    </a-spin>
    <template slot='footer' v-if='treeAction'>
      <div style='display: flex;justify-content: space-between'>
        <a-dropdown style='float: left' :trigger="['click']" placement='topCenter'>
          <a-menu slot='overlay'>
            <a-menu-item v-if='multi && checkStrictly' key='1' @click='switchCheckStrictly(1)'>父子关联</a-menu-item>
            <a-menu-item v-if='multi && !checkStrictly' key='2' @click='switchCheckStrictly(2)'>取消关联</a-menu-item>
            <a-divider v-if='multi' style='margin: 8px 0' />
            <a-menu-item v-if='multi' key='3' @click='checkALL'>全部勾选</a-menu-item>
            <a-menu-item v-if='multi' key='4' @click='cancelCheckALL'>取消全选</a-menu-item>
            <a-divider v-if='multi' style='margin: 8px 0' />
            <a-menu-item key='5' @click='expandAll'>展开所有</a-menu-item>
            <a-menu-item key='6' @click='closeAll'>合并所有</a-menu-item>
          </a-menu>
          <a-button>
            树操作
            <a-icon type='up' />
          </a-button>
        </a-dropdown>
        <div>
          <a-button key='back' @click='handleCancel'>
            取消
          </a-button>
          <a-button key='submit' type='primary' :loading='confirmLoading' @click='handleSubmit'>
            确定
          </a-button>
        </div>
      </div>

    </template>
  </j-modal>
</template>

<script>
import { queryDepartTreeList } from '@/api/api'

export default {
  name: 'JSelectDepartModal',
  props: ['modalWidth', 'multi', 'rootOpened', 'departId','treeAction'],
  data() {
    return {
      visible: false,
      confirmLoading: false,
      treeData: [],
      autoExpandParent: true,
      expandedKeys: [],//展开key
      dataList: [],
      checkedKeys: [],
      checkedRows: [],
      allTreeKeys: [],
      searchValue: '',
      checkStrictly: true//true 关闭父子关联 false 开启父子关联
    }
  },
  created() {
    this.loadDepart()
  },
  watch: {
    departId() {
      this.initDepartComponent()
    },
    visible: {
      handler() {
        if (this.departId) {
          this.checkedKeys = this.departId.split(',')
        } else {
          this.checkedKeys.splice(0)
        }
        this.checkedRows = this.getCheckedRows(this.checkedKeys)
      }
    }
  },
  methods: {
    //树形选择器父子关联操作
    switchCheckStrictly(v) {
      if (v == 1) {
        this.checkStrictly = false
        let relateIds = []
        if(this.checkedKeys.length>0){
          this.checkedKeys.forEach(key=>{
            let tem = this.dataList.find(el => el.id === key)
            if (tem && tem.children && Array.isArray(tem.children)) {
              this.getChildIds(tem.children, relateIds)
            }
            if(tem && tem.parentId && !relateIds.includes(tem.parentId)){
              this.parentStatusChecked(tem.parentId,relateIds)
            }
          })
          this.checkedKeys.push(...relateIds)
          const keySet = new Set(this.checkedKeys)
          this.checkedKeys = [...keySet]
          this.checkedRows = this.getCheckedRows(this.checkedKeys)
        }

      } else if (v == 2) {
        this.checkStrictly = true
      }
    },
    //展开所有
    expandAll() {
      this.expandedKeys = this.allTreeKeys
    },
    //合并所有
    closeAll() {
      this.expandedKeys = []
    },
    //
    checkALL() {
      this.checkedKeys = Array.from(new Set(this.allTreeKeys))
      this.checkedRows = this.getCheckedRows(this.checkedKeys)
    },
    //取消全选
    cancelCheckALL() {
      this.checkedKeys = []
      this.checkedRows = []
    },
    show() {
      this.checkStrictly=true
      this.checkedRows = []
      this.checkedKeys = []
      this.visible = true
    },
    loadDepart() {
      queryDepartTreeList().then(res => {
        if (res.success) {
          let arr = [...res.result]
          this.reWriterWithSlot(arr)
          this.allTreeKeys = this.dataList.map(el => el.id)
          this.treeData = arr
          this.initDepartComponent()
          if (this.rootOpened) {
            this.initExpandedKeys(res.result)
          }
        }
      })
    },
    initDepartComponent() {
      let names = ''
      if (this.departId) {
        let currDepartId = this.departId
        for (let item of this.dataList) {
          if (currDepartId.indexOf(item.key) >= 0) {
            names += ',' + item.title
          }
        }
        if (names) {
          names = names.substring(1)
        }
      }
      this.$emit('initComp', names)
    },
    reWriterWithSlot(arr) {
      for (let item of arr) {
        if (item.children && item.children.length > 0) {
          this.reWriterWithSlot(item.children)
          let temp = Object.assign({}, item)
          // temp.children = {}
          this.dataList.push(temp)
        } else {
          this.dataList.push(item)
          item.scopedSlots = { title: 'title' }
        }
      }
    },
    getChildIds(arr, ids) {
      arr.forEach(el => {
        ids.push(el.id)
        if (el.children && Array.isArray(el.children)) {
          this.getChildIds(el.children, ids)
        }
      })
    },
    findParent(id,ids = []){
      let node = this.dataList.find(el=>el.id===id)
      if(node && node.parentId){
        ids.push(node.parentId)
        this.findParent(node.parentId,ids)
      }
    },
    parentStatusChecked(pid,ids = []){
      //父级
      let node = this.dataList.find(el=>el.id===pid)
      if(node && Array.isArray(node.children)){
        //检查父级的所有子元素是否都选中了
        let exclude = node.children.find(el=>!this.checkedKeys.includes(el.id))
        if(!exclude && !ids.includes(pid)){
          ids.push(pid)
          if(node.parentId){
            this.parentStatusChecked(node.parentId,ids)
          }
        }

      }
    },
    initExpandedKeys(arr) {
      if (arr && arr.length > 0) {
        let keys = []
        for (let item of arr) {
          if (item.children && item.children.length > 0) {
            keys.push(item.id)
          }
        }
        this.expandedKeys = [...keys]
      } else {
        this.expandedKeys = []
      }
    },
    //点击复选框触发
    onCheck(checkedKeys, info) {
      // console.log('onCheck', this.checkedKeys, checkedKeys, info)
      if (!this.multi) {
        let arr = checkedKeys.checked.filter(item => this.checkedKeys.indexOf(item) < 0)
        this.checkedKeys = [...arr]
        this.checkedRows = (this.checkedKeys.length === 0) ? [] : [info.node.dataRef]
      } else {
        this.checkedKeys = this.checkStrictly ? checkedKeys.checked : checkedKeys
        this.checkedRows = this.getCheckedRows(this.checkedKeys)
      }
    },
    /*
    *点击树形节点触发
    * 确保数组组件selectedKeys 属性一直是空数组
    */
    onSelect(selectedKeys, info) {
      let key = selectedKeys[0]
      // console.log('onSelect', key, info)
      //单选时
      if (!this.multi) {
        if (this.checkedKeys[0] !== key) {
          this.checkedKeys = [key]
          this.checkedRows = [info.node.dataRef]
        } else {
          this.checkedKeys = []
          this.checkedRows = []
        }
      } else {
        //多选时没有开启父子关联
        if (this.checkStrictly) {
          if (this.checkedKeys.indexOf(key) >= 0) {
            this.checkedKeys = this.checkedKeys.filter(item => item !== key)
          } else {
            this.checkedKeys.push(key)
          }
        } else {
          //多选时没有关闭父子关联
          let relateIds = []
          //改节点下的所有子节点
          let tem = this.dataList.find(el => el.id === key)
          if (tem && tem.children && Array.isArray(tem.children)) {
            this.getChildIds(tem.children, relateIds)
          }
          relateIds.unshift(key)
          let temKeys = [...this.checkedKeys]
          if (temKeys.indexOf(key) >= 0) {//清除选中
            let pids = []
            //同时清除节点的父级节点
            this.findParent(key,pids)
            this.checkedKeys  = temKeys.filter(item => !relateIds.includes(item) && !pids.includes(item))
          } else {
            this.checkedKeys = temKeys.concat(relateIds)
            let pid = this.dataList.find(el=>el.id === key)?.parentId
            if(pid){
              let pids = []
              //父级节点的字节的都选择时 父级节点也时选中状态
              this.parentStatusChecked(pid,pids)
              this.checkedKeys = this.checkedKeys.concat(pids)
            }

          }
          const keySet = new Set(this.checkedKeys)
          this.checkedKeys = [...keySet]
        }
      }
      this.checkedRows = this.getCheckedRows(this.checkedKeys)
    },
    onExpand(expandedKeys) {
      this.expandedKeys = expandedKeys
      this.autoExpandParent = false
    },
    handleSubmit() {
      if (!this.checkedKeys || this.checkedKeys.length == 0) {
        this.$emit('ok', '')
      } else {
        this.$emit('ok', this.checkedRows, this.checkedKeys.join(','))
      }
      this.handleClear()
    },
    handleCancel() {
      this.handleClear()
    },
    handleClear() {
      this.checkStrictly=true
      this.visible = false
      //this.checkedKeys=[]
    },
    getParentKey(currKey, treeData) {
      let parentKey
      for (let i = 0; i < treeData.length; i++) {
        const node = treeData[i]
        if (node.children) {
          if (node.children.some(item => item.key === currKey)) {
            parentKey = node.key
          } else if (this.getParentKey(currKey, node.children)) {
            parentKey = this.getParentKey(currKey, node.children)
          }
        }
      }
      return parentKey
    },
    onSearch(value) {
      const expandedKeys = this.dataList.map((item) => {
        if (item.title.indexOf(value) > -1) {
          return this.getParentKey(item.key, this.treeData)
        }
        return null
      }).filter((item, i, self) => item && self.indexOf(item) === i)

      Object.assign(this, {
        expandedKeys,
        searchValue: value,
        autoExpandParent: true
      })


    },
    // 根据 checkedKeys 获取 rows
    getCheckedRows(checkedKeys) {
      const forChildren = (list, key) => {
        for (let item of list) {
          if (item.id === key) {
            return item
          }
          if (item.children instanceof Array) {
            let value = forChildren(item.children, key)
            if (value != null) {
              return value
            }
          }
        }
        return null
      }

      let rows = []
      for (let key of checkedKeys) {
        let row = forChildren(this.treeData, key)
        if (row != null) {
          rows.push(row)
        }
      }
      return rows
    }
  }
}

</script>

<style lang='less' scoped>
// 限制部门选择树高度，避免部门太多时点击确定不便
.my-dept-select-tree {
  height: 350px;
  overflow-y: scroll;
}

</style>