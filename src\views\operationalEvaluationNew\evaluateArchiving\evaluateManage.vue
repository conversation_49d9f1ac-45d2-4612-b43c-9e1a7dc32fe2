<template>
  <div style="height:100%">
    <component :is="pageName" :data="data" />
  </div>
</template>
<script>
  import evaluateList from './evaluateList'
  import evaluateDetail from './evaluateDetail'
  export default {
    name: "evaluateManage",
    data() {
      return {
        isActive: 0,
        data: {}
      };
    },
    components: {
      evaluateList,
      evaluateDetail
    },
    created() {
      this.pButton1(0);
    },
    //使用计算属性
    computed: {
      pageName() {
        switch (this.isActive) {
          case 0:
            return "evaluateList";
            break;
          default:
            return "evaluateDetail";
            break;
        }
      }
    },
    methods: {
      pButton1(index) {
        this.isActive = index;
      },
      pButton2(index, item) {

        this.isActive = index;
        this.data = item;
      },

    }
  }
</script>
<style scoped>
  .div {
    display: flex;
    margin-right: 10px;
    height: 30px;
    line-height: 30px;
    padding-left: 25px;
    width: 6px;
  }
</style>