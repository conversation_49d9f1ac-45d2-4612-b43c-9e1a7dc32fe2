<template>
  <div class='zr-business-view' ref='zrBusinessView'>
    <div class='business-view-left'>
      <div class='business-view-left-top' ref='lefTop'>
        <zr-comp-businesses></zr-comp-businesses>
      </div>
      <div v-if='leftBottomH' class='business-view-left-bottom' :style='{height:leftBottomH}'>
        <div class='business-view-left-middle'>
          <zr-comp-support></zr-comp-support>
        </div>
        <div class='business-view-left-bottom'>
          <zr-comp-hardware></zr-comp-hardware>
        </div>
      </div>
    </div>
    <div class='business-view-center'>
      <zr-status-info></zr-status-info>
      <div class='topo-box'>
        <vis-edit ref='visEdit' :blurId='blurId' operate='show'></vis-edit>
      </div>
      <div class='legend-block'>
        <zr-focus-info scene='unitNode'></zr-focus-info>
        <div class='legend-block-list'>
          <div class='legend-block-list-item' v-for='item in businessStatus' :key='item.value'>
            <div class='legend-block-list-item-icon' :style='{backgroundColor:item.color}'></div>
            <div class='legend-block-list-item-text'>{{item.label}}</div>
          </div>
        </div>
      </div>
    </div>
    <div class='business-view-right'>
      <div ref='rightTop'>
        <div class='business-view-right-top' ref='businessPosition'>
          <zr-comp-position :topoId='topoId' @changeBlur='changeBlur'></zr-comp-position>
        </div>
        <div class='business-view-right-middle' ref='businessManager'>
          <zr-comp-manager></zr-comp-manager>
        </div>
      </div>
      <div v-if='rightBottomH' class='business-view-right-bottom' ref='businessOperations'
           :style='{height:rightBottomH}'>
        <zr-comp-operations></zr-comp-operations>
      </div>
    </div>
  </div>
</template>
<script>
import resizeObserverMixin from '@views/statsCenter/com/resizeObserverMixin'
import ZrCompBusinesses from '@views/zrBigscreenStatic/zrComprehensive/modules/ZrCompBusinesses.vue'
import ZrCompHardware from '@views/zrBigscreenStatic/zrComprehensive/modules/ZrCompHardware.vue'
import ZrCompSupport from '@views/zrBigscreenStatic/zrComprehensive/modules/ZrCompSupport.vue'
// import ZrCompPosition from '@views/zrBigscreenStatic/zrComprehensive/modules/ZrCompPosition.vue'
import ZrCompPosition from '@views/zrBigscreenStatic/zrBusiness/modules/ZrBusinessPosition.vue'
import ZrCompManager from '@views/zrBigscreenStatic/zrComprehensive/modules/ZrCompManager.vue'
import ZrCompOperations from '@views/zrBigscreenStatic/zrComprehensive/modules/ZrCompOperations.vue'
import VisEdit from '@views/topo/nettopo/modules/VisEdit.vue'
import { businessStatus } from '@views/zrBigscreenStatic/modules/zrUtil'
import { organizations } from '@/views/zrBigscreens/modules/zrOrganizations'
import { flatTreeData } from '@/utils/util'
import ZrStatusInfo from '@views/zrBigscreenStatic/zrCompNational/modules/ZrStatusInfo.vue'
import ZrFocusInfo from '@views/zrBigscreenStatic/zrCompNational/modules/ZrFocusInfo.vue'
import ZrBusinessPosition from '@views/zrBigscreenStatic/zrBusiness/modules/ZrBusinessPosition.vue'
export default {
  name: 'businessIndex',
  components: {
    ZrBusinessPosition,
    VisEdit,
    ZrCompBusinesses,
    ZrCompSupport,
    ZrCompHardware,
    ZrCompPosition,
    ZrCompManager,
    ZrCompOperations,
    ZrStatusInfo,
    ZrFocusInfo
  },
  mixins: [resizeObserverMixin],
  data() {
    return {
      centerH: '',
      centerPd: "",
      legndBottom:"",
      leftBottomH: 0,
      rightBottomH: 0,
      businessStatus,
      cityId: '',
      organizations:[],
      blurId:'',
      topoId: window.ZrOrgTopoId || '1853673361481719810' // 默认拓扑图ID
    }
  },
  created() {
    this.organizations = flatTreeData([organizations], null, [])
    this.cityId = this.$route.query.id
    this.cityData = this.organizations.find(item => item.id === this.cityId)
    if(this.cityData?.topoId) {
      this.topoId = this.cityData.topoId // 如果城市数据中有拓扑图ID则使用
    }
  },
  mounted() {
    this.$refs.visEdit.createTopo(this.topoId)
  },
  methods: {
    changeBlur(e){
      this.blurId = e
    },
    //监听页面缩放 更新中间区域高度
    resizeObserverCb() {
      // let hScaleValue = window.innerHeight / 1080
      // this.centerH = `calc(100% - (70px * ${hScaleValue}))`
      // this.centerPd = `calc(100px * ${hScaleValue})`
      // this.legndBottom = `calc(60px * ${hScaleValue})`
      let leftTopRect = this.$refs.lefTop.getBoundingClientRect()
      this.leftBottomH = `calc(100% - ${leftTopRect.height}px)`
      let rightTopRect = this.$refs.rightTop.getBoundingClientRect()
      this.rightBottomH = `calc(100% - ${rightTopRect.height}px)`
      // 重新计算拓扑图画布
      if (this.$refs.visEdit) {
        let timer = setTimeout(() => {
          clearTimeout(timer)
          timer = null
          this.$refs.visEdit.resizeTopo()
        }, 80)
      }

    }
  }
}
</script>

<style scoped lang='less'>
.zr-business-view {
  padding-left: calc(33 / 19.2 * 1vw);
  padding-right: calc(33 / 19.2 * 1vw);
  display: flex;
  align-items: center;
  justify-content: space-around;
  height: 100%;

  .business-view-left {
    width: 25%;
    height: 100%;
    //background-image: url(/zrBigScreen/zrBusiness/businessLeftBg.png);
    //background-size: 100% 100%;
    //background-repeat: no-repeat;
    padding: 0px 0px 0px 0px;
    display: flex;
    flex-direction: column;

    .business-view-left-bottom {
      display: flex;
      flex-direction: column;

      .business-view-left-middle {
        height: 50%
      }

      .business-view-left-bottom {
        height: 50%;
      }
    }
  }

  .business-view-center {
    width: 50%;
    height: 100%;
    /*background-image: url(/zrBigScreen/zrBusiness/businessCenterBg.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;*/
    padding: 0px 12px;
    position: relative;
    .topo-box{
      margin-top: 8px;
      height: calc(100% - 118px);
    }
    .legend-block {
      position: absolute;
      bottom: 0px;
      display: flex;
      justify-content: center;
      flex-direction: column;
      align-items: center;
      width:100%;
      .legend-block-list {
        display: flex;
        .legend-block-list-item {
          display: flex;
          align-items: center;
          font-weight: 400;
          font-size: 14px;
          padding: 0 20px;
          color: #E3E7EF;
          .legend-block-list-item-icon{
            width:14px;
            height: 14px;
            margin-right: 12px;
          }
        }
      }
    }
  }

  .business-view-right {
    width: 25%;
    height: 100%;
  /*  background-image: url(/zrBigScreen/zrBusiness/businessRightBg.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;*/
    padding: 0px 0px 0px 0px;
    display: flex;
    flex-direction: column;
  }
}
</style>