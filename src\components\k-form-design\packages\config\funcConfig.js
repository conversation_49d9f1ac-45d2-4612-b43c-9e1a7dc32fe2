import { loadTreeData, ajaxGetDictItems, getDictItemsFromCache } from '@/api/api'
const getDataDict = function (code) {
    //    ajaxGetDictItems(code).then(res=>{
    //     console.log("获取字典数据",res)
    // })
}
const getTreeData = function () {
    console.log("获取分类数据")
}
export const funcOptions = [
    {
        label: "获取字典数据",
        key: "getDataDict",
    },
    {
        label: "获取分类数据",
        key: "getTreeData",
    }
]
export const allFunc = {
    getDataDict,
    getTreeData,
}