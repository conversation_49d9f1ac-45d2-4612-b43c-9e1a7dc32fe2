<template>
  <j-modal
    :title="title"
    :width="1200"
    :visible='visible'
    :destroyOnClose="true"
    :centered='true'
    switchFullscreen
    :confirmLoading="confirmLoading"
    @cancel="handleCancel"
    cancelText="关闭"
  >
    <template slot="footer">
      <a-button key="close" @click="handleCancel"> 关闭 </a-button>
    </template>
    <a-tabs class='tab-border'  default-active-key="comments">
      <a-tab-pane  key="comments" tab="流程审批进度历史">
        <a-table
            class='tab-pane-box-padding'
            ref="table"
            size="small"
            bordered
            rowKey="id"
            :columns="columns"
            :dataSource="dataSource"
            :pagination="ipagination"
            @change="handleTableChange"
          >
          <span slot="status" slot-scope="status">
            <span v-if="status == 'done'" style="color: #52c41a">已办理</span>
            <span v-else style="color: #999999">待处理</span>
          </span>
          <span slot='overTime' slot-scope='text, record'>
              <span v-if="text" style="color:red">{{text}}</span>
              <span v-else style="color:green">正常</span>

            </span>
          <span slot="action" slot-scope="text, record">
            <a v-if="record.endTime == null" @click="urge(record)">催办</a>
          </span>
        </a-table>
      </a-tab-pane>
      <a-tab-pane key="processIntanceImage" tab="实时流程图">
        <div class="tab-pane-box-padding">
          <process-picture-real :row="processInstanceId"></process-picture-real>
          <!-- <img style="max-width: 1200px; max-height: 600px" :src="imagePath" alt="流程图" /> -->
        </div>
      </a-tab-pane>
    </a-tabs>

    <a-modal
      title="催办"
      :visible='urgeVisible'
      :maskClosable='false'
      :destroyOnClose='true'
      :confirm-loading="urgeConfirmLoading"
      @ok="handleUrge"
      @cancel="handleCancelUrge"
    >
      <a-form ref="form" :model="form" :label-width="85" :rules="formValidate">
        <a-form-item label="催办内容" prop="reason">
          <a-input v-model='form.urgemessage' :allow-clear='true' :rows='4' autocomplete='off' type='textarea' />
        </a-form-item>
        <!--        <a-form-item label="提醒方式">
                  <a-checkbox v-model="form.sendMessage">站内消息通知</a-checkbox>
                  <a-checkbox v-model="form.sendSms" disabled>短信通知</a-checkbox>
                  <a-checkbox v-model="form.sendEmail" disabled>邮件通知</a-checkbox>
                </a-form-item>-->
      </a-form>
    </a-modal>
  </j-modal>
</template>
<script>
import { getAction, postAction } from '@/api/manage'
import ProcessPictureReal from '../../process-instance/modules/ProcessPictureReal'

export default {
  name: 'ProcessHistoryModal',
  components: {
    ProcessPictureReal,
  },
  props: {},
  data() {
    return {
      urgeVisible: false,
      urgeConfirmLoading: false,
      form: {
        urgemessage: '请尽快办理！',
        sendMessage: true,
      },
      formValidate: {
        // 表单验证规则
      },
      title: '',
      visible: false,
      confirmLoading: false,
      /* 分页参数 */
      ipagination: {
        current: 1,
        pageSize: 5,
        pageSizeOptions: ['5', '10', '15'],
        showTotal: (total, range) => {
          return range[0] + '-' + range[1] + ' 共' + total + '条'
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0,
      },
      //表头
      //列设置
      settingColumns: [],
      //列定义
      columns: [
        {
          title: '序号',
          dataIndex: '',
          key: 'rowIndex',
          customCell:() =>{
            let cellStyle = 'text-align:center;width:60px;max-width:120px !important;min-width:60px'
            return {style:cellStyle}
          },
          customRender: function (t, r, index) {
            return parseInt(index) + 1
          },
        },
        {
          title: '任务名称',
          dataIndex: 'name',
          customCell:() =>{
            let cellStyle = 'text-align:center'
            return {style:cellStyle}
          },
        },
        {
          title: '处理人',
          dataIndex: 'assigneeName',
          customCell:() =>{
            let cellStyle = 'text-align:center'
            return {style:cellStyle}
          },
        },
        {
          title: '候选人',
          dataIndex: 'candidateUserNames',
          customRender: (text) => {
            //字典值替换通用方法
            let str = ''
            if (text == null) {
              return ''
            }
            text.forEach((element) => {
              str += element + ','
            })
            return str.substring(0, str.length - 1)
          },
          customCell:() =>{
            let cellStyle = 'text-align:center'
            return {style:cellStyle}
          },
        },
        // {
        //   title: '审批操作',
        //   align: 'center',
        //   dataIndex: 'commentType',
        //   customCell:() =>{
        //     let cellStyle = 'text-align:center'
        //     return {style:cellStyle}
        //   },
        // },
        // {
        //   title: '审批意见',
        //   align: 'center',
        //   dataIndex: 'commentFullMessage',
        //   customCell:() =>{
        //     let cellStyle = 'text-align:center'
        //     return {style:cellStyle}
        //   },
        // },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          customCell:() =>{
            let cellStyle = 'text-align:center;width:160px'
            return {style:cellStyle}
          },
        },
        {
          title: '到期时间',
          dataIndex: 'dueDate',
          isUsed: true,
          customCell: () => {
            let cellStyle = 'text-align:center;width:120px'
            return {
              style: cellStyle
            }
          },
          sorter: true
        },
        {
          title: '超时',
          dataIndex: 'overTime',
          isUsed: true,
          scopedSlots: {
            customRender: 'overTime'
          },
          customCell: () => {
            let cellStyle = 'text-align:center;width: 20px'
            return {
              style: cellStyle
            }
          },
          sorter: false
        },
        {
          title: '完成时间',
          dataIndex: 'endTime',
          customCell:() =>{
            let cellStyle = 'text-align:center;width:160px'
            return {style:cellStyle}
          },
        },
        {
          title: '状态',
          dataIndex: 'status',
          scopedSlots: { customRender: 'status' },
          customCell:() =>{
            let cellStyle = 'text-align:center;width:120px'
            return {style:cellStyle}
          },
        },
        {
          title: '操作',
          dataIndex: 'action',
          fixed:'right',
          width:70,
          align: 'center',
          scopedSlots: {
           /* filterDropdown: 'filterDropdown',
            filterIcon: 'filterIcon',*/
            customRender: 'action',
          },
        },
      ],
      dataSource: [],
      processInstanceId: null,
      task: {},
      url: {
        historylist: '/flowable/task/list',
        urge: '/flowable/task/urge',
      },
    }
  },
  created() {},
  methods: {
    urge(record) {
      this.urgeVisible = true
      this.task = record
    },
    handleUrge() {
      postAction(this.url.urge, {
        username: this.task.assignee,
        candidates: this.task.candidateUsers,
        msg: this.form.urgemessage,
        taskId: this.task.id,
      }).then((res) => {
        if (res.code == 200) {
          this.$message.success('催办成功')
          this.urgeVisible = false
          this.resetForm()
        } else {
          this.$message.error('催办失败')
          this.urgeVisible = false
          this.resetForm()
        }
      })
    },
    handleTableChange(pagination, filters, sorter) {
      //分页、排序、筛选变化时触发
      //TODO 筛选
      console.log(pagination)
      if (Object.keys(sorter).length > 0) {
        this.isorter.column = sorter.field
        this.isorter.order = 'ascend' == sorter.order ? 'asc' : 'desc'
      }
      this.ipagination = pagination
      //this.loadData();
    },
    resetForm() {
      this.form.urgemessage = '请尽快办理！'
    },
    handleCancelUrge() {
      this.urgeVisible = false
      this.resetForm()
    },
    init(processInstanceId) {
      this.processInstanceId = processInstanceId
      this.visible = true
      getAction(this.url.historylist, { processInstanceId: this.processInstanceId })
        .then((res) => {
          console.log(res)
          this.dataSource = res.result.records
        })
        .finally(() => {})
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleCancel() {
      this.dataSource = []
      this.close()
    },
  },
}
</script>
<style scoped lang='less'>
@import '~@assets/less/common.less';
@import '~@assets/less/YQCommon.less';
@import '~@assets/less/normalModal.less';
//在全屏模式下，实时流程图容器containers的高度样式如下；至于非全屏模式下，containers高度样式在流程图组件中设定
.j-modal-box.fullscreen{
  ::v-deep .containers {
    height: calc(100vh - 56px - 24px - 1px - 60px - 32px - 12px - 1px - 24px - 56px) !important;
  }
}
.tab-border{
  border: 1px solid #e8e8e8
}
.tab-pane-box-padding{
  padding: 0px 5px !important;
}
::v-deep .ant-table-tbody tr {
    height: 38px !important;
}
</style>