<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container>
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="12">
            <a-form-item label='任务名称' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <a-input v-decorator="['taskName', validatorRules.taskName]" placeholder='请输入任务名称'
                :allowClear='true' autocomplete='off' />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label='目标数据表' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <a-input v-decorator="['targetTable', validatorRules.targetTable]" placeholder='请输入目标数据表'
                :allowClear='true' autocomplete='off' />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label='实体类全类名' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <a-input v-decorator="['dataClass', validatorRules.dataClass]" placeholder='请输入实体类全类名'
                :allowClear='true' autocomplete='off' />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label='服务类全类名' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <a-input v-decorator="['serviceClass', validatorRules.serviceClass]" placeholder='请输入服务类全类名'
                :allowClear='true' autocomplete='off' />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label='唯一字段' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <a-input v-decorator="['uniqueColumn', validatorRules.uniqueColumn]" placeholder='请输入唯一字段'
                :allowClear='true' autocomplete='off' />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label='执行频率' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <j-cron v-decorator="['executeCron', validatorRules.executeCron]" @change="setCorn"></j-cron>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label='任务状态' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <a-radio-group v-decorator="['taskStatus', validatorRules.taskStatus]" button-style="solid">
                <a-radio-button value="0">
                  禁用
                </a-radio-button>
                <a-radio-button value="1">
                  启用
                </a-radio-button>
              </a-radio-group>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label='时间字段' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <a-input v-decorator="['timeColumn', validatorRules.timeColumn]" placeholder='请输入时间字段'
                       :allowClear='true' autocomplete='off' >
                <a-tooltip slot="suffix" title="时间字段、开始时间、结束时间不填写就是全量上报">
                  <a-icon type="info-circle" theme='twoTone' />
                </a-tooltip>
              </a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label='开始时间' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <a-input v-decorator="['startTime', validatorRules.startTime]" placeholder='请输入开始时间'
                       :allowClear='true' autocomplete='off' >
                <a-tooltip slot="suffix" title="时间字段、开始时间、结束时间不填写就是全量上报">
                  <a-icon type="info-circle" theme='twoTone' />
                </a-tooltip>
              </a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label='结束时间' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <a-input v-decorator="['endTime', validatorRules.endTime]" placeholder='请输入结束时间'
                       :allowClear='true' autocomplete='off' >
                <a-tooltip slot="suffix" title="时间字段、开始时间、结束时间不填写就是全量上报">
                  <a-icon type="info-circle" theme='twoTone' />
                </a-tooltip>
              </a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label='备注' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <a-textarea v-decorator="['description', validatorRules.description]" placeholder='请输入备注'
                :allowClear='true' autocomplete='off' />
            </a-form-item>
          </a-col>

        </a-row>
        <depart-window ref='departWindow' @ok='modalFormOk'></depart-window>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
  import {
    httpAction,
    getAction
  } from '@/api/manage'
  import pick from 'lodash.pick'
  import JFormContainer from '@/components/jeecg/JFormContainer'
  import departWindow from '@/views/system/modules/DepartWindow'

  export default {
    name: 'ReportingStatisticsForm',
    components: {
      JFormContainer,
      departWindow,
    },
    data() {
      return {
        form: this.$form.createForm(this),
        checkedDepartKeys: [],
        checkedDepartNames: [],
        selectedDepartKeys: [], //保存用户选择部门id
        checkedDepartNameString: '', // 保存部门的名称 =>title
        model: {},
        userId: null,
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          },
        },
        labelCol1: {
          xs: {
            span: 24
          },
          sm: {
            span: 6
          },
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          },
        },
        wrapperCol1: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          },
        },
        confirmLoading: false,
        validatorRules: {
          taskName: {
            rules: [{
                required: true,
                message: '请输入任务名称'
              },
              {
                max: 15,
                message: '最多15个字符',
                trigger: 'blur'
              }
            ]
          },
          targetTable: {
            rules: [{
              required: true,
              message: '请输入目标数据表'
            },  {
              max: 50,
              message: '最多50个字符',
              trigger: 'blur'
            }]
          },
          dataClass: {
            rules: [{
              required: true,
              message: '请输入实体类全类名'
            }, ]
          },
          serviceClass: {
            rules: [{
              required: true,
              message: '请输入实体类全类名'
            }, ]
          },
          uniqueColumn: {
            rules: [{
              required: true,
              message: '请输入唯一字段'
            }, ]
          },
          executeCron: {
            initialValue: '0 0 0 * * ? *',
            rules: [{
              required: true,
              validator: this.validateCorn
            }]
          },taskStatus: {
            initialValue: '0',
            rules: [{
              required: true,
              message: '请选择任务状态'
            }]
          },
          description: {
            rules: [
              {
                max: 250,
                message: '最多250个字符',
                trigger: 'blur'
              }
            ]
          },
        },
        url: {
          add: '/statis/reportTask/add',
          edit: '/statis/reportTask/edit',
        }
      }
    },
    methods: {
      onSearch() {
        this.$refs.departWindow.add(this.checkedDepartKeys, this.userId)
      },
      add() {
        this.edit({});
      },
      edit(record) {
        this.form.resetFields();
        this.model = Object.assign({}, record);
        this.visible = true;
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.model, 'taskName', 'executeCron', 'targetTable',
            'taskStatus', 'dataClass', 'serviceClass', 'uniqueColumn', 'timeColumn', 'startTime','endTime','description'))
        })
      },
      submitForm() {
        const that = this;
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if (!this.model.id) {
              httpurl += this.url.add;
              method = 'post';
            } else {
              httpurl += this.url.edit;
              method = 'put';
            }
            let formData = Object.assign(this.model, values);
            formData.levelType = '1'
            formData.selfDepartId = this.selectedDepartKeys.join(',')
            httpAction(httpurl, formData, method).then((res) => {
              if (res.success) {
                that.$message.success(res.message);
                that.$emit('ok');
              } else {
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }

        })
      },
      popupCallback(row) {
        this.form.setFieldsValue(pick(row, 'parentRequestUrl', 'parentServerAddress', 'parentDepartCode',
          'parentDepartName', 'selfDepartId'))
      },
      modalFormOk(formData) {
        let info = formData.departIdList
        if (info.length > 1) {
          this.$message.warning('请选择单个单位')
          return
        }
        this.checkedDepartNameString = ''
        this.checkedDepartNames = []
        this.checkedDepartKeys = []
        this.selectedDepartKeys = []
        this.checkedDepartKeys.push(info[0].key)
        this.selectedDepartKeys.push(info[0].key)
        this.checkedDepartNames.push(info[0].title)
        this.checkedDepartNameString = this.checkedDepartNames.join(',')
      },
      //周期执行
      setCorn(data) {
        if (data && data.target != null) {
          let dataList = data.target.value.split(' ')
          if (dataList[0] === '*') {
            this.$message.warning('请确认是否每秒都执行')
          }
        } else {
          let dataList = data.split(' ')
          if (dataList[0] === '*') {
            this.$message.warning('请确认是否每秒都执行')
          }
        }

        if (Object.keys(data).length === 0) {
          this.$message.warning('请输入cron表达式!')
        }
      },
      validateCorn(rule, value, callback) {
        if (rule.required) {
          if (value && value.length > 0) {
            if (value.split(' ').length>7){
              callback('cron表达式格式错误!')
            }else{
              this.getValidateCornTips(value).then((res)=>{
                callback(res)
              }).catch((err)=>{
                callback(err)
              })
            }
          } else {
            callback('请输入或选择cron表达式')
          }
        } else {
          if (value && value.length > 0) {
            if (value.split(' ').length>7){
              callback('cron表达式格式错误!')
            }else{
              this.getValidateCornTips(value).then((res)=>{
                callback(res)
              }).catch((err)=>{
                callback(err)
              })
            }
          } else {
            callback()
          }
        }
      },

      getValidateCornTips(value){
        return new Promise((resolve, reject)=>{
          getAction("/autoInspection/devopsAutoInspection/cronCheck", {
            cronExpression: value
          }).then((res) => {
            if (res.success) {
              resolve()
            } else {
              this.$message.warning(res.message)
              reject('cron表达式格式错误!')
            }
          }).catch((err)=>{
            this.$message.warning(err.message)
            reject('cron表达式格式错误!')
          })
        })
      },
    }
  }
</script>