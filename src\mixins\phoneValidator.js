
export const phoneValidator = {
  methods: {
    phone(rule, value, callback) {
      let reg = /(^(0[0-9]{2,3}\-)?([2-9][0-9]{6,7})+(\-[0-9]{1,4})?$)|(^((\d3)|(\d{3}\-))?(1[123456789]\d{9})$)/;
      if (!value || reg.test(value)) {
        callback();
      } else {
        callback('请输入正确的手机号或座机号');
      }
    },
    requiredPhone(rule, value, callback) {
      let reg = /(^(0[0-9]{2,3}\-)?([2-9][0-9]{6,7})+(\-[0-9]{1,4})?$)|(^((\d3)|(\d{3}\-))?(1[123456789]\d{9})$)/;
      if (value && reg.test(value)) {
        callback();
      } else {
        callback('请输入正确的手机号或座机号');
      }
    },
    fixedPhone(rule, value, callback) {
      let reg = /(^(0[0-9]{2,3}\-)?([2-9][0-9]{6,7})+(\-[0-9]{1,4})?$)|(^((\d3)|(\d{3}\-))?$)/;
      if (!value || reg.test(value)) {
        callback();
      } else {
        callback('请输入正确的座机号');
      }
    },
    mobilePhone(rule, value, callback) {
      let reg = /^(1[123456789]\d{9})$/;
      if (!value || reg.test(value)) {
        callback();
      } else {
        callback('请输入正确的手机号');
      }
    },
  }
}