/** init domain config */
import Vue from 'vue'
//设置全局API_BASE_URL
Vue.prototype.API_BASE_URL = process.env.VUE_APP_API_BASE_URL
// window._CONFIG['domianURL'] = //Vue.prototype.API_BASE_URL
// winldow._CONFIG['downoadUrl'] = window._CONFIG['domianURL'] + '/sys/common/download'
// //单点登录地址
// window._CONFIG['casPrefixUrl'] = process.env.VUE_APP_CAS_BASE_URL
// window._CONFIG['onlinePreviewDomainURL'] =  process.env.VUE_APP_ONLINE_BASE_URL
// window._CONFIG['staticDomainURL'] = Vue.prototype.API_BASE_URL + '/sys/common/static'
// window._CONFIG['pdfDomainURL'] = Vue.prototype.API_BASE_URL+ '/sys/common/pdf/pdfPreviewIframe'
if (window.config.multipleURL) {
    let keys = Object.keys(window.config.multipleURL)
    keys.forEach(el => {
        // if (process.env.NODE_ENV === 'production'){}
        let urlstr = window.config.multipleURL[el];
        if (!urlstr.startsWith("http://") && !urlstr.startsWith("https://")) {
            urlstr = document.location.origin + urlstr;
        }
        Vue.prototype[el] = urlstr
    })
   
}