<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    switchFullscreen
    :centered='true'
    :destroyOnClose="true"
    @ok="handleOk"
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @cancel="handleCancel"
    cancelText="关闭">
    <sys-notice-template-form ref="realForm" @ok="submitCallback" :disabled="disableSubmit"></sys-notice-template-form>
  </j-modal>
</template>

<script>

  import SysNoticeTemplateForm from './SysNoticeTemplateForm'
  export default {
    name: 'SysNoticeTemplateModal',
    components: {
      SysNoticeTemplateForm
    },
    data () {
      return {
        title:'',
        width:'800px',
        visible: false,
        disableSubmit: false
      }
    },
    methods: {
      add () {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.add();
        })
      },
      edit (record) {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.edit(record);
        })
      },
      close () {
        this.$emit('close');
        this.visible = false;
      },
      handleOk () {
        this.$refs.realForm.submitForm();
      },
      submitCallback(){
        this.$emit('ok');
        this.visible = false;
      },
      handleCancel () {
        this.close()
      }
    }
  }
</script>
<style scoped lang='less'>
@import '~@assets/less/normalModal.less';
</style>