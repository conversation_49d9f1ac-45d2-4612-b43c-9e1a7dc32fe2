<template>
  <div style='height: 100%'>
    <keep-alive exclude='AlarmInfoDetails,DeviceInfoModal,AlarmTemplateInfoModal'>
      <component
        :is='pageName'
        :alarm-info='data'
        :data='data'
        :is-editing='true'
        :render-states='renderStates'
        :show-flag='true'
        :flow-view='true'
      />
    </keep-alive>
  </div>
</template>
<script>
import AlarmTaskList from './AlarmTaskList.vue'
/*告警信息查看*/
import AlarmInfoDetails from '@views/alarmManage/modules/AlarmInfoDetails.vue'
/*设备查看*/
import DeviceInfoModal from '@views/devicesystem/deviceshow/DeviceInfoModal'
/*告警策略查看*/
import AlarmTemplateInfoModal from '@comp/alarmTemplate/AlarmTemplateInfoModal'

export default {
  name: 'AlarmTaskManage',
  props: {},
  data() {
    return {
      isActive: 0,
      data: {},
      renderStates:{
        showBaseInfo:true,
        showStateInfo:true,
        showDeviceFunction:true,
        showJournal:true,
        showAlarm:true,
        showDeviceAlarm:true,
      }
    }
  },
  components: {
    AlarmTaskList,
    AlarmInfoDetails,
    // DeviceAlarmInfoModal,
    DeviceInfoModal,
    AlarmTemplateInfoModal
  },
  created() {
    this.pButton1(0)
  },
  //使用计算属性
  computed: {
    pageName() {
      switch (this.isActive) {
        case 0:
          return 'AlarmTaskList'
          break
        case 1:
          return 'AlarmInfoDetails'
          break
        case 2:
          return 'DeviceInfoModal'
          break
        default:
          return  'AlarmTemplateInfoModal'
          break
      }
    }
  },
  methods: {
    pButton1(index) {
      this.isActive = index
    },
    pButton2(index, item) {
      this.isActive = index
      this.data = item//告警信息
    }
  }
}
</script>