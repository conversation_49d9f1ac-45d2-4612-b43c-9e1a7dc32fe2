<template>
  <j-modal
    :title="title"
    :width="modalWidth"
    :visible="visible"
    :confirmLoading="confirmLoading"
    switchFullscreen
    :destroyOnClose='true'
    :centered="true"
    @cancel="handleCancel"
    @ok="handleOk"
    okText=""
    cancelText="关闭"
  >
     <a-form :form='form'>
        <a-row :gutter='24'>
          <a-col v-bind='formItemLayout'>
          <a-form-item class="two-words" :labelCol="labelCol"
                       :wrapperCol="wrapperCol"
                       label="省份"
                       >
            <a-input placeholder="请输入省份"
                     v-decorator="['parentRegion']"
                     :allowClear='true'
                     autocomplete='off'/>
          </a-form-item>
        </a-col>
        <a-col v-bind='formItemLayout'>
          <a-form-item class="two-words" :labelCol="labelCol"
                       :wrapperCol="wrapperCol"
                       label="地区"
                       >
            <!-- <a-input placeholder="请输入"
                     v-decorator="['region']" /> -->
                     <yq-area-cascader-select
                       placeholder="请选择地区"
                       v-decorator="['region', formValidator.region]">
                     </yq-area-cascader-select>
                    
          </a-form-item>
        </a-col>
        <a-col v-bind='formItemLayout'>
          <a-form-item class="two-words"
                       :labelCol="labelCol"
                       :wrapperCol="wrapperCol"
                       label="单位"
                      >
            <a-input placeholder="请输入单位"
                     v-decorator="['company',formValidator.company]"
                     :allowClear='true'
                     autocomplete='off'
                      />
          </a-form-item>
        </a-col>
        <a-col v-bind='formItemLayout'>
          <a-form-item class="two-words" :labelCol="labelCol"
                       :wrapperCol="wrapperCol"
                       label="年份"
                       >
            <!-- <a-year-picker placeholder="请选择年份" v-decorator="['year']"/> -->
            <a-input placeholder="请输入年份"
                     v-decorator="['year',formValidator.year]"
                     :allowClear='true'
                     autocomplete='off'
                      />
          </a-form-item>
        </a-col>
        <a-col v-bind='formItemLayout'>
          <a-form-item :labelCol="labelCol"
                       :wrapperCol="wrapperCol"
                       label="总应下发数"
                       >
            <a-input-number placeholder="请输入总应下发数" style="width:100%"
                     v-decorator="['totalDown']"
                            :allowClear='true'
                            autocomplete='off'
                      />
          </a-form-item>
        </a-col>
        <a-col v-bind='formItemLayout'>
          <a-form-item :labelCol="labelCol"
                       :wrapperCol="wrapperCol"
                       label="总应替换数"
                      >
            <a-input-number placeholder="请输入总应替换数" style="width:100%"
                     v-decorator="['totalReplace']"
                            :allowClear='true'
                            autocomplete='off'
                      />
          </a-form-item>
        </a-col>
        <a-col v-bind='formItemLayout'>
          <a-form-item :labelCol="labelCol"
                       :wrapperCol="wrapperCol"
                       label="服务器应下发数"
                       >
            <a-input-number placeholder="请输入服务器应下发数" style="width:100%"
                     v-decorator="['serverDown']"
                            :allowClear='true'
                            autocomplete='off'
                      />
          </a-form-item>
        </a-col>
        <a-col v-bind='formItemLayout'>
          <a-form-item :labelCol="labelCol"
                       :wrapperCol="wrapperCol"
                       label="服务器应替换数"
                       >
            <a-input-number placeholder="请输入服务器应替换数" style="width:100%"
                     v-decorator="['serverReplace']"
                            :allowClear='true'
                            autocomplete='off'
                      />
          </a-form-item>
        </a-col>
        <a-col v-bind='formItemLayout'>
          <a-form-item :labelCol="labelCol"
                       :wrapperCol="wrapperCol"
                       label="终端应下发数"
                       >
            <a-input-number placeholder="请输入终端应下发数" style="width:100%"
                     v-decorator="['terminalDown']"
                            :allowClear='true'
                            autocomplete='off'
                      />
          </a-form-item>
        </a-col>
        <a-col v-bind='formItemLayout'>
          <a-form-item :labelCol="labelCol"
                       :wrapperCol="wrapperCol"
                       label="终端应替换数"
                       >
            <a-input-number placeholder="请输入终端应替换数" style="width:100%"
                     v-decorator="['terminalReplace']"
                            :allowClear='true'
                            autocomplete='off'
                      />
          </a-form-item>
        </a-col>
        <a-col v-bind='formItemLayout'>
          <a-form-item :labelCol="labelCol"
                       :wrapperCol="wrapperCol"
                       label="打印机应下发数"
                       >
            <a-input-number placeholder="请输入打印机应下发数" style="width:100%"
                     v-decorator="['printerDown']"
                            :allowClear='true'
                            autocomplete='off'
                      />
          </a-form-item>
        </a-col>
        <a-col v-bind='formItemLayout'>
          <a-form-item :labelCol="labelCol"
                       :wrapperCol="wrapperCol"
                       label="打印机应替换数"
                       >
            <a-input-number placeholder="请输入打印机应替换数" style="width:100%"
                     v-decorator="['printerReplace']"
                            :allowClear='true'
                            autocomplete='off'
                      />
          </a-form-item>
        </a-col>
        <a-col v-bind='formItemLayout'>
          <a-form-item :labelCol="labelCol"
                       :wrapperCol="wrapperCol"
                       label="数据库应下发数"
                       >
            <a-input-number placeholder="请输入数据库应下发数" style="width:100%"
                     v-decorator="['dataDown']"
                            :allowClear='true'
                            autocomplete='off'
                      />
          </a-form-item>
        </a-col>
        <a-col v-bind='formItemLayout'>
          <a-form-item :labelCol="labelCol"
                       :wrapperCol="wrapperCol"
                       label="数据库应替换数"
                       >
            <a-input-number placeholder="请输入数据库应替换数" style="width:100%"
                     v-decorator="['dataReplace']"
                            :allowClear='true'
                            autocomplete='off'
                      />
          </a-form-item>
        </a-col>
        <a-col v-bind='formItemLayout'>
          <a-form-item :labelCol="labelCol"
                       :wrapperCol="wrapperCol"
                       label="应用服务器应下发数"
                       >
            <a-input-number placeholder="请输入应用服务器应下发数" style="width:100%"
                     v-decorator="['appDown']"
                            :allowClear='true'
                            autocomplete='off'
                      />
          </a-form-item>
        </a-col>
        <a-col v-bind='formItemLayout'>
          <a-form-item :labelCol="labelCol"
                       :wrapperCol="wrapperCol"
                       label="应用服务应替换数"
                       >
            <a-input-number placeholder="请输入应用服务应替换数" style="width:100%"
                     v-decorator="['appReplace']"
                            :allowClear='true'
                            autocomplete='off'
                      />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </j-modal>
</template>
<script>
import { editStandingBook } from '@/api/StandingBook'
import YqAreaCascaderSelect from '@/components/areaDict/YqAreaCascaderSelect'
import pick from 'lodash.pick'
export default {
  name: 'StandingBookEditModal',
  components: {
    YqAreaCascaderSelect
  },
  data() {
    return {
      title: '编辑',
      visible: false,
      confirmLoading: false,
      /* 弹框宽 */
      modalWidth: '1000px',
      form: this.$form.createForm(this),
      formItemLayout:{
        md:{span:12},
        sm:{span:24}
      },
      labelCol: {
        //style: 'width:145px',
        xs: { span: 24 },
        sm: { span: 7 },
        md: { span: 11 },
        lg: { span: 8 },
        xl:{span:8}
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
        md: { span: 13 },
        lg: { span: 16 },
        xl:{span:14}
      },
      formValidator: {
        region: {
          rules: [
            {
              required: true,
              message: '请选择地区',
            },
          ],
        },
        year: {
          rules: [
            {
              required: true,
              message: '请输入年份',
            },
          ],
        },
        company: {
          rules: [
            {
              required: true,
              message: '请输入单位',
            },
          ],
        },
      },
    }
  },
  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(
          pick(
            this.model,
            // 'id',
            'parentRegion',
            'region',
            'company',
            'year',
            'totalDown',
            'totalReplace',
            'serverDown',
            'serverReplace',
            'terminalDown',
            'terminalReplace',
            'printerDown',
            'printerReplace',
            'dataDown',
            'dataReplace',
            'appDown',
            'appReplace'
          )
        )
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let formData = Object.assign(this.model, values)
          let params = formData
          editStandingBook(params)
            .then(res => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
                that.$emit('ok')
              }
            })
            .finally(() => {
              that.confirmLoading = false
              that.close()
            })
        }
      })
    },
    handleCancel() {
      this.close()
    }
  }
}
</script>
<style scoped lang="less">
@import '~@assets/less/normalModal.less';
</style>