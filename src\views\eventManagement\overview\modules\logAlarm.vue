<template>
  <div class="content">
    <!-- 历史告警 -->
    <a-table ref="table" bordered :columns="columns" :dataSource="dataSource" :pagination="false" :loading="loading"
      :rowKey="(record, index) => record.id" :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}">
      <template slot="alarmLevel" slot-scope="text,record">
        <span class="level" :style="{backgroundColor:getAlarmColor(record)}"></span>
        {{ getAlarmTitle(record) }}
      </template>
      <template slot="tooltip" slot-scope="text">
        <a-tooltip placement="topLeft" :title="text" trigger="hover">
          <div class="tooltip">{{ text }}</div>
        </a-tooltip>
      </template>
    </a-table>
  </div>
</template>
<script>
  import {
    getAction
  } from '@/api/manage'
  import {
    dataAndFunction
  } from '@views/alarmManage/modules/dataAndFunction' 
  export default {
    mixins: [dataAndFunction],
    data() {
      return {
        url: {
          list: '/log/overview/getAlarmList'
        },
        loading: false,
        dataSource: [],
        columns: [{
            title: '告警名称',
            dataIndex: 'templateName',
            customCell: () => {
              let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
              return {
                style: cellStyle
              }
            },
            scopedSlots: {
              customRender: 'tooltip'
            }
          },
          {
            title: '设备名称',
            dataIndex: 'deviceName',
            customCell: () => {
              let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
              return {
                style: cellStyle
              }
            },
            scopedSlots: {
              customRender: 'tooltip'
            }
          },
          {
            title: '告警级别',
            dataIndex: 'alarmLevel',
            scopedSlots: {
              customRender: 'alarmLevel'
            },
            customCell: () => {
              let cellStyle = 'text-align: center;width:160px;'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '告警时间',
            dataIndex: 'alarmTime1',
            customCell: () => {
              let cellStyle = 'text-align: center;width:180px;'
              return {
                style: cellStyle
              }
            }
          }
        ]
      }
    },
    props: {
      timeData: {
        type: Object,
        default: () => {}
      }
    },
    watch: {
      timeData: {
        deep: true,
        handler(val) {
          this.getData()
        }
      }
    },
    mounted() {
      this.getAlarmLevelData()
      this.getData()
    },
    methods: {
      getData(startTime, endTime) {
        this.loading = true
        getAction(this.url.list, {
          alarmTime2_begin: this.timeData.startTime ? this.timeData.startTime : null,
          alarmTime2_end: this.timeData.endTime ? this.timeData.endTime : null,
          pageSize: 10,
          pageNo: 1
        }).then(res => {
          if (res.code == 200) {
            this.loading = false
            this.dataSource = res.result.records
          }
        })
      }
    }
  }
</script>
<style scoped lang="less">
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';

  .level {
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 3px;
    margin-right: 7px;
    margin-bottom: 2px;
  }

  ::v-deep .ant-table-thead>tr>th,
  ::v-deep .ant-table-tbody>tr>td {
    padding: 0.15rem 0.2rem;
  }
</style>