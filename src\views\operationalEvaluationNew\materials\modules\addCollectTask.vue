<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    :destroyOnClose="true"
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }"
    :centered="true"
    switch-fullscreen
    :maskClosable="false"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭"
  >
    <a-spin :spinning="confirmLoading">
      <j-form-container :disabled="disableSubmit">
        <a-form-model
          ref="form"
          slot="detail"
          :model="model"
          :rules="validatorRules"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
        >
          <a-row>
            <a-col :span="24">
              <a-form-model-item label="报告名称" prop="projectName">
                <a-input
                  style="width: 100%"
                  v-model="model.projectName"
                  :allow-clear="true"
                  autocomplete="off"
                  placeholder="请输入报告名称"
                />
              </a-form-model-item>
            </a-col>
<!--            <a-col :span="24">-->
<!--              <a-form-model-item ref="users" label="发起人" prop="sender">-->
<!--                <j-select-user-by-dep v-model="model.sender" :multi="false"></j-select-user-by-dep>-->
<!--              </a-form-model-item>-->
<!--            </a-col>-->
            <a-col :span="24">
              <a-form-model-item label="评估周期" prop="evaluateTime">
                <a-range-picker
                  format="YYYY-MM-DD HH:mm:ss"
                  show-time
                  v-model="model.evaluateTime"
                  :allow-clear="true"
                  autocomplete="off"
                  :placeholder="['开始时间', '截止时间']"
                  :disabled-date="disablePastDates"
                  @change="onChangeDate"
                />
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item label="指标与单位配置" prop="relations">
                <div class="relations-list" v-if="relations.length > 0">
                  <div v-for="(rel, index) in relations" :key="index" class="relation-item">
                    <div class="relation-header">
                      <span class="metric-name">{{ rel.metricName }}</span>
                      <a-tag color="blue">关联单位: {{ rel.deptIds.length }}个</a-tag>
                      <a-button
                        v-if="!disableSubmit"
                        type="link"
                        size="small"
                        @click="editRelation(index)"
                        icon="edit"
                      />
                      <a-button
                        v-if="!disableSubmit"
                        type="link"
                        size="small"
                        @click="removeRelation(index)"
                        icon="delete"
                      />
                    </div>
                    <div class="dept-tags">
                      <a-tag v-for="(deptName, i) in rel.deptNames" :key="i">
                        {{ deptName }}
                      </a-tag>
                    </div>
                  </div>
                </div>
                <a-button v-if="!disableSubmit" type="primary" @click="addRelationModal" icon="plus"
                  >配置指标与单位</a-button
                >
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </j-form-container>
    </a-spin>

    <!-- 指标和单位的配置弹框组件 -->
    <addMetricrRelationModal
      ref="addMetricrRelationModal"
      :metrics-tree-data="metricsTreeData"
      :initial-relations="relations"
      @ok="handleRelationOk"
    />
    <existingTaskModal ref="existingTaskModal" @ok="handleExistingTask"></existingTaskModal>
    <template slot="footer">
      <a-button v-if="!model.id" type="primary" @click="copyTask" style="float: left;">复制已有报告配置</a-button>
      <a-button @click="handleCancel">关闭</a-button>
      <a-button type="primary" @click="handleOk">确定</a-button>
    </template>
  </j-modal>
</template>

<script>
import moment from 'moment'
import { getAction, httpAction } from '@api/manage'
import JSelectUserByDep from '@comp/jeecgbiz/JSelectUserByDep.vue'
import addMetricrRelationModal from './addMetricrRelationModal.vue'
import { ValidateRequiredFields } from '@/utils/rules'
import existingTaskModal from './existingTaskModal.vue'

export default {
  name: 'addCollectTask',
  components: { JSelectUserByDep, addMetricrRelationModal, existingTaskModal },
  data() {
    return {
      title: '新增',
      width: '1200px',
      visible: false,
      disableSubmit: false,
      confirmLoading: false,
      labelCol: {
        xs: {
          span: 24,
        },
        sm: {
          span: 5,
        },
      },
      wrapperCol: {
        xs: {
          span: 24,
        },
        sm: {
          span: 16,
        },
      },
      model: {
        startTime: '',
        endTime: '',
        evaluateTime: [], // 评估周期
      },
      relations: [],
      metricsTreeData: [],
      validatorRules: {
        projectName: [
          { required: true,validator: (rule, value, callback) =>ValidateRequiredFields(rule, value, callback,'报告名称',50,1)}
        ],
        evaluateTime: [
          {
            required: true,
            message: '请选择评估周期',
          },
        ],
        sender: [{ required: true, message: '请输入发起人' }],
        relations: [{ required: true, validator: this.validateRelations }],
      },
      url: {
        add: '/devops/projectInfo/add',
        edit: '/devops/projectInfo/edit',
        metricsTree: '/evaluate/metricsType/treeNew', // 指标树
        deptListByMetrics: '/devops/projectInfo/deptListByMetrics', // 查询部门和指标之间的关联关系
      },
      moment,
      userInfo: this.$store.getters.userInfo,
    }
  },
  created() {},
  methods: {
    onChangeDate(date, dateString) {
      this.model.startTime = dateString[0]
      this.model.endTime = dateString[1]
    },
    // 打开指标与单位配置模态框
    addRelationModal() {
      this.$refs.addMetricrRelationModal.open()
    },
    // 编辑指标与单位关系
    editRelation(index) {
      const metricsId = this.relations[index].metricsId
      this.$refs.addMetricrRelationModal.open(metricsId)
    },
    // 删除指标与单位关系
    removeRelation(index) {
      this.relations.splice(index, 1)
    },
    // 配置完成的关系列表
    handleRelationOk(relations) {
      this.$nextTick(()=>{
        this.$refs.form.clearValidate('relations')
      })
      this.relations = relations
    },
    // 加载指标树数据
    loadMetricsTree() {
      getAction(this.url.metricsTree).then((res) => {
        if (res.success) {
          const processTreeData = (nodes) => {
            return nodes.map((node) => {
              return {
                ...node,
                scopedSlots: { title: 'title' },
                children: node.children ? processTreeData(node.children) : null,
              }
            })
          }
          this.metricsTreeData = processTreeData(res.result)
          // 加载部门数据
          if (this.model.id) {
            this.deptListByMetrics(this.model.id)
          }
        }
      })
    },
    async handleExistingTask(obj) {
      // 复制已有的任务单
      // 复制评估周期
      if (obj.startTime && obj.endTime) {
        this.model.startTime = obj.startTime
        this.model.endTime = obj.endTime
        this.$set(this.model, 'evaluateTime', [moment(obj.startTime), moment(obj.endTime)])
      }
      // 复制指标和单位的关联关系
      this.deptListByMetrics(obj.id)
      // 清空校验规则
      this.$nextTick(()=>{
        this.$refs.form.clearValidate('evaluateTime')
        this.$refs.form.clearValidate('relations')
      })
    },
    // 根据指标查询关联部门
    deptListByMetrics(id) {
      getAction(this.url.deptListByMetrics, {
        projectId: id,
      }).then((res) => {
        if (res.success) {
          const relations = []
          // 转换接口返回的数据结构
          for (const [metricsId, depts] of Object.entries(res.result)) {
            const metricName = this.findMetricName(metricsId)
            relations.push({
              metricsId: metricsId,
              metricName: metricName,
              deptIds: depts.map((dept) => dept.id),
              deptNames: depts.map((dept) => dept.departName),
            })
          }
          this.relations = relations
        }
      })
    },
    //  验证指标与单位关系是否配置
    validateRelations(rule, value, callback) {
      if (this.relations.length === 0) {
        callback(new Error('请配置指标与单位关系'))
      } else {
        callback()
      }
    },
    // 构建项目-部门-指标关联关系列表
    buildProject2Dept2MetricsList() {
      const result = []
      this.relations.forEach((rel) => {
        rel.deptIds.forEach((deptId) => {
          result.push({
            deptId: deptId,
            metricsId: rel.metricsId,
          })
        })
      })
      return result
    },
    // 禁用当前日期之前的日期
    disablePastDates(current) {
      return current && current < moment().startOf('day')
    },
    add() {
      this.edit({
        startTime: '',
        endTime: '',
        evaluateTime: []
      })
    },
    edit(record) {
      this.relations = []
      this.visible = true
      this.model = JSON.parse(JSON.stringify(record))
       // 初始化时间范围
      if (record.startTime && record.endTime) {
        this.$set(this.model, 'evaluateTime', [moment(record.startTime), moment(record.endTime)])
      }
      // 加载指标树
      this.loadMetricsTree()
    },
    // 根据指标ID查找指标名称
    findMetricName(metricsId) {
      const findInTree = (nodes) => {
        for (const node of nodes) {
          if (node.id === metricsId) {
            return node.title
          }
          if (node.children) {
            const found = findInTree(node.children)
            if (found) return found
          }
        }
        return null
      }
      const name = findInTree(this.metricsTreeData)
      return name || metricsId // 如果找不到，返回ID
    },
    close() {
      this.visible = false
      this.confirmLoading = false
    },
    handleOk() {
      const that = this
      that.$refs.form.validate((err, values) => {
        if (err && !that.confirmLoading) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!that.model.id) {
            httpurl += that.url.add
            method = 'post'
            // 新增默认填报状态设置为0
            that.model.status = '0'
          } else {
            httpurl += that.url.edit
            method = 'put'
          }
          // 构建project2Dept2MetricsList参数
          const formData = {
            ...this.model,
            status: '0',
            project2Dept2MetricsList: this.buildProject2Dept2MetricsList(),
            sender: this.userInfo.username
          }
          delete formData.evaluateTime
          console.log('指标配置融合后==', formData)
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
                that.close()
              } else {
                that.$message.warning(res.message)
              }
              that.confirmLoading = false
            })
            .catch((res) => {
              that.$message.warning(res.message)
              that.confirmLoading = false
            })
        }
      })
    },
    handleCancel() {
      this.close()
    },
    // 打开已有任务单列表弹框
    copyTask() {
      this.$refs.existingTaskModal.copyTask()
    }
  },
}
</script>

<style scoped lang="less">
.relations-list {
  margin-top: 12px;

  .relation-item {
    margin-bottom: 16px;
    padding: 12px;
    border: 1px solid #f0f0f0;
    border-radius: 4px;

    .relation-header {
      display: flex;
      align-items: center;
      margin-bottom: 8px;

      .metric-name {
        flex: 1;
        font-weight: 500;
      }
    }

    .dept-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }
  }
}
</style>
