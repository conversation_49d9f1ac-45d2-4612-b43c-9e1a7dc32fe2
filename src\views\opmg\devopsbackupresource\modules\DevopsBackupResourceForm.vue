<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-item label="连接类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag type="radio" v-decorator="['contype']" :trigger-change="true" dictCode="contype"
                placeholder="请选择连接类型" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="设备名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['resourceName', formValidator.resourceName]" :allowClear="true" autocomplete="off"
                placeholder="请输入设备名称"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="设备IP" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['resourceIp', validatorRules.resourceIp]" :allowClear="true" autocomplete="off"
                placeholder="请输入设备IP"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="设备用户名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['userName', validatorRules.userName]" :allowClear="true" autocomplete="off"
                placeholder="请输入设备用户名称"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="设备密码" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-password v-decorator="['resourcePwd', validatorRules.resourcePwd]" :allowClear="true" autocomplete="off"
                placeholder="请输入设备密码"></a-input-password>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="设备端口" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['resourcePort', validatorRules.resourcePort]" :allowClear="true" autocomplete="off"
                placeholder="请输入设备端口(数字类型)"></a-input>
            </a-form-item>
          </a-col>

          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
  import {
    httpAction,
    getAction
  } from '@/api/manage'
  import pick from 'lodash.pick'
  import {
    validateDuplicateValue
  } from '@/utils/util'
  import JFormContainer from '@/components/jeecg/JFormContainer'
  import JDictSelectTag from '@/components/dict/JDictSelectTag'
  import {
    duplicateCheck
  } from '@/api/api'

  export default {
    name: 'DevopsBackupResourceForm',
    components: {
      JFormContainer,
      JDictSelectTag,
    },
    props: {
      //流程表单data
      formData: {
        type: Object,
        default: () => {},
        required: false,
      },
      //表单模式：true流程表单 false普通表单
      formBpm: {
        type: Boolean,
        default: false,
        required: false,
      },
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false,
      },
    },
    data() {
      return {
        form: this.$form.createForm(this),
        model: {},
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          },
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          },
        },
        confirmLoading: false,
        validatorRules: {
          // resourceName:{
          //   rules: [
          //     {required: true,message:"请输入设备名称"},
          //     {validator: this.validateResourceName},
          //   ]
          // },
          resourceIp: {
            rules: [{
                required: true,
                pattern: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
                message: '请输入正确的设备IP',
              },
              {
                validator: this.validateResourceIp
              },
            ],
          },
          userName: {
            rules: [{
                required: true,
                message: '请输入设备用户名称'
              },
              {
                min: 2,
                message: '设备用户名称长度应在 2-20 之间！',
                trigger: 'blur'
              },
              {
                max: 20,
                message: '设备用户名称长度应在 2-20 之间！',
                trigger: 'blur'
              }
            ],
          },
          resourcePwd: {
            rules: [{
              required: true,
              message: '请输入设备密码'
            }, {
              pattern: /^[^\u4e00-\u9fa5]{2,20}$/,
              message: '设备密码为2-20位的非汉字字符串'
            }],
          },
          resourcePort: {
            rules: [{
              required: true,
              pattern: /^[0-9]{1,10}$/,
              message: '请输入正确的设备端口(数字类型)'
            }],
          },
        },
        formValidator: {
          resourceName: {
            rules: [{
                required: true,
                message: '请输入设备名称'
              },
              {
                min: 2,
                max: 30,
                message: '长度在2到30个字符',
                trigger: 'blur'
              },
              {
                validator: this.validateResourceName
              },
            ],
          },
        },
        resourceId: '',
        url: {
          add: '/devopsbackupresource/devopsBackupResource/add',
          edit: '/devopsbackupresource/devopsBackupResource/edit',
          queryById: '/devopsbackupresource/devopsBackupResource/queryById',
          testConnect: '/devopsbackupresource/devopsBackupResource/testConnect',
        },
      }
    },
    computed: {
      formDisabled() {
        if (this.formBpm === true) {
          if (this.formData.disabled === false) {
            return false
          }
          return true
        }
        return this.disabled
      },
      showFlowSubmitButton() {
        if (this.formBpm === true) {
          if (this.formData.disabled === false) {
            return true
          }
        }
        return false
      },
    },
    created() {
      //如果是流程中表单，则需要加载流程表单data
      this.showFlowData()
    },
    methods: {
      add() {
        this.edit({})
      },
      edit(record) {
        this.resourceId = record.id
        if ('' == record.contype || null == record.contype) {
          record.contype = '1'
        }
        this.form.resetFields()
        this.model = Object.assign({}, record)
        this.visible = true
        this.$nextTick(() => {
          this.form.setFieldsValue(
            pick(this.model, 'resourceName', 'resourceIp', 'userName', 'resourcePwd', 'resourcePort', 'contype')
          )
        })
      },
      //渲染流程表单数据
      showFlowData() {
        if (this.formBpm === true) {
          let params = {
            id: this.formData.dataId
          }
          getAction(this.url.queryById, params).then((res) => {
            if (res.success) {
              this.edit(res.result)
            }
          })
        }
      },

      validateResourceName(rule, value, callback) {
        var params = {
          tableName: 'devops_backup_resource',
          fieldName: 'resource_name',
          fieldVal: value,
          dataId: this.resourceId,
        }
        duplicateCheck(params).then((res) => {
          if (res.success) {
            callback()
          } else {
            callback('设备名称已存在!')
          }
        })
      },

      validateResourceIp(rule, value, callback) {
        var params = {
          tableName: 'devops_backup_resource',
          fieldName: 'resource_ip',
          fieldVal: value,
          dataId: this.resourceId,
        }
        duplicateCheck(params).then((res) => {
          if (res.success) {
            callback()
          } else {
            callback('设备IP已存在!')
          }
        })
      },

      //测试连接
      textCont() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let formData = Object.assign(this.model, values)
            getAction(this.url.testConnect, formData)
              .then((res) => {
                if (res.success) {
                  if ('连接成功！' === res.result) {
                    that.$message.success(res.result)
                  } else {
                    that.$message.error(res.result)
                  }
                }
              })
              .finally(() => {
                that.confirmLoading = false
              })
          }
        })
      },
      submitForm() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.model.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'put'
            }
            let formData = Object.assign(this.model, values)
            httpAction(httpurl, formData, method)
              .then((res) => {
                if (res.success) {
                  that.$message.success(res.message)
                  that.$emit('ok')
                } else {
                  that.$message.warning(res.message)
                }
              })
              .finally(() => {
                that.confirmLoading = false
              })
          }
        })
      },
      popupCallback(row) {
        this.form.setFieldsValue(
          pick(row, 'resourceName', 'resourceIp', 'userName', 'resourcePwd', 'resourcePort', 'contype')
        )
      },
    },
  }
</script>