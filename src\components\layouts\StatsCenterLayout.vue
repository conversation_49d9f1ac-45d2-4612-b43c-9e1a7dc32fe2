<template>
  <div class="stats-layout">
    <!-- 顶部组件 -->
    <stats-header></stats-header>
    <div class="stats-pages">
      <keep-alive v-if="keepAlive">
        <router-view />
      </keep-alive>
      <router-view v-else />
    </div>
  </div>
</template>

<script>
import StatsHeader from '../page/StatsHeader.vue'
import { WebsocketMixin } from '@/mixins/WebsocketMixin'
export default {
  name: 'StatsCenterLayout',
  components: { StatsHeader },
  mixins: [WebsocketMixin],
  data() {
    return {}
  },
  computed: {
    keepAlive() {
      return this.$route.meta.keepAlive
    },
  },
  methods: {},
}
</script>
<style>

</style>
<style lang="less" scoped>
.stats-layout {
  height: 100%;
  background-image: url(/statsCenter/homepage/bg.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  overflow: hidden;
}
.stats-pages {
  height: calc(100% - (135 / 19.2 * 1vw));
}
</style>