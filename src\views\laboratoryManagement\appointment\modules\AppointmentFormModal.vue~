<template>
  <j-modal ref='perModal' :title="title" :width="width" :visible="visible" :destroyOnClose="true" switchFullscreen
    :centered='true' :confirmLoading="confirmLoading" @cancel="handleCancel">
    <template slot="footer">
      <a-button :loading="confirmLoading" @click="handleCancel">关闭</a-button>
      <a-button :loading="confirmLoading" v-if="editstatus" type="primary" @click="handleOk">确定</a-button>
      <a-button :loading="confirmLoading" v-if="showDelayBtn" type="primary" @click="handleDelay">延后</a-button>
    </template>
    <div style='height: 60vh;overflow-y: auto'>
      <a-form-model v-if="rooms.length" ref="ruleForm" :model="form" :rules="rules" :label-col="labelCol"
        :wrapper-col="wrapperCol">
        <a-form-model-item label="房间" prop="roomId">
          <a-select style="width: 100%" v-model='form.roomId' :disabled="true" placeholder='请选择房间'
            :options="rooms"></a-select>
        </a-form-model-item>
        <a-form-model-item label="预约日期" prop="perDates">
          <a-date-picker v-model='form.perDates' :disabled="!editstatus" :disabled-date="disabledDate" @change="onDateChange" />
        </a-form-model-item>
        <a-form-model-item label="预约时段" prop="perTimes">
          <a-time-picker v-model="startTime" :disabled="!editstatus || startTimeDisabled" placeholder="开始时间"
            :disabledHours="startDisabledHours" :disabledMinutes="startDisabledMinutes" format="HH:mm" :minuteStep="30"
            @change="onStartTimeChange" />
          <span style="margin:0 10px;">~</span>
          <span v-show="showSign" style="font-size: 12px;">（次日）</span>
          <a-time-picker v-model="endTime" :disabled="!editstatus || endTimeDisabled" :disabledHours="endDisabledHours"
            :disabledMinutes="endDisabledMinutes" placeholder="结束时间" format="HH:mm" :minuteStep="30"
            @change="onEndTimeChange" />
          <a-tooltip v-if="this.appointmentTimes.length">
            <template slot="title">
              <p>已使用时间段：{{ this.appointmentTimes.join(" , ") }}</p>
            </template>
            <a-icon type="exclamation-circle" style="color: #1D509B;margin-left: 10px;" />
          </a-tooltip>
        </a-form-model-item>
        <a-form-model-item label="预约目的" prop="title">
          <a-input v-model="form.title" :disabled="!editstatus" placeholder='请输入预约目的' type="text" />
        </a-form-model-item>
        <a-form-model-item label="使用人" prop="contactName">
          <a-select
            mode="tags"
            placeholder="请选择或输入使用人"
            :getPopupContainer="node => node.parentNode"
            @change="handleChange"
            v-model="currentValue"
            show-search
            :filter-option="filterOption"
            :token-separators="[',']"
            allow-clear
            :disabled="!editstatus"
            style="width: 100%"
          >
            <a-select-option
              v-for="(item, index) in userList"
              :key="index"
              :value="item.username"
            >
              <div style="display: inline-block; width: 100%" :title="item.realname">
                {{ item.realname }}
                <span style="font-size: 12px; color: rgba(0, 0, 0, 0.45)">
                  ({{ item.username }})
                </span>
              </div>
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="支撑项目" prop="projectId">
          <a-select style="width: 100%" v-model='form.projectId' :disabled="!editstatus" placeholder='请选择支撑项目'
            :options="projects"></a-select>
        </a-form-model-item>
        <a-form-model-item label="参与人数" prop="participants">
          <a-input-number v-model="form.participants" :disabled="!editstatus" :allowClear="true" :min="1" :max="roomInfo.capacity"
            placeholder="请输入参与人数" style="width: 180px;"></a-input-number>
          <a-tooltip>
            <template slot="title">
              <p>房间容纳人数：{{ roomInfo.capacity }}人</p>
            </template>
            <a-icon type="question-circle" style="color: #1D509B;margin-left: 10px;" />
          </a-tooltip>
        </a-form-model-item>
        <a-form-model-item label="联系电话" prop="contactPhone">
          <a-input placeholder="请输入联系电话" v-model="form.contactPhone" :disabled="!editstatus" autocomplete="off" :allowClear="true" />
        </a-form-model-item>
        <a-form-model-item label="备注" prop="reason">
          <a-input v-model="form.reason" :disabled="!editstatus" type="textarea" />
        </a-form-model-item>
      </a-form-model>
      <a-empty v-else description="暂无房间可以预约" />
    </div>
    <DelayModal ref="delayModal" @ok='close'></DelayModal>
  </j-modal>
</template>
<script>
import moment from 'moment'
import { getAction, postAction, httpAction } from '@/api/manage'
import { getUserList } from '@/api/api'
import DelayModal from '@views/laboratoryManagement/appointment/modules/DelayModal.vue'
import {
  phoneValidator
} from '@/mixins/phoneValidator'
export default {
  name: 'AppointmentFormModal',
  components: { DelayModal },
  mixins: [phoneValidator],
  data() {
    return {
      projects: [],
      title: '房间预约',
      width: '1000px',
      visible: false,
      disableSubmit: false,
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
      other: "",
      rules: {
        roomId: [
          { required: true, message: '请选择房间', trigger: 'blur' },
        ],
        perDates: [
          { required: true, message: '请选择预约日期', trigger: 'change' },
        ],
        perTimes: [
          { required: true, message: '请选择预约时间', trigger: 'change' },
        ],
        projectId: [
          { required: true, message: '请选择支撑项目', trigger: 'change' },
        ],
        contactPhone: [
          { required: true, message: '请输入联系方式', trigger: 'change' },
          { validator: this.phone, trigger: 'change' },
        ],
        participants: [
          { required: true, message: '请输入参与人数', trigger: 'change' },
        ],
        contactName: [
          { required: true, validator: this.validateContactName}
        ],
        title: [
          { required: true, message: '请输入预约目的', trigger: 'change' },
          { max: 15, message: '最多15个字符', trigger: 'change' },
        ],
        reason: [
          { max: 100, message: '最多100个字符', trigger: 'change' },
        ],
      },
      form: {},
      appointmentTimes: [],
      editstatus: true,//编辑状态
      rooms: [],
      startTime: '',//开始时间段
      endTime: '',//结束时间段
      startDisHourArr: [],//开始时间段禁用小时
      endDisHourArr: [],//结束时间段禁用小时
      roomInfo: {},
      confirmLoading: false,
      userList: [], // 用户列表
      currentValue: [], // 当前显示的值（必须保持数组类型）
      selectedUserId: null, // 选择的用户ID（username）
      inputContactName: '', // 手动输入的联系人
      showDelayBtn: false,
      sysUserName: this.$store.getters.userInfo.username || '', // 当前登录用户的用户名
    }
  },
  created() {
    this.form = this.initForm();
    this.getRoomList();
    this.getProjectList();
    this.getuserList();
  },
  computed: {
    //开始时间段是否可用
    startTimeDisabled() {
      return this.form.perDates ? false : true;
    },
    //结束时间段是否可用
    endTimeDisabled() {
      return this.startTime ? false : true;
    },
    // 将用户列表转为 username 为 key 的映射
    userMap() {
      return this.userList.reduce((map, user) => {
        map[user.username] = user;
        return map;
      }, {});
    },
    showSign() {
      // 判断是否显示次日标识
      const start = moment(this.startTime);
      const end = moment(this.endTime);
      return (start.hour() === 0 && start.minute() === 0 && end.hour() === 0 && end.minute() === 0) || 
            (end.hour() < start.hour()) || 
            (end.hour() === 0 && end.minute() === 0 && start.minute() > 0);
    }
  },
  methods: {
    getuserList() {
      // 查询用户列表
      let param = {
        pageSize: 1000000
      }
      getUserList(param).then((res) => {
        if (res.success) {
          this.userList = res.result.records
        }
      })
    },
    handleChange(values) {
      // 强制只保留最后一个值
      const newValue = values.length > 0 ? [values[values.length - 1]] : [];
      
      // 同步到当前显示值
      this.currentValue = newValue;
      
      // 获取实际值（字符串）
      const inputValue = newValue[0] || '';

      // 判断是选择还是输入
      const selectedUser = this.userMap[inputValue];
      
      if (selectedUser) {
        // 情况1：选择的是列表中的用户
        this.selectedUserId = selectedUser.username;
        this.inputContactName = '';
      } else {
        // 情况2：手动输入的值
        this.selectedUserId = null;
        this.inputContactName = inputValue;
      }
    },
    filterOption(input, option) {
      // 搜索时同时匹配真实姓名和用户名
      const text = option.componentOptions.children[0].children[0].text + 
                  option.componentOptions.children[0].children[1].children[0].text;
      return text.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    },
    validateContactName(rule, value, callback) {
      if (rule.required) {
        if (this.selectedUserId || this.inputContactName) {
          if (this.inputContactName && this.inputContactName.length > 10) {
            callback('使用人不能超过10个字符')
          } else {
            callback()
          }
        } else {
          callback('使用人不能为空')
        }
      }
    },
    //获取房间的预约时间段
    getRoomTime() {
      const params = {
        roomId: this.form.roomId,
        date: moment(this.form.perDates).format('YYYY-MM-DD'),
        viewType: "day"
      }
      getAction('/reservations/calendar', params).then(res => {
        if (res.success && res.result) {
          this.appointmentTimes = res.result.map(item => {
            return item.startTime.substring(10,16) + " ~ " + item.endTime.substring(10,16)
          })
        }
      })
    },
    //获取房间
    getRoomList() {
      getAction('/topo/room/list', { pageNo: 1, pageSize: -1 }).then(res => {
        if (res.success && res.result && res.result.records) {
          this.rooms = res.result.records.filter(el => el.status == 0).map(item => {
            return {
              label: item.name,
              value: item.id,
            }
          })
        }
      })
    },
    //获取项目
    getProjectList() {
      getAction('/lab/project/list', { pageNo: 1, pageSize: -1 }).then(res => {
        if (res.success && res.result && res.result.records) {
          this.projects = res.result.records.map(item => {
            return {
              label: item.projectName,
              value: item.id,
            }
          })
        }
      })
    },
    //禁用时间
    disabledDate(value) {
      return value && value.valueOf() < moment().subtract(1, 'days').valueOf()
    },
    //开始时间段禁用小时判断
    startDisabledHours() {
      if (moment(this.form.perDates).format('YYYY-MM-DD') === moment().format('YYYY-MM-DD')) {
        let tem = []
        for (let i = 0; i <= 23; i++) {
          if (i <= moment().hour()) {
            tem.push(i)
          }
        }
        return tem
      } else {
        return []
      }
    },
    //结束时间段禁用小时判断
    endDisabledHours() {
      if (this.startTime) {
        let tem = []
        let m = moment(this.startTime).minute()
        let h = moment(this.startTime).hour()
        if (m >= 30 && h != 0) {
          tem.push(h)
        }
        for (let i = 0; i <= 23; i++) {
          if (i < h && i != 0) {
            tem.push(i)
          }
        }
        return tem
      } else {
        return []
      }
    },
    //开始时间段禁用分钟判断
    startDisabledMinutes(hour) {
      if (this.startTime) {
        return []
      } else {
        return [0, 30]
      }

    },
    //结束时间段禁用分钟判断
    endDisabledMinutes(hour) {
      if (this.endTime) {
        return []
      } else {
        return [0, 30]
      }

    },
    disabledTime(value) {
      if (value) {
        return value && value.valueOf() < Date.now()
      }
    },
    //初始化表单
    initForm(data) {
      this.startTime = '';
      this.endTime = '';
      this.currentValue = [];
      this.selectedUserId = null;
      this.inputContactName = '';
      this.showDelayBtn = false
      return {
        title: "",
        roomId: '',
        perDates: "",
        perTimes: [],
        startTime: '',
        endTime: '',
        projectId: undefined,
        contactName: '',
        contactPhone: '',
        participants: '',
        reason: '',
      }
    },
    //新增
    add(roomInfo,timesInfo) {
      // console.log("房间信息 == ", roomInfo);
      this.editstatus = true;
      this.form = this.initForm()
      this.form.roomId = roomInfo.id
      this.roomInfo = roomInfo;
      if(timesInfo){
        this.form.perDates = moment(timesInfo.start).format('YYYY-MM-DD')
        this.startTime = moment(timesInfo.start)
        this.endTime = moment(timesInfo.end)
        this.form.perTimes = [this.startTime, this.endTime]
      }
      this.getRoomTime()
      this.visible = true
    },
    //编辑
    edit(record, roomInfo) {
      this.roomInfo = roomInfo;
      this.editstatus = record.editable;
      Object.assign(this.form, record)
      this.form.perDates = moment(record.startTime).format('YYYY-MM-DD')
      this.startTime = moment(record.startTime)
      this.endTime = moment(record.endTime)
      this.form.perTimes = [this.startTime, this.endTime]
      this.currentValue = record.userId || record.contactName
      this.selectedUserId = record.userId
      this.inputContactName = record.contactName
      
      let isInTimeRange = this.isCurrentTimeInRange(this.startTime, this.endTime)
      // 判断这条数据的创建人和当前登录用户是否一致
      let isCurrentCreateBy = record.createBy == this.sysUserName ? true : false
      if (isInTimeRange && isCurrentCreateBy) {
        this.showDelayBtn = true
      } else {
        this.showDelayBtn = false
      }
      this.getRoomTime()
      this.visible = true
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    //确定提交
    handleOk() {
      this.onSubmit()
    },
    //提交表单
    onSubmit() {
      if (this.rooms.length == 0 || !this.editstatus) {
        this.close()
        return false;
      }
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          let params = Object.assign({}, this.form)
          params.startTime = moment(this.form.perDates).format('YYYY-MM-DD') + ' ' + moment(this.form.perTimes[0]).format('HH:mm') + ':00'
          //当结束时段为00：00时，默认设置为第二天00：00
          if (moment(this.endTime).hour() == 0 && moment(this.endTime).minute() == 0) {
            params.endTime = moment(this.form.perTimes[1]).format('YYYY-MM-DD HH:mm:ss');
          } else {
            params.endTime = moment(this.form.perDates).format('YYYY-MM-DD') + ' ' + moment(this.form.perTimes[1]).format('HH:mm') + ':00'
          }
          // 使用人选择的用户需要传userId, 手动输入的需要传contactName
          params.userId = this.selectedUserId
          params.contactName = this.inputContactName
          console.log('submit!', this.form, params);
          this.confirmLoading = true
          let path = "/reservations/create"
          let httpMethod = 'post';
          if (this.form.id) {
            path = "/reservations/update"
            httpMethod = 'put';
            delete params.day;
            delete params.month;
            delete params.year;
          }
          httpAction(path, params, httpMethod).then(res => {
            if (res.success) {
              this.close()
              this.$message.success('预约成功')
            } else {
              this.getRoomTime()
              this.$message.error(res.message)
            }
            this.confirmLoading = false
          }).catch(err => {
            this.getRoomTime()
            this.$message.error(err.message)
            this.confirmLoading = false
          })
        } else {
          return false;
        }
      });
    },

    handleCancel() {
      this.close()
    },
    //预约日期变化
    onDateChange(date, dateString) {
      this.startTime = '';
      this.endTime = '';
      this.form.perTimes = [];
      // console.log(date, dateString); 
    },
    //预约开始时间变化
    onStartTimeChange(date, dateString) {
      this.endTime = '';
      this.form.perTimes = [];
    },
    //预约结束时间变化
    onEndTimeChange(date, dateString) {
      if (date === null) {
        this.form.perTimes = [];
        this.$refs.ruleForm.validateField('perTimes')
      } else {
        //当起始时间段大于0点并且结束时间段为0点时，结束时间默认设置为第二天0点
        const isEndAtMidnight = moment(this.endTime).hour() === 0;
        const isStartAfterMidnight = moment(this.startTime).hour() > 0;
        const isStartAtHalfPast = moment(this.startTime).minute() === 30;
        const isEndAtSharp = moment(this.endTime).minute() === 0;

        if (isEndAtMidnight && (isStartAfterMidnight || isStartAtHalfPast || isEndAtSharp)) {
          this.endTime = moment(this.form.perDates).add(1, 'days').startOf('day');
        }

        this.form.perTimes = [this.startTime, this.endTime]
        this.$refs.ruleForm.clearValidate('perTimes')
      }
      console.log(this.form);
    },
    isCurrentTimeInRange(startTime, endTime) {
      const format = 'YYYY-MM-DD HH:mm:ss';
      const now = moment();
      return now.isBetween(moment(startTime, format), moment(endTime, format));
    },
    handleDelay() {
      // 打开延后弹框
      this.$refs.delayModal.edit(this.form)
    },
    closeDelayModal() {
      this.$refs.delayModal.close()
      this.close()
    },
  },

}
</script>
<style scoped lang='less'></style>