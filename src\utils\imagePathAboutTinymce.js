/*
* 注意事项：
* JEditor组件中，路径中有关<>一些特殊字符的，需要注意html和url转码问题，最好不要用这些特殊字符，具体使用时多多测试
* */
/**
 *根据相对路径拼接图片的绝对全路径，
 *所得全路径用于回显JEditor组件图片或者其他地方图片回显
 * @content 富文本json字符串内容
 * @midPath 后端保存图片的中间路径
 * */
export function setImgAllPath(content,midPath='/sys/common/static/'){
  if (content&&content.length>0){
    let comPath="<img src=\""+midPath
    let replaceComPath="<img src=\""+window._CONFIG['domianURL']+midPath
    let minioStr='!inmsiighntio!'
    let localStr='!inlsioghctal!'
    /*let minioAllPath=content.replaceAll(comPath+'!inmsiighntio!',replaceComPath+'!inmsiighntio!')
    let localAllPath=minioAllPath.replaceAll(comPath+'!inlsioghctal!',replaceComPath+'!inlsioghctal!')*/
    let minioAllPath=content.replace(new RegExp( comPath+minioStr,"gm"),replaceComPath+minioStr)
    let localAllPath=minioAllPath.replace(new RegExp(comPath+localStr,"gm"),replaceComPath+localStr)
    return localAllPath
  }else {
    return ''
  }
}
/**
 *根据全路径获取相对路径
 *可用于将JEditor组件中图片的全路径转成相对路径提交给后端
 * @content 富文本json字符串内容
 * @midPath 后端保存图片的中间路径
 * */
export function setImgRelativePath(content,midPath='/sys/common/static/'){
  if (content&&content.length>0) {
    let comPath = "<img src=\"" + window._CONFIG['domianURL'] + midPath
    let replaceComPath = "<img src=\"" + midPath
    let minioStr = '!inmsiighntio!'
    let localStr = '!inlsioghctal!'
    /*let filterMinioPath=content.replaceAll(comPath+'!inmsiighntio!',replaceComPath+'!inmsiighntio!')
    let filterLocalPath=filterMinioPath.replaceAll(comPath+'!inlsioghctal!',replaceComPath+'!inlsioghctal!')*/
    let filterMinioPath = content.replace(new RegExp(comPath + minioStr, "gm"), replaceComPath + minioStr)
    let filterLocalPath = filterMinioPath.replace(new RegExp(comPath + localStr, "gm"), replaceComPath + localStr)
    return filterLocalPath
  }
  else {
    return ''
  }
}

/*删除图片标签*/
export function removeImg(content,midPath='/sys/common/static/'){
  if (content&&content.length>0){
    let newContent1=content.replace(/<img src=".*?\/sys\/common\/static\/.*?\/>/g,'')
    let newContent2=newContent1.replace(/<img src=".*?\/sys\/common\/static\/.*?\/>/g,'')
    return newContent2
  }else {
    return ''
  }
}