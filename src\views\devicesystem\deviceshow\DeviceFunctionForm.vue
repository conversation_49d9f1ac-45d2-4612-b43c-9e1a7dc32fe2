<template>
  <a-spin :spinning='confirmLoading'>
    <j-form-container :disabled='formDisabled'>
      <a-form
        :form='form'
        slot='detail'
      >
        <div class='colorBox'>
          <span class='colorTotal'>方法名称：{{record.methodName}}</span>
        </div>
        <a-table
          ref='table'
          bordered
          row-key='id'
          :pagination="false"
          :columns='columns'
          :dataSource='dataSource'
          :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
          :loading='loading'
        >
          <div
            slot='defaultValue'
            slot-scope='text, record,index'
          >
            <div v-if='record.isEditable == 1'>
              <a-input
                style='text-align: center'
                :value='text'
                @change='changeValue($event,index)'
              />
            </div>
            <div v-else>
              {{text}}
            </div>
          </div>
          <span
            slot='action'
            class='caozuo'
            slot-scope='text, record'
          >
            <a @click='handleEdit(record)'>执行</a>
          </span>
          <template
            slot='tooltip'
            slot-scope='text'
          >
            <a-tooltip
              placement='topLeft'
              :title='text'
              trigger='hover'
            >
              <span class='tooltip'>
                {{ text }}
              </span>
            </a-tooltip>
          </template>
        </a-table>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import pick from 'lodash.pick'
import JFormContainer from '@/components/jeecg/JFormContainer'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { mixinDevice } from '@/utils/mixin'
export default {
  name: 'DeviceFunctionForm',
  mixins: [JeecgListMixin],
  components: {
    JFormContainer,
    getAction,
  },
  props: {
    // 流程表单data
    formData: {
      type: Object,
      default: () => {},
      required: false,
    },
    // 表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false,
    },
    // 表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false,
    },
    deviceInfo: {},
  },
  data() {
    return {
      form: this.$form.createForm(this),
      model: {},
      formItemLayout: {
        md: { span: 12 },
        sm: { span: 24 },
      },
      labelCol: {
        xs: { span: 24 },
        sm: { span: 7 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      confirmLoading: false,
      columns: [
        {
          title: '名称',
          dataIndex: 'name',
          customCell: () => {
            /*let cellStyle = 'text-align: center;min-width: 80px;max-width:300px'*/
            let cellStyle = 'text-align: center'
            return { style: cellStyle }
          },
        },
        {
          title: '项',
          dataIndex: 'item',
          customCell: () => {
            /* let cellStyle = 'text-align: center;min-width: 80px;max-width:300px'*/
            let cellStyle = 'text-align: center'
            return { style: cellStyle }
          },
        },
        {
          title: '值',
          dataIndex: 'defaultValue',
          scopedSlots: { customRender: 'defaultValue' },
          customCell: () => {
            /* let cellStyle = 'text-align: center;min-width: 50px;max-width:300px'*/
            let cellStyle = 'text-align: center'
            return { style: cellStyle }
          },
        },
      ],
      url: {
        execute: '/device/deviceInfo/execute',
      },
      //isCanInput:false,
      disableMixinCreated: true,
      record: {},
    }
  },
  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    },
  },
  methods: {
    changeValue(e, index) {
      this.dataSource[index].defaultValue = e.target.value
    },
    show(record) {
      this.form.resetFields()
      this.record = record
      this.dataSource = record.deviceControlCommandExtendList
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = this.url.execute
          let method = 'post'
          let formData = Object.assign(this.model, values)
          formData.productId = this.record.productId
          formData.deviceId = this.deviceInfo.id
          formData.commandId = this.record.id
          formData.methodName = this.record.methodName
          if (this.record && this.record.protocol) {
            formData.transferProtocol = this.record.protocol.trim()
          }
          formData.transferProtocolId = this.record.protocolId
          formData.dataSource = this.dataSource
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                //that.$message.success(res.result)
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                //that.$message.warning(res.result)
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
  },
}
</script>

<style lang='less' scoped='scoped'>
.colorBox {
  margin-bottom: 18px;
}
.colorTotal {
  padding-left: 7px;
  border-left: 4px solid #1e3674;
}
</style>
