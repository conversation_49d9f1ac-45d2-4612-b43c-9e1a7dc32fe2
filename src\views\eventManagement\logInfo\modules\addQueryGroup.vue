<template>
  <div>
    <a-spin :spinning="confirmLoading">
      <div class='table-page-search-wrapper header-wrapper'>
        <a-form-model
          ref='form'
          layout='inline'
          :model='model'
          :rules='validatorRules'
          :labelCol='labelCol'
          :wrapperCol='wrapperCol'>
          <a-row :gutter='[12,0]'>
            <a-col :span='24'>
              <a-form-model-item label='名称' prop='queryName'>
                <a-input v-model='model.queryName' placeholder='请输入名称' />
              </a-form-model-item>
            </a-col>
            <a-col :span='24' class='form-btn-wrapper'>
              <a-form-model-item class='cancel-item' style='margin-bottom: 0'>
                <a-button type='default' @click='cancelGroup'>取消</a-button>
              </a-form-model-item>
              <a-form-model-item class='submit-item'>
                <a-button type='primary' @click='submitGroup' class='submit'>保存查询分组</a-button>
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </div>
    </a-spin>
  </div>
</template>
<script>
import { httpAction } from '@api/manage'
import {groupOperationTypeEnum,opertionTips} from '@views/eventManagement/logInfo/modules/status'
export default {
  name: "addQueryGroup",
  props: {
    //日志来源id
    logAnalyzeId: {
      type: String,
      required: false,
      default: ''
    },
    //另存上一个分组的信息或者被编辑一个分组的信息
    groupInfo: {
      type: Object,
      required: false,
      default: ()=>{
        return {}
      }
    },
    groupOperationType:{
      type: Number,
      required: false,
      default: groupOperationTypeEnum.addOperation//1新建分组 2另存分组 3编辑分组
    }
  },
  data() {
    return {
      confirmLoading: false,
      labelCol: { style: 'width:70px' },
      wrapperCol: {},
      model: {
        queryName:''
      },
      validatorRules: {
        queryName: [
          { required: true, min: 1, max: 20, message: '名称长度应在【1-20】个字符之间' }
        ]
      },
      loading: false,
      url: {
        add: '/logAnalyze/group/add',//新建/另存为   ----groupOperationType为1和2的情况，调用该接口
        edit:'/logAnalyze/group/edit'//编辑   ----groupOperationType为3的情况，调用该接口
      }
    }
  },
  watch: {
    groupOperationType: {
      handler(nval, oval) {
        this.model.queryName = nval !== groupOperationTypeEnum.editOperation ? "" : this.groupInfo.queryName
      },
      deep:true,
      immediate:true
    }
  },
  methods: {
    cancelGroup(){
      this.model.queryName=""
      let key=this.groupOperationType!=groupOperationTypeEnum.editOperation?groupOperationTypeEnum.addOperation:groupOperationTypeEnum.editOperation
      this.$emit('cancel',key)
    },
    submitGroup(){
      let that=this
      //日志来源为空时，不能创建组，给出提示
      if (!that.logAnalyzeId||that.logAnalyzeId.length===0){
        that.$message.warning(opertionTips.newOrSaveGroup)
        return
      }

      that.$refs.form.validate((valid, values) => {
        if (valid) {
          that.confirmLoading = true
          let httpurl =''
          let method = ''

          if (that.groupOperationType!=groupOperationTypeEnum.editOperation){
             httpurl =that.url.add
             method = 'post'
          }else {
             httpurl =that.url.edit
             method = 'put'
          }
          let formData={
            queryName:that.model.queryName,
            id:that.groupOperationType==groupOperationTypeEnum.addOperation ?'':that.groupInfo.id,
            logAnalyzeId:that.logAnalyzeId
          }
          //console.log('新增查询分组 formData===',formData)
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok',that.groupOperationType)
              } else {
                that.$message.warning(res.message)
              }
              that.confirmLoading = false
            }).catch((err) => {
            that.$message.warning(err.message)
            that.confirmLoading = false
          })
        }
      })
    }
  }
}
</script>

<style scoped lang="less">
@import '~@assets/less/common.less';
.form-btn-wrapper{
  display: flex;
  justify-content: right;
  border-top: 1px solid #e6d8d8;
  padding: 12px 0 0;
  width: calc(100% + 24px);
  margin-left: -12px;

  .cancel-item{
    margin-bottom: 0px;
  }

  .submit-item{
    margin-bottom: 0px;
    margin-right: 16px;

    .submit{
      margin-left: 12px;
    }
  }
}
</style>