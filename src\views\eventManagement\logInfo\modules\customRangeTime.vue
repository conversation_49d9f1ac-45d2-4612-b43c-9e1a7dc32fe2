<template>
  <div>
    <a-input-group compact>
      <a-select
        v-model='operator'
        :allow-clear='false'
        style='width: 80px'
        placeholder='请选择运算符'
        :getPopupContainer='(node) => node.parentNode'
        @change='changeOperator'>
        <a-select-option v-for='(item,index) in operatorList' :key='"operator_"+index'
                         :value='item.key' :label='item.label'>
          {{ item.label }}
        </a-select-option>
      </a-select>

      <!--      <a-select v-model='symbol1' style='width: 60px' @change='changeSymbol'>
              <a-select-option value="[" label='闭 ['>
                闭 [
              </a-select-option>
              <a-select-option value="{" label='开 {'>
                开 {
              </a-select-option>
            </a-select>-->

      <a-range-picker
        style='display: inline-block;white-space: nowrap;width: calc(100% - 80px) !important;'
        :dropdownClassName="'log-info-range-picker'"
        format="YYYY-MM-DD HH:mm:ss"
        :show-time="{ defaultValue: [moment('00:00:00', 'HH:mm:ss'),moment('23:59:59', 'HH:mm:ss')]}"
        :placeholder="['开始时间', '截止时间']"
        :ranges="{
                               昨天: [
                                 moment().startOf('day').subtract(1, 'days'),
                                 moment().endOf('day').subtract(1, 'days')
                                ],
                               '近7天': [
                                 moment().startOf('day').subtract(1, 'weeks'),
                                 moment().endOf('day').subtract(1, 'days'),
                               ],
                               '近30天': [
                                 moment().startOf('day').subtract(30, 'days'),
                                 moment().endOf('day').subtract(1, 'days'),
                               ],
                               '近半年': [
                                 moment().startOf('month').subtract(5, 'months'),
                                 moment().endOf('month').subtract(0, 'months')
                               ],
                               '近一年': [
                                 moment().startOf('month').subtract(11, 'months'),
                                 moment().endOf('month').subtract(0, 'months'),
                               ],
                      }"
        @change='changeTime'>
      </a-range-picker>

      <!--      <a-select v-model='symbol2' style='width: 60px' @change='changeSymbol'>
              <a-select-option value="]" label='闭 ]'>
                闭 ]
              </a-select-option>
              <a-select-option value="}" label='开 }'>
                开 }
              </a-select-option>
            </a-select>-->
    </a-input-group>

  </div>
</template>
<script>
import moment from 'moment'
export default {
  name: "customRangeTime",
  props:{
    timestamp:{
      type: String,
      required: false,
      default: ''
    }
  },
  data() {
    return {
      moment,
      operator: 'withinRange',
      operatorList: [
        {
          label: '介于',
          key: 'withinRange',
          inverse: 0
        },
        {
          label: '不介于',
          key: 'outsideRange',
          inverse: 1
        }],
      rangeTime:'',
      symbol1: '[',
      symbol2: ']'
    }
  },
  methods:{
    /*改变运算符*/
    changeOperator() {
      this.setRangeTimeQuery()
    },
    changeSymbol(){
      this.setRangeTimeQuery()
    },
    changeTime(value, dateString) {
      let strTimes=''
      if (dateString.length>0&&dateString[0]&&dateString[1]){
        strTimes=dateString.map((item)=>{
          return '"'+item+'"'
        })
      }
      this.rangeTime=strTimes?strTimes.join(" TO "):""
      this.setRangeTimeQuery()
    },
    setRangeTimeQuery(){
      let query=""
      if (this.timestamp&&this.rangeTime){
        let inverse=this.operator==="withinRange"?'':'-'
        query=inverse+this.timestamp+":"+this.symbol1+this.rangeTime+this.symbol2
      }
      this.$emit('changeRangeTime',query)
    }
  }

}
</script>

<style scoped lang="less">
/*!* 日期范围 *!
.yq-range-picker .ant-calendar-range .ant-calendar-footer-extra {
  float: left !important;
}

.yq-range-picker .ant-tag .ant-tag-blue {
  cursor: pointer;
  font-size: 14px !important;
  color: #409EFF !important;
  background-color: #ecf5ff !important;
  background: #ECF5FF !important;
  border: 1px solid #B3D8FF !important;
  border-radius: 4px !important;
  border-radius: 4px !important;
  padding: 0 12px !important;
}

::v-deep .ant-calendar-picker-container{
  .ant-calendar-range{
    .ant-calendar-panel{
      .ant-calendar-footer>.ant-calendar-footer-btn>.ant-calendar-footer-extra{
        .ant-tag .ant-tag-blue{
          cursor: pointer;
          font-size: 14px !important;
          color: #409EFF !important;
          background-color: #ecf5ff !important;
          background: #ECF5FF !important;
          border: 1px solid #B3D8FF !important;
          border-radius: 4px !important;
          border-radius: 4px !important;
          padding: 0 12px !important;
        }
      }
    }
  }
}
.ant-calendar-ok-btn {
  margin-right: 8px;
}*/
</style>