<template>
  <div class="mouse" id="onClickHelpGateway">
    <div class="logo">
      <img :src="logo" alt="" />
      <span class="logo-text">{{ platformTitle }}</span>
    </div>
    <div class="center" :style="{ transform: `scale(${centerScale})`, left: `${leftNum}px`, top: `${topNum}px` }">
      <div class="title">运维助手</div>
      <!-- 1 -->
      <div class="menu-item top-right-menu" :style="pos[0]" v-if="paths[0]">
        <div class="menu" @click="goPage(paths[0])">{{ paths[0].meta.title }}</div>
        <div class="line-top-right-b"></div>
        <div class="menu-shine"></div>
        <div class="line-top-right-a"></div>
        <div class="disc" :class="{'disc-ani':simpleModel}"></div>
      </div>
      <!-- 2 -->
      <div class="menu-item top-left-menu" :style="pos[1]" v-if="paths[1]">
        <div class="menu" @click="goPage(paths[1])">
          <div style="transform: rotateY(180deg)">{{ paths[1].meta.title }}</div>
        </div>
        <div class="line-top-right-b"></div>
        <div class="menu-shine"></div>
        <div class="line-top-right-a"></div>
        <div class="disc" :class="{'disc-ani':simpleModel}"></div>
      </div>
      <!-- 3 -->
      <div class="menu-item bottom-right-menu" :style="pos[2]" v-if="paths[2]">
        <div class="menu" @click="goPage(paths[2])">{{ paths[2].meta.title }}</div>
        <div class="line-top-right-b"></div>
        <div class="menu-shine"></div>
        <div class="line-top-right-a"></div>
        <div class="disc" :class="{'disc-ani':simpleModel}"></div>
      </div>
      <!-- 4 -->
      <div class="menu-item top-left-menu" :style="pos[3]" v-if="paths[3]">
        <div class="menu" @click="goPage(paths[3])">
          <div style="transform: rotateY(180deg)">{{ paths[3].meta.title }}</div>
        </div>
        <div class="line-top-right-b"></div>
        <div class="menu-shine"></div>
        <div class="line-top-right-a"></div>
        <div class="disc" :class="{'disc-ani':simpleModel}"></div>
      </div>
      <!-- 5-->
      <div class="menu-item bottom-left-menu" :style="pos[4]" v-if="paths[4]">
        <div class="menu" @click="goPage(paths[4])">
          <div style="transform: rotateY(180deg)">{{ paths[4].meta.title }}</div>
        </div>
        <div class="line-top-right-b"></div>
        <div class="menu-shine"></div>
        <div class="line-top-right-a"></div>
        <div class="disc" :class="{'disc-ani':simpleModel}"></div>
      </div>
      <!-- 6-->
      <div class="menu-item top-right-menu" :style="pos[5]" v-if="paths[5]">
        <div class="menu" @click="goPage(paths[5])">{{ paths[5].meta.title }}</div>
        <div class="line-top-right-b"></div>
        <div class="menu-shine"></div>
        <div class="line-top-right-a"></div>
        <div class="disc" :class="{'disc-ani':simpleModel}"></div>
      </div>
    </div>
    <div class="bottom">
      <span v-html="bottomTitle"></span>
      <span>客服服务热线：{{ telphone }}</span>
      <!-- <span>{{ bottomEnTitle }}</span> -->
    </div>
    <div class="exit-sys" @click="exitSystem" title="退出登录">
      <img src="../gateway/images/Tab/28.png" />
    </div>
  </div>
</template>

<script>
import { getAction, postAction } from '@/api/manage'
import Vue from 'vue'
import { mapActions, mapGetters, mapState } from 'vuex'
import { generateIndexRouter, generateBigscreenRouter,getHostNameLocal } from '@/utils/util'
import { ACCESS_TOKEN, PLATFORM_TYPE } from '@/store/mutation-types'
import router from '@/router'
import store from '@/store'
export default {
  name: 'oneClickHelpGateway',
  //属性
  data() {
    return {
      title: window.config.oneClickHelp.title,
      platformTitle: window.config.oneClickHelp.platformTitle,
      logo: window.config.oneClickHelp.helpLogoUrl,
      bottomTitle: window.config.oneClickHelp.bottomTitle,
      telphone: window.config.oneClickHelp.telphone,
      centerScale: 1,
      resizeObserver: null,
      leftNum: 0,
      topNum: 0,
      paths: [],
      terminalCode: null,
      pos: [],
      posFive: [
        { top: '42%', left: '33%' },
        { top: '45%', right: '33%' },
        { top: '51%', left: '39%' },
        { top: '30%', right: '39%' },
        { top: '58%', right: '38%' },
      ],
      posSix: [
        { top: '30%', left: '36%' },
        { top: '50%', right: '33%' },
        { top: '62%', left: '38%' },
        { top: '30%', right: '36%' },
        { top: '62%', right: '38%' },
         { top: '50%', left: '33%' },
      ],
    }
  },
  created() {
    // console.log('用户信息 === ', this.$store.getters.userInfo)
    this.setScale()
    this.entrancePlanning('9')
  },
  mounted() {
    this.getHost()
    this.addResizeObserver()
  },
  beforeDestroy() {
    this.delResizeObserver()
  },
  computed:{
     simpleModel(){
      return window.config.simpleModel === 0
    }
  },
  //方法
  methods: {
    ...mapActions(['Logout', 'GetPermissionList']),
    getHost() {
      this.terminalCode = getHostNameLocal();
    },
    setScale() {
      if (window.innerWidth <= 1920 || window.innerHeight <= 1080) {
        let scaleW = window.innerWidth / 1920
        let scaleH = window.innerHeight / 1080
        this.centerScale = Math.min(scaleH, scaleW)
      } else {
        this.centerScale = 1
      }
      this.leftNum = (window.innerWidth - 1920 * this.centerScale) / 2
      this.topNum = (window.innerHeight - this.centerScale * 1080) / 2
    },
    addResizeObserver() {
      let onClickHelpGateway = document.getElementById('onClickHelpGateway')
      this.resizeObserver = new ResizeObserver((entries) => {
        this.setScale()
      })
      this.resizeObserver.observe(onClickHelpGateway)
    },
    delResizeObserver() {
      let onClickHelpGateway = document.getElementById('onClickHelpGateway')
      if (onClickHelpGateway) {
        this.resizeObserver.unobserve(onClickHelpGateway)
      }
      this.resizeObserver = null
    },
    setMenusQuery(menus) {
      menus.forEach((menu) => {
        if (menu.children && menu.children.length > 0) {
          this.setMenusQuery(menu.children)
        } else {
          menu.query = { hostname: this.terminalCode }
        }
      })
      return menus
    },
    entrancePlanning(index) {
      //内蒙定制 服务中心跳转到配置的url
      // sessionStorage.setItem(PLATFORM_TYPE, index)
      const that = this
      that.GetPermissionList(index).then((res) => {
        if (res === '1') {
          this.$message.warning('没有添加菜单！')
          return
        }
        const menuData = res.result.menu
        // console.log('获取到菜单', menuData)
        // return
        var redirect = ''
        if (menuData && menuData.length > 0) {
          this.paths = menuData.filter((el) => !el.hidden)
          console.log('菜单根数 === ', this.paths)
          if (this.paths.length >= 6) {
            this.pos = this.posSix
          } else {
            this.pos = this.posFive
          }
          // this.setMenusQuery(menuData)
          this.$store.commit('SET_PERMISSIONLIST', menuData)
          let firsMenu = menuData[0]
          redirect = firsMenu.children && firsMenu.children.length > 0 ? firsMenu.children[0].path : firsMenu.path
        } else {
          return
        }

        let constRoutes = []
        if (index === 4 || index === 8) {
          constRoutes = generateBigscreenRouter(menuData)
        } else {
          constRoutes = generateIndexRouter(menuData)
        }
        // console.log('生成的路由啊啊 === ', constRoutes)
        // 添加主界面路由
        store
          .dispatch('UpdateAppRouter', {
            constRoutes,
          })
          .then(() => {
            // 根据roles权限生成可访问的路由表
            // 动态添加可访问路由表
            router.addRoutes(store.getters.addRouters)
            // this.$router.push({
            //   path: redirect,
            // })
            // }
          })
      })
    },
    goPage(menu) {
      if (this.terminalCode === null) {
        this.$message.warning('请检查是否已经添加终端信息？')
        return
      }
      let path = ''
      if (menu.children && menu.children.length > 0) {
        path = menu.children[0].path
      } else {
        path = menu.path
      }
      router.push({ path: path})
    },
    exitSystem() {
      const that = this
      this.$confirm({
        title: '提示',
        content: '真的要注销登录吗 ?',
        okText: '确定',
        cancelText: '取消',
        class:'oneClickHelpConfirmModal',
        onOk() {
          return that
            .Logout({})
            .then((res) => {
              that.$router.push({ path: '/oneClickHelp/login'})
            })
            .catch((err) => {
              that.$message.error({
                title: '错误',
                description: err.message,
              })
            })
        },
        onCancel() {},
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.body {
  overflow-y: auto;
}
.mouse {
  width: 100%;
  height: 100%;
  background: #020a28;
  overflow: hidden;
  position: relative;
  // background-image: url(/oneClickHelp/bg1.png);
  // background-repeat: no-repeat;
  // background-size: 100% 100%;
  // display: flex;
  // align-items: center;
  // justify-content: center;
  .logo {
    position: absolute;
    top: 30px;
    left: 30px;
    z-index: 100;
    display: flex;
    align-items: center;
    img {
      width: 60px;
      height: 60px;
    }
    .logo-text {
      color: #e5e6e7;
      font-size: 36px;
      font-weight: 550;
      letter-spacing: 3px;
      margin-left: 12px;
    }
  }
  .center {
    width: 1920px;
    height: 1080px;
    background-color: #020a28;
    transform-origin: top left;
    background-image: url(/oneClickHelp/bg.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    position: absolute;
    .title {
      width: 477px;
      height: 88px;
      font-size: 90px;
      font-weight: 700;
      color: #ffffff;
      line-height: 88px;
      text-align: center;
      position: absolute;
      letter-spacing: 24px;
      top: calc((1080px - 88px - 80px) / 2);
      left: calc((1920px - 477px) / 2);
    }
    .menu-item {
      position: absolute;
    }
    .drive {
      position: absolute;
      top: 42%;
      left: 33%;
    }
    .knowledge {
      position: absolute;
      top: 51%;
      left: 39%;
    }
    .service {
      position: absolute;
      top: 30%;
      right: 39%;
    }
    .question {
      position: absolute;
      top: 45%;
      right: 33%;
    }
    .my-knowledge {
      position: absolute;
      top: 58%;
      right: 38%;
    }
    .disc {
      width: 60px;
      height: 60px;
      background-image: url(/oneClickHelp/disc.png);
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }
    .disc-ani{
      animation: rotate-wise-one 6s normal infinite linear;
    }
    .top-right-menu {
      .line-top-right-a {
        position: absolute;
        width: 160px;
        height: 2px;
        background: #8a8a8a;
        transform: rotate(50deg);
        left: -120px;
        top: -40px;
      }
      .line-top-right-b {
        position: absolute;
        width: 90px;
        height: 2px;
        background: #8a8a8a;
        left: -181px;
        top: -101px;
      }
      .menu-shine {
        position: absolute;
        width: 45px;
        height: 5px;
        background-image: url(/oneClickHelp/menuShine.png);
        background-repeat: no-repeat;
        background-size: 100% 100%;
        left: -141px;
        top: -102.5px;
      }
      .menu {
        position: absolute;
        width: 211px;
        height: 75px;
        background-image: url(/oneClickHelp/menuBg.png);
        background-repeat: no-repeat;
        background-size: 100% 100%;
        left: -395px;
        top: -150px;
        color: #fff;
        font-size: 28px;
        text-align: center;
        line-height: 75px;
        letter-spacing: 5px;
        font-weight: 550px;
        cursor: pointer;
      }
    }
    .bottom-right-menu {
      .line-top-right-a {
        position: absolute;
        width: 160px;
        height: 2px;
        background: #8a8a8a;
        transform: rotate(-50deg);
        left: -120px;
        bottom: -40px;
      }
      .line-top-right-b {
        position: absolute;
        width: 90px;
        height: 2px;
        background: #8a8a8a;
        left: -181px;
        bottom: -102px;
      }
      .menu-shine {
        position: absolute;
        width: 45px;
        height: 5px;
        background-image: url(/oneClickHelp/menuShine.png);
        background-repeat: no-repeat;
        background-size: 100% 100%;
        left: -141px;
        bottom: -104.5px;
      }
      .menu {
        position: absolute;
        width: 211px;
        height: 75px;
        background-image: url(/oneClickHelp/menuBg.png);
        background-repeat: no-repeat;
        background-size: 100% 100%;
        left: -390px;
        bottom: -150px;
        color: #fff;
        font-size: 28px;
        text-align: center;
        line-height: 75px;
        letter-spacing: 5px;
        font-weight: 550px;
        cursor: pointer;
      }
    }
    .top-left-menu {
      .line-top-right-a {
        position: absolute;
        width: 160px;
        height: 2px;
        background: #8a8a8a;
        transform: rotate(-50deg);
        right: -120px;
        top: -40px;
      }
      .line-top-right-b {
        position: absolute;
        width: 90px;
        height: 2px;
        background: #8a8a8a;
        right: -181px;
        top: -102px;
      }
      .menu-shine {
        position: absolute;
        width: 45px;
        height: 5px;
        background-image: url(/oneClickHelp/menuShine.png);
        background-repeat: no-repeat;
        background-size: 100% 100%;
        right: -141px;
        top: -104.5px;
        transform: rotate(180deg);
      }
      .menu {
        position: absolute;
        width: 211px;
        height: 75px;
        background-image: url(/oneClickHelp/menuBg.png);
        background-repeat: no-repeat;
        background-size: 100% 100%;
        right: -390px;
        top: -150px;
        color: #fff;
        font-size: 28px;
        text-align: center;
        line-height: 75px;
        letter-spacing: 5px;
        font-weight: 550px;
        cursor: pointer;
        transform: rotateY(180deg);
      }
    }
    .bottom-left-menu {
      .line-top-right-a {
        position: absolute;
        width: 160px;
        height: 2px;
        background: #8a8a8a;
        transform: rotate(50deg);
        right: -120px;
        bottom: -40px;
      }
      .line-top-right-b {
        position: absolute;
        width: 90px;
        height: 2px;
        background: #8a8a8a;
        right: -181px;
        bottom: -102px;
      }
      .menu-shine {
        position: absolute;
        width: 45px;
        height: 5px;
        background-image: url(/oneClickHelp/menuShine.png);
        background-repeat: no-repeat;
        background-size: 100% 100%;
        right: -141px;
        bottom: -104.5px;
        transform: rotate(180deg);
      }
      .menu {
        position: absolute;
        width: 211px;
        height: 75px;
        background-image: url(/oneClickHelp/menuBg.png);
        background-repeat: no-repeat;
        background-size: 100% 100%;
        right: -390px;
        bottom: -127px;
        color: #fff;
        font-size: 28px;
        text-align: center;
        line-height: 75px;
        letter-spacing: 5px;
        font-weight: 550px;
        cursor: pointer;
        transform: rotateY(180deg);
      }
    }
  }

  .bottom {
    position: absolute;
    width: 100%;
    height: 10%;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-size: 16px;
    bottom: 12px;
    letter-spacing: 5px;
    span:nth-child(1) {
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #ffffff;
      opacity: 0.5;
      text-align: center;
    }

    span:nth-child(2) {
      font-family: PingFang SC;
      font-weight: 100;
      color: #ffffff;
      opacity: 0.5;
      letter-spacing: 1px;
    }
  }
  .exit-sys {
    width: 25px;
    height: 25px;
    z-index: 1000;
    position: absolute;
    top: 20px;
    right: 20px;
    cursor: pointer;
  }
}

@media (max-width: 1500px) {
  /*旋转所处的位置调整*/
  .elementTop {
    height: 15% !important;
  }

  /**旋转背景**/
  .back-opa {
    height: 100% !important;
    width: 100% !important;
  }

  /*旋转内圈*/
  .entrance-rule,
  .internal-rotate {
    height: 40vh !important;
    width: 40vh !important;
  }

  .internal-rotate {
    top: -14px !important;
  }
}
@keyframes rotate-wise-one {
  0% {
    transform: rotate(0deg);
  }

  50% {
    transform: rotate(180deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>