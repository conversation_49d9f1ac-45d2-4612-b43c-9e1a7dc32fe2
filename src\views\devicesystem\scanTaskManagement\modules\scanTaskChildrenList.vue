<template>
  <a-row :gutter="10" style="height: 100%;" class="vScroll">
    <a-col style="width:100%;height: 100%;display: flex;flex-direction: column">
      <a-card :bordered="false" style="width: 100%; flex: auto">
        <div>
          <div class="colorBox">
            <span class="colorTotal">设备结果列表</span>
          </div>
          <div style="position: absolute; right: 24px; top: 18px; z-index: 10">
            <img
              src="~@assets/return1.png"
              alt
              @click="getGo"
              style="width: 20px; height: 20px; cursor: pointer"
            />
          </div>
        </div>

        <a-table
          ref="table"
          bordered
          rowKey="id"
          :columns="columns"
          :dataSource="dataSource"
          :pagination="ipagination"
          :loading="loading"
          @change="handleTableChange"
        >
          <template slot="tooltip" slot-scope="text">
            <a-tooltip placement="topLeft" :title="text" trigger="hover">
              <div class="tooltip">{{ text }}</div>
            </a-tooltip>
          </template>
          <span slot="action" slot-scope="text,record,index">
            <a
              v-if="!record.isMonitored"
              @click="handleAdd(record,index)"
              style="color: #409eff"
            >设备入库</a>
            <a v-else style="color: rgba(0, 0, 0, 0.4);cursor:default;">已入库</a>
          </span>
        </a-table>
      </a-card>
    </a-col>

    <device-info-modal ref="modalForm" @ok="modalFormOk" :scanTaskObject="scanTaskObject"></device-info-modal>
  </a-row>
</template>

<script>
import { getAction, putAction } from '@/api/manage'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import DeviceInfoModal from '@views/devicesystem/modules/DeviceInfoFormModal.vue'

export default {
  name: 'scanTaskChildrenList',
  mixins: [JeecgListMixin],
  components: {
    DeviceInfoModal
  },
  props: {
    data: {
      type: Object
    }
  },
  watch: {
    scanTaskObject: {
      handler(nv, ov) {
        // console.log(nv, 'scanTaskObject')
      },
      deep: true
    }
  },
  data() {
    return {
      // 表头
      columns: [],
      url: {
        list: '/deviceScan/task/queryRecordsById',
        editRecord: '/deviceScan/task/editRecord' // 修改扫描结果
      },
      currentIndex: 0, // 记录选中的当前设备的索引值
      AllRecords: [], // 记录当前扫描结果的数组
      currentRecord: {}, // 当前扫描结果的这一条记录
      scanTaskObject: {}
    }
  },
  methods: {
    handleColumn(data) {
      let list = data
      let that = this
      that.$nextTick(() => {
        that.columns = []
        that.dataSource = []
        for (let i = 0; i < list.length; i++) {
          let trData = {}
          //定义列字段columns
          if (i === 0) {
            let newList = list[i].recordColumnJson
            for (let k = 0; k < newList.length; k++) {
              let colItem = {
                title: newList[k].name,
                dataIndex: newList[k].code,
                ellipsis: true,
                customHeaderCell: () => ({ style: { textAlign: 'center' } }), //头部单元格水平居中
                customCell: (record, rowIndex) => {
                  let cellStyle = `text-align: center;max-width:400px`
                  return {
                    style: cellStyle
                  }
                }
              }

              that.columns.push(colItem)
            }
          }
          //获取列数据
          let recordColumnJson = list[i].recordColumnJson
          if (recordColumnJson.length > 0) {
            recordColumnJson.forEach(element => {
              trData[element.code] = element.value
            })
          }
          trData['isMonitored'] = list[i].isMonitored
          that.dataSource.push(trData)
        }
        that.columns.push({
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 147,
          scopedSlots: { customRender: 'action' }
        })
      })
    },
    loadData(arg) {
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1
      }
      if (!this.data.id) {
        // 任务id不存在
        return
      }
      var params = this.getQueryParams() //查询条件
      this.loading = true
      getAction(this.url.list, {
        taskId: this.data.id,
        ...params
      }).then(res => {
        if (res.success && res.result) {
          this.AllRecords = res.result.records
          this.handleColumn(res.result.records)
          if (this.dataSource.length < 9) {
            this.clientHeight = false
          }
          this.ipagination.total = res.result.total ? res.result.total : 0
        }
        this.loading = false
      })
    },
    handleAdd: function(record, index) {
      this.currentIndex = index
      this.scanTaskObject = this.data
      this.scanTaskObject['ip'] = record.ip
      this.$refs.modalForm.add()
      this.$refs.modalForm.title = '设备入库'
      this.$refs.modalForm.disableSubmit = false
    },
    modalFormOk() {
      //设备入库成功时,修改扫描结果,改为已入库并置灰按钮,重载列表
      this.editRecord()
    },
    editRecord() {
      let param = this.AllRecords[this.currentIndex]
      param['isMonitored'] = 1
      putAction(this.url.editRecord, {
        ...param
      })
        .then(res => {
          if (res.success) {
            this.$message.success('设备入库成功！')
            this.loadData()
          } else {
            this.$message.warning(res.message)
          }
        })
        .catch(err => {
          this.$message.warning(err.message)
        })
    },
    //返回上一级
    getGo() {
      this.$parent.pButton2(0)
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
.colorBox {
  margin-top: 10px;
  margin-bottom: 10px;
  .colorTotal {
    padding-left: 7px;
    border-left: 4px solid #1e3674;
  }
}
</style>