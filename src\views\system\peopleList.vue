<template>
  <a-row :gutter='10' style='height: 100%' class='vScroll'>
    <a-col style='width: 100%; height: 100%; display: flex; flex-direction: column'>
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <!-- 查询区域 -->
        <div class='table-page-search-wrapper'>
          <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
            <a-row :gutter='24' ref='row'>
              <a-col :span='spanValue'>
                <a-form-item label='用户账号'>
                  <a-input :maxLength="maxLength" placeholder='请输入用户账号' :allowClear='true' autocomplete='off' v-model='queryParam.username'>
                  </a-input>
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label='用户姓名'>
                  <a-input :maxLength="maxLength" placeholder='请输入用户姓名' :allowClear='true' autocomplete='off' v-model='queryParam.realname'>
                  </a-input>
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label='服务优先级'>
                  <a-input-number v-model='queryParam.priority' :min="1" placeholder='请输入服务优先级' style="width: 100%">
                  </a-input-number>
                </a-form-item>
              </a-col>
              <a-col :span='colBtnsSpan()'>
                <span class='table-page-search-submitButtons'
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button type='primary' class='btn-search btn-search-style' @click='searchQuery'>查询</a-button>
                  <a-button class='btn-reset btn-reset-style' @click='searchReset' style='margin-left: 8px'>重置
                  </a-button>
                  <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <!-- 操作按钮区域 -->
        <div class='table-operator table-operator-style'>
          <a-button @click='handleAdd'>新增</a-button>
          <a-dropdown v-if='selectedRowKeys.length > 0'>
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item key='1' @click='batchDel'>删除</a-menu-item>
            </a-menu>
            <a-button>
              批量操作
              <a-icon type='down' />
            </a-button>
          </a-dropdown>
        </div>
        <!-- table区域-begin -->
        <a-table ref='table' bordered rowKey='id' :columns='columns' :dataSource='dataSource'
          :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}" :pagination='ipagination' :loading='loading'
          :rowSelection='{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }' @change='handleTableChange'>
          <template slot='avatarslot' slot-scope='text, record'>
            <div class='anty-img-wrap'>
              <a-avatar shape='square' :src='getAvatarView(record.avatar)' icon='user' />
            </div>
          </template>
          <span slot="action" slot-scope="text, record" class="caozuo">
            <a href="javascript:;" @click="handleDetailPage(record)">查看</a>
            <a-divider type="vertical" />
            <a @click="handleEdit(record)">编辑</a>
            <a-divider type="vertical" />
            <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
              <a style='color: #409eff'>删除</a>
            </a-popconfirm>
          </span>
        </a-table>
        <!-- table区域-end -->
      </a-card>
      <people-modal ref='modalForm' @ok='modalFormOk'> </people-modal>
    </a-col>
  </a-row>
</template>

<script>
  import peopleModal from './modules/peopleModal'
  import {
    ajaxGetDictItems
  } from '@/api/api'
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import JInput from '@/components/jeecg/JInput'
  import JSuperQuery from '@/components/jeecg/JSuperQuery'
  import {
    YqFormSearchLocation
  } from '@/mixins/YqFormSearchLocation'
  import {
    getAction,
    deleteAction,
    getFileAccessHttpUrl
  } from '@/api/manage'

  export default {
    name: 'peopleList',
    mixins: [JeecgListMixin, YqFormSearchLocation],
    components: {
      JInput,
      peopleModal,
      JSuperQuery
    },
    data() {
      return {
        maxLength:50,
        description: '服务商管理页面',
        formItemLayout: {
          labelCol: {
            style: 'width:90px'
          },
          wrapperCol: {
            style: 'width:calc(100% - 90px)'
          }
        },
        fwList: [],
        queryParam: {},
        columns: [{
            title: '用户账号',
            dataIndex: 'username'
          },
          {
            title: '用户姓名',
            dataIndex: 'realname'
          },
          {
            title: '工号',
            dataIndex: 'workNo',
          },
          {
            title: '服务优先级',
            dataIndex: 'priority',
          },
          {
            title: '头像',
            dataIndex: 'avatar',
            scopedSlots: {
              customRender: 'avatarslot'
            }
          },
          {
            title: '邮箱',
            dataIndex: 'email',
          },
          {
            title: '手机号',
            dataIndex: 'phone',
          },
          {
            title: '座机',
            dataIndex: 'telephone',
          },
          {
            title: '操作',
            dataIndex: 'action',
            scopedSlots: {
              customRender: 'action'
            },
            fixed: 'right',
            align: 'center',
            width: 170
          }
        ],
        url: {
          list: '/serviceProvider/user/list',
          delete: '/serviceProvider/user/deleteUser',
          deleteBatch: '/serviceProvider/user/deleteUsers',
        }
      }
    },

    created() {
      this.getDictData('fw_priority')
    },
    methods: {
      loadData(arg) {
        if (!this.url.list) {
          this.$message.error('请设置url.list属性!')
          return
        }
        //加载数据 若传入参数1则加载第一页的内容
        if (arg === 1) {
          this.ipagination.current = 1
        }
        var params = this.getQueryParams() //查询条件
        this.loading = true
        getAction(this.url.list, params).then((res) => {
          if (res.success && res.result) {
            this.dataSource = res.result.records || res.result
            if (this.dataSource.length < 9) {
              this.clientHeight = false
            }
            this.ipagination.total = res.result.total ? res.result.total : 0
          } else {
            this.$message.warning(res.message)
          }
          this.loading = false
        })
      },
      getAvatarView: function (avatar) {
        return getFileAccessHttpUrl(avatar)
      },
      getDictData(dictCode) {
        ajaxGetDictItems(dictCode, null).then((res) => {
          if (res.success) {
            this.fwList = res.result
          }
        })
      },
      handleDelete: function (id) {
        if (!this.url.delete) {
          this.$message.error('请设置url.delete属性!')
          return
        }
        var that = this
        deleteAction(that.url.delete, {
          userId: id
        }).then((res) => {
          if (res.success) {
            //重新计算分页问题
            that.reCalculatePage(1)
            that.$message.success(res.message)
            that.loadData()
          } else {
            that.$message.warning(res.message)
          }
        })
      },
      batchDel: function () {
        if (!this.url.deleteBatch) {
          this.$message.error('请设置url.deleteBatch属性!')
          return
        }
        if (this.selectedRowKeys.length <= 0) {
          this.$message.warning('请选择一条记录！')
          return
        } else {
          var ids = ''
          for (var a = 0; a < this.selectedRowKeys.length; a++) {
            ids += this.selectedRowKeys[a] + ','
          }
          var that = this
          this.$confirm({
            title: '确认删除',
            okText: '是',
            cancelText: '否',
            content: '是否删除选中数据?',
            onOk: function () {
              that.loading = true
              deleteAction(that.url.deleteBatch, {
                  userIds: ids
                })
                .then((res) => {
                  if (res.success) {
                    //重新计算分页问题
                    that.reCalculatePage(that.selectedRowKeys.length)
                    that.$message.success(res.message)
                    that.loadData()
                    that.onClearSelected()
                  } else {
                    that.$message.warning(res.message)
                  }
                })
                .finally(() => {
                  that.loading = false
                })
            }
          })
        }
      },
    },
  }
</script>
<style lang='less' scoped>
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';

  ::v-deep .ant-dropdown-menu-item a {
    color: #409eff !important;
  }

  ::v-deep .ant-dropdown-menu-item .dontDelete {
    color: rgba(0, 0, 0, 0.35) !important;
  }

  /*表头样式*/
  ::v-deep .ant-table-thead>tr>th {
    text-align: center;
    white-space: nowrap;
  }

  /*内容对齐方式、省略显示*/
  ::v-deep .ant-table-tbody>tr>td {
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
  }
</style>