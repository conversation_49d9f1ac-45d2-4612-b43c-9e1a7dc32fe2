<template>
  <div style='width: 100%;height: 100%' :id='chartId'></div>
</template>
<script>
import * as echarts from 'echarts'
import 'echarts-liquidfill'
// import 'echarts/lib/component/graphic'
export default {
  name: 'PolarLiquidFill',
  props: {
    chartId: {
      type: String,
      default: 'polarLiquidFill'
    }
  },
  data() {
    return {
      myEchart: null,
      polarNum: 180,
      polarData1: [],
      polarData2: [],
      polarTimer: null,
      statusRate: 0.92,
      statusText:"正常",
      waveData: [],
      startAngle: 0
    }
  },
  created() {
    this.waveData = this.generateWaveData(this.polarNum)
  },
  mounted() {
    this.initEcharts()
  },
  beforeDestroy() {
    if (this.polarTimer) {
      clearInterval(this.polarTimer)
      this.polarTimer = null;
    }
    if (this.myEchart) {
      this.myEchart.dispose();
      this.myEchart = null;
    }
  },
  methods: {
    initEcharts() {
      var chartDom = document.getElementById(this.chartId)
      this.myEchart = echarts.init(chartDom)
      var option
      this.polarData1 = []
      this.polarData2 = []
      for (var i = 0; i < this.polarNum; i++) {
        this.polarData1.push({
          value: 100,
        })
        this.polarData2.push({
          value: this.waveData[i],
            itemStyle: {
            color: this.getPolarColor(i),
            opacity: 0.5
          }
        })
      }
      option = {
        title: [
          {
            text: (this.statusRate * 100).toFixed(0),
            left: '50%',
            top: '20%',
            textAlign: 'center',
            textStyle: {
              fontSize: 65,
              fontWeight: 'bolder',
              fontFamily: "DINPro",
              color: '#A5A5A5',
              textShadowColor:"#FFFFFF",
              textShadowBlur: 3,
            },
            rich: {
              a: {
                color: 'red',
                lineHeight: 10
              },
            },
            subtext: this.statusText,
            subtextStyle: {
              fontSize: 19,
              fontWeight: 'bolder',
              color: '#A5A5A5',
              // fontFamily: "Source Han Sans CN",
              textShadowColor:"#FEFEFE",
              textShadowBlur: 0,
            }
          },
        ],
        polar: {
          radius: ['85%', '100%']
        },
        radiusAxis: {
          show: false
        },
        angleAxis: {
          show: false,
          type: 'category',
          data: this.polarData1,
          startAngle: this.startAngle,
        },
        series: [
          {
            type: 'bar',
            data: this.polarData1,
            color: '#18456F',
            coordinateSystem: 'polar',
            label: {
              show: false,
            }
          },
          {
            type: 'bar',
            data: this.polarData2,
            color: '#18456F',
            barGap: '-100%',
            coordinateSystem: 'polar',
            label: {
              show: false,
            }
          },
          {
            type: 'liquidFill',
            shape: 'circle',
            radius: '78%',
            center: ['50%', '50%'],
            data: [this.statusRate],
            waveAnimation: true,
            hoverAnimation: false,
            // 球体配置
            outline: {
              show: true,
              borderDistance: 0,
              itemStyle: {
                borderWidth: 5,
                borderColor: {
                  type: 'linear',
                  x: 1,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [{
                    offset: 0,
                    color: '#48D3FF'
                  }, {
                    offset: 0.5,
                    color: '#FFFFFF'
                  }, {
                    offset: 1,
                    color: '#48D3FF'
                  }],
                  globalCoord: false
                },
              }
            },
            backgroundStyle: {
              color: 'rgba(235,241,254,0)',
            },
            color: [{
              type: 'linear',
              x: 0,
              y: 0,
              x2: 1,
              y2: 0,
              colorStops: [
                {
                  offset: 0,
                  color: '#6A82F1',
                },
                {
                  offset: 0.25,
                  color: '#65D4FA',
                },
                {
                  offset: 1,
                  color: '#65D4FA',
                },
              ],
              globalCoord: false,
            }],
            label: {
              show: false,
              textStyle: {
                color: '#ffffff',
                fontSize: 65
              },
            }
          }
        ],
        animation: false
      }

      option && this.myEchart.setOption(option)
      this.polarInterval();
    },
    polarInterval(){
      this.polarTimer = setInterval(() => {
        this.polarData2 = []
        // for (var i = 0; i < waveData.length; i++) {
        //   this.polarData2.push({
        //     value: this.waveData[i],
        //     itemStyle: {
        //       color: this.getPolarColor(i),
        //       opacity: 0.5
        //     }
        //   })
        // }
        if(this.startAngle >= 360){
          this.startAngle = 0
        }
        else{
          this.startAngle += 20
        }
        this.myEchart.setOption({
          angleAxis: {
            startAngle:this.startAngle ,
          },
          // series: [
          //   {},
          //   {
          //     data: this.polarData2,
          //   },
          // ]
        })
      }, 1000)
    },
    getPolarColor(i){
      let max = this.polarNum / 2
      let son = 0;
      if(i<= max){
        son = i
      }
      else{
        son = this.polarNum - i
      }
      let percentage = son / max * 100;
      return this.interpolateColor('#2E8BCD', '#ffffff',percentage )
    },
    interpolateColor(color1, color2, percentage) {
      // Convert hex color to RGB
      const hexToRgb = (hex) => {
        const bigint = parseInt(hex.slice(1), 16);
        return {
          r: (bigint >> 16) & 255,
          g: (bigint >> 8) & 255,
          b: bigint & 255,
        };
      };

      const rgbToHex = (r, g, b) => {
        return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`;
      };

      const startRGB = hexToRgb(color1);
      const endRGB = hexToRgb(color2);

      // Interpolate each channel
      const r = Math.round(startRGB.r + (endRGB.r - startRGB.r) * (percentage / 100));
      const g = Math.round(startRGB.g + (endRGB.g - startRGB.g) * (percentage / 100));
      const b = Math.round(startRGB.b + (endRGB.b - startRGB.b) * (percentage / 100));

      // Convert back to hex
      return rgbToHex(r, g, b);
    },
    generateWaveData(numPoints = 100, min = 60, max = 90, cycles = 5) {
      const data = [];
      const amplitude = (max - min) / 2;  // 振幅
      const verticalShift = min + amplitude; // 垂直偏移（中心线）

      for (let i = 0; i < numPoints; i++) {
        // 正弦波计算（角度转换为弧度）
        const radians = (i / numPoints) * Math.PI * 2 * cycles;
        const value = Math.sin(radians) * amplitude + verticalShift;

        // 确保精度为整数（可选）
        data.push(Math.round(value * 100) / 100); // 保留两位小数
      }

      return data;
    }
  }
}
</script>


<style scoped lang='less'>

</style>