<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
        <a-row :gutter="24" ref="row">
          <a-col v-show="getVisible('name')" :span="spanValue">
            <a-form-item :label="getTitle('name')">
              <j-input :maxLength='maxLength' :allow-clear="true" autocomplete="off" placeholder="请输入名称" v-model="queryParam.name" />
            </a-form-item>
          </a-col>
          <a-col v-show="getVisible('date')" :span="spanValue">
            <a-form-item :label="getTitle('date')">
              <a-range-picker style="width: 100%" :getCalendarContainer="node=> node.parentNode" format="YYYY-MM-DD"
                v-model="queryParam.searchDate" :placeholder="['开始日期', '结束日期']" @change="onCreteTimeChange" />
            </a-form-item>
          </a-col>
          <a-col v-show="getVisible('holidayType')" :span="spanValue">
            <a-form-item :label="getTitle('holidayType')">
              <a-select @change="changes" v-model="queryParam.holidayType" placeholder="请选择类型"
                :getPopupContainer='node=>node.parentNode'>
                <a-select-option :value="true">国家法定节假日</a-select-option>
                <a-select-option :value="false">自定义</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col v-show="getVisible('holiday')" :span="spanValue">
            <a-form-item :label="getTitle('holiday')">
              <a-select @change="changes" v-model="queryParam.holiday" placeholder="请选择状态"
                :getPopupContainer='node=>node.parentNode'>
                <a-select-option :value="true">休息日</a-select-option>
                <a-select-option :value="false">补班日</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="spanValue">
            <span class="table-page-search-submitButtons" style="overflow: hidden;">
              <a-button icon="search" type="primary" @click="searchQuery">查询</a-button>
              <a-button icon="reload" style="margin-left: 8px" @click="searchReset">重置</a-button>
              <a v-if="queryItems.length>0" style="margin-left: 8px" @click="doToggleSearch">{{queryName}}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
              </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!--自定义查询项 -->
    <div v-if="toggleSearchStatus" class="custom-query-item">
      <a-checkbox-group v-model="settingQueryItems" :defaultValue="settingQueryItems" style="width:100%"
        @change="onQuerySettingsChange">
        <a-row :gutter="24">
          <template v-for="(item,index) in queryItems">
            <a-col v-show='item.checked' :span='querySpanValue' class='col-checkbox'>
              <a-checkbox :disabled="item.disabled" :value="item.dataIndex">
                <j-ellipsis :length="7" :value="item.title"></j-ellipsis>
              </a-checkbox>
            </a-col>
          </template>
        </a-row>
      </a-checkbox-group>
    </div>
    <!-- 自定义查询项-END -->

    <!-- table区域-begin -->
    <div class="table-operator">
      <a-button icon="plus" type="primary" @click="handleAdd">新增</a-button>
      <a-button type="default" icon="download" @click="handleExportXls('节假日表')" v-has='"position:exportExcel"'>导出
      </a-button>
      <a-upload v-has='"position:importExcel"' name="file" :showUploadList="false" :multiple="false"
        :headers="tokenHeader" :action="importExcelUrl" @change="handleImportExcel">
        <a-button type="default" icon="import">导入</a-button>
      </a-upload>
      <a-upload v-has='"position:importJson"'  name="file" :showUploadList="false" :multiple="false"
                 :headers="tokenHeader" :action="importJsonUrl" @change="handleImportExcel">
        <a-button type="default" icon="import">JSON导入</a-button>
      </a-upload>
    </div>
    <div>
      <a-table ref="table" :columns="columns" :dataSource="dataSource" :loading="loading" :pagination="ipagination"
        bordered rowKey="id" size="middle" @change="handleTableChange">
        <template slot="holiday" slot-scope="holiday">
          <span v-if="holiday">休息日</span>
          <span v-else>补班日</span>
        </template>
        <template slot="holidayType" slot-scope="holidayType">
          <span v-if="holidayType">国家法定节假日</span>
          <span v-else>自定义</span>
        </template>
        <span slot="action" slot-scope="text, record" v-if="!record.holidayType">
          <template>
            <a @click="handleEdit(record)">编辑</a>
            <a-divider type="vertical" />
            <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
              <a>删除</a>
            </a-popconfirm>
          </template>
        </span>
      </a-table>
    </div>
    <customFestivalModal ref="modalForm" @ok="ok"></customFestivalModal>
  </a-card>
</template>

<script>
  import {
    JeecgListMixin
  } from "@/mixins/JeecgListMixin";
  import {
    YqFormSeniorSearchLocation
  } from "@/mixins/YqFormSeniorSearchLocation";
  import customFestivalModal from "./customFestivalModal"

  export default {
    mixins: [JeecgListMixin, YqFormSeniorSearchLocation],
    components: {
      customFestivalModal
    },
    name: "index",
    data() {
      return {
        maxLength:50,
        queryParam: {
          date: "",
        },
        isorter: {
          column: "date",
          order: "desc"
        },
        formItemLayout: {
          labelCol: {
            style: 'width:50px',
          },
          wrapperCol: {
            style: 'width:calc(100% - 50px)'
          }
        },
        //表头
        columns: [{
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            isUsed: false,
            customCell: () => {
              let cellStyle = 'text-align:center;width:60px'
              return {
                style: cellStyle
              }
            },
            customRender: function (t, r, index) {
              return parseInt(index) + 1
            }
          },
          {
            title: '名称',
            dataIndex: 'name',
            isUsed: true,
            customCell: () => {
              let cellStyle = 'text-align:center'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '日期',
            dataIndex: 'date',
            isUsed: true,
            sorter: true,
            customCell: () => {
              let cellStyle = 'text-align:center'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '类型',
            sorter: true,
            isUsed: true,
            dataIndex: 'holidayType',
            scopedSlots: {
              customRender: 'holidayType'
            },
            customCell: () => {
              let cellStyle = 'text-align:center'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '状态',
            sorter: true,
            isUsed: true,
            dataIndex: 'holiday',
            scopedSlots: {
              customRender: 'holiday'
            },
            customCell: () => {
              let cellStyle = 'text-align:center'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            isUsed: false,
            fixed: 'right',
            width: 150,
            scopedSlots: {
              customRender: 'action'
            }
          }
        ],
        url: {
          list: '/festival/custom/list',
          delete: '/festival/custom/delete',
          exportXlsUrl: '/festival/custom/exportXls',
          importExcelUrl: '/festival/custom/importExcel',
          importJsonUrl: '/festival/custom/importJson',
        }
      }
    },
    computed: {
      importExcelUrl: function () {
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
      },
      importJsonUrl: function () {
        return `${window._CONFIG['domianURL']}/${this.url.importJsonUrl}`
      }
    },
    created() {
      this.getColumns(this.columns)
    },
    methods: {
      changes() {
        this.$forceUpdate()
      },
      ok() {
        this.loadData()
      },
      onCreteTimeChange(value, dateString) {
        this.queryParam.startTime = dateString[0]
        this.queryParam.endTime = dateString[1]
      },
    }
  }
</script>
<style scoped lang='less'>
  @import '~@assets/less/common.less';
  @import '~@assets/less/YQCommon.less';
</style>
