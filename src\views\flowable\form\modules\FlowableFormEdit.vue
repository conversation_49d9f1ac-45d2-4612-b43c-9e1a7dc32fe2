<template>
  <j-modal :title="title"  :centered='true' :visible="visible" :destroyOnClose="true" fullscreen :footer='null'
           @ok="handleOk"  @cancel="handleCancel" cancelText="关闭">
    <k-form-design ref="KForm" @save="save($event, 'parent')" hideResetHint hideBackBtn />
  </j-modal>
</template>
<script>
  import {
    getAction,
    putAction
  } from '@/api/manage'
  export default {
    name: 'FlowableFormEdit',
    props:{
      title: {
        type: String,
        default: '表单设计器',
      }
    },
    data() {
      return {
        formKey: undefined,
        formData: {
          formKey: undefined,
          formName: '',
          formJson: '',
        },
        url: {
          queryByIdUrl: '/flowable/form/queryById',
          save: '/flowable/form/update',
        },
        visible: false,
      }
    },
    created() {
      console.log("edit创建")
      // if (this.$route.query && this.$route.query.formKey) {
      //   this.formKey = this.$route.query.formKey
      // }
      // // if (this.$route.params && this.$route.params["formkey"]) {
      // //   this.formKey = this.$route.params["formkey"]
      // // }
      // this.getFormData()
    },
    destroyed() {
      console.log("edit销毁")
    },
    methods: {
      show(formKey) {
        if(formKey) {
          this.formKey = formKey;
          this.getFormData()
          this.visible = true
        }
        // else{
        //   this.$message.warning('表单')
        // }
      },
      handleOk() {
        this.close();
      },
      handleCancel() {
        this.close();
      },
      handleDetail() {
      },
      close() {
        this.$emit('close');
        this.visible = false;
      },
      getFormData() {
        if (!this.formKey) {
          this.$message.error('formKey is null')
          return
        }
        getAction(this.url.queryByIdUrl, {
          id: this.formKey
        }).then((res) => {
          const data = res.result;
          if (data != undefined && data != null) {
            this.formData = data;
            let json = data.formJson;
            setTimeout(() => this.$refs.KForm.setJSON(json ? json : undefined), 100);
          }
        })
      },
      save($event, parameter) {
        this.formData.formJson = $event
        putAction(this.url.save, this.formData).then((res) => {
          if (res.code == 200) {
            this.$message.success(res.message)
          } else {
            this.$message.error('更新失败！')
          }
        })
      },
    },
  }
</script>
<style scoped lang='less'>
/deep/.ant-modal-body {
  padding:12px 12px 0;
}
</style>