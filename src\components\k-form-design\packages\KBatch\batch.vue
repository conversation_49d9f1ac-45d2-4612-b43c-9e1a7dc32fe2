<!--
 * @Description: 动态表格 用于批量填入数据
 * @Author: kcz
 * @Date: 2020-03-27 18:36:56
 * @LastEditors: kcz
 * @LastEditTime: 2021-05-14 14:04:14
 -->
<template>
  <a-form-model ref="dynamicValidateForm" layout="inline" :model="dynamicValidateForm">
    <a-table
      class="batch-table"
      :pagination="false"
      :rowKey="'id'"
      :columns="columns"
      :dataSource="dynamicValidateForm.domains"
      bordered
      :scroll="{
        x: calculateTableWidth(),
        y: record.options.scrollY,
      }"
      :style="{ width: '100%', minWidth: calculateTableWidth() + 'px' }"
    >
      <template v-for="item in record.list" :slot="item.key" slot-scope="text, record, index">
        <KFormModelItem
          :key="item.key + '1'"
          :ref="['batch', 'selectInputList'].includes(item.type) && 'KBatch'"
          :record="item"
          :config="config"
          :parentDisabled="disabled"
          :index="index"
          :domains="dynamicValidateForm.domains"
          :dynamicData="dynamicData"
          :dataSource="dataSources[item.key] || []"
          v-model="record[item.model]"
          @input="handleInput"
        />
      </template>
      <template slot="dynamic-opr-button" slot-scope="text, record">
        <a-icon
          title="删除该行"
          v-if="!disabled"
          class="dynamic-opr-button"
          type="minus-circle-o"
          @click="removeDomain(record)"
        />
        <a-icon
          title="复制添加"
          v-if="!disabled"
          type="copy-o"
          class="dynamic-opr-button"
          @click="copyDomain(record)"
        />
      </template>
    </a-table>
    <!-- 新增：增加按钮可隐藏 -->
    <a-button v-if="!disabled && !record.options.hideAddBtn" type="dashed" :disabled="disabled" @click="addDomain">
      <a-icon type="plus" />增加
    </a-button>
  </a-form-model>
</template>

<script>
import KFormModelItem from './module/KFormModelItem'
import { httpAction, getAction } from '@/api/manage'
import { loadTreeData, ajaxGetDictItems, getDictItemsFromCache } from '@/api/api'
export default {
  name: 'KBatch',
  props: ['record', 'value', 'dynamicData', 'config', 'parentDisabled', 'parentFormData'],

  components: {
    KFormModelItem,
  },
  created() {
    // console.log('初始化动态表格 === ', this.record)
    this.getDataSources()
  },
  mounted() {
    // 组件挂载后确保数据结构完整
    this.$nextTick(() => {
      this.ensureCompleteRowStructure()
    })
  },
  watch: {
    value: {
      // value 需要深度监听及默认先执行handler函数
      handler(val) {
        this.dynamicValidateForm.domains = val || []
        // 确保所有行都有完整的字段结构
        this.ensureCompleteRowStructure()
      },
      immediate: true,
      deep: true,
    },
    // 监听record.list的变化，当动态添加新字段时同步到现有行
    'record.list': {
      handler(newList, oldList) {
        if (newList && oldList && newList.length !== oldList.length) {
          this.syncNewFieldsToExistingRows(newList, oldList)
        }
      },
      deep: true,
    },
  },
  data() {
    return {
      dynamicValidateForm: {
        domains: [],
      },
      dataSources: {},
      optionsData: {} // 新增
    }
  },
  computed: {
    listLength() {
      return this.record.list.filter((item) => !item.options.hidden).length
    },
    columns() {
      const columns = []
      if (!this.record.options.hideSequence) {
        columns.push({
          title: '序号',
          dataIndex: 'sequence_index_number',
          width: 50,
          align: 'center',
          customRender: (text, record, index) => {
            return index + 1
          },
        })
      }
      columns.push(
        ...this.record.list
          .filter((item) => !item.options.hidden)
          .map((item, index) => {
            // 根据组件类型和tableWidth设置列宽
            let width = 190; // 默认宽度
            if (item.type === 'batch') {
              width = 400; // 嵌套batch需要更大的宽度
            } else if (item.options.tableWidth) {
              // 使用控件设置的tableWidth
              const tableWidth = item.options.tableWidth;
              // 解析宽度值，支持px、%、em、rem等单位
              if (tableWidth.includes('px')) {
                width = parseInt(tableWidth);
              } else if (tableWidth.includes('%')) {
                // 对于百分比，假设最小宽度为150px
                width = 150;
              } else {
                // 默认宽度
                width = 190;
              }
            }

            return {
              title: item.options.isEvaluationField
                ? `★ ${item.label}`
                : item.label,
              dataIndex: item.key,
              width: width,
              scopedSlots: { customRender: item.key },
              // 为评估指标列添加特殊的class
              className: item.options.isEvaluationField ? 'evaluation-column-header' : '',
            }
          })
      )
      // 新增：操作列可隐藏
      if (!this.disabled && !this.record.options.hideOprCol) {
        columns.push({
          title: '操作',
          dataIndex: 'dynamic-opr-button',
          width: '50px',
          align: 'center',
          scopedSlots: { customRender: 'dynamic-opr-button' },
        })
      }
      return columns
    },
    rowKey(){
      return this.record.options.rowKey || "key"
    },
    disabled() {
      return this.record.options.disabled || this.parentDisabled
    },
  },
  methods: {
    // 计算表格宽度，考虑嵌套batch和tableWidth的情况
    calculateTableWidth() {
      let totalWidth = 0;

      // 序号列宽度
      if (!this.record.options.hideSequence) {
        totalWidth += 60;
      }

      // 计算每列的宽度
      this.record.list.forEach(item => {
        if (!item.options.hidden) {
          if (item.type === 'batch') {
            // 嵌套batch需要更大的宽度
            totalWidth += 400;
          } else if (item.options.tableWidth) {
            // 使用控件设置的tableWidth
            const width = item.options.tableWidth;
            // 解析宽度值，支持px、%、em、rem等单位
            if (width.includes('px')) {
              totalWidth += parseInt(width);
            } else if (width.includes('%')) {
              // 对于百分比，假设最小宽度为150px
              totalWidth += 150;
            } else {
              // 默认宽度
              totalWidth += 190;
            }
          } else {
            totalWidth += 190;
          }
        }
      });

      // 操作列宽度
      if (!this.disabled && !this.record.options.hideOprCol) {
        totalWidth += 80;
      }

      return totalWidth;
    },

    parseJsonString(dataString, type = 1) {
      try {
        return JSON.parse(dataString)
      } catch (error) {
        console.log('解析参数报错', error)
        return type === 1 ? {} : []
      }
    },
    //初始化数据元
    getDataSources() {
      this.record.list.forEach((el) => {
        this.initOptions(el)
      })
    },
    initOptions(record) {
      let options = record.options
      if (options && options.dynamic) {
        if (options.dynamic === 'static') {
          this.dataSources[record.key] = record.options.staticOptions
        } else if (options.dynamic === 'dynamic') {
          ajaxGetDictItems(options.dynamicKey).then((res) => {
            if (res.success) {
              this.dataSources[record.key] = res.result.map((el) => {
                el.label = el.title
                return el
              })
            }
          }).catch(error => {
            console.error('[KBatch] 动态字典选项获取异常:', error)
          })
        } else if (options.dynamic === 'ajax' && !this.isPanel) {
          // 编辑面板中的不获取接口数据；
          let ajaxData = options.ajaxData
          if (!ajaxData.url) {
            this.optionsData = []
            console.log('[KBatch] Ajax配置无URL，跳过')
            return
          }
          console.log('[KBatch] 获取Ajax选项:', {
            fieldKey: record.key,
            url: ajaxData.url
          })
          let params = this.parseJsonString(ajaxData.params)
          let header = this.parseJsonString(ajaxData.header)
          httpAction(ajaxData.url, params, ajaxData.type, header).then((res) => {
            let fd = Function('"use strict";return (' + ajaxData.callFunc + ')')()
            this.dataSources[record.key] = fd(res)
            this.$forceUpdate() // 强制更新确保选项显示
          }).catch(error => {
            console.error('[KBatch] Ajax选项获取异常:', error)
          })
        }
      } else if (record.options && record.options.options) {
        // 处理直接配置的选项
        this.dataSources[record.key] = record.options.options
      } else if (['select', 'radio', 'checkbox'].includes(record.type)) {
        // 对于选择类型的字段，如果没有配置数据源，设置空数组并警告
        this.dataSources[record.key] = []
      }
    },
    validationSubform() {
      let verification
      this.$refs.dynamicValidateForm.validate((valid) => {
        verification = valid
      })
      if(verification && this.$refs.KBatch && this.$refs.KBatch.length>0){
        this.$refs.KBatch.forEach(item=>{
          if(item.validationSubform){
            verification = verification && item.validationSubform()
          }
        })
      }
      return verification
    },
    resetForm() {
      this.$refs.dynamicValidateForm.resetFields()
    },
    removeDomain(item) {
      const index = this.dynamicValidateForm.domains.indexOf(item)
      if (index !== -1) {
        this.dynamicValidateForm.domains.splice(index, 1)
      }
      this.handleInput()
    },
    copyDomain(record) {
      const data = {}
      this.record.list.forEach((item) => {
        if (item.type === 'batch' && Array.isArray(record[item.model])) {
          // 深拷贝嵌套batch的数据
          data[item.model] = JSON.parse(JSON.stringify(record[item.model]))
        } else {
          // 确保复制的值存在，如果不存在则使用默认值
          data[item.model] = record[item.model] !== undefined ? record[item.model] : item.options.defaultValue
        }
      })

      // 确保复制的行包含所有字段
      this.record.list.forEach((item) => {
        if (!(item.model in data)) {
          if (item.type === 'batch') {
            data[item.model] = item.options.defaultValue || []
          } else {
            data[item.model] = item.options.defaultValue
          }
        }
      })

      this.dynamicValidateForm.domains.push({
        ...data,
        key: Date.now(),
      })
      this.handleInput()
    },
    addDomain() {
      const data = {}
      this.record.list.forEach((item) => {
        if (item.type === 'batch') {
          // 嵌套batch的默认值应该是空数组
          data[item.model] = item.options.defaultValue || []
        } else {
          data[item.model] = item.options.defaultValue
        }
      })

      const newRow = {
        ...data,
        key: Date.now(),
      }
      this.dynamicValidateForm.domains.push(newRow)

      // 确保新行立即获得数据源
      this.$nextTick(() => {
        this.ensureDataSourcesForNewRow()
        this.handleInput()
      })
    },

    // 确保所有行都有完整的字段结构
    ensureCompleteRowStructure() {

      if (!this.dynamicValidateForm.domains || this.dynamicValidateForm.domains.length === 0) {
        return
      }

      const allFields = this.record.list.map(item => item.model)
      let hasChanges = false
      const missingFieldsLog = []
      this.dynamicValidateForm.domains.forEach((row, index) => {
        const missingFields = []
        allFields.forEach(fieldModel => {
          if (!(fieldModel in row)) {
            // 找到对应的字段配置
            const fieldConfig = this.record.list.find(item => item.model === fieldModel)
            if (fieldConfig) {
              if (fieldConfig.type === 'batch') {
                row[fieldModel] = fieldConfig.options.defaultValue || []
              } else {
                row[fieldModel] = fieldConfig.options.defaultValue
              }
              hasChanges = true
              missingFields.push({
                field: fieldModel,
                defaultValue: fieldConfig.options.defaultValue,
                type: fieldConfig.type
              })
            }
          }
        })

        if (missingFields.length > 0) {
          missingFieldsLog.push({
            rowIndex: index,
            missingFields: missingFields
          })
        }
      })

      if (hasChanges) {
        this.handleInput()
      }
    },

    // 当动态添加新字段时，同步到现有行
    syncNewFieldsToExistingRows(newList, oldList) {
      if (!this.dynamicValidateForm.domains || this.dynamicValidateForm.domains.length === 0) {
        return
      }

      // 找出新增的字段
      const oldFields = oldList.map(item => item.model)
      const newFields = newList.filter(item => !oldFields.includes(item.model))

      if (newFields.length === 0) {
        return
      }

      let hasChanges = false
      const syncLog = []

      // 为所有现有行添加新字段的默认值
      this.dynamicValidateForm.domains.forEach((row, index) => {
        const addedFields = []
        newFields.forEach(fieldConfig => {
          if (!(fieldConfig.model in row)) {
            if (fieldConfig.type === 'batch') {
              row[fieldConfig.model] = fieldConfig.options.defaultValue || []
            } else {
              row[fieldConfig.model] = fieldConfig.options.defaultValue
            }
            hasChanges = true
            addedFields.push({
              field: fieldConfig.model,
              defaultValue: fieldConfig.options.defaultValue,
              type: fieldConfig.type
            })
          }
        })

        if (addedFields.length > 0) {
          syncLog.push({
            rowIndex: index,
            addedFields: addedFields
          })
        }
      })

      if (hasChanges) {
        this.handleInput()
      }
    },
    // 确保新行获得数据源
    ensureDataSourcesForNewRow() {
      // 重新初始化所有字段的数据源，不管是否已存在
      this.record.list.forEach((item) => {
        // 如果是选择类型的字段，确保有数据源
        if (['select', 'radio', 'checkbox'].includes(item.type)) {
          if (!this.dataSources[item.key] || this.dataSources[item.key].length === 0) {
            this.initOptions(item)
          }
        }
      })
      // 强制更新所有子组件，确保数据源正确传递
      this.$forceUpdate()
    },
    handleInput() {
      this.$emit('change', this.dynamicValidateForm.domains)
    },
  },
}
</script>
<style lang="less" scoped>
/deep/.ant-col {
  width: 100%;
}
.dynamic-opr-button:last {
  margin-left: 0px;
}
.dynamic-opr-button {
  cursor: pointer;
  position: relative;
  top: 4px;
  font-size: 16px;
  color: #999;
  transition: all 0.3s;
  margin-left: 6px;
}

// 嵌套batch的样式优化
.batch-table {
  width: 100%;
  overflow-x: auto;
  
  // 确保表格容器能够正确显示宽度
  /deep/ .ant-table-wrapper {
    width: 100%;
    overflow-x: auto;
  }
  
  /deep/ .ant-table {
    width: 100%;
    min-width: 100%;
  }
  
  /deep/ .ant-table-tbody > tr > td {
    padding: 8px;
    vertical-align: top;
  }

  // 嵌套表格的样式
  /deep/ .batch-table {
    margin: 0;
    border: 1px solid #e8e8e8;
    border-radius: 4px;

    .ant-table-tbody > tr > td {
      padding: 4px;
    }

    .ant-btn {
      font-size: 12px;
      height: 24px;
      padding: 0 8px;
    }
  }
}
.dynamic-opr-button:hover {
  color: #e89;
}
.dynamic-opr-button[disabled] {
  cursor: not-allowed;
  opacity: 0.5;
}

/* 评估指标列头样式 */
/deep/ .evaluation-column-header {
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.1) 0%, rgba(24, 144, 255, 0.05) 100%) !important;
  color: #1890ff !important;
  font-weight: 600 !important;
  position: relative;
}

/deep/ .evaluation-column-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #1890ff 0%, #40a9ff 100%);
}
</style>
