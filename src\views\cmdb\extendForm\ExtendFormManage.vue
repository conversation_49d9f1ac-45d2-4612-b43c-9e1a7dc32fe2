<template>
  <div style="height: 100%">
    <component :is="pageName" :data="data" />
  </div>
</template>
<script>
import ExtendFormList from './ExtendFormList'
import ExtendFormFormDetails from './modules/ExtendFormFormDetails'
export default {
  name: 'ExtendFormManage',
  data() {
    return {
      isActive: 0,
      data: {},
    }
  },
  components: {
    ExtendFormList,
    ExtendFormFormDetails,
  },
  created() {
    this.pButton1(0)
  },
  //使用计算属性
  computed: {
    pageName() {
      switch (this.isActive) {
        case 0:
          return 'ExtendFormList'
          break

        default:
          return 'ExtendFormFormDetails'
          break
      }
    },
  },
  methods: {
    pButton1(index) {
      this.isActive = index
    },
    pButton2(index, item) {
      this.isActive = index
      this.data = item
    },
  },
}
</script>