<template>
  <div class="cla-card">
    <!--    <div  style='border:1px solid #FFFFFF;background-color: red ; margin-top:20px'>
      <a-button @click="refresh" type="default" icon="reload" :loading="loading">刷新</a-button>
    </div>-->
    <div class="hScroll vScroll" style="flex: auto; margin: 20px 14px 14px 14px">
      <!-- <a-input-search @change="onChange" v-model="inputSearch" style="width:100%;margin-top: 10px" placeholder="请输入类型名称" /> -->
      <template>
        <a-dropdown :trigger="[this.dropTrigger]" @visibleChange="dropStatus">
          <span style="user-select: none">
            <vue-easy-tree
              ref="veTree"
              node-key="id"
              height="calc(100vh - 325px)"
              :data="assetsCategoryTree"
              :props="replaceFields"
            >
             <div class="custom-tree-node" slot-scope="{ node, data }">
                 <span  @contextmenu="rightHandle(node,data)">{{ node.label }}</span>
             </div>
            </vue-easy-tree>
          </span>
          <!--新增右键点击事件,和增加添加和删除功能-->
          <a-menu slot="overlay">
            <a-menu-item v-if="nodeType === 'city'" @click="handleAddType" key="1">增加下级节点</a-menu-item>
            <a-menu-item v-if="nodeType === 'room' || nodeType === 'city'" @click="handleEditType"
              >编辑节点</a-menu-item
            >
            <a-menu-item
              v-if="nodeName !== '数据中心' && (nodeType === 'room' || nodeType === 'city')"
              @click="deleteType"
              key="2"
              >删除节点</a-menu-item
            >
            <a-menu-item v-if="nodeType === 'city' || nodeType === 'room'" @click="closeDrop" key="3">取消</a-menu-item>
          </a-menu>
        </a-dropdown>
      </template>
    </div>
    <tree-node-modal ref="modalTypeForm" @close="refresh(arguments)"></tree-node-modal>
  </div>
</template>
<script>
import { queryPostionTreeList } from '@/api/device'
import TreeNodeModal from './modules/TreeNodeModal'
import { deleteAction, getAction } from '@/api/manage'
import VueEasyTree from '@wchbrad/vue-easy-tree'
// 样式文件，可以根据需要自定义样式或主题
import '@wchbrad/vue-easy-tree/src/assets/index.scss'
//通过Key获取对应地title
const getTitleByKey = (key, tree) => {
  let selectTitle
  for (let i = 0; i < tree.length; i++) {
    const node = tree[i]
    if (node.key === key) {
      selectTitle = node.title
      break
    }
    if (node.children) {
      selectTitle = getTitleByKey(key, node.children)
    }
  }
  return selectTitle
}
//生成tree节点的数组[{ key, title: node.title }]
// const dataList = []
// var firstRoom = ''

//为tree生成对应地title slot
const generateSlotScopes = (data) => {
  for (let i = 0; i < data.length; i++) {
    // var info = data[i];
    data[i].scopedSlots = { title: 'title' }
    if (data[i].children) {
      generateSlotScopes(data[i].children)
    }
  }
}
export default {
  name: 'PostionTree',
  components: {
    'tree-node-modal': TreeNodeModal,
    VueEasyTree,
  },
  data() {
    return {
      firstTitle: '', //存储搜素tree的第一个title
      inputSearch: '',
      // 树
      assetsCategoryTree: [],
      treeData: [],
      expandedKeys: [],
      searchValue: '',
      autoExpandParent: true,
      dropTrigger: '',
      selectedKeys: [],
      selectedTitle: '',
      checkedKeys: [],
      checkStrictly: true,
      replaceFields: {
        key: 'id',
        label: 'name',
        type: 'type',
      },
      nodeType: '',
      nodeName: '',
      url: {
        delete: '/topo/room/delete',
        asyncUrl: '/topo/room/cabinet',
      },
      dataList: [],
      firstRoom: '',
      roomFlag: false,
      selectedNodeId: '',
      selectedNodeType: '',
    }
  },
  created() {
    this.loadTree()
  },
  methods: {
    asyncLoadTreeData(treeNode) {
      return new Promise((resolve) => {
        if (treeNode.$vnode.children) {
          resolve()
          return
        }
        let key = treeNode.$vnode.key
        this.getType(key, 'room', this.assetsCategoryTree)
        if (!this.roomFlag) {
          resolve()
          return
        }
        let param = {
          roomId: key,
        }
        getAction(this.url.asyncUrl, param).then((res) => {
          if (res.success) {
            this.generateIsLeafValue(res.result)
            this.addChildren(key, res.result, this.assetsCategoryTree)
            this.assetsCategoryTree = [...this.assetsCategoryTree]
          }
          resolve()
        })
      })
    },
    //机房编辑完之后的数据更新
    reloadTree() {
      // return new Promise(resolve => {
      this.getType(this.selectedNodeId, 'room', this.assetsCategoryTree)
      if (!this.roomFlag) {
        // resolve()
        return
      }
      let param = {
        roomId: this.selectedNodeId,
      }
      getAction(this.url.asyncUrl, param).then((res) => {
        if (res.success) {
          this.generateIsLeafValue(res.result)
          this.addChildren(this.selectedNodeId, res.result, this.assetsCategoryTree)
          this.assetsCategoryTree = [...this.assetsCategoryTree]
        }
        this.$emit('selected', this.selectedNodeType, this.selectedNodeId)
      })
      // })
    },
    //根据节点id查询后台更新tree
    reloadTreeByNodeid(key) {
      // return new Promise(resolve => {
      this.getType(key, 'room', this.assetsCategoryTree)
      if (!this.roomFlag) {
        // resolve()
        return
      }
      let param = {
        roomId: key,
      }
      getAction(this.url.asyncUrl, param).then((res) => {
        if (res.success) {
          this.generateIsLeafValue(res.result)
          this.addChildren(key, res.result, this.assetsCategoryTree)
          this.assetsCategoryTree = [...this.assetsCategoryTree]
        }
      })
      // })
    },
    //懒加载获取的数据添加到相应节点的children属性上
    addChildren(pid, children, treeArray) {
      if (treeArray && treeArray.length > 0) {
        for (let item of treeArray) {
          if (item.id == pid) {
            if (!children || children.length == 0) {
              item.isLeaf = true
              item.children = children
            } else {
              item.isLeaf = false
              item.children = children
            }
            break
          } else {
            this.addChildren(pid, children, item.children)
          }
        }
      }
    },
    //判断节点是否存在子节点来判断是否为Leaf节点
    generateIsLeafValue(treeArray) {
      if (treeArray && treeArray.length > 0) {
        for (let item of treeArray) {
          if (item.children && item.children.length > 0) {
            item.isLeaf = false
            this.generateIsLeafValue(item.children)
          } else {
            item.isLeaf = true
          }
        }
      }
    },
    //判断节点类型
    getType(nodeId, type, treeArray) {
      if (treeArray && treeArray.length > 0) {
        for (let item of treeArray) {
          if (item.id == nodeId) {
            if (item.type === type) {
              this.roomFlag = true
            } else {
              this.roomFlag = false
            }
            break
          } else {
            this.getType(nodeId, type, item.children)
          }
        }
      }
    },
    // 查询树
    async loadTree() {
      var that = this
      that.treeData = []
      that.assetsCategoryTree = []
      await queryPostionTreeList().then((res) => {
        if (res.success) {
          // 部门全选后，再添加部门，选中数量增多
          this.allTreeKeys = []
          if (res.result.length > 0) {
            generateSlotScopes(res.result)
            that.assetsCategoryTree = [...res.result]
          }
          this.loading = false
          this.generateList(that.assetsCategoryTree)
        }
      })
    },
    generateList(data) {
      for (let i = 0; i < data.length; i++) {
        const node = data[i]
        const key = node.id
        if (this.firstRoom === '' && node.type === 'room') {
          this.firstRoom = node.id
          this.selectedKeys = [node.id]
          this.getParentKey(node.id, this.assetsCategoryTree)
          this.selectedNodeType = node.type
          this.selectedNodeId = node.id
          this.$emit('selected', node.type, node.id)
        }
        this.dataList.push({ key, title: node.name })
        if (node.children) {
          this.generateList(node.children)
        }
      }
    },
    //子节点匹配，获取父节点，用于展开tree
    getParentKey(key, tree) {
      let parentKey
      for (let i = 0; i < tree.length; i++) {
        const node = tree[i]
        if (node.children) {
          if (node.children.some((item) => item.id === key)) {
            this.expandedKeys.push(node.id)
          } else if (this.getParentKey(key, node.children)) {
            parentKey = this.getParentKey(key, node.children)
            this.expandedKeys.push(parentKey)
          }
        }
      }
      return parentKey
    },
    //获取机柜的父级机房,因为机房的下级数据都是通过机房id懒加载获取到的，并查询后台更新数据
    // getCabinetParentKey(key, tree) {
    //   for (let i = 0; i < tree.length; i++) {
    //     const node = tree[i]
    //     if (node.children) {
    //       if (node.children.some(item => item.id === key)) {
    //         this.reloadTreeByNodeid(node.id)
    //         break
    //       } else if (this.getCabinetParentKey(key, node.children)) {
    //         parentKey = this.getCabinetParentKey(key, node.children)
    //       }
    //     }
    //   }
    // },
    //机柜编辑完之后的获取机房id，查询后台数据更新
    // reloadTreeAfterCabinet() {
    //   this.getCabinetParentKey(this.selectedNodeId, this.assetsCategoryTree)
    // },
    //暂时废弃
    onSearch(value) {
      let that = this
      if (value) {
        searchByConfigItemtype({ typeName: value }).then((res) => {
          if (res.success) {
            that.assetsCategoryTree = []
            for (let i = 0; i < res.result.length; i++) {
              let temp = res.result[i]
              that.assetsCategoryTree.push(temp)
            }
          } else {
            that.$message.warning(res.message)
          }
        })
      } else {
        that.loadTree()
      }
    },
    // 右键点击下拉框改变事件
    dropStatus(visible) {
      if (visible == false) {
        this.dropTrigger = ''
      }
    },
    // 选择树的方法
    onSelect(selectedKeys, e) {
      this.selectedKeys = selectedKeys
      this.firstTitle = ''
      if (e.selectedNodes.length < 1) {
        return
      }
      const nodeData = e.selectedNodes[0].data.props.dataRef
      this.selectedNodeType = nodeData.type
      this.selectedNodeId = nodeData.id
      //向父组件弹射抛值
      this.$emit('selected', this.selectedNodeType, this.selectedNodeId)
    },
    //tree的查询框输入时，默认选中匹配的第一个（用firstTitle表示）
    onChange(e) {
      const value = e.target.value
      this.searchValue = value
      //查询框第一个匹配的node对应的key
      let firstSearchedKey = ''
      const expandedKeys = dataList
        .map((item) => {
          if (item.title.indexOf(value) > -1) {
            //查询框第一个匹配的node对应的key
            if (firstSearchedKey == '') {
              firstSearchedKey = item.key
            }
            return getParentKey(item.key, this.assetsCategoryTree)
          }
          return null
        })
        .filter((item, i, self) => item && self.indexOf(item) === i)
      Object.assign(this, {
        expandedKeys,
        searchValue: value,
        autoExpandParent: true,
      })
      if (this.expandedKeys.length > 0 && value.trim().length > 0) {
        this.firstTitle = getTitleByKey(firstSearchedKey, this.assetsCategoryTree)
        this.selectedKeys = [firstSearchedKey]
        //向父组件弹射抛值
        this.$emit('selected', firstSearchedKey)
      }
      if (value.trim().length == 0) {
        //查询设备信息,此情况下，没有分类被选中
        this.firstTitle = ''
        this.selectedKeys = []
        //向父组件弹射抛值
        this.$emit('selected')
      }
    },
    onCheck(checkedKeys, e) {
      this.hiding = false
      // this.checkedKeys = checkedKeys.checked
      // <!---- author:os_chengtgen -- date:20190827 --  for:切换父子勾选模式 =======------>
      if (this.checkStrictly) {
        this.checkedKeys = checkedKeys.checked
      } else {
        this.checkedKeys = checkedKeys
      }
      // <!---- author:os_chengtgen -- date:20190827 --  for:切换父子勾选模式 =======------>
    },
    // 右键操作方法
    rightHandle(node) {
      this.dropTrigger = 'contextmenu'
    //   this.nodeType = node.dataRef.type
    //   this.nodeName = node.dataRef.name
    //   this.rightClickSelectedKey = node.eventKey
    //   this.rightClickSelectedBean = node.dataRef
    },
    //树的添加类型
    handleAddType() {
      this.$refs.modalTypeForm.add(this.rightClickSelectedKey, this.assetsCategoryTree)
      this.$refs.modalTypeForm.title = '新增'
    },
    //树的编辑类型
    handleEditType() {
      this.$refs.modalTypeForm.edit(this.rightClickSelectedBean, this.assetsCategoryTree)
      this.$refs.modalTypeForm.title = '编辑'
    },
    //树room节点的删除类型
    async deleteType() {
      await deleteAction(this.url.delete, { id: this.rightClickSelectedKey }).then((res) => {
        if (res.success) {
          this.$message.success(res.message)
          this.afterDelChangeData(this.rightClickSelectedKey, this.assetsCategoryTree)
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    //节点删除成功后，前端数据代码删除，不从后台同步
    afterDelChangeData(key, tree) {
      for (let i = 0; i < tree.length; i++) {
        const node = tree[i]
        if (node.id === key) {
          tree.splice(i, 1)
          break
        }
        if (node.children) {
          this.afterDelChangeData(key, node.children)
        }
      }
    },
    // 右键店家下拉关闭下拉框
    closeDrop() {
      this.dropTrigger = ''
    },
    refresh(args) {
      //节点新增
      if (args[0] && args[0] === 'post') {
        this.afterAddChangeData(this.rightClickSelectedKey, this.assetsCategoryTree, {
          id: args[1],
          name: args[2],
          type: args[3],
          children: [],
        })
      }
      //节点修改
      if (args[0] && args[0] === 'put') {
        this.afterEditChangeData(this.rightClickSelectedKey, this.assetsCategoryTree, {
          id: args[1],
          name: args[2],
          type: args[3],
        })
      }
      this.loading = true
      // this.loadTree()
    },
    //节点添加成功后，前端数据代码添加，不从后台同步
    afterAddChangeData(key, tree, nodeInfo) {
      for (let i = 0; i < tree.length; i++) {
        const node = tree[i]
        if (node.id === key) {
          node.children.push(nodeInfo)
          break
        }
        if (node.children) {
          this.afterAddChangeData(key, node.children, nodeInfo)
        }
      }
    },
    //节点添加成功后，前端数据代码添加，不从后台同步
    afterEditChangeData(key, tree, nodeInfo) {
      for (let i = 0; i < tree.length; i++) {
        const node = tree[i]
        if (node.id === key) {
          node.name = nodeInfo.name
          break
        }
        if (node.children) {
          this.afterEditChangeData(key, node.children, nodeInfo)
        }
      }
    },
    onExpand(expandedKeys) {
      // if not set autoExpandParent to false, if children expanded, parent can not collapse.
      // or, you can remove all expanded children keys.
      this.expandedKeys = expandedKeys
      this.autoExpandParent = false
    },
  },
}
</script>
<style lang="less" scoped>
@import '~@assets/less/scroll.less';
.cla-card {
  height: 100% !important;
  background-color: #ffffff;
  border-radius: 3px;
  display: flex;
  flex-direction: column;
}
/*::v-deep .ant-card-body {
  padding: 10px;
}*/
::v-deep span.ant-tree-title {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
  //width: 1.25rem /* 100/80 */;
}
</style>