import * as THREE from 'three'
import { Detector } from './Detector'
import { getFileAccessHttpUrl } from '@api/manage'
import { OrbitControls} from 'three/examples/jsm/controls/OrbitControls'
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
import { RoomEnvironment } from 'three/examples/jsm/environments/RoomEnvironment.js';
export default {
  data(){
    return{
      controls:null,
      light:null,
    }
  },
  methods:{
    initScene() {
      scene = new THREE.Scene()
    },
    importScene() {
      this.initModels(this.modelsArr)
    },
    initCamera() {
      camera = new THREE.PerspectiveCamera(45, this.boxW / this.boxH, 1, 5000) //45
      camera.position.set(0, 280, 1600)
      camera.lookAt(new THREE.Vector3(0, 0, 0))
    },
    initRenderer() {
      if (Detector.webgl) {
        this.renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true })
      } else {
        this.renderer = new THREE.CanvasRenderer()
      }
      this.renderer.setSize(this.boxW, this.boxH)
      this.container.appendChild(this.renderer.domElement)
      //设置背景
      let config = this.modelsArr.find((el) => el.modelCode === 'room_bg_config')
      if (config && Object.prototype.toString.call(config.userData) === "[object Object]") {
        let configData = config.userData
        if (configData.bgType === 'color') {
          scene.background = new THREE.Color(configData.bgColor)
        } else if (configData.bgType === 'pic' && configData.bgPic) {
          let url = getFileAccessHttpUrl(configData.bgPic)
          scene.background = new THREE.TextureLoader().load(url)
        }
      } else if(this.isEditor) {
        scene.background = new THREE.Color("#4682b4")
        this.$store.commit('threejs/SET_BG_CONGIG', { bgType: 'color', bgColor: '#4682b4', bgPic: '' })
      }
      // var helper = new THREE.GridHelper(window.innerWidth, 20, 0x444444, 0x888888)
      // helper.name = 'gridHelper'
      // scene.add(helper)
    },
    initLight() {
      var directionalLight = new THREE.DirectionalLight(0xffffff, 0.3) //模拟远处类似太阳的光源
      directionalLight.color.setHSL(0.1, 1, 0.95)
      directionalLight.position.set(0, 200, 0).normalize()
      scene.add(directionalLight)
      var ambient = new THREE.AmbientLight(0xffffff, 1) //AmbientLight,影响整个场景的光源
      ambient.position.set(0, 0, 0)
      scene.add(ambient)
    },
    initControls() {
      this.controls = new OrbitControls(camera, this.renderer.domElement)
      this.controls.enableDamping = true
      this.controls.dampingFactor = 0.5
      // 视角最小距离
      this.controls.minDistance = 1
      // 视角最远距离
      this.controls.maxDistance = 10000
      // 最大角度
      // this.controls.maxPolarAngle = Math.PI / 2.2
      // this.controls.enablePan = false
      this.controls.listenToKeyEvents(window)
    },
    animate() {
      if(scene && camera){
        this.renderer.render(scene, camera)
      }
      this.update()
      this.animationFrameId = requestAnimationFrame(this.animate)
    },
    update() {
      this.controls.update()
    },
    destroyThreejs() {
      try {
        if(this.animationFrameId){
          cancelAnimationFrame(this.animationFrameId);
        }
        if(this.renderer){
          this.renderer.dispose();
        }
        // camera = null;
        // camera = undefined;
      } catch (e) {
        console.error('Failed to destroy threejs', e);
      }
    },
    onWindowResize() {
      if (this.$refs.threeBox) {
        let rect = this.$refs.threeBox.getBoundingClientRect()
        this.boxH = rect.height
        this.boxW = rect.width
      }
      camera.aspect = this.boxW / this.boxH
      camera.updateProjectionMatrix()
      this.renderer.setSize(this.boxW, this.boxH)
    },
  }
}