<template>
  <div class='row-container'>
    <div class='step-top-card' :class='dataSource.length>0?"step-top":"step-top-empty-config"'>
      <div class='step-top-headline'>
        <span class='title'>
          任务名称：<span>{{ data.name }}</span>
          业务名称：<span>{{ data.bizInfoName }}</span>
        </span>
        <span class='step-btns'>
          <a @click='handleAddStep'><a-icon type='plus' />添加步骤</a>
          <a @click='handleClear'><a-icon type='delete' />清空</a>
          <a @click='handleSave'><a-icon type='save' />保存</a>
          <a @click='getGo'><a-icon type='rollback' />返回</a>
      </span>
      </div>
      <div class='step-top-pos-container' v-if='dataSource.length>0'>
        <a-steps size='small' :current='stepIndex' @change='locateStep'>
          <a-step status='wait' :title="'步骤'+(index+ 1)"
                  v-for='(item,index) in dataSource' :id='"locate_step_"+index' :key='"locate_step_"+index'
                  :description='"名称："+(item.name?item.name:"")'>
          </a-step>
        </a-steps>
      </div>
    </div>

    <div :class='dataSource.length>0?"step-config":"step-empty-config"'>
      <a-spin :spinning='loading'>
        <div class='step-content' v-if='dataSource.length>0'>
          <a-row class='step-items-wrapper'>
            <a-col :span='14' style='height: 100%'>
              <div class='step-items-container'>
                <div class='tag'>标签</div>
                <a-row :gutter='[0, 0]' class='step-items'>
<!--                  <draggable
                    v-model='dataSource'
                    :options='dragOptions'
                    :forceFallback='false'
                    dragClass='dragClass'
                    @dragover.native.stop.prevent='stepDragover'
                    @drag.native.stop.prevent='stepDrag'
                    @dragend.native.stop.prevent='stepDragend'
                  >-->
                  <draggable
                    v-model='dataSource'
                    :options='dragOptions'
                    :forceFallback='false'
                    dragClass='dragClass'
                    @end='stepDragend'
                  >
                    <a-col
                      style='padding: 8px 12px 8px 2px'
                      v-for='(item, index) in dataSource'
                      :key='"step_item_"+index'
                      :id='"step_item_"+index'
                      @click.native.stop.prevent='changeStep(index)'
                    >
                      <div class='step-card' :class='[stepIndex===index?"step-card-active":""]'>
                        <div v-if='!item.validation' class='err-tip'>校验未通过</div>
                        <div class='title'>
                          <div class='title-content'>步骤{{ index + 1 }}</div>
                          <!--<div class='title-content'>步骤{{ index + 1 }}：<span>{{ item.name }}</span></div>-->
                          <div class='title-btn'>
                          <span class='actions'>
                            <a-tooltip title='复制'>
                              <div class='actions-click' @click.stop='copyStep(index)'><a-icon type='copy'
                                                                                               class='icon' /></div>
                            </a-tooltip>
                            <a-tooltip title='删除'>
                              <div class='actions-click' @click.stop='deleteStep(index)'><a-icon type='delete'
                                                                                                 class='icon' /></div>
                            </a-tooltip>
                            <!--                            <a-tooltip title='查看'>
                                                          <a-icon class='icon icon-eye' type='eye'/>
                                                        </a-tooltip>-->
                            <!--                            <a-tooltip title='校验' v-if='stepIndex===index'>
                                                           <div class='actions-click' @click.stop='validateStep(index)'><a-icon type='safety-certificate' class='icon'/></div>
                                                        </a-tooltip>-->
                          </span>
                          </div>
                        </div>
                        <div class='content'>
                          <div class='info'>
                            <a-tooltip title='名称'>
                              <a-icon class='icon' type='profile' />
                            </a-tooltip>
                            <div class='description'>{{ item.name }}</div>
                          </div>
                          <!--                        <div class='info'>
                                                    <a-icon  class='icon' type='exception' />
                                                    <div class='description'>{{ item.name }}</div>
                                                  </div>-->
                          <!--                    <a-divider type='vertical'/>-->
                          <div class='info'>
                            <a-tooltip title='交互方式'>
                              <a-icon class='icon' type='select' />
                            </a-tooltip>
                            <div class='description'>{{ item.interactionTxt }}</div>
                          </div>
                          <!--                      <a-divider type='vertical'/>-->
                          <!--                        <div class='info'>
                                                    <a-tooltip title='描述'>
                                                      <a-icon  class='icon' type='profile' />
                                                    </a-tooltip>
                                                    <div class='description'>{{ item.message }}</div>
                                                  </div>-->
                        </div>
                      </div>
                    </a-col>
                  </draggable>
                </a-row>
              </div>
            </a-col>
            <a-col :span='10' style='height: 100%'>
              <div class='step-attribute'>
                <div class='tag'>属性</div>
                <step-attribute
                  ref='stepAttribute'
                  class='attribute-content'
                  :step-obj='dataSource[stepIndex]'
                  :interaction-info-list='interactionInfoList'
                  :locator-list='locatorList'
                  :info-type-list='infoTypeList'
                  :rule-conditions-list='ruleConditionsList'
                  @ok='updateStepInfo'>
                </step-attribute>
              </div>
            </a-col>
          </a-row>
        </div>
        <div class='empty-content' v-else>
          <a-list :data-source='[]'></a-list>
        </div>
      </a-spin>
    </div>
  </div>
</template>

<script>
import draggable from 'vuedraggable'
import stepAttribute from './modules/stepAttribute.vue'
import { getAction, httpAction } from '@api/manage'
import { ajaxGetDictItems, getDictItemsFromCache } from '@api/api'

export default {
  name: 'configurationStep',
  props: {
    data: {
      type: Object
    }
  },
  components: {
    draggable,
    stepAttribute
  },
  data() {
    return {
      interactionInfoList: [],
      locatorList: [],
      infoTypeList: [],
      ruleConditionsList: [],
      loading: false,
      dataSource: [],
      stepIndex: -1,
      dragOptions: {
        animation: 150
      },

      url: {
        list: '/business/availabilityResult/queryById',
        save: '/business/availability/saveStepConfig',
        interAttribute: '/business/availability/componentConfigList'
      }
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    /*初始化*/
    init() {
      let that = this
      that.loading = true
      Promise.all([
        that.getInteractionInfo(),
        that.initDictData('biz_locator_type'),
        that.initDictData('biz_getInfo_type'),
        that.initDictData('rule_conditions')
      ]).then((result) => {
        let proResult = result.every((item, index) => {
          if (index == 0) {
            that.interactionInfoList = item.data
          } else if (index == 1) {
            that.locatorList = item.data
          } else if (index == 2) {
            that.infoTypeList = item.data
          } else if (index == 3) {
            that.ruleConditionsList = item.data
          }
          return item.success == true
        })
        if (proResult) {
          that.loadStepConfig(that.data)
        } else {
          that.loading = false
          that.$message.warning('数据加载异常')
        }
      }).catch((err) => {
        that.loading = false
        that.$message.warning(err.message)
      })
    },
    /*获取定位器、获取信息类型、判断符号字典数据*/
    initDictData(dictCode) {
      return new Promise((resove, reject) => {
        if (dictCode != null && dictCode != '') {
          //优先从缓存中读取字典配置
          if (getDictItemsFromCache(dictCode)) {
            resove({
              success: true,
              data: getDictItemsFromCache(dictCode),
              message: '加载成功'
            })
            return
          }

          //根据字典Code, 初始化字典数组
          ajaxGetDictItems(dictCode, null).then((res) => {
            if (res.success) {
              resove({
                success: true,
                data: res.result,
                message: '加载成功'
              })
            } else {
              reject({
                success: false,
                data: [],
                message: '加载失败'
              })
            }
          }).catch((err) => {
            reject({
              success: false,
              data: [],
              message: err.message
            })
          })
        } else {
          reject({
            success: false,
            data: [],
            message: '加载失败'
          })
        }
      })
    },
    /*获取交互方式属性配置基础信息*/
    getInteractionInfo() {
      return new Promise((resolve, reject) => {
        getAction(this.url.interAttribute).then((res) => {
          if (res.success) {
            resolve({
              success: true,
              data: res.result,
              message: '操作成功'
            })
          } else {
            reject({
              success: false,
              data: [],
              message: '操作失败'
            })
          }
        }).catch((err) => {
          reject({
            success: false,
            data: [],
            message: '操作失败'
          })
        })
      })
    },
    /*获取步骤配置数据*/
    loadStepConfig(record) {
      this.dataSource = []
      this.stepIndex = -1

      let stepConfig = {}
      if (this.data.stepConfig) {
        stepConfig = this.data.stepConfig
      } else {
        let inter = this.interactionInfoList[4]//默认第一个为访问页面
        let arr = [{ name: inter.componentName, interaction: inter.componentCode, message: '' }]
        stepConfig = JSON.stringify(arr)
      }

      this.dataSource = JSON.parse(stepConfig)
      if (this.dataSource.length > 0) {
        for (let i = 0; i < this.dataSource.length; i++) {
          let item = this.dataSource[i]
          let inte = this.interactionInfoList.find(inter => inter.componentCode === item.interaction)
          item.interactionTxt = inte.componentName
          item.validation = true
        }
        this.stepIndex = 0
        this.$nextTick(() => {
          this.$refs.stepAttribute.switchAttribute(this.dataSource[this.stepIndex])
        })
      }
      this.loading = false
    },
    /*添加步骤*/
    async handleAddStep() {
      let that = this
      that.dataSource.splice(that.stepIndex + 1, 0, {})
      that.stepIndex += 1
      let locateStep = null
      let stepItem = null
      await that.$nextTick(() => {
        that.$refs.stepAttribute.switchAttribute(that.dataSource[that.stepIndex])
      })
      await new Promise(resolve => setTimeout(resolve, 500)) // 等待渲染完成
      await that.$nextTick(() => {
        locateStep = document.getElementById('locate_step_' + that.stepIndex)
        stepItem = document.getElementById('step_item_' + that.stepIndex)
      })
      await that.scrollToTarget(stepItem, 200)
      await new Promise(resolve => setTimeout(resolve, 800)) // 等待渲染完成
      await that.scrollToTarget(locateStep, 100)
    },

    async scrollToTarget(target, time) {
      target.scrollIntoView({ behavior: 'smooth' })
      await new Promise(resolve => setTimeout(resolve, time)) // 等待一小段时间
    },
    /*清空步骤*/
    handleClear() {
      this.dataSource.splice(0, this.dataSource.length)
      this.stepIndex = -1
    },
    /*保存步骤*/
    handleSave() {
      if (!this.loading) {
        if (this.dataSource.length === 0) {
          this.$message.warning('请配置步骤！')
          return
        }
        let newArray = []
        let step = this.dataSource.find((step, index) => step.validation === false)
        if (step) {
          this.$message.warning('步骤属性校验未通过！')
          this.$nextTick(() => {
            if (!this.dataSource[this.stepIndex].validation) {
              this.$refs.stepAttribute.switchAttribute(this.dataSource[this.stepIndex])
            }
          })
          return
        }
        newArray = this.dataSource.map(({ validation, interactionTxt, ...rest }) => rest)
        //第一个步骤不是访问页面给提示
        if(newArray[0].interaction!==this.interactionInfoList[4].componentCode){
          this.$message.warning(`步骤一交互方式选择不是${this.interactionInfoList[4].componentName}！`)
        }
        const stepConfig = JSON.stringify(newArray)
        const stepData = {
          id: this.data.id,
          stepConfig: stepConfig
        }
        this.loading = true
        httpAction(this.url.save, stepData, 'put').then((res) => {
          if (res.success) {
            this.$message.success(res.message)
            this.$emit('ok')
          } else {
            this.$message.warning(res.message)
          }
          this.loading = false
        }).catch((err) => {
          this.$message.warning(err.message)
          this.loading = false
        })
      } else {
        this.$message.info('正在保存数据')
      }
    },
    /*返回列表*/
    getGo() {
      this.$parent.pButton1(0)
    },
    /*快速定位步骤*/
    locateStep(stepIndex) {
      this.stepIndex = stepIndex
      let stepItem = document.getElementById('step_item_' + stepIndex)
      stepItem.scrollIntoView({ behavior: 'smooth' })// 平滑滚动到目标元素位置
      this.$nextTick(() => {
        this.$refs.stepAttribute.switchAttribute(this.dataSource[stepIndex])
      })
    },
    /*拖拽结束，激活选中步骤*/
    stepDragend(obj){
      this.changeStep(obj.newIndex)
    },
    /*点击切换当前激活步骤*/
    changeStep(stepIndex) {
      this.stepIndex = stepIndex
      let locateStep = document.getElementById('locate_step_' + stepIndex)
      locateStep.scrollIntoView({ behavior: 'smooth' })// 平滑滚动到目标元素位置
      this.$refs.stepAttribute.switchAttribute(this.dataSource[stepIndex])
    },
    /*赋值步骤*/
    copyStep(stepIndex) {
      let m = Object.assign({}, this.dataSource[stepIndex])
      this.dataSource.splice(stepIndex + 1, 0, m)
      this.stepIndex = stepIndex + 1
      this.$nextTick(() => {
        this.$refs.stepAttribute.switchAttribute(this.dataSource[this.stepIndex])
      })
    },
    /*删除步骤*/
    deleteStep(stepIndex) {
      this.dataSource.splice(stepIndex, 1)
      let len = this.dataSource.length
      if (this.stepIndex === stepIndex) {
        this.stepIndex = Math.min(stepIndex, len - 1)
        if (this.stepIndex >= 0) {
          this.$nextTick(() => {
            this.$refs.stepAttribute.switchAttribute(this.dataSource[this.stepIndex])
          })
        }
      } else if (stepIndex < this.stepIndex) {
        this.stepIndex -= 1
      }
    },
    /*同步步骤的表单属性数据*/
    updateStepInfo(attribute) {
      // console.log('更新步骤数据attribute===',attribute)
      const stepObj = Object.assign({}, this.dataSource[this.stepIndex], attribute)
      this.dataSource.splice(this.stepIndex, 1, stepObj)
    }
    /*用于测试步骤属性的校验*/
    /* validateStep(step){
       console.log('手动触发校验step')
       this.$refs.stepAttribute.validateFun()
     }*/
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
.row-container {
  height: 100%;
}

::v-deep .ant-steps {
  overflow: hidden;
  overflow-x: auto;

  .ant-steps-item {
    margin: 8px 8px;
    min-width: 170px !important;

    .ant-steps-item-description {
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      white-space: nowrap !important;
    }
  }

  .ant-steps-item-active {
    .ant-steps-item-icon {
      border-color: #40a9ff !important;

      .ant-steps-icon {
        color: #40a9ff !important;
      }
    }

    .ant-steps-item-title {
      color: #40a9ff !important;
    }

    .ant-steps-item-description {
      color: #40a9ff !important;
    }
  }
}

::v-deep .ant-steps .ant-steps-item:not(.ant-steps-item-active) > .ant-steps-item-container[role='button']:hover .ant-steps-item-title,
::v-deep .ant-steps .ant-steps-item:not(.ant-steps-item-active) > .ant-steps-item-container[role='button']:hover .ant-steps-item-subtitle,
::v-deep .ant-steps .ant-steps-item:not(.ant-steps-item-active) > .ant-steps-item-container[role='button']:hover .ant-steps-item-description {
  color: #40a9ff !important;
}

::v-deep .ant-steps .ant-steps-item:not(.ant-steps-item-active):not(.ant-steps-item-process) > .ant-steps-item-container[role='button']:hover .ant-steps-item-icon {
  border-color: #40a9ff !important;
}

::v-deep .ant-steps .ant-steps-item:not(.ant-steps-item-active):not(.ant-steps-item-process) > .ant-steps-item-container[role='button']:hover .ant-steps-item-icon .ant-steps-icon {
  color: #40a9ff !important;
}

::v-deep .ant-spin-nested-loading {
  height: 100%;

  .ant-spin-container {
    height: 100%;
  }
}
.step-top-card{
  background-color: #fff;
  border-radius: 3px;
  padding: 24px;
  box-sizing: content-box;
  margin-bottom: 16px;
}

.step-top {
  height: 118px;
}

.step-top-empty-config{
  height: 40px;
}
.step-top-headline {
  height: 40px;
  line-height: 40px;
  border: 1px solid #409EFF;
  //border-bottom: 2px solid #e4e7ed;
  border-radius: 3px;
  padding: 0 10px;
  background: rgba(230, 244, 255, 0.65);
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
  align-items: center;

  .title {
    display: inline-block;
    width: calc(100% - 300px);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    span {
      color: #40a9ff;
      font-size: 14px;
      margin-right: 14px;
      font-weight: 500
    }

    /* span:nth-child(1) {
      margin-right: 10px
    }*/
  }

  .step-btns {
    text-align: right;
    display: inline-block;
    width: 300px;

    a {
      color: #409eff;
      margin: 0 7px;

      i {
        margin-right: 3px;
      }
    }
  }
}

.step-top-pos-container {
  height: 77px;
  line-height: 77px;
  overflow: hidden;
  /*overflow-x:auto;*/
  max-width: 100%;
  display: inline-flex;
  justify-content: start;
  align-items: center;
  flex-flow: row nowrap;
  margin-bottom: 16px;
}

.step-config {
  height: calc(100% - 166px - 16px);
  min-height: 655px;

  .step-content {
    height: 100%;

    .step-items-wrapper {
      margin-top: 5px;
      height: 100%;

      .step-items-container {
        border-radius: 3px;
        height: 100%;
        background: #fff;
        padding: 24px 0px 24px 24px;

        .step-items {
          height: calc(100% - 41px);
          overflow: hidden;
          overflow-y: auto;
          margin-right:12px;

          .step-card {
            position: relative;
            padding: 16px 24px;
            box-shadow:  2px 2px 7px 1px rgba(199,199,199,0.50);
            border-radius: 4px;
            background: #fff;

            .err-tip {
              position: absolute;
              text-align: center;
              padding: 3px;
              top: -8px;
              left: 6px;
              width: 90px;
              height: 22px;
              line-height: 15px;
              font-size: 12px;
              background: linear-gradient(110deg, transparent 10px, #ff0000 10px) 0px 0px, linear-gradient(-70deg, transparent 10px, #ff0000 10px) 100% 0px;
              background-repeat: no-repeat;
              background-size: 51% 100%;

              color: #fff;
            }

            .title {
              margin-bottom: 10px;
              font-size: 16px;
              color: rgba(0, 0, 0, 0.85);

              .title-content {
                width: calc(100% - 140px);
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;

                span {
                  font-size: 14px
                }
              }

              .title-btn {
                position: absolute;
                top: 5px;
                right: 5px;

                text-align: right;
                width: 100px;

                .actions {
                  z-index: 9999;
                  display: none;

                  .actions-click {
                    cursor: pointer;
                    display: inline-block;
                  }

                  .icon {
                    font-size: 18px;
                    margin-right: 5px;
                    color: #409eff;
                  }

                  .icon-eye {
                    cursor: default;
                  }
                }
              }
            }

            .content {
              display: flex;
              justify-content: space-between;
              align-items: center;
              flex-flow: row nowrap;
              color: rgba(0, 0, 0, 0.50) !important;

              /* .info:first-child {
                 width: 260px;
               }

               .info:last-child {
                 width: calc(100% - 260px);
               }*/

              .info:first-child {
                width: calc(100% - 260px);
              }

              .info:last-child {
                width: 260px;
              }

              .info {
                display: flex;
                flex-flow: row nowrap;
                justify-content: center;
                align-items: center;
                font-size: 14px;

                .icon {
                  cursor: default;
                }

                .description {
                  overflow: hidden;
                  width: calc(100% - 24px);
                  margin: 0px 4px;
                  display: inline-block;
                  height: 100%;
                  line-height: 100%;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                }
              }
            }
          }

          .step-card-active, .step-card:hover {
            border: 2px solid #40a9ff;
            box-shadow: 1px 2px 7px 2px rgba(119, 148, 195, 0.47);
            cursor: move;

            .title {
              color: #40a9ff;

              .a {
                cursor: pointer;
              }

              .actions {
                display: inline-block !important;
              }
            }

            .content {
              color: rgba(0, 0, 0, 0.65) !important;

              .icon {
                color: #40a9ff;
              }
            }
          }
        }
      }

      .step-attribute {
        background: #fff;
        padding: 24px;
        height: 100%;
        margin-left: 12px;
        border-radius: 3px;

        .attribute-content {
          //padding: 12px 16px 0px;
          height: calc(100% - 41px);
          overflow: hidden;
          overflow-y: auto
        }
      }
    }
  }

  .tag{
    margin-bottom: 20px;
    padding:0 7px;
    border-left: 4px solid #409eff;
    font-size: 14px;
  }
}

.step-empty-config{
  height: calc(100% - 89px - 16px);

  .empty-content {
    background-color: #fff;
    height: 100%;
    margin-top: 5px;
    //border: 1px solid #e4e7ed;
  }
}

.dragClass {
  /* display: flex;
   flex-flow: row nowrap;
   background: #ecf5ff !important;
   border: 1px solid #e8e8e8 !important;*/
}
</style>