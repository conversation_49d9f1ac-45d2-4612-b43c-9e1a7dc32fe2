<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container>
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-item label="使用人" :labelCol="labelCol" :wrapperCol="wrapperCol" required>
              <j-select-user-by-dep-no-right ref="JSelectUserByDepEnhance"
                v-decorator="['userIds', formValidator.userIds]" :multi="false" :userIds.sync="userIds"
                :ccToVos.sync="ccToVos"></j-select-user-by-dep-no-right>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="所属单位" :labelCol="labelCol" :wrapperCol="wrapperCol" required>
<!--              <a-tree-select :getPopupContainer="(node) => node.parentNode" show-search style="width: 100%"-->
<!--                             :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }" placeholder="请选择单位" allow-clear-->
<!--                             tree-node-filter-prop="title" :replaceFields="replaceFields" :treeData="selectOption"-->
<!--                             v-decorator="['deptId', formValidator.deptId]">-->
<!--              </a-tree-select>-->
              <a-select placeholder="请选择所属单位" v-decorator="['deptId', formValidator.deptId]" :allowClear="true"
                        :getPopupContainer="(node) => node.parentNode" :dropdownClassName='"custom-select-dropdown"'>
                <a-select-option v-for="item in selectOption" :key="item.id" :value="item.id">
                  {{ item.departName }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="联系电话" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['phone', formValidator.phone]" :allowClear="true" disabled autocomplete="off"
                placeholder="请输入联系电话">
              </a-input>
            </a-form-item>
          </a-col>
<!--          <a-col :span="24">-->
<!--            <a-form-item label="使用部门" :labelCol="labelCol" :wrapperCol="wrapperCol">-->
<!--              <a-input placeholder="请输入使用部门" v-decorator="['userDepartment', formValidator.userDepartment]"-->
<!--                :allow-clear="true" autocomplete="off" disabled/>-->
<!--            </a-form-item>-->
<!--          </a-col>-->
          <a-col :span="24">
            <a-form-item label="cpu类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-select v-decorator="['cpuType', formValidator.cpuType]" placeholder="请选择cpu类型" :allowClear="true"
                :getPopupContainer="(node) => node.parentNode" :dropdownClassName='"custom-select-dropdown"'>
                <a-select-option v-for="item in cpuTypeList" :key="item.value" :value="item.value">
                  {{ item.text || item.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="操作系统" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-select placeholder="请选择操作系统" v-decorator="['osType', formValidator.osType]" :allowClear="true"
                :getPopupContainer="(node) => node.parentNode" :dropdownClassName='"custom-select-dropdown"'>
                <a-select-option v-for="item in osTypeList" :key="item.value" :value="item.value">
                  {{ item.text }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item :label="'描\u3000述'" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-textarea v-decorator="['description', formValidator.description]"
                :autoSize="{ minRows: 2, maxRows: 4 }" :allow-clear="true" autocomplete="off" placeholder="请输入描述" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
  import {
    ajaxGetDictItems
  } from '@/api/api'
  import YqAreaCascaderSelect from '@comp/areaDict/YqAreaCascaderSelect.vue'
  import {
    phoneValidator
  } from '@/mixins/phoneValidator'
  import {
    putAction,
    postAction,
    getAction
  } from '@/api/manage'
  import pick from 'lodash.pick'
  import JFormContainer from '@/components/jeecg/JFormContainer'
  import JSelectUserByDepNoRight from '@/components/flowable/JSelectUserByDepNoRight'

  export default {
    name: 'TerminalBindForm',
    mixins: [phoneValidator],
    components: {
      JFormContainer,
      JSelectUserByDepNoRight
    },
    data() {
      return {
        form: this.$form.createForm(this),
        model: {},
        userIds: '',
        ccToVos: [],
        departList: [],
        flatDeparts: [],
        cpuTypeList: [],
        osTypeList: [],
        selectOption: [],
        replaceFields: {
          children: 'children',
          title: 'deptName',
          key: 'deptId',
          value: 'deptId',
        },
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          },
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          },
        },
        confirmLoading: false,
        terminal: {},
        formValidator: {
          deptId: {
            rules: [{
              required: true,
              message: '请选择单位！'
            }],
          },
          userIds: {
            rules: [{
              required: true,
              message: '请选择使用人！'
            }],
          },
          phone: {
            rules: [{
              required: true,
              message: '请输入联系电话！'
            }, {
              validator: this.phone
            }]
          },
          username: {
            rules: [{
              required: true,
              message: '请选择使用人！'
            }]
          },
          // userDepartment: {
          //   rules: [{
          //     required: true,
          //     message: '使用部门不能为空！'
          //   }]
          // },
          cpuType: {
            rules: [{
              required: true,
              message: '请选择cpu类型！'
            }]
          },
          osType: {
            rules: [{
              required: true,
              message: '请选择操作系统！'
            }]
          },
          description: {
            rules: [{
              required: false,
              max: 200,
              message: '描述长度应在 200 个字符之内！'
            }]
          },
        },
        url: {
          userBind: '/terminal/terminalDevice/updateTerminalInfo',
          queryById: '/software/devopsSoftwareInfo/queryById',
        },
      }
    },
    watch:{
      "ccToVos":{
        immediate:false,
        handler(val) {
          if (val && val.length) {
            this.form.setFieldsValue({
              phone: val[0].phone,
              deptId:undefined,
            })
            this.selectOption = val[0].allocateOrgList
            console.log("选中用户了",val)
            // if (val[0].orgCodeTxt) {
            //   var list =  val[0].orgCodeTxt.split(',')
            //   this.form.setFieldsValue({
            //     userDepartment: list[0]
            //   })
            // } else {
            //   this.form.setFieldsValue({
            //     userDepartment: ''
            //   })
            // }
          }
        }
      }
    },
    created() {
      // this.getDepartList()
      this.initDictData('cpuType', 'cpuTypeList')
      this.initDictData('os_type', 'osTypeList')
      // this.getSelect()
    },
    methods: {
      //获取单位列表
      getSelect() {
        getAction('/sys/sysDepart/queryAllTree').then((res) => {
          for (let i = 0; i < res.length; i++) {
            let temp = res[i]
            this.selectOption.push(temp)
          }
        })
      },
      edit(data) {
        this.terminal = data
        this.model = Object.assign({}, data)
        // this.model.deptId = data.deptId?data.deptId: undefined
        this.model.deptId = undefined
        this.model.cpuType = data.cpuType?data.cpuType: undefined
        this.model.osType = data.osType?data.osType: undefined
        this.model['userIds'] =   ''//data.username?data.username:
        this.model.description = data.description?data.description: ''
        this.model.phone = ""
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.model, 'deptId', 'description', 'osType', 'cpuType',
            'phone','userIds'
          ))
        })
      },
      getDepartList() {
        getAction('/sys/sysDepart/queryAllTree').then((res) => {
          this.departList = res && res.length > 0 ? res : []
          this.getFlatDeparts(this.departList)
        })
      },
      getFlatDeparts(list) {
        list.forEach((el) => {
          let tem = {
            id: el.deptId,
            name: el.deptName
          }
          this.flatDeparts.push(tem)
          if (el.children && el.children.length > 0) {
            this.getFlatDeparts(el.children)
          }
        })
      },
      initDictData(dictCode, list) {
        //根据字典Code, 初始化字典数组
        ajaxGetDictItems(dictCode, null).then((res) => {
          if (res.success) {
            this[list] = res.result
          }
        })
      },
      submitForm() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let formData = Object.assign(this.model, values)
            formData.bindUser = this.model.userIds
            formData.username = this.model.username
            formData.uniqueCode = this.terminal.uniqueCode
            formData.id = this.terminal.id
            putAction(this.url.userBind, formData).then((res) => {
              if (res.success) {
                this.$message.success(res.message)
                this.$emit('ok')
                that.confirmLoading = false
              } else {
                this.$message.warning(res.message)
                that.confirmLoading = false
              }
            })
          }
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  ::v-deep .two-words>div>label {
    letter-spacing: 4px;
  }

  ::v-deep .two-words>div>label::after {
    letter-spacing: 0px;
  }
</style>