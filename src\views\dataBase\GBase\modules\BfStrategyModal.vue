<template>
  <j-modal
    :title='title'
    :width='width'
    :visible='visible'
    :destroyOnClose='true'
    :centered='true'
    switchFullscreen
    @ok='handleOk'
    @cancel='handleCancel'
    cancelText='关闭'
    okText='保存'>
    <BfStrategyForm
      ref='realForm'
      @ok='submitCallback'
      @closeForm='close'
      :disabled='disableSubmit'
      :back-levels='backLevels'
      :back-contents='backContents'
      :back-formats='backFormats'
      :data-bases='dataBases'
    ></BfStrategyForm>
  </j-modal>
</template>

<script>
import BfStrategyForm from './BfStrategyForm'

export default {
  name: 'BfStrategyModal',
  props: {
    backLevels: {
      type: Array,
      default: () => {
        return []
      }
    },
    backContents: {
      type: Array,
      default: () => {
        return []
      }
    }, backFormats: {
      type: Array,
      default: () => {
        return []
      }
    },
    dataBases: {
      type:Array,
      default: () => {
        return []
      }
    },
  },
  components: {
    BfStrategyForm
  },
  data() {
    return {
      title: '',
      width: '70%',
      visible: false,
      disableSubmit: false
    }
  },
  methods: {
    add() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.add()
      })
    },
    edit(record) {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.edit(record)
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
      //this.$refs.realForm.selectInspectionType()
    },
    handleOk() {
      this.$refs.realForm.submitForm()
    },
    submitCallback() {
      this.$emit('ok')
      this.visible = false
    },
    handleCancel() {
      //this.close()
      this.$nextTick(() => {
        this.$refs.realForm.closeForm()
      })
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/normalModal.less';
</style>