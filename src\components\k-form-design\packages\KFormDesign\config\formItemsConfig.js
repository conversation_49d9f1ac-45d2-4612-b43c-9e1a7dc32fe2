/*
 * author kcz
 * date 2019-11-20
 * description 表单控件项
 */
export class AjaxData {
  url = "";
  type = "GET";
  params = `{}`;
  header = `{}`;
  callFunc = `function(res){
  	if(res.success){
    	return res.result
    }else{
    	return []
    }
  }`;
}
export let changeFuncStr = `function(value, key,vm,http){}`
// 基本的form类型
export const formItemList = [
  'input',
  'textarea',
  'date',
  'time',
  'number',
  'radio',
  'checkbox',
  'select',
  'rate',
  'switch',
  'slider',
  'uploadImg',
  'uploadFile',
  'cascader',
  'treeSelect',
  'batch'
]
// 基础控件
export const basicsList = [
  {
    type: "input", // 表单类型
    label: "输入框", // 标题文字
    icon: "icon-write",
    options: {
      type: "text",
      width: "100%", // 宽度
      tableWidth: "100px", // 动态表格中的宽度
      defaultValue: "", // 默认值
      placeholder: "请输入", // 没有输入时，提示文字
      clearable: false,
      maxLength: null,
      addonBefore: "",
      addonAfter: "",
      hidden: false, // 是否隐藏，false显示，true隐藏
      disabled: false, // 是否禁用，false不禁用，true禁用
      linkageData: `[]`,
      changeFunc: changeFuncStr,
      // 评估指标相关配置
      isEvaluationField: false // 是否为评估指标字段
    },
    model: "", // 数据字段
    key: "",
    help: "",
    rules: [
      //验证规则
      {
        required: false, // 必须填写
        message: "必填项"
      }
    ]
  },
  {
    type: "textarea", // 表单类型
    label: "文本框", // 标题文字
    icon: "icon-edit",
    options: {
      width: "100%", // 宽度
      tableWidth: "200px", // 动态表格中的宽度
      minRows: 4,
      maxRows: 6,
      maxLength: null,
      defaultValue: "",
      clearable: false,
      hidden: false, // 是否隐藏，false显示，true隐藏
      disabled: false,
      placeholder: "请输入",
      linkageData: `[]`,
      changeFunc: changeFuncStr,
      // 评估指标相关配置
      isEvaluationField: false
    },
    model: "", // 数据字段
    key: "",
    help: "",
    rules: [
      {
        required: false,
        message: "必填项"
      }
    ]
  },
  {
    type: "number", // 表单类型
    label: "数字输入框", // 标题文字
    icon: "icon-number",
    options: {
      width: "100%", // 宽度
      tableWidth: "120px", // 动态表格中的宽度
      defaultValue: 0, // 默认值
      min: null, // 可输入最小值
      max: null, // 可输入最大值
      precision: null,
      step: 1, // 步长，点击加减按钮时候，加减多少
      hidden: false, // 是否隐藏，false显示，true隐藏
      disabled: false, //是否禁用
      placeholder: "请输入",
      linkageData: `[]`,
      changeFunc: changeFuncStr,
      // 评估指标相关配置
      isEvaluationField: false
    },
    model: "", // 数据字段
    key: "",
    help: "",
    rules: [
      {
        required: false,
        message: "必填项"
      }
    ]
  },
  {
    type: "select", // 表单类型
    label: "下拉选择器", // 标题文字
    icon: "icon-xiala",
    options: {
      width: "100%", // 宽度
      tableWidth: "150px", // 动态表格中的宽度
      defaultValue: "", // 空字符串 undefined添加控件时 属性会丢失
      multiple: false, // 是否允许多选
      disabled: false, // 是否禁用
      clearable: false, // 是否显示清除按钮
      hidden: false, // 是否隐藏，false显示，true隐藏
      placeholder: "请选择", // 默认提示文字
      linkageData: `[]`,
      changeFunc: changeFuncStr,
      dynamicKey: "",
      dynamic: "static",
      ajaxData: {
        url: "",
        type: "GET",
        params: `{}`,
        header: `{}`,
        callFunc: `function(res){
          return res.data;
        }`,
      },
      options: [],
      staticOptions: [
        {
          value: "1",
          label: "选项1"
        },
        {
          value: "2",
          label: "选项2"
        }
      ],
      showSearch: false, // 是否显示搜索框，搜索选择的项的值，而不是文字
      // 评估指标相关配置
      isEvaluationField: false
    },
    model: "",
    key: "",
    help: "",
    rules: [
      {
        required: false,
        message: "必填项"
      }
    ]
  },
  {
    type: "checkbox",
    label: "多选框",
    icon: "icon-duoxuan1",
    options: {
      width: "100%", // 宽度
      tableWidth: "150px", // 动态表格中的宽度
      disabled: false, //是否禁用
      hidden: false, // 是否隐藏，false显示，true隐藏
      defaultValue: [],
      linkageData: `[]`,
      changeFunc: changeFuncStr,
      dynamicKey: "",
      dynamic: "static",
      options: [],
      staticOptions: [
        {
          value: "1",
          label: "选项1"
        },
        {
          value: "2",
          label: "选项2"
        }
      ],
      ajaxData: new AjaxData(),
      // 评估指标相关配置
      isEvaluationField: false
    },
    model: "",
    key: "",
    help: "",
    rules: [
      {
        required: false,
        message: "必填项"
      }
    ]
  },
  {
    type: "radio", // 表单类型
    label: "单选框", // 标题文字
    icon: "icon-danxuan-cuxiantiao",
    options: {
      width: "100%", // 宽度
      tableWidth: "150px", // 动态表格中的宽度
      disabled: false, //是否禁用
      hidden: false, // 是否隐藏，false显示，true隐藏
      defaultValue: "", // 默认值
      linkageData: `[]`,
      changeFunc: changeFuncStr,
      dynamicKey: "",
      dynamic: "static",
      options: [],
      staticOptions: [
        {
          value: "1",
          label: "选项1"
        },
        {
          value: "2",
          label: "选项2"
        }
      ],
      ajaxData: new AjaxData(),
      // 评估指标相关配置
      isEvaluationField: false
    },
    model: "",
    key: "",
    help: "",
    rules: [
      {
        required: false,
        message: "必填项"
      }
    ]
  },
  {
    type: "date", // 表单类型
    label: "日期选择框", // 标题文字
    icon: "icon-calendar",
    options: {
      width: "100%", // 宽度
      tableWidth: "150px", // 动态表格中的宽度
      defaultValue: "", // 默认值，字符串 12:00:00
      rangeDefaultValue: [], // 默认值，字符串 12:00:00
      range: false, // 范围日期选择，为true则会显示两个时间选择框（同时defaultValue和placeholder要改成数组），
      showTime: false, // 是否显示时间选择器
      disabled: false, // 是否禁用
      hidden: false, // 是否隐藏，false显示，true隐藏
      clearable: false, // 是否显示清除按钮
      placeholder: "请选择",
      rangePlaceholder: ["开始时间", "结束时间"],
      format: "YYYY-MM-DD",// 展示格式  （请按照这个规则写 YYYY-MM-DD HH:mm:ss，区分大小写）
      linkageData: `[]`,
      changeFunc: changeFuncStr,
      // 评估指标相关配置
      isEvaluationField: false
    },
    model: "",
    key: "",
    help: "",
    rules: [
      {
        required: false,
        message: "必填项"
      }
    ]
  },
  {
    type: "time", // 表单类型
    label: "时间选择框", // 标题文字
    icon: "icon-time",
    options: {
      width: "100%", // 宽度
      tableWidth: "120px", // 动态表格中的宽度
      defaultValue: "", // 默认值，字符串 12:00:00
      disabled: false, // 是否禁用
      hidden: false, // 是否隐藏，false显示，true隐藏
      clearable: false, // 是否显示清除按钮
      placeholder: "请选择",
      format: "HH:mm:ss", // 展示格式
      linkageData: `[]`,
      changeFunc: changeFuncStr,
      // 评估指标相关配置
      isEvaluationField: false
    },
    model: "",
    key: "",
    help: "",
    rules: [
      {
        required: false,
        message: "必填项"
      }
    ]
  },
  {
    type: "rate", // 表单类型
    label: "评分", // 标题文字
    icon: "icon-pingfen_moren",
    options: {
      width: "100%", // 宽度
      tableWidth: "120px", // 动态表格中的宽度
      defaultValue: 0,
      max: 5, // 最大值
      disabled: false, // 是否禁用
      hidden: false, // 是否隐藏，false显示，true隐藏
      allowHalf: false,// 是否允许半选
      linkageData: `[]`,
      changeFunc: changeFuncStr,
      // 评估指标相关配置
      isEvaluationField: false
    },
    model: "",
    key: "",
    help: "",
    rules: [
      {
        required: false,
        message: "必填项"
      }
    ]
  },
  {
    type: "slider", // 表单类型
    label: "滑动输入条", // 标题文字
    icon: "icon-menu",
    options: {
      width: "100%", // 宽度
      tableWidth: "150px", // 动态表格中的宽度
      defaultValue: 0, // 默认值， 如果range为true的时候，则需要改成数组,如：[12,15]
      disabled: false, // 是否禁用
      hidden: false, // 是否隐藏，false显示，true隐藏
      min: 0, // 最小值
      max: 100, // 最大值
      step: 1, // 步长，取值必须大于 0，并且可被 (max - min) 整除
      showInput: false,// 是否显示输入框，range为true时，请勿开启
      // range: false ,// 双滑块模式
      linkageData: `[]`,
      changeFunc: changeFuncStr,
      // 评估指标相关配置
      isEvaluationField: false
    },
    model: "",
    key: "",
    help: "",
    rules: [
      {
        required: false,
        message: "必填项"
      }
    ]
  },
  {
    type: "uploadFile", // 表单类型
    label: "上传文件", // 标题文字
    icon: "icon-upload",
    options: {
      defaultValue: "[]",
      multiple: false,
      disabled: false,
      hidden: false, // 是否隐藏，false显示，true隐藏
      drag: false,
      downloadWay: "a",
      dynamicFun: "",
      width: "100%",
      tableWidth: "200px", // 动态表格中的宽度
      limit: 3,
      data: "{}",
      fileName: "file",
      headers: {},
      action: "",
      downloadFileUrl: "",
      placeholder: "上传",
      linkageData: `[]`,
      changeFunc: changeFuncStr,
      // 评估指标相关配置
      isEvaluationField: false
    },
    model: "",
    key: "",
    help: "",
    rules: [
      {
        required: false,
        message: "必填项"
      }
    ]
  },
  {
    type: "uploadImg",
    label: "上传图片",
    icon: "icon-image",
    options: {
      defaultValue: "[]",
      multiple: false,
      hidden: false, // 是否隐藏，false显示，true隐藏
      disabled: false,
      width: "100%",
      tableWidth: "200px", // 动态表格中的宽度
      data: "{}",
      limit: 3,
      placeholder: "上传",
      fileName: "image",
      headers: {},
      action: "",
      downloadImageUrl: "",
      listType: "picture-card",
      linkageData: `[]`,
      changeFunc: changeFuncStr,
      // 评估指标相关配置
      isEvaluationField: false
    },
    model: "",
    key: "",
    help: "",
    rules: [
      {
        required: false,
        message: "必填项"
      }
    ]
  },
  {
    type: "treeSelect", // 表单类型
    label: "树选择器", // 标题文字
    icon: "icon-tree",
    options: {
      width: "100%", // 宽度
      tableWidth: "180px", // 动态表格中的宽度
      disabled: false, //是否禁用
      defaultValue: undefined, // 默认值
      multiple: false,
      hidden: false, // 是否隐藏，false显示，true隐藏
      clearable: false, // 是否显示清除按钮
      showSearch: false, // 是否显示搜索框，搜索选择的项的值，而不是文字
      treeCheckable: false,
      placeholder: "请选择",
      linkageData: `[]`,
      changeFunc: changeFuncStr,
      dynamicKey: "",
      dynamic: "static",
      options: [],
      staticOptions: [],
      ajaxData: new AjaxData(),
      // 评估指标相关配置
      isEvaluationField: false
    },
    model: "",
    key: "",
    help: "",
    rules: [
      {
        required: false,
        message: "必填项"
      }
    ]
  },
  {
    type: "cascader", // 表单类型
    label: "级联选择器", // 标题文字
    icon: "icon-guanlian",
    options: {
      width: "100%", // 宽度
      tableWidth: "180px", // 动态表格中的宽度
      disabled: false, //是否禁用
      hidden: false, // 是否隐藏，false显示，true隐藏
      defaultValue: undefined, // 默认值
      showSearch: false, // 是否显示搜索框，搜索选择的项的值，而不是文字
      placeholder: "请选择",
      clearable: false, // 是否显示清除按钮
      linkageData: `[]`,
      changeFunc: changeFuncStr,
      dynamicKey: "",
      dynamic: "static",
      options: [],
      staticOptions: [],
      ajaxData: new AjaxData(),
      // 评估指标相关配置
      isEvaluationField: false
    },
    model: "",
    key: "",
    help: "",
    rules: [
      {
        required: false,
        message: "必填项"
      }
    ]
  },
  {
    type: "batch",
    label: "动态表格",
    icon: "icon-biaoge",
    list: [],
    options: {
      scrollY: 0,
      disabled: false,
      hidden: false, // 是否隐藏，false显示，true隐藏
      showLabel: false,
      hideSequence: false,
      width: "100%",
      rowKey: "id",
      hideAddBtn: false, // 是否隐藏增加按钮
      hideOprCol: false // 是否隐藏操作列
    },
    model: "",
    key: "",
    help: "",

  },
  {
    type: "selectInputList",
    label: "选择输入列",
    icon: "icon-biaoge",
    columns: [
      {
        value: "1",
        label: "选项1",
        list: []
      },
      {
        value: "2",
        label: "选项2",
        list: []
      }
    ],
    options: {
      disabled: false,
      multiple: true, // 是否允许多选
      hidden: false, // 是否隐藏，false显示，true隐藏
      showLabel: false,
      width: "100%"
    },
    model: "",
    key: "",
    help: ""
  },
  {
    type: "editor",
    label: "富文本",
    icon: "icon-LC_icon_edit_line_1",
    list: [],
    options: {
      height: 300,
      placeholder: "请输入",
      defaultValue: "",
      chinesization: true,
      hidden: false, // 是否隐藏，false显示，true隐藏
      disabled: false,
      showLabel: false,
      width: "100%"
    },
    model: "",
    key: "",
    help: "",
    rules: [
      {
        required: false,
        message: "必填项"
      }
    ]
  },
  {
    type: "switch", // 表单类型
    label: "开关", // 标题文字
    icon: "icon-kaiguan3",
    options: {
      defaultValue: false, // 默认值 Boolean 类型
      hidden: false, // 是否隐藏，false显示，true隐藏
      disabled: false,// 是否禁用
      linkageData: `[]`,
      changeFunc: changeFuncStr,
      // 评估指标相关配置
      isEvaluationField: false
    },
    model: "",
    key: "",
    help: "",
    rules: [
      {
        required: false,
        message: "必填项"
      }
    ]
  },
  {
    type: "button", // 表单类型
    label: "按钮", // 标题文字
    icon: "icon-button-remove",
    key: "",
    model: "",
    options: {
      type: "primary",
      handle: "submit",
      dynamicFun: `function(vm,http){}`,
      hidden: false, // 是否隐藏，false显示，true隐藏
      disabled: false // 是否禁用，false不禁用，true禁用
    },
  },
  {
    type: "alert",
    label: "警告提示",
    icon: "icon-zu",
    options: {
      type: "success",
      description: "",
      showIcon: false,
      banner: false,
      hidden: false, // 是否隐藏，false显示，true隐藏
      closable: false
    },
    key: ""
  },
  {
    type: "text",
    label: "文字",
    icon: "icon-zihao",
    options: {
      textAlign: "left",
      hidden: false, // 是否隐藏，false显示，true隐藏
      showRequiredMark: false,
      color: "rgb(0, 0, 0)",
      fontFamily: "SimHei",
      fontSize: "16pt"
    },
    key: ""
  },
  {
    type: "html",
    label: "HTML",
    icon: "icon-ai-code",
    options: {
      hidden: false, // 是否隐藏，false显示，true隐藏
      defaultValue: "<strong>HTML</strong>"
    },
    key: ""
  }
];
//给基本表单控件添加标签属性
basicsList.forEach(el => {
  if (formItemList.includes(el.type)) {
    el.options.showLabel = true
    if (el.options.hasOwnProperty('dynamicKey')) {
      el.options.getOptionFunc = undefined;
    }
  }
})
// 高级控件
// export const highList = [];

import { Alert } from "ant-design-vue";

// 自定义组件
export const customComponents = {
  title: "自定义组件",
  list: [
    {
      label: "测试",
      type: "jkjksdf",
      component: Alert,
      options: {
        multiple: false,
        disabled: false,
        width: "100%",
        data: "{}",
        limit: 3,
        placeholder: "上传",
        action: "",
        listType: "picture-card"
      },
      model: "",
      key: "",
      rules: [
        {
          required: false,
          message: "必填项"
        }
      ]
    }
  ]
};
// window.$customComponentList = customComponents.list;

// 布局控件
export const layoutList = [
  {
    type: "divider",
    label: "分割线",
    icon: "icon-fengexian",
    options: {
      orientation: "left"
    },
    key: "",
    model: ""
  },
  {
    type: "card",
    label: "卡片布局",
    icon: "icon-qiapian",
    list: [],
    key: "",
    model: ""
  },
  {
    type: "tabs",
    label: "标签页布局",
    icon: "icon-tabs",
    options: {
      tabBarGutter: null,
      type: "line",
      tabPosition: "top",
      size: "default",
      animated: true,
      hidden: false // 是否隐藏，false显示，true隐藏
    },
    columns: [
      {
        value: "1",
        label: "选项1",
        list: []
      },
      {
        value: "2",
        label: "选项2",
        list: []
      }
    ],
    key: "",
    model: "",
    help: "标签页布局用于组织表单控件，现在支持数据字段用于联动控制"
  },
  {
    type: "grid",
    label: "栅格布局",
    icon: "icon-zhage",
    columns: [
      {
        span: 12,
        list: []
      },
      {
        span: 12,
        list: []
      }
    ],
    options: {
      gutter: 0
    },
    key: "",
    model: ""
  },
  {
    type: "table",
    label: "表格布局",
    icon: "icon-biaoge",
    trs: [
      {
        tds: [
          {
            colspan: 1,
            rowspan: 1,
            list: []
          },
          {
            colspan: 1,
            rowspan: 1,
            list: []
          }
        ]
      },
      {
        tds: [
          {
            colspan: 1,
            rowspan: 1,
            list: []
          },
          {
            colspan: 1,
            rowspan: 1,
            list: []
          }
        ]
      }
    ],
    options: {
      width: "100%",
      bordered: true,
      bright: false,
      small: true,
      customStyle: ""
    },
    key: "",
    model: ""
  }
];

export class treeOptionClass {//使用类 会导致树形控件报错
  constructor(id) {
    this.id = id + "";
    this.label = "";
    this.value = "";
    this.children = [];
  }
}
export function setOption(id) {
  return {
    id:id+"",
    label:"",
    value:"",
    children:[],
  }
}

// linkpageData 格式 value handler 二选一  fields控制显隐控件的model字段值
// handler 必须返回bool值
// [
//   {
//   	"value":1,
//     "fields":["reason"],
//     "handler":"async function(v,http){return v == 1}"
//   }
// ]