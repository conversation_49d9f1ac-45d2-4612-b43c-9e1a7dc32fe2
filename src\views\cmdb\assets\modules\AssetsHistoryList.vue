<template>
  <div>
    <a-table
      ref="table"
      bordered
      :rowKey="(record) => { return record.id}"
      :columns="columns"
      :dataSource="dataSource" :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
      :pagination="ipagination"
      :loading="loading"
      @change='handleTableChange'
    >
      <template slot="tooltip" slot-scope="text">
        <a-tooltip placement="topLeft" :title="text" trigger="hover">
          <div class="tooltip">
            {{ text }}
          </div>
        </a-tooltip>
      </template>
    </a-table>
  </div>
</template>

<script>
import {JeecgListMixin} from '@/mixins/JeecgListMixin'
export default {
  name:'AssetsHistoryList',
  props:{
    assetsId:{
      type:'',
      required:true
    }
  },
  mixins: [JeecgListMixin,],
  data() {
    return {
      disableMixinCreated:true,
      ipagination: {
        current: 1,
        pageSize: 5,
        pageSizeOptions: ['5', '10', '20'],
        showTotal: (total, range) => {
          return ' 共' + total + '条'
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0
      },
      columns: [
        {
          title: '时间',
          dataIndex: 'createTime',
          customCell: () => {
            let cellStyle = 'text-align: center;width:160px'
            return {
              style: cellStyle
            }
          },
        },
        {
          title: '操作人',
          dataIndex: 'createBy',
          customCell: () => {
            let cellStyle = 'text-align: center;width:200px'
            return {
              style: cellStyle
            }
          },
        },
        {
          title: '操作内容',
          dataIndex: 'content',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 100px;max-width:300px'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'tooltip'
          },
        }],
      url: {
        list: '/assets/assets/queryLog',
      },
    }
  },
  watch:{
    assetsId:{
      handler(val) {
        this.show(val)
      },
      deep:true,
      immediate:true
    },
  },

  methods: {
    show(assetsId) {
      if(assetsId){
        this.queryParam.assetsId = assetsId
        this.loadData(1)
      }
    },
   /* queryLog(assetsId) {
      getAction(this.url.queryLog, { assetsId: this.assetsId }).then((res) => {
        if (res.success) {
          this.list = res.result
        } else {
          this.$message.warning(res.message)
        }
      })
    }*/
  }
}
</script>

<style lang="less" scoped="scoped">
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
</style>
