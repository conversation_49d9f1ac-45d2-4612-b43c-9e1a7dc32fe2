<template>
  <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0' }" class='card-style' style='min-height: 100%'>
    <a-tabs v-model='curKey' @change="tabChange">
      <a-tab-pane key="1" tab="备份策略">
        <div style='width:100%;padding:0 5px'>
          <BfStrategy></BfStrategy>
        </div>
      </a-tab-pane>
      <a-tab-pane key="2" tab="恢复策略">
        <div style='width:100%;padding:0 5px'>
          <RestoreStrategy></RestoreStrategy>
        </div>
      </a-tab-pane>
    </a-tabs>
  </a-card>
</template>
<script>
import BfStrategy from '@views/dataBase/GBase/modules/BfStrategy.vue'
import RestoreStrategy from '@views/dataBase/GBase/modules/RestoreStrategy.vue'
export default {
  name: 'strategyManage',
  components: {
    BfStrategy,
    RestoreStrategy,
  },
  data(){
    return {
      curKey: '1'
    }
  },
  created() {

  },
  mounted() {

  },
  methods: {
    tabChange(key) {
      this.curKey = key
    }
  }
}
</script>

<style scoped lang='less'>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
</style>