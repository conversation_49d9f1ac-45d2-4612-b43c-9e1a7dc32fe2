<template>
  <j-modal
    :title='title'
    :width='500'
    :visible='visible'
    :confirmLoading='confirmLoading'
    @ok='handleOk'
    @cancel='handleCancel'
    cancelText='关闭'
  >
    <a-spin :spinning='confirmLoading'>
      <a-form-model ref='form' v-bind='layout' :model='model' :rules='validatorRules'>
        <a-row>
          <a-col :span='24'>
            <a-form-model-item label='起始位置' required prop='startIndex'>
              <a-input-number :min="1" :max="12" :step="1" :precision="0" v-model="model.startIndex"/>
<!--              <a-input v-model='model.startIndex' placeholder='请输入铭牌打印纸起始打印位置'/>-->
            </a-form-model-item>
          </a-col>
          <a-col :span='24'>
            <a-form-model-item label='是否显示边框' required prop='showBorder'>
              <a-radio-group v-model='model.showBorder'>
                <a-radio value="1">
                  是
                </a-radio>
                <a-radio value="0">
                  否
                </a-radio>
              </a-radio-group>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </a-spin>
  </j-modal>
</template>

<script>

import { downloadFile } from '@/api/manage'
export default {
  name: 'ExportPdfModal',
  data() {
    return {
      title: '导出设置',
      visible: false,
      model: {},
      defaultModel:{
        ids:'',
        showBorder: '1',
        startIndex: 1
      },
      layout: {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 12 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 12 },
        },
      },
      confirmLoading: false,
      validatorRules: {
        startIndex: [
          { required: true, message: '请输入起始位置!' },
        ],
        showBorder: [
          { required: true, message: '请选择是否显示边框!' },
        ]
      }
    }
  },
  created() {},
  methods: {
    add(ids) {
      this.defaultModel.ids=ids
      this.edit(this.defaultModel)
    },
    edit(record) {
       this.model = Object.assign({}, record)
      this.visible = true
    },
    close() {
      this.$refs.form.clearValidate()
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
       const that = this
      // 触发表单验证
      this.$refs.form.validate(valid => {
        if (valid) {
          that.confirmLoading = true
          const fileName = "资产二维码.pdf";
          downloadFile("/sys/downPdf/assetsNameplate",fileName,that.model).then((res) => {

          }).catch((res)=>{
            that.$message.error('服务异常，文件下载失败')
          }).finally(() => {
            that.confirmLoading = false
            that.close()
            that.$emit('setRowKeys')
          })
        } else {
          return false
        }
      })
    },
    handleCancel() {
      this.close()
    }
  }
}
</script>

<style scoped lang='less'>
</style>