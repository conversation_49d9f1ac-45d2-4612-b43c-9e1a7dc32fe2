<template>
  <div style="height: 100%; width: 100%">
    <div class="topProductBox">
      <top-product v-for="(item,idx) in products" :key="idx" :src="item.src" :name="item.name" :value="item.value">
      </top-product>
    </div>
    <div class="alarmAll">
      <div class="planet-box">
        <planet-orbiting :alarmData="dataList" :allNum="allNum" :paused="isPuased"></planet-orbiting>
      </div>

    </div>
  </div>
</template>

<script>
  import PlanetOrbiting from './PlanetOrbiting.vue'
  import topProduct from './topProduct.vue'
  import {
    getAction
  } from '@/api/manage'
  export default {
    components: {
      topProduct,
      PlanetOrbiting,
    },
    data() {
      return {
        dataList: [],
        allNum: 0,
        products: [{
            src: require('/public/statsCenter/alarm/server.png'),
            name: '',
            value: 0,
          },
          {
            src: require('/public/statsCenter/alarm/networkDevice.png'),
            name: '',
            value: 0,
          },
          {
            src: require('/public/statsCenter/alarm/database.png'),
            name: '',
            value: 0,
          },
          {
            src: require('/public/statsCenter/alarm/middleware.png'),
            name: '',
            value: 0,
          },
          {
            src: require('/public/statsCenter/alarm/desktop.png'),
            name: '',
            value: 0,
          },
          {
            src: require('/public/statsCenter/alarm/safeDevice.png'),
            name: '',
            value: 0,
          },
        ],
        url: {
          productCategory: 'data-analysis/alarm/count/productCategory',
        },
      }
    },
    mounted() {
      this.getProduct()
    },
    computed: {
      isPuased() {
        return false; //window._CONFIG['simpleModel'] === 0?false:true;
      }
    },
    methods: {
      getProduct() {
        getAction(this.url.productCategory).then((res) => {
          if (res.success && res.result) {
            this.dataList = res.result.countProductCategory;
            this.allNum = res.result.allCount;
            res.result.countProductCategory.forEach((element, idx) => {
              Object.assign(this.products[idx], element)
            });
          }
        })
      },
    },
  }
</script>

<style scoped lang="less">
  .topProductBox {
    height: 80px;
    width: 100%;
    display: flex;
    justify-content: space-around;
  }

  .alarmAll {
    height: calc(100% - 80px);
    width: 100%;
  }

  .planet-box {
    height: 80%;
  }
</style>