<template>
  <div style="height: 100%;">
    <keep-alive >
      <component :is="pageName" :data="data"/>
    </keep-alive>
  </div>
</template>
<script>
  import SysNoticeTemplateList from './SysNoticeTemplateList'
  import SysNoticeTemplateDetail from './modules/SysNoticeTemplateDetail'
  export default {
    name: "SysNoticeTemplateManage",
    data() {
      return {
        isActive: 0,
        data:{}
      };
    },
    components: {
      SysNoticeTemplateList,
      SysNoticeTemplateDetail
    },
    created(){
      this.pButton1(0);
    },
    //使用计算属性
    computed: {
      pageName() {
        switch (this.isActive) {
          case 0:
            return "SysNoticeTemplateList";
            break;

          default:
            return "SysNoticeTemplateDetail";
            break;
        }
      }
    },
    methods: {
      pButton1(index) {
        this.isActive = index;
      },
      pButton2(index,item) {
        this.isActive = index;
        this.data = item;
      }
    }
  }
</script>