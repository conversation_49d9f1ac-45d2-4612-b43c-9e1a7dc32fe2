<template>
  <div class='zr-business-view' ref='zrBusinessView'>
    <div class='business-view-left'>
      <div v-if='!comparing' style='height: 100%;width: 100%'>
<!--        <div class='business-view-left-top' style='height: 45%'>
          <zr-evaluation-category-rank ref='lefTop'></zr-evaluation-category-rank>
        </div>
        <div style='height: 55%' class='business-view-left-bottom'>
          <zr-completion-status ref='lefBottom'></zr-completion-status>
        </div>-->
        <zr-op-left-list :project-id='timeRange'></zr-op-left-list>
      </div>
      <zr-op-compare v-else :valuationDates='valuationDates' :init-date='leftProId'
        :indicators='leftIndicator' @changeProject='changeLeftComparing'></zr-op-compare>
    </div>
    <div class='business-view-center'>
      <div v-if='!comparing' class="header-right-time">
        <span class='time-range-label'>任务单:</span>
        <div class="time-range-span">
          <a-select v-model='timeRange' style='width: 180px' placeholder='请选择任务单'
            :getPopupContainer="(node) => node.parentNode" :dropdownClassName='"custom-select-dropdown"'
            @change="proChange">
            <a-select-option v-for="item in valuationDates" :value="item.id" :key="item.id">
              {{ item.projectName }}
            </a-select-option>
          </a-select>
        </div>
        <div class='report-btn' @click='handleReport'>
          评估报告
        </div>
      </div>
      <zr-op-center ref="center" :eval-data='evalData' v-if='!comparing' @startComparing='changeComparing'
        @categoryComparing='changeCateGory'>
      </zr-op-center>
      <zr-op-compare-radar v-else  :category='curCategory' :left-data='leftRadarData' :right-data='rightRadarData' @cancelCompare='changeComparing'></zr-op-compare-radar>
    </div>
    <div class='business-view-right'>
      <div v-if='!comparing' style='width: 100%;height: 100%'>
        <div class='business-view-right-top' :style='{height:"45%"}'>
          <zr-evaluation-indicators :metrics-info-list="metricsInfoList" ref="rightTop"></zr-evaluation-indicators>
        </div>
        <div class='business-view-right-bottom' style='height: 55%'>
          <zr-focus-alert ref='rightBottom'></zr-focus-alert>
        </div>
      </div>
      <zr-op-compare v-else :valuationDates='valuationDates' :init-date='rightProId'
        :indicators='rightIndicator' @changeProject='changeRightComparing'></zr-op-compare>
    </div>
  </div>
</template>
<script>
  import {
    getAction
  } from '@/api/manage'
  import resizeObserverMixin from '@views/statsCenter/com/resizeObserverMixin'
  import ZrEvaluationCategoryRank from '@views/zrBigscreens/zrOperationalEvaluation/modules/ZrEvaluationCategoryRank.vue'
  import ZrCompletionStatus from '@views/zrBigscreens/zrOperationalEvaluation/modules/ZrCompletionStatus.vue'
  import ZrEvaluationIndicators from '@views/zrBigscreens/zrOperationalEvaluation/modules/ZrEvaluationIndicators.vue'
  import ZrFocusAlert from '@views/zrBigscreens/zrOperationalEvaluation/modules/ZrFocusAlert.vue'
  import ZrOpCenter from '@views/zrBigscreens/zrOperationalEvaluation/modules/ZrOpCenter.vue'
  import ZrOpCompareRadar from '@views/zrBigscreens/zrOperationalEvaluation/modules/ZrOpCompareRadar.vue'
  import {
    downloadFile
  } from '@/api/manage'
  import {
    indicators
  } from '@views/zrBigscreens/modules/zrUtil'
  import ZrOpCompare from './modules/ZrOpCompare.vue'
  import ZrOpLeftList from './modules/ZrOpLeftList.vue'
  export default {
    name: 'businessIndex',
    components: {
      ZrOpLeftList,
      ZrOpCompare,
      ZrOpCenter,
      ZrEvaluationCategoryRank,
      ZrCompletionStatus,
      ZrEvaluationIndicators,
      ZrFocusAlert,
      ZrOpCompareRadar,
    },
    mixins: [resizeObserverMixin],
    data() {
      return {
        centerH: '',
        centerPd: "",
        hScaleValue: 1, // 用于缩放比例
        timeRange: undefined,
        valuationDates: [],
        comparing: false,
        leftIndicator: [],
        rightIndicator: [],
        leftRadarData: [],
        rightRadarData: [],
        curCategory: '',
        url: {
          EvaluateData: 'devops/projectInfo/getEvaluateData',
          ProjectList: '/devops/projectInfo/getProjectList'
        },
        evalData: {},
        metricsInfoList: [],
        leftProId:'',
        rightProId:'',
      }
    },
    mounted() {
      this.getList()
    },
    methods: {
      //获取任务单列表
      getList() {
        getAction(this.url.ProjectList).then((res) => {
          this.valuationDates = res.result
          this.timeRange = this.valuationDates[0].id
          this.getData(this.timeRange)
        })
      },
      proChange(value) {
        this.getData(value)
      },
      //获取任务单数据
      getData(value) {
        getAction(this.url.EvaluateData, {
          projectId: value
        }).then((res) => {
          this.evalData = res.result || {}
          this.metricsInfoList =  this.evalData?.metricsInfoList || []
          this.$refs.rightBottom.show(res.result)
        })
      },
      //生成指标数据
      generateIndicators() {
        let tem = []
        for (let i = 0; i < indicators.length; i++) {
          let score = Math.floor(Math.random() * (100 - 40 + 1)) + 40;
          let level
          if (score >= 90) {
            level = "A"
          } else if (score > 75 && score < 90) {
            level = "B"
          } else if (score > 60 && score <= 75) {
            level = "C"
          } else {
            level = "D"
          }
          tem.push({
            name: indicators[i].name,
            score,
            level,
            type: indicators[i].type
          })
        }
        return tem;
      },
      //选择指标类
      changeCateGory(type) {
        this.curCategory = type;
        this.changeComparing()
      },
      // 获取任务单指标评估结果列表
      async getProjectEvaluateData(projectId){
        let temData = {}
        await getAction(this.url.EvaluateData,{projectId:projectId}).then(res=>{
          if(res.success){
            temData = res.result
          }
          // console.log("getProjectEvaluateData 获取指标评估结果成功",temData)
        }).catch(err=>{
          console.log("getProjectEvaluateData 获取指标评估结果失败",err)
        })
        return temData
      },
      changeLeftComparing(e){
        if(this.leftProId !== e){
          this.leftProId = e
          this.changeComparing(true)
        }
      },
      changeRightComparing(e){
        if(this.rightProId !== e){
          this.rightProId = e
          this.changeComparing(true)
        }
      },
      //改变对比状态
      async changeComparing(isComparing) {
        this.comparing =isComparing || !this.comparing
        if (this.comparing) {
          if(this.leftProId==="" && this.rightProId===""){
            let curIndex = this.valuationDates.findIndex(el=>el.id === this.timeRange)
            let rightIndex = curIndex>0?curIndex-1:(this.valuationDates.length>1?curIndex+1:0)
            this.leftProId = this.timeRange
            this.rightProId = this.valuationDates[rightIndex].id
            // return;
          }
          let leftData = await this.getProjectEvaluateData(this.leftProId)
          let rightData = await this.getProjectEvaluateData(this.rightProId)
          let leftMetrics = leftData.metricsInfoList || []
          let rightMetrics = rightData.metricsInfoList || []
          this.leftRadarData = leftData.metricsTypeList || []
          this.rightRadarData = rightData.metricsTypeList || []
          // console.log("leftData",leftMetrics)
          // console.log("rightData",rightMetrics)
          this.leftIndicator = this.curCategory ? leftMetrics.filter(el => el.metricsTypeId === this.curCategory) : leftMetrics
          this.rightIndicator = this.curCategory ? rightMetrics.filter(el => el.metricsTypeId === this.curCategory) :rightMetrics
          if(this.leftIndicator.length && this.rightIndicator.length){
            for (let i = 0; i < this.leftIndicator.length; i++) {
              // 定义等级对应的数值
              const levelValues = {
                'A': 4,
                'B': 3,
                'C': 2,
                'D': 1
              };
              const leftLevelValue = this.leftIndicator[i].metricsResult?levelValues[this.leftIndicator[i].metricsResult] : 0;
              const rightLevelValue = this.rightIndicator[i].metricsResult?levelValues[this.rightIndicator[i].metricsResult] : 0;

              if (leftLevelValue < rightLevelValue) {
                this.leftIndicator[i].color = "#FF1D42"
              } else if (leftLevelValue > rightLevelValue) {
                this.rightIndicator[i].color = "#FF1D42"
              } else {
                // 等级相同时，左右都设置为灰色示例，可按需修改
                this.leftIndicator[i].color = "#fff"
                this.rightIndicator[i].color = "#fff"
              }
            }
          }
        } else {
          this.leftProId = ''
          this.rightProId = ''
          this.leftIndicator = []
          this.rightIndicator = []
          this.curCategory = ''
          this.$nextTick(()=>{
            this.$refs.rightBottom.show(this.evalData)
          })
        }
      },
      //查看评估报告
      handleReport() {
        // this.$message.warning('评估报告暂未开放')
        // return
        const ext = 'docx';
        let fileName = `评估报告.${ext}`;
        downloadFile('/devops/projectInfo/RichTextExportReport', fileName, {
            id: this.timeRange,
            exportType: "word"
          })
          .then(res => {}).catch(err => {
            this.$message.error('获取评估报告失败')
          })
      },
      //监听页面缩放 更新中间区域高度
      resizeObserverCb() {
        this.hScaleValue = window.innerHeight / 1080
        this.centerH = `calc(100% - (70px * ${this.hScaleValue}))`
        // this.centerPd = `calc(100px * ${this.hScaleValue})`
      }
    }
  }
</script>

<style scoped lang='less'>
  @import "~@assets/less/onclickStyle.less";

  ::v-deep .ant-calendar-picker {
    width: calc(320 / 19.2 * 1vw);
    max-width: 320px;
    height: 100%;

    .ant-calendar-picker-input.ant-input {
      background-color: #021527;
      color: #909090;
      height: 100%;
      display: flex;
      align-items: center;

      .ant-calendar-range-picker-separator {
        color: #feffff;
        line-height: 30px;
      }
    }

    input::placeholder {
      color: #feffff !important;
      font-size: 14px;
    }
  }

  .zr-business-view {
    padding-left: calc(33 / 19.2 * 1vw);
    padding-right: calc(33 / 19.2 * 1vw);
    display: flex;
    align-items: center;
    justify-content: space-around;
    height: 100%;

    .business-view-left {
      width: 25%;
      height: 100%;
      //background-image: url(/zrBigScreen/zrBusiness/businessLeftBg.png);
      //background-size: 100% 100%;
      //background-repeat: no-repeat;
      //padding: 42px 41px 42px 24px;
      display: flex;
      flex-direction: column;

      .business-view-left-bottom {
        display: flex;
        flex-direction: column;

        .business-view-left-middle {
          height: 50%
        }

        .business-view-left-bottom {
          height: calc(50% - 8px);
        }
      }
    }

    .business-view-center {
      width: 50%;
      height: 100%;
      //background-image: url(/zrBigScreen/zrBusiness/businessCenterBg.png);
      //background-size: 100% 100%;
      //background-repeat: no-repeat;
      padding: 0px 16px;
      position: relative;

      .header-right-time {
        position: absolute;
        top: 24px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 10;
        display: flex;
        align-items: center;

        .time-range-label {
          font-weight: 400;
          font-size: 14px;
          color: #FFFFFF;
          opacity: 0.8;
          width: 62px;
        }

        .time-range-span {
          margin-left: 8px;
        }

        .report-btn {
          height: 30px;
          //line-height: 30px;
          color: rgba(255, 255, 255, 0.7);
          border: 1px solid rgba(255, 255, 255, 0.7);
          padding: 0 8px;
          border-radius: 4px;
          margin-left: 12px;
          display: flex;
          align-items: center;
          cursor: pointer;

          &:hover {
            color: rgba(255, 255, 255, 1);
            border: 1px solid rgba(255, 255, 255, 1);
          }
        }
      }
    }

    .business-view-right {
      width: 25%;
      height: 100%;
      //background-image: url(/zrBigScreen/zrBusiness/businessRightBg.png);
      //background-size: 100% 100%;
      //background-repeat: no-repeat;
      //padding: 42px 24px 42px 41px;
      display: flex;
      flex-direction: column;
    }
  }
</style>