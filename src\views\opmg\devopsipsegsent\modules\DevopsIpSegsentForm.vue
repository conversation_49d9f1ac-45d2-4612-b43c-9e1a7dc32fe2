<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-item label="IP网段" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['ipSegsent', validatorRules.ipSegsent]" :allowClear="true" autocomplete="off"
                placeholder="网段IP格式*************/255格式  例(************/255)"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item class="two-words" label="备注" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['remarks', validatorRules.remarks]" :allowClear="true" autocomplete="off"
                placeholder="请输入备注"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="执行周期" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-cron ref="innerVueCron" v-decorator="['executeCron', { initialValue: '0 0 0 * * ? *' }]"
                @change="setCorn"></j-cron>
            </a-form-item>
          </a-col>
          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
  import {
    httpAction,
    getAction
  } from '@/api/manage'
  import pick from 'lodash.pick'
  import {
    validateDuplicateValue
  } from '@/utils/util'
  import JFormContainer from '@/components/jeecg/JFormContainer'
  import JCron from '@/components/jeecg/JCron.vue'
  import {
    duplicateCheck
  } from '@/api/api'

  export default {
    name: 'DevopsIpSegsentForm',
    components: {
      JFormContainer,
    },
    props: {
      //流程表单data
      formData: {
        type: Object,
        default: () => {},
        required: false,
      },
      //表单模式：true流程表单 false普通表单
      formBpm: {
        type: Boolean,
        default: false,
        required: false,
      },
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false,
      },
    },
    data() {
      return {
        form: this.$form.createForm(this),
        model: {},
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          },
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          },
        },
        confirmLoading: false,
        ipSegsentId: '',
        validatorRules: {
          ipSegsent: {
            rules: [{
                required: true,
                pattern: /^(?:(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\/(?:[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$/,
                message: 'IP网段为空或格式不正确',
              },
              {
                validator: this.validateIpSegsent
              },
            ],
          },
          remarks: {
            rules: [{
              required: true,
              message: '请输入备注'
            }, {
              min: 1,
              max: 100,
              message: '备注长度应在1-100字符之内'
            }],
          },
        },

        url: {
          add: '/devopsipsegsent/devopsIpSegsent/add',
          edit: '/devopsipsegsent/devopsIpSegsent/edit',
          queryById: '/devopsipsegsent/devopsIpSegsent/queryById',
        },
      }
    },
    computed: {
      formDisabled() {
        if (this.formBpm === true) {
          if (this.formData.disabled === false) {
            return false
          }
          return true
        }
        return this.disabled
      },
      showFlowSubmitButton() {
        if (this.formBpm === true) {
          if (this.formData.disabled === false) {
            return true
          }
        }
        return false
      },
    },
    created() {
      //如果是流程中表单，则需要加载流程表单data
      this.showFlowData()
    },
    methods: {
      add() {
        this.edit({})
      },
      setCorn(data) {
        if (data && data.target != null) {
          let dataList = data.target.value.split(' ')
          if (dataList[0] == '*') {
            this.$message.warning('请确认是否每秒都执行')
          }
        } else {
          let dataList = data.split(' ')
          if (dataList[0] == '*') {
            this.$message.warning('请确认是否每秒都执行')
          }
        }
        this.$nextTick(() => {
          this.form.cronExpression = data
        })
      },
      edit(record) {
        this.ipSegsentId = record.id
        this.form.resetFields()
        this.model = Object.assign({}, record)
        this.visible = true
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.model, 'ipSegsent', 'remarks', 'executeCron'))
        })
      },
      validateIpSegsent(rule, value, callback) {
        var params = {
          tableName: 'devops_ip_segsent',
          fieldName: 'ip_segsent',
          fieldVal: value,
          dataId: this.ipSegsentId,
        }
        duplicateCheck(params).then((res) => {
          if (res.success) {
            callback()
          } else {
            callback('IP网段已存在!')
          }
        })
      },
      //渲染流程表单数据
      showFlowData() {
        if (this.formBpm === true) {
          let params = {
            id: this.formData.dataId
          }
          getAction(this.url.queryById, params).then((res) => {
            if (res.success) {
              this.edit(res.result)
            }
          })
        }
      },
      submitForm() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            var ipSegsentArr = values.ipSegsent.split('/')
            if (null != ipSegsentArr) {
              var ipArr = ipSegsentArr[0].split('.')
              if (parseInt(ipSegsentArr[1]) < parseInt(ipArr[3])) {
                that.$message.warning('请输入正确的IP网段')
                that.confirmLoading = false
                return
              }
            }
            let httpurl = ''
            let method = ''
            if (!this.model.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'put'
            }
            let formData = Object.assign(this.model, values)
            httpAction(httpurl, formData, method)
              .then((res) => {
                if (res.success) {
                  that.$message.success(res.message)
                  that.$emit('ok')
                } else {
                  that.$message.warning(res.message)
                }
              })
              .finally(() => {
                that.confirmLoading = false
              })
          }
        })
      },
      popupCallback(row) {
        this.form.setFieldsValue(pick(row, 'ipSegsent', 'remarks', 'executeCron'))
      },
    },
  }
</script>
<style lang="less" scoped>
  ::v-deep .two-words>div>label {
    letter-spacing: 4px;
  }

  ::v-deep .two-words>div>label::after {
    letter-spacing: 0px;
  }
</style>