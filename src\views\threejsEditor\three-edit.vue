<template>
  <div style="display: flex; height: 100vh">
    <div width="calc(100% - 300px)" id="threeContent">
      <three-content ref="threeContent" :modelsArr="modelsArr"></three-content>
      <div class="mode-tabbar" v-if="showMode">
        <a-radio-group v-model="threeMode" @change="modeChange" button-style="solid">
          <a-radio-button value="translate"> 平移 </a-radio-button>
          <a-radio-button value="rotate"> 旋转 </a-radio-button>
          <a-radio-button value="scale" v-if="!isGroupModel"> 缩放 </a-radio-button>
        </a-radio-group>
      </div>
      <div class="arrow-info" v-if="showMode">
        <span style="color: #ff0000; margin-right: 12px"><a-icon type="arrow-right" /> X</span>
        <span style="color: #00ff00; margin-right: 12px"><a-icon type="arrow-right" /> Y</span>
        <span style="color: #0000ff"><a-icon type="arrow-right" /> Z</span>
      </div>
      <div class="save-btn">
        <a-button type="primary" @click="saveScene" style="margin-right: 12px">保存</a-button>
        <a-button type="danger" @click="cancelEdit">取消</a-button>
      </div>
      <!-- <div class="clear-btn">
        <a-button type="primary" @click="clearScene">清空</a-button>
      </div> -->
    </div>
    <div style="width: 300px; border-left:1px solid rgba(0,0,0,0.2)">
      <three-toolbar ref="threeToolbar" @delGeo="delGeo" @hideTC="hideTC"></three-toolbar>
    </div>
  </div>
</template>
<script>
import threeContent from './three-content.vue'
import ThreeToolbar from './three-toolbar.vue'
import { httpAction, getAction } from '@/api/manage'
export default {
  components: {
    threeContent,
    ThreeToolbar,
  },
  props: {
    roomId: {
      type: String,
      default: '',
      required: true,
    },
    roomName: {
      type: String,
      default: '',
      required: true,
    },
    threeJson: {
      type: String,
      default: '',
    },
    modelsArr: {
      type: Array,
      default: () => [],
      required: true,
    },
  },
  data() {
    return {
      threeMode: 'translate',
      showMode: false,
      isGroupModel: false,
      roomInfo: {},
      isOk: false,
      bgId:"",
      showContent:false,
    }
  },
  watch: {
    '$store.state.threejs.isGroup'(newv) {
      this.isGroupModel = newv
    },
  },
  created() {
    this.$store.commit('threejs/changeSceneName', this.roomName);
    this.setBgConfig()
  },
  beforeDestroy(){
    this.$store.commit('threejs/SET_BG_CONGIG', null)
  },
  mounted() {},
  methods: {
    //设置背景配置
    setBgConfig(){
      let config = this.modelsArr.find(el=>el.modelCode==="room_bg_config")
      if(config){
        this.bgId = config.id;
        let configData = config.userData;
         this.$store.commit('threejs/SET_BG_CONGIG', configData)
      }
    },
    modeChange(e) {
      this.$refs.threeContent.changeThreeMode(e.target.value)
    },
    getSceneChild(children) {
      let arr = []
      children.forEach((el) => {
        let tem = {
          name: el.name,
          scale: el.scale,
          rotation: el.rotation,
          position: el.position,
          userData: el.userData,
        }
        arr.push(tem)
      })
      this.$ls.set('threeJson', arr)
      return arr
    },
    //保存机房
    saveScene() {
      if (this.$refs.threeContent.transformControls.object) {
        this.hideTC()
      }
      this.$refs.threeToolbar.barType = 1
      let outerMesh = scene.children.find((el) => {
        return el.name === 'yq_outerMesh'
      })
      if (outerMesh) scene.remove(outerMesh)
      let transtformControl = scene.children.find((el) => el.name === 'transformControls')
      if (transtformControl) scene.remove(transtformControl)
      let kc = scene.children.filter((el) => el.name === '')
      kc.forEach((node) => {
        scene.remove(node)
      })
      // 保存时生成模型数据
       let modelsArr = []
      for (let i = 0 ; i< scene.children.length; i++){
        let el = scene.children[i]
        if(el.userData.name === ""){
          this.$message.warning("有模型没有填写名称，无法保存！")
          return;
        }
        if(el.name === "yq_group_jigui"){
          if(el.userData.brand === ""){
            this.$message.warning("有机柜模型没有填写品牌，无法保存！")
            return;
          }
          if(el.userData.unit <= 0){
            this.$message.warning("有机柜模型没有填写容量，无法保存！")
            return;
          }
          if(el.userData.specifications === ""){
            this.$message.warning("有机柜模型没有填写规格，无法保存！")
            return;
          }else if(/^[1-9]\d*\*[1-9]\d*\*[1-9]\d*$/.test(el.userData.specifications) === false){
            this.$message.warning("有机柜模型规格格式不正确，无法保存，例如：长*宽*高，请正确填写！")
            return;
          }
        }
        let modelData = this.dataFactory(el)
        modelsArr.push(modelData)
      }
      modelsArr.push(this.bgFactory())
      // let sj = scene.toJSON()
      let sceneObj = {
        name: this.$store.state.threejs.sceneName,
        info: '', //sj
      }
      var sceneJson = JSON.stringify(sceneObj)
      this.roomInfo['threeDJson'] = sceneJson
      this.roomInfo['id'] = this.roomId
      this.roomInfo['threeDModelList'] = modelsArr
      let loading = this.$loading({
        background: 'rgba(0,0,0,0.6)',
      })
      loading.visible = true;
      httpAction('/topo/room/edit', this.roomInfo, 'put')
        .then((res) => {
          if (res.success) {
            this.$message.success('保存成功')
            this.onCancel()
          } else {
            this.$message.warning(res.message)
          }
          loading.visible = false
        })
        .catch((err) => {
          loading.visible = false
        })
      // localStorage.setItem("scene", sceneJson);
    },
    cancelEdit() {
      this.$confirm({
        // title: 'Confirm',
        content: '是否保存？',
        okText: '保存',
        cancelText: '不保存',
        onOk: this.onOk,
        onCancel: this.onCancel,
      })
      // this.saveScene();
      // this.$router.push("/threeScan");
    },
    onOk() {
      this.saveScene()
    },
    onCancel() {
      this.$emit('hideEditor')
    },
    hideTC() {
      if (this.$refs.threeContent.transformControls.object.name === 'yq_outerMesh') {
        scene.remove(this.$refs.threeContent.transformControls.object)
      }
      this.$refs.threeContent.transformControls.detach(this.$refs.threeContent.transformControls.object)

      this.showMode = false
    },
    delGeo() {
      if (this.$refs.threeContent.transformControls.object.name === 'yq_outerMesh') {
        let groupTarget = scene.children.find((el) => {
          return el.uuid === this.$refs.threeContent.transformControls.object.groupId
        })
        scene.remove(groupTarget)
      }
      scene.remove(this.$refs.threeContent.transformControls.object)
      this.hideTC()
    },
    //整合模型数据
    dataFactory(el) {
      let pos = el.position
      let roa = el.rotation
      let scale = el.scale
      let id = ''
      if (el.userData.uuid) {
        id = el.userData.uuid
      } else {
        id = el.uuid
        el.userData.uuid = el.uuid
      }
      let tem = {
        modelId: el.userData.uuid || el.uuid, //id
        modelCode: el.name, //模型code
        modelType: el.type, //模型类型
        position: JSON.stringify({ x: pos.x, y: pos.y, z: pos.z }), //模型位置
        rotation: JSON.stringify({ x: roa.x, y: roa.y, z: roa.z }), //模型旋转角度
        scale: JSON.stringify({ x: scale.x, y: scale.y, z: scale.z }), //模型大小
        userData: JSON.stringify(el.userData), //模型自定义业务数据
        visible: el.visible, //是否可见
      }
      if (el.userData.dataId) {
        tem.id = el.userData.dataId
      }
      return tem
    },
    //生成背景配置
    bgFactory(){
      let config = this.$store.state.threejs.bgConfig
      // console.log("保存背景配置 === ",config)
       let tem = {
        modelId: "room_bg_config_id",
        modelCode: "room_bg_config",
        modelType: "room_config",
        position: "",
        rotation: "",
        scale: "",
        userData: JSON.stringify(config), 
        visible: true,
      }
      if(this.bgId){
        tem.id = this.bgId
      }
      return tem;
    }
  },
}
</script>
<style scoped>
#threeContent {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  width: calc(100% - 300px);
}
.mode-tabbar {
  position: absolute;
  bottom: 20px;
}
.arrow-info {
  position: absolute;
  bottom: 20px;
  right: 20px;
  height: 32px;
  padding: 0 12px;
  border-radius: 5px;
  line-height: 32px;
  background: #fff;
  font-size: bold;
}
.save-btn {
  position: absolute;
  top: 20px;
  right: 20px;
}
.clear-btn {
  position: absolute;
  top: 20px;
  right: 150px;
}
</style>