<template>
  <a-modal
    :title="title"
    :visible="_listenerParamsShow"
    :width="'50%'"
    :maskClosable='false'
    @cancel='hideListenerParams'
    @ok="save"
  >
    <div class="listener-form-con">
      <a-form :form="listenerParam">
        <a-form-item>
          <span class="listener-form-item-label">名称：</span>
          <div class="listener-form-item-wrap">
            <a-input v-model="listenerParam.name" />
          </div>
        </a-form-item>
        <a-form-item>
          <span class="listener-form-item-label">类型：</span>
          <div class="listener-form-item-wrap">
            <a-select v-model="listenerParam.paramType" :getPopupContainer='node=>node.parentNode'>
              <a-select-option v-for="item in paramTypes" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </div>
        </a-form-item>
        <a-form-item>
          <span class="listener-form-item-label">值：</span>
          <div class="listener-form-item-wrap">
            <a-input v-model="listenerParam.value" />
          </div>
        </a-form-item>
      </a-form>
    </div>
  </a-modal>
</template>

<script>
export default {
  props: {
    listenerParamOuter: {
      type: Object,
      default: {
        id: '',
        name: '',
        paramType: 'string',
        value: '',
      },
    },
    title: {
      type: String,
      default: '',
    },
    listenerParamsShow: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
        isEdit:false,
      listenerParamsInner: [],
      paramType: {
        string: '字符串',
        expression: '表达式',
      },
      paramTypes: [
        { value: 'string', label: '字符串' },
        { value: 'expression', label: '表达式' },
      ],
      listenerParam: null,
    }
  },
  computed: {
    _listenerParamsShow: {
      get() {
        return this.listenerParamsShow
      },
      set(v) {
        this.$emit('changeListenerParams', v)
      },
    },
  },
  //   watch: {
  //     listenerParams(newVal) {
  //       this.listenerParamsInner = newVal
  //     },
  //   },
  created() {
    this.listenerParam = this.listenerParamOuter
  },
  mounted() {},
  methods: {
    hideListenerParams() {
      this._listenerParamsShow = false
    },
    save() {
      this._listenerParamsShow = false
      if(this.isEdit){
          this.$emit('saveListenerParams',false)
      }
      else{
          this.$emit('saveListenerParams', this.listenerParam)
      }
      
    },
  },
}
</script>

<style lang="less" scoped>
.listener-form-con {
  height: 40vh;
  .listener-form-item-label {
    display: inline-block;
    width: 80px;
    text-align: right;
  }
  .listener-form-item-wrap {
    display: inline-block;
    width: calc(100% - 80px);
  }
  .params-title {
    display: flex;
    justify-content: space-between;
  }
  .params-table {
    margin-top: 8px;
  }
  .listener-add {
    color: #1890ff;
    cursor: pointer;
  }
}
</style>