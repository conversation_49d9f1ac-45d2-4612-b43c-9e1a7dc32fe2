<template>
  <a-popover v-model="visible" :arrowPointAtCenter="true" :autoAdjustOverflow="true"
    :overlayStyle="{ width: '360px', top: '50px' }" overlayClassName="header-notice-wrapper" placement="bottomRight"
    trigger="click" @visibleChange="handleHoverChange">
    <template slot="content">
      <a-spin :spinning="loadding">
        <a-tabs>
          <div slot="tabBarExtraContent" style="padding: 6px 0 0 0">
            <yq-icon :key="audioIconKey" :type="iconName" style="font-size: 22px; cursor: pointer; color: #7da1ff"
              @click.native="changeTipStatus" />
          </div>

          <a-tab-pane key="2" :tab="msg2Title">
            <a-list>
              <a-list-item v-for="(record, index) in announcement2" :key="index" @click="showAnnouncement(record)">
                <a style="display: flex; width: 100%; align-items: center">
                  <div style="
                      text-align: center;
                      color: #fff;
                      background: #fff;
                      box-shadow: 0px 0px 3px 1px #c9c9c9;
                      margin-left: 3px;
                      height: 40px;
                    ">
                    <div style="padding: 2px 2px">
                      <div v-if="record.priority === 'L'" style="padding: 8px 11px; background: #7da1ff">低</div>
                      <div v-else-if="record.priority === 'M'" style="padding: 8px 11px; background: #ffa471">中</div>
                      <div v-else-if="record.priority === 'H'" style="padding: 8px 11px; background: #ff7d7d">高</div>
                    </div>
                  </div>
                  <div style="margin-left: 12px; width: 57%">
                    <p style="margin-bottom: 4px">
                      <a>{{ record.titile }}</a>
                    </p>
                    <p style="color: rgba(0, 0, 0, 0.45); margin-bottom: 0px">请进入待办栏，尽快处理！</p>
                  </div>
                  <div>
                    <p v-if="record.createTime" style="margin-bottom: 4px">{{ record.createTime.slice(0, 10) }}</p>
                    <p style="color: rgba(0, 0, 0, 0.45); margin-bottom: 0px" title="请进入待办栏，尽快处理！">
                      请进入待...
                    </p>
                  </div>
                </a>
              </a-list-item>
              <div style="margin-top: 11px; text-align: center">
                <a-button block type="dashed" @click="toMyAnnouncement(routerInfoList[0])">查看更多</a-button>
              </div>
            </a-list>
          </a-tab-pane>

          <a-tab-pane key="3" :tab="alarmTitle">
            <a-list>
              <a-list-item v-for="(items, index) in alarmList" :key="index" @click="showAlarm(items, index)">
                <a style="display: flex; width: 100%; align-items: center">
                  <div style="
                      text-align: center;
                      color: #fff;
                      background: #fff;
                      box-shadow: 0px 0px 3px 1px #c9c9c9;
                      margin-left: 3px;
                      height: 40px;
                      width: 50px;
                    ">
                    <div style="padding: 0 8px; background: #7da1ff">{{ items.level_text }}</div>
                  </div>
                  <div style="margin-left: 12px; width: 57%">
                    <p style="margin-bottom: 4px">
                      <a>{{ items.title }}</a>
                    </p>
                    <p style="color: rgba(0, 0, 0, 0.45); margin-bottom: 0px">请进入待办栏，尽快处理！</p>
                  </div>
                  <div>
                    <p v-if="items.sendTime" style="margin-bottom: 4px">{{ items.sendTime.slice(0, 10) }}</p>
                    <p style="color: rgba(0, 0, 0, 0.45); margin-bottom: 0px" title="请进入待办栏，尽快处理！">
                      请进入待...
                    </p>
                  </div>
                </a>
              </a-list-item>
              <div style="margin-top: 11px; text-align: center">
                <a-button block type="dashed" @click="toMyAnnouncement(routerInfoList[1])">查看更多</a-button>
              </div>
            </a-list>
          </a-tab-pane>

          <a-tab-pane key="4" :tab="msg1Title">
            <a-list>
              <a-list-item v-for="(record, index) in announcement1" :key="index" @click="showAnnouncement(record)">
                <a style="display: flex; width: 100%; align-items: center">
                  <div style="
                      text-align: center;
                      color: #fff;
                      background: #fff;
                      box-shadow: 0px 0px 3px 1px #c9c9c9;
                      margin-left: 3px;
                      height: 40px;
                    ">
                    <div style="padding: 2px 2px">
                      <div v-if="record.priority === 'L'" style="padding: 8px 11px; background: #7da1ff">低</div>
                      <div v-else-if="record.priority === 'M'" style="padding: 8px 11px; background: #ffa471">中</div>
                      <div v-else-if="record.priority === 'H'" style="padding: 8px 11px; background: #ff7d7d">高</div>
                    </div>
                  </div>
                  <div style="margin-left: 12px; width: 57%">
                    <p style="margin-bottom: 4px">
                      <a>{{ record.titile }}</a>
                    </p>
                    <p style="color: rgba(0, 0, 0, 0.45); margin-bottom: 0px">请进入待办栏，尽快处理！</p>
                  </div>
                  <div>
                    <p v-if="record.createTime" style="margin-bottom: 4px">{{ record.createTime.slice(0, 10) }}</p>
                    <p style="color: rgba(0, 0, 0, 0.45); margin-bottom: 0px" title="请进入待办栏，尽快处理！">
                      请进入待...
                    </p>
                  </div>
                </a>
              </a-list-item>
              <div style="margin-top: 11px; text-align: center">
                <a-button block type="dashed" @click="toMyAnnouncement(routerInfoList[3])">查看更多</a-button>
              </div>
            </a-list>
          </a-tab-pane>
        </a-tabs>
      </a-spin>
    </template>
    <span class="header-notice" @click="fetchNotice">
      <audio id="musicTips" ref="musicTips" :autoplay="isAutoPlay" controls muted="true" :src="audioUrl"
        style="width: 0px; height: 0px"></audio>
      <a-badge :count="msgTotal">
        <a-icon type="bell" class="icon-item" />
        <!--        <img alt='' src='~@/assets/message.png' />-->
      </a-badge>
    </span>
    <show-announcement ref="ShowAnnouncement" @ok="modalFormOk"></show-announcement>
    <dynamic-notice ref="showDynamNotice" :formData="formData" :path="openPath" />
  </a-popover>
</template>

<script>
  import {
    getAction,
    putAction
  } from '@/api/manage'
  import ShowAnnouncement from './ShowAnnouncement'
  import store from '@/store/'
  import yqIcon from './SvgIcon'
  import DynamicNotice from './DynamicNotice'
  import {
    WebsocketMixin
  } from '@/mixins/WebsocketMixin'
  import WebsocketMessageMixin from '@/mixins/WebsocketMessageMixin'
  import { setImgAllPath } from '@/utils/imagePathAboutTinymce'
  let curNode = null
  const synth = window.speechSynthesis
  const alarmMsg = typeof SpeechSynthesisUtterance==='undefined'?null:new SpeechSynthesisUtterance();

  export default {
    name: 'HeaderNotice',
    components: {
      yqIcon,
      DynamicNotice,
      ShowAnnouncement,
    },
    mixins: [WebsocketMixin, WebsocketMessageMixin],
    data() {
      return {
        loadding: false,
        url: {
          listCementByUser: '/sys/annountCement/listByUser',
          editCementSend: '/sys/sysAnnouncementSend/editByAnntIdAndUserId',
          queryById: '/sys/annountCement/queryById',
        },
        hovered: false,
        announcement1: [],
        announcement2: [],
        alarmList: [],
        routerInfoList: [{
            routerName: 'isps-userAnnouncement',
            redirect: '/isps/userAnnouncement',
            component: 'system/UserAnnouncementList',
            params: {
              msgCategory: 2
            },
          },
          {
            routerName: 'alarmManage-assetsAlarm-AssetsAlarmManage',
            redirect: '/alarmManage/assetsAlarm/AssetsAlarmManage',
            component: 'alarmManage/assetsAlarm/AssetsAlarmManage',
            params: null,
          },
          {
            routerName: 'downloadManagement-downloadManagementList',
            redirect: '/downloadManagement/downloadManagementList',
            component: 'downloadManagement/downloadManagementList',
            params: null,
          },
          {
            routerName: 'isps-userAnnouncement',
            redirect: '/isps/userAnnouncement',
            component: 'system/UserAnnouncementList',
            params: {
              msgCategory: 1
            },
          }
        ],
        befNoticeCount: 0,
        msg1Count: '0',
        msg2Count: '0',
        alarmCount: '0',
        msg1Title: '通知公告(0)',
        msg2Title: '系统消息(0)',
        alarmTitle: '告警通知(0)',
        stopTimer: false,
        websock: null,
        lockReconnect: false,
        heartCheck: null,
        formData: {},
        openPath: '',
        visible: false,
        noticeTimer: null,

      alarmTimer: null,
      audioUrl: '',
      musicUrl: window._CONFIG['staticDomainURL'],
      iconName: '',
      isAutoPlay: false,
      audioIconKey: 0,
      reTimer:null
    }
  },
  computed: {
    msgTotal() {
      return parseInt(this.alarmCount) + parseInt(this.msg2Count) + parseInt(this.msg1Count)
    },
  },
  created() {
    this.getIconName()
  },
  mounted() {
    curNode = this
    this.addAudio()
    this.loadData()
    // this.timerFun()
  },
  destroyed: function () {
    // 离开页面生命周期函数
    clearTimeout(this.alarmTimer)
    clearInterval(this.noticeTimer)
    this.noticeTimer = null
    curNode.$notification.destroy()
  },
  methods: {
    addAudio() {
      this.audio = document.getElementById('musicTips')
      this.audio.muted = true
      this.audio.pause()
    },
    getIconName() {
      var key = 'headerNotice:musicTips'
      let name = this.$ls.get(key, this.iconName)
      if (name) {
        this.iconName = name
        this.isAutoPlay = this.iconName === 'notice_play' ? true : false
      } else {
        this.iconName = 'notice_play'
        this.isAutoPlay = true
      }
    },
    changeTipStatus() {
      this.audio.pause()
      this.audioUrl = ''
      this.isAutoPlay = !this.isAutoPlay
      if (this.isAutoPlay) {
        this.alarmCancel()
      } else {
        synth.pause()
      }
      this.audio.muted = !this.isAutoPlay
      this.iconName = this.isAutoPlay ? 'notice_play' : 'notice_pause'
      var key = 'headerNotice:musicTips'
      this.$ls.set(key, this.iconName)
      ++this.audioIconKey
    },
    playMusicTips() {
      if (this.isAutoPlay && this.audio && this.audioUrl) {
        this.audio.muted = false
        this.audio.load()
        this.audio.play()
      }
    },
    getAlarmList() {
      sessionStorage.setItem('alarmList', JSON.stringify(store.getters.alarmInfo))
      curNode.alarmList = JSON.parse(sessionStorage.getItem('alarmList'))
      curNode.alarmCount = store.getters.alarmInfo.length
      curNode.alarmTitle = '告警通知(' + store.getters.alarmInfo.length + ')'
    },
    timerFun() {
      this.stopTimer = false
      if (this.noticeTimer === null) {
        this.noticeTimer = setInterval(() => {
          // 停止定时器
          if (this.stopTimer == true) {
            clearInterval(this.noticeTimer)
            this.noticeTimer = null
            return
          }
          this.loadData()
        }, 6000)
      }
    },
    alarmPlay(text) {
      if(alarmMsg){
        alarmMsg.text = text
        alarmMsg.lang = "zh-CN"
        synth.speak(alarmMsg)
      }
    },
    alarmCancel() {
      synth.cancel()
    },
    loadData() {
      if (!!sessionStorage.getItem('alarmList')) {
        this.alarmList = JSON.parse(sessionStorage.getItem('alarmList'))
        if (this.alarmList&&this.alarmList.length>0){
          this.alarmList.forEach((ele) => {
            if (ele.speech == true) {
              let mes = ele.msgContent.replace(/<p>/g, '')
              let msg = mes.replace(/<\/p>/g, '')
              this.alarmPlay(msg)
            }
            ele.msgContent=setImgAllPath(ele.msgContent)
          })
          this.alarmTitle = '告警通知(' + this.alarmList.length + ')'
          this.alarmCount = this.alarmList.length
        }
      }
      try {
        // 获取系统消息
        getAction(this.url.listCementByUser)
          .then((res) => {
            if (res.success) {
              this.announcement1 = res.result.anntMsgList;
              this.msg1Count = res.result.anntMsgTotal;
              this.msg1Title = "通知公告(" + this.msg1Count + ")";

              this.announcement2 = res.result.sysMsgList
              this.msg2Count = res.result.sysMsgTotal
              this.msg2Title = '系统消息(' + this.msg2Count + ')'

              let currNoticeCount = parseInt(this.msg2Count) + parseInt(this.msg1Count)
              if (currNoticeCount > 0 && currNoticeCount > this.befNoticeCount) {
                this.$refs.musicTips.src = require('@/assets/music/daiban.mp3')
                this.audioUrl = '@/assets/music/daiban.mp3'
                this.playMusicTips()
              }
              this.befNoticeCount = parseInt(this.msg2Count) + parseInt(this.msg1Count)
            }
          })
          .catch((error) => {
            this.stopTimer = true
          })
      } catch (err) {
        this.stopTimer = true
      }
    },
    playVoice(record) {
      let currNoticeCount = parseInt(this.msg2Count) + parseInt(this.msg1Count)
      if (currNoticeCount > 0 && currNoticeCount > this.befNoticeCount) {
        this.audioUrl = '@/assets/music/daiban.mp3'
        this.playMusicTips()
      }
      this.befNoticeCount = parseInt(this.msg2Count) + parseInt(this.msg1Count)
    },
    fetchNotice() {
      if (this.loadding) {
        this.loadding = false
        return
      }
      this.loadding = true
      setTimeout(() => {
        this.loadding = false
      }, 200)
    },
    showAlarm(record, index) {
      this.visible = false
      this.hovered = false
      this.alarmCancel()
      if (record.openType === 'component') {
        this.openPath = record.openPage
        this.formData = {
          id: record.busId,
        }
        this.$refs.showDynamNotice.detail(record.openPage)
      } else {
        record.msgContent=setImgAllPath(record.msgContent)
        this.$refs.ShowAnnouncement.detail(record)
      }
      this.alarmList.splice(index, 1)
      this.alarmCount = this.alarmList.length
      this.alarmTitle = '告警通知(' + this.alarmCount + ')'
      sessionStorage.setItem('alarmList', JSON.stringify(this.alarmList))
      store.dispatch('updateDataList', index)
    },
    showAnnouncement(record) {
      this.visible = false
      putAction(this.url.editCementSend, {
        anntId: record.id,
      }).then((res) => {
        if (res.success) {
          this.loadData()
        }
      })
      this.hovered = false
      if (record.openType === 'component') {
        this.openPath = record.openPage
        this.formData = {
          id: record.busId,
        }
        this.$refs.showDynamNotice.detail(record.openPage)
      } else {
        record.msgContent=setImgAllPath(record.msgContent)
        this.$refs.ShowAnnouncement.detail(record)
      }
    },
    toMyAnnouncement(routerInfo) {
      curNode.visible = false
      curNode.$emit('toMyAnnouncement', routerInfo)
    },
    modalFormOk() {},
    handleHoverChange(visible) {
      this.hovered = visible
    },
    /*
    *statusChange 设备在线离线推送消息处理统一在WebsocketMixn.js中处理
    * alarmStatus 设备告警状态推送统一在WebsocketMixn.js中处理
    * alarmClear 清除拓扑图告警统一在WebsocketMixn.js中处理
    */
    websocketOnmessage: function (e) {
      //console.log('heardnotice接收到推送消息 === ', e.data)
      if (e.data === 'HeartBeat') {
        return
      }
      let that = this
      var data = eval('(' + e.data + ')') //解析对象
    /*  if (data.cmd == 'topic') {
        //撤销发送给全体用户的系统消息和通知公告
        //当前用户全部标注为已读
        let idx1 = this.announcement1.findIndex(el=>el.id == data.msgId)
        let idx2 = this.announcement2.findIndex(el=>el.id == data.msgId)
        if(idx1 !== -1){
          this.announcement1.splice(idx1,1);
          this.msg1Count -= 1;
          this.msg1Title = "通知公告(" +  this.msg1Count + ")";
        }else if(idx2 !== -1){
          this.announcement2.splice(idx2,1);
          this.msg2Count -= 1
          this.msg2Title = '系统消息(' + this.msg2Count + ')'
        }else if(data.msgId === undefined){
          this.announcement1 = []
          this.announcement2 = []
          this.msg1Count = 0;
          this.msg2Count = 0;
          this.msg1Title = "通知公告(" +  this.msg1Count + ")";
          this.msg2Title = '系统消息(' + this.msg2Count + ')'
        }
      } else if (data.cmd == 'user') {
        //撤销发送给指定用户的系统消息和通知公告
        let idx1 = this.announcement1.findIndex(el=>el.id == data.msgId)
        let idx2 = this.announcement2.findIndex(el=>el.id == data.msgId)
        if(idx1 !== -1){
          this.announcement1.splice(idx1,1);
          this.msg1Count -= 1;
          this.msg1Title = "通知公告(" +  this.msg1Count + ")";
        }else if(idx2 !== -1){
          this.announcement2.splice(idx2,1);
          this.msg2Count -= 1
          this.msg2Title = '系统消息(' + this.msg2Count + ')'
        }
      }
      else */
        if (data.messageType == 'alarm') {
        //弹窗提示
        if (data.data.prompt) {
          let obj = {
            showTypeIcon: true,
            typeName: 'alarm',
            title: data.data.title,
            content: data.data.msgContent,
            duration: data.data.duration,
            position: data.data.position,
            moreEvent: () => {
              curNode.toMyAnnouncement(curNode.routerInfoList[1])
            },
          }
          curNode.$yq_notification(obj)
        }
        //告警提示音
        if (curNode.alarmTimer) {
          clearTimeout(curNode.alarmTimer)
        }
        curNode.audioUrl = curNode.musicUrl + '/' + data.data.alarmVoice
        curNode.alarmTimer = setTimeout(() => {
          curNode.playMusicTips()
        }, 200)

          //相同告警：新覆盖旧
          let alarmArr = store.getters.alarmInfo
          if (alarmArr.length > 0) {
            alarmArr.forEach((item, index) => {
              if (item.deviceCode === data.data.deviceCode && data.data.title == item.title) {
                alarmArr.splice(index, 1, data.data)
              }
            })
          }
          curNode.getAlarmList()

          //添加新增不同告警
          const flag = alarmArr.some((ele) => {
            return ele.deviceCode === data.data.deviceCode && data.data.title == ele.title
          })
          if (!flag) {
            //异步添加数据
            store.dispatch('addDataList', data.data).then(() => {
              curNode.getAlarmList()
            })
          }
        } else if (data.messageType === 'upDownload') {
          let tempData = data.data
          if (tempData.prompt) {
            let obj = {
              showTypeIcon: true,
              typeName: 'info',
              content: setImgAllPath(tempData.msgContent),
              duration: tempData.duration,
              position: tempData.position,
              moreEvent: () => {
                curNode.toMyAnnouncement(curNode.routerInfoList[2])
              },
            }
            curNode.$yq_notification(obj)
          }
          curNode.$yq_notification(obj)
        }else if (data.messageType === 'authorizationNotice') {
          let tempData = data.data;
          if (tempData.prompt) {
            let obj = {
              showTypeIcon: true,
              typeName: 'info',
              title:"授权即将过期通知",
              content: setImgAllPath(tempData.msgContent),
              duration: tempData.duration,
              position: tempData.position,
              moreEvent: () => {
                curNode.toMyAnnouncement( {
                  routerName: 'system-authorizationAll',
                  redirect: '/system/authorizationAll',
                  component: 'system/authorizationAll',
                  params: null,
                })
              },
            }
            curNode.$yq_notification(obj)
          }
        }else if (data.messageType === 'notice') {
        if(data.data.msgCategory == 1){
          this.announcement1.push(data.data);
          this.msg1Count += 1;
          this.msg1Title = "通知公告(" +  this.msg1Count + ")";
        }else if(data.data.msgCategory == 2){
          this.announcement2.push(data.data);
          this.msg2Count+=1
          this.msg2Title = '系统消息(' + this.msg2Count + ')'
        }
        this.playVoice()
      }else if (data.messageType == 'clearNotice') {
        //撤销发送给全体用户的系统消息和通知公告
          let msgData = data.data;
          if (msgData === undefined) {
            this.announcement1 = []
            this.announcement2 = []
            this.msg1Count = 0;
            this.msg2Count = 0;
            this.msg1Title = "通知公告(" + this.msg1Count + ")";
            this.msg2Title = '系统消息(' + this.msg2Count + ')'
          }else{
            let idx1 = this.announcement1.findIndex(el => el.id == msgData.id)
            let idx2 = this.announcement2.findIndex(el => el.id == msgData.id)
            if (idx1 !== -1) {
              this.announcement1.splice(idx1, 1);
              this.msg1Count -= 1;
              this.msg1Title = "通知公告(" + this.msg1Count + ")";
            } else if (idx2 !== -1) {
              this.announcement2.splice(idx2, 1);
              this.msg2Count -= 1
              this.msg2Title = '系统消息(' + this.msg2Count + ')'
            }
          }
      }
      //系统消息：流程消息
      else if(data.messageType === 'processNotice'){
        if (data.data.prompt) {
          let obj = {
            showTypeIcon: true,
            typeName: 'info',
            title: data.data.title,
            content: data.data.msgContent,
            duration: data.data.duration,
            position: data.data.position,
            moreText: '跳转至',
            moreEvent: () => {
              curNode.toMyAnnouncement({
                routerName:data.data.routerName,
                redirect:data.data.processRouter,
                component:data.data.component,
                params:null
              })
            },
          }
          curNode.$yq_notification(obj)
        }
      } else if(data.messageType === 'systemNotice'){
          if (data.data.prompt) {
            let obj = {
              showTypeIcon: true,
              typeName: 'info',
              title: data.data.title,
              content: data.data.msgContent,
              duration: data.data.duration,
              position: data.data.position,
              showMore: 'none'
            }
            curNode.$yq_notification(obj)
          }
        }
    },

      /*    openNotification(data) {
            var text = data.msgTxt
            const key = `open${Date.now()}`
            this.$notification.open({
              message: '消息提醒',
              placement: 'bottomRight',
              description: text,
              key,
              btn: (h) => {
                return h(
                  'a-button',
                  {
                    props: {
                      type: 'primary',
                      size: 'small',
                    },
                    on: {
                      click: () => this.showDetail(key, data),
                    },
                  },
                  '查看详情'
                )
              },
            })
          },
          showDetail(key, data) {
            this.$notification.close(key)
            var id = data.msgId
            getAction(this.url.queryById, {
              id: id,
            }).then((res) => {
              if (res.success) {
                var record = res.result
                this.showAnnouncement(record)
              }
            })
          },*/
    },
  }
</script>

<style lang='css' scoped>
  .header-notice-wrapper {
    top: 50px !important;
  }

  .icon-item {
    font-size: 15px;
  }

  img {
    width: 14px;
    height: 14px;
  }
</style>
<style lang='less' scoped>
  .header-notice {
    display: inline-block;
    transition: all 0.3s;

    span {
      vertical-align: initial;
    }
  }

  .ant-list-item {
    padding: 17px 0 6px !important;
  }
</style>