<!--
 * @Description: 传入record数据，通过判断record.type，来渲染对应的组件
 * @Author: kcz
 * @Date: 2020-01-02 22:41:48
 * @LastEditors: kcz
 * @LastEditTime: 2021-05-14 17:30:17
 -->
<template>
  <!-- 嵌套的batch组件 -->
  <div v-if="record.type === 'batch'" style="width:100%">
    <KBatch
      :ref="'KBatch'"
      :record="record"
      :value="value"
      :dynamicData="dynamicData"
      :config="config"
      :parentDisabled="record.options.disabled || parentDisabled"
      :parentFormData="getParentFormData()"
      @change="handleChange"
    />
  </div>

  <a-form-model-item
    style="width:100%"
    :class="{ 'evaluation-field-cell': record.options.isEvaluationField }"
    v-else-if="
      [
        'input',
        'textarea',
        'date',
        'time',
        'number',
        'radio',
        'checkbox',
        'select',
        'rate',
        'switch',
        'slider',
        'uploadImg',
        'uploadFile',
        'cascader',
        'treeSelect'
      ].includes(record.type)
    "
    :prop="`domains.${index}.${record.model}`"
    :rules="record.rules"
  >
    <!-- 多行文本 -->
    <a-textarea
      :style="`width:${record.options.width}`"
      v-if="record.type === 'textarea'"
      :autoSize="{
        minRows: record.options.minRows,
        maxRows: record.options.maxRows
      }"
      :disabled="record.options.disabled || parentDisabled"
      :placeholder="record.options.placeholder"
      :allowClear="record.options.clearable"
      :maxLength="record.options.maxLength"
      :rows="4"
      :value="value"
      @change="handleChange($event.target.value)"
    />

    <!-- 单选框 -->
    <a-radio-group
      v-else-if="record.type === 'radio'"
      :options="localDataSource"
      :disabled="record.options.disabled || parentDisabled"
      :placeholder="record.options.placeholder"
      :value="value"
      :checked="value"
      @change="handleChange($event.target.value)"
    />

    <!-- 多选框 -->
    <a-checkbox-group
      v-else-if="record.type === 'checkbox'"
      :options="localDataSource"
      :disabled="record.options.disabled || parentDisabled"
      :placeholder="record.options.placeholder"
      :value="value"
      @change="handleChange"
    />

    <!-- 滑块 -->
    <div
      v-else-if="record.type === 'slider'"
      :style="`width:${record.options.width}`"
      class="slider-box"
    >
      <div class="slider">
        <a-slider
          :disabled="record.options.disabled || parentDisabled"
          :min="record.options.min"
          :max="record.options.max"
          :step="record.options.step"
          :value="value"
          @change="handleChange"
        />
      </div>
      <div class="number" v-if="record.options.showInput">
        <a-input-number
          style="width:100%"
          :disabled="record.options.disabled || parentDisabled"
          :min="record.options.min"
          :max="record.options.max"
          :step="record.options.step"
          :value="value"
          @change="handleChange"
        />
      </div>
    </div>
    
    <component
      v-else
      :style="`width:${record.options.width}`"
      v-bind="componentOption"
      :min="
        record.options.min || record.options.min === 0
          ? record.options.min
          : -Infinity
      "
      :max="
        record.options.max || record.options.max === 0
          ? record.options.max
          : Infinity
      "
      :count="record.options.max"
      :precision="
        record.options.precision > 50 ||
        (!record.options.precision && record.options.precision !== 0)
          ? null
          : record.options.precision
      "
      :record="record"
      :config="config"
      :disabled="record.options.disabled || parentDisabled"
      :parentDisabled="record.options.disabled || parentDisabled"
      :allowClear="record.options.clearable"
      :dynamicData="dynamicData"
      :filterOption="
        record.options.showSearch
          ? (inputValue, option) => {
              return (
                option.componentOptions.children[0].text
                  .toLowerCase()
                  .indexOf(inputValue.toLowerCase()) >= 0
              );
            }
          : false
      "
      :treeData="localDataSource"
      :options="localDataSource"
      :mode="record.options.multiple ? 'multiple' : ''"
      :checked="value"
      :value="value"
      @change="handleChange($event)"
      :is="componentItem"
    ></component>
  </a-form-model-item>
  <!-- 文本 -->
  <a-form-model-item v-else-if="record.type === 'text'">
    <div :style="{ textAlign: record.options.textAlign }">
      <label
        :class="{ 'ant-form-item-required': record.options.showRequiredMark }"
        :style="{
          fontFamily: record.options.fontFamily,
          fontSize: record.options.fontSize,
          color: record.options.color
        }"
        v-text="record.label"
      ></label>
    </div>
  </a-form-model-item>
  <!-- html -->
  <div
    v-else-if="record.type === 'html'"
    v-html="record.options.defaultValue"
  ></div>

  <div v-else>
    <!-- 空 -->
  </div>
</template>
<script>
/*
 * author kcz
 * date 2019-11-20
 */
// import moment from "moment";

import UploadFile from "../../UploadFile";
import UploadImg from "../../UploadImg";
import KDatePicker from "../../KDatePicker";
import KTimePicker from "../../KTimePicker";
import ComponentArray from "../../core/components_use";
const _ = require("lodash/object");

export default {
  name: "KFormItem",
  props: [
    "record",
    "domains",
    "index",
    "value",
    "parentDisabled",
    "dynamicData",
    "config",
    "dataSource",
  ],
  data() {
    return {
      localDataSource: [], // 新增
      parentFormDataCache: null // 缓存父级表单数据
    }
  },
  watch: {
    dataSource: {
      handler(val) {
        this.localDataSource = val || [];
      },
      immediate: true,
      deep: true,
    },
    'record.options.options': {
      handler(val) {
        if (val) {
          this.localDataSource = val;
        }
      },
      deep: true,
    },
  },
  components: {
    UploadImg,
    UploadFile,
    KDatePicker,
    KTimePicker,
    // 使用异步组件解决循环依赖问题
    KBatch: () => import("../batch.vue")
  },
  computed: {
    componentItem() {
      return ComponentArray[this.record.type];
    },
    componentOption() {
      return _.omit(this.record.options, ["defaultValue", "disabled"]);
    }
  },
  mounted() {
    // 组件挂载后尝试更新考察项选项
    this.$nextTick(() => {
      this.updateExaminePointsOptions();
      // 确保当前行数据完整性
      this.ensureRowDataIntegrity();
      // 确保数据源正确设置
      this.ensureDataSourceIntegrity();
      // 处理上传文件组件的URL前缀
      this.processUploadFileUrls();
    });

    // 如果是需要自动获取考察项的选择框，启动定期检查
    if (this.needAutoUpdateExaminePoints()) {
      this.startParentDataWatcher();
    }
  },

  beforeDestroy() {
    // 清理定时器
    if (this.parentDataWatcherTimer) {
      clearInterval(this.parentDataWatcherTimer);
    }
  },
  methods: {
    handleChange(e) {
      let value = e;
      if (e.target) {
        value = e.target.value;
      }
      this.$emit("input", value);

      // 处理changeFunc回调
      this.executeChangeFunc(value);
      // 强制更新父组件，确保数据同步
      this.$nextTick(() => {
        if (this.$parent && this.$parent.handleInput) {
          this.$parent.handleInput();
        }
      });
    },

    // 执行changeFunc回调函数
    executeChangeFunc(value) {
      if (this.record.options && this.record.options.changeFunc) {
        try {
          // 创建模拟的vm对象
          const mockVm = {
            model: this.getCurrentRowData(),
            $parent: this.getParentVm(),
            $emit: this.$emit.bind(this),
            $set: (obj, key, val) => {
              if (obj && typeof obj === 'object') {
                this.$set(obj, key, val);
              }
            },
            $forceUpdate: () => {
              this.$forceUpdate();
              if (this.$parent && this.$parent.$forceUpdate) {
                this.$parent.$forceUpdate();
              }
            }
          };

          // 执行changeFunc
          const changeFuncStr = this.record.options.changeFunc;
          if (typeof changeFuncStr === 'string') {
            const changeFunc = eval(`(${changeFuncStr})`);
            if (typeof changeFunc === 'function') {
              changeFunc(value, this.record.key, mockVm, null);
            }
          } else if (typeof changeFuncStr === 'function') {
            changeFuncStr(value, this.record.key, mockVm, null);
          }
        } catch (error) {
          console.error('执行changeFunc回调时出错:', error);
        }
      }
    },

    // 获取当前行数据
    getCurrentRowData() {
      try {
        if (this.$parent && this.$parent.data && Array.isArray(this.$parent.data)) {
          return this.$parent.data[this.index] || {};
        }
        return { [this.record.model]: this.value };
      } catch (error) {
        return {};
      }
    },

    // 获取父级Vue实例
    getParentVm() {
      let parent = this.$parent;
      while (parent) {
        if (parent.getData || parent.form || parent.model) {
          return parent;
        }
        parent = parent.$parent;
      }
      return this.$parent;
    },

    // 获取父级表单数据
    getParentFormData() {
      try {
        // 向上查找表单实例
        let parent = this.$parent;
        while (parent) {
          if (parent.getData && typeof parent.getData === 'function') {
            return parent.getData();
          }
          if (parent.form && parent.form.getFieldsValue) {
            return parent.form.getFieldsValue();
          }
          parent = parent.$parent;
        }
        return {};
      } catch (e) {
        console.warn('获取父级表单数据失败:', e);
        return {};
      }
    },

    // 启动父级数据监听器
    startParentDataWatcher() {
      this.parentDataWatcherTimer = setInterval(() => {
        try {
          const parentFormData = this.getParentFormData();
          const examinePoints = parentFormData.examinePoints || [];

          // 检查数据是否发生变化
          const currentDataStr = JSON.stringify(examinePoints);
          const cachedDataStr = JSON.stringify(this.parentFormDataCache);

          if (currentDataStr !== cachedDataStr) {
            this.parentFormDataCache = examinePoints;
            this.updateExaminePointsOptions();
          }
        } catch (e) {
          // 静默处理错误，避免控制台噪音
        }
      }, 1000); // 每秒检查一次
    },

    // 检查是否需要自动更新考察项选项
    needAutoUpdateExaminePoints() {
      if (this.record.type !== 'select') {
        return false;
      }

      // 方式1: 通过options中的特殊标识
      if (this.record.options && this.record.options.autoGetExaminePoints) {
        return true;
      }

      // 方式2: 通过linkageData配置判断
      if (this.record.options && this.record.options.linkageData) {
        try {
          const linkageData = JSON.parse(this.record.options.linkageData);
          return linkageData.some(item => item.target); // 只要有target就认为需要联动
        } catch (e) {
          // linkageData解析失败，忽略
        }
      }

      // 方式3: 通过help信息判断（兼容现有配置）
      if (this.record.help && this.record.options) {
        const autoKeywords = this.record.options.autoKeywords || [];
        return autoKeywords.some(keyword => this.record.help.includes(keyword));
      }

      return false;
    },

    // 更新考察项选项
    updateExaminePointsOptions() {
      // 只对需要自动更新的选择框进行处理
      if (this.needAutoUpdateExaminePoints()) {
        try {
          const parentFormData = this.getParentFormData();

          // 获取配置信息
          const options = this.record.options || {};
          let sourceKey = options.examinePointsSource;
          let labelKey = options.examinePointsLabelKey;
          let valueKey = options.examinePointsValueKey;

          // 如果有linkageData配置，优先使用linkageData中的配置
          if (options.linkageData) {
            try {
              const linkageData = JSON.parse(options.linkageData);
              if (linkageData.length > 0) {
                const linkConfig = linkageData[0];
                sourceKey = linkConfig.target || sourceKey;
                labelKey = linkConfig.targetKey || labelKey;
                valueKey = linkConfig.valueKey || linkConfig.targetKey || valueKey;
              }
            } catch (e) {
              console.warn('解析linkageData失败:', e);
            }
          }

          // 如果仍然没有配置，则不进行自动更新
          if (!sourceKey || !labelKey) {
            console.warn('缺少必要的配置: sourceKey 或 labelKey');
            return;
          }

          // 如果没有指定valueKey，使用labelKey作为默认值
          if (!valueKey) {
            valueKey = labelKey;
          }

          // 从父级表单数据中获取数据源
          const sourceData = parentFormData[sourceKey] || [];

          if (sourceData.length > 0) {
            const optionsList = sourceData.map((item, index) => {
              // 获取标签值
              let label = item[labelKey];
              if (!label && label !== 0) {
                label = `${options.defaultLabelPrefix || '未命名项'}${index + 1}`;
              }

              // 获取值
              let value = item[valueKey];
              if (!value && value !== 0) {
                value = item[labelKey] || `${options.defaultValuePrefix || 'unnamed'}_${item.key || item.id || index}`;
              }

              return {
                label: String(label),
                value: String(value)
              };
            }).filter(option => option.label && option.value); // 过滤掉无效选项

            // 只更新本地数据源，避免触发无限循环
            this.localDataSource = optionsList;
          } else {
            // 如果没有数据，清空选项
            this.localDataSource = [];
          }
        } catch (e) {
          console.warn('更新选项失败:', e);
        }
      }
    },
    // 确保数据源完整性
    ensureDataSourceIntegrity() {
      // 如果是选择类型的字段但没有数据源，尝试从多个来源获取
      if (['select', 'radio', 'checkbox'].includes(this.record.type)) {
        if ((!this.localDataSource || this.localDataSource.length === 0) &&
            (!this.dataSource || this.dataSource.length === 0)) {

          // 优先级1: 检查静态选项配置
          if (this.record.options && this.record.options.staticOptions && this.record.options.staticOptions.length > 0) {
            this.localDataSource = this.record.options.staticOptions;
          }
          // 优先级2: 从父级batch组件获取数据源
          else {
            let parentBatch = this.$parent;
            while (parentBatch && !parentBatch.dataSources) {
              parentBatch = parentBatch.$parent;
            }

            if (parentBatch && parentBatch.dataSources && parentBatch.dataSources[this.record.key]) {
              this.localDataSource = parentBatch.dataSources[this.record.key];
            }
            // 优先级3: 使用字段配置中的选项
            else if (this.record.options && this.record.options.options) {
              this.localDataSource = this.record.options.options;
            }
            // 优先级4: 如果有动态配置，触发父级重新初始化
            else if (this.record.options && this.record.options.dynamic && parentBatch) {
              parentBatch.initOptions(this.record);
              // 延迟获取数据源
              setTimeout(() => {
                if (parentBatch.dataSources[this.record.key]) {
                  this.localDataSource = parentBatch.dataSources[this.record.key];
                  this.$forceUpdate();
                }
              }, 500);
            }
            // 最后: 设置空数组并警告
            else {
              this.localDataSource = [];
            }
          }
        }
      }
    },

    // 确保当前行数据完整性
    ensureRowDataIntegrity() {
      try {
        // 获取当前行数据
        const currentRowData = this.getCurrentRowData();
        if (!currentRowData || typeof currentRowData !== 'object') {
          return;
        }

        // 获取父级batch组件的所有字段配置
        let parentBatch = this.$parent;
        while (parentBatch && !parentBatch.record?.list) {
          parentBatch = parentBatch.$parent;
        }

        if (parentBatch && parentBatch.record && parentBatch.record.list) {
          let hasChanges = false;
          const missingFields = [];
          // 确保当前行包含所有字段
          parentBatch.record.list.forEach(fieldConfig => {
            if (!(fieldConfig.model in currentRowData)) {
              if (fieldConfig.type === 'batch') {
                currentRowData[fieldConfig.model] = fieldConfig.options.defaultValue || [];
              } else {
                currentRowData[fieldConfig.model] = fieldConfig.options.defaultValue;
              }
              hasChanges = true;
              missingFields.push({
                field: fieldConfig.model,
                type: fieldConfig.type,
                defaultValue: fieldConfig.options.defaultValue
              });
            }
          });
          // 如果有变化，触发更新
          if (hasChanges && parentBatch.handleInput) {
            parentBatch.handleInput();
          }
        }
      } catch (error) {
        console.error('确保行数据完整性时出错:', error);
      }
    },
    validationSubform() {
      let verification
      if(this.$refs.KBatch && Array.isArray(this.$refs.KBatch) && this.$refs.KBatch.length>0){
        this.$refs.KBatch.forEach(item=>{
          if(item.validationSubform){
            verification = item.validationSubform()
          }
        })
      }else if(this.$refs.KBatch){
        verification = this.$refs.KBatch.validationSubform()
      }else{
        verification = true
      }
      return verification
    },

    // 处理上传文件组件的URL前缀（修复动态表格中上传文件地址前缀问题）
    processUploadFileUrls() {
      try {
        let options = this.record.options
        let getAccessToken = this.$getAccessToken

        // 上传文件 图片组件 设置token
        if(options && options.headers && getAccessToken && typeof getAccessToken === 'function'){
          options.headers['X-Access-Token'] = getAccessToken()
        }

        // 上传文件 图片组件 拼接路径（与KFormItem保持一致）
        if(["uploadImg","uploadFile"].includes(this.record.type) && options ){
          if(options.action && !options.action.startsWith("http")){
            if(window._CONFIG && window._CONFIG['domianURL']){
              options.action = window._CONFIG['domianURL'] + options.action;
            }else{
              options.action = document.location.origin + options.action
            }
          }
          if(options.downloadImageUrl && !options.downloadImageUrl.startsWith("http")){
            if(window._CONFIG && window._CONFIG['domianURL']){
              options.downloadImageUrl = window._CONFIG['domianURL'] + options.downloadImageUrl;
            }else{
              options.downloadImageUrl = document.location.origin + options.downloadImageUrl
            }
          }
          if(options.downloadFileUrl && !options.downloadFileUrl.startsWith("http")){
            if(window._CONFIG && window._CONFIG['domianURL']){
              options.downloadFileUrl = window._CONFIG['domianURL'] + options.downloadFileUrl;
            }else{
              options.downloadFileUrl = document.location.origin + options.downloadFileUrl
            }
          }
        }
      } catch (error) {
        console.error('处理上传文件URL时出错:', error);
      }
    },
  }
};
</script>
<style lang="less" scoped>
.slider-box {
  display: flex;
  > .slider {
    flex: 1;
    margin-right: 16px;
  }
  > .number {
    width: 70px;
  }
}

/* 评估指标单元格样式 */
.evaluation-field-cell {
  position: relative;
  background: linear-gradient(90deg, rgba(24, 144, 255, 0.03) 0%, rgba(24, 144, 255, 0.01) 100%);
  border-radius: 4px;
}

.evaluation-field-cell::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(180deg, #1890ff 0%, #40a9ff 100%);
  border-radius: 1px;
}
</style>
