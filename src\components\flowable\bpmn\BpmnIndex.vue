<template>
  <div class="content with-diagram" ref="jsDropZone" id="js-drop-zone">
    <div class="canvas" id="js-canvas" ref="canvas"></div>
    <div class="properties-panel-parent" id="js-properties-panel"></div>
  </div>
</template>
 <script>
// 引入相关的依赖   bpmn-js高版本使用
import BpmnModeler from 'bpmn-js/lib/Modeler'
import { BpmnPropertiesPanelModule, BpmnPropertiesProviderModule } from 'bpmn-js-properties-panel'
import { xmlStr } from './resources/xmlStr' // 这里是直接引用了xml字符串
export default {
  name: '',
  components: {},
  // 生命周期 - 创建完成（可以访问当前this实例）
  created() {},
  // 生命周期 - 载入后, Vue 实例挂载到实际的 DOM 操作完成，一般在该过程进行 Ajax 交互
  mounted() {
    this.init()
    let a = document.getElementById('js-drop-zone')
    this.registerFileDrop(a, this.createNewDiagram)
  },
  data() {
    return {
      // bpmn建模器
      bpmnModeler: null,
      container: null,
      canvas: null,
    }
  },
  methods: {
    init() {
      // 获取到属性ref为“canvas”的dom节点
      const canvas = this.$refs.canvas
      // 建模
      this.bpmnModeler = new BpmnModeler({
        container: canvas,
        //添加控制板
        propertiesPanel: {
          parent: '#js-properties-panel',
        },
        additionalModules: [
          // 左边工具栏以及节点
          BpmnPropertiesProviderModule,
          // 右边的工具栏
          BpmnPropertiesPanelModule,
        ],
        // moddleExtensions: {
        //   camunda: camundaModdleDescriptor,
        // },
      })
      this.createNewDiagram()
    },
    registerFileDrop(container, callback) {
      let that = this
      function handleFileSelect(e) {
        e.stopPropagation()
        e.preventDefault()

        var files = e.dataTransfer.files

        var file = files[0]

        var reader = new FileReader()

        reader.onload = function (e) {
          var xml = e.target.result

          callback(xml)
        }

        reader.readAsText(file)
      }

      function handleDragOver(e) {
        e.stopPropagation()
        e.preventDefault()

        e.dataTransfer.dropEffect = 'copy' // Explicitly show this is a copy.
      }

      container.addEventListener('dragover', handleDragOver, false)
      container.addEventListener('drop', handleFileSelect, false)
    },
    createNewDiagram() {
      try {
         this.bpmnModeler.importXML(xmlStr)
      } catch (err) {
       

        console.error(err)
      }
    },
    success() {
      // console.log('创建成功!')
    },
  },
}
</script>

<style scoped lang="less">
.content {
  position: absolute;
  background: url('../../../assets/bg.svg');
  background-color: #ffffff;
  width: calc(100% - 240px);
  height: calc(100% - 59px - 50px - 32px);
}
.canvas {
  width: 100%;
  height: 100%;
}
.panel {
  position: absolute;
  right: 0;
  top: 0;
  width: 300px;
}
</style>