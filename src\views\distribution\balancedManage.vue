<template>
  <div style="height:100%">
    <component style="height:100%" :is="pageName" :data="data" />
  </div>
</template>
<script>
  import balancedDetail from './modules/balancedDetail'
  import balancedList from './balancedList'
  export default {
    name: "balancedManage",
    data() {
      return {
        isActive: 0,
        data: {},
      };
    },
    components: {
      balancedDetail,
      balancedList
    },
    created() {
      this.pButton1(0);
    },
    //使用计算属性
    computed: {
      pageName() {
        switch (this.isActive) {
          case 0:
            return "balancedList";
          default:
            return "balancedDetail";
        }
      }
    },
    methods: {
      pButton1(index, item) {
        this.isActive = index;
        this.data = item;
      },
      pButton2(index, item) {
        this.isActive = index;
        this.data = item
      }
    }
  }
</script>