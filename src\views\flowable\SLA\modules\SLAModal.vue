<template>
  <j-modal
    :title='title'
    :width='width'
    :centered='true'
    :visible='visible'
    :destroyOnClose='true'
    switchFullscreen
    cancelText='关闭'
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @ok='handleOk'
    @cancel='handleCancel'
  >
    <a-spin :spinning='confirmLoading'>
      <j-form-container :disabled='disableSubmit'>
        <a-form-model ref='form' :model='model' :rules='validatorRules' slot='detail'  :labelCol='labelCol' :wrapperCol='wrapperCol'>
          <a-row>
            <a-col :span='24'>
              <a-form-model-item label='所属流程' prop='processKey'>
                <a-select placeholder="请选择所属流程" v-model="model.processKey"
                          :getPopupContainer="(node) => node.parentNode" :allowClear="true" :show-search='true'
                          option-filter-prop='label' @change='changeProcess'>
                  <a-select-option v-for="item in flowList" :label='item.name' :text='item.name' :key="item.key"
                                   :value="item.key">{{ item.name }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
              <a-form-model-item label='SLA类型' prop='slaType' >
                <a-select placeholder="请选择SLA类型" v-model="model.slaType"
                          :getPopupContainer="(node) => node.parentNode" :allowClear="true" :show-search='true'
                          option-filter-prop='label'>
                  <a-select-option v-for="item in slaTypeList" :label='item.text' :key="item.value"
                                   :value="item.value">{{ item.text }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span='24' v-for='(item,index) in priorityLevelList' :key='"priority_"+index'>
              <a-form-model-item
                :ref='"priority_"+index'
                :auto-link='false'
                :label='"优先级"+item.text'
                :prop="'priority['+index+']'"
                :rules='[{required: true,validator:validatePriority}]'
              >
                <div v-model='model.priority[index]' style='display: flex;flex-flow: row nowrap;justify-content: space-between;align-items: center'>
                  <div style='width: 60%;padding-right: 24px'>
                    <a-input-number
                      :min="0"
                      :precision='0'
                      placeholder="请输入天"
                      v-model='model.priority[index].day'
                      style='width:calc(100% - 24px)'
                      @change='changeDay($event,index)'/>
                    <span style='display: inline-block;width: 24px;padding-left: 8px'>天</span>
                  </div>
                  <div style='width: 40%'>
                    <a-select style='width: calc(100% - 36px)' placeholder="请选择小时" v-model="model.priority[index].hour"
                                  :getPopupContainer="(node) => node.parentNode" :allowClear="true" :show-search='true'
                                  option-filter-prop='label' @change='changeHour($event,index)'>
                      <a-select-option v-for="h in item.hourList" :label='h.label' :key="'hour_'+h.value" :disabled='h.disable'
                                           :value="h.value">{{ h.label }}
                      </a-select-option>
                    </a-select>
                    <span style='display: inline-block;width: 36px;padding-left: 8px'>小时</span>
                  </div>
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </j-form-container>
    </a-spin>
  </j-modal>
</template>
<script>
import { getAction, httpAction } from '@api/manage'
import { ajaxGetDictItems, getDictItemsFromCache } from '@api/api'
import operation from 'ant-design-vue/lib/transfer/operation'

export default {
  name: 'SLAModal',
  props:{
    priorityList:{
      type:Array,
      required:true
    }
  },
  /*  computed: {
    operation() {
      return operation
    }
  },*/
  data() {
    return {
      title: '',
      width: '800px',
      disableSubmit: false,
      visible: false,
      confirmLoading: false,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      model: {
        priority: [],
        processKey:undefined,
        slaType:undefined
      },
      flowList: [],
      slaTypeList: [],
      hourList: [],
      priorityLevelList:[],
      validatorRules: {
        processKey: [
          { required: true, message: '请选择所属流程！' }
        ],
        slaType: [
          { required: true, message: '请选择SLA类型！' }
        ]
      },
      url: {
        add: '/sla/level/protocol/add',
        edit: '/sla/level/protocol/edit',
        process: '/flowable/processDefinition/listMyself'
      },
    }
  },
  watch:{
    priorityList:{
      handler(list){
        this.priorityLevelList=list
        this.model.priority=[]
        for (let i = 0; i < this.priorityLevelList.length; i++) {
          let hourList = []
          for (let i = 0; i < 24; i++) {
            let obj = {
              disable: false,
              value: i,
              label: i,
            }
            hourList.push(obj)
          }
          this.priorityLevelList[i]["hourList"] = hourList
          let obj = { day: null, hour: undefined, level: this.priorityLevelList[i].value }
          this.model.priority.push(obj)
        }
      },
      deep:true,
      immediate:true
    }
  },
  created() {
    this.loadProcessData()
    this.initDictData('slaTypeList', 'slaType')
  },
  methods: {
    add() {
      this.edit({
        id:'',
        processKey:undefined,
        slaType:undefined,
        processName:'',
      })
    },
    edit(record) {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.form.clearValidate()
        for (let i = 0; i < this.model.priority.length; i++) {
          this.model.priority[i].day = null
          this.model.priority[i].hour = undefined
          this.priorityLevelList[i].hourList[0].disable = false
        }
        this.model.id=record.id
        this.model.processKey =record.processKey
        this.model.slaType =record.slaType
        this.model.processName = record.processName

        if (record.levelData && record.levelData.length > 0) {
          let levelData = JSON.parse(record.levelData)
          let keys = Object.keys(levelData)
          for (let i = 0; i < keys.length; i++) {
            for (let k = 0; k < this.model.priority.length; k++) {
              if (keys[i] == this.model.priority[k].level) {
                let arr0 = levelData[keys[i]].split('d')
                let day = arr0[0]
                let hour = arr0[1].slice(0, arr0[1].length - 1)
                this.model.priority[k].day = day
                this.model.priority[k].hour = hour
                if (day <= 0) {
                  this.priorityLevelList[k].hourList[0].disable = true
                }
              }
            }
          }
        }
      })
    },
    close() {
      this.visible = false
    },
    handleOk() {
      let that = this
      that.$refs.form.validate((err, values) => {
        if (err && !that.confirmLoading) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!that.model.id) {
            httpurl += that.url.add
            method = 'post'
          } else {
            httpurl += that.url.edit
            method = 'put'
          }
          let formData = JSON.parse(JSON.stringify(that.model))
          formData.levelData=JSON.stringify(this.setPriorityData())
          delete formData.priority
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
                that.close()
              } else {
                that.$message.warning(res.message)
              }
              that.confirmLoading = false
            }).catch((err) => {
            that.$message.warning(err.message)
            that.confirmLoading = false
          })
        }
      })
    },
    setPriorityData(){
      let obj={}
      for (let i=0;i<this.model.priority.length;i++){
        let data=this.model.priority[i]
        if (data.day!=null&data.hour!=undefined){
         let key=data.level
          obj[key]=data.day+'d'+data.hour+"h"
        }
      }
      return obj
    },
    handleCancel() {
      this.close()
    },
    /*获取所属流程下拉数据*/
    loadProcessData() {
      getAction(this.url.process, { pageNo: 1, pageSize: 10000 }).then((res) => {
        if (res.success) {
          this.flowList = res.result.records || res.result
        } else {
          this.$message.warning(res.message)
        }
      }).catch((err) => {
        this.$message.warning(err.message)
      })
    },
    /*获取字典数据*/
    initDictData(dictOption, dictCode) {
      if (dictCode != null && dictCode != '') {
        /*//优先从缓存中读取字典配置
        if (getDictItemsFromCache(dictCode)) {
          this[dictOption] = getDictItemsFromCache(dictCode)
          return
        }
*/
        //根据字典Code, 初始化字典数组
        ajaxGetDictItems(dictCode, null).then((res) => {
          if (res.success) {
            this[dictOption] = res.result
          }
        })
      }
    },
    changeProcess(e, option) {
      this.model.processName = ''
      if (e) {
        this.model.processName = option.componentOptions.propsData.label
      }
    },
    changeDay(e, index) {
      let v = e + ''
      if (v && e > 0) {
        this.priorityLevelList[index].hourList[0].disable = false
      } else if (v && e == 0) {
        if (this.model.priority[index].hour == 0) {
          this.model.priority[index].hour = undefined
        }
        this.priorityLevelList[index].hourList[0].disable = true
      }

      this.linkageVerification(index)
    },
    changeHour(e, index) {
      this.linkageVerification(index)
    },
    /*优先级联动校验*/
    linkageVerification(curIndex){
      this.$nextTick(() => {
        let com = "priority_" + curIndex
        this.$refs[com][0].onFieldChange()

        for (let i=0;i<this.model.priority.length;i++){
          if (i!==curIndex&&this.model.priority[i].day!=null&&this.model.priority[i].hour!=undefined){
            let com1 = "priority_" + i
            this.$refs[com1][0].onFieldChange()
          }
        }
      })
    },
    /*校验阈值*/
    validatePriority(rule, value, callback) {
      if (rule.required) {
        let day = value.day
        let hour = value.hour
        if (day == null && hour == undefined) {
          callback('请输入天数，并选择小时数')
        } else if (day == null && hour != undefined) {
          callback('请输入天数')
        } else if (day != null && hour == undefined) {
          callback('请选择小时数')
        } else {
          let index = -1
          for (let i = 0; i<this.model.priority.length; i++) {
            if (this.model.priority[i].level === value.level) {
              index = i
              break
            }
          }
          let tips =false
          let currValue = value.day * 24 + parseInt(value.hour)
          for (let i = 0; i < this.model.priority.length; i++) {
            if (this.model.priority[i].day != null && this.model.priority[i].hour != undefined) {
              let tempValue = this.model.priority[i].day * 24 + parseInt(this.model.priority[i].hour)
              if ((currValue >= tempValue&&index<i) || (index >i && currValue <= tempValue)) {
                tips = true
                break
              }
            }
          }
          if (tips){
            callback('优先级取值应从小到大')
          }else{
            callback()
          }
        }
      } else {
        callback()
      }
    }
  }
}
</script>
<style scoped lang='less'>
@import '~@assets/less/normalModal.less';
</style>