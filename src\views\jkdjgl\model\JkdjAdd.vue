<template>
  <j-modal :title='title' :width='width' :visible='visible' :destroyOnClose='true'
           :okButtonProps="{ class:{'jee-hidden': disableSubmit} }" :centered='true' switchFullscreen @ok='handleOk'
           @cancel='handleCancel'
           cancelText='关闭'>
    <a-spin :spinning='confirmLoading'>
      <j-form-container :disabled='disableSubmit'>
        <a-form-model ref='form' slot='detail' :model='jkdjForm' :rules='validatorRules' :labelCol='labelCol'
                      :wrapperCol='wrapperCol'>
          <a-row>
            <a-col :span='24'>
              <a-form-model-item label='对接名称' prop='systemName'>
                <a-input style='width: 100%' v-model='jkdjForm.systemName' :allow-clear='true' autocomplete='off'
                         placeholder='请输入对接名称' />
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
              <a-form-model-item label='对接系统标识' prop='systemCode'>
                <a-input style='width: 100%' v-model='jkdjForm.systemCode' :allow-clear='true' autocomplete='off'
                         placeholder='请输入对接系统标识' />
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
              <a-form-model-item label='采集模式' prop='collectMode'>
                <a-radio-group v-model='jkdjForm.collectMode'>
                  <a-radio :value='0'>
                    推模式
                  </a-radio>
                  <a-radio :value='1'>
                    拉模式
                  </a-radio>
                </a-radio-group>
              </a-form-model-item>
            </a-col>
            <template v-if='jkdjForm.collectMode === 1'>
              <a-col :span='24'>
                <a-form-model-item label='任务名称' prop='abutmentTask.taskName'
                                   :rules="[
                                     { required: true, message: '请输入任务名称' },
                                     {min: 1,message: '名称长度应在1-50之间！'},
                                     {max: 50,message: '名称长度应在1-50之间！'}]">
                  <a-input style='width: 100%'
                           v-model='jkdjForm.abutmentTask.taskName'
                           :allow-clear='true'
                           autocomplete='off'
                           placeholder='请输入任务名称' />
                </a-form-model-item>
              </a-col>
              <a-col :span='24' v-if='jkdjForm.id && jkdjForm.abutmentTask.systemId'>
                <a-form-model-item  label='监控系统ID' prop='abutmentTask.systemId'
                                   :rules="[{ required: false, message: '请输入监控系统ID' }]">
                  <a-input style='width: 100%' v-model='jkdjForm.abutmentTask.systemId' :allow-clear='true'
                           autocomplete='off'
                           disabled
                           placeholder='请输入监控系统ID' />
                </a-form-model-item>
              </a-col>
              <a-col :span='24'>
                <a-form-model-item label='网关' prop='abutmentTask.gatewayCode' :rules="[{required: true,message: '请选择网关！'}]">
                  <a-select v-model='jkdjForm.abutmentTask.gatewayCode' style='width: 100%' allow-clear
                            placeholder='请选择网关'  :options='gatewayCodes' >
<!--                    <a-select-option v-for='item in gatewayCodes' :key='item.value' >-->
<!--                      {{ item.label }}-->
<!--                    </a-select-option>-->
                  </a-select>
                </a-form-model-item>
              </a-col>
              <a-col :span='24'>
                <a-form-model-item label='是否启用"' prop='abutmentTask.isEnable'>
                  <a-switch checked-children='是' un-checked-children='否' v-model='jkdjForm.abutmentTask.isEnable' />
                </a-form-model-item>
              </a-col>
              <a-col :span='24'>
                <a-form-model-item label='执行频率"' prop='abutmentTask.executeCron' :rules="[{required: true,message: '请选择执行频率！'}]">
                  <j-cron ref='executeCron' v-model='jkdjForm.abutmentTask.executeCron'
                          @change='setCorn'></j-cron>
                </a-form-model-item>
              </a-col>
              <a-col :span='24'>
                <a-form-model-item label='请求类型' prop='abutmentTask.requestType' :rules="[{required: true,message: '请选择请求类型！'}]">
                  <a-select v-model='jkdjForm.abutmentTask.requestType' style='width: 100%' allow-clear
                            placeholder='请选择请求类型' :options='requestTypes'>

                  </a-select>
                </a-form-model-item>
              </a-col>
              <a-col :span='24'>
                <a-form-model-item label='请求路径' prop='abutmentTask.requestUrl'
                                   :rules="[
                                     {required: true,message: '请输入请求路径！'}
                                     ]">
                  <a-input style='width: 100%' v-model='jkdjForm.abutmentTask.requestUrl' :allow-clear='true'
                           autocomplete='off'
                           placeholder='请输入请求路径 ' >
                    <a-tooltip slot="suffix" title="路径格式如：http://*************:8091/insight-api/abutment/task/testAbutmentPull?dataType={dataType}">
                      <a-icon type="info-circle" style="color: #0ABBF6" />
                    </a-tooltip>
                  </a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span='24'>
                <a-form-model-item label='请求参数'
                                   prop='abutmentTask.requestParam'
                                   :rules="[
                                     {required: true,message: '请输入请求参数！'},
                                      {min: 1, message: '称长度应在1-200之间！'},
                                      {max: 200,message: '长度应在1-200之间！'}
                                   ]">
                  <a-textarea placeholder='请输入请求参数，如：{ dataType:"device"}' :rows='2' v-model='jkdjForm.abutmentTask.requestParam' allow-clear >
                  </a-textarea>
                </a-form-model-item>
              </a-col>
            </template>
            <!--            <a-col :span='24'>-->
            <!--              <a-form-model-item label='数据类型' prop='dataType'>-->
            <!--                <a-select v-model='jkdjForm.dataType' style="width: 100%" allow-clear placeholder='请选择数据类型' @change='dataTypeChange'>-->
            <!--                  <a-select-option :value="item.value" V-for='(item, index) in datatypeList' :key='index'>-->
            <!--                 {{item.text}}-->
            <!--                  </a-select-option>-->
            <!--                </a-select>-->
            <!--              </a-form-model-item>-->
            <!--            </a-col>-->
            <a-col :span='24' v-if='jkdjForm.collectMode === 0'>
              <a-form-model-item label='服务地址' prop='urlPrefix'
                                 :rules="[
                                   {required: true,message: '请输入服务地址！'},
                                 { validator: validateIP, trigger: 'blur' }]"
              >
                <a-input style='width: 100%' v-model='jkdjForm.urlPrefix' :allow-clear='true' autocomplete='off'
                         placeholder='请输入服务地址' >
                  <a-tooltip slot="suffix" title="格式如：***************:800">
                    <a-icon type="info-circle" style="color: #0ABBF6" />
                  </a-tooltip>
                </a-input>
              </a-form-model-item>
            </a-col>

            <a-col :span='24'>
              <a-form-model-item v-if='jkdjForm.id' label='身份识别码' prop='accessToken'>
                <a-input style='width: 100%' disabled v-model='jkdjForm.accessToken' :allow-clear='true'
                         autocomplete='off'
                         placeholder='请输入身份识别码' />
              </a-form-model-item>
            </a-col>

            <a-col :span='24' v-if='jkdjForm.collectMode === 0'>
              <a-form-model-item v-if='jkdjForm.id && interface' label='接口路径'>
                <a-textarea style='width: 100%' disabled v-model='interface' :allow-clear='true' autocomplete='off'
                            placeholder='请输入接口路径' />
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
              <a-form-model-item label='描述' prop='description'>
                <a-textarea style='width: 100%' v-model='jkdjForm.description' :autoSize='{minRows:2,maxRows:4}'
                            :allow-clear='true' autocomplete='off' placeholder='请输入描述' />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>

      </j-form-container>
    </a-spin>
  </j-modal>
</template>

<script>

import {
  httpAction,
  getAction
} from '@api/manage'
import { ajaxGetDictItem } from '@api/api'

export default {
  name: 'JkdjAdd',
  data() {
    return {
      title: '',
      width: '800px',
      visible: false,
      disableSubmit: false,
      confirmLoading: false,
      labelCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 5
        }
      },
      wrapperCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 16
        }
      },
      jkdjForm: {},
      interface: '',
      validatorRules: {
        systemName: [
          {
            required: true,
            message: '请输入对接名称！'
          },
          {
            min: 1,
            message: '对接名称长度应在1-36之间！'
          },
          {
            max: 36,
            message: '对接名称长度应在1-36之间！'
          }
        ],
        systemCode: [{
          required: true,
          message: '请输入对接系统标识！'
        },
          {
            min: 1,
            message: '对接系统标识长度应在1-36之间！'
          },
          {
            max: 36,
            message: '对接系统标识长度应在1-36之间！'
          }
        ],
        description: [
          {
            min: 0,
            message: '描述长度应在0-255之间！'
          },
          {
            max: 255,
            message: '描述长度应在0-255之间！'
          }
        ],
      },
      url: {
        add: '/abutment/system/add',
        edit: '/abutment/system/edit'
      },
      datatypeList: [],
      gatewayCodes:[],
      requestTypes:[{ label: 'GET', value: 'GET' }, { label: 'POST', value: 'POST' }],
    }
  },
  created() {
    this.getGatewayCodes();
    this.jkdjForm = this.initFormData()
    // this.getDatatype()
  },
  methods: {
    getGatewayCodes(){
      getAction("/configureBack/task/getGatewayList").then(res=>{
        if(res.success){
          this.gatewayCodes = res.result.map(el=>{return {label:el.name,value:el.deviceCode}})
        }
      })
    },
    initFormData() {
      return {
        systemName: '',
        systemCode: '',
        accessToken: '',
        collectMode: 0,// 0 推 1拉
        abutmentTask: this.initAbutmentTask(),
        description: '',
        dataType: '',
        urlPrefix: ''
      }
    },
    initAbutmentTask(){
      return {
        taskName: '',
        systemId: '',
        gatewayCode: undefined,
        isEnable: true,
        executeCron: '0 0 0 * * ? *',
        requestType: "GET",
        requestUrl: '',
        requestParam: ''
      }
    },
    add() {
      this.edit({})
    },
    edit(record) {
      if(!record.abutmentTask){
        record.abutmentTask = this.initAbutmentTask();
      }else{
        record.abutmentTask.isEnable = Boolean(Number(record.abutmentTask.isEnable))
        console.log("record.abutmentTask.isEnable",record.abutmentTask.isEnable)
      }
      this.visible = true
      this.$nextTick(() => {
        this.jkdjForm = Object.assign(this.jkdjForm, record)
        this.interface = this.jkdjForm.requestUrl
      })
    },
    close() {
      this.jkdjForm = this.initFormData()
      this.visible = false
    },
    handleOk() {
      const that = this
      // console.log("喀喀喀 === >", that.jkdjForm)
      that.$refs.form.validate((err, value) => {
        if (err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!that.jkdjForm.id) {
            httpurl += that.url.add
            method = 'post'
          } else {
            httpurl += that.url.edit
            method = 'put'
          }
          if(that.jkdjForm.abutmentTask){
            this.jkdjForm.abutmentTask.isEnable = Number(this.jkdjForm.abutmentTask.isEnable)
          }
          let formData = {
            ...that.jkdjForm
          }
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
                that.close()
              } else {
                that.$message.warning(res.message)
              }
              that.confirmLoading = false
            }).catch((res) => {
            that.$message.warning(res.message)
            that.confirmLoading = false
          })
        }
      })
    },
    submitCallback() {
      this.$emit('ok')
      this.visible = false
    },
    handleCancel() {
      this.close()
    },
    setCorn(data) {
      if (data && data.target != null) {
        let dataList = data.target.value.split(' ')
        if (dataList[0] == '*') {
          this.$message.warning('请确认是否每秒都执行')
        }
      } else {
        let dataList = data.split(' ')
        if (dataList[0] == '*') {
          this.$message.warning('请确认是否每秒都执行')
        }
      }
      if (Object.keys(data).length == 0) {
        this.$message.warning('请输入cron表达式!')
      }
      // this.$nextTick(() => {
      //   this.jkdjForm.abutmentTask.executeCron = data;
      // })
    },
    validateIP(rule, value, callback){
      let reg = /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)(:\d{1,5})?$/
      if (!reg.test(value)) {
        callback('请输入正确服务地址！')
      } else {
        callback()
      }
    },
  }
}
</script>
<style scoped lang='less'>
@import '~@assets/less/normalModal.less';

//.color ::v-deep.ant-input {
//  padding: 0px 30px 0px 11px !important;
//}
</style>