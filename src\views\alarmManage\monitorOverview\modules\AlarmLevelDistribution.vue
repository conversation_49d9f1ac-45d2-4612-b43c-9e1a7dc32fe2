<template>
  <!-- 告警级别分布 -->
  <div style="height: 100%;width: 100%;">
    <div v-if="chartData.length===0">
      <a-list :data-source="[]" />
    </div>
    <div v-else ref="alarmLevelDistributionChart" style="height: 100%;width: 100%;"></div>
  </div>
</template>
<script>
import { getAction } from '@/api/manage'
export default {
  data() {
    return {
      chartData: [],
      url: {
        list: '/monitor/Overview/count/level'
      }
    }
  },
  props: {
    timeData: {
      type: Object,
      default: () => {}
    }
  },
  watch: {
    timeData: {
      deep: true,
      handler(val) {
        this.getData()
      }
    }
  },
  mounted() {
    this.getData()
  },
  methods: {
    getData() {
      const params = {};
      if (this.timeData.startTime) {
        params.startTime = this.timeData.startTime;
      }
      if (this.timeData.endTime) {
        params.endTime = this.timeData.endTime;
      }
      getAction(this.url.list, params).then(res => {
        if (res.code == 200) {
          this.chartData = res.result
          if (this.chartData.length > 0) {
            this.$nextTick(() => {
              this.initChart(res.result)
            })
          } else {
            if (this.myChart) {
              this.myChart.dispose()
            }
          }
        }
      })
    },
    initChart(data) {
      if (data.length == 0) {
        return
      }
      // 数据排序
      data.sort(function (a, b) { return b.level_value - a.level_value })
      let yData = []
      let color = []
      let max = data[0].level_value // 计算最大值
      data.map(item => {
        if (item.level_value && item.level_value > 0) {
          color.push(item.color)
          yData.push({
            name: item.name,
            value: item.level_value
          })
        }
      })
      if (max == 0) {
        this.chartData = []
        return
      }

      this.myChart = this.$echarts.init(this.$refs.alarmLevelDistributionChart)
      this.myChart.clear()

      let option = {
        color: color,
        tooltip: {
          confine: true,
          trigger: 'item',
          formatter: '{a} <br/>{b} : {c} 条'
        },
        grid: {
          top: '8%',
          left: '4%',
          right: '40%',
          bottom: '7%',
          containLabel: true
        },
        legend: {
          type: 'scroll',
          show: true,
          orient: 'vertical',
          right: '10',
          top: 'center',
          icon: 'none',
          itemGap: 25,
          formatter: function (name) {
            let html = ''
            data.forEach(val => {
              if (val.name == name) {
                if (name.length > 5) {
                  html = `${name.substring(0, 5)} ... :  {num|${val.level_value}}`
                } else {
                  html = `${name}:  {num|${val.level_value}}`
                }
              }
            })
            return html
          },
          textStyle: {
            color: 'rgba(0, 0, 0, 0.65)',
            fontSize: 14,
            rich: {
              num: {
                fontSize: 14,
                fontWeight: 600,
                color: color[0] || '#4389E4'
              }
            }
          }
        },
        calculable: true,
        series: [
          {
            name: '告警级别分布',
            type: 'funnel',
            left: '7%',
            top: '10%',
            right: '35%',
            bottom: 10,
            min: 1,
            max: max,
            minSize: '1%',
            maxSize: '100%',
            gap: 3,
            label: {
              normal: {
                // show: false
                color: 'transparent'
              }
            },
            labelLine: {
              normal: {
                length: 0
              }
            },
            itemStyle: {
              normal: {
                borderColor: '#fff',
                borderWidth: 1
              }
            },
            data: yData
          }
        ]
      }

      this.myChart.setOption(option)
      window.addEventListener('resize', () => {
        this.myChart.resize()
      })
    }
  }
}
</script>
<style scoped lang="less">
</style>