<template>
   <div class="desc-info" >
     <a-spin :spinning='confirmLoading'>
       <a-descriptions :column='{ xxl: 2, xl: 2, lg: 2, md: 2, sm: 1, xs: 1 }' bordered v-if='assets'>
       <a-descriptions-item  label='资产类型' v-if="assets.assetsCategoryText">{{assets.assetsCategoryText}}</a-descriptions-item>
       <a-descriptions-item  label='唯一标识符' v-if="assets.assetsUnique">{{assets.assetsUnique}}</a-descriptions-item>
       <a-descriptions-item  label='供应商名称' v-if="assets.producerName">{{assets.producerName}}</a-descriptions-item>
       <a-descriptions-item  label='型号'  v-if="assets.assetsModel">{{assets.assetsModel}}</a-descriptions-item>
       <!--      <a-descriptions-item  label='所属部门'  v-if="assets.departmentText">{{assets.departmentText}}</a-descriptions-item>-->
       <!--      <a-descriptions-item  label='所属人'  v-if="assets.ownerText">{{assets.ownerText}}</a-descriptions-item>-->
       <a-descriptions-item  label='质保开始日期'  v-if="assets.startQualityTime">{{assets.startQualityTime}}</a-descriptions-item>
       <a-descriptions-item  label='质保期限(月)'  v-if="assets.qualityTerm">{{assets.qualityTerm}}</a-descriptions-item>
       <a-descriptions-item  label='保修单位'  v-if="assets.repairFac">{{assets.repairFac}}</a-descriptions-item>
       <a-descriptions-item  label='保修单位电话'  v-if="assets.repairPhone">{{assets.repairPhone}}</a-descriptions-item>
       <a-descriptions-item  label='保修联系人'  v-if="assets.warrantyConnect">{{assets.warrantyConnect}}</a-descriptions-item>
       <a-descriptions-item  label='保修联系人电话'  v-if="assets.warrantyPhone">{{assets.warrantyPhone}}</a-descriptions-item>
       <a-descriptions-item  label='入库日期'  v-if="assets.storageTime">{{assets.storageTime}}</a-descriptions-item>
       <a-descriptions-item  label='更新时间'  v-if="assets.updateTime">{{assets.updateTime}}</a-descriptions-item>
       <a-descriptions-item  label='附件' v-if="filesList.length>0">
         <div v-for='(item, index) in filesList' :key='index'>
           <a :href='filesUrl + item' target='_bank' :download='item'> {{ item }} </a>
         </div>
       </a-descriptions-item>
     </a-descriptions>
     </a-spin>
   </div>

</template>
<script>
  import {getAction,} from '@/api/manage'
  export default {
    name: 'AlarmAssetsInfo',
    data() {
      return {
        confirmLoading:false,
        size: 'middle',
        assets: null,
        filesList: [],
        filesUrl: window._CONFIG['domianURL'] + '/sys/common/static/',
      }
    },
    methods: {
      show(record) {
        this.confirmLoading = true
        this.record = Object.assign({}, record)
        getAction('/assets/assets/queryByAssetsId', {
          assetsId: record.assetsIds
        }).then((res) => {
          this.assets ={}
          this.filesList =[]
          if (res.success&& res.result) {
            this.assets = res.result
            this.filesList =res.result.assetsFile&&res.result.assetsFile.length>0? res.result.assetsFile.split(','):[]
          }else if(!res.success) {
            this.$message.warning(res.message)
          }
          this.confirmLoading = false
        }).catch((err)=>{
          this.assets ={}
          this.filesList =[]
          this.confirmLoading = false
          this.$message.error(err.message)
        })
      }
    }
  }
</script>
<style lang="less" scoped>
.desc-info {
  text-align: center;
  min-height: 300px;
}
::v-deep .ant-descriptions-view {
  border-radius: 0px;
}

::v-deep .ant-descriptions-bordered .ant-descriptions-item-label {
  background-color: rgb(250, 250, 250);
  text-align: center;
  width: 17%;
}

::v-deep .ant-descriptions-item-label,
.ant-descriptions-item-content {
  color: rgb(96, 98, 102) !important;
}

::v-deep .ant-descriptions-bordered .ant-descriptions-item-content {
  word-break: break-word;
  white-space: normal;
}
</style>