<template>
  <div style="height:100%">
      <component :is="pageName" :data="data"/>
  </div>
</template>
<script>
  import DevOpsKnowledgeList from './DevOpsKnowledgeList'
  import UmpKnowledgeDetailForm from './modules/UmpKnowledgeDetailForm'
  export default {
    name: "DevOpsKnowledgeManage",
    data() {
      return {
        isActive: 0,
        data:{}
      };
    },
    components: {
      DevOpsKnowledgeList,
      UmpKnowledgeDetailForm
    },
    created(){
      this.pButton1(0);
    },
    //使用计算属性
    computed: {
      pageName() {
        switch (this.isActive) {
          case 0:
            return "DevOpsKnowledgeList";
            break;

          default:
            return "UmpKnowledgeDetailForm";
            break;
        }
      }
    },
    methods: {
      pButton1(index) {
        this.isActive = index;
      },
      pButton2(index,item) {
        this.isActive = index;
        this.data = item;
      }
    }
  }
</script>