<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container>
      <a-form :form="form" slot="detail">
        <a-row :gutter="24">
          <a-col :span="24">
            <a-form-item label="合同编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                v-decorator="['code', validatorRules.code]"
                :allowClear="true"
                autocomplete="off"
                placeholder="请输入合同编号"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="合同名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                v-decorator="['name', validatorRules.name]"
                :allowClear="true"
                autocomplete="off"
                placeholder="请输入合同名称"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item
              :label-col="labelCol"
              :wrapper-col="wrapperCol"
              label="合同类型"
              required
            >
              <j-dict-select-tag  v-decorator="['type', validatorRules.type]" :triggerChange="true" dictCode='contractType'
                  placeholder='请选择合同类型' />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="合同金额" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number
                v-decorator="['amount', validatorRules.amount]"
                :min='0'
                :max='1000000000000000'
                placeholder='请输入合同金额'
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="合同描述" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-textarea
                v-decorator="['description', validatorRules.description]"
                placeholder="请输入合同描述"
                :autoSize="{ minRows: 2, maxRows: 5 }"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
import { httpAction } from '@/api/manage'
import pick from 'lodash.pick'
import JFormContainer from '@/components/jeecg/JFormContainer'
export default {
  name: 'addContractForm',
  components: {
    JFormContainer
  },
  data() {
    return {
      form: this.$form.createForm(this),
      model: {},
      labelCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 12
        },
        md: {
          span: 10
        },
        lg: {
          span: 4
        }
      },
      wrapperCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 12
        },
        md: {
          span: 14
        },
        lg: {
          span: 19
        }
      },
      confirmLoading: false,
      validatorRules: {
        code: {
          rules: [
            {
              required: true,
              message: '请输入合同编号'
            },
            {
              min: 2,
              max: 20,
              message: '长度在2到20个字符',
              trigger: 'blur'
            }
          ]
        },
        name: {
          rules: [
            {
              required: true,
              message: '请输入合同名称'
            },
            {
              min: 2,
              max: 50,
              message: '长度在2到50个字符',
              trigger: 'blur'
            }
          ]
        },
        type: {
          rules: [
            {
              required: true,
              message: '请选择合同类型'
            }
          ]
        },
        description: {
          rules: [
            {
              required: true,
              message: '请输入合同描述'
            },
            { 
              min: 1, 
              max: 255, 
              message: '合同描述长度应在 1-255 之间' 
            }
          ]
        },
        amount: {
          rules: [
            {
              required: true,
              message: '请输入合同金额'
            },
            { 
              pattern: /^(([1-9][0-9]*)|([0]\.\d{0,2}|[1-9][0-9]*\.\d{0,2}))$/,
              trigger: 'change',
              message: '合同金额格式不正确'
            }
          ]
        }
      },
      url: {
        list: '/category/contract/list',
        add: '/category/contract/add',
        edit: '/category/contract/edit'
      }
    }
  },
  created() {},
  mounted() {},
  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(pick(this.model, 'name', 'code', 'description', 'type', 'amount'))
      })
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let formData = Object.assign(this.model, values)
          console.log(formData)
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    }
  }
}
</script>
<style scoped lang='less'>
</style>