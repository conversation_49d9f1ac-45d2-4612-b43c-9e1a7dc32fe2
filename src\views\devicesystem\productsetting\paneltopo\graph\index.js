import { Graph, Addon, FunctionExt, Shape } from '@antv/x6'
import './shape'

export default class FlowGraph {
  // public static graph: Graph
  // private static stencil: Addon.Stencil

  //编辑页面初始化
  static init (param) {
    console.log("初始化画布 ===",param)
    // 创建画布
    this.graph = new Graph({
      container: document.getElementById(param.id),
      grid: true,
      autoResize: true,
      interacting:{
        nodeMovable:function(e){
          if(param.operate==='create'){
            //面板不可移动
            if(e.cell.shape === "panel-node"){
              return true;
            }
            return true;
          }
          return false;
        },
      },
      //普通画布(未开启 scroller 模式)通过开启 panning 选项来支持拖拽平移
      panning: {
        enabled: true,
      },
      //配置节点的可移动区域
      translating: {
        restrict: true,//节点移动时无法超过画布区域
      },
      // 画布调整
      scroller: {
        enabled: false,
        pageVisible: true,
        pageBreak: false,//是否显示分页符, 显示时会产生分页白色虚线
        pannable: true
      },
      mousewheel: {
        enabled: true,
        modifiers: ['ctrl', 'meta'],
      },
      selecting: {
        enabled: param.operate==='create',
        multiple: true,
        rubberband: true, // 启用框选
        modifiers:'alt',//配合alt按键框选
        movable: true,
        showNodeSelectionBox: true,
        strict:true,
        pointerEvents:"none",//showNodeSelectionBox 时，会在节点上方盖一层元素 导致节点的事件无法响应，此时可以配置 pointerEvents: none 来解决
      },
      connecting: {
        anchor: 'center',
        connectionPoint: 'anchor',
        allowBlank: false,
        highlight: true,
        snap: true,
        createEdge() {
          return new Shape.Edge({
            attrs: {
              line: {
                stroke: '#5F95FF',
                strokeWidth: 1,
                targetMarker: {
                  name: 'classic',
                  size: 8
                }
              }
            },
            router: {
              name: 'manhattan'
            },
          })
        },
        validateConnection({ sourceView, targetView, sourceMagnet, targetMagnet }) {
          if (sourceView === targetView) {
            return false
          }
          if (!sourceMagnet) {
            return false
          }
          if (!targetMagnet) {
            return false
          }
          return true
        }
      },
      highlighting: {
        magnetAvailable: {
          name: 'stroke',
          args: {
            padding: 4,
            attrs: {
              strokeWidth: 4,
              stroke: 'rgba(223,234,255)'
            }
          }
        }
      },
      // 画布调整
      snapline: true,
      history: { //开启画布撤销/重做能力 只追踪节点连线的添加 忽略属性变化
        enabled: param.operate==='create',
        ignoreAdd: false,
        ignoreRemove: false,
        ignoreChange: true,
        beforeAddCommand(event,args){
          
          
        }
      },
      clipboard: {
        enabled: true
      },
      keyboard: {
        enabled: true
      },
      embedding: {
        enabled: true,
        findParent ({ node }) {
          const bbox = node.getBBox()
          return this.getNodes().filter((node) => {
            // 只有 data.parent 为 true 的节点才是父节点
            const data = node.getData()
            if (data && data.parent) {
              const targetBBox = node.getBBox()
              return bbox.isIntersectWithRect(targetBBox)
            }
            return false
          })
        }
      }
    })
    this.initEvent()
    return this.graph
  }

  static initEvent () {
    const { graph } = this
  }

  // 销毁
  static destroy () {
    this.graph.dispose()
  }
}
