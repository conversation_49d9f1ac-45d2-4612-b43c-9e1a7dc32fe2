<template>
  <j-modal :title='title' :width='width' :visible='visible' :destroyOnClose='true'
           :okButtonProps="{ class:{'jee-hidden': disableSubmit} }" :centered='true'
           :footer='null'
           @cancel='handleCancel'
           cancelText='关闭'>
    <div style='height: 75vh;overflow-y: auto'>
      <a-result
        :status="status"
        :title="resultTitle"
        :sub-title="resultSubTitle"
      >
        <div class="desc">
          <p style="font-size: 16px;">
            <strong>日志内容:</strong>
          </p>
          <p>
            <div v-html="record.executeLog"></div>
          </p>
        </div>
      </a-result>
    </div>
  </j-modal>
</template>
<script>
export default {
  name: 'StrategyLogDetail',
  props: {},
  components: {},
  data() {
    return {
      title: '日志详情',
      width: '70vw',
      visible: false,
      disableSubmit: false,
      confirmLoading: false,
      record: {},
      status: 'error',
      resultTitle:"",
      resultSubTitle:"",
    }
  },
  methods: {
    show(record) {
      this.record = record
      this.status = record.successFlag ? 'success' : 'error'
      this.resultTitle = record.executeStatus
      this.resultSubTitle = `开始时间：${record.startTime}\u3000结束时间：${record.endTime}`
      this.visible = true
    },
    handleCancel() {
      this.visible = false
    },
  }
}
</script>



<style scoped lang='less'>

</style>