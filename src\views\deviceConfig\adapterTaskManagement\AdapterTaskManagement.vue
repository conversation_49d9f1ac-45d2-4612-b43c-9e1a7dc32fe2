<template>
  <div style="height:100%">
    <keep-alive exclude='AdapterTaskInfo'>
      <component style="height:100%" :is="pageName" :data="data"/>
    </keep-alive>
  </div>
</template>
<script>
import AdapterTaskList from './AdapterTaskList.vue'
import AdapterTaskDetails from './AdapterTaskDetails.vue'
export default {
  name: "DdapterTaskManagement",
  data() {
    return {
      isActive: 0,
      data: {},
    };
  },
  components: {
    AdapterTaskList,
    AdapterTaskDetails
  },
  created() {
    this.pButton1(0);
  },
  //使用计算属性
  computed: {
    pageName() {
      switch (this.isActive) {
        case 0:
          return "AdapterTaskList";
        default:
          return "AdapterTaskDetails";
      }
    }
  },
  methods: {
    pButton1(index) {
      this.isActive = index;
    },
    pButton2(index, item) {
      this.isActive = index;
      this.data = item
    }
  }
}
</script>