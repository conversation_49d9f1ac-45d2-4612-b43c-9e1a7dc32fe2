<template>
  <div class='top-wrapper'>
    <div class='circle-img'></div>

    <div class='map-box'>
      <map-chart v-if='!loading' @changeMapADCode='changeMapADCode' :terminal-list='terminalList' :show-tooltip='showTooltip'></map-chart>
      <a-spin :spinning='loading' v-else-if='loading' class='spin'></a-spin>
    </div>
<!--    <div class='terminal-info-wrapper' v-if='selectedTerminalInfo.length>0'>
      <div class='terminal-info-content'>
        <div v-for='(item,idx) in selectedTerminalInfo' class='terminal-item' :title='item.title+":"+item.value'>
          <span class='title'>{{item.title}}</span>
          <span class='count' v-if='item.code==="onRate"'>{{item.value+item.unit}}</span>
          <span class='count' v-else>{{formatNumber(item.value)}}</span>
        </div>
      </div>
    </div>-->
    <div class='terminal-info-container' v-if='selectedTerminalInfo.length>0'>
      <div class='terminal-info-content'>
        <div v-for='(item,idx) in selectedTerminalInfo' class='terminal-item' :title='item.title+":"+item.value'>
          <div class='left' :style='{backgroundImage:`url(${item.img})`}'></div>
          <div class='right'>
            <span class='count' v-if='item.code==="onRate"'>{{item.value+item.unit}}</span>
            <span class='count' v-else>{{formatNumber(item.value)}}</span>
            <span class='title'>{{item.title}}</span>
          </div>
        </div>
      </div>
    </div>
    <div class='refresh-btn' @click='refreshMapFun'>
      <a-icon title='刷新' type='reload' class='refresh-icon'/>
    </div>
    <div class='region-name-wrapper'>
      <div class='border-one'></div>
      <div class='region-name'>
        <span><a-icon class='icon' type='environment'/></span>
        <span>{{areaName}}</span></div>
      <div class='border-two'></div>
    </div>
  </div>
</template>
<script>
import mapChart from '@views/statsCenter/homepageStatistics/modules/mapChart.vue'
import yqIcon from '@comp/tools/SvgIcon'
import { getAction } from '@api/manage'
import { queryConfigureDictItem,queryConfigureDictItems } from '@api/api'
export default {
  name: "",
  components: {
    mapChart,
    yqIcon
  },
  data() {
    return {
      loading:false,
      administrativeArea: '',
      areaName: '',
      showTooltip:false,
      selectedTerminalInfo:[],
      terminalList: [],
      initTerminalInfo:[
        {
          title:'终端数',
          img:'/statsCenter/homepage/terminalTotal.png',
          childDictCode:'total',
          display:"flex",
          code:'total',
          unit:'台',
          value:0
        },
        {
          title:'在线数',
          img:'/statsCenter/homepage/online.png',
          childDictCode:'online',
          display:"flex",
          code:'active',
          unit:'台',
          value:0
        },
        {
          title:'在线率',
          img:'/statsCenter/homepage/onlineRate.png',
          childDictCode:'onlineRate',
          display:"flex",
          code:'onRate',
          unit:'%',
          value:0
        },
        {
          title:'离线',
          childDictCode:'offline',
          display:"flex",
          code:'offLine',
          unit:'台',
          value:0
        },
        {
          title:'告警',
          childDictCode:'alarm',
          display:"flex",
          code:'alarm',
          unit:'台',
          value:0
        }
      ],
      url: {
        // terminal: '/data-analysis/index/getStatusCountByCity',
        terminal: '/data-analysis/index/getTerminalStatusCountByCity',
        areaInfo: '/sys/dict/getAreaInfo'
      }
    }
  },
  created() {
    this.loading=true
    this.terminalList=[]
    this.showTooltip=false

    this.getDispalyAttributeValue('tooltipConfiguration').then((result)=>{
      if (result){
        //设置终端显示内容个数--css变量
        let showCount=0
        this.initTerminalInfo.map(item=>{
          if(item.display=='flex'){
            ++showCount
          }
        })
        this.showTooltip=showCount>0?true:false
        document.body.style.setProperty('--tipRowCount', showCount)

        this.getAreaInfo().then((res)=>{
          if(res.success){
            this.getTerminalInfo().then((res1)=>{
              //this.getSelectedAreaTerminalInfo('')
              this.getMockJson()
              this.loading=false
            })
          }
        }).catch((err)=>{
          this.loading=false
          this.$message.warning(err.message)
        })
      }
    })
  },
  methods: {
    refreshMapFun() {
      this.loading=true
      this.getTerminalInfo().then((res)=>{
        if(res.success){
          //this.getSelectedAreaTerminalInfo('')
          this.getMockJson()
        }else {
          this.$message.warning(res.message)
        }
        this.loading=false
      }).catch((err)=>{
        this.$message.warning(err.message)
        this.loading=false
      })

      if (this.areaName !=this.administrativeArea.text) {
        this.$emit('changeMapADCode', '')
        this.areaName = this.administrativeArea.text
      } else {
        this.$emit('handleRefresh', '')
      }
    },
    changeMapADCode(adCode, areaName) {
      this.$emit('changeMapADCode', adCode)
     // this.getSelectedAreaTerminalInfo(adCode)
      this.getMockJson()
      this.areaName = areaName ? areaName : this.administrativeArea.text
    },
    getSelectedAreaTerminalInfo(adCode) {
      let code = adCode ? adCode : this.administrativeArea.id
      for (let i = 0; i < this.terminalList.length; i++) {
        let it = this.terminalList[i]
        if (it.areaId === code) {
          this.selectedTerminalInfo=it.value.slice(0,3)
          break
        }
      }
    },
    getTerminalInfo() {
      return new Promise((resolve, reject)=>{
        getAction(this.url.terminal, { cityId: '' }).then((res) => {
          this.terminalList = []
          if (res.success) {
            if (res.result.length > 0) {
              let result = res.result
              let nResult=[]
              for (let i=0;i<result.length;i++){
                let v=[]
                for (let k=0;k<this.initTerminalInfo.length;k++){
                  let item=this.initTerminalInfo[k]
                  if(result[i].hasOwnProperty(item.code)){
                    let m={...item}
                    m.value=result[i][item.code]
                    v.push(m)
                  }else{
                    if (item.code == "onRate") {
                      let m={...item}
                      if(result[i].total != 0){
                        let vn=(result[i].active / result[i].total * 100).toFixed('2')
                        let sv=vn+""
                        if(sv.slice(sv.length-2)==="00"){
                          m.value=sv.slice(0,sv.length-3)
                        }else if(sv.slice(sv.length-1)==="0"){
                          m.value=sv.slice(0,sv.length-1)
                        }else {
                          m.value=sv
                        }
                      }
                      v.push(m)
                    }
                  }
                }
                let item={
                  areaId:result[i].areaId,
                  value:v
                }
                nResult.push(item)
              }
              this.terminalList=nResult
              resolve({
                success:true,
                message:res.message
              })
            }
          } else {
            reject({
              success:false,
              message:res.message
            })
          }
        }).catch((err) => {
          reject({
            success:false,
            message:err.message
          })
        })
      })
    },
    getConfigureDict(parentCode,childCode) {
      return new Promise((resolve, reject) => {
        queryConfigureDictItem({ parentCode: parentCode, childCode: childCode }).then((res) => {
          if (res.success) {
            resolve({
              result: res.result,
              success: true,
              message: res.message
            })
          } else {
            reject({
              result: '',
              success: false,
              message: res.message
            })
          }
        }).catch((err) => {
          reject({
            result: '',
            success: false,
            message: err.message
          })
        })
      })
    },
    getConfigureDicts(parentCode) {
      return new Promise((resolve, reject) => {
        queryConfigureDictItems({ parentCode: parentCode}).then((res) => {
          if (res.success) {
            resolve({
              result: res.result,
              success: true,
              message: res.message
            })
          } else {
            reject({
              result: '',
              success: false,
              message: res.message
            })
          }
        }).catch((err) => {
          reject({
            result: '',
            success: false,
            message: err.message
          })
        })
      })
    },
    getAreaInfo() {
      return new Promise((resolve, reject) => {
        this.getConfigureDict('project_cityId',  'id').then((res0) => {
          getAction(this.url.areaInfo, { areaId: res0.result }).then((res) => {
            if (res.success) {
              this.administrativeArea = res.result
              this.areaName=res.result.text
              resolve({
                success: true,
                message: res.message
              })
            } else {
              this.$message.warning(res.message)
              reject({
                success: false,
                message: res.message
              })
            }
          }).catch((err) => {
            this.$message.warning(err.message)
            reject({
              success: false,
              message: err.message
            })
          })
        })
      })
    },
    getDispalyAttributeValue(parentCode){
      return new Promise((resolve)=>{
        this.getConfigureDicts(parentCode).then((res) => {
          if(res.success){
            if(res.result!='null'&&res.result.length>0){
              for(let m of this.initTerminalInfo){
                for (let i=0;i<res.result.length;i++){
                  let item=res.result[i]
                  if(item.name.includes(m.childDictCode)){
                    m.display=item.value==1?"flex":"none"
                    break
                  }
                }
              }
              resolve(true)
            }else{
              resolve(true)
            }
          }else{
            resolve(true)
          }
        }).catch(()=>{
          resolve(true)
        })
      })
    },
    formatNumber(num) {
      if(num!=undefined&&num!=null){
        return num.toString().replace(/\d+/, function(n) {
          return n.replace(/(\d)(?=(?:\d{4})+$)/g, '$1,')
        })
      }
    },
    getMockJson(){
      getAction(location.origin+"/statsCenter/mock/homeData.json").then((res) => {
        if(res){
          this.selectedTerminalInfo=res.mapAreaData
        }
      })
    }
  }
}
</script>

<style scoped lang="less">
.top-wrapper{
  height: 100%;
  width: 100%;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: start;
  overflow: hidden;

  .circle-img{
    position: absolute;
    content: "";
    width: 96%;
    height: 96%;
    top:0;
    background-image: url("/statsCenter/homepage/circle.png");
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center;
    //animation: rotate-circle-bg 6s normal infinite linear;
  }
  .refresh-btn{
    position: absolute;
    content: "";
    top:0.2rem;
    right: 0.5rem;
    width: 0.27rem;
    height: 0.25rem;

    .refresh-icon{
      cursor: pointer;
      color: rgba(255, 255, 255, 0.51);
      font-size: 0.3rem;
    }

    .refresh-icon:hover{
      color: #01A5FE;
    }
  }

/*  .terminal-info-wrapper{
    position: absolute;
    content: "";
    !*  top:0;
      left: 0.5rem;*!
    top:0.4rem;
    right: 0.6rem;
    width:2.4rem;//192/80

    .terminal-info-content{
      position: relative;
      padding:0.15rem 0.3rem ;
      //background: rgba(36, 118, 175, 0.2);
      background: rgba(14, 79, 125, 0.5);
      border-radius: 0.05rem;
      overflow: hidden;

      .terminal-item{
        width: 100%;
        display: flex;
        justify-content: start;
        align-items: center;
        flex-flow: row nowrap;
        overflow: hidden;
        padding:0.15rem 0;

        .title{
          margin-right: 0.2rem;
          color:#ffffff;
          font-size: 0.2rem;
          font-weight: 400;
          white-space: nowrap;
        }
        .count{
          font-size: 0.3rem;
          font-family: DIN-Medium;
          font-weight: bold;
          color:#67B9FF;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
    .terminal-info-content:before{
      position: absolute;
      content: '';
      left: -50%;
      top:0;
      width: 40%;
      height: 100%;
      opacity: 0.78;
      border-radius: 0.05rem;
      background-image: linear-gradient(to left, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0) 100%);
      //background-image: linear-gradient(-92deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0) 100%);
      //opacity: 0.78;
      //border-right: 1px solid rgba(255,255,255,0.6);
      border-right: 1px solid rgba(255,255,255,0.48);
      animation: scan 2s linear forwards infinite running;
    }
  }*/

  .terminal-info-container{
    position: absolute;
    content: "";
    top:0;
    left: 0.5rem;

    .terminal-info-content{
      position: relative;

      .terminal-item{
        display: flex;
        justify-content: start;
        align-items: center;
        flex-flow: row nowrap;
        margin-bottom: 0.125rem;//10/80

        .left{
          width: 1.2875rem;//103/80
          height:0.9125rem;//73/80
          background-repeat: no-repeat;
          background-size: 100%;
        }
        .right{
          display: flex;
          justify-content: center;
          align-items: start;
          flex-flow: column nowrap;

          .title{
            color:#ffffff;
            font-size: 0.2rem;
            font-weight: 400;
            white-space: nowrap;
          }
          .count{
            font-size: 0.4rem;
            font-family: DIN-Medium;
            font-weight: bold;
            color:#41BBFF;

            background: linear-gradient(0deg, #61DFFE 4.931640625%, #44A5FF 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            //color: transparent;
          }
        }
      }
    }
  }
  .region-name-wrapper{
    position: absolute;
    content:"";
    top:71%;
    right:0.3rem;//24/80
    width: 2.8rem;

    .border-one{
      width: 2.9rem;//225/80
      border-top: 2px solid #fff;
      border-bottom: 1px dashed #fff;
      padding-top:0.15rem;
    }
    .border-two{
      width:90%;
      padding-top:0.2rem;
      border-bottom: 2px solid #fff;
    }

    .region-name{
      width: 100%;
      height:0.6rem ;//40/80
      line-height: 0.6rem;
      padding-top:0.2rem;
      display: flex;
      flex-flow: row nowrap;
      justify-content: start;
      align-items: center;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;

      & :first-child{
        display: inline-block;
        height: 100%;
        line-height: 100%;
        font-weight: 400;
        color: #FFFFFF;
        margin-right: 0.05rem;

        .icon{
          font-size:0.35rem;// 28/80;
        }
      }

      & :last-child{
        display: inline-block;
        height: 100%;
        line-height: 100%;
        font-size:0.35rem;// 28/80;
        font-family: zhengku;
        font-weight: 400;
        color: #FFFFFF;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }

  .map-box{
    height:100%;
    width: 100%;
    //padding: 16px;
    text-align: center;

    .spin{
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
::v-deep .ant-spin-dot-item{
  background-color: #fff;
}
/*@keyframes rotate-circle-bg {
  0% {
    transform: rotate(0deg);
  }

  50% {
    transform: rotate(180deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
@keyframes scan{
  0%{
    left: -40%;
  }
  50% {
    left: 150%;
  }
  100%{
    left: 150%;
  }
}*/
</style>