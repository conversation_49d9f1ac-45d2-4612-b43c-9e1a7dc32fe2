<template>
  <a-row :gutter='10' style='height: 100%' class='vScroll zhl zhll'>
    <a-col style='width: 100%; height: 100%; display: flex; flex-direction: column'>
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <!-- 查询区域 -->
        <div class='table-page-search-wrapper'>
          <!-- 搜索区域 -->
          <a-form layout='inline' v-bind='formItemLayout'>
            <!-- 上三选择 -->
            <a-row :gutter='24' ref='row'>
              <a-col :span='spanValue'>
                <a-form-item label='类型'>
                  <!-- v-model="" @change="" -->
                  <a-select placeholder='请选择' allowClear v-model='queryParam.eventType'>
                    <a-select-option v-for="(item,index) in typeOptions" :key="index" :value="item.value">{{item.text}}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label='优先级'>
                  <a-select placeholder='请选择' allowClear v-model='queryParam.priorityInt'>
                    <a-select-option value='0'>低</a-select-option>
                    <a-select-option value='1'>中</a-select-option>
                    <a-select-option value='2'>高</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label='状态'>
                  <a-select placeholder='请选择' allowClear v-model='queryParam.status'>
                    <a-select-option value='0'>新建</a-select-option>
                    <a-select-option value='1'>审批</a-select-option>
                    <a-select-option value='2'>处理</a-select-option>
                    <a-select-option value='3'>审核</a-select-option>
                    <a-select-option value='4'>关闭</a-select-option>
                    <a-select-option value='5'>退回</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span='spanValue' v-show='toggleSearchStatus'>
                <a-form-item label='标题'>
                  <a-input placeholder='请输入' v-model='queryParam.title' :allowClear='true'></a-input>
                </a-form-item>
              </a-col>
              <!-- 按钮区域 -->
              <a-col :span='colBtnsSpan()'>
                <span class='table-page-search-submitButtons'
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button type='primary' class='btn-search btn-search-style' @click='searchQuery'>查询</a-button>
                  <a-button class='btn-reset btn-reset-style' @click='searchReset'>重置</a-button>
                  <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>

      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <!-- table区域-begin -->
        <div class='table-operator table-operator-style'>
          <!-- <a-button type="primary" @click="handleAdds">新增</a-button>&nbsp;
              <a-button @click="loadData" type="primary" icon="reload">刷新</a-button> -->
          <a-button @click="handleExportXlsEven('事件我的已办')">批量导出</a-button>
        </div>

        <a-table ref='table' bordered rowKey='id' :columns='columns' :dataSource='dataSource'
          :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}" :pagination='ipagination' :loading='loading'
          :rowSelection='{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }' @change='handleTableChange'>
          <span slot='priority' slot-scope='priority, record'>
            <div v-if='record.priority == 0' style='color: #ffb300'>低</div>
            <div v-if='record.priority == 1' style='color: #fc7611'>中</div>
            <div v-if='record.priority == 2' style='color: #df1a1a'>高</div>
          </span>
          <span slot='eventType' slot-scope='eventType, record'>
            <div>{{ getEvent(record) }}</div>
          </span>
          <span slot='status' slot-scope='status, record'>
            <div v-if='record.status == 0'>新建</div>
            <div v-if='record.status == 1'>审批</div>
            <div v-if='record.status == 2'>处理</div>
            <div v-if='record.status == 3'>审核</div>
            <div v-if='record.status == 4'>关闭</div>
            <div v-if='record.status == 5'>退回</div>
          </span>
          <span slot='result' slot-scope='result, record'>
            <div v-if='record.result == 0'>未提交</div>
            <div v-if='record.result == 1'>处理中</div>
            <div v-if='record.result == 2' style='color: #139b33'>已通过</div>
            <div v-if='record.result == 3' style='color: #df1a1a'>已退回</div>
          </span>
          <span slot='slaResponse' slot-scope='slaResponse, record'>
            <div v-if="record.slaResponseType === '0'" style='color: #139b33'>
              {{ record.slaResponse }}
            </div>
            <div v-if="record.slaResponseType === '1'" style='color: #df1a1a'>
              {{ record.slaResponse }}
            </div>
          </span>

          <span slot='slaAccomplish' slot-scope='slaAccomplish, record'>
            <div v-if="record.slaAccomplishType === '0'" style='color: #139b33'>
              {{ record.slaAccomplish }}
            </div>
            <div v-if="record.slaAccomplishType === '1'" style='color: #df1a1a'>
              {{ record.slaAccomplish }}
            </div>
          </span>

          <!-- 操作 -->
          <span slot='action' slot-scope='text, record' class='caozuo'>
            <template v-if='record.status == 0'>
              <a href='javascript:void(0);' style='color: #00a0e9' @click='apply(record)'>提交申请</a>
            </template>
            <template v-if='record.status != 0'>
              <a @click='handleDeatails(record)'>查看</a>
            </template>
            <template v-if='record.status == 0'>
              <a-divider type='vertical' />
              <a @click='handleEdit(record)'>编辑</a>
              <a-divider type='vertical' />
              <a-popconfirm title='确定删除吗?' @confirm='() => handleDelete(record)'>
                <a>删除</a>
              </a-popconfirm>
            </template>
          </span>
          <template slot='tooltip' slot-scope='text'>
            <a-tooltip placement='topLeft' :title='text' trigger='hover'>
              <div class='tooltip'>
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
      </a-card>
      <!--提交申请表单-->
      <a-modal title='提交申请' v-model='modalVisible' :mask-closable='false' :width='500' :footer='null'>
        <div v-if='modalVisible'>
          <a-form-item label='选择审批人' v-show='showAssign'>
            <a-select style='width: 100%' v-model='form.assignees' placeholder='请选择' mode='multiple' :allowClear='true'>
              <a-select-option v-for='(item, i) in assigneeList' :key='i' :value='item.username'>{{
                  item.username
                }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label='下一审批人' v-show='isGateway'>
            <a-alert type='info' showIcon message='分支网关处不支持自定义选择下一审批人，将自动下发给所有可审批人。'>，将发送给下一节点所有人
            </a-alert>
          </a-form-item>
          <a-form-item label='消息通知'>
            <a-checkbox v-model='form.sendMessage'>站内消息通知</a-checkbox>
            <a-checkbox v-model='form.sendSms' disabled>短信通知</a-checkbox>
            <a-checkbox v-model='form.sendEmail' disabled>邮件通知</a-checkbox>
          </a-form-item>
          <div slot='footer' style='text-align: right;'>
            <a-button type='text' @click='modalVisible = false'>取消</a-button>
            <div style='display: inline-block; width: 20px'></div>
            <a-button type='primary' :disabled='submitLoading' @click='applySubmit'>提交</a-button>
          </div>
        </div>
      </a-modal>
      <!-- <applicationAdd ref="modalForm" @ok="loadData"></applicationAdd>
    <applicationEdit ref="applicationEdit" @ok="modalFormOk"></applicationEdit> -->
      <applicationDetails ref='applicationDetails' @ok='modalFormOk'></applicationDetails>
    </a-col>
  </a-row>
</template>
<script>
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  //   import applicationAdd from './modules/applicationAdd'
  //   import applicationEdit from './modules/applicationEdit'
  import applicationDetails from './modules/applicationDetails'
  import {
    deleteAction,
    getAction,
    downFile,
    postAction
  } from '@/api/manage'
  import {
    ajaxGetDictItems
  } from '@/api/api'
  import {
    YqFormSearchLocation
  } from '@/mixins/YqFormSearchLocation'

  export default {
    name: 'eventDoneManage',
    mixins: [JeecgListMixin, YqFormSearchLocation],
    components: {
      //   applicationAdd,
      //   applicationEdit,
      applicationDetails
    },
    data() {
      return {
        typeOptions: [],
        formItemLayout: {
          labelCol: {
            style: 'width:80px'
          },
          wrapperCol: {
            style: 'width:calc(100% - 80px)'
          }
        },
        // 表格假数据点击按钮需要
        dataSource: [],
        form: {
          priority: 0,
          assignees: [],
          sendMessage: true
        },
        modalVisible: false,
        submitLoading: false,
        isGateway: false,
        assigneeList: [],
        // 表头
        columns: [{
            title: '编号',
            dataIndex: 'processNumber'
          },
          {
            title: '标题',
            dataIndex: 'title',
            scopedSlots: {
              customRender: 'tooltip'
            }
          },
          {
            title: '类型',
            dataIndex: 'eventType',
            scopedSlots: {
              customRender: 'eventType'
            }
          },
          {
            title: '优先级',
            dataIndex: 'priority',
            scopedSlots: {
              customRender: 'priority'
            }
          },
          {
            title: '状态',
            dataIndex: 'status',
            scopedSlots: {
              customRender: 'status'
            }
            // scopedSlots: { customRender: "" }
          },
          {
            title: '结果',
            dataIndex: 'result',
            scopedSlots: {
              customRender: 'result'
            }
          },
          {
            title: '创建时间',
            dataIndex: 'createTime'
          },
          {
            title: 'SLA响应',
            dataIndex: 'slaResponse',
            scopedSlots: {
              customRender: 'slaResponse'
            }
          },
          {
            title: 'SLA完成',
            dataIndex: 'slaAccomplish',
            scopedSlots: {
              customRender: 'slaAccomplish'
            }
          },
          {
            title: '任务名称',
            dataIndex: 'taskName'
          },
          {
            title: '任务开始时间',
            dataIndex: 'taskStartTime'
          },
          {
            title: '任务结束时间',
            dataIndex: 'taskEndTime'
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            fixed: 'right',
            width: 180,
            scopedSlots: {
              customRender: 'action'
            }
          }
        ],
        url: {
          list: '/event/doneList',
          getFirstNode: '/actProcessIns/getFirstNode',
          applyBusiness: '/actBusiness/apply',
          delByIds: '/actBusiness/delByIds',
          exportXlsUrl: '/event/doneExportXls'
        }
      }
    },
    created() {
      this.getType()
    },
    methods: {
      getEvent(data) {
        let e1 = this.typeOptions.find((el) => data.eventType == el.value);
        if (e1 != null) {
          return e1.text
        } else {
          return "";
        }
      },
      async getType() {
        await ajaxGetDictItems('event_Request_Type', null).then((res) => {
          if (res.success) {
            this.typeOptions = res.result
          }
        })
      },
      handleExportXlsEven(fileName) {
        if (!fileName || typeof fileName != 'string') {
          fileName = '导出文件'
        }
        let param = this.getQueryParams()
        if (this.selectedRowKeys && this.selectedRowKeys.length > 0) {
          param['selections'] = this.selectedRowKeys.join(',')
        }
        downFile(this.url.exportXlsUrl, param).then((data) => {
          if (!data) {
            this.$message.warning('文件下载失败')
            return
          }
          if (typeof window.navigator.msSaveBlob !== 'undefined') {
            window.navigator.msSaveBlob(new Blob([data], {
              type: 'application/vnd.ms-excel'
            }), fileName + '.xls')
          } else {
            let url = window.URL.createObjectURL(new Blob([data], {
              type: 'application/vnd.ms-excel'
            }))
            let link = document.createElement('a')
            link.style.display = 'none'
            link.href = url
            link.setAttribute('download', fileName + '.xls')
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link) //下载完成移除元素
            window.URL.revokeObjectURL(url) //释放掉blob对象
          }
        })
      },
      // loadData() {
      //   this.loading = true
      //   this.getListdata()
      // },
      // getListdata() {
      //   postAction('/event/listData', {}).then((res) => {
      //     if (res.success) {
      //       let records = res.result || []
      //       this.dataSource = records
      //       this.ipagination.total = records.length
      //     }
      //     if (res.code === 510) {
      //       this.$message.warning(res.message)
      //     }
      //     this.loading = false
      //   })
      // },
      handleAdds: function () {
        let v = {
          formKey: 'event_form'
        }
        if (!v.formKey) {
          this.$message.warning('该流程信息未配置表单，请联系开发人员！')
          return
        }
        this.$refs.modalForm.title = '新建事件'
        this.$refs.modalForm.add(v)

        // this.$refs.modalForm.
        // this.$refs.modalForm.title = '新建事件'
        this.$refs.modalForm.disableSubmit = false
      },
      //提交申请
      apply(v) {
        if (!v.procDefId || v.procDefId == 'null') {
          this.$message.error('流程定义为空')
          return
        }
        this.form.id = v.id
        this.form.procDefId = v.procDefId
        this.form.title = v.title
        // 加载审批人
        getAction(this.url.getFirstNode, {
          procDefId: v.procDefId
        }).then((res) => {
          if (res.success) {
            if (res.result.type == 3 || res.result.type == 4) {
              this.isGateway = true
              this.modalVisible = true
              this.form.firstGateway = true
              this.showAssign = false
              this.error = ''
              return
            }
            this.form.firstGateway = false
            this.isGateway = false
            if (res.result.users && res.result.users.length > 0) {
              this.error = ''
              this.assigneeList = res.result.users
              // 默认勾选
              let ids = []
              res.result.users.forEach((e) => {
                ids.push(e.username)
              })
              this.form.assignees = ids
              this.showAssign = true
            } else {
              this.form.assignees = []
              this.showAssign = true
              this.error = '审批节点未分配候选审批人员，请联系管理员！'
            }
            if (this.error) {
              this.$message.error(this.error)
              return
            }
            this.modalVisible = true
          } else {
            this.$message.error(res.message)
          }
        })
      },
      //申请提交
      applySubmit() {
        if (this.showAssign && this.form.assignees.length < 1) {
          this.error = '请至少选择一个审批人'
          this.$message.error(this.error)
          return
        } else {
          this.error = ''
        }
        this.submitLoading = true
        var params = Object.assign({}, this.form)
        params.assignees = params.assignees.join(',')
        postAction(this.url.applyBusiness, params)
          .then((res) => {
            if (res.success) {
              this.$message.success('操作成功')
              this.loadData()
              this.modalVisible = false
            } else {
              this.$message.error(res.message)
            }
          })
          .finally(() => (this.submitLoading = false))
      },
      handleEdit(r, isView) {
        if (!r.formKey) {
          this.$message.warning('该流程信息未配置表单，请联系开发人员！')
          return
        }
        this.$refs.modalForm.add(r)
        this.$refs.modalForm.title = '编辑'
      },
      handleDeatails: function (record) {
        this.$refs.applicationDetails.edit(record)
        this.$refs.applicationDetails.title = '事件详情'
        this.$refs.applicationDetails.disableSubmit = false
      },
      handleDelete(r) {
        postAction(this.url.delByIds, {
          ids: r.id
        }).then((res) => {
          if (res.success) {
            this.$message.success(res.message)
            this.loadData()
          } else {
            this.$message.error(res.message)
          }
        })
      }
    }
  }
</script>
<style lang='less' scoped>
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';

  /*给table列设置宽度*/
  ::v-deep .ant-table-scroll .ant-table-thead>tr>th,
  ::v-deep .ant-table-scroll .ant-table-tbody>tr>td {
    /*编号*/

    &:nth-child(2) {
      min-width: 150px;
      max-width: 300px;
    }

    /*标题*/

    &:nth-child(3) {
      min-width: 150px;
      max-width: 300px;
    }

    /*类型*/

    &:nth-child(4) {
      min-width: 50px;
      max-width: 120px;
    }

    /*优先级*/

    &:nth-child(5) {
      min-width: 50px;
      max-width: 150px;
    }

    /*状态*/

    &:nth-child(6) {
      min-width: 50px;
      max-width: 150px;
    }

    /*结果*/

    &:nth-child(7) {
      min-width: 50px;
      max-width: 150px;
    }

    /*创建时间*/

    &:nth-child(8) {
      min-width: 100px;
      max-width: 200px;
    }

    /*SLA响应*/

    &:nth-child(9) {
      min-width: 50px;
      max-width: 250px;
    }

    /*SLA完成*/

    &:nth-child(10) {
      min-width: 50px;
      max-width: 250px;
    }
  }

  /*表头样式*/
  ::v-deep .ant-table-thead>tr>th {
    text-align: center;
    white-space: nowrap;
  }

  /*内容对齐方式、省略显示*/
  ::v-deep .ant-table-tbody>tr>td {
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;

    &:first-child,
    &:nth-child(2),
    &:nth-child(4),
    &:nth-child(5),
    &:nth-child(6),
    &:nth-child(7),
    &:nth-child(8),
    &:nth-child(9),
    &:nth-child(10) {
      text-align: center;
    }

    &:nth-child(3) {
      text-align: left;
    }
  }
</style>