<template>
  <div>

    <a-modal
      title="到期时间配置"
      :visible="dueDateShow"
      :width="'20%'"
      :maskClosable="false"
      @ok="addProperty"
      @cancel="hideProperties"
    >

        <a-form :form="dueFrom">
          <a-form-item label="类型">
            <a-select  v-model="dueFrom.dueType" placeholder="请选择类型" >
              <a-select-option v-for="item in dataList"   :key="item.name" :value="item.model" width="50%">
                {{ item.name }}
              </a-select-option>
            </a-select>
          </a-form-item>
            <a-form-item label="值">
                <a-input-number placeholder="请输入值"  v-model="dueFrom.value"  style="width:100% !important;"/>
            </a-form-item>
        </a-form>
    </a-modal>
  </div>
</template>

<script>

export default {

  props: {},
  data() {
    return {
      dueDateShow: false,
      dataList: [
        { name: '分钟', model: 'F'},
        { name: '小时', model: 'H'},
        { name: '天 ', model: 'D'  },
        { name: '周 ', model: 'W'  },
        { name: '月 ', model: 'M'   }

      ],
      dueFrom: { dueType:'', value:0}
    }
  },

  created() {
  },
  methods: {
    extractNumber(str) {
      const match = str.match(/(\d+)/);
      return match ? match[0] : null;
    },
    openVisible(value) {
      if (value){
        if (value.includes('PT')&&value.includes('M')){
          this.dueFrom.dueType='F'
        }else if (value.includes('PT')&&value.includes('H')){
          this.dueFrom.dueType='H'
        }else if (value.includes('P')&&value.includes('D')){
          this.dueFrom.dueType='D'
        }else if (value.includes('P')&&value.includes('W')){
          this.dueFrom.dueType='W'
        }else if (value.includes('P')&&value.includes('M')){
          this.dueFrom.dueType='M'
        }else{
          this.dueFrom.dueType=''
        }
        this.dueFrom.value=this.extractNumber(value)
      }
      this.dueDateShow = true
    },
    closeVisible() {
      this.dueDateShow = false
    },
    addProperty() {
      if (this.dueFrom.dueType===''){
        this.$message.error("请选择类型!")
        return
      }
      if (this.dueFrom.value===0){
        this.$message.error("请输入值！")
        return
      }
      let data=''
      switch (this.dueFrom.dueType) {
        case 'F':
          data=`PT${this.dueFrom.value}M`
          break
        case 'H':
          data=`PT${this.dueFrom.value}H`
          break
        case 'D':
          data=`P${this.dueFrom.value}D`
          break
        case 'W':
          data=`P${this.dueFrom.value}W`
          break
        case 'M':
          data=`P${this.dueFrom.value}M`
          break
        default:
          data=''
      }
      this.$emit('getDueDate',data)
      this.dueDateShow = false
    },
    hideProperties() {
      this.closeVisible()
    }
  }
}
</script>

<style lang="less" scoped>

</style>