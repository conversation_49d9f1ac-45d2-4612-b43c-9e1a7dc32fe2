<template>
  <div style="height:100%">
    <keep-alive>
      <component :is="pageName" :data="data"/>
    </keep-alive>
  </div>
</template>
<script>
  import MonthlyMagazine from './MonthlyMagazine'
  import MonthlyMagazineDetails from './modules/MonthlyMagazineDetails'
  export default {
    name: "MonthlyMagazineManage",
    data() {
      return {
        isActive: 0,
        data:{}
      };
    },
    components: {
      MonthlyMagazine,
      MonthlyMagazineDetails
    },
    created(){
      this.pButton1(0);
    },
    //使用计算属性
    computed: {
      pageName() {
        switch (this.isActive) {
          case 0:
            return "MonthlyMagazine";
            break;

          default:
            return "MonthlyMagazineDetails";
            break;
        }
      }
    },
    methods: {
      pButton1(index) {
        this.isActive = index;
      },
      pButton2(index,item) {
        this.isActive = index;
        this.data = item;
      }
    }
  }
</script>