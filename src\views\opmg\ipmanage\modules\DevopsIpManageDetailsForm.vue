<template>
  <a-card style="height: 100%;">
    <a-row>
      <a-col :span="24">
        <span style="margin-left:10px;color: #409eff;cursor: pointer" @click="handleDetailEdit(data)">
          <a-icon type="edit" style="margin-right: 6px;"/>编辑
        </span>
        <span style="float: right;margin-bottom: 12px;" ><img src="~@/assets/return1.png" alt="" @click="getGo" style="width: 20px;height: 20px;cursor: pointer"></span>
      </a-col>
      <a-col :span="24">
        <table class="gridtable">
          <tr>
            <td class="leftTd">设备名称</td> <td class="rightTd">{{ data.terminalName }}</td>
            <td class="leftTd">IP地址</td> <td class="rightTd">{{ data.ipAddress }}</td>
          </tr>
          <tr>
            <td class="leftTd">MAC地址</td> <td class="rightTd">{{ data.macCode }}</td>
            <td class="leftTd">使用人</td> <td class="rightTd">{{data.utilizeUserText}}</td>
          </tr>
        </table>
      </a-col>
    </a-row>
    <devops-ip-manage-modal ref="modalForm" @ok="modalFormOk"></devops-ip-manage-modal>
  </a-card>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import pick from 'lodash.pick'
  import { validateDuplicateValue } from '@/utils/util'
  import JFormContainer from '@/components/jeecg/JFormContainer'
  import JSelectUserByDep from '@/components/jeecgbiz/JSelectUserByDep'
  import DevopsIpManageModal from './DevopsIpManageModal'
  export default {
    name: 'DevopsIpManageDetailsForm',
    components: {
      JFormContainer,
      JSelectUserByDep,
      DevopsIpManageModal
    },
    props: {
      //流程表单data
      //表单模式：true流程表单 false普通表单
      formBpm: {
        type: Boolean,
        default: false,
        required: false
      },
      formBpm: {
        type: Boolean,
        default: false,
        required: false
      },
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      },
      data:{
        type:Object
      }
    },
    data () {
      return {
        form: this.$form.createForm(this),
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        validatorRules: {
          terminalName: {
            rules: [
              { required: true, message: '请输入设备名称!'},
            ]
          },
          macCode: {
            rules: [
              { required: true,
                pattern: /^[A-F0-9]{2}(-[A-F0-9]{2}){5}$|^[A-F0-9]{2}(:[A-F0-9]{2}){5}$/,
                message: '请输入正确的MAC地址!'},
            ]
          },
          ipAddress: {
            rules: [
              {
                required: true,
                pattern: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
                message: '请输入正确的IP地址!'},

            ]
          },
        },
        url: {
          add: "/devopsipmanage/devopsIpManage/add",
          edit: "/devopsipmanage/devopsIpManage/edit",
          queryById: "/devopsipmanage/devopsIpManage/queryById"
        },
        iconShow:"0",
        inpData:{}
      }
    },
    computed: {
      formDisabled(){
        if(this.formBpm===true){
          if(this.formData.disabled===false){
            return false
          }
          return true
        }
        return this.disabled
      },
      showFlowSubmitButton(){
        if(this.formBpm===true){
          if(this.formData.disabled===false){
            return true
          }
        }
        return false
      }
    },
    created () {
      //如果是流程中表单，则需要加载流程表单data
      this.showFlowData();

    },
    mounted(){
    },
    methods: {
      modalFormOk(){
        let params = {id:this.data.id};
        getAction(this.url.queryById,params).then((res)=>{
          if(res.success){
            this.data = res.result;
          }
        });
      },
      //详情编辑
      handleDetailEdit: function (record) {
        this.$refs.modalForm.edit(record)
        this.$refs.modalForm.title = '编辑'
        this.$refs.modalForm.disableSubmit = false
      },
      //渲染流程表单数据
      showFlowData(){
        if(this.formBpm === true){
          let params = {id:this.formData.dataId};
          getAction(this.url.queryById,params).then((res)=>{
            if(res.success){
              this.edit (res.result);
            }
          });
        }
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
              method = 'put';
            }
            let formData = Object.assign(this.model, values);
            httpAction(httpurl,formData,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }

        })
      },
      popupCallback(row){
        this.form.setFieldsValue(pick(row,'terminalName','ipAddress','macCode','utilizeUserId'))
      },
      //返回上一级
      getGo(){
        this.$parent.pButton2(0);
      }
    }
  }
</script>
<style scoped>
  table.gridtable {
    font-family: verdana,arial,sans-serif;
    font-size:14px;
    color:#606266;
    border-width: 1px;
    border-color: #e8e8e8;
    border-collapse: collapse;
    text-align: left;
    width: 100%;
  }
  table.gridtable td {
    border-width: 1px;
    border-style: solid;
    border-color: #e8e8e8;
  }
  .leftTd{
    width: 17%;
    background-color: #FAFAFA;
    padding: 16px 24px;
    text-align: center;
  }
  .rightTd{
    width: 35%;
    padding: 16px 24px;
  }
</style>