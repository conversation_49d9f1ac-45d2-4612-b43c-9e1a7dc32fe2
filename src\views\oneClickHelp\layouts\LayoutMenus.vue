<template>
  <div class="menus">
    <a-menu
      mode="inline"
      style="width: 100%"
      :selectedKeys="selectedKeys"
      :openKeys="openKeys"
      @click="menuClick"
      @openChange="openChange"
    >
      <template v-for="item in menus">
        <a-sub-menu :key="item.id" class="yq-sub-menu" v-if="item.children && item.children.length">
          <span slot="title">
            <a-icon :type="item.meta.icon" />
            <span> {{ item.meta.title }}</span>
          </span>
          <a-menu-item v-for="subItem in item.children" :key="subItem.path">{{ subItem.meta.title }}</a-menu-item>
        </a-sub-menu>
        <a-menu-item :key="item.path" v-else>
          <a-icon :type="item.meta.icon" />
          {{ item.meta.title }}
        </a-menu-item>
      </template>
    </a-menu>
  </div>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex'
import { getQueryStringRegExp } from '@/utils/util'
export default {
  name: 'layoutMenus',
  props: {
    hostName: {
      type: String || null,
      default: '',
    },
  },
  data() {
    return {
      menus: [],
      flatMenus: [],
      selectedKeys: [],
      openKeys: [],
    }
  },
  computed: {
    ...mapState({
      permissionMenuList: (state) => state.user.permissionList,
      userInfo: (state) => state.user.info,
    }),
  },
  watch: {
    $route: function () {
      let path = this.$route.path
      this.selectedKeys = [path]
      let menu = this.flatMenus.find((el) => el.path === path)
      if (menu && menu.pid) {
        this.openKeys = [menu.pid]
      } else {
        this.openKeys = []
      }
    },
  },
  created() {
    this.menus = this.permissionMenuList.filter((el) => !el.hidden)
    this.selectedKeys.push(this.$route.path)
    if (this.menus.length > 0) {
      this.getFlatMenus(this.menus)
    }

    // console.log("当前的菜单 === ",this.$route,this.permissionMenuList)
  },
  methods: {
    getFlatMenus(list, pid) {
      list.forEach((item) => {
        if (item.children && item.children.length > 0) {
          this.getFlatMenus(item.children, item.id)
        } else if (item) {
          if (pid) {
            item.pid = pid
            if (this.$route.path === item.path) {
              this.openKeys = [pid]
            }
          }
          this.flatMenus.push(item)
        }
      })
    },
    menuClick({ item, key, keyPath }) {
      // console.log("选中的菜单 === ",this.selectedKeys)
      // this.selectedKeys = [key];
      this.$router.push({ path: key })
      // let menu = this.flatMenus.find(el=>el.id === key)
      // if(menu){
      //   this.$router.push({path:menu.path,query:menu.query})
      // }
    },
    openChange(keys) {
      this.openKeys = keys
    },
  },
}
</script>

<style lang="less" scoped>
.menus {
  width: 320px;
  height: 100%;
  background-image: url(/oneClickHelp/layout/menuBg.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  overflow: auto;
  padding: 40px 9px 8px 9px;
  font-family: SourceHanSansCN-Regular;
}

/deep/ .ant-menu {
  background: transparent;
}
/deep/ .ant-menu-submenu > .ant-menu {
  background: transparent;
}
/deep/ .ant-menu-inline,
.ant-menu-vertical,
.ant-menu-vertical-left {
  border-right: none;
}
/deep/ .ant-menu.ant-menu-root > .ant-menu-item {
  background-image: linear-gradient(
      90deg,
      rgba(103, 156, 255, 0.2) 2%,
      rgba(137, 191, 255, 0.09) 43%,
      rgba(163, 189, 255, 0) 100%
    ),
    url(/oneClickHelp/localDeviceInfo/bg.png);
  color: #fff;
  border-bottom-width: 1px;
  border-bottom-style: solid;
  border-image: linear-gradient(90deg, #3d7fff 2%, rgba(137, 191, 255, 0.43) 43%, rgba(61, 127, 255, 0) 100%) 1;
}
/deep/ .yq-sub-menu.ant-menu-submenu,
.ant-menu-submenu-inline {
  color: #fff;
  background-image: linear-gradient(
      90deg,
      rgba(103, 156, 255, 0.2) 2%,
      rgba(137, 191, 255, 0.09) 43%,
      rgba(163, 189, 255, 0) 100%
    ),
    url(/oneClickHelp/localDeviceInfo/bg.png);
  // border-bottom-width: 1px;
  // border-bottom-style: solid;
  // border-image: linear-gradient(90deg, #3d7fff 2%, rgba(137, 191, 255, 0.43) 43%, rgba(61, 127, 255, 0) 100%) 1;
}
/deep/ .ant-menu-sub.ant-menu-inline > .ant-menu-item,
.ant-menu-sub.ant-menu-inline > .ant-menu-submenu > .ant-menu-submenu-title {
  color: #fff;
}
/deep/ .ant-menu-submenu-title:hover {
  color: #fff;
}
/deep/ .ant-menu:not(.ant-menu-horizontal) .ant-menu-item-selected {
  background-color: transparent;
  color: #66ffff;
  background-image: linear-gradient(
      90deg,
      rgba(21, 85, 175, 0.6) 2%,
      rgba(21, 85, 175, 0.26) 43%,
      rgba(21, 85, 175, 0) 100%
    ),
    url(/oneClickHelp/localDeviceInfo/bg.png);
}
/deep/ .ant-menu:not(.ant-menu-horizontal) .ant-menu-item-selected {
  background-color: transparent;
  color: #66ffff;
  font-weight: 700;
  background-image: linear-gradient(
      90deg,
      rgba(21, 85, 175, 0.6) 2%,
      rgba(21, 85, 175, 0.26) 43%,
      rgba(21, 85, 175, 0) 100%
    ),
    url(/oneClickHelp/localDeviceInfo/bg.png);
}
/deep/ .ant-menu-item:active,
.ant-menu-submenu-title:active {
  background-color: transparent;
}
/deep/ .ant-menu-inline .ant-menu-selected::after,
.ant-menu-inline .ant-menu-item-selected::after {
  // opacity: 0;
  left:0;
  right: unset;
  border-color: #5081F9 ;
}
/deep/ .ant-menu-inline .ant-menu-submenu-title {
  border-bottom-width: 1px;
  border-bottom-style: solid;
  border-image: linear-gradient(90deg, #3d7fff 2%, rgba(137, 191, 255, 0.43) 43%, rgba(61, 127, 255, 0) 100%) 1;
}
/deep/ .ant-menu-submenu-inline > .ant-menu-submenu-title .ant-menu-submenu-arrow::after {
    
    // background: rgba(4,15,49,0.01);
    // background:url(/oneClickHelp/layout/menu-arrow.png);
    // background-size: 100% 100%;
    // border:none;
    // width: 32px;
    // height: 32px;
    // transform: rotate(0deg);
    content: none;
}
/deep/ .ant-menu-submenu-inline > .ant-menu-submenu-title .ant-menu-submenu-arrow::before{
  content: none;
}
/deep/  .ant-menu-submenu-inline > .ant-menu-submenu-title .ant-menu-submenu-arrow {
   background:url(/oneClickHelp/layout/menu-arrow.png);
   width: 16px;
   height: 16px;
   transform: translateY(-50%) rotate(0deg);
}
/deep/  .ant-menu-submenu-open.ant-menu-submenu-inline > .ant-menu-submenu-title .ant-menu-submenu-arrow{
  transform: rotate(180deg) translateY(50%);
}
/deep/ .ant-menu-submenu-selected .ant-menu-submenu-title{
   color: #66ffff;
  font-weight: 700;
}

</style>