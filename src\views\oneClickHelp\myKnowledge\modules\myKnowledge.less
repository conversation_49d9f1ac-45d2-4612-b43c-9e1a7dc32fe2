@primaryColor: #409eff;


.rightOne-item-left-one {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    font-weight: 600;
    font-size: 16px;
    width: calc(100% - 46px);

    .rightOne-item-top {
        width: 100%;
        display: flex;
        align-items: flex-end;
        overflow: hidden;
        /*溢出的部分隐藏*/
        white-space: nowrap;
        /*文本不换行*/
        text-overflow: ellipsis;
        /*ellipsis:文本溢出显示省略号*/

        .rightOne-item-top-title {
            max-width: 70%;
            font-size: 16px;
            line-height: 18px;
            font-weight: 600;
            margin-right: 25px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            cursor: pointer;
        }

        .badge {
            max-width: 170px;
            font-size: 13px;
            font-weight: 400;
            color: @primaryColor;
            border: 1px solid @primaryColor;
            padding: 2px 15px 2px 15px;
            border-radius: 6px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }


    .rightOne-item-time {
        display: flex;

        .rightOne-item-top-time {
            font-size: 12px;
            line-height: 18px;
            font-weight: 400;
            margin-top: 10px;
            margin-right: 30px;
        }
    }
}
.caozuo {
    display: flex;
    align-items: center !important;
    .action-icon {
        display: inline-block;
        font-size: 21px;
        font-weight: bold;
        color: @primaryColor;
        cursor: pointer;
    }
}
.know

::v-deep .ant-table-tbody {
    background: rgba(13, 102, 200, 0.07);
}

::v-deep .ant-table-row-selected,
::v-deep .ant-table-tbody>tr.ant-table-row-selected td {
    background: rgba(13, 102, 200, 0.07) !important;
}

::v-deep .ant-table-tbody .ant-table-row td {
    padding: 19px 16px !important;
}
::v-deep .ant-table-middle {
    border: none !important;
}