<template>
  <div>
    <div v-if='dataSource.length>0' class='content-box'>
      <div class="title-box">
        <span class="title">历史记录</span>
      </div>
      <a-table
        ref='table'
        bordered
        :row-key='(record, index) => {return record.id}'
        :columns='columns'
        :dataSource='dataSource'
        :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
        :pagination='ipagination'
        :loading='loading'
        @change='handleTableChange'>
      </a-table>
    </div>
  </div>
</template>
<script>
import {JeecgListMixin} from '@/mixins/JeecgListMixin'
export default {
  name: "AlarmAssignmentHistory",
  components:{},
  mixins: [JeecgListMixin],
  props:{
    alarmInfo:{
      type:Object,
      required:true,
      default:{}
    }
  },
  data() {
    return {
      disableMixinCreated:true,
      columns: [
        {
          title: '操作名称',
          dataIndex: 'name'
        },
        {
          title: '操作人',
          dataIndex: 'assignName_dictText'
        },
        {
          title: '负责人',
          dataIndex: 'headName_dictText',
        },
        {
          title: '操作时间',
          dataIndex: 'createTime'
        }
      ],
      url: {
        list: '/alarm/assign/history/list',//列表
      }
    }
  },
  watch: {
    alarmInfo: {
      handler(val) {
        this.queryParam={}
        this.dataSource=[]
        if (Object.keys(val).length > 0) {
          this.queryParam.alarmId = val.id
          this.loadData(1)
        }
      },
      deep: true,
      immediate: true
    }
  }
}
</script>

<style scoped lang="less">
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
.content-box{
  margin-bottom: 20px;
  .title-box {
    margin-bottom: 10px;
  }

  .title {
    padding-left: 7px;
    border-left: 4px solid #1e3674;
  }
}
</style>