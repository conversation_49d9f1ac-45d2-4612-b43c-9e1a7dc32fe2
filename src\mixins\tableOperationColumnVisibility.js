import { duplicateCheck, queryConfigureDictItem } from '@api/api'

export const tableOperationColumnVisibility = {
  data() {
    return {}
  },
  methods: {
    setTabelOperationCol(parentCode,childCode,colWidth=150){
      this.getConfigDictAboutOperColVisibility(parentCode, childCode).then((res) => {
        if (res.success) {
          let isExist = false
          for (let i = 0; i < this.columns.length; i++) {
            if (this.columns[i].dataIndex === "action") {
              isExist = true
            }
          }
          if (res.visibilty && !isExist) {
            this.columns.push(
              {
                title: '操作',
                dataIndex: 'action',
                fixed: 'right',
                width: colWidth,
                align: 'center',
                scopedSlots: { customRender: 'action' }
              })
          }
          if (!res.visibilty && isExist) {
            this.columns.splice(this.columns.length - 1, 1)
          }
        }
      })
    },
    getConfigDictAboutOperColVisibility(parentCode, childCode) {
      return new Promise((resolve) => {
        if (parentCode && parentCode.length > 0 && childCode && childCode.length > 0) {
          queryConfigureDictItem({
            parentCode: parentCode,
            childCode: childCode
          }).then((res) => {
            if (res.success) {
              let vis=res.result=='1'?true:false
              resolve({ success: true, visibilty: vis, message: res.message })
            } else {
              resolve({ success: true, visibilty: false, message: '操作成功' })
            }
          }).catch((err) => {
            resolve({ success: true, visibilty: false, message: '操作成功' })
          })
        } else {
          resolve({ success: true, visibilty: false, message: '操作成功' })
        }
      })
    }
  }
}