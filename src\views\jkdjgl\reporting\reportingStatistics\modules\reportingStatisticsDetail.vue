<template>
  <a-card :bordered='false'>
    <div class='action'>
      <span class='edit'>
        详情信息
      </span>
      <span class='return'>
        <img src='~@/assets/return1.png' alt='' @click='getGo'>
      </span>
    </div>
    <a-descriptions :column='{ xxl: 2, xl: 2, lg: 2, md: 2, sm: 2, xs: 2 }' bordered>
      <a-descriptions-item label='任务名称' >{{ data.taskName }}</a-descriptions-item>
      <a-descriptions-item label='目标数据表' >{{ data.targetTable }}</a-descriptions-item>
      <a-descriptions-item label='唯一字段' >{{ data.uniqueColumn }}</a-descriptions-item>
      <a-descriptions-item label='实体类全类名'>{{ data.dataClass }}</a-descriptions-item>
      <a-descriptions-item label='服务类全类名' >{{ data.serviceClass }}</a-descriptions-item>
      <a-descriptions-item label='执行频率'>{{data.executeCron}}</a-descriptions-item>
      <a-descriptions-item label='任务状态' >{{data.taskStatus0 ? '禁用' : '启用'}}</a-descriptions-item>
      <a-descriptions-item label='时间字段'>{{data.timeColumn}}</a-descriptions-item>
      <a-descriptions-item label='开始时间'>{{data.startTime}}</a-descriptions-item>
      <a-descriptions-item label='结束时间'>{{data.endTime}}</a-descriptions-item>
      <a-descriptions-item label='备注'>{{data.description}}</a-descriptions-item>
    </a-descriptions>
  </a-card>
</template>

<script>
  export default {
    name: 'data',
    data() {
      return {}
    },
    props: {
      data: {
        type: Object,
        required: false,
        default: () => {
          return {}
        }
      }
    },
    watch: {
      data: {
        handler(val) {
          this.data = val
        },
        deep: true,
        immediate: true
      }
    },
    mounted() {
      console.log(this.data, 'data');

    },
    methods: {
      //返回上一级
      getGo() {
        this.$parent.pButton2(0)
      }
    }
  }
</script>

<style scoped lang='less'>
  .action {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-flow: row nowrap;
    margin-bottom: 12px;

    .edit {
      margin-left: 10px;

      .icon {
        color: #409eff;
        margin-right: 6px
      }
    }

    .return {
      img {
        width: 20px;
        height: 20px;
        cursor: pointer
      }
    }
  }

  ::v-deep .ant-descriptions-view {
    border-radius: 0px;
  }

  ::v-deep .ant-descriptions-bordered .ant-descriptions-item-label {
    background-color: rgb(250, 250, 250);
    text-align: center;
    width: 17%;
  }

  ::v-deep .ant-descriptions-item-label,
  .ant-descriptions-item-content {
    color: rgb(96, 98, 102) !important;
  }

  ::v-deep .ant-descriptions-bordered .ant-descriptions-item-content {
    word-break: break-word;
    white-space: normal;
  }
</style>