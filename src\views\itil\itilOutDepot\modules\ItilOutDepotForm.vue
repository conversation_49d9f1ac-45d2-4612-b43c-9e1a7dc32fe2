<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="12">
            <a-form-item label="出库日期" :labelCol="{ span: 4 }" :wrapperCol="{ span: 20 }">
              <j-date
                placeholder="请选择出库日期"
                v-decorator="['outTime', { rules: [{ required: true, message: '请选择出库日期' }] }]"
                :trigger-change="true"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24" class="operSupplier">
            <a-row style='width: 100%'>
              <a-col :span='12'>
                <a-form-item label="出库类型" :labelCol="{ span: 4 }" :wrapperCol="{ span: 20 }">
                  <j-dict-select-tag
                    type="radio"
                    v-decorator="['outType', { rules: [{ required: true, message: '请选择出库类型' }] }]"
                    :trigger-change="true"
                    dictCode="out_type"
                    placeholder="请选择出库类型"
                    @change="radioChange"
                  />
                </a-form-item>
              </a-col>
              <a-col :span='12' v-if="outTypeRadio == '内部领用'">
                <a-form-item label="领用人"  style='float: right' :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-select
                    v-decorator="['outTargetUser', { rules: [{ required: true, message: '请选择领用人' }] }]"
                    :allowClear="true"
                    style="width: 200px"
                  >
                    <a-select-option v-for="(item, index) in receiverData" :key="index" :value="item.id">
                      {{ item.realname }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span='12' v-if="outTypeRadio == '退货出库'">
                <a-form-item label="供应商" style='float: right' :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-select
                    v-decorator="['outTargetSupplier', { rules: [{ required: true, message: '请选择供应商' }] }]"
                    :allowClear="true"
                    style="width: 200px"
                  >
                    <a-select-option v-for="(item, index) in supplierData" :key="index" :value="item.id">
                      {{ item.name }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span='12' v-if="outTypeRadio == '其他出库'" >
                <a-form-item label="出库对象" style='float: right' :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input
                    v-decorator="['outTarget', { rules: [{ required: true, message: '请输入出库对象' }] }]"
                    :allowClear="true"
                    placeholder="请输入出库对象"
                  ></a-input>
                </a-form-item>
              </a-col>
            </a-row>
          </a-col>
          <a-col :span="24">
            <div class="a-col-core">
              <ul>
                <li>
                  <div>序号</div>
                  <div>
                    <img src="@/assets/Mandatory.png" alt="" />
                    物品编号
                  </div>
                  <div>物品名称</div>
                  <div>当前库存</div>
                  <div>单位</div>
                  <div>
                    <img src="@/assets/Mandatory.png" alt="" />
                    数量
                  </div>
                  <div>单价(￥)</div>
                  <div>金额(￥)</div>
                  <div>备注</div>
                  <div>操作</div>
                </li>
                <li v-for="(list, index) in coreTable" :key="index">
                  <div>{{ index + 1 }}</div>
                  <div>
                    <a-select
                      v-model="list.goodsCode"
                      show-search
                      placeholder="请选择物品编号"
                      option-filter-prop="children"
                      style="width: 100%"
                      :filter-option="filterOption"
                      @change="handleChange(index, $event)"
                    >
                      <a-select-option v-for="(item, index) in selectData" :key="index" :value="item.id">
                        {{ item.goodsCode }}
                      </a-select-option>
                    </a-select>
                  </div>
                  <div>{{ list.goodsName }}</div>
                  <div>{{ list.stockNum }}</div>
                  <div>{{ list.unitCode }}</div>
                  <div>
                    <!-- <a-input v-if="list.goodsCode" size="small" v-model="list.operateNum"></a-input> -->
                    <a-input-number
                      v-if="list.goodsCode"
                      v-model="list.operateNum"
                      :precision="0"
                      size="small"
                      style="width: 80%"
                      :min="1"
                      :max="Number(list.stockNum)"
                      @blur="animateWidth()"
                    />
                  </div>
                  <div>
                    {{ list.unitPrice }}
                  </div>
                  <div style="text-align: center;">
                    <span v-if="list.operateNum && list.unitPrice">
                      {{ (list.moneys = list.operateNum * list.unitPrice).toFixed(2) }}
                    </span>
                  </div>
                  <div>
                    <span>
                      {{ list.remarks }}
                    </span>
                  </div>
                  <div>
                    <a-popconfirm v-if="list.goodsCode" title="确定删除吗?" @confirm="() => handleDelete(index)">
                      <a style="color: #409eff">删除</a>
                    </a-popconfirm>
                  </div>
                </li>
              </ul>
            </div>
          </a-col>
          <a-col :span="24" class="operSupplier">
            <a-form-item label="出库人" class="Center">
              {{ nickname() }}
            </a-form-item>
            <a-form-item v-if="totalMoney" label="总金额" class="Center">
              <span> ￥{{ totalMoney1 }}元 </span>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="备注信息" :labelCol="{ span: 2 }" :wrapperCol="{ span: 22 }">
              <a-textarea
                v-decorator="['remarks', { rules: [{ max: 500, message: '备注信息长度不超过500字符' }] }]"
                placeholder="请输入备注信息"
                :auto-size="{ minRows: 3, maxRows: 8 }"
                rows="1"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import pick from 'lodash.pick'
import { validateDuplicateValue } from '@/utils/util'
import JFormContainer from '@/components/jeecg/JFormContainer'
import JDate from '@/components/jeecg/JDate'
import JDictSelectTag from '@/components/dict/JDictSelectTag'
import { mapActions, mapGetters, mapState } from 'vuex'

export default {
  name: 'ItilOutDepotForm',
  components: {
    JFormContainer,
    JDate,
    JDictSelectTag,
  },
  props: {
    //流程表单data
    formData: {
      type: Object,
      default: () => {},
      required: false,
    },
    //表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false,
    },
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false,
    },
  },
  data() {
    return {
      form: this.$form.createForm(this),
      model: {},
      labelCol: {
        xs: { span: 7 },
      },
      wrapperCol: {
        xs: { span: 17 },
      },
      totalMoney1: '',
      confirmLoading: false,
      validatorRules: {},
      url: {
        add: '/itilOutDepot/itilOutDepot/add',
        edit: '/itilOutDepot/itilOutDepot/edit',
        queryById: '/itilOutDepot/itilOutDepot/queryById',
      },
      receiverData: [],
      supplierData: [],
      selectData: [],
      coreTable: [],
      totalMoney: '',
      outTypeRadio: '',
    }
  },
  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    },
  },
  created() {
    //如果是流程中表单，则需要加载流程表单data
    this.showFlowData()
  },
  methods: {
    ...mapGetters(['nickname', 'avatar', 'userInfo']),
    getListItem() {
      return {
        goodsCode: '',
        goodsName: '',
        stockNum: '',
        unitCode: '',
        operateNum: '',
        unitPrice: '',
        moneys: '',
        remarks: '',
      }
    },
    radioChange(value) {
      this.outTypeRadio = value
    },
    add() {
      this.totalMoney = ''
      this.coreTable = []
      this.coreTable.push(this.getListItem())
      this.selectLists()
      this.queryAllSupplier()
      this.queryReceiver()
      // this.edit({})
    },
    edit(record) {
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(
          pick(this.model, 'outCode', 'outTime', 'outType', 'outTarget', 'totalMoney', 'remarks')
        )
      })
    },
    //渲染流程表单数据
    showFlowData() {
      if (this.formBpm === true) {
        let params = { id: this.formData.dataId }
        getAction(this.url.queryById, params).then((res) => {
          if (res.success) {
            this.edit(res.result)
          }
        })
      }
    },
    animateWidth() {
      let array = []
      for (var i = 0; i < this.coreTable.length; i++) {
        Number(this.coreTable[i].moneys)
        array.push(this.coreTable[i].moneys)
      }
      this.totalMoney = array.reduce(function (a, b) {
        return a + b
      }, 0)

      // formData.totalMoney = this.totalMoney
      this.totalMoney1 = parseFloat(this.totalMoney).toFixed(2)
    },

    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let formData = Object.assign(this.model, values)

          if (this.coreTable.length < 2) {
            this.$message.error('请选择物品编号')
            that.confirmLoading = false
            return
            // } else if (this.coreTable.goodsCode == '') {
            //   this.$message.error('请填写出库单')
          } else {
            this.coreTable.pop()
            let array = []
            for (var i = 0; i < this.coreTable.length; i++) {
              Number(this.coreTable[i].moneys)
              array.push(this.coreTable[i].moneys)
            }
            this.totalMoney = array.reduce(function (a, b) {
              return a + b
            }, 0)
            formData.totalMoney = this.totalMoney
            formData.itilDepotRelations = this.coreTable
            formData.inby = this.nickname()
            httpAction(httpurl, formData, method)
              .then((res) => {
                if (res.success) {
                  that.$message.success(res.message)
                  that.$emit('ok')
                  this.form.resetFields()
                } else {
                  that.$message.warning(res.message)
                  this.coreTable.push(this.getListItem())
                  this.selectLists()
                  this.queryAllSupplier()
                  this.form.resetFields()
                }
              })
              .finally(() => {
                that.confirmLoading = false
                this.form.resetFields()
              })
          }
        }
      })
    },

    popupCallback(row) {
      this.form.setFieldsValue(pick(row, 'outCode', 'outTime', 'outType', 'outTarget', 'totalMoney', 'remarks'))
    },

    queryAllSupplier() {
      getAction('/supplier/cmdbSupplier/queryAllSupplier').then((res) => {
        if (res.success) {
          this.supplierData = res.result
        }
      })
    },

    queryReceiver() {
      getAction('/itilOutDepot/itilOutDepot/receiver').then((res) => {
        if (res.success) {
          this.receiverData = res.result
        }
      })
    },

    selectLists() {
      getAction('/itilStockInfo/itilStockInfo/lists').then((res) => {
        if (res.success) {
          this.selectData = res.result
        }
      })
    },

    handleChange(idx, value) {
      getAction('/itilStockInfo/itilStockInfo/queryById', { id: value }).then((res) => {
        if (res.success) {
          Object.assign(this.coreTable[idx], res.result)
          if (this.coreTable[idx + 1]) {
          } else {
            this.coreTable.push(this.getListItem())
          }
        }
      })
      getAction('/itilOutDepot/itilOutDepot/unitPrice', { goodsCode: value }).then((res) => {
        if (res.success) {
          this.coreTable[idx].unitPrice = res.result
        }
      })
    },
    handleDelete(index) {
      this.coreTable.splice(index, 1)
      this.animateWidth()
    },
    filterOption(input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
    },
  },
}
</script>
<style lang="less" scoped>
@media (max-width: 575px){
  ::v-deep .ant-form-item-label {
    display: block;
    margin: 0;
    padding: 0 0 8px;
    line-height:40px;
    white-space: initial;
    text-align: left;
  }
}
.operSupplier {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .operSupplierId {
    display: flex;
    align-items: center;
  }
}
li {
  list-style: none;
}
.Center {
  display: flex;
  align-items: center;
  span {
    color: red;
  }
}
.a-col-core {
  ul {
    border: 1px solid #e8e8e8;
    padding: 0;
    li {
      width: 100%;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1px solid #e8e8e8;
      div {
        width: 8.8%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        border-right: 1px solid #e8e8e8;
        span {
          width: 80%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
      div:last-of-type {
        border: none;
      }
      div:nth-child(2) {
        width: 20%;
        padding: 0 1%;
      }
    }
    li:last-of-type {
      border: none;
    }
  }
}
/deep/ .ant-select-selection {
  width: 100%;
}
::v-deep .ant-col .ant-col-2 {
  text-align: left;
}
</style>
