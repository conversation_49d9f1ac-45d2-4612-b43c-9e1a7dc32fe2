export default {
  data() {
    return {
      tipLeft: -10000,
      tipTop: -10000,
      tipWidth: 0,
      tipHeight: 0,
      tipShow: false,
      cellInfo: null,
      tipData: null,
      tipZindex: -1,
      tipShowTimer: null,
      tipHideTimer: null,
      tipReady: false,
      nodeHl: { body: { opacity: 1 }, label: { opacity: 1 }, image: { opacity: 1 } },
      nodeTr: { body: { opacity: 0.1 }, label: { opacity: 0.1 }, image: { opacity: 0.1 } }
    }
  },
  methods: {
    //鼠标移入tip卡片
    enterTipCard(e) {
      this.clearHideTimer()
    },
    //鼠标移出tip卡片
    leaveTipCard() {
      this.clearHideTimer()
      this.tipHideTimer = setTimeout(() => {
        this.clearHideTimer()
        let node = this.cellInfo
        this.toolTipHide()
        node.attr('body/filter', {
          name: 'dropShadow',
          args: {
            dx: 0,
            dy: 0,
            blur: 0
          }
        })
        let edges = this.graph.getEdges()
        edges.forEach(edge => {
          edge.setAttrs({ line: { strokeOpacity: 1 } })
          if (edge.getLabelAt(0)) {
            let label = JSON.parse(JSON.stringify(edge.getLabelAt(0)))
            label.attrs.label.fill = 'rgba(0, 0, 0, 0.8)'
            edge.setLabelAt(0, label)
          }

        })
        let nodes = this.graph.getNodes()
        nodes.forEach(node => {
          node.setAttrs(this.nodeHl)
        })
      }, 150)

    },
    //节点或边线点击的事件
    cellClick() {
      if (this.cellInfo.shape === 'edge') {
        this.toolTipHide()
        let sourceNode = this.graph.getCellById(this.cellInfo.source.cell)
        let targetNode = this.graph.getCellById(this.cellInfo.target.cell)
        const params = {
          linkInfo: this.cellInfo.data,
          sourceData: sourceNode.data,
          targetData: targetNode.data
        }
      }

    },
    clearShowTimer() {
      if (this.tipShowTimer) {
        clearTimeout(this.tipShowTimer)
        this.tipShowTimer = null
      }
    },
    clearHideTimer() {
      if (this.tipHideTimer) {
        clearTimeout(this.tipHideTimer)
        this.tipHideTimer = null
      }
    },
    // 监听画布节点、边自定义事件
    setup() {
      // 监听节点鼠标点击事件
      this.graph.on('node:click', ({ node }) => {
        this.toolTipHide()
        let size = node.size()
        let scale = this.graph.scale()
        const menuSize = {
          w: size.width * scale.sx,
          h: size.height * scale.sy
        }
        let tp = this.graph.localToClient(node.position())
        const menuPos = {
          left: tp.x,
          top: tp.y
        }
        if(node.data === null || node.data === undefined) return
        if(this.topoLevel === 1 && node.data.root) return
        // console.log("node data === ", node.data)
        if(this.topoLevel === 1 && node.data.level == 3){
          this.topoLevel =2;
          this.renderNodes = this.originResource.find(el=>el.id==node.data.id)
          this.destroyTopo()
          this.initNetworkTopo()
          this.nodeAnimate()
          return;
        }else if(this.topoLevel === 2 && node.data.root){
          this.topoLevel =1;
          let tem = JSON.parse(JSON.stringify(this.organizations))
          this.renderNodes = this.filterTreeNodes([tem], node => node.level <= 3)[0]
          this.destroyTopo()
          this.initNetworkTopo()
          this.nodeAnimate()
          return;
        }
        this.$router.push({
          path: '/static/operationsView/comprehensive',
          query: {
            id: node.data.id,
          }
        })
      })
      this.graph.on('node:mouseenter', ({ e, node, view }) => {
        if (node.data === null || node.data === undefined) return
        if(node.data.root)return;
        this.clearShowTimer()
        this.clearHideTimer()
        this.tipShowTimer = setTimeout(() => {
          // console.log("node ====",node)
          clearTimeout(this.tipShowTimer)
          this.tipShowTimer = null
          this.cellInfo = node
          this.tipData = node.data
          this.tipData.shape = 'node'
          const nodeView = this.graph.findView(node)
          const svgNode = nodeView.container
          const bbox = svgNode.getBBox()
          // let size = node.size()
          let scale = this.graph.scale()
          this.tipWidth = bbox.width * scale.sx
          this.tipHeight = bbox.height * scale.sy
          let tp = this.graph.localToClient(node.position())
          this.tipLeft = tp.x
          this.tipTop = tp.y
          this.tipZindex = -1
          this.tipShow = true
          //节点阴影滤镜
          node.attr('body/filter', {
            name: 'dropShadow',
            args: {
              color: node.attrs.body.fill,
              dx: 0,
              dy: 0,
              blur: 3
            }
          })
          //展示当前节点关联节点和连线
          let edges = this.graph.getEdges()
          let cEdges = this.graph.getConnectedEdges(node)
          edges.forEach(edge => {
            if (!cEdges.includes(edge)) {
              edge.setAttrs({ line: { strokeOpacity: 0.1 } })
              if (edge.getLabelAt(0)) {
                let label = JSON.parse(JSON.stringify(edge.getLabelAt(0)))
                label.attrs.label.fill = 'rgba(0, 0, 0, 0.1)'
                edge.setLabelAt(0, label)
              }
            } else {
              edge.setAttrs({ line: { strokeOpacity: 1 } })
            }
          })
          let nodes = this.graph.getNodes()
          nodes.forEach(el => {
            if (cEdges.length > 0 && !this.graph.isNeighbor(node, el)) {
              el.setAttrs(this.nodeTr)
            } else if (cEdges.length === 0 && el !== node) {
              el.setAttrs(this.nodeTr)
            } else {
              el.setAttrs(this.nodeHl)
            }
          })
        }, 100)
      })
      this.graph.on('node:mouseleave', ({ e, node, view }) => {
        if (node.data === null || node.data === undefined) return
        this.clearShowTimer()
        this.clearHideTimer()
        this.tipHideTimer = setTimeout(() => {
          clearTimeout(this.tipHideTimer)
          this.tipHideTimer = null
          this.toolTipHide()
          node.attr('body/filter', {
            name: 'dropShadow',
            args: {
              dx: 0,
              dy: 0,
              blur: 0
            }
          })
          let edges = this.graph.getEdges()
          edges.forEach(edge => {
            edge.setAttrs({ line: { strokeOpacity: 1 } })
            if (edge.getLabelAt(0)) {
              let label = JSON.parse(JSON.stringify(edge.getLabelAt(0)))
              label.attrs.label.fill = 'rgba(0, 0, 0, 0.8)'
              edge.setLabelAt(0, label)
            }

          })
          let nodes = this.graph.getNodes()
          nodes.forEach(node => {
            node.setAttrs(this.nodeHl)
          })
        }, 150)

      })
      /*
       this.graph.on('edge:mouseenter', ({e, edge, view}) => {
         // console.log("eeee ==> ", e,edge)
         this.cellInfo = edge;
         this.tipData = edge.data;
         this.tipData.shape = "edge"
         this.tipWidth = 24;
         this.tipHeight = 24;
         this.tipLeft = e.pageX - 12;
         this.tipTop = e.pageY - 12;
         this.tipZindex = 1000000
         this.tipShow = true;
       })
      */

      /* this.graph.on('edge:mouseleave', ({e, edge, view}) => {
       })*/

    },
    toolTipHide() {
      this.tipLeft = -10000
      this.tipTop = -10000
      this.tipWidth = 0
      this.tipHeight = 0
      this.tipShow = false
      this.tipZindex = -1
      this.enterPointer = null
      this.tipReady = false
    }
  }
}