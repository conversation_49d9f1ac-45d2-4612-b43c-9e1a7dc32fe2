window.config = {
  /*
 sysType取值意义
 0:存在模块入口首页，
 1:无模块入口首页，直接打开数据中心，默认显示菜单一功能页面, 遵义项目 为 "1"时必须为在用户管理中为用户设定上级身份.
 2:无模块入库首页，通过设置数据字典子系统数据数据顺序确定登录首入排序第一的子系统
 3:进入服务中心，(此类型删除，和类型2合并 需要将数据字典平台子系统服务中心的排序值调整为最小）
 */
  sysType: '2',
  platformType: '系统配置', // sysType为1时，进入管理页面的名称
  // apiUrl: "http://localhost:8092/insight-api",
  //apiUrl:"http://*************:8092/insight-api",//钰斌本地
  // apiUrl:"http://*************:8091/insight-api",
  //apiUrl:"http://*************:8091/insight-api",
  // apiUrl:"http://*************:8091/insight-api",//鹏东本地
  // apiUrl:"http://*************/insight-api",//93测试后台
   apiUrl:"http://*************9/insight-api",
  // apiUrl:"http://*************/insight-api",
  // apiUrl:"http://*************3:81/insight-api",
  //casUrl:"http://*************:8443/cas",
  // casSSo: 'true',
  //网页标题配置
  titleConfig:
  {
    // title: "远桥运维监控平台",
    title: "智能运维监控平台",
    icon: 'configImg/titleIcon1.png',//山东远桥logo
    // icon: 'configImg/logo-white2.png',//北京远桥logo
  },

  //登录页图标 UserLayout.vue
  login: {
    logoName: "智能运维监控平台", //远桥运维监控平台
    loginDesc: "科技创新&nbsp;自主可控",
    loginUrl: "/configImg/logo.png", //54*54  山东远桥logo
    // loginUrl: "/configImg/logo2.png",
    loginNameUrl: '/configImg/logoName.png', // 450*53
    // loginNameUrl: '/configImg/logoName-blankSpace.png', // 450*53  空白图
    loginBottomUrl: '/configImg/logoBottom.png',//北京远桥logo7
    // loginSysName: 'Copyright &copy; 2021 北京远桥科技有限公司 出品',
    loginSysName: 'Copyright &copy; 2021 山东远桥信息科技有限公司 出品',
  },
  //入口首页 gateway.vue
  gateway: {
    gatewaySysName: '智能运维监控平台', // 远桥运维监控平台
    gatewayBtmSysName: '智能运维监控平台', // 远桥运维监控平台
    gatewayBtmEnSysName: 'Operation and maintenance monitoring platform'
  },
  //系统左上角 Logo.vue
  sysConfig: {
    logourl: '/configImg/titleIcon1.png', //78*78  山东远桥logo
    // logourl: '/configImg/logo-white2.png',//北京远桥logo
    systemName: '智能运维监控平台' //远桥运维监控平台
  },
  //大屏左上角 BigScreenData.vue
  bigscreen: {
    bigScreenLogoUrl: '/configImg/logo.png', //54*54  山东远桥logo
    // bigScreenLogoUrl: '/configImg/logo2.png',//北京远桥logo
    bigScreenSysName: '智能运维监控平台'//远桥运维监控平台
  },
  //终端注册 activation.vue
  activation: {
    activationLogoUrl: '/configImg/logo.png', //234*228   山东远桥logo
    // activationLogoUrl: '/configImg/logo-white2.png',//北京远桥logo
    activationSysName: '智能运维监控平台',//中文系统名称a
    activationEnSysName: 'yuan qiao Operation and maintenance monitoring platform',
  },
  //一键帮助 oneClickHelp/index.vue
  oneClickHelp: {
    isNew: false,//false 使用老页面  true 使用新页面
    helpLogoUrl:'/configImg/logo.png',//234*228  山东远桥logo
    // helpLogoUrl: '/oneClickHelp/nmlogo.png', //运维助手logo
    pageLogoUrl: "/configImg/logo.png",  //山东远桥logo
    // pageLogoUrl: "/oneClickHelp/nmlogo1.png",
    pageStyle: "multiple",// 页面展示类型 single 单一  multiple多
    title: "运维助手",
    platformTitle: "信创一体化运维保障平台",
    bottomTitle: "山东远桥信息科技有限公司",
    telphone: '************',
    pageLogo: "/configImg/logo.png", //山东远桥logo
    // pageLogo: "/oneClickHelp/layout/logo.png",
    pageTitle:"信创运维小助手",//建议最多6个字
  },
  //省资源模式  0关闭，1开启
  simpleModel: 1,

  //logo显示  0不显示  1显示
  logoShow: 1,

  //资产编号自动生成 false不自动 true自动
  assetsAuto: false,

  // 各项目个性化配置
  customization: {
    // cz_+项目名
    // cz_yulin:{
    // }
    szBigTitle: "深圳市电子政务内网综合运维管理平台", // 深圳大屏顶部title
    szTitleImg: "/configImg/cattle.png", // 深圳大屏顶部logo
    //中软单点登录 三合一配置
    cz_zhongruan: {
      threeInOne:true,//是否需要进入三合一
      /*
      * 三合一子系统数组
      * name 子系统名称
      * type 系统类型 运维监控：yw ; 大屏可视化：ls ; 动态感知：sa;
      * link 子系统连接 运维监控不需要配置 格式如：http://*************9:8080?&access_token=
      * icon 子系统图标
      * iconH 子系统悬浮高亮图标
      * description 子系统描述
      * 调整数组顺序修改系统页面的排列顺序
      * */
      sysList:[
        {
          name:"运维监控",
          type:"yw",
          icon:"/threeInOne/tioYw.png",
          iconH:"/threeInOne/tioYwH.png",
          description:"统一运维管理和知识库系统",
        },
        {
          name:"大屏可视化",
          type:"ls",
          link:"http://*************9?&access_token=",
          icon:"/threeInOne/tioLs.png",
          iconH:"/threeInOne/tioLsH.png",
          description:"运维数据分析和可视化系统",
        },
        {
          name:"动态感知",
          type:"sa",
          link:"",
          icon:"/threeInOne/tioSa.png",
          iconH:"/threeInOne/tioSaH.png",
          description:"安全态势感知平台",
        },
      ],
      bottomText:"运维管理系统",
      serverUrl:"",//服务地址
      logoutUrl:"https://*************9:8080/api/sso/logout",//统一登录退出登录地址 末尾不加/   如： https://*************9:8080/api/sso/logout
      client_ip:"*************9",//客户端ip  终后端所在服务器的ip
      exitUrl:"http://localhost:3000/#/user/login",//退出登录跳转地址
    }
  },
  //多后台接口  如果需要从第三方后台获取数据配置第三方接口地址 不需要注释掉或者和apiUrl配置一样
  multipleURL: {
    // $UKEY: "/insight-api/sys/SZUKeyLogin",//ukey登录 完整的接口路径
    // $NWZHGL: "/nwzhgl-api",//http://*************1:9091
    // $INSIGHT: "/insight-api",//http://*************9
    // $YQSM: "http://*************:8081/yqsm-api",//http://*************1:277
    // $JKDJSTATICS:"http://*************:8091/insight-api",//监控对接统计
  },
  //是否ukey登录 0只能账号 1 只能ukey 2 ukey和账号
  UKey: 0,
  //应用系统唯一标识  应用系统编号，在维豪资源管理系统中注册后
  appUri: "",

  //数据中心布局类型
  //0-正常 数据中心 菜单平台类型设为4
  //1-统计中心 菜单平台类型设为8
  DataCenterType:0,
  //统计中心是否使静态数据页面 0 关闭 1 开启
  useStaticData: 1,

  //此值不为空字符串时 服务中心跳转此url页面
  //url字符串后面不带/
  //http://*************1:288
  serveCenterURL: "",
//拓扑图节点在线离线状态颜色 { onLine: "#52c41a", offLine: '#8c8c8c' }
  TopoStatuColors: { onLine: "#1BCB3C", offLine: '#FF1D42' },
  //拓扑图连线状态颜色  { connected: "#52c41a", notConnected: '#ff4d4f' }
  TopoLineColors: { connected: "#1BCB3C", notConnected: '#FF1E00' },
  //授权信息展示配置  0 隐藏部分信息  1 全部展示
  authType:1,
  ENABLE_SKYWALKING_REPORT: false, // 是否启动skywalking的浏览器监控
  subSystems:["1","2","3","4","5","6","8","11","13","14"], //子系统编号数组 控制需要显示那些已经授权的子系统
  subSystemUrls:{ //子系统需要跳到第三方的地址配置 不配置默认获取系统的菜单，key是子系统编码 value是跳转地址
    // "11":"http://localhost:3001/#/operationsView/compNational",
    // "11":"http://localhost:3000/#/static/operationsView/compNational",//静态大屏页面
    // "13":"http://**************:38080/ui/index.html#/login?target=blank&username=${username}&password=${password}", //admin  folib@v587
    // "14":"http://**************:30001/#/?target=self&username=${username}&password=${password}",  // boc 123456
  },
  // 运维态势静态页面
  ywtsStatic:false,//是否是静态大屏
}
