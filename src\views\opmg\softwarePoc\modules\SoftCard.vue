<template>
  <div class="box">
    <div class="logo-img">
      <img :src="downloadUrl + '/' + record.softwareLogo" />
    </div>
    <div class="soft-right">
      <div class="soft-text">
        <div class="soft-name">
          <span>{{ record.softwareName }}</span>
        </div>
        <div class="summary">
          <span>{{ record.softwareBrief }}</span>
        </div>
      </div>
      <div class="edit-btns">
        <div @click.stop="editHandle">
            <yq-icon type="edit" style="font-size: 15px;" />
        </div>
        <div @click.stop="deleteHandle" style="margin-left: 20px">
          <yq-icon type="delete" style="font-size: 16px;" />
        </div>
      </div>
      <div class="driverDown">升级包:{{ record.softwareUpgradePackageNum || 0 }}</div>
    </div>
  </div>
</template>

<script>
import yqIcon from '@comp/tools/SvgIcon'
export default {
  name: 'SoftCard',
  components: {
    yqIcon
  },
  props: {
    cpuList: {
      type: Array,
      default: () => [],
    },
    dictOptions: {
      type: Array,
      default: () => [],
    },
    record: {
      type: Object,
      default: '',
    },
    types: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      downloadUrl: window._CONFIG['downloadUrl'],
      cpuTexts: [],
    }
  },
  created() {},
  computed: {
    typeText() {
      return this.types.find((el) => el.value === this.record.softwareType)?.text
    },
    cpuText() {
      if (this.record.softwareCpuType) {
        let tem = []
        let arr = this.record.softwareCpuType.split(',')
        arr.forEach((el) => {
          tem.push(this.cpuList.find((item) => item.value === el)?.text)
        })
        return tem.join()
      }
      return '--'
    },
    osText() {
      if (this.record.softwareOsType) {
        let tem = []
        let arr = this.record.softwareOsType.split(',')
        arr.forEach((el) => {
          tem.push(this.dictOptions.find((item) => item.value === el)?.text)
        })
        return tem.join()
      }
      return '--'
    },
  },
  methods: {
    editHandle() {
      this.$emit('edit', this.record)
    },
    deleteHandle() {
      this.$confirm({
        content: '确定要删除吗?', // 也可以使用 slot
        okText: '确认',
        cancelText: '取消',
        onOk: () => {
          this.$emit('delete', this.record)
        },
      })
    },
  },
}
</script>

<style scoped lang="less">
.box {
  margin-top: 24px;
  width: 100%;
  display: flex;
  align-items: center;
  position: relative;
  cursor: pointer;
  .logo-img {
    display: flex;
    align-items: center;
    justify-content: center;
    img {
      width: 56px;
      height: 56px;
      border-radius: 10px;
    }
  }
  .soft-right {
    width: calc(100% - 56px - 18px);
    display: flex;
    align-items: center;
    padding: 15px 0;
    margin-left: 18px;
    border-bottom: 1px solid #e8e8e8;
    .soft-text {
      width: calc(100% - 90px - 100px);
      padding-right: 20px;
      flex: 1;
      .soft-name {
        width: 100%;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.65);
        font-weight: 500;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .summary {
        width: 100%;
        margin-top: 8px;
        font-size: 12px;
        color: rgba(0, 0, 0, 0.45);
        letter-spacing: 0.43px;
        font-weight: 400;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .edit-btns {
    width: 70px;
    opacity: 0;
    display: flex;
    justify-content: space-between;
    padding-right: 25px;
  }
  .driverDown {
    width: 100px;
    height: 33px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(184, 196, 203, 0.24);
    border-radius: 17px;
    cursor: pointer;
    font-size: 16px;
    color: #1890ff;
    font-weight: 400;
    white-space: nowrap;
  }
}
.box:hover {
  .edit-btns {
    opacity: 1;
  }
}
/* 隐藏最后一行的 border-bottom */
.last-row .soft-right {
  border-bottom: none !important;
}
</style>
