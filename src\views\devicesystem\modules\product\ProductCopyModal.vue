<template>
  <j-modal
    :confirmLoading='confirmLoading'
    :title="title"
    :width="modalWidth"
    :visible="visible"
    :centered='true'
    switchFullscreen
    :destroyOnClose='true'
    @ok="handleOk"
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @cancel="handleCancel"
    cancelText="关闭"
    :okText='okText'
  >
    <product-copy-form v-if='realForm==="copy"' :ref="realForm" @ok="submitCallback" @loading="loading" :disabled="disableSubmit"></product-copy-form>
    <product-export-form v-if='realForm==="export"' :ref="realForm" @ok="submitCallback" @loading="loading" :disabled="disableSubmit"></product-export-form>
    <product-import-form v-if='realForm==="import"' :ref="realForm" @ok="submitCallback" @loading="loading" :disabled="disableSubmit"></product-import-form>
  </j-modal>
</template>

<script>

  import ProductCopyForm from './ProductCopyForm'
  import ProductExportForm from './ProductExportForm'
  import ProductImportForm from './ProductImportForm'
  export default {
    name: 'ProductModal',
    components: {
      ProductCopyForm,
      ProductExportForm,
      ProductImportForm,
    },
    data () {
      return {
        title:'',
        modalWidth:'800px',
        visible: false,
        disableSubmit: false,
        okText:'',
        realForm:'',
        confirmLoading: false,
      }
    },
    methods: {
      loading (bool) {
        this.confirmLoading = bool
      },
      init () {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs[this.realForm].init();
        })
      },
      close () {
        this.$emit('close');
        this.visible = false;
      },
      handleOk () {
        this.$refs[this.realForm].submitForm();
      },
      submitCallback(){
        this.$emit('ok');
        this.visible = false;
        this.confirmLoading = false;
      },
      handleCancel () {
        this.close()
      }
    }
  }
</script>
<style scoped lang='less'>
@import '~@assets/less/normalModal.less';
</style>