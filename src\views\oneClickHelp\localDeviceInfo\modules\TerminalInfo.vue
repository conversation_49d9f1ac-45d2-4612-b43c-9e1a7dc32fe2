<template>
  <card-frame title='终端信息' :show-footer='true' :show-head-bg-img='true' @handleRefresh='handleRefresh' :show-refresh='true'>
    <div slot='bodySlot' class='bodySlot' v-if='!loading'>
      <div class='content-item' v-for='(item ,index) in info' :key='"content_"+index'>
        {{item.name}}：<span class='value'>{{item.value}}</span>
      </div>
    </div>
    <div slot='bodySlot' class='body-empty' v-else>
      <a-spin :spinning='loading' class='spin'></a-spin>
    </div>
  </card-frame>
</template>
<script>
import cardFrame from '@views/oneClickHelp/localDeviceInfo/modules/CardFrame.vue'
export default {
  name: "TerminalInfo",
  props: {
    info: {
      type: Array,
      required: true
    },
    loading: {
      type: Boolean,
      required: false,
      default: false
    }
  },
  components:{cardFrame},
  data() {
    return {
      title: '终端信息',
    }
  },
  methods:{
    handleRefresh(){
      this.$emit('handleRefresh')
    }
  }
}
</script>

<style scoped lang="less">
.bodySlot{
  height:100%;
  padding: 0.5rem 0.375rem;

  .content-item{
    font-size: 0.2rem;
    margin-bottom: 0.3rem;
    display: inline-block;
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    color: rgba(255,255,255,0.65);

    .value{
      color: #FFFFFF;
    }
  }
}
</style>