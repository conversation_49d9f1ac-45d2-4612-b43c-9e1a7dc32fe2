<template>
  <div style="height:100%">
    <component :is="pageName" :table-params='tableParams' :alarm-info="data" :data="data" />
  </div>
</template>
<script>
  import AlarmTemplateList from './AlarmTemplateList'
  import AlarmTemplateInfoModal from '@comp/alarmTemplate/AlarmTemplateInfoModal'
  export default {
    name: "AlarmTemplateManage",
    data() {
      return {
        isActive: 0,
        data: {},
        tableParams:{}
      };
    },
    components: {
      AlarmTemplateList,
      AlarmTemplateInfoModal
    },
    created() {
      this.pButton1(0);
    },
    //使用计算属性
    computed: {
      pageName() {
        switch (this.isActive) {
          case 0:
            return "AlarmTemplateList";
            break;
          default:
            return "AlarmTemplateInfoModal";
            break;
        }
      }
    },
    methods: {
      pButton1(index, item) {
        this.isActive = index;
        this.data = item;
      },
      pButton2(index, item) {
        this.isActive = index
        this.data = item.data
        this.tableParams={
          ipagination : item.ipagination,
          queryParam:item.queryParam
        }
      }
    }
  }
</script>