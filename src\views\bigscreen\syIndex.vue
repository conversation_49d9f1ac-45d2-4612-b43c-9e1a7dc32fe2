<template>
  <div class="background">
    <a-row
      :gutter="16"
      class="row-class"
    >
      <a-col
        :span="17"
        style="height:100%"
      >
        <div class="topo-class" >
          <vis-edit v-if="nettopoList.length > 0" ref="bigScreen" operate="show" topoBgByTheme ></vis-edit>
          <!-- <topo-big-screen-sx
            ref="bigScreen"
            v-if="nettopoList.length > 0"
            style="height: 100%;width: 100%"
          ></topo-big-screen-sx> -->
        </div>
      </a-col>
      <a-col
        :span="7"
        style="height:100%"
      >
        <div class="right_box">
          <div class="right_top">
            <div class="top_title">
              <span class="title">设备性能监控</span>
            </div>
            <div class="equipment_box">
              <div
                class="core-core-body-circularGraph"
                id="equipmentCircularGraph"
              ></div>
            </div>
          </div>
          <div class="right_center">
            <div class="top_title">
              <span class="title">设备告警TOP5</span>
            </div>
            <div class="alarmTOP_box">
              <div
                class="core-bottom-body-left"
                id="resourcesWarningHistogram"
              ></div>
            </div>
          </div>
          <div class="right_bottom">
            <div class="top_title">
              <span class="title">设备总览概况</span>
            </div>
            <div class="Overview_box">
              <vue-seamless-scroll
                :data="resourcesData"
                :class-option="warning"
                class="seamless"
              >
                <div
                  class="Overview_core"
                  v-for="item in resourcesData"
                  :key="item.id"
                >
                  <div class="title1">{{item.name}} :</div>
                  <div class="title2">{{item.value}}</div>
                  <div class="title1">台</div>
                </div>
              </vue-seamless-scroll>
            </div>
          </div>
        </div>
      </a-col>
      <div class="bottom_bg"></div>
    </a-row>
  </div>
</template>

<script>
import echarts from 'echarts'
import 'echarts/lib/component/graphic'
import vueSeamlessScroll from 'vue-seamless-scroll'
import { getAction } from '@/api/manage'
import VisEdit from '@/views/topo/nettopo/modules/VisEdit.vue'
export default {
  components: {
    vueSeamlessScroll,
    VisEdit,
  },
  data() {
    return {
      nettopoList: [],
      num: 0,
      resourcesData: [],
      netPageNo: 1,
      url: {
        list: '/topo/topoInfo/list', // 拓扑图数据
        alarmTop: '/data-analysis/device/alarm/top', //告警TOP5
        status: '/data-analysis/device/status', //设备性能监控
        overview: '/data-analysis/device/overview', //设备总览
      },
    }
  },
  computed: {
    warning() {
      return {
        step: 0.2, // 数值越大速度滚动越快
        limitMoveNum: 4, // 开始无缝滚动的数据量 this.dataList.length
        hoverStop: true, // 是否开启鼠标悬停stop
        direction: 1, // 0向下 1向上 2向左 3向右
        // openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 86, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
        // singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
        waitTime: 2, // 单步运动停止的时间(默认值1000ms)
      }
    },
  },
  mounted() {
    this.getNetTopoList()
    this.alarmTop()
    this.status()
    this.overview()
  },
  methods: {
    //拓扑数据
    getNetTopoList() {
      getAction(this.url.list, { topoType: '0', showType: '1', pageNo: this.netPageNo, pageSize: 30 }).then((res) => {
        if (res.success) {
          this.netPageNo += 1
          this.netPapeTotalNo = Math.ceil(res.result.pageList.total / 10)
          this.nettopoList = [...this.nettopoList, ...res.result.pageList.records]
          if (this.nettopoList.length > 0) {
            setTimeout(() => {
              this.hoverHeight = ([document.getElementsByClassName('img-div')][0].clientWidth / 5) * 3 + 'px'
            }, 50)
            this.$nextTick(() => {
              if (this.netPageNo == 2) {
                this.$refs.bigScreen.createTopo(this.nettopoList[0].id)
              }
            })
          }
        }
      })
    },
    // 设备总览数据
    overview() {
      getAction(this.url.overview).then((res) => {
        if (res.code == 200) {
          this.resourcesData = res.result
          res.result.forEach((ele) => {
            this.num += ele.value
          })
        }
      })
    },
    // 告警TOP5
    alarmTop() {
      getAction(this.url.alarmTop).then((res) => {
        if (res.code == 200) {
          this.resourcesWarningHistogram(res.result)
        }
      })
    },
    // 设备状态监控数据
    status() {
      getAction(this.url.status).then((res) => {
        if (res.success) {
          this.equipmentCircularGraph(res.result)
        }
      })
    },
    // 设备状态监控饼图
    equipmentCircularGraph(data) {
      for (let i = 0; i < data.length; i++) {
        data[i].value = data[i].values[0].value
      }

      let myChart = this.$echarts.init(document.getElementById('equipmentCircularGraph'))
      myChart.setOption({
        tooltip: {
          show: true,
          transitionDuration: 0, //echart防止tooltip的抖动
          formatter(data) {
            let value = data.name + ':' + data.percent + '%'
            return value
          },
        },
        series: [
          {
            type: 'pie',
            radius: ['0%', '80%'],
            center: ['50%', '50%'],
            color: ['#7AED82', '#F19610', '#0086FF', '#ac3a5c'],
            labelLine: {
              length: 5,
              show: true,
            },
            label: {
              fontSize: 10,
              formatter(data) {
                let value = data.name + ':' + data.percent + '%'
                return value
              },
            },
            data: data,
          },
        ],
      })
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    },
    // 告警TOP5柱状图
    resourcesWarningHistogram(data = []) {
      function attackSourcesDataFmt(sData) {
        var sss = []
        sData.forEach(function (item, i) {
          if (item.value == undefined || item.value == null || item.value == '') {
            item.value = 0
          }
          sss.push({
            value: item.value + '%',
          })
        })
        return sss.reverse()
      }
      let arr = []
      let brr = []
      if (typeof data == 'object' && data.length > 0) {
        data.forEach((e) => {
          arr.push(e.name)
          brr.push(e.value)
        })
        // brr.reverse()
        let myChart = this.$echarts.init(document.getElementById('resourcesWarningHistogram'))
        myChart.setOption({
          tooltip: {
            show: true,
            trigger: 'axis',
            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
            },
            transitionDuration: 0, //echart防止tooltip的抖动
          },
          xAxis: {
            type: 'value',
            max: '100',
            splitLine: {
              show: false,
            }, //去除网格线
            show: false,
          },
          yAxis: [
            {
              type: 'category',
              data: arr,
              splitLine: {
                show: false,
              }, //去除网格线
              axisTick: {
                show: false,
              },
              axisLine: {
                show: false, //y轴线消失
                lineStyle: {
                  //y轴字体颜色
                  color: '#f6f6f6',
                },
              },
              axisLabel: {
                inside: true,
                padding: [-30, 0, 0, 0],
              },
            },
            {
              type: 'category',
              inverse: true,
              axisTick: 'none',
              axisLine: 'none',
              show: true,
              axisLabel: {
                textStyle: {
                  color: '#00a1e7',
                  fontSize: '14',
                },
              },
              data: attackSourcesDataFmt(data),
            },
          ],

          grid: {
            top: 5,
            left: 20, // 调整这个属性
            right: 60,
            bottom: -5,
          },
          series: [
            {
              data: brr,
              type: 'bar',
              showBackground: true,
              backgroundStyle: {
                color: '#293E6C', //柱状图背景颜色
                barBorderRadius: 30,
              },
              barWidth: 10, //柱图宽度
              itemStyle: {
                emphasis: {
                  barBorderRadius: 30,
                },
                normal: {
                  barBorderRadius: [10, 10, 10, 10],
                  color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                    {
                      offset: 0,
                      color: '#3679fb',
                    },
                    {
                      offset: 1,
                      color: '#0cf6f7',
                    },
                  ]),
                },
              },
            },
          ],
        })
        window.addEventListener('resize', () => {
          myChart.resize()
        })
      }
    },
  },
}
</script>

<style lang="less" scoped>
::-webkit-scrollbar {
  display: none;
}
.row-class {
  width: 100%;
  height: 100%;
  padding: 24px 0;
  position: relative;
}
.bottom_bg {
  width: 100%;
  height: 25px;
  background: url('../.././assets/bigScreen/bottom_bg.png') center top no-repeat;
  z-index: 9;
  position: absolute;
  bottom: 20px;
}
.topo-class {
  width: 100%;
  height: 100%;
  padding: 20px;
  position: relative;
}
.Overview_box {
  display: flex;
  width: 100%;
  height: 90%;
  margin-top: 20px;
  overflow-x: auto;
  padding: 0 24px;

  .seamless {
    width: 100%;
    height: 100%;
  }
  .Overview_core {
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    width: 100%;
    margin-top: 12px;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 19px;

    .title1 {
      font-size: 14px;
      color: #ffffff;
      letter-spacing: 2px;
    }
    .title2 {
      font-size: 26px;
      color: #44f0ff;
      letter-spacing: 2px;
      margin: 0 10px;
    }
  }
}
.background {
  height: 100%;
  width: 100%;
  background: url('../.././assets/bigScreen/shenyangBg.png') center top no-repeat;
  padding: 0 12px 0 38px;
}
.right_box {
  height: 98%;
  width: 100%;
  padding: 30px;
  background: url('../.././assets/bigScreen/rightBox.png') center top no-repeat;
  z-index: 2;
}
.title {
  font-size: 19px;
  color: #e2ebf1;
  margin-left: 20px;
}
.right_top {
  height: 32.8%;
  width: 100%;
}
.equipment_box {
  height: 80%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  .core-core-body-circularGraph {
    width: 100%;
    height: 100%;
  }
}
.top_title {
  height: 34px;
  width: 100%;
  /* line-height: 34px; */
  background: url('../.././assets/bigScreen/shenyangTitle.png') center top no-repeat;
  background-size: 100% 100%;
  z-index: 3;
}
.right_center {
  height: 35.6%;
  width: 100%;
}
.alarmTOP_box {
  width: 100%;
  height: 90%;
  display: flex;
  align-items: center;
  justify-content: center;

  .core-bottom-body-left {
    width: 100%;
    height: 80%;
  }
}
.right_bottom {
  height: 30%;
  width: 100%;
}
</style>