<template>
  <a-row style='height: 100%'>
    <a-col style='height: 100%; display: flex; flex-direction: column'>
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0', marginRight: '12px' }" class='card-style'>
        <!-- 查询区域 -->
        <div class='table-page-search-wrapper'>
          <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
            <a-row :gutter='24' ref='row'>
              <a-col :span="spanValue">
                <a-form-item label="关键字">
                  <a-input v-model='queryParam.keyword' :maxLength='maxLength' :allow-clear='true' autocomplete='off' placeholder='请输入关键字' />
                </a-form-item>
              </a-col>
              <a-col v-show="getVisible('name')" :span="spanValue">
                <a-form-item :label="getTitle('name')">
                  <a-input v-model="queryParam.processInstanceName"  :maxLength='maxLength' :allow-clear="true" autocomplete="off"
                          placeholder="请输入业务标题" />
                </a-form-item>
              </a-col>
              <a-col v-show="getVisible('startUserName')" :span="spanValue">
                <a-form-item :label="getTitle('startUserName')">
                  <a-select :getPopupContainer='node=>node.parentNode' :allow-clear='true' v-model="queryParam.startUserId"
                    show-search placeholder="请选择发起人" option-filter-prop="children" :filter-option="filterOption">
                    <a-select-option v-for="(item, key) in userList" :key="key" :value="item.username">
                      <div style="display: inline-block; width: 100%" :title="item.realname">
                        {{ item.realname }}
                        <span style="font-size: 6px; color: rgba(0, 0, 0, 0.45);">{{
                            '(' + item.username + ')'
                          }}</span>
                      </div>
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col v-show="getVisible('startTime')" :span="spanValue">
                <a-form-item :label="getTitle('startTime')">
                  <a-range-picker :getCalendarContainer="node=> node.parentNode" style='width: 100%'
                                  v-model='queryParam.searchStartTime' :placeholder="['开始时间', '结束时间']" format='YYYY-MM-DD HH:mm:ss'
                                  showTime @change='onCreatedTimeChange' />
                </a-form-item>
              </a-col>
              <a-col v-show="getVisible('endTime')" :span="spanValue">
                <a-form-item :label="getTitle('endTime')">
                  <a-range-picker :getCalendarContainer="node=> node.parentNode" style='width: 100%'
                                  v-model='queryParam.searchEndTime' :placeholder="['开始时间', '结束时间']" format='YYYY-MM-DD HH:mm:ss' showTime
                                  @change='onEndTimeChange' />
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <span class="table-page-search-submitButtons" style="overflow: hidden;">
                  <a-button icon="search" type="primary" @click="searchQuery">查询</a-button>
                  <a-button icon="reload" style="margin-left: 8px" @click="searchReset">重置</a-button>
                  <a v-if="queryItems.length>0" style="margin-left: 8px" @click="doToggleSearch">{{queryName}}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
        <!-- 查询区域-END -->

        <!--自定义查询项 -->
        <div v-if="toggleSearchStatus" class="custom-query-item">
          <a-checkbox-group v-model="settingQueryItems" :defaultValue="settingQueryItems" style="width:100%"
                            @change="onQuerySettingsChange">
            <a-row :gutter="24">
              <div v-for="(item,index) in queryItems" :key='index'>
                <a-col v-show='item.checked' :span='querySpanValue' class='col-checkbox'>
                  <a-checkbox :disabled="item.disabled" :value="item.dataIndex">
                    <j-ellipsis :length="7" :value="item.title"></j-ellipsis>
                  </a-checkbox>
                </a-col>
              </div>
            </a-row>
          </a-checkbox-group>
        </div>
        <!-- 自定义查询项-END -->
      </a-card>

      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <!-- table区域-begin -->
        <div>
          <a-table
            ref='table'
            bordered
            rowKey='id'
            :columns='columns'
            :dataSource='dataSource'
            :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
            :pagination='ipagination'
            :loading='loading'
            @change='handleTableChange'>
            <div slot='filterDropdown'>
              <a-card>
                <a-checkbox-group @change='onColSettingsChange' v-model='settingColumns' :defaultValue='settingColumns'>
                  <a-row style='width: 400px'>
                    <template v-for='(item,index) in defColumns'>
                      <template v-if="item.key!='rowIndex'&& item.dataIndex!='action'">
                        <a-col :span='12' :key='index'>
                          <a-checkbox :value='item.dataIndex'>
                            <j-ellipsis :value='item.title' :length='10'></j-ellipsis>
                          </a-checkbox>
                        </a-col>
                      </template>
                    </template>
                  </a-row>
                </a-checkbox-group>
              </a-card>
            </div>
            <a-icon slot="filterIcon" type='setting' :style="{ fontSize:'16px',color:  '#108ee9' }" />

            <span slot='action' slot-scope='text, record'>
              <a href='javascript:' @click='handleInstanceInfo(record)'>查看</a>
            </span>
            <template slot='tooltip' slot-scope='text'>
              <a-tooltip placement='top'
                :title='text'
                trigger='hover'>
                <div class='tooltip'>
                  {{ text }}
                </div>
              </a-tooltip>
            </template>
            <template slot='tooltip2' slot-scope='text'>
              <a-tooltip placement='topLeft'
                :title='text'
                trigger='hover'>
                <div class='tooltip'>
                  {{ text }}
                </div>
              </a-tooltip>
            </template>
          </a-table>
        </div>
        <!-- table区域-end -->
      </a-card>

      <!-- 查看区域 -->
      <process-instancedetail-modal ref='processInstanceDetailModalForm' @ok='modalFormOk'></process-instancedetail-modal>
      <!-- 一查看详情区域 -->
      <process-instance-info-modal ref='processInstanceInfoModalForm' @ok='modalFormOk'></process-instance-info-modal>

    </a-col>
  </a-row>
</template>

<script>
import {
  getUserList
} from '@/api/api'
import JSuperQuery from '@/components/jeecg/JSuperQuery.vue'
import JInput from '@/components/jeecg/JInput.vue'
import {
  initDictOptions
} from '@/components/dict/JDictSelectUtil'
import {
  JeecgListMixin
} from '@/mixins/JeecgListMixin'
import Vue from 'vue'
import {
  filterObj
} from '@/utils/util'
import ProcessInstancedetailModal from '../process-instance/modules/ProcessInstanceDetailModal'
import ProcessInstanceInfoModal from '../process-instance/modules/ProcessInstanceInfoModal'
import {
  deleteAction
} from '@/api/manage'
import {
  processInstanceActivate,
  processInstanceSuspend
} from '@/api/flowable'
import {
  YqFormSeniorSearchLocation
} from '@/mixins/YqFormSeniorSearchLocation'

export default {
  name: 'processInstance',
  mixins: [JeecgListMixin, YqFormSeniorSearchLocation],
  components: {
    JSuperQuery,
    JInput,
    ProcessInstancedetailModal,
    ProcessInstanceInfoModal
  },
  props:{
    requestUrl: {
      type: String,
      default:'/flowable/processInstance/completedInstanceList',
    },
  },
  data() {
    return {
      maxLength:50,
      formItemLayout: {
        labelCol: {
          style: 'width:105px'
        },
        wrapperCol: {
          style: 'width:calc(100% - 105px)'
        }
      },
      description: '单表示例列表',
      //表头
      // columns: [],
      //列设置
      settingColumns: [],
      procInstId: '', //流程实例ID
      //列定义
      columns: [{
        title: '序号',
        width: 60,
        dataIndex: '',
        key: 'rowIndex',
        isUsed: false,
        align: 'center',
        customCell: () => {
          let cellStyle = 'text-align:center;width:60px'
          return {
            style: cellStyle
          }
        },
        customRender: function (t, r, index) {
          return parseInt(index) + 1
        }
      },
        {
          title: '业务标题',
          dataIndex: 'name',
          isUsed: true,
          customCell: () => {
            let cellStyle = 'text-align:center;min-width: 150px;max-width:300px'
            return {
              style: cellStyle
            }
          },
           scopedSlots: {
            customRender: 'tooltip'
          }
        },
        {
          title: '开始时间',
          dataIndex: 'startTime',
          isUsed: true,
          customCell: () => {
            let cellStyle = 'text-align:center;width: 200px;'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '结束时间',
          dataIndex: 'endTime',
          isUsed: true,
          customCell: () => {
            let cellStyle = 'text-align:center;width: 200px;'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '发起人',
          dataIndex: 'startUserName',
          isUsed: true,
          key: 'startUserName',
          customCell: () => {
            let cellStyle = 'text-align:center;width: 200px;'
            return { style: cellStyle }
          }
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          isUsed: false,
          width: 160,
          scopedSlots: {
            customRender: 'action'
          }
        }
      ],
      userList: [],
      url: {

        list: this.requestUrl,
      }
    }
  },
  created() {
    this.getColumns(this.columns)
    this.getuserList()
  },
  methods: {
    onCreatedTimeChange: function (value, dateString) {
      this.queryParam.startedAfter = dateString[0] || undefined
      this.queryParam.startedBefore = dateString[1] || undefined
    },
    onEndTimeChange: function (value, dateString) {
      this.queryParam.finishedAfter = dateString[0] || undefined
      this.queryParam.finishedBefore = dateString[1] || undefined
    },
    changeStartUser(e, node) {
      if (!e) {
        this.queryParam.startUserId = undefined
      }
    },
    getQueryParams() {
      //高级查询器
      let sqp = {}
      if (this.superQueryParams) {
        sqp['superQueryParams'] = encodeURI(this.superQueryParams)
        sqp['superQueryMatchType'] = this.superQueryMatchType
      }
      if (this.$route.query && this.$route.query.processDefinitionId) {
        this.queryParam.processDefinitionId = this.$route.query.processDefinitionId
      }
      var param = Object.assign(sqp, this.queryParam, this.isorter, this.filters)
      param.field = this.getQueryField()
      param.pageNo = this.ipagination.current
      param.pageSize = this.ipagination.pageSize
      delete param.birthdayRange //范围参数不传递后台
      return filterObj(param)
    },
    handleTaskList: function (record) {
      if (!record.id) {
        this.$message.error('流程实例ID不存在')
        return
      }
      this.$router.push({
        path: '/flowable/processTask',
        query: {
          processInstanceId: record.id
        }
      })
    },
    handleInstanceInfo: function (record) {
      if (!record.id) {
        this.$message.error('流程实例ID不存在')
        return
      }
      this.$refs.processInstanceInfoModalForm.init(record.id)
      this.$refs.processInstanceInfoModalForm.title = '流程实例信息'
      this.$refs.processInstanceInfoModalForm.disableSubmit = false
    },
    handleInstanceDetail: function (record) {
      if (!record.id) {
        this.$message.error('流程实例ID不存在')
        return
      }
      this.$refs.processInstanceDetailModalForm.edit(record)
      this.$refs.processInstanceDetailModalForm.title = '任务信息'
      this.$refs.processInstanceDetailModalForm.disableSubmit = false
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      )
    },
    onetomany: function () {
      this.$refs.jeecgDemoTabsModal.add()
      this.$refs.jeecgDemoTabsModal.title = '编辑'
    },
    onBirthdayChange: function (value, dateString) {
      this.queryParam.birthday_begin = dateString[0]
      this.queryParam.birthday_end = dateString[1]
    },
    //列设置更改事件
    onColSettingsChange(checkedValues) {
      var key = this.$route.name + ':colsettings'
      Vue.ls.set(key, checkedValues, 7 * 24 * 60 * 60 * 1000)
      this.settingColumns = checkedValues
      const cols = this.defColumns.filter(item => {
        if (item.key == 'rowIndex' || item.dataIndex == 'action') {
          return true
        }
        if (this.settingColumns.includes(item.dataIndex)) {
          return true
        }
        return false
      })
      this.columns = cols
    },
    initColumns() {
      //权限过滤（列权限控制时打开，修改第二个参数为授权码前缀）
      //this.defColumns = colAuthFilter(this.defColumns,'testdemo:');

      var key = this.$route.name + ':colsettings'
      let colSettings = Vue.ls.get(key)
      if (colSettings == null || colSettings == undefined) {
        let allSettingColumns = []
        this.defColumns.forEach(function (item, i, array) {
          allSettingColumns.push(item.dataIndex)
        })
        this.settingColumns = allSettingColumns
        this.columns = this.defColumns
      } else {
        this.settingColumns = colSettings
        const cols = this.defColumns.filter(item => {
          if (item.key == 'rowIndex' || item.dataIndex == 'action') {
            return true
          }
          if (colSettings.includes(item.dataIndex)) {
            return true
          }
          return false
        })
        this.columns = cols
      }
    },
    // 获取用户列表
    getuserList() {
      let param = {
        pageSize: 10000
      }
      getUserList(param).then((res) => {
        if (res.success) {
          this.userList = res.result.records
        }
      })
    }
  },
}
</script>
<style scoped lang='less'>
@import '~@assets/less/common.less';
@import '~@assets/less/YQCommon.less';
@import '~@assets/less/scroll.less';
</style>