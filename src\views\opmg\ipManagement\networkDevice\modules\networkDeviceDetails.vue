<template>
  <a-card style="height: 100%;">
    <a-row>
      <a-col :span="24">
        <span>
        </span>
        <span style="float: right;margin-bottom: 12px;"><img src="~@/assets/return1.png" alt="" @click="getGo"
            style="width: 20px;height: 20px;cursor: pointer"></span>
      </a-col>
      <a-col :span="24">
        <table class="gridtable">
          <tr>
            <td class="leftTd">设备名称</td>
            <td class="rightTd">{{ data.name }}</td>
            <td class="leftTd">设备IP</td>
            <td class="rightTd">{{ data.ipAddress }}</td>
          </tr>
          <tr>
            <td class="leftTd">设备类型</td>
            <td class="rightTd">{{ data.macCode }}</td>
            <td class="leftTd">设备型号</td>
            <td class="rightTd">{{data.utilizeUserText}}</td>
          </tr>
          <tr>
            <td class="leftTd">设备状态</td>
            <td class="rightTd">{{ data.macCode }}</td>
            <td class="leftTd">CPU使用率</td>
            <td class="rightTd">{{data.utilizeUserText}}</td>
          </tr>
          <tr>
            <td class="leftTd">内存使用率</td>
            <td class="rightTd">{{ data.macCode }}</td>
          </tr>
        </table>
      </a-col>
    </a-row>
  </a-card>
</template>

<script>
  import {
    httpAction,
    getAction
  } from '@/api/manage'
  import pick from 'lodash.pick'
  export default {
    name: 'ipAlarmDetails',
    props: {
      //流程表单data
      //表单模式：true流程表单 false普通表单
      formBpm: {
        type: Boolean,
        default: false,
        required: false
      },
      formBpm: {
        type: Boolean,
        default: false,
        required: false
      },
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      },
      data: {
        type: Object
      }
    },
    data() {
      return {
        form: this.$form.createForm(this),
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          },
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          },
        },
        confirmLoading: false,
        url: {
          queryById: "/devopsipmanage/devopsIpManage/queryById"
        },
        iconShow: "0",
        inpData: {}
      }
    },
    computed: {
      formDisabled() {
        if (this.formBpm === true) {
          if (this.formData.disabled === false) {
            return false
          }
          return true
        }
        return this.disabled
      },
      showFlowSubmitButton() {
        if (this.formBpm === true) {
          if (this.formData.disabled === false) {
            return true
          }
        }
        return false
      }
    },
    created() {
      //如果是流程中表单，则需要加载流程表单data
      this.showFlowData();

    },
    mounted() {},
    methods: {
      //渲染流程表单数据
      showFlowData() {
        if (this.formBpm === true) {
          let params = {
            id: this.formData.dataId
          };
          getAction(this.url.queryById, params).then((res) => {
            if (res.success) {
              this.edit(res.result);
            }
          });
        }
      },
      //返回上一级
      getGo() {
        this.$parent.pButton2(0);
      }
    }
  }
</script>
<style scoped>
  table.gridtable {
    font-family: verdana, arial, sans-serif;
    font-size: 14px;
    color: #606266;
    border-width: 1px;
    border-color: #e8e8e8;
    border-collapse: collapse;
    text-align: left;
    width: 100%;
  }

  table.gridtable td {
    border-width: 1px;
    border-style: solid;
    border-color: #e8e8e8;
  }

  .leftTd {
    width: 17%;
    background-color: #FAFAFA;
    padding: 16px 24px;
    text-align: center;
  }

  .rightTd {
    width: 35%;
    padding: 16px 24px;
  }
</style>