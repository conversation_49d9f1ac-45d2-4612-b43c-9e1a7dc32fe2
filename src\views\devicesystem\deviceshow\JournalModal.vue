<template>
  <div class="div-container">
    <a-row  class="row">
      <a-col :span="24" style="margin-bottom:14px">
        <div style="text-align: right;">
          <img src="~@assets/return1.png" alt="" @click="getGo"
            style="width: 20px;height: 20px;cursor: pointer">
        </div>
      </a-col>
      <a-col :span="24">
        <table class="gridtable">
          <tr>
            <td class="leftTd" style="font-size:14px">类 型</td>
            <td class="rightTd" style="font-size:14px">{{ record.type }}</td>
            <td class="leftTd" style="font-size:14px">时 间</td>
            <td class="rightTd" style="font-size:14px">{{ record.createTime }}</td>
          </tr>
          <tr>
            <td class="leftTd" style="font-size:14px"> 内 容</td>
            <td class="rightTd" colspan="3" style="font-size:14px">{{ record.content }}</td>
          </tr>
        </table>
      </a-col>
    </a-row>
  </div>
</template>
<script>
import { filterObj } from '@/utils/util'
import { getAction } from '@/api/manage'
import { ajaxGetDictItems, getDictItemsFromCache } from '@/api/api'
export default {
  name: 'alarmTemplateInfoModal',
  data() {
    return {
      record: {},
    }
  },
  props: {
    journalInfo: {
      type: Object,
      default:{}
    },
  },
  watch: {
    journalInfo(val, oldVal) {
      this.record = val
    },
  },
  mounted() {
    this.record = this.journalInfo
  },
  methods: {
    getGo() {
      this.$parent.pButton1(0)
    },
  },
}
</script>
<style lang="scss" scoped>
.div-container {
  height: 100%;
  overflow: hidden;
  overflow-y: auto;
}
.row {
  position: relative;
  margin-bottom: 16px;
  margin-right: 1px;
}
::v-deep .ant-descriptions-row {
  .ant-descriptions-item-label {
    padding: 10px;
    text-align: center;
  }
  .ant-descriptions-item-content {
    padding: 10px 24px;
  }
}
table.gridtable {
  font-family: verdana, arial, sans-serif;
  font-size: 11px;
  color: #606266;
  border-width: 1px;
  border-color: #e8e8e8;
  border-collapse: collapse;
  text-align: left;
  width: 100%;
  table-layout: fixed;
}
table.gridtable td {
  border-width: 1px;
  border-style: solid;
  border-color: #e8e8e8;
  word-wrap: break-word;
}
.leftTd {
  width: 17%;
  background-color: #fafafa;
  padding: 16px 24px;
  text-align: center;
}
.rightTd {
  width: 35%;
  padding: 16px 24px;
}
</style>

