<template>
  <a-spin :spinning="spinning" wrapperClassName='stratergy-spin'>
    <div class='task-strategy'>
      <div class='left-block'>
        <a-card style='height: 100%' :bodyStyle='{height:"100%"}' :bordered='false'>
          <div class='search-header'>
            <a-input-search style="width:calc(100% - 44px);margin-right: 12px;" v-model='stratergyName' placeholder='请输入策略名称' @search='onSearch' />
            <a-tooltip>
              <template slot='title'>
                {{ hasNewStrategy ? '请先保存当前新增策略' : '点击添加策略' }}
              </template>
              <div class="add-btn" :class="{'add-btn-disabled':hasNewStrategy}" @click='addStrategy'>
                <a-icon type='plus' ></a-icon>
              </div>
            </a-tooltip>

          </div>
          <div class='stratergy-list' ref='stratergyList' v-infinite-scroll="loadMore"  :infinite-scroll-disabled="busy" :infinite-scroll-immediate-check='false'>
            <a-empty style='margin-top: 24px' v-if='listData.length===0' />
            <div class='stratergy-item' :class='{"stratergy-item-active":index===curStratergy}'
                 v-for='(item,index) in listData' :key='index' @click='selectStratergy(item,index)'>
              {{ item.name }}
            </div>
          </div>

        </a-card>
      </div>
      <div class='right-block'>
        <a-card :bordered='false'>
          <div v-if='form'>
            <a-button type='link' class='save-btn' @click='onSubmit'>保存</a-button>
            <a-button style='margin-left: 12px' @click='onDelete'>删除</a-button>
          </div>
        </a-card>
        <a-card :bordered='false' style='height:calc(100% - 96px);margin-top: 16px;' :bodyStyle='{height:"100%",padding: "0px"}'>
          <div style='height: 100%;overflowY:auto;padding: 24px'>
            <a-form-model v-if='form' ref='stratergyForm' :model='form' :rules='rules' :labelCol='{width:"120px"}'>
              <a-form-model-item prop='name' style='display: flex'>
                <span slot='label'>策略名称</span>
                <a-input v-model='form.name' placeholder='请输入策略名称' allowClear style='width:448px' />
              </a-form-model-item>
              <a-form-model-item label='是否有效' prop='isEffective_bool' style='display: flex'>
                <a-switch v-model='form.isEffective_bool' checked-children="有效" un-checked-children="无效"  />
                <!--              <a-radio-group v-model='form.isEffective' button-style='solid'>
                                <a-radio-button value='0'>
                                  无效
                                </a-radio-button>
                                <a-radio-button value='1'>
                                  有效
                                </a-radio-button>
                              </a-radio-group>-->
              </a-form-model-item>
              <a-form-model-item label='策略范围' prop='effectiveType' style='display: flex'>
                <a-checkbox-group v-model='form.effectiveType' @change='onEffectiveTypeChange'>
                  <a-checkbox v-for='item in effectiveTypes' :key='item.value' :value='item.value' name='type'>
                    {{ item.label }}
                  </a-checkbox>
                </a-checkbox-group>
              </a-form-model-item>
              <!--        <a-form-model-item :wrapper-col="{ span: 14, offset: 4 }">-->
              <!--          <a-button type="primary" @click="onSubmit">-->
              <!--            Create-->
              <!--          </a-button>-->
              <!--          <a-button style="margin-left: 10px;">-->
              <!--            Cancel-->
              <!--          </a-button>-->
              <!--        </a-form-model-item>-->
            </a-form-model>
            <div v-if='effectiveTabs.length>0&&form && form.policyScopes'>
              <a-tabs
                v-model='activeKey'
                :style="{ height: '100%' }"
              >
                <a-tab-pane v-for='item in effectiveTabs' :key='item.value'
                            :tab="item.value==='terminalDevice'?`${item.label} (${form.policyScopes.terminalDevice.length})`:`${item.label}`"
                            :style='{height: `calc(100% / effectiveTabs.length)`}'>
                  <stratergy-dept-transfer v-if='activeKey==="department"' :value='form.policyScopes.department'
                                           @change='onDeptChange'></stratergy-dept-transfer>
                  <stratergy-users v-else-if='activeKey==="user"'></stratergy-users>
                  <stratergy-terminals v-else-if='activeKey==="terminalDevice"'
                                       :terminals='form.policyScopes.terminalDevice'
                                       @change='onTerminalChange'></stratergy-terminals>
                </a-tab-pane>
              </a-tabs>
            </div>
            <a-empty v-else-if='form === null' />
          </div>

        </a-card>
      </div>
    </div>
  </a-spin>

</template>
<script>
import StrategyDepartmentTree from '@views/opmg/taskStrategy/modules/StrategyDepartmentTree.vue'
import StratergyUsers from '@views/opmg/taskStrategy/modules/StratergyUsers.vue'
import StratergyTerminals from '@views/opmg/taskStrategy/modules/StratergyTerminals.vue'
import StratergyDeptTransfer from '@views/opmg/taskStrategy/modules/StratergyDeptTransfer.vue'
import moment from 'moment'
import { getAction,deleteAction,postAction,putAction } from '@api/manage'
import infiniteScroll from 'vue-infinite-scroll'
export default {
  name: 'TaskStrategy',
  components: { StratergyDeptTransfer, StratergyUsers, StrategyDepartmentTree, StratergyTerminals },
  directives: {infiniteScroll},
  data() {
    return {
      listData: [],
      labelCol: { width: 120 },
      wrapperCol: { span: 14 },
      form: null,
      effectiveTypes: [
        {
          value: 'department',
          label: '单位'
        },
        {
          value: 'terminalDevice',
          label: '终端'
        }
        // {
        //   value:"user",
        //   label:"用户"
        // },

      ],
      activeKey: '',
      rules: {
        name: [
          { required: true, message: '请输入策略名称', trigger: 'blur' },
          { max: 20, message: '最多20个字符', trigger: 'blur' }
        ],
        isEffective_bool: [
          { required: true, message: '请选择是否有效', trigger: 'change' }
        ],
        effectiveType: [
          { required: true, message: '请选择策略范围', trigger: 'change' }
        ]
      },
      curStratergy: null,
      pageNo:1,
      pageSize:50,
      stratergyName:"",
      busy:true,//是否下滑加载
      spinning:false,
    }
  },
  created() {
    this.getStratergyList(1)
  },
  mounted() {

  },
  computed: {
    hasNewStrategy() {
      return this.listData.find(el => el.id === undefined) ? true : false
    },
    effectiveTabs() {
      if (this.form && this.form.effectiveType && this.form.effectiveType.length) {
        return this.effectiveTypes.filter(el => this.form.effectiveType.includes(el.value))
      } else {
        return []
      }
    }
  },
  methods: {
    //下滑加载更多
    loadMore() {
      if(!this.busy){
        this.getStratergyList()
      }
    },
    //获取策略列表
    getStratergyList(type) {
      if(type === 1){
        // 初始化列表和表单
        this.pageNo = 1
        this.listData = []
        this.form = null
        this.activeKey = ''
        this.curStratergy = ""
      }
      let params = {
        pageNo:this.pageNo,
        pageSize:this.pageSize,
        name: this.stratergyName===""?undefined:this.stratergyName,
      }
      this.spinning = true
      getAction('/software/policy/list',params).then((res) => {
        // console.log('获取到的', res)
        if (res.success && res.result && res.result.records) {
          let records = res.result.records
          if(records.length<this.pageSize){
            if(!this.busy){
              this.$message.warning("没有更多了")
            }
            this.busy = true
          }else{
            this.pageNo += 1
            this.busy = false
          }
          if(records.length>0){
            this.listData = this.listData.concat(res.result.records)
            if(type===1){
              this.curStratergy = ""
              this.selectStratergy(this.listData[0],0)
            }
          }
        }
      }).finally(() => {
        this.spinning = false
      })

    },
    //新增策略
    addStrategy() {
      if(this.hasNewStrategy)return;
      // this.list.splice(this.list.length,0,this.getNewStrategy())
      let tem = this.getNewStrategy()
      this.listData.unshift(tem)
      this.form = null;
      this.$nextTick(() => {
        this.$refs.stratergyList.scrollTop = 0
        this.form = tem
        this.curStratergy = 0
        this.activeKey = this.form.effectiveType[0]
      })

    },
    //获取新的策略对象
    getNewStrategy() {
      return {
        name: '策略' + moment().format('YYYYMMDDHHmmss'),
        isEffective: '0',
        isEffective_bool:false,
        effectiveType: ['department', 'terminalDevice'],
        policyScopes: {
          department: [],
          terminalDevice: []
        }
      }
    },
    //监听策略范围变化
    onEffectiveTypeChange(e) {
      let scopeKeys = Object.keys(this.form.policyScopes)
      //检查表单策略对象是否已有选择范围字段了
      scopeKeys.forEach(key => {
        if (!e.includes(key)) {
          delete this.form.policyScopes[key]
        }
      })
      //删除表单策略对象里选择范围没有的字段
      e.forEach(el => {
        if (this.form.policyScopes[el] === undefined) {
          this.form.policyScopes[el] = []
        }
      })
      if (e.length > 0 && !e.includes(this.activeKey)) {
        this.activeKey = this.form.effectiveType[0]
      } else if (e.length === 0) {
        this.activeKey = ''
      }
      // console.log('策略范围变化了 === >', e, this.activeKey, this.form.effectiveType)
    },
    //按策略名搜索
    onSearch(e) {
      this.getStratergyList(1);
    },
    //选择策略
    selectStratergy(item, e) {
      if (e === this.curStratergy) {
        return
      }
      if (this.hasNewStrategy) {
        this.$message.warning('请先保存新增的策略')
        return
      }
      this.form = null
      this.activeKey = ''
      this.curStratergy = e
      this.$set(item,"isEffective_bool",item.isEffective==="1")
      this.$nextTick(() => {
        //switch 开关使用布尔值
        this.form = item
        this.form.effectiveType = typeof this.form.effectiveType === 'string' ?  this.form.effectiveType.split(','):this.form.effectiveType
        //根据id获取策略信息
        if(this.form.id){
          getAction("/software/policy/queryById", { id: this.form.id }).then((res) => {
            if (res.success) {
              this.$set(this.form, 'policyScopes', {})
              let scopes = JSON.parse(res.result.policyScopes)
              Object.keys(scopes).forEach(el => {
                this.$set(this.form.policyScopes, el, scopes[el].split(','))
                // this.form.policyScopes[el] = scopes[el].split(',')
              })
              this.activeKey = this.form.effectiveType[0]
            }
          })
        }

      })

    },
    //终端选择
    onTerminalChange(e) {
      this.form.policyScopes.terminalDevice = e
    },
    //单位变化
    onDeptChange(e) {
      this.form.policyScopes.department = e
    },
    //保存
    onSubmit() {
      this.$refs.stratergyForm.validate(valid=>{
        if(valid){
          let scopes = this.form.effectiveType
          let scopesObect = {}
          for(let i=0 ;i<scopes.length;i++){
            if(this.form.policyScopes[scopes[i]].length === 0){
              let t = this.effectiveTypes.find(el => el.value === scopes[i])
              this.$message.warning('请选择'+t?.label)
              return;
            }else{
              scopesObect[scopes[i]] = this.form.policyScopes[scopes[i]].join(',');
            }
          }
          this.form.isEffective = Number(this.form.isEffective_bool)+""
          let params = Object.assign({},this.form)
          params.effectiveType = this.form.effectiveType.join()
          params.policyScopes = JSON.stringify(scopesObect)
          this.spinning = true
          if(this.form.id){
            putAction('/software/policy/edit',params).then((res) => {
              if(res.success){
                this.$message.success("保存成功！")
              }
            }).finally(() => {
              this.spinning = false
            })
          }else{
            postAction('/software/policy/add',params).then((res) => {
              if(res.success){
                this.$message.success("保存成功！")
                this.getStratergyList(1)
              }
            }).finally(() => {
              this.spinning = false
            })
          }
        }
      })
    },
    //删除策略
    onDelete() {
      this.$confirm({
        title: '需要删除当前策略吗？',
        cancelText:'取消',
        okText:'删除',
        onOk:function() {
          //删除已添加的策略
          if(this.form.id){
            deleteAction('/software/policy/delete',{id:this.form.id}).then((res) => {
              if(res.success){
                this.getStratergyList(1);
              }
            })
          }else{
            //删除新增的
            this.form = null
            this.activeKey = ''
            this.curStratergy = null
            this.listData.splice(this.curStratergy,1)
            if(this.listData.length>0){
              this.selectStratergy(this.listData[0],0)
            }
          }

        }.bind(this),
        onCancel:()=>{

        },
      });
    }
  }
}
</script>

<style scoped lang='less'>
.stratergy-spin{
  height: 100%;
  /deep/ .ant-spin-container{
    height: 100%;
  }
}
.task-strategy {
  display: flex;
  height: 100%;

  .left-block {
    width: 300px;
    height: 100%;
    margin-right: 16px;
    .search-header {
      display: flex;
    }
    .stratergy-list {
      height: calc(100% - 48px);
      overflow-y: auto;
      margin-top: 16px;
      &::-webkit-scrollbar {
        display: none;
      }

      .stratergy-item {
        width: 100%;
        min-height: 36px;
        //border: 1px solid #e8e8e8;
        border-radius: 4px;
        padding: 8px;
        margin-bottom: 8px;
        cursor: pointer;
        text-align:left;
      }

      .stratergy-item:hover {
        border-color:  #ECF5FF;
        background: #ECF5FF;
        color: #409EFF;
        opacity: 0.6;
      }

      .stratergy-item-active {
        border-color: #ECF5FF;
        background: #ECF5FF;
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #409EFF;
        font-weight: 400;
      }
    }
  }

  .right-block {
    flex: 1;
    height: 100%;
    position: relative;

    .footer-btns {
      position: absolute;
      bottom: 8px;
      right: 16px;
    }
  }
}

/deep/ .ant-tabs-content {
  height: 100%;
}

/deep/ .ant-tabs-tabpane.ant-tabs-tabpane-active {
  height: 100%;
}
.add-btn{
  color:#d7d7d7;
  width:32px;
  height:32px;
  padding:0px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 2px;
  border: 1px solid #D7D7D7;
  &:hover{
    color:#1890FF;
  };
}
.add-btn-disabled{
  cursor: not-allowed;
  &:hover{
    color:#d7d7d7;
  };
}
/deep/ .ant-switch-checked {
  background-color: #1890FF;
}
/deep/ .ant-checkbox-checked .ant-checkbox-inner {
  background-color: #1890FF;
  border-color: #1890FF;
}
.save-btn{
  background: #ECF5FF;
  border: 1px solid #B3D8FF;
  border-radius: 4px;
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: #409EFF;
  font-weight: 400;
}
.ant-btn.save-btn:hover, .ant-btn.save-btn:focus {
  color: #409EFF;
  background-color: #fff;
  border: 1px solid #B3D8FF;
}
/deep/ .ant-tabs-ink-bar {
  background-color: #1890FF;
}
/deep/ .ant-tabs-nav .ant-tabs-tab-active {
  color: #1890FF;
}
/deep/ .ant-tabs-nav .ant-tabs-tab:hover {
  color: #1890FF;
}
/deep/ .ant-tree-checkbox-checked .ant-tree-checkbox-inner {
  background-color: #1890FF;
  border-color:#1890FF;
}
/deep/ .ant-tree-checkbox-wrapper:hover .ant-tree-checkbox-inner,
/deep/ .ant-tree-checkbox:hover .ant-tree-checkbox-inner,
/deep/ .ant-tree-checkbox-input:focus + .ant-tree-checkbox-inner {
  border-color: #1890FF;
}
/deep/ .ant-checkbox-wrapper:hover .ant-checkbox-inner,
/deep/ .ant-checkbox:hover .ant-checkbox-inner,
/deep/ .ant-checkbox-input:focus + .ant-checkbox-inner {
  border-color: #1890FF;
}
/deep/ .ant-checkbox-indeterminate .ant-checkbox-inner:after {
  border: 0;
  background-color: #1890FF;
}
/deep/ .ant-tree-checkbox-disabled .ant-tree-checkbox-inner {
  border-color: #d9d9d9;
  background-color: #f5f5f5;
}
/deep/ .ant-btn-primary {
  color: #fff;
  background: #1890FF;
  border-color: #1890FF;
  box-shadow: 0 2px 0 rgba(0, 0, 0, 0.035);
}
/deep/ .ant-btn-primary:hover, /deep/ .ant-btn-primary:focus {
  color: #fff;
  background: #1890FF;
  border-color: #1890FF;
  opacity: 0.8;
}
/deep/ .ant-transfer-list-header{
  background: #F5F5F5;
  font-family: PingFangSC-Medium;
  font-size: 16px;
  color: rgba(0,0,0,0.85);
  font-weight: 500;
}
/deep/ .ant-table-small > .ant-table-content > .ant-table-scroll > .ant-table-body > table > .ant-table-thead > tr > th{
  background: #FAFAFA;
}
</style>