<template>
  <div class="terminal-statistics" ref="terminalStatistics">
    <div class="terminal-statistics-inside" :style="`transform:scale(${scaleNum});left:${xNum}px;top:${yNum}px`">
      <div class="terminal-header">
        <div class="terminal-class" v-for="(item, idx) in terminals" :key="idx">
          <img :src="item.src" alt="" />
          <div class="terminal-class-right">
            <div class="terminal-num">{{ item.value }}</div>
            <div class="terminal-class-name">{{ item.name }}</div>
          </div>
        </div>
      </div>
      <div class="terminal-content">

        <div class="side-line-left">
          <div class="side-line-icon" @mouseleave="onMouseLeave('prev')" @mouseenter="onMouseEnter('prev')">
            <div class="arrow-left" v-show='prevVisibled' @click="arrowClick('prev')"></div>
          </div>
          <div class="side-line-inner">
            <div class='city-item' v-for='(item,idx) in cityLeft' :key='item.id'>
              <div class='city-name'
                   :style='{right:`${-Math.abs(idx+1 - cityLeftMid) * 15}px`}'
                   :class='{"city-name-actived":item.id === curCity}'
                   @click='citySelected(item)' >{{item.text}}</div>
            </div>
          </div>
        </div>
        <div class="terminal-carousel">
          <a-carousel
            ref="carousel"
            autoplay
            :autoplaySpeed="5000"
            :speed="1000"
            :dots="false"
            :after-change="onChange"
            v-if="carousels.length > 0"
          >
            <div class="carousel-item" v-for="(item, idx) in carousels" :key="idx">
              <div class="bar-box">
                <div class="bar-groups">
                  <div
                    :class="subIdx % 2 !== 0 ? 'bar-group-b' : 'bar-group-a'"
                    v-for="(subItem, subIdx) in item"
                    :key="idx + '_' + subIdx"
                  >
                    <div class="bars-row">
                      <div class="info-tip">
                        <div class="unit-name-box">
                          <div class="unit-name">
                            {{ subItem.name  }}
                          </div>
                        </div>
                        <div class="unit-terminal-num">{{ subItem.total }}台设备</div>
                        <div class="uni-terminal-rate">
                          <span class="open-rate">开机率：{{ subItem.onRate }}%</span>
                          <span class="domestic-rate">国产化率：{{ subItem.nationRate }}%</span>
                        </div>
                      </div>
                      <div
                        class="open-bar"
                        :style="{ height: (barheight * parseFloat(subItem.onRate)) / 100 + 'px' }"
                      ></div>
                      <div
                        class="domestic-bar"
                        :style="{ height: (barheight * parseFloat(subItem.nationRate)) / 100 + 'px' }"
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="bar-bottom">
                <img src="/statsCenter/terminal/rect11.png" alt="" />
                <img style="margin-top: -80px" src="/statsCenter/terminal/rect-copy.png" alt="" />
                <div class="bottom-units">
                  <div class="bottom-units-item" v-for="(subItem, subIdx) in item" :key="idx + '_b_' + subIdx">
                    {{ subItem.name}}
                  </div>
                </div>
              </div>
            </div>
          </a-carousel>
        </div>
        <div class="side-line-right" >
          <div class="side-line-icon" @mouseleave="onMouseLeave('next')" @mouseenter="onMouseEnter('next')">
            <div class="arrow-right" v-show='nextVisibled' @click="arrowClick('next')"></div>
          </div>
          <div class="side-line-inner">
            <div class='city-item' v-for='(item,idx) in cityRight' :key='item.id' >
              <div class='city-name'
                   :style='{left:`${-Math.abs(idx+1 - cityRightMid) * 15}px`}'
                   :class='{"city-name-actived":item.id === curCity}'
                   @click='citySelected(item)'>{{item.text}}</div>
            </div>
          </div>
        </div>


      </div>
      <div class="terminal-bottom">
        <div class="page-line">
          <div class="page-row">
            <div class="page-cir page-cir-s">
              <div v-show="carousels.length > 0 && curPage - 3 > 0">
                <img src="/statsCenter/terminal/cir-icon.png" alt="" />
                <div class="page-info">{{ curPage - 3 }}/{{ carousels.length }}</div>
              </div>
            </div>
            <div class="page-cir page-cir-m">
              <div v-show="carousels.length > 0 && curPage - 2 > 0">
                <img src="/statsCenter/terminal/cir-icon.png" alt="" />
                <div class="page-info">{{ curPage - 2 }}/{{ carousels.length }}</div>
              </div>
            </div>
            <div class="page-cir page-cir-b">
              <div v-show="carousels.length > 0 && curPage - 1 > 0">
                <img src="/statsCenter/terminal/cir-icon.png" alt="" />
                <div class="page-info">{{ curPage - 1 }}/{{ carousels.length }}</div>
              </div>
            </div>
            <div class="page-cir page-cir-b page-cir-cur">
              <div v-show="carousels.length > 0">
                <img src="/statsCenter/terminal/cir-icon.png" alt="" />
                <div class="page-info">{{ curPage }}/{{ carousels.length }}</div>
              </div>
            </div>
            <div class="page-cir page-cir-b">
              <div v-show="carousels.length > 0 && curPage + 1 <= carousels.length">
                <img src="/statsCenter/terminal/cir-icon.png" alt="" />
                <div class="page-info">{{ curPage + 1 }}/{{ carousels.length }}</div>
              </div>
            </div>
            <div class="page-cir page-cir-m">
              <div v-show="carousels.length > 0 && curPage + 2 <= carousels.length">
                <img src="/statsCenter/terminal/cir-icon.png" alt="" />
                <div class="page-info">{{ curPage + 2 }}/{{ carousels.length }}</div>
              </div>
            </div>
            <div class="page-cir page-cir-s">
              <div v-show="carousels.length > 0 && curPage + 3 <= carousels.length">
                <img src="/statsCenter/terminal/cir-icon.png" alt="" />
                <div class="page-info">{{ curPage + 3 }}/{{ carousels.length }}</div>
              </div>
            </div>
          </div>
          <img style="margin-top: 13px" src="/statsCenter/terminal/bottom-rect.png" alt="" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import resizeObserverMixin from '@views/statsCenter/com/resizeObserverMixin'
import { getAction } from '@/api/manage'
import {getAreaTree} from '@/api/api'
import { getRootAreaId } from '@/components/dict/JDictSelectUtil'
export default {
  name: 'DevicesStatistics',
  mixins: [resizeObserverMixin],
  data() {
    return {
      terminals: [
        {
          src: require('/public/statsCenter/terminal/terminals.png'),
          name: '设备总数',
          value: 0,
        },
        {
          src: require('/public/statsCenter/terminal/onlineCur.png'),
          name: '实时在线数',
          value: 0,
        },
        {
          src: require('/public/statsCenter/terminal/offline.png'),
          name: '离线数量',
          value: 0,
        },
        {
          src: require('/public/statsCenter/terminal/online.png'),
          name: '在线率',
          value: '0%',
        },
        {
          src: require('/public/statsCenter/terminal/domestic.png'),
          name: '国产化率',
          value: '0%',
        },
        {
          src: require('/public/statsCenter/terminal/alarm.png'),
          name: '告警数量',
          value: 0,
        },
        {
          src: require('/public/statsCenter/terminal/unit.png'),
          name: '单位数量',
          value: 0,
        },
      ],
      barheight: 368,
      scaleNum: 1,
      xNum: 0,
      yNum: 0,
      curPage: 1,
      sourceData: [],
      carousels: [],
      cityLeft:[],
      cityRight:[],
      curCity:undefined,
      prevVisibled: false,
      nextVisibled: false,
    }
  },
  created() {
    this.getAreaData();
    // this.getCarousels();
    this.getUnitTerminal()
  },
  mounted() {},
  computed: {
    cityLeftMid() {
      if(this.cityLeft.length%2 === 0){
        return (this.cityLeft.length + 1) / 2
      }
      else{
        return (this.cityLeft.length + 1) / 2
      }
    },
    cityRightMid() {
      if(this.cityRight.length%2 === 0){
        return (this.cityRight.length + 1) / 2
      }
      else{
        return (this.cityRight.length + 1) / 2
      }
    },
  },
  methods: {
    //获取当前区域数据
    getAreaData() {
      getRootAreaId().then(res => {
        let pid = res.data
        getAreaTree({pid: pid}).then((res) => {
          if (res.success && res.result) {
            this.cityLeft = res.result.splice(0,Math.ceil(res.result.length/2));
            this.cityRight = res.result;
          }
        })
      })

    },
    //选择城市区域
    citySelected(item){
      if(this.curCity === item.id) {
        this.curCity = undefined
      }else{
        this.curCity = item.id
      }
      this.curPage = 1;
      this.carousels = [];
      this.getUnitTerminal()
    },
    //获取单位的设备统计信息；
    getUnitTerminal() {
      getAction('/data-analysis/index/overviewNationalDeviceByDepart',{cityId: this.curCity}).then((res) => {
        if (res.success && res.result) {
          this.terminals[0].value = res.result.countInfo.total || 0
          this.terminals[1].value = res.result.countInfo.active || 0
          this.terminals[2].value = res.result.countInfo.offLine || 0
          this.terminals[3].value = res.result.countInfo.onRate ? res.result.countInfo.onRate + '%' : '0%'
          this.terminals[4].value = res.result.countInfo.nationalRate ? res.result.countInfo.nationalRate + '%' : '0%'
          this.terminals[5].value = res.result.countInfo.alarm || 0
          this.terminals[6].value = res.result.countInfo.depts || 0

          this.sourceData = res.result.barArray
          this.carousels = this.splitArray(this.sourceData, 5)
          this.$refs.carousel?.goTo(1)
        }
      })
    },
    splitArray(arr, len) {
      const arrLen = arr.length
      const result = []
      for (let i = 0; i < arrLen; i += len) {
        result.push(arr.slice(i, i + len))
      }
      return result
    },
    //测试用
    getCarousels() {
      for (let i = 0; i < 5; i++) {
        this.carousels.push([
          {
            name: '单位名称1'+i,
            total: 222,
            onRate: '67%',
            nationRate: '43%',
          },
          {
            name: '单位名称2'+i,
            total: 222,
            onRate: '67%',
            nationRate: '43%',
          },
          {
            name: '单位名称3'+i,
            total: 222,
            onRate: '67%',
            nationRate: '43%',
          },
          {
            name: '单位名称4'+i,
            total: 222,
            onRate: '67%',
            nationRate: '43%',
          },
          {
            name: '单位名称5'+i,
            total: 222,
            onRate: '67%',
            nationRate: '43%',
          },
        ])
      }
    },
    setScale() {
      let react = this.$refs.terminalStatistics.getBoundingClientRect()
      let scaleW = react.width / 1920
      let scaleH = react.height / 944
      this.scaleNum = Math.min(scaleH, scaleW)
      // console.log('放大缩小 === ', this.scaleNum)
      this.xNum = (react.width - 1795 * this.scaleNum) / 2
      this.yNum = (react.height - 944 * this.scaleNum) / 2
    },
    onChange(a) {
      this.curPage = a + 1
    },
    arrowClick(type) {
      this.$refs.carousel[type]()
    },
    onMouseLeave(type) {
      this.nextVisibled = false
      this.prevVisibled = false
    },
    onMouseEnter(type) {
      if(type === 'next') {
        this.nextVisibled = true
      }else{
        this.prevVisibled = true
      }
    },
  },
}
</script>

<style lang="less" scoped>
.terminal-statistics {
  width: 100%;
  height: 100%;
  background: transparent;
  position: relative;
  .terminal-statistics-inside {
    width: 1795px;
    height: 942px;
    position: absolute;
    display: flex;
    flex-direction: column;
    align-items:center;
    // border: 1px solid rgba(255, 255, 255, 0.3);
    transform-origin: left top;
    .terminal-header {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 125px;
      .terminal-class {
        display: flex;
        width: 250px;
        height: 70px;
        color: #fff;
        align-items: center;
        padding-right: 10px;
        border-right: 1px solid rgba(53, 172, 255, 0.56);
        margin-right: 50px;
        img {
          width: 102px;
          height: 70px;
        }
        .terminal-class-right {
          width: calc(100% - 102px);
          .terminal-num {
            font-size: 31px;
            font-family: DIN;
            font-weight: bold;
            color: #41bbff;
            line-height: 31px;
            background: linear-gradient(0deg, #61dffe 4.931640625%, #44a5ff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
          }
          .terminal-class-name {
            font-size: 16px;
            font-family: SourceHanSansCN-Regular;
            font-weight: 400;
            color: #ffffff;
            line-height: 24px;
          }
        }
      }
      .terminal-class:last-child {
        border: none;
        margin-right: 0px;
      }
    }
    .terminal-content {
      display: flex;
      align-items: center;
      position: relative;
      height: 618px;
      margin-top: 72px;
      width: 1638px;
      .arrow-left {
        // opacity: 0.4;
        cursor: pointer;
        position: absolute;
        left: 0px;
        width: 110px;
        height: 129px;
        background-image: url('/statsCenter/terminal/arrow.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        background-position: center;
      }
      .arrow-right {
        cursor: pointer;
        position: absolute;
        right: 0px;
        width: 110px;
        height: 129px;
        transform: rotate(180deg);
        background-image: url('/statsCenter/terminal/arrow.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        background-position: center;
      }
      .terminal-carousel {
        width: calc(100% - 171px - 171px);
        height: 100%;
        .carousel-item {
          width: 100%;
          height: 100%;
          .bar-box {
            height: 430px;
            width: 100%;
            position: relative;
            .bar-groups {
              width: 100%;
              top: 315px;
              display: flex;
              position: absolute;
              .bar-group-a {
                width: 20%;
                height: 222px;
                position: relative;
                background-image: url('/statsCenter/terminal/group-b.png');
                background-size: 183px 100%;
                background-repeat: no-repeat;
                background-position: center;
                .bars-row {
                  width: 100%;
                  padding: 0 108px;
                  min-height: 111px;
                  display: flex;
                  position: absolute;
                  align-items: end;
                  flex-basis: end;
                  bottom: 0px;
                  .info-tip {
                    width: 195px;
                    height: 115px;
                    top: -155px;
                    left: 35px;
                    position: absolute;
                    background-image: url('/statsCenter/terminal/info-rect.png');
                    background-size: 100%px 100%;
                    background-repeat: no-repeat;
                    background-position: center;
                    .unit-name-box {
                      font-size: 13px;
                      font-family: Adobe Heiti Std;
                      font-weight: normal;
                      color: #ffffff;
                      margin-top: 14px;
                      margin-left: 13px;
                      margin-right: 13px;
                      height: 29px;
                      display: flex;
                      align-items: center;
                      overflow: hidden;
                      overflow: hidden;
                      // white-space: nowrap;
                      text-overflow: ellipsis;
                      .unit-name {
                        line-height: 14.5px;
                        max-height: 29px;
                        display: -webkit-box;
                        -webkit-line-clamp: 2;
                        overflow: hidden;
                        text-overflow: ellipsis;
                      }
                    }

                    .unit-terminal-num {
                      height: 20px;
                      font-size: 21px;
                      font-family: Adobe Heiti Std;
                      font-weight: normal;
                      color: #ffffff;
                      line-height: 29px;
                      margin-top: 10px;
                      margin-left: 13px;
                    }
                    .uni-terminal-rate {
                      font-family: Adobe Heiti Std;
                      line-height: 29px;
                      font-size: 10px;
                      margin-top: 10px;
                      margin-left: 13px;
                      .open-rate {
                        color: #167fff;
                        margin-right: 5px;
                      }
                      .domestic-rate {
                        color: #27deff;
                      }
                    }
                  }
                  .open-bar {
                    height: 0px;
                    width: 21px;
                    margin-right: 15px;
                    background: linear-gradient(180deg, #167fff, rgba(97, 224, 255, 0));
                  }
                  .domestic-bar {
                    height: 0px;
                    width: 21px;
                    background: linear-gradient(180deg, #27deff, rgba(97, 224, 255, 0.01));
                  }
                }
              }
              .bar-group-b {
                width: 20%;
                height: 176px;
                background-image: url('/statsCenter/terminal/group-a.png');
                background-size: 145px 100%;
                background-repeat: no-repeat;
                background-position: center;
                position: relative;
                .bars-row {
                  width: 100%;
                  padding: 0 108px;
                  min-height: 88px;
                  display: flex;
                  // height: 403px;
                  position: absolute;
                  align-items: end;
                  flex-basis: end;
                  bottom: 0px;
                  .info-tip {
                    width: 171px;
                    height: 102px;
                    top: -124px;
                    left: 45px;
                    position: absolute;
                    background-image: url('/statsCenter/terminal/info-rect-q.png');
                    background-size: 100%px 100%;
                    background-repeat: no-repeat;
                    background-position: center;
                    .unit-name-box {
                      font-size: 12px;
                      font-family: Adobe Heiti Std;
                      font-weight: normal;
                      color: #ffffff;
                      height: 25px;
                      margin-top: 12.5px;
                      margin-left: 13px;
                      margin-right: 13px;
                      display: flex;
                      align-items: center;
                      overflow: hidden;
                      overflow: hidden;
                      // white-space: nowrap;
                      text-overflow: ellipsis;
                      .unit-name {
                         line-height: 12.5px;
                        max-height: 25px;
                        display: -webkit-box;
                        -webkit-line-clamp: 2;
                        overflow: hidden;
                        text-overflow: ellipsis;
                      }
                    }

                    .unit-terminal-num {
                      height: 18px;
                      font-size: 18px;
                      font-family: Adobe Heiti Std;
                      font-weight: normal;
                      color: #ffffff;
                      line-height: 25px;
                      margin-top: 6px;
                      margin-left: 10px;
                    }
                    .uni-terminal-rate {
                      font-size: 9px;
                      font-family: Adobe Heiti Std;
                      line-height: 25px;
                      margin-top: 10px;
                      margin-left: 10px;
                      .open-rate {
                        color: #167fff;
                        margin-right: 5px;
                      }
                      .domestic-rate {
                        color: #27deff;
                      }
                    }
                  }
                  .open-bar {
                    height: 0px;
                    width: 21px;
                    margin-right: 15px;
                    background: linear-gradient(180deg, #167fff, rgba(97, 224, 255, 0));
                  }
                  .domestic-bar {
                    height: 0px;
                    width: 21px;
                    background: linear-gradient(180deg, #27deff, rgba(97, 224, 255, 0.01));
                  }
                }
              }
            }
          }
          .bar-bottom {
            img {
              width: 100%;
            }
            .bottom-units {
              margin-top: -28px;
              display: flex;
              color: #fff;
              .bottom-units-item {
                width: 20%;
                text-align: center;
                padding: 0 5px;
              }
            }
          }
        }
      }
      .side-line-left {
        width: 171px;
        height: 100%;
        background-image: url('/statsCenter/terminal/side-line-left.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        background-position: center;
        position: relative;
        .side-line-icon{
          position: absolute;
          width: 100%;
          height: 171px;
          top:50%;
          transform: translateY(-50%);
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .side-line-inner{
          position: absolute;
          left:calc(-100% - 16px);
          height: 0px;
          display: flex;
          height: 100%;
          width: 100%;
          flex-direction: column;
          align-items: center;
          justify-content: space-evenly;
          .city-item{
            color: #fff;
            width: 100%;
            text-align: right;
            position: relative;
            .city-name{
              position: absolute;
              top: 0;
              padding:5px;
              text-align: right;
              cursor: pointer;
            }
            .city-name-actived{
              background-color: #146DDA;
            }
          }
        }
      }
      .side-line-right {
        width: 171px;
        height: 100%;
        background-image: url('/statsCenter/terminal/side-line-right.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        background-position: center;
        position: relative;
        .side-line-icon{
          position: absolute;
          width: 100%;
          height: 171px;
          top:50%;
          transform: translateY(-50%);
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .side-line-inner{
          position: absolute;
          left:calc(100% + 16px);
          height: 0px;
          display: flex;
          height: 100%;
          width: 100%;
          flex-direction: column;
          align-items: center;
          justify-content: space-evenly;
          .city-item{
            color: #fff;
            width: 100%;
            text-align: left;
            position: relative;
            .city-name{
              position: absolute;
              top: 0;
              padding:5px;
              text-align: left;
              cursor: pointer;
            }
            .city-name-actived{
              background-color: #146DDA;
            }
          }
        }
      }
    }
    .terminal-bottom {
      display: flex;
      justify-content: center;
      .page-line {
        width: 840px;
        display: flex;
        flex-direction: column;
        align-items: center;
        .page-row {
          display: flex;
          align-items: center;
          .page-cir {
            width: 94px;
            height: 94px;
            background-image: url('/statsCenter/terminal/cir1.png');
            background-size: 100% 100%;
            background-repeat: no-repeat;
            background-position: center;
            opacity: 0.4;
            img {
              width: 27px;
              height: 27px;
              margin-left: 33.5px;
              margin-top: 25px;
              opacity: 0.4;
            }
            .page-info {
              width: 94px;
              text-align: center;
              color: rgba(255, 255, 255, 0.6);
              font-size: 14px;
            }
          }
          .page-cir-cur {
            background-image: url('/statsCenter/terminal/cir2.png');
            opacity: 1;
            img {
              opacity: 1;
            }
            .page-info {
              color: rgba(255, 255, 255, 1);
            }
          }
          .page-cir-m {
            width: 64px;
            height: 64px;
            img {
              width: 18px;
              height: 18px;
              margin-left: 23px;
              margin-top: 15px;
            }
            .page-info {
              width: 64px;
              text-align: center;
              color: rgba(255, 255, 255, 0.6);
              font-size: 12px;
            }
          }
          .page-cir-s {
            width: 54px;
            height: 54px;
            img {
              width: 15px;
              height: 15px;
              margin-left: 19.5px;
              margin-top: 13px;
            }
            .page-info {
              width: 54px;
              text-align: center;
              color: rgba(255, 255, 255, 0.6);
              font-size: 10px;
            }
          }
        }
      }
    }
  }
}
</style>>
