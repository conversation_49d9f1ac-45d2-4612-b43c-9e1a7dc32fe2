<template>
  <div class='table-operator table-operator-style scroll'>
    <a-table
      style='margin-right: 1px'
      ref='table'
      bordered
      :columns='columns'
      :dataSource='dataSource'
      :loading='loading'
      :rowKey="(record) => { return record.id}"
      :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
      :pagination='ipagination'
      @change='handleTableChange'>
      <template slot="alarmStatus" slot-scope="text">
        <span v-if="text == 1">历史告警</span>
        <span v-else>当前告警</span>
      </template>
      <template slot="handleStatus" slot-scope="text">
        <span >{{getHandlingStatus(text)}}</span>
      </template>
      <template slot="alarmLevel" slot-scope="text,record">
        <div :style='{backgroundColor:getAlarmColor(record)}'
          style='display:inline-block;color:#ffffff; border-radius: 10px; padding: 2px 10px;'>
          {{ getAlarmTitle(record) }}
        </div>
      </template>
      <span slot="action" slot-scope="text, record" class="caozuo">
        <a @click="handleDetailPage(record)">查看</a>
      </span>
    </a-table>
  </div>
</template>

<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import {dataAndFunction} from '@views/alarmManage/modules/dataAndFunction'
  export default {
    name: 'deviceAlarmList',
    mixins: [JeecgListMixin,dataAndFunction],
    data() {
      return {
        // 表头
        columns: [
          {
            title: '设备名称',
            dataIndex: 'deviceName',
            scopedSlots: {
              customRender: 'deviceName'
            }
          },
          {
            title: '告警名称',
            dataIndex: 'templateName',
            scopedSlots: {
              customRender: 'alarmTemplateName'
            }
          },
          {
            title: '产品名称',
            dataIndex: 'productName'
          },
          {
            title: '告警级别',
            dataIndex: 'alarmLevel',
            scopedSlots: {
              customRender: 'alarmLevel'
            }
          },
          {
            title: '告警状态',
            dataIndex: 'alarmStatus',
            scopedSlots: {
              customRender: 'alarmStatus'
            }
          },
          {
            title: '触发时间',
            dataIndex: 'alarmTime1'
          },
          {
            title: '重复次数',
            dataIndex: 'repeatTimes',
            customCell: () => {
              let cellStyle = 'text-align: right'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '处理状态',
            dataIndex: 'handleStatus',
            scopedSlots: {
              customRender: 'handleStatus'
            }
          },
          {
            title: '责任人',
            dataIndex: 'responsibleUser_dictText',
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: 100,
            scopedSlots: {
              customRender: 'action'
            }
          },
        ],
        url: {
          list: '/alarm/alarmHistory/list'
        },
        disableMixinCreated: true
      }
    },
    props: {
      deviceInfo: {
        type: Object,
        required: true,
        default: null
      }
    },
    watch: {
      deviceInfo(newVal, oldVal) {
        this.init(newVal)
      }
    },
    created() {
      this.getAlarmLevelData()
    },
    methods: {
      init(record) {
        this.queryParam.deviceId=record.id
        this.loadData()
      },
    }
  }
</script>

<style lang='less' scoped>
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';
  .scroll{
    height: 100%;
    overflow: hidden;
    overflow-y: auto;
  }
  .confirm {
    color: rgba(0, 0, 0, 0.25) !important;
  }
</style>