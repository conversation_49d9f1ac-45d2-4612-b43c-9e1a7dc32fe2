<template>
  <div class="page-box big-screen-theme">
    <div class="page-inside">
      <a-row :gutter="16" style="height: 100%">
        <a-col style="height: 100%" :span="15">
          <internet-table-module></internet-table-module>
        </a-col>
        <a-col style="height: 100%" :span="9">
          <div class="charts-box">
            <div class="charts-box-top">
              <div class="charts-box-top-inside">
                <chart-title :title="'cpu使用率TOP10'"></chart-title>
                <div class="charts-con">
                  <pictorial-bar :chartId="'cpuTop'"></pictorial-bar>

                </div>
              </div>
              <div class="charts-box-top-inside">
                <chart-title :title="'内存使用率TOP10'"></chart-title>
                <div class="charts-con">
                  <gradual-bar :round="gradualRadius" :chartId="'memoryTop'" :chartObj="chartObj"></gradual-bar>
                </div>
              </div>
            </div>
            <div class="charts-box-center">
              <chart-title :title="'网络吞吐量TOP10'"></chart-title>
              <div class="charts-con">
                <triangle-bar :chartId="'internetTop'"></triangle-bar>
              </div>

            </div>
            <div class="charts-box-bottom">
              <chart-title :title="'总流量TOP10'"></chart-title>
              <div class="charts-con">
                <normal-y-bar :chartId="'allTop'" :chartObj="netInOutData"></normal-y-bar>
              </div>
            </div>
          </div>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script>
  import InternetTableModule from "../moduels/internet-table-module.vue"
  import ChartTitle from "../moduels/chart-title.vue"
  import PictorialBar from '../echartsModule/pictorial-bar.vue'
  import GradualBar from '../echartsModule/gradual-bar.vue'
  import ProcessBar from '../echartsModule/process-bar.vue'
  import TriangleBar from '../echartsModule/triangle-bar.vue'
  import NormalYBar from '../echartsModule/normal-y-bar.vue'
  import {
    getAction,
  } from '@/api/manage'
  export default {
    components: {
      InternetTableModule,
      ChartTitle,
      PictorialBar,
      GradualBar,
      ProcessBar,
      TriangleBar,
      NormalYBar,
    },
    data() {
      return {
        gradualRadius: [10, 10, 10, 10],
        chartObj: [],
        netInOutData: [],
        url: {
          mateTop10: 'device/statistics/memusedtop10',
          netInOutTop: 'device/statistics/netInOutTop'
        }
      }
    },
    created() {
      this.getMate()
      this.getGetInOut()
    },
    mounted() {

    },
    methods: {
      getMate() {
        getAction(this.url.mateTop10).then((res) => {
          this.chartObj = res.result
        })
      },
      getGetInOut() {
        getAction(this.url.netInOutTop).then((res) => {
          this.netInOutData = res.result
        })
      },
    },
  }
</script>

<style lang="less" scoped>
  .page-box {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;

    .page-inside {
      width: calc(100% - 32px);
      height: calc(100% - 32px);
    }

    .charts-box {
      height: 100%;

      .charts-box-top {
        height: calc(50% - 32px);
        margin-bottom: 16px;
        display: flex;
        justify-content: space-between;

        .charts-box-top-inside {
          height: 100%;
          width: calc(50% - 8px);
          background-color: #111217;
          padding: 8px;
        }
      }

      .charts-box-center {
        padding: 8px;
        height: 20%;
        background-color: #111217;
      }

      .charts-box-bottom {
        padding: 8px;
        height: 30%;
        margin-top: 16px;
        background-color: #111217;
      }

      .charts-con {
        height: calc(100% - 24px);

      }
    }
  }
</style>