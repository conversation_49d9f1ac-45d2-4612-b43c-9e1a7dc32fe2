<template>
  <card-frame title='内存'>
    <div slot='bodySlot' class='bodySlot' v-if='Object.keys(info).length >0&&info.infoArr.length>0&&info.rate&&(info.rate+"").length>0'>
      <div class='left'>
        <div class='content-item' v-for='(item ,index) in info.infoArr' :key='"content_"+index'>
          {{item.name}}：<span class='value'>{{item.value}}</span>
        </div>
      </div>
      <div id='chart_memory' class='right'></div>
    </div>
    <div slot='bodySlot' class='body-empty' v-else>
      <a-spin :spinning='loading' v-if='loading' class='spin'></a-spin>
      <a-list :data-source='[]' :locale='locale' v-else />
    </div>
  </card-frame>
</template>
<script>
import cardFrame from '@views/oneClickHelp/localDeviceInfo/modules/CardFrame.vue'
import Empty from '@/components/oneClickHelp/Empty.vue'
import 'echarts-liquidfill'
import 'echarts/lib/component/graphic'
import echarts from 'echarts/lib/echarts'
export default {
  name: "MemoryInfo",
  props: {
    info: {
      type: Object,
      required: true
    },
    loading: {
      type: Boolean,
      required: false,
      default:false
    }
  },
  components: { cardFrame, Empty },
  data() {
    return {
      locale: {
        emptyText: <Empty/>
      }
    }
  },
  watch: {
    info: {
      handler(nValue) {
        if(Object.keys(nValue).length>0&&nValue.infoArr.length>0&&nValue.rate&&(nValue.rate+'').length>0) {
          this.$nextTick(() => {
            this.initChart(nValue)
          })
        }
      },
      deep:true,
      immediate:true
    }
  },
  methods: {
    initChart(data) {
      let _this = this
      let myChart = _this.$echarts.init(document.getElementById('chart_memory'))
      let initOption = {
        angleAxis: {
          max: 1,
          show: false,  // 隐藏刻度线
          startAngle: 0
        },
        radiusAxis: {
          type: 'category',
          show: true,
          axisLabel: { show: false },
          axisLine: { show: false },
          axisTick: { show: false },
        },
        polar: {
          center: ["60%", "50%"],
          radius: '145%' //图形大小
        },
        series: [
          {
            type: 'pie',
            radius: ['85%', '95%'],
            center: ['60%', '50%'],
            label: { show: false },
            tooltip: { show: false },
            silent: true,
            hoverAnimation: false,
            clockWise: true,

            data: [{
              value: 100,
              itemStyle: {
                normal: {
                  color: {
                    colorStops: [{
                      offset: 0,
                      color: '#81F6AF'
                    }, {
                      offset: 1,
                      color: '#505AE5'
                    }]
                  }
                }
              }
            }]
          },
          /*  {
            name: '内环1',
            type: 'pie',
            radius: ['55%', '65%'],
            center: ['60%', '50%'],
            minAngle: 10,
            startAngle: 360,
            label: { show: false },
            tooltip: { show: false },
            silent: true,
            hoverAnimation: false,
            data: [{
              value: 83,
              itemStyle: {
                normal: {
                  borderRadius: "50%",
                  color: {
                    colorStops: [{
                      offset: 0,
                      color: '#505AE5'
                    }, {
                      offset: 1,
                      color: '#81F6AF'
                    }]
                  }
                }
              }
            },
              {
                value: 17,
                itemStyle: {
                  normal: {
                    color: 'rgba(255,255,255,0)'
                  }
                }
              }]
          },*/
          {
            type: 'bar',
            data: [data.rate],
            showBackground: true,
            roundCap: true,
            backgroundStyle: {
              color: 'rgba(64,76,76,0)',
            },
            coordinateSystem: 'polar',
            barWidth: 10,
            itemStyle: {
              normal: {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 1,
                  x2: 0,
                  y2: 0,
                  colorStops: [{
                    offset: 0, color: '#81F6AF'
                  }, {
                    offset: 1, color: '#515CE5'
                  }],
                  global: false
                }
              }
            },
          },
          {
            type: 'liquidFill',
            shape: 'circle',
            radius: '60%',
            center: ['60%', '50%'],
            data: [data.rate],
            waveAnimation: false,
            hoverAnimation: false,
            // 球体配置
            outline: {
              show: false,
              borderDistance: 0,
              itemStyle: {
                borderWidth: 0,
                borderColor: 'rgba(56,116,246,0)',
              }
            },
            backgroundStyle: {
              color: 'rgba(235,241,254,0)',
            },
            color: [{
              type: 'linear',
              x: 0,
              y: 0,
              x2: 1,
              y2: 0,
              colorStops: [
                {
                  offset: 0,
                  color: '#81F6AF',
                },
                {
                  offset: 0.25,
                  color: '#6eb7c5',
                },
                {
                  offset: 1,
                  color: '#515CE5',
                },
              ],
              globalCoord: false,
            }],
            label: {
              show: true,
              textStyle: {
                color: '#ffffff',
                fontSize: 28
              },
              formatter: params => {
                return `${(params.value * 100).toFixed(0)}%`
              },
            }
          }
          ]
      }

      myChart.setOption(initOption, true)
      window.addEventListener('resize', () => {
        myChart.resize();
      });
    }
  }
}
</script>

<style scoped lang="less">
.bodySlot {
  height: 100%;
  padding: 0.5rem 0.65rem 0.4rem;//40px 52px 32px/80
  display: flex;
   flex-flow: row nowrap;
   justify-content: start;
   align-items: start;
  .left {
    width:40%;
    height: 100%;
    .content-item {
      font-size: 0.2rem;
      margin-bottom: 0.125rem;// 10/80
      display: inline-block;
      width: 100%;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      color: rgba(255, 255, 255, 0.65);

      .value {
        color: #FFFFFF;
      }
    }
  }

  .right {
    width:60%;
    height: 100%;
  }
}
</style>