<template>
  <div style='width: 100%;padding:0 5px;'>
    <div class='filter-line'>
      <span>日志类型：</span>
      <a-select v-model='queryParam.logType' allowClear placeholder='请选择日志类型' @change='handleChange' style='width: 180px'>
        <a-select-option v-for='(item, index) in datatypeList' :value='item.value'  :key='index'>
          {{ item.text }}
        </a-select-option>
      </a-select>
    </div>
    <a-table style='width: 100%' ref='table' bordered :rowKey='(record,index)=>{return record.id}' :columns='columns'
             :dataSource='dataSource' :pagination='ipagination'
             :loading='loading' @change='handleTableChange'>
      <!-- 字符串超长截取省略号显示-->
      <template slot='index' slot-scope='text,record,index'>
        <span>{{ index + 1 }}</span>
      </template>
      <template slot='resultFlag' slot-scope='text,record,index'>
        <span>{{ ['失败', '成功'][text] }}</span>
      </template>
      <template slot='operateType' slot-scope='text,record,index'>
        <span>{{ operateTypes[text] }}</span>
      </template>
      <template slot='tooltip' slot-scope='text'>
        <a-tooltip placement='topLeft' :title='text' trigger='hover'>
          <div class='tooltip'>
            {{ text }}
          </div>
        </a-tooltip>
      </template>
      <span slot='action' slot-scope='text, record' class='caozuo' style='padding-top: 10px;padding-bottom: 10px;'>
            <a @click='handleDetail(record)'>查看</a>
          </span>
    </a-table>
    <jkdj-log-detail ref='jkdjLogDetail'></jkdj-log-detail>
  </div>
</template>
<script>
import { getAction } from '@api/manage'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
import { ajaxGetDictItem } from '@api/api'
import JkdjLogDetail from '@views/jkdjgl/model/JkdjLogDetail.vue'

export default {
  name: 'JkdjLogs',
  components: { JkdjLogDetail },
  props: {
    record: {
      type: Object,
      default: () => {
        return {}
      },
      required: true
    }
  },
  mixins: [JeecgListMixin, YqFormSearchLocation],

  data() {
    return {
      columns: [
        {
          title: '序号',
          dataIndex: 'index',
          scopedSlots: {
            customRender: 'index'
          },
          width: 80,
          customCell: () => {
            let cellStyle = 'text-align: center'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '请求结果',
          dataIndex: 'resultFlag',
          width: 160,
          scopedSlots: {
            customRender: 'resultFlag'
          },
          customCell: () => {
            let cellStyle = 'text-align: center'
            return {
              style: cellStyle
            }
          }
        },{
          title: '操作类型',
          dataIndex: 'operateType',
          width: 160,
          scopedSlots: {
            customRender: 'operateType'
          },
          customCell: () => {
            let cellStyle = 'text-align: center'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '日志内容',
          dataIndex: 'logContent',
          scopedSlots: {
            customRender: 'logContent'
          },
          ellipsis: true,
          customCell: () => {
            let cellStyle = 'text-align: center'
            return {
              style: cellStyle
            }
          }
        },  {
          title: '生成时间',
          dataIndex: 'createTime',
          scopedSlots: {
            customRender: 'createTime'
          },
          width: 180,
          customCell: () => {
            let cellStyle = 'text-align: center'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '操作',
          align: 'center',
          width: 120,
          fixed: 'right',
          dataIndex: 'action',
          scopedSlots: {
            customRender: 'action'
          }
        }
      ],
      url: {
        list: '/abutment/system/getLogByType'
      },
      disableMixinCreated: true,
      datatypeList:[],
      operateTypes:{},
    }
  },
  created() {
    this.getDatatype();
    this.queryParam.systemId = this.record.id
    this.loadData()
  },
  mounted() {


  },
  methods: {
    getDatatype(){
      ajaxGetDictItem("jkdj_data_type").then((res) => {
        if(res.success){
          this.datatypeList = res.result
        }
      })
      ajaxGetDictItem("operate_type_en").then((res) => {
        if(res.success){
          res.result.forEach(el=>{
            this.operateTypes[el.value] = el.text;
          })

        }
      })
    },
    // getLogs() {
    //   getAction('/abutment/system/getLogByType', { systemId: this.record.id, logType: this.logType }).then(res => {
    //     console.log('获取的日志信息 == ', res)
    //     this.dataSource = res.result.records
    //   })
    // },
    handleDetail(record) {
      record.operateTypeText=this.operateTypes[record.operateType];
        this.$refs.jkdjLogDetail.show(record)
    },
    handleChange() {
        this.loadData()
    },
  }
}
</script>


<style scoped lang='less'>
.filter-line{
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}
</style>