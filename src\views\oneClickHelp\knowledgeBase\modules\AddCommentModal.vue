<template>
  <yq-modal
    :title='title'
    :width='width'
    :centered='true'
    :visible='visible'
    :destroyOnClose='true'
    switchFullscreen
    cancelText='关闭'
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @ok='handleOk'
    @cancel='handleCancel'
  >
    <a-spin :spinning='confirmLoading'>
      <j-form-container :disabled='disableSubmit'>
        <a-form-model ref='form' :model='model' :rules='validatorRules' slot='detail' v-bind='formItemLayout'>
          <a-row>
            <a-col :span='24'>
              <a-form-model-item label='评论' prop='comment'>
                <a-textarea v-model='model.comment' :autoSize='{minRows:6,maxRows:10}' :allow-clear='true' placeholder='请输入评论信息'></a-textarea>
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </j-form-container>
    </a-spin>
  </yq-modal>
</template>
<script>
import {httpAction } from '@api/manage'
import { ValidateRequiredFields } from '@/utils/rules'
export default {
  name: 'AddCommentModal',
  props: {
    knowledgeId:{
      type:String,
      required:true,
      default:''
    },
  },
  data() {
    return {
      title: '新增',
      width: '600px',
      disableSubmit: false,
      visible: false,
      confirmLoading: false,
      formItemLayout: {
        labelCol: {
          xs:{span:24 },
          sm:{span:24},
          md:{span:3}
        },
        wrapperCol: {
          xs:{span:24 },
          sm:{span:24},
          md:{span:20}
        }
      },
      model: {},
      validatorRules: {
        comment: [
          { required: true,validator: (rule, value, callback) =>ValidateRequiredFields(rule, value, callback,'评论',200,1)}
        ]
      },
      url: {
        add: '/kbase/knowledges/comment'
      }
    }
  },
  methods: {
    add() {
      this.visible = true
      this.$nextTick(() => {
        this.model = {}
      })
    },
    handleOk() {
      let that = this
      that.$refs.form.validate((err, values) => {
        if (err) {
          that.confirmLoading = true
          let httpurl = that.url.add
          let method = 'post'

          let formData = JSON.parse(JSON.stringify(that.model))
          formData.knowledgeId=that.knowledgeId
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
                that.close()
              } else {
                that.$message.warning(res.message)
              }
              that.confirmLoading = false
            }).catch((err) => {
            that.$message.warning(err.message)
            that.confirmLoading = false
          })
        }
      })
    },
    handleCancel() {
      this.close()
    },
    close() {
      this.visible = false
    },
  }
}
</script>
<style scoped lang='less'>
@import "~@assets/less/onclickStyle.less";
@import "~@assets/less/normalModal.less";
</style>