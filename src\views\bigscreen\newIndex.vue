<template>
  <div class="body">
    <div class="core">
      <div class="core-top">
        <div class="topTitle">
          <img src="@/assets/bigScreen/9.png" alt />
          <span>资源总览</span>
        </div>
        <div class="core-top-body">
          <vue-seamless-scroll :data="resourcesList" :class-option="warning" class="seamless-warp">
            <ul>
              <li class="core-top-body-li" v-for="(item,index) in resources"  :key="index">
                <div class="pao_box other1"></div>
                <div class="pao_box other2"></div>
                <div class="pao_box other3"></div>
                <div class="pao_box other4"></div>
                <div class="pao_box other5"></div>
                <div class="pao_box area">
                  <div class="pao_box_count">{{item.data[0].countStatistics}}</div>
                  <div class="pao_box_title">{{item.data[0].gatewayStatistics}}</div>
                </div>
                <div class="pao_box appSystem">
                  <div class="pao_box_count">{{item.data[0].countNumber | unitChange}}</div>
                  <div class="pao_box_title">{{item.data[0].gatewayName}}</div>
                </div>
                <div class="pao_box netServer" v-if="item.data.length == 2">
                  <div class="pao_box_count">{{item.data[1].countNumber | unitChange}}</div>
                  <div class="pao_box_title">{{item.data[1].gatewayName}}</div>
                </div>
                <div class="pao_box appDatabase" v-if="item.data.length == 2">
                  <div class="pao_box_count">{{item.data[1].countStatistics | unitChange}}</div>
                  <div class="pao_box_title">{{item.data[1].gatewayStatistics}}</div>
                </div>
              </li>
            </ul>
          </vue-seamless-scroll>
        </div>
      </div>
      <div class="core-core">
        <seamless-scroll :titleL="titleL" :dataUrl="url.cityTerminal"></seamless-scroll>
        <!-- <div class="core-core-tableTitle">
          <span>地区名称</span>
          <span>终端总数</span>
          <span>在线数</span>
          <span>离线数</span>
          <span>在线率</span>
        </div>
        <div class="core-core-table">
          <vue-seamless-scroll :data="cityTerminal" :class-option="warning" class="seamless-warp">
            <ul>
              <li v-for="item in cityTerminal" :key="item.id">
                <span :title="item.city">{{ item.city }}</span>
                <span :title="item.allCount">{{ item.allCount }}</span>
                <span :title="item.onCount">{{ item.onCount }}</span>
                <span :title="item.offCount">{{ item.offCount }}</span>
                <span :title="item.onRate">{{ item.onRate }}</span>
              </li>
            </ul>
          </vue-seamless-scroll>
        </div>-->
      </div>
    </div>

    <div class="left">
      <div class="left-top">
        <div class="leftEchartsTitle">
          <img src="@/assets/bigScreen/9.png" alt />
          <span>地图</span>
        </div>
        <div class="left-top-core" id="echartsMap"></div>
      </div>
    </div>

    <div class="right">
      <div class="right-top">
        <div class="topTitle">
          <img src="@/assets/bigScreen/9.png" alt />
          <span>全市资源在线率</span>
        </div>
        <div class="right-top-body">
          <div class="right-top-body-left">
            <div class="healthPanelECharts" id="healthDegree"></div>
          </div>
          <div class="right-top-body-right">
            <div class="right-top-tableTitle">
              <span>系统</span>
              <span>在线率(%)</span>
            </div>
            <div class="right-top-table">
              <!-- <vue-seamless-scroll
                :data="warningData"
                :class-option="warning"
                class="seamless-warp"
              >-->
              <ul>
                <li>
                  <span>{{ title }}</span>
                  <span>{{ healthDegree }}</span>
                </li>
              </ul>
              <!-- </vue-seamless-scroll> -->
            </div>
          </div>
        </div>
      </div>
      <!-- <div class="right-core">
        <div class="rightBottomTitle first-title">
          <img src="@/assets/bigScreen/9.png" alt />
          <span>各区县使用率</span>
        </div>
        <div class="right-core-body">
          <vue-seamless-scroll
                :data="cityOnRates"
                :class-option="warning"
                class="seamless-warp"
              >
            <ul>
              <li v-for="(item,key) in cityOnRates" :key="key">
                <span>{{ item.city }}</span>
                <span>{{ item.onRate }}</span>
              </li>
            </ul>
          </vue-seamless-scroll>
        </div>
      </div>-->
      <div class="core-bottom">
        <!-- <div class="topTitle">
          <img src="@/assets/bigScreen/9.png" alt />
          <span>告警轮播</span>
        </div>-->
        <div class="core-bottom-core">
          <div class="core-bottom-tableTitle">
            <span>资源名称</span>
            <span>告警级别</span>
            <span>告警时间</span>
            <span>告警信息</span>
            <span>使用人</span>
          </div>
          <div class="core-bottom-table">
            <vue-seamless-scroll :data="warningData" :class-option="warning" class="seamless-warp">
              <ul>
                <li v-for="item in warningData" :key="item.id">
                  <span :title="item.deviceName">{{ item.deviceName }}</span>
                  <span :title="item.alarmLevel">{{ item.alarmLevel }}</span>
                  <span :title="item.createTime">{{ item.createTime }}</span>
                  <span :title="item.templateName">{{ item.templateName }}</span>
                  <span :title="item.userName">{{ item.userName }}</span>
                </li>
              </ul>
            </vue-seamless-scroll>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import echarts from 'echarts'
import 'echarts/lib/component/graphic'
import '../../../node_modules/echarts/map/js/china' // 引入中国地图数据
import vueSeamlessScroll from 'vue-seamless-scroll'
import { deleteAction, getAction, putAction, httpAction } from '@/api/manage'
import SeamlessScroll from '@/components/bigScreen/SeamlessScroll.vue'
// 地图数据
// const mapJson = require('./json/echarts.json')
const mapJson = window.echarts
echarts.registerMap('citymap', mapJson)

// window.dict.features[0].value

export default {
  data() {
    return {
      title: window._CONFIG['systemName'],
      warningData: [],
      // cityTerminal:[],
      resourcesData: [],
      resourcesWarning: [],
      workOrderData: [],
      workOrderDataOne: {},
      workOrderDataTwo: {},
      workOrderDataThree: {},
      url: {
        alarmList: '/data-analysis/device/alarm/list',
        cityTerminal: '/terminal/terminalDevices/getDeptRes', //六盘水各单位终端信息
        // cityTerminal: '/terminal/terminalDevices/getCityRes',//遵义各地区终端信息
        overview: '/data-analysis/device/overview',
        alarmTop: '/data-analysis/device/alarm/top',
        status: '/data-analysis/device/status',
        orderCount: '/data-analysis/order/count',
        resourceStatistic: '/terminal/terminalDevices/getResources', //资源总览
        cpuTop: '/data-analysis/device/cpu/top',
        healthDegree: '/terminal/terminalDevices/getResources1', //安全监控健康度
        cityOnRate: '/terminal/terminalDevices/getCityOnRate', //各区县使用率
        getCity: '/terminal/terminalDevices/getCity', //地图地区数据
        getResByCity: '/terminal/terminalDevices/getResByCity' //地图鼠标悬浮接口
      },
      theServerData: [],
      percent1: {},
      percent2: {},
      percent3: {},
      cpuTopData: [],
      resources: [],
      resourcesList:[],
      healthDegree: 0,
      cityOnRates: [],
      cityRes: [],
      cityVal: '',
      //六盘水配置
      titleL: {
        firstTitle: '单位名称',
        secondTitle: '终端总数',
        thirdTitle: '在线数',
        fourthTitle: '离线数',
        fifthTitle: '在线率'
      }
      //遵义
      // titleL:{
      //   firstTitle:'地区名称',
      //   secondTitle:'终端总数',
      //   thirdTitle:'在线数',
      //   fourthTitle:'离线数',
      //   fifthTitle:'在线率'
      // },
    }
  },
  components: {
    vueSeamlessScroll,
    'seamless-scroll': SeamlessScroll
  },
  filters:{
    unitChange(value){
      value = value.toString()
      if (value.indexOf('%')!=-1) {
        return value
      } else {
        return value + '个'
      }
    }
  },
  created() {},
  mounted() {
    //this.cpuTop()
    this.echart_map()
    this.alarmList()
    //this.geCityTerminal()
    this.overview()
    // this.getCityOnRate()
    this.getHealthDegree()
    //this.status()
    this.alarmTop()
    this.orderCount()
    this.resourceStatistic()
    this.getResByCity()
    this.cityVal = setInterval(() => {
      this.getResByCity()
    }, 60000)
  },
  computed: {
    warning() {
      return {
        step: 0.1, // 数值越大速度滚动越快
        limitMoveNum: 2, // 开始无缝滚动的数据量 this.dataList.length
        hoverStop: false, // 是否开启鼠标悬停stop
        direction: 1, // 0向下 1向上 2向左 3向右
        // openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 86, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
        // singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
        waitTime: 2 // 单步运动停止的时间(默认值1000ms)
      }
    }
  },
  beforeDestroy() {
    clearInterval(this.cityVal)
  },
  methods: {
    // 地图
    echart_map() {
      let myChart = this.$echarts.init(document.getElementById('echartsMap'))

      //城市经纬度数据
      var geoCoordMap = window.longitudeLatitude
      var GZData = []
      var option = {
        tooltip: {
          show: true,
          // trigger: 'item',
          triggerOn: 'mousemove',
          formatter: value => {
            //获取legend显示内容
            var that = this
            var dataset = value.data
            var cityRateInfo = this.cityRes.find(ele => ele.cityId === dataset.value[2])
            var rtStr = ''
            var AllCount = ''
            var onRate = ''
            return (
              dataset.name + '<br />终端总数：' + cityRateInfo.AllCount + '&nbsp;&nbsp;在线率：' + cityRateInfo.onRate
            )
          }
        },
        series: [],
        geo: {
          type: 'scatter',
          coordinateSystem: 'bmap',
          tooltip: {
            show: true
          },
          roam: true,
          show: true,
          zoom: 1.2,
          scaleLimit: { min: 1, max: 12 }, // 缩放级别
          map: 'citymap',
          // 定义样式
          itemStyle: {
            // 普通状态下的样式
            normal: {
              borderColor: '#07919e',
              areaColor: '#1c2f59'
            },
            // 高亮状态下的样式,默认黄色
            emphasis: {
              areaColor: '#1f2533'
            }
          },
          label: {
            normal: {
              //静态的时候展示样式
              show: false, //是否显示地图区的名称
              textStyle: {
                color: '#39fdf4'
              }
            },
            emphasis: {
              //动态展示的样式
              show: true, //是否显示地图区的名称
              color: '#39fdf4'
            }
          }
        }
      }

      // 使用刚指定的配置项和数据显示图表。
      myChart.setOption(option)
      window.addEventListener('resize', () => {
        myChart.resize()
      })
      getAction(this.url.getCity).then(res => {
        if (res.success) {
          //城市流线数据
          GZData = res.result
          const convertData = function(data) {
            var res = []
            for (var i = 0; i < data.length; i++) {
              var geoCoord = geoCoordMap[data[i].cityName]
              if (geoCoord) {
                res.push({
                  name: data[i].cityName,
                  value: geoCoord.concat(data[i].cityId)
                })
              }
            }
            return res
          }
          var color = ['#c5f80e']
          var series = []
          ;[['cityMap', GZData]].forEach(function(item, i) {
            series.push({
              name: item[0],
              type: 'scatter',
              coordinateSystem: 'geo',
              zlevel: 2,
              symbolSize: 20, //六盘水需要放开此配置
              rippleEffect: {
                brushType: 'stroke'
              },
              label: {
                normal: {
                  show: true,
                  position: 'left',
                  formatter: '{b}',
                  color: '#fff'
                }
              },
              itemStyle: {
                color: '#00A06A'
              },
              data: convertData(item[1])
            })
          })
          myChart.setOption({
            series: series
          })
        }
      })
    },
    getResByCity() {
      getAction(this.url.getResByCity).then(res => {
        if (res.success) {
          this.cityRes = res.result
        }
      })
    },
    // 告警轮播数据
    geCityTerminal() {
      getAction(this.url.cityTerminal).then(res => {
        if (res.code == 200) {
          this.cityTerminal = this.cityTerminal.concat(res.result)
        }
      })
    },

    // 告警轮播数据
    alarmList() {
      getAction(this.url.alarmList).then(res => {
        if (res.code == 200) {
          this.warningData = res.result
        }
      })
    },

    // 设备总览数据
    overview() {
      getAction(this.url.overview).then(res => {
        if (res.code == 200) {
          if (res.result.length <= 8) {
            this.resourcesData = res.result
          } else {
            this.resourcesData = res.result.slice(0, 8)
          }
        }
      })
    },

    getCityOnRate() {
      getAction(this.url.cityOnRate).then(res => {
        if (res.success) {
          this.cityOnRates = this.cityOnRates.concat(res.result)
        }
      })
    },

    getHealthDegree() {
      getAction(this.url.healthDegree).then(res => {
        if (res.success) {
          //this.assetStatisticsHistogram(res.result, res.result.line)
          this.setHealthDegree(res.result.onRate.substring(0, res.result.onRate.length - 1) || 0)
          this.healthDegree = parseFloat(res.result.onRate.substring(0, res.result.onRate.length - 1)) || 0
        }
      })
    },
    setHealthDegree(param) {
      let myChart = this.$echarts.init(document.getElementById('healthDegree'))
      myChart.setOption({
        // backgroundColor: '#02023E',
        tooltip: {
          formatter: '{a}<br/>{c}%'
        },
        toolbox: {
          show: true,
          feature: {
            mark: {
              show: false
            },
            restore: {
              show: false
            },
            saveAsImage: {
              show: false
            }
          }
        },
        series: [
          {
            name: '系统健康度',
            type: 'gauge', //仪表盘
            min: 0,
            max: 100,
            splitNumber: 5,
            radius: '80%',
            axisLine: {
              // 坐标轴线
              lineStyle: {
                // 属性lineStyle控制线条样式
                color: [
                  [0.125, '#ff4500'],
                  [0.82, '#1e90ff'],
                  [1, 'lime']
                ],
                width: 3,
                shadowColor: '#fff', //默认透明
                shadowBlur: 10
              }
            },
            axisLabel: {
              // 坐标轴小标记
              textStyle: {
                // 属性lineStyle控制线条样式
                fontSize: '10',
                fontWeight: 'bolder',
                color: '#fff',
                shadowColor: '#fff', //默认透明
                shadowBlur: 10
              }
            },
            axisTick: {
              // 坐标轴小标记
              length: 15, // 属性length控制线长
              lineStyle: {
                // 属性lineStyle控制线条样式
                color: 'auto',
                shadowColor: '#fff', //默认透明
                shadowBlur: 10
              }
            },
            splitLine: {
              // 分隔线
              length: 15, // 属性length控制线长
              lineStyle: {
                // 属性lineStyle（详见lineStyle）控制线条样式
                width: 3,
                color: '#fff',
                shadowColor: '#fff', //默认透明
                shadowBlur: 10
              }
            },
            pointer: {
              // 分隔线
              length: '80%',
              width: '6',
              shadowColor: '#fff', //默认透明
              shadowBlur: 5
            },
            title: {
              show: false
            },
            detail: {
              borderColor: '#fff',
              shadowColor: '#fff',
              shadowBlur: 5,
              offsetCenter: [0, '80%'], // x, y，单位px
              textStyle: {
                // 其余属性默认使用全局文本样式，详见TEXTSTYLE
                fontSize: 25,
                fontWeight: 'bolder',
                color: '#fff'
              },
              formatter: '{value}%'
            },
            data: [param]
          }
        ]
      })
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    },

    // 设备状态监控数据
    status() {
      getAction(this.url.status).then(res => {
        if (res.success) {
          this.serverEcharts(res.result)
          this.appserverEcharts(res.result)
          this.databaseEcharts(res.result)
          this.netdeviceEcharts(res.result)
        }
      })
    },

    // 设备状态监控环形图
    serverEcharts(data) {
      for (let i = 0; i < data.length; i++) {
        data[i].value = data[i].values[0].value
      }

      let myChart = this.$echarts.init(document.getElementById('serverEcharts'))
      myChart.setOption({
        tooltip: {
          show: true,
          trigger: 'item',
          transitionDuration: 0 //echart防止tooltip的抖动
        },
        legend: {
          top: '34%',
          right: '2%',
          orient: 'vertical',
          textStyle: {
            color: '#fff'
          },
          formatter: function(name) {
            // 获取legend显示内容
            // let data = aaa
            let total = 0
            let tarValue = 0
            for (let i = 0, l = data.length; i < l; i++) {
              total += data[i].value
              if (data[i].name == name) {
                tarValue = data[i].value
              }
            }
            let p = ((tarValue / total) * 100).toFixed(2)
            return name + ' ' + ' ' + p + '%'
          }
        },
        color: ['#058eee', '#ffba13', '#dd3f2a'],
        series: [
          {
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            hoverAnimation: false,
            label: {
              show: false,
              position: 'center'
            },

            labelLine: {
              show: false
            },
            center: ['40%', '50%'],
            data: data
          }
        ]
      })
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    },
    // 设备状态监控环形图
    appserverEcharts(data) {
      for (let i = 0; i < data.length; i++) {
        data[i].value = data[i].values[0].value
      }

      let myChart = this.$echarts.init(document.getElementById('appserverEcharts'))
      myChart.setOption({
        tooltip: {
          show: true,
          trigger: 'item',
          transitionDuration: 0 //echart防止tooltip的抖动
        },
        legend: {
          top: '34%',
          right: '2%',
          orient: 'vertical',
          textStyle: {
            color: '#fff'
          },
          formatter: function(name) {
            // 获取legend显示内容
            // let data = aaa
            let total = 0
            let tarValue = 0
            for (let i = 0, l = data.length; i < l; i++) {
              total += data[i].value
              if (data[i].name == name) {
                tarValue = data[i].value
              }
            }
            let p = ((tarValue / total) * 100).toFixed(2)
            return name + ' ' + ' ' + p + '%'
          }
        },
        color: ['#058eee', '#ffba13', '#dd3f2a'],
        series: [
          {
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            hoverAnimation: false,
            label: {
              show: false,
              position: 'center'
            },

            labelLine: {
              show: false
            },
            center: ['40%', '50%'],
            data: data
          }
        ]
      })
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    },
    // 设备状态监控环形图
    databaseEcharts(data) {
      for (let i = 0; i < data.length; i++) {
        data[i].value = data[i].values[0].value
      }

      let myChart = this.$echarts.init(document.getElementById('databaseEcharts'))
      myChart.setOption({
        tooltip: {
          show: true,
          trigger: 'item',
          transitionDuration: 0 //echart防止tooltip的抖动
        },
        legend: {
          top: '34%',
          right: '2%',
          orient: 'vertical',
          textStyle: {
            color: '#fff'
          },
          formatter: function(name) {
            // 获取legend显示内容
            // let data = aaa
            let total = 0
            let tarValue = 0
            for (let i = 0, l = data.length; i < l; i++) {
              total += data[i].value
              if (data[i].name == name) {
                tarValue = data[i].value
              }
            }
            let p = ((tarValue / total) * 100).toFixed(2)
            return name + ' ' + ' ' + p + '%'
          }
        },
        color: ['#058eee', '#ffba13', '#dd3f2a'],
        series: [
          {
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            hoverAnimation: false,
            label: {
              show: false,
              position: 'center'
            },

            labelLine: {
              show: false
            },
            center: ['40%', '50%'],
            data: data
          }
        ]
      })
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    },
    // 设备状态监控环形图
    netdeviceEcharts(data) {
      for (let i = 0; i < data.length; i++) {
        data[i].value = data[i].values[0].value
      }

      let myChart = this.$echarts.init(document.getElementById('netdeviceEcharts'))
      myChart.setOption({
        tooltip: {
          show: true,
          trigger: 'item',
          transitionDuration: 0 //echart防止tooltip的抖动
        },
        legend: {
          top: '34%',
          right: '2%',
          orient: 'vertical',
          textStyle: {
            color: '#fff'
          },
          formatter: function(name) {
            // 获取legend显示内容
            // let data = aaa
            let total = 0
            let tarValue = 0
            for (let i = 0, l = data.length; i < l; i++) {
              total += data[i].value
              if (data[i].name == name) {
                tarValue = data[i].value
              }
            }
            let p = ((tarValue / total) * 100).toFixed(2)
            return name + ' ' + ' ' + p + '%'
          }
        },
        color: ['#058eee', '#ffba13', '#dd3f2a'],
        series: [
          {
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            hoverAnimation: false,
            label: {
              show: false,
              position: 'center'
            },

            labelLine: {
              show: false
            },
            center: ['40%', '50%'],
            data: data
          }
        ]
      })
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    },

    // 资源告警量数据
    alarmTop() {
      getAction(this.url.alarmTop).then(res => {
        if (res.code == 200) {
          this.resourcesWarning = res.result
          //this.resourcesWarningHistogram(res.result)
        }
      })
    },

    // 资源告警量柱状图
    resourcesWarningHistogram(data = []) {
      function attackSourcesDataFmt(sData) {
        var sss = []
        sData.forEach(function(item, i) {
          sss.push({
            value: item.value + '%'
          })
        })
        return sss.reverse()
      }
      let arr = []
      let brr = []
      data.forEach(e => {
        arr.push(e.name)
        brr.push(e.value)
      })
      // brr.reverse()
      let myChart = this.$echarts.init(document.getElementById('resourcesWarningHistogram'))
      myChart.setOption({
        tooltip: {
          show: true,
          trigger: 'axis',
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
          },
          transitionDuration: 0 //echart防止tooltip的抖动
        },
        xAxis: {
          type: 'value',
          splitLine: { show: false }, //去除网格线
          show: false
        },
        yAxis: [
          {
            type: 'category',
            data: arr,
            splitLine: { show: false }, //去除网格线
            axisTick: {
              show: false
            },
            axisLine: {
              show: false, //y轴线消失
              lineStyle: {
                //y轴字体颜色
                color: '#f6f6f6'
              }
            },
            axisLabel: {
              formatter: value => {
                if (value.length > 8) {
                  return value.substring(0, 8) + '...'
                } else {
                  return value
                }
              }
            }
          },
          {
            type: 'category',
            inverse: true,
            axisTick: 'none',
            axisLine: 'none',
            show: true,
            axisLabel: {
              textStyle: {
                color: '#00a1e7',
                fontSize: '14'
              }
            },
            data: attackSourcesDataFmt(data)
          }
        ],

        grid: {
          top: 0,
          left: 80, // 调整这个属性
          right: 60,
          bottom: 0
        },
        series: [
          {
            data: brr,
            type: 'bar',
            showBackground: true,
            backgroundStyle: {
              color: 'rgba(180, 180, 180, 0.2)' //柱状图背景颜色
            },
            barWidth: 10, //柱图宽度
            itemStyle: {
              emphasis: {
                barBorderRadius: 30
              },
              normal: {
                barBorderRadius: [10, 10, 10, 10],
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                  {
                    offset: 0,
                    color: '#3679fb'
                  },
                  {
                    offset: 1,
                    color: '#0cf6f7'
                  }
                ])
                // label: {
                //   show: true,
                //   position: 'right',
                //   textStyle: {
                //     color: '#00a1e7',
                //     fontSize: 16,
                //   },
                // },
              }
            },
            backgroundStyle: {
              color: 'rgba(255,255,255,0)'
            }
          }
        ]
      })
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    },

    // 工单统计数据
    orderCount() {
      getAction(this.url.orderCount).then(res => {
        if (res.code == 200) {
          this.workOrderDataOne = res.result.number[0]
          this.workOrderDataTwo = res.result.number[1]
          this.workOrderDataThree = res.result.number[2]
          this.percent1 = res.result.chart[0]
          this.percent2 = res.result.chart[1]
          this.percent3 = res.result.chart[2]
          // this.liquidfillOne(res.result.chart[0])
          // this.liquidfillOne()
        }
      })
    },

    // 尝试做水球图，但是没做出来
    liquidfillOne() {
      let myChart = this.$echarts.init(this.$refs.liquidfillOne)
      var max = 100 //满刻度大小
      var data = 56
      var dataPer = data / 100
      var option = {
        title: [
          {
            text: '能耗环比',
            x: '50%',
            y: '90%',
            textAlign: 'center',
            textStyle: {
              fontSize: '14',
              fontWeight: '400',
              color: '#fff',
              textAlign: 'center'
            }
          },
          {
            top: '47%',
            left: 'center',
            text: data + ' %',
            textStyle: {
              color: '#fff',
              // fontStyle: 'normal',
              fontWeight: '400',
              fontSize: 16
            }
          }
        ],
        series: [
          {
            type: 'liquidFill',
            itemStyle: {
              opacity: 0.8, //波浪的透明度
              shadowBlur: 10, //波浪的阴影范围
              shadowColor: '#FFB931' //阴影颜色
            },
            radius: '60%',
            //水波
            color: [' rgba(28,58,154,.6)', '  rgba(28,58,154,.4)'],
            data: [dataPer, dataPer],
            // background: '#000',
            center: ['50%', '52%'],
            backgroundStyle: {
              color: 'transparent'
            },
            label: {
              normal: {
                formatter: '',
                textStyle: {
                  fontSize: 12
                }
              }
            },
            outline: {
              itemStyle: {
                borderColor: 'transparent',
                borderWidth: 5
              },
              borderDistance: 0
            }
          },
          //外环线
          {
            color: ['#1C3A9A', 'rgba(28,58,154,.2)'],
            type: 'pie',
            center: ['50%', '52%'],
            radius: ['60%', '68%'],
            hoverAnimation: false,
            data: [
              {
                name: '',
                value: data,
                label: {
                  show: false,
                  position: 'center',
                  color: '#fff',
                  fontSize: 38,
                  fontWeight: 'bold',
                  formatter: function(o) {
                    return data
                  }
                },
                labelLine: {
                  show: false
                }
              },
              {
                //画剩余的刻度圆环
                name: '',
                value: max - data,
                label: {
                  show: false
                },
                labelLine: {
                  normal: {
                    show: false
                  },
                  emphasis: {
                    show: false
                  }
                }
              }
            ]
          }
        ]
      }
      myChart.setOption(option)
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    },

    formatter: function(num) {
      if (!num) {
        return ''
      }
      return num.toFixed(2) //小数点后几位，数字就是几小数点后几位
    },

    // 资产统计柱状图数据
    resourceStatistic() {
      getAction(this.url.resourceStatistic).then(res => {
        if (res.success) {
          this.resourcesList = res.result.net1.concat(res.result.net)
          this.resources.push({id:'0',data:[...res.result.net1]})
          var list = []
          if(!!res.result.net){
            for(var i=0; i<res.result.net.length; i=i+2){
              list.splice(0,list.length)
              list.push(res.result.net[i])
              if(i+1<res.result.net.length){
                list.push(res.result.net[i+1])
              }
              this.resources.push({id:i+1+'',data:[...list]})
            }
          }
        }
      })
    },

    // 资产统计柱状图
    assetStatisticsHistogram(data, dataList) {
      let xArr = []
      let yValue1 = []
      let yValue2 = []
      dataList.forEach(e => {
        xArr.push(e.name)
        yValue1.push(e.value1)
        yValue2.push(e.value2)
      })

      let myChart = this.$echarts.init(document.getElementById('assetStatisticsHistogram'))
      myChart.setOption({
        legend: {
          data: [data.value1, data.value2],
          left: 'right',
          textStyle: {
            color: '#c4e4fd'
          }
        },
        tooltip: {
          type: true,
          transitionDuration: 0 //echart防止tooltip的抖动
        },
        grid: {
          left: '3%',
          right: 10,
          bottom: '',
          top: '14%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: xArr,
          axisLine: {
            lineStyle: {
              type: 'solid',
              color: '#5a595f', //左边线的颜色
              width: '1' //坐标线的宽度
            }
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#fff'
            }
          }
        },
        yAxis: {
          type: 'value',
          axisLine: {
            show: false, //y轴线消失
            lineStyle: {
              //y轴字体颜色
              color: '#c4c4c6'
            }
          },
          axisTick: {
            show: false
          },
          //网格线颜色
          splitLine: {
            show: true,
            lineStyle: {
              color: ['#424348'],
              width: 1,
              type: 'solid'
            }
          }
        },
        series: [
          {
            // 上半截柱子
            name: data.value1,
            type: 'bar',
            barGap: '-100%',
            barWidth: '10',
            z: 0,
            itemStyle: {
              color: '#009eff'
            },
            data: yValue1
          },
          // {
          //   name: data.value2,
          //   type: 'bar',
          //   stack: 'total',
          //   // label: {
          //   //   show: true,
          //   // },
          //   barWidth: 10, //柱图宽度
          //   emphasis: {
          //     focus: 'series',
          //   },
          //   data: yValue2,
          //   itemStyle: {
          //     normal: {
          //       color: '#4bffdc',
          //     },
          //   },
          // },
          {
            // 下半截柱子
            name: data.value2,
            type: 'bar',
            barGap: '-100%',
            barWidth: '10',
            // z: 0,
            itemStyle: {
              color: '#4bffdc'
            },
            data: yValue2
          }
          // {
          //   name: data.value1,
          //   type: 'bar',
          //   stack: 'total',
          //   // label: {
          //   //   show: true,
          //   // },
          //   barWidth: 10, //柱图宽度
          //   emphasis: {
          //     focus: 'series',
          //   },
          //   data: yValue1,
          //   itemStyle: {
          //     normal: {
          //       color: '#009eff',
          //     },
          //   },
          // },
        ]
      })
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    },

    // 服务器CPU使用率数据
    cpuTop() {
      getAction(this.url.cpuTop).then(res => {
        if (res.code == 200) {
          this.theServerHistogram(res.result)
          this.cpuTopData = res.result
        }
      })
    },

    // 服务器CPU使用率柱状图
    theServerHistogram(data = []) {
      function attackSourcesDataFmt(sData) {
        var sss = []
        sData.forEach(function(item, i) {
          sss.push({
            value: item.value + '%'
          })
        })
        return sss.reverse()
      }

      let arr = []
      let brr = []
      data.forEach(e => {
        arr.push(e.name)
        brr.push(e.value)
      })
      // brr.reverse()
      let myChart = this.$echarts.init(document.getElementById('theServerHistogram'))
      myChart.setOption({
        tooltip: {
          show: true,
          trigger: 'axis',
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
          },
          transitionDuration: 0 //echart防止tooltip的抖动
        },
        xAxis: {
          type: 'value',
          splitLine: { show: false }, //去除网格线
          show: false
        },
        yAxis: [
          {
            type: 'category',
            data: arr,
            splitLine: { show: false }, //去除网格线
            axisTick: {
              show: false
            },
            axisLine: {
              show: false, //y轴线消失
              lineStyle: {
                //y轴字体颜色
                color: '#f6f6f6'
              }
            },
            axisLabel: {
              formatter: value => {
                if (value.length > 8) {
                  return value.substring(0, 8) + '...'
                } else {
                  return value
                }
              }
            }
          },
          {
            type: 'category',
            inverse: true,
            axisTick: 'none',
            axisLine: 'none',
            show: true,
            axisLabel: {
              textStyle: {
                color: '#00a1e7',
                fontSize: '14'
              }
            },
            data: attackSourcesDataFmt(data)
          }
        ],

        grid: {
          top: 0,
          left: 90, // 调整这个属性
          right: 60,
          bottom: 0
        },
        series: [
          {
            data: brr,
            type: 'bar',
            showBackground: true,
            backgroundStyle: {
              color: 'rgba(180, 180, 180, 0.2)' //柱状图背景颜色
            },
            barWidth: 10, //柱图宽度
            itemStyle: {
              emphasis: {
                barBorderRadius: 30
              },
              normal: {
                barBorderRadius: [10, 10, 10, 10],
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                  {
                    offset: 0,
                    color: '#3679fb'
                  },
                  {
                    offset: 1,
                    color: '#0cf6f7'
                  }
                ])
                // label: {
                //   show: true,
                //   position: 'right',
                //   textStyle: {
                //     color: '#00a1e7',
                //     fontSize: 16,
                //   },
                // },
              }
            },
            backgroundStyle: {
              color: '#293e6d'
            }
          }
        ]
      })
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    }
  }
}
</script>
<style lang="less" scoped>
.body {
  padding: 0.25rem /* 20/80 */ 0.2rem 0.1125rem 0.2rem;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  .left {
    width: 49%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .left-top {
      height: 100%;
      width: 100%;
      background: #131419;
      border-radius: 0.075rem /* 6/80 */;
      .left-top-core {
        width: 100%;
        height: 93%;
        // background-color: #fff;
      }
    }
  }
  .core {
    width: 25%;
    height: 100%;
    // margin: 0 0.25rem /* 20/80 */;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .core-top {
      width: 100%;
      height: 27%;
      background: #131419;
      border-radius: 0.075rem /* 6/80 */;
      .core-top-body {
        color: #fff;
        // display: flex;
        // position: relative;
        height: 2.25rem /* 180/80 */;
        overflow:hidden;
        .seamless-warp {
          // width: 100%;
          ul {
            width: 100%;
            padding: 0;
            margin: 0;
            display:flex;
            flex-direction: column;
            .core-top-body-li {
              height: 2.25rem /* 180/80 */;
              position:relative;
              .pao_box {
                position: absolute;
                border-radius: 100%;
                .pao_box_count {
                  height: 0.5rem /* 40/80 */;
                  font-size: 0.25rem /* 20/80 */;
                  color: #ffffff;
                  letter-spacing: 0;
                  text-align: center;
                  line-height: 0.5rem /* 40/80 */;
                  position: absolute;
                  top: 50%;
                  margin-top: -40px;
                  left: 0;
                  right: 0;
                  min-width: 80px;
                }
                .pao_box_title {
                  height: 0.25rem /* 20/80 */;
                  font-size: 0.175rem /* 14/80 */;
                  color: #ffffff;
                  letter-spacing: 0;
                  text-align: center;
                  line-height: 0.25rem /* 20/80 */;
                  position: absolute;
                  top: 50%;
                  left: 0;
                  right: 0;
                  min-width: 80px;
                }
              }
              .other1 {
                box-shadow: inset 0 0 20px 0 #54f3fc;
                left: 5%;
                top: 12%;
                width: 40px;
                height: 40px;
              }
              .other2 {
                box-shadow: inset 0 0 20px 0 #54f3fc;
                left: 18%;
                top: 64%;
                width: 30px;
                height: 30px;
              }
              .other3 {
                box-shadow: inset 0 0 10px 0 #54f3fc;
                left: 71%;
                top: 18%;
                width: 20px;
                height: 20px;
              }
              .other4 {
                box-shadow: inset 0 0 20px 0 #54f3fc;
                left: 54%;
                top: 62%;
                width: 40px;
                height: 40px;
              }
              .other5 {
                box-shadow: inset 0 0 20px 0 #54f3fc;
                left: 41%;
                top: 28%;
                width: 40px;
                height: 40px;
              }
              .area {
                box-shadow: inset 0 0 50px 0 #54f3fc;
                left: 17%;
                top: 8%;
                width: 97px !important;
                height: 97px !important;
              }
              .appSystem {
                box-shadow: inset 0 0 50px 0 #e79818;
                left: 66%;
                top: 35%;
                width: 80px !important;
                height: 80px !important;
              }
              .netServer {
                box-shadow: inset 0 0 50px 0 #e8e212;
                left: 50%;
                top: 2%;
                width: 80px !important;
                height: 80px !important;
              }
              .appDatabase {
                box-shadow: inset 0 0 50px 0 #1e7cf5;
                left: 33%;
                bottom: 4%;
                width: 80px !important;
                height: 80px !important;
              }
            }
          }
        }
      }
    }
    .core-core {
      width: 100%;
      height: 72%;
      background: #131419;
      border-radius: 0.075rem /* 6/80 */;
      padding: 0 0.25rem /* 20/80 */;
      padding-top: 13px;
      // margin: 0.25rem /* 20/80 */ 0;
    }
  }
  .right {
    width: 25%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .right-top {
      width: 100%;
      height: 27%;
      background: #131419;
      border-radius: 0.075rem /* 6/80 */;
      .right-top-body {
        height: 80%;
        padding: 0 0.25rem /* 20/80 */;
        display: flex;
        align-items: center;
        .right-top-body-left {
          display: flex;
          width: 50%;
          height: 100%;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          .healthPanelECharts {
            width: 100%;
            height: 100%;
          }
        }
        .right-top-body-right {
          display: flex;
          width: 50%;
          height: 100%;
          flex-direction: column;
          justify-content: center;
          .right-top-tableTitle {
            display: flex;
            height: 30px;
            align-items: center;
            padding-bottom: 13px;
            border-bottom: 1px solid #1d44a9;
            span {
              font-size: 16px;
              width: 100%;
              text-align: center;
              color: #00c4f6;
            }
          }
          .right-top-table {
            width: 100%;
            height: 80%;
            overflow: hidden;
            // .seamless-warp {
            // width: 100%;
            ul {
              width: 100%;
              padding: 0;
              margin: 0;
              display: flex;
              flex-direction: column;
              justify-content: space-around;
              li {
                text-align: center;
                // height: 0.525rem /* 42/80 */;
                width: 100%;
                line-height: 20px;
                display: flex;
                justify-content: space-around;
                text-align: center;
                font-size: 14px;
                // padding: 5px 0;
                span {
                  width: 100%;
                  color: rgba(255, 255, 255, 0.75);
                  display: flex;
                  flex-direction: column;
                  justify-content: center;
                }
              }
              li:nth-child(2n + 0) {
                background: #1f2533;
                text-align: center;
              }
            }
            // }
          }
        }
      }
    }
    .core-bottom {
      height: 72%;
      width: 100%;
      // margin-top: 0.25rem /* 20/80 */;
      background: #131419;
      overflow: hidden;
      border-radius: 0.075rem /* 6/80 */;
      .core-bottom-core {
        width: 100%;
        height: 100%;
        padding: 0 0.25rem /* 20/80 */;
        padding-top: 13px;
        .core-bottom-tableTitle {
          display: flex;
          height: 30px;
          align-items: center;
          padding-bottom: 13px;
          border-bottom: 1px solid #1d44a9;
          span {
            font-size: 0.2rem /* 16/80 */;
            width: 100%;
            text-align: center;
            color: #00c4f6;
          }
        }
        .core-bottom-table {
          width: 100%;
          height: calc(100% - 30px);
          overflow: hidden;
          .seamless-warp {
            // width: 100%;
            ul {
              width: 100%;
              padding: 0;
              margin: 0;
              display: flex;
              flex-direction: column;
              justify-content: space-around;
              li {
                text-align: center;
                height: 0.525rem /* 42/80 */;
                width: 100%;
                line-height: 0.525rem /* 42/80 */;
                display: flex;
                justify-content: space-around;
                text-align: center;
                font-size: 14px;
                // padding: 5px 0;
                span {
                  // display:inline-block;
                  width: 25%;
                  color: rgba(255, 255, 255, 0.75);
                  overflow: hidden; /*溢出的部分隐藏*/
                  white-space: nowrap; /*文本不换行*/
                  text-overflow: ellipsis; /*ellipsis:文本溢出显示省略号（...）；clip：不显示省略标记（...），而是简单的裁切*/
                }
              }
              li:nth-child(2n + 0) {
                background: #1f2533;
                text-align: center;
              }
            }
          }
        }
      }
    }
    .right-core {
      width: 100%;
      height: 72%;
      background: #131419;
      border-radius: 0.075rem /* 6/80 */;
      .right-core-body {
        width: 92%;
        height: 92%;
        // height: 2.5rem /* 200/80 */;
        padding-top: 0.1875rem /* 15/80 */;
        overflow: hidden;
        .seamless-warp {
          width: 50%;
          ul {
            width: 100%;
            padding: 0;
            margin: 0;
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            li {
              text-align: center;
              height: 0.525rem /* 42/80 */;
              width: 100%;
              line-height: 20px;
              display: flex;
              justify-content: space-around;
              align-items: center;
              text-align: center;
              font-size: 14px;
              // padding: 5px 0;
              span {
                width: 100%;
              }
              span:nth-child(odd) {
                color: rgba(255, 255, 255, 0.75);
              }
              span:nth-child(even) {
                color: #45c5e0;
              }
            }
            // li:nth-child(2n + 0) {
            //   // background: #1f2533;
            //   text-align: center;
            // }
          }
        }
      }
    }
  }
}
.topTitle,
.topTitleCore,
.rightBottomTitle,
.leftEchartsTitle {
  height: 20%;
  display: flex;
  align-items: center;
  font-size: 0.225rem /* 18/80 */;
  color: #45c5e0;
  padding-left: 0.15rem /* 12/80 */;
  letter-spacing: 0.025rem /* 2/80 */;
  img {
    width: 0.125rem /* 10/80 */;
    height: 0.1625rem /* 13/80 */;
    margin-right: 0.0875rem /* 7/80 */;
  }
}
.topTitleCore {
  height: 14%;
}
.rightBottomTitle {
  height: 8%;
}
.first-title {
  padding-top: 0.125rem /* 10/80 */;
}
.leftEchartsTitle {
  height: 6%;
}
</style>