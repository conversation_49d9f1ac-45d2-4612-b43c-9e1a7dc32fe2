<template>
  <a-row style='height: 100%; margin-right: 4px; overflow: hidden; overflow-y: auto'>
    <a-col style='width: 100%; height: 100%; display: flex; flex-direction: column'>
      <!-- 查询区域 -->
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0', marginRight: '12px' }" class='card-style'
        style='width: 100%'>
        <div class='table-page-search-wrapper'>
          <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
            <a-row :gutter='24' ref='row'>
              <a-col :span='spanValue'>
                <a-form-item label='IP地址'>
                  <a-input :maxLength='maxLength' placeholder='请输入IP地址' v-model='queryParam.ipAddress' :allowClear='true' autocomplete='off' />
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label="使用状态">
                  <a-select v-model='queryParam.ipState' :allowClear="true" placeholder="请选择使用状态">
                    <a-select-option :value="'0'">未使用</a-select-option>
                    <a-select-option :value="'1'">已使用</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label="IP是否申请">
                  <a-select v-model='queryParam.isApply' :allowClear="true" placeholder="请选择IP是否申请">
                    <a-select-option :value="1">未申请</a-select-option>
                    <a-select-option :value="0">已申请</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span='spanValue' v-show='toggleSearchStatus'>
                <a-form-item label="是否在线">
                  <a-select v-model='queryParam.isOnline' :allowClear="true" placeholder="请选择是否在线">
                    <a-select-option :value="1">是</a-select-option>
                    <a-select-option :value="0">否</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span='spanValue' v-show='toggleSearchStatus'>
                <a-form-item label="IP和Mac是否一致">
                  <a-select v-model='queryParam.isEquals' :allowClear="true" placeholder="请选择IP和Mac是否一致">
                    <a-select-option :value="1">是</a-select-option>
                    <a-select-option :value="0">否</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span='colBtnsSpan()'>
                <span class='table-page-search-submitButtons'
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button type='primary' class='btn-search btn-search-style' @click='searchQuery'>查询</a-button>
                  <a-button class='btn-reset btn-reset-style' @click='searchReset'>重置</a-button>
                  <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <a-row>
          <a-col :span="24">
            <span>
            </span>
            <span style="float: right;margin-bottom: 12px;"><img src="~@/assets/return1.png" alt="" @click="getGo"
                style="width: 20px;height: 20px;cursor: pointer"></span>
          </a-col>
          <a-col :span="24">
            <a-table ref='table' bordered :row-key='(record, index) => {return record.id}' :columns='columns'
              :dataSource='dataSource' :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
              :pagination='ipagination' :loading='loading' @change='handleTableChange'>
            </a-table>
          </a-col>
        </a-row>
      </a-card>
    </a-col>
  </a-row>
</template>

<script>
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import {
    YqFormSearchLocation
  } from '@/mixins/YqFormSearchLocation'
  export default {
    name: 'AuditResultDetails',
    mixins: [JeecgListMixin, YqFormSearchLocation],
    props: {
      data: {
        type: Object
      }
    },
    watch: {
      data: {
        handler(nVal) {
          if (nVal && nVal.id) {
            this.queryParam.id = nVal.id
            this.loadData(1)
          }
        },
        immediate: true,
        deep: true,
      }
    },
    data() {
      return {
        maxLength:50,
        formItemLayout: {
          labelCol: {
            style: 'width:125px'
          },
          wrapperCol: {
            style: 'width:calc(100% - 125px)'
          }
        },
        disableMixinCreated: true,
        columns: [{
            title: 'IP地址',
            dataIndex: 'ipAddress'
          },
          {
            title: '使用状态',
            dataIndex: 'ipStateText'
          },
          {
            title: '非法占用',
            dataIndex: 'occupiedText'
          },
          {
            title: 'mac地址',
            dataIndex: 'macCode'
          },
          {
            title: '是否申请',
            dataIndex: 'isApplyText'
          },
          {
            title: 'IP和Mac是否一致',
            dataIndex: 'isEqualsText'
          },
          {
            title: '是否在线',
            dataIndex: 'isOnlineText'
          },
          {
            title: '是否扫描',
            dataIndex: 'isScanText'
          }
        ],
        url: {
          list: "/devops/ip/auditResult/queryById"
        }
      }
    },
    methods: {
      //返回上一级
      getGo() {
        this.$parent.pButton2(0);
      },
      searchReset() {
        this.queryParam = {
          id: this.data.id
        }
        this.loadData(1)
      },
    }
  }
</script>
<style scoped lang='less'>
  @import '~@assets/less/common.less';
  /*  table.gridtable {
    font-family: verdana, arial, sans-serif;
    font-size: 14px;
    color: #606266;
    border-width: 1px;
    border-color: #e8e8e8;
    border-collapse: collapse;
    text-align: left;
    width: 100%;
  }
  table.gridtable td {
    border-width: 1px;
    border-style: solid;
    border-color: #e8e8e8;
  }
  .leftTd {
    width: 17%;
    background-color: #FAFAFA;
    padding: 16px 24px;
    text-align: center;
  }
  .rightTd {
    width: 35%;
    padding: 16px 24px;
  }*/
</style>