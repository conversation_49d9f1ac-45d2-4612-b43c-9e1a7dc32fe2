import FlowGraph from '../../../graph/index'
export function gridOpt (globalGridAttr) {
  let options
  if (globalGridAttr.type === 'doubleMesh') {
    options = {
      type: globalGridAttr.type,
      args: [
        {
          color: globalGridAttr.color,
          thickness: globalGridAttr.thickness
        },
        {
          color: globalGridAttr.colorSecond,
          thickness: globalGridAttr.thicknessSecond,
          factor: globalGridAttr.factor
        }
      ]
    }
  } else {
    options = {
      type: globalGridAttr.type,
      args: [
        {
          color: globalGridAttr.color,
          thickness: globalGridAttr.thickness
        }
      ]
    }
  }
  const { graph } = FlowGraph
  graph.drawGrid(options)
}

export function gridSizeOpt (globalGridAttr) {
  const { graph } = FlowGraph
  graph.setGridSize(globalGridAttr.size)
}

const tryToJSON = (val) => {
  try {
    return JSON.parse(val)
  } catch (error) {
    return val
  }
}

export function backGroundOpt (globalGridAttr) {
  const options = {
    color: globalGridAttr.bgColor,
    // image: globalGridAttr.showImage ? require('@/assets/logo.svg') : undefined,
    image: (globalGridAttr.showImage && globalGridAttr.bgimg != "") ? globalGridAttr.bgimg : undefined,
    repeat: globalGridAttr.repeat,
    angle: globalGridAttr.angle,
    size: tryToJSON(globalGridAttr.bgSize),
    position: tryToJSON(globalGridAttr.position),
    opacity: globalGridAttr.opacity,
    transition: globalGridAttr.transition
  }
  const { graph } = FlowGraph
  graph.drawBackground(options)
}
export function fontColorOpt (attr) {
  let color = attr.color
  const { graph } = FlowGraph
  graph.getNodes().forEach(node => {
    node.attr({label: {  fill:color, fontSize:14 }})
    // node.attr('label/fill',attr.color)
  })
}