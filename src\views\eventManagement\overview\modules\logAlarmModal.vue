<template>
  <!-- 历史告警 -->
  <j-modal :title="title" :width="width" :centered="true" :visible="visible" :destroyOnClose="true" switchFullscreen
    cancelText="关闭" @cancel="handleCancel" :okButtonProps="{hidden:true}">
    <template slot="footer">
      <a-button @click="handleCancel">关闭</a-button>
    </template>
    <a-table ref="table" rowKey="id" bordered :columns="columns" :dataSource="dataSource" :pagination="ipagination"
      :loading="loading" @change="handleTableChange">
      <template slot="alarmLevel" slot-scope="text,record">
        <div :style="{backgroundColor:getAlarmColor(record)}"
          style="display:inline-block;color:#ffffff; border-radius: 10px; padding: 2px 10px;">
          {{ getAlarmTitle(record) }}</div>
      </template>
      <template slot="tooltip" slot-scope="text">
        <a-tooltip placement="topLeft" :title="text" trigger="hover">
          <div class="tooltip">{{ text }}</div>
        </a-tooltip>
      </template>
    </a-table>
  </j-modal>
</template>
<script>
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import {
    dataAndFunction
  } from '@views/alarmManage/modules/dataAndFunction'
  export default {
    mixins: [JeecgListMixin, dataAndFunction],
    data() {
      return {
        title: '历史告警',
        width: '1000px',
        visible: false,
        url: {
          list: '/log/overview/getAlarmList'
        },
        columns: [{
            title: '告警名称',
            dataIndex: 'templateName',
            customCell: () => {
              let cellStyle = 'text-align: left'
              return {
                style: cellStyle
              }
            },
            scopedSlots: {
              customRender: 'tooltip'
            }
          },
          {
            title: '设备名称',
            dataIndex: 'deviceName',
            customCell: () => {
              let cellStyle = 'text-align: left'
              return {
                style: cellStyle
              }
            },
            scopedSlots: {
              customRender: 'tooltip'
            }
          },
          {
            title: '告警级别',
            dataIndex: 'alarmLevel',
            scopedSlots: {
              customRender: 'alarmLevel'
            }
          },
          {
            title: '告警时间',
            dataIndex: 'alarmTime1',
            customCell: () => {
              let cellStyle = 'text-align: center'
              return {
                style: cellStyle
              }
            }
          }
        ],
        disableMixinCreated: true,
        queryParam: {}
      }
    },
    props: {
      timeData: {
        type: Object,
        default: () => {}
      }
    },
    watch: {
      timeData: {
        deep: true,
        handler(val) {
          if (val.startTime) {
            this.queryParam.alarmTime2_begin = val.startTime
          }
          if (val.endTime) {
            this.queryParam.alarmTime2_end = val.endTime
          }
        }
      }
    },
    methods: {
      open() {
        this.getAlarmLevelData()
        this.loadData(1)
      },
      handleCancel() {
        this.visible = false
      }
    }
  }
</script>
<style scoped lang="less">
  @import '~@assets/less/normalModal.less';
</style>