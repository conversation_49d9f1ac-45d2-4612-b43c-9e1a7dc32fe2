<template>
  <div class='opmg-know-body'>
    <div class='body-info'>
      <!--富文本、模板、附件-->
      <div class='plan-attachment-info'>
        <!--富文本、模板-->
        <div id='printContent' class='plan' v-html='kInfo.plan'></div>
        <!-- 附件-->
        <div class='text-wrapper'>
          <div v-for='(item,index) in kInfo.filesArr'
             class='file-item'
             :class='{"file-item-download":canDownload}'
             :key='"file-item_"+index'
               @click='forceDownload(item)'
          >
            <a-icon type='paper-clip'></a-icon>
            <span class='file-item-name' :title='item.fileName+item.suffixName'>{{item.fileName}}</span>
            <span :title='item.fileName+item.suffixName'>{{item.suffixName}}</span>
            <a-icon
              v-if='item.indexFile'
              :title='item.indexFile==="1"?"该文件已索引":"该文件未索引"'
              :type='item.indexFile==="1"?"check-circle":"minus-circle"'
              :style='{marginLeft:"0.05rem",color:item.indexFile==="1"?"#4bd863":"#D81E06"}'/>
            <div>{{item.err?item.err:''}}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>

export default {
  name: "",
  mixins: [],
  props: {
    kInfo: {
      type: Object,
      required: true
    },
    canDownload: {
      type: Boolean,
      required: false,
      default: false
    }
  },
  components: {},
  data() {
    return {
      basicUrl: window._CONFIG['staticDomainURL']
    }
  },
  watch: {
    kInfo: {
      handler(val) {

      },
      deep: true,
      immediate: true
    }
  },
  mounted() {

  },
  methods: {
    forceDownload(fileInfo) {
      if (this.canDownload) {
        let link = this.basicUrl + "/" + fileInfo.filePath+'?filename='+fileInfo.fileName+fileInfo.suffixName;
        let extnames = ['.png', ".jpg", ".jpeg", '.gif', '.bmp', ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".pdf", ".txt"];
        if (extnames.includes(fileInfo.suffixName)) {
          window.location.href=link
        } else {
          // 兼容非安全域，非安全域下不可使用navigator.clipboard.writeText
          if (navigator.clipboard && window.isSecureContext) {
            //console.log('非安全模式')
            navigator.clipboard.writeText(link).then(() => {
              this.$message.success('链接已复制,请在浏览器中粘贴并打开')
            }).catch((error) => {
              this.$message.warning('复制失败，请到pc端下载')
            })
          }
          else {
            //console.log('安全模式')
            var ele = document.createElement("input");
            ele.value = link
            document.body.appendChild(ele);
            ele.select();
            document.execCommand("copy");
            document.body.removeChild(ele);
            if (document.execCommand("copy")) {
              this.$message.success('链接已复制,请在浏览器中粘贴并打开')
            } else {
              this.$message.warning('复制失败，请到pc端下载')
            }
          }
        }
      } else {
        this.$message.info('知识创建者设置不允许下载附件')
      }
    }
  }
}
</script>

<style scoped lang="less">
.opmg-know-body {
  border-radius: 3px;
  padding:0.3rem;// 24px
  background: #fff;
  //height: calc(100% - 16px - 16px - 200px);
  //min-height: 200px;
  overflow: hidden;

  .body-info {
    //height: 100%;
    padding-right: 2px;
    overflow-y: auto !important;
  }

  .plan-attachment-info {
    .plan {
      background: #F7F7F7;
      font-size:0.175rem;
      padding: 0.2rem;
      border-radius: 2px;
      white-space: normal;
      word-break: break-all;
      overflow-x: auto;

      img{
        margin-right: 0.15rem !important;
      }
    }
    .text-wrapper{
      margin-top: 0.2rem;//16px
      display: flex;
      flex-flow: column nowrap;

      .file-item{
        display: block;
        font-size: 0.3rem;
        margin: 0.1rem 0rem;
      }

      .file-item-download {
        color: #409eff
      }

      .file-item-download:hover {
        cursor: pointer;
        //background: #ecf5ff !important;
      }
    }
  }
}
</style>