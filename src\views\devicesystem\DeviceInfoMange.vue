<template>
  <div style="height:100%">
    <keep-alive exclude='DeviceInfoModal'>
      <component style="height:100%" :is="pageName" :data="data" :is-editing='true' :render-states='renderStates'/>
    </keep-alive>
  </div>
</template>
<script>
  import DeviceInfoList from './DeviceInfoList'
  import DeviceInfoModal from './deviceshow/DeviceInfoModal'
  export default {
    name: "DeviceInfoMange",
    data() {
      return {
        isActive: 0,
        data: {},
        renderStates:{
          showBaseInfo:true,
          showStateInfo:true,
          showDeviceFunction:true,
          showDataAnalysis:true,
          showJournal:true,
          showAlarm:true,
          showDeviceAlarm:true,
          showVisView:false,
          showDevicePorts:true,
          showTopoView:true,
        }
      };
    },
    components: {
      DeviceInfoList,
      DeviceInfoModal
    },
    created() {
      this.pButton1(0);
    },
    watch:{
      "$route":{
        immediate:true,
        deep:true,
        handler(){
          this.isActive = 0
        }
      }
    },
    //使用计算属性
    computed: {
      pageName() {
        switch (this.isActive) {
          case 0:
            return "DeviceInfoList";
          default:
            return "DeviceInfoModal";
        }
      }
    },
    methods: {
      pButton1(index) {
        this.isActive = index;
      },
      pButton2(index, item) {
        this.isActive = index;
        this.data = item
      }
    }
  }
</script>