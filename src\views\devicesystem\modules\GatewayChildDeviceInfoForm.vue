<template>
  <a-row :gutter="10" style="padding: 10px; margin: -10px; height: 100%" class='zhl'>
    <a-col :md="24" :sm="24" style="height: 100%">
      <!-- 查询区域 -->
      <div class="table-page-search-wrapper">
        <a-form layout="inline" @keyup.enter.native="searchQuery">
          <a-row :gutter="24" ref="row">
            <!--  <a-col :span='spanValue'>
              <a-form-item label="设备标识">
                <a-input placeholder="请输入" v-model="queryParam.deviceCode" />
              </a-form-item>
            </a-col> -->
            <a-col :span='spanValue'>
              <a-form-item label="设备名称">
                <a-input placeholder="请输入设备名称" v-model="queryParam.name" :allowClear='true' autocomplete='off'/>
              </a-form-item>
            </a-col>
            <a-col :span='spanValue'>
              <a-form-item label="所属单位">
                <a-input placeholder="请输入所属单位" v-model="queryParam.deptName" :allowClear='true' autocomplete='off'/>
              </a-form-item>
            </a-col>
            <a-col :span='colBtnsSpan()'>
                <span class="table-page-search-submitButtons"
                      :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                <a-button type="primary" class="btn-search btn-search-style" @click="dosearch">查询</a-button>
                <a-button type="default" class="btn-reset btn-reset-style" @click="doreset">重置</a-button>
                <a v-if="isVisible" class='btn-updown-style' @click="doToggleSearch">
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <!-- 查询区域-END -->

      <!-- 操作按钮区域 -->
      <div class="table-operator table-operator-style">
        <a-button @click="unboundingBatch()" class="btn-add" type="primary" >解绑</a-button>
        <!-- <a-dropdown v-if="selectedRowKeys.length > 0">
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item key="1" @click="batchDel">删除</a-menu-item>
            </a-menu>
            <a-button> 批量操作 <a-icon type="down"/></a-button>
          </a-dropdown> -->
      </div>

      <!-- table区域-begin -->
      <div class="div-table-container">
<!--        <div class="table-operator" style="margin-bottom: 8px"></div>-->
        <a-table
          ref="table"
          bordered
          rowKey="id"
          :columns="columns"
          :dataSource="dataSource"
          :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
          :pagination="ipagination"
          :loading="loading"
          :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          @change="handleTableChange"
        >
          <template slot="htmlSlot" slot-scope="text">
            <div v-html="text"></div>
          </template>
          <template slot="imgSlot" slot-scope="text">
            <span v-if="!text" style="font-size: 14px">无图片</span>
            <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width: 80px; font-size: 14px" />
          </template>
          <template slot="status" slot-scope="text">
            <span v-if="text == 1" style="font-size: 14px; color: green">在线</span>
            <span v-if="text == 0" style="font-size: 14px; color: red">离线</span>
            <span v-if="text == 2" style="font-size: 14px; color: red">告警</span>
          </template>
          <span slot="action" slot-scope="text, record">
            <a @click="unbounding(record.id)">解绑</a>
          </span>
          <template slot="tooltip" slot-scope="text">
            <a-tooltip placement="topLeft" :title="text" trigger="hover">
              <div class="tooltip">
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
      </div>
      <!-- </a-card> -->
    </a-col>
  </a-row>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import JSuperQuery from '@/components/jeecg/JSuperQuery.vue'
import { queryAssetsCategoryTreeList } from '@/api/device'
import { httpAction, getAction, deleteAction, postAction } from '@/api/manage'
//引入公共devicetree组件
// import DeviceTree from '@/components/tree/DeviceTree.vue'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'

export default {
  name: 'GatewayChildDeviceInfoForm',
  mixins: [JeecgListMixin, mixinDevice,YqFormSearchLocation],
  components: {
    JSuperQuery,
    getAction,
    postAction,
    //'device-tree': DeviceTree,
  },
  props: ['parentIds'],
  data() {
    return {
      aLimitWidth: [992, 768, 576,480],
      pageId:'wangguanxiangqing',
      //查询条件，此参数名称与JeecgListMixin模块参数一致
      queryParam: {
        //查询参数
        name: '',
        id: '',
      },

      description: '网关设备批量解绑子设备页面',
      firstTitle: '', //存储搜素tree的第一个title
      // 树
      assetsCategoryTree: [],
      treeData: [],
      expandedKeys: [],
      searchValue: '',
      autoExpandParent: true,
      dropTrigger: '',
      selectedKeys: [],
      selectedTitle: '',
      checkedKeys: [],
      checkStrictly: true,
      // iExpandedKeys: [],
      currFlowId: '',
      currFlowName: '',
      /* rightClickSelectedBean: {},*/
      gatewayDeviceId: '',
      // 表头
      columns: [
        {
          title: '设备名称',
          dataIndex: 'name',
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 100px;max-width:400px'
            return { style: cellStyle }
          },
        },
        {
          title: '监控状态',
          dataIndex: 'status',
          scopedSlots: { customRender: 'status' },
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 100px;max-width:400px'
            return { style: cellStyle }
          },
        },
        {
          title: '所属单位',
          dataIndex: 'momgDeptName',
          scopedSlots: { customRender: 'tooltip' },
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 100px;max-width:450px'
            return { style: cellStyle }
          },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 80,
          scopedSlots: { customRender: 'action' },
        },
      ],
      url: {
        list: '/gateway/deviceInfo/querySelfGatewayChild', //查询当前网关设备下辖的网关子设备
        unbund: '/gateway/deviceInfo/Unbundling', //解绑
      },
      statuslist: [
        {
          name: '在线',
          code: '1',
        },
        {
          name: '离线',
          code: '0',
        },
        {
          name: '告警',
          code: '2',
        },
      ],
      categoryId: '', //选取tree的key
    }
  },
  created() {},

  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
  },

  methods: {
    show(id) {
      this.gatewayDeviceId = id
      this.queryParam.gatewayDeviceId = id
      this.loadData()

    },
    //表单查询,点击查询按钮，默认查询第一页
    dosearch() {
      this.loadData(1)
    },
    //表单重置
    doreset() {
      //重置form表单，不重置tree选中节点
      this.queryParam = {}
      this.queryParam.gatewayDeviceId = this.gatewayDeviceId
      this.loadData(1)
    },
    unbounding(id) {
      let params = {
        gatewayId: this.gatewayDeviceId,
        ids: id,
      }
      let that = this
      that.$confirm({
        title: '确认操作',
        okText: '是',
        cancelText: '否',
        content: '是否确定解绑选中的设备?',
        onOk: function () {
          postAction(that.url.unbund, params).then((res) => {
            if (res.success) {
              that.queryParam = {}
              that.queryParam.gatewayDeviceId = that.gatewayDeviceId
              that.loadData()
              that.$message.success(res.message)
            } else {
              that.$message.warning(res.message)
            }
          })
        },
      })
    },
    unboundingBatch() {
      if (this.selectedRowKeys.length <= 0) {
        this.$message.warning('请选择一条记录！')
        return
      } else {
        var ids = ''
        for (var a = 0; a < this.selectedRowKeys.length; a++) {
          ids += this.selectedRowKeys[a] + ','
        }
        let that = this
        that.$confirm({
          title: '确认操作',
          okText: '是',
          cancelText: '否',
          content: '是否确定解绑选中的设备?',
          onOk: function () {
            postAction('/gateway/deviceInfo/Unbundling', { ids: ids, gatewayId: that.gatewayDeviceId })
              .then((res) => {
                if (res.success) {
                  that.$message.success(res.message)
                } else {
                  that.$message.warning(res.message)
                }
              })
              .finally(() => {
                that.loading = false
                that.queryParam = {}
                that.queryParam.gatewayDeviceId = that.gatewayDeviceId
                that.loadData()
              })
          },
        })
      }
    },
  },
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
/*.form-row {
  display: flex;
  margin: 0px 0px !important;
  align-items: center;
  height: 60px;
  background-color: white;
}
.form-row .form-col:nth-child(1){
 padding-left:0px !important;
}
.form-col {
  height: 34px;
}*/
.div-table-container {
  background-color: white;
  margin-top: 10px;
 /* margin-right: -9px;
  height: calc(100% - 150px);*/
}
</style>
