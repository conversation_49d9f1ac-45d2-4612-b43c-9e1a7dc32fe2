<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <!-- 查询区域 -->
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span='spanValue'>
                <a-form-item label="告警名称">
                  <a-input :maxLength='maxLength' placeholder="请输入告警名称" v-model="queryParam.name" :allowClear='true' autocomplete='off' />
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label="产品名称">
                  <a-select v-model="queryParam.productId" placeholder="请选择产品名称"  :show-search='true'
                            :getPopupContainer='(node) => node.parentNode' option-filter-prop='label' :allow-clear='true'>
                    <a-select-option v-for="item in productList" :label='item.proName' :value='item.proId'
                                     :key="item.proId">
                      {{ item.proName }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label="启用状态">
                  <a-select placeholder="请选择启用状态" :getPopupContainer='(node) => node.parentNode' :allowClear='true'
                    v-model="queryParam.isOnline">
                    <a-select-option :key="1" :value="1">已启用</a-select-option>
                    <a-select-option :key="0" :value="0">未启用</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span='colBtnsSpan()'>
                <span class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button class="btn-search btn-search-style" type="primary" @click="searchQuery">查询</a-button>
                  <a-button class="btn-reset btn-reset-style" @click="searchReset">重置</a-button>
                  <a v-if="isVisible" class='btn-updown-style' @click="doToggleSearch">
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>

      <a-card :bordered="false" style="width: 100%; flex: auto">
        <!-- 操作按钮区域 -->
        <div class="table-operator table-operator-style">
          <a-button @click="handleAdd">新增</a-button>
          <a-dropdown v-if="selectedRowKeys.length > 0">
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item key="1" @click="batchDel">删除</a-menu-item>
            </a-menu>
            <a-button>批量操作
              <a-icon type="down" />
            </a-button>
          </a-dropdown>
        </div>

        <!-- table区域-begin -->
        <a-table ref="table" bordered :rowKey="(record)=>{return record.id}" :columns="columns" :dataSource="dataSource"
          :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}" :pagination="ipagination" :loading="loading"
          :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }" @change="handleTableChange">
          <!-- 字符串超长截取省略号显示-->
          <span slot="templateContent" slot-scope="text">
            <j-ellipsis :value="text" :length="25" />
          </span>
          <template slot="isOnline" slot-scope="text">
            <span v-if="text === '1'" style="color: green">已启用</span>
            <span v-else style="color: red">未启用</span>
          </template>
          <span slot="action" slot-scope="text, record" class="caozuo">
            <a @click="handleDetailPage(record)">查看</a>
            <a-divider type="vertical" />
            <a @click="handleEdit(record)">编辑</a>
            <!-- <a @click="handleConfirm(record)">确认</a> -->
            <a-divider type="vertical" />
            <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
              <a>删除</a>
            </a-popconfirm>
            <a-divider type="vertical" />
            <a-popconfirm title="确定禁用吗?" v-if="record.isOnline === '1'" @confirm="() => handleForbidden(record)">
              <a>禁用</a>
            </a-popconfirm>
            <a-popconfirm title="确定启用吗?" v-if="record.isOnline === '0'" @confirm="() => handleForbidden(record)">
              <a>启用</a>
            </a-popconfirm>
          </span>
          <template slot="tooltip" slot-scope="text">
            <a-tooltip placement="topLeft" :title="text" trigger="hover">
              <div class='tooltip'>
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
      </a-card>
      <!-- table区域-end -->

      <!--新增、编辑页面-->
      <detail-modal ref="modalForm" @ok="modalFormOk"></detail-modal>
    </a-col>
  </a-row>
</template>

<script>
  import { JeecgListMixin} from '@/mixins/JeecgListMixin'
  import {
    deleteAction,
    getAction,
    putAction
  } from '@/api/manage'
  import AlarmTemplateDetailModal from '@comp/alarmTemplate/AlarmTemplateDetailModal'
  import {
    YqFormSearchLocation
  } from '@/mixins/YqFormSearchLocation'

  export default {
    name: 'AlarmTemplateList',
    props: {
      tableParams: {
        type: Object,
      },
    },
    mixins: [JeecgListMixin, YqFormSearchLocation],
    components: {
      'detail-modal': AlarmTemplateDetailModal,
    },
    data() {
      return {
        maxLength:50,
        formItemLayout: {
          labelCol: {
            style: 'width:80px',
          },
          wrapperCol: {
            style: 'width:calc(100% - 80px)'
          }
        },
        // 表头
        columns: [{
            title: '告警名称',
            dataIndex: 'name',
          },
          /*{
              title: '通知模板',
              dataIndex: 'noticeTemplateName',
            },*/
          {
            title: '告警描述',
            dataIndex: 'remark',
            scopedSlots: {
              customRender: 'tooltip'
            },
            customCell: () => {
              let cellStyle = 'text-align: left;min-width: 200px;max-width:400px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '产品名称',
            dataIndex: 'proName'
          },
          {
            title: '设备名称',
            dataIndex: 'devName'
          },
          {
            title: '启用状态',
            dataIndex: 'isOnline',
            scopedSlots: {
              customRender: 'isOnline'
            }
          },
          {
            title: '操作',
            align: 'center',
            width: 180,
            fixed: 'right',
            dataIndex: 'action',
            scopedSlots: {
              customRender: 'action'
            },
          },
        ],
        url: {
          list: '/alarm/alarmTemplate/findAlarmTemplateByOther', //分页展示接口
          delete: '/alarm/alarmTemplate/delete', //删除接口
          deleteBatch: '/alarm/alarmTemplate//deleteBatch', //批量删除接口
          // exportXlsUrl: 'sys/message/sysMessageTemplate/exportXls',
          // importExcelUrl: 'sys/message/sysMessageTemplate/importExcel',
          // batchConfirm: '', //批量确认接口
          // edit: '/alarm/alarmTemplate/edit',
          editStatus: '/alarm/alarmTemplate/editStatus', //启用、禁用
          // queryById: '/alarm/alarmTemplate/queryById', // 通过id查询
          queryAllProduct: '/alarm/alarmHistory/queryAllProduct', // 产品名称查询
        },
        productList: [],
      }
    },
    computed: {
      // importExcelUrl: function () {
      //   return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
      // },
    },
    watch:{
      tableParams:{
        handler(val){
          if(val.ipagination){
            this.ipagination=Object.assign({},val.ipagination)
          }
          if(val.queryParam){
            this.queryParam=Object.assign({}, val.queryParam)
          }
          this.loadData()
        },
        deep:true,
        immediate:true,
      }
    },
    created() {
      this.queryAllProduct()
    },
    mounted() {},
    methods: {
      handleDetailPage: function (record) {
        let param={
          data:record,
          ipagination:this.ipagination,
          queryParam:this.queryParam
        }
        this.$parent.pButton2(1,param)
      },
      handleEdit: function (record) {
        this.$refs.modalForm.edit(record)
        this.$refs.modalForm.title = '编辑'
        this.$refs.modalForm.disableSubmit = false
      },
      handleAdd: function () {
        this.$refs.modalForm.add()
        this.$refs.modalForm.title = '新增'
        this.$refs.modalForm.disableSubmit = false
      },
      queryAllProduct() {
        if (!this.url.queryAllProduct) {
          this.$message.error('请设置url.queryAllProduct!')
          return
        }
        getAction(this.url.queryAllProduct).then((res) => {
          this.productList = []
          if (res.success) {
            if (res.result.length > 0) {
              res.result.map((item) => {
                let param = {
                  proId: item.id,
                  proName: item.displayName,
                }
                this.productList.push(param)
              })
            }
          } else {
            this.$message.warning(res.message)
          }
        })
      },

      handleForbidden(record) {
        let isOnline = record.isOnline === '0' ? '1' : '0'
        let alarmTemplate = {
          id: record.id,
          isOnline: isOnline,
        }
        putAction(this.url.editStatus, alarmTemplate).then((res) => {
          if (res.success) {
            this.$message.success(res.message)
            const newData = [...this.dataSource]
            newData.forEach((ele) => {
              if (record.id === ele.id) {
                ele.isOnline = isOnline
              }
            })
            this.dataSource = newData
          } else {
            this.$message.warning(res.message)
          }
        })
      },
      //批量确认
      /*    batchConfirm: function () {
            if (!this.url.batchConfirm) {
              this.$message.error('请设置url.batchConfirm属性!')
              return
            }
            if (this.selectedRowKeys.length <= 0) {
              this.$message.warning('请选择一条记录！')
              return
            } else {
              var ids = ''
              for (var a = 0; a < this.selectedRowKeys.length; a++) {
                ids += this.selectedRowKeys[a] + ','
              }
              var that = this
              this.$confirm({
                title: '确认操作',
                okText: '是',
                cancelText: '否',
                content: '是否确定修改选中数据?',
                onOk: function () {
                  that.loading = true
                  putAction(that.url.batchConfirm, { ids: ids })
                    .then((res) => {
                      if (res.success) {
                        that.$message.success(res.message)
                        that.loadData()
                        that.onClearSelected()
                      } else {
                        that.$message.warning(res.message)
                      }
                    })
                    .finally(() => {
                      that.loading = false
                    })
                },
              })
            }
          },*/
      /*  //确认操作
        handleConfirm(id) {
          if (!this.url.batchConfirm) {
            this.$message.error('请设置url.batchConfirm属性!')
            return
          }
          putAction(this.url.batchConfirm, { ids: id }).then((res) => {
            if (res.success) {
              this.$message.success(res.message)
              this.loadData()
              this.onClearSelected()
            } else {
              this.$message.warning(res.message)
            }
          })
        },   */
    },
  }
</script>
<style lang='less' scoped>
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';
</style>