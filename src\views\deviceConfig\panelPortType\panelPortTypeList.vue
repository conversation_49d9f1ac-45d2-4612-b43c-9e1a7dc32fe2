<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll zxw">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <!-- 查询区域 -->
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="类型名称">
                  <a-input :maxLength='maxLength' placeholder="请输入类型名称" :allowClear="true" autocomplete="off" v-model="queryParam.typeName">
                  </a-input>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="所属分类">
                  <a-select v-model="queryParam.dictValue" :allowClear="true" autocomplete="off" placeholder="请选择所属分类">
                    <a-select-option v-for='item in dictList' :key='item.value' :value='item.value'>
                      {{ item.text }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="colBtnsSpan()">
                <span class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button type="primary" @click="searchQuery" class="btn-search-style">查询</a-button>
                  <a-button @click="searchReset" style="margin-left: 10px" class="btn-reset-style">重置</a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <!-- 查询区域-END -->

      <!-- 操作按钮区域 -->
      <a-card :bordered="false" style="flex: auto">
        <div class="table-operator">
          <a-button @click="handleAdd">新增</a-button>
          <a-dropdown v-if="selectedRowKeys.length > 0">
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item key="1" @click="batchDel">删除 </a-menu-item>
            </a-menu>
            <a-button>
              批量操作
              <a-icon type="down" />
            </a-button>
          </a-dropdown>
        </div>

        <!-- table区域-begin -->
        <div>
          <a-table bordered ref="table" rowKey="id" :columns="columns" :dataSource="dataSource"
            :scroll='dataSource.length>0?{x:"max-content"}:{}' :pagination="ipagination" :loading="loading"
            :rowSelection='{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }'
            :expandedRowKeys="expandedRowKeys" @change="handleTableChange">
            <template slot="htmlSlot" slot-scope="text">
              <div v-html="text"></div>
            </template>
            <template slot="imgSlot" slot-scope="text">
              <span v-if="!text" style="font-size: 14px; font-style: italic">无图片</span>
              <img v-else :src="getImgView(text)" height="25px" alt=""
                style="max-width: 80px; font-size: 14px; font-style: italic" />
            </template>
            <span slot="action" slot-scope="text, record">
              <span>
                <a @click="handleDetailPage(record)">查看</a>
              </span>
              <span>
                <a-divider type="vertical" />
                <a @click="handleEdit(record)">编辑</a>
              </span>
              <span>
                <a-divider type="vertical" />
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </span>
            </span>
            <template slot="tooltip" slot-scope="text">
              <a-tooltip placement="topLeft" :title="text" trigger="hover">
                <div class="tooltip">
                  {{ text }}
                </div>
              </a-tooltip>
            </template>
          </a-table>
        </div>
        <panel-port-type-modal ref="modalForm" @ok="modalFormOk"> </panel-port-type-modal>
      </a-card>
    </a-col>
  </a-row>
</template>

<script>
  import {
    getAction,
    deleteAction
  } from '@/api/manage'
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import panelPortTypeModal from './modules/panelPortTypeModal'
  import {
    ajaxGetDictItems
  } from '@/api/api'
  import {
    filterObj
  } from '@/utils/util'
  import JSuperQuery from '@/components/jeecg/JSuperQuery.vue'
  import {
    YqFormSearchLocation
  } from '@/mixins/YqFormSearchLocation'

  export default {
    name: 'panelPortTypeList',
    mixins: [JeecgListMixin, YqFormSearchLocation],
    components: {
      JSuperQuery,
      panelPortTypeModal,
    },
    data() {
      return {
        maxLength:50,
        dictList: [],
        expandedRowKeys: [],
        formItemLayout: {
          labelCol: {
            style: 'width:80px',
          },
          wrapperCol: {
            style: 'width:calc(100% - 80px)'
          }
        },
        description: '端口类型管理页面',
        // 表头
        columns: [{
            title: '类型名称',
            dataIndex: 'typeName',
          },
          {
            title: '类型标识',
            dataIndex: 'typeCode',
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 100px;max-width:300px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '所属分类',
            dataIndex: 'dictValue_dictText',
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 100px;max-width:200px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '操作',
            dataIndex: 'action',
            fixed: 'right',
            align: 'center',
            width: 147,
            scopedSlots: {
              customRender: 'action'
            },
          },
        ],
        url: {
          list: '/device/panelType/list',
          delete: '/device/panelType/delete',
          deleteBatch: '/device/panelType/deleteBatch',
        },
      }
    },
    created() {
      this.initDictData()
    },
    mounted() {
      // this.initDictData()
    },
    methods: {
      initDictData() {
        //根据字典Code, 初始化字典数组
        ajaxGetDictItems('device_Config_value', null).then((res) => {
          if (res.success) {
            this.dictList = res.result
          }
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';
</style>