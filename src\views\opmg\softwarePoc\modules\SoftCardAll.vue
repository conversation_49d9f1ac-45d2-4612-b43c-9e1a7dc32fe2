<template>
  <div class="box">
    <div class="logo-img">
      <img :src="downloadUrl + '/' + record.softwareLogo"/>
    </div>
    <div class="soft-right">
      <div style="width: calc(100% - 300px);display:flex;">
        <div class="soft-text">
          <div class="soft-name">
            <span>{{ record.softwareName }}</span>
          </div>
          <div class="summary">
            <span>{{ record.softwareBrief }}</span>
          </div>
        </div>
        <div class="tag-list">
          <!-- 版本 -->
          <div v-if="record.softwareLatestVersion" class="tag">{{ record.softwareLatestVersion }}</div>
          <!-- 软件类别 -->
          <div class="tag">{{ typeText }}</div>
          <!-- 操作系统 -->
          <div class="tag" :title="osText">{{ osText }}</div>
          <!-- cpu架构 -->
          <div class="tag" :title="cpuText">{{ cpuText }}</div>
        </div>
      </div>
      <div style="width: 300px;display:flex; justify-content: space-between;align-items:center;">
        <div class="edit-btns">
          <div @click.stop="editHandle">
              <yq-icon type="edit" style="font-size: 15px;" />
          </div>
          <div @click.stop="deleteHandle" style="margin-left: 20px">
            <yq-icon type="delete" style="font-size: 16px;" />
          </div>
        </div>
        <div class="driverDown">升级包：{{ record.softwareUpgradePackageNum || 0 }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import yqIcon from '@comp/tools/SvgIcon'
export default {
  name: 'SoftCardAll',
  components: {
    yqIcon
  },
  props: {
    cpuList: {
      type: Array,
      default: () => [],
    },
    dictOptions: {
      type: Array,
      default: () => [],
    },
    record: {
      type: Object,
      default: '',
    },
    types: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      downloadUrl: window._CONFIG['downloadUrl'],
      cpuTexts: [],
    }
  },
  created() {},
  computed: {
    typeText() {
      return this.types.find((el) => el.value === this.record.softwareType)?.text
    },
    cpuText() {
      if (this.record.softwareCpuType) {
        let tem = []
        let arr = this.record.softwareCpuType.split(',')
        arr.forEach((el) => {
          tem.push(this.cpuList.find((item) => item.value === el)?.text)
        })
        return tem.join()
      }
      return '--'
    },
    osText() {
      if (this.record.softwareOsType) {
        let tem = []
        let arr = this.record.softwareOsType.split(',')
        arr.forEach((el) => {
          tem.push(this.dictOptions.find((item) => item.value === el)?.text)
        })
        return tem.join()
      }
      return '--'
    },
  },
  methods: {
    editHandle() {
      this.$emit('edit', this.record)
    },
    deleteHandle() {
      this.$confirm({
        content: '确定要删除吗?', // 也可以使用 slot
        okText: '确认',
        cancelText: '取消',
        onOk: () => {
          this.$emit('delete', this.record)
        },
      })
    },
  },
}
</script>

<style scoped lang="less">
.box {
  padding-top: 24px;
  width: 100%;
  display: flex;
  align-items: center;
  position: relative;
  cursor: pointer;
  .logo-img {
    display: flex;
    align-items: center;
    justify-content: center;
    img {
      width: 56px;
      height: 56px;
      border-radius: 10px;
    }
  }
  .soft-right {
    width: calc(100% - 56px);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 0;
    padding-left: 15px;
    border-bottom: 1px solid #e8e8e8;
    .soft-text {
      padding-right: 64px;
      .soft-name {
        width: 100%;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.65);
        font-weight: 500;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .summary {
        width: 280px;
        margin-top: 10px;
        font-size: 12px;
        color: rgba(0, 0, 0, 0.45);
        letter-spacing: 0.43px;
        font-weight: 400;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
  .tag-list {
    display: flex;
    align-items: center;
    .tag {
      background: #F5F5F5;
      border: 1px solid #D9D9D9;
      border-radius: 2px;
      font-size: 12px;
      color: rgba(0,0,0,0.65);
      line-height: 22px;
      font-weight: 400;
      padding: 1px 8px;
      margin-right: 42px;
      max-width: 150px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-bottom: 4px;
    }
  }

  .edit-btns {
    width: 100px;
    opacity: 0;
    display: flex;
    justify-content: space-between;
    padding-right: 40px;
  }
  .driverDown {
    width: 100px;
    height: 33px;
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 16px;
    color: #1890ff;
    font-weight: 400;
    white-space: nowrap;
  }
}
.box:hover {
  .edit-btns {
    opacity: 1;
  }
}
</style>
