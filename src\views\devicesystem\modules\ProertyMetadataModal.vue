<template>
<!-- <a-modal transitionName="" maskTransitionName="" :title="title" :maskClosable="false" :destroyOnClose="true" width='60%' placement="right" :closable="true"
    @cancel="close" :visible="visible"  :footer='null'> -->
      <!--    在这里定义具体的方法，@后面的部分为供父级页面调用的方法，双引号内部为本页面方法的具体定义  -->
  <a-drawer :title="title" :maskClosable="true" :destroyOnClose="true" width='auto' placement="right" :closable="true"
            @close="close" :visible="visible" style="overflow: auto;padding-bottom: 53px"  :wrapClassName="isLowBrower?'ant-drawer-no-animation':''">
    <proerty-metadata-form ref="realForm" :productInfo='productInfo' :showFlag="showFlag" @closePop="closePop"
                           @ok="submitCallback" :disabled="disableSubmit"></proerty-metadata-form>
<!--    <div style='max-height: 70vh;overflow-y: auto'>-->
<!--     -->
<!--    </div>-->
  </a-drawer>
<!--  </a-modal>-->
</template>

<!--说明：属性定义的新增编辑方法，均会先进入到此模块下，然后由此模块完成后续的add/edit方法/其他方法-->
<script>
  import ProertyMetadataForm from './ProertyMetadataForm';
  import {isLowBrower} from '@/utils/util';
  export default {
    name: 'ProertyMetadataModal',
    components: {
      ProertyMetadataForm,
    },
    props: {
      productInfo: {}
    },
    data() {
      return {
        title: '',
        width: 800,
        visible: false,
        disableSubmit: false,
        showFlag: true,
        pid: '',
        pCode: '',
        isLowBrower:isLowBrower(),
      }
    },
    methods: {
      add(index, pid, pCode) {
        // 如果是addChildren跳转过来的话，必定会带有pid参数值
        this.pid = pid
        this.pCode = pCode
        this.visible = true
        this.$nextTick(() => {
          // 打开真是的弹出层页面(可能是二级、三级、四级)  同时将pid传递给下一级页面
          this.$refs.realForm.add(index, this.pid, this.pCode)
        })
      },
      edit(record) {
        this.visible = true
        this.$nextTick(() => {
          // 这里的pid穿参数为空，意味着要用当前编辑页面的record中的id值 作为当前编辑页面的子页面的pid   A.id --> a.pid
          // 打开真正的子级页面，此时页面层会跳转到新开的子级页面，同时该子级页面的父页面的数据record传递给该子页面
          this.$refs.realForm.edit(record, null)
        })
      },
      close() {
        this.$emit('close')
        // this.$emit('ok')
        this.visible = false
      },
      closePop() {
        this.visible = false
      },
      handleOk() {
        this.$refs.realForm.submitForm()
      },
      submitCallback(info) {
        this.$emit('ok', info)
        this.visible = false
      },
      handleCancel() {
        this.close()
      },
    },
  }
</script>
<style scoped>
  ::v-deep .ant-drawer-content-wrapper {
    min-width: 400px !important;
    max-width: 650px !important;
  }
</style>