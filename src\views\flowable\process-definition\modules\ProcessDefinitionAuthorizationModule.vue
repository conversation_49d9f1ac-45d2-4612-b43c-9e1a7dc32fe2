<template>
  <j-modal :title="title" :width="800" :visible="visible" :footer='null' :maskClosable='false'
    :confirmLoading='confirmLoading' @cancel="handleCancel" cancelText="关闭">
    <div class="table-operator">
      <a-button type="primary" @click="selectUser()" icon="plus">新增</a-button>
    </div>
    <a-table ref="table" size="small" bordered rowKey="id" :columns="columns" :dataSource="dataSource"
      :pagination="ipagination" @change="handleTableChange">
      <span slot="action" slot-scope="text, record">
        <!-- <a href="javascript:" @click="urge(record)">查看</a>
        <a-divider type="vertical" /> -->
        <a-popconfirm title="确定删除吗?" @confirm="removeUser(record)">
          <a>删除</a>
        </a-popconfirm>
      </span>
    </a-table>
    <SelectUserModuleVue ref="selectUserModule" :processDefinitionId="processDefinitionId" @selectFinished="selectOK">
    </SelectUserModuleVue>
  </j-modal>
</template>
<script>
  import {
    deleteAction,
    getAction
  } from '@/api/manage'
  import SelectUserModuleVue from './SelectUserModule.vue'

  export default {
    name: 'process-definition-authorization-module',
    components: {
      SelectUserModuleVue,
    },
    data() {
      return {
        columns: [{
            title: '用户账号',
            align: 'center',
            dataIndex: 'identityId',
            key: 'identityId',
          },
          {
            title: '用户名称',
            align: 'center',
            dataIndex: 'identityName',
            key: 'identityName',
          },
          {
            title: '操作',
            align: 'center',
            fixed: 'right',
            width: 100,
            key: 'action',
            scopedSlots: {
              customRender: 'action'
            },
          },
        ],
        title: '流程授权',
        visible: false,
        confirmLoading: false,
        /* 分页参数 */
        ipagination: {
          current: 1,
          pageSize: 5,
          pageSizeOptions: ['5', '10', '15'],
          showTotal: (total, range) => {
            return range[0] + '-' + range[1] + ' 共' + total + '条'
          },
          showQuickJumper: true,
          showSizeChanger: true,
          total: 0,
        },
        processDefinitionId: null,
        dataSource: [],
        url: {
          list: '/flowable/processDefinitionIdentityLink/list',
        },
      }
    },
    created() {},
    methods: {
      init(row) {
        this.visible = true
        this.processDefinitionId = row.id
        this.loadData()
      },
      loadData() {
        getAction(this.url.list, {
            processDefinitionId: this.processDefinitionId
          })
          .then((res) => {
            this.dataSource = res.result
          })
          .finally(() => {})
      },
      removeUser(record) {
        deleteAction('/flowable/processDefinitionIdentityLink/delete', {
          processDefinitionId: this.processDefinitionId,
          identityType: '1',
          identityId: record.identityId,
        }).then((res) => {
          if (res.success) {
            this.$message.success('操作成功')
            this.loadData()
          } else {
            this.$message.error(res.message)
          }
        })
      },
      handleTableChange(pagination, filters, sorter) {
        //TODO 筛选
        // if (Object.keys(sorter).length > 0) {
        //   this.isorter.column = sorter.field;
        //   this.isorter.order = 'ascend' === sorter.order ? 'asc' : 'desc';
        // }
        this.ipagination = pagination
        this.loadData()
      },
      selectOK() {
        this.loadData()
      },
      selectUser() {
        this.$refs.selectUserModule.visible = true
      },
      close() {
        this.visible = false
      },
      handleCancel() {
        this.close()
      },
    },
  }
</script>
<style scoped lang='less'>
  @import '~@assets/less/YQNormalModal.less';
</style>