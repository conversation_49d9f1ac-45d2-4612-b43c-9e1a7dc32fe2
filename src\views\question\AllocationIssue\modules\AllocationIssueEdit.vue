<template>
  <a-modal :title="title"
           :width="modalWidth"
           :visible="visible"
           :confirmLoading="confirmLoading"
           @cancel="handleCancel"
           @ok="handleOk"
           okText=""
           cancelText="关闭"
           wrapClassName="ant-modal-cust-warp"
           style="height:80%;overflow:hidden">
    <div style="text-align:center;font-size:18px;margin-bottom: 10px;">工单分配</div>
    <a-form :form="form">
      <a-row>
        <a-col style="margin-top:20px;height: 199px;">
          <a-form-item :labelCol="labelCol"
                       :wrapperCol="wrapperCol"
                       label="问题内容">
            <textarea readonly
                      name=""
                      placeholder=""
                      style="width:80%;resize:none;border:1px solid #d9d9d9;height:200px"></textarea>
          </a-form-item>
        </a-col>
        <a-divider />
        <a-col>
          <a-form-item :labelCol="labelCol"
                       :wrapperCol="wrapperCol"
                       label="处理人">
            <a-select defaultValue=""
                      placeholder="请选择"
                      @change="handleChange"
                      :allowClear="true"
                      style="width: 80%;">
              <a-select-option value="1">1</a-select-option>
              <a-select-option value="2">2</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col>
          <a-form-item :labelCol="labelCol"
                       :wrapperCol="wrapperCol"
                       label="备注">
            <textarea name=""
                      placeholder=""
                      style="width:80%;resize:none;border:1px solid #d9d9d9"></textarea>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>
<script>
export default {
  name: 'AllocationIssueEdit',
  data() {
    return {
      title: '操作',
      visible: false,
      confirmLoading: false,
      /* 弹框宽 */
      modalWidth: '50%',
      form: this.$form.createForm(this),
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      }
    }
  },
  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      this.model = Object.assign({}, record)
      this.visible = true
      //编辑页面禁止修改角色编码
      if (this.model.id) {
        this.roleDisabled = true
      } else {
        this.roleDisabled = false
      }
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let formData = Object.assign(this.model, values)
          // let ids = formData.id
          // let remarkss =  formData.remarks
        }
      })
    },
    handleCancel() {
      this.close()
    }
  }
}
</script>
<style scoped></style>
