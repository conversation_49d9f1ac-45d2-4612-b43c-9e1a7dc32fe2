<template>
  <div style="height: 100%" class='zhl zhll'>
    <keep-alive>
      <component :is="pageName" :data="data"/>
    </keep-alive>
  </div>
</template>

<script>
import spplicationSystemMonitoringList from './spplicationSystemMonitoringList'
import spplicationSystemMonitoringaModal from './spplicationSystemMonitoringModal'
export default {
  name: "spplicationSystemMonitoring",
  data() {
    return {
      isActive: 0,
      data:{}
    };
  },
  components: {
    spplicationSystemMonitoringList,
    spplicationSystemMonitoringaModal
  },
  created(){
    this.pButton1(0);
  },
  //使用计算属性
  computed: {
    pageName() {
      switch (this.isActive) {
        case 0:
          return "spplicationSystemMonitoringList";
          break;

        default:
          return "spplicationSystemMonitoringaModal";
          break;
      }
    }
  },
  methods: {
    pButton1(index,item) {
      this.isActive = index;
      this.data = item;
    },
    pButton2(index) {
      this.isActive = index;
    }
  }
}
</script>