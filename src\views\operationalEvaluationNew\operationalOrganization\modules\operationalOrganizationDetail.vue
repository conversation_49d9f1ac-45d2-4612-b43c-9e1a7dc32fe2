<template>
  <a-spin :spinning="spinLoading" wrapperClassName="custom-ant-spin">
    <a-row :gutter="10" style="height: 100%" class="vScroll" v-if="originalData&&originalData.length>0">
      <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
        <a-card :bordered="false" style="width: 100%; flex: auto" class="card-style">
          <!--        <img src="~@/assets/return1.png" alt="" @click="getGo" class="return-img" />-->
          <task-card
            v-if="taskInfo"
            :task-info="taskInfo"
            :nodeInfo="taskInfo"
            :catEvaluateResult="null"
            @completeEditing="reload"
            @getGo="getGo">
          </task-card>
        </a-card>
        <a-card :bordered="false" style="width: 100%; flex: auto">
          <a-table
            ref="table"
            bordered
            rowKey="id"
            :columns="columns"
            :dataSource="dataSource"
            :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
            :pagination="false"
            :loading="loading"
            @change="handleTableChange"
          >
<!--         <span slot="category" slot-scope="text, record">
           <a-icon v-if='record.parentInfo&&record.parentInfo.isFocus&&record.parentInfo.isFocus==1' type="star" style="font-size: 10px; color: #f50; margin-right: 4px;" />
            {{text}}
          </span>-->
          <a style="color: #409eff" slot="isFocus" slot-scope="text, record" @click="handleDetailPage(record)">
             <a-icon v-if='record.info&&record.info.isFocus&&record.info.isFocus==1' type="star" style="font-size: 10px; color: #f50; margin-right: 4px;" />
             {{text}}
          </a>

          <span class="caozuo" slot="action" slot-scope="text, record">
            <a @click="handleDetailPage(record)">查看</a>
          </span>
          <template slot="tooltip" slot-scope="text">
            <a-tooltip placement="topLeft" :title="text" trigger="hover">
              <div class="tooltip"> {{ text }}</div>
            </a-tooltip>
          </template>
          </a-table>
        </a-card>
      </a-col>
    </a-row>
    <a-list :data-source="[]" v-else />
  </a-spin>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { getAction, putAction } from '@/api/manage'
import { getStatusConfig } from '@/views/operationalEvaluationNew/modules/statusConfig'
import taskCard from '@views/operationalEvaluationNew/operationalOrganization/modules/taskCard.vue'

export default {
  name: 'operationalOrganizationDetail',
  components: { taskCard },
  mixins: [JeecgListMixin],
  data() {
    return {
      maxLength: 50,
      spinLoading:false,
      categoryRowCounts: {}, // 新增：存储每个分类的行数
      // 表头
      columns: [
        {
          title: '序号',
          dataIndex: 'index',
          width: 50,
          customRender: (text, record, index) => {
            const isFirstRow = this.dataSource.findIndex((item) => item.parentId === record.parentId) === index
            if (isFirstRow) {
              const categoryIndex = this.originalData.findIndex((item) => item.id === record.parentId) + 1
              return {
                children: categoryIndex,
                attrs: {
                  rowSpan: this.categoryRowCounts[record.parentId],
                },
              }
            }
            return {
              children: '',
              attrs: {
                rowSpan: 0,
              },
            }
          },
        },
        {
          title: '类别',
          dataIndex: 'parentTitle',
          key: 'category',
         /* scopedSlots: {
            customRender: 'category',
          },*/
          customRender: (text, record, index) => {
            // 如果是该分类的第一行，显示分类名称并设置rowSpan
            const isFirstRow = this.dataSource.findIndex((item) => item.parentId === record.parentId) === index
            return {
              //children: record.parentTitle, // 使用record中的parentTitle
              children: (
                <a
                  style="cursor: pointer; color: #409eff;"
                  onClick={() => this.handleCategoryClick(record)}>
                  {record.parentInfo && record.parentInfo.isFocus === 1 &&
                   (<a-icon type="star" style="font-size: 10px; color: #f50; margin-right: 4px" />)}
                  {record.parentTitle}
               </a>
              ), // 使用record中的parentTitle
              attrs: {
                rowSpan: isFirstRow ? this.categoryRowCounts[record.parentId] : 0, // 使用this.categoryRowCounts
              }
            }
          }
        },
        {
          title: '评估结果',
          dataIndex: 'parentResult',
          key: 'parentResult',
          customRender: (text, record, index) => {
            const isFirstRow = this.dataSource.findIndex(item => item.parentId === record.parentId) === index;
            return {
              children: record.parentResult, // 使用record中的parentResult
              attrs: {
                rowSpan: isFirstRow ? this.categoryRowCounts[record.parentId] : 0, // 使用this.categoryRowCounts
              },
            }
          }
        },
        {
          title: '指标名称',
          dataIndex: ['info', 'metricsName'],
          key: 'metricsName',
          scopedSlots: {
            customRender: 'isFocus',
          }
        },
        {
          title: '评估结果',
          dataIndex: 'result',
          key: 'metricsResult',
          customRender: (value) => {
            return value || '-'
          }
        },
        {
          title: '备注',
          dataIndex: ['info', 'metricsDesc'],
          key: 'metricsDesc',
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 150px;max-width:400px'
            return {
              style: cellStyle,
            }
          },
          scopedSlots: {
            customRender: 'tooltip',
          },
        },
        {
          title: '操作',
          dataIndex: 'action',
          fixed: 'right',
          width: 100,
          scopedSlots: { customRender: 'action' },
        },
      ],
      url: {
        getMetricsTree: '/evaluate/metricsType/treeNew',
        dynamicGetEvaluateResult: '/devops/projectInfo/dynamicGetEvaluateResult'
      },
      disableMixinCreated: true,
      taskInfo: {}, // 单独存储任务信息
      originalData: [], // 新增：保存原始嵌套结构数据
    }
  },
  props: {
    data: {
      type: Object,
      required: false,
      default: () => {
        return {}
      },
    },
  },
  watch: {
    data: {
      handler(val) {
        this.data = val
      },
     /* deep: true,
      immediate: true,*/
    }
  },
  activated() {
    this.getMetricsTree()
  },
  methods: {
    statusConfig(status) {
      return getStatusConfig(status)
    },
    handleCategoryClick(record) {
      let nodeInfo= {
        id: record.parentId,
        title: record.parentTitle,
        info: record.parentInfo,
        result: record.parentResult || '-',
        type: record.parentType || '-'
      }
      this.$parent.pButton3(2,{taskInfo:this.taskInfo,nodeInfo:nodeInfo})
    },

    handleDetailPage(record) {
      this.$parent.pButton3(2,{taskInfo:this.taskInfo,nodeInfo:record})
    },

    // 处理数据扁平化并计算分类行数
    processData(data) {
      this.taskInfo ={}
      if (data&&data.length>0){
        // 1. 扁平化数据
        // 第一个元素是taskInfo
        this.taskInfo=JSON.parse(JSON.stringify(data[0]))
        this.taskInfo['previewStatus']=this.data.previewStatus||this.data.status

        // 从第二个元素开始是分类数据
        const categories = data.slice(1);
        this.originalData = categories;

        // 扁平化分类数据
        const flattenData = [].concat(...categories.map(parent => {
            return parent.children.map(child => ({
              ...child,
              parentId: parent.id,
              parentTitle: parent.title,
              parentInfo: parent.info,
              parentResult: parent.result || '-',
              parentType: parent.type || '-'
            }))
          }))

        // 计算每个分类的行数
        const rowCounts = categories.reduce((acc, curr) => {
          acc[curr.id] = curr.children.length;
          return acc;
        }, {});
        return { flattenData, rowCounts }
      }
      return { flattenData:[],rowCounts:{} }
    },
    getMetricsTree() {
      this.spinLoading=true
      getAction(this.url.getMetricsTree, { projectId: this.data.id }).then((res) => {
        if (res.success&&res.result ) {
          // 处理数据
          this.originalData = res.result // 保存原始数据
          const { flattenData, rowCounts } = this.processData(res.result)
          this.dataSource = flattenData
          this.categoryRowCounts = rowCounts
        }else if (!res.success){
          this.$message.warning(res.message)
        }
        this.spinLoading=false
      }).catch((err)=>{
        this.$message.warning(err.message)
        this.spinLoading=false
      })
    },
    async reload(){
      this.getMetricsTree()
    },
    getGo() {
      this.$parent.pButton2(0)
    },
  },
}
</script>

<style lang="less" scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
.return-img {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 20px;
  height: 20px;
  cursor: pointer;
  float: right;
}
</style>
