<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll zxw">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <!-- 查询区域 -->
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class="card-style">
        <div class="table-page-search-wrapper">
          <!-- 搜索区域 -->
          <a-form layout="inline" v-bind="formItemLayout">
            <!-- 上三选择 -->
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="类型">
                  <j-dict-select-tag v-model="queryParam.eventType" placeholder="请选择" dictCode="release_type" />
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="优先级">
                  <a-select
                    placeholder="请选择"
                    :getPopupContainer="(node) => node.parentNode"
                    allowClear
                    v-model="queryParam.priorityInt"
                  >
                    <a-select-option value="0">低</a-select-option>
                    <a-select-option value="1">中</a-select-option>
                    <a-select-option value="2">高</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="状态">
                  <a-select
                    placeholder="请选择"
                    :getPopupContainer="(node) => node.parentNode"
                    allowClear
                    v-model="queryParam.status"
                  >
                    <a-select-option value="0">新建</a-select-option>
                    <a-select-option value="1">审批</a-select-option>
                    <a-select-option value="2">处理</a-select-option>
                    <a-select-option value="7">测试</a-select-option>
                    <a-select-option value="6">实施</a-select-option>
                    <a-select-option value="3">审核</a-select-option>
                    <a-select-option value="4">关闭</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue" v-show="toggleSearchStatus">
                <a-form-item label="标题">
                  <a-input
                    placeholder="请输入"
                    :allowClear="true"
                    autocomplete="off"
                    v-model="queryParam.title"
                  ></a-input>
                </a-form-item>
              </a-col>
              <!-- 按钮区域 -->
              <a-col :span="colBtnsSpan()">
                <span
                  class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                >
                  <a-button type="primary" @click="searchQuery" class="btn-search-style">查询</a-button>
                  <a-button @click="searchReset" class="btn-reset-style">重置</a-button>
                  <a @click="doToggleSearch" class="btn-updown-style">
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered="false" class="core" style="flex: auto">
        <!-- table区域-begin -->
        <div>
          <a-table
            ref="table"
            bordered
            rowKey="id"
            :columns="columns"
            :dataSource="dataSource"
            :pagination="ipagination"
            :loading="loading"
            :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
            @change="handleTableChange"
          >
            <span slot="status" slot-scope="status, record">
              <div v-if="record.status == 0">新建</div>
              <div v-if="record.status == 1">审批</div>
              <div v-if="record.status == 2">处理</div>
              <div v-if="record.status == 3">审核</div>
              <div v-if="record.status == 4">关闭</div>
              <div v-if="record.status == 5">退回</div>
              <div v-if="record.status == 6">实施</div>
            </span>
            <span slot="result" slot-scope="result, record">
              <div v-if="record.result == 0">未提交</div>
              <div v-if="record.result == 1">处理中</div>
              <div v-if="record.result == 2" style="color: #139b33">已通过</div>
              <div v-if="record.result == 3" style="color: #df1a1a">已驳回</div>
            </span>
            <span slot="priority" slot-scope="priority, record">
              <div v-if="record.priority == 0" style="color: #ffb300">低</div>
              <div v-if="record.priority == 1" style="color: #fc7611">中</div>
              <div v-if="record.priority == 2" style="color: #df1a1a">高</div>
            </span>
            <span slot="eventType" slot-scope="eventType, record">
              <div v-if="record.eventType == 'bfb'">包发布</div>
              <div v-if="record.eventType == 'qfb'">全发布</div>
              <div v-if="record.eventType == 'detfb'">德尔塔发布</div>
            </span>
            <span slot="slaResponse" slot-scope="slaResponse, record">
              <div v-if="record.slaResponseType == '0'" style="color: #139b33">
                {{ record.slaResponse }}
              </div>
              <div v-if="record.slaResponseType == '1'" style="color: #df1a1a">
                {{ record.slaResponse }}
              </div>
            </span>

            <span slot="slaAccomplish" slot-scope="slaAccomplish, record">
              <div v-if="record.slaAccomplishType == '0'" style="color: #139b33">
                {{ record.slaAccomplish }}
              </div>
              <div v-if="record.slaAccomplishType == '1'" style="color: #df1a1a">
                {{ record.slaAccomplish }}
              </div>
            </span>
            <!-- 操作 -->
            <span slot="action" slot-scope="text, record">
              <a v-if="record.status != 0" @click="handleDeatails(record)">查看</a>
            </span>
          </a-table>
        </div>
        <!-- <applyAdd ref="applyAdd" @ok="modalFormOk"></applyAdd>
    <applyEdit ref="applyEdit" @ok="modalFormOk"></applyEdit>-->
        <doneDetails ref="doneDetails" @ok="modalFormOk"></doneDetails>
      </a-card>
    </a-col>
  </a-row>
</template>
<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
// import applyAdd from './modules/applyAdd'
// import applyEdit from './modules/applyEdit'
import doneDetails from './modules/doneDetails'
import { deleteAction, getAction, downFile, postAction } from '@/api/manage'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'

export default {
  name: 'doneMange',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components: {
    // applyAdd,
    // applyEdit,
    doneDetails,
  },
  data() {
    return {
      formItemLayout: {
        labelCol: {
          style: 'width:70px',
        },
        wrapperCol: {
          style: 'width:calc(100% - 70px)'
        }
      },
      // 表头
      form: {
        priority: 0,
        assignees: [],
        sendMessage: true,
      },
      modalVisible: false,
      submitLoading: false,
      isGateway: false,
      assigneeList: [],
      columns: [
        {
          title: '编号',
          dataIndex: 'processNumber',
        },
        {
          title: '标题',
          dataIndex: 'title',
        },
        {
          title: '类型',
          dataIndex: 'eventType',
          scopedSlots: { customRender: 'eventType' },
        },
        {
          title: '优先级',
          dataIndex: 'priority',
          scopedSlots: { customRender: 'priority' },
        },
        {
          title: '状态',
          dataIndex: 'status',
          scopedSlots: { customRender: 'status' },
        },
        {
          title: '结果',
          dataIndex: 'result',
          scopedSlots: { customRender: 'result' },
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
        },
        {
          title: 'SLA响应',
          dataIndex: 'slaResponse',
          scopedSlots: { customRender: 'slaResponse' },
        },
        {
          title: 'SLA完成',
          dataIndex: 'slaAccomplish',
          scopedSlots: { customRender: 'slaAccomplish' },
        },
        {
          title: '任务名称',
          dataIndex: 'taskName'
        },
        {
          title: '任务开始时间',
          dataIndex: 'taskStartTime'
        },
        {
          title: '任务结束时间',
          dataIndex: 'taskEndTime'
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 200,
          scopedSlots: { customRender: 'action' },
        },
      ],
      url: {
        list: '/release/doneList',
      },
    }
  },
  methods: {
    handleDeatails: function (record) {
      this.$refs.doneDetails.edit(record)
      this.$refs.doneDetails.title = '发布详情'
      this.$refs.doneDetails.disableSubmit = false
    },
  },
  mounted() {},
}
</script>
<style lang="less" scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
/*表头样式*/
::v-deep .ant-table-thead > tr > th {
  text-align: center;
  white-space: nowrap;
}

/*内容对齐方式、省略显示*/
::v-deep .ant-table-tbody > tr > td {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;

  &:first-child,
  &:nth-child(2),
  &:nth-child(3),
  &:nth-child(4),
  &:nth-child(5),
  &:nth-child(6),
  &:nth-child(7),
  &:nth-child(8),
  &:nth-child(9),
  &:nth-child(10) {
    text-align: center;
  }
}
</style>
