import { Graph, Addon, FunctionExt, Shape } from '@antv/x6'
import './chainShape'

export default class FlowGraph {
    // public static graph: Graph
    // private static stencil: Addon.Stencil

    static init(container) {
        this.graph = new Graph({
            container: document.getElementById(container),
            grid: true,
            panning: {
                enabled: true,
                eventTypes: ['leftMouseDown', 'mouseWheel'],
            },
            mousewheel: {
                enabled: true,
                zoomAtMousePosition: false,
                modifiers: 'ctrl',
                minScale: 0.5,
                maxScale: 3,
            },
            //配置节点的可移动区域
            translating: {
                restrict: true,//节点移动时无法超过画布区域
            },
            // 画布调整, 画布具备滚动、平移、居中、缩放等能力，默认禁用
            scroller: {
                enabled: false,
                pageVisible: true,
                pageBreak: false,//是否显示分页符, 显示时会产生分页白色虚线
                pannable: true//画布平移
            },
            connecting: {
                anchor: 'center',
                connectionPoint: 'anchor',
                router: {
                    name: 'manhattan',
                    args: {
                        padding: 1,
                    },
                },
                connector: {
                    name: 'rounded',
                    args: {
                        radius: 8,
                    },
                },
                allowBlank: false,
                snap: {
                    radius: 20,
                },
                createEdge() {
                    return new Shape.Edge({
                        attrs: {
                            line: {
                                stroke: '#A2B1C3',
                                strokeWidth: 2,
                                targetMarker: {
                                    name: 'block',
                                    width: 12,
                                    height: 8,
                                },
                            },
                        },
                        zIndex: 0,
                    })
                },
                validateConnection({ targetMagnet }) {
                    return !!targetMagnet
                },
            },
            highlighting: {
                magnetAdsorbed: {
                    name: 'stroke',
                    args: {
                        attrs: {
                            fill: '#5F95FF',
                            stroke: '#5F95FF',
                        },
                    },
                },
            },
            resizing: {
                enabled: false,
            },
            selecting: {
                enabled: true,
                rubberband: true,
                modifiers: 'alt',//配合alt按键框选
                showNodeSelectionBox: true,//显示节点的选择框
            },
            snapline: true,
            keyboard: true,
            clipboard: true,
        })
        this.initStencil(this.graph)
        this.initEvent(this.graph, container)

        return this.graph
    }

    static bigInit(container) {
        // 创建画布
        this.graph = new Graph({
            container: document.getElementById(container),
            grid: true,
            // 画布调整
            snapline: true,
            history: true,
            //监听容器大小改变，并自动更新画布大小
            autoResize: true,
            //普通画布(未开启 scroller 模式)通过开启 panning 选项来支持拖拽平移
            panning: {
                enabled: true,
            },
            resizing: {
                enabled: false,
            },
            clipboard: {
                enabled: true
            },
            keyboard: {
                enabled: true
            },
            interacting: false,
            //配置节点的可移动区域
            translating: {
                restrict: true,//节点移动时无法超过画布区域
            },
            // 画布调整
            scroller: {
                enabled: false,
                pageVisible: true,
                pageBreak: false,//是否显示分页符, 显示时会产生分页白色虚线
                pannable: true//画布平移
            },
            mousewheel: {
                enabled: true,
            },
            selecting: {
                enabled: true,
                rubberband: false, // 启用框选
                modifiers: 'alt',//配合alt按键框选
                movable: false,
                showNodeSelectionBox: true,
                showEdgeSelectionBox: true
            },
            connecting: {
                anchor: 'center',
                connectionPoint: 'anchor',
                allowBlank: false,
                highlight: true,
                snap: false,
                // snap: {
                //   radius: 20
                // },
                createEdge() {
                    return new Shape.Edge({
                        source: sourceNode, // 源节点
                        target: targetNode, // 目标节点
                        attrs: {
                            line: {
                                stroke: 'green',
                                strokeWidth: 2,
                                targetMarker: {
                                    name: 'classic',
                                    size: 8
                                }
                            }
                        },
                        router: {
                            name: 'manhattan'
                        },
                    })
                },

                validateConnection({ targetMagnet }) {
                    return !!targetMagnet
                },
            },
            embedding: {
                enabled: true,
                findParent({ node }) {
                    const bbox = node.getBBox()
                    return this.getNodes().filter((node) => {
                        // 只有 data.parent 为 true 的节点才是父节点
                        const data = node.getData()
                        if (data && data.parent) {
                            const targetBBox = node.getBBox()
                            return bbox.isIntersectWithRect(targetBBox)
                        }
                        return false
                    })
                }
            },
            highlighting: {
                magnetAvailable: {
                    name: 'stroke',
                    args: {
                        padding: 4,
                        attrs: {
                            strokeWidth: 4,
                            stroke: 'rgba(223,234,255)'
                        }
                    }
                }
            },
        })
        this.initStencil(this.graph)
        this.initEvent(this.graph, container)

        return this.graph
    }

    static initStencil(graph) {
        this.stencil = new Addon.Stencil({
            title: 'Components',
            target: graph,
            search(cell, keyword) {
                return cell.shape.indexOf(keyword) !== -1
            },
            stencilGraphWidth: 280,
            // search: { rect: true },
            collapsable: true,
            groups: [
                {
                    name: 'basic',
                    title: '基础节点',
                    graphHeight: 180
                },
                {
                    name: 'group',
                    title: '节点组',
                    graphHeight: 100,
                    layoutOptions: {
                        columns: 1,
                        marginX: 60
                    }
                }
            ]
        })
        const stencilContainer = document.querySelector('#flowStencil')
        //stencilContainer.appendChild(this.stencil.container)
    }

    static showPorts(ports, show) {
        for (let i = 0, len = ports.length; i < len; i = i + 1) {
            ports[i].style.visibility = show ? 'visible' : 'hidden'
        }
    }

    static initEvent(graph, containerStr) {
        const container = document.getElementById(containerStr)
        graph.bindKey('backspace', () => {
            const cells = graph.getSelectedCells()
            if (cells.length) {
                graph.removeCells(cells)
            }
        })
        graph.on(
            'node:mouseenter',
            FunctionExt.debounce(() => {
                const ports = container.querySelectorAll(
                    '.x6-port-body'
                )
                this.showPorts(ports, true)
            }),
            500
        )
        graph.on('node:mouseleave', () => {
            const ports = container.querySelectorAll(
                '.x6-port-body'
            )
            this.showPorts(ports, false)
        })
    }

    // 销毁
    static destroy() {
        this.graph.dispose()
    }
}
