<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    switchFullscreen
    :centered='true'
    :destroyOnClose='true'
    @ok="handleOk"
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @cancel="handleCancel"
    cancelText="关闭">
    <device-info-form ref="realForm" @ok="submitCallback" :disabled="disableSubmit"></device-info-form>
  </j-modal>
</template>

<script>

  import DeviceInfoForm from './GatewayDeviceInfoForm'
  export default {
    name: 'GatewayDeviceInfoModal',
    components: {
      DeviceInfoForm
    },
    data () {
      return {
        title:'',
        width:'1000px',
        visible: false,
        disableSubmit: false
      }
    },
    methods: {
      show (id) {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.show(id);
        })
      },
      add () {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.add();
        })
      },
      edit (record) {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.edit(record);
        })
      },
      close () {
        this.$emit('close');
        this.visible = false;
      },
      handleOk () {
        this.$refs.realForm.submitForm();
        this.$emit("refresh");
        this.close();
      },
      submitCallback(){
        this.$emit('ok');
        this.visible = false;
      },
      handleCancel () {
        this.close()
      }
    }
  }
</script>
<style scoped lang='less'>
@import '~@assets/less/normalModal.less';
</style>