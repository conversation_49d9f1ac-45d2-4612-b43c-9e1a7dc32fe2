<template>
  <div style="height: 100%">
    <div class="table-page-search-wrapper" style="padding: 12px">
      <a-form v-if="edit" layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24" class="form-row">
<!--          <a-col class="form-col" :lg="6" :sm="20" v-if="!targetModel">-->
<!--            <a-form-item label="高度（单元）">-->
<!--              <a-input-number :max="100" :min="10" placeholder="请输入" v-model="cabinetlayer" @blur="unitChange" />-->
<!--            </a-form-item>-->
<!--          </a-col>-->
          <a-col class="form-col" :lg="24" :sm="24">
            <a-form-item>
              <div class='handler-box'>
                <div class='hanler-left'>
                  <a-button type="primary" @click="dosave">保存</a-button>
                  <a-button  @click="cancelSave" style="margin-left: 8px;margin-right: 16px;">取消</a-button>
                  <span style="line-height: 1;">机柜名称：{{targetModel.userData.name}}</span>
                  <a-divider type="vertical" />
                  <span style="line-height: 1;">机柜容量：{{targetModel.userData.unit+"（U）"}}</span>
                  <a-divider type="vertical" />
                  <span style="line-height: 1;">机柜规格：{{targetModel.userData.specifications}} mm</span>
                  <a-divider type="vertical" />
                  <span style="line-height: 1;">机柜品牌：{{targetModel.userData.brand}}</span>
                </div>
                <div class='handle-item'></div>
              </div>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <div class="handler-box" v-else>
        <div class='hanler-left'>
          <a-button type="primary" @click="doEdit" style="height: 34px;margin-right: 16px;">编辑</a-button>
          <span style="line-height: 1;">机柜名称：{{targetModel.userData.name}}</span>
          <a-divider type="vertical" />
          <span style="line-height: 1;">机柜容量：{{targetModel.userData.unit+"（U）"}}</span>
          <a-divider type="vertical" />
          <span style="line-height: 1;">机柜规格：{{targetModel.userData.specifications}} mm</span>
          <a-divider type="vertical" />
          <span style="line-height: 1;">机柜品牌：{{targetModel.userData.brand}}</span>
        </div>
        <div class="status-info handle-item">
          <div class="status-item">
            <div class="online"></div>
            <span>在线</span>
          </div>
          <div class="status-item">
            <div class="offline"></div>
            <span>离线</span>
          </div>
          <div class="status-item">
            <div class="alarm"></div>
            <span>告警</span>
          </div>
        </div>
      </div>
    </div>

    <div class="div-table-container fluid container">
      <div class="col-md-3" v-if="edit">
        <div>
          <p class="title">可添加设备</p>
          <a-input
            style="margin: 0 12px 12px 0; width: calc(100% - 12px)"
            placeholder="输入关键字进行筛选"
            @change="deviceChange"
            v-model="deviceText"
          ></a-input>
        </div>
        <div class="device-tree-box">
          <vue-easy-tree ref="veTree" node-key="id" :data="deviceList" :props="replaceFields" height="100%">
            <div style="width: 180px; heidht: 100px" class="custom-tree-node" slot-scope="{ node, data }">
              <div draggable="true" @dragstart="onDragstart($event, data)">
                {{ node.label }}
              </div>
            </div>
          </vue-easy-tree>
        </div>
      </div>
      <div class="rack-home" style="height: 100%; overflow-y: scroll">
        <div ref="rackCon" class="rack-container" style="transform: scale(1); margin-left: 24px">
          <div class="rack-header"></div>
          <div
            class="rack-item"
            v-for="(item, idx) in cabinetList"
            :key="idx"
            :data-layer="cabinetList.length - idx"
            :class="{
              'rack-item-online': stuJudge(idx, 1),
              'rack-item-offline': stuJudge(idx, 0),
              'rack-item-warning': stuJudge(idx, 2),
              'rack-item-active': activeJudge(idx),
              'rack-item-drag': dragJudge(idx),
            }"
            @dragenter="ractDragEnter"
            @dragleave="ractDragLeave"
            @dragover="ractDragOver"
            @drop="ractDragDrop"
          >
            <div class="rack-item-left">{{ cabinetList.length - idx }}</div>
            <div class="rack-item-right">{{ cabinetList.length - idx }}</div>
            <div v-if="labelJudge(cabinetList.length - idx)" class="rack-item-label">
              {{ labelJudge(cabinetList.length - idx).name }}
            </div>
            <!-- <div class="reck-item-inside"></div> -->
          </div>
          <div class="rack-bottom"></div>
          <div
            v-for="device in cabinetDevices"
            :key="device.id"
            class="reck-item-ab"
            :draggable="edit"
            :style="{
              top: `calc(79px / 2 + (${layers - device.layer} * (86px / 2)))`,
              height: insideDevice && insideDevice.id === device.id ? `calc(86px / 2)` : `calc(86px / 2 * ${device.u})`,
              zIndex: dragDevice && dragDevice.id === device.id ? 0 : 10,
              'background-image':
                dragDevice && dragDevice.id === device.id
                  ? `none`
                  : `url(/threeImg/resource/equipment_front-${device.u}U.png)`,
            }"
            @contextmenu.prevent="rightClick($event, device)"
            @dragstart="onDragstart($event, device)"
            @dragend="onDragend($event, device)"
          ></div>
        </div>
      </div>
    </div>
    <e-vue-contextmenu ref="ctxshow" id="contextStyle" class="menu">
      <ul class="right-click-ul">
        <li v-for="(item, idx) in uArr" :key="idx" @click="deviceHandle(idx)">{{ idx ? idx + 'u' : '移除' }}</li>
      </ul>
    </e-vue-contextmenu>
  </div>
</template>
  <script >
import VueEasyTree from '@wchbrad/vue-easy-tree'
// 样式文件，可以根据需要自定义样式或主题
import '@wchbrad/vue-easy-tree/src/assets/index.scss'
import { mixinDevice } from '@/utils/mixin'
import { createPreImg } from '@/utils/util'
import { JeecgListMixin } from '@/mixins/JeecgListMixinNoInit'
import { putAction, getAction, deleteAction } from '@/api/manage'
import draggable from 'vuedraggable'

export default {
  name: 'cabinetEdit',
  mixins: [mixinDevice, JeecgListMixin],
  components: {
    draggable,
    VueEasyTree,
  },
  data() {
    return {
      edit: false,
      roomId: '', //机房id
      deviceList: [],
      defDeviceList: [],
      editable: true,
      isDragging: false,
      delayedDragging: false,
      url: {
        cabinetInfo: '/topo/cabinet/info',
        deviceAddAble: '/topo/cabinet/device-addable',
        saveCabinet: '/topo/cabinet/editCabinetToDevice',
      },
      replaceFields: {
        label: 'name',
        key: 'id',
      },
      deviceList1: [
        {
          id: '1',
        },
        {
          id: '2',
        },
      ],
      layers: 0, //机柜的高度单元
      cabinetList: [], //组装机柜列表，用于展示机柜设备
      cabinetlayer: 0, //机柜的高度单元
      deviceText: '',
      dropTargets: [],
      activeArea: undefined,
      preImg: null,
      cabinetDevices: [],
      dragDevice: null,
      insideDevice: null,
      uArr: new Array(9),
      rightClickData: null,
    }
  },
  props: {
    cabinetId: {
      type: String,
      default: '',
      required: true,
    },
    viewFlag: {
      type: Number,
      default: 2,
    },
    targetModel: {
      type: Object,
      default: () => null,
      required: false,
    },
  },
  computed: {
    dragOptions() {
      return {
        animation: 100,
        group: 'description',
        disabled: !this.editable,
        ghostClass: 'ghost',
        scroll: false,
      }
    },
    ria() {
      return this.cabinetDevices.reduce((pre, cur) => {
        return pre.concat(cur.layerPool)
      }, [])
    },
  },
  watch: {
    isDragging(newValue) {
      if (newValue) {
        this.delayedDragging = true
        return
      }
      this.$nextTick(() => {
        this.delayedDragging = false
      })
    },
  },
  created() {
    this.getCabinetInfo()
  },
  mounted() {
    createPreImg()
  },
  methods: {
    unitChange(e) {
      if (this.cabinetlayer !== this.layers && this.cabinetDevices.length > 0) {
        this.cabinetlayer = this.layers
        this.$message.warning('机架已经添加设备，不能更改机架高度')
      } else {
        this.layers = this.cabinetlayer
        this.cabinetList = new Array(Number(this.layers))
      }
    },
    labelJudge(layer) {
      return this.cabinetDevices.find((el) => el.layer == layer)
    },
    activeJudge(idx) {
      if (this.edit) {
        return (
          this.ria.includes(this.cabinetList.length - idx + '') ||
          this.cabinetList.length - idx == this.dropTargets[this.dropTargets.length - 1]
        )
      } else {
        return false
      }
    },
    // 设备状态判断 2告警 0离线 1在线
    stuJudge(idx, stu) {
      if (!this.edit) {
        // el.layerPool.includes(layer) 是否存储设备的单元
        //el.status === stu 是否对应状态
        let layer = this.cabinetList.length - idx + ''
        // console.log('设备状态判断 === ', this.cabinetDevices, layer)
        let dev
        if (stu === 2) {
          dev = this.cabinetDevices.find((el) => el.layerPool.includes(layer) && el.alarmStatus === 1)
        } else {
          dev = this.cabinetDevices.find((el) => el.layerPool.includes(layer) && el.status === stu)
        }
        if (dev) {
          return true
        } else {
          return false
        }
      } else {
        return false
      }
    },
    dragJudge(idx) {
      return this.edit && this.dragDevice && this.dragDevice.layer == this.cabinetList.length - idx
    },
    ractDragOver(e) {
      e.preventDefault()
    },
    ractDragDrop(e) {
      if (this.dragDevice && e.target.dataset.layer) {
        let tem = Object.assign({}, this.dragDevice)
        tem.layer = e.target.dataset.layer
        if (tem.u === undefined) {
          tem.layerPool = [tem.layer]
          tem.u = 1
          this.cabinetDevices.push(tem)
          this.deviceList = this.deviceList.filter((el) => el.id !== tem.id)
          this.defDeviceList = this.defDeviceList.filter((el) => el.id !== tem.id)
        } else {
          let temPool = this.createLyerPool(tem.layer, tem.u)
          if (this.hasRepeatU(this.ria, temPool, this.dragDevice)) {
            this.$message.warning('机柜单元不可用！')
            this.dragDevice.layerPool = this.createLyerPool(this.dragDevice.layer, this.dragDevice.u)
            let idx = this.cabinetDevices.findIndex((el) => el.id === this.dragDevice.id)
            this.cabinetDevices.splice(idx, 1, this.dragDevice)
            return
          }
          tem.layerPool = temPool
          let idx = this.cabinetDevices.findIndex((el) => el.id === tem.id)
          this.cabinetDevices.splice(idx, 1, tem)
        }
        this.dragDevice = null
        this.insideDevice = null
        this.dropTargets = []
      }
    },
    ractDragEnter(e) {
      if (e.target.dataset.layer) {
        this.dropTargets.push(e.target.dataset.layer)
      }
    },
    ractDragLeave(e) {
      if (e.target.dataset.layer !== undefined) {
        this.dropTargets = this.dropTargets.filter((el) => el !== e.target.dataset.layer)
      }
    },
    onDragstart(event, data) {
      this.hideMenu()
      if (data) {
        if (data.layer && data.u) {
          data.layerPool = [data.layer]
          this.dragDevice = data
          setTimeout(() => {
            this.insideDevice = data
          }, 100)
        } else {
          this.dragDevice = Object.assign({}, data)
        }
        let el = document.getElementById('preImg')
        el.innerHTML = data ? data.name : '没有设备数据'
        event.dataTransfer.setDragImage(el, 0, 0)
      }
    },
    onDragend(event, data) {
      if (data && this.dragDevice && this.dragDevice.id === data.id) {
        let temTimer = setTimeout(() => {
          this.dragDevice = null
          clearTimeout(temTimer)
        }, 100)
      } else {
        this.dragDevice = null
      }
      this.insideDevice = null
      this.dropTargets = []
    },
    hideMenu() {
      this.rightClickData = null
      this.$refs.ctxshow.hideMenu() // 隐藏菜单
    },
    removeDevice() {
      if (this.rightClickData) {
        let idx = this.cabinetDevices.findIndex((el) => el.id === this.rightClickData.id)
        this.cabinetDevices.splice(idx, 1)
        let tem = {
          ip: '',
          name: '',
          id: '',
          status: 0,
        }
        for (let k in tem) {
          tem[k] = this.rightClickData[k]
        }
        this.defDeviceList.push(tem)
        this.deviceChange()
      }
    },
    deviceHandle(u) {
      if (u === 0) {
        this.removeDevice()
      } else if (this.rightClickData && this.rightClickData.u !== u) {
        let temPool = this.createLyerPool(this.rightClickData.layer, u)
        if (this.hasRepeatU(this.ria, temPool, this.rightClickData)) {
          this.$message.warning('机柜单元不可用！')
          this.hideMenu()
          return
        }
        this.rightClickData.u = u
        this.rightClickData.layerPool = temPool
        let idx = this.cabinetDevices.findIndex((el) => el.id === this.rightClickData.id)
        this.cabinetDevices.splice(idx, 1, this.rightClickData)
      }
      this.hideMenu()
    },
    createLyerPool(layer, u) {
      let temPool = []
      for (let i = 0; i < u; i++) {
        temPool.push(layer - i + '')
      }
      return temPool
    },
    hasRepeatU(allHas, newHas, target) {
      let selfPool = target.layerPool
      let repeatArr = newHas.filter((el) => (allHas.includes(el) && !selfPool.includes(el)) || el <= 0)
      return repeatArr.length > 0
    },
    rightClick(e, item) {
      if (!this.edit) return
      //接受div参数
      this.rightClickData = item
      //name字段有值的设备才可右键展示
      if (!!item.name) {
        this.$refs.ctxshow.showMenu(e)
      }
    },
    doEdit() {
      this.edit = true
    },
    dosave() {
      if (this.cabinetlayer !== this.layers && this.cabinetDevices.length > 0) {
        this.$message.warning('机架已经添加设备，不能更改机架高度')
      } else {
        this.putSaveCabinet(this.cabinetDevices)
      }
    },
    cancelSave(){
      this.$confirm(  {
        title:'提示',
        content:'确定取消编辑吗？',
        okText: '确定',
        cancelText: '不取消',
        type: 'warning',
        onOk:()=>{
          this.getCabinetInfo()
          this.edit = false
        }
      })
      
    },
    putSaveCabinet(cabinetDeviceList) {
      let info = {
        cabinetId: this.cabinetId,
        layers: this.cabinetlayer,
        cabinetDeviceList: cabinetDeviceList,
      }
      putAction(this.url.saveCabinet, info).then((res) => {
        if (res.success) {
          this.$message.success(res.message)
          this.layers = this.cabinetlayer
          this.cabinetList = new Array(Number(this.layers))
          this.edit = false
          // this.$emit('editok')
        } else {
          this.$message.error(res.message)
        }
      })
    },
    getCabinetInfo() {
      let that = this
      getAction(that.url.cabinetInfo, { id: that.cabinetId }).then((res) => {
        if (res.success) {
          if (this.targetModel) {
            that.cabinetlayer = that.layers = this.targetModel.userData.unit
          } else {
            that.cabinetlayer = that.layers = res.result.layers
          }
          that.cabinetList = new Array(Number(that.cabinetlayer))

          that.cabinetDevices = res.result.devices.map((el) => {
            if(el.layerPool){
              el.layer = Math.max(...el.layerPool);
              el.u = el.layerPool.length;
            }
            // el.layerPool = that.createLyerPool(el.layer, el.u)
            return el
          })

          this.getDeviceAddAble()
        }
      })
    },
    getDeviceAddAble() {
      getAction(this.url.deviceAddAble, { viewFlag: this.viewFlag }).then((res) => {
        if (res.success) {
          let ct = this.cabinetList.filter((el) => el.name && el.u)
          this.deviceList = res.result
          this.defDeviceList = JSON.parse(JSON.stringify(res.result))
        }
      })
    },
    orderList() {
      this.list = this.list.sort((one, two) => {
        return one.order - two.order
      })
    },
    deviceChange() {
      let temList = this.defDeviceList
      if (this.deviceText) {
        this.deviceList = temList.filter((el) => el.name.includes(this.deviceText))
      } else {
        this.deviceList = this.defDeviceList
      }
    },
  },
}
</script>
<style scoped lang="less">
/*@onlineColor:rgb(6, 224, 10);
@offlineColor:rgb(195, 4, 4);
@alarmColor:rgb(213, 148, 6);*/
@onlineColor: rgb(21, 156, 53);
@offlineColor: rgb(135, 135, 135);
@alarmColor: rgb(255, 136, 31);
.handler-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .hanler-left{
    display: flex;
    align-items: center;
  }
  .handle-item{
    margin-left: 20px;
  }
  .status-info {
    display: flex;
    align-items: center;
    .status-item {
      display: flex;
      align-items: center;
      margin-left: 10px;
      .online {
        height: 20px;
        width: 20px;
        border-radius: 5px;
        background: @onlineColor;
        margin-right: 5px;
      }
      .offline {
        height: 20px;
        width: 20px;
        border-radius: 5px;
        background: @offlineColor;
        margin-right: 5px;
      }
      .alarm {
        height: 20px;
        width: 20px;
        border-radius: 5px;
        background: @alarmColor;
        margin-right: 5px;
      }
    }
  }
}
#previewImg {
  position: absolute;
  right: -100px;
  width: 100px;
  height: 12px;
}
.rack-home {
  .rack-container {
    position: relative;
    .reck-item-ab {
      position: absolute;
      left: calc(49px / 3 * 2);
      width: calc(440px / 3 * 2);
      background-size: 100% 100%;
    }
  }
}

.rack-header {
  width: calc(538px / 3 * 2);
  height: calc(79px / 2);
  background-image: url('/threeImg/ract/deviceheader.png');
  background-size: 100% 100%;
}

.rack-bottom {
  width: calc(538px / 3 * 2);
  height: calc(79px / 2);
  background-image: url('/threeImg/ract/devicebottom.png');
  background-size: 100% 100%;
}

.rack-item {
  width: calc(514px / 3 * 2);
  height: calc(86px / 2);
  margin-left: calc(12.8px / 3 * 2);
  background-image: url('/threeImg/ract/topo_cabinet_null_device.png');
  background-size: 100% 100%;
  position: relative;
  color: #fff;
  // z-index: 5;
  .rack-item-left {
    position: absolute;
    left: 0px;
    top: 0;
    font-size: 12px;
    width: calc(28px / 3 * 2);
    height: calc(86px / 2);
    line-height: calc(86px / 2);
    text-align: center;
    background-color: transparent;
  }

  .rack-item-right {
    position: absolute;
    right: 0px;
    top: 0;
    font-size: 12px;
    width: calc(28px / 3 * 2);
    height: calc(86px / 2);
    line-height: calc(86px / 2);
    text-align: center;
    background-color: transparent;
  }

  .rack-item-label {
    position: absolute;
    left: calc(514px / 3 * 2);
    top: 0;
    font-size: 14px;
    height: calc(86px / 2);
    line-height: calc(86px / 2);
    text-align: left;
    color: #000;
    width: 180px;
    background-color: transparent;
  }
}

.rack-item-active {
  .rack-item-left {
    background-color: #1890ff;
  }
  .rack-item-right {
    background-color: #1890ff;
  }
}
.rack-item-drag {
  .rack-item-left {
    background-color: #1e3674;
  }
  .rack-item-right {
    background-color: #1e3674;
  }
}
.rack-item-online {
  .rack-item-left {
    background-color: @onlineColor;
  }
  .rack-item-right {
    background-color: @onlineColor;
  }
}
.rack-item-offline {
  .rack-item-left {
    background-color: @offlineColor;
  }
  .rack-item-right {
    background-color: @offlineColor;
  }
}
.rack-item-warning {
  .rack-item-left {
    background-color: @alarmColor;
  }
  .rack-item-right {
    background-color: @alarmColor;
  }
}
</style>

<style scoped lang="less">
@import '~@assets/less/common.less';

::v-deep .ant-input-number {
  width: 100%;
}

.form-row {
  display: flex;
  margin: 0px 0px !important;
  align-items: center;
  // height: 60px;
  background-color: white;
}

.form-col {
  height: 34px;
}

.div-table-container {
  padding: 16px;
  background-color: #f3f3f3;
  margin: 0 12px;
  height: calc(100% - 70px);
  overflow: hidden;
}

.right-col {
  height: 100%;
}

.content-div {
  width: 100%;
  height: 100%;
  background-color: #fff;
  border-radius: 3px;
}

.col-md-3 {
  // margin: 20px;
  width: 240px;
  height: calc(100%);
  background: #e8e8e8;
  padding-left: 12px;
  border-radius: 4px;
  float: left;
  // overflow-y: auto;
  .device-tree-box {
    height: calc(100% - 80px);
    overflow-y: scroll;
    background: #fff;
  }
}

.ghost {
  opacity: 0.5;
  background: #c8ebfb;
}

.list-group {
  min-height: 20px;
  padding: 0px;
  margin: 10px 0px 0px;
}

.list-div {
  width: 55%;
  margin: auto;
}

.item-name {
  margin: 0px;
  text-align: end;
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
}

.deviceable {
  width: 100%;
  height: 32px;
  background-image: url('/threeImg/ract/deviceable.png');
  background-repeat: no-repeat;
  background-size: 100%;
}

.item-img {
  width: 100%;
  height: 100%;
}

.list-group-item {
  width: 55%;
  margin: auto;
  cursor: move;
  margin-top: 14px;
}

.list-group-item i {
  cursor: pointer;
}

.title {
  margin: 0px;
  text-align: center;
  height: 34px;
  line-height: 34px;
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
}

li {
  list-style-type: none;
}

.device-picture {
  position: relative;
  float: left;
  width: 300px;
  margin: 0 20px;
}

.index-ul {
  position: absolute;
  left: -32px;
  top: 44px;
  z-index: 10;
  text-align: center;

  li {
    height: 48.02px;
    line-height: 48.02px;
    color: white;
  }
}

.device-status-ul {
  position: absolute;
  left: 239px;
  top: 44px;
  z-index: 10;
  text-align: center;

  li {
    height: 48.02px;
    line-height: 48.02px;
    color: white;
  }
}

.device-header {
  margin: 0px;
}

.headerimg {
  width: 100%;
}

.device-div {
  width: 100%;
  padding: 0 6px 0 7px;
  margin-bottom: 0px;
}

.device-group-item {
  width: 100%;
  height: 48.02px;
  cursor: pointer;
}

.device-group {
  width: 100%;
  min-height: 20px;
  padding: 0px;
  margin: 0px;
}

.bottomimg {
  width: 100%;
}

.device-item {
  width: 100%;
  height: 100%;
  position: relative;
  background-image: url('/threeImg/ract/topo_cabinet_device.png');
  background-repeat: no-repeat;
  background-size: 100%;
}

.item-div {
  width: 100%;
  height: 100%;
  position: relative;
  background-image: url('/threeImg/ract/topo_cabinet_null_device.png');
  background-repeat: no-repeat;
  background-size: 100%;
}

.name-span {
  position: absolute;
  top: 10px;
  left: 302px;
  white-space: nowrap;
}

.index-span {
  position: absolute;
  top: 10px;
  color: white;
}

::v-deep .ctx-menu-container {
  width: 100px;
  min-width: 0px;
  text-align: center;
  .right-click-ul {
    padding: 10px 0 5px;
    margin-bottom: 0px;
    padding-inline-start: 0px;
    li {
      // padding-left: 20px
      margin-bottom: 5px;
      height: 20px;
      font-size: 14px;
      cursor: pointer;
    }

    li:hover {
      background-color: #f3f3f3;
    }
  }
}
</style>