<template>
  <j-modal :title="title" :visible="visible" :destroyOnClose="true" fullscreen @ok="handleOk" @cancel="handleCancel"
    okText="保存" cancelText="关闭">
    <scene-vis-edit v-if="visible" ref="visEdit" @ok="submitCallback" toolbar>
    </scene-vis-edit>
  </j-modal>
</template>

<script>
  import sceneVisEdit from './sceneVisEdit'
  export default {
    name: 'sceneTopoEdit',
    components: {
      sceneVisEdit,
    },
    data() {
      return {
        title: '',
        width: 800,
        record: {},
        visible: false,
        disableSubmit: false,
      }
    },
    methods: {
      edit(data) {
        this.visible = true
        this.$nextTick(() => {
          this.$refs.visEdit.create(data)
        })
      },
      close() {
        this.$emit('close')
        this.visible = false
      },
      handleOk() {
        this.$refs.visEdit.spinning = true
        this.$refs.visEdit.spinTip = "正在保存..."
        let timer = setTimeout(() => {
          clearTimeout(timer)
          timer = null;
          this.$refs.visEdit.save()
        }, 100)
      },
      handleCancel() {
        this.close()
      },
      submitCallback() {
        this.$emit('ok')
        this.visible = false
      },
    },
  }
</script>

<style lang="less" scoped>
</style>