<template>
    <div style='height: 100%'>
     <div v-if='rowList.length>0' class='rows-view' :style='{padding:edit?"16px":"0px"}'>
       <row-block
         v-for='item in rowList'
         :key='item.id'
         :record='item'
         :block-id='blockId'
         :edit='edit'
         :addedComponents='addedComponents'
         @setBlockId='$emit("setBlockId",$event)'
         @setComponent='setComponent'
       ></row-block>
       <div style='height: 16px;'></div>
     </div>
      <a-empty v-else style='margin-top: 30vh'>
        <span slot="description" style='color: rgb(42, 161, 152);'> {{edit?"暂无栅格，请添加":"暂无数据"}} </span>
      </a-empty>

    </div>
</template>
<script>
import RowBlock from '@views/customPages/models/RowBlock.vue'
export default {
  name: 'CustomDesignerView',
  components: {  RowBlock },
  props:{
    rowList:{
      type:Array,
      default:()=>[],
      require:true,
    },
    blockId:{
      type:String,
      default:"",
    },
    edit:{
      type:Boolean,
      default:false,
    }
  },
  data(){
    return{

    }
  },
  created() {
    // console.log("看偶 == ",this.rowList)
  },
  mounted() {
  },
  computed:{
     //当前选择的行
     addedComponents() {
      let components = []
      function getComponents(cols) {
        cols.forEach(col => {
          if (col.componentPath) {
            components.push(col.componentPath)
          }
        })
        return components
      }
      this.rowList.forEach(el => {
        if(el.cols && el.cols.length>0){
          return getComponents(el.cols)
        }else if (el.componentPath) {
          components.push(el.componentPath)
        }
      })
      return components
    },
  },
  methods:{
    setComponent(item,col){
      this.$emit("setComponent",item,col)
    },

  }
}
</script>



<style scoped lang='less'>
.rows-view{
  padding:16px;
  height: 100%;
}

</style>