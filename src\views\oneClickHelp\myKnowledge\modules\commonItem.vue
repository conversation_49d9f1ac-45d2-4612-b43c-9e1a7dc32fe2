<template>
  <div class="rightOne-item-left-one" style="margin-left: 40px;">
    <div class="rightOne-item-top" @click="handleDetailPage(record)">
      <div style="margin: 0 5px 0 0;">
        <img v-if="record.knowledgeType==1" src="~@assets/knowledge/wendang.png"
          style="width:18px;height:18px;background-color:#1890FF;padding:3px;border-radius:2px;"
        />
        <img v-if="record.knowledgeType==0" src="~@assets/knowledge/wendang.png"
          style="width:18px;height:18px;background-color:#40de5a;padding:3px;border-radius:2px;"
        />
      </div>
      <a-tooltip placement='topLeft' :title='text' trigger='hover' overlayClassName='oneClickHelpTooltip'>
        <div class="rightOne-item-top-title">{{ record.title }}</div>
      </a-tooltip>
      <div class="badge">{{ record.topicName }}</div>
    </div>
    <div class="rightOne-item-time">
      <div class="rightOne-item-top-time">创建时间：{{ record.createTime }}</div>
      <div v-if="type == 'share'" class="rightOne-item-top-time">分享时间：{{ record.shareTime }}</div>
      <div v-else class="rightOne-item-top-time">更新时间：{{ record.updateTime }}</div>
    </div>
  </div>
</template>
<script>
export default {
  name:'commonItem',
  props: {
    type: {
      type: String,
      default: ''
    },
    record: {
      type: Object,
      default: () => {},
    }
  },
  methods:{
    handleDetailPage(record) {
      this.$emit('handleDetailPage', record)
    }
  }
}
</script>
<style lang="less" scoped>
@import './myKnowledge.less';
</style>