<template>
  <div class="antv-chart-mini">
    <div class="chart-wrapper" :class='{"custom-chart-wrapper":isBottom}' :style="{ height: 46}" v-if="data.length">
      <v-chart :force-fit="true"  :height="height" :data="data" :scale="scale" y="" :padding="padding">
        <v-tooltip/>
        <v-smooth-area position="x*y"/>
        <!--<v-axis dataKey="time" >
        </v-axis>-->
      </v-chart>
    </div>
  </div>
</template>

<script>
  import moment from 'dayjs'
  const sourceData = []
  const beginDay = new Date().getTime()

  for (let i = 0; i < 10; i++) {
    sourceData.push({
      x: moment(new Date(beginDay + 1000 * 60 * 60 * 24 * i)).format('YYYY-MM-DD HH:mm:ss'),
      y: Math.round(Math.random() * 10)
    })
  }
  const DataSet = require('@antv/data-set');
  export default {
    name: 'MiniArea',
    props: {
      dataSource: {
        type: Array,
        default: () => []
      },
      height: {
        type: Number,
        default: 100
      },
      padding:{
        type: Array,
        default: () => [36, 0,18, 0]
      },
      isBottom:{
        type: Boolean,
        default: false
      },
      // x 轴别名
      x: {
        type: String,
        default: 'x',
      },
      // y 轴别名
      y: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        data: [],
        scale:[
          {
            dataKey: 'x',
            type: 'time',
            // tickCount: 8,
            mask: 'YYYY-MM-DD HH:mm:ss'
          },
          {
            dataKey: 'y',
            alias: '值',
            min: 0,
            // max: 1,
          },
        ],
      }
    },
    mounted(){
     this.updateChartData(this.dataSource);
    },
     watch: {
      dataSource: {
        handler(newVal) {
          this.updateChartData(newVal);
        },
        deep: true
      }
    },
    methods: {
      updateChartData(source) {
        var ds = new DataSet();
        var dv = ds.createView().source(source);
        // console.log("数据转换 === ",dv)
        this.data = source;
      }
    }
  }
</script>

<style lang="less" scoped>
  @import "chart";
  .custom-chart-wrapper{
    top:0px;
    bottom: 0px !important;
  }
</style>