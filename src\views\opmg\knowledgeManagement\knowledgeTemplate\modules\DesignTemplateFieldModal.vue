<template>
  <j-modal
    :title="title"
    :width="width"
    :centered='true'
    :visible="visible"
    :destroyOnClose="true"
    switchFullscreen
    @ok="handleOk"
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }"
    @cancel="handleCancel"
    cancelText="关闭"
  >
    <design-template-field-form ref="realForm" @ok="submitCallback" :disabled="disableSubmit"></design-template-field-form>
  </j-modal>
</template>

<script>
import DesignTemplateFieldForm from './DesignTemplateFieldForm'
export default {
  name: 'DesignTemplateFieldModal',
  components: {
    DesignTemplateFieldForm,
  },
  data() {
    return {
      title: '',
      width: 800,
      visible: false,
      disableSubmit: false,
    }
  },
  methods: {
    add() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.add()
      })
    },
    edit(record) {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.edit(record)
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      this.$refs.realForm.submitForm()
    },
    submitCallback() {
      this.$emit('ok')
      this.visible = false
    },
    handleCancel() {
      this.close()
    },
  },
}
</script>
<style lang="less" scoped>
@import '~@assets/less/normalModal.less';
</style>