<template>
  <div class='zr-national-nodes' ref='zrNationalNodes'>
    <a-select v-model='vDate'
              style='width: 180px'
              placeholder='请选择评估日期'
              :getPopupContainer="(node) => node.parentNode"
              :dropdownClassName='"custom-select-dropdown"'
              :allow-clear='false'
              @change='changeProject'
    >
      <a-select-option v-for="item in valuationDates" :value="item.id" :key="item.id">
        {{ item.projectName }}
      </a-select-option>
    </a-select>
    <div class='national-nodes-content'>
      <slot>
        <div class='nodes-statics scroll-list-item'>
          <div class='item-name'>
            指标名称
          </div>
          <div class='item-status'>等级</div>
        </div>
        <vue-seamless-scroll v-if='srollOption.autoPlay' :data='listData' class='scroll-warp'
                             :class-option='srollOption'>
          <ul class='scroll-list'>
            <li class='scroll-list-item' :class='{"scroll-list-odd":index%2!==0}' v-for='(item, index) in listData'
                :key='index'>
              <div class='item-name' :title='item.name' :style='{color:item.color}'>
                {{ item.metricsName }}
              </div>
              <div class='item-status' :style='{color:item.color}'>{{ item.metricsResult || "--" }}</div>
            </li>
          </ul>
        </vue-seamless-scroll>
        <div v-else class='scroll-warp'>
          <ul class='scroll-list'>
            <li class='scroll-list-item' :class='{"scroll-list-odd":index%2!==0}' v-for='(item, index) in listData'
                :key='index'>
              <div class='item-name' :title='item.name' :style='{color:item.color}'>
                {{ item.metricsName }}
              </div>
              <div class='item-status' :style='{color:item.color}'>{{ item.metricsResult || "--" }}</div>
            </li>
          </ul>
        </div>
      </slot>
    </div>
  </div>
</template>
<script>
import ZrBigscreenTitle from '@views/zrBigscreens/modules/ZrBigscreenTitle.vue'
import vueSeamlessScroll from 'vue-seamless-scroll'
import resizeObserverMixin from '@views/statsCenter/com/resizeObserverMixin'
export default {
  name: 'ZrOpCompare',
  components: { ZrBigscreenTitle, vueSeamlessScroll },
  mixins: [resizeObserverMixin],
  props:{
    valuationDates:{
      type:Array,
      default:()=>{return []}
    },
    initDate:{
      type:String,
      default:''
    },
    indicators:{
      type:Array,
      default:()=>{return []}
    }
  },
  data() {
    return {
      listData: [],
      srollOption: {
        step: 0.5, // 步长
        speed: 100, // 滚动速度
        timer: 3000,// 滚动时间间隔
        autoPlay: false,
        limitMoveNum: 10000,
        singleHeight: 36 ,
      },
      maxNum: 0,
      colors:[
        '#55A7F4', // 正常
        '#F4A655', // 异常
        '#F45555'  // 故障
      ],
      vDate:undefined,
      nodes:[],
    }
  },
  created() {
    this.vDate = this.initDate;
  },
  mounted() {
    this.$nextTick(()=>{
      this.setPlayState()
    })

  },
  watch:{
    indicators:{
      handler(newVal, oldVal){
        this.listData = this.indicators
        if (this.maxNum>0 && this.maxNum < this.listData.length) {
          this.srollOption.limitMoveNum = this.maxNum
          this.srollOption.autoPlay = true
        }else{
          this.srollOption.autoPlay = false
        }
      },
      immediate:true,
      deep:true
    }
  },
  methods: {
    changeProject() {
      this.$emit("changeProject",this.vDate)
    },
    // 屏幕变化回调
    resizeObserverCb() {
      this.setPlayState()
    },
    //设置滚动状态
    setPlayState() {
      if (this.$refs.zrNationalNodes && this.listData.length) {
        let bounded = this.$refs.zrNationalNodes.getBoundingClientRect()
        this.maxNum = Math.floor((bounded.height - 38 - 116) / 36)
        if (this.maxNum>0 && this.maxNum < this.listData.length) {
          this.srollOption.limitMoveNum = this.maxNum
          this.srollOption.autoPlay = true
          return
        }
      }
      this.srollOption.autoPlay = false
      this.srollOption.limitMoveNum = this.listData.length + 1
    }
  }
}
</script>


<style scoped lang='less'>
.zr-national-nodes {
  height: 100%;
  padding: 16px;
  background: linear-gradient(to right, rgba(29, 78, 140, 0.3), rgba(29, 78, 140, 0.0));
  .national-nodes-content {
    height: calc(100% - 38px);
    overflow: hidden;
    padding: 16px 0;
  }
}
.nodes-statics{
  background: rgba(94, 140, 199, 0.5);
  color: rgba(237, 245, 255, 0.95);
}
.scroll-warp {
  height: 100%;
  overflow: hidden;

  .scroll-list {
    width: 100%;
    height: 100%;
    margin: 0px;
    padding: 0px;
  }


}
.scroll-list-item {
  display: flex;
  align-items: center;
  color: rgba(237, 245, 255, 0.95);
  font-size: 14px;
  justify-content: space-between;
  height: 36px;
  .item-name {
    width: 60%;
    overflow: hidden;
    position: relative;
    padding: 0 8px;
    line-height: 1;
    opacity: 0.95;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .item-status {
    width: 40%;
    text-align: center;
    opacity: 0.95;
  }
}

.scroll-list-odd {
  background: rgba(29, 78, 140, 0.25);
}
</style>