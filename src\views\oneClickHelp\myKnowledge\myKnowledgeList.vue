<template>
  <div style="height: 100%" class="myKnowledge">
    <card-frame :title="'我的知识'" :showHeadBgImg="true">
      <div slot="bodySlot" class="my-knowledge-bottom" ref="pageBody">
        <div class="leftList">
          <div v-for="(item, index) in listData" :key="index" :class="item.id === subscript ? 'select' : '_select'">
            <a @click="treeSelect(item.id)">{{ item.name }}</a>
          </div>
        </div>
        <div class="lightXian"></div>
        <div class="rightList" ref="rightBox">
          <div v-if="subscript == 2">
            <my-collection ref='myCollection'
              @getRecord="changePage"></my-collection>
          </div>
          <div v-if="subscript == 3">
            <my-comments ref='myComments'
              @getRecord="changePage"></my-comments>
          </div>
          <div v-if="subscript == 4">
            <my-share ref='myShare'
              @getRecord="changePage"></my-share>
          </div>
        </div>
      </div>
    </card-frame>
  </div>
</template>

<script>
import cardFrame from '@views/oneClickHelp/localDeviceInfo/modules/CardFrame.vue'
import myComments from './myComments'
import myCollection from './myCollection'
import myShare from './myShare'

export default {
  name: 'myKnowledgeList',
  components: {
    cardFrame,
    myComments,
    myCollection,
    myShare
  },
  data() {
    return {
      listData: [
        {
          name: '我的收藏',
          id: 2
        },
        {
          name: '我的评论',
          id: 3
        },
        {
          name: '我的分享',
          id: 4
        }
      ],
      subscript: 2
    }
  },
  created() {

  },
  methods: {
    // 获取父组件的方法,切换组件
    changePage(record) {
      this.$parent.pButton2(1, record)
    },
    //点击左侧状态树
    treeSelect(i) {
      if (this.subscript === i) return
      this.subscript = i
    }
  }
}
</script>
<style scoped lang="less">
@import '~@assets/less/onclickStyle.less';

.myKnowledge {
  height: 100%;
  width: 100%;
}


.my-knowledge-bottom {
  width: 100%;
  height: calc(100% - 50px);
  display: flex;
  // background: #fff;
  @primaryColor: #409eff;

  

  .leftList {
    width: 261px;
    padding: 21px 0px;

    div {
      width: 100%;
      height: 45px;
      line-height: 45px;
      text-align: center;
      font-size: 16px;
      letter-spacing: 1px;

      a {
        color: rgba(0, 0, 0, 0.65);
      }
    }

    .select {
      background: linear-gradient(90deg, #0a1425 0%, rgba(8, 83, 255, 0.32) 51%, #101b2b 100%);
      margin-bottom: 15px;

      a {
        color: white;
      }
    }

    ._select {
      margin-bottom: 15px;

      a {
        color: white;
      }
    }
  }

  .lightXian {
    margin-top: 24px;
    height: calc(100% - 24px);
    width: 1px;
    margin-left: 10px;
    background: linear-gradient(to bottom, #0c1a32 0%, rgb(20, 100, 219, 0.8) 50%, #0b1931 100%);
  }

  .rightList {
    width: calc(100% - 237px);
    height: 100%;
    overflow-y: auto;
    padding: 0 43px 0 41px;

  }
}

.pagination {
  display: flex;
  justify-content: right;
  padding-right: 20px;
  margin-top: 12px;
}

::-webkit-scrollbar {
  display: none;
}
</style>
<style>
.ant-advanced-search-form .ant-form-item {
  display: flex;
}

.ant-advanced-search-form .ant-form-item-control-wrapper {
  flex: 1;
}

#components-form-demo-advanced-search .ant-form {
  max-width: none;
}</style>