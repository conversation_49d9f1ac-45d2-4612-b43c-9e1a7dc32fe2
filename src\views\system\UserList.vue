<template>
  <a-row :gutter='10' style='height: 100%' class='vScroll'>
    <a-col style='width: 100%; height: 100%; display: flex; flex-direction: column'>
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <!-- 查询区域 -->
        <div class='table-page-search-wrapper'>
          <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
            <a-row :gutter='24' ref='row'>
              <a-col :span='spanValue'>
                <a-form-item label='用户账号'>
                  <a-input placeholder='请输入' :maxLength="maxLength" :allowClear='true' autocomplete='off' v-model='queryParam.username'>
                  </a-input>
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label='用户姓名'>
                  <a-input placeholder='请输入用户姓名' :maxLength="maxLength" :allowClear='true' autocomplete='off'
                           v-model='queryParam.realname'>
                  </a-input>
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label='性别'>
                  <a-select v-model='queryParam.sex' :getPopupContainer='(node) => node.parentNode' :allowClear='true'
                            placeholder='请选择性别'>
                    <!--                  <a-select-option value="">请选择</a-select-option>-->
                    <a-select-option value='1'>男性</a-select-option>
                    <a-select-option value='2'>女性</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span='spanValue' v-show='toggleSearchStatus'>
                <a-form-item label='座机号码'>
                  <a-input placeholder='请输入座机号码' :maxLength="15" v-model='queryParam.telephone' :allowClear='true'
                           autocomplete='off'>
                  </a-input>
                </a-form-item>
              </a-col>
              <a-col :span='spanValue' v-show='toggleSearchStatus'>
                <a-form-item label='状态'>
                  <j-dict-select-tag v-model="queryParam.status" placeholder="请选择状态" dictCode="user_status" />
<!--                  <a-select v-model='queryParam.status' :getPopupContainer='(node) => node.parentNode'
                            :allowClear='true' placeholder='请选择状态'>
                    &lt;!&ndash;                  <a-select-option value="">请选择</a-select-option>&ndash;&gt;
                    <a-select-option value='1'>正常</a-select-option>
                    <a-select-option value='2'>冻结</a-select-option>
                  </a-select>-->
                </a-form-item>
              </a-col>
              <a-col :span='colBtnsSpan()'>
                <span class='table-page-search-submitButtons'
                      :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button type='primary' class='btn-search btn-search-style' @click='searchQuery'>查询</a-button>
                  <a-button class='btn-reset btn-reset-style' @click='searchReset' style='margin-left: 8px'>重置
                  </a-button>
                  <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <!-- 操作按钮区域 -->
        <div class='table-operator table-operator-style'>
          <a-button @click='handleAdd' v-has="'user:add'">新增</a-button>
          <a-button @click="handleExportXls('用户信息')">导出</a-button>
          <a-upload name='file' :showUploadList='false' :multiple='false' :headers='tokenHeader'
                    :action='importExcelUrl' @change='handleImportExcel' v-has="'user:import'">
            <a-button>导入</a-button>
          </a-upload>
          <a-button @click='recycleBinVisible = true'>回收站</a-button>
          <a-dropdown v-if='selectedRowKeys.length > 0'>
            <a-menu slot="overlay" style='text-align: center' @click='handleMenuClick'>
              <a-menu-item key='1' v-has="'user:delete'" @click='batchDel'>删除</a-menu-item>
              <a-menu-item key='2' @click="batchFrozen('2')" v-has="'user:frozen'">冻结</a-menu-item>
              <a-menu-item key='3' @click="batchFrozen('1')" v-has="'user:thaw'">解冻</a-menu-item>
            </a-menu>
            <a-button>
              批量操作
              <a-icon type='down' />
            </a-button>
          </a-dropdown>
          <!-- <j-super-query :fieldList="superQueryFieldList" @handleSuperQuery="handleSuperQuery" /> -->
        </div>
        <!-- table区域-begin -->
        <a-table ref='table' bordered rowKey='id' :columns='columns' :dataSource='dataSource'
                 :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}" :pagination='ipagination'
                 :loading='loading'
                 :rowSelection='{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }'
                 @change='handleTableChange'>
          <template slot='avatarslot' slot-scope='text, record'>
            <div class='anty-img-wrap'>
              <a-avatar shape='square' :src='getAvatarView(record.avatar)' icon='user' />
            </div>
          </template>
          <span slot="action" slot-scope="text, record" class="caozuo">
            <a href="javascript:;" @click="handleDetail(record)">查看</a>
            <span v-has="'user:edit'">
              <a-divider type="vertical" />
              <a v-if="record.isThreePowerUser==0&&record.isThreePowers==1" class="dontDelete">编辑</a>
              <a v-else @click="handleEdit(record)">编辑</a>
            </span>
            <a-divider type="vertical" />
            <a-dropdown>
              <a class='ant-dropdown-link' style='color: #409eff'> 更多
                <a-icon type='down' /> </a>
              <a-menu slot='overlay'>
                <a-menu-item> </a-menu-item>
                <a-menu-item v-has="'user:password'">
                  <a v-if="record.isThreePowerUser==0&&record.isThreePowers==1" class="dontDelete" href="javascript:;">密码</a>
                  <a v-else href="javascript:;" @click="handleChangePassword(record.username)">密码</a>
                </a-menu-item>
                <a-menu-item v-has="'user:akey'">
                  <a v-if="record.isThreePowerUser==0&&record.isThreePowers==1" class="dontDelete" href="javascript:;">生成akey</a>
                  <a v-else href="javascript:;" @click="generateAkey(record.id)">生成akey</a>
                </a-menu-item>
                <a-menu-item v-if='record.status == 1&&$yqHasPermission("user:frozen")'>
                  <a-popconfirm :disabled="record.isThreePowerUser==0&&record.isThreePowers==1" title="确定冻结吗?" @confirm="() => handleFrozen(record.id, 2, record.username)">
                    <a v-if="record.isThreePowerUser==0&&record.isThreePowers==1" class="dontDelete">冻结</a>
                    <a v-else>冻结</a>
                  </a-popconfirm>
                </a-menu-item>
                <a-menu-item v-if='record.status == 2&&$yqHasPermission("user:thaw")' >
                  <a-popconfirm :disabled="record.isThreePowerUser==0&&record.isThreePowers==1" title="确定解冻吗?" @confirm="() => handleFrozen(record.id, 1, record.username)">
                    <a v-if="record.isThreePowerUser==0&&record.isThreePowers==1" class="dontDelete">解冻</a>
                    <a v-else>解冻</a>
                  </a-popconfirm>
                </a-menu-item>
                <a-menu-item v-has="'user:delete'">
                  <a-popconfirm :disabled="record.isThreePowerUser==0&&record.isThreePowers==1" title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                    <a v-if="record.isThreePowerUser==0&&record.isThreePowers==1" class="dontDelete">删除</a>
                    <a v-else>删除</a>
                  </a-popconfirm>
                </a-menu-item>
              </a-menu>
            </a-dropdown>
          </span>
          <template slot='tooltip' slot-scope='text'>
            <a-tooltip placement='topLeft' :title='text' trigger='hover'>
              <div class='tooltip'>
                {{ text }}
              </div>
            </a-tooltip>
          </template>
          <template slot='sex' slot-scope='text' v-if='text != null'>
            <span class='tooltip'>
              {{ text == '2' ? '女' : '男' }}
            </span>
          </template>
          <template slot='sex' slot-scope='text'>
            <span class='tooltip' v-if='text != null'>
              {{ text == '2' ? '女' : '男' }}
            </span>
          </template>
        </a-table>
        <!-- table区域-end -->
      </a-card>
      <user-modal ref='modalForm' @ok='modalFormOk'></user-modal>
      <password-modal ref='passwordmodal'></password-modal>
      <!-- 用户回收站 -->
      <user-recycle-bin-modal :visible.sync='recycleBinVisible' @ok='modalFormOk' />
    </a-col>
  </a-row>
</template>

<script>
import UserModal from './modules/UserModal'
import PasswordModal from './modules/PasswordModal'
import {
  putAction,
  getFileAccessHttpUrl
} from '@/api/manage'
import {
  httpAction,
  getAction,
  deleteAction
} from '@/api/manage'
import {
  frozenBatch
} from '@/api/api'
import {
  JeecgListMixin
} from '@/mixins/JeecgListMixin'
import SysUserAgentModal from './modules/SysUserAgentModal'
import JInput from '@/components/jeecg/JInput'
import UserRecycleBinModal from './modules/UserRecycleBinModal'
import JSuperQuery from '@/components/jeecg/JSuperQuery'
import {
  YqFormSearchLocation
} from '@/mixins/YqFormSearchLocation'

export default {
  name: 'UserList1',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components: {
    SysUserAgentModal,
    UserModal,
    PasswordModal,
    JInput,
    UserRecycleBinModal,
    JSuperQuery
  },
  data() {
    return {
      maxLength:50,
      description: '这是用户管理页面',
      formItemLayout: {
        labelCol: {
          style: 'width:90px'
        },
        wrapperCol: {
          style: 'width:calc(100% - 90px)'
        }
      },
      queryParam: {},
      recycleBinVisible: false,
      columns: [
        /*{
            title: '#',
            dataIndex: '',
            key:'rowIndex',
            width:60,
            align:"center",
            customRender:function (t,r,index) {
              return parseInt(index)+1;
            }
          },*/
        {
          title: '用户账号',
          dataIndex: 'username'
          //sorter: true,
        },
        {
          title: '用户姓名',
          dataIndex: 'realname'
        },
        {
          title: '头像',
          dataIndex: 'avatar',
          scopedSlots: {
            customRender: 'avatarslot'
          }
        },
        {
          title: '性别',
          dataIndex: 'sex',
          sorter: (a, b) => a.sex - b.sex,
          scopedSlots: {
            customRender: 'sex'
          }
        },
        {
          title: '生日',
          dataIndex: 'birthday'
        },
        {
          title: '座机号码',
          dataIndex: 'telephone'
        },
        {
          title: '部门',
          dataIndex: 'orgCodeTxt'
        },
        {
          title: '负责部门',
          dataIndex: 'departIds_dictText'
        },
        {
          title: '状态',
          dataIndex: 'status_dictText'
        },
        {
          title: '操作',
          dataIndex: 'action',
          scopedSlots: {
            customRender: 'action'
          },
          fixed: 'right',
          align: 'center',
          width: 170
        }
      ],
      superQueryFieldList: [{
        type: 'input',
        value: 'username',
        text: '用户账号'
      },
        {
          type: 'input',
          value: 'realname',
          text: '用户姓名'
        },
        {
          type: 'select',
          value: 'sex',
          text: '性别',
          dictCode: 'sex'
        }
      ],
      url: {
        akey: '/sys/generateKey',
        syncUser: '/act/process/extActProcess/doSyncUser',
        list: '/sys/user/listByCondition',
        delete: '/sys/user/delete',
        deleteBatch: '/sys/user/deleteBatch',
        exportXlsUrl: '/sys/user/exportXls',
        importExcelUrl: 'sys/user/importExcel'
      }
    }
  },

  created() {},
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
  },
  mounted() {},
  methods: {
    getAvatarView: function (avatar) {
      return getFileAccessHttpUrl(avatar)
    },
    batchFrozen: function (status) {
      if (this.selectedRowKeys.length <= 0) {
        this.$message.warning('请选择一条记录！')
        return false
      } else {
        let ids = ''
        let that = this
        let isAdmin = false
        that.selectionRows.forEach(function (row) {
          if (row.username == 'admin') {
            isAdmin = true
          }
        })
        if (isAdmin) {
          that.$message.warning('管理员账号不允许此操作,请重新选择！')
          return
        }
        that.selectedRowKeys.forEach(function (val) {
          ids += val + ','
        })
        that.$confirm({
          title: '确认操作',
          okText: '是',
          cancelText: '否',
          content: '是否' + (status == 1 ? '解冻' : '冻结') + '选中账号?',
          onOk: function () {
            frozenBatch({
              ids: ids,
              status: status
            }).then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.loadData()
                that.onClearSelected()
              } else {
                that.$message.warning(res.message)
              }
            })
          },
        })
      }
    },
    handleMenuClick(e) {
      /* if (e.key == 1) {
         this.batchDel()
       } else if (e.key == 2) {
         this.batchFrozen(2)
       } else if (e.key == 3) {
         this.batchFrozen(1)
       }*/
    },
    handleFrozen: function (id, status, username) {
      let that = this
      //TODO 后台校验管理员角色
      if ('admin' == username) {
        that.$message.warning('管理员账号不允许此操作！')
        return
      }
      frozenBatch({
        ids: id,
        status: status
      }).then((res) => {
        if (res.success) {
          that.$message.success(res.message)
          that.loadData()
        } else {
          that.$message.warning(res.message)
        }
      })
    },
    handleChangePassword(username) {
      this.$refs.passwordmodal.show(username)
    },
    generateAkey(id) {
      let that = this
      getAction(this.url.akey, {
        userId: id
      }).then((res) => {
        const h = this.$createElement;
        this.$success({
          title: '成功生成akey',
          content: h('div', {}, [
            h('p', res.result),
          ]),
          okText: '复制并关闭',
          onOk:()=>{
            if (navigator.clipboard && window.isSecureContext) {
              navigator.clipboard.writeText(res.result).then(() => {
                this.$message.success('复制成功')
              }).catch((error) => {
                console.error("复制失败，请手动复制");
              })
            } else {
              var ele = document.createElement("input");
              ele.value = res.result
              document.body.appendChild(ele);
              ele.select();
              document.execCommand("copy");
              document.body.removeChild(ele);
              if (document.execCommand("copy")) {
                this.$message.info('复制成功')
              } else {
                console.error("复制失败，请手动复制");
              }
            }
          },
        });
      }).catch((res) => {
        this.$message.error(res.message)
      })
    }
  },
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
.caozuo .dontDelete{
  //color:#d9d9d9 !important;
  color:rgba(0, 0, 0, 0.35) !important;
}
::v-deep .ant-dropdown-menu-item a{
  color:#409eff !important;
}
::v-deep .ant-dropdown-menu-item .dontDelete{
  color:rgba(0, 0, 0, 0.35) !important;
}

/*给table列设置宽度*/
::v-deep .ant-table-scroll .ant-table-thead > tr > th,
::v-deep .ant-table-scroll .ant-table-tbody > tr > td {

  /*用户账号*/
  &:nth-child(2) {
    min-width: 100px;
  }

  /*用户姓名*/
  &:nth-child(3) {
    min-width: 100px;
  }

  /*头像*/
  &:nth-child(4) {
    min-width: 100px;
  }

  /*性别*/
  &:nth-child(5) {
    min-width: 60px;
  }

  /*生日*/
  &:nth-child(6) {
    min-width: 100px;
  }

  /*手机号码*/
  &:nth-child(7) {
    min-width: 100px;
  }

  /*部门*/
  &:nth-child(8) {
    min-width: 200px;
  }

  /*负责部门*/
  &:nth-child(9) {
    min-width: 200px;
  }

  /*状态*/
  &:nth-child(10) {
    min-width: 60px;
  }
}

/*表头样式*/
::v-deep .ant-table-thead > tr > th {
  text-align: center;
  white-space: nowrap;
}

/*内容对齐方式、省略显示*/
::v-deep .ant-table-tbody > tr > td {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;

  &:nth-child(-n + 5),
  &:nth-child(10) {
    text-align: center;
  }

  &:nth-child(6),
  &:nth-child(7) {
    text-align: right;
  }

  &:nth-child(8),
  &:nth-child(9) {
    text-align: left;
  }
}
</style>