<template>
  <div :id="containerId" style="position: relative">
    <a-upload
      v-if='isShow'
      :accept='accept'
      name="file"
      :multiple="multiple"
      :action="uploadAction"
      :headers="headers"
      :data="{'biz':bizPath}"
      :fileList="fileList"
      :beforeUpload="doBeforeUpload"
      @change="handleChange"
      :disabled="disabled"
      :returnUrl="returnUrl"
      :class="{'uploadty-disabled':disabled}"
      :showUploadList="showUploadList">
      <template>
        <div v-if="isImageComp">
          <a-icon type="plus"/>
          <div class="ant-upload-text">{{ text }}</div>
        </div>
        <a-button v-else-if="buttonVisible">
          <a-icon v-if='isShowUploadIcon' type="upload"/>
          {{ text }}
        </a-button>
      </template>
    </a-upload>

<!--    <div v-if="fileListArray" class='appendix' >
        <a-row class='row' :gutter='[4,4]'>
          <a-col class='col'  :xxl='6' :xl='6' :lg='6' :md='8' :sm='12' :xs='12' v-for='(item,index) in fileListArray' :key='index' >
            <template>
              <a-tooltip placement="top" :title="item.name" trigger="hover">
                <div class='bg-wrapper'>
                  <div class='bg img' v-if='item.type=="image"' :style="{background:'url('+item.imgSrc+') center center'}"></div>
                  &lt;!&ndash;                  <img class='bg img' v-if='item.fileType=="image"' :src='item.imgSrc'></img>&ndash;&gt;
                  <div class='bg' v-else-if='item.type=="file"'><yq-icon type='knowledge_file' class='icon'/></div>
                  <div class='bg' v-else-if='item.type=="media"'><yq-icon type='knowledge_media' class='icon'/></div>
                  <div class='bg' v-else><yq-icon type='knowledge_unknown_file' class='icon'/></div>
                  <div class='action-wrapper'>
                    <div class='action'>
                      <a-icon v-if='item.type!="otherType"&&lookShow' type='eye'  class='action-icon' style='margin-right: 10px' @click='view(item)'/>
                      <a-icon  type='download' class='action-icon' @click='download(item)' style='margin-right: 10px'/>
                      <a-icon v-if='isDelete' type='delete' class='action-icon' @click='handleDelete(item)'/>
                    </div>
                  </div>
                </div>
              </a-tooltip>
            </template>
          </a-col>
        </a-row>
      </div>-->
  </div>
</template>

<script>
function getBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = error => reject(error);
  });
}
import Vue from 'vue'
import { ACCESS_TOKEN } from '@/store/mutation-types'
import { getFileAccessHttpUrl } from '@api/manage'
import { Base64 } from 'js-base64'
// import YqIcon from '@views/insight/SvgIcon'

const FILE_TYPE_ALL = 'all'
const FILE_TYPE_IMG = 'image'
const FILE_TYPE_TXT = 'file'
const uidGenerator = () => {
  return '-' + parseInt(Math.random() * 10000 + 1, 10)
}
const getFileName = (path) => {
  if (path.lastIndexOf('\\') >= 0) {
    let reg = new RegExp('\\\\', 'g')
    path = path.replace(reg,"/");
  }
  return path.substring(path.lastIndexOf("/")+1);
}
const getFileAccessHttpUrl_1=(avatar,subStr)=> {
  if (!subStr) subStr = 'http'
  try {
    if (avatar && avatar.startsWith(subStr)) {
      return avatar;
    } else {
      if (avatar && avatar.length > 0 && avatar.indexOf('[') == -1) {
        //return window._CONFIG['domianURL'] + "/" + avatar;
        return window._CONFIG['staticDomainURL'] + "/" + avatar;
      }
    }
  } catch (err) {
    return;
  }
}
export default {
  name: 'WordUpload',
  // components:{YqIcon},
  data(){
    return {
      headers:{},
      fileList: [],
      newFileList: [],
      uploadGoOn:true,

      containerId:'',
      fileListArray:[],
      lookShow:window._CONFIG.VUE_APP_PREVIEW_SHOW
    }
  },
  props:{
    isShowUploadIcon:{
      type:Boolean,
      required:false,
      default:true
    },
    uploadAction:{
      type:String,
      required:false,
      default:window._CONFIG['domianURL']+"/sys/common/upload"
    },
    isShow:{
      type:Boolean,
      required:false,
      default:true
    },
    isDelete:{
      type:Boolean,
      required:false,
      default:true
    },
    accept:{
      type:String,
      required:false,
      default:''
    },
    text:{
      type:String,
      required:false,
      default:"点击上传"
    },
    fileType:{
      type:String,
      required:false,
      default:FILE_TYPE_ALL
    },
    listType:{
      type:String,
      required:false,
      default:'text'
    },
    /*这个属性用于控制文件上传的业务路径*/
    bizPath:{
      type:String,
      required:false,
      default:"temp"
    },
    value:{
      type:[String,Array],
      required:false
    },
    // update-begin- --- author:wangshuai ------ date:20190929 ---- for:Jupload组件增加是否能够点击
    disabled:{
      type:Boolean,
      required:false,
      default: false
    },
    showUploadList:{
      type:Boolean,
      required:false,
      default: false
    },

    /**
     * update -- author:lvdandan -- date:20190219 -- for:Jupload组件增加是否返回url，
     * true：仅返回url
     * false：返回fileName filePath fileSize
     */
    returnUrl:{
      type:Boolean,
      required:false,
      default: true
    },
    number:{
      type:Number,
      required:false,
      default: 0
    },
    buttonVisible:{
      type:Boolean,
      required:false,
      default: true
    },
    multiple: {
      type: Boolean,
      default: true
    },
    beforeUpload: {
      type: Function
    },
  },
  watch:{
    value:{
      immediate: true,
      handler() {
        let val = this.value
        if (val instanceof Array) {
          if(this.returnUrl){
            this.initFileList(val.join(','))
          }else{
            this.initFileListArr(val);
          }
        } else {
          this.initFileList(val)
        }
      }
    }
  },
  computed:{
    isImageComp(){
      return this.fileType === FILE_TYPE_IMG
    },
    complistType(){
      return this.fileType === FILE_TYPE_IMG?'picture-card':'text'
    }
  },
  created(){
    const token = Vue.ls.get(ACCESS_TOKEN);
    //---------------------------- begin 图片左右换位置 -------------------------------------
    this.headers = {"X-Access-Token":token};
    this.containerId = 'container-ty-'+new Date().getTime();
    //---------------------------- end 图片左右换位置 -------------------------------------
  },

  methods:{
    initFileListArr(val){
      if(!val || val.length==0){
        this.fileList = [];
        return;
      }
      let fileList = [];
      for(var a=0;a<val.length;a++){
        let url = getFileAccessHttpUrl(val[a].filePath);
        fileList.push({
          uid:uidGenerator(),
          name:val[a].fileName,
          status: 'done',
          url: url,

          response:{
            status:"history",
            message:val[a].filePath
          },
        })
      }
      this.fileList = fileList
    },
    initFileList(paths){
      if(!paths || paths.length==0){
        //return [];
        // update-begin- --- author:os_chengtgen ------ date:20190729 ---- for:issues:326,Jupload组件初始化bug
        this.fileList = [];
        return;
        // update-end- --- author:os_chengtgen ------ date:20190729 ---- for:issues:326,Jupload组件初始化bug
      }
      let fileList = [];
      let arr = paths.split(",")
      for(var a=0;a<arr.length;a++){
        let url = getFileAccessHttpUrl_1(arr[a]);
        fileList.push({
          uid:uidGenerator(),
          name:getFileName(arr[a]),
          status: 'done',
          url: url,
          response:{
            status:"history",
            message:arr[a],

          }
        })
      }
      this.fileList = fileList
      this.initPictureCard(this.fileList)
    },
    initPictureCard(fileList) {
      let obj_orderFile = []
      if (fileList) {
        let tempList =fileList
        for (let i = 0; i < tempList.length; i++) {
          let obj = {
            uid:tempList[i].uid,
            response:tempList[i].response,
            status: tempList[i].status,
            name: tempList[i].name,
            url: tempList[i].url,
            //type:type,
            //imgSrc:type=='image'?tempList[i].url:'',
          }
          obj_orderFile.push(obj)
        }
      }
      this.fileListArray = obj_orderFile
    },
    view(item){
      // 此base地址可以配置到一个常量文件里
      let base = window._CONFIG.VUE_APP_PREVIEW_URL
      let url = base + '?url=' + encodeURIComponent(Base64.encode(item.url))
      window.open(url, '_blank')

    },
    download(item){
      location.href = item.url
    },
    handlePathChange(){
      let uploadFiles = this.fileList
      let path = ''
      if(!uploadFiles || uploadFiles.length==0){
        path = ''
      }
      let arr = [];
      let arrObj=[]
      for(var a=0;a<uploadFiles.length;a++){
        // update-begin-author:lvdandan date:20200603 for:【TESTA-514】【开源issue】多个文件同时上传时，控制台报错
        if(uploadFiles[a].status === 'done' ) {
          arr.push(uploadFiles[a].name)
          arrObj.push(uploadFiles[a].response)
        }else{
          return;
        }
        // update-end-author:lvdandan date:20200603 for:【TESTA-514】【开源issue】多个文件同时上传时，控制台报错
      }
      if(arr.length>0){
        path = arr.join(",")
      }
      this.$emit('change',path, arrObj);
    },
    doBeforeUpload(file){
      this.uploadGoOn=true
      var fileType = file.type;
      if(this.fileType===FILE_TYPE_IMG){
        if(fileType.indexOf('image')<0){
          this.$message.warning('请上传图片');
          this.uploadGoOn=false
          return false;
        }
      }
      // 扩展 beforeUpload 验证
      if (typeof this.beforeUpload === 'function') {
        this.uploadGoOn= this.beforeUpload(file)
        return this.uploadGoOn
      }
      return true
    },
    handleChange(info) {
      if(!info.file.status && this.uploadGoOn === false){
        info.fileList.pop();
      }
      let fileList = info.fileList
      if(info.file.status==='done'){
        if(this.number>0){
          fileList = fileList.slice(-this.number);
        }
        if(info.file.response.success){
          fileList = fileList.map((file) => {
            if (file.response) {
              let reUrl = file.response.message;
              file.url = getFileAccessHttpUrl_1(reUrl);
            }
            return file;
          });
        }
        //this.$message.success(`${info.file.name} 上传成功!`);
      }else if (info.file.status === 'error') {
        this.$message.error(`${info.file.name} 上传失败.`);
      }else if(info.file.status === 'removed'){
        this.handleDelete(info.file)
      }
      this.fileList = fileList
      this.initPictureCard(this.fileList)
      if(info.file.status==='done' || info.file.status === 'removed'){
        //returnUrl为true时仅返回文件路径
        if(this.returnUrl){
          this.handlePathChange()
        }else{
          //returnUrl为false时返回文件名称、文件路径及文件大小
          this.newFileList = [];
          for(var a=0;a<fileList.length;a++){
            // update-begin-author:lvdandan date:20200603 for:【TESTA-514】【开源issue】多个文件同时上传时，控制台报错
            if(fileList[a].status === 'done' ) {
              var fileJson = {
                fileName:fileList[a].name,
                filePath:fileList[a].response.message,
                fileContent:fileList[a].response.result,
                fileSize:fileList[a].size
              };
              this.newFileList.push(fileJson);
            }else{
              return;
            }
            // update-end-author:lvdandan date:20200603 for:【TESTA-514】【开源issue】多个文件同时上传时，控制台报错
          }
          this.initPictureCard(this.newFileList)
          this.$emit('change', this.newFileList);
        }
      }
    },
    handleDelete(file) {
      //如有需要新增 删除逻辑

    let newFileList=this.fileList.filter((item)=>{
        if(item.uid===file.uid){
          return false
        }
        return true
      })
      this.fileList=newFileList
      this.initPictureCard(this.fileList)
    },
    // 文件下载
    handleDownload(file) {
      location.href = file.url
    },
  },

  model: {
    prop: 'value',
    event: 'change'
  }
}
</script>

<style lang="less" scoped>
.uploadty-disabled{
  .ant-upload-list-item {
    .anticon-close{
      display: none;
    }
    .anticon-delete{
      display: none;
    }
  }
}
//---------------------------- begin 图片左右换位置 -------------------------------------
.uploadty-mover-mask{
  background-color: rgba(0, 0, 0, 0.5);
  opacity: .8;
  color: #fff;
  height: 28px;
  line-height: 28px;
}
.appendix{
  .row{
   // margin-left: 2em!important;
    .col{
      .bg-wrapper{
        position: relative;
        width: 120px;
        height: 120px;
        border: 1px solid #d6d6d6;
        padding: 5px;
        box-sizing: border-box;
        border-radius: 3px;

        .bg{
          position: absolute;
          box-sizing: border-box;
          top:0px;
          left: 0px;
          width:100%;
          height: 100%;
          border-radius: 3px;

          .icon{
            position: absolute;
            top:25px;
            left:25px;
            font-size: 70px;
            color:var(--themeColor) !important;
          }
        }

        .img{
          background-size: cover !important;
        }

        .action-wrapper{
          position: absolute;
          top:0;
          left: 0;
          width: 100%;
          height: 100%;
          display: none;

          .action{
            display: flex;
            justify-content:center;
            align-items:center;
            background-color:  rgba(0, 0, 0, 0.75);
            height: 100%;
            font-size: 20px;
            border-radius: 3px;
            .action-icon{
              color: #ffffff;
            }
            .action-icon:hover{
              cursor: pointer;
              color: var(--themeColor) !important;
            }
          }
        }
      }
      .bg-wrapper:hover{
        .action-wrapper{
          display: block;
        }
      }
    }
  }
}
//---------------------------- end 图片左右换位置 -------------------------------------
</style>