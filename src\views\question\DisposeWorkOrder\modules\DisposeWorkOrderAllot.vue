<template>
  <a-modal
    :title="title"
    :width="modalWidth"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @cancel="handleCancel"
    @ok="handleOk"
    okText=""
    cancelText="关闭"
    wrapClassName="ant-modal-cust-warp"
    style="height: 50%; overflow: hidden; overflow-y: auto"
    :centered="true"
  >
    <a-form :form="form" style="height: 100%; overflow-y: auto">
      <div>
        <a-row>
          <a-col>
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="原因">
              <textarea
                :rows="4"
                style="width: 100%"
                placeholder="请输入请求原因"
                v-decorator="['remark', formValidator.remark]"
              ></textarea>
            </a-form-item>
          </a-col>
        </a-row>
      </div>
    </a-form>
  </a-modal>
</template>
<script>
import { editRecord } from '@api/AllocationIssue'
export default {
  name: 'DisposeWorkOrderAllot', //请求重新分配工单弹窗
  data() {
    return {
      title: '操作',
      visible: false,
      confirmLoading: false,
      /* 弹框宽 */
      modalWidth: '700px',
      form: this.$form.createForm(this),
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      formValidator: {
        remark: {
          rules: [
            {
              required: true,
              message: '必填!',
            },
            {
              min: 2,
              max: 500,
              message: '长度在 2 到 500 个字符',
              trigger: 'blur',
            },
          ],
        },
      },
    }
  },
  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let formData = Object.assign(this.model, values)
          //请求后，变成待分配工单
          formData.status = '请求分配'
          editRecord(formData)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
              that.close()
            })
        }
      })
    },
    handleCancel() {
      this.close()
    },
  },
}
</script>
<style scoped>
.Header div {
  display: inline-block;
}
.Header div:nth-child(1) {
  font-size: 17px;
  font-weight: 700;
}
.Header div:nth-child(2) {
  font-size: 16px;
}
</style>
<style lang="less" scoped>

::v-deep .ant-modal-body {
  padding: 24px 48px 24px 48px;
}
::v-deep .ant-modal {
  padding: 24px;
}
@media (max-width: 1012px) {
  ::v-deep .ant-modal {
    max-width: calc(100vw - 12px);
    margin: 0;
  }
}
</style>
