<template>
  <j-modal
    :title='title'
    :width='width'
    :centered='true'
    :visible='visible'
    :destroyOnClose='true'
    switchFullscreen
    cancelText='关闭'
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @cancel='handleCancel'
    :fullscreen.sync='fScreen'
  >
    <a-spin :spinning='confirmLoading' style='display: flex;flex-flow: nowrap column'>
      <div id='diffDescDiv'>{{ diffDescription }}</div>
      <div ref='contrastDiv' style='flex:auto' :style='{height: contrastDivHeight}'></div>
    </a-spin>
  </j-modal>
</template>
<script>

import 'codemirror/mode/shell/shell.js'
import CodeMirror from 'codemirror'
import 'codemirror/lib/codemirror.css'
import 'codemirror/addon/merge/merge.js'
import 'codemirror/addon/merge/merge.css'
import DiffMatchPatch from 'diff-match-patch'
import dedent from 'dedent'
window.diff_match_patch = DiffMatchPatch
window.DIFF_DELETE = -1
window.DIFF_INSERT = 1
window.DIFF_EQUAL = 0
export default {
  name: 'ContrastConfigModal',
  props: {},
  data() {
    return {
      title: '新增',
      width: '1300px',
      fScreen:true,
      disableSubmit: false,
      visible: false,
      confirmLoading: false,

      leftText: '', //左侧展示文本
      rightText: '', //右侧展示文本
      diffDescription: '', //差异内容描述
      contrastDivHeight: 0
    }
  },
  computed: {
    fullscreen: {
      get() {
      },
      set(v) {
        this.calcContrastDivHeight()
      }
    }
  },
  methods: {
    prepareData() {

    },
    contrast() {
      const that = this
      that.$nextTick(() => {
        const target = that.$refs.contrastDiv
        target.innerHTML = ''
        let difference = CodeMirror.MergeView(target, {
          value: that.leftText,
          originLeft: null,
          orig: that.rightText,
          lineNumbers: true,
          mode: "text/html",
          // mode: 'text/x-sh',
          hightlightDifference: true,
          connect: 'align',
          readOnly: true,
          theme: 'dracula',
          smartIndent: true
        })
        let diffCount = difference.right.chunks.length
        console.log(diffCount)
        let description = ''
        if (diffCount === 0) {
          description = '左右文本内容一致'
        } else if (diffCount === 1) {
          description =
            '共1处差异，差异的开始行号为：' +
            (difference.right.chunks[0].origFrom + 1)
        } else {
          description = '共' + diffCount + '处差异，每处差异的开始行号为：'
          for (let i = 0; i < diffCount; i++) {
            description += difference.right.chunks[i].origFrom + 1
            if (i !== diffCount - 1) {
              description += '、'
            }
          }
        }
        that.diffDescription = description
        that.calcContrastDivHeight()
      })
    },
    calcContrastDivHeight() {
      let that = this
      setTimeout(() => {
        let diffDescDiv = document.getElementById('diffDescDiv')
        if (diffDescDiv) {
          let diffDesHeight = diffDescDiv.offsetHeight
          that.contrastDivHeight = 'calc(100% - ' + diffDesHeight + 'px)'
        }
      })
    },
    loadConfigs(selectedRowKeys) {
      this.visible = true
      this.fScreen=true
      let lTxt=selectedRowKeys[0].configureText
      let rTxt= selectedRowKeys[1].configureText
      this.leftText = lTxt&&lTxt.length>0?lTxt.replaceAll('<br/>','\n'):""
      this.rightText = rTxt&&rTxt.length>0?rTxt.replaceAll('<br/>','\n'):""
      this.contrast()
    },
    close() {
      this.visible = false
    },
    handleCancel() {
      this.close()
    }
  }
}
</script>
<style scoped lang='less'>
@import '~@assets/less/normalModal.less';

::v-deep .CodeMirror-merge-copy {
  display: none !important;
}

::v-deep .CodeMirror-merge, ::v-deep .CodeMirror-merge .CodeMirror {
  height: 100% !important;
}

::v-deep .CodeMirror-merge-pane {
  height: 100% !important;
}

::v-deep .ant-modal {
  height: 100%;

  .ant-modal-content {
    height: 100% !important;

    .ant-modal-body {
      height: calc(100% - 55px - 53px) !important;

      .ant-spin-nested-loading {
        height: 100% !important;

        .ant-spin-container {
          height: 100% !important;
        }
      }
    }
  }
}
</style>