<template>
  <j-modal
    :maskClosable="false"
    :visible="dialogTaskBackNodesInChild"
    :destroyOnClose="true"
    :width="800"
    title="退回任务"
    @cancel="handleCancel"
    @ok="handleOk"
  >
    <a-form-item>
      <a-radio-group @change="selectBackNode">
        <a-radio v-for="item in backNodes" :key="item.nodeId " :value="item">
          【{{ item.nodeName }}】{{ item.userName }}
        </a-radio>

      </a-radio-group>
    </a-form-item>
  </j-modal>
</template>

<script>
import { getAction } from '@/api/manage'

export default {
  name: 'TaskBackNodes',
  props: {
    visible: {
      type: <PERSON>olean,
      default: function () {
        return false
      }
    },
    executeTaskId: {
      type: String,
      default: function () {
        return ''
      }
    }
  },
  data() {
    return {
      backNode: '',
      backNodes: []
    }
  },
  computed: {
    dialogTaskBackNodesInChild: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  created() {
    this.initData()
  },
  methods: {
    initData() {
      getAction('/flowable/task/backNodes', { taskId: this.executeTaskId }).then(res => {
        const { result } = res
        if (result) {
          this.backNodes = result
        }
        if (result.length === 0){
          this.dialogTaskBackNodesInChild = false
          this.$message.error('当前已是最初节点，无法回退！')
        }
      })
    },
    selectBackNode(val) {
      this.backNode = val.target.value
    },
    doConfirm() {
      this.dialogTaskBackNodesInChild = false
      this.$emit('backTaskFinished', this.backNode)
    },
    handleOk() {
      this.dialogTaskBackNodesInChild = false
      if (this.backNode) {
        this.$emit('backTaskFinished', this.backNode)
      } else {
        this.$message.error('当前已是最初节点，无法回退！')
      }
    },
    handleCancel() {
      this.dialogTaskBackNodesInChild = false
    },
  }
}
</script>
<style lang="less" scoped>
@import '~@assets/less/YQNormalModal.less';
</style>

