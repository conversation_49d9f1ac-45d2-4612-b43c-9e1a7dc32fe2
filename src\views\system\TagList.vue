<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll zhl zhll">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <!-- 查询区域 -->
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class="card-style">
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="标签名称">
                  <a-input :maxLength="maxLength" placeholder="请输入标签名称" v-model="queryParam.tagName" :allowClear="true" autocomplete="off" />
                </a-form-item>
              </a-col>

              <a-col :span="colBtnsSpan()">
                <span class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button class="btn-search btn-search-style" type="primary" @click="searchQuery">查询</a-button>
                  <a-button class="btn-reset btn-reset-style" @click="searchReset">重置</a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered="false" style="width: 100%; flex: auto">
        <!-- 操作按钮区域 -->
        <div class="table-operator table-operator-style">
          <a-button class="btn-confirm" @click="handleAdd">新增</a-button>
          <a-dropdown v-if="selectedRowKeys.length > 0">
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item key="1" @click="deleteCheck()">删除 </a-menu-item>
            </a-menu>
            <a-button>
              批量操作
              <a-icon type="down" />
            </a-button>
          </a-dropdown>
        </div>
        <!-- table区域-begin -->
        <a-table ref="table" bordered :rowKey="(record, index) => {
            return record.id
          }
          " :columns="columns" :dataSource="dataSource" :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
          :pagination="ipagination" :loading="loading"
          :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }" @change="handleTableChange">
          <!-- 字符串超长截取省略号显示-->
          <span slot="templateContent" slot-scope="text">
            <j-ellipsis :value="text" :length="25" />
          </span>
          <span slot="action" slot-scope="text, record" class="caozuo">
            <a-divider type="vertical" v-if="record.confirmStatus === '1'" />
            <a @click="handleEdit(record)">编辑</a>
            <a-divider type="vertical" />
            <a @click="deleteCheck(record)">删除</a>
            <!-- 单条删除 文本太长 弹窗样式会变形 点位不准 -->
            <!-- <a-popconfirm :title="deleteMsg" @confirm="() => deleteRecord(record)">
              <a>删除</a>
            </a-popconfirm> -->
          </span>
          <template slot="tooltip" slot-scope="text">
            <a-tooltip placement="topLeft" :title="text" trigger="hover">
              <div class="tooltip">
                {{ text }}
              </div>
            </a-tooltip>
          </template>
          <template slot="tagColor" slot-scope="text,record">
            <a-tooltip>
              <template slot="title">
                {{ text }}
              </template>
              <div :style="{
                'background-color': record.tagColor, color: 'white',
                'text-align': 'center', 'white-space': 'nowrap',
                'border-radius': '10px', padding: '2px 10px', 'text-stroke': '0px black',
                'width': '100%', overflow: 'hidden', 'text-overflow': 'ellipsis'
              }">
                {{ text }}</div>
            </a-tooltip>
          </template>
          <template slot="isStatistic" slot-scope="text">
            <span v-if="text == 0">否</span>
            <span v-else-if="text == 1">是</span>
          </template>
          <template slot="isInlay" slot-scope="text">
            <span v-if="text == 0">否</span>
            <span v-else-if="text == 1">是</span>
          </template>
        </a-table>
      </a-card>
    </a-col>

    <tag-modal ref="modalForm" @ok="modalFormOk"></tag-modal>
  </a-row>
</template>

<script>
import {
  JeecgListMixin
} from '@/mixins/JeecgListMixin'
import JEllipsis from '@/components/jeecg/JEllipsis'
import {
  deleteAction,
  getAction,
  putAction
} from '@/api/manage'
import JDictSelectTag from '@/components/dict/JDictSelectTag.vue'

import {
  YqFormSearchLocation
} from '@/mixins/YqFormSearchLocation'
import TagModal from './modules/TagModal.vue'
export default {
  name: 'TagList',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components: {
    JEllipsis,
    JDictSelectTag,
    TagModal,
  },
  data() {
    return {
      maxLength:50,
      description: '标签管理页面',
      formItemLayout: {
        labelCol: {
          style: 'width:80px',
        },
        wrapperCol: {
          style: 'width:calc(100% - 80px)'
        }
      },
      // 表头
      columns: [{
        title: '标签名称',
        dataIndex: 'tagName',
        scopedSlots: {
          customRender: 'tagColor'
        },
        customCell: () => {
          let cellStyle = 'text-align: center;max-width:200px'
          return {
            style: cellStyle
          }
        },
      },
      {
        title: '标签标识',
        dataIndex: 'tagKey',
        customCell: () => {
          let cellStyle = 'text-align: left;min-width: 130px;max-width:300px'
          return {
            style: cellStyle
          }
        },
      },
      {
        title: '标签颜色',
        dataIndex: 'tagColor',
        customCell: () => {
          let cellStyle = 'text-align: center;min-width: 90px;max-width:120px;{background-color:tagColor}'
          return {
            style: cellStyle
          }
        },
      },
      {
        title: '绑定资源数',
        dataIndex: 'resourceNum',
        customCell: () => {
          let cellStyle = 'text-align: right;min-width: 100px;max-width:130px'
          return {
            style: cellStyle
          }
        },
      },
      {
        title: '参与统计',
        dataIndex: 'isStatistic',
        scopedSlots: {
          customRender: 'isStatistic'
        },
        customCell: () => {
          let cellStyle = 'text-align: center;min-width: 90px;max-width:120px'
          return {
            style: cellStyle
          }
        },
      },
      {
        title: '是否内置',
        dataIndex: 'isInlay',
        scopedSlots: {
          customRender: 'isInlay'
        },
        customCell: () => {
          let cellStyle = 'text-align: center;min-width: 90px;max-width:120px'
          return {
            style: cellStyle
          }
        },
      },
      {
        title: '创建时间',
        dataIndex: 'createTime',
        customCell: () => {
          let cellStyle = 'text-align: center;min-width: 120px;max-width:150px'
          return {
            style: cellStyle
          }
        },
      },
      {
        title: '操作',
        dataIndex: 'action',
        align: 'center',
        width: 160,
        scopedSlots: {
          customRender: 'action'
        },
      },
      ],
      url: {
        list: '/utl/taginfo/list',
        deleteBatch: '/utl/taginfo/deleteBatch',
        check: '/utl/taginfo//isRelatedPolicy',
      },
      dataSource: [],
      deleteMsg: '',
    }
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
  },
  created() { },
  methods: {
    //标签删除检查
    deleteCheck(record) {
      let ids = '';
      let isBatch = false;
      //单个删除
      if (record && record.id) {
        ids = record.id;
        this.deleteMsg = "确定删除该条标签？"
      } else {
        //批量删除
        if (this.selectedRowKeys.length <= 0) {
          this.$message.warning('请选择一条记录！')
          return
        }
        for (let a = 0; a < this.selectedRowKeys.length; a++) {
          ids += this.selectedRowKeys[a] + ','
        }
        isBatch = true;
        this.deleteMsg = "确定删除选中的标签？"
      }
      getAction(this.url.check, { ids: ids }).then(res => {
        if (res.success && res.result) {
          this.deleteMsg = res.message;
        }
      }).finally(() => {
        this.batchDel(ids)
      })
    },
    //删除
    deleteRecord(record) {
      if (!this.url.deleteBatch) {
        this.$message.error('请设置url.deleteBatch属性!')
        return
      }
      var that = this
      that.loading = true
      deleteAction(that.url.deleteBatch, {
        ids: record.id
      }).then((res) => {
        if (res.success) {
          that.$message.success(res.message)
          that.loadData()
        } else {
          that.$message.warning('当前标签为内置标签，禁止删除')
          that.loadData()
        }
      })
    },
    //批量删除
    batchDel: function (ids) {
      if (!this.url.deleteBatch) {
        this.$message.error('请设置url.deleteBatch属性!')
        return
      }
      let that = this;
      this.$confirm({
          title: '确认删除',
          okText: '是',
          cancelText: '否',
          content: this.deleteMsg,
          onOk: function () {
            that.loading = true
            deleteAction(that.url.deleteBatch, {
              ids: ids
            })
              .then((res) => {
                if (res.success) {
                  that.$message.success(res.message)
                  that.loadData()
                  that.onClearSelected()
                } else {
                  that.$message.warning(res.message)
                }
              })
              .finally(() => {
                that.loading = false
              })
          },
        })
    },
  },
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';

.queren {
  color: rgba(0, 0, 0, 0.25) !important;
}
</style>