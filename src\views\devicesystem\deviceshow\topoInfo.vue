<template>
  <div>
    <div class="net-content" v-if="visible==true">
      <div class="big-screen-div" v-show="topoList.length>0">
        <!-- 拓扑图容器 -->
        <topo-view ref="bigScreen" operate="show" :lighterSetting="lighterSetting"></topo-view>
      </div>
      <div v-if="topoList.length===0 && !loading" style="width:100%;height:100%;">
        <a-list :data-source="[]" style="margin-top:8%;" />
      </div>
      <div class="topo-list" ref="netTopo">
        <div v-for="(item, index) in topoList" :key="item.id" class="list-div">
          <div
            :class="['img-div', selectIndex === index ? 'select-img' : '']"
            style="height: 127px;"
            v-if="item.topoSvg"
            v-html="item.topoSvg"
            @click="showNetTopo(item, index)"
          ></div>
          <div class="topoName">{{ item.topoName }}</div>
        </div>
        <infinite-loading @infinite="infiniteHandler" spinner="circles">
          <div slot="spinner">加载中...</div>
          <div slot="no-more" v-if="pageNo > 2">没有更多了</div>
          <div v-else slot="no-more"></div>
          <div slot="no-results" v-if="pageNo > 2">没有更多了</div>
          <div v-else slot="no-results"></div>
        </infinite-loading>
      </div>
    </div>
  </div>
</template>
<script>
import InfiniteLoading from 'vue-infinite-loading'
import { getAction } from '@/api/manage'
import topoView from '@views/topo/nettopo/modules/VisEdit'
export default {
  components: {
    InfiniteLoading,
    topoView
  },
  data() {
    return {
      topoList: [],
      pageNo: 1,
      pageSize: 10,
      url: {
        list: '/topo/topoInfo/getdeviceTopoList',
        getFocusTopo: '/topo/topoInfo/getFocusTopo'
      },
      selectIndex: 0,
      deviceInfo: {},
      visible: false,
      loading: true,
      lighterSetting: {
        isZoom: true, // 是否设置画布的缩放级别
        isCenter: true, // 是否将指定的点与视口中心对齐
      }
    }
  },
  destroyed() {},
  methods: {
    show(record) {
      this.visible = true
      this.deviceInfo = record
    },
    infiniteHandler($state) {
      this.loading = true
      getAction(this.url.list, {
        deviceCode: this.deviceInfo.deviceCode,
        pageNo: this.pageNo,
        pageSize: this.pageSize
      }).then(res => {
         this.loading = false
        if (res.success) {
          if (res.result.pageList.records.length > 0) {
            this.pageNo++
            this.topoList = [...this.topoList, ...res.result.pageList.records]
            this.$nextTick(() => {
              if (this.pageNo == 2 && this.topoList.length > 0) {
                this.showNetTopo(this.topoList[0], 0)
              }
            })
            if ($state) {
              if (res.result.pageList.records.length < this.pageSize) {
                $state.complete()
              } else {
                $state.loaded()
              }
            }
          } else {
            if ($state) {
              $state.complete()
            }
          }
        }
      }).catch(e => {
        this.loading = false
        this.$message.error(e)
      })
    },
    async showNetTopo(record, index) {
      await this.$refs.bigScreen.clearStatusInterval()
      await this.$refs.bigScreen.createTopo(record.id, false, this.deviceInfo.deviceCode, 'fiveTopo')
      this.selectIndex = index
    }
  }
}
</script>
<style lang="less" scoped>
.net-content {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.big-screen-div {
  width: calc(86% - 15px);
  height: 100%;
  margin-right: 15px;
}

.topo-list {
  width: 14%;
  height: 96%;
  padding-right: 10px;
  overflow-y: auto;
  position: absolute;
  right: 0;
  top: 0;
}

::-webkit-scrollbar {
  width: 6px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
}

.list-div {
  position: relative;
  text-align: center;
  cursor: pointer;
  margin-bottom: 10px;

  span {
    font-size: 14px;
    color: #4e4f53;
  }
}

.img-div {
  width: 100%;
  height: 100%;
  border: 1px solid #aaaaac;
  position: relative;
}

.topoName {
  position: absolute;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  color: #fff;
  width: 100%;
  padding: 0 10px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.select-img {
  border: 2px solid #000;
}
::v-deep .img-div > svg {
  width: 100% !important;
  height: 100% !important;
}
::v-deep .img-div > svg > g {
  transform: matrix(1, 0, 0, 1, 1, 1) !important;
}
</style>