<template>
  <div class="zr-bigscreen-layout">
    <!-- 顶部组件 -->
    <zr-bigscreen-header></zr-bigscreen-header>
    <div class="zr-bigscreen-pages">
      <keep-alive v-if="keepAlive">
        <router-view />
      </keep-alive>
      <router-view v-else />
    </div>
  <zr-bigscreen-footer></zr-bigscreen-footer>
  </div>
</template>

<script>
import ZrBigscreenHeader from '../page/ZrBigscreenHeader.vue'
import ZrBigscreenFooter from '../page/ZrBigscreenFooter.vue'
export default {
  name: 'ZrBigScreenLayout',
  components: { ZrBigscreenHeader,ZrBigscreenFooter },
  data() {
    return {}
  },
  computed: {
    keepAlive() {
      return this.$route.meta.keepAlive
    },
  },
  methods: {},
}
</script>
<style>

</style>
<style lang="less" scoped>
.zr-bigscreen-layout {
  height: 100%;
  background-image: url(/zrBigScreen/zrBg.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  overflow: hidden;
}
.zr-bigscreen-pages {
  height: calc(100% - (190 / 19.2 * 1vw) - 0px);
  margin-top: calc(-50 / 19.2 * 1vw);
}

</style>