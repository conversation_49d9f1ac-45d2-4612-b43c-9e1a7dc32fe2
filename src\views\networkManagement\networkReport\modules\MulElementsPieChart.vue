<template>
  <div ref='chart_ref' class='chart_ref' style='display: block;width: 100%;height: 100%'></div>
</template>
<script>
export default {
  name: 'MulElementsPieChart',
  props: {
    requestDataUrl: '',
    name: '',
    chartData: null
  },
  data() {
    return {
      chartInstance: null,
      newChartData: [],
      colorList: [
        { color1: 'rgba(195,252,192,1)', color2: 'rgba(41,202,127,1)' },
        { color1: 'rgba(253,186,197,1)', color2: 'rgba(244,73,103,1)' },
        { color1: 'rgba(184,234,255,1)', color2: 'rgba(37,168,223,1)' },
        { color1: 'rgba(174,203,255,1)', color2: 'rgba(93,149,252,1)' },
        { color1: 'rgba(246,203,159,1)', color2: 'rgba(255,169,64,1)' },
        { color1: 'rgba(90,255,223,1)', color2: 'rgba(76,167,163,1)' },
        { color1: 'rgba(159,110,254,1)', color2: 'rgba(106,76,178,1)' },
        { color1: 'rgba(115,255,145,1)', color2: 'rgba(65,149,99,1)' },
        { color1: 'rgba(76,174,254,1)', color2: 'rgba(54,122,194,1)' },
        { color1: 'rgba(193,255,138,1)', color2: 'rgba(127,177,108,1)' },
        { color1: 'rgba(234,103,161,1)', color2: 'rgba(173,15,84,1)' },
        { color1: 'rgba(255,207,74,1)', color2: 'rgba(190,164,75,1)' },
        { color1: 'rgba(153,255,179,1)', color2: 'rgba(98,167,131,1)' },
        { color1: 'rgba(224,119,199,1)', color2: 'rgba(164,41,206,1)' },
        { color1: 'rgba(151,176,255,1)', color2: 'rgba(105,123,186,1)' },
        { color1: 'rgba(164,151,255,1)', color2: 'rgba(106,102,173,1)' },
        { color1: 'rgba(236,113,187,1)', color2: 'rgba(208,44,143,1)' },
        { color1: 'rgba(254,178,128,1)', color2: 'rgba(165,123,98,1)' },
        { color1: 'rgba(180,143,241,1)', color2: 'rgba(134,109,188,1)' },
        { color1: 'rgba(131,199,255,1)', color2: 'rgba(97,145,197,1)' }]
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.getChartData()
      if(this.newChartData.length>0){
        this.initChart()
        this.$nextTick(() => {
          this.setChartize()
        })
      }
    })
  },
  destroyed() {
    window.removeEventListener('resize', this.setChartize)
  },
  methods: {
    getChartData() {
      this.newChartData = []
      let arr = []
      if (this.chartData.length > 0) {
        arr = this.chartData.sort(function(a, b) {
          return a.value - b.value
        })
        arr.map((item, index) => {
          let m = {
            name: this.chartData[index].name,
            value: this.chartData[index].value,
            label: {
              fontSize: 14,
              formatter: '{b}:{c}'
            },
            labelLine: {
             /* lineStyle: {
                color: 'rgba(255, 255, 255, 0.3)'
              },*/
              //smooth: 0.2,
              length: 20,
              length2:10
            },
            itemStyle: {
              normal: {
                color: {
                  colorStops: [{
                    offset: 0,
                    color: this.colorList[index].color1
                  }, {
                    offset: 1,
                    color: this.colorList[index].color2
                  }]
                }
                //shadowBlur: 200,
                //shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
          this.newChartData.push(m)
        })
      }
    },
    initChart() {
      let _this = this
      _this.chartInstance = _this.$echarts.init(_this.$refs['chart_ref'])
      let initOption = {
        tooltip: {
          trigger: 'item',
          // formatter: '{a} <br/>{b} : {c} ({d}%)'
          formatter: '{b} : {c} ({d}%)'
        },
        series: [
          {
            name: this.name,
            type: 'pie',
            roseType: 'radius',
            //roseType: 'area',
            radius: ['30%', '70%'],
            center: ['50%', '50%'],
            startAngle:70,
            minAngle: 10,
            animationType: 'scale',
            animationEasing: 'elasticOut',
            avoidLabelOverlap:true,
            animationDelay: function(idx) {
              return Math.random() * 200
            },
            data: this.newChartData
          },
          {
            name: '内环1',
            type: 'pie',
            minAngle: 10,
            startAngle:70,
            silent: true,
            clockWise: true,
            hoverAnimation: false,
            animationType: 'scale',
            radius: ['29%', '31%'],
            label: {show:false},
            tooltip:{
              show:false
            },
            /*data: [{
              value: 100,
              itemStyle: {
                normal: {
                  color: {
                    colorStops: [{
                      offset: 0,
                      color: 'rgba(196,203,196,0.38)'
                    }, {
                      offset: 1,
                      color: 'rgba(143,145,145,0.51)'
                    }]
                  }
                }
              }
            }]*/
            data:this.newChartData
          },
          {
            name: '内环2',
            type: 'pie',
            silent: true,
            clockWise: true,
            hoverAnimation: false,
            animationType: 'scale',
            radius: ['22%', '25%'],
            label: {
              normal: {
                position: 'center'
              }
            },
            data: [{
              value: 100,
              itemStyle: {
                normal: {
                  color: {
                    colorStops: [{
                      offset: 0,
                      color: 'rgba(152,251,152,1)'
                    }, {
                      offset: 1,
                      color: 'rgba(64,224,208,1)'
                    }]
                  }
                }
              }
            }]
          }
        ]
      }
      _this.chartInstance.setOption(initOption, true)
      window.addEventListener('resize', _this.setChartize)
    },
    setChartize() {
      this.chartInstance.resize()
    }
  }
}
</script>
<style scoped lang='less'>
.chart_ref {
  position: relative;
}

.chart_ref > div {
  width: 100% !important;
}
</style>