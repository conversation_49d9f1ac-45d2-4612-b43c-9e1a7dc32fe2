<template>
  <a-modal
    :title="title"
    :width="width"
    :visible="visible"
    :maskClosable="false"
    okText="确定"
    :confirmLoading="confirmLoading"
    switchFullscreen
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭">
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">

        <a-form-item label="应用名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="[ 'appName', validatorRules.appName]" placeholder="请输入应用名称"></a-input>
        </a-form-item>
        <a-form-item label="应用编码" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            v-decorator="[ 'appCode', validatorRules.appCode]"
            placeholder="请输入应用编码"></a-input>
        </a-form-item>

      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>

  import { httpAction } from '@/api/manage'
  import pick from 'lodash.pick'
  import { validateDuplicateValue } from '@/utils/util'
  import { duplicateCheck } from '@/api/api'

  export default {
    name: 'LedgerAppModal',
    components: {
    },
    data () {
      return {
        form: this.$form.createForm(this),
        title: '操作',
        width: 800,
        visible: false,
        model: {},
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        },
        confirmLoading: false,
        validatorRules: {
          appName: { rules: [
            { required: true, message: '请输入应用名称!' },
			{
				min: 2,
				max: 30,
				message: '应用名称长度在 2 到 30 个字符',
				trigger: 'blur'
			}
          ] },
          appCode: { rules: [
            {
              required: true, message: '请输入应用编码!'
            },
			{
				min: 2,
				max: 30,
				message: '应用编码长度在 2 到 30 个字符',
				trigger: 'blur'
			}, {
              validator: this.validateAppcode
            }
          ] }
        },
        url: {
          add: '/ledgerApp/add',
          edit: '/ledgerApp/edit'
        }
      }
    },
    created () {
    },
    methods: {
      add () {
        this.edit({})
      },
      edit (record) {
        this.form.resetFields()
        this.model = Object.assign({}, record)
        this.visible = true
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.model, 'appName', 'appCode'))
        })
      },
      close () {
        this.$emit('close')
        this.visible = false
      },
      handleOk () {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.model.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
               method = 'put'
            }
            let formData = Object.assign(this.model, values)
            httpAction(httpurl, formData, method).then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            }).finally(() => {
              that.confirmLoading = false
              that.close()
            })
          }
        })
      },
      handleCancel () {
        this.close()
      },
      popupCallback(row) {
        this.form.setFieldsValue(pick(row, 'appName', 'appCode'))
      },
      validateAppcode(rule, value, callback) {
      if (/[\u4E00-\u9FA5]/g.test(value)) {
        callback('角色编码不可输入汉字!')
      } else {
        var params = {
          tableName: 'sys_ledger_app',
          fieldName: 'app_code',
          fieldVal: value,
          dataId: this.model.id
        }
        duplicateCheck(params).then((res) => {
          if (res.success) {
            callback()
          } else {
            callback(res.message)
          }
        })
      }
    }

    }
  }
</script>
