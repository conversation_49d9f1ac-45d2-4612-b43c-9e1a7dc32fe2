<template>
  <div>
    <a-table
      ref='table'
      bordered
      :row-key='(record, index) => {return record.id}'
      :columns='columns'
      :dataSource='dataSource'
      :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
      :pagination='ipagination'
      :loading='loading'
      @change='handleTableChange'>
      <template slot='tooltip' slot-scope='text'>
        <a-tooltip placement='topLeft' :title='text' trigger='hover'>
          <div class='tooltip'>
            {{ text }}
          </div>
        </a-tooltip>
      </template>
    </a-table>
  </div>
</template>
<script>
import {JeecgListMixin} from '@/mixins/JeecgListMixin'
export default {
  name: "KnowledgeApprovalHistory",
  mixins: [JeecgListMixin],
  props:{
    kInfo:{
      type:Object,
      required:true,
      default:{}
    }
  },
  data() {
    return {
      disableMixinCreated:true,
      columns: [
        {
          title: '审批人',
          dataIndex: 'userName'
        },
        {
          title: '审批结果',
          dataIndex: 'resultString'
        },
        {
          title: '审批时间',
          dataIndex: 'createTime'
        },
        {
          title: '备注',
          dataIndex: 'comment',
          customCell: () => {
            let cellStyle = 'text-align: left;max-width:300px'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'tooltip'
          }
        }
      ],
      url: {
        list: '',//列表
      }
    }
  },
  watch:{
    kInfo:{
      handler(val){
        if(Object.keys(val).length>0){
          this.url.list=`/kbase/knowledges/${val.id}/review`
          this.loadData(1)
        }
      },
      deep:true,
      immediate:true
    }
  },
  methods:{

  }
}
</script>

<style scoped lang="less">
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
.add-comment{
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-flow: row wrap;
  margin-bottom: 16px;
}

.like-unlike-wrapper{
  margin: 0px 20px;

  .like-unlike {
    display: inline-block;
    white-space: nowrap;
    width: 30px;
    height: 30px;
    line-height: 30px;
    border-radius: 50%;
    border: 1px solid #e8e8e8;
    text-align: center;


    .like-unlike-icon {
      font-size: 18px;
    }
  }
  .like-unlike:hover{
    cursor: pointer;
    border: 1px solid #409eff;
  }
  .active-like,.active-unlike{
    color:#409eff
  }
}

.table-operator{
  margin-bottom: -16px;
}
</style>