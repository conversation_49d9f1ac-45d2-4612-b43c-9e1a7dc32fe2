<template>
  <j-modal
    :title="title"
    :visible="visible"
    :destroyOnClose="true"
    fullscreen
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭">
    <process-edit v-if="visible" ref="processEdit" @ok="submitCallback"></process-edit>
  </j-modal>
</template>

<script>
    import processEdit from './processEdit.vue'
  export default {
    name: 'processManage',
    components: {
      processEdit
    },
    data () {
      return {
        title: '',
        width: 800,
        record: {},
        visible: false,
        disableSubmit: false
      }
    },
    methods: {
      show(record) {
        this.visible = true
        this.record = record
        this.$nextTick(() => {
          this.$refs.processEdit.show(this.record)
        })
      },
      edit(record,info) {
        this.visible = true
        this.record = record
        this.$nextTick(() => {
         this.$refs.processEdit.create(record,info)
        })
      },
      close () {
        this.$emit('close')
        this.visible = false
      },
      handleOk () {
        this.$refs.processEdit.save()
      },
      handleCancel (record) {
        this.close()
      },
      submitCallback(params){
        this.$emit('ok',params);
        this.visible = true;
      },
    }
  }
</script>

<style lang="less" scoped>

</style>
