<template>
  <j-modal
    :width="1200"
    :title="row.name"
    :visible="_pictureVisible"
    :maskClosable="false"
    :ok-button-props="{ style: { display: 'none' } }"
    cancelText="关闭"
    @cancel="pictureHandleOk"
  >
    <div class="bpmn-box">
      <div class="scale-btns">
        <a-button-group>
          <a-button type="primary" @click="processZoomOut">
            <a-icon type="minus" />
          </a-button>
          <a-button style="width: 80px">{{ Math.floor(defaultZoom * 10 * 10) + '%' }}</a-button>
          <a-button type="primary" @click="processZoomIn">
            <a-icon type="plus" />
          </a-button>
        </a-button-group>
        <a-popover placement="top" style="margin-left: 10px" trigger="hover">
          <template slot="content">
            <span>复位</span>
          </template>
          <a-button type="default" @click="fitViewport">
            <a-icon type="vertical-align-middle" />
          </a-button>
        </a-popover>
      </div>
      <div class="containers" ref="container">
        <div
          id="bpmnCanvas"
          class="canvas"
          ref="bpmnCanvas"
          @mousewheel.prevent="rollProcessZoom"
          v-bind:style="{ width: 100 * scale + '%', height: 100 * scale + '%' }"
        ></div>
      </div>
    </div>
  </j-modal>
</template>

<script>
import BpmnViewer from 'bpmn-js/lib/Viewer'
import MoveCanvasModule from 'diagram-js/lib/navigation/movecanvas'
import ModelingModule from 'bpmn-js/lib/features/modeling'
export default {
  props: {
    row: {
      type: Object,
      default: {},
    },
    pictureVisible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      bpmnViewer: null,
      scale: 1,
      defaultZoom: 1,
      zoomStep: 0.1,
      ready: false,
    }
  },
  computed: {
    _pictureVisible: {
      get() {
        return this.pictureVisible
      },
      set(v) {
        this.$emit('changePictureVisible', v)
      },
    },
  },
  created() {},
  mounted() {},
  methods: {
    initPic(xml) {
      this.defaultZoom = 1
      let _this = this
      this.bpmnViewer && this.bpmnViewer.destroy()
      this.bpmnViewer = new BpmnViewer({
        container: document.getElementById('bpmnCanvas'),
        width: '100%',
        height: '100%',
        additionalModules: [
          MoveCanvasModule, // 移动整个画布
          ModelingModule,
        ],
      })
      let timer = setTimeout(() => {
        this.bpmnViewer.importXML(xml, function (err) {
          if (err) {
            console.error(err)
          } else {
            const canvas = _this.bpmnViewer.get('canvas')
            canvas.zoom(_this.defaultZoom)
            canvas.zoom('fit-viewport', 'auto')
          }
          clearTimeout(timer);
          timer = null;
        })
      }, 100)
    },
    pictureHandleOk() {
      this._pictureVisible = false
      this.ready = false
    },
    rollProcessZoom(event) {
      /* event.wheelDelta 获取滚轮滚动值并将滚动值叠加给缩放比zoom wheelDelta统一为±120，其中正数表示为向上滚动，负数表示向下滚动 */
      let zoom = 0
      zoom += event.wheelDelta / 12
      let newZoom = this.defaultZoom * 100 + zoom
      if (newZoom < 20) {
        newZoom = 20
      } else if (newZoom > 400) {
        newZoom = 400
      }
      this.defaultZoom = newZoom / 100
      this.bpmnViewer.get('canvas').zoom(this.defaultZoom)
    },
    processZoomIn() {
      let newZoom = Math.floor(this.defaultZoom * 100 + this.zoomStep * 100) / 100
      if (newZoom > 4) {
        return
        // throw new Error('不能大于4')
      }
      this.defaultZoom = newZoom
      this.bpmnViewer.get('canvas').zoom(this.defaultZoom)
    },
    processZoomOut() {
      let newZoom = Math.floor(this.defaultZoom * 100 - this.zoomStep * 100) / 100
      if (newZoom < 0.2) {
        return
        // throw new Error('不能小于0.2')
      }
      this.defaultZoom = newZoom
      this.bpmnViewer.get('canvas').zoom(this.defaultZoom)
    },
    // 让图能自适应屏幕
    fitViewport() {
      this.defaultZoom = 1
      this.zoom = this.bpmnViewer.get('canvas').zoom('fit-viewport', 'auto')
    },
    processReZoom() {
      this.defaultZoom = 1
      this.bpmnViewer.get('canvas').zoom('fit-viewport', 'auto')
    },
  },
}
</script>

<style lang="less" scoped>
@import '~@assets/less/YQNormalModal.less';
.bpmn-box {
  height: 60vh;
}
.scale-btns {
  margin-bottom: 12px;
}

.containers {
  background-color: #ffffff;
  width: 100%;
  //height: 50vh;
  //非全屏模式下，流程图容器containers的高度设置如下
  height: calc(100% - 44px);

  .canvas {
    width: 100%;
    height: 100%;
  }
}

//在全屏模式下，实时流程图容器containers的高度样式如下；
.j-modal-box.fullscreen {
  ::v-deep .containers {
    height: calc(100vh - 56px - 24px - 32px - 12px - 24px - 56px) !important;
  }

  .tipBox {
    width: 300px;
    background: #fff;
    border-radius: 4px;
    border: 1px solid #ebeef5;
    padding: 12px;

    .ant-popover-arrow {
      display: none;
    }

    p {
      line-height: 28px;
      margin: 0;
      padding: 0;
    }
  }
}
/deep/ .bjs-powered-by {
  display: none;
}
</style>
