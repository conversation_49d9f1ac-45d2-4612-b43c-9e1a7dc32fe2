<template>
  <div style="height:100%;overflow: hidden;overflow-y: auto">
    <keep-alive exclude='supportingMaterials'>
      <component :is="pageName" style="height:100%" :data="data"/>
    </keep-alive>
  </div>
</template>
<script>
import materialsList from './materialsList'
import supportingMaterials from './supportingMaterials.vue'
export default {
  name: "materialsManagement",
  data() {
    return {
      isActive: 0,
      data:{}
    };
  },
  components: {
    materialsList,
    supportingMaterials
  },
  created(){
    this.pButton1(0);
  },
  //使用计算属性
  computed: {
    pageName() {
      switch (this.isActive) {
        case 0:
          return "materialsList";
          break;
        default:
          return "supportingMaterials";
          break;
      }
    }
  },
  methods: {
    pButton1(index) {
      this.isActive = index;
    },
    pButton2(index,item) {
      this.isActive = index;
      this.data = item;
    }
  }
}
</script>