<template>
  <div class="days-box">
    <div class="operating-title" v-if='label'>{{label}}：</div>
    <div class="box-item">
      <div
          :class="{
		      'number-item': !isNaN(item),
		      'mark-item': isNaN(item),
		    }"
          v-for="(item, index) in runningDays"
          :key="index"
      >
		    <span v-if="!isNaN(item)">
		      <i ref="numberItem">0123456789</i>
		    </span>
        <span class="comma" v-else>{{ item }}</span>
      </div>
    </div>
    <div class="operating-title" v-if='unit'> {{ unit }}</div>
  </div>
</template>
<script>
export default {
  name: 'DaysBox',
  props: {
    days: {
      type: Number,
      default: 0,
    },
    label: {
      type: String,
      default: '稳定运行',
    },
    unit: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      runningDays: [],
    }
  },
  created() {
    this.toRunningNum(this.days)
  },
  mounted () {
    // // 获取当前日期
    // var today = new Date()
    // // 设置起始日期
    // var startDate = new Date("2023-04-24")
    // // 计算天数差
    // var timeDiff = Math.abs(today.getTime() - startDate.getTime())
    // var diffDays = Math.ceil(timeDiff / (1000 * 3600 * 24))
    this.$nextTick(() => {
      this.setNumberTransform()
    })
  },
  methods: {
    // 设置文字滚动
    setNumberTransform () {
      const numberItems = this.$refs.numberItem // 拿到数字的ref，计算元素数量
      const numberArr = this.runningDays.filter(item => !isNaN(item))
      // 结合CSS 对数字字符进行滚动,显示订单数量
      for (let index = 0; index < numberItems.length; index++) {
        const elem = numberItems[index]
        elem.style.transform = `translate(-50%, -${numberArr[index] * 10}%)`
      }
    },
    // 处理数字
    toRunningNum (num) {
      num = num.toString()
      this.runningDays= num.split('') // 将其便变成数据，渲染至滚动数组
    },
  }
}
</script>
<style lang="less" scoped>
.days-box{
  display: flex;
  align-items: center;
  height: 60px;
}
/*滚动数字设置*/
.operating-title {
  color: rgba(237, 245, 255, 0.95);
  font-size: 24px;
  //margin-bottom: 10px;
}
.box-item {
  position: relative;
  height: 60px;
  font-size: 54px;
  line-height: 41px;
  text-align: center;
  list-style: none;
  // color: #2d7cff;
  color: #fff;
  writing-mode: vertical-lr;
  text-orientation: upright;
  /*文字禁止编辑*/
  -moz-user-select: none; /*火狐*/
  -webkit-user-select: none; /*webkit浏览器*/
  -ms-user-select: none; /*IE10*/
  -khtml-user-select: none; /*早期浏览器*/
  user-select: none;
  /* overflow: hidden; */
}

/* 默认逗号设置 */
.mark-item {
  width: 10px;
  height: 100px;
  margin-right: 5px;
  line-height: 10px;
  font-size: 48px;
  position: relative;
  & > span {
    position: absolute;
    width: 100%;
    bottom: 0;
    writing-mode: vertical-rl;
    text-orientation: upright;
  }
}

/*滚动数字设置*/
.number-item {
  width: 41px;
  height: 60px;
  background: #ccc;
  list-style: none;
  margin-right: 5px;
  background-color: rgba(29, 78, 140, 0.5);
  border-radius: 4px;
  border: 1px solid rgba(29, 78, 140, 0.5);
  & > span {
    position: relative;
    display: inline-block;
    margin-right: 10px;
    width: 100%;
    height: 100%;
    writing-mode: vertical-rl;
    text-orientation: upright;
    overflow: hidden;
    & > i {
      font-style: normal;
      position: absolute;
      top: -8px;
      left: 50%;
      transform: translate(-50%, 0);
      transition: transform 1s ease-in-out;
      letter-spacing: 10px;
    }
  }
}
</style>
