<template>
  <j-modal
    :title='title'
    :width='width'
    :centered='true'
    :maskClosable='false'
    :visible='visible'
    :destroyOnClose='true'
    switchFullscreen
    cancelText='关闭'
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @ok='handleOk'
    @cancel='handleCancel'
  >
    <a-spin :spinning='confirmLoading'>
      <j-form-container>
        <a-form-model ref='form' :model='authorizationForm' :rules='validatorRules' slot='detail'>
          <a-row justify='space-between' type='flex'>
            <a-col :span='24'>
              <div class='colorBox'>
                <span class='colorTotal'>基本信息</span>
              </div>
            </a-col>
            <a-col :span='24'>
              <a-form-model-item label='名称' prop='licenseName' v-bind='formItemLayout1'>
                <a-input
                  v-model='authorizationForm.licenseName'
                  :disabled='authorizationForm.id && authorizationForm.licenseName === "local"'
                  :allow-clear='true'
                  autocomplete='off'
                  placeholder='请输入名称' />
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
              <a-form-model-item label='访问地址' prop='licenseAddress' v-bind='formItemLayout1'>
                <a-input
                  v-model='authorizationForm.licenseAddress'
                  :disabled='authorizationForm.id && authorizationForm.licenseName === "local"'
                  :allow-clear='true'
                  autocomplete='off'
                  placeholder='请填写访问地址，如：http://***********:8091' />
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
              <div class='colorBox'>
                <span class='colorTotal'>告警规则</span>
              </div>
            </a-col>
            <a-col :span='12'>
              <a-form-model-item label='开启告警' v-bind='formItemLayout'>
                <a-switch checked-children='开' un-checked-children='关' v-model='authorizationForm.switchStatus' />
              </a-form-model-item>
            </a-col>
            <a-col :span='12'>
              <a-form-model-item label='剩余授权时间小于' v-bind='formItemLayout' prop='days'>
                <a-input :disabled='!authorizationForm.switchStatus' type='number' v-model='authorizationForm.days'>
                  <div slot='addonAfter' style='width: 38px'>
                    天
                  </div>
                </a-input>
              </a-form-model-item>
            </a-col>
            <a-col :span='12'>
              <a-form-model-item label='剩余授权设备数量小于' v-bind='formItemLayout2' prop='devNum'>
                <a-input :disabled='!authorizationForm.switchStatus' type='number' v-model='authorizationForm.devNum'>
                  <a-select :disabled='!authorizationForm.switchStatus' slot='addonAfter' v-model='authorizationForm.devUnit' style='width: 60px'>
                    <a-select-option value='个'>
                      个
                    </a-select-option>
                    <a-select-option value='%'>
                      %
                    </a-select-option>
                  </a-select>
                </a-input>
              </a-form-model-item>
            </a-col>
            <a-col :span='12'>
              <a-form-model-item label='cron表达式' prop='taskCron' v-bind='formItemLayout'>
                <j-cron :disabled='!authorizationForm.switchStatus' ref='innerVueCron' v-model='authorizationForm.taskCron'
                        @change='setCorn'></j-cron>
              </a-form-model-item>
            </a-col>

          </a-row>
        </a-form-model>
      </j-form-container>
    </a-spin>
  </j-modal>
</template>
<script>
import { getAction, httpAction } from '@api/manage'
import { ajaxGetDictItems, getDictItemsFromCache } from '@api/api'
const validateIPadress = (rule, value, callback) => {
  let reg = /^(https?:\/\/)((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)(:\d{1,5})?$/
  if (!reg.test(value)) {
    callback('请输入正确IP地址！')
  } else {
    callback()
  }
}
export default {
  name: 'AuthorizationEditorModal',
  data() {
    const ValidateInputNum = (rule, value, callback) => {
      if (value < 0) {
        callback('最小值为0')
      } else {
        callback()
      }
    }
    return {
      title: '说明',
      width: '800px',
      disableSubmit: false,
      visible: false,
      confirmLoading: false,
      formItemLayout: {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 9 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        }
      },
      formItemLayout1: {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 4 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        }
      },
      formItemLayout2: {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 11 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 13 }
        }
      },
      authorizationForm: {
        licenseName: '',
        licenseAddress: '',
        taskStatus: '-1',
        taskCron: '0 0 0 * * ? *',
        devNum: 0,
        devUnit: '个',
        days: 0,
        switchStatus: false
      },
      validatorRules: {
        licenseName: [
          { required: true, message: '请输入名称' },
          { min: 1, max: 20, message: '名称长度应在【1-20】个字符之间' }
        ],
        licenseAddress: [
          { required: true, message: '请输入访问地址，如：http://***********:8080' }
        ],
        taskCron: [
          { required: true, message: 'cron表达式不能为空' }
        ],
        days: [{ required: true, message: `请输入授权时间` }, { validator: ValidateInputNum, trigger: 'blur' }],
        devNum: [{ required: true, message: `请输入设备数量` }, { validator: ValidateInputNum, trigger: 'blur' }]
      },
      url: {
        add: '/license/add',
        edit: '/license/edit',
      },
      target:{},
    }
  },
  created() {
  },
  methods: {
    add() {
      this.edit({
        licenseName: '',
        licenseAddress: '',
        taskStatus: '-1',
        taskCron: '0 0 0 * * ? *',
        devNum: 0,
        devUnit: '个',
        days: 0,
        switchStatus: false
      })
    },
    edit(record) {
      this.visible = true
      this.$nextTick(() => {
        this.authorizationForm = JSON.parse(JSON.stringify(record))
        if (record.id) {
          // console.log('要编辑的的数据 === ', record)
          if(record.licenseName === "local"){
            this.validatorRules.licenseAddress = [
              { required: true, message: '请输入IP地址' }
            ]
          }
          else{
            this.validatorRules.licenseAddress = [
              { required: true, message: '请输入IP地址' },
              { validator: validateIPadress, trigger: 'blur' }
            ]
          }
          if (record.taskStatus === '-1') {
            this.$set(this.authorizationForm,"switchStatus",false)
          } else {
            this.$set(this.authorizationForm,"switchStatus",true)
          }
          let alarmRule = JSON.parse(record.alarmRule)
          this.$set(this.authorizationForm,"days",Number(alarmRule.days.bound))
          this.$set(this.authorizationForm,"devNum",Number(alarmRule.devNum.bound))
          this.$set(this.authorizationForm,"devUnit",alarmRule.devNum.unit)
        }
      })
    },
    close() {
      this.visible = false
    },
    handleOk() {
      let that = this
      that.$refs.form.validate((err, values) => {
        if (err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!that.authorizationForm.id) {
            httpurl += that.url.add
            method = 'post'
          } else {
            httpurl += that.url.edit
            method = 'put'
          }
          let formData = Object.assign({}, this.authorizationForm)
          formData.taskStatus = this.authorizationForm.switchStatus?"0":"-1"
          formData.alarmRule = JSON.stringify({
            'days': {
              'title': '授权时间',
              'bound': this.authorizationForm.days,
              'unit': '天'
            },
            'devNum': {
              'title': '设备数量',
              'bound': this.authorizationForm.devNum,
              'unit':  this.authorizationForm.devUnit,
            }
          })
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok',formData)
                this.close()
              } else {
                that.$message.warning(res.message)
              }
              this.confirmLoading = false
            }).catch((err) => {
            this.confirmLoading = false
            this.$message.error(err.message)
            this.close()
          })
        }
      })
    },
    handleCancel() {
      this.close()
    },
    setCorn(data) {
      if (data && data.target != null) {
        let dataList = data.target.value.split(' ')
        if (dataList[0] == '*') {
          this.$message.warning('请确认是否每秒都执行')
        }
      } else {
        let dataList = data.split(' ')
        if (dataList[0] == '*') {
          this.$message.warning('请确认是否每秒都执行')
        }
      }
      this.$nextTick(() => {
        this.authorizationForm.taskCron = data
      })

      if (Object.keys(data).length == 0) {
        this.$message.warning('请输入cron表达式!')
      }
    }
  }
}
</script>
<style scoped lang='less'>
@import '~@assets/less/normalModal.less';

.colorBox {
  margin-bottom: 18px;
}

.colorTotal {
  padding-left: 7px;
  border-left: 4px solid #1e3674;
}
</style>