<template>
  <div style="background: #fff;border-radius: 3px"
       :style="{padding: showBack?'24px':'0',height:showBack?'100%':'auto' }">
    <div class='header-info'>
      <div v-if="(!showBack&&this.dataSource.length>0)||showBack" class="title-flag-wrapper"
           :style="{marginTop: titleMarginTop,marginBottom:titleMarginBottom}">
        <span class="title-flag" :style="{borderLeftColor:titleBorderLeftColor}">终端列表</span>
        <a-popover title='执行状态说明' placement='right'>
          <template slot='content'>
            <div class="execute-status-box">
              <div v-for="(item,index) in terStatusColorAndIcon" :key="'terStatusColorAndIcon_'+index" :class="{'stateBox':index!==2}">
                <span v-if="index!=2">{{terStatusColorAndIcon[index].title}}：</span>
                <span v-if="index!=2" class="execute-status" :style="{backgroundColor:terStatusColorAndIcon[index].color}">
                   <yq-icon v-if="index!=5" :type="terStatusColorAndIcon[index].icon"></yq-icon>
                   <img  v-else :src="terStatusColorAndIcon[index].img" alt="" style="width: 16px; height: 16px;margin-bottom: 4px" />
                </span>
              </div>
            </div>
          </template>
          <a-icon type='question-circle' theme='twoTone' style='font-size:20px;margin-left:10px;' />
        </a-popover>
      </div>

      <span  v-if="showBack" class="header-back" @click='getGo'>
        <img src='~@assets/return1.png' alt='' />
      </span>
    </div>
    <a-table
      v-if="showBack||(!showBack&&this.dataSource.length>0)"
      ref="table"
      bordered
      :rowKey="(record,index)=>index"
      :columns="columns"
      :dataSource="dataSource"
      :pagination="ipagination"
      :loading="loading"
      class="j-table-force-nowrap"
      @change="handleTableChange"
    >
      <div slot='executeStatus' slot-scope='text, record'>
        <div v-if="record.executeStatus=='-1'" class="execute-status-box">
          <span class="execute-status"  :style="{backgroundColor:record.color}" :key="record.img" :title="text">
             <img  :src="record.img" alt="" style="width: 16px; height: 16px;margin-bottom: 4px" />
          </span>
          <span>{{ text }}</span>
        </div>
        <div v-else="record.executeStatus>-1" class="execute-status-box">
          <span class="execute-status" :style="{backgroundColor:record.color}" :key="record.icon" :title="text">
             <yq-icon :type="record.icon"></yq-icon>
          </span>
          <span v-if="record.executeStatus=='0'">—</span>
          <span v-else-if="record.executeStatus=='1'">{{ record.startTime }} —</span>
          <span v-else-if="record.executeStatus=='3'">{{ record.startTime }} — {{ record.endTime }}</span>
          <span v-else-if="record.executeStatus=='4'">{{ record.startTime }} — {{ record.endTime }}</span>
        </div>
      </div>

      <span class='caozuo' slot='terminalName' slot-scope='text, record'>
        <a @click='handleViewTerminalInfo(record)'>{{ record.terminalName }}</a>
      </span>

      <span class='caozuo' slot='action' slot-scope='text, record'>
        <a @click='handleDetailPage(record)'>查看</a>
      </span>
      <template slot="tooltip" slot-scope="text">
        <a-tooltip placement="topLeft" :title="text" trigger="hover">
          <div class="tooltip">
            {{ text }}
          </div>
        </a-tooltip>
      </template>
    </a-table>
    <terminal-info-modal ref="terminalInfoModal"></terminal-info-modal>
    <task-terminal-details-modal ref="taskTerminalDetailsModal"></task-terminal-details-modal>
  </div>
</template>

<script>
import yqIcon from '@comp/tools/SvgIcon'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import terminalInfoModal from '@views/opmg/taskManagement/modules/terminalInfoModal.vue'
import taskTerminalDetailsModal from '@views/opmg/taskManagement/modules/terminalDetailsModal.vue'
import { terStatusColorAndIcon } from '@views/opmg/taskManagement/modules/comFunctions'
import { getAction } from '@api/manage'

export default {
  name: 'terminalList',
  props: {
    titleMarginTop: {
      type: String,
      required: false,
      default: '20px'
    },
    titleMarginBottom: {
      type: String,
      required: false,
      default: '0'
    },
    titleBorderLeftColor: {
      type: String,
      required: false,
      default: '#1e3674'
    },
    /**
     * 终端列表右上角是否显示返回上一级箭头
     * 任务列表--查看--终端列表--不显示，任务列表--查看终端--显示
     * */
    showBack: {
      type: Boolean,
      default: false,
      required: false
    },
    taskInfo: {
      type: Object,
      required: false,
      default: () => {
        return {}
      }
    }
  },
  mixins: [JeecgListMixin],
  components: { yqIcon, terminalInfoModal, taskTerminalDetailsModal },
  data() {
    return {
      disableMixinCreated: true,
      terStatusColorAndIcon:terStatusColorAndIcon,
      columns: [
        {
          title: '序号',
          dataIndex: '',
          fixed: 'left',
          disabled: false,
          key: 'rowIndex',
          width: 60,
          customRender: function(t, r, index) {
            return parseInt(index) + 1
          }
        },
        {
          title: '终端名称',
          dataIndex: 'terminalName',
          scopedSlots: {
            customRender: 'terminalName'
          }
        },
        {
          title: '操作系统',
          dataIndex: 'osType_dictText'
        },
        {
          title: 'cpu类型',
          dataIndex: 'cpuType_dictText'
        },
        {
          title: 'cpu架构',
          dataIndex: 'cpuArch'
        },
        {
          title: '所属单位',
          dataIndex: 'deptName'
        },
        {
          title: 'IP地址',
          dataIndex: 'ip'
        },
        {
          title: '任务执行截止时间',
          dataIndex: 'executeStatus_dictText',
          customCell: () => {
            let cellStyle = 'text-align: left;width:345px'
            return { style: cellStyle }
          },
          scopedSlots: {
            // filterDropdown: 'filterDropdown',
            // filterIcon: 'filterIcon',
            customRender: 'executeStatus'
          }
        },
        /* {
           title: '任务执行时间',
           dataIndex: 'startTime'
         },
         {
           title: '任务结束时间',
           dataIndex: 'endTime'
         },*/
        {
          title: '操作',
          dataIndex: 'action',
          fixed: 'right',
          align: 'center',
          width: 100,
          disabled: false,
          scopedSlots: {
            // filterDropdown: 'filterDropdown',
            // filterIcon: 'filterIcon',
            customRender: 'action'
          }
        }
      ],
      url: {
        list: '/software/softwareTask/getTerminal',
        getTerminalInfo: '/terminal/terminalDevice/getTerminalInfo'
      }
    }
  },
  watch: {
    taskInfo: {
      handler(val) {
        this.init(val)
      },
      deep: true,
      immediate: true
    }
  },
  created() {

  },
  methods: {
    init(info) {
      if (info.id) {
        this.queryParam.taskId = info.id
        this.loadData(1)
      } else {
        this.dataSource = []
        this.ipagination.current = 1
        this.ipagination.total = 0
      }
    },
    //返回上一级
    getGo() {
      this.$parent.pButton1(0)
    },
    loadData(arg) {
      if (!this.url.list) {
        this.$message.error('请设置url.list属性!')
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1
      }

      var params = this.getQueryParams() //查询条件
      this.loading = true
      getAction(this.url.list, params).then((res) => {
        if (res.success && res.result) {
          this.dataSource = res.result.records || res.result
          if (this.dataSource.length < 9) {
            this.clientHeight = false
          }
          if (this.dataSource.length > 0) {
            this.dataSource.map((r, index) => {
              let indx = parseInt(r.executeStatus)
              indx=indx == -1 ? 5 : indx
              console.log('indx===',indx)
              let t = this.terStatusColorAndIcon[indx]
              console.log('t===',t)
              r['icon'] =t.icon
              r['img'] = t.img
              r['color'] = t.color
            })
          }
          this.ipagination.total = res.result.total ? res.result.total : 0
        }
        if (res.code === 510) {
          this.$message.warning(res.message)
        }
        this.loading = false
      })
    },
    handleViewTerminalInfo(record) {
      let params = { uniqueCode: record.uniqueCode }
      getAction(this.url.getTerminalInfo, params).then((res) => {
        if (res.success && res.result.records && res.result.records.length > 0) {
          this.$refs.terminalInfoModal.edit(res.result.records[0])
        } else {
          this.$message.warning('操作失败')
        }
      }).catch((err) => {
        this.$message.error(err.message)
      })
    },
    handleDetailPage(record) {
      this.$refs.taskTerminalDetailsModal.edit(record)
    }
  }
}
</script>

<style scoped lang='less'>
@import '~@assets/less/common.less';

.header-info {
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
  align-items: center;
  //text-align: right;
  margin-bottom: 16px;

  .header-back {
    img {
      width: 20px;
      height: 20px;
      cursor: pointer
    }
  }
}

.title-flag-wrapper {
  display: flex;
  flex-flow: row nowrap;
  justify-content: start;
  align-items: center;

  .title-flag {
    padding-left: 7px;
    font-size: 14px;
    border-left-width: 4px;
    border-left-style: solid;
    margin-right: 12px;
  }
}
.stateBox {
  margin-left: 10px;
}
.execute-status-box{
  display: flex;
  flex-flow: row nowrap;
  justify-content: start;
  align-items: center;
  .execute-status {
    padding: 2px 4px;
    color: #fff;
    border-radius: 3px;
    margin-right: 12px;
    font-size: 16px;
  }
}

</style>
