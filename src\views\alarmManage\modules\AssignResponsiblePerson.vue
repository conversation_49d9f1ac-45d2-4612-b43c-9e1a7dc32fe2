<template>
  <j-modal
    :title='title'
    :width='width'
    :centered='true'
    :visible='visible'
    :destroyOnClose='true'
    switchFullscreen
    cancelText='关闭'
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @ok='handleOk'
    @cancel='handleCancel'
  >
    <a-spin :spinning='confirmLoading'>
      <j-form-container :disabled='disableSubmit'>
        <a-form-model ref='form' :model='model' :rules='validatorRules' slot='detail' v-bind='formItemLayout'>
          <a-row>
            <a-col :span='24'>
              <a-form-model-item label='责任人' prop='userName'>
                <a-select v-model='model.userName' :allow-clear='true' placeholder='请选择责任人'>
                  <a-select-option v-for='item in userNameList' :key='item.userName' :label='item.realName'
                                   :value='item.userName'>
                    {{ item.realName }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </j-form-container>
    </a-spin>
  </j-modal>
</template>
<script>
import { getAction, httpAction } from '@api/manage'
export default {
  name: 'AssignResponsiblePerson',
  props: {
    userName: {
      type: String,
      required: false,
      default: ''
    }
  },
  data() {
    return {
      title: '新增',
      width: '600px',
      disableSubmit: false,
      visible: false,
      confirmLoading: false,
      formItemLayout: {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 24 },
          md: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 24 },
          md: { span: 16 }
        }
      },
      model: {
        userName: '',
      },
      userNameList: [],
      validatorRules: {
        userName: [
          { required: true, message: '请选择责任人' }
        ]
      },
      url: {
        user:'/alarm/alarmHistory//getDeviceUsersById',//单条指定或转移责任人时，加载指定人下拉数据
        userBatch:'/alarm/alarmHistory/getDeviceUsersByIds',//批量指定或转移责任人时，加载指定人下拉数据

        httpurl: '',
        transfer:'/alarm/alarmHistory/transfer',//转移责任人
        transferBatch:'/alarm/alarmHistory/transferBatch',//批量转移责任人
        assign: '/alarm/alarmHistory/assign',//指定责任人
        assignBatch: '/alarm/alarmHistory/assignBatch',//批量指定责任人
      }
    }
  },

  methods: {
    filterUserName(lis){
      let tempList=lis
      if (this.userName&&lis.length>0){
        tempList =lis.filter((item)=>{
          return item.userName!==this.userName
        })
      }
      return tempList
    },
    /**根据所选单条告警信息的设备id，加载用户*/
    assign(record,flag="assign") {
      this.confirmLoading=true
      delete this.model.ids
      this.userNameList=[]
      getAction(this.url.user,{id:record.deviceId}).then((res)=>{
        if (res.success) {
          this.userNameList=this.filterUserName(res.result)
          this.url.httpurl =flag==="assign"? this.url.assign:this.url.transfer
          this.edit({id:record.id})
        } else {
          this.$message.warning(res.message)
        }
        this.confirmLoading=false
      }).catch((err) => {
        this.$message.warning(err.message)
        this.confirmLoading=false
      })
    },
    /**根据批量所选告警信息id，加载用户*/
    batchAssign(keys,flag="assignBatch") {
      this.confirmLoading=true
      delete this.model.id
      this.userNameList=[]
      let ids=keys.length > 0 ? keys.join(',') : ''
      getAction(this.url.userBatch,{ids:ids}).then((res)=>{
        if (res.success) {
          this.userNameList=this.filterUserName(res.result)
          this.url.httpurl =flag==="assignBatch"? this.url.assignBatch:this.url.transferBatch
          this.edit({ids:ids})
        } else {
          this.$message.warning(res.message)
        }
        this.confirmLoading=false
      }).catch((err) => {
        this.$message.warning(err.message)
        this.confirmLoading=false
      })
    },
    edit(record) {
      this.visible = true
      this.model = JSON.parse(JSON.stringify(record))
    },
    close() {
      this.visible = false
    },
    handleOk() {
      let that = this
      that.$refs.form.validate((err, values) => {
        if (err && !that.confirmLoading) {
          that.confirmLoading = true
          let httpurl = that.url.httpurl
          let method = 'get'
          let formData = JSON.parse(JSON.stringify(that.model))
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
                that.close()
              } else {
                that.$message.warning(res.message)
              }
              that.confirmLoading = false
            }).catch((err) => {
            that.$message.warning(err.message)
            that.confirmLoading = false
          })
        }
      })
    },
    handleCancel() {
      this.model = {}
      this.close()
    }
  }
}
</script>
<style scoped lang='less'>
@import '~@assets/less/normalModal.less';
</style>