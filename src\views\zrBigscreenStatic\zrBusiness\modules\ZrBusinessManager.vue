<template>
<div class='zr-business-manager' ref='zrBusinessManager'>
  <zr-bigscreen-title title='管理人员'></zr-bigscreen-title>
  <div class='business-support-content'>
    <slot>
      <vue-seamless-scroll v-if='srollOption.autoPlay' :data='listData' class='scroll-warp' :class-option='srollOption'>
        <ul class='scroll-list'>
          <li class='scroll-list-item' :class='{"scroll-list-odd":index%2!==0}' v-for='(item, index) in listData'
              :key='index'>
            <div class='item-name' :title='item.realName' :style='{"--status-color":item.color}' >
              {{item.realName}}
            </div>
            <div class='item-department' :title='item.department'>{{item.department}}</div>
            <div class='item-status'>{{item.phone}}</div>
          </li>
        </ul>
      </vue-seamless-scroll>
      <div v-else class='list-warp'>
        <ul  class='list-block'>
          <li class='list-item' :class='{"scroll-list-odd":index%2!==0}' v-for='(item, index) in listData'
              :key='index'>
            <div class='item-name' :title='item.realName' :style='{"--status-color":item.color}' >
              {{item.realName}}
            </div>
            <div class='item-department' :title='item.department'>{{item.department}}</div>
            <div class='item-status' >{{item.phone}}</div>
          </li>
        </ul>
      </div>
    </slot>
  </div>
</div>
</template>
<script>
import ZrBigscreenTitle from '@views/zrBigscreens/modules/ZrBigscreenTitle.vue'
import vueSeamlessScroll from 'vue-seamless-scroll'
import resizeObserverMixin from '@views/statsCenter/com/resizeObserverMixin'
import {businessStatus,managerList} from "@views/zrBigscreens/modules/zrUtil"
export default {
  name: 'ZrBusinessManager',
  components: { ZrBigscreenTitle, vueSeamlessScroll },
  mixins: [resizeObserverMixin],
  data() {
    return {
      listData: managerList,
      srollOption: {
        step: 0.5, // 步长
        speed: 100, // 滚动速度
        timer: 3000,// 滚动时间间隔
        autoPlay: false,
        limitMoveNum:100000,
        singleHeight: 36 ,
      },
      maxNum: 0,
    }
  },
  created() {
  },
  mounted() {
    this.$nextTick(()=>{
      this.setPlayState()
    })
  },
  methods: {
    // 屏幕变化回调
    resizeObserverCb(){
      this.setPlayState()
    },
    //设置滚动状态
    setPlayState(){
      if(this.$refs.zrBusinessManager && this.listData.length){
        this.maxNum = 3
        if(this.maxNum>0 && this.maxNum<this.listData.length){
          this.srollOption.limitMoveNum = this.maxNum
          this.srollOption.autoPlay =true;
          return ;
        }
      }

      this.srollOption.autoPlay =false;
      this.srollOption.limitMoveNum = this.listData.length+1
    }
  }
}
</script>



<style scoped lang='less'>
.zr-business-manager{
  height: 100%;
  .business-support-content {
    height: calc(100% - 51px);
    overflow: hidden;
    padding: 0 12px;
    background: linear-gradient(to right, rgba(29, 78, 140, 0.3), rgba(29, 78, 140, 0.0));
  }
}
.scroll-warp {
  height: 108px;
  overflow: hidden;
  max-width:410px;
  .scroll-list {
    width: 100%;
    height: 100%;
    margin: 0px;
    padding: 0px;
  }

  .scroll-list-item {
    display: flex;
    align-items: center;
    color: rgba(237, 245, 255, 0.95);
    font-size: 14px;
    justify-content: space-between;
    height: 36px;

    .item-name {
      width: 25%;
      overflow: hidden;
      position: relative;
      line-height: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .item-department {
      width: 40%;
      text-align: center;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .item-status {
      width: 35%;
      text-align: center;
      overflow: hidden;
    }
  }

  .scroll-list-odd {
    background: rgba(29, 78, 140, 0.25);
  }
}
.list-warp {
  height: 108px;
  overflow: hidden;
  .list-block {
    width: 100%;
    height: 100%;
    margin: 0px;
    padding: 0px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  .list-item {
    width: 100%;
    display: flex;
    align-items: center;
    color: rgba(237, 245, 255, 0.95);
    font-size: 14px;
    justify-content: space-between;
    height: 36px;
    padding:0 calc(25 / 19.2 * 1vw);
    .item-name {
      width: 25%;
      overflow: hidden;
      position: relative;
      line-height: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .item-department {
      width: 40%;
      text-align: center;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .item-status {
      width: 35%;
      text-align: center;
      overflow: hidden;
    }
  }
}

</style>