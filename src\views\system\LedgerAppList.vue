<template>
  <a-card :bordered="false">
    <!--流程申请选择-->
    <a-input-search
      style="margin-bottom: 10px;margin-right:10px;width: 200px"
      v-model="searchProcessKey"
      placeholder="输入应用名称"
      @search="onSearchProcess" />
    <a-button @click="onSearchProcess(searchProcessKey)" type="primary" style="margin-right: 10px">查询</a-button>
    <a-button @click="onSearchProcess('')">重置</a-button>

    <a-button @click="handleAdd" type="primary" icon="plus" style="float: right;margin-right: 10px" >新增</a-button>
    <a-empty description="无应用可供选择" v-if="activeKeyAll.length<1" />
    <div v-else>
      <!-- <a-collapse v-model="activeKey">
          <a-collapse-panel v-for="(value, index)  in activeKeyAll" :header="'未分类'" :key="value"> -->
      <a-list :grid="{ gutter: 10,column:4}" :dataSource="processDataMap">
        <a-list-item slot="renderItem" slot-scope="item">
          <a-card>
            <div slot="title">
              <a-row>
                <a-col span="10" :title="item.appCode">{{ item.appCode }} </a-col>
                <a-col span="14" style="text-align: right;">
                  <a href="javascript:void (0)" @click="handleToApplyList(item)">编辑菜单</a>&nbsp;&nbsp;
                  <a href="javascript:void (0)" @click="batchDel(item)">删除</a>&nbsp;&nbsp;
                  <a href="javascript:void (0)" @click="handleEdit(item)">编辑</a>
                </a-col>
              </a-row>
            </div>
            <b>应用名称：</b>{{ item.appName }}
            <br/>
            <b>应用编码：</b>{{ item.appCode }}
          </a-card>
        </a-list-item>
      </a-list>
      <!-- </a-collapse-panel>
        </a-collapse> -->
    </div>
    <!--流程表单-->
    <ledgerApp-Modal ref="modalForm" @ok="modalFormOk2"></ledgerApp-Modal>
  </a-card>

</template>

<script>
  import JEllipsis from '@/components/jeecg/JEllipsis'
  import JTreeSelect from '@/components/jeecg/JTreeSelect'
  import { deleteAction, getAction, downFile } from '@/api/manage'

  import LedgerAppModal from './modules/LedgerAppModal'
  export default {
    name: "LedgerAppList",
    components: {
       JEllipsis
      ,JTreeSelect,
      LedgerAppModal
    },
    data () {
      return {
        description: '所有',
        dictOptions:[],
        url: {
          getProcessDataList: "/ledgerApp/list",
          delete: "/ledgerApp/delete"
        },
        // 查询条件
        queryParam: {
          createTimeRange:[],
          keyWord:'',
        },
        // 表头
        labelCol: {
          xs: { span: 4 },
          sm: { span: 4 },
        },
        wrapperCol: {
          xs: { span: 20 },
          sm: { span: 20 },
        },
        processModalVisible: null,
        activeKeyAll: [],
        activeKey: [],
        processDataMap: [],
        searchProcessKey: null,
        addApplyLoading: false,
        lcModa: {
          title:'',
          disabled:false,
          visible:false,
          formComponent : null,
          isNew : false
        },
      }
    },
    computed:{
    },
    mounted() {
      this.getProcessList()
    },
    methods: {
      /*加载流程列表*/
      getProcessList() {
        this.addApplyLoading = true;
        getAction(this.url.getProcessDataList,{pageSize:100}).then(res => {
          this.activeKeyAll = [];
          if (res.success) {
            var result = res.result.records||[];
            if (result.length>0){
              let searchProcessKey = this.searchProcessKey;
              if (searchProcessKey){ //过滤条件
                result = _.filter(result, function(o) { return o.appName.indexOf(searchProcessKey)>-1; });
              }
              this.processDataMap = result;//_.groupBy(result,'id');
              for (const id in this.processDataMap) {
                this.activeKeyAll.push(id)
              }
              this.activeKey = this.activeKeyAll;
            }
            this.processModalVisible = true;
          }else {
            this.$message.warning(res.message)
          }
        }).finally(()=>this.addApplyLoading = false);
      },
      /**添加 */
      handleAdd(){
         this.$refs.modalForm.roleDisabled = true
          this.$refs.modalForm.selectedRole = [this.currentRoleId]

          this.$refs.modalForm.add()

          this.$refs.modalForm.title = '新增'
      },
      /**删除 */
      batchDel: function(v) {
          var id = v.id;
          var that = this
          this.$confirm({
            title: '确认删除',
            content: '是否删除选中数据?',
		      	okText: '是',
            cancelText: '否',
            onOk: function() {
              deleteAction(that.url.delete, { id: id }).then((res) => {
                if (res.success) {
                  that.$message.success(res.message)
                  that.getProcessList()
                } else {
                  that.$message.warning(res.message)
                }
              })
            }
          })
      },
       modalFormOk2() {
        // 新增/修改 成功时，重载列表
        this.getProcessList()
      },
      onSearchProcess(value) {
        this.searchProcessKey = value;
        this.getProcessList()
      },
     /**编辑 */
      handleEdit: function(record) {
      this.$refs.modalForm.title = '编辑'
      this.$refs.modalForm.roleDisabled = true
      this.$refs.modalForm.edit(record)
      },

      /*前往菜单页面*/
      handleToApplyList(v) {
		   this.$router.push({
		     path:'/isystem/permissionForApp',
		     query: {
				appCode:v.appCode,
				appName:v.appName
		     },
		   });
      }

    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less';
</style>