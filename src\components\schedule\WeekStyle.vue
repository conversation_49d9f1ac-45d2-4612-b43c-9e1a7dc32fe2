<template>
  <div style='height: 100%;width: 100%;position: relative'>
    <div class="weeks" style='width: calc(100% - 8px)'>
      <span class="week" v-for="dayIndex in 8" :key="dayIndex">
        {{ dayIndex | localeWeekDay }}
      </span>
    </div>
    <div class="c-container" ref='c-container' style='width: 100% !important;'>
      <div class='date' style='height: 100%'>
        <div v-for="(obj, j) in tableData" :key="j" class='row'>
        <div class="c-header">
          <div
            class="c-h-cell"
            v-for="(day, i) in 8"
            :key="i"
            :class="{ today: isToday(new Date()) }"
            @click="addNewEvent(day)"
          >
            <div :key='"row_"+j+"_col_"+i+"_1"' v-if="i===0">{{obj.time}}</div>
            <div :key='"row_"+j+"_col_"+i+"_1"' v-else></div>
            <div :key='"row_"+j+"_col_"+i+"_2"'></div>
          </div>
        </div>
        <div style="position: relative">
          <div
            class="event-container"
            :style="{ top: '-' + obj.bgMinHeight * layHeight + 'px' }"
          >
            <div
              :style="{
                width: 0,
                minHeight: obj.bgMinHeight * layHeight + 'px',
              }"
            ></div>
            <div
              class="event-item"
              v-for="(n, i) in obj.weekEventList"
              :key="i"
              :style="{
                width: n._eLen * layWidth + 'px',
                height: layHeight + 'px',
                left: n._eX * layWidth + 'px',
                top: n._eY * layHeight + 'px',
              }"
            >
              <div
                class="event-content"
                :class="n.className"
                :title="n.data.title"
                :style="{
                  background: n.data.planColor,
                  height: layHeight - 2 + 'px',
                  lineHeight: layHeight - 2 + 'px',
                }"
              >
                <div v-if="n.className.includes('is-start')">
                  <span class="event-text">{{ n.data.title }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      </div>
    </div>
  </div>
</template>
<script>
let $this;
export default {
  name: "MonthStyle",
  props: {
    days: {
      default: () => [],
    },
    currentMonth: {
      default: 1,
    },
    currentYear: {
      default: 1970,
    },
  },
  data() {
    return {
      layWidth: 0,
      layHeight: 65/60,
      tableData: [],
      testWeekEventList:[
        {
          endTime: "2022-09-30 14:35",
          id: "b54d5dcb-1afb-4015-a6d1-dc13565d9e87",
          planColor: "#1890FF",
          startTime: "2022-09-30 00:00",
          title: "测试",
          _eY: 0,

        },
        {
          endTime: "2022-09-31 10:21",
          id: "b54d5dcb-1afb-4015-a6d1-dc13565d9e88",
          planColor: "#1890FA",
          startTime: "2022-09-27 17:35",
          title: "测试111",
          _eY: 0,
        }
      ]
    };
  },
  filters: {
    localeWeekDay(weekday) {
      if($this.days[0]){
        let map = {
          0: "周一",
          1: "周一",
          2: "周二",
          3: "周三",
          4: "周四",
          5: "周五",
          6: "周六",
          7: "周日",
        };
        let temObj = $this.days[0].dayArr
        if (temObj[weekday - 1].dateStr==="时间"){
          return temObj[weekday - 1].dateStr
        }
        let dateStr = temObj[weekday - 1].month+"/"+temObj[weekday - 1].date
        return map[weekday-1]+" "+dateStr;
      }
    },
  },
  created() {
    $this = this;
  },
  mounted() {
    this.getTableData()
    console.log("days=",this.days)

for (let i=0;i<this.testWeekEventList.length;i++){
  let endTime= new Date(this.testWeekEventList[i].endTime)
  let startTime= new Date(this.testWeekEventList[i].startTime)
  let dayCount= parseInt((endTime-startTime) / (1000*60*60*24))//计算到天
  let _eLen= parseInt((endTime-startTime) / ( 1000*60))//计算到分钟
  //let

  //let _eY=
  console.log('endTime==',endTime)
  console.log('startTime==',startTime)
  console.log('endTime-startTime==',endTime-startTime)
  console.log('_eLen==',_eLen)
}


  },
  methods: {
    addNewEvent(day){
      if (day!==1){
        console.log(day)
      }
    },
    getTableData() {
      for (let i = 0; i < 24; i++) {
        this.tableData.push({
          id: i,
          time: `${i}时`,
        });
      }
      console.log("获取到数据", this.tableData);
    },
    isOtherMonth(item) {
      // let d = new Date()
      if (item.month === this.currentMonth && item.year === this.currentYear) {
        return false;
      } else {
        return true;
      }
    },
    isToday(item) {
      let d = new Date();
      if (
        item.month === d.getMonth() + 1 &&
        item.date === d.getDate() &&
        item.year === d.getFullYear()
      ) {
        return true;
      } else {
        return false;
      }
    },
  },
};
</script>
<style lang="less" scoped>
ul {
  padding: 0;
  margin: 0;
}
li {
  list-style: none;
}
.event-calender {
  width: 100%;
  margin: 0 auto;
}
.weeks {
  //margin-top: 12px;
  display: flex;
  border: 1px solid #e1e4e7;
  border-left: none;
}
.week {
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  height: 50px;
  border-left: 1px solid #e1e4e7;
  background: rgb(245, 248, 250);
  &:not(:first-child){
    flex: 1;
  }
  &:first-child{
    width: 55px;
  }
}
.other-m {
  color: rgba(51, 71, 91, 0.45);
  background: rgb(245, 245, 245);
}
.today {
  color: red;
  background-color: #fcf8e3;
}
// .other-m-bg{

// }
.event-container {
  width: 100%;
  box-sizing: border-box;
  position: absolute;
  height: 0;
  .event-item {
    box-sizing: border-box;
    padding-top: 2px;
    position: absolute;
    .event-content {
      color: #FFFFFF;
      cursor: pointer;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      word-break: break-all;
      &.is-start {
        margin-left: 1px;
        border-top-left-radius: 12px;
        border-bottom-left-radius: 12px;
      }
      &.is-end {
        border-top-right-radius: 12px;
        border-bottom-right-radius: 12px;
      }
      .event-text {
        padding-left: 5px;
      }
    }
  }
}
.c-container {
  border: none;
  height: calc(100% - 64px);
  border-left: 1px solid #eee;
  border-bottom: 1px solid #eee;
  overflow: hidden;
  overflow-y: scroll;
  //box-sizing: border-box;
  .date{
    margin-right: 0;
    .row{
      .c-header {
        display: flex;
        .c-h-cell {
          height: 65px;
          box-sizing: border-box;
          text-align: center;
          //line-height:2;
          //padding-right: 15px;
          border-top: 1px solid #e1e4e7;
          border-right: 1px solid #e1e4e7;

          &:not(:first-child) {
            flex: 1;
          }

          &:first-child {
            width: 55px;
            padding-right: 0px;
          }

          &:last-child {
            //border-right: none;
          }

          div:first-child {
            height: 33px;
            line-height: 33px;
            border-bottom: 1px solid #e1e4e7;
           // background-color: red;
          }

          div:nth-child(2) {
            height: 32px;
            line-height: 32px;
           //background-color: #0c8fcf;
          }
        }
        .cell-day {
          display: inline-block;
          width: 100%;
          font-size: 16px;
          line-height: 45px;
          // cursor: pointer;
        }
      }
      &:first-child{
        .c-header{
          .c-h-cell{
            border-top: none;
          }
        }
      }
    }
  }
}

.event-bg {
  position: relative;
  display: flex;
  .bg-cell {
    box-sizing: border-box;
    flex: 1;
    border-top: 1px solid #e1e4e7;
    border-right: 1px solid #e1e4e7;
    &:last-child {
      border-right: none;
    }
  }
}
</style>

