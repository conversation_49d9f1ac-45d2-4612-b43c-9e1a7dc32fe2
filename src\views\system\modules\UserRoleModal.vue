<template>
  <a-drawer :title="title" :maskClosable="true" width=650 placement="right" :closable="true" @close="close"
    :visible="visible" style="overflow: auto;padding-bottom: 53px;">

    <div class="table-page-search-wrapper">

      <a-form layout="inline" @keyup.enter.native="loadData" v-bind="formItemLayout">
        <a-row :gutter="24" ref="row">
          <a-col span=14>
            <a-form-item label="平台类型">
              <a-tree-select placeholder="请选择平台类型" :allowClear='true' v-if="sysType !== '1'"
                             :dropdownStyle="{ maxHeight: '500px', overflow: 'auto' }"
                v-model="queryParam1.platformType" :tree-data="platformTypes">
              </a-tree-select>
              <a-select v-else placeholder="请选择平台类型" :allowClear='true' v-model="queryParam1.platformType">
                <a-select-option :value="1">{{platformType}}</a-select-option>
                <a-select-option :value="4">数据中心</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="colBtnsSpan()">
            <span class="table-page-search-submitButtons"
              :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
              <a-button type="primary" class="btn-search btn-search-style" @click="loadData" style="margin-left: 12px">
                查询</a-button>
              <a-button class="btn-reset btn-reset-style" @click="searchReset" style="margin-left: 8px">重置</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <a-form>
      <a-form-item label='所拥有的权限'>
        <a-tree checkable @check="onCheck" :checkedKeys="checkedKeys" :treeData="treeData" @expand="onExpand"
          @select="onTreeNodeSelect" :selectedKeys="selectedKeys" :expandedKeys="expandedKeysss"
          :checkStrictly="checkStrictly">
          <span slot="hasDatarule" slot-scope="{slotTitle,ruleFlag}">
            {{ slotTitle }}<a-icon v-if="ruleFlag" type="align-left" style="margin-left:5px;color: red;"></a-icon>
          </span>
        </a-tree>
      </a-form-item>
    </a-form>

    <div class="drawer-bootom-button">
      <a-dropdown style="float: left" :trigger="['click']" placement="topCenter">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="switchCheckStrictly(1)">父子关联</a-menu-item>
          <a-menu-item key="2" @click="switchCheckStrictly(2)">取消关联</a-menu-item>
          <a-menu-item key="3" @click="checkALL">全部勾选</a-menu-item>
          <a-menu-item key="4" @click="cancelCheckALL">取消全选</a-menu-item>
          <a-menu-item key="5" @click="expandAll">展开所有</a-menu-item>
          <a-menu-item key="6" @click="closeAll">合并所有</a-menu-item>
        </a-menu>
        <a-button>
          树操作
          <a-icon type="up" />
        </a-button>
      </a-dropdown>
      <a-popconfirm title="确定放弃编辑？" @confirm="close" okText="确定" cancelText="取消">
        <a-button style="margin-right: .8rem">取消</a-button>
      </a-popconfirm>
      <a-button @click="handleSubmit(false)" type="primary" :loading="loading" ghost style="margin-right: 0.8rem">仅保存
      </a-button>
      <a-button @click="handleSubmit(true)" type="primary" :loading="loading">保存并关闭</a-button>
    </div>

    <role-datarule-modal ref="datarule"></role-datarule-modal>

  </a-drawer>

</template>
<script>
  import {
    queryTreeListForRole,
    queryRolePermission,
    saveRolePermission
  } from '@/api/api'
  import RoleDataruleModal from './RoleDataruleModal.vue'
  import {
    getAction,
    deleteAction
  } from '@/api/manage'
  import {
    YqFormSearchLocation
  } from '@/mixins/YqFormSearchLocation'
  export default {
    name: "RoleModal",
    mixins: [YqFormSearchLocation],
    components: {
      RoleDataruleModal
    },
    data() {
      return {
        formItemLayout: {
          labelCol: {
            style: 'width:80px',
          },
          wrapperCol: {
            style: 'width:calc(100% - 80px)'
          }
        },
        url: {
          platformTypeUrl: '/sys/permission/platformTypeList',
        },
        platformTypes: [],
        roleId: "",
        treeData: [],
        defaultCheckedKeys: [],
        checkedKeys: [],
        expandedKeysss: [],
        allTreeKeys: [],
        autoExpandParent: true,
        checkStrictly: true,
        title: "角色权限配置",
        visible: false,
        loading: false,
        selectedKeys: [],
        queryParam1: {},
        sysType: window._CONFIG['system_Type'],
        platformType: window._CONFIG['platform_Type'],
      }
    },
    created() {
      this.getSelectTree()
    },
    methods: {
      // 根据接口获取所有的平台类型
      getSelectTree() {
        getAction(this.url.platformTypeUrl).then((res) => {
          this.platformTypes = res.result
        })
      },
      onTreeNodeSelect(id) {
        if (id && id.length > 0) {
          this.selectedKeys = id
        }
        this.$refs.datarule.show(this.selectedKeys[0], this.roleId)
      },
      onCheck(o) {
        if (this.checkStrictly) {
          this.checkedKeys = o.checked;
        } else {
          this.checkedKeys = o
        }
      },
      show(roleId) {
        this.roleId = roleId
        this.visible = true;
      },
      close() {
        this.reset()
        this.$emit('close');
        this.visible = false;
      },
      onExpand(expandedKeys) {
        this.expandedKeysss = expandedKeys;
        this.autoExpandParent = false
      },
      reset() {
        this.expandedKeysss = []
        this.checkedKeys = []
        this.defaultCheckedKeys = []
        this.loading = false
      },
      expandAll() {
        this.expandedKeysss = this.allTreeKeys
      },
      closeAll() {
        this.expandedKeysss = []
      },
      checkALL() {
        let treeDataList = [...this.allTreeKeys, ...this.defaultCheckedKeys]
        this.checkedKeys = Array.from(new Set(treeDataList))
      },
      cancelCheckALL() {
        this.checkedKeys = []
      },
      switchCheckStrictly(v) {
        if (v == 1) {
          this.checkStrictly = false
        } else if (v == 2) {
          this.checkStrictly = true
        }
      },
      handleCancel() {
        this.close()
      },
      searchReset() {
        this.queryParam1 = {}
        this.loadData();
      },
      handleSubmit(exit) {
        let that = this;
        let params = {
          roleId: that.roleId,
          permissionIds: that.checkedKeys.join(","),
          lastpermissionIds: that.defaultCheckedKeys.join(","),
        };
        that.loading = true;
        saveRolePermission(params).then((res) => {
          if (res.success) {
            that.$message.success(res.message);
            that.loading = false;
            if (exit) {
              that.close()
              that.searchReset();
            }
          } else {
            that.$message.error(res.message);
            that.loading = false;
            if (exit) {
              that.close()
              that.searchReset();
            }
          }
          this.loadData();
        })
      },
      loadData() {
        var param = {};
        param.platformType = this.queryParam1.platformType
        param.roleId = this.roleId
        queryTreeListForRole(param).then((res) => {
          this.treeData = res.result.treeList
          this.allTreeKeys = res.result.ids
          queryRolePermission({
            roleId: this.roleId
          }).then((res) => {
            this.checkedKeys = [...res.result];
            this.defaultCheckedKeys = [...res.result];
            this.expandedKeysss = this.allTreeKeys;
          })
        })
      }
    },
    watch: {
      visible() {
        if (this.visible) {
          this.loadData();
        }
      }
    }
  }
</script>
<style lang="less" scoped>
::v-deep .ant-drawer-wrapper-body{
  overflow: hidden !important;
  .ant-drawer-body{
    height: calc(100% - 55px);
    padding:24px 24px 53px !important;

    .ant-form{
      height: calc(100% - 56px) !important;

      > .ant-form-item{
        height: 100%;
        margin-bottom: 0;

        .ant-form-item-control-wrapper{
          height: calc(100% - 40px);
          overflow: auto !important;

        }
      }
    }
  }

}

  .drawer-bootom-button {
    position: absolute;
    bottom: 0;
    width: 100%;
    border-top: 1px solid #e8e8e8;
    padding: 10px 16px;
    text-align: right;
    left: 0;
    background: #fff;
    border-radius: 0 0 2px 2px;
  }

  @media (max-width: 650px) {
    ::v-deep .ant-drawer-content-wrapper {
      max-width: 100vw;
      margin: 0;
    }
  }
</style>