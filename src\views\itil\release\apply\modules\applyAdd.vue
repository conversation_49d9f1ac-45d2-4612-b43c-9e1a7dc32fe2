<template>
  <a-modal
    :title="title"
    :width="modalWidth"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭"
    wrapClassName="ant-modal-cust-warp"
    style="top: 5%; height: 95%; overflow: auto"
  >
    <div style="padding-top: 0px">
      <fm-generate-form :data="startFormJson" ref="generateStartForm" :value="variables"> </fm-generate-form>
      <a-tabs :animated="false" :activeKey="defaultActiveKey" @change="callback">
        <a-tab-pane key="1" tab="附件">
          <div class="clearfix">
            <j-upload v-model="files" :number="5"></j-upload>
          </div>
        </a-tab-pane>
        <a-tab-pane key="2" tab="关联配置项" force-render>
          <a-table
            ref="table"
            size="middle"
            bordered
            rowKey="id"
            :columns="columns"
            :dataSource="dataSource"
            :pagination="ipagination"
            :loading="loading"
            :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
            class="j-table-force-nowrap"
            @change="handleTableChange"
          >
          </a-table>
        </a-tab-pane>
      </a-tabs>
    </div>
  </a-modal>
</template>
<script>
import { getAction, postAction } from '@/api/manage'
import JUpload from '@/components/jeecg/JUpload'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
export default {
  name: 'applyAdd',
  mixins: [JeecgListMixin],
  components: {
    JUpload,
  },
  data() {
    return {
      title: '操作',
      confirmLoading: false,
      /* 弹框宽 */
      modalWidth: '1000px',
      form: this.$form.createForm(this),
      defaultActiveKey: '1',
      visible: false,
      required: false,
      startFormJson: undefined,
      variables: undefined,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      // 上传相关
      previewVisible: false,
      previewImage: '',
      files: '',
      changeId: '', //变更ID
      // 关联配置项
      columns: [
        {
          title: '序号',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function (t, r, index) {
            return parseInt(index) + 1
          },
        },
        {
          title: '分类',
          align: 'center',
          dataIndex: 'configType_dictText',
        },
        {
          title: '编号',
          align: 'center',
          dataIndex: 'code',
        },
        {
          title: '名称',
          align: 'center',
          dataIndex: 'name',
        },
        {
          title: '状态',
          align: 'center',
          dataIndex: 'state_dictText',
        },
      ],
      dataSource: [],
      url: {
        list: '/itilconfigitemlibrary/itilConfigItemLibrary/list',
      },
    }
  },
  methods: {
    initData(v, changeId) {
      setTimeout(() => {
        this.modalWidth = '1000px'
      }, 400)
      this.changeId = changeId
      this.processDefinition = v
      this.defaultActiveKey = '1'
      this.selectedRowKeys = []
      this.files = '' //文件
      if (this.processDefinition.formKey) {
        getAction('/flowableform/umpFlowableForm/queryByKey', {
          key: this.processDefinition.formKey,
          tableId: this.processDefinition.tableId,
        }).then((res) => {
          if (res.success) {
            var formData = res.result
            if (formData && formData.formJson) {
              this.startFormJson = JSON.parse(formData.formJson)
              this.variables = JSON.parse(formData.formValue)
              this.visible = true
            }
          }
        })
      }
      if (null != v.id) {
        getAction('/businessrelation/actZBusinessRelation/list', { processId: this.processDefinition.id }).then(
          (res) => {
            if (res.success) {
              this.selectedRowKeys = res.result.itilConfigIds
              this.files = res.result.fileUrlList
            }
          }
        )
      }
    },
    // 关闭弹框
    close() {
      this.$emit('close')
      this.visible = false
      this.current = 0
    },
    // 提交
    handleOk() {
      if (this.$refs.generateStartForm) {
        if (this.processDefinition.tableId) {
          this.$refs.generateStartForm
            .getData()
            .then((values) => {
              if (values.planBegins > values.planEnd) {
                this.$message.warning('结束时间不能小于开始时间!')
                return
              }
              if (values && values != undefined) {
                let formData = Object.assign(this.data || {}, values)
                formData.procDefId = this.processDefinition.id
                formData.procDeTitle = this.processDefinition.name
                formData.form_value = JSON.stringify(values)
                //Object.assign({processInstanceFormData}, values)
                formData.filedNames = 'form_value' + ',' + 'form_key'
                formData.form_key = this.processDefinition.formKey
                formData.id = this.processDefinition.tableId
                formData.itilConfigIds = this.selectedRowKeys
                let faleUrl = ''
                if (this.files instanceof Array) {
                  for (var i = 0; i < this.files.length; i++) {
                    faleUrl = faleUrl + ',' + this.files[i]
                  }
                } else {
                  faleUrl = this.files
                }

                formData.file = faleUrl
                postAction('/release/editForm', formData).then((res) => {
                  this.uploading = false
                  if (res.success) {
                    this.$message.success('保存成功')
                    this.visible = false
                    this.$emit('ok')
                  } else {
                    this.$message.warning(res.message)
                    this.visible = false
                    this.$emit('ok')
                  }
                })
              }
            })
            .catch((e) => {})
        } else {
          this.$refs.generateStartForm
            .getData()
            .then((values) => {
              if (values.planBegins > values.planEnd) {
                this.$message.warning('结束时间不能小于开始时间!')
                return
              }
              if (values && values != undefined) {
                let formData = Object.assign(this.data || {}, values)
                formData.form_value = JSON.stringify(values)
                //Object.assign({processInstanceFormData}, values)
                formData.filedNames = 'form_value' + ',' + 'form_key'
                formData.form_key = this.processDefinition.formKey
                formData.itilConfigIds = this.selectedRowKeys
                formData.file = this.files
                formData.changeId = this.changeId
                postAction('/release/add', formData).then((res) => {
                  this.uploading = false
                  if (res.success) {
                    this.$message.success('保存成功')
                    this.visible = false
                    this.$emit('ok')
                  } else {
                    this.$message.warning(res.message)
                    this.visible = false
                    this.$emit('ok')
                  }
                })
              }
            })
            .catch((e) => {})
        }
      }
    },
    handleCancel() {
      this.close()
    },
    // tab
    callback(key) {
      this.defaultActiveKey = key
    },
    // 上传相关
    onCancel() {
      this.previewVisible = false
    },
    async handlePreview(file) {
      if (!file.url && !file.preview) {
        file.preview = await getBase64(file.originFileObj)
      }
      this.previewImage = file.url || file.preview
      this.previewVisible = true
    },
    handleChange({ fileList }) {
      this.fileList = fileList
    },
  },
}
</script>
<style scoped lang="less">
::v-deep .ant-modal-body {
  padding: 24px 48px 24ox 48px;
}
::v-deep .ant-modal {
  padding: 24px;
}
@media (max-width: 1012px) {
  ::v-deep .ant-modal {
    top: 0px;
    width: 1000px !important;
    max-width: 1000px !important;
    margin: 0 !important;
  }

  .ant-modal {
    top: 0px;
    width: 1000px !important;
    max-width: 1000px !important;
    margin: 0 !important;
  }
}
</style>
