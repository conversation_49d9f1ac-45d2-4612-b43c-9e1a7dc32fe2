<template>
  <a-modal
    :title="title"
    :width="width"
    :visible="visible"
    :confirmLoading="confirmLoading"
    switchFullscreen
    @ok="handleOk"
    @cancel="handleCancel"
    :destroyOnClose="true"
    cancelText="关闭"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item label="类别名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['typeName', validatorRules.typeName]" placeholder="请输入类别名称"></a-input>
        </a-form-item>
        <a-form-item class="two-words" label="序号" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input-number
            style="width: 200px"
            :min='0' :max="9999999999999999" :precision='0' :step="1"
            v-decorator="['typeOrder', validatorRules.typeOrder]"
            placeholder="请输入序号"
          />
        </a-form-item>
        <a-form-item class="two-words" label="类别描述" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-textarea
            style="width: 100%"
            v-decorator="['typeDesc', validatorRules.typeDesc]"
            :autoSize="{ minRows: 2, maxRows: 4 }"
            :allow-clear="true"
            autocomplete="off"
            placeholder="请输入类别描述"
          />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
import { httpAction, postAction } from '@/api/manage'
import pick from 'lodash.pick'
import JTreeSelect from '@/components/jeecg/JTreeSelect'
import JDictSelectTag from '@/components/dict/JDictSelectTag.vue'
import { ValidateOptionalFields, ValidateRequiredFields } from '@/utils/rules.js'
export default {
  name: 'metricsCategoryModal',
  props: {},
  components: {
    JTreeSelect,
    JDictSelectTag,
  },
  data() {
    return {
      form: this.$form.createForm(this),
      title: '操作',
      width: 800,
      visible: false,
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      confirmLoading: false,
      url: {
        add: '/evaluate/metricsType/add', // 添加评估指标类别
        edit: '/evaluate/metricsType/edit', // 编辑评估指标类别
      },
      validatorRules: {
        typeName: {
          rules: [
            {
              required: true,
              validator: (rule, value, callback) => ValidateRequiredFields(rule, value, callback, '类别名称', 30, 1)
            }
          ]
        },
        typeOrder: {
          rules: [
            { required: true, pattern: /^[1-9]{1}[0-9]*$/, message: '请输入大于0的整数'}
          ],
        },
        typeDesc: {
          rules: [
            { required: false, validator: (rule, value, callback) => ValidateOptionalFields(rule, value, callback, '类别描述', 255) },
          ]
        }
      }
    }
  },
  created() {},
  mounted() {},
  methods: {
    add() {
      this.form.resetFields()
      this.model = Object.assign({}, {})
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(pick(this.model, 'typeName', 'typeOrder', 'typeDesc'))
      })
    },
    edit(record) {
      this.form.resetFields()
      this.model = Object.assign({}, record)
      // console.log("编辑 === ", this.model)
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(pick(this.model, 'typeName', 'typeOrder', 'typeDesc'))
      })
    },
    close() {
      this.visible = false
    },
    handleOk() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let formData = Object.assign(this.model, values)
          //   that.confirmLoading = false
          console.log('获取formData==', formData)
          //   return
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                formData.id = res.result
                let param = {
                  ...formData,
                  method,
                }
                that.$emit('refresh', param)
                that.close()
              } else {
                that.$message.warning(res.message)
              }
              that.confirmLoading = false
            })
            .catch((error) => {
              that.confirmLoading = false
            })
        }
      })
    },
    handleCancel() {
      this.close()
    },
  },
}
</script>
