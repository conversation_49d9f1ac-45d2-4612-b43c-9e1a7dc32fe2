<template>
  <div>
    <!-- 告警数据统计 -->
    <div class="list">
      <div class="item-one">
        <div class="item-left">
          <img src="@/views/alarmManage/monitorOverview/image/solution.png" alt />
        </div>
        <div class="item-right">
          <div class="title">日志总数</div>
          <div class="num blue">{{info.totalLogs}}</div>
        </div>
      </div>
      <div class="item-one" style="margin-left:0.1rem;margin-right:0.1rem;">
        <div class="item-left">
          <img src="@/views/alarmManage/monitorOverview/image/today.png" alt
            style="width:0.3875rem;height:0.3875rem;" />
        </div>
        <div class="item-right">
          <div class="title">今日日志数量</div>
          <div class="num blue">{{info.todayLogs}}</div>
        </div>
      </div>
      <div class="item-one">
        <div class="item-left">
          <img src="@/views/alarmManage/monitorOverview/image/alarm.png" alt />
        </div>
        <div class="item-right">
          <div class="title">告警总数</div>
          <div class="num red">{{info.totalAlarms}}</div>
        </div>
      </div>
      <div class="item-one" style="margin-left:0.1rem;margin-right:0.1rem;">
        <div class="item-left">
          <img src="@/views/alarmManage/monitorOverview/image/today.png" alt
            style="width:0.3875rem;height:0.3875rem;" />
        </div>
        <div class="item-right">
          <div class="title">今日告警数量</div>
          <div class="num red">{{info.todayAlarms}}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  import {
    getAction
  } from '@/api/manage'
  export default {
    data() {
      return {
        url: {
          list: '/log/overview/count/level'
        },
        info: {}
      }
    },
    props: {
      timeData: {
        type: Object,
        default: () => {}
      }
    },
    watch: {
      timeData: {
        deep: true,
        handler(val) {
          this.getData()
        }
      }
    },
    mounted() {
      this.getData()
    },
    methods: {
      getData(startTime, endTime) {
        const params = {};
        if (this.timeData.startTime) {
          params.startTime = this.timeData.startTime;
        }
        if (this.timeData.endTime) {
          params.endTime = this.timeData.endTime;
        }
        getAction(this.url.list, params).then(res => {
          if (res.success) {
            this.info = Object.assign({}, this.info, res.result)
          }
        })
      }
    }
  }
</script>
<style scoped lang="less">
  .list {
    height: 100%;
    display: flex;
    flex-wrap: wrap;

    .item-one {
      width: calc(49% - 0.05rem);
      height: 45.5%;
      margin-bottom: 0.1875rem;
      padding-left: 0.1875rem;
      display: flex;
      align-items: center;
      border: 1px solid #f5f5f5;
      box-shadow: 0px 1px 4px 1px rgba(0, 0, 0, 0.09);

      img {
        width: 0.4375rem; // 35/80
        height: 0.4375rem;
        margin-right: 0.125rem;
      }

      .title {
        font-size: 0.2rem; // 16/80
        color: rgba(0, 0, 0, 0.65);
        white-space: nowrap;
      }

      .num {
        font-size: 0.375rem; // 30/80
        font-weight: 600;
        font-family: DIN-Medium;
        letter-spacing: 0;
        margin-left: 0.0375rem; // 3/80
        text-align: left;
        display: flex;
        height: 0.35rem;
        align-items: center;
        margin-top: 7%;
      }

      .red {
        color: #d61e1e;
      }

      .blue {
        color: #409eff;
      }

      .yellow {
        color: #ffc954;
      }
    }
  }

  @media (max-width: 1200px) {
    .item-one {
      .title {
        font-size: 0.15rem !important; // 12/80
      }

      .num {
        font-size: 0.35rem !important; // 28/80
      }
    }
  }
</style>