<template>
  <div style="height: 100%">
    <keep-alive exclude="metricsDetail">
      <component :is="pageName" style="height: 100%" :data="data" />
    </keep-alive>
  </div>
</template>
<script>
import metricsList from './metricsList'
import metricsDetail from './metricsDetail'
export default {
  name: 'metricsManage',
  data() {
    return {
      isActive: 0,
      data: {},
    }
  },
  components: {
    metricsList,
    metricsDetail,
  },
  created() {
    this.pButton1(0)
  },
  //使用计算属性
  computed: {
    pageName() {
      switch (this.isActive) {
        case 0:
          return 'metricsList'
          break

        default:
          return 'metricsDetail'
          break
      }
    },
  },
  methods: {
    pButton1(index) {
      this.isActive = index
    },
    pButton2(index, item) {
      this.isActive = index
      this.data = item
    },
  },
}
</script>
