<template>
  <div class="title">
    <div class="arrow"></div>
    <div class="text">{{ title }}</div>
    <div class="bg"></div>
    <div class="dot"></div>
  </div>
</template>
<script>
export default {
  name: 'CustomTitle',
  data() {
    return {
      
    }
  },
  props: {
    title: {
      type: String,
      default: '',
    },
  },
}
</script>

<style lang="less" scoped>
.title {
  width: 100%;
  // width: calc(100% - 118px);
  height: 29px;
  background: linear-gradient(90deg, rgba(12, 38, 50, 0.4) 0%, rgba(12, 38, 50, 0.1) 100%);
  position: relative;
  display: flex;
  align-items: center;
  .bg {
    width: 28%;
    height: 22px;
    background: linear-gradient(90deg, rgba(0, 148, 255, 0) 0%, rgba(0, 148, 255, 0.1) 100%);
    position: absolute;
    right: 118px;
    top: 3px;
  }
  .arrow {
    width: 33px;
    height: 24px;
    background: url('../../../assets/bigScreen/ITResource/title-arrow.png') center center no-repeat;
    background-size: 100% 100%;
  }
  .text {
    font-family: FZZYJW;
    font-weight: normal;
    font-size: 18px;
    margin-left: 10px;
  }
  .dot {
    width: 116px;
    height: 29px;
    background: url('../../../assets/bigScreen/ITResource/title-dot.png') center center no-repeat;
    background-size: 100% 100%;
    position: absolute;
    right: 0;
    top: 0;
  }
}
</style>
