<template>
  <div style="height:100%">
      <component :is="pageName" :data="data"/>
  </div>
</template>
<script>
  import DevopsIpManageList from './DevopsIpManageList'
  import DevopsIpManageDetailsForm from './modules/DevopsIpManageDetailsForm'
  export default {
    name: "DevopsIpManage",
    data() {
      return {
        isActive: 0,
        data:{}
      };
    },
    components: {
      DevopsIpManageList,
      DevopsIpManageDetailsForm
    },
    created(){
      this.pButton1(0);
    },
    //使用计算属性
    computed: {
      pageName() {
        switch (this.isActive) {
          case 0:
            return "DevopsIpManageList";
            break;

          default:
            return "DevopsIpManageDetailsForm";
            break;
        }
      }
    },
    methods: {
      pButton1(index) {
        this.isActive = index;
      },
      pButton2(index,item) {
        this.isActive = index;
        this.data = item;
      }
    }
  }
</script>