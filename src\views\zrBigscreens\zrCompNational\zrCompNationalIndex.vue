<template>
  <div class='zr-business-view' ref='zrBusinessView'>
    <div class='business-view-left'>
      <zr-national-nodes v-if='nodeList.length' :nodeList="nodeList"></zr-national-nodes>
    </div>
    <div class='business-view-center' >
      <zr-status-info v-if='deptId' :deptId="deptId"></zr-status-info>
     <div class='map-box'>
       <yq-chart-map-migrate v-if='nodeList.length' :nodeList="nodeList" ref='map'></yq-chart-map-migrate>
     </div>
      <div class='legend-block'>
        <zr-focus-info :infoText="infoStr" ></zr-focus-info>
        <div class='legend-block-list'>
          <div class='legend-block-list-item' v-for='item in unitLevels' :key='"u_"+item.value'>
            <div v-if='item.value===1' class='legend-block-list-item-icon level-circle' ></div>
            <div v-if='item.value===2' class='legend-block-list-item-icon level-rect'></div>
            <div v-if='item.value===3' class='legend-block-list-item-icon level-diamond'></div>
            <div class='legend-block-list-item-text'>{{item.label}}</div>
          </div>
          <div class='legend-block-list-item' v-for='item in businessStatus' :key='"s_"+item.value'>
            <div class='legend-block-list-item-icon' :style='{backgroundColor:item.color}'></div>
            <div class='legend-block-list-item-text'>{{item.label}}</div>
          </div>
        </div>
      </div>
    </div>
    <div class='business-view-right'>
      <zr-national-businesses v-if='systemList.length' :systemList="systemList"></zr-national-businesses>
    </div>
  </div>
</template>
<script>
import resizeObserverMixin from '@views/statsCenter/com/resizeObserverMixin'
import { businessStatus,unitLevels } from '@views/zrBigscreens/modules/zrUtil'
import ZrNationalOpStatus from '@views/zrBigscreens/zrCompNational/modules/ZrNationalOpStatus.vue'
import ZrNationalDevices from '@views/zrBigscreens/zrCompNational/modules/ZrNationalDevices.vue'
import ZrNationalNodes from '@views/zrBigscreens/zrCompNational/modules/ZrNationalNodes.vue'
import ZrStatusInfo from '@views/zrBigscreens/zrCompNational/modules/ZrStatusInfo.vue'
import ZrFocusInfo from '@views/zrBigscreens/zrCompNational/modules/ZrFocusInfo.vue'
import ZrNationalBusinesses from '@views/zrBigscreens/zrCompNational/modules/ZrNationalBusinesses.vue'
import YqChartMapMigrate from '@views/zrBigscreens/zrCompNational/modules/YqChartMapMigrate.vue'
import { getAction,postAction } from '@/api/manage'
export default {
  name: 'businessIndex',
  components: {
    ZrNationalBusinesses,
    ZrNationalDevices,
    ZrNationalOpStatus,
    YqChartMapMigrate,
    ZrNationalNodes,
    ZrStatusInfo,
    ZrFocusInfo
  },
  mixins: [resizeObserverMixin],
  data() {
    return {
      centerH: '',
      mapH: '',
      centerPd: "",
      legndBottom:"",
      businessStatus,
      unitLevels,
      nodeList:[],
      infoStr:"",
      systemList:[],
      deptId:"",
    }
  },
  created() {
    this.getDeptStatus();
    this.getBusinesses();
  },
  mounted() {
  },
  methods: {
    //获取应用系统
    getBusinesses(){
      getAction('/monitor/situation/getBusinessListByDeptId',{pageNo:1, pageSize:-1}).then(res => {
        // console.log("获取到的 === ",res)
        if (res.success && res.result && res.result.businessList) {
          this.systemList = res.result.businessList.records.map(el=>{
            el.businessImage = window._CONFIG['downloadUrl']+"/"+el.businessImage
            el.status = 0;
            return el
          })
        }
      })
    },
    //获取节点运行状态
    getDeptStatus() {
      getAction('/monitor/situation/getNodeStatusAndDevList', {
        deptAndDevBindDictCode: 'ZR_BIRSCREEN_DEPTS',
        withNodeDev: 'true',
        withChildBusiness: 'true',
        withRunTime: 'true',
        withChildDev: 'true'
      }).then(res => {
        if (res.success) {
          this.deptId = res.result.find(el=>el.orgType==1)?.id || ""
          this.nodeList = res.result.map(el => {
            let status = 0
            let day = '--'
            if (el.nodeDevObject) {
              if (el.nodeDevObject.status == 0) {
                status = 2
              } else if (el.nodeDevObject.alarmStatus !== 0) {
                status = 1
              } else {
                status = 0
              }
              if(el.nodeDevObject.sysUpTime){
                let temArr = el.nodeDevObject.sysUpTime.split('天')
                day = temArr.length>1?temArr[0]:"0"
              }
              // console.log("el.nodeDevObject",day)
            }
            let level = 1
            if(el.orgCategory){
              let orgCategory = Number(el.orgCategory)
              if(orgCategory<2000){
                level = 1
              }else if(orgCategory<3000){
                level = 2
              }else{
                level = 3
              }

            }

            return {
              name: el.departName,
              nickName:el.code,
              status: status,
              day: day,
              deviceCount:el.devInfoArray?el.devInfoArray.length:"--",
              businessCount: el.businessCount || "--",
              position:[el.longitude,el.latitude],
              level:level,
              deptId:el.id
            }
          })
          this.mapList()
          this.getFocusDept()
        }
        // console.log('获取节点运行状态成功', res)
      })
    },
    mapList() {
      this.nodeList = this.nodeList.map(el => {
        let bs = businessStatus.find(item => item.value == el.status)
        if (bs) {
          el.color = bs.color
          el.statusText = bs.label
        }else{
          el.color = "#fff"
          el.statusText = "--"
        }
        return el
      }).sort((a, b)=>{
        return a.day - b.day;
      })
    },
    //获取需要关注的单位
    getFocusDept(){
      let filters = this.nodeList.filter(el=>el.status !== "--").sort((a,b)=>{
        return a.status - b.status
      })
      let focusDept = filters.splice(0,3)
      let str = ""
      focusDept.forEach((item,index)=>{
        str += `${item.nickName || item.name}（${item.statusText}）`
        if(index !== focusDept.length - 1){
          str += "、"
        }
      })
      this.infoStr = str
    },
    //监听页面缩放 更新中间区域高度
    resizeObserverCb() {
      // let hScaleValue = window.innerHeight / 1080
      // this.centerH = `calc(100% -  (70px * ${hScaleValue}))`
      // this.centerPd = `calc(120px * ${hScaleValue})`
      // this.legndBottom = `calc(60px * ${hScaleValue})`
      this.$nextTick(()=>{
        if(this.$refs.map && this.$refs.map.resize) {
          this.$refs.map.resize()
        }
      })
    }
  }
}
</script>

<style scoped lang='less'>
.zr-business-view {
  padding-left: calc(33 / 19.2 * 1vw);
  padding-right: calc(33 / 19.2 * 1vw);
  display: flex;
  align-items: center;
  justify-content: space-around;
  height: 100%;

  .business-view-left {
    width: 25%;
    height: 100%;
    //background-image: url(/zrBigScreen/zrBusiness/businessLeftBg.png);
    //background-size: 100% 100%;
    //background-repeat: no-repeat;
    padding: 0px;
    display: flex;
    flex-direction: column;

    .business-view-left-bottom {
      display: flex;
      flex-direction: column;

      .business-view-left-middle {
        height: 50%
      }

      .business-view-left-bottom {
        height: calc(50% - 8px);
      }
    }
  }

  .business-view-center {
    width: 50%;
    height: 100%;
    //background-image: url(/zrBigScreen/zrBusiness/businessCenterBg.png);
    //background-size: 100% 100%;
    //background-repeat: no-repeat;
    padding: 12px;
    position: relative;
    .map-box{
      height: calc(100% - 110px);
    }
    .status-info {
      font-size: 20px;
      font-weight: bold;
      color: #E3E7EF;
      text-align: center;
    }

    .legend-block {
      position: absolute;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width:100%;
      .legend-block-list {
        display: flex;
        .legend-block-list-item {
          display: flex;
          align-items: center;
          font-weight: 400;
          font-size: 14px;
          padding: 0 20px;
          color: #E3E7EF;
          .legend-block-list-item-icon{
            width:14px;
            height: 14px;
            margin-right: 12px;
            background-color: #ffffff;
          }
          .level-circle {
            border-radius: 50%;
          }
          .level-rect {
            width: 14px;
            height: 14px;
          }
          .level-diamond {
            width: 14px;
            height: 14px;
            transform: rotate(45deg);
          }
        }
      }
    }
  }

  .business-view-right {
    width: 25%;
    height: 100%;
    //background-image: url(/zrBigScreen/zrBusiness/businessRightBg.png);
    //background-size: 100% 100%;
    //background-repeat: no-repeat;
    padding: 0px;
    display: flex;
    flex-direction: column;
  }
}
</style>