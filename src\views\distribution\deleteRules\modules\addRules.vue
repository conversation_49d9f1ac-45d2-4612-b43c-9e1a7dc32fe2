<template>
  <j-modal :title='title' :width='width' :visible='visible' :destroyOnClose='true'
           :okButtonProps="{ class:{'jee-hidden': disableSubmit} }" :centered='true' @ok='handleOk'
           @cancel='handleCancel'
           cancelText='关闭'>
    <a-spin :spinning='confirmLoading'>
      <j-form-container :disabled='disableSubmit' style='max-height: 70vh; overflow-y: auto;overflow-x: hidden'>
        <a-form-model ref='form' slot='detail' :model='rulesForm' :rules='validatorRules' :labelCol='labelCol'
                      :wrapperCol='wrapperCol' labelAlign='left'>
          <a-row>
            <a-col :span='24'>
              <a-form-model-item label='规则名称' prop='name'>
                <a-input style='width: 100%' v-model='rulesForm.name' :c='true' autocomplete='off'
                         placeholder='请输入规则名称' />
              </a-form-model-item>
            </a-col>
            <a-col :span='24' style='margin-bottom: 12px'>
             <span >规则设置</span>
            </a-col>
            <a-col :span='24' >
             <a-row :gutter="[12, 0]">
               <a-col :span='6'>
                 <a-form-model-item  prop='dateType'>
                   <a-select default-value="lastupdatetime" style="width: 100%" allowClear  disabled>
                     <a-select-option v-for="item in dates" :key="item.value" :value="item.value">
                       {{ item.label }}
                     </a-select-option>
                   </a-select>
                 </a-form-model-item>
               </a-col>
               <a-col :span='6'>
                 <a-form-model-item  prop='logic'>
                   <a-select default-value="gt"  style="width: 100%" allowClear disabled >
                     <a-select-option v-for="item in logics" :key="item.value" :value="item.value" >
                       {{ item.label }}
                     </a-select-option>
                   </a-select>
                 </a-form-model-item>
               </a-col>
               <a-col :span='6'>
                 <a-form-model-item  prop='num'>
                   <a-input-number style='width: 100%' id="inputNumber" v-model="rulesForm.timeNum" :min="1" allowClear />
                 </a-form-model-item>
               </a-col>
               <a-col :span='6'>
                 <a-select default-value="day" style="width: 100%" disabled >
                   <a-select-option value="day">
                     天
                   </a-select-option>
                 </a-select>
               </a-col>
             </a-row>
            </a-col>

            <a-col :span='24'>
              <a-form-model-item label='描述' prop='desp'>
                <a-textarea style='width: 100%' v-model='rulesForm.desp' :autoSize='{minRows:2,maxRows:4}'
                            :allow-clear='true' autocomplete='off' placeholder='请输入描述' />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>

      </j-form-container>
    </a-spin>
  </j-modal>
</template>

<script>

import {
  httpAction,
  getAction
} from '@api/manage'
import { ajaxGetDictItem } from '@api/api'

export default {
  name: 'AddRules',
  data() {
    return {
      title: '',
      width: '800px',
      visible: false,
      disableSubmit: false,
      confirmLoading: false,
      labelCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 24
        }
      },
      wrapperCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 24
        }
      },
      rulesForm: {},
      interface: '',
      validatorRules: {
        name: [{
          required: true,
          message: '请输入规则名称！'
        },{
          max: 50,
          message: '规则名称长度应小于50！'
        }],
        // dateType: [{
        //   required: true,
        //   message: '请选择时间点！'
        // }],
        // logic: [{
        //   required: true,
        //   message: '请选择逻辑！'
        // }],
        timeNum: [{
          required: true,
          message: '请输入数量！'
        }],
        desp: [
          {
            min: 0,
            max: 255,
            message: '描述长度应在0-255之间！'
          }
        ]
      },
      url: {
        add: '/distributedStorage/dump/rule',
        edit: '/distributedStorage/dump/rule'
      },
      dates:[
        {label:"添加时间",value:"createtime"},
        {label:"最后修改时间",value:"lastupdatetime"},
        {label:"最后查看时间",value:"lastlooktime"},
        {label:"最后下载时间",value:"lastdownloadtime"}],
      logics:[
        {label:"大于",value:"gt"},
        {label:"小于",value:"lt"},
        {label:"大于等于",value:"gte"},
        {label:"小于等于",value:"lte"},
      ]
    }

  },
  created() {
    this.getGatewayCodes()
    this.rulesForm = this.initFormData()
    // this.getDatatype()
  },
  methods: {
    getGatewayCodes() {
      getAction('/configureBack/task/getGatewayList').then(res => {
        if (res.success) {
          this.gatewayCodes = res.result.map(el => {
            return { label: el.name, value: el.deviceCode }
          })
        }
      })
    },
    initFormData() {
      return {
        name: '',
        ruleInfo: '',
        desp: '',
        // dateType:"",
        // logic:"",
        timeNum:1
      }
    },
    add() {
      this.edit({})
    },
    edit(record) {
      this.visible = true
      this.$nextTick(() => {
        this.rulesForm = Object.assign(this.rulesForm, record)
      })
    },
    close() {
      this.rulesForm = this.initFormData()
      this.visible = false
    },
    handleOk() {
      const that = this
      // console.log("喀喀喀 === >", that.rulesForm)
      that.$refs.form.validate((err, value) => {
        if (err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!that.rulesForm.id) {
            httpurl += that.url.add
            method = 'post'
          } else {
            httpurl += that.url.edit
            method = 'put'
          }
          if (that.rulesForm.abutmentTask) {
            this.rulesForm.abutmentTask.isEnable = Number(this.rulesForm.abutmentTask.isEnable)
          }
          let formData = {
            ...that.rulesForm
          }
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
                that.close()
              } else {
                that.$message.warning(res.message)
              }
              that.confirmLoading = false
            }).catch((res) => {
            that.$message.warning(res.message)
            that.confirmLoading = false
          })
        }
      })
    },
    submitCallback() {
      this.$emit('ok')
      this.visible = false
    },
    handleCancel() {
      this.close()
    },
    setCorn(data) {
      if (data && data.target != null) {
        let dataList = data.target.value.split(' ')
        if (dataList[0] == '*') {
          this.$message.warning('请确认是否每秒都执行')
        }
      } else {
        let dataList = data.split(' ')
        if (dataList[0] == '*') {
          this.$message.warning('请确认是否每秒都执行')
        }
      }
      if (Object.keys(data).length == 0) {
        this.$message.warning('请输入cron表达式!')
      }
      // this.$nextTick(() => {
      //   this.rulesForm.abutmentTask.executeCron = data;
      // })
    },
    validateIP(rule, value, callback) {
      let reg = /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)(:\d{1,5})?$/
      if (!reg.test(value)) {
        callback('请输入正确服务地址！')
      } else {
        callback()
      }
    }
  }
}
</script>
<style scoped lang='less'>
@import '~@assets/less/normalModal.less';

.color ::v-deep.ant-input {
  padding: 0px 30px 0px 11px !important;
}
</style>