<template>
  <a-row>
    <a-col style='width: 100%; height: 100%; display: flex; flex-direction: column'>
      <!-- 查询区域 -->
      <!--      <div class='table-page-search-wrapper-style'>
              <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
                <a-row :gutter='12' ref='row'>
                  <a-col :span='spanValue'>
                    <a-form-item >
                      <a-input :maxLength='maxLength' placeholder='请输入补丁名称' :allowClear='true' autocomplete='off'
                    </a-form-item>
                  </a-col>
                  <a-col :span='colBtnsSpan()'>
                      <span
                        class='table-page-search-submitButtons'
                        :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                      >
                      </span>
                  </a-col>
                </a-row>
              </a-form>
            </div>-->
      <!-- 查询区域-END -->

      <a-row class='lastBtn2'>
        <!-- 操作按钮区域 -->
<!--        <div class='table-operator'>
          <a-input
            style='width: 210px'
            placeholder='请输入名称'
            :allowClear='true'
            autocomplete='off'
            v-model='queryParam.patchName'
          ></a-input>
          <a-button style='margin-left: 10px' type='primary' @click='searchQuery' class='btn-search-style'>查询</a-button>
          <a-button @click='searchReset' class='btn-reset-style'>重置</a-button>
          <a-button  @click='handleAdd'>新增</a-button>
          <a-button  @click="handleExportXls('补丁管理表')">导出</a-button>
          <a-dropdown v-if='selectedRowKeys.length > 0'>
            <a-menu slot='overlay' style='text-align: center'>
              <a-menu-item key='1' @click='batchDel'>删除</a-menu-item>
            </a-menu>
            <a-button> 批量操作 <a-icon type='down' /></a-button>
          </a-dropdown>
        </div>-->
      </a-row>
      <!-- table区域-begin -->
      <a-table
        ref='table'
        bordered
        rowKey='id'
        :columns='columns'
        :dataSource='dataSource'
        :pagination='ipagination'
        :loading='loading'
        class='j-table-force-nowrap'
        @change='handleTableChange'
      >
        <template slot='htmlSlot' slot-scope='text'>
          <div v-html='text'></div>
        </template>
        <template slot='imgSlot' slot-scope='text'>
          <span v-if='!text' style='font-size: 14px'>无图片</span>
          <img v-else :src='getImgView(text)' height='25px' alt='' style='max-width: 80px; font-size: 14px' />
        </template>
        <template slot='fileSlot' slot-scope='text'>
          <span v-if='!text' style='font-size: 14px'>无文件</span>
          <a-button v-else :ghost='true' type='primary' icon='download' size='small' @click='downloadFile(text)'>
            下载
          </a-button>
        </template>

        <span
          slot='action'
          slot-scope='text, record'
          class='caozuo'
          style='display: inline-block; white-space: nowrap; text-align: center'
        >
<!--            <a style='color: #409eff' @click='handleDetailPage(record)'>查看</a>-->
          </span>
        <template slot='tooltip' slot-scope='text'>
          <a-tooltip placement='topLeft' :title='text' trigger='hover'>
            <div class='tooltip'>
              {{ text }}
            </div>
          </a-tooltip>
        </template>
      </a-table>
    </a-col>
  </a-row>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { filterMultiDictText } from '@/components/dict/JDictSelectUtil'
import JSuperQuery from '@/components/jeecg/JSuperQuery.vue'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
import SoftPatchInfoDetails from '@views/opmg/softwarePoc/patch/modules/SoftPatchInfoDetails.vue'
import {terminalSuccess} from '@views/opmg/softwarePoc/mock/listData'

export default {
  name: 'SoftPatchSuccess',
  mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
  props: {
    id: {
      type:String,
      default: '',
      required: true,
    }
  },
  components: {
    SoftPatchInfoDetails,
    JSuperQuery,
  },
  data() {
    return {
      maxLength:50,
      description: '升级包成功升级终端',
      // 表头
      columns: [
        {
          title: '序号',
          dataIndex: 'index',
          width: 60,
          customRender: (text, record, index) => {
            return index + 1;
          }
        },
        {
          title: '名称',
          dataIndex: 'terminalName'
        },
        {
          title: '所属单位',
          dataIndex: 'deptName'
        },{
          title: '升级开始时间',
          dataIndex: 'startTime',
          /*customRender: (text) => {
            return this.patchTypes.find(el=>el.value === text)?.label
          }*/
        },{
          title: '升级结束时间',
          dataIndex: 'endTime',
          /*customRender: (text) => {
            return this.patchTypes.find(el=>el.value === text)?.label
          }*/
        },
        // {
        //   title: '操作系统',
        //   dataIndex: 'patchOsText'
        // },
        // {
        //   title: '架构',
        //   dataIndex: 'fraworkText'
        // },

       /* {
          title: '操作',
          dataIndex: 'action',
          fixed: 'right',
          align: 'center',
          width: 147,
          scopedSlots: { customRender: 'action' }
        }*/
      ],
      url: {
        list: '/software/upgradePackageManage/successUpgradedTerminal/list',
        delete: '',
        deleteBatch: '',
        exportXlsUrl: '',
        importExcelUrl: ''
      },
      dictOptions: {},
      superFieldList: [],
      patchTypes: [
        { value: '1', label: '直接安装包' },
        { value: '2', label: '升级文件' }
      ],
      auditStatus: [
        { value: '1', label: '未审核' },
        { value: '2', label: '通过' },
        { value: '3', label: '未通过' },
      ],
      disableMixinCreated: true,
    }

  },
  created() {
    this.queryParam.id = this.id
    this.loadData()
    // this.getSuperFieldList()
  },
  mounted() {
  },
  computed: {
    importExcelUrl: function() {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    }
  },
  methods: {
    //补丁详情
    handleDetailPage(record) {
      this.$refs.softPatchInfoDetails.show(record)
    },
    initDictConfig() {
    },
    //下载
    fontClick(path) {
      window.open(window._CONFIG['downloadUrl'] + '/' + path)
    },
    getSuperFieldList() {
      let fieldList = []
      // fieldList.push({type:'string',value:'patchName',text:'补丁名称',dictCode:''})
      fieldList.push({ type: 'string', value: 'patchVersion', text: '补丁版本', dictCode: '' })
      fieldList.push({ type: 'string', value: 'patchOs', text: '操作系统', dictCode: 'patch_os' })
      fieldList.push({ type: 'string', value: 'frawork', text: '架构', dictCode: 'cpuArch' })
      fieldList.push({ type: 'string', value: 'patchFileId', text: '补丁文件id', dictCode: '' })
      fieldList.push({ type: 'string', value: 'scriptFileId', text: '安装脚本id', dictCode: '' })
      fieldList.push({ type: 'string', value: 'patchFileName', text: '补丁文件名称', dictCode: '' })
      fieldList.push({ type: 'string', value: 'scriptFileName', text: '脚本文件', dictCode: '' })
      fieldList.push({ type: 'string', value: 'patchDescribe', text: '描述', dictCode: '' })
      fieldList.push({ type: 'int', value: 'effect', text: '是否有效', dictCode: 'valid_status' })
      fieldList.push({ type: 'string', value: 'softwareId', text: '软件id', dictCode: '' })
      fieldList.push({ type: 'string', value: 'resourceType', text: '架构', dictCode: 'resources_type' })
      this.superFieldList = fieldList
    },
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';

.table-page-search-wrapper .ant-form-inline .ant-form-item {
  margin-bottom: 0 !important;
}

.ant-table-pagination.ant-pagination {
  margin: 16px 0 0 0 !important;
}


</style>
