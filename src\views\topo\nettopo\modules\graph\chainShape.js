import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>ha<PERSON> } from '@antv/x6'

class SwitchShape1 extends Shape.Rect {
    collapsed = false

    getTopPorts() {
        return this.getPortsByGroup('top')
    }

    getBottomPorts() {
        return this.getPortsByGroup('bottom')
    }

    getUsedInPorts(graph) {
        const incomingEdges = graph.getIncomingEdges(this) || []
        return incomingEdges.map((edge) => {
            const portId = edge.getTargetPortId()
            return this.getPort(portId)
        })
    }

    getNewInPorts(length) {
        return Array.from(
            {
                length,
            },
            () => {
                return {
                    group: 'top',
                }
            },
        )
    }

    updateInPorts(graph) {
        const minNumberOfPorts = 2
        const ports = this.getTopPorts()
        const usedPorts = this.getUsedInPorts(graph)
        const newPorts = this.getNewInPorts(
            Math.max(minNumberOfPorts - usedPorts.length, 1),
        )

        if (
            ports.length === minNumberOfPorts &&
            ports.length - usedPorts.length > 0
        ) {
            // noop
        } else if (ports.length === usedPorts.length) {
            this.addPorts(newPorts)
        } else if (ports.length + 1 > usedPorts.length) {
            this.prop(
                ['ports', 'items'],
                this.getBottomPorts().concat(usedPorts).concat(newPorts),
                {
                    rewrite: true,
                },
            )
        }
        return this
    }
    isCollapsed() {
        return this.collapsed
    }

    toggleButtonVisibility(visible) {
        this.attr('buttonGroup', {
            display: visible ? 'block' : 'none',
        })
    }

    toggleCollapse(collapsed) {
        const target = collapsed == null ? !this.collapsed : collapsed
        if (!target) {
            this.attr('buttonSign', {
                d: 'M 1 5 9 5 M 5 1 5 9',
                strokeWidth: 1.6,
            })
        } else {
            this.attr('buttonSign', {
                d: 'M 2 5 8 5',
                strokeWidth: 1.8,
            })
        }
        this.collapsed = target
    }
}

SwitchShape1.config({
    width: 80,
    height: 80,
    attrs: {
        body: {
            stroke: '#none',
            fill: 'rgba(95,149,255,0.05)',
            opacity: 0
        },
        image: {
            'xlink:href': require('@/assets/netdevice/switch.png'),
            width: 80,
            height: 80,
            // 'mix-blend-mode': 'color-dodge'
            filter: 'brightness(100%)'
        },
        image1: {
            width: 20,
            height: 20,
            refX: '80%',
            refY: '80%'
        },
        label: {
            text: '交换机',
            fontSize: 14,
            fill: '#fff',
            refX: 0.5,
            refY: 0.99,
            textAnchor: "middle",
            textVerticalAnchor: "top",
        },
        '.btn.add': {
            float: 'right',
            refX: '90%',
            refY: '10%',
            cursor: 'pointer',
            event: 'node:add',
        },
        '.btn.del': {
            float: 'right',
            refX: '90%',
            refY: '15%',
            cursor: 'pointer',
            display: 'none',
            event: 'node:delete',
        },
        '.btn > circle': {
            r: 10,
            fill: 'transparent',
            stroke: '#333',
            strokeWidth: 1,
        },
        '.btn.add > text': {
            float: 'right',
            refX: '0%',
            refY: '0%',
            fontSize: 20,
            fontWeight: 800,
            stroke: '#000',
            fontFamily: 'Times New Roman',
            text: '+',
        },
        '.btn.del > text': {
            float: 'right',
            refX: '0%',
            refY: '0%',
            fontSize: 28,
            fontWeight: 500,
            stroke: '#000',
            fontFamily: 'Times New Roman',
            text: '-',
        },
    },
    markup: [
        {
            tagName: 'rect',
            selector: 'body'
        },
        {
            tagName: 'image',
            selector: 'image'
        },
        {
            tagName: 'image',
            selector: 'image1',
            attrs: {
                class: 'image1',
            },
        },
        {
            tagName: 'text',
            selector: 'label',
        },
        {
            tagName: 'foreignObject',
            selector: 'fo',
            children: [
                {
                    ns: Dom.ns.xhtml,
                    tagName: 'body',
                    selector: 'foBody',
                    children: [
                        {
                            tagName: 'div',
                            selector: 'edit-text'
                        }
                    ]
                }
            ]
        },
    ],
    ports: {
        groups: {
            top: {
                position: 'top',
                attrs: {
                    circle: {
                        r: 3,
                        magnet: true,
                        stroke: '#5F95FF',
                        strokeWidth: 1,
                        fill: '#fff',
                    }
                }
            },
            right: {
                position: 'right',
                attrs: {
                    circle: {
                        r: 3,
                        magnet: true,
                        stroke: '#5F95FF',
                        strokeWidth: 1,
                        fill: '#fff',
                    }
                }
            },
            left: {
                position: 'left',
                attrs: {
                    circle: {
                        r: 3,
                        magnet: true,
                        stroke: '#5F95FF',
                        strokeWidth: 1,
                        fill: '#fff',
                    }
                }
            },
            bottom: {
                position: 'bottom',
                attrs: {
                    circle: {
                        r: 3,
                        magnet: true,
                        stroke: '#5F95FF',
                        strokeWidth: 1,
                        fill: '#fff',
                    }
                }
            },
        },
    },
    portMarkup: [
        {
            tagName: 'circle',
            selector: 'portBody',
        },
    ],
})

export const switchNode = Graph.registerNode('switch-node1', SwitchShape1)
export const sonTopoNode1 = Graph.registerNode('son-topo-node1', {
    inherit: 'circle',
    width: 80,
    height: 80,
    attrs: {
        body: {
            stroke: 'none',
            fill: 'rgba(95,149,255,0.05)'
        },
        image: {
            'xlink:href': require('@/assets/netdevice/switch.png'),
            width: 80,
            height: 80,
        },
        label: {
            text: '交换机',
            refX: 0.5,
            refY: 0.99,
            textAnchor: "middle",
            textVerticalAnchor: "top",
        },
    },
    markup: [
        {
            tagName: 'rect',
            selector: 'body'
        },
        {
            tagName: 'image',
            selector: 'image'
        },
        {
            tagName: 'text',
            selector: 'label',
        },
        {
            tagName: 'foreignObject',
            selector: 'fo',
            children: [
                {
                    ns: Dom.ns.xhtml,
                    tagName: 'body',
                    selector: 'foBody',
                    children: [
                        {
                            tagName: 'div',
                            selector: 'edit-text'
                        }
                    ]
                }
            ]
        },
    ],
    ports: {
        groups: {
            top: {
                position: 'top',
                attrs: {
                    circle: {
                        r: 3,
                        magnet: true,
                        stroke: '#5F95FF',
                        strokeWidth: 1,
                        fill: '#fff',
                    }
                }
            },
            right: {
                position: 'right',
                attrs: {
                    circle: {
                        r: 3,
                        magnet: true,
                        stroke: '#5F95FF',
                        strokeWidth: 1,
                        fill: '#fff',
                    }
                }
            },
            left: {
                position: 'left',
                attrs: {
                    circle: {
                        r: 3,
                        magnet: true,
                        stroke: '#5F95FF',
                        strokeWidth: 1,
                        fill: '#fff',
                    }
                }
            },
            bottom: {
                position: 'bottom',
                attrs: {
                    circle: {
                        r: 3,
                        magnet: true,
                        stroke: '#5F95FF',
                        strokeWidth: 1,
                        fill: '#fff',
                    }
                }
            },
        },
        items: [
            {
                group: 'left'
            },
            {
                group: 'right'
            },
            {
                group: 'top'
            },
            {
                group: 'bottom'
            },
        ]
    },
    portMarkup: [
        {
            tagName: 'circle',
            selector: 'portBody',
        },
    ],
})

export const FlowChartImageRect = Graph.registerNode('flow-chart-image-rect1', {
    inherit: 'rect',
    width: 60,
    height: 60,
    attrs: {
        body: {
            stroke: '#5F95FF',
            strokeWidth: 1,
            fill: 'rgba(95,149,255,0.05)'
        },
        image: {
            'xlink:href': require('@/assets/daiban.png'),
            width: 60,
            height: 60,
            x: 0,
            y: 0
        },
    },
    markup: [
        {
            tagName: 'rect',
            selector: 'body'
        },
        {
            tagName: 'image',
            selector: 'image'
        },
        {
            tagName: 'text',
            selector: 'title'
        },
        {
            tagName: 'text',
            selector: 'text'
        }
    ],
    ports: {
        groups: {
            top: {
                position: 'top',
                attrs: {
                    circle: {
                        r: 3,
                        magnet: true,
                        stroke: '#5F95FF',
                        strokeWidth: 1,
                        fill: '#fff',
                        style: {
                            visibility: 'hidden'
                        }
                    }
                }
            },
            right: {
                position: 'right',
                attrs: {
                    circle: {
                        r: 3,
                        magnet: true,
                        stroke: '#5F95FF',
                        strokeWidth: 1,
                        fill: '#fff',
                        style: {
                            visibility: 'hidden'
                        }
                    }
                }
            },
            bottom: {
                position: 'bottom',
                attrs: {
                    circle: {
                        r: 3,
                        magnet: true,
                        stroke: '#5F95FF',
                        strokeWidth: 1,
                        fill: '#fff',
                        style: {
                            visibility: 'hidden'
                        }
                    }
                }
            },
            left: {
                position: 'left',
                attrs: {
                    circle: {
                        r: 3,
                        magnet: true,
                        stroke: '#5F95FF',
                        strokeWidth: 1,
                        fill: '#fff',
                        style: {
                            visibility: 'hidden'
                        }
                    }
                }
            }
        },
        items: [
            {
                group: 'top'
            },
            {
                group: 'right'
            },
            {
                group: 'bottom'
            },
            {
                group: 'left'
            }
        ]
    }
})

export const FlowChartTitleRect = Graph.registerNode('flow-chart-title-rect1', {
    inherit: 'rect',
    width: 200,
    height: 68,
    attrs: {
        body: {
            stroke: '#5F95FF',
            strokeWidth: 1,
            fill: 'rgba(95,149,255,0.05)'
        },
        head: {
            refWidth: '100%',
            stroke: 'transparent',
            height: 28,
            fill: 'rgb(95,149,255)'
        },
        image: {
            'xlink:href': require('@/assets/logo.svg'),
            height: 16,
            x: 6,
            y: 6
        },
        title: {
            text: 'Node',
            refX: 30,
            refY: 9,
            fill: '#ffffff',
            fontSize: 12,
            'text-anchor': 'start'
        },
        text: {
            text: 'this is content text',
            refX: 8,
            refY: 45,
            fontSize: 12,
            fill: 'rgba(0,0,0,0.6)',
            'text-anchor': 'start'
        }
    },
    markup: [
        {
            tagName: 'rect',
            selector: 'body'
        },
        {
            tagName: 'rect',
            selector: 'head'
        },
        {
            tagName: 'image',
            selector: 'image'
        },
        {
            tagName: 'text',
            selector: 'title'
        },
        {
            tagName: 'text',
            selector: 'text'
        }
    ],
    ports: {
        groups: {
            top: {
                position: 'top',
                attrs: {
                    circle: {
                        r: 3,
                        magnet: true,
                        stroke: '#5F95FF',
                        strokeWidth: 1,
                        fill: '#fff',
                        style: {
                            visibility: 'hidden'
                        }
                    }
                }
            },
            right: {
                position: 'right',
                attrs: {
                    circle: {
                        r: 3,
                        magnet: true,
                        stroke: '#5F95FF',
                        strokeWidth: 1,
                        fill: '#fff',
                        style: {
                            visibility: 'hidden'
                        }
                    }
                }
            },
            bottom: {
                position: 'bottom',
                attrs: {
                    circle: {
                        r: 3,
                        magnet: true,
                        stroke: '#5F95FF',
                        strokeWidth: 1,
                        fill: '#fff',
                        style: {
                            visibility: 'hidden'
                        }
                    }
                }
            },
            left: {
                position: 'left',
                attrs: {
                    circle: {
                        r: 3,
                        magnet: true,
                        stroke: '#5F95FF',
                        strokeWidth: 1,
                        fill: '#fff',
                        style: {
                            visibility: 'hidden'
                        }
                    }
                }
            }
        },
        items: [
            {
                group: 'top'
            },
            {
                group: 'right'
            },
            {
                group: 'bottom'
            },
            {
                group: 'left'
            }
        ]
    }
})

export const FlowChartAnimateText = Graph.registerNode(
    'flow-chart-animate-text1',
    {
        inherit: 'rect',
        width: 200,
        height: 60,
        attrs: {
            body: {
                stroke: '#5F95FF',
                strokeWidth: 1,
                fill: 'rgba(95,149,255,0.05)'
            },
            text1: {
                class: 'animate-text1',
                text: 'AntV X6',
                fontSize: 32
            },
            text2: {
                class: 'animate-text2',
                text: 'AntV X6',
                fontSize: 32
            }
        },
        markup: [
            {
                tagName: 'rect',
                selector: 'body'
            },
            {
                tagName: 'text',
                selector: 'text1'
            },
            {
                tagName: 'text',
                selector: 'text2'
            }
        ]
    }
)

export class NodeGroup extends Node {
    // private collapsed: boolean = true

    // protected
    postprocess() {
        // this.collapsed = true
        this.toggleCollapse()
    }

    isCollapsed() {
        return this.data.collapsed
    }

    toggleCollapse() {
        const target = this.data.collapsed !== null ? this.data.collapsed : false
        //获取节点的width、height
        if (target) {
            this.attr('buttonSign', { d: 'M 1 5 9 5 M 5 1 5 9' })
            this.resize(this.data.nodeWidth, 40)
        } else {
            this.attr('buttonSign', { d: 'M 2 5 8 5' })
            this.resize(this.data.nodeWidth, this.data.nodeHeight)
        }
        //this.collapsed = target
    }
}

NodeGroup.config({
    shape: 'rect',
    width: 200,
    height: 40,
    data: {
        nodeWidth: 200,
        nodeHeight: 200
    },
    markup: [
        {
            tagName: 'rect',
            selector: 'body'
        },
        {
            tagName: 'text',
            selector: 'label'
        },
        {
            tagName: 'g',
            selector: 'buttonGroup',
            children: [
                {
                    tagName: 'rect',
                    selector: 'button',
                    attrs: {
                        'pointer-events': 'visiblePainted'
                    }
                },
                {
                    tagName: 'path',
                    selector: 'buttonSign',
                    attrs: {
                        fill: 'none',
                        'pointer-events': 'none'
                    }
                }
            ]
        }
    ],
})

Graph.registerNode('networkGroupNode1', NodeGroup)
