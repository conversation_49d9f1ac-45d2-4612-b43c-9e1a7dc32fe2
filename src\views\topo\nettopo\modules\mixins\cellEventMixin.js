import { getAction, postAction, httpAction } from '@/api/manage'

export default {
  data() {
    return {
      showTipTimer: null,
      enterNode: null,//鼠标进入的节点 用于判断鼠标从弹窗进入节点时不隐藏弹窗
      addvVirNode: null,
      replaceNode: null,
      onDraw: false,
      temLine: null,
      sourcePos: null,
      targetPos: null,
      startDragNode: null,
      endDragNode: null,
      tipLeft: -10000,
      tipTop: -10000,
      tipWidth: 0,
      tipHeight: 0,
      tipShow: false,
      cellInfo: null,
      tipData: null,
      tipZindex: -1,
      tipShowTimer: null,
      tipHideTimer: null,
      tipReady: false,
      nodeHl: { body: { opacity: 1 }, label: { opacity: 1 }, image: { opacity: 1 } },
      nodeTr: { body: { opacity: 0.1 }, label: { opacity: 0.1 }, image: { opacity: 0.1 } },
      isInNode:false
    }
  },
  methods: {
    //设置节点发光
    setNodeBlur(node, blurW, color = '#fff') {
      node.attr('body/filter', {
        name: 'dropShadow',
        args: {
          color: color,
          dx: 0,
          dy: 0,
          blur: blurW
        }
      })
    },
    //悬浮节点关系效果
    curNodeHighlight(node){
      //节点阴影滤镜
      node.attr('body/filter', {
        name: 'dropShadow',
        args: {
          color: node.attrs.body.fill,
          dx: 0,
          dy: 0,
          blur: 3
        }
      })
      //展示当前节点关联节点和连线
      let edges = this.graph.getEdges()
      let cEdges = this.graph.getConnectedEdges(node)
      edges.forEach(edge => {
        if (!cEdges.includes(edge)) {
          edge.setAttrs({ line: { strokeOpacity: 0.1 } })
          if (edge.getLabelAt(0)) {
            let label = JSON.parse(JSON.stringify(edge.getLabelAt(0)))
            if(label.attrs.text){
              label.attrs.text.opacity = 0.1
            }
            if(label.attrs.rect){
              label.attrs.rect.opacity = 0.1
            }
            edge.setLabelAt(0, label)
          }
        } else {
          edge.setAttrs({ line: { strokeOpacity: 1 } })
        }
      })
      let nodes = this.graph.getNodes()
      nodes.forEach(el => {
        if (cEdges.length > 0 && !this.graph.isNeighbor(node, el)) {
          el.setAttrs(this.nodeTr)
        } else if (cEdges.length === 0 && el !== node) {
          el.setAttrs(this.nodeTr)
        } else {
          el.setAttrs(this.nodeHl)
        }
      })
    },
    //节点信息弹窗显示
    nodeInfoShow(node){
      const nodeView = this.graph.findView(node)
      const svgNode = nodeView.container
      const bbox = svgNode.getBBox()
      let scale = this.graph.scale()
      this.tipWidth = bbox.width * scale.sx
      this.tipHeight = bbox.height * scale.sy
      let tp = this.graph.localToClient(node.position())
      this.tipLeft = tp.x
      this.tipTop = tp.y
      this.tipZindex = -1
      this.tipShow = true
    },
    //清除节点关系效果
    clearNodeHighlight(node){
      node.attr('body/filter', {
        name: 'dropShadow',
        args: {
          color: "transparent",
          dx: 0,
          dy: 0,
          blur: 0
        }
      })
      let edges = this.graph.getEdges()
      edges.forEach(edge => {
        edge.setAttrs({ line: { strokeOpacity: 1 } })
        if (edge.getLabelAt(0)) {
          let label = JSON.parse(JSON.stringify(edge.getLabelAt(0)))
          if(label.attrs.text){
            label.attrs.text.opacity =1
          }
          if(label.attrs.rect){
            label.attrs.rect.opacity = 1
          }
          edge.setLabelAt(0, label)
        }

      })
      let nodes = this.graph.getNodes()
      nodes.forEach(node => {
        node.setAttrs(this.nodeHl)
      })
    },
    //替换节点后检查节点的连接线 同时修改连线的相关信息
    replaceNodeCheckEdge(node) {
      // console.log("替换节点的连线 === ", edges)
      let edges = this.graph.getConnectedEdges(node)
      edges.forEach(el => {
        let sourceNode = el.getSourceNode()
        let targetNode = el.getTargetNode()
        if (sourceNode.data.nodeType !== 'app_auto_node' && targetNode.data.nodeType !== 'app_auto_node') {
          this.setEdgeData(el)
          this.setEdgeStatus(el)
        }
      })

    },
    //选择虚拟节点 弹出类型弹窗
    chooseVirtual() {
      this.$message.success('请选择虚拟节点的类型！')
      this.$refs.chooseType.show('replace')
    },
    //重置节点设备
    resetNodeDevice(data) {
      if (this.replaceNode) {
        this.replaceNode.setData({
          deviceId: data.id,
          deviceCode: data.code,
          deviceName: data.name,
          nodeType: 'device',
          isVirtual: false,
          nodeScore: 0
        })
        this.replaceNode.setAttrs({
          label: {
            text: data.name,
            fontSize: 13,
            fill: this.globalGridAttr.topoConfig.labelColor
          },
          image: {
            // 'xlink:href': this.picSrc + '/' + data.icon,
            'xlink:href': data.icon.includes('http') ? data.icon : this.picSrc + '/' + data.icon
          }
        })
        this.replaceNodeCheckEdge(this.replaceNode)
        this.getInternetType([data.code], true).then((res) => {
          let rData = res[0]
          if (rData.success) {
            if (rData.result.status === 2) {
              let alarm = this.$store.getters.alarmInfo.find(el => el.deviceCode === data.deviceCode)
              // console.log("设备属于告警 === ", alarm)
              this.setNodeAlarm(this.replaceNode, alarm.level)
            } else {
              this.setNodeOnAndOff(this.replaceNode, rData.result.status)
            }

          }
          this.replaceNode = null
        })
        // console.log("选择设备xinxi == ", this.replaceNode, data)
      }
    },
    //创建用于分割装饰边线节点
    createDecorationEdge() {
      return this.graph.addEdge(
        {
          source: this.sourcePos,
          target: this.targetPos,
          attrs: {
            line: {
              stroke: '#3c4260',
              strokeWidth: 1,
              strokeDasharray: '0',
              sourceMarker: {
                name: '',
                size: 8
              },
              targetMarker: {
                name: '',
                size: 8
              }
            }
          },
          connector: { name: 'normal' },
          router: {
            name: 'normal'
          },
          data: {
            edgeType: 'decoration'
          }
        }
      )
    },
    //创建拖拽节点
    createDragNode(point, type) {
      let node = this.graph.createNode(
        {
          shape: 'edgeDragNode',
          x: point.x - 2,
          y: point.y - 2,
          data: {
            dragType: type,
            nodeType: 'edgeDrag'
          }
        }
      )
      return this.graph.addNode(node)
    },
    //划线
    drawLine() {
      this.onDraw = !this.onDraw
      this.graph.togglePanning()
    },
    //显示节点的锚点
    showPorts(node) {
      if (this.operate === 'create') {
        if (node.hasPorts()) return
        if (node.data.nodeType !== 'device' && node.data.nodeType !== 'topo' && node.data.nodeType !== 'dept') return
        let portdata = [
          {
            group: 'top'
          },
          {
            group: 'bottom'
          },
          {
            group: 'left'
          },
          {
            group: 'right'
          }
        ]
        node.addPorts(portdata, { silent: false })
        this.portNode = node
      }
    },
    // 隐藏节点的锚点
    hidePorts() {
      if (this.operate === 'create') {
        let nodes = this.graph.getNodes()
        nodes.forEach(el => {
          el.removePorts(null, { silent: false })
        })
      }
    },
    clearShowTipTimer() {
      if (this.showTipTimer) {
        clearTimeout(this.showTipTimer)
        this.showTipTimer = null
      }
    },
    // 设置连线的业务数据
    setEdgeData(edge) {
      let sourceNode = edge.getSourceNode()
      let targetNode = edge.getTargetNode()
      let data = edge.data
      if (!data.fromDevice || !data.toDevice) {
        data.fromDevice = sourceNode.data.deviceCode || sourceNode.data.deviceId
        data.fromDeviceName = sourceNode.data.deviceName
        data.toDevice = targetNode.data.deviceCode || targetNode.data.deviceId
        data.toDeviceName = targetNode.data.deviceName
        data.fromPort = ''
        data.toPort = ''
        data.ljlx = 1
      }
      data.edgeType = 'connecting'
    },
    //设置虚拟节点的数据
    setVirtualType(name, type, key, scene) {
      getAction('/product/product/list', {
        displayName: name,
        column: 'createTime',
        order: 'desc',
        field: 'id',
        pageNo: 1,
        pageSize: 8
      }).then(res => {
        if (res.success) {
          let data = res.result.records.find(el => el.id === key)
          let icons = data.icon.split(',')
          if (scene === 'virtual') {
            if (this.addvVirNode) {
              // this.addvVirNode.attr("image/xlink:href", this.picSrc + '/' + icons[0])
              this.addvVirNode.attr('image/xlink:href', icons[0].includes('http') ? icons[0] : this.picSrc + '/' + icons[0])
              this.addvVirNode.data.productId = data.id
              this.addvVirNode.data.productName = data.displayName
              this.addvVirNode.attr('label/text', data.displayName)
              // console.log("获取到的虚拟节点的类型 == ",this.addvVirNode)
              this.addvVirNode = null
            }
          } else if (scene === 'replace') {
            //应用自动拓扑替换虚拟节点
            if (this.replaceNode) {
              this.replaceNode.setData({
                status: 1,
                nodeType: 'device',
                isVirtual: true,
                nodeScore: 0
              })
              this.replaceNode.setAttrs({
                body: {
                  stroke: 'none',
                  fill: 'rgba(0,128,0,0.8)',
                  opacity: 1
                },
                label: {
                  fontSize: 13,
                  fill: this.globalGridAttr.topoConfig.labelColor
                },
                image: {
                  // 'xlink:href': this.picSrc + '/' + icons[0],
                  'xlink:href': icons[0].includes('http') ? icons[0] : this.picSrc + '/' + icons[0]
                }
              })
              console.log('替换为虚拟节点', this.replaceNode)
              this.replaceNodeCheckEdge(this.replaceNode)
              this.replaceNode = null
            }

          }


        }

      })
    },
    // 拓扑图 连线/节点等的事件注册
    setup() {
      //鼠标移入边事件
      this.graph.on('edge:mouseenter', ({ e, edge, view }) => {
        // console.log("edge === ",edge)
        if (this.operate === 'create' && !this.globalGridAttr.infoPopup) return
        this.$refs.topoTip.hide()
        //每一次都重新设置timer，就是要保证每一次执行的至少 0.5s 秒后才可以执行
        this.clearShowTipTimer()
        // 被选中的连线触发提示弹窗
        if (this.graph.isSelected(edge)) return
        this.showTipTimer = setTimeout(() => {
          let sourceNode = edge.getSourceNode()
          let targetNode = edge.getTargetNode()
          //此处判读是不是连接群组节点的连线，设置连线真是的起始节点
          if (edge.data.groupEdge) {
            if (edge.data.groupTargetNodeId) {
              targetNode = this.graph.getCellById(edge.data.groupTargetNodeId)
            }
            if (edge.data.groupSourceNodeId) {
              sourceNode = this.graph.getCellById(edge.data.groupSourceNodeId)
            }
          }
          this.edgeConnectNode = {
            source: sourceNode.attr('label/text') || '',
            target: targetNode.attr('label/text') || ''
          }
          let sourceId = sourceNode.data.deviceCode
          let targetId = targetNode.data.deviceCode
          if (sourceId && targetId) {
            this.getInternetType([sourceId, targetId], false).then((data) => {
              if (!!data[0].success) {
                this.edgeConnectNode = {
                  ...this.edgeConnectNode,
                  ...{
                    srInFlow: data[0].result?.inSpeed,
                    srOutFlow: data[0].result?.outSpeed
                  }
                }
              }
              if (!!data[1].success) {
                this.edgeConnectNode = {
                  ...this.edgeConnectNode,
                  ...{
                    trInFlow: data[1].result?.inSpeed,
                    trOutFlow: data[1].result?.outSpeed
                  }
                }
              }
              this.$refs.topoTip.show({
                type: 'EdgeTip',
                pos: e,
                cell: edge,
                edgeConnectNode: this.edgeConnectNode
              })
            }).catch(err => {
              this.$refs.topoTip.show({
                type: 'EdgeTip',
                pos: e,
                cell: edge,
                edgeConnectNode: this.edgeConnectNode
              })
            })
          } else {
            this.$refs.topoTip.show({
              type: 'EdgeTip',
              pos: e,
              cell: edge,
              edgeConnectNode: this.edgeConnectNode
            })
          }
        }, 200)
      })
      // 鼠标移出连线事件
      this.graph.on('edge:mouseleave', () => {
        this.clearShowTipTimer()
        this.$refs.topoTip.hide()
      })
      //鼠标移入节点事件
      this.graph.on('node:mouseenter', ({ e, node, view }) => {
        this.showPorts(node)
        if (this.operate === 'create' && !this.globalGridAttr.infoPopup) {
          return
        }

        e.stopPropagation()
        this.enterNode = node
        this.clearShowTipTimer()
        if (this.graph.isSelected(node)) return
        this.isInNode = true
        this.showTipTimer = setTimeout(() => {
          if (['device'].includes(node.data.nodeType) && node.data.deviceCode) {
            this.getInternetType([node.data.deviceCode], true).then((result) => {
              // console.log("节点信息系 ==== ",result)
              if(!this.isInNode)return;
              let nodeAlarm = this.$store.getters.deviceAlarms.find(el => el.deviceCode === node.data.deviceCode)
              // console.log("告警信息 ==== ",nodeAlarm)
              let res = result[0]
              if (res.success && Object.keys(res.result).length !== 0) {
                let resu = res.result
                this.cellInfo = node
                this.showDevcInfo = {
                  name: node.data.name || resu.name || '',
                  status: node.data.status,
                  ip: resu.ip || '',
                  cpu: resu.cpu || '',
                  mem: resu.mem || '',
                  inSpeed: resu.inSpeed || '',
                  outSpeed: resu.outSpeed || '',
                  healthlevel: resu.healthlevel || '',
                  healthDegree: resu.healthDegree,
                  risklevel: resu.risklevel || '',
                  riskDegree: resu.riskDegree
                }
                this.tipData = {
                  shape: 'node',
                  type: this.globalGridAttr.topoType,
                  cell: node,
                  showDevcInfo: Object.assign({}, this.showDevcInfo),
                  alarmInfo: nodeAlarm ? nodeAlarm.alarmInfo : null,
                  alarmLevelList: this.alarmLevelList,
                  onOffColors: this.onOffColors,
                  alarmColor:this.alarmColor
                }
                this.nodeInfoShow(node)
                //节点阴影滤镜
                this.curNodeHighlight(node)
              }
            })
          }
          else if(['dept'].includes(node.data.nodeType) && node.data.deviceCode){
            let promise1 =  getAction("/monitor/situation/getNodeStatusAndDevList",{
              rootDeptId:node.data.deptId,
              withNodeDev:"true",
              withChildBusiness:"true",
              withChildDev:"true"})
            let promise2 =  getAction("/monitor/situation/getPortCountDataForDev",{deviceCode:node.data.deviceCode,})
            Promise.all([promise1,promise2]).then((allRes)=>{
              if(!this.isInNode)return;
              let statusObj = allRes[0].result && allRes[0].result[0] && allRes[0].result[0].nodeDevObject?allRes[0].result[0].nodeDevObject:{}
              let portObj = allRes[1].result?allRes[1].result:{}
              this.cellInfo = node
              this.showDevcInfo = {
                businessCount:allRes[0].result[0]?.businessCount || 0,
                devCount:allRes[0].result[0]?.devInfoArray?.length || 0,
                ...statusObj,
                ...portObj,
              }
              this.tipData = {
                shape: 'node',
                type: this.globalGridAttr.topoType,
                cell: node,
                showDevcInfo: Object.assign({}, this.showDevcInfo),
                alarmInfo:  null,
                alarmLevelList: this.alarmLevelList,
                onOffColors: this.onOffColors,
                alarmColor:this.alarmColor
              }
              this.nodeInfoShow(node)
              //节点阴影滤镜
              this.curNodeHighlight(node)
            })
          }else if(['dept'].includes(node.data.nodeType)){
            this.cellInfo = node
            this.showDevcInfo = {
              businessCount: "--",
              devCount: "--",
              status:1,
              alarmInfo:0,
              portNum:"--",
            }
            this.tipData = {
              shape: 'node',
              type: this.globalGridAttr.topoType,
              cell: node,
              showDevcInfo: Object.assign({}, this.showDevcInfo),
              alarmInfo:  null,
              alarmLevelList: this.alarmLevelList,
              onOffColors: this.onOffColors,
              alarmColor:this.alarmColor
            }
            this.nodeInfoShow(node)
            //节点阴影滤镜
            this.curNodeHighlight(node)
          }
        }, 200)

      })
      //鼠标移出节点事件
      this.graph.on('node:mouseleave', ({ e, node, view }) => {

        this.isInNode = false;
        this.clearShowTimer()
        this.clearHideTimer()
        this.tipHideTimer = setTimeout(() => {
          clearTimeout(this.tipHideTimer)
          this.tipHideTimer = null
          this.toolTipHide()
          this.clearNodeHighlight(node)
        }, 150)
        this.hidePorts()
        this.enterNode = null
        this.clearShowTipTimer()
        this.$refs.topoTip.hide()
        this.alarmInfo = null
        this.showDevcInfo = {
          name: '',
          status: '',
          ip: '',
          cpu: '',
          mem: ''
        }
      })
      //节点双击事件
      this.graph.on('node:dblclick', ({ e, x, y, node, view }) => {
        if (this.operate === 'create') return
        if (this.isHideDeviceDetailModal) return
        if(['dept'].includes(node.data.nodeType))return;
        if (node.data.deviceCode) {
          getAction(this.url.deviceDetailInfo, {
            code: node.data.deviceCode
          }).then((res) => {
            if (res.success) {
              if (this.isBusiness) {
                this.$emit('businessDetail', res.result)
                return
              } else {
                this.$refs.modalForm.edit(res.result)
                this.$refs.modalForm.title = '详情'
                this.$refs.modalForm.disableSubmit = true
              }

            }
          })
        }
      })
      // 节点鼠标右键点击事件
      this.graph.on('node:contextmenu', ({ e, node, view }) => {
        if (this.operate === 'create' && !this.globalGridAttr.infoPopup) return
        if (this.globalGridAttr.topoType === '1' || this.globalGridAttr.topoType === '2') return
        this.clearShowTipTimer()
        this.$refs.topoTip.hide(1)
        if (this.graph.isSelected(node)) return
        if (node.data.deviceCode) {
          this.$refs.topoTip.show({
            type: 'TestLink',
            cell: node,
            pos: e
          })
        }

      })
      //  鼠标进入拓扑图画布内
      this.graph.on('graph:mouseenter', ({ e }) => {
        this.$refs.topoTip.hide()
      })
      // 点击拓扑图画布空白区域
      this.graph.on('blank:click', (e) => {
        this.clearShowTipTimer()
        this.$refs.topoTip.hide()
      })
      // 点击节点触发
      this.graph.on('cell:click', ({ x, y, cell }) => {
        if(this.globalGridAttr.topoType === '2'&&this.operate==='show' && this.scene==="ywts_bigscreen" && cell.data.deptId){
          getAction("/monitor/situation/getNetTopoByDeptId",{ deptId:cell.data.deptId}).then(res=>{
            if(res.success && res.result && res.result.id){
              const topoId = res.result.id
              this.$router.push({
                path: '/operationsView/comprehensive',
                query: {
                  topoId,
                  deptId:cell.data.deptId,
                }
              })
            }else{
              this.$message.error('该单位还没有关联拓扑图')
            }
          }).catch(err=>{
            this.$message.error('获取单位的关联拓扑图失败')
          })

          return
        }
        if (cell.data.nodeType === 'app_auto_node') {
          this.graph.cleanSelection()
          this.replaceNode = cell
          this.$refs.chooseNodeDevice.show()
          return
        }
        if (cell.data.edgeType === 'app_auto_edge') {
          this.graph.cleanSelection()
          return
        }
        // console.log('监听到了 === ', cell.getData())
        this.clearShowTipTimer()
        this.$refs.topoTip.hide(1)
        //点击子拓扑跳转判断
        if (cell.isNode() && cell.getData().nodeType === 'topo') {
          this.showSonTopo(cell)
        }
      })
      // 群组节点注册的折叠事件
      this.graph.on('node:collapse', ({ node, e }) => {
        e.stopPropagation()
        const collapsed = (node.data.collapsed = !node.data.collapsed)
        node.toggleCollapse()
        this.graph.cleanSelection()
        const cells = node.getDescendants()
        if (cells === null || cells === undefined) return
        if (cells.length === 0) return
        const edges = cells.filter(el => el.isEdge())
        const edgesIds = edges.map(el => el.id)
        cells.forEach((n) => {
          if (collapsed) {
            let inEdges = this.graph.getIncomingEdges(n) //输入边
            let outEdges = this.graph.getOutgoingEdges(n) //输出边
            if (inEdges) {
              inEdges.forEach(el => {
                el.setData({
                  groupEdge: true,
                  groupTargetNodeId: n.id
                  // groupNodePos: "target"
                })
                el.setTarget(node)
              })
            }
            if (outEdges) {
              outEdges.forEach(el => {
                el.setData({
                  groupEdge: true,
                  groupSourceNodeId: n.id
                  // groupNodePos: "source"
                })
                el.setSource(node)
              })
            }

            n.hide()
          } else {
            n.show()
            const groupEdges = this.graph.getConnectedEdges(node)
            if (groupEdges) {
              for (let i = 0; i < groupEdges.length; i++) {
                let el = groupEdges[i]
                if (el.data.groupTargetNodeId === n.id) {
                  el.setTarget(n)
                  el.setData({
                    groupEdge: el.data.groupSourceNodeId ? true : false,
                    groupTargetNodeId: ''
                  })

                }
                if (el.data.groupSourceNodeId === n.id) {
                  el.setSource(n)
                  el.setData({
                    groupEdge: el.data.groupTargetNodeId ? true : false,
                    groupSourceNodeId: ''
                  })
                }
              }
            }
          }
        })
      })

      //将边连接到节点/边或者将边从节点/边上分离后触发
      this.graph.on('edge:connected', ({ isNew, edge }) => {
        // console.log("线段链接成功 === ",edge)
        let source = edge.getSourceCellId()
        edge.setSource({ cell: source })
        // 连线连接完成更新连线状态
        this.setEdgeData(edge)
        this.hidePorts()
        this.setEdgeStatus(edge)

      })


      // 当边被添加到画布时
      this.graph.on('edge:added', ({ e, edge }) => {
        edge.zIndex = 2
      })
      // 节点被添加时触发
      this.graph.on('node:added', ({ e, node }) => {
        //节点添加时判断是否时边线节点 生成边线；
        if (node.data.nodeType === 'edgeDrag') {
          if (node.data.addLine) {
            let pos = node.position()
            this.sourcePos = { x: pos.x + 5, y: pos.y + 5 }
            this.targetPos = { x: pos.x + 5 + 100, y: pos.y + 5 }
            this.createDecorationEdge()
            this.graph.removeNode(node)
            // console.log("添加了拖拽节点",node,pos)
          }
          return
        }
        if (node.data.nodeType === "device" && node.data.isVirtual && node.data.productId === '') {
          this.addvVirNode = node
          this.$refs.chooseType.show('virtual')
          return
        }
        if (node.shape === 'networkGroupNode') {
          node.zIndex = 1
        } else {
          node.zIndex = 5
        }

        // 节点添加到拓扑图上更新设备的在线离线状态
        if (node.data.deviceCode) {
          this.getInternetType([node.data.deviceCode], true).then((res) => {
            let data = res[0]
            if (data.success) {
              if (data.result.status === 2) {
                let alarm = this.$store.getters.alarmInfo.find(el => el.deviceCode === node.data.deviceCode)
                // console.log("设备属于告警 === ", alarm)
                this.setNodeAlarm(node, alarm.level)
              } else {
                this.setNodeOnAndOff(node, data.result.status)
              }

            }
          })
        }
      })

      // 暂不使用划线功能
      // this.graph.on("blank:mousedown", ({ e }) => {
      //     if (this.onDraw) {
      //         this.sourcePos = this.graph.pageToLocal(Math.floor(e.clientX), Math.floor(e.clientY));
      //         this.startDragNode = this.createDragNode(this.sourcePos, "start")
      //         // console.log("监听到在空白处的按下鼠标 === ", e, point)
      //     }
      // })
      this.graph.on('blank:mousemove', ({ e }) => {
        if (this.onDraw) {
          this.targetPos = this.graph.pageToLocal(Math.floor(e.clientX), Math.floor(e.clientY))
          if (this.temLine === null) {
            this.temLine = this.createDecorationEdge()
            this.endDragNode = this.createDragNode(this.targetPos, 'end')
          } else {
            this.temLine.setTarget(this.targetPos)
            this.endDragNode.position(this.targetPos.x - 2, this.targetPos.y - 2)
          }
          // console.log("监听到在空白处移动 === ", e)
        }
      })
      // this.graph.on("blank:mouseup", ({ e }) => {
      //     if (this.onDraw) {
      //         this.onDraw = false;
      //         this.graph.enablePanning();
      //         this.graph.removeNode(this.startDragNode);
      //         this.graph.removeNode(this.endDragNode);
      //         this.endDragNode = null;
      //         this.startDragNode = null;
      //         this.temLine = null;
      //     }
      // })

    },
    //鼠标移入tip卡片
    enterTipCard(e) {
      this.clearHideTimer()
    },
    //鼠标移出tip卡片
    leaveTipCard() {
      this.clearHideTimer()
      this.tipHideTimer = setTimeout(() => {
        this.clearHideTimer()
        let node = this.cellInfo
        this.toolTipHide()
        this.clearNodeHighlight(node)
      }, 150)

    },
    //节点或边线点击的事件
    cellClick() {
      if (this.cellInfo.shape === 'edge') {
        this.toolTipHide()
        let sourceNode = this.graph.getCellById(this.cellInfo.source.cell)
        let targetNode = this.graph.getCellById(this.cellInfo.target.cell)
        const params = {
          linkInfo: this.cellInfo.data,
          sourceData: sourceNode.data,
          targetData: targetNode.data
        }
      }

    },
    clearShowTimer() {
      if (this.tipShowTimer) {
        clearTimeout(this.tipShowTimer)
        this.tipShowTimer = null
      }
    },
    clearHideTimer() {
      if (this.tipHideTimer) {
        clearTimeout(this.tipHideTimer)
        this.tipHideTimer = null
      }
    },
    toolTipHide() {
      this.tipLeft = -10000
      this.tipTop = -10000
      this.tipWidth = 0
      this.tipHeight = 0
      this.tipShow = false
      this.tipZindex = -1
      this.enterPointer = null
      this.tipReady = false
    }
  }
}