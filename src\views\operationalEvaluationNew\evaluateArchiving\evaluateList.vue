<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class="card-style">
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="报告名称">
                  <a-input placeholder="请输入报告名称" :maxLength='maxLength' :allowClear="true" autocomplete="off"
                    v-model="queryParam.projectName">
                  </a-input>
                </a-form-item>
              </a-col>
              <a-col :span="colBtnsSpan()">
                <span class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button type="primary" class="btn-search btn-search-style" @click="searchQuery">查询</a-button>
                  <a-button class="btn-reset btn-reset-style" @click="searchReset" style="margin-left: 8px">重置
                  </a-button>
                  <a v-if="isVisible" class="btn-updown-style" @click="doToggleSearch">
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered="false" style="width: 100%; flex: auto">
        <a-table ref="table" bordered rowKey="id" :columns="columns" :dataSource="dataSource"
          :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}" :pagination="ipagination" :loading="loading"
          @change="handleTableChange">
          <span slot="action" slot-scope="text, record" class="caozuo">
            <a @click="openPreview(record)">预览</a>
            <a-divider type="vertical" />
            <a v-if="!!record.projectReportText" @click="downloadAIFile(record.projectReportFile)">下载</a>
          </span>
          <span slot="evaluateTime" slot-scope="text, record">{{ record.startTime + '~' + record.endTime }}</span>
        </a-table>
      </a-card>
      <preview ref="preview"></preview>
    </a-col>
  </a-row>
</template>

<script>
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import preview from '@views/operationalEvaluationNew/operationalOrganization/modules/preview.vue'
  import {
    downloadFile
  } from '@/api/manage'
  import {
    YqFormSearchLocation
  } from '@/mixins/YqFormSearchLocation'

  export default {
    name: 'evaluateList',
    mixins: [JeecgListMixin, YqFormSearchLocation],
    components: {
      preview
    },
    data() {
      return {
        maxLength: 50,
        description: '评估归档管理页面',
        formItemLayout: {
          labelCol: {
            style: 'width:90px',
          },
          wrapperCol: {
            style: 'width:calc(100% - 90px)',
          },
        },
        queryParam: {},
        columns: [{
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: 'center',
            isUsed: false,
            customRender: function (t, r, index) {
              return parseInt(index) + 1
            },
          },
          {
            title: '报告名称',
            dataIndex: 'projectName',
          },
          {
            title: '发起人',
            dataIndex: 'sender',
          },
          // {
          //   title: '评估指标',
          //   dataIndex: 'metricsNameStr',
          // },
          {
            title: '评估周期',
            dataIndex: 'evaluateTime',
            scopedSlots: {
              customRender: 'evaluateTime',
            },
          },
          {
            title: '操作',
            dataIndex: 'action',
            scopedSlots: {
              customRender: 'action',
            },
            fixed: 'right',
            align: 'center',
            width: 220,
          },
        ],
        url: {
          list: '/devops/projectInfo/projectRecordList',
        },
      }
    },
    created() {},
    methods: {
      openPreview(record) {
        this.$refs.preview.show(record)
      },
      handleDetailPage: function (record) {
        this.$parent.pButton2(1, record)
      },
      downloadAIFile(text) {
        if (!text) {
          this.$message.warning("没有报告文件路径！")
          return
        }
        let url = window._CONFIG['domianURL'] + '/sys/common/downloadFile/' + text
        downloadFile(url, text)
      },
    },
  }
</script>
<style lang="less" scoped>
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';
</style>