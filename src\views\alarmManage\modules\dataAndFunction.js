import { getAction } from '@api/manage'
import { queryConfigureDictItem,ajaxGetDictItems} from '@api/api'

export const dataAndFunction = {
  data() {
    return {
      sysUser: this.$store.getters.userInfo.username,
      /**产品名称下拉数据*/
      productList: [],
      /**分组名称下拉数据*/
      groupList: [],
      /**告警级别下拉数据*/
      alarmLevelList: [],
      /**告警最高级别*/
      maxAlarmLevle: '',
      /**告警最低级别*/
      minAlarmLevle: '',
      /**告警状态*/
      alarmStatusList: [],
      /**处理状态数组
       * @0-未处理
       * @1-处理中
       * @2-处理完成
       * @3-关闭*/
      handleStatusList: [
        {
          value: '0',
          label: '未处理'
        },
        {
          value: '1',
          label: '处理中'
        },
        {
          value: '2',
          label: '已完成'
        },
        {
          value: '3',
          label: '关闭'
        }
      ],
      url: {
        queryAllProducts: '/alarm/alarmHistory/queryAllProduct', //查询所有产品
        alarmLevel: '/alarm/alarmLevel/getLevelList', //获取告警级别所有数据
        alarmClose: '/alarm/alarmHistory/alarmClose', //关闭
        alarmCloseBatch: '/alarm/alarmHistory/alarmCloseBatch', //批量关闭
        deviceInfo: '/device/deviceInfo/queryById', //通过设备id，查找设备信息
        alarmTemplateInfo: '/alarm/alarmTemplate/queryById', //通过告警策略模板id，查找策略信息
        updateAlarmLevel: '/alarm/alarmHistory/updateAlarmLevel',//告警升降级
        queryGroupList: '/device/deviceGroup/getGroupList'//查询分组名称下拉数据
      },

      /**使用于告警处理、告警任务*/
      canBatchClaim: false,//true可批量认领，false不可批量认领
      canBatchChangeResponsibleUser: false,//true可批量转移责任人，false不可批量转移责任人
      canBatchClose: false,//true可批量关闭，false不可批量关闭
      allSelectedRows: [],//已选的所有行信息

      /**告警流程处理相关变量*/
      processDefinitionKey: '',
      associationId: '',
      formUrl: '',
      startUrl: '',
      dialogStartInstanceVisible: false,
      processDefinition: undefined,
      alarmHistory: {}
    }
  },
  methods: {
    /********告警信息、告警处理、告警任务共有方法*********/
    /*获取数据字典*/
    getDict(code,key){
      //根据字典Code, 初始化字典数组
      ajaxGetDictItems(code, null).then((res) => {
        this[key] = res.result?res.result:undefined
      })
    },
    /**根据告警状态获取告警状态中文名*/
    getAlarmStatus(record) {
      if (this.alarmStatusList.length > 0) {
        let status = record.alarmStatus
        let temp = this.alarmStatusList.filter((item) => {
          if (item.value == status) {
            return true
          }
        })
        if (temp.length > 0 && temp[0].title) {
          return temp[0].title
        }else {
          return ''
        }
      }
      return ''
    },
    /**获取所有产品信息*/
    queryAllProducts() {
      if (!this.url.queryAllProducts) {
        this.$message.error('请设置url.queryAllProducts!')
        return
      }
      getAction(this.url.queryAllProducts).then((res) => {
        this.productList = []
        if (res.success) {
          if (res.result.length > 0) {
            res.result.map((item) => {
              let param = {
                proId: item.id,
                proName: item.displayName
              }
              this.productList.push(param)
            })
          }
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    /**获取告警级别信息*/
    getAlarmLevelData() {
      let that = this
      that.alarmLevelList = []
      that.maxAlarmLevle = ''
      that.minAlarmLevle = ''
      getAction(that.url.alarmLevel,{pageSize:-1}).then((res) => {
        if (res.success) {
          that.alarmLevelList = res.result
          if (that.alarmLevelList.length > 0) {
            that.alarmLevelList.sort((a, b) => {
              return a.value - b.value
            })
            that.maxAlarmLevle = that.alarmLevelList.reduce((max, item) => Math.max(max, item.value), -Infinity)
            that.minAlarmLevle = that.alarmLevelList.reduce((min, item) => Math.min(min, item.value), Infinity)

          }
        }
      })
    },
    /**根据告警级别等级值获取告警等级颜色*/
    getAlarmColor(record) {
      if (this.alarmLevelList.length > 0) {
        let level = record.alarmLevel
        let temp = this.alarmLevelList.filter((item) => {
          if (item.value == level) {
            return true
          }
        })
        if (temp.length > 0 && temp[0].color) {
          record.alarmLevel_color = temp[0].color
          return temp[0].color
        }
      }
      return 'none'
    },
    /**根据告警级别等级值获取告警等级名称*/
    getAlarmTitle(record) {
      if (this.alarmLevelList.length > 0) {
        let level = record.alarmLevel
        let temp = this.alarmLevelList.filter((item) => {
          if (item.value === level) {
            return true
          }
        })
        if (temp.length > 0 && temp[0].title) {
          record.alarmLevel_dictText = temp[0].title
          return temp[0].title
        }
      }
      return ''
    },
    /**设置升级按钮的可操行*/
    setUpgradeStatus(record) {
      if (this.alarmLevelList.length > 0) {
        let level =  record.alarmLevel
        let index=this.alarmLevelList.findIndex(e=>e.value == level)
         if (index <= -1) return false
        if (level < this.maxAlarmLevle) {
          return true
        }
      }
      return false
    },
    /**设置降级按钮的可操行*/
    setDegradationStatus(record) {
      if (this.alarmLevelList.length > 0) {
        let level=record.alarmLevel
        let index=this.alarmLevelList.findIndex(e=>e.value == level)
        if (index <= -1) return false
        if (level > this.minAlarmLevle) {
          return true
        }
      }
      return false
    },
    /**根据处理状态value值，获取label值*/
    getHandlingStatus(value) {
      let temp = this.handleStatusList.find((item) => {
        if (item.value == value) {
          return true
        }
        return false
      })
      return temp ? temp.label : '未知'
    },
    /**根据处理状态value值，设置列表可选性
     * @value--处理状态为未处理时，数据可选；处理状态为处理中、处理完成、关闭状态时，数据不可选*/
    getTableChecked(value) {
      let disabled = false
      if (value != this.handleStatusList[0].value) {
        disabled = true
      }
      return disabled
    },
    /**获取分组名称下拉数据*/
    getGroupList() {
      this.groupList = []
      getAction(this.url.queryGroupList).then((res) => {
        if (res.success) {
          this.groupList = res.result
        } else {
          this.$message.warning(res.message)
        }
      }).catch((err) => {
        this.$message.warning(err.message)
      })
    },
    /**改变时间*/
    onChangeTime(date, dateString) {
      this.queryParam.alarmTime2_begin = dateString[0]
      this.queryParam.alarmTime2_end = dateString[1]
    },
    /**关闭告警*/
    closeAlarm(record) {
      let that = this
      that.$confirm({
        title: '确认关闭',
        okText: '是',
        cancelText: '否',
        content: '是否关闭告警?',
        onOk: function() {
          that.loading = true
          getAction(that.url.alarmClose, { id: record.id })
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.resetData()
              } else {
                that.$message.warning(res.message)
                that.loading = false
              }
            }).catch((err) => {
            that.$message.warning(err.message)
            that.loading = false
          })
        }
      })
    },
    /**批量关闭告警*/
    batchCloseAlarm() {
      let that = this
      that.$confirm({
        title: '确认关闭',
        okText: '是',
        cancelText: '否',
        content: '是否批量关闭告警?',
        onOk: function() {
          that.loading = true
          getAction(that.url.alarmCloseBatch, { ids: that.selectedRowKeys.join(',') })
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.resetData()
              } else {
                that.$message.warning(res.message)
                that.loading = false
              }
            }).catch((err) => {
            that.$message.warning(err.message)
            that.loading = false
          })
        }
      })
    },
    /**单条指定或转移责任人*/
    changeResponsibleUser(record, title, flag = 'assign') {
      this.$refs.modalForm.assign(record, flag)
      this.$refs.modalForm.title = title
      this.$refs.modalForm.disableSubmit = false
    },
    /**批量指定或转移责任人*/
    batchChangeResponsibleUser(title, flag = 'assignBatch') {
      this.$refs.modalForm.batchAssign(this.selectedRowKeys, flag)
      this.$refs.modalForm.title = title
      this.$refs.modalForm.disableSubmit = false
    },
    /**告警升降级
     * @record--告警信息
     * @flag--upgrade告警升级，degradation告警降级*/
    changeAlarmLevel(record, flag) {
      let index = -1
      if (this.alarmLevelList.length > 0) {
        let level = record.alarmLevel
        index=this.alarmLevelList.findIndex(e=>e.value == level)
      }

      if (index !== -1) {
        index = flag === 'upgrade' ? index + 1 : index - 1
        let alarmLevel = this.alarmLevelList[index].value
        getAction(this.url.updateAlarmLevel, { id: record.id, alarmLeve: alarmLevel, grade: flag }).then((res) => {
          if (res.success) {
            this.loadData()
            this.$message.success(res.message)
          } else {
            this.$message.warning(res.message)
          }
        }).catch((err) => {
          this.$message.warning(err.message)
        })
      }
    },
    /**查看设备信息*/
    handleViewDeviceInfo(record) {
      console.log('record===',record)
      getAction(this.url.deviceInfo, {
        id: record.deviceId
      }).then((res) => {
        if (res.success&&res.result&&res.result.records&&res.result.records.length>0) {
          this.$parent.pButton2(2, res.result.records[0])
        }else if(!res.success){
          this.$message.warning(res.message)
        }
      }).catch((err)=>{
        this.$message.error(err.message)
      })
    },
    /**查看告警策略信息*/
    handleViewAlarmTemplateInfo(record) {
      getAction(this.url.alarmTemplateInfo, {
        id: record.templateId
      }).then((res) => {
        if (res.success&&res.result) {
          this.$parent.pButton2(3, res.result)
        }else if(!res.success){
          this.$message.warning(res.message)
        }
      }).catch((err)=>{
        this.$message.error(err.message)
      })
    },
    /**查看告警详情*/
    handleShow(record) {
      this.$refs.infoModal.show(record)
    },


    /********告警处理、告警任务共有方法*********/
    /**获取流程定义key,并根据key,初始化流程相关变量*/
    getProcessDefinitionKey() {
      queryConfigureDictItem({
        parentCode: 'businessToProcesKey',
        childCode: 'alarmProcessKey'
      }).then((res) => {
        if (res.success) {
          this.processDefinitionKey = res.result
        } else {
          this.processDefinitionKey = 'eventProcessShenzhen'
        }
        this.initFlowData()
      })
    },

    /*记录选择的列表项的资产类型id及资产id*/
    updateAllSelectionRows(selectedRowKeys, selectionRows) {
      if (this.allSelectedRows.length > 0) {
        let newArr = []
        //根据selectedRowKeys，保留已存在的，去除不存在的
        for (let j = 0; j < selectedRowKeys.length; j++) {
          let item = undefined
          let selKeys = this.allSelectedRows
          for (let i = 0; i < selKeys.length; i++) {
            if (selKeys[i].id === selectedRowKeys[j]) {
              item = selKeys[i]
            }
          }
          if (item) {
            newArr.push({ responsibleUser: item.responsibleUser, id: item.id, flowHandleStatus: item.flowHandleStatus })
          }
        }
        //根据selectionRows，添加不存在的
        for (let j = 0; j < selectionRows.length; j++) {
          let item = undefined
          let selKeys = this.allSelectedRows
          for (let i = 0; i < selKeys.length; i++) {
            if (selKeys[i].id === selectionRows[j].id) {
              item = selKeys[i]
            }
          }
          if (!item) {
            newArr.push({
              responsibleUser: selectionRows[j].responsibleUser,
              id: selectionRows[j].id,
              flowHandleStatus: selectionRows[j].flowHandleStatus
            })
          }
        }
        this.allSelectedRows = newArr
      } else {
        for (let i = 0; i < selectionRows.length; i++) {
          this.allSelectedRows.push({
            responsibleUser: selectionRows[i].responsibleUser,
            id: selectionRows[i].id,
            flowHandleStatus: selectionRows[i].flowHandleStatus
          })
        }
      }
    },
    /**设置批量认领可操作性*/
    setBatchClaimStatus() {
      this.canBatchClaim = true
      for (let i = 0; i < this.allSelectedRows.length; i++) {
        let res = this.allSelectedRows[i].responsibleUser
        if (res && res.length > 0) {
          this.canBatchClaim = false
          return
        }
      }
    },
    /**设置批量关闭可操作性*/
    setBatchCloseStatus() {
      this.canBatchClose = true
      for (let i = 0; i < this.allSelectedRows.length; i++) {
        let res = this.allSelectedRows[i].responsibleUser
        if (!res || res.length == 0 || this.allSelectedRows[i].flowHandleStatus != this.handleStatusList[0].value) {
          this.canBatchClose = false
          return
        }
      }
    },
    /**设置批量转移责任人可操作性*/
    setBatchChangeResponsibleUserStatus() {
      this.canBatchChangeResponsibleUser = true
      for (let i = 0; i < this.allSelectedRows.length; i++) {
        let res = this.allSelectedRows[i].responsibleUser
        if (!res || res.length == 0 || this.allSelectedRows[i].flowHandleStatus != this.handleStatusList[0].value || (res && res.length > 0 && res !== this.sysUser)) {
          this.canBatchChangeResponsibleUser = false
          return
        }
      }
    },

    /**初始化流程相关变量*/
    initFlowData() {
      getAction('/flowable/processDefinition/queryByKey', { processDefinitionKey: this.processDefinitionKey })
        .then((res) => {
          if (res.success) {
            this.processDefinition = res.result
            this.formUrl = '/association/process/renderedStartForm'
            // this.startUrl= "/alarm/alarmHistory/start"
            this.startUrl = '/association/process/start'
          } else {
            this.$message.warning(res.message)
          }
        }).catch((err) => {
        this.$message.warning(err.message)
      })
    },
    /**处理发起工单*/
    launchOrder(data) {
      if (this.processDefinition.id && this.processDefinition.id.length > 0 && this.processDefinitionKey.length > 0) {
        this.associationId = data.id
        this.alarmHistory = data
        this.alarmHistory.processDefinitionId = this.processDefinition.id
        this.dialogStartInstanceVisible = true
      }
    },


    /********告警信息、告警任务共有方法--查看进度*********/
    /**根据发起流程成功后，返回的associationId值，查看进度*/
    history(record, title) {
      if (!record.associationId) {
        this.$message.error('流程实例ID不存在')
        return
      }
      record.state = record.endTime
      this.$refs.processHistoryModal.init(record.associationId)
      this.$refs.processHistoryModal.title = title
      this.$refs.processHistoryModal.disableSubmit = false
    }
  }
}