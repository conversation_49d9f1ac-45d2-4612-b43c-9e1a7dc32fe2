<template>
  <div style="height:100%">
    <keep-alive exclude='DeviceInfoModal'>
      <component style="height:100%" :is="pageName" :data="data" :is-editing='true' :render-states='renderStates'/>
    </keep-alive>
  </div>
</template>
<script>
  import cloudInfoList from './cloudInfoList'
  import DeviceInfoModal from '@/views/devicesystem/deviceshow/DeviceInfoModal'
  export default {
    name: "cloudInfoMange",
    data() {
      return {
        isActive: 0,
        data: {},
        renderStates:{
          showBaseInfo:true,
          showStateInfo:true,
          showDeviceFunction:true,
          showCloudHost:true,
          showAlarm:true,
        }
      };
    },
    components: {
      cloudInfoList,
      DeviceInfoModal
    },
    created() {
      this.pButton1(0);
    },
    //使用计算属性
    computed: {
      pageName() {
        switch (this.isActive) {
          case 0:
            return "cloudInfoList";
            break;

          default:
            return "DeviceInfoModal";
            break;
        }
      }
    },
    methods: {
      pButton1(index) {
        this.isActive = index;
      },
      pButton2(index, item) {
        this.isActive = index;
        this.data = item;
      }
    }
  }
</script>