<template>
  <a-config-provider :locale="locale">
    <div id="app">
      <router-view style="width: 100%; height: 100%" v-if="isRouterAlive" />
    </div>
  </a-config-provider>
</template>
<script>
import zhC<PERSON> from 'ant-design-vue/lib/locale-provider/zh_CN'
import enquireScreen from '@/utils/device'

export default {
  provide() {
    return {
      reload: this.reload,
    }
  },
  data() {
    return {
      locale: zhCN,
      isRouterAlive: true,
    }
  },
  created() {
    let that = this
    enquireScreen((deviceType,width) => {
      // tablet
      if (deviceType === 0) {
        that.$store.commit('TOGGLE_DEVICE', 'mobile')
        that.$store.dispatch('setSidebar', false)
      }
      // mobile
      else if (deviceType === 1) {
        that.$store.commit('TOGGLE_DEVICE', 'mobile')
        that.$store.dispatch('setSidebar', false)
      } else {
        that.$store.commit('TOGGLE_DEVICE', 'desktop')
        that.$store.dispatch('setSidebar', true)
      }
      // console.log('pc',deviceType,width) 
      if(width !== undefined){
        that.$store.commit('TOGGLE_SCREEN_WIDTH', width)
      }
     
      
    })
  },
  //刷新菜单
  methods: {
    reload() {
      this.isRouterAlive = false
      this.$nextTick(function () {
        this.isRouterAlive = true
      })
    },
  },
}
</script>
<style lang="less">
#app {
  height: 100%;
}
.el-popover {
  min-width: 90px !important;
  background: #000 !important;
  border: none !important;
  display: flex;
  justify-content: center;
  div{
    a{
      letter-spacing: 2px;
      line-height: 26px;
    }
    a:hover {
      color: #fff !important;
    }
  }
}
.el-popper[x-placement^='right'] .popper__arrow {
  border-right-color: #222224 !important;
}
.el-popper[x-placement^='right'] .popper__arrow::after {
  border-right-color: #222224 !important;
  
}
</style>