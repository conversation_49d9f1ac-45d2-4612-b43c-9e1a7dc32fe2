<template>
  <a-spin :spinning='confirmLoading'>
    <j-form-container>
      <a-form :form='form' slot='detail'>
        <a-row>
          <a-col :span='24'>
            <a-form-item label='上级单位名称' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <!--              <a-input v-decorator="['parentDepartName', validatorRules.parentDepartName]" placeholder='请输入单位名称'
                              :allowClear='true' autocomplete='off' />-->
              <a-tree-select placeholder='请选择上级单位'
                             :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }"
                             v-decorator="['parentDepartCode', validatorRules.parentDepartCode]" :allowClear='true'
                             :treeData='parentTree' :replaceFields='{value:"departNameEn",label:"departName"}'
                             @change='handleDepartChange'></a-tree-select>
            </a-form-item>
          </a-col>
          <!--          <a-col :span="24">-->
          <!--            <a-form-item label='上级单位标识' :labelCol='labelCol' :wrapperCol='wrapperCol'>-->
          <!--              <a-input disabled v-decorator="['parentDepartCode', validatorRules.parentDepartCode]" placeholder='请输入单位标识'-->
          <!--                :allowClear='true' autocomplete='off' />-->
          <!--            </a-form-item>-->
          <!--          </a-col>-->
          <a-col :span='24'>
            <a-form-item label='上级平台标识' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <a-input disabled v-decorator="['parentPlatformCode', validatorRules.parentPlatformCode]"
                       placeholder='请输入上级平台标识'
                       :allowClear='true' autocomplete='off' />
            </a-form-item>
          </a-col>
          <a-col :span='24'>
            <a-form-item label='上级服务地址' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <a-input v-decorator="['parentServerAddress', validatorRules.parentServerAddress]"
                       placeholder='请输入服务地址'
                       :allowClear='true' autocomplete='off'>
                <a-tooltip slot='suffix' title='服务地址说明，例 : ************:8091'>
                  <a-icon type='info-circle' theme='twoTone' />
                </a-tooltip>
              </a-input>
            </a-form-item>
          </a-col>
          <a-col :span='24' v-if='!!model.id'>
            <a-form-item label='上级数据汇聚请求路径' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <a-input v-decorator="['parentRequestUrl']" placeholder='请输入数据汇聚请求路径' :allowClear='true'
                       autocomplete='off' disabled />
            </a-form-item>
          </a-col>
          <a-col :span='24'>
            <a-form-item label='本级单位' :labelCol='labelCol' :wrapperCol='wrapperCol' required>
              <!--              <a-input-search placeholder='点击选择本级单位(单个，不可多选)' v-model='checkedDepartNameString' readOnly-->
              <!--                @search='onSearch'>-->
              <!--                <a-button slot='enterButton' icon='search'>选择本级单位-->
              <!--                </a-button>-->
              <!--              </a-input-search>-->
              <a-tree-select placeholder='请选择本级单位'
                             :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }"
                             v-decorator="['selfDepartCode', validatorRules.selfDepartCode]" :allowClear='true'
                             :treeData='selfTree'
                             :replaceFields='{value:"departNameEn",label:"departName"}'></a-tree-select>
            </a-form-item>
          </a-col>
        </a-row>
        <depart-window ref='departWindow' @ok='modalFormOk'></depart-window>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
import {
  httpAction,
  getAction
} from '@/api/manage'
import pick from 'lodash.pick'
import JFormContainer from '@/components/jeecg/JFormContainer'
import departWindow from '@/views/system/modules/DepartWindow'
import {rules} from '@/utils/rules'
export default {
  name: 'reportingForm',
  components: {
    JFormContainer,
    departWindow
  },
  data() {
    return {
      form: this.$form.createForm(this),
      checkedDepartKeys: [],
      checkedDepartNames: [],
      selectedDepartKeys: [], //保存用户选择部门id
      checkedDepartNameString: '', // 保存部门的名称 =>title
      model: {},
      userId: null,
      labelCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 5
        }
      },
      labelCol1: {
        xs: {
          span: 24
        },
        sm: {
          span: 6
        }
      },
      wrapperCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 16
        }
      },
      wrapperCol1: {
        xs: {
          span: 24
        },
        sm: {
          span: 16
        }
      },
      confirmLoading: false,
      validatorRules: {
        parentDepartCode: {
          rules: [{
            required: true,
            message: '请输入上级单位标识'
          }]
        },
        parentPlatformCode: {
          rules: [{
            required: true,
            message: '请输入上级平台标识'
          }]
        },
        parentServerAddress: {
          rules: [
            {
              required: true,
              message: '请输入上级服务地址'
            },
            {
              pattern:rules.ip,
              message: '请输入正确的服务地址'
            }
          ]
        },
        selfDepartCode: {
          rules: [{
            required: true,
            message: '请选择本级单位'
          }]
        }
      },
      url: {
        add: '/dataReport/manage/add',
        edit: '/dataReport/manage/edit',
        departList: '/device/statis/queryMyDeptTreeList'
      },
      parentTree: [],
      selfTree: [],
      flatterSelfData: [],
      flatterParentData: []
    }
  },
  created() {
  },
  methods: {
    //监听上级单位的变化
    handleDepartChange(value, label) {
      if (value) {
        let select = this.flatterParentData.find(el => el.departNameEn === value)
        if (select) {
          this.form.setFieldsValue({
            parentPlatformCode: select.platformCode,
            selfDepartCode: undefined
          })
          this.selfTree = select.children
        }
      } else {
        this.form.setFieldsValue({
          parentPlatformCode: '',
          selfDepartCode: undefined
        })
        this.selfTree = []
      }
    },
    //获取单位数据
    async getDepart() {
      await getAction(this.url.departList).then((res) => {
        this.parentTree = JSON.parse(JSON.stringify(res.result))
        this.flatterTreeHandle(this.parentTree, this.flatterParentData)
      })
    },
    //拉平树
    flatterTreeHandle(data, flatterData) {
      data.forEach(el => {
        flatterData.push(el)
        if (el.children && el.children.length > 0) {
          this.flatterTreeHandle(el.children, flatterData)
        }
      })
    },
    onSearch() {
      this.$refs.departWindow.add(this.checkedDepartKeys, this.userId)
    },
    add() {
      this.edit({})
    },
    async edit(record) {
      await this.getDepart()
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.$nextTick(() => {
        // this.checkedDepartNames = []
        // this.checkedDepartKeys = record && record.selfDepartId ? record.selfDepartId.split(',') : []
        // this.selectedDepartKeys = record && record.selfDepartId ? record.selfDepartId.split(',') : []
        // this.checkedDepartNameString = record && record.selfDepartName ? record.selfDepartName : ''
        this.form.setFieldsValue(pick(this.model, 'parentRequestUrl', 'parentServerAddress', 'parentDepartCode', 'parentPlatformCode', 'selfDepartCode'))
        this.$nextTick(() => {
          let v = this.form.getFieldValue('parentDepartCode')
          let s = this.flatterParentData.find(el => el.departNameEn === v)
          if (s) {
            this.selfTree = s.children
          }
        })
      })
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let formData = Object.assign(this.model, values)
          formData.levelType = '1'
          formData.selfDepartId = this.selectedDepartKeys.join(',')
          httpAction(httpurl, formData, method).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.$emit('ok')
            } else {
              that.$message.warning(res.message)
            }
          }).finally(() => {
            that.confirmLoading = false
          })
        }

      })
    },
    modalFormOk(formData) {
      let info = formData.departIdList
      if (info.length > 1) {
        this.$message.warning('请选择单个单位')
        return
      }
      this.checkedDepartNameString = ''
      this.checkedDepartNames = []
      this.checkedDepartKeys = []
      this.selectedDepartKeys = []
      this.checkedDepartKeys.push(info[0].key)
      this.selectedDepartKeys.push(info[0].key)
      this.checkedDepartNames.push(info[0].title)
      this.checkedDepartNameString = this.checkedDepartNames.join(',')
    }
  }
}
</script>