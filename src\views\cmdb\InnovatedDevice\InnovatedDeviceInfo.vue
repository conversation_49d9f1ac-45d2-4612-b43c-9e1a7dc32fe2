<template>
  <div class='lookTop'>
    <a-card>
      <a-row>
        <a-col :span="24">
          <div class='lookEditionWrapper'>
            <div  class='lookEdition' @click="handleEdit(record)" v-has="'supplier:edit'">
              <a-icon type="edit"  class='editIcon'/>编辑
            </div>
            <div class='lookReturn'>
              <img src="~@/assets/return1.png" alt="" @click="getGo" class='returnImage' />
            </div>
          </div>
        </a-col>
        <a-col :span="24">
          <a-descriptions :column="{ xxl: 2, xl: 2, lg: 2, md: 2, sm: 2, xs: 2 }" bordered>
            <a-descriptions-item label="型号">{{ record.model }}</a-descriptions-item>
            <a-descriptions-item label="产品类型">{{ record.productCategoryText }}</a-descriptions-item>
            <a-descriptions-item label="供应商">{{ record.supplierText }}</a-descriptions-item>
            <a-descriptions-item label="所属期">{{ record.catalogueText }}</a-descriptions-item>
          </a-descriptions>
        </a-col>
      </a-row>
      <innovated-device-modal ref="modalForm" @ok="query"></innovated-device-modal>
    </a-card>
  </div>
</template>

<script>
import {getAction } from '@/api/manage'
import innovatedDeviceModal from '@views/cmdb/InnovatedDevice/modules/InnovatedDeviceModal.vue'

export default {
  name: 'InnovatedDeviceInfo',
  components: {
    innovatedDeviceModal,
  },
  props: {
    data: {
      type: Object,
    },
  },
  data() {
    return {
      record:{},
      url: {
        queryById: '/itInnovate/cmdbItInnovate/queryById',
      },
    }
  },
  mounted() {
    this.record=this.data
  },
  methods: {
    handleEdit: function (record) {
      this.$refs.modalForm.edit(record)
      this.$refs.modalForm.title = '编辑'
      this.$refs.modalForm.disableSubmit = false
    },
    query() {
      getAction(this.url.queryById, { id: this.record.id }).then((res) => {
        if (res.code == 200) {
          this.record = res.result
        }
      })
    },
    //返回上一级
    getGo() {
      this.$parent.pButton1(0)
    },
  },
}
</script>
<style lang='less' scoped>
@import '~@assets/less/lookPage.less';
</style>