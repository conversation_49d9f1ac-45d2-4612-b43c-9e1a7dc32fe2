<template>
  <div id="echart" style="width: 100%;height: 100%"></div>
</template>

<script>
  export default {
    name: 'annular<PERSON>ie',
    props: {
      dataObj: {
        type: Object
      },
      colorObj: {
        type: Object
      },
    },
    data() {
      return {
        myChart: {},
        chartData1: [],
        chartData2: [],
        legendName: []
      }
    },
    watch: {
      dataObj: {
        handler(n, o) {
          if (Object.keys(n).length > 0) {
            this.legendName = []
            this.chartData1 = this.getChartData(n.data1, this.colorObj.colorList1)
            this.chartData2 = this.getChartData(n.data2, this.colorObj.colorList2)
            let sum=0
            if(this.chartData2.length>0){
              for (let i=0;i<this.chartData2.length;i++){
                if(isNaN(this.chartData2[i].value)){
                  continue
                }
                sum+=this.chartData2[i].value*1
              }
            }
            let series=sum>0?this.getSeries():this.getSeries().slice(0,1)
            this.$nextTick(() => {
              this.loadEchart(series);
            })
          }
        },
        deep: true,
        immediate: true
      },
    },
    mounted() {
      let _this = this;
      window.onresize = function() {
        _this.myChart.resize()
      }
    },
    methods: {
      getChartData(data, colorList) {
        let chartData = []
        if (data.length > 0) {
          for (let i = 0; i < data.length; i++) {
            if(data[i].value!==''){
              this.legendName.push(data[i].name)
            }
            let param = {
              ...data[i], itemStyle: {
                normal: {
                  color: {
                    colorStops: [{
                      offset: 0,
                      color: colorList[i].color1
                    }, {
                      offset: 1,
                      color: colorList[i].color2
                    }]
                  }
                  //shadowBlur: 200,
                  //shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
            chartData.push(param)
          }
        }
        return chartData
      },

      loadEchart(series) {
        this.myChart = this.$echarts.init(document.getElementById("echart"));
        this.myChart.setOption({
          /* title: {
             text: this.echartObj.title.text
           },*/
          legend: {
            data: this.legendName,
            type: 'scroll',
            orient: 'vertical',
            right: 10,
            top: 20,
            bottom: 20,
          },
          tooltip: {
            trigger: 'item',
            /*formatter: '{a} <br/>{b}: {c} ({d}%)'*/
            formatter: '{b}: {c} ({d}%)'
          },
          series: series
        })
      },

      getSeries(){
        let series=[
          {
            z: 1,
            name: 'ip使用统计',
            type: 'pie',
            radius: ['55%', '70%'],
            center: ['40%', '50%'],
            labelLine: {
              length: 30,
              show: true
            },
            label: {
              fontSize: 14,
              formatter: '{b}:{c}'
            },
            data: this.chartData1
          },
          {
            z: 2,
            name: 'ip使用统计',
            type: 'pie',
            radius: '45%',
            center: ['40%', '50%'],
            minAngle: 15,
            label: {
              show:false,
              position: 'inner',
              fontSize: 14,
              formatter: '{c}',
              color:'#fff'
            },
            labelLine: {
              show: false
            },
            data: this.chartData2
          },
          {
            z: 1,
            type: 'pie',
            radius: ['45%', '48%'],
            center: ['40%', '50%'],
            hoverAnimation: false,
            silent:true,
            animation:false,
            tooltip:{
              show:false
            },
            label: {
              show: false
            },
            labelLine: {
              show: false
            },
            data: [{
              value: 0,
              itemStyle: {
                color: 'rgba(217,211,211,0.7)'
              }
            }]
          }
        ]
        return series
      }
    }
  }
</script>

<style scoped>

</style>