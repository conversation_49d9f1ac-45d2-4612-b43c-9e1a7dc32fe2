import {
  getAction
} from '@api/manage'
import {
  queryConfigureDictItem
} from '@api/api'
export const knowledgeSearchIndexMixins = {
  data() {
    return {
      keywordList: [], // 搜索热词
      historyList: [], // 搜索历史
      url: {
        getHotSeanch: '/kbase/knowledges/getHotSearch', // 获取搜索热词
        getSearchHistoryList: '/kbase/knowledges/searchHistory'
      },
      knowledgeSearchName: '' // 知识搜索的大标题
    }
  },
  activated() {
    this.value = ''
    // 获取搜索历史记录
    this.getSearchHistoryList()
  },
  methods: {
    /*从配置字典中获取知识搜索的大标题*/
    getKnowledgeSearchName(type) {
      queryConfigureDictItem({
        parentCode: 'knowledgeSearchName',
        childCode: type
      }).then((res) => {
        if (res.success && res.result) {
          this.knowledgeSearchName = res.result
        } else {
          this.knowledgeSearchName = this.defaultName
        }
      }).catch(() => {
        this.knowledgeSearchName = this.defaultName
      })
    },
    // 获取前五条热门搜索词
    getHotSeanch() {
      getAction(this.url.getHotSeanch).then(res => {
        if (res.success) {
          this.keywordList = res.result
        }
      })
    },
    // 搜索历史列表
    getSearchHistoryList() {
      getAction(this.url.getSearchHistoryList).then((res) => {
        if (res.success) {
          if (res.result.length > 10) {
            this.historyList = res.result.slice(0, 10)
          } else {
            this.historyList = res.result
          }
        }
      })
    },
    // 跳转知识详情
    goKnowledgeDetail(item) {
      this.$parent.pButton2(2, item, 1);
    },
    // 跳转到搜索结果页
    goSearchResultPage(key) {
      if (key) {
        this.value = key;
      }
      if (!this.value.trim()) {
        return false
      }
      this.$parent.pButton2(0, {
        value: this.value
      })
    },
  }
}