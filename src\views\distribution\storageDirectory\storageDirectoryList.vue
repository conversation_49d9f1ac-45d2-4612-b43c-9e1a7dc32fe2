<template>
  <a-row class='row-wrapper' type='flex' justify='space-between'>
    <a-col :lg='6' :md='8' :sm='10' :xl='4' :xs='10' class="left-col-wrapper">
      <cluster-fields @change-field='setCluster'></cluster-fields>
    </a-col>
    <a-col :lg='18' :md='16' :sm='14' :xl='20' :xs='14' class="right-col-wrapper">
      <!-- 查询区域 -->
      <div class="table-wrapper">
        <div v-if="cluster&&cluster.id">
          <div class="bucket-table" v-if="tableType==='bucket'">
            <bucket-table :cluster-id="cluster.id" @loadFolders="loadFolders"></bucket-table>
          </div>
          <div class="files-table" v-else-if="tableType==='folder'">
            <folder-table :cluster-id="cluster.id" :bucket-name="bucket.bucketName" @openClusterTable="openClusterTable"></folder-table>
          </div>
        </div>
        <div v-else>暂无数据</div>
      </div>
    </a-col>
  </a-row>
</template>

<script>
import clusterFields from '@views/distribution/dataSecurity/modules/ClusterFields.vue'
import bucketTable from '@views/distribution/storageDirectory/modules/bucketTable.vue'
import folderTable from '@views/distribution/storageDirectory/modules/folderTable.vue'

export default {
  name: 'storageDirectoryList',
  components: {
    clusterFields,
    bucketTable,
    folderTable
  },
  data() {
    return {
      description: '数据存储目录',
      cluster:null,
      bucket:null,
      tableType:'',
    }
  },
  created() {
  },
  methods: {
    setCluster(cluster) {
      this.cluster=cluster
      this.tableType='bucket'
    },
    openClusterTable(){
      this.tableType='bucket'
    },
    loadFolders(bucket){
      this.bucket=bucket
      this.tableType='folder'
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
.row-wrapper {
  height: 100%;

  .left-col-wrapper {
    height: 100%;
    background: #fff;
    padding: 16px;
    border-radius: 3px;
  }

  .right-col-wrapper {
    height: 100%;

    .table-wrapper{
      height: 100%;
      overflow: hidden;
      overflow-y: auto;
      margin-left: 16px;
      background: #fff;
      border-radius: 3px;
      padding: 24px;
    }
  }
}
</style>