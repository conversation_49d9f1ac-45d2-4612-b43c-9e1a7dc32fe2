<template>
  <div class="wrap">
    <div class="content">
      <!-- 中间拓扑图面板 -->
      <div :class="toolbar ? 'panel' : 'panel-show'"
        :style="{ width: operate === 'create' ? 'calc(100% - 580px)' : '100%' }">
        <div v-if="spinning" class="spin-block " :class="{'spin-block-theme':topoBgByTheme}">
          <a-spin style="position: absolute; width: 100%; top: 40vh; z-index: 10000" :tip="spinTip"
            :spinning="spinning"></a-spin>
        </div>
        <!-- 拓扑图容器 -->
        <div :id="canvasId" ref="container" class="graphcontainer" style="flex: 1; width: 100% !important"></div>
        <!-- 提示弹窗组件 -->
        <topo-tip ref="topoTip"></topo-tip>
        <!-- 子拓扑返回按钮 -->
        <div @click="goParentTopo" class="parent-topo-btn" :style="{ top: toolbar ? '60px' : '20px' }"
          v-if="parentTopos.length > 0">
          <a-button type="primary" shape="circle" icon="rollback" />
        </div>
      </div>
    </div>
    <!-- 设备详情 -->
    <topo-device-modal  ref="modalForm"></topo-device-modal>
    <!-- 应用拓扑图业务应用选择 -->
    <business-type-modal ref="businessType" @ok="appAutoCreate"></business-type-modal>
    <!-- 选择设备弹窗 -->
    <choose-node-device ref="chooseNodeDevice" @ok="resetNodeDevice" @chooseVirtual="chooseVirtual">
    </choose-node-device>
  </div>
</template>

<script>
  // 样式文件，可以根据需要自定义样式或主题
  import '@wchbrad/vue-easy-tree/src/assets/index.scss'
  import './index.less'

  import {
    Graph,
    Color,
    Dom,
    Shape,
    Edge,
    NodeView,
    Addon,
    DataUri,
    Vector
  } from '@antv/x6'
  const {
    Dnd
  } = Addon
  import dagre from 'dagre'
  import FlowGraph from './graph'
  import {
    getAction,
    postAction,
    httpAction,
    uploadAction
  } from '@/api/manage'
  import {
    globalGridAttr
  } from './models/global'
  //设备信息详情
  import TopoDeviceModal from './components/TopoDeviceModal'
  //业务类型弹窗
  import BusinessTypeModal from './components/BusinessTypeModal'
  //选择节点设备
  import ChooseNodeDevice from './components/ChooseNodeDevice'
  //信息弹窗
  import TopoTip from './components/tips/TopoTip.vue'
  import {
    uuidX6
  } from '@/utils/util'

  // 拓扑图注册事件的方法
  import cellEventMixin from './mixins/cellEventMixin'
  //拓扑图创建节点
  import createTopoMixin from './mixins/createTopoMixin'
  // 改变节点边线状态的方法
  import setCellStatus from './mixins/setCellStatus'
  // 自动生成拓扑图
  import autoTopoMixin from './mixins/autoTopoMixin'
  // 拓扑图配置设置
  import topoConfigMixin from './mixins/topoConfigMixin'

  const tryToJSON = (val) => {
    try {
      return JSON.parse(val)
    } catch (error) {
      return val
    }
  }

  export default {
    name: 'index',
    components: {
      TopoDeviceModal,
      TopoTip,
      BusinessTypeModal,
      ChooseNodeDevice,
    },
    props: {
      topoBgByTheme: {
        type: Boolean,
        default: false,
      },
      operate: {
        type: String,
        default: 'create',
      }, //当前页面为查看、编辑：show、create
      toolbar: {
        type: Boolean,
        default: false,
      },
      canvasId: {
        type: String,
        default: 'layoutContainer',
      },
      // 判断是否在业务概览中使用拓扑图
      isBusiness: {
        type: Boolean,
        default: false,
      },
    },
    mixins: [cellEventMixin, createTopoMixin, setCellStatus, autoTopoMixin, topoConfigMixin],
    data() {
      return {
        type: 'grid',
        id: '',
        graph: null,
        isReady: false,
        url: {
          deviceInfo: '/topo/device/info',
          deviceStatusUrl: '/topo/device/status',
          topoEdit: '/topo/topoInfo/edit',
          getTopoById: '/topo/topoInfo/queryById',
          getStatus: '/topo/device/initStatus',
          deviceDetailInfo: '/device/deviceInfo/queryById',
          deviceTypeUrl: '/topo/device/selectNetType',
        },
        apiUrl: window._CONFIG['domianURL'],
        dnd: Object,
        deviceInfo: null,
        topoInfo: {},
        copyTopoInfo: null, //topo信息的复制保存，用于子拓扑返回父拓扑时使用
        cellList: [],
        alarmInfo: null,
        globalGridAttr: globalGridAttr,
        edgeConnectNode: {},
        showDevcInfo: {
          name: '',
          ip: '',
          mem: '',
          cpu: '',
        }, //气泡展示设备信息
        nodeIp: '',
        interval: null,
        alarmNodes: [],
        cellId: '',
        cellType: 'grid',
        spinning: false,
        spinTip: 'Loading...',
        statusTop: 0,
        statusRight: 0,
        parentTopos: [],
      }
    },
    watch: {
      '$store.getters.deviceAlarms': {
        // immediate:true,
        deep: true,
        handler() {
          this.getAlarmInfoTip(this.$store.getters.deviceAlarms)
        },
      },
      '$store.getters.deviceStatus': {
        // immediate:true,
        deep: true,
        handler(e) {
          if (this.graph) {
            this.getDeviceStatusTip()
          }
        },
      },
    },
    beforeDestroy() {
      if (this.interval) {
        clearInterval(this.interval)
      }
      this.onClose()
    },
    methods: {
      /*
       *根据拖拽的节点信息 生成拓扑图的节点
       *type为节点类型
       *data 节点信息
       *e拖拽事件对象
       * createTopoNode 方法定义在createTopoMixin中
       */

      //子拓扑返回显示父拓扑
      goParentTopo() {
        if (this.parentTopos.length > 0 && this.isReady) {
          this.isReady = false
          let topoId = this.parentTopos.splice(this.parentTopos.length - 1, 1)[0]
          this.createTopo(topoId, true)
        }
      },
      //展示子拓扑图
      showSonTopo(cell) {
        //只允许查看页面实现子拓扑节点跳转
        if (this.operate === 'show' && this.isReady) {
          this.isReady = false
          this.parentTopos.push(this.topoInfo.id)
          this.createTopo(cell.getData().deviceId, true)
        }
      },
      // 设备节点告警状态
      getAlarmInfoTip(deviceAlarms) {
        if (this.graph) {
          const nodes = this.graph.getNodes()
          deviceAlarms.forEach((ele) => {
            let node = nodes.find((val) => {
              if (val.data) {
                return val.data.deviceCode == ele.deviceCode
              }
            })
            if (node != undefined) {
              let level = 0
              ele.alarmInfo.forEach((el) => {
                if (el.alarmLevel > level) {
                  level = el.alarmLevel
                }
              })
              this.setNodeAlarm(node, level)
            }
          })
        }
      },
      // 根据后台推送的设备状态修改设备状态
      getDeviceStatusTip() {
        let statusArr = this.$store.getters.deviceStatus
        let nodes = this.graph.getNodes()
        let edges = this.graph.getEdges()
        statusArr &&
          statusArr.forEach((ele) => {
            const node = nodes.find((val) => {
              if (!!val.data && val.data.deviceCode) {
                return val.data.deviceCode == ele.deviceCode
              }
            })
            if (!!node) {
              // up 在线 // down 离线
              let status = 0
              if (ele.status === 'up') {
                status = 1
              }
              // 告警状态下不考虑离线在线
              if (node.data.status !== 2 && node.data.status !== status) {
                this.setNodeOnAndOff(node, status)
              }
            }
          })
        edges.forEach((edge) => {
          this.setEdgeStatus(edge)
        })
      },
      /*
       *getTopoById 通过id获取拓扑图信息
       *id 拓扑图的id
       *totpoDataType 获取渲染拓扑图数据类型 0或不传 不返回,  1 json,  2 node/edge,  3 两种数据都返回
       */
      async getTopoById(id) {
        await getAction(this.url.getTopoById, {
          id: id,
          topoDataType: 2,
        }).then((res) => {
          if (res.success) {
            this.topoInfo = res.result
          }
        })
      },
      //生成拓扑图
      async createTopo(id, isSwitch) {
        if (!isSwitch) {
          this.parentTopos = []
        }
        this.spinTip = '正在加载...'
        this.spinning = true
        if (this.graph) {
          this.graph.dispose()
          this.graph = null
        }
        // 获取告警级别列表
        await this.getAlarmLevelData()
        await this.getTopoById(id)
        // 如果已存在画布 销毁已存在的画布

        let timer = setTimeout(() => {
          clearTimeout(timer)
          timer = null
          // 初始化画布
          this.initGraph(this.canvasId)
          let containerBox = this.$refs.container.getBoundingClientRect()
          // 判断当前拓扑图是否有保存的节点边的序列化
          if (this.topoInfo.topoDataJson != null && this.topoInfo.topoDataJson != '') {
            let topoObj = JSON.parse(this.topoInfo.topoDataJson)
            let xlink = 'xlink:href'
            topoObj.cells.forEach((item, i) => {
              if (item.attrs && item.attrs.image && item.attrs.image[xlink]) {
                item.attrs.image[xlink] = this.apiUrl + '/' + item.attrs.image[xlink]
              }
            })
            this.graph.fromJSON(topoObj)
          } else if (this.topoInfo.nodeList && this.topoInfo.edgeList) {
            this.dataTransferCell(this.topoInfo.nodeList, this.topoInfo.edgeList)
          }
          // 初始化设备状态
          this.initDeviceStatus()
          // 初始化拓扑图的配置
          this.initConfig()

          // 缩放画布内容，使画布内容充满视口
          let rect = this.$refs.container.getBoundingClientRect()
          let topoConfig = this.globalGridAttr.topoConfig
          let topoWidth = topoConfig.maxX - topoConfig.minX + 50
          let topoHeight = topoConfig.maxY - topoConfig.minY + 50
          if (rect.width > topoWidth && rect.height > topoHeight) {
            this.graph.centerContent()
          } else {
            this.graph.zoomToFit({
              padding: 50
            })
          }
          let zoom = this.graph.zoom(0.9)
          this.setup()
          this.spinning = false
        }, 200)
      },
      // 初始化设备状态
      initDeviceStatus() {
        // 清空vuex的设备在线离线数据
        this.$store.commit('CLEAR_STATUS_LIST')
        // 清空vuex的设备告警状态
        this.$store.commit('CLEAR_ALARM_LIST')
        let nodes = this.graph.getNodes().filter((el) => el.data.nodeType === 'device')
        let edges = this.graph.getEdges()
        // 设定边线的层级
        edges.forEach((edge) => {
          edge.zIndex = 3
        })
        let codes = []
        nodes.forEach((e) => {
          if (e.data.isVirtual) {
            e.attr('body/fill', 'rgba(0,128,0,0.8)')
          }
          if (e.data.deviceCode) {
            codes.push(e.data.deviceCode)
          }
        })
        if (codes.length <= 0) return
        // 获取设备状态
        postAction(this.url.getStatus, {
          deviceCode: codes.join(','),
        }).then((res) => {
          if (res.success) {
            // 设置节点的状态
            nodes.forEach((el) => {
              let stdata = res.result.find((st) => st.deviceCode === el.data.deviceCode)
              if (stdata) {
                this.setNodeOnAndOff(el, stdata.status)
              } else {
                this.setNodeOnAndOff(el, 1)
              }
            })
            // 设置边线的状态
            edges.forEach((edge) => {
              this.setEdgeStatus(edge)
            })
            let alarmInfo = []
            res.result.forEach((e, inx) => {
              // 判断是否有告警告警信息
              let tem = {}
              if (e.alarmInfo && e.alarmInfo.length > 0) {
                alarmInfo.push(e)
              }
            })
            //初始化设备的告警状态
            alarmInfo.forEach((el, idx) => {
              const flag = this.$store.getters.alarmInfo.some((ele) => {
                return ele.deviceCode === el.deviceCode
              })
              if (!flag) {
                this.$store.dispatch('addAlarmList', el)
              }
            })
          }
        })
      },
      onClose() {
        if (this.graph && this.graph.dispose) {
          this.graph.dispose()
        }
      },
      /*
       *请求设备状态时是否需要判断内外网设备类型
       * needChecked 是否需要判断
       *window._CONFIG.customization 根据不同的项目设置请求IP 查看config配置文件
       *  */
      async getInternetType(idArr, needChecked) {
        if (
          needChecked &&
          window._CONFIG.customization &&
          window._CONFIG.customization.cz_zunyi &&
          this.url.deviceTypeUrl
        ) {
          let pArr = []
          let urlArr = []
          idArr.forEach((el) => {
            pArr.push(
              getAction(this.url.deviceTypeUrl, {
                deviceCode: el,
              })
            )
          })
          return Promise.all(pArr).then((data) => {
            data.forEach((res, idx) => {
              let url = this.url.deviceStatusUrl
              if (res.success) {
                if (res.result === window._CONFIG.customization.cz_zunyi.internetFlag) {
                  url = window._CONFIG.customization.cz_zunyi.internetServiceURL + url
                }
              }
              urlArr.push(
                postAction(url, {
                  deviceCode: idArr[idx],
                })
              )
            })
            return Promise.all(urlArr)
          })
        } else {
          let pArr = []
          idArr.forEach((el) => {
            pArr.push(
              postAction(this.url.deviceStatusUrl, {
                deviceCode: el,
              })
            )
          })
          return Promise.all(pArr)
        }
      },
      initGraph(container) {
        this.graph = FlowGraph.init(container, this.operate === 'create')
        this.dnd = new Dnd({
          target: this.graph,
          scaled: false,
          animation: true,
          validateNode(droppingNode, options) {
            return true
          },
        })
        // 给拓扑图边线注册监听事件

        this.isReady = true
      },

      //获取设备详情
      getDeviceInfo(code) {
        getAction(this.url.deviceInfo, {
          deviceCode: code,
        }).then((res) => {
          if (res.success) {
            return res.result
          }
        })
      },
    },
  }
</script>
<style>
  .parent-topo-btn {
    position: absolute;
    top: 20px;
    right: 20px;
  }

  .my-scroller::-webkit-scrollbar {
    display: none;
  }
</style>
<style scoped lang="less">
  ::v-deep .el-tree {
    max-height: calc(100% - 190px);
    overflow-y: auto;
    border: 1px solid rgba(0, 0, 0, 0.08);
    margin: 5px 0;
  }

  .panel {
    height: 100%;
    display: flex;
    position: relative;
    flex-direction: column;

    .graphcontainer {
      height: calc(100% - 38px) !important;
      width: 100% !important;
      flex: 1 !important;
    }
  }

  .panel-show {
    width: 100%;
    height: 100%;
    display: flex;
    position: relative;
    flex-direction: column;

    .graphcontainer {
      height: 100% !important;
      width: 100% !important;
      flex: 1 !important;
    }
  }

  .wrap .content {
    display: flex;
    height: 100% !important;
  }

  .spin-block {
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 10000;
    background: rgba(255, 255, 255, 0.6)
  }

  .spin-block-theme {
    background: rgba(0, 0, 0, 0.2)
  }

  ::v-deep .sider {
    box-shadow: none;
    border: 1px solid rgba(0, 0, 0, 0.08) !important;
    margin-right: 10px;
  }

  ::v-deep .x6-graph-scroller {
    width: 100% !important;
  }

  ::v-deep .x6-graph {
    box-shadow: none !important;
  }
</style>