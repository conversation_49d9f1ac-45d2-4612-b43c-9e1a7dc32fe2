<template>
  <div style="height: 100%">
    <component :is="pageName" :data="data" />
  </div>
</template>
<script>
  import panelPortTypeList from './panelPortTypeList'
  import panelPortTypeDetails from './modules/panelPortTypeDetails'
  export default {
    name: 'panelPortTypeManage',
    data() {
      return {
        isActive: 0,
        data: {},
      }
    },
    components: {
      panelPortTypeList,
      panelPortTypeDetails,
    },
    created() {
      this.pButton1(0)
    },
    //使用计算属性
    computed: {
      pageName() {
        switch (this.isActive) {
          case 0:
            return 'panelPortTypeList'
            break

          default:
            return 'panelPortTypeDetails'
            break
        }
      },
    },
    methods: {
      pButton1(index, item) {
        this.isActive = index
        this.data = item
      },
      pButton2(index, item) {
        this.isActive = index
        this.data = item
      }
    },
  }
</script>