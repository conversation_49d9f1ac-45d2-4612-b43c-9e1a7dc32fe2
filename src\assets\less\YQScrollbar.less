 .yq-scroll {
  @scrollBarSize: 10px;
    /* 定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
    &::-webkit-scrollbar {
      width: @scrollBarSize !important;
      height: @scrollBarSize !important;
      background-color: transparent !important;
      display: none !important;
    }

    & .-o-scrollbar {
      display: none !important;
    }

    /* 兼容IE */
    -ms-overflow-style: none;
    -ms-scroll-chaining: chained;
    -ms-content-zooming: zoom;
    -ms-scroll-rails: none;
    -ms-content-zoom-limit-min: 100%;
    -ms-content-zoom-limit-max: 500%;
    -ms-scroll-snap-type: proximity;
    -ms-scroll-snap-points-x: snapList(100%, 200%, 300%, 400%, 500%);

    /* 定义滚动条轨道 */
    &::-webkit-scrollbar-track {
      background-color: transparent !important;
    }

    /* 定义滑块 */
    &::-webkit-scrollbar-thumb {
      border-radius: @scrollBarSize !important;
      background-color: #eee !important;
      box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1) !important;

      &:hover {
        background-color: #dddddd !important;
      }

      &:active {
        background-color: #bbbbbb !important;
      }
    }
  }