<template>
  <a-row :gutter='10' style='height: 100%;' class='vScroll'>
    <a-col style='width:100%;height: 100%;display: flex;flex-direction: column'>
      <!-- 查询区域 -->
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0', marginRight: '12px' }" class='card-style'
              style='width: 100%'>
        <div class='table-page-search-wrapper'>
          <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
            <a-row :gutter='24' ref='row'>
              <a-col :span='spanValue'>
                <a-form-item label='节点名称'>
                  <a-input :maxLength='maxLength' placeholder='请输入节点名称' v-model='queryParam.nodeName' :allowClear='true' autocomplete='off' />
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label='节点IP'>
                  <a-input :maxLength='maxLength' placeholder='请输入节点IP' v-model='queryParam.nodeIp' :allowClear='true' autocomplete='off' />
                </a-form-item>
              </a-col>
              <!--          <a-col :span='spanValue' v-show="toggleSearchStatus">
                <a-form-item label='节点状态'>
                  <a-select v-model="queryParam.status" :allowClear='true' placeholder='请选择节点状态'>
                    <a-select-option :value="1">正常</a-select-option>
                    <a-select-option :value="2">禁用</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>-->
              <a-col :span='colBtnsSpan()'>
                <span class='table-page-search-submitButtons'
                      :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button type='primary' class='btn-search btn-search-style' @click='searchQuery'>查询</a-button>
                  <a-button class='btn-reset btn-reset-style' @click='searchReset'>重置</a-button>
                  <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <div class='table-operator table-operator-style'>
          <a-button @click="handleAdd">新增</a-button>
        </div>
        <a-table ref='table' bordered :row-key='(record,index)=>{return record.id || index}' :columns='columns'
                 :dataSource='dataSource' :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}" :pagination='ipagination'
                 :loading='loading' @change='handleTableChange'>
          <span slot='action' class='caozuo' slot-scope='text, record'>
            <a @click='handleDetailPage(record)' v-if="(!!record.nodeIp)">查看节点详情</a>
            <a-divider type='vertical' v-if="(!!record.nodeIp)" />
            <a-dropdown>
              <a class='ant-dropdown-link'>更多
                <a-icon type='down' /></a>
              <a-menu slot='overlay'>
                <a-menu-item>
                  <a class='overlay' @click='handleEdit(record)'>编辑</a>
                </a-menu-item>
                <a-menu-item>
                  <a class='overlay' @click='nodeDel(record.id)' v-if="(!!record.nodeIp)">删除节点</a>
                </a-menu-item>
                <a-menu-item>
                  <a class='overlay' @click='jiQunDel(record.clusterId)'>删除集群</a>
                </a-menu-item>
              </a-menu>
            </a-dropdown>
          </span>
        </a-table>
      </a-card>
    </a-col>
    <node-monitor-modal ref='modalForm' @ok='modalFormOk'> </node-monitor-modal>
  </a-row>
</template>

<script>
import {
  mixinDevice
} from '@/utils/mixin'
import {
  JeecgListMixin
} from '@/mixins/JeecgListMixin'
import nodeMonitorModal from './modules/nodeMonitorModal.vue'
import {
  YqFormSearchLocation
} from '@/mixins/YqFormSearchLocation'
import {
  getAction,
  deleteAction
} from '@/api/manage'
export default {
  name: 'nodeMonitorList',
  mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
  components: {
    nodeMonitorModal,
  },
  data() {
    return {
      maxLength:50,
      description: '节点监控管理页面',
      formItemLayout: {
        labelCol: {
          style: 'width:80px'
        }
      },
      fieldList: [{
        name: '高性能数据存储',
        value: '高性能数据存储'
      },
        {
          name: '动态数据存储',
          value: '动态数据存储'
        },
        {
          name: '静态数据存储',
          value: '静态数据存储'
        },
      ],
      // 表头
      columns: [{
        title: '集群名称',
        dataIndex: 'clusterName',
        customRender: (value, row) => {
          const obj = {
            children: value,
            attrs: {}
          };
          if (row.size != null) {
            obj.attrs.rowSpan = row.size
          } else {
            obj.attrs.rowSpan = 0
          }
          return obj
        },
        customCell: () => {
          let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
          return {
            style: cellStyle
          }
        }
      }, {
        title: '节点名称',
        dataIndex: 'nodeName',
        customCell: () => {
          let cellStyle = 'text-align: left;min-width: 120px;max-width:300px'
          return {
            style: cellStyle
          }
        }
      },
        {
          title: '状态',
          dataIndex: 'state'
        },
        {
          title: '节点IP',
          dataIndex: 'nodeIp'
        },
        {
          title: '端口号',
          dataIndex: 'nodePort'
        },
        {
          title: '机柜号',
          dataIndex: 'cabinetNumber',
        },
        {
          title: '槽位号',
          dataIndex: 'slotNumber'
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 180,
          scopedSlots: {
            customRender: 'action'
          }
        }
      ],
      url: {
        list: '/distributedStorage/node/list',
        delete: '/distributedStorage/cluster',
        nodeDel: '/distributedStorage/node',
      },
    }
  },
  mounted() {},
  methods: {
    loadData(arg) {
      if (!this.url.list) {
        this.$message.error('请设置url.list属性!')
        return
      }
      var params = this.getQueryParams() //查询条件
      this.loading = true
      getAction(this.url.list, params).then((res) => {
        if (res.success && res.result) {
          this.dataSource = res.result
          if (this.dataSource.length < 9) {
            this.clientHeight = false
          }
          this.ipagination.total = res.result.total ? res.result.total : 0
        }
        if (res.code === 510) {
          this.$message.warning(res.message)
        }
        this.loading = false
      })
    },
    handleEdit: function (record) {
      this.$refs.modalForm.getNode();
      this.$refs.modalForm.edit(record);
      this.$refs.modalForm.title = '编辑';
      this.$refs.modalForm.disableSubmit = false;
    },
    nodeDel(id) {
      var that = this
      this.$confirm({
        title: '确认删除',
        okText: '是',
        cancelText: '否',
        content: '是否删除选中节点?',
        onOk: function () {
          that.loading = true
          deleteAction(that.url.nodeDel, {
            nodeId: id
          }).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.loadData()
            } else {
              that.$message.warning(res.message)
              that.loadData()
            }
          })
        }
      })
    },
    jiQunDel(id) {
      var that = this
      this.$confirm({
        title: '确认删除',
        okText: '是',
        cancelText: '否',
        content: '是否删除选中集群?',
        onOk: function () {
          that.loading = true
          deleteAction(that.url.delete, {
            clusterId: id
          }).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.loadData()
            } else {
              that.$message.warning(res.message)
              that.loadData()
            }
          })
        }
      })
    },
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';

.stateBox {
  margin-left: 20px;
}

.stateImg {
  vertical-align: middle
}

.alarmStatus {
  margin-left: 8px;
}

.overlay {
  color: #409eff
}
</style>