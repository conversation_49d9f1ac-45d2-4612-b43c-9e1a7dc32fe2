export const alarmLevelValueUnit = {
  data() {
    return {
      //控制选择单位集合
      unitIdentifying: '/S',
      unitCodeListA: ['b', 'B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB'],
      unitCodeListB: ['b/S', 'Kb/S', 'Mb/S', 'Gb/S', 'Tb/S', 'Pb/S', 'Eb/S']
    }
  },
  methods: {
    getUnitList(unit) {
      if (unit && unit.toUpperCase().trim().indexOf(this.unitIdentifying)>-1) {
       return  this.unitCodeListB
      }else {
        return this.unitCodeListA
      }
    }
  }
}