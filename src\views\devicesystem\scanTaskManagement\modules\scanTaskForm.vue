<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container>
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <div class="colorBox">
          <span class="colorTotal">扫描任务信息</span>
        </div>
        <a-row>
          <a-col :sm="12" :xs="24">
            <a-form-model-item label="任务名称" prop="taskName" v-bind="formItemLayout">
              <a-input v-model="model.taskName" :allowClear="true" autocomplete="off" placeholder="请输入任务名称"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :sm="12" :xs="24">
            <a-form-model-item label="所属网关" prop="gatewayCode" v-bind="formItemLayout">
              <a-select v-model="model.gatewayCode" placeholder="请选择所属网关" :show-search="true"
                :getPopupContainer="(node) => node.parentNode" option-filter-prop="label" :allow-clear="true">
                <a-select-option v-for="item in gatewayData" :label="item.name" :value="item.deviceCode"
                  :key="item.id">{{ item.name }}</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :sm="12" :xs="24">
            <a-form-model-item label="IP网段类型" prop="filterType" v-bind="formItemLayout">
              <a-radio-group v-model="model.filterType" @change="onChange">
                <a-radio :value="'-1'">全网</a-radio>
                <a-radio :value="'0'">子网组</a-radio>
                <a-radio :value="'1'">子网</a-radio>
                <a-radio :value="'2'">网段</a-radio>
                <a-radio :value="'3'">扩展</a-radio>
              </a-radio-group>
            </a-form-model-item>
          </a-col>
          <a-col v-if="model.filterType === '0' || model.filterType === '1' || model.filterType === '2'" :sm="12" :xs="24">
            <a-form-model-item label="IP网段" prop="ipFilter" v-bind="formItemLayout">
              <a-select v-model="model.ipFilter" :allowClear="true" placeholder="请选择IP网段">
                <a-select-option v-for="item in auditList" :key="item.id">{{ item.title }}</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col v-if="model.filterType === '3'" :sm="12" :xs="24">
            <a-form-model-item label="IP网段" prop="customIpSegment" v-bind="formItemLayout">
              <a-input v-model="model.customIpSegment" :allowClear="true" autocomplete="off"
                placeholder="参考格式：************-255,*************"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :sm="12" :xs="24">
            <a-form-model-item label="启用状态" prop="isEnable" v-bind="formItemLayout">
              <a-radio-group v-model="model.isEnable">
                <a-radio :value="'1'">启用</a-radio>
                <a-radio :value="'0'">禁用</a-radio>
              </a-radio-group>
            </a-form-model-item>
          </a-col>
          <a-col :sm="12" :xs="24">
            <a-form-model-item label="执行方式" prop="executeType" v-bind="formItemLayout">
              <a-radio-group v-model="model.executeType">
                <a-radio value="1">周期执行</a-radio>
                <a-radio value="0">手动</a-radio>
              </a-radio-group>
            </a-form-model-item>
          </a-col>
          <a-col v-if="model.executeType == '1'" :sm="12" :xs="24">
            <a-form-model-item label="扫描频率" prop="executeCron" v-bind="formItemLayout">
              <j-cron v-model="model.executeCron" @change="setCorn"></j-cron>
            </a-form-model-item>
          </a-col>
          <a-col :sm="12" :xs="24">
            <a-form-model-item label="连接协议" prop="transferProtocol" v-bind="formItemLayout">
              <a-select v-model="model.transferProtocol" placeholder="请选择连接协议" :allow-clear="true"
                @change="changeTransferProtocol">
                <a-select-option v-for="item in transferProtocolList" :label="item.name" :value="item.id"
                  :key="item.id">{{ item.name }}</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row v-if="model.transferProtocol && model.transferProtocol.length > 0">
          <div class="colorBox">
            <span class="colorTotal">{{ model.transferProtocol }}连接参数</span>
          </div>
          <!-- 连接协议 start-->
          <template v-if="model.transferProtocol == 'SNMP'">
            <a-col :sm="12" :xs="24">
              <a-form-model-item label="端口号" prop="port" v-bind="formItemLayout">
                <a-input placeholder="请输入端口号" v-model="model.taskParam.port"></a-input>
              </a-form-model-item>
            </a-col>
            <a-col :sm="12" :xs="24">
              <a-form-model-item label="SNMP版本" prop="version" v-bind="formItemLayout">
                <a-radio-group ref="InpCheck" v-model="model.taskParam.version">
                  <a-radio value="V1">V1</a-radio>
                  <a-radio value="V2">V2</a-radio>
                  <a-radio value="V3">V3</a-radio>
                </a-radio-group>
              </a-form-model-item>
            </a-col>
            <a-col :sm="12" :xs="24">
              <a-form-model-item label="团体名" prop="community" v-bind="formItemLayout">
                <a-input placeholder="请输入团体名" v-model="model.taskParam.community"
                  :disabled="snmpRNameDisabled"></a-input>
              </a-form-model-item>
            </a-col>
            <a-col :sm="12" :xs="24">
              <a-form-model-item label="用户名" prop="username" v-bind="formItemLayout">
                <a-input placeholder="请输入用户名" v-model="model.taskParam.username"
                  :disabled="snmpUNameDisabled"></a-input>
              </a-form-model-item>
            </a-col>
            <a-col :sm="12" :xs="24">
              <a-form-model-item label="安全级别" prop="snmpAuthLevel" v-bind="formItemLayout">
                <a-select placeholder="请选择安全级别" allowClear v-model="model.taskParam.snmpAuthLevel"
                  :disabled="snmpAuthPrivDisabled">
                  <a-select-option value="noAuthNoPriv">noAuthNoPriv</a-select-option>
                  <a-select-option value="AuthNoPriv">AuthNoPriv</a-select-option>
                  <a-select-option value="AuthPriv">AuthPriv</a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :sm="12" :xs="24">
              <a-form-model-item label="认证协议" prop="sAuth" v-bind="formItemLayout">
                <a-select v-model="model.taskParam.sAuth" :disabled="sAuthDisabled" placeholder="请选择认证协议" allowClear>
                  <a-select-option value="MD5">MD5</a-select-option>
                  <a-select-option value="SHA">SHA</a-select-option>
                  <a-select-option value="SHA-256">SHA-256</a-select-option>
                  <a-select-option value="SHA-512">SHA-512</a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :sm="12" :xs="24">
              <a-form-model-item label="认证密码" prop="sAuth_passwd" v-bind="formItemLayout">
                <a-input-password v-model="model.taskParam.sAuth_passwd" placeholder="请输入认证密码" autocomplete="new-password"
                  :disabled="sAuthPasswdDisabled"></a-input-password>
              </a-form-model-item>
            </a-col>
            <a-col :sm="12" :xs="24">
              <a-form-model-item label="加密协议" prop="spriv" v-bind="formItemLayout">
                <a-select v-model="model.taskParam.spriv" :disabled="sprivDisabled" placeholder="请选择加密协议" allowClear>
                  <a-select-option value="DES">DES</a-select-option>
                  <a-select-option value="3DES">3DES</a-select-option>
                  <a-select-option value="AES128">AES128</a-select-option>
                  <a-select-option value="AES192">AES192</a-select-option>
                  <a-select-option value="AES256">AES256</a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :sm="12" :xs="24">
              <a-form-model-item label="加密密码" prop="spriv_passwd" v-bind="formItemLayout">
                <a-input-password v-model="model.taskParam.spriv_passwd" placeholder="请输入加密密码" autocomplete="new-password"
                  :disabled="sprivPasswdDisabled" />
              </a-form-model-item>
            </a-col>
          </template>
          <template v-if="model.transferProtocol == 'SSH'">
            <a-col :sm="12" :xs="24">
              <a-form-model-item label="端口号" prop="port" v-bind="formItemLayout">
                <a-input placeholder="请输入端口号" v-model="model.taskParam.port"></a-input>
              </a-form-model-item>
            </a-col>
            <a-col :sm="12" :xs="24">
              <a-form-model-item label="SSH用户名" prop="sshUsername" v-bind="formItemLayout">
                <a-input placeholder="请输入SSH用户名" v-model="model.taskParam.sshUsername"></a-input>
              </a-form-model-item>
            </a-col>
            <a-col :sm="12" :xs="24">
              <a-form-model-item label="SSH密码" prop="sshPassword" v-bind="formItemLayout">
                <a-input-password placeholder="请输入SSH密码" v-model="model.taskParam.sshPassword" autocomplete="new-password"></a-input-password>
              </a-form-model-item>
            </a-col>
            <a-col :sm="12" :xs="24">
              <a-form-model-item label="超时设置" prop="taskParam.timeOut" v-bind="formItemLayout">
                <a-input placeholder="请输入超时设置" v-model="model.taskParam.timeOut" suffix="(ms)" />
              </a-form-model-item>
            </a-col>
          </template>
          <template v-if="model.transferProtocol == 'PING'">
            <a-col :sm="12" :xs="24">
              <a-form-model-item label="超时设置" prop="taskParam.timeOut" v-bind="formItemLayout">
                <a-input placeholder="请输入超时设置" v-model="model.taskParam.timeOut" suffix="(ms)" />
              </a-form-model-item>
            </a-col>
            <a-col :sm="12" :xs="24">
              <a-form-model-item label="数据包大小" prop="taskParam.packetSize" v-bind="formItemLayout">
                <a-input placeholder="请输入数据包大小" v-model="model.taskParam.packetSize" suffix="(byte)" />
              </a-form-model-item>
            </a-col>
          </template>
          <!-- 连接协议 end -->
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import pick from 'lodash.pick'
import JFormContainer from '@/components/jeecg/JFormContainer'
import JCron from '@/components/jeecg/JCron.vue'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
export default {
  name: 'scanTaskForm',
  mixins: [JeecgListMixin],
  components: {
    JFormContainer,
    JCron
  },
  props: {
    data: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    function ipSegmentValidator(rule, value, callback) {
      // 定义正则表达式 
      const ipSegmentRegex = /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(?:-(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?))?(?:,|$)/;
      let arr = value.split(',');
      for (let i = 0; i < arr.length; i++) {
        let ipSegment = arr[i];
        if (!ipSegmentRegex.test(ipSegment)) {
          callback(new Error('请输入正确的IP地址或IP段'));
          return;
        }
      }
      callback()

    };
    return {
      form: this.$form.createForm(this),
      model: {},
      formItemLayout: {
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 9
          },
          md: {
            span: 8
          },
          lg: {
            span: 6
          }
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 14
          },
          md: {
            span: 14
          },
          lg: {
            span: 16
          }
        }
      },
      confirmLoading: false,
      validatorRules: {
        taskName: [
          {
            required: true,
            message: '请输入任务名称'
          },
          {
            min: 2,
            max: 36,
            message: '长度在2到36个字符',
            trigger: 'blur'
          }
        ],
        filterType: [
          {
            required: true,
            message: '请选择IP网段类型'
          }
        ],
        isEnable: [
          {
            required: true,
            message: '请选择启用状态'
          }
        ],
        executeType: [
          {
            required: true,
            message: '请选择执行方式'
          }
        ],
        executeCron: [
          {
            required: true,
            validator: this.validateCorn
          }
        ],
        filterType: [
          {
            required: true,
            message: '请选择IP网段类型'
          }
        ],
        ipFilter: [
          {
            required: true,
            message: '请选择IP网段'
          }
        ],
        customIpSegment: [
          {
            required: true,
            message: '请输入拓展网段'
          },
          {
            // pattern: /^((25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))\.){3}(25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))$/,
            // 修改后的正则表达式，优化了IP地址和IP段的验证
            // 简化版正则表达式
            validator: ipSegmentValidator,
          }
        ],
        gatewayCode: [
          {
            required: true,
            message: '请选择所属网关'
          }
        ],
        transferProtocol: [
          {
            required: true,
            message: '请选择连接协议'
          }
        ],
        'taskParam.timeOut': [
          {
            required: false,
            validator: this.timeOut,
            trigger: 'blur'
          }
        ],
        'taskParam.packetSize': [
          {
            required: false,
            validator: this.pingPacklen,
            trigger: 'blur'
          }
        ]
      },
      url: {
        add: '/deviceScan/task/add', // 添加任务
        edit: '/deviceScan/task/edit', // 编辑任务
        validateCorn: '/autoInspection/devopsAutoInspection/cronCheck', // 校验cron表达式
        gatewayList: '/configureBack/task/getGatewayList', //  获取所属网关
        queryListByType: '/devops/ip/queryListByType'
      },
      transferProtocolList: [
        { name: 'SNMP', id: 'SNMP' },
        { name: 'SSH', id: 'SSH' },
        { name: 'PING', id: 'PING' }
      ],
      auditList: [],
      disableMixinCreated: true,
      gatewayData: [], // 网关数据
      // 连接参数设置
      snmpRNameDisabled: false, // 共同体
      snmpUNameDisabled: false, //用户名
      snmpAuthPrivDisabled: false, //安全级别
      sAuthDisabled: false, //认证协议
      sAuthPasswdDisabled: false, //认证密码
      sprivDisabled: false, //加密协议
      sprivPasswdDisabled: false //加密密码
    }
  },
  methods: {
    closeForm() {
      this.$emit('closeForm')
    },
    add() {
      this.edit({})
    },
    edit(record) {
      this.visible = true
      let taskParam = {
        version: '',
        community: '',
        port: '',
        username: '',
        snmpAuthLevel: undefined,
        sAuth: undefined,
        sAuth_passwd: '',
        spriv: undefined,
        spriv_passwd: '',
        sshUsername: '',
        sshPassword: '',
        timeOut: '',
        packetSize: ''
      }
      this.model = Object.assign(
        {
          taskName: '',
          gatewayCode: undefined,
          isEnable: '1',
          executeType: '0',
          ipFilter: undefined,
          customIpSegment: '',
          filterType: '2',
          executeCron: '0 0 0 * * ? *',
          transferProtocol: undefined,
          taskParam: taskParam
        },
        record
      )
      Promise.all([this.gatewayList(), this.getType(this.model.filterType)]).then(res => {
        this.$nextTick(() => {
          this.form.resetFields()
          //处理是否启用字段
          if (this.model.isEnable === 0) {
            this.model['isEnable'] = '0'
          } else {
            this.model['isEnable'] = '1'
          }
          //处理是执行方式字段
          if (this.model.executeType === 1) {
            this.model['executeType'] = '1'
          } else {
            this.model['executeType'] = '0'
          }
          if (record.taskParam) {
            this.model['taskParam'] = Object.assign(taskParam, JSON.parse(record.taskParam))
          }
        })
      })
    },
    //提交
    submitForm() {
      const that = this
      // 触发表单验证
      this.$refs.form.validate((err, values) => {
        if (err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!that.model.id) {
            httpurl += that.url.add
            method = 'post'
          } else {
            httpurl += that.url.edit
            method = 'put'
          }
          let formData = JSON.parse(JSON.stringify(that.model))
          // 处理连接参数数据
          let taskParam = null
          if (that.model.transferProtocol == 'SNMP') {
            taskParam = {
              version: that.model.taskParam.version,
              community: that.model.taskParam.community,
              port: that.model.taskParam.port,
              username: that.model.taskParam.username,
              snmpAuthLevel: that.model.taskParam.snmpAuthLevel,
              sAuth: that.model.taskParam.sAuth, //认证协议
              sAuth_passwd: that.model.taskParam.sAuth_passwd,
              spriv: that.model.taskParam.spriv, //加密协议
              spriv_passwd: that.model.taskParam.spriv_passwd
            }
          }
          if (that.model.transferProtocol == 'SSH') {
            taskParam = {
              port: that.model.taskParam.port,
              sshUsername: that.model.taskParam.sshUsername,
              sshPassword: that.model.taskParam.sshPassword,
              timeOut: that.model.taskParam.timeOut
            }
          }
          if (that.model.transferProtocol == 'PING') {
            taskParam = {
              timeOut: that.model.taskParam.timeOut,
              packetSize: that.model.taskParam.packetSize
            }
          }
          formData['taskParam'] = taskParam ? JSON.stringify(taskParam) : null
          formData['isEnable'] = that.model.isEnable === '1' ? 1 : 0
          formData['executeCron'] = that.model.executeType === '1' ? that.model.executeCron : ''
          formData['executeType'] = that.model.executeType === '1' ? 1 : 0

          httpAction(httpurl, formData, method)
            .then(res => {
              that.confirmLoading = false
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
                that.closeForm()
              } else {
                that.$message.warning(res.message)
              }
            })
            .catch(err => {
              that.$message.warning(err.message)
              that.confirmLoading = false
            })
        }
      })
    },
    validateCorn(rule, value, callback) {
      if (rule.required) {
        if (value && value.length > 0) {
          getAction(this.url.validateCorn, {
            cronExpression: value
          }).then(res => {
            if (res.success) {
              callback()
            } else {
              callback('cron表达式格式错误!')
            }
          })
        } else {
          callback('请输入cron表达式')
        }
      } else {
        callback()
      }
    },
    //周期执行
    setCorn(data) {
      if (data && data.target != null) {
        let dataList = data.target.value.split(' ')
        if (dataList[0] === '*') {
          this.$message.warning('请确认是否每秒都执行')
        }
      } else {
        let dataList = data.split(' ')
        if (dataList[0] === '*') {
          this.$message.warning('请确认是否每秒都执行')
        }
      }

      if (Object.keys(data).length === 0) {
        this.$message.warning('请输入cron表达式!')
      }
    },
    // 网关下拉框
    gatewayList() {
      getAction(this.url.gatewayList).then(res => {
        if (res.success) {
          this.gatewayData = res.result
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    onChange(e) {
      this.model.ipFilter = ''
      this.model.customIpSegment = ''
      this.$refs.form.clearValidate(['customIpSegment', 'ipFilter'])
      let value = e.target.value
      this.getType(value)
    },
    // 获取网段
    getType(value) {
      if (!value) {
        return
      }
      getAction(this.url.queryListByType, {
        type: value
      }).then(res => {
        if (res.success && res.result) {
          this.auditList = res.result
        }
      })
    },
    changeTransferProtocol() {
      this.$refs.form.clearValidate()
    },
    timeOut(rule, value, callback) {
      let reg = /^[0-9]*$/
      if (value) {
        if (!reg.test(value)) {
          callback('请输入正确的超时值')
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    pingPacklen(rule, value, callback) {
      let reg = /^[0-9]*$/
      if (value) {
        if (!reg.test(value)) {
          callback('请输入正确的数据包大小')
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
  }
}
</script>
<style scoped lang='less'>
.colorBox {
  margin-bottom: 18px;
}

.colorTotal {
  padding-left: 7px;
  border-left: 4px solid #1e3674;
}
</style>