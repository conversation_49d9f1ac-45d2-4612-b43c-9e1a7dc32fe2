<template>
<div style='height: 100%;width: 100%'></div>
</template>
<script>
import Cookies from 'js-cookie'
import { getAction, postAction } from '@api/manage'
export default {
  name: 'boyunView',
  data() {
    return {}
  },
  created() {
    // this.$router.back()
    console.log("router === ",this.$router)
   /* this.$router.replace(
      "/workb/monitorOverview/index"
    ).then(()=>{
      window.open("https://www.baidu.com/","_self")
    });*/
  },
  methods: {

  }
}
</script>

<style scoped lang='less'>

</style>