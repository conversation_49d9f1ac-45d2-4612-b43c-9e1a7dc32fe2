<template>
  <div style="width: 100%; display: flex; flex-direction: column">
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
        <a-row :gutter="24" ref="row" style='margin-bottom: 0px'>
          <a-col :span='spanValue'>
            <a-form-item label="进程名称">
              <a-input placeholder="请输入进程名称" v-model="queryParam.procName" :allowClear='true' autocomplete='off' />
            </a-form-item>
          </a-col>
          <a-col :span='spanValue'>
            <a-form-item label="进程状态">
              <a-select
                style='width: 100%'
                v-model="queryParam.procStatus"
                :allow-clear="true"
                autocomplete="off"
                placeholder="请选择进程状态">
                <a-select-option
                  v-for="(item,index) in processStatusList"
                  :key="'processStatus_'+index"
                  :label="item"
                  :value="item">
                  {{ item }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span='colBtnsSpan()'>
            <span class="table-page-search-submitButtons"
                      :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
              <a-button class="btn-search btn-search-style" type="primary" @click="searchQuery">查询</a-button>
              <a-button class="btn-reset btn-reset-style" @click="searchReset">重置</a-button>
              <a v-if="isVisible" class='btn-updown-style' @click="doToggleSearch">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
              </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 操作按钮区域 -->
    <div class="table-operator table-operator-style">
      <a-button class="btn-add" @click="handleAdd">新增</a-button>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay" style='text-align: center'>
          <a-menu-item key="1" @click="batchDel">禁用</a-menu-item>
          <a-menu-item key="1" @click="batchDel">启用</a-menu-item>
        </a-menu>
        <a-button>批量操作
          <a-icon type="down" />
        </a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <a-table
      ref="table"
      bordered
      :rowKey="(record,index)=>{return record.id}"
      :columns="columns"
      :dataSource="dataSource"
      :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
      :pagination="ipagination"
      :loading="loading"
      @change="handleTableChange">
      <!-- 字符串超长截取省略号显示-->
      <span slot="templateContent" slot-scope="text">
            <j-ellipsis :value="text" :length="25" />
          </span>
      <span slot="action" slot-scope="text, record" class="caozuo" style="padding-top: 10px;padding-bottom: 10px;">
<!--            <a @click="handleDetail(record)">查看</a>
            <a-divider type="vertical" />-->
            <a @click="handleEdit(record)">编辑</a>
            <a-divider type="vertical" />
            <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
              <a>删除</a>
            </a-popconfirm>
          </span>
      <template slot="voice" slot-scope="text,record,index">
        <div style="display:flex; justify-content: space-between;">
          <alarm-upload v-model="record.voice" :file-list='fileList' :showUploadList='true' name="file"
                        :multiple="false">
          </alarm-upload>
          <yq-icon :type="iconName" style="font-size: 20px; cursor: pointer; color: #7da1ff;line-height:40px"
                   @click.native="changeTipStatus(record,index)" />
          <audio muted="true" ref="musicTips" id="musicTips" :src="musicUrl + '/' + record.voice" controls
                 :autoplay="isAutoPlay" style="display:none"></audio>
        </div>
      </template>
      <template slot="tooltip" slot-scope="text">
        <a-tooltip placement="topLeft" :title="text" trigger="hover">
          <div class='tooltip'>
            {{ text }}
          </div>
        </a-tooltip>
      </template>
      <template slot="nameColor" slot-scope="text,record">
        <div :style='{backgroundColor:record.color}'
             style='display:inline-block;color:#ffffff; border-radius: 10px; padding: 2px 10px;'>
          {{ text }}
        </div>
      </template>
    </a-table>
    <!-- table区域-end -->
    <process-modal ref="modalForm" @ok='loadData' :topo-device-list='topoDeviceList' :business-info='businessInfo'></process-modal>
  </div>
</template>

<script>
import {JeecgListMixin} from '@/mixins/JeecgListMixin'

import {
  deleteAction,
  getAction,
  putAction
} from '@/api/manage'
import {YqFormSearchLocation} from '@/mixins/YqFormSearchLocation'
import processModal from '@views/businessMonitoring/businessOverview/modules/processModal.vue'
export default {
  name: 'AlarmLevel',
  props:{
    businessInfo: {
      type: Object,
      required:false,
      default: {},
    },
  },
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components: {
    processModal
  },
  watch:{
    businessInfo: {
      handler(val, oldVal) {
        this.topoDeviceList=[]
         this.init(val)
      },
      immediate: true,
      deep: true,
    },
  },
  data() {
    return {
      description: '进程监控界面',
      formItemLayout: {
        labelCol: {
          style: 'width:80px',
        },
        wrapperCol: {
          style: 'width:calc(100% - 80px)'
        }
      },
      topoDeviceList:[  {deviceName:'deviceName'}],
      processStatusList:["运行","就绪","阻塞","挂起"],
      // 表头
      columns: [
        {
        title: '序号',
        dataIndex: '',
        key: 'rowIndex',
        width: 60,
        customRender: function (t, r, index) {
          return parseInt(index) + 1
        }
      },
        {
          title: '设备名称',
          dataIndex: 'deviceName',
        },
        {
        title: '进程名称',
        dataIndex: 'procName',
      },
        {
          title: 'CPU占用率',
          dataIndex: 'procCpu',
        },
        {
          title: '内存占用',
          dataIndex: 'procMem'
        },
        {
          title: '命令行',
          dataIndex: 'procCmd',
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 200px;max-width:400px'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'tooltip'
          }
        },
       /* {
          title: '进程类型',
          dataIndex: 'processType',
        },*/
        {
          title: '进程号',
          dataIndex: 'procId',
        },
        /*{
          title: '磁盘空间使用情况',
          dataIndex: 'voice',
        },
        {
          title: '吞吐量',
          dataIndex: 'voice'
        },*/
        {
          title: '进程状态',
          dataIndex: 'procStatus'
        },
        {
          title: '描述',
          dataIndex: 'description'
        },
        {
          title: '操作',
          align: 'center',
          width: 180,
          fixed: 'right',
          dataIndex: 'action',
          scopedSlots: {
            customRender: 'action'
          }
        }
      ],
      url: {
        deviceList: '/process/detection/topoDeviceList',
        list: '/process/detection/listSnmp',
        serverList:"/process/detection/queryList",
        delete: '/process/detection/delete',
      },
      disableMixinCreated: true
    }
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    }
  },
  created() {
    this.queryParam.businessId = this.businessInfo.id;
    this.loadData()
  },
  methods: {
    init(record) {
      if (record.topoId) {
        getAction(this.url.deviceList,{topoId:record.topoId}).then((res)=>{
          this.topoDeviceList=[]
         if (res.success){
            this.topoDeviceList=res.result
         }else {
           this.$message.warning(res.message)
         }
        }).catch((err)=>{
          this.$message.warning(err.message)
        })
      }
    },
    changeTipStatus(record, index) {
      if (this.musicIndex != index) {
        this.musicIndex = index
        this.$refs.musicTips.muted = false
        this.$refs.musicTips.pause()
        this.$refs.musicTips.src = this.musicUrl + '/' + record.voice
        setTimeout(() => {
          this.$refs.musicTips.play()
        }, 200)
      } else {
        this.$refs.musicTips.pause()
        this.musicIndex = ''
      }
    },
    searchReset() {
      this.queryParam = {}
      this.queryParam.businessId = this.businessInfo.id;
      this.loadData(1)
    },
  },
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';

/*/deep/.ant-table-tbody .ant-table-row td {
  padding-top: 5px;
  padding-bottom: 5px;
}*/
</style>
