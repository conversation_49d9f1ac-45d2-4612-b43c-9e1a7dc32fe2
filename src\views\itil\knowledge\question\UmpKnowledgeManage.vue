<template>
  <div style="height:100%;">
      <component :is="pageName" style="height:100%" :data="data"/>
  </div>
</template>
<script>
  import UmpKnowledgeList from './UmpKnowledgeList'
  import UmpKnowledgeDetails from './modules/UmpKnowledgeDetails'
  export default {
    name: "TerminalManage",
    data() {
      return {
        isActive: 0,
        data:{}
      };
    },
    components: {
      UmpKnowledgeList,
      UmpKnowledgeDetails
    },
    created(){
      this.pButton1(0);
    },
    //使用计算属性
    computed: {
      pageName() {
        switch (this.isActive) {
          case 0:
            return "UmpKnowledgeList";
            break;

          default:
            return "UmpKnowledgeDetails";
            break;
        }
      }
    },
    methods: {
      pButton1(index) {
        this.isActive = index;
      },
      pButton2(index,item) {
        this.isActive = index;
        this.data = item;
      }
    }
  }
</script>