<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll zxw">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <!-- 查询区域 -->
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <div class="table-page-search-wrapper-style">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="软件名称">
                  <a-input placeholder="请输入软件名称" :allowClear="true" autocomplete="off" v-model="queryParam.softwareName"
                    :maxLength="50">
                  </a-input>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="软件版本">
                  <a-input placeholder="请输入软件版本" :allowClear="true" autocomplete="off"
                    v-model="queryParam.softwareVersion" :maxLength="50">
                  </a-input>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="cpu架构">
                  <a-select :allow-clear="true" v-model="queryParam.softwareCpu" placeholder="请选择cpu架构">
                    <a-select-option v-for="(item, key) in cpuList" :key="key" :value="item.value">
                      {{ item.text || item.label }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue" v-show='toggleSearchStatus'>
                <a-form-item label="操作系统">
                  <a-select :allow-clear="true" v-model="queryParam.softwareOs" placeholder="请选择操作系统">
                    <a-select-option v-for="(item, key) in dictOptions" :key="key" :value="item.value">
                      {{ item.text || item.label }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="colBtnsSpan()">
                <span class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button type="primary" @click="searchQuery" class="btn-search-style">查询</a-button>
                  <a-button @click="searchReset" style="margin-left: 10px" class="btn-reset-style">重置</a-button>
                  <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <!-- 查询区域-END -->

      <a-card :bordered="false" style="flex: auto" class="core">
        <a-row class="lastBtn2">
          <!-- 操作按钮区域 -->
          <div class="table-operator">
            <a-button @click="handleAdd" v-has="'drive:add'">新增</a-button>
            <a-dropdown v-if="selectedRowKeys.length > 0">
              <a-menu slot="overlay" style='text-align: center'>
                <a-menu-item key="1" v-has="'drive:delete'" @click="batchDel">删除</a-menu-item>
              </a-menu>
              <a-button> 批量操作
                <a-icon type="down" />
              </a-button>
            </a-dropdown>
          </div>
        </a-row>
        <!-- table区域-begin -->
        <a-table ref="table" bordered rowKey="id" :columns="columns" :dataSource="dataSource" :pagination="ipagination"
          :loading="loading" :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          class="j-table-force-nowrap" @change="handleTableChange">
          <template slot="htmlSlot" slot-scope="text">
            <div v-html="text"></div>
          </template>
          <template slot="imgSlot" slot-scope="text">
            <span v-if="!text" style="font-size: 14px">无图片</span>
            <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width: 80px; font-size: 14px" />
          </template>
          <template slot="fileSlot" slot-scope="text">
            <span v-if="!text" style="font-size: 14px">无文件</span>
            <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
              下载
            </a-button>
          </template>

          <span slot="action" slot-scope="text, record" class="caozuo"
            style="display: inline-block; white-space: nowrap; text-align: center">
            <a style="color: #409eff" @click="handleDetailPage(record)">查看</a>
            <a-divider type="vertical" />
            <a-dropdown>
              <a class="ant-dropdown-link">更多
                <a-icon type="down" /></a>
              <a-menu slot="overlay">
                <!-- <a-menu-item> </a-menu-item> -->
                <a-menu-item>
                  <a style="color: #409eff" @click="downloadSofware(record)">下载软件</a>
                </a-menu-item>
                 <a-menu-item>
                  <a style="color: #409eff" @click="handleEdit(record)">编辑</a>
                 </a-menu-item>
                <a-menu-item>
                  <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                    <a style="color: #409eff">删除</a>
                  </a-popconfirm>
                </a-menu-item>
              </a-menu>
            </a-dropdown>
          </span>
          <span slot="downloadCount" slot-scope="text">
            <span v-if="text == null">0</span>
            <span v-else>{{ text }}</span>
          </span>
          <span slot="dictName" slot-scope="text">
            {{ getDictName(text) }}
          </span>
          <template slot="tooltip" slot-scope="text">
            <a-tooltip placement="topLeft" :title="text" trigger="hover">
              <div class="tooltip">
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
      </a-card>

      <drive-software-modal ref="modalForm" @ok="modalFormOk"> </drive-software-modal>
    </a-col>
  </a-row>
</template>

<script>
  import '@/assets/less/TableExpand.less'
  import {
    mixinDevice
  } from '@/utils/mixin'
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import driveSoftwareModal from './modules/driveSoftwareModal'
  import {
    filterMultiDictText
  } from '@/components/dict/JDictSelectUtil'
  import JSuperQuery from '@/components/jeecg/JSuperQuery.vue'
  import {
    YqFormSearchLocation
  } from '@/mixins/YqFormSearchLocation'
  import {
    ajaxGetDictItems
  } from '@/api/api'
  import {
    deleteAction, downFile
  } from '@/api/manage'
  import Vue from 'vue'

  export default {
    name: 'driveSoftwareList',
    mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
    components: {
      driveSoftwareModal,
      JSuperQuery,
    },
    data() {
      return {
        description: '软件管理表管理页面',
        // 表头
        columns: [
          {
            title: '软件名称',
            dataIndex: 'softwareName',
          },
          {
            title: '软件版本',
            dataIndex: 'softwareVersion'
          },
          {
            title: '操作系统',
            dataIndex: 'softwareOs',
            scopedSlots: {
              customRender: 'dictName'
            }
          },
          {
            title: 'cpu架构',
            dataIndex: 'softwareCpu'
          },
          {
            title: '下载次数',
            dataIndex: 'downloadCount',
            scopedSlots: {
              customRender: 'downloadCount'
            },
            customCell: () => {
              let cellStyle = 'text-align: right'
              return {
                style: cellStyle,
              }
            }
          },
          {
            title: '软件文件路径',
            dataIndex: 'softwareFile',
            customCell: () => {
              let cellStyle = 'text-align: left'
              return {
                style: cellStyle,
              }
            }
          },
          {
            title: '描述',
            dataIndex: 'softwareDescribe',
            customCell: () => {
              let cellStyle = 'text-align: left; min-width: 120px;max-width:300px'
              return {
                style: cellStyle,
              }
            },
            scopedSlots: {
              customRender: 'tooltip'
            }
          },
          {
            title: '操作',
            dataIndex: 'action',
            fixed: 'right',
            align: 'center',
            width: 147,
            scopedSlots: {
              customRender: 'action'
            },
          },
        ],
        url: {
          list: '/software/softwareRepository/list',
          delete: '/software/softwareRepository/deleteBatch',
          deleteBatch: '/software/softwareRepository/deleteBatch',
          exportXlsUrl: '/software/softwareRepository/exportXls',
          importExcelUrl: 'drive/driveInfo/importExcel',
        },
        dictOptions: [],
        cpuList: [],
      }
    },
    created() {},
    mounted() {
      this.initDictData()
    },
    computed: {
      importExcelUrl: function () {
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
      },
    },
    methods: {
      initDictData() {
        //根据字典Code, 初始化字典数组
        ajaxGetDictItems('cpuArch', null).then((res) => {
          if (res.success) {
            this.cpuList = res.result
          }
        })
        ajaxGetDictItems('os_type', null).then((res) => {
          if (res.success) {
            this.dictOptions = res.result
          }
        })
      },
      getDictName(value) {
        let dictName = ''
        this.dictOptions.forEach((res, i) => {
          if (this.dictOptions[i].value == value) {
            dictName = this.dictOptions[i].text
          }
        })
        return dictName
      },
      handleDelete: function (id) {
        if (!this.url.delete) {
          this.$message.error('请设置url.delete属性!')
          return
        }
        var that = this
        deleteAction(that.url.delete, {
          ids: id
        }).then((res) => {
          if (res.success) {
            that.$message.success(res.message)
            that.loadData()
          } else {
            that.$message.warning(res.message)
          }
        })
      },
      //下载
      downloadSofware(data) {
        let path=data.softwareFile
        if(path){
          let arr = path.split("/")
          let fileName = ""
          if(arr.length > 0){
            fileName = arr[arr.length - 1]
          }
          let url=window._CONFIG['domianURL'] + '/software/softwareRepository/download' + '/' + path + '?id=' + data.id
          this.downloadFileByURL(url,fileName)
        }
        else{
          this.$message.warning("没有软件文件！")
        }
      }
    }
  }
</script>
<style lang="less" scoped>
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';
</style>