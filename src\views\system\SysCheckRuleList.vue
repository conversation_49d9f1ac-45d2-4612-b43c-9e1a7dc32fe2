<template>
  <div style="height: 100%; overflow: hidden; overflow-y: auto">
    <a-card :bordered="false" style="height: 80px; margin-bottom: 10px">
      <!-- 查询区域 -->
      <div class="table-page-search-wrapper">
        <a-form layout="inline" @keyup.enter.native="searchQuery">
          <a-row :gutter="24">
            <a-col :span="6">
              <a-form-item label="规则名称">
                <a-input placeholder="请输入" v-model="queryParam.ruleName" style="width:93%" />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="规则Code">
                <a-input placeholder="请输入" v-model="queryParam.ruleCode" style="width:93%" />
              </a-form-item>
            </a-col>
            <template v-if="toggleSearchStatus"> </template>
            <a-col :span="6">
              <span style="float: left; overflow: hidden" class="table-page-search-submitButtons">
                <a-button type="primary" @click="searchQuery">查询</a-button>
                <a-button @click="searchReset" style="margin-left: 10px">重置</a-button>
                <!-- <a @click="handleToggleSearch" style="margin-left: 8px">
                  {{ toggleSearchStatus ? '收起' : '展开' }}
                  <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                </a> -->
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false" style="height: calc(100% - 90px)" class="core">
      <!-- 操作按钮区域 -->
      <div class="table-operator">
        <a-button @click="handleAdd">新增</a-button>
        <a-button @click="handleExportXls('编码校验规则')">导出</a-button>
        <a-upload
          name="file"
          :showUploadList="false"
          :multiple="false"
          :headers="tokenHeader"
          :action="importExcelUrl"
          @change="handleImportExcel"
        >
          <a-button>导入</a-button>
        </a-upload>
        <a-dropdown v-if="selectedRowKeys.length > 0">
          <a-menu slot="overlay" style='text-align: center'>
            <a-menu-item key="1" @click="batchDel">删除</a-menu-item>
          </a-menu>
          <a-button>
            批量操作
            <a-icon type="down" />
          </a-button>
        </a-dropdown>
      </div>

      <!-- table区域-begin -->

      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        @change="handleTableChange"
      >
        <template slot="action" slot-scope="text, record">
          <div class="caozuo">
            <a @click="handleTest(record)">功能测试</a>
            <a-divider type="vertical" />
            <a @click="handleEdit(record)">编辑</a>
            <a-divider type="vertical" />
            <a-popconfirm title="确定删除吗？" @confirm="handleDelete(record.id)">
              <a>删除</a>
            </a-popconfirm>
            <!-- <a-dropdown>
            <a class="ant-dropdown-link">
              <span>更多</span>
              <a-icon type="down" />
            </a>
            <a-menu slot="overlay">
              <a-menu-item>

              </a-menu-item>
            </a-menu>
          </a-dropdown> -->
          </div>
        </template>
      </a-table>
      <!-- table区域-end -->

      <!-- 表单区域 -->
      <sys-check-rule-modal ref="modalForm" @ok="modalFormOk" />

      <sys-check-rule-test-modal ref="testModal" />
    </a-card>
  </div>
</template>

<script>
import JEllipsis from '@/components/jeecg/JEllipsis'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import SysCheckRuleModal from './modules/SysCheckRuleModal'
import SysCheckRuleTestModal from './modules/SysCheckRuleTestModal'

export default {
  name: 'SysCheckRuleList',
  mixins: [JeecgListMixin],
  components: { SysCheckRuleModal, SysCheckRuleTestModal, JEllipsis },
  data() {
    return {
      description: '编码校验规则管理页面',
      // 表头
      columns: [
        {
          title: '#',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: (t, r, i) => i + 1,
        },
        {
          title: '规则名称',
          align: 'center',
          dataIndex: 'ruleName',
        },
        {
          title: '规则Code',
          align: 'center',
          dataIndex: 'ruleCode',
        },
        {
          title: '规则描述',
          align: 'center',
          dataIndex: 'ruleDescription',
          customRender: (t) => <j-ellipsis value={t} length={48} />,
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          scopedSlots: { customRender: 'action' },
        },
      ],
      url: {
        list: '/sys/checkRule/list',
        delete: '/sys/checkRule/delete',
        deleteBatch: '/sys/checkRule/deleteBatch',
        exportXlsUrl: 'sys/checkRule/exportXls',
        importExcelUrl: 'sys/checkRule/importExcel',
      },
    }
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
  },
  methods: {
    handleTest(record) {
      this.$refs.testModal.open(record.ruleCode)
    },
  },
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>