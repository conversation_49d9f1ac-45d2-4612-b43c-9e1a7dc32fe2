<template>
  <j-modal fullscreen :visible="listVisible" :footer="null" @cancel="hide">
    <template v-slot:title>
      <span>端口列表</span>
      <span style="margin-left: 12px;color: #1890ff" @click="reload"><a-icon type="reload" /></span>
    </template>
    <device-port-list
      ref='portList'
    v-if="listVisible" 
    :deviceInfo="deviceInfo"
    :type="type"
    :oid="oid"
    @showDetail="$emit('showDetail',$event)"
    ></device-port-list>
  </j-modal>
</template>

<script>
import DevicePortList from '@views/devicesystem/deviceshow/DevicePortList.vue'
import { watch } from 'vue'
export default {
  components: {
    DevicePortList,
  },
  props:{
    visible:{
        type:Boolean,
        default:false,
        required:false,
    },
     deviceInfo: {
      type: Object,
      default: () => null,
      required: false,
    },
      type:{
      type: String,
      default: "0",
      required: true,
    },
    oid:{
      type: String,
      default: "",
      required: true,
      
    }
  },
  data(){
    return{
        listVisible:false,
    }
  },
  created() {},
  mounted() {},
  watch:{
    visible(e){
        this.listVisible = e;
    }
  },
  methods: {
    reload() {
      this.$refs.portList.getPortData(true);
    },
    show(e) {
      this.listVisible = true
    },
    hide() {
        if(this.visible){
            this.$emit("hide",false)
        }
        else{
              this.listVisible = false
        }
    
    },
  },
}
</script>

<style>
</style>