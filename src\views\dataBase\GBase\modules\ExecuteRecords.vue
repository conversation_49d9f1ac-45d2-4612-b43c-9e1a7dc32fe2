<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    :destroyOnClose="true"
    :centered="true"
    switchFullscreen
    @cancel="close"
    :footer='null'
    cancelText="关闭"
    okText="保存">
    <div style='min-height: 80vh'>
      <strategyRecords :task-id="taskId"></strategyRecords>
    </div>

  </j-modal>
</template>


<script>
import strategyRecords from '@views/dataBase/GBase/strategyRecords.vue'

export default {
  name: 'ExecuteRecords',
  components: { strategyRecords },
  props: {
  },
  data(){
    return {
      title: '执行记录',
      width: '80%',
      visible: false,
      taskId: '',
    }
  },
  created() {

  },
  mounted() {

  },
  methods:{
    open(record) {
      this.taskId = record.id
      this.visible = true
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
  }
}
</script>



<style scoped lang='less'>

</style>