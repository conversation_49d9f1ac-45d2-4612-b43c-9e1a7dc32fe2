<template>
  <j-modal :maskClosable="false" :okButtonProps="{ class:{'jee-hidden': disableSubmit} }" :width='width' :title="title"
    :visible="visible" @ok='handleOk' cancelText="关闭" switch-fullscreen @cancel="handleCancel">
    <a-form-model :model="form" ref="form" :rules="validatorRules" :label-col="labelCol" :wrapper-col="wrapperCol">
      <a-form-model-item label="名称" required prop="name">
        <a-input v-model="form.name" :allow-clear='true' placeholder='请输入名称' autocomplete='off' />
      </a-form-model-item>
      <a-form-model-item label="日期" prop="date" required>
        <a-date-picker style="width: 100%" placeholder="请选择日期" v-model="form.date" @change="onChange"
          format="YYYY-MM-DD" :getCalendarContainer="node => node.parentNode" />
      </a-form-model-item>
      <!--      <a-form-model-item label="类型" prop="holidayType" required>
        <a-select v-model="form.holidayType" style="width: 100%;">
          <a-select-option :value="true" >国家法定节假日</a-select-option>
          <a-select-option :value="false">自定义</a-select-option>
        </a-select>
      </a-form-model-item>-->
      <a-form-model-item label="状态" prop="holiday" required>
        <a-radio-group v-model="form.holiday">
          <a-radio :value="true">
            休息日
          </a-radio>
          <a-radio :value="false">
            补班日
          </a-radio>
        </a-radio-group>
      </a-form-model-item>
    </a-form-model>
  </j-modal>
</template>

<script>
  import moment from "moment";
  import {
    getAction,
    httpAction
  } from '@/api/manage'
  export default {
    name: 'customFestivalModal',
    data() {
      return {
        title: "",
        time: "",
        width: 800,
        visible: false,
        disableSubmit: false,
        form: {},
        validatorRules: {
          name: [{
              required: true,
              message: '请输入名称!'
            },
            {
              min: 1,
              max: 50,
              message: '名称应在[1-50]个字符之间!'
            }
          ],
          date: [{
            required: true,
            message: '请选择日期!'
          }],
          holiday: [{
            required: true,
            message: '请选择状态!'
          }],
        },
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          },
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          },
        },
      }
    },
    methods: {
      handleCancel() {
        this.visible = false;
      },
      add() {
        this.form = {};
        this.visible = true;
      },
      onChange(date, dateString) {
        console.log(date)
        console.log(dateString)
        this.time = dateString
      },
      edit(record) {
        this.form = {};
        getAction("/festival/custom/queryById", {
          id: record.id
        }).then((res) => {
          if (res.success) {
            let formbrefore = JSON.parse(JSON.stringify(res.result));
            this.form = Object.assign({}, formbrefore);
            this.visible = true;
          } else {
            this.$message.warning(res.message);
          }
        })
      },
      handleOk() {
        this.$refs.form.validate(valid => {
          if (valid) {
            let url = "/festival/custom/add"
            if (this.form.id != undefined) {
              console.log(this.form.id)
              url = "/festival/custom/edit"
            }
            console.log(this.time)
            this.form.date = this.time
            this.form.holidayType = false
            httpAction(url, this.form, "post").then((res) => {
              if (res.success) {
                this.$message.success("操作成功")
                this.handleCancel()
                this.$emit('ok')
              } else {
                this.$message.warning(res.message)
              }
            })
          }
        })
      },
    }
  }
</script>

<style scoped>

</style>