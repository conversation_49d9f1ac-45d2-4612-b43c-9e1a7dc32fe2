<template>
  <a-row style='height: 100%'>
    <a-col style='height: 100%; display: flex; flex-direction: column'>
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0', marginRight: '12px' }" class='card-style'>
        <!-- 查询区域 -->
        <div class='table-page-search-wrapper'>
          <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
            <a-row :gutter='24' ref='row'>
              <a-col v-show="getVisible('name')" :span="spanValue">
                <a-form-item :label="getTitle('name')">
                  <a-input :allow-clear='true' autocomplete='off' :maxLength='maxLength' placeholder='请输入流程名称' type=''
                    v-model='queryParam.processDefinitionName' />
                </a-form-item>
              </a-col>
              <a-col v-show="getVisible('category')" :span="spanValue">
                <a-form-item :label="getTitle('category')">
                  <j-dict-select-tag v-model='queryParam.processDefinitionCategory' placeholder='请选择流程类别'
                    dictCode='bpm_process_type' @change='changeCategory' />
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <span class="table-page-search-submitButtons" style="overflow: hidden;">
                  <a-button icon="search" type="primary" @click="searchQuery">查询</a-button>
                  <a-button icon="reload" style="margin-left: 8px" @click="searchReset">重置</a-button>
                  <a v-if="queryItems.length>0" style="margin-left: 8px" @click="doToggleSearch">{{queryName}}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
        <!-- 查询区域-END -->

        <!--自定义查询项 -->
        <div v-if="toggleSearchStatus" class="custom-query-item">
          <a-checkbox-group v-model="settingQueryItems" :defaultValue="settingQueryItems" style="width:100%"
            @change="onQuerySettingsChange">
            <a-row :gutter="24">
              <template v-for="(item,index) in queryItems">
                <a-col :key="index" v-show='item.checked' :span='querySpanValue' class='col-checkbox'>
                  <a-checkbox :disabled="item.disabled" :value="item.dataIndex">
                    <j-ellipsis :length="7" :value="item.title"></j-ellipsis>
                  </a-checkbox>
                </a-col>
              </template>
            </a-row>
          </a-checkbox-group>
        </div>
        <!-- 自定义查询项-END -->
      </a-card>

      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <!-- 操作按钮区域 -->
        <!-- table区域-begin -->
        <div>
          <!-- <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
            <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{
              selectedRowKeys.length }}</a>项
            <a style="margin-left: 24px" @click="onClearSelected">清空</a>
            <span style="float:right;">
              <a @click="loadData()"><a-icon type="sync" />刷新</a>
              <a-divider type="vertical" />
              <a-popover title="自定义列" trigger="click" placement="leftBottom">
                <template slot="content">
                  <a-checkbox-group @change="onColSettingsChange" v-model="settingColumns" :defaultValue="settingColumns">
                    <a-row style="width: 400px">
                      <template v-for="(item,index) in defColumns">
                        <template v-if="item.key!='rowIndex'&& item.dataIndex!='action'">
                            <a-col :key="index" :span="12"><a-checkbox :value="item.dataIndex"><j-ellipsis :value="item.title" :length="10"></j-ellipsis></a-checkbox></a-col>
                        </template>
                      </template>
                    </a-row>
                  </a-checkbox-group>
                </template>
                <a><a-icon type="setting" />设置</a>
              </a-popover>
            </span>
          </div> -->
          <a-table ref='table' bordered rowKey='id' :columns='columns' :dataSource='dataSource'
            :pagination='ipagination' :loading='loading' @change='handleTableChange'>

            <div slot='filterDropdown'>
              <a-card>
                <a-checkbox-group @change='onColSettingsChange' v-model='settingColumns' :defaultValue='settingColumns'>
                  <a-row style='width: 400px'>
                    <template v-for='(item,index) in defColumns'>
                      <template v-if="item.key!='rowIndex'&& item.dataIndex!='action'">
                        <a-col :span='12' :key="index">
                          <a-checkbox :value='item.dataIndex'>
                            <j-ellipsis :value='item.title' :length='10'></j-ellipsis>
                          </a-checkbox>
                        </a-col>
                      </template>
                    </template>
                  </a-row>
                </a-checkbox-group>
              </a-card>
            </div>

            <template slot='tooltip' slot-scope='text'>
              <a-tooltip placement='top'
                :title='text'
                trigger='hover'>
                <div class='tooltip'>
                  {{ text }}
                </div>
              </a-tooltip>
            </template>

            <a-icon slot='filterIcon' type='setting' :style="{ fontSize:'16px',color:  '#108ee9' }" />

            <span slot='action' slot-scope='text, record'>
              <a @click='handleImage(record)'>流程图</a>
              <a-divider type='vertical' />
              <a @click='btnStartInstance(record)'>发起流程</a>
            </span>
          </a-table>
        </div>
        <!-- table区域-end -->
      </a-card>

      <!--流程图区域-->
      <process-picture ref='processPicture' :row='chooseRow' :pictureVisible='pictureVisible'
        @changePictureVisible='pictureHandleCancel'></process-picture>

      <!-- 表单区域 -->
      <Process-instance-start v-if='dialogStartInstanceVisible'
        :dialogStartInstanceVisible.sync='dialogStartInstanceVisible' :process-definition='processDefinition'>
      </Process-instance-start>
    </a-col>
  </a-row>
</template>

<script>
  import JInput from '@/components/jeecg/JInput.vue'
  import {
    filterDictTextByCache,
    initDictOptions
  } from '@/components/dict/JDictSelectUtil'
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import Vue from 'vue'
  import ProcessInstanceStart from './module/ProcessInstanceStart'
  import ProcessPicture from '../process-definition/modules/ProcessPicture'
  import {
    getAction
  } from '@api/manage'
  import {
    YqFormSeniorSearchLocation
  } from '@/mixins/YqFormSeniorSearchLocation'

  export default {
    name: 'FlowableStartMyProcess',
    mixins: [JeecgListMixin, YqFormSeniorSearchLocation],
    components: {
      JInput,
      ProcessInstanceStart,
      ProcessPicture
    },
    data() {
      return {
        maxLength:50,
        formItemLayout: {
          labelCol: {
            style: 'width:80px'
          },
          wrapperCol: {
            style: 'width:calc(100% - 80px)'
          }
        },
        // 流程图
        chooseRow: {},
        pictureVisible: false,
        selectedProcessDefinitionName: '',
        picturePath: '',
        processDefinition: undefined,
        dialogStartInstanceVisible: false,

        description: '发起流程',
        //字典数组缓存
        sexDictOptions: [],
        //表头
        columns: [],
        //列设置
        settingColumns: [],
        //列定义
        defColumns: [{
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            isUsed: false,
            customCell: () => {
              let cellStyle = 'text-align:center;width:60px'
              return {
                style: cellStyle
              }
            },
            customRender: function (t, r, index) {
              return parseInt(index) + 1
            }
          },
          {
            title: '流程名称',
            dataIndex: 'name',
            isUsed: true,
            customCell: () => {
              let cellStyle = 'text-align:center'
              return {
                style: cellStyle
              }
            },
            scopedSlots: {
              customRender: 'tooltip'
            },
            sorter: true
          },
          {
            title: '流程类别',
            dataIndex: 'category',
            isUsed: true,
            customCell: () => {
              let cellStyle = 'text-align:center'
              return {
                style: cellStyle
              }
            },
            scopedSlots: {
              customRender: 'category'
            },
            customRender: (text) => {
              //字典值替换通用方法
              return filterDictTextByCache('bpm_process_type', text)
            },
            sorter: true
          },
          {
            title: '操作',
            dataIndex: 'action',
            fixed: 'right',
            align: 'center',
            isUsed: false,
            width: 160,
            scopedSlots: {
              customRender: 'action'
            }
            /*  scopedSlots: {
                filterDropdown: 'filterDropdown',
                filterIcon: 'filterIcon',
                customRender: 'action'},*/
          }
        ],
        url: {
          list: '/flowable/processDefinition/listMyself'
        }
      }
    },
    created() {
      this.initColumns()
      this.getColumns(this.columns)
    },
    methods: {
      changeCategory(value) {
        if (!value) {
          this.queryParam.processDefinitionCategory = undefined
        }
      },
      handleImage(row) {
        getAction('/flowable/processDefinition/xml', {
          processDefinitionId: row.id
        }).then((res) => {
          this.chooseRow = row
          this.pictureVisible = true
          this.$nextTick(() => {
            this.$refs.processPicture.initPic(res)
          })
        })
      },
      /*  handleImage(row) {
          this.picturePath = `${window._CONFIG['domianURL']}` + '/flowable/processDefinition/image?processDefinitionId=' + row.id +'&time=' + new Date()
          this.pictureVisible = true
          this.selectedProcessDefinitionName=row.name
        },*/
      btnStartInstance(row) {
        this.processDefinition = row
        this.dialogStartInstanceVisible = true
      },
      pictureHandleOk(e) {
        this.pictureVisible = false
      },
      pictureHandleCancel(e) {
        this.pictureVisible = false
      },
      initDictConfig() {
        //初始化字典 - 性别
        initDictOptions('sex').then((res) => {
          if (res.success) {
            this.sexDictOptions = res.result
          }
        })
      },
      //列设置更改事件
      onColSettingsChange(checkedValues) {
        var key = this.$route.name + ':colsettings'
        Vue.ls.set(key, checkedValues, 7 * 24 * 60 * 60 * 1000)
        this.settingColumns = checkedValues
        const cols = this.defColumns.filter(item => {
          if (item.key == 'rowIndex' || item.dataIndex == 'action') {
            return true
          }
          if (this.settingColumns.includes(item.dataIndex)) {
            return true
          }
          return false
        })
        this.columns = cols
      },
      initColumns() {
        //权限过滤（列权限控制时打开，修改第二个参数为授权码前缀）
        //this.defColumns = colAuthFilter(this.defColumns,'testdemo:');

        var key = this.$route.name + ':colsettings'
        let colSettings = Vue.ls.get(key)
        if (colSettings == null || colSettings == undefined) {
          let allSettingColumns = []
          this.defColumns.forEach(function (item, i, array) {
            allSettingColumns.push(item.dataIndex)
          })
          this.settingColumns = allSettingColumns
          this.columns = this.defColumns
        } else {
          this.settingColumns = colSettings
          const cols = this.defColumns.filter(item => {
            if (item.key == 'rowIndex' || item.dataIndex == 'action') {
              return true
            }
            if (colSettings.includes(item.dataIndex)) {
              return true
            }
            return false
          })
          this.columns = cols
        }
      }
    },
  }
</script>
<style scoped lang='less'>
  @import '~@assets/less/common.less';
  @import '~@assets/less/YQCommon.less';
</style>