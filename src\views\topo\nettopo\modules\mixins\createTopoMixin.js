// 拓扑自定义节点注册 在../graph/shape.js 中
export default {
  data() {
    return {
      // 能创建节点的类型
      nodeTypes: {
        device: 'createDeviceNode',
        topo: 'createToposonNode',
        text: 'createTextNode',
        group: 'createGroupNode',
        virtual: 'createVirtualNode',
        edgeDragNode: 'createEdgeDragNode',
        rectDragNode: 'createRectDragNode',
        dept: 'createDeptNode'
      }

    }
  },
  methods: {
    createTopoNode(type, data) {
      return this[this.nodeTypes[type]](data)
    },
    // 创建设备节点
    createDeviceNode(data) {
      return this.graph.createNode({
        shape: this.globalGridAttr.shapeNames[this.globalGridAttr.topoConfig.shapeType],
        visible: true,
        data: {
          deviceId: data.id,
          deviceCode: data.code,
          deviceName: data.name,
          deviceIp: data.ip || '',
          deviceType: '',
          devicePanel: '',
          productId: data.scopedSlots?.parent?.id,//节点设备的产品id 用来获取面板信息
          productName: data.scopedSlots?.parent?.name,
          nodeType: 'device',
          isVirtual: false,
          nodeScore: 0
        },
        attrs: {
          label: {
            text: data.name,
            fontSize: 13,
            fill: this.globalGridAttr.topoConfig.labelColor
          },
          image: {
            // 'xlink:href': this.picSrc + '/' + data.icon,
            'xlink:href': data.icon.includes('http') ? data.icon : this.picSrc + '/' + data.icon
          }
        }
      })
    },
    //创建单位节点
    createDeptNode(data) {
      const shapes = this.globalGridAttr.shapeNames
      return this.graph.createNode({
        shape: shapes[data.treeLevel - 1],
        visible: true,
        data: {
          deviceId: '',
          deviceCode: '',
          deviceName: '',
          deviceIp: '',
          deviceType: '',
          devicePanel: '',
          productId: '',//节点设备的产品id 用来获取面板信息
          productName: '',
          nodeType: 'dept',
          isVirtual: false,
          nodeScore: 0,
          deptId: data.id
        },
        attrs: {
          body: {
            fill: 'rgba(95,149,255,0.5)'
          },
          label: {
            text: data.code?data.code:data.departName,
            fontSize: 13,
            fill: this.globalGridAttr.topoConfig.labelColor
          },
          image: {
            // 'xlink:href': data.icon.includes('http')? data.icon : this.picSrc + '/' + data.icon,
          }
        }
      })
    },
    //创建虚拟节点
    createVirtualNode() {
      return this.graph.createNode({
        shape: this.globalGridAttr.shapeNames[this.globalGridAttr.topoConfig.shapeType],
        visible: true,
        attrs: {
          body: {
            stroke: 'none',
            fill:  this.onOffColors.onLine,
            opacity: 1
          },
          label: {
            text: '',
            fontSize: 13,
            fill: this.globalGridAttr.topoConfig.labelColor
          }

        },
        data: {
          deviceId: '',
          deviceCode: '',
          deviceName: '',
          deviceType: '',
          devicePanel: '',
          productId: '',
          productName: '',
          status: 1,
          nodeType: this.topoInfo.topoType === '2' ? 'dept' : 'device',
          isVirtual: true,
          nodeScore: 0
        }
      })
    },
    // 创建子拓扑节点
    createToposonNode(data) {
      // console.log("创建子拓扑",data)
      let icon = ''
      if (!data.icon) {
        icon = 'temp/topo_default.jpg'
      } else {
        icon = data.icon
      }
      return this.graph.createNode({
        shape: 'son-topo-node',
        visible: true,
        data: {
          status: 1,
          deviceId: data.id,
          deviceCode: '',
          deviceName: data.name,
          deviceType: data.nodeType,
          nodeType: data.nodeType
        },
        attrs: {
          label: {
            text: data.name,
            fontSize: 13,
            fill: this.globalGridAttr.topoConfig.labelColor
          },
          image: {
            // 'xlink:href': this.picSrc + '/' + icon,
            'xlink:href': icon.includes('http') ? icon : this.picSrc + '/' + icon
          }
        }
      })
    },
    // 创建文字节点
    createTextNode() {
      return this.graph.createNode({
        shape: 'text-block',
        x: 160,
        y: 120,
        width: 128,
        height: 36,
        breakWord: true,
        visible: true,
        attrs: {
          body: {
            fill: 'none',
            stroke: '#8c8c8c',
            rx: 4,
            ry: 4
          },
          label: {
            text: 'text',
            style: {
              fontSize: 13,
              color: this.globalGridAttr.topoConfig.labelColor
            }
          }
        },
        data: {
          nodeType: 'text'
        }
      })
    },
    // 创建容器节点
    createGroupNode() {
      return this.graph.createNode({
        shape: 'networkGroupNode',
        visible: true,
        attrs: {
          label: {
            text: '群组节点',
            fontSize: 13,
            fill: this.globalGridAttr.topoConfig.labelColor
          }
        },
        data: {
          parent: true,
          nodeType: 'group'
        }
      })
    },
    //创建边线拖拽节点
    createEdgeDragNode() {
      return this.graph.createNode(
        {
          shape: 'edgeDragNode',
          width: 10,
          height: 10,
          data: {
            addLine: true,
            dragType: 'start',
            nodeType: 'edgeDrag'
          }
        }
      )
    },
    //创建矩形拖拽节点
    createRectDragNode() {
      return this.graph.createNode(
        {
          shape: 'rectDragNode',
          visible: true,
          data: {
            nodeType: 'rectDragNode'
          }
        }
      )
    }
  }
}