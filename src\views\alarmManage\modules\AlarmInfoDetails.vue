<template>
<div style='height: 100%'>
  <a-row v-if='!isOpeningPreview' class='vScroll' style='height: 100%'>
    <a-col>
      <div class='header-info'>
        <div>
          <p class='p-info-title'>
            <span>设备：{{ data.deviceName }}</span>
            <span :style='{backgroundColor:data.alarmLevel_color}' class='alarm-level'>
                 {{ data.alarmLevel_dictText }}
          </span>
          </p>
          <p class='p-info-product'>
            <span class='span-product'>告警名称：{{ data.templateName }}</span>
          </p>
        </div>
        <div style='text-align: right;float: right;padding-right: 24px;margin-top: 10px;'>
            <img alt='' src='~@/assets/return1.png' style='width: 20px;height: 20px;cursor: pointer' @click='getGo'>
        </div>
      </div>
      <div class='body-info'>
        <!--告警信息-->
        <alarm-basic-info :alarm-info='data'></alarm-basic-info>
        <!-- 与告警关联的知识 添加了按钮权限授权 需要请授权 -->
        <div v-has="'AssetsAlarmManage:knowledge'"  v-if="showKnowledge">
          <alarm-related-knowledge :alarm-info='data' @OpenPreview="OpenPreview"></alarm-related-knowledge>
        </div>
        <!--告警自动恢复-->
        <alarm-auto-recovery ref='alarmAutoRecovery' :alarm-rule-id='data.id'></alarm-auto-recovery>
        <!--评论-->
        <alarm-comment :alarm-info='data'></alarm-comment>
        <!--历史记录-->
<!--        <alarm-operation-history :alarm-info='data'></alarm-operation-history>-->
        <alarm-operation-history-step :alarm-info='data'></alarm-operation-history-step>
      </div>
    </a-col>
  </a-row>
  <!-- 告警关联知识 -->
  <alarm-knowledge-base-info  v-else-if='isOpeningPreview' :data='knowledgeInfo'></alarm-knowledge-base-info>
</div>
</template>

<script>
import alarmBasicInfo from '@views/alarmManage/modules/AlarmBasicInfo.vue'
import alarmComment from '@views/alarmManage/modules/AlarmComment.vue'
// import AlarmOperationHistory from '@views/alarmManage/modules/AlarmOperationHistory.vue'
import AlarmAutoRecovery from '@views/alarmManage/modules/AlarmAutoRecovery.vue'
import alarmOperationHistoryStep from '@views/alarmManage/modules/AlarmOperationHistoryStep.vue'
import alarmRelatedKnowledge from '@views/alarmManage/modules/alarmRelatedKnowledge.vue'
import alarmKnowledgeBaseInfo from '@views/opmg/knowledgeManagement/knowledgeBase/KnowledgeBaseInfo.vue'
export default {
  name: 'AlarmInfoDetails',
  props: {
    data: {
      type: Object
    },
    showKnowledge: {
      type: Boolean,
      required: false,
      default: true
    }
  },
  components: {
    AlarmAutoRecovery,
    alarmBasicInfo,
    alarmComment,
    // AlarmOperationHistory,
    alarmOperationHistoryStep,
    alarmRelatedKnowledge,
    alarmKnowledgeBaseInfo
  },
  data() {
    return {
      disableSubmit: false,
      imgStatus: '../../../assets/img/red-circle.png', //状态标识图标
      closable: false,//不显示右上方关闭按钮
      isOpeningPreview: false,
      knowledgeInfo: {} // 知识详情
    }
  },
  mounted() {
    this.show()
  },
  methods: {
    show() {
      this.visible = true
      //this.$refs.alarmAutoRecovery.getSelfRecoveryLog(this.data.alarmRuleId)
    },
    //返回上一级
    getGo() {
      this.$parent.pButton1(0)
    },
    OpenPreview(knowledgeInfo) {
      this.isOpeningPreview = true
      this.knowledgeInfo = knowledgeInfo.data
    },
    pButton1(index) {
      this.isOpeningPreview = false
      // this.show()
    },
  }
}
</script>

<style lang='less' scoped>
@import '~@assets/less/scroll.less';

.header-info {
  position: relative;
  background-color: white;
  height: 117px;
  margin-bottom: 16px;
  padding: 10px 0 0 24px;
  border-radius: 3px;
  display: flex;
  justify-content: space-between;

  .p-info-title {
    line-height: 45px;
    height: 45px;
    margin-bottom: 0px;
    font-size: 18px;
    color: #000000;

    .alarm-level {
      display: inline-block;
      margin-left: 66px;
      color: #ffffff;
      font-size: 14px;
      border-radius: 20px;
      padding: 2px 10px;
      line-height: 24px;
    }
  }

  .p-info-product {
    line-height: 45px;
    height: 45px;
    margin-bottom: 0px;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
  }
}

.body-info {
  background-color: white;
  padding: 24px;
  margin-right: 1px;
}
</style>
