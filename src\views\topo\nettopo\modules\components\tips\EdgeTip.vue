<template>
  <div id='edgeDiv' class='edge-div'>
    <div>
      <p>
        起始节点：
        <span>{{ edgeConnectNode != null ? edgeConnectNode.source : '' }}</span>
      </p>
      <p>
        终止节点：
        <span>{{ edgeConnectNode != null ? edgeConnectNode.target : '' }}</span>
      </p>
<!--      自定义边线属性-->
      <p v-for='item in edgeProperties' :key='item.value'>
        {{ item.text }}：
        <span>{{ edgeConnectNode != null ? edgeConnectNode[item.value] : '' }}</span>
      </p>
    </div>
  </div>
</template>

<script>

export default {
  props: {
    edgeConnectNode: null,
    cell: null,
    vEdge: { type:Boolean,default: false },
    globalGridAttr: {
      type: Object,
      default: null,
      required: true
    }
  },
  data() {
    return {
      edgeProperties:[],
    }
  },
  created() {
  },
  methods:{
    init(vEdge) {
      this.edgeProperties=[];
      if(!vEdge){
        let selected = this.cell.data.edgeProperties || this.globalGridAttr.topoConfig?.edgeProperties;
        let properties = this.globalGridAttr.edgeProperties;
        if(selected?.length>0 && properties){
          this.edgeProperties = selected.map(el=>{
            return properties.find(ed=>ed.value === el)
          })
        }
      }
    }
  }
}
</script>

<style lang='less' scoped>
.edge-div {
  color: #ffffff;
  p {
    margin-bottom: 0px;
    height: 30px;
    line-height: 30px;
    padding: 0px 10px;
  }
}
</style>