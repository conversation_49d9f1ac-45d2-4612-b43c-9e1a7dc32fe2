<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll zhl zhll">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <!-- 查询区域 -->
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <div class="table-page-search-wrapper">
          <!-- 搜索区域 -->
          <a-form layout="inline" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span='spanValue'>
                <a-form-item label="地区">
                  <a-input :maxLength="maxLength" placeholder="请输入" v-model="queryParamRegion" :allowClear="true" autocomplete="off" />
                </a-form-item>
              </a-col>

              <a-col :span='spanValue'>
                <a-form-item label="单位">
                  <a-input :maxLength="maxLength" placeholder="请输入" v-model="queryParamCompany" :allowClear="true" autocomplete="off"></a-input>
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label="时间" style="white-space: nowrap; width: 100%; text-align: center">
                  <a-month-picker
                    style="width: 45%; float: left; overflow: hidden"
                    :disabledDate="disabledStartDate"
                    v-model="startValue"
                    placeholder="开始年月"
                    @openChange="handleStartOpenChange"
                  />
                  <span>至</span>
                  <a-month-picker
                    style="width: 45%; float: right; overflow: hidden"
                    :disabledDate="disabledEndDate"
                    placeholder="截止年月"
                    v-model="endValue"
                    :open="endOpen"
                    @openChange="handleEndOpenChange"
                  />
                </a-form-item>
              </a-col>

              <a-col :span='colBtnsSpan()'>
                <span
                  class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                >
                  <a-button class="btn-search btn-search-style" type="primary" @click="searchQuery" >查询</a-button
                  >
                  <a-button class="btn-reset btn-reset-style" @click="mySearchReset">重置</a-button>
                    <a v-if="isVisible" class='btn-updown-style' @click="doToggleSearch">
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <!-- table区域 -->
      <a-card :bordered="false" style="width: 100%; flex: auto">
        <!-- 按钮区域 -->
        <div class="table-operator table-operator-style">
          <a-button @click="handleAdd">新增</a-button>
          <a-button @click="handleExportXls('台账月报导出表')">导出</a-button>
          <a-upload
            name="file"
            :showUploadList="false"
            :multiple="false"
            :headers="tokenHeader"
            :action="importExcelUrl"
            @change="handleImportExcel"
          >
            <a-button>导入</a-button>
          </a-upload>
          <a-button @click="handleTemplateXls()">下载模版</a-button>
        </div>

        <!-- table区域 -->
        <a-table
          ref="table"
          bordered
          rowKey="id"
          :columns="columns"
          :dataSource="dataSource"
          :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
          :pagination="ipagination"
          :loading="loading"
          @change="handleTableChange"
        >
          <template slot='region' slot-scope='text, record'>
            <span v-for='dict in sysAreaList' v-if='dict.id == record.region'>
              <a-tooltip placement='topLeft' :title='dict.text' trigger='hover'>
                <div class='tooltip'>
                 {{ dict.text }}
                </div>
              </a-tooltip>
            </span>
          </template>
          <span slot="assets" slot-scope="text, record">
            <span v-for="dict in AssetType" v-if="dict.value == record.assets">
              {{ dict.text }}
            </span>
          </span>
          <span slot="source" slot-scope="text, record">
            <div v-if="record.source == 1">人工修改</div>
            <div v-else>系统生成</div>
          </span>
          <span slot="action" slot-scope="text, record" class="caozuo">
            <a @click="handleDetailPage(record)">查看</a>
            <a-divider type="vertical" />
            <a href="javascript:;" @click="handleEdit(record)"> 编辑 </a>
            <a-divider type="vertical" />
            <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
              <a> 删除 </a>
            </a-popconfirm>
            <!-- <a-dropdown>
            <a class="ant-dropdown-link"> 更多 <a-icon type="down" /> </a>
            <a-menu slot="overlay">
              <a-menu-item> </a-menu-item>
              <a-menu-item> </a-menu-item>
            </a-menu>
          </a-dropdown> -->
          </span>
          <template slot="tooltip" slot-scope="text">
            <a-tooltip placement="topLeft" :title="text" trigger="hover">
              <div class='tooltip'>
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
      </a-card>

      <!-- 下载模版 -->
      <iframe id="download" style="display: none"></iframe>
      <MonthlyMagazineEdit ref="modalForm" :assetType="AssetType" @ok="modalFormOk"></MonthlyMagazineEdit>
    </a-col>
  </a-row>
</template>
<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { ajaxGetDictItems } from '@api/api'
import MonthlyMagazineEdit from './modules/MonthlyMagazineEdit'
import { getAction } from '@/api/manage'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
export default {
  name: 'MonthlyMagazine',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components: {
    MonthlyMagazineEdit,
  },
  data() {
    return {
      maxLength:50,
      formItemLayout: {
        labelCol: {
          style: 'width:60px',
        },
        wrapperCol: {
          style: 'width:calc(100% - 60px)'
        }
      },
      // 表头
      columns: [
        {
          title: '序号',
          dataIndex: '',
          key: 'rowIndex',
          customCell: () => {
            let cellStyle = 'text-align: center; min-width: 80px;max-width:300px'
            return { style: cellStyle }
          },
          customRender: function (t, r, index) {
            return parseInt(index) + 1
          },
        },
        {
          title: '地区',
          dataIndex: 'region',
          scopedSlots: { customRender: 'region' },
          customCell: () => {
            let cellStyle = 'text-align: left; min-width: 80px;max-width:400px'
            return { style: cellStyle }
          },
        },
        {
          title: '单位',
          dataIndex: 'company',
          scopedSlots: { customRender: 'tooltip' },
          customCell: () => {
            let cellStyle = 'text-align: left; min-width: 80px;max-width:400px'
            return { style: cellStyle }
          },
        },
        {
          title: '年份',
          dataIndex: 'year',
          customCell: () => {
            let cellStyle = 'text-align: center; min-width: 80px;max-width:200px'
            return { style: cellStyle }
          },
        },
        {
          title: '月份',
          dataIndex: 'month',
          customCell: () => {
            let cellStyle = 'text-align: center; min-width: 80px;max-width:200px'
            return { style: cellStyle }
          },
        },
        {
          title: '类型',
          dataIndex: 'assets',
          scopedSlots: { customRender: 'assets' },
          customCell: () => {
            let cellStyle = 'text-align: center; min-width: 80px;max-width:300px'
            return { style: cellStyle }
          },
        },
        {
          title: '采购数',
          dataIndex: 'purchaseNum',
          customCell: () => {
            let cellStyle = 'text-align: right; min-width: 90px;max-width:300px'
            return { style: cellStyle }
          },
        },
        {
          title: '下发数',
          dataIndex: 'downNum',
          customCell: () => {
            let cellStyle = 'text-align: right; min-width: 90px;max-width:300px'
            return { style: cellStyle }
          },
        },
        {
          title: '部署数',
          dataIndex: 'deployNum',
          customCell: () => {
            let cellStyle = 'text-align: right; min-width: 90px;max-width:300px'
            return { style: cellStyle }
          },
        },
        {
          title: '上线数',
          dataIndex: 'onlineNum',
          customCell: () => {
            let cellStyle = 'text-align: right; min-width: 90px;max-width:300px'
            return { style: cellStyle }
          },
        },
        {
          title: '替换数',
          dataIndex: 'replaceNum',
          customCell: () => {
            let cellStyle = 'text-align: right; min-width: 90px;max-width:300px'
            return { style: cellStyle }
          },
        },
        {
          title: '暂存数',
          dataIndex: 'depositNum',
          customCell: () => {
            let cellStyle = 'text-align: right; min-width: 90px;max-width:300px'
            return { style: cellStyle }
          },
        },
        {
          title: '转移扶贫数',
          dataIndex: 'transferNum',
          customCell: () => {
            let cellStyle = 'text-align: right; min-width: 120px;max-width:300px'
            return { style: cellStyle }
          },
        },
        {
          title: '销毁数',
          dataIndex: 'destructionNum',
          customCell: () => {
            let cellStyle = 'text-align: right; min-width: 90px;max-width:300px'
            return { style: cellStyle }
          },
        },
        {
          title: '来源',
          dataIndex: 'source',
          scopedSlots: { customRender: 'source' },
          customCell: () => {
            let cellStyle = 'text-align: center; min-width: 80px;max-width:300px'
            return { style: cellStyle }
          },
        },
        {
          title: '操作人',
          dataIndex: 'createByText',
          customCell: () => {
            let cellStyle = 'text-align: center; min-width: 90px;max-width:300px'
            return { style: cellStyle }
          },
        },
        {
          title: '操作',
          dataIndex: 'action',
          fixed:'right',
          align:'center',
          width: 160,
          scopedSlots: { customRender: 'action' },
        },
      ],
      url: {
        list: '/ledger/ledgerMonth/list',
        delete: '/ledger/ledgerMonth/delete',
        deleteBatch: '/ledger/ledgerMonth/deleteBatch',
        exportXlsUrl: '/ledger/ledgerMonth/exportXls',
        importExcelUrl: '/ledger/ledgerMonth/importExcel',
        downloadTemplateXlsUrl: '/ledger/ledgerMonth/downloadTemplate',
        getSysAreaList: '/sys/dict/getSysAreaList',
      },
      //开始时间
      startValue: null,
      //结束时间
      endValue: null,
      //是否自动打开结束时间选择
      endOpen: false,
      //地区查询条件
      queryParamRegion: null,
      //单位查询条件
      queryParamCompany: null,
      //资产类型
      AssetType: [],
      sysAreaList: [],
    }
  },
  mounted() {
    this.getDicData()
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
    downloadTemplateXlsUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.downloadTemplateXlsUrl}`
    },
  },
  watch: {
    //开始时间查询条件
    startValue(val) {
      this.queryParam.queryTimeFrom = val ? val.format('YYYY-MM') : null
    },
    endValue(val) {
      this.queryParam.queryTimeTo = val ? val.format('YYYY-MM') : null
    },
    //截止时间查询条件
    queryParamRegion(val) {
      if (val) {
        this.queryParam.region = val
      } else {
        this.queryParam.region = null
      }
    },
    //单位查询条件
    queryParamCompany(val) {
      if (val) {
        this.queryParam.company = '*' + val + '*'
      } else {
        this.queryParam.company = null
      }
    },
  },
  methods: {
    /* ***********  月份时间范围组件处理   开始  ********** */
    disabledStartDate(startValue) {
      const endValue = this.endValue
      if (!startValue || !endValue) {
        return false
      }
      return startValue.valueOf() > endValue.valueOf()
    },
    disabledEndDate(endValue) {
      const startValue = this.startValue
      if (!endValue || !startValue) {
        return false
      }
      return startValue.valueOf() >= endValue.valueOf()
    },
    handleStartOpenChange(open) {
      if (!open) {
        this.endOpen = true
      }
    },
    handleEndOpenChange(open) {
      this.endOpen = open
    },
    /* ***********  月份时间范围组件处理   结束  ********** */
    //重置搜索框
    mySearchReset() {
      //地区查询条件
      ;(this.queryParamRegion = null),
        //单位查询条件
        (this.queryParamCompany = null),
        (this.startValue = null)
      this.endValue = null
      this.queryParam = {}
      this.loadData(1)
    },
    //excel模板
    handleTemplateXls() {
      const path = this.downloadTemplateXlsUrl
      document.getElementById('download').src = path
    },

    //单个修改
    handleEdit(record) {
      this.$refs.modalForm.edit(record)
      this.$refs.modalForm.title = '修改月报'
      this.$refs.modalForm.disableSubmit = false
    },

    //查看
    handleView(record) {
      this.$refs.modalForm.view(record)
      this.$refs.modalForm.title = '月报详情'
      this.$refs.modalForm.disableSubmit = false
    },
    //字典获取选择框内容--------------------------------------------
    getDicData() {
      this.getQueryAssetType('asset_type')
      this.getSysAreaList()
    },
    getSysAreaList() {
      getAction(this.url.getSysAreaList, null).then((res) => {
        if (res.success) {
          this.sysAreaList = res.result
        }
      })
    },
    getQueryAssetType(code) {
      let that = this
      ajaxGetDictItems(code, null).then((res) => {
        if (res.success) {
          that.AssetType = res.result
        } else {
          that.$message.error('资产类型字典信息获取失败')
        }
      })
    },
  },
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';

/deep/ .ant-calendar-picker {
  div {
    input {
      letter-spacing: 2px;
    }
  }
}
</style>
