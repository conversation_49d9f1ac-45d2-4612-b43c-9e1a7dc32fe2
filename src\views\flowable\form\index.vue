<template>
  <a-row style='height: 100%'>
    <a-col style='height: 100%; display: flex; flex-direction: column'>
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0', marginRight: '12px' }" class='card-style'>
        <!-- 查询区域 -->
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref='row'>
              <a-col v-show="getVisible('formKey')" :span="spanValue">
                <a-form-item :label="getTitle('formKey')">
                  <j-input :allow-clear='true' :maxLength='maxLength' autocomplete='off' placeholder='请输入表单编码' v-model='queryParam.formKey' />
                </a-form-item>
              </a-col>
              <a-col v-show="getVisible('formName')" :span="spanValue">
                <a-form-item :label="getTitle('formName')">
                  <j-input :allow-clear='true' :maxLength='maxLength' autocomplete='off' placeholder='请输入表单名称' v-model='queryParam.formName' />
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <span class="table-page-search-submitButtons" style="overflow: hidden;">
                  <a-button icon="search" type="primary" @click="searchQuery">查询</a-button>
                  <a-button icon="reload" style="margin-left: 8px" @click="searchReset">重置</a-button>
                  <a v-if="queryItems.length>0" style="margin-left: 8px" @click="doToggleSearch">{{queryName}}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
        <!-- 查询区域-END -->

        <!--自定义查询项 -->
        <div v-if="toggleSearchStatus" class="custom-query-item">
          <a-checkbox-group v-model="settingQueryItems" :defaultValue="settingQueryItems" style="width:100%"
            @change="onQuerySettingsChange">
            <a-row :gutter="24">
              <template v-for="(item,index) in queryItems">
                <a-col v-show='item.checked' :span='querySpanValue' class='col-checkbox'>
                  <a-checkbox :disabled="item.disabled" :value="item.dataIndex">
                    <j-ellipsis :length="7" :value="item.title"></j-ellipsis>
                  </a-checkbox>
                </a-col>
              </template>
            </a-row>
          </a-checkbox-group>
        </div>
        <!-- 自定义查询项-END -->
      </a-card>

      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <!-- 操作按钮区域 -->
        <div class="table-operator">
          <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
          <a-dropdown v-if="selectedRowKeys.length > 0">
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item key="1" @click="batchDel">删除</a-menu-item>
            </a-menu>
            <a-button style="margin-left: 8px"> 批量操作
              <a-icon type="down" />
            </a-button>
          </a-dropdown>
        </div>

        <!-- table区域-begin -->
        <div>
          <!--      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
            <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{
              selectedRowKeys.length }}</a>项
            <a style="margin-left: 24px" @click="onClearSelected">清空</a>
            <span style="float:right;">
              <a @click="loadData()"><a-icon type="sync" />刷新</a>
              <a-divider type="vertical" />
              <a-popover title="自定义列" trigger="click" placement="leftBottom">
                <template slot="content">
                  <a-checkbox-group @change="onColSettingsChange" v-model="settingColumns" :defaultValue="settingColumns">
                    <a-row style="width: 400px">
                      <template v-for="(item,index) in defColumns">
                        <template v-if="item.key!='rowIndex'&& item.dataIndex!='action'">
                            <a-col :span="12" :key="index"><a-checkbox :value="item.dataIndex"><j-ellipsis :value="item.title" :length="10"></j-ellipsis></a-checkbox></a-col>
                        </template>
                      </template>
                    </a-row>
                  </a-checkbox-group>
                </template>
                <a><a-icon type="setting" />设置</a>
              </a-popover>
            </span>
          </div>-->

          <a-table ref="table" bordered rowKey="formKey" :columns="columns" :dataSource="dataSource"
            :pagination="ipagination" :loading="loading"
            :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}" @change="handleTableChange">

            <div slot="filterDropdown">
              <a-card>
                <a-checkbox-group @change="onColSettingsChange" v-model="settingColumns" :defaultValue="settingColumns">
                  <a-row style="width: 400px">
                    <template v-for="(item,index) in defColumns">
                      <template v-if="item.key!='rowIndex'&& item.dataIndex!='action'">
                        <a-col :span="12" :key="index">
                          <a-checkbox :value="item.dataIndex">
                            <j-ellipsis :value="item.title" :length="10"></j-ellipsis>
                          </a-checkbox>
                        </a-col>
                      </template>
                    </template>
                  </a-row>
                </a-checkbox-group>
              </a-card>
            </div>
            <a-icon slot="filterIcon" type='setting' :style="{ fontSize:'16px',color:  '#108ee9' }" />

            <span slot="action" slot-scope="text, record">
              <a @click="handleDetail(record)">查看</a>
              <a-divider type="vertical" />
              <a-dropdown>
                <a class="ant-dropdown-link">更多
                  <a-icon type="down" /></a>
                <a-menu slot="overlay">
                  <a-menu-item>
                    <a @click="handleEdit(record)">编辑</a>
                  </a-menu-item>
                  <a-menu-item>
                    <a @click="btnUpdateFormJson(record)">修改表单</a>
                  </a-menu-item>
                  <a-menu-item>
                    <a-popconfirm title="确定删除吗?" @confirm="handleDelete(record.formKey)">
                      <a>删除</a>
                    </a-popconfirm>
                  </a-menu-item>
                </a-menu>
              </a-dropdown>
            </span>
          </a-table>
        </div>
        <!-- table区域-end -->
      </a-card>
      <!-- 表单区域 -->
      <FormModule ref="modalForm" @ok="modalFormOk"></FormModule>
    </a-col>
    <flowable-form-edit ref='flowableFormEdit' :title="formTitle"></flowable-form-edit>
  </a-row>
</template>

<script>
  import FormModule from './modules/FormModule'
  import JSuperQuery from '@/components/jeecg/JSuperQuery.vue'
  import JInput from '@/components/jeecg/JInput.vue'
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import Vue from 'vue'
  import {
    deleteAction
  } from '@api/manage'
  import {
    YqFormSeniorSearchLocation
  } from '@/mixins/YqFormSeniorSearchLocation'
  import FlowableFormEdit from '@views/flowable/form/modules/FlowableFormEdit.vue'

  export default {
    name: 'flowable-form',
    mixins: [JeecgListMixin, YqFormSeniorSearchLocation],
    components: {
      FlowableFormEdit,
      FormModule,
      JSuperQuery,
      JInput
    },
    data() {
      return {
        maxLength:50,
        description: '单表示例列表',
        formItemLayout: {
          labelCol: {
            style: 'width:80px'
          },
          wrapperCol: {
            style: 'width:calc(100% - 80px)'
          }
        },
        //字典数组缓存
        sexDictOptions: [],
        importExcelUrl: `${window._CONFIG['domianURL']}/test/jeecgDemo/importExcel`,
        //列设置
        settingColumns: [],
        //列定义
        columns: [
          {
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            isUsed: false,
            customCell: () => {
              let cellStyle = 'text-align:center;width:60px'
              return {
                style: cellStyle
              }
            },
            customRender: function (t, r, index) {
              return parseInt(index) + 1
            }
          },
          {
            /*title: '表单key',*/
            title: '表单编码',
            dataIndex: 'formKey',
            isUsed: true,
            customCell: () => {
              let cellStyle = 'text-align:center'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '表单名称',
            dataIndex: 'formName',
            isUsed: true,
            customCell: () => {
              let cellStyle = 'text-align:center'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '操作',
            dataIndex: 'action',
            isUsed: false,
            width: 240,
            align: "center",
            fixed: 'right',
            scopedSlots: {
              customRender: 'action'
            },
            /*   scopedSlots: {
                 filterDropdown: 'filterDropdown',
                 filterIcon: 'filterIcon',
                 customRender: 'action'},*/
          }
        ],
        url: {
          list: '/flowable/form/list',
          delete: '/flowable/form/delete',
          deleteBatch: '/flowable/form/deleteBatch',
        },
        formTitle:"",
      }
    },
    created() {
      this.getColumns(this.columns)
      //this.initColumns();
    },
    methods: {
      handleDetail: function (record) {
        this.$refs.modalForm.edit(record)
        this.$refs.modalForm.title = '详情'
        this.$refs.modalForm.disableSubmit = true
        this.$refs.modalForm.dataSourceFlag = true
      },
      handleEdit: function (record) {
        this.$refs.modalForm.edit(record)
        this.$refs.modalForm.title = '编辑'
        this.$refs.modalForm.disableSubmit = false
        this.$refs.modalForm.dataSourceFlag = true
      },
      handleAdd: function () {
        this.$refs.modalForm.add()
        this.$refs.modalForm.title = '新增'
        this.$refs.modalForm.disableSubmit = false
        this.$refs.modalForm.dataSourceFlag = true
      },
      batchDel: function () {
        if (!this.url.deleteBatch) {
          this.$message.error('请设置url.deleteBatch属性!')
          return
        }
        if (this.selectedRowKeys.length <= 0) {
          this.$message.warning('请选择一条记录！')

        } else {
          var ids = ''
          for (var a = 0; a < this.selectedRowKeys.length; a++) {
            ids += this.selectedRowKeys[a] + ','
          }
          var that = this
          this.$confirm({
            title: '确认删除',
            okText: '是',
            cancelText: '否',
            content: '是否删除选中数据?',
            onOk: function () {
              that.loading = true
              deleteAction(that.url.deleteBatch, {
                ids: ids
              }).then((res) => {
                if (res.success) {
                  //重新计算分页问题
                  that.reCalculatePage(that.selectedRowKeys.length)
                  that.$message.success(res.message)
                  that.loadData()
                  that.onClearSelected()
                } else {
                  that.$message.warning(res.message)
                }
              }).finally(() => {
                that.loading = false
              })
            }
          })
        }
      },
      handleDelete: function (id) {
        let that = this
        deleteAction(that.url.delete, {
          id: id
        }).then((res) => {
          if (res.success) {
            //重新计算分页问题
            that.reCalculatePage(1)
            that.$message.success(res.message)
            that.loadData()
          } else {
            that.$message.warning(res.message)
          }
        })
      },
      btnUpdateFormJson(record) {
        this.formTitle = record.formName;
        this.$refs.flowableFormEdit.show(record.formKey)
      },
      initDictConfig() {},
      //跳转单据页面
      jump() {
        this.$router.push({
          path: '/jeecg/helloworld'
        })
      },
      onBirthdayChange: function (value, dateString) {
        this.queryParam.birthday_begin = dateString[0];
        this.queryParam.birthday_end = dateString[1];
      },
      //列设置更改事件
      onColSettingsChange(checkedValues) {
        var key = this.$route.name + ":colsettings";
        Vue.ls.set(key, checkedValues, 7 * 24 * 60 * 60 * 1000)
        this.settingColumns = checkedValues;
        const cols = this.defColumns.filter(item => {
          if (item.key == 'rowIndex' || item.dataIndex == 'action') {
            return true
          }
          if (this.settingColumns.includes(item.dataIndex)) {
            return true
          }
          return false
        })
        this.columns = cols;
      },
      initColumns() {
        //权限过滤（列权限控制时打开，修改第二个参数为授权码前缀）
        //this.defColumns = colAuthFilter(this.defColumns,'testdemo:');

        var key = this.$route.name + ":colsettings";
        let colSettings = Vue.ls.get(key);
        if (colSettings == null || colSettings == undefined) {
          let allSettingColumns = [];
          this.defColumns.forEach(function (item, i, array) {
            allSettingColumns.push(item.dataIndex);
          })
          this.settingColumns = allSettingColumns;
          this.columns = this.defColumns;
        } else {
          this.settingColumns = colSettings;
          const cols = this.defColumns.filter(item => {
            if (item.key == 'rowIndex' || item.dataIndex == 'action') {
              return true;
            }
            if (colSettings.includes(item.dataIndex)) {
              return true;
            }
            return false;
          })
          this.columns = cols;
        }
      }
    },
  }
</script>
<style scoped lang='less'>
  @import '~@assets/less/common.less';
  @import '~@assets/less/YQCommon.less';
</style>