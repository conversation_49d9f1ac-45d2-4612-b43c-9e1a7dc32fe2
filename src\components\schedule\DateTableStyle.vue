<template>
  <div  style="margin-top: 12px;height: 100%">
    <a-table
      bordered
      :pagination="false"
      rowKey="id"
      :columns="columns"
      :data-source="tableData"
      style='height: 100%'
    >
      <a slot="name" slot-scope="text">{{ text }}</a>
      <template slot="time" slot-scope="text">
        <div style='height: 33px;line-height:33px;box-sizing: border-box;border-bottom: 1px solid #eee;'>{{text}}</div>
        <div style='height: 32px;'></div>
      </template>
      <template slot="content" slot-scope="text">
        <div style='height: 33px;line-height:33px;box-sizing: border-box;border-bottom: 1px solid #eee;'></div>
        <div style='height: 32px;'></div>
      </template>
    </a-table>
  </div>
</template>
<script>
let $this;

export default {
  name: "MonthStyle",
  props: {
    days: {
      default: () => [],
    },
    currentWeek: {
      default: 1,
    },
  },
  data() {
    return {
      layWidth: 0,
      layHeight: 0,
      tableData: [],
    };
  },
  created() {
    $this = this;
    this.getTableData();
  },
  mounted() {},
  computed: {
    columns() {
      let map = {
        1: "星期一",
        2: "星期二",
        3: "星期三",
        4: "星期四",
        5: "星期五",
        6: "星期六",
        7: "星期日",
      };
      let columns = [
        {
          title: "时间",
          dataIndex: "time",
          key: "time",
         scopedSlots: { customRender: "time" },
          width: 55,
          align: "center",
          customHeaderCell: this.customHeaderCellFunc,
          customCell: () => {
            let cellStyle = 'text-align:center;padding:0px'
            return { style: cellStyle }
          },
        },
        {
          title: map[this.currentWeek],
          dataIndex: "1",
          key: "1",
          align: "center",
          scopedSlots: { customRender: "content" },
          customHeaderCell: this.customHeaderCellFunc,
          customCell: () => {
            let cellStyle = 'text-align:center;padding:0px'
            return { style: cellStyle }
          },
        },
      ];
      //   if ($this.days[0]) {
      //     let temObj = $this.days[0].dayArr;
      //     columns.forEach((el) => {
      //       if (el.dataIndex !== "time") {
      //         el.title = map[el.dataIndex]
      //         el.align = "center";
      //         el.dateObj = temObj[el.dataIndex - 1];
      //         if (this.isToday(temObj[el.dataIndex - 1])) {
      //           el.customRender = this.customRenderFunc;
      //           el.customCell = this.customCellFunc;
      //         }
      //       }
      //     });
      //   }
      return columns;
    },
  },
  methods: {
    customHeaderCellFunc(column) {
      return {
        style: {
          background: "#f5f8fa",
        },
        class:column.dataIndex === '1'?"test-header-demo":""
      };
    },
    customRenderFunc(value, record, index) {
      const obj = {
        children: value,
        attrs: {},
      };
      return obj;
    },
    rowClassFunc(record, idx) {
      console.log("行跟属性", record, idx);
    },
    customCellFunc(cellData, ridx) {
      return {
        style: { "background-color": "#fcf8e3", color: "red" },
        on: {
          click: (event) => {
            console.log("我就是试试");
          },
          dblclick: (event) => {},
          contextmenu: (event) => {},
          mouseenter: (event) => {},
          mouseleave: (event) => {},
        },
      };
    },
    getTableData() {
      for (let i = 0; i < 24; i++) {
        this.tableData.push({
          id: i,
          time: `${i}时`,
        });
      }
      console.log("获取到数据", this.tableData);
    },
    isToday(item) {
      let d = new Date();
      if (
        item.month === d.getMonth() + 1 &&
        item.date === d.getDate() &&
        item.year === d.getFullYear()
      ) {
        return true;
      } else {
        return false;
      }
    },
  },
};
</script>
<style lang="less" scoped>
/deep/.test-header-demo {
  .ant-table-header-column{
  margin-left:-100px;
}
}
/deep/ .ant-table-tbody .ant-table-row td{
  padding: 0px;
}
/deep/ .ant-table-thead > tr > th{
  padding: 0;
  height: 52px;
}
</style>

