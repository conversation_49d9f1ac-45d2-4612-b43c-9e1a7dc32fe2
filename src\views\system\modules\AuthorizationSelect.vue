<template>
  <div style='width: 100%'>
    <a-select
      v-model='curId'
      style="width: 100%"
      show-search
      :placeholder="placeholder"
      :optionLabelProp='"label"'
      :filter-option="filterOption"
      @change="handleChange"
    >
      <div slot="dropdownRender" slot-scope="menu" v-if='showAdd'>
        <v-nodes :vnodes="menu" />
        <a-divider style="margin: 4px 0;" />
        <div
          class='add'
          @mousedown="e => e.preventDefault()"
          @click.stop="add"
        >
          <a-icon type="plus" />新增
        </div>
      </div>
      <a-select-option v-for='(item,index) in sourceList' :key='item.id' :value='item.id' :label='item.licenseName'>
        <div class='selected-row'>
          <div class='selected-label' style='' :title='item.licenseName'>{{item.licenseName}}</div>
          <div v-if='showDelete||showEdit'>
            <span class='edit' title='编辑' v-if='showEdit' @click.stop='edit(item)'><a-icon type='edit'/></span>
            <span class='delete' title='删除' v-if='showDelete && item.licenseName !== "local"' @click.stop='deleteSource(item)'><a-icon type='delete'/></span>
          </div>
        </div>
      </a-select-option>
    </a-select>

    <!-- 授权来源新增、编辑弹窗-->
    <authorization-editor-modal ref='authorizationEditor' @ok='loadSource'></authorization-editor-modal>
    <author
  </div>
</template>
<script>
import { getAction ,deleteAction} from '@api/manage'
import AuthorizationEditorModal from '@views/system/modules/AuthorizationEditorModal.vue'
export default {
  name:"AuthorizationSelect",
  props:{
    placeholder:{
      type:String,
      required:false,
      default:'请选择'
    },
    dataUrl:{
      type:String,
      required: true
    },
    showAdd:{
      type:Boolean,
      required:false,
      default: true
    },
    showEdit:{
      type:Boolean,
      required:false,
      default: true
    },
    showDelete:{
      type:Boolean,
      required:false,
      default: true
    },
  },
  components: {
    AuthorizationEditorModal,
    VNodes: {
      functional: true,
      render: (h, ctx) => ctx.props.vnodes,
    },
  },
  data(){
    return{
      curId:undefined,
      sourceList:[],
    }
  },
  created() {
    this.getDataSource()
  },
  methods: {
   //获取授权信息列表数据
    async getDataSource() {
      await getAction(this.dataUrl).then((res)=>{
        if (res.success && res.result && res.result.records){
          this.sourceList=res.result.records
          if(this.curId === undefined){
            this.curId = this.sourceList[0].id;
            this.$emit('change',this.sourceList[0])
          }
        }else {
          this.$message.warning(res.message)
        }
      }).catch((err)=>{
        this.$message.warning(err.message)
      })
    },
    //选择授权产品
    handleChange(value) {
      let curLogAnalyze=null
      if (this.sourceList.length>0){
        curLogAnalyze= this.sourceList.find((item)=>{
          return item.id===value
        })
      }
      //console.log('curLogAnalyze===',curLogAnalyze)
      this.$emit('change',curLogAnalyze)
    },
    handleBlur() {
      console.log('blur');
    },
    handleFocus() {
      console.log('focus');
    },
    filterOption(input, option) {
      console.log("option === ",option)
      return (
        option.componentOptions.propsData.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
      );
    },
    /**根据id，删除日志来源数据*/
    deleteSource(item){
      let id = item.id;
      if(this.showDelete){
        this.$confirm({
          content: '确定要删除这条授权产品信息吗？',
          onOk:()=>{
            deleteAction("/license/delete",{id:id}).then((res)=>{
              if (res.success){
                if(id === this.curId){
                  this.curId = undefined;
                }
                this.getDataSource()
                this.$message.success(res.message)
              }else {
                this.$message.warning(res.message)
              }
            }).catch((err)=>{
              this.$message.warning(err.message)
            })
          },
          cancelText: '取消',
          onCancel() {

          },
        });
      }
    },
    add(){
      this.$refs.authorizationEditor.title='新增'
      this.$refs.authorizationEditor.add()
    },
    edit(record){
      this.$refs.authorizationEditor.title='编辑'
      this.$refs.authorizationEditor.edit(record)
    },
    /*重新编辑判断是否需要重新更新产品授权的配置信息*/
    async loadSource(e){
      await this.getDataSource()
      if(e.id && this.curId === e.id){
        let tem = this.sourceList.find(el=>el.id === this.curId)
        if(tem){
          this.$emit('change',tem)
        }
      }
    },
  },
};
</script>
<style scoped lang='less'>
.add{
  padding: 4px 8px;
  cursor: pointer;
  text-align: center;
  color:rgba(0, 0, 0, 0.65)
}
.add:hover{
  color:#409eff
}

.selected-row{
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-flow: row nowrap;
  overflow: hidden;

  .selected-label{
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    margin-right: 8px
  }
  .edit{
    margin-right: 8px;
    color:rgba(0, 0, 0, 0.65)
  }

  .delete{
    color:rgba(0, 0, 0, 0.65)
  }

  .edit:hover,.delete:hover{
    color:#409eff
  }
}
</style>