<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll zxw">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <!-- 查询区域 -->
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <div class="table-page-search-wrapper-style">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="按周查询:">
                  <a-week-picker
                    placeholder="请选择第几周"
                    @change="handleWeekChange"
                    v-model="queryParam.arrangementTimeRange"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>

              <a-col
                :span="colBtnsSpan()"
              >
                <span
                  class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                >
                  <a-button type="primary" @click="searchQuery" class="btn-search-style">查询</a-button>
                  <a-button @click="searchReset" style="margin-left: 10px" class="btn-reset-style">重置</a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>

      <a-card :bordered="false" style="flex: auto" class="core">
        <a-row class="lastBtn2">
          <div class="table-operator">
            <a-button @click="handleAllocateForm">排班</a-button>
            <a-dropdown v-if="selectedRowKeys.length > 0">
              <a-menu slot="overlay" style='text-align: center'>
                <a-menu-item key="1" @click="batchDel">删除</a-menu-item>
              </a-menu>
              <a-button> 批量操作 <a-icon type="down" /></a-button>
            </a-dropdown>
          </div>
        </a-row>
        <!-- 操作按钮区域 -->
        <a-table
          ref="table"
          bordered
          rowKey="id"
          :columns="columns"
          :dataSource="dataSource"
          :pagination="ipagination"
          :loading="loading"
          :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          class="j-table-force-nowrap"
          @change="handleTableChange"
        >
          <template slot="htmlSlot" slot-scope="text">
            <div v-html="text"></div>
          </template>
          <template slot="imgSlot" slot-scope="text">
            <span v-if="!text" style="font-size: 14px">无图片</span>
            <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width: 80px; font-size: 14px" />
          </template>
          <template slot="fileSlot" slot-scope="text">
            <span v-if="!text" style="font-size: 14px">无文件</span>
            <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
              下载
            </a-button>
          </template>

          <span slot="action" slot-scope="text, record" class="caozuo">
            <a @click="handleEdit(record)">编辑</a>
            <a-divider type="vertical" />
            <a @click="handleDetail(record)">详情</a>
            <a-divider type="vertical" />
            <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
              <a>删除</a>
            </a-popconfirm>
          </span>
          <template slot="tooltip" slot-scope="text">
            <a-tooltip placement="topLeft" :title="text" trigger="hover">
              <div class="tooltip">
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
      </a-card>

      <devops-arrangement-info-modal ref="modalForm" @ok="modalFormOk"></devops-arrangement-info-modal>
      <devops-arrangement-allocate-modal ref="allocateForm" @ok="modalFormOk"></devops-arrangement-allocate-modal>
    </a-col>
  </a-row>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import DevopsArrangementInfoModal from './modules/DevopsArrangementInfoModal'
import JSuperQuery from '@/components/jeecg/JSuperQuery.vue'
import moment from 'moment'
import { getAction } from '@/api/manage'
import DevopsArrangementAllocateModal from './modules/DevopsArrangementAllocateModal'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'

export default {
  name: 'DevopsArrangementInfoList',
  mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
  components: {
    DevopsArrangementInfoModal,
    DevopsArrangementAllocateModal,
    JSuperQuery,
  },
  data() {
    return {
      description: '排班表管理页面',
      // 表头
      columns: [
        {
          title: '排班时间',

          dataIndex: 'arrangementTime',
          customRender: function (text) {
            return !text ? '' : text.length > 10 ? text.substr(0, 10) : text
          },
        },
        {
          title: '班次名称',

          dataIndex: 'schedualName',
        },
        {
          title: '值班人',

          dataIndex: 'realname',
        },
      ],
      url: {
        list: '/arrangementinfo/devopsArrangementInfo/list',
        delete: '/arrangementinfo/devopsArrangementInfo/delete',
        deleteBatch: '/arrangementinfo/devopsArrangementInfo/deleteBatch',
        allocateBatch: '/arrangementinfo/devopsArrangementInfo/allocateBatch',
        exportXlsUrl: '/arrangementinfo/devopsArrangementInfo/exportXls',
        importExcelUrl: 'arrangementinfo/devopsArrangementInfo/importExcel',
      },
      dictOptions: {},
      superFieldList: [],
    }
  },
  created() {
    this.getSuperFieldList()
  },
  mounted() {
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
  },
  methods: {
    initDictConfig() {},
    getSuperFieldList() {
      let fieldList = []
      fieldList.push({ type: 'date', value: 'arrangementTime', text: '排班时间' })
      fieldList.push({ type: 'string', value: 'schedualId', text: '班次id', dictCode: '' })
      fieldList.push({ type: 'int', value: 'status', text: '状态', dictCode: '' })
      this.superFieldList = fieldList
    },

    handleWeekChange(weekData, weekStrings) {
      const startDate = moment(weekData).day(1).format('YYYY-MM-DD') // 周一日期
      const endDate = moment(weekData).day(7).format('YYYY-MM-DD') // 周日日期
      this.queryParam.arrangementTime_begin = startDate
      this.queryParam.arrangementTime_end = endDate
    },

    handleAllocateForm() {
      if (this.selectionRows.length <= 0) {
        this.$message.warning('请选择一条记录！')
        return
      } else {
        var f = false
        var ids = ''
        for (var a = 0; a < this.selectionRows.length; a++) {
          let timeNow = this.formatTime(new Date(), 'yyyy-MM-dd')
          if (this.selectionRows[a].arrangementTime < timeNow) {
            f = true
          } else {
            if (this.selectionRows[a].shiftStatus == 1 || this.selectionRows[a].shiftStatus == 2) {
              f = true
            } else {
              ids += this.selectionRows[a].id + ','
            }
          }
        }
        let that = this
        if (f) {
          this.$confirm({
            title: '提示',
            okText: '确认',
            cancelText: '取消',
            content: `存在不可排班的数据是否继续排班！`,
            onOk() {
              that.gohandleAllocateForm(ids)
            },
            onCancel() {},
          })
        } else {
          that.gohandleAllocateForm(ids)
        }
      }
    },
    gohandleAllocateForm(ids) {
      if ('' != ids) {
        this.$refs.allocateForm.edit(ids)
        this.$refs.allocateForm.title = '排班'
        this.$refs.allocateForm.disableSubmit = false
      }
    },
    formatTime: function (date, fmt) {
      var date = new Date(date)
      if (/(y+)/.test(fmt)) {
        fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length))
      }
      var o = {
        'M+': date.getMonth() + 1,
        'd+': date.getDate(),
        'h+': date.getHours(),
        'm+': date.getMinutes(),
        's+': date.getSeconds(),
      }
      for (var k in o) {
        if (new RegExp('(' + k + ')').test(fmt)) {
          var str = o[k] + ''
          fmt = fmt.replace(RegExp.$1, RegExp.$1.length === 1 ? str : ('00' + str).substr(str.length))
        }
      }
      return fmt
    },
    batchAllocate: function () {
      if (!this.url.allocateBatch) {
        this.$message.error('请设置url.batchAllocate!')
        return
      }
      if (this.selectedRowKeys.length <= 0) {
        this.$message.warning('请选择一条记录！')
        return
      } else {
        var ids = ''
        for (var a = 0; a < this.selectedRowKeys.length; a++) {
          ids += this.selectedRowKeys[a] + ','
        }
        var that = this
        this.$confirm({
          title: '信息确认',
          okText: '是',
          cancelText: '否',
          content: '是否修改已选中数据?',
          onOk: function () {
            that.loading = true
            getAction(that.url.allocateBatch, { ids: ids })
              .then((res) => {
                if (res.success) {
                  that.$message.success(res.message)
                  that.loadData()
                  that.onClearSelected()
                } else {
                  that.$message.warning(res.message)
                }
              })
              .finally(() => {
                that.loading = false
              })
          },
        })
      }
    },
  },
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
.table-page-search-wrapper .ant-form-inline .ant-form-item {
  margin-bottom: 0 !important;
}
.ant-table-pagination.ant-pagination {
  margin: 16px 0 0 0 !important;
}

::v-deep .ant-table-thead > tr > th {
  text-align: center;
  white-space: nowrap;
}
/*内容对齐方式、省略显示*/
::v-deep .ant-table-tbody > tr > td {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;

  &:first-child,
  &:nth-child(2),
  &:nth-child(3),
  &:nth-child(4) {
    text-align: center;
  }
}
</style>
