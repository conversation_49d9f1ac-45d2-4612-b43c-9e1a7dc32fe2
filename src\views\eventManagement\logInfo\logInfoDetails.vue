<template>
  <div style='height: 100%'>
    <a-card :bordered='false'>
      <div class='action'>
      <span class='return'>
        <img src='~@/assets/return1.png' alt='' @click='getGo'>
      </span>
      </div>
      <a-descriptions :column='{ xxl: 2, xl: 2, lg: 2, md: 2, sm: 2, xs: 2 }' bordered>
        <a-descriptions-item  v-for='(item,index) in keyList' :label='getChineseLabel(item)' :key='"column_data_"+index'>{{ valueList[index]}}</a-descriptions-item>
      </a-descriptions>
    </a-card>
  </div>
</template>
<script>

import adapterTaskModal from '@views/deviceConfig/adapterTaskManagement/modules/AdapterTaskModal.vue'
import { getAction } from '@api/manage'
import fa from 'element-ui/src/locale/lang/fa'
export default {
  name: 'AdapterTaskDetails',
  props: {
    showDocument:{
      type:Boolean,
      required: false,
      default:false
    },
    data: {
      type: Object,
      required:false,
      default:()=>null
    }
  },
  components:{adapterTaskModal},
  data() {
    return {
      keyList:[],
      valueList:[],
      url:{
        queryById:'/device/productJob/queryById'
      }
    }
  },

  watch: {
    data: {
      handler(nVal, oVal) {
        this.updateData(nVal,this.showDocument)
      },
      deep: true,
      immediate: true,
    },
    showDocument: {
      handler(nVal, oVal) {
        this.updateData(this.data,nVal)
      },
      deep: true,
    }
  },
  methods: {
    // 获取中文标签
    getChineseLabel(key) {
      // 检查是否有对应的mapping字段
      const mappingKey = `${key}_mapping`
      if (this.data && this.data[mappingKey]) {
        return `${this.data[mappingKey]} (${key})`;
      }
      // 如果没有mapping，返回原始key
      return key
    },
    updateData(data, showDocument) {
      this.keyList = []
      this.valueList = []
      if (data && Object.keys(data).length > 0) {
        this.keyList = Object.keys(data).filter((item) => {
          // 过滤掉document字段和所有_mapping结尾的字段
          if (item !== "document" && !item.endsWith('_mapping')) {
            this.valueList.push(data[item])
            return true
          }
          return false
        })
      }
     /* if (data && Object.keys(data).length > 0) {
        this.keyList = Object.keys(data).filter((item) => {
          if (item !== "document" && !showDocument) {
            this.valueList.push(data[item])
            return true
          } else if (item === "document" && showDocument) {
            this.valueList.push(data[item])
            return true
          }
          return false
        })
      }*/
    },

    //返回上一级
    getGo() {
      this.$parent.pButton1(0)
    },
    doEdit(record) {
      this.$refs.modalForm.edit(record)
      this.$refs.modalForm.title = '编辑'
      this.$refs.modalForm.disableSubmit = false
    },
    modalFormOk(){
      getAction(this.url.queryById,{id:this.record.id}).then((res)=>{
        if(res.success){
          this.record=JSON.parse(JSON.stringify(res.result))
        }else {
          this.$message.warning(res.message)
        }
      }).catch((err)=>{
        this.$message.warning(err.message)
      })
    }
  }
}
</script>

<style scoped lang='less'>
.action {
  display: flex;
  justify-content: end;
  align-items: center;
  flex-flow: row nowrap;
  margin-bottom: 12px;

  .return {
    img {
      width: 20px;
      height: 20px;
      cursor: pointer
    }
  }
}
::v-deep .ant-card{
  height: 100%;

  .ant-card-body{
    height: 100%;
    overflow: hidden;
  }
}

::v-deep .ant-descriptions{
  height: calc(100% - 32px);
  overflow: hidden;
  overflow-y: auto;
}
::v-deep .ant-descriptions-view {
  border-radius: 0px;
}

::v-deep .ant-descriptions-bordered .ant-descriptions-item-label {
  background-color: rgb(250, 250, 250);
  text-align: center;
  width: 17%;
}

::v-deep .ant-descriptions-item-label,
.ant-descriptions-item-content {
  color: rgb(96, 98, 102) !important;
}

::v-deep .ant-descriptions-bordered .ant-descriptions-item-content {
  word-break: break-word;
  white-space: normal;
}
</style>