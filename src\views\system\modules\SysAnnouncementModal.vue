<template>
  <a-modal
    :title="title"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    :okButtonProps="{class:{'jee-hidden': disableSubmit} }"
    cancelText="关闭">

    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-row style="width: 100%;">
          <a-col :span="12">
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="标题">
              <a-input placeholder="请输入标题" v-decorator="['titile', validatorRules.title]" :readOnly="disableSubmit" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="消息类型">
              <a-select v-decorator="[ 'msgCategory', validatorRules.msgCategory]" placeholder="请选择消息类型"
                        :disabled="disableSubmit" :getPopupContainer="(target) => target.parentNode">
                <a-select-option value="1">通知公告</a-select-option>
                <a-select-option value="2">系统消息</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="截止时间" class="endTime">
              <j-date style="width: 100%" :getCalendarContainer="node => node.parentNode"
                      v-decorator="[ 'endTime', validatorRules.endTime]" placeholder="请选择截止时间" showTime
                      dateFormat="YYYY-MM-DD HH:mm:ss" :disabled-date="disabledDate"></j-date>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="优先级">
              <a-select v-decorator="[ 'priority']" placeholder="请选择优先级" :disabled="disableSubmit"
                        :getPopupContainer="(target) => target.parentNode">
                <a-select-option value="L">低</a-select-option>
                <a-select-option value="M">中</a-select-option>
                <a-select-option value="H">高</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="通告类型">
              <a-select v-decorator="[ 'msgType', validatorRules.msgType]" placeholder="请选择通告类型"
                        :disabled="disableSubmit" @change="chooseMsgType" :getPopupContainer="(target) => target.parentNode">
                <a-select-option value="USER">指定用户</a-select-option>
                <a-select-option value="ALL">全体用户</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="摘要">
              <a-textarea placeholder="请输入摘要" v-decorator="['msgAbstract',validatorRules.msgAbstract]" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="指定用户" v-if="userType">
              <a-select mode="multiple" placeholder="请选择用户" :labelInValue=true v-model="selectedUser"
                        @dropdownVisibleChange="selectUserIds" @change="handleChange">
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item :labelCol="labelColX1" :wrapperCol="wrapperColX1" label="内容" class="j-field-content">
              <div style="border: 1px solid #ccc; z-index: 20000 !important">
                <Toolbar style="border-bottom: 1px solid #ccc" :editor="editor" :defaultConfig="toolbarConfig"
                         :mode="mode" />
                <Editor style="height: 400px; overflow-y: hidden"
                        v-model='model.msgContent'
                        :defaultConfig="editorConfig" :mode="mode" @onCreated="onCreated" />
              </div>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-spin>
    <select-user-list-modal ref="UserListModal" @choseUser="choseUser"></select-user-list-modal>
  </a-modal>
</template>

<script>
import {
  httpAction
} from '@/api/manage'
import pick from 'lodash.pick'
import {
  getAction
} from '@/api/manage'
import JDate from '@/components/jeecg/JDate'
import JEditor from '@/components/jeecg/JEditor'
import SelectUserListModal from "./SelectUserListModal";
import {setImgAllPath,setImgRelativePath} from '@/utils/imagePathAboutTinymce'
import {
  ACCESS_TOKEN
} from '@/store/mutation-types'
import {
  Editor,
  Toolbar
} from '@wangeditor/editor-for-vue'
import moment from 'moment'

export default {
  components: {
    JEditor,
    JDate,
    SelectUserListModal,
    Editor,
    Toolbar
  },
  name: "SysAnnouncementModal",
  data() {
    return {
      title: "操作",
      visible: false,
      editor: null,
      toolbarConfig: {},
      mode: 'default', // or 'simple'
      editorConfig: {
        placeholder: '请输入内容...',
        zindex: 20000,
        MENU_CONF: {
          uploadImage: {
            server: window._CONFIG['domianURL'] + '/sys/common/upload',
            headers: {
              //所以token放这里
              'X-Access-Token': this.$ls.get(ACCESS_TOKEN),
            },
            fieldName: 'file',
            customInsert(res, insertFn) {
              const url = window._CONFIG['staticDomainURL'] + '/' + res.message
              const alt = ''
              const href = ''
              insertFn(url, alt, href)
            },
          },
          otherMenuKey: {},
          insertImage: {
            onInsertedImage(imageNode) {
              this.customParseImageSrc()
            },
          },
          editImage: {
            onUpdatedImage(imageNode) {
              this.customParseImageSrc()
            },
          },
        },
      },
      disableSubmit: false,
      model: {},
      labelCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 6
        },
      },
      wrapperCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 18
        },
      },
      labelColX1: {
        xs: {
          span: 24
        },
        sm: {
          span: 3
        },
      },
      wrapperColX1: {
        xs: {
          span: 24
        },
        sm: {
          span: 21
        },
      },
      confirmLoading: false,
      form: this.$form.createForm(this),
      validatorRules: {
        title: {
          rules: [{
            required: true,
            message: '请输入标题!'
          }, {
          max: 30,
          message: '标题长度不超过30个字符'
          }]
        },
        msgCategory: {
          rules: [{
            required: true,
            message: '请选择消息类型!'
          }]
        },
        msgType: {
          rules: [{
            required: true,
            message: '请选择通告对象类型!'
          }]
        },
        endTime: {
          rules: [{
            required: true,
            message: '请选择截止时间!'
          }]
        },
        msgAbstract: {
          rules: [{
            required: true,
            message: '请输入摘要!'
          }, {
            max: 200,
            message: '摘要长度不超过200个字符'
          }]
        },
      },
      url: {
        queryByIds: "/sys/user/queryByIds",
        add: "/sys/annountCement/add",
        edit: "/sys/annountCement/edit",
      },
      userType: false,
      userIds: [],
      selectedUser: [],
      disabled: false,
      userList: []
    }
  },
  created() {},
  beforeDestroy() {
    const editor = this.editor
    if (editor == null) return
    editor.destroy() // 组件销毁时，及时销毁编辑器
  },
  methods: {
    disabledDate(current) {
      return current && current.isBefore(moment().subtract(1,"days"));
    },
    customParseImageSrc(src) {},
    onCreated(editor) {
      this.editor = Object.seal(editor) // 一定要用 Object.seal() ，否则会报错
    },
    add() {
      this.edit({});
    },
    edit(record) {
      this.form.resetFields();
      this.model = {}
      this.disable = false;
      this.visible = true;
      this.getUser(record);
    },
    getUser(record) {
      this.model = Object.assign({msgContent:""}, record);
      if (!this.model.priority){
        this.model["priority"]=undefined
      }
      this.model.msgContent=setImgAllPath(record.msgContent)
      // 指定用户
      if (record && record.msgType === "USER") {
        this.userType = true;
        this.userIds = record.userIds;
        getAction(this.url.queryByIds, {
          userIds: this.userIds
        }).then((res) => {
          if (res.success) {
            //update--begin--autor:wangshuai-----date:20200601------for：系统公告选人后，不能删除------
            var userList = [];
            for (var i = 0; i < res.result.length; i++) {
              var user = {};
              user.label = res.result[i].realname;
              user.key = res.result[i].id;
              userList.push(user);
            }
            this.selectedUser = userList;
            //update--begin--autor:wangshuai-----date:20200601------for：系统公告选人后，不能删除------
            this.$refs.UserListModal.edit(res.result, this.userIds);
          }
        });
      }
      this.$nextTick(() => {
        this.form.setFieldsValue(pick(this.model, 'endTime', 'titile', 'priority',
          'msgCategory', 'msgType', 'msgAbstract'))
      });
    },
    close() {
      this.$emit('close');
      this.selectedUser = [];
      this.visible = false;
    },
    handleOk() {
      const that = this;
      //当设置指定用户类型，但用户为空时，后台报错
      if (this.userType && !(this.userIds != null && this.userIds.length > 0)) {
        this.$message.warning('指定用户不能为空！')
        return;
      }
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true;
          let httpurl = '';
          let method = '';
          if (!this.model.id) {
            httpurl += this.url.add;
            method = 'post';
          } else {
            httpurl += this.url.edit;
            method = 'put';
          }
          let formData = Object.assign(this.model, values);
          if (this.userType) {
            formData.userIds = this.userIds;
          }
          let msgContent=setImgRelativePath(formData.msgContent)
          formData.msgContent=msgContent
          httpAction(httpurl, formData, method).then((res) => {
            if (res.success) {
              that.$message.success(res.message);
              that.$emit('ok');
              that.resetUser();
              that.close();
            } else {
              that.$message.warning(res.message);
            }
            that.confirmLoading = false;
          }).catch(() => {
            that.confirmLoading = false;
            that.$message.warning(err.message);
          })
        }
      })
    },
    handleCancel() {
      this.visible = false;
      this.$emit('close');
      this.resetUser();
    },
    resetUser() {
      this.userType = false;
      this.userIds = [];
      this.selectedUser = [];
      this.disabled = false;
      this.$refs.UserListModal.edit(null, null);
    },
    selectUserIds() {
      this.$refs.UserListModal.add(this.selectedUser, this.userIds);
    },
    chooseMsgType(value) {
      if ("USER" == value) {
        this.userType = true;
      } else {
        this.userType = false;
        this.selectedUser = [];
        this.userIds = [];
      }
    },
    // 子modal回调
    choseUser: function (userList) {
      this.selectedUser = [];
      this.userIds = [];
      for (var i = 0; i < userList.length; i++) {
        //update--begin--autor:wangshuai-----date:20200601------for：系统公告选人后，不能删除------
        var user = {};
        user.label = userList[i].realname;
        user.key = userList[i].id;
        this.selectedUser.push(user);
        //update--end--autor:wangshuai-----date:20200601------for：系统公告选人后，不能删除------
        this.userIds += userList[i].id + ","
      }
    },
    handleChange(userList) {
      if (userList) {
        this.userIds = [];
        var users = [];
        for (var i = 0; i < userList.length; i++) {
          var user = {};
          user.id = userList[i].key;
          user.realname = userList[i].label;
          this.userIds += userList[i].key + ',';
          users.push(user);
        }
      }
      this.$refs.UserListModal.edit(users, this.userIds);
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@wangeditor/editor/dist/css/style.css';
::v-deep .w-e-modal .babel-container span{
  margin-bottom: 0px;
}
::v-deep .w-e-modal input{
  padding: 0px 11px;
}
::v-deep .w-e-modal button{
  height: 51px;
}
</style>