<template>
  <div class="calender">
    <!-- <h2 class="calender-title">日历进程</h2> -->
    <div class="calender-content-wrapper">
      <div class="calender-content">
        <calendarHeadar
          :timeData="{
        year: year,
        month: month,
        timeType: timeType,
      }"
          :showToday="showToday"
          :weekDayStr="weekDayStr"
          :dateTitleStr="dateTitleStr"
          @changeTime="changeTime"
          @changeTimeType="changeTimeType"
        ></calendarHeadar>
        <div class="calender-body">
          <eventCalendar
            ref="eventCalendar"
            :year="year"
            :month="month"
            :tYear="tYear"
            :tMonth="tMonth"
            :tDay="tDay"
            :showToday="showToday"
            :showDialog="showDialog"
            :showLook="showLook"
            :timeType="timeType"
            :layHeight="layHeight"
            :asycEventList="asycEventList"
            @addEventShow="addEventShow"
            @deleteNewEvent="deleteNewEvent"
            @editNewShow="editNewShow"
            @checkHaveToday="checkHaveToday"
          ></eventCalendar>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {getAction, httpAction} from "@api/manage";
import dateFunc from "vue-fullcalendar/src/components/dateFunc";
export default {
  name: "calendar",
  data() {
    return {
      year: 1970,
      month: 1,
      aMonth: 1,
      tYear: 1970,
      tMonth: 1,
      tDay: 1,
      timeType: "month",
      currentDay: 1,
      currentMonth: 1,
      currentYear: 1970,
      currentWeek: 1,
      days: [],
      accountList: [],
      showToday: true,
      weekDayStr: "",
      dateTitleStr: "",
    };
  },
  props:{
    asycEventList: {
      default: () => [],
    },
    layHeight: {
      default: 0
    },
    showDialog:false,
    showLook:false
  },
  components: {
    calendarHeadar: () => import("./CalendarHeader"),
    eventCalendar: () => import("./EventCalendar"),
  },
  filters: {},
  mounted() {
    // 每月第一天作为初始参数
    let date = new Date();
    this.tYear = this.year = date.getFullYear();
    this.tMonth = this.month = date.getMonth() + 1;
    this.tDay = date.getDate();
    this.checkAccount();
  },
  methods: {
    checkAccount() {
      let user = window.localStorage.user
        ? JSON.parse(window.localStorage.user)
        : "";
      if (user && !user.parentUserId) {
        this.showAccout = true;
        let sublist = user.subUserList;
        let account = [
          {name: user.name || user.loginAccount, id: user.userId},
        ];
        if (sublist && sublist.length) {
          account = account.concat(sublist);
        }
        this.accountList = account;
      }
    },
    changeTime(a) {
      if (this.timeType === "month") {
        if (a === "today") {
          this.year = this.tYear;
          this.month = this.tMonth;
          this.$emit("loading",this.year + "-" + this.month)
        } else if (a === "add") {
          if (this.month < 12) {
            this.month += 1;
          } else {
            this.year += 1;
            this.month = 1;
          }
        } else if (a === "reduce") {
          if (this.month > 1) {
            this.month -= 1;
          } else {
            this.month = 12;
            this.year -= 1;
          }
        }
        if (this.month !== this.tMonth) {
          let month = this.tMonth
          if (this.aMonth !== 1) {
            month = this.aMonth
          }
          if (month - this.month > 3 || this.month - month > 3) {
            this.$emit("loading",this.year + "-" + this.month)
            this.aMonth = this.month
          }
        }


      } else if (this.timeType === "week") {
        let weekArrObj = this.$refs.eventCalendar.weekArrObj;
        if (weekArrObj.length > 0) {
          let temArr = [];
          if (a === "add") {
            weekArrObj[0].dayArr.forEach((el) => {
              let d = new Date(el.dateStr);
              d.setDate(d.getDate() + 7);
              temArr.push(this.$refs.eventCalendar.dealDateData(d));
            });
            temArr.splice(0, 1)
            temArr.splice(0, 0, {
              dateStr:"时间",
            });
          } else if (a === "reduce") {
            weekArrObj[0].dayArr.forEach((el) => {
              let d = new Date(el.dateStr);
              d.setDate(d.getDate() - 7);
              temArr.push(this.$refs.eventCalendar.dealDateData(d));
            });
            temArr.splice(0, 1)
            temArr.splice(0, 0, {
              dateStr:"时间",
            });
          } else if (a === "today") {
            this.showToday = true;
            this.year = this.tYear;
            this.month = this.tMonth;
            this.$nextTick(() => {
              this.$refs.eventCalendar.getEachCalendar(
                this.$refs.eventCalendar.formatDate(this.year, this.month, 1)
              );
            });
          }
          if (temArr.length > 0) {
            if (
              this.year !== temArr[0].year ||
              this.month !== temArr[0].month
            ) {
              this.year = temArr[0].year;
              this.month = temArr[0].month;
              this.$nextTick(() => {
                this.$refs.eventCalendar.weekArrObj[0].dayArr = temArr;
                this.$refs.eventCalendar.days =
                  this.$refs.eventCalendar.weekArrObj;
                this.checkHaveToday(this.$refs.eventCalendar.days);
              });
            } else {
              this.$refs.eventCalendar.weekArrObj[0].dayArr = temArr;
              this.$refs.eventCalendar.days =
                this.$refs.eventCalendar.weekArrObj;
              this.checkHaveToday(this.$refs.eventCalendar.days);
            }
          }
        }

      } else if (this.timeType === "day") {
        let dateObj = this.$refs.eventCalendar.dateArrObj
        let temArr = []
        if (a === "add") {
          dateObj[0].dayArr.forEach((el) => {
            let d = new Date(el.dateStr);
            d.setDate(d.getDate() + 1);
            temArr.push(this.$refs.eventCalendar.dealDateData(d));
          });
        } else if (a === "reduce") {
          dateObj[0].dayArr.forEach((el) => {
            let d = new Date(el.dateStr);
            d.setDate(d.getDate() - 1);
            temArr.push(this.$refs.eventCalendar.dealDateData(d));
          });
        } else if (a === "today") {
          this.showToday = true;
          if (this.year !== this.tYear || this.month !== this.tMonth) {
            this.year = this.tYear;
            this.month = this.tMonth;
          } else {
            this.$nextTick(() => {
              this.$refs.eventCalendar.getEachCalendar(
                this.$refs.eventCalendar.formatDate(this.year, this.month, 1)
              );
            });
          }
        }
        if (temArr.length > 0) {
          if (
            this.year !== temArr[0].year ||
            this.month !== temArr[0].month
          ) {
            this.year = temArr[0].year;
            this.month = temArr[0].month;
            this.$nextTick(() => {
              dateObj[0].dayArr = temArr;
              this.$refs.eventCalendar.days = dateObj;
              this.$refs.eventCalendar.resetCurrentDate(temArr[0])
              this.checkHaveToday(this.$refs.eventCalendar.days);
            });
          } else {
            dateObj[0].dayArr = temArr;
            this.$refs.eventCalendar.days = dateObj;
            this.$refs.eventCalendar.resetCurrentDate(temArr[0])
            this.checkHaveToday(this.$refs.eventCalendar.days);
          }
        }
      }
    },
    checkHaveToday(days) {
      if (this.timeType === "day") {
        let dateObj = days[0].dayArr[0];
        this.dateTitleStr =
          dateObj.year + "年" + dateObj.month + "月" + dateObj.date + "日";
      } else if (this.timeType === "week") {
        let dateArr = days[0].dayArr;
        let startDate = dateArr[1];
        let endDate = dateArr[7];
        let startStr =
          startDate.year +
          "年" +
          startDate.month +
          "月" +
          startDate.date +
          "日";
        let endStr = "";
        if (
          startDate.year === endDate.year &&
          startDate.month === endDate.month
        ) {
          endStr = endDate.date + "日";
        } else if (startDate.year === endDate.year) {
          endStr = endDate.month + "月" + endDate.date + "日";
        } else {
          endStr =
            endDate.year + "年" + endDate.month + "月" + endDate.date + "日";
        }
        this.weekDayStr = startStr + "—" + endStr;
      }
      let d = new Date();
      let haveToday;
      for (let i = 0; i < days.length; i++) {
        let item = days[i];
        haveToday = item.dayArr.find((el) => {
          return (
            el.month === this.tMonth &&
            el.date === this.tDay &&
            el.year === this.tYear &&
            this.month === this.tMonth &&
            this.year === this.tYear
          );
        });
        if (haveToday) {
          break;
        }
      }
      this.showToday = haveToday ? true : false;
    },
    changeTimeType(t) {
      this.timeType = t; // month or week or day
      if (this.timeType !== "day") {
        this.dateTitleStr = "";
        let dateArrObj = this.$refs.eventCalendar.dateArrObj
        if (this.timeType === "month" && dateArrObj.length > 0) {
          this.year = dateArrObj[0].dayArr[0].year
          this.month = dateArrObj[0].dayArr[0].month
        } else if (this.timeType === "week" && dateArrObj.length > 0) {
          this.year = dateArrObj[0].dayArr[0].year
          this.month = dateArrObj[0].dayArr[0].month
        }
      }
      // console.log(t, "改变日期类型 === ", this.month, this.year);

    },
    //添加弹窗
    addEventShow(t) {
      this.$emit('addEventShow',t);
    },
    //编辑弹窗
    editNewShow(data) {
      this.$emit('editNewShow',data);
    },
    //删除
    deleteNewEvent(id) {
      this.$emit('deleteNewEvent',id);
    },
    // 返回 类似 2016-01-02 格式的字符串
    formatDate(year, month, day) {
      var y = year;
      var m = month;
      if (m < 10) m = "0" + m;
      var d = day;
      if (d < 10) d = "0" + d;
      return y + "-" + m + "-" + d;
    },

  },
};
</script>
<style lang="less" scoped>
.calender {
  background: #fff;
  height: 100%;
  padding: 12px;

  .calender-content-wrapper{
    height:100%;
    overflow:hidden;
    overflow-x: auto;
    .calender-content{
      height: 100%;
      min-width: 1000px;

      .calendar-head{
        margin: 0;
      }

      .calender-title {
        line-height: 60px;
        font-size: 16px;
        text-indent: 20px;
      }

      .calender-body {
        margin-top: 12px;
        //padding: 0 12px;
        /* min-height: calc(100% - 64px);*/
        width: 100%;
        @media (min-width: 600px) {
          height: calc(100% - 44px);
        }
        @media (max-width: 600px) {
          height: calc(100% - 76px);
        }
      }
    }
  }
}
</style>

