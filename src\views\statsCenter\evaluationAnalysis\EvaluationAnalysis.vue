<template>
  <div class="body">
    <background-card>
      <a-row slot="big-screen-content" class="home-row">
        <a-col class="home-col-side">
          <div class="left-top">
            <service-request-number></service-request-number>
          </div>
          <div class="left-bottom">
            <service-request-type-chart :title="'服务请求类型统计'" :unit="'个'"></service-request-type-chart>
          </div>
        </a-col>
        <a-col class="home-col-middle">
          <div class="core-top">
            <div class="core-top-top">
              <service-request :title="'服务请求信息统计'"></service-request>
            </div>
            <div class="core-top-center">
              <reach-standard :title="'服务请求达标率统计'"></reach-standard>
            </div>
          </div>
          <div class="core-bottom">
            <line-chart :title="'服务请求数量统计'"></line-chart>
          </div>
        </a-col>
        <a-col class="home-col-side">
          <div class="right-top">
            <average-handle-time></average-handle-time>
          </div>
          <div class="right-bottom">
            <evaluate-info :title="'评价信息统计'"></evaluate-info>
          </div>
        </a-col>
      </a-row>
    </background-card>
  </div>
</template>

<script>
import backgroundCard from '@views/statsCenter/com/backgroundCard.vue'
import serviceRequestTypeChart from '@views/statsCenter/evaluationAnalysis/modules/serviceRequestTypeChart.vue'
import serviceRequest from '@views/statsCenter/evaluationAnalysis/modules/serviceRequest.vue'
import reachStandard from '@views/statsCenter/evaluationAnalysis/modules/reachStandard.vue'
import lineChart from '@views/statsCenter/evaluationAnalysis/modules/lineChart.vue'
import evaluateInfo from '@views/statsCenter/evaluationAnalysis/modules/evaluateInfo.vue'

import serviceRequestNumber from '@views/statsCenter/evaluationAnalysis/modules/serviceRequestNumber.vue'
import averageHandleTime from '@views/statsCenter/evaluationAnalysis/modules/averageHandleTime.vue'
export default {
  name: 'evaluationAnalysis',
  components: {
    backgroundCard,
    serviceRequestTypeChart,
    serviceRequest,
    reachStandard,
    lineChart,
    evaluateInfo,

    serviceRequestNumber,
    averageHandleTime
  },
  data() {
    return {
      time1: '',
      time2: ''
    }
  }
}
</script>

<style lang="less" scoped>
.body {
  padding: 0rem /* 20/80 */ 0.2rem 0.1125rem 0.2rem;
  width: 100%;
  height: 100%;

  .home-row {
    height: 100%;
    width: 100%;
    display: flex;
    padding: 0 0.2rem;
    flex-flow: row nowrap;
    justify-content: center;
    align-items: start;

    .home-col-side {
      width: 6.61rem; //529/80px
      height: 100%;

      .left-top {
        width: 100%;
        height: 57%;
        background: rgba(250, 250, 250, 0);
        border-radius: 0.075rem;
      }

      .left-bottom {
        width: 100%;
        margin-top: 0.4rem;
        height: calc(43% - 0.4rem);
        background: rgba(250, 250, 250, 0);
        border-radius: 0.075rem;
      }
    }

    .home-col-middle {
      height: 100%;
      width: calc(100% - (6.61rem * 2));
      padding: 0 0.6rem;

      .core-top {
        width: 100%;
        height: 57%;
        position: relative;
        .core-top-top {
          width: 100%;
          height: calc(50% - 0.2rem);
          position: absolute;
          top: 0;
        }
        .core-top-center {
          width: 100%;
          height: 50%;
          height: calc(50% - 0.2rem);
          position: absolute;
          bottom: 0;
        }
      }
      .core-bottom {
        width: 100%;
        height: calc(43% - 0.4rem);
        margin-top: 0.4rem;
      }
    }

    .home-col-side {
      width: 6.61rem; //420/80px
      height: 100%;

      .right-top {
        width: 100%;
        height: 57%;
        background: rgba(250, 250, 250, 0);
        border-radius: 0.075rem /* 6/80 */;
      }

      .right-bottom {
        width: 100%;
        margin-top: 0.4rem;
        height: calc(43% - 0.4rem);
        background: rgba(250, 250, 250, 0);
        border-radius: 0.075rem /* 6/80 */;
      }
    }
  }
}
/deep/ .card-wrapper {
  padding: 0.15rem 0.075rem 0 0;
}
</style>