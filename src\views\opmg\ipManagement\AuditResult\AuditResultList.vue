<template>
  <a-row style='height: 100%; margin-right: 4px; overflow: hidden; overflow-y: auto'>
    <a-col style='width: 100%; height: 100%; display: flex; flex-direction: column'>
      <!-- 查询区域 -->
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0', marginRight: '12px' }" class='card-style'
        style='width: 100%'>
        <div class='table-page-search-wrapper'>
          <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
            <a-row :gutter='24' ref='row'>
              <a-col :span='spanValue'>
                <a-form-item label='任务名称'>
                  <a-input placeholder='请输入任务名称' v-model='queryParam.auditName' :allowClear='true' autocomplete='off'
                    :maxLength="maxLength" />
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label="是否告警">
                  <a-select v-model='queryParam.alarm' :allowClear="true" placeholder="请选择是否告警">
                    <a-select-option value="1">是</a-select-option>
                    <a-select-option value="0">否</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="审计时间">
                  <a-range-picker class="a-range-picker-choice-date" @change="onChange"
                    v-model="queryParam.alarmTime2Range" format="YYYY-MM-DD HH:mm:ss" :placeholder="['开始时间', '截止时间']" />
                </a-form-item>
              </a-col>
              <a-col :span='colBtnsSpan()'>
                <span class='table-page-search-submitButtons'
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button type='primary' class='btn-search btn-search-style' @click='searchQuery'>查询</a-button>
                  <a-button class='btn-reset btn-reset-style' @click='searchReset'>重置</a-button>
                  <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <div class='table-operator table-operator-style'>
          <a-dropdown v-if='selectedRowKeys.length > 0'>
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item key='1' @click='batchDel'>删除</a-menu-item>
            </a-menu>
            <a-button> 批量操作
              <a-icon type='down' />
            </a-button>
          </a-dropdown>
        </div>
        <a-table ref='table' bordered :row-key='(record, index) => {return record.id}' :columns='columns'
          :dataSource='dataSource' :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}" :pagination='ipagination'
          :loading='loading' :rowSelection='{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }'
          @change='handleTableChange'>
          <span slot='action' class='caozuo' slot-scope='text, record'>
            <a @click="handleDetailPage(record)">查看</a>
            <a-divider type='vertical' />
            <a @click="handleExportXls3(record.id,'审计结果')">导出</a>
            <a-divider type='vertical' />
            <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
              <a style="color: #409eff">删除</a>
            </a-popconfirm>
          </span>
          <template slot='alarm' slot-scope='text'>
            <span>{{ text == 1 ? '是' : '否' }}</span>
          </template>
          <template slot='status' slot-scope='text'>
            <span>{{ text == 0 ? '执行中' : text == 1 ? '已完成' : '中断' }}</span>
          </template>
        </a-table>
      </a-card>
    </a-col>
  </a-row>
</template>

<script>
  import '@/assets/less/TableExpand.less'
  import {mixinDevice} from '@/utils/mixin'
  import { JeecgListMixin} from '@/mixins/JeecgListMixin'
  import {downFile} from '@/api/manage'
  import JDictSelectTag from '@/components/dict/JDictSelectTag.vue'
  import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
  export default {
    name: 'AuditResultList',
    mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
    components: {
      JDictSelectTag,
    },
    data() {
      return {
        maxLength:50,
        description: '审计结果管理页面',
        formItemLayout: {
          labelCol: {
            style: 'width:80px'
          },
          wrapperCol: {
            style: 'width:calc(100% - 80px)'
          }
        },
        // 表头
        columns: [{
            title: '任务名称',
            dataIndex: 'auditName'
          },
          {
            title: '审计开始时间',
            dataIndex: 'startTime'
          },
          {
            title: '审计结束时间',
            dataIndex: 'endTime'
          },
          {
            title: '是否告警',
            dataIndex: 'alarm',
            scopedSlots: {
              customRender: 'alarm'
            }
          },
          {
            title: '任务状态',
            dataIndex: 'status',
            scopedSlots: {
              customRender: 'status'
            }
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            fixed: 'right',
            width: 140,
            scopedSlots: {
              customRender: 'action'
            }
          }
        ],
        url: {
          list: '/devops/ip/auditResult/list',
          delete: '/devops/ip/auditResult/delete',
          deleteBatch: '/devops/ip/auditResult/deleteBatch',
          exportXlsUrl: '/devops/ip/auditResult/exportWord',
        },
      }
    },
    created() {},
    mounted() {},
    methods: {
      handleExportXls3(id, fileName) {
        if (!fileName || typeof fileName != 'string') {
          fileName = '导出文件'
        }
        let param = this.getQueryParams()
        if (fileName == '工单知识库') {
          param.recordType = 3
        } else if (fileName == '运维知识库') {
          param.recordType = 4
        }
        if (this.selectedRowKeys && this.selectedRowKeys.length > 0) {
          param['selections'] = this.selectedRowKeys.join(',')
        }
        this.loading = true
        downFile(this.url.exportXlsUrl, {
          id: id
        }).then((data) => {
          if (!data) {
            this.$message.warning('文件下载失败')
            return
          }
          if (typeof window.navigator.msSaveBlob !== 'undefined') {
            window.navigator.msSaveBlob(new Blob([data], {
              type: 'application/vnd.ms-excel'
            }), fileName + '.docx')
          } else {
            if (data.type == 'multipart/form-data') {
              let url = window.URL.createObjectURL(new Blob([data], {
                type: 'multipart/form-data'
              }))
              let link = document.createElement('a')
              link.style.display = 'none'
              link.href = url
              link.setAttribute('download', fileName + '.zip')
              document.body.appendChild(link)
              link.click()
              document.body.removeChild(link) //下载完成移除元素
              window.URL.revokeObjectURL(url) //释放掉blob对象
            } else {
              let url = window.URL.createObjectURL(new Blob([data], {
                type: 'application/vnd.ms-excel'
              }))
              let link = document.createElement('a')
              link.style.display = 'none'
              link.href = url
              link.setAttribute('download', fileName + '.docx')
              document.body.appendChild(link)
              link.click()
              document.body.removeChild(link) //下载完成移除元素
              window.URL.revokeObjectURL(url) //释放掉blob对象
            }
          }
          this.loading = false
        })
      },
      //时间
      onChange(date, dateString) {
        this.queryParam.firstTime = dateString[0]
        this.queryParam.lastTime = dateString[1]
      },
    }
  }
</script>
<style lang='less' scoped>
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';

  .stateBox {
    margin-left: 20px;
  }

  .stateImg {
    vertical-align: middle;
  }

  .alarmStatus {
    margin-left: 8px;
  }

  .overlay {
    color: #409eff
  }
</style>