<template>
  <a-row style="height: 100%">
    <a-col style="height: 100%; display: flex; flex-direction: column">
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0', marginRight: '12px' }" class="card-style">
        <!-- 查询区域 -->
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col v-show="getVisible('warrantyNumber')" :span="spanValue">
                <a-form-item :label="getTitle('warrantyNumber')">
                  <a-input :maxLength='maxLength' v-model="queryParam.warrantyNumber" :allow-clear="true" autocomplete="off"
                    placeholder="请输入服务请求编号"/>
                </a-form-item>
              </a-col>
              <a-col v-show="getVisible('name')" :span="spanValue">
                <a-form-item :label="getTitle('name')">
                  <a-input :maxLength='maxLength' v-model="queryParam.name" :allow-clear="true" autocomplete="off"
                    placeholder="请输入业务标题" />
                </a-form-item>
              </a-col>
              <a-col v-show="getVisible('userUnit')" :span="spanValue">
                <a-form-item label="申请单位">
                  <a-tree-select v-model="queryParam.userUnit" tree-node-filter-prop="title"
                                 :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }"
                    :replaceFields="replaceFields" :treeData="selectOption" style="width: 100%" placeholder="请选择单位"
                    allow-clear @change="onChangeTree">
                  </a-tree-select>
                </a-form-item>
              </a-col>
              <a-col v-show="getVisible('contactUserId')" :span="spanValue">
                <a-form-item label="申请人">
                  <a-select v-show='showValue' :getPopupContainer='node=>node.parentNode'
                    v-model="queryParam.contactUserId" show-search placeholder="请选择申请人" option-filter-prop="children"
                    :filter-option="filterOption" @change='changeUser'>

                    <a-select-option v-for="(item, key) in userList" :key="key" :value="item.id">
                      <div style="display: inline-block; width: 100%" :title="item.realname">
                        {{ item.realname }}
                      </div>
                    </a-select-option>
                  </a-select>
                  <a-select v-show='!showValue' :getPopupContainer='node=>node.parentNode'
                    v-model="queryParam.contactUserId" show-search placeholder="请选择申请人" option-filter-prop="children"
                    :filter-option="filterOption" @change='changeUser'>
                    <a-select-option v-for="(item, key) in userList" :key="key" :value="item.value">
                      <div style="display: inline-block; width: 100%" :title="item.title">
                        {{ item.title }}
                      </div>
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col v-show="getVisible('status')" :span="spanValue">
                <a-form-item :label="getTitle('status')">
                  <a-select v-model="queryParam.status" placeholder="请选择评价状态"
                    :getPopupContainer="(target) => target.parentNode" :allow-clear='true'>
                    <a-select-option :value="2">待评价</a-select-option>
                    <a-select-option :value="3">已评价</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <span class="table-page-search-submitButtons" style="overflow: hidden;">
                  <a-button icon="search" type="primary" @click="searchQuery">查询</a-button>
                  <a-button icon="reload" style="margin-left: 8px" @click="searchReset">重置</a-button>
                  <a v-if="queryItems.length>0" style="margin-left: 8px" @click="doToggleSearch">{{queryName}}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
        <!-- 查询区域-END -->

        <!--自定义查询项 -->
        <div v-if="toggleSearchStatus" class="custom-query-item">
          <a-checkbox-group v-model="settingQueryItems" :defaultValue="settingQueryItems" style="width:100%"
            @change="onQuerySettingsChange">
            <a-row :gutter="24">
              <template v-for="(item,index) in queryItems">
                <a-col v-show='item.checked' :span='querySpanValue' class='col-checkbox'>
                  <a-checkbox :disabled="item.disabled" :value="item.dataIndex">
                    <j-ellipsis :length="7" :value="item.title"></j-ellipsis>
                  </a-checkbox>
                </a-col>
              </template>
            </a-row>
          </a-checkbox-group>
        </div>
        <!-- 自定义查询项-END -->
      </a-card>

      <a-card :bordered="false" style="width: 100%; flex: auto">
        <!-- table区域-begin -->
        <div>
          <a-table ref="table" bordered rowKey="id" :columns="columns" :dataSource="dataSource"
            :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}" :pagination="ipagination" :loading="loading"
            @change="handleTableChange">
            <span slot="status" slot-scope="status">
              <a-tag color="#fa8c16" v-if="status == 2">待评价</a-tag>
              <a-tag color="#87d068" v-if="status == 3">已评价</a-tag>
            </span>

            <template slot="tooltip" slot-scope="text">
              <a-tooltip placement="topLeft" :title="text" trigger="hover">
                <div class="tooltip">{{ text }}</div>
              </a-tooltip>
            </template>

            <a-icon slot="filterIcon" type="setting" :style="{ fontSize: '16px', color: '#108ee9' }" />

            <span slot="action" slot-scope="text, record">
              <template v-if="record.status === '3'">
                <a href="javascript:void(0);" @click="evaluateLook(record)">查看评价</a>
                <a-divider type="vertical" />
              </template>
              <template>
                <a href="javascript:void(0);" @click='handleInstanceInfo(record)'>查看服务请求</a>
              </template>
            </span>
          </a-table>
        </div>
      </a-card>
      <!-- 一查看详情区域 -->
      <process-instance-info-modal ref='processInstanceInfoModalForm' @ok='modalFormOk'></process-instance-info-modal>
      <!-- 查看评价 -->
      <evaluation-modal ref="evaluationModal" @ok="searchReset"></evaluation-modal>
    </a-col>
  </a-row>
</template>

<script>
  import {
    getAction
  } from '@/api/manage'
  import {
    getUserList
  } from '@/api/api'
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import {
    YqFormSeniorSearchLocation
  } from '@/mixins/YqFormSeniorSearchLocation'
  import ProcessInstanceInfoModal from '@views/flowable/process-instance/modules/ProcessInstanceInfoModal'
  import evaluationModal from '@views/flowable/myProcess/modules/EvaluationModal.vue'

  export default {
    name: 'myRate',
    mixins: [JeecgListMixin, YqFormSeniorSearchLocation],
    components: {
      ProcessInstanceInfoModal,
      evaluationModal
    },
    data() {
      return {
        maxLength:50,
        formItemLayout: {
          labelCol: {
            style: 'width:105px'
          },
          wrapperCol: {
            style: 'width:calc(100% - 105px)'
          }
        },
        queryParam: {},
        //表头
        columns: [{
            title: '序号',
            width: 60,
            dataIndex: '',
            key: 'rowIndex',
            isUsed: false,
            customCell: () => {
              let cellStyle = 'text-align:center;'
              return {
                style: cellStyle
              }
            },
            customRender: function (t, r, index) {
              return parseInt(index) + 1
            }
          },
          {
            title: '服务请求编号',
            dataIndex: 'warrantyNumber',
            isUsed: true,
            customCell: () => {
              let cellStyle = 'text-align:left;min-width: 150px;max-width:300px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '业务标题',
            dataIndex: 'name',
            isUsed: true,
            customCell: () => {
              let cellStyle = 'text-align:center;min-width:150px;max-width: 280px;'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '申请部门',
            dataIndex: 'userUnit',
            isUsed: true,
            customCell: () => {
              let cellStyle = 'text-align:center;min-width: 150px;max-width:300px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '申请人',
            dataIndex: 'contactUserId',
            isUsed: true,
            customCell: () => {
              let cellStyle = 'text-align:center;min-width: 150px;max-width:300px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '评价状态',
            align: 'center',
            dataIndex: 'status',
            isUsed: true,
            width: 80,
            scopedSlots: {
              customRender: 'status'
            }
          },
          {
            title: '评价结果',
            dataIndex: 'commentContent',
            customCell: () => {
              let cellStyle = 'text-align:left;min-width: 150px;max-width:300px'
              return {
                style: cellStyle
              }
            },
            scopedSlots: {
              customRender: 'tooltip'
            },
          },
          {
            title: '评价完成时间',
            dataIndex: 'serviceTime',
            align: 'center',
            width: 170
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: 210,
            fixed: 'right',
            scopedSlots: {
              customRender: 'action'
            }
          }
        ],
        url: {
          list: '/evaluate/yqOpServiceProcessevaluate/list',
          cancel: '/business/actZBusiness/cancel'
        },
        selectOption: [],
        replaceFields: {
          children: 'children',
          title: 'deptName',
          key: 'deptId',
          value: 'deptId',
        },
        userList: [],
        showValue: true
      }
    },
    created() {
      this.getColumns(this.columns)
      this.getuserList()
      this.selectDepart()
    },
    methods: {
      evaluateLook(record) {
        this.$refs.evaluationModal.edit(record.proInsId)
        this.$refs.evaluationModal.title = '查看评价'
        this.$refs.evaluationModal.disableSubmit = false
      },
      changeUser() {
        this.$forceUpdate()
      },
      filterOption(input, option) {
        return (
          option.componentOptions.children[0].children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
        )
      },
      getuserList() {
        this.showValue = true
        let param = {
          pageSize: 1000000
        }
        getUserList(param).then((res) => {
          if (res.success) {
            this.userList = res.result.records
          }
        })
      },
      selectDepart() {
        getAction('/sys/sysDepart/queryAllTree').then((res) => {
          for (let i = 0; i < res.length; i++) {
            let temp = res[i]
            this.selectOption.push(temp)
          }
        })
      },
      onChangeTree(value) {
        this.queryParam.contactUserId = undefined
        if (value === undefined || value === "" || value === null) {
          this.getuserList()
          return
        }
        getAction("sys/user/queryList", {
          "deptId": value
        }).then(res => {
          if (res.success && res.result.length > 0) {
            this.showValue = false
            this.userList = res.result
          } else {
            this.getuserList()
          }
        })
      },
      handleInstanceInfo: function (record) {
        if (!record.proInsId) {
          this.$message.error('流程实例ID不存在')
          return
        }
        this.$refs.processInstanceInfoModalForm.init(record.proInsId)
        this.$refs.processInstanceInfoModalForm.title = '流程实例信息'
        this.$refs.processInstanceInfoModalForm.disableSubmit = false
      },
    }
  }
</script>

<style scoped lang='less'>
  @import '~@assets/less/common.less';
  @import '~@assets/less/YQCommon.less';
  @import '~@assets/less/scroll.less';
</style>