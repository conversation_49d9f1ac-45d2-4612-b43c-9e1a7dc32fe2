<template>
  <div class='soft-info'>
    <div class='soft-info-header'>
      <div class='logo-box'>
        <img class='logo' :src='downloadUrl+"/"+softInfo.softwareLogo'>
      </div>
      <div class='info-con'>
        <div class='soft-name'>
          {{ softInfo.softwareName }}
        </div>
        <div class='soft-summary'>
          {{ softInfo.softwareBrief }}
        </div>
        <div class='soft-load'>
          <span> CPU架构：{{ cpuText }} </span>
          <span style='margin-left: 43px'> 操作系统：{{ osText }} </span>
          <span style='margin-left: 43px'> 下载数：{{ softInfo.softwareDownloadNum || 0 }} </span>
        </div>
      </div>
      <img :src='backIcon' alt='' @click='getGo'
           class='goback' />
    </div>
    <a-divider></a-divider>
    <div class='info-block'>
      <div class='info-item'>
        <span class='s-title'>类别</span>
        <span class='s-value'>{{ typeText }}</span>
        <div class="s-line"></div>
      </div>
      <div class='info-item'>
        <span class='s-title'>版本</span>
        <span class='s-value'>{{ softInfo.softwareLatestVersion ||  "--" }}</span>
        <div class="s-line"></div>
      </div>
      <div class='info-item'>
        <span class='s-title'>升级包数量</span>
        <span class='s-value'>{{ softInfo.softwareUpgradePackageNum || 0 }}</span>
        <div class="s-line"></div>
      </div>
      <div class='info-item'>
        <span class='s-title'>更新日期</span>
        <span class='s-value'>{{ softInfo.softwareUpdateDate ||  "--" }}</span>
        <div class="s-line"></div>
      </div>
      <div class='info-item'>
        <span class='s-title'>开发者</span>
        <span class='s-value'>{{ softInfo.softwareProducer }}</span>
      </div>

    </div>
    <div v-if='softInfo.softwareScreenshot && screenshots.length' style='margin-top: 32px;'>
      <div class='colorBox' style='margin-bottom: 12px'>
        <span class="colorTotal">预览</span>
      </div>
     <div style='padding:0 11px'>
       <div class='screen-list' ref='screenList'>
         <img v-for='(item,index) in screenshots' :key='index' :src='downloadUrl+"/"+item' :style='{height:shotHeight+"px"}' >
       </div>
       <div v-if='softInfo.softwareDescribe' class='software-describe' v-html="softInfo.softwareDescribe.replace(/\n/g, '<br/>') || '--'"></div>
     </div>
    </div>

  </div>
</template>
<script>
import { ajaxGetDictItems } from '@api/api'
import {getAction} from '@/api/manage'

export default {
  name: 'SoftInfo',
  props: {
    record: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      downloadUrl: window._CONFIG['downloadUrl'],
      backIcon: require('@/assets/return1.png'),
      screenshots:[],
      dictOptions: [],
      cpuList: [],
      types:[],
      softInfo: {},
      shotHeight: 0
    }
  },
  created() {
    this.initDictData()
    this.getDataById()

  },
  mounted() {

  },
  computed: {
    typeText() {
      return this.types.find(el=>el.value === this.softInfo.softwareType)?.text || "其他"
    },
    cpuText() {
      if(this.softInfo.softwareCpuType){
        let tem = [];
        let arr = this.softInfo.softwareCpuType.split(',')
        arr.forEach(el=>{
          tem.push(this.cpuList.find(item=>item.value === el)?.text)
        })
        return tem.join("，")
      }
      return "--"
    },
    osText() {
      if(this.softInfo.softwareOsType){
        let tem = [];
        let arr = this.softInfo.softwareOsType.split(',')
        arr.forEach(el=>{
          tem.push(this.dictOptions.find(item=>item.value === el)?.text)
        })
        return tem.join("，")
      }
      return "--"
    },
  },
  methods: {
    getDataById() {
      getAction('/software/softwareInfoManage/queryById', { id: this.record.id }).then((res) => {
        if (res.success) {
          this.softInfo = res.result
          this.screenshots = this.softInfo.softwareScreenshot.split(",")
          if(this.softInfo.softwareScreenshot && this.screenshots.length){
            this.$nextTick(()=>{
              const rect = this.$refs.screenList.getBoundingClientRect()
              this.shotHeight = rect&&rect.width?(rect.width / 3 - 8) / 16 * 9:0
              console.log("矩形的宽高 === ", rect.width, this.shotHeight)
            })
          }
        }
      })
    },
    getGo() {
      this.$emit('getGo')
    },
    initDictData() {
      //根据字典Code, 初始化字典数组
      ajaxGetDictItems('cpuArch', null).then((res) => {
        if (res.success) {
          this.cpuList = res.result
        }
      })
      ajaxGetDictItems('os_type', null).then((res) => {
        if (res.success) {
          this.dictOptions = res.result
        }
      })
      ajaxGetDictItems('app_classify', null).then((res) => {
        if (res.success) {
          this.types = res.result
        }
      })

    },
  }
}
</script>


<style scoped lang='less'>
.soft-info {
  .soft-info-header {
    position: relative;
    display: flex;
    .logo-box{
      width: 92px;
      height: 92px;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      margin-left: 11px;
    }
    .logo {
      height: 92px;
      border-radius: 16px;
    }

    .info-con {
      margin-left: 32px;
    }

    .soft-name {
      font-family: PingFangSC-Medium;
      font-size: 28px;
      font-weight: 500;
      color: #000000;
    }

    .soft-summary {
      margin-top:3px;
      font-family: PingFangSC-Regular;
      font-size: 16px;
      color: rgba(0,0,0,0.65);
      letter-spacing: 0.57px;
      font-weight: 400;
    }

    .soft-load {
      margin-top:18px;
      font-family: PingFangSC-Regular;
      font-size: 14px;
      color: rgba(0,0,0,0.45);
      letter-spacing: 0.5px;
      font-weight: 400;
    }

    .goback {
      position: absolute;
      right: 0;
      top: 0;
      width: 20px;
      height: 20px;
      cursor: pointer
    }
  }

  .info-block {
    margin-top: 24px;
    display: flex;
    align-items: center;
    justify-content: space-around;
    border-bottom: 1px solid  #E8E8E8;
    padding-bottom: 24px;
    .info-item {
      display: flex;
      align-items: center;
      flex-direction: column;
      position: relative;
      width: 20%;
      .s-title {
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: rgba(0,0,0,0.45);
        letter-spacing: 0.5px;
        font-weight: 400;
      }

      .s-value {
        margin-top:21px;
        font-family: PingFangSC-Medium;
        font-size: 22px;
        color: rgba(0,0,0,0.65);
        letter-spacing: 0.79px;
        font-weight: bold
      }
      .s-line{
        position:absolute;
        width:2px;
        height:100%;
        background:#E8E8E8;
        right:0;
      }
    }
  }

  .screen-list {
    display: flex;
    margin-top: 8px;
    img{
      width: calc((100% / 3) - 30px);
      max-width: 438px;
    }
    img:nth-child(1){
      margin-right: 50px;
    }
    img:nth-child(2){
      margin-right: 50px;
    }
  }
  .software-describe{
    margin-top: 12px;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: rgba(0,0,0,0.65);
    letter-spacing: 0.5px;
    font-weight: 400;
  }
}

.colorBox {
  margin-bottom: 10px;
}

.colorTotal {
  padding-left: 7px;
  border-left: 4px solid #1e3674;
  font-size: 14px;
  font-weight: bold;
}
</style>