<template>
  <j-modal :title="title" :width="width" :centered='true' :visible="visible" :destroyOnClose="true" switchFullscreen
    @ok="handleOk" :okButtonProps="{ class:{'jee-hidden': disableSubmit} }" @cancel="handleCancel" cancelText="关闭">
    <add-asets ref="addAsets" @ok="submitCallback"></add-asets>
  </j-modal>

</template>

<script>
  import AddAsets from './AddAsets'

  import AFormItem from "ant-design-vue/es/form/FormItem";
  import {
    httpAction,
    getAction
  } from '@/api/manage'
  import ARow from "ant-design-vue/es/grid/Row";
  import AssetsInfoModal from "../../../devicesystem/modules/AssetsInfoModal";
  export default {
    name: 'addAssetsModal',
    components: {
      AssetsInfoModal,
      ARow,
      AFormItem,

      AddAsets
    },
    data() {
      return {
        title: '',
        width: 800,
        visible: false,
        disableSubmit: false,
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 2
          }
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 14
          }
        },
        url: {
          add: '/assets/assets/addField',
        }
      }
    },
    methods: {
      show(assetsId, data) {
        this.visible = true
        this.$nextTick(() => {
          this.$refs.addAsets.show(assetsId, data)
        })
      },
      close() {
        this.$emit('close');
        this.visible = false;
      },
      handleOk() {
        this.visible = true
        this.$nextTick(() => {
          this.$refs.addAsets.submitForm();
          // this.close();
        })
      },
      submitCallback() {
        this.$emit('ok');
        this.visible = false;
      },
      handleCancel() {
        this.close()
      }
    }
  }
</script>
<style scoped lang='less'>
  @import '~@assets/less/normalModal.less';
</style>