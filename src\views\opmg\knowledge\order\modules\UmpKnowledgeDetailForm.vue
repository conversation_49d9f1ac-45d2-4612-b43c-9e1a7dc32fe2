<template>
 <a-card>
     <a-col :span="24" style="margin-bottom: 5px">
        <span style="margin-left:10px;color: #409eff;cursor: pointer" @click="handleDetailEdit(data)">
          <a-icon type="edit" style="margin-right: 6px;"/>编辑
        </span>
       <span style="float: right;margin-bottom: 12px;" ><img src="~@/assets/return1.png" alt="" @click="getGo" style="width: 20px;height: 20px;cursor: pointer"></span>
     </a-col>

    <table class="gridtable">
      <tr>
        <td class="leftTd" style="font-size:14px">类 型</td> <td class="rightTd" style="font-size:14px">{{data.orderCategoryText}} </td>
        <td class="leftTd" style="font-size:14px">标 题</td> <td  class="rightTd" style="font-size:14px">{{data.title}}</td>
      </tr>

      <tr>
        <td class="leftTd" style="font-size:14px"> 描 述</td> <td class="rightTd" colspan="3" style="font-size:14px">{{data.description}} </td>
      </tr>
      <tr>
        <td class="leftTd" style="font-size:14px"> 解决方案</td> <td class="rightTd" colspan="3" style="font-size:14px">{{data.plan}} </td>
      </tr>

     <tr>
       <td class="leftTd" style="font-size:14px"> 附 件</td>
       <td class="rightTd" colspan="3" style="padding-bottom: 0;font-size:14px">
          <div v-for="(item,index) in files" style="float: left;margin-left: 10px;">
            <div v-if="item.suffix == 'png' || item.suffix == 'jpg' || item.suffix == 'jpeg' || item.suffix == 'gif' || item.suffix == 'ico' || item.suffix == 'bmp' || item.suffix == 'pic' || item.suffix == 'tif'">
              <div class="orientation" style="float: none;">
                <img :src=urla+item.fileUrl alt="" id="urla" >
                <span class="font" @click="fontClick(item.fileUrl)">下载文件</span>
              </div>
            </div>
            <div v-else-if="item.suffix == 'pdf'">
              <div class="orientationFile" style="float: none;">
                <img src="@/assets/img/pdf.png" alt="" id="urlas">
                <span class="font1" @click="fontClick(item.fileUrl)">下载文件</span>
              </div>
            </div>
            <div v-else-if="item.suffix == 'doc'" >
              <div class="orientationFile">
                <img src="@/assets/img/doc.png" alt="" id="urlas">
                <span class="font1" @click="fontClick(item.fileUrl)">下载文件</span>
              </div>
            </div>
            <div v-else-if="item.suffix == 'docx'">
              <div class="orientationFile">
                <img src="@/assets/img/docx.png" alt="" id="urlas">
                <span class="font1" @click="fontClick(item.fileUrl)">下载文件</span>
              </div>
            </div>
            <div v-else-if="item.suffix == 'txt'">
              <div class="orientationFile">
                <img src="@/assets/img/txt.png" alt="" id="urlas">
                <span class="font1" @click="fontClick(item.fileUrl)">下载文件</span>
              </div>
            </div>
            <div v-else>
              <a @click="fontClick(item.fileUrl)" style ="color: #40a9ff;">{{item.fileName}}</a>
            </div>
          </div>
       </td>
      </tr>
    </table>
   <ump-knowledge-update-modal ref="modalForm" @ok="modalFormOk"></ump-knowledge-update-modal>
  </a-card>
</template>

<script>
  import UmpKnowledgeUpdateModal from './UmpKnowledgeUpdateModal'
  import { httpAction, getAction } from '@/api/manage'
  import pick from 'lodash.pick'
  import { validateDuplicateValue } from '@/utils/util'
  import JFormContainer from '@/components/jeecg/JFormContainer'
  import JDictSelectTag from "@/components/dict/JDictSelectTag"
  import JUpload from '@/components/jeecg/JUpload'

  export default {
    name: 'UmpKnowledgeDetailForm',
    components: {
      JFormContainer,
      JDictSelectTag,
      JUpload,
      UmpKnowledgeUpdateModal
    },
    props: {
      //流程表单data
      formData: {
        type: Object,
        default: ()=>{},
        required: false
      },
      //表单模式：true流程表单 false普通表单
      formBpm: {
        type: Boolean,
        default: false,
        required: false
      },
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      },
      data:{
        type:Object
      }
    },
    data () {
      return {
        form: this.$form.createForm(this),
        model: {},
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        defaultActiveKey:"1",/**默认打开的标签 */
        files:[],
        validatorRules: {
        },
        urla:window._CONFIG['downloadUrl']+'/',
        url: {
          add: "/knowledges/add",
          edit: "/knowledges/edit",
          queryById: "/knowledges/queryById",
          queryFileGroup:"/fileinfo/fileInfo/queryByFileGroups"

        },
        /*data:{}*/
      }
    },
    computed: {
      formDisabled(){
        if(this.formBpm===true){
          if(this.formData.disabled===false){
            return false
          }
          return true
        }
        return this.disabled
      },
      showFlowSubmitButton(){
        if(this.formBpm===true){
          if(this.formData.disabled===false){
            return true
          }
        }
        return false
      }
    },
    mounted () {
      //如果是流程中表单，则需要加载流程表单data
    //   this.showFlowData();
      this.getFilesUrl(this.data.fileGroup);
    },
    methods: {
      modalFormOk(){
        let params = {id:this.data.id};
        getAction(this.url.queryById,params).then((res)=>{
          if(res.success){

            this.data = res.result;
          }
        });
        this.getFilesUrl(this.data.fileGroup);
      },
      //详情编辑
      handleDetailEdit: function (record) {

        this.$refs.modalForm.edit(record)
        this.$refs.modalForm.title = '编辑'
        this.$refs.modalForm.disableSubmit = false
      },
      getFilesUrl(fileGroup){
        if('' != fileGroup && null != fileGroup){
            let params = {fileGroup:fileGroup};
            getAction(this.url.queryFileGroup,params).then((res)=>{
            if(res.success){

              this.files=res.result;
            }
          });
        }
      },
      fontClick(path){
        // window.open(window._CONFIG['downloadUrl']+"/"+path);
        window.open(window._CONFIG['domianURL'] + '/sys/common/downloadFile/' + path)
      },
      popupCallback(row){
        this.form.setFieldsValue(pick(row,'type','title','description','plan','recordType'))
      },
      //返回上一级
      getGo(){
        this.$parent.pButton2(0);

      }
    }
  }
</script>
<style scoped>

table.gridtable {
    font-family: verdana,arial,sans-serif;
    font-size:11px;
    color:#606266;
    border-width: 1px;
    border-color: #e8e8e8;
    border-collapse: collapse;
    text-align: left;
    width: 100%;
}
table.gridtable td {
    border-width: 1px;
    border-style: solid;
    border-color: #e8e8e8;
}
.leftTd{
  width: 17%;
  background-color: #FAFAFA;
  padding: 16px 24px;
  text-align: center;
}
.rightTd{
  width: 35%;
  padding: 16px 24px;
}
#urla{
  width: 100px;
  height: 100px;
}
#urlas{
  width: 100px;
  height: 100px;
}
.orientation{
  width: 100%;
  /*margin: 0 auto;*/
  position: relative;
  overflow: hidden;
}
.orientationFile{
  width: 100%;
  /*margin: 0 auto;*/
  position: relative;
  overflow: hidden;
}
.font{
  position: absolute;
  bottom: 0;
  background: rgba(0,0,0,.75);
  left: 0;
  width: 100%;
  height: 30%;
  color: #fff;
  line-height: 32px;
  cursor: pointer;
  transform: translateY(109%);
  transition: all 0.3s ease-out 0s;
  text-align: center;
}
.font1{
  position: absolute;
  bottom: 0;
  background: rgba(0,0,0,.75);
  left: 0;
  width: 100%;
  height: 30%;
  color: #fff;
  line-height: 32px;
  cursor: pointer;
  transform: translateY(109%);
  transition: all 0.3s ease-out 0s;
  text-align: center;
}
.orientation:hover .font{
  transform: translateY(0%);
}
.orientationFile:hover .font1{
  transform: translateY(0%);
 }


</style>
