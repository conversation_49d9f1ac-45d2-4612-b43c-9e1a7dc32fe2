<template>
  <div style="height: 100%; margin-bottom: 16px;overflow: hidden;overflow-y: auto !important;">
    <a-card >
      <a-row>
        <a-col :span="24">
          <div style="display: flex; justify-content: space-between; margin-bottom: 16px">
            <div style="margin-left: 10px; color: #409eff; cursor: pointer" @click="handleEdit(data)">
              <a-icon type="edit" style="margin-right: 6px" />编辑
            </div>
            <div>
              <img src="~@/assets/return1.png" alt @click="getGo" style="width: 20px; height: 20px; cursor: pointer" />
            </div>
          </div>
        </a-col>
      </a-row>
      <table class="gridtable">
        <tr>
          <td class="leftTd">地区</td>
          <td class="rightTd">{{ data.regionName }}</td>
          <td class="leftTd">单位</td>
          <td class="rightTd">{{ data.company }}</td>
        </tr>
        <tr>
          <td class="leftTd">日期</td> <td class="rightTd">{{ data.time }}</td>
          <td class="leftTd">类型</td> <td class="rightTd">{{ data.assetsName }}</td>
        </tr>
        <tr>
          <td class="leftTd">采购数</td>
          <td class="rightTd">{{ data.purchaseNum }}</td>
          <td class="leftTd">下发数</td>
          <td class="rightTd">{{ data.downNum }}</td>
        </tr>
        <tr>
          <td class="leftTd">部署数</td>
          <td class="rightTd">{{ data.deployNum }}</td>
          <td class="leftTd">上线数</td>
          <td class="rightTd">{{ data.onlineNum }}</td>
        </tr>
        <tr>
          <td class="leftTd">替换数</td>
          <td class="rightTd">{{ data.replaceNum }}</td>
          <td class="leftTd">暂存数</td>
          <td class="rightTd">{{ data.depositNum }}</td>
        </tr>
        <tr>
          <td class="leftTd">转移数</td>
          <td class="rightTd">{{ data.transferNum }}</td>
          <td class="leftTd">销毁数</td>
          <td class="rightTd">{{ data.destructionNum }}</td>
        </tr>
      </table>
      <MonthlyMagazineEdit ref="modalForm" :assetType="AssetType" @ok="query"></MonthlyMagazineEdit>
    </a-card>
  </div>
</template>

<script>
import pick from 'lodash.pick'
import moment from 'moment'
import { ledgerMonthAdd, ledgerMonthEdit } from '@api/AssetsManagement'
import JDictSelectTag from '@/components/dict/JDictSelectTag.vue'
import YqAreaCascaderSelect from '@/components/areaDict/YqAreaCascaderSelect'
import MonthlyMagazineEdit from './MonthlyMagazineEdit'
import { getAction } from '../../../../api/manage'
import { ajaxGetDictItems } from '@api/api'
export default {
  name: 'MonthlyMagazineDetails',
  props: {
    data: {
      type: Object,
    },
  },
  components: {
    JDictSelectTag,
    YqAreaCascaderSelect,
    MonthlyMagazineEdit,
  },
  data() {
    return {
      //禁止编辑
      disabledEdit: false,
      //只读
      readOnly: false,
      AssetType: '',
      assets: '',
      validatorRules: {
        assets: {
          rules: [{ required: true, message: '请选择巡检类型' }],
        },
      },
    }
  },
  mounted() {
    this.getQueryAssetType()
  },
  methods: {
    //返回上一级
    getGo() {
      this.$parent.pButton2(0)
    },
    query(e) {
      if (e == null) {
        getAction('/ledger/ledgerMonth/queryById', { id: this.data.id }).then((res) => {
          if (res.code == 200) {
            this.data = res.result
          }
        })
      }else{
        this.data = e
      }
    },
    handleEdit(record) {
      this.$refs.modalForm.edit(record)
      this.$refs.modalForm.title = '修改月报'
      this.$refs.modalForm.disableSubmit = false
    },
    getQueryAssetType() {
      let that = this
      ajaxGetDictItems('asset_type', null).then((res) => {
        if (res.success) {
          that.AssetType = res.result
        } else {
          that.$message.error('资产类型字典信息获取失败')
        }
      })
    },
  },
}
</script>
<style scoped lang="less">
table.gridtable {
  font-family: verdana, arial, sans-serif;
  font-size: 14px;
  color: #606266;
  border-width: 1px;
  border-color: #e8e8e8;
  border-collapse: collapse;
  text-align: left;
  width: 100%;
  margin-top: 8px;
}
table.gridtable td {
  border-width: 1px;
  border-style: solid;
  border-color: #e8e8e8;
}
.leftTd {
  width: 17%;
  background-color: #fafafa;
  padding: 16px 24px;
  text-align: center;
}
.rightTd {
  width: 35%;
  padding: 16px 24px;
  word-break: break-word;
}
.colorBox {
  margin-bottom: 18px;
}
.colorTotal {
  padding-left: 7px;
  border-left: 4px solid #1e3674;
}
::v-deep .two-words > div > label {
  letter-spacing: 4px;
}
::v-deep .two-words > div > label::after {
  letter-spacing: 0px;
}
</style>
