<template>
  <a-card :bordered='false'>
    <div class='action'>
      <span class='edit'>
        详情信息
      </span>
      <span class='return'>
        <img src='~@/assets/return1.png' alt='' @click='getGo'>
      </span>
    </div>
    <a-descriptions :column='{ xxl: 2, xl: 2, lg: 2, md: 2, sm: 2, xs: 2 }' bordered>
      <a-descriptions-item label='结果标识' v-if='data.resultFlag'>{{ data.resultFlag }}</a-descriptions-item>
      <a-descriptions-item label='日志类型' v-if='data.logType'>{{ data.logType == 0 ? '推送': '接收' }}</a-descriptions-item>
      <a-descriptions-item label='数据类型' v-if='data.dataType'>{{ getDataType(data.dataType) }}</a-descriptions-item>
      <a-descriptions-item label='操作类型' v-if='data.operateType'>{{ getDataType(data.operateType) }}
      </a-descriptions-item>
      <a-descriptions-item label='下级单位名称' v-if='data.childDepartName'>{{ data.childDepartName }}</a-descriptions-item>
      <a-descriptions-item label='上级单位名称' v-if='data.parentDepartName'>{{ data.parentDepartName }}</a-descriptions-item>
      <a-descriptions-item label='响应内容' v-if='data.responseContent'>{{ data.responseContent }}</a-descriptions-item>
      <a-descriptions-item label='记录生成时间' v-if='data.createTime'>{{data.createTime}}</a-descriptions-item>
      <a-descriptions-item label='数据内容' v-if='data.dataContent'>{{ data.dataContent }}</a-descriptions-item>
    </a-descriptions>
  </a-card>
</template>

<script>
  export default {
    name: 'data',
    data() {
      return {}
    },
    props: {
      data: {
        type: Object,
        required: false,
        default: () => {
          return {}
        }
      }
    },
    watch: {
      data: {
        handler(val) {
          this.data = val
        },
        deep: true,
        immediate: true
      }
    },
    mounted() {
      console.log(this.data, 'data');

    },
    methods: {
      getDataType(data) {
        if (data == 'device') {
          return '设备'
        } else if (data == 'depart') {
          return '单位'
        } else if (data == 'terminal') {
          return '终端'
        } else if (data == 'data') {
          return '监控数据'
        } else if (data == 'I') {
          return '新增'
        } else if (data == 'U') {
          return '编辑'
        } else {
          return '删除'
        }
      },
      //返回上一级
      getGo() {
        this.$parent.pButton2(0)
      }
    }
  }
</script>

<style scoped lang='less'>
  .action {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-flow: row nowrap;
    margin-bottom: 12px;

    .edit {
      margin-left: 10px;

      .icon {
        color: #409eff;
        margin-right: 6px
      }
    }

    .return {
      img {
        width: 20px;
        height: 20px;
        cursor: pointer
      }
    }
  }

  ::v-deep .ant-descriptions-view {
    border-radius: 0px;
  }

  ::v-deep .ant-descriptions-bordered .ant-descriptions-item-label {
    background-color: rgb(250, 250, 250);
    text-align: center;
    width: 17%;
  }

  ::v-deep .ant-descriptions-item-label,
  .ant-descriptions-item-content {
    color: rgb(96, 98, 102) !important;
  }

  ::v-deep .ant-descriptions-bordered .ant-descriptions-item-content {
    word-break: break-word;
    white-space: normal;
  }
</style>