<template>
  <j-modal :title="title" :width="width" :visible="visible" switchFullscreen :destroyOnClose="true" :centered='true'
    @ok="handleOk" @cancel="handleCancel" cancelText="关闭">
    <iP-plan-form ref="realForm" @ok="submitCallback">
    </iP-plan-form>
  </j-modal>
</template>

<script>
  import IPPlanForm from './IPPlanForm'
  export default {
    name: 'IPPlanModal',
    components: {
      IPPlanForm
    },
    data() {
      return {
        title: '',
        width: '800px',
        visible: false,
        disableSubmit: false
      }
    },
    methods: {
      add({}, data) {
        this.visible = true
        this.$nextTick(() => {
          this.$refs.realForm.add({}, data);
        })
      },
      edit(record) {
        this.visible = true
        this.$nextTick(() => {
          this.$refs.realForm.show(record);
        })
      },
      close() {
        this.$emit('close');
        this.visible = false;
      },
      handleOk() {
        this.$refs.realForm.submitForm();
        this.$emit("refresh");
        // this.close();
      },
      submitCallback(e) {
        this.$emit('ok', e);
        this.visible = false;
      },
      handleCancel() {
        this.close()
      }
    }
  }
</script>
<style lang="less" scoped>
  @import '~@assets/less/normalModal.less';
</style>