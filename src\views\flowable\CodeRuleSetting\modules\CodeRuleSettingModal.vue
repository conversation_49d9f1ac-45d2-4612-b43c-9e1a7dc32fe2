<template>
  <j-modal
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    :title="title"
    :visible="visible"
    :width="width"
    cancelText="关闭"
    switchFullscreen
    :maskClosable='false'
    @cancel="handleCancel"
    @ok="handleOk">

    <template slot="footer">
      <a-button type="primary"  @click="generateInstance" style='float: left'> 生成实例 </a-button>
      <a-button key="close" type='default' @click="handleCancel"> 关闭 </a-button>
      <a-button key="back" type="primary" v-show="!disableSubmit" @click="handleOk"> 确认 </a-button>
    </template>
    <code-rule-setting-form v-bind="$attrs" ref="realForm" :disabled="disableSubmit" @ok="submitCallback"></code-rule-setting-form>
  </j-modal>
</template>

<script>

import CodeRuleSettingForm from './CodeRuleSettingForm'

export default {
  name: 'CodeRuleSettingModal',
  components: {
    CodeRuleSettingForm
  },
  data() {
    return {
      title: '',
      width: 800,
      visible: false,
      disableSubmit: false
    }
  },
  methods: {
    add() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.add()
      })
    },
    edit(record) {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.edit(record)
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      this.$refs.realForm.submitForm()
    },
    submitCallback() {
      this.$emit('ok')
      this.visible = false
    },
    handleCancel() {
      this.close()
    },
    generateInstance(){
      this.$refs.realForm.generateInstance()
    }
  }
}
</script>
<style scoped lang='less'>
@import '~@assets/less/YQNormalModal.less';
</style>