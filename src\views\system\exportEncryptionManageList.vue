<template>
  <a-card class='vScroll'>
    <!-- <div>密码规则设置</div> -->
    <j-form-container>
      <a-form :form='form' slot='detail'>
        <a-row class='title-row'>导出加密管理</a-row>
        <a-row>
          <a-col :span='24' style='margin-bottom: 5px'>
            <!-- <span
              style="margin-left:10px;color: #409eff;cursor: pointer"
              @click="handleDetailEdit(data)"
            >
              <a-icon
                type="edit"
                style="margin-right: 6px;"
              />编辑
            </span> -->
            <!-- <span style="float: right;margin-bottom: 12px;"><img
                src="~@/assets/return1.png"
                alt=""
                @click="getGo"
                style="width: 20px;height: 20px;cursor: pointer"
              ></span> -->
          </a-col>
        </a-row>
        <a-row class='second-row'>
          <a-form-item label='选择是否加密' :labelCol='labelCol' :wrapperCol='wrapperCol'>
            <a-radio-group @change='onChange'
                           v-decorator="['isEncry',
                                          {rules: [{ required: true, message: '请选择导出方式' }]},
                                          {initialValue: isRequired}
                                        ]">
              <a-radio :value='true'>导出文件加密</a-radio>
              <a-radio :value='false'>导出文件不加密</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-col :span='24'>
            <a-form-item label='解压旧密码' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <a-input-password
                :disabled='isRequired == 0'
                placeholder='请输入解压旧密码'
                v-decorator="[
                  'oldPwd',
                  {rules: [{ required:isRequired, message: '请输入解压旧密码' }]}
                ]"
              />
            </a-form-item>
          </a-col>
          <a-col :span='24'>
            <a-form-item label='解压新密码' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <a-input-password
                :disabled='isRequired == 0'
                placeholder='请输入解压新密码'
                v-decorator="[
                  'zipPwd',
                  {
                    rules: [
                      { required: isRequired, message: '请输入解压新密码' },
                      { validator: validatorPass, trigger: 'change' }
                      ]
                  },
                  ]"
              />
            </a-form-item>
          </a-col>
          <a-col :span='24'>
            <a-form-item label='确认解压新密码' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <a-input-password
                :disabled='isRequired == 0'
                placeholder='请输入确认解压新密码'
                v-decorator="[
                  'pwd',
                  {
                     rules: [
                       { required: isRequired, message: '请输入确认解压新密码' },
                       { validator: validatorPass2, trigger: 'change' }
                       ]
                  },
                ]"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span='24' style='text-align: center; margin-top: 0.45rem /* 20/80 */'>
            <a-button @click='submitForm' class='submit-btn'>提 交</a-button>
            <a-button v-show='isRequired' @click='doreset' class='reset-btn' style='margin-left: 8px'>清 空</a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-card>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import pick from 'lodash.pick'
import { validateDuplicateValue } from '@/utils/util'
import JFormContainer from '@/components/jeecg/JFormContainer'
import JDictSelectTag from '@/components/dict/JDictSelectTag'
import JCheckbox from '@/components/jeecg/JCheckbox'

export default {
  name: 'exportEncryptionManageList',
  components: {
    JFormContainer,
    JDictSelectTag,
    JCheckbox
  },
  props: {
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  data() {
    return {
      isRequired: true,
      // 表单数据
      ruleForm: {
        zipPwd: '',
        pwd: ''
      },
      form: this.$form.createForm(this),
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 8 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 8 }
      },
      confirmLoading: false,
      url: {
        add: '/umpPwdManage/umpPwdManage/addZip',
        edit: '/umpPwdManage/umpPwdManage/edit',
        list: '/umpPwdManage/umpPwdManage/list2'

      }
    }
  },
  computed: {},
  created() {
    this.loadData()
  },
  methods: {
    // 密码验证
    validatorPass(rule, value, callback) {
      if (!value) {
        callback()
      } else if (
        !/(?=^.{8,20}$)(?=(?:.*?\d){1})(?=.*[a-z])(?=(?:.*?[A-Z]){1})(?=(?:.*?[`·~!@#$%^&*()_+}{|:;'",<.>/?\=\[\]\-\\]){1})(?!.*\s)[0-9a-zA-Z`·~!@#$%^&*()_+}{|:;'",<.>/?\=\[\]\-\\]*$/.test(
          value
        ) ||
        value.length < 8
      ) {
        // } else if (!/^[A-Za-z0-9]+$/.test(value) || value.length < 6) {

        callback(new Error('密码应包含大小写字母、数字和特殊字符（可为!#$%&等），长度为8~20位'))
        // callback(new Error('123123'))
      } else {
        callback()
      }
      this.ruleForm.zipPwd = value
    },
    // 重复密码验证
    validatorPass2(rule, value, callback) {
      if (!value) {
        callback()
      } else {
        if (this.ruleForm.zipPwd != value) {
          callback(new Error('两次确认密码不一致'))
        }
        callback()
      }
      this.ruleForm.pwd = value
    },
    onChange(e) {
      if(e.target.value==false){
        this.doreset()
      }
      this.isRequired = e.target.value
    },
    getFormFieldValue(field) {
      return this.form.getFieldValue(field)
    },
    loadData() {
      getAction(this.url.list).then((res) => {
        if (res.success) {
          this.setForm(res.result || {})
        }
      })
    },
    setForm(record) {
      let complexPwds = []
      if (record.capitalize === 1) {
        complexPwds.push('capitalize')
      }
      if (record.lowercase === 1) {
        complexPwds.push('lowercase')
      }
      if (record.number === 1) {
        complexPwds.push('number')
      }
      if (record.special === 1) {
        complexPwds.push('special')
      }
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.model = { ...this.model, complexPwd: complexPwds.join(',') }
      this.isRequired = this.model.isEncry
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(pick(this.model, 'isEncry', 'oldPwd', 'zipPwd', 'pwd'))
      })
    },
    doreset() {
      this.form.setFieldsValue({ "oldPwd": undefined })
      this.form.setFieldsValue({ "zipPwd": undefined })
      this.form.setFieldsValue({ "pwd": undefined })
      //this.form.resetFields()
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''

          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          const complexPwd = this.getFormFieldValue('complexPwd')
          let formData = Object.assign(this.model, values)
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                //that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              //this.form.resetFields()
              this.doreset()
              that.confirmLoading = false
            })
        }
      })
    },
    popupCallback(row) {
      this.form.setFieldsValue(pick(row, 'isEncry', 'oldPwd', 'zipPwd', 'pwd'))
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/scroll.less';

.ant-card {
  height: 100%;
  border-radius: 3px !important;
}

::v-deep .ant-card-body {
  height: 100%;
  padding: 22px 23px 30px !important;

  > div {
    height: 100%;
    border: 1px solid #dedede;
    border-radius: 4px;
  }
}

.title-row {
  height: 48px;
  background: #f5f5f5;
  border-bottom: 1px solid #dedede;
  padding: 13px 0 13px 24px;
  font-family: PingFangSC-Medium;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.85);
}

.second-row {
  margin-top: 24px;
}

.submit-btn {
  color: #409eff;
  background: #ecf5ff;
  border-color: #b3d8ff;
}

.submit-btn:hover {
  color: #fff !important;
  background-color: #409eff !important;
  border-color: #409eff !important;
}

.reset-btn:hover {
  color: #409eff;
  background: #fff;
  border-color: #b3d8ff;
}

.radioGroup {
  padding-top: 0.375rem /* 30/80 */;
  padding-left: 5.25rem /* 420/80 */;
  padding-bottom: 0.1875rem /* 15/80 */;
}
</style>