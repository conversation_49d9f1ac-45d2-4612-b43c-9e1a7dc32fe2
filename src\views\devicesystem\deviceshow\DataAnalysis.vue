<template>
  <div>
    <a-row :gutter="24" style="margin-right: 1px" v-if="!isEmptyData || onlineInfo.length>0">
      <div class="colorBox" style="margin-left: 12px;">
        <span class="colorTotal">数据分析</span>
      </div>
    </a-row>
    <a-row :gutter="24">
      <div v-if="onlineInfo.length>0">
        <!-- 设备在线情况 -->
        <a-col :xxl='8' :xl="8" :lg="12" :md="12" :sm="24">
          <div class="cc-title border">
            <div class="title">设备在线情况</div>
            <div class="wrapper">
              <online ref="online" :chartData="onlineInfo" :fontSizeObject="fontSizeObject"></online>
            </div>
          </div>
        </a-col>
      </div>
      <div v-if="!isEmptyData">
        <div v-for="(value,key) in list" :key="key">
          <div v-if="Object.keys(value).length !== 0">
            <!-- <div class="colorBox" style="margin-left: 20px;">
              <span class="colorTotal">{{key}}协议</span>
            </div>-->
            <div v-for="(item, index) in value" :key="key + '_' + index">
              <!-- 内存使用率趋势 -->
              <a-col :xxl='8' :xl="8" :lg="12" :md="12" :sm="24" v-if="(index=='memUtilizRate' || index=='memRate') && item.length > 0">
                <div class="cc-title border">
                  <div class="title">
                    内存使用率趋势
                  </div>
                  <div class="wrapper">
                    <mem-usage-rate
                      v-if="index=='memUtilizRate'"
                      ref="memRate"
                      :chartData="{memRate:item, memUsed: value['mem.memUsed']}"
                      :fontSizeObject="fontSizeObject"
                    ></mem-usage-rate>
                    <mem-usage-rate
                      v-if="index=='memRate'"
                      ref="memRate"
                      :chartData="{memRate:item, memUsed: value['memUsed']}"
                      :fontSizeObject="fontSizeObject"
                    ></mem-usage-rate>
                  </div>
                </div>
              </a-col>
              <!-- 磁盘使用情况 -->
              <a-col :xxl='8' :xl="8" :lg="12" :md="12" :sm="24" v-if="index=='diskUsed' && item.length > 0">
                <div class="cc-title border">
                  <div class="title">磁盘使用情况</div>
                  <div class="wrapper">
                    <disk-usage ref="diskUsed" :chartData="{diskUsed:item, diskFree: value['diskFree']}" :fontSizeObject="fontSizeObject"></disk-usage>
                  </div>
                </div>
              </a-col>
              <!-- 功率变化趋势 -->
              <a-col :xxl='8' :xl="8" :lg="12" :md="12" :sm="24" v-if="index=='power' && item.length > 0">
                <div class="cc-title border">
                  <div class="title">功率变化趋势</div>
                  <div class="wrapper">
                    <!-- <line-chart ref="power"></line-chart> -->
                  </div>
                </div>
              </a-col>
              <!-- cpu使用率 -->
              <a-col :xxl='8' :xl="8" :lg="12" :md="12" :sm="24" v-if="index=='cpuRate' && item.length > 0">
                <div class="cc-title border" style="padding-bottom:0">
                  <div class="title" style="position:absolute;top:13px;left:16px;">cpu使用率</div>
                  <div class="wrapper" style="height:100%">
                    <cpu-usage ref="cpuRate" :chartData="item" :fontSizeObject="fontSizeObject"></cpu-usage>
                  </div>
                </div>
              </a-col>
              <!-- cpu温度趋势 -->
              <a-col :xxl='8' :xl="8" :lg="12" :md="12" :sm="24" v-if="index=='cpuTemperature' && item.length > 0">
                <div class="cc-title border">
                  <div class="title">cpu温度趋势</div>
                  <div class="wrapper">
                    <!-- <line-chart ref="cpuTemperature"></line-chart> -->
                  </div>
                </div>
              </a-col>
              <!-- 端口速率趋势 -->
              <a-col :xxl='8' :xl="8" :lg="12" :md="12" :sm="24" v-if="index=='inSpeed' && item.length > 0">
                <div class="cc-title border">
                  <div class="title">端口速率趋势</div>
                  <div class="wrapper">
                    <port-rate-trend :allSpeed="value['allSpeed']" :inSpeed="value['inSpeed']" :outSpeed="value['outSpeed']" :fontSizeObject="fontSizeObject"></port-rate-trend>
                  </div>
                </div>
              </a-col>
            </div>
          </div>
        </div>
      </div>
    </a-row>
  </div>
</template>
<script>
import { getAction } from '@/api/manage'
import memUsageRate from './baseEcharts/memUsageRate.vue'
import diskUsage from './baseEcharts/diskUsage.vue'
import cpuUsage from './baseEcharts/cpuUsage.vue'
import portRateTrend from './baseEcharts/portRateTrend.vue'
import portAllRate from './baseEcharts/portAllRate.vue'
import inSpeed from './baseEcharts/inSpeed.vue'
import online from './baseEcharts/online.vue'

export default {
  name: '',
  components: {
    memUsageRate,
    diskUsage,
    cpuUsage,
    portRateTrend,
    portAllRate,
    inSpeed,
    online
  },
  data() {
    return {
      deviceInfo: {},
      url: {
        getMonitorDataByTime: '/device/deviceInfo/getMonitorDataByTime',
        online: '/device/deviceInfo/switchingMachineStatistics'
      },
      chartData: [],
      memUsed: '',
      list: {},
      onlineInfo: [], // 设备在线情况
      selectedIndex: 0,
      fontSizeObject: {
        legendFontSize: 11,
        xAxisFontSize: 10,
        yAxisFontSize: 10
      },
      isEmptyData: false // 是否图表内容是空的
    }
  },
  watch: {
    deviceInfo(newVal, oldVal) {
      this.init(newVal)
      //现场要求隐藏设备在线情况
      // this.online()
    }
  },
  methods: {
    show(record) {
      this.deviceInfo = record
    },
    changeType(index) {
      this.selectedIndex = index
    },
    // 查询设备在线情况
    online() {
      getAction(this.url.online, {
        deviceCode: this.deviceInfo.deviceCode,
        categoryId: this.deviceInfo.categoryId,
        days: 30
      }).then(res => {
        if (res.success) {
          this.onlineInfo = res.result
        }
      })
    },
    init() {
      getAction(this.url.getMonitorDataByTime, {
        deviceCode: this.deviceInfo.deviceCode,
        productId: this.deviceInfo.productId,
        days: 30
      }).then(res => {
        if (res.success) {
          this.list = res.result
          // 判断是否全部的图表都是空数组
          let res1 =   this.isAllArrayPropertiesEmpty(res.result)
          this.isEmptyData = res1
        }
      })
    },
    isAllArrayPropertiesEmpty(obj) {
      // 遍历对象的每个属性
      for (let key in obj) {
        if (obj.hasOwnProperty(key)) {
          const value = obj[key]
          if (typeof value === 'object' && Object.keys(value).length !== 0) {
            for (let key2 in value) {
              if (value.hasOwnProperty(key2) && Array.isArray(value[key2])) {
                if (value[key2].length !== 0) {
                  // 如果找到非空数组，返回false
                  return false
                }
              }
            }
          }
        }
      }
      // 如果没有找到非空元素，返回true
      return true
    }
  }
}
</script>
<style scoped lang="less">
.colorBox {
  margin-bottom: 10px;
  .colorTotal {
    padding-left: 7px;
    border-left: 4px solid #1e3674;
  }
}
.cc-title {
  height: 289px;
  margin-bottom: 15px;
  background: #ffffff;
  border: 1px solid #f5f5f5;
  box-shadow: 3px 3px 9px -1px rgba(0, 0, 0, 0.2);
  .title {
    color: rgba(0,0,0,0.65);
    font-size: 14px;
    display: flex;
    align-items: flex-end;
    padding-left: 17px;
  }
  .time {
    display: flex;
    align-items: center;
    .item {
      color: #000;
      background: none;
      margin-left: 5px;
      padding: 0 7px;
      &.active {
        color: #fff;
        background: #409eff;
      }
    }
  }
}
.border {
  padding: 13px 0;
}
.wrapper {
  height: calc(100% - 18px);
}

</style>