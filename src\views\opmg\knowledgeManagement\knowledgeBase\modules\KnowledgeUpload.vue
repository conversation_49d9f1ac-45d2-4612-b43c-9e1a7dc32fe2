<template>
  <div :id="containerId" style="position: relative">

    <a-upload
      name="file"
      :multiple="multiple"
      :accept='accept'
      :action="uploadAction"
      :headers="headers"
      :data="{'biz':bizPath}"
      :fileList="fileList"
      :beforeUpload="beforeUpload"
      @change="handleChange"
      :disabled="disabled"
      :returnUrl="returnUrl"
      :listType="complistType"
      @preview="handlePreview"
      :class="{'uploadty-disabled':disabled}">
      <template>
        <a-button v-if="buttonVisible">
          <a-icon type="upload" />{{ text }}
        </a-button>
      </template>
    </a-upload>
  </div>
</template>

<script>

import Vue from 'vue'
import { ACCESS_TOKEN } from "@/store/mutation-types"
import { getFileAccessHttpUrl } from '@/api/manage';

const FILE_TYPE_ALL = "all"
const FILE_TYPE_IMG = "image"
const FILE_TYPE_TXT = "file"
const uidGenerator=()=>{
  return '-'+parseInt(Math.random()*10000+1,10);
}
const getFileName=(path)=>{
  if(path.lastIndexOf("\\")>=0){
    let reg=new RegExp("\\\\","g");
    path = path.replace(reg,"/");
  }
  return path.substring(path.lastIndexOf("/")+1);
}
export default {
  name: 'JUpload',
  data(){
    return {
      headers:{},
      fileList: [],
      newFileList: [],
      uploadGoOn:true,
      previewVisible: false,
    }
  },
  props:{
    text:{
      type:String,
      required:false,
      default:"点击上传"
    },
    fileType:{
      type:String,
      required:false,
      default:FILE_TYPE_ALL
    },
    /*限制可上传的文件格式（后缀名）*/
    accept:{
      type:String,
      required:false,
      default:''
    },
    /*这个属性用于控制文件上传的业务路径*/
    bizPath:{
      type:String,
      required:false,
      default:"temp"
    },
    //中文名
    value:{
      type:[String,Array],
      required:false
    },
    //带时间戳的文件路径
    filePaths:{
      type:[String,Array],
      required:false
    },
    // update-begin- --- author:wangshuai ------ date:20190929 ---- for:Jupload组件增加是否能够点击
    disabled:{
      type:Boolean,
      required:false,
      default: false
    },
    // update-end- --- author:wangshuai ------ date:20190929 ---- for:Jupload组件增加是否能够点击
    //此属性被废弃了
    triggerChange:{
      type: Boolean,
      required: false,
      default: false
    },
    multiple: {
      type: Boolean,
      default: true
    },
    /**
     * update -- author:lvdandan -- date:20190219 -- for:Jupload组件增加是否返回url，
     * true：仅返回url
     * false：返回fileName filePath fileSize
     */
    returnUrl:{
      type:Boolean,
      required:false,
      default: true
    },
    number:{
      type:Number,
      required:false,
      default: 0
    },
    buttonVisible:{
      type:Boolean,
      required:false,
      default: true
    },
    uploadAction:{
      type:String,
      required:false,
      default: window._CONFIG['domianURL']+"/sys/common/upload",
    }
  },
  watch:{
    value:{
      immediate: true,
      handler() {
        let val = this.value
        let path=this.filePaths&&this.filePaths.length>0?this.filePaths:''
        if (val instanceof Array) {
          path=path&&path.length>0?path:[]
          if(this.returnUrl){
            this.initFileList(path.join(','), val.join(','))
          }else{
            this.initFileListArr(path, val);
          }
        } else {
          this.initFileList(path,val)
        }
      }
    }
  },
  computed:{
    isImageComp(){
      return this.fileType === FILE_TYPE_IMG
    },
    complistType(){
      return this.fileType === FILE_TYPE_IMG?'picture-card':'text'
    }
  },
  created(){
    const token = Vue.ls.get(ACCESS_TOKEN);
    this.headers = {"X-Access-Token":token};
    this.containerId = 'container-ty-'+new Date().getTime();
  },

  methods:{
    initFileListArr(filePaths,val){
      if(!val || val.length==0){
        this.fileList = [];
        return;
      }
      let fileList = [];
      for(var a=0;a<val.length;a++){
        let url = getFileAccessHttpUrl(val[a].filePath);
        fileList.push({
          uid:uidGenerator(),
          name:val[a].fileName,
          status: 'done',
          url: url,
          response:{
            status:"history",
            message:val[a].filePath
          }
        })
      }
      this.fileList = fileList
    },
    initFileList(paths,fileNames){
      if(!paths || paths.length==0){
        //return [];
        // update-begin- --- author:os_chengtgen ------ date:20190729 ---- for:issues:326,Jupload组件初始化bug
        this.fileList = [];
        return;
        // update-end- --- author:os_chengtgen ------ date:20190729 ---- for:issues:326,Jupload组件初始化bug
      }
      let fileList = [];
      let arr = paths.split(",")
      for(var a=0;a<arr.length;a++){
        let url = getFileAccessHttpUrl(arr[a]);
        let fileName=fileNames&&fileNames.length>0?fileNames.split(',')[a]:''
        let obj={
          uid:uidGenerator(),
          name:fileName?fileName:getFileName(arr[a]),
          status: 'done',
          url: url,
          response:{
            status:"history",
            message:arr[a],
            result:fileName
          }
        }
        fileList.push(obj)
      }
      this.fileList = fileList
    },
    handlePathChange(){
      let uploadFiles = this.fileList
      let path = ''
      let realPaths=''
      if(!uploadFiles || uploadFiles.length==0){
        path = ''
        realPaths=''
      }
      let arr = [];
      let realPathsArr=[]
      for(var a=0;a<uploadFiles.length;a++){
        // update-begin-author:lvdandan date:20200603 for:【TESTA-514】【开源issue】多个文件同时上传时，控制台报错
        if(uploadFiles[a].status === 'done') {
          if(uploadFiles[a].response.result&&uploadFiles[a].response.result.length>0){
            arr.push(uploadFiles[a].response.result)
            realPathsArr.push(uploadFiles[a].response.message)
          }else {
            arr.push(uploadFiles[a].response.message)
          }
        }else{
          return;
        }
        // update-end-author:lvdandan date:20200603 for:【TESTA-514】【开源issue】多个文件同时上传时，控制台报错
      }
      if(arr.length>0) {
        if (realPathsArr.length>0){
          path =arr.join(",")
          realPaths=realPathsArr.join(",")
        }else{
          path = arr.join(",")
        }
      }
      // this.$emit('update:changeRealPaths', realPaths);
      this.$emit('change', path,realPaths);
    },
    beforeUpload(file) {
      this.uploadGoOn = true
      var fileType = file.type;
      if (this.fileType === FILE_TYPE_IMG) {
        if (fileType.indexOf('image') < 0) {
          this.$message.warning('请上传图片');
          this.uploadGoOn = false
          return false;
        }
      }
      //TODO 扩展功能验证文件大小
      return true
    },
    handleChange(info) {
      if (!info.file.status && this.uploadGoOn === false) {
        info.fileList.pop();
      }
      let fileList = info.fileList
      if (info.file.status === 'done') {
        if (this.number > 0) {
          fileList = fileList.slice(-this.number);
        }
        if (info.file.response.success) {
          fileList = fileList.map((file) => {
            if (file.response) {
              let reUrl = file.response.message;
              file.url = getFileAccessHttpUrl(reUrl);
            }
            return file;
          });
        }
        //this.$message.success(`${info.file.name} 上传成功!`);
      } else if (info.file.status === 'error') {
        this.$message.error(`${info.file.name} 上传失败.`);
      } else if (info.file.status === 'removed') {
        this.handleDelete(info.file)
      }
      this.fileList = fileList
      if (info.file.status === 'done' || info.file.status === 'removed') {
        //returnUrl为true时仅返回文件路径
        if (this.returnUrl) {
          this.handlePathChange()
        } else {
          //returnUrl为false时返回文件名称、文件路径及文件大小
          this.newFileList = [];
          for (var a = 0; a < fileList.length; a++) {
            // update-begin-author:lvdandan date:20200603 for:【TESTA-514】【开源issue】多个文件同时上传时，控制台报错
            if (fileList[a].status === 'done') {
              var fileJson = {
                fileName: fileList[a].name,
                filePath: fileList[a].response.message,
                fileSize: fileList[a].size
              };
              if (fileList[a].response.result&&fileList[a].response.result.length>0){
                fileJson['fileRealName']= fileList[a].response.result
              }
              this.newFileList.push(fileJson);
            } else {
              return;
            }
            // update-end-author:lvdandan date:20200603 for:【TESTA-514】【开源issue】多个文件同时上传时，控制台报错
          }
          this.$emit('change', this.newFileList);
        }
      }
    },
    handleDelete(file) {
      //如有需要新增 删除逻辑
    },
    handlePreview(file) {
      if (this.fileType === FILE_TYPE_IMG) {
        this.previewImage = file.url || file.thumbUrl;
        this.previewVisible = true;
      } else {
        location.href = file.url
      }
    },
    handleCancel() {
      this.previewVisible = false;
    },
  },
  mounted() {},
  model: {
    prop: 'value',
    event: 'change'
  }
}
</script>

<style lang='less'>
.uploadty-disabled {
  .ant-upload-list-item {
    .anticon-close {
      display: none;
    }

    .anticon-delete {
      display: none;
    }
  }
}

//---------------------------- begin 图片左右换位置 -------------------------------------
.uploadty-mover-mask {
  background-color: rgba(0, 0, 0, 0.5);
  opacity: .8;
  color: #fff;
  height: 28px;
  line-height: 28px;
}

//---------------------------- end 图片左右换位置 -------------------------------------
</style>