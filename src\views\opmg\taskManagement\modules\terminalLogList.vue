<template>
  <!-- -->
  <div class='terminal-log-info'  v-if="logList&&logList.length>0">
    <div class='colorBox' v-if='showTitle'>
      <span class='colorTotal'>{{ title }}</span>
    </div>

    <div class='steps-box'>
      <!--      <a-steps :current='1' status='error' direction='vertical'>
              <a-step v-for='(item,index) in logList'
                      :status='item.status'
                      :title='item.title'
                      :subTitle='item.subTitle'
              >
                <div class='' slot='description'>
                  <div>
                    {{ item.time }}
                  </div>
                  <div>
                    {{ item.exeSta }}
                  </div>
                  <div>
                    {{ item.log }}
                  </div>
                </div>
              </a-step>
            </a-steps>-->
      <step-bar :steps='logList' :currentStep='currentStep' :column='4'>
        <template #number="{ record,index }">
          <!-- 自定义步骤数字显示 -->
          <span v-if='currentStep==index'>{{index}}</span>
          <a-icon v-else-if='record.status==="error"' type="close" />
          <a-icon v-else-if='record.status==="finish"' type="check" />
          <span v-else>{{index}}</span>
        </template>
        <template #title="{ record,index }">
          <!-- 自定义步骤标题显示 -->
          <span :class="{'err-text':['3','-1'].includes(info.executeStatus)}">{{ record.title }}</span>
        </template>
        <template #content="{ record,index }">
          <!-- 自定义步骤内容 -->
          <div class='step-info' :class="{'err-text':['3','-1'].includes(info.executeStatus)}">{{ record.time }}</div>
          <div v-if='record.exeSta' class='step-info' :class="{'err-text':['3','-1'].includes(info.executeStatus)}">{{ record.exeSta }}</div>
          <div v-if='record.log' class='step-info step-log' :class="{'err-text':['3','-1'].includes(info.executeStatus)}">{{ record.log }}</div>
        </template>
      </step-bar>
    </div>
  </div>
</template>

<script>
import StepBar from '@/components/yq/StepBar.vue'

export default {
  name: 'terminalLogList',
  components: { StepBar },
  data() {
    return {
      stepList: [],
    }
  },
  props: {
    showTitle: {
      type: Boolean,
      required: false,
      default: true
    },
    title: {
      type: String,
      required: false,
      default: '任务执行日志'
    },
    logList: {
      type: Array,
      required: false,
      default: () => {
        return []
      }
    },
    info: {
      type: Object,
      required: false,
      default: () => {
        return {}
      }
    },
  },
  watch: {
    logList: {
      handler(val) {
        this.init(val)
      },
      deep: true,
      immediate: true
    },
    /* info: {
      handler(val) {
       console.log("终端信息变了")
      },
      deep: true,
      immediate: true
    } */
  },
  computed:{
    //正在执行的步骤 根据后台业务字段返回对应的步骤
    // 当前节点目前的判断逻辑：按照总任务的状态判断，总任务状态为1时，当前节点为1，总任务状态为0时，当前节点为-1
    //暂不确定日志顺序是否为倒序
    currentStep(){
      if(this.info.executeStatus === "1"){
        return 1
      }else{
        return -1
      }
     /*  let tem = this.stepList[0]
      if(tem){
        return tem.status?-1:1
      }else{
        return -1
      } */
    }
  },
  methods: {
    init(list) {
      this.stepList = list
    },
    //返回上一级
    getGo() {
      this.$parent.pButton1(this.backActive)
      /*if (this.backActive===2){
        console.log(' this.$parent====', this.$parent)
        this.$parent.pButton1(this.backActive)
      }else if (this.backActive===1){
        console.log('this.$parent====', this.$parent.$parent)
        this.$parent.pButton1(this.backActive)
      }*/
    }
  }
}
</script>

<style scoped lang='less'>
.terminal-log-info {
  .colorBox {
    margin-bottom: 10px;
    margin-top: 20px;

    .colorTotal {
      padding-left: 7px;
      font-size: 14px;
      border-left: 4px solid #1e3674;
      margin-right: 12px;
    }
  }
}
.step-info{
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: rgba(0,0,0,0.65);
  letter-spacing: 0.5px;
  font-weight: 400;
  margin-top: 8px;
}
.step-log{
 max-height: 200px;
 overflow-y: auto;
}
.err-text{
  color: rgba(0,0,0,0.35);
}
.step-log::-webkit-scrollbar {
 display: none;
}
</style>
