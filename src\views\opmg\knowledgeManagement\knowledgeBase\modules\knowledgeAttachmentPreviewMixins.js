import { downFile, getAction } from '@api/manage'
import Vue from 'vue'
export const knowledgeAttachmentPreviewMixins= {
  data() {
    return {
      // sysUserId: this.$store.getters.userInfo.id,
      openFileMenu:true,//控制附件菜单展开/收起
      currFileview: null,
      viewURL: '',
      isLoading: false,
      iframe: null,
      loadingMessage:null,
      iframeParentNode:null,
      unformattedContent:'',
      imgUrl:'',
      url:{
        unformattedText:'/kbase/knowledges/content'
      }
    }
  },
  computed:{
    sysUserId(){return  this.$store.getters.userInfo.id}
  },
  watch: {
    kInfo: {
      immediate: true,
      handler(nVal) {
        this.isLoading = false
        this.currFileview = null
        this.unformattedContent=''
        if (nVal && Object.keys(nVal).length > 0) {
          if (nVal.knowledgeType != 1) {
            return
          } else {
            if (nVal.filesArr && nVal.filesArr.length > 0) {
              nVal.filesArr.map((item, index) => {
                item.isSelected = index !== 0 ? false : true
              })
              this.currFileview = nVal.filesArr[0]
              this.currFileview['index'] = 0
              if (this.kkfileviewUrl&&this.kkfileviewUrl!=='null') {
                this.$nextTick(() => {
                  this.iframeParentNode=this.$refs.iframeParentNode
                  this.createIframe(this.currFileview)
                })
              }else{
                this.getUnformattedText(nVal.filesArr[0])
              }
            } else {
              this.currFileview = null
              this.unformattedContent=''
            }
          }
        }
      }
    }
  },
  mounted() {},
  methods: {
    createIframe(file) {
      this.isLoading=true
      // 创建一个新的空白 iframe
      this.iframe = document.createElement('iframe');
      this.iframe.id = 'myIframe';
      this.iframe.classList.add('iframe')
      this.iframe.style='height: 100%;width: 100%;border: none;background-color: rgba(247, 247, 247, 0.75)'
      this.iframeParentNode.appendChild(this.iframe);
      this.iframe.src=this.getKkFileViewURL(file)
      let that=this
      that.iframe.onload=function(event){
        that.isLoading=false
      }
    },
    clearIframe() {
      this.iframeParentNode.removeChild(this.iframe);
    },
    /*获取无格式文本内容*/
    getUnformattedText(file) {
      let sn = file.suffixName
      this.imgUrl = ''
      if (sn !== '.jpg' && sn !== '.jpeg' && sn !== '.png') {
        this.isLoading = true
        getAction(this.url.unformattedText, {
          id: this.kInfo.id,
          originalFilename: file.fileName + file.suffixName,
          filename: file.filePath
        }).then((res) => {
          if (res.success) {
            this.unformattedContent =this.setUnformattedContent(res.result)
          } else {
            this.unformattedContent = ''
          }
          this.isLoading = false
        }).catch(() => {
          this.unformattedContent = ''
          this.isLoading = false
        })
      } else {
        this.imgUrl = window._CONFIG['staticDomainURL'] + '/' + this.currFileview.filePath
      }
    },
    /*解析无格式文本中的换行符、空格、制表符*/
    setUnformattedContent(str) {
      if (str && str.length > 0) {
        // 通常在 Windows 上和 \n 一起出现, 或者忽略 \r，
        let formattedStr =JSON.stringify(str).replace(/(\\r?\\n|\u2028|\u2029)/g, "\<br>").replace(/\\t/g, "\\ &nbsp;&nbsp;&nbsp;&nbsp;")
         return JSON.parse(formattedStr).replace(/\\/g, "&nbsp;")
      } else {
        return ''
      }
    },
    /*附件下载*/
    downloadFile(obj,isShare=false) {
      let bo=this.approvalView || this.canDownload
      //若是通过知识审批查看知识详情，默认附件均可下载
      //若是分享页面，不考虑知识创建可以下载附件
      let condition=isShare?bo:bo||this.sysUserId === this.kInfo.createByUserId
      if (condition) {
        this.$nextTick(() => {
          this.downloadFileByURL(obj.filePath, obj.fileName + obj.suffixName).then((res) => {
            if (res.success) {
              this.$message.success('文件下载成功')
            }
          })
        })
      } else {
        this.$message.info('知识创建者设置附件不允许下载')
      }
    },
    selectFile(fileObj, index) {
      if (this.currFileview && this.currFileview.filePath != fileObj.filePath) {
        this.currFileview.isSelected = false
        fileObj.isSelected = true
        this.currFileview = fileObj
        this.currFileview['index'] = index
        if (this.kkfileviewUrl && this.kkfileviewUrl !== 'null') {
          this.clearIframe()
          this.createIframe(this.currFileview)
        } else {
          this.getUnformattedText(this.currFileview)
        }
      }
    },
    /*文档知识的附件预览*/
    getKkFileViewURL(currFileview) {
      let url = window._CONFIG['staticDomainURL']+"/"+currFileview.filePath+'?fileId='+currFileview.filePath+'&fullfilename='+currFileview.fileName+currFileview.suffixName
      return this.kkfileviewUrl + '/onlinePreview?url=' + encodeURIComponent(this.utf8ToBase64(url))
    },
    utf8ToBase64(str) {
      const encoder = new TextEncoder();
      const data = encoder.encode(str);
      const base64 = btoa(String.fromCharCode(...new Uint8Array(data)));
      return base64;
    },
    newPageDisplay(currFileview) {
      if (!this.kkfileviewUrl || this.kkfileviewUrl == 'null') {
        if (this.isLoading) {
          this.$message.warning('请稍等，正在加载内容')
          return
        }
        let data = ''
        let newWindow = null
        let sn = this.currFileview.suffixName
        if (sn == '.jpg' || sn == '.jpeg' || sn == '.png') {
          let imgUrl = window._CONFIG['staticDomainURL'] + '/' + this.currFileview.filePath
          data = `
                   <html>
                     <head>
                       <title>${this.currFileview.fileName + this.currFileview.suffixName}</title>
                     </head>
                     <body>
                       <img src='${imgUrl}'/>
                     </body>
                   </html>`;
          newWindow = window.open(data, '_blank')
        } else {
          data = this.unformattedContent ? this.unformattedContent : '该文件未被索引，请下载查看文件内容'
          newWindow = window.open('', '_blank');
        }
        newWindow.document.write(data);
        newWindow.document.close();
      } else {
        let url = this.getKkFileViewURL(currFileview)
        window.open(url)
      }
    },
    /*附件下载*/
    downloadFileByURL(path, fileName, parameter) {
      return new Promise((resolve, reject) => {
        let url = '/sys/common/static/' + path
        downFile(url, parameter).then((data) => {
          if (!data || data.size === 0) {
            Vue.prototype['$message'].warning('文件下载失败')
            reject({ success: false })
            return
          }
          if (typeof window.navigator.msSaveBlob !== 'undefined') {
            window.navigator.msSaveBlob(new Blob([data]), fileName)
            resolve({ success: true })
          } else {
            let url = window.URL.createObjectURL(new Blob([data]))
            let link = document.createElement('a')
            link.style.display = 'none'
            link.href = url
            link.setAttribute('download', fileName)
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link) //下载完成移除元素
            window.URL.revokeObjectURL(url) //释放掉blob对象
            resolve({ success: true })
          }
        })
      })
    },
  }
}