<template>
  <a-row :gutter="10" style="height: 100%">
    <a-col :lg="leftColLg" style="margin-bottom: 20px" class="vScroll">
      <a-card :bordered="false" :body-style="{ paddingBottom: '0' }" class='card-style'>
        <div v-if="rightcolval === 1" style="height: 30px"></div>
        <!-- 查询区域 -->
        <div class="table-page-search-wrapper">
          <!-- 搜索区域 -->
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24">
              <a-col :lg='(rightcolval === 0&&6)||(rightcolval === 1&&10)'
                     :md='(rightcolval === 0&&10)||(rightcolval === 1&&12)' :sm='24'>
                <a-form-item label="角色名称">
                  <a-input placeholder="请输入" :maxLength="maxLength" v-model="queryParam.roleName" :allowClear='true' autocomplete='off' />
                </a-form-item>
              </a-col>
              <a-col :lg='(rightcolval === 0&&6)||(rightcolval === 1&&10)'
                     :md='(rightcolval === 0&&10)||(rightcolval === 1&&12)' :sm='24'>
                <span class="table-page-search-submitButtons">
                  <a-button type="primary" class="btn-search btn-search-style" @click="searchQuery"
                            style="margin-left: 12px">查询</a-button>
                  <a-button class="btn-reset btn-reset-style" @click="searchReset" style="margin-left: 8px">重置
                  </a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered="false">
        <!-- 操作按钮区域 -->
        <div class="table-operator table-operator-style">
          <a-button @click="handleAdd" v-has="'role:add'">新增角色</a-button>
          <a-upload
            name="file"
            :showUploadList="false"
            :multiple="false" :headers="tokenHeader"
            :action="importExcelUrl"
            @change="handleImportExcel"
            v-has="'role:import'">
          </a-upload>
          <a-button @click="handleExportXls('角色管理')">导出</a-button>
        </div>
        <a-table
          ref="table"
          bordered rowKey="id"
          :columns="columns"
          :dataSource="dataSource"
          :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
          :pagination="ipagination"
          :loading="loading"
          :rowSelection="{
           type: 'radio',
           selectedRowKeys: selectedRowKeys,
           onChange: onSelectChange,
           getCheckboxProps: getCheckboxProps}"
          @change="handleTableChange">
          <span slot="action" slot-scope="text, record" class="caozuo">
            <a v-if="disabledPerssion(record)==true" class='dontDelete'>用户</a>
            <a v-else @click="handleOpen(record)">用户</a>
            <a-divider type="vertical" />
            <a v-if="disabledPerssion(record)==true" class='dontDelete'>授权</a>
            <a v-else @click="handlePerssion(record)">授权</a>
            <a-divider type="vertical" />
            <a v-if="disabledPerssion(record)==true" class='dontDelete'>编辑</a>
            <a v-else @click="handleEdit(record)">编辑</a>
            <a-popconfirm :disabled="disabledPerssion(record)==true" title="确定删除吗?" @confirm="() => handleDelete(record.id)" v-has="'role:del'">
              <a-divider type="vertical" />
              <a v-if="disabledPerssion(record)==true" class='dontDelete'>删除</a>
              <a v-else>删除</a>
            </a-popconfirm>
          </span>
        </a-table>
        <!-- 右侧的角色权限配置 -->
        <user-role-modal ref="modalUserRole"></user-role-modal>
      </a-card>
    </a-col>
    <a-col
      :lg="rightColLg"
      v-if="rightcolval === 1"
      style="height: 100%; margin-bottom: 20px"
      class="vScroll">
      <a-card :bordered="false" :body-style="{ paddingBottom: '0' }" class='card-style'>
        <div style="text-align: right; height: 30px">
          <a-icon type="close-circle" @click="hideUserList" />
        </div>
        <!-- 查询区域 -->
        <div class="table-page-search-wrapper">
          <a-form layout="inline" v-bind="formItemLayout">
            <a-row :gutter="24">
              <a-col :lg='rightcolval === 1&&10' :md='rightcolval === 1&&12' :sm='24'>
                <a-form-item label="用户账号">
                  <a-input placeholder="请输入" v-model="queryParam2.username" :maxLength="maxLength" :allowClear='true'></a-input>
                </a-form-item>
              </a-col>
              <a-col :lg='rightcolval === 1&&10' :md='rightcolval === 1&&12' :sm='24'>
                <span class="table-page-search-submitButtons" style="float: left; overflow: hidden">
                  <a-button type="primary" class="btn-search btn-search-style" @click="searchQuery2"
                            style="margin-left: 12px">查询</a-button>
                  <a-button class="btn-reset btn-reset-style" @click="searchReset2" style="margin-left: 8px">重置
                  </a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered="false">
        <!-- 操作按钮区域 -->
        <div class="table-operator table-operator-style">
          <a-button @click="handleAdd2" v-has="'roleUser:add'">新增用户</a-button>
          <a-button @click="handleAddUserRole">已有用户</a-button>
          <a-dropdown v-if="selectedRowKeys2.length > 0">
            <a-menu slot="overlay">
              <a-menu-item key="1" @click="batchDel2">
                <a-icon type="delete" />
                移除
              </a-menu-item>
            </a-menu>
            <a-button>
              批量操作
              <a-icon type="down" />
            </a-button>
          </a-dropdown>
        </div>
        <!-- table区域-begin -->
        <div>
          <a-table
            ref="table2"
            bordered rowKey="id"
            :columns="columns2"
            :dataSource="dataSource2"
            :scroll="dataSource2.length > 0 ? { x: 'max-content' } : {}"
            :pagination="ipagination2" :loading="loading2"
            :rowSelection="{ selectedRowKeys: selectedRowKeys2, onChange: onSelectChange2 ,getCheckboxProps:getCheckboxProps2}"
            @change="handleTableChange2">
            <span slot="action" slot-scope="text, record" class="caozuo">
              <a-popconfirm :disabled='record.choose'  title="确定移除吗?" @confirm="() => handleDelete2(record.id)">
                <a :class="{'dontDelete':record.choose}">移除</a>
              </a-popconfirm>
            </span>
          </a-table>
        </div>
      </a-card>
    </a-col>
    <!-- 表单区域 -->
    <role-modal ref="modalForm" @ok="modalFormOk"></role-modal>
    <user-modal ref="modalForm2" @ok="modalFormOk2"></user-modal>
    <Select-User-Modal ref="selectUserModal" @selectFinished="selectOK"></Select-User-Modal>
  </a-row>
</template>
<script>
import {
  JeecgListMixin
} from '@/mixins/JeecgListMixin'
import {deleteAction, postAction,getAction} from '@/api/manage'
import SelectUserModal from './modules/SelectUserModal'
import RoleModal from './modules/RoleModal'
import UserModal from './modules/UserModal'
import {filterObj} from '@/utils/util'
import UserRoleModal from './modules/UserRoleModal'
import moment from 'moment'

export default {
  name: 'RoleUserList',
  mixins: [JeecgListMixin],
  components: {
    UserRoleModal,
    SelectUserModal,
    RoleModal,
    UserModal,
    moment,
  },
  data() {
    return {
      maxLength:50,
      formItemLayout: {
        labelCol: {
          style: 'width:80px',
        },
        wrapperCol: {
          style: 'width:calc(100% - 80px)'
        }
      },
      isThreePowers: '',
      threePowers: [],
      model1: {},
      currentRoleId: '',
      queryParam2: {},
      dataSource2: [],
      ipagination2: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '30'],
        showTotal: (total, range) => {
          return range[0] + '-' + range[1] + ' 共' + total + '条'
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0,
      },
      isorter2: {
        column: 'createTime',
        order: 'desc',
      },
      filters2: {},
      loading2: false,
      selectedRowKeys2: [],
      selectionRows2: [],
      rightcolval: 0,
      columns: [
        {
        title: '角色编码',
        align: 'center',
        dataIndex: 'roleCode',
      },
        {
          title: '角色名称',
          align: 'center',
          dataIndex: 'roleName',
        },
        {
          title: '创建日期',
          dataIndex: 'createTime',
          align: 'center',
          sorter: (a, b) => {
            let aTime = new Date(a.createTime).getTime();
            let bTime = new Date(b.createTime).getTime();
            return aTime - bTime;
          },
          customRender: (text) => {
            return moment(text).format('YYYY-MM-DD')
          },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 200,
          scopedSlots: {
            customRender: 'action'
          },
        },
      ],
      columns2: [
        {
        title: '用户账号',
        align: 'center',
        dataIndex: 'username',
      },
        {
          title: '用户名称',
          align: 'center',
          dataIndex: 'realname',
        },
        {
          title: '状态',
          align: 'center',
          dataIndex: 'status_dictText',
        },

        {
          title: '操作',
          dataIndex: 'action',
          scopedSlots: {
            customRender: 'action'
          },
          align: 'center',
          fixed: 'right',
          width: 120,
        },
      ],

      // 高级查询参数
      superQueryParams2: '',
      // 高级查询拼接条件
      superQueryMatchType2: 'and',
      url: {
        list: '/sys/role/list',
        delete: '/sys/role/delete',
        list2: '/sys/user/userRoleList',
        addUserRole: '/sys/user/addSysUserRole',
        delete2: '/sys/user/deleteUserRole',
        deleteBatch2: '/sys/user/deleteUserRoleBatch',
        exportXlsUrl: 'sys/role/exportXls',
        importExcelUrl: 'sys/role/importExcel',
        isThreePowers: '/sys/isThreePowers', //三员是否开启
        gethreePowers: '/sys/user/threePowers', //三员是否开启
      },
    }
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
    leftColLg() {
      return this.selectedRowKeys.length === 0 ? 24 : 12
    },
    rightColLg() {
      return this.selectedRowKeys.length === 0 ? 0 : 12
    }
  },
  created() {
    this.getEnableThreePower()
    this.getThreePowers()
  },
  methods: {
    getCheckboxProps(record) {
      return ({
        props: {
          disabled: true
        }
      })
    },
    handleDelete: function(id) {
      if (!this.url.delete) {
        this.$message.error('请设置url.delete属性!')
        return
      }
      var that = this
      deleteAction(that.url.delete, { id: id }).then((res) => {
        if (res.success) {
          if (that.selectedRowKeys[0] === id) {
            that.currentRoleId = ''
            that.rightcolval = 0
            that.dataSource2 = []
            that.onClearSelected()
          }
          that.loadData()
          that.$message.success(res.message)
        } else {
          that.$message.warning(res.message)
        }
      })
    },
    handleOpen(record) {
      this.rightcolval = 1
      this.onSelectChange([record.id], [record])
      this.currentRoleId = record.id
      this.onClearSelected2()
      this.loadData2()
    },
    onSelectChange2(selectedRowKeys, selectionRows) {
      this.selectedRowKeys2 = selectedRowKeys
      this.selectionRows2 = selectionRows
    },
    /**未处理可选，处理中，已关闭，处理完成的不可选*/
    getCheckboxProps2(record) {
      record['choose'] = false
      if (this.isThreePowers) {
        if (this.threePowers.includes(record.username)) {
          record['choose'] = true
        }
      }
      return ({
        props: {
          disabled: record['choose']
        }
      })
    },
    onClearSelected2() {
      this.selectedRowKeys2 = []
      this.selectionRows2 = []
    },
    getThreePowers() {
      getAction(this.url.gethreePowers).then((res) => {
        if (res.code === 200) {
          this.threePowers = res.result
        } else {
          this.$message.error(res.message)
        }
      })
    },
    isThreePowerUser(record) {
      return this.threePowers.indexOf(record.username) > 0;
    },
    getEnableThreePower() {
      getAction(this.url.isThreePowers).then((res) => {
        if (res.code == 200) {
          this.isThreePowers = res.result
        } else {
          this.$message.error(res.message)
        }
      })
    },
    getQueryParams2() {
      //获取查询条件
      let sqp = {}
      if (this.superQueryParams2) {
        sqp['superQueryParams'] = encodeURI(this.superQueryParams2)
        sqp['superQueryMatchType'] = this.superQueryMatchType2
      }
      var param = Object.assign(sqp, this.queryParam2, this.isorter2, this.filters2)
      param.field = this.getQueryField2()
      param.pageNo = this.ipagination2.current
      param.pageSize = this.ipagination2.pageSize
      return filterObj(param)
    },
    getQueryField2() {
      //TODO 字段权限控制
      var str = 'id,'
      this.columns2.forEach(function(value) {
        str += ',' + value.dataIndex
      })
      return str
    },
    handleAdd2: function() {
      if (this.currentRoleId == '') {
        this.$message.error('请选择一个角色!')
      } else {
        this.$refs.modalForm2.roleDisabled = true
        this.$refs.modalForm2.selectedRole = [this.currentRoleId]
        this.$refs.modalForm2.add()
        this.$refs.modalForm2.title = '新增'
      }
    },
    modalFormOk2() {
      // 新增/修改 成功时，重载列表
      this.loadData2()
    },
    loadData2(arg) {
      if (!this.url.list2) {
        this.$message.error('请设置url.list2属性!')
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination2.current = 1
      }
      if (this.currentRoleId === '') return
      let params = this.getQueryParams2() //查询条件
      params.roleId = this.currentRoleId
      this.loading2 = true
      getAction(this.url.list2, params).then((res) => {
        if (res.success) {
          this.dataSource2 = res.result.records
          this.ipagination2.total = res.result.total
        }
        this.loading2 = false
      })
    },
    handleDelete2: function(id) {
      if (!this.url.delete2) {
        this.$message.error('请设置url.delete2属性!')
        return
      }
      var that = this
      deleteAction(that.url.delete2, {
        roleId: this.currentRoleId,
        userId: id
      }).then((res) => {
        if (res.success) {
          that.$message.success(res.message)
          if (that.selectedRowKeys2.length > 0) {
            that.selectedRowKeys2 = that.selectedRowKeys2.filter((item) => {
              return item !== id
            })
          }
          that.loadData2()
        } else {
          that.$message.warning(res.message)
        }
      })
    },
    batchDel2: function() {
      if (!this.url.deleteBatch2) {
        this.$message.error('请设置url.deleteBatch2属性!')
        return
      }
      if (this.selectedRowKeys2.length <= 0) {
        this.$message.warning('请选择一条记录！')
        return
      } else {
        var ids = ''
        for (var a = 0; a < this.selectedRowKeys2.length; a++) {
          ids += this.selectedRowKeys2[a] + ','
        }
        var that = this
        this.$confirm({
          title: '确认删除',
          content: '是否删除选中数据?',
          onOk: function() {
            deleteAction(that.url.deleteBatch2, {
              roleId: that.currentRoleId,
              userIds: ids
            }).then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.loadData2()
                that.onClearSelected2()
              } else {
                that.$message.warning(res.message)
              }
            })
          },
        })
      }
    },
    selectOK(data) {
      let params = {}
      params.roleId = this.currentRoleId
      params.userIdList = []
      for (var a = 0; a < data.length; a++) {
        params.userIdList.push(data[a])
      }
      postAction(this.url.addUserRole, params).then((res) => {
        if (res.success) {
          this.loadData2()
          this.$message.success(res.message)
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    handleAddUserRole() {
      if (this.currentRoleId == '') {
        this.$message.error('请选择一个角色!')
      } else {
        this.$refs.selectUserModal.visible = true
      }
    },
    searchQuery2() {
      this.loadData2(1)
    },
    searchReset2() {
      this.queryParam2 = {}
      this.loadData2(1)
    },
    handleTableChange2(pagination, filters, sorter) {
      //分页、排序、筛选变化时触发
      //TODO 筛选
      if (Object.keys(sorter).length > 0) {
        this.isorter2.column = sorter.field
        this.isorter2.order = 'ascend' == sorter.order ? 'asc' : 'desc'
      }
      this.ipagination2 = pagination
      this.loadData2()
    },
    hideUserList() {
      this.rightcolval = 0
      this.selectedRowKeys = []
    },
    disabledPerssion(role) {
      let roleCode = this.$store.getters.userInfo.roleCodes
      return this.isThreePowers && roleCode !== 'admin' && this.threePowers.includes(role.roleCode);
    },
    handlePerssion(role) {
      let isOpen = this.disabledPerssion(role)
      if (isOpen) {
        return
      }
      this.$refs.modalUserRole.show(role.id)
    }
  },
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';

.caozuo .dontDelete {
  color: rgba(0, 0, 0, 0.35) !important;
}
</style>