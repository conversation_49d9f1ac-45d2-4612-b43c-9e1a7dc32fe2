<template>
  <div class="event-calender">
    <month-style
      v-if="timeType === 'month'"
      :days="days"
      :currentMonth="currentMonth"
      :currentYear="currentYear"
      :layHeight="layHeight"
      :showDialog="showDialog"
      :showLook="showLook"
      @addNewEvent="addNewEvent"
      @editNewEvent="editNewEvent"
      @deleteNewEvent="deleteNewEvent"
    ></month-style>
    <week-style
      v-else-if="timeType === 'week'"
      :days="days"
      :currentMonth="currentMonth"
      :currentYear="currentYear"
    ></week-style>
    <date-style
      v-else-if="timeType === 'day'"
      :days="days"
      :currentWeek="currentWeek"
    ></date-style>
  </div>
</template>

<script>
import DateStyle from "./DateTableStyle.vue";
import MonthStyle from "./MonthStyle.vue";
import WeekStyle from "./WeekStyle.vue";
import dateFunc from "vue-fullcalendar/src/components/dateFunc";
import {addYear, commonAddMouth} from "@/utils/util";
import {getAction} from "@api/manage";

export default {
  name: "eventCalendar",
  props: {
    year: {
      default: 1970,
    },
    month: {
      default: 1,
    },
    tYear: {
      default: 1970,
    },
    tMonth: {
      default: 1,
    },
    tDay: {
      default: 1,
    },
    showToday: {
      default: true,
    },
    timeType: {
      default: "month",
    },
    asycEventList: {
      default: () => [],
    },
    layHeight: {
      default: 0
    },
    showDialog: false,
    showLook: false,
  },
  data() {
    return {
      currentDay: 1,
      currentMonth: 1,
      currentYear: 1970,
      currentWeek: 1,
      days: [],
      copyAsycList: [],
      monthArrObj: [],
      weekArrObj: [],
      dateArrObj: [],
      festival: [],
    };
  },
  components: {MonthStyle, WeekStyle, DateStyle},
  watch: {
    year() {
      this.getEachCalendar(this.formatDate(this.year, this.month, 1));
    },
    month() {
      this.getEachCalendar(this.formatDate(this.year, this.month, 1));
    },
    timeType() {
      this.getEachCalendar(this.formatDate(this.year, this.month, 1));
    },
    asycEventList() {
      this.getEachCalendar(this.formatDate(this.year, this.month, 1));
    },
    festival() {
      this.getEachCalendar(this.formatDate(this.year, this.month, 1));
    },
  },
  mounted() {
    // 每月第一天作为初始参数
    this.getEachCalendar(this.formatDate(this.year, this.month, 1));
    this.getFestival()
  },
  methods: {
    //删除
    deleteNewEvent(id) {
      this.$emit("deleteNewEvent", id);
    },
    //添加
    addNewEvent(day) {
      this.$emit("addEventShow", day.dateStr);
    },
    //编辑
    editNewEvent(data) {
      this.$emit("editNewShow",data);
    },

    initEventList(weekArr) {
      let week = [];
      weekArr.forEach((n) => {
        week.push(n.dateStr);
      });
      let objList = [];
      let weekObj = {};
      let weekLen = {};
      let weekPoint = [];
      week.forEach((w) => {
        weekObj[w] = [];
        weekLen[w] = 0;
      });
      let allLen = 0;
      this.copyAsycList.forEach((ev, i) => {
        let obj = {
          _day: this.tfDays(ev.startTime, ev.endTime),
          start: ev.startTime,
          end: ev.endTime,
          data: ev,
        };
        let dayWeekIndex = 0;
        let dayEventLen = 0;
        let dayWeekYi = 0;
        let className = ev.className || "";
        // 事件的Start在本周，或者本周第一天（周日）在事件中
        if (weekObj[ev.startTime] || this.inMiddleDay(ev.startTime, ev.endTime, week[0])) {
          if (weekObj[ev.startTime]) {
            dayWeekIndex = week.indexOf(ev.startTime);
            if (dayWeekIndex + this.tfDays(ev.startTime, ev.endTime) <= week.length) {
              dayEventLen = this.tfDays(ev.startTime, ev.endTime);
              weekLen[ev.startTime] = weekLen[ev.startTime] + this.tfDays(ev.startTime, ev.endTime);
              className = className + " is-start is-end";
            } else {
              dayEventLen = week.length - dayWeekIndex;
              weekLen[ev.startTime] = week.length;
              className = className + " is-start";
            }
            // 如果是周的第一天，切在一个事件的中
          } else if (this.inMiddleDay(ev.startTime, ev.endTime, week[0])) {
            if (dayWeekIndex + this.tfDays(week[0], ev.endTime) <= week.length) {
              dayEventLen = this.tfDays(week[0], ev.endTime);
              weekLen[week[0]] =
                weekLen[week[0]] + this.tfDays(week[0], ev.endTime);
              className = className + " is-end";
            } else {
              dayEventLen = week.length - dayWeekIndex;
              weekLen[ev.startTime] = week.length;
              className = className + "";
            }
          }
          // ev._eY用于与上上周位置对齐
          dayWeekYi =
            ev._eY || this.getEventY(dayWeekIndex, dayEventLen, weekPoint).yI;
          obj._eLen = dayEventLen; // event 长度，不能超过一周（7）
          obj._eX = dayWeekIndex; // 当天所在周的标（横坐标X）原点（0,0）
          obj._eY = dayWeekYi; // 纵坐标Y
          obj.className = className; // class名称： is-start is-end
          allLen = allLen + 1;
          objList.push(obj);
          let eventPoint = this.getEventPoint(obj._eX, obj._eY, obj._eLen);
          // let eventPoint = this.getEventY(dayWeekIndex, dayEventLen, weekPoint).pointArr // 新点位放入weekPoint
          weekPoint = weekPoint.concat(eventPoint);
          this.copyAsycList[i]._eY = obj._eY;
        }
      });
      return objList;
    },
    getEventPoint(eX, eY, eLen) {
      let arr = [];
      for (let i = 0; i < eLen; i++) {
        // Y坐标不变，X递增
        arr.push(eX + i + "," + eY);
      }
      return arr;
    },
    // 求Y坐标
    getEventY(weekIndex, len, allPoint, yI = 0) {
      let arr = [];
      for (let i = weekIndex; i < len + weekIndex; i++) {
        arr.push(i + "," + yI);
      }
      let beenY = false;
      arr.forEach((n) => {
        if (allPoint.indexOf(n) !== -1) {
          beenY = true;
        }
      });
      if (beenY) {
        return this.getEventY(weekIndex, len, allPoint, yI + 1);
      } else {
        return {
          yI: yI,
          pointArr: arr,
        };
      }
    },
    changeYearMonth() {
      this.getEachCalendar(this.formatDate(this.year, this.month, 1));
    },
    //获取相差多少个月
    getMouth(date1, date2) {
      // 拆分年月日
      date1 = date1.split('-');
      // 得到月数
      date1 = parseInt(date1[0]) * 12 + parseInt(date1[1]);
      // 拆分年月日
      date2 = date2.split('-');
      // 得到月数
      date2 = parseInt(date2[0]) * 12 + parseInt(date2[1]);
      return date1 - date2;
    },
    //日期增加指定天数
    dateAddDays(dateStr, dayCount) {
      var tempDate = new Date(dateStr.replace(/-/g, "/"));//把日期字符串转换成日期格式
      var resultDate = new Date((tempDate / 1000 + (86400 * dayCount)) * 1000);//增加n天后的日期
      return dateFunc.format(resultDate, 'yyyy-MM-dd');
    },
    // 每个月的日期表
    getEachCalendar(cur) {
      this.days = [];
      this.copyAsycList = this.asycEventList.slice(0);
      var date;
      let dayArrObj = [];
      if (cur) {
        date = new Date(cur);
      } else {
        date = new Date();
      }
      this.currentDay = date.getDate();
      this.currentYear = date.getFullYear();
      this.currentMonth = date.getMonth() + 1;
      this.currentWeek = date.getDay();
      let traceDay = 42 - (this.currentWeek - 1);
      //  如果把周日放在最后一列 setSunTolast = true
      let setSunTolast = true;
      if (this.currentWeek === 0 && setSunTolast) {
        this.currentWeek = 7;
        traceDay = 42 - this.currentWeek;
      }
      var str = this.formatDate(
        this.currentYear,
        this.currentMonth,
        this.currentDay
      );
      let dayArr = [];
      // 第一天以前的数据（上个月的数据）
      for (let i = this.currentWeek - 1; i >= 0; i--) {
        let d = new Date(str);
        d.setDate(d.getDate() - i);
        dayArr.push(this.dealDateData(d));
      }
      // 第一天及以后的日期（包括下个月）
      for (let i = 1; i <= traceDay; i++) {
        let d = new Date(str);
        d.setDate(d.getDate() + i);
        dayArr.push(this.dealDateData(d));
      }
      let month = (Array(2).join(0) + this.currentMonth).slice(-2);
      let day = (Array(2).join(0) + this.currentDay).slice(-2);

      let list = this.asycEventList.slice(0);

      for (let i = 0; i < list.length; i++) {
        if (list[i].reminders === "3" && list[i].startTime.substring(0, 7) !== this.currentYear + "-" + month) {
          //判断相差几个月
          let b = this.getMouth(this.currentYear + "-" + month + "-" + day, list[i].startTime);
          if (b <= 0) {
            break;
          }
          //判断每月重复显示
          //开始 结束相差多少天
          let a = this.tfDays(list[i].startTime, list[i].endTime);
          //期间添加月数
          let start = commonAddMouth(list[i].startTime, b, "0");
          let data = {
            startTime: start,
            endTime: this.dateAddDays(start, a - 1),
            planColor: list[i].planColor,
            title: list[i].title,
            id: list[i].id
          }
          this.copyAsycList.push(data)
        } else if (list[i].reminders === "1") {
          //判断每天重复显示
          list[i].endTime = dayArr[dayArr.length - 1].dateStr;
        } else if (list[i].reminders === "2") {
          //判断每周重复显示
          //判断相差几个月
          let b = this.getMouth(this.currentYear + "-" + month + "-" + day, list[i].startTime);
          if (b < 0) {
            break;
          }
          //获取当前时间所在行数
          let a = 0;
          for (let j = 0; j < dayArr.length; j++) {
            if (dayArr[j].dateStr === list[i].startTime) {
              a = (j + 1);
              break;
            }
          }
          //获取开始时间为周几
          var week = new Date(list[i].startTime).getDay();
          //开始 结束相差多少天
          let tfDay = this.tfDays(list[i].startTime, list[i].endTime);
          for (let j = a; j < dayArr.length; j++) {
            var now1 = new Date(dayArr[j].dateStr).getDay();
            if (week === now1) {
              let data = {
                startTime: dayArr[j].dateStr,
                endTime: this.dateAddDays(dayArr[j].dateStr, tfDay - 1),
                planColor: list[i].planColor,
                title: list[i].title,
                id: list[i].id
              }
              this.copyAsycList.push(data)
            }
          }
        } else if (list[i].reminders === "4" && list[i].startTime.substring(0, 4) !== this.currentYear + "") {
          //判断每年重复展示
          //判断相差几年
          let a = this.currentYear - parseInt(list[i].startTime.substring(0, 4))
          if (a <= 0) {
            break;
          }
          let data = {
            startTime: addYear(list[i].startTime, a),
            endTime: addYear(list[i].endTime, a),
            planColor: list[i].planColor,
            title: list[i].title,
            id: list[i].id
          }
          this.copyAsycList.push(data)
        }
      }

      // 把dayArr处理成5组
      let copyDayArr = dayArr.slice(0);
      if (this.timeType === "month") {
        this.dateArrObj = [];
        this.weekArrObj = [];
        for (let i = 0; i < 6; i++) {
          let weekArr = copyDayArr.splice(0, 7);
          dayArrObj.push({
            dayArr: weekArr,
            weekEventList: this.initEventList(weekArr),
            bgMinHeight: this.getBgMinHeight(this.initEventList(weekArr)),
          });
        }
        // this.monthArrObj = JSON.parse(JSON.stringify(dayArrObj))
        this.days = dayArrObj;
      } else if (this.timeType === "week") {
        if (this.showToday) {
          let todyStr = this.formatDate(this.tYear, this.tMonth, this.tDay);
          for (let i = 0; i < 6; i++) {
            let weekArr = copyDayArr.splice(0, 7);
            weekArr.splice(0, 0, {
              dateStr: "时间",
            });
            weekArr.forEach((n) => {
              if (todyStr === n.dateStr) {
                dayArrObj.push({
                  dayArr: weekArr,
                  weekEventList: this.initEventList(weekArr),
                  bgMinHeight: this.getBgMinHeight(this.initEventList(weekArr)),
                });
              }
            });
          }
        } else if (this.weekArrObj.length === 0) {
          let weekArr;
          if (this.dateArrObj.length > 0) {
            for (let i = 0; i < 6; i++) {
              let tem = copyDayArr.splice(0, 7);
              tem.forEach((n) => {
                if (this.dateArrObj[0].dayArr[0].dateStr === n.dateStr) {
                  weekArr = tem;
                }
              });
            }
          } else {
            weekArr = copyDayArr.splice(0, 7);
          }
          dayArrObj.push({
            dayArr: weekArr,
            weekEventList: this.initEventList(weekArr),
            bgMinHeight: this.getBgMinHeight(this.initEventList(weekArr)),
          });
        }
        if (dayArrObj.length > 0) {
          this.weekArrObj = JSON.parse(JSON.stringify(dayArrObj));
          this.days = dayArrObj;
        }
        this.dateArrObj = [];
      } else if (this.timeType === "day") {
        let temDay;
        if (this.showToday) {
          temDay = copyDayArr.filter((el) => {
            return (
              el.date === this.tDay &&
              el.month === this.tMonth &&
              el.year === this.tYear
            );
          });
        } else if (this.dateArrObj.length === 0) {
          if (this.weekArrObj.length > 0) {
            temDay = this.weekArrObj[0].dayArr.slice(0, 1);
          } else {
            temDay = copyDayArr.filter((el) => {
              return (
                el.date === 1 &&
                el.month === this.month &&
                el.year === this.year
              );
            });
          }
        }
        if (temDay) {
          dayArrObj.push({
            dayArr: temDay,
            weekEventList: this.initEventList(temDay),
            bgMinHeight: this.getBgMinHeight(this.initEventList(temDay)),
          });
          this.days = dayArrObj;
          this.dateArrObj = JSON.parse(JSON.stringify(dayArrObj));
          this.resetCurrentDate(temDay[0]);
        }
        this.weekArrObj = [];
      }
      if (this.days.length > 0) {
        this.$emit("checkHaveToday", this.days);
      }
    },
    resetCurrentDate(obj) {
      let d = new Date(obj.dateStr);
      this.currentDay = obj.date;
      this.currentMonth = obj.month;
      this.currentYear = obj.year;
      this.currentWeek = d.getDay();
      if (this.currentWeek === 0) {
        this.currentWeek = 7;
      }
    },
    getTableData() {
      let tableData = []
      for (let i = 0; i < 24; i++) {
        tableData.push({
          id: i,
          time: `${i}时`,
        });
      }
      return tableData
    },
    getBgMinHeight(list) {
      let h = 3;
      list.forEach((n) => {
        h = n._eY + 1 > h ? n._eY + 1 : h;
      });
      return h;
    },
    // 返回 类似 2016-01-02 格式的字符串
    formatDate(year, month, day) {
      var y = year;
      var m = month;
      if (m < 10) m = "0" + m;
      var d = day;
      if (d < 10) d = "0" + d;
      return y + "-" + m + "-" + d;
    },
    dealDateData(d) {
      let item = {};
      item.date = d.getDate();
      item.year = d.getFullYear();
      item.month = d.getMonth() + 1;
      item.dateStr = this.formatDate(item.year, item.month, item.date);
      item.eventList = [];
      // 异步数据
      this.copyAsycList.forEach((n) => {
        let col = Object.assign({row: 0}, n);
        if (n.start === item.dateStr) {
          col.tfDays = this.tfDays(n.start, n.end);
          col.row = col.row + col.tfDays;
          item.eventList.push(col);
        }
      });
      this.festival.forEach(o=>{
        if (o.date===item.dateStr){
          item.holiday=o.holiday?'0':'1'
          item.name=o.name
        }
      });
      return item;
    },
    // 时间差
    tfDays(start, end) {
      let sTime = new Date(start).getTime();
      let eTime = new Date(end).getTime();
      let day = (eTime - sTime) / (3600 * 1000 * 24) + 1;
      return day;
    },
    inMiddleDay(start, end, middle) {
      let isMiddle = false;
      let sTime = new Date(start).getTime();
      let eTime = new Date(end).getTime();
      let mTime = new Date(middle).getTime();
      if (eTime > mTime && mTime > sTime) {
        isMiddle = true;
      }
      return isMiddle;
    },
    isOtherMonth(item) {
      // let d = new Date()
      if (item.month === this.currentMonth && item.year === this.currentYear) {
        return false;
      } else {
        return true;
      }
    },
    isToday(item) {
      let d = new Date();
      if (
        item.month === d.getMonth() + 1 &&
        item.date === d.getDate() &&
        item.year === d.getFullYear()
      ) {
        return true;
      } else {
        return false;
      }
    },
    getFestival() {
      getAction("/festival/custom/queryFestival").then((res) => {
        if (res.success) {
          this.festival = res.result;
        } else {
          this.$message.warning(res.message);
        }
      })
    },
  },
};
</script>
<style lang="less" scoped>
.event-calender {
  width: 100%;
  height: 100%;
  margin: 0 auto;
}
</style>

