<template>
  <!-- , width: fixedHeader ? `calc(100% - ${sidebarOpened ? 256 : 80}px)` : '100%'  -->
  <a-layout-header
    v-if='!headerBarFixed'
    :class="[
      fixedHeader && 'ant-header-fixedHeader',
      sidebarOpened ? 'ant-header-side-opened' : 'ant-header-side-closed',
    ]"
    :style="{ padding: '0' }"
  >
    <div v-if="mode === 'sidemenu'" class='header' :class='theme'>
      <a-icon
        v-if="device === 'mobile'"
        class='trigger'
        :type="collapsed ? 'menu-fold' : 'menu-unfold'"
        @click='toggle'
        style='color: #666666'
      ></a-icon>
      <a-icon
        v-else
        class='trigger'
        :type="collapsed ? 'menu-unfold' : 'menu-fold'"
        @click='toggle'
        style='color: #666666'
      />

      <div v-if="device === 'desktop'" style='display: inline-block' class='breadStyle'>
        <a-breadcrumb style='font-size: 14px; font-weight: 700'>
          <a-breadcrumb-item v-if='curPlatformObject && !oneClickHelp'>
            <span v-if="systemType === '0'">{{curPlatformObject.text}}</span>
            <gateway-menus v-else-if="[2].includes(Number(systemType))  && !oneClickHelp" @resetMenus="$emit('resetMenus')" @platformChange="initPlatformTypeDictData" :menusName="curPlatformObject.text"></gateway-menus>
            <span v-else>{{platformType}}</span>
          </a-breadcrumb-item>
          <a-breadcrumb-item
            v-if="
              pageList[0].meta.title != '工作台' &&
              pageList[0].meta.title != '表单设计器' &&
              pageList[0].meta.title != '服务中心' &&
              pageList[0].meta.title != '个人中心' &&
              pageList[0].meta.title != '应用系统监控' &&
              pageList[0].meta.title != '产品设置' &&
              pageList[0].meta.title != '指标考核'&&parentPath
            "
            style='color: rgba(0, 0, 0, 0.65)'
          >{{ parentPath }}
          </a-breadcrumb-item
          >
          <a-breadcrumb-item style='color: rgba(0, 0, 0, 0.85)'>{{ pageList[0].meta.title }}</a-breadcrumb-item>
        </a-breadcrumb>
      </div>

      <user-menu
      :theme='theme'
      :oneClickHelp="oneClickHelp"
      @toMyAnnouncement='toMyAnnouncement'
      @resetMenus="$emit('resetMenus')"
      />
    </div>
    <!-- 顶部导航栏模式 -->
    <div v-else :class="['top-nav-header-index', theme]">
      <div class='header-index-wide'>
        <div class='header-index-left' :style='topMenuStyle.headerIndexLeft'>
          <logo class='top-nav-header' :show-title="device !== 'mobile'" :style='topMenuStyle.topNavHeader' @resetMenus="$emit('resetMenus')" />
          <div v-if="device !== 'mobile'" :style='topMenuStyle.topSmenuStyle'>
          </div>
          <a-icon v-else class='trigger' :type="collapsed ? 'menu-fold' : 'menu-unfold'" @click='toggle'></a-icon>
        </div>
        <user-menu
        class='header-index-right'
        :theme='theme'
        :oneClickHelp="oneClickHelp"
        :style='topMenuStyle.headerIndexRight'
        @resetMenus="$emit('resetMenus')"
        />
      </div>
    </div>
  </a-layout-header>
</template>

<script>
import UserMenu from '../tools/UserMenu'
import SMenu from '../menu/'
import Logo from '../tools/Logo'
import { PLATFORM_TYPE } from '@/store/mutation-types'
import Vue from 'vue'
import { mixin } from '@/utils/mixin.js'
import { ajaxGetDictItems, getDictItemsFromCache } from '@/api/api'
import GatewayMenus from '../tools/GatewayMenus.vue'

export default {
  name: 'GlobalHeader',
  components: {
    UserMenu,
    SMenu,
    Logo,
    GatewayMenus
  },
  mixins: [mixin],
  props: {
    mode: {
      type: String,
      // sidemenu, topmenu
      default: 'sidemenu'
    },
    menus: {
      type: Array,
      required: true
    },
    theme: {
      type: String,
      required: false,
      default: 'dark'
    },
    collapsed: {
      type: Boolean,
      required: false,
      default: false
    },
    device: {
      type: String,
      required: false,
      default: 'desktop'
    },
    oneClickHelp: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      headerBarFixed: false,
      //update-begin--author:sunjianlei---date:20190508------for: 顶部导航栏过长时显示更多按钮-----
      topMenuStyle: {
        headerIndexLeft: {},
        topNavHeader: {},
        headerIndexRight: {},
        topSmenuStyle: {}
      },
      pageList: [],
      linkList: [],
      activePage: '',
      parentPath: '',
      indexKey: '',
      systemType: window._CONFIG['system_Type'],
      platformType: window._CONFIG['platform_Type'],
      curPlatformObject: null
    }
  },
  watch: {
    /** 监听设备变化 */
    device() {
      if (this.mode === 'topmenu') {
        this.buildTopMenuStyle()
      }
    },
    /** 监听导航栏模式变化 */
    mode(newVal) {
      if (newVal === 'topmenu') {
        this.buildTopMenuStyle()
      }
    },
    $route: function(newRoute) {
      this.pageList = []
      this.activePage = newRoute.fullPath
      this.pageList.push(Object.assign({}, newRoute))
      this.parentPath=''
      if (newRoute.matched[2]) {
        this.parentPath = newRoute.matched[2].parent.meta.title
      }
      if(this.oneClickHelp){
        this.parentPath = window.config.oneClickHelp.title
      }
      this.initPlatformTypeDictData('platform_type')
    }
  },
  //update-end--author:sunjianlei---date:20190508------for: 顶部导航栏过长时显示更多按钮-----
  mounted() {
    window.addEventListener('scroll', this.handleScroll)
    //update-begin--author:sunjianlei---date:20190508------for: 顶部导航栏过长时显示更多按钮-----
    if (this.mode === 'topmenu') {
      this.buildTopMenuStyle()
    }
    //update-end--author:sunjianlei---date:20190508------for: 顶部导航栏过长时显示更多按钮-----
  },
  created() {
    this.pageList.push(this.$route)
    this.linkList.push(this.$route.fullPath)
    this.activePage = this.$route.fullPath
    if (this.$route.matched[2]) {
      this.parentPath = this.$route.matched[2].parent.meta.title
    }
     if(this.oneClickHelp){
        this.parentPath = window.config.oneClickHelp.title
      }
    this.initPlatformTypeDictData('platform_type')
  },
  methods: {
    /*获取系统平台类型数据字典*/
    initPlatformTypeDictData(dictCode) {
      //优先从缓存中读取字典配置
      if (getDictItemsFromCache(dictCode)) {
        let dictOptions = getDictItemsFromCache(dictCode)
        this.getPlatformTypeInfo(dictOptions)
        return
      }

      //根据字典Code, 初始化字典数组
      ajaxGetDictItems(dictCode, null).then((res) => {
        if (res.success) {
          let dictOptions = res.result
          this.getPlatformTypeInfo(dictOptions)
        }
      })
    },
    /*获取当前系统平台信息*/
    getPlatformTypeInfo(dictOptions) {
      let arr = []
      let plateformNum = sessionStorage.getItem(PLATFORM_TYPE)
      if (dictOptions.length > 0) {
        arr = dictOptions.filter(item => {
          if (item.value == plateformNum) {
            return true
          } else {
            return false
          }
        })
      }
      this.curPlatformObject = arr.length > 0 ? arr[0] : null
    },

    handleScroll() {
      if (this.autoHideHeader) {
        let scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop
        if (scrollTop > 100) {
          this.headerBarFixed = true
        } else {
          this.headerBarFixed = false
        }
      } else {
        this.headerBarFixed = false
      }
    },
    toggle() {
      this.$emit('toggle')
    },
    //update-begin--author:sunjianlei---date:20190508------for: 顶部导航栏过长时显示更多按钮-----
    buildTopMenuStyle() {
      if (this.mode === 'topmenu') {
        if (this.device === 'mobile') {
          // 手机端需要清空样式，否则显示会错乱
          this.topMenuStyle.topNavHeader = {}
          this.topMenuStyle.topSmenuStyle = {}
          this.topMenuStyle.headerIndexRight = {}
          this.topMenuStyle.headerIndexLeft = {}
        } else {
          let rightWidth = '360px'
          this.topMenuStyle.topNavHeader = { 'min-width': '165px' }
          this.topMenuStyle.topSmenuStyle = { width: 'calc(100% - 165px)' }
          this.topMenuStyle.headerIndexRight = { 'min-width': rightWidth }
          this.topMenuStyle.headerIndexLeft = { width: `calc(100% - ${rightWidth})` }
        }
      }
    },
    //update-begin--author:sunjianlei---date:20190508------for: 顶部导航栏过长时显示更多按钮-----
    /*通知（系统通知、告警通知、弹窗通知）跳转路由*/
    toMyAnnouncement(routerInfo) {
      let that = this
      that.$emit('toMyAnnouncement', routerInfo)
    }
  }
}
</script>

<style lang='less' scoped>
/* update_begin author:scott date:20190220 for: 缩小首页布局顶部的高度*/

@height: 59px;

.layout {
  .top-nav-header-index {
    .header-index-wide {
      margin-left: 10px;

      .ant-menu.ant-menu-horizontal {
        height: @height;
        line-height: @height;
      }
    }

    .trigger {
      line-height: 64px;

      &:hover {
        background: rgba(0, 0, 0, 0.05);
      }
    }
  }

  .header {
    z-index: 2;
    color: #1e3674;
    height: @height;
    background-color: @primary-color;
    transition: background 300ms;

    /* dark 样式 */

    &.dark {
      color: #000000;
      box-shadow: 0 0 4px rgba(0, 0, 0, 0.2);
      background-color: white !important;
    }
  }

  .header,
  .top-nav-header-index {
    &.dark .trigger:hover {
      background: rgba(0, 0, 0, 0.05);
    }
  }
}

.ant-layout-header {
  height: @height;
  line-height: @height;
}

/* update_end author:scott date:20190220 for: 缩小首页布局顶部的高度*/
.ant-breadcrumb-separator {
  color: #fff;
}

.breadStyle span {
  color: rgba(0, 0, 0, 0.65);
  font-family: PingFangSC-Regular;
}
</style>
