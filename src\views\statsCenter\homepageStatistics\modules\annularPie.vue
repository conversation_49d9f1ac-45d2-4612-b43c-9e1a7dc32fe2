<template>
  <div ref="echart" style="width: 100%;height: 100%"></div>
</template>

<script>
import echarts from 'echarts/lib/echarts'
import { heightPixel, widthPixel } from '@views/statsCenter/com/calculatePixel'
  export default {
    name: 'annular<PERSON>ie',
    props: {
      max: {
        type: Number,
        required:true
      },
      data: {
        type: Array,
        required:true
      },
      backGroundPieColor:{
        type: Array,
        required: false,
        default:()=>{
          return ['#07163c','#0d2540']
        }
      },
      barColor:{
        type: Array,
        required: false,
        default:()=>{
          return ['rgba(77,187,248,0)','rgba(77,187,248,0.7)','#4dbbf8']
        }
      },
      dotColor:{
        type: String,
        required: false,
        default:'#05adfb'
      },
      title:{
        type: String,
        required: true
      },
      titleColor:{
        type: String,
        required: false,
        default:'#6DD1FF'
      },
    },
    data() {
      return {
        myChart: {},
        chartData: [],
        legendName: []
      }
    },
    watch: {
      data: {
        handler(n, o) {
          if (n&&n.length > 0) {
            this.legendName = []
            this.$nextTick(() => {
              this.loadEchart();
            })
          }
        },
        deep: true,
        immediate: true
      },
    },
    mounted() {},
    methods: {
      loadEchart() {
        let w14=widthPixel(14)

        let w20=widthPixel(20)
        let h20=heightPixel(20)
        let titleSize=h20<=w20?h20:w20

        let w72=widthPixel(72)
        let h72=heightPixel(72)
        let imageSize=h72<=w72?h72:w72


        this.myChart = this.$echarts.init(this.$refs.echart);
        let giftImageUrl="data:image/png;base64,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"
        this.myChart.setOption({
          //中间圆形
          graphic: {
            elements: [
              {
                type: "image",
                zlevel:1,
                style: {
                  image: giftImageUrl,
                  width: imageSize,
                  height: imageSize             ,
                },
                left: "center",
                top: "center",
              }
            ]
          },
          //bar--start
          angleAxis: {
            max:this.max,
            clockwise: true, // 逆时针
            // 隐藏刻度线
            show: false,
            startAngle: 90
          },
          radiusAxis: {
            type: 'category',
            show: true,
            axisLabel: {
              show: false
            },
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            }
          },
          polar: [
            {
              center: ['50%', '50%'], //中心点位置
              radius: '170%' //图形大小
            }
          ],
          //bar--end

          title: {
            text: this.title,
            textStyle: {
              color:this.titleColor,
              fontSize: w14,
              height:titleSize,
              lineHeight:titleSize,
              fontWeight: 700,
              // textShadowColor: '#fff',
              // textShadowBlur: 5,
            },
            left: 'center',
            top: 'center',
          },
          legend: {
            show:false,
          },
          series: [
            //饼图--底图
            {
              type: 'pie',
              name: 'pie1',
              zlevel: 1,
              data: [100],
              radius: ['80%', '96%'],
              center: ['50%', '50%'],
              minAngle: 15,
              silent: true,
              labelLine: {
                show: false
              },
              label: {
                show: false,
              },
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 1, 1, [{
                  offset: 0,
                  color: this.backGroundPieColor[0]
                },
                  {
                    offset: 1,
                    color: this.backGroundPieColor[1]
                  }
                ], false),
              }
            },

            {
              stack: 'round',
              type: 'bar',
              zlevel: 2,
              data:this.data,
              showBackground: false,
              backgroundStyle: {
                color: 'blue',
                borderWidth: 2,
                width: 2
              },
              coordinateSystem: 'polar',
              roundCap: true,
              barWidth: 1.5, //大的占比环
              itemStyle: {
                normal: {
                  opacity: 1,
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: this.barColor[0]
                    },
                    {
                      offset: 0.3,
                      color: this.barColor[1]
                    },
                    {
                      offset: 1,
                      color: this.barColor[2]
                    }
                  ]),
                  /*shadowBlur: 1,
                  shadowColor: '#4dbbf8'*/
                }
              }
            },
            //小圆点
            {
              stack: 'round',
              zlevel: 3,
              type: 'bar',
              data: [0.01],
              showBackground: false,
              coordinateSystem: 'polar',
              roundCap: true,
              barWidth: 2,
              itemStyle: {
                color: this.dotColor,
                borderColor: this.dotColor,
                borderWidth: 3,
              },
            },
          ]
        })
        window.addEventListener('resize', () => {
          this.myChart.resize()
        })
      }
    }
  }
</script>

<style scoped>

</style>