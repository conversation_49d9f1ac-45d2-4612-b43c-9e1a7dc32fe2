<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <!-- 查询区域 -->
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class="card-style">
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="所属流程">
                  <a-input placeholder="请输入所属流程"  :maxLength='maxLength' v-model="queryParam.processName" autocomplete="off" :allowClear="true">
                  </a-input>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="流程编码">
                  <a-input placeholder="请输入流程编码" :maxLength='maxLength' v-model="queryParam.processKey" autocomplete="off" :allowClear="true">
                  </a-input>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="SLA类型">
                  <j-dict-select-tag v-model="queryParam.slaType" placeholder="请选择SLA类型" dictCode="slaType"></j-dict-select-tag>
                </a-form-item>
              </a-col>
              <a-col :span="colBtnsSpan()">
                <span class="table-page-search-submitButtons"
                      :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button class="btn-search btn-search-style" type="primary" @click="searchQuery">查询</a-button>
                  <a-button class="btn-reset btn-reset-style" @click="searchReset">重置</a-button>
                  <a v-if="isVisible" class="btn-updown-style" @click="doToggleSearch">
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered="false" style="width: 100%; flex: auto">
        <!-- 操作按钮区域 -->
        <div class="table-operator table-operator-style">
          <a-button type='primary' @click="handleAdd">新增</a-button>
          <a-dropdown v-if="selectedRowKeys.length >0">
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item key="1"  @click="batchDel">删除</a-menu-item>
            </a-menu>
            <a-button>批量操作
              <a-icon type="down" />
            </a-button>
          </a-dropdown>
        </div>
        <!-- table区域-begin -->
        <a-table
          ref="table"
          bordered
          :rowKey="(record) => { return record.id}"
          :columns="columns"
          :dataSource="dataSource" :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
          :pagination="ipagination"
          :loading="loading"
          :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
          @change="handleTableChange">
<!--          <template slot="alarmLevel" slot-scope="text,record">
            <div :style='{backgroundColor:getAlarmColor(record)}'
                 style='display:inline-block;color:#ffffff; border-radius: 10px; padding: 2px 10px;'>
              {{ getAlarmTitle(record) }}
            </div>
          </template>
          <template slot="flowHandleStatus" slot-scope="text">
            <span >{{getHandlingStatus(text)}}</span>
          </template>-->
          <span slot="action" slot-scope="text, record" class="caozuo">
            <a @click="handleDetailPage(record)">查看</a>
            <a-divider type="vertical" />
            <a-dropdown >
              <a class="ant-dropdown-link">更多 <a-icon type="down"/></a>
              <a-menu slot="overlay">
                <a-menu-item>
                  <a href="javascript:void(0);" style="color: #409eff" @click='handleEdit(record)'>编辑</a>
                </a-menu-item>
                <a-menu-item>
                  <a href="javascript:void(0);" style="color: #409eff" @click='confirmDelete(record.id)'>删除</a>
                </a-menu-item>
                </a-menu>
              </a-dropdown>
          </span>
          <template slot="tooltip" slot-scope="text">
            <a-tooltip placement="topLeft" :title="text" trigger="hover">
              <div class="tooltip">
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
      </a-card>
      <!-- 表单区域 -->
      <!--处理-->
      <sla-modal :priority-list='priorityList' ref="modalForm" @ok='loadData'></sla-modal>
    </a-col>
  </a-row>
</template>

<script>
import {JeecgListMixin} from '@/mixins/JeecgListMixin'
import { deleteAction, getAction, putAction } from '@/api/manage'
import {YqFormSearchLocation} from '@/mixins/YqFormSearchLocation'
import slaModal from '@views/flowable/SLA/modules/SLAModal.vue'
import { ajaxGetDictItems } from '@api/api'
export default {
  name: 'AlarmTaskList',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components: {
    slaModal
  },
  data() {
    return {
      maxLength:50,
      description: '告警任务页面',
      formItemLayout: {
        labelCol: {
          style: 'width:80px',
        },
        wrapperCol: {
          style: 'width:calc(100% - 80px)'
        }
      },
      priorityList: [],
      // 表头
      columns: [
        {
          title: '所属流程',
          dataIndex: 'processName'
        },
        {
          title: '流程编码',
          dataIndex: 'processKey'
        },
        {
          title: 'SLA类型',
          dataIndex: 'slaType_dictText'
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 120,
          scopedSlots: {
            customRender: 'action'
          },
        },
      ],
      url: {
        list: '/sla/level/protocol/list',
        delete: '/sla/level/protocol/delete',
        deleteBatch:'/sla/level/protocol/deleteBatch'
      },
      disableMixinCreated: true,
    }
  },
  created() {
    this.initPriorityData('priorityLevel')
  },
  activated() {
    this.loadData()
  },
  methods: {
    /*获取优先级下拉数据*/
    initPriorityData(dictCode) {
      if (dictCode != null && dictCode != '') {
        //根据字典Code, 初始化字典数组
        ajaxGetDictItems(dictCode, null).then((res) => {
          if (res.success) {
            this.priorityList = res.result
            for (let i = 0; i < this.priorityList.length; i++) {
              this.columns.splice(this.columns.length - 1, 0, {
                title: "优先级" + this.priorityList[i].text,
                dataIndex: this.priorityList[i].value
              })
            }
          }
        })
      }
    },
    loadData(arg) {
      if (!this.url.list) {
        this.$message.error('请设置url.list属性!')
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1
      }

      var params = this.getQueryParams() //查询条件
      this.loading = true
      getAction(this.url.list, params).then((res) => {
        if (res.success) {
          this.dataSource = res.result.records || res.result
          if (this.dataSource.length < 9) {
            this.clientHeight = false
          }
          for (let i = 0; i < this.dataSource.length; i++) {
            let levelData = JSON.parse(this.dataSource[i].levelData)
            let keys = Object.keys(levelData)
            this.dataSource[i].dynamicColumns=[]
            for (let k = 0; k < keys.length; k++) {
              let v = levelData[keys[k]].replace('d', '天')
              let v1 = v.replace('h', '小时')
              this.dataSource[i][keys[k]] = v1
              this.columns.forEach((item)=>{
                if (item.dataIndex===keys[k]){
                  this.dataSource[i].dynamicColumns.push({
                    title:item.title,
                    dataIndex: item.dataIndex
                  })
                }
              })
            }
          }
          this.ipagination.total = res.result.total ? res.result.total : 0
        }
        if (res.code === 510) {
          this.$message.warning(res.message)
        }
        this.loading = false
      })
    },
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
.caozuo .dropdown-disabled {
  color: rgba(0, 0, 0, 0.25) !important;
  cursor: default;
}
.confirm {
  color: rgba(0, 0, 0, 0.25) !important;
}
</style>