<template>
  <a-row :gutter='10' style='height: 100%' class='vScroll zxw'>
    <a-col style='width: 100%; height: 100%; display: flex; flex-direction: column'>
      <!-- 查询区域 -->
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <div class='table-page-search-wrapper'>
          <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
            <a-row :gutter='24' ref='row'>
              <a-col :span='spanValue'>
                <a-form-item label='模板名称'>
                  <a-input placeholder='请输入模板名称' :allowClear='true' autocomplete='off'
                    v-model='queryParam.templateName' :maxLength="maxLength"/>
                </a-form-item>
              </a-col>
              <a-col :span='colBtnsSpan()'>
                <span class='table-page-search-submitButtons'
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button type='primary' @click='searchQuery' class='btn-search-style'>查询</a-button>
                  <a-button @click='searchReset' style='margin-left: 10px' class='btn-reset-style'>重置</a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
        <!-- 查询区域-END -->
      </a-card>
      <a-card :bordered='false' style='flex: auto'>
        <!-- 操作按钮区域 -->
        <div class='table-operator'>
          <a-button @click='handleAdd'>新增</a-button>
          <a-dropdown v-if='selectedRowKeys.length > 0'>
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item key='1' @click='batchDel'>
                删除
              </a-menu-item>
            </a-menu>
            <a-button> 批量操作
              <a-icon type='down' />
            </a-button>
          </a-dropdown>
          <!-- <a-popover title='语音播报说明' placement='right'>
            <template slot='content'>
              <div>播报暂停后，需继续播报或取消播报后重新播报才能继续播报</div>
              <div>
                <a-icon type="audio" theme="twoTone" style="font-size: 20px;" /> 开始播报
              </div>
              <div>
                <a-icon type="play-circle" theme="twoTone" style="font-size: 20px" /> 取消播报
              </div>
            </template>
            <a-icon type='question-circle' theme='twoTone' style='font-size:20px;margin-left:10px;' />
          </a-popover> -->
        </div>
        <!-- table区域-begin -->
        <div>
          <a-table ref='table' bordered rowKey='id' :columns='columns' :dataSource='dataSource'
            :pagination='ipagination' :loading='loading'
            :rowSelection='{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }' @change='handleTableChange'>
            <template slot='contentSlot' slot-scope='text'>
              <a-tooltip placement='topLeft' overlayClassName='platformTableTooltip'>
                <template>
                  <div slot='title' v-html='JSON.parse(text).content' id="textHtml"></div>
                </template>
                <div>
                  <!-- <span class='tooltip'>{{ JSON.parse(text).content.substring(0, 20) + '...' }}</span> -->
                  <div class='tooltip'>{{ JSON.parse(text).content }}</div>
                  <!-- <div style="display: flex;justify-content: space-between;">
                    <a-icon type="audio" theme="twoTone" style="font-size: 20px;" @click="alarmPlay(text)" />
                    <a-icon type="play-circle" theme="twoTone" style="font-size: 20px;margin-left: 10px;"
                      @click="alarmCancel" />
                  </div> -->
                </div>
              </a-tooltip>
            </template>
            <span slot='action' slot-scope='text, record' class='caozuo'>
              <a @click='handleEdit(record)'>编辑</a>
              <a-divider type='vertical' />
              <a-dropdown>
                <a class='ant-dropdown-link'>更多
                  <a-icon type='down' /></a>
                <a-menu slot='overlay'>
                  <a-menu-item>
                    <a style='color: #409eff' @click='handleDetail(record)'>查看</a>
                  </a-menu-item>
                  <a-menu-item>
                    <a-popconfirm title='确定删除吗?' @confirm='() => handleDelete(record.id)'>
                      <a style='color: #409eff'>删除</a>
                    </a-popconfirm>
                  </a-menu-item>
                  <a-menu-item>
                    <a style='color: #409eff' @click='testSend(record.id)'>发送测试</a>
                  </a-menu-item>
                </a-menu>
              </a-dropdown>
            </span>
            <template slot='tooltip' slot-scope='text'>
              <a-tooltip placement='topLeft' :title='text' trigger='hover'>
                <div class='tooltip'>
                  {{ text }}
                </div>
              </a-tooltip>
            </template>
          </a-table>
        </div>
        <sys-notice-template-modal ref='modalForm' @ok='modalFormOk'></sys-notice-template-modal>
      </a-card>
    </a-col>
  </a-row>
</template>

<script>
  import '@/assets/less/TableExpand.less'
  import {
    mixinDevice
  } from '@/utils/mixin'
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import SysNoticeTemplateModal from './modules/SysNoticeTemplateModal'
  import JSuperQuery from '@/components/jeecg/JSuperQuery.vue'
  import { setImgAllPath } from '@/utils/imagePathAboutTinymce'
  import {
    httpAction,
    getAction,
    postAction,
    deleteAction
  } from '@/api/manage'
  import {
    YqFormSearchLocation
  } from '@/mixins/YqFormSearchLocation'
  export default {
    name: 'SysNoticeTemplateList',
    mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
    components: {
      SysNoticeTemplateModal,
      JSuperQuery
    },
    data() {
      return {
        maxLength:50,
        formItemLayout: {
          labelCol: {
            style: 'width:70px'
          },
          wrapperCol: {
            style: 'width:calc(100% - 70px)'
          }
        },
        description: '通知模板管理页面',
        // 表头
        columns: [{
            title: '模板编码',
            dataIndex: 'id'
          },
          {
            title: '模板名称',
            dataIndex: 'templateName'
          },
          {
            title: '通知名称',
            dataIndex: 'noticeConfigId_dictText'
          },
          {
            title: '模板内容',
            dataIndex: 'template',
            scopedSlots: {
              customRender: 'contentSlot'
            },
            customCell: () => {
              let cellStyle = 'text-align: left; min-width: 100px;max-width:400px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '操作',
            dataIndex: 'action',
            fixed: 'right',
            align: 'center',
            width: 200,
            scopedSlots: {
              customRender: 'action'
            }
          }
        ],
        url: {
          list: '/sys/notice/template/list',
          delete: '/sys/notice/template/delete',
          deleteBatch: '/sys/notice/template/deleteBatch',
          exportXlsUrl: '/sys/notice/template/exportXls',
          importExcelUrl: 'sys/notice/template/importExcel',
          sendNotice: 'sys/notice/template/sendNotice'
        },
        dictOptions: {},
        superFieldList: []
      }
    },
    created() {
      this.getSuperFieldList()
    },
    mounted() {},
    computed: {
      importExcelUrl: function () {
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
      }
    },
    methods: {
      loadData(arg) {
        if (!this.url.list) {
          this.$message.error('请设置url.list属性!')
          return
        }
        //加载数据 若传入参数1则加载第一页的内容
        if (arg === 1) {
          this.ipagination.current = 1
        }

        var params = this.getQueryParams() //查询条件
        this.loading = true
        getAction(this.url.list, params).then((res) => {
          if (res.success) {
            //update-begin---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
            this.dataSource = res.result.records || res.result
            if (this.dataSource.length>0){
              for (let i=0;i<this.dataSource.length;i++){
                let template=this.dataSource[i].template
                if (template&&template.length>0){
                  let obj=JSON.parse(template)
                  let content=setImgAllPath(obj.content)
                  obj.content=content
                  this.dataSource[i].template=JSON.stringify(obj)
                }
              }
            }
            if (this.dataSource.length < 9) {
              this.clientHeight = false
            }
            //author:weng    Date:20210402  for：if(res.result.total>0) 有错误，无查询结果时，页码显示有问题
            this.ipagination.total =res.result.total?res.result.total:0
            //update-end---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
          }
          if (res.code === 510) {
            this.$message.warning(res.message)
          }
          this.loading = false
        })
      },
      testSend(id) {
        postAction(this.url.sendNotice, {
          templateCode: id,
          dataJson: '{}'
        }).then((res) => {
          if (res.success) {
            this.$message.success('发送成功')
          } else {
            this.$message.error('发送失败')
          }
        })
      },

      getSuperFieldList() {
        let fieldList = []
        fieldList.push({
          type: 'string',
          value: 'id',
          text: '通知配置id',
          dictCode: ''
        })
        fieldList.push({
          type: 'string',
          value: 'templateName',
          text: '名称',
          dictCode: ''
        })
        // fieldList.push({
        //   type: 'string',
        //   value: 'business',
        //   text: '关联业务',
        //   dictCode: ''
        // })
        fieldList.push({
          type: 'string',
          value: 'template',
          text: '模板内容',
          dictCode: ''
        })
        fieldList.push({
          type: 'int',
          value: 'delflag',
          text: '删除标记, 0:未删除, 1:已删除',
          dictCode: ''
        })
        fieldList.push({
          type: 'string',
          value: 'weixinExtends',
          text: '微信',
          dictCode: ''
        })
        this.superFieldList = fieldList
      }
    }
  }
</script>
<style lang='less' scoped>
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';

/*  ::v-deep .ant-table-thead>tr>th {
    white-space: nowrap;
    text-align: center;
  }*/

  /*
  ::v-deep .ant-table-tbody>tr>td {
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;

    &:first-child,
    &:nth-child(2) {
      text-align: center;
    }

    &:nth-child(4),
    &:nth-child(5),
    &:nth-child(3) {
      text-align: left;
    }
  }
  */

  //表格宽度

/*  ::v-deep .ant-table-tbody>tr>td {
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;

    &:nth-child(4),
    &:nth-child(5) {
      min-width: 150px;
      max-width: 300px;
    }
  }*/
</style>