<template>
  <div class='card-wrapper' style='height: 100%'>
    <div class='card-top-border'></div>
    <div class='card-bottom-border'></div>

    <div v-if='title' class='title-bg' :style='{backgroundImage: `url(${titleBgPath})`}'>
      <div class='title-content'>
        <span class='main-title'>{{title}}</span>
        <span class='sub-title'>{{subTitle}}</span>
      </div>
    </div>

    <div class='body-slot' :class='{"reset-body-slot-height":title}'>
      <slot name='bodySlot' ></slot>
    </div>
  </div>
</template>
<script>

export default {
  name: "",
  props: {
    title: {
      type: String,
      required: false,
      default: ''
    },
    subTitle: {
      type: String,
      required: false,
      default: ''
    },
    titleBgPath:{
      type: String,
      required: false,
      default: '/statsCenter/homepage/title.png'
    }
  },
  data() {
    return {}
  }
}
</script>

<style scoped lang="less">
.card-wrapper {
  height: 100%;
  position: relative;
  padding: 0.15rem 0.075rem 0.15rem 0; //上下：12px/80px；右：6px/80px

  .card-border(@left:auto,@right:auto,@top:auto,@bottom:auto,@w:100%,@h:100%,@bg:#ffffff) {
    position: absolute;
    content: '';
    top: @top;
    left: @left;
    right: @right;
    bottom: @bottom;
    height: @h;
    width: @w;
    background: @bg;
  }

  .card-top-border {
    .card-border(@left: 0,@top:0,@h:0.025rem,@bg:rgba(255,255,255,0)); //@h：2px/80px
  }

  .card-bottom-border {
    .card-border(@left: 0,@bottom:0,@h:0.025rem,@bg:rgba(255,255,255,0));
  }

  .card-top-border:before {
    .card-border(@left: 0,@top:0,@w:0.1rem,@bg:#E7FFFD); //@w:8px/80px
  }

  .card-top-border:after {
    .card-border(@right: 0,@top:0,@w:0.1rem,@bg:#E7FFFD);
  }

  .card-bottom-border:before {
    .card-border(@left: 0,@bottom:0,@w:0.1rem,@bg:rgba(232,244,243,0.32));
  }

  .card-bottom-border:after {
    .card-border(@right: 0,@bottom:0,@w:0.1rem,@bg:rgba(232,244,243,0.32));
  }

  .title-bg {
    width:100% ; // 5.0625rem 405px/80px
    height: 0.425rem; // 34px/80px
    background-size: 100%;
    position: relative;

    .title-content {
      position: absolute;
      content: "";
      top: -0.1rem;
      left: 0.6rem; //48px/80px
      color: #DFEEF3;
      width: calc(100% - 0.6rem);
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;

      .main-title {
        font-size: 0.2rem;
        font-weight: 600;
        letter-spacing: 0.1em;
      }

      .sub-title {
        margin-left:0.15rem;//12px/80px
        font-size: 0.15rem; //12px/80px
        font-weight: 400;
        letter-spacing: 0.1em;
      }
    }
  }
}


.body-slot{
  height: 100%;
}
.reset-body-slot-height{
  height: calc(100% - 0.425rem) !important;
}
.empty-wrapper {
  height: 100%;

  .spin{
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
::v-deep .ant-empty-description{
  color:#fff !important;
}
::v-deep .ant-spin-dot-item{
  background-color: #fff;
}
</style>