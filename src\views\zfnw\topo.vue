<template>
  <vis-edit  ref="bigScreen" operate="show" ></vis-edit>
</template>

<script>
import { getAction, postAction, putAction, deleteAction } from '@/api/manage'
import VisEdit from '@/views/topo/nettopo/modules/VisEdit'
export default {
  components: {
    VisEdit
  },
  data() {
    return {
      nettopoList: [],
      topoKey: '',
      topo: null,
    }
  },
  created() {

  },
  mounted() {
     let query = this.$router.history.current.query
     //通过id获取拓扑图
    if (query && query.key) {
      this.topoKey = query.key
       this.$refs.bigScreen.createTopo(this.topoKey);
    }

  },
  methods: {

  },
}
</script>

<style lang="less" scoped>
.page-body {
  height: 100%;
  background: #fff;
}
</style>