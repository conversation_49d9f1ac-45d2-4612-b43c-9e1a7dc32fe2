<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll zxw">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <!-- 查询区域 -->
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class="card-style">
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="标题">
                  <a-input
                    placeholder="请输入搜索关键词"
                    autocomplete="off"
                    :allowClear="true"
                    v-model="queryParam.title"
                  ></a-input>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="状态">
                  <a-select
                    :getPopupContainer="(node) => node.parentNode"
                    v-model="queryParam.status"
                    placeholder="请选择"
                    :allowClear="true"
                  >
                    <a-select-option value="0">草稿</a-select-option>
                    <a-select-option value="1">处理中</a-select-option>
                    <a-select-option value="2">已结束</a-select-option>
                    <a-select-option value="3">已撤回</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="结果">
                  <a-select v-model="queryParam.result" placeholder="请选择" :allowClear="true">
                    <a-select-option value="0">未提交</a-select-option>
                    <a-select-option value="1">处理中</a-select-option>
                    <a-select-option value="2">通过</a-select-option>
                    <a-select-option value="3">驳回</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue" v-show="toggleSearchStatus">
                <a-form-item label="时间">
                  <a-range-picker
                    v-model="queryParam.createTimeRange"
                    format="YYYY-MM-DD"
                    :placeholder="['创建时间', '提交申请时间']"
                    @change="onDateChange"
                    @ok="onDateOk"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="colBtnsSpan()">
                <span
                  class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                >
                  <a-button type="primary" @click="searchQuery" class="btn-search-style">查询</a-button>
                  <a-button @click="searchReset" style="margin-left: 10px" class="btn-reset-style">重置</a-button>
                  <a v-if="isVisible" @click="doToggleSearch" class="btn-updown-style">
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered="false" style="flex: auto" class="core">
        <a-row>
          <a-col :span="12" class="table-operator">
            <a-button @click="addApply" :loading="addApplyLoading">发起申请</a-button>
          </a-col>
        </a-row>
        <!-- table区域-begin -->
        <a-table
          bordered
          ref="table"
          rowKey="id"
          :dataSource="dataSource"
          :pagination="ipagination"
          :loading="loading"
          @change="handleTableChange"
        >
          <a-table-column title="序号" :width="50" align="center">
            <template slot-scope="t, r, i">
              <span> {{ i + 1 }} </span>
            </template>
          </a-table-column>
          <a-table-column title="标题" dataIndex="title" :width="150" align="center" over-flow="eli">
            <template slot-scope="t, r, i">
              <span> {{ t }} </span>
            </template>
          </a-table-column>
          <a-table-column title="所属流程" dataIndex="processName" :width="150" align="center">
            <template slot-scope="t, r, i">
              <span> {{ t }} </span>
            </template>
          </a-table-column>
          <a-table-column title="当前审批环节" dataIndex="currTaskName" :width="150" align="center">
            <template slot-scope="t, r, i">
              <span> {{ t }} </span>
            </template>
          </a-table-column>
          <a-table-column
            title="状态"
            dataIndex="status"
            :width="150"
            align="center"
            key="s"
            :sorter="(a, b) => a.status - b.status"
          >
            <template slot-scope="t, r, i">
              <span :style="{ color: getStatus(t).color }"> {{ getStatus(t).text }} </span>
            </template>
          </a-table-column>
          <a-table-column
            title="结果"
            dataIndex="result"
            :width="150"
            align="center"
            key="result"
            :sorter="(a, b) => a.result - b.result"
          >
            <template slot-scope="t, r, i">
              <span :style="{ color: getResult(t).color }"> {{ getResult(t).text }} </span>
            </template>
          </a-table-column>
          <a-table-column title="创建时间" dataIndex="createTime" :width="150" align="center">
            <template slot-scope="t, r, i">
              <span> {{ t }} </span>
            </template>
          </a-table-column>
          <a-table-column title="提交申请时间" dataIndex="applyTime" :width="150" align="center">
            <template slot-scope="t, r, i">
              <span> {{ t }} </span>
            </template>
          </a-table-column>
          <a-table-column title="操作" dataIndex="" align="center" :width="180" class="caozuo">
            <template slot-scope="t, r, i">
              <template v-if="r.status == 0">
                <a href="javascript:void(0);" @click="apply(r)">提交申请</a>
                <a-divider type="vertical" />
                <a href="javascript:void(0);" @click="edit(r)">编辑</a>
                <a-divider type="vertical" />
                <a-popconfirm title="确定删除吗?" @confirm="() => remove(r)">
                  <a href="javascript:void(0);">删除</a>
                </a-popconfirm>
              </template>
              <template v-else-if="r.status == 1">
                <a href="javascript:void(0);" @click="cancel(r)">撤回</a>
                <a-divider type="vertical" />
                <a href="javascript:void(0);" @click="history(r)">查看进度</a>
                <a-divider type="vertical" />
                <a href="javascript:void(0);" @click="detail(r)">表单数据</a>
              </template>
              <template v-else-if="(r.status == 2 && r.result == 3) || r.status == 3">
                <a-popconfirm title="确定提交申请吗?" @confirm="() => apply(r)">
                  <a href="javascript:void(0);">重新申请</a>
                </a-popconfirm>
                <a-divider type="vertical" />
                <a href="javascript:void(0);" @click="edit(r)">编辑</a>
                <a-divider type="vertical" />
                <a href="javascript:void(0);" @click="history(r)">审批历史</a>
              </template>
              <template v-else>
                <a href="javascript:void(0);" @click="detail(r)">表单数据</a>
                <a-divider type="vertical" />
                <a href="javascript:void(0);" @click="history(r)">审批历史</a>
              </template>
            </template>
          </a-table-column>
          <template slot="tooltip" slot-scope="text">
            <a-tooltip placement="topLeft" :title="text" trigger="hover">
              <div class="tooltip">
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
        <!-- table区域-end -->
        <!--流程申请选择-->
        <a-drawer
          title="选择流程"
          width="500px"
          placement="right"
          :closable="false"
          @close="processModalVisible = false"
          :visible="processModalVisible"
        >
          <a-empty description="无流程可供选择" v-if="activeKeyAll.length == 0" />
          <div v-else>
            <a-input-search
              style="margin-bottom: 10px; width: 200px"
              placeholder="输入流程名称"
              @search="onSearchProcess"
              autocomplete="off"
              :allowClear="true"
            />
            <a-collapse v-model="activeKey">
              <a-collapse-panel
                v-for="(value, index) in activeKeyAll"
                :header="filterDictText(dictOptions, value) || '未分类'"
                :key="value"
              >
                <a-list :grid="{ gutter: 10, column: 1 }" :dataSource="processDataMap[value]">
                  <a-list-item slot="renderItem" slot-scope="item">
                    <a-card>
                      <div slot="title">
                        <a-row>
                          <a-col span="12" :title="item.realname">{{ item.realname }} </a-col>
                          <a-col span="12" style="text-align: right">
                            <a href="javascript:void (0)" @click="chooseProcess(item)" style="color: #01a9fd"
                              >发起申请</a
                            >
                          </a-col>
                        </a-row>
                      </div>
                      <b>版本：</b>v.{{ item.version }}
                      <br />
                      <b>说明：</b>{{ item.description }}
                    </a-card>
                  </a-list-item>
                </a-list>
              </a-collapse-panel>
            </a-collapse>
          </div>
        </a-drawer>
        <!--流程表单-->
        <a-modal :title="lcModa.title" v-model="lcModa.visible" :footer="null" :maskClosable="false" width="80%">
          <component
            :disabled="lcModa.disabled"
            v-if="lcModa.visible"
            :is="lcModa.formComponent"
            :processData="lcModa.processData"
            :isNew="lcModa.isNew"
            @afterSubmit="afterSub"
            @close=";(lcModa.visible = false), (lcModa.disabled = false)"
          ></component>
        </a-modal>
        <!--提交申请表单-->
        <a-modal title="提交申请" v-model="modalVisible" :mask-closable="false" :width="500" :footer="null">
          <div v-if="modalVisible">
            <a-form-item label="选择审批人" v-show="showAssign">
              <l-select-user-by-dep v-model="form.assignees" :multi="false"></l-select-user-by-dep>
            </a-form-item>
            <a-form-item label="下一审批人" v-show="isGateway">
              <a-alert type="info" showIcon message="分支网关处不支持自定义选择下一审批人，将自动下发给所有可审批人。"
                >，将发送给下一节点所有人</a-alert
              >
            </a-form-item>
            <a-form-item label="优先级" prop="priority">
              <a-select v-model="form.priority" placeholder="请选择" :allowClear="true" style="width: 100%">
                <a-select-option :value="0">普通</a-select-option>
                <a-select-option :value="1">重要</a-select-option>
                <a-select-option :value="2">紧急</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="消息通知">
              <a-checkbox v-model="form.sendMessage">站内消息通知</a-checkbox>
              <a-checkbox v-model="form.sendSms" disabled>短信通知</a-checkbox>
              <a-checkbox v-model="form.sendEmail" disabled>邮件通知</a-checkbox>
            </a-form-item>
            <div slot="footer" style="text-align: right">
              <a-button type="text" @click="modalVisible = false">取消</a-button>
              <div style="display: inline-block; width: 20px"></div>
              <a-button type="primary" :disabled="submitLoading" @click="applySubmit">提交</a-button>
            </div>
          </div>
        </a-modal>
        <a-modal title="审批历史" v-model="modalLsVisible" :mask-closable="false" :width="'80%'" :footer="null">
          <div v-if="modalLsVisible">
            <historicDetail :procInstId="procInstId"></historicDetail>
          </div>
        </a-modal>
        <a-modal title="确认撤回" v-model="modalCancelVisible" :mask-closable="false" :width="500">
          <a-form ref="delForm" v-model="cancelForm" :label-width="70" v-if="modalCancelVisible">
            <a-form-item label="撤回原因" prop="reason">
              <a-input type="textarea" v-model="cancelForm.reason" :allowClear="true" autocomplete="off" :rows="4" />
            </a-form-item>
          </a-form>
          <div slot="footer" style="text-align: right">
            <a-button type="text" @click="modalCancelVisible = false">取消</a-button>
            <a-button type="primary" :disabled="submitLoading" @click="handelSubmitCancel">提交</a-button>
          </div>
        </a-modal>
        <start-process ref="startCode" @ok="searchQuery"></start-process>
      </a-card>
    </a-col>
  </a-row>
</template>

<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { activitiMixin } from '@/views/activiti/mixins/activitiMixin'
import { filterObj } from '@/utils/util'
import JEllipsis from '@/components/jeecg/JEllipsis'
import { deleteAction, getAction, downFile, postAction } from '@/api/manage'
import pick from 'lodash.pick'
import JTreeSelect from '@/components/jeecg/JTreeSelect'
import { initDictOptions, filterDictText } from '@/components/dict/JDictSelectUtil'
import historicDetail from '@/views/activiti/historicDetail'
import StartProcess from '../../components/activiti/StartProcess'
import LSelectUserByDep from '@/components/jeecgbiz/LSelectUserByDep'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
export default {
  name: 'applyList',
  mixins: [activitiMixin, JeecgListMixin, YqFormSearchLocation],
  components: {
    JEllipsis,
    JTreeSelect,
    historicDetail,
    StartProcess,
    LSelectUserByDep,
  },
  data() {
    return {
      formItemLayout: {
        labelCol: {
          style: 'width:70px',
        },
        wrapperCol: {
          style: 'width:calc(100% - 70px)'
        }
      },
      scroll: { x: '100%', y: '100%' },
      description: '我的申请',
      dictOptions: [],
      url: {
        list: '/actBusiness/listData',
        getProcessDataList: '/activiti_process/listData',
        delByIds: '/actBusiness/delByIds',
        getFirstNode: '/actProcessIns/getFirstNode',
        applyBusiness: '/actBusiness/apply',
        cancelApply: '/actBusiness/cancel',
      },
      // 查询条件
      queryParam: {
        createTimeRange: [],
        keyWord: '',
      },
      processModalVisible: null,
      activeKeyAll: [],
      activeKey: [],
      processDataMap: {},
      searchProcessKey: null,
      addApplyLoading: false,
      lcModa: {
        title: '',
        disabled: false,
        visible: false,
        formComponent: null,
        isNew: false,
      },
      form: {
        priority: 0,
        assignees: '',
        sendMessage: true,
      },
      modalVisible: false,
      showAssign: false,
      assigneeList: [],
      isGateway: false,
      dictPriority: [],
      submitLoading: false,
      error: '',
      /*审批历史*/
      modalLsVisible: false,
      procInstId: '',
      modalCancelVisible: false,
      dialogStartInstanceVisible: false,
      processDefinition: undefined,
      cancelForm: {},
    }
  },
  methods: {
    initDictConfig() {
      //初始化字典 - 流程分类
      initDictOptions('bpm_process_type').then((res) => {
        if (res.success) {
          // this.dictOptions = res.result;
          res.result.forEach((e) => {
            if (e.value == 'sj' || e.value == 'wtgl' || e.value == 'bg' || e.value == 'fb' || e.value == 'pzgl') {
              return
            }
            this.dictOptions.push(e)
          })
        }
      })
    },
    filterDictText(dictOptions, text) {
      if (dictOptions instanceof Array) {
        for (let dictItem of dictOptions) {
          if (text === dictItem.value) {
            return dictItem.text
          }
        }
      }
      return text || text == 'null' ? '' : text
    },
    getProcessList() {
      this.addApplyLoading = true
      getAction(this.url.getProcessDataList, { status: 1, roles: true })
        .then((res) => {
          this.activeKeyAll = []
          if (res.success) {
            var result = res.result || []
            if (result.length > 0) {
              let searchProcessKey = this.searchProcessKey
              if (searchProcessKey) {
                //过滤条件
                result = _.filter(result, function (o) {
                  return o.name.indexOf(searchProcessKey) > -1
                })
              }
              this.processDataMap = _.groupBy(result, 'categoryId')
              for (const categoryId in this.processDataMap) {
                this.activeKeyAll.push(categoryId)
              }
              this.activeKey = this.activeKeyAll
            }
            this.processModalVisible = true
          } else {
            this.$message.warning(res.message)
          }
        })
        .finally(() => (this.addApplyLoading = false))
    },
    loadData(arg) {
      if (!this.url.list) {
        this.$message.error('请设置url.list属性!')
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1
      }
      var params = this.getQueryParams() //查询条件
      this.loading = true
      getAction(this.url.list, params).then((res) => {
        if (res.success) {
          let records = res.result || []
          this.dataSource = records
          this.ipagination.total = records.length
        }
        if (res.code === 510) {
          this.$message.warning(res.message)
        }
        this.loading = false
      })
    },
    getQueryParams() {
      var param = Object.assign({}, this.queryParam, this.isorter)
      delete param.createTimeRange // 时间参数不传递后台
      return filterObj(param)
    },

    // 重置
    searchReset() {
      var that = this
      var logType = that.queryParam.logType
      that.queryParam = {} //清空查询区域参数
      that.queryParam.logType = logType
      that.loadData(this.ipagination.current)
    },
    onDateChange: function (value, dateString) {
      this.queryParam.createTime_begin = dateString[0]
      this.queryParam.createTime_end = dateString[1]
    },
    onDateOk(value) {},

    getStatus(status) {
      let text = '未知',
        color = ''
      if (status == 0) {
        text = '草稿'
        // color = "default";
      } else if (status == 1) {
        text = '处理中'
        // color = "orange";
      } else if (status == 2) {
        text = '已结束'
        // color = "blue";
      } else if (status == 3) {
        text = '已撤回'
        // color = "magenta";
      }
      return { text: text, color: color }
    },
    getResult(result) {
      let text = '未知',
        color = ''
      if (result == 0) {
        text = '未提交'
        // color = "default";
      } else if (result == 1) {
        text = '处理中'
        // color = "orange";
      } else if (result == 2) {
        text = '已通过'
        color = '#139B33'
      } else if (result == 3) {
        text = '已驳回'
        color = '#DF1A1A'
      }
      return { text: text, color: color }
    },
    apply(v) {
      if (!v.procDefId || v.procDefId == 'null') {
        this.$message.error('流程定义为空')
        return
      }
      this.form.id = v.id
      this.form.procDefId = v.procDefId
      this.form.title = v.title

      this.form.assignees = ''
      this.modalVisible = true
      this.isGateway = false
      this.form.firstGateway = true
      this.showAssign = true
    },
    applySubmit() {
      if (this.showAssign && this.form.assignees.length < 1) {
        this.error = '请至少选择一个审批人'
        this.$message.error(this.error)
        return
      } else {
        this.error = ''
      }
      this.submitLoading = true
      var params = Object.assign({}, this.form)
      // params.assignees = params.assignees.join(",")
      postAction(this.url.applyBusiness, params)
        .then((res) => {
          if (res.success) {
            this.$message.success('操作成功')
            this.loadData()
            this.modalVisible = false
          } else {
            this.$message.error(res.message)
          }
        })
        .finally(() => (this.submitLoading = false))
    },
    edit(r, isView) {
      if (!r.formKey) {
        this.$message.warning('该流程信息未配置表单，请联系开发人员！')
        return
      }
      this.$refs.startCode.initData(r)
      this.$refs.startCode.title = '编辑流程业务信息：' + r.processName
    },
    remove(r) {
      postAction(this.url.delByIds, { ids: r.id }).then((res) => {
        if (res.success) {
          this.$message.success(res.message)
          this.loadData()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    cancel(v) {
      this.cancelForm.id = v.id
      this.cancelForm.procInstId = v.procInstId
      this.modalCancelVisible = true
    },
    handelSubmitCancel() {
      this.submitLoading = true
      postAction(this.url.cancelApply, this.cancelForm)
        .then((res) => {
          if (res.success) {
            this.$message.success('操作成功')
            this.loadData()
            this.modalCancelVisible = false
          } else {
            this.$message.error(res.message)
          }
        })
        .finally(() => (this.submitLoading = false))
    },
    history(v) {
      if (!v.procInstId) {
        this.$message.error('流程实例ID不存在')
        return
      }
      this.procInstId = v.procInstId
      this.modalLsVisible = true
    },
    detail(v) {
      this.edit(v, true)
    },
    handleTableChange(pagination, filters, sorter) {
      //分页、排序、筛选变化时触发
      //TODO 筛选
      if (Object.keys(sorter).length > 0) {
        this.isorter.column = sorter.field
        this.isorter.order = 'ascend' == sorter.order ? 'asc' : 'desc'
      }
      this.ipagination = pagination
      // this.loadData();
    },
    addApply() {
      this.getProcessList()
    },
    onSearchProcess(value) {
      this.searchProcessKey = value
      this.getProcessList()
    },
    chooseProcess(v) {
      if (!v.formKey) {
        this.$message.warning('该流程信息未配置表单，请联系开发人员！')
        return
      }
      this.$refs.startCode.initData(v)
      this.$refs.startCode.title = '发起流程：' + v.name
    },
    afterSub(formData) {
      this.lcModa.visible = false
      this.loadData()
    },
  },
}
</script>
<style lang="less" scoped>
@import '~@assets/less/scroll.less';
@import '~@assets/less/common.less';
</style>