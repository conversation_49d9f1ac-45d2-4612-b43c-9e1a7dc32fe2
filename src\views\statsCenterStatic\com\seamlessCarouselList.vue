<template>
  <div class="carousel-table">
    <div class="table-header">
      <span :style='{width:columnWidth[index]}' v-for='(item,index) in columnName' :key='"column_name_"+index'>{{item}}</span>
    </div>
    <div class="table-body" @mousewheel="handleMouseWheel">
      <vue-seamless-scroll :data="listData" :class-option="warning" ref="seamlessDiv" class="seamless-warp">
        <ul>
          <li
            v-for="(item,index) in listData"
            :key='"list_tr_"+index'
            :style='{backgroundColor:(index+1)%2!==0?"rgba(29, 78, 140, 0.25)": "rgba(29, 78, 140, 0)"}'>
            <a-tooltip placement="top" :title="m" trigger="hover" v-for='(m,idx) in item' :key='"tooltip_"+idx' :overlayClassName='overlayClassName'>
              <span :style='{width:columnWidth[idx]}' :key='"list_td_"+idx'> {{ m }} </span>
            </a-tooltip>
          </li>
        </ul>
      </vue-seamless-scroll>
    </div>
  </div>
</template>
<script>
import vueSeamlessScroll from 'vue-seamless-scroll'
export default {
  name: "seamlessCarouselList",
  components:{vueSeamlessScroll},
  props: {
    columnName: {
      type: Array,
      required: true
    },
   columnWidth: {
      type: Array,
      required: true
    },
    listData:{
      type: Array,
      required: true
    },
    overlayClassName: {
      type: String,
      required: false,
      default:''
    },
  },
  data(){
    return{}
  },
  computed: {
    warning() {
      return {
        step: 0.1, // 数值越大速度滚动越快
        limitMoveNum: 4, // 开始无缝滚动的数据量 this.dataList.length
        hoverStop: true, // 是否开启鼠标悬停stop
        direction: 1, // 0向下 1向上 2向左 3向右
        // openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 86, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
        // singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
        waitTime: 2, // 单步运动停止的时间(默认值1000ms)
      }
    }
  },
  methods:{
    handleMouseWheel(e) {
      if (Math.abs(this.$refs.seamlessDiv.yPos) < this.$refs.seamlessDiv.realBoxHeight / 2 || e.deltaY < 0) {
        this.$refs.seamlessDiv.yPos -= e.deltaY
        this.$refs.seamlessDiv.yPos = this.$refs.seamlessDiv.yPos > 0 ? 0 : this.$refs.seamlessDiv.yPos
      }
    }
  }
}
</script>

<style lang='less' scoped>
.carousel-table::-webkit-scrollbar {
  display: none;
  /*隐藏滚动条*/
}

.carousel-table {
  width: 100%;
  height: 100%;

  .table-header {
    display: flex;
    height: 0.2rem;
    align-items: center;
    margin-bottom: 0.2rem;

    span {
      display: inline-block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      padding: 0;
      font-size: 0.2rem;
      width: 100%;
      text-align: center;
      color: rgba(206, 225, 255, 0.7);
      font-weight: 500;
    }
  }

  .table-body {
    width: 100%;
    height: calc(100% - 0.4rem);
    overflow: hidden;

    .seamless-warp {
      height: 100%;
      width: 100%;

      ul {
        width: 100%;
        padding: 0;
        margin: 0;
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        li {
          text-align: center;
          height:0.45rem /* 36/80 */;
          width: 100%;
          line-height: 0.45rem;
          display: flex;
          justify-content: space-around;
          flex-flow: row nowrap;
          text-align: center;
          font-size: 0.175rem;//14/80

          span {
            display: inline-block;
            padding: 0 0.1rem;
            color: rgba(237, 245, 255, 0.95);
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
        /*li:nth-child(2n + 1) {
          background: rgba(29, 78, 140, 0.25);
        }*/
      }
    }
  }
}
</style>