<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-item label="类型名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                v-decorator="['categoryName', validatorRules.categoryName]"
                :allowClear="true"
                autocomplete="off"
                placeholder="请输入类型名称"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="类型编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                v-decorator="['categoryCode', validatorRules.assetsCode]"
                :allowClear="true"
                autocomplete="off"
                placeholder="请输入类型编号"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item class="two-words" label="描述" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                v-decorator="['categoryDescribe', validatorRules.categoryDescribe]"
                :allowClear="true"
                autocomplete="off"
                placeholder="请输入描述"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item class="two-words" label="序号" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number
                v-decorator="['categorySerial', validatorRules.categorySerial]"
                placeholder="请输入序号"
              ></a-input-number>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item class="two-words" label="状态" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-radio-group v-model="categoryState">
                <a-radio value="1"> 未启用</a-radio>
                <a-radio value="0"> 启用</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="类型属性" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag
                type="list"
                v-decorator="['isMonitorable', validatorRules.isMonitorable]"
                :trigger-change="true"
                dictCode="is_monitorable"
                placeholder="请选择类型属性"
                :show-select="false"
                @change="changeMonitorableType($event)"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="父级节点" :labelCol="labelCol" :wrapperCol="wrapperCol" :key='refreshKey'>
              <j-tree-select-expand
                style="width: 100%"
                v-model="parentId"
                :placeholder="'请选择父级节点'"
                dict='cmdb_assets_category,category_name,id'
                pid-field='parent_id'
                pid-value='0'
                :current-node='model.id'
                :replace-fields="{ children: 'children', key: 'key', value: 'key', title: 'title' }"
                :treeData="treeData"
                :condition=condition
                @change="changeTreeNode($event)"
              ></j-tree-select-expand>
            </a-form-item>
          </a-col>
          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>
<script>
import { httpAction, getAction } from '@/api/manage'
import pick from 'lodash.pick'
import { validateDuplicateValue } from '@/utils/util'
import JFormContainer from '@/components/jeecg/JFormContainer'
import JDictSelectTag from '@/components/dict/JDictSelectTag'

export default {
  name: 'CmdbAssetsCategory1Form',
  components: {
    JFormContainer,
    JDictSelectTag,
  },
  props: {
    //流程表单data
    formData: {
      type: Object,
      default: () => {},
      required: false,
    },
    //表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false,
    },
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false,
    },
  },
  data() {
    return {
      form: this.$form.createForm(this),
      model: {},
      isDis: false,
      dis: false,
      categoryState: '0',
      oldParentId: '',
      parentId: undefined,
      Monitorable: '1',
      recordCharge: '',
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      confirmLoading: false,

      validatorRules: {
        categoryName: {
          rules: [{ required: true, message: '请输入类型名称!'},
          {min: 2, message:'类型名称长度应在 2-20 之间！' ,trigger: 'blur'},
          {max:20, message:'类型名称长度应在 2-20 之间！' ,trigger: 'blur'}],
        },
        assetsCode: {
          rules: [{ required: true, message: '请输入类型编号!'},
          {min: 2, message:'类型编号长度应在 2-20 之间！' ,trigger: 'blur'},
          {max:20, message:'类型编号长度应在 2-20 之间！' ,trigger: 'blur'}],
        },
        categoryDescribe:{
          rules: [
            //{ required: false, message: '请输入描述!'},
          {min: 2, message:'描述长度应在 2-20 之间！' ,trigger: 'blur'},
          {max:20, message:'描述长度应在 2-20 之间！' ,trigger: 'blur'}],
        },
        categorySerial: {
          rules: [{ required: true, message: '请输入序号!' },
                  { pattern: /^[1-9]{1}[0-9]*$/,message: '请输入大于0的整数'}
          ],
        },
        parentId: {
          rules: [{ required: true, message: '请选择父节点!' }],
        },
        isMonitorable: {
          rules: [{ required: true, message: '请选择类型属性!' }],
        },
      },
      url: {
        add: '/category/cmdbAssetsCategory1/add',
        edit: '/category/cmdbAssetsCategory1/edit',
        queryById: '/category/cmdbAssetsCategory1/queryById',
        loadTreeData: '/sys/dict/loadTreeData',
      },
      treeData: [],
      condition:'{ "delflag": 0, "is_monitorable": 1}',
      refreshKey:0
    }
  },
  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    },
  },
  created() {
    //如果是流程中表单，则需要加载流程表单data
    this.showFlowData()
  },
  methods: {
    loadTreeData() {
       this.condition=this.Monitorable=='1'?'{ "delflag": 0, "is_monitorable": 1}':'{ "delflag": 0, "is_monitorable": 0}'
      let data = {
        pid: 0,
        tableName: 'cmdb_assets_category',
        text: 'category_name',
        code: 'id',
        pidField: 'parent_id',
        hasChildField: '',
        condition:  this.condition,
      }
      this.confirmLoading=true
      getAction(this.url.loadTreeData, data).then((res) => {
        if (res.success) {
          this.treeData = res.result
          this.refreshKey++
        }
        this.confirmLoading=false
      })
    },

    changeMonitorableType(e) {
      this.parentId = undefined
      //1可监控，0不可监控
      this.Monitorable=e==1?'1':'0'
      this.loadTreeData()
    },
    changeTreeNode(e) {
      this.parentId = e
    },
    add() {
      this.edit({})
    },
    edit(record) {
      this.Monitorable=1
      this.form.resetFields()
      this.model = Object.assign({}, record)
      if (Object.keys(record).length != 0) {
        this.categoryState = record.categoryState
        this.hasChild = record.hasChild
        this.oldParentId = record.parentId
        if (record.parentId == 0) {
          this.parentId = undefined
        } else {
          this.parentId = record.parentId
        }
        this.recordCharge = record.parentId
        this.Monitorable = record.isMonitorable
      }
      this.loadTreeData()
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(
          pick(
            this.model,
            'categoryName',
            'categoryCode',
            'categoryDescribe',
            'categorySerial',
            //'categoryState',
            //'parentId',
            //'hasChild',
            //'delflag',
            'isMonitorable'
          )
        )
      })
    },
    selDynamicTempByProId(e) {
      if (!!e) {
        let paramObj = {
          assetsCategoryId: e.trim(),
        }
        getAction(this.url.findCodeName, paramObj).then((res) => {
          if (!!res) {
            this.collectInfoList = res.result
          }
        })
      }
    },
    //渲染流程表单数据
    showFlowData() {
      if (this.formBpm === true) {
        let params = { id: this.formData.dataId }
        getAction(this.url.queryById, params).then((res) => {
          if (res.success) {
            this.edit(res.result)
          }
        })
      }
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let formData = Object.assign(this.model, values)
          formData.categoryState = this.categoryState
          formData.hasChild = this.hasChild
          formData.oldParentId = this.oldParentId
          formData.parentId = this.parentId
          if (formData.parentId == '' || formData.parentId == undefined) {
            formData.parentId = '0'
          }
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
              // that.loadData()
            })
        }
      })
    },
    popupCallback(row) {
      this.form.setFieldsValue(
        pick(
          row,
          'categoryName',
          'categoryCode',
          'categoryDescribe',
          'categorySerial',
          'categoryState',
          'parentId',
          'hasChild',
          'delflag',
          'isMonitorable'
        )
      )
    },
  },
}
</script>
<style lang="less" scoped>
::v-deep .two-words > div > label {
  letter-spacing: 4px;
}
::v-deep .two-words > div > label::after {
  letter-spacing: 0px;
}
</style>