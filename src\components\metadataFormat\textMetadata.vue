<template>
  <a-form-item label="长度" :labelCol="labelCol" :wrapperCol="wrapperCol" :validateStatus='validateStatus' :help='help' :required='false'>
    <a-input v-model="formatValue" placeholder="请输入格式化值" @change="onChange" :allowClear='true' autocomplete='off'/>
  </a-form-item>
</template>

<script>
  import fa from 'element-ui/src/locale/lang/fa'

  export default {
    name: 'textMetadata',
    computed: {
      fa() {
        return fa
      }
    },
    props: ['typeFormatValue'],
    data() {
      return {
        validateStatus: 'success',
        help: '',
        formatValue: '',
        labelCol: {
          xs: {span: 5},
          sm: {span: 5}
        },
        wrapperCol: {
          xs: {span: 24},
          sm: {span: 16}
        }
      }
    },
    mounted() {
      if (undefined != this.typeFormatValue && '' != this.typeFormatValue) {
        var obj = JSON.parse(this.typeFormatValue);
        this.formatValue = obj;
      }
    },
    methods: {
      onChange(value) {
        this.validateStatus = 'success'
        if (value && value.target.value.length > 50) {
          this.validateStatus = 'error'
          this.help = '长度不得超过50个字符'
        } else {
          this.validateStatus = 'success'
          this.help = ''
          this.value = value.target.value;
          this.$emit('changElemType', JSON.stringify(this.value));
        }
      }
    }
  }
</script>

<style>
</style>