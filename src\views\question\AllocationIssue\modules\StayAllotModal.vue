<template>
  <a-modal
    :title="title"
    :width="modalWidth"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @cancel="handleCancel"
    @ok="handleOk"
    okText=""
    cancelText="关闭"
    wrapClassName="ant-modal-cust-warp"
    style="height: 70%; overflow: hidden; overflow-y: auto"
    :centered="true"
  >
    <a-form :form="form" style="height: 100%; overflow-y: auto">
      <div>
        <a-row>
          <a-col>
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="问题类型">
              <j-dict-select-tag
                type="list"
                disabled
                v-decorator="['questionType']"
                :trigger-change="true"
                dictCode="helpQuestionType"
                placeholder="请输入问题类型"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col>
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="问题内容">
              <textarea style="width: 100%" disabled placeholder="请输入备注" v-decorator="['question']"></textarea>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col>
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="提问人">
              <a-input disabled v-decorator="['quizzer']" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col>
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="联系电话">
              <a-input disabled v-decorator="['contact']" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col>
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="地区">
              <a-input disabled v-decorator="['regionName']" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col>
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="提问时间">
              <a-input disabled v-decorator="['createTime']" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col>
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="设备地址">
              <a-input disabled v-decorator="['ip']" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-form-item label="处理人" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <l-select-user-by-dep v-decorator="['userid', formValidator.userid]" :multi="false"></l-select-user-by-dep>
          </a-form-item>
        </a-row>
        <a-row>
          <a-form-item label="备注" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <textarea
              style="width: 100%"
              placeholder="请输入备注"
              v-decorator="['remark', formValidator.remark]"
              class="border_color"
            ></textarea>
          </a-form-item>
        </a-row>
      </div>
    </a-form>
  </a-modal>
</template>
<script>
import pick from 'lodash.pick'
import { userList, editRecord } from '@api/AllocationIssue'
import JDictSelectTag from '@/components/dict/JDictSelectTag'
import LSelectUserByDep from '@/components/jeecgbiz/LSelectUserByDep'
export default {
  name: 'StayAllotModal', //分配工单弹窗
  components: {
    JDictSelectTag,
    LSelectUserByDep,
  },
  data() {
    return {
      title: '操作',
      visible: false,
      confirmLoading: false,
      /* 弹框宽 */
      modalWidth: '700px',
      form: this.$form.createForm(this),
      usersList: [], //运维人员列表
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      formValidator: {
        remark: {
          rules: [
            {
              min: 2,
              max: 500,
              message: '长度在 2 到 500 个字符',
              trigger: 'blur',
            },
          ],
        },
        user: {
          rules: [
            {
              required: true,
              message: '必选!',
            },
          ],
        },
        userid: {
          rules: [{ required: true, message: '请选择处理人' }],
        },
      },
    }
  },
  mounted() {},
  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      //获取运维人员列表
      // this.getUsersList()
      this.model = Object.assign({}, record)
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(
          pick(
            this.model,
            'questionType',
            'question',
            'quizzer',
            'createTime',
            'userid',
            'remark',
            'contact',
            'regionName',
            'ip'
          )
        )
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    //获取运维人员列表
    getUsersList() {
      userList().then((res) => {
        if (res.success) {
          this.usersList = res.result
        } else {
          this.$message.error('获取运维人员列表失败，请刷新后重试！')
        }
      })
    },

    handleOk() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let formData = Object.assign(this.model, values)
          //分配后，变成待处理工单
          formData.status = '待处理'
          // formData.userid = that.userid;
          editRecord(formData)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
              that.close()
            })
        }
      })
    },
    handleCancel() {
      this.close()
    },
  },
}
</script>
<style scoped>
.border_color {
  border-color: #d3d3d3;
}
</style>
<style lang="less" scoped>
::v-deep .ant-modal-body {
  padding: 24px 48px 24px 48px;
}
::v-deep .ant-modal {
  padding: 24px;
}
@media (max-width: 712px) {
  ::v-deep .ant-modal {
    max-width: calc(100vw - 12px);
    margin: 0;
  }
}
</style>
