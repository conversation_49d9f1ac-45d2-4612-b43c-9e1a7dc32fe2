<template>
  <div class='zr-rank-chart' ref='rankChart' :style='{height: chartH }'></div>
</template>
<script>
import resizeObserverMixin from '@views/statsCenter/com/resizeObserverMixin'
export default {
  name: 'ZrRankBar',
  mixins: [resizeObserverMixin],
  props: {
    ranking: {
      type: Boolean,
      default: false
    },
    singleH: {
      type: Number,
      default: 0
    },
    rankData: {
      type: Array,
      default: () => []
    },
    labelWidth: {
      type: Number,
      default: 100
    }
  },
  data() {
    return {
      chart: null,
      dangerColors: ['#FFFFFF', '#FE4F3D'],
      warningColors: ['#FFFFFF', '#FFAE38'],
      defaultColors: ['#89F7FE', '#1C7BFF'],
      chartH: '100%',
      hScale:1,
    }
  },
  created() {
    // this.hScale = Math.min(window.innerHeight / 1080,1)
    // this.chartH = this.singleH ? this.singleH * this.hScale * this.rankData.length + 'px' : '100%'
  },
  mounted() {
    this.initRankChart()
  },
  computed: {
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
    }
  },
  methods: {
    initRankChart() {
      if (this.chart) {
        this.chart.dispose()
      }
      this.chart = this.$echarts.init(this.$refs.rankChart)
      let that = this
      // 初始化排名图表
      // 这里可以添加图表初始化的逻辑
      let xdata = this.rankData.map(item => item.name)
      let sdata = this.rankData.map(item => item.score)
      let ldata = this.rankData.map(item => item.level)
      let styleData = this.rankData.map(item => {
        let colors = that.defaultColors
        if (item.level === 'D' && !that.ranking) {
          colors = that.dangerColors
        } else if (item.level === 'C' && !that.ranking) {
          colors = that.warningColors
        }
        return {
          value: item.score,
          itemStyle: {
            color: that.getLinearGradient(colors)
          }
        }
      })
      let option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'none'
          },
          formatter: function(params) {
            let index = params[0].dataIndex
            return xdata[index] + '<br/>' +
              '分数：' + sdata[index] + '分 | ' +
              '等级：' + ldata[index]
          }
        },
        grid: {
          containLabel: true,
          bottom: 0,
          left: 0,
          top: 0,
          right: 0
        },
        xAxis: {
          type: 'value',
          axisLabel: {
            show: false
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: false
          }
        },
        yAxis: [
          {
            type: 'category',
            data: xdata,
            inverse: true,
            axisLabel: {
              fontSize: 14,
              inside: false,
              verticalAlign: 'center',
              padding: [5, 0, 0, 0],
              margin: 20,
              color: '#ffffff',
              formatter: function(value, index) {
                if (!that.ranking) {
                  let str = xdata[index]
                  return str.length > 6 ? `${xdata[index].substring(0, 6)}...` : `${xdata[index]}`
                }
                if (index === 0) {
                  return `{a|T${index + 1}} ${xdata[index]}`
                } else if (index === 1) {
                  return `{b|T${index + 1}} ${xdata[index]}`
                } else if (index === 2) {
                  return `{c|T${index + 1}} ${xdata[index]}`
                } else if (index === 3) {
                  return `{d|T${index + 1}} ${xdata[index]}`
                } else {
                  return `{e|T${index + 1}} ${xdata[index]}`
                }
              },
              rich: {
                a: {
                  color: '#DE1515',
                  fontSize: 12
                }, b: {
                  color: '#FFAD0E',
                  fontSize: 12
                },
                c: {
                  color: '#40A7FF',
                  fontSize: 12
                },
                d: {
                  color: '#72F721',
                  fontSize: 12
                },
                e: {
                  color: '#ffffff',
                  fontSize: 12
                }
              }
            },
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: false,
              lineStyle: {
                color: '#13387a'
              }
            }
          },
          {
            type: 'category',
            data: xdata,
            inverse: true,
            axisLabel: {
              inside: false,
              color: 'rgba(255,255,255,0.8)',
              verticalAlign: 'center',
              padding: [5, 0, 0, 0],
              margin: 10,
              formatter: function(value, index) {
                if (that.ranking) {
                  return `${sdata[index]}分/等级${ldata[index]}`

                } else {
                  return `{a|${ldata[index]}}  ${sdata[index]}分`
                }
              },
              rich: {
                a: {
                  color: '#ffffff',
                  fontSize: 14
                }
              }
            },
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: false,
              lineStyle: {
                color: '#13387a'
              }
            }
          }
        ],
        series: [
          {
            name: '评估分类排名',
            type: 'bar',
            barWidth: 9,
            showBackground: true,
            itemStyle: {
              barBorderRadius: 10
            },
            backgroundStyle: {
              color: 'rgba(156,205,255,0.1)',
              barBorderRadius: 10
            },
            data: styleData

          }
        ]
      }
      this.chart.setOption(option)
    },
    getLinearGradient(colors) {
      return new this.$echarts.graphic.LinearGradient(0, 0, 1, 0, [
        {
          offset: 0,
          color: colors[0]
        },
        {
          offset: 1,
          color: colors[1]
        }
      ])
    },
    resizeObserverCb() {
      this.initRankChart()
    }
  }
}
</script>
<style scoped lang='less'>
.zr-rank-chart {
  width: 100%;
  height: 100%;
}
</style>