<template>
  <a-popover v-model="visible" :arrowPointAtCenter="true" :autoAdjustOverflow="true"
    :overlayStyle="{ width: '360px', top: '50px' }" overlayClassName="och-notice-filter" placement="bottomRight"
    trigger="click" @visibleChange="handleHoverChange">
    <template slot="content">
      <a-spin :spinning="loadding">
        <a-tabs>
          <div slot="tabBarExtraContent" style="padding: 6px 0 0 0">
            <yq-icon :key="audioIconKey" :type="iconName" style="font-size: 22px; cursor: pointer; color: #fff"
              @click.native="changeTipStatus" />
          </div>

          <a-tab-pane key="2" :tab="msg2Title">
            <a-list>
              <a-list-item v-for="(record, index) in announcement2" :key="index" @click="showAnnouncement(record)">
                <a style="display: flex; width: 100%; align-items: center">
                  <div style="
                      text-align: center;
                      color: #fff;
                      background: #fff;
                      box-shadow: 0px 0px 3px 1px #c9c9c9;
                      margin-left: 3px;
                      height: 40px;
                    ">
                    <div style="padding: 2px 2px">
                      <div v-if="record.priority === 'L'" style="padding: 8px 11px; background: #7da1ff">低</div>
                      <div v-else-if="record.priority === 'M'" style="padding: 8px 11px; background: #ffa471">中</div>
                      <div v-else-if="record.priority === 'H'" style="padding: 8px 11px; background: #ff7d7d">高</div>
                    </div>
                  </div>
                  <div style="margin-left: 12px; width: 57%">
                    <p style="margin-bottom: 4px;">
                      <a style="color:#fff">{{ record.titile || record.title }}</a>
                    </p>
                    <p style="color: rgba(255, 255, 255, 0.45); margin-bottom: 0px">请进入消息中心查看</p>
                  </div>
                  <div>
                    <p v-if="record.createTime" style="margin-bottom: 4px;color:#fff">{{ record.createTime.slice(0, 10)
                      }}</p>
                    <p style="color: rgba(255, 255, 255, 0); margin-bottom: 0px" title="请进入待办栏，尽快处理！">
                      请进入待...
                    </p>
                  </div>
                </a>
              </a-list-item>
              <div style="margin-top: 11px; text-align: center">
                <a-button block type="primary" @click="toMyAnnouncement">查看更多</a-button>
              </div>
            </a-list>
          </a-tab-pane>

          <!-- <a-tab-pane key="3" :tab="alarmTitle">
            <a-list>
              <a-list-item v-for="(items, index) in alarmList" :key="index" @click="showAlarm(items, index)">
                <a style="display: flex; width: 100%; align-items: center">
                  <div
                    style="
                      text-align: center;
                      color: #fff;
                      background: #fff;
                      box-shadow: 0px 0px 3px 1px #c9c9c9;
                      margin-left: 3px;
                      height: 40px;
                      width: 50px;
                    "
                  >
                    <div style="padding: 0 8px; background: #7da1ff">{{ items.level_text }}</div>
                  </div>
                  <div style="margin-left: 12px; width: 57%">
                    <p style="margin-bottom: 4px">
                      <a>{{ items.title }}</a>
                    </p>
                    <p style="color: rgba(0, 0, 0, 0.45); margin-bottom: 0px">请进入待办栏，尽快处理！</p>
                  </div>
                  <div>
                    <p v-if="items.sendTime" style="margin-bottom: 4px">{{ items.sendTime.slice(0, 10) }}</p>
                    <p style="color: rgba(0, 0, 0, 0.45); margin-bottom: 0px" title="请进入待办栏，尽快处理！">
                      请进入待...
                    </p>
                  </div>
                </a>
              </a-list-item>
              <div style="margin-top: 11px; text-align: center">
                <a-button block type="primary" @click="toMyAnnouncement">查看更多</a-button>
              </div>
            </a-list>
          </a-tab-pane> -->

          <a-tab-pane key="4" :tab="msg1Title">
            <a-list>
              <a-list-item v-for="(record, index) in announcement1" :key="index" @click="showAnnouncement(record)">
                <a style="display: flex; width: 100%; align-items: center">
                  <div style="
                      text-align: center;
                      color: #fff;
                      background: #fff;
                      box-shadow: 0px 0px 3px 1px #c9c9c9;
                      margin-left: 3px;
                      height: 40px;
                    ">
                    <div style="padding: 2px 2px">
                      <div v-if="record.priority === 'L'" style="padding: 8px 11px; background: #7da1ff">低</div>
                      <div v-else-if="record.priority === 'M'" style="padding: 8px 11px; background: #ffa471">中</div>
                      <div v-else-if="record.priority === 'H'" style="padding: 8px 11px; background: #ff7d7d">高</div>
                    </div>
                  </div>
                  <div style="margin-left: 12px; width: 57%">
                    <p style="margin-bottom: 4px">
                      <a style="color:#fff">{{ record.titile || record.title }}</a>
                    </p>
                    <p style="color: rgba(255, 255, 255, 0.45); margin-bottom: 0px">请进入消息中心查看</p>
                  </div>
                  <div>
                    <p v-if="record.createTime" style="margin-bottom: 4px;color:#fff">{{ record.createTime.slice(0, 10)
                      }}</p>
                    <p style="color: rgba(0, 0, 0, 0); margin-bottom: 0px" title="请进入待办栏，尽快处理！">
                      请进入待...
                    </p>
                  </div>
                </a>
              </a-list-item>
              <div style="margin-top: 11px; text-align: center">
                <a-button block type="primary" @click="toMyAnnouncement">查看更多</a-button>
              </div>
            </a-list>
          </a-tab-pane>
        </a-tabs>
      </a-spin>
    </template>
    <span class="header-notice" @click="fetchNotice" title='消息'>
      <audio id="musicTips" ref="musicTips" :autoplay="isAutoPlay" controls muted="true" :src="audioUrl"
        style="width: 0px; height: 0px"></audio>
      <a-badge :count="msgTotal">
        <a-icon type="bell" class="icon-item" />
      </a-badge>
    </span>
    <show-announcement ref="ShowAnnouncement" @ok="modalFormOk"></show-announcement>
    <dynamic-notice ref="showDynamNotice" :formData="formData" :path="openPath" />
  </a-popover>
</template>

<script>
import { getAction, putAction } from '@/api/manage'
import ShowAnnouncement from '@/components/tools/ShowAnnouncement'
import store from '@/store/'
import yqIcon from '@/components/tools/SvgIcon'
import DynamicNotice from '@/components/tools/DynamicNotice'
import Vue from 'vue'
import { ACCESS_TOKEN, PLATFORM_TYPE } from '@/store/mutation-types'
import { WebsocketMixin } from '@/mixins/WebsocketMixin'
import WebsocketMessageMixin from '@/mixins/WebsocketMessageMixin'
let curNode = null
export default {
  name: 'OchHeaderNotice',
  components: {
    yqIcon,
    DynamicNotice,
    ShowAnnouncement,
  },
  mixins: [WebsocketMixin, WebsocketMessageMixin],
  data() {
    return {
      loadding: false,
      url: {
        listCementByUser: '/sys/annountCement/listByUser',
        editCementSend: '/sys/sysAnnouncementSend/editByAnntIdAndUserId',
        queryById: '/sys/annountCement/queryById',
      },
      hovered: false,
      announcement1: [],
      announcement2: [],
      alarmList: [],
      routerInfoList: [
        {
          routerName: 'isps-userAnnouncement',
          redirect: '/isps/userAnnouncement',
          component: 'system/UserAnnouncementList',
          params: { msgCategory: 2 },
        },
        {
          routerName: 'alarmManage-assetsAlarm-AssetsAlarmManage',
          redirect: '/alarmManage/assetsAlarm/AssetsAlarmManage',
          component: 'alarmManage/assetsAlarm/AssetsAlarmManage',
          params: null,
        },
        {
          routerName: 'downloadManagement-downloadManagementList',
          redirect: '/downloadManagement/downloadManagementList',
          component: 'downloadManagement/downloadManagementList',
          params: null,
        },
        {
          routerName: 'isps-userAnnouncement',
          redirect: '/isps/userAnnouncement',
          component: 'system/UserAnnouncementList',
          params: { msgCategory: 1 },
        },
      ],
      msg1Count: '0',
      msg2Count: '0',
      alarmCount: '0',
      msg1Title: '通知公告(0)',
      msg2Title: '系统消息(0)',
      alarmTitle: '告警通知(0)',
      stopTimer: false,
      websock: null,
      lockReconnect: false,
      heartCheck: null,
      formData: {},
      openPath: '',
      visible: false,
      noticeTimer: null,

      alarmTimer: null,
      audioUrl: '',
      musicUrl: window._CONFIG['staticDomainURL'],
      iconName: '',
      isAutoPlay: false,
      audioIconKey: 0,
      reTimer: null,
      noticeKey: "ochHeaderNotice",
    }
  },
  computed: {
    msgTotal() {
      return this.msg2Count + this.msg1Count
    },
  },
  created() {
    this.getIconName()
  },
  mounted() {
    curNode = this
    this.addAudio()
    this.loadData()
    // this.timerFun()
  },
  destroyed: function () {
    // 离开页面生命周期函数
    clearTimeout(this.alarmTimer)
    clearInterval(this.noticeTimer)
    this.noticeTimer = null
  },
  methods: {
    addAudio() {
      this.audio = document.getElementById('musicTips')
      this.audio.muted = true
      this.audio.pause()
    },
    getIconName() {
      var key = 'headerNotice:musicTips'
      let name = this.$ls.get(key, this.iconName)
      if (name) {
        this.iconName = name
        this.isAutoPlay = this.iconName === 'notice_play' ? true : false
      } else {
        this.iconName = 'notice_play'
        this.isAutoPlay = true
      }
    },
    changeTipStatus() {
      this.audio.pause()
      this.audioUrl = ''
      this.isAutoPlay = !this.isAutoPlay
      this.audio.muted = !this.isAutoPlay
      this.iconName = this.isAutoPlay ? 'notice_play' : 'notice_pause'
      var key = 'headerNotice:musicTips'
      this.$ls.set(key, this.iconName)
      ++this.audioIconKey
    },
    playMusicTips() {
      if (this.isAutoPlay && this.audio && this.audioUrl) {
        this.audio.muted = false
        this.audio.load()
        this.audio.play()
      }
    },
    getAlarmList() {
      sessionStorage.setItem('alarmList', JSON.stringify(store.getters.alarmInfo))
      curNode.alarmList = JSON.parse(sessionStorage.getItem('alarmList'))
      curNode.alarmCount = store.getters.alarmInfo.length
      curNode.alarmTitle = '告警通知(' + store.getters.alarmInfo.length + ')'
    },
    timerFun() {
      this.stopTimer = false
      if (this.noticeTimer === null) {
        this.noticeTimer = setInterval(() => {
          // 停止定时器
          if (this.stopTimer == true) {
            clearInterval(this.noticeTimer)
            this.noticeTimer = null
            return
          }
          this.loadData()
        }, 6000)
      }
    },
    loadData() {
      if (!!sessionStorage.getItem('alarmList')) {
        this.alarmList = JSON.parse(sessionStorage.getItem('alarmList'))
        this.alarmTitle = '告警通知(' + this.alarmList.length + ')'
        this.alarmCount = this.alarmList.length
      }
      try {
        // 获取系统消息
        getAction(this.url.listCementByUser)
          .then((res) => {
            if (res.success) {
              this.announcement1 = res.result.anntMsgList
              this.msg1Count = res.result.anntMsgTotal
              this.msg1Title = '通知公告(' + (this.msg1Count < 0 ? 0 : this.msg1Count) + ')'

              this.announcement2 = res.result.sysMsgList
              this.msg2Count = res.result.sysMsgTotal
              this.msg2Title = '系统消息(' + this.msg2Count + ')'

              let currNoticeCount = parseInt(this.msg2Count) + parseInt(this.msg1Count)
              if (currNoticeCount > 0 && currNoticeCount > this.befNoticeCount) {
                this.$refs.musicTips.src = require('@/assets/music/daiban.mp3')
                this.audioUrl = '@/assets/music/daiban.mp3'
                this.playMusicTips()
              }
              this.befNoticeCount = parseInt(this.msg2Count) + parseInt(this.msg1Count)
            }
          })
          .catch((error) => {
            this.stopTimer = true
          })
      } catch (err) {
        this.stopTimer = true
      }
    },
    fetchNotice() {
      if (this.loadding) {
        this.loadding = false
        return
      }
      this.loadding = true
      setTimeout(() => {
        this.loadding = false
      }, 200)
    },
    showAlarm(record, index) {
      this.visible = false
      this.hovered = false
      if (record.openType === 'component') {
        this.openPath = record.openPage
        this.formData = {
          id: record.busId,
        }
        this.$refs.showDynamNotice.detail(record.openPage)
      } else {
        this.$refs.ShowAnnouncement.detail(record)
      }
      this.alarmList.splice(index, 1)
      this.alarmCount = this.alarmList.length
      this.alarmTitle = '告警通知(' + this.alarmCount + ')'
      sessionStorage.setItem('alarmList', JSON.stringify(this.alarmList))
      store.dispatch('updateDataList', index)
    },
    showAnnouncement(record) {
      this.visible = false
      putAction(this.url.editCementSend, {
        anntId: record.id,
      }).then((res) => {
        if (res.success) {
          this.loadData()
        }
      })
      this.hovered = false
      this.$router.push({ path: '/oneClickHelp/messageCenter', query: this.$route.query })
    },
    toMyAnnouncement() {
      curNode.visible = false
      this.$router.push({ path: '/oneClickHelp/messageCenter', query: this.$route.query })
    },
    modalFormOk() { },
    handleHoverChange(visible) {
      this.hovered = visible
    },
    /*
     *statusChange 设备在线离线推送消息处理统一在WebsocketMixn.js中处理
     * alarmStatus 设备告警状态推送统一在WebsocketMixn.js中处理
     * alarmClear 清除拓扑图告警统一在WebsocketMixn.js中处理
     */
    websocketOnmessage: function (e) {
      if (e.data === 'HeartBeat') {
        return
      }
      let that = this
      var data = eval('(' + e.data + ')')

      this.$notification.close(this.noticeKey)
      let title = '消息提醒'
      let content = '新的消息通知'
      if (data.cmd == 'user') {
        content = data.msgTxt
      } else if (data.messageType == 'alarm') {
        title = data.data.title
        content = data.data.msgContent
        return;
      } else if (data.messageType === 'notice') {
        title = data.data.titile
        content = data.data.msgContent
        if (data.data.msgCategory == 1) {
          this.announcement1.push(data.data);
          this.msg1Count += 1;
          this.msg1Title = "通知公告(" + (this.msg1Count < 0 ? 0 : this.msg1Count) + ")";
        } else if (data.data.msgCategory == 2) {
          this.announcement2.push(data.data);
          this.msg2Count += 1
          this.msg2Title = '系统消息(' + this.msg2Count + ')'
        }
        // this.playVoice()
      } else if (data.messageType == 'clearNotice') {
        //撤销发送给全体用户的系统消息和通知公告
        let msgData = data.data;
        if (msgData === undefined) {
          this.announcement1 = []
          this.announcement2 = []
          this.msg1Count = 0;
          this.msg2Count = 0;
          this.msg1Title = "通知公告(" + (this.msg1Count < 0 ? 0 : this.msg1Count) + ")";
          this.msg2Title = '系统消息(' + this.msg2Count + ')'
        } else {
          let idx1 = this.announcement1.findIndex(el => el.id == msgData.id)
          let idx2 = this.announcement2.findIndex(el => el.id == msgData.id)
          if (idx1 !== -1) {
            this.announcement1.splice(idx1, 1);
            this.msg1Count -= 1;
            this.msg1Title = "通知公告(" + (this.msg1Count < 0 ? 0 : this.msg1Count) + ")";
          } else if (idx2 !== -1) {
            this.announcement2.splice(idx2, 1);
            this.msg2Count -= 1
            this.msg2Title = '系统消息(' + this.msg2Count + ')'
          }
          return
        }
      }
      else if (['statusChange', 'alarmStatus', 'portStatus', 'alarmClear'].includes(data.messageType)) {
        return
      }
      this.$notification.open({
        message: title,
        description: h => {
          return h('div', null, [
            h('div', { attrs: { style: 'margin:0px 0px;max-height:calc(100vh * 0.7);overflow:auto', } }, [h("p", { domProps: { innerHTML: content } }, null)])
          ])
        },
        placement: 'bottomRight',
        key: this.noticeKey,
        duration: 30,
        class: 'yq-och-notice',
        onClick: (e) => {
          this.$router.push({ path: '/oneClickHelp/messageCenter', query: this.$route.query })
          this.$notification.close(this.noticeKey)
        },
      })
    },

    showDetail(key, data) {
      this.$notification.close(key)
      let id = data.msgId
      getAction(this.url.queryById, {
        id: id,
      }).then((res) => {
        if (res.success) {
          let record = res.result
          this.showAnnouncement(record)
        }
      })
    },
  },
}
</script>

<style lang='css' scoped>
.och-notice-filter {
  top: 50px !important;
}

.icon-item {
  font-size: 15px;
}

.icon-item:hover {
  color: #66ffff;
}

img {
  width: 14px;
  height: 14px;
}
</style>
<style lang='less' scoped>
.header-notice {
  display: inline-block;
  transition: all 0.3s;

  span {
    vertical-align: initial;
  }
}

.ant-list-item {
  padding: 17px 0 6px !important;
}
</style>