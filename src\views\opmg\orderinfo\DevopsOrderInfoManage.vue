<template>
  <div style="height:100%">
    <keep-alive >
      <component :is="pageName" :data="data"/>
    </keep-alive>
  </div>
</template>
<script>
  import DevopsOrderInfoList from './DevopsOrderInfoList'
  import OrderInfoForm from './modules/OrderInfoForm'
  export default {
    name: "DevopsOrderInfoManage",
    data() {
      return {
        isActive: 0,
        data:{}
      };
    },
    components: {
      DevopsOrderInfoList,
      OrderInfoForm
    },
    created(){
      this.pButton1(0);
    },
    //使用计算属性
    computed: {
      pageName() {
        switch (this.isActive) {
          case 0:
            return "DevopsOrderInfoList";
            break;

          default:
            return "OrderInfoForm";
            break;
        }
      }
    },
    methods: {
      pButton1(index) {
        this.isActive = index;
      },
      pButton2(index,item) {
        this.isActive = index;
        this.data = item;
      }
    }
  }
</script>