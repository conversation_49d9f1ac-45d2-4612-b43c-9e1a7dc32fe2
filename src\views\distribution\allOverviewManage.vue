<template>
  <div style="height:100%">
    <component style="height:100%" :is="pageName" :data="data" />
  </div>
</template>
<script>
  import nodeMonitorDetail from './modules/nodeMonitorDetail'
  import allOverviewList from './allOverviewList'
  export default {
    name: "allOverviewManage",
    data() {
      return {
        isActive: 0,
        data: {},
      };
    },
    components: {
      nodeMonitorDetail,
      allOverviewList
    },
    created() {
      this.pButton1(0);
    },
    //使用计算属性
    computed: {
      pageName() {
        switch (this.isActive) {
          case 0:
            return "allOverviewList";
          default:
            return "nodeMonitorDetail";
        }
      }
    },
    methods: {
      pButton1(index, item) {
        this.isActive = index;
        this.data = item;
      },
      pButton2(index, item) {
        this.isActive = index;
        this.data = item
      }
    }
  }
</script>