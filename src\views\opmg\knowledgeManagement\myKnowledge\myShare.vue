<template>
  <div style="margin-top:15px;">
    <!-- 查询区域 -->
    <div class='table-page-search-wrapper'>
      <a-form layout='inline'
        @keyup.enter.native='searchQuery'>
        <a-row :gutter='24' ref='row'>
          <a-col :span='spanValue'>
            <a-form-item label='标题'>
              <a-input placeholder='请输入标题'
                v-model='queryParam.title'
                :allowClear='true'
                autocomplete='off' :maxLength="maxLength"/>
            </a-form-item>
          </a-col>
          <a-col :span='colBtnsSpan()'>
            <span class='table-page-search-submitButtons'
              :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
              <a-button type='primary'
                class='btn-search btn-search-style'
                @click='searchQuery'>查询</a-button>
              <a-button class='btn-reset btn-reset-style'
                @click='searchReset'>重置</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <div class='table-operator table-operator-style'>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay" style='text-align: center'>
          <a-menu-item key="1" @click="batchDel">删除</a-menu-item>
        </a-menu>
        <a-button>批量操作
          <a-icon type="down" />
        </a-button>
      </a-dropdown>
    </div>
    <a-table ref="table"
      bordered
      :rowKey="(record)=>{return record.shareId}"
      :columns="columns"
      :dataSource="dataSource"
      :pagination="ipagination"
      :loading="loading"
      :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      class="j-table-force-nowrap"
      @change="handleTableChange">

      <span slot="shareStatus" slot-scope="text, record">
        <span :style='{color:text?"#ff0000":"#4BD863"}'>{{!!text?'已失效':'未失效'}}</span>
      </span>

      <span slot="action"
        slot-scope="text, record"
        class="caozuo">
        <a @click="handleDetailPage(record)">查看</a>
        <span v-if='!record.shareExpired'>
          <a-divider type='vertical' />
          <a @click="handleSharing(record)">链接</a>
        </span>

        <a-divider type='vertical' />
         <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.shareId)">
           <a>删除</a>
         </a-popconfirm>
      </span>

      <template slot='tooltip'
        slot-scope='text'>
        <a-tooltip placement='topLeft'
          :title='text'
          trigger='hover'>
          <div class='tooltip'>
            {{ text }}
          </div>
        </a-tooltip>
      </template>

      <template slot='knowledgeType' slot-scope='text'>
        <knowledge-icon :knowledgeType="text"></knowledge-icon>
      </template>
    </a-table>
    <add-share-modal ref='addShareModal'></add-share-modal>
  </div>
</template>

<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
import addShareModal from '@views/opmg/knowledgeManagement/knowledgeBase/modules/AddShareModal.vue'
import knowledgeIcon from '@views/opmg/knowledgeManagement/knowledgeBase/modules/KnowledgeIcon.vue'
export default {
  name: 'myShare',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components:{
    addShareModal,
    knowledgeIcon
  },
  data() {
    return {
      maxLength:50,
      columns: [
        {
          title: '标题',
          dataIndex: 'title',
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'tooltip'
          }
        },
        {
          title: '主题名称',
          dataIndex: 'topicName',
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'tooltip'
          }
        },
        {
          title: '知识类型',
          dataIndex: 'knowledgeType',
          customCell: () => {
            let cellStyle = 'width:80px'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'knowledgeType'
          }
        },
        {
          title: '创建人员',
          dataIndex: 'createBy'
        },
        {
          title: '分享时间',
          dataIndex: 'shareTime'
        },
        {
          title: '失效时间',
          dataIndex: 'shareExpiredTime',
          customRender: function (text) {
            return !text ? '永久有效':text
          }
        },
        {
          title: '失效状态',
          dataIndex: 'shareExpired',
          customCell: () => {
            let cellStyle = 'text-align: center;width: 120px'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'shareStatus'
          }
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 150,
          scopedSlots: {
            customRender: 'action'
          }
        }
      ],
      url: {
        list: '/kbase/knowledges/usershared',
        delete:'/kbase/knowledges/share/delete',
        deleteBatch:'/kbase/knowledges/share/deleteBatch'
      },
    }
  },
  activated() {
    console.log('激活我的分享')
    this.loadData()
  },
  methods: {
    // 查看
    handleDetailPage: function (record) {
      this.$emit('getRecord', record)
    },
    handleSharing(record){
      this.$refs.addShareModal.edit(record)
      this.$refs.addShareModal.title='知识分享'
      this.$refs.addShareModal.disableSubmit=false
    }
  }
}

</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
</style>
