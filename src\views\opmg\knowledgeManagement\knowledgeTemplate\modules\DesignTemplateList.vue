<template>
  <a-card :bordered="false" :bodyStyle="{ padding:'0'}">
    <!-- 操作按钮区域 -->
    <div class="table-operator table-operator-style">
      <a-button @click="handleAdd">新增</a-button>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel" style='text-align: center'>删除</a-menu-item>
        </a-menu>
        <a-button> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <a-table
        ref="table"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        @change="handleTableChange"
      >
        <span slot="action" slot-scope="text, record">
          <a @click="handleDetail(record)" style="color: #409eff">查看</a>
          <a-divider type="vertical" />
          <a @click="handleEdit(record)" style="color: #409eff">编辑</a>
          <a-divider type="vertical" />
          <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
            <a style="color: #409eff">删除</a>
          </a-popconfirm>
        </span>
      </a-table>
    </div>
    <design-template-field-modal ref="modalForm" @ok="modalFormOk"></design-template-field-modal>
  </a-card>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import DesignTemplateFieldModal from './DesignTemplateFieldModal'
import JSuperQuery from '@/components/jeecg/JSuperQuery.vue'
import { httpAction, getAction, deleteAction } from '@/api/manage'

export default {
  name: 'DesignTemplateList',
  mixins: [JeecgListMixin],
  components: {
    DesignTemplateFieldModal,
    JSuperQuery,
    getAction,
  },
  data() {
    return {
      description: '设计模版的字段管理页面',
      // 表头
      columns: [
        {
          title: '序号',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function (t, r, index) {
            return parseInt(index) + 1
          },
        },
        {
          title: '字段标识',
          dataIndex: 'code',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 50px;max-width:300px'
            return { style: cellStyle }
          },
        },
        {
          title: '字段名称',
          dataIndex: 'name',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 50px;max-width:300px'
            return { style: cellStyle }
          },
        },
        {
          title: '字段类型',
          dataIndex: 'type',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 50px;max-width:300px'
            return { style: cellStyle }
          },
        },
        {
          title: '字段描述',
          dataIndex: 'description',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 50px;max-width:400px'
            return { style: cellStyle }
          },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 147,
          scopedSlots: { customRender: 'action' },
        },
      ],
      url: {
        list: '/extendField/extendField/listAll',
        // delete: '/extendField/extendField/delete',
        // deleteBatch: '/extendField/extendField/deleteBatch',
      },
      disableMixinCreated:true
    }
  },
  methods: {
    edit(record) {
      this.loadData()
    },
    handleAdd: function () {
      this.$refs.modalForm.add()
      this.$refs.modalForm.title = '新增'
      this.$refs.modalForm.disableSubmit = false
    }
  }
}
</script>
<style lang="less" scoped>
@import '~@assets/less/common.less';
</style>
