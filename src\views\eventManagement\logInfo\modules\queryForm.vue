<template>
  <div>
    <a-spin :spinning="confirmLoading">
      <div class='table-page-search-wrapper header-wrapper'>
        <a-form-model
          ref='form'
          layout='inline'
          :model='model'
          :rules='validatorRules'
          :labelCol='labelCol'
          :wrapperCol='wrapperCol'
        >
          <a-row :gutter='[12,0]'>
            <a-col :span='24'>
              <a-form-model-item label='标签' prop='alias'>
                <a-input v-model='model.alias' placeholder='请输入标签' />
              </a-form-model-item>
            </a-col>
            <a-col :span='15'>
              <a-form-model-item label='字段'  prop='field' ref='field' :auto-link='false'>
                <a-input-group compact>
                  <a-select
                    v-model='model.field'
                    style='width: calc(100% - 80px)'
                    :allow-clear='true'
                    show-search
                    placeholder='请选择字段'
                    option-filter-prop='children'
                    :optionLabelProp='"label"'
                    :filter-option='filterOption'
                    :getPopupContainer='(node) => node.parentNode'
                    @blur="() => {$refs.field.onFieldBlur()}"
                    @change='changeField'>
                    <a-select-option  v-for='(item,index) in fieldList' :key='"field_"+index' :title='item.field' :value='item.field'
                                      :label='item.field'>
                      {{ item.field }}
                    </a-select-option>
                  </a-select>
                  <a-input class='field-type-disabled' :title='model.fieldType' :disabled='true' placeholder='字段类型' v-model='model.fieldType'/>
                </a-input-group>
              </a-form-model-item>
            </a-col>
            <a-col :span='9'>
              <a-form-model-item label='运算符' prop='operator' ref='operator' :auto-link='false'>
                <div>
                  <a-select
                    v-model='model.operator'
                    :allow-clear='true'
                    style='width:calc(100% - 32px)'
                    placeholder='请选择运算符'
                    :getPopupContainer='(node) => node.parentNode'
                    @blur="() => {$refs.operator.onFieldBlur()}"
                    @change='changeOperator'>
                    <a-select-option v-for='(item,index) in operatorList' :key='"operator_"+index'
                                     :value='item.key' :label='item.label'>
                      {{ item.label }}
                    </a-select-option>
                  </a-select>
                  <a-popover title='运算符说明' placement="top">
                    <template slot='content'>
                      <div v-if='model.operator === "equal" || model.operator==="notEqual"'>
                        <div>是、不是：匹配指定短语；例如:</div>
                        <ul>
                          <li>active：指定字段包含/不包含active</li>
                          <li>"John Smith"：指定字段包含/不包含确切的短语"john smith"。即把"john smith"视作一个整体，<br/>对于"I'm john smith"和"My name is John, last name Smith"会匹配到"I'm john smith"。</li>
                          <li>(quick OR brown)：指定字段包含/不包含quick或brown。</li>
                        </ul>
                        <div>日期格式使用yyyy-MM-dd hh:mm:ss，因为有一个空格，所以需要使用""包括，使其变为一个整体。</div>
                      </div>
                      <div v-if='model.operator==="orEqual"||model.operator==="orNotEqual"'>
                        <div>属于、不属于：匹配一个或多个短语,相当于"是、不是"运算符中的(quick OR brown)；例如:</div>
                        <ul>
                          <li>active：指定字段包含/不包含active</li>
                          <li>"John Smith"：指定字段包含/不包含确切的短语"john smith"。即把"john smith"视作一个整体，<br/>对于"I'm john smith"和"My name is John, last name Smith"会匹配到"I'm john smith"。</li>
                        </ul>
                        <div>日期格式使用yyyy-MM-dd hh:mm:ss，因为有一个空格，所以需要使用""包括，使其变为一个整体。</div>
                      </div>
                      <div v-if='model.operator==="withinRange"||model.operator==="outsideRange"'>
                        <div>介于、不介于：匹配区间范围值；例如:</div>
                        <ul>
                          <li>[：闭区间，包含边界值</li>
                          <li>{：开区间，不包含边界值</li>
                        </ul>
                      </div>
                      <div v-if='model.operator === "exist" || model.operator==="notExist"'>
                        存在、不存在：是否存在或不存在任何非null值。
                      </div>
                      <div>AND，OR，TO为关键字，且必须为大写</div>
                    </template>
                    <a-icon style='font-size: 20px;height: 32px; line-height: 32px;margin-left: 10px' theme='twoTone' type='question-circle' />
                  </a-popover>
                </div>
              </a-form-model-item>
            </a-col>

            <a-col :span='24' v-if='model.operator==="equal"||model.operator==="notEqual"'>
              <a-form-model-item label='值' prop='value'>
                <a-input
                  v-model="model.value"
                  style="width: 100%"
                  placeholder="请输入值"
                  :getPopupContainer='(node) => node.parentNode'
                />
              </a-form-model-item>
            </a-col>
            <a-col :span='24' v-if='model.operator==="orEqual"||model.operator==="orNotEqual"'>
              <a-form-model-item label='值' prop='value'>
                <a-select
                  v-model='model.value'
                  mode="tags"
                  style="width: 100%"
                  placeholder="输入完成后回车创建"
                  :getPopupContainer='(node) => node.parentNode'
                  @change="changeSelectedTags">
                  <a-select-option v-for="(item,index) in selectedTags" :key="'selectedTags_' + index">
                    {{item }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span='24' v-if='model.operator==="withinRange"||model.operator==="outsideRange"'>
              <a-form-model-item label='值'>
                <div style='display: flex;flex-flow: row nowrap;justify-content: space-between'>
                  <a-form-model-item prop='value1' style='width:calc((100% - 40px) / 2)'>
                    <a-input-group compact>
                      <a-select v-model='model.symbol1' style='width: 50px'>
                        <a-select-option value="[" label='['>[</a-select-option>
                        <a-select-option value="{" label='{'>{</a-select-option>
                      </a-select>
                      <a-input style='width: calc(100% - 50px);' v-model='model.value1' placeholder="请输入范围开始值"/>
                    </a-input-group>
                  </a-form-model-item>
                  <div style='padding: 0 12px'>T0</div>
                  <a-form-model-item prop='value2' style='width:calc((100% - 40px) / 2)'>
                    <a-input-group compact>
                      <a-input style='width: calc(100% - 50px);' v-model='model.value2'  placeholder="请输入范围结束值"/>
                      <a-select v-model='model.symbol2' style='width: 50px'>
                        <a-select-option value="]" label=']'>]</a-select-option>
                        <a-select-option value="}" label='}'>}</a-select-option>
                      </a-select>
                    </a-input-group>
                  </a-form-model-item>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span='24' class='form-btn-wrapper'>
              <a-form-model-item class='cancel-item' style='margin-bottom: 0'>
                <a-button type='default' @click='cancelQuery'>取消</a-button>
              </a-form-model-item>
              <a-form-model-item class='submit-item'>
                <a-button type='primary' @click='submitQuery' class='submit'>{{btnName}}</a-button>
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </div>
    </a-spin>
  </div>
</template>
<script>
import { getAction, httpAction } from '@api/manage'
import moment from "moment";
import {opertionTips} from '@views/eventManagement/logInfo/modules/status'
import Log from 'echarts/src/scale/Log'

export default {
  name: "queryForm",
  props: {
    //日志来源id
    logAnalyzeId: {
      type: String,
      required: false,
      default: ''
    },
    //查询分组id
    groupId:{
      type: String,
      required: false,
      default: ''
    },
    fieldList:{
      type: Array,
      required: false,
      default: ()=>{return []}
    },
    btnName:{
      type:String,
      required:false,
      default:"添加筛选"
    }
  },
  data() {
    return {
      confirmLoading:false,
      labelCol: { style: 'width:70px' },
      wrapperCol: {},
      model: {
        value:'',
        value1:'',
        value2:'',
        alias: '',
        field: undefined,
        operator: undefined,
        symbol1: '[',
        symbol2: ']'
      },
      validatorRules: {
        alias: [
          { required: false,min:0,max:20, message: '标签最大长度应小于20个字符'}
        ],
        field: [
          { required: true, validator:this.validateFormField,trigger:['change', 'blur']}
        ],
        operator: [
          { required: true,validator:this.validateOperator,trigger:['change', 'blur']}
        ],
        tagName: [
          { required: false, message: '请输入标签' },
          { min: 1, max: 20, message: '标签长度应在【1-20】个字符之间' }
        ],
        value:[
          {required: true, validator:this.validateValue}
        ],
        value1:[
          {required: false, validator:this.validateValue}
        ],
        value2:[
          {required: false, validator:this.validateValue}
        ]
      },
      loading: false,
      //运算符
      defOperatorList: [
        {
          label: '是',
          key: 'equal',
          inverse:0
        },
        {
          label: '不是',
          key: 'notEqual',
          inverse:1
        },
        {
          label: '属于',
          key: 'orEqual',
          inverse:0
        },
        {
          label: '不属于',
          key: 'orNotEqual',
          inverse:1
        },
        {
          label: '介于',
          key: 'withinRange',
          inverse:0
        },
        {
          label: '不介于',
          key: 'outsideRange',
          inverse:1
        },
        {
          label: '存在',
          key: 'exist',
          inverse:0
        },
        {
          label: '不存在',
          key: 'notExist',
          inverse:1
        }
      ],
      operatorList:[],
      //数字类型
      numberTypeList:['byte','short','int','integer','long','float','double','chart','number'],
      //属于/不属于情况下，创建的tag标签值
      selectedTags: [],
      url: {
        /*选择添加查询条件操作中涉及接口*/
        feilds: '/logAnalyze/getFields',//加载字段数据接口
        add: '/logAnalyze/term/add',//新增查询条件
        edit:'/logAnalyze/term/edit'//修改查询条件
      }
    }
  },
  watch: {},
  methods: {
    initForm() {
      this.$refs.form.clearValidate()
      // this.$refs.form.resetFields()
      this.operatorList=[]
      this.model["alias"] = ''
      this.model["field"] = undefined
      this.model["operator"] = undefined
      this.model["fieldType"] = ''
      this.model['value']=''
      this.model['value1']=''
      this.model['value2']=''
      this.resetValue()
    },
    /*下拉框输入过滤不区分大小写*/
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      )
    },
    /*改变字段*/
    changeField(value) {
      this.clearValueValidate()
      let obj = this.fieldList.find((item) => {
        return item.field === value
      })
      this.model.fieldType = obj ? obj.type.trim().toLowerCase() : undefined
      this.operatorList = []
      if (this.model["operator"]) {
        this.model["operator"] = undefined
      }
      if (value) {
        this.operatorList = this.defOperatorList.filter((operator) => {
          if (this.model.fieldType === "date" || this.numberTypeList.includes(this.model.fieldType)) {
            return true
          } else {
            return operator.key !== "withinRange" && operator.key !== "outsideRange"
          }
        })
      } else {
        this.operatorList = []
      }
      // this.resetValue()
      this.$refs.field.onFieldChange()
    },
    validateFormField(rule, value, callback) {
      if (rule.required) {
        let length = value === null || value === undefined || value.toString().replace(/\s+/g, '') === '' ? 0 : value.toString().length
        if (length === 0) {
          callback("请选择字段")
        } else {
          callback()
        }
      }
    },
    /*改变运算符*/
    changeOperator(value) {
      //console.log('changeOperator value===',value)
      this.resetValue()
      this.clearValueValidate()
      if (value) {
        let obj = this.operatorList.filter((item) => {
          return item.key === value
        })
        this.model['inverse'] = obj[0].inverse
        switch (value) {
          case "equal":
          case "notEqual":
            this.model.value = ''
            break;
          case "orEqual":
          case "orNotEqual":
            this.model.value = []
            break;
          case "withinRange":
          case "outsideRange":
            this.model.value1 = ''
            this.model.value2 = ''
            break;
          case "exist":
          case "notExist":
            break;
          default:
            break;
        }
      }
      this.$nextTick(()=>{
        this.$refs.operator.onFieldChange()
      })
    },
    validateOperator(rule, value, callback) {
      if (rule.required) {
        let length = value === null || value === undefined || value.toString().replace(/\s+/g, '') === '' ? 0 : value.toString().length
        if (length === 0) {
          callback("请选择运算符")
        } else {
          callback()
        }
      }
    },
    resetValue() {
      /* delete this.model["value"]
       delete this.model["value1"]
       delete this.model["value2"]
       delete this.model["inverse"]*/
      // this.$delete(this.model, 'value')
      // this.$delete(this.model, 'value1')
      // this.$delete(this.model, 'value2')
      // this.$delete(this.model, 'inverse')

      this.model.symbol1 = '['
      this.model.symbol2 = ']'
    },
    clearValueValidate(){
      //console.log('clearValueValidate==',this.model)
      let keys=Object.keys(this.model)
      if (keys.length>0&&keys.includes("value")){
        this.$refs.form.clearValidate(["value"])
      }
      if (keys.length>0&&keys.includes("value1")&&keys.includes("value2")){
        this.$refs.form.clearValidate(["value1","value2"])
      }
    },
    /*运算符为是、不是时，校验输入的值*/
    validateEqual(value) {
      //console.log('validateEqual  value===',value)
      let length = value === null || value === undefined || value.toString().replace(/\s+/g, '') === '' ? 0 : value.toString().length
      //console.log('validateEqual  length===',length)
      if (length === 0) {
        return "请输入值"
      } else {
        return
        //暂时注释对日期和数字的校验，如果以后需要，放开即可
        /*if (this.model.fieldType === "date") {
          return this.validateDate(value)
        } else if (this.model.fieldType === "boolean") {
          return this.validateBoolean(value)
        }
        //判断选定字段的类型是不是数字类型，若为数字类型，需要校验当前输入值是否为数字
        else if (this.numberTypeList.includes(this.model.fieldType)) {
          return this.validateNumber(value)
        } else {
          return
        }*/
      }
    },
    /*运算符为属于、不属于时，校验输入的值*/
    validateOrEqual(value) {
      if (value) {
        if (value.length > 0) {
          for (let i = 0; i < value.length; i++) {
            let result = ''
            if (this.model.fieldType === "date") {
              result = this.validateDate(value[i])
            } else if (this.model.fieldType === "boolean") {
              result = this.validateBoolean(value[i])
            } else if (this.numberTypeList.includes(this.model.fieldType)) {
              result = this.validateNumber(value[i])
            }
            if (result) {
              return result
            } else {
              if (i < value.length - 1) {
                continue
              } else {
                return
              }
            }
          }
        } else {
          return '请输入值'
        }
      } else {
        return '请输入值'
      }
    },
    validateRange(value) {
      let length = value === null || value === undefined || value.toString().replace(/\s+/g, '') === '' ? 0 : value.toString().length
      if (length > 0) {
        if (this.model.fieldType === "date") {
          return this.validateDate(value)
        } else if (this.model.fieldType === "boolean") {
          return this.validateBoolean(value)
        }
        //判断选定字段的类型是不是数字类型，若为数字类型，需要校验当前输入值是否为数字
        else if (this.numberTypeList.includes(this.model.fieldType)) {
          return this.validateNumber(value)
        } else {
          return
        }
      } else {
        return
      }
    },
    /*校验date类型数据*/
    validateDate(value) {
      if (moment(value, 'YYYY-MM-DD HH:mm:ss', true).isValid()) {
        return
      } else {
        return '请按照YYYY-MM-DD HH:mm:ss格式输入date类型数据，并考略大小月的情况'
      }
    },
    /*校验boolean类型数据*/
    validateBoolean(value) {
      if (value === "true" || value === "false") {
        return
      } else {
        return "字段为布尔类型，取值请输入true或false"
      }
    },
    /*校验数字类型数据*/
    validateNumber(value) {
      if (new RegExp(/^[-]?((0|([1-9]\d*))(\.\d+)?)$/).test(value)) {
        if (!isFinite(value)) {
          return "请输入有限的数值"
        } else {
          return
        }
      } else {
        return '请输入有效的数值'
      }
    },
    /*校验输入的值*/
    validateValue(rule, value, callback) {
      //console.log('validateValue this.model===',this.model)
      //console.log('validateValue this.model.operator===',this.model.operator)
      //console.log('validateValue value===',value)
      let tip = ''
      if (rule.required) {
        if (this.model.operator === 'equal' || this.model.operator === 'notEqual') {
          tip = this.validateEqual(value)
          //console.log('validateEqual tip==',tip)
          callback(tip)
        } else if (this.model.operator === 'orEqual' || this.model.operator === 'orNotEqual') {
          tip = this.validateOrEqual(value)
          //console.log('validateOrEqual tip==',tip)
          callback(tip)
        }
      } else {
        if (this.model.operator === 'withinRange' || this.model.operator === 'outsideRange') {
          tip = this.validateRange(value)
          callback(tip)
        } else {
          callback()
        }
      }
    },
    /*选择改变标签值*/
    changeSelectedTags(value) {
      //console.log(`selected ${value}`);
    },
    generateTerm() {
      let term = ''
      switch (this.model.operator) {
        case "equal":
        case "notEqual":
          term = this.model.field + ":\"" + `${this.model.value}` + "\""
          break;
        case "orEqual":
        case "orNotEqual":
          let values=this.model.value.map((item)=>{return '"'+item+'"'})
          term = this.model.field + ":(" + `${values.join(" OR ")}` + ")"
          // term = this.model.field + ":(" + `${this.model.value.join(" OR ")}` + ")"
          break;
        case "withinRange":
        case "outsideRange":
          term = this.model.field + ":" + this.model.symbol1 + `${this.model.value1? '"'+this.model.value1 +'"':"*"}` + " TO " + `${this.model.value2?'"'+this.model.value2+'"':"*"}` + this.model.symbol2
          break;
        case "exist":
        case "notExist":
          term = "_exists_:" + this.model.field
          break;
        default:
          break;
      }
      return term
    },
    generateQuery() {
      let query = {}
      switch (this.model.operator) {
        case "equal":
        case "notEqual":
        case "orEqual":
        case "orNotEqual":
          query = {
            alias: this.model.alias,
            field: this.model.field,
            operator: this.model.operator,
            value: this.model.value,
          }
          break;
        case "withinRange":
        case "outsideRange":
          query = {
            alias: this.model.alias,
            field: this.model.field,
            operator: this.model.operator,
            symbol1: this.model.symbol1,
            symbol2: this.model.symbol2,
            value1: this.model.value1,
            value2: this.model.value2,
          }
          break;
        case "exist":
        case "notExist":
          query = {
            alias: this.model.alias,
            field: this.model.field,
            operator: this.model.operator
          }
          break;
        default:
          break;
      }
      return query
    },

    edit(record) {
      this.model = JSON.parse(record.query)
      this.model.id=record.id
      this.setOpertion(this.model.field)
    },
    /**编辑情况，根据字段设置运算符下拉数据*/
    setOpertion(value) {
      let obj = this.fieldList.find((item) => {
        return item.field === value
      })
      this.model.fieldType = obj ? obj.type.trim().toLowerCase() : undefined
      this.operatorList = []
      this.operatorList = this.defOperatorList.filter((operator) => {
        if (this.model.fieldType === "date" || this.numberTypeList.includes(this.model.fieldType)) {
          return true
        } else {
          return operator.key !== "withinRange" && operator.key !== "outsideRange"
        }
      })
      //console.log('编辑this.operatorList===',this.operatorList)
    },
    submitQuery() {
      let that=this
      //日志来源为空时，不能创建查询，给出提示
      if (!that.logAnalyzeId||that.logAnalyzeId.length===0){
        that.$message.warning(opertionTips.addQuery)
        return
      }
      that.$refs.form.validate((valid, values) => {
        if (valid) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!that.model.id){
            httpurl = that.url.add
            method = 'post'
          }else {
            httpurl = that.url.edit
            method = 'put'
          }

          let formData={
            alias:that.model.alias,
            inverse:that.model.inverse,
            logAnalyzeId:that.logAnalyzeId,
            query:JSON.stringify(that.generateQuery()),
            term:that.generateTerm(),
            termQueryId:that.groupId
          }
          if (this.model.id){
            formData["id"]=this.model.id
          }
          //console.log('formData===',formData)
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
              that.confirmLoading = false
            }).catch((err) => {
            that.$message.warning(err.message)
            that.confirmLoading = false
          })
        }
      })
    },
    cancelQuery() {
      this.$emit('cancel')
    }
  }
}
</script>

<style scoped lang="less">
.field-type-disabled{
  width: 80px !important;
  border-radius:0 4px 4px 0 !important;
  border-right: 1px solid #d9d9d9 !important
}

.form-btn-wrapper{
  display: flex;
  justify-content: right;
  border-top: 1px solid #e6d8d8;
  padding: 12px 0 0;
  width: calc(100% + 24px);
  margin-left: -12px;

  .cancel-item{
    margin-bottom: 0px;
  }

  .submit-item{
    margin-bottom: 0px;
    margin-right: 16px;

    .submit{
      margin-left: 12px;
    }
  }
}
</style>