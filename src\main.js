/** init domain config */
import './config'


import Vue from 'vue'
import App from './App.vue'
import Storage from 'vue-ls'
import router from './router'
import store from './store/'

import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import locale from 'element-ui/lib/locale/lang/zh-CN'
// 引入字体
import './assets/font/stylesheet.css'

import FormMaking from 'form-making'
import 'form-making/dist/FormMaking.css'
import { VueAxios } from '@/utils/request'

require('@jeecg/antd-online-mini')
require('@jeecg/antd-online-mini/dist/OnlineForm.css')

import Antd, { version } from 'ant-design-vue'

//表单验证
import { rules } from '@/utils/rules'
Vue.prototype.rules = rules
import '@/components/flowable/bpmn/init'
import VueClipBoard from 'vue-clipboard2'
//表单设计
import '@/components/k-form-design/index'


import Viser from 'viser-vue'
import 'ant-design-vue/dist/antd.less' // or 'ant-design-vue/dist/antd.less'
//注入全局属性$message
import { message } from 'ant-design-vue'
Vue.prototype.$message = message
message.config({
  duration: 2, // 持续时间
  top: `64px`, // 到页面顶部距离
  maxCount: 3 // 最大显示数, 超过限制时，最早的消息会被自动关闭
})

// 引入echarts
import echarts from 'echarts'
// import 'echarts-liquidfill'
Vue.prototype.$echarts = echarts
// Vue.prototype.$dict = window.dict

import '@/permission' // permission control
import '@/utils/filter' // base filter
import '@/utils/dragModal.js'
import {yq_notification} from "@/utils/yq_notification"
Vue.prototype.$yq_notification = yq_notification
import Print from 'vue-print-nb-jeecg'
/*import '@babel/polyfill'*/
import preview from 'vue-photo-preview'
import 'vue-photo-preview/dist/skin.css'
import SSO from '@/cas/sso.js'
import {
  ACCESS_TOKEN,
  DEFAULT_COLOR,
  DEFAULT_THEME,
  DEFAULT_LAYOUT_MODE,
  DEFAULT_COLOR_WEAK,
  SIDEBAR_TYPE,
  DEFAULT_FIXED_HEADER,
  DEFAULT_FIXED_HEADER_HIDDEN,
  DEFAULT_FIXED_SIDEMENU,
  DEFAULT_CONTENT_WIDTH_TYPE,
  DEFAULT_MULTI_PAGE
} from '@/store/mutation-types'
import config from '@/defaultSettings'

import JDictSelectTag from './components/dict/index.js'
import hasPermission from '@/utils/hasPermission'
import vueBus from '@/utils/vueBus'
import JeecgComponents from '@/components/jeecg/index'
import oneClickHelpComponents from '@/components/oneClickHelp/index'
import '@/assets/less/JAreaLinkage.less'
import '@/assets/less/flex.less'
import '@/assets/less/yqTheme.less'
import VueAreaLinkage from 'vue-area-linkage'
import '@/components/jeecg/JVxeTable/install'
import '@/components/JVxeCells/install'
import Contextmenu from 'e-vue-contextmenu'
import VueAnimateNumber from 'vue-animate-number'
// px2rem 自适应
/*import 'lib-flexible' //已提取到下面文件中*/
import '@/utils/yq_flexible.js'
import { yqHasPermission } from '@/utils/YqHasPermission'
//skywalking监控系统
import ClientMonitor from 'skywalking-client-js';

if (window.config.ENABLE_SKYWALKING_REPORT) {
//注册skywalking
  ClientMonitor.register({
    service: 'insight::front',//应用名称
    serviceVersion: 'V4.1.4',//应用版本号
    traceSDKInternal: true,//追踪sdk
    pagePath: location.href,//当前路由地址
    useFmp: true,
    vue: Vue,//vue实例
  });
// Vue 报错上报到skywalking。
  Vue.config.errorHandler = (error) => {
    console.error(error);
    reportFrameErrors(error);
  }
//监听ajax报错
  window.addEventListener('error', error => {
    console.log("error--->", error)
    reportFrameErrors(error);
  }, true);

//上报错误信息方法
  function reportFrameErrors(error) {
    ClientMonitor.reportFrameErrors({
      service: 'insight::front',//应用名称
      serviceVersion: 'V4.1.4',//应用版本号
      pagePath: location.href,
      vue: Vue,
    }, error);
  }
}
//由于tabs不支持v-has，以下方法可解决此问题，使用：v-if=“$yqHasPermission（‘按钮权限授权标识’）”
Vue.prototype.$yqHasPermission=yqHasPermission
Vue.config.productionTip = false
Vue.use(Storage, config.storageOptions)
Vue.use(VueAxios, router)
Vue.use(Viser)
Vue.use(hasPermission)
Vue.use(JDictSelectTag)
Vue.use(Print)
Vue.use(preview)
Vue.use(vueBus)
Vue.use(JeecgComponents)
Vue.use(oneClickHelpComponents)
Vue.use(VueAreaLinkage)
Vue.use(ElementUI)
Vue.use(FormMaking)
Vue.use(Antd)
Vue.use(Contextmenu)
Vue.use(VueAnimateNumber)
Vue.use(VueClipBoard);
// Vue.use(VueQuillEditor)
  // new Vue({
  //   router,
  //   store,
  //   mounted() {
  //     store.commit('SET_SIDEBAR_TYPE', Vue.ls.get(SIDEBAR_TYPE, true))
  //     store.commit('TOGGLE_THEME', Vue.ls.get(DEFAULT_THEME, config.navTheme))
  //     store.commit('TOGGLE_LAYOUT_MODE', Vue.ls.get(DEFAULT_LAYOUT_MODE, config.layout))
  //     store.commit('TOGGLE_FIXED_HEADER', Vue.ls.get(DEFAULT_FIXED_HEADER, config.fixedHeader))
  //     store.commit('TOGGLE_FIXED_SIDERBAR', Vue.ls.get(DEFAULT_FIXED_SIDEMENU, config.fixSiderbar))
  //     store.commit('TOGGLE_CONTENT_WIDTH', Vue.ls.get(DEFAULT_CONTENT_WIDTH_TYPE, config.contentWidth))
  //     store.commit('TOGGLE_FIXED_HEADER_HIDDEN', Vue.ls.get(DEFAULT_FIXED_HEADER_HIDDEN, config.autoHideHeader))
  //     store.commit('TOGGLE_WEAK', Vue.ls.get(DEFAULT_COLOR_WEAK, config.colorWeak))
  //     store.commit('TOGGLE_COLOR', Vue.ls.get(DEFAULT_COLOR, config.primaryColor))
  //     store.commit('SET_TOKEN', Vue.ls.get(ACCESS_TOKEN))
  //     store.commit('SET_MULTI_PAGE', Vue.ls.get(DEFAULT_MULTI_PAGE, config.multipage))
  //   },
  //   render: h => h(App)
  // }).$mount('#app')
  SSO.init(() => {
    main();
  });


  function main() {
    new Vue({
      router,
      store,
      mounted () {
        store.commit('SET_SIDEBAR_TYPE', Vue.ls.get(SIDEBAR_TYPE, true))
        store.commit('TOGGLE_THEME', Vue.ls.get(DEFAULT_THEME, config.navTheme))
        store.commit('TOGGLE_LAYOUT_MODE', Vue.ls.get(DEFAULT_LAYOUT_MODE, config.layout))
        store.commit('TOGGLE_FIXED_HEADER', Vue.ls.get(DEFAULT_FIXED_HEADER, config.fixedHeader))
        store.commit('TOGGLE_FIXED_SIDERBAR', Vue.ls.get(DEFAULT_FIXED_SIDEMENU, config.fixSiderbar))
        store.commit('TOGGLE_CONTENT_WIDTH', Vue.ls.get(DEFAULT_CONTENT_WIDTH_TYPE, config.contentWidth))
        store.commit('TOGGLE_FIXED_HEADER_HIDDEN', Vue.ls.get(DEFAULT_FIXED_HEADER_HIDDEN, config.autoHideHeader))
        store.commit('TOGGLE_WEAK', Vue.ls.get(DEFAULT_COLOR_WEAK, config.colorWeak))
        store.commit('TOGGLE_COLOR', Vue.ls.get(DEFAULT_COLOR, config.primaryColor))
        store.commit('SET_TOKEN', Vue.ls.get(ACCESS_TOKEN))
        store.commit('SET_MULTI_PAGE',Vue.ls.get(DEFAULT_MULTI_PAGE,config.multipage))
        store.commit("SET_SSO_ACCESS_TOKEN",sessionStorage.getItem("sso_access_token")||"")
      },
      render: h => h(App)
    }).$mount('#app')
  }

