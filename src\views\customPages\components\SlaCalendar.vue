<template>
  <a-card :loading='loading' style='width: 100%; height: 100%'>
    <div slot='title'>
      <a-icon type='calendar' />
       SLA统计
<!--      <a-tooltip title='刷新'>-->
<!--        <a-icon type='sync' style='color: #ccc; cursor: pointer; font-size: 14px' @click='loadBusiness' />-->
<!--      </a-tooltip>-->
    </div>
<!--    <a slot='extra' @click='goPage(4)'>更多-->
<!--      <a-icon type='double-right' />-->
<!--    </a>-->
    <div class='schedule-calendar'>
      <a-calendar  :fullscreen='false' @change='changeDate' @panelChange='onPanelChange'>
        <div slot='headerRender' slot-scope='{value, type, onChange, onTypeChange}'>
          <div style='padding: 15px;text-align:center'>
            <a style='margin-right:10px'
               @click='changeYear(value,onChange,"reduce")'>
              <a-icon type='double-left'/>
            </a>
            <a style='margin-right:19px'
               @click='changeMonth(value,onChange,"reduce")'>
              <a-icon type='left'/>
            </a>
            <span>{{ value.format('YYYY-MM') }}</span>
            <!--<span>{{ year }}年{{ strMonth }}月</span>-->
            <a style='margin-left:19px' @click='changeMonth(value,onChange,"plus")'>
              <a-icon type='right'/>
            </a>
            <a style='margin-left:10px' @click='changeYear(value,onChange,"plus")'>
              <a-icon type='double-right'/>
            </a>
          </div>
        </div>
        <template slot='dateCellRender' slot-scope='value'>
          <div :class='slaFlag(value)' class='flag'></div>
          <a-popover @visibleChange='slaTip($event,value)'>
            <template slot="content">
              <div class='sla-item'>
                <div class='dot' style='background-color: #af0000'></div>
                总&emsp;数：<span style='color: #6DACF8'>{{slaRecord.total || 0}}</span></div>
              <div class='sla-item'>
                <div class='dot'></div>
                响应数：<span style='color: #6DACF8'>{{slaRecord.response || 0}}</span></div>
              <div class='sla-item'>
                <div class='dot'></div>
                处理数：<span style='color: #6DACF8'>{{slaRecord.handle || 0}}</span></div>
            </template>
            <div class='date-cell-pos' v-if='hasSla(value)'></div>
          </a-popover>
        </template>
      </a-calendar>
    </div>
  </a-card>
</template>
<script>
import moment from 'moment'
import {getAction} from '@api/manage'

export default {
  name: 'SlaCalendar',
  components: {},
  data(){
    return{
      year: moment().year(),
      month: (moment().month() + 1),
      date:moment().date(),
      slaList: [],//sla事件列表;
      slaRecords:{},
      loading:false,
      slaRecord:{total:0,handle:0,response:0},
    }
  },
  created() {
    this.getDateSLA()
  },
  mounted() {
  },
  methods:{
    //sls提示窗监听
    slaTip(e,value){
      if(e){
        let date = value.format('YYYY-MM-DD').toString()
        let tem = this.slaRecords[date]
        if(tem){
          this.$set(this,"slaRecord",tem)
          // Object.assign(this.slaRecord,tem)
        }
      }
    },
    //获取某日的sla记录
    getDateSLA(date){
      this.slaList = [];
      getAction("/sla/process/statisticsSla", { date:date }).then(res=>{
        if(res.success && res.result){
          this.slaRecords = res.result;
          this.slaList = Object.keys(res.result)
        }

      })
    },
    onPanelChange(value, mode) {
      this.year = value.year()
      this.month = value.month() + 1
      this.date=value.date()
      this.getDateSLA(value.format('YYYY-MM'))
    },
    changeDate(value) {
      this.year = value.year()
      this.month = value.month() + 1
      this.date=value.date()
    },
    changeYear(value, onChange, operation) {
      if (operation === 'plus') {
        /*if (this.year < moment().year()) {
          this.year += 1
          onChange(moment(value).add(1, 'years'))
        }*/
        this.year += 1
        onChange(moment(value).add(1, 'years'))
      } else {
        /*if (this.year > moment().year() - 10) {
          this.year -= 1
          onChange(moment(value).add(-1, 'years'))
        }*/
        this.year -= 1
        onChange(moment(value).add(-1, 'years'))
      }
    },
    changeMonth(value, onChange, operation) {
      if (operation === 'plus') {
        if (this.month < 12) {
          this.month += 1
          onChange(moment(value).add(1, 'month'))
        }
      } else {
        if (this.month > 1) {
          this.month -= 1
          onChange(moment(value).add(-1, 'month'))
        }
      }
    },
    slaFlag(value) {
      if (this.slaList.length > 0) {
        let flag = 'bgFlag'
        for (let i = 0; i < this.slaList.length; i++) {
          let date = value.format('YYYY-MM-DD').toString()
          if (date === this.slaList[i]) {
            flag =  'redFlag'
          }
        }
        return flag
      }
    },
    validateYearColor() {
      /* this.plusYearColor = this.year < moment().year() ? this.normalColor : this.disableColor
       this.reduceYearColor = (this.year > moment().year() - 10) ? this.normalColor : this.disableColor*/
    },
    validateMonthColor() {
      /* this.plusMonthColor = this.month < 12 ? this.normalColor : this.disableColor
       this.reduceMonthColor = this.month > 1 ? this.normalColor : this.disableColor*/
      // this.strMonth = this.month < 10 ? '0' + this.month : this.month.toString()
    },
    hasSla(value){
      let date = value.format('YYYY-MM-DD').toString()
      return this.slaList.includes(date)
    }
  }
}
</script>


<style scoped lang='less'>
.sla-item{
  display: flex;
  font-size: 16px;
  align-items: center;
  .dot{
    width: 5px;
    height: 5px;
    background-color: #0B86BC;
    border-radius: 50%;
    margin-right: 5px;
  }
}
.sla-item:not(.sla-item:last-child){
  margin-bottom: 8px;
}
::v-deep .ant-card-body{
  padding:0px;
}
::v-deep .ant-fullcalendar-calendar-body {
  padding: 5px 0px;

  .ant-fullcalendar-table {
    .ant-fullcalendar-tbody {
      height: 227px;
      padding-top: 13px;
      //background-color: #F5F7F9;
      background-color:#ffffff;

      .ant-fullcalendar-content {
        position: inherit;
        margin: 3px auto 0px auto;
        text-align: center;
        width: 24px;

        .flag {
          margin: auto;
          width: 5px;
          height: 5px;
          border-radius: 50%
        }

        .redFlag {
          background-color: #FF3545;
        }

        .greyFlag {
          //background-color: #B3B9BF;
          //background-color: black;
          background-color:var(--themeColor)
        }

        .bgFlag {
          //background-color: #F5F7F9;
          background-color:#ffffff;
        }
        .date-cell-pos{
          width: 24px;
          height: 24px;
          position: absolute;
          //background-color: #0ac540;
          //opacity: .3;
          top: 0px;
          left: 50%;
          transform: translateX(-50%);
          margin-top: 2px;
        }
      }
    }
  }

}

</style>