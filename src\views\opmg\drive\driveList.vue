<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll zxw">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <!-- 查询区域 -->
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <div class="table-page-search-wrapper-style">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="驱动名称">
                  <a-input placeholder="请输入驱动名称" :allowClear="true" autocomplete="off" v-model="queryParam.driveName"
                    :maxLength="50">
                  </a-input>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="驱动版本">
                  <a-input placeholder="请输入驱动版本" :allowClear="true" autocomplete="off"
                    v-model="queryParam.driveVersion" :maxLength="50">
                  </a-input>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="cpu架构">
                  <a-select :allow-clear="true" v-model="queryParam.driveCpu" placeholder="请选择cpu架构">
                    <a-select-option v-for="(item, key) in cpuList" :key="key" :value="item.value">
                      {{ item.text || item.label }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue"  v-show='toggleSearchStatus'>
                <a-form-item label="操作系统">
                  <a-select :allow-clear="true" v-model="queryParam.driveOs" placeholder="请选择操作系统">
                    <a-select-option v-for="(item, key) in dictOptions" :key="key" :value="item.value">
                      {{ item.text || item.label }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="colBtnsSpan()">
                <span class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button type="primary" @click="searchQuery" class="btn-search-style">查询</a-button>
                  <a-button @click="searchReset" style="margin-left: 10px" class="btn-reset-style">重置</a-button>
                  <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <!-- 查询区域-END -->

      <a-card :bordered="false" style="flex: auto" class="core">
        <a-row class="lastBtn2">
          <!-- 操作按钮区域 -->
          <div class="table-operator">
            <a-button @click="handleAdd" v-has="'drive:add'">新增</a-button>
            <a-dropdown v-if="selectedRowKeys.length > 0">
              <a-menu slot="overlay" style='text-align: center'>
                <a-menu-item v-has="'drive:delete'" key="1" @click="batchDel" style="text-align: center">删除</a-menu-item>
              </a-menu>
              <a-button> 批量操作
                <a-icon type="down" />
              </a-button>
            </a-dropdown>
          </div>
        </a-row>
        <!-- table区域-begin -->
        <a-table ref="table" bordered rowKey="id" :columns="columns" :dataSource="dataSource" :pagination="ipagination"
          :loading="loading" :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          class="j-table-force-nowrap" @change="handleTableChange">
          <template slot="htmlSlot" slot-scope="text">
            <div v-html="text"></div>
          </template>
          <template slot="imgSlot" slot-scope="text">
            <span v-if="!text" style="font-size: 14px">无图片</span>
            <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width: 80px; font-size: 14px" />
          </template>
          <template slot="fileSlot" slot-scope="text">
            <span v-if="!text" style="font-size: 14px">无文件</span>
            <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
              下载
            </a-button>
          </template>

          <span slot="action" slot-scope="text, record" class="caozuo"
            style="display: inline-block; white-space: nowrap; text-align: center">
            <a style="color: #409eff" @click="handleDetailPage(record)">查看</a>
            <a-divider type="vertical" />
            <a-dropdown>
              <a class="ant-dropdown-link">更多
                <a-icon type="down" /></a>
              <a-menu slot="overlay">
                <!-- <a-menu-item> </a-menu-item> -->
                <a-menu-item>
                  <a style="color: #409eff" @click="downloadDriveFile(record)">下载驱动</a>
                </a-menu-item>
                 <a-menu-item v-has="'drive:edit'">
                   <a style="color: #409eff" @click="handleEdit(record)">编辑</a>
                 </a-menu-item>
                <a-menu-item v-has="'drive:delete'">
                  <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                    <a style="color: #409eff">删除</a>
                  </a-popconfirm>
                </a-menu-item>
              </a-menu>
            </a-dropdown>
          </span>
          <span slot="downloadCount" slot-scope="text">
            <span v-if="text == null">0</span>
            <span v-else>{{ text }}</span>
          </span>
          <span slot="dictName" slot-scope="text">
            {{ getDictName(text) }}
          </span>
          <template slot="tooltip" slot-scope="text">
            <a-tooltip placement="topLeft" :title="text" trigger="hover">
              <div class="tooltip">
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
      </a-card>

      <drive-modal ref="modalForm" @ok="modalFormOk"> </drive-modal>
      <!-- <patch-info-modal ref="patchInfoModalForm" @ok="modalFormOk"></patch-info-modal> -->
    </a-col>
  </a-row>
</template>

<script>
  import '@/assets/less/TableExpand.less'
  import {
    mixinDevice
  } from '@/utils/mixin'
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import driveModal from './modules/driveModal'
  //   import PatchInfoModal from './modules/PatchInfoModal'
  import {
    filterMultiDictText
  } from '@/components/dict/JDictSelectUtil'
  import JSuperQuery from '@/components/jeecg/JSuperQuery.vue'
  import {
    YqFormSearchLocation
  } from '@/mixins/YqFormSearchLocation'
  import {
    ajaxGetAreaItems,
    ajaxGetDictItems,
    getDictItemsFromCache
  } from '@/api/api'
  import {
    deleteAction,
    getAction,
    downFile,
    getFileAccessHttpUrl
  } from '@/api/manage'
  import {
    postAction
  } from '../../../api/manage'
  import Vue from 'vue'

  export default {
    name: 'driveList',
    mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
    components: {
      driveModal,
      JSuperQuery,
      //   PatchInfoModal,
    },
    data() {
      return {
        description: '驱动管理表管理页面',
        // 表头
        columns: [{
            title: '驱动名称',
            dataIndex: 'driveName',
          },
          {
            title: '驱动版本',
            dataIndex: 'driveVersion'
          },
          {
            title: '操作系统',
            dataIndex: 'driveOs',
            scopedSlots: {
              customRender: 'dictName'
            }
          },
          {
            title: 'cpu架构',
            dataIndex: 'driveCpu'
          },
          {
            title: '下载次数',
            dataIndex: 'downloadCount',
            scopedSlots: {
              customRender: 'downloadCount'
            },
            customCell: () => {
              let cellStyle = 'text-align: right'
              return {
                style: cellStyle,
              }
            }
          },
          {
            title: '驱动文件路径',
            dataIndex: 'driveFile',
            customCell: () => {
              let cellStyle = 'text-align: left'
              return {
                style: cellStyle,
              }
            }
          },
          {
            title: '描述',
            dataIndex: 'driveDescribe',
            customCell: () => {
              let cellStyle = 'text-align: left; min-width: 120px;max-width:300px'
              return {
                style: cellStyle,
              }
            },
            scopedSlots: {
              customRender: 'tooltip'
            }
          },
          {
            title: '操作',
            dataIndex: 'action',
            fixed: 'right',
            align: 'center',
            width: 147,
            scopedSlots: {
              customRender: 'action'
            },
          },
        ],
        url: {
          list: '/drive/driveInfo/list',
          delete: '/drive/driveInfo/deleteBatch',
          deleteBatch: '/drive/driveInfo/deleteBatch',
          exportXlsUrl: '/drive/driveInfo/exportXls',
          importExcelUrl: 'drive/driveInfo/importExcel',
        },
        dictOptions: [],
        cpuList: [],
      }
    },
    created() {},
    mounted() {
      this.initDictData()
    },
    computed: {
      importExcelUrl: function () {
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
      },
    },
    methods: {
      initDictData() {
        //根据字典Code, 初始化字典数组
        ajaxGetDictItems('cpuArch', null).then((res) => {
          if (res.success) {
            this.cpuList = res.result
          }
        })
        ajaxGetDictItems('os_type', null).then((res) => {
          if (res.success) {
            this.dictOptions = res.result
          }
        })
      },
      getDictName(value) {
        let dictName = ''
        this.dictOptions.forEach((res, i) => {
          if (this.dictOptions[i].value == value) {
            dictName = this.dictOptions[i].text
          }
        })
        return dictName
      },
      handleDelete: function (id) {
        if (!this.url.delete) {
          this.$message.error('请设置url.delete属性!')
          return
        }
        var that = this
        deleteAction(that.url.delete, {
          ids: id
        }).then((res) => {
          if (res.success) {
            that.$message.success(res.message)
            that.loadData()
          } else {
            that.$message.warning(res.message)
          }
        })
      },
      //下载
      downloadDriveFile(data) {
        let path=data.driveFile
        if(path){
          let arr = path.split("/")
          let fileName = ""
          if(arr.length > 0){
            fileName = arr[arr.length - 1]
          }
          this.downloadFileByURL(window._CONFIG['pathUrl'] + '/' + path + '?id=' + data.id,fileName)
        }
        else{
          this.$message.warning("没有驱动文件！")
        }
      }
    }
  }
</script>
<style lang="less" scoped>
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';
</style>