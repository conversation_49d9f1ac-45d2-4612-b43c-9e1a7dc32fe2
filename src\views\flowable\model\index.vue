<template>
  <a-row style='height: 100%'>
    <a-col style='height: 100%; display: flex; flex-direction: column'>
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0', marginRight: '12px' }" class='card-style'>
        <!-- 查询区域 -->
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref='row'>
              <a-col v-show="getVisible('key')" :span="spanValue">
                <a-form-item :label="getTitle('key')">
                  <a-select :getPopupContainer='node=>node.parentNode' v-model="queryParam.modelKey" show-search
                            placeholder="请选择模型编码" option-filter-prop="children" :filter-option="filterOption"   allow-clear
                  >
                    <a-select-option v-for="(item, key) in processDefinitionKeyList" :key="item" :value="item">
                      <div style="display: inline-block; width: 100%" :title="item">
                        {{ item }}
                      </div>
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col v-show="getVisible('name')" :span="spanValue">
                <a-form-item :label="getTitle('name')">
                  <a-input :allow-clear='true' :maxLength='maxLength' autocomplete='off' v-model='queryParam.modelName' placeholder='请输入模型名称' />
                </a-form-item>
              </a-col>
              <a-col v-show="getVisible('category')" :span="spanValue">
                <a-form-item :label="getTitle('category')">
                  <j-dict-select-tag v-model='queryParam.modelCategory' placeholder='请选择模型类别' dictCode='bpm_process_type'
                    @change='changeModelCategory' />
                </a-form-item>
              </a-col>
              <a-col v-show="getVisible('version')" :span="spanValue">
                <a-form-item :label="getTitle('version')">
                  <a-select :allow-clear='true' :getPopupContainer='(node) => node.parentNode' v-model='modelVer'
                    placeholder='请选择模型版本' @change='versionChange'>
                    <a-select-option value='0'> 最新版本</a-select-option>
                    <a-select-option value='1'> 全部</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <!-- <a-col v-show="getVisible('createTime')"  :span="spanValue">
                <a-form-item :label="getTitle('createTime')">
                  <a-range-picker
                    :getCalendarContainer="node=> node.parentNode"
                    style='width: 100%'
                    v-model='queryParam.createTime'
                    :placeholder="['开始时间', '结束时间']"
                    format='YYYY-MM-DD HH:mm:ss'
                    showTime
                    @change='onCreatedTimeChange' />
                </a-form-item>
              </a-col> -->
              <a-col :span="spanValue">
                <span class="table-page-search-submitButtons" style="overflow: hidden;">
                  <a-button icon="search" type="primary" @click="searchQuery">查询</a-button>
                  <a-button icon="reload" style="margin-left: 8px" @click="searchReset">重置</a-button>
                  <a v-if="queryItems.length>0" style="margin-left: 8px" @click="doToggleSearch">{{queryName}}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
        <!-- 查询区域-END -->

        <!--自定义查询项 -->
        <div v-if="toggleSearchStatus" class="custom-query-item">
          <a-checkbox-group v-model="settingQueryItems" :defaultValue="settingQueryItems" style="width:100%"
            @change="onQuerySettingsChange">
            <a-row :gutter="24">
              <template v-for="(item,index) in queryItems">
                <a-col v-show='item.checked' :span='querySpanValue' class='col-checkbox'>
                  <a-checkbox :disabled="item.disabled" :value="item.dataIndex">
                    <j-ellipsis :length="7" :value="item.title"></j-ellipsis>
                  </a-checkbox>
                </a-col>
              </template>
            </a-row>
          </a-checkbox-group>
        </div>
        <!-- 自定义查询项-END -->
      </a-card>

      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <!-- 操作按钮区域 -->
        <div class="table-operator">
          <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
          <!--      <a-button type="primary" icon="import" @click="handleImport">导入</a-button>-->
          <!-- <a-upload
            name="file"
            :showUploadList="false"
            :multiple="false"
            :headers="tokenHeader"
            :action="importExcelUrl"
            @change="handleImportExcel"
          >

          </a-upload> -->
        </div>

        <!-- table区域-begin -->
        <div>
          <!--      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px">
            <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择
            <a style="font-weight: 600">{{ selectedRowKeys.length }}</a
            >项
            <a style="margin-left: 24px" @click="onClearSelected">清空</a>
            <span style="float: right">
              <a @click="loadData()"><a-icon type="sync" />刷新</a>
              <a-divider type="vertical" />
              <a-popover title="自定义列" trigger="click" placement="leftBottom">
                <template slot="content">
                  <a-checkbox-group @change="onColSettingsChange" v-model="settingColumns" :defaultValue="settingColumns">
                    <a-row style="width: 400px">
                      <template v-for="(item, index) in defColumns">
                        <template v-if="item.key != 'rowIndex' && item.dataIndex != 'action'">
                          <a-col :key="index" :span="12"
                            ><a-checkbox :value="item.dataIndex"
                              ><j-ellipsis :value="item.title" :length="10"></j-ellipsis></a-checkbox
                          ></a-col>
                        </template>
                      </template>
                    </a-row>
                  </a-checkbox-group>
                </template>
                <a><a-icon type="setting" />设置</a>
              </a-popover>
            </span>
          </div>-->
          <a-table ref="table" bordered rowKey="id" :columns="columns" :dataSource="dataSource"
            :pagination="ipagination" :loading="loading"
            :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }" @change="handleTableChange">
            <span slot="deployed" slot-scope="text">{{ text ? '是' : '否' }}</span>
            <span slot="action" slot-scope="text, record">
              <a @click="handleView(record)">查看</a>
              <a-divider type="vertical" />
              <a-dropdown>
                <a class="ant-dropdown-link">更多
                  <a-icon type="down" /></a>
                <a-menu slot="overlay">
                  <!-- <a-menu-item @click="handleCopy(record)">复制</a-menu-item> -->
                  <a-menu-item @click="handleUpdateModel(record)">流程设计</a-menu-item>
                  <a-menu-item>
                    <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                      <a>删除</a>
                    </a-popconfirm>
                  </a-menu-item>
                  <a-menu-item>
                    <a-popconfirm title="确定删除包含历史吗?" @confirm="() => handleDelete(record.id, true)">
                      <a>删除包含历史</a>
                    </a-popconfirm>
                  </a-menu-item>
                  <a-menu-item v-if='!record.deployed' @click='handleDeploy(record.id,record.version,record.key)'>发布
                  </a-menu-item>
                </a-menu>
              </a-dropdown>
            </span>
          </a-table>
        </div>
        <!-- table区域-end -->
      </a-card>
      <!-- 表单区域 -->
      <!-- 新增 -->
      <add-modal ref="modalForm" @ok="modalFormOk"></add-modal>
      <!-- 导入 -->
      <!--    <upload-model ref="uploadModel" @uploadOk="loadData"></upload-model>-->
    </a-col>
  </a-row>
</template>

<script>
  import AddModal from './modules/AddModal'
  import {
    filterDictTextByCache
  } from '@/components/dict/JDictSelectUtil'
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import Vue from 'vue'
  import {
    filterObj
  } from '@/utils/util'
  import {
    deleteAction, getAction,
    postAction,
    putAction
  } from '@/api/manage'
  import UploadModel from './modules/UploadModel.vue'
  import {
    YqFormSeniorSearchLocation
  } from '@/mixins/YqFormSeniorSearchLocation'
  //高级查询modal需要参数
  export default {
    name: 'ModelList',
    mixins: [JeecgListMixin, YqFormSeniorSearchLocation],
    components: {
      AddModal,
      UploadModel,
    },
    data() {
      return {
        maxLength:50,
        disableMixinCreated: true,
        formItemLayout: {
          labelCol: {
            style: 'width:80px'
          },
          wrapperCol: {
            style: 'width:calc(100% - 80px)'
          }
        },
        modelVer: '0',
        description: '单表示例列表',
        processDefinitionKeyList:[],
        //字典数组缓存
        sexDictOptions: [],
        importExcelUrl: `${window._CONFIG['domianURL']}/test/jeecgDemo/importExcel`,
        //列设置
        settingColumns: [],
        //列定义
        columns: [{
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            isUsed: false,
            customCell: () => {
              let cellStyle = 'text-align:center;width:60px'
              return {
                style: cellStyle
              }
            },
            customRender: function (t, r, index) {
              return parseInt(index) + 1
            },
          },
          {
            title: '模型编码',
            key: 'key',
            dataIndex: 'key',
            isUsed: true,
            customCell: () => {
              let cellStyle = 'text-align:center'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '模型名称',
            dataIndex: 'name',
            isUsed: true,
            customCell: () => {
              let cellStyle = 'text-align:center'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '模型类别',
            dataIndex: 'category',
            isUsed: true,
            customRender: (text) => {
              //字典值替换通用方法
              return filterDictTextByCache('bpm_process_type', text)
            },
            customCell: () => {
              let cellStyle = 'text-align:center'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '模型版本',
            dataIndex: 'version',
            isUsed: true,
            customRender: (v) => {
              return 'v' + v
            },
            customCell: () => {
              let cellStyle = 'text-align:center'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '创建时间',
            dataIndex: 'createTime',
            isUsed: false,
            customCell: () => {
              let cellStyle = 'text-align:center;width:160px'
              return {
                style: cellStyle
              }
            },
          },
          {
            title: '是否发布',
            dataIndex: 'deployed',
            isUsed: false,
            scopedSlots: {
              customRender: 'deployed'
            },
            customCell: () => {
              let cellStyle = 'text-align:center;width:100px'
              return {
                style: cellStyle
              }
            },
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            fixed: 'right',
            isUsed: false,
            width: 150,
            scopedSlots: {
              // filterDropdown: 'filterDropdown',
              // filterIcon: 'filterIcon',
              customRender: 'action',
            },
          },
        ],
        url: {
          list: '/flowable/model/list',
          delete: '/flowable/model/delete',
          deleteBatch: '/test/jeecgDemo/deleteBatch',
          exportXlsUrl: '/test/jeecgDemo/exportXls',
          processDefinitionKeyList:'/flowable/processDefinition/queryKeyList'
        },
      }
    },
    created() {
      this.getProcessDefinitionKeyList()
      this.getColumns(this.columns)
      this.queryParam.latestVersion = true
      this.loadData()
      //this.getModelList()
    },
    methods: {
      filterOption(input, option) {
        return (
          option.componentOptions.children[0].children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
        )
      },
      getProcessDefinitionKeyList() {
        this.processDefinitionKeyList=[]
        let param = {
          pageSize: -1
        }
        getAction(this.url.processDefinitionKeyList,param).then((res) => {
          console.log('获取流程定义res==',res)
          if (res.success&&res.result) {
            this.processDefinitionKeyList = res.result
          }else {
            this.processDefinitionKeyList=[]
          }
        }).catch((err)=>{
          this.$message.error(err.message)
          this.processDefinitionKeyList=[]
        })
      },
      onCreatedTimeChange: function (value, dateString) {
        this.queryParam.modelCreatedAfter = dateString[0]
        this.queryParam.modelCreatedBefore = dateString[1]
      },
      changeModelCategory(value) {
        if (!value) {
          this.queryParam.modelCategory = undefined
        }
      },
      searchReset() {
        this.modelVer = '0'
        this.queryParam = {
          latestVersion: true
        }
        this.loadData(1)
      },
      handleImport() {
        this.$refs.uploadModel.visible = true
      },
      handleUpdateModel(row) {
        let comItem = {
          path: '/bpmn',
          name: 'bpmn',
          meta: {
            title: '流程设计'
          },
          component: () => import('@/components/flowable/bpmn/index'),
        }
        // let parentName = ''
        // let parentRoute = getParentRoute(this.$route)
        // if (parentRoute) {
        //   parentName = parentRoute.name
        // }
        // addChildRouter(parentName, comItem)
        this.$router.push({
          path: '/bpmn',
          query: {
            id: row.id,
            isView: row.deployed
          },
        })
      },
      handleDeploy(id, version, key) {
        postAction('/flowable/model/deploy', {
          id,
          version,
          key
        }).then((res) => {
          if (res.success) {
            this.$message.success('发布成功！')
            this.loadData()
          } else {
            this.$message.warning(res.message)
          }
        })
      },
      handleCopy(row) {
        putAction('/flowable/model/copy', {
          id: row.id
        }).then((res) => {
          if (res.success) {
            //重新计算分页问题
            this.reCalculatePage(1)
            this.$message.success('复制成功！')
            this.loadData()
          } else {
            this.$message.warning(res.message)
          }
        })
      },
      handleDelete(id, cascade) {
        let ids = id ? [id] :
          this.selectedRecords.map((record) => {
            return record.id
          })
        if (ids.length == 0) {
          Message.error('请选择要删除的模型')
          return
        }
        deleteAction(this.url.delete, {
          ids: ids.toString(),
          cascade,
        }).then((res) => {
          if (res.success) {
            //重新计算分页问题
            this.reCalculatePage(1)
            this.$message.success('删除成功！')
            this.loadData()
          } else {
            this.$message.warning(res.message)
          }
        })
      },
      handleView(record) {
        this.$refs.modalForm.edit(record)
        this.$refs.modalForm.title = '详情'
        this.$refs.modalForm.disableSubmit = true
      },
      versionChange(e) {
        if (e == 0) {
          this.queryParam.latestVersion = true
        } else {
          this.queryParam.latestVersion = false
        }
      },
      getQueryParams() {
        //高级查询器
        let sqp = {}
        if (this.superQueryParams) {
          sqp['superQueryParams'] = encodeURI(this.superQueryParams)
          sqp['superQueryMatchType'] = this.superQueryMatchType
        }
        var param = Object.assign(sqp, this.queryParam, this.isorter, this.filters)

        param.field = this.getQueryField()
        param.pageNo = this.ipagination.current
        param.pageSize = this.ipagination.pageSize
        delete param.birthdayRange //范围参数不传递后台
        return filterObj(param)
      },
      onetomany: function () {
        this.$refs.jeecgDemoTabsModal.add()
        this.$refs.jeecgDemoTabsModal.title = '编辑'
      },
      //跳转单据页面
      jump() {
        this.$router.push({
          path: '/jeecg/helloworld'
        })
      },
      onBirthdayChange: function (value, dateString) {
        this.queryParam.birthday_begin = dateString[0]
        this.queryParam.birthday_end = dateString[1]
      },
      //列设置更改事件
      onColSettingsChange(checkedValues) {
        var key = this.$route.name + ':colsettings'
        Vue.ls.set(key, checkedValues, 7 * 24 * 60 * 60 * 1000)
        this.settingColumns = checkedValues
        const cols = this.defColumns.filter((item) => {
          if (item.key == 'rowIndex' || item.dataIndex == 'action') {
            return true
          }
          if (this.settingColumns.includes(item.dataIndex)) {
            return true
          }
          return false
        })
        this.columns = cols
      },
      initColumns() {
        var key = this.$route.name + ':colsettings'
        let colSettings = Vue.ls.get(key)
        if (colSettings == null || colSettings == undefined) {
          let allSettingColumns = []
          this.defColumns.forEach(function (item, i, array) {
            allSettingColumns.push(item.dataIndex)
          })
          this.settingColumns = allSettingColumns
          this.columns = this.defColumns
        } else {
          this.settingColumns = colSettings
          const cols = this.defColumns.filter((item) => {
            if (item.key == 'rowIndex' || item.dataIndex == 'action') {
              return true
            }
            if (colSettings.includes(item.dataIndex)) {
              return true
            }
            return false
          })
          this.columns = cols
        }
      },
    },
  }
</script>
<style scoped lang='less'>
  @import '~@assets/less/common.less';
  @import '~@assets/less/YQCommon.less';
</style>