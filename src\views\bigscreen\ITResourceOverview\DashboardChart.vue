<template>
  <div class='alarm-num'>
    <div ref='cpuUsageTopChart' class='alarm-num-chart'></div>
    <div class='alarm-num-info'>
      <div style='width: 100%'>
        <div class='alarm-num-info-item'>
          <span class='info-name'>今日告警数</span>
          <span class='info-value'>{{todayCount}}</span>
        </div>
        <div class='alarm-num-info-item'>
          <span class='info-name'>近7日告警数</span>
          <span class='info-value'>{{ last7DaysCount }}</span>
        </div>
        <div class='alarm-num-info-item'>
          <span class='info-name'>近30日告警数</span>
          <span class='info-value'>{{ last30DaysCount }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import * as echarts from 'echarts'
import resizeObserverMixin from '@views/statsCenter/com/resizeObserverMixin'
import { getAction } from '@api/manage'

export default {
  name: 'DashboardChart',
  mixins: [resizeObserverMixin],
  data() {
    return {
      myChart: null,
      last7DaysCount: 0,
      last30DaysCount: 0,
      todayCount: 0,
      alarmRate:0,
      alarmDevices:0,
    }
  },
  created() {
  },
  mounted() {
    var chartDom = this.$refs.cpuUsageTopChart
    this.myChart = echarts.init(chartDom)
    setTimeout(() => {
      this.initChart()
    }, 1000)
  },
  methods: {
    async initChart() {
      await getAction('/openAPI/alarmCount').then(res => {
        // console.log(res)
        if(res.success && res.result){
          this.last7DaysCount = res.result.last7DaysCount || 0
          this.last30DaysCount = res.result.last30DaysCount || 0
          this.todayCount = res.result.todayCount || 0
          this.alarmRate = res.result.alarmRate || 0
          this.alarmDevices = res.result.alarmCount || 0
        }else{
          console.log("没有获取到告警数量",res)
        }
      }).catch(err=>{
        console.log("获取告警数量报错",err)
      })
      const dataArr = this.alarmRate
      const dataX = 100
      const height1 = { value: 80 }
      let option = {
        /** 标题*/
        title: [
          {
            text: `告警设备 ${this.alarmDevices} 台`,
            bottom: '5%',
            left: 'center',
            textStyle: {
              fontSize: 12,
              fontWeight: 600,
              color: '#2BDAED'
            },
            triggerEvent: true
          }
        ],

        /** 关闭必图例*/
        legend: {
          show: false
        },
        graphic: {
          elements: [
            {
              type: 'line',
              z: -1,
              style: {
                fill: 'rgba(7,81,115,1)',
                stroke: 'rgba(7,81,115,1)',
                lineWidth: 2,
                shadowBlur: 15,
                shadowOffsetX: 0,
                shadowOffsetY: -4,
                shadowColor: '#13E6FF'
              },
              shape: {
                x1: height1.value * 0.57,
                y1: 0,
                x2: 0,
                y2: 0
              },
              left: 'center',
              bottom: '21%',
              silent: true
            },
            {
              type: 'line',
              z: 0,
              style: {
                fill: '#075173',
                stroke: '#075173',
                lineWidth: 2,
                shadowBlur: 15,
                shadowOffsetX: 0,
                shadowOffsetY: -4,
                shadowColor: '#13E6FF'
              },
              shape: {
                x1: height1.value * 0.43,
                y1: 0,
                x2: 0,
                y2: 0
              },
              left: 'center',
              bottom: '28.5%',
              silent: true
            }
          ]
        },
        series: [
          {
            name: '刻度尺',
            z: 8,
            type: 'gauge',
            radius: '81%',
            splitNumber: 5, // 刻度数量
            min: 0, // 最小刻度
            max: dataX, // 最大刻度
            // 仪表盘轴线相关配置
            axisLine: {
              lineStyle: {
                color: [
                  [
                    1,
                    {
                      type: 'radial',
                      x: 0.5,
                      y: 0.6,
                      r: 0.6,
                      colorStops: [
                        {
                          offset: 0.85,
                          color: 'rgba(3,31,70,0)' // 0% 处的颜色
                        },
                        {
                          offset: 0.93,
                          color: 'rgba(8,105,137,0)' // 100% 处的颜色
                        },
                        {
                          offset: 1,
                          color: '#12D7EF' // 100% 处的颜色
                        }
                      ]
                    }
                  ]
                ],
                width: 500
              }
            },
            /** 分隔线样式*/
            splitLine: {
              show: true,
              length: 12,
              lineStyle: {
                width: 3,
                color: 'rgba(18,229,254,0.8)' // 用颜色渐变函数不起作用
              }
            },
            /** 刻度线*/
            axisTick: {
              show: true,
              splitNumber: 20,
              lineStyle: {
                color: 'rgba(18,229,254,0.4)', // 用颜色渐变函数不起作用
                width: 1
              },
              length: 5
            },
            /** 刻度标签*/
            axisLabel: {
              distance: 5,
              fontSize: 10,
              color: 'rgba(255,255,255,0.7)'
            },
            detail: {
              show: false
            },
            animationDuration: 4000
          },
          {
            name: '渐变进度',
            type: 'gauge',
            radius: '80%',
            z: 7,
            splitNumber: 15,
            axisLine: {
              lineStyle: {
                color: [
                  [
                    dataArr / dataX,
                    {
                      type: 'linear',
                      x: 0,
                      y: 1,
                      x2: 0,
                      y2: 0,
                      colorStops: [
                        {
                          offset: 0,
                          color: 'rgba(60,207,223,0)' // 0% 处的颜色
                        },
                        {
                          offset: 0.9,
                          color: 'rgba(60,207,223,0.5)' // 100% 处的颜色
                        },
                        {
                          offset: 1,
                          color: 'rgba(60,207,223,0.9)' // 100% 处的颜色
                        }
                      ],
                      global: false // 缺省为 false
                    }
                  ]
                ],
                width: 16
              }
            },
            axisLabel: {
              show: false
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: false
            },
            itemStyle: {
              show: false
            },
            detail: {
              show: true,
              fontSize: 14,
              fontWeight: 'bold',
              offsetCenter: [0, '30%'],
              color: 'rgba(87,252,238,1)',
              formatter: '{value}%'
            },
            title: {
              // 标题
              show: false
            },
            data: [
              {
                value: dataArr
              }
            ],
            pointer: {
              show: false
            },
            animationDuration: 4000
          },
          {
            name: '内层带指针',
            type: 'gauge',
            radius: '61%',
            splitNumber: 10, // 刻度数量
            min: 0, // 最小刻度
            max: dataX, // 最大刻度
            // 仪表盘轴线相关配置
            axisLine: {
              lineStyle: {
                color: [
                  [
                    1,
                    {
                      type: 'radial',
                      x: 0.5,
                      y: 0.59,
                      r: 0.6,
                      colorStops: [
                        {
                          offset: 0.72,
                          color: 'rgba(3,32,70,0.1)'
                        },
                        {
                          offset: 0.94,
                          color: '#086989'
                        },
                        {
                          offset: 0.98,
                          color: '#0FAFCB'
                        },
                        {
                          offset: 1,
                          color: '#0EA4C1'
                        }
                      ]
                    }
                  ]
                ],
                width: 1000
              }
            },
            /** 分隔线样式*/
            splitLine: {
              show: false
            },
            /** 刻度线*/
            axisTick: {
              show: false
            },
            /** 刻度标签*/
            axisLabel: {
              show: false
            },
            /** 仪表盘指针*/
            pointer: {
              show: true,
              length: '50%',
              width: 3 // 指针粗细
            },
            /** 仪表盘指针样式*/
            itemStyle: {
              color: 'rgba(255,255,255,1)'
            },
            data: [
              {
                value: dataArr
              }
            ],
            detail: {
              show: false
            }
          }
        ]

      }
      option && this.myChart.setOption(option)
    },
    // 屏幕变化回调
    resizeObserverCb() {
      this.myChart.resize()
    }
  }
}
</script>
<style lang='less' scoped>
.alarm-num {
  height: 100%;
  width: 100%;
  display: flex;
  padding-bottom: 16px;

  .alarm-num-chart {
    height: 100%;
    width: 60%;
  }

  .alarm-num-info {
    height: 100%;
    width: 40%;

    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .alarm-num-info-item {
      line-height: 39px;

      .info-name {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.7);
        font-weight: 400;
        margin-right: 13px;
        letter-spacing: 1px;
      }

      .info-value {
        font-size: 16px;
        color: #FFFFFF;
        font-weight: bold;
        background: linear-gradient(0deg, #00FFFF 0%, #FFFFFF 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      /*     &:not(:last-child) {
             margin-bottom: 12px;
           }*/
    }
  }

  //background: #fff;
  //padding: 12px;

}
</style>
