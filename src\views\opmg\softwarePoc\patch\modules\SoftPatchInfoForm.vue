<template>
  <a-spin :spinning='confirmLoading'>
    <a-form-model ref='form' :model='model' :rules='validatorRules' :labelCol='labelCol'
                  :wrapperCol='wrapperCol'>
      <a-row>
        <a-col :md='12' :xs='24'>
          <a-form-model-item label='升级包类别' prop='patchType'>
            <a-select v-model='model.patchType' placeholder='请选择升级包类别' @change='onPatchTypeChange'>
              <a-select-option v-for='item in patchTypes' :key='"patchtype_"+item.value' :value='item.value'>
                {{ item.text }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <!--          <a-col :md='12' :xs='24'>
                    <a-form-model-item label='名称' prop='patchName'>
                      <a-input v-model='model.patchName' :allowClear='true' autocomplete='off' placeholder='请输入名称'>
                      </a-input>
                    </a-form-model-item>
                  </a-col>-->
        <a-col :md='12' :xs='24'>
          <a-form-model-item label='版本' prop='patchVersion'>
            <a-input v-model='model.patchVersion' :allowClear='true' autocomplete='off'
                     placeholder='数字三段式+英文如1.0.0-beta'>
            </a-input>
          </a-form-model-item>
        </a-col>
        <a-col :md='12' :xs='24'>
          <a-form-model-item label='支持操作系统' prop='patchOs'>
            <a-select key='patchOs' mode='multiple' v-model='model.patchOs' :allowClear='true' placeholder='请选择支持操作系统'>
              <a-select-option v-for='item in dictOptions' :key='item.value' :value='item.value'>
                {{ item.text }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :md='12' :xs='24'>
          <a-form-model-item class='two-words' label='支持架构' prop='frawork' >
            <a-select mode='multiple' v-model='model.frawork' :allowClear='true' :options='cpuList' placeholder='请选择支持架构'>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :md='12' :xs='24'>
          <a-form-model-item label='支持设备类型' prop='resourceType'>
            <a-select mode='multiple' v-model='model.resourceType' :allowClear='false' placeholder='请选择支持设备类型'>
              <a-select-option v-for='item in resourceTypeList' :key='item.value' :value='item.value'>
                {{ item.text }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <!--安装包文件上传-->
        <a-col span='24' v-if='model.patchType==="0"'>
          <a-row>
            <a-col :md='12' :xs='24'>
              <a-form-model-item label='升级包' prop='file'>
                <j-upload
                  v-model='model.file'
                  :multiple='false'
                  :number='1'
                  :bizPath='bizPath'
                  :size='500*1024*1024'
                  :accept='packageFileAccept'
                  @change='onFileChange'
                >
                </j-upload>
              </a-form-model-item>
            </a-col>
          </a-row>

        </a-col>
        <!--升级文件上传-->
        <a-col span='24' v-else-if='model.patchType==="1"'>
          <a-row>
            <a-col :md='12' :xs='24'>
              <a-form-model-item label='升级文件' prop='file'>
                <j-upload
                  v-model='model.file'
                  :returnUrl='true'
                  :multiple='false'
                  :number='1'
                  :bizPath='bizPath'
                  :size='500*1024*1024'
                  :accept='patchFileAccept'
                  @change='onFileChange'
                >
                </j-upload>
              </a-form-model-item>
            </a-col>
            <a-col :md='12' :xs='24'>
              <a-form-model-item label='执行脚本' prop='inst'>
                <j-upload
                  v-model='model.inst'
                  :multiple='false'
                  :number='1'
                  :bizPath='bizPath'
                  :size='10*1024*1024'
                  :accept='scriptFileAccept'
                  @change='onInstFileChange'
                >
                </j-upload>
              </a-form-model-item>
            </a-col>
            <a-col :md='12' :xs='24'>
              <a-form-model-item label='执行前脚本' prop='preInst'>
                <j-upload
                  v-model='model.preInst'
                  :multiple='false'
                  :number='1'
                  :bizPath='bizPath'
                  :size='10*1024*1024'
                  :accept='scriptFileAccept'
                  @change='onPreInstFileChange'
                >
                </j-upload>
              </a-form-model-item>
            </a-col>
            <a-col :md='12' :xs='24'>
              <a-form-model-item label='执行后脚本' prop='postInst'>
                <j-upload
                  v-model='model.postInst'
                  :multiple='false'
                  :number='1'
                  :bizPath='bizPath'
                  :size='10*1024*1024'
                  :accept='scriptFileAccept'
                  @change='onPostInstFileChange'>
                </j-upload>
              </a-form-model-item>
            </a-col>
          </a-row>

        </a-col>
        <!--          <a-col :md='12' :xs='24'>-->
        <!--            <a-form-model-item label='审核状态' prop='audit'>-->
        <!--              <a-select v-model='model.audit' placeholder='请选择审核状态'>-->
        <!--                <a-select-option v-for='item in auditStatus' :key='"audit_"+item.value' :value='item.value'>-->
        <!--                  {{ item.label }}-->
        <!--                </a-select-option>-->
        <!--              </a-select>-->
        <!--            </a-form-model-item>-->
        <!--          </a-col>-->
        <a-col :md='12' :xs='24'>
          <a-form-model-item label='是否启用' prop='isEnable'>
            <a-select v-model='model.isEnable' placeholder='请选择是否启用'>
              <a-select-option v-for='item in enables' :key='"effect_"+item.value' :value='item.value'>
                {{ item.text }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :md='12' :xs='24'>
          <a-form-model-item label='安装后设备是否重启' prop='isRestart'>
            <a-select v-model='model.isRestart' placeholder='请选择安装后设备是否重启'>
              <a-select-option v-for='item in restarts' :key='"restart_"+item.value' :value='item.value'>
                {{ item.text }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>

        <a-col :md='24' :xs='24'>
          <a-form-model-item
            class='two-words'
            label='升级特性'
            prop='upgradeFeature'
            :label-col='{xs: {span: 24},sm: {span: 4}}'
            :wrapperCol='{xs: {span: 24},sm: {span: 19}}'
          >
            <a-textarea v-model='model.upgradeFeature' :allowClear='true' autocomplete='off'
                        :autoSize='{ minRows: 2, maxRows: 8 }' placeholder='请输入升级特性' />
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
  </a-spin>
</template>

<script>
import {
  httpAction
} from '@/api/manage'
import {
  ajaxGetDictItems,
  getDictItemsFromCache
} from '@api/api'
import {
  packageFileAccept,
  scriptFileAccept,
  patchFileAccept,
  patchFileNameSuffix,
  scriptFileNameSuffix
} from '../../mock/fileTypes'

export default {
  name: 'DevopePatchInfoForm',
  props: {
    patchTypes: {
      type: Array,
      default: () => [],
      required: false
    },
    auditStatus: {
      type: Array,
      default: () => [],
      required: false
    },
    restarts: {
      type: Array,
      default: () => [],
      required: false
    },
    dictOptions: {
      type: Array,
      default: () => [],
      required: false
    },
    cpuList: {
      type: Array,
      default: () => [],
      required: false
    },
    enables: {
      type: Array,
      default: () => [],
      required: false
    },
    resourceTypeList: {
      type: Array,
      default: () => [],
      required: false
    },
    softwareId: {
      type:String,
      default:'',
      required:true
    }
  },
  data() {
    return {
      visible: false,
      confirmLoading: false,
      bizPath: 'software',
      model: {
        patchType: '0',
        patchVersion: '1.0.0',
        resourceType: ['6'],
        frawork: [],
        patchOs:[],
        file:"",
        preInst:"",
        postInst:"",
        inst:"",
        isRestart: "0",
        isEnable: '0',
        upgradeFeature:'',
        preInstOriginalName: '',
        postInstOriginalName: '',
        instOriginalName: '',
        fileOriginalName: ''
      },
      labelCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 8
        }
      },
      wrapperCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 14
        }
      },
      packageFileAccept,
      patchFileNameSuffix,
      scriptFileNameSuffix,
      patchFileAccept,
      scriptFileAccept,

      validatorRules: {
        softwareId: [{
          required: true,
          message: '请选择软件名称'
        }], patchName: [{
          required: true,
          message: '请输入名称'
        },
          { min: 1, max: 20, message: '长度不能少于1字符超过20个字符' }
        ],
        patchVersion: [{
          required: true,
          message: '请输入补丁版本'

        },
          { min: 1, max: 15, message: '长度不能少于1字符超过15个字符' },
          {
            pattern: /^\d+\.\d+\.\d+(-[a-zA-Z0-9]+)?$/,
            message: '请输入正确的补丁版本,如：1.0.0-test'
          }
        ],
        patchOs: [{
          required: true,
          message: '请选择支持操作系统'
        }],
        patchType: [{
          required: true,
          message: '请选择升级包类别'
        }],
        frawork: [{
          required: true,
          message: '请选择支持架构'
        }], resourceType: [{
          required: true,
          message: '请选择设备类型'
        }],
        file: [{
          required: true,
          message: '请上传相关文件'
        }],
        patchFileName: [{
          required: true,
          message: '请上传补丁文件'
        }],
        inst: [{
          required: true,
          message: '请上传执行脚本文件'
        }],
        preInst: [{
          required: true,
          message: '请上传执行前脚本文件'
        }],
        postInst: [{
          required: true,
          message: '请上传执行后脚本文件'
        }],
        upgradeFeature: [{
          required: false,
          message: '请输入描述'
        },
          {
            max: 300,
            message: '描述长度不可超过 300 个字符'
          }
        ]
      },
      url: {
        add: '/software/upgradePackageManage/add',
        edit: '/software/upgradePackageManage/edit'
      }
    }
  },
  created() {
  },
  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      //新增默认值
      this.$refs.form.resetFields();
      // console.log('record==', record)
      if (record.id) {
        Object.assign(this.model,record)
        // console.log('this.model', this.model)
        this.model.frawork = this.model.frawork.split(',')
        this.model.patchOs = this.model.patchOs.split(',')
        this.model.resourceType = this.model.resourceType.split(',')
      }
      this.visible = true
    },
    changePatchOs(value) {
    },
    onFileChange(e,info) {
      this.model.fileOriginalName = e && info? info[0].fileName:"";
    },
    onInstFileChange(e,info) {
      this.model.instOriginalName = e && info? info[0].fileName:"";
    },
    onPreInstFileChange(e,info) {
      this.model.preInstOriginalName = e && info? info[0].fileName:"";
    },
    onPostInstFileChange(e,info) {
      this.model.postInstOriginalName = e && info? info[0].fileName:"";
    },
    onPatchTypeChange(value) {
      this.model.file = '';
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.$refs.form.validate((valid, values) => {
        if (valid) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += that.url.add
            method = 'post'
          } else {
            httpurl += that.url.edit
            method = 'put'
          }
          let tem = JSON.parse(JSON.stringify(this.model))
          let formData = Object.assign(tem, values)
          formData.frawork = formData.frawork.join(',')
          formData.resourceType = formData.resourceType.join(',')
          formData.patchOs = formData.patchOs.join(',')
          formData.softwareId = this.softwareId
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.result)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
              that.confirmLoading = false
            })
            .catch((err) => {
              that.$message.warning(err.message)
              that.confirmLoading = false
            })
        }
      })
    }
  }
}
</script>
<style lang='less' scoped>
::v-deep .two-words > div > label {
  letter-spacing: 4px;
}

::v-deep .two-words > div > label::after {
  letter-spacing: 0px;
}
</style>