
import { getAction, deleteAction, putAction, postAction, httpAction } from '@/api/manage'
import Vue from 'vue'
import {UI_CACHE_DB_DICT_DATA } from "@/store/mutation-types"

//资产类型树
const queryAssetsCategoryTreeList = (params)=>getAction("/assetscategory/assetsCategory/selectTree",params);

const queryDeviceTreeList = (params)=>getAction("/device/deviceInfo/selectTree",params);
const queryTerminalTreeList = (params)=>getAction("/terminal/deviceInfo/selectTree",params);

const queryDeptTreeList = (params)=>getAction("/device/momgDept/selectTree",params);

const queryPostionTreeList = (params)=>getAction("/topo/room/tree",params);
const getStateInfo = (params)=>getAction("/device/deviceInfo/getStateInfo",params);

export {
  queryAssetsCategoryTreeList,
  queryDeviceTreeList,
  getStateInfo,
  queryDeptTreeList,
  queryPostionTreeList,
  queryTerminalTreeList
}