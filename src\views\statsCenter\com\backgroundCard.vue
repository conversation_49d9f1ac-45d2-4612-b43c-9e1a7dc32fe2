<template>
  <div class="outer-border" id="outer-border-height">
    <div class="outer-border-scale-left">
      <div class="outer-border-scale-item" v-for="(h,idx) in outerScaleList" :key="'a_'+idx" :style="{ top: h + 'px' }"></div>
    </div>
    <div class="outer-border-scale-right">
      <div class="outer-border-scale-item" v-for="(h,idx) in outerScaleList" :key="'b_'+idx" :style="{ top: h + 'px' }"></div>
    </div>

    <div class="inner-border-scale-left">
      <div
        class="inner-border-scale-item-left"
        :class="index % innerIntervalsNumber === 0 ? 'inner-border-scale-item-long' : 'inner-border-scale-item-short'"
        v-for="(h, index) in innerScaleList"
        :style="{ top: h + 'px' }"
        :key="'c_'+index"
      ></div>
    </div>
    <div class="inner-border-scale-right">
      <div
        class="inner-border-scale-item-right"
        :class="index % innerIntervalsNumber === 0 ? 'inner-border-scale-item-long' : 'inner-border-scale-item-short'"
        v-for="(h, index) in innerScaleList"
        :style="{ top: h + 'px' }"
        :key="'d_'+index"
      ></div>
    </div>

    <div class="big-screen-wrapper">
      <slot name="big-screen-content"></slot>
    </div>
  </div>
</template>
<script>
import resizeObserverMixin from '@views/statsCenter/com/resizeObserverMixin'
export default {
  name: 'backgroundCard',
  mixins: [resizeObserverMixin],
  data() {
    return {
      outerBorderHeight: 0,
      outerIntervalsNumber: 15,
      innerIntervalsNumber: 10,
      outerScaleList: [],
      innerScaleList: [],
    }
  },
  created() {},
  mounted() {},
  methods: {
    setScale() {
      this.initLine()
    },
    initLine() {
      this.outerScaleList = []
      this.innerScaleList = [];
      this.outerBorderHeight = document.getElementById('outer-border-height').getBoundingClientRect().height - 0.05 * 80
      let outerPerHeight = this.outerBorderHeight / this.outerIntervalsNumber
      for (let i = 0; i < this.outerIntervalsNumber + 1; i++) {
        this.outerScaleList.push(outerPerHeight * i)
        let innerPerHeight = outerPerHeight / this.innerIntervalsNumber
        if (i < this.outerIntervalsNumber) {
          for (let j = 0; j < this.innerIntervalsNumber; j++) {
            this.innerScaleList.push(innerPerHeight * j + i * outerPerHeight)
          }
          if (i === this.outerIntervalsNumber - 1) {
            this.innerScaleList.push(innerPerHeight * this.innerIntervalsNumber + i * outerPerHeight)
          }
        }
      }
    },
  },
}
</script>

<style scoped lang="less">
.outer-border {
  height: 100%;
 /* min-width: 1200px;
  min-height: 600px;*/

  min-width: 1366px;
  min-height: 600px;
  position: relative;

  .outer-border-scale-left {
    position: absolute;
    content: '';
    left: 0;
    top: 0;
    width: 1px;
    height: 100%;
    background: rgba(255, 255, 255, 0.2);
  }
  .outer-border-scale-right {
    position: absolute;
    content: '';
    right: 0;
    top: 0;
    width: 1px;
    height: 100%;
    background: rgba(255, 255, 255, 0.2);
  }
  .outer-border-scale-item {
    position: absolute;
    content: '';
    left: 0;
    width: 2px;
    height: 0.05rem;
    background: rgba(255, 255, 255, 0.3);
  }

  .inner-border-scale-left {
    position: absolute;
    content: '';
    left: 0.2rem;
    top: 0;
    width: 1px;
    height: 100%;
    background: rgba(255, 255, 255, 0);
  }
  .inner-border-scale-right {
    position: absolute;
    content: '';
    right: 0.2rem;
    top: 0;
    width: 1px;
    height: 100%;
    background: rgba(255, 255, 255, 0);
  }

  .inner-border-scale-item-left {
    position: absolute;
    content: '';
    left: 0;
    height: 1px;
    background: rgba(255, 255, 255, 0.3);
  }
  .inner-border-scale-item-right {
    position: absolute;
    content: '';
    right: 0;
    height: 1px;
    background: rgba(255, 255, 255, 0.3);
  }

  .inner-border-scale-item-short {
    width: 0.125rem;
  }
  .inner-border-scale-item-long {
    width: 0.2rem;
  }

  .big-screen-wrapper {
    height: 100%;
    margin: 0 0.4rem;
  }
}
</style>