<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll zxw">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <!-- 查询区域 -->
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <div class="table-page-search-wrapper-style">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="标题">
                  <j-input
                    placeholder="请输入标题"
                    :allowClear="true"
                    autocomplete="off"
                    v-model="queryParam.title"
                  ></j-input>
                </a-form-item>
              </a-col>
              <a-col :span="colBtnsSpan()">
                <span
                  class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                >
                  <a-button type="primary" @click="searchQuery" class="btn-search-style">查询</a-button>
                  <a-button @click="searchReset" style="margin-left: 10px" class="btn-reset-style">重置</a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>

      <a-card :bordered="false" style="flex: auto" class="core">
        <a-row class="lastBtn2">
          <div class="table-operator">
            <a-button @click="handleAdd">新增</a-button>
            <a-button @click="handleExportXls('运维知识库')">导出</a-button>
            <a-dropdown v-if="selectedRowKeys.length > 0">
              <a-menu slot="overlay" style='text-align: center'>
                <a-menu-item key="1" @click="batchDel">删除</a-menu-item>
              </a-menu>
              <a-button> 批量操作 <a-icon type="down"/></a-button>
            </a-dropdown>
          </div>
        </a-row>
        <!-- 查询区域-END -->
        <!-- table区域-begin -->
        <a-table
          ref="table"
          bordered
          rowKey="id"
          :columns="columns"
          :dataSource="dataSource"
          :pagination="ipagination"
          :loading="loading"
          :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          class="j-table-force-nowrap"
          @change="handleTableChange"
        >
          <span slot="fileUrl" slot-scope="fileUrl, record">
            <div style="color: #1890ff">
              {{ record.fileUrl }}
            </div>
          </span>

          <template slot="htmlSlot" slot-scope="text">
            <div v-html="text"></div>
          </template>
          <template slot="imgSlot" slot-scope="text">
            <span v-if="!text" style="font-size: 14px">无图片</span>
            <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width: 80px; font-size: 14px" />
          </template>
          <template slot="fileSlot" slot-scope="text">
            <span v-if="!text" style="font-size: 14px">无文件</span>
            <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
              下载
            </a-button>
          </template>

          <span
            slot="action"
            slot-scope="text, record"
            class="caozuo"
            style="display: inline-block; white-space: nowrap; text-align: center"
          >
            <!-- <a @click="handleEdit(record)">编辑</a> -->
            <a @click="fontClick(record.fileUrl)" v-if="record.fileUrl !=''">下载</a>
            <a-divider type="vertical" v-if="record.fileUrl!=''" />
            <a @click="handleDetailPage(record)">查看</a>
            <a-divider type="vertical" />
            <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
              <a>删除</a>
            </a-popconfirm>
          </span>
          <template slot="tooltip" slot-scope="text">
            <a-tooltip placement="topLeft" :title="text" trigger="hover">
              <div class="tooltip">
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
      </a-card>
      <ump-knowledge-modal ref="modalForm" @ok="modalFormOk"></ump-knowledge-modal>
      <!--详情-->
      <ump-knowledge-detail-modal ref="modalDetailForm" @ok="modalFormOk"></ump-knowledge-detail-modal>
    </a-col>
  </a-row>
</template>

<script>
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import UmpKnowledgeModal from './modules/UmpKnowledgeModal'
import JInput from '@/components/jeecg/JInput'
import UmpKnowledgeDetailModal from './modules/UmpKnowledgeDetailModal'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'

export default {
  name: 'DevOpsKnowledgeList',
  mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
  components: {
    UmpKnowledgeModal,
    JInput,
    UmpKnowledgeDetailModal,
  },
  data() {
    return {
      formItemLayout: {
        labelCol: {
          style: 'width:80px',
        },
        wrapperCol: {
          style: 'width:calc(100% - 80px)'
        }
      },
      description: '运维知识库管理页面',
      queryParam: {
        title: '',
        type: '',
        recordType: 4,
      },
      // 表头
      columns: [
        {
          title: '文件名称',
          dataIndex: 'title',
        },
        {
          title: '存储路径',
          dataIndex: 'fileUrl',
          scopedSlots: { customRender: 'fileUrl' },
        },
        {
          title: '创建人',
          dataIndex: 'createByName',
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
        },
        {
          title: '操作',
          dataIndex: 'action',
          fixed: 'right',
          align: 'center',
          width: 147,
          scopedSlots: { customRender: 'action' },
        },
      ],
      url: {
        list: '/knowledges/devOpslist',
        delete: '/knowledges/delete',
        deleteBatch: '/knowledges/deleteBatch',
        exportXlsUrl: '/knowledges/exportXls',
        importExcelUrl: 'knowledges/importExcel',
      },
      dictOptions: {},
      condition: '{"delflag": 0}',
    }
  },
  created() {},
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
  },
  mounted() {},
  methods: {
    initDictConfig() {},
    handleOrderDetail: function (record) {
      this.$refs.modalDetailForm.edit(record)
      this.$refs.modalDetailForm.title = '详情'
      this.$refs.modalDetailForm.disableSubmit = true
    },
    fontClick: function (path) {
      // window.open(window._CONFIG['downloadUrl'] + '/' + path)
      window.open(window._CONFIG['domianURL'] + '/sys/common/downloadFile/' + path)
    },
  },
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
.table-page-search-wrapper .ant-form-inline .ant-form-item {
  margin-bottom: 0 !important;
}
/*表头样式*/
::v-deep .ant-table-thead > tr > th {
  text-align: center;
  white-space: nowrap;
}

/*内容对齐方式、省略显示*/
::v-deep .ant-table-tbody > tr > td {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;

  &:first-child,
  &:nth-child(2),
  &:nth-child(3),
  &:nth-child(4),
  &:nth-child(5) {
    text-align: center;
  }
}
</style>
