<template>
  <div class="wrap">
    <a-spin :spinning="spinning" wrapperClassName="panel-spin">
      <div class="content">
        <!--右侧工具栏-->
        <div v-if="operate === 'create'">
          <panel-elements v-if="hasElements" @startDrag="startDrag" :panelElementTypes="panelElementTypes"></panel-elements>
        </div>
        <div id='panelBox' class="panel" :class="{ 'panel-create': operate === 'create' }">
          <!--流程图工具栏-->
          <div class="toolbar" v-if="isReady && toolbar">
            <tool-bar
              :operate="operate"
              :panelType="value"
              :panelTypes="panelTypes"
              :portList="portList"
              :gridShow='gridShow'
              @save="save"
              @desktop="desktop"
              @edit="$emit('edit')"
              @cutPanel="delPanel"
              @setPanelType="setPanelType"
              @portList="portListShow"
              @grid='gridShow = !gridShow'
              @reload="getDevicePortStatus(true)"
            />
          </div>
          <!--流程图画板-->
          <div ref="canvasBox" :id="canvasId" class="panel-box" :class="toolbar ? 'panel-box-tool' : ''"></div>
        </div>
        <config-panel
          v-if="operate === 'create' && isReady"
          :operate="operate"
          :productId="productId"
          :selects="selects"
        ></config-panel>
      </div>
    </a-spin>
    <extend-form
      v-if="operate === 'create'"
      ref="extendForm"
      @batchCreateInterface="batchCreateInterface"
      @close="templateNode = null"
    ></extend-form>
    <tool-tip
      :left="tipLeft"
      :top="tipTop"
      :width="tipWidth"
      :height="tipHeight"
      :portInfo="portInfo"
      @hide="toolTipHide"
      @showDetail="showDetail"
    ></tool-tip>
    <port-list-modal
      ref="portListModal"
      :visible="listVisible"
      :type="value"
      :oid="oid"
      :deviceInfo="deviceInfo"
      @hide="listVisible = false"
      @showDetail="showDetail"
    ></port-list-modal>
    <port-detail ref="portDetail"></port-detail>
  </div>
</template>

<script>
const { Dnd } = Addon
import './index.less'
import ToolBar from './components/ToolBar/index.vue'
import ConfigPanel from './components/ConfigPanel/index.vue'
import PanelElements from './components/PanelElements.vue'
import FlowGraph from './graph'
import { Graph, Color, Dom, Shape, Edge, NodeView, Addon, DataUri } from '@antv/x6'
import dagre from 'dagre'
import { getAction, httpAction, postAction, putAction, deleteAction } from '@/api/manage'
import { globalGridAttr } from './models/global'
import './graph/shape'
import CreateElementsMixin from './mixins/CreateElementsMixin.js'
import EventMixin from './mixins/EventMixin.js'
import PanelConfigMixin from './mixins/PanelConfigMixin.js'
import PanelStatusMixin from './mixins/PanelStatusMixin.js'
import ExtendForm from './components/ExtendForm.vue'
import ToolTip from './components/ToolTip.vue'
import PortListModal from './components/PortListModal.vue'
import PortDetail from './components/PortDetail.vue'
import { v4 as uuidv4 } from 'uuid'
import { svgStore } from "@/components/tools/SvgIcon/svgStore"
const tryToJSON = (val) => {
  try {
    return JSON.parse(val)
  } catch (error) {
    return val
  }
}
// 布局方向
const dir = 'LR' // LR RL TB BT

export default {
  name: 'PanelTopo',
  components: {
    ToolBar,
    ConfigPanel,
    PanelElements,
    ExtendForm,
    ToolTip,
    PortListModal,
    PortDetail,
  },
  mixins: [CreateElementsMixin, EventMixin, PanelConfigMixin, PanelStatusMixin],
  props: {
    productId: {
      type: String,
      default: '',
      required: true,
    },
    deviceInfo: {
      type: Object,
      default: () => null,
      required: false,
    },
    operate: {
      //当前页面为仅查看,查看可编辑、编辑：show、show-edit、create
      type: String,
      default: 'show',
    },
    toolbar: {
      type: Boolean,
      default: false,
    },
    canvasId: {
      type: String,
      default: 'devicePanelId',
    },
    value: {
      type: String,
      default: '0',
      required: false,
    },
    portList: {
      type: Boolean,
      default: false,
      required: false,
    },
  },
  data() {
    return {
      panelData: '',
      isPanel: '',
      visible: false,
      graph: '',
      isReady: false,
      topoInfo: {},
      url: {
        roomEdit: '/product/product/savePanel',
      },
      picSrc: window._CONFIG['staticDomainURL'],
      apiUrl: window._CONFIG['domianURL'],
      downloadUrl: window._CONFIG['downloadUrl'] + '/',
      dnd: Object,
      globalGridAttr: globalGridAttr,
      selects: [],
      templateNode: null,
      spinning: false,
      panelTypes: [
        {
          key: '0',
          label: '正面板',
        },
        {
          key: '1',
          label: '反面板',
        },
      ],
      tipLeft: -10000,
      tipTop: -10000,
      tipWidth: 0,
      tipHeight: 0,
      portInfo: {},
      listVisible: false,
      oid:"",
      panelElementTypes:[],
      hasElements:false,
      gridShow:true,
      resizeObserver:null,

    }
  },
  created() {
   
  },
  mounted() {
    this.addResizeObserver()
    this.createPanelTopo()
    //设置snmp参数信息
    this.globalGridAttr.physicalPortList = []
    this.setConnectInfo()

  },
  computed: {},
  beforeDestroy() {
    this.delResizeObserver()
  },
  watch: {
    // 控制网格的显示隐藏
    gridShow(e){
      if(e){
        this.graph.showGrid();
      }else{
        this.graph.hideGrid();
      }
    },
    value() {
      this.createPanelTopo()
    },
  },
  methods: {
    addResizeObserver() {
      let app = document.getElementById("panelBox")
      this.resizeObserver = new ResizeObserver((entries) => {
        this.graph?.resize(entries[0].contentRect.width, entries[0].contentRect.height-38)
        if(this.operate === "create"){
          this.graph?.centerContent()
        }
        else {
          this.graph?.zoomToFit()
        }
      })
      this.resizeObserver.observe(app)
    },
    delResizeObserver() {
      let app = document.getElementById("panelBox")
      if (app && this.resizeObserver) {
        this.resizeObserver.unobserve(app)
        this.resizeObserver = null
      }
    },
    //获取面板元素类型列表
    async getEleTypeList(){
      if(this.hasElements)return;
      await getAction('device/panelType/list', { pageSize: 10000 })
        .then((res) => {
          if (res.success && res.result) {
            let records = res.result.records
            if (records && records.length > 0) {
              this.panelElementTypes = records
            }
          }
          this.hasElements = true;
        })
        .catch((error) => {
          this.hasElements= true;
        })
    },
    createPanelTopo() {
      this.isReady = false;
      this.$nextTick(() => {
        this.initGraph()
        // this.$ls.remove('too-json')
        this.getPanelInfoById()
        this.setup()
        // this.getExtendNodeIndex();
      })
      this.visible = true
    },
    async getPanelInfoById() {
      this.topoInfo = {}
      await this.getEleTypeList();
      this.spinning = true
      getAction('/product/panelInfo/queryByProductId', { productId: this.productId, topoType: this.value })
        .then((res) => {
          if (res.success && res.result) {
            let panelTopodata = res.result
            if (panelTopodata) {
              this.topoInfo = panelTopodata
              // console.log('编辑面板脱坡 == ', this.topoInfo)
              this.dataTransferCell(this.topoInfo.nodeList)
            }
          } else {
            //编辑时无面板数据时 自动添加面板节点
            if (this.operate === 'create') {
              let node = this.createTopoNode('panelNode', {})
              this.graph.addNode(node)
            }
          }
          this.spinning = false
          this.initConfig()
          if (this.operate === 'show') {
            this.getDevicePortStatus()
          }
          if(this.operate === "create"){
            this.graph.centerContent()
          }
          else {
            this.graph.zoomToFit()
          }
        })
        .catch((error) => {
          this.spinning = false
        })
    },
    onClose() {
      this.graph.dispose()
    },
    initGraph() {
      if (this.graph) {
        this.graph.dispose()
        this.graph = null
      }

      this.graph = FlowGraph.init({ operate: this.operate, id: this.canvasId })
      if(this.operate === "create"){
        this.gridShow = true;
        this.graph.showGrid();
      }
      else{
        this.gridShow = false;
        this.graph.hideGrid();
      }
      this.dnd = new Dnd({
        target: this.graph,
        scaled: false,
        animation: false,
        validateNode(droppingNode, options) {
          return true
        },
      })
      this.isReady = true
    },
    //图元区拖拽节点
    startDrag(e, data) {
      if (!data.nodeType) {
        return
      }
      const nodeType = data.nodeType
      let node = this.createTopoNode(nodeType, data)
      this.dnd.start(node, e)
    },
    save(isSwitch, panelType) {
      if (this.topoInfo.id === undefined) {
        this.topoInfo.id = uuidv4()
      }
      let topoJson = this.graph.toJSON()
      //生成后台保存的节点数据
      let xlink = 'xlink:href'
      topoJson.cells.forEach((item, i) => {
        if (item.attrs && item.attrs.image && item.attrs.image[xlink]) {
          let images = item.attrs.image[xlink].split(this.apiUrl + '/')
          item.attrs.image[xlink] = images[1]
        }
      })
      let nodeAndEdge = this.cellTransferData(topoJson.cells)
      Object.assign(this.topoInfo, nodeAndEdge)
      //保存拓扑图 同步配置信息
      this.saveTopoConfig()
      // console.log('要保存的数据 === ', this.topoInfo)
      if (isSwitch) {
        this.$emit('input', panelType)
      }
      if (this.spinning) return
      if (this.topoInfo.productId === undefined && this.productId) {
        this.topoInfo.productId = this.productId
        this.spinning = true
        postAction('/product/panelInfo/add', this.topoInfo)
          .then((res) => {
            if (res.success) {
              this.$message.success('保存成功！')
            }
            this.spinning = false
          })
          .catch((error) => {
            this.spinning = false
          })
      } else if (this.topoInfo.productId === this.productId) {
        this.spinning = true
        putAction('/product/panelInfo/edit', this.topoInfo)
          .then((res) => {
            if (res.success) {
              this.$message.success('保存成功！')
            } else {
            }
            this.spinning = false
          })
          .catch((error) => {
            this.spinning = false
          })
      }
    },
    desktop() {
      this.$emit('desktop')
    },
    setPanelType(e) {
      if (this.operate === 'create') {
        this.$confirm({
          title: '需要保存当前面板吗？',
          okText: '保存',
          cancelText: '不保存',
          onOk: () => {
            this.save(true, e)
          },
          onCancel: () => {
            this.$emit('input', e)
          },
        })
      } else {
        this.$emit('input', e)
      }
    },
    exportPanel(type) {
      if (type === 'svg') {
        this.graph.toSVG(
          (dataUri) => {
            // 下载
            DataUri.downloadDataUri(DataUri.svgToDataUrl(dataUri), 'chart.svg')
          },
          {
            preserveDimensions: {
              width: '100%',
              height: '100%',
            },
            stylesheet: `
            .x6-graph-svg-viewport {
              transform: matrix(1, 0, 0, 1, 0, 0);
            }
          `,
          }
        )
      } else if (type === 'png') {
        this.graph.toPNG((dataUri) => {
          // 下载
          DataUri.downloadDataUri(dataUri, 'chart.png')
        })
      }
    },
    delPanel() {
      if (this.topoInfo.id === undefined) {
        this.$message.warning('面板不存在！')
        return
      }
      this.$confirm({
        title: '确定要删除当前面板吗？',
        okText: '删除',
        cancelText: '取消',
        onOk: () => {
          deleteAction('/product/panelInfo/deleteBatch', {
            ids: this.topoInfo.id,
          }).then((res) => {
            if (res.success) {
              this.$message.success(res.message)
              this.createPanelTopo()
            } else {
              this.$message.warning(res.message)
            }
          })
        },
        onCancel: () => {},
      })
    },
    portListShow() {
      this.oid = globalGridAttr.connectInfo.oid
      this.listVisible = true
    },
    showDetail(e) {
      this.$refs.portDetail.show(e)
    },
  },
}
</script>

<style scoped lang="less">
.panel {
  height: 100%;
  width: 100%;
  border: 1px solid #eee;
}
.panel-create {
  width: calc(100% - 524px);
  min-width: 560px;
}
.panel .toolbar {
  height: 38px;
  background-color: #f7f9fb;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  border-top: hidden;
}

.wrap .content {
  display: flex;
  height: 100%;
  justify-content: space-around;
}

::v-deep .sider {
  box-shadow: none;
  padding: 0px !important;
  border: none !important;
  border-right: 1px solid rgba(0, 0, 0, 0.08) !important;
  margin-right: 10px;
  border-radius: 3px;
}

.panel-box {
  height: 100%;
  width: 100%;
  flex: 1;
}
.panel-box-tool {
  height: calc(100% - 38px);
}
::v-deep .x6-graph-scroller {
  width: 100% !important;
}

::v-deep .x6-graph {
  box-shadow: none !important;
}

::v-deep .x6-widget-stencil {
  margin: 5px;
  background-color: white !important;
}
::v-deep .x6-node foreignObject > body {
  font-size: inherit;
}
.panel-spin {
  height: 100%;
  width: 100%;
  /deep/ .ant-spin-container {
    height: 100%;
    width: 100%;
  }
}
</style>