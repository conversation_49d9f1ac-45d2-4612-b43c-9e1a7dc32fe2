import Vue from 'vue'
import App from './topoApp.vue'
import store from '../store'
import Storage from 'vue-ls'
import Antd, { version } from 'ant-design-vue'
// import { Modal } from 'ant-design-vue';
import 'ant-design-vue/dist/antd.less'
import JModal from '@/components/jeecg/JModal'
Vue.component(JModal.name, JModal)
// Vue.component(Modal.name, Modal)
Vue.use(Storage)
Vue.use(Antd)

new Vue({
    store,
    render: h => h(App)
  }).$mount('#app')
