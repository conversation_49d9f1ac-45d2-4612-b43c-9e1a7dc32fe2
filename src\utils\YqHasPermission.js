import { USER_AUTH } from '@/store/mutation-types'

export const yqHasPermission=function (text){
  let permissionList = [];
  //let authList = Vue.ls.get(USER_AUTH);
  let authList = JSON.parse(sessionStorage.getItem(USER_AUTH) || "[]");
  for (let auth of authList) {
    if(auth.type != '2') {
      permissionList.push(auth);
    }
  }
  if (permissionList === null || permissionList === "" || permissionList === undefined||permissionList.length<=0) {
    return false;
  }
  let permissions = [];
  for (let item of permissionList) {
    //权限策略1显示2禁用
    if(item.type != '2'){
      //update--begin--autor:wangshuai-----date:20200729------for：按钮权限，授权标识的提示信息是多个用逗号分隔逻辑处理 gitee#I1OUGU-------
      if(item.action){
        if(item.action.includes(",")){
          let split = item.action.split(",")
          for (let i = 0; i <split.length ; i++) {
            if(!split[i] ||split[i].length==0){
              continue;
            }
            permissions.push(split[i]);
          }
        }else{
          permissions.push(item.action);
        }
      }
      //update--end--autor:wangshuai-----date:20200729------for：按钮权限，授权标识的提示信息是多个用逗号分隔逻辑处理 gitee#I1OUGU------
    }
  }
  if (!permissions.includes(text)) {
    return false
  }else {
    return true
  }
}
