
<template>
  <a-card :loading='loading' style='width: 100%; height: 100%'>
    <div slot='title'>
      <a-icon type='pie-chart' />
      工单统计
      <!--      <a-tooltip title='刷新'>-->
      <!--        <a-icon type='sync' style='color: #ccc; cursor: pointer; font-size: 14px' @click='loadBusiness' />-->
      <!--      </a-tooltip>-->
    </div>
    <!--    <a slot='extra' @click='goPage(4)'>更多-->
    <!--      <a-icon type='double-right' />-->
    <!--    </a>-->
    <div slot='extra' class="header-right">
      <div class="header-right-time">
        <span>选择日期:</span>
        <div class="time-range-span">
          <a-range-picker @change="onChange" />
        </div>
      </div>
      <a-button type='primary'  @click="search">
        查询
      </a-button>
    </div>
    <a-row :gutter="0" style='height: 100%'>
      <a-col :span="8" v-for='(item,idx) in orderData' :key='item.id' style='height: 50%;'>
        <div class='order-item'>
          <div class='order-item-content'>
            <span>{{item.name}}</span>
            <span class='order-value' :style='{color:colors[idx]}'>  {{item.value}}</span>
          </div>
        </div>
      </a-col>
      <a-col :span="8" v-for='(item,idx) in orderTypeData' :key='item.id' style='height: 50%;'>
        <div class='order-item'>
          <div class='order-item-content'>
            <span>{{item.name}}</span>
            <span class='order-value' :style='{color:colors[idx+3]}'>  {{item.number}}</span>
          </div>
        </div>
      </a-col>
    </a-row>
  </a-card>
</template>
<script>
import { getAction } from '@api/manage'

export default {
  name: 'WorkOrderStatistics',
  data(){
    return{
      time1: '',
      time2: '',
      orderData:[],
      orderTypeData: [],
      colors:['#579EF8',"#5DCAE9","#ED6A4D","#F2A042","#469A2D","#2D298D"],
      loading:false,
    }
  },
  created() {
    this.getCount();

  },
  mounted() {
  },
  methods:{
    getCount() {
      this.orderData = []
      getAction('/data-analysis/order/workOrderStatusStatistics', {
        time1: this.time1,
        time2: this.time2
      }).then((res) => {
        if (res.code == 200 && res.result) {
          this.orderData = res.result;
          this.workOrderTypeStatistics();
        }
      })
    },
    workOrderTypeStatistics() {
      getAction('/data-analysis/order/workOrderTypeStatistics', {
        time1: this.time1,
        time2: this.time2
      }).then((res) => {
        if (res.code == 200 && res.result) {
          this.orderTypeData = res.result;
          // this.orderData = this.orderData.concat(res.result)
        }
      })
    },
    onChange(dates, dateStrings) {
      this.time1 = dateStrings[0]
      this.time2 = dateStrings[1]
    },
    search() {
      this.getCount();
      // this.workOrderTypeStatistics();
    },
  }
}
</script>


<style scoped lang='less'>
.order-item{
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 1px 0 0 0 #f0f0f0, 0 1px 0 0 #f0f0f0, 1px 1px 0 0 #f0f0f0, 1px 0 0 0 #f0f0f0 inset, 0 1px 0 0 #f0f0f0 inset;
  .order-item-content{
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .order-value{
    margin-top: 12px;
    font-size:24px;
    font-width: 700;
  }
}
::v-deep .ant-card-body{
  height: calc(100% - 63px);
  padding:0px;
}
.header-right {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  .header-right-time {
    font-size: 0.175rem;
    font-family: PingFang SC;
    letter-spacing: 0px;
    font-weight: 100;
    color: #ffffff;
    display: flex;
    align-items: center;
    .time-range-span {
      margin-right: 0.4375rem;
      margin-left: 0.2rem;
    }
  }
}
</style>