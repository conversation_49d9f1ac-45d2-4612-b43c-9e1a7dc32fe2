<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <!-- 查询区域 -->
      <a-card :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="供应商名称">
                  <a-input
                    :maxLength="maxLength"
                    placeholder="请输入供应商名称"
                    :allowClear="true"
                    autocomplete="off"
                    v-model="queryParam.name"
                  ></a-input>
                </a-form-item>
              </a-col>

              <a-col :span="colBtnsSpan()">
                <span
                  class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                >
                  <a-button type="primary" @click="searchQuery">查询</a-button>
                  <a-button @click="searchReset" style="margin-left: 10px">重置</a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <!-- 查询区域-END -->

      <a-card :bordered="false" style="flex: auto" class="core">
        <!-- 操作按钮区域 -->
        <div class="table-operator">
          <a-button @click="handleAdd" v-has="'supplier:add'">新增</a-button>
          <a-button @click="handleExportXls('供应商')" v-has="'supplier:export'">导出</a-button>
          <a-button @click="handleTemplateXls()" v-has="'supplier:import'">下载模版</a-button>
          <a-upload v-has="'supplier:import'"
            name="file"
            :showUploadList="false"
            :multiple="false"
            :headers="tokenHeader"
            :action="importExcelUrl"
            @change="handleImportExcel"
          >
            <a-button>导入</a-button>
          </a-upload>
          <!-- 高级查询区域 -->
          <!-- <j-super-query :fieldList="superFieldList" ref="superQueryModal" @handleSuperQuery="handleSuperQuery"></j-super-query> -->
          <a-dropdown v-if="selectedRowKeys.length > 0" v-has="'supplier:delete'">
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item key="1" @click="batchDel">删除</a-menu-item>
            </a-menu>
            <a-button> 批量操作 <a-icon type="down" /></a-button>
          </a-dropdown>
        </div>

        <!-- table区域-begin -->
        <div>
          <a-table
            ref="table"
            bordered
            rowKey="id"
            :columns="columns"
            :dataSource="dataSource"
            :pagination="ipagination"
            :loading="loading"
            :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
            class="j-table-force-nowrap"
            @change="handleTableChange"
          >
            <template slot="htmlSlot" slot-scope="text">
              <div v-html="text"></div>
            </template>
            <template slot="imgSlot" slot-scope="text">
              <span v-if="!text" style="font-size: 14px">无图片</span>
              <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width: 80px; font-size: 14px" />
            </template>
            <template slot="fileSlot" slot-scope="text">
              <span v-if="!text" style="font-size: 14px">无文件</span>
              <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
                下载
              </a-button>
            </template>

            <span slot="action" slot-scope="text, record">
              <a style="color: #409eff" @click="handleDetailPage(record)">查看</a>
              <span v-has="'supplier:edit'">
                 <a-divider type="vertical" />
                 <a style="color: #409eff" @click="handleEdit(record)">编辑</a>
              </span>
             <span v-has="'supplier:delete'">
                <a-divider type="vertical" />
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a style="color: #409eff">删除</a>
              </a-popconfirm>
             </span>
            </span>
            <template slot="tooltip" slot-scope="text">
              <a-tooltip placement="topLeft" :title="text" trigger="hover">
                <div class="tooltip">
                  {{ text }}
                </div>
              </a-tooltip>
            </template>
          </a-table>
        </div>

        <cmdb-supplier-modal ref="modalForm" @ok="modalFormOk"></cmdb-supplier-modal>
        <!-- 下载模版 -->
        <iframe id="download" style="display: none"></iframe>
      </a-card>
    </a-col>
  </a-row>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import CmdbSupplierModal from './modules/CmdbSupplierModal'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'

export default {
  name: 'CmdbSupplierList',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components: {
    CmdbSupplierModal
  },
  data() {
    return {
      maxLength:50,
      description: '供应商管理页面',
      // 表头
      columns: [
        {
          title: '供应商名称',
          dataIndex: 'name'
        },
        {
          title: '联系人名称',
          dataIndex: 'contactName'
        },
        {
          title: '联系人邮箱',
          dataIndex: 'contactEmail',
          customCell: () => {
            let cellStyle = 'text-align: left'
            return { style: cellStyle }
          },
        },
        {
          title: '联系人电话',
          dataIndex: 'contactTel',
          customCell: () => {
            let cellStyle = 'width:200px'
            return { style: cellStyle }
          },
        },
        {
          title: '网站',
          dataIndex: 'website',
          scopedSlots: { customRender: 'tooltip' },
          customCell: () => {
            let cellStyle = 'text-align: left;min-width:200px;max-width:400px'
            return { style: cellStyle }
          },
        },
        {
          title: '操作',
          dataIndex: 'action',
          fixed: 'right',
          align: 'center',
          width: 147,
          scopedSlots: { customRender: 'action' },
        },
      ],
      url: {
        list: '/supplier/cmdbSupplier/list',
        delete: '/supplier/cmdbSupplier/delete',
        deleteBatch: '/supplier/cmdbSupplier/deleteBatch',
        exportXlsUrl: '/supplier/cmdbSupplier/exportXls',
        importExcelUrl: 'supplier/cmdbSupplier/importExcel',
        downloadUserTemplateUrl: '/supplier/cmdbSupplier/downloadUserTemplate',
        downloadTemplateXlsUrl: '/supplier/cmdbSupplier/downloadTemplate',
      },
      disableMixinCreated: true,
    }
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
    downloadTemplateXlsUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.downloadTemplateXlsUrl}`
    },
  },
  activated() {
    this.loadData()
  },
  methods: {
    //excel模板
    handleTemplateXls() {
      const path = this.downloadTemplateXlsUrl
      document.getElementById('download').src = path
    }
  },
}
</script>
<style lang="less" scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
</style>
