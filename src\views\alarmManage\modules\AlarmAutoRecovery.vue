<template>
  <div>
    <div v-if="showAutoRecovery" class='content-box'>
      <div class="headerBox">
        <div class="title-box">
          <span class="title">告警自动恢复</span>
        </div>
        <a-button type="primary" @click='viewConfig(record, "log")' style="margin-bottom: 10px;">查看执行日志</a-button>
      </div>

      <table class="gridtable">
        <tr>
          <td class="leftTd">脚本场景名称</td>
          <td class="rightTd">{{record.sceneName}} </td>
          <td class="leftTd">执行时间</td>
          <td class="rightTd">{{record.createTime}}</td>
        </tr>
        <tr>
          <td class="leftTd">执行状态</td>
          <td class="rightTd">{{record.executeStatus}} </td>
          <td class="leftTd">执行结果</td>
          <td class="rightTd">{{record.successFlag === 'true' ? '成功': '失败'}}</td>
        </tr>
      </table>
      <view-config-modal ref='viewConfigModal'></view-config-modal>
    </div>
  </div>
</template>
<script>
  import {getAction} from '@/api/manage'
  import ViewConfigModal from '@views/networkManagement/networkDevice/modules/ViewConfigModal.vue'
  export default {
    name: 'AlarmAutoRecovery',
    props:{
      alarmRuleId:{
        type:String,
        required:false,
        default:''
      }
    },
    components: {
      ViewConfigModal
    },
    data() {
      return {
        record: {},
        showAutoRecovery: false,
        url: {
          getSelfRecoveryLog: '/alarm/alarmHistory/getSelfRecoveryLog'
        }
      }
    },
    watch:{
      alarmRuleId:{
        handler(nval,oval){
          if (nval&&nval.length>0){
            this.getSelfRecoveryLog(nval)
          }
        },
        deep:true,
        immediate: true,
      }
    },
    methods: {
      // 获取告警自动恢复信息
      getSelfRecoveryLog(id) {
        this.record={}
        this.showAutoRecovery=false
        if (!id) {
          return false
        }
        getAction(this.url.getSelfRecoveryLog, {
          id: id
        }).then(res => {
          if (res.success && res.result) {
            this.showAutoRecovery = true
            this.record = res.result
          }
        }).catch(err => {
          this.showAutoRecovery = false
        })
      },
      // 查看执行日志
      viewConfig(record) {
        this.$refs.viewConfigModal.loadConfig(record.executeLog)
        this.$refs.viewConfigModal.title = "日志详情"
        this.$refs.viewConfigModal.disableSubmit = true
      }
    }
  }
</script>
<style lang="less" scoped>
  table.gridtable {
    font-family: verdana, arial, sans-serif;
    font-size: 14px;
    color: #606266;
    border-width: 1px;
    border-color: #e8e8e8;
    border-collapse: collapse;
    text-align: left;
    width: 100%;
  }

  table.gridtable td {
    border-width: 1px;
    border-style: solid;
    border-color: #e8e8e8;
  }

  .leftTd {
    width: 17%;
    background-color: #FAFAFA;
    padding: 16px 24px;
    text-align: center;
  }

  .rightTd {
    width: 35%;
    padding: 16px 24px;
  }

  .headerBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .content-box{
    margin-bottom:20px;
    .title-box {
      font-size: 14px;
      margin-bottom: 10px;

      .title {
        padding-left: 7px;
        border-left: 4px solid #1e3674;
      }
    }
  }

</style>