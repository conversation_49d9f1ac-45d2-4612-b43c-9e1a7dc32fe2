<template>
  <div class="rigthCont">
    <a-card style="width: 100%; border: 0" size="default">
      <div slot="cover">
        <span
          class="coverSpan"
          style="
            padding-top: 2.2%;
            font-family: PingFangSC-Regular;
            font-size: 16px;
            color: rgba(0, 0, 0, 0.65);
            padding-left: 8px;
            border-bottom: 1px solid #f1f0f0;
            padding-bottom: 12px;
            display: block;
          "
        >
          <span style="border-left: 4px solid #1890ff; margin-right: 11px"></span>
          快捷请求
        </span>
      </div>
      <a-row :gutter="[16, 16]" style="text-align: center; margin: -7px" class="boxRow">
<!--        <a-col :sm="4" :xs="24" class="col4" @click="affairClick(6)" style="padding: 14px">
          <span
            class="iconBackPhoto"
            style="
              width: 48px;
              height: 48px;
              display: block;
              margin: 0 auto;
              background: #009e15;
              border-radius: 8px;
              line-height: 48px;
              color: #fff;
              font-size: 22px;
            "
          >
            <a-icon type="setting" theme="filled" class="icon" />
          </span>
          <p class="col4Font">委办局请求</p>
        </a-col>-->
        <a-col :sm="4" :xs="24" class="col4" @click="affairClick(2)" style="padding: 14px">
          <span
            class="iconBackPhoto"
            style="
              width: 48px;
              height: 48px;
              display: block;
              margin: 0 auto;
              background: #ffc200;
              border-radius: 8px;
              line-height: 48px;
              color: #fff;
              font-size: 22px;
            "
          >
            <a-icon type="question-circle" theme="filled" class="icon" />
          </span>
          <p class="col4Font">问题请求</p>
        </a-col>
        <a-col :sm="4" :xs="24" class="col4" @click="affairClick(3)" style="padding: 14px">
          <span
            class="iconBackPhoto"
            style="
              width: 48px;
              height: 48px;
              display: block;
              margin: 0 auto;
              background: #009dff;
              border-radius: 8px;
              line-height: 48px;
              color: #fff;
              font-size: 22px;
            "
          >
            <a-icon type="interaction" theme="filled" class="icon" />
          </span>
          <p class="col4Font">变更请求</p>
        </a-col>
        <a-col :sm="4" :xs="24" class="col4" @click="affairClick(4)" style="padding: 14px">
          <span
            class="iconBackPhoto"
            style="
              width: 48px;
              height: 48px;
              display: block;
              margin: 0 auto;
              background: #ff7d00;
              border-radius: 8px;
              line-height: 48px;
              color: #fff;
              font-size: 22px;
            "
          >
            <a-icon type="tag" theme="filled" class="icon" />
          </span>
          <p class="col4Font">发布请求</p>
        </a-col>
        <a-col
          :sm="4"
          :xs="24"
          style="width: 20%; height: 101px; line-height: 85px; cursor: pointer; padding: 14px"
          @click="affairClick(1)"
        >
          <span
            class="iconBackPhoto"
            style="
              width: 48px;
              height: 48px;
              display: block;
              margin: 0 auto;
              background: rgb(168, 148, 246);
              border-radius: 8px;
              line-height: 48px;
              color: #fff;
              font-size: 22px;
            "
          >
            <a-icon type="folder" theme="filled" class="icon" />
          </span>
          <p class="col4Font">事件请求</p>
        </a-col>
      </a-row>
    </a-card>
    <a-card style="width: 100%; border: 0; margin-top: 16px; height: 400px" size="default" class="calendar">
      <div slot="cover">
        <span
          class="coverSpan"
          style="
            padding-top: 2.2%;
            font-family: PingFangSC-Regular;
            font-size: 16px;
            color: rgba(0, 0, 0, 0.65);
            padding-left: 8px;
            border-bottom: 1px solid #f1f0f0;
            padding-bottom: 12px;
            display: block;
          "
        >
          <span style="border-left: 4px solid #1890ff; margin-right: 11px"></span>
          日程表
        </span>
      </div>
      <!-- <el-calendar v-model="value"> -->
      <!-- <template
          slot="dateCell"
          slot-scope="{date, data}" style="height:68px">
          <div class="calendar-day">{{ data.day.split('-').slice(2).join('-') }}</div>
          <div v-for="item in calendarData" >
            <div v-if="(item.months).indexOf(data.day.split('-').slice(1)[0])!=-1">
              <div v-if="(item.days).indexOf(data.day.split('-').slice(2).join('-'))!=-1" :class="'li-0'+item.type">
                <el-tooltip class="item" effect="dark" :content="item.things" placement="right">
                  <div class="is-selected">{{item.things}}</div>
                </el-tooltip>
              </div>
              <div v-else></div>
            </div>
            <div v-else></div>
          </div>
        </template>
      </el-calendar> -->
      <el-calendar
        v-model="value"
        id="calendar"
        style="padding-top: 0%; padding-right: 2.5%; padding-bottom: 2.5%; padding-left: 2.5%"
      >
        <template slot="dateCell" slot-scope="{ data }">
          <div style="position: relative; text-align: center">
            {{ data.day.split('-').slice(2)[0] }}
            <div v-for="item in calendarData" :key="item.id">
              <div v-if="data.day.split('-').slice(1)[0] == item.months">
                <div
                  v-if="item.days.indexOf(data.day.split('-').slice(2).join('-') ) != -1 &amp;&amp; item.type != -1"
                  :class="'li-0' + item.type"
                  class="calendar-for"
                >
                  <span>{{ data.day.split('-').slice(2)[0] }} </span>
                </div>
              </div>
              <div v-else></div>
            </div>
          </div>
        </template>
      </el-calendar>
    </a-card>
    <a-card style="width: 100%; border: 0; margin-top: 16px; height: 327px" size="default">
      <div slot="cover">
        <span
          class="coverSpan"
          style="
            padding-top: 2.2%;
            font-family: PingFangSC-Regular;
            font-size: 16px;
            color: rgba(0, 0, 0, 0.65);
            padding-left: 8px;
            border-bottom: 1px solid #f1f0f0;
            padding-bottom: 12px;
            display: block;
          "
        >
          <span style="border-left: 4px solid #1890ff; margin-right: 11px"></span>
          知识搜索
        </span>
      </div>
      <div style="margin-right: 6px; margin-top: 12px; position: relative">
        <a-input-search v-model="searchVal" allow-clear id="aInp" class="aInp" @focus="tips" @change="inputChang" />
        <!--        placeholder="请输入搜索内容"-->
        <!--        enter-button="搜索"-->
        <!--        size="large"-->
        <!--        @search="onSearch"-->
        <ul class="aUi" id="aUi">
          <li
            v-for="(item, index) in NewItems"
            id="aSearchClass"
            :key="index"
            :value="item.id"
            v-text="item.title"
            class="ali"
            @click="LiValue(item)"
          ></li>
        </ul>
        <p style="color: red; margin-top: 32px" v-if="f">热门搜索</p>
        <p style="color: red; margin-top: 32px" v-if="!f">搜索结果</p>
        <div v-for="(item, index) in hotSearchList" id="aClass" style="float: left" v-if="f">
          <!-- <a href="#"
             style="color: rgb(128 128 128);margin-right:50px">{{item.title}}</a> -->
          <a
            v-if="index % 2 == 0"
            href="#"
            style="color: rgb(128 128 128)"
            @click="handleDetail(item)"
            :title="item.title"
            >{{ item.title }}</a
          >
          <a
            v-if="index % 2 != 0"
            href="#"
            style="color: rgb(128 128 128)"
            @click="handleDetail(item)"
            :title="item.title"
            >{{ item.title }}</a
          >
        </div>
        <div v-for="(item, index) in searchList" class="aSearchClass" style="width: 50%; float: left" v-if="!f">
          <!-- <a href="#"
             style="color: rgb(128 128 128);margin-right:50px">{{item.title}}</a> -->
          <a
            v-if="index % 2 == 0"
            href="#"
            style="color: rgb(128 128 128)"
            @click="handleDetail(item)"
            :title="item.title"
            >{{ item.title }}</a
          >
          <a
            v-if="index % 2 != 0"
            href="#"
            style="color: rgb(128 128 128)"
            @click="handleDetail(item)"
            :title="item.title"
            >{{ item.title }}</a
          >
        </div>
      </div>
      <ump-knowledge-modal ref="modalForm"></ump-knowledge-modal>
    </a-card>
  </div>
</template>
<script>
import { getAction, postAction } from '@/api/manage'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import UmpKnowledgeModal from './UmpKnowledgeModal'
export default {
  name: 'homeRight',
  components: {
    UmpKnowledgeModal,
  },
  data() {
    return {
      // calendarData: [
      //   // { months: ['09', '11'],days: ['15'],things: '看电影' },
      //   // { months: ['10', '11'], days: ['03'],things: 'didi' },
      //   // { months: ['11'], days: ['02'],things: '看星星' },
      // ],
      value: new Date(),
      calendarData: [],
      searchVal: '',
      list: [],
      url: {
        getHotSearch: '/knowledge/getHotSearch',
        getFuzzyQuery: '/knowledge/getFuzzyQuery',
        getCalendar: '/workbench/getCalendar',
        updateSearchesNum: '/knowledge/updateSearchesNum',
      },
      hotSearchList: [],
      f: true,
      searchList: [],
    }
  },
  computed: {
    NewItems() {
      var _this = this
      var NewItems = []
      this.list.map(function (list) {
        if (list.title.search(_this.searchVal) !== -1) {
          NewItems.push(list)
        }
      })
      return NewItems
    },
  },
  mounted() {
    this.initData();
      this.$nextTick(() => {
        // 点击上个月
        let prevBtn1 = document.querySelector('.el-calendar__button-group .el-button-group>button:nth-child(1)')
        prevBtn1.addEventListener('click', () => {
          var date = new Date(this.value) //时间戳为10位需*1000，时间戳为13位的话不需乘1000
          var Y = date.getFullYear() + '-'
          var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-'
          var D = date.getDate()

          getAction(this.url.getCalendar, { createTime_begin: Y + M + D }).then((res) => {
            if (res.success) {
              this.calendarData = res.result
            } else {
              this.$message.error(res.message)
            }
          })
        })
        // 点击今天
        let prevBtn2 = document.querySelector('.el-calendar__button-group .el-button-group>button:nth-child(2)')
        prevBtn2.addEventListener('click', () => {
          var date = new Date(this.value) //时间戳为10位需*1000，时间戳为13位的话不需乘1000
          var Y = date.getFullYear() + '-'
          var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-'
          var D = date.getDate()
          getAction(this.url.getCalendar, { createTime_begin: Y + M + D }).then((res) => {
            if (res.success) {
              this.calendarData = res.result
            } else {
              this.$message.error(res.message)
            }
          })
        })
        // 点击下个月
        let prevBtn3 = document.querySelector('.el-calendar__button-group .el-button-group>button:nth-child(3)')
        prevBtn3.addEventListener('click', () => {
          var date = new Date(this.value) //时间戳为10位需*1000，时间戳为13位的话不需乘1000
          var Y = date.getFullYear() + '-'
          var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-'
          var D = date.getDate()

          getAction(this.url.getCalendar, { createTime_begin: Y + M + D }).then((res) => {
            if (res.success) {
              this.calendarData = res.result
            } else {
              this.$message.error(res.message)
            }
          })
        })
      });
      document.addEventListener('click', (e) => {
        let thisClassName = e.target.className
        if (thisClassName != 'aUi' && thisClassName != 'ant-input' && thisClassName != 'aInp') {
          if (document.getElementsByClassName('aUi')[0]) {
            document.getElementsByClassName('aUi')[0].style.display = 'none'
          }
        } else {
          if (document.getElementsByClassName('aUi')[0]) {
            document.getElementsByClassName('aUi')[0].style.display = 'block'
          }
        }
      })
  },
  methods: {
    initData() {
      var date = new Date(this.value) //时间戳为10位需*1000，时间戳为13位的话不需乘1000
      var Y = date.getFullYear() + '-'
      var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-'
      var D = date.getDate()
      getAction(this.url.getCalendar, { createTime_begin: Y + M + D }).then((res) => {
        if (res.success) {
          this.calendarData = res.result
        } else {
          this.$message.error(res.message)
        }
      })
      getAction(this.url.getHotSearch).then((res) => {
        if (res.success) {
          this.hotSearchList = res.result
        } else {
          this.$message.error(res.message)
        }
      })
    },

    affairClick(index) {
      if (index == '1') {
        // this.$router.replace('/event/application')
        this.$router.push({ path: '/event/application', query: { id: index } })
      } else if (index == '2') {
        // this.$router.replace('/question/application')
        this.$router.push({ path: '/question/application', query: { id: index } })
      } else if (index == '3') {
        // this.$router.replace('/change/apply')
        this.$router.push({ path: '/change/apply', query: { id: index } })
      } else if (index == '4') {
        // this.$router.replace('/release/apply')
        this.$router.push({ path: '/release/apply', query: { id: index } })
      } else if (index == '5') {
        // this.$router.replace('/itilconfigitemlibrary/ItilConfigItemLibraryList')
        this.$router.push({ path: '/itilconfigitemlibrary/ItilConfigItemLibraryList', query: { id: index } })
      } else if (index == '6') {
        // this.$router.replace('/itilconfigitemlibrary/ItilConfigItemLibraryList')
        this.$router.push({ path: '/busQuestion/application', query: { id: index } })
      }
    },
    tips() {
      if (document.getElementsByClassName('aUi')[0]) {
        document.getElementsByClassName('aUi')[0].style.display = 'block'
      }
    },

    LiValue(data) {
      this.searchList = []
      this.searchVal = data.title
      if (document.getElementsByClassName('aUi')[0]) {
        document.getElementsByClassName('aUi')[0].style.display = 'none'
      }
      this.searchList.push(data)
      this.f = false
      this.updateSearchesNum(data)
    },
    updateSearchesNum(data) {
      postAction(this.url.updateSearchesNum, { id: data.id }).then((res) => {})
    },
    inputChang() {
      this.f = !this.searchVal;
      getAction(this.url.getFuzzyQuery, { title: this.searchVal }).then((res) => {
        if (res.success) {
          this.searchList = res.result;
          this.list = res.result;
        } else {
          this.$message.error(res.message)
        }
      })
    },
    handleDetail: function (record) {
      this.$refs.modalForm.edit(record)
      this.$refs.modalForm.title = '详情'
      this.$refs.modalForm.disableSubmit = true
    },
  },
}
</script>
<style scoped>
.calendar /deep/ .ant-card-body {
  /*padding-top: 0;*/
}
.calendar /deep/ .el-calendar-table .el-calendar-day {
  /*padding: 22px;*/
  height: 1%;
  padding: 8%;
}
.calendar /deep/ .el-calendar .el-calendar__body {
  padding: 0px 20px 0px;
}
.calendar /deep/ .el-calendar-table thead th {
  padding: 11px 0;
  text-align: center;
}
.calendar-day {
  text-align: center;
  color: #202535;
  line-height: 24px;
  font-size: 14px;
}
.is-selected {
  color: #f8a535;
  font-size: 10px;
  margin-top: 5px;
  background-color: #25b591;
}
.calendar /deep/ .el-calendar-table td.is-selected {
  color: #cccccc;
}
#calendar .el-button-group > .el-button:not(:first-child):not(:last-child):after {
  content: '当月';
}

.col4 {
  width: 20%;
  height: 103px;
  border-right: 2px dashed rgb(232, 232, 232);
  line-height: 85px;
  cursor: pointer;
}
.col4Font {
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
  line-height: 50px;
  white-space: nowrap;
}
.aUi {
  padding-left: 0px;
  background: #fff;
  position: absolute;
  width: 100%;
  height: 166px;
  display: none;
  border-left: 1px solid #d9d9d9;
  border-bottom: 1px solid #d9d9d9;
  border-right: 1px solid #d9d9d9;
  overflow: hidden;
  overflow-y: auto;
}
.aUi .ali {
  list-style: none;
  height: 18%;
  line-height: 38px;
  padding-left: 12px;
  cursor: pointer;
}
.ali:hover {
  background: #f1f1f1;
}

.left-wrap /deep/ .el-calendar-table .el-calendar-day {
  padding: 22px;
}
.left-wrap /deep/ .el-backtop,
.el-calendar-table td.is-today p {
  height: 30px;
  width: 30px;
  color: white;
  border-radius: 15px;
  line-height: 30px;
  margin: 0 auto;
  margin-top: -6px;
  background-image: linear-gradient(to right, #2160dc, #4880f0);
}
.budge {
  width: 10px;
  height: 10px;
  border-radius: 5px;
  margin: 5px auto;
}
.red {
  background-color: #c9413f;
}
.green {
  background-color: #25b591;
}
.orange {
  background-color: #ee915c;
}

.li-00 {
  color: #5f5f5f;
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
}

.li-01 {
  background: #199e26 !important;
  color: #fff;
  position: absolute;
  top: 0;
  width: 110%;
  top: -12%;
  left: -5.25%;
  padding: 6% !important;
}
.li-02 {
  background: #f12929 !important;
  color: #fff;
  position: absolute;
  width: 110%;
  top: -12%;
  left: -5.25%;
  padding: 6% !important;
}
#aClass {
  width: 33%;
  display: -webkit-box; /*作为弹性伸缩盒子模型显示*/
  -webkit-line-clamp: 1; /*显示的行数；如果要设置2行加...则设置为2*/
  overflow: hidden; /*超出的文本隐藏*/
  text-overflow: ellipsis; /* 溢出用省略号*/
  -webkit-box-orient: vertical; /*伸缩盒子的子元素排列：从上到下*/
}
#aSearchClass {
  width: 100%;
  display: -webkit-box; /*作为弹性伸缩盒子模型显示*/
  -webkit-line-clamp: 1; /*显示的行数；如果要设置2行加...则设置为2*/
  overflow: hidden; /*超出的文本隐藏*/
  text-overflow: ellipsis; /* 溢出用省略号*/
  -webkit-box-orient: vertical; /*伸缩盒子的子元素排列：从上到下*/
}
.aSearchClass {
  width: 100%;
  display: -webkit-box; /*作为弹性伸缩盒子模型显示*/
  -webkit-line-clamp: 1; /*显示的行数；如果要设置2行加...则设置为2*/
  overflow: hidden; /*超出的文本隐藏*/
  text-overflow: ellipsis; /* 溢出用省略号*/
  -webkit-box-orient: vertical; /*伸缩盒子的子元素排列：从上到下*/
}
@media (min-width: 1000px) and (max-width: 1680px) {
  .col4Font {
    font-size: 10px;
  }
  .iconBackPhoto {
    width: 30px !important;
    height: 30px !important;
    line-height: 26px !important;
  }
  .boxRow div {
    padding: 0 !important;
    padding-top: 18px !important;
  }
  .icon {
    font-size: 16px !important;
  }
  .ant-card-body {
    padding: 24px 7px !important;
    padding-bottom: 24px !important;
  }
  .rigthCont /deep/ .ant-card-body {
    padding: 24px 12px !important;
    padding-bottom: 24px !important;
  }
  .coverSpan {
    padding-top: 3% !important;
    font-size: 14px !important;
  }
}
@media (max-width: 1500px) {
  #aClass {
    width: 50%;
  }
}
</style>