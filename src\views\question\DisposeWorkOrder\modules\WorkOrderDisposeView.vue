<template>
  <a-modal :title="title"
           :width="modalWidth"
           :visible="visible"
           :confirmLoading="confirmLoading"
           @cancel="handleCancel"
           cancelText="关闭"
           :footer="null"
           wrapClassName="ant-modal-cust-warp"
           style="height:70%;overflow:hidden;overflow-y:auto"
           :centered="true">
    <a-form :form="form" style="height:100%; overflow-y: auto;">
      <div>
        <a-row>
          <a-col :span="12">
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol"
                         label="问题类型">
              <a-input disabled v-decorator="[ 'questionType', formValidator.questionTypeText]" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item :labelCol="labelCol"
                         :wrapperCol="wrapperCol"
                         label="问题内容">
              <textarea style="width: 100%;" disabled placeholder="请输入备注" v-decorator="[ 'question', formValidator.question]"></textarea>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-item :labelCol="labelCol"
                         :wrapperCol="wrapperCol"
                         label="提问人">
              <a-input disabled v-decorator="[ 'quizzer', formValidator.quizzer]" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item :labelCol="labelCol"
                         :wrapperCol="wrapperCol"
                         label="联系电话">
              <a-input disabled v-decorator="[ 'contact', formValidator.contact]" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-item :labelCol="labelCol"
                         :wrapperCol="wrapperCol"
                         label="提问时间">
              <a-input disabled v-decorator="[ 'createTime', formValidator.createTime]" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item :labelCol="labelCol"
                         :wrapperCol="wrapperCol"
                         label="提问人地址">
              <a-input disabled v-decorator="[ 'ip', formValidator.ip]" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-item :labelCol="labelCol"
                         :wrapperCol="wrapperCol"
                         label="分配时间">
              <a-input disabled v-decorator="[ 'confirmTime', formValidator.confirmTime]" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item :labelCol="labelCol"
                         :wrapperCol="wrapperCol"
                         label="分配人">
              <a-input disabled v-decorator="[ 'confirmor', formValidator.confirmor]" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-item :labelCol="labelCol"
                         :wrapperCol="wrapperCol"
                         label="处理人">
              <a-input disabled v-decorator="[ 'answerer', formValidator.answerer]" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item :labelCol="labelCol"
                         :wrapperCol="wrapperCol"
                         label="处理时间">
              <a-input disabled v-decorator="[ 'answerTime', formValidator.answerTime]" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-item 
            label="解决方案"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol">
            <textarea disabled style="width: 100%;" placeholder="请输入解决方案" v-decorator="['answererContent', formValidator.answererContent]"></textarea>
          </a-form-item>
          </a-col>
          
        </a-row>
      </div>
    </a-form>
    
  </a-modal>
</template>
<script>
  import pick from 'lodash.pick'
export default {
  name: 'WorkOrderDisposeView', //处理工单查看弹窗
  data() {
    return {
      title: '操作',
      visible: false,
      confirmLoading: false,
      /* 弹框宽 */
      modalWidth: '40%',
      form: this.$form.createForm(this),
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      formValidator:{
        answererContent: {
        	rules: [{
        			required: true,
        			message: '必填!'
        		},
        		{
        			min: 2,
        			max: 500,
        			message: '长度在 2 到 500 个字符',
        			trigger: 'blur'
        		}
        	]
        },
      }
    }
  },
  mounted() {
    
  },
  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      this.form.resetFields();
      this.model = Object.assign({}, record)
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(pick(this.model,'questionType','question','quizzer','createTime','ip','contact','confirmTime','answererContent','answerer','answerTime','confirmor'))
      });
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    
    handleCancel() {
      this.close()
    }
  }
}
</script>
<style scoped></style>
