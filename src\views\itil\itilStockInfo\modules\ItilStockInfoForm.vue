<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-item label="物品编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['goodsCode', validatorRules.goodsCode]"
                :allowClear="true" autocomplete="off" placeholder="请输入物品编号"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="物品名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['goodsName', validatorRules.goodsName]"
                :allowClear="true" autocomplete="off" placeholder="请输入物品名称"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="物品单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag type="list"
                v-decorator="['unitCode', { rules: [{ required: true, message: '请输入物品单位' }] }]" :trigger-change="true"
                dictCode="good_unit" placeholder="请选择物品单位" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="所属类目" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-tree-select v-decorator="['goodsTypeName', { rules: [{ required: true, message: '请输入所属类目' }] }]"
                style="width: 100%" :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }"
                @change="selectAssetsCategory" :allowClear="true" tree-data-simple-mode :tree-data="treeData"
                :replace-fields="replaceFields" placeholder="请输入所属类目" :load-data="onLoadData"
                :getPopupContainer="(node) => node.parentNode" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="备注信息" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-textarea v-decorator="['remarks', validatorRules.remarks]" placeholder="请输入备注信息" :auto-size="{ minRows: 3, maxRows: 10 }" />
            </a-form-item>
          </a-col>
          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
  import {
    httpAction,
    getAction
  } from '@/api/manage'
  import pick from 'lodash.pick'
  import {
    validateDuplicateValue
  } from '@/utils/util'
  import JFormContainer from '@/components/jeecg/JFormContainer'

  export default {
    name: 'ItilStockInfoForm',
    components: {
      JFormContainer,
    },
    props: {
      //流程表单data
      formData: {
        type: Object,
        default: () => {},
        required: false,
      },
      //表单模式：true流程表单 false普通表单
      formBpm: {
        type: Boolean,
        default: false,
        required: false,
      },
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false,
      },
    },
    data() {
      return {
        form: this.$form.createForm(this),
        model: {},
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          },
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          },
        },
        confirmLoading: false,
        validatorRules: {
          goodsCode: {
            rules: [
              { required: true, message: '请输入物品编号' },
              { max: 10, message: '物品编号长度不超过10字符' }
            ],
          },
          goodsName: {
            rules: [
              { required: true, message: '请输入物品名称' },
              { max: 100, message: '物品名称长度不超过100字符' }
            ]
          },
          remarks: {
            rules: [{ max: 500, message: '备注信息长度不超过500字符'}]
          }
        },
        url: {
          add: '/itilStockInfo/itilStockInfo/add',
          edit: '/itilStockInfo/itilStockInfo/edit',
          queryById: '/itilStockInfo/itilStockInfo/queryById',
        },
        code: '',
        replaceFields: {
          key: 'id',
          value: 'id',
          title: 'typeName',
        },
        treeData: [],
        goodsType: '',
        goodsTypeName: '',
      }
    },
    computed: {
      formDisabled() {
        if (this.formBpm === true) {
          if (this.formData.disabled === false) {
            return false
          }
          return true
        }
        return this.disabled
      },
      showFlowSubmitButton() {
        if (this.formBpm === true) {
          if (this.formData.disabled === false) {
            return true
          }
        }
        return false
      },
    },
    created() {
      this.getTreeData()
      //如果是流程中表单，则需要加载流程表单data
      this.showFlowData()
    },
    methods: {
      selectAssetsCategory(goodsType, data) {
        this.goodsType = goodsType
        this.goodsTypeName = data[0]
      },
      add() {
        this.edit({})
      },
      edit(record) {
        this.form.resetFields()
        this.model = Object.assign({}, record)
        this.visible = true
        this.$nextTick(() => {
          this.form.setFieldsValue(
            pick(this.model, 'goodsCode', 'goodsName', 'unitCode', 'goodsTypeName', 'remarks', 'stockNum',
              'totalMoney')
          )
        })
      },
      //渲染流程表单数据
      showFlowData() {
        if (this.formBpm === true) {
          let params = {
            id: this.formData.dataId
          }
          getAction(this.url.queryById, params).then((res) => {
            if (res.success) {
              this.edit(res.result)
            }
          })
        }
      },
      submitForm() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.model.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'put'
            }
            let formData = Object.assign(this.model, values)
            formData.goodsType = this.goodsType
            formData.goodsTypeName = this.goodsTypeName
            httpAction(httpurl, formData, method)
              .then((res) => {
                if (res.success) {
                  that.$message.success(res.message)
                  that.$emit('ok')
                } else {
                  that.$message.warning(res.message)
                }
              })
              .finally(() => {
                that.confirmLoading = false
              })
          }
        })
      },
      popupCallback(row) {
        this.form.setFieldsValue(
          pick(row, 'goodsCode', 'goodsName', 'unitCode', 'goodsTypeName', 'remarks', 'stockNum', 'totalMoney')
        )
      },

      getTreeData() {
        this.treeData = []
        getAction('/itilStockInfo/itilStockInfo/tree').then((res) => {
          if (res.success) {
            this.treeData = [...res.result]
          }
        })
      },
      onLoadData(treeNode) {
        return new Promise((resolve) => {
          if (treeNode.dataRef.children) {
            resolve()
            return
          }
          let param = {
            id: treeNode.$vnode.key,
          }
          getAction('/itilStockInfo/itilStockInfo/tree', param).then((res) => {
            if (res.success) {
              treeNode.dataRef.children = [...res.result]
              // this.treeData = [...res.result]
            }
            resolve()
          })
        })
      },
    },
  }
</script>