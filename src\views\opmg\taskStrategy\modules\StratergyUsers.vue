<template>
  <a-row class='row'>
    <a-col :lg="6" :md='24' class='col'>
      <!--组织机构-->
      <a-directory-tree selectable :selectedKeys="selectedDepIds" :checkStrictly="true"
                        :dropdownStyle="{ maxHeight: '200px', overflow: 'auto' }" :treeData="departTree" :expandAction="false"
                        :expandedKeys.sync="expandedKeys" @select="onDepSelect" />
    </a-col>
    <a-col :lg='18' :md="24" class='col'>
      <a-form layout='inline'>
        <a-row :gutter='24' ref='row'>
          <a-col >
            <a-form-item label="账号">
              <a-input-search  placeholder="请输入用户账号" v-model="queryParam.username" @search="onSearch"/>
            </a-form-item>
            <a-form-item label="用户名">
              <a-input-search  placeholder="请输入用户名" v-model="queryParam.realname" @search="onSearch"/>
            </a-form-item>
            <a-form-item>
              <a-button @click="searchReset(1)" icon="redo">重置</a-button>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <!--用户列表-->
      <a-table ref="table" rowKey="id" :columns="columns" :dataSource="dataSource"
               size='small'
               :pagination="ipagination"
               :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
               :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange, type: getType }"
               :loading="loading" @change="handleTableChange">
      </a-table>
    </a-col>
  </a-row>
</template>
<script>
import {filterObj,pushIfNotExist} from '@/utils/util'
import {
  queryDepartTreeList,
  getUserList,
  queryUserByDepId,
  getUsersByCondition,
  queryUserByDepIdWithPage
} from '@/api/api'

export default {
  name: 'StratergyUsers',
  components: {},
  props:{
    modalWidth:{
      type:String,
      required:false,
      default:()=>{return '800px'}
    },
    multi:{
      type:Boolean,
      required:false,
      default:()=>{return true}
    },
    userIds:{
      type:String,
      required:false,
      default:()=>{return ''}
    }
  },
  data() {
    return {
      queryParam: {
        username: '',
        realname: ''
      },
      columns: [{
        title: '用户账号',
        align: 'center',
        dataIndex: 'username',
      },
        {
          title: '用户姓名',
          align: 'center',
          dataIndex: 'realname',
        },
        {
          title: '性别',
          align: 'center',
          dataIndex: 'sex',
          width: 60,
          customRender: function (text) {
            if (text === 1) {
              return '男'
            } else if (text === 2) {
              return '女'
            } else {
              return text
            }
          },
        },
        {
          title: '手机',
          align: 'center',
          dataIndex: 'phone',
        },
        {
          title: '部门',
          align: 'center',
          dataIndex: 'orgCodeTxt',
          width: 260,
        },
      ],
      scrollTrigger: {},
      dataSource: [],
      selectedRowKeys: [],
      selectedRows:[],
      selectedUserNames: '',
      selectedUserIds: '',
      title: '根据部门选择用户',
      ipagination: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '30'],
        showTotal: (total, range) => {
          return range[0] + '-' + range[1] + ' 共' + total + '条'
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0,
      },
      isorter: {
        column: 'createTime',
        order: 'desc',
      },
      selectedDepIds: [],
      departTree: [],
      visible: false,
      /*     form: this.$form.createForm(this),*/
      loading: false,
      expandedKeys: [],
    }
  },
  computed: {
    // 计算属性的 getter
    getType: function () {
      return this.multi == true ? 'checkbox' : 'radio'
    },
  },
  watch: {
    userIds: {
      handler(nVal,oVal) {
        this.initUserNames(nVal)
      },
      deep:true,
      immediate:true
    }
  },
  created() {
    // 该方法触发屏幕自适应
    //this.resetScreenSize()
    this.queryDepartTree()
    this.loadData()
  },
  methods: {
    initUserNames(userIds) {
      this.selectedUserIds=userIds
      this.selectedUserNames=''
      this.selectedRows=[]
      this.selectedRowKeys=[]

      if (userIds.length>0) {
        // 这里最后加一个 , 的原因是因为无论如何都要使用 in 查询，防止后台进行了模糊匹配，导致查询结果不准确
        let length = userIds.split(',').length
        let ids=userIds+ ','
        getUsersByCondition({
          username: ids,
          pageNo: 1,
          pageSize: length,
        }).then((res) => {
          if (res.success) {
            let selectedRowKeys = []
            let realNames = []
            this.selectedRows=res.result.records
            res.result.records.forEach((user) => {
              realNames.push(user['realname'])
              selectedRowKeys.push(user['id'])
            })
            this.selectedRowKeys = selectedRowKeys
            this.selectedUserNames= realNames.join(',')
            this.$emit('initComp', this.selectedUserNames)
          }
        })
      } else {
        this.$emit('initComp', '')
      }
    },
    showModal(userIds) {
      this.visible = true
      this.queryDepartTree()
      //防止点选了数据，但操作了关闭，再次打开时需要重新初始化已选条目
      this.initUserNames(userIds)
      this.loadData(1)
      // this.form.resetFields()
    },
    async loadData(arg) {
      if (arg === 1) {
        this.ipagination.current = 1
      }
      if (this.selectedDepIds && this.selectedDepIds.length > 0) {
        await this.initQueryUserByDepId(this.ipagination.current, this.ipagination.pageSize, this.selectedDepIds,
          this.queryParam.username, this.queryParam.realname)
      } else {
        this.loading = true
        let params = this.getQueryParams() //查询条件
        await getUsersByCondition(params)
          .then((res) => {
            if (res.success) {
              this.dataSource = res.result.records
              this.ipagination.total = res.result.total
            }
          })
          .finally(() => {
            this.loading = false
          })
      }
    },
    // 触发屏幕自适应
    resetScreenSize() {
      let screenWidth = document.body.clientWidth
      if (screenWidth < 500) {
        this.scrollTrigger = {
          x: 800
        }
      } else {
        this.scrollTrigger = {}
      }
    },

    getQueryParams() {
      let param = Object.assign({}, this.queryParam, this.isorter)
      param.field = this.getQueryField()
      param.pageNo = this.ipagination.current
      param.pageSize = this.ipagination.pageSize
      return filterObj(param)
    },
    getQueryField() {
      let str = 'id,'
      for (let a = 0; a < this.columns.length; a++) {
        str += ',' + this.columns[a].dataIndex
      }
      return str
    },
    searchReset(num) {
      let that = this
      that.selectedRowKeys = []
      that.selectedRows=[]
      that.selectedUserIds = ''
      that.selectedUserNames=''
      that.selectedDepIds = []
      //if (num !== 0) {
      that.queryParam = {}
      that.loadData(1)
      //}

    },
    close() {
      this.visible = false
    },
    handleTableChange(pagination, filters, sorter) {
      //TODO 筛选
      if (Object.keys(sorter).length > 0) {
        this.isorter.column = sorter.field
        this.isorter.order = 'ascend' === sorter.order ? 'asc' : 'desc'
      }
      this.ipagination = pagination
      this.loadData()
    },
    handleSubmit() {
      let that = this
      that.getSelectedUserNames()
      that.$emit('ok', that.selectedUserNames, that.selectedUserIds)
      that.visible = false
    },
    //获取选择用户信息
    getSelectedUserNames(rowId) {
      let userIds = ''
      let realName=''
      for (let i = 0; i<this.selectedRows.length; i++) {
        realName= realName + ',' + this.selectedRows[i].realname
        userIds = userIds + ',' + this.selectedRows[i].username
      }
      this.selectedUserNames= realName.substring(1)
      this.selectedUserIds = userIds.substring(1)
    },
    // 点击树节点,筛选出对应的用户
    onDepSelect(selectedDepIds) {
      if (selectedDepIds[0] != null) {
        this.initQueryUserByDepId(null, null, selectedDepIds, this.queryParam.username, this.queryParam.realname) // 调用方法根据选选择的id查询用户信息
        if (this.selectedDepIds[0] !== selectedDepIds[0]) {
          this.selectedDepIds = [selectedDepIds[0]]
        }
      }
    },
    onSelectChange(selectedRowKeys, selectedRow) {
      this.selectedRowKeys = selectedRowKeys
      this.pushIfNotExist(this.selectedRows, selectedRow, 'id')
      this.selectedRows=this.removeIfNotExist(this.selectedRows, this.selectedRowKeys, 'id')
    },
    pushIfNotExist(array, value, key) {
      for (let val of value) {
        let isExist = false
        if (array.length > 0) {
          array.filter((item, index) => {
            if (item[key] === val[key]) {
              isExist = true
            }
          })
        }
        if (!isExist) {
          array.push(val)
        }
      }
    },
    removeIfNotExist(array, value, key) {
      let temArr=[]
      for (let id of value) {
        if (array.length > 0) {
          array.filter((item, index) => {
            if (item[key] === id) {
              temArr.push(item)
            }
          })
        }
      }
      return temArr
    },
    onSearch() {
      this.loadData(1)
    },
    // 根据选择的id来查询用户信息
    initQueryUserByDepId(pageNo, pageSize, selectedDepIds, username, realname) {
      this.loading = true
      this.dataSource=[]
      this.ipagination.total = 0
      return queryUserByDepIdWithPage({
        pageNo: pageNo,
        pageSize: pageSize,
        id: selectedDepIds.toString(),
        username: username,
        realname: realname
      })
        .then((res) => {
          if (res.success&&res.result&&res.result.records) {
            this.dataSource = res.result.records
            this.ipagination.total = res.result.total
          }
          this.loading =false
        }).catch((err)=>{
          this.$message.warning(err.message)
          this.loading =false
        })
    },
    queryDepartTree() {
      queryDepartTreeList().then((res) => {
        if (res.success) {
          this.departTree = res.result
          // 默认展开父节点
          this.expandedKeys = this.departTree.map((item) => item.id)
        }
      })
    }
  }
}
</script>



<style scoped lang='less'>
@import '~@assets/less/limitModalHeight.less';

.row{
  background-color: #fff;
  width: 100%;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
}

::v-deep .ant-table-body{
  overflow-x: auto !important;
}

</style>