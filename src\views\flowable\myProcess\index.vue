<template>
  <a-row style='height: 100%'>
    <a-col style='height: 100%; display: flex; flex-direction: column'>
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0', marginRight: '12px' }" class='card-style'>
        <!-- 查询区域 -->
        <div class='table-page-search-wrapper'>
          <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
            <a-row :gutter='24' ref='row'>
              <a-col :span='spanValue'>
                <a-form-item label='关键字'>
                  <a-input v-model='queryParam.keyword' :maxLength='maxLength'
                           :allow-clear='true' autocomplete='off'
                           placeholder='请输入关键字' />
                </a-form-item>
              </a-col>
              <a-col v-show="getVisible('name')" :span='spanValue'>
                <a-form-item :label="getTitle('name')">
                  <a-input v-model='queryParam.processInstanceName' :maxLength='maxLength'
                           :allow-clear='true' autocomplete='off'
                           placeholder='请输入业务标题' />
                </a-form-item>
              </a-col>
              <a-col v-show="getVisible('processDefinitionName')" :span='spanValue'>
                <a-form-item :label="getTitle('processDefinitionName')">
                  <a-input :allow-clear='true' autocomplete='off' :maxLength='maxLength'
                           placeholder='请输入所属流程全量内容'
                           v-model='queryParam.processDefinitionName' />
                </a-form-item>
              </a-col>
              <a-col v-show="getVisible('status')" :span='spanValue'>
                <a-form-item :label="getTitle('status')">
                  <a-select v-model='queryParam.status' placeholder='请选择状态'
                            :getPopupContainer='(target) => target.parentNode' :allow-clear='true'>
                    <a-select-option :value='1'>处理中</a-select-option>
                    <a-select-option :value='2'>已结束</a-select-option>
                    <a-select-option :value='3'>已撤销</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col v-show="getVisible('startTime')" :span='spanValue'>
                <a-form-item :label="getTitle('startTime')">
                  <a-range-picker :getCalendarContainer='node=> node.parentNode' format='YYYY-MM-DD HH:mm:ss' showTime
                                  v-model='queryParam.searchStartTime' :placeholder="['开始时间', '结束时间']"
                                  @change='onAppllicationTimeChange'
                                  style='width: 100%' />
                </a-form-item>
              </a-col>
              <a-col v-show="getVisible('endTime')" :span='spanValue'>
                <a-form-item :label="getTitle('endTime')">
                  <a-range-picker :getCalendarContainer='node=> node.parentNode' format='YYYY-MM-DD HH:mm:ss' showTime
                                  v-model='queryParam.searchEndTime' :placeholder="['开始时间', '结束时间']"
                                  @change='onEndTimeChange'
                                  style='width: 100%' />
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <span class='table-page-search-submitButtons' style='overflow: hidden;'>
                  <a-button icon='search' type='primary' @click='searchQuery'>查询</a-button>
                  <a-button icon='reload' style='margin-left: 8px' @click='searchReset'>重置</a-button>
                  <a v-if='queryItems.length>0' style='margin-left: 8px' @click='doToggleSearch'>{{ queryName }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
        <!-- 查询区域-END -->

        <!--自定义查询项 -->
        <div v-if='toggleSearchStatus' class='custom-query-item'>
          <a-checkbox-group v-model='settingQueryItems' :defaultValue='settingQueryItems' style='width:100%'
                            @change='onQuerySettingsChange'>
            <a-row :gutter='24'>
              <template v-for='(item,index) in queryItems'>
                <a-col v-show='item.checked' :span='querySpanValue' class='col-checkbox'>
                  <a-checkbox :value='item.dataIndex'>
                    <j-ellipsis :length='7' :value='item.title'></j-ellipsis>
                  </a-checkbox>
                </a-col>
              </template>
            </a-row>
          </a-checkbox-group>
        </div>
        <!-- 自定义查询项-END -->
      </a-card>
      <div v-if="taskBackNodesVisible">
        <task-back-nodes
          ref="taskBackNodesRef"
          :visible.sync="taskBackNodesVisible"
          :execute-task-id="taskId"
          @backTaskFinished="handleBackTask"
        />
      </div>

      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <!-- table区域-begin -->
        <div>
          <a-table
            ref='table'
            bordered
            rowKey='id'
            :columns='columns'
            :dataSource='dataSource'
            :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
            :pagination='ipagination'
            :loading='loading'
            @change='handleTableChange'>
            <span slot='status' slot-scope='status'>
              <a-tag color='#108ee9' v-if='status == 1'> 处理中</a-tag>
              <a-tag color='#f5222d' v-if='status == 2'> 已结束</a-tag>
              <a-tag v-if='status == 3'> 已撤销</a-tag>
              <a-tag color='#fa8c16' v-if='status == 4'> 待评价</a-tag>
              <a-tag color='#87d068' v-if='status == 5'> 已评价</a-tag>
            </span>
            <div slot='filterDropdown'>
              <a-card>
                <a-checkbox-group @change='onColSettingsChange' v-model='settingColumns' :defaultValue='settingColumns'>
                  <a-row style='width: 400px'>
                    <template v-for='(item, index) in defColumns'>
                      <template v-if="item.key != 'rowIndex' && item.dataIndex != 'action'">
                        <a-col :span='12' :key='index'>
                          <a-checkbox :value='item.dataIndex'>
                            <j-ellipsis :value='item.title' :length='10'></j-ellipsis>
                          </a-checkbox>
                        </a-col>
                      </template>
                    </template>
                  </a-row>
                </a-checkbox-group>
              </a-card>
            </div>

            <template slot='tooltip' slot-scope='text'>
              <a-tooltip placement='top'
                         :title='text'
                         trigger='hover'>
                <div class='tooltip'>
                  {{ text }}
                </div>
              </a-tooltip>
            </template>
            <template slot='tooltip2' slot-scope='text'>
              <a-tooltip placement='topLeft'
                         :title='text'
                         trigger='hover'>
                <div class='tooltip'>
                  {{ text }}
                </div>
              </a-tooltip>
            </template>

            <a-icon slot='filterIcon' type='setting' :style="{ fontSize: '16px', color: '#108ee9' }" />
            <span slot='action' slot-scope='text, record'>
              <template v-if='record.status == 1'>
                <a href='javascript:void(0);' v-if='!showAll' @click='withdraw(record)'>撤销</a>
                <a-divider type='vertical' v-if='!showAll' />
                <a href='javascript:' @click="history(record,'查看进度')">查看</a>
                <a-divider type='vertical' v-if='!showAll' />
                <a href='javascript:void(0);' v-if='!showAll' @click='showReturn(record)'>退回</a>
              </template>

              <template v-if='record.status ===4&&!showAll'>
                <a href='javascript:void(0);' @click='evaluate(record,false)'>评价</a>
                <a-divider type='vertical' />
              </template>
              <template v-if='record.status !==1'>
                <a href='javascript:void(0);' @click="history(record,'查看审批历史')">查看</a>
              </template>

              <template v-if='record.status ===5&&!showAll'>
                <a-divider type='vertical' />
                <a href='javascript:void(0);' @click='evaluateLook(record)'>查看评价</a>
              </template>
            </span>
          </a-table>
        </div>
      </a-card>

      <evaluation-modal ref='evaluationModal' @ok='searchReset'></evaluation-modal>
      <ProcessHistoryAllModal ref='processHistoryModal' :showAll='showAll' @ok='modalFormOk'></ProcessHistoryAllModal>
      <process-modal ref='processModal'></process-modal>
      <a-modal title='确认撤销' v-model='modalCancelVisible' :mask-closable='false' :width='500'>
        <a-form-model ref='cancelForm' :model='cancelForm' :rules="validatorCancelRules" :label-width='70' v-if='modalCancelVisible'>
          <a-form-model-item label='撤销原因' prop='reason'>
            <a-input v-model='cancelForm.reason' :allow-clear='true' :rows='4' autocomplete='off' type='textarea' placeholder="请输入撤销原因" />
          </a-form-model-item>
        </a-form-model>
        <div slot='footer' style='text-align: right'>
          <a-button type='text' @click='modalCancelVisible = false'>取消</a-button>
          <a-button type='primary' :disabled='submitLoading' @click='handelSubmitCancel'>提交</a-button>
        </div>
      </a-modal>
    </a-col>
  </a-row>
</template>
<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import Vue from 'vue'
import ProcessHistoryAllModal from './modules/ProcessHistoryAllModal.vue'
import { postAction } from '@/api/manage'
import ProcessModal from './modules/ProcessModal'
import { YqFormSeniorSearchLocation } from '@/mixins/YqFormSeniorSearchLocation'
import evaluationModal from './modules/EvaluationModal.vue'
import TaskBackNodes from '../task-todo/modules/TaskBackNodes.vue'
import { getAction, putAction } from '../../../api/manage'

export default {
  name: 'myApplication',
  mixins: [JeecgListMixin, YqFormSeniorSearchLocation],
  components: {
    TaskBackNodes,
    ProcessHistoryAllModal,
    ProcessModal,
    evaluationModal
  },
  props: {
    includeFilter: {
      required: false,
      type: String,
      description: '需要过滤的流程定义keys'
    },
    showAll: false
  },
  data() {
    return {
      maxLength:50,
      formItemLayout: {
        labelCol: {
          style: 'width:105px'
        },
        wrapperCol: {}
      },
      queryParam: {},
      //表头
      columns: [],
      //列设置
      settingColumns: [],
      //列定义
      defColumns: [{
        title: '序号',
        width: 60,
        dataIndex: '',
        key: 'rowIndex',
        isUsed: false,
        customCell: () => {
          let cellStyle = 'text-align:center;'
          return {
            style: cellStyle
          }
        },
        customRender: function(t, r, index) {
          return parseInt(index) + 1
        }
      },
        {
          title: '业务标题',
          dataIndex: 'name',
          isUsed: true,
          customCell: () => {
            let cellStyle = 'text-align:center;min-width:150px;max-width: 280px;'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'tooltip'
          },
          sorter: true
        },
        {
          title: '所属流程',
          dataIndex: 'processDefinitionName',
          isUsed: true,
          customCell: () => {
            let cellStyle = 'text-align:center;width:180px;'
            return {
              style: cellStyle
            }
          },
          sorter: true
        },
        {
          title: '流程版本',
          dataIndex: 'processDefinitionVersion',
          isUsed: false,
          customRender: (text) => {
            //字典值替换通用方法
            return 'v' + text
          },
          customCell: () => {
            let cellStyle = 'text-align:center;width:80px;'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '当前审批环节',
          dataIndex: 'currTaskName',
          isUsed: false,
          customCell: () => {
            let cellStyle = 'text-align:center;width:160px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '状态',
          dataIndex: 'status',
          isUsed: true,
          scopedSlots: {
            customRender: 'status'
          },
          customCell: () => {
            let cellStyle = 'text-align:center;width:80px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '提交申请时间',
          dataIndex: 'startTime',
          isUsed: true,
          customCell: () => {
            let cellStyle = 'text-align:center;width:160px'
            return {
              style: cellStyle
            }
          },
          sorter: true
        },
        {
          title: '结束时间',
          dataIndex: 'endTime',
          isUsed: true,
          customCell: () => {
            let cellStyle = 'text-align:center;width:160px'
            return {
              style: cellStyle
            }
          },
          sorter: true
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 210,
          isUsed: false,
          fixed: 'right',
          scopedSlots: {
            /*  filterDropdown: 'filterDropdown',
              filterIcon: 'filterIcon',*/
            customRender: 'action'
          }
        }
      ],
      modalCancelVisible: false,
      cancelForm: {
        reason: '',
        cascade: false,
        procInsId: null
      },
      isview: false,
      submitLoading: false,
      taskBackNodesVisible: false,
      taskId: '',
      message: '',
      processInstanceId: '',
      url: {
        list: this.showAll ? '/flowable/processInstance/list' : '/flowable/processInstance/listStartedByMe',
        cancel: '/business/actZBusiness/cancel'
      },
      validatorCancelRules: {
        reason: [{
          required: true,
          message: '请输入撤销原因!'
        },
        {
          max: 255,
          message: '撤销原因长度不超过255个字符!'
        }],
      }
    }
  },
  computed: {
    tasktimestimp() {
      return this.$store.getters.tasktimestimp
    }
  },
  watch: {
    tasktimestimp: {
      handler(nval, oval) {
        this.loadData()
      },
      deep: true,
      immediate: true
    }
  },
  created() {
    this.queryParam = {
      processDefinitionKey: this.includeFilter
    }

    this.initColumns()
    this.getColumns(this.columns)
    if (this.showAll) {
      this.defColumns.splice(4, 0, {
        title: '申请人',
        dataIndex: 'startUserName'
      })
    }

  },
  methods: {
    evaluateLook(record) {
      this.$refs.evaluationModal.edit(record.id)
      this.$refs.evaluationModal.title = '查看评价'
      this.$refs.evaluationModal.disableSubmit = false
    },
    //点击评价
    evaluate(record) {
      this.$refs.evaluationModal.add(record.id)
      this.$refs.evaluationModal.title = '评价'
      this.$refs.evaluationModal.disableSubmit = false
    },

    onEndTimeChange: function(value, dateString) {
      this.queryParam.finishedAfter = dateString[0]
      this.queryParam.finishedBefore = dateString[1]
    },
    onAppllicationTimeChange: function(value, dateString) {
      this.queryParam.startedAfter = dateString[0]
      this.queryParam.startedBefore = dateString[1]
    },
    //撤销
    withdraw(record) {
      //this.cancelForm.id = v.id
      this.cancelForm.reason = '',
      this.cancelForm.procInsId = record.id
      this.modalCancelVisible = true
    },
    //退回
    showReturn(record) {
      this.$confirm({
        title: '确认退回',
        content: '确定要退回此任务吗？',
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          getAction('/flowable/task/getTaskIdByProcessInstanceId', { processInstanceId: record.id })
            .then(res => {
              const { result } = res
              if (result) {
                this.taskId = result;
                this.taskBackNodesVisible = true;
              }

            })
            .catch(error => {
              // 处理异步请求失败的情况
              console.error('请求失败:', error);
              this.$message.error('请求失败，请重试');
            });
        },
        onCancel: () => {
          // 用户取消操作
        }
      });
    },
    handleBackTask(backNode) {
      putAction('/flowable/task/back', {
        taskId: this.taskId,
        activityId: backNode.nodeId,
        activityName: backNode.nodeName,
        userId: backNode.userId,
        message: this.message
      }).then(res => {
        this.$message.success(res.message)
        this.taskBackNodesVisible = false
        this.$store.commit('TASK_TIMESTIMP', res.timestamp)
        this.$emit('close')
        this.init(this.processInstanceId,this.isAddKnowledge=true)
      })
    },
    handelSubmitCancel() {
      this.$refs.cancelForm.validate(valid => {
        if (valid) {
          this.submitLoading = true
          postAction(this.url.cancel, this.cancelForm)
            .then((res) => {
              if (res.code == 200) {
                this.$message.success('撤销成功,表单已退回至草稿')
              } else {
                this.$message.error('撤销失败' + res.message)
              }
              this.modalCancelVisible = false
              this.resetCancleForm()
              this.loadData()
            })
            .finally(() => (this.submitLoading = false))
        }
      })
    },
    resetCancleForm() {
      this.cancelForm = {
        reason: '',
        cascade: false,
        processInstanceId: null
      }
    },
    detail(record) {
      if (!record.id) {
        this.$message.error('申请不存在')
        return
      }
      this.$refs.processModal.init(record)
    },
    //查看进度
    history(record, title) {
      if (!record.id) {
        this.$message.error('流程实例ID不存在')
        return
      }
      record.state = record.endTime
      this.$refs.processHistoryModal.init(record.id)
      this.$refs.processHistoryModal.title = title
      this.$refs.processHistoryModal.disableSubmit = false
    },
    //列设置更改事件
    onColSettingsChange(checkedValues) {
      var key = this.$route.name + ':colsettings'
      Vue.ls.set(key, checkedValues, 7 * 24 * 60 * 60 * 1000)
      this.settingColumns = checkedValues
      const cols = this.defColumns.filter((item) => {
        if (item.key == 'rowIndex' || item.dataIndex == 'action') {
          return true
        }
        if (this.settingColumns.includes(item.dataIndex)) {
          return true
        }
        return false
      })
      this.columns = cols
    },
    initColumns() {
      //权限过滤（列权限控制时打开，修改第二个参数为授权码前缀）
      //this.defColumns = colAuthFilter(this.defColumns,'testdemo:');

      var key = this.$route.name + ':colsettings'
      let colSettings = Vue.ls.get(key)
      if (colSettings == null || colSettings == undefined) {
        let allSettingColumns = []
        this.defColumns.forEach(function(item, i, array) {
          allSettingColumns.push(item.dataIndex)
        })
        this.settingColumns = allSettingColumns
        this.columns = this.defColumns
      } else {
        this.settingColumns = colSettings
        const cols = this.defColumns.filter((item) => {
          if (item.key == 'rowIndex' || item.dataIndex == 'action') {
            return true
          }
          if (colSettings.includes(item.dataIndex)) {
            return true
          }
          return false
        })
        this.columns = cols
      }
    }
  }
}
</script>
<style scoped lang='less'>
@import '~@assets/less/common.less';
@import '~@assets/less/YQCommon.less';
@import '~@assets/less/scroll.less';
</style>
