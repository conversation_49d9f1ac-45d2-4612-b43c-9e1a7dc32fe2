<template>
  <div>
    <a-form-item label="类型">
      {{ isSequentialStr }}
    </a-form-item>
    <a-form-item label="循环基数">
      {{ multiInstanceCopy.loopCardinality }}
    </a-form-item>
    <a-form-item label="集合">
      {{ multiInstanceCopy.collection }}
    </a-form-item>
    <a-form-item label="元素变量">
      {{ multiInstanceCopy.elementVariable }}
    </a-form-item>
    <a-form-item label="完成条件">
      {{ multiInstanceCopy.completionCondition }}
    </a-form-item>
    <a-modal
      :title="title"
      :visible='_multiInstanceShow'
      :width='1000'
      :maskClosable='false'
      @cancel="hideForm"
      @ok="save"
    >
      <div class="listener-form-con">
        <a-form :form="multiInstance">
          <a-form-item>
            <span class="listener-form-item-label">多实例类型：</span>
            <div class='listener-form-item-wrap'>
              <a-select
                v-model='multiInstance.isSequential'
                :allow-clear='true'
                :getPopupContainer='(node) => node.parentNode'
              >
                <a-select-option v-for='(item, idx) in multiInstanceOptions' :key='idx' :value='item.value'>
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </div>
          </a-form-item>
          <a-form-item>
            <span class="listener-form-item-label">循环基数：</span>
            <div class="listener-form-item-wrap">
              <a-input v-model='multiInstance.loopCardinality' allow-clear />
            </div>
          </a-form-item>
          <a-form-item>
            <span class="listener-form-item-label">集合：</span>
            <div class="listener-form-item-wrap">
              <a-input v-model='multiInstance.collection' allow-clear />
            </div>
          </a-form-item>
          <a-form-item>
            <span class="listener-form-item-label">元素变量：</span>
            <div class="listener-form-item-wrap">
              <a-input v-model='multiInstance.elementVariable' allow-clear />
            </div>
          </a-form-item>
          <a-form-item>
            <span class="listener-form-item-label">完成条件：</span>
            <div class="listener-form-item-wrap">
              <a-input v-model='multiInstance.completionCondition' allow-clear />
            </div>
          </a-form-item>
        </a-form>
      </div>
    </a-modal>
  </div>
</template>
<script>
import mixinPanel from '../mixins/mixinPanel'

export default {
  props: {
    multiInstanceShow: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '',
    },
  },
  mixins: [mixinPanel],
  data() {
    return {
      multiInstanceOptions: [
        {
          value: 1,
          label: '串行',
        },
        {
          value: 0,
          label: '并行',
        },
      ],
      multiInstance: {
        isSequential: '',
        loopCardinality: '',
        collection: '',
        elementVariable: '',
        completionCondition: '',
      },
      multiInstanceCopy: {
        isSequential: '',
        loopCardinality: '',
        collection: '',
        elementVariable: '',
        completionCondition: '',
      },
      isSequentialStr: '',
    }
  },
  computed: {
    _multiInstanceShow: {
      get() {
        return this.multiInstanceShow
      },
      set(v) {
        this.$emit('changeMultiInstanceShow', v)
      },
    },
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.multiInstance={
        isSequential: '',
        loopCardinality: '',
        collection: '',
        elementVariable: '',
        completionCondition: '',
      }
      if (this.element.businessObject) {
        const loopCharacteristics = this.element.businessObject.loopCharacteristics
        if (loopCharacteristics) {
          const data = {}
          //判断是否是串行实例,表单中isSequential的选项为1/0，Boolean类型的值会抛异常
          if(loopCharacteristics.isSequential){
            data.isSequential = 1
          }else{
            data.isSequential = 0
          }
          data.collection = loopCharacteristics.collection
          data.elementVariable = loopCharacteristics.elementVariable
          ;(data.loopCardinality = loopCharacteristics.loopCardinality ? loopCharacteristics.loopCardinality.body : ''),
            (data.completionCondition = loopCharacteristics.completionCondition ? loopCharacteristics.completionCondition.body : '')
          this.multiInstance = data
        }
      }
      Object.assign(this.multiInstanceCopy, this.multiInstance)
      this.initStr()
    },
    initStr() {
      let t = this.multiInstanceOptions.find((el) => el.value === this.multiInstanceCopy.isSequential)
      if (t) {
        this.isSequentialStr = t.label
      } else {
        this.isSequentialStr = ''
      }
    },
    save() {
      if (this.multiInstance.isSequential === undefined || this.multiInstance.isSequential === '') {
        delete this.element.businessObject.loopCharacteristics
        this.multiInstance = {
          isSequential: '',
          loopCardinality: '',
          collection: '',
          elementVariable: '',
          completionCondition: '',
        }
        this.updateProperties({ loopCharacteristics: undefined })
      } else {
        let loopCharacteristics = this.element.businessObject.loopCharacteristics
        if (!loopCharacteristics) {
          loopCharacteristics = this.modeler.get('moddle').create('bpmn:MultiInstanceLoopCharacteristics')
        }
        //判断是否是串行实例，xml中isSequential的值为true/false
        if(this.multiInstance.isSequential==1){
          loopCharacteristics.isSequential = true
        }else{
          loopCharacteristics.isSequential = false
        }
        if (this.multiInstance.collection) {
          loopCharacteristics.set(this.descriptorPrefix + 'collection', this.multiInstance.collection)
        } else {
          loopCharacteristics.set(this.descriptorPrefix + 'collection', undefined)
        }
        if (this.multiInstance.elementVariable) {
          loopCharacteristics.set(this.descriptorPrefix + 'elementVariable', this.multiInstance.elementVariable)
        } else {
          loopCharacteristics.set(this.descriptorPrefix + 'elementVariable', undefined)
        }
        if (this.multiInstance.loopCardinality) {
          const loopCardinality = this.modeler
            .get('moddle')
            .create('bpmn:Expression', { body: this.multiInstance.loopCardinality })
          loopCharacteristics['loopCardinality'] = loopCardinality
        } else {
          loopCharacteristics['loopCardinality'] = undefined
        }
        if (this.multiInstance.completionCondition) {
          const completionCondition = this.modeler
            .get('moddle')
            .create('bpmn:Expression', { body: this.multiInstance.completionCondition })
          loopCharacteristics['completionCondition'] = completionCondition
        } else {
          loopCharacteristics['completionCondition'] = undefined
        }
        this.updateProperties({ loopCharacteristics })
      }
      this.hideForm()
      Object.assign(this.multiInstanceCopy, this.multiInstance)
      this.initStr()
    },
    hideForm() {
      this._multiInstanceShow = false
    },
  },
}
</script >

<style lang="less" scoped>
@import '~@assets/less/YQNormalModal.less';
.listener-form-con {
  //height: 50vh;
  .listener-form-item-label {
    display: inline-block;
    width: 90px;
    text-align: right;
  }
  .listener-form-item-wrap {
    display: inline-block;
    width: calc(100% - 90px);
  }
}
</style>