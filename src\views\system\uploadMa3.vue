<template>
  <div>
    <el-upload
      ref="replaceUploader"
      class="avatar-uploader"
      :action="url1"
      :limit="1"
      :file-list="fileList"
      :show-file-list="true"
      :on-success="handleReplaceAvatarSuccess"
      :before-upload="beforeAvatarUpload"
      accept=".mp3,.wav"
    >
      <el-button size="small" type="primary">一般告警铃声</el-button>
    </el-upload>
    <hr />
    <el-upload
      ref="replaceUploader"
      v-model="title1"
      class="avatar-uploader"
      :action="url2"
      :limit="1"
      :show-file-list="true"
      :file-list="fileList1"
      :on-success="handleReplaceAvatarSuccess1"
      :before-upload="beforeAvatarUpload1"
      accept=".mp3,.wav"
    >
      <el-button size="small" type="primary">严重告警铃声</el-button>
    </el-upload>
  </div>
</template>
<script>
import { httpAction, getAction } from '@/api/manage'
export default {
  data() {
    return {
      title1: '',
      list:'/umpPwdManage/umpPwdManage/list2',
      list1:'/umpPwdManage/umpPwdManage/list3',
      url1: window._CONFIG['domianURL'] + '/sys/common/upload1?type=10',
      url2: window._CONFIG['domianURL'] + '/sys/common/upload1?type=20',
      fileList: [],
      fileList1: [],
    }
  },
  mounted() {},
  created() {
    this.show()
    this.show2()
  },

  methods: {
    handleReplaceAvatarSuccess(response, file, fileList) {
      this.fileList = fileList
    },
    handleReplaceAvatarSuccess1() {},
    beforeAvatarUpload(file) {
      var testmsg = file.name.substring(file.name.lastIndexOf('.') + 1)
      const extension = testmsg === 'mp3' || testmsg === 'WAV' || testmsg === 'MP3' || testmsg === 'wav'
      if (!extension) {
        // this.$message({
        //   message: '上传文件只能是mp3格式！',
        //   type: 'error',
        // })
        this.$message.error('上传文件只能是mp3,wav格式！')
      }
      return extension
    },
    beforeAvatarUpload1(file) {
      var testmsg = file.name.substring(file.name.lastIndexOf('.') + 1)
      const extension = testmsg === 'mp3' || testmsg === 'WAV' || testmsg === 'MP3' || testmsg === 'wav'
      if (!extension) {
        // this.$message({
        //   message: '上传文件只能是mp3格式！',
        //   type: 'error',
        // })
        this.$message.error('上传文件只能是mp3,wav格式！')
      }
      return extension
    },

    show() {
      getAction(this.list).then((res) => {
        if (res.success) {
          //   this.title = res.result.commonly
          this.fileList.push(res.result)
        }
      })
    },

     show2() {
      getAction(this.list1).then((res) => {
        if (res.success) {
          //   this.title = res.result.commonly
          this.fileList1.push(res.result)
        }
      })
    },
  },
}
</script>
<style lang="less" scoped>
</style>