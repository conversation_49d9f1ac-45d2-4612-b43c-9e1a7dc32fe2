<template>
  <div style="height:100%">
    <keep-alive exclude="contractInfo">
      <component style="height:100%" :is="pageName" :data="data" />
    </keep-alive>
  </div>
</template>
<script>
import contractList from './contractList.vue'
import contractInfo from './contractInfo.vue'
export default {
  name: 'contractManagement',
  data() {
    return {
      isActive: 0,
      data: {}
    }
  },
  components: {
    contractList,
    contractInfo
  },
  created() {
    this.pButton1(0)
  },
  //使用计算属性
  computed: {
    pageName() {
      switch (this.isActive) {
        case 0:
          return 'contractList'
        default:
          return 'contractInfo'
      }
    }
  },
  methods: {
    pButton1(index) {
      this.isActive = index
    },
    pButton2(index, item) {
      this.isActive = index
      this.data = item
    }
  }
}
</script>