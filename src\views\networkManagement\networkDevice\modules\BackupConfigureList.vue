<template>
  <div class='scroll'>
    <!-- 查询区域 -->
    <div class='table-operator table-operator-style'>
      <!-- 操作按钮区域 -->
      <a-button @click='backupsOrRestoreConfig("backUp")'>备份</a-button>
      <a-button @click='contrastConfigs'>对比</a-button>
      <a-button @click='refreshConfigs'>刷新</a-button>
      <a-dropdown v-if='selectedRowKeys.length > 0'>
        <a-menu slot="overlay" style='text-align: center'>
          <a-menu-item key='1' @click='batchDel'>删除</a-menu-item>
        </a-menu>
        <a-button> 批量操作
          <a-icon type='down' />
        </a-button>
      </a-dropdown>
      <!-- table区域-begin -->
      <div>
        <a-table
          ref='table'
          bordered
          rowKey='id'
          :columns='columns'
          :dataSource='dataSource'
          :scroll='dataSource.length>0?{x:"max-content"}:{}'
          :pagination='ipagination'
          :loading='loading'
          :rowSelection='{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }'
          @change='handleTableChange'
        >
          <template slot='tooltip' slot-scope='text'>
            <a-tooltip placement='topLeft' :title='text' trigger='hover'>
              <div class='tooltip'>
                {{ text }}
              </div>
            </a-tooltip>
          </template>
          <span slot='action' class='caozuo' slot-scope='text, record'>
                <a @click='viewConfig(record,"detail")'>查看</a>
                <a-divider type='vertical' />
                <a-dropdown>
                  <a class='ant-dropdown-link'>更多
                    <a-icon type='down' /></a>
                  <a-menu slot='overlay'>
                    <a-menu-item v-if='restoreBtnVisible==1'>
                      <a @click='backupsOrRestoreConfig("recovery",record)' class='overlay'>还原</a>
                    </a-menu-item>
                     <a-menu-item>
                      <a @click='viewConfig(record,"log")' class='overlay'>日志</a>
                    </a-menu-item>
                    <a-menu-item>
                      <a @click='downLoadConfig(record)' class='overlay'>下载</a>
                    </a-menu-item>
                    <a-menu-item>
                       <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)" placement="topLeft">
                         <a class='overlay'>删除</a>
                       </a-popconfirm>
                      <!--<a @click='handleDelete(record.id)' class='overlay'>删除</a>-->
                    </a-menu-item>
                  </a-menu>
                </a-dropdown>
              </span>
        </a-table>
      </div>
    </div>
    <backup-config-modal ref='backupConfigForm' @ok='modalFormOk'></backup-config-modal>
    <contrast-config-modal ref='contrastConfigModal'></contrast-config-modal>
    <view-config-modal ref='viewConfigModal'></view-config-modal>
  </div>
</template>

<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import JEllipsis from '@comp/jeecg/JEllipsis.vue'
import { deleteAction, getAction, getFileAccessHttpUrl, putAction } from '@api/manage'
import BackupConfigModal from '@views/networkManagement/networkDevice/modules/BackupConfigModal.vue'
import ContrastConfigModal from '@views/networkManagement/networkDevice/modules/ContrastConfigModal.vue'
import ViewConfigModal from '@views/networkManagement/networkDevice/modules/ViewConfigModal.vue'

export default {
  name: 'BackupConfigureList',
  props:{
    restoreBtnVisible:{
      type:[String,Number],
      required:true
    }
  },
  mixins: [JeecgListMixin],
  components: {
    JEllipsis,
    BackupConfigModal,
    ContrastConfigModal,
    ViewConfigModal
  },
  data() {
    return {
      description: '设备配置管理',
      //查询条件，此参数名称与JeecgListMixin模块参数一致
      queryParam: {
        deviceId: '', //查询参数 设备id
        recordType:'0'//区分还原和备份的字段，后端使用
      },
      disableMixinCreated: true,
      // 表头
      columns: [
        {
          title: '版本',
          dataIndex: 'version',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 100px'
            return { style: cellStyle }
          }
        },
        {
          title: '创建人',
          dataIndex: 'createBy',
          customCell: () => {
            let cellStyle = 'text-align: center;width: 180px'
            return { style: cellStyle }
          }
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          customCell: () => {
            let cellStyle = 'text-align: center;width:200px'
            return { style: cellStyle }
          }
        },
        {
          title: '状态',
          dataIndex: 'executeStatus',
          scopedSlots: { customRender: 'executeStatus' },
          customCell: () => {
            let cellStyle = 'text-align: center;width: 150px'
            return { style: cellStyle }
          }
        },
        {
          title: '备份说明',
          dataIndex: 'description',
          scopedSlots: { customRender: 'tooltip' },
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 100px;max-width:300px'
            return { style: cellStyle }
          }
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          scopedSlots: { customRender: 'action' },
          width: 200
        }
      ],
      url: {
        list: '/net/device/configureBckList', //分页展示接口
        delete: '/net/device/configureBackBathDelete', //删除接口
        deleteBatch:'/net/device/configureBackBathDelete', //批量删除和删除接口一样
      },
      deviceInfo: {}
    }
  },
  watch: {
    deviceInfo(newVal, oldVal) {
      this.init(newVal)
    }
  },
  methods: {
    show(record) {
      this.deviceInfo = record
    },
    init(record) {
      this.queryParam.deviceId = record.id
      if (this.queryParam.deviceId) {
        this.loadData()
      }
    },
    //备份/恢复
    backupsOrRestoreConfig: function(actionType,record) {
      this.$refs.backupConfigForm.actionType=actionType
      this.$refs.backupConfigForm.title =actionType==="backUp"?"备份说明":"还原说明"
      this.$refs.backupConfigForm.disableSubmit = false
      this.$refs.backupConfigForm.addRemark(this.deviceInfo,record)
    },
    //对比
    contrastConfigs(deviceInfo) {
      if (this.selectedRowKeys.length !== 2) {
        this.$message.warning('请任意选择两条记录')
        return
      }
      this.$refs.contrastConfigModal.loadConfigs(this.selectionRows)
      this.$refs.contrastConfigModal.title = '配置对比'
      this.$refs.contrastConfigModal.disableSubmit = true
    },
    refreshConfigs(){
      this.queryParam = {}
      this.queryParam.deviceId=this.deviceInfo.id
      this.queryParam.recordType='0'
      this.selectionRows=[]
      this.selectedRowKeys=[]
      this.loadData(1)
    },
    //查看
    viewConfig(record,sign) {
      let config=sign=="log"?record.executeLog:record.configureText

      this.$refs.viewConfigModal.loadConfig(config)
      this.$refs.viewConfigModal.title = sign=="log"?"日志详情":"配置详情"
      this.$refs.viewConfigModal.disableSubmit = true
    },
    //恢复
    restoreConfig(record) {
      if (!this.url.restoreConfig) {
        this.$message.error('请设置url.restoreConfig属性!')
        return
      }
      var that = this
      const param={
        deviceId:this.queryParam.deviceId,
        configureManageId:record.configureManageId,
        description:record.description,
        type:'recovery',
        backRecordId:record.id
      }
      getAction(that.url.restoreConfig, param).then((res) => {
        if (res.success) {
          that.$message.success(res.message)
          that.loadData()
        } else {
          that.$message.warning(res.message)
        }
      })
    },
    //下载
    downLoadConfig(record) {
      this.downloadFile(record.configureFile)
    },
    //删除
    handleDelete: function (id) {
      if (!this.url.delete) {
        this.$message.error('请设置url.delete属性!')
        return
      }
      var that = this
      deleteAction(that.url.delete, { ids: id }).then((res) => {
        if (res.success) {
          that.$message.success(res.message)
          that.loadData()
        } else {
          that.$message.warning(res.message)
        }
      })
    },
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';

.scroll {
  height: 100%;
  overflow: hidden;
  overflow-y: auto;
}

.table-operator {
  margin-right: 1px;
}

.overlay {
  color: #409eff
}
</style>
