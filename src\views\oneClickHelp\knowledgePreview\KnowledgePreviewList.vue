  <template>
    <card-frame :showHeadBgImg='true' :show-title-img='true' title='知识浏览'>
      <div slot='bodyLeft'>
        <tree-expand
          :inputFlag='false'
          :tree-url="'/knowledges/topic/selectTreeForKnowledges'"
          :fieldKey='"id"'
          :is-show-all-btn='true'
          :root-node='treeRootNode'
          :default-expanded-keys='["root"]'
          :default-selected-keys='["root"]'
          :is-show-icon='true'
          :is-show-line='true'
          :oper-permission='"userWithPermission"'
          @selected='treeSeletedSearch'>
        </tree-expand>
      </div>
      <div slot='bodyRight'>
        <div  style='padding: 16px 42px 0px 42px'>
          <!-- 查询区域 -->
          <div class='table-page-search-wrapper'>
            <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
              <a-row :gutter='24' ref='row'>
                <a-col :span='spanValue'>
                  <a-form-item :label="'标\u3000题'">
                    <a-input placeholder='请输入标题' v-model='queryParam.title' :allowClear='true'
                             autocomplete='off' :maxLength="maxLength"/>
                  </a-form-item>
                </a-col>
                <a-col :span='spanValue'>
                  <a-form-item label='能见度' >
                    <a-select v-model='queryParam.isPrivate'
                              placeholder='请选择能见度'
                              :getPopupContainer="(node) => node.parentNode"
                              :dropdownClassName='"custom-select-dropdown"'
                              :allow-clear='true'>
                      <a-select-option v-for="item in visibility" :value="item.value" :key="'visibility_'+item.value" :label="item.label">
                        {{ item.label }}
                      </a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span='spanValue'>
                  <a-form-item label='知识类型'>
                    <a-select
                      v-model='queryParam.knowledgeType'
                      placeholder='请选择知识类型'
                      :getPopupContainer="(node) => node.parentNode"
                      :dropdownClassName='"custom-select-dropdown"'
                      :allowClear='true'>
                      <a-select-option :value='0' label='文本'>文本</a-select-option>
                      <a-select-option :value='1' label='文档'>文档</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span='colBtnsSpan()'>
                    <span class='table-page-search-submitButtons'
                          :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                      <a-button type='primary' class='btn-search' @click='searchQuery'>查询</a-button>
                      <a-button class='btn-reset' @click='searchReset'>重置</a-button>
                      <a v-if='isVisible' class='btn-updown' @click='doToggleSearch'>
                        {{ toggleSearchStatus ? '收起' : '展开' }}
                        <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                      </a>
                    </span>
                </a-col>
              </a-row>
            </a-form>
          </div>
          <a-table
            ref='table'
            size='middle'
            bordered
            :row-key='(record, index) => {return record.id}'
            :columns='columns'
            :dataSource='dataSource'
            :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
            :pagination='ipagination'
            :loading='loading'
            :locale='locale'
            @change='handleTableChange'>
            <template slot='htmlSlot' slot-scope='text' width='500'>
              <div v-html='text'></div>
            </template>
            <template slot='imgSlot' slot-scope='text'>
              <span v-if='!text' style='font-size: 14px'>无图片</span>
              <img v-else :src='getImgView(text)' height='25px' alt='' style='max-width: 80px; font-size: 14px' />
            </template>

            <span slot='action' class='caozuo' slot-scope='text, record'>
              <a @click='handleDetailPage(record)'>查&nbsp;看</a>
<!--              <span v-if="$yqHasPermission('knowledgeBase:edit')||sysUserId===record.createByUserId">
                  <a-divider type='vertical' />
                  <a @click='handleEdit(record)'>编辑</a>
                </span>
              <span v-if="$yqHasPermission('knowledgeBase:delete')||sysUserId===record.createByUserId">
                  <a-divider type='vertical' />
                  <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                    <a>删除</a>
                  </a-popconfirm>
                </span>-->
            </span>
            <template slot='tooltip' slot-scope='text'>
              <a-tooltip placement='topLeft' :title='text' trigger='hover' overlayClassName='oneClickHelpTooltip'>
                <div class='tooltip'>
                  {{ text }}
                </div>
              </a-tooltip>
            </template>

            <template slot='isPrivate' slot-scope='text'>
<!--              <a-icon :theme='"filled"' :type='text==="1"?"lock":"unlock"' :style='{color:text==="1"?"#FE9400":"#4BD863"}'></a-icon>-->
              <a-icon  :type='text==="1"?"lock":"global"' :style='{color:text==="1"?"#FE9400":"#4BD863"}'></a-icon>
              {{ text==='1'?'私有':'公开' }}
            </template>

            <template slot='knowledgeType' slot-scope='text'>
              <knowledge-icon :knowledgeType="text"></knowledge-icon>
            </template>
          </a-table>
        </div>
      </div>
    </card-frame>
  </template>
  <script>
  import cardFrame from '@views/oneClickHelp/knowledgePreview/modules/CardFrame.vue'
  import {JeecgListMixin} from '@/mixins/JeecgListMixin'
  import knowledgeIcon from '@views/opmg/knowledgeManagement/knowledgeBase/modules/KnowledgeIcon.vue'
  import treeExpand from '@comp/oneClickHelp/tree/KnowledgeTopicTreeExpand.vue'
  import Empty from '@/components/oneClickHelp/Empty.vue'
  import {YqFormSearchLocation} from '@/mixins/YqFormSearchLocation'
  export default {
    name: 'KnowledgeBaseList',
    mixins: [JeecgListMixin, YqFormSearchLocation],
    components: {
      cardFrame,
      // addKnowlegeModal,
      knowledgeIcon,
      treeExpand,
      Empty
    },
    data() {
      return {
        maxLength:50,
        locale: {
          emptyText: <Empty/>
        },
        description: '设备表管理页面',
        sysUserId:this.$store.getters.userInfo.id,
        formItemLayout: {
          labelCol: {style: 'width:70px'},
          wrapperCol: {
            style: 'width:calc(100% - 70px)'
          }
        },
        visibility: [
          { label: '公开', value: '0' },
          { label: '私有', value: '1' }
        ],
        // 表头
        columns: [
          {
            title: '标\u0020题',
            dataIndex: 'title',
            customCell: () => {
              let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
              return {
                style: cellStyle
              }
            },
            scopedSlots: {
              customRender: 'tooltip'
            }
          },
          {
            title: '主题名称',
            dataIndex: 'topicName',
            customCell: () => {
              let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
              return {
                style: cellStyle
              }
            },
            scopedSlots: {
              customRender: 'tooltip'
            }
          },
          {
            title: '能见度',
            dataIndex: 'isPrivate',
            customCell: () => {
              let cellStyle = 'text-align: center;width:80px'
              return {
                style: cellStyle
              }
            },
            scopedSlots: {
              customRender: 'isPrivate'
            }
          },
          {
            title: '知识类型',
            dataIndex: 'knowledgeType',
            customCell: () => {
              let cellStyle = 'width:80px'
              return {
                style: cellStyle
              }
            },
            scopedSlots: {
              customRender: 'knowledgeType'
            }
          },
          {
            title: '创建人员',
            dataIndex: 'createBy',
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 80px;max-width:200px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '创建时间',
            dataIndex: 'createTime',
            customCell: () => {
              let cellStyle = 'text-align: center;width: 160px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '最近更新时间',
            dataIndex: 'updateTime',
            customCell: () => {
              let cellStyle = 'text-align: center;width: 160px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '操\u0020作',
            dataIndex: 'action',
            width: 100,
            scopedSlots: {
              customRender: 'action'
            }
          }
        ],
        url: {
          list: '/kbase/knowledges/list',
          delete: '/kbase/knowledges/delete',
          deleteBatch: '/kbase/knowledges/deleteBatch'
        },
        disableMixinCreated: true,
        treeRootNode:{
          id: 'root',
          key: 'root',
          title: '全部主题',
          canDisplay: true,
          userWithPermission: true,
          isExpanded:true
        }
      }
    },
    activated() {
      this.loadData()
    },
    mounted() {
      this.loadData(1)
    },
    methods: {
      treeSeletedSearch(id = '') {
        this.queryParam.knowledgeTopicId = id
        this.loadData(1)
      },
      handleDetailPage: function(record) {
        this.$parent.pButton2(1, record)
      }
    }
  }
  </script>

  <style lang='less' scoped>
  @import "~@assets/less/onclickStyle.less";
  @import '~@assets/less/scroll.less';
  </style>
