import { getAction } from '@api/manage'
import {
  queryConfigureDictItem
} from '@api/api'
export const knowledgeSearchAttachmentPreviewMixins = {
  data() {
    return {
      url: {
        list: '/kbase/knowledges/search',
        unformattedText: '/kbase/knowledges/content' // 获取无格式内容
      },
      unformattedContent: ''
    }
  },
  methods: {
    /*从配置字典中获取kkfileview文件在线预览服务地址*/
    getKkfileviewURL() {
      return new Promise((resolve) => {
        queryConfigureDictItem({
          parentCode: 'kkfileviewURL',
          childCode: 'url'
        }).then((res) => {
          if (res.success) {
            resolve(res.result && res.result != 'null' ? res.result : 'null')
          } else {
            resolve('null')
          }
        }).catch((err) => {
          resolve('null')
        })
      })
    },
    /*附件预览*/
    getKkFileViewURL(fileUrl) {
      let url = window._CONFIG['staticDomainURL'] + '/' + fileUrl
      return this.kkfileviewUrl + '/onlinePreview?url=' + encodeURIComponent(Base64.encode(url))
    },
    async newPageDisplay(file, id) {
      if (!this.kkfileviewUrl || this.kkfileviewUrl == 'null') {
        let data = ''
        let newWindow = null
        let sn = file.originalFilename.substring(file.originalFilename.lastIndexOf(".")).toLowerCase();
        // 去掉file.originalFilename的所有html标签
        let originalFilename = file.originalFilename.replace(/<[^>]*>/g, "");
       
        if (sn == '.jpg' || sn == '.jpeg' || sn == '.png') {
          let imgUrl = window._CONFIG['staticDomainURL'] + '/' + file.fileUrl
          data = `
                    <html>
                      <head>
                        <title>${originalFilename}</title>
                      </head>
                      <body>
                        <img src='${imgUrl}'/>
                      </body>
                    </html>`;
          newWindow = window.open(data, '_blank')
        } else {
          this.loading = true
          await getAction(this.url.unformattedText, {
            id: id,
            originalFilename: originalFilename,
            filename: file.fileUrl
          }).then((res) => {
            this.loading = false
            if (res.success) {
              this.unformattedContent =this.setUnformattedContent(res.result)
            } else {
              this.unformattedContent = ''
            }
          }).catch(() => {
            this.unformattedContent = ''
            this.loading = false
          })
          data = this.unformattedContent ? this.unformattedContent : '该文件未被索引，请下载查看文件内容'
          newWindow = window.open('', '_blank');
        }
        newWindow.document.write(data);
        newWindow.document.close();
      } else {
        let url = this.getKkFileViewURL(file.fileUrl)
        window.open(url)
      }
    },
    /*解析无格式文本中的换行符、空格、制表符*/
    setUnformattedContent(str) {
      if (str && str.length > 0) {
        // 通常在 Windows 上和 \n 一起出现, 或者忽略 \r，
        let formattedStr = JSON.stringify(str).replace(/(\\r?\\n|\u2028|\u2029)/g, "\<br>").replace(/\\t/g, "\\ &nbsp;&nbsp;&nbsp;&nbsp;")
        return JSON.parse(formattedStr).replace(/\\/g, "&nbsp;")
      } else {
        return ''
      }
    },
  }
}