<template>
  <div :class="theme">
    <div v-for="(item, index) in dataSource" :key="index" class="item-info">
      <div style="display:flex;align-items:center;">
        <div v-if="item.knowledgeType==1" class="icon blue">
          <img src="~@assets/knowledge/wendang.png" />
        </div>
        <div v-if="item.knowledgeType==0" class="icon green">
          <img src="~@assets/knowledge/wenben.png" />
        </div>
        <div :class="['topicName', 'over', theme=='dark'? 'maxwidth': ''] " @click="goDetail(item)" v-html="setName(item.title)"></div>
        <div v-if="item.updateTime && theme=='dark'" class="time">{{ item.updateTime }}</div>
      </div>
      <div class="flex">
        <div :class="['info-btn', theme=='dark'?'info-btn1':'']">{{ item.topicName }}</div>
        <div :class="['info-btn', theme=='dark'?'info-btn2':'']">{{ item.createBy }}</div>
        <div v-if="item.updateTime && theme!=='dark'" class="info-btn">{{ item.updateTime }}</div>
      </div>
      <div class="desc over over3" v-html="setName(item.plan)"></div>

      <div v-if="item.attachment && item.attachment.length > 0" class="fileWrapper">
        <div class="divider">
          <div class="line"></div>
          <div class="text">附件</div>
          <div class="line"></div>
        </div>
        <div
          v-for="(citem,cindex) in item.attachment"
          class="fileContent"
          :key="item.id + '_' + cindex"
        >
          <div class="fileHead">
            <img
              src="~@assets/knowledge/hxz.png"
              style="width: 18px; height: 18px;margin-right:6px;"
            />
            <div
              class="fileName over"
              v-html="setName(citem.originalFilename)"
              @click="newPageDisplay(citem,item.id)"
            ></div>
          </div>
          <div class="bg" v-if="citem.content&&citem.content.length>0">
            <div class="desc over over3" v-html="setName(citem.content)"></div>
          </div>
        </div>
      </div>
      <div class="like-info">
        <div class="like">
          <a-icon type="like"></a-icon>
          {{ item.likeCount || 0 }}
        </div>
        <div class="line"></div>
        <div class="dislike">
          <a-icon type="dislike"></a-icon>
          {{ item.unlikeCount || 0 }}
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { knowledgeSearchAttachmentPreviewMixins } from '@/views/opmg/knowledgeManagement/knowledgeSearch/modules/knowledgeSearchAttachmentPreviewMixins'
export default {
  name: 'knowledgeItem',
  mixins: [knowledgeSearchAttachmentPreviewMixins],
  props: {
    kkfileviewUrl: {
      type: [String],
      required: true
    },
    theme: {
      type: String,
      default: ''
    },
    dataSource: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      dataSource:[],
    }
  },
  watch: {
    dataSource: {
      immediate: true,
      handler(nval) {
        if (nval && nval.length > 0) {}
      }
    }
  },
  data() {
    return {

    }
  },
  methods: {
    setName(str) {
      if (this.theme == 'dark') {
        let partter = /<font color='red'>/g
        return str.replace(partter, "<font color='#409EFF'>")
      } else {
        return str
      }
    },
    // 跳转详情
    goDetail(item) {
      this.$emit('goDetail', item)
    },
  }
}
</script>
<style lang="less" scoped>
@color: #1890FF;
@color2: #40de5a;
@font-size-color: #000000a5;
.item-wrapper {

    margin-top: 5px;

    .item-info {
      padding: 30px 0;
      font-size: 14px;
      border: 1px solid #E9E9E9;
      border-radius: 2px;
      padding: 25px;
      margin-bottom: 24px;
      color: @font-size-color;
      .icon {
        width: 18px;
        height: 18px;
        border-radius: 2px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 10px;
        &.blue {
          background-color: @color;
        }
        &.green {
          background-color: @color2;
        }
        img {
          width: 12px;
          height: 12px;
        }
      }

      .topicName {
        font-size: 20px;
        font-weight: 600;
        max-width: calc(100% - 30px);
        cursor: pointer;

        &:hover {
          color: #409eff;
          text-decoration: underline;
        }
      }
      .maxwidth {
        max-width: calc(100% - 200px);
      }

      .flex {
        display: flex;
        margin: 12px 0;

        .info-btn {
          color: @font-size-color;
          padding: 0px 8px;
          background-color: #f5f5f5;
          border: 1px solid #ddd;
          border-radius: 5px;
          margin-right: 10px;
        }
        .info-btn1 {
          color: #3F9EFF !important;
          background: rgba(63, 158, 255, 0.14) !important;
          border: 1px solid #3F9EFF !important;
        }
        
        .info-btn2 {
          color: #FAAD1A !important;
          background: rgba(250, 173, 26, 0.14) !important;
          border: 1px solid #FAAD1A !important;
        }
      }

      .time {
        font-size: 14px;
        color: rgba(203, 203, 203, 0.50);
        letter-spacing: 0;
        font-weight: 400;
        margin-top: 8px;
        margin-left: 40px;
      }
      .desc {
        letter-spacing: 1px;
        line-height: 24px;
        -webkit-line-clamp: 3 !important;
      }

      .over {
        /* 溢出用省略号*/
        display: -webkit-box;
        -webkit-line-clamp: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        /* autoprefixer: off */
        -webkit-box-orient: vertical;
        /* autoprefixer: on */
      }
      .over3 {
        line-clamp: 3;
        -webkit-line-clamp: 3 !important;
      }

      .like-info {
        display: flex;
        align-items: center;
        margin-top: 12px;
        margin-bottom: 5px;
        color: #00000072;

        .line {
          width: 1px;
          height: 14px;
          background-color: #d9d9d9;
          margin: 0 10px;
        }
      }
      .divider {
        display: flex;
        align-items: center;
        margin-top: 20px;
        margin-left: 15px;
        .line {
          width: 183px;
          border-top: 1.5px dashed #D7D7D7;
          height: 1px;
        }
        .text{
          padding: 0 40px 0 47px;
        }
      }
      .fileWrapper {
        .fileContent {
          margin-bottom: 10px;
          .fileHead {
            height: 59px;
            display:flex;
            align-items:center;
          }
          &:last-child {
            margin-bottom: 0;
          }
          .fileName {
            font-size: 15px;
            font-weight: 600;
            color: @color;
            cursor: pointer;
            text-decoration: underline;

            &:hover {
              color: @color;
              text-decoration: underline;
            }
          }
          .bg {
            padding: 24px;
            background-color: #F7F7F7;
            border-radius: 2px;
          }
          .desc {
            line-height: 38px;
          }
        }
      }
    }
  }
</style>