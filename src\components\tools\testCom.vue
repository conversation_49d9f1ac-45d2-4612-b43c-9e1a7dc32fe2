<template>
  <a-row style="height: 100%; display: flex; overflow-x: auto">
    <a-col ref="print" id="printContent" class="abcdefg" :xl="12" :lg="12" :md="10" :sm="10" :xs="10"
      style="overflow-x: auto; min-width: 350px">
      <div>
        <p style="padding: 16px 24px; background-color: #fafafa; border-bottom: 1px solid #e8e8e8; font-size: 16px">
          {{title}}连接测试
        </p>
      </div>
      <!--签字-->
      <a-col style="padding: 0 24px 24px">
        <a-form :form="form">
          <a-row>
            <a-col :span="24" class="aCol" v-for="(item,index) in dataList" :key="index">
              <a-form-item :label="item.name" :labelCol="labelCol" :wrapperCol="wrapperCol" v-if="item.type == 'input'">
                <a-input :placeholder="'请输入' + item.name"
                  v-decorator="[item.field,{rules: [{ required: true, message: item.name + '不能为空' }]}]" allowClear />
              </a-form-item>
              <a-form-item :label="item.name" :labelCol="labelCol" :wrapperCol="wrapperCol"
                v-if="item.type == 'select'">
                <a-select :placeholder="'请选择' + item.name"
                  v-decorator="[item.field,{rules: [{ required: true, message: item.name + '不能为空' }]}]" allowClear>
                  <a-select-option v-for="(items,i) in item.option" :key="i" :value=items.value>{{ items.name }}
                  </a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item :label="item.name" :labelCol="labelCol" :wrapperCol="wrapperCol"
                v-if="item.type == 'checkbox'">
                <a-checkbox-group v-decorator="[item.field,{rules: [{ required: true, message: item.name + '不能为空' }]}]">
                  <a-checkbox v-for="(items,i) in item.option" :key="i" :value=items.value>{{ items.name }}
                  </a-checkbox>
                </a-checkbox-group>
              </a-form-item>
              <a-form-item :label="item.name" :labelCol="labelCol" :wrapperCol="wrapperCol"
                v-if="item.type == 'radio'">
                <a-radio-group v-decorator="[item.field,{rules: [{ required: true, message: item.name + '不能为空' }]}]">
                  <a-radio v-for="(items,i) in item.option" :key="i" :value=items.value>{{ items.name }}
                  </a-radio>
                </a-radio-group>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row class="btnStyle">
            <a-col :span="24" :style="{ textAlign: 'center' }">
              <a-button type="shallow" @click="handelSubmit" :disabled="buttonDisadled"> 提交 </a-button>
              <a-button :style="{ marginLeft: '8px' }" @click="handleReset"> 重置 </a-button>
            </a-col>
          </a-row>
        </a-form>
      </a-col>
    </a-col>
    <a-col :span="12" class="contTwo" style="overflow-x: auto; min-width: 350px">
      <div class="returnDiv">
        <p class="returnTitle">{{title}}连接测试</p>
        <div v-html="result" style="padding: 5px 16px 0 24px"></div>
      </div>
    </a-col>
  </a-row>
</template>
<script>
  import ACol from 'ant-design-vue/es/grid/Col'
  import ARow from 'ant-design-vue/es/grid/Row'
  import ATextarea from 'ant-design-vue/es/input/TextArea'
  import {
    getAction
  } from '@/api/manage'

  export default {
    components: {
      ATextarea,
      ARow,
      ACol,
    },
    name: 'testCom',
    props: {
      title: {
        type: String,
        default: '',
      },
      url: {
        type: String,
        default: '',
      },
      dataList: {
        type: Array,
        default: [],
      },
      paramIp: {
        type: String,
        default: '',
      },
    },
    watch: {
      paramIp: {
        handler(nv) {
          this.$nextTick(() => {
            this.form.setFieldsValue({
              ip: nv,
            })
          })
        },
        immediate: true,
      },
    },
    data() {
      return {
        form: this.$form.createForm(this),
        model: {},
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 8
          },
          md: {
            span: 7
          },
          lg: {
            span: 6
          },
          xl: {
            span: 5
          },
        },
        wrapperCol: {
          xs: {
            span: 23
          },
          sm: {
            span: 16
          },
          md: {
            span: 17
          },
          lg: {
            span: 18
          },
          xl: {
            span: 18
          },
        },
        buttonDisadled: false,
        result: '',
      }
    },
    created() {},
    methods: {
      //提交
      handelSubmit() {
        this.form.validateFields((err, values) => {
          if (!err) {
            this.result = ''
            this.buttonDisadled = true
            let param = {}
            this.dataList.forEach((ele) => {
              let field = ele.field
              param[field] = values[field]
            })
            getAction(this.url, param).then((res) => {
              if (res.success) {
                this.result = res.result
                this.buttonDisadled = false
              }
            })
          }
        })
      },
      //刷新
      handleReset() {
        this.form.resetFields()
        this.result = ''
        this.buttonDisadled = false
      },
    },
  }
</script>
<style lang="scss" scoped>
  /*update_begin author:scott date:20191203 for:打印机打印的字体模糊问题 */
  * {
    color: #000000;
    -webkit-tap-highlight-color: #000000;
  }

  /*update_end author:scott date:20191203 for:打印机打印的字体模糊问题 */
  .importDiv {
    width: 60%;
    height: 10em;
    margin: 0 auto;
    border: 1px solid #d9d9d9;
    padding: 18px;
  }

  .returnDiv {
    height: 100%;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
  }

  .returnTitle {
    padding: 16px 24px;
    background-color: rgb(250, 250, 250);
    border-bottom: 1px solid rgb(232, 232, 232);
    font-size: 16px;
  }

  .leftSpan {
    width: 14%;
  }

  .abcdefg .ant-card-body {
    margin-left: 0%;
    margin-right: 0%;
    margin-bottom: 1%;
    border: 0px solid black;
    min-width: 800px;
    color: #000000 !important;
  }

  .explain {
    text-align: left;
    margin-left: 50px;
    color: #000000 !important;
  }

  .explain .ant-input,
  .sign .ant-input {
    font-weight: bolder;
  }

  .aCol {
    margin-bottom: 5px;
  }

  .aCol:first-child {
    margin-top: 10px;
  }

  .btnStyle {
    margin-top: 24px;
  }

  .explain div {
    margin-bottom: 10px;
  }

  /* you can make up upload button and sample style by using stylesheets */
  .ant-upload-select-picture-card i {
    font-size: 32px;
    color: #999;
  }

  .ant-upload-select-picture-card .ant-upload-text {
    margin-top: 8px;
    color: #666;
  }

  #printContent {
    height: 100%;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
  }

  .contTwo {
    margin-left: 16px;
    width: calc(100% - 50% - 16px);
    height: 100%;
  }
</style>