<template>
  <div style="height:100%">
    <keep-alive exclude='networkDeviceDetails'>
      <component style="height:100%" :is="pageName" :data="data" :render-states='renderStates' />
    </keep-alive>
  </div>
</template>
<script>
  import networkDeviceList from './networkDeviceList'
  /*设备查看*/
  import DeviceInfoModal from '@views/devicesystem/deviceshow/DeviceInfoModal'
  export default {
    name: "networkDeviceManage",
    data() {
      return {
        isActive: 0,
        data: {},
        renderStates: {
          showBaseInfo: true,
          showStateInfo: true,
          showDeviceFunction: true,
          showDataAnalysis: true,
          showJournal: true,
          showAlarm: true,
          showDeviceAlarm: true,
        }
      }
    },
    components: {
      networkDeviceList,
      DeviceInfoModal
    },
    created() {
      this.pButton1(0);
    },
    //使用计算属性
    computed: {
      pageName() {
        switch (this.isActive) {
          case 0:
            return "networkDeviceList";
          default:
            return "DeviceInfoModal";
        }
      }
    },
    methods: {
      pButton1(index) {
        this.isActive = index;
      },
      pButton2(index, item) {
        this.isActive = index;
        this.data = item
      }
    }
  }
</script>