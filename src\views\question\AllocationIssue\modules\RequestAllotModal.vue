<template>
  <a-modal
    :title="title"
    :width="modalWidth"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @cancel="handleCancel"
    @ok="handleOk"
    okText=""
    cancelText="关闭"
    wrapClassName="ant-modal-cust-warp"
    style="height: 550px; overflow: hidden; overflow-y: auto"
    :centered="true"
  >
    <a-row style="margin-bottom: 5px">
      <a-col style="width: 18%; float: left; margin-left: 55px; font-size: 15px">问题内容：</a-col>
      <a-col :span="14" style="font-size: 15px">
        <div>电脑开机之后在桌面点击文档直接卡死机，然后一重启就蓝屏，电脑还冒烟</div>
      </a-col>
    </a-row>
    <a-row style="margin-bottom: 5px">
      <a-col style="width: 15%; float: left; margin-left: 69px; font-size: 15px">请求人：</a-col>
      <a-col :span="16" style="font-size: 15px">
        <div>李工程师</div>
      </a-col>
    </a-row>
    <a-row style="">
      <a-col style="width: 12%; float: left; margin-left: 85px; font-size: 15px">备注：</a-col>
      <a-col :span="16" style="font-size: 15px">
        <div>无法修复</div>
      </a-col>
    </a-row>
    <a-divider />
    <a-row style="margin-bottom: 20px">
      <a-col style="width: 15%; float: left; margin-left: 69px; font-size: 15px">处理人：</a-col>
      <a-col :span="12">
        <a-select
          defaultValue="请选择"
          placeholder="请选择"
          @change="handleChange"
          :allowClear="true"
          style="width: 98.1%"
        >
          <a-select-option value="1">毛工程师</a-select-option>
          <a-select-option value="2">张工程师</a-select-option>
        </a-select>
      </a-col>
    </a-row>
    <a-row style="margin-left: 10px">
      <a-col style="width: 12.5%; float: left; margin-left: 73px; font-size: 15px">备注：</a-col>
      <a-col :span="12">
        <textarea
          name=""
          placeholder="请输入备注"
          style="width: 100%; resize: none; height: 105px; border: 1px solid #d9d9d9"
        ></textarea>
      </a-col>
    </a-row>
  </a-modal>
</template>
<script>
export default {
  name: 'RequestAllotModal',
  data() {
    return {
      title: '操作',
      visible: false,
      confirmLoading: false,
      /* 弹框宽 */
      modalWidth: '30%',
      form: this.$form.createForm(this),
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
    }
  },
  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      this.model = Object.assign({}, record)
      this.visible = true
      //编辑页面禁止修改角色编码
      if (this.model.id) {
        this.roleDisabled = true
      } else {
        this.roleDisabled = false
      }
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let formData = Object.assign(this.model, values)
          // let ids = formData.id
          // let remarkss =  formData.remarks
        }
      })
    },
    handleCancel() {
      this.close()
    },
    handleChange(value) {
    },
  },
}
</script>
<style scoped></style>
