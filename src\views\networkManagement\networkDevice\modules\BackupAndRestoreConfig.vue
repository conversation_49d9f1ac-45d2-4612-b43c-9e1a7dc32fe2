<template>
  <a-tabs style='padding: 0px' type='card' :tab-bar-gutter='10' :animated='false' :active-key='defaultActiveKey' default-active-key='1'   @change='callback'>
    <a-tab-pane key='1' tab='备份'>
      <backup-configure-list ref='backupConfigureList' :restore-btn-visible='deviceRestoreVisible'></backup-configure-list>
    </a-tab-pane>
    <a-tab-pane key='2' tab='还原' v-if='deviceRestoreVisible==1'>
     <restore-configure-list ref='restoreConfigureList'></restore-configure-list>
    </a-tab-pane>
  </a-tabs>
</template>

<script>
import  RestoreConfigureList from './RestoreConfigureList'
import  BackupConfigureList from './BackupConfigureList'
import { ajaxGetDictItems } from '@api/api'
export default {
  name: 'BackupAndRestoreConfig',
  components: {
    BackupConfigureList,
    RestoreConfigureList
  },
  data() {
    return {
      defaultActiveKey: '1',
      record: {},
      deviceRestoreVisible:'0'
    }
  },
  watch: {
    data: function(val) {
      this.show(val)
    }
  },
  methods: {
    callback(key) {
      let that = this
      that.defaultActiveKey = key
      setTimeout(
        function() {
          if (key == '1') {
            that.$refs.backupConfigureList.show(that.record)
          } else if (key == '2') {
            that.$refs.restoreConfigureList.show(that.record)
          }
        }, 100)
    },
    show(record,visible) {
      let that = this
      that.record = record
      that.deviceRestoreVisible=visible
      this.callback('1')
    },
    initDictData(dictCode, listname) {
      //根据字典Code, 初始化字典数组
      ajaxGetDictItems(dictCode, null).then((res) => {
        if (res.success) {
          this[listname] = res.result[0].value
        }
      })
    },
  }
}
</script>
<style scoped lang='less' >

</style>