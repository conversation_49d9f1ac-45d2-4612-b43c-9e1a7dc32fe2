<template>
  <div>
    <a-table :columns="columns1" :data-source="checkLevelList" :pagination="false" rowKey="id" :scroll="{ x: 450 }">
      <template slot="handle" slot-scope="tex, record, index">
        <a-space class="listener-add">
          <span><a-icon type="delete" @click="deleteProperty(index)" /></span>
        </a-space>
      </template>
    </a-table>
    <a-modal
      title="服务级别选择"
      :width="'60%'"
      :maskClosable="false"
      :visible="slaLevelFormShow"
      @ok="addSlaLevel"
      @cancel="hideSlaLevels"
    >
      <div>
        <a-table
          ref="table"
          :pagination="false"
          bordered
          rowKey="id"
          :columns="columns"
          :dataSource="serviceLevelList"
          :rowSelection="{selectedRowKeys: checkedKeys, onChange: handleRowClick, type: 'radio'}"
        >
          <template slot="tooltip" slot-scope="text">
            <a-tooltip placement="topLeft" :title="text" trigger="hover">
              <div class="tooltip">
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
      </div>
    </a-modal>
  </div>
</template>

<script>
import mixinPanel from '../mixins/mixinPanel'
import { ajaxGetDictItems } from '@api/api'
import { getAction } from '@api/manage'

export default {
  mixins: [mixinPanel],
  props: {
    slaLevelFormShow: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    }, processKey: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      serviceLevelList: [],
      checkLevelList: [],
      checkedKeys: [],
      columns: [
        {
          title: '操作类型',
          dataIndex: 'slaType_dictText'
        }
      ],
      columns1: [
        {
          title: '操作类型',
          dataIndex: 'slaType_dictText'
        },
        {
          title: '操作',
          dataIndex: 'handle',
          isUsed: false,
          fixed: 'right',
          key: 'handle',
          align: 'center',
          scopedSlots: { customRender: 'handle' }
        }
      ]
    }
  },
  created() {
    this.initPriorityData('priorityLevel')
  },
  mounted() {
    this.getSlaLevel()
    this.initProperties()
  },
  methods: {
    initProperties() {

      if (this.element.businessObject.extensionElements) {
        let data = this.element.businessObject.extensionElements.values
          .filter((item) => item.$type === this.descriptorPrefix + 'Properties')
          .shift()
        if (data.values) {
          this.checkLevelList = []
          this.setLevelListById(data.values[0].value)
        }
      }
    },
    setLevelListById(id) {
      this.checkedKeys = [id]
      getAction('/sla/level/protocol/list', { id: id, pageSize: 1000 }).then(({ result, success }) => {
        if (success) {
          this.checkLevelList = result.records
          for (let i = 0; i < this.checkLevelList.length; i++) {
            let levelData = JSON.parse(this.checkLevelList[i].levelData)
            let keys = Object.keys(levelData)
            this.checkLevelList[i].dynamicColumns = []
            for (let k = 0; k < keys.length; k++) {
              let v = levelData[keys[k]].replace('d', '天')
              let v1 = v.replace('h', '小时')
              this.checkLevelList[i][keys[k]] = v1
              this.columns.forEach((item) => {
                if (item.dataIndex === keys[k]) {
                  this.checkLevelList[i].dynamicColumns.push({
                    title: item.title,
                    dataIndex: item.dataIndex
                  })
                }
              })
            }
          }
        }
      })
    },
    getSlaLevel() {
      getAction('/sla/level/protocol/list', { processKey: this.processKey, pageSize: 1000 }).then(({
                                                                                                     result,
                                                                                                     success
                                                                                                   }) => {
        if (success) {
          this.serviceLevelList = result.records
          for (let i = 0; i < this.serviceLevelList.length; i++) {
            let levelData = JSON.parse(this.serviceLevelList[i].levelData)
            let keys = Object.keys(levelData)
            this.serviceLevelList[i].dynamicColumns = []
            for (let k = 0; k < keys.length; k++) {
              let v = levelData[keys[k]].replace('d', '天')
              let v1 = v.replace('h', '小时')
              this.serviceLevelList[i][keys[k]] = v1
              this.columns.forEach((item) => {
                if (item.dataIndex === keys[k]) {
                  this.serviceLevelList[i].dynamicColumns.push({
                    title: item.title,
                    dataIndex: item.dataIndex
                  })
                }
              })
            }
          }
        }
      })
    },
    hideSlaLevels() {
      this.slaLevelFormShow = false
    },
    addSlaLevel() {
      if (this.checkedKeys.length === 0) {
        this.$message.warn('请选择服务级别')
        return
      }
      this.save()
      this.checkLevelList = []
      this.serviceLevelList.forEach((item) => {
        if (item.id === this.checkedKeys[0]) {
          this.checkLevelList.push(item)
        }
      })
      this.hideSlaLevels()
    },
    deleteProperty(idx) {
      this.checkLevelList = []
      this.checkedKeys = []
      this.save()
    },
    save() {
      let extensionElements = this.modeler.get('moddle').create('bpmn:ExtensionElements')
      let tem = this.element.businessObject.get('extensionElements')
      extensionElements.values = []
      if (tem && tem.values > 0) {
        extensionElements.values = tem.values.filter((item) => item.$type !== this.descriptorPrefix + 'Properties')
      }

      if (this.checkedKeys.length > 0) {
        const propertiesElement = this.modeler.get('moddle').create(this.descriptorPrefix + 'Properties')

        const propertyElement = this.modeler.get('moddle').create(this.descriptorPrefix + 'Property')
           propertyElement['name'] ='slaLevelId'
           propertyElement['value'] =this.checkedKeys[0]

          propertiesElement.get('values').push(propertyElement)

        extensionElements.get('values').push(propertiesElement)
      }
      this.updateProperties({
        extensionElements:
          extensionElements.get('values') && extensionElements.get('values').length > 0 ? extensionElements : undefined,
      })
    },
    handleRowClick(e) {
      this.checkedKeys = e
    },
    /*获取优先级下拉数据*/
    initPriorityData(dictCode) {
      if (dictCode != null && dictCode != '') {
        //根据字典Code, 初始化字典数组
        ajaxGetDictItems(dictCode, null).then((res) => {
          if (res.success) {
            let priorityList = res.result
            for (let i = 0; i < priorityList.length; i++) {
              this.columns.splice(this.columns.length, 0, {
                title: '优先级' + priorityList[i].text,
                dataIndex: priorityList[i].value
              })
              this.columns1.splice(this.columns1.length - 1, 0, {
                title: '优先级' + priorityList[i].text,
                dataIndex: priorityList[i].value
              })
            }
          }
        })
      }
    }
  }
}
</script>

<style lang="less" scoped>
.listener-add {
  color: #1890ff;
  cursor: pointer;
}

.SlaLevels-form-con {
  height: 50vh;

  .SlaLevels-form-item-label {
    display: inline-block;
    width: 80px;
    text-align: right;
  }

  .SlaLevels-form-item-wrap {
    display: inline-block;
    width: calc(100% - 80px);
  }

  // .params-title {
  //   display: flex;
  //   justify-content: space-between;
  // }
  // .params-table {
  //   margin-top: 8px;
  // }
}
</style>