<template>
  <j-modal
    :title='title'
    :width='width'
    :centered='true'
    :visible='visible'
    :destroyOnClose='true'
    switchFullscreen
    cancelText='关闭'
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @cancel='handleCancel'
  >
    <template slot="footer">
      <a-button  @click="handleCancel"> 关闭 </a-button>
      <a-button  type="primary" @click='handleOk("false")'> 保存 </a-button>
      <a-button v-if='model.isPrivate!=="1"' type="primary" @click='handleOk("true")'> 发布 </a-button>
    </template>

    <a-spin :spinning='confirmLoading'>
      <j-form-container :disabled='disableSubmit'>
        <a-form-model ref='form' :model='model' :rules='validatorRules' slot='detail' v-bind='formItemLayout'>
          <a-row>
            <a-col :span='24'>
              <a-form-model-item label='分类名称' prop='topicId'  v-if="parentTopicId">
                <div style="display: flex; align-items: center;">
                  <a-tree-select
                    v-model='model.topicId'
                    style='width: 100%; margin-right: 8px;'
                    :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                    :replaceFields="{key:'id',value:'id',title:'topicName',children:'children'}"
                    :tree-data='topicList'
                    placeholder='请选择分类名称'
                    :allow-clear='true'>
                  </a-tree-select>
                  <a-button type="primary" @click="handleAddTopic" style="flex-shrink: 0;">添加</a-button>
                </div>
              </a-form-model-item>
              <a-form-model-item label='主题名称' prop='topicId' v-if="!parentTopicId">
                <a-tree-select
                  v-model='model.topicId'
                  style='width: 100%'
                  :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                  :replaceFields="{key:'id',value:'id',title:'title',children:'children'}"
                  :tree-data='topicList'
                  placeholder='请选择主题名称'
                  :allow-clear='true'>
                </a-tree-select>
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
              <a-form-model-item label='标题' prop='title'>
                <a-input
                  v-model='model.title'
                  :allow-clear='true'
                  autocomplete='off'
                  placeholder='请输入标题' />
              </a-form-model-item>
            </a-col>

            <a-col :span='24'>
              <a-form-model-item label='能见度' prop='isPrivate'>
                <a-radio-group v-model='model.isPrivate' :disabled='!!model.id&&sysUserId!==model.createByUserId'  :options='visibility'/>
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
              <a-form-model-item label='知识类型' prop='knowledgeType'>
                <a-radio-group v-model='model.knowledgeType' :disabled='!!model.id' :options='knowledgeTypeList'/>
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
              <a-form-model-item label='获取内容方式' prop='method'>
                <a-radio-group :default-value="'0'" v-model='model.method'>
                  <a-radio value='1' key='contentOption_1' @click='fucusContent'>
                    模板
                  </a-radio>
                  <a-radio value='2' key='contentOption_2' @click='fucusContent'>
                    导入文件
                  </a-radio>
                </a-radio-group>
              </a-form-model-item>
            </a-col>
            <a-col :span='24' v-if='model.method==="1"' >
              <a-form-model-item label='选择模板' prop='template' :rules='validatorRules.template' :key='"template"'>
                <a-select v-model='model.template' placeholder='请选择模板' :allow-clear='true' @change='changeTemplate'>
                  <a-select-option v-for='item in templateList' :value='item.id' :key="'template'+item.id"
                                   :label='item.templateName'>
                    {{ item.templateName }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span='24' v-if='model.method==="2"'>
              <a-form-model-item prop='files' :key='"files"'>
                <span slot='label'>
                  <span>选择文件</span>
                  <a-popover title='说明'>
                    <template slot='content'>
                      <p>文件不能超过20MB</p>
                      <p>支持文件格式：.docx</p>
                      <p>限制只可选择一个文件</p>
                    </template>
                    <a-icon style='margin-left:5px;font-size: 20px; line-height: 40px' theme='twoTone' type='question-circle' />
                  </a-popover>
                </span>
                <word-upload
                  v-model='model.tempFiles'
                  :upload-action='url.importFile'
                  :file-list='fileList'
                  :is-show-upload-icon='false'
                  :text='"选择文件"'
                  :multiple="false"
                  :number='1'
                  :accept='accept'
                  :showUploadList='false'
                  :before-upload="beforeUpload"
                  @change='changeImportFile'>
                </word-upload>
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
              <a-form-model-item label='内容' prop='plan'>
                <j-editor v-model='model.plan' :upload-url='uploadUrl' :imgBizPath='"knowledges/image"' />
              </a-form-model-item>
            </a-col>

            <a-col :span='24'>
              <a-form-model-item>
                <span slot='label'>
                  <span>上传附件</span>
                  <a-popover title='说明'>
                    <template slot='content'>
                      <p>单个文件不能超过20MB</p>
                    </template>
                    <a-icon style='margin-left:5px;font-size: 20px; line-height: 40px' theme='twoTone' type='question-circle' />
                  </a-popover>
                </span>
              <knowledge-upload v-model='model.fileNames' :number='5' :file-paths='model.filePaths' :upload-action='uploadUrl' :bizPath='"knowledges/file"' @change='changeFile' ></knowledge-upload>
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
              <a-form-model-item label='其他设置'>
                <a-checkbox-group :disabled='!!model.id&&sysUserId!==model.createByUserId' v-model='model.otherConfig' :options='otherSettingOption'/>
              </a-form-model-item>
            </a-col>
            <a-col :span='24' v-if='model.processInstanceId'>
              <a-form-model-item label='关联' prop='relation'>
                <span v-html='model.relation'></span>
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </j-form-container>
    </a-spin>

    <!-- 添加分类弹窗 -->
    <topic-modal
      ref="topicModal"
      :fromKnowledgeBase="true"
      :knowledgeParentId="parentTopicId"
      @knowledgeBaseOk="handleTopicModalOk">
    </topic-modal>
  </j-modal>
</template>
<script>
import { getAction, httpAction } from '@api/manage'
import wordUpload from '@views/opmg/knowledgeManagement/knowledgeBase/modules/WordUpload.vue'
import { setImgAllPath,setImgRelativePath } from '@/utils/imagePathAboutTinymce'
import {visibility,knowledgeType,getFileNamesAndFilePaths} from '@views/opmg/knowledgeManagement/knowledgeBase/modules/dataListAndFunc.js'
import knowledgeUpload from '@views/opmg/knowledgeManagement/knowledgeBase/modules/KnowledgeUpload.vue'
import {ValidateOptionalFields,ValidateRequiredFields} from '@/utils/rules.js'
import TopicModal from '@views/opmg/knowledgeManagement/knowledgeTopic/modules/TopicModal.vue'
export default {
  name: 'AddKnowledgeModal',
  components: { wordUpload,knowledgeUpload,TopicModal },
  props: {
    //判断知识是否来自流程
    addFromFlowable:{
      type:Boolean,
      required:false,
      default:false
    },
    //指定的父主题ID，用于过滤主题列表
    parentTopicId:{
      type:String,
      required:false,
      default:''
    }
  },
  data() {
    return {
      title: '新增',
      width: '900px',
      disableSubmit: false,
      visible: false,
      confirmLoading: false,
      uploadUrl:window._CONFIG['domianURL']+"/kbase/knowledges/template/upload2Minio",
      sysUserId:this.$store.getters.userInfo.id,
      formItemLayout: {
        labelCol: {
          xs:{span:24 },
          sm:{span:24},
          md:{span:4}
        },
        wrapperCol: {
          xs:{span:24 },
          sm:{span:24},
          md:{span:19}
        }
      },
      showUploadList: {
        showPreviewIcon : true,
        showRemoveIcon: true,
        previewIcon: 'eye', // 自定义预览图标
      },
      visibility: visibility,
      knowledgeTypeList:knowledgeType,
      otherSettingOption: [
        { label: '允许评论', value: 'allowComment',key:'allowComment', checked: false },
        { label: '允许下载附件', value: 'allowDownload',key:'allowDownload', checked: false  }],
      /*contentOption: [
        { label: '模板', value: '1', key: 'contentOption_1', checked: false },
        { label: '导入文件', value: '2', key: 'contentOption_2', checked: false }
      ],*/
      model: {
        isPrivate: '0',
        knowledgeType:0,
        method: '0',
        plan: '',
        filePaths:''
      },
      // uploadAction:window._CONFIG['domianURL']+'/sys/common/uploadMinio',
      accept: '.docx',
      fileList: null,
      templateList: [],
      topicList: [],
      otherConfig:["allowComment","allowDownload"],
      topicPermission:false,//从流程处添加知识库，需要判断当前用户是否拥有已绑定了该流程主题的权限，true有权限，false无权限
      validatorRules: {
        topicId: [
          { required: true, message: '请选择分类名称' }
        ],
        title: [
          { required: true, validator: (rule, value, callback) =>ValidateRequiredFields(rule, value, callback,'标题',50,2) }
        ],
        template: [
          { required: false, message: '请选择模板！' }
        ],
        files: [
          { required: false, message: '只支持docx格式', trigger: 'change' }
        ],
        plan: [
          { required: true, message: '请输入内容', trigger: 'blur' }
        ],
      },
      url: {
        add: '/kbase/knowledges/add',
        edit: '/kbase/knowledges/edit',
        topicTree: '/knowledges/topic/selectTree',
        topicTreeByParent: '/knowledges/topic//getChildListBatch', // 根据父ID获取主题树
        template: '/kbase/knowledges/template/listAll',
        templateContent: '/kbase/knowledges/template/queryById',
        importFile: window._CONFIG['domianURL'] + '/kbase/knowledges/template/word2Html',
        kInfo:'/kbase/knowledges/queryById',//通过知识id获取知识信息
        addFromFlowable:'/kbase/knowledges/addFromFlowable',
        queryTopicByProcessDefinitionKey:'/knowledges/topic/getTopicByProcessDefinitionKey'
      }
    }
  },
  created() {
    this.getTopicList()
    this.getTemplateList()
  },
  methods: {
    changeFile(value,path){
      this.model.filePaths=path
    },
    /**
     * 获取主题下拉数据
     */
    getTopicList() {
      console.log(this.parentTopicId)
      this.topicList=[]
      // 如果指定了父主题ID，则根据父ID获取主题列表
      if (this.parentTopicId) {
        this.getTopicListByParent(this.parentTopicId)
      } else {
        // 否则获取全部主题列表
        getAction(this.url.topicTree).then((res) => {
          if (res.success) {
            if(res.result&&res.result.children){
              this.topicList = res.result.children
              this.setTreeNodeStatus(this.topicList)
            }
          }
        }).catch((err) => {
          this.$message.error(err.message)
        })
      }
    },

    /**
     * 根据父ID获取主题下拉数据
     */
    getTopicListByParent(parentId) {
      this.topicList=[]
      getAction(this.url.topicTreeByParent, { parentIds: parentId }).then((res) => {
        if (res.success) {
          if(res.result){
            // 如果返回的是数组，直接使用；如果是对象且有children，使用children
            this.topicList = res.result
            this.setTreeNodeStatus(this.topicList)
          }
        }
      }).catch((err) => {
        this.$message.error(err.message)
      })
    },
    setTreeNodeStatus(list) {
      for (let i = 0; i < list.length; i++) {
        let m = list[i]
        m.disabled = !m.userWithPermission
        if (m.children && m.children.length > 0) {
          this.setTreeNodeStatus(m.children)
        }
      }
    },

    /**
     * 获取模板下拉数据
     */
    getTemplateList() {
      getAction(this.url.template).then((res) => {
        if (res.success) {
          this.templateList = res.result
        }
      }).catch((err) => {
        this.$message.error(err.message)
      })
    },
    /**
     * 切换模板，加载对应模板内容
     */
    changeTemplate(value) {
      getAction(this.url.templateContent, { id: value }).then((res) => {
        if (res.success) {
          this.$nextTick(() => {
            if (res.result.content&&res.result.content.length>0){
              this.model.plan =setImgAllPath(res.result.content)
            }
          })
        }
      })
    },
    /**
     * 切换获取内容获取方式
     */
    fucusContent(value) {
      if (this.model.method === '0') {
        this.model.method = value.target.value
      } else {
        if (value.target.value === this.model.method) {
          this.model.method = '0'
        }
      }
    },
    /**
     * 选择文件格式是否正确
     */
    beforeUpload(file) {
      var testmsg = file.name.substring(file.name.lastIndexOf('.') + 1)
      const extension = this.accept.includes(testmsg)
      if (!extension) {
        this.$message.error('上传文件只能是' + this.accept + '格式')
      }
      return extension;
    },
    /**
     * 选择文件后，展示文件内容
     */
    changeImportFile(path, files) {
      if (files.length > 0) {
        if (files[0].success) {
          this.$message.success(files[0].message)
          this.model.plan =setImgAllPath(files[0].result)
        } else {
          this.$message.error(files[0].message)
        }
      }
    },

    getTopicByProcessDefinitionKey(key){
      return new Promise((resolve, reject)=>{
        getAction(this.url.queryTopicByProcessDefinitionKey,{processDefinitionKey:key}).then((res)=>{
          if (res.success){
            resolve({
              success:true,
              data:res.result,
              message:res.message
            })
          }else {
            reject({
              success:false,
              data:'',
              message:res.message
            })
          }
        }).catch((err)=>{
          reject({
            success:false,
            data:'',
            message:err.message
          })
        })
      })
    },
    /*判断当前用户是否拥有已绑定了某个流程的主题权限*/
    getTopicPermission(list,topicId) {
      for (let i = 0; i < list.length; i++) {
        let m = list[i]
         if (m.id===topicId){
           this.topicPermission=true
           return
         }else{
           if (m.children && m.children.length > 0) {
             this.getTopicPermission(m.children,topicId)
           }
         }
      }
    },
    /* 流程：加入知识库*/
    addKnowledgeFromProcess(record) {
      let fileInfo= getFileNamesAndFilePaths(record.files)
      let dataObj={
        processInstanceId: record.processInstanceId,
        knowledgeType:0,
        isPrivate: '0',
        method: '0',
        plan: record.plan,
        title: record.title,
        filePaths:fileInfo.filePaths,
        fileNames: fileInfo.fileNames,
        relation: "流程名称："+record.processDefinitionName+"<br>流程编码："+record.processDefinitionKey+"<br>实例id：" + record.processInstanceId,
        // processInstanceType: record.processInstanceType,
        otherConfig: "allowComment,allowDownload",
      }
      this.getTopicByProcessDefinitionKey(record.processDefinitionKey).then((res) => {
        if (res.success) {
          //流程绑定了主题
          if (res.data){
            this.topicPermission=false
            this.getTopicPermission(this.topicList,res.data.id)
            //用户有主题权限
            if (this.topicPermission){
                dataObj['topicId']=res.data.id
            }
            //用户无主题权限
            else{
             this.$message.info('该流程所绑定的知识主题，当前用户无操作权限！')
            }
          }
          this.edit(dataObj)
        } else {
          this.$message.warning(res.message)
        }
      }).catch((err) => {
        this.$message.warning(err.message)
      })
    },
    /*新增知识*/
    add() {
      this.edit({
        knowledgeType:0,
        isPrivate: '0',
        method: '0',
        plan: '',
        otherConfig:"allowComment,allowDownload",
        filePaths:''
      })
    },
    /*编辑知识*/
    edit(record) {
      this.visible = true
      if(!record.id){
        this.$nextTick(() => {
          this.model =JSON.parse(JSON.stringify(record))
          let config=record.otherConfig
          this.model.otherConfig=config&&config.length>0? config.split(','):[]
        })
      }else {
        this.initEditKInfo(record.id)
      }
    },
    initEditKInfo(id){
      this.confirmLoading=true
      getAction(this.url.kInfo,{id:id}).then((res)=>{
        if(res.success){
          this.model=res.result
          let config=this.model.otherConfig
          this.model.otherConfig=config&&config.length>0? config.split(','):[]
          let plan=this.model.plan
          this.model.plan=plan&&plan.length>0?setImgAllPath(plan):''
          let fileInfo= getFileNamesAndFilePaths(this.model.files)
          this.model.filePaths=fileInfo.filePaths
          this.model.fileNames=fileInfo.fileNames
        }else {
          this.$message.warning(res.message)
        }
        this.confirmLoading=false
      }).catch((err)=>{
        this.$message.warning(err.message)
        this.confirmLoading=false
      })
    },
    /**
     *提交表单数据
     * @param {boolean} publishForAddAndEdit - true发布，false保存不发布
     */
    handleOk(publishForAddAndEdit) {
      let that = this
      that.$refs.form.validate((err, values) => {
        if (err&&!that.confirmLoading) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!that.model.id) {
            if(that.addFromFlowable){
              httpurl +=that.url.addFromFlowable
            }else {
              httpurl +=that.url.add
            }
            method = 'post'
          } else {
            httpurl += that.url.edit
            method = 'put'
          }
          let formData = JSON.parse(JSON.stringify(that.model))
          let fileObj={
            fileUrl:formData.filePaths? formData.filePaths:'',
            originalFilename:formData.fileNames?formData.fileNames:''
          }
          formData.files=JSON.stringify(fileObj)
          delete formData.filePaths
          delete formData.fileNames
          let config=that.model.otherConfig
          formData.otherConfig=config&&config.length>0?config.join(','):''
          formData.publishForAddAndEdit=publishForAddAndEdit
          formData.plan=setImgRelativePath(formData.plan)
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
                that.close()
              } else {
                that.$message.warning(res.message)
              }
              that.confirmLoading = false
            }).catch((err) => {
            that.$message.warning(err.message)
            that.confirmLoading = false
          })
        }
      })
    },
    handleCancel() {
      this.close()
    },
    close() {
      this.visible = false
      this.confirmLoading = false
      this.model={}
    },

    // 处理添加分类
    handleAddTopic() {
      // 获取当前选中的分类ID作为父ID，如果没有选中则使用传入的父ID
      const parentId = this.model.topicId || this.parentTopicId || 'root'
      // 设置TopicModal的父节点ID
      this.$refs.topicModal.knowledgeParentId = parentId
      this.$refs.topicModal.add(parentId)
      this.$refs.topicModal.title = '新增分类'
      this.$refs.topicModal.disableSubmit = false
    },

    // 分类弹窗确认回调
    handleTopicModalOk(newTopic) {
      // 如果有父主题ID，调用topicTreeByParent接口
      if (this.parentTopicId) {
        this.getTopicListByParent(this.parentTopicId)
      } else {
        // 否则重新获取全部分类列表
        this.getTopicList()
      }
      this.$message.success('分类添加成功')

      // 如果新增的分类有返回数据，可以自动选中新增的分类
      if (newTopic && newTopic.id) {
        this.$nextTick(() => {
          this.model.topicId = newTopic.id
        })
      }
    },
  }
}
</script>
<style scoped lang='less'>
@import '~@assets/less/normalModal.less';
</style>