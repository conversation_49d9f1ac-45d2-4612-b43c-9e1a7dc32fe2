/*列表上方操作按钮区域*/
.ant-card-body .table-operator, .table-operator-style {
  margin-bottom: 8px;
}

/** Button按钮间距 */
.table-operator .ant-btn {
  margin: 0 10px 8px 0;
}
.table-operator .ant-btn-group .ant-btn {
  margin: 0;
}

.table-operator .ant-btn-group .ant-btn:last-child {
  margin: 0 10px 8px 0;
}

.table-operator button {
  height:32px !important;
}

.table-operator .ant-btn:hover {
  color: #409eff;
  background: #fff;
  border-color: #b3d8ff;
}

.table-operator .ant-btn:focus {
  color: #409eff;
  background: #fff;
  border-color: #b3d8ff;
}

.table-operator button:nth-child(1):hover {
  color: #fff !important;
  background-color: #409eff !important;
  border-color: #409eff !important;
}

.table-operator button:nth-child(1):focus {
  color: #409eff;
  background: #ecf5ff;
  border-color: #b3d8ff;
}

.table-operator button:nth-child(1) {
  color: #409eff;
  background: #ecf5ff;
  border-color: #b3d8ff;
}
/*列表td的padding设置 可以控制列表大小*/
.ant-table-tbody .ant-table-row td {
  padding-top: 15px;
  padding-bottom: 15px;
  text-align: center;
}

/*列表页面弹出modal*/
.ant-modal-cust-warp {
  height: 100%
}

/*弹出modal Y轴滚动条*/
.ant-modal-cust-warp .ant-modal-body {
  height: calc(100% - 110px) !important;
  overflow-y: auto
}

/*弹出modal 先有content后有body 故滚动条控制在body上*/
.ant-modal-cust-warp .ant-modal-content {
  height: 90% !important;
  overflow-y: hidden
}

/*列表中有图片的加这个样式 参考用户管理*/
.anty-img-wrap {
  height: 25px;
  position: relative;
}

.anty-img-wrap>img {
  max-height: 100%;
}

/*列表中范围查询样式*/
.query-group-cust {
  width: calc(50% - 10px)
}

.query-group-split-cust:before {
  content: "~";
  width: 20px;
  display: inline-block;
  text-align: center
}
/*erp风格子表外框padding设置*/
.ant-card-wider-padding.cust-erp-sub-tab>.ant-card-body {
  padding: 5px 12px
}

/* 内嵌子表背景颜色 */
.j-inner-table-wrapper /deep/ .ant-table-expanded-row .ant-table-wrapper .ant-table-tbody .ant-table-row {
  background-color: #FFFFFF;
}
/*table表头样式*/
::v-deep .ant-table-thead > tr > th {
  text-align: center;
  white-space: nowrap;
}
/*table水平滚动条*/
::v-deep .ant-table-body {
  min-width: 0px !important;
  overflow-x: auto !important;
}
/*table表体内容对齐方式、省略显示*/
::v-deep .ant-table-tbody > tr > td {
  text-align: center;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}
//table提示
/deep/ .tooltip{
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  width: 100% !important;
  height: 100% !important;
  cursor: default;
}
/**隐藏样式-modal确定按钮 */
.jee-hidden {
  display: none
}
/*查询区域*/
.card-style{
  margin-bottom: 16px
}
/** 全局查询按钮的样式*/
.btn-search {
  background: #1e3674;
  border-radius: 4px;
  width: 73px;
  height: 32px;
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: #ffffff;
}
/*全局针对查询表单设置的样式，防止修改btn-search影响其他网页*/
.btn-search-style{
  margin-left: 12px
}
/** 全局重置按钮的样式*/
.btn-reset {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  width: 73px;
  height: 32px;
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: #747679;
}
/*全局针对重置表单设置的样式，防止修改btn-search影响其他网页*/
.btn-reset-style{
  margin-left: 8px
}
/** 全局展开/收起的样式*/
.btn-updown-style{
  margin-left: 8px;
  color: #409eff;
}
/** 全局增加按钮的样式*/
.btn-add,
.btn-confirm {
  background: #ecf5ff;
  border: 1px solid #b3d8ff;
  border-radius: 4px;
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: #409eff;
  width: 73px;
  height: 28px;
  cursor: pointer;
  margin: 0px;
}

/** 全局删除、禁用/启用按钮的样式*/
.btn-del,
.btn-enable {
  background: white;
  border-radius: 4px;
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: #737578;
  width: 73px;
  height: 28px;
  border: 1px solid #dcdfe6;
  cursor: pointer;
  margin: 0 0 0 10px;
}

/deep/ .body-top-card {
  margin-bottom: 12px;

  .ant-card-body {
    height: 80px !important;

    .table-page-search-submitButtons {
      button:nth-child(1):hover {
        background: #1e3674;
        border-color: #1e3674;
        color: #fff !important;
      }
    }
  }
}
.ant-checkbox-checked .ant-checkbox-inner {
  background-color: #409eff !important;
  border-color: #409eff !important;
}

.ant-checkbox-indeterminate .ant-checkbox-inner:after {
  background-color: #409eff !important;
}

.ant-radio-checked .ant-radio-inner {
  border-color: #409eff !important;
}

.ant-radio-inner:after {
  background-color: #409eff !important;
}
//table右侧固定列--操作
.caozuo,/deep/ .caozuo{
  display: inline-block;
  width: 100%;
  text-align: center;
  a {
      color: #409eff !important;
   }
}

.ant-table-row-cell-break-word span a {
  color: #409eff;
}

.ant-table-row-cell-break-word span a[disabled] {
  color: rgba(0, 0, 0, 0.25);
}

/deep/ .ant-checkbox-indeterminate .ant-checkbox-inner:after {
  background-color: #409eff !important;
}

/deep/ .ant-checkbox-checked .ant-checkbox-inner {
  background-color: #409eff !important;
  border-color: #409eff !important;
}

.core .ant-card-body {
  background: #fff !important;
}

/deep/ .core {
  .ant-card-body {
    background: #fff !important;
  }
}
.lastBtn2{
  margin-left: 0;
  margin-right: 0;
  background: #fff;
}
//日期选择
.a-range-picker-choice-date{
  display: inline-block;
  white-space: nowrap;
  width: 100% !important
}
//查询板块，下拉列表多选情况，input出现垂直滚动条
::v-deep .table-page-search-wrapper-style {
  .ant-form-inline {
    .ant-form-item {
      display: flex;
      margin-bottom: 24px;
      margin-right: 0;

      .ant-form-item-control-wrapper {
        flex: 1 1;
        display: inline-block;
        vertical-align: middle;
      }

      > .ant-form-item-label {
        line-height: 32px;
        padding-right: 8px;
        width: auto;
      }
      .ant-form-item-control {
         height: 32px;
         line-height: 32px;
        /* max-height: 50px !important;
         overflow: hidden;
         overflow-y: auto;*/
       /* .ant-select-selection__rendered{
          max-height: 30px !important;
          overflow: hidden;
          overflow-y: auto;
          margin-right:2px !important;
          cursor:pointer;
          .ant-select-selection__choice{
            height: 27px !important;
            line-height: 27px !important;
            max-width: 96%;
          }
        }
        .ant-select-selection__rendered::-webkit-scrollbar {
          !*滚动条整体样式*!
          width: 7px; !*高宽分别对应横竖滚动条的尺寸*!
          height: 7px;
          background-color: #EAEAEA !important;
          border-radius: 3px !important;
          -moz-border-radius: 3px;
        }
        .ant-select-selection__rendered::-webkit-scrollbar-thumb{
          !*滚动条里面小方块*!
          background-color: #D6D6D6 !important;
          border-radius: 3px !important;
          -moz-border-radius: 3px;
        }
        .ant-select-selection--multiple .ant-select-selection__clear, .ant-select-selection--multiple .ant-select-arrow {
          top: 0px !important;
          right: -12px;
        }
        .ant-select-selection--multiple .ant-select-selection__rendered {
          height: auto;
          margin-bottom: 0;
          margin-left: 3px;
        }
        .ant-select, .ant-select-enabled, .ant-select-allow-clear{
          max-height: 50px !important;
        }
        .ant-select-selection__choice__content{
          display: block;
          white-space:normal !important;
          overflow: hidden !important;
          text-overflow: ellipsis !important;
          height: 30px !important;
        }*/
      }
    }
  }
  .table-page-search-submitButtons {
    display: block;
    margin-bottom: 24px;
    white-space: nowrap;
  }
}


