<template>
  <ul v-if="res">
    <li class="core-top-body-li" v-if="!Array.isArray(res)">
      <div class="pao_box other1"></div>
      <div class="pao_box other2"></div>
      <div class="pao_box other3"></div>
      <div class="pao_box other4"></div>
      <div class="pao_box other5"></div>
      <div class="pao_box area">
        <div class="pao_box_count" style="top:65%">{{ res.gatewayType }}</div>
        <div class="pao_box_title"></div>
      </div>
      <div class="pao_box appSystem">
        <div class="pao_box_count">{{ res.offNumber }}</div>
        <div class="pao_box_title">{{ res.subtitle }}</div>
      </div>

      <div class="pao_box netServer">
        <div class="pao_box_count">{{ res.countNumber | unitChange }}</div>
        <div class="pao_box_title">{{ res.gatewayName }}</div>
      </div>

      <div class="pao_box appDatabase">
        <div class="pao_box_count">{{ res.countStatistics | unitChange }}</div>
        <div class="pao_box_title">{{ res.gatewayStatistics }}</div>
      </div>
    </li>
    <li class="core-top-body-li" v-else>
      <div class="pao_box other1" ></div>
      <div class="pao_box other2" ></div>
      <div class="pao_box other3" ></div>
      <div class="pao_box other4" ></div>
      <div class="pao_box other5" ></div>
      <div class="pao_box area" >
        <div class="pao_box_count">{{ res[1].countStatistics | unitChange }}</div>
        <div class="pao_box_title">{{ res[1].gatewayStatistics }}</div>
      </div>
      <div class="pao_box appSystem" >
        <div class="pao_box_count">{{ res[1].countNumber | unitChange }}</div>
        <div class="pao_box_title">{{ res[1].gatewayName }}</div>
      </div>
      <div class="pao_box netServer" >
        <div class="pao_box_count">{{ res[0].countNumber | unitChange }}</div>
        <div class="pao_box_title">{{ res[0].gatewayName }}</div>
      </div>
      <div class="pao_box appDatabase" >
        <div class="pao_box_count">{{ res[0].countStatistics }}</div>
        <div class="pao_box_title">{{ res[0].gatewayStatistics }}</div>
      </div>
    </li>
  </ul>
</template>
<script>
export default {
  props: ['bubbleData'],
  data() {
    return {
      res: {},
    }
  },
  filters: {
    unitChange(value) {
      if (!!value) {
        value = value.toString()
        if (value.indexOf('%') != -1) {
          return value
        } else {
          return value + '个'
        }
      }
    },
  },
  created() {
    this.res = this.bubbleData
  },
  mounted() {},
  methods: {},
}
</script>
<style lang="less" scoped>
ul {
  width: 100%;
  height: 100%;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  .core-top-body-li {
    height: 100% /* 180/80 */;
    position: relative;
    .pao_box {
      position: absolute;
      border-radius: 100%;
      .pao_box_count {
        height: 0.5rem /* 40/80 */;
        font-size: 0.25rem /* 20/80 */;
        color: #ffffff;
        letter-spacing: 0;
        text-align: center;
        line-height: 0.5rem /* 40/80 */;
        position: absolute;
        top: 50%;
        margin-top: -40px;
        left: 0;
        right: 0;
        min-width: 80px;
      }
      .pao_box_title {
        height: 0.25rem /* 20/80 */;
        font-size: 0.175rem /* 14/80 */;
        color: #ffffff;
        letter-spacing: 0;
        text-align: center;
        line-height: 0.25rem /* 20/80 */;
        position: absolute;
        top: 45%;
        left: 0;
        right: 0;
        min-width: 80px;
      }
    }
    .other1 {
      box-shadow: inset 0 0 20px 0 #54f3fc;
      left: 5%;
      top: 12%;
      width: 40px;
      height: 40px;
    }
    .other2 {
      box-shadow: inset 0 0 20px 0 #54f3fc;
      left: 18%;
      top: 64%;
      width: 30px;
      height: 30px;
    }
    .other3 {
      box-shadow: inset 0 0 10px 0 #54f3fc;
      left: 71%;
      top: 18%;
      width: 20px;
      height: 20px;
    }
    .other4 {
      box-shadow: inset 0 0 20px 0 #54f3fc;
      left: 54%;
      top: 62%;
      width: 40px;
      height: 40px;
    }
    .other5 {
      box-shadow: inset 0 0 20px 0 #54f3fc;
      left: 41%;
      top: 28%;
      width: 40px;
      height: 40px;
    }
    .area {
      box-shadow: inset 0 0 50px 0 #54f3fc;
      left: 17%;
      top: 8%;
      width: 97px !important;
      height: 97px !important;
    }
    .appSystem {
      box-shadow: inset 0 0 50px 0 #e79818;
      left: 66%;
      top: 35%;
      width: 80px !important;
      height: 80px !important;
    }
    .netServer {
      box-shadow: inset 0 0 50px 0 #e8e212;
      left: 50%;
      top: 2%;
      width: 80px !important;
      height: 80px !important;
    }
    .appDatabase {
      box-shadow: inset 0 0 50px 0 #1e7cf5;
      left: 33%;
      bottom: 4%;
      width: 80px !important;
      height: 80px !important;
    }
  }
}
</style>