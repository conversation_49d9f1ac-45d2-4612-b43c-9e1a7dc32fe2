<template>
  <a-select :getPopupContainer = "(target) => target.parentNode" :placeholder="placeholder" :disabled="disabled" :value="value" @change="handleInput">
    <a-select-option value="">请选择</a-select-option>
    <a-select-option v-for="(item, key) in dictOptions" :key="key" :value="item.id">
      <span style="display: inline-block;width: 100%" :title=" item.text || item.label ">
        {{ item.text || item.label }}
      </span>
    </a-select-option>
  </a-select>
</template>

<script>
import { ajaxGetAreaItems, ajaxGetDictItems, getDictItemsFromCache } from '@/api/api'

  export default {
    name: "YqAreaCountySelect",  //根据父id获取下级行政区划的选择框
    props: {
      pid: String,
      placeholder: String,
      triggerChange: Boolean,
      disabled: Boolean,
      value: String,
    },
    data() {
      return {
        dictOptions: [],
      }
    },
    watch:{
      pid:{
        immediate:true,
        handler() {
          this.initDictData()
        },
      }
    },
    created() {
      
    },
    methods: {
      initDictData() {
        if (this.pid != null && this.pid != '') {
          //优先从缓存中读取字典配置
          if (getDictItemsFromCache(this.pid)) {
            this.dictOptions = getDictItemsFromCache(this.pid)
            return
          }

          //根据字典Code, 初始化字典数组
          ajaxGetDictItems(this.pid, null).then((res) => {
            if (res.success) {
              this.dictOptions = res.result
            }
          })
        }
      },
/*      initDictData() {
        //根据字典Code, 初始化字典数组
        ajaxGetAreaItems(this.pid, null).then((res) => {
          if (res.success) {
            this.dictOptions = res.result;
          }
        })
      },*/
      handleInput(e) {
        let val;
        val = e
        this.$emit('change', val);
      },
      //手动设置选项
      setCurrentDictOptions(dictOptions){
        this.dictOptions = dictOptions
      },
      //获取当前选项列表
      getCurrentDictOptions(){
        return this.dictOptions
      }
    },
    model: {
      prop: 'value',
      event: 'change'
    }
  }
</script>

<style scoped>
</style>