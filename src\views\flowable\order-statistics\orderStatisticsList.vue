<template>
  <a-row :gutter="10" style="height: 100%;" class="vScroll">
    <a-col style="width:100%;height: 100%;display: flex;flex-direction: column">
      <!-- 查询区域 -->
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0', marginRight: '12px' }" class="card-style"
        style="width: 100%">
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="服务商">
                  <a-select placeholder="请选择服务商" v-model="queryParam.engineerProvider" :allowClear="true">
                    <a-select-option v-for="item in serviceList" :value="item.id">{{ item.providerName}}</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <!-- <a-col :span="spanValue">
                <a-form-item label="服务类型">
                  <a-select placeholder="请选择服务类型" v-model="queryParam.serviceWorkOrderType" :allowClear="true">
                    <a-select-option v-for="item in orderList" :value="item.value">{{ item.title }}</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col> -->
              <a-col :span="spanValue">
                <a-form-item label="工程师">
                  <a-select :getPopupContainer='node=>node.parentNode' :allow-clear='true'
                    v-model="queryParam.engineerId" placeholder="请选择工程师" option-filter-prop="children"
                    :filter-option="filterOption">
                    <a-select-option v-for="item in userList" :value="item.username">
                      <div style="display: inline-block; width: 70%" :title="item.realname">
                        {{ item.realname }}
                        <span style="font-size: 10px; color: rgba(0, 0, 0, 0.45);">{{
                            '(' + item.username + ')'
                          }}</span>
                      </div>
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue" v-show="toggleSearchStatus">
                <a-form-item label="发起时间">
                  <a-range-picker v-model="queryParam.serviceTime" @change="onTimeChange" format="YYYY-MM-DD HH:mm:ss"
                    class="a-range-picker-choice-date"></a-range-picker>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue" v-show="toggleSearchStatus">
                <a-form-item label="评价等级">
                  <a-select placeholder="请选择评价等级" v-model="queryParam.serviceAttitude">
                    <a-select-option :value="item.value" v-for="item in attitudeList">
                      {{item.title}}</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span='colBtnsSpan()'>
                <span :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                  class='table-page-search-submitButtons'>
                  <a-button class='btn-search btn-search-style' type='primary' @click='searchQuery'>查询</a-button>
                  <a-button class='btn-reset btn-reset-style' @click='searchReset'>重置</a-button>
                  <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered="false" style="width: 100%; flex: auto">
        <!-- 操作按钮区域 -->
        <div class="table-operator table-operator-style">
          <a-button @click="selfExport">导出</a-button>
        </div>
        <a-table ref="table" bordered rowKey="id" :columns="columns" :dataSource="dataSource"
          :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }" :pagination="ipagination"
          :loading="loading" @change="handleTableChange">
          <span slot="action" slot-scope="text, record">
            <a @click="handleInstanceInfo(record)" style="color: #409eff">查看详情</a>
          </span>
        </a-table>
      </a-card>
      <process-instance-info-modal ref='processInstanceInfoModalForm' @ok='modalFormOk'></process-instance-info-modal>
    </a-col>
  </a-row>
</template>

<script>
  import ProcessInstanceInfoModal from '@views/flowable/process-instance/modules/ProcessInstanceInfoModal'
  import {
    getAction
  } from '@/api/manage'
  import {
    ajaxGetDictItems,
    getEngineerList
  } from '@/api/api'
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import {
    YqFormSearchLocation
  } from '@/mixins/YqFormSearchLocation'
  export default {
    name: 'orderStatisticsList',
    mixins: [JeecgListMixin, YqFormSearchLocation],
    components: {
      ProcessInstanceInfoModal
    },
    data() {
      return {
        orderList: [],
        serviceList: [],
        userList: [],
        attitudeList: [],
        // 表头
        columns: [{
            title: '服务单号',
            dataIndex: 'auto_warrantyNumber',
            scopedSlots: {
              customRender: 'tooltip'
            },
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 130px;max-width:300px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '服务商',
            dataIndex: 'engineerProvider',
            customCell: () => {
              let cellStyle = 'text-align: left;min-width: 150px;max-width:350px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '工程师',
            dataIndex: 'engineerId_dictText',
            customCell: () => {
              let cellStyle = 'text-align: left;min-width: 150px;max-width:350px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '所属区域',
            dataIndex: 'serviceArea',
            customCell: () => {
              let cellStyle = 'text-align: left;min-width: 150px;max-width:350px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '用户单位',
            dataIndex: 'userUnitName',
            customCell: () => {
              let cellStyle = 'text-align: left;min-width: 150px;max-width:350px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '工单状态',
            dataIndex: 'questionStatus',
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 110px;max-width:350px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '用户评价等级',
            dataIndex: 'serviceAttitude',
            customCell: () => {
              let cellStyle = 'text-align: left;min-width: 110px;max-width:350px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            fixed: 'right',
            width: 147,
            scopedSlots: {
              customRender: 'action'
            }
          }
        ],
        url: {
          list: '/serviceProvider/ticketStatistics',
          service: '/serviceProvider/list',
          deleteBatch: '/device/deviceGroup/deleteBatch',
          exportXlsUrl: '/serviceProvider/exportTicketStatistics'
        },
      }
    },
    created() {
      this.getDictData('serviceWorkOrderType', 'orderList')
      this.getDictData('service_evaluation', 'attitudeList')
      this.getList()
      this.getService()
    },
    methods: {
      getService() {
        getAction(this.url.service, {
          size: 100
        }).then((res) => {
          if (res.success) {
            this.serviceList = res.result.records
          }
        })
      },
      getList() {
        let param = {
          pageSize: 10000
        }
        getEngineerList(param).then((res) => {
          if (res.success) {
            this.userList = res.result
          }
        })
      },
      getDictData(dictCode, list) {
        ajaxGetDictItems(dictCode, null).then((res) => {
          if (res.success) {
            this[list] = res.result
          }
        })
      },
      onTimeChange(date, dateString) {
        this.queryParam.startedAfter = dateString[0]
        this.queryParam.startedBefore = dateString[1]
      },
      filterOption(input, option) {
        return (
          option.componentOptions.children[0].children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
        )
      },
      // 自定义导出列表文件
      selfExport() {
        this.backendExportData('工单统计表')
      },
      handleInstanceInfo: function (record) {
        console.log(record, 'record');

        if (!record.processInstanceId) {
          this.$message.error('工单ID不存在')
          return
        }
        this.$refs.processInstanceInfoModalForm.init(record.processInstanceId)
        this.$refs.processInstanceInfoModalForm.title = '工单详情'
        this.$refs.processInstanceInfoModalForm.disableSubmit = false
      },
    }
  }
</script>
<style lang='less' scoped>
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';
</style>