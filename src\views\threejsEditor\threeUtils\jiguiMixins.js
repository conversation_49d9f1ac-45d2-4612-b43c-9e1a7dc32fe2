import * as THREE from 'three'
export const jiguiMixins = {
    data() {
        return {
            jiGui_wall_material: null,
            jiGui_door_material: null,
            jiGui_ceil_material: null,
            warn_sprite_material: null,
            yellow_sprite_material: null,
            red_sprite_material: null,
        }
    },
    created() {
        this.initJiGuiMaterial()
    },
    methods: {
        initJiGuiMaterial() {
            // let jiguiCeil = require('@/assets/threejsImg/jigui/ceil.png')
            // let jiguiDoor = require('@/assets/threejsImg/jigui/door.png')
            this.jiGui_ceil_material = this.getMeshBasicMaterial("threeImg/jigui/ceil.png")
            this.jiGui_door_material = this.getMeshBasicMaterial("threeImg/jigui/door.png")
            this.jiGui_wall_material = this.getMeshBasicMaterial("threeImg/wall.png")
            this.warn_sprite_material = this.getSpriteMaterial("threeImg/2.png")
            this.yellow_sprite_material = this.getSpriteMaterial("threeImg/2.png");
            this.red_sprite_material = this.getSpriteMaterial("threeImg/3.png");
        },

        createSphere() {
            var sphereGeometry = new THREE.SphereGeometry(50, 50, 50, 50);
            var sphereMaterial = new THREE.MeshLambertMaterial({ color: 0x3CB371 });
            var sphere = new THREE.Mesh(sphereGeometry, sphereMaterial);
            // sphere.position.x = 200;
            sphere.position.y = 50;
            // sphere.position.z = 200;
            sphere.name = 'yq_sphere';

            return sphere;
        },
        createCylinder() {
            var cylinderGeometry = new THREE.CylinderGeometry(50, 50, 100, 100);
            var cylinderMaterial = new THREE.MeshLambertMaterial({ color: 0xcd7054 });
            var cylinder = new THREE.Mesh(cylinderGeometry, cylinderMaterial);
            // cylinder.position.x = -200;
            cylinder.position.y = 50;
            cylinder.name = "yq_cylinder";
            return cylinder

        },
        createCube() {
            var cubeGeometry = new THREE.BoxGeometry(100, 100, 100);
            var cubeMaterial = new THREE.MeshLambertMaterial({ color: 0x9370DB });
            var cube = new THREE.Mesh(cubeGeometry, cubeMaterial);
            cube.position.y = 50;
            cube.name = "yq_cube";
            return cube
        },
        createJiGuiWall(g, m, x, y, z, t, i) {
            let wall = new THREE.Mesh(g, m);//mesh 实体
            wall.position.set(x, y, z);//实体位置
            //添加实体信息
            wall.name = i.name;
            wall.userData = {
                meshType: t,
            }
            wall.addType = 'group';
            return wall;
        },
        createCabinetGroup() {
            let unit = 12;//默认为12U
            let group = new THREE.Object3D();
            this.jiguiWall(group, { unit:unit,brand:"",specifications:""})
            group.name = "yq_group_jigui";
            group.userData.name = "机柜"
            scene.add(group)
            return this.createOutMesh(group)
        },
        jiguiWall(group, data) {
            group.clear();
            let info = { name: "yq_jigui" }
            let jiguiX = 70;//70
            let jiguiZ = 50;//50
            let jiguiY = Number(5 * data.unit) + 20;
            // let jiguiBoxX = new THREE.BoxGeometry(1, jiguiY, jiguiZ);//左右面
            // jiguiBoxX.translate(0, 0, jiguiZ / 2);
            let jiGuiBoxZ = new THREE.BoxGeometry(jiguiX, jiguiY, 1);//前后面
            let jiGuiBoxY = new THREE.BoxGeometry(jiguiX, 1, jiguiZ);//上下面
            // 机柜前后方向平面
            let front = this.createJiGuiWall(jiGuiBoxZ, this.jiGui_wall_material, 0, jiguiY / 2, jiguiZ / 2, "wall", info)
            group.add(front);
            let back = this.createJiGuiWall(jiGuiBoxZ, this.jiGui_wall_material, 0, jiguiY / 2, -jiguiZ / 2, "wall", info)
            group.add(back);

            // 机柜左右方向平面
            let left = this.createJiGuiWall(new THREE.BoxGeometry(1, jiguiY, jiguiZ), this.jiGui_ceil_material, - jiguiX / 2, jiguiY / 2, 0, "wall", info)
            // left.geometry.center(0, 0, jiguiZ / 2);
            // left.translateZ(jiguiZ / 2)
            group.add(left);
            let right = this.createJiGuiWall(new THREE.BoxGeometry(1, jiguiY, jiguiZ), this.jiGui_door_material, jiguiX / 2, jiguiY / 2, 0, "jigui_door", info)
            // right.translateZ(jiguiZ / 2)
            group.add(right);

            // 机柜上下平面
            let ceil = this.createJiGuiWall(jiGuiBoxY, this.jiGui_ceil_material, 0, jiguiY, 0, "wall", info)
            group.add(ceil);
            let floor = this.createJiGuiWall(jiGuiBoxY, this.jiGui_ceil_material, 0, 0, 0, "wall", info)
            group.add(floor);
            group.userData.jiguiX = jiguiX
            group.userData.jiguiY = jiguiY
            group.userData.depth = jiguiZ
            group.userData.jiguiZ = jiguiZ
            group.userData.unit = data.unit
            group.userData.specifications = data.specifications;
            group.userData.brand = data.brand;
        },
        createOutMesh(group) {
            let pos = group.position;
            let ud = group.userData;
            let geometry = new THREE.BoxGeometry(ud.width, ud.height, ud.depth);
            let material = new THREE.MeshBasicMaterial({
                color: 0xfff,
                transparent: true,
                opacity: 0
            });
            let outerMesh = new THREE.Mesh(geometry, material);
            outerMesh.visible = false;
            outerMesh.position.x = pos.x;
            outerMesh.position.y = pos.y;
            outerMesh.position.z = pos.z;
            outerMesh.rotation.x = group.rotation.x
            outerMesh.rotation.y = group.rotation.y
            outerMesh.rotation.z = group.rotation.z
            if(group.userData.glb){
                outerMesh.scale.set(group.scale.x, group.scale.y, group.scale.z)
            }
            outerMesh.name = "yq_outerMesh"
            outerMesh.groupId = group.uuid;
            return outerMesh
        },
        createJigui(obj) {
          if(obj){
            let info = { name: "yq_jigui" }
            let group = new THREE.Object3D();
            let jiguiX = obj.userData.jiguiX;
            let jiguiZ = obj.userData.depth || obj.userData.jiguiZ;
            let jiguiY = obj.userData.jiguiY;
            let jiGuiBoxZ = new THREE.BoxGeometry(jiguiX, jiguiY, 1);//前后面
            let jiGuiBoxY = new THREE.BoxGeometry(jiguiX, 1, jiguiZ);//上下面
            // 机柜前后方向平面
            let front = this.createJiGuiWall(jiGuiBoxZ, this.jiGui_wall_material, 0, jiguiY / 2, jiguiZ / 2, "wall", info)
            group.add(front);
            let back = this.createJiGuiWall(jiGuiBoxZ, this.jiGui_wall_material, 0, jiguiY / 2, -jiguiZ / 2, "wall", info)
            group.add(back);
            // 机柜左右方向平面
            let left = this.createJiGuiWall(new THREE.BoxGeometry(1, jiguiY, jiguiZ), this.jiGui_ceil_material, - jiguiX / 2, jiguiY / 2, 0, "wall", info)
            // left.geometry.center(0, 0, jiguiZ / 2);
            // left.translateZ(jiguiZ / 2)
            group.add(left);
            let right = this.createJiGuiWall(new THREE.BoxGeometry(1, jiguiY, jiguiZ), this.jiGui_door_material, jiguiX / 2, jiguiY / 2, 0, "jigui_door", info)
            // right.translateZ(jiguiZ / 2)
            group.add(right);
            // 机柜上下平面
            let ceil = this.createJiGuiWall(jiGuiBoxY, this.jiGui_ceil_material, 0, jiguiY, 0, "wall", info)
            group.add(ceil);
            let floor = this.createJiGuiWall(jiGuiBoxY, this.jiGui_ceil_material, 0, 0, 0, "wall", info)
            group.add(floor);
            group.name = obj.name || obj.modelCode;
            group.position.set(obj.position.x, obj.position.y, obj.position.z);
            // group.scale.set(obj.scale.x, obj.scale.y, obj.scale.z);
            // group.rotation.set(obj.rotation._x, obj.rotation._y, obj.rotation._z);
            group.rotation.set(obj.rotation.x, obj.rotation.y, obj.rotation.z);
            group.userData = obj.userData;
            scene.add(group)
          }else{
            let unit = 12;//默认为12U
            let group = new THREE.Object3D();
            this.jiguiWall(group, { unit:unit,brand:"",specifications:""})
            group.name = "yq_group_jigui";
            let nowTime = new Date();
            group.userData.name = "机柜_"+nowTime.getTime();
            scene.add(group)
            return this.createOutMesh(group)
          }

        },
        createJGWarnSign(group) {
            let alarmSprite  = new THREE.Sprite(this.warn_sprite_material);
            alarmSprite.position.x = 0;
            alarmSprite.position.y = group.userData.jiguiY + 20;
            alarmSprite.position.z = 0;
            alarmSprite.scale.set(50, 50, 1);
            alarmSprite.name = "warnSign";
            if (group) {
                group.add(alarmSprite);
                alarmSprite.visible = false;
            }
            else {
                return alarmSprite;
            }
        },
       

    }
}