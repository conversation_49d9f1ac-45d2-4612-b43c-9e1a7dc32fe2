<template>
  <div style="height:100%;">
    <keep-alive exclude='reportingDetail'>
      <component :is="pageName" style="height:100%" :data="data" />
    </keep-alive>
  </div>
</template>
<script>
  import reportingList from './reportingStatisticsList.vue'
  import reportingDetail from './modules/reportingStatisticsDetail.vue'
  export default {
    name: "reportingManage",
    data() {
      return {
        isActive: 0,
        data: {}
      };
    },
    components: {
      reportingList,
      reportingDetail
    },
    created() {
      this.pButton1(0);
    },
    //使用计算属性
    computed: {
      pageName() {
        switch (this.isActive) {
          case 0:
            return "reportingList";
            break;

          default:
            return "reportingDetail";
            break;
        }
      }
    },
    methods: {
      pButton1(index) {
        this.isActive = index;
      },
      pButton2(index, item) {
        this.isActive = index;
        this.data = item;
      }
    }
  }
</script>