<template>
  <div style="height:100%">
    <keep-alive exclude='AuditResultDetails'>
      <component style="height:100%" :is="pageName" :data="data" />
    </keep-alive>
  </div>
</template>
<script>
  import AuditResultList from './AuditResultList'
  import AuditResultDetails from './modules/AuditResultDetails'
  export default {
    name: "AuditResultManage",
    data() {
      return {
        isActive: 0,
        data: {},
      }
    },
    components: {
      AuditResultList,
      AuditResultDetails
    },
    created() {
      this.pButton1(0);
    },
    //使用计算属性
    computed: {
      pageName() {
        switch (this.isActive) {
          case 0:
            return "AuditResultList";
          default:
            return "AuditResultDetails";
        }
      }
    },
    methods: {
      pButton1(index) {
        this.isActive = index;
      },
      pButton2(index, item) {
        this.isActive = index;
        this.data = item
      }
    }
  }
</script>