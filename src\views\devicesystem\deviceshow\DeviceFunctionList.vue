<template>
    <!-- table区域-begin -->
    <div class='scroll'>
      <div class="colorBox">
        <span class="colorTotal">设备功能</span>
      </div>
      <a-table
          style='margin-right: 1px'
          ref="table"
          bordered
          :rowKey="(record,index)=>{return index}"
          :columns="columns"
          :dataSource="dataSource"
          :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
          :pagination="ipagination"
          :loading="loading"
          @change="handleTableChange"
        >
          <template slot="tooltip" slot-scope="text">
            <a-tooltip placement="topLeft" :title="text" trigger="hover">
              <div class="tooltip">
                {{ text }}
              </div>
            </a-tooltip>
          </template>
          <span slot="action" slot-scope="text, record" class='caozuo'>
          <a @click="handleImplementation(record)">执行</a>
        </span>
        </a-table>
    <device-function-modal :deviceInfo='deviceInfo' ref="modalForm" @ok="modalFormOk"></device-function-modal>
    </div>
</template>

<script>
import {JeecgListMixin} from '@/mixins/JeecgListMixin'
import JEllipsis from '@/components/jeecg/JEllipsis'
import {deleteAction, getAction, putAction} from '@/api/manage'

import DeviceFunctionModal from './DeviceFunctionModal.vue'

export default {
  name: 'DeviceFunctionList',
  mixins: [JeecgListMixin],
  components: {
    JEllipsis,
    DeviceFunctionModal
  },
  data() {
    return {
      // 表头
      columns: [
        {
          title: '功能标识',
          dataIndex: 'code',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 80px'
            return { style: cellStyle }
          },
        },
        {
          title: '功能名称',
          dataIndex: 'name',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 80px'
            return { style: cellStyle }
          },
        },
        {
          title: '是否异步',
          dataIndex: 'isAsync',
          customRender:(text,record,index)=>{
            return record.isAsync=='1'?'是':'否'
          },
          customCell: () => {
            let cellStyle = 'text-align: center;width: 50px'
            return { style: cellStyle }
          },
        },
        {
          title: '传输协议',
          dataIndex: 'protocol',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 50px'
            return { style: cellStyle }
          },
        },
        {
          title: '描述',
          dataIndex: 'description',
          scopedSlots: { customRender: 'tooltip' },
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 50px;max-width:400px'
            return { style: cellStyle }
          },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 150,
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: '/product/deviceControlCommand/commands' //设备功能table数据请求接口
      },
      queryParam: {
        createTimeRange: [],
        keyWord: '',
      },
      deviceInfo:{},
      disableMixinCreated: true
    }
  },
  watch: {
    deviceInfo(newVal, oldVal) {
      this.init(newVal)
    }
  },
  methods: {
    handleImplementation(record){
      this.$refs.modalForm.edit(record)
      this.$refs.modalForm.title = '执行'
      this.$refs.modalForm.disableSubmit = true
    },
    show(record){
      this.deviceInfo=record
    },
    init(record){
      this.queryParam.productId= this.deviceInfo.productId
      this.queryParam.deviceId =  this.deviceInfo.id
      this.loadData()
    },
  }
}
</script>
<style lang="less" scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
.scroll{
  height: 100%;
  overflow: hidden;
  overflow-y: auto;
}
.colorBox {
  margin-bottom: 10px;
  .colorTotal {
    padding-left: 7px;
    border-left: 4px solid #1e3674;
  }
}
</style>
