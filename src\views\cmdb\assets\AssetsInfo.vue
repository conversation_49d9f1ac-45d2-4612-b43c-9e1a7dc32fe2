<template>
  <div style='height: 100% !important;width:100%;display: flex;flex-direction: column'>
    <div class='header-info'>
      <div style='width:calc(100% - 21px);margin-right: 16px;margin-bottom: -12px'>
        <p class='p-info-title'>
          <span>资产：{{ assetsInfo.assetsName }}</span>
        </p>
        <p class='p-info-product'>
          <span class='span-assets'>资产编号：{{ assetsInfo.assetsCode }}</span>
          <div  class='assets-status-wrapper-top' style='display: flex;justify-content: space-between;flex-flow: row nowrap;margin-top:10px;align-items: center'>
            <div class='assets-status-wrapper' style='white-space: nowrap;height: 32px;line-height: 32px;font-size: 14px'>
              <span class='assets-status'>资产状态:</span>
              <span  v-if="!assetsEditableStatus">
                {{ assetsInfo.statusName }}
                <span v-if="!isFromAssetsUpdate" style="margin-left: 8px; color: #409eff; cursor: pointer" @click="editButton" >
                  <a-icon type="edit" style="margin-right: 6px"/>编辑
                </span>
              </span>
              <span v-else>
                <a-input-group compact style='display: inline-block;margin-left: 10px;line-height:32px !important;'>
                 <a-select
                   v-model='assetsStatus'
                   style="width: 180px"
                   :allowClear="true"
                   label-in-value
                   placeholder='请选择资产状态'
                   @change="selectAssetsStatus">
                   <a-select-option v-for="item in assetsStatusList" :key="item.id" :value="item.id">
                     {{ item.name }}
                   </a-select-option>
               </a-select>
                 <a-button icon='check' @click="preservationStates"></a-button>
                 <a-button icon='close' @click="assetsEditableStatus = false"></a-button>
              </a-input-group>
              </span>
            </div>
            <div v-if="!isFromAssetsUpdate" style='white-space: nowrap;height: 32px;line-height: 32px'>
              <a-button class='action-btn' @click='handleDispatch'>派发</a-button>
            </div>
          </div>
        </p>
      </div>
      <div  v-if="!isFromAssetsUpdate" style='width: 21px'>
        <span class='header-back'>
          <img src='~@assets/return1.png' alt='' @click='getGo'
               style='width: 20px; height: 20px; cursor: pointer' />
        </span>
      </div>
    </div>
    <div style='flex:1;height: 50%'>
      <tabs
        ref='assetsTabs'
        :assets-info.sync='assetsInfo'
        :is-editing='isEditing'
        :render-states='renderStates'
        :isFromAssetsUpdate="isFromAssetsUpdate">
      </tabs>
    </div>
    <assets-dispatch-modal ref='assetsDispatch'  @ok="dispatchOk"> </assets-dispatch-modal>
  </div>
</template>
<script>
import Tabs from '@views/cmdb/assets/modules/AssetsInfoTabs.vue'
import AssetsDispatchModal from '@views/cmdb/assets/modules/AssetsDispatchModal.vue'
import {putAction, getAction } from '@api/manage'
export default {
  name: "AssetsInfo",
  props: {
    data: {
      type: Object
    },
    isEditing: {
      type: Boolean,
      default: true,
      required: false
    },
    renderStates: {
      type: Object,
      required: false,
      default:()=>{return {}}
    },
    hideBack:{
      type:Boolean,
      default:false,
    },
    // from资产变更
    isFromAssetsUpdate: {
      type: Boolean,
      default: false,
    }
  },
  components: {
    Tabs,
    AssetsDispatchModal
  },
  data() {
    return {
      assetsInfo: {},
      departInfo: {},
      assetsStatusList: [],//资产状态下下拉数据

      url: {
        queryDepartInfo: '/assets/assets/findName',//根据资产id，获取所属部门、所属人
        queryStatus: '/assets/assets/queryStatus',//获取资产状态下拉数据

        updateAssetsStatus: '/device/deviceInfo/updateAssetsStatus',
      },

      assetsEditableStatus: false,
      assetsStatus:undefined
    }
  },
  watch: {
    editabled: {
      handler(nv) {
        if (!nv) {
          this.showabled = nv
        }
      },
      immediate: true,
    },
    assetsInfo:{
      handler(val){
        if (!this.isFromAssetsUpdate) {
          this.queryAssetsStatusList()
        }
      }
    }
  },
  mounted() {
    this.initFun()
  },
  methods: {
    initFun() {
      this.assetsInfo = this.data
      this.departInfo={
        departmentId:this.data.departmentId,
        departmentText:this.data.departmentText,
        ownerId:this.data.ownerId,
        ownerText:this.data.ownerText
      }
    },
    queryAssetsStatusList() {
      this.assetsStatus=undefined
      return new Promise((resolve, reject)=>{
        getAction(this.url.queryStatus, {
          assetsId: this.assetsInfo.id,
        }).then((res) => {
          if (res.success) {
            this.assetsStatusList = res.result?res.result:[]
            if(this.assetsStatusList.length>0){
              if(this.assetsInfo.statusId&&this.assetsInfo.statusName){
                this.assetsStatus={
                  key:this.assetsInfo.statusId,
                  label:this.assetsInfo.statusName
                }
              }
            }
          } else {
            this.$message.warning(res.message)
          }
        })
      })
    },
    editButton() {
      this.assetsEditableStatus = true
    },
    /*保存资产状态*/
    preservationStates() {
      let data = {
        updateAssetsId: this.assetsInfo.id,
        status_name: '',
        status_id: '',
      }
      if (this.assetsStatus && Object.keys(this.assetsStatus).length === 2) {
        data.status_id = this.assetsStatus.key
        data.status_name = this.assetsStatus.label
      }
      putAction(this.url.updateAssetsStatus, data).then((res) => {
        if (res.code == 200) {
          let tempInof = Object.assign(JSON.parse(JSON.stringify(this.assetsInfo)), {
            statusId: data.status_id,
            statusName: data.status_name
          })
          this.assetsInfo = tempInof
        }
        this.assetsEditableStatus = false
      }).catch((err) => {
        this.$message.warning(err.message)
        this.assetsEditableStatus = false
      })
    },
    selectAssetsStatus(value) {

    },
    //返回上一级
    getGo() {
      let tabKey = this.$refs.assetsTabs.defaultActiveKey
      if (tabKey == '4') {
        this.$refs.assetsTabs.$refs.journalManagement.pButton1(0)
      } else if (tabKey == '7') {
        this.$refs.assetsTabs.$refs.alarmManagement.pButton1(0)
      }else if (tabKey == '9') {
        this.$refs.assetsTabs.$refs.deviceAlarmManagement.pButton1(0)
      }
      this.$refs.assetsTabs.defaultActiveKey = '1'
      this.$parent.pButton1(0)
    },
    /*打开派发窗口*/
    handleDispatch() {
      let info={
        departId:this.departInfo.departmentId,
        departName:this.departInfo.departmentText,
        userId:this.departInfo.ownerId,
        realname:this.departInfo.ownerText
      }
      this.$refs.assetsDispatch.edit(this.assetsInfo.id,info)
      this.$refs.assetsDispatch.title = '派发'
      this.$refs.assetsDispatch.disableSubmit = false
    },
    /*派发确认:根据资产id，重新获取部门信息*/
    dispatchOk(info) {

      getAction(this.url.queryDepartInfo, {
        id: this.assetsInfo.id,
      }).then((res) => {
        if (res.success) {
          if(info.departId!==this.departInfo.departmentId||info.userId!==this.departInfo.ownerText){
            this.departInfo={
              departmentId:info.departId,
              departmentText:info.departName,
              ownerId:info.userId,
              ownerText:info.realname
            }
            let temAssetsInfo=JSON.parse(JSON.stringify(this.assetsInfo))
            Object.assign(temAssetsInfo,{departmentText:info.departName,ownerText:info.realname })
            this.assetsInfo=temAssetsInfo
          }
        } else {
          this.$message.warning(res.message)
        }
      }).catch((err) => {
        this.$message.warning(err.message)
      })
    }
  }
}
</script>
<style lang='less' scoped='scoped'>
.header-info {
  border-radius: 3px;
  margin-bottom: 16px;
  padding: 24px;
  background: #fff;
  display: flex;
  justify-content: space-between;
  align-items: start;


  /*position: relative;
  background-color: white;
  height: 117px;
  margin-bottom: 16px;
  padding: 10px 0 0 24px;
  border-radius: 3px;*/
}

.p-info-title {
  /*  line-height: 45px;
    height: 45px;*/
  margin-bottom: 20px;
  font-family: PingFangSC-Medium;
  font-size: 18px;
  color: #000000;
}

.span-status {
  margin-left: 12px;
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
}

.span-status img {
  width: 6px;
  height: 6px;
}

.assets-status {
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
}

.status {
  padding-left: 7px;
}

.p-info-product {
  /* line-height: 45px;
   height: 45px;*/
  margin-bottom: 0px;
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
  display: flex;
  justify-content:start;
  align-items: start;
  flex-flow: row wrap;
}

.span-assets {
  margin-right: 119px;
}

.span-tag {
  display: inline-block;
  color: white;
  border-radius: 10px;
  padding: 2px 10px;
  margin-left: 8px;
  margin-bottom:5px
}

.header-back {
  text-align: right;
}




.scroll{
  height: 100%;
  overflow: hidden;
  overflow-y: auto;
}
::v-deep .ant-descriptions-view {
  border-radius: 0px;
}

::v-deep .ant-descriptions-bordered .ant-descriptions-item-label {
  background-color: rgb(250, 250, 250);
  text-align: center;
  width: 17%;
}

::v-deep .ant-descriptions-item-label,
.ant-descriptions-item-content {
  color: rgb(96, 98, 102) !important;
}

::v-deep .ant-descriptions-bordered .ant-descriptions-item-content {
  width: 35%;
}

.colorBox1 {
  margin-bottom: 20px;
  margin-right: 1px;
}

.colorBox {
  margin-bottom: 10px;
}

.colorTotal {
  padding-left: 7px;
  border-left: 4px solid #1e3674;
}

::v-deep .ant-descriptions {
  width: 100%;
}
</style>