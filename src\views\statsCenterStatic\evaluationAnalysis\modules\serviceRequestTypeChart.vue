<template>
  <card-frame :title="title" :titleBgPath="'/statsCenter/evaluate/title1.png'">
    <div slot="bodySlot" class="empty-wrapper" v-if="chartData.length===0">
      <a-spin :spinning="loading" v-if="loading" class="spin"></a-spin>
      <a-list :data-source="[]" v-else />
    </div>
    <div slot="bodySlot" v-else style="height: 100%;width: 100%;" ref="pieChart"></div>
  </card-frame>
</template>

<script>
import cardFrame from '@views/statsCenter/com/cardFrame.vue'
import { getAction } from '@/api/manage'
export default {
  components: {
    cardFrame
  },
  data() {
    return {
      loading: false,
      chartData: [],
      time1: '',
      time2: '',
      url: {
        typeCount: '/data-analysis/order/type/count'
      }
    }
  },
  props: {
    title: '',
    unit: {
      type: String,
      default: ''
    }
  },
  created() {
    this.getMockJson()
    // this.typeCount()
  },
  methods: {
    getMockJson(){
      getAction(location.origin+"/statsCenter/mock/evaluateData.json").then((res) => {
        if(res){
          this.chartData = res.serviceRequestTypeData
          this.$nextTick(() => {
            this.typeCountPie(this.chartData)
          })
        }
      })
    },
    typeCount() {
      this.loading = true
      getAction(this.url.typeCount, {
        time1: this.time1,
        time2: this.time2
      })
        .then(res => {
          if (res.code == 200) {
            if (res.result && res.result.length > 0) {
              let count = 0
              res.result.map(item => {
                count += item.number
              })
              if (count > 0) {
                this.chartData = res.result
                this.$nextTick(() => {
                  this.typeCountPie(res.result)
                })
              }
            }
          } else {
            this.$message.warning(res.message)
          }
          this.loading = false
        })
        .catch(err => {
          this.$message.warning(err.message)
          this.loading = false
        })
    },

    typeCountPie(data) {
      let sum = 0
      let data1 = []
      data.forEach(item => {
        sum += Number(item.number)
        data1.push(item, {
          name: '',
          value: 0,
          labelLine: {
            show: false,
            lineStyle: {
              color: 'transparent'
            }
          },
          itemStyle: {
            color: 'transparent'
          }
        })
      })
      const color = [
        '#0080FF',
        '',
        '#06FFA1',
        '',
        '#36FFCC',
        '',
        '#36FFCC',
        '',
        '#46FDFF',
        '',
        '#74CBFF',
        '',
        '#1EABFB',
        '',
        '#c58353',
        '',
        '#d0405b',
        '',
        '#be71b5'
      ]

      let myChart = this.$echarts.init(this.$refs.pieChart)
      myChart.setOption({
        tooltip: {
          show: true,
          formatter: function(params) {
            console.log(params)
            if (params.name) {
              return params.name + '： ' + params.data.number + '个'
            }
          },
          transitionDuration: 0 //echart防止tooltip的抖动
        },
        label: {
          show: false
        },
        title: {
          text: sum,
          left: '49%',
          top: '39%',
          itemGap: 10,
          textAlign: 'center',
          textStyle: {
            color: '#fff',
            fontSize: 36,
            fontWeight: 600
          },
          subtext: '服务请求总数',
          subtextStyle: {
            color: '#9FA5AD',
            fontSize: 14,
            fontWeight: 500
          }
        },
        legend: {
          show: false // 是否显示图例
        },
        series: [
          {
            type: 'pie',
            radius: ['55%', '60%'],
            center: ['50%', '50%'],
            hoverAnimation: false, // 取消掉饼图鼠标移上去时自动放大
            animation: false,
            minAngle: 5,
            label: {
              show: true,
              normal: {
                formatter: function(params) {
                  console.log(params)
                  if (params.name) {
                    return `{b|${params.name}：}{d|${params.percent} %}`
                  }
                },
                rich: {
                  b: {
                    fontSize: 12,
                    color: '#fff',
                    padding: [0, 10, 0, 0]
                  },
                  d: {
                    fontSize: 12
                  },
                  f: {
                    fontSize: 12,
                    color: '#fff'
                  }
                }
              },
              color: '#fff'
            },
            labelLine: {
              length: 2,
              length2: 40
            },
            itemStyle: {
              normal: {
                color: function(params) {
                  return color[params.dataIndex]
                }
              }
            },
            data: data1,
            z: 1
          },
          {
            type: 'pie',
            radius: ['44%', '49%'],
            center: ['50%', '50%'],
            minAngle: 5,
            hoverAnimation: false, // 取消掉饼图鼠标移上去时自动放大
            animation: false,
            labelLine: {
              show: false
            },
            label: {
              show: false
            },
            itemStyle: {
              normal: {
                color: '#272B36'
              }
            },
            data: data1,
            z: 666
          }
        ]
      })
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    }
  }
}
</script>

<style scoped lang="less">
</style>