<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll zhl zhll">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0', paddingTop: '24px' }">
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery">
            <a-row :gutter="24" ref="row">
              <a-col :xxl="6" :xl="8" :lg="10" :md="12" :sm="24">
                <a-form-item label="场景名称">
                  <a-input placeholder="请输入场景名称" v-model="queryParam.sceneName" allowClear :maxLength="50"></a-input>
                </a-form-item>
              </a-col>
              <a-col :xxl="6" :xl="8" :lg="10" :md="12" :sm="24">
                <span class="table-page-search-submitButtons" :style="{ overflow: 'hidden' } || {}">
                  <a-button type="primary" class="btn-search btn-search-style" @click="searchQuery">查询</a-button>
                  <a-button @click='searchReset' style='margin-left: 10px' class='btn-reset-style'>重置</a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
        <div class="table-operator">
          <a-button @click="addTopo">新增</a-button>
        </div>
      </a-card>
      <a-card :bordered="false" :bodyStyle="{ padding: '0' }"
        style="width: 100%; flex: auto; background-color: rgba(255, 255, 255, 0)">
        <!-- table区域-begin -->
        <div class="topo-list-div">
          <a-row :gutter="16" type="flex" align="middle">
            <a-col v-for="topoInfo in dataSource" :key="topoInfo.id" v-bind="CardColLayout" style="margin-top: 20px">
              <a-card class="topo-card" style="box-shadow: 0 3px 7px -1px rgb(0 0 0 / 16%)">
                <a slot="extra" href="#" style="margin: 0px">
                  <span style="margin-right: 12px" :title=topoInfo.description>
                    <a-icon type="eye" :style="{ fontSize: '16px' }" />
                  </span>
                </a>
                <span class="span-sceneName" @click="editName(topoInfo)">{{ topoInfo.sceneName }}</span>
                <div class="hover" :style="{ height: '200px' }" v-if="!editFlag && topoInfo.topoDataJson"
                  v-html="topoInfo.topoSvg" @click="showTopo(topoInfo)"></div>
                <div class="hover"
                  :style="{ height: '200px', display: 'flex', 'justify-content': 'center', 'align-items': 'center' }"
                  v-else>
                  <div class="ant-empty ant-empty-normal">
                    <div class="ant-empty-image">
                      <svg width="64" height="41" viewBox="0 0 64 41" xmlns="http://www.w3.org/2000/svg">
                        <g transform="translate(0 1)" fill="none" fill-rule="evenodd">
                          <ellipse fill="#F5F5F5" cx="32" cy="33" rx="32" ry="7" />
                          <g fill-rule="nonzero" stroke="#D9D9D9">
                            <path
                              d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z" />
                            <path
                              d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z"
                              fill="#FAFAFA" />
                          </g>
                        </g>
                      </svg>
                    </div>
                    <p class="ant-empty-description">暂无数据</p>
                  </div>
                </div>
                <template slot="actions">
                  <span @click="editTopo(topoInfo)">
                    <a-icon type="edit" style="margin-right: 8px" />修改 </span>
                  <span @click="deleteTopo(topoInfo.id)">
                    <a-icon type="delete" style="margin-right: 8px" />删除 </span>
                </template>
              </a-card>
            </a-col>
          </a-row>
          <a-modal :title="title" :visible="visible" @ok="handleOk" @cancel="visible = !visible">
            <a-form>
              <a-form-item :label="'场景名称'" :labelCol="labelCol" :wrapperCol="wrapperCol" :required="true">
                <a-input v-model="sceneName" placeholder="请输入场景名称" :maxLength="20" />
              </a-form-item>
              <a-form-item :label="'场景描述'" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-textarea v-model="description" placeholder="请输入场景描述" :maxLength="50" />
              </a-form-item>
            </a-form>
          </a-modal>
        </div>
        <div class="pagination-div">
          <a-pagination show-quick-jumper show-size-changer :hideOnSinglePage="false"
            :default-current="ipagination.current" :total="ipagination.total" @change="onChange"
            :page-size="ipagination.pageSize" :pageSizeOptions="ipagination.pageSizeOptions"
            :show-total="(total) => `共 ${ipagination.total} 条`" @showSizeChange="onShowSizeChange" size="small">
          </a-pagination>
        </div>
      </a-card>
      <scene-topo-show ref="topoView" @close="closeModal">
        </scene-topo-show>
      <scene-topo-edit ref="topoEdit" @ok="modalFormOk" @close="closeModal"></scene-topo-edit>
    </a-col>
  </a-row>
</template>

<script>
  import '@/assets/less/TableExpand.less'
  import {
    getAction,
    postAction,
    putAction,
    deleteAction
  } from '@/api/manage'
  import sceneTopoShow from '@views/topo/nettopo/modules/sceneTopoShow'
  import sceneTopoEdit from '@views/topo/nettopo/modules/sceneTopoEdit'
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  export default {
    name: 'AutoControlSceneList',
    mixins: [JeecgListMixin],
    components: {
      sceneTopoShow,
      sceneTopoEdit,
    },
    data() {
      return {
        labelCol: {
          md: {
            span: 5
          },
          sm: {
            span: 3
          },
          xs: {
            span: 24
          },
        },
        wrapperCol: {
          md: {
            span: 19
          },
          sm: {
            span: 21
          },
          xs: {
            span: 24
          },
        },
        CardColLayout: {
          xl: {
            span: 6,
          },
          lg: {
            span: 6,
          },
          md: {
            span: 8,
          },
          sm: {
            span: 12,
          },
          xs: {
            span: 24,
          },
        },
        editFlag: false,
        queryParam: {
          name: '',
        },
        visible: false,
        title: '新增',
        topoId: '',
        dataSource: [],
        description: '',
        sceneName: '',
        ipagination: {
          current: 1,
          pageSize: 8,
          pageSizeOptions: ['8', '16', '24'],
          showTotal: (total, range) => {
            return range[0] + '-' + range[1] + ' 共' + total + '条'
          },
          showQuickJumper: true,
          showSizeChanger: true,
          total: 0,
        },
        url: {
          list: '/autoControl/scene/list',
          add: "/autoControl/scene/add",
          edit: "/autoControl/scene/edit",
        },
        hoverHeight: '0px',
        clientHeight: '',
        staticDomainURL: '',
      }
    },
    created() {
      this.staticDomainURL = window._CONFIG['staticDomainURL'] + '/'
    },
    mounted() {
      this.getTopoImgList()
      this.clientHeight = `${document.documentElement.clientHeight}` //获取浏览器可视区域高度
      let that = this
      window.onresize = function () {
        that.clientHeight = `${document.documentElement.clientHeight}`
      }
    },
    methods: {
      modalFormOk() {
        this.editFlag = false
        // 新增/修改 成功时，重载列表
        this.loadData()
      },
      closeModal() {
        this.editFlag = false
      },
      loadData(arg) {
        if (!this.url.list) {
          this.$message.error('请设置url.list属性!')
          return
        }
        if (arg === 1) {
          this.ipagination.current = 1
        }
        var params = this.getQueryParams() //查询条件
        this.loading = true
        getAction(this.url.list, params).then((res) => {
          if (res.success) {
            this.dataSource = res.result.records
            this.$nextTick(() => {
              let tags = document.getElementsByTagName('image')
              // 修改土图片ip
              Array.from(tags).forEach((tag) => {
                let url = tag.getAttribute('xlink:href')
                if (url) {
                  tag.setAttribute('xlink:href', url)
                }
              })
            })
            this.ipagination.total = res.result.total
            let timer = setTimeout(() => {
              let hoverEl = document.getElementsByClassName('hover')
              if (hoverEl && hoverEl[0]) {
                this.hoverHeight = (hoverEl[0].clientWidth / 5) * 3 + 'px'
              }
              clearTimeout(timer)
            }, 50)
          }
          if (res.code === 510) {
            this.$message.warning(res.message)
          }
          this.loading = false
        })
      },
      topobgimg(e, img) {},
      onShowSizeChange(current, pageSize) {
        this.ipagination.pageSize = pageSize
        this.ipagination.current = current
        this.loadData()
      },
      onChange(pageNumber, pageSize) {
        this.ipagination.pageSize = pageSize
        this.ipagination.current = pageNumber
        this.tabShow = false
        this.loadData()
      },
      getTopoImgList() {
        getAction('/product/topoImg/getPruductImgList').then((res) => {
          this.$store.state.topo.productTopoImg = res.result
        })
      },
      handleOk() {
        if (!/^[a-zA-Z0-9\u4e00-\u9fa5~!@#$%^&*()_+`\-={}:";'<>?,.\/]{1,20}$/.test(this.sceneName)) {
          this.$message.warning('场景名称应为2-20位字符!')
          return
        } else {
          if (this.title == '新增') {
            postAction(this.url.add, {
              sceneName: this.sceneName,
              description: this.description,
            }).then((res) => {
              if (res.code == 200) {
                this.loadData()
                this.visible = false
              } else {
                this.$message.warning(res.message)
              }
            })
          } else if (this.title == '修改') {
            putAction(this.url.edit, {
              id: this.topoId,
              sceneName: this.sceneName,
              description: this.description,
            }).then((res) => {
              if (res.code == 200) {
                this.loadData()
                this.visible = false
                this.topoId = ''
              } else {
                this.$message.warning(res.message)
              }
            })
          } else {
            this.visible = false
          }
          this.sceneName = ''
          this.description = ''
        }
      },
      addTopo() {
        this.visible = true
        this.title = '新增'
      },
      showTopo(record) {
        this.editFlag = false
        this.$refs.topoView.show(record)
      },
      editTopo(record) {
        this.editFlag = true
        this.$refs.topoEdit.edit(record)
      },
      deleteTopo(record) {
        var that = this
        this.$confirm({
          title: '确认删除',
          okText: '是',
          cancelText: '否',
          content: '是否删除数据?',
          onOk: function () {
            that.loading = true
            deleteAction('/autoControl/scene/delete', {
              id: record,
            }).then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.loadData()
              } else {
                that.$message.warning(res.message)
              }
            })
          },
        })
      },
      editName(record) {
        this.visible = true
        this.title = '修改'
        this.sceneName = record.sceneName
        this.topoId = record.id
      },
    },
  }
</script>

<style lang="less" scoped>
  @import '~@assets/less/common.less';

  .table-container {
    background-color: #fff;
  }

  .table-operator {
    margin-bottom: 10px;
  }

  .ant-table-tbody .ant-table-row td {
    padding-top: 15px;
    padding-bottom: 15px;
  }

  .anty-row-operator button {
    margin: 0 5px;
  }

  .ant-modal-cust-warp .ant-modal-body {
    height: calc(100% - 110px) !important;
    overflow-y: auto;
  }

  .ant-modal-cust-warp .ant-modal-content {
    height: 90% !important;
    overflow-y: hidden;
  }

  .table-page-search-wrapper {
    //background-color: #fff;
    //padding: 24px 24px 0 24px;
  }

  .table-page-search-wrapper .table-page-search-submitButtons,
  .table-page-search-wrapper .ant-form-inline .ant-form-item {
    //margin-bottom: 24px;
  }

  .hover {
    cursor: pointer;
    width: 100%;
    height: 100%;
    margin: auto;
    border: 1px solid #e8e8e8;
    position: relative;
  }

  .topobgimg {
    background: url('/assets/topobgImage/bgcolor-gray.png');
  }

  .topobgcolor {
    background-color: #f3f3f3;
  }

  ::v-deep .hover>svg {
    width: 100% !important;
    height: 100% !important;
  }

  ::v-deep .hover>svg>g {
    transform: matrix(1, 0, 0, 1, 1, 1) !important;
  }

  .pagination-div {
    display: flex;
    flex-direction: row-reverse;
    padding-bottom: 20px;
    margin-top: 20px;
  }

  ::v-deep .ant-card-head-title {
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
  }

  ::v-deep .ant-card-extra {
    clear: both;
    margin: 0px;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
    padding: 12px 0 !important;
  }

  ::v-deep .ant-card-head {
    border-bottom: 0px solid #e8e8e8;
  }

  ::v-deep .ant-card-actions {
    border-top: 0px solid #e8e8e8;
  }

  ::v-deep .ant-card-bordered {
    width: 100%;
    position: relative;

    .ant-card-head-wrapper {
      justify-content: flex-end;
    }
  }

  ::v-deep .ant-card-body {
    padding: 0px 22px 16px;
    width: 100%;
    margin: 0px;
  }

  .span-sceneName {
    cursor: pointer;
    position: absolute;
    top: 14px;
  }
</style>