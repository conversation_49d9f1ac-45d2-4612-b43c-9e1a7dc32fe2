
<template>
  <div class="zrljView">
  </div>
</template>
<script>
export default {
  name: 'zrljView',
  data() {
    return {
      // Define any data properties needed for this component
    };
  },
  created() {
    //判断进入到了zrlj视图页面 并直接跳转到大屏；
    sessionStorage.setItem("zrlj_ywtsview", "true");
    this.$router.replace("/operationsView/compNational")
  },
  mounted() {
    // Any initialization logic can go here
  },
  methods: {
    // Define any methods needed for this component
  },
}
</script>
<style scoped lang='less'>

</style>