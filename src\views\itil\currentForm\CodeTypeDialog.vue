<template>
    <el-dialog
    :title = "title"
    :visible.sync="visible"
    width="60%"
    :before-close="handleClose">
     <fm-generate-form :data="startFormJson" :value="variables">
      </fm-generate-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">关 闭</el-button>
    </span>
  </el-dialog>
</template>
<script>
import {getAction} from '@/api/manage'
export default {
  data() {
    return {
      title:"",
      visible:false,
      startFormJson: undefined,
      variables: undefined,
    }
  },
  methods: {
    see(record){
      this.visible=true;
      getAction('/flowableform/umpFlowableForm/queryById',{id:record.id}).then(res=>{
            if(res.success){
            var formData = res.result;
            if(formData &&formData.formJson){
               this.startFormJson =JSON.parse(formData.formJson);
            }
        }
      })
    },
    handleClose(){
      this.visible = false
    }
   
  }
}
</script>

<style lang="scss" scoped>

</style>
