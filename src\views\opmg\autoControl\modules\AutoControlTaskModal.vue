<template>
  <j-modal
    :title='title'
    :width='800'
    :visible='visible'
    :confirmLoading='confirmLoading'
    switchFullscreen
    @ok='handleOk'
    @cancel='handleCancel'
    cancelText='关闭'>

    <a-spin :spinning='confirmLoading'>
      <a-form :form='form'>
        <a-row>
          <a-col :sm='12' :xs='24'>
            <a-form-item label='作业名称' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <a-input v-decorator="['taskName', validatorRules.taskName]" :allowClear='true' autocomplete='off'
                       placeholder='请输入作业名称'></a-input>
            </a-form-item>
          </a-col>
          <a-col :sm='12' :xs='24'>
            <a-form-item
              :labelCol='labelCol'
              :wrapperCol='wrapperCol'
              label='关联场景'>
              <a-select
                v-decorator="['sceneId', validatorRules.scene]"
                :allow-clear='true' :show-search='true'
                option-filter-prop='label' placeholder='请选择关联场景'>
                <a-select-option v-for='item in sceneList' :key="item.id"
                                 :label='item.sceneName'
                                 :value='item.id'>
                  {{ item.sceneName }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :sm='12' :xs='24'>
            <a-form-item :labelCol='labelCol' :wrapperCol='wrapperCol' label='状态'>
              <j-dict-select-tag type='radio' v-decorator="[ 'status', {'initialValue':0}]"
                                 :trigger-change='true' dictCode='quartz_status' />
            </a-form-item>
          </a-col>
          <a-col :sm='12' :xs='24'>
            <a-form-item label='周期执行' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <!-- cron表达式  -->
              <j-cron ref='innerVueCron' v-decorator="['taskCron', validatorRules.taskCron]"
                      @change='setCorn'></j-cron>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>

    </a-spin>
  </j-modal>
</template>

<script>
import { getAction, httpAction } from '@/api/manage'
import pick from 'lodash.pick'
import JCron from '@/components/jeecg/JCron.vue'

export default {
  name: 'AutoControlTaskModal',
  components: { JCron },
  data() {
    return {
      title: '操作',
      visible: false,
      model: {},
      labelCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 12
        },
        md: {
          span: 10
        },
        lg: {
          span: 7
        }
      },
      wrapperCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 12
        },
        md: {
          span: 14
        },
        lg: {
          span: 16
        }
      },
      confirmLoading: false,
      form: this.$form.createForm(this),
      validatorRules: {
        taskName: {
          rules: [{
            required: true,
            message: '请输入作业名称'
          },
            {
              min: 2,
              max: 30,
              message: '长度在2到30个字符',
              trigger: 'blur'
            }
          ]
        },
        scene: {
          rules: [{
            required: true,
            message: '请选择关联场景'
          }]
        },
        taskCron: {
          initialValue: '0 0 0 * * ? *',
          rules: [{
            required: true,
            validator: this.validateCorn
          }]
        }
        // pushAddress: {
        //   rules: [{required: true, pattern: /^([a-z0-9_\.-]+)@([\da-z\.-]+)\.([a-z\.]{2,6})$/, message: '请输入正确的邮箱地址'}]
        // },
      },
      url: {
        add: '/autoControl/task/add',
        edit: '/autoControl/task/edit',
        sceneList: '/autoControl/task/getSceneList',
        validateCorn: '/autoInspection/devopsAutoInspection/cronCheck'
      },
      sceneList: [],
    }
  },
  created() {
    this.getSceneList()
  },
  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(pick(this.model, 'taskName', 'sceneId', 'status', 'taskCron'))
        //时间格式化
      })

    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let formData = Object.assign(this.model, values)
          //时间格式化

          console.log(formData)
          httpAction(httpurl, formData, method).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.$emit('ok')
            } else {
              that.$message.warning(res.message)
            }
          }).finally(() => {
            that.confirmLoading = false
            that.close()
          })
        }
      })
    },
    handleCancel() {
      this.close()
    },
    //周期执行
    setCorn(data) {
      if (data && data.target != null) {
        let dataList = data.target.value.split(' ')
        if (dataList[0] === '*') {
          this.$message.warning('请确认是否每秒都执行')
        }
      } else {
        let dataList = data.split(' ')
        if (dataList[0] === '*') {
          this.$message.warning('请确认是否每秒都执行')
        }
      }
      this.$nextTick(() => {
        this.model.cronExpression = data
      })

      if (Object.keys(data).length === 0) {
        this.$message.warning('请输入cron表达式!')
      }
    },
    validateCorn(rule, value, callback) {
      if (rule.required) {
        if (value && value.length > 0) {
          if (value.split(' ').length>7){
            callback('cron表达式格式错误!')
          }else{
            this.getValidateCornTips(value).then((res)=>{
              callback(res)
            }).catch((err)=>{
              callback(err)
            })
          }
        } else {
          callback('请输入或选择cron表达式')
        }
      } else {
        if (value && value.length > 0) {
          if (value.split(' ').length>7){
            callback('cron表达式格式错误!')
          }else{
            this.getValidateCornTips(value).then((res)=>{
              callback(res)
            }).catch((err)=>{
              callback(err)
            })
          }
        } else {
          callback()
        }
      }
    },

    getValidateCornTips(value){
      return new Promise((resolve, reject)=>{
        getAction(this.url.validateCorn, {
          cronExpression: value
        }).then((res) => {
          if (res.success) {
            resolve()
          } else {
            this.$message.warning(res.message)
            reject('cron表达式格式错误!')
          }
        }).catch((err)=>{
          this.$message.warning(err.message)
          reject('cron表达式格式错误!')
        })
      })
    },
    getSceneList() {
      this.confirmLoading = true
      getAction(this.url.sceneList, {})
        .then((res) => {
          this.sceneList = res.result
        })
        .finally(() => {
          this.confirmLoading = false
        })
    },
  }
}
</script>

<style lang='less' scoped>

</style>