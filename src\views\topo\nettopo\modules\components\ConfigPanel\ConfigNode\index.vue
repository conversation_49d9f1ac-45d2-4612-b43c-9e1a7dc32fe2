<template>
  <div>
    <a-tabs v-if='curCel' :animated='false' v-model='activeKey'>
      <a-tab-pane tab='节点配置' key='1'>
        <div v-if='activeKey == 1' class='tab-con'>
          <div v-if='!hasRectDragNode'>
            <a-row align='middle' v-if="cellShape !== 'text-block' && size === 1">
              <a-col :span='8'>节点名称:</a-col>
              <a-col :span='16'>
                <a-input type='text' :value='nodeConfig.label' style='width: 100%' @change='onLabelChange' />
              </a-col>
            </a-row>
            <a-row align='middle' v-else-if='size === 1' style='margin-top: 12px'>
              <a-col :span='8'>文本内容:</a-col>
              <a-col :span='16'>
                <a-textarea type='text' :value='nodeConfig.label' style='width: 100%' @change='onLabelChange' />
              </a-col>
            </a-row>
            <a-row align='middle' style='margin-top: 12px'>
              <a-col :span='8'>文字大小:</a-col>
              <a-col :span='16'>
                <a-input-number v-model='nodeConfig.labelSize' :min='8' :max='60' @change='onNodeFontSizeChange'
                                style='width: 100%' />
              </a-col>
            </a-row>
            <a-row align='middle' v-if='showLabelPos'>
              <a-col :span='8'>文字位置</a-col>
              <a-col :span='16'>
                <a-select style='width: 100%' v-model='nodeConfig.labelPos' @change='onNodeLablePos'>
                  <a-select-option :value='0'>下</a-select-option>
                  <a-select-option :value='1'>上</a-select-option>
                  <a-select-option :value='2'>右</a-select-option>
                  <a-select-option :value='3'>左</a-select-option>
                </a-select>
              </a-col>
            </a-row>
            <a-row align='middle'>
              <a-col :span='8'>文字颜色</a-col>
              <a-col :span='16'>
                <Shader v-model='nodeConfig.labelColor' @change='onLabelColorChange'></Shader>
              </a-col>
            </a-row>
          </div>
          <!-- 矩形线框装饰--start -->
          <div v-if='showNodeStyle'>
            <a-row align='middle'>
              <a-col :span='8'>线框样式</a-col>
              <a-col :span='16'>
                <a-select style='width: 100%' v-model='nodeConfig.strokeDasharray' @change='onBorderDashChange'>
                  <a-select-option :value='dash' v-for='(dash, idx) in globalGridAttr.edgeDashArr' :key='idx'>
                    <line-dash :dash-type='dash'></line-dash>
                  </a-select-option>
                </a-select>
              </a-col>
            </a-row>
            <a-row align='middle'>
              <a-col :span='8'>线框颜色</a-col>
              <a-col :span='16'>
                <Shader v-model='nodeConfig.stroke' @change='onBorderColorChange'></Shader>
              </a-col>
            </a-row>
            <a-row align='middle'>
              <a-col :span='8'>背景颜色</a-col>
              <a-col :span='16'>
                <Shader v-model='nodeConfig.nodeBgColor' @change='onNodeBgColorChange'></Shader>
              </a-col>
            </a-row>
          </div>
          <!-- 矩形线框装饰--end -->
          <a-row align='middle' v-if='size === 1 && isDeviceNode'>
            <a-col :span='8'>节点样式</a-col>
            <a-col :span='16'>
              <a-select style='width: 100%' :value='nodeConfig.shapeType' @change='onShapeTypeChange'>
                <a-select-option :value='idx' v-for='(shape, idx) in globalGridAttr.shapeTypes' :key='idx'>
                  {{ shape }}
                </a-select-option>
              </a-select>
            </a-col>
          </a-row>
          <div v-if='size === 1'>
            <a-row align='middle'>
              <a-col :span='8'>节点位置:</a-col>
            </a-row>
            <a-row align='middle' style='width: 100%'>
              <a-col :span='24'>
                <a-input type='number' :addonBefore="'X'" style='width: calc(50% - 5px); margin-right: 10px'
                         v-model='nodeConfig.position.x' @change="onPositionChange($event, 'x')" />
                <a-input type='number' :addonBefore="'Y'" style='width: calc(50% - 5px)' v-model='nodeConfig.position.y'
                         @change="onPositionChange($event, 'y')" />
              </a-col>
            </a-row>
          </div>

          <div v-if='showNodeSize'>
            <a-row align='middle'>
              <a-col :span='8'>节点大小:</a-col>
            </a-row>
            <a-row align='middle'>
              <a-col :span='24'>
                <div style='display: flex; justify-content: space-between'>
                  <span class='ant-input-group-wrapper' style='width: calc(50% - 5px)'>
                    <span class='ant-input-wrapper ant-input-group'>
                      <span class='ant-input-group-addon'>宽</span>
                      <a-input-number style='width: 100%' :disabled='groupSizeDisabled' v-model='nodeConfig.size.width'
                                      :min='30' @change="onSizeChange($event, 'width')" />
                    </span>
                  </span>
                  <span class='ant-input-group-wrapper' style='width: calc(50% - 5px)'>
                    <span class='ant-input-wrapper ant-input-group'>
                      <span class='ant-input-group-addon'>高</span>
                      <a-input-number style='width: 100%' :disabled='groupSizeDisabled' :min='30'
                                      v-model='nodeConfig.size.height' @change="onSizeChange($event, 'height')" />
                    </span>
                  </span>
                </div>
              </a-col>
            </a-row>
          </div>
          <div v-if='showRadius'>
            <a-row align='middle'>
              <a-col :span='8'>节点圆角:</a-col>
            </a-row>
            <a-row align='middle'>
              <a-col :span='24'>
                <div style='display: flex; justify-content: space-between'>
                  <span class='ant-input-group-wrapper' style='width: calc(50% - 5px)'>
                    <span class='ant-input-wrapper ant-input-group'>
                      <span class='ant-input-group-addon'>X</span>
                      <a-input-number style='width: 100%' :disabled='groupSizeDisabled' v-model='nodeConfig.radius.rx'
                                      :min='0' @change="onRadiusChange($event, 'rx')" />
                    </span>
                  </span>
                  <span class='ant-input-group-wrapper' style='width: calc(50% - 5px)'>
                    <span class='ant-input-wrapper ant-input-group'>
                      <span class='ant-input-group-addon'>Y</span>
                      <a-input-number style='width: 100%' :disabled='groupSizeDisabled' :min='0'
                                      v-model='nodeConfig.radius.ry' @change="onRadiusChange($event, 'ry')" />
                    </span>
                  </span>
                </div>
              </a-col>
            </a-row>
          </div>
          <a-row align='middle' v-if="curCel.shape === 'text-block' && size === 1">
            <a-col :span='6'>隐藏边框</a-col>
            <a-col :span='14'>
              <a-switch checkedChildren='开' unCheckedChildren='关' defaultChecked
                        v-model='globalGridAttr.textNodeBorder'
                        @change='textBorderChange' />
            </a-col>
          </a-row>
          <a-row align='middle' v-if="size == 1 && cellShape === 'networkGroupNode'">
            <a-col :span='6'>隐藏按钮</a-col>
            <a-col :span='14'>
              <a-switch checkedChildren='开' unCheckedChildren='关' defaultChecked
                        v-model='globalGridAttr.groupButtonHide' @change='groupButtonChange' />
            </a-col>
          </a-row>
          <a-row align='middle' v-if='size === 1 && showIcon'>
            <a-col :span='8'>图标:</a-col>
            <a-col :span='14'>
              <img :src='nodeImg' style='width: 50%; height: 50%' alt='图片加载失败,请检查图片资源或重新上传' />
            </a-col>
          </a-row>
          <div v-if='size === 1 && showIcon'>
            <a-row align='middle'>
              <a-col :span='8'> 图标列表:</a-col>
            </a-row>
            <a-row align='middle' class='img-list'>
              <a-col :span='22'>
                <topo-image-upload
                  isMultiple
                  default-remove
                  bizPath="image/topoIcon"
                  :accept="accept"
                  :handle-change-additional-fun="handleChangeAdditionalFun"
                  :beforeUploadFun="beforeUploadFun"
                  @change="changeImgList"
                  @changeImg="changeImg"
                  :value="imgList"
                ></topo-image-upload>
              </a-col>
            </a-row>
          </div>
        </div>
      </a-tab-pane>
      <a-tab-pane tab='业务属性' key='2' v-if="size === 1 && ['device','dept'].includes(nodeType) ">
        <div v-if='activeKey == 2' class='tab-con'>
          <a-row align='middle' v-if='isVirtual'>
            <a-col :span='8' style='font-weight: bold'>虚拟设备节点</a-col>
          </a-row>
          <a-row v-if='nodeType === "dept" ' align='middle'>
            <a-col :span='8'>设备：</a-col>
            <a-col :span='16'>
              <a-select style='width: 100%' v-model='bindDeviceCode' placeholder='选择设备' @change='bindDevice'>
                <a-select-option v-for='item in availableDevices' :value='item.deviceCode'>{{ item.name }}
                </a-select-option>
              </a-select>
            </a-col>
          </a-row>
          <a-row align='middle' v-else>
            <a-col :span='8'>设备名称：</a-col>
            <a-col :span='16'>
              {{ curCel.data.deviceName }}
            </a-col>
          </a-row>
          <a-row align='middle'>
            <a-col :span='8'>设备标识：</a-col>
            <a-col :span='16'>
              {{ curCel.data.deviceCode }}
            </a-col>
          </a-row>
          <a-row align='middle' v-if='nodeIp'>
            <a-col :span='8'>设备IP：</a-col>
            <a-col :span='16'>
              {{ nodeIp }}
            </a-col>
          </a-row>
          <!--          <a-row align="middle">-->
          <!--            <a-col :span="8">设备类型：</a-col>-->
          <!--            <a-col :span="16">-->
          <!--              {{ curCel.data.deviceType }}-->
          <!--            </a-col>-->
          <!--          </a-row>-->
          <!--          <a-row align="middle">-->
          <!--            <a-col :span="8">面板信息：</a-col>-->
          <!--            <a-col :span="16">-->
          <!--              {{ curCel.nodePanel }}-->
          <!--            </a-col>-->
          <!--          </a-row>-->
          <a-row align='middle' style='margin-top: 12px' v-if="globalGridAttr.topoType === '1'">
            <a-col :span='8'>节点分值:</a-col>
            <a-col :span='16'>
              <a-input-number v-model='nodeScore' :min='0' :max='100' @change='onNodeWeightChange'
                              style='width: 100%' />
            </a-col>
          </a-row>
          <a-row align='middle' style='margin-top: 12px' v-if="globalGridAttr.topoType === '1'&& curCel.data.deviceCode">
            <a-col :span='8'>设备类型:</a-col>
            <a-col :span='16'>
              <a-select style='width: 100%' v-model='deviceType' placeholder='选择设备类型' @change='bindDeviceType'>
                <a-select-option v-for='item in deviceTypes' :value='item.value'>{{ item.label }}
                </a-select-option>
              </a-select>
            </a-col>
          </a-row>
        </div>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script>
import { nodeOpt } from './method'
import FlowGraph from '../../../graph/index'
import TopoImageUpload from '@/components/topo/TopoImageUpload'
import { httpAction, getAction } from '@/api/manage'
// 连线图案组件
import LineDash from '../LineDash.vue'
import Shader from '@/components/shader/shader.vue'
import { checkAccept,checkBeforeUpload, compareFileSizes } from '@comp/yq/yqUpload/YqUploadCommonFuns'
export default {
  name: 'Index',
  components: {
    TopoImageUpload,
    LineDash,
    Shader
  },
  props: {
    globalGridAttr: {
      type: Object,
      default: null,
      required: true
    },
    id: {
      type: String,
      default: ''
    },
    size: {
      type: Number,
      default: 0,
      required: true
    }
  },
  data() {
    return {
      accept:'image/jpg,image/png, image/jpeg, image/jfif, image/pjp, image/pjpeg',
      acceptTips:'jpg、png、jpeg、jfif、pjp、pjpeg',
      activeKey: '1',
      curCel: null,
      iconAdd: '/topo/device/icon/add',
      iconUpdate: 'product/product/updateIcons',
      cellShape: '',
      nodeType: '',
      nodeConfig: {
        label: '',
        labelSize: 14,
        labelColor: '#000000',
        labelPos: 0,
        groupButtonHide: false,
        textNodeBorder: false,
        position: { x: 0, y: 0 },
        size: { width: 0, height: 0 },
        radius: { rx: 0, ry: 0 },
        groupLine: 'solid',
        stroke: '#000000',
        strokeDasharray: '0', // 默认实线
        shape: 1
      },
      imgList: [],
      nodeImg: '',
      nodePanel: '',
      nodeIp: '',
      posArr: [
        { refx: 0.5, refy: '100%', anchor: 'middle', vAnchor: 'top' },
        { refx: 0.5, refy: -4, anchor: 'middle', vAnchor: 'bottom' },
        { refx: '100%', refy: 0.5, anchor: 'start', vAnchor: 'middle' },
        { refx: -4, refy: 0.5, anchor: 'end', vAnchor: 'middle' }
      ],
      selectNodes: [],
      virtualData: null,
      nodeScore: 0,
      deviceType:undefined,
      deviceTypes:[{
        label:'基础支撑软件层',
        value:'software'
      },{
        label:'基础支撑硬件层',
        value:'hardware'
      }],
      availableDevices: [],
      bindDeviceCode: undefined
    }
  },
  created() {
  },
  computed: {
    showNodeStyle() {
      return this.curCel.shape === 'rectDragNode' || this.curCel.shape === 'networkGroupNode'
    },
    hasRectDragNode() {
      return this.selectNodes.find((el) => el.shape === 'rectDragNode')
    },
    isDeviceNode() {
      let hasNode = this.selectNodes.find((el) => el.data.nodeType !== 'device')
      return hasNode === undefined
    },
    isVirtual() {
      return this.curCel.data.isVirtual
    },
    showIcon() {
      return ['device', 'topo'].includes(this.nodeType)
    },
    showNodeSize() {
      if (this.size === 1) {
        return true
      } else {
        let hasNode = this.selectNodes.find((el) => el.data.nodeType !== 'device')
        return hasNode === undefined
      }
    },
    showRadius() {
      return this.showNodeSize && this.curCel.shape !== 'circle-node'
    },
    showLabelPos() {
      let hasNode = this.selectNodes.find((el) => el.shape === 'networkGroupNode' || el.shape === 'text-block' || el.shape === 'rectDragNode')
      return hasNode === undefined
    },
    groupSizeDisabled() {
      if (this.curCel && this.cellShape === 'networkGroupNode' && this.curCel.data.collapsed) {
        return true
      }
      return false
    }
  },
  watch: {
    size: {
      immediate: true,
      handler() {
        const { graph } = FlowGraph
        this.selectNodes = graph.getSelectedCells()
        if (this.size > 0) {
          if (this.curCel) {
            this.curCel.off('change:position')
          }
          this.curCel = this.selectNodes[this.size - 1]
          this.cellShape = this.curCel.shape

          this.nodeType = this.curCel.data.nodeType
          // console.log('选中的节点', this.cellShape, this.curCel);
          this.activeKey = '1'
          this.initNodeConfig()
          //监听选中节点位置移动
          this.curCel.on('change:position', (e) => {
            Object.assign(this.nodeConfig.position, this.curCel.position())
          })
        }
      }
    }
  },
  beforeDestroy() {
    if (this.curCel) {
      this.curCel.off('change:position')
    }
    this.curCel = null
    this.cellShape = ''
  },
  methods: {
    /**
     * 选择文件格式是否正确
     */
    beforeUploadFun(file) {
      let result = checkAccept(file, this.acceptTips,false)
      if (!result) {
        return result
      }
      return checkBeforeUpload(file, true, 10, 'MB',false)
    },
    /*文件发生改变后，附加处理方法，一次性上传多个文件，剔除列表中不满足条件的文件*/
    handleChangeAdditionalFun(fileList){
      let list = fileList
      if (fileList.length > 0) {
        list = fileList.filter((item) => {
          return compareFileSizes(true, 10, item.size, 'MB')
        }).filter((item) => {
          return checkAccept(item, this.acceptTips)
        })
      }
      return list
    },
    //绑定设备
    bindDevice(e) {
      this.curCel.data.deviceCode = e
      this.curCel.deviceId = ''
      this.curCel.deviceName = ''
      this.$emit("bindDevice",this.curCel)
      // console.log('给节点绑定设备了 === ', this.curCel.data, e)
    },
    //初始化节点配置内容
    initNodeConfig() {
      if (this.size === 1 && this.curCel.data.nodeType === 'dept') {
        this.bindDeviceCode = this.curCel.data.deviceCode
        getAction('/monitor/situation/getAvailableDevForDeptTop', { deptId: this.curCel.data.deptId }).then(res => {
          if (res.success) {
            this.availableDevices = res.result
          }
        })
      }
      if (this.cellShape === 'switch-node') {
        this.nodeConfig.shapeType = 0
      } else if (this.cellShape === 'circle-node') {
        this.nodeConfig.shapeType = 1
      }
      if (this.cellShape === 'text-block') {
        // 文字节点 内容 字体样式
        this.nodeConfig.label = this.curCel.attr('label/text')
        this.nodeConfig.labelSize = this.curCel.attr('label/style/fontSize')
        this.nodeConfig.labelColor = this.curCel.attr('label/style/color')
        this.nodeConfig.textNodeBorder = this.curCel.attrs.body.stroke === 'none'
      } else {
        // 文本样式
        this.nodeConfig.label = this.curCel.attr('label/text')
        this.nodeConfig.labelSize = this.curCel.attr('label/fontSize')
        this.nodeConfig.labelColor = this.curCel.attr('label/fill')
      }
      //群组节点按钮显示
      if (this.cellShape === 'networkGroupNode') {
        this.nodeConfig.groupButtonHide =
          this.curCel.attr('button/stroke') === 'none' &&
          this.curCel.attr('button/fill') === 'none' &&
          this.curCel.attr('buttonSign/stroke') === 'none'
      }
      //矩形装饰节点
      if (this.showNodeStyle) {
        this.nodeConfig.stroke = this.curCel.attr('body/stroke')
        this.nodeConfig.strokeDasharray = this.curCel.attr('body/strokeDasharray') || '0'
        this.nodeConfig.nodeBgColor = this.curCel.attr('body/fill')
      }
      //节点位置
      Object.assign(this.nodeConfig.position, this.curCel.position())
      //节点大小
      Object.assign(this.nodeConfig.size, this.curCel.size())
      if (this.showLabelPos) {
        let refX = this.curCel.attr('label/refX')
        let refY = this.curCel.attr('label/refY')
        this.nodeConfig.labelPos = this.posArr.findIndex((el) => el.refx === refX && el.refy === refY)
      }
      if (this.showIcon) {
        // console.log('设备节点', this.curCel)
        this.nodeImg = this.curCel.attr('image/xlink:href')
        if (this.curCel.getData().deviceCode) {
          getAction('/topo/device/info', { deviceCode: this.curCel.getData().deviceCode }).then((res) => {
            if (res.success) {
              // console.log('节点图片', this.curCel)
              this.imgList = res.result.icons
              this.nodeIp = res.result.ip
            }
          })
        } else if (this.curCel.getData().isVirtual && this.curCel.getData().productId) {
          //虚拟节点获取产品类的图标
          getAction('/product/product/list', {
            displayName: this.curCel.getData().productName,
            column: 'createTime',
            order: 'desc',
            field: 'id',
            pageNo: 1,
            pageSize: 8
          }).then((res) => {
            if (res.success) {
              this.virtualData = res.result.records.find((el) => el.id === this.curCel.getData().productId)
              let icons = this.virtualData.icon.split(',')
              this.imgList = icons
            }
          })
        }
      }
      if (this.showRadius) {
        this.nodeConfig.radius.rx = this.curCel.attr('body/rx')
        this.nodeConfig.radius.ry = this.curCel.attr('body/ry')
      }
      // 节点的节点分值
      this.nodeScore = this.curCel.data.nodeScore || 0
      this.deviceType = this.curCel.data.deviceType || undefined
    },
    // 获取节点设备信息
    // getDeviceInfo() {
    //   getAction("/device/deviceInfo/queryById", {
    //     code: this.curCel.data.deviceCode,
    //   }).then((res) => {
    //     if (res.success&&res.result&&res.result.records&&res.result.records.length>0) {
    //       console.log('获取节点设备信息 === ', res)
    //     }
    //   })
    // },
    //监听节点分值
    onNodeWeightChange() {
      this.curCel.data.nodeScore = this.nodeScore
    },
    //选择设备类型
    bindDeviceType() {
      this.curCel.data.deviceType = this.deviceType
    },
    //监听节点内容变化
    onLabelChange(e) {
      const val = e.target.value
      this.nodeConfig.label = val
      this.curCel.attr('label/text', val)
      // if(this.curCel.data.isVirtual){
      //   this.curCel.data.deviceName =val
      // }
    },
    // 矩形装饰节点边框样式变化
    onBorderDashChange(val) {
      this.selectNodes.forEach((el) => {
        if (el.shape === 'rectDragNode' || el.shape === 'networkGroupNode') {
          el.attr('body/strokeDasharray', val)
        }
      })
    },
    // 矩形装饰节点边框颜色变化
    onBorderColorChange(e) {
      let val = e
      this.selectNodes.forEach((el) => {
        if (el.shape === 'rectDragNode' || el.shape === 'networkGroupNode') {
          el.attr('body/stroke', val)
        }

      })
    },
    onNodeBgColorChange(e) {
      let val = e
      this.selectNodes.forEach((el) => {
        if (el.shape === 'rectDragNode' || el.shape === 'networkGroupNode') {
          el.attr('body/fill', val)
        }
      })
    },
    //文字大小变化
    onNodeFontSizeChange(val) {
      this.selectNodes.forEach((el) => {
        if (el.shape === 'text-block') {
          el.attr('label/style/fontSize', val)
        } else {
          el.attr('label/fontSize', val)
        }
      })
    },
    //文字颜色变化
    onLabelColorChange(e) {
      let val = e
      this.selectNodes.forEach((el) => {
        if (el.shape === 'text-block') {
          el.attr('label/style/color', val)
        } else {
          el.attr('label/fill', val)
        }
      })
    },
    // 文字位置变化
    onNodeLablePos(e) {
      let posArr = this.posArr[e]
      this.selectNodes.forEach((el) => {
        el.attr('label/refX', posArr.refx)
        el.attr('label/refY', posArr.refy)
        el.attr('label/textAnchor', posArr.anchor)
        el.attr('label/textVerticalAnchor', posArr.vAnchor)
      })
    },
    //节点位置变化
    onPositionChange(e, str) {
      const val = parseInt(e.target.value)
      let pos = this.nodeConfig.position
      this.curCel.position(Number(pos.x), Number(pos.y))
    },
    // 节点大小变化
    onSizeChange(e, str) {
      const val = parseInt(e)
      let size = this.nodeConfig.size
      this.selectNodes.forEach((el) => {
        if (el.shape === 'networkGroupNode') {
          el.setData({ nodeWidth: Number(size.width) })
          el.setData({ nodeHeight: Number(size.height) })
        }
        el.resize(Number(size.width), Number(size.height))
      })
    },
    // 切换节点样式
    onShapeTypeChange(e) {
      const { graph } = FlowGraph
      this.nodeConfig.shapeType = e
      let attr = this.curCel.getAttrs()
      let shape = this.globalGridAttr.shapeNames[e]
      let node = graph.addNode(graph.createNode(
        {
          shape: shape,
          visible: true,
          attrs: attr,
          data: this.curCel.getData(),
          position: this.curCel.position(),
          size: this.curCel.size(),
          angle: this.curCel.getAngle()
        }))
      let outEdges = graph.getOutgoingEdges(this.curCel)
      let inEdges = graph.getIncomingEdges(this.curCel)
      if (outEdges && outEdges.length > 0) {
        outEdges.forEach(el => {
          el.setSource(node)
        })
      }
      if (inEdges && inEdges.length > 0) {
        inEdges.forEach(el => {
          el.setTarget(node)
        })
      }

      graph.removeCell(this.curCel)
      graph.resetSelection(node)
    },
    onRadiusChange(e, str) {
      const val = parseInt(e)
      // console.log("圆角改变了",e)
      let size = this.nodeConfig.radius
      this.curCel.attr('body/' + str, val)
    },
    //隐藏群组按钮
    groupButtonChange(e) {
      if (this.globalGridAttr.groupButtonHide) {
        this.curCel.attr('button/stroke', 'none')
        this.curCel.attr('button/fill', 'none')
        this.curCel.attr('buttonSign/stroke', 'none')
      } else {
        this.curCel.attr('button/stroke', '#ccc')
        this.curCel.attr('button/fill', '#f5f5f5')
        this.curCel.attr('buttonSign/stroke', '#808080')
      }
    },
    //文字节点连线变化
    textBorderChange(e) {
      if (this.globalGridAttr.textNodeBorder) {
        this.curCel.attr('body/stroke', 'none')
      } else {
        this.curCel.attr('body/stroke', '#8c8c8c')
      }
    },
    //群组连线改变
    groupLineChange(e) {
      if (this.nodeConfig.groupLine === 'dashed') {
        this.curCel.attr('body/strokeDasharray', '10 4')
      } else {
        this.curCel.attr('body/strokeDasharray', '')
      }
    },
    //节点图标列表变化
    changeImgList(path, status) {
      this.imgList = path.split(',')
      let imgListLength = this.imgList.length
      let imgUrl = this.imgList[imgListLength - 1]
      this.nodeImg = window._CONFIG['staticDomainURL'] + '/' + imgUrl
      if (imgUrl.startsWith('http://') || imgUrl.startsWith('https://')) {
        this.nodeImg = imgUrl
      }
      this.curCel.attr('image/xlink:href', this.nodeImg)
      if (status === 'removed') {
        this.updateIcon(path)
      } else {
        this.addIcon(imgUrl, path)
      }
    },
    //选中图标
    changeImg(fileUrl) {
      if (fileUrl.startsWith('http://') || fileUrl.startsWith('https://')) {
        this.nodeImg = fileUrl
        this.curCel.attr('image/xlink:href', fileUrl)
      } else {
        this.nodeImg = window._CONFIG['staticDomainURL'] + '/' + fileUrl
        this.curCel.attr('image/xlink:href', window._CONFIG['staticDomainURL'] + '/' + fileUrl)
      }

    },
    addIcon(fileUrl, path) {
      if (this.curCel && this.curCel.getData().deviceId) {
        httpAction(this.iconAdd, { deviceId: this.curCel.getData().deviceId, icon: fileUrl }, 'post').then((res) => {
          if (res.result) {
          }
        })
      } else if (this.curCel.getData().isVirtual && this.curCel.getData().productId) {
        if (this.virtualData) {
          this.virtualData.icon = path
        }
        this.updateProductIcons()

      }
    },
    updateIcon(fileUrl) {
      if (this.curCel.getData().isVirtual && this.curCel.getData().productId) {
        this.virtualData.icon = fileUrl
        this.updateProductIcons()
      } else {
        httpAction(
          this.iconUpdate,
          { deviceId: this.curCel ? this.curCel.getData().deviceId : '', icon: fileUrl },
          'post'
        ).then((res) => {
          if (res.result) {
          }
        })
      }
    },
    // 虚拟设备 更新产品类的图标
    updateProductIcons() {
      httpAction('/product/product/edit', this.virtualData, 'put').then((res) => {
        if (res.success) {
          console.log('产品更新成功 === ', res)
        }
      })
    }
  }
}
</script>

<style lang='less' scoped>
.ant-tabs {
  color: rgba(0, 0, 0, 0.65);
  height: 100%;
}

.ant-tabs .ant-tabs-top-content,
.ant-tabs .ant-tabs-bottom-content {
  width: 100%;
  height: calc(100% - 64px);
}

.ant-tabs .ant-tabs-top-content > .ant-tabs-tabpane,
.ant-tabs .ant-tabs-bottom-content > .ant-tabs-tabpane {
  height: 100% !important;
}

.tab-con {
  height: 80vh;
  width: 100%;
  overflow-y: scroll;
}

.img-list {
  // height:380px;
  // overflow-y: auto;
  display: flex;
  justify-content: center;
}

::v-deep .ant-upload-list-item-info > span {
  display: flex;
  justify-content: center;
}

::v-deep .ant-upload-list-picture-card .ant-upload-list-item-thumbnail,
::v-deep .ant-upload-list-picture-card .ant-upload-list-item-thumbnail img {
  position: relative;
  display: flex;
  width: 90%;
  height: auto;
  left: 0px;
  top: 0px !important;
  align-items: center;
  justify-content: center;
}

.port-item {
  margin-bottom: 5px;
  display: flex;
  justify-content: space-around;

  span {
    width: 160px;
  }
}
</style>
