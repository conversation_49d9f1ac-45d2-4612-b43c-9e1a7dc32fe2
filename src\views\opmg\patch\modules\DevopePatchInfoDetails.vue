<template>
  <a-card style="height: 100%; overflow: hidden">
    <a-row>
      <a-col :span="24">
        <span style="margin-left: 10px; color: #409eff; cursor: pointer" @click="handleDetailEdit(data)">
          <a-icon type="edit" style="margin-right: 6px" />编辑
        </span>
        <span style="float: right; margin-bottom: 12px"
          ><img src="~@/assets/return1.png" alt="" @click="getGo" style="width: 20px; height: 20px; cursor: pointer"
        /></span>
      </a-col>
      <a-col :span="24" style="overflow-x: auto">
        <table class="gridtable">
          <tr>
            <td class="leftTd">软件名称</td>
            <td class="rightTd">{{ data.patchName }}</td>
            <td class="leftTd">补丁版本</td>
            <td class="rightTd">{{ data.patchVersion }}</td>
          </tr>
          <tr>
            <td class="leftTd">操作系统</td>
            <td class="rightTd">{{ data.patchOsText }}</td>
            <td class="leftTd">架构</td>
            <td class="rightTd">{{ data.frawork }}</td>
          </tr>
          <tr>
            <td class="leftTd">补丁文件</td>
            <td class="rightTd">{{ data.patchFileNameText }}</td>
            <td class="leftTd">脚本文件</td>
            <td class="rightTd">{{ data.scriptFileNameText }}</td>
          </tr>
          <tr>
            <td class="leftTd">是否有效</td>
            <td class="rightTd">{{ data.effect == '0' ? '无效' : data.effect == '1' ? '有效' : '' }}</td>
            <td class="leftTd">设备类型</td>
            <td  class="rightTd">{{ data.resourceTypeText }}</td>
          </tr>
          <tr>
            <td class="leftTd">描述</td>
            <td colspan='3' class="rightTd">{{ data.patchDescribe }}</td>
          </tr>
        </table>
      </a-col>
    </a-row>
    <devope-patch-info-modal ref="modalForm" @ok="modalFormOk"></devope-patch-info-modal>
  </a-card>
</template>

<script>
import DevopePatchInfoModal from './DevopePatchInfoModal'
import { httpAction, getAction } from '@/api/manage'
export default {
  name: 'DevopePatchInfoDetails',
  props: {
    data: {
      type: Object,
    },
  },
  components: {
    DevopePatchInfoModal,
  },
  data() {
    return {
      form: this.$form.createForm(this),
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      url: {
        queryById: '/patch/devopePatchInfo/queryById',
      },
    }
  },
  mounted() {},
  methods: {
    modalFormOk() {
      let params = { id: this.data.id }
      getAction(this.url.queryById, params).then((res) => {
        if (res.success) {
          this.data = res.result
        }
      })
    },
    //详情编辑
    handleDetailEdit: function (record) {
      this.$refs.modalForm.edit(record)
      this.$refs.modalForm.title = '编辑'
      this.$refs.modalForm.disableSubmit = false
    },
    //返回上一级
    getGo() {
      this.$parent.pButton2(0)
    },
  },
}
</script>
<style lang="less" scoped>
::v-deep .two-words > div > label {
  letter-spacing: 4px;
}
::v-deep .two-words > div > label::after {
  letter-spacing: 0px;
}
table.gridtable {
  font-family: verdana, arial, sans-serif;
  font-size: 14px;
  color: #606266;
  border-width: 1px;
  border-color: #e8e8e8;
  border-collapse: collapse;
  text-align: left;
  width: 100%;
}
table.gridtable td {
  border-width: 1px;
  border-style: solid;
  border-color: #e8e8e8;
  white-space: nowrap;
}
.leftTd {
  width: 17%;
  background-color: #fafafa;
  padding: 16px 24px;
  text-align: center;
}
.rightTd {
  width: 35%;
  padding: 16px 24px;
}
</style>