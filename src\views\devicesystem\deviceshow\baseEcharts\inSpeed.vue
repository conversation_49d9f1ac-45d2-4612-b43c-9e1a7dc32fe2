<template>
  <!-- 上下行速率 -->
  <div ref="baseEcharts" style="height: 100%;width: 100%;"></div>
</template>

<script>
import echarts from 'echarts'
import { chartMixins } from './chartMixins'
export default {
  name: 'inSpeed',
  mixins: [chartMixins],
  props: {
    chartData: {
      type: Object,
      default: () => {}
    },
    fontSizeObject: {
      type: Object,
        default: function () {
        return {
          legendFontSize: 8,
          xAxisFontSize: 8,
          yAxisFontSize: 10
        }
      }
    }
  },
  watch: {
    chartData: {
      handler(nVal, oVal) {
        this.$nextTick(() => {
          this.initData(nVal)
        })
      },
      deep: true,
      immediate: true
    }
  },
  data() {
    return {
      myChart: null
    }
  },
  methods: {
    initData(obj) {
      let xData = []
      let xAllData = []
      let yData = []
      let yAllData = [] // 30日上行速率
      let yData2 = []
      let yAllData2 = [] // 30日下行速率
      let unit = '' // 获取单位
      if (obj.outSpeed && obj.outSpeed.length > 0) {
        // 下行速率
        xAllData = obj.outSpeed.map(item => item.time) // 处理x轴数据
        yAllData2 = obj.outSpeed.map(item => item.value) // 处理y轴数据
        // 获取单位
        let result = obj.outSpeed.find(item=>item.value.unit)
        if (result && result.value && result.value.value) {
          unit = result.value.unit
        }
      }
      if (obj.inSpeed && obj.inSpeed.length > 0) {
        // 上行速率
        xAllData = obj.inSpeed.map(item => item.time) // 处理x轴数据
        yAllData = obj.inSpeed.map(item => item.value) // 处理y轴数据
        // 获取单位
        let result = obj.inSpeed.find(item=>item.value.unit)
        if (result && result.value && result.value.value) {
          unit = result.value.unit
        }
      }

      if (xAllData.length > 6) {
        // 截取近七日数据
        xData = xAllData.slice(xAllData.length - 7, xAllData.length)
        yData = yAllData.slice(xAllData.length - 7, xAllData.length)
        yData2 = yAllData2.slice(xAllData.length - 7, xAllData.length)
      } else {
        // 全部数据
        xData = xAllData
        yData = yAllData
        yData2 = yAllData2
      }

      this.myChart = this.$echarts.init(this.$refs.baseEcharts)
      this.myChart.clear()
      let option = {
        grid: {
          top: '18%',
          left: '6%',
          right: '7%',
          bottom: 4,
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            let html = `${params[0].name}<br/>`
            params.map((item, i) => {
              if (item.value !== '' && item.value !== null && item.value !== undefined) {
                html += `${item.marker}${item.seriesName}：${item.value} ${item.data.unit || ''} <br/>`
              } else {
                html += `${item.marker}${item.seriesName}：无数据<br/>`
              }
            })
            return html
          }
        },
        legend: {
          top: '2%',
          left: '7%',
          icon: 'rect',
          itemWidth: 14,
          itemHeight: 3,
          textStyle: {
            fontSize: this.fontSizeObject.legendFontSize
          }
        },
        xAxis: {
          type: 'category',
          boundaryGap: true,
          axisLine: {
            show: true,
            lineStyle: {
              color: 'rgba(0,0,0,0.15)'
            }
          },
          axisLabel: {
            textStyle: {
              color: 'rgba(0,0,0,0.45)',
              fontSize: this.fontSizeObject.xAxisFontSize
            }
          },
          axisTick: {
            show: false
          },
          data: xData
        },
        yAxis: {
          type: 'value',
          axisTick: {
            show: false
          },
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed',
              color: 'rgba(0,0,0,0.15)',
              width: 1
            }
          },
          axisLine: {
            show: false
          },
          axisLabel: {
            formatter: '{value}' + unit,
            textStyle: {
              color: 'rgba(0,0,0,0.45)',
              fontSize: this.fontSizeObject.yAxisFontSize
            }
          }
        },
        dataZoom: [
          {
            xAxisIndex: [0],
            show: false, //是否显示滑动条，不影响使用
            start: 0, // 从头开始。
            endValue: 30,
            realtime: true, //是否实时更新
          },
          {
            type: 'inside',
            xAxisIndex: 0,
            zoomOnMouseWheel: false, //滚轮是否触发缩放
            moveOnMouseMove: true, //鼠标滚轮触发滚动
            moveOnMouseWheel: true
          }
        ],
        series: [
          {
            data: yData,
            name: '端口上行速率',
            type: 'line',
            symbol: 'circle',
            showSymbol: false,
            emphasis: {
              focus: 'series' //高亮显示
            },
            symbolSize: 4,
            lineStyle: {
              color: 'rgba(62,136,249,1)'
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: 'rgba(62,136,249,0.3)'
                },
                {
                  offset: 1,
                  color: 'rgba(62,136,249,0.3)'
                }
              ])
            },
            itemStyle: {
              // 折线拐点标志的样式
              normal: {
                color: '#3E88F9',
                // borderColor: 'rgba(255, 234, 0, 0.5)',
                borderWidth: 1
              }
            }
          },
          {
            data: yData2,
            name: '端口下行速率',
            type: 'line',
            symbol: 'circle',
            emphasis: {
              focus: 'series' //高亮显示
            },
            showSymbol: false,
            symbolSize: 4,
            lineStyle: {
              color: 'rgba(255,163,47,1)'
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: 'rgba(255,163,47,0.2)'
                },
                {
                  offset: 1,
                  color: 'rgba(255,163,47,0.2)'
                }
              ])
            },
            itemStyle: {
              // 折线拐点标志的样式
              normal: {
                color: '#FFA32F',
                // borderColor: 'rgba(255, 234, 0, 0.5)',
                borderWidth: 1
              }
            }
          }
        ]
      }

      this.myChart.setOption(option)
    }
  }
}
</script>