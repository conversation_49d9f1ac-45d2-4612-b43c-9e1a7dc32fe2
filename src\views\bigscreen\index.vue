<template>
  <div class="body">
    <div class="left">
      <div class="left-top">
        <bigscreen-map></bigscreen-map>
<!--        <div class="left-top-core" id="echartsMap"></div>-->
      </div>
      <div class="left-bottom">
        <div class="topTitle">
          <img src="@/assets/bigScreen/9.png" alt="" />
          <span style="cursor: pointer" @click="jumpRouter(routerInfoList[0])">告警轮播</span>
        </div>
        <div class="left-bottom-core">
          <div class="left-bottom-tableTitle">
            <span>告警时间</span>
            <span>设备名称</span>
            <span>告警级别</span>
            <span>告警名称</span>
          </div>
          <div class="left-bottom-table" @mousewheel="handleMouseWheel">
            <vue-seamless-scroll :data="warningData" :class-option="warning" ref="seamlessDiv" class="seamless-warp">
              <div v-for="item in warningData" :key="item.id">
                <span :title="item.createTime"> {{ item.createTime }} </span>
                <span :title="item.deviceName"> {{ item.deviceName }} </span>
                <span :title="item.alarmLevel"> {{ item.alarmLevel }} </span>
                <span :title="item.templateName"> {{ item.templateName }}</span>
              </div>
            </vue-seamless-scroll>
          </div>
        </div>
      </div>
    </div>
    <div class="core">
      <div class="core-top">
        <div class="topTitle">
          <img src="@/assets/bigScreen/9.png" alt="" />
          <span style="cursor: pointer" @click="jumpRouter(routerInfoList[1])">设备总览</span>
        </div>
        <div v-if="resourcesData != '暂无信息'" class="core-top-body">
          <!--         原来样式 <div class="core-top-body-patch">，下面是零时添加的“一期2300”-->
          <div
            :class="{
              'core-top-body-patch': resourcesData.length <= 8,
              'core-top-body-patch1': resourcesData.length > 8,
            }"
          >
            <div class="core-top-body-item" v-for="(item, index) in resourcesData" :key="index">
              <span>{{ item.name }}</span>
              <animate-number from="0" :to="item.value" :key="item.value" duration="2000"></animate-number>
            </div>
          </div>
        </div>
        <div v-else class="no-content">{{ resourcesData }}！</div>
      </div>
      <div class="core-core">
        <div class="topTitle">
          <img src="@/assets/bigScreen/9.png" alt="" />
          <span style="cursor: pointer" @click="jumpRouter(routerInfoList[1])">设备状态监控</span>
        </div>
        <div class="core-core-body">
          <div class="core-core-body-circularGraph" id="equipmentCircularGraph"></div>
          <!-- 可用size=40%,70%调整饼图尺寸 饼图则调整为 0%，70%，环形图则根据大小调整 rose控制是否为玫瑰图 -->
          <!-- <chart-ring url='/data-analysis/device/status' size="40%,70%" :rose="true"></chart-ring> -->
        </div>
      </div>
      <div class="core-bottom">
        <div class="topTitle">
          <img src="@/assets/bigScreen/9.png" alt="" />
          <span>设备告警量TOP5</span>
        </div>
        <div class="core-bottom-body">
          <div class="core-bottom-body-left" id="resourcesWarningHistogram"></div>
        </div>
      </div>
    </div>
    <div class="right">
      <div class="right-top">
        <div class="topTitle">
          <img src="@/assets/bigScreen/9.png" alt="" />
          <span>工单统计</span>
        </div>
        <div class="right-top-body">
          <div class="right-top-body-top">
            <div class="right-top-body-top-item">
              <span class="name"> {{ workOrderDataOne.name }} </span>
              <div class="value">
                <animate-number
                  v-if="workOrderDataOne.value>=0"
                  from="0"
                  :to="workOrderDataOne.value"
                  :key="workOrderDataOne.value"
                  duration="5000"
                ></animate-number>
                <span> {{ workOrderDataOne.unit }} </span>
              </div>
            </div>
            <div class="right-top-body-top-item">
              <span class="name"> {{ workOrderDataTwo.name }} </span>
              <div class="value">
                <animate-number
                  v-if="workOrderDataTwo.value >= 0"
                  from="0"
                  :to="workOrderDataTwo.value"
                  :key="workOrderDataTwo.value"
                  duration="5000"
                ></animate-number>
                <span> {{ workOrderDataTwo.unit }} </span>
              </div>
            </div>
            <div class="right-top-body-top-item">
              <span class="name"> {{ workOrderDataThree.name }} </span>
              <div class="value">
                <animate-number
                  v-if="workOrderDataThree.value >= 0"
                  from="0"
                  :to="workOrderDataThree.value"
                  :key="workOrderDataThree.value"
                  duration="5000"
                  :formatter="formatter"
                ></animate-number>
                <span> {{ workOrderDataThree.unit }} </span>
              </div>
            </div>
          </div>
          <div class="right-top-body-bottom">
            <div class="right-top-body-bottom-left">
              <a-progress
                type="circle"
                :stroke-color="{
                  '0%': '#080aff',
                  '100%': '#56e3fe',
                }"
                :width="50"
                :percent="percent1.value"
              >
                <template #format="percent">
                  <span style="color: #50d2e8; font-size: 0.175rem">{{ percent1.value }}%</span>
                </template>
              </a-progress>
              <span> {{ percent1.name }}</span>
              <!-- <div class="right-top-body-bottom-left-one">
                <div class="liquidfillOne" id="liquidfillOne" ref="liquidfillOne"></div>
              </div> -->
            </div>
            <div class="right-top-body-bottom-core">
              <a-progress
                type="circle"
                :stroke-color="{
                  '0%': '#080aff',
                  '100%': '#56e3fe',
                }"
                :width="50"
                :percent="percent2.value"
              >
                <template #format="percent">
                  <span style="color: #50d2e8; font-size: 0.175rem">{{ percent2.value }}%</span>
                </template>
              </a-progress>
              <span> {{ percent2.name }} </span>
            </div>
            <div class="right-top-body-bottom-right">
              <a-progress
                type="circle"
                :stroke-color="{
                  '0%': '#080aff',
                  '100%': '#56e3fe',
                }"
                :width="50"
                :percent="percent3.value"
              >
                <template #format="percent">
                  <span style="color: #50d2e8; font-size: 0.175rem">{{ percent3.value }}%</span>
                </template>
              </a-progress>
              <span> {{ percent3.name }} </span>
            </div>
          </div>
        </div>
      </div>
      <div class="right-core">
        <div class="topTitle">
          <img src="@/assets/bigScreen/9.png" alt="" />
          <span style="cursor: pointer" @click="jumpRouter(routerInfoList[3])">资产统计</span>
        </div>
        <div class="right-core-body" id="assetStatisticsHistogram"></div>
        <!-- 通过barGap来选择合并柱状图（-100%）还是经典柱状图（0%） -->
        <!-- <chart-column url="/data-analysis/cmdb/count" barGap="-100%"></chart-column> -->
      </div>
      <div class="right-bottom">
        <div class="topTitle">
          <img src="@/assets/bigScreen/9.png" alt="" />
          <span>服务器CPU使用率TOP5</span>
        </div>
        <div class="right-bottom-body">
          <div class="right-bottom-body-left" id="theServerHistogram" v-if="cpuTopData != []"></div>
          <img src="@/assets/nodata.png" v-else alt="" />
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import echarts from 'echarts'
// import chartRing from '@/components/bigScreen/chartRing'
// import chartColumn from '@/components/bigScreen/chartColumn'
// import 'echarts/lib/component/graphic'
import { mapState, mapActions } from 'vuex'
import vueSeamlessScroll from 'vue-seamless-scroll'
import { deleteAction, getAction, putAction, httpAction } from '@/api/manage'
import Vue from 'vue'
import { PLATFORM_TYPE, RESET_MENUS, SYS_BUTTON_AUTH, USER_AUTH } from '@/store/mutation-types'
import store from '@/store'
import router from '@/router'
import { generateBigscreenRouter, generateIndexRouter, triggerWindowResizeEvent } from '@/utils/util'
import BigscreenMap from '@views/bigscreen/echartsModule/BigscreenMap.vue'

// 地图数据
const mapJson = window.echarts
echarts.registerMap('citymap', mapJson)
let map_terminals = {}
// window.dict.features[0].value

export default {
  data() {
    return {
      warningData: [],
      resourcesData: [],
      resourcesWarning: [],
      workOrderData: [],
      workOrderDataOne: {},
      workOrderDataTwo: {},
      workOrderDataThree: {},
      url: {
        alarmList: '/data-analysis/device/alarm/list',
        tagsOverView: '/data-analysis/device/tagsOverView',
        overview: '/data-analysis/device/overview',
        alarmTop: '/data-analysis/device/alarm/top',
        status: '/data-analysis/device/status',
        orderCount: '/data-analysis/order/count',
        cmdbCount: '/data-analysis/cmdb/count',
        cpuTop: '/data-analysis/device/cpu/top',
      },
      theServerData: [],
      percent1: {},
      percent2: {},
      percent3: {},
      cpuTopData: [],
      simpleModel: !window._CONFIG['simpleModel'],
      routerInfoList: [
        {
          actionMenu: ['告警轮播'],
          targetMemuName: '设备告警',
          routerName: 'alarmManage-assetsAlarm-AssetsAlarmManage',
          redirect: '/alarmManage/assetsAlarm/AssetsAlarmManage',
          component: 'alarmManage/assetsAlarm/AssetsAlarmManage',
          params: null,
        },
        {
          actionMenu: ['设备总览', '设备状态监控'],
          targetMemuName: '设备信息',
          routerName: 'devicesystem-DeviceInfoMange',
          redirect: '/devicesystem/DeviceInfoMange',
          component: 'devicesystem/DeviceInfoMange',
          params: null,
        },
        {
          actionMenu: ['设备告警量TOP5'],
          targetMemuName: '设备信息/查看/设备告警',
          routerName: 'devicesystem-DeviceInfoMange',
          redirect: '/devicesystem/DeviceInfoMange',
          component: 'devicesystem/DeviceInfoMange',
          params: {
            deviceName: '',
            tabKey: 7,
          }
        },
        {
          actionMenu: ['资产统计'],
          targetMemuName: '资产信息',
          routerName: 'assets-AssetsManagement',
          redirect: '/assets/AssetsManagement',
          component: 'cmdb/assets/AssetsManagement',
          params: null,
        },
        {
          actionMenu: ['服务器CPU使用率TOP5'],
          targetMemuName: '设备信息',
          routerName: 'devicesystem-DeviceInfoMange',
          redirect: '/devicesystem/DeviceInfoMange',
          component: 'devicesystem/DeviceInfoMange',
          params: {
            deviceName: '',
            tabKey: 2,
          },
        },
      ],
      terminals: {},
    }
  },
  components: {
    BigscreenMap,
    vueSeamlessScroll,
    // chartRing,
    // chartColumn
  },
  created() {
   /* getAction(document.location.origin + '/static/static-echarts/terminal.json').then((res) => {
      console.log("获取到的j'son === ", res)
      this.terminals = res
      map_terminals = res
    })*/
  },
  mounted() {
    this.$nextTick(() => {
      this.cpuTop()
      // this.echart_map()
      this.alarmList()
      this.overview()
      this.status()
      this.alarmTop()
      this.orderCount()
      this.cmdbCount()
    })
  },
  computed: {
    warning() {
      return {
        step: this.simpleModel ? 0.1 : 0, // 数值越大速度滚动越快
        limitMoveNum: 4, // 开始无缝滚动的数据量 this.dataList.length
        hoverStop: true, // 是否开启鼠标悬停stop
        direction: 1, // 0向下 1向上 2向左 3向右
        // openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 86, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
        // singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
        waitTime: 2, // 单步运动停止的时间(默认值1000ms)
      }
    },
  },
  methods: {
    handleMouseWheel(e) {
      if (Math.abs(this.$refs.seamlessDiv.yPos) < this.$refs.seamlessDiv.realBoxHeight / 2 || e.deltaY < 0) {
        this.$refs.seamlessDiv.yPos -= e.deltaY
        this.$refs.seamlessDiv.yPos = this.$refs.seamlessDiv.yPos > 0 ? 0 : this.$refs.seamlessDiv.yPos
      }
    },
    ...mapActions(['GetMenuPermissions']),
    jumpRouter(routerInfo) {
      let that = this
      let params = {
        url: routerInfo.redirect,
        component: routerInfo.component,
      }
      that
        .GetMenuPermissions(params)
        .then((res) => {
          if (res.success) {
            sessionStorage.setItem(PLATFORM_TYPE, res.platformType)
            const menuData = res.menu
            let constRoutes = []
            if (res.platformType === 4|| res.platformType === 8) {
              constRoutes = generateBigscreenRouter(menuData)
            } else {
              constRoutes = generateIndexRouter(menuData)
            }
            // 添加主界面路由
            store
              .dispatch('UpdateAppRouter', {
                constRoutes,
              })
              .then(() => {
                // 根据roles权限生成可访问的路由表
                // 动态添加可访问路由表
                router.addRoutes(store.getters.addRouters)
                that.$store.commit(RESET_MENUS,true)
                that.$router.push({
                  name: routerInfo.routerName,
                  path: routerInfo.redirect,
                  params: routerInfo.params,
                })
              })
          } else {
            alert(res.message)
          }
        })
        .catch((err) => {
          alert(err.message)
        })
    },
    // 地图
    echart_map() {
      let vm = this
      let myChart = this.$echarts.init(document.getElementById('echartsMap'))
      //城市经纬度数据
      var geoCoordMap = window.longitudeLatitude
      //城市流线数据
      var GZData = window.city
      //获取连线数据
      var convertData = function (data) {
        var res = []
        for (var i = 0; i < data.length; i++) {
          var dataItem = data[i]
          var fromCoord = geoCoordMap[dataItem[0].name] //起点经纬度
          var toCoord = geoCoordMap[dataItem[1].name] //终点经纬度
          if (fromCoord && toCoord) {
            res.push({
              fromName: dataItem[0].name, //起点名称
              toName: dataItem[1].name, //终点名称
              coords: [fromCoord, toCoord], //[起点经纬度,终点经纬度]
            })
          }
        }
        return res
      }
      //获取点数据
      var scatterData = function (data) {
        var res = []
        for (var item in data) {
          let params = {
            name: item,
            value: data[item],
          }
          res.push(params)
        }
        return res
      }
      var color = '#c5f80e'
      let option = {
        tooltip: {
          trigger: 'item',
          backgroundColor: 'rgba(255,255,255,0)',
          textStyle: {
            color: '#fff',
            decoration: 'none',
          },
          formatter: function (value) {
            // console.log('卡纳安 == ', value,map_terminals)
            let terminalInfo = map_terminals[value.name] || { all: 0, online: 0, offline: 0, rate: '0' }
            if (value.componentSubType === 'effectScatter' || value.componentSubType === 'map') {
              return ` <div class="yq-map-tip">
      <div class="tip-title">${value.name}</div>
      <div class="tip-con">
        <div class="tip-item">
          <div class="title">
            <span>终端总数：</span>
          </div>
          <div class="value">${terminalInfo.all}</div>
        </div>

      </div>
    </div>`
    /*
     <div class="tip-item">
          <div class="title">
            <span>在线数：</span>
          </div>
          <div class="value">${terminalInfo.online}</div>
        </div>
        <div class="tip-item">
          <div class="title">
            <span>离线数：</span>
          </div>
          <div class="value">${terminalInfo.offline}</div>
        </div>
        <div class="tip-item">
          <div class="title">
            <span>在线率：</span>
          </div>
          <div class="value" style="color:#EFC855">${terminalInfo.rate}</div>
        </div>
     */
            }
          },
        },
        grid: {
          left: '0', // 与容器左侧的距离
          right: '0', // 与容器右侧的距离
          top: '0', // 与容器顶部的距离
          bottom: '0', // 与容器底部的距离
        },
        geo: {
          map: 'citymap',
          // aspectScale: 0.85,
          layoutCenter: ['50%', '50%'], //地图位置
          // layoutSize: '100%',
          animationDurationUpdate: 0,
          zoom: 1,
          scaleLimit: {
            min: 1,
            max: 18,
          },
          itemStyle: {
            normal: {
                shadowColor: '#276fce',
              shadowOffsetX: 0,
              shadowOffsetY: 0,
              opacity: 0.3,
            },
          },
        },
        series: [
          {
            type: 'map',
            mapType: 'citymap',
            // aspectScale: 0.85,
            layoutCenter: ['50%', '50%'], //地图位置
            // layoutSize: '100%',
            roam: true,
            zoom: 1, //当前视角的缩放比例
            // roam: true, //是否开启平游或缩放
            scaleLimit: {
              min: 1,
              max: 18,
            },
            itemStyle: {
              // 普通状态下的样式
              normal: {
                borderColor: '#07919e',
                areaColor: '#1c2f59',
              },
              // 高亮状态下的样式,默认黄色
              emphasis: {
                areaColor: '#1f2533',
              },
            },
            label: {
              normal: {
                //静态的时候展示样式
                show: false, //是否显示地图区的名称
                textStyle: {
                  color: '#39fdf4',
                },
              },
              emphasis: {
                //动态展示的样式
                show: false, //是否显示地图区的名称
                color: '#39fdf4',
              },
            },
            data: scatterData(geoCoordMap),
          },
          {
            type: 'effectScatter',
            coordinateSystem: 'geo',
            animationDurationUpdate: 0,
            symbolSize: 10,
            rippleEffect: {
              //坐标点动画
              period: 3,
              scale: 5,
              brushType: 'fill',
            },
            data: scatterData(geoCoordMap),
            symbolSize: function (val) {
              return 6
            },
            showEffectOn: 'render',
            rippleEffect: {
              brushType: 'stroke',
            },
            hoverAnimation: true,
            label: {
              normal: {
                show: true,
                position: 'right',
                formatter: '{b}',
              },
            },
            itemStyle: {
              normal: {
                color: color,
              },
            },
            zlevel: 1,
          },
          {
            name: 'cityMap',
            type: 'lines',
            zlevel: 2,
            symbol: ['none', 'none'], //['arrow','arrow]表示线两头用箭头显示
            symbolSize: 10, //箭头大小
            effect: {
              show: true,
              period: 6,
              trailLength: 0,
              symbol: 'arrow',
              symbolSize: 5,
            },
            label: {
              normal: {
                show: true,
                position: 'left',
                formatter: '{b}',
              },
            },

            lineStyle: {
              normal: {
                color: color,
                width: 1,
                opacity: 0.6,
                curveness: 0.2,
              },
            },
            data: convertData(GZData),
          },
        ],
      }
      myChart.setOption(option)
      window.addEventListener('resize', () => {
        myChart.resize()
      })
      //地理坐标系 geo 的缩放和平移漫游事件
      myChart.on('georoam', function (params) {
        var option = myChart.getOption() //获得option对象
        if (params.zoom != null && params.zoom != undefined) {
          //捕捉到缩放时
          option.geo[0].zoom = option.series[0].zoom
          option.geo[0].center = option.series[0].center
        } else {
          //捕捉到拖曳时
          option.geo[0].center = option.series[0].center
        }
        myChart.setOption(option) //设置option
      })
    },
    // 告警轮播数据
    alarmList() {
      getAction(this.url.alarmList).then((res) => {
        if (res.code == 200) {
          if (res.result != '暂无信息') {
            this.warningData = res.result
          }
        }
      })
    },
    // 设备总览数据
    overview() {
      getAction(this.url.overview).then((res) => {
        if (res.code == 200) {
          if (res.result.length <= 8) {
            this.resourcesData = res.result
          } else {
            this.resourcesData = res.result.slice(0, 8)
          }
          //榆林：零时添加
          // this.CZ_Data = window._CONFIG['customization'];
          // if (this.CZ_Data && this.CZ_Data.cz_yulin && this.CZ_Data.cz_yulin.tempData) {
          getAction(this.url.tagsOverView).then((res) => {
            for (let i = 0; i < res.result.length; i++) {
              this.resourcesData.splice(1, 0, res.result[i])
            }
          })
          // }
        }
      })
    },
    // 设备状态监控数据
    status() {
      getAction(this.url.status).then((res) => {
        if (res.success) {
          this.equipmentCircularGraph(res.result)
        }
      })
    },
    // 设备状态监控环形图
    equipmentCircularGraph(data) {
      for (let i = 0; i < data.length; i++) {
        data[i].value = data[i].values[0].value
      }

      let myChart = this.$echarts.init(document.getElementById('equipmentCircularGraph'))
      myChart.setOption({
        tooltip: {
          show: true,
          trigger: 'item',
          transitionDuration: 0, //echart防止tooltip的抖动
        },
        legend: {
          top: '34%',
          right: '2%',
          orient: 'vertical',
          textStyle: {
            color: '#fff',
          },
          formatter: function (name) {
            // 获取legend显示内容
            // let data = aaa
            let total = 0
            let tarValue = 0
            for (let i = 0, l = data.length; i < l; i++) {
              total += data[i].value
              if (data[i].name == name) {
                tarValue = data[i].value
              }
            }
            let p = ((tarValue / total) * 100).toFixed(2)
            return name + ' ' + ' ' + p + '%'
          },
        },
        color: ['#058eee', '#ffba13', '#dd3f2a'],
        series: [
          {
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            hoverAnimation: false,
            label: {
              show: false,
              position: 'center',
            },

            labelLine: {
              show: false,
            },
            center: ['40%', '50%'],
            data: data,
          },
        ],
      })
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    },
    // 资源告警量数据
    alarmTop() {
      getAction(this.url.alarmTop).then((res) => {
        if (res.code == 200) {
          this.resourcesWarning = res.result
          this.resourcesWarningHistogram(res.result)
        }
      })
    },
    // 资源告警量柱状图
    resourcesWarningHistogram(data = []) {
      function attackSourcesDataFmt(sData) {
        var sss = []
        sData.forEach(function (item, i) {
          if (item.value == undefined || item.value == null || item.value == '') {
            item.value = 0
          }
          sss.push({
            value: item.value + '%',
          })
        })
        return sss.reverse()
      }

      let arr = []
      let brr = []
      if (typeof data == 'object' && data.length > 0) {
        data.forEach((e) => {
          arr.push(e.name)
          brr.push(e.value)
        })
        arr.reverse()
        let myChart = this.$echarts.init(document.getElementById('resourcesWarningHistogram'))
        myChart.setOption({
          tooltip: {
            show: true,
            trigger: 'axis',
            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
            },
            transitionDuration: 0, //echart防止tooltip的抖动
          },
          xAxis: {
            type: 'value',
            max: '100',
            splitLine: {
              show: false,
            }, //去除网格线
            show: false,
          },
          yAxis: [
            {
              type: 'category',
              data: arr,
              splitLine: {
                show: false,
              }, //去除网格线
              axisTick: {
                show: false,
              },
              axisLine: {
                show: false, //y轴线消失
                lineStyle: {
                  //y轴字体颜色
                  color: '#f6f6f6',
                },
              },
              axisLabel: {
                formatter: (value) => {
                  if (value.length > 20) {
                    return value.substring(0, 20) + '...'
                  } else {
                    return value
                  }
                },
              },
            },
            {
              type: 'category',
              inverse: true,
              axisTick: 'none',
              axisLine: 'none',
              show: true,
              axisLabel: {
                textStyle: {
                  color: '#00a1e7',
                  fontSize: '14',
                },
              },
              data: attackSourcesDataFmt(data).reverse(),
            },
          ],

          grid: {
            top: 0,
            left: 130, // 调整这个属性
            right: 60,
            bottom: 0,
          },
          series: [
            {
              data: brr.reverse(),
              type: 'bar',
              showBackground: true,
              backgroundStyle: {
                color: 'rgba(180, 180, 180, 0.2)', //柱状图背景颜色
              },
              barWidth: 10, //柱图宽度
              itemStyle: {
                emphasis: {
                  barBorderRadius: 30,
                },
                normal: {
                  barBorderRadius: [10, 10, 10, 10],
                  color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                    {
                      offset: 0,
                      color: '#3679fb',
                    },
                    {
                      offset: 1,
                      color: '#0cf6f7',
                    },
                  ]),
                  // label: {
                  //   show: true,
                  //   position: 'right',
                  //   textStyle: {
                  //     color: '#00a1e7',
                  //     fontSize: 16,
                  //   },
                  // },
                },
              },
              backgroundStyle: {
                color: 'rgba(255,255,255,0)',
              },
            },
          ],
        })
        let that = this
        myChart.getZr().on('click', function (params) {
          let op = myChart.getOption()
          let pointInPixel = [params.offsetX, params.offsetY]
          if (myChart.containPixel('grid', pointInPixel)) {
            const xIndex = myChart.convertFromPixel(
              {
                seriesIndex: 0,
              },
              pointInPixel
            )[1]
            let handleIndex = Number(xIndex)
            //获得图表中点击的列
            var name = op.yAxis[0].data[handleIndex] // 当前点击的 Y轴 的名称
            if (name != undefined && name != '') {
              let routerInfo = that.routerInfoList[2]
              Object.assign(routerInfo.params, { deviceName: name })
              that.jumpRouter(routerInfo)
            }
          }
        })
        window.addEventListener('resize', () => {
          myChart.resize()
        })
      }
    },
    // 工单统计数据
    orderCount() {
      getAction(this.url.orderCount).then((res) => {
        if (res.code == 200) {
          this.workOrderDataOne = res.result.number[0]
          let value = this.workOrderDataOne.value
          if(value===undefined || value===null || value===''){
            this.workOrderDataOne.value = 0
          }
          this.workOrderDataTwo = res.result.number[1]
          let value1 = this.workOrderDataTwo.value
          if(value1===undefined || value1===null || value1===''){
            this.workOrderDataTwo.value = 0
          }
          this.workOrderDataThree = res.result.number[2]
          let value2 = this.workOrderDataThree.value
          if(value2===undefined || value2===null || value2===''){
            this.workOrderDataThree.value = 0
          }
          this.percent1 = res.result.chart[0]
          this.percent2 = res.result.chart[1]
          this.percent3 = res.result.chart[2]
          // this.liquidfillOne(res.result.chart[0])
          // this.liquidfillOne()
        }
      })
    },
    // 尝试做水球图，但是没做出来
    liquidfillOne() {
      // var max = 100 //满刻度大小
      // let dataPer
      // dataPer = data.value / 100
      // let myChart = this.$echarts.init(document.getElementById('liquidfillOne'))
      // myChart.setOption({
      //   backgroundColor: 'rgba(0, 10, 124, )',
      //   title: [
      //     {
      //       text: '能耗环比',
      //       x: '50%',
      //       y: '90%',
      //       textAlign: 'center',
      //       textStyle: {
      //         fontSize: '14',
      //         fontWeight: '400',
      //         color: '#fff',
      //         textAlign: 'center',
      //         fontFamily: 'PingFangSC-Regular, PingFang SC',
      //       },
      //     },
      //     {
      //       top: '47%',
      //       left: 'center',
      //       text: data.value + ' %',
      //       textStyle: {
      //         color: '#fff',
      //         fontStyle: 'normal',
      //         fontWeight: '400',
      //         fontFamily: 'PingFangSC-Regular, PingFang SC',
      //         fontSize: 46,
      //       },
      //     },
      //   ],
      //   series: [
      //     {
      //       type: 'liquidFill',
      //       itemStyle: {
      //         opacity: 0.8, //波浪的透明度
      //         shadowBlur: 10, //波浪的阴影范围
      //         shadowColor: '#FFB931', //阴影颜色
      //       },
      //       radius: '60%',
      //       //水波
      //       color: [' rgba(28,58,154,.6)', '  rgba(28,58,154,.4)'],
      //       data: [dataPer, dataPer],
      //       // background: '#000',
      //       center: ['50%', '52%'],
      //       backgroundStyle: {
      //         color: 'transparent',
      //       },
      //       label: {
      //         normal: {
      //           formatter: '',
      //           textStyle: {
      //             fontSize: 12,
      //           },
      //         },
      //       },
      //       outline: {
      //         itemStyle: {
      //           borderColor: 'transparent',
      //           borderWidth: 5,
      //         },
      //         borderDistance: 0,
      //       },
      //     },
      //     //外环线
      //     {
      //       color: ['#1C3A9A', 'rgba(28,58,154,.2)'],
      //       type: 'pie',
      //       center: ['50%', '52%'],
      //       radius: ['60%', '64%'],
      //       hoverAnimation: false,
      //       data: [
      //         {
      //           name: '',
      //           value: data.value,
      //           label: {
      //             show: false,
      //             position: 'center',
      //             color: '#fff',
      //             fontSize: 38,
      //             fontWeight: 'bold',
      //             formatter: function (o) {
      //               return data.value
      //             },
      //           },
      //           labelLine: {
      //             show: false,
      //           },
      //         },
      //         {
      //           //画剩余的刻度圆环
      //           name: '',
      //           value: max - data.value,
      //           label: {
      //             show: false,
      //           },
      //           labelLine: {
      //             normal: {
      //               show: false,
      //             },
      //             emphasis: {
      //               show: false,
      //             },
      //           },
      //         },
      //       ],
      //     },
      //   ],
      // })
      // let myChart = this.$echarts.init(document.getElementById('liquidfillOne'))
      let myChart = this.$echarts.init(this.$refs.liquidfillOne)
      var max = 100 //满刻度大小
      var data = 56
      var dataPer = data / 100
      var option = {
        title: [
          {
            text: '能耗环比',
            x: '50%',
            y: '90%',
            textAlign: 'center',
            textStyle: {
              fontSize: '14',
              fontWeight: '400',
              color: '#fff',
              textAlign: 'center',
            },
          },
          {
            top: '47%',
            left: 'center',
            text: data + ' %',
            textStyle: {
              color: '#fff',
              // fontStyle: 'normal',
              fontWeight: '400',
              fontSize: 16,
            },
          },
        ],
        series: [
          {
            type: 'liquidFill',
            itemStyle: {
              opacity: 0.8, //波浪的透明度
              shadowBlur: 10, //波浪的阴影范围
              shadowColor: '#FFB931', //阴影颜色
            },
            radius: '60%',
            //水波
            color: [' rgba(28,58,154,.6)', '  rgba(28,58,154,.4)'],
            data: [dataPer, dataPer],
            // background: '#000',
            center: ['50%', '52%'],
            backgroundStyle: {
              color: 'transparent',
            },
            label: {
              normal: {
                formatter: '',
                textStyle: {
                  fontSize: 12,
                },
              },
            },
            outline: {
              itemStyle: {
                borderColor: 'transparent',
                borderWidth: 5,
              },
              borderDistance: 0,
            },
          },
          //外环线
          {
            color: ['#1C3A9A', 'rgba(28,58,154,.2)'],
            type: 'pie',
            center: ['50%', '52%'],
            radius: ['60%', '68%'],
            hoverAnimation: false,
            data: [
              {
                name: '',
                value: data,
                label: {
                  show: false,
                  position: 'center',
                  color: '#fff',
                  fontSize: 38,
                  fontWeight: 'bold',
                  formatter: function (o) {
                    return data
                  },
                },
                labelLine: {
                  show: false,
                },
              },
              {
                //画剩余的刻度圆环
                name: '',
                value: max - data,
                label: {
                  show: false,
                },
                labelLine: {
                  normal: {
                    show: false,
                  },
                  emphasis: {
                    show: false,
                  },
                },
              },
            ],
          },
        ],
      }
      myChart.setOption(option)
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    },
    formatter: function (num) {
      if (num < 0) {
        return ''
      }
      return num.toFixed(2) //小数点后几位，数字就是几小数点后几位
    },
    // 资产统计柱状图数据
    cmdbCount() {
      getAction(this.url.cmdbCount).then((res) => {
        if (res.code == 200) {
          this.assetStatisticsHistogram(res.result, res.result.line)
        }
      })
    },
    // 资产统计柱状图
    assetStatisticsHistogram(data, dataList) {
      let xArr = []
      let yValue1 = []
      let yValue2 = []
      dataList.forEach((e) => {
        xArr.push(e.name)
        yValue1.push(e.value1)
        yValue2.push(e.value2)
      })

      let myChart = this.$echarts.init(document.getElementById('assetStatisticsHistogram'))
      myChart.setOption({
        legend: {
          data: [data.value1, data.value2],
          left: 'right',
          textStyle: {
            color: '#c4e4fd',
          },
        },
        tooltip: {
          type: true,
          transitionDuration: 0, //echart防止tooltip的抖动
        },
        grid: {
          left: '3%',
          right: 10,
          bottom: '',
          top: '14%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: xArr,
          axisLine: {
            lineStyle: {
              type: 'solid',
              color: '#5a595f', //左边线的颜色
              width: '1', //坐标线的宽度
            },
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#fff',
            },
          },
        },
        yAxis: {
          type: 'value',
          axisLine: {
            show: false, //y轴线消失
            lineStyle: {
              //y轴字体颜色
              color: '#c4c4c6',
            },
          },
          axisTick: {
            show: false,
          },
          //网格线颜色
          splitLine: {
            show: true,
            lineStyle: {
              color: ['#424348'],
              width: 1,
              type: 'solid',
            },
          },
        },
        series: [
          {
            // 上半截柱子
            name: data.value1,
            type: 'bar',
            barGap: '-100%',
            barWidth: '10',
            z: 0,
            itemStyle: {
              color: '#009eff',
            },
            data: yValue1,
          },
          // {
          //   name: data.value2,
          //   type: 'bar',
          //   stack: 'total',
          //   // label: {
          //   //   show: true,
          //   // },
          //   barWidth: 10, //柱图宽度
          //   emphasis: {
          //     focus: 'series',
          //   },
          //   data: yValue2,
          //   itemStyle: {
          //     normal: {
          //       color: '#4bffdc',
          //     },
          //   },
          // },
          {
            // 下半截柱子
            name: data.value2,
            type: 'bar',
            barGap: '-100%',
            barWidth: '10',
            // z: 0,
            itemStyle: {
              color: '#4bffdc',
            },
            data: yValue2,
          },
          // {
          //   name: data.value1,
          //   type: 'bar',
          //   stack: 'total',
          //   // label: {
          //   //   show: true,
          //   // },
          //   barWidth: 10, //柱图宽度
          //   emphasis: {
          //     focus: 'series',
          //   },
          //   data: yValue1,
          //   itemStyle: {
          //     normal: {
          //       color: '#009eff',
          //     },
          //   },
          // },
        ],
      })
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    },
    // 服务器CPU使用率数据
    cpuTop() {
      getAction(this.url.cpuTop).then((res) => {
        if (res.code == 200) {
          this.theServerHistogram(res.result)
          this.cpuTopData = res.result
        }
      })
    },
    // 服务器CPU使用率柱状图
    theServerHistogram(data = []) {
      function attackSourcesDataFmt(sData) {
        var sss = []
        sData.forEach(function (item, i) {
          sss.push({
            value: item.value + '%',
          })
        })
        return sss
      }

      let arr = []
      let brr = []
      data.forEach((e) => {
        arr.push(e.name)
        brr.push(e.value)
      })
      arr.reverse()
      brr.reverse()
      let myChart = this.$echarts.init(document.getElementById('theServerHistogram'))
      myChart.setOption({
        tooltip: {
          show: true,
          trigger: 'axis',
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
          },
          transitionDuration: 0, //echart防止tooltip的抖动
        },
        xAxis: {
          type: 'value',
          max: '100',
          splitLine: {
            show: false,
          }, //去除网格线
          show: false,
        },
        yAxis: [
          {
            type: 'category',
            data: arr,
            splitLine: {
              show: false,
            }, //去除网格线
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false, //y轴线消失
              lineStyle: {
                //y轴字体颜色
                color: '#f6f6f6',
              },
              axisLabel: {
                formatter: (value) => {
                  if (value.length > 10) {
                    return value.substring(0, 9) + '...'
                  } else {
                    return value
                  }
                }
              },
            },
          },
          {
            type: 'category',
            inverse: true,
            axisTick: 'none',
            axisLine: 'none',
            show: true,
            axisLabel: {
              textStyle: {
                color: '#00a1e7',
                fontSize: '14',
              },
            },
            data: attackSourcesDataFmt(data),
          },
        ],

        grid: {
          top: 0,
          left: 140, // 调整这个属性
          right: 50,
          bottom: 0,
        },
        series: [
          {
            data: brr,
            type: 'bar',
            showBackground: true,
            backgroundStyle: {
              color: 'rgba(180, 180, 180, 0.2)', //柱状图背景颜色
            },
            barWidth: 10, //柱图宽度
            itemStyle: {
              emphasis: {
                barBorderRadius: 30,
              },
              normal: {
                barBorderRadius: [10, 10, 10, 10],
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                  {
                    offset: 0,
                    color: '#3679fb',
                  },
                  {
                    offset: 1,
                    color: '#0cf6f7',
                  },
                ]),
                // label: {
                //   show: true,
                //   position: 'right',
                //   textStyle: {
                //     color: '#00a1e7',
                //     fontSize: 16,
                //   },
                // },
              },
            },
            backgroundStyle: {
              color: '#293e6d',
            },
          },
        ],
      })
      let that = this
      myChart.getZr().on('click', function (params) {
        let op = myChart.getOption()
        let pointInPixel = [params.offsetX, params.offsetY]
        if (myChart.containPixel('grid', pointInPixel)) {
          const xIndex = myChart.convertFromPixel(
            {
              seriesIndex: 0,
            },
            pointInPixel
          )[1]
          let handleIndex = Number(xIndex)
          //获得图表中点击的列
          var name = op.yAxis[0].data[handleIndex] // 当前点击的 Y轴 的名称
          if (name != undefined && name != '') {
            let routerInfo = that.routerInfoList[4]
            Object.assign(routerInfo.params, { deviceName: name })
            that.jumpRouter(routerInfo)
          }
        }
      })
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    },
  },
}
</script>
<style lang="less">
.yq-map-tip {
  padding-top: 20px;
  width: 291px;
  height: 100px;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-image: url(/img/mapTip.png);
  color: #fff;
  .tip-title {
    // margin-top: 16px;
    font-size: 20px;
    text-align: center;
    font-weight: 600;
    letter-spacing: 1px;
  }
  .tip-con {
    .tip-item {
      display: flex;
      margin-top: 12px;
      .title {
        font-size: 14px;
        width: 50%;
        display: flex;
        flex-direction: row-reverse;
        letter-spacing: 2px;
        color:#fff;
        span {
          display: inline-block;
          width: 90px;
        }
      }
      .value {
        width: 50%;
        font-size: 16px;
        font-weight: 600;
        font-family: DIN-Medium;
        color: #56c4f2;
        letter-spacing: 1px;
      }
    }
  }
}
</style>
<style lang='less' scoped>
.core-top-body-patch1::-webkit-scrollbar {
  display: none;
  /*隐藏滚动条*/
}

.left-bottom-table::-webkit-scrollbar {
  display: none;
  /*隐藏滚动条*/
}

.body {
  padding: 0.25rem /* 20/80 */ 0.2rem 0.1125rem 0.2rem;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;

  .left {
    width: 45%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .left-top {
      height: 68%;
      width: 100%;
      background: #131419;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 0.075rem /* 6/80 */;

      .left-top-core {
        width: 100%;
        height: 100%;
        // background-color: #fff;
      }
    }

    .left-bottom {
      height: 30.6%;
      width: 100%;
      // margin-top: 0.25rem /* 20/80 */;
      background: #131419;
      overflow: hidden;
      border-radius: 0.075rem /* 6/80 */;

      .left-bottom-core {
        width: 100%;
        height: 100%;
        padding: 0 0.25rem /* 20/80 */;

        .left-bottom-tableTitle {
          display: flex;
          height: 30px;
          align-items: center;
          padding-bottom: 13px;
          border-bottom: 1px solid #1d44a9;

          span {
            font-size: 16px;
            width: 100%;
            text-align: center;
            color: #00c4f6;
          }
        }

        .left-bottom-table {
          width: 100%;
          height: 80%;
          overflow-x: auto;

          .seamless-warp {
            height: 100%;
            width: 100%;

            div {
              align-items: center;
              width: 100%;
              line-height: 0.525rem;
              /* 42/80 */
              display: flex;
              justify-content: space-around;

              span {
                width: 100%;
                color: rgba(255, 255, 255, 0.75);
                font-size: 0.175rem;
                text-align: center;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }

              div:nth-child(2n + 0) {
                background: #1f2533;
                text-align: center;
              }
            }
          }
        }
      }
    }
  }

  .core {
    width: 27%;
    height: 100%;
    // margin: 0 0.25rem /* 20/80 */;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .core-top {
      width: 100%;
      height: 27%;
      background: #131419;
      border-radius: 0.075rem /* 6/80 */;

      .core-top-body {
        color: #fff;
        display: flex;

        .core-top-body-patch {
          width: 100%;
          color: #fff;
          display: flex;
          flex-wrap: wrap;
          // justify-content: space-around;
          padding: 0.3rem /* 24/80 */ 0.375rem /* 30/80 */ 0 0.375rem /* 30/80 */;

          .core-top-body-item {
            width: 24%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            margin-bottom: 0.3rem /* 24/80 */;

            span {
              text-align: center;
            }

            span:nth-child(1) {
              font-size: 0.175rem /* 14/80 */;
              border-right: #019cef solid 1px;
            }

            span:nth-child(2) {
              font-size: 0.275rem /* 22/80 */;
              color: #39fef4;
              border-right: #1b2f61 solid 1px;
            }
          }

          div:nth-child(2n + 0) {
            span:nth-child(2) {
              color: #47caff;
            }
          }

          div:nth-child(4n + 0) {
            span:nth-child(1) {
              border: none;
            }

            span:nth-child(2) {
              border: none;
            }
          }
        }

        .core-top-body-patch1 {
          width: 100%;
          color: #fff;
          display: flex;
          flex-wrap: wrap;
          // justify-content: space-around;
          padding: 0.2rem /* 24/80 */ 0.2rem /* 30/80 */ 0 0.2rem /* 30/80 */;

          .core-top-body-item {
            width: 18%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            margin-bottom: 0.3rem /* 24/80 */;

            span {
              text-align: center;
            }

            span:nth-child(1) {
              font-size: 0.175rem /* 14/80 */;
              border-right: #019cef solid 1px;
            }

            span:nth-child(2) {
              font-size: 0.275rem /* 22/80 */;
              color: #39fef4;
              border-right: #1b2f61 solid 1px;
            }
          }

          div:nth-child(2n + 0) {
            span:nth-child(2) {
              color: #47caff;
            }
          }

          div:nth-child(4n + 0) {
            span:nth-child(1) {
              //border: none;
            }

            span:nth-child(2) {
              // border: none;
            }
          }
        }
      }

      .no-content {
        height: 80%;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 0.3rem /* 24/80 */;
        color: #45c5e0;
        margin-top: -0.25rem /* 20/80 */;
      }
    }

    .core-core {
      width: 100%;
      height: 40%;
      background: #131419;
      border-radius: 0.075rem /* 6/80 */;

      // margin: 0.25rem /* 20/80 */ 0;
      .core-core-body {
        height: 80%;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;

        .core-core-body-circularGraph {
          width: 100%;
          height: 100%;
        }
      }
    }

    .core-bottom {
      width: 100%;
      height: 31%;
      background: #131419;
      border-radius: 0.075rem /* 6/80 */;

      .core-bottom-body {
        width: 100%;
        height: 74%;
        display: flex;
        // height: 2.5rem /* 200/80 */;
        align-items: center;
        justify-content: center;

        // 226px
        .core-bottom-body-left {
          width: 90%;
          height: 80%;
        }
      }
    }
  }

  .right {
    width: 27%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .right-top {
      width: 100%;
      height: 27%;
      background: #131419;
      border-radius: 0.075rem /* 6/80 */;

      .right-top-body {
        height: 80%;
        padding: 0 0.25rem /* 20/80 */;

        .right-top-body-top {
          display: flex;
          height: 40%;
          justify-content: space-between;
          border-bottom: #1a3061 solid 1px;

          // padding: 12px 0;
          .right-top-body-top-item {
            color: #fff;
            width: 137px;
            font-size: 0.1625rem /* 13/80 */;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            .name {
              border-right: #009bee solid 1px;
              width: 100%;
              text-align: center;
            }

            .value {
              color: #44efff;
              font-size: 0.275rem /* 22/80 */;
              border-right: #1a3061 solid 1px;
              width: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }

          div:nth-child(3) {
            .name {
              border: none;
            }

            .value {
              border: none;
            }
          }
        }

        .right-top-body-bottom {
          display: flex;
          align-items: center;
          height: 58%;

          .right-top-body-bottom-left {
            display: flex;
            width: 33%;
            flex-direction: column;
            justify-content: center;
            align-items: center;

            .right-top-body-bottom-left-one {
              width: 100%;
              height: 100%;
              display: flex;
              justify-content: center;
              align-items: center;

              .liquidfillOne {
                width: 100%;
                height: 100%;
              }
            }

            span {
              color: rgba(255, 255, 255, 0.75);
              margin-top: 0.1rem /* 8/80 */;
            }
          }

          .right-top-body-bottom-core {
            display: flex;
            width: 33%;
            flex-direction: column;
            justify-content: center;
            align-items: center;

            span {
              color: rgba(255, 255, 255, 0.75);
              margin-top: 0.1rem /* 8/80 */;
            }
          }

          .right-top-body-bottom-right {
            display: flex;
            width: 33%;
            flex-direction: column;
            justify-content: center;
            align-items: center;

            span {
              color: rgba(255, 255, 255, 0.75);
              margin-top: 0.1rem /* 8/80 */;
            }
          }
        }
      }
    }

    .right-core {
      width: 100%;
      height: 40%;
      background: #131419;
      border-radius: 0.075rem /* 6/80 */;

      // margin: 0.25rem /* 20/80 */ 0;
      .right-core-body {
        width: 98%;
        height: 74%;
      }
    }

    .right-bottom {
      width: 100%;
      height: 31%;
      background: #131419;
      border-radius: 0.075rem /* 6/80 */;

      .right-bottom-body {
        display: flex;
        width: 98%;
        height: 74%;

        // height: 2.5rem /* 200/80 */;
        .right-bottom-body-left {
          width: 100%;
        }
      }
    }
  }
}

.topTitle {
  height: 20%;
  display: flex;
  align-items: center;
  font-size: 0.225rem /* 18/80 */;
  color: #45c5e0;
  padding-left: 0.15rem /* 12/80 */;
  letter-spacing: 0.025rem /* 2/80 */;

  img {
    width: 0.125rem /* 10/80 */;
    height: 0.1625rem /* 13/80 */;
    margin-right: 0.0875rem /* 7/80 */;
  }
}
</style>