<template>
  <a-modal
    :title="title"
    :width="modalWidth"
    :visible="visible"
    :confirmLoading="confirmLoading"
    :destroyOnClose="true"
    @ok="doStartInstance"
    @cancel="handleCancel"
    cancelText="关闭"
    wrapClassName="ant-modal-cust-warp"
    style="top: 5%; height: 95%; overflow: hidden; overflow-x: auto; z-index: 999"
  >
    <div>
      <fm-generate-form :data="startFormJson" ref="generateStartForm" :value="variables" :remote="remoteFuncs" disabled>
      </fm-generate-form>
    </div>
    <div slot="footer" class="dialog-footer" style="text-align: right">
      <a-button @click="handleCancel">取消</a-button>
      <a-button v-if="type" type="primary" style="margin-left: 16px" @click="doStartInstance">确定 </a-button>
    </div>
  </a-modal>
  <!-- <el-dialog :title="title" :visible.sync="dialogStartProcessVisibleInChild" :fullscreen="false">

  </el-dialog> -->
</template>

<script>
import { getAction, postAction } from '@/api/manage'
import { ajaxGetDictItems, getProgectList } from '@/api/api'
import { Message } from 'element-ui'
//import { JeecgListMixin } from '@/mixins/JeecgListMixin'

export default {
  name: 'StartProcess',
  //mixins: [JeecgListMixin],
  data() {
    return {
      // fullscreen: true,
      confirmLoading: false,
      /* 弹框宽 */
      modalWidth: '1000px',
      visible: false,
      //dialogStartProcessVisibleInChild: false,
      startFormJson: { list: [] },
      variables: {},
      // showBusinessKey: false,
      businessKey: undefined,
      processDefinition: {
        type: Object,
        default: function () {
          return {}
        },
      },
      title: '发起流程',
      type: true,
      remoteFuncs: {
        getType(resolve) {
          ajaxGetDictItems('question_type_sy', null).then((res) => {
            if (res.success) {
              const options = res.result
              resolve(options)
            }
          })
        },
        getProgect(resolve) {
          getProgectList(null).then((res) => {
            if (res.success) {
              const options = res.result
              resolve(options)
            }
          })
        },
      },
    }
  },
  created() {
    this.variables = null
    //this.initData()
  },
  methods: {
    //开始加载时  获取表单
    initData(v) {
      this.processDefinition = v
      if (this.processDefinition.formKey) {
        getAction('/flowableform/umpFlowableForm/queryByKey', {
          key: this.processDefinition.formKey,
          tableId: this.processDefinition.tableId,
        }).then((res) => {
          if (res.success) {
            var formData = res.result
            if (formData && formData.formJson) {
              this.startFormJson = JSON.parse(formData.formJson)
              this.variables = JSON.parse(formData.formValue)
              //this.dialogStartProcessVisibleInChild = true
              this.confirmLoading = false
              this.visible = true
            }
          }
        })
      }
    },
    handleCancel() {
      this.close()
      //this.dialogStartProcessVisibleInChild = false;
    },
    // 关闭弹框
    close() {
      this.$emit('close')
      this.visible = false
      //this.current = 0
    },
    //提交表单
    doStartInstance() {
      var that = this
      if (this.$refs.generateStartForm) {
        if (this.processDefinition.tableId) {
          this.$refs.generateStartForm
            .getData()
            .then((values) => {
              if (values && values != undefined) {
                let formData = Object.assign(this.data || {}, values)
                formData.procDefId = this.processDefinition.id
                formData.procDeTitle = this.processDefinition.name
                formData.form_value = JSON.stringify(values)
                //Object.assign({processInstanceFormData}, values)
                formData.filedNames = 'form_value' + ',' + 'form_key'
                formData.form_key = this.processDefinition.formKey
                formData.id = this.processDefinition.tableId
                postAction('/actBusiness/editForm', formData).then((res) => {
                  this.uploading = false
                  if (res.success) {
                    this.$message.success('保存成功')
                    //this.dialogStartProcessVisibleInChild = false
                    this.visible = false
                    that.$emit('ok')
                  } else {
                    this.$message.warning(res.message)
                    //this.dialogStartProcessVisibleInChild = false
                    this.visible = false
                  }
                })
              }
            })
            .catch((e) => {})
        } else {
          this.$refs.generateStartForm
            .getData()
            .then((values) => {
              if (values && values != undefined) {
                let formData = Object.assign(this.data || {}, values)
                formData.procDefId = this.processDefinition.id
                formData.procDeTitle = this.processDefinition.name
                formData.form_value = JSON.stringify(values)
                //Object.assign({processInstanceFormData}, values)
                formData.filedNames = 'form_value' + ',' + 'form_key'
                formData.form_key = this.processDefinition.formKey
                postAction('/actBusiness/add', formData).then((res) => {
                  this.uploading = false
                  if (res.success) {
                    this.$message.success('保存成功')
                    //this.dialogStartProcessVisibleInChild = false
                    this.visible = false
                    that.$emit('ok')
                  } else {
                    this.$message.warning(res.message)
                    //this.dialogStartProcessVisibleInChild = false
                    this.visible = false
                  }
                })
              }
            })
            .catch((e) => {})
        }
      }
    },
  },
}
</script>

