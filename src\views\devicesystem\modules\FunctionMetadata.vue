<template>
  <!--  <a-form-item label="输入参数" :labelCol="labelCol" :wrapperCol="wrapperCol">-->
  <a-form-item label=" " :colon='false' :labelCol="labelCol" :wrapperCol="wrapperCol">
    <a-table
      bordered
      :showHeader="true"
      :pagination="false"
      :row-key='(record,index)=>{return index}'
      :columns="columns"
      :data-source="parameterList"
      :scroll='parameterList.length>0?{x:"max-content"}:{}'
    >
      <template slot="name" slot-scope="text">
        {{ text }}
      </template>
      <template slot="isEditable" slot-scope="text">
        <span v-if="text == 1">是</span>
        <span v-else>否</span>
      </template>

      <span slot="action" slot-scope="text, record,index">
        <a @click="handleEdit(record,index)">编辑</a>
        <a-divider type="vertical" />
        <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(index)">
          <a>删除</a>
        </a-popconfirm>
      </span>
    </a-table>
    <function-input-parameter-modal ref="modalForm" @ok="modalFormOk"></function-input-parameter-modal>
  </a-form-item>
</template>

<script>
import FunctionInputParameterModal from '@views/devicesystem/modules/FunctionInputParameterModal'
export default {
  name: 'FunctionMetadata',
  components: {
    FunctionInputParameterModal
  },
  props: {
    functionParameterList: Array,
  },
  data() {
    return {
      labelCol: {
        xs: { span: 5 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      columns: [
        {
          title: '名称',
          dataIndex: 'name',
          align: 'center',
        },
        {
          title: '项',
          dataIndex: 'item',
          align: 'center',
        },
        {
          title: '默认值',
          dataIndex: 'defaultValue',
          align: 'center',
        },
        {
          title: '默认值是否可编辑',
          dataIndex: 'isEditable',
          scopedSlots: { customRender: 'isEditable' },
          align: 'center',
        },
        {
          title: '描述',
          dataIndex: 'description',
          align: 'center',
        },
        {
          title: '操作',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' },
          align: 'center',
          width: '120px',
        },
      ],
      url: {
        delete: '/product/FunctionMetadata/delete',
      },
      parameterList: [],
    }
  },
  watch: {
    //监听
    functionParameterList: {
      handler: function (val, oldval) {
        if (val != oldval) {
          this.$nextTick(() => {
            this.initData()
          })
        }
      },
      immediate: true, //关键
      deep: true,
    },
  },
  methods: {
    initData() {
      this.parameterList = this.functionParameterList
    },
    handleEdit(record,index) {
      const that = this
      that.$refs.modalForm.parameterList=that.parameterList
      that.$refs.modalForm.edit(record,index)
      that.$refs.modalForm.title = '编辑'
      that.$refs.modalForm.disableSubmit = false
    },
    modalFormOk(info,index) {
      if (undefined != info) {
        const that = this
        // 新增/修改 成功时，重载列表
        let tempData=that.parameterList
        if(!tempData[index]){
          tempData.push(info)
        }
        else {
          tempData.splice(index,1,info)
        }
        that.parameterList=tempData
        that.$emit('func', that.parameterList)
      }
    },
    handleDelete(index) {
      const that = this
      this.parameterList.splice(index,1)
      that.$emit('func', that.parameterList)
    },
  },
}
</script>

<style>
</style>
