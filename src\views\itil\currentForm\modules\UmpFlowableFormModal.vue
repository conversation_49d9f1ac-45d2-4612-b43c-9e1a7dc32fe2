<template>
  <a-modal
    :title="title"
    :width="width"
    :visible="visible"
    :confirmLoading="confirmLoading"
    switchFullscreen
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item label="表单编码" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            :allowClear="true"
            autocomplete="off"
            v-decorator="['formKey', validatorRules.formKey]"
            placeholder="请输入表单编码"
          ></a-input>
        </a-form-item>
        <a-form-item label="表单名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input
            :allowClear="true"
            autocomplete="off"
            v-decorator="['formName', validatorRules.formName]"
            placeholder="请输入表单名称"
          ></a-input>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
import { httpAction } from '@/api/manage'
import pick from 'lodash.pick'
import { validateDuplicateValue } from '@/utils/util'
import { duplicateCheck } from '@/api/api'

export default {
  name: 'UmpFlowableFormModal',
  components: {},
  data() {
    return {
      form: this.$form.createForm(this),
      title: '操作',
      width: 800,
      visible: false,
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      confirmLoading: false,
      validatorRules: {
        formKey: {
          rules: [
            {
              required: true,
              message: '请输入表单编码!',
            },
            {min:2,max:50,message:'表单编码在2-50个非汉字之间'},
            {
              validator: this.validateFormKey,
            },
          ],
        },
        formName: { rules: [{ required: true, message: '请输入表单名称!' },
        {min:2,max:50,message:'表单名称在2-50个字符之间'}] },
      },
      url: {
        add: '/flowableform/umpFlowableForm/add',
        edit: '/flowableform/umpFlowableForm/edit',
      },
    }
  },
  created() {},
  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(pick(this.model, 'formKey', 'formName'))
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let formData = Object.assign(this.model, values)
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
              that.close()
            })
        }
      })
    },
    handleCancel() {
      this.close()
    },
    popupCallback(row) {
      this.form.setFieldsValue(pick(row, 'formKey', 'formName', 'formJson'))
    },
    validateFormKey(rule, value, callback) {
      if (/[\u4E00-\u9FA5]/g.test(value)) {
        callback('表单编码不可输入汉字!')
      } else {
        var params = {
          tableName: 'ump_flowable_form',
          fieldName: 'FORM_KEY',
          fieldVal: value,
          dataId: this.model.id,
        }
        duplicateCheck(params).then((res) => {
          if (res.success) {
            callback()
          } else {
            callback(res.message)
          }
        })
      }
    },
  },
}
</script>