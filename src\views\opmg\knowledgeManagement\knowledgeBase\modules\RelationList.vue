<template>
  <div>
    <a-table
      ref='table'
      bordered
      :row-key='(record, index) => {return record.relationId}'
      :columns='columns'
      :dataSource='dataSource'
      :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
      :pagination='ipagination'
      :loading='loading'
      @change='handleTableChange'>
      <span slot='action' class='caozuo' slot-scope='text, record'  >
        <a @click="btnView(record.id)">查看</a>
        <span v-if='sysUserId==kInfo.createByUserId||approvalView'>
          <a-divider type="vertical" />
          <a-popconfirm :disabled='approvalView' title="确定删除吗?" @confirm="() => handleDelete(record.relationId)" >
            <a :class='{"disabled-delete":approvalView}'>删除</a>
          </a-popconfirm>
        </span>
      </span>

      <template slot='tooltip' slot-scope='text'>
        <a-tooltip placement='topLeft' :title='text' trigger='hover'>
          <div class='tooltip'>
            {{ text }}
          </div>
        </a-tooltip>
      </template>
    </a-table>
    <!-- 表单区域 -->
    <process-instance-info-modal ref="processInstanceInfoModalForm" @ok="modalFormOk"></process-instance-info-modal>
  </div>
</template>
<script>
import {JeecgListMixin} from '@/mixins/JeecgListMixin'
import ProcessInstanceInfoModal from '@views/flowable/process-instance/modules/ProcessInstanceInfoModal'
import fa from 'element-ui/src/locale/lang/fa'
import { getAction } from '@api/manage'
export default {
  name: "KnowledgeComment",
  components:{ ProcessInstanceInfoModal},
  props:{
    kInfo:{
      type:Object,
      required:true,
      default:{}
    },
    //1表示事件，2表示问题，3表示工单
    processDefinitionKey:{
      type:String,
      required:true,
      default:''
    },
    /**若是通过知识审批列表打卡查看，
     收藏、分享、打印、评论、关联、点赞、点踩都不可操作性，
     附件统统可以下载，同时告诉管理员，知识创建者设置的允许下载附件状态*/
    approvalView:{
      type:Boolean,
      required:false,
      default:false
    }
  },
  mixins: [JeecgListMixin],
  data() {
    return {
      sysUserId:this.$store.getters.userInfo.id,
      disableMixinCreated:true,
      ipagination: {
        current: 1,
        pageSize: 5,
        pageSizeOptions: ['5', '10', '20'],
        showTotal: (total, range) => {
          return ' 共' + total + '条'
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0
      },
      columns: [
        {
          title: '编号',
          dataIndex: 'id',
        },
        {
          title: '标题',
          dataIndex: 'title',
        },
       /* {
          title: '类型',
          dataIndex: 'classification'
        },
        {
          title: '优先级',
          dataIndex: 'priority',
        },*/
        {
          title: '创建人员',
          dataIndex: 'startUser'
        },
        {
          title: '创建时间',
          dataIndex: 'startTime',
        },
        {
          title: '操作',
          dataIndex: 'action',
          customCell: () => {
            let cellStyle = 'text-align: center;width:120px'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'action'
          }
        }
      ],
      url: {
        list: '/kbase/relation/list',//评论列表
        delete:'/kbase/relation',
        deleteBatch:'/kbase/relation/batch',
      }
    }
  },
  watch:{
    kInfo:{
      handler(val){
        this.queryParam.knowledgeId=val.id
        this.init()
      },
      deep:true,
      immediate:true
    },
    processDefinitionKey:{
      handler(val){
        this.queryParam.processDefinitionKey=val
        this.init()
      },
      deep:true,
      immediate:true
    },
  },
  mounted() {

  },
  methods: {
    init() {
      if (this.queryParam.knowledgeId && this.queryParam.processDefinitionKey) {
        this.loadData(0)
      }
    },
    btnView: function (id) {
      if (!id) {
        this.$message.error('流程实例ID不存在')
        return
      }
      this.$refs.processInstanceInfoModalForm.init(id,false);
      this.$refs.processInstanceInfoModalForm.title = "流程实例信息";
      this.$refs.processInstanceInfoModalForm.disableSubmit = false;
    },
    handleTableChange(pagination, filters, sorter) {
      //分页、排序、筛选变化时触发
      //TODO 筛选
      if (Object.keys(sorter).length > 0) {
        this.isorter.column = sorter.field
        this.isorter.order = 'ascend' == sorter.order ? 'asc' : 'desc'
      }
      this.ipagination = pagination
    },
  }
}
</script>

<style scoped lang="less">
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
.add-comment{
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-flow: row wrap;
  margin-bottom: 16px;
}

.like-unlike-wrapper{
  margin: 0px 20px;

  .like-unlike {
    display: inline-block;
    white-space: nowrap;
    width: 30px;
    height: 30px;
    line-height: 30px;
    border-radius: 50%;
    border: 1px solid #e8e8e8;
    text-align: center;


    .like-unlike-icon {
      font-size: 18px;
    }
  }
  .like-unlike:hover{
    cursor: pointer;
    border: 1px solid #409eff;
  }
  .active-like,.active-unlike{
    color:#409eff
  }
}

.table-operator{
  margin-bottom: -16px;
}
.caozuo{
  .disabled-delete{
    color: rgba(0, 0, 0, 0.25) !important;
    cursor: default;
  }
}
</style>