<template>
  <div class="logo" :class="oneClickHelp ? 'helpLogo' : ''">
    <div v-if="oneClickHelp" class="oneClickHelp">
      <img :src="helpLogo" alt="" />
      <span class="logo-text">运维助手</span>
    </div>
    <a v-else-if="!onePtmFlag" href="javascript:void(0);" @click="logoClick">
      <h1 v-show="logoShow">
        <img v-if="navTheme === 'dark'" :src="logoUrl" alt="logo" />
        <img v-else :src="logoUrl" alt="logo" />
      </h1>
      <h1 v-if="showTitle" :style="{'font-size': fontSize + 'px'}">{{title}}</h1>
    </a>
    <span v-else href="#">
      <!-- update-begin- author:sunjianlei --- date:20190814 --- for: logo颜色根据主题颜色变化 -->
      <h1 v-show="logoShow">
        <img v-if="navTheme === 'dark'" :src="logoUrl" alt="logo" />
        <img v-else :src="logoUrl" alt="logo" />
      </h1>
      <!-- update-begin- author:sunjianlei --- date:20190814 --- for: logo颜色根据主题颜色变化 -->
      <h1 v-if="showTitle" style="font-size: 16px">{{ title }}</h1>
    </span>
  </div>
</template>

<script>
import { getAction, postAction } from '@/api/manage'
import { mixin } from '@/utils/mixin.js'
import store from '@/store'
import Vue from 'vue'
import { ONE_PLATFORM_FLAG, PLATFORM_TYPE,PTM_CHANGE } from '@/store/mutation-types'
import { mapActions } from 'vuex'
import { generateBigscreenRouter, generateIndexRouter } from '@/utils/util'
import router from '@/router'
export default {
  name: 'Logo',
  mixins: [mixin],
  data() {
    return {
      logoShow: window._CONFIG['logoShow'] === 1 ? true : false,
      title: window._CONFIG['systemName'],
      helpLogo: window.config.oneClickHelp.pageLogoUrl,
      logoUrl: window._CONFIG['logoUrl'],
      onePtmFlag: false,
      userRole: store.getters.userInfo.userIdentity,
      fontSize: 18,
      maxWidth: 160
    }
  },
  props: {
    showTitle: {
      type: Boolean,
      default: true,
      required: false,
    },
    oneClickHelp: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    title() {
      this.adjustFontSize()
    }
  },
  computed: {
    textWidth() {
      // 创建临时元素来获取文本宽度
      const tempEl = document.createElement('span')
      tempEl.style.fontSize = this.fontSize + 'px'
      tempEl.style.visibility = 'hidden'
      tempEl.innerHTML = this.title
      document.body.appendChild(tempEl)
      const width = tempEl.offsetWidth
      document.body.removeChild(tempEl)
      return width
    }
  },
  created() {
    if (this.oneClickHelp) {
      this.title = window.config.oneClickHelp.title
    } else {
      this.title = window._CONFIG['systemName']
    }
    this.onePtmFlag = Vue.ls.get(ONE_PLATFORM_FLAG) && st!=="3";
  },
  mounted() {
    this.adjustFontSize()
  },
  methods: {
    adjustFontSize() {
      // 当文本宽度超过容器最大宽度时，调整字体大小
      if (this.textWidth > this.maxWidth && this.fontSize > 12) {
        this.fontSize--
        this.adjustFontSize()
      }
    },
    logoClick() {
       let st = window._CONFIG['system_Type']
      if (this.oneClickHelp && st!=="3") {
        this.$router.push('/oneClickHelp/index')
        return
      }

      switch (st) {
        case '0':
          this.$router.push('/gateway')
          break
        case '1':
          if (this.userRole === 2) {
            this.entrancePlanning(4)
          } else {
            this.entrancePlanning(1)
          }
          break
        case '2':
          getAction('/sys/permission/getUserPlatformTypeByToken').then((pres) => {
            if(pres.success){
              let userPlatforms = [...pres.result.split(',')]
              getAction('/sys/permission/platformTypeList').then((res) => {
                if (res.success && res.result) {
                  let tem = res.result[0].value
                  if (userPlatforms.includes(tem)) {
                    this.entrancePlanning(tem)
                  } else {
                    this.entrancePlanning(userPlatforms[0])}
                }
              })
            }

          })
          break
      }
    },
    ...mapActions(['GetPermissionList']),
    entrancePlanning(index) {
      if (sessionStorage.getItem(PLATFORM_TYPE) !== index) {
        this.$store.commit(PTM_CHANGE,true)
      }
      sessionStorage.setItem(PLATFORM_TYPE, index)
      const that = this
      that.GetPermissionList(index).then((res) => {
        if (res === '1') {
          this.$message.warning('没有添加菜单！')
          return
        }
        const menuData = res.result.menu
        var redirect = ''
        if (menuData && menuData.length > 0) {
          let firsMenu = menuData[0]
          redirect = firsMenu.children && firsMenu.children.length > 0 ? firsMenu.children[0].path : firsMenu.path
        } else {
          return
        }

        let constRoutes = []
        if ([4, 8].includes(Number(index))) {
          constRoutes = generateBigscreenRouter(menuData)
        } else {
          constRoutes = generateIndexRouter(menuData)
        }
        // 添加主界面路由
        store
          .dispatch('UpdateAppRouter', {
            constRoutes,
          })
          .then(() => {
            // 根据roles权限生成可访问的路由表
            // 动态添加可访问路由表
            router.addRoutes(store.getters.addRouters)
            this.$emit('resetMenus')
            this.$router.push({
              path: redirect,
            })
            // }
          })
      })
    },
  },
}
</script>
<style lang="less" scoped>
  /*缩小首页布 局顶部的高度*/
  @height: 59px;

  .sider {
    box-shadow: none !important;

    .logo {
      height: @height  !important;
      line-height: @height  !important;
      box-shadow: none !important;
      transition: background 300ms;

      a {
        color: white;

        &:hover {
          color: rgba(255, 255, 255, 0.8);
        }
      }
    }

  &.light .logo {
    /*background-color: @primary-color;*/
    padding-top: 17px;
  }
}
.sider.light .helpLogo {
  padding: 0px;
  background: #000e23 !important;
  // display: flex;
  // justify-content: center;
  // align-items: center;
  height: 100%;
  width: 100%;
  .oneClickHelp {
    display: flex;
    align-items: center;
    padding-left: 45px;
    img {
      width: 24px;
      height: 24px;
    }
    .logo-text {
      margin-left: 12px;
      font-size: 18px;
      color: #ffffff;
      letter-spacing: 2.4px;
      font-weight: 500;
    }
  }
}
.sider.dark .helpLogo {
  padding: 0px;
  background: #000e23 !important;
}
</style>