<template>
  <!-- 端口速率趋势 -->
  <div>
    <div slot="bodySlot" class="empty-wrapper" v-if="chartData.length === 0">
      <a-spin :spinning="loading" v-if="loading" class="spin"></a-spin>
      <a-list :data-source="[]" v-else />
    </div>
    <div v-else ref="baseEcharts" style="height: 100%; width: 100%"></div>
  </div>
</template>

<script>
import { getAction } from '@/api/manage'
import echarts from 'echarts'
export default {
  name: 'PortSpeedChart',
  props: {
    fontSizeObject: {
      type: Object,
      default: function () {
        return {
          legendFontSize: 10,
          xAxisFontSize: 12,
          yAxisFontSize: 12,
        }
      },
    },
    showType: {
      type: Number,
      default: 0,
    },
  },
  watch: {
    showType: {
      handler(nVal) {
        this.$nextTick(() => {
          this.initData(this.chartData)
        })
      },
      immediate: true,
    },
  },
  data() {
    return {
      myChart: null,
      loading: false,
      chartData: {
        allSpeed: [],
        inSpeed: [],
        outSpeed: [],
      },
      url: {
        getDataTrendByTime: '/openAPI/getDataTrendByTime',
      },
    }
  },
  created() {
    this.getDataTrendByTime()
  },
  methods: {
    // 获取端口速率趋势数据
    getDataTrendByTime() {
      this.loading = true
      getAction(this.url.getDataTrendByTime)
        .then((res) => {
          if (res.code == 200) {
            if (res.result) {
              this.chartData = res.result.SNMP
              this.$nextTick(() => {
                this.initData(this.chartData)
              })
            }
          }
          this.loading = false
        })
        .catch((err) => {
          this.$message.warning(err.message)
          this.loading = false
        })
    },
    // 初始化图表数据
    initData(data) {
      if (!data || (data.allSpeed.length <= 0 && data.inSpeed.length <= 0 && data.outSpeed.length <= 0)) {
        return
      }

      this.myChart = this.$echarts.init(this.$refs.baseEcharts)
      this.myChart.clear()

      let option = {
        grid: {
          top: '30%',
          left: '6%',
          right: '10px',
          bottom: 0,
          containLabel: true,
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
          formatter: this.formatTooltip,
        },
        legend: {
          top: '10%',
          right: '10px',
          itemWidth: 14,
          itemHeight: 7,
          textStyle: {
            color: 'rgba(255, 255, 255, 0.8)',
            fontSize: this.fontSizeObject.legendFontSize,
          },
        },
        xAxis: {
          type: 'category',
          boundaryGap: true,
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed',
              color: 'rgba(235, 241, 249, 0.25)',
              width: 1,
            },
          },
          axisLabel: {
            textStyle: {
              color: 'rgba(255, 255, 255, 0.8)',
              fontSize: this.fontSizeObject.xAxisFontSize,
            },
          },
          axisTick: {
            show: false,
          },
        },
        yAxis: {
          type: 'value',
          axisTick: {
            show: true,
          },
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed',
              color: 'rgba(235, 241, 249, 0.25)',
              width: 1,
            },
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.4)',
            },
          },
          axisLabel: {
            textStyle: {
              color: 'rgba(255, 255, 255, 0.8)',
              fontSize: this.fontSizeObject.yAxisFontSize,
            },
          },
        },
        dataZoom: [
          {
            xAxisIndex: [0],
            show: false,
            start: 0,
            endValue: 30,
            realtime: true,
          },
          {
            type: 'inside',
            xAxisIndex: 0,
            zoomOnMouseWheel: true,
            moveOnMouseMove: true,
            moveOnMouseWheel: true,
          },
        ],
        series: [],
      }

      if (this.showType === 0) {
        // 总速率
        this.setTotalSpeedOption(option, data.allSpeed)
      } else {
        // 上下行速率
        this.setInOutSpeedOption(option, data.inSpeed, data.outSpeed)
      }

      this.myChart.setOption(option)
      window.addEventListener('resize', () => {
        this.myChart.resize()
      })
    },
    // 设置总速率图表选项
    setTotalSpeedOption(option, allSpeedData) {
      if (!allSpeedData || allSpeedData.length === 0) return
      // 获取x轴数据
      const xData = this.getXData(allSpeedData)
      // 获取y轴数据
      const yData = allSpeedData.map((item) => item.value)
      // 获取单位
      const unit = this.getUnit(allSpeedData)

      option.xAxis.data = xData
      option.yAxis.axisLabel.formatter = `{value}`

      option.series = [
        {
          data: yData,
          name: '总速率',
          type: 'line',
          smooth: false,
          showAllSymbol: true,
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(88, 238, 255, 0.3)',
              },
              {
                offset: 1,
                color: 'rgba(47,228,255,0)',
              },
            ]),
          },
          lineStyle: {
            color: 'rgba(88, 238, 255, 1)',
          },
          itemStyle: {
            normal: {
              color: '#a3ffff',
              borderColor: 'rgba(58,65,70,0.58)',
              borderWidth: 8,
            },
          },
          symbol: 'circle',
          symbolSize: [16, 16],
        },
      ]
    },
    // 设置上下行速率图表选项
    setInOutSpeedOption(option, inSpeedData, outSpeedData) {
      const hasInData = inSpeedData && inSpeedData.length > 0
      const hasOutData = outSpeedData && outSpeedData.length > 0

      if (!hasInData && !hasOutData) return

      // 确定使用哪个数据集作为x轴和单位的基础数据
      const primaryData = hasInData ? inSpeedData : outSpeedData
      const xData = this.getXData(primaryData)
      const unit = this.getUnit(primaryData)

      option.xAxis.data = xData
      option.yAxis.axisLabel.formatter = `{value}`

      option.series = []
      // 添加上行速率系列
      if (hasInData) {
        option.series.push({
          data: inSpeedData.map((item) => item.value),
          name: '上行速率',
          type: 'line',
          smooth: false,
          showAllSymbol: true,
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(0, 255, 157, 0.3)' },
              { offset: 1, color: 'rgba(0, 255, 157, 0)' },
            ]),
          },
          lineStyle: {
            color: 'rgba(0, 255, 157, 1)',
          },
          itemStyle: {
            normal: {
              color: '#00ff9d',
              borderColor: 'rgba(58,65,70,0.58)',
              borderWidth: 8,
            },
          },
          symbol: 'circle',
          symbolSize: [16, 16],
        })
      }
      // 添加下行速率系列
      if (hasOutData) {
        option.series.push({
          data: outSpeedData.map((item) => item.value),
          name: '下行速率',
          type: 'line',
          smooth: false,
          showAllSymbol: true,
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(255, 107, 107, 0.3)' },
              { offset: 1, color: 'rgba(255, 107, 107, 0)' },
            ]),
          },
          lineStyle: {
            color: 'rgba(255, 107, 107, 1)',
          },
          itemStyle: {
            normal: {
              color: '#ff6b6b',
              borderColor: 'rgba(58,65,70,0.58)',
              borderWidth: 8,
            },
          },
          symbol: 'circle',
          symbolSize: [16, 16],
        })
      }
    },
    // 获取x轴数据（时间）
    getXData(data) {
      if (!data || data.length === 0) return []
      const slicedData = data.length > 7 ? data.slice(data.length - 7) : data
      return slicedData.map((item) => item.time && item.time.substring(5, 10))
    },
    // 获取单位
    getUnit(data) {
      if (!data || data.length === 0) return ''
      const itemWithUnit = data.find((item) => item.value && item.value.unit)
      return itemWithUnit ? itemWithUnit.value.unit : ''
    },
    // 格式化tooltip显示内容
    formatTooltip(params) {
      let result = `${params[0].name}<br>`
      params.forEach((param) => {
        const value = param.value
        if (value !== '' && value !== null && value !== undefined) {
          const unit = param.data.unit || ''
          result += `${param.marker} ${param.seriesName}：${value} ${unit}<br>`
        } else {
          result += `${param.marker} ${param.seriesName}：无数据<br>`
        }
      })
      return result
    },
  },
}
</script>

<style scoped lang="less">
.empty-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
::v-deep {
  .ant-empty-description {
    color: #fff !important;
  }
}
</style>
