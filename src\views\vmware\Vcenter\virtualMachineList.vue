<template>
  <div :class="['table-container']">
    <!-- table区域-begin -->
    <a-table style='margin-right: 1px' ref='table' :columns='columns' :dataSource='dataSource' :loading='loading'
      :pagination='ipagination' :rowKey='(record,index)=>{return index}'
      :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}" bordered @change='handleTableChange'>
      <template slot='tooltip' slot-scope='text'>
        <a-tooltip :title='text' placement='topLeft' trigger='hover'>
          <div class='tooltip'>
            {{ text }}
          </div>
        </a-tooltip>
      </template>
      <!-- 字符串超长截取省略号显示-->
      <span slot='templateContent' slot-scope='text'>
        <j-ellipsis :length='25' :value='text' />
      </span>
      <template slot="cpuTotal" slot-scope='text'>
        <span>{{ (text/1000).toFixed(2) }}</span>
      </template>
      <template slot="memTotal" slot-scope='text'>
        <span>{{ (text/1000).toFixed(2) }}</span>
      </template>
      <template slot='powerState' slot-scope='text'>
        <div v-if="text == 'POWERED_ON'" style="background-color: #569B2E;color: white;border-radius: 5px;">
          在线
        </div>
        <div v-else style="background-color: #C9C9C9;color: white;border-radius: 5px;">离线</div>
      </template>

      <span slot='action' slot-scope='text, record'>
        <span @click="powerAction(record,'POWERED_OFF','PowerOnVM')" :style='{color:powerStyle(record,"POWERED_OFF")}'
          class="actionHead">
          开机
        </span>
        <a-divider type='vertical' />
        <span @click="powerAction(record,'POWERED_ON','PowerOffVM')" :style='{color:powerStyle(record,"POWERED_ON")}'
          class="actionHead">
          关机
          <a-divider type='vertical' />
        </span>
        <span @click="powerAction(record,'POWERED_ON','ResetVM')" :style='{color:powerStyle(record,"POWERED_ON")}'
          class="actionHead">
          重启
        </span>
      </span>
    </a-table>
  </div>
</template>

<script>
import {tableOperationColumnVisibility} from '@/mixins/tableOperationColumnVisibility'
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import JEllipsis from '@/components/jeecg/JEllipsis'
  import {
    getAction,
    postAction
  } from '@/api/manage'
  import yqIcon from '@comp/tools/SvgIcon'
  export default {
    name: 'virtualMachineList',
    mixins: [JeecgListMixin, tableOperationColumnVisibility],
    components: {
      JEllipsis,
      yqIcon
    },
    data() {
      return {
        deviceInfo: {},
        // 表头
        columns: [{
          title: '序号',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function(t, r, index) {
            return parseInt(index) + 1
          },
        }, {
          title: '名称',
          dataIndex: 'name',
          customCell: () => {
            let cellStyle = 'text-align: center'
            return {
              style: cellStyle
            }
          }
        },
          {
            title: 'CPU使用率(%)',
            dataIndex: 'cpuUsage',
            customCell: () => {
              let cellStyle = 'text-align: right;min-width: 90px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: 'CPU总量(GHz)',
            dataIndex: 'cpuTotal',
            customCell: () => {
              let cellStyle = 'text-align: right;min-width: 90px'
              return {
                style: cellStyle
              }
            },
            scopedSlots: {
              customRender: 'cpuTotal'
            }
          },
          {
            title: '内存使用率(%)',
            dataIndex: 'memUsage',
            customCell: () => {
              let cellStyle = 'text-align: right;min-width: 90px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '内存总量(GB)',
            dataIndex: 'memTotal',
            customCell: () => {
              let cellStyle = 'text-align: right;min-width: 90px'
              return {
                style: cellStyle
              }
            },
            scopedSlots: {
              customRender: 'memTotal'
            }
          },
          {
            title: '状态',
            dataIndex: 'powerState',
            customCell: () => {
              let cellStyle = 'text-align: center;width: 80px'
              return {
                style: cellStyle
              }
            },
            scopedSlots: {
              customRender: 'powerState'
            }
          },
          {
            title: '开机时间',
            dataIndex: 'runTime',
            scopedSlots: {
              customRender: 'tooltip'
            },
            customCell: () => {
              let cellStyle = 'text-align: center;width: 180px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '操作',
            dataIndex: 'action',
            fixed: 'right',
            width: 150,
            align: 'center',
            scopedSlots: {
              customRender: 'action'
            }
          }
        ],
        url: {
          list: '/device/deviceInfo/vmList',
          execute: '/device/deviceInfo/execute',
          vmwareInfo: '/alarm/alarmTemplate/getVmwareInfo',
        },
        queryParam: {
          createTimeRange: [],
          keyWord: ''
        },
        disableMixinCreated: true,
      }
    },
    created() {
      this.setTabelOperationCol('operationColumnVisibility', 'virtualMachineOperVis')
    },
    methods: {
      show(data, record) {
        this.deviceInfo = record
        this.queryParam.deviceId = record.id
        this.queryParam.type = data.type
        this.queryParam.title = data.title
        this.loadData(1)
      },
      handleDetailPage: function(record) {
        this.$parent.pButton2(1, record)
      },
      loadData(arg) {
        if (!this.url.list) {
          this.$message.error('请设置url.list属性!')
          return
        }
        //加载数据 若传入参数1则加载第一页的内容
        if (arg === 1) {
          this.ipagination.current = 1
        }
        var params = this.getQueryParams() //查询条件
        this.loading = true
        getAction(this.url.list, params).then((res) => {
          if (res.success) {
            let data = res.result.records || res.result
            if (data.length > 0) {
              data.map((item, index) => {
                item.loading = false
                item.rowIndex = 'row_' + index
                item.powerIcon = item.powerState == 'POWERED_ON' ? 'on_status' : 'off_status'
                item.powerColor = item.powerState == 'POWERED_ON' ? '#1dff02' : '#3b3a3a'
              })
            }
            this.dataSource = data
            this.ipagination.total = data.length
          }
          if (res.code === 510) {
            this.$message.warning(res.message)
          }
          this.loading = false
        })
      },
      refreshVmware(record, message) {
        getAction(this.url.vmwareInfo, {
          deviceId: this.data.id,
          ip: this.data.ip,
          vmwareName: record.vmName
        }).then((res) => {
          if (res.success) {
            if (res.result.vmwareinfo.length > 0) {
              let data = res.result.vmwareinfo
              if (data[0].value === record.vmName) {
                this.$message.success(message)
                record.ip = data[1].value ? data[1].value : ''
                record.vmwareCpuUsage = data[2].value ? data[2].value : ''
                record.vmwareMemRate = data[3].value ? data[3].value : ''
                record.powerState = data[4].value
                record.runTime = data[5].value ? data[5].value : ''
                record.powerIcon = record.powerState == 'POWERED_ON' ? 'on_status' : 'off_status'
                record.powerColor = record.powerState == 'POWERED_ON' ? '#1dff02' : '#3b3a3a'
                record.loading = false
                return
              }
            }
          }
        }).catch((err) => {
          this.$message.error(err + "")
          this.$message.warning('发生异常，请稍后尝试刷新页面或重新操作')
          record.loading = false
        })
      },
      submitForm(record, actionState) {
        var unixtime = new Date().getTime()
        const that = this
        record.loading = true
        let formData = {}
        formData.deviceId = this.queryParam.deviceId
        formData.methodName = actionState
        formData.vmwareName = record.name
        formData.transferProtocol = 'Vmware'
        this.info()
        postAction(this.url.execute, formData)
          .then((res) => {
            if (res.success && res.result.indexOf('成功') !== -1) {
              //that.$message.success(res.result)
              //命令执行完毕后，后需要一段时间从虚拟主机中获取数据，所以加了定时
              setTimeout(() => {
                this.refreshVmware(record, res.result)
              }, 5000)
            } else {
              record.loading = false
              that.$message.warning(res.message)
            }
          }).catch((err) => {
          this.$message.error(err + "")
          this.$message.warning('发生异常，请稍后尝试刷新页面或重新操作')
          record.loading = false
        })
      },
      info() {
        const h = this.$createElement;
        const modal = this.$info({
          title: '提示',
          content: h('div', {}, [h('p', '命令已开始执行，请耐心等待！')]),
          okButtonProps: {
            style: {
              display: 'none'
            }
          },
        });
        setTimeout(() => {
          modal.destroy();
        }, 2000);
      },
      powerStyle(record, powerState) {
        if (record.powerState === powerState) {
          return '#409eff !important'
        }
        return "#C9C9C9"
      },
      powerAction(record, powerState, actionState) {
        if (record.powerState === powerState) {
          this.submitForm(record, actionState)
        }
      },
    }
  }
</script>
<style lang='less' scoped>
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';

  .actionHead {
    cursor: pointer;
  }

  /** Button按钮间距 */
  .ant-btn {
    margin-left: 3px;
  }

  .ant-card-body .table-operator {
    margin-bottom: 18px;
  }

  .ant-table-tbody .ant-table-row td {
    padding-top: 15px;
    padding-bottom: 15px;
  }

  .anty-row-operator button {
    margin: 0 5px;
  }

  .ant-btn-danger {
    background-color: #ffffff;
  }

  .ant-modal-cust-warp {
    height: 100%;
  }

  .ant-modal-cust-warp .ant-modal-body {
    height: calc(100% - 110px) !important;
    overflow-y: auto;
  }

  .ant-modal-cust-warp .ant-modal-content {
    height: 90% !important;
    // overflow-y: hidden;
  }

  .table-page-search-wrapper {
    background-color: #fff;
    padding: 15px 0 0 15px;
  }

  .table-container {
    background-color: #fff;
    padding-right: 24px;
  }

  .query-btn {
    background: #ecf5ff;
    border: 1px solid #b3d8ff;
    border-radius: 4px;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #409eff;
    width: 73px;
    height: 28px;
    cursor: pointer;
    margin: 0px;
  }

  .yq-icon {
    font-size: 24px;
  }
</style>