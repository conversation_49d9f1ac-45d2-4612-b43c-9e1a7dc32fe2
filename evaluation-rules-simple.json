{"list": [{"type": "batch", "label": "动态表格（外层下拉框+内层下拉框）", "list": [{"type": "select", "label": "外层下拉框", "icon": "icon-xiala", "options": {"width": "100%", "tableWidth": "150px", "multiple": false, "disabled": false, "clearable": false, "hidden": false, "placeholder": "请选择外层选项", "linkageData": "[]", "changeFunc": "function(value, key, vm, http) {\n  console.log('外层下拉框值变化:', value, key);\n  \n  // 定义内层下拉框的数据映射\n  const innerDataMap = {\n    'category1': [\n      { value: 'item1_1', label: '类别1-项目1' },\n      { value: 'item1_2', label: '类别1-项目2' },\n      { value: 'item1_3', label: '类别1-项目3' }\n    ],\n    'category2': [\n      { value: 'item2_1', label: '类别2-项目1' },\n      { value: 'item2_2', label: '类别2-项目2' },\n      { value: 'item2_3', label: '类别2-项目3' }\n    ],\n    'category3': [\n      { value: 'item3_1', label: '类别3-项目1' },\n      { value: 'item3_2', label: '类别3-项目2' }\n    ]\n  };\n  \n  // 获取当前行的内层下拉框key\n  const innerSelectKey = key.replace('outer_select', 'inner_select');\n  console.log('内层下拉框key:', innerSelectKey);\n  \n  // 更新当前行的内层下拉框选项\n  if (value && innerDataMap[value]) {\n    try {\n      // 获取batch组件\n      const batchVm = vm.$parent;\n      console.log('batch组件:', batchVm);\n      \n      if (batchVm && batchVm.dataSources) {\n        // 更新内层下拉框的数据源\n        batchVm.dataSources[innerSelectKey] = [...innerDataMap[value]];\n        \n        // 强制更新batch组件\n        batchVm.$forceUpdate();\n        \n        console.log('内层下拉框数据已更新:', innerSelectKey, innerDataMap[value]);\n      } else {\n        console.warn('未找到batch组件或dataSources');\n      }\n    } catch (error) {\n      console.error('更新内层下拉框数据时出错:', error);\n    }\n  } else if (!value) {\n    // 重置内层下拉框\n    try {\n      const batchVm = vm.$parent;\n      if (batchVm && batchVm.dataSources) {\n        batchVm.dataSources[innerSelectKey] = [{ value: '', label: '请先选择外层选项' }];\n        batchVm.$forceUpdate();\n        console.log('内层下拉框已重置:', innerSelectKey);\n      }\n    } catch (error) {\n      console.error('重置内层下拉框时出错:', error);\n    }\n  }\n}", "dynamicKey": "", "dynamic": "static", "ajaxData": {"url": "", "type": "GET", "params": "{}", "header": "{}", "callFunc": "function(res){\n          return res.data;\n        }"}, "options": [{"value": "category1", "label": "类别1"}, {"value": "category2", "label": "类别2"}, {"value": "category3", "label": "类别3"}], "staticOptions": [{"value": "category1", "label": "类别1"}, {"value": "category2", "label": "类别2"}, {"value": "category3", "label": "类别3"}], "showSearch": false, "isEvaluationField": false, "showLabel": true}, "model": "outer_select", "key": "outer_select", "help": "", "rules": [{"required": false, "message": "必填项"}]}, {"type": "select", "label": "内层下拉框", "icon": "icon-xiala", "options": {"width": "100%", "tableWidth": "150px", "multiple": false, "disabled": false, "clearable": false, "hidden": false, "placeholder": "请先选择外层选项", "linkageData": "[]", "changeFunc": "function(value, key, vm, http) {\n  console.log('内层下拉框值变化:', value, key);\n}", "dynamicKey": "", "dynamic": "static", "ajaxData": {"url": "", "type": "GET", "params": "{}", "header": "{}", "callFunc": "function(res){\n          return res.data;\n        }"}, "options": [{"value": "", "label": "请先选择外层选项"}], "staticOptions": [{"value": "", "label": "请先选择外层选项"}], "showSearch": false, "isEvaluationField": false, "showLabel": true}, "model": "inner_select", "key": "inner_select", "help": "", "rules": [{"required": false, "message": "必填项"}]}], "options": {"scrollY": 0, "disabled": false, "hidden": false, "showLabel": true, "hideSequence": false, "width": "100%", "rowKey": "id", "hideAddBtn": false, "hideOprCol": false, "linkageData": "[]", "changeFunc": "function(value, key, vm, http) {\n  console.log('动态表格数据变化:', value, key);\n}"}, "model": "batch_table", "key": "batch_table", "help": ""}], "config": {"layout": "horizontal", "labelCol": {"xs": 4, "sm": 4, "md": 4, "lg": 4, "xl": 4, "xxl": 4}, "labelWidth": 100, "labelLayout": "flex", "wrapperCol": {"xs": 18, "sm": 18, "md": 18, "lg": 18, "xl": 18, "xxl": 18}, "hideRequiredMark": false, "customStyle": ""}}