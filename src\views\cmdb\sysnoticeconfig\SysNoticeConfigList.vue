<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll zxw">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <!-- 查询区域 -->
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="通知名称">
                  <a-input
                    placeholder="请输入通知名称"
                    :allowClear="true"
                    autocomplete="off"
                    v-model="queryParam.configName" :maxLength="50"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="colBtnsSpan()">
                <span
                  class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                >
                  <a-button type="primary" @click="searchQuery" class="btn-search-style">查询</a-button>
                  <a-button @click="searchReset" style="margin-left: 10px" class="btn-reset-style">重置</a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
        <!-- 查询区域-END -->
      </a-card>
      <a-card :bordered="false" style="flex: auto" class="core">
        <!-- 操作按钮区域 -->
        <div class="table-operator">
          <a-button @click="handleAdd">新增</a-button>
          <a-dropdown v-if="selectedRowKeys.length > 0">
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item key="1" @click="batchDel">删除</a-menu-item>
            </a-menu>
            <a-button> 批量操作 <a-icon type="down"/></a-button>
          </a-dropdown>
        </div>
        <!-- table区域-begin -->
        <div>
          <a-table
            ref="table"
            bordered
            rowKey="id"
            :columns="columns"
            :dataSource="dataSource"
            :pagination="ipagination"
            :loading="loading"
            :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
            class="j-table-force-nowrap"
            @change="handleTableChange"
          >
            <template slot="htmlSlot" slot-scope="text">
              <div v-html="text"></div>
            </template>
            <template slot="imgSlot" slot-scope="text">
              <span v-if="!text" style="font-size: 14px; font-style: italic">无图片</span>
              <img
                v-else
                :src="getImgView(text)"
                height="25px"
                alt=""
                style="max-width: 80px; font-size: 14px; font-style: italic"
              />
            </template>
            <template slot="fileSlot" slot-scope="text">
              <span v-if="!text" style="font-size: 14px; font-style: italic">无文件</span>
              <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
                下载
              </a-button>
            </template>
            <span slot="action" slot-scope="text, record" class="caozuo">
              <a @click="handleDetail(record)">查看</a>
              <a-divider type="vertical" />
              <a @click="handleEdit1(record)">编辑</a>
              <a-divider type="vertical" />
              <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                <a>删除</a>
              </a-popconfirm>
            </span>
            <template slot="tooltip" slot-scope="text">
              <a-tooltip placement="topLeft" :title="text" trigger="hover">
                <div class="tooltip">
                  {{ text }}
                </div>
              </a-tooltip>
            </template>
          </a-table>
        </div>
        <sys-notice-config-modal ref="modalForm" @ok="modalFormOk"></sys-notice-config-modal>
      </a-card>
    </a-col>
  </a-row>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import SysNoticeConfigModal from './modules/SysNoticeConfigModal'
import JSuperQuery from '@/components/jeecg/JSuperQuery.vue'
import '@/assets/less/TableExpand.less'
import JDictSelectTag from '@/components/dict/JDictSelectTag.vue'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'

export default {
  name: 'SysNoticeConfigList',
  mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
  components: {
    SysNoticeConfigModal,
    JSuperQuery,
    JDictSelectTag,
  },
  data() {
    return {
      formItemLayout: {
        labelCol: {
          style: 'width:70px',
        },
        wrapperCol: {
          style: 'width:calc(100% - 70px)'
        }
      },
      description: '通知管理页面',
      // 表头
      columns: [
        {
          title: '通知配置编码',
          dataIndex: 'id',
        },
        {
          title: '通知名称',
          dataIndex: 'configName',
        },
        {
          title: '通知类型',
          dataIndex: 'configType_dictText',
          scopedSlots: { customRender: 'status' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          fixed: 'right',
          align: 'center',
          width: 180,
          scopedSlots: { customRender: 'action' },
        },
      ],
      url: {
        list: '/sys/notice/config/list',
        delete: '/sys/notice/config/delete',
        deleteBatch: '/sys/notice/config/deleteBatch',
        exportXlsUrl: '/sys/notice/config/exportXls',
        importExcelUrl: '/sys/notice/config/importExcel',
      },
      dictOptions: {},
      superFieldList: [],
    }
  },
  created() {
    this.getSuperFieldList()
  },
  mounted() {},
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
  },
  methods: {
    handleDetails: function (record) {
      this.$refs.sysNoticeConfigDetail.edit(record)
      this.$refs.sysNoticeConfigDetail.title = '详情'
      this.$refs.sysNoticeConfigDetail.disableSubmit = true
    },
    initDictConfig() {},
    getSuperFieldList() {
      let fieldList = []
      fieldList.push({ type: 'string', value: 'name', text: '名称', dictCode: '' })
      fieldList.push({
        type: 'string',
        value: 'type',
        text: '类型,email:邮箱,dingding_robot:钉钉机器人,stationNotice:站内通知',
        dictCode: '',
      })
      this.superFieldList = fieldList
    },
    handleEdit1: function (record) {
      this.$refs.modalForm.edit(record)
      this.$refs.modalForm.title = '编辑'
      this.$refs.modalForm.disableSubmit = false
    },
  },
}
</script>
<style lang="less" scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
::v-deep .ant-table-thead > tr > th {
  text-align: center;
  white-space: nowrap;
}
::v-deep .ant-table-tbody > tr > td {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;

  &:nth-child(2) {
    text-align: center;
  }
  &:nth-child(4),
  &:nth-child(3) {
    text-align: left;
  }
}
</style>