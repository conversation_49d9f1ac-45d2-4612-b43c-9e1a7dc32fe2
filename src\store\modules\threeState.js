const moduleThree ={
    namespaced: true,
    state:()=>{
        return{
            isEditor:true,
            sceneName:'',
            isGroup:false,
            bgConfig:null,
        }
    },
    mutations: {
        changeEditorStatus (state,status) {
          state.isEditor = status
        },
        changeSceneName(state,name){
          state.sceneName = name;
        },
        MODELGROUP(state,bool){
            state.isGroup = bool
        },
        SET_BG_CONGIG(state,config){
          // console.log("改变状态管理值 == ",config)
          state.bgConfig = config;
        }
      },
}
export default moduleThree