<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="12">
            <a-form-item label="驱动名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['driveName',validatorRules.driveName]" placeholder="请输入驱动名称" :allowClear="true"
                autocomplete="off" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="驱动版本" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['driveVersion',validatorRules.driveVersion]" placeholder="请输入驱动版本(必须是数字)"
                :allowClear="true" autocomplete="off">
              </a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="操作系统" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-select v-decorator="['driveOs',validatorRules.driveOs]" :allowClear="true" placeholder="请选择操作系统">
                <a-select-option v-for="(item, key) in dictOptions" :key="key" :value="item.value">
                  {{ item.text }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="cpu架构" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-select v-decorator="['driveCpu',validatorRules.driveCpu]" :allowClear="true" placeholder="请选择cpu架构">
                <a-select-option v-for="(item, key) in cpuList" :key="key" :value="item.value">
                  {{ item.text }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12" style="height:70px">
            <a-form-item label="驱动文件" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-upload
                v-decorator="['driveFile', validatorRules.driveFile]"
                :multiple="false"
                :number="1"
                :bizPath="bizPath"
                :accept="accept"
                :upload-action="fildUrl+'/kbase/knowledges/template/upload2Minio'"
                ></j-upload>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="描述" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-textarea
                :autoSize="{ minRows: 1, maxRows: 4 }"
                v-decorator="['driveDescribe', validatorRules.driveDescribe]"
                placeholder="请输入描述"
                :allowClear="true"
                autocomplete="off" />
            </a-form-item>
          </a-col>
          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
  import {
    httpAction,
    getAction
  } from '@/api/manage'
  import pick from 'lodash.pick'
  import {
    validateDuplicateValue
  } from '@/utils/util'
  import JFormContainer from '@/components/jeecg/JFormContainer'
  import JUpload from '@/components/jeecg/JUpload'
  import JDictSelectTag from "@/components/dict/JDictSelectTag"
  import {
    ajaxGetAreaItems,
    ajaxGetDictItems,
    getDictItemsFromCache
  } from '@/api/api'
  import wordUpload from '@views/opmg/knowledgeManagement/knowledgeBase/modules/WordUpload.vue'
  import fa from 'element-ui/src/locale/lang/fa'

  export default {
    name: 'driveForm',
    components: {
      wordUpload,
      JFormContainer,
      JUpload,
      JDictSelectTag,
    },
    props: {
      //流程表单data
      formData: {
        type: Object,
        default: () => {},
        required: false
      },
      //表单模式：true流程表单 false普通表单
      formBpm: {
        type: Boolean,
        default: false,
        required: false
      },
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data() {
      return {
        form: this.$form.createForm(this),
        model: {},
        dictOptions: [],
        cpuList: [],
        fildUrl: window._CONFIG['domianURL'],
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 6
          },
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 17
          },
        },
        bizPath: 'drivePackage',
        confirmLoading: false,
        accept:'',
        driveFileNameSuffix: [
          '.tar',
          '.tbz',
          '.tgz',
          '.zip',
          '.rar',
          '.jar',
          '.class',
          '.java',
          '.c',
          '.cpp',
          '.h',
          '.so',
          '.7z',
          '.rpm',
          '.deb',
        ],
        validatorRules: {
          driveName: {
            rules: [{
              required: true,
              message: '请输入驱动名称!'
            },
              {
                max: 50,
                message: '驱动名称长度不应超过 50 个字符!'
              }]
          },
          driveVersion: {
            rules: [{
                required: true,
                message: '请输入驱动版本!'
              },
              {
                // pattern: /(^[1-9][0-9]*$)|(^(([1-9][0-9]*)+\.)+(([1-9][0-9]*)+)$)/,//限制第一位不能为0
                pattern: /(^[0-9]*$)|(^([0-9]+\.)+([0-9]+)$)/,//对第一位没有限制，只要是数字就行
                message: '请输入正确的驱动版本,如：xxx或者x.xx.x'
              }
            ]
          },
          driveOs: {
            rules: [{
              required: true,
              message: '请选择操作系统!'
            }]
          },
          driveCpu: {
            rules: [{
              required: true,
              message: '请选择cpu架构!'
            }]
          },
          driveFile: {
            rules: [{
              required: true,
              message: '请上传驱动文件!'
            }]
          },
          driveDescribe: {
            rules: [{
              required: false,
              max:255,
              message: '描述长度不应超过 255 个字符!'
            }]
          },
        },
        url: {
          add: "/drive/driveInfo/add",
          edit: "/drive/driveInfo/edit",
          queryById: "/drive/driveInfo/queryById"
        }
      }
    },
    computed: {
      formDisabled() {
        if (this.formBpm === true) {
          if (this.formData.disabled === false) {
            return false
          }
          return true
        }
        return this.disabled
      },
      showFlowSubmitButton() {
        if (this.formBpm === true) {
          if (this.formData.disabled === false) {
            return true
          }
        }
        return false
      }
    },
    created() {
      this.accept=this.driveFileNameSuffix.join(",")
      //如果是流程中表单，则需要加载流程表单data
      this.showFlowData();
    },
    mounted() {
      this.initDictData()
    },
    methods: {
      initDictData() {
        //根据字典Code, 初始化字典数组
        ajaxGetDictItems('cpuArch', null).then((res) => {
          if (res.success) {
            this.cpuList = res.result
          }
        })
        ajaxGetDictItems('os_type', null).then((res) => {
          if (res.success) {
            this.dictOptions = res.result
          }
        })
      },
      add() {
        this.edit({});
      },
      edit(record) {
        this.form.resetFields();
        this.model = Object.assign({}, record);
        this.visible = true;
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.model, 'driveName', 'driveVersion', 'driveOs', 'driveCpu',
            'driveFile', 'driveDescribe'))
        })
      },
      //渲染流程表单数据
      showFlowData() {
        if (this.formBpm === true) {
          let params = {
            id: this.formData.dataId
          };
          getAction(this.url.queryById, params).then((res) => {
            if (res.success) {
              this.edit(res.result);
            }
          });
        }
      },
      submitForm() {
        const that = this;
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if (!this.model.id) {
              httpurl += this.url.add;
              method = 'post';
            } else {
              httpurl += this.url.edit;
              method = 'put';
            }

            //进行驱动文件的筛选
            if ('' != values.driveFile && null != values.driveFile && undefined != values.driveFile) {
              var f = true
              for (var i = 0; i < that.driveFileNameSuffix.length; i++) {
                var suffix = that.driveFileNameSuffix[i]
                var index = values.driveFile.lastIndexOf('.')
                var fileSuffix = values.driveFile.substring(index, values.driveFile.length)
                if (that.driveFileNameSuffix[i] == fileSuffix || index == -1) {
                  f = false
                }
              }
              if (f) {
                var str = ''
                for (var i = 0; i < that.driveFileNameSuffix.length; i++) {
                  str = str + '[' + that.driveFileNameSuffix[i] + ']' + '、'
                }
                that.$message.error('请选择以' + str.substring(0, str.length - 1) + '为后缀的驱动文件')
                // that.updateModelFileName()
                that.confirmLoading = false
                return
              }
            }

            let formData = Object.assign(this.model, values);
            httpAction(httpurl, formData, method).then((res) => {
              if (res.success) {
                that.$message.success(res.message);
                that.$emit('ok');
              } else {
                that.$message.warning(res.message);
              }
              that.confirmLoading = false;
            }).catch((err) => {
              that.$message.warning(err.message);
              that.confirmLoading = false;
            })
          }

        })
      },
      popupCallback(row) {
        this.form.setFieldsValue(pick(row, 'driveName', 'driveVersion', 'driveOs', 'driveCpu',
          'driveFile', 'driveDescribe'))
      },
      updateModelFileName() {
        this.model.driveFile = null
        this.form.setFieldsValue(pick(this.model, 'driveName', 'driveVersion', 'driveOs', 'driveCpu',
          'driveFile', 'driveDescribe'))
      },
    }
  }
</script>