<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form='form' slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-item label="关系类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-select :allowClear='true' :getPopupContainer='(node) => node.parentNode' placeholder="请选择关系类型"
                v-decorator="['relationName', validatorRules.relationName]" @change="selectRelationType($event)">
                <a-select-option v-for="coupon in relationList" :key="coupon.id" :value="coupon.id">{{coupon.relation}}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <!-- <a-form-item v-show="false" :wrapper-col="{ span: 14, offset: 4 }">
            <a-button type="primary" @click="submitForm">
              Create
            </a-button>
          </a-form-model-item> -->
          <!-- <br />
          <br />
          <hr /> -->

          <a-col :span="24">
            <a-form-item label="资产类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-tree-select-expand ref="treeSelect" placeholder="请选择资产类型" v-model="assetsCategoryId"
                dict="cmdb_assets_category,category_name,id" condition='{"delflag":0}' pidField="parent_id" pidValue="0"
                hasChildField="has_child" @change="selectAssetsCategory($event)">
              </j-tree-select-expand>
            </a-form-item>
          </a-col>

          <a-col :span='24' style="height: 100%">
            <!-- table区域-begin -->
            <div class="div-table-container" style="margin-bottom: 8px">
              <div class="table-operator"></div>
              <a-table ref="table" bordered rowKey="id" :columns="columns" :dataSource="dataSource"
                :scroll='dataSource.length>0?{x:"max-content"}:{}' :loading="loading"
                :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
                @change="handleTableChange">
              </a-table>
            </div>
            <!-- </a-card> -->
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
  import {
    httpAction,
    getAction
  } from '@/api/manage'
  import pick from 'lodash.pick'
  import {
    mixinDevice
  } from '@/utils/mixin'
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import {
    checkPhone,
    filterObj,
    validateDuplicateValue
  } from '@/utils/util'
  import JFormContainer from '@/components/jeecg/JFormContainer'
  import JSuperQuery from '@/components/jeecg/JSuperQuery.vue'
  export default {
    name: 'addAests',
    mixins: [JeecgListMixin, mixinDevice],
    components: {
      JFormContainer,
      JSuperQuery,
    },
    props: {
      //流程表单data
      formData: {
        type: Object,
        default: () => {},
        required: false,
      },
      //表单模式：true流程表单 false普通表单
      formBpm: {
        type: Boolean,
        default: false,
        required: false,
      },
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false,
      },
    },
    data() {
      return {
        form: this.$form.createForm(this),
        //查询条件，此参数名称与JeecgListMixin模块参数一致
        queryParam: {
          //查询参数
          name: '',
          id: '',
        },
        // 表头
        columns: [{
            title: '资产编号',
            align: 'center',
            dataIndex: 'assetsCode',
          },
          {
            title: '资产名称',
            align: 'center',
            dataIndex: 'assetsName',
          },
          {
            title: '资产类型',
            align: 'center',
            dataIndex: 'categoryName',
          },
          {
            title: '供应商名称',
            align: 'center',
            dataIndex: 'producerName',
          },
          {
            title: '型号',
            align: 'center',
            dataIndex: 'assetsModel',
          },
        ],
        confirmLoading: false,
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          },
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          },
        },
        selectedAssetsIds: '',
        assetsIdOne: '',
        relationId: '',
        assetsId: '',
        relationList: [],
        assetsCategoryId: '',
        validatorRules: {
          relationName: {
            rules: [{
              required: true,
              message: '请选择关系类型!',
              trigger: 'blur'
            }]
          },
        },
        url: {
          //list: '/assets/assets/findAll',
          list: '/assets/assets/findAssets4Relation',
          allRelations: '/relation/cmdbRelation/findAll',
          addRelation: '/assets/assets/relationAdd',
        },
        disableMixinCreated: true
      }
    },
    computed: {
      formDisabled() {
        if (this.formBpm === true) {
          if (this.formData.disabled === false) {
            return false
          }
          return true
        }
        return this.disabled
      },
      showFlowSubmitButton() {
        if (this.formBpm === true) {
          if (this.formData.disabled === false) {
            return true
          }
        }
        return false
      },
    },
    created() {
      this.getAllRelationType()
      // this.list()
    },
    methods: {
      selectAssetsCategory(e) {
        this.queryParam.assetsCategoryId = e
        this.loadData()
      },
      //选择关系类型
      selectRelationType(e) {
        this.noticeConfigid = e
      },
      //获取关系下拉数据
      getAllRelationType() {
        getAction(this.url.allRelations).then((res) => {
          if (res.success) {
            this.relationList = res.result
          }
        })
      },
      getNoticeConfig(e) {
        getAction(this.url.allRelations).then((res) => {
          if (res.success) {
            this.NoticeConfig = res.result
          }
        })
      },
      show(assetsId, data) {
        this.assetsId = assetsId
        this.queryParam.assetsId = assetsId
        this.queryParam.assetsIgnoreIds = data.toString()
        this.loadData()
      },
      submitForm() {
        this.form.validateFields((err, value) => {
          if (err) {
            return false
          } else {
            if (this.selectedRowKeys.length <= 0) {
              this.$message.warning('请选择一条记录！')
              return
            } else {
              var ids = ''
              for (var a = 0; a < this.selectedRowKeys.length; a++) {
                ids += this.selectedRowKeys[a] + ','
              }
              let formData = {
                assetsIdTwo: ids,
                assetsIdOne: this.assetsId,
                relationId: this.noticeConfigid,
              }
              let that = this
              httpAction(that.url.addRelation, formData, 'put').then((res) => {
                if (res.success) {
                  that.$message.success(res.message)
                  that.$emit('ok')
                  that.loadData()
                } else {
                  that.$message.warning(res.message)
                }
              })
            }
          }
        })
      },
    },
  }
</script>

<style lang='less' scoped>
  @import '~@assets/less/common.less';

  .div-table-container {
    background-color: white;
    margin-top: 10px;
    height: calc(100% - 150px);
  }

  ::v-deep .ant-table-body {
    overflow-x: auto !important;
  }
</style>