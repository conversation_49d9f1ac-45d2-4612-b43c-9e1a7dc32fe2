<template>
  <j-modal
    :title="title"
    :width="width"
    :centered="true"
    :visible="visible"
    :switchFullscreen="switchFullscreen"
    @ok="handleOk"
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @cancel="handleCancel"
    cancelText="关闭">
    <devops-schedual-info-form ref="realForm" @ok="submitCallback" :disabled="disableSubmit"></devops-schedual-info-form>
  </j-modal>
</template>

<script>

  import DevopsSchedualInfoForm from './DevopsSchedualInfoForm'
  export default {
    name: 'DevopsSchedualInfoModal',
    components: {
      DevopsSchedualInfoForm
    },
    data () {
      return {
        title:'',
        width:600,
        switchFullscreen: false,
        visible: false,
        disableSubmit: true 
      }
    },
    methods: {
      add () {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.add();
        })
      },
      edit (record) {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.edit(record);
        })
      },
      close () {
        this.$emit('close');
        this.visible = false;
      },
      handleOk () {
        this.$refs.realForm.submitForm();
      },
      submitCallback(){
        this.$emit('ok');
        this.visible = false;
      },
      handleCancel () {
        this.close()
      }
    }
  }
</script>
<style lang="less" scoped>
::v-deep .ant-modal-body {
  padding: 24px 48px 24px 48px;
}
::v-deep .ant-modal {
  padding: 24px;
}
</style>