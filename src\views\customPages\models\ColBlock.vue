<template>
  <a-col v-if='record' :lg='24' :xxl='record.xxl'  >
   <div class='col-cotent' @mouseenter='maskShow' @mouseleave='maskHide' :style='{height: height+"px"}'>
     <div class='col-empty' v-if='record.componentPath==""'>
       <a-popover v-if='edit'>
         <template slot='content'>
           <div class='comp-item'
                v-for='item in componentsList'
                :key='item.id'
                @click='setComponent(item)'
           >
             {{item.componentName}}
           </div>
         </template>
         <a-button>添加组件</a-button>
       </a-popover>
       <a-empty v-else>
         <span slot='description' style='color: rgb(42, 161, 152);'>无数据</span>
       </a-empty>
     </div>
     <component v-else v-bind:is="currentComponent"></component>
     <div class='col-mask' v-if="maskVisible">
       <a-button type="danger" shape="circle" icon="delete" style='margin-top:20px' @click='delComponent'/>
     </div>
   </div>
  </a-col>
</template>

<script>
import { componentsList } from '@views/customPages/models/componentsList'
import ComIs from '@views/customPages/models/ComIs.vue'
export default {
  name: 'ColBlock',
  components:{ComIs},
  props: {
    record: {
      type: Object,
      default: () => {
        return null
      }
    },
    blockId: {
      type: String,
      default: ''
    },
    edit:{
      type:Boolean,
      default:false,
    } ,
    height:{
      type:Number,
      default:0,
    },
     //已添加的组件
     addedComponents:{
      type:Array,
      default:()=>{
        return [];
      } 
    }
  },
  data() {
    return {
      currentComponent:"",
      maskVisible:false,

    }
  },
  created() {
    if(this.record.componentPath){
      this.currentComponent = () => ({
        component: import(`@/views/customPages/components/${this.record.componentPath}.vue`),
      })
    }
  },
  mounted() {
  },
  computed:{
    componentsList(){
      return componentsList.filter(item=>{
        return !this.addedComponents.includes(item.componentPath)
      })
    }
  },
  methods: {
    setComponent(item){
      this.$emit("setComponent",item,this.record)
      this.currentComponent = () => ({
        component: import(`@/views/customPages/components/${item.componentPath}.vue`),
      })
    },
    maskShow(){
        if(this.record.componentPath && this.edit){
          this.maskVisible =true;
        }
    },
    maskHide(){
      this.maskVisible = false;
    },
    delComponent(){
      this.$confirm({
        content: '要删除组件吗？',
        onOk:()=>{
          this.$emit("setComponent", {  componentName:"", componentPath:"", },this.record);
          this.currentComponent = null;
        },
        cancelText: '取消',
        okText: '删除',
      });
    }

  }
}
</script>

<style scoped lang='less'>
.col-cotent{
  width: 100%;
  height: 100%;
  position: relative;
  overflow-y: auto;
  .col-mask{
    position: absolute;
    background-color: rgba(0,0,0,0.01);
    width: 100%;
    height: 100%;
    top:0;
    left:0;
    z-index: 100;
    display: flex;
    justify-content: center;
  }
}
.col-empty {
  width: 100%;
  height: 100%;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
}
.comp-item{
  padding: 10px;
  border-radius: 8px;
  cursor: pointer;
  color: rgba(0, 0, 0, 0.88);
}
.comp-item:hover{
  background-color: rgba(0, 0, 0, 0.06)
}
</style>