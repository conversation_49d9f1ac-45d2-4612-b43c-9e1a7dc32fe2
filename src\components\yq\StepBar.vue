<template>
  <div class="step-bar">
    <div
      v-for='(stepItem,idx) in list'
      class='step-row'
      :class="{
        'step-row-reverse':idx%2==1
      }"
    >
    <!--   'step-item-bl':index==colNum-1&& idx!==list.length-1, -->
      <div
        v-for="(step, index) in stepItem"
        :key="idx+'_'+index"
        :style='{ width: `calc(100% / ${colNum}`}'
        :class="{
        'step-item': true,
        'step-item-current': currentStep == (index + 1)+(colNum * idx),
      }"
      >
        <div v-if="index==colNum-1&& idx!==list.length-1" class="step-column-line"></div>
        <div class="step-number">
          <!--使用了一个作用域插槽 :index="(index + 1)+(colNum * idx)"，这样在使用组件时可以通过 scope 获取当前步骤的索引，从而更灵活地替换步骤数字-->
          <slot name='number' :record="step" :index='(index + 1)+(colNum * idx)'>
            {{ (index + 1)+(colNum * idx) }}
          </slot>
        </div>
        <div class="step-content">
          <div class='step-item-title'>
            <div class='step-name' :class="{'step-name-full':!rowLinePipe(index,idx)}">
              <slot name='title' :record="step">
                {{step.title}}
              </slot>
            </div>
            <div class='step-row-line' v-show='rowLinePipe(index,idx)' :key="'line_'+index+'_'+idx"></div>
          </div>
          <div class='info-box'>
            <!--使用了一个作用域插槽 自定义显示内容-->
            <slot name='content' :record="step" :index='(index + 1)+(colNum * idx)'>
            </slot>
          </div>

        </div>
      </div>
    </div>
  </div>
</template>
<script>
/*
* 此组件出入的步骤列表的元素中必须带的两个参数：
* title：步骤名称
* */
export default {
  name: 'StepBar',
  props: {
    //步骤列表
    steps: {
      type: Array,
      required: true,
      default:()=>[],
    },
    //当前步骤或正在运行步骤
    currentStep: {
      type: Number,
      default: -1
    },
    //一行需要展示几列
    column:{
      type: Number,
      default: 0
    },
  },
  data(){
    return {
      list:[],
      colNum:3,
    }
  },
  created() {
    this.initStepData();
  },
  watch:{
    column(){
      this.initStepData()
    },
    steps(){
      this.initStepData()
    }
  },
  methods:{
    rowLinePipe(index,idx){
      let bool = idx%2===0?index<this.colNum-1 && ((index + 1)+(this.colNum * idx)<this.steps.length):index>0
      // console.log("数据为 ==== ",index,idx,this.colNum,bool)
      return bool;
    },
    initStepData(){
      this.list = []
      if(this.column>0){
        this.colNum = Math.ceil(this.column)
      }
      let temlist = this.steps.slice()
      const len = Math.ceil(temlist.length/ this.colNum)
      for(let i = 0 ; i<len ; i++){
        let tem = temlist.splice(0, this.colNum)
        this.list.push(tem)
      }
    }
  }
}
</script>



<style scoped lang='less'>
.step-bar {
  position: relative;
  padding: 20px 0;
  width: 100%;
}
.step-row{
  display: flex;
  flex-wrap: nowrap;
  width: 100%;
  padding:0 16px;
}
.step-row-reverse{
  flex-direction: row-reverse;
}
.step-item {

  padding-bottom: 80px;
  position: relative;
  .step-column-line{
        width: 1px;
        height: calc(100% - 60px);
        background-color: #E8E8E8;
        position: absolute;
        left:0px;
        top:30px;
      }
  .step-number {
    position: absolute;
    left:0px;
    top:0px;
    z-index: 10;
    transform: translateX(-50%) translateY(-50%);
    text-align: center;
    width: 30px;
    height: 30px;
    line-height: 30px;
    border-radius: 50%;
    display: inline-block;
    transition: all 0.3s ease;
    background: #FAFAFA;
    border: 1px solid #E8E8E8;
  }
  .step-content {
    margin-top: -15px;
    padding:0px 25px;
    .step-item-title{
      display: flex;
      .step-name{
        font-family: PingFangSC-Regular;
        font-size: 18px;
        color: rgba(0,0,0,0.85);
        font-weight: 400;
        max-width: 70%;
        // padding-right: 20px;
      }
      .step-name-full{
        max-width: 100%;
        padding-right: 8px;
      }
      .step-row-line{
        flex:1;
        margin-top: 15px;
        margin-right: 10px;
        margin-left: 20px;
        height: 1px;
        background: #E8E8E8;
      }
    }
  }
}
.step-item-current{
  .step-number {
    opacity: 1;
    display: inline-block;
    background: #FAFAFA;
    border: 1px solid #4793FE;
    font-size: 14px;
    color: #4793FE;
    font-weight: 400;
  }
  .step-content{
    .step-item-title{
      .step-name{
        opacity: 1;
        color: #4793FE;
      }
    }
    .info-box{
      opacity: 1;
    }
  }
}
.step-item-bt{
  border-top: 1px solid  #E8E8E8;
}
.step-item-bl{
  border-left: 1px solid  #E8E8E8;
}
.active.step-number {
  border-color: #42b983;
}

.completed.step-number {
  background-color: #42b983;
  color: white;
  border-color: #42b983;
}

</style>