<template>
  <a-card :loading='loading' style='width: 100%; height: 100%'>
    <div slot='title'>
      <a-icon type='line-chart' />
      工单数量统计
      <!--      <a-tooltip title='刷新'>-->
      <!--        <a-icon type='sync' style='color: #ccc; cursor: pointer; font-size: 14px' @click='loadBusiness' />-->
      <!--      </a-tooltip>-->
    </div>
    <!--    <a slot='extra' @click='goPage(4)'>更多-->
    <!--      <a-icon type='double-right' />-->
    <!--    </a>-->
    <div slot='extra' class="header-right">
      <div class="header-right-time">
        <span>选择日期:</span>
        <div class="time-range-span">
          <a-range-picker @change="onChange" />
        </div>
      </div>
      <a-button type='primary'  @click="search">
        查询
      </a-button>
    </div>
    <div class="core-bottom-center-body">
      <div class="core-bottom-center-body-Line" id="dayCountLine"></div>
    </div>
  </a-card>
</template>

<script>
import echarts from 'echarts'
import { getAction } from '@api/manage'
export default {
  name: 'WorkOrderQuantity',
  data(){
    return{
      time1: '',
      time2: '',
      loading:false,
    }
  },
  created() {

  },
  mounted() {
    this.getWorkOrderCoun()
  },
  methods:{
    // 工单数据统计折线图数据
    getWorkOrderCoun() {
      getAction('/data-analysis/order/day/count', {
        time1: this.time1,
        time2: this.time2
      }).then((res) => {
        if (res.code == 200) {
          this.dayCountLine(res.result)
        }
      })
    },
    // 工单数据统计折线图
    dayCountLine(data) {
      let xArr = []
      let yArr = []
      data.forEach((e) => {
        xArr.push(e.name)
        yArr.push(e.value)
      })
      let myChart = this.$echarts.init(document.getElementById('dayCountLine'))
      myChart.setOption({
        tooltip: {
          show: true,
          trigger: 'axis',
          transitionDuration: 0, //echart防止tooltip的抖动
          backgroundColor:"#fff",
          textStyle:{color:"#000"}
        },
        xAxis: [{
          type: 'category',
          boundaryGap: false,
          data: xArr,
          axisLabel: {
            show: true,
            textStyle: {
              color: '#A8A8A8', //更改坐标轴文字颜色
            },
          },
        }, ],
        yAxis: [{
          minInterval: 1,
          type: 'value',
          axisLabel: {
            show: true,
            textStyle: {
              color: '#A8A8A8', //更改坐标轴文字颜色
            },
          },
          axisTick:{show:false},
          axisLine: {
            show: false,
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: ['#f0f0f0'],
              width: 2,
              type: 'solid',
            },
          },
        }, ],
        grid: {
          top: 12,
          right: 40,
          bottom: 24,
          left: 40,
        },
        series: [{
          name: '工单数量',
          type: 'line',
          areaStyle: {},
          emphasis: {
            focus: 'series',
          },
          data: yArr,
          itemStyle: {
            normal: {
              areaStyle: {
                type: 'default',
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1, //变化度
                  //两种种由深及浅的颜色
                  [{
                    offset: 0,
                    color: 'rgba(147,197,253,.5)',
                  },
                    {
                      offset: 1,
                      color: 'rgba(147,197,253,.5)',
                    },
                  ]
                ),
              },
              color: '#91B1F5', //改变折线点的颜色
            },
          },
          lineStyle: {
            color: '#6893F2', //改变折线颜色
          },
        }, ],
      })
      window.addEventListener("resize", () => {
        myChart.resize();
      });
    },
    onChange(dates, dateStrings) {
      this.time1 = dateStrings[0]
      this.time2 = dateStrings[1]
    },
    search() {
        this.getWorkOrderCoun();
    },
  }
}
</script>



<style scoped lang='less'>
::v-deep .ant-card-body{
  padding: 12px;
  height: calc(100% - 24px);
}
.core-bottom-center-body {
  width: 100%;
  height: 86%;
  display: flex;
  align-items: center;
  justify-content: center;

  .core-bottom-center-body-Line {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.header-right {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  .header-right-time {
    font-size: 0.175rem;
    font-family: PingFang SC;
    letter-spacing: 0px;
    font-weight: 100;
    color: #ffffff;
    display: flex;
    align-items: center;
    .time-range-span {
      margin-right: 0.4375rem;
      margin-left: 0.2rem;
    }
  }
}
</style>