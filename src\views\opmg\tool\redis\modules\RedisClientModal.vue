<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    :destroyOnClose="true"
    :centered="true"
    switchFullscreen
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭"
    okText="保存">
    <template slot="footer">
      <div class="footer-view">
        <div class="left">
          <a-button type="primary" @click="redisConnect">连接测试</a-button>
        </div>
        <div class="right">
          <a-button key="back" @click="handleCancel">关闭</a-button>
          <a-button type="primary" @click="handleOk">保存</a-button>
        </div>
      </div>
    </template>
    <redis-client-form
      ref="realForm"
      @ok="submitCallback"
      @closeForm='close'
      :disabled="disableSubmit"
    ></redis-client-form>
  </j-modal>
</template>

<script>
import RedisClientForm from './RedisClientForm.vue'
export default {
  name: 'RedisClientModal',
  components: {
    RedisClientForm,
  },
  data() {
    return {
      title: '',
      width: '800px',
      visible: false,
      disableSubmit: false,
    }
  },
  methods: {
    add() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.add()
      })
    },
    edit(record) {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.edit(record)
      })
    },
    close () {
      this.$emit('close');
      this.visible = false;
    },
    handleOk() {
      this.$refs.realForm.submitForm()
    },
    submitCallback() {
      this.$emit('ok')
      this.visible = false
    },
    handleCancel() {
      this.close()
    },
    redisConnect() {
      this.$refs.realForm.redisConnect()
    }
  },
}
</script>
<style lang="less" scoped>
@import '~@assets/less/normalModal.less';
.footer-view {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .left {
    padding-left: 20px;
    a {
      color: #40a9ff;
    }
  }
}
</style>