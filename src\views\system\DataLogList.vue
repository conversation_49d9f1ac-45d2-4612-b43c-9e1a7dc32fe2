<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll zhl zhl">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <a-card :bordered="false" style="width: 100%; flex: auto">
        <!-- 查询区域 -->
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="表名">
                  <a-input
                    placeholder="请输入表名"
                    :allowClear="true"
                    autocomplete='off'
                    v-model="queryParam.dataTable"/>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="数据ID">
                  <a-input
                    placeholder="请输入ID"
                    :allowClear="true"
                    autocomplete='off'
                    v-model="queryParam.dataId"/>
                </a-form-item>
              </a-col>

              <a-col :span="colBtnsSpan()">
                <span
                  class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                >
                  <a-button type="primary" class="btn-search btn-search-style" @click="searchQuery">查询</a-button>
                  <a-button class="btn-reset btn-reset-style" @click="searchReset">重置</a-button>
                  <a v-if="isVisible" class="btn-updown-style" @click="doToggleSearch">
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
        <!-- 操作按钮区域 -->
        <div class="table-operator table-operator-style">
          <a-button @click="handleCompare()" type="primary" icon="plus">数据比较</a-button>
        </div>
        <!--table区 -->
        <!--已选择的清空 -->
        <a-table
          ref="table"
          bordered
          rowKey="id"
          :columns="columns"
          :dataSource="dataSource"
          :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
          :pagination="ipagination"
          :loading="loading"
          :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          @change="handleTableChange"
        >
          <!-- 字符串超长截取省略号显示-->
          <span slot="dataContent" slot-scope="text, record"
            >{{ text }}
            <!--          <j-ellipsis :value='text' :length='80' />-->
          </span>
        </a-table>
        <data-log-modal ref="modalForm" @ok="modalFormOk"></data-log-modal>
      </a-card>
    </a-col>
  </a-row>
</template>

<script>
import DataLogModal from './modules/DataLogModal'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import JEllipsis from '@/components/jeecg/JEllipsis'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'

export default {
  name: 'DataLogList',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components: {
    JEllipsis,
    DataLogModal,
  },
  data() {
    return {
      description: '数据日志管理页面',
      formItemLayout: {
        labelCol: {
          style: 'width:80px',
        },
        wrapperCol: {
          style: 'width:calc(100% - 80px)'
        }
      },
      //表头
      columns: [
        {
          title: '表名',
          dataIndex: 'dataTable',
        },
        {
          title: '数据ID',
          dataIndex: 'dataId',
        },
        {
          title: '版本号',
          dataIndex: 'dataVersion',
        },
        {
          title: '数据内容',
          dataIndex: 'dataContent',
          scopedSlots: { customRender: 'dataContent' },
        },
        {
          title: '创建人',
          dataIndex: 'createBy',
        },
      ],
      url: {
        list: '/sys/dataLog/list',
      },
    }
  },
  mounted() {},
  methods: {
    handleCompare: function () {
      if (!this.selectionRows || this.selectionRows.length != 2) {
        this.openNotifIcon('请选择两条数据')
        return false
      } else if (this.selectionRows[0].dataId != this.selectionRows[1].dataId) {
        this.openNotifIcon('请选择相同的数据库表和数据ID进行比较')
        return false
      } else {
        this.$refs.modalForm.addModal(this.selectionRows)
        this.$refs.modalForm.title = '数据比较'
      }
    },
    openNotifIcon(msg) {
      this.$notification['warning']({
        message: '提示信息',
        description: msg,
      })
    },
  },
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
/*给table列设置宽度*/
::v-deep .ant-table-thead > tr > th,
::v-deep .ant-table-tbody > tr > td {
  /*表名称*/

  &:nth-child(2) {
    min-width: 100px;
  }

  /*数据ID*/

  &:nth-child(3) {
    min-width: 100px;
  }

  /*版本号*/

  &:nth-child(4) {
    min-width: 70px;
  }
  /*数据内容*/

  &:nth-child(5) {
    min-width: 200px;
  }
  /*创建人*/

  &:nth-child(6) {
    min-width: 100px;
  }
}

/*表头样式*/
::v-deep .ant-table-thead > tr > th {
  text-align: center;
  white-space: nowrap;
}

/*内容对齐方式、省略显示*/
::v-deep .ant-table-tbody > tr > td {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;

  /*&:nth-child(-n + 5) , &:nth-child(10){
    text-align: center;
  }

  &:nth-child(6), &:nth-child(7){
    text-align: right;
  }
  &:nth-child(8), &:nth-child(9){
    text-align: left;
  }*/
}
</style>