<template>
  <a-drawer :title='title' :maskClosable='true' :width='drawerWidth' placement='right' :closable='true'
    @close='handleCancel' :visible='visible' style='height: 100%; padding-bottom: 53px'>
    <template slot='title'>
      <div style='width: 100%'>
        <span>{{ title }}</span>
        <span style='display: inline-block; width: calc(100% - 51px); padding-right: 10px; text-align: right'>
          <a-button @click='toggleScreen' icon='appstore' style='height: 20px; width: 20px; border: 0px'></a-button>
        </span>
      </div>
    </template>

    <a-spin :spinning='confirmLoading'>
      <a-form :form='form'>
        <a-form-item label='用户账号' :labelCol='labelCol' :wrapperCol='wrapperCol'>
          <a-input placeholder='请输入用户账号' v-decorator.trim="['username', validatorRules.username]" :readOnly='!!model.id'
            :allowClear='true' autocomplete='off' />
        </a-form-item>

        <template v-if='!model.id'>
          <a-form-item label='登录密码' :labelCol='labelCol' :wrapperCol='wrapperCol'>
            <a-input type='password' placeholder='请输入登录密码' v-decorator="['password', validatorRules.password]"
              :allowClear='true' autocomplete='off' />
          </a-form-item>

          <a-form-item label='确认密码' :labelCol='labelCol' :wrapperCol='wrapperCol'>
            <a-input type='password' @blur='handleConfirmBlur' placeholder='请重新输入登录密码'
              v-decorator="['confirmpassword', validatorRules.confirmpassword]" :allowClear='true' autocomplete='off' />
          </a-form-item>
        </template>

        <a-form-item label='用户姓名' :labelCol='labelCol' :wrapperCol='wrapperCol'>
          <a-input placeholder='请输入用户姓名' v-decorator.trim="['realname', validatorRules.realname]" :allowClear='true'
            autocomplete='off' />
        </a-form-item>

        <a-form-item class='two-words' label='工号' :labelCol='labelCol' :wrapperCol='wrapperCol'>
          <a-input placeholder='请输入工号' v-decorator.trim="['workNo', validatorRules.workNo]" :allowClear='true'
            autocomplete='off' />
        </a-form-item>

        <a-form-item class='two-words' label='职务' :labelCol='labelCol' :wrapperCol='wrapperCol'>
          <j-select-position placeholder='请选择职务' :multiple='false' v-decorator="['post', {}]" />
        </a-form-item>

        <a-form-item label='角色分配' :labelCol='labelCol' :wrapperCol='wrapperCol' v-show='roleShow'>
          <div>
            <a-select mode='multiple' :disabled='roleDisabled'
              :style='{width:isThreePowers==1? "calc(100% - 30px)":"100%"}' placeholder='请选择用户角色'
              optionFilterProp='children' v-model='selectedRole' :getPopupContainer='(target) => target.parentNode'
              :allowClear='true' option-label-prop="label" @change='changeRole'>
              <a-select-option v-for='(role, roleindex) in roleList' :key='roleindex.toString()' :value='role.id'
                :label='role.roleName' :text='role.roleCode'>
                {{ role.roleName }}
              </a-select-option>
            </a-select>
            <a-popover title='参数说明' v-if='isThreePowers==1' placement="topRight">
              <template slot='content'>
                <p>启用三员后，一个用户不可同时具备安全保密管理员、安全审计员两个角色，且他们也不具备其他任何权限</p>
              </template>
              <a-icon style='font-size: 20px; line-height: 40px;margin-left: 10px' theme='twoTone'
                type='question-circle' />
            </a-popover>
          </div>
        </a-form-item>

        <!--部门分配-->
        <a-form-item label='部门分配' :labelCol='labelCol' :wrapperCol='wrapperCol' v-show='!departDisabled' required>
          <div>
            <a-input-search placeholder='点击选择部门' v-model='checkedDepartNameString' readOnly @search='onSearch'
              style="width: calc(100% - 30px)">
              <a-button slot='enterButton' icon='search'
                style="color: #fff;background-color: #1E3674;border-color: #1E3674;">选择
              </a-button>
            </a-input-search>
            <a-popover title='部门分配说明' placement="topLeft">
              <template slot='content'>
                <p>拥有管理员角色的用户在维护其他普通用户时，向其分配其可选择负责的部门</p>
              </template>
              <a-icon style='font-size: 20px; line-height: 40px;margin-left: 10px' theme='twoTone'
                type='question-circle' />
            </a-popover>
          </div>
        </a-form-item>

        <!--租户分配-->
        <!--        <a-form-item label="租户分配" :labelCol="labelCol" :wrapperCol="wrapperCol" v-show="!departDisabled">
          <a-select
            mode="multiple"
            style="width: 100%"
            placeholder="请选择租户分配"
            :disabled="disableSubmit"
            v-model="currentTenant"
            :getPopupContainer="(target) => target.parentNode"
            :allowClear="true"
          >
            <a-select-option v-for="(item, index) in tenantList" :key="index" :value="item.id">
              {{ item.realname }}
            </a-select-option>
          </a-select>
        </a-form-item>-->

        <!-- update--begin--autor:wangshuai-----date:20200108------for：新增身份和负责部门------ -->
        <a-form-item class='two-words' label='身份' :labelCol='labelCol' :wrapperCol='wrapperCol'>
          <div>
            <a-radio-group v-model='identity' @change='identityChange' style="width: calc(100% - 30px)">
              <a-radio value='1'>普通用户</a-radio>
              <a-radio value='2'>上级</a-radio>
            </a-radio-group>
            <a-popover title='身份说明' placement="topLeft">
              <template slot='content'>
                <p>普通用户:可以从管理员为其分配的部门中选择自己具体负责的部门，并拥有其对应部门下终端、设备及其告警数据的查看和管理权限</p>
                <p>上级:拥有本系统内所有终端、设备及其告警数据的查看和管理权限;</p>
              </template>
              <a-icon style='font-size: 20px; line-height: 40px;margin-left: 10px' theme='twoTone'
                type='question-circle' />
            </a-popover>
          </div>
        </a-form-item>
        <a-form-item label='负责部门' :labelCol='labelCol' :wrapperCol='wrapperCol' v-if='departIdShow == true'>
          <div>
            <a-select mode='multiple' style="width: calc(100% - 30px)" placeholder='请选择负责部门' v-model='departIds'
              optionFilterProp='children' :dropdownStyle="{ maxHeight: '200px', overflow: 'auto' }"
              :getPopupContainer='(target) => target.parentNode' :allowClear='true'>
              <a-select-option v-for='item in resultDepartOptions' :key='item.key' :value='item.key'>{{
                item.title
              }}
              </a-select-option>
            </a-select>
            <a-popover title='负责部门说明' placement="topLeft">
              <template slot='content'>
                <p>普通用户从管理员用户为其分配的部门中，选择一个或多个部门自己具体负责</p>
              </template>
              <a-icon style='font-size: 20px; line-height: 40px;margin-left: 10px' theme='twoTone'
                type='question-circle' />
            </a-popover>
          </div>
        </a-form-item>
        <!-- update--end--autor:wangshuai-----date:20200108------for：新增身份和负责部门------ -->
        <a-form-item class='two-words' label='头像' :labelCol='labelCol' :wrapperCol='wrapperCol'>
          <j-image-upload bizPath='image/usesrIcon' class='avatar-uploader' text='上传' v-model='fileList'>
          </j-image-upload>
        </a-form-item>

        <a-form-item class='two-words' label='生日' :labelCol='labelCol' :wrapperCol='wrapperCol'>
          <a-date-picker style='width: 100%' placeholder='请选择生日' :disabled-date='disabledDate'
            v-decorator="['birthday', { initialValue: !model.birthday ? null : moment(model.birthday, dateFormat) }]"
            :getCalendarContainer='(node) => node.parentNode' />
        </a-form-item>

        <a-form-item class='two-words' label='性别' :labelCol='labelCol' :wrapperCol='wrapperCol'>
          <a-select v-decorator="['sex', {}]" placeholder='请选择性别' :getPopupContainer='(target) => target.parentNode'
            :allowClear='true'>
            <a-select-option :value='1'>男</a-select-option>
            <a-select-option :value='2'>女</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item class='two-words' label='邮箱' :labelCol='labelCol' :wrapperCol='wrapperCol'>
          <a-input placeholder='请输入邮箱' v-decorator="['email', validatorRules.email]" autocomplete='off'
            :allowClear='true' />
        </a-form-item>
        <a-form-item label='手机号码' :labelCol='labelCol' :wrapperCol='wrapperCol' prop='phone' style='z-index:1'>
          <a-input placeholder='请输入手机号码' :disabled="isDisabledAuth('user:form:phone')"
            v-decorator="['phone', validatorRules.phone]" autocomplete='off' :allowClear='true' />
        </a-form-item>

        <a-form-item class='two-words' label='座机' :labelCol='labelCol' :wrapperCol='wrapperCol'>
          <a-input placeholder='请输入座机' v-decorator="['telephone', validatorRules.telephone]" autocomplete='off'
            :allowClear='true' />
        </a-form-item>

        <a-form-item label='工作流引擎' :labelCol='labelCol' :wrapperCol='wrapperCol'>
          <j-dict-select-tag v-decorator="['activitiSync', {}]" placeholder='请选择是否同步工作流引擎' :type="'radio'"
            :triggerChange='true' dictCode='activiti_sync' />
        </a-form-item>
      </a-form>
    </a-spin>
    <depart-window ref='departWindow' @ok='modalFormOk'></depart-window>

    <div class='drawer-bootom-button' v-show='!disableSubmit' style='z-index:3'>
      <a-popconfirm title='确定放弃编辑？' @confirm='handleCancel' okText='确定' cancelText='取消'>
        <a-button style='margin-right: 0.8rem'>取消</a-button>
      </a-popconfirm>
      <a-button @click='handleSubmit' type='primary' :loading='confirmLoading'>提交
      </a-button>
    </div>
  </a-drawer>
</template>

<script>
  import pick from 'lodash.pick'
  import moment from 'moment'
  import Vue from 'vue'
  // 引入搜索部门弹出框的组件
  import departWindow from './DepartWindow'
  import JSelectPosition from '@/components/jeecgbiz/JSelectPosition'
  import {
    ACCESS_TOKEN,
    ROLE_CODES
  } from '@/store/mutation-types'
  import {
    getAction
  } from '@/api/manage'
  import {
    addUser,
    editUser,
    queryUserRole,
    queryall
  } from '@/api/api'
  import {
    disabledAuthFilter
  } from '@/utils/authFilter'
  import {
    duplicateCheck
  } from '@/api/api'
  import JImageUpload from '../../../components/jeecg/JImageUpload'
  import {
    phoneValidator
  } from '@/mixins/phoneValidator'
  import {
    mapActions,
    mapGetters,
    mapState
  } from 'vuex'
  import {
    getFileAccessHttpUrl
  } from '@/api/manage'
  import roleList from '@views/system/RoleList.vue'

  export default {
    name: 'UserModal',
    components: {
      JImageUpload,
      departWindow,
      JSelectPosition
    },
    mixins: [phoneValidator],
    data() {
      return {
        title: '操作',
        visible: false,
        modalWidth: 800,
        drawerWidth: 800,
        disableSubmit: false,
        confirmLoading: false,

        isThreePowers: '',
        roleShow: true,
        departDisabled: false, //是否是我的部门调用该页面
        roleDisabled: false, //是否是角色维护调用该页面

        modaltoggleFlag: true,
        confirmDirty: false,
        selectedDepartKeys: [], //保存用户选择部门id
        checkedDepartKeys: [],
        checkedDepartNames: [], // 保存部门的名称 =>title
        checkedDepartNameString: '', // 保存部门的名称 =>title
        resultDepartOptions: [],
        userId: '', //保存用户id,

        userDepartModel: {
          userId: '',
          departIdList: []
        }, // 保存SysUserDepart的用户部门中间表数据需要的对象
        dateFormat: 'YYYY-MM-DD',
        validatorRules: {
          username: {
            rules: [{
                required: true,
                message: '请输入用户账号!'
              },
              {
                min: 2,
                max: 20,
                message: '用户账号在2-20个字符之间'
              },
              {
                validator: this.validateUsername
              }
            ]
          },
          assetsCode: {
            rules: [{
              required: true,
              message: '请输入分配的角色!'
            }]
          },
          password: {
            rules: [{
                required: true,
                message: '请输入密码！'
              },
              {
                validator: this.validateToNextPassword
              }
            ]
          },
          confirmpassword: {
            rules: [{
                required: true,
                message: '请重新输入登录密码!'
              },
              {
                validator: this.compareToFirstPassword
              }
            ]
          },
          realname: {
            rules: [{
                required: true,
                message: '请输入用户姓名!'
              },
              {
                min: 2,
                max: 20,
                message: '用户姓名在2-20位字符之间'
              }
            ]
          },
          phone: {
            rules: [
            {
                required: true,
                message: '请输入手机号!'
              },{
              validator: this.validatePhone
            }]
          },
          email: {
            rules: [{
              validator: this.validateEmail
            }]
          },
          rules: {
            name: [{
                required: true,
                message: 'Please input Activity name',
                trigger: 'blur'
              },
              {
                min: 3,
                max: 5,
                message: 'Length should be 3 to 5',
                trigger: 'blur'
              }
            ],
            region: [{
              required: true,
              message: 'Please select Activity zone',
              trigger: 'change'
            }],
            date1: [{
              required: true,
              message: 'Please pick a date',
              trigger: 'change'
            }],
            type: [{
              type: 'array',
              required: true,
              message: 'Please select at least one activity type',
              trigger: 'change'
            }],
            resource: [{
              required: true,
              message: 'Please select activity resource',
              trigger: 'change'
            }],
            desc: [{
              required: true,
              message: 'Please input activity form',
              trigger: 'blur'
            }]
          },

          //  sex:{initialValue:((!this.model.sex)?"": (this.model.sex+""))}
          workNo: {
            rules: [{
                required: false,
              },
              {
                pattern: /^[a-zA-Z0-9_-]{1,50}$/,
                message: '工号支持英文、数字、下划线、短划线'
              }, {
                validator: this.validateWorkNo
              }
            ]
          },
          telephone: {
            rules: [{
              validator: this.fixedPhone
            }]
          }
        },
        departIdShow: true,
        departIds: [], //负责部门id
        pwdRuleInfo: null,
        model: {},
        roleList: [],
        selectedRole: [],
        singleRole: [],
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          }
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          }
        },
        uploadLoading: false,
        headers: {},
        form: this.$form.createForm(this),
        picUrl: '',
        url: {
          fileUpload: window._CONFIG['domianURL'] + '/sys/common/upload',
          userWithDepart: '/sys/user/userDepartList', // 引入为指定用户查看部门信息需要的url
          userId: '/sys/user/generateUserId', // 引入生成添加用户情况下的url
          syncUserByUserName: '/act/process/extActProcess/doSyncUserByUserName', //同步用户到工作流
          queryTenantList: '/sys/tenant/queryList',
          isThreePowers: '/sys/isThreePowers', //三员是否开启
          getThreePowers: '/sys/user/getThreePowers', //三员是否开启
          pwdRuleUrl: 'umpPwdManage/umpPwdManage/list',
        },
        identity: '1',
        fileList: [],
        tenantList: []
        // currentTenant: [],
      }
    },
    created() {
      const token = Vue.ls.get(ACCESS_TOKEN)
      this.headers = {
        'X-Access-Token': token
      }
      this.initTenantList()
      this.getPwdRuleData()
    },
    mounted() {},
    computed: {
      uploadAction: function () {
        return this.url.fileUpload
      }
    },
    methods: {
      getPwdRuleData() {
        getAction(this.url.pwdRuleUrl).then((res) => {
          if (res.success) {
            this.pwdRuleInfo = res.result.records[0]
            this.setValidator(this.pwdRuleInfo)
          }
        })
      },
      setValidator(info) {
        let capRegStr = ''
        let lowerRegStr = ''
        let numRegStr = ''
        let speRegStr = ''
        let cbStr = ''
        if (!!info.pwdMin) {
          cbStr = '密码至少由' + this.pwdRuleInfo.pwdMin + '位组成，包含'
        }
        if (info.capitalize) {
          capRegStr = "[A-Z]+"
          cbStr += "大写字母"
        }
        if (info.lowercase) {
          lowerRegStr = "[a-z]+"
          cbStr += cbStr.length > (4 + this.pwdRuleInfo.pwdMin.length) ? "、小写字母" : "小写字母"
        }
        if (info.hasNum) {
          numRegStr = "[0-9]+"
          cbStr += cbStr.length > (4 + this.pwdRuleInfo.pwdMin.length) ? "、数字" : "数字"
        }
        if (info.special) {
          speRegStr += "[`~!@#$^&*()=|{}':;',\\[\\].<>/?~！@#￥……&*（）——|{}【】‘；：”“'。，、？]+"
          cbStr += cbStr.length > (4 + this.pwdRuleInfo.pwdMin.length) ? "、特殊字符" : "特殊字符"
        }
        this.validatorRules.password.rules.push({
          validator: (rule, value, callback) => {
            let capRegEn = new RegExp(capRegStr)
            let lowerRegEn = new RegExp(lowerRegStr)
            let numRegEn = new RegExp(numRegStr)
            let speRegEn = new RegExp(speRegStr)
            if (value && (value.length < parseInt(this.pwdRuleInfo.pwdMin) || !capRegEn.test(value) ||
                !lowerRegEn.test(value) || !numRegEn.test(value) || !speRegEn.test(value))) {
              callback(cbStr + "！");
            } else {
              callback()
            }
          }
        })
      },
      disabledDate(current) {
        return current && current > moment().endOf('day')
      },
      ...mapGetters(['avatar']),
      isDisabledAuth(code) {
        return disabledAuthFilter(code)
      },
      initTenantList() {
        getAction(this.url.queryTenantList).then((res) => {
          if (res.success) {
            this.tenantList = res.result
          }
        })
      },

      /*getIsThreePowers() {
        getAction(this.url.isThreePowers).then((res) => {
          if (res.code == 200) {
            this.isThreePowers = res.result
          } else {
            this.$message.error(res.message)
          }
        })
      },*/
      getThreePowers(username) {
        getAction(this.url.getThreePowers).then((res) => {
          if (res.code == 200) {
            this.isThreePowers = res.result.isThreePowers
            if (this.isThreePowers === 1) {
              this.roleShow = res.result.roleShow
              this.singleRole = res.result.singleRole
              // if (res.result.threePowers.indexOf(username) !== -1) {
              //   this.roleDisabled = true
              // }
            }
          } else {
            this.$message.error(res.message)
          }
        }).then(() => this.initialRoleList(username))
      },

      test(e) {},

      //窗口最大化切换
      toggleScreen() {
        if (this.modaltoggleFlag) {
          this.modalWidth = window.innerWidth
        } else {
          this.modalWidth = 800
        }
        this.modaltoggleFlag = !this.modaltoggleFlag
      },
      initialRoleList(username) {
        queryall({
          username
        }).then((res) => {
          if (res.success) {
            this.roleList = res.result
          }
        })
      },
      loadUserRoles(userid) {
        queryUserRole({
          userid: userid
        }).then((res) => {
          if (res.success) {
            this.selectedRole = res.result
          } else {}
        })
      },
      refresh() {
        this.selectedDepartKeys = []
        this.checkedDepartKeys = []
        this.checkedDepartNames = []
        this.checkedDepartNameString = ''
        this.userId = ''
        this.resultDepartOptions = []
        this.departId = []
        this.departIdShow = false
        this.currentTenant = []
        this.roleDisabled = false;
        this.roleShow = true;
      },
      add() {
        this.picUrl = ''
        this.edit({
          activitiSync: '1'
        })
      },
      edit(record) {
        this.refresh();
        //this.resetScreenSize() // 调用此方法,根据屏幕宽度自适应调整抽屉的宽度
        let that = this
        //that.initialRoleList();
        that.checkedDepartNameString = ''
        that.form.resetFields()

        if (record.hasOwnProperty('id')) {
          that.loadUserRoles(record.id)
          setTimeout(() => {
            that.fileList = record.avatar
          }, 5)
        }
        that.userId = record.id
        that.visible = true
        that.model = Object.assign({}, record)
        that.getThreePowers(record.username);
        that.$nextTick(() => {
          that.form.setFieldsValue(
            pick(
              that.model,
              'username',
              'sex',
              'realname',
              'email',
              'phone',
              'activitiSync',
              'workNo',
              'telephone',
              'post'
            )
          )
        })
        //身份为上级显示负责部门，否则不显示
        if (that.model.userIdentity == '1') {
          that.identity = '1'
          that.departIdShow = true
        } else {
          that.identity = '2'
          that.departIdShow = false
        }
        // 调用查询用户对应的部门信息的方法
        that.checkedDepartKeys = []
        that.loadCheckedDeparts()

        //update-begin-author:taoyan date:2020710 for:多租户配置
        if (!record.relTenantIds || record.relTenantIds.length == 0) {
          that.currentTenant = []
        } else {
          that.currentTenant = record.relTenantIds.split(',').map(Number)
        }
        //update-end-author:taoyan date:2020710 for:多租户配置
      },
      //
      loadCheckedDeparts() {
        let that = this
        if (!that.userId) {
          return
        }
        getAction(that.url.userWithDepart, {
          userId: that.userId
        }).then((res) => {
          that.checkedDepartNames = []
          if (res.success) {
            var depart = []
            var departId = []
            for (let i = 0; i < res.result.length; i++) {
              that.checkedDepartNames.push(res.result[i].title)
              that.checkedDepartNameString = this.checkedDepartNames.join(',')
              that.checkedDepartKeys.push(res.result[i].key)
              //新增负责部门选择下拉框
              depart.push({
                key: res.result[i].key,
                title: res.result[i].title
              })
              departId.push(res.result[i].key)
            }
            that.resultDepartOptions = depart
            //判断部门id是否存在，不存在择直接默认当前所在部门
            if (that.model.departIds) {
              that.departIds = this.model.departIds.split(',')
            } else {
              that.departIds = departId
            }
            that.userDepartModel.departIdList = that.checkedDepartKeys
          } else {}
        })
      },
      close() {
        this.$emit('close')
        this.visible = false
        this.disableSubmit = false
        this.selectedRole = []
        this.userDepartModel = {
          userId: '',
          departIdList: []
        }
        this.checkedDepartNames = []
        this.checkedDepartNameString = ''
        this.checkedDepartKeys = []
        this.selectedDepartKeys = []
        this.resultDepartOptions = []
        this.departIds = []
        this.departIdShow = false
        this.identity = '1'
        this.fileList = []
      },
      moment,
      handleSubmit() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            // 校验部门必填
            if (!this.checkedDepartNameString) {
              this.$message.error('请选择部门!')
              return
            }
            that.confirmLoading = true
            if (!values.birthday) {
              values.birthday = ''
            } else {
              values.birthday = values.birthday.format(this.dateFormat)
            }
            let formData = Object.assign(this.model, values)
            if (that.fileList != '') {
              formData.avatar = that.fileList
            } else {
              formData.avatar = null
            }
            //update-begin-author:taoyan date:2020710 for:多租户配置
            formData.relTenantIds = this.currentTenant.length > 0 ? this.currentTenant.join(',') : ''
            //update-end-author:taoyan date:2020710 for:多租户配置

            // if (this.isThreePowers == 0) {
            formData.selectedroles = this.selectedRole.length > 0 ? this.selectedRole.join(',') : ''
            // } else {
            //   formData.selectedroles = this.selectedRole
            // }

            formData.selecteddeparts =
              this.userDepartModel.departIdList.length > 0 ? this.userDepartModel.departIdList.join(',') : ''
            formData.userIdentity = this.identity
            //如果是上级择传入departIds,否则为空
            if (this.identity === '1') {
              formData.departIds = this.departIds.join(',')
            } else {
              formData.departIds = ''
            }
            // that.addDepartsToUser(that,formData); // 调用根据当前用户添加部门信息的方法
            let obj
            if (!this.model.id) {
              formData.id = this.userId
              obj = addUser(formData)
            } else {
              obj = editUser(formData)
            }
            obj
              .then((res) => {
                if (res.success) {
                  that.$message.success(res.message)
                  that.$emit('ok')
                  this.$store.commit('SET_CA', true)
                } else {
                  that.$message.warning(res.message)
                }
              })
              .finally(() => {
                that.confirmLoading = false
                that.checkedDepartNames = []
                that.userDepartModel.departIdList = {
                  userId: '',
                  departIdList: []
                }
                that.close()
              })
          }
        })
      },
      handleCancel() {
        this.close()
      },
      compareToFirstPassword(rule, value, callback) {
        const form = this.form
        if (value && value !== form.getFieldValue('password')) {
          callback('两次输入的密码不一样！')
        } else {
          callback()
        }
      },
      validateEmail(rule, value, callback) {
        if (!value) {
          callback()
        } else {
          if (
            new RegExp(
              /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
            ).test(value)
          ) {
            var params = {
              tableName: 'sys_users',
              fieldName: 'email',
              fieldVal: value,
              dataId: this.userId
            }
            getAction('/sys/duplicate/checkForUser', params).then((res) => {
              if (res.success) {
                callback()
              } else {
                callback('邮箱已存在!')
              }
            })
          } else {
            callback('请输入正确格式的邮箱!')
          }
        }
      },
      validateUsername(rule, value, callback) {
        var params = {
          tableName: 'sys_users',
          fieldName: 'username',
          fieldVal: value,
          dataId: this.userId
        }
        duplicateCheck(params).then((res) => {
          if (res.success) {
            callback()
          } else {
            callback('用户名已存在!')
          }
        })
      },
      validateToNextPassword(rule, value, callback) {
        const form = this.form
        const confirmpassword = form.getFieldValue('confirmpassword')

        if (value && confirmpassword && value !== confirmpassword) {
          callback('两次输入的密码不一样！')
        }
        if (value && this.confirmDirty) {
          form.validateFields(['confirm'], {
            force: true
          })
        }
        callback()
      },
      validateWorkNo(rule, value, callback) {
        var params = {
          tableName: 'sys_users',
          fieldName: 'work_no',
          fieldVal: value,
          dataId: this.userId
        }
        getAction('/sys/duplicate/checkForUser', params).then((res) => {
          if (res.success) {
            callback()
          } else {
            callback('工号已存在!')
          }
        })
      },
      validatePhone(rule, value, callback) {
        var params = {
          tableName: 'sys_users',
          fieldName: 'phone',
          fieldVal: value,
          dataId: this.userId
        }
        let reg = /(^(0[0-9]{2,3}\-)?([2-9][0-9]{6,7})+(\-[0-9]{1,4})?$)|(^((\d3)|(\d{3}\-))?(1[123456789]\d{9})$)/;
        if (!value || reg.test(value)) {
          getAction('/sys/duplicate/checkForUser', params).then((res) => {
            if (res.success) {
              callback()
            } else {
              callback('手机号已存在!')
            }
          })
        } else {
          callback('请输入正确的手机号或座机号');
        }
      },
      handleConfirmBlur(e) {
        const value = e.target.value
        this.confirmDirty = this.confirmDirty || !!value
      },

      normFile(e) {
        if (Array.isArray(e)) {
          return e
        }
        return e && e.fileList
      },
      beforeUpload: function (file) {
        var fileType = file.type
        if (fileType.indexOf('image') < 0) {
          this.$message.warning('请上传图片')
          return false
        }
        //TODO 验证文件大小
      },
      handleChange(info) {
        this.picUrl = ''
        if (info.file.status === 'uploading') {
          this.uploadLoading = true
          return
        }
        if (info.file.status === 'done') {
          var response = info.file.response
          this.uploadLoading = false
          if (response.success) {
            this.model.avatar = response.message
            this.picUrl = 'Has no pic url yet'
          } else {
            this.$message.warning(response.message)
          }
        }
      },
      // 选择角色
      changeRole(value, option) {
        if (value && value.length > 0) {
          if (this.isThreePowers == 1 && value.length > 1) {
            for (let i = option.length - 1; i >= 0; i--) {
              if (this.singleRole.indexOf(option[i].data.attrs.text) > -1) {
                this.selectedRole = value.slice(-1)
                break
              }
            }
          }
        }
      },

      // 搜索用户对应的部门API
      onSearch() {
        this.$refs.departWindow.add(this.checkedDepartKeys, this.userId)
      },

      // 获取用户对应部门弹出框提交给返回的数据
      modalFormOk(formData) {
        this.checkedDepartNames = []
        this.selectedDepartKeys = []
        this.checkedDepartNameString = ''
        this.userId = formData.userId
        this.userDepartModel.userId = formData.userId
        this.departIds = []
        this.resultDepartOptions = []
        var depart = []
        for (let i = 0; i < formData.departIdList.length; i++) {
          this.selectedDepartKeys.push(formData.departIdList[i].key)
          this.checkedDepartNames.push(formData.departIdList[i].title)
          this.checkedDepartNameString = this.checkedDepartNames.join(',')
          //新增部门选择，如果上面部门选择后不为空直接付给负责部门
          depart.push({
            key: formData.departIdList[i].key,
            title: formData.departIdList[i].title
          })
          this.departIds.push(formData.departIdList[i].key)
        }
        this.resultDepartOptions = depart
        this.userDepartModel.departIdList = this.selectedDepartKeys
        this.checkedDepartKeys = this.selectedDepartKeys //更新当前的选择keys
      },
      // 根据屏幕变化,设置抽屉尺寸
      // resetScreenSize() {
      //   let screenWidth = document.body.clientWidth
      //   if (screenWidth < 800) {
      //     this.drawerWidth = screenWidth
      //   } else {
      //     this.drawerWidth = 800
      //   }
      // },
      identityChange(e) {
        if (e.target.value === '2') {
          this.departIdShow = false
        } else {
          this.departIdShow = true
        }
      }
    }
  }
</script>

<style scoped>
  .avatar-uploader>.ant-upload {
    width: 104px;
    height: 104px;
  }

  .ant-upload-select-picture-card i {
    font-size: 49px;
    color: #999;
  }

  .ant-upload-select-picture-card .ant-upload-text {
    margin-top: 8px;
    color: #666;
  }

  .ant-table-tbody .ant-table-row td {
    padding-top: 10px;
    padding-bottom: 10px;
  }

  .drawer-bootom-button {
    position: absolute;
    bottom: 0px;
    width: 100%;
    border-top: 1px solid #e8e8e8;
    padding: 10px 16px;
    text-align: right;
    left: 0;
    background: #fff;
    border-radius: 0 0 2px 2px;
  }

  ::v-deep .two-words>div>label {
    letter-spacing: 4px;
  }

  ::v-deep .two-words>div>label::after {
    letter-spacing: 0px;
  }

  @media (max-width: 800px) {
    ::v-deep .ant-drawer-content-wrapper {
      max-width: 100vw;
      margin: 0;
    }
  }
</style>