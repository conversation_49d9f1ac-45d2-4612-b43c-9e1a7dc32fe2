<template>
    <a-row style="height: 100%;">
      <a-col ref="print" id="printContent" class="abcdefg" :span="12">
        <div>
          <p style="padding: 16px 24px;background-color: #fafafa;border-bottom:1px solid #e8e8e8;font-size: 16px;">JMX连接测试</p>
        </div>
        <!--签字-->
        <a-col style='padding: 0 24px 24px'>
          <a-form :form='form'>
            <a-row>
              <a-col :span="24" class="aCol">
                <a-form-item label="应用服务器类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-select
                    placeholder="请选择"
                    v-decorator="[
                    'type',
                    {
                    rules: [{ required: true,  message:'请选择应用服务类型'}]}]"
                  >
<!--                    <a-select-option value="''">
                      请选择
                    </a-select-option>-->
                    <a-select-option value="AS">
                      AS
                    </a-select-option>
                    <a-select-option value="apusic">
                      apusic
                    </a-select-option>
                    <a-select-option value="Jboss">
                      Jboss
                    </a-select-option>
                    <a-select-option value="Tomcat">
                      Tomcat
                    </a-select-option>
                    <a-select-option value="TongWeb">
                      TongWeb
                    </a-select-option>
                    <a-select-option value="TongWeb5">
                      TongWeb5
                    </a-select-option>
                    <a-select-option value="Websphere">
                      Websphere
                    </a-select-option>
                     <a-select-option value="BES">
                      BES
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>

              <a-col :span="24" class="aCol">
                <a-form-item label="IP地址" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input  placeholder="请输入IP地址" v-decorator="['ip',validatorRules.ip]" class="aInp" />
                </a-form-item>
              </a-col>
              <a-col :span="24"  class="aCol">
                <a-form-item label="端       口" :labelCol="labelCol" :wrapperCol="wrapperCol" :required="true">
                  <a-input placeholder="请输入端口"  class="aInp"  v-decorator="['port',validatorRules.port]"/>
                </a-form-item>
              </a-col>

            <a-col :span="24" class="aCol">
              <a-form-item label="用户名" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input class="aInp" v-decorator="['jmxName']"/>
              </a-form-item>
            </a-col>
            <a-col :span="24" class="aCol">
              <a-form-item label="密       码" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input-password class="aInp" v-decorator="['jmxPwd', {
                    rules: [{ required: false,validator:this.jmxPwd ,trigger: 'blur'}]}]"></a-input-password>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row class="btnStyle">
            <a-col :span="24" :style="{ textAlign: 'center' }">
              <a-button type="shallow" @click="handelSubmit" :disabled="buttonDisadled">
                开始
              </a-button>
              <a-button :style="{ marginLeft: '8px' }" @click="handleReset">
                重置
              </a-button>
            </a-col>
          </a-row>
        </a-form>
      </a-col>
    </a-col>
    <a-col :span="12" class="contTwo">
      <div class="returnDiv">
        <p class="returnTitle">JMX连接测试</p>
        <p v-html="result" style="padding: 5px 16px 0 24px;">{{result}}</p>
      </div>
    </a-col>
  </a-row>
  <!--</page-layout>-->
</template>
<script>
  import ACol from 'ant-design-vue/es/grid/Col'
  import ARow from 'ant-design-vue/es/grid/Row'
  import ATextarea from 'ant-design-vue/es/input/TextArea'
  import {
    getAction
  } from '@/api/manage'
  import {
    type
  } from 'os';

  export default {
    components: {
      ATextarea,
      ARow,
      ACol
    },
    name: 'Printgzsld',
    props: {
      reBizCode: {
        type: String,
        default: ''
      },
      paramIp: {
        type: String,
        default: ''
      }
    },
    watch: {
      paramIp: {
        handler(nv) {
          this.$nextTick(()=>{
            this.form.setFieldsValue({
              ip: nv,
            })
          })
        },
        immediate: true
      }
    },
    data() {
      return {
        form: this.$form.createForm(this),
        model: {},
        labelCol: {
          xs: { span: 24 },
          sm:{span:24},
          md:{span:24},
          lg:{ span: 8 },
          xl: { span: 8 },
          xxl:{span: 6 }
        },
        wrapperCol: {
          xs: { span: 23 },
          sm: { span: 24 },
          md: { span: 24 },
          lg: { span: 16 },
          xl: { span: 16 },
          xxl:{span: 17 }
        },
        url: '/connect/testJmx',
        result: '',
        buttonDisadled: false,
        validatorRules: {
          ip: {
            rules: [
              { required: true, message: '请输入IP地址!' },
              {
                pattern: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
                message: '请输入正确的IP地址!',
              },
            ],
          },
          port: {
            rules: [
              { required: true, message: '端口不能为空！' },
              {pattern:/^[0-9]*$/,message: '请输入正确的端口号！'}
            ],
          },
        },
      }
    },
    created() {},
    methods: {
      jmxPwd(rule, value, callback) {
        let reg =/.*[\u4e00-\u9fa5]+.*$/;
        if(value){
          if(!reg.test(value)){
            callback();
          }
          else {
            callback('数据库密码不能为汉字!');
          }
        }
        else {
          callback();
        }
      },
      //提交方法
      handelSubmit() {
        this.form.validateFields((err, values) => {
          if (!err) {
            this.result = '';
            this.buttonDisadled = true;
            //参数
            let param = {
              ip: values.ip, // ip
              type: values.type, //类型
              port: values.port, //端口
              name: values.jmxName, //用户名
              pwd: values.jmxPwd, //密码
            }
            let pingUrl = window._CONFIG['domianURL'] + this.url;
            this.$http.get(pingUrl, {
              params: Object.assign(param)
            }).then(response => {
              if (response.success) {
                this.result = response.result
              }
              this.buttonDisadled = false;
            }, response => {
            });
          }})
      },
      //刷新
      handleReset() {
        this.form.resetFields()
        this.buttonDisadled = false;
        this.result = '';
      },
    }
  }
</script>
<style lang="scss" scoped>
  /*update_begin author:scott date:20191203 for:打印机打印的字体模糊问题 */
  * {
    color: #000000;
    -webkit-tap-highlight-color: #000000;
  }

  /*update_end author:scott date:20191203 for:打印机打印的字体模糊问题 */
  .importDiv {
    width: 60%;
    height: 14em;
    margin: 0 auto;
    border: 1px solid #d9d9d9;
    padding: 18px;
  }

  .returnDiv {
    height: 100%;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
  }

  .returnTitle {
    padding: 16px 24px;
    background-color: rgb(250, 250, 250);
    border-bottom: 1px solid rgb(232, 232, 232);
    font-size: 16px;
  }

  /* .returnDiv{
  width: 53%;
  height: 16em;
  margin: 0 auto;
  border: 1px solid #d9d9d9;
  padding: 18px;
  margin-top: 20px;
} */
  .leftSpan {
    width: 14%;
  }

  .abcdefg .ant-card-body {
    margin-left: 0%;
    margin-right: 0%;
    margin-bottom: 1%;
    border: 0px solid black;
    min-width: 800px;
    color: #000000 !important;
  }

  .explain {
    text-align: left;
    margin-left: 50px;
    color: #000000 !important;
  }

  .explain .ant-input,
  .sign .ant-input {
    font-weight: bolder;
  }

  .aCol {
    // height: 45px;
    margin-bottom: 5px;
  }

  .aCol:first-child {
    margin-top: 10px;
  }

  .btnStyle {
    margin-top: 24px;
  }

  .explain div {
    margin-bottom: 10px;
  }

  /* you can make up upload button and sample style by using stylesheets */
  .ant-upload-select-picture-card i {
    font-size: 32px;
    color: #999;
  }

  .ant-upload-select-picture-card .ant-upload-text {
    margin-top: 8px;
    color: #666;
  }

  .aInp {
    //width: 92%;
  }

  #printContent {
    height: 100%;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
  }

  .contTwo {
    margin-left: 16px;
    width: calc(100% - 50% - 16px);
    height: 100%;
  }
</style>