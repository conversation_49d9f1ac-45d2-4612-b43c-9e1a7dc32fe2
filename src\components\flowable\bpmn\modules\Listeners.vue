<template>
  <div>
    <a-table :columns="columns" :data-source="listeners" :pagination="false" rowKey="id">
      <template slot="handle" slot-scope="tex, record, index">
        <a-space class="listener-add">
          <span><a-icon type="delete" @click="deleteListener(index)" /></span>
          <span><a-icon type="edit" @click="editListener(record)" /></span>
        </a-space>
      </template>
    </a-table>
    <a-modal
      :title="title"
      :visible="_listenerShow"
      :width="'60%'"
      :maskClosable="false"
      @cancel="hideListener"
      @ok="addListener"
    >
      <div class="listener-form-con">
        <a-form :form="listener">
          <a-form-item>
            <span class="listener-form-item-label">事件类型：</span>
            <div class="listener-form-item-wrap">
              <a-select v-model="listener.eventType" :getPopupContainer='node=>node.parentNode'>
                <a-select-option v-for="item in eventTypes" :key="item.value" :value="item.value">
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </div>
          </a-form-item>
          <a-form-item>
            <span class="listener-form-item-label">监听类型：</span>
            <div class="listener-form-item-wrap">
              <a-select v-model="listener.listenerType" :getPopupContainer='node=>node.parentNode'>
                <a-select-option v-for="item in listenerTypes" :key="item.value" :value="item.value">
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </div>
          </a-form-item>
          <a-form-item>
            <span class="listener-form-item-label">值：</span>
            <div class="listener-form-item-wrap">
              <a-input v-model="listener.value" />
            </div>
          </a-form-item>
        </a-form>
        <div>
          <div class="params-title">
            <span class="listener-form-item-label">参数：</span>
            <span class="listener-add" slot="extra" @click.stop="showParamsForm">
              <a-icon type="plus-circle" /> 添加参数
            </span>
          </div>
          <div class="params-table">
            <span class="listener-form-item-label"></span>
            <span class="listener-form-item-wrap">
              <a-table
                ref="paramsTable"
                rowKey="id"
                :columns="paramsColumns"
                :data-source="listenerParams"
                :pagination="false"
              >
                <template slot="handle" slot-scope="text, record">
                  <a-space class="listener-add">
                    <span @click="deleteListenerParam(record)"><a-icon type="delete" /></span>
                    <span @click="editListenerParam(record)"><a-icon type="edit" /></span>
                  </a-space>
                </template>
              </a-table>
            </span>
          </div>
        </div>
      </div>
    </a-modal>
    <listener-param
      v-if="listenerParamsShow"
      :title="listenerParamsTitle"
      :listenerParamsShow="listenerParamsShow"
      :listenerParamOuter="listenerParamOuter"
      @changeListenerParams="hideListenerParams"
      @saveListenerParams="saveListenerParams"
    >
    </listener-param>
  </div>
</template>

<script>
import mixinPanel from '../mixins/mixinPanel'
import ListenerParam from './ListenerParam.vue'
import { randomString } from '@/utils/util'

export default {
  components: { ListenerParam },
  mixins: [mixinPanel],
  props: {
    listenerShow: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '',
    },
    type: {
      type: String,
      default: 'Execution',
    },
  },
  data() {
    return {
      isEdit: false,
      listeners: [],
      listenerType: {
        class: '类',
        expression: '表达式',
        delegateExpression: '代理表达式',
      },
      excutionEventTypes: [
        { value: 'start', label: '开始' },
        { value: 'take', label: '执行' },
        { value: 'end', label: '结束' },
      ],
      taskEventTypes: [
        { value: 'create', label: '创建' },
        { value: 'assignment', label: '指派' },
        { value: 'complete', label: '完成' },
        { value: 'delete', label: '删除' },
      ],

      listenerTypes: [
        { value: 'class', label: '类' },
        { value: 'expression', label: '表达式' },
        { value: 'delegateExpression', label: '代理表达式' },
      ],
      listener: {
        id: '',
        eventType: 'start',
        listenerType: 'class',
        value: '',
      },
      columns: [
        {
          title: '事件',
          dataIndex: 'eventType',
          key: 'eventType',
          scopedSlots: { customRender: 'eventType' },
        },
        {
          title: '类型',
          dataIndex: 'listenerType',
          key: 'listenerType',
          scopedSlots: { customRender: 'listenerType' },
        },
        {
          title: '操作',
          dataIndex: 'handle',
          key: 'handle',
          scopedSlots: { customRender: 'handle' },
        },
      ],
      showForm: false,
      listenerParamsShow: false,
      listenerParamsTitle: '参数',
      listenerParams: [],
      listenerParamOuter: {
        id: '',
        name: '',
        paramType: 'string',
        value: '',
      },
      paramsColumns: [
        {
          title: '名称',
          dataIndex: 'name',
          key: 'name',
          scopedSlots: { customRender: 'name' },
        },
        {
          title: '类型',
          dataIndex: 'paramType',
          key: 'paramType',
          scopedSlots: { customRender: 'paramType' },
        },
        {
          title: '值',
          dataIndex: 'value',
          key: 'value',
          scopedSlots: { customRender: 'value' },
        },
        {
          title: '操作',
          dataIndex: 'handle',
          key: 'handle',
          scopedSlots: { customRender: 'handle' },
        },
      ],
    }
  },
  computed: {
    _listenerShow: {
      get() {
        return this.listenerShow
      },
      set(v) {
        this.$emit('changeListenerShow', v)
      },
    },
  },
  created() {
    this.eventTypes = this.type === 'Execution' ? this.excutionEventTypes : this.taskEventTypes
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.isEdit = false
      this.eventTypes = this.type === 'Execution' ? this.excutionEventTypes : this.taskEventTypes
      this.listener = {
        id: randomString(32),
        eventType: this.type === 'Task' ? 'create' : 'start',
        listenerType: 'class',
        value: '',
      }
      this.listenerParams = []
      this.listeners = []
      if (this.element.businessObject.extensionElements) {
        this.listeners = this.element.businessObject.extensionElements.values
          .filter((item) => item.$type === this.descriptorPrefix + this.type + 'Listener')
          .map((item) => {
            let type
            if ('class' in item) type = 'class'
            if ('expression' in item) type = 'expression'
            if ('delegateExpression' in item) type = 'delegateExpression'
            return {
              id: item.id,
              eventType: item.event,
              listenerType: type,
              value: item[type],
              params: item.fields
                ? item.fields.map((field) => {
                    let fieldType, value
                    if ('string' in field) {
                      fieldType = 'string'
                      value = field.string.body
                    }
                    if ('expression' in field) {
                      fieldType = 'expression'
                      value = field.expression.body
                    }
                    return {
                      id: field.id,
                      name: field.name,
                      paramType: fieldType,
                      value: value,
                    }
                  })
                : [],
            }
          })
      }
      // this.addListener()
      // console.log('初始化监听事件 === ', this.listeners)
    },
    addListener() {
      // let val = {
      //   id: randomString(32),
      //   eventType: undefined,
      //   listenerType: undefined,
      //   value: '',
      // }
      if (!this.isEdit) {
        this.listeners.push(this.listener)
      }
      this.save()
      // this.listener = val
      // this.listenerParams = []
    },
    deleteListener(idx) {
      this.listeners.splice(idx, 1)
      this.listenerParams = []
      this.save()
    },
    editListener(item) {
      this.isEdit = true
      this.listener = item
      this.listenerParams = item.params || []
      this._listenerShow = true
    },
    hideListener() {
      this._listenerShow = false
    },
    hideListenerParams(v) {
      this.listenerParamsShow = v
    },
    showParamsForm() {
      this.listenerParamOuter = {
        id: randomString(32),
        name: '',
        paramType: 'string',
        value: '',
      }
      this.listenerParamsTitle = '添加参数'
      this.listenerParamsShow = true
    },
    saveListenerParams(v) {
      if (v) {
        if (this.listenerParams.find((el) => v.id === el.id)) {
          console.log('保存啊啊啊有这个参数', v)
        } else {
          this.listenerParams.push(v)
          this.listener.params = this.listenerParams
        }
      }
    },
    deleteListenerParam(row) {
      let index = this.listenerParams.indexOf(row)
      this.listenerParams.splice(index, 1)
    },
    editListenerParam(row) {
      this.listenerParamOuter = row
      this.listenerParamsShow = true
    },
    save() {
      let extensionElements = this.modeler.get('moddle').create('bpmn:ExtensionElements')
      let tem = this.element.businessObject.get('extensionElements')
      // if (this.isEdit) {
      //   let item = tem.values.find((el) => el.id == this.listener.id)
      //   item.event = this.listener.eventType
      //   let types = ['class', 'expression', 'delegateExpression']
      //   let type = types.find((el) => el in item)
      //   if (type !== this.listener.listenerType) {
      //     delete item[type]
      //   }
      //   item[this.listener.listenerType] = this.listener.value
      //   item.fields = []
      //   this.addParams(item, this.listener)
      //   // console.log('编辑时保存 === ', tem, this.listener)
      //   this._listenerShow = false
      //   return
      // }
      // if (!extensionElements) {
      //   extensionElements = this.modeler.get('moddle').create('bpmn:ExtensionElements')
      // }
      extensionElements.values = []
      if (tem && tem.values.length > 0) {
        extensionElements.values = tem.values.filter(
          (item) => item.$type !== this.descriptorPrefix + this.type + 'Listener'
        )
      }
      // extensionElements.values = extensionElements.values
      //   ? extensionElements.values.filter((item) => item.$type !== this.descriptorPrefix + this.type + 'Listener')
      //   : []
      this.listeners.forEach((item) => {
        const listener = this.modeler.get('moddle').create(this.descriptorPrefix + this.type + 'Listener')
        listener['event'] = item.eventType
        listener[item.listenerType] = item.value
        listener['id'] = item.id
        this.addParams(listener, item)
        extensionElements.get('values').push(listener)
      })
      this.updateProperties({
        extensionElements:
          extensionElements.get('values') && extensionElements.get('values').length > 0 ? extensionElements : undefined,
      })
      this._listenerShow = false
    },
    addParams(listener, item) {
      if (item.params && item.params.length) {
        item.params.forEach((field) => {
          const fieldElement = this.modeler.get('moddle').create(this.descriptorPrefix + 'Field')
          fieldElement['name'] = field.name
          fieldElement['id'] = field.id
          // fieldElement[field.type] = field.value
          // 注意：flowable.json 中定义的string和expression类为小写，不然会和原生的String类冲突，此处为hack
          const valueElement = this.modeler
            .get('moddle')
            .create(this.descriptorPrefix + field.paramType, { body: field.value })
          // fieldElement[field.type] = valueElement
          fieldElement.set(field.paramType, valueElement)
          // fieldElement.push(valueElement)
          listener.get('fields').push(fieldElement)
        })
      }
    },
  },
}
</script>

<style lang="less" scoped>
.listener-add {
  color: #1890ff;
  cursor: pointer;
}
.listener-form-con {
  height: 50vh;
  .listener-form-item-label {
    display: inline-block;
    width: 80px;
    text-align: right;
  }
  .listener-form-item-wrap {
    display: inline-block;
    width: calc(100% - 80px);
  }
  .params-title {
    display: flex;
    justify-content: space-between;
  }
  .params-table {
    margin-top: 8px;
  }
  .listener-add {
    color: #1890ff;
    cursor: pointer;
  }
}
</style>