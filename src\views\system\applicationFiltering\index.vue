<template>
  <div style="height:100%">
    <keep-alive exclude='ApplicationFilteringDetail'>
      <component style="height:100%" :is="pageName" :data="data"/>
    </keep-alive>
  </div>
</template>
<script>
import ApplicationFilteringList from './ApplicationFilteringList.vue'
import ApplicationFilteringDetail from './ApplicationFilteringDetail.vue'
export default {
  name: "ApplicationFiltering",
  data() {
    return {
      isActive: 0,
      data: {},
    };
  },
  components: {
    ApplicationFilteringList,
    ApplicationFilteringDetail
  },
  created() {
    this.pButton1(0);
  },
  //使用计算属性
  computed: {
    pageName() {
      switch (this.isActive) {
        case 0:
          return "ApplicationFilteringList";
        default:
          return "ApplicationFilteringDetail";
      }
    }
  },
  methods: {
    pButton1(index) {
      this.isActive = index;
    },
    pButton2(index, item) {
      this.isActive = index;
      this.data = item
    }
  }
}
</script>