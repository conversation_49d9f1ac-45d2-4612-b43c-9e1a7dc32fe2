<template>
  <a-spin :spinning='confirmLoading'>
    <j-form-container :disabled='formDisabled'>
      <a-form :form='form' slot='detail'>
        <a-row>
          <a-col :span='24'>
            <a-form-item label='功能标识' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <a-input v-decorator="['code',validatorRules.code]" placeholder='请输入功能标识' :allowClear='true' autocomplete='off'/>
            </a-form-item>
          </a-col>
          <a-col :span='24'>
            <a-form-item label='功能名称' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <a-input v-decorator="['name',validatorRules.name]" placeholder='请输入功能名称' :allowClear='true' autocomplete='off'/>
            </a-form-item>
          </a-col>
          <a-col :span='24'>
            <a-form-item label='传输协议' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <a-select :getPopupContainer="(node) => node.parentNode"
                        allowClear
                        v-decorator="['protocolId',validatorRules.protocol]"
                        placeholder="请选择传输协议"
                        @change="changeProtocol">
                <a-select-option v-for="item in productInfo.selectedProtocolList" :key="item.transferProtocolId">
                  {{item.transferProtocol}}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span='24'>
            <a-form-item label='是否异步' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <a-radio-group placeholder="请选择是否异步" v-decorator="['isAsync', { initialValue: '0' }]">
                <a-radio value="0">否</a-radio>
                <a-radio value="1">是</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
          <a-col :span='24'>
            <a-form-item label='方法名称' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <a-input v-decorator="['methodName',validatorRules.methodName]" placeholder='请输入方法名称' :allowClear='true' autocomplete='off'></a-input>
            </a-form-item>
          </a-col>
          <a-col v-if='functionParameterList.length&&functionParameterList.length>0' :span='24'>
            <function-metadata :functionParameterList='functionParameterList'
                               @func='changeParameterList'></function-metadata>
          </a-col>
          <a-col :span='24'>
            <div style='text-align: center;margin-bottom: 10px;'>
              <a @click='addParameter'>
                <a-icon type='plus' />
                添加参数</a>
            </div>
          </a-col>
          <a-col :span='24'>
            <a-form-item label='描述' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <a-textarea
                v-decorator="['description',validatorRules.description]"
                :auto-size="{ minRows: 2, maxRows: 5 }"
                placeholder='请输入描述'
                :allowClear='true'
                autocomplete='off'
              ></a-textarea>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>

    <div
      v-if='!disabled'
      :style="{
          position: 'relative',
          right: 0,
          bottom: 0,
          width: '100%',
          borderTop: '1px solid #e9e9e9',
          padding: '10px 16px',
          background: '#fff',
          textAlign: 'right',
          zIndex: 1,
        }"
    >
      <a-button @click='pClose(false)' type='primary' :loading='loading' ghost style='margin-right: 0.8rem'>取消
      </a-button>
      <a-button @click='submitForm' type='primary' :loading='loading'>保存</a-button>
    </div>
    <function-input-parameter-modal ref='modalForm' @ok='modalFormOk'></function-input-parameter-modal>
  </a-spin>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import pick from 'lodash.pick'
import JFormContainer from '@/components/jeecg/JFormContainer'
import JDictSelectTag from '@/components/dict/JDictSelectTag'
import FunctionInputParameterModal from '@views/devicesystem/modules/FunctionInputParameterModal'
import { ValidateOptionalFields, ValidateRequiredFields } from '@/utils/rules'
export default {
  name: 'FunctionDefinitionForm',
  components: {
    FunctionInputParameterModal,
    JFormContainer,
    JDictSelectTag,
    FunctionMetadata: () => import('@views/devicesystem/modules/FunctionMetadata.vue'),
  },
  props: {
    //流程表单data
    formData: {
      type: Object,
      default: () => {
      },
      required: false
    },
    // 表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false
    },
    // 表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    },
  },
  data() {
    return {
      form: this.$form.createForm(this),
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      confirmLoading: false,
      functionParameterList: [],//参数列表
      validatorRules: {
        code: {
          rules: [
            {
              required: true,
              validator: (rule, value, callback) => ValidateRequiredFields(rule, value, callback, '功能标识', 32, 2)
            }]
        },
        name: {
          rules: [
            {
              required: true,
              validator: (rule, value, callback) => ValidateRequiredFields(rule, value, callback, '功能名称', 32, 2)
            }]
        },
        protocol: {
          rules: [
            { required: true, message: '请选择传输协议!' }
          ]
        },
        methodName: {
          rules: [
            {
              required: true,
              validator: (rule, value, callback) => ValidateRequiredFields(rule, value, callback, '方法名称', 50, 2)
            }]
        },
        description: {
          rules: [
            {
              required: false,
              validator: (rule, value, callback) => ValidateOptionalFields(rule, value, callback, '描述', 200)
            }
          ]
        },
      },
      url: {
        add: '/product/deviceControlCommand/addCommand',
        edit: '/product/deviceControlCommand/editCommand',
      },
      loading: false,
      functionId:undefined,
      protocolName:undefined,
      productInfo:{},
    }
  },
  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    }
  },
  methods: {
    add(index) {
      this.edit({},index)
    },
    edit(record,index) {
      this.form.resetFields()
      this.functionId=record.id
      this.functionParameterList=record.deviceControlCommandExtendList?record.deviceControlCommandExtendList:[]
      if (record.protocol) {
        this.protocolName=record.protocol.trim()
      }
      this.model = Object.assign({}, record)
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(
          pick(
            this.model,
            'code',
            'name',
            'protocolId',
            'isAsync',
            'methodName',
            'description'
          ))
      })
    },
    changeParameterList(data) {
      this.functionParameterList = data
    },
    changeProtocol(value,extra){
      this.protocolName=value?extra.componentOptions.children[0].text.trim():undefined
    },
    pClose() {
      const that = this
      that.$emit('ok')
    },
    addParameter() {
      const that = this
      that.$refs.modalForm.parameterList=that.functionParameterList
      that.$refs.modalForm.add(that.functionParameterList.length)

      that.$refs.modalForm.title = '新增'
      that.$refs.modalForm.disableSubmit = false
    },
    submitForm() {
      const that = this
      // 触发表单验证
      that.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!that.model.id) {
            httpurl += that.url.add
            method = 'post'
          } else {
            httpurl += that.url.edit
            //method = 'put'
            method = 'post'
          }
          let formData = Object.assign(that.model, values)
          formData.productId=that.productInfo.id
          formData.id=that.functionId
          formData.protocol=that.protocolName
          formData.deviceControlCommandExtendList=that.functionParameterList
          httpAction(httpurl, formData, method).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.$emit('ok')
            } else {
              that.$message.warning(res.message)
            }
            that.confirmLoading = false
          }).catch((err) => {
            that.$message.warning(err.message)
          })
        }
      })
    },
    popupCallback(row) {
      this.form.setFieldsValue
      (pick(
        row,
        'code',
        'name',
        'is_async',
        'methodName',
      ))
    },
    modalFormOk(info) {
      console.log('info===',info)
      // 新增/修改 成功时，重载列表
      this.functionParameterList.push(info)
      console.log('this.functionParameterList===',this.functionParameterList)
    }
  }
}
</script>
<style lang='less' scoped>
::v-deep .two-words > div > label {
  letter-spacing: 4px;
}

::v-deep .two-words > div > label::after {
  letter-spacing: 0px;
}
</style>