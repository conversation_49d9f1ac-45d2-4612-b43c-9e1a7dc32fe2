<template>
  <a-row>
    <a-col>
      <!-- table区域 -->
      <a-table
        ref='table'
        bordered
        :rowKey='(record,index)=>{return index.toString()}'
        :columns='columns'
        :dataSource='dataSource'
        :scroll='dataSource.length > 0 ? { x:"max-content"} : {}'
        :pagination='ipagination'
        :loading='loading'
        @change='handleTableChange'
        style='margin-bottom: 10px;margin-top: 10px'
      >
      </a-table>
    </a-col>
  </a-row>
</template>
<script>
import { NoLoadDataListMixin } from '@/mixins/NoLoadDataListMixin'
import { ajaxGetDictItems } from '@api/api'
import { getAction } from '@/api/manage'

export default {
  name: 'LogList',
  mixins: [NoLoadDataListMixin],
  data() {
    return {
      // 表头
      columns: [
        {
          title: '操作内容',
          dataIndex: 'operation'
        },
        {
          title: '操作类型',
          dataIndex: 'operationType'
        },
        {
          title: '资产状态',
          dataIndex: 'operationStatus',
          sorter: true
        },
        {
          title: '操作人',
          dataIndex: 'createByText'
        },
        {
          title: '操作时间',
          dataIndex: 'createTime',
          sorter: true
        }
      ],
      url: {
        list: '/ledger/ledgerLog/list'
      },
      //资产状态
      QueryStatus: []
    }
  },
  mounted() {
    this.getDicData()
  },
  methods: {
    //加载列表
    loadList(id) {
      this.queryParam.manageId = id
      this.loadData(1)
    },
    //刷新
    Refresh() {
    },
    handleChange(value) {
    },
    onTimeChange(date, dateString) {
      //查询质保开始时间范围
      this.queryParam.queryWarrantyStartTimeFrom = dateString[0]
      this.queryParam.queryWarrantyStartTimeTo = dateString[1]
    },
    //日期调用方法
    getCurrentStyle(current, today) {
      const style = {}
      if (current.date() === 1) {
        style.border = '1px solid #1890ff'
        style.borderRadius = '50%'
      }
      return style
    },
    //单个修改资产状态
    handleChangeStatus(id) {
      this.$refs.AssetsStatusEdit.edit(id)
      this.$refs.AssetsStatusEdit.title = '修改资产状态'
      this.$refs.AssetsStatusEdit.disableSubmit = false
    },
    //批量修改资产状态
    batchChangeStatus: function() {
      if (this.selectedRowKeys.length <= 0) {
        this.$message.warning('请至少选择一条记录！')
        return
      } else {
        var ids = ''
        for (var a = 0; a < this.selectedRowKeys.length; a++) {
          ids += this.selectedRowKeys[a] + ','
        }
        this.$refs.AssetsStatusEdit.batchEdit(ids)
        this.$refs.AssetsStatusEdit.title = '批量修改资产状态'
        this.$refs.AssetsStatusEdit.disableSubmit = false
      }
    },

    //详情页面
    handleAssetsManagementView(id) {
      this.$refs.AssetsManagementView.show(id)
      this.$refs.AssetsManagementView.title = '工单详情'
      this.$refs.AssetsManagementView.disableSubmit = false
    },
    //字典获取选择框内容--------------------------------------------
    getDicData() {
      this.getQueryStatus('asset_queryStatus')
    },
    getQueryStatus(code) {
      let that = this
      ajaxGetDictItems(code, null).then(res => {
        if (res.success) {
          that.QueryStatus = res.result
        } else {
          that.$message.error('资产状态字典信息获取失败')
        }
      })
    },
    //字典获取选择框内容-----------------------------------------end
    //属地---------------------------------------------
    onChangeCascader(value) {
    }
    //属地---------------------------------------------end
  }
}
</script>
<style lang='less' scoped>
/*给table列设置宽度*/
::v-deep .ant-table-scroll .ant-table-thead > tr > th,
::v-deep .ant-table-scroll .ant-table-tbody > tr > td {
  /*操作内容*/

  &:nth-child(1) {
    min-width: 100px !important;
    max-width: 300px !important;
  }

  /*操作类型*/

  &:nth-child(2) {
    min-width: 100px !important;
    max-width: 250px !important;
  }

  /*资产状态*/

  &:nth-child(3) {
    min-width: 100px !important;
    max-width: 250px !important;
  }

  /*操作人*/

  &:nth-child(4) {
    min-width: 100px !important;
    max-width: 250px !important;
  }

  /*操作时间*/

  &:nth-child(5) {
    min-width: 100px !important;
    max-width: 300px !important;
  }
}

/*内容对齐方式、省略显示*/
::v-deep .ant-table-tbody > tr > td {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;

  &:nth-child(-n+5) {
    text-align: center;
  }
}

/*表头样式*/
::v-deep .ant-table-thead > tr > th {
  text-align: center;
  white-space: nowrap;
}
</style>
