<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container>
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-item class="two-words" label="标识" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['name', validatorRules.name]" placeholder="请输入标识" :allowClear="true"
                autocomplete="off"/>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item class="two-words" label="名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['displayName', validatorRules.displayName]" placeholder="请输入名称" :allowClear="true"
                autocomplete="off"/>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="文件" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <div class="clearfix">
                <a-upload :file-list="fileList" v-decorator="['file',validatorRules.uploadFiles]" :remove="handleRemove" :before-upload="beforeUpload">
                  <a-button> <a-icon type="upload" />点击上传 </a-button>
                </a-upload>
              </div>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
  import {
    uploadAction,
    getAction
  } from '@/api/manage'
  import pick from 'lodash.pick'

  export default {
    name: 'ProductExportForm',
    components: {},
    props: {
      // 流程表单data
      formData: {
        type: Object,
        default: () => {},
        required: false,
      },
      // 表单模式：true流程表单 false普通表单
      formBpm: {
        type: Boolean,
        default: false,
        required: false,
      },
      // 表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false,
      },
    },
    data() {
      return {
        form: this.$form.createForm(this),
        model: {},
        labelCol: {
          xs: {
            span: 24,
          },
          sm: {
            span: 5,
          },
        },
        wrapperCol: {
          xs: {
            span: 24,
          },
          sm: {
            span: 16,
          },
        },
        confirmLoading: false,
        fileList: [],
        uploading: false,
        validatorRules: {
          name: {
            rules: [{
              required: true,
              pattern: /^([a-zA-Z][a-zA-Z0-9_]{4,63})$/,
              message: '以字母开头，可包含字母、数字或下划线，5-64个字符',
              trigger: 'blur'
            }]
          },
          displayName: {
            rules: [{
              required: true,
              validator: (rule, value, callback) => {
                if (value) {
                  if (value.trim() === '') {
                    return callback('名称不能全为空格！');
                  } else if (value !== value.trim()&&value.trim()!=='') {
                    return callback('名称前后不能有空格！');
                  } else if (value.length > 20 || value.length < 2) {
                    callback('名称长度应在 2-20 个字符之间！')
                  } else {
                    callback()
                  }
                } else {
                  callback('请输入名称!')
                }
              }
            }]
          },
          uploadFiles: {
            rules: [{
              required: true,
              message: '请上传文件',
            }, ],
          },
        },
        url: {
          copyForProductModel: '/product/product/importProduct',
        },
      }
    },
    created() {},
    methods: {
      init() {
        this.form.resetFields()
        this.visible = true
        this.$nextTick(() => {
          this.form.setFieldsValue(
            pick(
              'name',
              'displayName',
              'file'
            )
          )
        })
      },
      submitForm() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            this.$emit('loading', true)
            const formData = new FormData();
            console.log('this.fileList[0]===',this.fileList[0])
            formData.append('file', this.fileList[0]);
            formData.append('name', values.name?values.name:'');
            formData.append('displayName', values.displayName?values.displayName:'');
            uploadAction(this.url.copyForProductModel,formData)
              .then((res) => {
                if (res.success) {
                  that.$message.success(res.message)
                  that.$emit('ok')
                } else {
                  that.$message.warning(res.message)
                }
              })
              .finally(() => {
                that.confirmLoading = false
                this.$emit('loading', false)
              });
          }
        })
      },
      handleRemove(file) {
        const index = this.fileList.indexOf(file);
        const newFileList = this.fileList.slice();
        newFileList.splice(index, 1);
        this.fileList = newFileList;
      },
      beforeUpload(file) {
        this.fileList = [file];
        console.log('file===',file)
        return false;
      },
    }
  }
</script>
<style lang='less' scoped>
  @import '~@assets/less/scroll.less';

  ::v-deep .two-words>div>label {
    letter-spacing: 4px;
  }

  ::v-deep .two-words>div>label::after {
    letter-spacing: 0px;
  }

  ::v-deep .ant-upload-list-item-info>span {
    display: flex;
    justify-content: center;
  }

  ::v-deep .ant-upload-list-picture-card .ant-upload-list-item-thumbnail,
  ::v-deep .ant-upload-list-picture-card .ant-upload-list-item-thumbnail img {
    position: relative;
    display: flex;
    width: 90%;
    height: auto;
    left: 0px;
    top: 0px !important;
    align-items: center;
    justify-content: center;
  }

  .jobChange {
    background-color: #f5f5f5;
  }
</style>