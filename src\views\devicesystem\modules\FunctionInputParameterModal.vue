<template>
  <a-drawer
    :title="title"
    :maskClosable="true"
    :destroyOnClose="true"
    width='auto'
    placement="right"
    :closable="true"
    @close="close"
    :visible="visible"
    style="overflow: auto;padding-bottom: 53px">
    <function-parameter-form ref="realForm"  :parameter-list='parameterList' @ok="submitCallback" :disabled="disableSubmit"></function-parameter-form>
    <div :style="{
          position: 'relative',
          right: 0,
          bottom: 0,
          width: '100%',
          borderTop: '1px solid #e9e9e9',
          padding: '10px 16px',
          background: '#fff',
          textAlign: 'right',
          zIndex: 1,
        }">
      <a-button @click="handleCancel" style="margin-right:30px;">取消</a-button>
      <a-button v-if="!disableSubmit"   @click="handleOk" type="primary">确认</a-button>
    </div>
  </a-drawer>
</template>

<script>

import FunctionParameterForm from './FunctionParameterForm'
export default {
  name: 'FunctionInputParameterModal',
  components: {
    FunctionParameterForm
  },

  data () {
    return {
      title:'',
      width:800,
      visible: false,
      disableSubmit: false,
      parameterList: [],
    }
  },

  methods: {
    add (index) {
      this.visible=true
      this.$nextTick(()=>{
        this.$refs.realForm.add(index,this.parameterList);
      })
    },
    edit (record,index) {
      this.visible=true
      this.$nextTick(()=>{
        this.$refs.realForm.edit(record,index,this.parameterList);
      })
    },
    close () {
      this.$emit('close');
      this.visible = false;
    },
    handleOk () {
      this.$refs.realForm.submitForm();
    },
    submitCallback(info,index){
      console.log('info,index===',info,index)
      this.$emit('ok',info,index);
      this.visible = false;
    },
    handleCancel () {
      this.close()
    }
  }
}
</script>
<style  scoped>
::v-deep .ant-drawer-content-wrapper{
  min-width: 400px !important;
  max-width: 650px !important
}
</style>
