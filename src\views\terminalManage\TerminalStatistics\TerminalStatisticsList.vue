<template>
  <a-row :gutter='10' style='height: 100%;' class='vScroll'>
    <a-col style='width:100%;height: 100%;display: flex;flex-direction: column'>
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <!-- 查询区域 -->
        <div class='table-page-search-wrapper-style'>
          <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
            <a-row :gutter='24' ref='row'>
              <a-col :span='spanValue'>
                <a-form-item label='操作系统'>
                  <a-select :getPopupContainer='(node) => node.parentNode' allowClear v-model='osTypeValues'
                    mode='multiple' :maxTagCount='1' :maxTagTextLength="6" placeholder='请选择操作系统'
                    @change='setQueryParam($event, "osType")'>
                    <a-select-option v-for='i in osTypeList' :key='"osType_" + i.value' :value='i.value'>
                      {{ i.text }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label='cpu类型'>
                  <a-select :getPopupContainer='(node) => node.parentNode' allowClear v-model='cpuTypeValues'
                    mode='multiple' :maxTagCount='1' :maxTagTextLength="6" placeholder='请选择cpu类型'
                    @change='setQueryParam($event, "cpuType")'>
                    <a-select-option v-for='i in cpuTypeList' :key='"cpuType_" + i.value' :value='i.value'>
                      {{ i.text }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label='设备状态'>
                  <a-select placeholder='请选择设备状态' allowClear v-model='queryParam.deviceType'
                    :getPopupContainer="(node) => node.parentNode">
                    <a-select-option :key='0' value='开机'>开机</a-select-option>
                    <a-select-option :key='1' value='关机'>关机</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span='spanValue' v-show='toggleSearchStatus'>
                <a-form-item label='终端名称'>
                  <!-- <a-input :maxLength='maxLength' placeholder="请输入搜索关键词" v-model="queryParam.title"></a-input> -->
                  <a-input :maxLength='maxLength' placeholder='请输入终端名称' v-model='queryParam.name' :allowClear='true' autocomplete='off'>
                  </a-input>
                </a-form-item>
              </a-col>

              <a-col :span='spanValue' v-show='toggleSearchStatus'>
                <a-form-item label='选择日期'>
                  <div>
                    <a-range-picker class='a-range-picker-choice-date' :key='updateKey' :ranges="{
                      昨天: [
                        moment().startOf('day').subtract(1, 'days'),
                        moment().endOf('day').subtract(1, 'days')
                      ],
                      '7天': [
                        moment().startOf('day').subtract(1, 'weeks'),
                        moment().startOf('day').subtract(1, 'days'),
                      ],
                      '30天': [
                        moment().startOf('day').subtract(30, 'days'),
                        moment().startOf('day').subtract(1, 'days'),
                      ],
                    }" @change='onChangePicker' :dropdownClassName="'yq-picker'" :disabledDate='disabledDate'
                      v-model='queryParam.createTimeRange' :default-value="[startTime, endTime]" />
                  </div>
                </a-form-item>
              </a-col>

              <a-col :span='spanValue' v-show='toggleSearchStatus'>
                <a-form-item label='终端IP'>
                  <a-input :maxLength='maxLength' placeholder='请输入终端IP' v-model='queryParam.ip' :allowClear='true' autocomplete='off'>
                  </a-input>
                </a-form-item>
              </a-col>

              <a-col :span='spanValue' v-show='toggleSearchStatus'>
                <a-form-item label='SN'>
                  <a-input :maxLength='maxLength' placeholder='请输入SN' v-model='queryParam.sn' :allowClear='true' autocomplete='off'></a-input>
                </a-form-item>
              </a-col>

              <a-col :span='spanValue' v-show='toggleSearchStatus'>
                <a-form-item label='所属网关'>
                  <!-- <a-input placeholder="请输入搜索关键词" v-model="queryParam.title"></a-input> -->
                  <a-input :maxLength='maxLength' placeholder='请输入网关' v-model='queryParam.gatewayCode' :allowClear='true' autocomplete='off'>
                  </a-input>
                </a-form-item>
              </a-col>

              <a-col :span='spanValue' v-show='toggleSearchStatus'>
                <a-form-item label='单位'>
                  <a-tree-select :getPopupContainer='(node) => node.parentNode' v-model='searchedDepKey'
                    :maxTagCount='1' :dropdownMatchSelectWidth="true" tree-node-filter-prop='title'
                    :replaceFields='replaceFields' :treeData='departsTreeData' show-search
                    :searchValue='bSearchedDepKey' multiple :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                    placeholder='请选择单位' allow-clear @change='onChangeDeparts' @search='onSearchDeparts'
                    @select='onSelectDeparts'>
                  </a-tree-select>
                </a-form-item>
              </a-col>

              <a-col :span='spanValue' v-show='toggleSearchStatus'>
                <a-form-item label='使用人'>
                  <!-- <a-input placeholder="请输入搜索关键词" v-model="queryParam.title"></a-input> -->
                  <!-- <a-input placeholder='请输入使用人' v-model='queryParam.username' :allowClear='true' autocomplete='off'>
                  </a-input> -->
                  <a-select v-model="queryParam.username" :getPopupContainer='node => node.parentNode'
                      :allow-clear='true' placeholder="请选择使用人" show-search option-filter-prop="children">
                      <a-select-option v-for="(item, key) in userList" :key="key" :value="item.username">
                        <div :title="item.realname">{{ item.realname }}</div>
                      </a-select-option>
                    </a-select>
                </a-form-item>
              </a-col>

              <a-col :span='colBtnsSpan()'>
                <span class='table-page-search-submitButtons'
                  :style="toRight && { float: 'right', overflow: 'hidden' } || {}">
                  <a-button type='primary' class='btn-search btn-search-style' @click='dosearch'>查询</a-button>
                  <a-button class='btn-reset btn-reset-style' @click='doReset'>重置</a-button>
                  <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>

      <a-card :bordered='false' style='width:100%;flex: auto'>
        <!-- 操作按钮区域 -->
        <div class='table-operator table-operator-style'>
          <a-button @click='selfExport' :loading='loading'>导出</a-button>
          <!-- <a-dropdown v-if="selectedRowKeys.length > 0">
              <a-menu slot="overlay" style='text-align: center'>
                <a-menu-item key="1" @click="batchDel">
                  删除
                </a-menu-item>
              </a-menu>
              <a-button> 批量操作
                <a-icon type="down" />
              </a-button>
            </a-dropdown> -->
        </div>
        <!-- table区域-begin -->
        <a-table ref='table' class="terminal-table" bordered :rowKey='(record, index) => { return index }' :columns='columns'
          :dataSource='dataSource' :scroll='dataSource.length > 0 ? { x: "max-content" } : {}' :pagination='ipagination'
          :loading='loading' @change='handleTableChange'>
          <template slot='htmlSlot' slot-scope='text'>
            <div v-html='text'></div>
          </template>
          <template slot='onTotal' slot-scope='text'>
            <span v-if='text' style='font-size: 14px'>
              {{ (parseInt(text) / 60).toFixed(2) }}
            </span>
          </template>

          <div slot='filterDropdown'>
            <a-card>
              <div style='text-align: right;font-size: small;color: #AAAAAC'>拖拽可进行排序</div>
              <a-divider style='margin: 5px 0px 5px 0px'></a-divider>
              <a-checkbox-group @change='onColSettingsChange' v-model='settingColumns' :key="refreshKey">
                <a-row style='width: 170px;max-height:350px;overflow-y: auto '>
                  <a-col v-for='(item, idx) in defColumns' v-if="item.key !== 'rowIndex' && item.dataIndex !== 'action'"
                    :span='24' @drop='drop($event, idx)' @dragover='allowDrop($event)' :key='"defColumns_" + idx'>
                    <div draggable='true' @dragstart='dragStart($event, idx)' @dragend='dragEnd($event, idx)'
                      :key="'defColumns_draggable_' + idx">
                      <a-checkbox :disabled='item.disabled' :value='item.dataIndex' :key='"defColumns_checkbox_" + idx'>
                        {{ item.title }}
                      </a-checkbox>
                    </div>
                  </a-col>
                </a-row>
              </a-checkbox-group>
            </a-card>
          </div>
          <a-icon slot='filterIcon' type='setting' :style="{ fontSize: '16px', color: '#108ee9' }" title='动态列表显示' />

          <template slot='fileSlot' slot-scope='text'>
            <span v-if='!text' style='font-size: 14px'>无文件</span>
            <a-button v-else :ghost='true' type='primary' icon='download' size='small' @click='downloadFile(text)'>
              下载
            </a-button>
          </template>
          <!-- <span
              slot="action"
              slot-scope="text, record"
              class="caozuo"
            >
              <a @click="handleDetailPage(record)">查看</a>
              <a-divider type="vertical" />
              <a @click="handleEdit(record)">编辑</a>
              <a-divider type="vertical" />
              <a-popconfirm
                title="确定删除吗?"
                @confirm="() => handleDelete(record.id)"
              >
                <a>删除</a>
              </a-popconfirm>
            </span> -->
          <template slot='tooltip' slot-scope='text'>
            <a-tooltip placement='topLeft' :title='text' trigger='hover'>
              <div class='tooltip'>
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
      </a-card>
    </a-col>
  </a-row>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import moment from 'moment'
import { getAction } from '@/api/manage'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
import { addCancelDebounce } from '@/utils/util'
import { ajaxGetDictItems,getUserList } from '@/api/api'
import Vue from 'vue'
export default {
  name: 'TerminalStatisticsList',
  mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
  data() {
    return {
      maxLength:50,
      description: '终端统计',
      formItemLayout: {
        labelCol: {
          style: 'width:80px'
        },
        wrapperCol: {
          style: 'width:calc(100% - 80px)'
        }
      },
      updateKey: 0,
      startTime: moment().startOf('day').subtract(1, 'days'), // 创建起始时间-筛选框
      endTime: moment().endOf('day').subtract(1, 'days'), // 创建结束时间-筛选框

      //单位tree
      searchedDepKey: undefined,
      bSearchedDepKey: '',
      departsTreeData: [],
      replaceFields: {
        children: 'children',
        title: 'deptName',
        key: 'deptId',
        value: 'deptId'
      },

      osTypeList: [],
      osTypeValues: undefined,

      cpuTypeList: [],
      cpuTypeValues: undefined,

      //刷新列表操作设置
      refreshKey: 0,
      dragItemIndex: 0,
      dragDom: null,
      isDown: 1,
      debounceCalWidth: null,
      disLength: 2,
      exportFields: '',
      //表头
      columns: [],
      //列设置
      settingColumns: [],
      defColumns: [
        {
          title: '终端名称',
          dataIndex: 'name',
          disabled: false,
          checked: true
        },
        {
          title: 'SN',
          dataIndex: 'SN',
          sorter: true,
          disabled: false,
          checked: true,
        },
        {
          title: '终端IP',
          dataIndex: 'ip',
          disabled: false,
          checked: true,
        },
        {
          title: 'MAC地址',
          dataIndex: 'mac',
          disabled: false,
          checked: true,
        },
        {
          title: '单位',
          dataIndex: 'momgDeptName',
          disabled: false,
          checked: true,
          scopedSlots: {
            customRender: 'tooltip'
          },
          customCell: () => {
            let cellStyle = 'text-align: left;max-width:400px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '终端类型',
          dataIndex: 'deviceType1',
          disabled: false,
          checked: true,
        },
        {
          title: '操作系统',
          dataIndex: 'osType_dictText',
          disabled: false,
          checked: true,
        },
        {
          title: 'cpu类型',
          dataIndex: 'cpuType_dictText',
          disabled: false,
          checked: true,
        },
        {
          title: '使用人',
          dataIndex: 'username',
          disabled: false,
          checked: true,
        },
        {
          title: '使用部门',
          dataIndex: 'userDepartment',
          disabled: false,
          checked: false,
        },
        {
          title: '详细地址',
          dataIndex: 'addrDetail',
          disabled: false,
          checked: false,
        },
        {
          title: '设备状态',
          dataIndex: 'deviceType',
          disabled: false,
          checked: true,
        },
        {
          title: '所属网关',
          dataIndex: 'gatewayCode',
          disabled: false,
          checked: true,
        },
        {
          title: '管理人',
          dataIndex: 'administrator',
          disabled: false,
          checked: false,
        },
        {
          title: '管理部门',
          dataIndex: 'adminDepartment',
          disabled: false,
          checked: false,
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          sorter: true,
          disabled: false,
          checked: true,
        },
        {
          title: '第一次开机时间',
          dataIndex: 'onFristTime',
          disabled: false,
          checked: true,
        },
        {
          title: '最后一次开机时间',
          dataIndex: 'onLastTime',
          disabled: false,
          checked: true,
        },
        {
          title: '最后关机时间',
          dataIndex: 'offLastTime',
          disabled: false,
          checked: true,
        },
        {
          title: '当天在线率(%)',
          dataIndex: 'onlineRate',
          sorter: true,
          disabled: false,
          checked: false,
        },
        {
          title: '当天开机次数',
          dataIndex: 'onCount',
          disabled: false,
          checked: true,
          customCell: () => {
            let cellStyle = 'text-align: right'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '当天总时长',
          dataIndex: 'onTotal_text',
          disabled: false,
          checked: true
        }
      ],
      url: {
        list: '/device/statis/list',
        // exportXlsUrl: '/device/statis/deviceStatisExcel'
        exportXlsUrl: '/device/statis/saveMemberList'
        // delete: '/knowledge/delete',
      },
      disableMixinCreated: true,
      userList: [],
    }
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    }
  },
  created() {
    this.getuserList()
    this.initColumns()
    this.loadDepartsData()
    this.initDictConfig()
  },
  mounted() {
    this.debounceCalWidth = addCancelDebounce(this.setColumnsWidth.bind(this), 500)
    if (this.startTime && this.endTime) {
      this.queryParam.startTime = this.startTime.format("YYYY-MM-DD")
      this.queryParam.endTime = this.endTime.format("YYYY-MM-DD")
    }
    this.loadData()
  },
  beforeDestroy() {
    if (this.debounceCalWidth) {
      window.removeEventListener("resize", this.debounceCalWidth);
      this.debounceCalWidth.cancel(); // 取消计时器
    }
  },
  methods: {
    getuserList() {
      let param = {
        pageSize: 10000
      }
      getUserList(param).then((res) => {
        if (res.success) {
          this.userList = res.result.records
        }
      })
    },
    loadData(arg) {
      if (!this.url.list) {
        this.$message.error('请设置url.list属性!')
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1
      }

      var params = this.getQueryParams() //查询条件
      this.loading = true
      getAction(this.url.list, params).then((res) => {
        if (res.success && res.result) {
          this.dataSource = res.result.records || res.result
          if (this.dataSource.length < 9) {
            this.clientHeight = false
          }
          if (this.dataSource && this.dataSource.length > 0) {
            this.dataSource.map(item => {
              item.cpuRateStr = item.cpuRateStr ? item.cpuRateStr.slice(0, item.cpuRateStr.length - 1) * 1 : ''
              item.memUtilizRateStr = item.memUtilizRateStr ? item.memUtilizRateStr.slice(0, item.memUtilizRateStr.length - 1) * 1 : ''
              item.diskRateStr = item.diskRateStr ? item.diskRateStr.slice(0, item.diskRateStr.length - 1) * 1 : ''
            })
          }
          this.ipagination.total = res.result.total ? res.result.total : 0
          this.debounceCalWidth()
        }
        if (res.code === 510) {
          this.$message.warning(res.message)
        }
        this.loading = false
      })
    },
    initColumns() {
      //获取默认列表的缓存dataIndex数据集合
      var key1 = this.$route.name + ':allCols'
      let allCols = Vue.ls.get(key1)

      if (allCols) {
        let indexArr = []
        // 根据缓存顺序重新排序 defColumns
        const sortedColumns = allCols
          .map(dataIndex => this.defColumns.find(item => item.dataIndex === dataIndex))
          .filter(item => item) // 过滤掉 undefined（未找到的项）
        indexArr = sortedColumns.map(item => item.dataIndex)

        // 过滤出未在缓存中出现的列
        const remainingColumns = this.defColumns.filter(
          item => !allCols.includes(item.dataIndex)
        )
        if (remainingColumns.length > 0) {
          let indexRemainIndexArr = remainingColumns.map(item => item.dataIndex)
          indexArr.push(...indexRemainIndexArr)
        }
        Vue.ls.set(key1, indexArr)
        // 将剩余列和最后一个元素插入到排序后的数组末尾
        if (remainingColumns.length > 0) {
          sortedColumns.push(...remainingColumns)
        }

        // 更新 defColumns
        this.defColumns = sortedColumns
      } else {
        //缓存数据集合为空时，按照data中定义的defColumns列表顺序写缓存
        let tempDataIndex = []
        this.defColumns.forEach(item => {
          if (item.dataIndex !== 'action' && item.key !== 'rowIndex') {
            tempDataIndex.push(item.dataIndex)
          }
        })
        Vue.ls.set(key1, tempDataIndex)
      }

      //从缓存中获取列表中显示的dataIndex
      var key2 = this.$route.name + ':colsettings'
      let colSettings = Vue.ls.get(key2)
      //已选显示列表缓存dataIndex数据集合为空
      if (!colSettings || colSettings.length == 0) {
        let allSettingColumns = []
        let columns = []
        this.defColumns.forEach(function (item, i, array) {
          delete item.fixed
          delete item.scopedSlots
          //item.disabled=false
          if (item.checked) {
            allSettingColumns.push(item.dataIndex)
            columns.push(item)
          }
        })
        this.settingColumns = allSettingColumns
        Vue.ls.set(key2, allSettingColumns)
        this.columns = columns
        //this.columns.unshift(firstItem)
        // this.columns.push(lastItem)
      }
      //已选列表缓存dataIndex数据集合不为空，设置table要显示的列
      else {
        this.settingColumns = this.defColumns.filter(
          item => colSettings.includes(item.dataIndex)).map(item => item.dataIndex)
        Vue.ls.set(key2, this.settingColumns)

        this.columns = this.defColumns.filter((item) => {
          delete item.fixed
          delete item.scopedSlots

          if (item.dataIndex === 'action' || item.key === 'rowIndex') {
            return true
          }
          if (colSettings.includes(item.dataIndex)) {
            item.disabled = colSettings.length <= this.disLength//只有两个字段时，不可点击取消
            item.checked = true
            return true
          } else {
            item.checked = false
            return false
          }
        })
      }
      this.columns[0]['fixed'] = 'left'
      //this.columns[1]['fixed'] = 'left'
      this.columns[this.columns.length - 1]['fixed'] = 'right'
      this.columns[this.columns.length - 1]['scopedSlots'] = {
        filterDropdown: 'filterDropdown',
        filterIcon: 'filterIcon',
      }
      let indx = this.columns.findIndex(item => item.dataIndex === 'momgDeptName')
      if (indx > -1) {
        if (this.columns[indx].scopedSlots) {
          this.columns[indx].scopedSlots['customRender'] = 'tooltip'
        } else {
          this.columns[indx]['scopedSlots'] = { customRender: 'tooltip' }
        }
      }
    },
    //列设置更改事件
    onColSettingsChange(checkedValues) {
      let tempColumns = []
      this.defColumns.forEach((item) => {
        delete item.fixed
        delete item.scopedSlots

        if (item.key === 'rowIndex' || item.dataIndex === 'action') {
          tempColumns.push(item)
        }
        if (checkedValues.includes(item.dataIndex)) {
          item.disabled = checkedValues.length <= this.disLength
          item.checked = true
          tempColumns.push(item)
        } else {
          if (item.checked) {
            item.checked = false
          }
          if (item.disabled) {
            item.disabled = false
          }
        }
      })
      tempColumns[0]['fixed'] = 'left'
      //tempColumns[1]['fixed'] = 'left'
      //this.columns[2]['fixed'] = 'left'
      tempColumns[tempColumns.length - 1]['fixed'] = 'right'
      tempColumns[tempColumns.length - 1]['scopedSlots'] = {
        filterDropdown: 'filterDropdown',
        filterIcon: 'filterIcon',
      }
      let indx = tempColumns.findIndex(item => item.dataIndex === 'momgDeptName')
      if (indx > -1) {
        if (tempColumns[indx].scopedSlots) {
          tempColumns[indx].scopedSlots['customRender'] = 'tooltip'
        } else {
          tempColumns[indx]['scopedSlots'] = { customRender: 'tooltip' }
        }
      }
      this.columns.splice(0, this.columns.length, ...tempColumns)
      //将已选并在列表显示的字段dataIndex写入缓存中
      var key = this.$route.name + ':colsettings'
      // Vue.ls.set(key, this.settingColumns, 7 * 24 * 60 * 60 * 1000)
      Vue.ls.set(key, this.settingColumns)
      this.setColumnsWidth()
    },
    dragStart(event, index) {
      this.dragItemIndex = index
      this.dragDom = event.currentTarget.cloneNode(true)
    },
    allowDrop(event) {
      event.preventDefault()
    },
    drop(event, index) {
      event.preventDefault()
      //默认列表重新进行排序
      if (index !== this.dragItemIndex) {
        let temp = this.defColumns[this.dragItemIndex]
        this.defColumns.splice(this.dragItemIndex, 1)
        this.defColumns.splice(index, 0, temp)
      }
    },
    dragEnd(event, index) {
      //event.preventDefault()
      //table列显示顺序同步拖拽后的顺序
      let tempSettingDataIndex = []
      let tempAllDataIndex = []
      let cols = this.defColumns.filter(item => {
        delete item.fixed
        delete item.scopedSlots

        if (item.key === 'rowIndex' || item.dataIndex === 'action') {
          return true
        } else {
          tempAllDataIndex.push(item.dataIndex)
        }
        if (this.settingColumns.includes(item.dataIndex)) {
          tempSettingDataIndex.push(item.dataIndex)
          return true
        }
        return false
      })
      this.settingColumns.splice(0, this.settingColumns.length, ...tempSettingDataIndex) //按照排序重新保存选中显示列表
      cols[0]['fixed'] = 'left'
      //cols[1]['fixed'] = 'left'
      //cols[2]['fixed'] = 'left'
      cols[cols.length - 1]['fixed'] = 'right'
      cols[cols.length - 1]['scopedSlots'] = {
        filterDropdown: 'filterDropdown',
        filterIcon: 'filterIcon',
      }
      let indx = cols.findIndex(item => item.dataIndex === 'momgDeptName')
      if (indx > -1) {
        if (cols[indx].scopedSlots) {
          cols[indx].scopedSlots['customRender'] = 'tooltip'
        } else {
          cols[indx]['scopedSlots'] = { customRender: 'tooltip' }
        }
      }
      this.columns.splice(0, this.columns.length, ...cols)
      var key1 = this.$route.name + ':colsettings' //浏览器缓存重新记录排序后的选中列表
      //Vue.ls.set(key1, tempColumns, 7 * 24 * 60 * 60 * 1000)//保留7天缓存
      Vue.ls.set(key1, this.settingColumns)

      var key2 = this.$route.name + ':allCols' //浏览器缓存重新记录排序后的所有列表
      Vue.ls.set(key2, tempAllDataIndex)
      this.dragDom.remove()
      ++this.refreshKey
      this.setColumnsWidth()
    },
    /*获取滚动区列宽，用于设置对应固定列的宽*/
    setColumnsWidth() {
      this.$nextTick(() => {
        if (this.columns && this.columns.length > 0) {
          let table = document.querySelector('.terminal-table')
          if (table) {
            let scrollThs = table.querySelector('.ant-table-scroll .ant-table-thead tr').querySelectorAll('th')
            let leftFixedThs = table.querySelector('.ant-table-fixed-left .ant-table-thead tr').querySelectorAll('th')
            for (let i = 0; i < leftFixedThs.length; i++) {
              leftFixedThs[i].setAttribute('data-index-left', i); // 添加 data-index 属性
              leftFixedThs[i].style.setProperty(`--height-left-${i}`, scrollThs[i].offsetHeight + 'px');
              leftFixedThs[i].style.setProperty(`--width-left-${i}`, scrollThs[i].offsetWidth + 'px');
            }

            let rightFixedThs = table.querySelector('.ant-table-fixed-right .ant-table-thead tr').querySelectorAll('th')
            let lastIndex = scrollThs.length - 1
            for (let i = 0; i < rightFixedThs.length; i++) {
              rightFixedThs[i].setAttribute('data-index-right', i); // 添加 data-index 属性
              rightFixedThs[i].style.setProperty(`--height-right-${i}`, scrollThs[lastIndex - i].offsetHeight + 'px');
              rightFixedThs[i].style.setProperty(`--width-right-${i}`, scrollThs[lastIndex - i].offsetWidth + 'px');
            }
          }
        }
      })
    },
    initDictConfig() {
      this.getDictData('cpuType', 'cpuTypeList')
      this.getDictData('os_type', 'osTypeList')
    },
    getDictData(dictCode, list) {
      ajaxGetDictItems(dictCode, null).then((res) => {
        if (res.success) {
          this[list] = res.result
        }
      })
    },
    handleTableChange(pagination, filters, sorter) {
      //分页、排序、筛选变化时触发
      //TODO 筛选
      if (Object.keys(sorter).length > 0) {
        this.isorter.column = sorter.field
        if (sorter.order != undefined) {
          this.isorter.order = 'ascend' == sorter.order ? 'asc' : 'desc'
        } else {
          this.isorter.order = 'undefined'
        }
      }
      this.ipagination = pagination
      this.loadData()
    },
    loadDepartsData() {
      getAction('/sys/sysDepart/queryAllTree').then((res) => {
        for (let i = 0; i < res.length; i++) {
          let temp = res[i]
          this.departsTreeData.push(temp)
        }
      })
    },
    onChangeDeparts(value) {
      // this.queryParam.deptId = value.join(",")
    },
    onSearchDeparts(e) {
      this.bSearchedDepKey = e
    },
    onSelectDeparts() { },
    setQueryParam(e, field) {
      this.queryParam[field] = e.join(',')
    },
    selfExport() {
      if (!this.searchedDepKey && this.bSearchedDepKey) {
        this.searchedDepKey = this.bSearchedDepKey
      }
      if (Array.isArray(this.searchedDepKey)) {
        this.queryParam.momgDeptName = this.searchedDepKey.join(',')
      } else if (typeof this.searchedDepKey === 'string') {
        this.queryParam.momgDeptName = this.searchedDepKey
      }
      this.exportFields = this.columns.filter((item) => item.key !== 'rowIndex' && item.dataIndex !== 'action').map(item => { return item.dataIndex })
      this.setExportField('osType_dictText', ['osType'])
      this.setExportField('cpuType_dictText', ['cpuType'])
      //this.setExportField('onTotal_text',['onTotal'])
      this.queryParam.exportFields = this.exportFields.join(',')
      this.backendExportData('终端统计')
    },
    setExportField(oldField, arrNewField, deleCount = 1) {
      let indx = this.exportFields.indexOf(oldField)
      if (indx > -1) {
        this.exportFields.splice(indx, deleCount, ...arrNewField)
      }
    },
    //表单查询,点击查询按钮，默认查询第一页
    dosearch() {
      if (!this.searchedDepKey && this.bSearchedDepKey) {
        this.searchedDepKey = this.bSearchedDepKey
      }
      if (Array.isArray(this.searchedDepKey)) {
        this.queryParam.momgDeptName = this.searchedDepKey.join(',')
      } else if (typeof this.searchedDepKey === 'string') {
        this.queryParam.momgDeptName = this.searchedDepKey
      }
      if (this.startTime && this.endTime) {
        this.queryParam.startTime = this.startTime.format("YYYY-MM-DD")
        this.queryParam.endTime = this.endTime.format("YYYY-MM-DD")
      } else {
        this.queryParam.startTime = null
        this.queryParam.endTime = null
      }
      this.loadData(1)
    },
    doReset() {
      this.queryParam = {}
      this.osTypeValues = undefined
      this.cpuTypeValues = undefined
      this.searchedDepKey = undefined
      this.updateKey++;
      this.startTime = moment().startOf('day').subtract(1, 'days'), // 创建起始时间-筛选框
        this.endTime = moment().endOf('day').subtract(1, 'days'), // 创建结束时间-筛选框
        this.queryParam.startTime = this.startTime.format("YYYY-MM-DD")
      this.queryParam.endTime = this.endTime.format("YYYY-MM-DD")

      this.loadData(1)
    },
    moment,
    onChangePicker(value, dateString) {
      this.startTime = value[0] // 创建起始时间-筛选框
      this.endTime = value[1] // 创建结束时间-筛选框
    },
    //限制日期不可选
    disabledDate(current) {
      // return current && current > moment().subtract(1, "days"); //不包括当天
      return (current && current > moment().subtract(1, 'days').endOf('day')) || current < moment().subtract(31,
        'days')
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';

/* 为每个 th 元素设置独立的样式 */
::v-deep .ant-table-fixed-left .ant-table-thead tr th[data-index-left="0"] {
  height: var(--height-left-0) !important;
  width: var(--width-left-0) !important;
}

::v-deep .ant-table-fixed-right .ant-table-thead tr th[data-index-right="0"] {
  height: var(--height-right-0) !important;
  width: var(--width-right-0) !important;
}
</style>