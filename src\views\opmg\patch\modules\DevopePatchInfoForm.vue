<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="disabled">
      <a-form-model slot="detail" ref='form' :model='model' :rules='validatorRules' :labelCol="labelCol"
        :wrapperCol="wrapperCol">
        <a-row>
          <a-col :md="12" :xs="24">
            <a-form-model-item label="软件名称" prop='softwareId'>
              <a-select v-model='model.softwareId' :allowClear='true' placeholder='请选择软件名称'>
                <a-select-option v-for='item in devopsSoftwareList' :key='"softwareId_"+item.value' :value='item.value'>
                  {{ item.text }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :md="12" :xs="24">
            <a-form-model-item label="补丁版本" prop='patchVersion'>
              <a-input v-model='model.patchVersion' :allowClear="true" autocomplete="off" placeholder="请输入补丁版本">
              </a-input>
            </a-form-model-item>
          </a-col>
          <a-col :md="12" :xs="24">
            <a-form-model-item label="操作系统" prop='patchOs'>
              <a-select v-model='model.patchOs' :allowClear='true' placeholder='请选择操作系统' @change="changePatchOs">
                <a-select-option v-for='item in patchOsList' :key='"patchOs_"+item.value' :value='item.value'>
                  {{ item.text }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :md="12" :xs="24">
            <a-form-model-item class="two-words" label="架构" prop='frawork'>
              <a-select v-model='model.frawork' :allowClear='true' placeholder='请选择架构'>
                <a-select-option v-for='item in fraworkList' :key='"frawork_"+item.value' :value='item.value'>
                  {{ item.text }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :md="12" :xs="24">
            <a-form-model-item label="补丁文件" prop='patchFileName'>
              <j-upload
                v-model='model.patchFileName'
                :number="1"
                :bizPath="bizPath"
                :accept='patchFileAccept'>
              </j-upload>
            </a-form-model-item>
          </a-col>
          <a-col :md="12" :xs="24">
            <a-form-model-item label="脚本文件" prop='scriptFileName'>
              <j-upload
                v-model='model.scriptFileName'
                :number="1"
                :bizPath="bizPath"
                :accept='scriptFileAccept'>
              </j-upload>
            </a-form-model-item>
          </a-col>
          <a-col :md="12" :xs="24">
            <a-form-model-item label="是否有效" prop='effect'>
              <a-select v-model='model.effect' placeholder='请选择是否有效'>
                <a-select-option v-for='item in effectList' :key='"effect_"+item.value' :value='item.value'>
                  {{ item.text }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :md="12" :xs="24">
            <a-form-model-item label="设备类型" prop='resourceType'>
              <a-select v-model='model.resourceType' :allowClear='true' placeholder='请选择设备类型'>
                <a-select-option v-for='item in resourceTypeList' :key='"resourceType_"+item.value' :value='item.value'>
                  {{ item.text }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :md="12" :xs="24">
            <a-form-model-item class="two-words" label="描述" prop='patchDescribe'>
              <a-textarea v-model='model.patchDescribe' :allowClear="true" autocomplete="off"
                :autoSize="{ minRows: 2, maxRows: 8 }" placeholder="请输入描述" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>
  import {
    httpAction
  } from '@/api/manage'
  import {
    ajaxGetDictItems,
    getDictItemsFromCache
  } from '@api/api'
  export default {
    name: 'DevopePatchInfoForm',
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false,
      },
    },
    data() {
      return {
        confirmLoading: false,
        bizPath: 'software',
        devopsSoftwareList: [],
        patchOsList: [],
        fraworkAllList: [],
        fraworkList: [],
        effectList: [],
        resourceTypeList: [],
        model: {},
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 6
          },
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          },
        },
        patchFileAccept:'',
        patchFileNameSuffix: [
          '.tar',
          '.tbz',
          '.tgz',
          '.zip',
          '.rar',
          '.jar',
          '.class',
          '.java',
          '.c',
          '.cpp',
          '.h',
          '.so',
          '.7z',
          '.rpm',
          '.deb',
        ], //补丁
        scriptFileAccept:'',
        scriptFileNameSuffix: [
          '.sh',
          '.csh',
          '.py',
          '.gz',
          '.tar',
          '.tbz',
          '.tgz',
          '.zip',
          '.rar',
          '.bat',
          '.pl',
          '.7z',
          '.rpm',
          '.deb',
          '.conf',
        ], //脚本
        validatorRules: {
          softwareId: [{
            required: true,
            message: '请选择软件名称'
          }],
          patchVersion: [{
              required: true,
              message: '请输入补丁版本'
            },
            {
              pattern: /^([1-9]\d|[1-9])([.]([1-9]\d|\d)){2}$/,
              message: '请输入正确的补丁版本,如：x.xx.x'
            }
          ],
          patchOs: [{
            required: true,
            message: '请选择操作系统'
          }],
          frawork: [{
            required: true,
            message: '请选择架构'
          }],
          patchFileName: [{
            required: true,
            message: '请选择补丁文件'
          }],
          scriptFileName: [{
            required: true,
            message: '请选择脚本文件'
          }],
          patchDescribe: [{
              required: false,
              message: '请输入描述'
            },
            {
              max: 255,
              message: '描述长度不可超过 255 个字符'
            }
          ]
        },
        url: {
          add: '/patch/devopePatchInfo/add',
          edit: '/patch/devopePatchInfo/edit'
        },
      }
    },
    created() {
      this.patchFileAccept=this.patchFileNameSuffix.join(',')
      this.scriptFileAccept=this.scriptFileNameSuffix.join(',')
      //一个字符串直接走数据字典，若是多个字符串通过逗号拼接直接查询数据库，一般有页面服务这类数据
      this.initTopicDictData('devops_software_info,software_name,id', 'devopsSoftwareList')
      this.initTopicDictData('patch_os', 'patchOsList')
      this.initTopicDictData('wind_frawork', 'fraworkAllList')
      this.initTopicDictData('cpuArch', 'fraworkAllList')
      this.initTopicDictData('valid_status', 'effectList')
      this.initTopicDictData('resources_type', 'resourceTypeList')
    },
    methods: {
      initTopicDictData(dictCode, list) {
        //优先从缓存中读取字典配置
        /*   if (getDictItemsFromCache(dictCode)) {
             this[list] = getDictItemsFromCache(dictCode)
             return
           }*/

        //根据字典Code, 初始化字典数组
        ajaxGetDictItems(dictCode, null).then((res) => {
          if (res.success) {
            if (list.indexOf('frawork') > -1) {
              this[list].push(res.result)
            } else {
              this[list] = res.result
            }
          }
        })
      },
      add() {
        this.edit({})
      },
      edit(record) {
        this.visible = true
        this.$refs.form.resetFields()
        this.fraworkList = []
        console.log('record==',record)
        if (record.patchOs && record.patchOs != "" && record.patchOs != "null" && record.patchOs != "undefined") {
          for (let k = 0; k < this.patchOsList.length; k++) {
            if (this.patchOsList[k].value == record.patchOs) {
              this.fraworkList = this.fraworkAllList[k]
            }
          }
        }
        this.model = JSON.parse(JSON.stringify(record))
        if (!record.resourceType||record.resourceType.length<=0){
          delete this.model.resourceType
        }
        if (!this.model.id) {
          this.model.effect = '0'
        } else {
          this.model.effect = this.model.effect.toString()
        }
      },
      updateModelPatchFileName() {
        this.model.patchFileName = null
      },
      updateModelScriptFileName() {
        this.model.scriptFileName = null
      },
      changePatchOs(value) {
        this.fraworkList = []
        if (this.model.frawork) {
          this.model.frawork = undefined
        }
        if (value) {
          for (let k = 0; k < this.patchOsList.length; k++) {
            if (this.patchOsList[k].value == value) {
              this.fraworkList = this.fraworkAllList[k]
            }
          }
        }
      },
      submitForm() {
        const that = this
        // 触发表单验证
        this.$refs.form.validate((valid, values) => {
          if (valid) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.model.id) {
              httpurl += that.url.add
              method = 'post'
            } else {
              httpurl += that.url.edit
              method = 'put'
            }
            //进行补丁文件的筛选
            if ('' != this.model.patchFileName && null != this.model.patchFileName && undefined !=
            this.model.patchFileName)
            {
              var f = true
              for (var i = 0; i < that.patchFileNameSuffix.length; i++) {
                var suffix = that.patchFileNameSuffix[i]
                var index = this.model.patchFileName.lastIndexOf('.')
                var fileSuffix = this.model.patchFileName.substring(index, this.model.patchFileName.length)
                if (that.patchFileNameSuffix[i] == fileSuffix || index == -1) {
                  f = false
                }
              }
              if (f) {
                var str = ''
                for (var i = 0; i < that.patchFileNameSuffix.length; i++) {
                  str = str + '[' + that.patchFileNameSuffix[i] + ']' + '、'
                }
                that.$message.warning('请选择以' + str.substring(0, str.length - 1) + '为后缀的补丁文件')
                // that.updateModelPatchFileName()
                that.confirmLoading = false
                return
              }
            }
            //进行脚本文件的筛选
            if (null != this.model.scriptFileName && '' != this.model.scriptFileName && undefined != this.model
              .scriptFileName) {
              var f = true
              for (var i = 0; i < that.scriptFileNameSuffix.length; i++) {
                var suffix = that.scriptFileNameSuffix[i]
                var index = this.model.scriptFileName.lastIndexOf('.')
                var fileSuffix = this.model.scriptFileName.substring(index, this.model.scriptFileName.length)
                if (that.scriptFileNameSuffix[i] == fileSuffix) {
                  f = false
                }
              }
              if (f) {
                var str = ''
                for (var i = 0; i < that.scriptFileNameSuffix.length; i++) {
                  str = str + '[' + that.scriptFileNameSuffix[i] + ']' + '、'
                }
                that.$message.warning('请选择以' + str.substring(0, str.length - 1) + '为后缀的脚本文件')
                //that.updateModelScriptFileName()
                that.confirmLoading = false
                return
              }
            }

            let formData = Object.assign(this.model, values)
             if (!formData.resourceType||formData.resourceType.length<=0){
               formData.resourceType=''
             }
            httpAction(httpurl, formData, method)
              .then((res) => {
                if (res.success) {
                  that.$message.success(res.result)
                  that.$emit('ok')
                } else {
                  that.$message.warning(res.message)
                }
                that.confirmLoading = false
              })
              .catch((err) => {
                that.$message.warning(err.message)
                that.confirmLoading = false
              })
          }
        })
      }
    }
  }
</script>
<style lang="less" scoped>
  ::v-deep .two-words>div>label {
    letter-spacing: 4px;
  }

  ::v-deep .two-words>div>label::after {
    letter-spacing: 0px;
  }
</style>