import { Graph, Dom, Node, <PERSON>hape ,Color,Line,Curve,Path} from '@antv/x6'

import {
  globalGridAttr
} from '../models/global'
class SwitchShape extends Shape.Rect { }

SwitchShape.config({
  width: 60,
  height: 60,
  attrs: {
    body: {
      stroke: 'none',
      fill: 'rgba(95,149,255,0.05)',
      opacity: 1,
      rx:0,
      ry:0,
    },
    image: {
      'xlink:href': "",
      refWidth: "90%",
      refHeight: "90%",
      refX: '5%',
      refY: '5%',
      filter: 'brightness(100%)',
    },
    label: {
      text: '',
      fontSize: 13,
      fill: globalGridAttr.topoConfig.labelColor,
      refX: 0.5,
      refY: "100%",
      refY2: 4,
      textAnchor: "middle",
      textVerticalAnchor: "top",
    },
   
  },
  markup: [
    {
      tagName: 'rect',
      selector: 'body'
    },
    {
      tagName: 'image',
      selector: 'image'
    },
    {
      tagName: 'text',
      selector: 'label',
    },
  ],
  ports: {
    groups: {
      top: {
        position: 'top',
        attrs: {
          circle: {
            r: 4,
            magnet: true,
            stroke: '#5F95FF',
            strokeWidth: 1,
            fill: '#fff',
            style: {
              visibility: "visible"
            }
          }
        }
      },
      right: {
        position: 'right',
        attrs: {
          circle: {
            r: 4,
            magnet: true,
            stroke: '#5F95FF',
            strokeWidth: 1,
            fill: '#fff',
            style: {
              visibility: "visible",
            },
          }
        }
      },
      left: {
        position: 'left',
        attrs: {
          circle: {
            r: 4,
            magnet: true,
            stroke: '#5F95FF',
            strokeWidth: 1,
            fill: '#fff',
            style: {
              visibility: "visible",
            },
          }
        }
      },
      bottom: {
        position: "bottom",
        attrs: {
          circle: {
            r: 4,
            magnet: true,
            stroke: '#5F95FF',
            strokeWidth: 1,
            fill: '#fff',
            style: {
              visibility: "visible",
            }

          },

        }
      },
    },
    // items: [
    //   {
    //     group: 'top'
    //   },
    //   {
    //     group: 'bottom'
    //   },
    // ]
  },
  portMarkup: [
    {
      tagName: 'circle',
      selector: 'portBody',
    },
  ],
})

export const switchNode = Graph.registerNode('switch-node', SwitchShape)


class CircleNode extends Shape.Circle {

}
CircleNode.config({
  width: 60,
  height: 60,
  attrs: {
    body: {
      stroke: 'none',
      fill: 'rgba(95,149,255,0.05)',
      opacity: 1,
      rx:0,
      ry:0,
    },
    image: {
      'xlink:href': "",
      refWidth: "70%",
      refHeight: "70%",
      refX: '15%',
      refY: '15%',
      filter: 'brightness(100%)'
    },
    label: {
      text: '',
      fontSize: 13,
      fill: globalGridAttr.topoConfig.labelColor,
      refX: 0.5,
      refY: "100%",
      refY2: 4,
      textAnchor: "middle",
      textVerticalAnchor: "top",
    },
  },
  markup: [
    {
      tagName: 'circle',
      selector: 'body'
    },
    {
      tagName: 'image',
      selector: 'image'
    },
    {
      tagName: 'text',
      selector: 'label',
    },
  ],
  ports: {
    groups: {
      top: {
        position: 'top',
        attrs: {
          circle: {
            r: 4,
            magnet: true,
            stroke: '#5F95FF',
            strokeWidth: 1,
            fill: '#fff',
            style: {
              visibility: "visible"
            }
          }
        }
      },
      right: {
        position: 'right',
        attrs: {
          circle: {
            r: 4,
            magnet: true,
            stroke: '#5F95FF',
            strokeWidth: 1,
            fill: '#fff',
            style: {
              visibility: "visible",
            },
          }
        }
      },
      left: {
        position: 'left',
        attrs: {
          circle: {
            r: 4,
            magnet: true,
            stroke: '#5F95FF',
            strokeWidth: 1,
            fill: '#fff',
            style: {
              visibility: "visible",
            },
          }
        }
      },
      bottom: {
        position: "bottom",
        attrs: {
          circle: {
            r: 4,
            magnet: true,
            stroke: '#5F95FF',
            strokeWidth: 1,
            fill: '#fff',
            style: {
              visibility: "visible",
            }

          },

        }
      },
    },
    items: [

    ]
  },
  portMarkup: [
    {
      tagName: 'circle',
      selector: 'portBody',
    },
  ],
})
export const circleNode = Graph.registerNode('circle-node', CircleNode)
class DiamondNode extends Shape.Polygon {}
DiamondNode.config({
  width: 60,
  height: 60,
  attrs: {
    body: {
      fill: "rgba(95,149,255,0.05)",
      opacity: 1,
      stroke: 'transparent',
      refPoints: '0,10 10,0 20,10 10,20',
      rx:0,
      ry:0,
    },
    image: {
      'xlink:href': "",
      refWidth: "70%",
      refHeight: "70%",
      refX: '15%',
      refY: '15%',
      filter: 'brightness(100%)'
    },
    label: {
      text: '',
      fontSize: 13,
      fill: globalGridAttr.topoConfig.labelColor,
      refX: 0.5,
      refY: "100%",
      refY2: 4,
      textAnchor: "middle",
      textVerticalAnchor: "top",
    },
  },
  markup: [
    {
      tagName: 'polygon',
      selector: 'body'
    },
    {
      tagName: 'image',
      selector: 'image'
    },
    {
      tagName: 'text',
      selector: 'label',
    },
  ],
  ports: {
    groups: {
      top: {
        position: 'top',
        attrs: {
          circle: {
            r: 4,
            magnet: true,
            stroke: '#5F95FF',
            strokeWidth: 1,
            fill: '#fff',
            style: {
              visibility: "visible"
            }
          }
        }
      },
      right: {
        position: 'right',
        attrs: {
          circle: {
            r: 4,
            magnet: true,
            stroke: '#5F95FF',
            strokeWidth: 1,
            fill: '#fff',
            style: {
              visibility: "visible",
            },
          }
        }
      },
      left: {
        position: 'left',
        attrs: {
          circle: {
            r: 4,
            magnet: true,
            stroke: '#5F95FF',
            strokeWidth: 1,
            fill: '#fff',
            style: {
              visibility: "visible",
            },
          }
        }
      },
      bottom: {
        position: "bottom",
        attrs: {
          circle: {
            r: 4,
            magnet: true,
            stroke: '#5F95FF',
            strokeWidth: 1,
            fill: '#fff',
            style: {
              visibility: "visible",
            }

          },

        }
      },
    },
    items: [

    ]
  },
  portMarkup: [
    {
      tagName: 'circle',
      selector: 'portBody',
    },
  ],
})
export const diamondNode = Graph.registerNode('diamond-node', DiamondNode)

export const sonTopoNode = Graph.registerNode('son-topo-node', {
  inherit: 'rect',
  width: 80,
  height: Math.floor(80 * 301 / 573),
  attrs: {
    body: {
      stroke: '#1890ff',
      strokeWidth: 1,
      fill: 'none',
      opacity: 1,
      strokeLinecap: "round",
    },
    image: {
      'xlink:href': "",
      refWidth: '100%',
      refHeight: "100%",
      refX: "0",
      refY: "0",
      // height: 80,

    },
    label: {
      text: '子拓扑',
      refX: 0.5,
      refY: "100%",
      textAnchor: "middle",
      textVerticalAnchor: "top",
    },
  },
  markup: [
    {
      tagName: 'rect',
      selector: 'body'
    },
    {
      tagName: 'image',
      selector: 'image'
    },
    {
      tagName: 'text',
      selector: 'label',
    },
    {
      tagName: 'foreignObject',
      selector: 'fo',
      children: [
        {
          ns: Dom.ns.xhtml,
          tagName: 'body',
          selector: 'foBody',
          children: [
            {
              tagName: 'div',
              selector: 'edit-text'
            }
          ]
        }
      ]
    },
  ],
  ports: {
    groups: {
      top: {
        position: 'top',
        attrs: {
          circle: {
            r: 4,
            magnet: true,
            stroke: '#5F95FF',
            strokeWidth: 1,
            fill: '#fff',
            style: {
              visibility: "visible"
            }
          }
        }
      },
      right: {
        position: 'right',
        attrs: {
          circle: {
            r: 4,
            magnet: true,
            stroke: '#5F95FF',
            strokeWidth: 1,
            fill: '#fff',
            style: {
              visibility: "visible"
            }
          }
        }
      },
      left: {
        position: 'left',
        attrs: {
          circle: {
            r: 4,
            magnet: true,
            stroke: '#5F95FF',
            strokeWidth: 1,
            fill: '#fff',
            style: {
              visibility: "visible"
            }
          }
        }
      },
      bottom: {
        position: 'bottom',
        attrs: {
          circle: {
            r: 4,
            magnet: true,
            stroke: '#5F95FF',
            strokeWidth: 1,
            fill: '#fff',
            style: {
              visibility: "visible"
            }
          }
        }
      },
    },
    // items: [
    //   {
    //     group: 'top'
    //   },
    //   {
    //     group: 'bottom'
    //   },
    // ]
  },
  portMarkup: [
    {
      tagName: 'circle',
      selector: 'portBody',
    },
  ],
})
export class NodeGroup extends Node {

  // protected
  postprocess() {
    this.toggleCollapse()
  }

  isCollapsed() {
    return this.data.collapsed
  }

  toggleCollapse() {
    const target = this.data.collapsed !== null ? this.data.collapsed : false
    //获取节点的width、height
    if (target) {
      this.attr('buttonSign', { d: 'M 1 5 9 5 M 5 1 5 9' })
      this.resize(this.data.nodeWidth, this.data.collapsedHeight)
    } else {
      this.attr('buttonSign', { d: 'M 2 5 8 5' })
      this.resize(this.data.nodeWidth, this.data.nodeHeight)
    }
  }
}

NodeGroup.config({
  shape: 'rect',
  width: 200,
  height: 40,
  data: {
    nodeWidth: 200,
    nodeHeight: 200,
    collapsedHeight: 40,

  },
  markup: [
    {
      tagName: 'rect',
      selector: 'body'
    },
    {
      tagName: 'text',
      selector: 'label'
    },
    {
      tagName: 'g',
      selector: 'buttonGroup',
      children: [
        {
          tagName: 'rect',
          selector: 'button',
          attrs: {
            'pointer-events': 'visiblePainted'
          }
        },
        {
          tagName: 'path',
          selector: 'buttonSign',
          attrs: {
            fill: 'none',
            'pointer-events': 'none'
          }
        }
      ]
    }
  ],
  attrs: {
    body: {
      refWidth: '100%',
      refHeight: '100%',
      strokeWidth: 1,
      fill: globalGridAttr.topoConfig.nodeBgColor,
      stroke: '#5F95FF',
      rx:30,
      ry:30,
    },
    label: {
      text: '群组名称',
      fontSize: 12,
      fill: 'rgba(0,0,0,0.85)',
      refX: 30,
      refY: 15
    },
    buttonGroup: {
      refX: '100%',
      refX2: -25,
      refY: 13
    },
    button: {
      height: 14,
      width: 16,
      rx: 2,
      ry: 2,
      fill: '#f5f5f5',
      stroke: '#ccc',
      cursor: 'pointer',
      event: 'node:collapse'
    },
    buttonSign: {
      refX: 3,
      refY: 2,
      stroke: '#808080'
    }
  },
})

Graph.registerNode('networkGroupNode', NodeGroup)

class edgeDragNode extends Shape.Circle {

}
edgeDragNode.config({
  width: 4,
  height: 4,
  attrs: {
    body: {
      stroke: 'rgba(95,149,255,1)',
      fill: 'rgba(95,149,255,1)',
      opacity: 1
    },
  },
  markup: [
    {
      tagName: 'circle',
      selector: 'body'
    },
  ],
  data:{
    nodeType:"edgeDrag"
  }
})
Graph.registerNode('edgeDragNode', edgeDragNode)

const color1 = Color.randomHex()
const color2 = Color.randomHex()
Graph.registerEdge(
  'performance_edge',
  {
    zIndex: 1,
    markup: [
      {
        tagName: 'path',
        selector: 'wrap',
        attrs: {
          fill: 'none',
          cursor: 'pointer',
          stroke: 'transparent',
          strokeWidth: 10,
          strokeLinecap: 'round',
        },
      },
      {
        tagName: 'path',
        selector: 'line',
      },
    ],
    attrs: {
      wrap: {
        connection: true,
      },
      line: {
        connection: true,
        stroke: color2,
        strokeWidth: 1,
        targetMarker: 'classic',
      },
    },
    data: {
      dragType: "start",
      nodeType: "connecting",
  }
  },
  true,
)

class rectDragNode extends Shape.Rect {

}
rectDragNode.config({
  width: 120,
  height: 60,
  attrs: {
    body: {
      stroke: '#080808',
      strokeDasharray: '0',
      strokeWidth: 1,
      fill: 'rgba(255,255,255,0)'
    },
  },
  markup: [
    {
      tagName: 'rect',
      selector: 'body'
    },
  ],
  data:{
    nodeType:"rectDragNode"
  }
})
Graph.registerNode('rectDragNode', rectDragNode)


Graph.registerConnector(
  'multi-smooth',
  (
    sourcePoint,
    targetPoint,
    routePoints,
    options,
  ) => {
    const {index = 1, total = 1, gap = 12} = options
    const line = new Line(sourcePoint, targetPoint)
    const centerIndex = (total - 1) / 2
    const dist = index - centerIndex
    const diff = Math.abs(dist)
    const factor = diff === 0 ? 1 : diff / dist
    const vertice = line
      .pointAtLength(line.length() / 2 + gap * factor * Math.ceil(diff))
      .rotate(90, line.getCenter())

    const points = [sourcePoint, vertice, targetPoint]
    const curves = Curve.throughPoints(points)
    const path = new Path(curves)
    return options.raw ? path : path.serialize()
  },
  true,
)
Graph.registerConnector(
  'mindmap',
  (sourcePoint, targetPoint, routerPoints, options) => {
    const midX = sourcePoint.x + 10
    const midY = sourcePoint.y
    const ctrX = (targetPoint.x - midX) / 5 + midX
    const ctrY = targetPoint.y
    const pathData = `
     M ${sourcePoint.x} ${sourcePoint.y}
     L ${midX} ${midY}
     Q ${ctrX} ${ctrY} ${targetPoint.x} ${targetPoint.y}
    `
    return options.raw ? Path.parse(pathData) : pathData
  },
  true,
)

export const shapeExtends= {"circle-node":"circle","switch-node":"rect","diamond-node":"polygon"};