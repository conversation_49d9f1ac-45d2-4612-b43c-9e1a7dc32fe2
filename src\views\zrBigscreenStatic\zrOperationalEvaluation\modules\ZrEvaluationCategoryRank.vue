<template>
  <div class='zr-evaluation-category-rank'>
    <zr-bigscreen-title title='评估分类排名'>
    </zr-bigscreen-title>
    <div class='rank-content'>
      <div style='height: 15%'></div>
      <div style='height: 70%'>
        <zr-rank-bar :rank-data='renkData'  ranking></zr-rank-bar>
      </div>

    </div>
  </div>
</template>
<script>
import ZrBigscreenTitle from '@views/zrBigscreens/modules/ZrBigscreenTitle.vue'
import ZrRankBar from '@views/zrBigscreens/zrOperationalEvaluation/modules/ZrRankBar.vue'

export default {
  name: 'ZrEvaluationCategoryRank',
  components: {
    ZrRankBar,
    ZrBigscreenTitle
  },
  props: {
  },
  data() {
    return {
      renkData:[
        { name: '基础设施', score: 99,level: 'A' },
        { name: '数据资源', score: 94,level: 'A' },
        { name: '网络通联', score: 88,level: 'B' },
        { name: '应用服务', score: 81,level: 'B' },
        { name: '运维保障', score: 74,level: 'C' },
        { name: '质量效益', score: 60,level: 'D' }
      ],
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
  }
}
</script>
<style scoped lang='less'>
.zr-evaluation-category-rank {
  width: 100%;
  height: 100%;
  .rank-content {
    width: 100%;
    height: calc(100% - 51px);
    padding: 0 16px;
    background: linear-gradient(to right, rgba(29, 78, 140, 0.3), rgba(29, 78, 140, 0.0));
  }
}
</style>