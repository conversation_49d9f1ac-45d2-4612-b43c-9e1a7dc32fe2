// 节点属性与保存数据的对应
export const nodeProperties = {
    "id": "id",
    "sysOrgCode": "sysOrgCode",
    "nodePosition": "position",
    "nodeSize": "size",
    "nodeAttrs": "attrs",
    "nodeVisible": "visible",
    "nodeShape": "shape",
    "portMarkup": "portMarkup",
    "nodePorts": "ports",
    "portLabelMarkup": "portLabelMarkup",
    "nodeData": "data",
    "nodeZIndex": "zIndex",
    "nodeChildren": "children",
    "nodeParent": "parent",
    "nodeView": "view",
    "markUp": "markup",
    "nodeTools": "tools",
    "nodeAngle": "angle",
    "nodeConfig": "nodeConfig",
    "deviceCode": "bus_data",
    "deviceType": "bus_data",
    "devicePanel": "bus_data",
    "deptId": "bus_data",
    "nodeScore": "bus_data",
    "nodeDeviceInfo": "deviceInfo",
}
// 连线属性与保存数据的对应
export const edgeProperties =   {
    "id": "id",
    "sysOrgCode": "sysOrgCode",
    "edgeShape": "shape",
    "edgeAttrs": "attrs",
    "edgeLabels": "labels",
    "edgeParent": "parent",
    "edgeZIndex": "zIndex",
    "markUp": "markup",
    "edgeView": "view",
    "edgeTools": "tools",
    "edgeData": "data",
    "edgeRouter": "router",
    "edgeConnector": "connector",
    "edgeVertices": "vertices",
    "edgeVisible": "visible",
    "edgeConfig": "edgeConfig",
    "edgeSource": "source",
    "edgeTarget": "target",
    "fromDevice": "bus_data",
    "fromPort": "bus_data",
    "toDevice": "bus_data",
    "toPort": "bus_data",
}



const topo = {
    "id": "1661268534652133377",//拓扑图ID
    name: "的点点滴滴",//拓扑名称
    "topoType": "0",//拓扑类型 0网络拓扑 1应用拓扑 
    "showType": "1",//是否展示到大屏上 0 不展示 1展示
    topoDataJson: "",//拓扑图生成的json数据
    topoSvg: "",//保存时拓扑图生成的svg 在拓扑列表展示时使用
    topoImg: "",//保存拓扑图时生成的图片 暂时没有该功能 可做缩略图
    topoConfig: "",//拓扑图的配置信息
}
const node = {
    "id": "5fe3916a-7a70-43de-b07c-6e487da834d4",//节点的ID
    topoId: "",//所属拓id
    "attrs": "",//节点的属性 可根据节点的其他属性生成 保留字段
    "shape": "switch-node",//拓扑节点类型
    view: "",//定渲染节点/边所使用的视图
    markup: "",//渲染节点/边时使用的 SVG/HTML 片段
    tools: "",//工具
    "zIndex": 0,//节点层级
    "parent": "",//父节点ID
    "children": "",//子节点
    "visible": false,//节点是否可见
    "data": "", //与节点/边关联的业务数据
    position: "",//位置；
    size: "",//大小
    angle: "",//节点的旋转角度
    "ports": "",//节点连接桩的配置 目前不考虑使用后台数据
    "portMarkup": "",//链接桩的 DOM 结构 目前不考虑使用后台数据
    portLabelMarkup: "",//链接桩标签的 DOM 结构
    deviceCode: "",//节点对应设备code
    deviceType: "",//设备类型
    devicePanel: "",//对应的设备面板
    nodeCofig: "",//节点的一些特殊的属性配置  比如群组节点的展示按钮是否需要隐藏 文字节点的连线是否显示
}
const edge = {
    "id": "c67d2157-c3c0-41c1-8be1-4da64b74f859",//连线的id
    "shape": "edge",//拓扑节点类型
    "attrs": "",//连线的属性 可根据连线的其他属性生成 保留字段
    markup: "",//渲染节点/边时使用的 SVG/HTML 片段
    "zIndex": 2,//变得层级
    view: "",//视图
    visible: false,//节点是否可见
    "parent": "",//父节点ID
    tools: "",//工具
    data: "",//业务数据
    source: "",//起始节点
    target: "",//终止节点
    vertices: "",//路径点
    router: "",//路由
    connector: "",//连接器
    from_device: "",//起始设备ID
    from_port: "",//起始设备的端口
    to_device: "",//目标设备ID
    to_port: "",//目标设备的端口
    edgeConfig: "",//连线配置
}




