/*
1、弹窗可切换全屏模式和非全屏模式
2、弹窗宽度不固定，随着浏览器可视窗口变化而变化
3、全屏模式下：垂直滚动条在白色弹窗内（弹窗高度= 浏览器可视窗口的高度）
4、非全屏模式下:当弹窗高度>浏览器可视窗口高度时，垂直滚动条出现，且位置有两种情况，即在白色弹窗外、在白色弹窗内。要求垂直滚动条在白色弹窗内时，需令其属性wrapClassName=‘tableModal’
*/
::v-deep .ant-modal {
 // margin: 0;
  padding: 18px 18px;
  vertical-align: middle;
}

::v-deep .ant-modal-body {
  padding: 24px;
  //padding: 24px 48px;
  /* max-height: calc(100vh - 55px - 55px - 48px);
   overflow-y:auto*/
}
.tableModal{
  .ant-modal-body {
    //padding: 24px 48px !important;
    padding: 24px !important;
    max-height: calc(100vh - 55px - 55px - 48px) !important;
    overflow-y:auto
  }
}
.j-modal-box.fullscreen {
  margin: 0 !important;
  max-width: 100vw !important;

  ::v-deep .ant-modal {
    max-width: 100vw !important;
    margin: 0;
    padding-bottom: 0px;
    .ant-modal-body {
      max-height: calc(100vh - 55px - 55px) !important;
    }
  }
  .tableModal{
    .ant-modal-body {
      //padding: 24px 48px !important;
      padding: 24px !important;
      max-height: calc(100vh - 55px - 55px) !important;
      overflow-y:auto
    }
  }
}
@media (min-width: 480px){
  ::v-deep .ant-modal {
    max-width: calc(100vw - 12px);
  }
}

::v-deep .two-words > div > label {
  letter-spacing: 4px;
}
::v-deep .two-words > div > label::after {
  letter-spacing: 0px;
}