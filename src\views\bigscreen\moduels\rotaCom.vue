<template>
  <div style="height: 100%;width: 100%;display: flex;align-items: center;justify-content: center;">
    <div style="height: 65%;width: 28%;position: relative;"><img :src="src" alt=""
        style="height: 100%;position: absolute;right: 0;"></div>
    <div style="width: 72%;">
      <div class="infoBox">
        <div style="font-size:0.45rem;letter-spacing: 5px;font-weight: 600;">{{ name }}</div>
        <div style="font-size:0.35rem;margin-left: 10px;">{{ career }}</div>
      </div>
      <div style="margin-left: 50px;font-size: 0.32rem;letter-spacing: 1px;color: rgba(250,250,250,.9);">{{ time }}
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    props: {
      name: {
        type: String,
        default: '',
      },
      career: {
        type: String,
        default: '',
      },
      time: {
        type: String,
        default: '',
      },
      src: {
        type: String,
        default: '',
      },
    },
    data() {
      return {

      };
    },
    methods: {

    }
  }
</script>

<style scoped lang="less">
  .infoBox {
    color: rgba(250, 250, 250, .9);
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 0.5rem 0 0.5rem;
  }
</style>