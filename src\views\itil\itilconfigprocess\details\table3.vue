<template>
  <a-table
    :columns="columns"
    :data-source="data"
    ref="table"
    size="middle"
    bordered
    rowKey="id"
  >
  </a-table>
</template>
<script>
import { getAction} from '@/api/manage'
export default {
  // 关联配置项
  name: 'table1',
  data() {
    return {
        columns: [
            {
                title: '序号',
                dataIndex: '',
                    key: 'rowIndex',
                width: 60,
                    align: 'center',
                customRender: function(t, r, index) {
                    return parseInt(index) + 1
                 }
            },
            {
                title: '分类',
                align: 'center',
                dataIndex: 'configType'
            },
            {
                title: '编号',
                align: 'center',
                dataIndex: 'code'
            },
            {
                title: '名称',
                align: 'center',
                dataIndex: 'name'
            },
            {
                title: '状态',
                align: 'center',
                dataIndex: 'state'
            }
        ],
        data: [],
        url:{
            list:'/event/getItilConfigList/',
        },
    }
  },
  methods: {
    getDataList(id) {
      this.loading = true;
      getAction(this.url.list,{busId:id}).then(res => {
        if (res.success) {
          this.loading = false;
          this.data = res.result;
          if (!res.result || res.result.length == 0) {
            this.$message.warning( "未找到关联配置项数据");
          }
        }else {
          this.$message.error( res.message);
        }
      });
    },
  }
}
</script>
<style scoped></style>
