<template>
  <div style='width: 100%;height:100%;' :id='uuid'></div>
</template>

<script>
import { registerMap } from '@/utils/mapManager'
import { mapOption } from './YqChartMapMigrateOption'
import { organizations } from '@/views/zrBigscreens/modules/zrOrganizations'
import { unitLevels, businessStatus } from '@/views/zrBigscreens/modules/zrUtil'
import { flatTreeData } from '@/utils/util'
import { getAction } from '@api/manage'

export default {
  name: 'YqChartMapMigrate',
  props: {
    width: Number,
    height: Number,
    nodeList: {
      type: Array,
      default: () => []
    },
  },
  data() {
    return {
      uuid: '',
      cptData: {},
      chartOption: {},
      chart: undefined,
      map: null,
      backMaps: [],
      option: mapOption,
      addScale: 0.02,
      organizations: []
    }
  },
  watch: {
    'option.attribute': {
      handler(newObj) {
        this.loadChart(newObj)
      },
      deep: true//深度监听
    },
    width() {
      this.resize()
    },
    height() {
      this.resize()
    }
  },
  created() {
    // this.organizations = flatTreeData([organizations], null, []).filter(item => item.city)
    this.uuid = require('uuid').v1()
  },
  mounted() {
    this.chart = this.$echarts.init(document.getElementById(this.uuid))
    let timer = setTimeout(() => {
      clearTimeout(timer)
      timer = null
      this.loadData()
    }, 100) // 延时加载数据，确保图表初始化完成
  },
  methods: {
    refreshCptData() {
      this.loadData()
    },
    loadData() {
      if (this.map === null) {
        this.map = this.option.attribute.map
      }
      // this.cptData = this.organizations
      this.cptData = this.nodeList
      // console.log("this.cptData === ",this.cptData)
      console.log("地图节点的数据 === ",this.nodeList)
      this.loadChart(this.option.attribute)
    },
    async loadChart(attribute) {
      const that = this
      // 注册地图
      await registerMap(this.map)
      // 获取城市坐标
      // await this.getToPosition()
      const seaData = (data) => {
        const res = []
        data.forEach(item => {
          res.push({
            symbol: this.getSymbol(item),
            symbolSize: 12,
            name: item.nickName || item.name,
            value: [...item.position, item.status],
            itemData: item, // 单独存储数值
            itemStyle: {
              color: businessStatus.find(el => el.value == item.status)?.color || '#FF1D42'
            }
          })
        })
        // console.log("seaData === ",res)
        return res
      }

      that.chartOption = {
        title: {
          text: attribute.titleText,
          subtext: attribute.subtext,
          left: attribute.titleLeft,
          top: attribute.titleTop,
          textStyle: {
            color: attribute.titleColor,
            fontSize: attribute.titleFontSize
          },
          subtextStyle: {
            color: attribute.subTitleColor,
            fontSize: attribute.subTitleFontSize
          }
        },
        tooltip: {
          trigger: 'item',
          backgroundColor: 'transparent',
          padding: 0,
          formatter: function(params) {
            let itemData = params.data.itemData
            if (params.seriesType === 'effectScatter' && itemData) {
              let status = businessStatus.find(el => el.value == itemData.status)
              let statusColor = status?.color || '#FF1D42'
              let statusLabel = status?.label || '未知状态'
              return `<div class='map-tip-box'>
                        <div class='tip-title'>
                          ${itemData.nickName || itemData.name}
                        </div>
                        <div class='tip-info-line' style='margin-top: 8px;'>
                          <span>运行状态：</span>
                          <span style='color:${statusColor}'>${statusLabel}</span>
                        </div>
                        <div class='tip-info-line'>
                          <span>网络联通：</span>
                          <span >${statusLabel}</span>
                        </div>
                         <div class='tip-info-line'>
                          <span>业务数量：</span>
                          <span style='color:#ffff'>${itemData.businessCount}</span>
                        </div>
                         <div class='tip-info-line'>
                          <span>设备数量：</span>
                          <span style='color:#fff'>${itemData.deviceCount}</span>
                        </div>
                      </div>`
            }
          }
        },
        geo: {
          map: that.map,
          roam: attribute.roam,//允许缩放
          zoom: attribute.zoom,
          animation: false,
          aspectScale: 0.75,
          label: {
            show: attribute.geoLabelShow,
            fontSize: attribute.geoLabelSize,
            color: attribute.geoLabelColor
          },
          itemStyle: {
            borderColor: attribute.geoBorderColor,//边界线颜色
            borderType: attribute.geoBorderType,
            borderWidth: 1,
            areaColor: attribute.geoAreaColor
          },
          emphasis: {
            label: {
              show: attribute.geoLabelShow,
              fontSize: attribute.geoLabelSize,
              color: attribute.emphasisLabelColor
            },
            itemStyle: {
              areaColor: attribute.emphasisAreaColor,
              borderColor: attribute.emphasisBorderColor,
              borderType: attribute.geoBorderType
            }
          },
          regions: [{
            name: '南海诸岛',
            selected: false,
            emphasis: {
              itemStyle: {
                areaColor: '#00000000',
                borderColor: '#00000000'
              }
            },
            itemStyle: {
              areaColor: '#00000000',
              borderColor: '#00000000'
            }
          }],
          selectedMode: attribute.selectable ? 'single' : '',
          z: 2
        },
        series: [
          {
            type: 'map',
            map: 'china',
            zoom: attribute.zoom + this.addScale,
            tooltip: {
              show: false
            },
            label: {
              show: false, // 显示省份名称
              color: '#04CFF5',
              align: 'center'
            },
            aspectScale:  0.75,
            roam: false, // 地图缩放和平移
            animation: false,
            itemStyle: {
              borderColor: '#4690D8',
              borderWidth: 1,
              shadowColor: '#4690D8',
              areaColor: '#4690D8',
              shadowBlur: 25,
              opacity: 1
            },
            // 去除选中状态
            select: {
              disabled: true
            },
            emphasis: { // 聚焦后颜色
              disabled: true, // 开启高亮
              label: {
                show: false,
                align: 'center',
                color: '#04CFF500'
              },
              itemStyle: {
                borderColor: '#4690D8',
                borderWidth: 1,
                shadowColor: '#4690D8',
                areaColor: '#4690D8',
                shadowBlur: 15,
                opacity: 1
              }
            },
            z: 1
          },
          /*  {
            show:false,
            name: attribute.seriesName,
            type: 'lines',
            zlevel: 1,
            effect: {
              show: attribute.effectShow && attribute.effectTrailShow,
              period: attribute.effectPeriod,
              trailLength: attribute.effectTrailLength,
              color: attribute.effectTrailColor,
              symbolSize: attribute.effectSymbolSize/5
            },
            lineStyle: {
              color: attribute.linesColor,
              width: 0,
              opacity: attribute.linesOpacity,
              curveness: attribute.linesCurveness,
              type: attribute.linesType,
            },
            data: convertData(this.cptData)
          },*/
          /*{
            show:false,
            name: attribute.seriesName,
            type: 'lines',
            zlevel: 2,
            effect: {
              show: attribute.effectShow,
              period: attribute.effectPeriod,
              trailLength:0,
              color:attribute.effectColor,
              symbol: attribute.effectSymbol==="none"?attribute.effectSymbolPath:attribute.effectSymbol,
              symbolSize: attribute.effectSymbolSize
            },
            lineStyle: {
              color: attribute.linesColor,
              width: attribute.linesWidth,
              opacity: attribute.linesOpacity,
              curveness: attribute.linesCurveness,
              type: attribute.linesType,
            },
            data: convertData(this.cptData)
          },*/
          {
            name: attribute.seriesName,
            type: 'effectScatter',
            coordinateSystem: 'geo',
            zlevel: 2,
            symbol: attribute.scatterSymbol === 'none' ? attribute.scatterSymbolPath : attribute.scatterSymbol,
            symbolSize: function(val) {
              return attribute.scatterSymbolSize
            },
            rippleEffect: {
              brushType: attribute.scatterRippleType,
              scale: attribute.scatterRippleScale,
              period: attribute.scatterRipplePeriod
            },
            label: {
              show: attribute.scatterLabelShow,
              position: attribute.scatterLabelPos,
              formatter: attribute.scatterLabelFormatter
            },
            itemStyle: {
              color: attribute.scatterSymbolColor
            },
            data: seaData(this.cptData)
          }]
        /*     toolbox: {
               show: that.backMaps.length>0 || (that.editStatus && attribute.changeable),
               itemSize:attribute.mapBackSize,
               orient: 'vertical',
               right: attribute.mapBackRight,
               top: attribute.mapBackTop,
               feature: {
                 myBack: {
                   show: true,
                   title: attribute.mapBackTitle,
                   iconStyle: {  // 设置图标样式
                     color: attribute.mapBackColor,  // 图标颜色
                     borderWidth: 0,
                   },
                   emphasis:{
                     iconStyle: {
                       color: attribute.mapBackColorH,
                     },
                   },
                   icon: mapBackIcon,
                   onclick: function() {
                     that.backMapHandler()
                   }
                 }
               }
             }*/
      }
      that.chart.setOption(that.chartOption)
      this.chart.off('geoselectchanged')// 先移除旧监听防止重复
      this.chart.off('click')
      this.chart.off('georoam')
      // 添加联动事件
      this.chart.on('georoam', (params) => {
        var option = this.chart.getOption()
        var geo = option.geo[0]
        this.chart.setOption({
          series: [{
            center: geo.center,
            zoom: geo.zoom + this.addScale
          }]
        })
      })
      if (attribute.selectable) {
        //  监听地图区域选择
        this.chart.on('geoselectchanged', (event) => {
          if (event && event.batch && event.batch.length > 0) {
            let geoData = event.batch[0]
            let isSelected = geoData.selected[geoData.itemData.name]
            this.selectArea(geoData.name, isSelected, attribute)
          }

        })
      }
      if (attribute.changeable) {
        this.chart.on('click', (event) => {
          // console.log('点击了区域', event)
          if(event.seriesType === 'effectScatter') {
            // 处理点击事件
            let itemData = event.data.itemData
            getAction("/monitor/situation/getNetTopoByDeptId",{ deptId:itemData.deptId}).then(res=>{
              if(res.success && res.result && res.result.id){
                const topoId = res.result.id
                this.$router.push({
                  path: '/operationsView/comprehensive',
                  query: {
                    topoId,
                    deptId:itemData.deptId,
                  }
                })
              }else{
                this.$message.error('该单位还没有关联拓扑图')
              }
            }).catch(err=>{
              this.$message.error('获取单位的关联拓扑图失败')
            })
          }
          // this.changeMap(event.name)
        })
      }
    },
    //获取标记
    getSymbol(item) {
      if (item.level == 1) {
        return 'circle'
      } else if (item.level == 2) {
        return 'rect'
      } else {
        return 'diamond'
      }
    },
    //选中地区处理逻辑
    selectArea(areaName, isSelected, attribute) {
      //获取到当前地图数据

    },
    //切换地图
    changeMap(areaName) {
      let code = this.getAreaCode(areaName)
      registerMap(code).then(mapFlag => {
        if (mapFlag) {
          this.backMaps.push(this.map)
          this.map = code
          this.refreshCptData()
        } else {
          // this.$message.warning('没有该地图数据！')
        }
      })

    },
    //返回上级地图
    backMapHandler() {
      let lastMap = this.backMaps[this.backMaps.length - 1]
      if (lastMap) {
        this.map = lastMap
        this.backMaps.pop()
        this.refreshCptData()
      }

    },
    //通过name找到对应的行政区域code
    getAreaCode(areaName) {
      let mapData = this.$echarts.getMap(this.map)
      if (mapData && mapData.geoJson && mapData.geoJson.features) {
        for (let i = 0; i < mapData.geoJson.features.length; i++) {
          let feature = mapData.geoJson.features[i]
          //在地图数据找到区域的行政区划  regionalism_code
          if (feature.properties && feature.properties.name === areaName) {
            //更新页面的行政区划参数
            return feature.id
          }
        }
      }
    },
    resize() {
      this.chart.resize()
    }
  }
}
</script>

<style lang='less'>
.map-tip-box {
  min-width: 320px;
  min-height: 228px;
  background-image: url(/zrBigScreen/tipBg.png);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding: 24px 22px;

  .tip-title {
    font-weight: bold;
    font-size: 21px;
    height: 48px;
    width: 100%;
    padding-left: 32px;
    display: flex;
    align-items: center;
    color: #FFFFFF;
    background-image: url(/zrBigScreen/tipTitleBg.png);
    background-size: auto;
    background-repeat: no-repeat;
  }

  .tip-info-line {
    margin-left: 14px;
    //margin-top: 16px;
    font-weight: 400;
    font-size: 17px;
    color: #969696;
    line-height: 33px;
  }
}
</style>
