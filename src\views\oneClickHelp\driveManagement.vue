<template>
  <div class="driveBox">
    <card-frame :title="'驱动管理'" showHeadBgImg>
      <a-row :gutter="10" style="height: calc(100%)" class="vScroll" slot="bodySlot">
        <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
          <!-- 查询区域 -->
          <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class="card-style">
            <div class="table-page-search-wrapper-style">
              <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
                <a-row :gutter="24" ref="row">
                  <a-col :span="spanValue">
                    <a-form-item label="驱动名称">
                      <a-input placeholder="请输入驱动名称" :allowClear="true" autocomplete="off"
                        v-model="queryParam.driveName" :maxLength="50">
                      </a-input>
                    </a-form-item>
                  </a-col>
                  <a-col :span="spanValue">
                    <a-form-item label="cpu架构" :labelCol="labelCol" :wrapperCol="wrapperCol">
                      <a-select placeholder="请输选择cpu架构" v-model="queryParam.driveCpu" @change="cpuChange"
                        :allowClear="true" :getPopupContainer='(node) => node.parentNode'>
                        <a-select-option v-for="item in cpuList" :value="item.value" :key="item.value">
                          {{ item.text || item.label }}
                        </a-select-option>
                      </a-select>
                    </a-form-item>
                  </a-col>
                  <a-col :span="spanValue">
                    <a-form-item label="操作系统" :labelCol="labelCol" :wrapperCol="wrapperCol">
                      <a-select placeholder="请输选择操作系统" v-model="queryParam.driveOs" @change="actionChange"
                        :allowClear="true" :getPopupContainer='(node) => node.parentNode'>
                        <a-select-option v-for="item in dictOptions" :value="item.value" :key="item.value">
                          {{ item.text }}
                        </a-select-option>
                      </a-select>
                    </a-form-item>
                  </a-col>
                  <a-col :span="colBtnsSpan()">
                    <span class="table-page-search-submitButtons"
                      :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                      <a-button type="primary" @click="searchQuery" class="btn-search">查询</a-button>
                      <a-button @click="searchReset" style="margin-left: 10px" class="btn-reset">重置</a-button>
                    </span>
                  </a-col>
                </a-row>
              </a-form>
            </div>
          </a-card>
          <!-- 查询区域-END -->
          <div class="card-style2">
            <div class="lightXian"></div>
            <div class="contentBox" v-if="driveList.length>0">
              <a-row :gutter="16" type="flex" style="margin-left: 8px; margin-right: 8px">
                <a-col v-bind="CardColLayout" v-for="item in driveList" :key="item.id">
                  <div class="libraryBox">
                    <driver-library :cpuType="item.driveCpu" :version="item.driveVersion" :system="item.driveOs"
                      :name="item.driveName" :record="item" @loadData="refresh">
                    </driver-library>
                  </div>
                </a-col>
              </a-row>
              <div class="pagination">
                <a-pagination show-size-changer :hideOnSinglePage="false" :default-current="ipagination.current"
                  :total="ipagination.total" @change="onChange" :page-size="ipagination.pageSize"
                  :pageSizeOptions="ipagination.pageSizeOptions" :show-total="(total) => `共 ${ipagination.total} 条`"
                  @showSizeChange="onShowSizeChange" size="small">
                </a-pagination>
              </div>
            </div>
            <!-- 暂无数据 -->
            <div class="contentBox"  v-if="driveList.length == 0 && loading == false" style="margin-top: 10%;">
              <empty/>
            </div>
          </div>
        </a-col>
      </a-row>
    </card-frame>
  </div>
</template>

<script>
  import '@/assets/less/TableExpand.less'
  import {
    mixinDevice
  } from '@/utils/mixin'
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import {
    filterMultiDictText
  } from '@/components/dict/JDictSelectUtil'
  import JSuperQuery from '@/components/jeecg/JSuperQuery.vue'
  import cardFrame from '../oneClickHelp/localDeviceInfo/modules/CardFrame.vue'
  import driverLibrary from './modules/driverLibrary.vue'
  import Empty from '@/components/oneClickHelp/Empty.vue'
  import {
    YqFormSearchLocation
  } from '@/mixins/YqFormSearchLocation'
  import {
    ajaxGetAreaItems,
    ajaxGetDictItems,
    getDictItemsFromCache
  } from '@/api/api'
  import {
    deleteAction,
    getAction,
    downFile,
    getFileAccessHttpUrl
  } from '@/api/manage'
  import {
    getHostNameLocal
  } from '@/utils/util'
  export default {
    name: 'driveList',
    mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
    components: {
      JSuperQuery,
      driverLibrary,
      cardFrame,
      Empty
    },
    props: {
      terminal: {
        type: String,
        default: '',
      },
    },
    data() {
      return {
        description: '一键帮助驱动管理',
        driveList: [],
        ipagination: {
          current: 1,
          pageSize: 12,
          pageSizeOptions: ['12', '24', '36'],
          showTotal: (total, range) => {
            return range[0] + '-' + range[1] + ' 共' + total + '条'
          },
          showSizeChanger: true,
          total: 0,
        },
        CardColLayout: {
          xxl: {
            span: 6
          },
          xl: {
            span: 8,
          },
          lg: {
            span: 12,
          },
          md: {
            span: 12,
          },
          sm: {
            span: 24,
          },
          xs: {
            span: 24,
          },
        },
        formItemLayout: {
          md: {
            span: 6,
          },
          sm: {
            span: 12,
          },
        },
        labelCol: {
          xs: {
            span: 24,
          },
          sm: {
            span: 7,
          },
        },
        wrapperCol: {
          xs: {
            span: 24,
          },
          sm: {
            span: 16,
          },
        },
        url: {
          list: '/drive/driveInfo/list',
          getCpuOsType: '/drive/driveInfo/getCpuOsType',
          exportXlsUrl: '/patch/devopePatchInfo/exportXls',
          importExcelUrl: 'patch/devopePatchInfo/importExcel',
        },
        dictOptions: [],
        cpuList: [],
      }
    },

    created() {
      this.initData()
      this.initDictData()
    },
    mounted() {},
    computed: {
      importExcelUrl: function () {
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
      },
    },
    methods: {
      onShowSizeChange(current, pageSize) {
        this.ipagination.pageSize = pageSize
        this.ipagination.current = current
        this.loadData()
      },
      onChange(pageNum, pageSize) {
        this.ipagination.current = pageNum
        this.ipagination.pageSize = pageSize
        this.loadData()
      },
      initData() {
        let hostName = getHostNameLocal()
        if (hostName === null) {
          this.$message.warning('请检查是否已经添加终端信息？')
          return
        }
        this.queryParam.terminalCode = hostName
        this.getCpuOsType(hostName)
      },
      getCpuOsType(code) {
        getAction(this.url.getCpuOsType, {
          terminalCode: code,
        }).then((res) => {
          if (res.success) {
            if (res.result.cpuArch != null) {
              this.queryParam.driveCpu = res.result.cpuArch
            }
            if (res.result.osType != null) {
              this.queryParam.driveOs = res.result.osType
            }
            this.loadData(1)
          }
        })
      },
      refresh() {
        this.loadData()
      },
      loadData(arg) {
        if (!this.url.list) {
          this.$message.error('请设置url.list属性!')
          return
        }
        var params = this.getQueryParams() //查询条件
        this.loading = true
        getAction(this.url.list, params).then((res) => {
          if (res.success) {
            this.driveList = res.result.records
            //author:weng    Date:20210402  for：if(res.result.total>0) 有错误，无查询结果时，页码显示有问题
            this.ipagination.total = res.result.total
          }
          if (res.code === 510) {
            this.$message.warning(res.message)
          }
          this.loading = false
        }).catch(err=>{
          this.loading = false
        })
      },
      initDictData() {
        //根据字典Code, 初始化字典数组
        ajaxGetDictItems('cpuArch', null).then((res) => {
          if (res.success) {
            this.cpuList = res.result
          }
        })
        ajaxGetDictItems('os_type', null).then((res) => {
          if (res.success) {
            this.dictOptions = res.result
          }
        })
      },
      actionChange(value) {
        this.$forceUpdate()
        this.queryParam.terminalCode = ''
        this.queryParam.driveOs = value
      },
      cpuChange(value) {
        this.$forceUpdate()
        this.queryParam.terminalCode = ''
        this.queryParam.driveCpu = value
      },
      searchQuery() {
        if (
          (this.queryParam.driveCpu != '' && this.queryParam.driveCpu != null) ||
          (this.queryParam.driveOs != '' && this.queryParam.driveOs != null)
        ) {
          this.queryParam.terminalCode = null
        }
        this.ipagination.current = 1
        this.loadData(1)
      },
      searchReset() {
        let hostName = getHostNameLocal()
        if (hostName === null) {
          this.$message.warning('请检查是否已经添加终端信息？')
          return
        }
        this.queryParam = {}
        this.queryParam.terminalCode = hostName
        this.ipagination.current = 1
        this.getCpuOsType(hostName)
      },
    },
  }
</script>
<style lang="less" scoped>
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';
  @import '~@assets/less/onclickStyle.less';

  ::-webkit-scrollbar {
    display: none;
  }

  .topImg {
    height: 50px;
    width: 100%;
    background-image: url('../../../public/oneClickHelp/localDeviceInfo/leftHeadBg.png');
    background-repeat: no-repeat;
    background-position: right center;
    position: relative;

    .topTitle {
      color: white;
      font-size: 14px;
      height: 50px;
      line-height: 50px;
      padding-left: 22px;
    }

    .head-left::before {
      position: absolute;
      content: '';
      top: 0;
      left: 0;
      width: 3px;
      height: 3px;
      background-color: #2f5bff;
    }

    .head-left::after {
      position: absolute;
      content: '';
      bottom: 0;
      left: 0;
      width: 3px;
      height: 3px;
      background-color: #2f5bff;
    }

    .head-right::before {
      position: absolute;
      content: '';
      top: 0;
      right: 0;
      width: 3px;
      height: 3px;
      background-color: #2f5bff;
    }

    .head-right::after {
      position: absolute;
      content: '';
      bottom: 0;
      right: 0;
      width: 3px;
      height: 3px;
      background-color: #2f5bff;
    }
  }

  .lightXian {
    height: 1px;
    width: 98%;
    margin: 0 auto;
    margin-top: -10px;
    background-color: #0e4589;
  }

  .driveBox {
    height: 100%;
    width: 100%;
  }

  .btn-search-style {
    background-color: #409eff;
    border: 1px solid #409eff;
    height: 28px;
    width: 73px;
  }

  .card-style {
    background-color: rgba(250, 250, 250, 0);
  }

  .card-style2 {
    background-color: rgba(250, 250, 250, 0);
  }

  .table-page-search-wrapper .ant-form-inline .ant-form-item {
    margin-bottom: 0 !important;
  }

  .contentBox {
    width: 100%;
    font-family: SourceHanSansCN-Regular;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.5);
    letter-spacing: 0;
    font-weight: 400;
  }

  .libraryBox {
    height: 391px;
    width: 100%;
    border-radius: 8px;
    margin-top: 24px;
    background: rgba(30, 58, 95, 0.29);
  }

  .pagination {
    position: fixed;
    bottom: 20px;
    left: 50%;
  }
</style>