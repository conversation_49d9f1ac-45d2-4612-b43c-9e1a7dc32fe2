/*密级资料上传混入脚本*/

import { ajaxGetDictItems,queryConfigureDictItem } from '@api/api'
import {checkBeforeUpload,checkAccept,checkChangedFiles,previewFile} from '@comp/yq/yqUpload/YqUploadCommonFuns'

export const YqMixinsHandleFile = {
  data() {
    return {
      /*控制否请求密级开启配置字典，若不请求，对应也不会读取密级等级字典数据*/
      isLevelEnable: false,
      /*若isLevelEnable=true，获取密级开启状态后，用showLevel记录，showLevel=true时，加载密级等级字典数据并*/
      showLevel: false,
      /*密级选择radiogrid样式*/
      levelStyle:{height: '40px',lineHeight: '40px',position: 'absolute',top:'0px',left: '120px',display:'flex',flexFlow:'row nowrap',justifyItems:'start',alignItems:'center'},
      /*记录密级项*/
      attachmentLevelOptions: [],
      /*默认选中的密级*/
      defaultAttachmentLevel: '',
      /*记录当前操作选中的密级*/
      attachmentLevel: '',

      /*控制否请求附件大小的数据字典*/
      isAttSizeLimitedEnable:false,
      /*记录显示附件大小的数据字典值*/
      attachmentSizeDict:[],
      /*限制附件字典是否启用*/
      attachmentSizeLimited:false,
      /*文件大小限制*/
      attachmentMaxSize: 20,
      /*文件大小单位*/
      attachmentUnit: 'MB'
    }
  },
  created() {
    if (this.isLevelEnable) {
      this.getAttachmentLevelStatus().then((res) => {
        if (res.success) {
          this.showLevel = res.enabled
          if (this.showLevel) {
            this.initLevelDictData()
          }
        }
      })
    }
    if (this.isAttSizeLimitedEnable) {
      this.getAttachmentSizeAndUnit()
    }
  },
  methods: {
    /*获取是否开启密级*/
    getAttachmentLevelStatus() {
      return new Promise((resolve, reject) => {
        let enabled=false
        queryConfigureDictItem({
          parentCode: 'InExSecret',
          childCode: 'isEnable',
        }).then((res) => {
          if (res.success) {
            enabled= res.result === 'true' ? true : false
          } else {
            enabled = false
          }
          resolve({success:true,enabled:enabled})
        }).catch(err => {
          enabled= false
          resolve({success:true,enabled:enabled})
        })
      })
    },
    /*获取密级项*/
    initLevelDictData() {
      ajaxGetDictItems('Classification', null).then((res) => {
        if (res.success && res.result && res.result.length > 0) {
          this.initConfigDictLevelItem('ClassificationDefaultValue','defaultValue').then((res1)=>{
            this.defaultAttachmentLevel =res.result[0].value
            this.attachmentLevelOptions =res.result.map((item) => {
              item['label'] = item.title
              return item
            })

            if (res1.success) {
              let m= res.result.filter((item)=>item.value===res1.data)
              if (m&&m.length>0){
                this.defaultAttachmentLevel = res1.data
              }
            }
            this.attachmentLevel = this.defaultAttachmentLevel
          }).catch((err)=>{
            this.attachmentLevel=this.defaultAttachmentLevel
          })
        }else {
          this.attachmentLevelOptions = []
          this.attachmentLevel=''
          this.defaultAttachmentLevel=''
        }
      }).catch((err)=>{
        this.$message.error(err.message);
        this.attachmentLevelOptions = []
        this.attachmentLevel=''
        this.defaultAttachmentLevel=''
      })
    },
    /*获取密级默认选中项*/
    initConfigDictLevelItem(parentCode,childCode) {
      return new Promise((resolve, reject) =>{
        let param={
          success:false,
          data:''
        }
        queryConfigureDictItem({parentCode: 'ClassificationDefaultValue',childCode: 'defaultValue'}).then((res) => {
          if (res.success && res.result) {
            param.data=res.result
            param.success=true
          }
          resolve(param)
        }).catch((err) => {
          reject(param)
          this.$message.error(err.message)
        })
      })
    },
    /*文件上传限制大小及单位*/
    getAttachmentSizeAndUnit() {
       ajaxGetDictItems('AttachmentSizeAndUnit', null).then((res) => {
        if (res.success && res.result && res.result.length > 0) {
          this.attachmentSizeDict=res.result
          this.attachmentSizeLimited =res.result[0].value
          this.attachmentMaxSize =res.result[1].value
          this.attachmentUnit =res.result[2].value
        }
      })
    },
    /*限制文件上传格式*/
    limitAccept(file, accept) {
      return checkAccept(file,accept)
    },
    /*改变密级*/
    changeAttachmentLevel(value) {
      this.attachmentLevel = value.target.value;
    },
    /*文件上传前的预处理：比如文件大小*/
    beforeUploadAttachment(file) {
     return checkBeforeUpload(file,this.attachmentSizeLimited,this.attachmentMaxSize,this.attachmentUnit)
    },
    /*获取附件信息*/
    getAttachmentJson(fileInfo) {
      return this.getFileJson(fileInfo, this.showLevel)
    },
    /*组合附件信息：文件名添加密级
    * 必须的属性数据是fileName和filePath，缺一不可，其他属性可自扩展
    * @param {Object} fileInfo 附件信息
    *  @param {Boolean} showLevel 附件名是否添加表明密级
    * */
    getFileJson(fileInfo, showLevel = false) {
      let fileName = fileInfo.name
      if (showLevel&&!fileInfo.response.status) {
        let newName = true
        let lastIndex1 = fileName.lastIndexOf('【');
        let lastIndex2 = fileName.lastIndexOf('】');
        if (lastIndex1 > -1 && lastIndex2 > -1) {
          let levName = fileName.substring(lastIndex1 + 1, lastIndex2)
          let m = this.attachmentLevelOptions.filter((item) => item.label === levName)
          if (m && m.length > 0) {
            newName = false
          }
        }
        if (newName) {
          let lastIndex = fileName.lastIndexOf('.');
          let lev = this.attachmentLevelOptions.filter((item) => item.value === this.attachmentLevel)
          fileName = fileName.substring(0, lastIndex) + "【" + lev[0].label + "】" + fileName.substring(lastIndex);
        }
      }
      return {
        fileName: fileName,
        filePath: fileInfo.response.message,
        fileSize: fileInfo.size?fileInfo.size:'null'
      }
    },
    /*文件发生改变后，附加处理方法，一次性上传多个文件，剔除列表中不满足条件的文件*/
    handleChangeAdditionalFun(fileList){
     return checkChangedFiles(fileList,this.attachmentSizeLimited,this.attachmentMaxSize,this.attachmentUnit)
    },
    /*下载预览文件*/
    previewFun(fileInfo) {
      previewFile(fileInfo)
    },
    /*获取数组对象方式存储的附件信息
    *每个对象元素包含两个属性，分别是filePath，originalFilename
    --*/
    getArrayFilesInfo(files,fileNameFeild='originalFilename',filePathFeild='filePath') {
      if (files && files.length > 0&&JSON.parse(files).length>0) {
        //新的格式：json串，含有相对路径和中文名
        if (files.includes(fileNameFeild) && files.includes(filePathFeild)) {
          const arr = JSON.parse(files);
          return  arr.map(item => {
            return {
              fileName: item[fileNameFeild],
              filePath: item[filePathFeild]
            }
          })
        } else {
          //兼顾旧数据：只存储路径用逗号隔开拼接的情况
          let list = files.split(',')
          if (list && list.length > 0) {
            return list.map(item => {
              return {
                fileName: this.getFileName(item),
                filePath: item
              }
            })
          }
        }
      }else {
        return []
      }
    },
    /*获取以对象方式，属性分别附件中文名、存储相对路径，取值分别是附件的中文名+逗号（,）拼接，相对路径+逗号（,）拼接--*/
    getStringFilesInfo(files,fileNameFeild='originalFilename',filePathFeild='fileUrl') {
      if (files && files.length > 0&&JSON.parse(files).length>0) {
        //新的格式：json串，含有相对路径和中文名
        if (files.includes(fileNameFeild) && files.includes(filePathFeild)) {
          const obj = JSON.parse(files);
          let filesNameList =obj[fileNameFeild]&&obj[fileNameFeild].length>0? obj[fileNameFeild].split(','):''
          let filesPathList =obj[filePathFeild]&&obj[filePathFeild].length>0? obj[filePathFeild].split(','):''
          if (filesNameList&&filesNameList.length > 0) {
            return  filesNameList.map((item,index) => {
              return {
                fileName: item,
                filePath: filesPathList[index]
              }
            })
          }else {
            return []
          }
        } else {
          //兼顾旧数据：只存储路径用逗号隔开拼接的情况
          let list = files.split(',')
          if (list && list.length > 0) {
           return list.map(item => {
              return {
                fileName: this.getFileName(item),
                filePath: item
              }
            })
          }
        }
      }else {
        return []
      }
    },

    /*兼顾之前路径数据（逗号隔开字符串形式，非包含属性originalFilename和filePath的数组对象形式），获取附件名称*/
    getFileName(path) {
      let fileName = ''
      if (path && path.length > 0) {
        if (path.lastIndexOf("\\") >= 0) {
          let reg = new RegExp("\\\\", "g");
          path = path.replace(reg, "/");
        }
        fileName = path.substring(path.lastIndexOf("/") + 1);
        //处理两种存储local\Minio
        if (fileName.includes('!inmsiighntio!')){
          fileName=fileName.replace('!inmsiighntio!','');
        }
        if (fileName.includes('!inlsioghctal!')){
          fileName=fileName.replace('!inlsioghctal!','');
        }
      }
      return fileName
    },
  }
}