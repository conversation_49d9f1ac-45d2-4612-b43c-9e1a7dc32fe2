<template>
  <div style='height: 100%' class='vScroll'>
    <a-card :bordered="false" style='min-width: 800px'>
      <div class="header-title">
        <span>出库单</span>
        <img
          @click="getGo"
          src="~@/assets/return1.png"
          alt=""
          style="width: 20px; height: 20px; cursor: pointer"
        />
      </div>
      <div class="header-oddNumber">
        <span>单号：{{ cardData.inCode }}</span>
        <div class="table-operator">
          <a-button v-print="'#printContent'" type="primary">打 印</a-button>
          <a-button @click="exportPDF" style='padding: 0 15px'>导出</a-button>
        </div>
      </div>

      <section ref="print" id="printContent">
        <div class="card-core">
          <span class="core-title">出库单</span>
          <div class="core-time">
            <span>出库日期：{{ cardData.inTime }}</span>
            <span style="color: rgba(0, 0, 0, 0.85)">单号：{{ cardData.inCode }}</span>
          </div>
          <div class="core-information">
            <span>出库类型：{{ cardData.inType }}</span>
            <span v-if="cardData.inType != '其他出库'">领用人：{{ cardData.supplier || cardData.outTarget }}</span>
            <span v-if="cardData.inType != '其他出库' && cardData.inType != '内部领用'"
            >部门：{{ cardData.contactName }}</span
            >
            <span v-if="cardData.inType != '其他出库'"
            >联系电话：{{ cardData.contactTel || cardData.outTargetPhone }}</span
            >
          </div>
          <table class='gridtable'>
            <thead>
            <tr>
              <td style='width: 6%'>序号</td>
              <td style='width: 18%'>物品编号</td>
              <td style='width: 11%'>物品名称</td>
              <td style='width: 11%'>当前库存</td>
              <td style='width: 8%'>单位</td>
              <td style='width: 10%'>数量</td>
              <td style='width: 10%'>单价</td>
              <td style='width: 12%'>总价</td>
              <td style='width: 14%'>备注</td>
            </tr>
            </thead>
            <tbody>
            <tr v-for='(item, index) in cardData.stockInfo' :key='index'>
              <td style='width: 6%'>{{ index + 1 }}</td>
              <td style='width: 18%'> {{ item.goodsCode }}</td>
              <td style='width: 11%'> {{ item.goodsName }}</td>
              <td style='width: 11%'> {{ item.stockNum }}</td>
              <td style='width: 8%'> {{ item.unitCode }}</td>
              <td style='width: 10%'>{{ item.operateNum }}</td>
              <td style='width: 10%'>¥ {{ item.unitPrice }} </td>
              <td style='width: 12%'>¥ {{ item.moneys }}</td>
              <td style='width: 14%'>{{ item.remarkss }}</td>
            </tr>
            </tbody>
          </table>
        </div>
        <div class="totalPrice" style="min-width: 700px">
          <div>
            <span>总金额：</span>
            <span>￥ {{ cardData.infoMoney }} 元</span>
          </div>
        </div>
        <div class="core-bottom" style="min-width: 700px">
          <div>
            <span>出库人：{{ cardData.realname }} </span>
            <span>联系电话：{{ cardData.phone }}</span>
            <span>签字/盖章：</span>
            <span>备注信息：{{ cardData.remarks }}</span>
          </div>
        </div>
      </section>
    </a-card>
  </div>
</template>
<script>
import { getAction } from '@api/manage'

export default {
  name: "ItilOutDepotDetails",
  props: {
    data: {
      type: Object
    }
  },
  data() {
    return {
      cardData:{}
    }
  },
  watch: {
    data: {
      handler(nVal, oVal) {
        this.cardData=nVal
      },
      deep: true,
      immediate: true,
    }
  },
  methods: {
    exportPDF() {
      getAction('/itilOutDepot/itilOutDepot/outEexportXls', { outId:this.cardData.outId }).then((res) => {
        if (res.success) {
          window.open(window._CONFIG['domianURL'] + '/sys/common/downloadFile/' + res.result)
        }
      })
    },
    //返回上一级
    getGo() {
      this.$parent.pButton1(0)
    }
  }
}
</script>

<style scoped lang="less">
@import '~@assets/less/scroll.less';

.header-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-family: PingFangSC-Medium;
  font-size: 18px;
  color: #000000;
}

.header-oddNumber {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 20px;
  margin-bottom: 20px;

  span {
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
  }

  .table-operator {
    margin: 0px;
  }

  div {
    button {
      margin-bottom: 0;
    }

    button:nth-child(2) {
      margin-right: 0px;
    }
  }
}

.card-core {
  display: flex;
  flex-direction: column;
  padding: 30px;
  padding-bottom: 80px;
  border: 1px solid #e8e8e8;

  .core-title {
    font-size: 28px;
    font-family: PingFangSC-Medium;
    color: #000000;
    text-align: center;
  }

  .core-time {
    width: 100%;
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    margin-top: 30px;

    span:nth-child(1) {
      color: rgba(0, 0, 0, 0.65);
    }

    span:nth-child(2) {
      color: #000000;
    }
  }

  .core-information {
    display: flex;
    justify-content: space-between;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    margin-top: 16px;
  }

  .gridtable {
    width: 100%;
    margin-top: 20px;
    font-family: verdana, arial, sans-serif;
    font-size: 14px;
    color: #606266;
    border-width: 1px;
    border-color: #e8e8e8;
    border-collapse: collapse;
    text-align: center;
    thead > tr > td {
      white-space: nowrap;
    }

    tbody > tr > td {
      white-space: normal;
      word-break: break-word;
    }

    td {
      padding: 5px;
      height: 40px;
      line-height: 32px;
      border-width: 1px;
      border-style: solid;
      border-color: #e8e8e8;
    }

    /*td:nth-child(1){
      width: 6%;
    }
    td:nth-child(2){
      width: 18%;
    }
    td:nth-child(3){
      width: 11%;
    }
    td:nth-child(4){
      width: 11%;
    }
    td:nth-child(5){
      width: 8%;
    }
    td:nth-child(6){
      width: 10%;
    }
    td:nth-child(7){
      width: 10%;
    }
    td:nth-child(8){
      width: 12%;
    }
    td:nth-child(9){
      width: 14%;
    }*/
  }
}

.totalPrice {
  height: 40px;
  padding: 0 30px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  font-family: PingFangSC-Medium;
  color: #000000;
  border: 1px solid #e8e8e8;
  border-top: none;

  div {
    span:nth-child(2) {
      color: red;
    }
  }
}

.core-bottom {
  padding: 30px;

  div {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;

    span {
      width: 33%;
    }

    span:nth-child(4) {
      margin-top: 20px;
    }
  }
}

li {
  list-style-type: none;
}

::v-deep .ant-table-tbody > tr > td.ant-table-column-sort {
  background-color: #fff;
}

::v-deep .ant-table-thead > tr > th.ant-table-column-sort {
  background-color: #fafafa;
}
</style>