<template>
  <j-modal
    :title='title'
    :width='width'
    :centered='true'
    :visible='visible'
    :destroyOnClose='true'
    switchFullscreen
    cancelText='关闭'
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @ok='handleOk'
    @cancel='handleCancel'
  >
    <a-spin :spinning='confirmLoading'>
      <j-form-container :disabled='disableSubmit'>
        <a-form-model ref='form' :model='model' :rules='validatorRules' slot='detail' v-bind='formItemLayout'>
          <a-row>
            <a-col :span='24'>
              <a-form-model-item label='名称' prop='commandName'>
                <a-input v-model='model.commandName'
                         placeholder="请输入名称"
                         :allowClear="true"
                         autocomplete="off" />
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
              <a-form-model-item label='命令' prop='commandCode'>
                <command-input v-model='model.commandCode' :node-id='"sensitive-commandList-editor"' @changeCommand="changeCommand"></command-input>
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
              <a-form-model-item label='状态' prop='disable'>
                <a-radio-group v-model='model.disable' :options='radioOption' />
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
              <a-form-model-item label='描述' prop='description'>
                <a-textarea v-model='model.description' :autoSize='{minRows:2,maxRows:6}'
                            :allow-clear='true' autocomplete='off' placeholder='请输入描述内容' />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </j-form-container>
    </a-spin>
  </j-modal>
</template>
<script>
import { getAction, httpAction } from '@api/manage'
import commandInput from '@views/devicesystem/modules/CommandInput.vue'

export default {
  name: 'SensitiveCommandModal',
  components: { commandInput },
  props: {
    //判断知识是否来自流程
    addFromFlowable:{
      type:Boolean,
      required:false,
      default:false
    }
  },
  data() {
    return {
      title: '新增',
      width: '800px',
      disableSubmit: false,
      visible: false,
      confirmLoading: false,
      formItemLayout: {
        labelCol: {
          xs:{span:24 },
          sm:{span:24},
          md:{span:5},
          lg:{span:3}
        },
        wrapperCol: {
          xs:{span:24 },
          sm:{span:24},
          md:{span:18},
          lg:{span:20}
        }
      },
      radioOption: [
        { label: '禁用', value: '0',key:'agree', checked: false },
        { label: '启用', value: '1',key:'disagree' , checked: true}],
      model: {
        disable: '1',
        commandCode:''
      },
      validatorRules: {
        commandName: [
          { required: true, message: '请输入名称' },
          { min: 1, max: 50, message: '名称长度不应超出50个字符',trigger:'blur'}
        ],
        commandCode: [
          { required: true, validator:this.validateCommand,trigger:'blur'}
        ],
        description: [
          { required: false, message: '请输入描述内容' },
          { min: 1, max: 255, message: '描述内容长度不应超出255个字符',trigger:'blur'}
        ],
      },
      url: {
        add: '/command/add',
        edit:"/command/edit"
      }
    }
  },
  methods: {
    add(){

      this.edit({ disable: '1',commandCode:''})
    },
    /*编辑知识*/
    edit(record) {
      this.visible = true
      this.model=Object.assign({},record)
    },
    /*提交表单数据*/
    handleOk() {
      let that = this
      that.$refs.form.validate((err, values) => {
        if (err&&!that.confirmLoading) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!that.model.id) {
            httpurl += that.url.add
            method = 'post'
          } else {
            httpurl += that.url.edit
            method = 'put'
          }
          let formData = JSON.parse(JSON.stringify(that.model))
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
                that.close()
              } else {
                that.$message.warning(res.message)
              }
              that.confirmLoading = false
            }).catch((err) => {
            that.$message.warning(err.message)
            that.confirmLoading = false
          })
        }
      })
    },
    handleCancel() {
      this.close()
    },
    close() {
      this.visible = false
    },
    changeCommand(value){
      this.$refs.form.validateField("commandCode",this.validateCommand)
    },
    validateCommand(rule, value, callback){
      if(rule.required){
        if(value&&value.length>0){
          if (value.length>255){
            callback('命令长度不应超出255个字符')
          }else {
            callback()
          }
        }else{
          callback('请输入命令')
        }
      }
      else {
        callback()
      }
    }
  }
}
</script>
<style scoped lang='less'>
@import '~@assets/less/normalModal.less';
</style>