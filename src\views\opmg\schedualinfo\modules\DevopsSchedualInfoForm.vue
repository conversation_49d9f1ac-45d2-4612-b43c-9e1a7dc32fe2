<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-item label="班次名称" :labelCol="labelColName" :wrapperCol="wrapperColName">
              <a-input
                v-decorator="['name', validatorRules.name]"
                :allowClear="true"
                autocomplete="off"
                placeholder="请输入班次名称"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="开始时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-time-picker v-decorator="['startTimeLong', validatorRules.startTimeLong]" format="HH:mm" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="结束时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-time-picker v-decorator="['endTimeLong', validatorRules.endTimeLong]" format="HH:mm" />
            </a-form-item>
          </a-col>
          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import pick from 'lodash.pick'
import { validateDuplicateValue } from '@/utils/util'
import JFormContainer from '@/components/jeecg/JFormContainer'
import JDate from '@/components/jeecg/JDate'
import moment from 'moment'

export default {
  name: 'DevopsSchedualInfoForm',
  components: {
    JFormContainer,
    JDate,
  },
  props: {
    //流程表单data
    formData: {
      type: Object,
      default: () => {},
      required: false,
    },
    //表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false,
    },
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false,
    },
  },
  data() {
    return {
      form: this.$form.createForm(this),
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 8 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      labelColName: {
        xs: { span: 24 },
        sm: { span: 4 },
      },
      wrapperColName: {
        xs: { span: 24 },
        sm: { span: 18 },
      },
      confirmLoading: false,
      validatorRules: {
        name: {
          rules: [{ required: true, message: '请输入班次名称!'},
          {min: 2, message:'班次名称长度应在 2-20 之间！' ,trigger: 'blur'},
          {max:20, message:'班次名称长度应在 2-20 之间！' ,trigger: 'blur'},],
        },
        startTimeLong: {
          rules: [{ required: true, message: '请选择开始时间!' }],
        },
        endTimeLong: {
          rules: [{ required: true, message: '请选择结束时间!' }],
        },
      },
      url: {
        add: '/schedualinfo/devopsSchedualInfo/add',
        edit: '/schedualinfo/devopsSchedualInfo/edit',
        queryById: '/schedualinfo/devopsSchedualInfo/queryById',
      },
    }
  },
  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    },
  },
  created() {
    //如果是流程中表单，则需要加载流程表单data
    this.showFlowData()
  },
  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(
          pick(
            this.model,
            'name',
            'startTime',
            'endTime',
            'createBy',
            'createTime',
            'updateBy',
            'updateTime',
            'sysOrgCode',
            'startTimeLong',
            'endTimeLong'
          )
        )
      })
    },
    //渲染流程表单数据
    showFlowData() {
      if (this.formBpm === true) {
        let params = { id: this.formData.dataId }
        getAction(this.url.queryById, params).then((res) => {
          if (res.success) {
            this.edit(res.result)
          }
        })
      }
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }

          // if(values.startTimeLong == '' || values.startTimeLong == null || values.endTimeLong=='' || values.endTimeLong==null){
          //      this.$message.error('请请填写值班时间!');
          //      that.confirmLoading = false;
          //      return;
          // }
          var startTimeLong = moment(values.startTimeLong).unix()
          var endTimeLong = moment(values.endTimeLong).unix()
          var startTime = moment(values.startTimeLong).format('HH:mm')
          var endTime = moment(values.endTimeLong).format('HH:mm')

          if (startTimeLong > endTimeLong) {
            this.$message.error('请确认时间的先后顺序!')
            that.confirmLoading = false
            return
          }
          let formData = Object.assign(this.model, values)
          formData.startTimeLong = startTimeLong
          formData.endTimeLong = endTimeLong
          formData.startTime = startTime
          formData.endTime = endTime
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    popupCallback(row) {
      this.form.setFieldsValue(
        pick(
          row,
          'name',
          'startTime',
          'endTime',
          'createBy',
          'createTime',
          'updateBy',
          'updateTime',
          'sysOrgCode',
          'startTimeLong',
          'endTimeLong'
        )
      )
    },
  },
}
</script>