<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    :centered="true"
    switchFullscreen
    @ok="handleOk"
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }"
    @cancel="handleCancel"
    cancelText="关闭"
  >
    <div style='min-height: 65vh'>
      <soft-patch-info-form
        ref="realForm"
        @ok="submitCallback"
        :disabled="disableSubmit"
        :patchTypes='patchTypes'
        :auditStatus='auditStatus'
        :restarts='restarts'
        :dict-options='dictOptions'
        :cpu-list='cpuList'
        :enables='enables'
        :resource-type-list='resourceTypeList'
        :softwareId='softwareId'
      ></soft-patch-info-form>
    </div>

  </j-modal>
</template>

<script>
import SoftPatchInfoForm from './SoftPatchInfoForm'
import SoftPatchInfoDetails from '@views/opmg/softwarePoc/patch/modules/SoftPatchInfoDetails.vue'
export default {
  name: 'SoftPatchInfoModal',
  components: {
    SoftPatchInfoDetails,
    SoftPatchInfoForm,
  },
  props: {
    patchTypes: {
      type:Array,
      default: () => [],
      required: false,
    },
    auditStatus: {
      type:Array,
      default: () => [],
      required: false,
    },
    restarts: {
      type:Array,
      default: () => [],
      required: false,
    },
    dictOptions: {
      type: Array,
      default: () => [],
      required: false
    },
    cpuList: {
      type: Array,
      default: () => [],
      required: false
    },
    enables: {
      type: Array,
      default: () => [],
      required: false
    },
    resourceTypeList: {
      type: Array,
      default: () => [],
      required: false
    },
    softwareId: {
      type:String,
      default:'',
      required:true
    }
  },
  data() {
    return {
      title: '',
      width: 1000,
      visible: false,
      disableSubmit: false,
    }
  },
  methods: {
    add() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.add()
      })
    },
    edit(record) {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.edit(record)
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      this.$refs.realForm.submitForm()
    },
    submitCallback() {
      this.$emit('ok')
      this.visible = false
    },
    handleCancel() {
      this.close()
    },
  },
}
</script>
<style scoped lang='less'>
@import '~@assets/less/normalModal.less';
</style>