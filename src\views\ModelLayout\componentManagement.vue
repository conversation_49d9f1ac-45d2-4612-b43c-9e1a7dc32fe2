<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll zhl zhll">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <a-card :bordered="false" :body-style="{ paddingBottom: '0' }" class="card-style">
        <!-- 查询区域 -->
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="组件名称">
                  <a-input
                    :maxLength='maxLength'
                    placeholder="请输入组件名称"
                    v-model="queryParam.name"
                    :allowClear="true"
                    autocomplete="off"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="组件类型">
                  <a-tree-select
                    allowClear
                    :getPopupContainer="(node) => node.parentNode"
                    :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }"
                    v-model="queryParam.cmpGroup"
                    placeholder="请选择组件类型"
                    :tree-data="platformTypes"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="colBtnsSpan()">
                <span
                  class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                >
                  <a-button
                    type="primary"
                    class="btn-search btn-search-style"
                    @click="searchQuery"
                    style="margin-left: 12px"
                    >查询</a-button
                  >
                  <a-button class="btn-reset btn-reset-style" @click="searchReset" style="margin-left: 8px"
                    >重置</a-button
                  >
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered="false" style="width: 100%; flex: auto">
        <!-- 操作按钮区域 -->
        <div class="table-operator table-operator-style">
          <a-button @click="handleAdd">新增</a-button>
          <a-button @click="batchDel" v-if="selectedRowKeys.length > 0">批量删除</a-button>
        </div>
        <!-- table区域-begin -->
        <a-table
          :columns="columns"
          :pagination="false"
          :dataSource="dataSource"
          :loading="loading"
          rowKey="id"
          bordered
          :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
          :expandedRowKeys="expandedRowKeys"
          :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          @expandedRowsChange="handleExpandedRowsChange"
        >
          <span slot="action" slot-scope="text, record" class="caozuo">
            <a href="javascript:;" @click="handleLook(record)" style="color: #409eff">查看</a>
            <a-divider type="vertical" />
            <a @click="handleEdit(record)">编辑</a>
            <a-divider type="vertical" />
            <a-dropdown>
              <a class="ant-dropdown-link">
                更多
                <a-icon type="down" />
              </a>
              <a-menu slot="overlay">
                <a-menu-item>
                  <a href="javascript:;" @click="handleAddSub(record)" style="color: #409eff">添加下级</a>
                </a-menu-item>
                <a-menu-item>
                  <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                    <a style="color: #409eff">删除</a>
                  </a-popconfirm>
                </a-menu-item>
              </a-menu>
            </a-dropdown>
          </span>
          <!-- 字符串超长截取省略号显示 -->
          <span slot="url" slot-scope="text">
            <j-ellipsis :value="text" :length="25" />
          </span>
          <!-- 字符串超长截取省略号显示-->
          <span slot="component" slot-scope="text">
            <j-ellipsis :value="text" />
          </span>
          <template slot="tooltip" slot-scope="text">
            <a-tooltip placement="topLeft" :title="text" trigger="hover">
              <div class="tooltip">
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
        <!-- table区域-end -->
      </a-card>
      <component-Management-Module ref="modalForm" @ok="modalFormOk"></component-Management-Module>
      <compManage-Look ref="lookForm"></compManage-Look>
    </a-col>
  </a-row>
</template>

<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import JEllipsis from '@/components/jeecg/JEllipsis'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
import componentManagementModule from './modules/componentManagementModule'
import compManageLook from './modules/compManageLook'
import { filterObj } from '@/utils/util'
import { modelList, ajaxGetDictItems, getDictItemsFromCache } from '@/api/api'
import { getAction, deleteAction } from '@/api/manage'

export default {
  name: 'componentManagement',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components: {
    componentManagementModule: () => import('./modules/componentManagementModule'),
    JEllipsis,
    compManageLook,
  },
  data() {
    return {
      maxLength:50,
      formItemLayout: {
        labelCol: {
          style: 'width:80px',
        },
        wrapperCol: {
          style: 'width:calc(100% - 80px)'
        }
      },
      platformTypes: [],
      queryParam: {},
      // 表头
      columns: [
        {
          title: '组件名称',
          dataIndex: 'name',
          key: 'name',
          customCell: () => {
            let cellStyle = 'text-align:left;width:200px;min-width: 180px;max-width:380px'
            return { style: cellStyle }
          },
        },
        {
          title: '序号',
          dataIndex: 'cmpSerial',
          key: 'cmpSerial',
          customCell: () => {
            let cellStyle = 'text-align:center;width:100px;min-width: 80px;max-width:180px'
            return { style: cellStyle }
          },
        },
        {
          title: '组件标识',
          dataIndex: 'code',
          key: 'code',
          customCell: () => {
            let cellStyle = 'width:180px;min-width: 150px;max-width:350px'
            return { style: cellStyle }
          },
        },
        {
          title: '组件类型',
          dataIndex: 'cmpGroupText',
          key: 'cmpGroupText',
          customCell: () => {
            let cellStyle = 'text-align:center;width:120px;min-width: 100px;max-width:250px'
            return { style: cellStyle }
          },
        },
        {
          title: '组件说明',
          dataIndex: 'cmpDesc',
          key: 'cmpDesc',
          scopedSlots: { customRender: 'tooltip' },
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 100px'
            return { style: cellStyle }
          },
        },
        {
          title: '操作',
          dataIndex: 'action',
          key: 'action',
          fixed: 'right',
          width: 180,
          align: 'center',
          scopedSlots: { customRender: 'action' },
        },
      ],

      loading: false,
      // 展开的行，受控属性
      expandedRowKeys: [],
      url: {
        list: '/flow/cmp/list',
        delete: '/flow/cmp/delete',
        deleteBatch: '/flow/cmp/deleteBatch',
      },
    }
  },
  created() {
    //获取组件类型的字典
    this.initDictData('cmpType', 'platformTypes')
  },
  methods: {
    initDictData(dictCode, listname) {
      //根据字典Code, 初始化字典数组
      ajaxGetDictItems(dictCode, null).then((res) => {
        if (res.success) {
          this[listname] = res.result
        }
      })
    },
    searchQuery() {
      this.loadData()
    },
    loadData() {
      this.dataSource = []
      let params = this.getQueryParams() //查询条件
      modelList(params).then((res) => {
        if (res.success) {
          this.dataSource = res.result
        }
      })
    },
    getQueryParams() {
      var param = Object.assign({}, this.queryParam)
      param.field = this.getQueryField()
      return filterObj(param)
    },
    handleAddSub(record) {
      if (record.isCmp == 1) {
        this.$message.warning('组件不可添加下级')
        return
      }
      this.$refs.modalForm.title = '新增'
      this.$refs.modalForm.localMenuType = 1
      this.$refs.modalForm.disableSubmit = false
      this.$refs.modalForm.edit({ parentId: record.id, protocol: record.protocol, cmpGroup: record.cmpGroup })
    },
    handleLook(record) {
      this.$refs.lookForm.edit(record)
      this.$refs.lookForm.title = '详情'
      this.$refs.lookForm.disableSubmit = true
    },
    handleExpandedRowsChange(expandedRows) {
      this.expandedRowKeys = expandedRows
    },
  },
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
</style>
