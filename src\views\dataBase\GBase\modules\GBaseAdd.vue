<template>
  <j-modal :title='title' :width='width' :visible='visible' :destroyOnClose='true'
           :okButtonProps="{ class:{'jee-hidden': disableSubmit} }" :centered='true' @ok='handleOk'
           @cancel='handleCancel'
           cancelText='关闭'>
    <a-spin :spinning='confirmLoading'>
      <j-form-container :disabled='disableSubmit' style='max-height: 70vh; overflow: auto'>
        <a-form-model ref='form' slot='detail' :model='gbaseForm' :rules='validatorRules' :labelCol='labelCol'
                      :wrapperCol='wrapperCol'>
          <a-row>
            <a-col :span='24'>
              <a-form-model-item label='IP' prop='dbIp'>
                <a-input style='width: 100%' v-model='gbaseForm.dbIp' :allow-clear='true' autocomplete='off'
                         placeholder='请输入IP地址'>
                  <a-tooltip slot='suffix' title='格式如：***************'>
                    <a-icon type='info-circle' style='color: #0ABBF6' />
                  </a-tooltip>
                </a-input>
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
              <a-form-model-item label='端口' prop='dbPort'>
                <a-input style='width: 100%' v-model='gbaseForm.dbPort' :allow-clear='true' autocomplete='off'
                         placeholder='请输入端口' />
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
              <a-form-model-item label='数据库名称' prop='dbInstance'>
                <a-input style='width: 100%' v-model='gbaseForm.dbInstance' :allow-clear='true'
                         autocomplete='off'
                         placeholder='请输入数据库名称' />
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
              <a-form-model-item label='数据库用户名' prop='dbUsername'>
                <a-input style='width: 100%' v-model='gbaseForm.dbUsername' :allow-clear='true'
                         autocomplete='off'
                         placeholder='请输入数据库用户名' />
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
              <a-form-model-item label='数据库密码' prop='dbPassword'>
                <a-input-password style='width: 100%' v-model='gbaseForm.dbPassword' :allow-clear='true'
                                  autocomplete='off'
                                  placeholder='请输入数据库密码' />
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
              <a-form-model-item label='SSH用户名' prop='sshUsername'>
                <a-input style='width: 100%' v-model='gbaseForm.sshUsername' :allow-clear='true'
                         autocomplete='off'
                         placeholder='请输入SSH用户名' />
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
              <a-form-model-item label='SSH密码' prop='sshPassword'>
                <a-input-password style='width: 100%' v-model='gbaseForm.sshPassword' :allow-clear='true'
                                  autocomplete='off'
                                  placeholder='请输入SSH密码' />
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
              <a-form-model-item label='SSH超时时间' prop='sshTimeout'>
                <a-input-number style='width: 100%' v-model='gbaseForm.sshTimeout' :min='1000'
                                :allow-clear='true'
                                autocomplete='off'
                                :formatter='value => `${value}ms`'
                                :parser="value => value.replace('ms', '')"
                                placeholder='请输入SSH超时时间' />
              </a-form-model-item>
            </a-col>
            <!--            <a-col :span='24'>-->
            <!--              <a-form-model-item label='秘钥' prop='sshkey'>-->
            <!--                <a-input style='width: 100%' v-model='gbaseForm.sshkey' :allow-clear='true'-->
            <!--                         autocomplete='off'-->
            <!--                         placeholder='请输入秘钥' />-->
            <!--              </a-form-model-item>-->
            <!--            </a-col>-->
            <a-col :span='24'>
              <a-form-model-item label='描述' prop='description'>
                <a-textarea style='width: 100%' v-model='gbaseForm.description' :autoSize='{minRows:2,maxRows:4}'
                            :allow-clear='true' autocomplete='off' placeholder='请输入描述' />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>

      </j-form-container>
    </a-spin>
  </j-modal>
</template>

<script>

import {
  httpAction,
  getAction
} from '@api/manage'
import { ajaxGetDictItem } from '@api/api'

export default {
  name: 'JkdjAdd',
  data() {
    return {
      title: '',
      width: '800px',
      visible: false,
      disableSubmit: false,
      confirmLoading: false,
      labelCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 5
        }
      },
      wrapperCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 16
        }
      },
      gbaseForm: {},
      interface: '',
      validatorRules: {
        dbIp: [
          {
            required: true,
            message: '请输入ip地址！'
          },
          {
            pattern: /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?))$/,
            message: '请输入正确的服务器地址！'
          }
        ],
        dbPort: [{
          required: true,
          message: '请输入端口！'
        },
          {
            pattern: /^([1-9]{1}|[1-9]\d{1}|[1-9]\d{2}|[1-9]\d{3}|[1-5][0-9]{4}|6[0-5][0-5][0-3][0-5])$/,
            message: "请输入正确端口号！"
          }
        ],
        dbInstance: [{
          required: true,
          message: '请输入数据库名称！'
        }, {
          min: 0,
          max: 20,
          message: '描述长度应在0-20之间！'
        }
        ],
        dbUsername: [{
          required: true,
          message: '请输入数据库用户名！'
        },
          {
            min: 0,
            max: 20,
            message: '描述长度应在0-20之间！'
          }
        ],
        dbPassword: [{
          required: true,
          message: '请输入数据库密码！'
        }],
        sshUsername: [{
          required: true,
          message: '请输入SSH用户名！'
        }],
        sshport: [{
          required: true,
          message: '请输入SSH端口！'
        }], sshPassword: [{
          required: true,
          message: '请输入SSH密码！'
        }],
        description: [
          {
            min: 0,
            max: 255,
            message: '描述长度应在0-255之间！'
          }
        ]
      },
      url: {
        add: '/gbase/manage/add',
        edit: '/gbase/manage/edit'
      }
    }
  },
  created() {
    this.getGatewayCodes()
    this.gbaseForm = this.initFormData()
    // this.getDatatype()
  },
  methods: {
    getGatewayCodes() {
      getAction('/configureBack/task/getGatewayList').then(res => {
        if (res.success) {
          this.gatewayCodes = res.result.map(el => {
            return { label: el.name, value: el.deviceCode }
          })
        }
      })
    },
    initFormData() {
      return {
        dbIp: '',
        dbPort: '15400',
        dbInstance: '',
        dbUsername: '',
        dbPassword: '',
        sshUsername: '',
        sshPassword: '',
        // sshkey: '',
        sshTimeout: '5000'
      }
    },
    add() {
      this.edit({})
    },
    edit(record) {
      this.visible = true
      this.$nextTick(() => {
        this.gbaseForm = Object.assign(this.gbaseForm, record)
      })
    },
    close() {
      this.gbaseForm = this.initFormData()
      this.visible = false
    },
    handleOk() {
      const that = this
      // console.log("喀喀喀 === >", that.gbaseForm)
      that.$refs.form.validate((err, value) => {
        if (err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!that.gbaseForm.id) {
            httpurl += that.url.add
            method = 'post'
          } else {
            httpurl += that.url.edit
            method = 'put'
          }
          let formData = {
            ...that.gbaseForm
          }
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
                that.close()
              } else {
                that.$message.warning(res.message)
              }
              that.confirmLoading = false
            }).catch((res) => {
            that.$message.warning(res.message)
            that.confirmLoading = false
          })
        }
      })
    },
    submitCallback() {
      this.$emit('ok')
      this.visible = false
    },
    handleCancel() {
      this.close()
    },
    setCorn(data) {
      if (data && data.target != null) {
        let dataList = data.target.value.split(' ')
        if (dataList[0] == '*') {
          this.$message.warning('请确认是否每秒都执行')
        }
      } else {
        let dataList = data.split(' ')
        if (dataList[0] == '*') {
          this.$message.warning('请确认是否每秒都执行')
        }
      }
      if (Object.keys(data).length == 0) {
        this.$message.warning('请输入cron表达式!')
      }
      // this.$nextTick(() => {
      //   this.gbaseForm.abutmentTask.executeCron = data;
      // })
    },
    validateIP(rule, value, callback) {
      let reg = /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)(:\d{1,5})?$/
      if (!reg.test(value)) {
        callback('请输入正确服务地址！')
      } else {
        callback()
      }
    }
  }
}
</script>
<style scoped lang='less'>
@import '~@assets/less/normalModal.less';

.color ::v-deep.ant-input {
  padding: 0px 30px 0px 11px !important;
}
</style>