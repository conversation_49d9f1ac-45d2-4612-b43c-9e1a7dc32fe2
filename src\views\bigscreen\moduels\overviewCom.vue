<template>
  <div class="overviewBox">
    <div style="height: 55%;width: 25%;display: flex;flex-direction: column;justify-content: center;">
      <img :src="src" alt="" style="height: 50%;width: 50%;margin: 0 auto;">
      <img src="../../../assets/szImg/bottom_light.png" alt="" style="height: 50%;width: 80%;margin: 0 auto;">
    </div>
    <div style="width: 75%;">
      <div style="margin-left: 50px;font-size: 0.45rem;letter-spacing: 5px;color: rgba(250,250,250,.8);">{{ value }}
      </div>
      <div class="infoBox">
        <div :style="{'color': color}" class="numBox">
          <img :src="src2" alt="" style="margin-right: 0.2rem;">{{ changeNum }}</div>
        <div style="font-size:0.25rem;">{{ title }}</div>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    props: {
      changeNum: {
        type: String,
        default: '',
      },
      title: {
        type: String,
        default: '',
      },
      value: {
        type: String,
        default: '',
      },
      src: {
        type: String,
        default: '',
      },
      src2: {
        type: String,
        default: '',
      },
      color: {
        type: String,
        default: '#8196FF',
      },
    },
    data() {
      return {

      };
    },
    methods: {

    }
  }
</script>

<style scoped lang="less">
  .overviewBox {
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding-left: 0.5rem;
  }

  .infoBox {
    color: rgba(250, 250, 250, .8);
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-left: 0.2rem;
  }

  .numBox {
    font-size: 0.22rem;
    letter-spacing: 5px;
    font-weight: 600;
  }
</style>