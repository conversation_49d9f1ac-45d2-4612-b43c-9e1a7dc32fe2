<template>
  <div>
    <a-card>
      <div class="colorBox">
        <span class="colorTotal">巡检参数</span>
        <span style="margin-left: 10px; color: #409eff; cursor: pointer" @click="handleEdit(data)">
          <a-icon type="edit" style="margin-right: 6px" />编辑
        </span>
        <div style="float: right">
          <img src="~@/assets/return1.png" alt="" @click="getGo" style="width: 20px; height: 20px; cursor: pointer" />
        </div>
      </div>
      <!-- <a-row>
      <a-col :span="12">
        <a-form-item label="巡检任务名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input :value="data.taskName" placeholder="请输入任务名称" disabled></a-input>
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="巡检类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <j-dict-select-tag
            type="list"
            disabled
            v-model="data.inspectionType"
            :trigger-change="true"
            dictCode="inspection_type"
            placeholder="请选择巡检类型"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="巡检报告推送" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <j-switch v-model="data.pushType" disabled></j-switch>
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="推送地址" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input :value="data.pushAddress" rows="4" auto-size placeholder="请输入推送地址" disabled />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="任务执行类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <j-dict-select-tag
            type="list"
            disabled
            v-model="data.taskexecuteType"
            :trigger-change="true"
            dictCode="taskexecute_type"
            placeholder="请选择巡检设置"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="周期执行" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input ref="innerVueCron" disabled :value="data.taskexecuteTime"></a-input>
        </a-form-item>
      </a-col>
    </a-row> -->

      <!-- <a-descriptions bordered>
        <a-descriptions-item label="巡检类型"> {{ data.inspectionDictText }} </a-descriptions-item>
        <a-descriptions-item label="巡检任务名称" :span="2"> {{ data.taskName }} </a-descriptions-item>
        <a-descriptions-item label="任务执行类型"> {{ data.taskexecuteTypeText }} </a-descriptions-item>
        <a-descriptions-item label="巡检报告推送" :span="2">
          <span v-if="data.pushType == 'N'">否</span> <span v-else>是</span>
        </a-descriptions-item>
        <a-descriptions-item label="推送地址"> {{ data.pushAddress }} </a-descriptions-item>
        <a-descriptions-item label="周期执行" :span="2"> {{ data.taskexecuteTime }} </a-descriptions-item>
      </a-descriptions> -->

      <div class="table">
        <ul>
          <li>
            <div class="leftTd">巡检类型</div>
            <div class="rightTd">{{ data.inspectionDictText }}</div>
            <div class="leftTd">巡检任务名称</div>
            <div class="rightTd">{{ data.taskName }}</div>
          </li>
          <li>
            <div class="leftTd">巡检报告推送</div>
            <div class="rightTd"><span v-if="data.pushType === 'N'">否</span> <span v-else>是</span></div>
            <div class="leftTd">通知模板</div>
            <div class="rightTd">{{ data.pushAddressText }}</div>
          </li>
          <li>
            <div class="leftTd">任务执行类型</div>
            <div class="rightTd">{{ data.taskexecuteTypeText }}</div>
            <div class="leftTd" v-if="data.taskexecuteType == '1'">执行频率</div>
            <div class="rightTd" v-if="data.taskexecuteType == '1'">{{ data.taskexecuteTime }}</div>
          </li>
        </ul>
      </div>

      <!-- <a-divider /> -->
    </a-card>
    <a-card style="margin-top: 16px">
      <div class="colorBox">
        <span class="colorTotal">设备选择</span>
      </div>
      <div>
        <!--  :scroll="{x:true}"-->
        <a-table size="middle" bordered v-if="data.inspectionType == '1'"  :columns="columns" :dataSource="dataSource" :rowKey="(record,index) => {return index}"
 class="j-table-force-nowrap" ></a-table>
        <a-table size="middle" bordered v-if="data.inspectionType == '2'"  :columns="columns1" :dataSource="dataSource" :rowKey="(record,index) => {return index}"
 class="j-table-force-nowrap" ></a-table>
      </div>
    </a-card>
    <config-inspection-modal ref="modalForm" @ok="modalFormOk"></config-inspection-modal>
  </div>
</template>

<script>
import ConfigInspectionModal from './ConfigInspectionModal.vue'
import { httpAction, getAction } from '@/api/manage'
export default {
  name: 'ConfigInspectionForm',
  props: {
    data: {
      type: Object,
    },
  },
  components: {
    ConfigInspectionModal,
  },
  data() {
    return {
      form: this.$form.createForm(this),
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 7 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 13 },
      },
      columns: [
        {
          title: '产品名称',
          align: 'center',
          dataIndex: 'categoryName',
        },
        {
          title: '设备名称',
          align: 'center',
          dataIndex: 'name',
        },
        {
          title: 'IP',
          align: 'center',
          dataIndex: 'ip',
        },
        // {
        //   title: '操作',
        //   align: 'center',
        //   dataIndex: 'caozuo',
        // },
      ],
      columns1: [
         {
            title: '应用名称',
            align: 'center',
            dataIndex: 'name',
          },
      ],
      dataSource: [],
      url: {
        list: '/autoInspection/devopsAutoInspection/getBindDeviceList',
        queryById: '/autoInspection/devopsAutoInspection/queryById',
      },
    }
  },
  mounted() {
    this.getDevices(this.data.id)
  },
  methods: {
    handleEdit: function (record) {

      this.$refs.modalForm.edit(record)
      this.$refs.modalForm.title = '编辑'
      this.$refs.modalForm.disableSubmit = false
    },
    modalFormOk() {
      let params = { id: this.data.id }
      getAction(this.url.queryById, params).then((res) => {
        if (res.success) {
          this.data = res.result
          this.getDevices(this.data.id)
        }
      })
    },
    //返回上一级
    getGo() {

      this.$parent.pButton2(0)
    },
    getDevices(id) {
      if (id == null || id == '' || id == undefined) {
        return
      }
      let params = { id: id, inspectionType: this.data.inspectionType }
      getAction(this.url.list, params).then((res) => {
        if (res.success) {

          this.dataSource = res.result.records
        }
      })
    },
  },
}
</script>
<style lang="less" scoped>
.colorBox {
  margin-bottom: 18px;
}
.colorTotal {
  padding-left: 7px;
  border-left: 4px solid #1e3674;
}
.leftTd {
  width: 17%;
  background-color: #fafafa;
  padding: 16px 24px;
  text-align: center;
}
.rightTd {
  width: 35%;
  padding: 16px 24px;
}
// /deep/ .ant-descriptions-item-label{
//   text-align: center !important;
// }
.table {
  width: 100%;
  margin-top: 20px;
  ul {
    width: 100%;
    padding: 0 !important;
    border: 1px solid #e8e8e8;
    border-bottom: none;
    li {
      width: 100%;
      border-bottom: 1px solid #e8e8e8;
      list-style: none;
      display: flex;
      div {
        display: flex;
        /*width: 20%;*/
        border-left: 1px solid #e8e8e8;
        /*padding: 16px 24px;*/
      }
      div:nth-child(1) {
        background: #fafafa;
        border-left: none;
        display: flex;
        justify-content: center;
      }
      div:nth-child(3) {
        background: #fafafa;
        text-align: center;
        display: flex;
        justify-content: center;
      }
      div:nth-child(2n + 0) {
        width: 30%;
      }
    }
  }
}
</style>