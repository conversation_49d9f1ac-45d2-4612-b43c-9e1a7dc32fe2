<template>
  <a-col :xl='20' :lg='18' :md='16' :sm='14' :xs='14' style='height: 100%; overflow: hidden; overflow-y: auto'>
    <a-row style='height: 100%; margin-left: 16px; margin-right: 4px'>
      <a-col style='width: 100%; height: 100%; display: flex; flex-direction: column'>
        <!-- 查询区域 -->
        <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0', marginRight: '12px' }" class='card-style'
          style='width: 100%'>
          <div class='table-page-search-wrapper'>
            <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
              <a-row :gutter='24' ref='row'>
                <a-col :span='spanValue'>
                  <a-form-item label='子网名称'>
                    <a-input placeholder='请输入子网名称' v-model='queryParam.subnetName' :allowClear='true'
                      autocomplete='off' :maxLength="maxLength"/>
                  </a-form-item>
                </a-col>
                <a-col :span='spanValue'>
                  <a-form-item label='子网地址'>
                    <a-input placeholder='请输入子网地址' v-model='queryParam.subnetAddress' :allowClear='true'
                      autocomplete='off' :maxLength="maxLength"/>
                  </a-form-item>
                </a-col>
                <a-col :span='spanValue'>
                  <a-form-item label='子网掩码'>
                    <a-input placeholder='请输入子网掩码' v-model='queryParam.mask' :allowClear='true' autocomplete='off'
                      :maxLength="maxLength" />
                  </a-form-item>
                </a-col>
                <a-col :span='spanValue' v-show='toggleSearchStatus'>
                  <a-form-item label='使用部门'>
                    <a-input placeholder='请输入使用部门' v-model='queryParam.departName' :allowClear='true'
                      autocomplete='off' :maxLength="maxLength" />
                  </a-form-item>
                </a-col>
                <a-col :span='spanValue' v-show='toggleSearchStatus'>
                  <a-form-item label='使用位置'>
                    <a-input placeholder='请输入使用位置' v-model='queryParam.location' :allowClear='true'
                      autocomplete='off' :maxLength="maxLength"/>
                  </a-form-item>
                </a-col>
                <a-col :span='colBtnsSpan()'>
                  <span class='table-page-search-submitButtons'
                    :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                    <a-button type='primary' class='btn-search btn-search-style' @click='searchQuery'>查询</a-button>
                    <a-button class='btn-reset btn-reset-style' @click='searchReset'>重置</a-button>
                    <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                      {{ toggleSearchStatus ? '收起' : '展开' }}
                      <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                    </a>
                  </span>
                </a-col>
              </a-row>
            </a-form>
          </div>
        </a-card>
        <a-card :bordered='false' style='width: 100%; flex: auto'>
          <div class='table-operator table-operator-style'>
            <a-button @click='handleAdd({})'>添加子网</a-button>
            <a-dropdown v-if='selectedRowKeys.length > 0'>
              <a-menu slot="overlay" style='text-align: center'>
                <a-menu-item key='1' @click='batchDel'>删除</a-menu-item>
              </a-menu>
              <a-button> 批量操作
                <a-icon type='down' />
              </a-button>
            </a-dropdown>
          </div>
          <a-table ref='table' bordered :row-key='(record, index) => {return record.id}' :columns='childrenColumn'
            :dataSource='dataSource' :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
            :pagination='ipagination' :loading='loading'
            :rowSelection='{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }' @change='handleTableChange'>
            <span slot='action' class='caozuo' slot-scope='text, record'>
              <a @click='handleDetailPage1(record)'>查看</a>
              <a-divider type='vertical' />
              <a-dropdown>
                <a class='ant-dropdown-link'>更多
                  <a-icon type='down' /></a>
                <a-menu slot='overlay'>
                  <a-menu-item>
                    <a @click='handleEdit(record)' style="color: #409eff">编辑</a>
                  </a-menu-item>
                  <a-menu-item>
                    <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                      <a style="color: #409eff">删除</a>
                    </a-popconfirm>
                  </a-menu-item>
                </a-menu>
              </a-dropdown>
            </span>
          </a-table>
        </a-card>
      </a-col>
    </a-row>
    <network-plan-children ref='modalForm' @ok='modalFormOk'> </network-plan-children>
  </a-col>
</template>

<script>
  import networkPlanChildren from './modules/networkPlanChildren.vue'
  import {
    filterObj
  } from '@/utils/util'
  import {
    httpAction,
    getAction,
    postAction,
    deleteAction
  } from '@/api/manage'
  import {
    YqFormSearchLocation
  } from '@/mixins/YqFormSearchLocation'
  export default {
    mixins: [YqFormSearchLocation],
    components: {
      networkPlanChildren
    },
    props: {
      groupId: {
        type: String,
        default: '',
      },
      topData: {
        type: Object,
        default: () => {},
      },
    },
    data() {
      return {
        maxLength:50,
        formItemLayout: {
          labelCol: {
            style: 'width:80px'
          },
          wrapperCol: {
            style: 'width:calc(100% - 80px)'
          }
        },
        loading: false,
        queryParam: {},
        columns: [],
        dataSource: [],
        ipagination: {
          current: 1,
          pageSize: 10,
          pageSizeOptions: ['10', '20', '50'],
          showTotal: (total, range) => {
            return ' 共' + total + '条'
          },
          showQuickJumper: true,
          showSizeChanger: true,
          total: 0
        },
        selectedRowKeys: [],
        selectionRows: [],
        toggleSearchStatus: false,
        //子网表头
        childrenColumn: [{
            title: '子网名称',
            dataIndex: 'subnetName',
            customCell: () => {
              let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '子网地址',
            dataIndex: 'subnetAddress',
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 150px;max-width:300px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '子网掩码',
            dataIndex: 'mask',
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 100px;max-width:300px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '使用部门',
            dataIndex: 'departName',
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 100px;max-width:300px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '使用位置',
            dataIndex: 'location',
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 100px;max-width:300px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            fixed: 'right',
            width: 140,
            scopedSlots: {
              customRender: 'action'
            }
          }
        ],
        url: {
          list: 'devops/ip/subnet/list',
          delete: 'devops/ip/subnet/delete',
          deleteBatch: 'devops/ip/subnet/deleteBatch',
        },

      };
    },
    watch: {
      groupId: {
        immediate: true,
        handler(newVal) {
          this.queryParam.subnetGroupId = newVal
          this.loadData()
        }
      }
    },
    methods: {
      searchQuery() {
        this.loadData(1)
      },
      searchReset() {
        this.queryParam.subnetName = ''
        this.queryParam.subnetAddress = ''
        this.queryParam.mask = ''
        this.queryParam.departName = ''
        this.queryParam.location = ''
        this.loadData(1)
      },
      onSelectChange(selectedRowKeys, selectionRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectionRows = selectionRows
      },
      handleAdd({}) {
        this.$refs.modalForm.add({}, this.topData);
        this.$refs.modalForm.title = '新增';
        this.$refs.modalForm.disableSubmit = false;
      },
      handleEdit: function (record) {
        this.$refs.modalForm.edit(record);
        this.$refs.modalForm.title = '编辑';
        this.$refs.modalForm.disableSubmit = false;
      },
      handleDelete: function (id) {
        if (!this.url.delete) {
          this.$message.error('请设置url.delete属性!')
          return
        }
        var that = this
        deleteAction(that.url.delete, {
          id: id
        }).then((res) => {
          if (res.success) {
            //重新计算分页问题
            that.reCalculatePage(1)
            that.$message.success(res.message)
            that.loadData()
          } else {
            that.$message.warning(res.message)
          }
        })
      },
      reCalculatePage(count) {
        //总数量-count
        let total = this.ipagination.total - count
        //获取删除后的分页数
        let currentIndex = Math.ceil(total / this.ipagination.pageSize)
        //删除后的分页数<所在当前页
        if (currentIndex < this.ipagination.current) {
          this.ipagination.current = currentIndex
        }
      },
      handleTableChange(pagination, filters, sorter) {
        //分页、排序、筛选变化时触发
        //TODO 筛选
        if (Object.keys(sorter).length > 0) {
          this.isorter.column = sorter.field
          this.isorter.order = 'ascend' == sorter.order ? 'asc' : 'desc'
        }
        this.ipagination = pagination
        this.loadData()
      },
      handleToggleSearch() {
        this.toggleSearchStatus = !this.toggleSearchStatus
      },
      loadData(arg) {
        if (!this.url.list) {
          this.$message.error('请设置url.list属性!')
          return
        }
        //加载数据 若传入参数1则加载第一页的内容
        if (arg === 1) {
          this.ipagination.current = 1
        }

        var params = this.getQueryParams() //查询条件
        this.loading = true
        getAction(this.url.list, params).then((res) => {
          if (res.success) {
            //update-begin---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
            this.dataSource = res.result.records || res.result
            if (this.dataSource.length < 9) {
              this.clientHeight = false
            }
            //author:weng    Date:20210402  for：if(res.result.total>0) 有错误，无查询结果时，页码显示有问题
            this.ipagination.total = res.result.total ? res.result.total : 0
            //update-end---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
          }
          if (res.code === 510) {
            this.$message.warning(res.message)
          }
          this.loading = false
        })
        this.$emit('refresh')
      },
      batchDel: function () {
        if (!this.url.deleteBatch) {
          this.$message.error('请设置url.deleteBatch属性!')
          return
        }
        if (this.selectedRowKeys.length <= 0) {
          this.$message.warning('请选择一条记录！')
          return
        } else {
          var ids = ''
          for (var a = 0; a < this.selectedRowKeys.length; a++) {
            ids += this.selectedRowKeys[a] + ','
          }
          var that = this
          this.$confirm({
            title: '确认删除',
            okText: '是',
            cancelText: '否',
            content: '是否删除选中数据?',
            onOk: function () {
              that.loading = true
              deleteAction(that.url.deleteBatch, {
                  ids: ids
                })
                .then((res) => {
                  if (res.success) {
                    //重新计算分页问题
                    that.reCalculatePage(that.selectedRowKeys.length)
                    that.$message.success(res.message)
                    that.loadData()
                    that.onClearSelected()
                  } else {
                    that.$message.warning(res.message)
                  }
                })
                .finally(() => {
                  that.loading = false
                })
            }
          })
        }
      },
      getQueryParams() {
        //获取查询条件
        let sqp = {}
        if (this.superQueryParams) {
          sqp['superQueryParams'] = encodeURI(this.superQueryParams)
          sqp['superQueryMatchType'] = this.superQueryMatchType
        }
        var param = Object.assign(sqp, this.queryParam, this.isorter, this.filters)
        param.field = this.getQueryField()
        param.pageNo = this.ipagination.current
        param.pageSize = this.ipagination.pageSize
        return filterObj(param)
      },
      getQueryField() {
        //TODO 字段权限控制
        var str = 'id,'
        this.columns.forEach(function (value) {
          str += ',' + value.dataIndex
        })
        return str
      },
      handleDetailPage1(record) {
        this.$emit('detail', 2, record)
      },
      modalFormOk() {
        // 新增/修改 成功时，重载列表
        this.loadData()
      },
    }
  }
</script>

<style scoped lang="less">
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';
</style>