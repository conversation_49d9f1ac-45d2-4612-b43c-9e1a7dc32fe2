<template>
  <div class="setting">
    <a-form :form="form" slot="setting">
      <a-row>
        <a-col :span="12">
          <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol">
            <template slot="label">
              <span>显示行号</span>
              <a-tooltip placement="topLeft">
                <template slot="title">
                  <span>每一行前边显示对应的行数</span>
                </template>
                <a-icon type="question-circle" />
              </a-tooltip>
            </template>
            <a-switch v-model="form.lineNumbers" checked-children="开" un-checked-children="关" default-checked />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item class="two-words" :labelCol="labelCol" :wrapperCol="wrapperCol">
			<template slot="label">
              <span>自动换行</span>
              <a-tooltip placement="topLeft">
                <template slot="title">
                  <span>光标移动到编辑器最后时自动转到下一行</span>
                </template>
                <a-icon type="question-circle" />
              </a-tooltip>
            </template>
            <a-switch v-model="form.lineWrapping" checked-children="开" un-checked-children="关" default-checked />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item class="two-words" :labelCol="labelCol" :wrapperCol="wrapperCol">
			<template slot="label">
              <span>触发提示</span>
              <a-tooltip placement="topLeft">
                <template slot="title">
                  <span>触发自动补全提示的按键</span>
                </template>
                <a-icon type="question-circle" />
              </a-tooltip>
            </template>
            <a-select placeholder="请选择触发提示" default-value="Ctrl" v-model="form.extraKeys" style="width: 120px">
              <a-select-option value="Ctrl"> Ctrl </a-select-option>
              <a-select-option value="Tab"> Tab </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item class="two-words" :labelCol="labelCol" :wrapperCol="wrapperCol">
			<template slot="label">
              <span>激活当前行</span>
              <a-tooltip placement="topLeft">
                <template slot="title">
                  <span>当前行背景高亮显示</span>
                </template>
                <a-icon type="question-circle" />
              </a-tooltip>
            </template>
            <a-switch v-model="form.styleActiveLine" checked-children="开" un-checked-children="关" default-checked />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item class="two-words" :labelCol="labelCol" :wrapperCol="wrapperCol">
			<template slot="label">
              <span>表查询sql</span>
              <a-tooltip placement="topLeft">
                <template slot="title">
                  <span>左侧数据库表名查询语句</span>
                </template>
                <a-icon type="question-circle" />
              </a-tooltip>
            </template>
            <a-textarea v-model="form.tableSql"></a-textarea>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item class="two-words" :labelCol="labelCol" :wrapperCol="wrapperCol">
			<template slot="label">
              <span>列查询sql</span>
              <a-tooltip placement="topLeft">
                <template slot="title">
                  <span>每张表对应的列字段名查询语句</span>
                </template>
                <a-icon type="question-circle" />
              </a-tooltip>
            </template>
            <a-textarea v-model="form.rowSql"></a-textarea>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item class="two-words" :labelCol="labelCol" :wrapperCol="wrapperCol">
			<template slot="label">
              <span>默认查询条数</span>
              <a-tooltip placement="topLeft">
                <template slot="title">
                  <span>为防止查询数据量过大，可设置默认数据查询条数</span>
                </template>
                <a-icon type="question-circle" />
              </a-tooltip>
            </template>
            <a-input-number id="inputNumber" v-model="form.dataSize" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item class="two-words" :labelCol="labelCol" :wrapperCol="wrapperCol">
			<template slot="label">
              <span>主题</span>
              <a-tooltip placement="topLeft">
                <template slot="title">
                  <span>sql编辑器主题样式选择</span>
                </template>
                <a-icon type="question-circle" />
              </a-tooltip>
            </template>
            <a-select
              placeholder="请选择主题"
              default-value="default"
              v-model="form.theme"
              style="width: 220px"
              @change="handleChange"
            >
              <a-select-option value="default"> default </a-select-option>
              <a-select-option value="solarized light"> solarized light </a-select-option>
              <a-select-option value="idea"> idea </a-select-option>
              <a-select-option value="eclipse"> eclipse </a-select-option>
              <a-select-option value="cobalt"> cobalt </a-select-option>
              <a-select-option value="ayu-dark"> ayu-dark </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item class="two-words" label="主题预览" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <img :src="imgUrl" width="200px" height="120px" />
          </a-form-item>
        </a-col>
        <a-col :span="12"> </a-col>
        <a-col :span="24" style="text-align: center">
          <a-button type="primary" @click="submitForm">提 交</a-button>
        </a-col>
      </a-row>
    </a-form>
  </div>
</template>
<script>
import { getAction, postAction } from '@/api/manage'
export default {
  name: 'Setting',
  data() {
    return {
      form: {
        lineNumbers: false,
        lineWrapping: false,
        styleActiveLine: false,
        extraKeys: 'Ctrl',
        theme: 'default',
        tableSql: null,
        rowSql: null,
        dataSize: 1000,
      },
      imgUrl: require('@/assets/theme/default.png'),
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      url: {
        add: '/sqlEditor/save',
        getSetting: '/sqlEditor/querySetting',
      },
    }
  },
  created() {
    this.queryUserSetting()
  },
  methods: {
    queryUserSetting() {
      getAction(this.url.getSetting).then((res) => {
        if (res.success) {
          this.form = res.result
          this.handleChange(res.result.theme)
        } else {
          this.$message.error(res.message)
        }
      })
    },
    handleChange(value) {
      switch (value) {
        case 'default':
          this.imgUrl = require('@/assets/theme/default.png')
          break
        case 'solarized light':
          this.imgUrl = require('@/assets/theme/solarized light.png')
          break
        case 'idea':
          this.imgUrl = require('@/assets/theme/idea.png')
          break
        case 'eclipse':
          this.imgUrl = require('@/assets/theme/eclipse.png')
          break
        case 'cobalt':
          this.imgUrl = require('@/assets/theme/cobalt.png')
          break
        case 'ayu-dark':
          this.imgUrl = require('@/assets/theme/ayu-dark.png')
          break
      }
    },
    submitForm() {
      var obj = this.form
      var dataList = []
      for (let key in obj) {
        if ((key == 'lineNumbers' || key == 'lineWrapping' || key == 'styleActiveLine') && obj[key] === true) {
          dataList.push({
            configkey: key,
            configvalue: '1',
          })
        } else if ((key == 'lineNumbers' || key == 'lineWrapping' || key == 'styleActiveLine') && obj[key] === false) {
          dataList.push({
            configkey: key,
            configvalue: '0',
          })
        } else {
          dataList.push({
            configkey: key,
            configvalue: obj[key],
          })
        }
      }
      postAction(this.url.add, dataList).then((res) => {
        if (res.success) {
          this.$message.success(res.message)
          this.$emit('updateSetting')
        } else {
          this.$message.error(res.message)
        }
      })
    },
  },
}
</script>
<style scoped>
.setting {
  height: 100%;
  overflow: auto;
  box-sizing: border-box;
  padding: 30px 20px;
  background-color: #fff;
}
</style>
