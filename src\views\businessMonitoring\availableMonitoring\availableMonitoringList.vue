<template>
  <a-row :gutter='10' style='height: 100%' class='vScroll'>
    <a-col style='width: 100%; height: 100%; display: flex; flex-direction: column'>
      <!-- 查询区域 -->
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <div class='table-page-search-wrapper'>
          <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
            <a-row :gutter='24' ref='row'>
              <a-col :span='spanValue'>
                <a-form-item label='任务名称'>
                  <a-input :maxLength='maxLength' placeholder='请输入任务名称' v-model='queryParam.name' :allowClear='true'
                           autocomplete='off' />
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label='业务名称'>
                  <a-input :maxLength='maxLength' placeholder='请输入业务名称' v-model='queryParam.bizInfoName' :allowClear='true'
                           autocomplete='off' />
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label='最近执行结果'>
                  <a-select
                    v-model='queryParam.lastTestResult'
                    placeholder='请选择执行结果'
                    :show-search='true'
                    :getPopupContainer='(node) => node.parentNode'
                    option-filter-prop='label'
                    :allow-clear='true'>
                    <a-select-option v-for='item in resultStatusList' :label='item.label' :value='item.value'
                                     :key='item.value'>
                      {{ item.label }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span='spanValue' v-show='toggleSearchStatus'>
                <a-form-item label='最近执行时间'>
                  <a-range-picker class='a-range-picker-choice-date' @change='onChangeTime'
                                  v-model='queryParam.testTime' format='YYYY-MM-DD'
                                  :placeholder="['开始时间', '结束时间']" />
                </a-form-item>
              </a-col>
              <a-col :span='spanValue' v-show='toggleSearchStatus'>
                <a-form-item label='任务状态'>
                  <a-select
                    v-model='queryParam.status'
                    placeholder='请选择任务状态'
                    :show-search='true'
                    :getPopupContainer='(node) => node.parentNode'
                    option-filter-prop='label'
                    :allow-clear='true'
                  >
                    <a-select-option v-for='item in taskStatusList' :label='item.label' :value='item.value'
                                     :key='item.value'>
                      {{ item.label }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span='spanValue' v-show='toggleSearchStatus'>
                <a-form-item label='锁定状态'>
                  <a-select
                    v-model='queryParam.runLocked'
                    placeholder='请选择锁定状态'
                    :show-search='true'
                    :getPopupContainer='(node) => node.parentNode'
                    option-filter-prop='label'
                    :allow-clear='true'
                  >
                    <a-select-option v-for='item in runLockedList' :label='item.label' :value='item.value'
                                     :key='item.value'>
                      {{ item.label }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span='colBtnsSpan()'>
                <span class='table-page-search-submitButtons'
                      :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button class='btn-search btn-search-style' type='primary' @click='searchQuery'>查询</a-button>
                  <a-button class='btn-reset btn-reset-style' @click='searchReset'>重置</a-button>
                  <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>

      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <!-- 操作按钮区域 -->
        <div class='table-operator table-operator-style'>
          <a-button class='btn-add' @click='handleAdd'>新增</a-button>
          <a-dropdown v-if='selectedRowKeys.length > 0'>
            <a-menu slot='overlay' style='text-align: center'>
              <a-menu-item key='1' @click='batchDel'>删除</a-menu-item>
            </a-menu>
            <a-button>批量操作
              <a-icon type='down' />
            </a-button>
          </a-dropdown>
          <a-popover title='可用监控状态说明'>
            <template slot='content'>
              <div style='display: flex'>
                <div>
                  <span>禁用：</span>
                  <a-icon type='stop' theme='twoTone' two-tone-color='#eb2f96' class='state-img'/>
                </div>
                <div class='stateBox'>
                  <span>锁定：</span>
                  <a-icon type='lock' theme='twoTone' two-tone-color='#fa8c16' class='state-img state-img-margin'/>
                </div>
              </div>
            </template>
            <a-icon type='question-circle' theme='twoTone' style='font-size: 18px' />
          </a-popover>
        </div>

        <!-- table区域-begin -->
        <a-table ref='table'
                 bordered
                 :rowKey='(record,index)=>{return record.id}'
                 :columns='columns'
                 :dataSource='dataSource'
                 :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
                 :pagination='ipagination'
                 :loading='loading'
                 :rowSelection='{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }'
                 @change='handleTableChange'>
          <!-- 字符串超长截取省略号显示-->
          <span slot='templateContent' slot-scope='text'>
            <j-ellipsis :value='text' :length='25' />
          </span>
          <template slot='taskName' slot-scope='text,record'>
            <a-icon v-if='record.status!=1' type='stop' theme='twoTone' two-tone-color='#eb2f96' class='state-img'/>
            <a-icon v-if='record.runLocked==1' type='lock' theme='twoTone' two-tone-color='#fa8c16' class='state-img state-img-margin'/>
            {{ text }}
          </template>
          <template slot='business' slot-scope='text,record'>
            <span v-if='!text' style='color:#ff0000'>业务已不存在</span>
            <span v-else>{{text}}</span>
          </template>
          <template slot='result' slot-scope='text,record'>
            <a-tag v-if='text===0'  color='volcano-inverse' style='width: 52px'>失败</a-tag>
            <a-tag v-else-if='text===1' color='lime-inverse' style='width: 52px'>成功</a-tag>
            <a-tag v-else color='blue-inverse'>未执行</a-tag>
          </template>
          <span slot='action' slot-scope='text, record' class='caozuo'>
            <a @click='handleDetailPages(record)'>查看</a>
<!--            <span v-if='record.runLocked!=1&&record.stepConfig!=null'>
              <a-divider type='vertical' />
              <a class='overlay' @click='handleExecution(record)'>执行</a>
            </span>-->
             <span v-if='record.runLocked!=1'>
              <a-divider type='vertical' />
              <a class='overlay' @click='handleExecution(record)'>执行</a>
            </span>
             <a-divider type='vertical' />
             <a-dropdown>
                  <a class='ant-dropdown-link'>更多
                    <a-icon type='down' /></a>
                  <a-menu slot='overlay'>
                    <a-menu-item>
                     <a class='overlay' @click='handleEdit(record)'>编辑</a>
                    </a-menu-item>
                     <a-menu-item>
                       <a class='overlay' @click='handleDelete(record)'>删除</a>
                    </a-menu-item>
                    <a-menu-item v-if='record.status==1'>
                     <a class='overlay' @click='taskEnable(record)'>禁用</a>
                    </a-menu-item>
                     <a-menu-item v-else>
                     <a class='overlay' @click='taskEnable(record)'>启用</a>
                    </a-menu-item>
                     <a-menu-item>
                     <a class='overlay' @click='handleCopy(record)'>复制</a>
                    </a-menu-item>
                     <a-menu-item>
                     <a class='overlay' @click='handleConfig(record)'>步骤配置</a>
                    </a-menu-item>
                     <a-menu-item v-if='record.runLocked==1'>
                     <a class='overlay' @click='handleExecution(record)'>强制执行</a>
                    </a-menu-item>
                  </a-menu>
                </a-dropdown>
          </span>
          <template slot='tooltip' slot-scope='text'>
            <a-tooltip placement='topLeft' :title='text' trigger='hover'>
              <div class='tooltip'>
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
        <!-- table区域-end -->
      </a-card>
      <available-monitoring-modal ref='modalForm' @ok='loadData'></available-monitoring-modal>
    </a-col>
  </a-row>
</template>

<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
import availableMonitoringModal from './modules/availableMonitoringModal'
import { deleteAction, getAction, putAction } from '@api/manage'

export default {
  name: 'availableMonitoringList',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components: {
    availableMonitoringModal
  },
  data() {
    return {
      maxLength:50,
      description: '可用监控设置界面',
      formItemLayout: {
        labelCol: {
          style: 'width:105px'
        },
        wrapperCol: {
          style: 'width:calc(100% - 105px)'
        }
      },
      resultStatusList: [{ label: '失败', value: '0' }, { label: '成功', value: '1' }, { label: '未执行', value: '2' }],
      taskStatusList: [{ label: '禁用', value: '0' }, { label: '启用', value: '1' }],
      runLockedList: [{ label: '未锁定', value: '0' }, { label: '锁定', value: '1' }],
      // 表头
      columns: [
        {
          title: '任务名称',
          dataIndex: 'name',
          scopedSlots: {
            customRender: 'taskName'
          },
          customCell: () => {
            let cellStyle = 'text-align: left'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '业务名称',
          dataIndex: 'bizInfoName',
          customCell: () => {
            let cellStyle = 'text-align: center'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'business'
          }
        },
        {
          title: '最近执行结果',
          dataIndex: 'lastTestResult',
          scopedSlots: {
            customRender: 'result'
          },
          customCell: () => {
            let cellStyle = 'text-align: center;width: 130px;'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '最近执行时间',
          dataIndex: 'lastTestTime',
          customCell: () => {
            let cellStyle = 'text-align: center;width:200px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '任务描述',
          dataIndex: 'description',
          scopedSlots: {
            customRender: 'tooltip'
          },
          customCell: () => {
            let cellStyle = 'text-align: left;min-width:150px;max-width:300px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '操作',
          width: 180,
          fixed: 'right',
          dataIndex: 'action',
          scopedSlots: {
            customRender: 'action'
          }
        }
      ],
      url: {
        list: '/business/availability/list',
        delete: '/business/availability/remove',
        deleteBatch: '/business/availability/deleteBatch',
        status: '/business/availability/updateStatus',
        execution: '/business/availability/runNow',
        unlock: '/business/availability/forceUnlock'
      },
     disableMixinCreated: true
    }
  },
  activated() {
    this.loadData()
  },
  methods: {
    loadData(arg) {
      if (!this.url.list) {
        this.$message.error('请设置url.list属性!')
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1
      }
      //从数据中心index.vue路由跳转后，根据路由参数设置列表查询条件
      if (this.$route.params.bizInfoName) {
        this.queryParam.bizInfoName = this.$route.params.bizInfoName
      }
      var params = this.getQueryParams() //查询条件
      this.loading = true
      getAction(this.url.list, params).then((res) => {
        if (res.success) {
          this.dataSource = res.result.records || res.result
          if (this.dataSource.length < 9) {
            this.clientHeight = false
          }
          this.ipagination.total = res.result.total
        }
        if (res.code === 510) {
          this.$message.warning(res.message)
        }
        this.loading = false
      })
    },
    onChangeTime(date, dateString) {
      this.queryParam.lastTestTime_begin = dateString[0]
      this.queryParam.lastTestTime_end = dateString[1]
    },
    handleDetailPages(record) {
      this.$parent.pButton2(1, record)
    },
    handleDelete(record) {
      if (!this.url.delete) {
        this.$message.error('请设置url.delete属性!')
        return
      }
      var that = this
      this.$confirm({
        title: '确认删除',
        okText: '是',
        cancelText: '否',
        content: '是否删除选中数据?',
        onOk: function() {
          that.loading = true
          deleteAction(that.url.delete, {
            id: record.id
          }).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.loadData()
            } else {
              that.$message.warning(res.message)
            }
          }).catch((err) => {
            that.$message.warning(err.message)
          })
        }
      })
    },
    handleConfig(record) {
      this.$parent.pButton2(2, record)
    },
    handleExecution(record) {
      if (!record.stepConfig){
        this.$message.warning('步骤配置信息为空，请先进行配置')
        return
      }
      getAction(this.url.execution, { id: record.id,force:record.runLocked }).then((res) => {
        if (res.success) {
          this.$message.success(res.message)
          this.loadData()
        } else {
          this.$message.warning(res.message)
        }
      }).catch((err) => {
        this.$message.warning(err.message)
      })
    },
    handleCopy(record) {
      let { name, bizInfoId, cron, description, stepConfig } = record
      let newRecord = { name, bizInfoId, cron, description, stepConfig }
      this.$refs.modalForm.edit(newRecord)
      this.$refs.modalForm.title = '复制'
      this.$refs.modalForm.disableSubmit = false
    },
    handleUnlock(record) {
      putAction(this.url.unlock, { id: record.id }).then((res) => {
        if (res.success) {
          this.$message.success(res.message)
          this.loadData()
        } else {
          this.$message.warning(res.message)
        }
      }).catch((err) => {
        this.$message.warning(err.message)
      })
    },
    //任务禁用/启用
    taskEnable(record) {
     let that=this
      let enable = record.status
      let tip=enable === 1 ? "禁用" : "启用"
      that.$confirm({
        title: `确认${tip}`,
        okText: '是',
        cancelText: '否',
        content: `是否${tip}选中数据?`,
        onOk: function() {
          putAction(that.url.status, {
            status: enable === 1 ? 0 : 1,
            ids: record.id
          }).then((res) => {
            if (res.success) {
              if (enable === 1) {
                that.$message.success('禁用成功!')
              } else {
                that.$message.success('启用成功!')
              }
              that.loadData()
            } else {
              that.$message.warning(res.message)
            }
          }).catch((err) => {
            that.$message.warning(err.message)
          })
        }
      })
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';

.stateBox {
  margin-left: 20px;
}
.result{
  display: inline-block;
  width: 62px;
  padding: 2px 10px;
  border-radius:10px;
  color: #fff
}
.success{
  background:#52c41a;
}
.fail{
  background:#f5222d;
}
.none{
  background:rgba(0, 0, 0, 0.25);
}

.state-img {
  vertical-align: middle;
  font-size: 16px
}

.state-img-margin {
  margin-left: 3px;
}

.overlay {
  color: #409eff !important
}
</style>