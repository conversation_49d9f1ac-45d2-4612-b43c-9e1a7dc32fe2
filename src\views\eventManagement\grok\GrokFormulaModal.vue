<template>
  <j-modal
    :title='title'
    :centered='true'
    switchFullscreen
    :width='800'
    :visible='visible'
    :confirmLoading='confirmLoading'
    @ok='handleOk'
    @cancel='handleCancel'
    cancelText='关闭'
  >
    <a-spin :spinning='confirmLoading'>
      <a-form :form='form'>
        <a-form-item
          :labelCol='labelCol'
          :wrapperCol='wrapperCol'
          label='表达式'>
          <a-textarea placeholder="请输入表达式"
                      v-decorator.trim="['grokFormula', validatorRules.grokFormula]"
                      :allowClear="true"
                      autosize
                      autocomplete="off" />
        </a-form-item>

        <a-form-item class='two-words'
                     :labelCol='labelCol'
                     :wrapperCol='wrapperCol'
                     label='描述'>
          <a-input v-decorator="['description', validatorRules.description]"
                   :allowClear='true'
                   autocomplete='off' />
        </a-form-item>

      </a-form>
    </a-spin>
  </j-modal>
</template>

<script>
import pick from 'lodash.pick'
import { postAction, putAction } from '@api/manage'

export default {
  name: 'GrokFormulaModal',
  data() {
    return {
      title: '操作',
      visible: false,
      model: {},
      status: 1,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      confirmLoading: false,
      form: this.$form.createForm(this),
      validatorRules: {
        grokFormula: {
          rules: [{ required: true, message: '请输入表达式!' }, {
            max: 500,
            message: '表达式不可超过500个字符'
          }]
        },
        description: {
          rules: [{
            max: 200,
            message: '描述不可超过200个字符'
          }]
        }
      },
      url: {
        add: '/grok/addUtlGrokFormula',
        edit: '/grok/editUtlGrokFormula'
      }
    }
  },
  created() {
  },
  methods: {
    add(grokId, grokCode) {
      this.edit({ grokId, grokCode })
    },
    edit(record) {
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.model.grokId = record.grokId
      this.model.grokCode = record.grokCode
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(pick(this.model, 'grokFormula', 'description'))
      })
    },
    // 确定
    handleOk() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          values.grokFormula = (values.grokFormula || '').trim()
          values.description = (values.description || '').trim()
          let formData = Object.assign(this.model, values)
          formData.status = this.status
          let res;
          if (!this.model.id) {
            res = postAction(this.url.add, formData)
          } else {
            res = putAction(this.url.edit, formData)
          }
          res.then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.$emit('ok')
            } else {
              that.$message.warning(res.message)
            }
          }).finally(() => {
            that.confirmLoading = false
            that.close()
          })
        }
      })
    },
    // 关闭
    handleCancel() {
      this.close()
    },
    close() {
      this.$emit('close')
      this.visible = false
    }
  }
}
</script>
<style lang='less' scoped>
::v-deep .two-words > div > label {
  letter-spacing: 4px;
}

::v-deep .two-words > div > label::after {
  letter-spacing: 0px;
}

::v-deep .ant-modal-body {
  padding: 24px 48px 24px 48px;
}

::v-deep .ant-modal {
  padding: 24px;
}

@media (max-width: 812px) {
  ::v-deep .ant-modal {
    max-width: calc(100vw - 12px);
    margin: 0;
  }
}

.j-modal-box.fullscreen {
  margin: 0 !important;
  max-width: 100vw !important;

  ::v-deep .ant-modal {
    max-width: 100vw !important;
    margin: 0;
  }
}
</style>
