<template>
  <div id='zrNetworkView' ref='zrNetwork'>
    <div class='zr-network-topo'>
      <div class='zr-network-topo-l' style='width: calc(100% - 24%)'>
        <div class='top-info'></div>
        <div id='topoBox'>
          <vis-edit ref='visEdit' operate='show' scene="ywts_bigscreen" topoBgByTheme label-color='#fff'></vis-edit>
        </div>
        <div class='status-legend'>
          <zr-focus-info scene='network' :infoText='infoStr'></zr-focus-info>
          <div class='legend-block-list'>
            <div class='legend-block-list-item' v-for='item in unitLevels' :key='"v_"+item.value'>
              <div v-if='item.value===1' class='legend-block-list-item-icon level-circle'></div>
              <div v-if='item.value===2' class='legend-block-list-item-icon level-rect'></div>
              <div v-if='item.value===3' class='legend-block-list-item-icon level-diamond'></div>
              <div class='level-legend-list-item-text'>{{ item.label }}</div>
            </div>
            <div class='legend-block-list-item' v-for='item in businessStatus' :key='item.value'>
              <div class='legend-block-list-item-icon' :style='{backgroundColor:item.color}'></div>
              <div class='legend-block-list-item-text'>{{ item.label }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class='zr-network-topo-r' style='width: 24%'>
        <zr-network-nodes :nodes='renderNodes'></zr-network-nodes>
      </div>
    </div>
    <!-- 提示弹窗组件 -->
    <zr-tool-tip
      :left='tipLeft'
      :top='tipTop'
      :width='tipWidth'
      :height='tipHeight'
      :tipData='tipData'
      :visible='tipShow'
      :tipZindex='tipZindex'
      :status='businessStatus'
      @hide='toolTipHide'
      @cellClick='cellClick'
      @enterTipCard='enterTipCard'
      @leaveTipCard='leaveTipCard'
    ></zr-tool-tip>
  </div>
</template>
<script>
import { Graph, Model } from '@antv/x6'
import Hierarchy from '@antv/hierarchy'
import { organizations, departtLinks } from '../modules/zrOrganizations'
import { businessStatus, unitLevels } from '../modules/zrUtil'
import ZrToolTip from '@views/zrBigscreens/zrNetwork/modules/ZrToolTip.vue'
import ZrNetworkNodes from '@views/zrBigscreens/zrNetwork/modules/ZrNetworkNodes.vue'
import ZrTopoEventMixin from '@views/zrBigscreens/zrNetwork/modules/ZrTopoEventMixin'
import resizeObserverMixin from '@views/statsCenter/com/resizeObserverMixin'
import { flatTreeData, darkenColor } from '@/utils/util'
import ZrFocusInfo from '@views/zrBigscreens/zrCompNational/modules/ZrFocusInfo.vue'
import VisEdit from '@views/topo/nettopo/modules/VisEdit.vue'
import { getAction,postAction } from '@/api/manage'

export default {
  name: 'zrNetworkIndex',
  components: {
    ZrToolTip,
    ZrFocusInfo,
    ZrNetworkNodes,
    VisEdit
  },
  mixins: [ZrTopoEventMixin, resizeObserverMixin],
  data() {
    return {
      originResource: {},
      graph: null,
      businessStatus: JSON.parse(JSON.stringify(businessStatus)), // 深拷贝，防止修改原数据
      animates: {},
      unitLevels,
      rootNodes: [],
      circleIds: [],
      rectIds: [],
      departtLinks: [],
      renderOk: false, // 渲染状态
      renderNodes: [],
      topoLevel: 1,
      organizations,
      infoStr:"",
      resizeTimer:null,
    }
  },
  created() {
    this.originResource = flatTreeData([organizations], null, [])
    this.businessStatus.map(el => {
      el.dcolor = darkenColor(el.color, 50) // 生成比状态颜色较暗的颜色
      return el
    })
  },
  mounted() {
    this.getDeptTopo()
    this.getDeptStatus()
    this.getAlarmInfo()
  },
  beforeDestroy() {

  },
  methods: {
    //获取预警信息
    getAlarmInfo() {
      postAction("/monitor/situation/getAlarmInfoForDev",[]).then(res=>{
        if(res.success && res.result && res.result.records){
          this.infoStr = res.result.records[0]?.templateName || ""
          // console.log("获取告警信息 == ",this.infoStr)
        }
      })
    },
    //获取节点运行状态
    getDeptStatus() {
      getAction('/monitor/situation/getNodeStatusAndDevList', {
        deptAndDevBindDictCode: 'ZR_BIRSCREEN_DEPTS',
        withNodeDev: 'true',
        withRunTime: 'true'
      }).then(res => {
        if (res.success) {
          this.renderNodes = res.result.map(el => {
            let status = 0
            let day = '--'
            if (el.nodeDevObject) {
              if (el.nodeDevObject.status == 0) {
                status = 2
              } else if (el.nodeDevObject.alarmStatus !== 0) {
                status = 1
              } else {
                status = 0
              }
              if(el.nodeDevObject.sysUpTime){
                let temArr = el.nodeDevObject.sysUpTime.split('天')
                day = temArr.length>1?temArr[0]:"0"
              }
              // console.log("el.nodeDevObject",el.nodeDevObject)
            }
            return {
              name: el.code || el.departName,
              status: status,
              day: day
            }
          })
        }
        // console.log('获取节点运行状态成功', res)
      })
    },
    // 获取单位关系拓扑图
    getDeptTopo() {
      getAction('/topo/topoInfo/list', { topoType: '2' }).then(res => {
        if (res.success) {
          let topoInfo = res.result.pageList.records[0]
          if (topoInfo) {
            this.$refs.visEdit.createTopo(topoInfo.id)
          }
        }
        // console.log('获取部门拓扑图成功', res)
      })
    },
    // 监听窗口大小变化
    resizeObserverCb() {
      // 监听窗口大小变化，重新设置拓扑图大小
      if (this.$refs.visEdit) {
        if(this.resizeTimer){
          clearTimeout(this.resizeTimer)
          this.resizeTimer = null;
        }
        this.$nextTick(()=>{
          this.resizeTimer = setTimeout(()=>{
            this.$refs.visEdit.resizeTopo()
            clearTimeout(this.resizeTimer)
            this.resizeTimer = null;
          },100)
        })
      }
    },
  }
}
</script>

<style scoped lang='less'>
#zrNetworkView {
  padding: 0px 16px 0px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;

  .zr-network-topo {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: space-between;

    .zr-network-topo-l {
      position: relative;
      height: 100%;
      width: 75%;

      .top-info {
        height: 8px;
      }

      #topoBox {
        width: 100%;
        height: calc(100% - 50px - 8px);
        background: rgba(77, 182, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.1);
      }
    }

    .zr-network-topo-r {
      width: 25%;
      padding: 0 12px 0px
    }
  }
}

.status-legend {
  position: absolute;
  bottom: 0px;
  //right: 33px;
  width: 100%;
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  //width:100%;
  .legend-block-list {
    display: flex;

    .legend-block-list-item {
      display: flex;
      align-items: center;
      font-weight: 400;
      font-size: 14px;
      padding: 0 20px;
      color: #E3E7EF;

      .legend-block-list-item-icon {
        width: 14px;
        height: 14px;
        margin-right: 12px;
      }
    }

    .level-circle {
      border-radius: 50%;
      background-color: #ffffff;
    }

    .level-rect {
      width: 14px;
      height: 14px;
      background-color: #ffffff;
    }

    .level-diamond {
      width: 14px;
      height: 14px;
      transform: rotate(45deg);
      background-color: #ffffff;
    }
  }

}
</style>