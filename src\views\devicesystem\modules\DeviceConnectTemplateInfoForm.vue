<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-item class="two-words" label="标识" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                v-decorator="['code', validatorRules.code]"
                placeholder="请输入标识"
                :allowClear="true"
                autocomplete="off"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="参数名" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                v-decorator="['displayName', validatorRules.displayName]"
                placeholder="请输入参数名"
                :allowClear="true"
                autocomplete="off"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="控件类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-select
                :getPopupContainer="(node) => node.parentNode"
                v-decorator="['controlType', validatorRules.controlType]"
                placeholder="请选择控件类型"
                allowClear
                @change="controlTypeChoose($event)"
              >
                <a-select-option value="inputString">文本输入框</a-select-option>
                <a-select-option value="inputNumber">数字输入框</a-select-option>
                <a-select-option value="inputPassword">密码输入框</a-select-option>
                <a-select-option value="inputSelect">选择框</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="是否必填" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-radio-group v-decorator="['isRequired',{ initialValue: '1'}]">
                <a-radio value="1">是</a-radio>
                <a-radio value="0">否</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
          <a-col :span="24" v-if="controlType!=='inputSelect'">
            <a-form-item label="正则校验" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['validatorRule', validatorRules.validatorRule]" rows="4"
                       placeholder="请输入正则表达式" :allowClear='true' autocomplete="off" />
            </a-form-item>
          </a-col>
          <a-col :span="24" v-if="controlType!=='inputSelect'">
            <a-form-item label="默认值" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['defaultValue', validatorRules.defaultValue]" placeholder="请输入默认值"
                       :allowClear="true" autocomplete="off" />
            </a-form-item>
          </a-col>
          <a-col :span="24" v-if="controlType==='inputSelect'">
            <a-form-item label="数据字典" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-select
                :getPopupContainer="(node) => node.parentNode"
                v-decorator="['dictType', validatorRules.dictType]"
                optionFilterProp="children"
                placeholder="请选择数据字典"
                allowClear
                showSearch
                @change="dictChoose($event)"
              >
                <a-select-option v-for="item in this.dictList" :key="item.dictCode" :value="item.dictCode">
                  {{ item.dictName }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="24" v-if="productInfo.selectedProtocolList">
            <a-form-item label="传输协议" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-select
                :getPopupContainer="(node) => node.parentNode"
                v-decorator="['transferProtocolId', validatorRules.transferProtocol]"
                placeholder="请选择传输协议"
                :allow-clear="true"
                @change="changeTansferProtocol"
              >
                <a-select-option v-for="item in productInfo.selectedProtocolList" :label="item.transferProtocol"
                                 :value="item.transferProtocolId" :key="item.transferProtocolId">
                  {{ item.transferProtocol }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="持久化配置" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <div style="width:100%;height: 40px;display:flex;align-items:center;">
                <a-input
                  v-decorator="['persistentconf', validatorRules.persistentconf]"
                  placeholder="持久化配置-目标表名,目标字段名，匹配条件字段名；"
                  :allowClear="true"
                  autocomplete="off"
                  style="margin-right:5px;"
                />
                <a-popover title="连接参数持久化配置说明">
                  <template slot="content">
                    <div style="display: flex">
                      <div>
                            <span>
                              持久化配置规则：【目标表名,目标字段名,匹配字段名;】不同标识之间使用英文逗号间隔，多个不同配置之间使用英文分号间隔。
                            </span>
                        <br>
                        <span>
                              示例配置：momg_device_info,ip,device_code; 即当momg_device_info表中的device_code字段的值与当前设备的唯一标识的值相同时，
                            </span>
                        <br>
                        <span>
                              将当前连接参数的值赋值给momg_device_info表中的ip字段。
                            </span>
                      </div>
                    </div>
                  </template>
                  <a-icon
                    type="question-circle"
                    theme="twoTone"
                    style="font-size: 18px; line-height: 32px;margin-right:-24px;"
                  />
                </a-popover>
              </div>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item class="two-words" label="序号" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number v-decorator="['numIndex']" :min="1" :max="99999999999" style="width: 100%"
                              placeholder="请输入序号" />
            </a-form-item>
          </a-col>

          <!-- 暂时隐藏，TODO优化 -->
          <a-col :span="24" v-if="associationParameterList.length>0" style="display:none">
            <a-form-item label="关联参数" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-select
                :getPopupContainer="(node) => node.parentNode"
                v-model="parentKey"
                placeholder="请选择关联参数"
                allowClear
                @change="changeAssociationParameter"
              >
                <a-select-option v-for="item in associationParameterList" :key="item.code" :selected="item">
                  {{ item.displayName }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col v-if="showParentValue" :span="24" style="display:none">
            <a-form-item label="关联参数值" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-select
                :getPopupContainer="(node) => node.parentNode"
                v-model="parentValue"
                placeholder="请选择关联参数字值"
                allowClear
              >
                <a-select-option v-for="item in associationParameterDictList" :key="item.value">
                  {{ item.title }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <a-col :span="24">
            <a-form-item class="two-words" label="备注" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-textarea v-decorator="['remark', validatorRules.remark]" rows="4" placeholder="请输入备注"
                          :allowClear="true" />
            </a-form-item>
          </a-col>
          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
import { ValidateOptionalFields } from '@/utils/rules.js'
import { httpAction, getAction } from '@/api/manage'
import pick from 'lodash.pick'
import { initDictOptions } from '@/components/dict/JDictSelectUtil'

export default {
  name: 'DeviceConnectTemplateInfoForm',
  props: {
    // 流程表单data
    formData: {
      type: Object,
      default: () => {
      },
      required: false
    },
    // 表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false
    },
    // 表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    },
    productInfo: {}
  },
  data() {
    return {
      dictList: [],
      //protocolList: [],
      form: this.$form.createForm(this),
      controlType: '请选择控件类型',
      dictType: '',
      model: {},
      labelCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 5
        }
      },
      wrapperCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 16
        }
      },
      wrapperCol1: {
        xs: {
          span: 24
        },
        sm: {
          span: 18
        }
      },
      confirmLoading: false,
      validatorRules: {
        code: {
          rules: [
            {
              required: true,
              message: '请输入参数标识!'
            },
            {
              pattern: /^(([a-zA-Z]){1,64})$/,
              message: '英文字母组成，长度不能超出 64 个字符！'
            }
          ]
        },
        displayName: {
          rules: [
            { required: true, message: '请输入参数名!' },
            {
              pattern: /^(([a-zA-Z]+|[0-9]+|[a-zA-Z0-9-_@&]+|[\u4E00-\u9FA5]){2,64})$/,
              message: '参数名由字母、数字、中文字符或符号-、_、@、&组成，长度在 2-64 个字符之间'
            }
          ]
        },
        controlType: {
          rules: [
            { required: false, message: '请选择控件类型!' }
          ]
        },
        dictType: {
          rules: [
            { required: false, message: '请选择数据字典!' }
          ]
        },
        transferProtocol: {
          rules: [
            { required: true, message: '请选择传输协议!' }
          ]
        },
        persistentconf: {
          rules: [
            { required: false, validator: this.validatePersistentConf }
          ]
        },
        validatorRule: {
          rules: [
            {
              required: false,
              validator: (rule, value, callback) => ValidateOptionalFields(rule, value, callback, '正则校验', 255)
            }
          ]
        },
        defaultValue: {
          rules: [
            {
              required: false,
              validator: this.validateDefaultValue
            }
          ]
        },
        remark: {
          rules: [
            {
              required: false,
              validator: (rule, value, callback) => ValidateOptionalFields(rule, value, callback, '备注', 255)
            }
          ]
        }
      },
      url: {
        dictList: 'sys/dict/getAllDict',
        associationParameterList: '/product/deviceConnectTemplateInfo/list', // 获取关联参数列表
        add: '/product/deviceConnectTemplateInfo/add',
        edit: '/product/deviceConnectTemplateInfo/edit',
        queryById: '/product/deviceConnectTemplateInfo/queryById'
        //protocolList: '/product/product/queryBindTransferProtocol' //传输协议接口
      },
      transferProtocol: undefined,
      associationParameterList: [], // 关联参数列表
      associationParameterDictList: [], // 关联参数字典值列表
      associationParameterOption: {}, // 当前选中的关联参数
      parentKey: '', // 关联父级参数标识
      parentValue: '', // 关联值标识
      showParentValue: false // 是否显示关联值
    }
  },
  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    }
  },
  created() {
    this.showFlowData()
    this.getDict()
  },
  mounted() {

  },
  methods: {
    validatePersistentConf(rule, value, callback) {
      if (!value) {
        return callback()
      }
      const trimmedValue = value.trim()
      if (trimmedValue === '') {
        return callback('内容不可全为空白字符，请修改或清空')
      }
      const isValidFormat = /^([a-z][a-z_]*(?:,[a-z][a-z_]*){2})(?:(?:;[a-z][a-z_]*(?:,[a-z][a-z_]*){2})*;?)$/.test(value)
      if (!isValidFormat) {
        return callback('格式应为：表名,字段名,匹配字段名（仅含小写字母和下划线，且每项以字母开头）。请将鼠标移入后面蓝色问号看提示')
      }
      if (value.length < 5 || value.length > 100) {
        return callback('持久化配置长度应在 5-100 个字符之间！')
      }
      return callback()
    },

    validateDefaultValue(rule, value, callback) {
      if (!value) {
        return callback()
      }
      const trimmedValue = value.trim()
      if (trimmedValue === '') {
        return callback('内容不可全为空白字符，请修改或清空')
      } else if (value.length > 255) {
        return callback('默认值长度不能超出 255 个字符！')
      }

      let validatorRule = this.form.getFieldValue('validatorRule')
      if (validatorRule) {
        try {
          const regex = new RegExp(validatorRule)
          if (!regex.test(value)) {
            return callback('默认值不符合校验规则')
          }
        } catch (e) {
          return callback('校验规则格式不正确')
        }
      }
      return callback()
    },
    getDict() {
      getAction(this.url.dictList).then((res) => {
        if (res.success) {
          res.result.forEach((item) => {
            this.dictList.push(item)
          })
        }
      })
    },
    dictChoose(dictType) {
      this.dictType = dictType
    },
    add() {
      this.edit({})
    },
    edit(record) {
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true

      this.transferProtocol = record.transferProtocol ? this.model.transferProtocol.trim() : undefined
      this.controlType = record.controlType || 'inputString'
      // 回显关联参数和关联参数值
      this.parentKey = record.parentKey
      this.parentValue = record.parentValue
      // 回显下拉框的关联参数列表
      if (record.transferProtocolId) {
        this.getAssociationParameterList(record.transferProtocolId).then(() => {
          if (record.parentKey && this.associationParameterList.length > 0) {
            this.showParentValue = true
            // 设置选中的关联项
            this.associationParameterOption = this.associationParameterList.filter((items) => {
              return items.code == record.parentKey
            })
            // 获取关联参数字典值列表
            this.getDictList(this.associationParameterOption[0].dictType)
          }
        })
      }


      this.$nextTick(() => {
        this.form.setFieldsValue(
          pick(
            this.model,
            'code',
            'displayName',
            'defaultValue',
            'isRequired',
            'validatorRule',
            'controlType',
            'dictType',
            'transferProtocolId',
            'persistentconf',
            'numIndex',
            'remark'
          )
        )
      })
    },
    // 渲染流程表单数据
    showFlowData() {
      if (this.formBpm === true) {
        let params = {
          id: this.formData.dataId
        }
        getAction(this.url.queryById, params).then((res) => {
          if (res.success) {
            this.edit(res.result)
          }
        })
      }
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let formData = Object.assign(this.model, values)
          formData.controlType = this.controlType
          formData.transferProtocol = this.transferProtocol
          formData.productId = this.productInfo.id
          formData.parentKey = this.parentKey || ''
          formData.parentValue = this.parentValue || ''

          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    popupCallback(row) {
      this.form.setFieldsValue(
        pick(
          row,
          'code',
          'displayName',
          'defaultValue',
          'isRequired',
          'validatorRule',
          'controlType',
          'dictType',
          'transferProtocolId',
          'persistentconf',
          'numIndex',
          'remark'
        )
      )
    },
    controlTypeChoose(controlType) {
      this.controlType = controlType
    },
    changeTansferProtocol(value, option) {
      this.transferProtocol = value ? option.componentOptions.children[0].text.trim() : undefined
      // 清除关联参数
      this.parentKey = ''
      this.parentValue = ''
      this.showParentValue = false
      this.associationParameterList = []
      this.associationParameterDictList = []
      // 根据选的传输协议,获取关联参数列表
      if (!!option) {
        this.getAssociationParameterList(value)
      }
    },
    // 选择关联参数
    changeAssociationParameter(value, option) {
      this.associationParameterDictList = []
      this.parentValue = ''

      if (!!option) {
        // 设置当前选中的关联项
        const selectedObj = option.data.attrs.selected
        if (selectedObj.code == this.model.code) {
          this.parentKey = ''

          return this.$message.warning('请选择其他关联参数！')
        }
        this.associationParameterOption = selectedObj

        if (selectedObj.dictType && selectedObj.controlType == 'inputSelect') {
          this.showParentValue = true
          // 获取当前关联参数的字典值
          this.getDictList(selectedObj.dictType)
        }
      }
    },
    // 获取关联参数的字典值列表
    getDictList(dictType) {
      if (!dictType) {
        return
      }
      return new Promise((resolve, reject) => {
        initDictOptions(dictType).then((res) => {
          if (res.success) {
            resolve({
              success: true,
              data: res.result,
              message: res.message
            })
            this.associationParameterDictList = res.result
          } else {
            reject({
              success: false,
              data: undefined,
              message: res.message
            })
          }
        }).catch(err => {
          reject({
            success: false,
            message: err.message
          })
        })
      })
    },
    // 获取当前关联参数的列表
    getAssociationParameterList(transferProtocolId) {
      return new Promise((resolve, reject) => {
        if (transferProtocolId) {
          getAction(this.url.associationParameterList, {
            productId: this.productInfo.id,
            pageSize: 100
          }).then((res) => {
            if (res.success) {
              this.associationParameterList = res.result.filter(item => {
                return item.controlType == 'inputSelect' && item.code != this.model.code && item.transferProtocolId == transferProtocolId && !item.parentKey
              })
              resolve({
                success: true,
                data: res.result,
                message: res.message
              })
            } else {
              reject({
                success: false,
                data: undefined,
                message: res.message
              })
            }
          }).catch(err => {
            reject({
              success: false,
              message: err.message
            })
          })
        } else {
          reject({
            success: false,
            message: '请求失败'
          })
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
::v-deep .two-words > div > label {
  letter-spacing: 4px;
}

::v-deep .two-words > div > label::after {
  letter-spacing: 0px;
}
</style>