<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    :centered="true"
    switchFullscreen
    @ok="handleOk"
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }"
    @cancel="handleCancel"
    cancelText="关闭"
  >
    <devops-order-info-handle-form
      ref="devopsOrderInfoHandleForm"
      @ok="submitCallback"
      :disabled="disableSubmit"
    ></devops-order-info-handle-form>
  </j-modal>
</template>

<script>
import DevopsOrderInfoHandleForm from './DevopsOrderInfoHandleForm'

export default {
  name: 'DevopsOrderInfoHandleModal',
  components: {
    DevopsOrderInfoHandleForm,
  },
  data() {
    return {
      title: '',
      width: 896,
      visible: false,
      disableSubmit: false,
    }
  },
  methods: {
    add() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.devopsOrderInfoHandleForm.add()
      })
    },
    edit(record) {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.devopsOrderInfoHandleForm.edit(record)
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      this.$refs.devopsOrderInfoHandleForm.submitForm()
    },
    submitCallback() {
      this.$emit('ok')
      this.visible = false
    },
    handleCancel() {
      this.close()
    },
  },
}
</script>
<style lang="less" scoped>
::v-deep .ant-modal-body {
  padding: 24px 48px 24px 48px;
}
::v-deep .ant-modal {
  padding: 24px;
}
@media (max-width: 1012px) {
  ::v-deep .ant-modal {
    max-width: calc(100vw - 12px);
    margin: 0;
  }
}
.j-modal-box.fullscreen {
  margin: 0 !important;
  max-width: 100vw !important;
  ::v-deep .ant-modal {
    max-width: 100vw !important;
    margin: 0;
  }
}
</style>