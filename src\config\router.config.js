import { UserLayout, TabLayout, RouteView, BlankLayout, PageView,ZrBigScreenLayout } from '@/components/layouts'
import store from '../store'
import OneClickHelpLayout from '@/views/oneClickHelp/layouts/Layout'
/**
 * 走菜单，走权限控制
 * @type {[null,null]}
 */
export const asyncRouterMap = [

  {
    path: '/',
    name: 'dashboard',
    component: TabLayout,
    meta: { title: '首页' },
    redirect: '/dashboard/analysis',
    children: [

      // // dashboard
      // {
      //   path: '/dashboard',
      //   name: 'dashboard',
      //   redirect: '/dashboard/workplace',
      //   component: RouteView,
      //   meta: { title: '仪表盘', icon: 'dashboard', permission: [ 'dashboard' ] },
      //   children: [
      //     {
      //       path: '/dashboard/analysis',
      //       name: 'Analysis',
      //       component: () => import('@/views/dashboard/Analysis'),
      //       meta: { title: '分析页', permission: [ 'dashboard' ] }
      //     },
      //     {
      //       path: '/dashboard/monitor',
      //       name: 'Monitor',
      //       hidden: true,
      //       component: () => import('@/views/dashboard/Monitor'),
      //       meta: { title: '监控页', permission: [ 'dashboard' ] }
      //     },
      //     {
      //       path: '/dashboard/workplace',
      //       name: 'Workplace',
      //       component: () => import('@/views/dashboard/Workplace'),
      //       meta: { title: '工作台', permission: [ 'dashboard' ] }
      //     }
      //   ]
      // },
      //
      // // forms
      // {
      //   path: '/form',
      //   redirect: '/form/basic-form',
      //   component: PageView,
      //   meta: { title: '表单页', icon: 'form', permission: [ 'form' ] },
      //   children: [
      //     {
      //       path: '/form/base-form',
      //       name: 'BaseForm',
      //       component: () => import('@/views/form/BasicForm'),
      //       meta: { title: '基础表单', permission: [ 'form' ] }
      //     },
      //     {
      //       path: '/form/step-form',
      //       name: 'StepForm',
      //       component: () => import('@/views/form/stepForm/StepForm'),
      //       meta: { title: '分步表单', permission: [ 'form' ] }
      //     },
      //     {
      //       path: '/form/advanced-form',
      //       name: 'AdvanceForm',
      //       component: () => import('@/views/form/advancedForm/AdvancedForm'),
      //       meta: { title: '高级表单', permission: [ 'form' ] }
      //     }
      //   ]
      // },
      //
      // // list
      // {
      //   path: '/list',
      //   name: 'list',
      //   component: PageView,
      //   redirect: '/list/query-list',
      //   meta: { title: '列表页', icon: 'table', permission: [ 'table' ] },
      //   children: [
      //     {
      //       path: '/list/query-list',
      //       name: 'QueryList',
      //       component: () => import('@/views/list/TableList'),
      //       meta: { title: '查询表格', permission: [ 'table' ] }
      //     },
      //     {
      //       path: '/list/edit-table',
      //       name: 'EditList',
      //       component: () => import('@/views/list/TableInnerEditList'),
      //       meta: { title: '内联编辑表格', permission: [ 'table' ] }
      //     },
      //     {
      //       path: '/list/user-list',
      //       name: 'UserList',
      //       component: () => import('@/views/list/UserList'),
      //       meta: { title: '用户列表', permission: [ 'table' ] }
      //     },
      //     {
      //       path: '/list/role-list',
      //       name: 'RoleList',
      //       component: () => import('@/views/list/RoleList'),
      //       meta: { title: '角色列表', permission: [ 'table' ] }
      //     },
      //     {
      //       path: '/list/permission-list',
      //       name: 'PermissionList',
      //       component: () => import('@/views/list/PermissionList'),
      //       meta: { title: '权限列表', permission: [ 'table' ] }
      //     },
      //     {
      //       path: '/list/basic-list',
      //       name: 'BasicList',
      //       component: () => import('@/views/list/StandardList'),
      //       meta: { title: '标准列表', permission: [ 'table' ] }
      //     },
      //     {
      //       path: '/list/card',
      //       name: 'CardList',
      //       component: () => import('@/views/list/CardList'),
      //       meta: { title: '卡片列表', permission: [ 'table' ] }
      //     },
      //     {
      //       path: '/list/search',
      //       name: 'SearchList',
      //       component: () => import('@/views/list/search/SearchLayout'),
      //       redirect: '/list/search/article',
      //       meta: { title: '搜索列表', permission: [ 'table' ] },
      //       children: [
      //         {
      //           path: '/list/search/article',
      //           name: 'SearchArticles',
      //           component: () => import('../views/list/TableList'),
      //           meta: { title: '搜索列表（文章）', permission: [ 'table' ] }
      //         },
      //         {
      //           path: '/list/search/project',
      //           name: 'SearchProjects',
      //           component: () => import('../views/list/TableList'),
      //           meta: { title: '搜索列表（项目）', permission: [ 'table' ] }
      //         },
      //         {
      //           path: '/list/search/application',
      //           name: 'SearchApplications',
      //           component: () => import('../views/list/TableList'),
      //           meta: { title: '搜索列表（应用）', permission: [ 'table' ] }
      //         },
      //       ]
      //     },
      //   ]
      // },
      //
      // // profile
      // {
      //   path: '/profile',
      //   name: 'profile',
      //   component: RouteView,
      //   redirect: '/profile/basic',
      //   meta: { title: '详情页', icon: 'profile', permission: [ 'profile' ] },
      //   children: [
      //     {
      //       path: '/profile/basic',
      //       name: 'ProfileBasic',
      //       component: () => import('@/views/profile/basic/Index'),
      //       meta: { title: '基础详情页', permission: [ 'profile' ] }
      //     },
      //     {
      //       path: '/profile/advanced',
      //       name: 'ProfileAdvanced',
      //       component: () => import('@/views/profile/advanced/Advanced'),
      //       meta: { title: '高级详情页', permission: [ 'profile' ] }
      //     }
      //   ]
      // },
      //
      // // result
      // {
      //   path: '/result',
      //   name: 'result',
      //   component: PageView,
      //   redirect: '/result/success',
      //   meta: { title: '结果页', icon: 'check-circle-o', permission: [ 'result' ] },
      //   children: [
      //     {
      //       path: '/result/success',
      //       name: 'ResultSuccess',
      //       component: () => import(/* webpackChunkName: "result" */ '@/views/result/Success'),
      //       meta: { title: '成功', hiddenHeaderContent: true, permission: [ 'result' ] }
      //     },
      //     {
      //       path: '/result/fail',
      //       name: 'ResultFail',
      //       component: () => import(/* webpackChunkName: "result" */ '@/views/result/Error'),
      //       meta: { title: '失败', hiddenHeaderContent: true, permission: [ 'result' ] }
      //     }
      //   ]
      // },
      //
      // // Exception
      // {
      //   path: '/exception',
      //   name: 'exception',
      //   component: RouteView,
      //   redirect: '/exception/403',
      //   meta: { title: '异常页', icon: 'warning', permission: [ 'exception' ] },
      //   children: [
      //     {
      //       path: '/exception/403',
      //       name: 'Exception403',
      //       component: () => import(/* webpackChunkName: "fail" */ '@/views/exception/403'),
      //       meta: { title: '403', permission: [ 'exception' ] }
      //     },
      //     {
      //       path: '/exception/404',
      //       name: 'Exception404',
      //       component: () => import(/* webpackChunkName: "fail" */ '@/views/exception/404'),
      //       meta: { title: '404', permission: [ 'exception' ] }
      //     },
      //     {
      //       path: '/exception/500',
      //       name: 'Exception500',
      //       component: () => import(/* webpackChunkName: "fail" */ '@/views/exception/500'),
      //       meta: { title: '500', permission: [ 'exception' ] }
      //     }
      //   ]
      // },
      //
      // // account
      // {
      //   path: '/account',
      //   component: RouteView,
      //   name: 'account',
      //   meta: { title: '个人页', icon: 'user', keepAlive: true, permission: [ 'user' ] },
      //   children: [
      //     {
      //       path: '/account/center',
      //       name: 'center',
      //       component: () => import('@/views/account/center/Index'),
      //       meta: { title: '个人中心', keepAlive: true, permission: [ 'user' ] }
      //     },
      //     {
      //       path: '/account/settings',
      //       name: 'settings',
      //       component: () => import('@/views/account/settings/Index'),
      //       meta: { title: '个人设置', hideHeader: true, keepAlive: true, permission: [ 'user' ]  },
      //       redirect: '/account/settings/base',
      //       alwaysShow: true,
      //       children: [
      //         {
      //           path: '/account/settings/base',
      //           name: 'BaseSettings',
      //           component: () => import('@/views/account/settings/BaseSetting'),
      //           meta: { title: '基本设置', hidden: true, keepAlive: true, permission: [ 'user' ]  }
      //         },
      //         {
      //           path: '/account/settings/security',
      //           name: 'SecuritySettings',
      //           component: () => import('@/views/account/settings/Security'),
      //           meta: { title: '安全设置', hidden: true, keepAlive: true, permission: [ 'user' ]  }
      //         },
      //         {
      //           path: '/account/settings/custom',
      //           name: 'CustomSettings',
      //           component: () => import('@/views/account/settings/Custom'),
      //           meta: { title: '个性化设置', hidden: true, keepAlive: true, permission: [ 'user' ]  }
      //         },
      //         {
      //           path: '/account/settings/binding',
      //           name: 'BindingSettings',
      //           component: () => import('@/views/account/settings/Binding'),
      //           meta: { title: '账户绑定', hidden: true, keepAlive: true, permission: [ 'user' ]  }
      //         },
      //         {
      //           path: '/account/settings/notification',
      //           name: 'NotificationSettings',
      //           component: () => import('@/views/account/settings/Notification'),
      //           meta: { title: '新消息通知', hidden: true, keepAlive: true, permission: [ 'user' ]  }
      //         },
      //       ]
      //     },
      //   ]
      // }
    ]
  },
  {
    path: '*', redirect: '/404', hidden: true
  }
]
/**
 * 基础路由
 * @type { *[] }
 */
export const constantRouterMap = [
  // 门户
  {
    path: '/gateway',
    component: BlankLayout,
    redirect: '/gateway/gateway',
    children: [
      {
        path: 'gateway',
        name: 'gateway',
        component: () => import('@/views/gateway/gateway')
      },
      // 中软三合一页面
      {
        path: 'threeInOne',
        name: 'threeInOne',
        component: () => import('@/views/gateway/threeInOne')
      }
    ]
  },
  {
    path: '/user',
    component: UserLayout,
    redirect: '/user/login',
    hidden: true,
    children: [
      {
        path: 'login',
        name: 'login',
        component: () => import(/* webpackChunkName: "user" */ '@/views/user/Login')
      },
      {
        path: 'register',
        name: 'register',
        component: () => import(/* webpackChunkName: "user" */ '@/views/user/register/Register')
      },
      {
        path: 'register-result',
        name: 'registerResult',
        component: () => import(/* webpackChunkName: "user" */ '@/views/user/register/RegisterResult')
      },
      {
        path: 'alteration',
        name: 'alteration',
        component: () => import(/* webpackChunkName: "user" */ '@/views/user/alteration/Alteration')
      },
    ]
  },
  {
    path: '/bigscreen/dataAnalysisIndex',
    name: 'bigscreen-dataAnalysisIndex',
    component: () => import(/* webpackChunkName: "user" */ '@/views/bigscreen/dataAnalysisIndex')
  },
  {
    path: '/bigscreen/ITResourceOverview',
    name: 'bigscreen-ITResourceOverview',
    component: () => import(/* webpackChunkName: "user" */ '@/views/bigscreen/ITResourceOverview/index')
  },
  {
    path: '/bigscreen/szIndex',
    name: 'bigscreen-szIndex',
    component: () => import(/* webpackChunkName: "user" */ '@/views/bigscreen/szIndex')
  },
  {
    path: '/zfnw/topo',
    component: BlankLayout,
    hidden: true,
    children: [{
      path: '/zfnw/topo',
      name: 'zfnwTopo',
      component: () => import('@/views/zfnw/topo'),
      meta: { title: '拓扑图' }
    }]
  },
  {
    path: '/zr/operationsView',
    component: ZrBigScreenLayout,
    hidden: true,
    children: [
      {
      path: '/operationsView/business',
      name: 'operationsViewBusiness',
      component: () => import('@views/zrBigscreens/zrBusiness/zrBusinessIndex.vue'),
      meta: { title: '应用系统视图' }
    },   {
      path: '/operationsView/comprehensive',
      name: 'operationsViewComprehensive',
      component: () => import('@views/zrBigscreens/zrComprehensive/zrComprehensiveIndex.vue'),
      meta: { title: '节点视图' }
    },{
      path: '/operationsView/compNational',
      name: 'operationsViewCompNational',
      component: () => import('@views/zrBigscreens/zrCompNational/zrCompNationalIndex.vue'),
      meta: { title: '综合视图' }
    },{
      path: '/operationsView/network',
      name: 'operationsViewNetwork',
      component: () => import('@views/zrBigscreens/zrNetwork/zrNetworkIndex.vue'),
      meta: { title: '网络视图' }
    },{
      path: '/operationsView/OpEval',
      name: 'operationsViewOpEval',
      component: () => import('@views/zrBigscreens/zrOperationalEvaluation/zrOpEvalIndex.vue'),
      meta: { title: '运行评估视图' }
    },
      //静态页面
      {
        path: '/static/operationsView/business',
        name: 'staticoperationsViewBusiness',
        component: () => import('@views/zrBigscreenStatic/zrBusiness/zrBusinessIndex.vue'),
        meta: { title: '应用系统视图' }
      },   {
        path: '/static/operationsView/comprehensive',
        name: 'staticoperationsViewComprehensive',
        component: () => import('@views/zrBigscreenStatic/zrComprehensive/zrComprehensiveIndex.vue'),
        meta: { title: '节点视图' }
      },{
        path: '/static/operationsView/compNational',
        name: 'staticoperationsViewCompNational',
        component: () => import('@views/zrBigscreenStatic/zrCompNational/zrCompNationalIndex.vue'),
        meta: { title: '综合视图' }
      },{
        path: '/static/operationsView/network',
        name: 'staticoperationsViewNetwork',
        component: () => import('@views/zrBigscreenStatic/zrNetwork/zrNetworkIndex.vue'),
        meta: { title: '网络视图' }
      },
      {
        path: '/static/operationsView/OpEval',
        name: 'staticoperationsViewOpEval',
        component: () => import('@views/zrBigscreenStatic/zrOperationalEvaluation/zrOpEvalIndex.vue'),
        meta: { title: '运行评估视图' }
      },
    ]
  },
  {
    path: '/knowledgeManagement/Sharing',
    component: BlankLayout,
    hidden: true,
    children: [{
      path: '/knowledgeManagement/Sharing',
      name: 'KnowledgeSharing',
      component: () => import('@views/opmg/knowledgeManagement/Sharing.vue'),
      meta: {
        title: '知识分享'
      }
    }]
  },
  {
    path: '/knowledgeManagement/knowledgeBase/knowledgePdf',
    component: BlankLayout,
    hidden: true,
    children: [{
      path: '/knowledgeManagement/knowledgeBase/knowledgePdf',
      name: ' knowledgePdf',
      component: () => import('@/views/opmg/knowledgeManagement/knowledgeBase/knowledgePdf'),
      meta: {
        title: '知识导出'
      }
    }]
  },
  {
    path: '/user/activation',
    component: BlankLayout,
    hidden: true,
    children: [{
      path: '/user/activation',
      name: 'activation',
      component: () => import('@/views/user/activation'),
      meta: { title: '用户激活' }
    }]
  },
  {
    path: '/isps/userAnnouncement',
    component: TabLayout,
    hidden: true,
    children: [{
      path: '/isps/userAnnouncement',
      name: 'userAnnouncement',
      component: () => import('@/views/system/UserAnnouncementList'),
      meta: { title: '我的消息' }
    }]
  },
  {
    path: '/account/center/Index',
    component: TabLayout,
    hidden: true,
    children: [{
      path: '/account/center/Index',
      name: 'account-center',
      component: () => import('@/views/account/center/Index'),
      meta: { title: '个人中心' }
    }]
  },
  {
    path: '/test',
    component: BlankLayout,
    redirect: '/test/home',
    children: [
      {
        path: 'home',
        name: 'TestHome',
        component: () => import('@/views/Home')
      }
    ]
  },
  {
    path: '/404',
    component: () => import(/* webpackChunkName: "fail" */ '@/views/exception/404')
  },
  {
    path: '/oneClickHelp/login',
    name: 'oneClickHelpLogin',
    component: () => import('@/views/oneClickHelp/login'),
    meta: { title: '运维助手登录' }
  },
  {
    path: '/oneClickHelp/register',
    name: 'oneClickHelpRegister',
    component: () => import('@/views/oneClickHelp/register'),
    meta: { title: '运维助手注册' }
  },
  // {
  //   path: '/oneClickHelp/userCenter',
  //   component: OneClickHelpLayout,
  //   hidden: true,
  //   children: [{
  //     path: '/oneClickHelp/userCenter',
  //     name: 'oneClickHelp-user-center',
  //     component: () => import('@/views/oneClickHelp/user/UserCenter'),
  //     meta: { title: '个人中心' }
  //   }]
  // },
]
export const oneClickHelpRouters = [
  {
    path: '/oneClickHelp/myQuestion',
    name: 'myQuestion',
    component: () => import('@/views/oneClickHelp/myQuestion'),
    meta: {
      title: '我的问题',
      oneClickHelp: true,
      icon: "file-unknown",
      internalOrExternal: false,
      keepAlive: false,
    },
  },
  {
    path: '/oneClickHelp/artificialServices',
    name: 'artificialServices',
    component: () => import('@/views/oneClickHelp/artificialServices'),
    meta: { 
      title: '人工服务',
     oneClickHelp: true, 
     icon: "meh",
     internalOrExternal: false,
     keepAlive: false,
    },
  },
  {
    path: '/oneClickHelp/driveManagement',
    name: 'driveManagement',
    component: () => import('@/views/oneClickHelp/driveManagement'),
    meta: { 
      title: '驱动管理',
      oneClickHelp: true,
      icon: "usb",
      internalOrExternal: false,
      keepAlive: false,
  },
  },
  {
    path: '/oneClickHelp/knowledgeSearch',
    name: 'knowledgeSearch',
    component: () => import('@views/opmg/knowledgeManagement/knowledgeSearch/knowledgeSearch.vue'),
    meta: { 
      title: '知识库', 
      oneClickHelp: true,
      icon: "file-search",
      internalOrExternal: false,
      keepAlive: false,
     },
  },
]
if (window.config.oneClickHelp.pageStyle === "multiple") {
  constantRouterMap.push({
    path: '/oneClickHelp/index',
    name: 'oneClickHelp',
    component: () => import('@/views/oneClickHelp/gateway'),
    meta: { title: '一键帮助' }
  })
  // constantRouterMap.splice(-1, 0, {
  //   path: '/oneClickHelp',
  //   name: 'oneClickHelp',
  //   component: (resolve) => require(['@/components/layouts/TabLayout'], resolve),
  //   meta: { title: '一键帮助' },
  //   redirect: '/oneClickHelp/index',
  //   children: oneClickHelpRouters,
  // })
  // store.commit("SET_ONE_CLICK_HELP_ROUTERS",oneClickHelpRouters)
} else {
  constantRouterMap.push({
    path: '/oneClickHelp',
    component: BlankLayout,
    hidden: true,
    children: [{
      path: '/oneClickHelp/index',
      name: 'oneClickHelp',
      component: () => import('@/views/oneClickHelp/index'),
      meta: { title: '一键帮助' }
    }]
  })
}

