<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <!-- 查询区域 -->
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span='spanValue'>
                <a-form-item label="类型名称">
                  <a-input :maxLength='maxLength' placeholder="请输入类型名称" v-model="queryParam.typeName" :allowClear='true' autocomplete='off' />
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label="类型标识">
                  <a-input :maxLength='maxLength' placeholder="请输入类型标识" v-model="queryParam.typeCode" :allowClear='true' autocomplete='off' />
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label="绑定项">
                  <a-input :maxLength='maxLength' placeholder="请输入绑定项" v-model="queryParam.childName" :allowClear='true' autocomplete='off' />
                </a-form-item>
              </a-col>
              <a-col :span='colBtnsSpan()'>
                <span class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button class="btn-search btn-search-style" type="primary" @click="searchQuery">查询</a-button>
                  <a-button class="btn-reset btn-reset-style" @click="searchReset">重置</a-button>
                  <a v-if="isVisible" class='btn-updown-style' @click="doToggleSearch">
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>

      <a-card :bordered="false" style="width: 100%; flex: auto">
        <!-- 操作按钮区域 -->
        <div class="table-operator table-operator-style">
          <a-button @click="handleAdd">新增</a-button>
          <a-dropdown v-if="selectedRowKeys.length > 0">
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item key="1" @click="batchDel">删除</a-menu-item>
            </a-menu>
            <a-button>批量操作
              <a-icon type="down" />
            </a-button>
          </a-dropdown>
        </div>

        <!-- table区域-begin -->
        <a-table ref="table" bordered :rowKey="(record)=>{return record.id}" :columns="columns" :dataSource="dataSource"
          :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}" :pagination="ipagination" :loading="loading"
          :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }" @change="handleTableChange">
          <!-- 字符串超长截取省略号显示-->
          <span slot="templateContent" slot-scope="text">
            <j-ellipsis :value="text" :length="25" />
          </span>
          <template slot="childrenList" slot-scope="text, record">
            {{ getName(record) }}
          </template>
          <template slot="isUsed" slot-scope="text">
            <span>{{text == 0 ? '是' : '否'}}</span>
          </template>
          <span slot="action" slot-scope="text, record" class="caozuo">
            <a @click="handleDetailPage(record)">查看</a>
            <a-divider type="vertical" />
            <a @click="handleEdit(record)">编辑</a>
            <a-divider type="vertical" />
            <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record)">
              <a>删除</a>
            </a-popconfirm>
          </span>
          <template slot="tooltip" slot-scope="text">
            <a-tooltip placement="topLeft" :title="text" trigger="hover">
              <div class='tooltip'>
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
      </a-card>
      <!-- table区域-end -->
      <detail-modal ref="modalForm" @ok="modalFormOk" :showFlag="showFlag"></detail-modal>
    </a-col>
  </a-row>
</template>

<script>
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import JEllipsis from '@/components/jeecg/JEllipsis'
  import {
    deleteAction,
    getAction,
    putAction
  } from '@/api/manage'
  import deviceClassificationModal from './classification/deviceClassificationModal.vue'
  import {
    YqFormSearchLocation
  } from '@/mixins/YqFormSearchLocation'

  export default {
    name: 'deviceClassificationList',
    mixins: [JeecgListMixin, YqFormSearchLocation],
    components: {
      JEllipsis,
      'detail-modal': deviceClassificationModal,
    },
    data() {
      return {
        maxLength:50,
        showFlag: true,
        formItemLayout: {
          labelCol: {
            style: 'width:80px',
          },
          wrapperCol: {
            style: 'width:calc(100% - 80px)'
          }
        },
        // 表头
        columns: [{
            title: '类型名称',
            dataIndex: 'typeName',
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 80px;max-width:200px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '类型标识',
            dataIndex: 'typeCode',
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 80px;max-width:200px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '绑定项',
            dataIndex: 'childrenList',
            scopedSlots: {
              customRender: 'childrenList'
            },
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 80px;max-width:200px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '是否使用',
            dataIndex: 'isUsed',
            scopedSlots: {
              customRender: 'isUsed'
            },
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 60px;max-width:80px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '描述',
            dataIndex: 'description',
            scopedSlots: {
              customRender: 'tooltip'
            },
            customCell: () => {
              let cellStyle = 'text-align: left;min-width: 200px;max-width:400px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '操作',
            align: 'center',
            width: 180,
            fixed: 'right',
            dataIndex: 'action',
            scopedSlots: {
              customRender: 'action'
            },
          },
        ],
        url: {
          list: '/product/categoryType/list', //分页展示接口
          delete: '/product/categoryType/deleteBatch',
          deleteBatch: '/product/categoryType/deleteBatch',
        },
      }
    },
    methods: {
      getName(info) {
        let name = ''
        info.childrenList.forEach((ele) => {
          name += ele.childName + ' '
        })
        return name
      },
      handleDelete: function (record) {
        if (record.isUsed == 0) {
          this.$message.error('已使用的分类不可被删除!')
          return
        }
        if (!this.url.delete) {
          this.$message.error('请设置url.delete属性!')
          return
        }
        var that = this
        deleteAction(that.url.delete, {
          ids: record.id
        }).then((res) => {
          if (res.success) {
            //重新计算分页问题
            this.reCalculatePage(1)
            that.$message.success(res.message)
            that.loadData()
          } else {
            that.$message.warning(res.message)
          }
        })
      },
    },
  }
</script>
<style lang='less' scoped>
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';
</style>