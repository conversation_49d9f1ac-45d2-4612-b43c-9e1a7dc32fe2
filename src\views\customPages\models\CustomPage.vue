<template>
  <div>
    <custom-designer-view v-if='desiner' :row-list='rowList'></custom-designer-view>
    <custom-page-designer ref='designerModal' @reset='reset'></custom-page-designer>
    <div class='page-setting'>
      <a-button type="primary" shape="circle" icon="setting" style='margin-top:20px' @click='showDesigner'/>
    </div>

  </div>
</template>
<script>
import CustomDesignerView from '@views/customPages/models/CustomDesignerView.vue'
import CustomPageDesigner from '@views/customPages/models/CustomPageDesigner.vue'
import {getAction} from '@api/manage'

export default {
  name: 'CustomPage',
  components: { CustomPageDesigner, CustomDesignerView },
  props:{
    pageType:{
      type:String,
      default:'',
    }
  },
  data(){
      return{
        desiner:true,
        rowList:[],
        pageInfo:null,
        pageTempalte:null,
      }
  },
  created() {
    this.getUserPageInfo();
  },
  mounted() {
  },
  methods:{
    tryToJSON(val) {
      try {
        return JSON.parse(val)
      } catch (error) {
        return false
      }
    },
    //获取用户页面数据 如果没有用户页面信息获取默认的页面
    getUserPageInfo(){
      getAction("/user/pageInfo/queryUserPage",{pageType: "ywgzt"}).then(userRes=>{
        // console.log("获取用户运维工作台保存的数据",userRes)
        if(userRes.success){
          if(userRes.result){
            this.pageInfo = userRes.result
            this.pageTempalte = userRes.result.pageData
            if(this.pageTempalte && this.tryToJSON(this.pageTempalte)){
              this.rowList = JSON.parse(this.pageTempalte)
            }else{
              console.error("页面信息有误",this.pageTempalte)
            }
          }else{
            this.getDefaultPage()
          }
        }

      })

    },
    getDefaultPage(){
      getAction("/custom/template/getDefaultList").then(res=>{
        if(res.success && res.result && res.result[0]){
          this.pageInfo = res.result[0]
          this.pageTempalte = res.result[0].templateData
          if(this.pageTempalte && this.tryToJSON(this.pageTempalte)){
            this.rowList = JSON.parse(this.pageTempalte)
            this.desiner = true;
          }else{
            console.error("页面信息有误", this.pageTempalte)
          }
        }
        else{
          this.rowList = [
            {
              "id": "row-341e7690-6ed5-434a-aca9-339ba3b07930",
              "type": "row",
              "justify": "start",
              "gutter": 16,
              "gutterBottom": 16,
              "height": 430,
              "cols": [
                {
                  "id": "col-1aa06231-58e5-4861-9be0-dc25fb6c62b2",
                  "rowId": "row-341e7690-6ed5-434a-aca9-339ba3b07930",
                  "type": "col",
                  "xxl": 12,
                  "xl": 12,
                  "lg": 12,
                  "md": 12,
                  "sm": 24,
                  "xs": 24,
                  "offset": 0,
                  "order": 0,
                  "pull": 0,
                  "push": 0,
                  "children": [],
                  "componentPath": "MyTodo",
                  "componentName": "我的待办"
                },
                {
                  "id": "col-9d8f8f41-58cd-40b8-b243-e7edb11a1d43",
                  "rowId": "row-341e7690-6ed5-434a-aca9-339ba3b07930",
                  "type": "col",
                  "xxl": 12,
                  "xl": 12,
                  "lg": 12,
                  "md": 12,
                  "sm": 24,
                  "xs": 24,
                  "offset": 0,
                  "order": 0,
                  "pull": 0,
                  "push": 0,
                  "children": [],
                  "componentPath": "MyPendingReview",
                  "componentName": "我的待阅"
                }
              ]
            },
            {
              "id": "row-eafb0ded-3728-43eb-b6bd-0fd375af94be",
              "type": "row",
              "justify": "start",
              "gutter": 16,
              "gutterBottom": 16,
              "height": 430,
              "cols": [
                {
                  "id": "col-e26a79ea-c166-4f8c-9222-d93507cdf87d",
                  "rowId": "row-eafb0ded-3728-43eb-b6bd-0fd375af94be",
                  "type": "col",
                  "xxl": 12,
                  "xl": 12,
                  "lg": 12,
                  "md": 12,
                  "sm": 24,
                  "xs": 24,
                  "offset": 0,
                  "order": 0,
                  "pull": 0,
                  "push": 0,
                  "children": [],
                  "componentPath": "MyApplication",
                  "componentName": "我的申请"
                },
                {
                  "id": "col-59a883d5-8f0a-4238-bf62-15fc23815a21",
                  "rowId": "row-eafb0ded-3728-43eb-b6bd-0fd375af94be",
                  "type": "col",
                  "xxl": 12,
                  "xl": 12,
                  "lg": 12,
                  "md": 12,
                  "sm": 24,
                  "xs": 24,
                  "offset": 0,
                  "order": 0,
                  "pull": 0,
                  "push": 0,
                  "children": [],
                  "componentPath": "MyDraft",
                  "componentName": "我的草稿"
                }
              ]
            }

          ]
        }
      })
    },
    showDesigner(){
      // 此处后去用户保存的页面数据 如果没有是由默认模板的页面数据
      if(this.pageInfo){
        let rowList = []
        if(this.pageTempalte && this.tryToJSON(this.pageTempalte)){
          rowList = JSON.parse(this.pageTempalte)
        }else{
          console.error("页面信息有误",this.pageTempalte)
          return;
        }
        this.$refs.designerModal.editUserPage(rowList,this.pageInfo,"工作台")
      }
      else{
        this.$refs.designerModal.editUserPage(this.rowList,{},"工作台")
      }
      this.desiner = false;

    },
    reset(){
      this.rowList = [];
      this.getUserPageInfo();
      this.desiner = true;
    }
  }
}
</script>



<style scoped lang='less'>
.page-setting{
  position: fixed;
  right: 0px;
  top:50vh;
}
</style>