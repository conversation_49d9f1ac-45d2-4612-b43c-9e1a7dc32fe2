<template>
  <a-row style='height: 100%; display: flex; overflow-x: auto'>
    <a-col ref='print' id='printContent' class='abcdefg' :xl='12' :lg='12' :md='10' :sm='10' :xs='10'
           style='overflow-x: auto; min-width: 350px'>
      <div>
        <p style='
            padding: 16px 24px;
            background-color: #fafafa;
            border-bottom: 1px solid #e8e8e8;
            font-size: 16px;
            white-space: nowrap;
          '>
          SNMP连接测试
        </p>
      </div>
      <a-col style='padding: 0 24px 24px'>
      </a-col>
    </a-col>
    <a-col :span='12' class='contTwo' style='overflow-x: auto; min-width: 350px'>
      <!--        <a-descriptions layout="vertical" bordered  >-->
      <!--          <a-descriptions-item label="SNMP连接测试"  style="" class="returnDiv">-->
      <div class='returnDiv' v-if="this.methodValue != 'getPduTable'">
        <p class='returnTitle'>SNMP连接测试</p>
        <p v-html='result' style='padding: 5px 16px 0 24px'>{{ result }}</p>
      </div>
      <div v-else>
        <p class='returnTitle'>SNMP连接测试</p>
        <a-table :data-source='snmpData' :columns='columnList' bordered
                 :scroll="snmpData.length > 0 ? { x: 'max-content' } : {}">
          <template slot='name' slot-scope='text'>
            <span> {{ text }} </span>
          </template>
        </a-table>
      </div>
      <!--          </a-descriptions-item>-->
      <!--        </a-descriptions>-->
    </a-col>
  </a-row>
</template>
<script>
import { v4 as uuidv4 } from 'uuid'
import ACol from 'ant-design-vue/es/grid/Col'
import ARow from 'ant-design-vue/es/grid/Row'
import ATextarea from 'ant-design-vue/es/input/TextArea'
import {
  getAction, postAction
} from '@api/manage'
import pick from 'lodash.pick'

export default {
  components: {
    pick,
    ATextarea,
    ARow,
    ACol
  },
  name: 'Printgzsld',
  props: {
    reBizCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      columnList: [],
      labelCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 8
        },
        md: {
          span: 7
        },
        lg: {
          span: 6
        },
        xl: {
          span: 5
        }
      },
      wrapperCol: {
        xs: {
          span: 23
        },
        sm: {
          span: 16
        },
        md: {
          span: 17
        },
        lg: {
          span: 18
        },
        xl: {
          span: 18
        }
      },
      confirmLoading: false,
      url: {
        createRedisConnection: '/redis/client/redisConnect'
      },
      result: ''
    }
  },
  created() {
  },
  mounted() {
    //this.createConnection()
  },
  methods: {
    createConnection() {
      console.log(uuidv4())
      postAction(this.url.createRedisConnection, param).then(
        (res) => {
          if (res.success) {

          }
        }
      )
    }
  }
}
</script>
<style lang='scss' scoped>
/*update_begin author:scott date:20191203 for:打印机打印的字体模糊问题 */
* {
  color: #000000;
  -webkit-tap-highlight-color: #000000;
}

/*update_end author:scott date:20191203 for:打印机打印的字体模糊问题 */
.sn-box {
  border: 1px solid #e8e8e8;
  border-radius: 3px;
  height: 1200px;
}

.sn-title {
  background-color: #f5f5f5;
  padding: 16px;
  border-bottom: 1px solid #e8e8e8;
}

.importDiv {
  width: 60%;
  height: 24em;
  margin: 0 auto;
  border: 1px solid #d9d9d9;
  padding: 18px;
}

.returnDiv {
  height: 100%;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
}

.returnTitle {
  padding: 16px 24px;
  background-color: rgb(250, 250, 250);
  border-bottom: 1px solid rgb(232, 232, 232);
  font-size: 16px;
}

.leftSpan {
  width: 14%;
}

.abcdefg .ant-card-body {
  margin-left: 0%;
  margin-right: 0%;
  margin-bottom: 1%;
  border: 0px solid black;
  min-width: 800px;
  color: #000000 !important;
}

.explain {
  text-align: left;
  margin-left: 50px;
  color: #000000 !important;
}

.explain .ant-input,
.sign .ant-input {
  font-weight: bolder;
}

.btnStyle {
  //margin-bottom: 24px;
}

.explain div {
  margin-bottom: 10px;
}

.ant-upload-select-picture-card i {
  font-size: 32px;
  color: #999;
}

.ant-upload-select-picture-card .ant-upload-text {
  margin-top: 8px;
  color: #666;
}

.aInp {
  //width: 91%;
}

#printContent {
  height: 100%;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
}

.contTwo {
  margin-left: 12px;
  width: calc(100% - 50% - 12px);
  height: 100%;
}
</style>
<style scoped>
.aCol {
  /* height: 45px; */
  margin-bottom: 5px;
}

.aCol:first-child {
  margin-top: 10px;
}

@media (min-width: 1650px) and (max-width: 1850px) {
  .aCol {
    height: 50px;
    margin-bottom: 8px;
  }
}

@media screen and (max-width: 1649px) {
  .aCol {
    /* height: 27px; */
    margin-bottom: 8px;
  }
}
</style>