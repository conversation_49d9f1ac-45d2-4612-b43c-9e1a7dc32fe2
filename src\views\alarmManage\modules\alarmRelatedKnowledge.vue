<template>
  <div>
    <div class="HotKnowledge" v-if="dataSource.length>0">
      <div class="title-box">
        <span class="title">知识推荐</span>
      </div>
      <div class="wrapper">
        <div
          v-for="(kitem, kindex) in dataSource"
          :key="'kl_' + kindex"
          class="item"
          @click="goKnowledgePage(kitem)"
        >
          <div class="name" v-html="kitem.title"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getAction } from '@/api/manage'
export default {
  description: '与告警相关的知识',
  name: 'alarmRelatedKnowledge',
  props: {
    alarmInfo: {
      type: Object
    }
  },
  data() {
    return {
      dataSource: [],
      url: {
        list: '/kbase/knowledges/search'
      }
    }
  },
  watch: {
    alarmInfo: {
      handler(val) {
        let keword = ''
        // 以告警规则、告警触发值为关键词搜索相关的知识
        if (val.alarmRule) {
          keword = keword + val.alarmRule
        }
        if (val.alarmValue) {
          if (keword.trim()) {
            keword = keword + ' ' + val.alarmValue
          } else {
            keword = val.alarmValue
          }
        }
        if (keword && keword.trim()) {
          this.loadData(keword)
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    // 跳转知识详情
    goKnowledgePage: function (record) {
      let param = {
        data: record
      }
      this.$emit('OpenPreview', param)
    },
    loadData(keyword) {
      // 知识搜索
      var params = {
        pageSize: 5,
        pageNo: 1,
        keyword: keyword,
        isFromAlarm: true
      }
      getAction(this.url.list, params).then(res => {
        if (res.success && res.result) {
          this.dataSource = res.result.records || res.result
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.title-box {
  font-size: 14px;
  margin-bottom: 10px;

  .title {
    padding-left: 7px;
    border-left: 4px solid #1e3674;
  }
}
.HotKnowledge {
  width: 100%;

  .wrapper {
    width: 100%;
    margin-bottom: 20px;
    .item {
      color: #2440b3;
      display: inline-block;
      margin-left: 8px;
      margin-top: 9px !important;
      padding: 5px 13px 5px 13px;
      background: #f5f5f5;
      border-radius: 6px;
      cursor: pointer;

      .name {
        overflow: hidden;
        display: inline;
      }

      &:hover {
        background-color: rgba(49, 94, 251, 0.1);
        color: #315efb;
      }

      &:visited {
        color: #771caa;
      }
    }
  }
}
</style>