<template>
  <a-row :gutter='10' style='height: 100%;margin-bottom: 16px' class='vScroll zxw'>
    <a-col>
      <a-card
        :bordered="false"
        style="height: 100%"
        :bodyStyle="{ paddingBottom: '25px' }"
      >
        <div style="height: 100%" class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="onSearchProcess(searchProcessKey)">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="流程名称">
                  <a-input
                    v-model="searchProcessKey"
                    :allowClear="true"
                    autocomplete="off"
                    placeholder="请输入"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="colBtnsSpan()">
            <span
              class="table-page-search-submitButtons"
              :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
            >
              <a-button @click="onSearchProcess(searchProcessKey)" type="primary">查询</a-button>
              <a-button @click="handleToApplyList" style="margin-left: 15px">前往我的申请列表</a-button>
              <a-button @click="onSearchProcess('')" style="margin-left: 15px">重置</a-button>
            </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
        <a-empty description="无流程可供选择" v-if="activeKeyAll.length == 0" />
        <div v-else>
          <a-collapse v-model="activeKey">
            <a-collapse-panel
              v-for="(value, index) in activeKeyAll"
              :header="filterDictText(dictOptions, value) || '未分类'"
              :key="value"
            >
              <a-list :grid="{ xs: 1, sm: 1, md: 2, lg: 2, xl: 4 }" :dataSource="processDataMap[value]">
                <a-list-item slot="renderItem" slot-scope="item">
                  <a-card>
                    <div slot="title">
                      <a-row :gutter="24">
                        <a-col span="12" :title="item.name">{{ item.name }} </a-col>
                        <a-col span="12" style="text-align: right">
                          <a href="javascript:void (0)" style="color: #409eff" @click="chooseProcess(item)">发起申请</a>
                        </a-col>
                      </a-row>
                    </div>
                    <b>版本：</b>v.{{ item.version }}
                    <br />
                    <b>说明：</b>{{ item.description }}
                  </a-card>
                </a-list-item>
              </a-list>
            </a-collapse-panel>
          </a-collapse>
        </div>
        <!--流程表单-->
        <j-modal :title="lcModa.title" v-model="lcModa.visible" :footer="null" :maskClosable="false" width="80%">
          <component
            :disabled="lcModa.disabled"
            v-if="lcModa.visible"
            :is="lcModa.formComponent"
            :processData="lcModa.processData"
            :isNew="lcModa.isNew"
            @afterSubmit="afterSub"
            @close=";(lcModa.visible = false), (lcModa.disabled = false)"
          ></component>
        </j-modal>

        <start-process ref="startCode"></start-process>
      </a-card>
    </a-col>
  </a-row>

  <!---->
</template>

<script>
import { activitiMixin } from '@/views/activiti/mixins/activitiMixin'
import JEllipsis from '@/components/jeecg/JEllipsis'
import JTreeSelect from '@/components/jeecg/JTreeSelect'
import { initDictOptions, filterDictText } from '@/components/dict/JDictSelectUtil'
import historicDetail from '@/views/activiti/historicDetail'
import { getAction, deleteAction, putAction, postAction } from '@/api/manage'
import StartProcess from '../../components/activiti/StartProcess'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'

export default {
  name: 'applyHome',
  mixins: [activitiMixin, YqFormSearchLocation],
  components: {
    JEllipsis,
    JTreeSelect,
    historicDetail,
    StartProcess,
  },
  data() {
    return {
      description: '所有',
      dictOptions: [],
      url: {
        getProcessDataList: '/activiti_process/listData',
        getFirstNode: '/actProcessIns/getFirstNode',
        applyBusiness: '/actBusiness/apply',
      },
      // 查询条件
      queryParam: {
        createTimeRange: [],
        keyWord: '',
      },
      // 表头
      labelCol: {
        xs: { span: 4 },
        sm: { span: 4 },
      },
      wrapperCol: {
        xs: { span: 20 },
        sm: { span: 20 },
      },
      processModalVisible: null,
      activeKeyAll: [],
      activeKey: [],
      processDataMap: {},
      searchProcessKey: null,
      addApplyLoading: false,
      lcModa: {
        title: '',
        disabled: false,
        visible: false,
        formComponent: null,
        isNew: false,
      },
      dialogStartInstanceVisible: false,
      processDefinition: undefined,
    }
  },
  computed: {},
  mounted() {
    this.initDictConfig()
    this.getProcessList()
  },
  methods: {
    initDictConfig() {
      this.dictOptions = []
      //初始化字典 - 流程分类
      initDictOptions('bpm_process_type').then((res) => {
        if (res.success) {
          res.result.forEach((e) => {
            if (e.value == 'sj' || e.value == 'wtgl' || e.value == 'bg' || e.value == 'fb' || e.value == 'pzgl') {
              return
            }
            this.dictOptions.push(e)
          })
        }
      })
    },
    filterDictText(dictOptions, text) {
      if (dictOptions instanceof Array) {
        for (let dictItem of dictOptions) {
          if (text === dictItem.value) {
            return dictItem.text
          }
        }
      }
      return text || text == 'null' ? '' : text
    },
    /*加载流程列表*/
    getProcessList() {
      this.addApplyLoading = true
      getAction(this.url.getProcessDataList, { status: 1, roles: true })
        .then((res) => {
          this.activeKeyAll = []
          if (res.success) {
            var result = res.result || []
            if (result.length > 0) {
              let searchProcessKey = this.searchProcessKey
              if (searchProcessKey) {
                //过滤条件
                result = _.filter(result, function (o) {
                  return o.name.indexOf(searchProcessKey) > -1
                })
              }
              this.processDataMap = _.groupBy(result, 'categoryId')
              for (const categoryId in this.processDataMap) {
                this.activeKeyAll.push(categoryId)
              }
              this.activeKey = this.activeKeyAll
            }
            this.processModalVisible = true
          } else {
            this.$message.warning(res.message)
          }
        })
        .finally(() => (this.addApplyLoading = false))
    },
    onSearchProcess(value) {
      this.searchProcessKey = value
      this.getProcessList()
    },
    chooseProcess(v) {
      if (!v.formKey) {
        this.$message.warning('该流程信息未配置表单，请联系开发人员！')
        return
      }
      // this.lcModa.formComponent = this.getFormComponent(v.routeName).component;
      // this.lcModa.title = '发起流程：'+v.name;
      // this.lcModa.isNew = true;
      // this.lcModa.processData = v;
      // this.lcModa.visible = true;

      this.$refs.startCode.initData(v)
    },
    /*提交成功申请后*/
    afterSub(formData) {
      this.lcModa.visible = false
      this.$message('请前往我的申请列表提交审批！')
    },
    /*前往我的申请页面*/
    handleToApplyList() {
      this.$router.push({ path: '/activiti/applyList' })
    },
  },
}
</script>
<style lang="less" scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
::v-deep .ant-modal-body {
  padding: 24px 48px 24ox 48px;
}
::v-deep .ant-modal {
  padding: 24px;
}
@media (max-width: 1012px) {
  ::v-deep .ant-modal {
    top: 0px;
    width: 1000px !important;
    max-width: 1000px !important;
    margin: 0 !important;
  }

  .ant-modal {
    top: 0px;
    width: 1000px !important;
    max-width: 1000px !important;
    margin: 0 !important;
  }
}
</style>