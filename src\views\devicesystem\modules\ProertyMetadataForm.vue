<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-item label="属性标识" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['code', validatorRules.code]" @change="codeChange" placeholder="请输入属性标识"
                :allowClear="true" autocomplete="off"/>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="属性名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['name', validatorRules.name]" @change="nameChange" placeholder="请输入属性名称" :allowClear="true" autocomplete="off"/>
            </a-form-item>
          </a-col>

          <a-col :span="24">
            <a-form-item label="属性索引" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['indexes',validatorRules.indexes]" placeholder="请输入属性索引" :allowClear="true"
                autocomplete="off"/>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="属性单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['unit',validatorRules.unit]" placeholder="请输入属性单位" :allowClear="true"
                autocomplete="off"/>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="属性进制" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number style="width: 150px" :min="0" :max="99999999999"  :step="1" :precision="10" v-decorator="['originStep']" placeholder="请输入属性进制" :allowClear="true"
                autocomplete="off"/>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="序号" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number style="width: 150px" :min="1" :max="99999999999" :step="1"  :precision="0" v-decorator="['serial', validatorRules.serial]" placeholder="请输入序号" :allowClear="true"
                autocomplete="off"/>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <!-- 现阶段此处需要顶级指标与子级指标相匹配，子级存储需要顶级也存储才行 -->
            <a-form-item label="是否持久化存储" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-radio-group v-model="isSave" @change="isPersistenceFlagChange($event)">
                <a-radio :style="radioStyle" value="1"> 是 </a-radio>
                <a-radio :style="radioStyle" value="0"> 否 </a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="持久化存储表名" :labelCol="labelCol" :wrapperCol="wrapperCol" v-show="isSave == '1'">
              <a-input :allowClear="true" autocomplete="off" v-decorator="['tableName',validatorRules.tableName]"
                placeholder="请输入持久化存储表名">
              </a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="持久化存储Tag列" :labelCol="labelCol" :wrapperCol="wrapperCol" v-show="isSave == '1'">
              <a-input :allowClear="true" autocomplete="off" v-decorator="['tagColumn',validatorRules.tagColumn]"
                placeholder="请输入持久化存储Tag列">
              </a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="传输协议" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-select :getPopupContainer="(node) => node.parentNode" allowClear
                v-decorator="['transferProtocolId', validatorRules.transferProtocol]" placeholder="请选择传输协议"
                @change="changeProtocol">
                <a-select-option v-for="item in productInfo.selectedProtocolList" :key="item.transferProtocolId">
                  {{ item.transferProtocol }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="数据类型" :labelCol="labelCol" :wrapperCol="wrapperCol" :required="true" :help="dataHelp"
              :validate-status="dataStatus">
              <a-select :getPopupContainer="(node) => node.parentNode" placeholder="请选择数据类型" allowClear
                @change="dataTypeChoose" v-model="dataType">
                <a-select-opt-group>
                  <span slot="label">基本类型</span>
                  <a-select-option value="int">int(整型)</a-select-option>
                  <a-select-option value="long">long(长整数型)</a-select-option>
                  <a-select-option value="float">float(单精度浮点型)</a-select-option>
                  <a-select-option value="double">double(双精度浮点型)</a-select-option>
                  <a-select-option value="text">text(字符串)</a-select-option>
                  <a-select-option value="bool">bool(布尔型)</a-select-option>
                </a-select-opt-group>
                <a-select-opt-group>
                  <span slot="label">其他类型</span>
                  <a-select-option value="date">date(时间型)</a-select-option>
                  <a-select-option value="enum">enum(枚举)</a-select-option>
                  <a-select-option value="array">array(数组)</a-select-option>
                  <a-select-option value="object">object(结构体)</a-select-option>
                  <a-select-option value="file">file(文件)</a-select-option>
                  <a-select-option value="password">password(密码)</a-select-option>
                  <a-select-option value="table">table(表格)</a-select-option>
                  <a-select-option value="geoPoint">geoPoint(地理位置)</a-select-option>
                </a-select-opt-group>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col v-if="dataType == 'int'" :span="24">
            <int-metadata @changElemType="changElemType" :typeFormatValue="typeElemType"></int-metadata>
          </a-col>
          <a-col v-if="dataType == 'array'" :span="24">
            <a-form-item label="元素类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-select :getPopupContainer="(node) => node.parentNode" allowClear placeholder="请选择元素类型"
                v-model="elemType" @change="elemTypeChoose">
                <a-select-opt-group>
                  <span slot="label">基本类型</span>
                  <a-select-option value="int">int(整型)</a-select-option>
                  <a-select-option value="long">long(长整数型)</a-select-option>
                  <a-select-option value="float">float(单精度浮点型)</a-select-option>
                  <a-select-option value="double">double(双精度浮点型)</a-select-option>
                  <a-select-option value="text">text(字符串)</a-select-option>
                  <a-select-option value="bool">bool(布尔型)</a-select-option>
                </a-select-opt-group>
                <a-select-opt-group>
                  <span slot="label">其他类型</span>
                  <a-select-option value="date">date(时间型)</a-select-option>
                  <a-select-option value="enum">enum(枚举)</a-select-option>
                  <a-select-option value="object">object(结构体)</a-select-option>
                  <a-select-option value="file">file(文件)</a-select-option>
                  <a-select-option value="password">password(密码)</a-select-option>
                  <a-select-option value="table">table(表格)</a-select-option>
                  <a-select-option value="geoPoint">geoPoint(地理位置)</a-select-option>
                </a-select-opt-group>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col v-if="dataType == 'text'" :span="24">
            <text-metadata @changElemType="changElemType" :typeFormatValue="typeElemType"></text-metadata>
          </a-col>
          <a-col v-if="dataType == 'bool'" :span="24">
            <bool-metadata v-decorator="['elemType']"></bool-metadata>
          </a-col>
          <a-col v-if="dataType == 'date'" :span="24">
            <date-Metadata @changElemType="changElemType" :typeFormatValue="typeElemType"></date-Metadata>
          </a-col>
          <a-col v-if="dataType == 'long'" :span="24">
            <long-metadata @changElemType="changElemType" :typeFormatValue="typeElemType"></long-metadata>
          </a-col>
          <a-col v-if="(elemType == 'object' || dataType == 'object') && 0 < childrenDataList.length" :span="24">
            <object-Metadata ref="objectMetadata" v-decorator="['elemType']" :childrenDataList="childrenDataList"
              :pCode="pCode" @func="childrenChange" :childrenChange="childrenChange" :productInfo="productInfo"
              :flowModel="flowModel"></object-Metadata>
          </a-col>
          <a-col v-if="elemType == 'object' || dataType == 'object'" :span="24">
            <div style="text-align: center; margin-bottom: 10px">
              <a @click="addChildren">
                <a-icon type="plus" />
                添加参数</a>
            </div>
          </a-col>
          <a-col v-if="dataType == 'float'" :span="24">
            <float-Metadata @changElemType="changElemType" :typeFormatValue="typeElemType"></float-Metadata>
          </a-col>
          <a-col v-if="dataType == 'double'" :span="24">
            <double-Metadata @changElemType="changElemType" :typeFormatValue="typeElemType"></double-Metadata>
          </a-col>
          <a-col v-if="dataType == 'file'" :span="24">
            <file-Metadata v-decorator="['elemType']"></file-Metadata>
          </a-col>
          <a-col v-if="dataType == 'table'" :span="24">
            <table-Metadata v-decorator="['elemType']"></table-Metadata>
          </a-col>
          <a-col v-if="dataType == 'enum'" :span="24">
            <enum-Metadata v-decorator="['elemType']"></enum-Metadata>
          </a-col>

          <a-col v-if="dataType == 'int' || dataType == 'double' || dataType == 'text'" :span="24">
            <a-form-item label="计算方法名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <!-- <a-input v-decorator="['']" placeholder="请输入计算方法名称" ></a-input> -->
              <j-dict-select-tag v-model="funcName" placeholder="请选择计算方法名称" dictCode="func_name"></j-dict-select-tag>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item v-if="funcName != '' && funcName != null" label="计算方法参数" :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-input v-decorator="['funcParam' , validatorRules.funcParam]" placeholder="请输入计算方法参数" :allowClear="true"
                autocomplete="off">
              </a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="读写标识" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-radio-group v-decorator="['readWriteFlag']" :defaultValue="'read'" @change="readWriteFlagChange">
                <a-radio :style="radioStyle" value="read"> 只读 </a-radio>
                <a-radio :style="radioStyle" value="readwrite"> 读写 </a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
          <a-col :span="24" v-if="showFlag">
            <a-form-item label="告警指标" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-radio-group v-model="isWarning" @change="isWarningChange">
                <a-radio :style="radioStyle" value="1"> 是 </a-radio>
                <a-radio :style="radioStyle" value="0"> 否 </a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>

          <a-col :span="24" v-if="showFlag">
            <a-form-item label="展示方式" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-radio-group v-decorator="['chart']" @change="chartChange">
                <a-radio :style="radioStyle" value="line"> 折线 </a-radio>
                <a-radio :style="radioStyle" value="pie"> 饼图 </a-radio>
                <a-radio :style="radioStyle" value="table"> 表格 </a-radio>
                <a-radio :style="radioStyle" value="gauge"> 仪表盘 </a-radio>
                <a-radio :style="radioStyle" value="text"> 普通文本框 </a-radio>
                <a-radio :style="radioStyle" value="frontText"> 前置文本框 </a-radio>
                <a-radio :style="radioStyle" value=""> 不展示 </a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>

          <a-col :span="24">
            <a-form-item class="two-words" label="描述" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['remark' , validatorRules.remark]" placeholder="请输入描述" :allowClear="true"
                autocomplete="off"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24" v-if="metadataExtendsList.length && metadataExtendsList.length > 0">
            <a-form-item v-for="(items, index) in metadataExtendsList" :key="index" label="扩展属性" :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-row :gutter="10">
                <a-col :span="10">
                  <a-input v-model="items.code1" placeholder="请输入code" :allowClear="true" autocomplete="off"></a-input>
                </a-col>
                <a-col :span="10">
                  <a-input v-model="items.value" placeholder="请输入oid" :allowClear="true" autocomplete="off"></a-input>
                </a-col>
                <a-col :span="4">
                  <a @click="deleteInfo(index)"> 删除</a>
                </a-col>
              </a-row>
            </a-form-item>
            <div style="text-align: center; margin-bottom: 10px">
              <a @click="addPro">
                <a-icon type="plus" />
                增加扩展属性</a>
            </div>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
    <div v-if="!disabled" :style="{
        position: 'relative',
        right: 0,
        bottom: 0,
        width: '100%',
        borderTop: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
        textAlign: 'right',
        zIndex: 1,
      }">
      <a-button @click="pClose(false)" type="primary" :loading="loading" ghost style="margin-right: 0.8rem">关闭
      </a-button>
      <a-button @click="submitForm" type="primary" :loading="loading">保存</a-button>
    </div>
    <proerty-metadata-modal :productInfo="productInfo" ref="modalForm" @ok="modalFormOk"></proerty-metadata-modal>
  </a-spin>
</template>

<script>
  import ProertyMetadataModal from './ProertyMetadataModal'
  import {
    httpAction,
    getAction
  } from '@/api/manage'
  import pick from 'lodash.pick'
  import intMetadata from '@/components/metadataFormat/intMetadata.vue'
  import arrayMetadata from '@/components/metadataFormat/arrayMetadata.vue'
  import textMetadata from '@/components/metadataFormat/textMetadata.vue'
  import boolMetadata from '@/components/metadataFormat/boolMetadata.vue'
  import dateMetadata from '@/components/metadataFormat/dateMetadata.vue'
  // import processManage from './processManage.vue'
  import longMetadata from '@/components/metadataFormat/longMetadata.vue'
  import objectMetadata from '@/components/metadataFormat/objectMetadata.vue'
  import floatMetadata from '@/components/metadataFormat/floatMetadata.vue'
  import doubleMetadata from '@/components/metadataFormat/doubleMetadata.vue'
  import fileMetadata from '@/components/metadataFormat/fileMetadata.vue'
  import tableMetadata from '@/components/metadataFormat/tableMetadata.vue'
  import enumMetadata from '@/components/metadataFormat/enumMetadata.vue'
  import JFormContainer from '@/components/jeecg/JFormContainer'
  import JDictSelectTag from '@/components/dict/JDictSelectTag'
  import {
    ValidateRequiredFields,
    ValidateOptionalFields
  } from '@/utils/rules.js'
  import {
    deleteAction
  } from '../../../api/manage'

  export default {
    name: 'ProertyMetadataForm',
    components: {
      JFormContainer,
      JDictSelectTag,
      intMetadata,
      arrayMetadata,
      // processManage,
      textMetadata,
      boolMetadata,
      dateMetadata,
      longMetadata,
      objectMetadata: () => import('@/components/metadataFormat/objectMetadata.vue'),
      floatMetadata,
      doubleMetadata,
      fileMetadata,
      tableMetadata,
      enumMetadata,
      // proertymetadatamodal,
      ProertyMetadataModal: () => import('./ProertyMetadataModal.vue'),
    },
    props: {
      // 流程表单data
      formData: {
        type: Object,
        default: () => {},
        required: false,
      },
      // 表单模式：true流程表单 false普通表单
      formBpm: {
        type: Boolean,
        default: false,
        required: false,
      },
      // 表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false,
      },
      //告警指标、图标类型展示标识
      showFlag: {
        type: Boolean,
        default: true,
        required: false,
      },
      productInfo: {},
    },
    data() {
      return {
        turnOff: false,
        suspend: false,
        flowModel: false,
        dataHelp: '',
        dataStatus: '',
        chainIdJudge: false,
        records: {},
        info: {},
        form: this.$form.createForm(this),
        model: {},
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          },
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          },
        },
        confirmLoading: false,
        validatorRules: {
          code: {
            rules: [{
              required: true,
              validator:(rule, value, callback) =>ValidateRequiredFields (rule, value, callback,'属性标识',30)
            }]
          },
          name: {
            rules: [{
              required: true,
              validator: (rule, value, callback) =>ValidateRequiredFields (rule, value, callback,'属性名称',30)
            }]
          },
          indexes: {
            rules: [
              {
                required: false,
                validator: (rule, value, callback) =>ValidateOptionalFields (rule, value, callback,'属性索引',50)
              }
            ]
          },
          unit: {
            rules: [
              {
                required: false,
                validator: (rule, value, callback) =>ValidateOptionalFields (rule, value, callback,'属性单位',50)
              }
            ]
          },
          serial: {
            rules: [
              {required: true,message: '请输入序号'}
            ]
          },
          tableName: {
            rules: [
              {
                required: false,
                validator: (rule, value, callback) =>ValidateOptionalFields (rule, value, callback,'持久化存储表名',100)
              }
            ]
          },
          tagColumn: {
            rules: [
              {
                required: false,
                validator: (rule, value, callback) =>ValidateOptionalFields (rule, value, callback,'持久化存储Tag列',100)
              }
            ]
          },
          transferProtocol: {
            rules: [
              {required: true,message: '请选择传输协议!'}
            ]
          },
          funcParam: {
            rules: [
              {
                required: false,
                validator: (rule, value, callback) =>ValidateOptionalFields (rule, value, callback,'计算方法参数',255)
              }
            ]
          },
          remark: {
            rules: [
              {required: false, validator: (rule, value, callback) =>ValidateOptionalFields (rule, value, callback,'描述',100)}
            ]
          }
        },
        url: {
          queryChainList: '/flow/chain/queryChain',
          add: '/product/proertyMetadata/add',
          edit: '/product/proertyMetadata/edit',
          queryById: '/product/proertyMetadata/queryById',
          childrenList: '/product/proertyMetadata/childrenList',
          extendList: '/product/proertyMetadata/extendList',
          delete: '/flow/chain/delete',
        },
        loading: false,
        dataType: '',
        readWriteFlag: 'read',
        isWarning: '0',
        value: '',
        metadataExtendsList: [],
        chart: 'line',
        typeElemType: '',
        radioStyle: {
          height: '30px',
          lineHeight: '30px',
        },
        funcName: '',
        childrenDataList: [], //子列表
        extendListDataList: [],
        chainId: '',
        elemType: '',
        whIndex: '',
        // unit: '',
        code1: '',
        value1: '',
        saveFlag: false,
        pid: '',
        pCode: '',
        transferProtocol: undefined,
        isSave: '0',
        isDisplay: '',
        //protocolList:[]
      }
    },
    computed: {
      // processConfig() {
      //   return this.chainIdJudge == true ? '编辑流程' : '配置流程'
      // },
      formDisabled() {
        if (this.formBpm === true) {
          if (this.formData.disabled === false) {
            return false
          }
          return true
        }
        return this.disabled
      },
      showFlowSubmitButton() {
        if (this.formBpm === true) {
          if (this.formData.disabled === false) {
            return true
          }
        }
        return false
      },
    },
    created() {
      // 如果是流程中表单，则需要加载流程表单data
      this.showFlowData()
    },
    methods: {
      nameChange() {
        this.suspend = false
      },
      codeChange() {
        this.suspend = false
      },
      // 添加参数按钮 打开新的子级页面，本质上还是当前vue组件
      addChildren() {
        if (!this.saveFlag || this.model.dataType == null) {
          this.$message.info('请先保存本页面数据')
          return false
        }
        const that = this
        // 此add 先去ProertyMetadataModal组件中的add方法， 然后ProertyMetadataModal组件再调用本组件的add方法
        that.$refs.modalForm.add('1', this.pid, this.pCode)
        that.$refs.modalForm.title = '新增'
        that.$refs.modalForm.showFlag = false
        that.$refs.modalForm.disableSubmit = false
      },
      changeProtocol(value, option) {
        this.transferProtocol = value ? option.componentOptions.children[0].text.trim() : undefined
      },
      add(index, pid, pCode) {
        if (index == undefined) {
          this.whIndex = '0'
        } else {
          this.whIndex = '1'
        }
        this.edit({}, pid, pCode)
      },

      edit(record, pid, pCode) {
        this.flowModel = record.isFlowModel
        this.chainId = record.chainId
        if (record.chainId != null && record.chainId != '' && record.chainId != undefined) {
          this.chainIdJudge = true
        } else {
          this.chainIdJudge = false
        }
        this.records.metadataId = record.id
        this.records.chainId = record.chainId
        this.records.productId = record.productId
        this.info = record
        this.records.assetsCategoryId = this.productInfo.assetsCategoryId
        this.records.displayName = this.productInfo.displayName
        this.records.protocol = record.transferProtocol
        this.records.code = record.code
        this.records.unit = record.unit
        this.form.resetFields()
        this.dataType = record.dataType
        this.elemType = record.elemType
        this.funcName = record.funcName
        if (record && record.isWarning != undefined) {
          this.isWarning = record.isWarning
        } else {
          this.isWarning = '0'
        }
        //如果record为0、false、undefined、null 则无法通过&&符前面的判断
        //如果record为{} 则无法通过&&符后面的判断
        if (record && record.isSave !== undefined) {
          this.isSave = record.isSave
          this.isDisplay = record.chart
        } else {
          this.isSave = '0'
          this.isDisplay = ''
        }
        if (pid) {
          // add调用本方法传递了pid的话，则将pid直接赋值给this.pid
          this.pid = pid
          this.pCode = pCode
          this.turnOff = true
          // this.getChildrenDataList(this.pid)
        }
        if (record.id == null && !pid) {
          this.turnOff = true
        }
        if (JSON.stringify(record) != '{}') {
          this.saveFlag = true
          // 将record中的属性id的值作为当前pid来实现数据保存, 以实现当前页面新开页面的pid赋值
          this.pid = record.id
          this.pCode = record.code
        }
        if (record.transferProtocol) {
          this.transferProtocol = record.transferProtocol.trim()
        }
        //判断是否去加载
        if (1 != this.whIndex) {
          this.getChildrenDataList(record.id)
        }
        this.model = Object.assign({}, record)
        if (record && (record.readWriteFlag== null||record.readWriteFlag== ''||record.readWriteFlag==undefined)) {
          this.model.readWriteFlag='read'
        }

        this.getExtendList(record.id)
        this.visible = true
        this.typeElemType = record.formatValue
        this.$nextTick(() => {
          this.form.setFieldsValue(
            pick(
              this.model,
              'code',
              'name',
              'transferProtocolId',
              'funcParam',
              'indexes',
              'readWriteFlag',
              'remark',
              'unit',
              'chart',
              'tableName',
              'tagColumn',
              'conditionValue',
              'newCode',
              'newName',
              'chart',
              'serial',
              'originStep'
              // 'metadataExtendsList'
            )
          )
        })
      },

      changElemType(info) {
        this.typeElemType = info
      },

      //通过id获取子list
      getChildrenDataList(id) {
        if (undefined != id && null != id) {
          getAction(this.url.childrenList, {
            id: id
          }).then((res) => {
            if (res.success) {
              if (null == res.result) {
                this.childrenDataList = []
              } else {
                this.childrenDataList = res.result
              }
            }
          })
        } else {
          this.childrenDataList = []
        }
      },
      getExtendList(id) {
        if (undefined != id && null != id) {
          getAction(this.url.extendList, {
            id: id
          }).then((res) => {
            if (res.success) {
              if (null == res.result) {
                this.metadataExtendsList = null
              } else {
                this.metadataExtendsList = res.result
              }
            }
          })
        } else {
          this.metadataExtendsList = []
        }
      },
      // 渲染流程表单数据
      showFlowData() {
        if (this.formBpm === true) {
          let params = {
            id: this.formData.dataId
          }
          getAction(this.url.queryById, params).then((res) => {
            if (res.success) {
              this.edit(res.result)
            }
          })
        }
      },

      pClose() {
        const that = this
        that.$emit('closePop')
      },

      addPro() {
        this.metadataExtendsList.push({
          value1: this.value,
          code1: this.code,
        })
      },
      deleteInfo(index) {
        this.metadataExtendsList.splice(index, 1)
      },
      submitForm() {
        if (this.dataType == '' || this.dataType == null) {
          this.dataHelp = '请选择数据类型'
          this.dataStatus = 'error'
        }
        // this.$refs.verifyViews.validataForm();
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let formData = Object.assign(that.model, values)
            formData.code = formData.code.trim()
            formData.unit = formData.unit != null ? formData.unit : ''
            if (that.pid != null) {
              let pend = this.childrenDataList.filter((item) => {
                return item.code == formData.code || item.name == formData.name
              })
              if (pend != null && pend.length > 0) {
                this.suspend = true
              }
            }
            if (this.suspend == true) {
              that.$message.warning('名称或属性标识重复')
              that.confirmLoading = false
              return
            }
            let httpurl = ''
            let method = ''
            let f = true //判断添加还是编辑
            if (!this.model.id) {
              httpurl += that.url.add
              method = 'post'
            } else {
              this.turnOff = false
              formData.id = this.model.id
              httpurl += that.url.edit
              f = false
              method = 'put'
            }
            formData.elemType = that.elemType
            formData.dataType = that.dataType
            formData.funcName = that.funcName
            // 如果当前页面的id 和 当前页面存储的this.pid值一样的话，那么说明此时页面的操作是编辑操作
            if (f) {
              formData.pid = that.pid
              formData.pCode = that.pCode
            } else {
              formData.proertyMetadataList = that.childrenDataList
            }
            formData.chainId = this.chainId
            formData.productId = window.localStorage.productsettingId
            formData.metadataExtendsList = that.metadataExtendsList
            formData.productId = this.productInfo.id
            formData.transferProtocol = this.transferProtocol
            formData.isSave = this.isSave
            formData.isWarning = this.isWarning
            formData.formatValue = this.typeElemType
            httpAction(httpurl, formData, method)
              .then((res) => {
                if (res.success) {
                  if (this.turnOff) {
                    // 将保存完的属性定义的id作为子级的pid
                    this.pid = res.result.id
                    this.pCode = res.result.code
                    formData.id = this.model.id = res.result.id
                  }
                  this.saveFlag = true
                  that.$message.success(res.message)
                  if (f) {
                    that.$emit('ok', res.result)
                  } else {
                    that.$emit('ok', formData)
                  }
                  // 实现保存数据后弹出层不关闭
                  that.visible = true
                } else {
                  that.$message.warning(res.message)
                }
              })
              .finally(() => {
                that.confirmLoading = false
              })
          }
        })
      },
      popupCallback(row) {
        this.form.setFieldsValue(
          pick(
            row,
            'code',
            'name',
            'indexes',
            'funcName',
            'funcParam',
            'elemType',
            'readWriteFlag',
            'remark',
            'unit',
            'chart',
            'metadataExtendsList',
            'tableName',
            'tagColumn',
            'conditionValue',
            'newCode',
            'newName',
            'serial',
            'originStep'
          )
        )
      },
      dataTypeChoose(dataType) {
        this.typeElemType = ''
        this.dataHelp = ''
        this.dataStatus = ''
        this.dataType = dataType
        this.form.setFieldsValue(pick({
          chart: 'table'
        }, 'chart'))
      },
      elemTypeChoose(elemType) {
        this.elemType = elemType
      },
      readWriteFlagChange(readWriteFlag) {
        this.readWriteFlag = readWriteFlag.target.readWriteFlag
      },
      isWarningChange(isWarning) {
        this.isWarning = isWarning.target.value
      },
      isPersistenceFlagChange(e) {
        this.isSave = e.target.value
      },
      chartChange(chart) {
        this.chart = chart.target.value
        this.isDisplay = this.chart
      },
      changeElemType(value) {
        this.form.setFieldsValue({
          elemType: value,
        })
      },
      childrenChange(data) {
        this.childrenDataList = data
      },
      modalFormOk(info) {
        let y = this.childrenDataList.filter((item) => {
          return item.id == info.id
        })
        if (info.id && y.length == 0) {
          this.childrenDataList.push(info)
        } else if (info.id && y.length > 0) {
          this.childrenDataList.forEach((e, index) => {
            if (this.childrenDataList[index].id == info.id) {
              this.childrenDataList.splice(index, 1, info)
            }
          })
        }
        this.suspend = false
      },
      modalFormOk2(data) {
        this.records.chainId = data.chainId
        this.chainId = data.chainId
        // 新增/修改 成功时，重载列表
        // this.loadData()
      },
      closeModal() {
        this.editFlag = false
      },
    },
  }
</script>
<style lang='less' scoped>
  ::v-deep .two-words>div>label {
    letter-spacing: 4px;
  }

  ::v-deep .two-words>div>label::after {
    letter-spacing: 0px;
  }
</style>