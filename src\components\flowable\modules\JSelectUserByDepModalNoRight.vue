<template>
  <j-modal
    :title="title"
    :width="modalWidth"
    :visible="visible"
    :centered='true'
    switchFullscreen
    :maskClosable='false'
    wrapClassName='limit-height-modal'
    @ok="handleSubmit"
    @cancel="close"
    cancelText="关闭">
    <a-row class='row'>
      <a-col :lg="6" :md='24' class='col'>
        <a-card :bordered="false" class='card'>
          <!--组织机构-->
          <a-directory-tree selectable :selectedKeys="selectedDepIds" :checkStrictly="true"
            :style="{maxHeight:'640px',overflow:'auto'}" :treeData="departTree" :expandAction="false"
            @select="onDepSelect" :load-data="onLoadDepartment" @expand="onDepExpand" />
        </a-card>
      </a-col>
      <a-col :lg='18' :md="24" class='col'>
        <a-card :bordered="false" class='card'>
          <a-form-model>
            <a-form-model-item label="用户姓名" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-row type="flex" :gutter="8">
                <a-col :span="18">
                  <a-input-search :style="{width:'100%'}" placeholder="请输入用户姓名" v-model="queryParam.realname"
                    @search="onSearch" allowClear></a-input-search>
                </a-col>
                <a-col :span="6">
                  <a-button @click="searchReset(1)" icon="redo">重置</a-button>
                </a-col>
              </a-row>
            </a-form-model-item>
          </a-form-model>
          <!--用户列表-->
          <a-table ref="table"  size="middle" rowKey="id" :columns="columns" :dataSource="dataSource"
            :pagination="ipagination"
            :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange,type: getType}"
            :loading="loading" @change="handleTableChange">
          </a-table>
        </a-card>
      </a-col>
    </a-row>
  </j-modal>
</template>

<script>
  import {
    filterObj
  } from '@/utils/util'
  import {
    queryDepartTreeSync,
    queryUserByDepId
  } from '@/api/api'
  import {
    getAction
  } from '@/api/manage'

  export default {
    name: 'JSelectUserByDepModalEnhance',
    components: {},
    props: {
      modalWidth: {
        type: Number,
        default: 1300,
        required: false
      },
      multi: {
        type: Boolean,
        default: true,
        required: false
      },
      store: {
        type: String,
        default: 'username',
        required: false
      },
      text: {
        type: String,
        default: 'realname',
        required: false
      },
      userIds: {
        type: String,
        required: false
      },
      tips: {
        type: String,
        required: false,
        default: '请先选择用户1111',
      },
      allowClear:{
        type:Boolean,
        default:false,
        required:false
      }
    },
    data() {
      return {
        queryParam: {
          username: '',
        },
        columns: [{
            title: '用户账号',
            align: 'center',
            dataIndex: 'username',
            width: '150px',
          },
          {
            title: '用户姓名',
            align: 'center',
            dataIndex: 'realname',
            width: '150px',
          },
          {
            title: '部门',
            align: 'center',
            dataIndex: 'departIds_dictText',
            width: '150px',
          },
          {
            title: '手机',
            align: 'center',
            dataIndex: 'phone',
            width: '130px',
          },
        ],
        scrollTrigger: {},
        dataSource: [],
        selectionRows: [],
        selectedRowKeys: [],
        selectUserRows: [],
        selectUserIds: [],
        title: '根据部门选择绑定用户',
        ipagination: {
          current: 1,
          pageSize: 10,
          pageSizeOptions: ['10', '20', '30'],
          showTotal: (total, range) => {
            return range[0] + '-' + range[1] + ' 共' + total + '条'
          },
          showQuickJumper: true,
          showSizeChanger: true,
          total: 0
        },
        isorter: {
          column: 'createTime',
          order: 'desc'
        },
        selectedDepIds: [],
        departTree: [],
        visible: false,
        form: this.$form.createForm(this),
        loading: false,
        expandedKeys: [],
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 4
          },
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 11
          },
        },
      }
    },
    computed: {
      // 计算属性的 getter
      getType: function () {
        return this.multi == true ? 'checkbox' : 'radio'
      }
    },
    watch: {
      userIds: {
        immediate: true,
        handler() {
          this.initUserNames()
        }
      },
    },
    created() {
      // 该方法触发屏幕自适应
      //this.resetScreenSize()
      this.loadData()
    },
    methods: {
      initUserNames() {
        if (this.userIds) {
          // 这里最后加一个 , 的原因是因为无论如何都要使用 in 查询，防止后台进行了模糊匹配，导致查询结果不准确
          let values = this.userIds.split(',') + ','
          let param = {
            [this.store]: values
          }
          getAction('/sys/user/getMultiUser', param).then((list) => {
            this.selectionRows = []
            let selectedRowKeys = []
            let textArray = []
            if (list && list.length > 0) {
              for (let user of list) {
                textArray.push(user[this.text])
                selectedRowKeys.push(user['id'])
                this.selectionRows.push(user)
              }
            }
            this.selectedRowKeys = selectedRowKeys
            this.$emit('initComp', textArray.join(','))
          })

        } else {
          // JSelectUserByDep组件bug issues/I16634
          this.$emit('initComp', '')
          // 前端用户选择单选无法置空的问题 #2610
          this.selectedRowKeys = []
        }
      },
      async loadData(arg) {
        if (arg === 1) {
          this.ipagination.current = 1
        }
        let params = this.getQueryParams() //查询条件
        this.loading = true
        //'/sys/user/queryUserComponentData'
        getAction('/terminal/terminalDevice/getUnbindUser', params).then(res => {
          console.log(res)
          if (res.success) {
            this.dataSource = res.result.records
            this.ipagination.total = res.result.total
          }
        }).finally(() => {
          this.loading = false
        })
      },
      // 触发屏幕自适应
      resetScreenSize() {
        let screenWidth = document.body.clientWidth
        if (screenWidth < 500) {
          this.scrollTrigger = {
            x: 800
          }
        } else {
          this.scrollTrigger = {}
        }
      },
      showModal() {
        this.visible = true
        this.queryDepartTree()
        this.initUserNames()
        this.loadData()
        this.form.resetFields()
      },
      getQueryParams() {
        let param = Object.assign({}, this.queryParam, this.isorter)
        param.field = this.getQueryField()
        param.pageNo = this.ipagination.current
        param.pageSize = this.ipagination.pageSize
        // param.departId = this.selectedDepIds.join(',')
        param.orgCode  = this.selectedDepIds.join(',')
        return filterObj(param)
      },
      getQueryField() {
        let str = 'id,'
        for (let a = 0; a < this.columns.length; a++) {
          str += ',' + this.columns[a].dataIndex
        }
        return str
      },
      searchReset(num) {
        let that = this
        that.selectedRowKeys = []
        that.selectUserIds = []
        that.selectedDepIds = []
        if (num !== 0) {
          that.queryParam = {}
          that.loadData(1)
        }
      },
      close() {
        this.selectedRowKeys = []
        this.selectionRows = []
        this.searchReset(0)
        this.visible = false
      },
      handleTableChange(pagination, filters, sorter) {
        //TODO 筛选
        if (Object.keys(sorter).length > 0) {
          this.isorter.column = sorter.field
          this.isorter.order = 'ascend' === sorter.order ? 'asc' : 'desc'
        }
        this.ipagination = pagination
        this.loadData()
      },
      handleSubmit() {
        console.log(this.selectedRowKeys,"dfjdlfjldfjldjf")
        let that = this
        that.getSelectUserRows()
        if (that.selectUserRows.length > 0) {
          that.$emit('ok', that.selectUserRows)
          that.searchReset(0)
          that.close()
        } else if(this.allowClear){
          //允许删除时，可以不选择用户
          that.$emit('ok', that.selectUserRows)
          that.searchReset(0)
          that.close()

        } else {
          that.$message.warn(that.tips)
        }
      },
      //获取选择用户信息
      getSelectUserRows() {
        this.selectUserRows = []
        for (let row of this.selectionRows) {
          if (this.selectedRowKeys.includes(row.id)) {
            this.selectUserRows.push(row)
          }
        }
        this.selectUserIds = this.selectUserRows.map(row => row.username).join(',')
      },
      // 点击树节点,筛选出对应的用户
      onDepSelect(selectedDepIds) {
        if (selectedDepIds[0] != null) {
          if (this.selectedDepIds[0] !== selectedDepIds[0]) {
            this.selectedDepIds = [selectedDepIds[0]]
          }
          this.loadData(1)
        }
      },
      onSelectChange(selectedRowKeys, selectionRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectionRows = selectionRows
      },
      onSearch() {
        this.loadData(1)
      },
      // 根据选择的id来查询用户信息
      initQueryUserByDepId(selectedDepIds) {
        this.loading = true
        return queryUserByDepId({
          id: selectedDepIds.toString()
        }).then((res) => {
          if (res.success) {
            this.dataSource = res.result
            this.ipagination.total = res.result.length
          }
        }).finally(() => {
          this.loading = false
        })
      },
      queryDepartTree() {
        //update-begin-author:taoyan date:20211202 for: 异步加载部门树 https://github.com/jeecgboot/jeecg-boot/issues/3196
        this.expandedKeys = []
        this.departTree = []
        queryDepartTreeSync().then((res) => {
          if (res.success) {
            for (let i = 0; i < res.result.length; i++) {
              let temp = res.result[i]
              this.departTree.push(temp)
            }
          }
        })
      },
      onDepExpand(e, data) {
        this.onLoadDepartment(data.node)
      },
      onLoadDepartment(treeNode) {
        return new Promise(resolve => {
          queryDepartTreeSync({
            pid: treeNode.dataRef.id
          }).then((res) => {
            if (res.success) {
              //判断chidlren是否为空，并修改isLeaf属性值
              if (res.result.length == 0) {
                treeNode.dataRef['isLeaf'] = true

              } else {
                treeNode.dataRef['children'] = res.result
              }
            }
          })
          resolve()
        })
      },
      //update-end-author:taoyan date:20211202 for: 异步加载部门树 https://github.com/jeecgboot/jeecg-boot/issues/3196
      modalFormOk() {
        this.loadData()
      }
    }
  }
</script>

<style scoped lang='less'>
@import '~@assets/less/limitModalHeight.less';

  //.ant-table-tbody .ant-table-row td {
  //  padding-top: 10px;
  //  padding-bottom: 10px;
  //}
  //
  //#components-layout-demo-custom-trigger .trigger {
  //  font-size: 18px;
  //  line-height: 64px;
  //  padding: 0 24px;
  //  cursor: pointer;
  //  transition: color .3s;
  //}

.row{
  background-color: #ececec;
  width: 100%;
}
@media (min-width: 968px) {
  .row{
    height: 100%;
    //max-width: 100%;
    overflow: auto !important;

    .col{
      padding: 10px;
      height: 100%;
      overflow: hidden !important;

      .card{
        height: 100%;
        overflow: auto !important;
      }
      &:nth-child(2){
        padding: 10px 10px 10px 0px;
      }
    }
  }
}
@media (max-width: 967px) {
  .row{
    height: auto;
    overflow: hidden !important;

    .col{
      padding: 10px 10px 0px;
      height: auto;
      overflow: hidden !important;

      .card{
        height: auto;
        overflow: hidden !important;
      }
      &:last-child{
        padding: 10px
      }
    }
  }
}
</style>