<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll zxw">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <!-- 查询区域 -->
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="字典名称">
                  <a-input
                    :maxLength="maxLength"
                    placeholder="请输入"
                    autocomplete="off"
                    :allowClear="true"
                    v-model="queryParam.dictName"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="字典编码">
                  <a-input
                    :maxLength="maxLength"
                    placeholder="请输入"
                    autocomplete="off"
                    :allowClear="true"
                    v-model="queryParam.dictCode"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="字典描述">
                  <a-input
                    :maxLength="maxLength"
                    placeholder="请输入"
                    autocomplete="off"
                    :allowClear="true"
                    v-model="queryParam.description"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="colBtnsSpan()">
                <span
                  class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                >
                  <a-button type="primary" @click="searchQuery" class="btn-search-style">查询</a-button>
                  <a-button @click="searchReset" class="btn-reset-style">重置</a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered="false" style="flex: auto" class="core">
        <div class="table-operator">
          <a-button @click="handleAdd">新增</a-button>
          <a-button @click="handleExportXls('字典信息')">导出</a-button>
          <a-upload
            name="file"
            :showUploadList="false"
            :multiple="false"
            :headers="tokenHeader"
            :action="importExcelUrl"
            @change="handleImportExcel"
          >
            <a-button>导入</a-button>
          </a-upload>
          <a-button @click="handleTemplateXls()" @mouseover="mouseOver" >下载模版</a-button>
          <a-button @click="refleshCache()">刷新缓存</a-button>
          <a-button @click="openDeleteList">回收站</a-button>
        </div>
        <a-table
          ref="table"
          rowKey="id"
          :columns="columns"
          :dataSource="dataSource"
          :pagination="ipagination"
          :loading="loading"
          bordered
          @change="handleTableChange"
        >
          <span
            slot="action"
            slot-scope="text, record"
            class="caozuo"
            style="display: inline-block; white-space: nowrap; text-align: center"
          >
            <a @click="editDictItem(record)">字典配置</a>
            <a-divider type="vertical" />
            <a @click="handleEdit(record)">
              编辑
            </a>
            <a-divider type="vertical" />
            <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
              <a>删除</a>
            </a-popconfirm>
          </span>
          <template slot="tooltip" slot-scope="text">
            <a-tooltip placement="topLeft" :title="text" trigger="hover">
              <div class="tooltip">
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>

        <dict-modal ref="modalForm" @ok="modalFormOk"></dict-modal>
        <!-- 字典类型 -->
        <dict-item-list ref="dictItemList"></dict-item-list>
        <dict-delete-list ref="dictDeleteList" @refresh="() => loadData()"></dict-delete-list>
        <!-- 下载模版 -->
        <iframe id="download" style="display: none" v-if="isDown == 1"></iframe>
      </a-card>
    </a-col>
  </a-row>
</template>

<script>
import { filterObj } from '@/utils/util'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import DictModal from './modules/DictModal'
import DictItemList from './DictItemList'
import DictDeleteList from './DictDeleteList'
import { getAction } from '@/api/manage'
import { UI_CACHE_DB_DICT_DATA } from '@/store/mutation-types'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
import Vue from 'vue'

export default {
  name: 'DictList',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components: { DictModal, DictItemList, DictDeleteList },
  data() {
    return {
      maxLength:50,
      isDown: 1,
      description: '这是数据字典页面',
      formItemLayout: {
        labelCol: {
          style: 'width:80px'
        },
        wrapperCol: {
          style: 'width:calc(100% - 80px)'
        }
      },
      visible: false,
      // 查询条件
      queryParam: {
        dictCode: '',
        dictName: '',
      },
      // 表头
      columns: [
        {
          title: '字典名称',
          dataIndex: 'dictName',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 150px;max-width:300px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '字典编码',
          dataIndex: 'dictCode',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 150px;max-width:300px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '描述',
          dataIndex: 'description',
          scopedSlots: { customRender: 'tooltip' },
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 200,
          scopedSlots: { customRender: 'action' },
        },
      ],
      dict: '',
      url: {
        list: '/sys/dict/list',
        delete: '/sys/dict/delete',
        exportXlsUrl: 'sys/dict/exportXls',
        importExcelUrl: 'sys/dict/importExcel',
        refleshCache: 'sys/dict/refleshCache',
        queryAllDictItems: 'sys/dict/queryAllDictItems',
        downloadTemplateXlsUrl: 'sys/dict/downloadTemplate',
      },
    }
  },
  mounted() {},
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
    downloadTemplateXlsUrl: function () {
        this.isDown == 1
        return `${window._CONFIG['domianURL']}/${this.url.downloadTemplateXlsUrl}`
      },
  },
  methods: {
     mouseOver() {
        this.isDown = 1
      },
      handleDetailPage: function (record) {
        this.isDown = 2
        this.$parent.pButton2(1, record)
      },
    //excel模板
      handleTemplateXls() {
        if (this.isDown = 1) {
          const path = this.downloadTemplateXlsUrl
          document.getElementById('download').src = path
        }
      },
    getQueryParams() {
      var param = Object.assign({}, this.queryParam, this.isorter)
      param.field = this.getQueryField()
      param.pageNo = this.ipagination.current
      param.pageSize = this.ipagination.pageSize
      if (this.superQueryParams) {
        param['superQueryParams'] = encodeURI(this.superQueryParams)
        param['superQueryMatchType'] = this.superQueryMatchType
      }
      return filterObj(param)
    },
    //取消选择
    cancelDict() {
      this.dict = ''
      this.visible = false
      this.loadData()
    },
    //编辑字典数据
    editDictItem(record) {
      this.$refs.dictItemList.edit(record)
    },
    // 重置字典类型搜索框的内容
    searchReset() {
      var that = this
      that.queryParam.dictName = ''
      that.queryParam.dictCode = ''
      that.queryParam.description = ''
      that.loadData(this.ipagination.current)
    },
    openDeleteList() {
      this.$refs.dictDeleteList.show()
    },
    refleshCache() {
      getAction(this.url.refleshCache)
        .then((res) => {
          if (res.success) {
            //重新加载缓存
            getAction(this.url.queryAllDictItems).then((res) => {
              if (res.success) {
                Vue.ls.remove(UI_CACHE_DB_DICT_DATA)
                Vue.ls.set(UI_CACHE_DB_DICT_DATA, res.result, 7 * 24 * 60 * 60 * 1000)
              }
            })
            this.$message.success('刷新缓存完成！')
          }
        })
        .catch((e) => {
          this.$message.warn('刷新缓存失败！')
        })
    },
  },
  watch: {
    openKeys(val) {},
  },
}
</script>
<style lang="less" scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
</style>