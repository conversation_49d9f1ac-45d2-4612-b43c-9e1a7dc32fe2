<template>
  <a-spin :spinning='confirmLoading'>
    <j-form-container style="min-height: 50vh;" :disabled="disabled">
      <a-form-model ref='ruleForm' slot='detail' :model='model' :rules='validatorRules'>
        <a-row>
          <a-col v-bind='formItemLayout1'>
            <a-form-model-item label='策略名称' prop='policyName' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <a-input v-model='model.policyName' placeholder='请输入策略名称' allowClear />
            </a-form-model-item>
          </a-col>
          <a-col v-bind='formItemLayout1'>
            <a-form-model-item label='资源类型' prop='resourceType' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <a-select v-model="model.resourceType" style="width: 100%" placeholder='请选择资源类型' :allowClear="false">
                <a-select-option v-for="item in deviceTypes" :key="item.value">
                  {{ item.text }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col v-bind='formItemLayout1'>
            <a-form-model-item label='资源分类' prop='resourceCategoryIds' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <a-tree-select v-model="model.resourceCategoryIds" style="width:100%"
                :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }" :tree-data="categoryTreeData"
                placeholder="请选择资源分类" :filterTreeNode="filterTreeNode" multiple :maxTagCount="5"
                :getPopupContainer="(node) => node.parentNode" :treeNodeFilterProp="'title'"
                :replaceFields="{ children: 'children', title: 'title', key: 'key', value: 'value' }">
              </a-tree-select>
            </a-form-model-item>
          </a-col>
          <a-col v-bind='formItemLayout1'>
            <a-form-model-item label='标签' prop='tags' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <a-select v-model="model.tags" mode="multiple" style="width: 100%;" :maxTagCount="5" placeholder='请选择标签'
                allowClear optionFilterProp="children">
                <a-select-option v-for="item in tagList" :key="item.value">
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col v-bind='formItemLayout1'>
            <a-form-model-item label='启用状态' prop='isEnable' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <a-switch v-model="model.isEnable" checked-children="启用" un-checked-children="未启用" />
            </a-form-model-item>
          </a-col>
          <a-col v-bind='formItemLayout1'>
            <a-form-model-item label='策略描述' prop='policyDesc' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <a-textarea v-model='model.policyDesc' placeholder='请输入策略描述' allowClear
                :auto-size="{ minRows: 2, maxRows: 4 }" style="width: 100%;" />
            </a-form-model-item>
          </a-col>
          <!--  规则区域          -->
          <a-col v-bind='formItemLayout' v-if="ruleList.length >= 2">
            <a-form-model-item label='匹配基准' prop='policyMatchBasis' :labelCol='{ xs: { span: 24 }, sm: { span: 2 } }'
              :wrapperCol='{ xs: { span: 24 }, sm: { span: 22 } }'>
              <div style="display: flex; align-items: center;height: 40px;">
                <a-select v-model="model.policyMatchBasis" style="width: 360px" placeholder='请选择匹配基准'
                  @change="logicChange">
                  <a-select-option v-for="item in logicTypes" :key="item.value">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
                <a-input :disabled="model.policyMatchBasis !== 'self'" style="margin-left: 8px;flex:1"
                  v-model='model.policyMatchBasisFormulas' placeholder='A and ( B or C )' allowClear />
              </div>
            </a-form-model-item>
          </a-col>
          <a-col v-bind='formItemLayout'>
            <a-form-model-item label='匹配规则' prop='policyMatchRule' :labelCol='{ xs: { span: 24 }, sm: { span: 2 } }'
              :wrapperCol='{ xs: { span: 24 }, sm: { span: 22 } }'>
              <a-table :columns="columns" :data-source="ruleList" :bordered="false" :pagination="false">
                <template slot="ruleType" slot-scope="text, record, index">
                  <a-select v-model="text" style="width: 180px" placeholder='请选择类别'
                    @change="e => handleChange(e, index, 'ruleType')">
                    <a-select-option v-for="item in ruleTypes" :key="item.value">
                      {{ item.text }}
                    </a-select-option>
                  </a-select>
                </template>
                <template :slot="'ruleKey'" slot-scope="text, record, index">
                  <div>
                    <a-input style="margin: -5px 0" v-model="ruleList[index]['ruleKey']" placeholder="请输入字段"
                      @change="e => handleChange(e.target.value, index, 'ruleKey')" />
                    <!-- <template v-else>
                    {{ text }}
                  </template> -->
                  </div>
                </template>
                <!-- 规则的匹配模式 -->
                <template slot="ruleOperator" slot-scope="text, record, index">
                  <a-select v-model="text" style="width: 180px" placeholder='请选择匹配模式'
                    @change="e => handleChange(e, index, 'ruleOperator')">
                    <a-select-option v-for="item in ruleOperators" :key="item.value">
                      {{ item.text }}
                    </a-select-option>
                  </a-select>
                </template>
                <!-- 规则的表达式 -->
                <template :slot="'ruleValue'" slot-scope="text, record, index">
                  <div>
                    <a-input style="margin: -5px 0" v-model="text" placeholder="请输入正则表达式"
                      @change="e => handleChange(e.target.value, index, 'ruleValue')" />
                    <!-- <template v-else>
                    {{ text }}
                  </template> -->
                  </div>
                </template>
                <!-- 规则的操作 -->
                <template slot="operation" slot-scope="text, record, index">
                  <div class="editable-row-operations">
                    <!--   <span v-if="record.editable">
                    <a @click="() => save(record.key)">Save</a>
                    <a-popconfirm title="Sure to cancel?" @confirm="() => cancel(record.key)">
                      <a>Cancel</a>
                    </a-popconfirm>
                  </span> -->
                    <span>
                      <a :disabled="ruleList.length <= 1" @click="delRules(index)">删除</a>
                    </span>
                  </div>
                </template>
              </a-table>
            </a-form-model-item>
            <div v-if="!disabled" style="display: flex;flex-direction: row-reverse;">
              <a-button type="dashed" @click="addRules" style=" margin-top: -8px;margin-bottom: 24px;">
                <a-icon type="plus" /> 添加
              </a-button>
            </div>
          </a-col>

        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

import { httpAction } from '@api/manage'
import { getAction } from '@api/manage'
import { assetsCategoryTreeList } from '@/api/api'
import { ValidateOptionalFields, ValidateRequiredFields } from '@/utils/rules'
export default {
  name: 'LocalizationStrategyForm',
  components: {},
  props: {
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    },
    deviceTypes: {
      type: Array,
      default: () => []
    },
    logicTypes: {
      type: Array,
      default: () => []
    },
    ruleOperators: {
      type: Array,
      default: () => []
    },
    ruleTypes: {
      type: Array,
      default: () => []
    },
  },
  data() {
    return {
      formItemLayout: {
        md: { span: 24 },
        sm: { span: 24 }
      },
      formItemLayout1: {
        md: { span: 12 },
        sm: { span: 12 }
      },
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 20 }
      },
      confirmLoading: false,
      validatorRules: {
        policyName: [
          { required: true,validator: (rule, value, callback) =>ValidateRequiredFields(rule, value, callback,'策略名称',15,1)}
        ],
        policyDesc: [
          { required: false, validator: (rule, value, callback) =>ValidateOptionalFields(rule, value, callback,'策略描述',200) }
        ],
        resourceType: [
          { required: true, message: '请选择资源类型!', trigger: 'blur' },
        ],
        resourceCategoryIds: [
          { required: true, message: '请选择资源分类!', trigger: 'change' },
        ],
        tags: [
          { required: true, message: '请选择标签!', trigger: 'blur' },
        ],
        policyMatchBasis: [
          { required: true, message: '请选择匹配基准!', trigger: 'blur' },
          { validator: this.validateRuleLogic }
        ],
        policyMatchRule: [
          { required: true, message: '请填写匹配规则!', trigger: 'blur' },
          { validator: this.validateRules }
        ],
      },
      url: {
        add: '/tags/utlTagAutoPolicy/add',
        edit: '/tags/utlTagAutoPolicy/edit',
        queryById: '/tags/utlTagAutoPolicy/queryById'
      },
      ruleList: [],
      columns: [
        {
          title: '标签',
          dataIndex: 'ruleTag',
          width: 60,
          scopedSlots: { customRender: 'ruleTag' },
        },
        {
          title: '信息类别',
          dataIndex: 'ruleType',
          // width: 120,
          scopedSlots: { customRender: 'ruleType' },
        },
        {
          title: '字段',
          dataIndex: 'ruleKey',
          // width: 120,
          scopedSlots: { customRender: 'ruleKey' },
        },
        {
          title: '模式',
          dataIndex: 'ruleOperator',
          // width:120,
          scopedSlots: { customRender: 'ruleOperator' },
        },
        {
          title: '正则表达式',
          dataIndex: 'ruleValue',
          // width: '20%',
          scopedSlots: { customRender: 'ruleValue' },
        },
        {
          title: '操作',
          dataIndex: 'operation',
          width: 60,
          scopedSlots: { customRender: 'operation' },
        },
      ],
      uppercaseLetters: [],
      categoryTreeData: [],
      tagList: [],
    }
  },
  computed: {

  },
  watch: {
    ruleList: {
      handler(list) {
        this.setLogicValue(this.model.policyMatchBasis)
        this.model.policyMatchRule = this.setRulesData()
      },
      immediate: false,
      deep: true
    },
    confirmLoading(val) {
      this.$emit('loadingHandler', val)
    }
  },
  created() {
    this.getUppercaseLetters();
    this.getCategoryTreeData();
    this.getTagList();
    this.model = this.initModel()
    this.addRules()
  },
  methods: {
    //初始化model
    initModel() {
      return {
        policyName: '',
        resourceType: this.deviceTypes[0]?.value,
        policyMatchBasis: "",
        policyMatchRule: null,
        resourceCategoryIds: [],
        tags: [],
        policyMatchBasisFormulas: "",
        isEnable: true,
        policyDesc: '',
      }
    },
    //添加规则
    addRules() {
      let temTag = ""
      if (this.ruleList.length > 0) {
        temTag = this.ruleList[this.ruleList.length - 1].ruleTag
      }
      this.ruleList.push(
        {
          ruleTag: this.generateRuleTag(temTag),
          ruleType: undefined,
          ruleKey: '',
          ruleOperator: undefined,
          ruleValue: '',
        }
      )
      if (this.ruleList.length === 2) {
        this.model.policyMatchBasis = 'and'
      }
    },
    //delRules 删除规则
    delRules(idx) {
      if (this.ruleList.length > 1) {
        this.$confirm({
          title: '提示',
          content: '是否删除该规则?',
          okText: '确定',
          cancelText: '取消',
          onOk: () => {
            this.ruleList.splice(idx, 1)
            if (this.ruleList.length <= 1) {
              this.model.policyMatchBasis = ''
            }
          }
        })
      }

    },
    //规则内容改变事件
    handleChange(value, key, type) {
      // console.log("value, key, type", value, key, type)
      this.ruleList[key][type] = value
    },
    //设置匹配基准
    setLogicValue(val) {
      if (val === 'self') {
        this.model.policyMatchBasisFormulas = ''
      } else if (val && this.ruleList.length >= 2) {
        this.model.policyMatchBasisFormulas = this.ruleList.map((item, index) => {
          return item.ruleTag
        }).join(' ' + val + ' ')
      } else {
        this.model.policyMatchBasisFormulas = this.ruleList[0] ? this.ruleList[0].ruleTag : ""
      }
    },
    //匹配基准改变事件
    logicChange(val) {
      this.setLogicValue(val)
    },
    //新增测略
    add() {
      this.ruleList = [];
      this.addRules();
      this.model = this.initModel();
      this.edit(this.model)
    },
    //编辑测略
    edit(record) {
      this.$refs.ruleForm.resetFields();

      if (record.id) {
        const temData = {
          policyMatchRule: JSON.parse(record.policyMatchRule),
          resourceCategoryIds: record.resourceCategoryIds.split(','),
          tags: record.tags.split(','),
          isEnable: record.isEnable == 1 ? true : false,
        }
        this.ruleList = Object.values(temData.policyMatchRule)
        Object.assign(this.model, record, temData)
      } else {
        Object.assign(this.model, record)
      }
      this.visible = true
    },
    //校验基准
    validateRuleLogic(rule, value, callback) {
      if (this.model.policyMatchBasis === 'self' && this.model.policyMatchBasisFormulas === '') {
        callback(new Error('请输入自定义表大式'))
        return
      }
      callback()
    },
    //校验规则
    validateRules(rule, value, callback) {
      for (let i = 0; i < this.ruleList.length; i++) {
        if (!this.ruleList[i].ruleType || !this.ruleList[i].ruleKey || !this.ruleList[i].ruleOperator || !this.ruleList[i].ruleValue) {
          callback(new Error(`请完整填写 ${this.ruleList[i].ruleTag} 规则的内容`))
          return
        }
      }
      for (let i = 0; i < this.ruleList.length; i++) {
        if (this.ruleList[i].ruleValue && !this.isRegexLiteralFormat(this.ruleList[i].ruleValue)) {
          callback(new Error(`请正确填写 ${this.ruleList[i].ruleTag} 规则的正则表达式字面量`))
          return
        }
      }
      callback()
    },
    //将规则数据转化为对应格式的对象
    setRulesData() {
      let tem = {}
      this.ruleList.forEach(item => {
        tem[item.ruleTag] = item
      })
      //  console.log("tem", tem)
      return tem
    },
    //提交规则表单
    submitForm() {
      const that = this
      // 触发表单验证
      this.$refs.ruleForm.validate((valid) => {
        if (this.ruleList.length === 0) {
          that.$message.warning('请先制定规则')
          return
        }
        if (valid) {
          let temParams = {
            resourceCategoryIds: this.model.resourceCategoryIds.join(','),
            policyMatchRule: JSON.stringify(this.model.policyMatchRule),
            tags: this.model.tags.join(','),
            isEnable: this.model.isEnable ? 1 : 0,
          }
          let params = Object.assign({}, this.model, temParams)
          // console.log("新增的数据", params)
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          httpAction(httpurl, params, method).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.$emit('ok')
            } else {
              that.$message.warning(res.message)
            }
          }).finally(() => {
            let timer = setTimeout(() => {
              that.confirmLoading = false
              clearTimeout(timer)
              timer = null
            }, 500);
          })
        }
      })
    },
    //大写字母
    getUppercaseLetters() {
      for (let i = 0; i < 26; i++) {
        this.uppercaseLetters.push(String.fromCharCode(65 + i)); // 65 是 'A' 的 ASCII 码
      }
    },
    // 生成规则标签的逻辑
    generateRuleTag(lastTag) {
      if (!lastTag) {
        return 'A';
      }
      let chars = lastTag.split('');
      let index = chars.length - 1;

      while (index >= 0) {
        if (chars[index] !== 'Z') {
          // 将当前字符替换为下一个字母
          chars[index] = String.fromCharCode(chars[index].charCodeAt(0) + 1);
          // 使用 break 语句 wile条件值为真之前停止循环
          break;
        } else {
          // 如果是 Z，将其变为 A，并继续检查前一个字符
          chars[index] = 'A';
          index--;
        }
      }

      // 如果所有字符都是 Z，在前面添加一个 A
      if (index < 0) {
        chars.unshift('A');
      }

      return chars.join('');
    },
    //判断一个字符串是否是合法的正则表达式
    isValidRegex(str) {
      try {
        console.log("str", str)
        // 尝试用传入的字符串创建 RegExp 对象
        new RegExp(str);
        return true;
      } catch (e) {
        console.log("e", e)
        // 若创建过程中抛出异常，说明字符串不是合法的正则表达式
        if (e instanceof SyntaxError) {
          return false;
        }
        // 抛出非 SyntaxError 异常时，重新抛出
        throw e;
      }
    },
    // 判断一个字符串是否符合正则表达式字面量格式
    isRegexLiteralFormat(str) {
      // 用于匹配正则表达式字面量格式的正则
      const regexLiteralPattern = /^\/(?:[^\/\\]|\\.)+\/[gimsuy]*$/;
      return regexLiteralPattern.test(str);
    },
    //获取资源分类数据
    getCategoryTreeData() {
      assetsCategoryTreeList({ i: "all" }).then(res => {
        if (res.success) {
          this.categoryTreeData = res.result || []
        }
      }).catch(err => {

      })
    },
    // 过滤树节点的方法，用于控制树节点的显示和隐藏
    filterTreeNode(inputValue, treeNode) {
      console.log("inputValue", inputValue, treeNode.data.props.title)
      return treeNode.data.props.title.indexOf(inputValue) > -1;
    },
    //获取标签列表
    getTagList() {
      getAction('/utl/taginfo/list', { pageSize: -1, pageNum: 1 }).then(res => {
        if (res.success && res.result && res.result.records) {
          this.tagList = res.result.records.map(item => { return { value: item.tagKey, label: item.tagName } })
        }
      })
    },
  }
}

</script>
<style scoped lang='less'>
.new-button {
  text-align: center;
}

.delete-button {
  height: 40px;
  line-height: 40px;
  margin-left: 12px;
  margin-top: 0px;
}

.instance {
  padding: 10px;
  border-radius: 4px;
  display: flex;
  justify-content: start;
  align-items: center;
  flex-wrap: wrap;
  border: 1px solid #d9d9d9
}

@media (max-width: 575px) {
  .delete-button {
    margin-top: 29px;
  }
}

// 覆盖表格行悬浮颜色
::v-deep .ant-table-tbody>tr:hover>td {
  background-color: transparent !important;
}
</style>