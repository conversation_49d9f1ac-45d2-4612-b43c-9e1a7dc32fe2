d<template>
  <a-spin :spinning="loading">
    <a-card :bordered="false"
      class="core"
      style="width: 100%; flex: auto;" :body-style="{ paddingLeft:'39px' }">
      <!--搜索区域 -->
        <div class="header-wrapper-r">
          <a-form class="header-form">
            <a-input-search v-model="queryParam.keyword"
              placeholder="请输入关键字"
              size="large"
              @search="onSearch">
              <template #enterButton>
                <a-button type="primary"
                  class="btn">
                  <a-icon type="search"></a-icon>
                  搜索</a-button>
              </template>
            </a-input-search>
          </a-form>
        </div>
        <div class="back-icon">
          <!-- 返回按钮 -->
          <div class='header-back'
            style="display: inline-block;width: 30px;">
            <img src='~@assets/return1.png'
              alt=''
              @click='getGo'
              style='width: 20px; height: 20px; cursor: pointer' />
          </div>
        </div>
        <div class='body-wrapper'>
          <div class="body-info-l">
            <a-tabs :defaultActiveKey="0" size="small" @change="changeTab">
              <a-tab-pane key="0">
                <span slot="tab">
                  文&nbsp;&nbsp;本
                </span>
              </a-tab-pane>
              <a-tab-pane key="1">
                <span slot="tab">
                  文&nbsp;&nbsp;档
                </span>
              </a-tab-pane>
            </a-tabs>
            <div v-if="dataSource.length > 0" class="item-wrapper">
              <!-- 知识内容 -->
              <knowledgeItem ref="knowledgeItem" :kkfileview-url='kkfileviewUrl' :dataSource="dataSource" @goDetail="goDetail"></knowledgeItem>

              <a-pagination :current="ipagination.current"
                :hideOnSinglePage="true"
                :defaultPageSize="ipagination.pageSize"
                :total="ipagination.total"
                @change="onChange"
                style="margin-bottom:15px;" />
            </div>
            <!-- 暂无数据 -->
            <div v-if="dataSource.length == 0 && loading == false" style="margin-top:15%">
              <a-list :data-source='[]' />
            </div>
          </div>
          <!-- 热门知识 -->
          <div class="body-info-r">
            <hot-knowledge :theme="'theme3'" @goDetail="goDetail" style="margin-left: 24%;margin-right:16%;"></hot-knowledge>
          </div>
        </div>
    </a-card>
  </a-spin>
</template>

<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { PLATFORM_TYPE,RESET_MENUS } from '@/store/mutation-types'
import { generateBigscreenRouter, generateIndexRouter } from '@/utils/util'
import store from '@/store'
import router from '@/router'
import { mapActions } from 'vuex'
import hotKnowledge from './modules/hotKnowledge'
import knowledgeItem from './modules/knowledgeItem'
import { knowledgeSearchAttachmentPreviewMixins } from './modules/knowledgeSearchAttachmentPreviewMixins'
export default {
  name: 'knowledgeSearchResult',
  mixins: [JeecgListMixin, knowledgeSearchAttachmentPreviewMixins],
  components:{
    hotKnowledge,
    knowledgeItem
  },
  props: {
    data: {
      type: Object,
      required: false,
      default: {}
    },
    //记录返回到运维工作台的路由信息
    backRouteInfo:{
      type:Object,
      required :false,
      default: null
    }
  },
  data() {
    return {
      url: {
        list: '/kbase/knowledges/search',
      },
      queryParam: {
        knowledgeType: 0
      },
      disableMixinCreated: true,
      kkfileviewUrl: ''
    }
  },
  activated() {
    if (this.kkfileviewUrl === '') {
      this.getKkfileviewURL().then((res)=>{
        this.kkfileviewUrl = res
        this.getlist(1);
      })
    } else {
      this.getlist(1);
    }
  },
  watch: {
    data: {
      handler(val) {
        if (val.value) {
          this.queryParam.keyword = val.value;
        }
      },
      deep: true,
      immediate: true,
    }
  },
  methods: {
    getlist(arg) {
      if (!this.queryParam.keyword.trim()) {
        return false
      }
      this.loadData(arg);
    },
    onSearch() {
      this.getlist(1);
    },
    onChange(number) {
      this.ipagination.current = number;
      this.getlist();
    },
    ...mapActions(['GetMenuPermissions']),
    //返回上一级
    getGo() {
      //路由返回运维工作台
      if(this.backRouteInfo){
        let routerInfo={
          redirect:this.backRouteInfo.routePath,
          routerName:this.backRouteInfo.routeName,
          params:null
        }
        this.jumpRouter(routerInfo)
      }else {
        this.$parent.pButton1(1)
      }
    },
    /**获取菜单权限，拥有权限后，跳至运维工作台*/
    jumpRouter(routerInfo) {
      let that = this
      let params = {
        url: routerInfo.redirect,
        //component: routerInfo.component,
      }
      that
        .GetMenuPermissions(params)
        .then((res) => {
          if (res.success) {
            sessionStorage.setItem(PLATFORM_TYPE, res.platformType)
            const menuData = res.menu
            let constRoutes = []
            if (res.platformType === 4|| res.platformType === 8) {
              constRoutes = generateBigscreenRouter(menuData)
            } else {
              constRoutes = generateIndexRouter(menuData)
            }
            // 添加主界面路由
            store
              .dispatch('UpdateAppRouter', {
                constRoutes,
              })
              .then(() => {
                // 根据roles权限生成可访问的路由表
                // 动态添加可访问路由表
                router.addRoutes(store.getters.addRouters)
                that.$store.commit(RESET_MENUS,true)
                that.$router.replace({
                  name: routerInfo.routerName,
                  path: routerInfo.redirect
                })
              })
          } else {
            alert(res.message)
          }
        })
        .catch((err) => {
          alert(err.message)
        })
    },
    changeTab(e) {
      this.queryParam.knowledgeType = e
      this.getlist(1);
    },
    // 跳转知识详情
    goDetail(item) {
      this.$parent.pButton2(2, item);
    }
  }
}

</script>
<style scoped lang="less">
@color: #1890FF;
.back-icon {
  position: absolute;
  right: 15px;
  top: 20px;
}
.header-wrapper-r {
  width: 100%;
  display: flex;
  .header-form {
    width: 63%;
    margin-bottom: 10px;
  }
}

.body-wrapper {
  display: flex;
  justify-content: space-between;
  width: 100%;
  height: 100%;

  .body-info-l {
    width: 63%;
    height: calc(100vh - 195px);
    overflow: auto;
  }
  .body-info-r {
     width: 37%;
  }
}
/****搜索框 样式 ****/
/deep/ .ant-input  {
  border-color: #d9d9d9;
  height: 50px !important;
}

/deep/ .ant-input:hover,
/deep/ .ant-input:focus {
  border-color: @color !important;
}

/deep/ .ant-btn,
/deep/ .ant-btn:hover,
/deep/ .ant-btn:focus {
  height: 50px !important;
  padding: 0 30px 0 30px !important;
  background-color: @color !important;
  border-color: @color !important;
}

/****tab 样式 ****/
/deep/ .ant-tabs-tab {
  padding: 5px 22px !important;
  margin-right: 10px !important;
  font-size: 15px;
}
/deep/ .ant-tabs-tab-active {
  color: @color !important;
}
/deep/ .ant-tabs-ink-bar {
  background-color: @color;
}

/deep/ .ant-tabs-bar {
  border-bottom: none !important
}

::-webkit-scrollbar {
  display: none;
  /*隐藏滚动条*/
}
</style>
