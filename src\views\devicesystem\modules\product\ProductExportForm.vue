<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container>
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-item label="产品模板" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-select show-search optionFilterProp="children" allowClear option-label-prop="label"
                v-decorator="['productId', validatorRules.productId]" placeholder="请选择产品模板" ref='productModel'>
                <a-select-option v-for="item in productList" :value="item.id" :key="item.id" :label="item.displayName">
                  {{ item.displayName }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
  import {
    getAction,
    downFile

  } from '@/api/manage'
  import pick from 'lodash.pick'
  import Vue from 'vue'

  export default {
    name: 'ProductCopyForm',
    components: {},
    props: {
      // 流程表单data
      formData: {
        type: Object,
        default: () => {},
        required: false,
      },
      // 表单模式：true流程表单 false普通表单
      formBpm: {
        type: Boolean,
        default: false,
        required: false,
      },
      // 表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false,
      },
    },
    data() {
      return {
        form: this.$form.createForm(this),
        productList: [],
        model: {},
        labelCol: {
          xs: {
            span: 24,
          },
          sm: {
            span: 5,
          },
        },
        wrapperCol: {
          xs: {
            span: 24,
          },
          sm: {
            span: 16,
          },
        },
        confirmLoading: false,
        validatorRules: {
          productId: {
            rules: [{
              required: true,
              message: '请选择产品模板',
            }, ],
          },
        },
        url: {
          exportProduct: '/product/product/exportProduct',
          productList: '/product/product/productList'
        },
      }
    },
    created() {},
    methods: {
      init() {
        console.log("init ProductExportForm.vue");
        this.getProductList();
        this.form.resetFields()
        this.visible = true
        this.$nextTick(() => {
          this.form.setFieldsValue(
            pick(
              'productId'
            )
          )
        })
      },
      getProductList() {
        getAction(this.url.productList)
          .then((res) => {
            this.productList = res.result
          });
      },
      submitForm() {
        // console.log("submitForm ProductExportForm.vue");
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.$emit('loading', true)
            downFile(this.url.exportProduct, values).then((response) => {
              let nameStr = response.headers['file-name'];
              let data = response.data;
              if (!data || data.size === 0) {
                Vue.prototype['$message'].warning('文件下载失败')
                return
              }
              if (typeof window.navigator.msSaveBlob !== 'undefined') {
                window.navigator.msSaveBlob(new Blob([data]), nameStr)
              } else {
                let url = window.URL.createObjectURL(new Blob([data]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', nameStr)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
              }
            }).finally(() => {
              that.$emit('loading', false)
            })
          }
        })
      },
    }
  }
</script>
<style lang='less' scoped>
  @import '~@assets/less/scroll.less';

  ::v-deep .two-words>div>label {
    letter-spacing: 4px;
  }

  ::v-deep .two-words>div>label::after {
    letter-spacing: 0px;
  }

  ::v-deep .ant-upload-list-item-info>span {
    display: flex;
    justify-content: center;
  }

  ::v-deep .ant-upload-list-picture-card .ant-upload-list-item-thumbnail,
  ::v-deep .ant-upload-list-picture-card .ant-upload-list-item-thumbnail img {
    position: relative;
    display: flex;
    width: 90%;
    height: auto;
    left: 0px;
    top: 0px !important;
    align-items: center;
    justify-content: center;
  }

  .jobChange {
    background-color: #f5f5f5;
  }
</style>