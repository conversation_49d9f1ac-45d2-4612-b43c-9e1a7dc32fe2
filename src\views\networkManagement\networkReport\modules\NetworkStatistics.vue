<template>
  <div>
    <device-statistic
      :device-info='deviceInfo'
      :show-sync='true'
      @executeSync='getDeviceInfoMap(true)'>
    </device-statistic>
<!--    <a-row :gutter='[12,12]' class='row-example'>
      <a-col class='gutter-row' :lg='6' :md='12' :xs='24' v-for='(item,index) in deviceInfo' :key='index'>
        <div class='gutter-box'>
          <a-tooltip title='同步' class='sync-img' v-if='item.showSync'>
            <a-icon type='swap' width='3em' height='3em' @click='getDeviceInfoMap(true)' />
          </a-tooltip>
          <div class='gutter-box-left'>
            <div class='gutter-box-left-top'>
              <span>{{ item.count }}</span>
              <span>台</span>
            </div>
            <div class='gutter-box-left-bottom'>
              <div class='dian'></div>
              {{ item.type }}
            </div>
          </div>
          <div class='gutter-box-right'>
            <img v-if='item.img' :src='item.img' alt='' />
            <a-icon v-if='item.icon' :type='item.icon' class='icon'/>
          </div>
        </div>
      </a-col>
    </a-row>-->
  </div>
</template>

<script>
import {getAction} from '@/api/manage'
import deviceStatistic from '../../../devicesystem/modules/DeviceStatistic.vue'
import device from '@/utils/device'
export default {
  name: 'NetworkStatistics',
  components:{deviceStatistic},
  data() {
    return {
      description: '网络设备统计',
      deviceInfo: [
        {
          type: '全部设备',
          count: '0',
          img: require('@/assets/01.png')
        },
        {
          type: '在线设备',
          count: '0',
          img: require('@/assets/02.png')
        },
        {
          type: '告警设备',
          count: '0',
          icon:'warning'
        },
        {
          type: '未启用设备',
          count: '0',
          img: require('@/assets/04.png'),
        }],
      dictCode :'Network',
      url: {
        deviceInfoUrl: '/net/device/overviewAll',
      }
    }
  },
  created() {
    this.getDeviceInfoMap()
  },

  methods: {
    getDeviceInfoMap(flag = false) {
      getAction(this.url.deviceInfoUrl,{dictCode  : this.dictCode}).then((res) => {
        if (res.success) {
          this.deviceInfo[0].count = res.result.total
          this.deviceInfo[1].count = res.result.onLine
          this.deviceInfo[2].count = res.result.alarm
          this.deviceInfo[3].count = res.result.disEnable
          if (flag) {
            this.$message.success('同步成功')
          }
        }
      }).catch((err)=>{
        this.$message.warning(err.message)
      })
    }
  }
}
</script>
<style lang='less' scoped>
  .row-example> :first-child>.gutter-box{
    background-color: #aecbff;
    /* 不支持线性的时候显示 */
    background-image: linear-gradient(to right, #aecbff, #5d95fc);
  }
  .row-example> :nth-child(2)>.gutter-box{
      background-color: #c3fcc0;
      background-image: linear-gradient(to right, #c3fcc0, #29ca7f);
  }
  .row-example> :nth-child(3)>.gutter-box{
      background-color: #fdbac5;
      background-image: linear-gradient(to right, #fdbac5, #f44967);
  }
  .row-example> :last-child>.gutter-box{
      background-color: #b8eaff;
      background-image: linear-gradient(to right, #b8eaff, #25a8df);
  }

  .gutter-row {
    .gutter-box {
      position: relative;
      border-radius: 3px;
      padding: 5px 5px 5px 0px;
      box-shadow: 2px 2px 4px 1px rgba(0, 0, 0, 0.2);
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-flow: row nowrap;

      .gutter-box-left {
        padding: 12px 20px;
        overflow: hidden;

        .gutter-box-left-top {
          display: flex;
          align-items: flex-end;

          span:nth-child(1) {
            font-family: 'Eurostile-Bold';
            font-size: 0.4rem
            /* 32/80 */;
            color: #ffffff;
            overflow: hidden;
            display: inline-block;
            white-space: nowrap;
            text-overflow: ellipsis;
          }

          span:nth-child(2) {
            font-family: 'HYk2gj';
            font-size: 14px;
            color: rgba(255, 255, 255, 0.85);
            line-height: 38px;
            margin-left: 11px;
          }
        }

        .gutter-box-left-bottom {
          font-family: 'Microsoft YaHei';
          font-size: 0.2rem;
          color: rgba(255, 255, 255, 0.85);
          display: flex;
          align-items: center;
          white-space: nowrap;
          justify-content: start;
          text-align: start;

          .dian {
            width: 6px;
            height: 6px;
            background: #fff;
            margin-right: 8px;
          }
        }
      }

      .gutter-box-right {
        position: absolute;
        right: 5px;
        bottom: 5px;
        //align-self: end;

        img {
          width: 74px;
          height: 74px;
          opacity: 0.2;
        }
        .icon{
          font-size: 70px;
          color:rgba(255,255,255,0.15);
        }
      }
    }
  }

  .sync-img {
    position: absolute;
    top: 12px;
    right: 12px;
    cursor: pointer;
    color: #fff;
    z-index: 100;
  }
</style>