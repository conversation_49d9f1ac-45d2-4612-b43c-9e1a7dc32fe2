<template>
  <div class="bpmn-box">
    <bpmn-header ref="bpmnHeader" v-if="!isView"></bpmn-header>
    <!-- <div>{{testcom}}</div> -->
    <div class="bpmn-main" :class="{ 'bpmn-main-view': isView }">
      <div class="bpmn-content with-diagram" ref="jsDropZone" id="js-drop-zone">
        <div class="canvas" id="js-canvas" ref="canvas"></div>
        <!-- <div class="properties-panel-parent" id="js-properties-panel"></div> -->
      </div>
      <div class="bpmn-aside">
        <bpmn-panel
          ref="_bpmnPanel"
          v-if="bpmnModeler"
          :modeler="bpmnModeler"
          :processCategory="processCategory"
          :businessList="businessList"
          :taskCategory="taskCategory"
          :processInfo="modelData"
          @setProcessInfo="setProcessInfo"
        >
        </bpmn-panel>
      </div>
    </div>
  </div>
</template>
 <script>
// 引入相关的依赖
import BpmnModeler from 'bpmn-js/lib/Modeler'
import translate from './translate/index' //汉译
// 自定义左侧菜单（修改 默认任务 为 用户任务）
import CustomPaletteProvider from './modules/bpmn-js/palette'
//自定义元素选中时的弹出菜单（修改 默认任务 为 用户任务）
import CustomContentPadProvider from './modules/bpmn-js/context-pad'
// 引入flowable的节点文件
import flowableModdle from './resources/flowable.json'
import newXml from './resources/newDiagram.js' // 这里是直接引用了xml字符串
import BpmnHeader from './modules/bpmn-header.vue'
import BpmnPanel from './modules/BpmnPanel'
import { initDictOptions } from '@/components/dict/JDictSelectUtil'
import { getAction } from '@/api/manage'
export default {
  name: 'bpmn',
  props: {},
  components: { BpmnHeader, BpmnPanel },
  data() {
    return {
      // bpmn建模器
      bpmnModeler: null,
      container: null,
      canvas: null,
      process: {
        xml: '',
        svg: '',
      },
      idTest: /^[a-z_][\w-.]*$/i,
      processCategory: [],
      businessList: [],
      taskCategory: [],
      modelData: {},
      isView: false,
      isDeployed: false,
      commandIdx: -1,
      commandLen: 0,
      commandAction: '',
    }
  },
  // 生命周期 - 创建完成（可以访问当前this实例）
  computed: {},
  created() {},
  // 生命周期 - 载入后, Vue 实例挂载到实际的 DOM 操作完成，一般在该过程进行 Ajax 交互
  mounted() {
    this.getCategory()
    //this.getBusinessList()
    if (this.$route.query && this.$route.query.id) {
      this.getModelData()
    } else {
      this.init()
    }
    //新版本bpmn
    // let a = document.getElementById('js-drop-zone')
    // this.registerFileDrop(a, this.createNewDiagram)
  },

  methods: {
    setProcessInfo(key, val) {
      this.modelData[key] = val;
      console.log("改变了",this.modelData)

    },
    getModelData() {
      getAction('/flowable/model/queryById', { id: this.$route.query.id }).then((res) => {
        if (res.success) {
          let data = res.result
          this.modelData.id = data.id
          this.modelData.editor = data.editor
          this.modelData.key = data.key
          this.modelData.name = data.name
          this.modelData.category = data.category
          this.modelData.description = data.description
          this.modelData.tenantId = data.tenantId
          // this.show = true
          // this.isView = data.deployed
          this.isDeployed = data.deployed
          this.init()
        }
      })
    },
    init() {
      // 获取到属性ref为“canvas”的dom节点
      const canvas = this.$refs.canvas
      // 建模
      this.bpmnModeler = new BpmnModeler({
        container: canvas,
        //添加控制板
        // propertiesPanel: {
        //   parent: '#js-properties-panel',
        // },
        additionalModules: [
          CustomPaletteProvider,
          CustomContentPadProvider,
          {
            translate: ['value', translate],
          },
        ],
        moddleExtensions: {
          activiti: flowableModdle,
          // camunda: camundaModdleDescriptor,
        },
      })
      this.addModelerListener()
      this.createNewDiagram(this.modelData.editor)
      this.removeLogo()
    },
    //获取流程类型 任务类型字典值
    getCategory() {
      initDictOptions('bpm_process_type').then((res) => {
        if (res.success) {
          this.processCategory = res.result
        }
      })
      initDictOptions('bpm_task_category').then((res) => {
        if (res.success) {
          this.taskCategory = res.result
        }
      })
    },
    getBusinessList() {
      this.businessList = []
      getAction('/business/info/queryList', { categoryId: 'business' })
        .then((res) => {
          if (res.success) {
            this.businessList = res.result
          } else {
            this.$message.warning(res.message)
          }
        })
        .finally(() => {
          this.confirmLoading = false
        })
    },
    // 删除 bpmn logo
    removeLogo() {
      const Logo = document.querySelector('.bjs-powered-by')
      while (Logo.firstChild) {
        Logo.removeChild(Logo.firstChild)
      }
    },
    createNewDiagram(xml) {
      if (xml) {
        this.process.xml = xml
      } else {
        // 初始化XML文本
        this.process.xml = newXml(
          this.modelData.key,
          this.modelData.name,
          this.modelData.category,
          this.modelData.description
        )
      }
      try {
        this.bpmnModeler.importXML(this.process.xml)
      } catch (err) {
        console.error(err)
      }
    },
    success() {
      // console.log('创建成功!')
    },
    //添加监听
    addModelerListener() {
      //监听命令栈变化
      this.bpmnModeler.on('commandStack.changed', (e) => {
        let cst = this.bpmnModeler.get('commandStack')
        console.log('**&&&^^%% =  ', cst)
        if (this.commandAction === 'undo') {
          //执行撤回导致命令栈变化
          let actionObj = cst._stack[cst._stackIdx + 1]
          this.commandStackDo(actionObj)
          //是否是面板添加属性命令
        } else if (this.commandAction === 'redo') {
          //执行重做导致命令栈变化
          let actionObj = cst._stack[cst._stackIdx]
          this.commandStackDo(actionObj)
        }
        this.commandLen = cst._stack.length
        this.commandIdx = cst._stackIdx
        this.commandAction = ''
      })
      // this.bpmnModeler.on('element.changed', (event) => {
      //   const element = event.element
      //   console.log("监听到element变化乐乐了 === ",event)
      //   // the element was changed by the user
      // })
    },
    commandStackDo(actionObj) {
      if (actionObj.command === 'element.updateProperties') {
        let actionElement = actionObj.context.element
        if (actionElement.id === this.$refs._bpmnPanel.node.id) {
          let panelComponent = this.$refs._bpmnPanel.$refs._component
          if (panelComponent) {
            panelComponent.updateActiveName(actionObj)
            panelComponent.initFormData()
            if (panelComponent.$refs.listenerTable) {
              panelComponent.$refs.listenerTable.init()
            }
            if (panelComponent.$refs.propertiesTable) {
              panelComponent.$refs.propertiesTable.init()
            }
            if (panelComponent.$refs.taskListenerTable) {
              panelComponent.$refs.taskListenerTable.init()
            }
            if (panelComponent.$refs.multiInstance) {
              panelComponent.$refs.multiInstance.init()
            }
          }
        } else {
          // this.$refs._bpmnPanel.node = actionElement;
          this.$store.commit('SET_COMMANSTACKOBJ', actionObj)
          let selections = this.bpmnModeler.get('selection')
          selections.select(actionElement)
        }
      } else if (actionObj.command === 'element.updateLabel') {
        this.$refs._bpmnPanel.node = actionObj.context.element
      } else {
        let elementRegistry = this.bpmnModeler.get('elementRegistry')
        let rootel = elementRegistry.filter((item) => item.type === 'bpmn:Process')
        this.$refs._bpmnPanel.node = rootel[0]
      }
    },
  },
}
</script>

<style scoped lang="less">
.bpmn-box {
  width: calc(100%);
  height: calc(100%);
}
.bpmn-main {
  display: flex;
  width: calc(100%);
  //height: calc(100% - 50px - 16px);
  height: calc(100vh - 59px - 12px - 40px - 12px);
  //height: calc(100vh - 40px);
  background-color: #ffffff;
  .bpmn-aside {
    height: 100%;
    width: 355px;
  }
  .bpmn-content {
    background: url('../../../assets/bg.svg');
    width: calc(100% - 355px);
    height: calc(100%);
    .canvas {
      width: 100%;
      height: 100%;
    }
  }
}
.bpmn-main-view {
  height: calc(100% - 16px);
  .bpmn-aside {
    display: none;
  }
  .bpmn-content {
    width: 100%;
    background: transparent;
    ::v-deep .djs-palette.two-column.open {
      display: none;
    }
  }
}
</style>