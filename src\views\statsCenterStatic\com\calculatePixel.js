 export function widthPixel(w_px) {
    let docEl = document.documentElement,
      clientWidth =
        window.innerWidth ||
        document.documentElement.clientWidth ||
        document.body.clientWidth
    if (!clientWidth) return
   // 此处的1920为设计稿的宽度，记得修改！
   let w = clientWidth>1366?(clientWidth / 1920):(1366 / 1920)
   return w_px * w
  }
 export function heightPixel(h_px) {
   let docEl = document.documentElement,
     clientHeight =
       window.innerHeight ||
       document.documentElement.clientHeight ||
       document.body.clientHeight
   if (!clientHeight) return
   // 此处的1080为设计稿的高度，记得修改！
   let h = clientHeight>768?(clientHeight / 1080):(768 / 1080)
   return h_px * h
 }
