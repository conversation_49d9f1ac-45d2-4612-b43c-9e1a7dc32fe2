<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <!-- 查询区域 -->
      <a-card :bodyStyle="{ paddingBottom: '0' }" class="card-style">
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="资产类型">
                  <j-tree-select-expand v-model="queryParam.assetsCategoryId" show-search treeNodeFilterProp="title"
                                        placeholder="请选择资产类型" dict="cmdb_assets_category,category_name,id"
                                        pidField="parent_id"
                                        condition='{"delflag":0,"category_state":"0"}' pidValue="0"
                                        @change="changeAssetsCategorySelected($event)" />
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="资产名称">
                  <a-input placeholder="请输入资产名称" :maxLength="maxLength"  :allowClear="true" autocomplete="off"
                           v-model="queryParam.assetsName"></a-input>
                </a-form-item>
              </a-col>

              <a-col :span="spanValue">
                <a-form-item label="供应商">
                  <a-input placeholder="请输入供应商" :maxLength="maxLength" :allowClear="true" autocomplete="off"
                           v-model="queryParam.producerName"></a-input>
                </a-form-item>
              </a-col>
              <a-col v-show="toggleSearchStatus" :span="spanValue" v-for="aq in addQuerys" :key="aq.id">
                <a-form-item :label="aq.name" v-if="aq.dictType">
                  <j-dict-select-tag v-model="queryParam[aq.id]" :placeholder="'请选择' + aq.name"
                                     :dictCode="aq.dictType" />
                </a-form-item>
                <a-form-item :label="aq.name" v-else-if="aq.type == '计数器'">
                  <a-input v-model="queryParam[aq.id]" :maxLength="maxLength" :placeholder="'请输入' + aq.name" type="number" />
                </a-form-item>
                <a-form-item :label="aq.name" v-else>
                  <j-input :placeholder="'请输入' + aq.name" :maxLength="maxLength" :allowClear="true" v-model="queryParam[aq.id]" />
                </a-form-item>
              </a-col>
              <a-col v-show="toggleSearchStatus" :span="spanValue">
                <a-form-item label='合同名称'>
                  <a-select
                    v-model="queryParam.contractId"
                    show-search
                    :allow-clear='true'
                    placeholder="请选择合同名称"
                  >
                    <a-select-option v-for='item in contractList' :key='"contract_" + item.id' :label='item.name'
                                    :value='item.id'>
                      {{ item.name }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="colBtnsSpan()">
                      <span class="table-page-search-submitButtons"
                            :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                        <a-button type="primary" @click="selfSearchQuery">查询</a-button>
                        <a-button @click="searchReset" style="margin-left: 10px">重置</a-button>
                        <a v-if="isVisible" class="btn-updown-style" @click="doToggleSearch">
                          {{ toggleSearchStatus ? '收起' : '展开' }}
                          <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                        </a>
                      </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <!-- 查询区域-END -->
      <a-card :bordered="false" style="flex: auto">
        <!-- 操作按钮区域 -->
        <div class="table-operator">
          <a-button @click="handleAdd" v-has="'assets:add'">新增</a-button>
          <a-button @click="selfExport" v-has="'assets:export'">导出</a-button>
          <!-- <a-button @click="handleTemplateXls()" v-has="'assets:import'">下载模版</a-button> -->
          <a-button @click="downAssetTemplate" v-has="'assets:import'">下载模版</a-button>
          <a-upload v-has="'assets:import'" name="file" :showUploadList="false" :multiple="false"
                    :headers="tokenHeader" :action="importExcelUrl" @change="backendImportData($event,tips)">
            <a-button>导入</a-button>
          </a-upload>
          <a-dropdown v-if="selectedRowKeys.length > 0" v-has="'assets:delete'">
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item key="1" @click="batchDel"> 删 除</a-menu-item>
              <a-menu-item @click="batchChangeStatus()"> 编 辑</a-menu-item>
              <a-menu-item @click="handlePdfExport()">导出铭牌</a-menu-item>
            </a-menu>
            <a-button>
              批量操作
              <a-icon type="down" />
            </a-button>
          </a-dropdown>
        </div>
        <!-- table区域-begin -->
        <a-table
          ref="table"
          bordered
          rowKey="id"
          :columns="columns"
          :dataSource="dataSource"
          :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
          :pagination="ipagination"
          :loading="loading" :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          @change="handleTableChange">
          <template slot="htmlSlot" slot-scope="text">
            <div v-html="text"></div>
          </template>
          <template slot="imgSlot" slot-scope="text">
            <span v-if="!text" style="font-size: 14px">无图片</span>
            <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width: 80px; font-size: 14px" />
          </template>
          <template slot="fileSlot" slot-scope="text">
            <span v-if="!text" style="font-size: 14px">无文件</span>
            <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
              下载
            </a-button>
          </template>
          <span slot="action" slot-scope="text, record">
            <a @click="handleDetailPage(record)">查看</a>
            <a-divider type='vertical' />
            <a-dropdown>
              <a class='ant-dropdown-link'>更多<a-icon type='down' /></a>
              <a-menu slot='overlay'>
                <a-menu-item>
                  <span v-has="'assets:edit'">
                    <a @click="handleEdit(record)">编辑</a>
                  </span>
                </a-menu-item>
                <a-menu-item>
                  <span v-has="'assets:delete'">
                    <a @click="deleteRecord(record)">删除</a>
                  </span>
                </a-menu-item>
                <a-menu-item>
                  <span  >
                    <a @click="handlePdfExport(record)">导出铭牌</a>
                  </span>
                </a-menu-item>
              </a-menu>
            </a-dropdown>
          </span>
          <template slot="tooltip" slot-scope="text">
            <a-tooltip placement="topLeft" :title="text" trigger="hover">
              <div class="tooltip">{{ text }}</div>
            </a-tooltip>
          </template>
        </a-table>
        <!-- table区域-END -->

        <assets-modal ref="modalForm" @ok="modalFormOk"></assets-modal>
        <assets-change-modal ref="AssetsStatusChange" @ok="modalFormOk"></assets-change-modal>
        <a-modal title="选择资产信息模板类型" :visible="showAssets" :footer="null" destroyOnClose
                 @cancel="showAssets = false">
          <div class="assets-info-modoule">
            <device-tree-expand ref="deviceTree" :inputFlag="true"
                                :treeUrl="'/assetscategory/assetsCategory/selectAssetsCategoryTree'" :params="params"
                                @selected="handleTemplateXls" :btnName="'通用模板'" :is-show-all-btn="true"
                                :is-show-btn-icon="false"
                                :is-show-icon="false"></device-tree-expand>
          </div>
        </a-modal>
        <!-- 下载模版 -->
        <iframe id="download" style="display: none"></iframe>
        <ExportPdfModal ref="exportPdfModal" @setRowKeys="setRowKeys"></ExportPdfModal>
      </a-card>
    </a-col>
  </a-row>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import AssetsModal from './modules/AssetsModal'
import AssetsStatus from './modules/AssetsStatus'
import AssetsChangeModal from './modules/AssetsChangeModal'
import JSuperQuery from '@/components/jeecg/JSuperQuery.vue'
import { filterDictTextByCache } from '@/components/dict/JDictSelectUtil'
import { deleteAction, getAction, postAction, putAction, httpAction } from '@/api/manage'
//引入公共devicetree组件
import DeviceTreeExpand from '@/components/tree/DeviceTreeExpand.vue'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
import ExportPdfModal from './ExportPdfModal'

export default {
  name: 'AssetsList',
  mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
  components: {
    AssetsModal,
    AssetsChangeModal,
    JSuperQuery,
    AssetsStatus,
    DeviceTreeExpand, ExportPdfModal
  },
  data() {
    return {
      maxLength:50,
      tips:"后台服务正在处理，稍后可前往下载管理页面下载对应文件查看信息。当所有数据无误时，导入成功，刷新页面可更新资产信息列表",
      // wuhu: false,
      description: '资产表管理页面',
      // 表头
      columns: [],
      baseColumns: [
        {
          title: '资产编号',
          dataIndex: 'assetsCode',
          customCell: () => {
            let cellStyle = 'text-align: center'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '资产名称',
          dataIndex: 'assetsName',
          customCell: () => {
            let cellStyle = 'text-align: center'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '资产类型',
          dataIndex: 'assetsCategoryText',
          customCell: () => {
            let cellStyle = 'text-align: center'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '供应商',
          dataIndex: 'producerName',
          customCell: () => {
            let cellStyle = 'text-align: center'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '型号',
          dataIndex: 'assetsModel',
          customCell: () => {
            let cellStyle = 'text-align: center'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '资产状态',
          dataIndex: 'statusName',
          customCell: () => {
            let cellStyle = 'text-align: center'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '入库日期',
          dataIndex: 'storageTime',
          customRender: function(text) {
            return !text ? '' : text.length > 10 ? text.substr(0, 10) : text
          },
          customCell: () => {
            let cellStyle = 'text-align: center'
            return {
              style: cellStyle
            }
          },
        },
        {
          title: '关联合同',
          dataIndex: 'contractName',
          scopedSlots: { customRender: 'tooltip' },
          customCell: () => {
            let cellStyle = 'text-align: left;width:100px;max-width:200px;'
            return {
              style: cellStyle,
            }
          }
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 147,
          scopedSlots: {
            customRender: 'action'
          }
        }
      ],
      url: {
        list: '/assets/assets/list1',
        // delete: '/assets/assets/delete',
        // deleteBatch: '/assets/assets/deleteBatch',
        deleteBatch: '/device/deviceInfo/deleteBatchAssets',
        // edit: '/assets/assets/edit',

        exportXlsUrl: '/assets/assets/exportXls',
        importExcelUrl: 'assets/assets/importExcel',
        // findAllByPid: 'category/cmdbAssetsCategory1/findAllByPid',
        // queryLog: '/assets/assets/queryLog',
        // findCodeValue: 'extendField/extendField/findCodeValue',
        // queryFilesByAssetsId: '/assets/assets/queryByIds',
        // queryById: '/assets/assets/queryById',
        // downloadUserTemplateUrl: '/assets/assets/downloadUserTemplate',
        downloadTemplateXlsUrl: 'assets/assets/downloadTemplate',
        contract: '/category/contract/list', // 获取合同列表
        // queryStatus: '/assets/assets/queryStatus',
        // findName: '/assets/assets/findName',
        // updateStatus: '/assets/assets/updateStatus',
        // getStatus: '/assets/assets/= ''',
        // loadTreeData: '/sys/dict/loadTreeData',
      },
      superFieldList: [],
      // isDetail: false, //是否查看详情
      // depName: {},
      // assetsId: '',
      // tableData: [],
      // assetsStatusList: [],
      // collectInfoList: [],
      // filesList: [],
      // filesUrl: window._CONFIG['domianURL'] + '/sys/common/static/',
      params: {
        i: 'categoryId'
      },
      assetsStatus: false,
      // departList: '',
      // sysUsers: '',
      // userId: '',
      // departId: '',
      // assetsStatusName: '',
      // assetsStatusId: '',
      // treeData: [],
      form: this.$form.createForm(this, {
        name: 'coordinated'
      }),
      addColumns: [],
      addQuerys: [],
      showAssets: false,
      assetsCategoryId: '',
      selCateAndAssetsKeys: [],
      contractList: [] // 合同列表
    }
  },
  computed: {
    importExcelUrl: function() {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
    downloadTemplateXlsUrl: function() {
      return `${window._CONFIG['domianURL']}/${this.url.downloadTemplateXlsUrl}`
    }
  },
  created() {
    this.columns = this.baseColumns.slice()
    this.getSuperFieldList()
    this.getContract()
  },
  methods: {
    setRowKeys(){
      this.selectedRowKeys = []
    },
    // 打开PDF导出配置页面
    handlePdfExport: function(data) {
      var ids = ''
      if (data){
        ids=data.id
      }else{
        for (var a = 0; a < this.selectedRowKeys.length; a++) {
          ids += this.selectedRowKeys[a] + ','
        }
      }
      this.$refs.exportPdfModal.add(ids)
    },
    /*打开批量编辑窗口（针对同一资产类型的资产，可批量编辑资产状态、附加字段）*/
    batchChangeStatus() {
      if (this.selectedRowKeys.length <= 0) {
        this.$message.warning('请至少选择一条记录！')
        return
      } else {
        if (this.assetsCategoryId) {
          let ids = this.selectedRowKeys.join(',')
          this.$refs.AssetsStatusChange.edit(ids, this.assetsCategoryId)
          this.$refs.AssetsStatusChange.title = '批量编辑'
          this.$refs.AssetsStatusChange.disableSubmit = false
        } else {
          this.$message.warning('请选择同一资产类型进行批量编辑')
        }
      }
    },
    /*记录选择的列表项，并判断所选项是否为统一资产类型*/
    onSelectChange(selectedRowKeys, selectionRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectionRows = selectionRows
      this.addSelectedCateAndAssetsKeys(selectedRowKeys, selectionRows)
      this.canBatchEdit(this.selCateAndAssetsKeys)
    },
    /*记录选择的列表项的资产类型id及资产id*/
    addSelectedCateAndAssetsKeys(selectedRowKeys, selectionRows) {
      if (this.selCateAndAssetsKeys.length > 0) {
        let newArr = []
        //根据selectedRowKeys，保留已存在的，去除不存在的
        for (let j = 0; j < selectedRowKeys.length; j++) {
          let item = undefined
          let selKeys = this.selCateAndAssetsKeys
          for (let i = 0; i < selKeys.length; i++) {
            if (selKeys[i].id === selectedRowKeys[j]) {
              item = selKeys[i]
            }
          }
          if (item) {
            newArr.push({ assetsCategoryId: item.assetsCategoryId, id: item.id })
          }
        }
        //根据selectionRows，添加不存在的
        for (let j = 0; j < selectionRows.length; j++) {
          let item = undefined
          let selKeys = this.selCateAndAssetsKeys
          for (let i = 0; i < selKeys.length; i++) {
            if (selKeys[i].id === selectionRows[j].id) {
              item = selKeys[i]
            }
          }
          if (!item) {
            newArr.push({ assetsCategoryId: selectionRows[j].assetsCategoryId, id: selectionRows[j].id })
          }
        }
        this.selCateAndAssetsKeys = newArr
      } else {
        for (let i = 0; i < selectionRows.length; i++) {
          this.selCateAndAssetsKeys.push({
            assetsCategoryId: selectionRows[i].assetsCategoryId,
            id: selectionRows[i].id
          })
        }
      }
    },
    /*判断选择的列表项的资产类型id是否相同*/
    canBatchEdit(selKeys) {
      let catId = ''
      if (selKeys.length > 0) {
        catId = selKeys[0].assetsCategoryId
        for (let i = 0; i < selKeys.length; i++) {
          if (selKeys[i].assetsCategoryId !== catId) {
            catId = ''
            break
          }
        }
      }
      this.assetsCategoryId = catId
    },
    downAssetTemplate() {
      this.showAssets = true
    },
    // 自定义导出列表文件
    selfExport() {
      // if (!this.searchKey && this.bsearchKey) {
      //   this.searchKey = this.bsearchKey
      // }
      // if (Array.isArray(this.searchKey)) {
      //   this.queryParam.momgDeptName = this.searchKey.join(',')
      // } else if (typeof this.searchKey === 'string') {
      //   this.queryParam.momgDeptName = this.searchKey
      //   // this.searchKey = ""
      // }
      this.backendExportData('资产表')
    },
    // 自定义查询
    selfSearchQuery(pageReset) {
      this.columns = this.baseColumns.slice()
      this.selectedRowKeys = []
      if (this.addColumns.length > 0) {
        this.addColumns.forEach((el) => {
          this.columns.splice(this.columns.length - 2, 0, el)
        })
      }
      this.loadData(pageReset)
    },
    // 获取列表信息 将要展示的附加字段添加到列表数据中
    loadData(pageReset) {
      if (pageReset) {
        this.ipagination.current = 1
      }
      var params = this.getQueryParams() //查询条件
      if (this.addQuerys) {
        params.extendField = []
        this.addQuerys.forEach((el) => {
          if (params[el.id]) {
            params.extendField.push({
              type: el.type,
              value: params[el.id],
              id: el.id
            })
          }
        })
      }
      postAction(this.url.list + `?pageNo=${params.pageNo}&pageSize=${params.pageSize}`, params).then((res) => {
        if (res.success) {
          let temList = res.result.records || res.result
          // 添加附加字段
          if (temList.length > 0 && this.addColumns.length > 0) {
            temList.forEach((el) => {
              let extendValue = el.extendValue
              if (extendValue) {
                this.addColumns.forEach((addel) => {
                  let tem = extendValue.find((etv) => etv.fieldId === addel.dataIndex)
                  el[addel.dataIndex] = tem ? tem.value || '' : ''
                })
              }
            })
          }
          this.dataSource = temList
          if (this.dataSource.length < 9) {
            this.clientHeight = false
          }
          this.ipagination.total = res.result.total
        }
        if (res.code === 510) {
          this.$message.warning(res.message)
        }
        this.loading = false
      })
    },

    //删除
    deleteRecord(record) {
      if (!this.url.deleteBatch) {
        this.$message.error('请设置url.delete属性!')
        return
      }
      var that = this
      this.$confirm({
        title: '确认删除',
        okText: '是',
        cancelText: '否',
        content: '是否删除选中数据?',
        onOk: function() {
          that.loading = true
          deleteAction(that.url.deleteBatch, {
            ids: record.id
          }).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.loadData()
            } else {
              that.$message.warning(res.message)
              that.loadData()
            }
          })
        }
      })
    },
    //下载excel模板
    handleTemplateXls(e) {
      this.showAssets = false
      let path = this.downloadTemplateXlsUrl
      let idStr = e ? e : ''
      path = path + '?assetsCategoryId=' + idStr
      document.getElementById('download').src = path
    },
    searchReset() {
      // 重置按钮将会将所有的搜索条件清空
      // if (this.queryParam.assetsCategoryId) {
      //   let id = this.queryParam.assetsCategoryId
      //   this.queryParam = {}
      //   this.queryParam.assetsCategoryId = id
      // } else {
      //   this.queryParam = {}
      // }
      this.queryParam = {}
      this.columns = this.baseColumns.slice()
      this.addQuerys = []
      this.addColumns = []
      this.loadData(1)
    },
    changeAssetsCategorySelected(e) {
      /* if (e != undefined) {
         this.queryParam.assetsCategoryId = e
       } else {
         this.queryParam.assetsCategoryId = ''
       }*/
      // 选择资产类型时查看是否有要现在列表的附加字段
      this.addColumns = []
      this.addQuerys = []
      if (!!e) {
        getAction('/extendField/extendField/findCodeName', {
          assetsCategoryId: e.trim()
        }).then((res) => {
          if (!!res) {
            let addCodesList = res.result.filter((el) => el.isShow === 1)
            this.addQuerys = res.result.filter((el) => el.isShow === 1 && el.isQuery === 1)
            addCodesList.forEach((el) => {
              let temColumn = {
                title: el.name,
                dataIndex: el.id,
                align: 'center',
                customRender: (text) => {
                  if (el.dictType) {
                    return filterDictTextByCache(el.dictType, text)
                  } else {
                    return text
                  }
                }
              }
              this.addColumns.push(temColumn)
            })
          }
        })
      }
    },
    /*获取合同列表*/
    getContract() {
      getAction(this.url.contract, { pageSize: -1 }).then((res) => {
        if (res.success) {
          this.contractList = res.result.records
        }
      })
    },
    getSuperFieldList() {
      let fieldList = []
      fieldList.push({
        type: 'string',
        value: 'assetsCode',
        text: '资产编号',
        dictCode: ''
      })
      fieldList.push({
        type: 'string',
        value: 'assetsName',
        text: '资产名称',
        dictCode: ''
      })
      fieldList.push({
        type: 'string',
        value: 'assetsUnique',
        text: '唯一标识符',
        dictCode: ''
      })
      fieldList.push({
        type: 'string',
        value: 'assetsCategoryId',
        text: '资产类型',
        dictCode: ''
      })
      fieldList.push({
        type: 'int',
        value: 'assetsSerial',
        text: '序列号',
        dictCode: ''
      })
      fieldList.push({
        type: 'string',
        value: 'producerName',
        text: '厂商名称',
        dictCode: ''
      })
      fieldList.push({
        type: 'string',
        value: 'assetsModel',
        text: '型号',
        dictCode: ''
      })
      fieldList.push({
        type: 'string',
        value: 'departmentId',
        text: '所属部门',
        dictCode: ''
      })
      fieldList.push({
        type: 'string',
        value: 'ownerId',
        text: '所属人',
        dictCode: ''
      })
      fieldList.push({
        type: 'date',
        value: 'startQualityTime',
        text: '质保开始日期'
      })
      fieldList.push({
        type: 'int',
        value: 'qualityTerm',
        text: '质保期限(月)',
        dictCode: ''
      })
      fieldList.push({
        type: 'string',
        value: 'repairFac',
        text: '保修单位',
        dictCode: ''
      })
      fieldList.push({
        type: 'string',
        value: 'repairPhone',
        text: '保修单位电话',
        dictCode: ''
      })
      fieldList.push({
        type: 'string',
        value: 'warrantyConnect',
        text: '保修联系人',
        dictCode: ''
      })
      fieldList.push({
        type: 'string',
        value: 'warrantyPhone',
        text: '保修联系人电话',
        dictCode: ''
      })
      fieldList.push({
        type: 'string',
        value: 'storageManager',
        text: '入库人',
        dictCode: ''
      })
      fieldList.push({
        type: 'date',
        value: 'storageTime',
        text: '入库日期'
      })
      fieldList.push({
        type: 'string',
        value: 'assetsFile',
        text: '附件',
        dictCode: ''
      })
      fieldList.push({
        type: 'string',
        value: 'file',
        text: '附件Url',
        dictCode: ''
      })
      this.superFieldList = fieldList
    },
    handleDetailPage: function(record) {
      this.$parent.pButton2(1, record)
    }
    /*    /!*获取历史表格*!/
        queryTable() {
          getAction(this.url.queryLog, {
            assetsId: this.assetsId,
          }).then((res) => {
            if (res.success) {
              this.tableData = res.result
            } else {
              this.$message.warning(res.message)
            }
          })
        },*/
    /*   findCodeValueById(record) {
         if (record.assetsCategoryId && record.id) {
           let paramObj = {
             assetsCategoryId: record.assetsCategoryId.trim(),
             assetsId: record.id.trim(),
           }
           getAction(this.url.findCodeValue, paramObj).then((res) => {
             if (!!res) {
               this.collectInfoList = res.result
               // 判断是否有使用数据字典字段  将值转换成文本
               if (this.collectInfoList.length > 0) {
                 this.collectInfoList.forEach((el) => {
                   if (el.dictType) {
                     el.value = filterDictTextByCache(el.dictType, el.value)
                   }
                 })
               }
             }
           })
         }
       },*/
    /*    getDataById(asstesIds) {
          getAction(this.url.queryByIds, {
            id: asstesIds,
          }).then((res) => {
            if (res.success) {
              this.filesList = res.result.fileUrlList
            }
          })
        },*/
    /*    queryStatus() {
          getAction(this.url.queryStatus, {
            assetsId: this.assetsId,
          }).then((res) => {
            if (this.assetsId == '') {
              return
            }
            if (res.success) {
              this.assetsStatusList = res.result
            } else {
              this.$message.warning(res.message)
            }
          })
        },*/
    /*    findName() {
          getAction(this.url.findName, {
            id: this.assetsId,
          }).then((res) => {
            if (res.success) {
              this.depName = res.result
            } else {
              this.$message.warning(res.message)
            }
          })
        }*/
  }
}
</script>
<style lang="less" scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';

/*.body {
  padding-top: 10px;
}*/

/*.ant-modal-body {
  background-color: #eee;
}*/

/*
.div-info {
  position: relative;
  background-color: white;
  border-radius: 3px;
}

.cls-div {
  position: absolute;
  top: 6px;
  right: 11px;
  cursor: pointer;
}

.p-info-title {
  !* line-height: 45px;
 height: 45px;*!
  margin-bottom: 0px;
  font-family: PingFangSC-Medium;
  font-size: 18px;
  color: #000000;
  font-weight: 600;
}

.p-info-product {
  //margin-left: 2em;
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
  display: flex;
}
*/

/*.div-style-1 {
  white-space: nowrap;
  height: 45px;
  line-height: 45px;
}*/

/*.div-style-2 {
  .div-style-1;
  display: inline-flex;
  //justify-content: space-between
}*/

/*.div-style-3 {
  .div-style-1;
  display: inline-block;
}*/

/*.div-style-4 {
  .div-style-1;
  margin-left: 63px;
}*/

/*.span-status {
  margin-left: 12px;
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
}*/

/*.editButton {
  width: 83px;
  height: 33px;
  border: none;
  background-color: rgb(30, 54, 116);
  color: #fff;
  border-radius: 4px;
  margin: 0 10px;
}*/

/*.paifaButton {
  width: 83px;
  height: 33px;
  // border: none;
  // background-color: rgb(30, 54, 116);
  // color: #fff;
  border-radius: 4px;
  !*margin-left: 20px;*!
  margin: 0 8px;
}*/

/*.span-status > img {
  width: 6px;
  height: 6px;
}*/

/*.status {
  padding-left: 7px;
}*/

/*table.gridtable {
  //white-space: nowrap;
  font-family: verdana, arial, sans-serif;
  font-size: 14px;
  color: #606266;
  border-width: 1px;
  border-color: #e8e8e8;
  border-collapse: collapse;
  text-align: left;
  width: 100%;
}*/

/*
table.gridtable td {
  border-width: 1px;
  border-style: solid;
  border-color: #e8e8e8;
}

.leftTd {
  width: 17%;
  background-color: #fafafa;
  padding: 16px 24px;
  text-align: center;
}

.rightTd {
  width: 35%;
  padding: 16px 24px;
}
*/

/*
.relation-btn-div {
  display: flex;
  justify-content: flex-start;
  padding-bottom: 8px;

  .add-btn {
    height: 28px;
    margin-right: 16px;
    background: #ecf5ff;
    border: 1px solid #b3d8ff;
    border-radius: 4px;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #409eff;
  }

  .enlarge-btn {
    height: 28px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #737578;
  }
}
*/

/*.center {
  width: 100%;
  // background: skyblue;
  opacity: 0.5;
  display: flex;
  flex-wrap: wrap;
  margin-top: 40px;

  .item {
    width: 30%;
    padding: 12px 24px 12px 24px;
    display: flex;

    div:nth-child(1) {
      width: 80px;
      text-align: right;
      color: #000;
      font-size: 16px;
      font-weight: normal;
    }

    div:nth-child(2) {
      color: #000;
      font-size: 16px;
      font-weight: normal;
    }
  }
}

.icon-fullscreen {
  position: absolute;
  right: 1%;
  top: 15%;
  z-index: 100000;
}*/

/*.file {
  font-size: 14px;
}*/

/*/deep/ .ant-descriptions-item-label {
  text-align: center;
}

/deep/ .body-top-card {
  margin-bottom: 12px;

  .ant-card-body {
    height: 80px !important;

    .table-page-search-submitButtons {
      button:nth-child(1):hover {
        background: #1e3674;
        border-color: #1e3674;
        color: #fff !important;
      }
    }
  }
}

/deep/ .ant-radio-checked .ant-radio-inner {
  border-color: #409eff !important;
}

/deep/ .ant-radio-inner:after {
  background-color: #409eff !important;
}

/deep/ .table-operator {
  .ant-btn:hover,
  .ant-btn:focus {
    border-color: #409eff !important;
    color: #409eff !important;
  }
}

/deep/ .ant-checkbox-checked .ant-checkbox-inner {
  background-color: #409eff !important;
  border-color: #409eff !important;
}

/deep/ .ant-checkbox-indeterminate .ant-checkbox-inner:after {
  background-color: #409eff !important;
}

/deep/ .ant-modal-footer {
  div {

    // button:nth-child(1) {
    //   border: 2px solid #041e61 !important;
    //   color: #041e61 !important;
    // }
    button:nth-child(2):hover {
      color: #fff !important;
      border: 1px solid #041e61 !important;
      width: none !important;
    }
  }
}

/deep/ .ant-tabs-nav .ant-tabs-tab:hover {
  color: #409eff !important;
}

/deep/ .ant-tabs-nav .ant-tabs-tab-active {
  color: #409eff !important;
}

/deep/ .ant-tabs-ink-bar {
  background-color: #409eff !important;
}*/

/*.additional {
  margin-top: 18px;
  width: 100%;

  .title {
    color: rgba(0, 0, 0, 0.85);
    font-weight: bold;
    font-size: 16px;
    line-height: 1.5;
  }

  .additional-table {
    width: 100%;
    margin-top: 0.25rem
    !* 20/80 *!;
    display: flex;
    flex-wrap: wrap;
    font-family: verdana, arial, sans-serif;
    font-size: 14px;
    color: #606266;
    border-width: 1px;
    border-color: #e8e8e8;
    border-collapse: collapse;
    text-align: left;

    .additional-table-item {
      width: 52%;
      display: flex;
      border: 1px solid #d7d7d7;

      span {
        display: flex;

        background-color: #fafafa;
        padding: 16px 24px;
      }

      span:nth-child(1) {
        width: 33%;
        justify-content: center;
        border-right: 2px solid #e8e8e8;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      span:nth-child(2) {
        width: 66%;
        background: #fff;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }

    div:nth-child(2n + 0) {
      width: 46%;

      span:nth-child(1) {
        width: 39%;
        background: #fff;
      }
    }
  }
}*/

/*::v-deep .ant-table-thead > tr > th {
  text-align: center;
  white-space: nowrap;
}*/

/*内容对齐方式、省略显示*/
/*::v-deep .ant-table-tbody > tr > td {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}*/

.assets-info-modoule {
  height: 50vh;
  // overflow: scroll;
}
</style>