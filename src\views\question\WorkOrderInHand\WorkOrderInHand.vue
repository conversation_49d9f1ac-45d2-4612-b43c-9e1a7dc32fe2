<template>
  <div style="height:100%">
    <keep-alive>
      <component
        :is="pageName"
        :data="data"
      />
    </keep-alive>
  </div>
</template>
<script>
import WorkOrderInHandList from './WorkOrderInHandList'
import WorkOrderInHandForm from './WorkOrderInHandForm'
export default {
  name: 'DevopsOrderInfoManage',
  data() {
    return {
      isActive: 0,
      data: {}
    }
  },
  components: {
    WorkOrderInHandList,
    WorkOrderInHandForm
  },
  created() {
    this.pButton1(0)
  },
  //使用计算属性
  computed: {
    pageName() {
      switch (this.isActive) {
        case 0:
          return 'WorkOrderInHandList'
          break

        default:
          return 'WorkOrderInHandForm'
          break
      }
    }
  },
  methods: {
    pButton1(index) {
      this.isActive = index
    },
    pButton2(index, item) {
      this.isActive = index
      this.data = item
    }
  }
}
</script>