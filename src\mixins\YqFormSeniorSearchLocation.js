//该脚本用于计算表单高级查询/自定义查询项的显隐及自适应
/*toggleSearchStatus是@/mixins/JeecgListMixin.js或@/mixins/JeecgListMixinNoInit.js脚本中的变量，记录查询板块展开/收起状态，
所以混入了JeecgListMixin或JeecgListMixinNoInit脚本的页面可以混入此脚本*/
import Vue from 'vue'

export const YqFormSeniorSearchLocation = {
  data() {
    return {
      //查询板块需计算宽度组件的ref注册名
      rowName:'row',
      //自定义查询项数组的浏览器缓存名
      queryCacheName:'querySettings',
      //切换显示自定义项的按钮名称
      queryName: '自定义',
      //查询项input对象
      queryCol: {},
      //自定义查询项checkbox数组内容
      queryItems: [],
      //被设置为查询项的dataIndex字符串数组
      settingQueryItems: [],
      //table默认的所有字段
      defColumns: [],
      //table中当前显示的字段
      resultColums:[],
      //table自定义字段数组的浏览器缓存名
      tableCacheName:'',
      //当前每行最多可显示checkbox个数
      queryPerFormItemCount: 1,
      //自定义查询项宽度所对应的比较值数组
      queryLimitWidth: [1600,1200, 992, 768, 576,480],
      //不同宽度下每行显示checkbox的个数
      queryPerRowColCount: [8,8, 6, 4, 3,2],
      //不同宽度下每行checkbox对应所占栅格数数组
      queryPerColSpan: [3,3, 4,6, 8,12],
      //checkbox当前所占的栅格数
      querySpanValue: 8,
      //控制开始是否执行计算方法
      isDo: true,
      //当前每行最多可显示formItem个数
      perFormItemCount: 1,
      //查询板块宽度所对应的比较值数组
      aLimitWidth: [1600,1200, 992, 768, 576],
      //不同宽度下每行显示col的个数
      perRowColCount: [4,4, 3, 2, 1],
      //不同宽度下每行col对应所占栅格数数组
      perColSpan: [6,6, 8, 12, 24],
      //span所占的栅格数
      spanValue: 6,
      //展开、收起按钮是否可见
      isVisible: true,
      //当前ref注册组件的元素宽度
      rowWidth: 600,
      //formItem的label和input宽度设置
      formItemLayout: {
        labelCol: {
          //style:'width:90px;text-align: justify; text-align-last: justify',
          style: 'width:105px'
          /* lg:{ span: 6},
           md:{ span: 5 },
           sm: { span: 3 },
           xs: { span: 24 },*/
        },
        wrapperCol: {
          style: 'width:calc(100% - 105px)',
          /* lg:{ span: 18},
           md:{ span: 19},
           sm: { span: 21},
           xs: { span: 24 },*/
        }
      },
      isUpdatedRowWidth: false,
    }
  },

  //注销window.onresize事件
  beforeDestroy() {
    //window.onresize = null;
    //注销当前resize事件
    window.removeEventListener('resize', this.getAreaRowSize)
  },
  //注销window.onresize事件
  /*destroy(){
    //window.onresize = null;
    //注销当前resize事件
    window.removeEventListener("resize", this.getAreaRowSize)
  },*/
  mounted() {
    if (this.isDo) {
      let th = setTimeout(() => {
          this.$nextTick(() => {
            this.areaRowInitFuns()
          })
        }
        , 2000)
    }
    this.$nextTick(() => {
      this.setListUpButtonStyle()
    })
  },
  activated() {
    this.rowWidth = this.$refs[this.rowName].$el.offsetWidth
    this.getFormItemsColData()
    this.getQueryItemsColData()
  },
  updated() {
    if (this.isUpdatedRowWidth) {
      this.rowWidth = this.$refs[this.rowName].$el.offsetWidth
      this.getFormItemsColData()
      this.getQueryItemsColData()
      this.isUpdatedRowWidth = false
    }
  },
  methods: {
    /**
     * 设置列表上方按钮样式
     */
    setListUpButtonStyle(){
      var rootNode=document.getElementsByClassName('table-operator')
      if (rootNode[0]) {
        let parentNode = rootNode[0].getElementsByTagName('button')
        if (parentNode) {
          for (let i = 0; i < parentNode.length; i++) {
            if (parentNode[i].className.indexOf('yq-primary')!==-1){
              parentNode[i].className = 'ant-btn ant-btn-primary yq-primary'
            }
            else {
              parentNode[i].className = i == 0 ? 'ant-btn ant-btn-primary' : 'ant-btn ant-btn-default'
            }
          }
        }
      }
    },

    areaRowInitFuns() {
      let that = this
      that.$nextTick(() => {
        if (that.$refs[this.rowName] && that.$refs[this.rowName].$el) {
          that.MountWindownOnResizeEvent()
          that.rowWidth = that.$refs[this.rowName].$el.offsetWidth
          that.getFormItemsColData()
          that.getQueryItemsColData()
        }
      })
    },

    //挂载window.onresize事件,获取当前ref注册组件的元素宽度值
    MountWindownOnResizeEvent() {
      window.addEventListener('resize', this.getAreaRowSize)
    },
    getAreaRowSize() {
      let th = setTimeout(() => {
          this.$nextTick(() => {
            let that = this
            that.rowWidth = that.$refs[that.rowName].$el.offsetWidth
            that.getFormItemsColData()
            that.getQueryItemsColData()
          })
        }
        , 1000)
    },

    //判断查询、重置、展开\收起按钮是否居右显示
    setBtnslocationStatus(val,limitWidth,perRowColCount,perColSpan) {
      let that=this
      let perFormItemCount=0
      let spanValue=0
      for (let i = 0; i < limitWidth.length; i++) {
        if (val >= limitWidth[i]) {
          perFormItemCount = perRowColCount[i]
          spanValue = perColSpan[i]
          break
        } else {
          if (i >= limitWidth.length - 1) {
            perFormItemCount = perRowColCount[i]
            spanValue = perColSpan[i]
          }
        }
      }
      return {
             perFormItemCount:perFormItemCount,
             spanValue:spanValue
         }
    },
    //计算ref元素的宽度时，收起展开按钮调用此方法
    doToggleSearch() {
      let that=this
      that.toggleSearchStatus = !that.toggleSearchStatus
      //that.getFormItemsColData()
      that.getQueryItemsColData()
    },
    getFormItemsColData(){
      let that=this
      let obj= that.setBtnslocationStatus(that.rowWidth,that.aLimitWidth,that.perRowColCount,that.perColSpan)
      that.perFormItemCount=obj.perFormItemCount
      that.spanValue=obj.spanValue
    },
    getQueryItemsColData(){
      let that=this
      let obj= that.setBtnslocationStatus(that.rowWidth,that.queryLimitWidth,that.queryPerRowColCount,that.queryPerColSpan)
      that.queryPerFormItemCount=obj.perFormItemCount
      that.querySpanValue=obj.spanValue
    },
    getCacheName(queryCacheName,tableCacheName){
      this.queryCacheName=queryCacheName
      this.tableCacheName=tableCacheName
    },
    getColumns(defColumns,columns={}) {
      this.defColumns = defColumns
      this.resultColums=columns==={}?defColumns:columns
      this.initQueryItem()
      this.updateQueryItem()
    },
    initQueryItem() {
      let that = this
      for (let i = 0; i < that.defColumns.length; i++) {
        if (that.defColumns[i].isUsed) {
          let param = {
            index: i,
            visible: false,
            disabled: false,
            checked:true,
            dataIndex: that.defColumns[i].dataIndex,
            title: that.defColumns[i].title
          }
          that.queryItems.push(param)
        }
      }
    },
    updateQueryItem(){
      let that = this
      var key = that.$route.name +this.tableCacheName
      let colSettings = Vue.ls.get(key)
      if (colSettings == null || colSettings == undefined) {
        if(that.resultColums.length<=1){
          for (let i=0;i<that.queryItems.length;i++){
            that.queryItems[i].checked=false
          }
        }
        else {
          // console.log("操作开始缓存为空，resultColums和defColums相等")
          var key = this.$route.name + this.queryCacheName
          let querySettings = Vue.ls.get(key)
          that.setQueryItemVisible(querySettings)
          that.setqueryCol()
        }
      }
      else {
        // console.log("操作缓存不为空")
        this.changeQueryItem(colSettings)
      }
    },

    changeQueryItem(newColumn) {
      let that = this
      if (that.queryItems.length > 0) {
       let querySettings= that.setQueryItemChecked(newColumn)
        that.setQueryItemVisible(querySettings)
        that.setqueryCol()
      }
    },
    setQueryItemChecked(newColumn){
      let that = this
      var key = this.$route.name + this.queryCacheName
      let querySettings = Vue.ls.get(key)
      // console.log('querySettings==',querySettings)
      for (let i = 0; i < that.queryItems.length; i++) {
        if (!newColumn.includes(that.queryItems[i].dataIndex)) {
          if (querySettings) {
            let newQuerySettings = querySettings.filter((item) => {
              if (item === that.queryItems[i].dataIndex) {
                return false
              } else {
                return true
              }
            })
            querySettings = newQuerySettings
            // console.log("从查询缓存中剔除操作中不含的字段querySettings==",querySettings)
          }
          // console.log("隐藏操作中不存在的字段，设置checked=false")
          that.queryItems[i].checked = false
          that.queryItems[i].visible = false
          that.queryItems[i].disabled = false
          that.queryParam[that.queryItems[i].dataIndex] = undefined
          // console.log(that.queryItems[i].dataIndex,"的checked值为===",that.queryItems[i].checked)
        }else
        {
          that.queryItems[i].checked =true
        }
      }
      return querySettings
    },
    setQueryItemVisible(querySettings){
      let that = this
      that.settingQueryItems = querySettings
      if (querySettings == null || querySettings == undefined||querySettings.length==0) {
        for (let i = 0; i < that.queryItems.length; i++) {
          if (that.queryItems[i].checked) {
            that.settingQueryItems = []
            that.settingQueryItems.push(that.queryItems[i].dataIndex)
            that.queryItems[i].visible = true
            that.queryItems[i].disabled = true
            // console.log("唯一选项that.settingQueryItems==",that.settingQueryItems)
            break
          }
          else {
            that.queryItems[i].visible = false
            that.queryItems[i].disabled = false
            that.queryParam[that.queryItems[i].dataIndex] = undefined
          }
        }
      } else {
        // console.log("查询缓存不为空==",that.settingQueryItems)
        that.settingQueryItems.filter((item) => {
          for (let i = 0; i < that.queryItems.length; i++) {
            if (that.queryItems[i].dataIndex === item) {
              that.queryItems[i].visible = true
              /*that.queryItems[i].checked = true*/
              that.queryItems[i].disabled = that.settingQueryItems.length==1?true:false
            }
          }
        })
      }
      var key = this.$route.name + this.queryCacheName
      Vue.ls.set(key, that.settingQueryItems, 7 * 24 * 60 * 60 * 1000)
    },
    setqueryCol(){
      let that = this
      that.queryCol={}
      for (let i = 0; i < that.queryItems.length; i++) {
        that.queryCol[that.queryItems[i].dataIndex]={
          visible: that.queryItems[i].visible,
          title: that.queryItems[i].title,
          //checked: that.queryItems[i].checked,
        }
      }
      // console.log("that.queryCol==",that.queryCol)
    },

    onQuerySettingsChange(checkedValues) {
      this.queryItems.filter(item => {
        if (checkedValues.includes(item.dataIndex)) {
          item.visible = true
          item.disabled = checkedValues.length === 1 ? true : false
        } else {
          item.visible = false
          this.queryParam[item.dataIndex] = undefined
          item.disabled = false
        }
      })
      // console.log("checkedValues==",checkedValues)
      var key = this.$route.name + this.queryCacheName
      //Vue.ls.set(key, checkedValues, 7 * 24 * 60 * 60 * 1000)
      Vue.ls.set(key, checkedValues, 7 * 24 * 60 * 60 * 1000)
      this.settingQueryItems = checkedValues
      this.setqueryCol()
    },
    getVisible(attr){
     return this.queryCol[attr]&&this.queryCol[attr].visible
    },
    getTitle(attr){
      return this.queryCol[attr]&&this.queryCol[attr].title
    }
  }
}