<template>
  <div v-if='infoStr' class='focus-info' :title='infoStr'>
    <span v-if='scene!=="network"'>重点关注：</span>{{ infoStr }}
  </div>
</template>
<script>
import { organizations } from '@/views/zrBigscreens/modules/zrOrganizations'
import { unitLevels, businessStatus } from '@/views/zrBigscreens/modules/zrUtil'
import { flatTreeData } from '@/utils/util'
export default {
  name: 'ZrFocusInfo',
  props:{
    scene:{
      type:String,
      default:'national'
    },
    infoText:{
      type:String,
      default:""
    }
  },
  data() {
    return {
      organizations:[],
    }
  },
  created() {
    if(this.scene==="national"){
      this.organizations = flatTreeData([organizations], null, []).filter(el=>el.city).sort((a,b)=>{
        return   a.score - b.score
      }).splice(0,3)
    }
    else if(this.scene==="business"){

    }
  },
  computed: {
    infoStr() {
      if(this.scene==="national"){
        return this.infoText
      }
      else if(this.scene==="business"){
        return this.infoText
      }else if(this.scene==="unitNode"){
        return this.infoText
      }else if(this.scene==="network"){
        return this.infoText
      }
    }
  },
  methods: {
  }
}
</script>

<style scoped lang='less'>
.focus-info{
  width: calc(100% - 40px);
  padding: 0 20px;
  font-size: 14px;
  //position: absolute;
  //bottom: 33px;
  color: #E3E7EF;
  color: rgba(250, 226, 70, 0.9);
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;  // 添加这行禁止换行
}
</style>