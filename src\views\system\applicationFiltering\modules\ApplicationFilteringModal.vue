<template>
  <j-modal
    :title='title'
    :width='width'
    :centered='true'
    :visible='visible'
    :destroyOnClose='true'
    switchFullscreen
    cancelText='关闭'
    @cancel='handleCancel'
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @ok="handleOk"
  >
    <a-spin :spinning='confirmLoading'>
      <j-form-container :disabled='disableSubmit'>
        <a-form-model ref='form' :model='model' :rules='validatorRules' slot='detail' v-bind='formItemLayout'>
          <a-row>
            <a-col :span='24'>
              <a-form-model-item label='策略名称' prop='strategyName'>
                <a-input
                  v-model='model.strategyName'
                  :allow-clear='true'
                  autocomplete='off'
                  placeholder='请输入策略名称' />
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
              <a-form-model-item label='策略描述' prop='remark'>
                <a-textarea
                  v-model='model.remark'
                  :allow-clear='true'
                  autocomplete='off'
                  placeholder="请输入策略描述"
                  :auto-size="{ minRows: 1, maxRows: 6 }">
                </a-textarea>
              </a-form-model-item>
            </a-col>
            <a-col :span='24' >
              <a-form-model-item label='操作系统类型' prop='osType'>
                <j-dict-select-tag v-model='model.osType' placeholder='请选择操作系统类型' dictCode='os_type'  @change="changeOsType"/>
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
              <a-form-model-item label='部门' prop='deptId'>
                <a-tree-select v-model='model.deptId'
                               :getPopupContainer='(node) => node.parentNode'
                               tree-node-filter-prop='title'
                               :replaceFields='replaceFields'
                               :treeData='treeData'
                               show-search
                               :searchValue='bsearchKey'
                               style='width: 100%'
                               :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                               placeholder='请选择单位'
                               allow-clear
                               @change='onChangeTree'
                               @search='onSearch'>
                </a-tree-select>
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
              <a-form-model-item label='配置范围' prop='scopeType'>
                <a-radio-group v-model='model.scopeType' :options='configRangeOption'/>
              </a-form-model-item>
            </a-col>
            <a-col :span='24' v-if="model.scopeType==1">
              <a-form-model-item label='终端名称' prop='uniqueCode'>
                <a-select
                  v-model='model.uniqueCode'
                  :maxTagCount="10"
                  :maxTagTextLength="30"
                  mode="multiple"
                  placeholder='请选择终端'
                  :allow-clear='true'
                  @change='changeTerminal'>
                  <a-select-option v-for='item in terminalList' :value='item.uniqueCode' :key="'terminal'+item.uniqueCode"
                                   :label='item.name'>
                    {{ item.name }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
              <a-form-model-item label='展示方式' prop='status'>
                <a-radio-group v-model='model.status' :options='displayMethodOption'/>
              </a-form-model-item>
            </a-col>
            <a-col :span='24' >
              <a-form-model-item prop='applicationList'>
                <span slot='label'>
                  <span>应用列表</span>
                  <a-popover title='说明'>
                    <template slot='content'>
                      <p><h4>数据来源：</h4>根据所选终端名称第一个标签内容加载获取</p>
                      <p><h4>交互：</h4>1、所选应用列表，会自动填充到应用名称中;<br>2、取消所选项，不会影响应用名称取值内容</p>
                    </template>
                    <a-icon style='margin-left:5px;font-size: 20px; line-height: 40px' theme='twoTone' type='question-circle' />
                  </a-popover>
                </span>
                <a-select v-model='model.applicationList' placeholder='请选择应用列表' mode="multiple" :allow-clear='true' @change='changeApplicationList'>
                  <a-select-option v-for='item in applicationList' :value='item' :key="'applicationList'+item"
                                   :label='item'>
                    {{ item }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span='24' >
              <a-form-model-item prop='applicationName'>
                <span slot='label'>
                  <span>应用名称</span>
                  <a-popover title='说明'>
                    <template slot='content'>
                      <p>应用名称内容可自定义输入、也可选择应用列表内容进行填充</p>
                    </template>
                    <a-icon style='margin-left:5px;font-size: 20px; line-height: 40px' theme='twoTone' type='question-circle' />
                  </a-popover>
                </span>
                <a-select v-model='model.applicationName' :open="false" mode="tags" placeholder='请输入应用名称' :allow-clear='true' @change='changeApplicationName'>
                  <a-select-option v-for='item in applicationNameList' :value='item.id' :key="'applicationName'+item.id"
                                   :label='item.templateName'>
                    {{ item.templateName }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </j-form-container>
    </a-spin>
  </j-modal>
</template>
<script>
import { getAction, httpAction } from '@api/manage'
import { mapGetters } from 'vuex'
export default {
  name: 'ApplicationFilteringModal',
  components: {},
  data() {
    return {
      title: '新增',
      width: '900px',
      disableSubmit: false,
      visible: false,
      confirmLoading: false,
      formItemLayout: {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 24 },
          md: { span: 4 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 24 },
          md: { span: 19 }
        }
      },
      configRangeOption: [{ label: '全部', value: 0, key: 0, checked: false },
        { label: '批量', value: 1, key: 1, checked: false }],
      displayMethodOption: [{ label: '展示应用', value: 0, key: 0, checked: false },
        { label: '过滤应用', value: 1, key: 1, checked: false }],

      replaceFields: {
        children: 'children',
        title: 'deptName',
        key: 'deptId',
        value: 'deptId'
      },
      treeData: [],
      bsearchKey: '',
      allDepts: '',

      terminalList: [],
      applicationList: [],
      applicationNameList: [],
      model: {
        scopeType: 0,
        status: 0,
      },
      validatorRules: {
        strategyName: [
          { required: true, message: '请输入策略名称' },
          { min: 2, max: 50, message: '策略名称长度应2-50之间', trigger: 'blur' }
        ],
        remark: [
          { required: true, message: '请输入策略描述' },
          { min: 2, max: 100, message: '策略描述长度应2-100之间', trigger: 'blur' }
        ],
        osType: [
          { required: false, message: '请选择操作系统类型！' }
        ],
        deptId: [
          { required: false, message: '请选择部门', trigger: 'change' }
        ],
        scopeType: [
          { required: true, message: '请选择配置范围', trigger: 'blur' }
        ],
        uniqueCode: [
          { required: true, message: '请选择终端', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '选择展示方式', trigger: 'blur' }
        ],
        applicationName: [
          { required: true, message: '请输入应用名称', trigger: 'blur' }
        ],
      },
      url: {
        add: '/terminal/application/add',
        edit: '/terminal/application/edit',
        depart: '/sys/sysDepart/queryAllTree',
        queryInfoName: '/terminal/application/queryInfoName',
        queryApplication: '/terminal/application/queryApplication'
      }
    }
  },
  created() {
    this.getDepartList()
    this.getTerminals()
  },
  methods: {
    /*新增知识*/
    add() {
      this.edit({
        scopeType: 0,
        status: 0,
      })
    },
    /*编辑知识*/
    async edit(record) {
      this.visible = true
      this.confirmLoading = true
      this.model = JSON.parse(JSON.stringify(record))

      if (!this.model['osType']) {
        this.model['osType'] = undefined
      }
      if (!this.model['deptId']) {
        this.model['deptId'] = undefined
      }

      this.resetFieldValue('uniqueCode')
      await this.getTerminals()
      this.resetFieldValue('applicationName')
      this.confirmLoading = false
    },
    resetFieldValue(field, value = []) {
      if (this.model[field] && typeof this.model[field] !== 'string') return
      if (this.model[field]) {
        let v = this.model[field].split(',')
        this.$set(this.model, field, v)
      } else {
        this.$set(this.model, field, value)
      }
    },
    getDepartList() {
      getAction(this.url.depart).then((res) => {
        if (res) {
          this.treeData = res
        } else {
          this.treeData = []
        }
      })
    },
    async changeOsType(value) {
      await this.getTerminals()
    },
    async onChangeTree(value) {
      await this.getTerminals()
    },
    onSearch(e) {
      this.bsearchKey = e
    },
    async getTerminals() {
      let { osType, deptId } = this.model
      let param = {
        osType: osType,
        deptId: deptId
      }
      await getAction(this.url.queryInfoName, param).then((res) => {
        let isResetApplication = true
        if (res.success && res.result) {
          this.terminalList = res.result
          if (this.terminalList.length > 0 && this.model.uniqueCode && this.model.uniqueCode.length > 0) {
            let has = []
            this.terminalList.filter(item => {
              if (this.model.uniqueCode.includes(item.uniqueCode)) {
                has.push(item.uniqueCode)
              }
              return false
            })
            this.$set(this.model, 'uniqueCode', has.length > 0 ? has : []);
            if (has.length > 0) {
              this.getApplication(has[0])
            } else {
              this.getApplication('')
            }
            isResetApplication = false
          }
        }
        if (isResetApplication) {
          this.getApplication('')
          this.$set(this.model, 'uniqueCode', []);
        }
      })
    },
    async getApplication(firstCode = '') {
      if (firstCode && firstCode.length > 0) {
        await getAction(this.url.queryApplication, { uniqueCode: firstCode }).then((res) => {
          if (res.success && res.result) {
            this.$set(this.model, 'firstCode', firstCode);
            this.applicationList = res.result
          }
        })
      } else {
        this.$set(this.model, 'applicationList', []);
        this.$set(this.model, 'firstCode', '');
        this.applicationList = []
      }
    },
    changeTerminal(value) {
      let f = value && value.length > 0 ? value[0] : ''
      if (f && this.model['firstCode'] && this.model['firstCode'] === f) {
        return
      }
      this.getApplication(f)
    },
    changeApplicationList(value) {
      if (value && value.length > 0) {
        let arr = this.model.applicationName
        for (let i = 0; i < value.length; i++) {
          if (!arr.includes(value[i])) {
            arr.push(value[i])
            this.$set(this.model, 'applicationName', arr)
          }
        }
      }
    },
    changeApplicationName(value) {
    },

    /**
     *提交表单数据
     */
    handleOk() {
      let that = this
      that.$refs.form.validate((err, values) => {
        if (err && !that.confirmLoading) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!that.model.id) {
            httpurl += that.url.add
            method = 'post'
          } else {
            httpurl += that.url.edit
            method = 'post'
          }
          let formData = JSON.parse(JSON.stringify(that.model))
          delete formData.firstCode
          if (formData.scopeType != 1) {
            formData.uniqueCode = ''
          } else {
            formData.uniqueCode = formData.uniqueCode.join(',')
          }
          formData.applicationName = formData.applicationName.join(',')
          if (!formData['deptId']) {
            formData['deptId'] = ''
          }
          if (!formData['osType']) {
            formData['osType'] = ''
          }

          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
                that.close()
              } else {
                that.$message.warning(res.message)
              }
              that.confirmLoading = false
            }).catch((err) => {
            that.$message.warning(err.message)
            that.confirmLoading = false
          })
        }
      })
    },
    handleCancel() {
      this.close()
    },
    close() {
      this.visible = false
      this.confirmLoading = false
      this.model = {}
    },
  }
}
</script>
<style scoped lang='less'>
@import '~@assets/less/normalModal.less';
</style>