<template>
  <div class="background">
    <div>
      <div class="topBox">
        <div style="">运维监控大数据平台</div>
        <div style="display: flex; float: right; margin-right: 30px; margin-top: -10px">
          <div style="font-size: 16px">{{ data }}</div>
          <div style="font-size: 18px; margin-left: 15px; font-weight: 800">{{ time }}</div>
        </div>
      </div>
    </div>
    <a-row :gutter="24" class="row-class">
      <a-col :span="6" class="col-class">
        <div class="left_top">
          <div class="topTitle">
            <img src="@/assets/bigScreen/shanxi/equipmentAlarm.png" alt="" style="margin-right: -12px" />
            <div class="left_top_title">设备告警数量TOP5</div>
          </div>
          <!-- <charts-top-title :title="'设备告警数量TOP5'" :src="'@/assets/bigScreen/shanxi/equipmentAlarm.png'"></charts-top-title> -->
          <div id="equipmentAlarm" style="width: 100%; height: 80%"></div>
        </div>
        <div class="center_left">
          <div class="topTitle">
            <img src="@/assets/bigScreen/shanxi/trendAnalysis.png" alt="" style="margin-right: -12px" />
            <div class="center_left_title">告警趋势分析</div>
          </div>
          <div id="trendAnalysis" style="width: 100%; height: 90%"></div>
        </div>
        <div class="left_bottom">
          <div class="topTitle">
            <img src="@/assets/bigScreen/shanxi/AlarmMonitoring.png" alt="" style="margin-right: -12px" />
            <div class="left_bottom_title">实时告警监控</div>
          </div>
          <div class="left-bottom-core">
            <div class="left-bottom-table">
              <vue-seamless-scroll :data="warningData" :class-option="warning" class="seamless-warp">
                <div v-for="item in warningData" :key="item.id">
                  <img v-if="item.alarmLevel == '一般告警'" src="../.././assets/bigScreen/29.png" />
                  <img v-else src="../.././assets/bigScreen/30.png" />
                  <span :title="item.templateName"> {{ item.templateName }}</span>
                  <span :title="item.deviceName"> {{ item.deviceName }} </span>
                  <span :title=" item.createTime"> {{ item.createTime }} </span>
                </div>
              </vue-seamless-scroll>
            </div>
          </div>
        </div>
      </a-col>
      <a-col :span="12" class="col-class">
        <div class="center_top">
          <div class="equipmentTotal">
            <div style="display: flex">
              <span class="stateTitle">设备总数</span>
              <div class="bigScreen_number" v-for="(i, index) in item.all" :key="index" style="color: #5ec7ff">
                <animate-number from="0" :to="i || 0" :key="i" duration="2000"></animate-number>
              </div>
              <span class="unit">个</span>
            </div>
            <div style="display: flex">
              <span class="stateTitle">在线</span>
              <div class="bigScreen_number" v-for="(i, index) in item.on" :key="index" style="color: #5bfcd2">
                <animate-number from="0" :to="i" :key="i || 0" duration="2000"></animate-number>
              </div>
              <span class="unit">个</span>
            </div>
            <div style="display: flex">
              <span class="stateTitle">离线</span>
              <div class="bigScreen_number" v-for="(i, index) in item.out" :key="index" style="color: #fdcc08">
                <animate-number from="0" :to="i || 0" :key="i" duration="2000"></animate-number>
              </div>
              <span class="unit">个</span>
            </div>
            <div style="display: flex">
              <span class="stateTitle">告警</span>
              <div class="bigScreen_number" v-for="(i, index) in item.alarms" :key="index" style="color: #f43954">
                <animate-number from="0" :to="i || 0" :key="i" duration="2000"></animate-number>
              </div>
              <span class="unit">个</span>
            </div>
          </div>
          <div class="center_top_bottom">
            <div class="center_top_bottom_box" style="width: 30%">
              <span class="center_top_bottom_title">设备总数分类占比</span>
              <div style="display: flex; justify-content: center">
                <img src="@/assets/bigScreen/halation.png" alt="" />
              </div>
              <div id="deviceClass" style="width: 100%; height: 100%"></div>
            </div>
            <div class="center_top_bottom_box" style="width: 40%">
              <span class="center_top_bottom_title">设备在线分类统计</span>
              <div style="display: flex; justify-content: center">
                <img src="@/assets/bigScreen/halation.png" alt="" />
              </div>
              <div id="deviceStatus" style="width: 100%; height: 74%"></div>
            </div>
            <div class="center_top_bottom_box" style="width: 30%">
              <span class="center_top_bottom_title">设备告警分布</span>
              <div style="display: flex; justify-content: center">
                <img src="@/assets/bigScreen/halation.png" alt="" />
              </div>
              <div id="alarmDistribution" style="width: 100%; height: 70%"></div>
            </div>
          </div>
        </div>
        <div class="center_bottom">
          <div style="display: flex; justify-content: center">
            <img src="../../assets/bigScreen/upBracket.png" alt="" />
          </div>
          <div style="display: flex; justify-content: center">
            <div class="networkTopology">全网拓扑监控</div>
          </div>
          <div class="topo-class">
            <div style="width: 100%; height: calc(100% - 24px)">
              <vis-edit v-if="nettopoList.length > 0" ref="bigScreen" operate="show" topoBgByTheme ></vis-edit>
            </div>
            <!-- <topo-big-screen-sx ref="bigScreen" v-if="nettopoList.length > 0" style="height: 100%;width: 100%">
            </topo-big-screen-sx> -->
            <div class="center_bottom_img"><img src="../../assets/bigScreen/downBracket.png" alt="" /></div>
          </div>
        </div>
      </a-col>
      <a-col :span="6" class="col-class">
        <div class="right_top">
          <div class="topTitle">
            <img src="../../assets/bigScreen/workOrder.png" alt="" style="margin-right: -12px" />
            <div class="right_top_title">智能运维工单</div>
          </div>
          <div class="right_top_center">
            <div style="display: flex; align-items: center">
              <span style="font-size: 14px; color: #ffffff">工单总数</span>
              <div class="bigScreen_number" v-for="(i, index) in count.all" :key="index" style="color: #46fbe9">
                <animate-number from="0" :to="i || 0" :key="i" duration="2000"></animate-number>
              </div>
              <span style="font-size: 12px; color: #ffffff">个</span>
            </div>
            <div style="display: flex; align-items: center">
              <div style="font-size: 14px; color: #ffffff">已处理</div>
              <div style="width: 129px; margin-left: 10px">
                <a-progress
                  :percent="percent"
                  status="active"
                  size="small"
                  :stroke-color="{
                    '0%': '#177FFF',
                    '100%': '#36E8FD',
                  }"
                />
              </div>
              <div style="font-size: 12px; color: #ffffff">
                <animate-number from="0" :to="count.done || 0" :key="count.done" duration="2000"></animate-number>个
              </div>
            </div>
          </div>
          <div class="workOrder_class">
            <div id="workOrder" style="width: 100%; height: 100%"></div>
          </div>
        </div>
        <div class="right_center">
          <div style="display: flex; justify-content: space-between">
            <div class="topTitle">
              <img src="../../assets/bigScreen/equipmentPerformance.png" alt="" style="margin-right: -12px" />
              <div class="right_center_title">设备性能监控</div>
            </div>
            <div class="equipmentPerformanceTitle">
              <div :class="onButton" @click="buttonChange1">CPU</div>
              <div :class="notButton" style="margin-left: 5px" @click="buttonChange2">内存</div>
            </div>
          </div>
          <div id="equipmentPerformance" style="width: 100%; height: 74%"></div>
        </div>
        <div class="right_bottom">
          <div class="topTitle">
            <img src="../../assets/bigScreen/warningWords.png" alt="" style="margin-right: -12px" />
            <div class="right_bottom_title">告警词云</div>
          </div>
          <div style="padding: 0 24px">
            <div v-for="(item, index) in this.nameList" :key="index" :class="itemStyle[index]">{{ item }}</div>
          </div>
        </div>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import { formatDate } from '@/utils/util'
import { getAction } from '@/api/manage'
import echarts from 'echarts/lib/echarts'
import vueSeamlessScroll from 'vue-seamless-scroll'
import VisEdit from '@/views/topo/nettopo/modules/VisEdit.vue'
import ChartsTopTitle from './moduels/charts-top-title.vue'
import { WebsocketMixin } from '@/mixins/WebsocketMixin'
export default {
  components: {
    vueSeamlessScroll,
    ChartsTopTitle,
    VisEdit,
  },
  mixins: [WebsocketMixin],
  data() {
    return {
      timer: null,
      data: '',
      time: '',
      onButton: 'onButton',
      notButton: 'notButton',
      nameList: [],
      nettopoList: [],
      netPageNo: 1,
      percent: 0,
      hoverHeight: '0px',
      netPapeTotalNo: 0,
      itemStyle: ['style1', 'style2', 'style3', 'style4', 'style5', 'style6'],
      warningData: [],
      item: [],
      count: [],
      url: {
        equipmentAlarmNum: '/data-analysis/alarm/template/top10', //设备告警数量
        alarmTrend: '/data-analysis/alarm/week', //告警趋势分析
        alarmList: '/data-analysis/device/alarm/list', // 告警轮播数据
        showCounts: '/device/deviceInfo/showCounts', // 设备数量统计
        Proportion: '/data-analysis/momg/device/count', // 设备总数分类
        alarmWords: '/data-analysis/alarm/dict/top6', // 告警词云数据
        list: '/topo/topoInfo/list', // 拓扑图数据
        orderCount: '/data-analysis/order/count', // 运维工单数据
        deviceList: '/data-analysis/alarm/findDevCountByProAndStatus', //设备在线分类统计
        deviceAlarmDistri: '/data-analysis/alarm/deviceAlarmDistri', //设备告警分类
        monitor: '/data-analysis/device/memRate/top', // 设备性能监控内存数据
        cpu: '/data-analysis/device/cpu/top', // 设备性能监控CPU数据
        typeCount: '/data-analysis/order/type/count', // 运维工单环形图数据
      },
    }
  },
  computed: {
    warning() {
      return {
        step: 0.1, // 数值越大速度滚动越快
        limitMoveNum: 4, // 开始无缝滚动的数据量 this.dataList.length
        hoverStop: false, // 是否开启鼠标悬停stop
        direction: 1, // 0向下 1向上 2向左 3向右
        // openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 86, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
        // singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
        waitTime: 2, // 单步运动停止的时间(默认值1000ms)
      }
    },
  },
  created() {
    setInterval(() => {
      this.data = formatDate(Date.parse(new Date()), 'yyyy-MM-dd')
      this.time = formatDate(Date.parse(new Date()), 'hh:mm:ss')
    }, 1000)
  },
  mounted() {
    this.getNetTopoList()
    this.equipment()
    this.alarmTrend()
    this.alarmList()
    this.showCounts()
    this.Proportion()
    this.orderCount()
    this.typeCount()
    this.buttonChange1()
    this.alarmWords()
    this.deviceList()
    this.deviceAlarmDistri()
  },
  methods: {
    clearTimer() {
      this.timer && clearInterval(this.timer)
    },
    buttonChange1() {
      this.clearTimer()
      this.timer = setInterval(() => {
        this.buttonChange2()
      }, 5000)
      this.onButton = 'onButton'
      this.notButton = 'notButton'
      this.getCPU()
    },
    buttonChange2() {
      this.clearTimer()
      this.timer = setInterval(() => {
        this.buttonChange1()
      }, 5000)
      this.onButton = 'notButton'
      this.notButton = 'onButton'
      this.monitor()
    },
    //拓扑数据
    getNetTopoList() {
      getAction(this.url.list, {
        topoType: '0',
        showType: '1',
        pageNo: this.netPageNo,
        pageSize: 30,
      }).then((res) => {
        if (res.success) {
          this.netPageNo += 1
          this.netPapeTotalNo = Math.ceil(res.result.pageList.total / 10)
          this.nettopoList = [...this.nettopoList, ...res.result.pageList.records]
          if (this.nettopoList.length > 0) {
            setTimeout(() => {
              this.hoverHeight = ([document.getElementsByClassName('img-div')][0].clientWidth / 5) * 3 + 'px'
            }, 50)
            this.$nextTick(() => {
              if (this.netPageNo == 2) {
                this.$refs.bigScreen.createTopo(this.nettopoList[0].id)
              }
            })
          }
        }
      })
    },
    // 设备告警数量柱状图数据
    equipment() {
      getAction(this.url.equipmentAlarmNum).then((res) => {
        if (res.success) {
          let data = []
          if (res.result.length > 5) {
            data = res.result.slice(res.result.length - 5, res.result.length)
          } else {
            data = res.result
          }
          this.equipmentAlarm(data)
        }
      })
    },
    // 设备告警数量柱状图
    equipmentAlarm(data) {
      function attackSourcesDataFmt(sData) {
        var sss = []
        sData.forEach(function (item, i) {
          if (item.value == undefined || item.value == null || item.value == '') {
            item.value = 0
          }
          sss.push({
            value: item.value,
          })
        })
        return sss.reverse()
      }
      let yarr = []
      let xarr = []
      if (data && data.length > 0) {
        data.forEach((e) => {
          xarr.push(e.name)
          yarr.push(e.value)
        })
      }
      let myChart = this.$echarts.init(document.getElementById('equipmentAlarm'))
      myChart.setOption({
        tooltip: {
          show: true,
          trigger: 'axis',
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
          },
          transitionDuration: 0, //echart防止tooltip的抖动
        },
        xAxis: {
          type: 'value',
          splitLine: {
            show: false,
          }, //去除网格线
          show: false,
        },
        yAxis: [
          {
            type: 'category',
            data: xarr,
            splitLine: {
              show: false,
            }, //去除网格线
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false, //y轴线消失
              lineStyle: {
                //y轴字体颜色
                color: '#C2C3CD',
              },
            },
            axisLabel: {
              formatter: (value) => {
                if (value.length > 8) {
                  return value.substring(0, 8) + '...'
                } else {
                  return value
                }
              },
            },
          },
          {
            type: 'category',
            inverse: true,
            axisTick: 'none',
            axisLine: 'none',
            show: true,
            axisLabel: {
              textStyle: {
                color: '#00a1e7',
                fontSize: '12',
              },
            },
            data: attackSourcesDataFmt(data),
          },
        ],
        grid: {
          top: 0,
          left: 90, // 调整这个属性
          right: 50,
          bottom: 0,
        },
        series: [
          {
            data: yarr,
            type: 'bar',
            showBackground: true,
            backgroundStyle: {
              color: 'rgba(8,28,78)',
              barBorderRadius: 30,
            },
            barWidth: 10, //柱图宽度
            itemStyle: {
              emphasis: {
                barBorderRadius: 30,
              },
              normal: {
                barBorderRadius: [10, 10, 10, 10],
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                  {
                    offset: 0,
                    color: '#3679fb',
                  },
                  {
                    offset: 1,
                    color: '#5BFCD2',
                  },
                ]),
              },
            },
          },
        ],
      })
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    },
    // 告警趋势分析数据
    alarmTrend() {
      getAction(this.url.alarmTrend).then((res) => {
        if (res.success) {
          this.trendAnalysis(res.result)
        }
      })
    },
    // 告警趋势分析柱状图
    trendAnalysis(data) {
      let time = []
      let title = ['告警总数', '服务器告警']
      let value1 = []
      let value2 = []
      if (data && data.length > 0) {
        data.forEach((item) => {
          time.push(item.name)
          value1.push(item.value1)
          value2.push(item.value2)
        })
      }
      let myChart = this.$echarts.init(document.getElementById('trendAnalysis'))
      myChart.setOption({
        tooltip: {
          show: true,
          trigger: 'axis',
          transitionDuration: 0, //echart防止tooltip的抖动
        },
        legend: {
          left: 'right',
          data: title,
          textStyle: {
            color: '#fff',
          },
        },
        grid: {
          top: '10%',
          left: '5%',
          right: '8%',
          bottom: '15%',
          containLabel: true,
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: false,
            axisLine: {
              //坐标轴轴线相关设置。数学上的x轴
              show: true,
              lineStyle: {
                color: '#1f313d',
              },
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: '#97ABEA', //更改坐标轴文字颜色
              },
            },
            splitLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            data: time,
          },
        ],
        yAxis: [
          {
            type: 'value',
            nameTextStyle: {
              color: '#FFFFFF',
              fontSize: 12,
            },
            min: 0,
            splitLine: {
              show: true,
              lineStyle: {
                color: ['#1c2a37'],
                width: 2,
                type: 'solid',
              },
            },
            axisLine: {
              show: false,
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: '#5189ba', //更改坐标轴文字颜色
              },
            },
            axisTick: {
              show: false,
            },
          },
        ],
        series: [
          {
            name: '告警总数',
            type: 'line',
            symbol: 'circle', // 默认是空心圆（中间是白色的），改成实心圆
            smooth: true,
            showAllSymbol: true,
            lineStyle: {
              normal: {
                width: 1,
                color: '#2c81ff', // 线条颜色
              },
              borderColor: 'rgba(0,0,0,.4)',
            },
            itemStyle: {
              color: '#2c81ff',
              borderColor: '#2c81ff',
              borderWidth: 2,
            },
            tooltip: {
              show: true,
              transitionDuration: 0, //echart防止tooltip的抖动
            },
            areaStyle: {
              //区域填充样式
              normal: {
                //线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是‘true’，则该四个值是绝对像素位置。
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: 'rgba(25,163,223,.3)',
                    },
                    {
                      offset: 1,
                      color: 'rgba(25,163,223, 0)',
                    },
                  ],
                  false
                ),
                shadowColor: 'rgba(25,163,223, 0.5)', //阴影颜色
                shadowBlur: 20, //shadowBlur设图形阴影的模糊大小。配合shadowColor,shadowOffsetX/Y, 设置图形的阴影效果。
              },
            },
            data: value1,
          },
          {
            name: '服务器告警',
            type: 'line',
            symbol: 'circle', // 默认是空心圆（中间是白色的），改成实心圆
            smooth: true,
            showAllSymbol: true,

            lineStyle: {
              normal: {
                width: 1,
                color: '#06cdf6', // 线条颜色
              },
              borderColor: 'rgba(0,0,0,.4)',
            },
            itemStyle: {
              color: '#06cdf6',
              borderColor: '#06cdf6',
              borderWidth: 2,
            },
            tooltip: {
              show: true,
              transitionDuration: 0, //echart防止tooltip的抖动
            },
            areaStyle: {
              //区域填充样式
              normal: {
                //线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是‘true’，则该四个值是绝对像素位置。
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: 'rgba(10,219,250,.3)',
                    },
                    {
                      offset: 1,
                      color: 'rgba(10,219,250, 0)',
                    },
                  ],
                  false
                ),
                shadowColor: 'rgba(10,219,250, 0.5)', //阴影颜色
                shadowBlur: 20, //shadowBlur设图形阴影的模糊大小。配合shadowColor,shadowOffsetX/Y, 设置图形的阴影效果。
              },
            },
            data: value2,
          },
        ],
      })
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    },
    // 告警轮播数据
    alarmList() {
      getAction(this.url.alarmList).then((res) => {
        if (res.code == 200) {
          if (res.result != '暂无信息') {
            this.warningData = res.result
          }
        }
      })
    },
    // 设备数量统计
    showCounts() {
      getAction(this.url.showCounts).then((res) => {
        if (res.success) {
          this.item = res.result
          this.item.all = this.item.all.toString()
          this.item.on = this.item.on.toString()
          this.item.out = this.item.out.toString()
          this.item.alarms = this.item.alarms.toString()
        }
      })
    },
    // 设备总数分类占比数据
    Proportion() {
      getAction(this.url.Proportion).then((res) => {
        if (res.success) {
          this.deviceClass(res.result)
        }
      })
    },
    // 设备总数分类占比环形图
    deviceClass(data) {
      let myChart = this.$echarts.init(document.getElementById('deviceClass'))
      myChart.setOption({
        tooltip: {
          show: true,
          transitionDuration: 0, //echart防止tooltip的抖动
          formatter(data) {
            let value = data.name + ':' + data.percent + '%'
            return value
          },
        },
        series: [
          {
            type: 'pie',
            radius: ['25%', '40%'],
            center: ['50%', '40%'],
            roseType: 'radius',
            color: ['#0278E7', '#7AED82', '#49FAF9', '#00C6FF', '#F19610', '#6054FF', '#ac3a5c'],
            labelLine: {
              length: 5,
              show: true,
            },
            label: {
              fontSize: 10,
              formatter(data) {
                let value = data.name + ':' + data.percent + '%'
                return value
              },
            },
            data: data,
          },
        ],
      })
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    },
    // 设备在线分类统计数据
    deviceList() {
      getAction(this.url.deviceList).then((res) => {
        if (res.success) {
          this.deviceStatus(res.result)
        }
      })
    },
    // 设备在线分类统计柱状图
    deviceStatus(data) {
      let xArr = []
      let value1 = []
      let value2 = []
      if (data && data.length > 0) {
        data.forEach((e) => {
          xArr.push(e.name)
          value1.push(e.value1)
          value2.push(e.value2)
        })
      }
      let myChart = this.$echarts.init(document.getElementById('deviceStatus'))
      myChart.setOption({
        legend: {
          data: ['在线', '离线'],
          left: 'right',
          textStyle: {
            color: '#c4e4fd',
          },
          itemHeight: 10,
          itemWidth: 10,
        },
        tooltip: {
          type: true,
          transitionDuration: 0, //echart防止tooltip的抖动
        },
        grid: {
          left: '3%',
          right: '5%',
          bottom: '5%',
          top: '14%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: xArr,
          axisLine: {
            lineStyle: {
              type: 'solid',
              color: '#5a595f', //左边线的颜色
              width: '1', //坐标线的宽度
            },
          },
          axisLabel: {
            show: true,
            interval: 0,
            textStyle: {
              fontSize: '10',
              color: '#C2C3CD',
            },
          },
        },
        yAxis: {
          type: 'value',
          axisLine: {
            show: false, //y轴线消失
            lineStyle: {
              //y轴字体颜色
              color: '#97ABEA',
            },
          },
          axisTick: {
            show: false,
          },
          //网格线颜色
          splitLine: {
            show: true,
            lineStyle: {
              color: ['#999999'],
              width: 1,
              opacity: 0.1,
              type: 'solid',
            },
          },
        },
        series: [
          {
            // 上半截柱子
            name: '在线',
            type: 'bar',
            stack: 'one',
            barWidth: '16',
            z: 0,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                {
                  offset: 0,
                  color: '#1563FD',
                },
                {
                  offset: 1,
                  color: '#1EACFD',
                },
              ]),
            },
            data: value1,
          },
          {
            // 下半截柱子
            name: '离线',
            type: 'bar',
            stack: 'one',
            barWidth: '16',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                {
                  offset: 0,
                  color: '#56C3FF',
                },
                {
                  offset: 1,
                  color: '#5CFFD0',
                },
              ]),
            },
            data: value2,
          },
        ],
      })
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    },
    // 设备告警分布数据
    deviceAlarmDistri() {
      getAction(this.url.deviceAlarmDistri).then((res) => {
        if (res.success) {
          this.alarmDistribution(res.result)
        }
      })
    },
    // 设备告警分布雷达图
    alarmDistribution(data) {
      let value = 0
      let value1 = []
      let value2 = []
      if (data && data.length > 0) {
        data.forEach((e) => {
          value += e.value1
          value1.push(e.value1)
        })
        value1.forEach((ele) => {
          if(value === 0){
            value2.push(0)
          }else{
            value2.push((ele / value) * 100)
          }

        })
      }
      console.log("value2", value2)
      let myChart = this.$echarts.init(document.getElementById('alarmDistribution'))
      myChart.setOption({
        radar: {
          radius: '60%',
          indicator: [
            {
              name: data[0].name,
              max: '100',
            },
            {
              name: data[1].name,
              max: '100',
            },
            {
              name: data[2].name,
              max: '100',
            },
            {
              name: data[3].name,
              max: '100',
            },
            {
              name: data[4].name,
              max: '100',
            },
            {
              name: data[5].name,
              max: '100',
            },
          ],
          splitNumber: 4,
          splitArea: {
            areaStyle: {
              color: ['#5C5F76', '#484B6D', '#343962', '#181E50'],
              shadowColor: 'rgba(0, 0, 0, 0.2)',
              shadowBlur: 10,
            },
          },
          axisLine: {
            lineStyle: {
              width: 0,
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              width: 1,
              color: 'rgba(131,141,158,0.1)', // 设置网格的颜色
            },
          },
        },
        series: [
          {
            type: 'radar',
            lineStyle: {
              // show: false,
              width: 1,
              color: new echarts.graphic.RadialGradient(0.1, 0.6, 1, [
                {
                  color: '#018CF8',
                  offset: 0,
                },
                {
                  color: 'rgba(131,141,158)',
                  offset: 1,
                },
              ]),
            },
            label: {
              normal: {
                fontSize: 10,
              },
            },
            symbol: 'none',
            data: [
              {
                value: value2,
                name: '告警分布',
                areaStyle: {
                  color: new echarts.graphic.RadialGradient(0.1, 0.6, 1, [
                    {
                      color: '#018CF8',
                      offset: 0,
                    },
                    {
                      color: '#00FFF6',
                      offset: 1,
                    },
                  ]),
                },
              },
            ],
          },
        ],
      })
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    },
    // 运维工单统计数据
    orderCount() {
      getAction(this.url.orderCount).then((res) => {
        if (res.success) {
          this.count = res.result.number
          this.count.all = this.count[0].value.toString()
          this.count.done = this.count[1].value.toString()
          this.percent = parseInt((this.count[1].value / this.count[0].value) * 100)
        }
      })
    },
    // 运维工单环形图数据
    typeCount() {
      getAction(this.url.typeCount).then((res) => {
        if (res.code == 200) {
          this.workOrder(res.result)
        }
      })
    },
    // 运维工单统计环形图
    workOrder(data) {
      let num = []
      if (data && data.length > 0) {
        data.forEach((e) => {
          num.push(e.number)
        })
      }
      let myChart = this.$echarts.init(document.getElementById('workOrder'))
      myChart.setOption({
        tooltip: {
          show: true,
          trigger: 'item',
          transitionDuration: 0, //echart防止tooltip的抖动
          formatter(data) {
            let value = data.name + ':' + data.value + '%'
            return value
          },
        },
        legend: {
          top: 'center',
          right: '20%',
          orient: 'vertical',
          icon: 'circle',
          textStyle: {
            color: '#ffffff',
          },
          itemHeight: 5,
          itemWidth: 5,
        },
        series: [
          {
            type: 'pie',
            color: ['#4B84E6', '#54E999', '#20C2FA', '#FED708'],
            radius: ['50%', '65%'],
            center: ['30%', '50%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 1,
              borderColor: '',
              borderWidth: 2,
            },
            label: {
              show: false,
              position: 'center',
            },
            labelLine: {
              show: false,
            },
            data: data,
          },
        ],
      })
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    },
    // 设备监控性能内存数据
    monitor() {
      getAction(this.url.monitor).then((res) => {
        if (res.success) {
          let data = []
          if (res.result.length > 5) {
            data = res.result.slice(0, 5)
          } else {
            data = res.result
          }
          this.equipmentPerformance(data)
        }
      })
    },
    getCPU() {
      getAction(this.url.cpu).then((res) => {
        if (res.success) {
          let data = []
          if (res.result.length > 5) {
            data = res.result.slice(0, 5)
          } else {
            data = res.result
          }
          this.equipmentPerformance(data)
        }
      })
    },
    // 设备监控性能象形柱状图
    equipmentPerformance(data) {
      let xArr = []
      let value = []
      if (data && data.length > 0) {
        data.forEach((item, index) => {
          if (index == 1 || index == 3) {
            xArr.push('\n' + data[index].name)
          } else {
            xArr.push(data[index].name)
          }
          value.push(item.value)
        })
      }
      let myChart = this.$echarts.init(document.getElementById('equipmentPerformance'))
      myChart.setOption({
        legend: {
          data: ['CPU', '内存'],
          left: 'right',
          textStyle: {
            color: '#fff',
          },
          itemHeight: 10,
          itemWidth: 10,
        },
        tooltip: {
          type: true,
          transitionDuration: 0, //echart防止tooltip的抖动
          formatter(data) {
            let value = data.name + ' : ' + data.value + '%'
            return value
          },
        },
        grid: {
          left: '5%',
          top: '15%',
          right: '10%',
          bottom: '5%',
          containLabel: true,
        },
        xAxis: {
          data: xArr,
          axisLine: {
            lineStyle: {
              type: 'solid',
              color: '#5a595f', //左边线的颜色
              width: '1', //坐标线的宽度
            },
          },
          axisLabel: {
            interval: 0,
            rotate: 20,
            formatter: (value) => {
              if (value.length > 12) {
                return value.substring(0, 11) + '...'
              } else {
                return value
              }
            },
          },
        },
        yAxis: {
          type: 'value',
          axisLine: {
            show: false,
            lineStyle: {
              color: '#97ABEA',
            },
          },
          axisLabel: {
            formatter: '{value}%',
          },
          axisTick: {
            show: false,
          },
          //网格线颜色
          splitLine: {
            show: true,
            lineStyle: {
              color: ['#999999'],
              opacity: 0.1,
              width: 1,
              type: 'solid',
            },
          },
        },
        series: [
          //中间的圆柱
          {
            type: 'bar',
            showBackground: true, // 柱状背景容器
            backgroundStyle: {
              color: 'rgba(8,28,78)',
              barBorderRadius: 10,
            },
            barWidth: 15,
            data: value,
            itemStyle: {
              normal: {
                borderWidth: 1,
                barBorderRadius: [15, 15, 20, 20],
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 1,
                  y2: 0,
                  colorStops: [
                    {
                      offset: 0,
                      color: '#1448D2',
                    },
                    {
                      offset: 1,
                      color: '#2FE0FC',
                    },
                  ],
                  global: false, // 缺省为 false
                },
                shadowBlur: 5,
                shadowColor: 'rgba(255, 255, 255, 0.18000000715255737)',
                shadowOffsetX: 0,
                shadowOffsetY: -2,
              },
            },
          },
          //顶部圆柱帽子
          {
            tooltip: {
              show: true,
              transitionDuration: 0, //echart防止tooltip的抖动
            },
            type: 'pictorialBar',
            color: '#17A5FF',
            itemStyle: {
              normal: {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 1,
                  y2: 0,
                  colorStops: [
                    {
                      offset: 0,
                      color: '#1448D2', // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: '#2FE0FC', // 100% 处的颜色
                    },
                  ],
                  global: false, // 缺省为 false
                },
                borderWidth: 1,
                borderColor: '#17A5FF',
                shadowBlur: 5,
                shadowColor: 'rgba(255, 255, 255, 0.18000000715255737)',
                shadowOffsetX: 0,
                shadowOffsetY: 2,
              },
            },
            symbol: 'circle',
            symbolSize: ['13', '5'],
            symbolPosition: 'end',
            data: value, //具体位置需要自己细微调整
            z: 3,
          },
          //底座圆柱
          // {
          //   type: 'pictorialBar',
          //   itemStyle: {
          //     normal: {
          //       color: {
          //         type: 'linear',
          //         x: 0,
          //         y: 0,
          //         x2: 1,
          //         y2: 0,
          //         colorStops: [
          //           {
          //             offset: 0,
          //             color: '#1448D2', // 0% 处的颜色
          //           },
          //           {
          //             offset: 1,
          //             color: '#2FE0FC', // 100% 处的颜色
          //           },
          //         ],
          //         global: false, // 缺省为 false
          //       },
          //       borderWidth: 1,
          //       borderColor: '#18CEE2',
          //       shadowBlur: 5,
          //       shadowColor: 'rgba(255, 255, 255, 0.18000000715255737)',
          //       shadowOffsetX: 0,
          //       shadowOffsetY: -2,
          //     },
          //   },
          //   symbol: 'circle',
          //   symbolSize: ['9', '5'],
          //   symbolPosition: 'end',
          //   data: [3, 3, 3, 3, 3], //具体细微差距还是得进行一个自己调整
          //   z: 3,
          // },
        ],
      })
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    },
    //告警词云数据
    alarmWords() {
      getAction(this.url.alarmWords).then((res) => {
        if (res.success) {
          if (res.result.length > 0) {
            res.result.forEach((item) => {
              this.nameList.push(item.name)
              this.nameList.slice(0, 6)
            })
          }
        }
      })
    },
  },
}
</script>

<style lang="less" scoped>
.left-bottom-table::-webkit-scrollbar {
  display: none;
  /*隐藏滚动条*/
}

.topBox {
  height: 90px;
  width: 100%;
  background: url('../../assets/bigScreen/shanxi/Title.png') center top no-repeat;
  font-size: 50px;
  color: #ffffff;
  align-items: center;
  text-align: center;
  text-shadow: 0px 5px 0px rgba(7, 58, 152, 0.5);
}

.topTitle {
  display: flex;
  align-items: center;
  font-size: 0.225rem;
}

.background {
  height: 100%;
  width: 100%;
  background-image: url('../../assets/bigScreen/shanxi/bg.png');
  padding: 0 12px 0 38px;
}

.left_top {
  border-top: 1px solid #1d5acc;
  width: 100%;
  height: 30.8%;
  background: linear-gradient(180deg, rgba(65, 113, 234, 0.2), rgba(7, 58, 152, 0));
  // opacity: 0.8;

  .left_top_title {
    letter-spacing: 3px;
    height: 21px;
    font-size: 20px;
    line-height: 19px;
    font-family: 59--Regular;
    font-weight: 400;
    color: #ccfafe;
  }
}

.center_left {
  border-top: 1px solid #1d5acc;
  width: 100%;
  height: 30.8%;
  background: linear-gradient(180deg, rgba(65, 113, 234, 0.2), rgba(7, 58, 152, 0));
  // opacity: 0.8;
  margin-top: 24px;

  .center_left_title {
    letter-spacing: 3px;
    height: 21px;
    font-size: 20px;
    line-height: 19px;
    font-family: 59--Regular;
    font-weight: 400;
    color: #ccfafe;
  }
}

.left_bottom {
  border-top: 1px solid #1d5acc;
  width: 100%;
  height: 30.8%;
  background: linear-gradient(180deg, rgba(65, 113, 234, 0.2), rgba(7, 58, 152, 0));
  // opacity: 0.8;
  margin-top: 24px;
  overflow: hidden;
  border-radius: 0.075rem;

  .left_bottom_title {
    letter-spacing: 3px;
    height: 21px;
    font-size: 20px;
    line-height: 19px;
    font-family: 59--Regular;
    font-weight: 400;
    color: #ccfafe;
  }

  .left-bottom-core {
    width: 100%;
    padding: 0.1rem 0.25rem;

    .left-bottom-table {
      width: 100%;
      height: calc(100% - 30px);
      overflow-x: auto;

      .seamless-warp {
        width: 100%;
        height: 100%;

        div {
          align-items: center;
          width: 100%;
          line-height: 0.525rem;
          /* 42/80 */
          display: flex;
          justify-content: space-around;

          span {
            width: 100%;
            color: rgba(255, 255, 255, 0.75);
            font-size: 0.175rem;
            text-align: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
    }
  }
}

.center_top {
  border-top: 1px solid #1d5acc;
  width: 100%;
  height: 30.8%;
  background: linear-gradient(180deg, rgba(65, 113, 234, 0.2), rgba(7, 58, 152, 0));
  // opacity: 0.8;
  padding: 14px 24px;

  .equipmentTotal {
    display: flex;
    justify-content: space-between;

    .stateTitle {
      font-size: 20px;
      color: #5bb0ff;
      margin-right: 8px;
    }

    .unit {
      font-size: 20px;
      color: #5bb0ff;
      font-family: Adobe Heiti Std;
      margin-left: 8px;
    }
  }

  .center_top_bottom {
    display: flex;
    height: 100%;

    .center_top_bottom_box {
      height: 100%;
      margin: 15px auto;
    }

    .center_top_bottom_title {
      font-size: 14px;
      font-weight: 400;
      color: #b6f0ff;
      opacity: 0.8;
      display: block;
      text-align: center;
    }
  }
}

.center_bottom {
  width: 100%;
  height: 61%;
  margin-top: 24px;
  background: linear-gradient(180deg, rgba(65, 113, 234, 0.2), rgba(7, 58, 152, 0));
  // opacity: 0.8;
  padding: 0px 24px 0 24px;
  position: relative;

  .center_bottom_img {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}

.right_top {
  border-top: 1px solid #1d5acc;
  width: 100%;
  height: 30.8%;
  background: linear-gradient(180deg, rgba(65, 113, 234, 0.2), rgba(7, 58, 152, 0));
  // opacity: 0.8;

  .right_top_title {
    letter-spacing: 3px;
    height: 21px;
    font-size: 20px;
    line-height: 19px;
    font-family: 59--Regular;
    font-weight: 400;
    color: #ccfafe;
  }

  .right_top_center {
    display: flex;
    padding: 0 24px;
    justify-content: space-between;
  }
}

.right_center {
  height: calc(30.8% - 24px);
  border-top: 1px solid #1d5acc;
  width: 100%;
  background: linear-gradient(180deg, rgba(65, 113, 234, 0.2), rgba(7, 58, 152, 0));
  // opacity: 0.8;
  margin-top: 24px;

  .right_center_title {
    letter-spacing: 3px;
    height: 21px;
    font-size: 20px;
    line-height: 19px;
    font-family: 59--Regular;
    font-weight: 400;
    color: #ccfafe;
  }
}

.right_bottom {
  border-top: 1px solid #1d5acc;
  width: 100%;
  height: 30.8%;
  background: linear-gradient(180deg, rgba(65, 113, 234, 0.2), rgba(7, 58, 152, 0));
  margin-top: 24px;

  .right_bottom_title {
    letter-spacing: 3px;
    height: 21px;
    font-size: 20px;
    line-height: 19px;
    font-family: 59--Regular;
    font-weight: 400;
    color: #ccfafe;
  }
}

.style3 {
  font-size: 26px;
  color: rgba(240, 120, 23, 0.973);
  font-weight: 600;
  margin-left: 50px;
}

.style4 {
  font-size: 23px;
  color: rgb(201, 171, 38);
  font-weight: 600;
  margin-left: 150px;
}

.style1 {
  font-size: 18px;
  color: rgb(160, 168, 241);
  font-weight: 600;
  margin-left: 70px;
}

.style2 {
  font-size: 20px;
  color: rgb(194, 240, 111);
  font-weight: 600;
  margin-left: 90px;
}

.style5 {
  font-size: 16px;
  color: rgb(74, 169, 233);
  font-weight: 600;
  margin-left: 90px;
}

.style6 {
  font-size: 16px;
  color: rgb(74, 169, 233);
  font-weight: 600;
  margin-left: 90px;
}

.networkTopology {
  margin-top: -18px;
  font-size: 22px;
  color: #cdfbff;
  height: 50px;
  line-height: 59px;
  letter-spacing: 2px;
  width: 317px;
  text-align: center;
  background-image: url('../../assets/bigScreen/networkTopology.png');
}

.row-class {
  width: 100%;
  height: calc(100% - 90px);
  padding-top: 24px;
}

.col-class {
  height: 100%;
}

.topo-class {
  width: 100%;
  height: 100%;
  padding-top: 10px;
  position: relative;
}

.workOrder_class {
  border: 1px solid rgba(155, 155, 155, 0.3);
  background-color: rgba(0, 0, 24, 0.3);
  height: 60%;
  width: 90%;
  margin: 14px auto 0 auto;
}
</style>
<style scoped>
.ant-carousel >>> .slick-slide {
  text-align: center;
  line-height: 360px;
  overflow: hidden;
}

.onButton {
  width: 71px;
  height: 25px;
  background: rgba(14, 139, 255, 0.1);
  font-size: 14px;
  color: #26b3fe;
  text-align: center;
  background-image: url('../../assets/bigScreen/shanxi/CPU.png');
  cursor: pointer;
}

.notButton {
  width: 71px;
  height: 25px;
  background: rgba(14, 139, 255, 0.1);
  font-size: 14px;
  text-align: center;
  color: #26b3fe;
  background-image: url('../../assets/bigScreen/shanxi/CPU.png');
  opacity: 0.5;
  cursor: pointer;
}

.equipmentPerformanceTitle {
  display: flex;
  margin-right: 17px;
  margin-top: 29px;
}
</style>