<template>
  <div class="panel-el">
    <vue-easy-tree ref="veTree" node-key="id" :data="dataList" :props="replaceFields" height="calc(100vh - 325px)">
      <div class="custom-tree-node" slot-scope="{ data }">
        <div :draggable="draggable" @mousedown="startDrag($event, data)" style="display: flex; align-items: center">
          <!-- <img v-if="data.icon != null && data.icon.length > 0" :src="picSrc + '/' + data.icon"
                style="width: 20px; height: 20px" /> -->
          <!-- <div
            v-if="data.icon != null && data.icon.length > 0"
            style="width: 18px; height: 18px; color: red"
            v-html="svgData"
          ></div> -->
          <YqSvg
            v-if="data.typeIcon"
            :type="data.typeIcon"
            :outSide="outSideUrl + data.typeIcon"
            style="color: #1677ff; font-size: 16px; margin-right: 5px"
          />
          <span>{{ data.typeName }}</span>
        </div>
      </div>
    </vue-easy-tree>
    <ele-modal ref="eleModal"></ele-modal>
  </div>
</template>

<script>
import { ajaxGetDictItems } from '@/api/api'
import { getAction, httpAction } from '@/api/manage'
import eleModal from './eleModal.vue'
import VueEasyTree from '@wchbrad/vue-easy-tree'
import YqSvg from '@/components/tools/SvgIcon/index.js'
export default {
  name: 'PanelElements',
  components: {
    eleModal,
    VueEasyTree,
    YqSvg,
  },
  props:{
    panelElementTypes:{
      type:Array,
      default:()=>[],
      required:true,
    }
  },
  data() {
    return {
      draggable: true,
      dataList: [],
      replaceFields: {
        title: 'name',
        key: 'id',
      },
      outSideUrl: window._CONFIG['downloadUrl'] + '/',
    }
  },
  created() {
    this.dataList = [
      {
        typeName: '面板',
        typeCode: 'panel',
        id: 'panel',
        typeIcon: '',
        nodeType: 'panelNode',
        children: [],
      },
    ]
    this.getData()
  },
  mounted() {},
  methods: {
    startDrag(e, data) {
      this.$emit('startDrag', e, data)
    },
    async getData() {
      await ajaxGetDictItems('device_Config_value', null).then((res) => {
        if (res.success) {
          let dicts = res.result
          dicts.forEach((dict) => {
            let tem = {
              typeName: dict.text,
              typeCode: dict.value,
              id: dict.value,
              typeIcon: '',
              nodeType: '',
              children: [],
            }
            this.dataList.push(tem)
          })
          // this.dictOptions = res.result
        }
      })
      this.dataList.forEach((el) => {
        let childs = this.panelElementTypes.filter((ele) => ele.dictValue === el.typeCode)
        childs.forEach((cel) => {
          cel.nodeType = 'panelElement'
        })
        el.children.push(...childs)
      })
      
      // getAction(document.location.origin + '/panelElements/elements.json')
      //   .then((res) => {
      //     if (res) {
      //       this.dataList = res
      //     }
      //   })
      //   .catch((error) => {})
    },
  },
}
</script>

<style lang="less" scoped>
.panel-el {
  width: 200px;
  height: 100%;
  padding: 12px 0;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
}
.title {
  display: flex;
  justify-content: space-between;
  height: 40px;
  line-height: 40px;
  padding-left: 10px;
  background: rgba(207, 207, 207, 0.24);

  .title-btn {
    font-size: 14px;
    margin: 4px 10px 0 0;
  }
}
</style>