<template>
  <a-table
    :columns="columns"
    :data-source="data"
    ref="table"
    size="middle"
    bordered
    rowKey="id"
  >
  </a-table>
</template>
<script>
export default {
  // 历史信息
  name: 'table1',
  data() {
    return {
      columns: [
        // {
        //     title: '序号',
        //     dataIndex: '',
        //     key: 'rowIndex',
        //     width: 60,
        //     align: 'center',
        //     customRender: function(t, r, index) {
        //         return parseInt(index) + 1
        //     }
        // },
        {
          title: '步骤',
          align: 'center',
          dataIndex: ''
        },
        {
          title: '动作',
          align: 'center',
          dataIndex: 'title'
        },
        {
          title: '详情',
          align: 'center',
          dataIndex: ''
        },
        {
          title: '操作人',
          align: 'center',
          dataIndex: ''
        },
        {
          title: '时间',
          align: 'center',
          dataIndex: ''
        },
        {
          title: '附件',
          align: 'center',
          dataIndex: ''
        }
      ],
      data: [
        {
          id: '1',
          title: '嗯嗯嗯'
        }
      ]
    }
  }
}
</script>
<style scoped></style>
