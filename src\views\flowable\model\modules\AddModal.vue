<template>
  <a-modal :title="title" :width='800' :maskClosable='false' :destroyOnClose="true" :visible='visible'
    :confirmLoading="confirmLoading" :okButtonProps="{ class:{'jee-hidden': disableSubmit} }" @ok="handleOk"
    @cancel="handleCancel" cancelText="关闭">
    <a-spin :spinning="confirmLoading">
      <j-form-container :disabled="disableSubmit">
        <a-form-model ref="form" slot="detail" :model="model" :rules="validatorRules" :label-col="labelCol"
          :wrapper-col="wrapperCol">
          <a-row :gutter='24'>
            <a-col v-bind='formItemLayout'>
              <a-form-model-item label='编码' prop='key'>
                <a-input v-model='model.key' :allow-clear='true' autocomplete='off' placeholder='请输入模型编码' />
              </a-form-model-item>
            </a-col>
            <a-col v-bind='formItemLayout'>
              <a-form-model-item label='名称' prop='name'>
                <a-input v-model='model.name' :allow-clear='true' autocomplete='off' placeholder='请输入模型名称' />
              </a-form-model-item>
            </a-col>
            <a-col v-bind='formItemLayout'>
              <a-form-model-item label='类别' prop='category'>
                <!-- <j-dict-select-tag v-model='model.category' :trigger-change='true' dictCode='bpm_process_type'
                  placeholder='请选择模型类别' @change='changeCategory' /> -->
                <a-select v-model='model.category' placeholder='请选择模型类别' @change='changeCategory'>
                  <a-select-option v-for="item in dictOptions" :key="item.value">{{ item.text }}</a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <!--          <a-col v-bind='formItemLayout' v-if='busEditable'>
            <a-form-model-item label="业务" prop='tenantId' >
              <a-select
                :allow-clear='true'
                :getPopupContainer='(node) => node.parentNode'
                v-model='model.tenantId'
                placeholder='请选择业务'>
                <a-select-option :value='item.id' v-for='(item,index) in businessList' :key='"business_"+index'>{{item.businessTitle}}</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>-->
            <a-col v-bind='formItemLayout'>
              <a-form-model-item label='描述' prop='description'>
                <a-textarea v-model='model.description' :allow-clear='true' :auto-size='{minRows:1,maxRows:6}'
                  autocomplete='off' placeholder='请输入模型的描述信息' />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </j-form-container>
    </a-spin>
  </a-modal>
</template>

<script>
  import {
    getAction,
    httpAction
  } from '@/api/manage'
  import {
    YqFormVerification
  } from '@/mixins/YqFormVerification'
  import {
    ajaxGetDictItems,
    getDictItemsFromCache
  } from '@/api/api'

  export default {
    name: 'AddModal',
    mixins: [YqFormVerification],
    data() {
      return {
        title: '操作',
        visible: false,
        disableSubmit: false,
        /* model: {
           key: '',
           name: '',
           category: undefined,
           tenantId: 'undefined',
           description: ''
         },*/
        model: {},
        busEditable: false,
        formItemLayout: {
          md: {
            span: 24
          },
          sm: {
            span: 24
          }
        },
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          },
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          },
        },
        confirmLoading: false,
        validatorRules: {
          name: [{
              required: true,
              message: '请输入模型名称!'
            },
            {
              min: 2,
              max: 30,
              message: '模型名称长度应[2-30]个字符之间',
              trigger: 'change'
            },
            {
              validator: this.validateNameFun_1
            }
          ],
          key: [{
              required: true,
              message: '请输入模型编码!'
            },
            {
              validator: this.validateCodeFun_2,
            },
            {
              min: 2,
              max: 30,
              message: '模型编码长度应在[2-30]个字符之间',
              trigger: 'change'
            }
          ],
          category: [{
            required: true,
            message: '请选择模型类别!'
          }],
          description: [{
            required: false,
            min: 0,
            max: 200,
            message: '描述长度应在[0-200]个字符之间',
            trigger: 'change'
          }],
          tenantId: [{
            required: true,
            message: '请选择业务!'
          }],
        },
        url: {
          add: '/flowable/model/save',
          edit: '/test/jeecgDemo/edit',
          business: '/business/info/queryList'
        },
        businessList: [],
        dictOptions: []
      }
    },
    created() {
      //this.loadBusiness()
      this.initDictData()
    },
    methods: {
      changeCategory(value) {
        this.busEditable = value === "business"
      },
      add() {
        this.busEditable = false
        this.edit({})
      },
      edit(record) {
        this.model = Object.assign({}, record)
        this.busEditable = this.model.category === 'business'
        this.visible = true
      },
      initDictData() {
        //根据字典Code, 初始化字典数组
        ajaxGetDictItems('bpm_process_type', null).then((res) => {
          if (res.success) {
            this.dictOptions = res.result
          }
        })
      },
      loadBusiness() {
        this.confirmLoading = true
        this.businessList = []
        getAction(this.url.business, {
          categoryId: 'business'
        }).then((res) => {
          if (res.success) {
            this.businessList = res.result
          } else {
            this.$message.warning(res.message)
          }
        }).finally(() => {
          this.confirmLoading = false
        })
      },
      /* validatorName(rule, value, callback){
          if (!/^[a-zA-Z\u4e00-\u9fa5][a-zA-Z0-9\u4e00-\u9fa5]*$/.test(value)) {
           callback(new Error('格式不正确!,为字母数字或汉字且开头不能是数字'))
         } else {
           callback()
         }
       },
       validatorKey(rule, value, callback){
          if (!/^[a-zA-Z][a-zA-Z0-9]*$/.test(value)) {
           callback(new Error('格式不正确!,为字母数字且开头不能是数字'))
         } else {
           callback()
         }
       },*/
      close() {
        this.visible = false;
      },
      handleOk() {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if (!this.model.id) {
              httpurl += this.url.add;
              method = 'post';
            } else {
              httpurl += this.url.edit;
              method = 'put';
            }
            let formData = JSON.parse(JSON.stringify(this.model))
            if (!this.busEditable) {
              formData.tenantId = ''
            }
            httpAction(httpurl, formData, method).then((res) => {
              if (res.success) {
                that.$message.success("添加成功！");
                that.$emit('ok');
              } else {
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
              that.close();
            })
          }
        })
      },
      handleCancel() {
        this.close()
      }
    }
  }
</script>
<style scoped lang='less'>
  @import '~@assets/less/YQNormalModal.less';
</style>