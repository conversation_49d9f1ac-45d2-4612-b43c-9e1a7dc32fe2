<template>
  <div>
    <!-- 抽屉 -->
    <a-drawer title='表达式列表' :width='screenWidth' @close='onClose' :visible='visible'>
      <!-- 抽屉内容的border -->
      <div
        :style="{
          padding: '10px',
          border: '1px solid #e9e9e9',
          background: '#fff',
        }"
      >
        <div class='table-page-search-wrapper'>
          <a-row>
            <a-col :md='2' :sm='24' class='table-operator'>
              <a-button style='margin-bottom: 10px' @click='handleAdd'>新增</a-button>
            </a-col>
          </a-row>
        </div>
        <div>
          <a-table
            ref='table'
            rowKey='id'
            size='middle'
            :columns='columns'
            :dataSource='dataSource'
            :scroll='dataSource.length>0?{x:"max-content"}:{}'
            :pagination='false'
            :loading='loading'
            @change='handleTableChange'
          >
            <span slot='action' slot-scope='text, record' class='caozuo'>
              <a @click='handleEdit(record)'>编辑</a>
              <a-divider type='vertical' />
              <a-popconfirm title='确定删除吗?' @confirm='() => handleDelete(record.id)'>
                <a>删除</a>
              </a-popconfirm>
            </span>
            <template slot='contentSlot' slot-scope='text'>
              <a-tooltip placement='topLeft'>
                <template>
                  <span slot='title' v-html='text'></span>
                </template>
                <span class='tooltip'>{{ text}}</span>
              </a-tooltip>
            </template>
          </a-table>

        </div>
      </div>
    </a-drawer>
    <GrokFormulaModal ref='modalForm' @ok='modalFormOk'></GrokFormulaModal>
    <!-- 字典数据 -->
  </div>
</template>


<script>
import '@assets/less/TableExpand.less'
import GrokFormulaModal from '@views/eventManagement/grok/GrokFormulaModal.vue'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'

export default {
  name: 'DictItemList',
  mixins: [JeecgListMixin],
  components: { GrokFormulaModal },
  data() {
    return {
      columns: [
        {
          title: '数据值',
          dataIndex: 'grokFormula',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 50px;max-width:300px'
            return { style: cellStyle }
          },
          scopedSlots: {
            customRender: 'contentSlot'
          }
        },
        {
          title: '操作',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' },
          width: 150,
        }
      ],
      title: '操作',
      visible: false,
      screenWidth: '800px',
      grokId: '',
      grokCode: '',
      url: {
        list: '/grok/queryUtlGrokFormula',
        delete: '/grok/delUtlGrokFormula',
      },
      queryParam: {
        grokId: ''
      },
      disableMixinCreated:true,
    }
  },
  created() {
    // 当页面初始化时,根据屏幕大小来给抽屉设置宽度
    //this.resetScreenSize()
  },
  methods: {
    edit(record) {
      this.grokId = record.id
      this.grokCode = record.grokCode
      this.queryParam.grokId = record.id
      this.visible = true
      this.loadData()
    },
    // 添加字典数据
    handleAdd() {
      this.$refs.modalForm.add(this.grokId, this.grokCode)
      this.$refs.modalForm.title = '新增'
    },
    showDrawer() {
      this.visible = true
    },
    onClose() {
      this.visible = false
      this.dataSource = []
    }
  }
}
</script>
<style lang='less' scoped>
/deep/ .data-rule-invalid {
  background: #f4f4f4;
  color: #bababa;
}

@import '~@assets/less/common.less';
@media (max-width: 800px) {
  ::v-deep .ant-drawer-content-wrapper {
    max-width: 100vw;
    margin: 0;
  }
}

/*表头样式*/
::v-deep .ant-table-thead > tr > th {
  text-align: center;
  white-space: nowrap;
}

/*内容对齐方式、省略显示*/
::v-deep .ant-table-tbody > tr > td {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

::v-deep .ant-table-body {
  overflow-x: auto !important;
}
</style>