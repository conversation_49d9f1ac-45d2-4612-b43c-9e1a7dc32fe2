<template>
  <div class="table-module" ref="tableMoudle">
    <div class="table-title flex-between align-center">
      <div class="table-title-left flex flex align-center">
        <div style="margin-right: 5px"><img src="@/assets/bigScreen/9.png" alt /></div>
        <div>服务器资源统计</div>
      </div>
    </div>
    <div class="table-box" ref="tableBox">
      <a-table bordered :row-key='(record, index) => {return index}' :pagination="false" :columns="columns" :data-source="tableData"
        :rowClassName="tableRowName" :scroll="{ y: tableHeight }">
        <template slot="networkCount" slot-scope="text">
          <span style="color: #0998e3">{{ text }}</span>
        </template>
        <template slot="cpuRate" slot-scope="text">
          <span style="color: #0cba3c">{{ text }}</span>
        </template>
        <template slot="swapTotal" slot-scope="text">
          <span style="color: #8f1bc8">{{ text }}</span>
        </template>
        <template slot="memtotal" slot-scope="text">
          <span style="color: #e4c905">{{ text }}</span>
        </template>
      </a-table>
    </div>
  </div>
</template>

<script>
  import {
    getAction
  } from '@/api/manage'
  export default {
    data() {
      return {
        columns: [{
            title: '名称',
            dataIndex: 'name',
            align: 'center',
            scopedSlots: {
              customRender: 'name'
            },
          },
          {
            title: 'IP地址',
            dataIndex: 'ip',
            align: 'center',
            scopedSlots: {
              customRender: 'ip'
            },
          },
          {
            title: '运行时间',
            dataIndex: 'sysUptime',
            align: 'center',
            scopedSlots: {
              customRender: 'sysUptime'
            },
          },
          {
            title: '进程数',
            dataIndex: 'processCount',
            align: 'center',
            scopedSlots: {
              customRender: 'processCount'
            },
          },
          {
            title: 'CPU平均使用率（%）',
            dataIndex: 'cpuRate',
            align: 'center',
            scopedSlots: {
              customRender: 'cpuRate'
            },
          },
          {
            title: '网络I/O',
            dataIndex: 'networkCount',
            align: 'center',
            scopedSlots: {
              customRender: 'networkCount'
            },
          },
          {
            title: '物理内存（GB）',
            dataIndex: 'memtotal',
            align: 'center',
            scopedSlots: {
              customRender: 'memtotal'
            },
          },
          {
            title: '虚拟内存（GB）',
            dataIndex: 'swapTotal',
            align: 'center',
            scopedSlots: {
              customRender: 'swapTotal'
            },
          },
        ],
        tableData: [],
        tableHeight: 0,
        url: {
          list: '/device/statistics/server'
        },
      }
    },
    created() {
      this.initData()
    },
    mounted() {
      let rect = this.$refs.tableBox.getBoundingClientRect()
      this.tableHeight = rect.height - 32 - 37
    },
    methods: {
      tableRowName(record, index) {
        return index % 2 === 0 ? '' : 'gray-row'
      },
      initData() {
        getAction(this.url.list).then((res) => {
          this.tableData = res.result
        })
      },
    },
  }
</script>

<style lang="less" scoped>
  .table-module {
    height: 100%;
    background-color: #111217;
    padding: 16px;

    .table-title {
      height: 32px;

      .table-title-left {
        color: #45c5e0;
        font-size: 16px;
      }

      .table-title-btn {
        margin-left: 16px;
      }
    }

    .table-box {
      margin-top: 16px;
      width: 100%;
      height: calc(100% - 32px - 16px);
    }
  }
</style>