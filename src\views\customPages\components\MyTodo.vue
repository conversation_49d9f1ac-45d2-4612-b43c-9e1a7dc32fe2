<template>
  <a-card :loading='loading' style='width: 100%; height: 100%'>
    <div slot='title'>
      <a-icon type='clock-circle' />
      我的待办【<a slot='extra' @click='goPage'>{{ dataSourceCount1 }}</a>】
      <a-tooltip title='刷新'>
        <a-icon type='sync'
                style='color: #ccc; cursor: pointer; font-size: 14px'
                @click='loadTodoList' />
      </a-tooltip>
    </div>
    <a slot='extra' @click='goPage'>更多
      <a-icon type='double-right' />
    </a>
    <a-table
      :body-style="{ overflowX: 'auto' }"
      size='middle'
      rowKey='id'
      bordered
      :columns='columnstodo'
      :dataSource='dataSource1'
      :pagination='false'>
              <span slot='action' slot-scope='text, record'>
                <a @click='btnView(record)'>查看</a>
                <span v-if="record.assignee == null || record.assignee == ''">
                  <a-divider type='vertical' />
                  <a @click='btnClaim(record)'>认领并处理</a>
                </span>
                <span v-if="
                    record.assignee === $store.getters.userInfo.username &&
                    (record.endTime == null || record.endTime == '') &&
                    record.claimTime != null &&
                    record.claimTime != ''
                  ">
                  <a-divider type='vertical' />
                  <a @click='btnUnclaim(record)'>取消认领</a>
                </span>
                <span v-if="record.endTime == null && record.assignee != null && record.assignee != ''">
                  <a-divider type='vertical' />
                  <a @click='btnExcuteTask(record)'>处理</a>
                </span>
              </span>
    </a-table>
    <process-instance-info-modal ref="processInstanceInfoModalForm"></process-instance-info-modal>

    <!--    任务执行区域-->
    <execute-task v-if="dialogExecuteTaskVisible" :execute-task-id="executeTaskId"
                  :processInstanceId.sync="processInstanceId" :selectRow="selectionRow" :visible.sync="dialogExecuteTaskVisible"
                  @ok="loadTodoList"></execute-task>

  </a-card>
</template>

<script>
import { getAction } from '@api/manage'
import { taskTodoApi } from '@api/flowable'
import ProcessInstanceInfoModal from '@views/flowable/process-instance/modules/ProcessInstanceInfoModal.vue'
import executeTask from '@views/flowable/task-todo/modules/executeTask.vue'

export default {
  name: 'MyTodo',
  components: { executeTask, ProcessInstanceInfoModal },
  props: {},
  data() {
    return {
      dataSourceCount1: 0,
      dataSource1: [],
      url: {
        todoList: '/flowable/task/listTodo',
        withdrowUrl: '/flowable/processInstance/delete',
        applyUrl: '/business/actZBusiness/sbApply',
        deleteBusiness: '/business/actZBusiness/delete'
      },
      columnstodo: [
        {
          title: '流程实例名称',
          width: '26%',
          dataIndex: 'processInstanceName',
          align: 'center',
        },
        {
          title: '任务名称',
          width: '20%',
          dataIndex: 'name'
        },
        {
          title: '开始时间',
          width: '25%',
          dataIndex: 'createTime',
          align: 'center',
        },
        {
          title: '操作',
          width: '25%',
          dataIndex: 'action',
          scopedSlots: {
            customRender: 'action'
          },
          align: 'center',
        }
      ],
      dialogExecuteTaskVisible: false,
      selectionRow: {},
      executeTaskId: null,
      processInstanceId: null,
      loading: false,
    }
  },
  created() {
    this.loadTodoList()
  },
  mounted() {

  },
  computed: {
    tasktimestimp() {
      return this.$store.getters.tasktimestimp
    }
  },
  watch: {
    tasktimestimp: {
      handler(nval, oval) {
        this.loadTodoList()
      },
      deep: true,
      immediate: true,
    }
  },

  methods: {
    loadTodoList() {
      if (!this.url.todoList) {
        this.$message.error('请设置url.todoList属性!')
        return
      }
      this.loading = true
      getAction(this.url.todoList, {
        pageNo: 1,
        pageSize: 6
      }).then((res) => {
        if (res.success) {
          this.dataSource1 = res.result.records || res.result
          this.dataSourceCount1 = res.result.total
        } else {
          this.$message.warning(res.message)
        }
        this.loading = false
      })
    },
    //签收
    btnClaim(row) {
      let current = this
      taskTodoApi.claim({
        taskId: row.id
      }).then((res) => {
        current.loadTodoList()
        this.btnExcuteTask(row)
      })
    },
    //取消签收
    btnUnclaim(row) {
      let current = this
      taskTodoApi.unclaim({
        taskId: row.id
      }).then((res) => {
        current.$message.success(res.message)
        current.loadTodoList()
      })
    },
    goPage(arg) {
      this.$router.push({
        path: '/flowable/taskTodo'
      })
    },
    //处理
    btnExcuteTask(row) {
      this.selectionRow = row
      this.executeTaskId = row.id
      this.processInstanceId = row.processInstanceId
      this.dialogExecuteTaskVisible = true
    },

    btnView(record) {
      console.log(record)
      this.$refs.processInstanceInfoModalForm.init(record.processInstanceId)
      this.$refs.processInstanceInfoModalForm.title = '流程实例信息'
      this.$refs.processInstanceInfoModalForm.disableSubmit = false
    },

  }

}
</script>

<style scoped lang='less'>
/deep/ .ant-card-body{
  height: calc(100% - 56px);
  overflow-y: auto;
}
</style>