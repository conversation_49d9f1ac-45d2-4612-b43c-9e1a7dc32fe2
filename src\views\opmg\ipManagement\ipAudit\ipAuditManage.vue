<template>
  <div style="height:100%">
    <keep-alive exclude='ipAuditDetails'>
      <component style="height:100%" :is="pageName" :data="data" />
    </keep-alive>
  </div>
</template>
<script>
  import ipAuditList from './ipAuditList'
  import ipAuditDetails from './modules/ipAuditDetails'
  export default {
    name: "ipAuditManage",
    data() {
      return {
        isActive: 0,
        data: {},
      }
    },
    components: {
      ipAuditList,
      ipAuditDetails
    },
    created() {
      this.pButton1(0);
    },
    //使用计算属性
    computed: {
      pageName() {
        switch (this.isActive) {
          case 0:
            return "ipAuditList";
          default:
            return "ipAuditDetails";
        }
      }
    },
    methods: {
      pButton1(index) {
        this.isActive = index;
      },
      pButton2(index, item) {
        this.isActive = index;
        this.data = item
      }
    }
  }
</script>