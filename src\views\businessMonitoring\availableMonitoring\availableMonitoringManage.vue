<template>
  <div style="height:100%;">
    <keep-alive exclude='availableMonitoringInfo,configurationStep'>
      <component :is="pageName" :data="data"/>
    </keep-alive >
  </div>
</template>
<script>
import availableMonitoringList from './availableMonitoringList'
import availableMonitoringInfo from './availableMonitoringInfo'
import configurationStep from './configurationStep.vue'
export default {
  name: "availableMonitoringManage",
  data() {
    return {
      isActive: 0,
      data:{},
    };
  },
  components: {
    availableMonitoringList,
    availableMonitoringInfo,
    configurationStep
  },
  created(){
    this.pButton1(0);
  },
  //使用计算属性
  computed: {
    pageName() {
      switch (this.isActive) {
        case 0:
          return "availableMonitoringList";
          break;
        case 1:
          return "availableMonitoringInfo";
          break;
        default:
          return "configurationStep";
          break;
      }
    }
  },
  methods: {
    pButton1(index) {
      this.isActive = index;
    },
    pButton2(index,item) {
      this.isActive = index;
      this.data = item;
    }
  }
}
</script>