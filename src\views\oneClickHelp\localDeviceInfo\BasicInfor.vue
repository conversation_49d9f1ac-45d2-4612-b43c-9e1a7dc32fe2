<template>
  <div class='info-wrapper'>
    <div class='left-info'>
      <terminal-info :info='terminalInfo' @handleRefresh='handleRefresh' :loading='loading'></terminal-info>
    </div>
    <div class='right-info'>
      <div class='right-top'>
        <info-statistic :info='statisticInfo'></info-statistic>
      </div>
      <div class='right-middle'>
        <div class='right-middle-div right-middle-left'>
          <cpu-info style='height: 100%' :info='cupInfo' :loading='loading'></cpu-info>
        </div>
        <div class='right-middle-div right-middle-right'>
          <memory-info style='height: 100%' :info='memoryInfo' :loading='loading'></memory-info>
        </div>
      </div>
      <div class='right-middle right-middle-bottom'>
        <div class='right-middle-div right-middle-left'>
          <disk-info style='height: 100%' :info='diskInfo' :loading='loading'></disk-info>
        </div>
        <div class='right-middle-div right-middle-right'>
          <application-info style='height: 100%' :info='appInfo' :loading='loading'></application-info>
        </div>
      </div>
<!--      <div class='right-bottom'>-->
<!--        <disk-info style='height: 100%' :info='diskInfo' :loading='loading'></disk-info>-->
<!--      </div>-->
    </div>
  </div>
</template>

<script>
import terminalInfo from '@views/oneClickHelp/localDeviceInfo/modules/TerminalInfo.vue'
import memoryInfo from '@views/oneClickHelp/localDeviceInfo/modules/MemoryInfo.vue'
import infoStatistic from '@views/oneClickHelp/localDeviceInfo/modules/InfoStatistic.vue'
import cpuInfo from '@views/oneClickHelp/localDeviceInfo/modules/cpuInfo.vue'
import diskInfo from '@views/oneClickHelp/localDeviceInfo/modules/DiskInfo.vue'
import ApplicationInfo from '@views/oneClickHelp/localDeviceInfo/modules/ApplicationInfo.vue'
import {getAction} from '@/api/manage'
import { getHostNameLocal } from '@/utils/util'
export default {
  name: 'BasicInfor',
  mixins: [],
  components: {terminalInfo,cpuInfo,infoStatistic,memoryInfo,diskInfo,ApplicationInfo},
  data() {
    return {
      description: '本机概况页面',
      hostName:'',
      querDeviceInfo:'/terminal/runningState/getStateInfo',

      terminalInfoInit:[
        {name:'终端名称',value:''},
        {name:'IP地址',value:''},
        {name:'MAC地址',value:''},
        {name:'操作系统',value:''},
        {name:'CPU类型',value:''},
        {name:'CPU架构',value:''},
        {name:'单位',value:''},
        {name:'使用部门',value:''},
        {name:'使用人',value:''},
        {name:'联系方式',value:''}],
      cupInfoInit:{
        infoArr: [
          { name: '名称', code: 'cpuName', value: '' },
          {name: '核数',code: 'cpuCores', value: ''},
          { name: '频率', code: 'cpuFreq', value: '' }],
        trendXData:[],
        trendYData:[]
      },
      memoryInfoInit:{
        infoArr: [
          {name: '内存总量', code: 'memTotal', value: '' },
          {name: '内存可使用量',code: 'memAvailable',value: ''},
          {name: '高速缓存量', code: 'cached', value: '' },
          {name: '交换分区空闲量', code: 'swapFree', value: '' },
          {name: '内存空闲量', code: 'memFree', value: '' }],
        rate: ''
      },
      appInfoInit:{
        infoArr:[],
        onlineNum:0,
        total:0,
      },
      diskInfoInit:{
        infoArr: [],
        trendXData:[],
        trendYData:[]
      },
      terminalInfo:[],
      statisticInfo:{},
      cupInfo:{},
      memoryInfo:{},
      diskInfo:{},
      appInfo:{},
      loading:false
    }
  },
  computed: {},
  created() {
    this.hostName = getHostNameLocal()
  },
  watch:{
    "$store.state.app.ochInfoChange"(v){
      if(v){
        this.handleRefresh()
        this.$store.commit("ONE_CLICK_HELP_CHANGE",false)
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.handleRefresh()
    })
  },
  methods: {
    handleRefresh(){
      this.terminalInfo=JSON.parse(JSON.stringify(this.terminalInfoInit))
      this.statisticInfo={}
      this.cupInfo={}
      this.memoryInfo={}
      this.diskInfo={}
      this.appInfo={}
      if (this.hostName === null || this.hostName === '') {
        this.$message.warning('未获取到终端')
        return
      }else {
        this.getInfo()
      }
    },
    getInfo(){
      if(!this.loading){
        this.loading=true
        getAction(this.querDeviceInfo,{uniqueCode:this.hostName}).then((res)=>{
          if(res.success){
            this.getTerminalValue(res.result.terminalInfo)
            this.getStatisticData(res.result.terminalInfo)
            this.getCPUInfo(res.result.dataList)
            this.getMemoryInfo(res.result.dataList)
            this.getDiskInfo(res.result.dataList)
            this.getApplicationInfo(res.result.dataList)
          }else {
            this.$message.warning(res.message)
          }
          this.loading=false
        }).catch((err)=>{
          this.$message.warning(err.message)
          this.loading=false
        })
      }
    },
    getTerminalValue(record) {
      let fieldList = ['name', 'ip', 'macAddr', 'osType', 'cpuType', 'cpuArch', 'deptName', 'userDepartment', 'usernameText', 'phone']
      for (let i = 0; i < fieldList.length; i++) {
        this.terminalInfo[i].value = record[fieldList[i]]
      }
    },
    getStatisticData(record) {
      this.statisticInfo = {
        status: record.status,
        onCount: record.onCount&&record.onCount!=null?record.onCount:'暂无数据',
        onTotal:record.onTotal&&record.onTotal!=null? record.onTotal / 60 +'':'暂无数据',
        lastOn: record.lastOn&&record.lastOn!=null? record.lastOn:'暂无数据',
        onTotal_text: record.onTotal_text&&record.onTotal_text!=null? record.onTotal_text:'暂无数据',
      }
    },
    getCPUInfo(list) {
      this.cupInfo=JSON.parse(JSON.stringify(this.cupInfoInit))

      for (let i = 0; i < list.length; i++) {
        if (list[i].code === 'cpu') {
          this.cupInfo.infoArr.map((item) => {
            let lis =list[i].value&&list[i].value.length>0&&list[i].value[0].value&&list[i].value[0].value.length>0? list[i].value[0].value[0]:[]
            for (let j = 0; j < lis.length; j++) {
              if (lis[j].code === item.code) {
                item.value = lis[j].value
              }
            }
          })
          break
        }
      }
      for (let i = 0; i < list.length; i++) {
        if (list[i].code === 'cpuRate') {
          let lis = list[i].value&&list[i].value.length>0?list[i].value:[]
          for (let j = 0; j < lis.length; j++) {
            this.cupInfo.trendXData.push(lis[j].timestamp)
            this.cupInfo.trendYData.push(lis[j].value)
          }
          break
        }
      }
    },
    getMemoryInfo(list){
      this.memoryInfo=JSON.parse(JSON.stringify(this.memoryInfoInit))

      for (let i = 0; i < list.length; i++) {
        if (list[i].code === 'mem') {
          this.memoryInfo.infoArr.map((item) => {
            let lis =list[i].value&&list[i].value.length>0&&list[i].value[0].value&&list[i].value[0].value.length>0? list[i].value[0].value[0]:[]
            for (let j = 0; j < lis.length; j++) {
              if (lis[j].code === item.code) {
                item.value = lis[j].value+ lis[j].unit
              }
            }
          })
          break
        }
      }
      for (let i = 0; i < list.length; i++) {
        if (list[i].code === 'memUtilizRate') {
          let val =list[i].value&&list[i].value.length>0&&list[i].value[0].value?list[i].value[0].value / 100:0
          this.memoryInfo.rate= val.toFixed(2)
          break
        }
      }
    },
    getDiskInfo(list,code){
      this.diskInfo=JSON.parse(JSON.stringify(this.diskInfoInit))
      for (let i = 0; i < list.length; i++) {
        if (list[i].code === 'filepartirion') {
          let lis =list[i].value&&list[i].value.length>0&&list[i].value[0].value&&list[i].value[0].value.length>0? list[i].value[0].value:[]
          for (let j = 0; j < lis.length; j++) {
            let m = {}
            for (let k = 0; k < lis[j].length; k++) {
              if (lis[j][k].code === 'fileSys') {
                m.diskName = lis[j][k].value
              }
              if (lis[j][k].code === 'partTotal') {
                m.partTotal = lis[j][k].value + lis[j][k].unit
              }
              if (lis[j][k].code === 'partUsed') {
                m.partUsed = lis[j][k].value + lis[j][k].unit
              }
            }
            if (Object.keys(m).length > 0) {
              this.diskInfo.infoArr.push(m)
            }
          }
          break
        }
      }
      for (let i = 0; i < list.length; i++) {
        if (list[i].code === 'diskRate') {
          let lis = list[i].value
          for (let j = 0; j < lis.length; j++) {
            this.diskInfo.trendXData.push(lis[j].timestamp)
            this.diskInfo.trendYData.push(lis[j].value)
          }
          break
        }
      }
    },
    getApplicationInfo(list,code){
      this.appInfo=JSON.parse(JSON.stringify(this.appInfoInit))

      for (let i = 0; i < list.length; i++) {
        if (list[i].code === 'application') {
          let lis = list[i].value[0].value
          this.appInfo.total=lis?lis.length:0

          for (let j = 0; j < lis.length; j++) {
            let m = {}
            for (let k = 0; k < lis[j].length; k++) {
              if (lis[j][k].code === 'name') {
                m.name = lis[j][k].value
              }
              if (lis[j][k].code === 'version') {
                m.version = lis[j][k].value
              }
              if (lis[j][k].code === 'runStatus') {
                m.runStatus = lis[j][k].value==true?"正在运行":'未运行'
                if (lis[j][k].value) {
                  this.appInfo.onlineNum++
                }
              }
            }
            if (Object.keys(m).length > 0) {
              this.appInfo.infoArr.push(m)
            }
          }
          break
        }
      }
    }
  }
}
</script>
<style lang='less' scoped>
.info-wrapper{
  display: flex;
  width:100%;
  //min-width: 800px;
  flex-flow:row nowrap;
  height: 100%;
  //overflow: hidden;
  //overflow-y: auto;

  .left-info{
    width: 4.1rem;
    margin-right: 0.2rem;
  }
  .right-info{
    width:calc(100% - 4.3rem);

    .right-top{
      padding-bottom: 0.2rem;
    }

    .right-middle{

      .right-middle-div{}
      .right-middle-left {}
      .right-middle-right{}
    }
    .right-bottom{
      //padding-top: 0.2rem;
    }
  }
}
@media (min-width: 1600px) {
  .info-wrapper{
    height: 100%;

    .left-info{
      height: 100%;
      min-height: calc(1.95rem + 3.75rem + 3.75rem);
    }
    .right-info{
      height: 100%;

      .right-top{
        //height: 1.95rem;
        //height: 1.8375rem;
        height: 1.9375rem;
      }

      .right-middle{
        height: 32%;
        min-height:3.75rem;
        display: flex;
        flex-flow: row nowrap;

        .right-middle-div{
          width: 50%;
          height:100%;
        }
        .right-middle-left {
          padding-right: 0.1rem;
          padding-bottom: 0;
        }
        .right-middle-right{
          padding-left: 0.1rem;
          padding-top: 0;
        }
      }
      .right-middle-bottom{
        margin-top: 0.2rem;
        max-height: calc(100% - 3.75rem - 1.95rem - 0.2rem);
        //height: calc(100% - 32% - 1.95rem);
        height: calc(100% - 32% - 1.9375rem - 0.2rem);
        min-height: 3.75rem;
      }
      .right-bottom{
        max-height: calc(100% - 3.75rem - 1.95rem);
        //height: calc(100% - 32% - 1.95rem);
        height: calc(100% - 32% - 1.9375rem);
        min-height: 3.75rem;
      }
    }
  }
}
@media (max-width: 1599px) and (min-width:1200px) {
  .info-wrapper{
    height: max-content;

    .left-info{
      height:auto;
      min-height: 100%;
    }
    .right-info{
      height: max-content;
      .right-top{
        height:max-content ;
        //height:calc(1.95rem * 2 + 0.8rem) ;
      }

      .right-middle{
        height: 8.65rem;
        min-height:3.75rem;
        display: flex;
        flex-flow: column nowrap;

        .right-middle-div{
          width: 100%;
          height:50%;
        }
        .right-middle-left {
          padding-right: 0;
          padding-bottom: 0.1rem;
        }
        .right-middle-right{
          padding-left: 0;
          padding-top: 0.1rem;
        }
      }
      .right-middle-bottom{
        margin-top: 0.2rem;
        max-height: 10.225rem;//(434*2-50)/80
        height: 10.225rem;
        min-height: 7.5rem;//600/80
      }
      .right-bottom{
        max-height: 10.225rem;//(434*2-50)/80
        height: 10.225rem;
        min-height: 7.5rem;//600/80
      }
    }
  }
}
@media (max-width: 1199px){
  .info-wrapper{
    height: max-content;

    .left-info{
      height:auto;
      min-height: 100%;
    }
    .right-info{
      width:calc(100% - 4.3rem);
      height: max-content;

      .right-top{
        height: max-content;
      }

      .right-middle{
        height: 8.65rem;
        min-height:3.75rem;
        display: flex;
        flex-flow: column nowrap;

        .right-middle-div{
          width: 100%;
          height:50%;
        }
        .right-middle-left {
          padding-right: 0;
          margin-bottom: 0.1rem;
        }
        .right-middle-right{
          padding-left: 0;
          margin-top: 0.1rem;
        }
      }
      .right-middle-bottom{
        margin-top: 0.2rem;
        max-height: 10.225rem;//(434*2-50)/80
        height: 10.225rem;
        min-height: 7.5rem;//600/80
      }
      .right-bottom{
        max-height: 10.225rem;//(434*2-50)/80
        height: 10.225rem;
        min-height: 7.5rem;//600/80
      }
    }
  }
}
</style>