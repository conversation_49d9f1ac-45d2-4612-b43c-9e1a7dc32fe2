<template>
  <a-row style='height: 100%; margin-right: 4px; overflow: hidden; overflow-y: auto'>
    <a-col style='width: 100%; height: 100%; display: flex; flex-direction: column'>
      <!-- 查询区域 -->
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0', marginRight: '12px' }" class='card-style'
        style='width: 100%'>
        <div class='table-page-search-wrapper'>
          <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
            <a-row :gutter='24' ref='row'>
              <a-col :span='spanValue'>
                <a-form-item label='任务名称'>
                  <a-input placeholder='请输入任务名称' v-model='queryParam.auditName' :allowClear='true' autocomplete='off'
                    :maxLength="maxLength" />
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label="是否告警">
                  <a-select v-model='queryParam.alarm' :allowClear="true" placeholder="请选择是否告警">
                    <a-select-option value="1">是</a-select-option>
                    <a-select-option value="0">否</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label="任务状态">
                  <a-select v-model='queryParam.enabled' :allowClear="true" placeholder="请选择任务状态">
                    <a-select-option value="1">启用</a-select-option>
                    <a-select-option value="0">禁用</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span='colBtnsSpan()'>
                <span class='table-page-search-submitButtons'
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button type='primary' class='btn-search btn-search-style' @click='searchQuery'>查询</a-button>
                  <a-button class='btn-reset btn-reset-style' @click='searchReset'>重置</a-button>
                  <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <div class='table-operator table-operator-style'>
          <a-button @click='handleAdd'>新增</a-button>
          <a-dropdown v-if='selectedRowKeys.length > 0'>
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item key='1' @click='batchDel'>删除</a-menu-item>
            </a-menu>
            <a-button> 批量操作
              <a-icon type='down' />
            </a-button>
          </a-dropdown>
        </div>
        <a-table ref='table' bordered :row-key='(record, index) => {return record.id}' :columns='columns'
          :dataSource='dataSource' :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}" :pagination='ipagination'
          :loading='loading' :rowSelection='{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }'
          @change='handleTableChange'>
          <span slot='action' class='caozuo' slot-scope='text, record'>
            <a @click='handleDetailPage(record)'>查看</a>
            <a-divider type='vertical' />
            <a-dropdown>
              <a class='ant-dropdown-link'>更多
                <a-icon type='down' /></a>
              <a-menu slot='overlay'>
                <a-menu-item>
                  <a @click='executeOne(record.id)' class='overlay'>执行一次</a>
                </a-menu-item>
                <a-menu-item>
                  <a @click='handleEdit(record)' class='overlay'>编辑</a>
                </a-menu-item>
                <a-menu-item>
                  <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                    <a style="color: #409eff">删除</a>
                  </a-popconfirm>
                </a-menu-item>
              </a-menu>
            </a-dropdown>
          </span>
          <template slot='auditStrategy' slot-scope='text'>
            <a-tooltip placement='topLeft' :title='text' trigger='hover'>
              <div class='tooltip'>
                {{ text }}
              </div>
            </a-tooltip>
          </template>
          <template slot='alarm' slot-scope='text'>
            <span>{{ text == 1 ? '是' : '否' }}</span>
          </template>
          <template slot='enabled' slot-scope='text'>
            <span>{{ text == 1 ? '启用' : '禁用' }}</span>
          </template>
        </a-table>
      </a-card>
    </a-col>

    <ip-audit-modal ref='modalForm' @ok='modalFormOk'> </ip-audit-modal>
  </a-row>
</template>

<script>
  import '@/assets/less/TableExpand.less'
  import {
    mixinDevice
  } from '@/utils/mixin'
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import ipAuditModal from './modules/ipAuditModal.vue'
  import {
    httpAction,
    getAction,
    postAction,
    deleteAction
  } from '@/api/manage'
  import JDictSelectTag from '@/components/dict/JDictSelectTag.vue'
  import {
    YqFormSearchLocation
  } from '@/mixins/YqFormSearchLocation'
  export default {
    name: 'ipAuditList',
    mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
    components: {
      ipAuditModal,
      JDictSelectTag,
    },
    data() {
      return {
        maxLength:50,
        description: '设备表管理页面',
        formItemLayout: {
          labelCol: {
            style: 'width:80px'
          },
          wrapperCol: {
            style: 'width:calc(100% - 80px)'
          }
        },
        // 表头
        columns: [{
            title: '任务名称',
            dataIndex: 'auditName',
            customCell: () => {
              let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '审计类型',
            dataIndex: 'auditTypeText',
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 100px;max-width:300px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '审计对象',
            dataIndex: 'auditObjectText',
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 100px;max-width:300px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: 'cron表达式',
            dataIndex: 'cron',
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 100px;max-width:300px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '审计策略',
            dataIndex: 'auditStrategyText',
            scopedSlots: {
            customRender: 'auditStrategy'
            },
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 100px;max-width:300px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '是否告警',
            dataIndex: 'alarm',
            scopedSlots: {
              customRender: 'alarm'
            },
            width: 80,
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 80px;max-width:300px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '任务状态',
            dataIndex: 'enabled',
            width: 80,
            scopedSlots: {
              customRender: 'enabled'
            },
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 80px;max-width:300px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            fixed: 'right',
            width: 140,
            scopedSlots: {
              customRender: 'action'
            }
          }
        ],
        url: {
          list: '/devops/ip/auditTask/list',
          delete: '/devops/ip/auditTask/delete',
          deleteBatch: '/devops/ip/auditTask/deleteBatch',
          execute: '/devops/ip/auditTask/execute',
        },
      }
    },
    created() {},
    mounted() {},
    methods: {
      executeOne(id) {
        getAction(this.url.execute, {
          id: id
        }).then((res) => {
          if (res.success) {
            this.$message.success(res.message)
          }
        })
      },
    }
  }
</script>
<style lang='less' scoped>
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';

  .stateBox {
    margin-left: 20px;
  }

  .stateImg {
    vertical-align: middle;
  }

  .alarmStatus {
    margin-left: 8px;
  }

  .overlay {
    color: #409eff
  }
</style>