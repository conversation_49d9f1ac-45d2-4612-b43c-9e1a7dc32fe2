<template>
  <div id="flowStencil" class="sider">
    <a-input placeholder="输入关键字进行过滤" v-model="filterText"></a-input>
    <vue-easy-tree
      ref="veTree"
      node-key="key"
      :data="assetsCategoryTree"
      :props="replaceFields"
      height="calc(100vh - 325px)"
      :filter-node-method="filterNode"
    >
      <div class="custom-tree-node" slot-scope="{ node, data }">
        <div :draggable="draggable"  @mousedown="startDrag($event, 'dept', data)">
          <img
            v-if="data.icon != null && data.icon.length > 0"
            :src="setUrl(data.icon)"
            style="width: 20px; height: 20px"
          />
          <span>{{ node.label }}</span>
        </div>
      </div>
    </vue-easy-tree>
    <div class="node-box">
      <div v-for="item in otherNodes" 
      :key="item.type" 
      class="group-div" 
       @mousedown="startDrag($event, item.type)">
        <div class="img-div">
          <img :src="item.icon" />
        </div>
        <div class="label">{{item.name}}</div>
      </div>
    </div>
  </div>
</template>

<script>
import VueEasyTree from '@wchbrad/vue-easy-tree'
import { getAction, postAction, httpAction } from '@/api/manage'
import { uuidX6 } from '@/utils/util'
import {
  queryDepartTreeList,
} from '@/api/api'
export default {
  name:"DepNodeList",
  components: {
    VueEasyTree,
  },
  data() {
    return {
      // 树数据
      assetsCategoryTree: [],
      filterText: '',
      replaceFields: {
        label: 'title',
        key: 'key',
      },
      draggable: true,
      picSrc: window._CONFIG['staticDomainURL'],
      url: {
        treeUrl: '/topo/device/tree',
      },
      otherNodes: [
        {
          name: '群组容器',
          type: 'group',
          icon: require('@/assets/netdevice/group.png'),
        },
        {
          name: '文本节点',
          type: 'text',
          icon: require('@/assets/netdevice/text.png'),
        },
        {
          name: '虚拟节点',
          type: 'virtual',
          icon:  require('@/assets/netdevice/virtual.png'),
        },
         {
          name: '边线节点',
          type: 'edgeDragNode',
          icon:  require('@/assets/netdevice/line.png'),
        },
        {
          name: '矩形节点',
          type: 'rectDragNode',
          icon:  require('@/assets/netdevice/rect.png'),
        },
      ],
    }
  },
  created() {
    this.loadTree()
  },
  watch: {
    filterText(val) {
      this.$refs.veTree.filter(val)
    },
  },
  methods: {
    // 加载设备树列表
    loadTree() {
      var that = this
      that.assetsCategoryTree = []
      //为tree生成对应地title slot
      const generateSlotScopes = (data,treeLevel,pData) => {
        /*let level = 1
        if(treeLevel<=1){
          level=1
        }else if(treeLevel>=3){
          level=3
        }else {
          level=2
        }*/
        for (let i = 0; i < data.length; i++) {
          data[i].scopedSlots = {
            parent:pData || null,
          }
          let level = 1
          let orgCategory = data[i].orgCategory
          if(orgCategory){
            orgCategory = Number(orgCategory)
            if(orgCategory<2000){
              level=1
            }else if(orgCategory<3000){
              level=2
            }else {
              level=3
            }
          }
          data[i].treeLevel = level
          data[i].topoNodeId = uuidX6()
          if (data[i].children) {
              generateSlotScopes(data[i].children,treeLevel+1,data[i])
          }
        }
      }
      queryDepartTreeList().then(res=>{
        if (res.success) {
          generateSlotScopes(res.result,0)
          that.assetsCategoryTree = res.result
        }
      })
    },
    filterNode(value, data) {
      if (!value) return true
      return data.departName.indexOf(value) !== -1
    },
    //tree拖拽节点
    async startDrag(e, type, data) {
      this.$emit('dragNode', e, type, data)
      return
    },
    setUrl(url) {
      if (url.includes('http')) {
        return url
      } else {
        return this.picSrc + '/' +url
      }
    }
  },
}
</script>

<style lang="less" scoped>
.node-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
}

.group-div {
  width: calc(50% - 4px);
  height: 36px;
  border: 1px solid #1296db;
  border-radius: 4;
  display: flex;
  align-items: center;
  margin-top: 8px;
  .img-div {
    // margin: auto;
    width: 24px;
    height: 24px;
    margin: 0 10px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .label {
    font-weight: 700;
  }
}

.text-div {
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 36px;
}
</style>