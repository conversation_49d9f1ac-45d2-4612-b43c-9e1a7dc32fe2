<template>
  <div style="height: 100%">
    <a-row style="height: 100%; overflow: hidden">
      <a-col :span="6" class="bacColor leftStyle">
        <div class="tree">
          <div v-for="(item, index) in list" :key="index" class="ipStyle">
            <a @click="getTreeIP(item, item.key)" :class="{ realColorStyle: flag === index + 1 }"
              :id="'treeStyle' + index">{{ item.name }}</a>
          </div>
        </div>
      </a-col>
      <a-col :span="18" class="bacColor rightStyle">
        <a-col :span="24" class="cont" v-if="flag == 1">
          <snpm-test :paramIp="ip"></snpm-test>
        </a-col>
        <a-col :span="24" class="cont" v-else-if="flag == 2">
          <tcp-test :paramIp="ip"></tcp-test>
        </a-col>
        <a-col :span="24" class="cont" v-else-if="flag == 3">
          <ping-test :paramIp="ip"></ping-test>
        </a-col>
        <a-col :span="24" class="cont" v-else-if="flag == 4">
          <jmx-test :paramIp="ip"></jmx-test>
        </a-col>
        <a-col :span="24" class="cont" v-else-if="flag == 5">
          <jdbc-test :paramIp="ip"></jdbc-test>
        </a-col>
        <a-col :span="24" class="cont" v-else-if="flag == 6">
          <ipmi-test :paramIp="ip"></ipmi-test>
        </a-col>
        <a-col :span="24" class="cont" v-else-if="flag == 7">
          <telnet-test :paramIp="ip"></telnet-test>
        </a-col>
        <!-- 连接测试组件勿删 -->
        <!-- <a-col :span="24" class="cont" v-else-if="flag == 8">
          <test-com :paramIp="ip" :dataList="testData.dataList" :title=testData.title :url=testData.url></test-com>
        </a-col> -->
      </a-col>
    </a-row>
  </div>
</template>

<script>
  import snpmTest from './snpmTest'
  import tcpTest from './tcpTest'
  import pingTest from './pingTest'
  import jmxTest from './jmxTest'
  import jdbcTest from './jdbcTest'
  import ipmiTest from './ipmiTest'
  import telnetTest from './telnetTest'
  import testCom from '@/components/tools/testCom'
  export default {
    name: 'testTool',
    components: {
      snpmTest,
      tcpTest,
      pingTest,
      jmxTest,
      jdbcTest,
      ipmiTest,
      telnetTest,
      testCom
    },
    data() {
      return {
        list: [{
            key: 1,
            name: 'SNMP连接测试',
          },
          {
            key: 2,
            name: 'TCP连接测试',
          },
          {
            key: 3,
            name: 'PING测试',
          },
          {
            key: 4,
            name: 'JMX连接测试',
          },
          {
            key: 5,
            name: 'JDBC连接测试',
          },
          {
            key: 6,
            name: 'IPMI连接测试',
          },
          {
            key: 7,
            name: 'Telnet连接测试',
          },
          // {
          //   key: 8,
          //   name: '连接测试组件测试',
          // },
        ],
        // 连接测试组件数据格式
        // testData: {
        //   title: '芜湖',
        //   url: '/connect/testTelnet',
        //   dataList: [{
        //       name: 'IP地址',
        //       type: 'input',
        //       field: 'ip'
        //     }, {
        //       name: '端 口',
        //       type: 'input',
        //       field: 'port'
        //     }, {
        //       name: '超时时间',
        //       type: 'input',
        //       field: 'timeout'
        //     }, {
        //       name: '用户名提示',
        //       type: 'input',
        //       field: 'usrPrompt'
        //     }, {
        //       name: '用户名',
        //       type: 'input',
        //       field: 'username'
        //     }, {
        //       name: '密码提示',
        //       type: 'input',
        //       field: 'pwdPrompt'
        //     }, {
        //       name: '密码',
        //       type: 'input',
        //       field: 'password'
        //     }, {
        //       name: '登录响应标识',
        //       type: 'input',
        //       field: 'loginPrompt'
        //     }, {
        //       name: '命令',
        //       type: 'input',
        //       field: 'command'
        //     }, {
        //       name: '命令响应标识',
        //       type: 'input',
        //       field: 'commandPrompt'
        //     },
        //     // {
        //     //   name: '端  口',
        //     //   type: 'select',
        //     //   field: 'ip1',
        //     //   option: [{
        //     //     name: '勇敢牛牛',
        //     //     value: 1
        //     //   }, {
        //     //     name: '不怕困难',
        //     //     value: 2
        //     //   }]
        //     // },
        //     // {
        //     //   name: 'IP多选',
        //     //   type: 'checkbox',
        //     //   field: 'ip2',
        //     //   option: [{
        //     //     name: '勇敢牛牛',
        //     //     value: 1
        //     //   }, {
        //     //     name: '不怕困难',
        //     //     value: 2
        //     //   }]
        //     // }
        //     // {
        //     //   name: 'IP单选选',
        //     //   type: 'radio',
        //     //   field: 'ip2',
        //     //   option: [{
        //     //     name: '勇敢牛牛',
        //     //     value: 1
        //     //   }, {
        //     //     name: '不怕困难',
        //     //     value: 2
        //     //   }]
        //     // }
        //   ],
        // },
        flag: 1,
        ip: '',
      }
    },
    methods: {
      getTreeIP(item, index) {
        this.flag = index
      },
    },
  }
</script>

<style scoped>
  .cont {
    /*padding: 28px;*/
    height: 100%;
    padding: 22px 24px 22px 23px;
  }

  .bacColor {
    background-color: #ffffff;
    height: 100%;
  }

  .leftStyle {
    width: 20%;
    margin-right: 12px;
    white-space: nowrap;
    overflow-x: auto;
  }

  .rightStyle {
    width: calc(100% - 20% - 12px);
  }

  .tree {
    height: 100%;
    padding: 24px 10px;
  }

  .ipStyle {
    font-size: 1.3em;
    margin-bottom: 17px;
  }

  .ipStyle a {
    color: #595959;
    padding: 4px 21px;
  }

  a:active,
  a:hover,
  .realColorStyle {
    background-color: #ecf5ff;
    color: #409eff !important;
  }
</style>