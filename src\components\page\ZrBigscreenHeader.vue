<template>
  <div class="zr-bigscreen-header">
    <div class="logo" :style="{'font-size': fontSize + 'px'}" @click="goGateway">
      <span class='title-text'> {{ routerTitle }}</span>
    </div>
    <div class="tab tab-left">
      <div class="left-date">
        <div class="date-str">{{ dateStr }}<span style='margin-left: 8px'>{{dayStr}}</span></div>
      </div>
    </div>
    <div class="tab tab-right">
      <div class="right-time">
        <div class="time-str">{{ timeStr }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import { getAction } from '@/api/manage';
import { PLATFORM_TYPE } from '@/store/mutation-types';
import { generateIndexRouter, generateBigscreenRouter } from '@/utils/util'
import store from '@/store'
import router from '@/router'
import {
  mapActions,
  mapState
} from 'vuex'
import moment from 'moment'
export default {
  data() {
    return {
      title: "",
      logoUrl:window.config.bigscreen.bigScreenLogoUrl,
      logoShow:window.config.logoShow,
      menus: [],
      timeStr: '',
      dateStr: '',
      dayStr: '',
      aniFrame: null,
      fontSize: 56,
      maxWidth: 440
    }
  },
  watch: {
    title() {
      this.adjustFontSize()
    }
  },
  computed: {
    ...mapState({
      permissionMenuList: (state) => state.user.permissionList,
      userInfo: (state) => state.user.info,
    }),
    path() {
      return this.$route.path
    },
    textWidth() {
      // 创建临时元素来获取文本宽度
      const tempEl = document.createElement('span')
      tempEl.style.fontSize = this.fontSize + 'px'
      tempEl.style.visibility = 'hidden'
      tempEl.innerHTML = this.title
      document.body.appendChild(tempEl)
      const width = tempEl.offsetWidth
      document.body.removeChild(tempEl)
      return width
    },
    routerTitle(){
      return this.$route.meta.title
    }
  },
  created() {
    this.menus = this.permissionMenuList
    this.getDateAndTime()
  },
  mounted() {
    this.adjustFontSize()
  },
  beforeDestroy() {
    sessionStorage.removeItem("zrlj_ywtsview")
  },
  methods: {
    ...mapActions(['Logout', 'GetPermissionList', 'saveSysTypeMenu']),
    adjustFontSize() {
      // 当文本宽度超过容器最大宽度时，调整字体大小
      if (this.textWidth > this.maxWidth && this.fontSize > 12) {
        this.fontSize--
        this.adjustFontSize()
      }
    },
    getDateAndTime() {
      this.dateStr = moment().format('L')
      this.timeStr = moment().format('HH:mm:ss')
      this.dayStr = moment().format('dddd')
      window.requestAnimationFrame(this.getDateAndTime)
    },
    goGateway() {
      //判断如果是从配置ywtsview页面进入的，则直接跳转到系统的首页
      if(sessionStorage.getItem("zrlj_ywtsview")) {
        getAction('/sys/permission/getUserPlatformTypeByToken').then((res) => {
          if (res.success && res.result) {
            let tem = res.result.split(',')
            getAction('/sys/permission/platformTypeList').then((sres) => {
              if (sres.success && sres.result) {
                let temPlat = sres.result.find(el=>el.value != sessionStorage.getItem(PLATFORM_TYPE)) // 过滤掉当前平台
                let plat = temPlat?.value || "1"
                if (tem.includes(plat)) {
                  this.entrancePlanning(plat)
                } else {
                  this.entrancePlanning(tem[0])
                }
              }
            })
          }
        })
      }

    },
    changeMenu(menu) {
      if (menu.path === this.path) return
      this.$router.push({
        path: menu.path
      })
    },
    entrancePlanning(index) {
      sessionStorage.setItem(PLATFORM_TYPE, index)
      const that = this
      that.GetPermissionList(index).then((res) => {
        if (res == 1) {
          return
        }
        const menuData = res.result.menu
        var redirect = ''
        if (menuData && menuData.length > 0) {
          if (menuData[0].children && menuData[0].children.length > 0) {
            redirect = menuData[0].children[0].path
          } else {
            redirect = menuData[0].path
          }
        } else {
          return
        }

        let constRoutes = []
        if (index === 4) {
          constRoutes = generateBigscreenRouter(menuData)
        } else {
          constRoutes = generateIndexRouter(menuData)
        }
        // 添加主界面路由
        store
          .dispatch('UpdateAppRouter', {
            constRoutes,
          })
          .then(() => {
            // 根据roles权限生成可访问的路由表
            // 动态添加可访问路由表
            router.addRoutes(store.getters.addRouters)
            this.$router.push({
              path: redirect,
            })
          })
      })
    },
  },
}
</script>

<style lang="less" scoped>
.zr-bigscreen-header {
  height: calc(168 / 19.2 * 1vw);
  color: #fff;
  // display: flex;
  // align-items: center;
  //background-image: url(/zrBigScreen/zrHeader.png);
  background-image: url(/zrBigScreen/zrHeader01.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  position: relative;
  .logo {
    white-space: nowrap;
    width: calc(480 / 19.2 * 1vw);
    line-height: calc(56 / 19.2 * 1vw);
    // font-size: calc(37 / 19.2 * 1vw);
    font-weight: 500;
    cursor: pointer;
    position: absolute;
    top: calc(14 / 19.2 * 1vw);
    left: calc((100vw - 480 / 19.2 * 1vw) / 2 );
    text-align: center;
    color: #fff;
    letter-spacing: calc(12 / 19.2 * 1vw);
    font-family: FZZYJW;
    .title-text{
      background: linear-gradient(0deg, #FFFFFF 37.6953125%, #59B7FF 99.5849609375%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    /* span::before {
       content: attr(content);
       position: absolute;
       inset: 0;
       transform: rotatex(180deg) translatey(15px);
       transform-origin: 50% 100%;
       mask: linear-gradient(transparent 30%, rgba(255, 255, 255, 0.2) 90%);
     }*/
  }

  .tab {
    height: calc(46 / 19.2 * 1vw);
    width: calc(500 / 19.2 * 1vw);
    top: calc(0 / 19.2 * 1vw);
    position: absolute;
    display: flex;
    align-items: center;
    color: #fff;
    font-style: italic;

  }

  .tab-left {
    .left-date {
      width: 100%;
      display: flex;
      align-items: center;
      font-size: calc(16 / 19.2 * 1vw);
      height: 100%;
      margin-left: calc(33 / 19.2 * 1vw);
      .date-str {
        color: #fff;
      }
    }
  }

  .tab-right {
    right: 0;
    .right-time {
      display: flex;
      align-items: center;
      flex-direction: row-reverse;
      width: 100%;
      height: 100%;
      .time-str {
        font-size: calc(16 / 19.2 * 1vw);
        font-family: FZHTJW;
        line-height: 1;
        color: #fff;
        margin-right: calc(33 / 19.2 * 1vw);
      }
    }
  }

  .menu-item {
    cursor: pointer;
    margin-left: 30px;
  }

  .menu-active {
    color: aqua;
  }
}
</style>