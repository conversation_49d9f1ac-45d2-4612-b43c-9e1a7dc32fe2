<template>
  <card-frame :title="title" :titleBgPath="'/statsCenter/evaluate/title2.png'">
    <div slot="bodySlot" style="height: 100%;width: 100%;">
      <div class="core-top-core-top-body">
        <div class="core-top-core-top-body-top">
          <div class="core-top-core-top-body-item">
            <img class="core-top-core-top-body-item-img" src="/statsCenter/evaluate/workOrder.png" />
            <span class="core-top-core-top-body-item-title">{{ workOrderDataOne.name }}</span>
            <div class="core-top-core-top-body-item-value">
              <animate-number
                from="0"
                :to="workOrderDataOne.value || 0"
                :key="workOrderDataOne.value"
                duration="5000"
              ></animate-number>
            </div>
          </div>
          <div class="line">
            <div class="sm-line"></div>
          </div>
          <div class="core-top-core-top-body-item">
            <img class="core-top-core-top-body-item-img" src="/statsCenter/evaluate/calendar.png" />
            <span class="core-top-core-top-body-item-title">{{ workOrderDataTwo.name }}</span>
            <div class="core-top-core-top-body-item-value">
              <animate-number
                from="0"
                :to="workOrderDataTwo.value || 0"
                :key="workOrderDataTwo.value"
                duration="5000"
              ></animate-number>
            </div>
          </div>
          <div class="line">
            <div class="sm-line"></div>
          </div>
          <div class="core-top-core-top-body-item">
            <img class="core-top-core-top-body-item-img" src="/statsCenter/evaluate/clock.png" />
            <span class="core-top-core-top-body-item-title">{{ workOrderDataThree.name }}</span>
            <div class="core-top-core-top-body-item-value">
              <animate-number
                from="0"
                :to="workOrderDataThree.value || 0"
                :key="workOrderDataThree.value"
                duration="5000"
                :formatter="formatter"
              ></animate-number>
              <span>h</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </card-frame>
</template>

<script>
import cardFrame from '@views/statsCenter/com/cardFrame.vue'
import { getAction } from '@/api/manage'
export default {
  components: {
    cardFrame
  },
  data() {
    return {
      time1: '',
      time2: '',
      workOrderDataOne: '',
      workOrderDataTwo: '',
      workOrderDataThree: '',
      url: {
        count: '/data-analysis/order/count'
      }
    }
  },
  props: {
    title: ''
  },
  mounted() {
    this.getMockJson()
    // this.count()
  },
  methods: {
    getMockJson(){
      getAction(location.origin+"/statsCenter/mock/evaluateData.json").then((res) => {
        if(res){
          let result = res.serviceRequestInfoStatistics
          this.workOrderDataOne = result[0]
          this.workOrderDataTwo = result[1]
          this.workOrderDataThree = result[2]
        }
      })
    },
    formatter: function(num) {
      if (num == 0) {
        return 0
      } else {
        return num.toFixed(2) //小数点后几位，数字就是几小数点后几位
      }
    },
    // 服务请求信息统计与服务请求达标率统计数据
    count() {
      getAction(this.url.count, {
        time1: this.time1,
        time2: this.time2
      }).then(res => {
        if (res.code == 200) {
          this.workOrderDataOne = res.result && res.result.number[0] ? res.result.number[0] : 0
          this.workOrderDataTwo = res.result && res.result.number[1] ? res.result.number[1] : 0
          this.workOrderDataThree = res.result && res.result.number[2] ? res.result.number[2] : 0
        }
      })
    }
  }
}
</script>

<style scoped lang="less">
.core-top-core-top-body {
  width: 100%;
  height: calc(100% + 0.09rem);
  margin-top: -0.09rem;
  color: #fff;
  font-size: 0.2rem;
  background-color: rgba(0, 23, 34, 0.36);

  .core-top-core-top-body-top {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-left: 0.1rem;

    .line {
      width: 0.01rem;
      height: 1.54rem;
      background-color: rgba(255, 255, 255, 0.2);
      .sm-line {
        width: 0.01rem;
        height: 0.19rem;
        background-color: rgba(0, 174, 255, 1);
      }
    }

    .core-top-core-top-body-item {
      width: 33%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .core-top-core-top-body-item-img {
        width: 1.05rem;
        height: 1.07rem;
      }

      .core-top-core-top-body-item-title {
        width: 100%;
        font-size: 0.2rem;
        text-align: center;
        letter-spacing: 0.05rem;
        margin-top: 1%;
        font-family: SourceHanSansCN-Regular;
      }

      .core-top-core-top-body-item-value {
        width: 100%;
        color: #03fafc;
        font-size: 0.39rem;
        text-align: center;
        margin-top: 1%;
        font-family: DIN-Medium;
      }
    }
  }
}
</style>