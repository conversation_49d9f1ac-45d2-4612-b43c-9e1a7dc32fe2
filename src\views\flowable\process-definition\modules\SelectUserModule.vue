<template>
  <j-modal :title="title" :width="800" :visible='visible' :maskClosable='false' @ok='handleOk' @cancel="handleCancel"
    cancelText="关闭">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :md='10' :sm='12' :xs='24'>
            <a-form-item label="用户账号">
              <a-input placeholder="请输入用户账号" v-model="queryParam.username"></a-input>
            </a-form-item>
          </a-col>
          <a-col :md='8' :sm='12' :xs='24'>
            <span style="float: left; overflow: hidden" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <!-- table区域-begin -->
    <div>
      <a-table size="small" bordered rowKey="username" :columns="columns1" :dataSource="dataSource1"
        :pagination="ipagination" :loading="loading" :rowSelection="{
          selectedRowKeys: selectedRowKeys,
          onSelectAll: onSelectAll,
          onSelect: onSelect,
          onChange: onSelectChange,
        }" @change="handleTableChange">
      </a-table>
    </div>
    <!-- table区域-end -->
  </j-modal>
</template>

<script>
  import {
    filterObj
  } from '@/utils/util'
  import {
    getAction,
    postAction
  } from '@/api/manage'

  export default {
    name: 'SelectUserModule',
    data() {
      return {
        title: '添加已有用户',
        names: [],
        visible: false,
        placement: 'right',
        description: '',
        loading: false,
        queryParam: {
          username: null,
        },
        columns1: [{
            title: '用户账号',
            align: 'center',
            dataIndex: 'username',
          },
          {
            title: '用户名称',
            align: 'center',
            dataIndex: 'realname',
          },
        ],
        dataSource1: [],
        dataSource2: [],
        // 分页参数
        ipagination: {
          current: 1,
          pageSize: 10,
          pageSizeOptions: ['10', '20', '30'],
          showTotal: (total, range) => {
            return range[0] + '-' + range[1] + ' 共' + total + '条'
          },
          showQuickJumper: true,
          showSizeChanger: true,
          total: 0,
        },
        selectedRowKeys: [],
        selectedRows: [],
        temp: {
          processDefinitionId: null,
          identityType: '1',
          identityIds: '',
        },
        url: {
          list: '/sys/user/list',
        },
      }
    },
    props: {
      processDefinitionId: {
        type: String,
        default: '',
      },
    },
    created() {
      this.loadData()
    },
    methods: {
      handleOk() {
        this.dataSource2 = this.selectedRowKeys
        if (this.dataSource2.length > 0) {
          this.temp.identityIds = this.dataSource2
          this.temp.processDefinitionId = this.processDefinitionId;
          console.log(this.temp)
          postAction('/flowable/processDefinitionIdentityLink/save', this.temp).then((res) => {
            console.log(res)
            if (res.success) {
              this.$message.success('操作成功')
              this.visible = false
              this.$emit('selectFinished');
            } else {
              this.$message.error(res.message)
            }
          })
        }
      },
      searchQuery() {
        this.loadData(1)
      },
      searchReset() {
        this.queryParam = {}
        this.loadData(1)
      },
      loadData(arg) {
        //加载数据 若传入参数1则加载第一页的内容
        if (arg === 1) {
          this.ipagination.current = 1
        }
        var params = this.getQueryParams() //查询条件
        getAction(this.url.list, params).then((res) => {
          if (res.success) {
            this.dataSource1 = res.result.records
            this.ipagination.total = res.result.total
          }
        })
      },
      getQueryParams() {
        var param = Object.assign({}, this.queryParam, this.isorter)
        param.field = this.getQueryField()
        param.pageNo = this.ipagination.current
        param.pageSize = this.ipagination.pageSize
        return filterObj(param)
      },
      getQueryField() {
        //TODO 字段权限控制
      },
      onSelectAll(selected, selectedRows, changeRows) {
        if (selected === true) {
          for (var a = 0; a < changeRows.length; a++) {
            this.dataSource2.push(changeRows[a])
          }
        } else {
          for (var b = 0; b < changeRows.length; b++) {
            this.dataSource2.splice(this.dataSource2.indexOf(changeRows[b]), 1)
          }
        }
        // console.log(selected, selectedRows, changeRows);
      },
      onSelect(record, selected) {
        if (selected === true) {
          this.dataSource2.push(record)
        } else {
          var index = this.dataSource2.indexOf(record)
          //console.log();
          if (index >= 0) {
            this.dataSource2.splice(this.dataSource2.indexOf(record), 1)
          }
        }
      },
      onSelectChange(selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectionRows = selectedRows
      },
      onClearSelected() {
        this.selectedRowKeys = []
        this.selectionRows = []
      },
      handleDelete: function (record) {
        this.dataSource2.splice(this.dataSource2.indexOf(record), 1)
      },
      handleTableChange(pagination, filters, sorter) {
        //分页、排序、筛选变化时触发
        console.log(sorter)
        //TODO 筛选
        if (Object.keys(sorter).length > 0) {
          this.isorter.column = sorter.field
          this.isorter.order = 'ascend' == sorter.order ? 'asc' : 'desc'
        }
        this.ipagination = pagination
        this.loadData()
      },

      close() {
        this.visible = false
      },
      handleCancel() {
        this.close()
      },
    },
  }
</script>

<style scoped lang='less'>
  @import '~@assets/less/YQCommon.less';
  @import '~@assets/less/YQNormalModal.less';
</style>