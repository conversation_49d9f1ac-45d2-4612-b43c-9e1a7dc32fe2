<template>
  <a-card :bordered="false" style="height: 100%;">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind='formItemLayout'>
        <a-row :gutter="24" ref='row'>
          <a-col :span='spanValue'>
            <a-form-item label="项目名称">
              <a-input v-model="queryParam.projectName" :maxLength='maxLength' allowClear placeholder="请输入项目名称" />
            </a-form-item>
          </a-col>
          <!--   <a-col :span='spanValue'>
            <a-form-item label="启用状态">
              <a-select v-model="queryParam.isEnable" style="width: 100%" placeholder='请选择启用状态'>
                <a-select-option key="1">
                  启用
                </a-select-option>
                <a-select-option key="0">
                  未启用
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col> -->
          <a-col :span='spanValue'>
            <span class='table-page-search-submitButtons' style='overflow: hidden;'>
              <a-button  type='primary' @click='searchQuery'>查询</a-button>
              <a-button  style='margin-left: 8px' @click='searchReset'>重置</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!--自定义查询项 -->
    <div v-if='toggleSearchStatus' class='custom-query-item'>
      <a-checkbox-group v-model='settingQueryItems' :defaultValue='settingQueryItems' style='width:100%'
        @change='onQuerySettingsChange'>
        <a-row :gutter="24">
          <template v-for='(item, index) in queryItems'>
            <a-col v-show='item.checked' :span='querySpanValue' class='col-checkbox'>
              <a-checkbox :disabled='item.disabled' :value='item.dataIndex'>
                <j-ellipsis :length='10' :value='item.title'></j-ellipsis>
              </a-checkbox>
            </a-col>
          </template>
        </a-row>
      </a-checkbox-group>
    </div>
    <!-- 自定义查询项-END -->

    <!-- 操作按钮区域 -->
    <!-- <div class="table-operator">
      <a-button icon="plus" type="primary" @click="handleAdd">新增</a-button>
      <a-dropdown v-if="selectedRowKeys.length > 0" v-has='"codeRule:delete"'>
        <a-menu slot="overlay" style='text-align: center'>
          <a-menu-item key="b1" @click="batchDel(false)">删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作
          <a-icon type="down" />
        </a-button>
      </a-dropdown>
    </div> -->

    <!-- table区域-begin -->
    <div>
      <!--  :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }" -->
      <a-table ref="table" bordered rowKey="id" size="middle" :columns="columns" :dataSource="dataSource"
        :loading="loading" :pagination="ipagination" :scroll="{ x: inWidth }" @change="handleTableChange">
        <span slot="action" slot-scope="text, record">
          <a @click="handleDetailPage(record)">查看</a>
          <a-divider type="vertical" />
          <span>
            <a-dropdown>
              <a>生成报告</a>
              <a-menu slot="overlay">
                <a-menu-item>
                  <a @click="handleEvaluateRport(record,'word')">WORD</a>
                </a-menu-item>
                <a-menu-item>
                  <a @click="handleEvaluateRport(record,'pdf')">PDF</a>
                </a-menu-item>
              </a-menu>
            </a-dropdown>
          </span>

          <!--   <span>
            <a-divider type="vertical" />
            <a-dropdown>
              <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
              <a-menu slot="overlay">
                <a-menu-item>
                  <a @click="handleEdit(record)">编辑</a>
                </a-menu-item>
                <a-menu-item>
                  <a @click="batchDel(record.id)">删除</a>
                </a-menu-item>
              </a-menu>
            </a-dropdown>
          </span> -->
        </span>
      </a-table>
    </div>

    <localization-strategy-modal ref="modalForm" @ok="modalFormOk">
    </localization-strategy-modal>
  </a-card>
</template>

<script>

import '@assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import LocalizationStrategyModal from './modules/LocalizationStrategyModal'
import { YqFormSeniorSearchLocation } from '@/mixins/YqFormSeniorSearchLocation'
import { deleteAction } from '@api/manage'
import { ajaxGetDictItems } from '@api/api'
import { downloadFile } from '@/api/manage'
export default {
  name: 'operationalOrganizationList',
  mixins: [JeecgListMixin, mixinDevice, YqFormSeniorSearchLocation],
  components: {
    LocalizationStrategyModal
  },
  data() {
    return {
      maxLength: 50,
      description: '运行指标评估组织管理页面',
      hadCodeList: [],
      // 表头
      columns: [
        {
          title: '序号',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          isUsed: false,
          customRender: function (t, r, index) {
            return parseInt(index) + 1
          }
        },
        {
          title: '项目名称',
          align: 'center',
          dataIndex: 'projectName',
          ellipsis: true,
          // width: 200
        },
        {
          title: '发起人',
          align: 'center',
          dataIndex: 'sender_dictText',
        },
        {
          title: '评估时间',
          align: 'center',
          dataIndex: 'evaluateTime',
          ellipsis: true,
          scopedSlots: { customRender: 'resourceCategoryIds' }
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 150,
          isUsed: false,
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: '/evaluate/projectInfo/pageList',
        delete: '/tags/utlTagAutoPolicy/delete',
        deleteBatch: '/tags/utlTagAutoPolicy/deleteBatch',
        enableBatch: '/tags/utlTagAutoPolicy/enableBatch',
        exportXlsUrl: '',
        importExcelUrl: ''
      },
    }
  },
  created() {
    // this.getDictData()
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
    inWidth() {
      let maxW = this.$store.state.app.maxScreenWidth
      return maxW != -1 && maxW <= 1400
    }
  },
  methods: {
    /*  loadData(){
       this.dataSource = [
         {
           id: 1,
           projectName: '国产化设备测率项目1',
           createBy: '张三',
           evaluateTime: '2023-10-01'
         },
         {
           id: 2,
           projectName: '国产化设备测率项目2',
           createBy: '李四',
           evaluateTime: '2023-10-02'
         }
       ]
     }, */

    /* getDictData() {
       ajaxGetDictItems('innovation_rule_operator').then(res => {
         if (res.success) {
           this.ruleOperators = res.result
         }
       })
    }, */
    // 批量删除
    batchDel: function (sigleid) {
      let ids = ''
      if (sigleid) {
        ids = sigleid
      } else {
        if (!this.url.deleteBatch) {
          this.$message.error('请设置url.deleteBatch属性!')
          return
        }
        if (this.selectedRowKeys.length <= 0) {
          this.$message.warning('请选择一条记录！')
          return
        }

        for (var a = 0; a < this.selectedRowKeys.length; a++) {
          ids += this.selectedRowKeys[a] + ','
        }
      }
      var that = this
      this.$confirm({
        title: sigleid ? '确定要删除这条策略吗？' : '确定要删除选中的策略吗？',
        okText: '确定',
        cancelText: '取消',
        onOk: function () {
          that.loading = true
          deleteAction(that.url.deleteBatch, { ids: ids })
            .then((res) => {
              if (res.success) {
                //重新计算分页问题
                that.reCalculatePage(that.selectedRowKeys.length)
                that.$message.success(res.message)
                that.loadData()
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.loading = false
            })
        }
      })

    },
    handleDetailPage: function (record, index = 1) {
      const params = {
        record: record,
      }
      this.$parent.pButton2(index, params)
    },
    //查看评估报告
    handleEvaluateRport(record,type) {
      //  console.log('查看评估报告', record)
       const ext = type === 'word' ? 'docx' : 'pdf';
       let fileName = `${record.projectName}评估报告.${ext}`;
      downloadFile('/evaluate/projectInfo/exportReport',fileName, { id: record.id, exportType: type })
      .then(res => {
      }).catch(err => {
        this.$message.error('获取评估报告失败')
      })
    },

  }
}
</script>
<style scoped lang='less'>
@import '~@assets/less/common.less';
@import '~@assets/less/YQCommon.less';
</style>