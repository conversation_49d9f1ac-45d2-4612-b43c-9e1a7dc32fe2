<template>
  <div ref='chart_ref' class='chart_ref' style='display: block;width: 100%;height: 100%'></div>
</template>
<script>
export default {
  name: 'TwoElementsPieChart',
  props: {
    name: '',
    chartData: null,
    dataTypeName:{
      type:Array,
      required:false,
      default:()=>{
        return ['已完成','未完成']
      }
    }
  },
  data() {
    return {
      chartInstance: null,
    }
  },
  mounted() {
    if(this.chartData!=null){
      console.log('this.chartData===',this.chartData)
      this.$nextTick(() => {
        this.initChart()
        this.swithData()
        this.$nextTick(()=>{
          this.setChartize()
        })
      })
    }
  },
  destroyed() {
    window.removeEventListener('resize', this.setChartize)
  },
  methods: {
    initChart() {
      let _this = this
      const item = {
        value: [_this.chartData.pDoneNum, _this.chartData.pTodoNum],
        status: '这是我的标签',
        total: _this.chartData.pTotal
      }

      _this.chartInstance = _this.$echarts.init(_this.$refs['chart_ref'])
      let initOption = {
        title: [
          {
            z: 10,
            //text: this.name,
            text: _this.dataTypeName[0] + item.value[0] + '%',
            x: 'center',
            y:'center',
            padding:0,
            textStyle: {
              fontSize: 16,
              color: 'rgb(255,255,255)'
            }
          },
         /* {
            z: 10,
            text: _this.dataTypeName[0] + item.value[0] + '%',
            x: 'center',
            y: 'center',
            textStyle: {
              //fontFamily: 'DIN-Medium',
              fontSize: 15,
              color: '#ffffff',
              padding: 5
            }
          }*/
        ],
        series: [
          { // 绘制最上层圆环
            //name: item.title,
            z: 1,
            type: 'pie',
            roseType: 'radius',
            center: ['50%', '50%'],                 // 圆环中心相对于容器位置
            radius: ['55%', '70%'],
            minAngle: 20,
            labelLine: {
              show: false
            },
            label: {
              show: false
            },
            data: [
              {
                //name: this.name,
                value: item.value[0],
                itemStyle: {
                  //borderRadius: 10,
                  borderWidth: 2,
                  borderColor: 'rgba(0,11,15,0)',
                  /* shadowOffsetY: 2,
                  shadowColor: 'rgba(32,148,201,0.31)',
                  shadowBlur: 2,*/
                  color: {
                    type: 'linear',
                    x: 1,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [{
                      offset: 0,
                      color: '#FFC600' || '#30D27C' // 0% 处的颜色
                      //color: '#9aee94' || '#30D27C' // 0% 处的颜色
                    }, {
                      offset: 1,
                      color: '#0B95FF' || '#367bec' // 100% 处的颜色
                    }]
                  }
                }
              },
              {
                // name: this.name,
                value: item.value[1],
                //borderRadius: 10,
                itemStyle: {
                  borderWidth: 2,
                  borderColor: 'rgba(0,11,15,0)',
                  /* shadowOffsetY: 2,
                  shadowColor: 'rgba(32,148,201,0.31)',
                  shadowBlur: 2,*/
                  color: {
                    type: 'linear',
                    x: 1,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [{
                      offset: 0,
                      color: '#e8e5e5' || '#30D27C' // 0% 处的颜色
                    }, {
                      offset: 1,
                      color: '#e8e8ec' || '#367bec' // 100% 处的颜色
                    }]
                  }
                }
              }
            ]
          },
          //内圆
          /*     {
                 z: 8,
                 type: 'pie',
                 center: ['50%', '50%'],
                 radius: ['45%'],// 圆环中心相对于容器位置
                 //radius: ['49%', '70%'],
                 /!* cursor: 'auto',*!/
                 data: [{
                   value: 0,
                   itemStyle: {
                     normal: {
                       color: '#7d8cf1',
                       opacity: 0.5
                     }
                   },
                   label: { show: false },
                   labelLine: {
                     show: false
                   }
                 }
                 ]
               }, */
          {
            z: 8,
            type: 'pie',
            center: ['50%', '50%'],
            radius: ['0%', '50%'],// 圆环中心相对于容器位置
            //radius: ['49%', '70%'],
            /* cursor: 'auto',*/
            hoverAnimation:false,
            label: {
              show: false
            },
            labelLine: {
              show: false
            },
            data: [{
              value: 0,
              itemStyle: {
                color: {
                  type: 'radio',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [{
                    offset: 0,
                    color: '#FFC600' // 0% 处的颜色
                    //color: '#9aee94' // 0% 处的颜色
                  }, {
                    offset: 0.5,
                    color: '#30D27C' // 100% 处的颜色
                    //color: '#25a8df' // 100% 处的颜色
                  }, {
                    offset: 1,
                    color: '#0B95FF' // 100% 处的颜色
                    //color: '#5d95fc'
                  }]
                }
              }
            }]
          },
          //刻度尺
         /*    {
            // name: "白色圈刻度",
            type: 'gauge',
            radius: '65%',
            startAngle: 225, //刻度起始
            endAngle: -134.8, //刻度结束
            z: 4,
            axisTick: {
              show: true,
              lineStyle: {
                width: 2,
                color: 'rgba(1,244,255, 0.9)'
              }
            },
            splitLine: {
              length: 16, //刻度节点线长度
              lineStyle: {
                width: 2,
                color: 'rgba(1,244,255, 0.9)'
              } //刻度节点线
            },
            axisLabel: {
              color: 'rgba(255,255,255,0)',
              fontSize: 12
            }, //刻度节点文字颜色
            pointer: {
              show: false
            },
            axisLine: {
              lineStyle: {
                opacity: 0
              }
            },
            detail: {
              show: false
            },
            data: [{
              value: 0,
              name: ''
            }]
          }*/
        ]
      }
      _this.chartInstance.setOption(initOption, true)
      window.addEventListener('resize', _this.setChartize)
    },
    setChartize() {
      this.chartInstance.resize()
    },
    swithData() {
      let _this = this
      _this.chartInstance.on('mouseover', (params) => {
        let op = _this.chartInstance.getOption()
        if (params.componentIndex === 0) {
          let _title = [
            {
              z:10,
              //text: params.name,
              text: params.value === this.chartData.pDoneNum ? _this.dataTypeName[0] + params.value + '%' :_this.dataTypeName[1] + params.value + '%',
              x: 'center',
              y: 'center',
              textStyle: {
                fontSize: 16,
                color: 'rgba(255,255,255,1)'
              }
            },
           /* {
             z:10,
              text: params.value === this.chartData.pDoneNum ? _this.dataTypeName[0] + params.value + '%' : _this.dataTypeName[1] + params.value + '%',
              x: 'center',
              y: 'center',
              textStyle: {
                //fontFamily: 'DIN-Medium',
                fontSize: 15,
                color: '#7EEAF3',
                padding: 5
              }
            }*/
          ]
          // _this.chartInstance.clear()
          op.title = _title
          _this.chartInstance.setOption(op, false)
        }
      })
      _this.chartInstance.on('mouseout', (params) => {
        let op = _this.chartInstance.getOption()
        if (params.componentIndex === 0) {
          let _title = [
            {
              z:10,
              //text: params.name,
              text: _this.dataTypeName[0] + params.value + '%',
              x: 'center',
              y: 'center',
              textStyle: {
                fontSize: 16,
                color: 'rgba(255,255,255,1)'
              }
            },
            /* {
              z:10,
               text: params.value === this.chartData.pDoneNum ? _this.dataTypeName[0] + params.value + '%' : _this.dataTypeName[1] + params.value + '%',
               x: 'center',
               y: 'center',
               textStyle: {
                 //fontFamily: 'DIN-Medium',
                 fontSize: 15,
                 color: '#7EEAF3',
                 padding: 5
               }
             }*/
          ]
          // _this.chartInstance.clear()
          op.title = _title
          _this.chartInstance.setOption(op, false)
        }
      })
    }
  }
}
</script>
<style scoped lang='less'>
.chart_ref{
  position: relative;
}
.chart_ref > div{
  width: 100% !important;
}
</style>