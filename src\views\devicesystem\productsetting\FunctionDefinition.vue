<template>
  <div  class='zhl'>
    <!-- 操作按钮区域 -->
    <div class='table-operator table-operator-style'>
      <a-button @click='handleAdd' type='primary' icon='plus'>新增</a-button>
    </div>

    <!-- table区域-begin -->
    <div>
      <a-table
        ref='table'
        bordered
        rowKey='id'
        :columns='columns'
        :dataSource='dataSource'
        :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
        :pagination='ipagination'
        :loading='loading'
        @change='handleTableChange'
      >
        <template slot='htmlSlot' slot-scope='text'>
          <div v-html='text'></div>
        </template>
        <template slot='imgSlot' slot-scope='text'>
          <span v-if='!text' style='font-size: 14px'>无图片</span>
          <img v-else :src='getImgView(text)' height='25px' alt='' style='max-width: 80px; font-size: 14px' />
        </template>
        <template slot='fileSlot' slot-scope='text'>
          <span v-if='!text' style='font-size: 14px'>无文件</span>
          <a-button v-else :ghost='true' type='primary' icon='download' size='small' @click='downloadFile(text)'>
            下载
          </a-button>
        </template>

        <span slot='action' class='caozuo' slot-scope='text, record,index'>
          <a @click='handleEdit(record,index)'>编辑</a>
          <a-divider type='vertical' />
          <a-dropdown>
            <a class='ant-dropdown-link'>更多 <a-icon type='down' /></a>
            <a-menu slot='overlay'>
              <a-menu-item>
                <a @click='handleDetail(record)' style='color: #409eff'>查看</a>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title='确定删除吗?' @confirm='() => handleDelete(record.id)'>
                  <a style='color: #409eff'>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>
        <template slot='tooltip' slot-scope='text'>
          <a-tooltip placement='topLeft' :title='text' trigger='hover'>
              <span class='tooltip'>
                {{ text }}
              </span>
          </a-tooltip>
        </template>
      </a-table>
    </div>
    <function-definition-modal :productInfo='productInfo' ref='modalForm' @ok='modalFormOk'></function-definition-modal>
  </div>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import JSuperQuery from '@/components/jeecg/JSuperQuery.vue'
import FunctionDefinitionModal from '@views/devicesystem/modules/FunctionDefinitionModal'

export default {
  name: 'FunctionDefinition',
  mixins: [JeecgListMixin, mixinDevice],
  components: {
    FunctionDefinitionModal,
    JSuperQuery
  },
  data() {
    return {
      description: '功能元数据管理页面',
      // 表头
      columns: [
        {
          title: '功能标识',
          dataIndex: 'code',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 80px;max-width:300px'
            return { style: cellStyle }
          },
        },
        {
          title: '功能名称',
          dataIndex: 'name',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 80px;max-width:300px'
            return { style: cellStyle }
          },
        },
        {
          title: '是否异步',
          dataIndex: 'isAsync',
          customRender:(text,record,index)=>{
            return record.isAsync=='1'?'是':'否'
          },
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 50px;max-width:200px'
            return { style: cellStyle }
          },
        },
        {
          title: '传输协议',
          dataIndex: 'protocol',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 50px;max-width:300px'
            return { style: cellStyle }
          },
        },
        {
          title: '描述',
          dataIndex: 'description',
          scopedSlots: { customRender: 'tooltip' },
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 50px;max-width:400px'
            return { style: cellStyle }
          },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 150,
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: '/product/deviceControlCommand/commands',
        delete: '/product/deviceControlCommand/delete',
      },
      superFieldList: [],
      productInfo:{}
    }
  },

  methods: {
    handleEdit: function (record,index) {
      this.$refs.modalForm.edit(record,index);
      this.$refs.modalForm.title = "编辑";
      this.$refs.modalForm.disableSubmit = false;
    },
    handleAdd: function () {
      this.$refs.modalForm.add(this.dataSource.length);
      this.$refs.modalForm.title = "新增";
      this.$refs.modalForm.disableSubmit = false;
    },
    show(record) {
      this.productInfo=record
      this.queryParam.productId = this.productInfo.id
      this.ipagination.pageSize=10
      this.ipagination.current=1
      this.loadData()
    },
    initDictConfig() {
    },
    getSuperFieldList() {
      let fieldList = []
      fieldList.push({ type: 'string', value: 'code', text: '功能标识', dictCode: '' })
      fieldList.push({ type: 'string', value: 'name', text: '功能名称', dictCode: '' })
      fieldList.push({ type: 'string', value: 'isAsync', text: '是否异步', dictCode: '' })
      fieldList.push({ type: 'string', value: 'protocol', text: '协议', dictCode: '' })
      fieldList.push({ type: 'string', value: 'description', text: '描述', dictCode: '' })
      this.superFieldList = fieldList
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
</style>
