<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container>
      <a-form :form="form" slot="detail">
        <a-row :gutter='24'>
          <a-col :span="24">
            <a-form-item label="任务名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['taskName', validatorRules.taskName]" :allowClear="true" autocomplete="off"
                       placeholder="请输入任务名称"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="周期执行" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <!-- cron表达式  -->
              <j-cron ref="innerVueCron" v-decorator="['executeCron', validatorRules.executeCron]" @change="setCorn" :hideYear="true"></j-cron>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="有效限期" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-radio-group v-model="effectiveStatus">
                <a-radio value='0'> 永久有效</a-radio>
                <a-radio value='1'> 有效天数：<a-input-number v-model="effectiveDate" :min="1" :precision="0" :max='99999' type="number" placeholder="请输入有效天数" style="width:135px"></a-input-number></a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="状态" :labelCol="labelCol" :wrapperCol="wrapperCol" prop='taskStatus'>
              <a-radio-group v-decorator="['taskStatus',{initialValue: '1'}]">
                <a-radio value='1'> 启用</a-radio>
                <a-radio value='0'> 禁用</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label='任务描述' prop='description' :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-textarea style='width: 100%' v-decorator="['description', validatorRules.description]" :autoSize='{minRows:2,maxRows:4}'
                :allow-clear='true' autocomplete='off' placeholder='请输入任务描述' />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>

    <div>
      <div class="colorBox">
        <span class="colorTotal">设备选择</span>
      </div>
      <div class="table-operator">
        <a-button @click="handleAddDevice" type="primary">新增</a-button>
      </div>
      <a-table size="middle" bordered :row-key="(record, index) => {
          return record.id
      }
          " :columns="columns" :dataSource="dataSource" :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
          :pagination="ipagination" :loading="loading" @change="handleTableChange">
          <span slot="action" slot-scope="text, record">
              <a-popconfirm v-if="queryParam.taskId" title="确定解绑设备吗?" @confirm="() => handleDelete(record)">
                  <a>解绑</a>
              </a-popconfirm>
              <a v-else @click="handleDelete(record)">删除</a>
          </span>
      </a-table>
    </div>
    <device-selection-modal ref="modalForm" @loadTableData="loadTableData" @ok="tableOK"></device-selection-modal>
  </a-spin>
</template>

<script>
import {
  httpAction, getAction, deleteAction
} from '@/api/manage'
import pick from 'lodash.pick'
import JFormContainer from '@/components/jeecg/JFormContainer'
import JCron from '@/components/jeecg/JCron.vue'
import {
    JeecgListMixin
} from '@/mixins/JeecgListMixin'
export default {
  name: 'TaskForm',
  mixins: [JeecgListMixin],
  components: {
    JFormContainer,
    JCron,
    DeviceSelectionModal: () => import('./DeviceSelectionModal')
  },
  data() {
    return {
      form: this.$form.createForm(this),
      model: {
        deviceIds: ''
      },
      labelCol: {
        xs: {
          span: 24,
        },
        sm: {
          span: 4,
        },
        md: {
          span: 3,
        },
        lg: {
          span: 3,
        },
      },
      wrapperCol: {
        xs: {
          span: 24,
        },
        sm: {
          span: 20,
        },
        md: {
          span: 20,
        },
        lg: {
          span: 20,
        },
      },
      confirmLoading: false,
      validatorRules: {
        taskName: {
          rules: [{
            required: true,
            message: '请输入任务名称',
          },
            {
              min: 2,
              max: 30,
              message: '长度在2到30个字符',
              trigger: 'blur',
            },
          ],
        },
        executeCron: {
          initialValue: '0 0 0 * * ?',
          rules: [{
            required: true,
            validator: this.validateCorn
          }, ],
        },
        description: {
          rules: [{ required: false, min: 0, max: 255, message: '任务描述长度应在 0-255 之间' }]
        },
      },
      url: {
        add: '/configureBack/task/add', // 添加任务
        edit: '/configureBack/task/edit', // 编辑任务
        validateCorn: '/autoInspection/devopsAutoInspection/cronCheckSix', // 校验cron表达式
        list: '/configureBack/task/getDevice', // 获取某个设备的任务列表
        delete: '/configureBack/task/unbindDevice', // 某个任务解绑设备
      },
      effectiveDate: null,
      taskId: undefined,
      effectiveStatus: '0', // 默认永久有效
      disableMixinCreated: true,
      columns: [{
                title: '产品名称',
                align: 'center',
                dataIndex: 'productName',
            },
            {
                title: '设备名称',
                align: 'center',
                dataIndex: 'name',
            },
            {
                title: 'IP',
                align: 'center',
                dataIndex: 'ip',
            },
            {
                title: '操作',
                dataIndex: 'action',
                align: 'center',
                scopedSlots: {
                    customRender: 'action',
                },
            }]
    }
  },
  methods: {
    closeForm() {
      this.$emit('closeForm')
    },
    add() {
      this.edit({})
    },
    edit(record) {
      this.queryParam.taskId = record.id
      this.form.resetFields()
      this.model = Object.assign({}, record)

      // 设置有效期限 0或null代表永久有效
      if (!!record.effectiveDate) {
        // 有效天数
        this.effectiveStatus =  '1'
        this.effectiveDate = record.effectiveDate
      } else {
        this.effectiveStatus = '0'
        this.effectiveDate = null
      }

      this.visible = true
      this.$nextTick(() => {
        this.formInit(this.model)
      })
      if (record.id) {
        this.loadData()
      }
    },
    formInit(pickData) {
      this.form.setFieldsValue(
        pick(
          pickData,
          'taskName',
          'executeCron',
          'taskStatus',
          'description'
        )
      )
    },
    //向后端请求table数据
    loadTableData() {
      this.loading = true
      this.loadData()
    },
    tableOK(idList, list) {
      let deviceIds = this.model.deviceIds || []
      if (list && list.length > 0) {
        list.forEach(ele => {
          if (deviceIds.indexOf(ele.id) == -1) {
            this.dataSource.push(ele)
          }
        })
        let ids = this.dataSource.map(m => {
          return m.id
        })
        this.model.deviceIds = ids.join(',')
      }
    },
    //提交
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (
          !err
        ) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }

          let formData = Object.assign(that.model, values)

          if (that.effectiveStatus == '0') {
            // 永久有效
            formData.effectiveDate = 0
          } else {
            formData.effectiveDate = that.effectiveDate
          }

          httpAction(httpurl, formData, method)
            .then((res) => {
              that.confirmLoading = false
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
                that.closeForm()
              } else {
                that.$message.warning(res.message)
              }
            })
            .catch((err) => {
              that.$message.warning(err.message)
              that.confirmLoading = false
          })
        }
      })
    },
    validateCorn(rule, value, callback) {
      if (rule.required) {
        if (value && value.length > 0) {
          getAction(this.url.validateCorn, {
            cronExpression: value
          }).then((res) => {
            if (res.success) {
              callback()
            } else {
              callback('cron表达式格式错误!')
            }
          })
        } else {
          callback('请输入cron表达式')
        }
      } else {
        callback()
      }
    },
    //周期执行
      setCorn(data) {
      if (data && data.target != null) {
        let dataList = data.target.value.split(' ')
        if (dataList[0] === '*') {
          this.$message.warning('请确认是否每秒都执行')
        }
      } else {
        let dataList = data.split(' ')
        if (dataList[0] === '*') {
          this.$message.warning('请确认是否每秒都执行')
        }
      }

      if (Object.keys(data).length === 0) {
        this.$message.warning('请输入cron表达式!');
      }
    },
    //新增添加设备
    handleAddDevice: function() {
       let record = {
        id: this.queryParam.taskId
      }
      this.$refs.modalForm.edit(record, this.model.deviceIds)
      this.$refs.modalForm.title = '设备添加'
      this.$refs.modalForm.disableSubmit = false
    },
    //table中操作删除数据
    handleDelete(record) {
      if (null == this.queryParam.taskId || '' == this.queryParam.taskId) {
        console.log(record.id)
        this.dataSource.forEach((ele, index) => {
          if (this.dataSource[index].id == record.id) {
            this.dataSource.splice(index, 1)
          }
          let ids = this.dataSource.map(m => {
            return m.id
          })
          this.model.deviceIds = ids.join(',')
        })
      } else {
        var params = {
            taskId: this.queryParam.taskId,
            deviceId: record.id
        }
        deleteAction(this.url.delete, params).then((res) => {
          if (res.success) {
              //重新计算分页问题
              this.reCalculatePage(1)
              this.$message.success(res.message)
              this.loadData()
          } else {
              this.$message.warning(res.message)
          }
        })
      }
    }
  }
}
</script>
<style scoped lang='less'>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
.colorBox {
  margin-top: 18px;
  margin-bottom: 18px;
}
.colorTotal {
  padding-left: 7px;
  border-left: 4px solid #1e3674;
}

/* 定义滚动条样式 */
::-webkit-scrollbar {
  width: 0.15rem
  /* 12/80 */
;
  // height: 6px;
  //background-color: #222325;
}

/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track {
  box-shadow: inset 0 0 0px rgba(240, 240, 240, 0.5);
  // border-radius: 50%;
  background-color: #eaeaea;
  border-radius: 0.1rem
  /* 8/80 */
;
}

/*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
  border-radius: 0.1rem
  /* 8/80 */
;
  box-shadow: inset 0 0 0px rgba(240, 240, 240, 0.5);
  background-color: #d6d6d6;
}

::v-deep .ant-spin-nested-loading {
  padding-right: 13px;
}
</style>