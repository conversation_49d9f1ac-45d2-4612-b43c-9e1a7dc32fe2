<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    :destroyOnClose="true"
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }"
    :centered="true"
    :fullscreen="true"
    :confirmLoading="confirmLoading"
    @cancel='handleCancel'
    cancelText="关闭"
    :bodyStyle="{ padding: '0' }"
  >
    <a-spin :spinning="confirmLoading">
      <div class="task-container">
        <!-- 左侧指标锚点导航 -->
        <div class="metrics-nav">
          <a-anchor :affix="false" :target-offset="100" :showInkInFixed="true">
            <a-anchor-link
              v-for="metric in metricsList"
              :key="metric.id"
              :href="`#metric-${metric.id}`"
              :title="metric.metricsName"
              :showInkInFixed="false"
            />
          </a-anchor>
        </div>
        <!-- 右侧指标表单内容 -->
        <div class="metrics-content">
          <div v-for="metric in metricsList" :key="metric.id" :id="`metric-${metric.id}`" class="metric-item">
            <div class="metrics-title">{{ metric.metricsName }}</div>
            <template v-if="metric.metricsForm">
              <div class="metric-form">
                <k-form-build
                  :ref="`rulesForm_${metric.id}`"
                  :value="metric.metricsForm"
                  :dynamicData="dynamicData"
                  :defaultValue="metric.metricsData || {}"
                  :disabled="disableSubmit"
                />
              </div>
            </template>
            <template v-else>
              <a-alert message="表单配置加载失败" type="error" />
            </template>
          </div>
        </div>
      </div>
    </a-spin>
    <template slot="footer">
      <a-button key="submit" type="primary" :loading="submitLoading" @click="handleOk">提交</a-button>
      <a-button key="draft" @click="saveDraft">暂存</a-button>
      <a-button key="back" @click="handleCancel"> 关闭</a-button>
    </template>
  </j-modal>
</template>

<script>
import { getAction, postAction } from '@api/manage'

export default {
  name: 'addQuestionModal',
  data() {
    return {
      title: '填报',
      width: '1200px',
      visible: false,
      disableSubmit: false,
      confirmLoading: false,
      submitLoading: false,
      url: {
        fillQuestionnaire: '/devops/projectInfo/fillQuestionnaire',
        saveDraft: '/devops/projectInfo/saveDraft',
      },
      // 指标列表数据
      metricsList: [],
      // 存储所有表单数据
      allFormData: {},
      variables: {},
      dynamicData: {
        bpmnDepartTreeData: [],
      },
      jsonData: {
        list: [],
        config: {
          layout: 'horizontal',
          labelCol: { span: 4 },
          wrapperCol: { span: 18 },
          hideRequiredMark: true,
          customStyle: '',
        },
      },
    }
  },
  created() {
    this.getTreeData()
  },
  methods: {
    // 在初始化数据时处理 metricsForm
    async initData(metricsList) {
      // console.log('metricsList', metricsList)
      this.visible = true
      this.confirmLoading = true
      // 通过metricsIds 批量获取指标对应的表单
      const relaIds = metricsList.map((item) => item.id)
      // 获取所有指标的表单数据
      let formValues = await this.getFromDatabyId(relaIds)

      // 处理数据格式
      this.metricsList = (metricsList || []).map((metric) => {
        try {
          // 处理 metricsForm
          if (metric.metricsInfo?.metricsForm) {
            let formStr = metric.metricsInfo.metricsForm
            metric.metricsForm = JSON.parse(formStr)
          }
          if (formValues && formValues[metric.id]) {
            metric.metricsData = formValues[metric.id] || {}
          } else {
            metric.metricsData = {}
          }
        } catch (e) {
         // console.error('处理指标数据出错:', e)
          metric.metricsForm = this.jsonData
          metric.metricsData = {}
        }
        return metric
      })
      this.confirmLoading = false
    },
    getTreeData() {
      getAction('/sys/sysDepart/queryTreeList').then((res) => {
        this.dynamicData.bpmnDepartTreeData = res.result
      })
    },
    // 接口获取表单数据
    getFromDatabyId(ids) {
      // 根据评估报告与单位和指标关联表id查询指标表单内容
      let retationsIds = ids ? ids.join(',') : ''
      return new Promise((resolve, reject) => {
        getAction('/evaluate/fieldInfo/getFormValue', { ids: retationsIds })
          .then((res) => {
            if (res.success) {
              resolve(res.result || [])
            } else {
              reject(new Error(res.message || '获取表单数据失败'))
            }
          })
          .catch((error) => {
            reject(error)
          })
      })
    },
    // 保存所有表单数据
    async saveAllFormData() {
      try {
        const formResults = []
        let hasError = false
        // 遍历所有表单
        for (const metric of this.metricsList) {
          const formRef = this.$refs[`rulesForm_${metric.id}`]
          if (formRef && formRef[0]) {
            try {
              const values = await formRef[0].getData()
              formResults.push({
                id: metric.id,
                projectId: metric.projectId,
                metricsId: metric.metricsId,
                formValues: values,
              })
            } catch (err) {
              this.$message.error(`请完善【${metric.metricsName}】表单`)
              hasError = true
              // 滚动到验证失败的表单位置
              document.getElementById(`metric-${metric.id}`)?.scrollIntoView({
                behavior: 'smooth',
                block: 'center',
              })
              return false // 直接返回false，不再继续验证其他表单
            }
          }
        }

        return formResults
      } catch (error) {
        console.error('表单验证过程中出错:', error)
        throw error // 继续抛出错误，让调用方处理
      }
    },
    // 保存所有表单数据
    async saveDraftFormData() {
      const formResults = []
      // 遍历所有表单
      for (const metric of this.metricsList) {
        const formRef = this.$refs[`rulesForm_${metric.id}`]
        if (formRef && formRef[0]) {
          const values = await formRef[0].getFieldsValue()
          formResults.push({
            id: metric.id,
            projectId: metric.projectId,
            metricsId: metric.metricsId,
            formValues: values,
          })
        }
      }
      return formResults
    },
    // 暂存
    async saveDraft() {
      // 先验证并获取所有表单数据 不校验
      const formData = await this.saveDraftFormData()
      this.confirmLoading = true
      postAction(this.url.saveDraft, formData).then(res => {
        if (res.success) {
          this.$message.success(res.message)
          this.$emit('ok')
          this.close()
        } else {
          this.$message.error(res.message)
        }
      }).finally(() => {
        this.confirmLoading = false
      })
    },
    async handleOk() {
      try {
        // 先验证并获取所有表单数据
        const formData = await this.saveAllFormData()
        if (formData === false) return // 如果返回false，说明有验证错误，直接返回
        // console.log('验证通过，提交的数据==', formData)
        this.confirmLoading = true
        const res = await postAction(this.url.fillQuestionnaire, formData)
        if (res.success) {
          this.$message.success(res.message)
          this.$emit('ok')
          this.close()
        } else {
          this.$message.warning(res.message)
        }
      } catch (error) {
        // 这里捕获的是验证错误或提交错误
        this.$message.warning(error.message || '提交失败')
      } finally {
        this.confirmLoading = false
      }
    },
    close() {
      this.confirmLoading = false
      this.visible = false
      this.metricsList = []
      this.allFormData = {}
    },
    handleCancel() {
      this.close()
    }
  }
}
</script>

<style scoped lang="less">
@import '~@assets/less/normalModal.less';
::v-deep {
  .ant-spin-nested-loading {
    height: 100% !important;

    .ant-spin-container {
      height: 100% !important;
    }
  }
}
// 锚点样式
::v-deep .ant-anchor-link-title {
  line-height: 3;
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: rgba(0,0,0,0.45);
  font-weight: 400;
}
::v-deep .ant-anchor-ink-ball {
  display: none !important;
}
::v-deep .ant-anchor-link-active > .ant-anchor-link-title {
  color: #000000;
}
::v-deep .ant-anchor-ink::before {
  display: none !important;
}
.task-container {
  width: 100%;
  height: 100%;
  display: flex;

  .metrics-nav {
    width: 300px;
    height: 100%;
    padding: 15px 15px 15px 20px;
    position: relative;
    left: 0;
    top: 0;
    z-index: 99;
    border-right: 1px solid #f0f0f0;
    background-color: #fff;
    overflow-y: auto;

    ::v-deep .ant-anchor {
      padding-left: 8px;
    }
  }

  .metrics-content {
    width: calc(100% - 300px);
    height: 100%;
    overflow-y: auto;
    .metrics-title {
      height: 62px;
      font-family: PingFangSC-Semibold;
      font-size: 20px;
      color: rgba(0,0,0,0.85);
      letter-spacing: 1.11px;
      font-weight: 600;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #F5F7FB;
      border-top: 1px solid #f0f0f0;
      border-bottom: 1px solid #f0f0f0;
    }

    .metric-form {
      padding: 54px 50px 44px 50px;
    }
  }
}
</style>
