<template>
  <a-row class="home">
    <a-col :xl="6" :lg="8" :md="10" :sm="24" class="home-container-left">
      <div class="tree-container">
        <a-tree :load-data="onLoadData" :tree-data="treeData" show-icon>
          <a-icon slot="table" type="table" />
        </a-tree>
      </div>
    </a-col>
    <a-col :xl="18" :lg="16" :md="14" :sm="24" class="home-contaioner-right">
      <div class="home-contaioner-edit">
        <!-- SQL编辑器 -->
        <codemirror-editor
          style="background-color: #ffffff;border-radius: 2px;"
          ref="codemirrorEditor"
          :run-loading="runLoading"
          @commit="onSQLCommit"
          @input="onEditorInput"
          @run="onSQLRun"
          @stop="onSQLStop"
        >
        </codemirror-editor>
        <div class="tables-result">
          <a-tabs :activeKey="defaultActiveKey" type="card" @change="tabClick">
            <a-tab-pane key="1" tab="信息">
              <div style="margin:10px">
                <a-icon v-if="runType === 0" style="color: #909399" type="info-circle" />
                <a-icon v-if="runType === 1" style="color: #67c23a" type="check-circle" />
                <a-icon v-if="runType === 2" style="color: #e6a23c" type="warning" />
                <a-icon v-if="runType === 3" style="color: #f56c6c" type="close-circle" />
                {{ runResult }}
                <template v-if="runType === 3">
                  <div class="error-message"><b>message：</b>{{ errMsg.message }}</div>
                  <div class="error-message"><b>sql：</b>"{{ errMsg.sql }}"</div>
                  <div class="error-message"><b>sqlState：</b>{{ errMsg.state }}</div>
                </template>
              </div>
            </a-tab-pane>
            <a-tab-pane key="2" tab="结果">
              <!-- table区域-begin -->
              <div  style="margin: 10px">
                <a-table
                  style="wordbreak: break-all"
                  ref="table"
                  size="small"
                  :columns="columns"
                  :dataSource="tableData"
                  :loading="loading"
                  :pagination="false"
                  :scroll="tableData.length > 0 ? { x: 'max-content' } : {}"
                  bordered
                  rowKey="id"
                ></a-table>
              </div>
            </a-tab-pane>
          </a-tabs>
        </div>
      </div>
    </a-col>
  </a-row>
</template>
<script>
import CodemirrorEditor from '@/components/CodemirrorEditor'
import { getAction } from '@/api/manage'
import { message } from 'ant-design-vue'

export default {
  name: 'sqlEdit',
  components: {
    CodemirrorEditor,
  },
  data() {
    return {
      winHeight: 0,
      tablesHeight: 0,
      foldIndex: [], // 记录展开状态的表结构，存入index
      treeData: [], // 左侧展示的表结构
      runResult: '暂无数据', // 运行结果提示
      runType: 0, // 运行结果类型，0无结果、1成功、2警告、3异常
      tableData: [], // 运行结果的表数据展示
      columns: [], // 运行的表结构，对象元素，{label: '', value: ''}
      code: '', // 实时输入的代码
      runLoading: false, // 运行加载状态
      defaultActiveKey: '1',
      /* table加载状态 */
      loading: false,
      errMsg: {
        message: '',
        sql: '',
        state: '',
      },
      /* 分页参数 */
      ipagination: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '50'],
        showTotal: (total, range) => {
          return ' 共' + total + '条'
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0,
      },
      showIcon: true,
      url: {
        tablesUrl: '/sqlEditor/query/tables',
        fieldUrl: '/sqlEditor/query/field',
        extUrl: '/sqlEditor/ext',
      },
    }
  },
  created() {
  },
  mounted() {
    this.winHeight = window.innerHeight - 152
    this.tablesHeight = window.innerHeight - 572
    window.onresize = () => {
      this.winHeight = window.innerHeight - 152
      this.tablesHeight = window.innerHeight - 572
    }
	this.queryTables()
  },
  methods: {
    callback(val) {
    },
    getSetting() {
      //this.$refs.codemirrorEditor.init();
    },
    tabClick(key) {
      this.defaultActiveKey = key
    },
    onLoadData(treeNode) {
      return new Promise((resolve) => {
        if (treeNode.dataRef.children) {
          resolve()
          return
        }
        let param = {
          tableName: treeNode.title,
        }
        getAction(this.url.fieldUrl, param).then((res) => {
          if (res.success) {
            var fieldData = []
            res.result.forEach((fname, index) => {
              if (fname && fname.trim() !== '') {
                const treeObj = {
                  title: fname,
                  key: index,
                  isLeaf: true,
                }
                fieldData.push(treeObj)
              }
            })
            treeNode.dataRef.children = [...fieldData]
            this.treeData = [...this.treeData]
          }else{
			this.$message.error(res.message)
		  }
          resolve()
        })
      })
    },
    onFoldTable(index) {
      // 表结构折叠事件
      const i = this.foldIndex.indexOf(index)
      if (i === -1) {
        this.foldIndex.push(index)
      } else {
        this.foldIndex.splice(i, 1)
      }
    },
    copySQLOrder(item) {
      // 复制命令
      this.$refs.codemirrorEditor.setValue(item.sql)
    },
    onEditorInput(code) {
      // 编辑器输入事件
      this.code = code
    },
    onSQLRun() {
      // SQL编辑器运行事件
      this.executeSql()
    },
    onSQLStop() {
      // SQL编辑器停止运行事件
    },
    getTreeData() {
      var res = null

      return res
    },
    getFieldNames() {},
    onSQLCommit() {},
    executeSql() {
      // 执行SQL语句
      if (this.code.trim() === '') {
        this.$message.warning('请先编辑 SQL 命令！')
        return
      }
      this.runLoading = true
      this.tableData = []
      this.columns = []
      this.runResult = '执行中...'
      this.runType = 0
      this.errMsg = {}
      getAction(this.url.extUrl, { sql: this.code }).then((res) => {
        if (this.code.startsWith('SELECT') || this.code.startsWith('select')) {
          if (res.success) {
            this.runType = 1
            this.runResult = `成功查询 ${res.data.length} 条数据`
            this.defaultActiveKey = '2'
            if (res.data.length > 0) {
              this.tableData = [...res.data]
              const obj = { ...this.tableData[0] }
              for (let key in obj) {
                this.columns.push({
                  title: key,
                  dataIndex: key,
                })
              }
            }
          } else {
            this.runType = 3
            this.runResult = '执行失败！'
            this.defaultActiveKey = '1'
            this.errMsg = { ...res }
          }
        } else {
          if (res.success) {
            this.runType = 1
            this.runResult = `执行成功！${res.data}行数据受影响`
            this.defaultActiveKey = '1'
            this.queryTables()
          } else {
            this.runType = 3
            this.defaultActiveKey = '1'
            this.runResult = '执行失败！'
            this.errMsg = { ...res }
          }
        }
      })

      this.runLoading = false
    },
    queryTables() {
      // 查询所有表
      getAction(this.url.tablesUrl).then((res) => {
        if (res.success && res.result.length > 0) {
          this.treeData = []
          const tips = {}
          res.result.forEach((tname, index) => {
            if (tname && tname.trim() !== '') {
              const treeObj = {
                title: tname,
                key: index,
                slots: { icon: 'table' },
              }
              tips[tname] = []
              this.treeData.push(treeObj)
            }
          })
          this.$refs.codemirrorEditor.setHintOptions(tips)
        } else {
          this.$message.error("未获取到数据库表,请检查表查询sql")
        }
      })
    },
  },
}
</script>
<style lang='less' scoped>
/*table表头样式*/
::v-deep .ant-table-thead > tr > th {
  text-align: center;
  white-space: nowrap;
}
.home {
  height: 100%;
  .home-container-left {
    border-radius: 2px;
    background-color: #fff;
  }
  // 右侧区域
  .home-contaioner-right {

    .home-contaioner-edit{

      // 工具栏
      .header-tools {
        height: 40px;
      }
      // 运行结果
      .tables-result {
        border-radius: 2px;
        width: 100%;
        padding: 5px 0 0 1px;
        margin-top: 16px;
        background-color: #ffffff;
        .ant-tabs {
          .ant-tabs-content {
            font-size: 18px;

            .error-message {
              margin: 8px 0px;
              padding-left: 14px;
            }
          }
        }
      }
    }

  }
}
@media (min-width: 768px){
  .home {
    .home-container-left {
      height: 100%;
      background-color: #fff;
      overflow: auto;
    }
    //.home-container-left::-webkit-scrollbar {
    //  width: 5px;
    //}
    //.home-container-left::-webkit-scrollbar-thumb {
    //  border-radius: 10px;
    //  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
    //  background: rgba(0, 0, 0, 0.1);
    //}
    // 右侧区域
    .home-contaioner-right {
      padding-left: 16px;
      height: 100%;
      .home-contaioner-edit{
        height: 100%;
        overflow: auto;
          display: flex;
          flex-flow: column nowrap;

        // 工具栏
        .header-tools {
          height: 40px;
        }
        // 运行结果
        .tables-result {
          flex: 1;
          width: 100%;
          padding: 5px 0 0 1px;
          margin-top: 16px;
          background-color: #ffffff;
          .ant-tabs {
            .ant-tabs-content {
              font-size: 18px;

              .error-message {
                margin: 8px 0px;
                padding-left: 14px;
              }
            }
          }
        }
      }
    }
  }
}
@media (max-width: 767px){
  .home {
    overflow: auto;
    .home-container-left {
     margin-bottom: 16px;
    }
    // 右侧区域
    .home-contaioner-right {
      // 工具栏
      .header-tools {
        height: 40px;
      }
    }

    // 运行结果
    .tables-result {
      width: 100%;
      padding: 5px 0 0 1px;
      margin-top: 16px;
      background-color: #ffffff;
      .ant-tabs {
        .ant-tabs-content {
          font-size: 18px;

          .error-message {
            margin: 8px 0px;
            padding-left: 14px;
          }
        }
      }
    }
  }
}
/*.home {
  height: calc(100% - 60px);
  .home-container-left {
    height: 100%;
    background-color: #fff;
    overflow: auto;
  }
  //.home-container-left::-webkit-scrollbar {
  //  width: 5px;
  //}
  //.home-container-left::-webkit-scrollbar-thumb {
  //  border-radius: 10px;
  //  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  //  background: rgba(0, 0, 0, 0.1);
  //}
  // 右侧区域
  .home-contaioner-right {
    height: 100%;
    // 工具栏
    .header-tools {
      height: 40px;
    }
  }

  // 运行结果
  .tables-result {
    width: 100%;
    padding: 5px 0 0 1px;
    margin-top: 16px;
    background-color: #ffffff;
    .ant-tabs {
      .ant-tabs-content {
        font-size: 18px;

        .error-message {
          margin: 8px 0px;
          padding-left: 14px;
        }
      }
    }
  }
}*/
</style>