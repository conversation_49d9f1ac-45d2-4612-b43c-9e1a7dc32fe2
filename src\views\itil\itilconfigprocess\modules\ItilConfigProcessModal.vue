<template>
  <a-modal
    :title="title"
    :width="modalWidth"
    :visible="visible"
    :confirmLoading="confirmLoading"
    :destroyOnClose="true"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭"
    wrapClassName="ant-modal-cust-warp"
    style="top: 5%; height: 95%; overflow-x: auto; overflow: hidden"
  >
    <div style="padding-top: 0px">
      <fm-generate-form :data="startFormJson" ref="generateStartForm" :value="variables" :remote="remoteFuncs">
      </fm-generate-form>
      <a-tabs :animated="false" default-active-key="1" @change="callback">
        <a-tab-pane key="1" tab="附件">
          <div class="clearfix">
            <j-upload v-model="files" :number="5"></j-upload>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>
  </a-modal>
</template>
<script>
import { getAction, postAction } from '@/api/manage'
import JUpload from '@/components/jeecg/JUpload'
export default {
  name: 'itilConfigProcessmodal',
  components: {
    JUpload,
  },
  data() {
    return {
      title: '操作',
      confirmLoading: false,
      processDefinition: { formKey: 'itilconfigprocess_from' },
      /* 弹框宽 */
      modalWidth: '800px',
      form: this.$form.createForm(this),
      visible: false,
      required: false,
      startFormJson: undefined,
      variables: undefined,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      // 上传相关
      previewVisible: false,
      previewImage: '',
      files: '',

      remoteFuncs: {
        getType(resolve) {
          // 下拉选择框 select_1566990949275
          // 获取到远端数据后执行回调函数
          // resolve(data)
          // 模拟数据获取
          // 对象中 value、label 是设计器中配置的值和标签

          getAction('/itilconfigitemtype/itilConfigItemType/getTypeList').then((res) => {
            if (res.success) {
              const options = res.result
              // this.remoteFuncs.options = formData
              resolve(options)
            }
          })
          // resolve(this.options)
        },
        getUser(resolve) {
          getAction('/sys/user/users').then((res) => {
            if (res.success) {
              const options = res.result
              // this.remoteFuncs.users = formData
              resolve(options)
            }
          })
          // 对象中 value、label 是设计器中配置的值和标签
        },
      },
      // 关联配置项
    }
  },
  created() {
    this.variables = null
  },
  mounted() {
    if (this.processDefinition.formKey) {
      getAction('/flowableform/umpFlowableForm/queryByKey', {
        key: this.processDefinition.formKey,
        tableId: null,
      }).then((res) => {
        if (res.success) {
          var formData = res.result
          if (formData && formData.formJson) {
            this.startFormJson = JSON.parse(formData.formJson)
            this.startFormJson.list[1].rules[2].message = '名称不能超过16个字符'
            this.startFormJson.list[8].rules[0].message = '配置项描述不能超过30个字符'
            this.variables = null
          }
        }
      })
    }
  },
  methods: {
    initData(v, releaseId) {
      this.releaseId = releaseId
      this.processDefinition = v
      this.variables = null
      this.visible = true
      this.files = '' //文件
      if (this.processDefinition.tableId) {
        this.getData()
      }
      if (null != v.busId) {
        getAction('/businessrelation/actZBusinessRelation/list', { processId: this.processDefinition.busId }).then(
          (res) => {
            if (res.success) {
              this.files = res.result.fileUrlList
            }
          }
        )
      }
    },
    //获取数据
    getData() {
      if (this.processDefinition.formKey) {
        getAction('/flowableform/umpFlowableForm/queryByKey', {
          key: this.processDefinition.formKey,
          tableId: this.processDefinition.tableId,
        }).then((res) => {
          if (res.success) {
            var formData = res.result
            if (formData && formData.formJson) {
              // this.startFormJson = JSON.parse(formData.formJson)
              this.variables = JSON.parse(formData.formValue)
            }
          }
        })
      }
    },
    // 关闭弹框
    close() {
      this.$emit('close')
      this.visible = false
      this.current = 0
    },
    // 提交
    handleOk() {
      if (this.$refs.generateStartForm) {
        if (this.processDefinition.tableId) {
          this.$refs.generateStartForm
            .getData()
            .then((values) => {
              if (values && values != undefined) {
                let formData = Object.assign(this.data || {}, values)
                formData.procDefId = this.processDefinition.id
                formData.procDeTitle = this.processDefinition.name
                formData.form_value = JSON.stringify(values)
                //Object.assign({processInstanceFormData}, values)
                formData.filedNames = 'form_value' + ',' + 'form_key'
                formData.form_key = this.processDefinition.formKey
                formData.id = this.processDefinition.tableId
                let faleUrl = ''
                if (this.files instanceof Array) {
                  for (var i = 0; i < this.files.length; i++) {
                    faleUrl = faleUrl + ',' + this.files[i]
                  }
                } else {
                  faleUrl = this.files
                }
                formData.file = faleUrl
                postAction('/itilconfigprocess/ItilConfigProcess/editForm', formData).then((res) => {
                  this.uploading = false
                  if (res.success) {
                    this.$message.success('保存成功')
                    this.visible = false
                    this.$emit('ok')
                  } else {
                    this.$message.warning(res.message)
                    this.visible = false
                    this.$emit('ok')
                  }
                })
              }
            })
            .catch((e) => {})
        } else {
          this.$refs.generateStartForm
            .getData()
            .then((values) => {
              if (values && values != undefined) {
                let formData = Object.assign(this.data || {}, values)
                formData.form_value = JSON.stringify(values)
                //Object.assign({processInstanceFormData}, values)
                formData.filedNames = 'form_value' + ',' + 'form_key'
                formData.form_key = this.processDefinition.formKey
                formData.file = this.files
                formData.releaseId = this.releaseId
                postAction('/itilconfigprocess/ItilConfigProcess/add', formData).then((res) => {
                  this.uploading = false
                  if (res.success) {
                    this.$message.success('保存成功')
                    this.visible = false
                    this.$emit('ok')
                  } else {
                    this.$message.warning(res.message)
                    this.visible = false
                    this.$emit('ok')
                  }
                })
              }
            })
            .catch((e) => {})
        }
      }
    },
    handleCancel() {
      this.close()
    },
    // tab
    callback(key) {},
    // 上传相关
    onCancel() {
      this.previewVisible = false
    },
    async handlePreview(file) {
      if (!file.url && !file.preview) {
        file.preview = await getBase64(file.originFileObj)
      }
      this.previewImage = file.url || file.preview
      this.previewVisible = true
    },
    handleChange({ fileList }) {
      this.fileList = fileList
    },
  },
}
</script>
<style scoped lang="less">
::v-deep .ant-modal-body {
  padding: 24px 48px 24ox 48px;
}
::v-deep .ant-modal {
  padding: 24px;
}
@media (max-width: 812px) {
  ::v-deep .ant-modal {
    top: 0px;
    width: 1000px !important;
    max-width: 1000px !important;
    margin: 0 !important;
  }

  .ant-modal {
    top: 0px;
    width: 1000px !important;
    max-width: 1000px !important;
    margin: 0 !important;
  }
}
</style>
