<template>
  <a-row :gutter='10' style='height: 100%' class='vScroll'>
    <a-col style='width: 100%; height: 100%; display: flex; flex-direction: column'>
      <!-- 查询区域 -->
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <div class='table-page-search-wrapper-style'>
          <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
            <a-row :gutter='24' ref='row'>
              <a-col :span='spanValue'>
                <a-form-item label='终端名称'>
                  <a-input :maxLength='maxLength' placeholder='请输入终端名称' v-model='queryParam.terminalName' :allowClear='true'
                    autocomplete='off' />
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label='使用人'>
                  <a-input :maxLength='maxLength' placeholder='请输入使用人' v-model='queryParam.username' :allowClear='true' autocomplete='off' />
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label='绑定状态'>
                  <j-dict-select-tag v-model='queryParam.bindFlag' placeholder='请选择绑定状态' dictCode='bind_status' />
                </a-form-item>
              </a-col>
              <a-col :span='colBtnsSpan()'>
                <span class='table-page-search-submitButtons'
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button type='primary' class='btn-search btn-search-style' @click='searchQuery'>查询</a-button>
                  <a-button class='btn-reset btn-reset-style' @click='searchReset'>重置</a-button>
                  <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <!-- 查询区域-END -->
      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <!-- table区域-begin -->
        <a-table ref='table' bordered rowKey='id' :columns='columns' :dataSource='dataSource'
          :scroll='dataSource.length>0?{x:"max-content"}:{}' :pagination='ipagination' :loading='loading'
          @change='handleTableChange'>
          <span class='caozuo' slot='action' slot-scope='text, record'>
            <a @click="handleDetailPage(record)" v-if="record.bindStatus != '未绑定'">查看</a>
            <a-divider type="vertical" v-if="record.bindStatus != '未绑定'" />
            <a @click='userBind(record)' v-if="record.bindStatus == '未绑定'">绑定用户</a>
            <!-- <a @click='unBind(record)' v-else>解除绑定</a> -->
            <a-popconfirm title="确定解除绑定吗?" @confirm="() => unBind(record)" v-else>
              <a style="color: #409eff">解除绑定</a>
            </a-popconfirm>
          </span>
          <template slot='tooltip' slot-scope='text'>
            <a-tooltip placement='topLeft' :title='text' trigger='hover'>
              <div class='tooltip'>
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
      </a-card>
      <terminal-bind-modal ref='modalForm' @ok='modalFormOk'></terminal-bind-modal>
    </a-col>
  </a-row>
</template>

<script>
  import '@/assets/less/TableExpand.less'
  import {
    mixinDevice
  } from '@/utils/mixin'
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import TerminalBindModal from './modules/TerminalBindModal'
  import JSuperQuery from '@/components/jeecg/JSuperQuery.vue'
  import {
    putAction,
    getAction,
  } from '@/api/manage'
  import JDictSelectTag from '@/components/dict/JDictSelectTag.vue'
  import YqAreaCascaderSelect from '@/components/areaDict/YqAreaCascaderSelect'
  import {
    YqFormSearchLocation
  } from '@/mixins/YqFormSearchLocation'

  export default {
    name: 'TerminalBindList',
    mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
    components: {
      TerminalBindModal,
      JSuperQuery,
      JDictSelectTag,
      YqAreaCascaderSelect
    },
    data() {
      return {
        maxLength:50,
        description: '终端绑定管理页面',
        formItemLayout: {
          labelCol: {
            style: 'width:90px'
          },
          wrapperCol: {
            style: 'width:calc(100% - 90px)'
          }
        },
        columns: [{
            title: '终端名称',
            dataIndex: 'name'
          },
          {
            title: '使用人',
            dataIndex: 'bindUser'
          },
          {
            title: '绑定状态',
            dataIndex: 'bindStatus'
          },
          {
            title: '绑定时间',
            dataIndex: 'bindTime'
          },
          {
            title: '操作',
            dataIndex: 'action',
            width: 120,
            scopedSlots: {
              customRender: 'action'
            }
          }
        ],
        url: {
          list: '/terminal/terminalDevice/selectTerWithUser',
          unBindTerminal: '/terminal/terminalDevice/unBindTerminal',
        }
      }
    },
    mounted() {
      this.loadData(1)
    },

    methods: {
      userBind(item) {
        this.$refs.modalForm.add(item)
      },
      unBind(item) {
        var that = this
        this.$confirm({
          title: '清除确认',
          okText: '是',
          cancelText: '否',
          content: '是否清除绑定信息(绑定信息为运维助手终端注册内字段)?',
          onOk: function () {
            that.loading = true
            getAction(that.url.unBindTerminal, {
              uniqueCode: item.uniqueCode
            }).then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.loadData()
              } else {
                that.$message.warning(res.message)
                that.loadData()
              }
            })
          },
          onCancel() {
            that.loading = true
            getAction(that.url.unBindTerminal, {
              uniqueCode: item.uniqueCode,
              isClear: false
            }).then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.loadData()
              } else {
                that.$message.warning(res.message)
                that.loadData()
              }
            })
          },
        })
      },
    }
  }
</script>
<style lang='less' scoped>
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';

  /*操作--设置按钮背景色*/
  ::v-deep .ant-table-filter-icon {
    background-color: #e5e5e5;
  }
</style>