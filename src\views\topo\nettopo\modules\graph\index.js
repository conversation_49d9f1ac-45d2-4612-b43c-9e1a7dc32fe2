import { Graph, Addon, FunctionExt, Shape } from '@antv/x6'
import './shape'
import {
  globalGridAttr
} from '../models/global'
export default class FlowGraph {
  // public static graph: Graph
  // private static stencil: Addon.Stencil
  /* 
    * static 静态方法 只能类本身自己使用 实例无法使用
  */
  // 编辑模式下画布的初始化
  static init (container,isEdit) {
    // 创建画布
    this.graph = new Graph({
      container: document.getElementById(container),
      grid: false,// 网格大小 10px，并绘制网格
      // 启用对齐线
      snapline: isEdit,
      async: false,
      history: { //开启画布撤销/重做能力 只追踪节点连线的添加 忽略属性变化
        enabled: isEdit,
        ignoreAdd: false,
        ignoreRemove: false,
        ignoreChange: true,
        beforeAddCommand(event,args){
          if(args.cell.shape === "edgeDragNode"){
            return false;
          }
          
        }
      },
      //监听容器大小改变，并自动更新画布大小
      autoResize: true,
      //普通画布(未开启 scroller 模式)通过开启 panning 选项来支持拖拽平移
      panning: {
        enabled: true,
      },
      scroller: {
        enabled: false, // 低版本x6使用minimap必须配置true
        // pannable: true, //画布平移
        className: 'my-scroller',
      },
      resizing:{//缩放节点，默认禁用
        enabled: false,
      },
      clipboard: {//剪切板，默认禁用
        enabled: isEdit
      },
      keyboard: {//键盘快捷键，默认禁用
        enabled: true
      },
      interacting:{//定制节点和边的交互行为
        nodeMovable: isEdit,
        edgeMovable:false,
      },
      //配置节点的可移动区域
      translating: {
        restrict: isEdit,//节点移动时无法超过画布区域
      },
      
      mousewheel: {//鼠标滚轮的默认行为是滚动页面，启用 Scroller 后用于滚动画布
        enabled: true,
      },
      selecting: {//点击或者套索框选节点
        enabled: isEdit,
        multiple: true,
        rubberband: true, // 启用框选
        modifiers:'alt',//配合alt按键框选
        movable: true,
        showNodeSelectionBox: true,//显示节点的选择框
        showEdgeSelectionBox: true,//显示边的选择框
        pointerEvents: 'none',
        // content: "拖动框选的节点，可以整体移动",
      },
      minimap: {
        enabled: true,
        container: document.getElementById('minimap'),
        width: 200,
        height: 160,
        scalable: false // 是否可以缩放
    },
      connecting: {//配置全局的连线规则
        anchor: 'nodeCenter',
        connectionPoint:  { 
          name: 'boundary',
          args: {
            sticky: true,
            extrapolate:true,
            stroked:false,
            // offset:-15,
          },},
        allowBlank: false,
        allowNode:true,
        highlight: true,
        snap: {
          radius: 20
        },
        createEdge() {//连接的过程中创建新的边
          return new Shape.Edge({
            attrs: {
              line: {
                stroke: 'rgba(0,0,0,.6)',
                strokeWidth: 2,
                strokeDasharray:globalGridAttr.topoConfig.edgeDash,
                sourceMarker:[1,3].includes(globalGridAttr.topoConfig.edgeMarker)? { name:'classic' }: "",
                targetMarker: [2,3].includes(globalGridAttr.topoConfig.edgeMarker)?{ name:'classic' }:"",
              }
            },
            connector:{name:globalGridAttr.topoConfig.edgeType===2?"smooth":"normal"},
            router: {
              name: globalGridAttr.topoConfig.edgeType===1?"manhattan":"normal"
            },
            data:{
              edgeType:"connecting",
              flow:false,
            }
          })
        },
        validateConnection({ sourceView, targetView, sourceMagnet, targetMagnet,targetCell}) {
          if (sourceView === targetView) {
            return false
          }
          if (!sourceMagnet) {
            return false
          }
          // if (!targetMagnet) {
          //   return false
          // }
          if(targetCell && targetCell.shape !== "networkGroupNode" && targetCell.shape !== "text-block"&& !targetMagnet){
            return true;
          }
          // return true
        }
      },
      embedding: {//通过embedding可以将一个节点拖动到另一个节点中，使其成为另一节点的子节点
        enabled: isEdit,
        frontOnly:true,
        findParent ({ node }) {
          const bbox = node.getBBox()
          return this.getNodes().filter((node) => {
            // 只有 data.parent 为 true 的节点才是父节点
            const data = node.getData()
            if (data && data.parent) {
              const targetBBox = node.getBBox()
              return bbox.isIntersectWithRect(targetBBox)
            }
            return false
          })
        }
      },
      highlighting: {
        // 当链接桩可以被链接时，在链接桩外围渲染一个 2px 宽的红色矩形框
        magnetAvailable: {
          name: 'stroke',
          args: {
            padding: 4,
            attrs: {
              strokeWidth: 4,
              stroke: 'rgba(223,234,255)'
            }
          }
        }
      },
    })
    return this.graph
  }

  // 销毁
  static destroy () {
    this.graph.dispose()
  }
}
