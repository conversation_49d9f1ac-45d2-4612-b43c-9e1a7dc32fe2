<template>
  <div class="empty-wrap">
    <img :src="imgSrc" :width="imgWidth" />
    <div class="text">{{description}}</div>
  </div>
</template>
<script>
export default {
  name: 'Empty',
  data() {
    return {}
  },
  props: {
    imgWidth: {
      type: Number,
      default: 126
    },
    imgSrc: {
      type: String,
      default: require('/public/oneClickHelp/nodata.png')
    },
    description: {
      type: String,
      default: '暂无数据'
    }
  }
}
</script>
<style scoped lang="less">
.empty-wrap {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  img {
    margin-bottom: 10px;
  }
  .text {
    color: #999;
    font-size: 14px;
  }
}
</style>
