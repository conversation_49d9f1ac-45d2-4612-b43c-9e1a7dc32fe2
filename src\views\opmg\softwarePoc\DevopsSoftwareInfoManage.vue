<template>
  <div style="height:100%">
      <component :is="pageName" :data="data"/>
  </div>
</template>
<script>
  import SoftwareList from './SoftwareList.vue'
  export default {
    name: "SoftwareInfoManage",
    data() {
      return {
        isActive: 0,
        data:{}
      };
    },
    components: {
      SoftwareList
    },
    created(){
      this.pButton1(0);
    },
    //使用计算属性
    computed: {
      pageName() {
        switch (this.isActive) {
          case 0:
            return "SoftwareList";
            break;
          default:
            return "SoftwareInfoDetails";
            break;
        }
      }
    },
    methods: {
      pButton1(index) {
        this.isActive = index;
      },
      pButton2(index,item) {
        this.isActive = index;
        this.data = item;
      }
    }
  }
</script>