<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-item label="策略名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['proName', formValidator.proName]" :allowClear="true" autocomplete="off"
                       placeholder="请输入策略名称"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="执行cron码" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-cron ref="innerVueCron"
                      v-decorator="['taskCron', { initialValue: '0 0 0 * * ? *' }, validatorRules.taskCron]"
                      @change="setCorn"></j-cron>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="文件类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag type="list" v-decorator="['whCondense', validatorRules.whCondense]"
                                 :trigger-change="true" dictCode="wh_condense" placeholder="请选择文件类型" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="目标文件夹名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['fileName', validatorRules.fileName]" :allowClear="true" autocomplete="off"
                       placeholder="请输入目标文件夹名称"></a-input>
            </a-form-item>
          </a-col>
          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
import {
  httpAction,
  getAction
} from '@/api/manage'
import pick from 'lodash.pick'
import {
  validateDuplicateValue
} from '@/utils/util'
import JFormContainer from '@/components/jeecg/JFormContainer'
import JDictSelectTag from '@/components/dict/JDictSelectTag'
import JCron from '@/components/jeecg/JCron.vue'
import {
  duplicateCheck
} from '@/api/api'

export default {
  name: 'DevopsBackupProForm',
  components: {
    JFormContainer,
    JDictSelectTag,
  },
  props: {
    //流程表单data
    formData: {
      type: Object,
      default: () => {},
      required: false,
    },
    //表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false,
    },
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false,
    },
  },
  data() {
    return {
      form: this.$form.createForm(this),
      model: {},
      labelCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 5
        },
      },
      wrapperCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 16
        },
      },
      confirmLoading: false,
      backupProId: '',
      validatorRules: {
        // proName:{
        //   rules: [
        //     {required: true,message:"请输入策略名称"},
        //   ]
        // },
        taskCron: {
          rules: [{
            required: true,
            message: '请输入执行cron码'
          }],
        },
        whCondense: {
          rules: [{
            required: true,
            message: '请选择文件类型'
          }],
        },
        fileName: {
          rules: [{
            required: true,
            message: '请输入备份文件格式'
          },
            {
              min: 2,
              message: '备份文件名称长度应在 2-20 之间！',
              trigger: 'blur'
            },
            {
              max: 20,
              message: '备份文件名称长度应在 2-20 之间！',
              trigger: 'blur'
            }
          ],
        },
      },
      formValidator: {
        proName: {
          rules: [{
            required: true,
            message: '请输入策略名称'
          },
            {
              min: 2,
              max: 30,
              message: '长度在2到30个字符',
              trigger: 'blur'
            },
            {
              validator: this.validateProName
            },
          ],
        },
      },
      url: {
        add: '/devopsbackuppro/devopsBackupPro/add',
        edit: '/devopsbackuppro/devopsBackupPro/edit',
        queryById: '/devopsbackuppro/devopsBackupPro/queryById',
      },
    }
  },
  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    },
  },
  created() {
    //如果是流程中表单，则需要加载流程表单data
    this.showFlowData()
  },
  methods: {
    add() {
      this.edit({})
    },
    setCorn(data) {
      if (data && data.target != null) {
        let dataList = data.target.value.split(' ')
        if (dataList[0] == '*') {
          this.$message.warning('请确认是否每秒都执行')
        }
      } else {
        let dataList = data.split(' ')
        if (dataList[0] == '*') {
          this.$message.warning('请确认是否每秒都执行')
        }
      }
      this.$nextTick(() => {
        this.form.cronExpression = data
      })
    },
    edit(record) {
      this.backupProId = record.id
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(pick(this.model, 'proName', 'taskCron', 'whCondense', 'fileName'))
      })
    },

    validateProName(rule, value, callback) {
      var params = {
        tableName: 'devops_backup_pro',
        fieldName: 'pro_name',
        fieldVal: value,
        dataId: this.backupProId,
      }
      duplicateCheck(params).then((res) => {
        if (res.success) {
          callback()
        } else {
          callback('策略名称已存在!')
        }
      })
    },
    //渲染流程表单数据
    showFlowData() {
      if (this.formBpm === true) {
        let params = {
          id: this.formData.dataId
        }
        getAction(this.url.queryById, params).then((res) => {
          if (res.success) {
            this.edit(res.result)
          }
        })
      }
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let formData = Object.assign(this.model, values)
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    popupCallback(row) {
      this.form.setFieldsValue(pick(row, 'proName', 'taskCron', 'whCondense', 'fileName'))
    },
  },
}
</script>