<template>
  <div>
    <a-table
      ref="table"
      bordered
      :rowKey="(record) => { return record.id}"
      :columns="columns"
      :dataSource="dataSource" :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
      :pagination="ipagination"
      :loading="loading"
      @change='handleTableChange'
    >
      <template slot="tooltip" slot-scope="text">
        <a-tooltip placement="topLeft" :title="text" trigger="hover">
          <div class="tooltip">
            {{ text }}
          </div>
        </a-tooltip>
      </template>
      <span slot="action" slot-scope="text, record">
        <a @click='btnView(record)'  v-has="'assets:processView'">查看</a>
      </span>
    </a-table>
    <process-instance-info-modal ref="processInstanceInfoModalForm"></process-instance-info-modal>
  </div>
</template>

<script>
import {JeecgListMixin} from '@/mixins/JeecgListMixin'
import ProcessInstanceInfoModal from '@views/flowable/process-instance/modules/ProcessInstanceInfoModal.vue'
import { getAction } from '@api/manage'
export default {
  name:'AssetAssociationProcessList',
  props:{
    assetsInfo:{
      type:Object,
      required:true
    }
  },
  components: { ProcessInstanceInfoModal },
  mixins: [JeecgListMixin,],
  data() {
    return {
      disableMixinCreated:true,
      ipagination: {
        current: 1,
        pageSize: 5,
        pageSizeOptions: ['5', '10', '20'],
        showTotal: (total, range) => {
          return ' 共' + total + '条'
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0
      },
      columns: [
        {
          title: '业务标题',
          dataIndex: 'name',
          customCell: () => {
            let cellStyle = 'text-align:center'
            return {
              style: cellStyle
            }
          },
          sorter: true
        },
        {
          title: '所属流程',
          dataIndex: 'processDefinitionName',
          customCell: () => {
            let cellStyle = 'text-align:center'
            return {
              style: cellStyle
            }
          },
          sorter: true
        },
        {
          title: '流程版本',
          dataIndex: 'processDefinitionVersion',
          customRender: (text) => {
            //字典值替换通用方法
            return 'v' + text
          },
          customCell: () => {
            let cellStyle = 'text-align:center'
            return {
              style: cellStyle
            }
          },
        },
        {
          title: '当前审批环节',
          dataIndex: 'currTaskName',
          customCell: () => {
            let cellStyle = 'text-align:center'
            return {
              style: cellStyle
            }
          },
        },
        {
          title: '状态',
          dataIndex: 'status',
          scopedSlots: {
            customRender: 'status'
          },
          customCell: () => {
            let cellStyle = 'text-align:center;width:100px'
            return {
              style: cellStyle
            }
          },
        },
        {
          title: '提交申请时间',
          dataIndex: 'startTime',
          customCell: () => {
            let cellStyle = 'text-align:center;width:160px'
            return {
              style: cellStyle
            }
          },
          sorter: true
        },
        {
          title: '结束时间',
          dataIndex: 'endTime',
          customCell: () => {
            let cellStyle = 'text-align:center;width:160px'
            return {
              style: cellStyle
            }
          },
          sorter: true
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 210,
          fixed: 'right',
          scopedSlots: {
            customRender: 'action',
          }
        }
      ] ,
      url: {
        list: '/flowable/processInstance/getProcessAssociationChildById',
      },
    }
  },
  watch:{
    assetsInfo:{
      handler(val){
        this.show(val.id)
      },
      deep:true,
      immediate:true
    },
  },
  methods: {
    show(assetsId) {
      if(assetsId){
        this.queryParam.primaryId = assetsId
        this.loadData(1)
      }
    },
    btnView(record) {
      this.$refs.processInstanceInfoModalForm.init(record.id)
      this.$refs.processInstanceInfoModalForm.title = '关联流程信息'
      this.$refs.processInstanceInfoModalForm.disableSubmit = false
    },
    loadData(arg) {
      if (!this.url.list) {
        this.$message.error('请设置url.list属性!')
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1
      }
      var params = this.getQueryParams() //查询条件
      this.loading = true
      getAction(this.url.list, params).then((res) => {
        if (res.success&&res.result) {
          //update-begin---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
          this.dataSource = res.result.records || res.result
          if (this.dataSource.length < 9) {
            this.clientHeight = false
          }
          //author:weng    Date:20210402  for：if(res.result.total>0) 有错误，无查询结果时，页码显示有问题
          this.ipagination.total = res.result.total
          //update-end---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
        }
        if (res.code === 510) {
          this.$message.warning(res.message)
        }
        this.loading = false
      })
    },
  }
}
</script>

<style lang="less" scoped="scoped">
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
</style>
