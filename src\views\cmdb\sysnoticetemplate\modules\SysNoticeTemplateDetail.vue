<template>
  <a-card style="height: 100%;">
    <div style="text-align: right;margin-bottom: 12px;" >
      <img src="~@/assets/return1.png" alt="" @click="getGo" style="width: 20px;height: 20px;cursor: pointer">
    </div>
    <j-form-container disabled>
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-item class="two-words" label="名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-model="name"
                       :allowclear="true"
                       autocomplete="off"/>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="关联业务" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <!--<a-input v-decorator="['business', validatorRules.assetsCode]" placeholder="请输入关联业务"></a-input>-->
              <a-cascader
                :default-value="[]"
                :field-names="{ label: 'code', value: 'type', children: 'items' }"
                :options="options"
                :load-data="loadData"
                placeholder=""
                change-on-select
              />
            </a-form-item>
          </a-col>
          <!-- 附件 -->
          <dev v-if="mold === 'email'">
            <a-col :span="24">
              <a-form-item class="two-words" label="附件" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <!-- <journal-manage ref="journalManage" :deviceInfo="record"></journal-manage> -->
                <div class="clearfix">
                  <j-upload v-model="files" :number="5"></j-upload>
                </div>
              </a-form-item>
            </a-col>
          </dev>
          <dev v-if="mold === 'email'">
            <a-col :span="24">
              <a-form-item class="two-words" label="标题" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input v-model="subject" placeholder="请输入标题"></a-input>
              </a-form-item>
            </a-col>

            <a-col :span="24">
              <a-form-item label="收件人" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input v-model="sendTo" placeholder="请输入收件人，请用“，”隔开"></a-input>
              </a-form-item>
            </a-col>
          </dev>

           <dev v-if="mold === 'message'">
            <a-col :span="24">
              <a-form-item class="two-words" label="短信模板编码" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input v-model="subject" placeholder="请输入编码"></a-input>
              </a-form-item>
            </a-col>

             <a-col :span="24">
              <a-form-item class="two-words" label="短信签名" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input v-model="signName" placeholder="请输入签名"></a-input>
              </a-form-item>
            </a-col>

            <a-col :span="24">
              <a-form-item label="收信人" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input v-model="sendTo" placeholder="请输入收信人，请用“，”隔开"></a-input>
              </a-form-item>
            </a-col>
          </dev>

          <dev v-if="mold === 'dingding_robot'">
            <a-col :span="24">
              <a-form-item class="two-words" label="标题" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input v-model="subject" placeholder="请输入标题"></a-input>
              </a-form-item>
            </a-col>
          </dev>

          <dev v-if="mold === 'weixin'">
            <a-col :span="24">
              <a-form-item label="微信模板id" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input v-model="subject" placeholder="请输入标题微信模板id"></a-input>
              </a-form-item>
            </a-col>
          </dev>

          <a-col :span="24"  v-if="this.mold === 'weixin'" >
            <a-form-item label="关联人" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-select mode="multiple" :default-value="realName"  placeholder="请选择" @change="getUserId($event)">
                <a-select-option v-for="coupon in dataSource" :key="coupon.id" :value="coupon.id">{{
                  coupon.realName
                  }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <a-col :span="24"  v-if="this.mold === 'shengshitong'" >
            <a-form-item label="关联人" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-select mode="multiple" :default-value="realName"  placeholder="请选择" @change="getUserId($event)">
                <a-select-option v-for="coupon in dataSource" :key="coupon.id" :value="coupon.id">{{
                  coupon.realName
                  }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <dev v-if="mold === 'weixin'">
            <a-col :span="24">
              <a-row :gutter="24">
                <a-form-item
                  v-for="(items, index) in weixinExtendsList"
                  :key="index"
                  label="消息模板内容"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                >
                    <a-input :span="8" v-model="items.value" placeholder="请输入消息模板内容"></a-input>
                </a-form-item>
              </a-row>
            </a-col>
          </dev>
          <a-col :span="24">
            <a-form-item label="模板内容" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <div class="textStyle">
                <div v-html="jeditor"></div>
              </div>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-card>
</template>

<script>
  import { httpAction, getAction } from '@/api/manage'
  import pick from 'lodash.pick'
  import { validateDuplicateValue } from '@/utils/util'
  import JFormContainer from '@/components/jeecg/JFormContainer'
  import JEditor from '@/components/jeecg/JEditor'
  import JUpload from '@/components/jeecg/JUpload'
  export default {
    name: 'SysNoticeTemplateDetail',
    components: {
      JFormContainer,
      JUpload,
      JEditor,
    },
    props: {
     data:{
       type:Object
     }
    },
    data() {
      return {
        files: '',
        form: this.$form.createForm(this),
        model: {},
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        url: {
          add: '/sys/notice/template/add',
          edit: '/sys/notice/template/edit',
          queryById: '/sys/notice/template/queryById',
          findAll: '/sys/notice/config/findAll',
          alarm: '/sys/notice/template/alarm', // 第一层
          level: '/sys/notice/template/level', // 第2层
          assets: '/sys/notice/template/assets', // 第3层
          productList: '/sys/notice/template/productList', // 第4层  参数：assetsId
          devList: '/sys/notice/template/devList', // 第5层 参数 productId
          list: '/sysNoticeExtend/sysNoticeExtend/findUserExtend',
        },
        NoticeConfig: [],
        noticeConfigid: '',
        addFlag: false,
        type: '',
        mold: '',
        subject: '',
        sendTo: '',
        signName: '',
        name: '',
        type1: '',
        dataSource: [],
        realName:[],
        couponList: [
          {
            id: 'email',
            name: '邮箱',
          },
          {
            id: 'dingding_robot',
            name: '钉钉机器人',
          },
          {
            id: 'weixin',
            name: '微信',
          },
          {
            id: 'shengshitong',
            name: '盛事通',
          },
        ],
        userId: '',
        weixinList: [{ name: '收件人', code: 'from', value: '' }],
        weixinExtendsList: [],

        validatorRules: {
          assetsCode: {
            rules: [{ required: true, message: '请输入关联业务!' }],
          },
          assetsName: {
            rules: [{ required: true, message: '请输入名称!' }],
          },
          content: {
            rules: [{ required: true, message: '请输入模板内容!' }],
          },
        },
        options: [],
        jeditor: {
          value: '',
        },
        isClear: false, //设置为true的时候，这个可以用this.wangEditorDetail=''来替代
        wangEditorDetail: '',
        hierarchy: '',
        business: {
          type: '',
          alarm_level: '',
          product_category_ids: [],
          product_ids: [],
          device_ids: [],
        },
      }
    },
    computed: {
    },
    mounted(){
      this.mold = this.data.type;
      this.name = this.data.name;
      this.queryById(this.data)
      this.wangEditorDetail = 'wangEditorDetail默认值'

    },
    watch:{
      data:function(val,oldVal){
        this.mold = val.type;
        this.name = val.name;
        this.queryById(val)

      }
    },
    created() {
      this.getUserList()
      //如果是流程中表单，则需要加载流程表单data
      this.showFlowData()
      this.getNoticeConfig()
      getAction(this.url.alarm).then((res) => {
        this.options = res.result
        for (let i = 0; i < this.options.length; i++) {
          this.options[i].isLeaf = false
        }
      })
    },
    methods: {
      //返回上一级
      getGo() {
        this.$parent.pButton2(0);
      },
      wangEditorChange(val) {
      },
      queryById(record){
        getAction(this.url.queryById, {id: record.id}).then((res) => {

          this.name = res.result.name
          this.business = res.result.business
          this.type1 = res.result.type1
          this.mold = res.result.mold
          this.files = res.result.files
          this.sendTo = res.result.sendTo
          this.signName=res.result.signName
          this.subject = res.result.subject
          this.jeditor = res.result.content
          this.noticeConfigid = res.result.noticeConfigId
          this.weixinExtendsList = res.result.weixinExtendsList
          this.realName = res.result.realName
        })
      },
      // this.name = res.result.name
      // this.shortFiles = res.result.files
      // this.business = res.result.business
      // this.type1 = res.result.type1
      // this.mold = res.result.mold
      // this.files = res.result.files
      // this.sendTo = res.result.sendTo
      // this.subject = res.result.subject
      // this.jeditor.value = res.result.content
      // this.noticeConfigid = res.result.noticeConfigId
      // this.weixinExtendsList = res.result.weixinExtendsList
      // this.realName = res.result.realName

      addPro() {
        this.weixinExtendsList.push({
          value: this.value,
        })
      },
      getCouponSelected(e) {
        this.mold = this.type1
      },

      getUserList() {
        getAction(this.url.list).then((res) => {
          this.dataSource = res.result
        })
      },

      getUserId(e) {
        this.userId = e.join(",")
      },

      getId(e) {
        this.noticeConfigid = this.NoticeConfig[e].id
        this.mold = this.NoticeConfig[e].type;
      },
      getNoticeConfig(e) {
        getAction(this.url.findAll).then((res) => {
          if (res.success) {
            this.NoticeConfig = res.result
          }
        })
      },

      popupCallback(row) {
        this.form.setFieldsValue(pick(row, 'noticeConfigId', 'name', 'business', 'template', 'delflag'))
      },
    }
  }
</script>
<style lang="less" scoped>
  ::v-deep .two-words > div > label{
    letter-spacing:4px;
  }
  ::v-deep .two-words > div > label::after{
    letter-spacing:0px;
  }
  .ant-form-item{
    margin-bottom: 12px!important;
  }
  .textStyle{
    border: 1px solid #d9d9d9;
    padding: 25px;
    border-radius: 4px;
  }
</style>

