<template>
  <yq-modal
    :title="title"
    :width="width"
    :visible="visible"
    :destroyOnClose="true"
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }"
    :centered="true"
    @ok="handleOk"
    :ok-close="false"
    @cancel="handleCancel"
    cancelText="关闭"
  >
    <template slot="footer">
      <a-button @click="handleCancel">关闭</a-button>
      <a-button type="primary" @click="handleOk" v-if="!show">确定</a-button>
    </template>
    <a-spin :spinning="confirmLoading">
      <j-form-container :disabled="disableSubmit">
        <a-form-model
          ref="model"
          slot="detail"
          :model="model"
          :rules="validatorRules"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
        >
          <a-row :gutter="24">
            <a-col :sm="24" :md="!show ? 19 : 22" style="padding-right: 0">
              <a-form-model-item label="服务满意度" prop="serviceAttitude">
                <a-select
                  placeholder="请选择服务满意度"
                  v-model="model.serviceAttitude"
                 :getPopupContainer="(node) => node.parentNode"
                  :disabled="show"
                >
                  <a-select-option
                    :value="item.value"
                    :key="'attitude_' + index"
                    v-for="(item, index) in attitudeList"
                    >{{ item.title }}</a-select-option
                  >
                </a-select>
              </a-form-model-item>
            </a-col>
            <!--            <a-col v-bind="colLayout">
              <a-form-model-item label="运维服务质量" prop="serviceQuality">
                <a-select placeholder="请选择运维服务质量" v-model="model.serviceQuality"
                          dropdownClassName="custom-select-dropdown" :disabled="show">
                  <a-select-option :value="item.value" :key="'quality_'+index" v-for="(item,index) in qualityList">{{item.title}}</a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>-->
            <a-col :sm="24" :md="!show ? 19 : 22" style="padding-right: 0">
              <a-form-model-item label="服务评价" prop="commentContent">
                <a-textarea
                  style="width: 100%"
                  v-model="model.commentContent"
                  :autoSize="{ minRows: 2, maxRows: 4 }"
                  :disabled="show"
                  :allow-clear="true"
                  autocomplete="off"
                  placeholder="请输入服务评价描述"
                />
              </a-form-model-item>
            </a-col>
            <a-col v-if="!show" :sm="22" :md="4">
              <common-phrases-list @choose="getCommnonPhrases" overlayClassName="och-notice-filter" :module="'evaluate'"></common-phrases-list>
            </a-col>
          </a-row>
        </a-form-model>
      </j-form-container>
    </a-spin>
  </yq-modal>
</template>

<script>
import { getAction, httpAction } from '@api/manage'
import { ajaxGetDictItems, getDictItemsFromCache } from '@/api/api'
import commonPhrasesList from '@views/flowable/CommonPhrases/modules/commonPhrasesList.vue'
export default {
  name: 'EvaluationModal',
  components: {
    commonPhrasesList,
  },
  data() {
    return {
      title: '',
      width: '700px',
      visible: false,
      disableSubmit: false,
      confirmLoading: false,
      show: true,
      colLayout: {
        lg: { span: 24 },
        md: { span: 24 },
      },
      labelCol: {
        sm: { span: 24 },
        md: { span: 6 },
        lg: { span: 6 },
      },
      wrapperCol: {
        sm: { span: 24 },
        md: { span: 18 },
        lg: { span: 18 },
      },
      model: {
        comment: '',
        associationId : '',
      },
      validatorRules: {
        serviceAttitude: [{ required: true, message: '请选择服务满意度！' }],
        serviceQuality: [{ required: true, message: '请选择服务质量！' }],
        commentContent: [{ required: true, min: 2, max: 300, message: '服务评价描述长度应在1-300之间！' }],
      },
      url: {
        processEvaluate: '/question/question/processEvaluate',
        queryByProInsId: '/question/question/queryByProInsId',
        queryByQuestionId: '/question/question/queryByQuestionId',
      },
      attitudeList: [],
      qualityList: [],
    }
  },
  created() {
    this.initDictData('service_evaluation', 'attitudeList')
    //this.initDictData('service_quality_level', 'qualityList')
  },
  methods: {
    add(id) {
      this.model = {
        comment: '',
      }
      this.model.proInsId = id
      this.show = false
      this.visible = true
    },
    edit(id) {
      getAction(this.url.queryByProInsId + '?id=' + id).then((res) => {
        if (res.success) {
          this.model = res.result
          this.visible = true
          this.show = true
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    addQuestion(id) {
      this.model = {
        comment: '',
      }
      this.model.associationId = id
      this.show = false
      this.visible = true
    },
    editQuestion(id) {
      getAction(this.url.queryByQuestionId + '?id=' + id).then((res) => {
        if (res.success) {
          this.model = res.result
          this.visible = true
          this.show = true
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    close() {
      this.visible = false
    },
    handleOk() {
      const that = this
      that.$refs.model.validate((err, value) => {
        if (err) {
          that.confirmLoading = true
          let formData = {
            ...that.model,
          }
          httpAction(that.url.processEvaluate, formData, 'put')
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
                that.close()
              } else {
                that.$message.warning(res.message)
                that.close()
              }
              that.confirmLoading = false
            })
            .catch((res) => {
              that.$message.warning(res.message)
              that.close()
              that.confirmLoading = false
            })
        }
      })
    },
    handleCancel() {
      this.close()
    },
    initDictData(dictCode, list) {
      //优先从缓存中读取字典配置
      if (getDictItemsFromCache(dictCode)) {
        this[list] = getDictItemsFromCache(dictCode)
        return
      }

      //根据字典Code, 初始化字典数组
      ajaxGetDictItems(dictCode, null).then((res) => {
        if (res.success) {
          this[list] = res.result
        }
      })
    },
    getCommnonPhrases(value) {
      this.$set(this.model, 'commentContent', value);
      this.$refs.model.validateField('commentContent');
    }
  },
}
</script>
<style scoped lang="less">
@import '~@assets/less/onclickStyle.less';
@import "~@assets/less/normalModal.less";
</style>
