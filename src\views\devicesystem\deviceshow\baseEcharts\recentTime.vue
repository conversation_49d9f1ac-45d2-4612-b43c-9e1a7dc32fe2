<template>
  <div class="time">
    <div
      v-for="(item,index) in typeList"
      :key="index"
      :class="[selectedIndex==index?'active':'','item']"
      @click="changeType(index)"
    >{{item}}</div>
  </div>
</template>

<script>
export default {
  data() {
    return {}
  },
  props: {
    selectedIndex: 0,
    typeList: {
      type: Array,
      default: function() {
        return ['近7天', '近30天']
      }
    }
  },
  methods: {
    changeType(index) {
      this.$emit('changeType', index)
    }
  }
}
</script>

<style scoped lang="less">
.time {
  display: flex;
  align-items: center;
  position: absolute;
  top: 25px;
  right: 36px;
  .item {
    color: #000;
    background: none;
    margin-left: 5px;
    padding: 0 7px;
    font-size: 9px;
    cursor: pointer;
    &.active {
      color: #fff;
      background: #73a0fa;
    }
  }
}
</style>