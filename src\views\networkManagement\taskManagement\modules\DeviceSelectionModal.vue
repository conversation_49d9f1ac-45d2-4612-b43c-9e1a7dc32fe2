<template>
  <j-modal :title='title' :width='width' :visible='visible' :centered='true' :confirmLoading='confirmLoading'
           switchFullscreen @ok='handleOk' :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }" @cancel='handleCancel'
           cancelText='关闭'>
    <div class='table-page-search-wrapper'>
      <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
        <a-row :gutter='24' ref='row'>
          <a-col :xs="24" :sm="24" :md="12" :lg="12">
            <a-form-item label="产品名称">
              <a-select v-model="queryParam.productId" placeholder="请选择产品名称"  :show-search='true'
                        :getPopupContainer='(node) => node.parentNode' option-filter-prop='label' :allow-clear='true'>
                <a-select-option v-for="item in productTreeData" :label='item.proName' :value='item.proId'
                                  :key="item.proId">
                  {{ item.proName }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="12" v-show='toggleSearchStatus'>
            <a-form-item label="所属网关">
              <a-select v-model="queryParam.gatewayCode" placeholder="请选择所属网关"  :show-search='true'
                        :getPopupContainer='(node) => node.parentNode' option-filter-prop='label' :allow-clear='true'>
                <a-select-option v-for="item in gatewayData" :label='item.name' :value='item.deviceCode'
                                  :key="item.deviceCode">
                  {{ item.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="12" v-show='toggleSearchStatus'>
            <a-form-item label="IP">
              <a-input v-model="queryParam.ip" :allowClear="true" autocomplete="off" placeholder="请输入IP" :maxLength="30"></a-input>
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="12">
            <a-button type='primary' class='btn-search btn-search-style' @click='searchQuery'>查询</a-button>
            <a-button class='btn-reset btn-reset-style' @click='searchReset'>重置</a-button>
            <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
              {{ toggleSearchStatus ? '收起' : '展开' }}
              <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
            </a>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <div>
      <a-table style='width: 100%' size='middle' bordered :row-key='(record,index)=>{return record.id}'
                :columns='columns' :dataSource='dataSource'
                :pagination='ipagination' :loading='loading'
                :rowSelection='{ selectedRowKeys: selectedRowKeys,onChange: onSelectChange }' @change='handleTableChange'>
      </a-table>
    </div>
  </j-modal>
</template>

<script>
import JDictSelectTag from '@/components/dict/JDictSelectTag'
import { YqFormSearchLocation} from '@/mixins/YqFormSearchLocation'
import {
  JeecgListMixin
} from '@/mixins/JeecgListMixin'
import {
  getAction,
  postAction
} from '@/api/manage'

export default {
  name: 'DeviceSelectionModal',
  components: {
    JDictSelectTag
  },
  mixins: [JeecgListMixin, YqFormSearchLocation],
  data() {
    return {
      title: '',
      width: 800,
      visible: false,
      confirmLoading: false,
      disableSubmit: false,
      spanValue: 12,
      formItemLayout: {
        labelCol: {style: 'width:80px'},
        wrapperCol: {
          style: 'width:calc(100% - 80px)'
        }
      },
      queryParam:{
        dictCode : 'Network',
        ip: null,
        productId: undefined,
        gatewayCode: undefined
      },
      url: {
        list: '/configureBack/task/getResourceList',
        productTreeList: '/net/device/productListByCategoryDict', // 查询网络类型的产品
        gatewayList: '/configureBack/task/getGatewayList', //  获取所属网关
        bindDevice: '/configureBack/task/bindDevice', // 绑定设备
      },
      columns: [{
          title: '产品名称',
          align: 'center',
          dataIndex: 'productName'
        },
          {
            title: '设备名称',
            align: 'center',
            dataIndex: 'name'
          },
          {
            title: '所属网关',
            align: 'center',
            dataIndex: 'gatewayName'
          },
          {
            title: 'IP',
            align: 'center',
            dataIndex: 'ip'
          }
        ],
      productTreeData: [], // 产品数据
      gatewayData: [], // 网关数据
      disableMixinCreated: true,

    }
  },
  methods: {
    searchReset() {
      this.queryParam = {
        dictCode: 'network'
      }
      this.loadData(1)
    },
    // 产品下拉框
    getProductTreeData() {
      getAction(this.url.productTreeList,{dictCode:this.queryParam.dictCode}).then((res) => {
        this.productTreeData = []
        if (res.success) {
          if (res.result.length > 0) {
            res.result.map((item) => {
              let param = {
                proId: item.id,
                proName: item.displayName,
              }
              this.productTreeData.push(param)
            })
          }
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    // 网关下拉框
    gatewayList(){
      getAction(this.url.gatewayList).then((res) => {
        if (res.success) {
          this.gatewayData = res.result
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    add() {
      this.edit({})
    },
    edit(record, idList) {
      if (idList && idList.length > 0) {
        this.selectedRowKeys = idList.split(',')
      }
      this.taskId = record.id
      this.visible = true
      this.loadData(1)
      this.getProductTreeData()
      this.gatewayList()
    },
    close() {
      this.visible = false
    },
    handleOk() {
      const that = this
        if (that.selectedRowKeys.length == 0) {
          that.$message.warning('请选择要添加的设备!')
          return
        }
        if (that.taskId == null) {
          that.$emit('ok', that.selectedRowKeys, that.selectionRows)
          that.close()
        } else {
          var ids = that.selectedRowKeys.join(',')
          let param = {
            id: this.taskId,
            deviceIds: ids
          }

          that.confirmLoading=true
          postAction(this.url.bindDevice, param).then((res) => {
            if (res.success) {
              that.$emit('loadTableData')
              that.close()
            } else {
              this.$message.warning(res.message)
            }
            that.confirmLoading=false
          }).catch((err)=>{
            this.$message.warning(err.message)
            that.confirmLoading=false
          })
        }
    },
    handleCancel() {
      this.close()
    }
  }
}
</script>

<style lang='less' scoped>
@import '~@assets/less/normalModal.less';
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
::v-deep .ant-table-wrapper .ant-spin-nested-loading {
  padding-right: 0px;
}
</style>