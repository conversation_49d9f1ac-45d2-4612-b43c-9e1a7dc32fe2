<template>
  <j-modal
    :title="title"
    :width="modalWidth"
    :visible="visible"
    :confirmLoading="confirmLoading"
    switchFullscreen
    @cancel="handleCancel"
    @ok="handleOk"
    okText=""
    cancelText="关闭"
    :centered="true"
    :destroyOnClose="true"
    >
      <a-form :form="form">
      <div v-if="model!=null">
        <a-row :gutter='24'>
          <a-col v-bind='formItemLayout'>
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="采购日期">
              <j-date
                placeholder="请选择采购日期" 
                style="width:100%" 
                :trigger-change="true" 
                :showTime="true" 
                dateFormat="YYYY-MM-DD HH:mm:ss" 
                v-decorator="['purchaseDate', formValidator.purchaseDate]">
              </j-date>
            </a-form-item>
          </a-col>
          <a-col v-bind='formItemLayout'>
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="采购人">
              <a-input
                :allowClear="true"
                autocomplete="off"
                placeholder="请输入采购人"
                v-decorator="['purchaseUser', formValidator.purchaseUser]" />
            </a-form-item>
          </a-col>
        </a-row>
        <!-- 下发 -->
        <a-row :gutter='24' v-show="model.downDate!=null">
          <a-col v-bind='formItemLayout'>
            <a-form-item
              label="下发时间"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <j-date 
                placeholder="请选择" 
                style="width:100%" 
                :trigger-change="true" 
                :showTime="true" 
                dateFormat="YYYY-MM-DD HH:mm:ss" 
                v-decorator="['downDate', formValidator.downDate]">
              </j-date>
            </a-form-item>
          </a-col>
          <a-col v-bind='formItemLayout'>
            <a-form-item
              label="下发到"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-input
                :allowClear="true"
                autocomplete="off"
                placeholder="请输入"
                v-decorator="['downPlace', formValidator.downPlace]"></a-input>
            </a-form-item>
          </a-col>
        </a-row>
        <!-- 已部署 -->
        <a-row :gutter='24' v-show="model.deployDate!=null">
          <a-col v-bind='formItemLayout'>
            <a-form-item
              label="部署日期"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <j-date
                placeholder="请选择" 
                style="width:100%" 
                :trigger-change="true" 
                :showTime="true" 
                dateFormat="YYYY-MM-DD HH:mm:ss" 
                v-decorator="['deployDate', formValidator.deployDate]">
              </j-date>
            </a-form-item>
          </a-col>
          <a-col v-bind='formItemLayout'>
            <a-form-item
              label="单位"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-input
                :allowClear="true"
                autocomplete="off"
                placeholder="请输入"
                v-decorator="['deployCompany', formValidator.deployCompany]"></a-input>
            </a-form-item>
          </a-col>
        </a-row>
        <!-- 已上线 -->
        <a-row :gutter='24' v-show="model.onlineDate!=null">
          <a-col v-bind='formItemLayout'>
            <a-form-item
              label="上线日期"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <j-date
                placeholder="请选择" 
                style="width:100%" 
                :trigger-change="true" 
                :showTime="true" 
                dateFormat="YYYY-MM-DD HH:mm:ss" 
                v-decorator="['onlineDate', formValidator.onlineDate]">
              </j-date>
            </a-form-item>
          </a-col>
          <a-col v-bind='formItemLayout'>
            <a-form-item
              label="使用人"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-input
                placeholder="请输入"
                :allowClear="true"
                autocomplete="off"
                v-decorator="['userid', formValidator.userid]"></a-input>
            </a-form-item>
          </a-col>
        </a-row>
        <!-- 已暂存 -->
        <a-row :gutter='24' v-show="model.leaveDate!=null">
          <a-col v-bind='formItemLayout'>
            <a-form-item
              label="暂存日期"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <j-date
                placeholder="请选择" 
                style="width:100%" 
                :trigger-change="true" 
                :showTime="true" 
                dateFormat="YYYY-MM-DD HH:mm:ss" 
                v-decorator="['leaveDate', formValidator.leaveDate]">
              </j-date>
            </a-form-item>
          </a-col>
          <a-col v-bind='formItemLayout'>
            <a-form-item
              label="暂存到"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-input
                placeholder="请输入"
                :allowClear="true"
                autocomplete="off"
                v-decorator="['leavePlace', formValidator.leavePlace]"></a-input>
            </a-form-item>
          </a-col>
        </a-row>
        <!-- 已转移 -->
        <a-row :gutter='24' v-show="model.transferDate!=null">
          <a-col v-bind='formItemLayout'>
            <a-form-item
              label="转移日期"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <j-date
                placeholder="请选择" 
                style="width:100%" 
                :trigger-change="true" 
                :showTime="true" 
                dateFormat="YYYY-MM-DD HH:mm:ss" 
                v-decorator="['transferDate', formValidator.transferDate]">
              </j-date>
            </a-form-item>
          </a-col>
          <a-col v-bind='formItemLayout'>
            <a-form-item
              label="转移扶贫到"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-input
                placeholder="请输入"
                :allowClear="true"
                autocomplete="off"
                v-decorator="['transferPlace', formValidator.transferPlace]"></a-input>
            </a-form-item>
          </a-col>
        </a-row>
        <!-- 已销毁 -->
        <a-row :gutter='24' v-show="model.destructionDate!=null">
          <a-col v-bind='formItemLayout'>
            <a-form-item
              label="销毁日期"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <j-date
                placeholder="请选择" 
                style="width:100%" 
                :trigger-change="true" 
                :showTime="true" 
                dateFormat="YYYY-MM-DD HH:mm:ss" 
                v-decorator="['destructionDate', formValidator.destructionDate]">
              </j-date>
            </a-form-item>
          </a-col>
          <a-col v-bind='formItemLayout'>
            <a-form-item
              label="销毁备注"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol">
              <a-input
                placeholder="请输入"
                :allowClear="true"
                autocomplete="off"
                v-decorator="['destructionRemarks', formValidator.destructionRemarks]"></a-input>
            </a-form-item>
          </a-col>
        </a-row>
      </div>
    </a-form>
  </j-modal>
</template>
<script>
  import pick from 'lodash.pick'
  import JDate from '@/components/jeecg/JDate'
  import { ajaxGetDictItems } from '@api/api'
  import { queryById,queryChangeLedgerInfo,ledgerChangeEdit } from '@api/AssetsManagement'
export default {
  name: 'ChangeInfoEditModal',
  components: {
    JDate
  },
  props: ['queryStatus'],
  data() {
    return {
      title: '操作',
      //资产状态
      QueryStatus: [],
      visible: false,
      confirmLoading: false,
      /* 弹框宽 */
      modalWidth: '800px',
      form: this.$form.createForm(this),
      model: null,
      id: "", //记录id
      formItemLayout:{
        md:{span:12},
        sm:{span:24}
      },
      labelCol: {
        //style: 'width:100px',
        xs: { span: 24 },
        sm: { span: 7 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      showHeader: false,
      columns: [
        { title: 'Name', dataIndex: 'name', key: 'name', width: 100 },
        { title: 'actionPeople', dataIndex: 'actionPeople', key: 'actionPeople', width: 150 },
        { title: 'status', dataIndex: 'status', key: 'status' },
        { title: 'time', dataIndex: 'time', key: 'time' }
      ],
      formValidator:{
        purchaseUser: {
        	rules: [{
        			required: true,
        			message: '必填!'
        		}
        	]
        },
        purchaseDate: {
        	rules: [{
        			required: true,
        			message: '必选!'
        		}
        	]
        },
        downPlace: {
        	rules: [{
        			required: true,
        			message: '必填!'
        		}
        	]
        },
        downDate: {
        	rules: [{
        			required: true,
        			message: '必选!'
        		}
        	]
        },
        deployCompany: {
        	rules: [{
        			required: true,
        			message: '必填!'
        		}
        	]
        },
        deployDate: {
        	rules: [{
        			required: true,
        			message: '必选!'
        		}
        	]
        },
        userid: {
        	rules: [{
        			required: true,
        			message: '必填!'
        		},
        		{
        			min: 2,
        			max: 30,
        			message: '长度在 2 到 30 个字符',
        			trigger: 'blur'
        		}
        	]
        },
        onlineDate: {
        	rules: [{
        			required: true,
        			message: '必选!'
        		}
        	]
        },
        leavePlace: {
        	rules: [{
        			required: true,
        			message: '必填!'
        		},
        		{
        			min: 2,
        			max: 30,
        			message: '长度在 2 到 30 个字符',
        			trigger: 'blur'
        		}
        	]
        },
        leaveDate: {
        	rules: [{
        			required: true,
        			message: '必选!'
        		}
        	]
        },
        transferPlace: {
        	rules: [{
        			required: true,
        			message: '必填!'
        		},
        		{
        			min: 2,
        			max: 30,
        			message: '长度在 2 到 30 个字符',
        			trigger: 'blur'
        		}
        	]
        },
        transferDate: {
        	rules: [{
        			required: true,
        			message: '必选!'
        		}
        	]
        },
        destructionRemarks: {
        	rules: [{
        			required: true,
        			message: '必填!'
        		},
        		{
        			min: 2,
        			max: 30,
        			message: '长度在 2 到 30 个字符',
        			trigger: 'blur'
        		}
        	]
        },
        destructionDate: {
        	rules: [{
        			required: true,
        			message: '必选!'
        		}
        	]
        },
      }
    }
  },
  mounted() {
    
  },
  methods: {
    //获取信息
    loadData(id){
      this.id = id
      this.getBaseInfo()
    },

    //获取基本信息
    getBaseInfo(){
      queryById({id: this.id}).then(res => {
        if(res.success){
          this.formBaseData = res.result
          this.getChangeLedgerInfo(this.formBaseData.assetNo)
        }else{
          this.$message.error("获取基本信息失败")
        }
      })
    },
    //获取变更信息
    getChangeLedgerInfo(assetNo){
      queryChangeLedgerInfo({assetNo:assetNo}).then(res => {
        if(res.success){
          this.edit(res.result)
        }else{
          this.$message.error("获取变更记录失败")
        }
      })
    },
    
    edit(record) {
      this.form.resetFields();
      this.model = Object.assign({}, record)
      this.changeRules(this.model)
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(pick(this.model,'purchaseDate','purchaseUser','downDate','downPlace','deployDate','deployCompany',
        'onlineDate','userid','leaveDate','leavePlace','transferDate','transferPlace','destructionDate','destructionRemarks'))
      });
    },
    //将隐藏的表单项的必选取消
    changeRules(model){
      if(model.downDate==null){
        this.formValidator.downDate.rules[0].required = false
        this.formValidator.downPlace.rules[0].required = false
      }
      if(model.deployDate==null){
        this.formValidator.deployDate.rules[0].required = false
        this.formValidator.deployCompany.rules[0].required = false
      }
      if(model.onlineDate==null){
        this.formValidator.onlineDate.rules[0].required = false
        this.formValidator.userid.rules[0].required = false
      }
      if(model.leaveDate==null){
        this.formValidator.leaveDate.rules[0].required = false
        this.formValidator.leavePlace.rules[0].required = false
      }
      if(model.transferDate==null){
        this.formValidator.transferDate.rules[0].required = false
        this.formValidator.transferPlace.rules[0].required = false
      }
      if(model.destructionDate==null){
        this.formValidator.destructionDate.rules[0].required = false
        this.formValidator.destructionRemarks.rules[0].required = false
      }
    },
    
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let formData = Object.assign(this.model, values)
          ledgerChangeEdit(formData).then((res)=>{
            if(res.success){
              that.$message.success(res.message);
            }else{
              that.$message.warning(res.message);
            }
          }).finally(() => {
            that.confirmLoading = false;
            that.close();
          })
        }
      })
    },
    handleCancel() {
      this.close()
    },
    callback(key) {
    },
    //单个日期框
    onChange(date, dateString) {
    },
    
    //字典获取选择框内容--------------------------------------------
    getDicData() {
      this.getQueryStatus('asset_queryStatus')
    },
    getQueryStatus(code) {
      let that = this
      ajaxGetDictItems(code, null).then(res => {
        if(res.success){
          that.QueryStatus = res.result
        }else{
          that.$message.error("资产状态字典信息获取失败")
        }
      })
    },
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/normalModal.less';
</style>
