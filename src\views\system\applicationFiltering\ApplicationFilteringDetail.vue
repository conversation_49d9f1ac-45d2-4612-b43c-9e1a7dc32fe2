<template>
  <div class="page-box">
    <a-spin :spinning="loading">
      <div class='header-info'>
<!--        <div class="action-box">
          <a-button class="action" type="link" icon="reload" :loading="loading" @click="queryInfo(info)" >编辑</a-button>
        </div>-->
        <div class="header-back">
          <img src='~@assets/return1.png' alt='' @click='getGo'/>
        </div>
      </div>
      <div class="task-info" >
        <div class="content-box">
          <div  class="colorBox1">
            <span class="colorTotal">基本信息</span>
          </div>
           <a-descriptions  :column='{ xxl: 2, xl: 2, lg: 2, md: 2, sm: 2, xs: 1 }' bordered>
            <a-descriptions-item v-for='(item,index) in desItems' :key="'desItems_'+index" :label='item.label'>
             {{item.value}}
            </a-descriptions-item>
          </a-descriptions>
        </div>
      </div>
    </a-spin>
  </div>
</template>
<script>

import { getAction } from '@api/manage'

export default {
  name: 'TerminalInfo',
  props: {
    data: {
      type: Object,
      required: false,
      default: () => {
        return {}
      }
    }
  },
  components: {},
  data() {
    return {
      terminalList:[],
      desItems:[],
      loading:false,
      info: {},
      basicFields: [
        {
          field: 'strategyName',
          label: '策略名称'
        },
        {
          field: 'remark',
          label: '策略描述'
        },
        {
          field: 'scopeType',
          label: '配置范围',
          dictText: [
            {
              key: 0,
              value: '全部'
            },
            {
              key: 1,
              value: '批量'
            }]
        },
        {
          field: 'status',
          label: '展示方式',
          dictText: [
            {
              key: 0,
              value: '展示应用'
            },
            {
              key: 1,
              value: '过滤应用'
            }]
        },
        {
          field: 'strategyStatus',
          label: '策略状态',
          dictText: [
            {
              key: 0,
              value: '启用'
            },
            {
              key: 1,
              value: '禁用'
            }]
        },
        {
          field: 'osType_dictText',
          label: '操作系统类型'
        },
        {
          field: 'deptId_dictText',
          label: '部门'
        },
        {
          field: 'terminalName',
          label: '终端名称'
        },
        {
          field: 'applicationName',
          label: '应用名称'
        }
      ],
      url: {
        queryById: '/software/softwareTask/queryById',
        queryInfoName: '/terminal/application/queryInfoName',
      }
    }
  },
  watch: {
    data: {
      async handler(val) {
        this.info=val
        this.loading=true
        await this.getTerminals()
        let m=this.terminalList.filter((item)=>{
          if (val.scopeType==1&&val['uniqueCode']&&val['uniqueCode'].includes(item.uniqueCode)){
            return true
          }
          else if(val.scopeType!=1){
            return true
          }
          return false
        })
        if (m&&m.length>0){
          let str=''
          for (let i=0;i<m.length;i++) {
            str += i < m.length - 1 ? m[i].name + ',' : m[i].name
          }
          this.info['terminalName']=str
        }
        this.desItems=this.getDescriptionsItem(this.basicFields)
        this.loading=false
      },
      deep: true,
      immediate: true
    },
  },
  created() {

  },
  methods: {
    //返回上一级
    getGo() {
      this.$parent.pButton2(0)
    },
    /**
     * 整理、基本信息数据，以排除空数据     *
     * @param {Array} fields - 数组：元素包含字段英文名和中文名
     * */
    getDescriptionsItem(fields) {
      let data = []
      let obj = this.info
      for (let i = 0; i < fields.length; i++) {
        let field=fields[i].field
        let va=obj[field]+''
        if (va != '' && va !='null') {
          let item={ label: fields[i].label}
          if (fields[i].dictText){
            let key=obj[fields[i].field]
            let m=fields[i].dictText.filter((it)=>{
              return it.key==key
            })
            item['value']=m[0].value
          }else {
            item['value']=obj[fields[i].field]
          }
          data.push(item)
        }
      }
      return data
    },
    async getTerminals() {
      let { osType, deptId } = this.info
      let param = {
        osType,
        deptId
      }
     await getAction(this.url.queryInfoName, param).then((res) => {
        if (res.success && res.result) {
          this.terminalList = res.result
        }else{
          this.terminalList=[]
        }
      })
    },
  }
}
</script>

<style scoped lang='less'>
.page-box {
  border-radius: 3px;
  background: #fff;
  padding: 24px;
  height: 100% !important;
  width:100%;
  display: flex;
  flex-flow: column nowrap;
  overflow: hidden;

  ::v-deep .ant-spin-nested-loading{
    height: 100%;
    .ant-spin-container{
      height: 100%;
    }
  }
}

.header-info {
  display: flex;
  justify-content: end;
  align-items: start;
  margin-bottom: 16px;

/*  .action-box{
    margin-right: 16px;

    .action,.action:hover,.action:focus{
      color: #4b90de;
      font-size: 14px;
      cursor: pointer;
      margin-right: 12px;
      box-shadow: none !important;
      border: none !important;
      background: transparent !important;
    }
  }*/
  .header-back {
    img{
      width: 20px;
      height: 20px;
      cursor: pointer
    }
  }
}

.task-info {
  //flex: 1;
  height: calc(100% - 37px);

  .content-box {
    height:100%;
    padding-right:1px;
    overflow: hidden !important;
    overflow-y: auto !important;

    .colorBox1 {
      margin-bottom: 10px;

      .colorTotal {
        padding-left: 7px;
        font-size: 14px;
        border-left: 4px solid #1e3674;
        margin-right: 12px;
      }
    }
  }
}
::v-deep .ant-descriptions-bordered .ant-descriptions-item-label {
  width: 12%;
  color: rgba(0,0,0,0.65);
  background-color: #fafafa;;
  text-align: center;
}

::v-deep .ant-descriptions-bordered .ant-descriptions-item-content {
  color: rgba(0,0,0,0.85) !important;
  word-break: break-word;
  white-space: normal;
  width: 38%;
}
</style>
