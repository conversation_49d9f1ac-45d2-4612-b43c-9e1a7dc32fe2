<template>
  <div style="height:100%">
    <keep-alive>
      <component style="height:100%" :is="pageName" :data="data" />
    </keep-alive>
  </div>
</template>
<script>
  import peopleList from './peopleList'
  import peopleDetail from './modules/peopleDetail'
  export default {
    name: "peopleMange",
    data() {
      return {
        isActive: 0,
        data: {},
      };
    },
    components: {
      peopleList,
      peopleDetail
    },
    created() {
      this.pButton1(0);
    },
    //使用计算属性
    computed: {
      pageName() {
        switch (this.isActive) {
          case 0:
            return "peopleList";
          default:
            return "peopleDetail";
        }
      }
    },
    methods: {
      pButton1(index) {
        this.isActive = index;
      },
      async pButton2(index, item) {
        this.isActive = index;
        this.data = item
      }
    }
  }
</script>