<template>
  <div style="height: 100%;width: 100%;">
    <div style="margin-left: 0.5rem;font-size: 0.3rem;letter-spacing: 5px;color: rgba(250,250,250,.8);">{{ unit }}
    </div>
    <div
      :style="{'color':color,'font-size':'0.5rem','letter-spacing': '5px','font-weight':'600','margin-left':'0.5rem'}">
      {{value}}</div>
  </div>
</template>

<script>
  export default {
    props: {
      color: {
        type: String,
        default: '',
      },
      value: {
        type: Number,
        default: 0,
      },
      unit: {
        type: String,
        default: '',
      },
    },
    data() {
      return {

      };
    },
    methods: {

    }
  }
</script>

<style scoped lang="less">

</style>