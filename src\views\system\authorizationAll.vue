<template>
  <div style="height:100%">
    <component :is="pageName" :data="data" />
  </div>
</template>
<script>
  import AuthorizationList from './modules/AuthorizationList'
  import AuthorizationForm from './modules/AuthorizationForm'
  export default {
    name: 'AuthorizationAll',
    data() {
      return {
        isActive: 0,
        data: {}
      }
    },
    components: {
      AuthorizationList,
      AuthorizationForm
    },
    created() {
      this.pButton1(0)
    },
    //使用计算属性
    computed: {
      pageName() {
        switch (this.isActive) {
          case 0:
            return 'AuthorizationList'
            break

          default:
            return 'AuthorizationForm'
            break
        }
      }
    },
    methods: {
      pButton1(index) {
        this.isActive = index
      },
      pButton2(index, item) {
        this.isActive = index
        this.data = item
      }
    }
  }
</script>