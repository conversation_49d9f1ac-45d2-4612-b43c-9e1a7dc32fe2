<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    :confirmLoading="confirmLoading"
    switchFullscreen
    @ok="handleOk"
    @cancel="handleCancel"
    :destroyOnClose="true"
    cancelText="关闭"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item class="two-words" label="名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['name', validatorRules.name]" placeholder="请输入名称"></a-input>
        </a-form-item>

        <a-form-item class="two-words" label="行政区划" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <yq-area-cascader-select
            placeholder="请选择归属地"
            v-decorator="['cityId', validatorRules.cityId]"
          ></yq-area-cascader-select>
        </a-form-item>
        <a-form-item class="two-words" label="地址" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['address', validatorRules.address]" placeholder="请输入地址"></a-input>
        </a-form-item>
        <a-form-item label="联系电话" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['phone', validatorRules.phone]" placeholder="请输入联系电话"></a-input>
        </a-form-item>
        <a-form-item label="联系人" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['contacts']" placeholder="请输入联系人"></a-input>
        </a-form-item>
        <a-form-item class="two-words" label="标识" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['code', validatorRules.code]" placeholder="请输入标识"></a-input>
        </a-form-item>
        <a-form-item label="经度" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['longitude', validatorRules.longitude]" placeholder="请输入经度"></a-input>
        </a-form-item>
        <a-form-item label="纬度" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['latitude', validatorRules.latitude]" placeholder="请输入纬度"></a-input>
        </a-form-item>

        <a-form-item label="计划数量" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['planNumber']" placeholder="请输入单位计划数量"></a-input>
        </a-form-item>

        <a-form-item label="父级节点" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <j-tree-select
            ref="treeSelect"
            placeholder="请选择父级节点"
            v-decorator="['pid', validatorRules.pid]"
            dict="momg_dept,name,id"
            pidField="pid"
            pidValue="0"
            hasChildField="has_child"
            :disabled="true"
          >
          </j-tree-select>
        </a-form-item>
      </a-form>
    </a-spin>
  </j-modal>
</template>

<script>
import { httpAction } from '@/api/manage'
import pick from 'lodash.pick'
import { validateDuplicateValue, checkPhone } from '@/utils/util'
import JTreeSelect from '@/components/jeecg/JTreeSelect'
import YqAreaCascaderSelect from '@/components/areaDict/YqAreaCascaderSelect'
export default {
  name: 'MomgDeptModal',
  components: {
    JTreeSelect,
    YqAreaCascaderSelect,
  },
  data() {
    return {
      form: this.$form.createForm(this),
      title: '新增',
      width: 800,
      visible: false,
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      confirmLoading: false,
      validatorRules: {
        name: {
          rules: [{ required: true, message: '请输入单位名称!' }],
        },
        code: {
          rules: [{ required: true, message: '请输入单位标识!' }],
        },
        address: {
          rules: [{ required: true, message: '请输入单位地址!' }],
        },
        phone: {
          rules: [
            {
              required: true,
              pattern: /^1[34578][0-9]{9}$|^(0[0-9]{2,3}\-)?([2-9][0-9]{6,7})+(\-[0-9]{1,4})?$/,
              message: '请输入联系电话!',
            },
          ],
        },
        cityId: {
          rules: [{ required: true, message: '请选择行政区划' }],
        },
        planNumber: {
          rules: [{ required: true, message: '请输入计划数量' }],
        },
        pid: {
          rules: [{ required: true }],
        },
        longitude: {
          rules: [
            {
              pattern: /^[\-\+]?(0?\d{1,2}\.\d{1,5}|1[0-7]?\d{1}\.\d{1,5}|180\.0{1,5})$/,
              message: '请输入正确的经度(精度1~5位)',
            },
          ],
        },
        latitude: {
          rules: [
            {
              pattern: /^[\-\+]?([0-8]?\d{1}\.\d{1,5}|90\.0{1,5})$/,
              message: '请输入正确的维度(精度1~5位)',
            },
          ],
        },
      },

      url: {
        add: '/device/momgDept/add',
        edit: '/device/momgDept/edit',
      },
      expandedRowKeys: [],
      pidField: 'pid',
      // cityId: '',
    }
  },
  created() {},
  methods: {
    add(obj) {
      if (typeof obj == 'undefined') {
        obj = { pid: '0000000001' }
      }
      this.edit(obj)
    },
    edit(record) {
      this.form.resetFields()
      this.model = Object.assign({}, record)
      // this.cityId = (this.model.cityId || '').toString()
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(
          pick(
            this.model,
            'name',
            'code',
            'address',
            'phone',
            'contacts',
            'pid',
            'cityId',
            'longitude',
            'latitude',
            'planNumber'
          )
        )
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let formData = Object.assign(this.model, values)
          // formData['cityId'] = this.cityId
          if (formData.pid == '' || formData.pid == undefined) {
            formData.pid = '0'
          }

          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                this.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
              that.close()
            })
        }
      })
    },
    handleCancel() {
      this.close()
    },
    popupCallback(row) {
      this.form.setFieldsValue(pick(row, 'name', 'code', 'address', 'phone', 'contacts', 'pid'))
    },
    submitSuccess(formData, flag) {
      if (!formData.id) {
        let treeData = this.$refs.treeSelect.getCurrTreeData()
        this.expandedRowKeys = []
        this.getExpandKeysByPid(formData[this.pidField], treeData, treeData)
        this.$emit('ok', formData, this.expandedRowKeys.reverse())
      } else {
        this.$emit('ok', formData, flag)
      }
    },
    getExpandKeysByPid(pid, arr, all) {
      if (pid && arr && arr.length > 0) {
        for (let i = 0; i < arr.length; i++) {
          if (arr[i].key == pid) {
            this.expandedRowKeys.push(arr[i].key)
            this.getExpandKeysByPid(arr[i]['parentId'], all, all)
          } else {
            this.getExpandKeysByPid(pid, arr[i].children, all)
          }
        }
      }
    },
  },
}
</script>
<style lang="less" scoped>
::v-deep .two-words > div > label {
  letter-spacing: 4px;
}
::v-deep .two-words > div > label::after {
  letter-spacing: 0px;
}
</style>