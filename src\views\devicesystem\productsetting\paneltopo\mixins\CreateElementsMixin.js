// 拓扑自定义节点注册 在../graph/shape.js 中
export default {
    data() {
        return {
            // 能创建节点的类型
            nodeTypes: {
                panelElement: 'createElementNode',
                panelNode: 'createPanelNode',
                text: "createTextNode",
                group: "createGroupNode",
            },
            panelTexts:{
                0:"正面板",
                1:"反面板",
            }
        }
    },
    methods: {
        /**
         * 批量生成接口节点
         * extendConfig 接口排列配置
         */
        batchCreateInterface(extendConfig) {
            let batchId = "batchNode_" + Date.now()
            let parentNode = this.templateNode.getParent()
            let { row, column, interfacePos, interfaceOrder, startIndex, bandwidth, direction,eleSize } = extendConfig;
            this.templateNode.setData({ bandwidth: bandwidth});
            this.templateNode.resize(eleSize, eleSize)
            this.templateNode.attr('foreignObject/fontSize', eleSize)
            let nodeNums = row * column;
            let startNum = this.getStartNodeIndex(startIndex, nodeNums, interfaceOrder, interfacePos, row, column);
            this.templateNode.attr('label/text', startNum)
            if(nodeNums <=1)return;
            this.templateNode.setData({ batchId: batchId,batchPos:{x:0,y:0,row:row,isFirst:true} });
            let size = this.templateNode.size();
            let pos = this.templateNode.position();
            // console.log("批量创建的配置 == ", extendConfig, "batchNode_" + Date.now())
            let nodeData = this.templateNode.data;
            if (row === 2) {
                this.setLabelPos(this.templateNode)
            }
            this.setDirection(this.templateNode, direction, row, 0);
          
            for (let i = 1; i < nodeNums; i++) {
                let x = i % column;
                let y = Math.floor(i / column);
                nodeData.typeName = this.getExtendNodeIndex(x, y, startNum, interfaceOrder, interfacePos, row, column);
                let node = this.createElementNode(nodeData);

                //当接口只有两行时 第一行字体默认在上面
                if (row === 2) {
                    if (y === 0) {
                        this.setLabelPos(node)
                    }
                    node.position(pos.x + (x * size.width), pos.y + (y * size.height))
                }
                else {
                    node.position(pos.x + (x * size.width), pos.y + (y * size.height) + (y * 10))
                }
                node.resize(eleSize, eleSize);
                node.attr('foreignObject/fontSize', eleSize)
                node.setData({ batchId: batchId,batchPos:{x:x,y:y,isFirst:false,row:row} });
                this.setDirection(node, direction, row, y);
                if (parentNode) {
                    parentNode.embed(node)
                }
                this.graph.addNode(node);
            }


        },
        //批量接口第一接口的序号
        getStartNodeIndex(index, sum, order, pos, row, column) {
            if (pos === "upper") {
                if (order === "123") {
                    return index
                }
                else if (order === "321") {
                    return index + column - 1;
                } else if (order === "135") {
                    return index
                } else if (order === "531") {
                    return sum - row + 1;
                }
            }
            else if (pos === "below") {
                if (order === "123") {
                    return sum - column + 1;
                }
                else if (order === "321") {
                    return index + sum - 1
                    //到这了
                } else if (order === "135") {
                    return index + row - 1;
                } else if (order === "531") {
                    return sum + index - 1;
                }
            }
        },
        //批量接口 扩展接口的序号
        getExtendNodeIndex(x, y, startNum, order, pos, row, column) {
            if (pos === "upper") {
                if (order === "123") {
                    let rowStart = startNum + (column * y)
                    return rowStart + x
                }
                else if (order === "321") {
                    let rowStart = startNum + (column * y)
                    return rowStart - x
                } else if (order === "135") {
                    let rowStart = startNum + y
                    return rowStart + row * x
                } else if (order === "531") {
                    let rowStart = startNum + y
                    return rowStart - row * x
                }
            } else {
                if (order === "123") {
                    let rowStart = startNum - (column * y)
                    return rowStart + x
                } else if (order === "321") {
                    let rowStart = startNum - (column * y)
                    return rowStart - x
                } else if (order === "135") {
                    let rowStart = startNum - y
                    return rowStart + row * x
                } else if (order === "531") {
                    let rowStart = startNum - y
                    return rowStart - row * x
                }

            }
        },
        //设置接口label位置
        setLabelPos(node) {
            let labelPos = { refx: 0.5, refy: 0, anchor: 'middle', vAnchor: 'bottom' }
            node.attr('label/refX', labelPos.refx)
            node.attr('label/refY', labelPos.refy)
            node.attr('label/textAnchor', labelPos.anchor)
            node.attr('label/textVerticalAnchor', labelPos.vAnchor)
        },
        //设置接口方向
        setDirection(node, dir, row, y) {
            switch (dir) {
                case "down":
                    node.setData({ angle: 180 })
                    break;
                case "up_down":
                    if (y === 1) {
                        node.setData({ angle: 180 })
                    }
                    break;
                case "down_up":
                    if (y === 0) {
                        node.setData({ angle: 180 })
                    }
                    break;
            }
        },
        createTopoNode(type, data) {
            return this[this.nodeTypes[type]](data);
        },
        // 创建元素节点
        createElementNode(data) {
            return this.graph.createNode({
                shape: "panel-element",
                visible: true,
                zIndex: 2,
                attrs: {
                    label: {
                        text: data.typeName,
                    },
                },
                data: {
                    typeIcon: data.typeIcon,
                    nodeType: "panelElement",
                    nodeSize: "",
                    typeCode: data.typeCode,
                    iconColor: "#1677FF",
                    portIndex: "",//绑定端口
                    portDesc:"",//物理端口
                    virtualPorts:[],
                    bandwidth: '',
                    angle: 0,
                    batchId: "",//批量添加的编号 同批编号相同
                    batchPos:{x:0,y:0,isFirst:true,row:0},
                },
            })
        },
        //创建面板节点
        createPanelNode() {
            let panelText = this.panelTypes.find(el=>el.key === this.value)
            return this.graph.createNode({
                shape: 'panel-node',
                visible: true,
                zIndex: 1,
                attrs: {
                    // label: {
                    //     text:panelText?panelText.label:"",
                    // },
                },
                data: {
                    parent: true,
                    nodeType: "panelNode",
                    bgType: "color",
                    bgColor: "#E7EFF5",
                    bgImage: "",
                },
            })
        },
        // 创建文字节点
        createTextNode() {
            return this.graph.createNode({
                shape: 'text-block',
                x: 160,
                y: 120,
                width: 128,
                height: 36,
                breakWord: true,
                visible: true,
                attrs: {
                    body: {
                        fill: 'none',
                        stroke: '#8c8c8c',
                        rx: 4,
                        ry: 4,
                    },
                    label: {
                        text: "text",
                        style: {
                            fontSize: 13,
                            color: this.globalGridAttr.topoConfig.labelColor,
                        }
                    },
                },
                data: {
                    nodeType: 'text',
                },
            })
        },
        // 创建容器节点
        createGroupNode() {
            return this.graph.createNode({
                shape: 'networkGroupNode',
                visible: true,
                attrs: {
                    label: {
                        text: "群组节点",
                        fontSize: 13,
                        fill: this.globalGridAttr.topoConfig.labelColor,
                    },
                },
                data: {
                    parent: true,
                    nodeType: "group",
                },
            })
        },
    }
}