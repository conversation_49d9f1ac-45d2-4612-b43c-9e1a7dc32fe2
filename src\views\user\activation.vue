<template>
  <div class="activation">
    <div class="header">
      <img v-show="logoShow"
        :src="logoUrl"
        alt=""
      />
      <div>
        <span>{{ title }}</span>
        <!-- <span>{{ enTitle }}</span> -->
      </div>
    </div>
    <div class="activation-body">
      <div class="body-title">用户填写</div>
      <div class="body-form">
        <a-form
          :form="form"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 12 }"
          @submit="handleSubmit"
        >
          <a-form-item label="单位">
            <a-tree-select
              :getPopupContainer="(node) => node.parentNode"
              tree-node-filter-prop="title"
              v-decorator="['deptId', { rules: [{ required: true, message: '请选择单位' }] }]"
              :replaceFields="replaceFields"
              :treeData="selectOption"
              show-search
              style="width: 100%"
              :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
              placeholder="请选择您所在的单位"
              allow-clear
              @change="onChangeTree"
              @search="onSearch"
              @select="onSelect"
            >
            </a-tree-select>
          </a-form-item>
          <a-form-item label="使用人">
            <a-input
              placeholder="请输入使用人姓名"
              v-decorator="['username', { rules: [{ required: true, message: '请填写使用人姓名' }] }]"
            />
          </a-form-item>
          <a-form-item label="联系电话">
            <a-input
              placeholder="请输入您的联系电话"
              v-decorator="[
                'phone',
                {
                  rules: [
                    { required: true, message: '请填写联系电话!' },
                    {
                      pattern:
                      /(^(0[0-9]{2,3}\-)?([2-9][0-9]{6,7})+(\-[0-9]{1,4})?$)|(^(1[345789]\d{9})$)/,
                      message: '请填写正确的联系电话',
                    },
                  ],
                },
              ]"
            />
          </a-form-item>
          <a-form-item label="使用部门">
            <a-input
              placeholder="请输入使用部门"
              v-decorator="['useDept', { rules: [{ required: true, message: '请填写使用部门' }] }]"
            />
          </a-form-item>

          <a-form-item
            label="SN"
            v-if="snVisible"
          >
            <a-input
              placeholder="请输入sn"
              v-decorator="['sn', { rules: [{ required: true, message: '请填写 sn' }] }]"
            />
          </a-form-item>

          <!-- <a-form-item label="管理开关">
            <a-switch @change="onChange" />
          </a-form-item> -->

          <div v-if="dictOptions_required =='1'">
            <a-form-item label="管理人">
              <a-input
                placeholder="请输入管理人"
                v-decorator="['manager', { rules: [{ required: true, message: '请填写管理人' }] }]"
              />
            </a-form-item>
            <a-form-item label="管理部门">
              <a-input
                placeholder="请输入管理部门"
                v-decorator="['managerDept', { rules: [{ required: true, message: '请填写管理部门' }] }]"
              />
            </a-form-item>
            <a-form-item label="地址">
              <yq-area-cascader-select
                disabled
                placeholder="请选择归属地"
                v-decorator="['addrId', {initialValue: cityId, rules: [{ required: true, message: '请选择地区' }] }]"
              ></yq-area-cascader-select>
            </a-form-item>
            <a-form-item label="详细地址">
              <a-input
                placeholder="请输入具体详细地址"
                v-decorator="['addrDetail', { rules: [{ required: true, message: '请填写具体详细地址' }] }]"
              />
            </a-form-item>
          </div>
          <a-form-item label="备注">
            <a-textarea
              placeholder="请输入您的备注"
              v-decorator="['remarks', { rules: [{ required: false }] }]"
            />
          </a-form-item>

          <div class="form-bottom">
            <!-- ^((13[0-9])|(14[5,6,7,9])|(15[^4])|(16[5,6])|(17[0-9])|(18[0-9])|(19[1,8,9]))\\d{8}$ -->
            <!-- <button html-type="submit">
              <a> 提交 </a>
            </button>
            <button style="margin-left: 16px" @click="handleReset">重置</button> -->
            <a-button
              :loading="tLoading"
              type="primary"
              html-type="submit"
            > {{tLoading ? '':'提 交'}}</a-button>
            <a-button
              style="margin-left: 16px"
              @click="handleReset"
            >重 置</a-button>
          </div>
        </a-form>
      </div>
    </div>
  </div>
</template>
<script>
import { putAction, postAction, getAction } from '@/api/manage'
import YqAreaCascaderSelect from '@/components/areaDict/YqAreaCascaderSelect'
import { ajaxGetDictItem, getDictItemsFromCache } from '@/api/api'
import { resolve } from '@antv/x6/lib/registry/router/manhattan/options'

export default {
  data() {
    return {
      logoShow: window._CONFIG['logoShow'] === 1 ? true : false,
      tLoading: false,
      title: window._CONFIG['activationSysName'],
      enTitle: window._CONFIG['activationEnSysName'],
      // logoUrl: process.env.NODE_ENV === 'production' ? window._CONFIG['activationLogoUrl'] : require('@/assets/06.png'),
      logoUrl: process.env.NODE_ENV === 'production' ? window._CONFIG['activationLogoUrl'] : require('@/assets/logo-white2.png'),
      formLayout: 'horizontal',
      form: this.$form.createForm(this, { name: 'coordinated' }),
      selectOption: [],
      checked: false,
      dictOptions_type: [],
      dictOptions_schema: [],
      dictOptions_required: [],
      dictOptions_sn: 0,
      snVisible: false,
      schemaItem: '',
      typeItem: [],
      value: undefined,
      cityId: "",
      treeData: [],
      replaceFields: {
        children: 'children',
        title: 'deptName',
        key: 'deptId',
        value: 'deptId'
      },
    }
  },
  components: {
    YqAreaCascaderSelect,
  },
  watch: {
    dictCode: {
      immediate: true,
      handler() {
        this.getDictItem()
        this.actRequiredData()
      },
    }
  },
  props: {
    dictCode: String,
    placeholder: String,
    triggerChange: Boolean,
    disabled: Boolean,
    // value: [String, Number],
    type: String,
    getPopupContainer: {
      type: Function,
      default: (node) => node.parentNode
    }
  },
  mounted() {
    this.select()
  },
  created() {
  },
  methods: {
    //获取3个字典：act_sn、act_sn_type、act_sn_schema，之后再进行判断sn是否必输入
    getDictItem(){
      var p_sn = ajaxGetDictItem('act_sn', null).catch(()=>{return Promise.resolve("act_sn")})
      var p_type = ajaxGetDictItem('act_sn_type', null).catch(()=>{return Promise.resolve("act_sn_type")})
      var p_schema = ajaxGetDictItem('act_sn_schema', null).catch(()=>{return Promise.resolve("act_sn_schema")})
      Promise.all([p_sn,p_type,p_schema]).then(data=>{
        if(!!data[0].success){
          this.dictOptions_sn = data[0].result[0].value
        }
        if(!!data[1].success){
          this.dictOptions_type = data[1].result
          this.typeItem = this.dictOptions_type.map(x => { return x.value })
        }
        if(!!data[2].success){
          this.dictOptions_schema = data[2].result
          this.schemaItem = this.dictOptions_schema.map(x => { return x.value })
        }
        this.snVisibleClick()
      })
    },
    onChangeTree(value) {
      this.value = value;
      getAction('/sys/sysDepart/queryById',{id:value}).then((res) => {
        if (res.success) {
          this.cityId = res.result.cityId;
        }
      })
    },
    onSearch() {
    },
    onSelect() {
    },
    // sn必输入控制
    snVisibleClick() {
      // const url = this.$route.query
      const _sn = this.$route.query.sn
      const _framework = this.$route.query.framework
      const _cliettype = this.$route.query.clientype

      const _schema = this.schemaItem.includes(_framework)

      const _type = this.typeItem.includes(_cliettype)

      if (this.dictOptions_sn == 0) { //获取字典终端激活sn控制（act_sn） 为 0， 非必输入
        this.snVisible = false
      } else if(this.dictOptions_sn == 1) { ////获取字典终端激活sn控制（act_sn） 为 1
        if (!!_sn) { //URL参数sn的值 不为空：!=null && typeof()!=undefined && !=''，非必输入 已验证
          this.snVisible = false 
        } else { //URL参数sn的值 为空：==null || typeof()==undefined || ==''
          this.snVisible = false
          if (!!_framework && !!_cliettype) { //获取URL参数clienttype和framework的值，两者都存在 已验证
            if(this.schemaItem.length > 0 && this.typeItem.length > 0){ //字典act_sn_type、act_sn_schema都存在
              if (_schema && _type) { //clientype、framework同时匹配 已验证
                this.snVisible = true
              }
            } else {
              if(_schema || _type){ //在字典act_sn_type、act_sn_schema存在一个情形下，匹配一个，则必输入   已验证
                this.snVisible = true
              }
            }
          } else { //获取URL参数clienttype和framework的值，一个或者两个都不存在    已验证
            this.snVisible = true
          }
        }
      }
    },
    // act_required字典
    actRequiredData() {
      //优先从缓存中读取字典配置
      // if (getDictItemsFromCache(dictCode)) {
      //   this.dictOptions = getDictItemsFromCache(dictCode)
      //   return
      // }

      //根据字典Code, 初始化字典数组
      ajaxGetDictItem('act_required', null).then(res => {
        if (res.success) {
          this.dictOptions_required = res.result[0].value
        }
      })
    },
    //   自动获取地址栏的参数
    getQueryStringRegExp(name) {
      var reg = new RegExp('(^|\\?|&)' + name + '=([^&]*)(\\s|&|$)', 'i')
      if (reg.test(location.href)) {
        return unescape(RegExp.$2.replace(/\+/g, ' '))
      }

      return null
    },
    onChange(checked) {
      this.checked = checked;
    },

    //   提交按钮
    handleSubmit(e) {
      let data = {
        uniqueCode: this.getQueryStringRegExp('hostname'),
        sn: this.getQueryStringRegExp('sn'),
        cpuArch: this.getQueryStringRegExp('framework'),
        terminalType: this.getQueryStringRegExp('clientype'),
        deptId: '',
        username: '',
        phone: '',
        userDepartment: '',
        administrator: '',
        adminDepartment: '',
        addrId: '',
        remarks: '',

      }
      e.preventDefault()
      this.form.validateFields((err, values) => {
        if (!err) {
          data.deptId = values.deptId
          data.username = values.username
          data.phone = values.phone
          data.userDepartment = values.useDept
          data.administrator = values.manager
          data.adminDepartment = values.managerDept
          data.addrId = values.addrId
          data.addrDetail = values.addrDetail
          data.remarks = values.remarks
          if (data.sn == '') {
            data.sn = values.sn
          }
          this.tLoading = true
          putAction('terminal/terminalDevice/updateStatus', data).then((res) => {
            if (res.code == 200) {
              this.tLoading = false
              this.$message.success(res.message)
            } else {
              this.$message.error(res.message)
            }
          })
          this.form.resetFields()
        }
      })
    },
    // 重置按钮
    handleReset() {
      this.form.resetFields()
      this.tLoading = false
    },

    filterOption(input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
    },

    select() {
      getAction('/sys/sysDepart/queryAllTree').then((res) => {
        for (let i = 0; i < res.length; i++) {
          let temp = res[i];
          this.selectOption.push(temp);
        }

      })
    },


    // select() {
    //   getAction('device/momgDept/queryAllDepts').then((res) => {
    //     if (res.code == 200) {
    //       this.selectOption = res.result
    //     } else {
    //       this.$message.error(res.message)
    //     }
    //   })
    // },
  },
}
</script>
<style lang="less" scoped>
.activation {
  width: 100%;
  // height: 100%;
  background: #e4e4e4;
  padding-bottom: 30px;
  .header {
    width: 100%;
    height: 0.75rem /* 60/80 */;
    background: #fff;
    padding: 0 4.5rem /* 360/80 */;
    display: flex;
    align-items: center;

    img {
      width: 0.45rem /* 36/80 */;
      height: 0.45rem /* 36/80 */;
    }
    div {
      display: flex;
      flex-direction: column;
      padding-left: 0.2rem /* 16/80 */;
      span:nth-child(1) {
        font-size: 0.3rem /* 24/80 */;
        font-weight: 600;
        letter-spacing: 2px;
        color: #1e3676;
      }
      span:nth-child(2) {
        font-size: 0.15rem /* 12/80 */;
      }
    }
  }
  .activation-body {
    margin: 16px auto;
    width: 60%;
    // height: 80%;
    background: #fff;
    overflow-y: auto;
    .body-title {
      padding: 16px 0px 12px 16px;
      font-size: 0.2rem /* 16/80 */;
      color: #787878;
      width: 100%;
      border-bottom: 1px solid #e4e4e4;
    }
    .body-form {
      width: 60%;
      height: 60%;
      margin: 1rem /* 80/80 */ auto;
      //   .button {
      //     display: flex;
      //     // justify-content: center;
      //     flex-direction: row;
      //     button {
      //       width: 1.25rem /* 100/80 */;
      //       height: 0.5rem /* 40/80 */;
      //       border-radius: 0.1rem /* 8/80 */;
      //       display: flex;
      //       align-items: center;
      //       justify-content: center;
      //     }
      //     button:nth-child(1) {
      //       background: #1e3674;
      //       color: #fff;
      //       a {
      //         color: #fff;
      //       }
      //     }
      //   }
      .form-bottom {
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        // padding-left: 1.775rem /* 142/80 */;
        button {
          width: 1rem /* 80/80 */;
          height: 0.4375rem /* 35/80 */;
          border-radius: 0.1rem /* 8/80 */;
        }
      }
    }
  }
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  -webkit-transition-delay: 111111s;
  -webkit-transition: color 11111s ease-out, background-color 111111s ease-out;
}
</style>