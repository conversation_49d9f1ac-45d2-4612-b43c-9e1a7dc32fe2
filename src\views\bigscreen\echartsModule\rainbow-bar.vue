<template>
  <div :id="chartId" ref="chartDom" style="width: 100%; height: 100%"></div>
</template>

<script>
export default {
  props: ['chartId', 'processObj'],
  data() {
    return {}
  },
  created() {
  },
  mounted() {
  },
  watch: {
    processObj() {
      if (this.chartId) {
        this.drawChart()
      }
    },
  },
  methods: {
    drawChart() {
      var chartDom = document.getElementById(this.chartId)
      var myChart = this.$echarts.init(chartDom)
      let processObj = this.processObj
      let colorArr = [
        '#D93637',
        '#EB7A2E',
        '#FEB546',
        '#FFE143',
        '#BFFD44',
        '#44FF78',
        '#44FDF8',
        '#44BEFF',
        '#5988FD',
        '#8270D2',
      ]
      let series = []
      processObj.forEach((el, idx) => {
        series.push({
          name: el.name,
          type: 'bar',
          stack: 'total',
          label: {
            show: true,
          },
          emphasis: {
            focus: 'series',
          },
          animationDelay: function (idx) {
            return idx * 50 + 1000
          },
          itemStyle: {
            normal: {
              color: colorArr[idx],
            },
          },
          data: [el.count],
        })
      })
      var option = {
        tooltip: {
          trigger: 'axis',
          trigger: 'item',

          backgroundColor: 'rgba(9, 24, 48, 0.5)',
          borderColor: 'rgba(75, 253, 238, 0.4)',
          textStyle: {
            color: '#CFE3FC',
          },
          borderWidth: 1,
          appendToBody: true,
          formatter:(a,b)=>{
            return '名称：'+a.seriesName + "<br/> 进程数：" + a.value
            
          }
        },
        legend: {
          bottom: 8,
          left: 0,
          icon: 'circle',
          textStyle: {
            color: '#fff',
          },
          formatter: function (name) {
            let arr = name.split('.')
            return arr[arr.length - 1]
          },
          width: '60%',
        },
        grid: {
          left: 0,
          right: 0,

          // bottom: 0,
          top: 8,
          // containLabel: true,
        },
        xAxis: {
          type: 'value',
          show: false,
        },
        yAxis: {
          type: 'category',
          show: false,
          splitLine: 'none',
          axisTick: 'none',
          axisLine: 'none',
          data: ['server'],
        },
        series: series,
        animationEasing: 'elasticOut',
        animationDuration: 2000,
        // animationDelayUpdate: function (idx) {
        //   return idx * 50 + 1000;
        // }
        barWidth: 60,
      }

      option && myChart.setOption(option)
      myChart.resize()
    },
  },
}
</script>

<style lang="less" scoped>
</style>