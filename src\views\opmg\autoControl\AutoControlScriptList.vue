<template>
  <a-row :gutter='10' style='height: 100%' class='vScroll'>
    <a-col style='width: 100%; height: 100%; display: flex; flex-direction: column'>
      <!-- 查询区域 -->
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <div class='table-page-search-wrapper-style'>
          <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
            <a-row :gutter='24' ref='row'>
              <a-col :span='spanValue'>
                <a-form-item label='脚本名称'>
                  <a-input placeholder='请输入名称' v-model='queryParam.scriptName' :allowClear='true' autocomplete='off'
                    :maxLength="maxLength" />
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label='脚本类型'>
                  <j-dict-select-tag v-model='queryParam.scriptLanguage' placeholder='请选择脚本类型'
                    dictCode='script_language' />
                </a-form-item>
              </a-col>
              <a-col :span='colBtnsSpan()'>
                <span class='table-page-search-submitButtons'
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button type='primary' class='btn-search btn-search-style' @click='dosearch'>查询</a-button>
                  <a-button class='btn-reset btn-reset-style' @click='doreset'>重置</a-button>
                  <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <!-- 查询区域-END -->
      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <!-- 操作按钮区域 -->
        <div class='table-operator table-operator-style'>
          <a-button @click='handleAdd'>新增</a-button>
          <a-dropdown v-if='selectedRowKeys.length > 0'>
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item key='1' @click='batchDel'>删除</a-menu-item>
            </a-menu>
            <a-button> 批量操作
              <a-icon type='down' />
            </a-button>
          </a-dropdown>
        </div>
        <!-- table区域-begin -->
        <a-table ref='table' bordered rowKey='id' :columns='columns' :dataSource='dataSource'
          :scroll='dataSource.length>0?{x:"max-content"}:{}' :pagination='ipagination' :loading='loading'
          :rowSelection='{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }' @change='handleTableChange'>
          <span class='caozuo' slot='action' slot-scope='text, record'>
            <a @click='handleDetailPage(record)'>查看</a>
            <a @click='handleEdit(record)'>
              <a-divider type='vertical' />编辑</a>
            <a @click='deleteRecord(record)'>
              <a-divider type='vertical' />删除</a>
          </span>
          <template slot="scriptFile" slot-scope="text,record">
            <alarm-upload v-model="record.scriptFile" :file-list='fileList' :showUploadList='true' name="file"
              :multiple="false">
            </alarm-upload>
          </template>
          <template slot='scriptContext' slot-scope='text'>
            <a-tooltip placement='topLeft' :title='text' trigger='hover'>
              <div class='tooltip'>
                {{text != null && text != '' ? text.substring(0,20) : ''}}
              </div>
            </a-tooltip>
          </template>
        </a-table>
      </a-card>
      <!-- 表单区域 -->
      <auto-control-script-modal ref='modalForm' @ok='modalFormOk'></auto-control-script-modal>
    </a-col>
  </a-row>
</template>

<script>
  import Vue from 'vue'
  import '@/assets/less/TableExpand.less'
  import {
    mixinDevice
  } from '@/utils/mixin'
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import AutoControlScriptModal from './modules/AutoControlScriptModal'
  import JSuperQuery from '@/components/jeecg/JSuperQuery.vue'
  import alarmUpload from '@views/alarmManage/alarmLevel/alarmUpload'
  import {
    httpAction,
    getAction,
    deleteAction
  } from '@/api/manage'
  import JDictSelectTag from '@/components/dict/JDictSelectTag.vue'
  import {
    YqFormSearchLocation
  } from '@/mixins/YqFormSearchLocation'

  export default {
    name: 'TerminalList',
    mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
    components: {
      AutoControlScriptModal,
      JSuperQuery,
      JDictSelectTag,
      alarmUpload
    },
    data() {
      return {
        maxLength:50,
        description: '脚本管理页面',
        formItemLayout: {
          labelCol: {
            style: 'width:90px'
          },
          wrapperCol: {
            style: 'width:calc(100% - 90px)'
          }
        },
        fileList: null,
        searchedDepKey: undefined,
        bSearchedDepKey: '',
        //列设置
        settingColumns: [],
        columns: [{
            title: '脚本名称',
            dataIndex: 'scriptName',
            customCell: () => {
              let cellStyle = 'text-align: left; min-width: 50px;max-width:300px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '脚本类型',
            dataIndex: 'scriptLanguage',
            customCell: () => {
              let cellStyle = 'text-align: center; min-width: 100px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '运行脚本',
            dataIndex: 'scriptContext',
            scopedSlots: {
              customRender: 'scriptContext'
            },
            display: false,
            customCell: () => {
              let cellStyle = 'text-align: center; min-width: 130px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '附件',
            dataIndex: 'scriptFile',
            scopedSlots: {
              customRender: 'scriptFile'
            },
            customCell: () => {
              let cellStyle = 'text-align: left; min-width: 180px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '描述',
            dataIndex: 'description',
            customCell: () => {
              let cellStyle = 'text-align: center; min-width: 130px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '操作',
            dataIndex: 'action',
            scopedSlots: {
              customRender: 'action'
            },
            customCell: () => {
              let cellStyle = 'text-align: center; width: 180px'
              return {
                style: cellStyle
              }
            },
          }
        ],
        refreshKey: 0,
        url: {
          list: '/autoControl/script/list',
          delete: '/autoControl/script/delete',
          deleteBatch: '/autoControl/script/deleteBatch',
          exportXlsUrl: '/autoControl/script/exportXls',
          importExcelUrl: '/autoControl/script/importExcel'
        }
      }
    },
    created() {},
    computed: {
      importExcelUrl: function () {
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
      },
    },
    mounted() {
      this.loadData(1)
    },

    methods: {
      //勾选时进行状态判断
      onSelectChange(selectedRowKeys, selectionRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectionRows = selectionRows
      },
      handleAdd: function () {
        this.$refs.modalForm.add()
        this.$refs.modalForm.title = '新增'
        this.$refs.modalForm.disableSubmit = false
      },
      //查看详情
      handleShow(record) {
        this.$refs.terminalTopInfo.show(record)
      },
      //删除
      deleteRecord(record) {
        if (!this.url.delete) {
          this.$message.error('请设置url.delete属性!')
          return
        }
        var that = this
        this.$confirm({
          title: '确认删除',
          okText: '是',
          cancelText: '否',
          content: '是否删除选中数据?',
          onOk: function () {
            that.loading = true
            deleteAction(that.url.deleteBatch, {
              ids: record.id
            }).then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.loadData()
              } else {
                that.$message.warning(res.message)
                that.loadData()
              }
            })
          }
        })
      },
      //表单查询,点击查询按钮，默认查询第一页
      dosearch() {
        if (!this.searchedDepKey && this.bSearchedDepKey) {
          this.searchedDepKey = this.bSearchedDepKey
        }
        if (Array.isArray(this.searchedDepKey)) {
          this.queryParam.deptId = this.searchedDepKey.join(',')
        } else if (typeof this.searchedDepKey === 'string') {
          this.queryParam.deptId = this.searchedDepKey
        }
        this.bSearchedDepKey = ''
        this.loadData(1)
      },
      //重置
      doreset() {
        this.queryParam = {}
        this.loadData(1)
      },
    }
  }
</script>
<style lang='less' scoped>
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';

  /*操作--设置按钮背景色*/
  ::v-deep .ant-table-filter-icon {
    background-color: #e5e5e5;
  }

  .stateBox {
    margin-left: 20px;
  }

  .stateImg {
    vertical-align: middle
  }
</style>