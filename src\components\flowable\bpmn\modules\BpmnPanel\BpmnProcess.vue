<template>
  <div>
    <a-form ref="refForm" :form="formData" :label-col="{ span: 5 }" :wrapper-col="{ span: 19 }">
      <a-collapse
        accordion
        :activeKey="activeName"
        :bordered="false"
        expandIconPosition="right"
        @change="collapseChange"
      >
        <a-collapse-panel key="1" header="基本设置">
          <a-form-item label="流程主键">
            <a-input v-model="formData.id" disabled />
          </a-form-item>
          <a-form-item label="流程名称">
            <a-input v-model="formData.name" />
          </a-form-item>
          <a-form-item label="流程描述">
            <a-textarea v-model="formData.documentation" :rows="3" allow-clear />
          </a-form-item>
          <a-form-item label="流程分类">
            <a-select
              v-model="formData.processCategory"
              :getPopupContainer="(node) => node.parentNode"
              @change="changeCategory"
            >
              <a-select-option v-for="item in processCategory" :key="item.value" :value="item.value">
                {{ item.text }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="业务" v-show="busEditable">
            <a-select v-model="formData.tenantId" :getPopupContainer="(node) => node.parentNode">
              <a-select-option v-for="(item, index) in businessList" :key="'business_' + index" :value="item.id">
                {{ item.businessTitle }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-collapse-panel>
        <a-collapse-panel key="2" header="候选配置">
          <a-form-item label="可发起人">
            <a-input v-model="formData.candidateStarterUsers" allow-clear />
          </a-form-item>
          <a-form-item label="可发起组">
            <a-input v-model="formData.candidateStarterGroups" allow-clear />
          </a-form-item>
        </a-collapse-panel>
        <a-collapse-panel key="executionListener" header="执行监听器">
          <span class="listener-add" slot="extra" @click.stop="showListenerForm">
            <a-icon type="plus-circle" /> 添加
          </span>
          <listener
            ref="listenerTable"
            type="Execution"
            :title="listenerTitle"
            :element="element"
            :modeler="modeler"
            @changeListenerShow="changeListenerShow"
            @saveListener="saveExecutionListener"
            :listenerShow="listenerShow"
          ></listener>
        </a-collapse-panel>
        <a-collapse-panel key="propertyForm" header="附加属性">
          <span class="listener-add" slot="extra" @click.stop="showPropertyForm">
            <a-icon type="plus-circle" /> 添加
          </span>
          <properties
            ref="propertiesTable"
            :propertiesShow="propertiesShow"
            :title="propertiesTitle"
            :element="element"
            :modeler="modeler"
            @changePropertiesShow="changePropertiesShow"
          ></properties>
        </a-collapse-panel>
      </a-collapse>
    </a-form>
  </div>
</template>

<script>
import mixinPanel from '../../mixins/mixinPanel'
import Listener from '../Listeners'
import Properties from '../Properties'

export default {
  name: 'BpmnProcess',
  components: {
    Properties,
    Listener,
  },
  mixins: [mixinPanel],
  props: {
    processCategory: {
      type: Array,
      required: true,
    },
    businessList: {
      type: Array,
      required: true,
    },
    processInfo: {
      type: Object,
      default: () => {
        return {}
      },
    },
  },
  data() {
    return {
      activeName: '1',
      formData: {},
      rules: {
        id: [{ required: true, message: '该项不能为空', trigger: 'change' }],
        name: [{ required: true, message: '该项不能为空', trigger: 'change' }],
      },
      listenerShow: false,
      listenerTitle: '监听器',
      executionListenerLength: 0,
      propertiesShow: false,
      propertiesTitle: '附加属性',
      propertiesLength: 0,
      busEditable: false,
    }
  },
 
  created() {
    this.initFormData()
  },
  mounted() {},
  methods: {
    initFormData() {
      const data = {
        ...this.element.businessObject,
        ...this.element.businessObject.$attrs,
      }
      const targetNamespace = this.element.businessObject.$parent.targetNamespace
      data.processCategory = targetNamespace
      data.documentation = this.getDocumentation()
      this.busEditable = data.processCategory === 'business'
      data.tenantId = this.processInfo.tenantId || ''
      this.formData = data
    },
    //监听折叠面板
    collapseChange(e) {
      // if(this.activeName)
      this.activeName = e
      // console.log("监听到折叠面板变化 === ",e,this.activeName)
    },
    //显示属性
    showPropertyForm() {
      this.activeName = 'propertyForm'
      this.propertiesTitle = '添加附加属性'
      if (this.$refs.propertiesTable) {
        this.$refs.propertiesTable.init()
      }
      this.propertiesShow = true
    },
    //显示添加监听器
    showListenerForm() {
      this.activeName = 'executionListener'
      this.listenerTitle = '添加监听器'
      if (this.$refs.listenerTable) {
        this.$refs.listenerTable.init()
        this.listenerShow = true
      } else {
        this.$nextTick(() => {
          this.listenerShow = true
        })
      }
    },
    //改变监听器弹窗
    changeListenerShow(v) {
      this.listenerShow = v
    },
    saveExecutionListener(v) {
      this.executionListenerLength = v
    },
    changePropertiesShow(v) {
      this.propertiesShow = v
    },
    saveProperties(v) {
      this.propertiesLength = v
    },
    changeCategory(value) {
      this.busEditable = value === 'business'
    },
  },
}
</script>

<style lang="less" scoped>
.listener-add {
  color: #1890ff;
  cursor: pointer;
}
</style>