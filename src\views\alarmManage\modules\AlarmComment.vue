<template>
  <div class='content-box'>
    <div class="title-box">
      <span class="title">评论</span>
    </div>
    <div class='add-comment'>
      <div class='table-operator table-operator-style'>
        <a-button @click='handleAdd'>评论</a-button>
      </div>
    </div >

    <a-table
      ref='table'
      bordered
      :row-key='(record, index) => {return record.id}'
      :columns='columns'
      :dataSource='dataSource'
      :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
      :pagination='ipagination'
      :loading='loading'
      @change='handleTableChange'>
      <span slot='action' class='caozuo' slot-scope='text, record'  >
        <a-popconfirm :disabled='record.createBy!==sysUser' title="确定删除吗?" @confirm="() => handleDelete(record.id)">
          <a :class='{"disabled-delete":record.createBy!==sysUser}'>删除</a>
        </a-popconfirm>
      </span>

      <template slot='tooltip' slot-scope='text'>
        <a-tooltip placement='topLeft' :title='text' trigger='hover'>
          <div class='tooltip'>
            {{ text }}
          </div>
        </a-tooltip>
      </template>
    </a-table>
    <add-alarm-comment-modal ref='modalForm' @ok='modalFormOk' :alarm-id='queryParam.alarmId'></add-alarm-comment-modal>
  </div>
</template>
<script>
import {JeecgListMixin} from '@/mixins/JeecgListMixin'
import {postParamsAction,putParamsAction, getAction } from '@api/manage'
import addAlarmCommentModal from '@views/alarmManage/modules/AddAlarmCommentModal.vue'

export default {
  name: "KnowledgeComment",
  components:{addAlarmCommentModal},
  mixins: [JeecgListMixin],
  props:{
    alarmInfo:{
      type:Object,
      required:true,
      default:{}
    }
  },
  data() {
    return {
      sysUser:this.$store.getters.userInfo.username,
      disableMixinCreated:true,
      columns: [
        {
          title: '评论时间',
          dataIndex: 'createTime'
        },
        {
          title: '评论内容',
          dataIndex: 'evaluationMessage',
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 150px;max-width:400px'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'tooltip'
          }
        },
        {
          title: '评论人',
          dataIndex: 'evaluationUser'
        },
        {
          title: '操作',
          dataIndex: 'action',
          customCell: () => {
            let cellStyle = 'width:100px'
            return {
              style: cellStyle
            }
          },
          scopedSlots: {
            customRender: 'action'
          }
        }
      ],
      url: {
        list: '/alarm/alarmEvaluation/list',//评论列表
        delete:'/alarm/alarmEvaluation/delete',
      }
    }
  },
  watch: {
    alarmInfo: {
      handler(val) {
        this.queryParam={}
        this.dataSource=[]
        if (Object.keys(val).length > 0) {
          this.queryParam.alarmId = val.id
          this.loadData(1)
        }
      },
      deep: true,
      immediate: true
    }
  }
}
</script>

<style scoped lang="less">
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
.content-box{
  margin-bottom: 20px;
  .title-box {
    margin-bottom: 10px;
  }

  .title {
    padding-left: 7px;
    border-left: 4px solid #1e3674;
  }
}

.add-comment{
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-flow: row wrap;
  margin-bottom: 16px;
}

.like-unlike-wrapper{
  margin: 0px 20px;

  .like-unlike {
    display: inline-block;
    white-space: nowrap;
    width: 30px;
    height: 30px;
    line-height: 30px;
    border-radius: 50%;
    border: 1px solid #e8e8e8;
    text-align: center;


    .like-unlike-icon {
      font-size: 18px;
    }
  }
  .like-unlike:hover{
    cursor: pointer;
    border: 1px solid #409eff;
  }
  .active-like,.active-unlike{
    color:#409eff
  }
}

.table-operator{
  margin-bottom: -16px;
}
.caozuo{
  .disabled-delete{
    color: rgba(0, 0, 0, 0.25) !important;
    cursor: default;
  }
}
</style>