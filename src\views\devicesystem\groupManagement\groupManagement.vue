<template>
  <div style="height:100%">
    <keep-alive exclude="groupInfo">
      <component style="height:100%" :is="pageName" :data="data" />
    </keep-alive>
  </div>
</template>
<script>
import groupList from './groupList.vue'
import groupInfo from './groupInfo.vue'
export default {
  name: 'groupManagement',
  data() {
    return {
      isActive: 0,
      data: {}
    }
  },
  components: {
    groupList,
    groupInfo
  },
  created() {
    this.pButton1(0)
  },
  //使用计算属性
  computed: {
    pageName() {
      switch (this.isActive) {
        case 0:
          return 'groupList'
        default:
          return 'groupInfo'
      }
    }
  },
  methods: {
    pButton1(index) {
      this.isActive = index
    },
    pButton2(index, item) {
      this.isActive = index
      this.data = item
    }
  }
}
</script>