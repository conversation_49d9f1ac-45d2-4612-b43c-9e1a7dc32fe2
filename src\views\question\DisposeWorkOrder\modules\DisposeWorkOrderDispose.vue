<template>
  <a-modal
    :title="title"
    :width="modalWidth"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @cancel="handleCancel"
    @ok="handleOk"
    okText=""
    cancelText="关闭"
    wrapClassName="ant-modal-cust-warp"
    style="height: 70%; overflow: hidden; overflow-y: auto"
    :centered="true"
  >
    <a-form :form="form" style="height: 100%; overflow-y: auto">
      <div>
        <a-row>
          <a-col>
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="问题类型">
              <!-- <a-input disabled v-decorator="[ 'questionType', formValidator.questionType]" /> -->
              <j-dict-select-tag
                type="list"
                disabled
                v-decorator="['questionType', formValidator.questionType]"
                :trigger-change="true"
                dictCode="helpQuestionType"
                placeholder="请输入问题类型"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col>
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="问题内容">
              <textarea
                style="width: 100%"
                disabled
                placeholder="请输入备注"
                v-decorator="['question', formValidator.question]"
              ></textarea>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col>
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="提问人">
              <a-input disabled v-decorator="['quizzer', formValidator.quizzer]" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col>
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="联系电话">
              <a-input disabled v-decorator="['contact', formValidator.contact]" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col>
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="地区">
              <a-input disabled v-decorator="['regionName']" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col>
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="提问时间">
              <a-input disabled v-decorator="['createTime', formValidator.createTime]" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col>
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="设备地址">
              <a-input disabled v-decorator="['ip', formValidator.ip]" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col>
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="分配时间">
              <a-input disabled v-decorator="['confirmTime', formValidator.confirmTime]" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col>
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="未解决原因">
              <a-input :allowClear="true" autocomplete="off" v-decorator="['unsolveCause']" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-form-item label="解决方案 " :labelCol="labelCol" :wrapperCol="wrapperCol">
            <textarea
              style="width: 100%"
              placeholder="请输入解决方案"
              v-decorator="['answererContent', formValidator.answererContent]"
              class="border_color"
            ></textarea>
          </a-form-item>
        </a-row>
      </div>
    </a-form>
  </a-modal>
</template>
<script>
import { formatDate } from '@/utils/util'
import pick from 'lodash.pick'
import { userList, editRecord } from '@api/AllocationIssue'
import JDictSelectTag from '@/components/dict/JDictSelectTag'
export default {
  components: {
    JDictSelectTag,
  },
  name: 'DisposeWorkOrderDispose', //处理工单弹窗
  data() {
    return {
      title: '操作',
      visible: false,
      confirmLoading: false,
      /* 弹框宽 */
      modalWidth: '700px',
      form: this.$form.createForm(this),
      usersList: [], //运维人员列表
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      formValidator: {
        answererContent: {
          rules: [
            {
              required: false,
              message: '必填!',
            },
            {
              min: 2,
              max: 500,
              message: '长度在 2 到 500 个字符',
              trigger: 'blur',
            },
          ],
        },
      },
    }
  },
  mounted() {},
  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      this.form.resetFields()
      //获取运维人员列表
      this.getUsersList()
      this.model = Object.assign({}, record)
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(
          pick(
            this.model,
            'questionType',
            'question',
            'quizzer',
            'createTime',
            'ip',
            'contact',
            'confirmTime',
            'answererContent',
            'unsolveCause',
            'regionName'
          )
        )
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    //获取运维人员列表
    getUsersList() {
      userList().then((res) => {
        if (res.success) {
          this.usersList = res.result
        } else {
          this.$message.error('获取运维人员列表失败，请刷新后重试！')
        }
      })
    },

    handleOk() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let formData = Object.assign(this.model, values)
          //处理后，变成待处理工单
          formData.status = '处理中'
          formData.answerer = this.$store.state.user.realname
          formData.answerTime = formatDate(Date.parse(new Date()), 'yyyy-MM-dd hh:mm:ss')
          editRecord(formData)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
              that.close()
            })
        }
      })
    },
    handleCancel() {
      this.close()
    },
  },
}
</script>
<style scoped>
.border_color {
  border-color: #d3d3d3;
}
</style>
<style lang="less" scoped>
::v-deep .ant-modal-body {
  padding: 24px 48px 24px 48px;
}
::v-deep .ant-modal {
  padding: 24px;
}
@media (max-width: 1012px) {
  ::v-deep .ant-modal {
    max-width: calc(100vw - 12px);
    margin: 0;
  }
}
</style>
