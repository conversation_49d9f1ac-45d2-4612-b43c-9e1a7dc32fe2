<template>
  <div style='height: 100%'>
  <a-card :bordered='false'>
    <div class='action'>
      <span class='return'>
        <img src='~@/assets/return1.png' alt='' @click='getGo'>
      </span>
    </div>
    <a-descriptions :column='{ xxl: 2, xl: 2, lg: 2, md: 2, sm: 2, xs: 2 }' bordered>
      <a-descriptions-item label='类型名称'>{{ data.typeName }}</a-descriptions-item>
      <a-descriptions-item label='类型标识'>{{ data.typeCode }}</a-descriptions-item>
      <a-descriptions-item label='绑定项'>{{getName(data) }}</a-descriptions-item>
      <a-descriptions-item label='是否使用'>{{data.isUsed == 1 ? '是' : '否' }}</a-descriptions-item>
      <a-descriptions-item label='描述'>{{data.description }}</a-descriptions-item>
    </a-descriptions>
  </a-card>
  </div>
</template>
<script>

  export default {
    name: 'deviceClassificationDetails',
    props: {
      data: {
        type: Object,
      }
    },
    data() {
      return {}
    },
    mounted() {},
    methods: {
      getName(info) {
        let name = ''
        if (info.childrenList&&info.childrenList.length>0){
          info.childrenList.forEach((ele) => {
            name += ele.childName + ' '
          })
        }
        return name
      },
      getGo() {
        this.$parent.pButton1(0)
      },
    },
  }
</script>
<style scoped lang='less'>
.action {
  display: flex;
  justify-content: end;
  align-items: center;
  flex-flow: row nowrap;
  margin-bottom: 12px;

  .return {
    img {
      width: 20px;
      height: 20px;
      cursor: pointer
    }
  }
}

::v-deep .ant-descriptions-view {
  border-radius: 0px;
}

::v-deep .ant-descriptions-bordered .ant-descriptions-item-label {
  background-color: rgb(250, 250, 250);
  text-align: center;
  width: 17%;
}

::v-deep .ant-descriptions-item-label,
.ant-descriptions-item-content {
  color: rgb(96, 98, 102) !important;
}

::v-deep .ant-descriptions-bordered .ant-descriptions-item-content {
  word-break: break-word;
  white-space: normal;
}
</style>