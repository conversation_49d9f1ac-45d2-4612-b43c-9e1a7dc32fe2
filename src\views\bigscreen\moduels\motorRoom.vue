<template>
  <div class="room-content">
    <div class="threeD-content" v-if="threeDFlag">
      <three-scan v-if="roomVisible" :modelsArr="modelsArr" :roomId="roomId"></three-scan>
      <div class="room-select">
        <p class="room-title-p">
          <a-tree-select :treeDefaultExpandAll="true" :replaceFields="replaceFields"
            :getPopupContainer="(node) => node.parentNode" :dropdownClassName="'topo-tree-select'" :dropdownStyle="{
              maxHeight: '4rem',
              overflow: 'auto',
              backgroundColor: '#1B1C20',
              color: '#fff',
              opacity: 0.9,
              boxShadow: '1px #000000',
              fontSize:'0.3rem',
              width: '2.5rem',
            }" v-model="roomId" :treeData="treeData" @change="onChange"></a-tree-select>
        </p>
      </div>
    </div>
    <div class="twoD-content" v-if="twoDFlag">
    </div>
  </div>
</template>
<script>
  import {
    getAction,
    postAction,
    putAction,
    deleteAction
  } from '@/api/manage'
  import ThreeScan from '@/views/threejsEditor/sz-three-scan'
  import {
    tryToJSON
  } from '@/utils/util.js'
  export default {
    components: {
      ThreeScan,
    },
    data() {
      return {
        threeDFlag: true,
        twoDFlag: false,
        url: {
          roomInfo: '/topo/room/info',
          roomTree: '/data-analysis/topo/room/tree2',
        },
        roomInfo: {
          id: '',
          twoDJson: '',
        },
        threeJson: '',
        roomId: '',
        treeData: [],
        replaceFields: {
          children: 'children',
          title: 'name',
          key: 'id',
          value: 'id',
          selectable: 'selectable'
        },
        firstRoom: false,
        modelsArr: [],
        roomVisible: false,
      }
    },
    created() {},
    mounted() {
      this.load3DInfo()
    },
    methods: {
      async load3DInfo() {
        //获取左侧机房tree
        await getAction(this.url.roomTree).then((res) => {
          if (res.success) {
            this.handleTreeNodeValue(res.result)
            this.treeData = [...res.result]
          } else {
            this.$message.error(res.message)
          }
        })
        //获取机房的3D拓扑信息
        this.getRoomInfo(this.roomId)
      },
      show2D() {
        this.threeDFlag = false
        getAction(this.url.roomInfo, {
          id: this.roomId,
          view: '2d'
        }).then((res) => {
          if (res.success) {
            this.roomInfo = Object.assign(this.roomInfo, {
              id: this.roomId,
              twoDJson: res.result
            })
            this.twoDFlag = true
          } else {
            this.$message.warning(res.message)
            this.threeDFlag = true
          }
        })
      },
      handleTreeNodeValue(result) {
        for (let i of result) {
          if (i.type === 'city') {
            i.selectable = false
          } else {
            i.selectable = true
            if (!this.firstRoom) {
              this.roomId = i.id
              this.firstRoom = !this.firstRoom
            }
          }
          if (i.children && i.children.length > 0) {
            this.handleTreeNodeValue(i.children)
          }
        }
      },
      onChange(value) {
        this.roomFlag = false
        this.getRoomInfo(value)
      },
      ////获取机房的3D拓扑信息
      getRoomInfo(roomId) {
        if (this.roomVisible) {
          this.roomVisible = false;
        }
        this.getRoomInfoAction(roomId)
      },
      getRoomInfoAction(roomId) {
        getAction(this.url.roomInfo, {
          id: roomId,
          view: '3d'
        }).then((res) => {
          if (res.success) {
            this.roomId = roomId
            this.modelsArr = res.result || []
            if (this.modelsArr.length > 0) {
              try {
                this.modelsArr.forEach((el) => {
                  el.position = tryToJSON(el.position)
                  el.rotation = tryToJSON(el.rotation)
                  el.scale = tryToJSON(el.scale)
                  el.userData = tryToJSON(el.userData)
                  if (Object.prototype.toString.call(el.userData) === '[object Object]') {
                    el.userData.dataId = el.id
                  }
                })
              } catch (error) {
                this.modelsArr = []
                this.$message.error('机房数据有错误！')
              }
            }
            if (!this.roomFlag && !this.appFlag && !this.networkFlag) {
              this.roomFlag = true
            }
            this.roomVisible = true;
          } else {
            this.$message.warning(res.message)
          }
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  .viewCenter {
    color: #fff;
    height: 100%;
    width: 100%;
  }

  .title-container {
    display: flex;
    justify-content: center;
    height: 5%;
  }

  .title {
    width: 100px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 15px;
    color: white;
  }

  .content-container {
    height: 94%;
    width: 100%;
    background: #222224;
    padding: 0 16px;
  }

  .net-content {
    position: relative;
    // width: 22.375rem /* 1790/80 */;
    width: 100%;
    height: 100%;
    background-color: #111217;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .big-screen-div {
    width: 86%;
    height: 100%;
  }

  .topo-list {
    width: 14%;
    height: 96%;
    padding-right: 16px;
    overflow-y: auto;
  }

  .list-div {
    text-align: center;
    cursor: pointer;

    span {
      font-size: 14px;
      color: #4e4f53;
    }
  }

  .select-span {
    color: #aaaaac !important;
  }

  .img-div {
    width: 100%;
    height: 100%;
    border: 1px solid #4e4f53;
    position: relative;
  }

  ::v-deep .img-div>svg {
    width: 100% !important;
    height: 100% !important;
  }

  ::v-deep .img-div>svg>g {
    transform: matrix(1, 0, 0, 1, 1, 1) !important;
  }

  .select-img {
    border: 1px solid #aaaaac;
  }

  .room-content {
    width: 100%;
    height: 100%;
  }

  .threeD-content,
  .twoD-content {
    width: 100%;
    height: 100%;
  }

  .threeD-content {
    position: relative;
  }

  .room-select {
    position: absolute;
    top: 30px;
    right: 60px;
    width: 2.5rem;
    font-size: 0.3rem;
  }

  .room-title-p {
    color: #64e0ea;
    font-size: 0.3rem;
    margin-bottom: 7px;
  }

  /deep/ .ant-select-tree-title {
    font-size: 0.3rem !important;
    color: rgba(255, 255, 255, .6);
  }

  ::v-deep .container canvas {
    height: 100% !important;
    width: 100% !important;
  }

  ::v-deep .ant-select {
    color: #63d7de !important;
    font-size: 27px;
    width: 2.5rem;

    .ant-select-selection {
      background-color: #101117 !important;
      border: 1px solid #101117 !important;
      box-shadow: none !important;
    }
  }

  ::v-deep .ant-select-selection:hover {
    border-color: #101117 !important;
  }

  ::v-deep .ant-select-arrow {
    color: #63d7de !important;
    right: 5px !important;
  }

  .pagination-div {
    display: flex;
    flex-direction: row-reverse;
  }
</style>