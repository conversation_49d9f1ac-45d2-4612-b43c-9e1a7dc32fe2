<template>
  <j-modal :title='title' :width='width' :visible='visible' :destroyOnClose='true'
           :okButtonProps="{style:{display:'none'}}" :centered='true' switchFullscreen
           @cancel='handleCancel'
           cancelText='关闭'>
      <div>
        <a-table ref='table' bordered :rowKey='(record,index)=>{return record.id}' :columns='columns'
                 :dataSource='dataSource' :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
                 :pagination='false'
                 :loading='loading'>
          <!-- 字符串超长截取省略号显示-->
          <template slot='index' slot-scope='text,record,index'>
            <span>{{ index + 1 }}</span>
          </template>
          <span slot='templateContent' slot-scope='text'>
            <j-ellipsis :value='text' :length='25' />
          </span>
          <template slot='tooltip' slot-scope='text'>
            <a-tooltip placement='topLeft' :title='text' trigger='hover'>
              <div class='tooltip'>
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
      </div>
  </j-modal>
</template>

<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
import { getAction } from '@api/manage'

export default {
  name: 'DeviceAlarm',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  data() {
    return {
      title: '设备告警',
      width: '1000px',
      visible: false,
      disableSubmit: false,
      confirmLoading: false,
      record: {},
      columns: [
        {
          title: '序号',
          dataIndex: 'index',
          scopedSlots: {
            customRender: 'index'
          },
          width: 80,
          customCell: () => {
            let cellStyle = 'text-align: center'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '告警名称',
          dataIndex: 'alarmName',
          scopedSlots: {
            customRender: 'alarmName'
          },
          customCell: () => {
            let cellStyle = 'text-align: center'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '告警信息',
          dataIndex: 'alarmMessage',
          customCell: () => {
            let cellStyle = 'text-align: center'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '告警级别',
          dataIndex: 'levelText',
          scopedSlots: {
            customRender: 'levelText'
          },
        },
        // {
        //   title: '描述',
        //   dataIndex: 'description',
        //   scopedSlots: {
        //     customRender: 'description'
        //   },
        //   customCell: () => {
        //     let cellStyle = 'text-align: left;min-width: 200px;max-width:400px'
        //     return {
        //       style: cellStyle
        //     }
        //   }
        // },
        // {
        //   title: '操作',
        //   align: 'center',
        //   width: 180,
        //   fixed: 'right',
        //   dataIndex: 'action',
        //   scopedSlots: {
        //     customRender: 'action'
        //   },
        // },
      ],
      url: {
        list: '/abutment/system/getAlarm',
      },
      disableMixinCreated:true,
      alarmLevels:{},
      dataSource:[],
    }
  },
  created() {
  },
  mounted() {

  },
  methods: {
    show(record) {
      this.visible = true;
      this.dataSource = record.abutmentAlarmList || []
    },
    handleCancel() {
      this.close()
    },
    handleOk() {
      this.close()
    },
    close() {
      this.visible = false
    },
  }
}
</script>


<style scoped lang='less'>
@import '~@assets/less/normalModal.less';
</style>