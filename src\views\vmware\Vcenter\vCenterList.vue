<template>
  <a-row style="height: 100%">
    <a-col :xl="5" :lg="6" :md="8" :sm="10" :xs="10"
      style="height: 100%; overflow: hidden; overflow-y: auto; background: #fff">
      <div style="margin:10px 0 0 16px;"><img src="~@/assets/return1.png" alt="" @click="goBack()"
          style="width: 18px; height: 18px; cursor: pointer" /></div>
      <v-center-tree :inputFlag="true" @selected="treeSeletedSearch" :tree-url="'/device/deviceInfo/vcenterTree'"
        :params="deviceId" :arr-type="[null, 'product']" :icon-name="['cluster', 'desktop']" :btnIconName="'home'"
        :is-show-all-btn="true" :btnName="'vCenter'" :is-show-btn-icon="true" :is-show-icon="true" placeholder="请输入名称">
      </v-center-tree>
    </a-col>
    <a-col :xl="19" :lg="18" :md="16" :sm="14" :xs="14" style="height: 100%; overflow: hidden; overflow-y: auto">
      <a-row style="height: 100%; margin-left: 16px; margin-right: 4px">
        <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
          <a-card :bordered="false" style="width: 100%; flex: auto">
            <div class='tabs-container'>
              <a-tabs :animated='false' class='device-tabs' :active-key='defaultActiveKey' default-active-key='1'
                @change='callback'>
                <a-tab-pane key='1' tab='摘要'>
                  <abstract ref='Abstract'></abstract>
                </a-tab-pane>
                <a-tab-pane v-if="this.vCenterType == 'vcenter'" key='2' tab='设备信息' :force-render='false'>
                  <StateInfo ref='stateInfo'>
                  </StateInfo>
                </a-tab-pane>
                <a-tab-pane v-if="this.vCenterType != 'vm' && this.vCenterType != 'host'" key='3' tab='主机列表'
                  :force-render='false'>
                  <host-list ref='hostList'>
                  </host-list>
                </a-tab-pane>
                <a-tab-pane v-if="this.vCenterType != 'vm'" key='4' tab='虚拟机列表' :force-render='false'>
                  <virtual-machine-list ref='virtualMachineList'>
                  </virtual-machine-list>
                </a-tab-pane>
              </a-tabs>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </a-col>
  </a-row>
</template>

<script>
  import '@/assets/less/TableExpand.less'
  import StateInfo from '@/views/devicesystem/deviceshow/StateInfo.vue'
  import Abstract from './Abstract'
  import {
    mixinDevice
  } from '@/utils/mixin'
  // import {
  //   JeecgListMixin
  // } from '@/mixins/JeecgListMixin'
  import JSuperQuery from '@/components/jeecg/JSuperQuery.vue'
  import {
    queryAssetsCategoryTreeList
  } from '@/api/device'
  import {
    httpAction,
    getAction,
    postAction,
  } from '@/api/manage'
  //引入公共devicetree组件
  import vCenterTree from '@/components/tree/vCenterTree.vue'
  import virtualMachineList from './virtualMachineList'
  import hostList from './hostList'
  import difference from 'lodash/difference';

  var deviceForTag = '';
  export default {
    name: 'vCenterList',
    mixins: [mixinDevice],
    components: {
      JSuperQuery,
      virtualMachineList,
      hostList,
      getAction,
      StateInfo,
      Abstract,
      vCenterTree,
    },
    props: {
      data: {
        type: Object,
      },
    },
    data() {
      return {
        deviceId: '',
        vCenterType: '',
        treeData: {},
        record: {},
        dataInfo: {},
        defaultActiveKey: '1',
        url: {
          getData: '/device/deviceInfo/vcenterDetail'
        },
        dataSource: [], //table数据
        name: '',
      }
    },
    created() {
      this.deviceId = this.data.id
    },
    mounted() {
      this.dataInfo = this.data
    },
    methods: {
      //返回上一级
      goBack() {
        this.$parent.pButton1(0)
      },
      getData(type, title) {
        getAction(this.url.getData, {
          deviceId: this.deviceId,
          type: type,
          title: title
        }).then((res) => {
          if (res.success) {
            this.record = res.result
            this.$refs.Abstract.show(this.record, this.treeData)
          }
        })
      },
      callback(key) {
        this.defaultActiveKey = key
        let that = this
        setTimeout(
          function () {
            if (key == '1') {
              that.$refs.Abstract.show(that.record, this.treeData)
            } else if (key == '2') {
              that.$refs.stateInfo.show(that.dataInfo)
            } else if (key == '3') {
              that.$refs.hostList.show(that.treeData, that.data.id)
            } else if (key == '4') {
              that.$refs.virtualMachineList.show(that.treeData, that.data)
            }
          },
          100)
      },
      treeSeletedSearch(option) {
        this.treeData = option
        this.getData(option.type, option.title)
        this.defaultActiveKey = '1'
        this.vCenterType = option.type
        this.loadData(1)
      },
      loadData() {},
    },
  }
</script>
<style lang='less' scoped>
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';


  .gutter-example {
    margin-left: -16px;
  }

  .gutter-row {
    padding-right: 0px !important;
    margin-bottom: 16px;
  }

  ::v-deep .gutter-example .ant-row>div {
    background: transparent;
    border: 0;
  }

  .row-example {
    margin: 0px !important;
    margin-left: 4px !important;
  }

  .ant-table-row-cell-break-word span a {
    color: #409eff !important;
  }

  /*.ant-row .ant-col-3 .ant-card .ant-card-body {
  height: 810px !important;
}*/
  /*操作--设置按钮背景色*/
  ::v-deep .ant-table-filter-icon {
    background-color: #e5e5e5;
  }

  .stateBox {
    margin-left: 20px;
  }

  .stateImg {
    vertical-align: middle
  }
</style>