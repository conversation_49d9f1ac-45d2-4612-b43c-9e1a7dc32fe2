<template>
  <a-modal
    :title="title"
    :width="modalWidth"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭"
    wrapClassName="ant-modal-cust-warp"
    style="top:5%;height: 95%;overflow: auto"
  >
    <a-tabs :animated="false" default-active-key="1" @change="callback">
      <a-tab-pane key="1" tab="基本信息" @click="onIndex(index)">
        <table1 ></table1>
      </a-tab-pane>
      <a-tab-pane key="2" tab="流程跟踪" @click="onIndex(index)">
        Content of Tab Pane 2
      </a-tab-pane>
      <a-tab-pane key="3" tab="历史记录">
        <table2></table2>
      </a-tab-pane>
      <a-tab-pane key="4" tab="关联配置项">
        <table3></table3>
      </a-tab-pane>
      <a-tab-pane key="5" tab="关联问题">
        <table4></table4>
      </a-tab-pane>
      <a-tab-pane key="6" tab="关联变更">
        <table5></table5>
      </a-tab-pane>
      <a-tab-pane key="7" tab="处理结果">
        <table6></table6>
      </a-tab-pane>
    </a-tabs>
  </a-modal>
</template>
<script>
import table1 from '../../application/details/table1'
import table2 from '../../application/details/table2'
import table3 from '../../application/details/table3'
import table4 from '../../application/details/table4'
import table5 from '../../application/details/table5'
import table6 from '../../application/details/table6'
import pick from "lodash.pick";
export default {
    name:'todoDetails',
    components:{
        table1,
        table2,
        table3,
        table4,
        table5,
        table6
    },
    data(){
        return{
            title: '操作',
            confirmLoading: false,
            /* 弹框宽 */
            modalWidth: '55%',
            form: this.$form.createForm(this),
            visible: false,
        }
    },
    methods:{
        add() {
            this.edit({});
        },
        edit(record) {
            this.form.resetFields();
            this.model = Object.assign({}, record);
            this.visible = true;
            this.$nextTick(() => {
                this.form.setFieldsValue(
                pick(
                    this.model,
                    "id",
                    "type",
                    "source",
                    "priority",
                    "contactWay",
                    "title",
                    "description"
                )
                );
            });
        },
        // 关闭弹框
        close() {
        this.$emit('close')
        this.visible = false
        this.current = 0
        },
        // 提交
        handleOk() {
        let that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
            if (!err) {
            that.confirmLoading = true
            let formData = Object.assign(that.model, values)
            let params = {
                id: formData.id,
                type: formData.type,
                priority: formData.priority,
                source: formData.source,
                contactWay: formData.contactWay,
                title: formData.title,
                description: formData.description
            }
            }
        })
        },
        handleCancel() {
            this.close()
        },
        onIndex(index){
        },
        // tab
        callback(key) {
        },
    }
}
</script>
<style scoped></style>
