<template>
  <div style='height: 100%'>
    <div id='home-echarts-map'></div>
  </div>
</template>
<script>
import { heightPixel, widthPixel } from '@views/statsCenter/com/calculatePixel'
import echarts from 'echarts'
// 地图数据
const mapJson = window.echarts
echarts.registerMap('citymap', mapJson)
export default {
  name: 'mapChart',
  props: {
    terminalList: {
      type: Array,
      required: false,
      default: []
    },
    showTooltip: {
      type: Boolean,
      required: false,
      default: true
    }
  },
  data() {
    return {
      myChart: null,
      zoom: 1,//当前视角的缩放比例。
      maxZoom:14,
      aspectScale: 0.82,//scale地图的长宽比
      layoutSize: '90%',//地图大小,
      layoutCenter1: ['50%', '45%'], //地图位置
      layoutCenter2: ['50%', '46%'],
      layoutCenter3: ['50%', '47%'],
      layoutCenter4: ['50%', '48%'],
      layoutCenter5: ['50%', '49%'],
      //城市经纬度数据
      geoCoordMap: window.longitudeLatitude,
      //城市流线数据
      GZData: window.city,
      //连线数据
      convertData: [],
      //点数据
      scatterData: [],

      //地图区域样式
      series_0_itemNormal: {},
      series_0_itemHover: {},

      series_1_symbolSizeNormal: 0,
      series_1_symbolSizeHover: widthPixel(50),

      series_2_symbolSizeNormal: [0, 0],
      series_2_symbolSizeHover: [widthPixel(40), heightPixel(35)],

      series_3_itemNormal: {},
      series_3_itemHover: {},
      //点文字样式
      series_4_labelNormal: {},
      series_4_labelHover: {},

      series_0_data: [],
      series_1_data: [],
      series_2_data: [],
      series_3_data: [],
      series_4_data: [],
    }
  },
  created() {
    let h2 = heightPixel(2)
    let h5 = heightPixel(5)
    let h14 = heightPixel(14)
    let w3 = widthPixel(3)
    let w6 = widthPixel(6)
    let w14 = widthPixel(14)
    let w19 = widthPixel(19)

    this.convertData = this.getConvertData(this.GZData)
    this.scatterData = this.getScatterData(this.geoCoordMap)

    this.series_0_itemNormal = {
      normal: {
        show: true,
        areaColor: {
          type: 'linear',
          x: 1200,
          y: 0,
          x2: 0,
          y2: 1200,
          colorStops: [{
            offset: 0,
            color: 'rgba(33,108,247,0.61)' // 0% 处的颜色
          },
            {
              offset: 0.5,
              color: 'rgba(0,86,255,0.2)' // 50% 处的颜色
            },
            {
              offset: 1,
              color: 'rgba(33,109,247,0.47)' // 50% 处的颜色
            }],
          global: true // 缺省为 false
        },
        borderColor: '#ffffff',
        borderWidth: 1,
        //shadowColor: 'rgba(135,182,252,0.14)',
        shadowColor: 'rgba(135,182,252,0.7)',
        /*shadowColor: 'rgba(255,255,255,0.4)',
        shadowOffsetX: 2 ,
        shadowOffsetY: 2 ,*/
        shadowBlur: w6,
        global: true // 缺省为 false
      },
      emphasis: {
        show: false
      }
    }
    this.series_0_itemHover = {
      normal: {
        show: true,
        areaColor: 'rgba(2,20,58,0.4)',
        borderWidth: 2,
        borderColor: 'rgba(111,213,255,1)',
        shadowColor: 'rgba(111,213,255,0.7)',
        // shadowColor: 'rgba(111,213,255,0.25)',
        shadowBlur: w14
        //shadowOffsetY: h5,
      },
      emphasis: {
        show: false
      }
    }

    this.series_3_itemNormal= {
      normal: {
        show: true,
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0,
            color: 'rgba(208,216,228,1)'
          }, {
            offset: 1,
            color: 'rgb(208,216,228,0)'
          }],
          global: true // 缺省为 false
        },
      },
      emphasis: {
        show: false
      }
    }
    this.series_3_itemHover={
      normal: {
        show: true,
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0,
            color: 'rgb(255,255,255)'
          }, {
            offset: 1,
            color: 'rgb(255,255,255,0)'
          }],
          global: true // 缺省为 false
        }
      },
      emphasis: {
        show: false
      }
    }

    this.series_4_labelNormal = {
      normal: {
        show: true,
        position: 'inside',
        padding: [h5, w14, h2, w14],
        formatter: '{b}',
        align: 'center',
        textStyle: {
          color: '#ffffff',
          fontSize: w19,
          fontWeight: 500,
          // textShadowColor: 'rgba(19,80,143,0.66)',
          // textShadowBlur: 2,
          // textShadowOffsetX: 0,
          // textShadowOffsetY: 2

          textShadowColor: 'rgb(0,0,0)',
          textShadowBlur: w6,
          textShadowOffsetX: w3,
          textShadowOffsetY: h5
        }
      },
      emphasis: {
        show: false
      }
    }
    this.series_4_labelHover = {
      normal: {
        show: true,
        position: 'inside',
        formatter: '{b}',
        // align: 'center',
        padding: [h5, w14, h2, w14],
        borderWidth: 1,
        borderColor: 'rgba(255, 209, 51, 1)',
        borderRadius: 2,
        backgroundColor: 'rgba(255, 209, 51, 1)',
        shadowBlur:w14,
        shadowColor:'rgba(0,0,0,0.5)',
        shadowOffsetY:h5,

        textStyle: {
          color: '#CA4411',
          fontSize: w19,
          fontWeight: 500
        }
      },
      emphasis: {
        show: false
      }
    }
  },
  mounted() {
    this.echart_map()
  },
  destroyed() {
    this.myChart.off()
    this.myChart.dispose()
  },
  methods: {
    /*获取点连线数据*/
    getConvertData(data) {
      var res = []
      for (var i = 0; i < data.length; i++) {
        var dataItem = data[i]
        var fromCoord = this.geoCoordMap[dataItem[0].name] //起点经纬度
        var toCoord = this.geoCoordMap[dataItem[1].name] //终点经纬度
        if (fromCoord && toCoord) {
          res.push({
            fromName: dataItem[0].name, //起点名称
            toName: dataItem[1].name, //终点名称
            coords: [fromCoord, toCoord] //[起点经纬度,终点经纬度]
          })
        }
      }
      return res
    },
    /*获取点基础数据*/
    getScatterData(data) {
      var res = []
      for (var item in data) {
        let params = {
          name: item,
          value: data[item],
          adcode: this.getADCode(item),
          checked: false
        }
        res.push(params)
      }
      return res
    },
    getADCode(areaName) {
      let features = mapJson.features
      for (let i = 0; i < features.length; i++) {
        if (areaName === features[i].properties.name) {
          return features[i].properties.adcode + ''
        }
      }
    },
    /*通过地区名称，返回数据索引号*/
    getScatterDataIndex(name) {
      for (let i = 0; i < this.scatterData.length; i++) {
        if (this.scatterData[i].name === name) {
          return i
        }
      }
    },
    /*初始化点数据中series配置项*/
    initSeriesData(key, value) {
      var res = []
      let data = this.scatterData
      for (let i = 0; i < data.length; i++) {
        let item = {
          name: data[i].name,
          value: data[i].value,
          adcode: data[i].adcode,
          checked: data[i].checked
        }
        item[key] = value
        res.push(item)
      }
      return res
    },
    /*渲染地图*/
    echart_map() {
      this.myChart = this.$echarts.init(document.getElementById('home-echarts-map'))
      this.series_0_data = this.initSeriesData('itemStyle', this.series_0_itemNormal)
      this.series_1_data = this.initSeriesData('symbolSize', this.series_1_symbolSizeNormal)
      this.series_2_data = this.initSeriesData('symbolSize', this.series_2_symbolSizeNormal)
      this.series_3_data = this.initSeriesData('itemStyle', this.series_3_itemNormal)
      this.series_4_data = this.initSeriesData('label', this.series_4_labelNormal)

      let h2 = heightPixel(2)
      let h5 = heightPixel(5)
      let h8 = heightPixel(8)
      let h15 = heightPixel(15)
      let h20 = heightPixel(20)
      let h25 = heightPixel(25)
      let h35 = heightPixel(35)

      let w3 = widthPixel(3)
      let w6 = widthPixel(6)
      let w8 = widthPixel(8)
      let w10 = widthPixel(10)
      let w14 = widthPixel(14)
      let w19 = widthPixel(19)
      let w30 = widthPixel(30)
      let w70 = widthPixel(70)

      let option = {
        tooltip: {
          show: this.showTooltip,
          trigger: 'item',
          backgroundColor: 'rgb(255,255,255,0)',
          textStyle: {
            color: '#fff',
            decoration: 'none'
          },
          formatter: (value) => {
            if (this.terminalList && this.terminalList.length > 0) {
              for (let i = 0; i < this.terminalList.length; i++) {
                if (this.terminalList[i].areaId == value.data.adcode) {
                  let terminalInfo = this.terminalList[i]
                  if (value.componentSubType === 'scatter' || value.componentSubType === 'map') {
                    let hl=`<div class='yq-homepage-map-tip'>
                          <div class='tip-title'>${value.name}</div>
                          <div class='tip-con'>`
                    let hr=`</div></div>`
                    let hm=''
                    for (let k=0;k<terminalInfo.value.length;k++){
                      let item=terminalInfo.value[k]
                      if(item.display=='flex'){
                        hm+=`<div class='tip-item' style='display: ${item.display}'>
                               <div class='title'>
                                 <span>${item.title}:</span>
                               </div>
                               <div class='value'>${item.value+item.unit}</div>
                             </div>`
                      }
                    }
                    return hl+hm+hr
                  }
                  break
                }
              }
            }
          }
        },
        grid: {
          left: '5%', // 与容器左侧的距离
          right: '5%', // 与容器右侧的距离
          top: 0, // 与容器顶部的距离
          bottom: '5%' // 与容器底部的距离
        },
        geo: [
          {
            type: 'map',
            map: 'citymap',
            layoutCenter: this.layoutCenter1, //地图位置
            layoutSize: this.layoutSize,//大小
            data: this.series_0_data,
            animationDurationUpdate: 0,
            hoverAnimation: false,
            show:false,
            roam: false,
            silent: true,
            zoom: this.zoom,
            aspectScale: this.aspectScale,//地图的长宽比
            zlevel: 0,
            scaleLimit: {min: this.zoom,max: this.maxZoom},
            label: {
              normal: {
                show: false,
                textStyle: {
                  color: '#ffffff',
                  fontSize: w19,
                  fontWeight: 500,
                  textShadowColor: 'rgb(0,0,0)',
                  textShadowBlur: w6,
                  textShadowOffsetX: w3,
                  textShadowOffsetY: h5
                }
              },
              emphasis: {
                show: false,
                borderWidth: 1,
                borderColor: '#rgba(255, 209, 51, 1)',
                borderRadius: 2,
                backgroundColor: 'rgba(255, 209, 51, 1)',
                opacity: 1,
                padding: [h5, w14, h2, w14],
                textStyle: {
                  fontWeight: 500,
                  color: '#CA4411',
                  // fontSize: '120%'
                  fontSize: w19
                }
              }
            },
            itemStyle: {
              normal: {
                areaColor: {
                  type: 'linear',
                  x: 1200,
                  y: 0,
                  x2: 0,
                  y2: 1200,
                  colorStops: [{
                    offset: 0,
                    color: 'rgba(33,108,247,0.61)' // 0% 处的颜色
                  },
                    {
                      offset: 0.5,
                      color: 'rgba(0,86,255,0.2)' // 50% 处的颜色
                    },
                    {
                      offset: 1,
                      color: 'rgba(33,109,247,0.47)' // 50% 处的颜色
                    }],
                  global: true // 缺省为 false
                },
                borderColor: '#ffffff',
                borderWidth: 1,
                //shadowColor: 'rgba(135,182,252,0.14)',
                shadowColor: 'rgba(135,182,252,0.7)',
                /*shadowColor: 'rgba(255,255,255,0.4)',
                shadowOffsetX: 2 ,
                shadowOffsetY: 2 ,*/
                shadowBlur: w6,
                global: true // 缺省为 false
              },
              emphasis: {
                areaColor: 'rgba(2,20,58,0.4)',
                borderWidth: 2,
                borderColor: 'rgba(111,213,255,1)',
                shadowColor: 'rgba(111,213,255,0.7)',
                // shadowColor: 'rgba(111,213,255,0.25)',
                shadowBlur: w14
                //shadowOffsetY: h5,
              }
            }
          },
          {
            type: 'map',
            map: 'citymap',
            zlevel: -2,
            aspectScale: this.aspectScale,
            zoom: this.zoom,
            layoutCenter: this.layoutCenter2,
            layoutSize: this.layoutSize,
            hoverAnimation: false,
            animationDurationUpdate:0,
            roam: false,
            scaleLimit: {min: this.zoom,max: this.maxZoom},
            silent: true,
            itemStyle: {
              show: false,
              normal: {
                borderWidth: 1,
                //areaColor: 'rgba(46,67,109,0.15)',
                //borderColor: 'rgba(137,173,212,0.2)',

                /*shadowColor: 'rgba(65, 214, 255,1)',
                shadowOffsetY: 5,
                shadowBlur: 15,*/

                areaColor: 'rgba(46,67,109,0)',
                borderColor: 'rgba(137,173,212,0.2)',
                shadowColor: 'rgb(3,106,208)',
                shadowOffsetX: -w8,
                shadowOffsetY: h15,
                shadowBlur: w10
              },
              emphasis: {
                show: false
              }
            }
          },
          {
            type: 'map',
            map: 'citymap',
            zlevel: -3,
            aspectScale: this.aspectScale,
            zoom: this.zoom,
            layoutCenter: this.layoutCenter3,
            layoutSize: this.layoutSize,
            hoverAnimation: false,
            animationDurationUpdate:0,
            roam: false,
            scaleLimit: {min: this.zoom,max: this.maxZoom},
            silent: true,
            itemStyle: {
              normal: {
                borderWidth: 1,
                /* areaColor: 'rgba(46,67,109,0.15)',
                    borderColor: 'rgba(137,173,212,0.2)',
                    shadowColor: 'rgba(65, 214, 255,0.5)',
                    shadowOffsetY: 5,
                    shadowBlur: 15,*/
                areaColor: 'rgba(46,67,109,0)',
                borderColor: 'rgba(137,173,212,0.2)',
                shadowColor: 'rgb(4,86,166)',
                shadowOffsetX: -w10,
                shadowOffsetY: h20,
                shadowBlur: w10
              },
              emphasis: {
                show: false
              }
            }
          },
          {
            type: 'map',
            map: 'citymap',
            zlevel: -4,
            aspectScale: this.aspectScale,
            zoom: this.zoom,
            layoutCenter: this.layoutCenter4,
            layoutSize: this.layoutSize,
            hoverAnimation: false,
            animationDurationUpdate:0,
            roam: false,
            scaleLimit: {min: this.zoom,max: this.maxZoom},
            silent: true,
            itemStyle: {
              show: false,
              normal: {
                borderWidth: 1,
                // areaColor: 'rgba(46,67,109,0.25)',
                //borderColor: 'rgba(137,173,212,0.4)',
                // shadowColor: 'rgba(29, 111, 165,0.8)',
                areaColor: 'rgba(46,67,109,0)',
                borderColor: 'rgba(137,173,212,0.6)',
                shadowColor: 'rgb(3,65,126)',
                shadowOffsetX: -w10,
                shadowOffsetY: h25,
                shadowBlur: w10
              }
            }
          }
        ],

        series: [
          //地图
          {
            type: 'map',
            map: 'citymap',
            data: this.series_0_data,
            //data: this.scatterData,
            layoutCenter: this.layoutCenter1,
            layoutSize: this.layoutSize,
            //geoIndex: 0,
            roam: true,//是否开启鼠标缩放和平移漫游。false不开启、scale至缩放 、move只平移、true都开启
            zlevel: 0,//渲染图层，数字大显示最上面
            aspectScale: this.aspectScale,// scale地图的长宽比
            showLegendSymbol: false,
            animationDurationUpdate:0,
            hoverAnimation: true,
            zoom: this.zoom,//当前视角的缩放比例。
            //滚轮缩放的极限控制，通过min, max最小和最大的缩放值，默认的缩放为1
            scaleLimit: { min: this.zoom, max: this.maxZoom },
            //选中模式，表示是否支持多个选中，默认关闭，支持布尔值和字符串，字符串取值可选'single'表示单选，或者'multiple'表示多选。
            // selectedMode: 'single',
            //图形是否不响应和触发鼠标事件，默认为 false，即响应和触发鼠标事件。
            silent: false,
            label: {
              normal: {
                show: false
              },
              emphasis: {
                show: false
              }
            },
            animation: false,
            selectedMode:"single",
            markPoint: {
              symbol: 'none'
            }
          },
          //换色圆点
          {
            type: 'scatter',
            coordinateSystem: 'geo',
            zlevel: 1,
            data: this.series_1_data,
            silent: true,
            geoIndex: 0,
            symbol: 'circle',//标记的图形 circle
            showEffectOn: 'render',//配置何时显示特效。'render' 绘制完成后显示特效。'emphasis' 高亮（hover）的时候显示特效。
            hoverAnimation: false,
            animationDurationUpdate:0,
            label: {
              normal: {
                show: false
              },
              emphasis: {
                show: false
              }
            },
            itemStyle: {
              normal: {
                show: true,
                color: function(params) {
                  return new echarts.graphic.RadialGradient(
                    0.5, 0.5, 0.5, [
                      {
                        offset: 0,
                        color: 'rgb(255,242,0,0.5)'
                      },
                      {
                        offset: 1,
                        color: 'rgb(255,242,0,0)'
                      }
                    ])
                },
                shadowBlur: w70,
                shadowColor: 'rgb(255,242,0,1)',
                /* shadowOffsetX: 5,
                 shadowOffsetY: 5*/
                global: true
              },
              emphasis: {
                show: false
              }
            }
          },
          //黄色三角点
          {
            type: 'scatter',
            coordinateSystem: 'geo',
            geoIndex: 0,
            silent: true,
            zlevel: 2,
            data: this.series_2_data,
            symbol: 'triangle',//标记的图形
            showEffectOn: 'render',//配置何时显示特效。'render' 绘制完成后显示特效。'emphasis' 高亮（hover）的时候显示特效。
            symbolOffset: [0, -h8],//标记相对于原本位置的偏移
            // symbolOffset: [0, -10],//标记相对于原本位置的偏移
            symbolRotate: 180,
            animationDurationUpdate: 0,
            hoverAnimation: false,
            label: {
              normal: {
                show: false
              },
              emphasis: {
                show: false
              }
            },
            itemStyle: {
              normal: {
                show: true,
                color: function(params) {
                  return new echarts.graphic.LinearGradient(
                    0, 0, 0, 1, [
                      {
                        offset: 0,
                        color: 'rgb(244,204,87)'
                      },
                      {
                        offset: 1,
                        color: 'rgb(244,204,87,0.3)'
                      }
                    ]
                  )
                },
                global: true
              },
              emphasis: {
                show: false
              }
            }
          },
          //灰色三角
          {
            type: 'scatter',
            coordinateSystem: 'geo',
            silent: false,
            geoIndex: 0,
            zlevel: 3,
            symbol: 'triangle',//标记的图形
            symbolSize: [w30, h25],//标记的大小,可以是一个数字，也可是一个数组，标记的图形长宽[10,20]
            showEffectOn: 'render',//配置何时显示特效。'render' 绘制完成后显示特效。'emphasis' 高亮（hover）的时候显示特效。
            symbolOffset: [0, -h15],//标记相对于原本位置的偏移
            symbolRotate: 180,
            animationDurationUpdate: 0,
            hoverAnimation: false,
            label: {
              normal: {
                show: false
              },
              emphasis: {
                show: false
              }
            },
            data: this.series_3_data,
          },
          //label
          {
            type: 'scatter',
            coordinateSystem: 'geo',
            silent: false,
            geoIndex: 0,
            zlevel: 4,
            data: this.series_4_data,
            symbol: 'rect',//标记的图形
            symbolSize: (value, params) => {
              let w = params.name.length * w19
              return [w, h25]
            },//标记的大小,可以是一个数字，也可是一个数组，标记的图形长宽[10,20]
            showEffectOn: 'render',//配置何时显示特效。'render' 绘制完成后显示特效。'emphasis' 高亮（hover）的时候显示特效。
            symbolOffset: [0, -h35],//标记相对于原本位置的偏移
            symbolRotate: 180,
            animationDurationUpdate: 0,
            hoverAnimation: false,
            itemStyle: {
              normal: {
                show: true,
                color: 'rgba(255,0,0,0)',
                global: true
              },
              emphasis: {
                show: false
              }
            }
          },
          {
            name: 'cityMap',
            type: 'lines',
            zlevel: 4,
            symbol: ['none', 'none'], //['arrow','arrow]表示线两头用箭头显示
            symbolSize: w10, //箭头大小
            animationDurationUpdate:0,
            effect: {
              show: true,
              period: 6,
              trailLength: 0,
              symbol: 'arrow',
              symbolSize: w6
            },
            label: {
              normal: {
                show: true,
                position: 'left',
                formatter: '{b}'
              }
            },

            lineStyle: {
              normal: {
                color: 'rgba(239,227,145,0.64)',
                width: 1,
                opacity: 0.6,
                curveness: 0.2
              }
            },
            data: this.convertData
          }
        ]
      }
      this.myChart.setOption(option,true)

      window.addEventListener('resize', () => {
        this.myChart.resize()
      })

      let isMoveOrDrag=false
      //地理坐标系 geo 的缩放和平移漫游事件
      this.myChart.on('georoam', (params) => {
        //获得option对象
        var option = this.myChart.getOption()
        if (params.zoom != null && params.zoom != undefined) {
          //捕捉到缩放时
          for(let i=0;i<option.geo.length;i++){
            option.geo[i].zoom = option.series[0].zoom
            option.geo[i].center = option.series[0].center
          }
        } else {
          //捕捉到拖曳时
          isMoveOrDrag=true
          for(let i=0;i<option.geo.length;i++){
            option.geo[i].center = option.series[0].center
          }
        }
        this.myChart.setOption(option,true) //设置option
      })
      this.myChart.getZr().on('dragend', (params)=> {
          if (params.type === 'dragend') {
            isMoveOrDrag = false;
          }
      });

      //this.myChart.getZr().off('mousedown') //防止触发两次点击事件
      //zrender 事件与echarts 事件不同。前者是当鼠标在任何地方都会被触发，
      //on和getZr().on的区别为，前者是内容区的点击事件，即地图区域，后者为整个区域，包含地图和dom区域的空白部分区域。
      this.myChart.getZr().on('click', (params) => {
        if(!isMoveOrDrag){
          if (!params.target) {
            this.resetDataByMouseDownOperation('itemStyle', this.series_0_itemNormal, this.series_0_itemHover, this.series_0_data)
            this.resetDataByMouseDownOperation('symbolSize', this.series_1_symbolSizeNormal, this.series_1_symbolSizeHover, this.series_1_data)
            this.resetDataByMouseDownOperation('symbolSize', this.series_2_symbolSizeNormal, this.series_2_symbolSizeHover, this.series_2_data)
            this.resetDataByMouseDownOperation('itemStyle', this.series_3_itemNormal, this.series_3_itemHover, this.series_3_data)
            this.resetDataByMouseDownOperation('label', this.series_4_labelNormal, this.series_4_labelHover, this.series_4_data)
            this.setChartOption()
            this.$emit('changeMapADCode', '')
          }
        }
      })

      this.myChart.on('mousedown', (params) => {
        if(!isMoveOrDrag){
          let series_data_index = this.getScatterDataIndex(params.name)
          this.resetDataByMouseDownOperation('itemStyle', this.series_0_itemNormal, this.series_0_itemHover, this.series_0_data, series_data_index)
          this.resetDataByMouseDownOperation('symbolSize', this.series_1_symbolSizeNormal, this.series_1_symbolSizeHover, this.series_1_data, series_data_index)
          this.resetDataByMouseDownOperation('symbolSize', this.series_2_symbolSizeNormal, this.series_2_symbolSizeHover, this.series_2_data, series_data_index)
          this.resetDataByMouseDownOperation('itemStyle', this.series_3_itemNormal, this.series_3_itemHover, this.series_3_data, series_data_index)
          this.resetDataByMouseDownOperation('label', this.series_4_labelNormal, this.series_4_labelHover, this.series_4_data, series_data_index)
          this.setChartOption()
          this.$emit('changeMapADCode', params.data.adcode, params.name)
        }
      })
      this.myChart.on('mouseover', (params) => {
          if(!isMoveOrDrag) {
            let series_data_index = this.getScatterDataIndex(params.name)
            this.resetDataByMouseOver('itemStyle', this.series_0_itemNormal, this.series_0_itemHover, this.series_0_data, series_data_index)
            this.resetDataByMouseOver('symbolSize', this.series_1_symbolSizeNormal, this.series_1_symbolSizeHover, this.series_1_data, series_data_index)
            this.resetDataByMouseOver('symbolSize', this.series_2_symbolSizeNormal, this.series_2_symbolSizeHover, this.series_2_data, series_data_index)
            this.resetDataByMouseOver('itemStyle', this.series_3_itemNormal, this.series_3_itemHover, this.series_3_data, series_data_index)
            this.resetDataByMouseOver('label', this.series_4_labelNormal, this.series_4_labelHover, this.series_4_data, series_data_index)
            this.setChartOption()
          }
      })

      this.myChart.on('mouseout', (params) => {
          if(!isMoveOrDrag) {
            let series_data_index = this.getScatterDataIndex(params.name)
            this.resetDataByMouseOut('itemStyle', this.series_0_itemNormal, this.series_0_data, series_data_index)
            this.resetDataByMouseOut('symbolSize', this.series_1_symbolSizeNormal, this.series_1_data, series_data_index)
            this.resetDataByMouseOut('symbolSize', this.series_2_symbolSizeNormal, this.series_2_data, series_data_index)
            this.resetDataByMouseOut('itemStyle', this.series_3_itemNormal, this.series_3_data, series_data_index)
            this.resetDataByMouseOut('label', this.series_4_labelNormal, this.series_4_data, series_data_index)
            this.setChartOption()
          }
      })
    },
    /*移入、移出时，重新设置数据中series配置项*/
    resetDataByMouseOver(key, nVal, hVal, arr, index = -1) {
      for (let i = 0; i < arr.length; i++) {
        if (!arr[i].checked) {
          if (i === index) {
            arr[i][key] = hVal
            // break
          } else {
            arr[i][key] = nVal
          }
        }
      }
    },
    /*移入、移出时，重新设置数据中series配置项*/
    resetDataByMouseOut(key, value, arr, index = -1) {
      for (let i = 0; i < arr.length; i++) {
        if (!arr[i].checked && i === index) {
          arr[i][key] = value
          break
        }
      }
    },
    /*鼠标按下，重新设置数据中series配置项*/
    resetDataByMouseDownOperation(key, nVal, hVal, arr, index = -1) {
      for (let i = 0; i < arr.length; i++) {
        if (index >= 0) {
          if (i === index) {
            arr[i][key] = hVal
            arr[i].checked = true
          } else {
            arr[i][key] = nVal
            arr[i].checked = false
          }
        } else if (index === -1) {
          arr[i][key] = nVal
          arr[i].checked = false
        }
      }
    },
    setChartOption() {
      let op = this.myChart.getOption()
      op.series[0].data = this.series_0_data
      op.series[1].data = this.series_1_data
      op.series[2].data = this.series_2_data
      op.series[3].data = this.series_3_data
      op.series[4].data = this.series_4_data
      this.myChart.setOption(op, true)
    }
  }
}
</script>
<style lang='less'>
.yq-homepage-map-tip {
  padding-top: 0.25rem; //20/80
  width: 3.6375rem; //291/80
  /*height:2.7rem;// 216/80;*/
  height: calc(1.15rem + 0.325rem * var(--tipRowCount));//(24+20+24+24+26*count)

  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-image: url(/img/mapTipBG.png);

  //background-position: center;
  color: #fff;

  border:0.3rem solid;
  border-image-source:url(/img/mapTip.png);
  border-image-slice:30;

  .tip-title {
    // margin-top: 16px;
    font-size: 0.25rem; // 20/80
    height: 0.3rem;
    line-height: 0.3rem;
    text-align: center;
    font-weight: 600;
    letter-spacing: 1px;
  }

  .tip-con {
    .tip-item {
      margin-top: 0.125rem; // 10/80;
      height: 0.2rem;
      line-height: 0.2rem;

      .title {
        font-size: 0.175rem; //14/80;

        width: 50%;
        display: flex;
        flex-direction: row-reverse;
        letter-spacing: 0.025rem; // 2/80;

        span {
          display: inline-block;
          width: 1.125rem; //90/80
          color: #fff !important;
        }
      }

      .value {
        width: 50%;
        font-size: 0.2rem; //16/80
        font-weight: 600;
        font-family: DIN-Medium;
        color: #56c4f2 !important;
        letter-spacing: 1px;
      }
    }
  }
}
</style>
<style scoped lang='less'>
#home-echarts-map {
  height: 100%;
  //margin:0 0.7rem;
}
</style>