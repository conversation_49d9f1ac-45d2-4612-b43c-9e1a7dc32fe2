<template>
  <a-tooltip :placement="placement" overlayClassName="panel-tip-card" @visibleChange="visibleChange" >
    <template slot="title">
      <a-descriptions :column="1">
        <a-descriptions-item v-for="(item,idx) in showList" :key="idx" :label="item.name"> {{item.value}} </a-descriptions-item>
      </a-descriptions>
      <div class="detail-btn" @click="showDetail"> 查看详情</div>
    </template>
    <span class="panel-tip-node" :style="`left:${left}px;top:${top}px;width:${width}px;height:${height}px`"> </span>
  </a-tooltip>
</template>

<script>
export default {
  name: 'PanelToolTip',
  props: {
    left: {
      type: Number,
      default: 0,
      require: false,
    },
    top: {
      type: Number,
      default: 0,
      require: false,
    },
    width: {
      type: Number,
      default: 32,
      require: false,
    },
    height: {
      type: Number,
      default: 32,
      require: false,
    },
    portInfo: {
      type: Object,
      default: () => {
        return {}
      },
      require: false,
    },
  },
  data() {
    return {
        portStatus:{
            1:"连接",
            2:"关闭",
            3:"其他"
        }
    }
  },
  created() {
   
  },
  mounted() {},
  computed: {
    showList(){
      let arr = ["portDesc","ahaah","portStatus","alarmStatus","inSpeed","inputFlow","outputFlow"]
      let mapArr = arr.filter(el=>this.portInfo[el]).map(el=>{
        let info = this.portInfo[el]
        if(info){
          let value = info.value+info.unit
          if(el === "portStatus"){
            value = this.portStatus[info.value]
          }
          else if(el === "alarmStatus"){
            value = info.value === 1?"告警":"正常"
          }
           return {name:info.name,value:value}
        }
       
      })
      return mapArr
    },
    placement() {
      if (this.left < window.screen.width / 3) {
        if (this.top >= (window.innerHeight * 2) / 3) {
          return 'topLeft'
        } else if (this.top < window.innerHeight / 3) {
          return 'bottomLeft'
        } else {
          return 'right'
        }
      } else if (this.left > (window.screen.width * 2) / 3) {
        if (this.top >= (window.innerHeight * 2) / 3) {
          return 'topRight'
        } else if (this.top < window.innerHeight / 3) {
          return 'bottomRight'
        } else {
          return 'left'
        }
      } else {
        if (this.top >= window.innerHeight / 2) {
          return 'top'
        } else {
          return 'bottom'
        }
      }
    },
  },
  methods: {
    showDetail(e){
      this.$emit("hide")
      this.$emit("showDetail",this.portInfo)
    },
    visibleChange(e){
      if(!e){
        this.$emit("hide")
      }
    }
  },
}
</script>
<style lang="less">
.panel-tip-card {
  max-width: unset;
  .ant-descriptions-view{
    width: auto;
    table{
      width:auto
    }
    .ant-descriptions-item-label{
        color:#fff;
    }
    .ant-descriptions-item-content{
         color:#fff;
    }
  }
  .detail-btn{
    text-align:center;
    margin-bottom: 8px;
    cursor: pointer;
  }
}
</style>
<style lang="less" scoped>
.panel-tip-node {
  position: fixed;
  z-index: 10000;
  display: block;
  width: 32px;
  height: 32px;
  background: transparent;
}
</style>