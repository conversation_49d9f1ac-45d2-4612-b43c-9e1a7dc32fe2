<template>
  <a-row :gutter='16' style=' margin: -10px;height: 100%;' class='vScroll zhl'>
    <a-col :md='5' :sm='8' :xs='10'>
      <div style='height: 100%; border: 1px solid #E8E8E8; border-radius: 4px;'>
        <device-tree-expand
          ref='deviceTree'
          :treeUrl="'/assetscategory/assetsCategory/selectAssetsCategoryTree'"
          :params='params'
          :btnName="'全部资产'"
          @selected='treeSelectedSearch'
          :inputFlag="true"
          :is-show-all-btn='true'
          :is-show-btn-icon='false'
          :is-show-icon='false'
          style='height: 100%'
        ></device-tree-expand>
      </div>
    </a-col>
    <a-col :md='19' :sm='16' :xs='14' style='height: 100%;'>
      <a-card :bordered='false' style='height: 100%;border:1px solid #E8E8E8; border-radius: 4px;'>
        <!-- 查询区域 -->
        <div class='table-page-search-wrapper'>
          <a-form layout='inline' @keyup.enter.native='searchQuery'>
            <a-row :gutter='24' ref='row'>
              <a-col :span='spanValue'>
                <a-form-item label='资产名称'>
                  <a-input :maxLength='maxLength' placeholder='请输入' v-model='queryParam.assetsName' :allowClear='true' autocomplete='off'/>
                </a-form-item>
              </a-col>
<!--              <a-col :span='spanValue'>
                <a-form-item label='资产类型'>
                  <j-tree-select-expand
                    placeholder='请选择资产类型'
                    v-model='queryParam.assetsCategoryId'
                    dict='cmdb_assets_category,category_name,id'
                    condition='{"delflag":0}'
                    pidField='parent_id'
                    pidValue='0'
                    @change='changeSelected($event)'
                  />
                </a-form-item>
              </a-col>-->
              <a-col :span='spanValue'>
                <a-form-item label='厂商名称'>
                  <a-input :maxLength='maxLength' placeholder='请输入' v-model='queryParam.producerName' :allowClear='true' autocomplete='off'/>
                </a-form-item>
              </a-col>
              <a-col :span='colBtnsSpan()'>
                <span class='table-page-search-submitButtons'
                      :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button type="primary" class='btn-search btn-search-style' @click='doSearch'>查询</a-button>
                  <a-button class='btn-reset btn-reset-style' @click='doReset'>重置</a-button>
                 <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
        <!-- 查询区域-END -->

        <!-- table区域-begin -->
        <div>
          <a-table
            ref='table'
            bordered
            rowKey='id'
            :columns='columns'
            :dataSource='dataSource'
            :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
            :pagination='ipagination'
            :loading='loading'
            @change='handleTableChange'
          >
            <template slot='htmlSlot' slot-scope='text'>
              <div v-html='text'></div>
            </template>
            <template slot='imgSlot' slot-scope='text'>
              <span v-if='!text' style='font-size: 14px; '>无图片</span>
              <img v-else :src='getImgView(text)' height='25px' alt='' style='max-width:80px;font-size: 14px; ' />
            </template>
            <span slot='action' slot-scope='text, record' class='caozuo'>
              <a @click='selectData(record)' style='color:red;' v-if='record.selected'>已关联</a>
              <a @click='selectData(record)' style='color:#1890FF;' v-else>关联</a>
            </span>
          </a-table>
        </div>
      </a-card>
    </a-col>
  </a-row>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import JSuperQuery from '@/components/jeecg/JSuperQuery.vue'
import { queryAssetsCategoryTreeList } from '@/api/device'
import { httpAction, getAction } from '@/api/manage'
//引入公共devicetree组件
import DeviceTreeExpand from '@/components/tree/DeviceTreeExpand.vue'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'

export default {
  name: 'AssetsInfoList',
  mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
  components: {
    JSuperQuery,
    getAction,
    'device-tree-expand': DeviceTreeExpand
  },
  data() {
    return {
      maxLength:50,
      //查询条件，此参数名称与JeecgListMixin模块参数一致
      queryParam: {
        //查询参数
        assetsName: '',
        producerName: '',
        categoryId: ''
      },
      description: '设备表管理页面',
      // 树
      assetsCategoryTree: [],
      treeData: [],
      dropTrigger: '',
      selectedKeys: [],
      checkedKeys: [],
      checkStrictly: true,
      iExpandedKeys: [],
      autoExpandParent: true,
      currFlowId: '',
      currFlowName: '',
      rightClickSelectedBean: {},
      // 表头
      columns: [
        {
          title: '资产编号',
          dataIndex: 'assetsCode',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 100px'
            return { style: cellStyle }
          },
        },
        {
          title: '资产名称',
          dataIndex: 'assetsName',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 100px'
            return { style: cellStyle }
          },
        },
        {
          title: '资产类型',
          dataIndex: 'assetsCategory',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 100px'
            return { style: cellStyle }
          },
        },
        {
          title: '厂商名称',
          dataIndex: 'producerName',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 100px'
            return { style: cellStyle }
          },
        },
        {
          title: '型号',
          dataIndex: 'assetsModel',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 80px'
            return { style: cellStyle }
          },
        },
        {
          title: '所属人',
          dataIndex: 'owner',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 90px'
            return { style: cellStyle }
          },
        },
        {
          title: '所属部门',
          dataIndex: 'departmentText',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 100px'
            return { style: cellStyle }
          },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 80,
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: '/device/deviceInfo/selectAssetsByCondition',
        delete: '/device/deviceInfo/delete',
        deleteBatch: '/device/deviceInfo/deleteBatchDevice',
        exportXlsUrl: '/device/deviceInfo/exportXls',
        importExcelUrl: 'device/deviceInfo/importExcel'
      },
      // dataSource:[],//table数据
      dictOptions: {},
      statuslist: [
        {
          name: '正常',
          code: 'normal'
        },
        {
          name: '异常',
          code: 'unnormal'
        }
      ],
      selectAssets: {},
      params: { i: 'categoryId' }
    }
  },
  computed: {
    importExcelUrl: function() {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    }
  },
  methods: {
    changeSelected(e) {
      this.queryParam.assetsCategoryId = e
    },
    selectData(record) {
      this.selectAssets = record
      const newData = [...this.dataSource]
      newData.forEach(ele => {
        if (record.id !== ele.id) {
          ele.selected = false
        } else {
          ele.selected = true
        }
      })
      this.dataSource = newData
    },
    //表单查询,点击查询按钮，默认查询第一页
    doSearch() {
      this.loadData(1)
    },
    //重置
    doReset() {
      //重置form表单，不重置tree选中节点
      if (this.queryParam.assetsCategoryId){
        let id=this.queryParam.assetsCategoryId
        this.queryParam={};
        this.queryParam.assetsCategoryId=id
      }
      else{
        this.queryParam = {}
      }
      this.loadData(1)
    },
    treeSelectedSearch(selectedKey = '') {
      this.queryParam.assetsCategoryId = selectedKey
      this.loadData(1)
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
</style>
