<template>
  <a-row :gutter='10' style='height: 100%;' class='vScroll'>
    <a-col style='width:100%;height: 100%;display: flex;flex-direction: column'>
      <!-- 查询区域 -->
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0', marginRight: '12px' }" class='card-style'
              style='width: 100%'>
        <div class='table-page-search-wrapper'>
          <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
            <a-row :gutter='24' ref='row'>
              <a-col :span='spanValue'>
                <a-form-item label='任务标识'>
                  <a-input :maxLength='maxLength' placeholder='请输入任务标识' v-model='queryParam.code' :allowClear='true'
                           autocomplete='off' />
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label='任务类名'>
                  <a-input :maxLength='maxLength' placeholder='请输入任务类名' v-model='queryParam.value' :allowClear='true'
                           autocomplete='off' />
                </a-form-item>
              </a-col>
<!--               <a-col :span='spanValue'>
                <a-form-item label='启用状态'>
                  <j-dict-select-tag v-model='queryParam.enable' placeholder='请选择启用状态'
                                     dictCode='device_enable_status' />
                </a-form-item>
              </a-col>-->
              <a-col :span='colBtnsSpan()'>
                <span class='table-page-search-submitButtons'
                          :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button type='primary' class='btn-search btn-search-style' @click='searchQuery'>查询</a-button>
                  <a-button class='btn-reset btn-reset-style' @click='searchReset'>重置</a-button>
                  <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                        {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <div class='table-operator table-operator-style'>
          <a-button type='primary' @click="handleAdd">新增</a-button>
          <a-dropdown v-if="selectedRowKeys.length > 0">
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item key="1" @click="batchDel">删除</a-menu-item>
            </a-menu>
            <a-button> 批量操作 <a-icon type="down"/></a-button>
          </a-dropdown>
        </div>
        <a-table
          ref='table'
          bordered
          :row-key='(record,index)=>{return record.id}'
          :columns='columns'
          :dataSource='dataSource'
          :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
          :pagination='ipagination'
          :loading='loading'
          :rowSelection='{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }'
          @change='handleTableChange'>
          <span slot='action' class='caozuo' slot-scope='text, record'>
            <a @click='handleDetailPage(record)'>查看</a>
             <a-divider type='vertical' />
               <a-dropdown>
                  <a class='ant-dropdown-link'>更多
                    <a-icon type='down' /></a>
                  <a-menu slot='overlay'>
                    <a-menu-item>
                        <a class='overlay' @click='handleEdit(record)'>编辑</a>
                    </a-menu-item>
                    <a-menu-item>
                      <a @click='confirmDelete(record.id)' class='overlay'>删除</a>
                    </a-menu-item>
<!--                    <a-menu-item>
                      <a class='overlay' v-if='record.status == 1' >禁用</a>
                      <a class='overlay' v-else>启用</a>
                    </a-menu-item>-->
                  </a-menu>
                </a-dropdown>
          </span>
          <template slot='tooltip' slot-scope='text'>
            <a-tooltip placement='topLeft' :title='text' trigger='hover'>
              <div class='tooltip'>
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
      </a-card>
    </a-col>
   <adapter-task-modal ref='modalForm' @ok='modalFormOk'></adapter-task-modal>
  </a-row>
</template>

<script>
import '@assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { getAction } from '@api/manage'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
import AdapterTaskModal from '@views/deviceConfig/adapterTaskManagement/modules/AdapterTaskModal.vue'
export default {
  name: 'AdapterTaskList',
  mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
  components: {
    AdapterTaskModal
  },
  data() {
    return {
      maxLength:50,
      description: '适配器页面',
      formItemLayout: {
        labelCol: { style: 'width:80px' },
        wrapperCol: {
          style: 'width:calc(100% - 80px)'
        }
      },
      // 表头
      columns: [
        {
          title: '任务标识',
          dataIndex: 'code'
        },
        {
          title: '任务类名',
          dataIndex: 'value',
          scopedSlots: { customRender: 'tooltip' },
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 120px;max-width:400px'
            return { style: cellStyle }
          }
        },
        {
          title: '描述',
          dataIndex: 'description',
          scopedSlots: { customRender: 'tooltip' },
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 120px;max-width:260px'
            return { style: cellStyle }
          }
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 140,
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: '/device/productJob/list',
        delete: '/device/productJob/delete',
        deleteBatch: '/device/productJob/deleteBatch'
      },
      disableMixinCreated: true
    }
  },
  activated() {
    this.loadData()
  },
  methods: {}
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';

.overlay {
  color: #409eff
}
</style>