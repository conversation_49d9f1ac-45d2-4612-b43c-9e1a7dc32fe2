<template>
  <div v-if='targetObj'>
    <div style='height: 20px'></div>
    <a-form-model ref='modelForm' :label-col="{span:5}"  :model='userData' :rules='rules'  :wrapper-col="{span:19}" labelAlign='left'>
      <a-form-model-item label="名称"   prop='name'>
        <a-input placeholder='请填写名称' v-model='userData.name' @change='userDataChange("name")' />
      </a-form-model-item>
      <a-form-model-item label='容量' v-if='isJigui'    prop='unit'>
        <a-input type='number' :disabled='hasDevices' placeholder='请填写容量' v-model='userData.unit' @change='userDataChange("unit")' suffix="U"  />
      </a-form-model-item>
      <a-form-model-item label='规格' v-if='isJigui'    prop='specifications'>
        <a-input  placeholder='请填写长宽高 (例:1*1*1)' suffix="mm" v-model='userData.specifications' @change='userDataChange("specifications")' />
      </a-form-model-item>
      <a-form-model-item label='品牌' v-if='isJigui'   prop='brand'>
        <a-input  placeholder='请填写品牌' v-model='userData.brand'  @change='userDataChange("brand")'/>
      </a-form-model-item>
      <a-form-model-item label='设备' v-if='isEMS'   prop='deviceCode'>
        <a-tree-select
          v-model="userData.deviceCode"
          allowClear
          style="width: 100%"
          :tree-data="treeData"
          show-search
          treeNodeFilterProp="title"
          placeholder='请选择设备'
          :replaceFields="{ title: 'name', key: 'id', value: 'code' }"
          :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }"
          search-placeholder="输入关键字"
          @change='userDataChange("deviceCode")'
        />
      </a-form-model-item>
    <div class='' v-for='(item, k) in modelForm1' :key='k'>
      <div>
        <div v-if='needsKey(k)' style='text-align: left'>{{ k | labelName }}:</div>
        <div v-for='(v, subk) in item' :key='subk'>
          <a-form-model-item v-if='needsKey(k)' :label=" k === 'scale' ? getLabelText(subk) : subk" required>
            <a-input-number
              style='width: 100%'
              v-model.number='modelForm1[k][subk]'
              :min="k === 'scale' ? 0 : -10000"
              :max='10000'
              :step='1'
              precision:2
              @change='infoChange(k, subk)'
            />
          </a-form-model-item>
        </div>
      </div>
    </div>
    <!-- <a-button v-if="targetObj.userData && targetObj.userData.addDoor" block style="margin-bottom: 20px" @click="addDoor">
      添加门窗
    </a-button> -->
    <a-button block type='danger' style='margin-bottom: 20px' @click='delGeo'> 删除</a-button>
    <a-button block type='primary' style='margin-bottom: 20px' @click='goBack'> 确定</a-button>
    </a-form-model>
  </div>
</template>
<script>
import { mapMutations } from 'vuex'
import { putAction, getAction, deleteAction } from '@/api/manage'
import modelJson from '../threeUtils/model.json'
let validatePass = (rule, value, callback) => {
  if (value <1) {
    callback(new Error('最小值为1'));
  }else{
    callback();
  }

};
export default {
  props: ['modelObj'],
  data() {
    return {
      targetName: '',
      targetWidth: 0,
      targetHeight: 0,
      targetDepth: 0,
      targetObj: null,
      outMeshObj: null,
      isJigui: false,
      userData:{
        name:"",
        unit: 1,
        brand: '',
        specifications: '',
        deviceCode: undefined,
      },
      hasDevices: false,
      modelForm1: {
        position: { x: 0, y: 0, z: 0 },
        rotation: { x: 0, y: 0, z: 0 },
        scale: { x: 0, y: 0, z: 0 }
      },
      rules:{
        name: [
          { required: true, message: '请填写名称', trigger: 'change' },
          { min: 1, message: '名称长度应在 1-50 之间', trigger: 'change' },
          { max: 50, message: '名称长度应在 1-50 之间', trigger: 'change' },
          {validator: this.validateName, trigger: 'change'}
        ],
        unit:[{ required: true, message: '请填写容量', trigger: 'change' },{validator: validatePass, trigger: 'change'}],
        specifications:[{ required: true, message: '请填写规格', trigger: 'change' }, {
         pattern: /^[1-9]\d*\*[1-9]\d*\*[1-9]\d*$/,message: '请输入正确格式，例：1*1*1', trigger: 'change'
        }],
        brand:[
          { required: true, message: '请填写品牌', trigger: 'change' },
          { min: 1, message: '名称长度应在 1-100 之间', trigger: 'change' },
          { max: 100, message: '名称长度应在 1-100 之间', trigger: 'change' },
        ],
      },
      treeData:[],

    }
  },
  created() {
    this.getDeviceTree()
    this.$root.$off('update-model-info')
    this.$root.$on('update-model-info', () => {
      if (this.targetObj) {
        this.showModelInfo(this.targetObj)
      }
    })
  },
  mounted() {
  },
  filters: {
    labelName(e) {
      let msg = ''
      switch (e) {
        case 'position':
          msg = '平移'
          break
        case 'scale':
          msg = '缩放'
          break
        case 'rotation':
          msg = '旋转'
          break
      }
      return msg
    }
  },
  computed: {
    isEMS() {
      if(this.targetObj){
        let model = modelJson.find((el) =>el.type === this.targetObj.name);
        if(model){
          return model.EMS
        }
        return false;
      }
      return false;
    }
  },
  methods: {
    //获取设备树
    checkTree(e) {
      e.forEach(el=>{
        if(!el.code){
          el.code = el.id;
          el.disabled = true;
        }
        if(el.children && el.children.length > 0){
          this.checkTree(el.children)
        }
      })
    },
    getDeviceTree(){
      getAction("/topo/device/tree").then((res) => {
        if (res.success) {
          this.checkTree(res.result)
          this.treeData = res.result
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    ...mapMutations({
      modelGroup: 'threejs/MODELGROUP'
    }),
    getLabelText(subk) {
      return { x: '长(x)', y: '高(y)', z: '宽(z)' }[subk]
    },
    needsKey(k) {
      if (k === 'scale' && this.isJigui) {
        return false
      }
      return true
    },
    addDoor() {
      let modelObj = {
        type: 'wall-door',
        create: 'createResultBsp'
      }
      this.$root.$emit('addTest', modelObj, this.targetObj)
    },
    init(e) {

      if (this.targetObj) {
        this.targetObj = null
        this.$nextTick(() => {
          this.setTarget(e)
        })
      } else {
        this.setTarget(e)
      }
    },
    setTarget(e) {
      this.targetObj = e
      if (e.name === 'yq_outerMesh') {
        this.targetObj = scene.children.find((el) => {
          return el.uuid ===  e.groupId
        })
      }
      // console.log('模型信息 === ', this.targetObj)
      this.userData.name = this.targetObj.userData.name
      if (this.targetObj.name === 'yq_group_jigui') {
        this.outMeshObj = e
        this.modelGroup(true)
        this.isJigui = true
        this.userData.unit = this.targetObj.userData.unit || 12
        this.userData.brand = this.targetObj.userData.brand || "";
        this.userData.specifications = this.targetObj.userData.specifications || "";
        this.hasDevices = false
        if (this.targetObj.userData.id) {
          getAction('/topo/cabinet/info', { id: this.targetObj.userData.id }).then((res) => {
            if (res.success) {
              if (res.result && res.result.devices.length > 0) {
                this.hasDevices = true
              }
            }
          })
        }
      }
      else if(this.targetObj.userData.glb){
        this.outMeshObj = e
        this.modelGroup(false)
        this.isJigui = false;
        this.userData.deviceCode = this.targetObj.userData?.deviceCode
      }
      else {
        this.outMeshObj = null
        this.modelGroup(false)
        this.isJigui = false;
        this.userData.deviceCode = this.targetObj.userData?.deviceCode
      }
      if (!this.targetObj.userData.name) {
        this.targetObj.userData.name = this.targetObj.name
      }
      this.showModelInfo(this.targetObj)
    },
    showModelInfo(emodel) {
      let keys = ['position', 'scale', 'rotation']
      let subKeys = ['x', 'y', 'z']
      keys.forEach((key) => {
        if (emodel[key]) {
          subKeys.forEach((subkey) => {
            if (key === 'rotation') {
              this.modelForm1[key][subkey] = this.dubouleFloat((emodel[key][subkey] / Math.PI) * 180)
            } else {
              this.modelForm1[key][subkey] = this.dubouleFloat(emodel[key][subkey])
            }
          })
        }
      })
    },
    dubouleFloat(num) {
      var result = parseFloat(num)
      if (isNaN(result)) {
        this.$message.warning('传递参数错误，请检查！')
        return 0
      }
      result = Math.round(num * 100) / 100
      return result
    },
    goBack() {
      this.$refs.modelForm.validate(valid => {
        if (valid) {
          this.$parent.goBack()
        } else {
          console.log('error submit!!');
          return false;
        }
      });
      // this.targetObj.userData.brand = this.brand;
      // this.targetObj.userData.specifications = this.specifications;

    },
    numFormatter(v) {
      return Number(v).toFixed(2)
    },
    userDataChange(key){
      if(key === "name"){
        let jiguis = scene.children.filter(el=>el.name === "yq_group_jigui"&&el.uuid!==this.targetObj.uuid)
        let tem = jiguis.find(el=>el.userData.name === this.userData.name)
        if(tem){
          this.$message.warning("机柜名称不能重复")
        }
      }
      if(key === "unit"){
        this.$root.$emit('unitChange', this.targetObj, this.userData)
      }else{
        this.targetObj.userData[key] = this.userData[key]
      }

    },
    // unitChange() {
    //
    // },
    // specChange(){
    //   this.targetObj.userData.specifications = this.specifications;
    // },
    // brandChange(){
    //   this.targetObj.userData.brand = this.brand;
    // },
    infoChange(key, subkey) {
      if (!this.targetObj) return
      if (key === 'rotation') {
        this.targetObj[key][subkey] = (this.modelForm1[key][subkey] / 180) * Math.PI
        if (this.outMeshObj) {
          this.outMeshObj[key][subkey] = (this.modelForm1[key][subkey] / 180) * Math.PI
        }
      } else {
        this.targetObj[key][subkey] = Math.floor(this.modelForm1[key][subkey] * 100) / 100
        if (this.outMeshObj) {
          this.outMeshObj[key][subkey] = Math.floor(this.modelForm1[key][subkey] * 100) / 100
        }
      }
      this.$root.$emit('modelInfoChange')
    },
    validateName(rule, value, callback) {
      let jiguis = scene.children.filter(el=>el.name === "yq_group_jigui"&&el.uuid!==this.targetObj.uuid)
      let tem = jiguis.find(el=>el.userData.name === value)
      if(tem){
        callback(new Error('机柜名称不能重复'));
      }
      else {
        callback();
      }
    },
    delGeo() {
      this.$parent.delGeo()
    }
  }
}
</script>
<style scoped>
.input-item {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}
</style>