<template>
  <a-card :bordered='false'>
    <div class='action'>
      <span class='edit'>
        详情信息
      </span>
      <span class='return'>
        <img src='~@/assets/return1.png' alt='' @click='getGo'>
      </span>
    </div>
    <a-descriptions :column='{ xxl: 2, xl: 2, lg: 2, md: 2, sm: 2, xs: 2 }' bordered>
      <a-descriptions-item label='下级单位名称' v-if='data.childDepartName'>{{ data.childDepartName }}</a-descriptions-item>
      <a-descriptions-item label='下级单位标识' v-if='data.childDepartCode'>{{ data.childDepartCode }}</a-descriptions-item>
      <a-descriptions-item label='下级平台标识' v-if='data.childPlatformCode'>{{data.childPlatformCode}}</a-descriptions-item>
      <a-descriptions-item label='下级单位所在地' v-if='data.childLocation'>{{data.childLocation}}</a-descriptions-item>
      <a-descriptions-item label='推送记录总数' v-if='data.pushCount != null'>{{data.pushCount}}</a-descriptions-item>
      <a-descriptions-item label='推送记录成功数' v-if='data.pushSuccessCount != null'>{{data.pushSuccessCount}}
      </a-descriptions-item>
    </a-descriptions>
  </a-card>
</template>

<script>
  export default {
    name: 'data',
    data() {
      return {}
    },
    props: {
      data: {
        type: Object,
        required: false,
        default: () => {
          return {}
        }
      }
    },
    watch: {
      data: {
        handler(val) {
          this.data = val
        },
        deep: true,
        immediate: true
      }
    },
    mounted() {
      console.log(this.data, 'data');

    },
    methods: {
      //返回上一级
      getGo() {
        this.$parent.pButton2(0)
      }
    }
  }
</script>

<style scoped lang='less'>
  .action {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-flow: row nowrap;
    margin-bottom: 12px;

    .edit {
      margin-left: 10px;

      .icon {
        color: #409eff;
        margin-right: 6px
      }
    }

    .return {
      img {
        width: 20px;
        height: 20px;
        cursor: pointer
      }
    }
  }

  ::v-deep .ant-descriptions-view {
    border-radius: 0px;
  }

  ::v-deep .ant-descriptions-bordered .ant-descriptions-item-label {
    background-color: rgb(250, 250, 250);
    text-align: center;
    width: 17%;
  }

  ::v-deep .ant-descriptions-item-label,
  .ant-descriptions-item-content {
    color: rgb(96, 98, 102) !important;
  }

  ::v-deep .ant-descriptions-bordered .ant-descriptions-item-content {
    word-break: break-word;
    white-space: normal;
  }
</style>