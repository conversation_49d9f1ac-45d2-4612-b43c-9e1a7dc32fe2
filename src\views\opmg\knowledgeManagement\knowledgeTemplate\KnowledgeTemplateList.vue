<template>
  <div style="height:100%">
    <keep-alive exclude='templateInfo'>
      <component style="height:100%" :is="pageName" :data="data"/>
    </keep-alive>
  </div>
</template>
<script>
import templateList from './templateList.vue'
import templateInfo from './templateInfo.vue'
export default {
  name: "KnowledgeTemplateList",
  data() {
    return {
      isActive: 0,
      data: {},
    };
  },
  components: {
    templateInfo,
    templateList
  },
  created() {
    this.pButton1(0);
  },
  //使用计算属性
  computed: {
    pageName() {
      switch (this.isActive) {
        case 0:
          return "templateList";
        default:
          return "templateInfo";
      }
    }
  },
  methods: {
    pButton1(index) {
      this.isActive = index;
    },
    pButton2(index, item) {
      this.isActive = index;
      this.data = item
    }
  }
}
</script>