<template>
  <div class="cla-card">
    <!--    <div  style='border:1px solid #FFFFFF;background-color: red ; margin-top:20px'>
      <a-button @click="refresh" type="default" icon="reload" :loading="loading">刷新</a-button>
    </div>-->

    <!--   <div v-if='assetsCategoryTree.length<=0' @click="handleAddType">添加根节点</div>-->

    <div class="hScroll vScroll" style="flex: auto; margin: 20px 14px 14px 14px">
      <div v-if='assetsCategoryTree.length<=0'  style='cursor: pointer' @click="handleAddType" >
        <div style='padding:0 2px;background: #409eff;margin-right:3px;border-radius: 2px;display: inline-block'>
          <a-icon type='plus' style='color: #ffffff'></a-icon>
        </div >
        <span style='font-size: 14px'>添加根节点</span>
      </div>
      <!-- <a-input-search @change="onChange" v-model="inputSearch" style="width:100%;margin-top: 10px" placeholder="请输入类型名称" /> -->
      <template>
        <a-dropdown :trigger="[this.dropTrigger]"  @visibleChange="dropStatus">
          <div style="user-select: none">
            <a-tree
              @select="onSelect"
              @rightClick="rightHandle"
              :selectedKeys="selectedKeys"
              :checkedKeys="checkedKeys"
              :treeData="assetsCategoryTree"
              :loadData="asyncLoadTreeData"
              :replace-fields="replaceFields"
              :checkStrictly="checkStrictly"
              :expandedKeys="expandedKeys"
              :autoExpandParent="autoExpandParent"
              @expand="onExpand"
            >
              <template slot="title" slot-scope="{ name }">
                <span :title="name">{{ name }}</span>
              </template>
            </a-tree>
          </div>
          <!--新增右键点击事件,和增加添加和删除功能-->
          <a-menu slot="overlay" >
            <a-menu-item v-if="rightHandleNode&&rightHandleNode.type === 'city'" @click="handleAddType" key="1">增加下级节点</a-menu-item>
            <a-menu-item v-if="rightHandleNode&&(rightHandleNode.type === 'room' || rightHandleNode.type === 'city')" @click="handleEditType"
            >编辑节点</a-menu-item>
            <a-menu-item
              v-if=" rightHandleNode &&(rightHandleNode.type === 'room' || rightHandleNode.type === 'city')"
              @click="confirmDelete"
              key="2">删除节点</a-menu-item>
            <!--            <a-menu-item
                          v-if=" rightHandleNode&&rightHandleNode.pid &&(rightHandleNode.type === 'room' || rightHandleNode.type === 'city')"
                          @click="confirmDelete"
                          key="2">删除节点</a-menu-item>-->
            <a-menu-item v-if="rightHandleNode&&(rightHandleNode.type === 'city' || rightHandleNode.type === 'room')" @click="closeDrop" key="3">取消</a-menu-item>
          </a-menu>
        </a-dropdown>
      </template>
    </div>
    <tree-node-modal :node-type='assetsCategoryTree.length<=0?"city":""' ref="modalTypeForm" @refresh="refresh"></tree-node-modal>
  </div>
</template>
<script>
import { queryPostionTreeList } from '@/api/device'
import TreeNodeModal from './modules/TreeNodeModal'
import { deleteAction, getAction } from '@/api/manage'

//通过Key获取对应地title
const getTitleByKey = (key, tree) => {
  let selectTitle
  for (let i = 0; i < tree.length; i++) {
    const node = tree[i]
    if (node.key === key) {
      selectTitle = node.title
      break
    }
    if (node.children) {
      selectTitle = getTitleByKey(key, node.children)
    }
  }
  return selectTitle
}
//生成tree节点的数组[{ key, title: node.title }]
// const dataList = []
// var firstRoom = ''

//为tree生成对应地title slot
const generateSlotScopes = (data) => {
  for (let i = 0; i < data.length; i++) {
    // var info = data[i];
    data[i].scopedSlots = { title: 'title' }
    if (data[i].children) {
      generateSlotScopes(data[i].children)
    }
  }
}
export default {
  name: 'PostionTree',
  props: {
    viewFlag: {
      type: Number,
      default: 2,
      required: false,
    },
  },
  data() {
    return {
      firstTitle: '', //存储搜素tree的第一个title
      inputSearch: '',
      // 树
      assetsCategoryTree: [],
      treeData: [],
      expandedKeys: [],
      searchValue: '',
      autoExpandParent: true,
      dropTrigger: '',
      selectedKeys: [],
      selectedTitle: '',
      checkedKeys: [],
      checkStrictly: true,
      replaceFields: {
        key: 'id',
        title: 'name',
        type: 'type',
      },
      rightHandleNode: '',
      nodeName: '',
      url: {
        delete: '/topo/room/delete',
        asyncUrl: '/topo/room/cabinet',
      },
      dataList: [],
      firstRoom: '',
      roomFlag: false,
      selectedNodeId: '',
      selectedNodeType: '',
      selectedNodeName: '',
      selectedNode:null,
      rightMenuVisible:false
    }
  },
  components: {
    'tree-node-modal': TreeNodeModal,
  },
  created() {
    this.loadTree()
  },
  methods: {
    asyncLoadTreeData(treeNode) {
      return new Promise((resolve) => {
        if (treeNode.$vnode.children) {
          resolve()
          return
        }
        let key = treeNode.$vnode.key
        this.getType(key, 'room', this.assetsCategoryTree)
        if (!this.roomFlag) {
          resolve()
          return
        }
        let param = {
          roomId: key,
          viewFlag: this.viewFlag, //3D：3  拓扑 2
        }
        getAction(this.url.asyncUrl, param).then((res) => {
          if (res.success) {
            this.generateIsLeafValue(res.result)
            this.addChildren(key, res.result, this.assetsCategoryTree)
            this.assetsCategoryTree = [...this.assetsCategoryTree]
          }
          resolve()
        })
      })
    },
    //机房编辑完之后的数据更新
    reloadTree() {
      // return new Promise(resolve => {
      this.getType(this.selectedNodeId, 'room', this.assetsCategoryTree)
      if (!this.roomFlag) {
        // resolve()
        return
      }
      let param = {
        roomId: this.selectedNodeId,
        viewFlag: this.viewFlag,
      }
      getAction(this.url.asyncUrl, param).then((res) => {
        if (res.success) {
          this.generateIsLeafValue(res.result)
          this.addChildren(this.selectedNodeId, res.result, this.assetsCategoryTree)
          this.assetsCategoryTree = [...this.assetsCategoryTree]
        }
        this.$emit('selected', this.selectedNodeType, this.selectedNodeId, this.selectedNodeName,this.selectedNode)
      })
      // })
    },
    //根据节点id查询后台更新tree
    reloadTreeByNodeid(key) {
      // return new Promise(resolve => {
      this.getType(key, 'room', this.assetsCategoryTree)
      if (!this.roomFlag) {
        // resolve()
        return
      }
      let param = {
        roomId: key,
        viewFlag: this.viewFlag,
      }
      getAction(this.url.asyncUrl, param).then((res) => {
        if (res.success) {
          this.generateIsLeafValue(res.result)
          this.addChildren(key, res.result, this.assetsCategoryTree)
          this.assetsCategoryTree = [...this.assetsCategoryTree]
        }
      })
      // })
    },
    //懒加载获取的数据添加到相应节点的children属性上
    addChildren(pid, children, treeArray) {
      if (treeArray && treeArray.length > 0) {
        for (let item of treeArray) {
          if (item.id == pid) {
            if (!children || children.length == 0) {
              item.isLeaf = true
              item.children = children
            } else {
              item.isLeaf = false
              item.children = children
            }
            break
          } else {
            this.addChildren(pid, children, item.children)
          }
        }
      }
    },
    //判断节点是否存在子节点来判断是否为Leaf节点
    generateIsLeafValue(treeArray) {
      if (treeArray && treeArray.length > 0) {
        for (let item of treeArray) {
          if (item.children && item.children.length > 0) {
            item.isLeaf = false
            this.generateIsLeafValue(item.children)
          } else {
            item.isLeaf = true
          }
        }
      }
    },
    //判断节点类型
    getType(nodeId, type, treeArray) {
      if (treeArray && treeArray.length > 0) {
        for (let item of treeArray) {
          if (item.id == nodeId) {
            if (item.type === type) {
              this.roomFlag = true
            } else {
              this.roomFlag = false
            }
            break
          } else {
            this.getType(nodeId, type, item.children)
          }
        }
      }
    },
    // 查询树
    async loadTree() {
      var that = this
      that.treeData = []
      that.assetsCategoryTree = []
      await queryPostionTreeList().then((res) => {
        if (res.success) {
          // 部门全选后，再添加部门，选中数量增多
          this.allTreeKeys = []
          if (res.result&&res.result.length > 0) {
            generateSlotScopes(res.result)
            that.assetsCategoryTree = [...res.result]
          }
          this.loading = false
          this.generateList(that.assetsCategoryTree)
        }
      })
    },
    generateList(data) {
      for (let i = 0; i < data.length; i++) {
        const node = data[i]
        const key = node.id
        if (this.firstRoom === '' && node.type === 'room') {
          this.firstRoom = node.id
          this.selectedKeys = [node.id]
          this.getParentKey(node.id, this.assetsCategoryTree)
          this.selectedNodeType = node.type
          this.selectedNodeName = node.name
          this.selectedNodeId = node.id
          this.selectedNode = node
          this.$emit('selected', node.type, node.id, node.name,node)
        }
        this.dataList.push({ key, title: node.name })
        if (node.children) {
          this.generateList(node.children)
        }
      }
    },
    //子节点匹配，获取父节点，用于展开tree
    getParentKey(key, tree) {
      let parentKey
      for (let i = 0; i < tree.length; i++) {
        const node = tree[i]
        if (node.children) {
          if (node.children.some((item) => item.id === key)) {
            this.expandedKeys.push(node.id)
          } else if (this.getParentKey(key, node.children)) {
            parentKey = this.getParentKey(key, node.children)
            this.expandedKeys.push(parentKey)
          }
        }
      }
      return parentKey
    },
    //获取机柜的父级机房,因为机房的下级数据都是通过机房id懒加载获取到的，并查询后台更新数据
    // getCabinetParentKey(key, tree) {
    //   for (let i = 0; i < tree.length; i++) {
    //     const node = tree[i]
    //     if (node.children) {
    //       if (node.children.some(item => item.id === key)) {
    //         this.reloadTreeByNodeid(node.id)
    //         break
    //       } else if (this.getCabinetParentKey(key, node.children)) {
    //         parentKey = this.getCabinetParentKey(key, node.children)
    //       }
    //     }
    //   }
    // },
    //机柜编辑完之后的获取机房id，查询后台数据更新
    // reloadTreeAfterCabinet() {
    //   this.getCabinetParentKey(this.selectedNodeId, this.assetsCategoryTree)
    // },
    //暂时废弃
    onSearch(value) {
      let that = this
      if (value) {
        searchByConfigItemtype({ typeName: value }).then((res) => {
          if (res.success) {
            that.assetsCategoryTree = []
            for (let i = 0; i < res.result.length; i++) {
              let temp = res.result[i]
              that.assetsCategoryTree.push(temp)
            }
          } else {
            that.$message.warning(res.message)
          }
        })
      } else {
        that.loadTree()
      }
    },
    // 右键点击下拉框改变事件
    dropStatus(visible) {
      if (visible == false) {
        this.dropTrigger = ''
        this.rightHandleNode=null
      }
    },
    // 选择树的方法
    onSelect(selectedKeys, e) {
      this.selectedKeys = selectedKeys
      this.firstTitle = ''
      if (e.selectedNodes.length < 1) {
        return
      }
      const nodeData = e.selectedNodes[0].data.props.dataRef
      this.selectedNodeType = nodeData.type
      this.selectedNodeId = nodeData.id
      this.selectedNodeName = nodeData.name
      this.selectedNode = nodeData
      //向父组件弹射抛值
      // if(nodeData.pid){
      this.$emit('selected', this.selectedNodeType, this.selectedNodeId, this.selectedNodeName,this.selectedNode)
      // }
    },
    //tree的查询框输入时，默认选中匹配的第一个（用firstTitle表示）
    onChange(e) {
      const value = e.target.value
      this.searchValue = value
      //查询框第一个匹配的node对应的key
      let firstSearchedKey = ''
      const expandedKeys = dataList
        .map((item) => {
          if (item.title.indexOf(value) > -1) {
            //查询框第一个匹配的node对应的key
            if (firstSearchedKey == '') {
              firstSearchedKey = item.key
            }
            return getParentKey(item.key, this.assetsCategoryTree)
          }
          return null
        })
        .filter((item, i, self) => item && self.indexOf(item) === i)
      Object.assign(this, {
        expandedKeys,
        searchValue: value,
        autoExpandParent: true,
      })
      if (this.expandedKeys.length > 0 && value.trim().length > 0) {
        this.firstTitle = getTitleByKey(firstSearchedKey, this.assetsCategoryTree)
        this.selectedKeys = [firstSearchedKey]
        //向父组件弹射抛值
        this.$emit('selected', firstSearchedKey)
      }
      if (value.trim().length == 0) {
        //查询设备信息,此情况下，没有分类被选中
        this.firstTitle = ''
        this.selectedKeys = []
        //向父组件弹射抛值
        this.$emit('selected')
      }
    },
    onCheck(checkedKeys, e) {
      this.hiding = false
      // this.checkedKeys = checkedKeys.checked
      // <!---- author:os_chengtgen -- date:20190827 --  for:切换父子勾选模式 =======------>
      if (this.checkStrictly) {
        this.checkedKeys = checkedKeys.checked
      } else {
        this.checkedKeys = checkedKeys
      }
      // <!---- author:os_chengtgen -- date:20190827 --  for:切换父子勾选模式 =======------>
    },
    // 右键操作方法
    rightHandle(node) {
      this.dropTrigger = 'contextmenu'
      this.rightHandleNode = node.node.dataRef
      this.nodeName = node.node.dataRef.name
      this.rightClickSelectedKey = node.node.eventKey
      this.rightClickSelectedBean = node.node.dataRef
    },
    //树的添加类型
    handleAddType() {
      this.$refs.modalTypeForm.add(this.rightClickSelectedKey, this.assetsCategoryTree)
      this.$refs.modalTypeForm.title = '新增'
    },
    //树的编辑类型
    handleEditType() {
      this.$refs.modalTypeForm.edit(this.rightClickSelectedBean, this.assetsCategoryTree)
      this.$refs.modalTypeForm.title = '编辑'
    },
    async confirmDelete(){
      let node=this.rightHandleNode
      if(node){
        let tip="是否删除该节点数据?"
        if(node.type==="room"){
          tip='机房数据会同时被删除，是否删除该节点?'
        }
        else if(node.type=="city"&&node.children&&node.children.length>0){
          tip='子节点数据会同时被删除，是否删除该节点?'
        }
        let _this=this
        this.$confirm({
          title: '确认删除',
          okText: '是',
          cancelText: '否',
          content: tip,
          onOk: function () {
            _this.deleteType();
          }
        })
      }
    },

    //树room节点的删除类型
    async deleteType() {
      await deleteAction(this.url.delete, { id: this.rightClickSelectedKey }).then((res) => {
        if (res.success) {
          this.$message.success(res.message)
          this.$emit("delOk")
          this.afterDelChangeData(this.rightClickSelectedKey, this.assetsCategoryTree)
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    //节点删除成功后，前端数据代码删除，不从后台同步
    afterDelChangeData(key, tree) {
      for (let i = 0; i < tree.length; i++) {
        const node = tree[i]
        if (node.id === key) {
          tree.splice(i, 1)
          break
        }
        if (node.children) {
          this.afterDelChangeData(key, node.children)
        }
      }
    },
    // 右键店家下拉关闭下拉框
    closeDrop() {
      this.dropTrigger = ''
      this.rightHandleNode=null
    },
    refresh(args) {
      //节点新增
      if (args && args.method === 'post') {
        this.afterAddChangeData(this.rightClickSelectedKey, this.assetsCategoryTree, {
          pid:this.rightHandleNode?this.rightHandleNode.id:'',
          ...args,
          children: [],
        })
      }
      //节点修改
      if (args && args.method === 'put') {
        this.afterEditChangeData(this.rightClickSelectedKey, this.assetsCategoryTree, {
          pid:this.rightHandleNode.pid,
          ...args
        })
        if(this.rightHandleNode.id === this.selectedNode.id && this.selectedNodeType === 'room'){
          this.$emit("changeInfo",args)
        }
      }
      this.loading = true
      // this.loadTree()
    },
    //节点添加成功后，前端数据代码添加，不从后台同步
    afterAddChangeData(key, tree, nodeInfo) {
      if(tree.length<=0){
        tree.push(nodeInfo)
        return
      }
      for (let i = 0; i < tree.length; i++) {
        const node = tree[i]
        if (node.id === key) {
          if(node.children==undefined||node.children==null||node.children==''){
            node.children=[]
          }
          node.children.push(nodeInfo)
          break
        }
        if (node.children&&node.children.length>0) {
          this.afterAddChangeData(key, node.children, nodeInfo)
        }
      }
    },
    //节点编辑成功后，前端数据代码添加，不从后台同步
    afterEditChangeData(key, tree, nodeInfo) {
      for (let i = 0; i < tree.length; i++) {
        const node = tree[i]
        if (node.id === key) {
          tree[i] = Object.assign(node, nodeInfo)
          break
        }
        if (node.children) {
          this.afterEditChangeData(key, node.children, nodeInfo)
        }
      }
    },
    onExpand(expandedKeys) {
      // if not set autoExpandParent to false, if children expanded, parent can not collapse.
      // or, you can remove all expanded children keys.
      this.expandedKeys = expandedKeys
      this.autoExpandParent = false
    },
  },
}
</script>
<style lang="less" scoped>
@import '~@assets/less/scroll.less';
.cla-card {
  height: 100% !important;
  background-color: #ffffff;
  border-radius: 3px;
  display: flex;
  flex-direction: column;
}
/*::v-deep .ant-card-body {
  padding: 10px;
}*/
::v-deep span.ant-tree-title {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
  //width: 1.25rem /* 100/80 */;
}
.cus-menu{
  background: #ffffff !important;
  font-size: 14px !important;
  border: 1px solid #e8e6e6;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 15%);
  padding-top: 0 !important;
  .ant-menu-item{
    font-size: 14px !important;
    color:rgba(0, 0, 0, 0.65) !important;

    line-height: 24px;
    height:24px;
    margin: 6px 0;
  }
  .ant-menu-item:hover{
    background: #f5e1e1 !important;
    color:rgba(0, 0, 0, 0.65) !important;
  }
}

</style>