<template>
  <div style="height: 100%;width: 100%" id="chartCom"></div>
</template>

<script>
  import {
    getAction
  } from '@/api/manage'
  export default {
    props: {
      url: {
        type: String,
        default: ''
      },
      size: {
        type: String,
        default: '40%,70%'
      },
      rose: {
        type: Boolean,
        default: false
      },
    },
    mounted() {
      this.getChartData()
    },
    methods: {
      getChartData() {
        getAction(this.url).then((res) => {
          if (res.success) {
            let radiusSize = this.size.split(',')
            this.chartCom(res.result, radiusSize)
          }
        })
      },
      chartCom(data, size) {
        for (let i = 0; i < data.length; i++) {
          data[i].value = data[i].values[0].value
        }
        let myChart = this.$echarts.init(document.getElementById('chartCom'))
        myChart.setOption({
          tooltip: {
            show: true,
            trigger: 'item',
            transitionDuration: 0, //echart防止tooltip的抖动
          },
          legend: {
            top: '34%',
            right: '2%',
            orient: 'vertical',
            textStyle: {
              color: '#fff',
            },
            formatter: function (name) {
              // 获取legend显示内容
              let total = 0
              let tarValue = 0
              for (let i = 0, l = data.length; i < l; i++) {
                total += data[i].value
                if (data[i].name == name) {
                  tarValue = data[i].value
                }
              }
              let p = ((tarValue / total) * 100).toFixed(2)
              return name + ' ' + ' ' + p + data[0].values[1].unit
            },
          },
          color: ['#058eee', '#ffba13', '#dd3f2a'],
          series: [{
            type: 'pie',
            radius: size,
            roseType: this.rose ? 'area' : '',
            avoidLabelOverlap: false,
            hoverAnimation: false,
            label: {
              show: false,
              position: 'center',
            },
            labelLine: {
              show: false,
            },
            center: ['40%', '50%'],
            data: data,
          }, ],
        })
        window.addEventListener('resize', () => {
          myChart.resize()
        })
      }
    },
  }
</script>

<style>

</style>