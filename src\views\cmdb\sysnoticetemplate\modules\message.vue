<template>
  <div>
    <a-col :span='23'>
      <a-form-model-item label='短信模板编码' prop='subject' :rules='validatorRules.subject'>
        <a-input v-model='personalizedObject.subject' :allowClear='true' autocomplete='off' placeholder='请输入短信模板编码' @change='changeValue'/>
      </a-form-model-item>
    </a-col>
    <a-col :span='23'>
      <a-form-model-item class='two-words' label='短信签名' prop='signName' :rules='validatorRules.signName'>
        <a-input v-model='personalizedObject.signName' :allowClear='true' autocomplete='off' placeholder='请输入签名' @change='changeValue'/>
      </a-form-model-item>
    </a-col>
    <a-col :span='23'>
      <a-form-model-item class='two-words' label='收信人' prop='sendTo' :rules='validatorRules.sendTo'>
        <a-select v-model='personalizedObject.sendTo' :allowClear='true' :getPopupContainer='(node) => node.parentNode'
                  mode='multiple' option-filter-prop='children'
                  placeholder='请选择收信人' show-search @change='changeValue'>
          <a-select-option v-for='item in usersList' :key='item.id' :value='item.id'>
            {{ item.realName }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
    </a-col>
  </div>
</template>

<script>
export default {
  name: 'message',
  props: {
    data: {
      type: Object,
      required: false,
      default: () => {
        let personalizedObject = {
          subject: '',
          signName:'',
          sendTo: undefined
        }
        return personalizedObject
      }
    },
    usersList: {
      type: Array,
      required: true,
      default: []
    }
  },
  data() {
    return {
      personalizedObject: {
        subject: '',
        signName:'',
        sendTo: undefined
      },
      validatorRules: {
        subject: [
          { required: true, validator: this.subjectValidate }
        ],
        signName: [
          { required: false,validator: this.signNameValidate }
        ],
        sendTo: [
          { required: true, validator: this.sendToValidate }
        ]
      }
    }
  },
  watch: {
    data: {
      handler(val) {
        if (Object.keys(val).length > 0) {
          this.personalizedObject = val
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    changeValue() {
      this.$emit('changeModelValue', this.personalizedObject)
    },
    getTips(fullField) {
      let str = ''
      switch (fullField) {
        case 'subject':
          str = '请输入短信模板编码！'
          break
        case 'signName' :
          str = '请输入短信签名！'
          break
        case 'sendTo' :
          str = '请选择收信人！'
          break
      }
      return str
    },

    subjectValidate(rule, value, callback) {
      if (rule.required) {
        if (value && value.length > 0) {
          if(value.length>30||value.length<2){
            callback('短信模板编码长度应在 2-30 之间！')
          }else{
            callback()
          }
        } else {
          callback(this.getTips(rule.fullField))
        }
      } else {
        callback()
      }
    },
    signNameValidate(rule, value, callback) {
        if (value&&value.length>30) {
            callback('短信签名长度应在 0-30 之间！')
        } else {
          callback()
        }
    },
    sendToValidate(rule, value, callback) {
      if (rule.required) {
        if (value && value.length > 0) {
          callback()
        } else {
          callback(this.getTips(rule.fullField))
        }
      } else {
        callback()
      }
    }
  }
}
</script>

<style scoped>

</style>