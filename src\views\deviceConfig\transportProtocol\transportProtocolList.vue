<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <!-- 查询区域 -->
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="协议名称">
                  <a-input :maxLength='maxLength' placeholder="请输入协议名称" :allowClear="true" autocomplete="off" v-model="queryParam.name">
                  </a-input>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="协议标识">
                  <a-input :maxLength='maxLength' placeholder="请输入协议标识" :allowClear="true" autocomplete="off" v-model="queryParam.code">
                  </a-input>
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label="协议类型" prop="type">
                  <a-select v-model="queryParam.type" placeholder="请选择" :allowClear="true" style="width: 100%">
                    <a-select-option :value="0">推模式</a-select-option>
                    <a-select-option :value="1">拉模式</a-select-option>
                    <a-select-option :value="2">其他</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="colBtnsSpan()">
                <span class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button type="primary" @click="searchQuery" class="btn-search-style">查询</a-button>
                  <a-button @click="searchReset" style="margin-left: 10px" class="btn-reset-style">重置</a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <!-- 查询区域-END -->

      <!-- 操作按钮区域 -->
      <a-card :bordered="false" style="flex: auto">
        <div class="table-operator">
          <a-button type='primary' @click="handleAdd">新增</a-button>
          <a-dropdown v-if="selectedRowKeys.length > 0">
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item key="1" @click="batchDel">删除 </a-menu-item>
            </a-menu>
            <a-button>
              批量操作
              <a-icon type="down" />
            </a-button>
          </a-dropdown>
        </div>

        <!-- table区域-begin -->
        <div>
          <a-table
            ref='table'
            bordered
            :row-key='(record,index)=>{return record.id}'
            :columns='columns'
            :dataSource='dataSource'
            :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
            :pagination='ipagination'
            :loading='loading'
            :rowSelection='{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }'
            @change='handleTableChange'>
            <template slot="type" slot-scope="text">
              <span v-if="text && text >= 0">{{ typeList[text] }}</span>
            </template>
            <template slot="htmlSlot" slot-scope="text">
              <div v-html="text"></div>
            </template>
            <template slot="imgSlot" slot-scope="text">
              <span v-if="!text" style="font-size: 14px; font-style: italic">无图片</span>
              <img v-else :src="getImgView(text)" height="25px" alt=""
                style="max-width: 80px; font-size: 14px; font-style: italic" />
            </template>
            <span slot="action" slot-scope="text, record">
              <span>
                <a @click='handleDetailPage(record)'>查看</a>
              </span>
              <span>
                <a-divider type="vertical" />
                <a @click="handleEdit(record)">编辑</a>
              </span>
              <span>
                <a-divider type="vertical" />
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </span>
            </span>
            <template slot="tooltip" slot-scope="text">
              <a-tooltip placement="topLeft" :title="text" trigger="hover">
                <div class="tooltip">
                  {{ text }}
                </div>
              </a-tooltip>
            </template>
          </a-table>
        </div>
        <transport-protocol-modal ref="modalForm" @ok="modalFormOk"> </transport-protocol-modal>
      </a-card>
    </a-col>
  </a-row>
</template>

<script>
  import {
    getAction,
    deleteAction
  } from '@/api/manage'
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import transportProtocolModal from './modules/transportProtocolModal'
  import {
    YqFormSearchLocation
  } from '@/mixins/YqFormSearchLocation'

  export default {
    name: 'transportProtocolList',
    mixins: [JeecgListMixin, YqFormSearchLocation],
    components: {
      transportProtocolModal,
    },
    data() {
      return {
        maxLength:50,
        formItemLayout: {
          labelCol: {
            style: 'width:80px',
          },
          wrapperCol: {
            style: 'width:calc(100% - 80px)'
          }
        },
        description: '传输协议管理页面',
        // 表头
        columns: [{
            title: '协议标识',
            dataIndex: 'code',
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 100px;max-width:300px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '协议名称',
            dataIndex: 'name',
            scopedSlots: {
              customRender: 'tooltip'
            },
            customCell: () => {
              let cellStyle = 'text-align: left;min-width: 100px;max-width:300px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '协议类型',
            dataIndex: 'type',
             scopedSlots: {
              customRender: 'type'
            },
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 100px;max-width:150px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '描述',
            dataIndex: 'description',
            scopedSlots: {
              customRender: 'tooltip'
            },
            customCell: () => {
              let cellStyle = 'text-align: left;min-width: 100px;max-width:150px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '操作',
            dataIndex: 'action',
            fixed: 'right',
            align: 'center',
            width: 160,
            scopedSlots: {
              customRender: 'action'
            },
          },
        ],
        url: {
          list: '/device/productTransfer/list',
          delete: '/device/productTransfer/delete',
          deleteBatch: '/device/productTransfer/deleteBatch',
        },
        typeList: ['推模式', '拉模式', '其他'],
        disableMixinCreated: true
      }
    },
    activated() {
      this.loadData()
    }
  }
</script>
<style lang="less" scoped>
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';
</style>