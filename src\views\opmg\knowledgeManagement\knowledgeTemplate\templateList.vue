<template>
  <a-row :gutter='10' style='height: 100%' class='vScroll' >
    <a-col style='width:100%;height: 100%;display: flex;flex-direction: column'>
      <!-- 查询区域 -->
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0', marginRight: '12px' }" class='card-style'
        style='width: 100%'>
        <div class='table-page-search-wrapper'>
          <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
            <a-row :gutter='24' ref='row'>
              <a-col :span='spanValue'>
                <a-form-item label='模板名称'>
                  <a-input placeholder='请输入模板名称' v-model='queryParam.templateName' :allowClear='true'
                    autocomplete='off' :maxLength="maxLength"/>
                </a-form-item>
              </a-col>
              <a-col :span='colBtnsSpan()'>
                <span class='table-page-search-submitButtons'
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button type='primary' class='btn-search btn-search-style' @click='searchQuery'>查询</a-button>
                  <a-button class='btn-reset btn-reset-style' @click='searchReset'>重置</a-button>
                  <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <div class='table-operator table-operator-style'>
          <a-button @click='handleAdd' v-has="'muban:add'">新增</a-button>
          <a-dropdown v-if='selectedRowKeys.length > 0'>
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item v-has="'muban:delete'" key='1' @click='batchDel'>删除</a-menu-item>
            </a-menu>
            <a-button> 批量操作
              <a-icon type='down' />
            </a-button>
          </a-dropdown>
        </div>
        <a-table ref='table' bordered :row-key='(record,index)=>{return record.id}' :columns='columns'
          :dataSource='dataSource' :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}" :pagination='ipagination'
          :loading='loading' :rowSelection='{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }'
          @change='handleTableChange'>

          <template slot='tooltip' slot-scope='text'>
            <a-tooltip placement='topLeft' :title='text' trigger='hover'>
              <div class='tooltip'>
                {{ text }}
              </div>
            </a-tooltip>
          </template>

          <span slot='action' class='caozuo' slot-scope='text, record'>
            <a @click='handleDetailPage(record)'>查看</a>
            <a-divider type='vertical' v-has="'muban:edit'" />
            <a @click='handleEdit(record)' class='overlay' v-has="'muban:edit'">编辑</a>
            <a-divider type='vertical' v-has="'muban:delete'" />
            <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
              <a v-has="'muban:delete'">删除</a>
            </a-popconfirm>
          </span>
        </a-table>
      </a-card>
    </a-col>
    <add-template-modal ref='modalForm' @ok='modalFormOk'></add-template-modal>
  </a-row>
</template>

<script>
  import '@assets/less/TableExpand.less'
  import {JeecgListMixin} from '@/mixins/JeecgListMixin'
  import addTemplateModal from '@views/opmg/knowledgeManagement/knowledgeTemplate/modules/AddTemplateModal.vue'
  import {YqFormSearchLocation} from '@/mixins/YqFormSearchLocation'
  export default {
    name: 'templateList',
    mixins: [JeecgListMixin, YqFormSearchLocation],
    components: {
      addTemplateModal
    },
    data() {
      return {
        maxLength:50,
        description: '知识模板管理列表页面',
        formItemLayout: {
          labelCol: {
            style: 'width:80px'
          },
          wrapperCol: {
            style: 'width:calc(100% - 80px)'
          }
        },
        // 表头
        columns: [
          {
            title: '模板名称',
            dataIndex: 'templateName',
            customCell: () => {
              let cellStyle = 'text-align: center'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '描述',
            dataIndex: 'description',
            customCell: () => {
              let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
              return {
                style: cellStyle
              }
            },
            scopedSlots: {
              customRender: 'tooltip'
            }
          },
          {
            title: '创建时间',
            dataIndex: 'createTime',
            customCell: () => {
              let cellStyle = 'text-align: center;width:200px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '最近更新时间',
            dataIndex: 'updateTime',
            customCell: () => {
              let cellStyle = 'text-align: center;width:200px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            fixed: 'right',
            width: 140,
            scopedSlots: {
              customRender: 'action'
            }
          }
        ],
        url: {
          list: '/kbase/knowledges/template/list',
          delete: '/kbase/knowledges/template/delete',
          deleteBatch: '/kbase/knowledges/template/deleteBatch'
        },
        disableMixinCreated:true
      }
    },
    activated() {
      this.loadData()
    },
  }
</script>
<style lang='less' scoped>
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';

  .stateBox {
    margin-left: 20px;
  }

  .stateImg {
    vertical-align: middle
  }

  .alarmStatus {
    margin-left: 8px;
  }

  .overlay {
    color: #409eff
  }
</style>