<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    switchFullscreen
    @ok="handleOk"
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @cancel="handleCancel"
    cancelText="关闭">
    <devops-arrangement-allocate-form ref="DevopsArrangementAllocateForm" @ok="submitCallback" :disabled="disableSubmit"></devops-arrangement-allocate-form>
  </j-modal>
</template>

<script>

  import DevopsArrangementAllocateForm from './DevopsArrangementAllocateForm'
  export default {
    name: 'DevopsArrangementInfoModal',
    components: {
      DevopsArrangementAllocateForm
    },
    data () {
      return {
        title:'',
        width:896,
        visible: false,
        disableSubmit: false
      }
    },
    methods: {
      add () {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.DevopsArrangementAllocateForm.add();
        })
      },
      edit (ids) {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.DevopsArrangementAllocateForm.edit(ids);
        })
      },
      close () {
        this.$emit('close');
        this.visible = false;
      },
      handleOk () {
        this.$refs.DevopsArrangementAllocateForm.submitForm();
      },
      submitCallback(){
        this.$emit('ok');
        this.visible = false;
      },
      handleCancel () {
        this.close()
      }
    }
  }
</script>