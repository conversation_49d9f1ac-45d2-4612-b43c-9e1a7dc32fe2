<template>
  <div class="net-content">
    <div class="big-screen-div">
      <vis-edit ref="bigScreen" operate="show" topoBgByTheme></vis-edit>
    </div>
    <div class="topo-list" ref="netTopo">
      <div v-for="(item, index) in nettopoList" :key="item.id" class="list-div">
        <div
          :class="['img-div', selectIndex === index ? 'select-img' : '']"
          :style="{ height: hoverHeight }"
          v-if="item.topoSvg"
          v-html="item.topoSvg"
          @click="showNetTopo(item, index)"
        ></div>
        <div
          :class="['img-div', selectIndex === index ? 'select-img' : '']"
          :style="{ height: hoverHeight }"
          v-if="!item.topoSvg"
          @click="showNetTopo(item, index)"
        >
          <div class="ant-empty ant-empty-normal">
            <div class="ant-empty-image">
              <svg width="64" height="41" viewBox="0 0 64 41" xmlns="http://www.w3.org/2000/svg">
                <g transform="translate(0 1)" fill="none" fill-rule="evenodd">
                  <ellipse fill="#F5F5F5" cx="32" cy="33" rx="32" ry="7" />
                  <g fill-rule="nonzero" stroke="#D9D9D9">
                    <path
                      d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"
                    />
                    <path
                      d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z"
                      fill="#FAFAFA"
                    />
                  </g>
                </g>
              </svg>
            </div>
            <p class="ant-empty-description" style="color: white !important">暂无数据</p>
          </div>
        </div>
        <span :class="{ 'select-span': selectIndex === index }">{{ item.name }}</span>
      </div>
      <div v-show="netLoading">加载中...</div>
    </div>
  </div>
</template>
<script>
import { getAction, postAction, putAction, deleteAction } from '@/api/manage'
import VisEdit from '@/views/topo/nettopo/modules/VisEdit'
export default {
  components: {
    VisEdit,
  },
  data() {
    return {
      nettopoList: [],
      hoverHeight: '0px',
      netPageNo: 1,
      netPapeTotalNo: 0,
      netLoading: false,
      url: {
        list: '/topo/topoInfo/list',
      },
      selectIndex: 0,
    }
  },
  created() {
    this.getNetTopoList()
  },
  mounted() {
    //监听如果页面发生滚动时
    this.$nextTick(() => {
      this.$refs.netTopo.addEventListener('scroll', this.netHandleScroll, true)
    })
  },
  beforeDestroy() {
    // this.$refs.bigScreen.disposeGraph()
  },
  destroyed() {},
  methods: {
    getNetTopoList() {
      getAction(this.url.list, { topoType: '0', showType: '1', pageNo: this.netPageNo, pageSize: 10 }).then((res) => {
        if (res.success) {
          this.netPageNo += 1
          this.netPapeTotalNo = Math.ceil(res.result.pageList.total / 10)
          this.nettopoList = [...this.nettopoList, ...res.result.pageList.records]
          this.netLoading = false
          if (this.nettopoList.length > 0) {
            setTimeout(() => {
              this.hoverHeight = ([...document.getElementsByClassName('img-div')][0].clientWidth / 5) * 3 + 'px'
            }, 50)
            // this.$nextTick(() => {
            if (this.netPageNo == 2) {
              this.$refs.bigScreen.createTopo(this.nettopoList[0].id)
            }
            // })
          }
        }
      })
    },
    async showNetTopo(record, index) {
      await this.$refs.bigScreen.clearStatusInterval()
      await this.$refs.bigScreen.createTopo(record.id)
      this.selectIndex = index
    },
    //网络拓扑页面滑到底部需要调用的方法
    netHandleScroll() {
      //下面这部分兼容手机端和PC端
      var scrollTop = this.$refs.netTopo.scrollTop //滚动条的位置
      var windowHeitht = this.$refs.netTopo.clientHeight //在页面上返回内容的可视高度
      var scrollHeight = this.$refs.netTopo.scrollHeight //返回整个元素的高度（包括带滚动条的隐蔽的地方）
      //是否滚动到底部的判断
      if (Math.round(scrollTop) + windowHeitht == scrollHeight) {
        if (this.netPageNo <= this.netPapeTotalNo) {
          this.netLoading = true
          this.getNetTopoList()
        }
      }
    },
  },
}
</script>
<style lang="less" scoped>
.viewCenter {
  color: #fff;
  height: 100%;
  width: 100%;
}
.title-container {
  display: flex;
  justify-content: center;
  height: 5%;
}
.title {
  width: 100px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 15px;
  color: white;
}
.content-container {
  height: 94%;
  width: 100%;
  background: #222224;
  padding: 0 16px;
}
.net-content {
  position: relative;
  // width: 22.375rem /* 1790/80 */;
  width: 100%;
  height: 100%;
  background-color: #111217;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.big-screen-div {
  width: 86%;
  height: 100%;
}
.topo-list {
  width: 14%;
  height: 96%;
  padding-right: 16px;
  overflow-y: auto;
}
.list-div {
  text-align: center;
  cursor: pointer;
  margin-bottom: 10px;
  span {
    font-size: 14px;
    color: #4e4f53;
  }
}
.select-span {
  color: #aaaaac !important;
}
.img-div {
  width: 100%;
  height: 100%;
  border: 1px solid #4e4f53;
  position: relative;
}
::v-deep .img-div > svg {
  width: 100% !important;
  height: 100% !important;
}
::v-deep .img-div > svg > g {
  transform: matrix(1, 0, 0, 1, 1, 1) !important;
}
.select-img {
  border: 1px solid #aaaaac;
}
.room-content {
  width: 100%;
  height: 100%;
}
.threeD-content,
.twoD-content {
  width: 100%;
  height: 100%;
}
.threeD-content {
  position: relative;
}
.room-select {
  position: absolute;
  top: 30px;
  left: 60px;
  width: 185px;
}
.room-title-p {
  color: #64e0ea;
  font-size: 27px;
  margin-bottom: 7px;
}
.room-title-p-english {
  color: #dd7a1f;
  font-size: 10px;
  margin-bottom: 10px;
}
.room-position {
  color: #63d7de;
  font-size: 12px;
}
.title-div1 {
  border: none;
  height: 1px;
  background-color: #38747c;
}
.title-div2 {
  border: none;
  height: 2px;
  width: 80%;
  background-color: #4ca5ad;
  margin-top: 0.05rem; /* 4/80 */
}
.position-bottom-div {
  height: 2px;
  width: 55%;
  background-color: #39747c;
}
.borderBottom {
  border-bottom: 3px solid #0585aa;
}
::v-deep .container canvas {
  height: 100% !important;
  width: 100% !important;
}

::v-deep .ant-select {
  color: #63d7de !important;
  font-size: 27px;
  .ant-select-selection {
    background-color: #101117 !important;
    border: 1px solid #101117 !important;
    box-shadow: none !important;
  }
}
::v-deep .ant-select-selection:hover {
  border-color: #101117 !important;
}

::v-deep .ant-select-arrow {
  color: #63d7de !important;
  right: 5px !important;
}

.pagination-div {
  display: flex;
  flex-direction: row-reverse;
}
</style>
