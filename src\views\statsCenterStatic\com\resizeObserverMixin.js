export default {
    data() {
        return {
            scaleNum: 1,
            xNum: 0,
            resizeObserver:null,
        }
    },
    created() {

    },
    mounted() {
        this.addResizeObserver()
    },
    beforeDestroy(){
        this.delResizeObserver()
      },
    methods: {
        setScale() {
            this.scaleNum = window.innerWidth / 1920
            this.xNum = (412 * this.scaleNum - 412) / 2
            // let scaleH = window.innerHeight / 1080
            // this.scaleNum = Math.min(scaleH, scaleW)
        },
        addResizeObserver() {
            let app = document.getElementById('app')
            this.resizeObserver = new ResizeObserver((entries) => {
                this.setScale()
            })
            this.resizeObserver.observe(app)
        },
        delResizeObserver() {
            let app = document.getElementById('app')
            if (app && this.resizeObserver) {
                this.resizeObserver.unobserve(app)
                this.resizeObserver = null
            }
        },
    }
}