import { downFile, getAction, postParamsAction } from '@api/manage'
import {getFileNamesAndFilePaths} from '@views/opmg/knowledgeManagement/knowledgeBase/modules/dataListAndFunc'
import Vue from 'vue'
import { queryConfigureDictItem } from '@api/api'
import { setImgAllPath } from '@/utils/imagePathAboutTinymce'
export const knowledgeInfoMixins= {
  props: {
    fromtype: {
      type: Number,
      required: false,
      default: 0,
    }
  },
  data() {
    return {
      kkfileviewUrl: '',
      knowledgeId: '',
      kInfo: {},
      colStatus: 0,
      likeStatus: -1,
      unlikeStatus: -1,
      canComment: false,
      canDownload: false,
      exportType: 'word',
      defaultActiveKey: '1',
      associationVisible:1,
      url: {
        kInfo: '/kbase/knowledges/queryById',//通过知识id获取知识信息
        hanlderStatus: '/kbase/knowledges/userchosen',//初始获取点赞、点踩、收藏状态

        collect: '/kbase/knowledges/collect',//点击收藏调用
        uncollect: '/kbase/knowledges/uncollect',//反选，点击取消收藏调用

        wordExport: '/kbase/knowledges/word',
        pdfExport: '/kbase/knowledges/pdf'
      }
    }
  },
  methods: {
    /*初始化知识
    知识库、我的知识、运维助手调用同一个知识详情接口，知识审批调用另外的接口*/
    init(infoUrl=''){
      let kinfoUrl=infoUrl?infoUrl:this.url.kInfo
      this.colStatus = 0
      this.likeStatus =-1
      this.unlikeStatus =-1
      this.canDownload=false
      this.kInfo={}
      if (this.data.id) {
        this.knowledgeId = this.data.id
        this.initKnowledgeInfo(kinfoUrl ,this.data.id)
        this.initHanlderStatus(this.data.id)
        this.defaultActiveKey='1'
      }
    },
    /*初始化知识：涉及运维助手、知识审批、知识库、我的知识*/
    initKnowledgeInfo(infoUrl, id) {
      getAction(infoUrl, { id: id }).then((res) => {
        if (res.success) {
          this.kInfo = res.result
          //是否允许下载
          let config = this.kInfo.otherConfig
          if (config && config.length > 0) {
            this.canDownload = config.indexOf('allowDownload') > -1 ? true : false
            // this.canComment=config.indexOf('allowComment')>-1?true:false
          }

          //富文本图片地址处理
          let plan = this.kInfo.plan
          if (plan && plan.length > 0) {
            this.kInfo.plan = setImgAllPath(plan)
          }
          //文件是否能被索引到
          let indexFile=null
          let idxFile=this.kInfo.indexFile
          if (idxFile && idxFile.length > 0) {
            indexFile=idxFile.split(',')
          }
          //附件地址和名称处理
          let fileInfo = getFileNamesAndFilePaths(this.kInfo.files)
          let fileNames = fileInfo.fileNames
          let filePaths = fileInfo.filePaths

          if (filePaths && filePaths.length > 0) {
            let filePathsArr = filePaths.split(',')
            let fileNamesArr = fileNames.split(',')
            let filesArr = []
            for (let i = 0; i < filePathsArr.length; i++) {
              let item = {
                filePath: filePathsArr[i],
                fileName: this.getFileName(fileNamesArr[i]),
                suffixName: this.getFileSuffixName(fileNamesArr[i]),
                isSelected: false
              }
              if (indexFile){
                item['indexFile']=indexFile[i]
              }
              filesArr.push(item)
            }
            this.kInfo.filesArr = filesArr
          }
        } else {
          this.$message.warning(res.message)
        }
      }).catch((err) => {
        this.$message.warning(err.message)
      })
    },
    /*初始化知识分享页面的知识信息*/
    initShareKInfo(result) {
      this.kInfo = result

      //是否允许下载
      let config = this.kInfo.otherConfig
      if (config && config.length > 0) {
        this.canDownload = config.indexOf('allowDownload') > -1 ? true : false
        // this.canComment=config.indexOf('allowComment')>-1?true:false
      }

      //富文本图片地址处理
      let plan = this.kInfo.plan
      if (plan && plan.length > 0) {
        this.kInfo.plan = setImgAllPath(plan)
      }

      let indexFile=null
      let idxFile=this.kInfo.indexFile
      if (idxFile && idxFile.length > 0) {
        indexFile=idxFile.split(',')
      }
      //附件地址和名称处理
      let fileInfo = getFileNamesAndFilePaths(this.kInfo.files)
      let fileNames = fileInfo.fileNames
      let filePaths = fileInfo.filePaths

      if (filePaths && filePaths.length > 0) {
        let filePathsArr = filePaths.split(',')
        let fileNamesArr = fileNames.split(',')
        let filesArr = []
        for (let i = 0; i < filePathsArr.length; i++) {
          let item = {
            filePath: filePathsArr[i],
            fileName: this.getFileName(fileNamesArr[i]),
            suffixName: this.getFileSuffixName(fileNamesArr[i]),
            isSelected: false
          }
          if (indexFile){
            item['indexFile']=indexFile[i]
          }
          filesArr.push(item)
        }
        this.kInfo.filesArr = filesArr
      }
    },
   /*初始化收藏、点赞、点踩状态*/
    initHanlderStatus(id) {
      getAction(this.url.hanlderStatus, { knowledgeId: id }).then((res) => {
        if (res.success) {
          this.colStatus = res.result.currentUserCollect
          this.likeStatus = res.result.currentUserLike
          this.unlikeStatus = res.result.currentUserUnlike

        } else {
          this.$message.warning(res.message)
        }
      }).catch((err) => {
        this.$message.warning(err.message)
      })
    },
    /*从配置字典中获取kkfileview文件在线预览服务地址*/
    getKkfileviewURL() {
      return new Promise((resolve) => {
        queryConfigureDictItem({
          parentCode: 'kkfileviewURL',
          childCode: 'url'
        }).then((res) => {
          if (res.success) {
            resolve(res.result && res.result != 'null' ? res.result : 'null')
          } else {
            resolve('null')
          }
        }).catch((err) => {
          resolve('null')
        })
      })
    },
    /*获取文件不包含后缀的中文名*/
    getFileName(fullName) {
      let index = fullName.lastIndexOf('.')
      return index > -1 ? fullName.substring(0, index) : fullName
    },
    /*获取文件后缀名*/
    getFileSuffixName(fullName) {
      let index = fullName.lastIndexOf('.')
      return index > -1 ? fullName.substring(index) : ''
    },
    /**是否收藏*/
    handleCollection(){
      switch (this.colStatus){
        case 0:
          this.collect()
          break;
        case 1:
          this.uncollect()
          break;
      }
    },
    /*收藏*/
    collect() {
      postParamsAction(this.url.collect, { knowledgeId: this.knowledgeId }).then((res) => {
        if (res.success) {
          this.$message.success('收藏成功')
          this.colStatus= 1
        } else {
          this.$message.warning('收藏失败')
        }
      }).catch((err) => {
        this.$message.warning(err.message)
      })
    },
    /*取消收藏*/
    uncollect() {
      postParamsAction(this.url.uncollect, { knowledgeId: this.knowledgeId }).then((res) => {
        if (res.success) {
          this.$message.success('取消收藏成功')
          this.colStatus= 0
        } else {
          this.$message.warning('取消收藏失败')
        }
      }).catch((err) => {
        this.$message.warning(err.message)
      })
    },
    /**分享*/
    handleShare(){
      this.$refs.addShareModal.add(this.knowledgeId)
      this.$refs.addShareModal.title='知识分享'
      this.$refs.addShareModal.disableSubmit=false
    },
    //返回上一级
    getGo() {
      if(!!this.fromtype) {
        this.$parent.pButton1(this.fromtype)
      } else {
        this.$parent.pButton1(0)
      }
      this.kInfo={}
    }
  }
}


