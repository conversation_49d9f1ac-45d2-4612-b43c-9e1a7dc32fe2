<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll zxw">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <!-- 查询区域 -->
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <div class="table-page-search-wrapper-style">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="设备名称">
                  <a-input placeholder="请输入" autocomplete="off" :allowClear="true" v-model="queryParam.terminalName">
                  </a-input>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="IP地址">
                  <a-input placeholder="请输入" autocomplete="off" :allowClear="true" v-model="queryParam.ipAddress">
                  </a-input>
                </a-form-item>
              </a-col>
              <a-col :span="colBtnsSpan()">
                <span class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button type="primary" @click="searchQuery" class="btn-search-style">查询</a-button>
                  <a-button @click="searchReset" style="margin-left: 10px" class="btn-reset-style">重置</a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <!-- 查询区域-END -->

      <a-card :bordered="false" style="flex: auto" class="core">
        <a-row class="lastBtn2">
          <!-- 操作按钮区域 -->
          <div class="table-operator">
            <a-button @click="handleAdd">新增</a-button>
            <a-button @click="handleExportXls('ip地址白名单')">导出</a-button>
            <a-button @click="handleTemplateXls()">下载模版</a-button>
            <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader"
              :action="importExcelUrl" @change="handleImportExcel">
              <a-button>导入</a-button>
            </a-upload>
            <a-dropdown v-if="selectedRowKeys.length > 0">
              <a-menu slot="overlay" style='text-align: center'>
                <a-menu-item key="1" @click="batchDel">删除</a-menu-item>
              </a-menu>
              <a-button> 批量操作
                <a-icon type="down" />
              </a-button>
            </a-dropdown>
          </div>
        </a-row>
        <!-- table区域-begin -->
        <a-table ref="table" bordered rowKey="id" :columns="columns" :dataSource="dataSource" :pagination="ipagination"
          :loading="loading" :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          class="j-table-force-nowrap" @change="handleTableChange">
          <template slot="htmlSlot" slot-scope="text">
            <div v-html="text"></div>
          </template>
          <template slot="imgSlot" slot-scope="text">
            <span v-if="!text" style="font-size: 14px">无图片</span>
            <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width: 80px; font-size: 14px" />
          </template>
          <template slot="fileSlot" slot-scope="text">
            <span v-if="!text" style="font-size: 14px">无文件</span>
            <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
              下载
            </a-button>
          </template>

          <span slot="action" slot-scope="text, record" class="caozuo"
            style="display: inline-block; white-space: nowrap; text-align: center">
            <a @click="handleDetailPage(record)">查看</a>
            <a-divider type="vertical" />
            <a @click="handleEdit(record)">编辑</a>
            <a-divider type="vertical" />
            <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
              <a>删除</a>
            </a-popconfirm>
          </span>
          <template slot="tooltip" slot-scope="text">
            <a-tooltip placement="topLeft" :title="text" trigger="hover">
              <div class="tooltip">
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
        <!-- 下载模版 -->
        <iframe id="download" style="display: none"></iframe>
      </a-card>

      <devops-ip-manage-modal ref="modalForm" @ok="modalFormOk"></devops-ip-manage-modal>
    </a-col>
  </a-row>
</template>

<script>
  import '@/assets/less/TableExpand.less'
  import {
    mixinDevice
  } from '@/utils/mixin'
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import DevopsIpManageModal from './modules/DevopsIpManageModal'
  import JSuperQuery from '@/components/jeecg/JSuperQuery'
  import {
    YqFormSearchLocation
  } from '@/mixins/YqFormSearchLocation'

  export default {
    name: 'DevopsIpManageList',
    mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
    components: {
      DevopsIpManageModal,
      JSuperQuery,
    },
    data() {
      return {
        description: 'ip地址白名单管理页面',
        // 表头
        columns: [{
            title: '设备名称',
            dataIndex: 'terminalName',
          },
          {
            title: 'IP地址',
            dataIndex: 'ipAddress',
            customCell: () => {
              let cellStyle = 'text-align: center; min-width: 100px'
              return {
                style: cellStyle,
              }
            },
          },
          {
            title: 'MAC地址',
            dataIndex: 'macCode',
          },
          {
            title: '使用人',
            dataIndex: 'utilizeUserText',
          },
          {
            title: '创建时间',
            dataIndex: 'createTime',
          },
          {
            title: '操作',
            dataIndex: 'action',
            fixed: 'right',
            align: 'center',
            width: 147,
            scopedSlots: {
              customRender: 'action'
            },
          },
        ],
        url: {
          list: '/devopsipmanage/devopsIpManage/list',
          delete: '/devopsipmanage/devopsIpManage/delete',
          deleteBatch: '/devopsipmanage/devopsIpManage/deleteBatch',
          exportXlsUrl: '/devopsipmanage/devopsIpManage/exportXls',
          importExcelUrl: 'devopsipmanage/devopsIpManage/importExcel',
          downloadTemplateXlsUrl: '/devopsipmanage/devopsIpManage/downloadTemplate',
        },
        dictOptions: {},
        superFieldList: [],
        detailsData: {},
      }
    },
    created() {
      this.getSuperFieldList()
    },
    mounted() {},
    computed: {
      importExcelUrl: function () {
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
      },
      downloadTemplateXlsUrl: function () {
        return `${window._CONFIG['domianURL']}/${this.url.downloadTemplateXlsUrl}`
      },
    },
    methods: {
      //excel模板
      handleTemplateXls() {
        const path = this.downloadTemplateXlsUrl
        document.getElementById('download').src = path
      },
      initDictConfig() {},
      getSuperFieldList() {
        let fieldList = []
        fieldList.push({
          type: 'string',
          value: 'terminalName',
          text: '设备名称',
          dictCode: ''
        })
        fieldList.push({
          type: 'string',
          value: 'ipAddress',
          text: 'IP地址',
          dictCode: ''
        })
        fieldList.push({
          type: 'string',
          value: 'macCode',
          text: 'MAC地址',
          dictCode: ''
        })
        fieldList.push({
          type: 'string',
          value: 'utilizeUserId',
          text: '使用人id',
          dictCode: ''
        })
        this.superFieldList = fieldList
      },
    },
  }
</script>
<style lang="less" scoped>
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';

  .table-page-search-wrapper .ant-form-inline .ant-form-item {
    margin-bottom: 0 !important;
  }

  .ant-table-pagination.ant-pagination {
    margin: 16px 0 0 0 !important;
  }

  ::v-deep .ant-table-thead>tr>th {
    text-align: center;
  }

  ::v-deep .ant-table-tbody>tr>td {

    &:nth-child(4),
    &:nth-child(5),
    &:nth-child(6) {
      text-align: center;
    }

    &:nth-child(2) {
      text-align: left;
    }

    &:nth-child(3) {
      text-align: right;
    }
  }
</style>