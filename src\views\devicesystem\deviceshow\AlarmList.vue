<template>
  <div class='scroll'>
      <!-- 查询区域 -->
      <div class='table-operator table-operator-style'>
        <!-- 操作按钮区域 -->
        <a-button @click='handleAdd()'>新增告警</a-button>
        <!-- table区域-begin -->
        <div>
          <a-table
            ref='table'
            bordered
            rowKey='id'
            :columns='columns'
            :dataSource='dataSource'
            :scroll='dataSource.length>0?{x:"max-content"}:{}'
            :pagination='ipagination'
            :loading='loading'
            @change='handleTableChange'
          >
            <template slot='tooltip' slot-scope='text'>
              <a-tooltip placement='topLeft' :title='text' trigger='hover'>
                <div class='tooltip'>
                  {{ text }}
                </div>
              </a-tooltip>
            </template>
            <template slot='isOnline' slot-scope='text'>
              <span v-if="text === '1'" style='font-size: 14px;   color: green'>已启用</span>
              <span v-else style='font-size: 14px;   color: red'>未启用</span>
            </template>
            <span slot='action' slot-scope='text, record' class='caozuo'>
          <a @click='handleShow(record)'>查看</a>
          <a-divider type='vertical' />
          <a @click='handleEdit(record)'>编辑</a>
              <!-- <a @click="handleConfirm(record)">确认</a> -->
          <a-divider type='vertical' />
           <a @click='customHandleDelete(record)'>删除</a>
          <a-divider type='vertical' />
          <a-popconfirm title='确定禁用吗?' v-if="record.isOnline === '1'" @confirm='() => handleForbidden(record)'>
            <a>禁用</a>
          </a-popconfirm>
          <a-popconfirm title='确定启用吗?' v-if="record.isOnline === '0'" @confirm='() => handleForbidden(record)'>
            <a>启用</a>
          </a-popconfirm>
        </span>
          </a-table>
        </div>
      </div>
    <!--新增、编辑页面-->
    <detail-modal ref='modalForm' @ok='modalFormOk'></detail-modal>
  </div>
</template>

<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import JEllipsis from '@/components/jeecg/JEllipsis'
import { deleteAction, getAction, putAction } from '@/api/manage'
import AlarmTemplateDetailModal from '@comp/alarmTemplate/AlarmTemplateDetailModal'

export default {
  name: 'AlarmList',
  mixins: [JeecgListMixin],
  components: {
    JEllipsis,
    'detail-modal': AlarmTemplateDetailModal
  },
  data() {
    return {
      description: '设备信息查看告警策略界面',
      showFlag: false,//通过设备信息查看告警策略需隐藏产品名称和设备名称，默认是显示的
      //查询条件，此参数名称与JeecgListMixin模块参数一致
      queryParam: {
        deviceId: '' //查询参数 设备id
      },
      disableMixinCreated: true,
      // 表头
      columns: [
        {
          title: '告警名称',
          dataIndex: 'name',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 100px'
            return { style: cellStyle }
          }
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          customCell: () => {
            let cellStyle = 'text-align: center;width: 180px'
            return { style: cellStyle }
          }
        },
        {
          title: '告警描述',
          dataIndex: 'remark',
          scopedSlots: { customRender: 'tooltip' },
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 100px;max-width:300px'
            return { style: cellStyle }
          }
        },
        {
          title: '启用状态',
          align: 'center',
          dataIndex: 'isOnline',
          scopedSlots: { customRender: 'isOnline' },
          customCell: () => {
            let cellStyle = 'text-align: center;width: 100px'
            return { style: cellStyle }
          }
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          scopedSlots: { customRender: 'action' },
          width:200,
          /*customCell: () => {
            let cellStyle = 'text-align: center;width: 200px'
            return { style: cellStyle }
          }*/
        }
      ],
      url: {
        list: '/alarm/alarmTemplate/findTemAndRuleByDeviceId', //分页展示接口
        delete: '/alarm/alarmTemplate/delete', //删除接口
        editStatus: '/alarm/alarmTemplate/editStatus' //启用、禁用
      }
    }
  },
  props: {
    deviceInfo: {
      type: Object,
      required: true,
      default: null
    }
  },
  watch: {
    deviceInfo(newVal,oldVal){
      this.init(newVal)
    }
  },
  activated() {
    if (this.queryParam.deviceId) {
      this.loadData()
    }
  },
  methods: {
    init(record){
      this.queryParam.deviceId = record.id
      if (this.queryParam.deviceId) {
        this.loadData()
      }
    },
    handleEdit: function(record) {
      this.$refs.modalForm.edit(record)
      this.$refs.modalForm.deviceInfo = this.deviceInfo //传递设备信息
      this.$refs.modalForm.showFlag = false //模板中 是否显示产品名称和设备名称
      this.$refs.modalForm.title = '编辑告警'
      this.$refs.modalForm.disableSubmit = false
    },
    handleAdd: function() {
      this.$refs.modalForm.add()
      this.$refs.modalForm.deviceInfo = this.deviceInfo //传递设备信息
      this.$refs.modalForm.showFlag = false //模板中 是否显示产品名称和设备名称
      this.$refs.modalForm.title = '新增告警'
      this.$refs.modalForm.disableSubmit = false
    },
    customHandleDelete: function(record) {
      let that = this
      if (!that.url.delete) {
        that.$message.error('请设置url.delete属性!')
        return
      }
      let devList = record.deviceIdList
      let tips = '确认删除吗？'
      if (!devList || devList.length == 0 || devList.length > 1) {
        tips = '有其他设备绑定了此告警，确认后会统一删除,确认删除吗？'
        that.confirmDelete(tips, record.id)
      } else {
        that.confirmDelete(tips, record.id)
      }
    },
    confirmDelete(tips, id) {
      let that = this
      that.$confirm({
        title: '确认删除',
        type: 'warning',
        okText: '确认',
        cancelText: '取消',
        content: tips,
        onOk: function() {
          that.handleDelete(id)
        }
      })
    },
    handleForbidden(record) {
      let isOnline = record.isOnline === '0' ? '1' : '0'
      let alarmTemplate = {
        id: record.id,
        isOnline: isOnline
      }
      putAction(this.url.editStatus, alarmTemplate).then(res => {
        if (res.success) {
          this.$message.success(res.message)
          const newData = [...this.dataSource]
          newData.forEach(ele => {
            if (record.id === ele.id) {
              ele.isOnline = isOnline
            }
          })
          this.dataSource = newData
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    //查看详情
    handleShow(record) {
      this.$parent.pButton2(1, record)
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
.scroll{
  height: 100%;
  overflow: hidden;
  overflow-y: auto;
}
.table-operator{
  margin-right: 1px;
}
</style>
