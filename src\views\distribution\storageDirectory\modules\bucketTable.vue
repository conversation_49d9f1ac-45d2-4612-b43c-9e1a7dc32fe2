<template>
  <div >
    <a-table
      ref='table'
      bordered
      size="middle"
      :row-key='(record, index) => {return index}'
      :columns='columns'
      :dataSource='dataSource'
      :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
      :loading='loading'
      :pagination='false'
      @change='handleTableChange'>
      <span slot='bucketName' slot-scope='text, record'>
        <a @click='loadFiles(record)' style="color:#409eff"><a-icon type="delete"  class="table-icon"/>{{text}}</a>
      </span>
      <template slot='tooltip' slot-scope='text'>
        <a-tooltip placement='topLeft' :title='text' trigger='hover'>
          <div class='tooltip'>
            {{ text }}
          </div>
        </a-tooltip>
      </template>
    </a-table>
  </div>
</template>
<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { getAction } from '@api/manage'

export default {
  name: "bucketTable",
  props:{
    clusterId:{
      type:String,
      required:true
    }
  },
  mixins: [JeecgListMixin],
  data() {
    return {
      disableMixinCreated: true,
      columns: [
        {
          title: '桶名',
          dataIndex: 'bucketName',
          customCell: () => {
            let cellStyle = 'text-align: left'
            return { style: cellStyle }
          },
          scopedSlots: { customRender: 'bucketName' }
        }
      ],
      url: {
        list: '/distributedStorage/listBucket'
      }
    }
  },
  watch:{
    clusterId:{
      deep:true,
      immediate: true,
      handler(val){
        if (val){
          this.queryParam.clusterId = val
          this.loadData(1)
        }else {
          this.queryParam={}
          this.dataSource=[]
        }
      }
    }
  },
  methods:{
    loadData(arg) {
      if (!this.url.list) {
        this.$message.error('请设置url.list属性!')
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1
      }
      var params = this.getQueryParams() //查询条件
      this.loading = true
      getAction(this.url.list, params).then((res) => {
        if (res.success && res.result) {
          let data=res.result.records || res.result
          this.dataSource =[]
          for (let i = 0; i < data.length; i++) {
            this.dataSource.push({bucketName:data[i]})
          }
          if (this.dataSource.length < 9) {
            this.clientHeight = false
          }
          this.ipagination.total= this.dataSource.length
        }
        if (res.code === 510) {
          this.$message.warning(res.message)
        }
        this.loading = false
      })
    },
    loadFiles(bucket){
      this.$emit('loadFolders',bucket)
    }
  }
}
</script>

<style scoped lang="less">
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
.table-icon{
  font-size:18px;
  margin-right: 8px
}
</style>