<template>
  <div>
    <a-input-search v-model="userNames" placeholder="请先选择终端" readOnly unselectable="on" @search="onSearchDepUser">
      <a-button slot="enterButton" :disabled="disabled">选择终端</a-button>
    </a-input-search>
    <j-select-terminal-by-dep-modal ref="selectModal" :modal-width="modalWidth" :centered="true" :multi="multi"
                                @ok="selectOK" :user-ids="userIds" @initComp="initComp" />
  </div>
</template>

<script>
import JSelectTerminalByDepModal from './modal/JSelectTerminalByDepModal'

export default {
  name: 'JSelectTerminalByDep',
  components: {
    JSelectTerminalByDepModal
  },
  props: {
    modalWidth: {
      type: String,
      default: '1300px',
      required: false,
    },
    value: {
      type: String,
      required: false,
      default: () => {
        return ''
      }
    },
    disabled: {
      type: Boolean,
      required: false,
      default: false,
    },
    multi: {
      type: Boolean,
      default: true,
      required: false,
    },
  },
  data() {
    return {
      userNames: '',
      userIds: ''
    }
  },
  watch: {
    value: {
      handler(nVal, oVal) {
        this.userIds = nVal
      },
      deep: true,
      immediate: true
    }
  },
  model: {
    prop: 'value',
    event: 'change',
  },
  methods: {
    initComp(userNames) {
      this.userNames = userNames
    },
    onSearchDepUser() {
      this.$refs.selectModal.showModal(this.userIds)
    },
    selectOK(realNames, idstr) {
      this.userIds = idstr
      this.$emit('change', idstr)
    }
  }
}
</script>

<style scoped lang="less">
::v-deep .ant-modal-body {
  padding: 24px 48px 24px 48px;
}

::v-deep .ant-modal {
  padding: 24px;
}

.j-modal-box.fullscreen {
  margin: 0 !important;
  max-width: 100vw !important;

  ::v-deep .ant-modal {
    max-width: 100vw !important;
    margin: 0;
  }
}

@media (max-width: 1312px) {
  ::v-deep .ant-modal {
    top: 0px;
    width: 1200px !important;
    max-width: 1200px !important;
    margin: 0 !important;
  }

  .ant-modal {
    top: 0px;
    width: 1200px !important;
    max-width: 1200px !important;
    margin: 0 !important;
  }
}
</style>