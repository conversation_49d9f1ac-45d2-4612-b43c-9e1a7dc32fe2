<template>
  <a-card style="height: 100%;overflow: hidden; overflow-y: auto">
    <a-row>
      <a-col :span="24">
        <span style="margin-left:10px;color: #409eff;cursor: pointer" @click="handleDetailEdit(info)">
          <a-icon type="edit" style="margin-right: 6px;"/>编辑
        </span>
        <span style="float: right;margin-bottom: 12px;" >
          <img src="~@/assets/return1.png" alt="" @click="getGo" style="width: 20px;height: 20px;cursor: pointer">
        </span>
      </a-col>
      <a-col :span="24">
        <descriptions
          :show-title="false"
          :items="basicData"
          :title="'基本信息'"
          :column="{ xxl: 2, xl: 2, lg: 2, md: 2, sm: 1, xs: 1 }"
        ></descriptions>
      </a-col>
    </a-row>
    <devops-backup-scan-task-modal  ref="modalForm" @ok="modalFormOk"></devops-backup-scan-task-modal>
  </a-card>
</template>

<script>
  import {getAction } from '@/api/manage'
  import DevopsBackupScanTaskModal from './DevopsBackupScanTaskModal'
  import descriptions from '@comp/descriptions/descriptions.vue'
  export default {
    name: 'DevopsBackupScanTaskDetails',
    components: { DevopsBackupScanTaskModal ,descriptions},
    props: {
      data:{
        type:Object
      }
    },
    data () {
      return {
        basicFields: [
          {
            field: 'taskName',
            label: '任务名称'
          },
          {
            field: 'tarkCron',
            label: '触发任务码'
          },
          /* {
          field: 'conType_dictText',
          label: '备份类型'
        },*/
          {
            field: 'operateType_dictText',
            label: '操作类型'
          },
          {
            field: 'scanType_dictText',
            label: '扫描类型'
          },
          {
            field: 'resource_dictText',
            label: '待扫描设备'
          },
          {
            field: 'backupName',
            label: ''
          },
          {
            field: 'fileAdd',
            label: '待扫描文件路径'
          },
          {
            field: 'whPushMes',
            label: '是否推送消息'
          },
         /* {
            field: 'emailAddress',
            label: '邮箱地址'
          },*/
          {
            field: 'pushAddress_dictText',
            label: '通知模板'
          }
        ],
        basicData: [],
        info: {},
        url: {
          queryById: "/devopsBackupScanTask/devopsBackupScanTask/queryById"
        }
      }
    },
    watch: {
      data: {
        handler(val) {
          this.info =JSON.parse(JSON.stringify(val))
          this.init()
        },
        deep: true,
        immediate: true
      }
    },
    methods: {
      init(){
        this.basicData=[]
        if (this.info.id){
          this.basicFields=this.basicFields.map((item)=>{
              if (item.field==='backupName'){
                if (this.info.scanType==1){
                  item.label='备份策略'
                }else {
                  item.label='备份任务'
                }
              }
              return item
            })
          this.basicData=this.getDescriptionsItem(this.basicFields)
        }
      },
      /**
       * 整理所属信息、基本信息数据，以排除空数据     *
       * @param {Array} fields - 数组：元素包含字段英文名和中文名
       * */
      getDescriptionsItem(fields){
        let data=[]
        let obj=this.info
        for (let i = 0; i < fields.length; i++) {
          let field=fields[i].field
          let va=obj[field]+''
          if (va!=''&&va!='null'){
            let item={
              label:fields[i].label,
              value:obj[fields[i].field]
            }
            if (field==='whPushMes'){
              item.value=obj[fields[i].field]=="Y"?"是":"否"
            }
            data.push(item)
          }
        }
        return data
      },
      modalFormOk(){
        let params = {id:this.info.id };
        getAction(this.url.queryById,params).then((res)=>{
          if(res.success){
            this.info = res.result
            this.init(this.info)
          }
        });
      },
      //详情编辑
      handleDetailEdit: function (record) {
        this.$refs.modalForm.edit(record)
        this.$refs.modalForm.title = '编辑'
        this.$refs.modalForm.disableSubmit = false
      },
      //返回上一级
      getGo(){
        this.$parent.pButton2(0);
      }
    }
  }
</script>

<style scoped>
</style>