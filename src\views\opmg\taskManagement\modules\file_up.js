export const file_upExtendFields=[
  {
    extendName: '上传文件',
    extendCode: 'file',
    extendValue: '',
    extendType:'upload',
    /*查看时是否显示:true显示、false不显示*/
    display:false,
    placeholder: '请上传文件',
    multiple:false,
    number:1,
    /*500MB=500*1024*1024=524288000bit*/
    // maxFileSize:524288000,
    maxFileSize:500,
    unit:'MB',
    labelTips:"文件最大不能超过500MB",
    validatorRules:[{ required:true,message:'请上传文件'}],
    changeExtendFile:(value,model)=> changeFileUp(value,model),
    customParsingFun:(value,fileName)=> customFileParsingFun(value,fileName),
    resultDataFun:(value)=>setFileResultDataFun(value)
  },
  {
    extendName: 'MD5',
    extendCode: 'fileHash',
    extendValue: '',
    extendType:'',
    /*查看时是否显示:true显示、false不显示*/
    display:false,
  },
  {
    extendName: '文件名',
    extendCode: 'fileOriginalName',
    extendValue: '',
    extendTypeAlias:'loadFile',//查看下载
    extendType:'',
    /*查看时是否显示:true显示、false不显示*/
    display:true,
    displayDataFun:(value)=>getFileNameAndUrl(value)
  },
  {
    extendName: '下发位置',
    extendCode: 'issuedIn',
    extendValue: '',
    extendType:'inputString',//inputPassword 都用a-input组件,inputNumber用a-input-number，text用a-textarea组件
    /*查看时是否显示:true显示、false不显示*/
    display:true,
    placeholder: '请输入下发文件位置',
    validatorRules:[
      { required:true,message:'请输入下发文件位置'}
      // { min:2,max:100,message:'下发位置字符长度应在【2-100】之间',trigger:'blur'}
    ]
  },
  {
    extendName: '是否创建目录',
    extendCode: 'isMKdir',
    extendValue: true,
    extendType:'switch',
    /*查看时是否显示:true显示、false不显示*/
    display:true,
    validatorRules:[{ required:true,message:'请选择是否创建目录'}],
    /*设置col*/
    spanLayout:{
      xs: { span: 24 },
      sm: { span: 24 },
      md: { span: 12 }
    },
    /*设置labelCol、wrapperCol*/
    formItemLayout: {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 24 },
        md: { span: 12}
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 24 },
        md: { span: 12}
      }
    },
    displayDataFun:(value)=>displayBooleanDataFun(value)
  },
  {
    extendName: '是否强制覆盖',
    extendCode: 'isCover',
    extendValue: true,
    extendType: 'switch',
    /*查看时是否显示:true显示、false不显示*/
    display:true,
    validatorRules:[{ required:true,message:'请选择是否强制覆盖'}],
    /*设置col*/
    spanLayout:{
      xs: { span: 24 },
      sm: { span: 24 },
      md: { span: 12 }
    },
    /*设置labelCol、wrapperCol*/
    formItemLayout: {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 24 },
        md: { span: 12 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 24 },
        md: { span: 12 }
      }
    },
    displayDataFun:(value)=>displayBooleanDataFun(value)
  },
]

/*改变上传，调用此函数*/
function changeFileUp(value,model){
  model['fileHash']=value[0].fileHash
  model['fileOriginalName']=JSON.stringify({fileName:value[0].fileOriginalName,filePath:value[0].filePath})
}
/**根据后端返回数据格式，解析成前端需要的数据结构*/
function customFileParsingFun(value){
  let obj=JSON.parse(value.response.message)
  return {
    fileName:obj.fileOriginalName,
    filePath:obj.savePath,
    fileHash:obj.md5,
    fileOriginalName:obj.fileOriginalName,
  }
}
/**将数据处理成后端需要的结构，*/
function setFileResultDataFun(value) {
  let res = value
    .map((item, index) =>{
        return item.filePath
    }).join(',');
  return res
}
/*返回文件名和下载路径*/
function getFileNameAndUrl(value){
 let obj=JSON.parse(value)
  return {
    value: obj.fileName,
    url: obj.filePath,
    comType: 'triggerParentMethod'
  }
}
/*处理是否创建目录、是否强制覆盖在查看中的显示内容*/
function displayBooleanDataFun(value){
  let txt=value=='true'?'是':'否'
  return {value:`<span style="background-color:#007bff;padding:2px 6px;border-radius:3px;color:#fff">${txt}</span>`}
}