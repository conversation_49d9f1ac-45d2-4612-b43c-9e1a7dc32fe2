import Vue from 'vue'
import {} from "@/store/mutation-types"

const yqsm = {
  state: {
    toBigScreen:false,

    commandStackObj: null,
    checkActive: false,
    openDevice: false,
    //0 拨打电话  1 通话中    2已挂断
    callStatus: "",
    //通话时长
    duration: "00:00",
    //保障时间
    reportDate: "",
    //通话结束返回保存记录后返回数据
    callData:"",
    //服务请求通话结束返回保存记录后返回数据
    serviceCallData:[],
    //呼叫中心服务请求创建界面关闭按钮是否显示
    showClose:false,
    //人员信息
    user: {},
    //录音文件地址
    recordPath:"",
    //值班人拨号方式
    dialMode:""//0 值班人右下角操作呼叫   1 值班人服务请求创建界面回拨
  },
  mutations: {
    SET_TO_SCREEN(state, obj) {
      state.toBigScreen = obj;
    },
    SET_COMMAND_STACK_OBJ(state, obj) {
      state.commandStackObj = obj;
    },
    SET_CHECK_ACTIVE(state, obj) {
      state.checkActive = obj;
    },
    SET_OPEN_DEVICE(state, obj) {
      state.openDevice = obj;
    },
    SET_RECORD_PATH(state, obj) {
      state.recordPath = obj;
    },
    SET_CALL_STATUS(state, obj) {
      state.callStatus = obj;
    },
    USER(state, obj) {
      state.user = obj;
    },
    SET_DURATION(state, obj) {
      state.duration = obj;
    },
    SET_REPORT_DATE(state, obj) {
      state.reportDate = obj;
    },
    SET_Call_DATA(state,obj){
      state.callData=obj;
    },
    SET_SERVICE_CALL_DATA(state,obj){
		console.log(obj)
		if(obj == null || obj == '' || obj == undefined){
			state.serviceCallData = []
		}else{
			state.serviceCallData.push(obj.id)
		}
     
    },
   SET_SHOW_CLOSE(state, obj) {
    state.showClose = obj;
   },
    SET_DIAL_MODE(state, obj) {
      state.dialMode = obj;
    },
  },
  actions: {}
}
export default yqsm