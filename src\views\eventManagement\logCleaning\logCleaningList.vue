<template>
  <div style='height: 100%; display: flex; flex-direction: column'>
    <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
      <!-- 查询区域 -->
      <div class='table-page-search-wrapper'>
        <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
          <a-row :gutter='24' ref='row'>
            <a-col :span='spanValue'>
              <a-form-item label='名称'>
                <a-input
                  :maxLength='maxLength'
                  placeholder='请输入名称'
                  autocomplete='off'
                  :allowClear='true'
                  v-model='queryParam.dataViewName'
                />
              </a-form-item>
            </a-col>
            <a-col :span='colBtnsSpan()'>
                <span
                  class='table-page-search-submitButtons'
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                >
                  <a-button type='primary' @click='searchQuery' class='btn-search-style'>查询</a-button>
                  <a-button @click='searchReset' class='btn-reset-style'>重置</a-button>
                </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered='false' style='flex: auto' class='core'>
      <div class="table-operator">
        <a-dropdown v-if="selectedRowKeys.length > 0">
          <a-menu slot="overlay" style='text-align: center'>
            <a-menu-item key="1" @click="handleCleaning(60)">清理60天前</a-menu-item>
            <a-menu-item key="2" @click="handleCleaning(90)">清理90天前</a-menu-item>
            <a-menu-item key="3" @click="handleCleaning(180)">清理180天前</a-menu-item>
          </a-menu>
          <a-button> 批量操作
            <a-icon type="down" />
          </a-button>
        </a-dropdown>
      </div>
      <a-table
        ref='table'
        rowKey='id'
        bordered
        :columns='columns'
        :dataSource='dataSource'
        :pagination='ipagination'
        :loading='loading'
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        @change='handleTableChange'
      >
        <span slot='action' slot-scope='text, record' class='caozuo'>
         <a @click="clean(record.id,60)">清理60天前</a>
          <a-divider type='vertical'/>
           <a @click="clean(record.id,90)">清理90天前</a>
          <a-divider type='vertical'/>
           <a @click="clean(record.id,180)">清理180天前</a>
        </span>
        <template slot='tooltip' slot-scope='text'>
          <a-tooltip placement='topLeft' :title='text' trigger='hover'>
            <div class='tooltip'>
              {{ text }}
            </div>
          </a-tooltip>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
import {getAction } from '@api/manage'

export default {
  name: 'logCleaningList',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  data() {
    return {
      maxLength: 50,
      description: '',
      formItemLayout: {
        labelCol: {
          style: 'width:50px'
        },
        wrapperCol: {
          style: 'width:calc(100% - 50px)'
        }
      },
      // 表头
      columns: [
        {
          title: '名称',
          dataIndex: 'dataViewName'
        },
        {
          title: '索引模式',
          dataIndex: 'indexPatterns'
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          scopedSlots: { customRender: 'action' },
          width: 200
        }
      ],
      url: {
        list: '/logAnalyze/pageList',
        cleaning: '/logAnalyze/logPurge'
      }
    }
  },
  methods: {
    handleCleaning(days) {
      if (!this.url.cleaning) {
        this.$message.error('请设置url.cleaning属性!')
        return
      }
      if (this.selectedRowKeys.length <= 0) {
        this.$message.warning('请选择一条记录！')
        return
      } else {
        var ids = ''
        for (var a = 0; a < this.selectedRowKeys.length; a++) {
          ids += this.selectedRowKeys[a] + ','
        }
        this.clean(ids, days)
      }
    },
    clean(ids, days) {
      var that = this
      this.$confirm({
        title: '确认清理',
        okText: '是',
        cancelText: '否',
        content: '是否清理' + days + '天前日志数据?',
        onOk: () => {
          that.loading = true
          getAction(that.url.cleaning, { logAnalyzeIds: ids, syslogExpireDays: days })
            .then((res) => {
              if (res.success) {
                //重新计算分页问题
                that.reCalculatePage(that.selectedRowKeys.length)
                that.$message.success(res.message)
                that.loadData()
                that.onClearSelected()
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.loading = false
            })
        }
      })
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
</style>