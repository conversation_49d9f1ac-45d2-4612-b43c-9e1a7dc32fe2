<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll zxw">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <!-- 查询区域 -->
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <div class="table-page-search-wrapper-style">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="任务名称">
                  <a-input
                    placeholder="请输入"
                    autocomplete="off"
                    :allowClear="true"
                    v-model="queryParam.taskName" :maxLength="50"
                  ></a-input>
                </a-form-item>
              </a-col>
<!--              <a-col :span="spanValue">
                <a-form-item label="备份类型">
                  <j-dict-select-tag
                    :allowClear="true"
                    v-model="queryParam.conType"
                    dictCode="resource_type"
                    placeholder="请选择"
                  />
                </a-form-item>
              </a-col>-->
              <a-col :span="spanValue">
                <a-form-item label="任务状态">
                  <a-select v-model="queryParam.execstate" placeholder="请选择状态" :allowClear="true">
                    <a-select-option value="">全部</a-select-option>
                    <a-select-option value="0">正常</a-select-option>
                    <a-select-option value="-1">停止</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="colBtnsSpan()">
                <span
                  class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                >
                  <a-button type="primary" @click="searchQuery">查询</a-button>
                  <a-button @click="searchReset" style="margin-left: 8px">重置</a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <!-- 查询区域-END -->

      <a-card :bordered="false" style="flex: auto" class="core">
        <a-row class="lastBtn2">
          <!-- 操作按钮区域 -->
          <div class="table-operator">
            <a-button @click="handleAdd">新增</a-button>
            <a-button @click="handleExportXls('扫描任务')">导出</a-button>
            <a-button @click="seeResult">扫描结果</a-button>
            <a-dropdown v-if="selectedRowKeys.length > 0">
              <a-menu slot="overlay" style='text-align: center'>
                <a-menu-item key="1" @click="batchDel">删除</a-menu-item>
              </a-menu>
              <a-button> 批量操作
                <a-icon type="down"/>
              </a-button>
            </a-dropdown>
          </div>
        </a-row>
        <!-- table区域-begin -->
        <a-table
          ref="table"
          bordered
          rowKey="id"
          :columns="columns"
          :dataSource="dataSource"
          :pagination="ipagination"
          :loading="loading"
          :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          class="j-table-force-nowrap"
          @change="handleTableChange"
        >
          <span slot="action" slot-scope="text, record">
          <a @click="resumeJob(record)" v-if="record.execstate==='-1'">启动</a>
          <a @click="pauseJob(record)" v-if="record.execstate==='0'">停止</a>

          <a-divider type="vertical"/>
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down"/></a>
            <a-menu slot="overlay">
              <a-menu-item><a @click="executeImmediately(record)">执行一次</a></a-menu-item>
              <a-menu-item><a @click="handleDetailPage(record)">查看</a></a-menu-item>
              <a-menu-item><a @click="handleEdit(record)">编辑</a></a-menu-item>
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>
          <template slot="customRenderStatus" slot-scope="execstate">
            <a-tag v-if="execstate==='0'" color="green">已启动</a-tag>
            <a-tag v-if="execstate==='-1'" color="orange">已暂停</a-tag>
          </template>
        </a-table>
      </a-card>

      <devops-backup-scan-task-modal ref="modalForm" @ok="modalFormOk"></devops-backup-scan-task-modal>
      <devops-backup-scan-task-result-list ref="taskResult" @ok="modalFormOk"></devops-backup-scan-task-result-list>
    </a-col>
  </a-row>
</template>

<script>
import '@/assets/less/TableExpand.less'
import {mixinDevice} from '@/utils/mixin'
import {JeecgListMixin} from '@/mixins/JeecgListMixin'
import DevopsBackupScanTaskModal from './modules/DevopsBackupScanTaskModal'
import DevopsBackupScanTaskResultList from './DevopsBackupScanTaskResultList'
import {filterMultiDictText} from '@/components/dict/JDictSelectUtil'
import JSuperQuery from '@/components/jeecg/JSuperQuery.vue'
import {YqFormSearchLocation} from '@/mixins/YqFormSearchLocation'
import { getAction } from '@/api/manage'

export default {
  name: 'DevopsBackupScanTaskList',
  mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
  components: {
    DevopsBackupScanTaskModal,
    JSuperQuery,
    DevopsBackupScanTaskResultList,
  },
  data() {
    return {
      formItemLayout: {
        labelCol: {
          style: 'width:100px',
        },
        wrapperCol: {
          style: 'width:calc(100% - 80px)'
        }
      },
      description: '扫描任务管理页面',
      // 表头
      columns: [
        {
          title: '任务名称',
          dataIndex: 'taskName',
        },
        // {
        //   title: '连接方式',
        //   dataIndex: 'conTypes',
        // },
        {
          title: '扫描类型',
          dataIndex: 'scanType_dictText',
        },
        {
          title: '备份策略/备份任务',
          dataIndex: 'backupName',
        },
       /* {
          title: '备份类型',
          dataIndex: 'conType_dictText',
        },*/
         {
         title: '操作类型',
         dataIndex: 'operateType_dictText',
       },
        {
          title: '设备IP',
          dataIndex: 'resourceIP',
        },

        {
          title: '任务状态',
          align:"center",
          dataIndex: 'execstate',
          scopedSlots: { customRender: 'customRenderStatus' },
          /*filterMultiple: false,
          filters: [
            { text: '已启动', value: '0' },
            { text: '已暂停', value: '-1' },
          ]*/
        },
        {
          title: '操作',
          dataIndex: 'action',
          fixed: 'right',
          align: 'center',
          width: 147,
          scopedSlots: {customRender: 'action'},
        },
      ],
      url: {
        list: '/devopsBackupScanTask/devopsBackupScanTask/list',
        delete: '/devopsBackupScanTask/devopsBackupScanTask/delete',
        deleteBatch: '/devopsBackupScanTask/devopsBackupScanTask/deleteBatch',
        exportXlsUrl: '/devopsBackupScanTask/devopsBackupScanTask/exportXls',
        importExcelUrl: 'devopsBackupScanTask/devopsBackupScanTask/importExcel',
        pause: "/devopsBackupScanTask/devopsBackupScanTask/pause",
        resume: "/devopsBackupScanTask/devopsBackupScanTask/resume",
        execute: "/devopsBackupScanTask/devopsBackupScanTask/execute"
      }
    }
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
  },
  methods: {
    seeResult: function () {
      var ids = ''
      if (0 < this.selectionRows.length) {
        for (var i = 0; i < this.selectionRows.length; i++) {
          ids += this.selectionRows[i].id + ','
        }
      }
      this.$refs.taskResult.edit(ids)
      this.$refs.taskResult.title = '扫描结果'
      this.$refs.taskResult.disableSubmit = false
    },
    pauseJob: function(record){
      var that = this;
      //暂停定时任务
      this.$confirm({
        title:"确认暂停",
        okText: '是',
        cancelText: '否',
        content:"是否暂停选中任务?",
        onOk: function(){
          getAction(that.url.pause,{id:record.id}).then((res)=>{
            if(res.success){
              that.$message.success(res.message);
              that.loadData();
              that.onClearSelected();
            }else{
              that.$message.warning(res.message);
            }
          })
        }
      })
    },
    resumeJob: function(record){
      var that = this;
      //恢复定时任务
      this.$confirm({
        title:"确认启动",
        okText: '是',
        cancelText: '否',
        content:"是否启动选中任务?",
        onOk: function(){
          getAction(that.url.resume,{id:record.id}).then((res)=>{
            if(res.success){
              that.$message.success(res.message);
              that.loadData();
              that.onClearSelected();
            }else{
              that.$message.warning(res.message);
            }
          });
        }
      });
    },
    executeImmediately(record){
      var that = this;
      //立即执行定时任务
      this.$confirm({
        title:"确认提示",
        okText: '是',
        cancelText: '否',
        content:"是否立即执行任务?",
        onOk: function(){
          getAction(that.url.execute,{id:record.id}).then((res)=>{
            if(res.success){
              that.$message.success(res.message);
              that.loadData();
              that.onClearSelected();
            }else{
              that.$message.warning(res.message);
            }
          });
        }
      });
    }
  },
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';

.table-page-search-wrapper .ant-form-inline .ant-form-item {
  margin-bottom: 0 !important;
}

.ant-table-pagination.ant-pagination {
  margin: 16px 0 0 0 !important;
}

/*表头样式*/
::v-deep .ant-table-thead > tr > th {
  text-align: center;
  white-space: nowrap;
}

/*内容对齐方式、省略显示*/
::v-deep .ant-table-tbody > tr > td {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;

  &:first-child,
  &:nth-child(2),
  &:nth-child(3),
  &:nth-child(4),
  &:nth-child(5),
  &:nth-child(6),
  &:nth-child(7),
  &:nth-child(8) {
    text-align: center;
  }
}
</style>
