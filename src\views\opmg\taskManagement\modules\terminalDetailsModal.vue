<template>
  <j-modal
    :title="title"
    fullscreen
    :visible="visible"
    :confirmLoading="confirmLoading"
    :footer="null"
    @cancel="handleCancel">
    <a-spin :spinning="confirmLoading">
  <div class="page-box">
    <a-spin :spinning="loading">
      <div class="terminal-info">
        <div class="content-box">
          <descriptions
            title-margin-top="0px"
            :items="basicData"
            @triggerParentMethod="getTerminalInfo"
            :column="{ xxl: 2, xl: 2, lg: 2, md: 2, sm: 1, xs: 1 }"
          >
            <div slot="title-extension" class="title-flag-wrapper">
              <span class="title-flag">基本信息</span>
              <span class='refresh-span' @click="queryTerminalInfo">
                <a-icon class="icon" type='reload'/>刷新
              </span>
            </div>
          </descriptions>
          <terminal-log-list v-if="visible" :log-list="logList" :info="tInfo"></terminal-log-list>
        </div>
      </div>
    </a-spin>
    <terminal-info-modal ref="terminalInfoModal"></terminal-info-modal>
  </div>
    </a-spin>
  </j-modal>
</template>

<script>
import {getAction } from '@/api/manage'
import descriptions from '@comp/descriptions/descriptions.vue'
import terminalLogList from '@views/opmg/taskManagement/modules/terminalLogList.vue'
import terminalInfoModal from '@views/opmg/taskManagement/modules/terminalInfoModal.vue'

export default {
  name: 'terminalDetailsModal',
  components: {
    terminalInfoModal,
    descriptions,
    terminalLogList
  },
  data() {
    return {
      title: "",
      visible: false,
      confirmLoading: false,

      loading:false,
      tInfo:{},
      logList:[],
      basicFields: [
        {
          field: 'terminalName',
          label: '终端名称',
          comType:'triggerParentMethod',
        },
        {
          field: 'osType_dictText',
          label: '操作系统'
        },
        {
          field: 'cpuType_dictText',
          label: 'cpu类型',
        },
        {
          field: 'cpuArch',
          label: '终端架构'
        },
        {
          field: 'deptName',
          label: '所属单位'
        },
        {
          field: 'ip',
          label: 'IP地址'
        },
        {
          field: 'executeStatus_dictText',
          label: '任务执行状态'
        },
        {
          field: 'startTime',
          label: '任务执行时间'
        },
        {
          field: 'endTime',
          label: '任务结束时间'
        }
      ],
      basicData: [],

      url: {
        getTerminalById:'/software/softwareTask/getTerminalById',
        queryTerminalLog: '/software/softwareTask/getTerminalLog',
        getTerminalInfo: '/terminal/terminalDevice/getTerminalInfo'
      }
    }
  },
  methods: {
    async init(info){
      if (info.id){
        this.loading=true
        this.basicData=this.getDescriptionsItem(this.basicFields)
        this.logList=await this.getTerminalLog(info)
        this.loading=false
      }
    },
    /**
     * 获取任务相关终端基本信息
     * */
    queryTerminalInfo() {
      this.loading = true
      getAction(this.url.getTerminalById, { terminalId: this.tInfo.id }).then(res => {
        if (res.code == 200 && res.result) {
          this.tInfo = JSON.parse(JSON.stringify(res.result))
          this.init(this.tInfo)
        }else {
          this.tInfo={}
          this.basicData=[]
        }
        this.loading=false
      }).catch((err)=>{
        this.$message.error(err.message)
        this.tInfo={}
        this.basicData=[]
        this.loading=false
      })
    },
    /**
     * 获取相关终端详细信息
     * */
    getTerminalInfo() {
      let params = { uniqueCode:this.tInfo.uniqueCode}
      getAction(this.url.getTerminalInfo, params).then((res) => {
        if (res.success&&res.result&&res.result.records&&res.result.records.length>0) {
          this.$refs.terminalInfoModal.edit(res.result.records[0])
        }else {
          this.$message.warning('操作失败')
        }
      }).catch((err)=>{
        this.$message.error(err.message)
      })
    },
    /**
     * 整理所属信息、基本信息数据，以排除空数据     *
     * @param {Array} fields - 数组：元素包含字段英文名和中文名
     * */
    getDescriptionsItem(fields){
      let data=[]
      let obj=this.tInfo
      for (let i = 0; i < fields.length; i++) {
        if (obj[fields[i].field]!==''&&obj[fields[i].field]!==null){
          let item={
            label:fields[i].label,
            value:obj[fields[i].field]
          }
          if (fields[i].comType){
            item['comType'] = fields[i].comType
          }
          data.push(item)
        }
      }
      return data
    },
    /*获取终端任务执行步骤信息*/
    async getTerminalLog(info){
      let data=[]
      await getAction(this.url.queryTerminalLog,{taskId:info.taskId,uniqueCode:info.uniqueCode}).then((res)=>{
        if (res.success&&res.result&&res.result.length>0){
          let tempData=res.result
          for (let i=0;i<tempData.length;i++){
            let st="开始时间："+tempData[i].startTime
            let et=tempData[i].endTime?" ~ 结束时间："+tempData[i].endTime:''
            let sta=tempData[i].executeResult==="success"?"finish":'error'
            let log=tempData[i].executeLog?tempData[i].executeLog:''
            let exeSta=tempData[i].executeStatusText

            let item={
              status:sta,
              title:tempData[i].message,
              subTitle:'',
              time:st+et,
              log:log,
              exeSta:exeSta
            }
            data.push(item)
          }
        }
      })
      return data
    },

    edit(record) {
      this.visible = true
      this.tInfo=record
      this.init(record)
    },
    handleCancel() {
      this.visible = false;
    },
  }
}
</script>

<style scoped lang='less'>
.page-box {
  border-radius: 3px;
  background: #fff;
  padding: 24px;
  height: 100% !important;
  width:100%;
  display: flex;
  flex-flow: column nowrap;
  overflow: hidden;

  ::v-deep .ant-spin-nested-loading{
    height: 100%;
    .ant-spin-container{
      height: 100%;
    }
  }
}

.header-info {
  display: flex;
  justify-content: space-between;
  align-items: start;
  margin-bottom: 16px;
  .action-box{
    //margin-right: 16px;

    .action,.action:hover,.action:focus{
      color: #4b90de;
      font-size: 14px;
      cursor: pointer;
      margin-right: 12px;
      box-shadow: none !important;
      border: none !important;
      background: transparent !important;
    }
  }
  .header-back {
    img{
      width: 20px;
      height: 20px;
      cursor: pointer
    }
  }
}

.terminal-info {
  height: calc(100% - 37px);

  .content-box {
    height:100%;
    padding-right:1px;
    overflow: hidden !important;
    overflow-y: auto !important;
  }
  .title-flag-wrapper {
    margin-bottom: 12px;
    .title-flag {
      padding-left: 7px;
      font-size: 14px;
      border-left-width: 4px;
      border-left-style: solid;
      color: rgba(0,0,0,0.85);
    }
  }
  .refresh-span{
    font-size: 14px;
    margin-left: 12px;
    cursor: pointer;
    color: #4b90de;
    .icon{
      margin-right: 4px;
    }
  }
}
</style>
