<template>
  <div class='char-content'>
    <div id='compareRadar' ref='radarChart'></div>
  </div>
</template>
<script>
import ZrBigscreenTitle from '@views/zrBigscreens/modules/ZrBigscreenTitle.vue'
import resizeObserverMixin from '@views/statsCenter/com/resizeObserverMixin'
import{mapBackIcon,simpleBackIcon} from '@views/zrBigscreens/modules/zrUtil'
export default {
  name: 'ZrOpCompareRadar',
  components: { ZrBigscreenTitle },
  mixins: [resizeObserverMixin],
  props:{
    category:{
      type:String,
      default:''
    },
    leftData:{
      type:Array,
      default:()=>[]
    },
    rightData:{
      type:Array,
      default:()=>[]
    }
  },
  data() {
    return {
      chart: null,
      radarData:[
        // { name: '基础设施', value: 100,level: 'A',type:"jzss" },
        // { name: '数据资源', value: 100,level: 'A' ,type:"sjzy" },
        // { name: '网络通联', value: 80,level: 'B' ,type:"wltl" },
        // { name: '应用服务', value: 80,level: 'B' ,type:"yyfw" },
        // { name: '运维保障', value: 75,level: 'C' ,type:"ywbz" },
        // { name: '质量效益', value: 60,level: 'D' ,type:"zlxy" }
      ], radarData1:[
        // { name: '基础设施', value: 80,level: 'B' ,type:"jzss" },
        // { name: '数据资源', value: 100,level: 'A' ,type:"sjzy" },
        // { name: '网络通联', value: 80,level: 'B' ,type:"wltl" },
        // { name: '应用服务', value: 75,level: 'C' ,type:"yyfw" },
        // { name: '运维保障', value: 80,level: 'B' ,type:"ywbz" },
        // { name: '质量效益', value: 100,level: 'A' ,type:"zlxy" }
      ],
      scoreMap: {
        'A': 100,
        'B': 80,
        'C': 60,
        'D': 45,
      },
    }
  },
  mounted() {
    this.chart = this.$echarts.init(this.$refs.radarChart)
    this.setRadarData("radarData",this.leftData)
    this.setRadarData("radarData1",this.rightData)
    this.initRadarChart()
  },
  watch:{
    leftData:{
      handler(newVal,oldVal){
        // console.log("leftData 雷达 ",newVal)
        if(newVal){
          this.setRadarData("radarData",newVal)
        }else{
          this.radarData = []
        }
        this.initRadarChart()
      },
      deep:true,
      immediate:false
    },
    rightData:{
      handler(newVal,oldVal){
        // console.log("rightData 雷达 ",newVal)
        if(newVal){
          this.setRadarData("radarData1",newVal)
        }else{
          this.radarData1 = []
        }

        this.initRadarChart()
      },
      deep:true,
      immediate:false
    }
  },
  methods: {
    //设置雷达数据
    setRadarData(key,list){
      this[key] = list.map(el=>{
        return {
          name:el.typeName,
          value:el.metricsTypeResult?this.scoreMap[el.metricsTypeResult]:0,
          level:el.metricsTypeResult || "--",
          type:el.id
        }
      })
    },
    // 初始化雷达图
    initRadarChart() {
      let that = this;
      if(this.radarData.length==0 || this.radarData1.length==0)return;
      let option = {
        color: ['#89F7FE', '#1C7BFF'],
        legend: {
          top:20,
          textStyle:{
            color:'#fff'
          },
          data: ['2025-07', '2024-12']
        },
        toolbox: {
          show: true,
          orient: 'vertical',
          left: 'right',
          top: 'top',
          feature: {
            myBack: {
              show: true,
              title: "返回",
              iconStyle: {
                color: "rgba(255, 255, 255, 0.5)",
                borderWidth: 0,
              },
              emphasis:{
                iconStyle: {
                  color: "rgba(255, 255, 255, 0.8)",
                },
              },
              icon: mapBackIcon,
              onclick: function() {
                that.backMapHandler()
              }
            }
          }
        },
        //工具按钮
        tooltip: {
          trigger: 'item',
          formatter: (params) => {
            let tData = params.data.radarData;
            if(!tData){return ""}
            let str = '';
            tData.forEach(item => {
              str += `<span style='color:#ccc'>${item.name}</span>： ${item.level}<br/>`;
            });
            return `${params.data.name}<br/>${str}`;
          }
        },
        radar: [
          {
            //指标
            indicator: this.radarData.map(item => ({ name: item.name, max: 100,color:this.category===item.type?"rgba(250, 226, 70, 0.9)":'rgba(255, 255, 255, 1)'})),
            center: ['50%', '50%'],
            radius:"75%",
            startAngle: 90,
            splitNumber: 4,
            shape: 'polygon',
            nameGap: 5,
            axisLabel: {
              color: 'rgba(255, 255, 255, 1)',
            },
            splitArea: {
              areaStyle: {
                color: ['#0080CD', '#015199', '#023769', 'rgba(56,154,255,0.2)'],
                shadowColor: 'rgba(0, 0, 0, 0.2)',
                shadowBlur: 0
              }
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: 'rgba(211, 253, 250, 0.8)'
              }
            },
            splitLine: {
              show: false,
              lineStyle: {
                color: 'rgba(211, 253, 250, 0.8)'
              }
            }
          },
        ],
        series: [
          {
            type: 'radar',
            symbolSize: 8,
            emphasis: {
              lineStyle: {
                width: 4
              }
            },
            data: [
              {
                radarData:this.radarData,
                value: this.radarData.map(item => item.value),
                name: '2025-07'
              },
              {
                radarData:this.radarData1,
                value: this.radarData1.map(item => item.value),
                name: '2024-12'
              },
            ]
          }
        ]
      }
      this.chart.setOption(option)
    },
    backMapHandler() {
      this.$emit('cancelCompare')
    },
    resize() {
      if (this.chart) {
        this.chart.resize()
      }
    },
    resizeObserverCb() {
     this.resize()
    }
  }
}
</script>

<style scoped lang='less'>
.char-content {
  width: 100%;
  height: 100%;
  position: relative;
  #compareRadar {
    width: 100%;
    height: 100%;
  }
}
</style>