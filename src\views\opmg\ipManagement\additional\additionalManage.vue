<template>
  <div style="height:100%">
    <keep-alive exclude='additionalDetails'>
      <component style="height:100%" :is="pageName" :data="data" />
    </keep-alive>
  </div>
</template>
<script>
  import additionalList from './additionalList'
  import additionalDetails from './modules/additionalDetails'
  export default {
    name: "additionalManage",
    components: {
      additionalList,
      additionalDetails
    },
    data() {
      return {
        isActive: 0,
        data: {},
      }
    },
    created() {
      this.pButton1(0);
    },
    //使用计算属性
    computed: {
      pageName() {
        switch (this.isActive) {
          case 0:
            return "additionalList";
          default:
            return "additionalDetails";
        }
      }
    },
    methods: {
      pButton1(index) {
        this.isActive = index;
      },
      pButton2(index, item) {
        this.isActive = index;
        this.data = item
      }
    }
  }
</script>