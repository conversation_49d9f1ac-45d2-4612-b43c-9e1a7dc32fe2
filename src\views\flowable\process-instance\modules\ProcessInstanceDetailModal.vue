<template>
  <j-modal
    :title="title"
    :width="1200"
    :visible='visible'
    :maskClosable='false'
    :destroyOnClose="true"
    :confirmLoading='confirmLoading'
    @cancel="handleCancel"
    cancelText="关闭"
  >
    <a-spin :spinning="confirmLoading">
      <a-descriptions bordered title="" size="small">
        <a-descriptions-item label="流程实例ID">{{ model.id }}</a-descriptions-item>
        <a-descriptions-item label="业务主键Key">{{ model.businessKey }}</a-descriptions-item>
        <a-descriptions-item label="流程ID">{{ model.processDefinitionId }}</a-descriptions-item>
        <a-descriptions-item label="流程名称">{{ model.processDefinitionName }}</a-descriptions-item>
        <a-descriptions-item label="流程编码">{{ model.processDefinitionKey }}</a-descriptions-item>
        <a-descriptions-item label="流程版本">{{ model.processDefinitionVersion }}</a-descriptions-item>
        <a-descriptions-item label="开始时间">{{ model.startTime }}</a-descriptions-item>
        <a-descriptions-item label="结束时间">{{ model.endTime }}</a-descriptions-item>
        <a-descriptions-item label="启动人ID">{{ model.startUserId }}</a-descriptions-item>
        <a-descriptions-item label="启动人姓名">{{ model.startUserName }}</a-descriptions-item>
        <a-descriptions-item label="启动节点ID">{{ model.startActivityId }}</a-descriptions-item>
        <a-descriptions-item label="耗时(毫秒)">{{ model.durationInMillis }}</a-descriptions-item>
        <a-descriptions-item label="子流程ID">{{ model.superProcessInstanceId }}</a-descriptions-item>
        <a-descriptions-item label="状态">
          <a-badge status="success" text="激活" v-if="!model.suspended" />
          <a-badge status="error" text="挂起" v-else />
        </a-descriptions-item>
      </a-descriptions>
    </a-spin>
    <template slot="footer">
      <a-button @click="handleCancel">关闭</a-button>
    </template>
  </j-modal>
</template>

<script>
import { getAction } from '@/api/manage'

export default {
  name: 'ProcessInstanceDetailModal',
  data() {
    return {
      title: '操作',
      visible: false,
      model: {},
      layout: {
        labelCol: { span: 10 },
        wrapperCol: { span: 14 },
      },
      labelCol: {
        span: 6,
      },
      wrapperCol: {
        span: 18,
      },
      confirmLoading: false,
      form: this.$form.createForm(this),
    }
  },
  created() {},
  methods: {
    edit(record) {
      getAction('/flowable/processInstance/queryById', { processInstanceId: record.id }).then((res) => {
        const data = res.result
        console.log(data)
        this.model = Object.assign({}, data)
        console.log(data)
        //this.model = Object.assign({}, record);
        this.visible = true
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleCancel() {
      this.close()
    },
  },
}
</script>

<style lang='less' scoped>
@import '~@assets/less/YQNormalModal.less';
</style>