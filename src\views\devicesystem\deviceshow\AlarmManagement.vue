<template>
  <div :class='{"alarm-list":pageName==="AlarmList","alarm-view":pageName==="AlarmTemplateInfoModal"}'>
     <keep-alive>
       <component :is="pageName" :alarm-info="alarmInfo" :device-info='deviceInfo'  :show-flag='false'/>
     </keep-alive>
  </div>
</template>
<script>
import alarmList from './AlarmList'
import alarmTemplateInfoModal from '@comp/alarmTemplate/AlarmTemplateInfoModal'
export default {
  name: "AlarmManage",
  data() {
    return {
      isActive: 0,
      alarmInfo:{},//告警信息
      deviceInfo:{},//设备信息
    };
  },
  components: {
    alarmList,
    alarmTemplateInfoModal
  },
  created(){
    this.pButton1(0);
  },
  //使用计算属性
  computed: {
    pageName() {
      switch (this.isActive) {
        case 0:
          return "alarmList";
          break;

        default:
          return "alarmTemplateInfoModal";
          break;
      }
    }
  },
  methods: {
    show(index,item) {
      this.isActive = index;
      this.deviceInfo = item;
    },
    pButton1(index) {
      this.isActive = index;
    },
    pButton2(index,item) {
      this.isActive = index;
      this.alarmInfo = item;
    }
  }
}
</script>
<style scoped>
.alarm-list{
  height: 100%;
  padding-right: 12px;
}
.alarm-view{
  height: 100%;
  //margin-left: -24px;
  margin-top: -24px;
  margin-bottom: -24px;
}
</style>