<template>
  <div style="width: 98%;height: 74%;" id="chartCom"></div>
</template>

<script>
  import {
    getAction
  } from '@/api/manage'
  export default {
    props: {
      url: {
        type: String,
        default: ''
      },
      barGap: {
        type: String,
        default: ''
      }
    },
    mounted() {
      this.getChartData()
    },
    methods: {
      getChartData() {
        getAction(this.url).then((res) => {
          if (res.code == 200) {
            this.chartCom(res.result, res.result.line)
          }
        })
      },
      chartCom(data, dataList) {
        let xArr = []
        let yValue1 = []
        let yValue2 = []
        dataList.forEach((e) => {
          xArr.push(e.name)
          yValue1.push(e.value1)
          yValue2.push(e.value2)
        })

        let myChart = this.$echarts.init(document.getElementById('chartCom'))
        myChart.setOption({
          legend: {
            data: [data.value1, data.value2],
            left: 'right',
            textStyle: {
              color: '#c4e4fd',
            },
          },
          tooltip: {
            type: true,
            transitionDuration: 0, //echart防止tooltip的抖动
          },
          grid: {
            left: '3%',
            right: 10,
            bottom: '',
            top: '14%',
            containLabel: true,
          },
          xAxis: {
            type: 'category',
            data: xArr,
            axisLine: {
              lineStyle: {
                type: 'solid',
                color: '#5a595f', //左边线的颜色
                width: '1', //坐标线的宽度
              },
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: '#fff',
              },
            },
          },
          yAxis: {
            type: 'value',
            axisLine: {
              show: false, //y轴线消失
              lineStyle: {
                //y轴字体颜色
                color: '#c4c4c6',
              },
            },
            axisTick: {
              show: false,
            },
            //网格线颜色
            splitLine: {
              show: true,
              lineStyle: {
                color: ['#424348'],
                width: 1,
                type: 'solid',
              },
            },
          },
          series: [{
              // 上半截柱子
              name: data.value1,
              type: 'bar',
              barGap: this.barGap,
              barWidth: '10',
              z: 0,
              itemStyle: {
                color: '#009eff',
              },
              data: yValue1,
            },
            {
              // 下半截柱子
              name: data.value2,
              type: 'bar',
              barGap: this.barGap,
              barWidth: '10',
              // z: 0,
              itemStyle: {
                color: '#4bffdc',
              },
              data: yValue2,
            },
          ],
        })
        window.addEventListener('resize', () => {
          myChart.resize()
        })
      },
    }
  }
</script>

<style>

</style>