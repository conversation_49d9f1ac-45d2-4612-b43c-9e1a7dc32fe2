<template>
  <div class='zr-business-view' ref='zrBusinessView'>
    <div class='business-view-left'>
      <div v-if='!comparing' style='height: 100%;width: 100%'>
<!--        <div class='business-view-left-top' ref='lefTop' style='height: 45%'>-->
<!--          <zr-evaluation-category-rank></zr-evaluation-category-rank>-->
<!--        </div>-->
<!--        <div style='height: 55%' class='business-view-left-bottom'>-->
<!--          <zr-completion-status></zr-completion-status>-->
<!--        </div>-->
        <zr-op-left-list></zr-op-left-list>
      </div>
      <zr-op-compare v-else :valuationDates='valuationDates' :init-date='valuationDates[0].value' :indicators='leftIndicator'></zr-op-compare>
    </div>
    <div class='business-view-center'>
      <div v-if='!comparing' class="header-right-time">
        <span class='time-range-label'>评估日期:</span>
        <div class="time-range-span">
          <a-select v-model='timeRange'
                    style='width: 180px'
                    placeholder='请选择评估日期'
                    :getPopupContainer="(node) => node.parentNode"
                    :dropdownClassName='"custom-select-dropdown"'
                    :allow-clear='false'>
            <a-select-option v-for="item in valuationDates" :value="item.value" :key="'visibility_'+item.value" :label="item.label">
              {{ item.label }}
            </a-select-option>
          </a-select>
<!--          <a-range-picker
            dropdownClassName="big-screen-range-picker"
            v-model="timeRange"
            size='small'
            @change="timeRangeChange"/>-->
        </div>
        <div class='report-btn' @click='handleReport'>
          评估报告
        </div>
      </div>
      <zr-op-center v-if='!comparing' @startComparing='changeComparing' @categoryComparing='changeCateGory'></zr-op-center>
      <zr-op-compare-radar v-else @cancelCompare='changeComparing' :category='curCategory'></zr-op-compare-radar>
    </div>
    <div class='business-view-right'>
      <div v-if='!comparing' style='width: 100%;height: 100%'>
        <div  class='business-view-right-top' :style='{height:"45%"}'>
          <zr-evaluation-indicators></zr-evaluation-indicators>
        </div>
        <div class='business-view-right-bottom' ref='rightBottom' style='height: 55%'>
          <zr-focus-alert></zr-focus-alert>
        </div>
      </div>
      <zr-op-compare v-else :valuationDates='valuationDates' :init-date='valuationDates[1].value' :indicators='rightIndicator'></zr-op-compare>
    </div>
  </div>
</template>
<script>
import resizeObserverMixin from '@views/statsCenter/com/resizeObserverMixin'
import ZrEvaluationCategoryRank from '@views/zrBigscreenStatic/zrOperationalEvaluation/modules/ZrEvaluationCategoryRank.vue'
import ZrCompletionStatus from '@views/zrBigscreenStatic/zrOperationalEvaluation/modules/ZrCompletionStatus.vue'
import ZrEvaluationIndicators from '@views/zrBigscreenStatic/zrOperationalEvaluation/modules/ZrEvaluationIndicators.vue'
import ZrFocusAlert from '@views/zrBigscreenStatic/zrOperationalEvaluation/modules/ZrFocusAlert.vue'
import ZrOpCenter from '@views/zrBigscreenStatic/zrOperationalEvaluation/modules/ZrOpCenter.vue'
import ZrOpCompareRadar from '@views/zrBigscreenStatic/zrOperationalEvaluation/modules/ZrOpCompareRadar.vue'
import { downloadFile } from '@/api/manage'
import { indicators } from '@views/zrBigscreenStatic/modules/zrUtil'
import ZrOpCompare from './modules/ZrOpCompare.vue'
import ZrOpLeftList from '@views/zrBigscreenStatic/zrOperationalEvaluation/modules/ZrOpLeftList.vue'
export default {
  name: 'businessIndex',
  components: {
    ZrOpLeftList,
    ZrOpCompare,
    ZrOpCenter,
    ZrEvaluationCategoryRank,
    ZrCompletionStatus,
    ZrEvaluationIndicators,
    ZrFocusAlert,
    ZrOpCompareRadar
  },
  mixins: [resizeObserverMixin],
  data() {
    return {
      centerH: '',
      centerPd: "",
      hScaleValue: 1, // 用于缩放比例
      timeRange: '2025-07',
      valuationDates: [
        { label: '2025-07', value: '2025-07' },
          { label: '2024-12', value: '2024-12' },
          { label: '2024-08', value: '2024-08' },
      ],
      comparing:false,
      leftIndicator:[],
      rightIndicator:[],
      curCategory:'',
    }
  },
  mounted() {
  },
  methods: {
    //生成指标数据
    generateIndicators(){
      const list = window.zrIndicators || indicators
      let tem = []
      for(let i = 0 ; i<list.length;i++){
        let score =  Math.floor(Math.random() * (100 - 40 + 1)) + 40;
        let level
        if(score>=90){
          level = "A"
        }else if(score>75&&score<90){
          level= "B"
        }else if(score>60&&score<=75){
          level= "C"
        }else{
          level= "D"
        }
        tem.push({
          name:indicators[i].name,
          score,
          level,
          type:indicators[i].type
        })
      }
      return tem;
    },
    //选择指标类
    changeCateGory(type){
      this.curCategory = type;
      this.changeComparing()
    },
    //改变对比状态
    changeComparing(){
      this.comparing = !this.comparing
      if(this.comparing){
        this.leftIndicator =  this.curCategory?this.generateIndicators().filter(el=>el.type==this.curCategory):this.generateIndicators()
        this.rightIndicator = this.curCategory?this.generateIndicators().filter(el=>el.type==this.curCategory):this.generateIndicators()
        for(let i=0; i<this.leftIndicator.length;i++){
          // 定义等级对应的数值
          const levelValues = { 'A': 4, 'B': 3, 'C': 2, 'D': 1 };
          const leftLevelValue = levelValues[this.leftIndicator[i].level];
          const rightLevelValue = levelValues[this.rightIndicator[i].level];

          if(leftLevelValue < rightLevelValue){
            this.leftIndicator[i].color = "#FF1D42"
          }else if(leftLevelValue > rightLevelValue){
            this.rightIndicator[i].color = "#FF1D42"
          }else{
            // 等级相同时，左右都设置为灰色示例，可按需修改
            this.leftIndicator[i].color = "#fff"
            this.rightIndicator[i].color = "#fff"
          }
        }
      }else{
        this.leftIndicator =[]
        this.rightIndicator = []
        this.curCategory = ''
      }
    },
    //查看评估报告
    handleReport() {
      //  console.log('查看评估报告', record)
      this.$message.warning('评估报告暂未开放')
      return
      const ext =  'word' ;
      let fileName = `${record.projectName}评估报告.${ext}`;
      downloadFile('/evaluate/projectInfo/exportReport',fileName, { id: record.id, exportType: type })
        .then(res => {
        }).catch(err => {
        this.$message.error('获取评估报告失败')
      })
    },

    timeRangeChange(value) {
      console.log('选择的时间范围:', value)
    },
    //监听页面缩放 更新中间区域高度
    resizeObserverCb() {
      this.hScaleValue = window.innerHeight / 1080
      this.centerH = `calc(100% - (70px * ${this.hScaleValue}))`
      // this.centerPd = `calc(100px * ${this.hScaleValue})`
    }
  }
}
</script>

<style scoped lang='less'>
@import "~@assets/less/onclickStyle.less";
::v-deep .ant-calendar-picker {
  width: calc(320 / 19.2 * 1vw);
  max-width: 320px;
  height: 100%;
  .ant-calendar-picker-input.ant-input {
    background-color: #021527;
    color: #909090;
    height: 100%;
    display: flex;
    align-items: center;

    .ant-calendar-range-picker-separator {
      color: #feffff;
      line-height: 30px;
    }
  }
  input::placeholder {
    color: #feffff !important;
    font-size: 14px;
  }
}

.zr-business-view {
  padding-left: calc(33 / 19.2 * 1vw);
  padding-right: calc(33 / 19.2 * 1vw);
  display: flex;
  align-items: center;
  justify-content: space-around;
  height: 100%;

  .business-view-left {
    width: 25%;
    height: 100%;
    //background-image: url(/zrBigScreen/zrBusiness/businessLeftBg.png);
    //background-size: 100% 100%;
    //background-repeat: no-repeat;
    //padding: 42px 41px 42px 24px;
    display: flex;
    flex-direction: column;

    .business-view-left-bottom {
      display: flex;
      flex-direction: column;

      .business-view-left-middle {
        height: 50%
      }

      .business-view-left-bottom {
        height: calc(50% - 8px);
      }
    }
  }

  .business-view-center {
    width: 50%;
    height: 100%;
    //background-image: url(/zrBigScreen/zrBusiness/businessCenterBg.png);
    //background-size: 100% 100%;
    //background-repeat: no-repeat;
    padding: 0px 16px;
    position: relative;
    .header-right-time {
      position: absolute;
      top: 24px;
      left:50%;
      transform: translateX(-50%);
      z-index: 10;
      display: flex;
      align-items: center;
      .time-range-label {
        font-weight: 400;
        font-size: 14px;
        color: #FFFFFF;
        opacity: 0.8;
        width: 62px;
      }
      .time-range-span {
        margin-left: 8px;
      }
      .report-btn{
        height: 30px;
        //line-height: 30px;
        color: rgba(255, 255, 255, 0.7);
        border: 1px solid rgba(255, 255, 255, 0.7);
        padding:0 8px;
        border-radius: 4px;
        margin-left: 12px;
        display: flex;
        align-items: center;
        cursor: pointer;
        &:hover{
          color: rgba(255, 255, 255, 1);
          border: 1px solid rgba(255, 255, 255, 1);
        }
      }
    }
  }

  .business-view-right {
    width: 25%;
    height: 100%;
    //background-image: url(/zrBigScreen/zrBusiness/businessRightBg.png);
    //background-size: 100% 100%;
    //background-repeat: no-repeat;
    //padding: 42px 24px 42px 41px;
    display: flex;
    flex-direction: column;
  }
}
</style>