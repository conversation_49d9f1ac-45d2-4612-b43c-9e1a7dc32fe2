<template>
  <div class="body">
    <div class="left">
      <div class="left-top">
        <div class="topTitle" style="justify-content: space-between">
          <div>
            <img src="@/assets/bigScreen/9.png" alt="" />
            <span>告警数量统计</span>
            <span :class="buttonStyle" @click="timeChange" v-show="!monthJudge">近7天</span>
            <span :class="buttonStyle1" @click="timeChange1" v-show="!monthJudge">近30天</span>
            <span :class="buttonStyle2" @click="timeChange2" v-show="monthJudge">近半年</span>
            <span :class="buttonStyle3" @click="timeChange3" v-show="monthJudge">近一年</span>
          </div>
          <div style="margin-right:15px">单位:
            <span :class="dayStyle" @click="dayChange">天</span>
            <span :class="monthStyle" @click="monthChange">月</span>
          </div>
        </div>
        <div class="left-top-body">
          <div class="left-top-body-histogram" id="warningSituationHistogram"></div>
        </div>
      </div>
      <div class="left-bottom">
        <div class="left-bottom-top">
          <div class="topTitle">
            <img src="@/assets/bigScreen/9.png" alt="" />
            <span>历史告警</span>
          </div>
        </div>
        <div class="left-bottom-body">
          <div class="left-bottom-tableTitle">
            <span>告警时间</span>
            <span>设备名称</span>
            <span>告警级别</span>
            <span>告警名称</span>
          </div>
          <div class="left-bottom-table" @mousewheel="historyMouseWheel">
            <vue-seamless-scroll :data="warningData" :class-option="warning" ref="seamlessHistory"
              class="seamless-warp">
              <div v-for="(array, index) in warningData" :key="index">
                <span :title="array.create_time"> {{ array.create_time }} </span>
                <span :title="array.device_name"> {{ array.device_name }} </span>
                <span :title="array.alarm_level"> {{ array.alarm_level }} </span>
                <span :title="array.template_name"> {{ array.template_name }} </span>
              </div>
            </vue-seamless-scroll>
          </div>
        </div>
      </div>
    </div>
    <div class="core">
      <div class="core-top">
        <div class="topTitle">
          <img src="@/assets/bigScreen/9.png" alt="" />
          <span>告警情况</span>
        </div>
        <div class="core-top-body">
          <div class="core-top-body-item-one">
            <div class="core-top-body-item-left">
              <img src="@/assets/bigScreen/24.png" alt="" />
            </div>
            <div class="core-top-body-item-right">
              <span>总告警数量</span>
              <animate-number from="0" :to="totalAlarm" :key="totalAlarm" duration="2000"></animate-number>
            </div>
          </div>
          <div class="core-top-body-item-two">
            <div class="core-top-body-item-left">
              <img src="@/assets/bigScreen/25.png" alt="" />
            </div>
            <div class="core-top-body-item-right">
              <span>解决告警数量</span>
              <animate-number from="0" :to="totalSolve" :key="totalSolve" duration="2000"></animate-number>
            </div>
          </div>
          <div class="core-top-body-item-three">
            <div class="core-top-body-item-left">
              <img src="@/assets/bigScreen/26.png" alt="" />
            </div>
            <div class="core-top-body-item-right">
              <span>今日告警数量</span>
              <animate-number from="0" :to="todayAlarm" :key="todayAlarm" duration="2000"></animate-number>
            </div>
          </div>
          <div class="core-top-body-item-four">
            <div class="core-top-body-item-left">
              <img src="@/assets/bigScreen/27.png" alt="" />
            </div>
            <div class="core-top-body-item-right">
              <span>今日解决告警数量</span>
              <animate-number from="0" :to="todaySolve" :key="todaySolve" duration="2000"></animate-number>
            </div>
          </div>
          <div class="core-top-body-item-five">
            <div class="core-top-body-item-left">
              <img src="@/assets/bigScreen/25.png" alt="" />
            </div>
            <div class="core-top-body-item-right">
              <span>解决数量百分比</span>
              <div>
                <animate-number from="0" :to="totalPercent" :key="totalPercent" duration="2000"
                  style="font-size:0.325rem;color:#fec900"></animate-number>
                <span style="margin-left:5px">%</span>
              </div>
            </div>
          </div>
          <div class="core-top-body-item-six">
            <div class="core-top-body-item-left">
              <img src="@/assets/bigScreen/27.png" alt="" />
            </div>
            <div class="core-top-body-item-right">
              <span>今日解决数量百分比</span>
              <div>
                <animate-number from="0" :to="todayPercent" :key="todayPercent" duration="2000"
                  style="font-size:0.325rem;color:#fec900"></animate-number>
                <span style="margin-left:5px">%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="core-core">
        <div class="topTitle">
          <img src="@/assets/bigScreen/9.png" alt="" />
          <span>10日内告警产品统计</span>
        </div>
        <div class="core-core-body" id="warningResourcesPieChart"></div>
      </div>
    </div>
    <div class="right">
      <div class="right-top">
        <div class="topTitle">
          <img src="@/assets/bigScreen/9.png" alt="" />
          <span>实时告警</span>
        </div>
        <div class="right-top-body">
          <div class="right-top-body-tabel" @mousewheel="handleMouseWheel">
            <vue-seamless-scroll :data="realTimeAlarmData" :class-option="realTimeWarning" ref="seamlessDiv"
              class="seamless-scroll">
              <ul>
                <li v-for="(item, index) in realTimeAlarmData" :key="index" class="tabel-item">
                  <div class="tabel-item-left" :style="{color: item.color}">
                    [{{ item.alarmLevelName }}]
                  </div>
                  <div class="tabel-item-right">{{ item.info }}</div>
                </li>
              </ul>
            </vue-seamless-scroll>
          </div>
        </div>
      </div>
      <div class="right-bottom">
        <div class="topTitle" style="justify-content: space-between">
          <div>
            <img src="@/assets/bigScreen/9.png" alt="" />
            <span>告警级别趋势分析</span>
            <span :class="buttonClass" @click="alarmChange" v-show="!lineMonthJudge">近7天</span>
            <span :class="buttonClass1" @click="alarmChange1" v-show="!lineMonthJudge">近30天</span>
            <span :class="buttonClass2" @click="alarmChange2" v-show="lineMonthJudge">近半年</span>
            <span :class="buttonClass3" @click="alarmChange3" v-show="lineMonthJudge">近一年</span>
          </div>
          <div style="margin-right:15px">单位:
            <span :class="lineDayStyle" @click="lineDayChange">天</span>
            <span :class="lineMonthStyle" @click="lineMonthChange">月</span>
          </div>
        </div>
        <div class="right-bottom-body">
          <div class="right-bottom-body-lineChart" id="warningTrendLineChart"></div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  import echarts from 'echarts/lib/echarts'
  import vueSeamlessScroll from 'vue-seamless-scroll'
  import {
    deleteAction,
    getAction,
    putAction,
    httpAction
  } from '@/api/manage'
  export default {
    components: {
      vueSeamlessScroll,
    },
    data() {
      return {
        monthJudge: false,
        lineMonthJudge: false,
        warningData: [],
        buttonStyle: 'button_style',
        buttonStyle1: 'button_style',
        buttonStyle2: 'button_style',
        buttonStyle3: 'button_style',
        buttonClass: 'button_style',
        buttonClass1: 'button_style',
        buttonClass2: 'button_style',
        buttonClass3: 'button_style',
        monthStyle: 'button_style',
        dayStyle: 'button_style',
        lineDayStyle: 'button_style',
        lineMonthStyle: 'button_style',
        todayAlarm: 0,
        todaySolve: 0,
        totalAlarm: 0,
        totalSolve: 0,
        totalPercent: 0,
        todayPercent: 0,
        realTimeAlarmData: [],
        simpleModel: !window._CONFIG['simpleModel'],
        url: {
          countDays: '/data-analysis/alarm/count/date',
          alarmHistory: '/data-analysis/alarm/history',
          alarmOverview: '/data-analysis/alarm/overview',
          alarmDeviceDays: '/data-analysis/alarm/device/days',
          alarmRealtime: '/data-analysis/alarm/realtime',
          alarmTrend: '/data-analysis/alarm/alarmLevelTrend',
        },
      }
    },
    computed: {
      warning() {
        return {
          step: this.simpleModel ? 0.2 : 0, // 数值越大速度滚动越快
          limitMoveNum: 4, // 开始无缝滚动的数据量 this.dataList.length
          hoverStop: true, // 是否开启鼠标悬停stop
          direction: 1, // 0向下 1向上 2向左 3向右
          // openWatch: true, // 开启数据实时监控刷新dom
          singleHeight: 86, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
          // singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
          waitTime: 2, // 单步运动停止的时间(默认值1000ms)
        }
      },
      realTimeWarning() {
        return {
          step: this.simpleModel ? 0.2 : 0, // 数值越大速度滚动越快
          limitMoveNum: 6, // 开始无缝滚动的数据量 this.dataList.length
          hoverStop: true, // 是否开启鼠标悬停stop
          direction: 1, // 0向下 1向上 2向左 3向右
          // openWatch: true, // 开启数据实时监控刷新dom
          singleHeight: 50, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
          // singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
          waitTime: 2, // 单步运动停止的时间(默认值1000ms)
        }
      },
    },
    created() {},
    mounted() {
      this.alarmList()
      this.dayChange()
      this.alarmOverview()
      this.alarmDeviceDays()
      this.alarmRealtime()
      this.lineDayChange()
    },
    methods: {
      // 解决在内容向上滑动后鼠标滑轮不能向上查看内容问题
      handleMouseWheel(e) {
        if (Math.abs(this.$refs.seamlessDiv.yPos) < this.$refs.seamlessDiv.realBoxHeight / 2 || e.deltaY < 0) {
          this.$refs.seamlessDiv.yPos -= e.deltaY;
          this.$refs.seamlessDiv.yPos = this.$refs.seamlessDiv.yPos >
            0 ? 0 : this.$refs.seamlessDiv.yPos;
        }
      },
      historyMouseWheel(e) {
        if (Math.abs(this.$refs.seamlessHistory.yPos) < this.$refs.seamlessHistory.realBoxHeight / 2 || e.deltaY < 0) {
          this.$refs.seamlessHistory.yPos -= e.deltaY;
          this.$refs.seamlessHistory.yPos = this.$refs.seamlessHistory.yPos >
            0 ? 0 : this.$refs.seamlessHistory.yPos;
        }
      },
      dayChange() {
        this.monthJudge = false
        this.dayStyle = 'button_style1'
        this.monthStyle = 'button_style'
        this.timeChange()
      },
      monthChange() {
        this.monthJudge = true
        this.dayStyle = 'button_style'
        this.monthStyle = 'button_style1'
        this.timeChange2()
      },
      timeChange() {
        this.countDays(7)
        this.buttonStyle = 'button_style1'
        this.buttonStyle1 = 'button_style'
        this.buttonStyle2 = 'button_style'
        this.buttonStyle3 = 'button_style'

      },
      timeChange1() {
        this.countDays(30)
        this.buttonStyle = 'button_style'
        this.buttonStyle1 = 'button_style1'
        this.buttonStyle2 = 'button_style'
        this.buttonStyle3 = 'button_style'
      },
      timeChange2() {
        this.countDays1(6)
        this.buttonStyle = 'button_style'
        this.buttonStyle1 = 'button_style'
        this.buttonStyle2 = 'button_style1'
        this.buttonStyle3 = 'button_style'
      },
      timeChange3() {
        this.countDays1(12)
        this.buttonStyle = 'button_style'
        this.buttonStyle1 = 'button_style'
        this.buttonStyle2 = 'button_style'
        this.buttonStyle3 = 'button_style1'
      },
      lineDayChange() {
        this.lineMonthJudge = false
        this.lineDayStyle = 'button_style1'
        this.lineMonthStyle = 'button_style'
        this.alarmChange()
      },
      lineMonthChange() {
        this.lineMonthJudge = true
        this.lineDayStyle = 'button_style'
        this.lineMonthStyle = 'button_style1'
        this.alarmChange2()
      },
      alarmChange() {
        this.alarmTrend(7)
        this.buttonClass = 'button_style1'
        this.buttonClass1 = 'button_style'
        this.buttonClass2 = 'button_style'
        this.buttonClass3 = 'button_style'

      },
      alarmChange1() {
        this.alarmTrend(30)
        this.buttonClass = 'button_style'
        this.buttonClass1 = 'button_style1'
        this.buttonClass2 = 'button_style'
        this.buttonClass3 = 'button_style'
      },
      alarmChange2() {
        this.alarmTrend1(6)
        this.buttonClass = 'button_style'
        this.buttonClass1 = 'button_style'
        this.buttonClass2 = 'button_style1'
        this.buttonClass3 = 'button_style'
      },
      alarmChange3() {
        this.alarmTrend1(12)
        this.buttonClass = 'button_style'
        this.buttonClass1 = 'button_style'
        this.buttonClass2 = 'button_style'
        this.buttonClass3 = 'button_style1'
      },
      // 告警情况柱状图
      countDays(day) {
        getAction(this.url.countDays, {
          time: day,
          isDay: 1
        }).then((res) => {
          if (res.code == 200) {
            this.warningSituationHistogram(res.result, res.result.line)
          }
        })
      },
      // 告警情况柱状图
      countDays1(day) {
        getAction(this.url.countDays, {
          time: day,
          isDay: 0
        }).then((res) => {
          if (res.code == 200) {
            this.warningSituationHistogram(res.result, res.result.line)
          }
        })
      },

      // 告警情况统计柱状图
      warningSituationHistogram(data, dataList) {
        let yarr1 = []
        let yarr2 = []
        let xarr = []
        dataList.forEach((e) => {
          yarr1.push(e.value1)
          yarr2.push(e.value2)
          xarr.push(e.name)
        })

        let myChart = this.$echarts.init(document.getElementById('warningSituationHistogram'))
        myChart.setOption({
          legend: {
            data: [data.value1, data.value2],
            left: 'right',
            textStyle: {
              color: '#edf1fc',
            },
          },
          tooltip: {
            show: true,
            trigger: 'axis',
            transitionDuration: 0, //echart防止tooltip的抖动
          },
          xAxis: [{
            type: 'category',
            data: xarr,
            boundaryGap: true,
            axisLine: {
              lineStyle: {
                //x轴字体颜色
                color: '#41759c',
              },
            },
            axisLabel: {
              textStyle: {
                color: '#41759c',
              },
            },
            axisTick: {
              show: true,
              alignWithLabel: true,
            },
          }, ],
          yAxis: [{
            type: 'value',
            axisLine: {
              show: false, //y轴线消失
              lineStyle: {
                //y轴字体颜色
                color: '#41759c',
              },
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: ['#1c2a37'],
                width: 2,
                type: 'solid',
              },
            },
          }, ],
          grid: {
            top: 34,
            right: 10,
            bottom: 30,
            left: 40,
          },
          series: [{
              name: data.value1,
              type: 'bar',
              data: yarr1,
              barWidth: 10, //柱图宽度
              itemStyle: {
                normal: {
                  color: '#259cfa',
                },
              },
            },
            {
              name: data.value2,
              type: 'bar',
              data: yarr2,
              barWidth: 10, //柱图宽度
              itemStyle: {
                normal: {
                  color: '#feb528',
                },
              },
            },
          ],
        })
        window.addEventListener("resize", () => {
          myChart.resize();
        });
      },

      // 挤压告警轮播
      alarmList() {
        getAction(this.url.alarmHistory).then((res) => {
          if (res.code == 200) {
            this.warningData = res.result
          }
        })
      },

      // 告警情况
      alarmOverview() {
        getAction(this.url.alarmOverview).then((res) => {
          if (res.code == 200) {
            this.todayAlarm = res.result.today_alarm
            this.todaySolve = res.result.today_solve
            this.totalAlarm = res.result.total_alarm
            this.totalSolve = res.result.total_solve
            this.totalPercent = res.result.total_solve_percentage || 0
            this.todayPercent = res.result.today_solve_percentage || 0

          }
        })
      },

      // 10日内告警资源
      alarmDeviceDays() {
        getAction(this.url.alarmDeviceDays).then((res) => {
          if (res.code == 200) {
            this.warningResourcesPieChart(res.result)
          }
        })
      },

      // 10日内告警资源饼状图
      warningResourcesPieChart(data) {
        let yarr = []
        data.forEach((e) => {
          // e.name = e.displayName;
        })
        let myChart = this.$echarts.init(document.getElementById('warningResourcesPieChart'))
        let option = {
          tooltip: {
            show: true,
            trigger: 'item',
            transitionDuration: 0, //echart防止tooltip的抖动
          },
          legend: {
            top: '20%',
            right: '40',
            orient: 'vertical',
            icon: 'circle',
            textStyle: {
              color: 'white',
            },
            selectedMode: false, // 取消标题点击后事件-这里显示和隐藏指定项
            formatter: function (name) {
              // 获取legend显示内容
              let data = option.series[0].data
              let total = 0
              let tarValue = 0
              for (let i = 0, l = data.length; i < l; i++) {
                total += data[i].value
                if (data[i].name == name) {
                  tarValue = data[i].value
                }
              }
              let p = total>0?((tarValue / total) * 100).toFixed(2) : 0;
              return name + ' ' + ' ' + p + '%'
            }
          },
          color: ['#4988e7', '#04c3f9', '#4ae599', '#fed500', '#c5dff6'],
          series: [{
            type: 'pie',
            radius: '70%',
            itemStyle: {
              normal: {
                label: {
                  show: false,
                  position: 'inner',
                },
                labelLine: {
                  show: false,
                },
              },
            },
            center: ['30%', '50%'],
            data: data,
            hoverAnimation: false, // 关闭鼠标悬停放大
          }, ],
        }

        myChart.setOption(option)
        window.addEventListener("resize", () => {
          myChart.resize();
        });
      },

      // 实时告警表格
      alarmRealtime() {
        getAction(this.url.alarmRealtime).then((res) => {
          if (res.code == 200) {
            this.realTimeAlarmData = res.result
          }
        })
      },

      // 本月告警趋势分析折线图数据
      alarmTrend(day) {
        getAction(this.url.alarmTrend, {
          isDay: 1,
          time: day
        }).then((res) => {
          if (res.code == 200) {
            this.warningTrendLineChart(res.result)
          }
        })
      },
      // 本月告警趋势分析折线图数据
      alarmTrend1(day) {
        getAction(this.url.alarmTrend, {
          isDay: 0,
          time: day
        }).then((res) => {
          if (res.code == 200) {
            this.warningTrendLineChart(res.result)
          }
        })
      },

      // 告警趋势分析折线图
      warningTrendLineChart(data) {
        let time = data.time
        let title = data.title
        let value = data.value
        let color = ['29,159,240', '255,255,0', '254,126,3', '219,18,18']
        let serveList = []
        value.forEach((ele, i) => {
          serveList.push({
            name: ele.name,
            type: 'line',
            symbol: 'circle', // 默认是空心圆（中间是白色的），改成实心圆
            showAllSymbol: true,

            lineStyle: {
              normal: {
                width: 1,
                color: `rgba(${ i<4 ? color[i] : ele.color})`, // 线条颜色
              },
              borderColor: `rgba(${ i<4 ? color[i] : ele.color})`,
            },
            itemStyle: {
              color: `rgba(${ i<4 ? color[i] : ele.color})`,
              borderColor: `rgba(${ i<4 ? color[i] : ele.color})`,
              borderWidth: 2,
            },
            tooltip: {
              show: true,
            },
            areaStyle: {
              //区域填充样式
              normal: {
                //线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是‘true’，则该四个值是绝对像素位置。
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [{
                      offset: 0,
                      color: `rgba(${ i<4 ? color[i] : ele.color},.2)`,
                    },
                    {
                      offset: 1,
                      color: `rgba(${ i<4 ? color[i] : ele.color},0)`,
                    },
                  ],
                  false
                ),
                shadowColor: `rgba(${ i<4 ? color[i] : ele.color},.2)`, //阴影颜色
                shadowBlur: 20, //shadowBlur设图形阴影的模糊大小。配合shadowColor,shadowOffsetX/Y, 设置图形的阴影效果。
              },
            },
            data: ele.data,
          })
        })
        let myChart = this.$echarts.init(document.getElementById('warningTrendLineChart'))
        myChart.setOption({
          tooltip: {
            show: true,
            trigger: 'axis',
            transitionDuration: 0, //echart防止tooltip的抖动
          },
          legend: {
            left: 'right',
            data: title,
            color: '#fff',
            textStyle: {
              color: '#fff',
            },
          },
          grid: {
            top: '10%',
            left: '9%',
            right: '6%',
            bottom: '15%',
            // containLabel: true
          },
          xAxis: [{
            type: 'category',
            boundaryGap: false,
            axisLine: {
              //坐标轴轴线相关设置。数学上的x轴
              show: true,
              lineStyle: {
                color: '#1f313d',
              },
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: '#41759c', //更改坐标轴文字颜色
              },
            },
            splitLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            data: time,
          }, ],
          yAxis: [{
            nameTextStyle: {
              color: '#7ec7ff',
              fontSize: 16,
              padding: 10,
            },
            min: 0,
            splitLine: {
              show: true,
              lineStyle: {
                color: ['#1c2a37'],
                width: 2,
                type: 'solid',
              },
            },
            axisLine: {
              show: false,
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: '#5189ba', //更改坐标轴文字颜色
              },
            },
            axisTick: {
              show: false,
            },
          }, ],
          series: serveList,
        })
        window.addEventListener("resize", () => {
          myChart.resize();
        });
      },
    },
  }
</script>
<style lang="less" scoped>
  .left-bottom-table::-webkit-scrollbar {
    display: none;
    /*隐藏滚动条*/
  }

  .right-top-body-tabel::-webkit-scrollbar {
    display: none;
    /*隐藏滚动条*/
  }

  .button_style {
    margin: 0 5px;
    user-select: none;
    background-color: #111217;
    color: rgb(209, 206, 206)
  }

  .button_style1 {
    margin: 0 5px;
    user-select: none;
    background-color: #5a5c63;
  }

  .topTitle {
    height: 16%;
    display: flex;
    align-items: center;
    font-size: 0.225rem
      /* 18/80 */
    ;
    color: #45c5e0;
    padding-left: 0.15rem
      /* 12/80 */
    ;
    letter-spacing: 0.025rem
      /* 2/80 */
    ;

    img {
      width: 0.125rem
        /* 10/80 */
      ;
      height: 0.1625rem
        /* 13/80 */
      ;
      margin-right: 0.0875rem
        /* 7/80 */
      ;
    }
  }

  .body {
    padding: 0.25rem
      /* 20/80 */
      0.2rem 0.1125rem 0.2rem;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: space-between;

    .left {
      width: 34%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .left-top {
        width: 100%;
        height: 50%;
        background: #111217;
        border-radius: 0.075rem
          /* 6/80 */
        ;

        .left-top-body {
          width: 98%;
          height: 84%;
          display: flex;
          align-items: center;
          justify-content: center;

          .left-top-body-histogram {
            width: 100%;
            height: 100%;
          }
        }
      }

      .left-bottom {
        width: 100%;
        height: 49%;
        background: #111217;
        border-radius: 0.075rem
          /* 6/80 */
        ;

        .left-bottom-top {
          height: 0.625rem
            /* 50/80 */
          ;
          display: flex;
          align-items: center;
          justify-content: space-between;

          .left-bottom-top-right {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            margin-right: 0.3rem
              /* 24/80 */
            ;
          }
        }

        .left-bottom-body {
          width: 100%;
          height: 84%;
          padding: 0 0.125rem
            /* 10/80 */
          ;

          .left-bottom-tableTitle {
            display: flex;
            height: 30px;
            align-items: center;
            padding-bottom: 13px;
            border-bottom: 1px solid #343f45;

            span {
              font-size: 16px;
              width: 100%;
              text-align: center;
              color: #00c4f6;
            }
          }

          .left-bottom-table {
            width: 100%;
            height: calc(100% - 30px);
            overflow-x: auto;

            .seamless-warp {
              width: 100%;
              height: 100%;

              div {
                align-items: center;
                width: 100%;
                line-height: 0.525rem;
                /* 42/80 */
                display: flex;
                justify-content: space-around;

                span {
                  width: 100%;
                  color: rgba(255, 255, 255, 0.75);
                  font-size: 0.175rem;
                  text-align: center;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                }

                div:nth-child(2n + 0) {
                  background: #1f2533;
                  text-align: center;
                }
              }
            }
          }
        }
      }
    }

    .core {
      // margin: 0 0.25rem /* 20/80 */;
      display: flex;
      flex-direction: column;
      width: 30%;
      height: 100%;
      justify-content: space-between;

      .core-top {
        width: 100%;
        height: 50%;
        background: #111217;
        border-radius: 0.075rem
          /* 6/80 */
        ;

        .core-top-body {
          width: 100%;
          height: 84%;
          display: flex;
          flex-wrap: wrap;
          padding: 0 0.2875rem
            /* 23/80 */
          ;
          justify-content: space-around;
          align-items: center;

          .core-top-body-item-one {
            width: 46%;
            height: 27%;
            display: flex;
            align-items: center;

            background: #222325;

            .core-top-body-item-left {
              width: 2rem
                /* 160/80 */
              ;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
            }

            .core-top-body-item-right {
              width: 100%;
              height: 100%;
              margin-left: -0.125rem
                /* 10/80 */
              ;
              color: #fff;
              display: flex;
              justify-content: center;
              flex-direction: column;

              span:nth-child(2) {
                font-size: 0.325rem
                  /* 26/80 */
                ;
                color: #47cfff;
              }
            }
          }

          .core-top-body-item-two {
            width: 46%;
            height: 27%;
            display: flex;
            align-items: center;
            background: #222325;

            .core-top-body-item-left {
              width: 2rem
                /* 160/80 */
              ;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
            }

            .core-top-body-item-right {
              width: 100%;
              height: 100%;
              margin-left: -0.05rem
                /* 4/80 */
              ;
              color: #fff;
              display: flex;
              justify-content: center;
              flex-direction: column;

              span:nth-child(2) {
                font-size: 0.325rem
                  /* 26/80 */
                ;
                color: #37fceb;
              }
            }
          }

          .core-top-body-item-three {
            width: 46%;
            height: 27%;
            display: flex;
            align-items: center;
            background: #222325;

            .core-top-body-item-left {
              width: 2rem
                /* 160/80 */
              ;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
            }

            .core-top-body-item-right {
              width: 100%;
              height: 100%;
              margin-left: -0.05rem
                /* 4/80 */
              ;
              color: #fff;
              display: flex;
              justify-content: center;
              flex-direction: column;

              span:nth-child(2) {
                font-size: 0.325rem
                  /* 26/80 */
                ;
                color: #2949fe;
              }
            }
          }

          .core-top-body-item-four {
            width: 46%;
            height: 27%;
            display: flex;
            align-items: center;
            background: #222325;

            .core-top-body-item-left {
              width: 2rem
                /* 160/80 */
              ;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
            }

            .core-top-body-item-right {
              width: 100%;
              height: 100%;
              margin-left: -0.05rem
                /* 4/80 */
              ;
              color: #fff;
              display: flex;
              justify-content: center;
              flex-direction: column;

              span:nth-child(2) {
                font-size: 0.325rem
                  /* 26/80 */
                ;
                color: #fec900;
              }
            }
          }

          .core-top-body-item-five {
            width: 46%;
            height: 27%;
            display: flex;
            align-items: center;
            background: #222325;
            margin-bottom: 0.125rem;

            .core-top-body-item-left {
              width: 2rem
                /* 160/80 */
              ;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
            }

            .core-top-body-item-right {
              width: 100%;
              height: 100%;
              margin-left: -0.05rem
                /* 4/80 */
              ;
              color: #fff;
              display: flex;
              justify-content: center;
              flex-direction: column;

              span:nth-child(2) {
                font-size: 0.325rem
                  /* 26/80 */
                ;
                color: #fec900;
              }
            }
          }

          .core-top-body-item-six {
            width: 46%;
            height: 27%;
            display: flex;
            align-items: center;
            background: #222325;
            margin-bottom: 0.125rem;

            .core-top-body-item-left {
              width: 2rem
                /* 160/80 */
              ;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
            }

            .core-top-body-item-right {
              width: 100%;
              height: 100%;
              margin-left: -0.05rem
                /* 4/80 */
              ;
              color: #fff;
              display: flex;
              justify-content: center;
              flex-direction: column;

              span:nth-child(2) {
                font-size: 0.325rem
                  /* 26/80 */
                ;
                color: #fec900;
              }
            }
          }
        }
      }

      .core-core {
        width: 100%;
        height: 49%;
        background: #111217;
        border-radius: 0.075rem
          /* 6/80 */
        ;

        // margin: 0.25rem /* 20/80 */ 0;
        .core-core-body {
          width: 100%;
          height: 84%;
        }
      }
    }

    .right {
      display: flex;
      flex-direction: column;
      width: 35%;
      height: 100%;
      justify-content: space-between;

      .right-top {
        width: 100%;
        height: 50%;
        background: #111217;
        border-radius: 0.075rem
          /* 6/80 */
        ;

        .right-top-body {
          width: 100%;
          height: 73%;
          // padding: 0 0.3625rem /* 29/80 */;
          color: #fff;
          font-size: 0.175rem
            /* 14/80 */
          ;
          overflow: hidden;

          .right-top-body-tabel {
            width: 100%;
            height: 100%;
            overflow-x: auto;

            .seamless-scroll {
              width: 100%;
              height: 100%;

              ul {
                margin: 0;
                padding: 0;

                .tabel-item {
                  width: 100%;
                  height: 0.625rem
                    /* 50/80 */
                  ;
                  display: flex;
                  align-items: center;

                  // justify-content: space-around;
                  .tabel-item-left {
                    width: 20%;
                    height: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: #d62629;
                  }

                  .tabel-item-right {
                    height: 100%;
                    width: 70%;
                    color: rgba(255, 255, 255, 0.85);
                    display: flex;
                    align-items: center;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    border-bottom: 0.0125rem
                      /* 1/80 */
                      dashed #43565d;
                  }
                }
              }
            }
          }
        }
      }

      .right-bottom {
        width: 100%;
        height: 49%;
        // margin-top: 0.25rem /* 20/80 */;
        background: #111217;
        border-radius: 0.075rem
          /* 6/80 */
        ;

        .right-bottom-body {
          height: 84%;
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: center;

          .right-bottom-body-lineChart {
            width: 98%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }
    }
  }
</style>