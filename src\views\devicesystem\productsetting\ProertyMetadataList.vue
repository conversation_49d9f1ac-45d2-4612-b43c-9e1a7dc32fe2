<template>
  <div>
    <!-- 查询区域 -->
    <a-row :gutter="24" ref="row" style='margin-bottom: 16px' type='flex'>
      <a-col :span="spanValue">
        <div style='display: flex;align-items: center'>
          <div style='width: 90px;text-align: right'>属性标识：</div>
          <div style='width: calc(100% - 90px)'>
            <a-input :maxLength='maxLength' style='width: 100%' placeholder="请输入属性标识" v-model="queryParam.code" :allowClear="true" autocomplete="off" />
          </div>
        </div>
      </a-col>
      <a-col :span="spanValue">
        <div style='display: flex;align-items: center'>
          <div style='width: 90px;text-align: right'>属性名称：</div>
          <div style='width: calc(100% - 90px)'>
            <a-input :maxLength='maxLength' style='width: 100%' placeholder="请输入属性名称" v-model="queryParam.name" :allowClear="true" autocomplete="off" />
          </div>
        </div>
      </a-col>
      <a-col :span="spanValue">
        <div style='display: flex;align-items: center'>
          <div style='width: 90px;text-align: right'>传输协议：</div>
          <div style='width: calc(100% - 90px)'>
            <a-select style='width: 100%' :getPopupContainer="(node) => node.parentNode"
                      v-model="queryParam.transferProtocolId"
                      allowClear
                      placeholder="请选择传输协议">
              <a-select-option v-for="item in productInfo.selectedProtocolList" :key="item.transferProtocolId">
                {{ item.transferProtocol }}
              </a-select-option>
            </a-select>
          </div>
        </div>
      </a-col>

      <a-col >
        <!--                <span class="table-page-search-submitButtons"-->
        <!--                      :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">-->
        <a-button type="primary" class="btn-search btn-search-style" @click="searchQuery">查询</a-button>
        <a-button class="btn-reset btn-reset-style" @click="searchReset">重置</a-button>
        <!--                  <a v-if="isVisible" class="btn-updown-style" @click="doToggleSearch">-->
        <!--                    {{ toggleSearchStatus ? '收起' : '展开' }}-->
        <!--                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />-->
        <!--                  </a>-->
        <!--                </span>-->
      </a-col>
    </a-row>
    <!-- 操作按钮区域 -->
    <div class="table-operator table-operator-style">
      <a-button @click="privateHandleAdd" type="primary" icon="plus">新增</a-button>
      <a-dropdown v-if='selectedRowKeys.length > 0'>
        <a-menu slot="overlay" style='text-align: center'>
          <a-menu-item key='1' @click='batchDel'>删除</a-menu-item>
        </a-menu>
        <a-button> 批量操作
          <a-icon type='down' />
        </a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <a-table ref="table" bordered rowKey="id" :columns="columns" :dataSource="dataSource"
        :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}" :pagination="ipagination" :loading="loading"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }" @change="handleTableChange">
        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 14px">无图片</span>
          <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width: 80px; font-size: 14px" />
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 14px">无文件</span>
          <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" class="caozuo" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>

          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多
              <a-icon type="down" />
            </a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleDetail(record)" style="color: #409eff">查看</a>
              </a-menu-item>
              <a-menu-item v-if="record.isFlowModel == true">
                <a style="color: #409eff" @click="process(record)">{{
                  record.chainId != '' && record.chainId != null ? '编辑流程' : '配置流程'
                }}</a>
              </a-menu-item>
              <a-menu-item v-if="record.chainId != '' && record.chainId != null && record.isFlowModel == true">
                <a-popconfirm title="确定删除流程配置吗?" @confirm="() => processDel(records)">
                  <a style="color: #409eff">删除流程</a>
                </a-popconfirm>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a style="color: #409eff">删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>
        <template slot="readWriteFlag" slot-scope="text">
          <span>
            {{ text == 'read' ? '只读' : '读写' }}
          </span>
        </template>
        <template slot="tooltip" slot-scope="text">
          <a-tooltip placement="topLeft" :title="text" trigger="hover">
            <span class="tooltip">
              {{ text }}
            </span>
          </a-tooltip>
        </template>
      </a-table>
    </div>
    <proerty-metadata-modal ref="modalForm" :productInfo="productInfo" @ok="modalFormOk"></proerty-metadata-modal>
    <process-manage ref="processManage" @close="close" @ok="modalFormOk2"></process-manage>
  </div>
</template>

<script>
  import '@/assets/less/TableExpand.less'
  import {
    mixinDevice
  } from '@/utils/mixin'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import {
    deleteAction,
    getAction,
    httpAction,
    postAction
  } from '@/api/manage'
  import ProertyMetadataModal from '../modules/ProertyMetadataModal'
  import processManage from '../modules/processManage.vue'
  import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'

  export default {
    name: 'ProertyMetadataList',
    mixins: [JeecgListMixin, mixinDevice,YqFormSearchLocation],
    components: {
      ProertyMetadataModal,
      processManage,
    },
    data() {
      return {
        maxLength:50,
        formItemLayout: {
          labelCol: {
            style: 'width:90px',
          },
          wrapperCol: {
            style: 'width:calc(100% - 90px)'
          }
        },
        CardColLayout: {
          xl: {
            span: 6,
          },
          lg: {
            span: 8,
          },
          md: {
            span: 12,
          },
          sm: {
            span: 24,
          },
          xs: {
            span: 24,
          },
        },
        records: {},
        description: '属性元数据管理页面',
        isorter: {
          column: 'serial',
          order: 'desc'
        },
        // 表头
        columns: [
          {
            title: '属性标识',
            dataIndex: 'code',
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 80px;max-width:300px'
              return {
                style: cellStyle
              }
            },
          },
          {
            title: '属性名称',
            dataIndex: 'name',
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 80px;max-width:300px'
              return {
                style: cellStyle
              }
            },
          },
          {
            title: '序号',
            dataIndex: 'serial',
            sorter: true,
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 50px;max-width:100px'
              return {
                style: cellStyle
              }
            },
          },
          {
            title: '传输协议',
            dataIndex: 'transferProtocol',
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 50px;max-width:300px'
              return {
                style: cellStyle
              }
            },
          },
          {
            title: '数据类型',
            dataIndex: 'dataType',
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 50px;max-width:200px'
              return {
                style: cellStyle
              }
            },
          },
          {
            title: '读写标识',
            dataIndex: 'readWriteFlag',
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 50px;max-width:300px'
              return {
                style: cellStyle
              }
            },
            scopedSlots: {
              customRender: 'readWriteFlag'
            },
          },
          {
            title: '描述',
            dataIndex: 'remark',
            scopedSlots: {
              customRender: 'tooltip'
            },
            customCell: () => {
              let cellStyle = 'text-align: left;min-width: 50px;max-width:400px'
              return {
                style: cellStyle
              }
            },
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            fixed: 'right',
            width: 150,
            scopedSlots: {
              customRender: 'action'
            },
          },
        ],
        url: {
          list: '/product/proertyMetadata/pageList',
          delete: '/product/proertyMetadata/delete',
          deleteChain: '/flow/chain/delete',
          queryChainList: '/flow/chain/queryChain',
          deleteBatch: '/product/proertyMetadata/deleteBatch',
          exportXlsUrl: '/product/proertyMetadata/exportXls',
          importExcelUrl: 'product/proertyMetadata/importExcel',
        },
        dictOptions: {},
        productInfo: {},
        disableMixinCreated:true
      }
    },
    created() {},
    computed: {
      importExcelUrl: function () {
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
      },
    },
    methods: {
      getChain(data) {
        if (data && data.chainId != '' && data.chainId != null) {
          getAction(this.url.queryChainList, {
            chainId: data.chainId
          }).then((res) => {
            this.records = res.result
            this.$refs.processManage.edit(Object.assign({}, this.records), data)
          })
        }
      },
      process(info) {
        if (info && info.chainId != null && info.chainId != '') {
          this.getChain(info)
        } else {
          this.records = {}
          this.$refs.processManage.edit(Object.assign({}, this.records), info)
        }
      },
      processDel(record) {
        if (record.id) {
          deleteAction(this.url.deleteChain, {
            chainId: record.chainId
          }).then((res) => {
            if (res.success) {
              record.topoDataJson = ''
              record.chainDesc = ''
              record.chainId = ''
              record.chainName = ''
              record.chain = ''
              record.params = ''
              this.$message.success(res.message)
              this.loadData()
            } else {
              this.$message.warning(res.message)
            }
            this.chainIdJudge = false
          })
        } else {
          this.$message.warning('未配置流程')
        }
      },
      show(record) {
        this.productInfo = record
        this.queryParam.productId = this.productInfo.id
        this.ipagination.pageSize = 10
        this.ipagination.current = 1
        this.loadData()
      },
      initDictConfig() {},
      privateHandleAdd() {
        this.$refs.modalForm.add()
        this.$refs.modalForm.title = '新增'
        this.$refs.modalForm.disableSubmit = false
      },
      close() {
        this.loadData()
      },
      modalFormOk2(data) {
        this.getChain(data.chainId)
        // 新增/修改 成功时，重载列表
        this.loadData()
      },
      searchReset() {
        this.queryParam = {}
        this.queryParam.productId = this.productInfo.id
        this.loadData(1)
      },
    },
  }
</script>
<style lang='less' scoped>
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';
</style>