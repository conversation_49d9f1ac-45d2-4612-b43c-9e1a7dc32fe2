<template>
  <div style="padding: 0 12px;height: calc(100vh - 80px);overflow-y: auto;overflow-x: hidden">
    <!-- <img :src="imgPath+'threeImg/3dapple.png'" alt=""> -->
    <div >
      <a-row :gutter="8">
        <a-col class="gutter-col" :span="12" v-for="(item, idx) in threeObjArr" :key="idx" >
          <div class="gutter-box" @click="addTest(item)" >
           <div>
            <img v-if="item.icon" style="height:80px" :src="imgPath+item.icon" alt=""/>
            <div v-else class="wall-show"></div>
           </div>
           {{ item.title }}
          </div>
        </a-col>
      </a-row>
    </div>
    <!-- <div v-for="(item, idx) in threeObjArr" :key="idx">
      <a-button v-if="item.using" block type="primary" @click="addTest(item)">
        {{ item.title }}
      </a-button>
    </div> -->
  </div>
</template>
<script>
import modelJson from '../threeUtils/model.json'
export default {
  data() {
    return {
      threeObjArr: modelJson.filter(el=>el.using),
      imgPath: process.env.BASE_URL,
    }
  },
  created() {
    // this.getModle()
  },
  mounted() {},
  methods: {
    // getModle(){
    //   fetch("model.json")
    //   .then((res)=>res.json())
    //   .then((result)=>{this.threeObjArr=result})
    // },
    addTest(model) {
      this.$root.$emit('addTest', model)
    },
  },
}
</script>
<style scoped lang="less">
  .gutter-col{
    margin-bottom: 8px;
  }
.gutter-box {
  background: transparent;
  width: 100%;
  height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: center;
  text-align: center;
  border:1px solid #8c8c8c;
  border-radius: 8px;
}
.gutter-box:hover{
  border: 1px solid #4682B4;
  background: #4682B4;
  box-shadow: 0px 0px 5px#4682B4;
  cursor: pointer;
}
.wall-show{
  height: 80px;
  width: 80px;
  background: #AFC0CA;
}
.ant-btn {
  margin-bottom: 12px;
}
</style>