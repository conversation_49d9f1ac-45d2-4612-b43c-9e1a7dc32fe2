<template>
  <div :id="chartId" ref="chartDom" style="width: 100%; height: 100%"></div>
</template>

<script>
import { round } from '@antv/x6/lib/geometry/util'
export default {
  props: ['chartId'],
  data() {
    return {
      barWidth: 0,
    }
  },
  created() {},
  mounted() {
    if (this.chartId) {
      this.drawChart()
    }
  },
  methods: {
    drawChart() {
      var chartDom = document.getElementById(this.chartId)
      var myChart = this.$echarts.init(chartDom)
    
           const chartData = [
        {
            value: 520,
            name: '温度传感器'
        },
        {
            value: 280,
            name: '湿度传感器'
        },
        {
            value: 100,
            name: 'UPS'
        },
        {
            value: 100,
            name: '门禁'
        },
        {
            value: 100,
            name: '消防'
        },
       
    ]
    const colorList = ['#115FEA', '#10EBE3', '#10A9EB', '#EB9C10', '#2E10EB', '#9B10EB']
    const sum = chartData.reduce((per, cur) => per + cur.value, 0)
    const gap = (1 * sum) / 100
    const pieData1 = []
    const pieData2 = []
    const gapData = {
        name: '',
        value: gap,
        itemStyle: {
            color: 'transparent'
        }
    }
    for (let i = 0; i < chartData.length; i++) {
        // 第一圈数据
        pieData1.push({
            ...chartData[i],
            itemStyle: {
                 borderCap:'round',
                    borderWidth:0,
                    borderColor:"#fff",
            }
        })
        pieData1.push(gapData)

        // 第二圈数据
        pieData2.push({
            ...chartData[i],
            itemStyle: {
                color: colorList[i],
                opacity: 1
            }
        })
        pieData2.push(gapData)
    }

   var option = {
        // backgroundColor: '#FC7D3F',
        tooltip: {
            show: false
            // backgroundColor: 'rgba(255, 255, 255)',
            // textStyle: {
            //     color: '#FC7D3F'#FC7D3F
            // }
        },
       
        graphic: {
            elements: [
                // {
                //     type: 'image',
                //     z: 3,
                //     style: {
                //         image: imgSrc,
                //         width: 70,
                //         height: 70
                //     },
                //     top: 'middle',
                //     left: 'center',
                //     scale: [1.5, 1.5]
                // }
            ]
        },
        grid: {
            top: 0,
            right: 0,
            bottom: 20,
            left: 0
        },
        color: colorList,
        series: [
            {
                name: '消息来源',
                type: 'pie',
                // roundCap: true,
                radius: ['70%', '80%'],
                center: ['50%', '50%'],
                 coordinateSystem: 'polar',
                label: {
                    show: false
                },
                labelLine: {
                    show: false
                },
                
                 silent: true,
                data: pieData1
            },
            {
                type: 'pie',
                radius: ['55%', '35%'],
                center: ['50%', '50%'],
                gap: 1.71,
                roundCap: true,
                label: {
                    show: false
                },
                labelLine: {
                    show: false
                },
                silent: true,
                data: pieData2
            },
            
        ]
    }




      option && myChart.setOption(option)
      myChart.resize()
    },
  },
}
</script>

<style>
</style>