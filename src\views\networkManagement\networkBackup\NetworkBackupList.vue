<template>
  <a-row :gutter='10' style='height: 100%;' class='vScroll'>
    <a-col style='width:100%;height: 100%;display: flex;flex-direction: column'>
      <!-- 查询区域 -->
      <a-card :bordered='false'
              :bodyStyle="{ paddingBottom: '0', marginRight: '12px' }"
              class='card-style'
              style='width: 100%'>
        <div class='table-page-search-wrapper'>
          <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
            <a-row :gutter='24' ref='row'>
              <a-col :span='spanValue'>
                <a-form-item label='设备名称'>
                  <a-input
                    :maxLength='maxLength'
                    placeholder='请输入设备名称'
                    v-model='queryParam.deviceName'
                    :allowClear='true'
                    autocomplete='off' />
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label='IP地址'>
                  <a-input
                    :maxLength='maxLength'
                    placeholder='请输入IP地址'
                    v-model='queryParam.deviceIp'
                    :allowClear='true'
                    autocomplete='off' />
                </a-form-item>
              </a-col>

              <a-col :span='spanValue' v-show='toggleSearchStatus'>
                <a-form-item label="备份创建人">
                  <a-select :getPopupContainer='node => node.parentNode'
                            v-model="queryParam.createBy1"
                            show-search
                            allow-clear
                            placeholder="请选择备份创建人"
                            option-filter-prop="children"
                            :filter-option="filterOption"
                            @change="changeCreateBy">
                    <a-select-option v-for="(item, index) in userList"
                                     :key="'createBy_'+item.username+index"
                                     :value="item.username" :label="item.realname">
                      <div style="display: inline-block; width: 100%" :title="item.realname">
                        {{ item.realname }}
                        <span style="font-size: 12px; color: rgba(0, 0, 0, 0.45);">{{'(' + item.username + ')'}}</span>
                      </div>
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue" v-show='toggleSearchStatus'>
                <a-form-item label="备份创建时间">
                  <a-range-picker class="a-range-picker-choice-date"
                                  v-model="queryParam.createTimeRange"
                                  format="YYYY-MM-DD"
                                  :placeholder="['开始时间', '结束时间']"
                                  @change="onDateChange"
                                  @ok="onDateOk" />
                </a-form-item>
              </a-col>
              <a-col :span="spanValue"  v-show='toggleSearchStatus'>
                <a-form-item label="备份状态">
                  <a-select :getPopupContainer="(node) => node.parentNode"
                            v-model="queryParam.executeStatus"
                            placeholder="请选择备份状态"
                            :allowClear="true">
                    <a-select-option value="执行中">执行中</a-select-option>
                    <a-select-option value="备份成功">备份成功</a-select-option>
                    <a-select-option value="备份失败">备份失败</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue" v-show='toggleSearchStatus'>
                <a-form-item label="任务名称">
                  <a-select v-model="queryParam.backTaskId" placeholder="请选择任务名称" :show-search='true'
                            :getPopupContainer='(node) => node.parentNode' option-filter-prop='label'
                            :allow-clear='true'>
                    <a-select-option v-for="item in taskList" :label='item.taskName' :value='item.id'
                                     :key="item.id">
                      {{ item.taskName }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span='colBtnsSpan()'>
                <span class='table-page-search-submitButtons'
                      :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button type='primary' class='btn-search btn-search-style'  @click='searchQuery'>查询</a-button>
                  <a-button class='btn-reset btn-reset-style' @click='searchReset'>重置</a-button>
                  <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <div class='table-operator table-operator-style'>
          <!-- 操作按钮区域 -->
          <a-dropdown v-if='selectedRowKeys.length > 0'>
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item key='1' @click='batchDel'>删除</a-menu-item>
            </a-menu>
            <a-button> 批量操作
              <a-icon type='down' />
            </a-button>
          </a-dropdown>
        </div>
        <a-table ref='table'
                 bordered
                 rowKey='id'
                 :columns='columns'
                 :dataSource='dataSource'
                 :scroll='dataSource.length > 0 ? { x: "max-content" } : {}'
                 :pagination='ipagination'
                 :loading='loading'
                 :rowSelection='{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }'
                 @change='handleTableChange'>

          <!-- <template slot='status'
              slot-scope='text, record'>
              <span v-if='record.enable == 1'>
                  <img v-if='record.status == 1'
                      src='../../../assets/bigScreen/28.png'
                      alt=''
                      class='stateImg' />
                  <img v-else
                      src='../../../assets/bigScreen/57.png'
                      alt=''
                      class='stateImg' />
                  <img v-if='record.alarmStatus == 1'
                      src='../../../assets/bigScreen/56.png'
                      alt=''
                      class='stateImg alarmStatus' />
              </span>
              <span v-else>
                  <a-icon type='stop'
                      theme='twoTone'
                      two-tone-color='#eb2f96'
                      style='font-size: 16px'
                      class='stateImg ' />
              </span>
              <span style='margin-left: 10px'>{{ text }}</span>
          </template> -->
          <template slot="backTaskName" slot-scope='text'>
            {{ text ? text : '手动执行' }}
          </template>

          <template slot='tooltip'
                    slot-scope='text'>
            <a-tooltip placement='topLeft'
                       :title='text'
                       trigger='hover'>
              <div class='tooltip'>
                {{ text }}
              </div>
            </a-tooltip>
          </template>
          <span slot='action'
                class='caozuo'
                slot-scope='text, record'>
                        <a @click='viewConfig(record, "detail")'>查看</a>
                        <a-divider type='vertical' />
                        <a-dropdown>
                            <a class='ant-dropdown-link'>更多
                                <a-icon type='down' /></a>
                            <a-menu slot='overlay'>
                                <a-menu-item>
                                    <a @click='viewConfig(record, "log")'
                                       class='overlay'>日志</a>
                                </a-menu-item>
                                <a-menu-item>
                                    <a @click='downLoadConfig(record)'
                                       class='overlay'>下载</a>
                                </a-menu-item>
                                <a-menu-item>
                                    <a-popconfirm title="确定删除吗?"
                                                  @confirm="() => handleDelete(record.id)"
                                                  placement="topLeft">
                                        <a class='overlay'>删除</a>
                                    </a-popconfirm>
                                </a-menu-item>
                            </a-menu>
                        </a-dropdown>
                    </span>
        </a-table>
      </a-card>
      <view-config-modal ref='viewConfigModal'></view-config-modal>
    </a-col>
  </a-row>
</template>

<script>
import { getUserList } from '@/api/api'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
import { filterObj } from '@/utils/util'
import { deleteAction, getAction } from '@/api/manage'
import ViewConfigModal from '@views/networkManagement/networkDevice/modules/ViewConfigModal.vue'

export default {
  name: 'NetworkBackupList',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components: {
    ViewConfigModal
  },
  data() {
    return {
      maxLength: 50,
      // 查询条件
      queryParam: {
        deviceName: '',
        deviceIp: '',
        createBy: undefined,
        beginTime: '',
        endTime: '',
        executeStatus: undefined,
        backTaskId: undefined
      },
      userList: [],
      // 表头
      columns: [
        {
          title: '设备名称',
          dataIndex: 'deviceName',
          scopedSlots: { customRender: 'status' },
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
            return { style: cellStyle }
          }
        },
        {
          title: 'IP地址',
          dataIndex: 'deviceIp',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 150px;max-width:300px'
            return { style: cellStyle }
          }
        },
        {
          title: '产品名称',
          dataIndex: 'productName',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 150px;max-width:300px'
            return { style: cellStyle }
          }
        },
        {
          title: '版本',
          dataIndex: 'version',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 100px'
            return { style: cellStyle }
          }
        },
        {
          title: '备份创建人',
          dataIndex: 'createBy',
          customCell: () => {
            let cellStyle = 'text-align: center;width: 180px'
            return { style: cellStyle }
          }
        },
        {
          title: '备份创建时间',
          dataIndex: 'createTime',
          customCell: () => {
            let cellStyle = 'text-align: center;width:200px'
            return { style: cellStyle }
          }
        },
        {
          title: '备份状态',
          dataIndex: 'executeStatus',
          scopedSlots: { customRender: 'executeStatus' },
          customCell: () => {
            let cellStyle = 'text-align: center;width: 150px'
            return { style: cellStyle }
          }
        },
        {
          title: '任务名称',
          dataIndex: 'backTaskName',
          scopedSlots: { customRender: 'backTaskName' },
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
            return { style: cellStyle }
          }
        },
        {
          title: '备份说明',
          dataIndex: 'description',
          scopedSlots: { customRender: 'tooltip' },
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 100px;max-width:300px'
            return { style: cellStyle }
          }
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 140,
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: '/net/device/configureBckList', //分页展示接口
        delete: '/net/device/configureBackBathDelete', //删除接口
        deleteBatch: '/net/device/configureBackBathDelete', //批量删除和删除接口一样
        taskList: '/configureBack/task/list' // 获取任务列表
      },
      disableMixinCreated: true,
      taskList: [] // 所有的任务
    }
  },
  created() {
    this.getuserList()
    this.loadData()
    this.getTaskData()
  },
  methods: {
    changeCreateBy(value,option){
      if (value){
        this.queryParam.createBy=option.componentOptions.propsData.label
      }else {
       delete this.queryParam['createBy']
      }
    },
    loadData(arg) {
      if (arg === 1) {
        this.ipagination.current = 1
      }
      var params = this.getQueryParams() //查询条件
      this.loading = true
      getAction(this.url.list, params).then((res) => {
        if (res.success) {
          this.dataSource = res.result.records || res.result
          if (this.dataSource.length < 9) {
            this.clientHeight = false
          }
          this.ipagination.total = res.result.total
        }
        if (res.code === 510) {
          this.$message.warning(res.message)
        }
        this.loading = false
      })
    },
    //查看
    viewConfig(record, sign) {
      let config = sign == 'log' ? record.executeLog : record.configureText
      // console.log('config==', config)
      this.$refs.viewConfigModal.loadConfig(config)
      this.$refs.viewConfigModal.title = sign == 'log' ? '日志详情' : '配置详情'
      this.$refs.viewConfigModal.disableSubmit = true
    },
    //下载
    downLoadConfig(record) {
      this.downloadFile(record.configureFile)
    },
    //删除
    handleDelete: function(id) {
      if (!this.url.delete) {
        this.$message.error('请设置url.delete属性!')
        return
      }
      var that = this
      deleteAction(that.url.delete, { ids: id }).then((res) => {
        if (res.success) {
          that.$message.success(res.message)
          that.loadData()
        } else {
          that.$message.warning(res.message)
        }
      })
    },
    getQueryParams() {
      let param = Object.assign({}, this.queryParam, this.isorter)
      param.field = this.getQueryField()
      param.pageNo = this.ipagination.current
      param.pageSize = this.ipagination.pageSize
      delete param.createTimeRange // 时间参数不传递后台
      return filterObj(param)
    },
    // 重置
    searchReset() {
      var that = this
      that.queryParam = {} //清空查询区域参数
      that.loadData(this.ipagination.current)
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      )
    },
    getuserList() {
      let param = { pageSize: 10000 }
      getUserList(param).then((res) => {
        if (res.success) {
          this.userList = res.result.records
        }
      })
    },
    onDateChange: function(value, dateString) {
      this.queryParam.beginTime = dateString[0]
      this.queryParam.endTime = dateString[1]
    },
    onDateOk(value) {
    },
    // 获取所有的任务
    getTaskData() {
      let params = {
        pageNo: 1,
        pageSize: -1
      }
      getAction(this.url.taskList, params).then((res) => {
        if (res.success) {
          this.taskList = res.result.records || res.result
        }
      })
    }
  }
}

</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
</style>
