<template>
  <a-form-item label="精度" :labelCol="labelCol" :wrapperCol="wrapperCol">
    <a-input-number id="inputNumber" v-model="formatValue" :min="1" style="width:100%" @change="onChange"
      placeholder="请输入小数点位数" />
  </a-form-item>
</template>

<script>
  export default {
    name: 'floatMetadata',
    props: ['typeFormatValue'],
    data() {
      return {
        formatValue: '',
        labelCol: {
          xs: {
            span: 5
          },
          sm: {
            span: 5
          }
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          }
        },
        value: 3,
      }
    },
    mounted() {
      if (undefined != this.typeFormatValue && '' != this.typeFormatValue) {
        var obj = JSON.parse(this.typeFormatValue);
        this.formatValue = obj;
      }
    },
    methods: {
      onChange(value) {
        this.value = value;
        this.$emit('changElemType', JSON.stringify(this.value));
      },
    },
  }
</script>

<style>
</style>