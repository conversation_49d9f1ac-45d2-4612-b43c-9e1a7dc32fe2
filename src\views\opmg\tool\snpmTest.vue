<template>
  <a-row style="height: 100%; display: flex; overflow-x: auto">
    <a-col ref="print" id="printContent" class="abcdefg" :xl="12" :lg="12" :md="10" :sm="10" :xs="10"
      style="overflow-x: auto; min-width: 350px">
      <div>
        <p style="
            padding: 16px 24px;
            background-color: #fafafa;
            border-bottom: 1px solid #e8e8e8;
            font-size: 16px;
            white-space: nowrap;
          ">
          SNMP连接测试
        </p>
      </div>
      <a-col style="padding: 0 24px 24px">
        <a-form :form="form">
          <a-row>
            <a-col :span="24" class="aCol">
              <a-form-item label="IP地址" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input placeholder="请输入IP地址" v-decorator="['ip', validatorRules.ip]" class="aInp"></a-input>
              </a-form-item>
            </a-col>
            <a-col :span="24" class="aCol">
              <a-form-item label="SNMP版本" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-checkbox-group @change="onChangeStype" ref="InpCheck" v-decorator="['stype', validatorRules.stype]">
                  <a-checkbox value="SNMPV1"> V1 </a-checkbox>
                  <a-checkbox value="SNMPV2"> V2 </a-checkbox>
                  <a-checkbox value="SNMPV3"> V3 </a-checkbox>
                </a-checkbox-group>
              </a-form-item>
            </a-col>

            <a-col :span="24" class="aCol">
              <a-form-item label="共同体名" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input v-decorator="['snmpRName', { initialValue: 'public' }]" class="aInp"
                  :disabled="snmpRNameDisabled">
                </a-input>
              </a-form-item>
            </a-col>
            <a-col :span="24" class="aCol">
              <a-form-item label="端  口" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input placeholder="161" class="aInp" v-decorator="['snmpPort', { initialValue: '161' }]"></a-input>
              </a-form-item>
            </a-col>

            <a-col :span="24" class="aCol">
              <a-form-item label="超时值" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input placeholder="2000" class="aInp" v-decorator="['snmpTimeout', { initialValue: '2000' }]">
                </a-input>
              </a-form-item>
            </a-col>
            <a-col :span="24" class="aCol">
              <a-form-item label="重复次数" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input placeholder="0" class="aInp" v-decorator="['snmpRetry', { initialValue: '0' }]"></a-input>
              </a-form-item>
            </a-col>

            <a-col :span="24" class="aCol">
              <a-form-item label="用户名" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input placeholder="" class="aInp" v-decorator="['snmpUName']" :disabled="snmpUNameDisabled">
                </a-input>
              </a-form-item>
            </a-col>
            <a-col :span="24" class="aCol">
              <a-form-item label="安全级别" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-select class="aInp" v-decorator="['snmpAuthLevel', { initialValue: { key: 'noAuthNoPriv' } }]"
                  label-in-value @change="snmpAuthPriv" :disabled="snmpAuthPrivDisabled">
                  <a-select-option value="noAuthNoPriv"> noAuthNoPriv </a-select-option>
                  <a-select-option value="AuthNoPriv"> AuthNoPriv </a-select-option>
                  <a-select-option value="AuthPriv"> AuthPriv </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="24" class="aCol">
              <a-form-item label=" 认证协议" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-select v-decorator="['sAuth']" label-in-value :disabled="sAuthDisabled">
                  <a-select-option value="MD5"> MD5 </a-select-option>
                  <a-select-option value="SHA"> SHA </a-select-option>
                  <a-select-option value="SHA-256"> SHA-256 </a-select-option>
                  <a-select-option value="SHA-512"> SHA-512 </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="24" class="aCol">
              <a-form-item label="认证密码" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input v-decorator="['sAuthPasswd']" class="aInp" :disabled="sAuthPasswdDisabled"></a-input>
              </a-form-item>
            </a-col>
            <a-col :span="24" class="aCol">
              <a-form-item label=" 加密协议" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-select v-decorator="['spriv']" label-in-value :disabled="sprivDisabled">
                  <a-select-option value="DES"> DES </a-select-option>
                  <a-select-option value="3DES"> 3DES </a-select-option>
                  <a-select-option value="AES128"> AES128 </a-select-option>
                  <a-select-option value="AES192"> AES192 </a-select-option>
                  <a-select-option value="AES256"> AES256 </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="24" class="aCol">
              <a-form-item label="加密密码" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <!-- <a-input class="aInp" v-model="sprivPasswd" :disabled="sprivPasswdDisabled"></a-input> -->
                <a-input-password v-decorator="['sprivPasswd']" class="aInp" :disabled="sprivPasswdDisabled" />
              </a-form-item>
            </a-col>
            <a-col :span="24" class="aCol">
              <a-form-item label="取值方式" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-radio-group v-decorator="['method', { initialValue: 'getPdu' }]" @change="methodsChange">
                  <a-radio value="getPdu"> getPdu </a-radio>
                  <a-radio value="getPduWalk"> getPduWalk </a-radio>
                  <a-radio value="getPduTable"> getPduTable </a-radio>
                </a-radio-group>
              </a-form-item>
            </a-col>
            <a-col :span="24" class="aCol">
              <a-form-item label="OID示例" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input v-decorator="['oid', { initialValue: '*******.*******' }]" placeholder="*******.*******"
                  class="aInp"></a-input>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row class="btnStyle">
            <a-col :span="24" :style="{ textAlign: 'center' }">
              <a-button type="shallow" @click="handelSubmit" :disabled="buttonDisadled"> 提交 </a-button>
              <a-button :style="{ marginLeft: '8px' }" @click="handleReset"> 重置 </a-button>
            </a-col>
          </a-row>
        </a-form>
      </a-col>
    </a-col>
    <a-col :span="12" class="contTwo" style="overflow-x: auto; min-width: 350px">
      <!--        <a-descriptions layout="vertical" bordered  >-->
      <!--          <a-descriptions-item label="SNMP连接测试"  style="" class="returnDiv">-->
      <div class="returnDiv" v-if="this.methodValue != 'getPduTable'">
        <p class="returnTitle">SNMP连接测试</p>
        <p v-html="result" style="padding: 5px 16px 0 24px">{{ result }}</p>
      </div>
      <div v-else>
        <p class="returnTitle">SNMP连接测试</p>
        <a-table :data-source="snmpData" :columns="columnList" bordered
          :scroll="snmpData.length > 0 ? { x: 'max-content' } : {}">
          <template slot="name" slot-scope="text">
            <span> {{text }} </span>
          </template>
        </a-table>
      </div>
      <!--          </a-descriptions-item>-->
      <!--        </a-descriptions>-->
    </a-col>
  </a-row>
</template>
<script>
  import ACol from 'ant-design-vue/es/grid/Col'
  import ARow from 'ant-design-vue/es/grid/Row'
  import ATextarea from 'ant-design-vue/es/input/TextArea'
  import {
    getAction
  } from '@/api/manage'
  import pick from 'lodash.pick'
  import {
    validateDuplicateValue
  } from '@/utils/util'
  import JFormContainer from '@/components/jeecg/JFormContainer'
  import {
    notification
  } from 'ant-design-vue'

  export default {
    components: {
      pick,
      JFormContainer,
      ATextarea,
      ARow,
      ACol,
    },
    name: 'Printgzsld',
    props: {
      reBizCode: {
        type: String,
        default: '',
      },
      paramIp: {
        type: String,
        default: '',
      },
    },
    watch: {
      paramIp: {
        handler(nv) {
          this.$nextTick(() => {
            this.form.setFieldsValue({
              ip: nv,
            })
          })
        },
        immediate: true,
      },
    },
    data() {
      return {
        columnList: [],
        snmpData: [],
        model: {},
        labelCol: {
          xs: {
            span: 24,
          },
          sm: {
            span: 8,
          },
          md: {
            span: 7,
          },
          lg: {
            span: 6,
          },
          xl: {
            span: 5,
          },
        },
        wrapperCol: {
          xs: {
            span: 23,
          },
          sm: {
            span: 16,
          },
          md: {
            span: 17,
          },
          lg: {
            span: 18,
          },
          xl: {
            span: 18,
          },
        },
        confirmLoading: false,
        validatorRules: {
          ip: {
            rules: [{
                required: true,
                message: '请输入IP地址!',
              },
              {
                pattern: /^((25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))\.){3}(25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))$/,
                message: '请输入正确的IP地址!',
              },
            ],
          },
          stype: {
            rules: [{
              required: true,
              message: '请勾选SNMP版本!',
            }, ],
          },
        },
        form: this.$form.createForm(this),
        url: '/connect/testSnmp',
        result: '',
        snmpRNameDisabled: true, // 共同体
        snmpUNameDisabled: true, //用户名
        snmpAuthPrivDisabled: true, //安全级别
        sAuthDisabled: true, //认证协议
        sAuthPasswdDisabled: true, //认证密码
        sprivDisabled: true, //加密协议
        sprivPasswdDisabled: true, //加密密码
        buttonDisadled: false,
        methodValue: '',
      }
    },
    created() {},
    methods: {
      methodsChange(e) {
        this.result = ''
        //getPduTable
        this.methodValue = e.target.value
      },
      //提交方法
      handelSubmit() {
        this.form.validateFields((err, values) => {
          if (!err) {
            this.result = ''
            this.buttonDisadled = true
            let param = {
              ip: values.ip, // ip
              snmpRName: values.snmpRName, // 共同体
              snmpPort: values.snmpPort, //端口
              snmpTimeout: values.snmpTimeout, //超时值
              snmpRetry: values.snmpRetry, //重复次数
              snmpUName: values.snmpUName, //用户名
              snmpAuthLevel: values.snmpAuthLevel.key, //安全级别
              sAuth: values.sAuth != null ? values.sAuth.key : '', //认证协议
              sAuthPasswd: values.sAuthPasswd, //认证密码
              spriv: values.spriv != null ? values.spriv.key : '', //加密协议
              sprivPasswd: values.sprivPasswd, //加密密码
              stype: values.stype.join(','), // snmp 版本
              method: values.method,
              oid: values.oid,
            }
            getAction(this.url, param).then(
              (res) => {
                if (res.success) {
                  if (this.methodValue != 'getPduTable') {
                    this.result = res.result
                  } else {
                    this.columnList = res.result.columnList
                    this.snmpData = res.result.dataList
                  }
                  this.buttonDisadled = false
                }
              },
              (error) => {
                notification.error({
                  message: '系统提示：',
                  description: error.message
                })
                this.buttonDisadled = false
              }
            )
          }
        })
      },
      handleReset() {
        this.form.resetFields()
        this.form.setFieldsValue({
          ip: '', // ip
          stype: [], // snmp 版本
          snmpRName: 'public', // 共同体
          snmpPort: 161, //端口
          snmpTimeout: 2000, //超时值
          snmpRetry: 0, //重复次数
          snmpUName: '', //用户名
          snmpAuthLevel: {
            key: 'noAuthNoPriv',
          }, //安全级别
          sAuth: undefined, //认证协议
          sAuthPasswd: '', //认证密码
          spriv: undefined, //加密协议
          sprivPasswd: '', //加密密码
          method: 'getPdu', //取值方式
          oid: '*******.*******',
        })

        this.$refs.InpCheck.sValue = []
        this.result = ''
        this.buttonDisadled = false
        this.snmpRNameDisabled = true // 共同体
        this.snmpUNameDisabled = true //用户名
        this.snmpAuthPrivDisabled = true //安全级别
        this.sAuthDisabled = true //认证协议
        this.sAuthPasswdDisabled = true //认证密码
        this.sprivDisabled = true //加密协议
        this.sprivPasswdDisabled = true //加密密码
      },
      //安全级别
      snmpAuthPriv(value) {
        this.sAuthDisabled = value.key == 'noAuthNoPriv' //认证协议
        this.sAuthPasswdDisabled = value.key == 'noAuthNoPriv' //认证密码
        this.sprivDisabled = value.key == 'noAuthNoPriv' || value.key == 'AuthNoPriv' //加密协议
        this.sprivPasswdDisabled = value.key == 'noAuthNoPriv' || value.key == 'AuthNoPriv' //加密密码
        if (value.key == 'noAuthNoPriv') {
          this.form.setFieldsValue({
            sAuth: undefined, //认证协议
            sAuthPasswd: '', //认证密码
            spriv: undefined, //加密协议
            sprivPasswd: '', //加密密码
          })
        } else if (value.key == 'AuthNoPriv') {
          this.form.setFieldsValue({
            spriv: undefined, //加密协议
            sprivPasswd: '', //加密密码
          })
        }
      },
      //snmp 类型
      onChangeStype(checkedValues) {
        var f = true
        var n3 = true
        for (var i = 0; i < checkedValues.length; i++) {
          if (checkedValues[i] == 'SNMPV3') {
            f = false
          } else {
            n3 = false
          }
        }
        if (!n3) {
          this.snmpRNameDisabled = true
        }
        if (!f) {
          this.snmpUNameDisabled = false //用户名
          this.snmpAuthPrivDisabled = false //安全级别
        } else {
          this.sAuthDisabled = true //认证协议
          this.sAuthPasswdDisabled = true //认证密码
          this.sprivDisabled = true //加密协议
          this.sprivPasswdDisabled = true //加密密码
          this.snmpUNameDisabled = true //用户名
          this.snmpAuthPrivDisabled = true //安全级别
          this.snmpRNameDisabled = false

          this.form.setFieldsValue({
            snmpUName: '', //用户名
            snmpAuthLevel: {
              key: 'noAuthNoPriv',
            }, //安全级别
            sAuth: undefined, //认证协议
            sAuthPasswd: '', //认证密码
            spriv: undefined, //加密协议
            sprivPasswd: '', //加密密码
          })
        }
        if (!n3) {
          this.snmpRNameDisabled = false
        }
        if (0 == checkedValues.length) {
          this.snmpRNameDisabled = true
          this.form.setFieldsValue({
            snmpRName: '', //共同体
          })
        }
        this.stype = checkedValues.toString()
      },
    },
  }
</script>
<style lang="scss" scoped>
  /*update_begin author:scott date:20191203 for:打印机打印的字体模糊问题 */
  * {
    color: #000000;
    -webkit-tap-highlight-color: #000000;
  }

  /*update_end author:scott date:20191203 for:打印机打印的字体模糊问题 */
  .sn-box {
    border: 1px solid #e8e8e8;
    border-radius: 3px;
    height: 1200px;
  }

  .sn-title {
    background-color: #f5f5f5;
    padding: 16px;
    border-bottom: 1px solid #e8e8e8;
  }

  .importDiv {
    width: 60%;
    height: 24em;
    margin: 0 auto;
    border: 1px solid #d9d9d9;
    padding: 18px;
  }

  .returnDiv {
    height: 100%;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
  }

  .returnTitle {
    padding: 16px 24px;
    background-color: rgb(250, 250, 250);
    border-bottom: 1px solid rgb(232, 232, 232);
    font-size: 16px;
  }

  .leftSpan {
    width: 14%;
  }

  .abcdefg .ant-card-body {
    margin-left: 0%;
    margin-right: 0%;
    margin-bottom: 1%;
    border: 0px solid black;
    min-width: 800px;
    color: #000000 !important;
  }

  .explain {
    text-align: left;
    margin-left: 50px;
    color: #000000 !important;
  }

  .explain .ant-input,
  .sign .ant-input {
    font-weight: bolder;
  }

  .btnStyle {
    //margin-bottom: 24px;
  }

  .explain div {
    margin-bottom: 10px;
  }

  .ant-upload-select-picture-card i {
    font-size: 32px;
    color: #999;
  }

  .ant-upload-select-picture-card .ant-upload-text {
    margin-top: 8px;
    color: #666;
  }

  .aInp {
    //width: 91%;
  }

  #printContent {
    height: 100%;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
  }

  .contTwo {
    margin-left: 12px;
    width: calc(100% - 50% - 12px);
    height: 100%;
  }
</style>
<style scoped>
  .aCol {
    /* height: 45px; */
    margin-bottom: 5px;
  }

  .aCol:first-child {
    margin-top: 10px;
  }

  @media (min-width: 1650px) and (max-width: 1850px) {
    .aCol {
      height: 50px;
      margin-bottom: 8px;
    }
  }

  @media screen and (max-width: 1649px) {
    .aCol {
      /* height: 27px; */
      margin-bottom: 8px;
    }
  }
</style>