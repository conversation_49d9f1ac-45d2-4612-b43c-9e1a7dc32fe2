<template>
  <div :class="['table-container']">
    <!-- table区域-begin -->
    <div style='overflow-y: auto'>
      <a-table ref='table' :columns='columns' :dataSource='dataSource' :loading='loading' :pagination='ipagination'
        :rowKey='(record,index)=>{return index}' :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}" bordered
        @change='handleTableChange'>
        <!-- 字符串超长截取省略号显示-->
        <span slot='templateContent' slot-scope='text'>
          <j-ellipsis :length='25' :value='text' />
        </span>
        <template slot="name" slot-scope='text'>
          <!-- <a style="color: rgb(23, 87, 204);" @click="linkTo(text)">{{ text }}</a> -->
          <span>{{ text }}</span>
        </template>
        <template slot="cpuTotal" slot-scope='text'>
          <span>{{ (text/1000).toFixed(2) }}</span>
        </template>
        <template slot="memTotal" slot-scope='text'>
          <span>{{ (text/1000000000).toFixed(2) }}</span>
        </template>
        <span slot='powerState' slot-scope='text'>
          <div v-if="text == 'POWERED_ON'" style="background-color: #569B2E;color: white;border-radius: 5px;">
            在线
          </div>
          <div v-else style="background-color: #C9C9C9;color: white;border-radius: 5px;">离线</div>
        </span>
      </a-table>
    </div>
  </div>
</template>

<script>
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import JEllipsis from '@/components/jeecg/JEllipsis'
  import {
    getAction,
    postAction
  } from '@/api/manage'
  import yqIcon from '@comp/tools/SvgIcon'
  export default {
    name: 'virtualizationManagementList',
    mixins: [JeecgListMixin],
    components: {
      JEllipsis,
      yqIcon
    },
    data() {
      return {
        // 表头
        columns: [
          {
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: 'center',
            customRender: function (t, r, index) {
              return parseInt(index) + 1
            },
          }, {
            title: '名称',
            dataIndex: 'name',
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 200px'
              return {
                style: cellStyle
              }
            },
            scopedSlots: {
              customRender: 'name'
            }
          },
          {
            title: 'CPU使用率(%)',
            dataIndex: 'cpuUsage',
            customCell: () => {
              let cellStyle = 'text-align: right;min-width: 90px'
              return {
                style: cellStyle
              }
            }
          },
          // CPU和内存后期会修改，前端处理单位换算
          {
            title: 'CPU总量(GHz)',
            dataIndex: 'cpuTotal',
            customCell: () => {
              let cellStyle = 'text-align: right;min-width: 90px'
              return {
                style: cellStyle
              }
            },
            scopedSlots: {
              customRender: 'cpuTotal'
            }
          },
          {
            title: '内存使用率(%)',
            dataIndex: 'memUsage',
            customCell: () => {
              let cellStyle = 'text-align: right;min-width: 90px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '内存总量(GB)',
            dataIndex: 'memTotal',
            customCell: () => {
              let cellStyle = 'text-align: right;min-width: 90px'
              return {
                style: cellStyle
              }
            },
            scopedSlots: {
              customRender: 'memTotal'
            }
          },
          {
            title: '状态',
            dataIndex: 'powerState',
            customCell: () => {
              let cellStyle = 'text-align: center;width: 80px'
              return {
                style: cellStyle
              }
            },
            scopedSlots: {
              customRender: 'powerState'
            }
          },
          {
            title: '开机时间',
            dataIndex: 'runTime',
            customCell: () => {
              let cellStyle = 'text-align: center;min-width: 200px;max-width:800px'
              return {
                style: cellStyle
              }
            }
          },
        ],
        url: {
          list: '/device/deviceInfo/hostList'
        },
        option: {},
        queryParam: {
          createTimeRange: [],
          keyWord: ''
        },
        disableMixinCreated: true
      }
    },
    methods: {
      show(data, id) {
        this.queryParam.deviceId = this.option.deviceId = id
        this.queryParam.type = data.type
        this.queryParam.title = data.title
        this.loadData(1)
      },
      // linkTo(name) {
      //   this.option.title = name
      //   this.option.type = 'host'
      //   // this.$emit('selected', this.option)
      // },
      loadData(arg, data, id) {
        if (!this.url.list) {
          this.$message.error('请设置url.list属性!')
          return
        }
        //加载数据 若传入参数1则加载第一页的内容
        if (arg === 1) {
          this.ipagination.current = 1
        }
        var params = this.getQueryParams() //查询条件
        this.loading = true
        getAction(this.url.list, params).then((res) => {
          if (res.success) {
            this.dataSource = res.result.records || res.result
            this.ipagination.total = this.dataSource.length
          }
          if (res.code === 510) {
            this.$message.warning(res.message)
          }
          this.loading = false
        })
      },
      refreshVmware() {
        getAction('/alarm/alarmTemplate/refreshVmware', {
          deviceId: this.data.id,
          ip: this.data.ip,
          username: 'root',
          password: 'vms1!123',
          vmwareName: 'ZABBIX-41'
        }).then((res) => {
          if (res.success) {
            this.loadData()
          }
        })
      },
      submitForm(values, data) {
        var unixtime = new Date().getTime()
        const that = this

        that.loading = true
        let httpurl = '/device/deviceInfo/execute'

        let formData = {}
        formData.deviceId = this.queryParam.deviceId
        formData.methodName = data
        formData.vmwareName = values.name
        formData.transferProtocol = 'Vmware'
        postAction(httpurl, formData)
          .then((res) => {
            if (res.success) {
              that.$message.success(res.result)
              if (data == 'PowerOnVM') {
                setTimeout(() => {
                  this.refreshVmware()
                }, 5000)
              } else if (data == 'PowerOffVM') {
                this.refreshVmware()
              }
            } else {
              that.$message.warning(res.result)
            }
          })
          .finally(() => {
            that.loading = false
          })
      }
    }
  }
</script>
<style lang='less' scoped>
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';

  /** Button按钮间距 */
  .ant-btn {
    margin-left: 3px;
  }

  .ant-card-body .table-operator {
    margin-bottom: 18px;
  }

  .ant-table-tbody .ant-table-row td {
    padding-top: 15px;
    padding-bottom: 15px;
  }

  .anty-row-operator button {
    margin: 0 5px;
  }

  .ant-btn-danger {
    background-color: #ffffff;
  }

  .ant-modal-cust-warp {
    height: 100%;
  }

  .ant-modal-cust-warp .ant-modal-body {
    height: calc(100% - 110px) !important;
    overflow-y: auto;
  }

  .ant-modal-cust-warp .ant-modal-content {
    height: 90% !important;
    // overflow-y: hidden;
  }

  .table-page-search-wrapper {
    background-color: #fff;
    padding: 15px 0 0 15px;
  }

  .table-container {
    background-color: #fff;
    padding-right: 24px;
  }

  .query-btn {
    background: #ecf5ff;
    border: 1px solid #b3d8ff;
    border-radius: 4px;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #409eff;
    width: 73px;
    height: 28px;
    cursor: pointer;
    margin: 0px;
  }

  .yq-icon {
    font-size: 24px;
  }
</style>