<template>
  <j-modal
    :title='title'
    :width='width'
    :centered='true'
    :visible='visible'
    :destroyOnClose='true'
    switchFullscreen
    cancelText='关闭'
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @ok='handleOk'
    @cancel='handleCancel'
  >
    <a-spin :spinning='confirmLoading'>
      <j-form-container>
        <a-form-model ref='form' :model='model' :rules='validatorRules' slot='detail'  :labelCol='labelCol' :wrapperCol='wrapperCol'>
          <a-row>
            <a-col :span='24'>
              <a-form-model-item label='任务标识' prop='code'>
                <a-input
                  v-model='model.code'
                  :allow-clear='true'
                  autocomplete='off'
                  placeholder='请输入任务标识'/>
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
              <a-form-model-item label='任务类名' prop='value' >
                <a-input
                  v-model='model.value'
                  :allow-clear='true'
                  autocomplete='off'
                  placeholder='请输入任务类名'/>
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
              <a-form-model-item label='描述' prop='description'>
                <a-textarea
                  v-model='model.description'
                  :auto-size='{ minRows: 5, maxRows: 8 }'
                  :allowClear='true'
                  autocomplete='off'
                  placeholder='请输入描述' />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </j-form-container>
    </a-spin>
  </j-modal>
</template>
<script>
import { getAction, httpAction } from '@api/manage'
import { ajaxGetDictItems, getDictItemsFromCache } from '@api/api'

export default {
  name: 'DdapterTaskModal',
  data() {
    return {
      title: '',
      width: '800px',
      disableSubmit: false,
      visible: false,
      confirmLoading: false,
      labelCol: {
        xs: {span: 24},
        sm: {span: 5}
      },
      wrapperCol: {
        xs: {span: 24},
        sm: {span: 16}
      },
      model: {},
      solutionList:[],
      validatorRules: {
        code: [
          { required: true,message:'请输入任务标识！' },
          { min: 2, max: 255, message: '任务标识长度应在 2-255 之间' }
        ],
        value: [
          { required: true, message:'请输入任务类名！' },
          { min: 2, max: 255, message: '任务类名长度应在 2-255 之间' }
        ],
        description: [
          { required: false,min:0,max:255, message:'描述信息字符长度不能超过255个字符！',trigger: 'change'}
        ]
      },
      url:{
        add: '/device/productJob/add',
        edit:'/device/productJob/edit',
      },
    }
  },

  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      this.visible = true
      this.$nextTick(() => {
        this.model = JSON.parse(JSON.stringify(record))
      })
    },
    close() {
      this.visible = false
    },
    handleOk() {
      let that = this
      that.$refs.form.validate((err, values) => {
        if (err&&!that.confirmLoading) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!that.model.id) {
            httpurl += that.url.add
            method = 'post'
          } else {
            httpurl += that.url.edit
            method = 'put'
          }
          let formData = JSON.parse(JSON.stringify(that.model))
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
                that.close()
              } else {
                that.$message.warning(res.message)
              }
              that.confirmLoading = false
            }).catch((err) => {
            that.$message.warning(err.message)
            that.confirmLoading = false
          })
        }
      })
    },
    handleCancel() {
      this.close()
    },
  }
}
</script>
<style scoped lang='less'>
@import '~@assets/less/normalModal.less';
</style>