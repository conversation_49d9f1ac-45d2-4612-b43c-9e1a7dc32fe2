<template>
  <div class='zr-business-view' ref='zrBusinessView'>
    <div class='business-view-left'>
      <div class='business-view-left-top' ref='lefTop' >
        <zr-business-status v-if='systemInfo' :business-data='systemInfo'></zr-business-status>
      </div>
      <div v-if='leftBottomH' class='business-view-left-bottom' :style='{height:leftBottomH}'>
        <div class='business-view-left-middle'>
          <zr-business-support></zr-business-support>
        </div>
        <div class='business-view-left-bottom'>
          <zr-business-hardware></zr-business-hardware>
        </div>
      </div>
    </div>
    <div class='business-view-center'>
      <zr-status-info></zr-status-info>
      <div class='topo-box'>
        <vis-edit ref='visEdit' operate='show' :blurId='blurId' ></vis-edit>
      </div>
      <div class='legend-block'>
        <zr-focus-info scene='business'></zr-focus-info>
        <div style='height: 50px;width: 100%'>
          <zr-business-score-line></zr-business-score-line>
        </div>
        <div class='legend-block-list'>
          <div class='legend-block-list-item' v-for='item in businessStatus' :key='item.value'>
            <div class='legend-block-list-item-icon' :style='{backgroundColor:item.color}'></div>
            <div class='legend-block-list-item-text'>{{item.label}}</div>
          </div>
        </div>
      </div>
    </div>
    <div class='business-view-right'>
      <div ref='rightTop'>
        <div class='business-view-right-top' style='height: 227px' ref='businessPosition' v-if='systemInfo'>
          <zr-business-position :topoId='systemInfo.topoId' @changeBlur='changeBlur'></zr-business-position>
        </div>
        <div class='business-view-right-middle' ref='businessManager'>
          <zr-business-manager></zr-business-manager>
        </div>
      </div>
      <div v-if='rightBottomH' class='business-view-right-bottom' ref='businessOperations'
           :style='{height:rightBottomH}'>
        <zr-business-operations></zr-business-operations>
      </div>
    </div>
  </div>
</template>
<script>
import resizeObserverMixin from '@views/statsCenter/com/resizeObserverMixin'
import ZrBusinessStatus from '@views/zrBigscreenStatic/zrBusiness/modules/ZrBusinessStatus.vue'
import ZrBusinessHardware from '@views/zrBigscreenStatic/zrBusiness/modules/ZrBusinessHardware.vue'
import ZrBusinessSupport from '@views/zrBigscreenStatic/zrBusiness/modules/ZrBusinessSupport.vue'
import ZrBusinessPosition from '@views/zrBigscreenStatic/zrBusiness/modules/ZrBusinessPosition.vue'
import ZrBusinessManager from '@views/zrBigscreenStatic/zrBusiness/modules/ZrBusinessManager.vue'
import ZrStatusInfo from '@views/zrBigscreenStatic/zrCompNational/modules/ZrStatusInfo.vue'
import ZrFocusInfo from '@views/zrBigscreenStatic/zrCompNational/modules/ZrFocusInfo.vue'
import ZrBusinessOperations from '@views/zrBigscreenStatic/zrBusiness/modules/ZrBusinessOperations.vue'
import VisEdit from '@views/topo/nettopo/modules/VisEdit.vue'
import { businessStatus,systemList } from '@views/zrBigscreenStatic/modules/zrUtil'
import ZrBusinessScoreLine from './modules/ZrBusinessScoreLine.vue'

export default {
  name: 'businessIndex',
  components: {
    ZrBusinessScoreLine,
    VisEdit,
    ZrBusinessStatus,
    ZrBusinessSupport,
    ZrBusinessHardware,
    ZrBusinessPosition,
    ZrBusinessManager,
    ZrBusinessOperations,
    ZrStatusInfo,
    ZrFocusInfo
  },
  mixins: [resizeObserverMixin],
  data() {
    return {
      centerH: '',
      centerPd: "",
      legndBottom:"",
      leftBottomH: 0,
      rightBottomH: 0,
      businessStatus,
      systemInfo:null,
      systemId:"",
      blurId:"",
    }
  },
  created() {
    this.systemId = this.$route.query?.id
  },
  mounted() {
    this.getSystemInfo()
  },
  methods: {
    changeBlur(e){
      this.blurId = e
    },
    //监听页面缩放 更新中间区域高度
    resizeObserverCb() {
      // let hScaleValue = window.innerHeight / 1080
      // this.centerH = `calc(100% - (70px * ${hScaleValue}))`
      // this.centerPd = `calc(180px * ${hScaleValue})`
      // this.legndBottom = `calc(60px * ${hScaleValue})`
      let leftTopRect = this.$refs.lefTop.getBoundingClientRect()
      this.leftBottomH = `calc(100% - ${leftTopRect.height}px)`
      let rightTopRect = this.$refs.rightTop.getBoundingClientRect()
      this.rightBottomH = `calc(100% - ${rightTopRect.height}px)`
      // 重新计算拓扑图画布
      if (this.$refs.visEdit) {
        let timer = setTimeout(() => {
          clearTimeout(timer)
          timer = null
          this.$refs.visEdit.resizeTopo()
        }, 80)
      }

    },
    getSystemInfo() {
      this.systemInfo = this.systemId? systemList.find(item => item.id == this.systemId): systemList[0]
      this.$refs.visEdit.createTopo(this.systemInfo.topoId)
    }
  }
}
</script>

<style scoped lang='less'>
.zr-business-view {
  padding-left: calc(33 / 19.2 * 1vw);
  padding-right: calc(33 / 19.2 * 1vw);
  display: flex;
  align-items: center;
  justify-content: space-around;
  height: 100%;

  .business-view-left {
    width: 25%;
    height: 100%;
    //background-image: url(/zrBigScreen/zrBusiness/businessLeftBg.png);
    //background-size: 100% 100%;
    //background-repeat: no-repeat;
    padding: 0px 0px 0px 0px;
    display: flex;
    flex-direction: column;

    .business-view-left-bottom {
      display: flex;
      flex-direction: column;

      .business-view-left-middle {
        height: 50%
      }

      .business-view-left-bottom {
        height: calc(50% - 8px);
      }
    }
  }

  .business-view-center {
    width: 50%;
    height: 100%;
    //background-image: url(/zrBigScreen/zrBusiness/businessCenterBg.png);
    //background-size: 100% 100%;
    //background-repeat: no-repeat;
    padding: 0px 16px 0px;
    position: relative;
    .topo-box{
      margin-top: 8px;
      height:calc(100% - 100px - 60px - 8px);
    }
    .legend-block {
      width:100%;
      position: absolute;
      bottom: 0px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      .legend-block-list {
        display: flex;
        .legend-block-list-item {
          display: flex;
          align-items: center;
          font-weight: 400;
          font-size: 14px;
          padding: 0 20px;
          color: #E3E7EF;
          .legend-block-list-item-icon{
            width:14px;
            height: 14px;
            margin-right: 12px;
          }
        }
      }
    }
  }

  .business-view-right {
    width: 25%;
    height: 100%;
    //background-image: url(/zrBigScreen/zrBusiness/businessRightBg.png);
    //background-size: 100% 100%;
    //background-repeat: no-repeat;
    padding: 0px 0px 0px 0px;
    display: flex;
    flex-direction: column;
  }
}
</style>