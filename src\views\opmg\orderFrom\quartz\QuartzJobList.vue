<template>
  <a-card :bordered="false">

    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
        <a-row :gutter="24" ref="row">

          <a-col :span="spanValue">
            <a-form-item label="任务名称">
              <a-input placeholder="请输入任务类名" v-model="queryParam.orderName"></a-input>
            </a-form-item>
          </a-col>
          <!-- <a-col :span="spanValue">
            <a-form-item label="任务状态">
              <a-select v-model="queryParam.status" placeholder="请选择状态" :allowClear="true">
                <a-select-option value="">全部</a-select-option>
                <a-select-option value="0">正常</a-select-option>
                <a-select-option value="-1">停止</a-select-option>
              </a-select>
            </a-form-item>
          </a-col> -->
          <a-col :span="spanValue">
                <a-form-item label="任务状态:">
                  <j-dict-select-tag
                    v-model="queryParam.status"
                    dictCode="quartz_status"
                    :allowClear="true"
                    placeholder="请选择任务状态"
                  />
                </a-form-item>
              </a-col>

          <a-col :span="colBtnsSpan()">
            <span class="table-page-search-submitButtons" :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
              <a-button type="primary" @click="searchQuery" class="btn-search btn-search-style">查询
              </a-button>
              <a-button @click="searchReset" class="btn-reset btn-reset-style">重置</a-button>
            </span>
          </a-col>

        </a-row>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary">新增</a-button>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay" style='text-align: center'>
          <a-menu-item key="1" @click="batchDel">删除</a-menu-item>
        </a-menu>
        <a-button> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <a-table
        ref="table"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        @change="handleTableChange">

        <!-- 字符串超长截取省略号显示-->
        <span slot="description" slot-scope="text">
          <j-ellipsis :value="text" :length="20" />
        </span>
        <span slot="parameterRender" slot-scope="text">
          <j-ellipsis :value="text" :length="20" />
        </span>


        <span slot="action" slot-scope="text, record">
          <a @click="resumeJob(record)" v-if="record.status==-1">启动</a>
          <a @click="pauseJob(record)" v-if="record.status==0">停止</a>

          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item><a @click="executeImmediately(record)">执行一次</a></a-menu-item>
              <a-menu-item><a @click="handleEdit(record)">编辑</a></a-menu-item>
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>

        <!-- 状态渲染模板 -->
        <template slot="customRenderStatus" slot-scope="status">
          <a-tag v-if="status==0" color="green">已启动</a-tag>
          <a-tag v-if="status==-1" color="orange">已暂停</a-tag>
        </template>
      </a-table>
    </div>
    <!-- table区域-end -->

    <!-- 表单区域 -->
    <quartzJob-modal ref="modalForm" @ok="modalFormOk"></quartzJob-modal>
  </a-card>
</template>

<script>
  import QuartzJobModal from './modules/QuartzJobModal'
  import { getAction } from '@/api/manage'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import JEllipsis from "@/components/jeecg/JEllipsis";
  import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'

  export default {
    name: "QuartzJobList",
    mixins:[JeecgListMixin,YqFormSearchLocation],
    components: {
      QuartzJobModal,
      JEllipsis
    },
    data () {
      return {
        description: '定时任务在线管理',
        // 查询条件
        queryParam: {},
        formItemLayout: {
          labelCol: { style: 'width:90px' },
          wrapperCol: {
            style: 'width:calc(100% - 90px)'
          }
        },
        // 表头
        columns: [
           {
            title: '任务名称',
            align:"center",
            dataIndex: 'orderName'
          },
          // {
          //   title: '任务类名',
          //   align:"center",
          //   dataIndex: 'jobClassName',
          //   sorter: true,
          // },
          {
            title: 'cron表达式',
            align:"center",
            dataIndex: 'cronExpression'
          },
          {
            title: '参数',
            align:"center",
            width: 150,
            dataIndex: 'parameter',
            scopedSlots: {customRender: 'parameterRender'},
          },
          {
            title: '描述',
            align:"center",
            width: 250,
            dataIndex: 'description',
            scopedSlots: {customRender: 'description'},
          },
          {
            title: '任务状态',
            align:"center",
            dataIndex: 'status',
            scopedSlots: { customRender: 'customRenderStatus' },
            filterMultiple: false,
          },
          {
            title: '操作',
            dataIndex: 'action',
            align:"center",
            width:180,
            scopedSlots: { customRender: 'action' },
          }
        ],
		url: {
          list: "/sys/quartzJob/orderlist",
          delete: "/sys/quartzJob/delete",
          deleteBatch: "/sys/quartzJob/deleteBatch",
          pause: "/sys/quartzJob/pause",
          resume: "/sys/quartzJob/resume",
          exportXlsUrl: "sys/quartzJob/exportXls",
          importExcelUrl: "sys/quartzJob/importExcel",
          execute: "sys/quartzJob/execute"
        },
      }
    },
    computed: {
      importExcelUrl: function () {
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
      }
    },

    methods: {

      //筛选需要重写handleTableChange
      // handleTableChange(pagination, filters, sorter) {
      //   //分页、排序、筛选变化时触发
      //   //TODO 筛选
      //   if (Object.keys(sorter).length > 0) {
      //     this.isorter.column = sorter.field;
      //     this.isorter.order = "ascend" == sorter.order ? "asc" : "desc"
      //   }
      //   //这种筛选方式只支持单选
      //   this.filters.status = filters.status[0];
      //   this.ipagination = pagination.current;
      //   this.loadData();
      // },
      pauseJob: function(record){
        var that = this;
        //暂停定时任务
        this.$confirm({
          title:"确认暂停",
          okText: '是',
          cancelText: '否',
          content:"是否暂停选中任务?",
          onOk: function(){
            getAction(that.url.pause,{jobClassName:record.jobClassName}).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.loadData();
                that.onClearSelected();
              }else{
                that.$message.warning(res.message);
              }
            });
          }
        });

      },
      resumeJob: function(record){
        var that = this;
        //恢复定时任务
        this.$confirm({
          title:"确认启动",
          okText: '是',
          cancelText: '否',
          content:"是否启动选中任务?",
          onOk: function(){
            getAction(that.url.resume,{jobClassName:record.jobClassName}).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.loadData();
                that.onClearSelected();
              }else{
                that.$message.warning(res.message);
              }
            });
          }
        });
      },
      executeImmediately(record){
        var that = this;
        //立即执行定时任务
        this.$confirm({
          title:"确认提示",
          okText: '是',
          cancelText: '否',
          content:"是否立即执行任务?",
          onOk: function(){
            getAction(that.url.execute,{id:record.id}).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.loadData();
                that.onClearSelected();
              }else{
                that.$message.warning(res.message);
              }
            });
          }
        });
      }
    }
  }
</script>
<style scoped lang='less'>
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';
</style>