<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll zxw">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <div style="height: 100%">
        <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
          <!-- 查询区域 -->
          <div class="table-page-search-wrapper">
            <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
              <a-row :gutter="24" ref="row">
                <a-col :span="spanValue">
                  <a-form-item label="出库日期：">
                    <a-range-picker
                      v-model="queryParam.warehousingTime"
                      @change="onChangePicker"
                      format="YYYY-MM-DD"
                      :placeholder="['开始日期', '截止日期']"
                      style="width: 100%"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="colBtnsSpan()">
                  <span
                    class="table-page-search-submitButtons"
                    :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                  >
                    <a-button type="primary" @click="searchQuery" class="btn-search-style">查询</a-button>
                    <a-button @click="searchReset" style="margin-left: 10px" class="btn-reset-style">重置</a-button>
                  </span>
                </a-col>
              </a-row>
            </a-form>
          </div>
          <!-- 查询区域-END -->
        </a-card>
        <a-card :bordered="false" style="flex: auto" class="core">
          <!-- 操作按钮区域 -->
          <div class="table-operator" style="width: 100%">
            <a-button @click="handleAdd" v-has="'outDepot:add'">新增</a-button>
            <!-- 高级查询区域 -->
            <a-dropdown v-if="selectedRowKeys.length > 0" v-has="'outDepot:delete'">
              <a-menu slot="overlay" style='text-align: center'>
                <a-menu-item key="1" @click="batchDel">删除</a-menu-item>
              </a-menu>
              <a-button style="margin-left: 8px">
                批量操作
                <a-icon type="down" />
              </a-button>
            </a-dropdown>
          </div>

          <!-- table区域-begin -->
          <div>
            <a-table
              ref="table"
              bordered
              rowKey="id"
              :columns="columns"
              :dataSource="dataSource"
              :pagination="ipagination"
              :loading="loading"
              :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
              class="j-table-force-nowrap"
              @change="handleTableChange"
              style="margin-bottom: 16px; overflow: hidden"
            >
              <template slot="target" slot-scope="record">
                <span v-if="record.outType_dictText == '内部领用'" style="font-size: 14px">
                  {{ record.outTargetUserName }}
                </span>

                <span v-if="record.outType_dictText == '退货出库'" style="font-size: 14px">
                  {{ record.outTargetSupplierName }}
                </span>

                <span v-if="record.outType_dictText == '其他出库'" style="font-size: 14px">
                  {{ record.outTarget }}
                </span>
              </template>

              <span slot="action" slot-scope="text, record">
                <a @click="openDetail(record)">查看</a>
              </span>
              <template slot="tooltip" slot-scope="text">
                <a-tooltip placement="topLeft" :title="text" trigger="hover">
                  <div class="tooltip">
                    {{ text }}
                  </div>
                </a-tooltip>
              </template>
            </a-table>
          </div>

          <itil-out-depot-modal ref="modalForm" @ok="modalFormOk"></itil-out-depot-modal>
        </a-card>
      </div>
    </a-col>
  </a-row>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import ItilOutDepotModal from './modules/ItilOutDepotModal'
import { filterMultiDictText } from '@/components/dict/JDictSelectUtil'
import { getAction, downFile } from '@/api/manage'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'

export default {
  name: 'ItilOutDepotList',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components: {
    ItilOutDepotModal
  },
  data() {
    return {
      description: '出库表管理页面',
      // 表头
      columns: [
        {
          title: '出库编码',
          dataIndex: 'outCode'
        },
        {
          title: '出库日期',
          dataIndex: 'outTime'
        },
        {
          title: '出库类型',
          dataIndex: 'outType_dictText'
        },
        {
          title: '出库目标',
          scopedSlots: { customRender: 'target' }
        },
        {
          title: '总金额(￥)',
          dataIndex: 'totalMoney'
        },
        {
          title: '备注',
          dataIndex: 'remarks',
          scopedSlots: { customRender: 'tooltip' },
          customCell: () => {
            let cellStyle = 'text-align: left; min-width: 100px;max-width:400px'
            return { style: cellStyle }
          },
        },
        {
          title: '操作',
          dataIndex: 'action',
          fixed: 'right',
          align: 'center',
          width: 130,
          scopedSlots: { customRender: 'action' },
        },
      ],
      url: {
        list: '/itilOutDepot/itilOutDepot/list',
        delete: '/itilOutDepot/itilOutDepot/delete',
        deleteBatch: '/itilOutDepot/itilOutDepot/deleteBatch',
        exportXlsUrl: '/itilOutDepot/itilOutDepot/exportXls',
        importExcelUrl: 'itilOutDepot/itilOutDepot/importExcel',
      }
    }
  },

  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
  },
  methods: {
    onChangePicker(data, dateString) {
      this.queryParam.time1 = dateString[0]
      this.queryParam.time2 = dateString[1]
    },
    openDetail(record) {
      getAction('/itilOutDepot/itilOutDepot/details', { outId: record.id }).then((res) => {
        if (res.success) {
          this.handleDetailPage(res.result)
        }
      })
    }
  },
}
</script>
<style lang="less" scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
.header-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-family: PingFangSC-Medium;
  font-size: 18px;
  color: #000000;
}
.header-oddNumber {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 20px;
  span {
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
  }
  .table-operator {
    margin: 0px;
  }
  div {
    button {
      margin-bottom: 0;
    }
    button:nth-child(2) {
      margin-right: 0px;
    }
  }
}

.card-core {
  display: flex;
  flex-direction: column;
  padding: 30px;
  padding-bottom: 80px;
  border: 1px solid #e8e8e8;
  .core-title {
    font-size: 28px;
    font-family: PingFangSC-Medium;
    color: #000000;
    text-align: center;
  }
  .core-time {
    width: 100%;
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    margin-top: 30px;
    span:nth-child(1) {
      color: rgba(0, 0, 0, 0.65);
    }
    span:nth-child(2) {
      color: #000000;
    }
  }
  .core-information {
    display: flex;
    justify-content: space-between;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    margin-top: 16px;
  }
  .core-table {
    margin-top: 20px;
    ul {
      border: 1px solid #e8e8e8;
      padding: 0;
      li {
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #909399;
        display: flex;
        justify-content: space-between;
        border-bottom: 1px solid #e8e8e8;
        align-items: center;
        span {
          height: 40px;
          line-height: 40px;
          text-align: center;
          border-right: 1px solid #e8e8e8;
        }
        span:nth-child(1) {
          width: 6%;
        }
        span:nth-child(2) {
          width: 20%;
        }
        span:nth-child(3) {
          width: 11%;
        }
        span:nth-child(4) {
          width: 11%;
        }
        span:nth-child(5) {
          width: 6%;
        }
        span:nth-child(6) {
          width: 10%;
        }
        span:nth-child(7) {
          width: 12%;
        }
        span:nth-child(8) {
          width: 10%;
        }
        span:nth-child(9) {
          width: 14%;
        }
        span:last-of-type {
          border: none;
        }
      }
      li:nth-child(1) {
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.85);
      }
      li:last-of-type {
        border: none;
      }
    }
  }

  .gridtable {
    width: 100%;
    margin-top: 20px;
    font-family: verdana, arial, sans-serif;
    font-size: 14px;
    color: #606266;
    border-width: 1px;
    border-color: #e8e8e8;
    border-collapse: collapse;
    text-align: center;
    thead > tr > td {
      white-space: nowrap;
    }

    tbody > tr > td {
      white-space: normal;
    }

    td {
      padding: 5px;
      height: 40px;
      line-height: 32px;
      border-width: 1px;
      border-style: solid;
      border-color: #e8e8e8;
    }

    /*td:nth-child(1){
      width: 6%;
    }
    td:nth-child(2){
      width: 18%;
    }
    td:nth-child(3){
      width: 11%;
    }
    td:nth-child(4){
      width: 11%;
    }
    td:nth-child(5){
      width: 8%;
    }
    td:nth-child(6){
      width: 10%;
    }
    td:nth-child(7){
      width: 10%;
    }
    td:nth-child(8){
      width: 12%;
    }
    td:nth-child(9){
      width: 14%;
    }*/
  }
}

.gridtable {
  width: 100%;
  margin-top: 20px;
  font-family: verdana, arial, sans-serif;
  font-size: 14px;
  color: #606266;
  border-width: 1px;
  border-color: #e8e8e8;
  border-collapse: collapse;
  text-align: center;
  thead > tr > td {
    white-space: nowrap;
  }

  tbody > tr > td {
    white-space: normal;
    word-break: break-word;
  }

  td {
    padding: 5px;
    height: 40px;
    line-height: 32px;
    border-width: 1px;
    border-style: solid;
    border-color: #e8e8e8;
  }

  /*td:nth-child(1){
    width: 6%;
  }
  td:nth-child(2){
    width: 18%;
  }
  td:nth-child(3){
    width: 11%;
  }
  td:nth-child(4){
    width: 11%;
  }
  td:nth-child(5){
    width: 8%;
  }
  td:nth-child(6){
    width: 10%;
  }
  td:nth-child(7){
    width: 10%;
  }
  td:nth-child(8){
    width: 12%;
  }
  td:nth-child(9){
    width: 14%;
  }*/
}

.totalPrice {
  height: 40px;
  padding: 0 30px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  font-family: PingFangSC-Medium;
  color: #000000;
  border: 1px solid #e8e8e8;
  border-top: none;
  div {
    span:nth-child(2) {
      color: red;
    }
  }
}

.core-bottom {
  padding: 30px;
  div {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    span {
      width: 33%;
    }
    span:nth-child(4) {
      margin-top: 20px;
    }
  }
}

li {
  list-style-type: none !important;
}
::v-deep .ant-table-tbody {
  font-style: none !important;
}
</style>