<template>
  <div style="height:100%">
    <keep-alive exclude='logInfoDetails'>
      <component :is="pageName" :data="data" :show-document='showDocument'/>
    </keep-alive>
  </div>
</template>
<script>
import logInfoList from './logInfoList.vue'
import logInfoDetails from './logInfoDetails.vue'
export default {
  name: "logInfoManagement",
  data() {
    return {
      isActive: 0,
      data: {},
      showDocument:false
    };
  },
  components: {
    logInfoList,
    logInfoDetails
  },
  created() {
    this.pButton1(0);
  },
  //使用计算属性
  computed: {
    pageName() {
      switch (this.isActive) {
        case 0:
          return "logInfoList";
        default:
          return "logInfoDetails";
      }
    }
  },
  methods: {
    pButton1(index) {
      this.isActive = index;
    },
    pButton2(index, item,showDocument) {
      this.isActive = index;
      this.data = item
      this.showDocument=showDocument
    }
  }
}
</script>