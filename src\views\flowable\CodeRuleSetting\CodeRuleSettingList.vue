<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind='formItemLayout'>
        <a-row :gutter="24" ref='row'>
          <a-col v-show="getVisible('code_dictText')" :span='spanValue'>
            <a-form-item :label="getTitle('code_dictText')">
              <j-dict-select-tag v-model="queryParam.code" dictCode="code-generation-bind"
                                 placeholder="请选择业务绑定" />
            </a-form-item>
          </a-col>
          <a-col :span='spanValue'>
               <span class='table-page-search-submitButtons' style='overflow: hidden;'>
                 <a-button icon='search' type='primary' @click='searchQuery'>查询</a-button>
                 <a-button icon='reload' style='margin-left: 8px' @click='searchReset'>重置</a-button>
                  <a v-if='queryItems.length>0' style='margin-left: 8px' @click='doToggleSearch'>{{ queryName }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                 </a>
               </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!--自定义查询项 -->
    <div v-if='toggleSearchStatus' class='custom-query-item'>
      <a-checkbox-group
        v-model='settingQueryItems'
        :defaultValue='settingQueryItems'
        style='width:100%'
        @change='onQuerySettingsChange'>
        <a-row :gutter="24">
          <template v-for='(item,index) in queryItems'>
            <a-col v-show='item.checked' :span='querySpanValue' class='col-checkbox'>
              <a-checkbox :disabled='item.disabled' :value='item.dataIndex'>
                <j-ellipsis :length='10' :value='item.title'></j-ellipsis>
              </a-checkbox>
            </a-col>
          </template>
        </a-row>
      </a-checkbox-group>
    </div>
    <!-- 自定义查询项-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button icon="plus" type="primary" @click="handleAdd" v-has='"codeRule:add"'>新增</a-button>
      <!--      <a-button icon="download" type="default" @click="handleExportXls('编码生成规则')">导出</a-button>
            <a-upload :action="importExcelUrl" :headers="tokenHeader" :multiple="false" :showUploadList="false" name="file"
                      @change="handleImportExcel">
              <a-button icon="import" type="default">导入</a-button>
            </a-upload>-->
      <a-dropdown v-if="selectedRowKeys.length > 0" v-has='"codeRule:delete"'>
        <a-menu slot="overlay" style='text-align: center'>
          <a-menu-item key="1" @click="batchDel">删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作
          <a-icon type="down" />
        </a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <!--      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
              <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a
              style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
              <a style="margin-left: 24px" @click="onClearSelected">清空</a>
            </div>-->

      <a-table
        ref="table"
        bordered
        rowKey="id"
        size="middle"
        :columns="columns"
        :dataSource="dataSource"
        :loading="loading"
        :pagination="ipagination"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        class="j-table-force-nowrap"
        :scroll="{x:true}"
        @change="handleTableChange">

        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text,record">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
          <img v-else :preview="record.id" :src="getImgView(text)" alt="" height="25px"
               style="max-width:80px;font-size: 12px;font-style: italic;" />
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button
            v-else
            :ghost="true"
            icon="download"
            size="small"
            type="primary"
            @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleDetail(record)">查看</a>
          <span v-has='"codeRule:edit","codeRule:delete"'>
            <a-divider type="vertical" />
            <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item v-has='"codeRule:edit"'>
                 <a @click="handleEdit(record)">编辑</a>
              </a-menu-item>
              <a-menu-item v-has='"codeRule:delete"'>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
         </span>
        </span>
      </a-table>
    </div>

    <code-rule-setting-modal :hadCodeList='hadCodeList' ref="modalForm" @ok="modalFormOk"></code-rule-setting-modal>
  </a-card>
</template>

<script>

import '@assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import CodeRuleSettingModal from './modules/CodeRuleSettingModal'
import { YqFormSeniorSearchLocation } from '@/mixins/YqFormSeniorSearchLocation'
import { getAction } from '@api/manage'

export default {
  name: 'CodeRuleSettingList',
  mixins: [JeecgListMixin, mixinDevice, YqFormSeniorSearchLocation],
  components: {
    CodeRuleSettingModal
  },
  data() {
    return {
      description: '编码生成规则管理页面',
      hadCodeList: [],
      // 表头
      columns: [
        {
          title: '序号',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          isUsed: false,
          customRender: function(t, r, index) {
            return parseInt(index) + 1
          }
        },
        {
          title: '业务绑定',
          align: 'center',
          dataIndex: 'code_dictText',
          isUsed: true
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 150,
          isUsed: false,
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: '/CodeRuleSetting/codeRuleSetting/list',
        delete: '/CodeRuleSetting/codeRuleSetting/delete',
        deleteBatch: '/CodeRuleSetting/codeRuleSetting/deleteBatch',
        exportXlsUrl: '/CodeRuleSetting/codeRuleSetting/exportXls',
        importExcelUrl: 'CodeRuleSetting/codeRuleSetting/importExcel'
      }
    }
  },
  created() {
    this.getColumns(this.columns)
    this.getList()
  },
  computed: {
    importExcelUrl: function() {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    }
  },
  methods: {
    getList() {
      getAction("/CodeRuleSetting/codeRuleSetting/queryList", {}).then((res) => {
        if (res.success) {
          this.hadCodeList=res.result;
        }
      })
    }
  }
}
</script>
<style scoped lang='less'>
@import '~@assets/less/common.less';
@import '~@assets/less/YQCommon.less';
</style>