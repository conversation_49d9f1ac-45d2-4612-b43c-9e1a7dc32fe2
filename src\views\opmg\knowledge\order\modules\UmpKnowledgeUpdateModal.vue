<template>
  <a-modal
    :title="title"
    :width="width"
    :visible="visible"
    :centered="true"
    switchFullscreen
    @ok="handleOk"
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @cancel="handleCancel"
    cancelText="关闭">
    <ump-knowledge-update-form ref="realForm" @ok="submitCallback" :disabled="disableSubmit"></ump-knowledge-update-form>
  </a-modal>
</template>

<script>

  import UmpKnowledgeUpdateForm from './UmpKnowledgeUpdateForm'
  export default {
    name: 'UmpKnowledgeUpdateModal',
    components: {
      UmpKnowledgeUpdateForm
    },
    data () {
      return {
        title:'',
        width:800,
        visible: false,
        disableSubmit: true
      }
    },
    methods: {
      add () {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.add();
        })
      },
      edit (record) {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.edit(record);
        })
      },
      close () {
        this.$emit('close');
        this.visible = false;
      },
      handleOk () {
        this.$refs.realForm.submitForm();
      },
      submitCallback(){
        this.$emit('ok');
        this.visible = false;
      },
      handleCancel () {
        this.close()
      }
    }
  }
</script>
<style lang="less" scoped>
::v-deep .ant-modal-body {
  padding: 24px 48px 24px 48px;
}
::v-deep .ant-modal {
  padding: 24px;
}
</style>