//面板拓扑字段
let panelTopoInfo = {
    "id": "1476358077862133762",
    "topoName": "tp1",//拓扑名称
    "topoJson": "", //预留字段 防止以后使用json
    "topoType": "0",//预留字段 正反面拓扑可能使用
    "topoConfig": "{}",//拓扑图配置属性
    "productId":"",//产品id
  }
  //面板拓扑节点字段
let  nodeInfo =
    {
      "id": "6d8656c7-a532-4442-9e63-00b4dbceeb06",
      "topoId": "1476358077862133762",
      "nodePosition": "{\"x\":-190,\"y\":70}",
      "nodeSize": "{\"width\":60,\"height\":60}",
      "nodeAttrs": "{\"body\":{\"fill\":\"none\",\"opacity\":0,\"rx\":30,\"ry\":30},\"image\":{\"xlink:href\":\"sys/common/static/default/server.png\",\"opacity\":1},\"label\":{\"text\":\"运维监控\"}}",
      "nodeVisible": null,
      "nodeShape": "switch-node",
      "portMarkup": "[{\"tagName\":\"circle\",\"selector\":\"portBody\"}]",
      "nodePorts": "{\"groups\":{\"top\":{\"position\":\"top\",\"attrs\":{\"circle\":{\"r\":3,\"magnet\":true,\"stroke\":\"#5F95FF\",\"strokeWidth\":1,\"fill\":\"#fff\",\"style\":{\"visibility\":\"visible\"}}}},\"right\":{\"position\":\"right\",\"attrs\":{\"circle\":{\"r\":3,\"magnet\":true,\"stroke\":\"#5F95FF\",\"strokeWidth\":1,\"fill\":\"#fff\",\"style\":{\"visibility\":\"visible\"}}}},\"left\":{\"position\":\"left\",\"attrs\":{\"circle\":{\"r\":3,\"magnet\":true,\"stroke\":\"#5F95FF\",\"strokeWidth\":1,\"fill\":\"#fff\",\"style\":{\"visibility\":\"visible\"}}}},\"bottom\":{\"position\":\"bottom\",\"attrs\":{\"circle\":{\"r\":3,\"magnet\":true,\"stroke\":\"#5F95FF\",\"strokeWidth\":1,\"fill\":\"#fff\",\"style\":{\"visibility\":\"visible\"}}}}},\"items\":[]}",
      "portLabelMarkup": null,
      "nodeData": "{\"deviceId\":\"1705110120380813313\",\"deviceCode\":\"test\",\"deviceName\":\"运维监控\",\"deviceType\":\"\",\"devicePanel\":\"\",\"productId\":\"\",\"productName\":\"\",\"nodeType\":\"device\",\"isVirtual\":false,\"nodeScore\":0,\"status\":1,\"alarmNode\":false}",
      "nodeZIndex": 5,
      "nodeChildren": null,
      "nodeParent": null,
      "nodeView": null,
      "markUp": null,
      "nodeTools": null,
      "nodeAngle": null,
      "nodeConfig": null,
    }
//面板拓扑连线字段
  let edgeInfo = 
    {
      "id": "2defc4b1-4665-4720-89c9-3c2c04f0426a",
      "topoId": "1476358077862133762",
      "edgeShape": "edge",
      "edgeAttrs": "{\"line\":{\"stroke\":\"#e5e5e5\",\"targetMarker\":{\"name\":\"\",\"size\":8},\"strokeDasharray\":\"0\",\"sourceMarker\":{\"name\":\"\",\"size\":8}}}",
      "edgeParent": null,
      "edgeZIndex": 3,
      "markUp": null,
      "edgeView": null,
      "edgeTools": null,
      "edgeData": "{\"edgeType\":\"connecting\",\"fromDevice\":\"test\",\"fromDeviceName\":\"运维监控\",\"toDevice\":\"huawei\",\"toDeviceName\":\"路由器\",\"fromPort\":\"\",\"toPort\":\"\",\"status\":1}",
      "edgeRouter": "{\"name\":\"normal\"}",
      "edgeConnector": "{\"name\":\"normal\"}",
      "edgeVertices": null,
      "edgeVisible": null,
      "edgeConfig": null,
      "edgeSource": "{\"cell\":\"6d8656c7-a532-4442-9e63-00b4dbceeb06\"}",
      "edgeTarget": "{\"cell\":\"c80850de-7220-4185-b498-f68fb49c13a4\"}",
    }