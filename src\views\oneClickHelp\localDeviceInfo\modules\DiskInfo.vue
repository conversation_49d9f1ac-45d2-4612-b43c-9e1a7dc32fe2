<template>
  <card-frame title='磁盘'>
    <div slot='bodySlot' class='bodySlot' v-if='Object.keys(info).length >0&&info.infoArr.length>0'>
      <div class='disk-info'>
        <div class='left'>
          <div class='left-table'>
            <div class='table-header table-layout'>
              <div >磁盘名称</div>
              <div>总量</div>
              <div>使用量</div>
            </div>
            <div class='table-body-tr table-layout' v-for='(item ,index) in info.infoArr' :key='"body_"+index'>
              <div class='table-td'>{{item.diskName}}</div>
              <div class='table-td'>{{item.partTotal}}</div>
              <div class='table-td'>{{item.partUsed}}</div>
            </div>
          </div>
        </div>
<!--        <div class='right' v-if='info.trendXData.length>0&&info.trendYData.length>0'>-->
<!--          <div class='rate'>-->
<!--            {{rate}}-->
<!--          </div>-->
<!--          <div id='chart_disk' class='chart'></div>-->
<!--        </div>-->
      </div>
    </div>
    <div slot='bodySlot' class='body-height body-empty' v-else>
      <a-spin :spinning='loading' v-if='loading' class='spin'></a-spin>
      <a-list :data-source='[]' :locale='locale' v-else />
    </div>
  </card-frame>
</template>
<script>
import cardFrame from '@views/oneClickHelp/localDeviceInfo/modules/CardFrame.vue'
import Empty from '@/components/oneClickHelp/Empty.vue'
import echarts from 'echarts/lib/echarts'
import 'echarts/lib/component/graphic'
export default {
  name: "DiskInfo",
  props: {
    info: {
      type: Object,
      required: true
    },
    loading: {
      type: Boolean,
      required: false,
      default:false
    }
  },
  components:{cardFrame, Empty},
  data() {
    return {
      rate:'',
      locale: {
        emptyText: <Empty/>
      }
    }
  },
  watch:{
    info:{
      handler(nValue){
        this.rate=''
        // if(Object.keys(nValue).length>0&&nValue.infoArr.length>0&&nValue.trendXData.length>0&&nValue.trendYData.length>0) {
        //   this.$nextTick(() => {
        //     this.trend(nValue)
        //   })
        // }
      }
    },
    deep:true,
    immediate:true
  },
  methods: {
    trend(data) {
      let myChart = this.$echarts.init(document.getElementById('chart_disk'))
      let option= {
        tooltip: {
          trigger: 'axis',
          padding: [8,8],
          backgroundColor: 'rgba(25,41,56,0.75)',
          borderColor: '#192938',
          width: 55,
          position: 'top',
          axisPointer: {
            type: 'line',
            lineStyle: {
              type: 'dotted',
              width: 0.5,
              color: 'rgba(255,255,255,0.78)',
              cap: 'none',
            },
          },
          textStyle: {
            color: 'rgba(255,255,255,0.78)',
            fontSize: 15,
          },
          formatter:(params)=>{
            this.rate=params[0].data+"%"
            let dotHtml = '<span style="display:inline-block;margin-right:5px;border-radius:100%;width:10px;height:10px;background-color:#7af8b7"></span>'
            if(params[0].axisValue){
              return params[0].axisValue+`<br/>`+dotHtml+params[0].seriesName+":"+params[0].data+"%"
            }else {
              return dotHtml+params[0].seriesName+":"+params[0].data+"%"
            }
          }
        },
        grid: {
          top: '20px',
          left: '2%',
          right: '2%',
          bottom: '1%',
          // containLabel: true,
        },
        xAxis: [{
          type: 'category',
          boundaryGap: false,//两端不留白
          data: data.trendXData,
          axisLine: {
            show: true,
            lineStyle: {
              width: 1,
              color: "rgba(151,151,151,0.2)",
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            show: false
          },
        }],
        yAxis: [{
          type: 'value',
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
            lineStyle: {
              width: 1,
              color: "rgba(151,151,151,0.2)",
            },
          },
          axisLabel: {
            show: false,
          },
          splitLine: {
            show: false,
          },
        }],
        series: [{
          name: '磁盘使用率',
          type: 'line',
          smooth: true,
          symbol: 'circle',
          symbolSize: 1,
          showSymbol: false,
          itemStyle:{
            normal:{
              color:'#7af8b7'//symbol颜色
            }
          },
          lineStyle: {
            width: 1,
            color: '#9EFFCD'
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: '#7EEBB3',
              },
              {
                offset: 0.89,
                color: 'rgba(126,235,179,0.00)',
              },
            ]),
          },
          emphasis: {
            focus: 'series',
          },
          data:data.trendYData
        }
        ]
      }
      myChart.setOption(option)
      window.addEventListener("resize", () => {
        myChart.resize();
      });
    },
  }
}
</script>

<style scoped lang="less">
@media (min-width: 1600px) {
  .bodySlot {
    height: 100%;
    //padding: 0.5rem 0.65rem 0.4rem; //40px 52px 32px/80
    overflow: hidden;
    padding-top: 0.1rem;

    .disk-info {
      height: 100%;
      display: flex;
      flex-flow: row nowrap;
      flex-direction: column;
      .left {
        width: 100%;
        //padding-right: 0.1rem;
        height: 100%;
        max-height: 100%;
        margin-bottom:0;

        .left-table {
          height: 100%;
          overflow: hidden;
          overflow-y: auto;

          .table-layout {
            display: flex;
            flex-flow: row nowrap;
            justify-content: space-around;
            align-items: center;
            height: 0.5625rem; // 45px;
            line-height: 0.5625rem; //45px;
            color: #fff;
            font-size: 0.2rem;
            padding: 0.125rem 0.375rem; //10px 30px/80px

            div {
              flex: 1;
              text-align: center;
            }
          }

          .table-header {
            background-color: rgba(6, 126, 223, 0.13);
          }

          .table-body-tr {
            border-bottom: 1px dotted rgba(63, 80, 125, 0.42);
          }
        }
      }

      .right {
        padding-left: 0.1rem;
        height: 100%;
        width: 50%;

        .rate {
          margin-left: 0.75rem; // 60px/80;
          font-size: 0.55rem; //44px/80;
          height:0.8125rem ;
          line-height: 0.8125rem; // 65px/80;
          font-weight: 700;
          color: #FFFFFF;
          font-family: DIN-Medium
        }

        .chart {
          height: calc(100% - 0.8125rem)
        }
      }
    }
  }
}
@media (max-width: 1599px) {
  .bodySlot {
    height: 100%;
    padding: 0.5rem 0.65rem 0.4rem; //40px 52px 32px/80
    overflow: hidden;

    .disk-info {
      height: 100%;
      display: flex;
      flex-flow: column nowrap;

      .left {
        width: 100%;
        padding-right: 0;
        //height: 50%;
        height: max-content;
        max-height:50%;
        margin-bottom:0.3rem;

        .left-table {
          height: 100%;
          overflow: hidden;
          overflow-y: auto;

          .table-layout {
            display: flex;
            flex-flow: row nowrap;
            justify-content: space-around;
            align-items: center;
            height: 0.5625rem; // 45px;
            line-height: 0.5625rem; //45px;
            color: #fff;
            font-size: 0.2rem;
            padding: 0.125rem 0.375rem; //10px 30px/80px

            div {
              flex: 1;
              text-align: center;
            }
          }

          .table-header {
            background-color: rgba(6, 126, 223, 0.13);
          }

          .table-body-tr {
            border-bottom: 1px dotted rgba(63, 80, 125, 0.42);
          }
        }
      }

      .right {
        padding-left: 0;
        height: 50%;
        width: 100%;

        .rate {
          margin-left: 0.75rem; // 60px/80;
          font-size: 0.55rem; //44px/80;
          height:0.8125rem ;
          line-height: 0.8125rem; // 65px/80;
          font-weight: 700;
          color: #FFFFFF;
          font-family: DIN-Medium
        }

        .chart {
          height: calc(100% - 0.8125rem)
        }
      }
    }
  }
}
</style>