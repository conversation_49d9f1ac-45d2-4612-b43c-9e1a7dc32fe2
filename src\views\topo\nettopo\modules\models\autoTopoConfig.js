
import { globalGridAttr } from './global'

//应用拓扑图数据结构
const topology = {
    "nodes": [
        {
            "id": "****************************.0",
            "name": "10.100.202.155:61616",
            "type": "ActiveMQ",
            "isReal": false
        },
        {
            "id": "YWdlbnQ6OnJlY29tbWVuZGF0aW9u.1",
            "name": "agent::recommendation",
            "type": "Flask",
            "isReal": true
        },
        {
            "id": "YWdlbnQ6OnNvbmdz.1",
            "name": "agent::songs",
            "type": "Undertow",
            "isReal": true
        },
        {
            "id": "YWdlbnQ6OmdhdGV3YXk=.1",
            "name": "agent::gateway",
            "type": "spring-webflux",
            "isReal": true
        },
        {
            "id": "bG9jYWxob3N0Oi0x.0",
            "name": "localhost:-1",
            "type": "H2",
            "isReal": false
        }
    ],
    "calls": [
        {
            "id": "YWdlbnQ6OmdhdGV3YXk=.1-YWdlbnQ6OnNvbmdz.1",
            "source": "YWdlbnQ6OmdhdGV3YXk=.1",
            "detectPoints": [
                "CLIENT",
                "SERVER"
            ],
            "target": "YWdlbnQ6OnNvbmdz.1"
        },
        {
            "id": "YWdlbnQ6OnJlY29tbWVuZGF0aW9u.1-YWdlbnQ6OnNvbmdz.1",
            "source": "YWdlbnQ6OnJlY29tbWVuZGF0aW9u.1",
            "detectPoints": [
                "CLIENT",
                "SERVER"
            ],
            "target": "YWdlbnQ6OnNvbmdz.1"
        },
        {
            "id": "YWdlbnQ6OnNvbmdz.1-bG9jYWxob3N0Oi0x.0",
            "source": "YWdlbnQ6OnNvbmdz.1",
            "detectPoints": [
                "CLIENT"
            ],
            "target": "bG9jYWxob3N0Oi0x.0"
        },
        {
            "id": "YWdlbnQ6OnNvbmdz.1-****************************.0",
            "source": "YWdlbnQ6OnNvbmdz.1",
            "detectPoints": [
                "CLIENT"
            ],
            "target": "****************************.0"
        },
        {
            "id": "****************************.0-YWdlbnQ6OnNvbmdz.1",
            "source": "****************************.0",
            "detectPoints": [
                "SERVER"
            ],
            "target": "YWdlbnQ6OnNvbmdz.1"
        }
    ]
}
// 测试用
export const deviceList = {
    nodes: ["00:0c:29:d4:98:36", "00:0c:29:4e:eb:b1", "testSwitch2", "IPtest", "gw-device-001"],
    edges: [
        ["00:0c:29:4e:eb:b1", "testSwitch2"],
        ["00:0c:29:4e:eb:b1", "IPtest"],
        ["IPtest", "gw-device-001"],
    ],
}
// 测试用关系信息
export const relatives = [
    // {
    //     "children": null,
    //     "ip": "************",
    //     "icon": "default/server.png",
    //     "id": "00:0c:29:d4:98:36",
    //     "code": "00:0c:29:d4:98:36",
    // },
    {
        "children": [
            {
                "children": [
                    {
                        "children": null,
                        "ip": "************",
                        "icon": "ids_1645874516672.png",
                        "id": "*************",
                        "code": "*************",
                    }
                ],
                "ip": "**************",
                "icon": "ids_1645874516672.png",
                "id": "00:0c:29:d4:98:36",
                "code": "00:0c:29:d4:98:36",
            },
            {
                "children": [
                    // {
                    //     "children": null,
                    //     "ip": "************",
                    //     "icon": "ids_1645874516672.png",
                    //     "id": "1637728833223204865",
                    //     "code": "",
                    // }
                ],
                "ip": "**************",
                "icon": "",
                "id": "",
                "code": "",
                "virtual": 1,
            },
            {
                "children": [
                    {
                        "children": [
                            {
                                "children": null,
                                "ip": "*************",
                                "icon": "ids_1645874516672.png",
                                "id": "gw-device-001",
                                "code": "gw-device-001",
                            }
                        ],
                        "ip": "**************",
                        "icon": "ids_1645874516672.png",
                        "id": "IPtest",
                        "code": "IPtest",
                    }
                ],
                "ip": "**************",
                "icon": "ids_1645874516672.png",
                "id": "testSwitch2",
                "code": "testSwitch2",
            }
        ],
        "ip": "**************",
        "icon": "ids_1645874516672.png",
        "id": "00:0c:29:4e:eb:b1",
        "code": "00:0c:29:4e:eb:b1"
    }
]
//网络自动拓扑节点配置
export const nodeConfig = function (shape, id, code, virtual = false, el) {
    // let icon = deviceInfo.icons ? deviceInfo.icons[0] : ""
    let namestr = ""
    let ip = ""
    if (virtual) {
        namestr = el.ip || el.mac || ""
        ip = el.ip || ""
    }
    return {
        id: id,
        shape: shape,
        zIndex: 5,
        attrs: {
            label: {
                text: namestr,
                fontSize: 13,
                fill: globalGridAttr.topoConfig.labelColor,
            },
            image: {
                'xlink:href': "",//window._CONFIG['staticDomainURL'] + '/' + icon
            },
        },
        data: {
            deviceId: "",//deviceInfo.id
            deviceCode: code,//deviceInfo.code
            deviceName: namestr,//deviceInfo.name
            deviceType: "",
            devicePanel: "",
            productId: "",
            productName: "",
            nodeType: "device",
            isVirtual: virtual,
        },

    }
}
//网络自动拓扑连线配置
export const edgeConfig = function (source, target) {
    return {
        attrs: {
            line: {
                stroke: 'green',
                strokeWidth: 2,
                strokeDasharray: globalGridAttr.topoConfig.edgeDash,
                sourceMarker: {
                    name: [1, 3].includes(globalGridAttr.topoConfig.edgeMarker) ? "classic" : "",
                    size: 8
                },
                targetMarker: {
                    name: [2, 3].includes(globalGridAttr.topoConfig.edgeMarker) ? "classic" : "",
                    size: 8
                },
            }
        },
        connector: { name: globalGridAttr.topoConfig.edgeType === 2 ? "smooth" : "normal" },
        router: {
            name: globalGridAttr.topoConfig.edgeType === 1 ? "manhattan" : "normal"
        },
        source: {
            "cell": source.id,
        },
        target: {
            "cell": target.id,
        },
        data: {
            fromDevice: "",
            fromDeviceName: "",
            toDevice: "",
            toDeviceName: "",
            fromPort: '',
            toPort: '',
            edgeType: "connecting",
        }
    }
}

//应用自动拓扑图节点配置
export const appNodeConfig = function (shape, id,namestr,rid) {
    return {
        id: id,
        shape: shape,
        zIndex: 5,
        attrs: {
            label: {
                text: namestr,
                fontSize: 13,
                fill: globalGridAttr.topoConfig.labelColor,
            },
            image: {
                'xlink:href': "",
            },
        },
        data: {
            deviceId: "",
            deviceCode: "",
            deviceName: namestr,
            deviceType: "",
            devicePanel: "",
            productId: "",
            productName: "",
            nodeType: "app_auto_node",
            isVirtual: false,
            relativesId:rid
        },
    }
}
//获取单位节点配置
export const getDeptNodeData = function(data) {
  const shapes = globalGridAttr.shapeNames
  return {
    shape: shapes[data.data.treeLevel - 1],
    visible: true,
    id: `${data.data.topoNodeId}`,
    x: data.x + 0,
    y: data.y + 0,
    data: {
      deviceId: '',
      deviceCode: '',
      deviceName: '',
      deviceIp: '',
      deviceType: '',
      devicePanel: '',
      productId: '',//节点设备的产品id 用来获取面板信息
      productName: '',
      nodeType: 'dept',
      isVirtual: false,
      nodeScore: 0,
      deptId: data.data.id
    },
    attrs: {
      body: {
        fill: 'rgba(95,149,255,0.5)'
      },
      label: {
        text: data.data.code?data.data.code:data.data.departName,
        fontSize: 13,
        fill: globalGridAttr.topoConfig.labelColor
      },
      image: {
        // 'xlink:href': data.icon.includes('http')? data.icon : this.picSrc + '/' + data.icon,
      }
    }
  }
}
// 应用自动拓扑连线配置
export const appEdgeConfig = function (source, target){
    return {
        attrs: {
            line: {
                stroke: 'green',
                strokeWidth: 2,
                strokeDasharray: globalGridAttr.topoConfig.edgeDash,
                sourceMarker: {
                    name: [1, 3].includes(globalGridAttr.topoConfig.edgeMarker) ? "classic" : "",
                    size: 8
                },
                targetMarker: {
                    name: [2, 3].includes(globalGridAttr.topoConfig.edgeMarker) ? "classic" : "",
                    size: 8
                },
            }
        },
        connector: { name: globalGridAttr.topoConfig.edgeType === 2 ? "smooth" : "normal" },
        router: {
            name: globalGridAttr.topoConfig.edgeType === 1 ? "manhattan" : "normal"
        },
        source: {
            "cell": source.id,
        },
        target: {
            "cell": target.id,
        },
        data: {
            fromDevice: "",
            fromDeviceName: "",
            toDevice: "",
            toDeviceName: "",
            fromPort: '',
            toPort: '',
            edgeType: "app_auto_edge",
        }
    }
}

export const CreateLargeNode = function (graph) {
    const count = 500
    const async = false;
    const node = graph.createNode({
        shape: 'switch-node',
        attrs: {
            image: {
              'xlink:href': "http://192.168.16.249/insight-api/sys/common/static/default/server.png",
            },
        },
        data: {
            deviceId: "",
            deviceCode: "",
            deviceName: "测试",
            deviceType: "",
            devicePanel: "",
            productId: "",
            productName: "",
            nodeType: "device",
            isVirtual: false,
        },
    });
    const edge = graph.createEdge({ shape: "performance_edge" });
    const cells = [];

    Array.from({ length: 500 / 2 }).forEach((_, n) => {
        const a = node
            .clone()
            .position(n * 60 + 20, 20)
        // .attr("label/text", n + 1);
        const b = node
            .clone()
            .position(n * 60 + 20, 150)
        // .attr("label/text", n + 1 + count / 2);
        const ab = edge.clone().setSource(a).setTarget(b);
        cells.push(a, b, ab);
    });

    const startTime = new Date().getTime();
    const showResult = () => {
        const duration = (new Date().getTime() - startTime) / 1000;
        console.log("使用的时间", duration)
        //   const elapsed = document.getElementById("elapsed")!;

        //   elapsed.innerText = `render ${count} nodes and ${
        //     count / 2
        //   } edges in ${duration}s`;
    };

    graph.resize((count / 2) * 110);
    graph.setAsync(async);
    graph.resetCells(cells);
    if (async) {
        graph.on("render:done", showResult);
    } else {
        showResult();
    }
}