<template>
<div>
  <a-table ref="table" bordered :rowKey="(record,index)=>{return record.id}" :columns="columns"
           :dataSource="dataSource" :pagination="ipagination"
           :loading="loading" @change="handleTableChange">
    <!-- 字符串超长截取省略号显示-->
    <template slot="index" slot-scope="text,record,index">
      <span>{{index+1}}</span>
    </template>
    <span slot="templateContent" slot-scope="text">
            <j-ellipsis :value="text" :length="25" />
          </span>
    <template slot="tooltip" slot-scope="text">
      <a-tooltip placement="topLeft" :title="text" trigger="hover">
        <div class='tooltip'>
          {{ text }}
        </div>
      </a-tooltip>
    </template>
  </a-table>
</div>
</template>
<script>
import JkdjDeviceAlarm from '@views/jkdjgl/model/JkdjDeviceAlarm.vue'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'

export default {
  name: 'ZabbixMonitors',
  components: { JkdjDeviceAlarm },
  mixins: [JeecgListMixin, YqFormSearchLocation],
  props:{
    record:{
      type:Object,
      default:()=>{return {}},
      required:true,
    }
  },
  data(){
    return{
      columns: [
        {
          title: '序号',
          dataIndex: 'index',
          scopedSlots: {
            customRender: 'index'
          },
          width: 80,
          customCell: () => {
            let cellStyle = 'text-align: center'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '名称',
          dataIndex: 'name',
          ellipsis: true,
          scopedSlots: {
            customRender: 'name'
          },
          customCell: () => {
            let cellStyle = 'text-align: center'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '标识',
          dataIndex: 'key_',
          scopedSlots: {
            customRender: 'key_'
          },
        },
        {
          title: '值',
          dataIndex: 'lastvalue',
          ellipsis: true,
          scopedSlots: {
            customRender: 'lastvalue'
          },
        }, {
          title: '单位',
          dataIndex: 'units',
          scopedSlots: {
            customRender: 'units'
          },
        },
        {
          title: '更新时间',
          dataIndex: 'lastTime',
          scopedSlots: {
            customRender: 'lastTime'
          },
        },
        // {
        //   title: '操作',
        //   align: 'center',
        //   width: 180,
        //   fixed: 'right',
        //   dataIndex: 'action',
        //   scopedSlots: {
        //     customRender: 'action'
        //   },
        // },
      ],
      url: {
        list: '/device/deviceInfo/getZabbixItemByHostId',
      },
      disableMixinCreated:true,
      ipagination: {
        current: 1,
        pageSize: 15,
        pageSizeOptions: ['10', '20', '50'],
        showTotal: (total, range) => {
          return ' 共' + total + '条'
        },
        showQuickJumper: true,
        showSizeChanger: false,
        total: 0
      },
    }
  },
  created() {
    this.queryParam.hostId = this.record.hostid;
    this.loadData()
  },
  mounted() {
  },
  methods:{
    handleTableChange(pagination, filters, sorter) {
      //分页、排序、筛选变化时触发
      //TODO 筛选
      if (Object.keys(sorter).length > 0) {
        this.isorter.column = sorter.field
        this.isorter.order = 'ascend' === sorter.order ? 'asc' : 'desc'
      }
      this.ipagination = pagination
    },
  }
}
</script>



<style scoped lang='less'>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';

/deep/.ant-table-tbody .ant-table-row td {
  padding-top: 5px;
  padding-bottom: 5px;
}
</style>