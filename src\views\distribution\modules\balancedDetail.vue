<template>
  <a-card style="height: 100%">
    <a-row>
      <a-col :span="24">
        <span style="margin-left: 10px;font-size: 18px;">详情</span>
        <span style="float: right; margin-bottom: 12px">
          <img src="~@/assets/return1.png" alt @click="getGo" style="width: 20px; height: 20px; cursor: pointer" />
        </span>
      </a-col>
      <a-col :span="24">
        <table class="gridtable">
          <tr>
            <td class="leftTd">远程站点</td>
            <td class="rightTd">{{ data.name }}</td>
            <td class="leftTd">链路状态</td>
            <td class="rightTd">{{ data.status }}</td>
          </tr>
          <tr>
            <td class="leftTd">探测次数</td>
            <td class="rightTd">{{ data.allCounts }}</td>
            <td class="leftTd">失败次数</td>
            <td class="rightTd">{{ data.fallCounts }}</td>
          </tr>
          <tr>
            <td class="leftTd">探测协议</td>
            <td class="rightTd">{{ data.checktype }}</td>
          </tr>
        </table>
      </a-col>
    </a-row>
  </a-card>
</template>

<script>
  import {
    getAction
  } from '@/api/manage'
  export default {
    name: 'nodeMonitorDetail',
    props: {
      data: {
        type: Object,
      },
    },
    data() {
      return {
        form: this.$form.createForm(this),
        model: {},
        dataSource: [],
        columns: [{
          title: ' ',
          dataIndex: 'disk',
        }, {
          title: '磁盘使用量',
          dataIndex: 'age1',
        }, {
          title: '空闲inode数',
          dataIndex: 'age2',
        }, {
          title: '磁盘延迟(ms)',
          dataIndex: 'age3',
        }, {
          title: '磁盘错误数',
          dataIndex: 'age4',
        }, {
          title: '磁盘超时错误数',
          dataIndex: 'age5',
        }, {
          title: 'IO操作等待数',
          dataIndex: 'age6',
        }, ],
      }
    },
    mounted() {
      this.getData()
    },
    methods: {
      getData() {

      },
      //返回上一级
      getGo() {
        this.$parent.pButton2(0)
      },
    },
  }
</script>
<style lang="less" scoped>
  ::v-deep .two-words>div>label {
    letter-spacing: 4px;
  }

  ::v-deep .two-words>div>label::after {
    letter-spacing: 0px;
  }

  table.gridtable {
    font-family: verdana, arial, sans-serif;
    font-size: 14px;
    color: #606266;
    border-width: 1px;
    border-color: #e8e8e8;
    border-collapse: collapse;
    text-align: left;
    width: 100%;
  }

  table.gridtable td {
    border-width: 1px;
    border-style: solid;
    border-color: #e8e8e8;
  }

  .leftTd {
    width: 17%;
    background-color: #fafafa;
    padding: 16px 24px;
    text-align: center;
  }

  .rightTd {
    width: 35%;
    padding: 16px 24px;
  }
</style>