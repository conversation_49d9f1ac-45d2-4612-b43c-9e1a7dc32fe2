<template>
  <a-dropdown :overlayClassName="colorTheme" placement="bottomCenter">
    <span :class="[colorTheme?colorTheme:'', 'gateway-menus-title']">
      <a-icon v-if="showIcon" type="bars" :style="icon1Style" />
      <span v-if="menusName" :style="textStyle">{{ menusName }}</span>
      <a-icon
        v-if="menusName && menusRights.length > 0"
        type="down"
        class="gateway-menu-icon-down"
        :style="icon2Style"
      ></a-icon>
    </span>
    <a-menu
      v-if="menusRights.length>0"
      slot="overlay"
      :selectedKeys="selectedKeys"
      @click="menuClick"
    >
      <a-menu-item v-for="item in menusRights" :key="item.key" class="gateway-menu-item">
        <div>
          <a-icon :type="item.icon" class="gateway-menus-item-icon" />
          <span>{{item.title}}</span>
        </div>
      </a-menu-item>
    </a-menu>
  </a-dropdown>
</template>
<script>
import { getAction, postAction } from '@/api/manage'
import Vue from 'vue'
import { mapActions, mapGetters, mapState } from 'vuex'
import { generateIndexRouter, generateBigscreenRouter,getQueryStringRegExp } from '@/utils/util'
import { ACCESS_TOKEN, PLATFORM_TYPE, PTM_CHANGE, USER_INFO } from '@/store/mutation-types'
import router from '@/router'
import store from '@/store'
export default {
  props: {
    icon1Style: {
      type: Object,
      default: () => {
        return { fontSize: '16px' }
      }
    },
    icon2Style: {
      type: Object,
      default: () => {
        return { fontSize: '8px' }
      }
    },
    showIcon: {
      type: Boolean,
      default: false
    },
    menusName: {
      type: String,
      default: ''
    },
    textStyle: {
      type: Object,
      default: () => {
        return {}
      }
    },
    colorTheme: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      visible: false,
      dataCenterType: window.config.DataCenterType || 0,
      selectedKeys: [],
      url: {
        checkHasMenu: '/sys/permission/checkHasMenu' //菜单权限
      },
      menuIcons:{
        "1":'desktop',
        "3":'crown',
        "6":'transaction',
        "2":'tool',
        "4":'radar-chart',
        "8":'radar-chart',
        "5":'setting',
        "13":'code',
        "14":'fork',
      },
      menus: [],
      menusRights: []
    }
  },
  watch:{
    "$store.state.app.ptmChange"(e){
      if(e){
        this.selectedKeys = [sessionStorage.getItem(PLATFORM_TYPE) + '']
        this.$store.commit(PTM_CHANGE,false)
        this.$emit('resetMenus')
        this.$emit('platformChange','platform_type')
      }
    }
  },
  created() {
    this.selectedKeys = [sessionStorage.getItem(PLATFORM_TYPE) + '']
    this.getPlatformTypeList();
  },
  mounted() {},
  methods: {
    ...mapActions(['Logout', 'GetPermissionList']),
    //获取子系统类型
    getPlatformTypeList(){
      getAction("sys/permission/platformTypeList").then(res=>{
        if(res.success && res.result){
          this.menus = res.result
            .filter(el=>{
              if(window.config.subSystems && window.config.subSystems.length > 0) {
                return window.config.subSystems.includes(el.value + "")
              }else{
                return el.value <= 8 && el.value != 7
              }
            })
            .map(el=>{return {key:el.value,title:el.text,icon:this.menuIcons[el.value]||"project"}})
          this.MenuJudgment()
        }
      })
    },
    // 判断菜单权限
    MenuJudgment() {
      getAction('/sys/permission/getUserPlatformTypeByToken').then(res => {
        // console.log("用户的菜单权限 === ",res)
        if (res.success) {
          let tem = res.result.split(',')
          this.menusRights = this.menus.filter(el => {
            if (window.config.DataCenterType === 0 && el.key === '8') {
              return false
            }
            if (window.config.DataCenterType === 1 && el.key === '4') {
              return false
            }
            return tem.includes(el.key)
          })
          // console.log("用户的菜单权限 === ",this.menusRights)
        }
      })
    },
    menuClick({ item, key, keyPath }) {
      //判断是否配置了新标签地址
      if(window.config.subSystemUrls && window.config.subSystemUrls[key]){
        this.openMenuUrl(window.config.subSystemUrls[key])
        return
      }
      this.entrancePlanning(key)
    },
    entrancePlanning(index) {
      //内蒙定制 服务中心跳转到配置的url
      if (sessionStorage.getItem(PLATFORM_TYPE) == index) {
        return
      }
      const that = this
      that.GetPermissionList(index).then(res => {
        console.log("获取到的菜单 === ",res)
        if (res === '1') {
          this.$message.warning('没有添加菜单！')
          return
        }
        const menuData = res.result.menu
        //如果只有一个菜单 直接跳转url跳转 认为是打开第三方连接
        if(menuData && menuData.length === 1){
          let firsMenu = menuData[0]
          const meta = firsMenu.children && firsMenu.children.length > 0 ? firsMenu.children[0].meta : firsMenu.meta
          if(meta && meta.url){
            this.openMenuUrl(meta.url,meta.internalOrExternal)
            return
          }
        }
        var redirect = ''
        if (menuData && menuData.length > 0) {
          let firsMenu = menuData[0]
          redirect = firsMenu.children && firsMenu.children.length > 0 ? firsMenu.children[0].path : firsMenu.path
        } else {
          return
        }

        let constRoutes = []
        if ([4, 8].includes(Number(index))) {
          constRoutes = generateBigscreenRouter(menuData)
        } else {
          constRoutes = generateIndexRouter(menuData)
        }
        sessionStorage.setItem(PLATFORM_TYPE, index)
        this.selectedKeys = [index]
        // 添加主界面路由
        store
          .dispatch('UpdateAppRouter', {
            constRoutes
          })
          .then(() => {
            // 根据roles权限生成可访问的路由表
            // 动态添加可访问路由表
            router.addRoutes(store.getters.addRouters)
            this.$emit('resetMenus')
            this.$router.push({
              path: redirect
            })
            // }
          })
      })
    },
    async openMenuUrl(toUrl,internalOrExternal){
      let userinfo = Vue.ls.get(USER_INFO)
      let password = ''
      let userStr = '${username}'
      if (toUrl.indexOf(userStr) != -1) {
        toUrl = toUrl.replace(userStr, userinfo.username)
      }
      let passwordStr = '${password}'
      if (toUrl.indexOf(passwordStr) != -1) {
        await getAction("/sys/user/getUserInfoByUsername",{username:userinfo.username}).then(res=>{
          console.log("获取密码成功",res)
          if(res.success && res.result){
            password = res.result.originalPassword || ""
          }

        }).catch(err=>{
          console.log("获取pw失败",err)
        })
        console.log("pw是",password)
        toUrl = toUrl.replace(passwordStr, password)
        if(this.password === ""){
          this.$message.warning('没有添加密码！')
          return
        }
      }
      let target = '_blank'
      if (internalOrExternal !== undefined){
        target = internalOrExternal ?'_blank':'_self'
      }else{
        target=getQueryStringRegExp('target',toUrl) ==='self'?'_self':'_blank'
      }
      window.open(toUrl, target)
    }
  }
}
</script>
<style lang="less" scoped>
.gateway-menus-item-icon {
  margin-right: 12px;
}
.gateway-menus-title {
  cursor: pointer;
  &:hover {
    color: #409eff;
  }
}
.gateway-menu-item {
  font-size: 14px;
  padding: 6px 10px;
  margin-bottom: 4px;
  cursor: pointer;
}
.gateway-menu-icon-down {
  position: relative;
  top: -2px;
  margin: 0 5px 0 10px;
}
.ant-dropdown-menu {
  width: 140px;
  text-align: center;
  padding: 12px 10px 10px 10px;
}
.ant-dropdown-menu-item-active {
  background: transparent !important;
}
.ant-dropdown-menu-item-selected {
  background: #ecf5ff !important;
  color: #409eff !important;
}

.gateway-menu-inner-content {
  width: 120px;
}
</style>
<style lang="less" scoped>
@darkbg: #1e1e24;
@darkText: #45c5e0;
/**深色背景**/
.darkTheme.gateway-menus-title {
  color: @darkText;
  &:hover {
    color: @darkText;
  }
}
.darkTheme {
  .ant-dropdown-menu {
    background-color: @darkbg;
    padding: 8px 15px 8px 8px;
  }
  .gateway-menu-item {
    color: #fff;
  }
  .ant-dropdown-menu-item-selected {
    color: @darkText !important;
    background: transparent !important;
  }
}
</style>