<template>
  <a-row style="background: #f0f2f5; height: 100%; display: flex;">
    <a-col :span="5" class="listTree" id="listTree" style="">
      <devops-ipManage-real-list-tree
        ref="DevopsIpManageRealListTree"
        :getTree="getTree"
      ></devops-ipManage-real-list-tree>
    </a-col>
    <a-col :span="19" class="listCont">
      <devops-ipManage-real-list-cont
        ref="DevopsIpManageRealListCont"
        :treeFlag="treeFlag"
      ></devops-ipManage-real-list-cont>
    </a-col>
  </a-row>
</template>
<script>
import DevopsIpManageRealListCont from './DevopsIpManageRealListCont'
import DevopsIpManageRealListTree from './DevopsIpManageRealListTree'
export default {
  name: 'DevopsIpManageRealList',
  components: {
    DevopsIpManageRealListCont,
    DevopsIpManageRealListTree,
  },
  data() {
    return {
      treeFlag: '',
      //  this.$refs.DevopsIpManageRealListTree.ipCont
    }
  },
  computed: {},
  mounted() {
    /*let listTree = document.getElementById('listTree');
      let height = document.body.clientHeight - 59 - 101;
      listTree.style.height = height+'px';*/
  },
  methods: {
    getTree(val) {
      this.treeFlag = val
    },
  },
}
</script>
<style scoped>
.listTree {
  width: 15%;
  height: 100%;
}
.listCont {
  width: calc(100% - 15% - 16px);
  margin-left: 16px;
  height: 100%;
}
</style>