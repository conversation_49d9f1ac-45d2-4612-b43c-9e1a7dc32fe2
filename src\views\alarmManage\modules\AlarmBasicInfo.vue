<template>
  <div class='content-box'>
    <div class="title-box">
      <span class="title">告警信息</span>
    </div>
    <a-descriptions :column='{ xxl: 2, xl: 2, lg: 2, md: 2, sm: 2, xs: 2 }' bordered>
      <a-descriptions-item  label='设备名称'>{{record.deviceName}}</a-descriptions-item>
      <a-descriptions-item  label='产品名称'>{{record.productName}}</a-descriptions-item>
      <a-descriptions-item  label='告警名称'>{{record.templateName}}</a-descriptions-item>
      <a-descriptions-item  label='告警级别'>{{record.alarmLevel_dictText}}</a-descriptions-item>
      <a-descriptions-item  label='告警状态'>{{record.alarmStatus == 0 ? '当前告警': '历史告警'}}</a-descriptions-item>
      <a-descriptions-item  label='告警触发时间'>{{record.alarmTime1}}</a-descriptions-item>
      <a-descriptions-item  label='重复次数'>{{record.repeatTimes}}</a-descriptions-item>
      <a-descriptions-item  label='最近告警时间'>{{record.alarmTime2}}</a-descriptions-item>
      <a-descriptions-item  label='处理状态' v-if='flowView'>{{getHandlingStatus(record.flowHandleStatus)}}</a-descriptions-item>
      <a-descriptions-item  label='处理状态' v-else>{{getHandlingStatus(record.handleStatus)}}</a-descriptions-item>
      <a-descriptions-item  label='责任人'>{{record.responsibleUser_dictText}}</a-descriptions-item>
      <a-descriptions-item  label='告警规则'>{{record.alarmRule}}</a-descriptions-item>
      <a-descriptions-item  label='告警触发值'>{{record.alarmValue}}</a-descriptions-item>
    </a-descriptions>
  </div>
</template>
<script>
import {dataAndFunction} from '@views/alarmManage/modules/dataAndFunction'
export default {
  name: 'DeviceAlarmInfo',
  mixins: [dataAndFunction],
  props: {
    alarmInfo: {
      type: Object
    },
    flowView: {
      type: Boolean,
      required:false,
      default:false
    }
  },
  data() {
    return {
      size: 'middle',
      record: {},
    }
  },
  watch: {
    alarmInfo: function (val, oldVal) {
      this.record = Object.assign({}, val);
    }
  },
  mounted() {
    this.record = Object.assign({}, this.alarmInfo);
  },
  methods: {}
}
</script>
<style lang="less" scoped>
.content-box{
  margin-bottom:20px;
  .title-box {
    font-size: 14px;
    margin-bottom: 10px;

    .title {
      padding-left: 7px;
      border-left: 4px solid #1e3674;
    }
  }
}

::v-deep .ant-descriptions-view {
  border-radius: 0px;
}

::v-deep .ant-descriptions-bordered .ant-descriptions-item-label {
  background-color: rgb(250, 250, 250);
  text-align: center;
  width: 17%;
}

::v-deep .ant-descriptions-item-label,
.ant-descriptions-item-content {
  color: rgb(96, 98, 102) !important;
}

::v-deep .ant-descriptions-bordered .ant-descriptions-item-content {
  word-break: break-word;
  white-space: normal;
}
</style>