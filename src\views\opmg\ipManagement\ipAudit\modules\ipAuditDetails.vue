<template>
  <a-card style="height: 100%;overflow: auto">
    <a-row>
      <a-col :span="24">
        <span>
        </span>
        <span style="float: right;margin-bottom: 12px;"><img src="~@/assets/return1.png" alt="" @click="getGo"
            style="width: 20px;height: 20px;cursor: pointer"></span>
      </a-col>
      <a-col :span="24">
        <table class="gridtable">
          <tr>
            <td class="leftTd">审计任务名称</td>
            <td class="rightTd">{{ record.auditName }}</td>
            <td class="leftTd">审计类型</td>
            <td class="rightTd">{{ record.auditTypeText }}</td>
          </tr>
          <tr>
            <td class="leftTd">审计对象</td>
            <td class="rightTd">{{record.auditObjectText}}</td>
            <td class="leftTd">cron表达式</td>
            <td class="rightTd">{{ record.cron }}</td>
          </tr>
          <tr>
            <td class="leftTd">是否告警</td>
            <td class="rightTd">{{record.alarm == 1 ? '是' : '否'}}</td>
            <td class="leftTd">任务状态</td>
            <td class="rightTd">{{ record.enabled == 1 ? '启用' : '禁用'}}</td>
          </tr>
          <tr>
            <td class="leftTd">审计策略</td>
            <td class="rightTd">{{record.auditStrategyText}}</td>
            <td class="leftTd">备注</td>
            <td class="rightTd">{{record.remark}}</td>
          </tr>
        </table>
      </a-col>
    </a-row>
  </a-card>
</template>

<script>
  import {
    httpAction,
    getAction
  } from '@/api/manage'
  import pick from 'lodash.pick'
  export default {
    name: 'ipAuditDetails',
    props: {
      data: {
        type: Object
      }
    },
    data() {
      return {
        form: this.$form.createForm(this),
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          },
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          },
        },
        confirmLoading: false,
        url: {
          queryById: "/devops/ip/auditTask/queryById"
        },
        iconShow: "0",
        record: {}
      }
    },
    mounted() {
      this.getRecord(this.data.id)
    },
    methods: {
      getRecord(id) {
        getAction(this.url.queryById, {
          id: id
        }).then((res) => {
          this.record = res.result
        })
      },
      //返回上一级
      getGo() {
        this.$parent.pButton2(0);
      }
    }
  }
</script>
<style scoped>
  table.gridtable {
    font-family: verdana, arial, sans-serif;
    font-size: 14px;
    color: #606266;
    border-width: 1px;
    border-color: #e8e8e8;
    border-collapse: collapse;
    text-align: left;
    width: 100%;
  }

  table.gridtable td {
    border-width: 1px;
    border-style: solid;
    border-color: #e8e8e8;
  }

  .leftTd {
    width: 17%;
    background-color: #FAFAFA;
    padding: 16px 24px;
    text-align: center;
  }

  .rightTd {
    width: 35%;
    padding: 16px 24px;
  }
</style>