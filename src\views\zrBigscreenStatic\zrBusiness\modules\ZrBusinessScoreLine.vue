<template>
  <div id='zrBusinessScoreLine' class='score-line'></div>
</template>
<script>
import { businessStatus } from '@views/zrBigscreens/modules/zrUtil'
export default {
  name: 'ZrBusinessScoreLine',
  props: {
  },
  data() {
    return {
      myChart:null,
      rangeColors:[],
      scoreLineTimer:null,
      lineData:[],
    }
  },
  created() {
    this.rangeColors = businessStatus.map(item => item.color)
  },
  mounted() {
    this.initScoreLine()
    this.scoreLineTimer = setInterval(()=>{
      this.resetLineData()
    },60*1000)
  },
  beforeDestroy(){
    clearInterval(this.scoreLineTimer)
    this.scoreLineTimer = null
    this.myChart.dispose()
    this.myChart = null;
  },
  methods:{
    resetLineData(){
      this.lineData.shift()
      const timeStr = this.getTimeStr(0)
      const value = 30+Math.floor(Math.random() * 70) + 1;
      this.lineData.push([timeStr, value]);
      const dateList = this.lineData.map(function (item) {
        return item[0];
      });
      const valueList =  this.lineData.map(function (item) {
        return item[1];
      });
      this.myChart.setOption({
        xAxis:[{
          data: dateList,
        }],
        series: [{
          data: valueList
        }]
      })
    },
    getTimeStr(i){
      const now = new Date();
      const time = new Date(now.getTime() - i * 60000);
      const hours = time.getHours().toString().padStart(2, '0');
      const minutes = time.getMinutes().toString().padStart(2, '0');
      return `${hours}:${minutes}`;
    },
    getLastMinutesData() {
      const data = [];
      for (let i = 59; i >= 0; i--) {
        const timeStr = this.getTimeStr(i)
        const value = 85+Math.floor(Math.random() * 15) + 1;
        data.push([timeStr, value]);
      }
      return data;
    },
    initScoreLine(){
      const chartDom = document.getElementById('zrBusinessScoreLine');
      if(!chartDom)return;
      this.myChart = this.$echarts.init(chartDom);
      this.lineData = this.getLastMinutesData();
      const dateList = this.lineData.map(function (item) {
        return item[0];
      });
      const valueList =  this.lineData.map(function (item) {
        return item[1];
      });
      let option = {
        title: [],
        tooltip: {
          trigger: 'axis'
        },
        visualMap: [
          {
            show: false,
            type: 'continuous',
            seriesIndex: 0,
            min: 0,
            max: 100,
            inRange: {
              color: this.rangeColors.reverse()
            }
          },
        ],
        xAxis: [
          {
            show: false,
            data: dateList,
          }
        ],
        yAxis: [
          {
            show: false,
          }
        ],
        grid: [
          {
            top: 8,
            bottom:0,
          }
        ],
        series: [
          {
            type: 'line',
            showSymbol: false,
            data: valueList,
          }
        ]
      };
      option && this.myChart.setOption(option);
    }
  }
}
</script>

<style scoped lang='less'>
.score-line{
  width: 100%;
  height: 100%;
}
</style>