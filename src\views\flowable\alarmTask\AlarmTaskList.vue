<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <!-- 查询区域 -->
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class="card-style">
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="设备名称">
                  <a-input placeholder="请输入设备名称" v-model="queryParam.deviceName" :maxLength='maxLength' autocomplete="off" :allowClear="true">
                  </a-input>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="产品名称">
                  <a-select placeholder="请选择产品名称" v-model="queryParam.productId"
                            :getPopupContainer="(node) => node.parentNode" :allowClear="true" :show-search='true'
                            option-filter-prop='label'>
                    <a-select-option v-for="item in productList" :label='item.proName' :key="item.proId"
                                     :value="item.proId">{{ item.proName }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="告警级别">
                  <a-select v-model='queryParam.alarmLevel' :allow-clear='true' placeholder='请选择告警级别'>
                    <a-select-option v-for='item in alarmLevelList' :key='item.value' :label='item.title'
                                     :value='item.value'>
                      {{ item.title }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue" v-show="toggleSearchStatus">
                <a-form-item label="告警状态">
                  <j-dict-select-tag v-model="queryParam.alarmStatus" placeholder="请选择告警状态" dictCode="alarm_status" />
                </a-form-item>
              </a-col>

              <a-col :span="spanValue" v-show="toggleSearchStatus">
                <a-form-item label="触发时间">
                  <a-range-picker class="a-range-picker-choice-date" @change="onChangeTime"
                                  v-model="queryParam.alarmTime2Range" format="YYYY-MM-DD" :placeholder="['开始时间', '截止时间']" />
                </a-form-item>
              </a-col>
              <a-col :span="spanValue" v-show="toggleSearchStatus">
                <a-form-item label="处理状态">
                  <a-select v-model='queryParam.flowHandleStatus' :allow-clear='true' placeholder='请选择处理状态'>
                    <a-select-option v-for='item in handleStatusList' :key='item.value' :label='item.label'
                                     :value='item.value'>
                      {{ item.label }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue"  v-show="toggleSearchStatus">
                <a-form-item label="分组名称">
                  <a-select placeholder="请选择分组名称" v-model="queryParam.groupId"
                            :getPopupContainer="(node) => node.parentNode" :allowClear="true" :show-search='true'
                            option-filter-prop='label'>
                    <a-select-option v-for="item in groupList" :label='item.name' :key="item.id"
                                     :value="item.id">{{ item.name }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="colBtnsSpan()">
                <span class="table-page-search-submitButtons"
                      :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button class="btn-search btn-search-style" type="primary" @click="searchQuery">查询</a-button>
                  <a-button class="btn-reset btn-reset-style" @click="searchReset">重置</a-button>
                  <a v-if="isVisible" class="btn-updown-style" @click="doToggleSearch">
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered="false" style="width: 100%; flex: auto">
        <!-- 操作按钮区域 -->
        <div class="table-operator table-operator-style">
          <a-dropdown v-if="selectedRowKeys.length > 0&&(canBatchClaim||canBatchClose||canBatchChangeResponsibleUser)">
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item v-if='canBatchChangeResponsibleUser' key="1" @click="batchChangeResponsibleUser('转移责任人','transferBatch')">
                转移责任人
              </a-menu-item>
              <a-menu-item v-if='canBatchClose' key="2" @click="batchCloseAlarm">
                关闭
              </a-menu-item>
            </a-menu>
            <a-button>批量操作
              <a-icon type="down" />
            </a-button>
          </a-dropdown>
        </div>
        <!-- table区域-begin -->
        <a-table
          ref="table"
          bordered
          :rowKey="(record) => { return record.id}"
          :columns="columns"
          :dataSource="dataSource" :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
          :pagination="ipagination"
          :loading="loading"
          :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChangeKeysAndRows,getCheckboxProps:getCheckboxProps }"
          @change="handleTableChange">
          <template slot="alarmStatus" slot-scope="text,record">
            <span>{{getAlarmStatus(record)}}</span>
          </template>
          <template slot="deviceName" slot-scope="text,record">
            <span style='color: #409eff;cursor: pointer' @click='handleViewDeviceInfo(record)'>{{text}}</span>
          </template>
          <template slot="alarmTemplateName" slot-scope="text,record">
            <span style='color: #409eff;cursor: pointer' @click='handleViewAlarmTemplateInfo(record)'>{{text}}</span>
          </template>
          <template slot="alarmLevel" slot-scope="text,record">
            <div :style='{backgroundColor:getAlarmColor(record)}'
                 style='display:inline-block;color:#ffffff; border-radius: 10px; padding: 2px 10px;'>
              {{ getAlarmTitle(record) }}
            </div>
          </template>
          <template slot="flowHandleStatus" slot-scope="text">
            <span >{{getHandlingStatus(text)}}</span>
          </template>
          <span slot="action" slot-scope="text, record" class="caozuo">
            <a @click="handleDetailPage(record)">查看</a>
            <a-divider type="vertical" />
            <a-dropdown
              :disabled='record.flowHandleStatus!=handleStatusList[0].value&&record.flowHandleStatus!=handleStatusList[1].value'
              :class='{"dropdown-disabled":record.flowHandleStatus!=handleStatusList[0].value&&record.flowHandleStatus!=handleStatusList[1].value}'>
                <a class="ant-dropdown-link">更多 <a-icon type="down"/></a>
                <a-menu slot="overlay" style="text-align: center">
                  <a-menu-item v-if='record.flowHandleStatus==handleStatusList[0].value'>
                    <a href="javascript:void(0);" @click='launchOrder(record)'>处理</a>
                  </a-menu-item>
                  <a-menu-item v-if='record.flowHandleStatus==handleStatusList[0].value'>
                    <a href="javascript:void(0);" @click='changeResponsibleUser(record,"转移责任人","transfer")'>转移责任人</a>
                  </a-menu-item>
                  <!--告警级别小于最大级别时，可升级-->
                  <a-menu-item v-if='setUpgradeStatus(record)&&record.flowHandleStatus==handleStatusList[0].value'>
                    <a href="javascript:void(0);" @click='changeAlarmLevel(record,"upgrade")'>升级</a>
                  </a-menu-item>
                  <!--告警级别大于最小级别时，可升级-->
                   <a-menu-item v-if='setDegradationStatus(record)&&record.flowHandleStatus==handleStatusList[0].value'>
                    <a href="javascript:void(0);" @click='changeAlarmLevel(record,"downgrade")'>降级</a>
                  </a-menu-item>
                  <a-menu-item v-if='record.flowHandleStatus==handleStatusList[0].value'>
                    <a href="javascript:void(0);" @click='closeAlarm(record)'>关闭</a>
                  </a-menu-item>
                   <a-menu-item v-if='record.flowHandleStatus==handleStatusList[1].value'>
                    <a href="javascript:void(0);" @click="history(record,'查看进度')">查看进度</a>
                  </a-menu-item>
                </a-menu>
              </a-dropdown>
          </span>
          <template slot="tooltip" slot-scope="text">
            <a-tooltip placement="topLeft" :title="text" trigger="hover">
              <div class="tooltip">
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
      </a-card>
      <!-- 表单区域 -->
      <!--处理-->
      <Process-instance-start
        v-if='dialogStartInstanceVisible'
        :dialogStartInstanceVisible.sync='dialogStartInstanceVisible'
        :process-definition='processDefinition'
        :associationId='associationId'
        :alarmHistory='alarmHistory'
        :formUrl='formUrl'
        :startUrl='startUrl'
        :showDdraft='false'
        :dict-key='"alarmConfirm"'
        method='post'
        @loadData='resetData'>
      </Process-instance-start>
      <!--转移责任人-->
      <assign-responsible-person ref="modalForm" :user-name='sysUser' @ok='resetData'></assign-responsible-person>
      <!--查看进度-->
      <process-history-modal ref="processHistoryModal" @ok="modalFormOk"></process-history-modal>
    </a-col>
  </a-row>
</template>

<script>
import {JeecgListMixin} from '@/mixins/JeecgListMixin'
import { deleteAction, getAction, putAction } from '@/api/manage'
import {YqFormSearchLocation} from '@/mixins/YqFormSearchLocation'
import ProcessInstanceStart from '../../flowable/process-instance-start/module/ProcessInstanceStart.vue'
import {dataAndFunction} from '@views/alarmManage/modules/dataAndFunction'
import assignResponsiblePerson from '@views/alarmManage/modules/AssignResponsiblePerson.vue'
import processHistoryModal from '@views/flowable/myProcess/modules/ProcessHistoryModal.vue'
export default {
  name: 'AlarmTaskList',
  mixins: [JeecgListMixin, YqFormSearchLocation,dataAndFunction],
  components: {
    ProcessInstanceStart,
    assignResponsiblePerson,
    processHistoryModal
  },
  data() {
    return {
      maxLength:50,
      description: '告警任务页面',
      formItemLayout: {
        labelCol: {
          style: 'width:80px',
        },
        wrapperCol: {
          style: 'width:calc(100% - 80px)'
        }
      },
      queryParam:{
        isResponsible:true,
      },
      // 表头
      columns: [
        {
          title: '设备名称',
          dataIndex: 'deviceName',
          scopedSlots: {
            customRender: 'deviceName'
          },
        },
        {
          title: '告警名称',
          dataIndex: 'templateName',
          scopedSlots: {
            customRender: 'alarmTemplateName'
          },
        },
        {
          title: '产品名称',
          dataIndex: 'productName'
        },
        {
          title: '告警级别',
          dataIndex: 'alarmLevel',
          scopedSlots: {
            customRender: 'alarmLevel'
          }
        },
        {
          title: '告警状态',
          dataIndex: 'alarmStatus',
          scopedSlots: {
            customRender: 'alarmStatus'
          }
        },
        {
          title: '触发时间',
          dataIndex: 'alarmTime1'
        },
        {
          title: '重复次数',
          dataIndex: 'repeatTimes',
          customCell: () => {
            let cellStyle = 'text-align: right'
            return {
              style: cellStyle
            }
          },
        },
        {
          title: '处理状态',
          dataIndex: 'flowHandleStatus',
          scopedSlots: {
            customRender: 'flowHandleStatus'
          }
        },
        {
          title: '责任人',
          dataIndex: 'responsibleUser_dictText',
        },
        {
          title: '分组名称',
          dataIndex: 'groupName',
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 120,
          scopedSlots: {
            customRender: 'action'
          },
        },
      ],
      url: {
        list: '/alarm/alarmHistory/list'
      },
      disableMixinCreated:true,
    }
  },
  created() {
    this.queryAllProducts()
    this.getAlarmLevelData()
    this.getGroupList()
    this.getProcessDefinitionKey()
    this.getDict('alarm_status','alarmStatusList')
  },
  activated() {
    this.queryParam.isResponsible=true
    this.loadData()
  },
  methods: {
    searchReset() {
      this.queryParam = {}
      this.queryParam.isResponsible=true
      this.loadData(1)
    },
    /**未处理可选，处理中，已关闭，处理完成的不可选*/
    getCheckboxProps(record) {
      return ({
        props: {
          disabled: this.getTableChecked(record.flowHandleStatus) && (record.responsibleUser && record.responsibleUser !== this.sysUser),
        }
      })
    },
    /**重置取消列表选择，批量操作*/
    resetData() {
      this.loadData()
      this.allSelectedRows = []
      this.onClearSelected()
      this.canBatchClose = false
      this.canBatchChangeResponsibleUser = false
    },
    /**列表框选事件*/
    onSelectChangeKeysAndRows(selectedRowKeys, selectionRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectionRows = selectionRows

      this.updateAllSelectionRows(selectedRowKeys, selectionRows)
      this.setBatchCloseStatus()
      this.setBatchChangeResponsibleUserStatus()
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
.caozuo .dropdown-disabled {
  color: rgba(0, 0, 0, 0.25) !important;
  cursor: default;
}
.confirm {
  color: rgba(0, 0, 0, 0.25) !important;
}
</style>