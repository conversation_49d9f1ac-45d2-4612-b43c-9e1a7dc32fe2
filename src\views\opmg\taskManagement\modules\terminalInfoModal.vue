<template>
  <j-modal
    :title="title"
    fullscreen
    :visible="visible"
    :confirmLoading="confirmLoading"
    :footer="null"
    @cancel="handleCancel">
    <a-spin :spinning="confirmLoading">
      <terminal-info :data="terminalInfo" :showOperation="false"></terminal-info>
    </a-spin>
  </j-modal>
</template>
<script>
import terminalInfo from '@views/terminalManage/terminalInfo/modules/TerminalInfo.vue'

export default {
  name: "terminalInfoModal",
  components:{
    terminalInfo
  },
  data() {
    return {
      title: "",
      visible: false,
      confirmLoading: false,
      terminalInfo: {},
    }
  },
  methods: {
    edit(record) {
      this.visible = true
      this.terminalInfo=record
    },
    close() {
      this.visible = false;
    },
    handleOk() {
      const that = this;
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true;
          let formData = Object.assign(this.model, values);
          let obj;
          if (!this.model.id) {
            obj = addRole(formData);
          } else {
            obj = editRole(formData);
          }
          obj.then((res) => {
            if (res.success) {
              that.$message.success(res.message);
              that.$emit('ok');
            } else {
              that.$message.warning(res.message);
            }
          }).finally(() => {
            that.confirmLoading = false;
            that.close();
          })
        }
      })
    },
    handleCancel() {
      this.close()
    },
  }
}
</script>

<style scoped lang="less">

</style>