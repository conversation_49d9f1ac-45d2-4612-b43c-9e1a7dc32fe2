<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form
        :form="form"
        slot="detail"
      >
        <a-row>
          <a-col :span="24">
            <a-form-item
              label="表单名称"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input
                v-decorator="['name',validatorRules.name]"
                :allowClear="true"
                autocomplete="off"
                placeholder="请输入表单名称"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item
              label="表单编码"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input
                v-decorator="['code',validatorRules.code]"
                :allowClear="true"
                autocomplete="off"
                placeholder="请输入表单编码"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item
              class="two-words"
              label="描述"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input
                v-decorator="['description',validatorRules.description]"
                :allowClear="true"
                autocomplete="off"
                placeholder="请输入描述"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item
              label="资产类型"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <j-tree-select-expand
                :multiple="true"
                ref="treeSelect"
                placeholder="请选择资产类型"
                v-decorator="['assetsCategoryId', validatorRules.assetsCategoryId]"
                dict="cmdb_assets_category,category_name,id"
                pidField="parent_id"
                condition='{"delflag":0,"category_state":"0"}'
                pidValue="0"
                hasChildField="has_child"
                multiple=true
              >
              </j-tree-select-expand>
            </a-form-item>
          </a-col>
          <a-col
            v-if="showFlowSubmitButton"
            :span="24"
            style="text-align: center"
          >
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import pick from 'lodash.pick'
import { validateDuplicateValue } from '@/utils/util'
import JFormContainer from '@/components/jeecg/JFormContainer'

export default {
  name: 'ExtendFormForm',
  components: {
    JFormContainer,
  },
  props: {
    //流程表单data
    formData: {
      type: Object,
      default: () => {},
      required: false,
    },
    //表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false,
    },
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false,
    },
  },
  data() {
    return {
      form: this.$form.createForm(this),
      assetsCategoryList: [],
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      confirmLoading: false,

      url: {
        add: '/extendForm/extendForm/add',
        edit: '/extendForm/extendForm/edit',
        queryById: '/extendForm/extendForm/queryById',
        CategoryqueryById: '/category/cmdbAssetsCategory1/queryById',
        findAll: '/category/cmdbAssetsCategory1/findAll',
      },
      validatorRules: {
        assetsCategoryId: {
          rules: [{ required: true, message: '请选择资产类型!' }],
        },
        name: {
          rules: [
            { required: true, message: '请输入表单名称!' },
            { min: 2, max: 20, message: '表单名称在2-20个字符之间' },
          ],
        },
        code: {
          rules: [
            { required: true, message: '请输入表单编码!' },
            { min: 2, max: 20, message: '表单编码在2-20个字符之间' },
          ],
        },
        description: {
          rules: [
            { required: true, message: '请输入描述!' },
            { min: 2, max: 50, message: '描述长度在2-50个字符之间' },
          ],
        },
      },
    }
  },
  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    },
  },
  created() {
    //如果是流程中表单，则需要加载流程表单data
    this.showFlowData()
    this.findAll()
  },
  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(pick(this.model, 'name', 'code', 'description', 'assetsCategoryId'))
      })
    },
    //渲染流程表单数据
    showFlowData() {
      if (this.formBpm === true) {
        let params = { id: this.formData.dataId }
        getAction(this.url.queryById, params).then((res) => {
          if (res.success) {
            this.edit(res.result)
          }
        })
      }
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let formData = Object.assign(this.model, values)
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    popupCallback(row) {
      this.form.setFieldsValue(pick(row, 'name', 'code', 'description', 'assetsCategoryId'))
    },

    findAll() {
      getAction(this.url.findAll).then((res) => {
        if (res.success) {
          this.assetsCategoryList = res.result
        }
      })
    },

    //   selDynamicTempByProId(e) {
    //   if (!!e) {
    //     let paramObj = {
    //       assetsCategoryId: e.trim()
    //     }
    //     getAction(this.url.CategoryqueryById, paramObj).then(res => {
    //       if (!!res) {

    //         res.forEach(ele => {
    //           if (ele.defaultValue != null && ele.defaultValue.length > 0) {
    //             ele.connectValue = ele.defaultValue
    //           }
    //         })
    //         this.collectInfoList = res
    //       }
    //     })
    //   }
    // },
  },
}
</script>
<style lang="less" scoped>
::v-deep .two-words > div > label {
  letter-spacing: 4px;
}
::v-deep .two-words > div > label::after {
  letter-spacing: 0px;
}
</style>