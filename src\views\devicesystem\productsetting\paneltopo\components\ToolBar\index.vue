<template>
  <div class="bar-container">
    <div class="left-bar" @click="handleClick">
      <a-tooltip placement="bottom" v-if="operate === 'show-edit'">
        <template #title>
          <span>编辑</span>
        </template>
        <a-button name="edit" class="item-space" size="small" icon="edit"> </a-button>
      </a-tooltip>
      <a-tooltip placement="bottom" v-if="operate === 'show-edit'">
        <template #title>
          <span>删除面板</span>
        </template>
        <a-button name="cutPanel" class="item-space" size="small" icon="delete"> </a-button>
      </a-tooltip>
      <a-tooltip placement="bottom">
        <template #title>
          <span>放大</span>
        </template>
        <a-button name="zoomIn" class="item-space" size="small" icon="zoom-in"> </a-button>
      </a-tooltip>
      <a-tooltip placement="bottom">
        <template #title>
          <span>缩小</span>
        </template>
        <a-button name="zoomOut" class="item-space" size="small" icon="zoom-out"> </a-button>
      </a-tooltip>
      <a-tooltip placement="bottom" v-if="operate === 'create'">
        <template #title>
          <span>清空</span>
        </template>
        <a-button name="delete" class="item-space delete-img" size="small"> </a-button>
      </a-tooltip>
      <a-tooltip placement="bottom" v-if="operate === 'create'">
        <template #title>
          <span>回退</span>
        </template>
        <a-button :disabled="!canUndo" name="undo" class="item-space" size="small" icon="undo"> </a-button>
      </a-tooltip>
      <a-tooltip placement="bottom" v-if="operate === 'create'">
        <template #title>
          <span>前进</span>
        </template>
        <a-button :disabled="!canRedo" name="redo" class="item-space" size="small" icon="redo"> </a-button>
      </a-tooltip>
      <!-- <a-tooltip placement="bottom">
        <template #title>
          <span>复制 (Cmd + Shift + Z)</span>
        </template>
        <a-button name="copy"  class="item-space" size="small" icon="copy">
        </a-button>
      </a-tooltip> -->
      <a-tooltip placement="bottom" v-if="operate === 'create'">
        <template #title>
          <span>删除</span>
        </template>
        <a-button name="cut" class="item-space" size="small" icon="delete"> </a-button>
      </a-tooltip>
      <a-tooltip placement="bottom" v-if="portList">
        <template #title>
          <span>端口列表</span>
        </template>
        <a-button name="portList" class="item-space" size="small" icon="unordered-list"> </a-button>
      </a-tooltip>
      <a-tooltip placement="bottom" v-if="operate === 'show'">
        <template #title>
          <span>刷新</span>
        </template>
        <a-button name="reload" class="item-space" size="small" icon="reload"> </a-button>
      </a-tooltip>
      <a-tooltip placement="bottom"  v-if="operate === 'create'">
        <template #title>
          <span>{{gridShow?"隐藏网格":"显示网格"}}</span>
        </template>
        <span v-if="!gridShow" class="nav-icon icon-hover" style="background-color:#fff">
          <img name="grid" src="./img/grid.png" alt="">
        </span>
        <span v-if="gridShow" class="nav-icon" style="background-color:#1E3674">
          <img name="grid" src="./img/grid-white.png" alt="">
        </span>
      </a-tooltip>
      <a-popover placement="bottom" title='端口状态说明'>
        <template slot='content'>
          <div style='display: flex'>
            <div class='stateBox'>
              <span>连接：</span>
              <div class='statu-rect' :style="{background:panelConfig.upColor}"></div>
            </div>
            <div class='stateBox'>
              <span>关闭：</span>
              <div class='statu-rect' :style="{background:panelConfig.downColor}"></div>
            </div>
            <div class='stateBox'>
              <span>告警：</span>
              <div class='alarm-rect':style="{borderColor:panelConfig.alarmColor}"></div>
            </div>
          </div>
        </template>
        <a-icon v-if='operate === "show"' type='question-circle' theme='twoTone' style='font-size: 18px' />
      </a-popover>
      <!-- <a-tooltip placement="bottom">
        <template #title>
          <span>粘贴 (Cmd + V)</span>
        </template>
        <a-button name="paste" class="item-space" size="small" icon="snippets">
        </a-button>
      </a-tooltip> -->
      <!-- <a-tooltip placement="bottom">
        <template #title>
          <span>保存PNG (Cmd + S)</span>
        </template>
        <a-button name="savePNG"  class="item-space" size="small" icon="download">
          png
        </a-button>
      </a-tooltip> -->
      <!-- <a-tooltip placement="bottom">
        <template #title>
          <span>打印 (Cmd + P)</span>
        </template>
        <a-button name="print" class="item-space" size="small" icon="printer">
        </a-button>
      </a-tooltip> -->
      <!-- <a-tooltip placement="bottom">
        <template #title>
          <span>导出 (Cmd + P)</span>
        </template>
        <a-button name="toJSON"  class="item-space" size="small">
          toJSON
        </a-button>
      </a-tooltip> -->
      <!-- <a-tooltip placement="bottom" v-if="sonTopoFlag">
        <template #title>
          <span>跳转到父拓扑</span>
        </template>
        <a-button name="back"  class="item-space" size="small" icon="rollback">
          返回到父拓扑
        </a-button>
      </a-tooltip> -->
    </div>
    <div class="right-bar">
      <a-select v-model="barPanelType" style="width: 120px" @change="panelTypeChange">
        <a-select-option 
        v-for="item in panelTypes" 
        :key="item.key" 
        :value="item.key"> {{item.label}} </a-select-option>
      </a-select>
      <!-- <a-tooltip placement="bottom" v-if="operate === 'create'">
        <template #title>
          <span>保存</span>
        </template>
        <a-button name="save"  class="item-space" size="small" icon="save">
        </a-button>
      </a-tooltip> -->
      <!-- <a-tooltip placement="bottom" v-if="operate === 'create'">
        <template #title>
          <span>返回展示</span>
        </template>
        <a-button name="desktop"  class="item-space" size="small" icon="desktop">
        </a-button>
      </a-tooltip> -->
    </div>
  </div>
</template>

<script>
import FlowGraph from '../../graph'
import { DataUri } from '@antv/x6'
import { globalGridAttr } from '../../models/global'
export default {
  name: 'Index',
  components: {},
  props: {
    sonTopoFlag: {
      type: Boolean,
      default: false,
      require: false,
    },
    operate: {
      type: String,
      default: 'create',
      required: true,
    },
    portList: {
      type: Boolean,
      default: false,
      required: false,
    },
    panelType: {
      type: String,
      default: '0',
      required: true,
    },
    gridShow: {
      type: Boolean,
      default:false,
      required: false,
    },
    panelTypes: {
      type: Array,
      default: ()=>[],
      required: true,
    },
  },
  data() {
    return {
      canUndo: '',
      canRedo: '',
      backFlag: false,
      panelConfig:{},
    }
  },

  mounted() {
    setTimeout(() => {
      this.initEvent()
    }, 200)
  },
  computed: {
    barPanelType: {
      get() {
        return this.panelType
      },
      set(v) {
        this.$emit('setPanelType', v)
      },
    },
  },
  methods: {
    panelTypeChange(val) {
      console.log(`selected ${val}`)
    },
    initEvent() {

      const { graph } = FlowGraph

      this.panelConfig = globalGridAttr.topoConfig;
      // console.log(this.panelConfig,"吼吼哈嘿", graph.isHistoryEnabled())
      const { history } = graph
      history.on('change', () => {
        this.canUndo = history.canUndo()
        this.canRedo = history.canRedo();
      })
      // graph.bindKey('ctrl+z', () => {
      //   if (history.canUndo()) {
      //     history.undo()
      //   }
      //   return false
      // })
      // graph.bindKey('ctrl+shift+z', () => {
      //   if (history.canRedo()) {
      //     history.redo()
      //   }
      //   return false
      // })
      // graph.bindKey('ctrl+d', () => {
      //   graph.clearCells()
      //   return false
      // })
      // graph.bindKey('ctrl+s', () => {
      //   graph.toPNG((datauri) => {
      //     DataUri.downloadDataUri(datauri, 'chart.png')
      //   })
      //   return false
      // })
      // graph.bindKey('ctrl+p', () => {
      //   graph.printPreview()
      //   return false
      // })
      // graph.bindKey('ctrl+c', this.copy)
      // graph.bindKey('ctrl+v', this.paste)
      // graph.bindKey('ctrl+x', this.cut)
    },
    copy() {
      const { graph } = FlowGraph
      const cells = graph.getSelectedCells()
      if (cells.length) {
        graph.copy(cells)
      }
      return false
    },
    cut() {
      const { graph } = FlowGraph
      const cells = graph.getSelectedCells()
      if (cells.length) {
        graph.cut(cells)
      }
      return false
    },
    paste() {
      const { graph } = FlowGraph
      if (!graph.isClipboardEmpty()) {
        const cells = graph.paste({ offset: 32 })
        graph.cleanSelection()
        graph.select(cells)
      }
      return false
    },
    handleClick(event) {
      const { graph } = FlowGraph
      const name = event.target.name
      switch (name) {
        case 'reload':
          this.$emit('reload')
          break
        case 'grid':
          this.$emit('grid')
          break
        case 'portList':
          this.$emit('portList')
          break
        case 'cutPanel':
          this.$emit('cutPanel')
          break
        case 'edit':
          this.$emit('edit')
          break
        case 'zoomIn':
          graph.zoom(0.2)
          break
        case 'zoomOut':
          graph.zoom(-0.2)
          break
        case 'undo':
          graph.history.undo()
          break
        case 'redo':
          graph.history.redo()
          break
        case 'delete':
          graph.clearCells()
          break
        case 'savePNG':
          graph.toPNG(
            (dataUri) => {
              // 下载
              DataUri.downloadDataUri(dataUri, 'chartx.png')
            },
            {
              backgroundColor: 'white',
              padding: {
                top: 20,
                right: 30,
                bottom: 40,
                left: 50,
              },
              quality: 1,
            }
          )
          break
        case 'saveSVG':
          graph.toSVG(
            (dataUri) => {
              // 下载
              DataUri.downloadDataUri(DataUri.svgToDataUrl(dataUri), 'chart.svg')
            },
            {
              preserveDimensions: {
                width: 100,
                height: 100,
              },
            }
          )
          break
        case 'print':
          graph.printPreview()
          break
        case 'copy':
          this.copy()
          break
        case 'cut':
          this.cut()
          break
        case 'paste':
          this.paste()
          break
        case 'toJSON':
          // graph.fromJSON({cells:[graph.toJSON().cells[0],graph.toJSON().cells[1]]})
          break
        case 'save':
          this.$emit('save')
          break
        case 'desktop':
          this.$emit('desktop')
          break
        default:
          break
      }
    },
  },
}
</script>

<style lang="less" scoped>
button {
  margin-right: 8px;
}
.bar-container {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.left-bar,
.right-bar {
  margin-left: 16px;
  margin-right: 16px;
  display: flex;
  align-items: center;
}
.delete-img {
  width: 24px;
  height: 24px;
  padding: 0;
  font-size: 14px;
  border-radius: 4px;
  background: url('./img/clear-up.png') no-repeat center;
}
button.item-space.delete-img.ant-btn.ant-btn-sm {
  background: url('./img/clear-up.png') no-repeat center;
}
.nav-icon {
  display: inline-block;
  width: 24px;
  height: 24px;
  padding: 0;
  font-size: 14px;
  margin-right: 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  img {
    width: 16px;
    height: 16px;
  }
}
.icon-hover {
  &:hover {
    border: 1px solid #1E3674;
  }
}
.stateBox{
  display: flex;
  margin-right: 12px;
  align-items: center;
  .statu-rect{
    width: 16px;
    height:16px;
    background: red;
  }
  .alarm-rect{
    width: 16px;
    height:16px;
    border: 1px solid red;
  }
}
</style>
