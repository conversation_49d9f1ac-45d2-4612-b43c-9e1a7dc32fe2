<!--
 * @Description: 传入record数据，通过判断record.type，来渲染对应的组件
 * @Author: kcz
 * @Date: 2020-01-02 22:41:48
 * @LastEditors: kcz
 * @LastEditTime: 2021-05-28 00:59:02
 -->
<template>
  <a-form-item
    v-if="formItemList.includes(record.type) && !record.options.hidden"
    :class="{ 'evaluation-field-item': record.options.isEvaluationField }"
    :label-col="
      formConfig.layout === 'horizontal'
        ? formConfig.labelLayout === 'flex'
          ? { style: `width:${formConfig.labelWidth}px` }
          : formConfig.labelCol
        : {}
    "
    :wrapper-col="
      formConfig.layout === 'horizontal'
        ? formConfig.labelLayout === 'flex'
          ? { style: 'width:auto;flex:1' }
          : formConfig.wrapperCol
        : {}
    "
    :style="formConfig.layout === 'horizontal' && formConfig.labelLayout === 'flex' ? { display: 'flex' } : {}"
  >
    <span slot="label" v-if="record.options.showLabel === undefined || record.options.showLabel">
      <a-tooltip>
        <span
          :class="{ 'evaluation-field-label': record.options.isEvaluationField }"
          :style="record.options.isEvaluationField ? 'color: #1890ff; font-weight: 600;' : ''"
        >
          <span v-if="record.options.isEvaluationField" class="evaluation-indicator">★</span>
          <span v-text="record.label"></span>
        </span>
        <span v-if="record.help" slot="title" v-html="record.help"></span>
        <a-icon v-if="record.help" class="question-circle" type="question-circle-o" />
      </a-tooltip>
    </span>
    <!-- 多行文本 -->
    <a-textarea
      :style="`width:${record.options.width}`"
      v-if="record.type === 'textarea'"
      :autoSize="{
        minRows: record.options.minRows,
        maxRows: record.options.maxRows,
      }"
      :disabled="disabled || record.options.disabled"
      :placeholder="record.options.placeholder"
      :allowClear="record.options.clearable"
      :maxLength="record.options.maxLength"
      :rows="4"
      @change="handleChange($event.target.value, record.model)"
      v-decorator="[
        record.model, // input 的 name
        {
          initialValue: record.options.defaultValue, // 默认值
          rules: record.rules, // 验证规则
        },
      ]"
    />
    <!-- 单选框 -->
    <a-radio-group
      v-else-if="record.type === 'radio'"
      :options="optionsData"
      :disabled="disabled || record.options.disabled"
      @change="handleChange($event.target.value, record.model)"
      v-decorator="[
        record.model,
        {
          initialValue: record.options.defaultValue,
          rules: record.rules,
        },
      ]"
    />
    <!-- 多选框 -->
    <a-checkbox-group
      v-else-if="record.type === 'checkbox'"
      :options="optionsData"
      :disabled="disabled || record.options.disabled"
      @change="handleChange($event, record.model)"
      v-decorator="[
        record.model,
        {
          initialValue: record.options.defaultValue,
          rules: record.rules,
        },
      ]"
    />
    <!-- 开关 -->
    <a-switch
      v-else-if="record.type === 'switch'"
      :disabled="disabled || record.options.disabled"
      @change="handleChange($event, record.model)"
      v-decorator="[
        record.model,
        {
          initialValue: record.options.defaultValue,
          valuePropName: 'checked',
          rules: record.rules,
        },
      ]"
    />
    <!-- 滑块 -->
    <div v-else-if="record.type === 'slider'" :style="`width:${record.options.width}`" class="slider-box">
      <div class="slider">
        <a-slider
          :disabled="disabled || record.options.disabled"
          :min="record.options.min"
          :max="record.options.max"
          :step="record.options.step"
          @change="handleChange($event, record.model)"
          v-decorator="[
            record.model,
            {
              initialValue: record.options.defaultValue,
              rules: record.rules,
            },
          ]"
        />
      </div>
      <div class="number" v-if="record.options.showInput">
        <a-input-number
          style="width: 100%"
          :disabled="disabled || record.options.disabled"
          :min="record.options.min"
          :max="record.options.max"
          :step="record.options.step"
          @change="handleChange($event, record.model)"
          v-decorator="[
            record.model,
            {
              initialValue: record.options.defaultValue,
              rules: [
                {
                  validator: (rule, value, callback) => {
                    if (record.options.step && value % record.options.step !== 0) {
                      callback('输入值必须是步长的倍数')
                    }
                    callback()
                  },
                },
              ],
            },
          ]"
        />
      </div>
    </div>
    <component
      v-else
      :ref="['batch', 'selectInputList'].includes(record.type) && 'KBatch'"
      :getPopupContainer="(node) => node.parentNode"
      :style="`width:${record.options.width}`"
      v-bind="componentOption"
      :min="record.options.min || record.options.min === 0 ? record.options.min : -Infinity"
      :max="record.options.max || record.options.max === 0 ? record.options.max : Infinity"
      :precision="
        record.options.precision > 50 || (!record.options.precision && record.options.precision !== 0)
          ? null
          : record.options.precision
      "
      :parentDisabled="disabled || record.options.disabled"
      :disabled="disabled || record.options.disabled"
      :record="record"
      :config="config"
      :filterOption="
        record.options.showSearch
          ? (inputValue, option) => {
              return option.componentOptions.children[0].text.toLowerCase().indexOf(inputValue.toLowerCase()) >= 0
            }
          : false
      "
      :allowClear="record.options.clearable"
      :dynamicData="dynamicData"
      :treeData="optionsData"
      :options="optionsData"
      treeNodeFilterProp="title"
      :mode="record.options.multiple ? 'multiple' : ''"
      @change="handleChange($event, record.model)"
      v-decorator="[
        record.model, // input 的 name
        {
          initialValue: record.options.defaultValue, // 默认值
          rules: record.rules, // 验证规则
        },
      ]"
      :is="componentItem"
    ></component>
  </a-form-item>
  <!-- 可隐藏label -->
  <a-form-item
    v-else-if="['batch', 'editor', 'selectInputList'].includes(record.type)"
    :label="!record.options.showLabel ? '' : record.label"
    :label-col="
      formConfig.layout === 'horizontal' && record.options.showLabel
        ? formConfig.labelLayout === 'flex'
          ? { style: `width:${formConfig.labelWidth}px` }
          : formConfig.labelCol
        : {}
    "
    :wrapper-col="
      formConfig.layout === 'horizontal' && record.options.showLabel
        ? formConfig.labelLayout === 'flex'
          ? { style: 'width:auto;flex:1' }
          : formConfig.wrapperCol
        : {}
    "
    :style="
      formConfig.layout === 'horizontal' && formConfig.labelLayout === 'flex' && record.options.showLabel
        ? { display: 'flex' }
        : {}
    "
  >
    <component
      :ref="['batch', 'selectInputList'].includes(record.type) && 'KBatch'"
      :style="`width:${record.options.width}`"
      v-bind="componentOption"
      :record="record"
      :config="config"
      :parentDisabled="disabled || record.options.disabled"
      :disabled="disabled || record.options.disabled"
      :dynamicData="dynamicData"
      @change="handleChange($event, record.model)"
      v-decorator="[
        record.model, // input 的 name
        {
          initialValue: record.options.defaultValue, // 默认值
          rules: record.rules, // 验证规则
        },
      ]"
      :is="componentItem"
    ></component>
  </a-form-item>
  <!-- button按钮 -->
  <a-form-item v-else-if="record.type === 'button'">
    <a-button
      :disabled="disabled || record.options.disabled"
      @click="dynamicFun"
      :type="record.options.type"
      :html-type="record.options.handle === 'submit' ? 'submit' : undefined"
      >{{ record.label }}</a-button
    >
  </a-form-item>
  <!-- alert提示 -->
  <a-form-item v-else-if="record.type === 'alert'">
    <a-alert
      :message="record.label"
      :description="record.options.description"
      :type="record.options.type"
      :showIcon="record.options.showIcon"
      :closable="record.options.closable"
      :banner="record.options.banner"
    />
  </a-form-item>

  <!-- 文本 -->
  <a-form-item v-else-if="record.type === 'text'">
    <div :style="{ textAlign: record.options.textAlign }">
      <label
        :class="{ 'ant-form-item-required': record.options.showRequiredMark }"
        :style="{
          fontFamily: record.options.fontFamily,
          fontSize: record.options.fontSize,
          color: record.options.color,
        }"
        v-text="record.label"
      ></label>
    </div>
  </a-form-item>
  <!-- html -->
  <div v-else-if="record.type === 'html'" v-html="record.options.defaultValue"></div>

  <!-- 自定义组件 -->
  <customComponent
    v-else-if="customList.includes(record.type)"
    :record="record"
    :disabled="disabled"
    :dynamicData="dynamicData"
    @change="handleChange($event, record.model)"
    :formConfig="formConfig"
  />

  <div v-else-if="record.type === 'divider'">
    <!-- 分割线 -->
    <a-divider v-if="record.label !== '' && record.options.orientation" :orientation="record.options.orientation">{{
      record.label
    }}</a-divider>
    <a-divider v-else-if="record.label !== ''">{{ record.label }}</a-divider>
    <a-divider v-else-if="record.label === ''" />
  </div>
</template>
<script>
/*
 * author kcz
 * date 2019-11-20
 */
// import moment from "moment";
import customComponent from './customComponent'
import ComponentArray from '../core/components_use'
import { formItemList } from '../KFormDesign/config/formItemsConfig'
const _ = require('lodash/object')
import { httpAction, getAction } from '@/api/manage'
import { loadTreeData, ajaxGetDictItems, getDictItemsFromCache } from '@/api/api'
export default {
  name: 'KFormItem',
  components: {
    customComponent,
  },
  props: {
    // 表单数组
    record: {
      type: Object,
      required: true,
    },
    // form-item 宽度配置
    formConfig: {
      type: Object,
      required: true,
    },
    config: {
      type: Object,
      default: () => ({}),
    },
    dynamicData: {
      type: Object,
      default: () => ({}),
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    //判断是不是再编辑面板中使用
    isPanel: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      formItemList,
      optionsData: [],
    }
  },

  created() {
    this.initOptions()
    this.initFieldsStatus();
    // console.log("初始化了控件 == ",this.record)
  },
  computed: {
    customList() {
      if (window.$customComponentList) {
        return window.$customComponentList.map((item) => item.type)
      } else {
        return []
      }
    },
    /**
     * @description: 输出对应组件
     * @param {*}
     * @return {*} component
     */

    componentItem() {
      return ComponentArray[this.record.type]
    },
    componentOption() {
      return _.omit(this.record.options, ['defaultValue', 'disabled'])
    },
  },
  watch: {
    // 编辑状态下控件属性 和 控件面板的选项值的动态改变
    'record.options.options'(v) {
      let options = this.record.options
      if (options && options.options) {
        this.optionsData = options.options || []
      }
    },
  },
  methods: {
    parseJsonString(dataString, type = 1) {
      try {
        return JSON.parse(dataString)
      } catch (error) {
        console.log('解析参数报错', error)
        return type === 1 ? {} : []
      }
    },
    // 初始化 联动字段 状态
    initFieldsStatus() {
      if(this.isPanel)return;
      let options = this.record.options
      if (options) {
        this.changeFieldsStatus(options.defaultValue,true)
      }
    },
    async changeFieldsStatus(v,isDefault) {
      let vm = this
      let options = this.record.options
      if (options && options.linkageData) {
        this.$emit('changeFields', {
          options: this.record.options,
          value: v,
          isDefault,
          model:this.record.model,
          })
        return
        let linkageData = this.parseJsonString(options.linkageData, 2)
        if (!Array.isArray(linkageData)) return
        linkageData.forEach((el) => {
          if (el.fields && el.fields.length > 0) {
            // console.log("执行了 === ",el,v)
            let type = 'hide'
            if(el.handler){
              let handler = Function('"use strict";return (' + el.handler + ')')()
              if(typeof handler === 'function'){
                handler(v,httpAction).then(res=>{
                   console.log("dfdqwq",res)
                   type = res?'show':'hide';
                   this.$emit('changeFields', { fields: el.fields, type: type })
                })
              }
           }
            else{
              type = el.value == v?'show':'hide';
              this.$emit('changeFields', { fields: el.fields, type: type })
            }



          }
        })
      }
    },
    //初始化空间选项数据
    initOptions() {
      //在编辑面板加载时不执行此操作
       if(this.isPanel)return;
      let options = this.record.options
      let getAccessToken = this.$getAccessToken
      //上传文件 图片组件 设置token
      if(options && options.headers && getAccessToken  && typeof getAccessToken === 'function'){
        options.headers['X-Access-Token'] = getAccessToken()
      }
      //上传文件 图片组件 拼接路径
      if(["uploadImg","uploadFile"].includes(this.record.type) && options ){
        if(options.action && !options.action.startsWith("http")){
          if(window._CONFIG['domianURL']){
            options.action =  window._CONFIG['domianURL'] +options.action;
          }else{
             options.action = document.location.origin+options.action
          }
        }
        if(options.downloadImageUrl && !options.downloadImageUrl.startsWith("http")){
          if(window._CONFIG['domianURL']){
            options.downloadImageUrl = window._CONFIG['domianURL'] +options.downloadImageUrl;
          }else{
             options.downloadImageUrl = document.location.origin+options.downloadImageUrl
          }
        }
        if(options.downloadFileUrl && !options.downloadFileUrl.startsWith("http")){
          if(window._CONFIG['domianURL']){
            options.downloadFileUrl = window._CONFIG['domianURL'] +options.downloadFileUrl;
          }else{
             options.downloadFileUrl = document.location.origin+options.downloadFileUrl
          }
        }
      }
      else if(this.record.type === 'cascader' && options){//级联选择默认值要解析 级联默认选项是数组
        options.defaultValue = this.parseJsonString(options.defaultValue,2)
      }
      if (options && options.dynamic) {
        if (options.dynamic === 'static') {
          this.optionsData = this.record.options.staticOptions
        } else if (options.dynamic === 'dynamic') {
          ajaxGetDictItems(options.dynamicKey).then((res) => {
            if (res.success) {
              this.optionsData = res.result.map((el) => {
                el.label = el.title
                return el
              })
            }
          })
        } else if (options.dynamic === 'ajax' && !this.isPanel) {
          // 编辑面板中的不获取接口数据；
          let ajaxData = options.ajaxData
          if (!ajaxData.url) {
            this.optionsData = []
            return
          }
          let params = this.parseJsonString(ajaxData.params)
          let header = this.parseJsonString(ajaxData.header)
          httpAction(ajaxData.url, params, ajaxData.type, header).then((res) => {
            var vm = this
            let fd = Function('"use strict";return (' + ajaxData.callFunc + ')')()
            this.optionsData = fd(res)
          })
        }
      }
    },
    validationSubform() {
      // 验证动态表格
      if (!this.$refs.KBatch) return true
      if(Array.isArray(this.$refs.KBatch)){
        return this.$refs.KBatch.every(item=>item.validationSubform())
      }else{
        return this.$refs.KBatch.validationSubform()
      }

    },
    //控件change 事件
    handleChange(e, key) {
      let value = e
      if (e && e.target) {
        value = e.target.value
      }
      this.changeFieldsStatus(value,false)
      let options = this.record.options
      let callback = options && options.changeFunc?options.changeFunc:""
      // 传递change事件
      this.$emit('change', value, key,callback)
    },
    //动态函数按钮点击事件
    dynamicFun(){
      let handle = this.record.options.handle;
      if(handle === 'submit'){
        this.$emit('submit')
      }else if(handle === 'reset'){
        this.$emit('reset')
      }else{
         this.$emit("btnFunc",this.record.options.dynamicFun)
      }
    },
  },
}
</script>
<style lang="less" scoped>
.slider-box {
  display: flex;
  > .slider {
    flex: 1;
    margin-right: 16px;
  }
  > .number {
    width: 70px;
  }
}
.anticon.anticon-question-circle-o {
  margin-left: 5px;
}

/* 评估指标字段样式 */
.evaluation-field-item {
  position: relative;
  border-left: 3px solid #1890ff;
  padding-left: 8px;
  background: linear-gradient(90deg, rgba(24, 144, 255, 0.05) 0%, rgba(24, 144, 255, 0.01) 100%);
  border-radius: 4px;
  margin-bottom: 16px;
}

.evaluation-field-item::before {
  content: '';
  position: absolute;
  left: -3px;
  top: 0;
  bottom: 0;
  width: 3px;
  background: linear-gradient(180deg, #1890ff 0%, #40a9ff 100%);
  border-radius: 2px;
}

.evaluation-field-label {
  position: relative;
}

.evaluation-indicator {
  color: #faad14;
  font-size: 14px;
  margin-right: 4px;
  font-weight: bold;
  text-shadow: 0 0 2px rgba(250, 173, 20, 0.3);
}
</style>
