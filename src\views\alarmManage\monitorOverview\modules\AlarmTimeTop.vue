<template>
  <!-- 告警最长持续时间top -->
  <div style="height: 100%;width: 100%;">
    <div v-if="chartData.length===0">
      <a-list :data-source="[]" />
    </div>
    <div v-else ref="alarmTimeTop" style="height: 100%;width: 100%;"></div>
  </div>
</template>
<script>
import { getAction } from '@/api/manage'
export default {
  props: {
    unit: {
      type: String,
      default: ''
    },
    timeData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      chartData: [],
      url: {
        list: '/monitor/Overview/alarm/duration'
      }
    }
  },
  watch: {
    timeData: {
      deep: true,
      handler(val) {
        this.getData()
      }
    }
  },
  mounted() {
    this.getData()
  },
  methods: {
    getData() {
      const params = {};
      if (this.timeData.startTime) {
        params.startTime = this.timeData.startTime;
      }
      if (this.timeData.endTime) {
        params.endTime = this.timeData.endTime;
      }
      getAction(this.url.list, params).then(res => {
        if (res.code == 200) {
          this.chartData = res.result
          if (this.chartData.length > 0) {
            this.$nextTick(() => {
              this.initChart(res.result)
            })
          } else {
            if (this.myChart) {
              this.myChart.dispose()
            }
          }
        }
      })
    },
    initChart(data) {
      if (data.length <= 0) {
        return
      }
      let maxValue = data[0].durationHours //最大值
      let yName = [] // y轴名称
      let yData = []
      let maxArr = []

      data.forEach(element => {
        yName.push(element.deviceName)
        yData.push(element.durationHours)
        maxArr.push(maxValue)
      })
      if (maxValue == 0) {
        this.chartData = []
        return
      }

      this.myChart = this.$echarts.init(this.$refs.alarmTimeTop)
      this.myChart.clear()

      let option = {
        tooltip: {
          trigger: 'item',
          formatter: function(params) {
            let html = ''
            let option = data[params.dataIndex]
            html = `${option.deviceName}<br/>
              告警名称: ${option.templateName}<br/>
              ${option.levelName ? '告警级别: ' + option.levelName + '<br/>' : ''}
              告警持续时间: ${option.durationHours || 0}h`
            return html
          }
        },
        xAxis: {
          max: maxValue,
          splitLine: {
            show: false
          },
          axisLine: {
            show: false
          },
          axisLabel: {
            show: false
          },
          axisTick: {
            show: false
          }
        },
        grid: {
          left: '2%',
          top: '8%',
          right: 80,
          bottom: 5,
          containLabel: true
        },
        dataZoom: [
          {
            show: false, // 为true 滚动条出现
            startValue: 0,
            endValue: 4,
            yAxisIndex: [0] //关联多个y轴
          },
          {
            //没有下面这块的话，只能拖动滚动条，鼠标滚轮在区域内不能控制外部滚动条
            type: 'inside',
            yAxisIndex: 0,
            zoomOnMouseWheel: false, //滚轮是否触发缩放
            moveOnMouseMove: true, //鼠标滚轮触发滚动
            moveOnMouseWheel: true
          }
        ],
        yAxis: [
          {
            // 每条图形上面的文字
            inverse: true,
            data: yName,
            axisLabel: {
              padding: [-28, 0, 0, -8],
              inside: true,
              textStyle: {
                fontSize: 12,
                color: 'rgba(0,0,0,0.45)',
                align: 'left'
              },
              formatter: '{value}'
            },
            splitLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            }
          }
        ],
        series: [
          {
            // 真实值
            name: '真实值',
            type: 'bar',
            barWidth: 8,
            itemStyle: {
              normal: {
                color: {
                  x: 0,
                  y: 0,
                  x2: 1,
                  y2: 0,
                  colorStops: [
                    {
                      offset: 0,
                      color: '#54E4FF'
                    },
                    {
                      offset: 1,
                      color: '#409EFF'
                    }
                  ]
                }
              }
            },
            label: {
              show: false
            },
            data: yData,
            z: 2
          },
          {
            // 背景样式
            name: '背景',
            type: 'bar',
            barGap: '-100%',
            barWidth: 8,
            itemStyle: {
              normal: {
                borderWidth: 0,
                color: 'rgba(64,158,255,0.16)'
              },
              barBorderRadius: 10
            },
            label: {
              show: true,
              position: 'right',
              distance: 0,
              color: 'rgba(0,0,0,0.45)',
              fontSize: '12',
              padding: [0, 0, 0, 15],
              formatter: function(data) {
                return `${yData[data.dataIndex]}h`
              }
            },
            data: maxArr,
            z: 0
          },
          {
            //条形码
            name: 'info',
            type: 'pictorialBar',
            symbol: 'fixed',
            barGap: '-100%',
            symbolRepeat: 'repeat',
            itemStyle: {
              normal: {
                color: '#fff'
              }
            },
            data: maxArr,
            symbolMargin: 2,
            symbolSize: [2, 14],
            z: 3
          },
          {
            // 边框样式
            name: '边框',
            type: 'bar',
            barGap: '-110%',
            barWidth: 12,
            itemStyle: {
              normal: {
                color: 'transparent',
                borderWidth: 1,
                borderColor: 'rgba(64,158,255,0.16)'
                // borderColor: 'red'
              }
            },
            label: {
              show: false
            },
            data: maxArr,
            z: 4
          }
        ]
      }
      this.myChart.setOption(option)
      window.addEventListener('resize', () => {
        this.myChart.resize()
      })
    }
  }
}
</script>
<style scoped lang="less">
</style>