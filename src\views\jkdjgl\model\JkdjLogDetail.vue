<template>
  <j-modal :title='title' :width='width' :visible='visible' :destroyOnClose='true'
           :okButtonProps="{style:{display:'none'}}" :centered='true' switchFullscreen
           @cancel='handleCancel'
           cancelText='关闭'>
    <div>
      <a-result
        :status="status"
        :title="resultTitle"
        :sub-title="resultSubTitle"
      >
        <div class="desc">
          <p style="font-size: 16px;">
            <strong>日志内容:</strong>
          </p>
          <p>
           {{record.logContent}}
          </p>
        </div>
      </a-result>
    </div>
  </j-modal>
</template>
<script>
export default {
  name: 'JkdjLogDetail',
  props: {},
  components: {},
  data() {
    return {
      title: '记录详情',
      width: '1000px',
      visible: false,
      disableSubmit: false,
      confirmLoading: false,
      record: {},
      status: 'error',
      resultTitle:"",
      resultSubTitle:"",
    }
  },
  methods: {
    show(record) {
      this.record = record
      this.status = record.resultFlag == 1 ? 'success' : 'error'
      this.resultTitle = record.resultFlag == 1 ? '执行成功' : '执行失败'
      this.resultSubTitle = `操作类型：${record.operateTypeText}\u3000生成时间：${record.createTime}`
      this.visible = true
    },
    handleCancel() {
      this.visible = false
    },
  }
}
</script>



<style scoped lang='less'>
@import '~@assets/less/normalModal.less';
</style>