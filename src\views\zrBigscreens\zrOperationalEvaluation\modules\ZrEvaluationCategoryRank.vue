<template>
  <div class='zr-evaluation-category-rank'>
    <zr-bigscreen-title title='评估分类排名'>
    </zr-bigscreen-title>
    <div class='rank-content'>
      <div style='height: 15%'></div>
      <div style='height: 70%'>
        <zr-rank-bar :rank-data='rankData' ranking></zr-rank-bar>
      </div>
    </div>
  </div>
</template>
<script>
  import ZrBigscreenTitle from '@views/zrBigscreens/modules/ZrBigscreenTitle.vue'
  import ZrRankBar from '@views/zrBigscreens/zrOperationalEvaluation/modules/ZrRankBar.vue'

  export default {
    name: 'ZrEvaluationCategoryRank',
    components: {
      ZrRankBar,
      ZrBigscreenTitle
    },
    props: {},
    data() {
      return {
        rankData: [],
      }
    },
    created() {},
    mounted() {},
    methods: {
      show(record) {
        record.metricsTypeList.forEach((e) => {
          this.rankData.push({
            typeName: e.typeName,
            score: e.metricsTypeResult == 'A' ? 100 : e.metricsTypeResult == 'B' ? 75 : e.metricsTypeResult ==
              'C' ? 50 : e.metricsTypeResult == 'A' ? 25 : 0,
            metricsTypeResult: e.metricsTypeResult != null ? e.metricsTypeResult : '-'
          })
        })
      },
    }
  }
</script>
<style scoped lang='less'>
  .zr-evaluation-category-rank {
    width: 100%;
    height: 100%;

    .rank-content {
      width: 100%;
      height: calc(100% - 51px);
      padding: 0 16px;
      background: linear-gradient(to right, rgba(29, 78, 140, 0.3), rgba(29, 78, 140, 0.0));
    }
  }
</style>