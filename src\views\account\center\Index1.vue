<template>
  <div class="page-header-index-wide page-header-wrapper-grid-content-main">
    <a-row :gutter="24">
      <a-col :md="24" :lg="7">
        <a-card :bordered="false">
          <div>
            <h1>个人资料</h1>
          </div>
          <div class="account-center-avatarHolder">
            <div class="avatar">
              <img :src="userAvatar" />
            </div>
            <div>
              <span id="uploadImg">
                <a-upload name="file" :multiple="false" :action="uploadAction" @change="handleChange">
                  <a href="#">修改头像</a>
                </a-upload>
              </span>
            </div>
          </div>
          <div class="account-center-detail">
            <p><a-icon type="contacts" theme="twoTone" />{{ this.username }}</p>
            <a-divider />
            <p>
              <a-icon type="mobile" theme="twoTone" />{{ this.phone }}</p>
            <a-divider />
            <p><a-icon type="phone" theme="twoTone" />{{ this.telephone }}</p>
            <a-divider />
            <p><a-icon type="idcard" theme="twoTone" />{{ this.post }}</p>
            <a-divider />
            <p><a-icon type="bank" theme="twoTone" />{{ this.departName }}</p>
            <a-divider />
            <p><a-icon type="mail" theme="twoTone" />{{ this.email }}</p>
            <a-divider />
            <p><a-icon type="clock-circle" theme="twoTone" />{{ this.createTime }}</p>
            <a-divider />
          </div>
        </a-card>
      </a-col>
      <a-col :md="24" :lg="17">
        <a-card style="width: 100%" :bordered="false" :tabList="tabListNoTitle" :activeTabKey="noTitleKey"
          @tabChange="(key) => handleTabChange(key, 'noTitleKey')">
          <article-page v-if="noTitleKey === 'article'"></article-page>
          <app-page v-else-if="noTitleKey === 'app'"></app-page>
          <project-page v-else-if="noTitleKey === 'project'"></project-page>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script>
  import PageLayout from '@/components/page/PageLayout'
  import RouteView from '@/components/layouts/RouteView'
  import {
    AppPage,
    ArticlePage,
    ProjectPage
  } from './page'
  import {
    mapGetters
  } from 'vuex'
  import {
    getFileAccessHttpUrl,
    getAction
  } from '@/api/manage'


  export default {
    components: {
      RouteView,
      PageLayout,
      AppPage,
      ArticlePage,
      ProjectPage,
    },
    data() {
      return {
        uploadAction: window._CONFIG['domianURL'] + '/sys/common/upload',
        tagInputVisible: false,
        tagInputValue: '',
        post: '',
        departName: '',
        email: '',
        username: '',
        phone: '',
        telephone:'',
        signature: '',
        sex: '',
        realname: '',
        createTime: '',
        userid: '',
        value: "",
        teams: [],
        headers: {},
        teamSpinning: true,
        userAvatar: '',
        tabListNoTitle: [
          // {
          //   key: 'article',
          //   tab: '基本资料',
          // },
          {
            key: 'app',
            tab: '基本资料',
          },
          //  {
          //   key: 'project',
          //   tab: '项目(8)',
          // }
        ],
        noTitleKey: 'app',
        url: {
          getInfo: '/sys/user/getInfo', //个人信息
          // getTag: "/sys/user/getTag", //获取标签和个签
          // saveTag: "/sys/user/saveTag",//添加标签
          // signature: "/sys/user/signature",//修改个签
          updateUserInfo: '/sys/user/updateUserInfo', //编辑个人信息
          upload: window._CONFIG['domianURL'] + '/sys/common/upload', //图片上传
        },
      }
    },
    mounted() {
      // this.getTeams();
      this.getInfo()
      this.getAvatar()
      // this.getTag();
    },

    methods: {
      ...mapGetters(['nickname', 'avatar']),
      getAvatar() {
        getAction('/sys/user/getUserAvatar').then((res) => {
          if (res.success) {
            this.$store.commit('SET_AVATAR', res.result)
            this.$store.commit('SET_CA', true)
            this.$nextTick(() => {
              this.userAvatar = ''
              this.userAvatar = getFileAccessHttpUrl(res.result)
            })
          }
        })
        // return getFileAccessHttpUrl(this.avatar());
      },
      getTeams() {
        // this.$http.get('/mock/api/workplace/teams')
        //   .then(res => {
        //     this.teams = res.result
        //     this.teamSpinning = false
        //   })
      },
      getInfo() {
        this.$http.get(this.url.getInfo).then((res) => {
          if (res.code == 200) {

            this.post = res.result.post
            this.departName = res.result.orgCode
            this.email = res.result.email
            this.username = res.result.username
            this.phone = res.result.phone
            this.telephone = res.result.telephone
            this.createTime = res.result.birthday
            this.realname = res.result.realname
            this.sex = res.result.sex
            this.userid = res.result.id
            this.value = res.result.sex;
          } else {
            this.$message.error(res.message)
          }
        })
      },
      handleChange(info) {

        //判断只上传图片格式
        let str = info.file.type
        if (str == '') {
          return
        }
        this.picUrl = false
        let fileList = info.fileList
        //update-begin-author:wangshuai date:20201022 for:LOWCOD-969 判断number是否大于0和是否多选，返回选定的元素。
        if (this.number > 0 && this.isMultiple) {
          fileList = fileList.slice(-this.number)
        }
        //update-end-author:wangshuai date:20201022 for:LOWCOD-969 判断number是否大于0和是否多选，返回选定的元素。
        if (info.file.status === 'done') {
          if (info.file.response.success) {
            this.picUrl = true
            fileList = fileList.map((file) => {
              if (file.response) {
                file.url = file.response.message
              }
              return file
            })
          }
          //this.$message.success(`${info.file.name} 上传成功!`);
        } else if (info.file.status === 'error') {
          this.$message.error(`${info.file.name} 上传失败.`)
        }
        this.fileList = fileList
        if (info.file.status === 'done' || info.file.status === 'removed') {
          this.handlePathChange()
        }
      },
      handlePathChange() {

        let uploadFiles = this.fileList
        let path = ''
        if (!uploadFiles || uploadFiles.length == 0) {
          path = ''
        }
        let arr = []
        if (!this.isMultiple) {
          arr.push(uploadFiles[uploadFiles.length - 1].response.message)
        } else {
          for (let a = 0; a < uploadFiles.length; a++) {
            // update-begin-author:taoyan date:20200819 for:【开源问题z】上传图片组件 LOWCOD-783
            if (uploadFiles[a].status === 'done') {
              arr.push(uploadFiles[a].response.message)
            } else {
              return
            }
            // update-end-author:taoyan date:20200819 for:【开源问题z】上传图片组件 LOWCOD-783
          }
        }
        if (arr.length > 0) {

          path = arr.join(',')
          this.$http.post(this.url.updateUserInfo, {
            id: this.userid,
            avatar: path
          }).then((res) => {
            if (res.code == 200) {
              this.getAvatar()
            }
          })
          // this.getAvatar()
        }
        this.$emit('change', path)
      },
      getTag() {
        this.$http.get(this.url.getTag).then((res) => {
          if (res.code == 200) {
            this.signature = res.result.signature
          } else {
            this.signature = '编辑按钮'
          }
        })
      },

      handleTabChange(key, type) {
        this[type] = key
      },

      handleTagClose(removeTag) {
        const tags = this.tags.filter((tag) => tag != removeTag)
        this.tags = tags
      },

      showTagInput() {
        this.tagInputVisible = true
        this.$nextTick(() => {
          this.$refs.tagInput.focus()
        })
      },

      handleInputChange(e) {
        this.tagInputValue = e.target.value
      },

      handleTagInputConfirm() {
        const inputValue = this.tagInputValue
        let tags = this.tags
        if (inputValue && tags.indexOf(inputValue) === -1) {
          tags = [...tags, inputValue]
        }

        Object.assign(this, {
          tags,
          tagInputVisible: false,
          tagInputValue: '',
        })
      },

    },
  }
</script>

<style lang="less" scoped>
  .page-header-wrapper-grid-content-main {
    width: 100%;
    height: 100%;
    min-height: 100%;
    transition: 0.3s;

    .account-center-avatarHolder {
      text-align: center;
      margin-bottom: 24px;

      &>.avatar {
        margin: 0 auto;
        width: 104px;
        height: 104px;
        margin-bottom: 20px;
        border-radius: 50%;
        overflow: hidden;

        img {
          height: 100%;
          width: 100%;
        }
      }

      .username {
        color: rgba(0, 0, 0, 0.85);
        font-size: 20px;
        line-height: 28px;
        font-weight: 500;
        margin-bottom: 4px;
      }
    }

    .account-center-detail {
      // margin-top: 60px;

      p {
        margin-bottom: 8px;
        padding-left: 26px;
        position: relative;
      }

      i {
        position: absolute;
        height: 14px;
        width: 14px;
        left: 0;
        top: 4px;
      }

      .title {
        background-position: 0 0;
      }

      .group {
        background-position: 0 -22px;
      }

      .address {
        background-position: 0 -44px;
      }
    }

    .account-center-tags {
      .ant-tag {
        margin-bottom: 8px;
      }
    }

    .account-center-team {
      .members {
        a {
          display: block;
          margin: 12px 0;
          line-height: 24px;
          height: 24px;

          .member {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.65);
            line-height: 24px;
            max-width: 100px;
            vertical-align: top;
            margin-left: 12px;
            transition: all 0.3s;
            display: inline-block;
          }

          &:hover {
            span {
              color: #1e3674;
            }
          }
        }
      }
    }

    .tagsTitle,
    .teamTitle {
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
      margin-bottom: 12px;
    }
  }

  .avatar-uploader>.ant-upload {
    width: 104px;
    height: 104px;
  }

  #uploadImg {
    font-size: 14px;
    overflow: hidden;
    // position: absolute;
    // margin-left: -35px;
  }

  ::v-deep .ant-upload-list {
    display: none;
  }

  #file {
    position: absolute;
    z-index: 100;
    opacity: 0;
    filter: alpha(opacity=0);
    margin-left: 50;
  }

  .avatar-uploader>.ant-upload {
    width: 128px;
    height: 128px;
  }
</style>