<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll zxw">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <!-- 查询区域 -->
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <div class="table-page-search-wrapper-style">
          <a-form layout="inline" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="工单名称:">
                  <a-input
                    v-model="queryParam.orderName"
                    autocomplete="off"
                    :allowClear="true"
                    placeholder="请输入"
                  ></a-input>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="故障类型:">
                  <j-dict-select-tag
                    :allowClear="true"
                    v-model="queryParam.orderCategoryId"
                    dictCode="fault_type"
                    placeholder="请选择"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <!--起止时间选择-->
                <a-form-item label="分配时间:">
                  <a-range-picker
                    @change="onChange"
                    v-model="queryParam.handleEndTimeRange"
                    format="YYYY-MM-DD"
                    :placeholder="['开始时间', '截止时间']"
                    class="a-range-picker-choice-date"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="colBtnsSpan()">
                <span
                  class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                >
                  <a-button type="primary" @click="searchQuery">查询</a-button>
                  <a-button @click="searchReset" style="margin-left: 10px">重置</a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered="false" style="flex: auto" class="core">
        <a-row class="lastBtn2">
          <div class="table-operator">
            <a-button @click="handleAddKnowledge">加入知识库</a-button>
            <a-dropdown v-if="selectedRowKeys.length > 0">
              <a-menu slot="overlay" style='text-align: center'>
                <a-menu-item key="1" @click="batchDel">删除</a-menu-item>
              </a-menu>
              <a-button> 批量操作 <a-icon type="down"/></a-button>
            </a-dropdown>
          </div>
        </a-row>
        <a-table
          ref="table"
          bordered
          rowKey="id"
          :columns="columns"
          :dataSource="dataSource"
          :pagination="ipagination"
          :loading="loading"
          :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          class="j-table-force-nowrap"
          @change="handleTableChange"
        >
          <template slot="htmlSlot" slot-scope="text">
            <div v-html="text"></div>
          </template>
          <template slot="imgSlot" slot-scope="text">
            <span v-if="!text" style="font-size: 14px">无图片</span>
            <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width: 80px; font-size: 14px" />
          </template>
          <template slot="fileSlot" slot-scope="text">
            <span v-if="!text" style="font-size: 14px">无文件</span>
            <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
              下载
            </a-button>
          </template>
          <span
            slot="action"
            slot-scope="text, record"
            class="caozuo"
            style="display: inline-block; white-space: nowrap; text-align: center"
          >
            <a @click="handleDetailPage(record)">查看</a>
            <a-divider type="vertical" />
            <a @click="handleDeleteOrder(record.id)">删除</a>
          </span>
          <template slot="tooltip" slot-scope="text">
            <a-tooltip placement="topLeft" :title="text" trigger="hover">
              <div class="tooltip">
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
      </a-card>
      <devops-order-info-modal ref="modalForm" @ok="modalFormOk"></devops-order-info-modal>
      <devops-order-info-detail-modal ref="modalDetailForm" @ok="modalFormOk"></devops-order-info-detail-modal>
      <order-info-modal ref="orderInfoForm" @ok="modalFormOk"></order-info-modal>
    </a-col>
  </a-row>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import DevopsOrderInfoModal from './modules/DevopsOrderInfoModal'
import JSuperQuery from '@/components/jeecg/JSuperQuery.vue'
import { getAction, deleteAction, postAction } from '@/api/manage'
import DevopsOrderInfoDetailModal from './modules/DevopsOrderInfoDetailModal'
import DevopsOrderInfoAllocateModal from './modules/DevopsOrderInfoAllocateModal'
import JTreeSelect from '@/components/jeecg/JTreeSelect'
import OrderInfoModal from './modules/OrderInfoModal'
import JDictSelectTag from '@/components/dict/JDictSelectTag'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'

export default {
  name: 'DevopsOrderInfoListProcessed',
  mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
  components: {
    DevopsOrderInfoDetailModal,
    DevopsOrderInfoAllocateModal,
    DevopsOrderInfoModal,
    JSuperQuery,
    JTreeSelect,
    OrderInfoModal,
    JDictSelectTag,
  },
  data() {
    return {
      description: '已处理工单',
      // 表头
      columns: [
        {
          title: '工单名称',
          dataIndex: 'orderName',
        },
        {
          title: '工单来源',
          dataIndex: 'orderSourceText',
        },
        {
          title: '故障类型',
          dataIndex: 'orderCategoryText',
        },
        {
          title: '确认人',
          dataIndex: 'confirmUserName',
        },
        {
          title: '工单状态',
          dataIndex: 'orderStateName',
        },
        {
          title: '负责人',
          dataIndex: 'handlerUserName',
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          // customRender: function (text) {
          //   return !text ? '' : text.length > 10 ? text.substr(0, 10) : text
          // },
        },
        {
          title: '分配时间',
          dataIndex: 'allocTime',
          // customRender: function (text) {
          //   return !text ? '' : text.length > 10 ? text.substr(0, 10) : text
          // },
        },
        {
          title: '响应时长(s)',
          width: 110,
          dataIndex: 'responseSecondStr',
        },
        {
          title: '办结时间',
          dataIndex: 'handleEndTime',
          // customRender: function (text) {
          //   return !text ? '' : text.length > 10 ? text.substr(0, 10) : text
          // },
        },
        {
          title: '处理时长(s)',
          width: 110,
          dataIndex: 'handleSecondStr',
        },

        {
          title: '操作',
          dataIndex: 'action',
          fixed: 'right',
          align: 'center',
          width: 147,
          scopedSlots: { customRender: 'action' },
        },
      ],

      url: {
        list: '/orderinfo/devopsOrderInfo/listProcessed',
        delete: '/orderinfo/devopsOrderInfo/delete',
        deleteBatch: '/orderinfo/devopsOrderInfo/deleteBatch',
        exportXlsUrl: '/orderinfo/devopsOrderInfo/exportXls',
        importExcelUrl: 'orderinfo/devopsOrderInfo/importExcel',
        goAddKnowledges: '/orderinfo/devopsOrderInfo/goAddKnowledges',
      },
      dictOptions: {},
      superFieldList: [],
      condition: '{"delflag": 0}',
    }
  },
  created() {
    this.getSuperFieldList()
  },
  mounted() {},
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
  },

  methods: {
    initDictConfig() {},
    //批量添加知识库
    handleAddKnowledge() {
      if (this.selectionRows.length <= 0) {
        this.$message.warning('请选择一条记录！')
        return
      } else {
        var ids = ''
        for (var a = 0; a < this.selectionRows.length; a++) {
          ids += this.selectionRows[a].id + ','
        }
        if (ids != '') {
          var that = this
          that.loading = true
          postAction(that.url.goAddKnowledges, { ids: ids })
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.loadData()
                that.onClearSelected()
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.loading = false
            })
        }
      }
    },
    handleDeleteOrder: function (id) {
      if (!this.url.delete) {
        this.$message.error('请设置url.delete属性!')
        return
      }
      var that = this
      this.$confirm({
        title: '确认删除',
        okText: '是',
        cancelText: '否',
        content: '是否删除选中数据?',
        onOk: function () {
          that.loading = true
          deleteAction(that.url.delete, { id: id }).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.loadData()
            } else {
              that.$message.warning(res.message)
            }
          })
        },
      })
    },
    onChange(date, dateString) {
      this.queryParam.handleEndTime_begin = dateString[0]
      this.queryParam.handleEndTime_end = dateString[1]
    },

    handleDetailForm(record) {
      this.$refs.orderInfoForm.edit(record)
      this.$refs.orderInfoForm.title = '详情'
      this.$refs.orderInfoForm.disableSubmit = true
    },

    getSuperFieldList() {
      let fieldList = []
      fieldList.push({ type: 'string', value: 'orderCategoryId', text: '故障类型(资源类型)', dictCode: '' })
      fieldList.push({ type: 'string', value: 'warningId', text: '告警id', dictCode: '' })
      fieldList.push({ type: 'string', value: 'confirmUserId', text: '确认人id', dictCode: '' })
      fieldList.push({ type: 'int', value: 'orderState', text: '工单状态(0:待分配,1:待处理,2已处理:)', dictCode: '' })
      fieldList.push({ type: 'date', value: 'wamingCreateTime', text: '故障触发时间' })
      fieldList.push({ type: 'string', value: 'remarks', text: '备注', dictCode: '' })
      fieldList.push({ type: 'string', value: 'handlerUserId', text: '负责人id', dictCode: '' })
      fieldList.push({ type: 'date', value: 'allocTime', text: '分配时间' })
      fieldList.push({ type: 'int', value: 'responseSecond', text: '响应时间秒', dictCode: '' })
      fieldList.push({ type: 'string', value: 'handlerResults', text: '处理结果', dictCode: '' })
      fieldList.push({ type: 'date', value: 'handleEndTime', text: '处理完成时间' })
      fieldList.push({ type: 'int', value: 'handleSecond', text: '处理时长秒', dictCode: '' })
      fieldList.push({ type: 'string', value: 'createBy', text: '创建人', dictCode: '' })
      fieldList.push({ type: 'date', value: 'createTime', text: '创建日期' })
      fieldList.push({ type: 'string', value: 'updateBy', text: '更新人', dictCode: '' })
      fieldList.push({ type: 'date', value: 'updateTime', text: '更新日期' })
      fieldList.push({ type: 'string', value: 'sysOrgCode', text: '所属部门', dictCode: '' })
      this.superFieldList = fieldList
    },
  },
}
</script>
<style lang="less" scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
.table-page-search-wrapper .ant-form-inline .ant-form-item {
  margin-bottom: 0 !important;
}
.ant-table-pagination.ant-pagination {
  margin: 16px 0 0 0 !important;
}
/*表头样式*/
::v-deep .ant-table-thead > tr > th {
  text-align: center;
  white-space: nowrap;
}

/*内容对齐方式、省略显示*/
::v-deep .ant-table-tbody > tr > td {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;

  &:first-child,
  &:nth-child(2),
  &:nth-child(3),
  &:nth-child(4),
  &:nth-child(5),
  &:nth-child(6),
  &:nth-child(7),
  &:nth-child(8),
  &:nth-child(9),
  &:nth-child(11) {
    text-align: center;
  }
  &:nth-child(10),
  &:nth-child(12) {
    text-align: right;
  }
}
</style>
