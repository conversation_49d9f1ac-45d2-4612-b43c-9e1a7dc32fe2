
class TopoConfig {
  topoName = "";//拓扑名称
  topoType ="0";//拓扑类型 "0" 网络拓扑图
  showType="0";//是否在大屏展示
  deptId = "";//单位id
  infoPopup = false;//信息弹窗
  fitPadding = 50;
  topoConfig = this.initTopoConfig();
  bgImgList = []//拓扑背景图片列表
  edgeDashArr = ["0", "5", "5, 5, 1, 5"] //连线样式集合
  edgeMarkerArr = ['不显示', "起始箭头", "目标箭头", "双箭头"]//连线箭头集合
  edgeTypeArr = ["直线","折线","三次贝尔曲线"]
  shapeTypes = ["圆形","矩形","菱形"];
  edgeProperties = [
    // {text:"起始端口状态",value:"srStatus"},
    // {text:"目标端口状态",value:"trStatus"},
    {text:"起始设备输入流量",value:"srInFlow"},
    {text:"目标设备输入流量",value:"trInFlow"},
    {text:"起始设备输出流量",value:"srOutFlow"},
    {text:"目标设备输出流量",value:"trOutFlow"}
  ];
  shapeNames=["circle-node","switch-node","diamond-node"];
  setTopoConfig(data) {
    if (data) {
      this.topoConfig = data;
    }
    else {
      this.topoConfig = this.initTopoConfig();
    }
  }
  //初始化拓扑图配置
  initTopoConfig() {
    return {
      busCode:"",//自动应用拓扑图关联的应用
      labelColor: "#080808",
      edgeMarker: 0,
      edgeType: 0,
      edgeDash: "0",//字符串
      edgeProperties:[],
      edgeStatus: true,
      edgeAni:false,
      aniTime:4,
      bgType: "1",
      bgColor: '#f8f8f8',
      bgimg: "",
      shapeType:0,
      maxX:"",
      minX:"",
      maxY:"",
      minY:"",
      zoomFit: true,
      nodeBgColor: 'rgba(95,149,255,0.05)',
    }
  }
}

export const globalGridAttr = new TopoConfig();