<template>
<!--  <div class='top-wrapper' style='height: calc(100% - 12px);width: 100%;overflow: hidden;overflow-x:auto;position: relative'>-->
  <div class='top-wrapper'>
<!--    <div class="weeks" style='width: calc(100% - 8px);min-width: 1016px'>-->
    <div class="weeks" style='width: calc(100% - 12px)'>
      <span class="week" v-for="dayIndex in 7" :key="dayIndex">
        {{ dayIndex | localeWeekDay }}
      </span>
    </div>
<!--    <div class="c-container" ref='c-container' style='  min-width: 1024px;width: 100% !important;'>-->
    <div class="c-container" ref='c-container'>
      <div class='date' style='height: 100%'>
        <div v-for="(obj, j) in days" :key="j" class='row'>
          <!--每天时间展示-->
          <div class="c-header">
            <div
              class="c-h-cell"
              v-for="(day, i) in obj.dayArr"
              :class="{ 'other-m': isOtherMonth(day), today: isToday(day),isWeekend:isWeekend(day)}"
              :key="i"
              :style="showDialog?'height:'+layDefaultHeight+'px'+';minHeight:'+(obj.bgMinHeight*layHeight+30) + 'px':'cursor:pointer;height:'+layDefaultHeight+'px'+';minHeight:'+(obj.bgMinHeight * layHeight + 30) + 'px'"
              @click="addNewEvent(day)"
            >
              <div style="display: flex">
                <div>
                  <span class="cell-festival" v-if="day.holiday==='1'">班</span>
                  <span class="cell-festival" style="color:red" v-if="day.holiday==='0'">休</span>
                </div>
                <span class="cell-day"  :class="{isRed:isRed(day)}">{{ day.date }}</span>
              </div>
              <span v-if="day.holiday==='0'" class="cell-name">{{ day.name }}</span>
            </div>
          </div>
          <!--数据展示-->
          <div style="position: relative">
            <div
              class="event-container"
              :style="{ top: '-' + obj.bgMinHeight * layHeight + 'px' }"
            >
              <div
                :style="{
                width: 0,
                minHeight: obj.bgMinHeight * layHeight + 'px',
              }"
              ></div>
              <div
                class="event-item"
                v-for="(n, i) in obj.weekEventList"
                :key="i"
                :style="{
                width: n._eLen * layWidth + 'px',
                height: layHeight + 'px',
                left: (n._eX * layWidth)+ 'px',
                top: n._eY * layHeight + 'px',
              }"
            >
              <div v-if="showDialog">
                <a-popover>
                  <template slot="content">
                    <div style="display: flex">
                      <div style="margin-top: 6px">
                        {{ n.data.title }}值班详情
                      </div>
                      <a-button v-if="showLook&&n.data.time>=n.data.newTime" @click="deleteNewEvent(n.data.id)" type="link" style="margin-left: 12px;color: red">
                        删除
                      </a-button>
                    </div>
                    <a-divider style="margin: 6px 0;"/>
                    <div class="e-popover-body">
                      <div>
                        <p class="e-p-user"><label class="label">班次名称：</label><span>{{ n.data.shiftName }}</span></p>
                        <p class="e-p-user"><label class="label">当前日期：</label><span>{{ n.data.startTime }}</span></p>
                        <p class="e-p-user"><label class="label">时间范围：</label><span>{{ n.data.date }}</span></p>
                      </div>
                    </div>
                  </template>
                  <div

                    class="event-content"
                    :class="n.className"
                    :title="n.data.title"
                    :style="{
                  background: n.data.planColor,
                  height: layHeight - 2 + 'px',
                  lineHeight: layHeight - 2 + 'px',
                }">
                      <div class="event-co" v-if="n.className.includes('is-start')"  @click="editNewEvent(n)">
                        <span class="event-text">{{ n.data.title }}</span>
                        <span class="event-text" style="float: right;padding-right: 10px;">{{ n.data.shiftName}}</span>
                      </div>

                    </div>
                  </a-popover>
                </div>
                <div v-else
                     class="event-content"
                     :class="n.className"
                     :title="n.data.title"
                     :style="{
                  background: n.data.planColor,
                  height: layHeight - 2 + 'px',
                  lineHeight: layHeight - 2 + 'px',
                }"
                     @click="editNewEvent(n)"
                >
                  <div class="event-co" v-if="n.className.includes('is-start')">
                    <span class="event-text">{{ n.data.title }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: "MonthStyle",
  props: {
    days: {
      default: () => [],
    },
    currentMonth: {
      default: 1
    },
    currentYear: {
      default: 1970
    },
    layHeight: {
      default: 0
    },
    showDialog: false,
    showLook: false,
  },
  data() {
    return {
      layWidth: 0,
      layDefaultHeight: 0,
    };

  },
  filters: {
    localeWeekDay(weekday) {
      let map = {
        7: "周日",
        1: "周一",
        2: "周二",
        3: "周三",
        4: "周四",
        5: "周五",
        6: "周六",
      };
      return map[weekday];
    },
  },
  created() {

  },
  mounted() {
    this.$nextTick(() => {
      let that = this
      if (that.$refs['c-container']) {
        this.computedSize()
        window.addEventListener('resize', this.getAreaWidth)
      }
    })
  },
  //注销window.onresize事件
  beforeDestroy() {
    window.removeEventListener('resize', this.getAreaWidth)
  },
  methods: {
    getAreaWidth() {
      let th = setTimeout(() => {
          this.$nextTick(() => {
            this.computedSize()
          })
        }
        , 1000)
    },
    computedSize() {
      let that = this
      let width = that.$refs['c-container'].offsetWidth
     /* if ((width - 8) > 1016) {
        that.layWidth = (width - 8) / 7
      } else {
        that.layWidth = 1016 / 7
      }*/
      that.layWidth = (width - 14) / 7

      that.layDefaultHeight = (that.$refs['c-container'].offsetHeight - 1) / 6
    },
    isOtherMonth(item) {
      return !(item.month === this.currentMonth && item.year === this.currentYear);
    },
    isWeekend(item){
      return item.holiday === '0';
    },
    isRed(item){
      var dt = new Date(item.dateStr);
      var weekDay = [7,1,2,3,4,5,6];
      return item.holiday === '0' || ((weekDay[dt.getDay()] === 7 || weekDay[dt.getDay()] === 6)) && item.holiday !== '1';
    },
    isToday(item) {
      let d = new Date();
      return item.month === d.getMonth() + 1 &&
        item.date === d.getDate() &&
        item.year === d.getFullYear();
    },
    //添加
    addNewEvent(day) {
      this.$emit('addNewEvent', day)
    },
    //编辑
    editNewEvent(data) {
      // console.log("编辑事件",data)
      this.$emit('editNewEvent',data)
    },
    deleteNewEvent(id) {
      this.$emit('deleteNewEvent', id)
    }
  },
};
</script>
<style lang="less" scoped>
/*
ul {
  padding: 0;
  margin: 0;
}

li {
  list-style: none;
}
*/

/*.event-calender {
  width: 100%;
  margin: 0 auto;
}*/
.top-wrapper{
  width: 100%;
  position: relative;
  @media (max-width: 600px){
    height: calc(100% + 32px);
  }
  @media (min-width: 600px){
    height: 100%;
  }
}
.weeks {
  position: absolute;
  display: flex;
  border: 1px solid #e1e4e7;
  border-left: none;
}

.week {
  height: 50px;
  line-height: 50px;
  flex: 1;
  text-align: center;
  border-left: 1px solid #e1e4e7;
  background: rgb(245, 248, 250);
}

.other-m {
  color: rgba(51, 71, 91, 0.45);
  background: rgb(245, 245, 245);
}
.isWeekend{
  background: #f3b2b347;
}
.isRed{
  color: red;
}

.today {
  background-color: #fcf8e3;
}
.event-container {
  width: 100%;
  box-sizing: border-box;
  position: absolute;
  height: 0;

  .event-item {
    box-sizing: border-box;
    padding-top: 2px;
    position: absolute;

    .event-content {
      color: #FFFFFF;
      cursor: pointer;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      word-break: break-all;
      padding-left: 10px;

      &.is-start {
        margin-left: 1px;
        border-top-left-radius: 12px;
        border-bottom-left-radius: 12px;
      }

      &.is-end {
        border-top-right-radius: 12px;
        border-bottom-right-radius: 12px;
      }

      .event-text {
        padding-left: 5px;
      }

      .event-co {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        word-break: break-all;
      }
    }
  }
}

.c-container {
  border: none;
  border-left: 1px solid #eee;
  border-bottom: 1px solid #eee;
  position: absolute;
  top: 52px;
  //min-width: 1024px;
  width:100%;
  height: calc(100% - 52px) !important;
  overflow: hidden;
  overflow-y: scroll;

  .date {
    margin-right: 0px;
    .row {
      .c-header {
        display: flex;

        .c-h-cell {
          box-sizing: border-box;
          text-align: right;
          padding-right: 15px;
          flex: 1;
          border-top: 1px solid #e1e4e7;
          border-right: 1px solid #e1e4e7;

          &:last-child {
            //border-right: none;
          }
        }

        .cell-day {
          display: inline-block;
          width: 100%;
          font-size: 16px;
        }

        .cell-festival {
          display: inline-block;
          font-size: 16px;
          color: red;
        }

        .cell-name {
          font-size: 10px;

        }
      }

      &:first-child {
        .c-header {
          .c-h-cell {
            border-top: none;
          }
        }
      }
    }
  }
}
.event-bg {
  position: relative;
  display: flex;

  .bg-cell {
    box-sizing: border-box;
    flex: 1;
    border-top: 1px solid #e1e4e7;
    border-right: 1px solid #e1e4e7;

    &:last-child {
      border-right: none;
    }
  }
}

h5 {
  margin: 20px 0;
  line-height: 22px;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.80);
}

.e-p-time {
  line-height: 22px;
  font-size: 16px;
  color: rgba(51, 71, 91, 0.80);
}

</style>

