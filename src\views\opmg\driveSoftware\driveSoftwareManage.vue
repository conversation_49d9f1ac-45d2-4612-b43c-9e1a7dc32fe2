<template>
  <div style="height:100%">
    <component :is="pageName" :data="data" />
  </div>
</template>
<script>
  import driveSoftwareList from './driveSoftwareList'
  import driveSoftwareDetails from './modules/driveSoftwareDetails'
  export default {
    name: "driveSoftwareManage",
    data() {
      return {
        isActive: 0,
        data: {}
      };
    },
    components: {
      driveSoftwareList,
      driveSoftwareDetails
    },
    created() {
      this.pButton1(0);
    },
    //使用计算属性
    computed: {
      pageName() {
        switch (this.isActive) {
          case 0:
            return "driveSoftwareList";
            break;

          default:
            return "driveSoftwareDetails";
            break;
        }
      }
    },
    methods: {
      pButton1(index) {
        this.isActive = index;
      },
      pButton2(index, item) {
        this.isActive = index;
        this.data = item;
      }
    }
  }
</script>