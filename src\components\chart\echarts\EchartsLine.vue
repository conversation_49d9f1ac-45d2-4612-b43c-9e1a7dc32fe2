<template>
  <div style='width: 100%; height: 100%'>
    <div v-show='!isEmpty' :id='id' :style='{width: width, height:height}'></div>
    <a-empty v-if='isEmpty' style='width: 100%; height: 100%'></a-empty>
  </div>
</template>

<script>
/*环形图表*/
import echarts from 'echarts'
import { getAction } from '@api/manage'
import cloneDeep from 'lodash/cloneDeep'
import merge from 'lodash/merge'
export default {
  name: 'EchartsLine',
  props: {
    id: {
      type: String,
      default: 'echartsPie'
    },
    url: {
      type: String,
      default: ''
    },
    chartData: {
      type: Array,
      default: () => {
        return []
      }
    },
    xAxisData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    colors: {
      type: Array,
      default: () => {
        return [ '#73c0de', '#3ba272','#fc8452', '#9a60b4','#ee6666', '#fac858', '#ea7ccc','#5ab1ef', '#b6a2de', '#67e0e3', '#2ec7c9','#5470c6','#91cc75']
      }
    },
    option: {
      type: Object,
      default: () => ({})
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    },
    areaOpcity: { //区域透明度 0-1
      type: Number,
      default: 0
    },
  },
  data() {
    return {
      chart: null,
      sourceData: [],
      chartOption:{},
    }
  },
  created() {
    //融合个性化配置；
   merge(this.chartOption,{
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          lineStyle: {
            width: 1,
            color: '#019680'
          }
        }
      },
      xAxis: [{
        type: 'category',
        boundaryGap: false,
        data: [],
        legend: {
          top: 30,
          type:'scroll',
        },
        splitLine: {
          show: false,
          lineStyle: {
            width: 1,
            type: 'solid',
            color: 'rgba(226,226,226,0.5)'
          }
        },
        axisTick: {
          show: false
        }
      }],
     yAxis: [
       {
         type: 'value',
         splitNumber: 4,
         axisTick: {
           show: false
         },
         splitLine: {
           show: true,
           lineStyle: {
             width: 1,
             type: 'solid',
             color: 'rgba(225,225,255,0.5)'
           }
         },
         splitArea: {
           show: false,
           areaStyle: {
             color: ['rgba(255,255,255,0.2)', 'rgba(226,226,226,0.2)']
           }
         }
       }
     ],
      grid: { left: '1%', right: '1%', top: '30', bottom: 0, containLabel: true, },
      legend: {
        top:0
      }
    }, this.option)
  },
  mounted() {
    if (this.url) {
      this.getChartData()
    } else {
      this.sourceData = this.chartData
      if (!this.isEmpty) {
        this.$nextTick(() => {
          this.initChart(this.chartData)
        })
      }
    }
  },
  computed: {
    isEmpty() {
      return this.sourceData.length === 0
    }
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    window.removeEventListener('resize',  this.changeResize)
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    getChartData() {
      getAction(this.url).then((res) => {
        if (res.success) {
          this.initChart()
        }
      })
    },
    initChart(data) {
      let el = document.getElementById(this.id)
      if (el === null) return
      this.chart = echarts.init(el)
      if (this.chart === null) return
      let seriesData = []
      for (let i = 0; i < data.length; i++) {
        let obj = {
          smooth: true,
          data: [],
          type: 'line',
          areaStyle: {
             opacity: 0,
          },
          itemStyle: {
            color: this.colors[i]
          }
        }
        obj = merge(obj,data[i])
        seriesData.push(obj)
      }
      this.chartOption.series = seriesData;
      this.chart.setOption(this.chartOption)
      window.addEventListener('resize', this.changeResize)
    },
    changeResize(){
      if (this.chart){
        this.chart.resize()
      }
    },
    refresh(data) {
      let seriesData = [];
      for (let i = 0; i < data.length; i++) {
        let obj = {
          smooth: true,
          data: [],
          type: 'line',
          itemStyle: {
            color: this.colors[i]
          }
        }
        obj = merge(obj,data[i])
        seriesData.push(obj)
      }
      this.chart.setOption({
        series: seriesData
      })
    }
  }
}
</script>

<style lang='less' scoped>

</style>