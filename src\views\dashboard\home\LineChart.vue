<template>
	<div>
		<div ref="lineChart" :style="chartStyle"></div>
	</div>
</template>

<script>
	import _ from 'lodash';
	export default {
		name: 'LineChart',
		props: {
			//图表样式，默认高度为300px
			chartStyle: {
				type: Object,
				default: function() {
					return {
						// width: '100%',
						// height: '300px'
					}
				},
			},
			//图表配置
			chartOption: {
				type: Object,
				default: function() {
					return {
						xAxis: {
							data: []
						},
						series: [
              {
                name: '正常',
                type: 'line',
                areaStyle: {
                  color:'#c8b5e680',
                },
                data: [],
                smooth: true,
                itemStyle : {
                  normal : {
                    color:'#AD92D9',
                    lineStyle:{
                      color:'#AD92D9',
                      width:1
                    }
                  }
                },
              },
              {
                name: '违反SLA',
                type: 'line',
                areaStyle: {
                  color:'#8aceff80',
                },
                data: [],
                smooth: true,
                itemStyle : {
                  normal : {
                    color:'#0E9CFF',
                    lineStyle:{
                      color:'#0E9CFF',
                      width:1
                    }
                  }
                },
              },
            ]
					}
				},
			},
			//图表数据
			/* chartData: {
				type: Array,
				default: function () {
					return []
				},
			}, */
			//是否替换配置项（是：完全替换配置项；否：合并配置项）
			isReplaceOption: {
				type: Boolean,
				default: false
			},
			chartChange: {
				type: Boolean,
				default: false
			},
		},
		components: {},
		watch: {
			chartChange() {
				this.drawBar();
			},

		},

		data() {
			return {
				myChart: null,
				chartOptions: {
					tooltip: {
						trigger: 'axis',
					},
					legend: {},
					// toolbox: {
					// 	feature: {
					// 			dataZoom: {
					// 					yAxisIndex: 'none'
					// 			},
					// 			restore: {},
					// 			saveAsImage: {}
					// 	}
					// },
					dataZoom: [{
						type: 'inside',
						start: 0,
						end: 100
					}],
					grid: {
						left: '3%',
						right: '14%',
						// bottom: '10%',
						// containLabel: true
					},
					xAxis: {
						name: '时间',
						type: 'category',

					},
					yAxis: {
						name: '数量',
						type: 'value',
					},
					series: [

					]
				},

			}
		},
		created() {
			_.debounce(function() {
				//延迟创建图表，解决echarts参数未定义的问题
				this.drawBar();
			}, 100)
		},
		mounted() {
			const that = this
			window.addEventListener('resize', function() {
				that.myChart.resize()
			})
		},
		computed: {},
		methods: {
			drawBar() {
				if (!this.myChart) {
					this.myChart = this.$echarts.init(this.$refs.lineChart);
				}
				this.myChart.clear();
				let option = {};
				if (this.isReplaceOption) {
					option = this.chartOption;
				} else {
					option = _.merge({}, this.chartOptions, this.chartOption);
				}
				this.myChart.setOption(option);
			}
		}

	}
</script>

<style>
</style>
