<template>
  <a-row :gutter='10' class='vScroll zxw' style='height: 100%'>
    <a-col style='width: 100%; height: 100%; display: flex; flex-direction: column'>
      <!-- 查询区域 -->
      <a-card :bodyStyle="{ paddingBottom: '0' }" :bordered='false' class='card-style'>
        <div class='table-page-search-wrapper-style'>
          <a-form layout='inline' v-bind='formItemLayout' @keyup.enter.native='searchQuery'>
            <a-row ref='row' :gutter='24'>
              <a-col :span='spanValue'>
                <a-form-item label='任务名称'>
                  <a-input
                    :maxLength='maxLength'
                    v-model='queryParam.name'
                    :allowClear='true'
                    autocomplete='off'
                    placeholder='请输入任务名称'
                  ></a-input>
                </a-form-item>
              </a-col>

              <a-col :span='colBtnsSpan()'>
                <span
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                  class='table-page-search-submitButtons'
                >
                  <a-button class='btn-search-style' type='primary' @click='searchQuery'>查询</a-button>
                  <a-button class='btn-reset-style' style='margin-left: 10px' @click='searchReset'>重置</a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>

      <a-card :bordered='false' class='core' style='flex: auto'>
        <a-row class='lastBtn2'>
          <div class='table-operator'>
            <a-button @click='handleAdd'>新增</a-button>
<!--            <a-dropdown v-if='selectedRowKeys.length > 0'>
              <a-menu slot='overlay' style='text-align: center'>
                <a-menu-item key='1' @click='batchDel'>删除</a-menu-item>
              </a-menu>
              <a-button> 批量操作
                <a-icon type='down' />
              </a-button>
            </a-dropdown>-->
          </div>
        </a-row>
        <!-- table区域-begin -->
<!--        <a-table
          ref='table'
          bordered
          rowKey='id'
          :columns='columns'
          :dataSource='dataSource'
          :scroll='dataSource.length>0?{x:"max-content"}:{}'
          :pagination='ipagination'
          :loading='loading'
          :rowSelection='{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }'
          @change='handleTableChange'
        >-->
        <a-table
          ref='table'
          :columns='columns'
          :dataSource='dataSource'
          :loading='loading'
          :pagination='ipagination'
          :scroll='dataSource.length>0?{x:"max-content"}:{}'
          bordered
          rowKey='id'
          @change='handleTableChange'
        >
          <template slot='htmlSlot' slot-scope='text'>
            <div v-html='text'></div>
          </template>
          <template slot='imgSlot' slot-scope='text'>
            <span v-if='!text' style='font-size: 14px'>无图片</span>
            <img v-else :src='getImgView(text)' alt='' height='25px' style='max-width: 80px; font-size: 14px' />
          </template>
          <template slot='fileSlot' slot-scope='text'>
            <span v-if='!text' style='font-size: 14px'>无文件</span>
            <a-button v-else :ghost='true' icon='download' size='small' type='primary' @click='downloadFile(text)'>
              下载
            </a-button>
          </template>
          <template slot='index' slot-scope='text,record,index'>
            <span>{{ index + 1 }}</span>
          </template>
          <span slot='action' slot-scope='text, record'>
            <a v-if='record.state==="已启动"' @click='pauseJob(record)'>停止</a>
            <a v-else @click='resumeJob(record)'>启动</a>
            <a-divider type='vertical' />
            <a-dropdown>
              <a class='ant-dropdown-link'>更多 <a-icon type='down' /></a>
              <a-menu slot='overlay'>
                <a-menu-item><a @click='executeImmediately(record)'>执行一次</a></a-menu-item>
                <a-menu-item><a @click='openHistory(record)'>执行历史</a></a-menu-item>
                <a-menu-item v-if='record.state!=="已启动"'><a @click='handleEdit(record)'>编辑</a></a-menu-item>
                <a-menu-item><a @click='confirmDelete(record.id)'>删除</a></a-menu-item>
              </a-menu>
            </a-dropdown>
        </span>

          <template slot='tooltip' slot-scope='text'>
            <a-tooltip :title='text' placement='topLeft' trigger='hover'>
              <div class='tooltip'>
                {{ text }}
              </div>
            </a-tooltip>
          </template>
          <!-- 状态渲染模板 -->
          <span slot='strategyStatus' slot-scope='text'>
            <a-tag v-if="text==='已暂停'" color="orange">{{text}}</a-tag>
            <a-tag v-else-if="text==='已启动'||text==='成功'||text==='执行中'" color="green">{{text}}</a-tag>
            <!--失败-->
            <a-tag v-else color="red">{{text}}</a-tag>
          </span>
        </a-table>
      </a-card>
      <transitionTaskModal ref='modalForm' :rules='rules' :sources='sources' @ok='modalFormOk'></transitionTaskModal>
      <transitionHistorykModal ref='historyModal'></transitionHistorykModal>
    </a-col>
  </a-row>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import transitionTaskModal from './modules/transitionTaskModal.vue'
import transitionHistorykModal from './modules/transitionHistorykModal.vue'
import ViewConfigModal from '@views/networkManagement/networkDevice/modules/ViewConfigModal.vue'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
import { getAction, deleteAction, putAction, postAction, postParamsAction } from '@/api/manage'
import { taskData } from '@views/distribution/transitionTask/mock/taskData'
import transitionTaskForm from '@views/distribution/transitionTask/modules/transitionTaskForm.vue'

export default {
  name: 'RestoreStrategy',
  mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
  components: {
    transitionTaskForm,
    transitionTaskModal,
    ViewConfigModal,
    transitionHistorykModal
  },
  data() {
    return {
      maxLength:50,
      description: '分布式迁移任务管理',
      // 表头
      columns: [
        {
          title: '序号',
          dataIndex: 'index',
          scopedSlots: {
            customRender: 'index'
          },
          customCell: () => {
            let cellStyle = 'width:60px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '任务名称',
          dataIndex: 'name'
        },
        {
          title: '迁移规则',
          dataIndex: 'transferRuleId',
          customRender: (text) => {
            return this.rules.find(el => el.value === text)?.label
          }
        }, {
          title: '执行时间',
          dataIndex: 'executeCron'
        },
        {
          title: '源数据库',
          dataIndex: 'sourceDatabaseId',
          customRender: (text) => {
            return this.sources.find(el => el.value === text)?.label
          }
        }, {
          title: '源bucket桶',
          dataIndex: 'sourceDatabaseBucket'
        },
        {
          title: '目标数据库',
          dataIndex: 'targetDatabaseId',
          customRender: (text) => {
            return this.sources.find(el => el.value === text)?.label
          }
        }, {
          title: '目标bucket桶',
          dataIndex: 'targetDatabaseBucket'
        },
        {
          title: '状态',
          align: 'center',
          dataIndex: 'state',
          scopedSlots: { customRender: 'strategyStatus' }
        },
        {
          title: '操作',
          dataIndex: 'action',
          fixed: 'right',
          align: 'center',
          width: 180,
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: '/distributedStorage/transfer/job', // 任务列表
        delete: '/distributedStorage/transfer/job', // 删除任务
        deleteBatch: '/distributedStorage/transfer/job/batch', // 批量删除任务
        exportXlsUrl: '',
        importExcelUrl: '',
        pause: '/distributedStorage/transfer/job/stop',  // 禁用
        resume: '/distributedStorage/transfer/job/start', // 启用
        execute: '/distributedStorage/transfer/job/exec' // 执行
      },
      dictOptions: {},
      disableMixinCreated: false,
      rules: [],
      sources: []
    }
  },
  created() {
    this.getRuleList()
    this.getSourceList()
  },
  mounted() {
  },
  computed: {
    importExcelUrl: function() {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    }
  },
  methods: {
    // 获取规则列表
    getRuleList() {
      getAction('/distributedStorage/transfer/allrule').then((res) => {
        if (res.success) {
          this.rules = res.result.map(el => {
            return { label: el.name, value: el.id }
          })
        }
      })
    },
    //获取数据源列表
    getSourceList() {
      getAction('/distributedStorage/transfer/dasource/all').then((res) => {
        if (res.success) {
          this.sources = res.result.map(el => {
            return { label: el.name, value: el.id }
          })
        }
      })
    },
    /**弹窗确认删除*/
    confirmDelete: function (id) {
      if (!this.url.delete) {
        this.$message.error('请设置url.delete属性!')
        return
      }
      var that = this
      that.$confirm({
        title: '确认删除',
        okText: '是',
        cancelText: '否',
        content: '是否删除数据?',
        onOk: function () {
          that.loading = true
          deleteAction(that.url.delete, { jobId: id }).then((res) => {
            if (res.success) {
              //重新计算分页问题
              that.reCalculatePage(1)
              that.$message.success(res.message)
              that.loadData()
            } else {
              that.$message.warning(res.message)
            }
            that.loading = false
          }).catch((err)=>{
            that.$message.warning(err.message)
            that.loading = false
          })
        }
      })
    },
    batchDel: function() {
      if (!this.url.deleteBatch) {
        this.$message.error('请设置url.deleteBatch属性!')
        return
      }
      if (this.selectedRowKeys.length <= 0) {
        this.$message.warning('请选择一条记录！')
        return
      } else {
        var ids = ''
        for (var a = 0; a < this.selectedRowKeys.length; a++) {
          ids += this.selectedRowKeys[a] + ','
        }
        var that = this
        this.$confirm({
          title: '确认删除',
          okText: '是',
          cancelText: '否',
          content: '是否删除选中数据?',
          onOk: function() {
            that.loading = true
            deleteAction(that.url.deleteBatch, { jobIds: ids })
              .then((res) => {
                if (res.success) {
                  //重新计算分页问题
                  that.reCalculatePage(that.selectedRowKeys.length)
                  that.$message.success(res.message)
                  that.loadData()
                  that.onClearSelected()
                } else {
                  that.$message.warning(res.message)
                }
              })
              .finally(() => {
                that.loading = false
              })
          }
        })
      }
    },
    pauseJob: function(record) {
      var that = this
      //禁用定时任务
      let params = {jobId: record.id }
      this.$confirm({
        title: '确认停止',
        okText: '是',
        cancelText: '否',
        content: '是否停止选中任务?',
        onOk: function() {
          postParamsAction(that.url.pause, params).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.loadData()
              that.onClearSelected()
            } else {
              that.$message.warning(res.message)
            }
          })
        }
      })

    },
    resumeJob: function(record) {
      var that = this
      //恢复定时任务
      let params = {jobId: record.id}
      this.$confirm({
        title: '确认启动',
        okText: '是',
        cancelText: '否',
        content: '是否启动选中任务?',
        onOk: function() {
          postParamsAction(that.url.resume, params).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.loadData()
              that.onClearSelected()
            } else {
              that.$message.warning(res.message)
            }
          })
        }
      })
    },
    executeImmediately(record) {
      var that = this
      //立即执行定时任务
      let params = {jobId: record.id}
      this.$confirm({
        title: '确认提示',
        okText: '是',
        cancelText: '否',
        content: '是否立即执行任务?',
        onOk: function() {
          postParamsAction(that.url.execute, params).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.loadData()
              that.onClearSelected()
            } else {
              that.$message.warning(res.message)
            }
          })
        }
      })
    },
    openHistory(record) {
      this.$refs.historyModal.open(record)
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
</style>
