<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container>
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-item label="子网组名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['subnetGroupName', validatorRules.subnetGroupName]" :allowClear="true"
                autocomplete="off" placeholder="请输入子网组名称"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="子网组简称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['subnetGroupNickname', validatorRules.subnetGroupNickname]" :allowClear="true"
                autocomplete="off" placeholder="请输入子网组简称"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="使用部门" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-select-depart v-model="model.departId"></j-select-depart>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="使用位置" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['location',validatorRules.location]" :allowClear="true" autocomplete="off"
                placeholder="请输入使用位置"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="备注" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-textarea :autoSize="{ minRows: 1, maxRows: 4 }" v-decorator="['remark',validatorRules.remark]" :allowClear="true" autocomplete="off"
                placeholder="请输入备注"></a-textarea>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
  import {
    httpAction,
    getAction
  } from '@/api/manage'
  import pick from 'lodash.pick'
  import JFormContainer from '@/components/jeecg/JFormContainer'
  import JSelectDepart from '@/components/jeecgbiz/JSelectDepart'
  import JDictSelectTag from '@/components/dict/JDictSelectTag'

  export default {
    name: 'DevopsBackupProForm',
    components: {
      JFormContainer,
      JDictSelectTag,
      JSelectDepart
    },
    data() {
      return {
        form: this.$form.createForm(this),
        model: {},
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          },
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          },
        },
        confirmLoading: false,
        backupProId: '',
        validatorRules: {
          subnetGroupName: {
            rules: [{
              required: true,
              message: '请输入子网组名称'
            }, {
              min: 2,
              max: 50,
              message: '子网组名称长度应在2-50个字符之间'
            }],
          },
          subnetGroupNickname: {
            rules: [{
              required: true,
              message: '请输入子网组简称'
            }, {
              min: 2,
              max: 20,
              message: '子网组简称长度应在2-20个字符之间'
            }],
          },
          location: {
            rules: [{
              max: 50,
              message: '使用位置长度不超过50字符'
            }]
          },
          remark: {
            rules: [{
              max: 200,
              message: '备注长度不超过200字符'
            }]
          },
        },
        url: {
          add: '/devops/ip/subnetGroup/add',
          edit: '/devops/ip/subnetGroup/edit',
        },
      }
    },
    created() {},
    methods: {
      add() {
        this.edit({})
      },
      edit(record) {
        this.backupProId = record.id
        this.form.resetFields()
        this.model = Object.assign({}, record)
        this.visible = true
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.model, 'subnetGroupName', 'subnetGroupNickname',
            'location', 'remark'))
        })
      },
      submitForm() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.model.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'put'
            }
            let formData = Object.assign(this.model, values)
            httpAction(httpurl, formData, method)
              .then((res) => {
                if (res.success) {
                  that.$message.success(res.message)
                  that.$emit('ok')
                } else {
                  that.$message.warning(res.message)
                }
              })
              .finally(() => {
                that.confirmLoading = false
              })
          }
        })
      },
      popupCallback(row) {
        this.form.setFieldsValue(pick(row, 'subnetGroupName', 'subnetGroupNickname', 'location', 'remark'))
      },
    },
  }
</script>