<template>
  <card-frame :title='"告警级别趋势分析"' :sub-title='""'>
    <div slot='bodySlot' style="height: 100%;width: 100%;">
      <div class="daySelect">
        <div>
          <!-- <span :class="buttonClass" @click="alarmChange" v-show="!lineMonthJudge">近7天</span> -->
          <span :class="buttonClass" @click="alarmChange" v-show="!lineMonthJudge">近{{day1}}天</span>
          <span :class="buttonClass1" @click="alarmChange1" v-show="!lineMonthJudge"
            style="margin-left: 20px;">近{{day2}}天</span>
          <span :class="buttonClass2" @click="alarmChange2" v-show="lineMonthJudge">近半年</span>
          <span :class="buttonClass3" @click="alarmChange3" v-show="lineMonthJudge"
            style="margin-left: 20px;">近一年</span>
        </div>
        <div style="margin-right:5px;color: rgb(209, 206, 206)">
          <span style="letter-spacing: 1px;margin-right: 15px;">单位:</span>
          <span :class="lineDayStyle" @click="lineDayChange">天</span>
          <span :class="lineMonthStyle" @click="lineMonthChange" style="margin-left: 20px;">月</span>
        </div>
      </div>
      <div style="height: calc(100% - 40px);width: 100%;" id="warningTrendLineChart"></div>
    </div>
  </card-frame>
</template>
<script>
  import echarts from 'echarts/lib/echarts'
  import cardFrame from '@views/statsCenter/com/cardFrame.vue'
  import {
    getAction
  } from '@/api/manage'
  export default {
    components: {
      cardFrame
    },
    props: {
      day1: {
        type: String,
        default: ''
      },
      day2: {
        type: String,
        default: ''
      },
    },
    data() {
      return {
        lineMonthJudge: false,
        buttonClass: 'button_style',
        buttonClass1: 'button_style',
        buttonClass2: 'button_style',
        buttonClass3: 'button_style',
        lineDayStyle: 'button_style',
        lineMonthStyle: 'button_style',
      };
    },
    mounted() {
      this.lineDayChange()
    },
    methods: {
      lineDayChange() {
        this.lineMonthJudge = false
        this.lineDayStyle = 'button_style1'
        this.lineMonthStyle = 'button_style'
        this.alarmChange()
      },
      lineMonthChange() {
        this.lineMonthJudge = true
        this.lineDayStyle = 'button_style'
        this.lineMonthStyle = 'button_style1'
        this.alarmChange2()
      },
      alarmChange() {
        this.$emit('alarmTrend', this.day1)
        this.buttonClass = 'button_style1'
        this.buttonClass1 = 'button_style'
        this.buttonClass2 = 'button_style'
        this.buttonClass3 = 'button_style'

      },
      alarmChange1() {
        this.$emit('alarmTrend', this.day2)
        this.buttonClass = 'button_style'
        this.buttonClass1 = 'button_style1'
        this.buttonClass2 = 'button_style'
        this.buttonClass3 = 'button_style'
      },
      alarmChange2() {
        this.$emit('alarmTrend1', 6)
        this.buttonClass = 'button_style'
        this.buttonClass1 = 'button_style'
        this.buttonClass2 = 'button_style1'
        this.buttonClass3 = 'button_style'
      },
      alarmChange3() {
        this.$emit('alarmTrend1', 12)
        this.buttonClass = 'button_style'
        this.buttonClass1 = 'button_style'
        this.buttonClass2 = 'button_style'
        this.buttonClass3 = 'button_style1'
      },
      // 告警趋势分析折线图
      warningTrendLineChart(time, title, value, color) {
        let serveList = []
        value.forEach((ele, i) => {
          serveList.push({
            name: ele.name,
            type: 'line',
            symbol: 'none', // 默认是空心圆（中间是白色的），改成实心圆
            smooth: true,
            lineStyle: {
              normal: {
                width: 2,
                color: `rgba(${ i<4 ? color[i] : ele.color})`, // 线条颜色
              },
              borderColor: `rgba(${ i<4 ? color[i] : ele.color})`,
            },
            itemStyle: {
              color: `rgba(${ i<4 ? color[i] : ele.color})`,
              borderColor: `rgba(${ i<4 ? color[i] : ele.color})`,
              borderWidth: 2,
            },
            tooltip: {
              show: true,
            },
            areaStyle: {
              //区域填充样式
              normal: {
                //线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是‘true’，则该四个值是绝对像素位置。
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [{
                      offset: 0,
                      color: `rgba(${ i<4 ? color[i] : ele.color},.6)`,
                    },
                    {
                      offset: 1,
                      color: `rgba(${ i<4 ? color[i] : ele.color},0)`,
                    },
                  ],
                  false
                ),
                shadowColor: `rgba(${ i<4 ? color[i] : ele.color},.2)`, //阴影颜色
                shadowBlur: 20, //shadowBlur设图形阴影的模糊大小。配合shadowColor,shadowOffsetX/Y, 设置图形的阴影效果。
              },
            },
            data: ele.data,
          })
        })
        let myChart = this.$echarts.init(document.getElementById('warningTrendLineChart'))
        myChart.setOption({
          tooltip: {
            show: true,
            trigger: 'axis',
            transitionDuration: 0, //echart防止tooltip的抖动
          },
          legend: {
            left: 'right',
            data: title,
            icon: 'rect',
            itemWidth: 9,
            itemHeight: 2,
            textStyle: {
              color: 'rgba(250,250,250,.8)',
              padding: [2, 5, 0, 0]
            },
          },
          grid: {
            top: '20%',
            left: '10%',
            right: '5%',
            bottom: '20%',
          },
          xAxis: [{
            type: 'category',
            boundaryGap: false,
            axisLine: {
              //坐标轴轴线相关设置。数学上的x轴
              show: true,
              lineStyle: {
                color: '#1f313d',
              },
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: 'rgba(250,250,250,.6)', //更改坐标轴文字颜色
                fontSize: 12,
              },
            },
            splitLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            data: time,
          }, ],
          yAxis: [{
            nameTextStyle: {
              color: 'rgba(250,250,250,.6)',
              fontSize: 12,
              padding: 10,
            },
            min: 0,
            splitLine: {
              show: true,
              lineStyle: {
                color: ['#1c2a37'],
                width: 2,
                type: 'Dashed',
              },
            },
            axisLine: {
              show: false,
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: 'rgba(250,250,250,.6)', //更改坐标轴文字颜色
              },
            },
            axisTick: {
              show: false,
            },
          }, ],
          series: serveList,
        })
        window.addEventListener("resize", () => {
          myChart.resize();
        });
      },
    }
  }
</script>

<style scoped lang="less">
  .daySelect {
    height: 40px;
    width: 100%;
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
    font-size: 14px;
  }

  .button_style {
    margin: 0 5px;
    user-select: none;
    cursor: pointer;
    color: rgb(209, 206, 206)
  }

  .button_style1 {
    margin: 0 5px;
    user-select: none;
    cursor: pointer;
    color: #27BDFF;
  }
</style>