<template>
  <j-modal :visible="visible"
           title="排班"
           @cancel="handleCancel"
           :centered="true"
           @ok='handleOk'
           cancelText="关闭"
           :width="900"
           switchFullscreen
           :maskClosable='true'
  >
    <a-spin :spinning='confirmLoading'>
      <a-form-model ref="form"  :model="form" :rules="validatorRules" :label-col='labelCol' :wrapper-col='wrapperCol'>
        <a-row :gutter="24">
          <a-col v-bind='formItemLayout'>
            <a-form-model-item label="排班日期" prop="dateList">
              <a-range-picker @change="onChange"
                              :getCalendarContainer="node=> node.parentNode"
                              v-model="form.dateList"
                              :disabled-date="disabledDate"
                              format="YYYY-MM-DD"
                              style='width: 100%;'
              />
            </a-form-model-item>
          </a-col>
          <a-col v-bind='formItemLayout'>
            <a-form-model-item label="跳过节假日" prop="skip">
              <a-radio-group v-model="form.skip" :options="options"/>
            </a-form-model-item>
          </a-col>
          <a-col v-bind='formItemLayout'>
            <a-form-model-item label="值班人员" prop="userIds">
              <j-select-multi-user @change="changeUser"
                                   v-model='form.userIds'
                                   :query-config="selectUserQueryConfig"
                                   :url="url"
                                   :default="defaultUser"/>
            </a-form-model-item>
          </a-col>
<!--          <a-col v-bind='formItemLayout'>
            <a-form-model-item label="当天值班人数" prop="dutyNumber">
              <a-select
                :getPopupContainer='node=>node.parentNode'
                v-model='form.dutyNumber'
                placeholder='请选择人数'
                @change="updateNumber"
              >
                <a-select-option v-for="i in number" :key="i">
                  {{ i }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>-->
        </a-row>
      </a-form-model>
    </a-spin>
  </j-modal>
</template>

<script>
import { getAction, httpAction } from '@api/manage'
import moment from 'moment'
import { queryConfigureDictItem } from '@api/api'

export default {
  name: 'shiftArrangementModal',
  data(){
    return{
      // 选择用户查询条件配置
      selectUserQueryConfig: [],
      visible:false,
      confirmLoading: false,
      options: [
        {label: '是', value: true},
        {label: '否', value: false}
      ],
      formItemLayout: {
        md: { span: 24 },
        sm: { span: 24 }
      },
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
        md: { span: 5 },
        lg: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
        md: { span: 15 },
        lg: { span: 16 }
      },
      validatorRules: {
        dateList: [
          {required: true, message: '请选择排班日期!'},
        ],
        skip:[
          {required: false},
        ],
        userIds: [
          {required: true, message: '请选择值班人员!'},
        ],
        dutyNumber:[
          {required: false},
        ],
      },
      number: 0,
      form: {skip:false,dutyNumber:"1"},
      url:{list:'/sys/user/queryUsersRolesList'},
      defaultUser:{
        name: '值班人员',
        width: 1200,
        displayKey: 'realname',
        returnKeys: ['id', 'username'],
        queryParamText: '姓名'
      },
      moment,
    }
  },
  mounted() {
      queryConfigureDictItem({
        parentCode: 'dutyRole',
        childCode: "roleId"
      }).then((res) => {
        if (res.success && res.result) {
          this.url.list = "/sys/user/queryUsersRolesList?roleId="+res.result
          this.$forceUpdate()
        }
      })
    //this.getRoles()
  },
  methods:{
    getRoles(){
      getAction("/sys/role/queryall").then((res) => {
         if (res.success){
           this.selectUserQueryConfig.push({ key: 'roleId', label: '角色',placeholder:"请选择角色",roleOptions:res.result })
         }
      })
    },
    updateNumber(){
      this.$forceUpdate();
    },
    disabledDate(current) {
      // Can not select days before today and today
      return current && current < moment().endOf('day');
    },
    /**
     * 时间选择
     *
     * @param date
     * @param dateString
     */
    onChange(date, dateString) {
      this.form.startDate = dateString[0] + " 00:00:00"
      this.form.endDate = dateString[1] + " 00:00:00"
    },
    /**
     * 关闭
     */
    handleCancel() {
      this.visible = false
    },
    handleOk() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.confirmLoading=true
          let url = "/duty/shift/schedulePlan"
          httpAction(url, this.form, "post").then((res) => {
            this.confirmLoading=false
            if (res.success) {
              this.$message.success("操作成功")
              this.handleCancel()
              this.$emit("loadData1")
              this.$emit("getUserList")
            } else {
              this.$message.warning(res.message)
            }
          })
        }
      })
    },
    changeUser(text) {
      this.number =text? text.split(",").length:0
      this.form.dutyNumber=this.number>0?1:null
    },
    /**
     * 添加
     */
    add(id) {
      this.form = {skip:false,shiftId:id};
      this.visible = true
    },
  }
}
</script>

<style lang="less" scoped>
@import '~@assets/less/normalModal.less';
</style>