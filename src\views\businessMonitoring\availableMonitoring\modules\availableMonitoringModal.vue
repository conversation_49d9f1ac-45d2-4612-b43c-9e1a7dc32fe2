<template>
  <j-modal
    :title='title'
    :width='width'
    :centered='true'
    :visible='visible'
    :destroyOnClose='true'
    switchFullscreen
    cancelText='关闭'
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @ok='handleOk'
    @cancel='handleCancel'
  >
    <a-spin :spinning='confirmLoading'>
      <j-form-container :disabled='disableSubmit'>
        <a-form-model ref='form' :model='model' :rules='validatorRules' slot='detail'>
          <a-row>
            <a-col :span='24'>
              <a-form-model-item label='任务名称' prop='name' v-bind='formItemLayout'>
                  <a-input
                    v-model='model.name'
                    :allow-clear='true'
                    autocomplete='off'
                    placeholder='请输入任务名称' />
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
              <a-form-model-item label='业务名称' prop='bizInfoId' v-bind='formItemLayout'>
                <a-select
                  v-model='model.bizInfoId'
                  :allow-clear='true'
                  autocomplete='off'
                  placeholder='请选择业务名称'>
                  <a-select-option v-for='item in businessNameList' :key='item.id' :label='item.businessName' :value='item.id'>
                    {{ item.businessName }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
              <a-form-model-item label='时间规则' prop='cron' v-bind='formItemLayout'>
                <j-cron
                  v-model='model.cron'
                  placeholder='请输入时间规则'>
                </j-cron>
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
              <a-form-model-item label='任务描述' prop='description' v-bind='formItemLayout'>
                <a-textarea
                  v-model='model.description'
                  :auto-size='{ minRows: 2, maxRows: 8 }'
                  :allowClear='true'
                  autocomplete='off'
                  placeholder='请输入描述信息' />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </j-form-container>
    </a-spin>
  </j-modal>
</template>
<script>
import { getAction, httpAction } from '@api/manage'

export default {
  name: 'availableMonitoringModal',
  props: {},
  data() {
    return {
      title: '新增',
      width: '800px',
      disableSubmit: false,
      visible: false,
      confirmLoading: false,
      formItemLayout: {
        labelCol: { span: 5 },
        wrapperCol: { span: 16 }
      },
      model: {},
      businessNameList:[],
      validatorRules: {
        name:[
          {
            required: true, message: '请输入任务名称!'
          },
          {
            min: 2, max: 50, message: '任务名称长度应在2-50之间！', trigger: 'blur',
          }
        ],
        bizInfoId: [
          { required: true, message: '请选择业务名称!' }
        ],
        description: [
          { required: true, validator: this.validateDescription }
        ],
        cron: [
          { required: true, validator: this.validateCorn }
        ],
      },
      url:{
        businessName:'/business/info/businessList',
        validateCorn:'/autoInspection/devopsAutoInspection/cronCheck',
        add:'/business/availability/add',
        edit:'/business/availability/edit'
      }
    }
  },
  created() {
    this.getBusinessNameList()
  },
  methods: {
    getBusinessNameList() {
      this.confirmLoading=true
      getAction(this.url.businessName).then((res) => {
        if (res.success) {
          this.businessNameList=res.result
        }
        this.confirmLoading=false
      }).catch((err)=>{
        this.$message.error(err.message)
        this.confirmLoading=false
      })
    },
    validateCorn(rule, value, callback) {
      if(rule.required){
        if(value&&value.length>0){
          getAction(this.url.validateCorn,{cronExpression:value}).then((res) => {
            if (res.success) {
              callback()
            } else {
              callback('cron表达式格式错误!')
            }
          })
        }
        else {
          callback('请输入或选择cron表达式')
        }
      }
      else {
        callback()
      }
    },
    validateDescription(rule, value, callback) {
      if(rule.required){
        if (value) {
          if(value.length > 500){
            callback('任务描述信息长度应在 1-500 之间！')
          }
          else {
            callback()
          }
        } else {
          callback('请输入任务描述信息！')
        }
      }else {
        callback()
      }
    },
    add() {
      this.edit({
        cron:"0 0 0 * * ? *"
      })
    },
    edit(record) {
      this.visible = true
      this.$nextTick(() => {
        this.model =JSON.parse(JSON.stringify(record))
        /*if(!this.model.bizInfoId||!this.model.bizInfoName){
          this.model.bizInfoId=undefined
        }*/
      })
    },
    close() {
      this.visible = false
    },
    handleOk() {
      let that = this
      that.$refs.form.validate((err, values) => {
        if (err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!that.model.id) {
            httpurl += that.url.add
            method = 'post'
          } else {
            httpurl += that.url.edit
            method = 'put'
          }
          let formData=that.model
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
                that.close()
              } else {
                that.$message.warning(res.message)
              }
              that.confirmLoading = false
            }).catch((err) => {
            that.$message.warning(err.message)
            that.confirmLoading = false
          })
        }
      })
    },
    handleCancel() {
      this.close()
    }
  }
}
</script>
<style scoped lang='less'>
@import '~@assets/less/normalModal.less';
</style>