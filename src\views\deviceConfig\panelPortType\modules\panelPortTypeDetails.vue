<template>
  <a-card class='lookTop'>
    <a-row>
      <a-col :span="24">
        <div class='lookEditionWrapper'>
          <div class='lookEdition' @click="handleEdit(data)">
            <a-icon type="edit" class='editIcon' />编辑
          </div>
          <div style="display: inline-block;float: right; text-align: right; margin-bottom: 12px">
            <img src="~@/assets/return1.png" alt="" @click="getGo(ipagination)" class='returnImage' />
          </div>
        </div>
      </a-col>
      <a-col :span="24">
        <table class="gridtable">
          <tr>
            <td class="leftTd">类型名称</td>
            <td class="rightTd">{{ data.typeName }}</td>
            <td class="leftTd">类型标识</td>
            <td class="rightTd">{{ data.typeCode }}</td>
          </tr>
          <tr>
            <td class="leftTd">所属分类</td>
            <td class="rightTd">{{ data.dictValue_dictText }}</td>
            <td class="leftTd">图标</td>
            <td class="rightTd">
              <YqSvg v-if="data.typeIcon.slice(-3) == 'svg'" :type="data.typeIcon" :outSide="outSideUrl + data.typeIcon"
                style="color: #1677ff; font-size: 20px;" />
              <img v-else :src="imgUrl +'/'+ data.typeIcon" alt="" style="height: 20px;width: 20px;" />
            </td>
          </tr>
        </table>
      </a-col>
    </a-row>
    <panel-port-type-modal ref="modalForm" @ok="query"> </panel-port-type-modal>
  </a-card>
</template>
<script>
  import {
    httpAction,
    getAction
  } from '@/api/manage'
  import pick from 'lodash.pick'
  import JFormContainer from '@/components/jeecg/JFormContainer'
  import JDictSelectTag from '@/components/dict/JDictSelectTag'
  import panelPortTypeModal from './panelPortTypeModal'
  import YqSvg from '@/components/tools/SvgIcon/index.js'

  export default {
    name: 'panelPortTypeDetails',
    components: {
      JFormContainer,
      JDictSelectTag,
      panelPortTypeModal,
      YqSvg,
    },
    props: {
      data: {
        type: Object,
      },
      ipagination: {
        type: Object,
      },
    },
    data() {
      return {
        form: this.$form.createForm(this),
        imgUrl: window._CONFIG['staticDomainURL'],
        outSideUrl: window._CONFIG['downloadUrl'] + '/',
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          },
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          },
        },
        url: {
          edit: '/device/panelType/edit',
          queryById: '/device/panelType/queryById',
        },
      }
    },
    methods: {
      //返回上一级
      getGo(ipagination) {
        this.$parent.pButton1(0, ipagination)
      },
      handleEdit: function (record) {
        this.$refs.modalForm.edit(record)
        this.$refs.modalForm.title = '编辑'
        this.$refs.modalForm.disableSubmit = false
      },
      query() {
        getAction(this.url.queryById, {
          id: this.data.id
        }).then((res) => {
          if (res.code == 200) {
            this.data = res.result
          }
        })
      },
    },
  }
</script>
<style scoped>
  @import '~@assets/less/lookPage.less';
</style>