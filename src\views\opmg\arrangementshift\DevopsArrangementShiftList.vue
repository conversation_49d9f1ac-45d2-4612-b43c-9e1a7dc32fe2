<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll zxw">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <!-- 查询区域 -->
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <div class="table-page-search-wrapper-style">
          <a-form layout="inline" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="班次:">
                  <a-input
                    placeholder="请输入"
                    autocomplete="off"
                    :allowClear="true"
                    v-model="queryParam.schedualName"
                  ></a-input>
                </a-form-item>
              </a-col>

              <a-col :span="spanValue">
                <a-form-item label="交接人:">
                  <a-input
                    placeholder="请输入"
                    autocomplete="off"
                    :allowClear="true"
                    v-model="queryParam.shifteUserName"
                  ></a-input>
                </a-form-item>
              </a-col>

              <a-col :span="spanValue">
                <!--起止时间选择-->
                <a-form-item label="日期:">
                  <a-range-picker
                    @change="onChanges"
                    format="YYYY-MM-DD"
                    v-model="queryParam.arrangementTimeRange"
                    :placeholder="['开始日期', '截止日期']"
                    class="a-range-picker-choice-date"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="colBtnsSpan()">
                <span
                  class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                >
                  <a-button type="primary" @click="searchQuery" class="btn-search-style">查询</a-button>
                  <a-button @click="searchReset" style="margin-left: 10px" class="btn-reset-style">重置</a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>

      <a-card :bordered="false" style="flex: auto" class="core">
        <a-row class="lastBtn2">
          <!-- 操作按钮区域 -->
          <div class="table-operator">
            <a-button @click="goToWork">上班</a-button>
            <a-button @click="goOffWork">下班</a-button>
          </div>
        </a-row>
        <a-table
          ref="table"
          bordered
          rowKey="id"
          :columns="columns"
          :dataSource="dataSource"
          :pagination="ipagination"
          :loading="loading"
          :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          class="j-table-force-nowrap"
          @change="handleTableChange"
        >
          <template slot="htmlSlot" slot-scope="text">
            <div v-html="text"></div>
          </template>
          <template slot="imgSlot" slot-scope="text">
            <span v-if="!text" style="font-size: 14px">无图片</span>
            <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width: 80px; font-size: 14px" />
          </template>
          <template slot="fileSlot" slot-scope="text">
            <span v-if="!text" style="font-size: 14px">无文件</span>
            <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
              下载
            </a-button>
          </template>

          <span
            slot="action"
            slot-scope="text, record"
            class="caozuo"
            style="display: inline-block; white-space: nowrap; text-align: center"
          >
            <a @click="handleEdit(record)">编辑</a>

            <a-divider type="vertical" />
            <a @click="handleDetail(record)">详情</a>
            <a-divider type="vertical" />
            <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
              <a>删除</a>
            </a-popconfirm>
            <!-- <a-dropdown>
          <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
          <a-menu slot="overlay">
            <a-menu-item>
            </a-menu-item>
            <a-menu-item>
            </a-menu-item>
          </a-menu>
        </a-dropdown> -->
          </span>
          <template slot="tooltip" slot-scope="text">
            <a-tooltip placement="topLeft" :title="text" trigger="hover">
              <div class="tooltip">
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
      </a-card>

      <devops-arrangement-shift-modal ref="modalForm" @ok="modalFormOk"></devops-arrangement-shift-modal>
      <devops-arrangement-shift-off-work-modal
        ref="offWorkModalForm"
        @ok="modalFormOk"
      ></devops-arrangement-shift-off-work-modal>
    </a-col>
  </a-row>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import DevopsArrangementShiftModal from './modules/DevopsArrangementShiftModal'
import DevopsArrangementShiftOffWorkModal from './modules/DevopsArrangementShiftOffWorkModal'
import JSuperQuery from '@/components/jeecg/JSuperQuery.vue'
import { getAction } from '@/api/manage'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'

export default {
  name: 'DevopsArrangementShiftList',
  mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
  components: {
    DevopsArrangementShiftModal,
    DevopsArrangementShiftOffWorkModal,
    JSuperQuery,
  },
  data() {
    return {
      formItemLayout: {
        labelCol: {
          style: 'width:70px',
        },
        wrapperCol: {
          style: 'width:calc(100% - 70px)'
        }
      },
      description: '值班记录表管理页面',
      // 表头
      columns: [
        {
          title: '日期',

          dataIndex: 'arrangementTime',
          customRender: function (text) {
            return !text ? '' : text.length > 10 ? text.substr(0, 10) : text
          },
        },
        {
          title: '班次名称',

          dataIndex: 'schedualName',
        },
        {
          title: '值班人',

          dataIndex: 'realname',
        },
        {
          title: '值班状态',

          dataIndex: 'statusName',
        },
        {
          title: '上班时间',

          dataIndex: 'creattTime',
        },
        {
          title: '下班时间',

          dataIndex: 'endTime',
        },
        {
          title: '交接人',

          dataIndex: 'shifteUserName',
        },
        {
          title: '交接记录',

          dataIndex: 'shiftrecord',
        },
      ],
      url: {
        list: '/arrangementshift/devopsArrangementShift/list',
        delete: '/arrangementshift/devopsArrangementShift/delete',
        deleteBatch: '/arrangementshift/devopsArrangementShift/deleteBatch',
        exportXlsUrl: '/arrangementshift/devopsArrangementShift/exportXls',
        importExcelUrl: 'arrangementshift/devopsArrangementShift/importExcel',
        goWorkUrl: 'arrangementshift/devopsArrangementShift/doWorkUp',
      },
      dictOptions: {},
      superFieldList: [],
    }
  },
  created() {
    this.getSuperFieldList()
  },
  mounted() {},
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
    rowSelection() {
      return {
        getCheckboxProps: (record) => ({
          props: {
            disabled: record.name === 'Disabled User', // Column configuration not to be checked
            name: record.name,
          },
        }),
      }
    },
  },

  methods: {
    initDictConfig() {},
    //时间插件变更
    onChanges(date, dateString) {
      this.queryParam.arrangementTime_begin = dateString[0]
      this.queryParam.arrangementTime_end = dateString[1]
    },
    //上班
    goToWork() {
      if (this.selectionRows.length <= 0) {
        this.$message.warning('请选择一条记录！')
        return
      } else {
        var id = ''
        for (var a = 0; a < this.selectionRows.length; a++) {
          // 2022-04-18 00:00:00
          let timeNow = this.formatTime(new Date(), 'yyyy-MM-dd') + ' 00:00:00'
          // 2022-04-18 18:00:00
          if (this.selectionRows[a].arrangementTime != timeNow) {
            this.$message.warning('请选择今天相对应的排班日期！')
            return
          } else {
            if (this.selectionRows[a].status != 0) {
              this.$message.warning('今天已上班！')
              return
            }
            id = this.selectionRows[a].id
          }
        }
        this.goWork(id)
      }
    },
    formatTime: function (date, fmt) {
      var date = new Date(date)
      if (/(y+)/.test(fmt)) {
        fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length))
      }
      var o = {
        'M+': date.getMonth() + 1,
        'd+': date.getDate(),
        'h+': date.getHours(),
        'm+': date.getMinutes(),
        's+': date.getSeconds(),
      }
      for (var k in o) {
        if (new RegExp('(' + k + ')').test(fmt)) {
          var str = o[k] + ''
          fmt = fmt.replace(RegExp.$1, RegExp.$1.length === 1 ? str : ('00' + str).substr(str.length))
        }
      }
      return fmt
    },
    //请求后台
    goWork(id) {
      if ('' != id) {
        var that = this
        that.loading = true
        getAction(that.url.goWorkUrl, { id: id })
          .then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.loadData()
              that.onClearSelected()
            } else {
              that.$message.warning(res.message)
            }
          })
          .finally(() => {
            that.loading = false
          })
      }
    },
    //下班
    goOffWork() {
      if (this.selectionRows.length <= 0) {
        this.$message.warning('请选择一条记录！')
        return
      } else {
        for (var a = 0; a < this.selectionRows.length; a++) {
          let timeNow = this.formatTime(new Date(), 'yyyy-MM-dd') + ' 00:00:00'
          if (this.selectionRows[a].arrangementTime != timeNow) {
            this.$message.warning('请选择今天相对应的排班日期！')
            return
          } else {
            if (this.selectionRows[a].status == 0) {
              this.$message.warning('请先上班再下班！')
              return
            }
            if (this.selectionRows[a].status == 2) {
              this.$message.warning('已下班！')
              return
            }
            this.$refs.offWorkModalForm.edit(this.selectionRows[a])
            this.$refs.offWorkModalForm.title = '下班'
            this.$refs.offWorkModalForm.disableSubmit = false
          }
        }
      }
    },

    getSuperFieldList() {
      let fieldList = []
      fieldList.push({ type: 'string', value: 'arrangementId', text: '排班id', dictCode: '' })
      fieldList.push({ type: 'int', value: 'status', text: '状态', dictCode: '' })
      fieldList.push({ type: 'date', value: 'creattTime', text: '上班时间' })
      fieldList.push({ type: 'date', value: 'endTime', text: '下班时间' })
      fieldList.push({ type: 'string', value: 'shifteUser', text: '交接人', dictCode: '' })
      fieldList.push({ type: 'string', value: 'shiftrecord', text: '交接记录', dictCode: '' })
      this.superFieldList = fieldList
    },
  },
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
.table-page-search-wrapper .ant-form-inline .ant-form-item {
  margin-bottom: 0 !important;
}
.ant-table-pagination.ant-pagination {
  margin: 16px 0 0 0 !important;
}
/*表头样式*/
::v-deep .ant-table-thead > tr > th {
  text-align: center;
  white-space: nowrap;
}

/*内容对齐方式、省略显示*/
::v-deep .ant-table-tbody > tr > td {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;

  &:first-child,
  &:nth-child(2),
  &:nth-child(3),
  &:nth-child(4),
  &:nth-child(5),
  &:nth-child(6),
  &:nth-child(7),
  &:nth-child(8),
  &:nth-child(9) {
    text-align: center;
  }
}
</style>
