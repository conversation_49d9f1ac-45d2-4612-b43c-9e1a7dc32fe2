<template>
  <j-modal
    :title='title'
    :width='width'
    :visible='visible'
    :destroyOnClose='true'
    :centered='true'
    switchFullscreen
    @ok='handleOk'
    @cancel='handleCancel'
    :confirmLoading='confirmLoading'
    cancelText='关闭'
    okText='确定授权'>
    <div style='height: 70vh;overflow-y: auto'>
      <a-table
        tableLayout='fixed'
        rowKey='name'
        :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        :columns='columns'
        :data-source='dataSource'
        :pagination='false'
        :loading='loading'
        bordered
      />
    </div>
  </j-modal>
</template>

<script>
import { getAction ,postAction } from '@api/manage'

const columns = [
  {
    title: '角色名称',
    dataIndex: 'name',
    width: 180
  },
  {
    title: '角色权限',
    dataIndex: 'auth',
    ellipsis: true
  }
]


export default {
  name: 'DataSecurityAuthModal',
  components: {},
  data() {
    return {
      title: '修改策略',
      width: '70%',
      visible: false,
      disableSubmit: false,
      dataSource: [],
      columns,
      selectedRowKeys: [],
      loading: false,
      cluster:"",
      detachPolicies:[],
      userName:"",
      confirmLoading:false,
    }
  },
  created() {

  },
  mounted() {

  },
  computed: {
    hasSelected() {
      return this.selectedRowKeys.length > 0
    }
  },
  methods: {
    getAuthorityList() {
      this.loading = true;
      getAction('/distributedStorage/authority',{clusterId:this.cluster}).then((res) => {
        if (res.success) {
          this.dataSource = res.result;
        }
      }).finally(()=>{
        this.loading = false;
      })
    },
    open(cluster,userAuthority,userName) {
      this.userName = userName;
      this.selectedRowKeys = userAuthority.map(el=>el.policyName);
      this.detachPolicies = userAuthority.map(el=>el.policyName);
      this.cluster = cluster
      this.visible = true
      this.getAuthorityList();

    },
    close() {
      this.selectedRowKeys = [];
      this.cluster = "";
      this.dataSource = [];
      this.$emit('close')
      this.visible = false
      //this.$refs.realForm.selectInspectionType()
    },
    handleOk() {
      let params = {
        clusterId: this.cluster+"",
        userName: this.userName,
        attachPolicies: this.selectedRowKeys.join(","),
        detachPolicies: this.detachPolicies.join(","),
      }
      this.confirmLoading = true;
      postAction("distributedStorage/user/authority",params).then(res=>{
        if(res.success) {
          this.$message.success("授权修改成功")
          this.$emit('ok')
          this.close()
        }
      }).finally(()=>{
        this.confirmLoading = false
      })
    },
    submitCallback() {
      this.$emit('ok')
      this.visible = false
    },
    handleCancel() {
      this.close()
    },
    onSelectChange(selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys
      console.log("选择的权限便哈了=== ",this.selectedRowKeys,this.detachPolicies )
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/normalModal.less';
</style>