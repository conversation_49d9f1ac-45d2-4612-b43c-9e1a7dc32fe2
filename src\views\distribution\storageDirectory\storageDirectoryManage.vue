<template>
  <div style="height:100%">
    <keep-alive exclude=''>
      <component :is="pageName" :data="data" />
    </keep-alive>
  </div>
</template>
<script>
import storageDirectoryList from './storageDirectoryList'
// import storageDirectoryDetail from '@comp/alarmTemplate/storageDirectoryDetail'
export default {
  name: "storageDirectoryManage",
  data() {
    return {
      isActive: 0,
      data: {},
      tableParams:{}
    };
  },
  components: {
    storageDirectoryList,
    //storageDirectoryDetail
  },
  created() {
    this.pButton1(0);
  },
  //使用计算属性
  computed: {
    pageName() {
      switch (this.isActive) {
        case 0:
          return "storageDirectoryList";
          break;
        default:
          return "storageDirectoryDetail";
          break;
      }
    }
  },
  methods: {
    pButton1(index) {
      this.isActive = index;
    },
    pButton2(index, item) {
      this.isActive = index
      this.data = item.data
    }
  }
}
</script>