<template>
  <div style="height: 100%;">
    <keep-alive >
      <component :is="pageName" :data="data"/>
    </keep-alive>
  </div>
</template>
<script>
  import SysNoticeConfigList from './SysNoticeConfigList'
  import SysNoticeConfigDetail from './modules/SysNoticeConfigDetail'
  export default {
    name: "SysNoticeConfigManage",
    data() {
      return {
        isActive: 0,
        data:{}
      };
    },
    components: {
      SysNoticeConfigList,
      SysNoticeConfigDetail
    },
    created(){
      this.pButton1(0);
    },
    //使用计算属性
    computed: {
      pageName() {
        switch (this.isActive) {
          case 0:
            return "SysNoticeConfigList";
            break;

          default:
            return "SysNoticeConfigDetail";
            break;
        }
      }
    },
    methods: {
      pButton1(index) {
        this.isActive = index;
      },
      pButton2(index,item) {
        this.isActive = index;
        this.data = item;
      }
    }
  }
</script>