<template>
  <j-modal
    :title='title'
    :width='width'
    :centered='true'
    :visible='visible'
    :destroyOnClose='true'
    switchFullscreen
    cancelText='关闭'
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @ok='handleOk'
    @cancel='handleCancel'
  >
    <a-spin :spinning='confirmLoading'>
      <j-form-container :disabled='disableSubmit'>
        <a-form-model ref='form' :model='model' :rules='validatorRules' slot='detail' v-bind='formItemLayout'>
          <a-row>
            <a-col :span='24'>
              <a-form-model-item label='模板名称' prop='templateName'>
                <a-input
                  v-model='model.templateName'
                  :allow-clear='true'
                  autocomplete='off'
                  placeholder='请输入模板名称' />
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
              <a-form-model-item label='描述' prop='description'>
                <a-textarea style='width: 100%' v-model='model.description' :autoSize='{minRows:1,maxRows:6}'
                            :allow-clear='true' autocomplete='off' placeholder='请输入描述' />
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
              <a-form-model-item label='模板内容' prop='content'>
                <j-editor v-model='model.content' :img-biz-path='"knowledges/image"' />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </j-form-container>
    </a-spin>
  </j-modal>
</template>
<script>
import { getAction, httpAction } from '@api/manage'
import { setImgAllPath,setImgRelativePath } from '@/utils/imagePathAboutTinymce'
export default {
  name: 'AddTemplateModal',
  components: {},
  props: {},
  data() {
    return {
      title: '新增',
      width: '900px',
      disableSubmit: false,
      visible: false,
      confirmLoading: false,
      formItemLayout: {
        labelCol: { span: 5 },
        wrapperCol: { span: 16 }
      },
      model: {},
      validatorRules: {
        templateName: [
          { required: true,min:2,max:50,validator:this.validateTemplateName}
        ],
        description: [
          { required: true,max: 200, validator:this.validateDescription}
        ],
        content: [
          { required: true, message: '请输入模板内容！', trigger: 'blur' }
        ]
      },
      url: {
        add: '/kbase/knowledges/template/add',
        edit: '/kbase/knowledges/template/edit',
        templateNameDuplicate: '/kbase/knowledges/template/nameIsDuplicate'
      }
    }
  },

  methods: {
    validateTemplateName(rule, value, callback) {
      let displayName='模板名称'
      let { min, max,fullField} = rule
      if (!value) {
        callback(`请输入${displayName}!`)
      }
      const trimmedValue = value.trim()
      if (trimmedValue === '') {
        callback(`${displayName}不能全为空白字符`)
      }
      if (value !== trimmedValue) {
        callback(`${displayName}首尾不能包含空白字符！`)
      }
      if (min && max && (value.length > max || value.length < min)) {
        callback(`${displayName}长度应在 ${min}-${max} 个字符之间！`)
      } else if (max && value.length > max) {
        callback(`${displayName}长度不能超出 ${max} 个字符！`)
      } else if (min && value.length < min) {
        callback(`${displayName}长度不能少于 ${min} 个字符！`)
      }

      let param = {
        templateId:this.model.id||''
      }
      param[fullField]=value
      let inf=fullField+'Duplicate'
      getAction( this.url[inf], param).then((res) => {
        if (res.success) {
          callback()
        } else {
          callback(res.message)
        }
      }).catch((err)=>{
        callback(err.message)
      })
    },
    validateDescription(rule, value, callback) {
      let displayName='描述'
      let { min, max} = rule
      if (!value) {
        callback(`请输入${displayName}!`)
      }
      const trimmedValue = value.trim()
      if (trimmedValue === '') {
        callback(`${displayName}不能全为空白字符`)
      }
      /*if (value !== trimmedValue) {
        callback(`${displayName}首尾不能包含空白字符！`)
      }*/
      if (min && max && (value.length > max || value.length < min)) {
        callback(`${displayName}长度应在 ${min}-${max} 个字符之间！`)
      } else if (max && value.length > max) {
        callback(`${displayName}长度不能超出 ${max} 个字符！`)
      } else if (min && value.length < min) {
        callback(`${displayName}长度不能少于 ${min} 个字符！`)
      }
      callback()
    },
    add() {
      this.edit({})
    },
    edit(record) {
      this.visible = true
      this.$nextTick(() => {
        this.model = record
        let content=this.model.content
        this.model.content=content&&content.length>0?setImgAllPath(content):''
      })
    },
    close() {
      this.visible = false
    },
    handleOk() {
      let that = this
      that.$refs.form.validate((err, values) => {
        if (err&&!that.confirmLoading) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!that.model.id) {
            httpurl += that.url.add
            method = 'post'
          } else {
            httpurl += that.url.edit
            method = 'put'
          }
          let formData = JSON.parse(JSON.stringify(that.model))
          formData.content=setImgRelativePath(formData.content)
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
                that.close()
              } else {
                that.$message.warning(res.message)
              }
              that.confirmLoading = false
            }).catch((err) => {
            that.$message.warning(err.message)
            that.confirmLoading = false
          })
        }
      })
    },
    handleCancel() {
      this.close()
    }
  }
}
</script>
<style scoped lang='less'>
@import '~@assets/less/normalModal.less';
</style>