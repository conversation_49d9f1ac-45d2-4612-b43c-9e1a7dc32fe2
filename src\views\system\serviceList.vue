<template>
  <a-row :gutter='10' style='height: 100%' class='vScroll'>
    <a-col style='width: 100%; height: 100%; display: flex; flex-direction: column'>
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <!-- 查询区域 -->
        <div class='table-page-search-wrapper'>
          <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
            <a-row :gutter='24' ref='row'>
              <a-col :span='spanValue'>
                <a-form-item label='服务商名称'>
                  <a-input  :maxLength="maxLength" placeholder='请输入服务商名称' :allowClear='true' autocomplete='off'
                    v-model='queryParam.providerName'>
                  </a-input>
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label='服务单位'>
                  <a-select v-model='queryParam.departs' :getPopupContainer='(node) => node.parentNode'
                    :allowClear='true' placeholder='请选择服务单位'>
                    <a-select-option v-for="item in serveList" :key="item.id" :value='item.departName'>
                      {{ item.departName }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label='服务优先级'>
                  <a-input-number v-model='queryParam.priority' :min="1" placeholder='请输入服务优先级' style="width: 100%">
                  </a-input-number>
                </a-form-item>
              </a-col>
              <a-col :span='colBtnsSpan()'>
                <span class='table-page-search-submitButtons'
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button type='primary' class='btn-search btn-search-style' @click='searchQuery'>查询</a-button>
                  <a-button class='btn-reset btn-reset-style' @click='searchReset' style='margin-left: 8px'>重置
                  </a-button>
                  <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <!-- 操作按钮区域 -->
        <div class='table-operator table-operator-style'>
          <a-button @click='handleAdd' v-has="'user:add'">新增</a-button>
          <a-dropdown v-if='selectedRowKeys.length > 0'>
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item key='1' v-has="'user:delete'" @click='batchDel'>删除</a-menu-item>
            </a-menu>
            <a-button>
              批量操作
              <a-icon type='down' />
            </a-button>
          </a-dropdown>
        </div>
        <!-- table区域-begin -->
        <a-table ref='table' bordered rowKey='id' :columns='columns' :dataSource='dataSource'
          :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}" :pagination='ipagination' :loading='loading'
          :rowSelection='{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }' @change='handleTableChange'>
          <template slot="departs" slot-scope="text,record">
            <a-tooltip placement='topLeft' :title='record.departs' trigger='hover'>
              <div class='tooltip'>
                {{ record.departs }}
              </div>
            </a-tooltip>
          </template>
          <span slot="action" slot-scope="text, record" class="caozuo">
            <a href="javascript:;" @click="handleDetailPage(record)">查看</a>
            <a-divider type="vertical" />
            <a @click="handleEdit(record)">编辑</a>
            <a-divider type="vertical" />
            <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
              <a style='color: #409eff'>删除</a>
            </a-popconfirm>
          </span>
        </a-table>
        <!-- table区域-end -->
      </a-card>
      <serviceModal ref='modalForm' @ok='modalFormOk'> </serviceModal>
    </a-col>
  </a-row>
</template>

<script>
  import serviceModal from './modules/serviceModal'
  import {JeecgListMixin} from '@/mixins/JeecgListMixin'
  import {YqFormSearchLocation} from '@/mixins/YqFormSearchLocation'
  import {getAction} from '@/api/manage'

  export default {
    name: 'serviceList',
    mixins: [JeecgListMixin, YqFormSearchLocation],
    components: {serviceModal},
    data() {
      return {
        maxLength:50,
        description: '服务商管理页面',
        formItemLayout: {
          labelCol: {
            style: 'width:90px'
          },
          wrapperCol: {
            style: 'width:calc(100% - 90px)'
          }
        },
        serveList: [],
        queryParam: {},
        isorter: {
          column: 'priority',
          order: 'ascend'
        },
        columns: [{
            title: '服务商名称',
            dataIndex: 'providerName'
          },
          {
            title: '服务优先级',
            dataIndex: 'priority',
            customCell: () => {
              let cellStyle = 'text-align: center;width:120px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '联系电话',
            dataIndex: 'phone',
            customCell: () => {
              let cellStyle = 'width:120px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '服务单位',
            data: 'departs',
            scopedSlots: {
              customRender: 'departs'
            },
            customCell: () => {
              let cellStyle = 'text-align: left;min-width: 100px;max-width:300px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '操作',
            dataIndex: 'action',
            scopedSlots: {
              customRender: 'action'
            },
            fixed: 'right',
            align: 'center',
            width: 170
          }
        ],
        url: {
          list: '/serviceProvider/list',
          delete: '/serviceProvider/delete',
          deleteBatch: '/serviceProvider/deleteBatch',
          queryDepartTreeSync: '/sys/sysDepart/listAll',
        }
      }
    },

    created() {
      this.queryDepartTreeSync()
    },
    methods: {
      queryDepartTreeSync() {
        getAction(this.url.queryDepartTreeSync, {
          size: 100
        }).then(res => {
          if (res.success) {
            this.serveList = res.result

          }
        })
      },
    },
  }
</script>
<style lang='less' scoped>
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';

  ::v-deep .ant-dropdown-menu-item a {
    color: #409eff !important;
  }

  ::v-deep .ant-dropdown-menu-item .dontDelete {
    color: rgba(0, 0, 0, 0.35) !important;
  }
</style>