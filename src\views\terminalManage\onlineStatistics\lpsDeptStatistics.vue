<template>
  <a-row :gutter='10' style='height: 100%;overflow: hidden' class='vScroll zhl zhll'>
    <a-col style='width: 100%; height: 100%; display: flex; flex-direction: column'>
      <div  class='gutter-example'>
        <a-row :gutter='10'>
          <a-col :lg='3' :md='6' :sm='12' :xs='24' class='col-example'>
            <a-row class='row-example'>
              <a-col :span='24' class='gutter-row col-one'>
                <div class='gutter-box '>
                  <div class='gutter-box-left-top'>
                    <span>分发总数</span>
                  </div>
                  <div class='gutter-box-left-bottom'>
                    <span>{{ statisticsInfo.planNumber }}</span>
                  </div>
                </div>
              </a-col>
            </a-row>
          </a-col>
          <a-col :lg='3' :md='6' :sm='12' :xs='24' class='col-example'>
            <a-row class='row-example'>
              <a-col :span='24' class='gutter-row col-four'>
                <div class='gutter-box'>
                  <div class='gutter-box-left-top'>
                    <span>注册总数</span>
                  </div>
                  <div class='gutter-box-left-bottom'>
                    <span>{{ statisticsInfo.registerNumber }}</span>
                  </div>
                </div>
              </a-col>
            </a-row>
          </a-col>
          <a-col :lg='3' :md='6' :sm='12' :xs='24' class='col-example'>
            <a-row class='row-example'>
              <a-col :span='24' class='gutter-row col-three'>
                <div class='gutter-box'>
                  <div class='gutter-box-left-top'>
                    <span>单机总数</span>
                  </div>
                  <div class='gutter-box-left-bottom'>
                    <span>{{ statisticsInfo.singleMachine }}</span>
                  </div>
                </div>
              </a-col>
            </a-row>
          </a-col>
          <a-col :lg='3' :md='6' :sm='12' :xs='24' class='col-example'>
            <a-row class='row-example'>
              <a-col :span='24' class='gutter-row col-two'>
                <div class='gutter-box'>
                  <div class='gutter-box-left-top'>
                    <span>专网机总数</span>
                  </div>
                  <div class='gutter-box-left-bottom'>
                    <span>{{ statisticsInfo.expertMachine }}</span>
                  </div>
                </div>
              </a-col>
            </a-row>
          </a-col>
          <a-col :lg='3' :md='6' :sm='12' :xs='24' class='col-example'>
            <a-row class='row-example'>
              <a-col :span='24' class='gutter-row col-five'>
                <div class='gutter-box'>
                  <div class='gutter-box-left-top'>
                    <span>开机总数</span>
                  </div>
                  <div class='gutter-box-left-bottom'>
                    <span>{{ statisticsInfo.onCount }}</span>
                  </div>
                </div>
              </a-col>
            </a-row>
          </a-col>
          <a-col :lg='3' :md='6' :sm='12' :xs='24' class='col-example'>
            <a-row class='row-example'>
              <a-col :span='24' class='gutter-row col-six'>
                <div class='gutter-box'>
                  <div class='gutter-box-left-top'>
                    <span>未开机总数</span>
                  </div>
                  <div class='gutter-box-left-bottom'>
                    <span>{{ statisticsInfo.offCount }}</span>
                  </div>
                </div>
              </a-col>
            </a-row>
          </a-col>
          <a-col :lg='3' :md='6' :sm='12' :xs='24' class='col-example'>
            <a-row class='row-example'>
              <a-col :span='24' class='gutter-row col-seven'>
                <div class='gutter-box'>
                  <div class='gutter-box-left-top'>
                    <span>使用率</span>
                  </div>
                  <div class='gutter-box-left-bottom'>
                    <span>{{ statisticsInfo.onCountRatio }}%</span>
                  </div>
                </div>
              </a-col>
            </a-row>
          </a-col>
        </a-row>
      </div>
      <!-- 查询区域 -->
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <div class='table-page-search-wrapper-style'>
          <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
            <a-row :gutter='24' ref='row'>
              <a-col :span='spanValue'>
                <a-form-item label='单位'>
                  <a-tree-select
                    :getPopupContainer='(node) => node.parentNode'
                    v-model='searchKey'
                    tree-node-filter-prop='title'
                    :replaceFields='replaceFields'
                    :treeData='selectOption'
                    allow-clear
                    :searchValue='bsearchKey'
                    style='width: 100%'
                    multiple
                    :maxTagCount='1'
                    :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                    placeholder='请选择单位'
                    @change='onChangeTree'
                    @search='onSearch'
                    @select='onSelect'
                  >
                  </a-tree-select>
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label='统计时间'>
                  <a-range-picker
                    class='a-range-picker-choice-date' :key='updateKey'
                    :getPopupContainer='(node) => node.parentNode'
                    v-model='queryParam.warehousingTime'
                    :disabled-date='disabledDate'
                    format='YYYY-MM-DD'
                    :placeholder="['开始时间', '截止时间']"
                    @calendarChange='calendarPriceRangeChange'
                    @openChange='openChange'
                    @change='onChangePicker'
                    :default-value='[startTime, endTime]'
                  />
                </a-form-item>
              </a-col>
              <a-col :span='colBtnsSpan()'>
                <span
                  class='table-page-search-submitButtons'
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                >
                  <a-button type='primary' class='btn-search btn-search-style' @click='dosearch'>查询</a-button>
                  <a-button class='btn-reset btn-reset-style' @click='doreset'>重置</a-button>
                  <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <div class='table-operator table-operator-style'>
          <a-button class='btn-add' @click='selfexport'>导出</a-button>
        </div>

        <a-table
          ref='table'
          bordered
          :rowKey='(record,index)=>{return index}'
          :columns='columns'
          :dataSource='dataSource'
          :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
          :pagination='ipagination'
          :loading='loading'
          :footer='handleFooterShow'
          @change='handleTableChange'
        >
          <template slot='rate' slot-scope='text'>
            <span>{{ text }}%</span>
          </template>
          <template slot='tooltip' slot-scope='text'>
            <a-tooltip placement='topLeft' :title='text' trigger='hover'>
              <div class='tooltip'>
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
      </a-card>
    </a-col>
  </a-row>
</template>

<script>
import moment from 'moment'
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import JSuperQuery from '@/components/jeecg/JSuperQuery.vue'
import { queryAssetsCategoryTreeList } from '@/api/device'
import { httpAction, getAction, deleteAction } from '@/api/manage'
import JDictSelectTag from '@/components/dict/JDictSelectTag.vue'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
import { filterObj } from '@/utils/util'

export default {
  name: 'lpsDeptStatistics',
  mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
  components: {
    JSuperQuery,
    JDictSelectTag
  },
  data() {
    return {
      updateKey: 0,
      startTime: moment().startOf('day').subtract(15, 'days'), // 创建起始时间-筛选框
      endTime: moment().endOf('day').subtract(1, 'days'), // 创建结束时间-筛选框
      formItemLayout: {
        labelCol: {
          style: 'width:80px'
        },
        wrapperCol: {
          style: 'width:calc(100% - 80px)'
        }
      },
      offsetDays: 86400000 * 9, //最多选择7天
      searchKey: undefined,
      bsearchKey: '',
      value: undefined,
      replaceFields: {
        children: 'children',
        title: 'deptName',
        key: 'deptId',
        value: 'deptId'
      },
      //tree
      selectOption: [],
      batchEnable: 1,
      description: '设备表管理页面',
      firstTitle: '', //存储搜素tree的第一个title
      // 树
      assetsCategoryTree: [],
      treeData: [],
      expandedKeys: [],
      searchValue: '',
      autoExpandParent: true,
      dropTrigger: '',
      selectedKeys: [],
      selectedTitle: '',
      checkedKeys: [],
      checkStrictly: true,
      // iExpandedKeys: [],
      currFlowId: '',
      currFlowName: '',
      rightClickSelectedBean: {},
      // 表头
      columns: [
        {
          title: '单位',
          dataIndex: 'deptName',
          scopedSlots: { customRender: 'tooltip' },
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 150px;max-width:400px'
            return { style: cellStyle }
          }
        },
        {
          title: '分发数',
          dataIndex: 'planNumber',
          customCell: () => {
            let cellStyle = 'text-align: right;min-width: 100px;max-width:300px'
            return { style: cellStyle }
          }
        },
        {
          title: '注册数',
          dataIndex: 'registerNumber',
          customCell: () => {
            let cellStyle = 'text-align: right;min-width: 100px;max-width:150px'
            return { style: cellStyle }
          }
        },
        {
          title: '单机数',
          dataIndex: 'singleMachine',
          customCell: () => {
            let cellStyle = 'text-align: right;min-width: 100px;max-width:300px'
            return { style: cellStyle }
          }
        },
        {
          title: '专网机数',
          dataIndex: 'expertMachine',
          customCell: () => {
            let cellStyle = 'text-align: right;min-width: 100px;max-width:300px'
            return { style: cellStyle }
          }
        },
        {
          title: '开机数',
          dataIndex: 'onCount',
          customCell: () => {
            let cellStyle = 'text-align: right;min-width: 100px;max-width:300px'
            return { style: cellStyle }
          }
        },
        {
          title: '未开机数',
          dataIndex: 'offCount',
          customRender: (text, record, index) => {
            let offCount = text
            if (offCount < 0) {
              offCount = 0
            }
            return offCount
          },
          customCell: () => {
            let cellStyle = 'text-align: right;min-width: 100px;max-width:300px'
            return { style: cellStyle }
          }
        },
        {
          title: '使用率',
          dataIndex: 'onCountRatio',
          customRender: (text, record, index) => {
            let onCountRatio = text
            if (onCountRatio <= 0) {
              onCountRatio = 0
            }
            else if(onCountRatio>=100){
               onCountRatio=100
            }
            return onCountRatio + '%'
          },

          customCell: () => {
            let cellStyle = 'text-align: right;min-width: 100px;max-width:300px'
            return { style: cellStyle }
          }
        }
      ],
      url: {
        list: '/unit/statis/dz/findStatisList',
        getStatisticsNum: '/unit/statis/dz/findTopCardInfo',
        exportXlsUrl: '/unit/statis/dz/exportExcel',
        importExcelUrl: 'device/deviceInfo/importExcel',
        getUrl: '/device/deviceInfo/getUrl'
      },
      dataSource: [], //table数据
      //table分页合计数据
      dictOptions: {},
      superFieldList: [],
      name: '',
      status: '',
      statuslist: [
        {
          name: '在线',
          code: '1'
        },
        {
          name: '离线',
          code: '0'
        },
        {
          name: '告警',
          code: '2'
        }
      ],
      categoryId: '', //选取tree的key
       statisticsInfo: {
         isLoad:true,
         planNumber: 0,
         registerNumber: 0,
         singleMachine: 0,
         expertMachine: 0,
         onCount: 0,
         offCount: 0,
         onCountRatio: 0
       },
      totalDataSource: [],
      disableMixinCreated: true
    }
  },
  mounted() {
    if (this.startTime && this.endTime) {
      this.queryParam.startTime = this.startTime.format('YYYY-MM-DD')
      this.queryParam.endTime = this.endTime.format('YYYY-MM-DD')
    }
    //this.getStatisticsNum()
    this.select()
    this.loadData()
  },

  computed: {
    importExcelUrl: function() {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    }
  },
  methods: {
    handleFooterShow() {
      return (
        <div style='padding:16px 0;boder:1px solid;width:100%;background-color:white;padding-left:16px'>
          <div style='font-size:16px;font-weight:700;display:inline-block;text-align:center'>小计：</div>
          <div style='display:inline-block'>
             <span style='white-space: nowrap'>
               <span>分发数：</span><span style='font-weight:700'>{this.totalDataSource.totalPlanNumber}；</span>
             </span>
            <span style='white-space: nowrap'>
               <span>注册数：</span><span style='font-weight:700'>{this.totalDataSource.totalRegisterNumber}；</span>
             </span>
            <span style='white-space: nowrap'>
               <span>单机数：</span><span style='font-weight:700'>{this.totalDataSource.totalSingleMachine}；</span>
             </span>
            <span style='white-space: nowrap'>
               <span>专网机数：</span><span style='font-weight:700'>{this.totalDataSource.totalExpertMachine}；</span>
             </span>
            <span style='white-space: nowrap'>
               <span>开机数：</span><span style='font-weight:700'>{this.totalDataSource.totalOnCount}；</span>
             </span>
            <span style='white-space: nowrap'>
               <span>未开机数：</span><span style='font-weight:700'>{this.totalDataSource.totalOffCount}；</span>
             </span>
            <span style='white-space: nowrap'>
               <span>使用率：</span><span style='font-weight:700'>{this.totalDataSource.totalOnCountRatio}%</span>
             </span>
          </div>
        </div>
      )
    },
    handleTableChange(pagination, filters, sorter) {
      //分页、排序、筛选变化时触发
      //TODO 筛选
      if (Object.keys(sorter).length > 0) {
        this.isorter.column = sorter.field
        this.isorter.order = 'ascend' == sorter.order ? 'asc' : 'desc'
      }
      this.ipagination = pagination
      //this.loadData()
    },
    loadData(arg) {
      if (!this.url.list) {
        this.$message.error('请设置url.list属性!')
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1
      }
      var params = this.getQueryParams() //查询条件
      this.loading = true
      getAction(this.url.list, params).then((res) => {
        if (res.success) {
          this.dataSource = res.result.records || res.result
          this.totalDataSource = JSON.parse(res.message)
          this.totalDataSource.totalPlanNumber = this.totalDataSource.totalPlanNumber >= 0 ? this.totalDataSource.totalPlanNumber : 0
          this.totalDataSource.totalRegisterNumber = this.totalDataSource.totalRegisterNumber >= 0 ? this.totalDataSource.totalRegisterNumber : 0
          this.totalDataSource.totalSingleMachine = this.totalDataSource.totalSingleMachine >= 0 ? this.totalDataSource.totalSingleMachine : 0
          this.totalDataSource.totalExpertMachine = this.totalDataSource.totalExpertMachine >= 0 ? this.totalDataSource.totalExpertMachine : 0
          this.totalDataSource.totalOnCount = this.totalDataSource.totalOnCount >= 0 ? this.totalDataSource.totalOnCount : 0
          this.totalDataSource.totalOffCount = this.totalDataSource.totalOffCount >= 0 ? this.totalDataSource.totalOffCount : 0
          this.totalDataSource.totalOnCountRatio = this.totalDataSource.totalOnCountRatio >= 0 ? this.totalDataSource.totalOnCountRatio : 0
          if(this.totalDataSource.totalOnCountRatio>=100){
            this.totalDataSource.totalOnCountRatio=100
          }

          //第一次加载数据时，页面底部数据初始化
          if (this.statisticsInfo.isLoad) {
            let totalData = {
              isLoad:false,
              planNumber: this.totalDataSource.totalPlanNumber,
              registerNumber: this.totalDataSource.totalRegisterNumber,
              singleMachine: this.totalDataSource.totalSingleMachine,
              expertMachine: this.totalDataSource.totalExpertMachine,
              onCount: this.totalDataSource.totalOnCount,
              offCount: this.totalDataSource.totalOffCount,
              onCountRatio: this.totalDataSource.totalOnCountRatio,
            }
            this.statisticsInfo = totalData
          }

          if (this.dataSource.length < 9) {
            this.clientHeight = false
          }
          this.ipagination.total = res.result.total
        }
        if (res.code === 510) {
          this.$message.warning(res.message)
        }
        this.loading = false
      })
    },
    getQueryParams() {
      //获取查询条件
      let sqp = {}
      if (this.superQueryParams) {
        sqp['superQueryParams'] = encodeURI(this.superQueryParams)
        sqp['superQueryMatchType'] = this.superQueryMatchType
      }
      var param = Object.assign(sqp, this.queryParam, this.isorter, this.filters)
      param.field = this.getQueryField()
      param.pageNo = this.ipagination.current
      param.pageSize = this.ipagination.pageSize
      return filterObj(param)

    },
    moment,
    //根据选择的开始时间/结束时间，动态渲染要禁用的日期
    disabledDate(current) {
     //return current < moment().subtract(31, 'days') || current > moment().subtract(0, 'days')
      return (current && current > moment().subtract(1, 'days').endOf('day'))||current < moment().subtract(31, 'days')
    },
    openChange(status) {
      if (!status) {
        this.selectPriceDate = ''
      }
    },
    //选择开始时间/结束时间
    calendarPriceRangeChange(date) {
      this.selectPriceDate = date[0]
    },
    select() {
      getAction('/sys/sysDepart/queryAllTree').then((res) => {
        for (let i = 0; i < res.length; i++) {
          let temp = res[i]
          this.selectOption.push(temp)
        }
      })
    },

    onChangeTree(value) {
      this.value = value
      // this.queryParam.deptId = value.join(",")
    },
    onSearch(e) {
      this.bsearchKey = e
    },
    onSelect() {
    },
    onChangePicker(value, dateString) {
      this.startTime = value[0]
      this.endTime = value[1]
    },
    getStatisticsNum() {
      getAction(this.url.getStatisticsNum).then((res) => {
        if (res.code == 200) {
          this.statisticsInfo.planNumber = res.result ? (res.result.planNumber >= 0 ? res.result.planNumber : 0) : 0
          this.statisticsInfo.registerNumber = res.result ? (res.result.registerNumber >= 0 ? res.result.registerNumber : 0) : 0
          this.statisticsInfo.singleMachine = res.result ? (res.result.singleMachine >= 0 ? res.result.singleMachine : 0) : 0
          this.statisticsInfo.expertMachine = res.result ? (res.result.expertMachine >= 0 ? res.result.expertMachine : 0) : 0
          this.statisticsInfo.onCount = res.result ? (res.result.onCount >= 0 ? res.result.onCount : 0) : 0
          this.statisticsInfo.offCount = res.result ? (res.result.offCount >= 0 ? res.result.offCount : 0) : 0
          this.statisticsInfo.onCountRatio = res.result ? (res.result.onCountRatio >= 0 ? res.result.onCountRatio : 0) : 0
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // deptSelect() {
    //   getAction('device/momgDept/queryAllDepts').then(res => {
    //     if (res.code == 200) {
    //       this.selectOption = res.result
    //     } else {
    //       this.$message.error(res.message)
    //     }
    //   })
    // },
    //表单查询,点击查询按钮，默认查询第一页
    selfexport() {
      if (!this.searchKey && this.bsearchKey) {
        this.searchKey = this.bsearchKey
      }
      if (Array.isArray(this.searchKey)) {
        this.queryParam.deptId = this.searchKey.join(',')
      } else if (typeof this.searchKey === 'string') {
        this.queryParam.deptId = this.searchKey
        // this.searchKey = ""
      }
      this.handleExportXls('单位统计')
    },

    dosearch() {
      if (!this.searchKey && this.bsearchKey) {
        this.searchKey = this.bsearchKey
      }
      if (Array.isArray(this.searchKey)) {
        this.queryParam.deptId = this.searchKey.join(',')
      } else if (typeof this.searchKey === 'string') {
        this.queryParam.deptId = this.searchKey
        // this.searchKey = ""
      }
      if (this.startTime && this.endTime) {
        this.queryParam.startTime = this.startTime.format('YYYY-MM-DD')
        this.queryParam.endTime = this.endTime.format('YYYY-MM-DD')
      } else {
        this.queryParam.startTime = null
        this.queryParam.endTime = null
      }
      this.bsearchKey = ''
      this.loadData(1)
    },
    //表单重置
    doreset() {
      this.updateKey++
      this.startTime = moment().startOf('day').subtract(15, 'days') // 创建起始时间-筛选框
      this.endTime = moment().endOf('day').subtract(1, 'days') // 创建结束时间-筛选框
      this.queryParam.startTime = this.startTime.format('YYYY-MM-DD')
      this.queryParam.endTime = this.endTime.format('YYYY-MM-DD')
      //重置form表单，不重置tree选中节点
      this.queryParam = {
        deptId: ''
      }
      this.searchKey = undefined
      this.bsearchKey = ''
      this.loadData(1)
      this.type = undefined
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';

.col-example {
  margin-bottom: 16px;
}

.gutter-example {
  width: 100%;
}

.row-example {
  margin: 0px !important;
}

.gutter-row {
  padding-right: 0px !important;
}

.col-one {
  padding-left: 0px !important;
  border-radius: 3px;
  background-image: linear-gradient(90deg, #5d95fc 5%, #aecbff 97%);
}

.col-two {
  background-image: linear-gradient(90deg, #29ca7f 4%, #c3fcc1 98%);
  padding-left: 0px !important;
  border-radius: 3px;
}

.col-three {
  background-image: linear-gradient(90deg, #f44967 5%, #fdbac5 93%);
  padding-left: 0 !important;
  border-radius: 3px;
}

.col-four {
  background-image: linear-gradient(90deg, #49f46e 5%, #fdbac5 93%);
  padding-left: 0 !important;
  margin-right: 16px !important;
  border-radius: 3px;
}

.col-five {
  background-image: linear-gradient(90deg, #0099cc 5%, #aecbff 93%);
  padding-left: 0 !important;
  margin-right: 16px !important;
  border-radius: 3px;
}

.col-six {
  background-image: linear-gradient(90deg, #ff8247 5%, #ffd39b 93%);
  padding-left: 0 !important;
  margin-right: 16px !important;
  border-radius: 3px;
}

.col-seven {
  background-image: linear-gradient(90deg, #35cbd9 5%, #a4d3d7 93%);
  padding-left: 0 !important;
  margin-right: 16px !important;
  border-radius: 3px;
}

.gutter-box {
  height: 1rem /* 103/80 */;
  margin: 0.15rem /* 16/80 */ 0.15rem /* 24/80 */;
  padding: 0.125rem /* 10/80 */ 0.125rem;
  background: rgba(255, 255, 255, 0.1);

  .gutter-box-left-top {
    height: 50%;
    display: flex;
    justify-content: center;

    span {
      font-family: MicrosoftYaHei;
      font-size: 0.2rem /* 16/80 */;
      color: rgba(255, 255, 255, 0.85);
      display: flex;
      align-items: center;
    }
  }

  .gutter-box-left-bottom {
    height: 50%;
    display: flex;
    justify-content: center;

    span {
      font-family: Eurostile-Bold;
      font-size: 0.3rem /* 40/80 */;
      color: #ffffff;
      display: flex;
      align-items: center;
    }
  }
}

@media (min-width: 992px) {
  .ant-col-lg-3 {
    display: block;
    box-sizing: border-box;
    width: 14.28%;
  }
}

.div-table-container {
  /* padding: 18px 30px 18px 24px; */
  /* background-color: white; */
  //margin-top: 16px;
  /* margin-right: -9px; */
  /* height: calc(100% - 158px); */
}

.posi-col {
  position: relative;
}

.sync-img {
  position: absolute;
  top: 14px;
  right: 20px;
  cursor: pointer;
  color: #fff;
  z-index: 100;
}

.p-device-status {
  text-align: center;
  height: 30px;
  line-height: 30px;
  margin-bottom: 0px;
}

.span-title {
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
}

.span-num {
  font-family: PingFangSC-Medium;
  font-size: 24px;
}

.color-blue {
  color: #409eff;
}

.color-green {
  color: #139b33;
}

.color-red {
  color: #df1a1a;
}

.color-grey {
  color: #868686;
}

.ant-table-row-cell-break-word span a {
  color: #409eff !important;
}

//.ant-row .ant-col-3 .ant-card .ant-card-body {
//  height: 810px !important;
//}
/*表头样式*/
::v-deep .ant-table-thead > tr > th {
  text-align: center;
  white-space: nowrap;
}

/*内容对齐方式、省略显示*/
::v-deep .ant-table-tbody > tr > td {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

::v-deep .ant-table-footer {
  padding: 0px !important;
  //border: 0px !important;
}
</style>


