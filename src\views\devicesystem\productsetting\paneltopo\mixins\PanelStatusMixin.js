import { getAction } from '@/api/manage'
export default {
    data() {
        return {
            panelPortInfo:[],
        };
    },
    methods: {
        // 获取面板端口状态信息
        getDevicePortStatus(isRefresh) {
            if (this.deviceInfo) {
                let params = {
                    deviceCode: this.deviceInfo.deviceCode,
                    productId: this.productId,
                    topoType: this.value,
                }
                if(isRefresh){
                  params.connectStr = JSON.stringify({ oid: this.globalGridAttr.connectInfo.oid })
                }
                this.spinning = true;
                getAction("/net/device/devicePortInfo", params)
                    .then(res => {
                        this.spinning = false
                        // console.log("获取到的设备状态 === ", res)
                        if (res.success && res.result) {
                            let values = res.result.values
                            this.panelPortInfo = values;
                            if (values.length > 0) {
                                //给端口节点设置状态颜色
                                let config = this.globalGridAttr.topoConfig
                                let downColor = config.downColor;
                                let upColor = config.upColor;
                                let alarmColor = config.alarmColor;
                                let nodes = this.graph.getNodes();
                                nodes.forEach(el => {
                                    let data = el.data;
                                    el.attr('body/stroke', '')
                                    if (data.portIndex) {
                                        let statusInfo = values.find(item => data.portIndex === item.index.value)
                                        if (statusInfo) {
                                            let color = statusInfo.portStatus.value === "1" ? upColor : downColor
                                            el.setData({ iconColor: color })
                                            let alarmColor1 = statusInfo.alarmStatus && statusInfo.alarmStatus.value === 1 ? alarmColor : ""
                                            el.attr('body/stroke', alarmColor1)
                                        }
                                    }
                                });
                            }
                        }
                       
                    }).catch((error) => {
                        // console.log("获取状态失败啊啊 == ",error)
                        this.$message.warning("获取端口状态信息失败！")
                        this.spinning = false
                      })
            }

        }
    }
}