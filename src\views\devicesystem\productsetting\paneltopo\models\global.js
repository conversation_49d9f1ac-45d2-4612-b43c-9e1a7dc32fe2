
class TopoConfig {
  topoName = "";//拓扑名称
  topoType ="0";//拓扑面板类型 正反
  showType="0";//
  infoPopup = false;//信息弹窗
  topoConfig = this.initTopoConfig();
  connectInfo = this.initConnectInfo();
  physicalPortList = [];
  alarmTemplateId= "";
  bgImgList = []//拓扑背景图片列表
  edgeDashArr = ["0", "5", "5, 5, 1, 5"] //连线样式集合
  edgeMarkerArr = ['不显示', "起始箭头", "目标箭头", "双箭头"]//连线箭头集合
  edgeTypeArr = ["直线","折线","曲线"]
  shapeTypes = ["矩形","圆形"];
  shapeNames=["circle-node","switch-node","diamond-node"];
  nodeActiveKey="1";
  setTopoConfig(data) {
    if (data) {
      Object.assign(this.topoConfig,data)
    }
    else {
      let config = this.initTopoConfig();
      Object.assign(this.topoConfig,config)
    }
  }
  //初始化拓扑图配置
  initTopoConfig() {
    return {
      busCode:"",//自动应用拓扑图关联的应用
      labelColor: "#080808",
      upColor: "#52c41a",//已占用
      downColor: "#8c8c8c",//未占用
      unboundColor:"#8c8c8c",//未绑定
      boundColor:"#1677FF",//绑定
      alarmColor:"#ff0000",//告警
      edgeMarker: 0,
      edgeType: 0,
      edgeDash: "0",//字符串
      edgeStatus: true,
      edgeAni:false,
      aniTime:4,
      bgType: "1",
      bgColor: '#e5e5e5',
      bgimg: "",
      shapeType:0,
      maxX:"",
      minX:"",
      maxY:"",
      minY:"",
     
    }
  }
  setConnectInfo(info){
    if (info) {
      Object.assign(this.connectInfo,info)
    }
    else {
      Object.assign(this.connectInfo,this.initConnectInfo())
    }
  }
  initConnectInfo(){
    return {
      port : "",
      sAuth_passwd : "",
      ip : "",
      sAuth : "",
      snmpAuthLevel : "",
      spriv : "",
      community : "",
      spriv_passwd : "",
      version :"" ,
      username : "",
      oid:"*******.*******",
    }
  }
}

export const globalGridAttr = new TopoConfig();

export const bandwidths = [
  {key:"100M",value:"100M",text:"100M"},
  {key:"1000M",value:"1000M",text:"1000M"},
  {key:"8G",value:"8G",text:"8G"},
  {key:"10G",value:"10G",text:"10G"},
  {key:"40G",value:"40G",text:"40G"},
  {key:"100G",value:"100G",text:"100G"},
]