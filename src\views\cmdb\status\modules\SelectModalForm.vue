<template>
  <a-spin :spinning="confirmLoading">
    <a-input-search
      v-model="inputValue"
      ref="searchBar"
      :allowClear="true"
      placeholder="请输入内容按回车进行搜索"
      @search="onSearch"
    />
    <a-dropdown style="margin-top: 10px" :trigger="['click']" placement="bottomCenter">
      <a-menu slot="overlay">
        <a-menu-item key="1" @click="checkALL">全部勾选</a-menu-item>
        <a-menu-item key="2" @click="cancelCheckALL">取消全选</a-menu-item>
      </a-menu>
      <a-button> 树操作 <a-icon type="down" /> </a-button>
    </a-dropdown>
    <a-tree
      style="margin-top: 10px"
      checkable
      :treeData="treeData"
      :checkStrictly="true"
      @check="onCheck"
      @select="onSelect"
      @expand="onExpand"
      :autoExpandParent="autoExpandParent"
      :expandedKeys="expandedKeys"
      :checkedKeys="checkedKeys"
    >
      <template slot="title" slot-scope="{ title }">
        <span v-if="title.indexOf(searchValue) > -1">
          {{ title.substr(0, title.indexOf(searchValue)) }}
          <span style="color: #f50">{{ searchValue }}</span>
          {{ title.substr(title.indexOf(searchValue) + searchValue.length) }}
        </span>
        <span v-else>{{ title }}</span>
      </template>
    </a-tree>
  </a-spin>
</template>

<script>
import { assetsCategoryTreeList } from '@/api/api'
import { postAction } from '@/api/manage'
export default {
  name: 'SelectModalForm',
  // props:['modalWidth','multi','rootOpened','departId'],
  data() {
    return {
      checkedAll: [],
      inputValue: '',
      visible: false,
      confirmLoading: false,
      treeData: [],
      autoExpandParent: true,
      expandedKeys: [],
      dataList: [],
      checkedKeys: [],
      checkedRows: [],
      searchValue: '',
      multi: true,
      url: {
        configsave: '/status/cmdbStatus/configsave',
      },
      record: null,
    }
  },
  created() {
    this.loadDepart()
  },
  watch: {
    departId() {
      this.initDepartComponent()
    },
    visible: {
      handler() {
        if (this.departId) {
          this.checkedKeys = this.departId.split(',')
        } else {
          this.checkedKeys = []
        }
      },
    },
  },
  methods: {
    checkALL() {
      this.checkedKeys = this.checkedAll
    },
    cancelCheckALL() {
      this.checkedKeys = []
    },
    add() {
      this.edit({})
    },
    edit(record) {
      this.inputValue = ''
      this.searchValue = ''
      this.record = record
      var str = record.assetsCategoryId.toString()
      var arr = str.split(',')
      var list = new Array()
      for (let i = 0; i < arr.length; i++) {
        var num = arr[i].replace(/[^0-9]/gi, '')
        list.push(num)
      }
      record.assetsCategoryId = list
      this.checkedKeys = record.assetsCategoryId
      this.loadDepart()
    },
    show() {
      //清空操作
      this.visible = true
      this.checkedRows = []
      this.checkedKeys = []
    },
    loadDepart() {
      assetsCategoryTreeList({
        i: 'all'
      }).then((res) => {
        if (res.success) {
          res.result.forEach((e) => {
            this.checkedAll.push(e.key)
          })
          let arr = [...res.result]
          this.reWriterWithSlot(arr)
          this.treeData = arr
          this.initDepartComponent()
          if (this.rootOpened) {
            this.initExpandedKeys(res.result)
          }
        }
      })
    },
    initDepartComponent() {
      let names = ''
      if (this.departId) {
        let currDepartId = this.departId
        for (let item of this.dataList) {
          if (currDepartId.indexOf(item.key) >= 0) {
            names += ',' + item.title
          }
        }
        if (names) {
          names = names.substring(1)
        }
      }
      this.$emit('initComp', names)
    },
    reWriterWithSlot(arr) {
      for (let item of arr) {
        if (item.children && item.children.length > 0) {
          this.reWriterWithSlot(item.children)
          let temp = Object.assign({}, item)
          temp.children = {}
          this.dataList.push(temp)
        } else {
          this.dataList.push(item)
          item.scopedSlots = { title: 'title' }
        }
      }
    },
    initExpandedKeys(arr) {
      if (arr && arr.length > 0) {
        let keys = []
        for (let item of arr) {
          if (item.children && item.children.length > 0) {
            keys.push(item.id)
          }
        }
        this.expandedKeys = [...keys]
      } else {
        this.expandedKeys = []
      }
    },
    onCheck(checkedKeys, info) {
      if (!this.multi) {
        let arr = checkedKeys.checked.filter((item) => this.checkedKeys.indexOf(item) < 0)
        this.checkedKeys = [...arr]
        this.checkedRows = this.checkedKeys.length === 0 ? [] : [info.node.dataRef]
      } else {
        this.checkedKeys = checkedKeys.checked
        this.checkedRows = this.getCheckedRows(this.checkedKeys)
      }
    },
    onSelect(selectedKeys, info) {
      let keys = []
      keys.push(selectedKeys[0])
      if (!this.checkedKeys || this.checkedKeys.length === 0 || !this.multi) {
        this.checkedKeys = [...keys]
        this.checkedRows = [info.node.dataRef]
      } else {
        let currKey = info.node.dataRef.key
        if (this.checkedKeys.indexOf(currKey) >= 0) {
          this.checkedKeys = this.checkedKeys.filter((item) => item !== currKey)
        } else {
          this.checkedKeys.push(...keys)
        }
      }
      this.checkedRows = this.getCheckedRows(this.checkedKeys)
    },
    onExpand(expandedKeys) {
      this.expandedKeys = expandedKeys
      this.autoExpandParent = false
    },
    handleSubmit() {
      if (!this.checkedKeys || this.checkedKeys.length == 0) {
        this.$emit('ok', '')
      } else {
        this.$emit('ok', this.checkedRows, this.checkedKeys.join(','))
      }
      this.handleClear()
    },
    handleCancel() {
      this.handleClear()
    },
    handleClear() {
      this.visible = false
      this.checkedKeys = []
    },
    getParentKey(currKey, treeData) {
      let parentKey
      for (let i = 0; i < treeData.length; i++) {
        const node = treeData[i]
        if (node.children) {
          if (node.children.some((item) => item.key === currKey)) {
            parentKey = node.key
          } else if (this.getParentKey(currKey, node.children)) {
            parentKey = this.getParentKey(currKey, node.children)
          }
        }
      }
      return parentKey
    },
    onSearch(value) {
      const expandedKeys = this.dataList
        .map((item) => {
          if (item.title.indexOf(value) > -1) {
            return this.getParentKey(item.key, this.treeData)
          }
          return null
        })
        .filter((item, i, self) => item && self.indexOf(item) === i)

      Object.assign(this, {
        expandedKeys,
        searchValue: value,
        autoExpandParent: true,
      })
    },
    // 根据 checkedKeys 获取 rows
    getCheckedRows(checkedKeys) {
      const forChildren = (list, key) => {
        for (let item of list) {
          if (item.id === key) {
            return item
          }
          if (item.children instanceof Array) {
            let value = forChildren(item.children, key)
            if (value != null) {
              return value
            }
          }
        }
        return null
      }

      let rows = []
      for (let key of checkedKeys) {
        let row = forChildren(this.treeData, key)
        if (row != null) {
          rows.push(row)
        }
      }
      return rows
    },
    submitForm() {
      const params = {
        cmdbStatus: this.record,
        id: this.checkedKeys.join(','),
      }
      postAction(this.url.configsave, params).then((res) => {
        if (res.success) {
          this.$message.success('配置成功')
          this.$emit('ok')
        } else {
          this.$message.error(res.message)
        }
      })
    },
  },
}
</script>
