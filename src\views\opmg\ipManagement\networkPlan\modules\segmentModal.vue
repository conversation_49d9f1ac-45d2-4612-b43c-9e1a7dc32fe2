<template>
  <j-modal :title="title" :width="width" :visible="visible" switchFullscreen :destroyOnClose="true" :centered='true'
    @ok="handleOk" :okButtonProps="{ class:{'jee-hidden': disableSubmit} }" @cancel="handleCancel" cancelText="关闭">
    <segment-form ref="realForm" @ok="submitCallback" :disabled="disableSubmit">
    </segment-form>
  </j-modal>
</template>

<script>
  import segmentForm from './segmentForm'
  export default {
    name: 'segmentModal',
    components: {
      segmentForm
    },
    data() {
      return {
        title: '',
        width: '800px',
        visible: false,
        disableSubmit: false
      }
    },
    methods: {
      add({}, data) {
        this.visible = true
        this.$nextTick(() => {
          this.$refs.realForm.add({}, data);
        })
      },
      edit(record) {
        this.visible = true
        this.$nextTick(() => {
          this.$refs.realForm.show(record);
        })
      },
      close() {
        this.$emit('close');
        this.visible = false;
      },
      handleOk() {
        this.$refs.realForm.submitForm();
        this.$emit("refresh");
        // this.close();
      },
      submitCallback(e) {
        this.$emit('ok', e);
        this.visible = false;
      },
      handleCancel() {
        this.close()
      }
    }
  }
</script>
<style lang="less" scoped>
  @import '~@assets/less/normalModal.less';
</style>