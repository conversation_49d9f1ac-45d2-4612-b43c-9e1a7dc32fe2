<template>
  <a-row :gutter='10' style='height: 100%' class='vScroll'>
    <a-col style='width:100%;height: 100%;display: flex;flex-direction: column'>
      <!-- 查询区域 -->
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0', marginRight: '12px' }" class='card-style'
        style='width: 100%'>
        <div class='table-page-search-wrapper'>
          <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
            <a-row :gutter='24' ref="row">
              <a-col :span='spanValue'>
                <a-form-item label='作业名称'>
                  <a-input placeholder='请输入作业名称' v-model='queryParam.taskName' allowClear autocomplete='off'
                    :maxLength="maxLength"></a-input>
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label='关联场景'>
                  <a-input placeholder='请输入关联场景' v-model='queryParam.sceneName' allowClear autocomplete='off'
                    :maxLength="maxLength"></a-input>
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label='状态'>
                  <j-dict-select-tag :allowClear="true" v-model="queryParam.status" dictCode="quartz_status"
                    placeholder="请选择状态" />
                </a-form-item>
              </a-col>
              <a-col :span='colBtnsSpan()'>
                <span class='table-page-search-submitButtons'
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button type='primary' class='btn-search btn-search-style' @click='searchQuery'>查询</a-button>
                  <a-button class='btn-reset btn-reset-style' @click='searchReset'>重置</a-button>
                  <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>

            </a-row>
          </a-form>
        </div>
      </a-card>
      <!-- 操作按钮区域 -->
      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <div class='table-operator table-operator-style'>
          <a-button @click='handleAdd' type='primary'>新增</a-button>
          <a-dropdown v-if='selectedRowKeys.length > 0'>
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item key='1' @click='batchDel'>删除</a-menu-item>
            </a-menu>
            <a-button style='margin-left: 8px'> 批量操作
              <a-icon type='down' />
            </a-button>
          </a-dropdown>
        </div>
        <!-- table区域-begin -->
        <div>
          <a-table ref='table' bordered rowKey='id' :columns='columns' :dataSource='dataSource'
            :pagination='ipagination' :loading='loading' class='j-table-force-nowrap'
            :rowSelection='{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}' @change='handleTableChange'>
            <span slot='action' slot-scope='text, record'>
              <a @click="resumeJob(record)" v-if="record.status==='-1'">启用</a>
              <a @click="pauseJob(record)" v-if="record.status==='0'">禁用</a>
              <a-divider type='vertical' />
              <a-dropdown>
                <a class='ant-dropdown-link'>更多
                  <a-icon type='down' /></a>
                <a-menu slot='overlay'>
                  <a-menu-item><a @click="executeImmediately(record)" style="color: #409eff">执行一次</a></a-menu-item>
                  <a-menu-item><a @click="handleEdit(record)" style="color: #409eff">编辑</a></a-menu-item>
                  <a-menu-item>
                    <a-popconfirm title='确定删除吗?' @confirm='() => handleDelete(record.id)'>
                      <a style="color: #409eff">删除</a>
                    </a-popconfirm>
                  </a-menu-item>
                </a-menu>
              </a-dropdown>
            </span>
            <!-- 状态渲染模板 -->
            <template slot="customRenderStatus" slot-scope="status">
              <a-tag v-if="status==='0'" color="green">启用</a-tag>
              <a-tag v-if="status==='-1'" color="orange">禁用</a-tag>
            </template>
          </a-table>
        </div>
        <!-- table区域-end -->
      </a-card>
    </a-col>
    <!-- 表单区域 -->
    <auto-control-task-modal ref='modalForm' @ok='modalFormOk'></auto-control-task-modal>
  </a-row>
</template>

<script>
  import '@/assets/less/TableExpand.less'
  import AutoControlTaskModal from './modules/AutoControlTaskModal'
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import {
    getAction
  } from '@api/manage'
  import {
    YqFormSearchLocation
  } from '@/mixins/YqFormSearchLocation'

  export default {
    name: 'AutoControlTaskList',
    mixins: [JeecgListMixin, YqFormSearchLocation],
    components: {
      AutoControlTaskModal
    },
    data() {
      return {
        maxLength:50,
        description: '自动化控制作业管理管理页面',
        // 表头
        columns: [{
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: 'center',
            customRender: function (t, r, index) {
              return parseInt(index) + 1
            }
          },
          {
            title: '作业名称',
            align: 'center',
            dataIndex: 'taskName'
          },
          {
            title: '关联场景',
            align: 'center',
            dataIndex: 'sceneId_dictText'
          },
          {
            title: '状态',
            align: 'center',
            dataIndex: 'status',
            scopedSlots: {
              customRender: 'customRenderStatus'
            },
          },
          {
            title: '执行周期',
            align: 'center',
            dataIndex: 'taskCron'
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: 147,
            scopedSlots: {
              customRender: 'action'
            }
          }
        ],
        url: {
          list: '/autoControl/task/list',
          delete: '/autoControl/task/delete',
          deleteBatch: '/autoControl/task/deleteBatch',
          exportXlsUrl: '/autoControl/task/exportXls',
          importExcelUrl: '/autoControl/task/importExcel',
          pause: "/autoControl/task/pause",
          resume: "/autoControl/task/resume",
          execute: "/autoControl/task/execute"
        }
      }
    },
    computed: {
      importExcelUrl: function () {
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
      }
    },
    methods: {
      pauseJob: function (record) {
        var that = this;
        //暂停定时作业
        this.$confirm({
          title: "确认暂停",
          okText: '是',
          cancelText: '否',
          content: "是否暂停选中作业?",
          onOk: function () {
            getAction(that.url.pause, {
              id: record.id
            }).then((res) => {
              if (res.success) {
                that.$message.success(res.message);
                that.loadData();
                that.onClearSelected();
              } else {
                that.$message.warning(res.message);
              }
            });
          }
        });

      },
      resumeJob: function (record) {
        var that = this;
        //恢复定时作业
        this.$confirm({
          title: "确认启动",
          okText: '是',
          cancelText: '否',
          content: "是否启动选中作业?",
          onOk: function () {
            getAction(that.url.resume, {
              id: record.id
            }).then((res) => {
              if (res.success) {
                that.$message.success(res.message);
                that.loadData();
                that.onClearSelected();
              } else {
                that.$message.warning(res.message);
              }
            });
          }
        });
      },
      executeImmediately(record) {
        var that = this;
        //立即执行定时作业
        this.$confirm({
          title: "确认提示",
          okText: '是',
          cancelText: '否',
          content: "是否立即执行作业?",
          onOk: function () {
            getAction(that.url.execute, {
              id: record.id
            }).then((res) => {
              if (res.success) {
                that.$message.success(res.message);
                that.loadData();
                that.onClearSelected();
              } else {
                that.$message.warning(res.message);
              }
            });
          }
        });
      }
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less';
</style>