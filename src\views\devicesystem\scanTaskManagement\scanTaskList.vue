<template>
  <a-row :gutter="10" style="height: 100%;" class="vScroll">
    <a-col style="width:100%;height: 100%;display: flex;flex-direction: column">
      <!-- 查询区域 -->
      <a-card
        :bordered="false"
        :bodyStyle="{ paddingBottom: '0', marginRight: '12px' }"
        class="card-style"
        style="width: 100%"
      >
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="任务名称">
                  <a-input
                    :maxLength='maxLength'
                    placeholder="请输入任务名称"
                    v-model="queryParam.taskName"
                    :allowClear="true"
                    autocomplete="off"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="连接协议">
                  <a-select v-model='queryParam.transferProtocol' :allow-clear='true' placeholder='请选择连接协议'>
                    <a-select-option value='SNMP'>SNMP</a-select-option>
                    <a-select-option value='SSH'>SSH</a-select-option>
                    <a-select-option value='PING'>PING</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="启用状态">
                  <a-select v-model='queryParam.isEnable' :allow-clear='true' placeholder='请选择是否启用'>
                    <a-select-option value='1'>启用</a-select-option>
                    <a-select-option value='0'>禁用</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col v-show='toggleSearchStatus' :span='spanValue'>
                <a-form-item label="执行方式">
                  <a-select v-model='queryParam.executeType' :allow-clear='true' placeholder='请选择执行方式'>
                    <a-select-option value='1'>周期</a-select-option>
                    <a-select-option value='0'>手动</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col v-show='toggleSearchStatus' :span='spanValue'>
                <a-form-item label="所属网关">
                  <a-select v-model='queryParam.gatewayCode' :allow-clear='true' placeholder='请选择所属网关'>
                    <a-select-option
                      v-for="item in gatewayData"
                      :label="item.name"
                      :value="item.deviceCode"
                      :key="item.id"
                    >{{ item.name }}</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col v-show='toggleSearchStatus' :span='spanValue'>
                <a-form-item label="IP网段类型">
                  <a-select v-model='queryParam.filterType' :allow-clear='true' placeholder='请选择IP网段类型' @change="changeType">
                    <a-select-option value='-1'>全网</a-select-option>
                    <a-select-option value='0'>子网组</a-select-option>
                    <a-select-option value='1'>子网</a-select-option>
                    <a-select-option value='2'>网段</a-select-option>
                    <a-select-option value='3'>扩展</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col v-show='toggleSearchStatus && (queryParam.filterType==0||queryParam.filterType==1||queryParam.filterType==2)' :span='spanValue'>
                <a-form-item label="IP网段">
                  <a-select
                    v-model='queryParam.ipFilter'
                    :allowClear="true"
                    placeholder="请选择IP网段"
                  >
                    <a-select-option v-for="item in auditList" :key="item.id">{{item.title}}</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span='colBtnsSpan()'>
                <span :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                      class='table-page-search-submitButtons'>
                  <a-button class='btn-search btn-search-style' type='primary' @click='searchQuery'>查询</a-button>
                  <a-button class='btn-reset btn-reset-style' @click='searchReset'>重置</a-button>
                  <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered="false" style="width: 100%; flex: auto">
        <!-- 操作按钮区域 -->
        <div class="table-operator table-operator-style">
          <a-button @click="handleAdd">新增</a-button>
          <a-dropdown v-if="selectedRowKeys.length > 0">
            <a-menu slot="overlay" style="text-align: center">
              <a-menu-item key="1" @click="batchDel">删除</a-menu-item>
            </a-menu>
            <a-button>
              批量操作
              <a-icon type="down" />
            </a-button>
          </a-dropdown>
        </div>
        <a-table
          ref="table"
          bordered
          rowKey="id"
          :columns="columns"
          :dataSource="dataSource"
          :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          :pagination="ipagination"
          :loading="loading"
          @change="handleTableChange"
        >
          <template slot="tooltip" slot-scope="text">
            <a-tooltip placement="top" :title="text" trigger="hover">
              <div class="tooltip">{{ text }}</div>
            </a-tooltip>
          </template>
          <template slot="isEnable" slot-scope="text, record">
            <a-tag v-if="record.isEnable==1" color="green">启用</a-tag>
            <a-tag v-else color="orange">禁用</a-tag>
          </template>
          <template slot="executeType" slot-scope="text, record">
            <span v-if="record.executeType==1">周期</span>
            <span v-else>手动</span>
          </template>
          <template slot="filterType" slot-scope="text, record">
            <span v-if="record.filterType==='-1'">全网</span>
            <span v-if="record.filterType==='0'">子网组</span>
            <span v-if="record.filterType==='1'">子网</span>
            <span v-if="record.filterType==='2'">网段</span>
            <span v-if="record.filterType==='3'">扩展</span>
          </template>
           <template slot="ipName" slot-scope="text, record">
            <span v-if="record.filterType==='3'">{{record.customIpSegment}}</span>
            <span v-else>{{text}}</span>
          </template>
          <template slot="scanRecordNum" slot-scope="text, record">
            <span
              v-if="record.scanRecordNum>0"
              @click="goDevicePage(record)"
              style="color: #409eff;cursor:pointer;font-weight:bold"
            >{{record.scanRecordNum}}</span>
            <span v-else>0</span>
          </template>
          <span slot="action" slot-scope="text, record">
            <a @click="handleDetailPage(record)" style="color: #409eff">查看</a>
            <a-divider type="vertical" />
            <a-dropdown>
              <a class="ant-dropdown-link">
                更多
                <a-icon type="down" />
              </a>
              <a-menu slot="overlay">
                <a-menu-item @click="handleEdit(record)" class="overlay">编辑</a-menu-item>
                <a-menu-item>
                  <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                    <a>删除</a>
                  </a-popconfirm>
                </a-menu-item>
                <!-- <a  style="color: #409eff">编辑</a> -->
                <a-menu-item class="overlay" @click="executeImmediately(record)">立即执行</a-menu-item>
              </a-menu>
            </a-dropdown>
          </span>
        </a-table>
      </a-card>
      <scan-task-modal ref="modalForm" @ok="modalFormOk"></scan-task-modal>
    </a-col>
  </a-row>
</template>

<script>
import { getAction, putAction } from '@/api/manage'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
import scanTaskModal from './modules/scanTaskModal'
export default {
  description: '扫描任务管理页面',
  name: 'scanTaskList',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components: {
    scanTaskModal
  },
  data() {
    return {
      maxLength:50,
      queryParam: {
        ipFilter:undefined
      },
      // 表头
      columns: [
        {
          title: '任务名称',
          dataIndex: 'taskName',
          scopedSlots: {
            customRender: 'tooltip'
          },
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 150px;max-width:300px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '所属网关',
          dataIndex: 'gatewayName',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 150px;max-width:350px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: 'IP网段类型',
          dataIndex: 'filterType',
          scopedSlots: {
            customRender: 'filterType'
          },
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 150px;max-width:300px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: 'IP网段',
          dataIndex: 'ipName',
          scopedSlots: {
            customRender: 'ipName'
          },
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 150px;max-width:300px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '扫描频率',
          dataIndex: 'executeCron',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 150px;max-width:300px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '连接协议',
          dataIndex: 'transferProtocol',
          customCell: () => {
            let cellStyle = 'text-align: center;width: 120px;'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '启用状态',
          dataIndex: 'isEnable',
          scopedSlots: {
            customRender: 'isEnable'
          },
          customCell: () => {
            let cellStyle = 'text-align: center;width: 100px;'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '执行方式',
          dataIndex: 'executeType',
          scopedSlots: {
            customRender: 'executeType'
          },
          customCell: () => {
            let cellStyle = 'text-align: center;width: 100px;'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '执行次数',
          dataIndex: 'executeTimes',
          customCell: () => {
            let cellStyle = 'text-align: center;width: 100px;'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '发现设备数',
          dataIndex: 'scanRecordNum',
          scopedSlots: {
            customRender: 'scanRecordNum'
          },
          customCell: () => {
            let cellStyle = 'text-align: center;width: 100px;'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 147,
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        // list: '/device/deviceGroup/list',
        list: '/deviceScan/task/list',
        delete: '/deviceScan/task/delete',
        deleteBatch: '/deviceScan/task/deleteBatch',
        execute: '/deviceScan/task/execute', // 执行一次
        gatewayList: '/configureBack/task/getGatewayList', //  获取所属网关
        queryListByType: '/devops/ip/queryListByType' // 获取ip网段
      },
      gatewayData: [], // 网关数据
      auditList:[] // ip网段
    }
  },
  created(){
    this.gatewayList()
  },
  methods: {
        // 网关下拉框
    gatewayList() {
      getAction(this.url.gatewayList).then(res => {
        if (res.success) {
          this.gatewayData = res.result
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    changeType(e) {
      this.queryParam.ipFilter = undefined
      this.getType(e)
    },
    // 获取网段
    getType(value) {
      if (!value) {
        this.auditList = []
        return
      }
      getAction(this.url.queryListByType, {
        type: value
      }).then(res => {
        if (res.success && res.result) {
          this.auditList = res.result
        }
      })
    },
    executeImmediately(record) {
      var that = this
      //立即执行定时任务
      this.$confirm({
        title: '确认提示',
        okText: '是',
        cancelText: '否',
        content: '是否立即执行任务?',
        onOk: function() {
          putAction(that.url.execute, record).then(res => {
            if (res.success) {
              that.$message.success('操作成功，后台服务正在处理，稍后刷新页面可查看扫描结果')
              that.loadData()
              that.onClearSelected()
            } else {
              that.$message.warning(res.message)
            }
          })
        }
      })
    },
    // 查看设备
    goDevicePage: function(record) {
      this.$parent.pButton2(2, record)
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
</style>