export const mapOption = {
  attribute: {
    //地图map使用区划代码控制
    map: 'china',
    titleText: '',
    titleFontSize: 20,
    titleColor: '#ffffff',
    titleLeft: 'center',
    titleTop: 10,
    subtext: '',
    subTitleColor: '#ccc',
    subTitleFontSize: 13,
    roam: true,
    seriesName: '运行状态',
    zoom: 1.2,
    geoLabelShow: true,
    geoLabelColor: '#DDEAF8',
    geoLabelSize: 10,
    geoAreaColor: '#156399',
    geoBorderColor: '#EAF1F8',
    geoBorderType: 'solid',
    emphasisAreaColor: '#156399',
    emphasisBorderColor: '#EAF1F8',
    emphasisLabelColor: '#DDEAF8',
    selectable: false,
    changeable: true,
    mapBackColor: '#ccc',
    mapBackTitle: '返回上级',
    mapBackSize: 20,
    mapBackRight: '20',
    mapBackTop: '20',
    mapBackColorH: '#ccc',
    linesColor: '#04731E',
    linesWidth: 1,
    linesType: 'solid',
    linesCurveness: 0.2,
    linesOpacity: 0.5,
    effectShow: true,
    effectSymbol: 'arrow',
    effectSymbolSize: 8,
    effectPeriod: 4,//动画的时间
    effectTrailShow: true,
    effectTrailLength: 0.7,
    effectColor: '#04731E',
    effectTrailColor: '#fff',
    effectSymbolPath: 'path://M1705.06,1318.313v-89.254l-319.9-221.799l0.073-208.063c0.521-84.662-26.629-121.796-63.961-121.491c-37.332-0.305-64.482,36.829-63.961,121.491l0.073,208.063l-319.9,221.799v89.254l330.343-157.288l12.238,241.308l-134.449,92.931l0.531,42.034l175.125-42.917l175.125,42.917l0.531-42.034l-134.449-92.931l12.238-241.308L1705.06,1318.313z',

    scatterSymbol: 'circle',
    scatterSymbolSize: 8,
    scatterSymbolColor: '#1BC145',
    scatterLabelShow: false,
    scatterLabelPos: 'right',
    scatterLabelFormatter: '{b}',
    scatterRippleType: 'stroke',
    scatterRipplePeriod: 4,
    scatterRippleScale: 2.5,
    scatterSymbolPath: 'path://M30.9,53.2C16.8,53.2,5.3,41.7,5.3,27.6S16.8,2,30.9,2C45,2,56.4,13.5,56.4,27.6S45,53.2,30.9,53.2z M30.9,3.5C17.6,3.5,6.8,14.4,6.8,27.6c0,13.3,10.8,24.1,24.101,24.1C44.2,51.7,55,40.9,55,27.6C54.9,14.4,44.1,3.5,30.9,3.5z M36.9,35.8c0,0.601-0.4,1-0.9,1h-1.3c-0.5,0-0.9-0.399-0.9-1V19.5c0-0.6,0.4-1,0.9-1H36c0.5,0,0.9,0.4,0.9,1V35.8z M27.8,35.8 c0,0.601-0.4,1-0.9,1h-1.3c-0.5,0-0.9-0.399-0.9-1V19.5c0-0.6,0.4-1,0.9-1H27c0.5,0,0.9,0.4,0.9,1L27.8,35.8L27.8,35.8z',
    ripples: {
      'china': {
        'position': window.ZrMapCities || {
          '北京': [116.4074, 39.9042],
          '上海': [121.4648, 31.2891],
          '包头': [110.3467, 41.4899],
          '乌鲁木齐': [87.9236, 43.5883],
          '拉萨': [91.1865, 30.1465],
          '重庆': [107.7539, 30.1904],
          '大连': [122.2229, 39.4409],
          '南昌': [116.0046, 28.6633],
          '南京': [118.8062, 31.9208],
          '福州': [119.4543, 25.9222],
          '徐州': [117.5208, 34.3268],
          '厦门': [118.1689, 24.6478],
          '南宁': [108.479, 23.1152],
          '广州': [113.5107, 23.2196],
          '昆明': [102.9199, 25.4663],
          '柳州': [109.3799, 24.9774],
          '兰州': [103.5901, 36.3043],
          '成都': [103.9526, 30.7617],
          '西宁': [101.4038, 36.8207],
          '济南': [117.1582, 36.8701],
          '长春': [125.8154, 44.2584],
          '沈阳': [123.1238, 42.1216],
          '哈尔滨': [127.9688, 45.368],
          '石家庄': [114.4995, 38.1006],
          '保定': [115.0488, 39.0948],
          '郑州': [113.4668, 34.6234],
          '邯郸': [114.4775, 36.535]
        }
        // "lines":[["北京","上海"],["北京","包头"]],
      }
    }
  }
}