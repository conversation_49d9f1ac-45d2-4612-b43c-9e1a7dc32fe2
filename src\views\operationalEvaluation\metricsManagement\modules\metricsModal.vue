<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    :destroyOnClose="true"
    :centered="true"
    switchFullscreen
    @ok="handleOk"
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }"
    @cancel="handleCancel"
    cancelText="关闭"
  >
    <metrics-form ref="realForm" :metricsTypeId="metricsTypeId" @ok="submitCallback"> </metrics-form>
  </j-modal>
</template>

<script>
import metricsForm from './metricsForm'
export default {
  name: 'metricsModal',
  props: ['metricsTypeId'],
  components: {
    metricsForm,
  },
  watch: {
    metricsTypeId(val) {
      // 监听到类别的变化
    },
  },
  data() {
    return {
      title: '',
      width: '800px',
      visible: false,
      disableSubmit: false,
    }
  },
  methods: {
    add() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.add()
      })
    },
    edit(record) {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.edit(record)
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      this.$refs.realForm.submitForm()
    },
    submitCallback() {
      this.$emit('ok')
      this.visible = false
    },
    handleCancel() {
      this.close()
    },
  },
}
</script>
<style lang="less" scoped>
@import '~@assets/less/normalModal.less';
</style>
