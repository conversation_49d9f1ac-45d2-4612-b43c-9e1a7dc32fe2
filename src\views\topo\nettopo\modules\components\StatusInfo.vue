<template>
  <div class="status-info" style="infoStyle"></div>
</template>

<script>
export default {
  props: {
    top: {
      type: Number,
      default: 0,
    },
    right: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {}
  },
  created() {},
  computed: {
    infoStyle() {
      return {
        top: this.top+'px',
        right: this.right+'px',
      }
    },
  },
}
</script>

<style scoped lang="less">
.status-info {
  min-height: 20px;
  min-width: 200px;
  overflow: hidden;
  background-color: rgba(255, 255, 255, 0.6);
  border-radius: 5px;
  position: absolute;
  z-index: 1000;
  padding: 10px;
  top: 0px;
  right: 0px;
}
</style>