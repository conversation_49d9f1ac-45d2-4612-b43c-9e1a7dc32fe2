<template>
  <div style="height:100%;">
    <keep-alive exclude='deviceReportingDetails'>
      <component :is="pageName" style="height:100%" :data="data"/>
    </keep-alive>
  </div>
</template>
<script>
import deviceReportingList from './deviceReportingList'
import deviceReportingDetails from './deviceReportingDetails'
export default {
  name: "deviceReportingManage",
  data() {
    return {
      isActive: 0,
      data:{}
    };
  },
  components: {
    deviceReportingList,
    deviceReportingDetails
  },
  created(){
    this.pButton1(0);
  },
  //使用计算属性
  computed: {
    pageName() {
      switch (this.isActive) {
        case 0:
          return "deviceReportingList";
          break;

        default:
          return "deviceReportingDetails";
          break;
      }
    }
  },
  methods: {
    pButton1(index) {
      this.isActive = index;
    },
    pButton2(index,item) {
      this.isActive = index;
      this.data = item;
    }
  }
}
</script>