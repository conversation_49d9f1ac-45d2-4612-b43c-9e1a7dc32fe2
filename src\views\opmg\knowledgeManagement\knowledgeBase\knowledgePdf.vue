<template>
  <div class='knowledge-wrapper'>
    <div class='print-wrapper' id='printContent'></div>
  </div>

</template>
<script>

export default {
  data() {
    return {}
  },
  mounted() {
    var htmlString = localStorage.getItem('knowledgeHtmlString');
    var htmlTitle = localStorage.getItem('knowledgeHtmlTitle');

    document.getElementById('printContent').innerHTML = htmlString;
    // 在组件加载后自动触发打印
    setTimeout(()=>{
      window.print()
    },500)
   /* this.$nextTick(() => {
      window.print()
    });*/
  }
}


</script>

<style scoped lang="less">
.knowledge-wrapper {
  min-height: 100%;
  background-color: #f0f2f5;
  color: rgba(0, 0, 0, 0.65);
  overflow: hidden;
  overflow-y: auto;

  .print-wrapper{
    height: 100%;
    border-radius: 3px;
    background: #fff;
    overflow-x: hidden;

    img{
      max-width: 100%;
    }
  }
  @media (min-width: 1600px){
    .print-wrapper{
      width: 60%;
      margin:16px auto;
      padding: 24px;
    }
  }
  @media (min-width: 1200px) and (max-width: 1600px){
    .print-wrapper{
      width: 70%;
      margin:16px auto;
      padding: 24px;
    }
  }
  @media (min-width: 992px) and (max-width: 1200px){
    .print-wrapper{
      width: 80%;
      margin:16px auto;
      padding: 24px;
    }
  }
  @media (max-width: 992px) {
    .print-wrapper{
      width: 100%;
      padding: 24px;
      margin: 0px;
    }
  }
}
</style>