<template>
  <a-card :bordered="false" style="height: 100%">
    <!-- 按钮操作区域 -->
    <a-row style="margin-left: 14px">
      <!--<a-button @click="refresh" type="default" icon="reload" :loading="loading">刷新</a-button>-->
    </a-row>
    <div style="background: #fff;height: 100%; margin-top: 5px;height:100%">
      <a-input-search @change="onChange" v-model="inputSearch" style="width:100%;margin-top: 10px" placeholder="请输入类型名称" />
      <!-- <a-input-search @search="onSearch" style="width:100%;margin-top: 10px" placeholder="请输入类型名称" /> -->
      <!-- 树-->
      <a-col :md="10" :sm="24" style="height: 100%;">
        <template>
          <a-dropdown :trigger="[this.dropTrigger]" @visibleChange="dropStatus">
            <span style="user-select: none">
              <a-tree
                @select="onSelect"
                :selectedKeys="selectedKeys"
                :checkedKeys="checkedKeys"
                :treeData="assetsCategoryTree"
                :checkStrictly="checkStrictly"
                :expandedKeys="expandedKeys"
                :autoExpandParent="autoExpandParent"
                @expand="onExpand"
              >
                <template slot="title" slot-scope="{ title }">
                  <span v-if="title.indexOf(searchValue) > -1 && title !== firstTitle">
                    {{ title.substr(0, title.indexOf(searchValue)) }}
                    <span style="color: #f50">{{ searchValue }}</span>
                    {{ title.substr(title.indexOf(searchValue) + searchValue.length) }}
                  </span>
                  <span v-else-if="title.indexOf(searchValue) > -1 && title === firstTitle">
                    <span style="background-color: #bae7ff;color: #f50">{{ title }}</span>
                  </span>
                  <span v-else>{{ title }}</span>
                </template>
              </a-tree>
            </span>
          </a-dropdown>
        </template>
      </a-col>
    </div>
  </a-card>
</template>
<script>
import { queryDeptTreeList } from '@/api/device'
//通过Key获取对应地title
const getTitleByKey = (key, tree) => {
  let selectTitle
  for (let i = 0; i < tree.length; i++) {
    const node = tree[i]
    if (node.key === key) {
      selectTitle = node.title
      break
    }
    if (node.children) {
      selectTitle = getTitleByKey(key, node.children)
    }
  }
  return selectTitle
}
//子节点匹配，获取父节点，用于展开tree
const getParentKey = (key, tree) => {
  let parentKey
  for (let i = 0; i < tree.length; i++) {
    const node = tree[i]
    if (node.children) {
      if (node.children.some(item => item.key === key)) {
        parentKey = node.key
      } else if (getParentKey(key, node.children)) {
        parentKey = getParentKey(key, node.children)
      }
    }
  }
  return parentKey
}
//生成tree节点的数组[{ key, title: node.title }]
const dataList = []
const generateList = data => {
  for (let i = 0; i < data.length; i++) {
    const node = data[i]
    const key = node.key
    dataList.push({ key, title: node.title })
    if (node.children) {
      generateList(node.children)
    }
  }
}
//为tree生成对应地title slot
const generateSlotScopes = data => {
  for (let i = 0; i < data.length; i++) {
    // var info = data[i];
    data[i].scopedSlots = { title: 'title' }
    if (data[i].children) {
      generateSlotScopes(data[i].children)
    }
  }
}
export default {
  name: 'device-tree',
  data() {
    return {
      firstTitle: '', //存储搜素tree的第一个title
      inputSearch:'',
      // 树
      assetsCategoryTree: [],
      treeData: [],
      expandedKeys: [],
      searchValue: '',
      autoExpandParent: true,
      dropTrigger: '',
      selectedKeys: [],
      selectedTitle: '',
      checkedKeys: [],
      checkStrictly: true,
    }
  },
  watch:{
    // init(val, oldVal){
    //   if(val){
    //     this.selectedKeys = [];
    //     this.firstTitle="";
    //   }
    // }
  },
  created() {
    this.loadTree()
  },
  methods: {
    // 查询树
    loadTree() {
      var that = this
      that.treeData = []
      that.assetsCategoryTree = []
      queryDeptTreeList().then(res => {
        if (res.success) {
          // 部门全选后，再添加部门，选中数量增多
          this.allTreeKeys = []
          if (res.result.length > 0) {
            generateSlotScopes(res.result)
            that.assetsCategoryTree = [...res.result]
          }
          this.loading = false
          generateList(that.assetsCategoryTree)
        }
      })
    },
    //暂时废弃
    onSearch(value) {
      let that = this
      if (value) {
        searchByConfigItemtype({ typeName: value }).then(res => {
          if (res.success) {
            that.assetsCategoryTree = []
            for (let i = 0; i < res.result.length; i++) {
              let temp = res.result[i]
              that.assetsCategoryTree.push(temp)
            }
          } else {
            that.$message.warning(res.message)
          }
        })
      } else {
        that.loadTree()
      }
    },
    // 右键点击下拉框改变事件
    dropStatus(visible) {
      if (visible == false) {
        this.dropTrigger = ''
      }
    },
    // 选择树的方法
    onSelect(selectedKeys, e) {
      this.selectedKeys = selectedKeys;
      this.firstTitle = '';
      this.assetsCategoryId = e.selectedNodes[0].data.props.dataRef.key;
      //向父组件弹射抛值
      this.$emit('selected', this.assetsCategoryId);
    },
    //tree的查询框输入时，默认选中匹配的第一个（用firstTitle表示）
    onChange(e) {
      const value = e.target.value
      this.searchValue = value
      //查询框第一个匹配的node对应的key
      let firstSearchedKey = ''
      const expandedKeys = dataList
        .map(item => {
          if (item.title.indexOf(value) > -1) {
            //查询框第一个匹配的node对应的key
            if(firstSearchedKey == ''){
              firstSearchedKey = item.key
            }
            return getParentKey(item.key, this.assetsCategoryTree)
          }
          return null
        })
        .filter((item, i, self) => item && self.indexOf(item) === i)
      Object.assign(this, {
        expandedKeys,
        searchValue: value,
        autoExpandParent: true
      })
      if (this.expandedKeys.length > 0 && value.trim().length > 0) {
        this.firstTitle = getTitleByKey(firstSearchedKey, this.assetsCategoryTree)
        this.selectedKeys = [firstSearchedKey];
        //向父组件弹射抛值
        this.$emit('selected', firstSearchedKey);
      }
      if (value.trim().length == 0) {
        //查询设备信息,此情况下，没有分类被选中
        this.firstTitle = ''
        this.selectedKeys = []
        //向父组件弹射抛值
        this.$emit("selected");
      }
    },
    onCheck(checkedKeys, e) {
      this.hiding = false
      // this.checkedKeys = checkedKeys.checked
      // <!---- author:os_chengtgen -- date:20190827 --  for:切换父子勾选模式 =======------>
      if (this.checkStrictly) {
        this.checkedKeys = checkedKeys.checked
      } else {
        this.checkedKeys = checkedKeys
      }
      // <!---- author:os_chengtgen -- date:20190827 --  for:切换父子勾选模式 =======------>
    },
    // 右键操作方法
    rightHandle(node) {
      this.dropTrigger = 'contextmenu'
      this.rightClickSelectedKey = node.node.eventKey
      this.rightClickSelectedBean = node.node.dataRef
    },
    onExpand(expandedKeys) {
      // if not set autoExpandParent to false, if children expanded, parent can not collapse.
      // or, you can remove all expanded children keys.
      this.expandedKeys = expandedKeys
      this.autoExpandParent = false
    },
  }
}
</script>
<style lang="less" scoped>
@import '~@assets/less/common.less';
</style>