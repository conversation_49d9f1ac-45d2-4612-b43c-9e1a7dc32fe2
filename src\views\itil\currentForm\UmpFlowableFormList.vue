<template>
  <!-- 查询区域 -->
  <a-card :bordered="false" style="height: 100%; overflow: hidden; overflow-y: auto" class="vScroll zxw">
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24"> </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->
    <!-- 操作按钮区域 -->
    <div class="table-operator tableBottom" style="flex: auto">
      <a-button @click="handleAdd">新增</a-button>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay" style='text-align: center'>
          <a-menu-item key="1" @click="batchDel">删除</a-menu-item>
        </a-menu>
        <a-button> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>
    </div>
    <!-- table区域-begin -->
    <div>
      <a-table
        ref="table"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        class="j-table-force-nowrap"
        @change="handleTableChange"
      >
        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 14px">无此图片</span>
          <img v-else :src="getImgView(text)" height="25px" alt="加载失败" style="max-width: 80px; font-size: 14px" />
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 14px">无此文件</span>
          <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="uploadFile(text)">
            下载
          </a-button>
        </template>
        <span slot="action" slot-scope="text, record" class="caozuo">
          <a @click="handleEdit(record)">编辑</a>
          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a href="javascript:;" @click="see(record)" style="color: #409eff">浏览表单</a>
              </a-menu-item>
              <a-menu-item>
                <a href="javascript:;" @click="btnUpdateFormJson(record)" style="color: #409eff">修改表单</a>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a style="color: #409eff">删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>
      </a-table>
    </div>
    <!--查看-->
    <code-type-dialog ref="modalCode" />
    <umpFlowableForm-modal ref="modalForm" @ok="modalFormOk"></umpFlowableForm-modal>
  </a-card>
</template>

<script>
// import '@/assets/less/TableExpand.less'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import UmpFlowableFormModal from './modules/UmpFlowableFormModal'
import CodeTypeDialog from './CodeTypeDialog'

export default {
  name: 'UmpFlowableFormList',
  mixins: [JeecgListMixin],
  components: {
    UmpFlowableFormModal,
    CodeTypeDialog,
  },
  data() {
    return {
      dialogVisible: false,
      description: '表单管理页面',
      // 表头
      columns: [
        {
          title: '表单编码',
          align: 'center',
          dataIndex: 'formKey',
        },
        {
          title: '表单名称',
          align: 'center',
          dataIndex: 'formName',
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          // fixed:"right",
          width: 200,
          scopedSlots: { customRender: 'action' },
        },
      ],
      url: {
        list: '/flowableform/umpFlowableForm/list',
        delete: '/flowableform/umpFlowableForm/delete',
        deleteBatch: '/flowableform/umpFlowableForm/deleteBatch',
        // exportXlsUrl: "/flowableform/umpFlowableForm/exportXls",
        // importExcelUrl: "flowableform/umpFlowableForm/importExcel",
      },
      dictOptions: {},
    }
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
  },
  methods: {
    initDictConfig() {},
    btnUpdateFormJson(record) {
      // 表单修改 以前的不能用了 换成新的
      this.formTitle = record.formName;
      this.$refs.flowableFormEdit.show(record.formKey)
    },
    see(record) {
      this.$refs.modalCode.see(record)
      this.$refs.modalCode.title = '浏览表单'
    },
  },
}
</script>
<style lang="less" scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
</style>