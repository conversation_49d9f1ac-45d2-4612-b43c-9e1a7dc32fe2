<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="disabled">
      <a-form-model ref='form' :model='model' :rules='validatorRules' slot='detail' v-bind='formItemLayout'>
        <a-row>
<!--          <a-col :span="24">
            <a-form-model-item label="任务类型" prop='resourceType'>
              <j-dict-select-tag
                type="radio"
                defaultChecked="true"
                v-model='model.resourceType'
                dictCode="resource_type"
                placeholder="请选择任务类型"
              />
            </a-form-model-item>
          </a-col>-->

          <a-col :span="24">
            <a-form-model-item label="数据源资源" prop='datasourceId'>
              <a-select
                v-model='model.datasourceId'
                :allow-clear='true'
                placeholder="请选择数据源资源">
                <a-select-option v-for='item in datasourceList' :disabled="model.datadestId&&item.id===model.datadestId" :key='"datasourceId_" + item.id' :label='item.resourceName'
                                 :value='item.id'>
                  {{ item.resourceName }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="目的端资源" prop='datadestId'>
              <a-select
                v-model='model.datadestId'
                :allow-clear='true'
                placeholder="请选择目的端资源">
                <a-select-option v-for='item in datasourceList' :disabled="model.datasourceId&&item.id===model.datasourceId" :key='"datadestId_" + item.id' :label='item.resourceName'
                                 :value='item.id'>
                  {{ item.resourceName }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="任务名称" prop='taskName'>
              <a-input
                v-model='model.taskName'
                :allowClear="true"
                autocomplete="off"
                placeholder="请输入任务名称"
              ></a-input>
            </a-form-model-item>
          </a-col>

          <a-col :span="24">
            <a-form-model-item label="备份数据源" prop='backupDatasourceId'>
              <a-input
                v-model='model.backupDatasourceId'
                :allowClear="true"
                autocomplete="off"
                placeholder="请输入备份数据源"
              ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="备份目的地" prop='backupDatadestId'>
              <a-input
                v-model='model.backupDatadestId'
                :allowClear="true"
                autocomplete="off"
                placeholder="请输入备份目的地"
              ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="备份策略" prop='proId'>
              <a-select
                v-model='model.proId'
                :allow-clear='true'
                placeholder="请选择备份策略">
                <a-select-option v-for='item in proIdList' :key='"proId_" + item.id' :label='item.proName'
                                 :value='item.id'>
                  {{ item.proName }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="状态" prop='execstate'>
              <j-dict-select-tag
                type="radio"
                v-model='model.execstate'
                dictCode="quartz_status" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import pick from 'lodash.pick'
import { validateDuplicateValue } from '@/utils/util'
import JFormContainer from '@/components/jeecg/JFormContainer'
import JDictSelectTag from '@/components/dict/JDictSelectTag'
import { duplicateCheck } from '@/api/api'

export default {
  name: 'DevopsBackupTaskForm',
  components: {
    JFormContainer,
    JDictSelectTag,
  },
  props: {
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false,
    },
  },
  data() {
    return {
      confirmLoading: false,
      formItemLayout: {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 24 },
          md: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 24 },
          md: { span: 16 }
        }
      },
      model: {},
      datasourceList:[],
      proIdList:[],

      validatorRules: {
       /* resourceType: [
          { required: true, message: '请选择任务类型'}],*/
        datasourceId: [
          { required: true, message: '请选择数据源资源'}],
        datadestId:[
          { required: true, message: '请选择目的端资源'}],
        taskName: [
          {required: true, message: '请输入任务名称', trigger: 'blur' },
          { min: 2, max: 30, message: '长度在2到30个字符', trigger: 'blur' },
          { validator: this.validateTaskName ,trigger: 'blur' },
        ],
        backupDatasourceId: [
          { required: true, pattern: '(^//.|^/|^[a-zA-Z])?:?/.+(/$)?', message: '路径为空或输入的路径符号未使用/'}],
        backupDatadestId: [
          { required: true, pattern: '(^//.|^/|^[a-zA-Z])?:?/.+(/$)?', message: '路径为空或输入的路径符号未使用/' }
        ],
        proId: [
          {required: true,message:'请选择备份策略' }
        ],
        execstate: [
          { required: true, message: '请选择状态'}],
      },
      url: {
        add: '/devopsBackupTask/devopsBackupTask/add',
        edit: '/devopsBackupTask/devopsBackupTask/edit',
        queryById: '/devopsBackupTask/devopsBackupTask/queryById',
        queryProIds:'/devopsbackuppro/devopsBackupPro/list',
        queryDatasourceList:'/devopsbackupresource/devopsBackupResource/list'
      },
    }
  },
  created() {
    this.getDatasourceList()
    this.getProIds()
  },
  methods: {
    /*获取备份策略下拉数据*/
    getProIds(){
      getAction(this.url.queryProIds,{pageSize:-1}).then((res) => {
        if (res.success&&res.result&&res.result.records) {
          this.proIdList=res.result.records
        }else {
          this.proIdList=[]
          if (!res.success){
            this.$message.warning(res.message)
          }
        }
      }).catch((err)=>{
        this.$message.error(err.message)
      })
    },
    /*获取资源下拉数据*/
    getDatasourceList(){
      getAction(this.url.queryDatasourceList,{pageSize:-1}).then((res) => {
        if (res.success&&res.result&&res.result.records) {
          this.datasourceList=res.result.records
        }else {
          this.datasourceList=[]
          if (!res.success){
            this.$message.warning(res.message)
          }
        }
      }).catch((err)=>{
        this.$message.error(err.message)
      })
    },
    add() {
      this.edit({
        /*resourceType:1,*/
        execstate:0
      })
    },
    edit(record) {
      this.model= Object.assign({},record)
      this.$refs.form.resetFields()
    },
    validateTaskName(rule, value, callback) {
      var params = {
        tableName: 'devops_backup_task',
        fieldName: 'task_name',
        fieldVal: value,
        dataId: this.model.id,
      }
      duplicateCheck(params).then((res) => {
        if (res.success) {
          callback()
        } else {
          callback('任务名称已存在!')
        }
      })
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.$refs.form.validate((success, values) => {
        if (success&&!that.confirmLoading) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let formData = Object.assign(this.model, values)
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
              that.confirmLoading = false
            }).catch((err) => {
              that.$message.warning(err.message)
             that.confirmLoading = false
          })
        }
      })
    }
  }
}
</script>