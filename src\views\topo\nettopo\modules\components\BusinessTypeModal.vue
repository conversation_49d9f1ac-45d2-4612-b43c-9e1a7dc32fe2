<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    :destroyOnClose="true"
    :closable="true"
    :maskClosable="false"
    @cancel="hide"
    @ok="handleOk"
    cancelText="关闭"
  >
    <div id="flowStencil" class="sider">
      <a-input placeholder="输入关键字进行过滤" v-model="filterText"></a-input>
      <div class="tree-box">
        <vue-easy-tree
          ref="veTree"
          node-key="businessCode"
          :data="busList"
          :props="replaceFields"
          height="300px"
          :filter-node-method="filterNode"
        >
          <div class="custom-tree-node" slot-scope="{ node, data }">
            <div :class="{ 'bus-active': code === data.businessCode }" @click="chooseBus(data)">
              <a-icon type="cluster" />
              <span class="node-label">{{ node.label }}</span>
            </div>
          </div>
        </vue-easy-tree>
      </div>
    </div>
  </j-modal>
</template>

<script>
import VueEasyTree from '@wchbrad/vue-easy-tree'
import { getAction, postAction, httpAction } from '@/api/manage'
export default {
  name: 'businessTypeModal',
  components: {
    VueEasyTree,
  },
  data() {
    return {
      title: '选择业务类型',
      width: 600,
      visible: false,
      disableSubmit: false,
      showabled: false,
      data: null,
      filterText: '',
      replaceFields: {
        label: 'businessName',
        key: 'businessCode',
      },
      busList: [],
      code: '',
    }
  },
  created() {
    this.getBusList()
  },
  watch: {
    filterText(val) {
      this.$refs.veTree.filter(val)
    },
  },
  methods: {
    chooseBus(data) {
      this.code = data.businessCode
    },
    getBusList() {
      getAction('/business/info/list', {
        column: 'createTime',
        order: 'desc',
        field: 'id',
        pageNo: 1,
        pageSize: -1,
      }).then((res) => {
        if (res.success && res.result) {
          this.busList = res.result.records
        }
      })
    },
    filterNode(value, data) {
      if (!value) return true
      return data.businessName.indexOf(value) !== -1
    },
    show(code) {
      this.code = code || "";
      this.visible = true
    },
    hide() {
      this.visible = false
    },
    handleOk(key, type, name) { 
      if (this.code) {
        this.hide();
        this.$emit("ok",this.code)
      }
      else{
        this.$message.warning("请选择业务！")
      }
    },
  },
}
</script>
<style lang="less" scoped>
.tree-box {
  margin-top: 16px;
}
.custom-tree-node {
  .node-label {
    margin-left: 5px;
  }
}
.bus-active {
  color: #1890ff;
}
</style>