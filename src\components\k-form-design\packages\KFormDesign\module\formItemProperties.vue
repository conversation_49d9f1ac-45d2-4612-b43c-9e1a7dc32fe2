<template>
  <div class="properties-centent kk-checkbox">
    <div class="properties-body" :key="selectItem.key">
      <a-empty class="hint-box" v-show="selectItem.key === ''" description="未选择控件" />

      <a-form v-show="selectItem.key !== ''">
        <a-form-item v-if="selectItem.label !== undefined" label="标签">
          <a-input v-model="selectItem.label" placeholder="请输入" />
        </a-form-item>
        <!-- 评估指标配置 start -->
        <a-form-item
          v-if="isEvaluationSupportedField(selectItem.type)"
          label="评估指标"
        >
          <kCheckbox
            v-model="options.isEvaluationField"
            label="是否为评估指标"
          />
        </a-form-item>
        <a-form-item v-if="!hideModel && selectItem.model !== undefined" label="数据字段">
          <a-input v-model="selectItem.model" placeholder="请输入" />
        </a-form-item>
        <!-- input type start -->
        <a-form-item v-if="selectItem.type === 'input'" label="输入框type">
          <a-input v-model="options.type" placeholder="请输入" />
        </a-form-item>
        <!-- input type end -->
        <a-form-item v-if="options.rangePlaceholder !== undefined && options.range" label="占位内容">
          <a-input placeholder="请输入" v-model="options.rangePlaceholder[0]" />
          <a-input placeholder="请输入" v-model="options.rangePlaceholder[1]" />
        </a-form-item>

        <a-form-item v-else-if="options.placeholder !== undefined" label="占位内容">
          <a-input placeholder="请输入" v-model="options.placeholder" />
        </a-form-item>
        <a-form-item v-if="selectItem.type === 'textarea'" label="自适应内容高度">
          <a-input-number style="width: 100%" v-model="options.minRows" placeholder="最小高度" />
          <a-input-number style="width: 100%" v-model="options.maxRows" placeholder="最大高度" />
        </a-form-item>
        <a-form-item v-if="options.width !== undefined" label="宽度">
          <a-input placeholder="请输入" v-model="options.width" />
        </a-form-item>

        <!-- 动态表格中的宽度设置 -->
        <a-form-item v-if="options.tableWidth !== undefined && formItemList.includes(selectItem.type) && isInBatchTable" label="表格列宽度">
          <a-input
            placeholder="请输入表格列宽度，支持单位：px、%、em、rem"
            v-model="options.tableWidth"
            @change="handleTableWidthChange"
          />
        </a-form-item>
        <a-form-item v-if="selectItem.type === 'batch'" label="rowKey">
          <a-input placeholder="请输入" v-model="options.rowKey" />
        </a-form-item>
        <a-form-item v-if="options.height !== undefined" label="高度">
          <a-input-number v-model="options.height" />
        </a-form-item>
        <a-form-item v-if="options.step !== undefined" label="步长">
          <a-input-number v-model="options.step" placeholder="请输入" />
        </a-form-item>
        <a-form-item v-if="options.min !== undefined" label="最小值">
          <a-input-number v-model="options.min" placeholder="请输入" />
        </a-form-item>
        <a-form-item v-if="options.max !== undefined" label="最大值">
          <a-input-number v-model="options.max" placeholder="请输入" />
        </a-form-item>
        <a-form-item v-if="options.maxLength !== undefined" label="最大长度">
          <a-input-number v-model="options.maxLength" placeholder="请输入" />
        </a-form-item>
        <a-form-item v-if="options.tabBarGutter !== undefined" label="标签间距">
          <a-input-number v-model="options.tabBarGutter" placeholder="请输入" />
        </a-form-item>
        <a-form-item v-if="options.precision !== undefined" label="数值精度">
          <a-input-number :min="0" :max="50" v-model="options.precision" placeholder="请输入" />
        </a-form-item>
        <a-form-item v-if="options.dictCode !== undefined" label="dictCode">
          <a-input v-model="options.dictCode"></a-input>
        </a-form-item>
        <!-- 树选择器 级联选择器 start -->
        <!-- <a-form-item
          v-if="['treeSelect', 'cascader'].includes(selectItem.type)"
          label="选项配置"
        >
          <a-radio-group size="small" buttonStyle="solid" v-model="options.dynamic">
           <a-radio-button value="static">静态数据</a-radio-button>
            <a-radio-button value="dynamic">动态数据</a-radio-button>
            <a-radio-button value="ajax">接口数据</a-radio-button>
          </a-radio-group>
          <div v-if="options.dynamic==='dynamic'">
            <a-input v-model="options.dynamicKey" placeholder="动态数据变量名"></a-input>
          </div>
        </a-form-item> -->
        <!-- 树选择器 级联选择器 start -->
        <!-- 联动数据 -->
        <k-linkage-data
          v-if="formItemList.includes(selectItem.type)"
          v-model="options.linkageData"
          type="linkage"
        ></k-linkage-data>
        <!-- update回调 -->
        <k-linkage-data
          v-if="formItemList.includes(selectItem.type)"
          v-model="options.changeFunc"
          type="update"
        ></k-linkage-data>
        <!-- 选择器选项配置及动态数据配置 start  -->
        <a-form-item v-if="isDynamic" label="选项配置">
          <a-radio-group size="small" buttonStyle="solid" v-model="options.dynamic" @change="dynamicChange($event, 1)">
            <a-radio-button value="static">静态数据</a-radio-button>
            <a-radio-button v-if="!isTree" value="dynamic">字典数据</a-radio-button>
            <a-radio-button value="ajax">接口数据</a-radio-button>
          </a-radio-group>

          <div v-if="isTree && options.dynamic === 'static'" style="position: relative">
            <div style="position: absolute; right: 0; top: 0" v-if="options.staticOptions.length > 0">
              <a :type="'link'" @click="treeOptionsShow = true"> <a-icon type="edit" /> 编辑 </a>
            </div>
            <a-button v-else type="primary" style="margin-bottom: 12px; width: 100%" @click="treeOptionsShow = true">
              添加选项配置
            </a-button>
            <a-tree 
            :getPopupContainer="(node) => node.parentNode"
            :tree-data="options.staticOptions" 
            :replace-fields="{ title: 'label', key: 'value' }" 
            />
            <KChangeOption
              v-if="treeOptionsShow"
              v-model="options.staticOptions"
              :type="'treeOptions'"
              @hideTreeOptions="hideTreeOptions"
            />
          </div>

          <div v-else-if="options.dynamic === 'dynamic'">
            <a-select
              :getPopupContainer="(node) => node.parentNode"
              v-model="options.dynamicKey"
              optionFilterProp="children"
              placeholder="请选择数据字典"
              allowClear
              showSearch
              @change="dictChange($event, 1)"
            >
              <a-select-option v-for="item in this.dictList" :key="item.dictCode" :value="item.dictCode">
                {{ item.dictName }}
              </a-select-option>
            </a-select>
            <!-- <a-input v-model="options.dynamicKey" placeholder="动态数据变量名"></a-input> -->
            <!-- <a-select allowClear placeholder="获取数据的方法" :options="funcArr" v-model="options.getOptionFunc" /> -->
          </div>
          <div v-else-if="options.dynamic === 'static'" style="position: relative">
            <KChangeOption v-model="options.staticOptions" />
          </div>
          <div v-else-if="options.dynamic === 'ajax'">
            <k-ajax-option v-model="options.ajaxData"></k-ajax-option>
          </div>
        </a-form-item>

        <a-form-item v-if="selectItem.type === 'select' && options.options.length > 0" label="默认值">
          <a-select
            :getPopupContainer="(node) => node.parentNode"
            :key="'select_' + options.dynamic"
            :options="options.options"
            v-model="options.defaultValue"
            allowClear
          />
        </a-form-item>
        <a-form-item v-if="selectItem.type === 'radio' && options.options.length > 0" label="默认值">
          <a-radio-group :key="'radio_' + options.dynamic" :options="options.options" v-model="options.defaultValue" />
        </a-form-item>
        <a-form-item v-if="selectItem.type === 'checkbox' && options.options.length > 0" label="默认值">
          <a-checkbox-group
            :key="'checkbox_' + options.dynamic"
            :options="options.options"
            v-model="options.defaultValue"
          />
        </a-form-item>
        <!-- 选择器选项配置及动态数据配置 end -->

        <!-- tabs配置 start -->
        <a-form-item
          v-if="['tabs', 'selectInputList'].includes(selectItem.type)"
          :label="selectItem.type === 'tabs' ? '页签配置' : '列选项配置'"
        >
          <KChangeOption v-model="selectItem.columns" type="tab" />
        </a-form-item>
        <!-- tabs配置 end -->
        <a-form-item v-if="selectItem.type === 'grid'" label="栅格间距">
          <a-input-number v-model="selectItem.options.gutter" placeholder="请输入" />
        </a-form-item>
        <a-form-item v-if="selectItem.type === 'grid'" label="列配置项">
          <KChangeOption v-model="selectItem.columns" type="colspan" />
        </a-form-item>

        <a-form-item v-if="selectItem.type === 'switch'" label="默认值">
          <a-switch v-model="options.defaultValue" />
        </a-form-item>
        <a-form-item v-if="['number', 'slider'].indexOf(selectItem.type) >= 0" label="默认值">
          <a-input-number
            :step="options.step"
            :min="options.min || -Infinity"
            :max="options.max || Infinity"
            v-model="options.defaultValue"
          />
        </a-form-item>
        <a-form-item v-if="selectItem.type === 'rate'" label="默认值">
          <a-rate v-model="options.defaultValue" :allowHalf="options.allowHalf" :count="options.max" />
        </a-form-item>

        <!-- 日期选择器默认值 start -->
        <a-form-item v-if="selectItem.type === 'date'" label="默认值">
          <a-input
            v-if="!options.range"
            v-model="options.defaultValue"
            :placeholder="options.format === undefined ? '' : options.format"
          />
          <a-input
            v-if="options.range"
            v-model="options.rangeDefaultValue[0]"
            :placeholder="options.format === undefined ? '' : options.format"
          />
          <a-input
            v-if="options.range"
            v-model="options.rangeDefaultValue[1]"
            :placeholder="options.format === undefined ? '' : options.format"
          />
        </a-form-item>
        <!-- 日期选择器默认值 start -->
        <a-form-item
          v-if="
            !['number', 'radio', 'checkbox', 'date', 'rate', 'select', 'switch', 'slider', 'html'].includes(
              selectItem.type
            ) && options.defaultValue !== undefined
          "
          label="默认值"
        >
          <a-input
            v-model="options.defaultValue"
            :placeholder="options.format === undefined ? '请输入' : options.format"
          />
        </a-form-item>
        <!-- 修改html -->
        <a-form-item v-if="selectItem.type === 'html'" label="默认值">
          <a-textarea v-model="options.defaultValue" :autoSize="{ minRows: 4, maxRows: 8 }" />
        </a-form-item>
        <a-form-item v-if="options.format !== undefined" label="时间格式">
          <a-input v-model="options.format" placeholder="时间格式如：YYYY-MM-DD HH:mm:ss" />
        </a-form-item>

        <a-form-item v-if="options.orientation !== undefined" label="标签位置">
          <a-radio-group buttonStyle="solid" v-model="options.orientation">
            <a-radio-button value="left">左</a-radio-button>
            <a-radio-button value="">居中</a-radio-button>
            <a-radio-button value="right">右</a-radio-button>
          </a-radio-group>
        </a-form-item>
        <!-- 页签位置 start -->
        <a-form-item v-if="selectItem.type === 'tabs'" label="页签位置">
          <a-radio-group buttonStyle="solid" v-model="options.tabPosition">
            <a-radio value="top">top</a-radio>
            <a-radio value="right">right</a-radio>
            <a-radio value="bottom">bottom</a-radio>
            <a-radio value="left">left</a-radio>
          </a-radio-group>
        </a-form-item>
        <!-- 页签位置 end -->
        <!-- 页签类型 start -->
        <a-form-item v-if="selectItem.type === 'tabs'" label="页签类型">
          <a-radio-group buttonStyle="solid" v-model="options.type">
            <a-radio-button value="line">line</a-radio-button>
            <a-radio-button value="card">card</a-radio-button>
          </a-radio-group>
        </a-form-item>
        <!-- 页签类型 end -->
        <!-- 页签大小 start -->
        <a-form-item v-if="options.size !== undefined" label="大小">
          <a-radio-group buttonStyle="solid" v-model="options.size">
            <a-radio-button value="large">large</a-radio-button>
            <a-radio-button value="default">default</a-radio-button>
            <a-radio-button value="small">small</a-radio-button>
          </a-radio-group>
        </a-form-item>
        <!-- 页签大小 end -->
        <a-form-item v-if="selectItem.type === 'button'" label="类型">
          <a-radio-group buttonStyle="solid" v-model="options.type">
            <a-radio value="primary">Primary</a-radio>
            <a-radio value="default">Default</a-radio>
            <a-radio value="dashed">Dashed</a-radio>
            <a-radio value="danger">Danger</a-radio>
          </a-radio-group>
        </a-form-item>
        <!-- 下载方式 start -->
        <a-form-item v-if="options.downloadWay !== undefined" label="下载方式">
          <a-radio-group buttonStyle="solid" v-model="options.downloadWay">
            <a-radio-button value="a">a标签</a-radio-button>
            <a-radio-button value="ajax">ajax</a-radio-button>
            <a-radio-button value="dynamic">动态函数</a-radio-button>
          </a-radio-group>
          <a-input
            v-show="options.downloadWay === 'dynamic'"
            v-model="options.dynamicFun"
            placeholder="动态函数名"
          ></a-input>
        </a-form-item>
        <!-- 下载方式 end -->
        <a-form-item v-if="selectItem.type === 'button'" label="按钮操作">
          <a-radio-group buttonStyle="solid" v-model="options.handle">
            <a-radio-button value="submit">提交</a-radio-button>
            <a-radio-button value="reset">重置</a-radio-button>
            <a-radio-button value="dynamic">动态函数</a-radio-button>
          </a-radio-group>
          <!-- <a-input
            v-show="options.handle === 'dynamic'"
            v-model="options.dynamicFun"
            placeholder="动态函数名"
          ></a-input> -->
        </a-form-item>
        <!-- 按钮回调 -->
        <k-linkage-data v-if="options.handle === 'dynamic'" v-model="options.dynamicFun" type="btn"></k-linkage-data>
        <a-form-item v-if="selectItem.type === 'alert'" label="辅助描述">
          <a-input v-model="options.description"></a-input>
        </a-form-item>
        <a-form-item v-if="selectItem.type === 'alert'" label="类型">
          <a-radio-group buttonStyle="solid" v-model="options.type">
            <a-radio value="success">success</a-radio>
            <a-radio value="info">info</a-radio>
            <a-radio value="warning">warning</a-radio>
            <a-radio value="error">error</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item v-if="selectItem.type === 'alert'" label="操作属性">
          <kCheckbox v-model="options.showIcon" label="显示图标" />
          <kCheckbox v-model="options.banner" label="无边框" />
          <kCheckbox v-model="options.closable" label="可关闭" />
        </a-form-item>
        <!-- 上传图片 -->
        <a-form-item v-if="selectItem.type === 'uploadImg'" label="样式">
          <a-radio-group buttonStyle="solid" v-model="options.listType">
            <a-radio-button value="text">text</a-radio-button>
            <a-radio-button value="picture">picture</a-radio-button>
            <a-radio-button value="picture-card">card</a-radio-button>
          </a-radio-group>
        </a-form-item>
        <!-- 上传数量 -->
        <a-form-item v-if="options.limit !== undefined" label="最大上传数量">
          <a-input-number :min="1" v-model="options.limit" />
        </a-form-item>

        <!-- scrollY -->
        <a-form-item v-if="options.scrollY !== undefined" label="scrollY">
          <a-input-number :min="0" v-model="options.scrollY" />
        </a-form-item>

        <!-- 上传地址 -->
        <a-form-item v-if="options.action !== undefined" label="上传地址">
          <a-input v-model="options.action" placeholder="请输入"></a-input>
        </a-form-item>
        <a-form-item v-if="options.downloadImageUrl !== undefined" label="下载地址">
          <a-input v-model="options.downloadImageUrl" placeholder="请输入"></a-input>
        </a-form-item>
        <a-form-item v-if="options.downloadFileUrl !== undefined" label="下载地址">
          <a-input v-model="options.downloadFileUrl" placeholder="请输入"></a-input>
        </a-form-item>
        <!-- 文件name -->
        <a-form-item v-if="options.fileName !== undefined" label="文件name">
          <a-input v-model="options.fileName" placeholder="请输入"></a-input>
        </a-form-item>
        <!-- 上传额外参数 -->
        <a-form-item v-if="options.data !== undefined" label="额外参数（JSON格式）">
          <a-textarea v-model="options.data" placeholder="严格JSON格式"></a-textarea>
        </a-form-item>
        <!-- 文字对齐方式 -->
        <a-form-item v-if="selectItem.type === 'text'" label="文字对齐方式">
          <a-radio-group buttonStyle="solid" v-model="options.textAlign">
            <a-radio-button value="left">左</a-radio-button>
            <a-radio-button value="center">居中</a-radio-button>
            <a-radio-button value="right">右</a-radio-button>
          </a-radio-group>
        </a-form-item>
        <!-- 文字字体 -->
        <a-form-item v-if="selectItem.type === 'text'" label="字体属性设置">
          <colorPicker v-model="options.color" />
          <a-select
            :options="familyOptions"
            v-model="options.fontFamily"
            style="width: 36%; margin-left: 2%; vertical-align: bottom"
          />
          <a-select
            :options="sizeOptions"
            v-model="options.fontSize"
            style="width: 35%; margin-left: 2%; vertical-align: bottom"
          />
        </a-form-item>
        <a-form-item v-if="selectItem.type === 'text'" label="操作属性">
          <kCheckbox v-model="options.showRequiredMark" label="显示必选标记" />
        </a-form-item>

        <a-form-item
          v-if="
            options.hidden !== undefined ||
            options.disabled !== undefined ||
            options.readonly !== undefined ||
            options.clearable !== undefined ||
            options.multiple !== undefined ||
            options.range !== undefined ||
            options.showTime !== undefined ||
            options.allowHalf !== undefined ||
            options.showInput !== undefined ||
            options.animated !== undefined
          "
          label="操作属性"
        >
          <kCheckbox v-if="options.hidden !== undefined" v-model="options.hidden" label="隐藏" />
          <kCheckbox v-if="options.disabled !== undefined" v-model="options.disabled" label="禁用" />
          <kCheckbox v-if="options.readonly !== undefined" v-model="options.readonly" label="只读" />
          <kCheckbox v-if="options.clearable !== undefined" v-model="options.clearable" label="可清除" />
          <kCheckbox v-if="options.multiple !== undefined" v-model="options.multiple" label="多选" />
          <kCheckbox v-if="options.range !== undefined" v-model="options.range" label="范围选择" />
          <kCheckbox v-if="options.showTime !== undefined" v-model="options.showTime" label="时间选择器" />
          <kCheckbox v-if="options.allowHalf !== undefined" v-model="options.allowHalf" label="允许半选" />
          <kCheckbox v-if="options.showInput !== undefined" v-model="options.showInput" label="显示输入框" />
          <kCheckbox v-if="options.showLabel !== undefined" v-model="options.showLabel" label="显示标签" />
          <kCheckbox v-if="options.chinesization !== undefined" v-model="options.chinesization" label="汉化" />
          <kCheckbox v-if="options.hideSequence !== undefined" v-model="options.hideSequence" label="隐藏序号" />
          <kCheckbox v-if="options.drag !== undefined" v-model="options.drag" label="允许拖拽" />
          <kCheckbox v-if="options.showSearch !== undefined" v-model="options.showSearch" label="可搜索" />
          <kCheckbox v-if="options.treeCheckable !== undefined" v-model="options.treeCheckable" label="可勾选" />
          <kCheckbox v-if="options.animated !== undefined" v-model="options.animated" label="动画切换" />
          <span v-if="selectItem.type === 'batch'">
          <kCheckbox v-model="options.hideAddBtn" label="隐藏增加按钮" />
          <kCheckbox v-model="options.hideOprCol" label="隐藏操作列" />
        </span>
        </a-form-item>

        <a-form-item v-if="selectItem.rules !== undefined && selectItem.rules.length > 0" label="校验">
          <kCheckbox v-model="selectItem.rules[0].required" label="必填" />
          <a-input v-model="selectItem.rules[0].message" placeholder="必填校验提示信息" />
          <KChangeOption v-model="selectItem.rules" type="rules" />
        </a-form-item>

        <!-- 表格选项 -->
        <a-form-item v-if="selectItem.type === 'table'" label="表格样式CSS">
          <a-input v-model="selectItem.options.customStyle" />
        </a-form-item>
        <a-form-item v-if="selectItem.type === 'table'" label="属性">
          <kCheckbox v-model="selectItem.options.bordered" label="显示边框" />
          <kCheckbox v-model="selectItem.options.bright" label="鼠标经过点亮" />
          <kCheckbox v-model="selectItem.options.small" label="紧凑型" />
        </a-form-item>

        <a-form-item v-if="selectItem.type === 'table'" label="提示">
          <p style="line-height: 26px">请点击右键增加行列，或者合并单元格</p>
        </a-form-item>

        <a-form-item v-if="selectItem.help !== undefined" label="帮助信息">
          <a-input v-model="selectItem.help" placeholder="请输入" />
        </a-form-item>

        <!-- 前缀 -->
        <a-form-item label="前缀" v-if="options.addonBefore !== undefined">
          <a-input v-model="options.addonBefore" placeholder="请输入" />
        </a-form-item>

        <!-- 后缀 -->
        <a-form-item label="后缀" v-if="options.addonAfter !== undefined">
          <a-input v-model="options.addonAfter" placeholder="请输入" />
        </a-form-item>
      </a-form>
    </div>
  </div>
</template>
<script>
/*
 * author kcz
 * date 2019-11-20
 * description 表单控件属性设置组件,因为配置数据是引用关系，所以可以直接修改
 */
import KChangeOption from '../../KChangeOption/index.vue'
import KAjaxOption from '../../KAjaxOption/index.vue'
import KLinkageData from '../../KLinkageData/index.vue'
import kCheckbox from '../../KCheckbox/index.vue'
import { formItemList, changeFuncStr, AjaxData } from '../config/formItemsConfig'
import { loadTreeData, ajaxGetDictItems, getDictItemsFromCache } from '@/api/api'
export default {
  name: 'formItemProperties',
  components: {
    KChangeOption,
    kCheckbox,
    KAjaxOption,
    KLinkageData,
  },
  props: {
    selectItem: {
      type: Object,
      required: true,
      default: () => {
        return {}
      },
    },
    hideModel: {
      type: Boolean,
      default: false,
    },
    dictList: {
      type: Array,
      default: () => [],
    },
    formData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      familyOptions: [
        // 字体选择设置
        {
          value: 'SimSun',
          label: '宋体',
        },
        {
          value: 'FangSong',
          label: '仿宋',
        },
        {
          value: 'SimHei',
          label: '黑体',
        },
        {
          value: 'PingFangSC-Regular',
          label: '苹方',
        },
        {
          value: 'KaiTi',
          label: '楷体',
        },
        {
          value: 'LiSu',
          label: '隶书',
        },
      ],
      sizeOptions: [
        //字号选择设置
        {
          value: '26pt',
          label: '一号',
        },
        {
          value: '24pt',
          label: '小一',
        },
        {
          value: '22pt',
          label: '二号',
        },
        {
          value: '18pt',
          label: '小二',
        },
        {
          value: '16pt',
          label: '三号',
        },
        {
          value: '15pt',
          label: '小三',
        },
        {
          value: '14pt',
          label: '四号',
        },
        {
          value: '12pt',
          label: '小四',
        },
        {
          value: '10.5pt',
          label: '五号',
        },
        {
          value: '9pt',
          label: '小五',
        },
      ],
      treeOptionsShow: false,
      funcArr: this.$formFuncOptions,
      staticOptions: [
        {
          value: '1',
          label: '选项1',
        },
        {
          value: '2',
          label: '选项2',
        },
      ],
      treeItems: ['treeSelect', 'cascader'],
      selectItems: ['select', 'radio', 'checkbox'],
      formItemList,
    }
  },
  created() {
    // console.log("获取动态数据的函数123 === ",this.selectItem,funcPools)
  },
  watch: {
    selectItem: {
      deep: false,
      immediate: true,
      handler(v) {
        if (v.options) {
          this.initOptions()
        }
      },
    },
    //当有使用静态数据时 监听静态数据的改变
    'options.staticOptions': {
      deep: true,
      immediate: false,
      handler(v) {
        if (v) {
          let options = this.selectItem.options
          if (options && options.dynamic && options.dynamic === 'static') {
            this.selectItem.options.options = v
          }
        }
      },
    },
  },
  computed: {
    // 判断当前控件是否在动态表格中
    isInBatchTable() {
      // 通过检查当前选中项的key来判断是否在动态表格中
      if (!this.selectItem || !this.selectItem.key) {
        return false
      }

      // 使用传入的formData prop
      if (!this.formData || !this.formData.list) {
        return false
      }

      // 递归查找当前控件是否在batch类型的容器中
      return this.findItemInBatch(this.formData.list, this.selectItem.key)
    },
    options() {
      if (this.formItemList.includes(this.selectItem.type)) {
        if (this.selectItem.options.linkageData === undefined) {
          this.selectItem.options.linkageData = `[]`
        }
        if (this.selectItem.options.changeFunc === undefined) {
          this.selectItem.options.changeFunc = changeFuncStr
        }
      }
      if (this.selectItem.type === 'batch') {
        if (this.selectItem.options.rowKey === undefined) {
          this.selectItem.options.rowKey = 'id'
        }
      }
      if (this.selectItem.type === 'select' && this.selectItem.options.defaultValue === '') {
        this.selectItem.options.defaultValue = undefined
      }

      // 确保isEvaluationField属性存在
      const supportedTypes = ['input', 'textarea', 'number', 'select', 'date', 'checkbox', 'radio', 'time', 'rate', 'slider', 'uploadFile', 'uploadImg', 'treeSelect', 'cascader', 'switch'];
      if (supportedTypes.includes(this.selectItem.type)) {
        if (this.selectItem.options.isEvaluationField === undefined) {
          this.$set(this.selectItem.options, 'isEvaluationField', false)
        }
      }

      return this.selectItem.options || {}
    },
    isTree() {
      return this.treeItems.includes(this.selectItem.type)
    },
    isDynamic() {
      let options = this.selectItem.options
      return options && options.dynamic
    },
  },
  methods: {
    initOptions() {
      if (this.selectItem && this.selectItem.options) {
        let options = this.selectItem.options
        if (options === undefined) return
        if (this.selectItem.options.dynamic === undefined) return

        if (typeof options.dynamic === 'boolean') {
          if (options.dynamic === false) {
            options.staticOptions = options.options
          }
          options.dynamic = 'static'
        }
        options.options = []
        if (this.selectItem.options.ajaxData === undefined) {
          this.selectItem.options.ajaxData = new AjaxData()
        }
        if (options.staticOptions === undefined) {
          this.$set(options, 'staticOptions', this.staticOptions)
        }
        if (options.dynamic === 'dynamic') {
          this.dictChange(options.dynamicKey, 2)
        } else if (options.dynamic === 'static') {
          options.options = options.staticOptions || this.staticOptions
        }
      }
    },

    dynamicChange(e) {
      let options = this.selectItem.options
      options.options = []
      options.defaultValue = ''
      if (e.target.value === 'dynamic') {
        this.dictChange(options.dynamicKey, 3)
      } else if (e.target.value === 'static') {
        options.options = options.staticOptions
      }
    },
    dictChange(e, t) {
      let options = this.selectItem.options
      if (e) {
        if (t === 1) {
          options.defaultValue = undefined
        }
        ajaxGetDictItems(e).then((res) => {
          if (res.success) {
            options.options = res.result.map((el) => {
              el.label = el.title
              return el
            })
            // console.log('动态字典变化了 === ', res.result)
          } else {
            options.options = []
          }
        })
      } else {
        options.options = []
      }
    },
    hideTreeOptions() {
      this.treeOptionsShow = false
    },

    // 评估指标相关方法
    isEvaluationSupportedField(fieldType) {
      // 支持评估指标的字段类型
      const supportedTypes = ['input', 'textarea', 'number', 'select', 'date', 'checkbox', 'radio', 'time', 'rate', 'slider', 'uploadFile', 'uploadImg', 'treeSelect', 'cascader', 'switch'];
      return supportedTypes.includes(fieldType);
    },
    handleTableWidthChange(e) {
      const value = e.target.value;
      if (value.endsWith('%')) {
        this.selectItem.options.tableWidth = value;
      } else if (value.endsWith('px') || value.endsWith('em') || value.endsWith('rem')) {
        this.selectItem.options.tableWidth = value;
      } else {
        this.selectItem.options.tableWidth = value;
      }
    },
    // 递归查找控件是否在动态表格中
    findItemInBatch(list, targetKey) {
      for (let item of list) {
        if (item.type === 'batch' && item.list) {
          // 在动态表格中查找目标控件
          for (let batchItem of item.list) {
            if (batchItem.key === targetKey) {
              return true
            }
          }
        } else if (item.type === 'grid' && item.columns) {
          // 在栅格布局中递归查找
          for (let column of item.columns) {
            if (this.findItemInBatch(column.list, targetKey)) {
              return true
            }
          }
        } else if (item.type === 'card' && item.list) {
          // 在卡片布局中递归查找
          if (this.findItemInBatch(item.list, targetKey)) {
            return true
          }
        } else if (item.type === 'table' && item.trs) {
          // 在表格布局中递归查找
          for (let tr of item.trs) {
            for (let td of tr.tds) {
              if (this.findItemInBatch(td.list, targetKey)) {
                return true
              }
            }
          }
        }
      }
      return false
    }
  },
}
</script>
