<template>
  <a-row :gutter="10" style="display: flex; flex-flow: column nowrap; height: 100%">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" class="card-style">
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="报告名称">
                  <a-input
                    :maxLength='maxLength'
                    :allowClear="true"
                    autocomplete="off"
                    v-model="queryParam.projectName"
                    placeholder="请输入报告名称"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="colBtnsSpan()">
                <span
                  class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                >
                  <a-button type="primary" class="btn-search btn-search-style" @click="searchQuery">查询</a-button>
                  <a-button class="btn-reset btn-reset-style" @click="searchReset">重置</a-button>
                  <a v-if="isVisible" class="btn-updown-style" @click="doToggleSearch">
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>

      <a-card :bordered="false" style="flex: auto">
        <div class="table-operator table-operator-style">
          <a-button @click="handleAdd()">新增</a-button>
          <a-dropdown v-if="selectedRowKeys.length > 0">
            <a-menu slot="overlay" style="text-align: center">
              <a-menu-item key="1" @click="batchDel"> 删 除</a-menu-item>
            </a-menu>
            <a-button>
              批量操作
              <a-icon type="down" />
            </a-button>
          </a-dropdown>
        </div>

        <a-table
          ref="table"
          bordered
          rowKey="id"
          :columns="columns"
          :dataSource="dataSource"
          :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
          :pagination="ipagination"
          :loading="loading"
          :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          @change="handleTableChange"
        >
          <span class="caozuo" slot="action" slot-scope="text, record">
            <a @click="handleDetail(record)">查看</a>
            <span v-if="record.status == '0'">
              <a-divider type="vertical" />
              <a @click="handleEdit(record)">编辑</a>
              <a-divider type="vertical" />
              <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                <a>删除</a>
              </a-popconfirm>
              <!-- 仅对待发布展示 -->
              <a-divider type="vertical" />
              <a @click="distribute(record)">下发</a>
            </span>
          </span>
          <template slot="tooltip" slot-scope="text">
            <a-tooltip placement="topLeft" :title="text" trigger="hover">
              <div class="tooltip">
                {{ text }}
              </div>
            </a-tooltip>
          </template>
          <template slot="status" slot-scope="text, record">
            <a-tag class="tag" :color="statusConfig(record.status).color">
              {{ statusConfig(record.status).text }}
            </a-tag>
          </template>
          <template slot="evaluationCycle" slot-scope="text, record">
            {{ record.startTime }} - {{ record.endTime }}
          </template>
        </a-table>
      </a-card>
      <addCollectTask ref="modalForm" @ok="modalFormOk"></addCollectTask>
    </a-col>
  </a-row>
</template>

<script>
import { putAction, deleteAction } from '@/api/manage'
import '@/assets/less/TableExpand.less'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
import addCollectTask from '@views/operationalEvaluationNew/materials/modules/addCollectTask.vue'
import { getStatusConfig } from '@/views/operationalEvaluationNew/modules/statusConfig'
export default {
  name: 'materialsList',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components: { addCollectTask },
  props: {

  },
  data() {
    return {
      maxLength: 50,
      formItemLayout: {
        labelCol: {
          style: 'width:90px',
        },
        wrapperCol: {
          style: 'width:calc(100% - 90px)',
        },
      },
      // 表头
      columns: [
        {
          title: '报告名称',
          dataIndex: 'projectName',
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
            return {
              style: cellStyle,
            }
          },
          scopedSlots: {
            customRender: 'tooltip',
          },
        },
        // {
        //   title: '关联单位',
        //   dataIndex: 'deptNameStr',
        //   customCell: () => {
        //     let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
        //     return {
        //       style: cellStyle,
        //     }
        //   },
        //   scopedSlots: {
        //     customRender: 'tooltip',
        //   },
        // },
        {
          title: '状态',
          dataIndex: 'status',
          scopedSlots: {
            customRender: 'status',
          },
        },
        {
          title: '评估周期',
          dataIndex: 'evaluationCycle',
          scopedSlots: {
            customRender: 'evaluationCycle',
          },
        },
        {
          title: '发起人',
          dataIndex: 'sender_dictText',
        },
        // {
        //   title: '创建时间',
        //   dataIndex: 'createTime',
        // },
        {
          title: '操作',
          dataIndex: 'action',
          fixed: 'right',
          width: 200,
          scopedSlots: { customRender: 'action' },
        },
      ],
      url: {
        list: '/devops/projectInfo/pageList',
        delete: '/devops/projectInfo/deleteBatch',
        deleteBatch: '/devops/projectInfo/deleteBatch',
        edit: '/devops/projectInfo/edit',
      },
    }
  },
  created() {

  },
  mounted() {},
  methods: {
    // 获取填报状态的颜色配置
    statusConfig(status) {
      return getStatusConfig(status)
    },
    handleDelete: function (id) {
      var that = this
      deleteAction(that.url.deleteBatch, { ids: id }).then((res) => {
        if (res.success) {
          //重新计算分页问题
          that.reCalculatePage(1)
          that.$message.success(res.message)
          that.loadData()
        } else {
          that.$message.warning(res.message)
        }
      })
    },
    // 下发操作
    distribute(record) {
      var that = this
      that.$confirm({
        content: '确认下发任务',
        okText: '是',
        cancelText: '否',
        onOk: () => {
          putAction(that.url.edit, {
            ...record,
            status: '1',
          }).then((res) => {
            if (res.success) {
              //重新计算分页问题
              that.reCalculatePage(1)
              that.$message.success(res.message)
              that.loadData()
            } else {
              that.$message.warning(res.message)
            }
          })
        },
      })
    },
  },
}
</script>
<style lang="less" scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
</style>
