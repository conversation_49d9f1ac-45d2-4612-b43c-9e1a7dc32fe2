<template>
  <a-layout class="och-layout">
    <layout-header :hostName="hostName"></layout-header>
    <div class="och-layout-content">
      <layout-menus :hostName="hostName"></layout-menus>
      <a-layout-content style="width: calc(100% - 320px)">
        <div class="menu-content">
          <transition name="page-toggle">
            <template>
              <router-view />
            </template>
          </transition>
        </div>
      </a-layout-content>
    </div>
  </a-layout>
</template>

<script>
import LayoutHeader from './LayoutHeader.vue'
import LayoutMenus from './LayoutMenus.vue'
import { getHostNameLocal } from '@/utils/util'
export default {
  name: 'OneClickHelpLayout',
  components: { LayoutHeader, LayoutMenus },
  data() {
    return {
      hostName: '',
    }
  },
  created() {
    this.hostName = getHostNameLocal();
  },
  mounted() {},
  methods: {},
}
</script>

<style lang="less" scoped>
.och-layout {
  background-color: #000;
  min-width: 1200px;
}
.och-layout-content {
  margin-top: 36px;
  height: calc(100% - 61px - 36px);
  overflow: hidden;
  box-sizing: border-box;
  display: flex;

  .menu-content {
    width: 100%;
    padding: 0 16px;
    height: calc(100% - 16px);
    overflow: auto;
  }
}
</style>

<style lang='less'>
.och-layout {
  ::-webkit-scrollbar {
    background-color: #000 !important;
  }
  ::-webkit-scrollbar-thumb {
    background-color: rgba(21, 85, 175, 0.4) !important;
  }
  .ant-spin-container::after {
    background-color: rgba(0, 0, 0, 0.5);
  }
  .ant-spin-dot-item {
    background-color: #fff;
  }
}
.och-notice-filter {
  .ant-popover-inner {
    // background-color: rgba(0, 0, 0, 0.7);
    background-color: #091425;
  }
  .ant-popover-inner-content {
    color: #fff;
  }
   .ant-tabs-bar{
      border-bottom: 1px solid rgba(21, 85, 175, 0.6);
    }
  .ant-tabs-nav{
   
    .ant-tabs-tab{
      color:#fff;
    }
    .ant-tabs-tab:hover{
       color:#66ffff;
    }
    .ant-tabs-tab-active{
      color:#66ffff;
    }
    .ant-tabs-ink-bar{
       background-color:#66ffff
    }
  }
  .ant-list-split {
    .ant-list-item {
    border-bottom: 1px solid rgba(21, 85, 175, 0.6);
  }
  }

}
.och-notice-filter.ant-popover-placement-right {
   .ant-popover-content {
    .ant-popover-arrow {
      border-bottom-color: #091425;
      border-left-color: #091425;
    }
  }
}
.och-notice-filter.ant-popover-placement-leftBottom {
  .ant-popover-content {
    .ant-popover-arrow {
      border-top-color: #091425;
      border-right-color: #091425;
    }
  }
}
.och-notice-filter.ant-popover-placement-bottomRight{
   .ant-popover-content {
    .ant-popover-arrow {
      border-top-color: #091425;
      border-left-color: #091425;
    }
  }
}
</style>