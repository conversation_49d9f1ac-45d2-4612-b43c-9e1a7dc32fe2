<template>
  <div>
    <a-table :columns="columns" :data-source="properties" :pagination="false" rowKey="id">
      <template slot="handle" slot-scope="tex, record, index">
        <a-space class="listener-add">
          <span><a-icon type="delete" @click="deleteProperty(index)" /></span>
          <span><a-icon type="edit" @click="editProperty(record)" /></span>
        </a-space>
      </template>
    </a-table>
    <a-modal
      :title="title"
      :visible="_propertiesShow"
      :width="'60%'"
      :maskClosable="false"
      @ok="addProperty"
      @cancel="hideProperties"
    >
      <div class="properties-form-con">
        <a-form :form="property">
          <a-form-item>
            <span class="properties-form-item-label">名称：</span>
            <div class="properties-form-item-wrap">
              <a-input v-model="property.name" />
            </div>
          </a-form-item>
          <a-form-item>
            <span class="properties-form-item-label">值：</span>
            <div class="properties-form-item-wrap">
              <a-input v-model="property.value" />
            </div>
          </a-form-item>
        </a-form>
      </div>
    </a-modal>
  </div>
</template>

<script>
import mixinPanel from '../mixins/mixinPanel'
import { randomString } from '@/utils/util'
export default {
  mixins: [mixinPanel],
  props: {
    propertiesShow: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      properties: [],
      property: {
        id: '',
        name: '',
        value: '',
      },
      columns: [
        {
          title: '名称',
          dataIndex: 'name',
          key: 'name',
          scopedSlots: { customRender: 'name' },
        },
        {
          title: '值',
          dataIndex: 'value',
          key: 'value',
          scopedSlots: { customRender: 'value' },
        },
        {
          title: '操作',
          dataIndex: 'handle',
          key: 'handle',
          scopedSlots: { customRender: 'handle' },
        },
      ],
      isEdit: false,
    }
  },
  computed: {
    _propertiesShow: {
      get() {
        return this.propertiesShow
      },
      set(v) {
        this.$emit('changePropertiesShow', v)
      },
    },
  },
  created() {},
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.property = {
        id: randomString(32),
        name: '',
        value: '',
      }
      this.initProperties();
      // console.log("初始化附加属性表单",this.properties)
    },
    initProperties() {
      this.properties = [];
      if (this.element.businessObject.extensionElements) {
        this.properties = this.element.businessObject.extensionElements.values
          .filter((item) => item.$type === this.descriptorPrefix + 'Properties')
          .shift()
        this.properties = this.properties
          ? this.properties.values.map((property) => {
              return {
                name: property.name,
                value: property.value,
                id: property.id,
              }
            })
          : []
      }
    },
    addProperty() {
      if (!this.properties.find((el) => el.id === this.property.id)) {
        this.properties.push(this.property)
      }
      this.save()
    },
    hideProperties() {
      this._propertiesShow = false
    },
    deleteProperty(idx) {
      this.properties.splice(idx, 1)
      this.save()
    },
    editProperty(row) {
      this.property = row
      this._propertiesShow = true
    },
    save() {
      let  extensionElements = this.modeler.get('moddle').create('bpmn:ExtensionElements')
      let tem = this.element.businessObject.get('extensionElements')
      // if (!extensionElements) {
      //   extensionElements = this.modeler.get('moddle').create('bpmn:ExtensionElements')
      // }
      extensionElements.values = [];
      if(tem && tem.values>0){
         extensionElements.values = tem.values.filter((item) => item.$type !== this.descriptorPrefix + 'Properties')
      }
      // extensionElements.values = extensionElements.values
      //   ? extensionElements.values.filter((item) => item.$type !== this.descriptorPrefix + 'Properties')
      //   : []
      if (this.properties && this.properties.length > 0) {
        const propertiesElement = this.modeler.get('moddle').create(this.descriptorPrefix + 'Properties')
        this.properties.forEach((item) => {
          const propertyElement = this.modeler.get('moddle').create(this.descriptorPrefix + 'Property')
          propertyElement['name'] = item.name
          propertyElement['value'] = item.value
          propertyElement['id'] = item.id

          propertiesElement.get('values').push(propertyElement)
        })
        extensionElements.get('values').push(propertiesElement)
      }

      this.updateProperties({
        extensionElements:
          extensionElements.get('values') && extensionElements.get('values').length > 0 ? extensionElements : undefined,
      })
      this._propertiesShow = false
      // this.$emit('saveProperties', this.properties.length)
    },
  },
}
</script>

<style lang="less" scoped>
.listener-add {
  color: #1890ff;
  cursor: pointer;
}
.properties-form-con {
  height: 50vh;
  .properties-form-item-label {
    display: inline-block;
    width: 80px;
    text-align: right;
  }
  .properties-form-item-wrap {
    display: inline-block;
    width: calc(100% - 80px);
  }
  // .params-title {
  //   display: flex;
  //   justify-content: space-between;
  // }
  // .params-table {
  //   margin-top: 8px;
  // }
}
</style>