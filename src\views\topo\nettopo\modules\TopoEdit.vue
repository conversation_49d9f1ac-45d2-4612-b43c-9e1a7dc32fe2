<template>
  <j-modal
    :title="title"
    :visible="visible"
    :destroyOnClose="true"
    fullscreen
    @ok="handleOk"
    @cancel="handleCancel"
    okText="保存"
    cancelText="关闭"
  >
    <vis-edit v-if="visible" ref="visEdit" @ok="submitCallback" toolbar></vis-edit>
  </j-modal>
</template>

<script>
import VisEdit from './VisEdit'
export default {
  name: 'TopoEdit',
  components: {
    VisEdit,
  },
  data() {
    return {
      title: '',
      width: 800,
      record: {},
      visible: false,
      disableSubmit: false,
    }
  },
  methods: {
    edit(id) {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.visEdit.createTopo(id)
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      this.$refs.visEdit.spinning = true
      this.$refs.visEdit.spinTip = "正在保存..."
      let timer = setTimeout(() => {
        clearTimeout(timer)
        timer = null;
        this.$refs.visEdit.save()
      }, 100)
    },
    handleCancel() {
      this.close()
    },
    submitCallback() {
      this.$emit('ok')
      this.visible = false
    },
  },
}
</script>

<style lang="less" scoped>
::v-deep .ant-modal-content, ::v-deep .ant-modal-content .ant-modal-body {
  overflow: hidden !important;
}
</style>
