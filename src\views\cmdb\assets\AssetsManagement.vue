<template>
  <div style="height:100%;">
    <keep-alive exclude='AssetsInfo'>
      <component :is="pageName" :data="data" :is-editing='true' :render-states='renderStates'/>
    </keep-alive >
  </div>
</template>
<script>
import assetsList from './AssetsList'
import assetsInfo from './AssetsInfo'
export default {
  name: "AssetsManagement",
  data() {
    return {
      isActive: 0,
      data:{},
      renderStates:{
        showBaseInfo:true,
        showStateInfo:true,
        showDeviceFunction:true,
        showJournal:true,
        showAlarm:true,
        showDeviceAlarm:true,
      }
    };
  },
  components: {
    assetsList,
    assetsInfo
  },
  created(){
    this.pButton1(0);
  },
  //使用计算属性
  computed: {
    pageName() {
      switch (this.isActive) {
        case 0:
          return "assetsList";
          break;
        default:
          return "assetsInfo";
          break;
      }
    }
  },
  methods: {
    pButton1(index) {
      this.isActive = index;
    },
    pButton2(index,item) {
      this.isActive = index;
      this.data = item;
    }
  }
}
</script>