<template>
  <div class='zr-national-nodes' ref='zrNationalNodes'>
    <zr-bigscreen-title title='指标评估结果'>
    </zr-bigscreen-title>
    <div class='national-nodes-content'>
      <slot>
        <div v-if='listData.length' style='height: 100%;'>
          <div class='nodes-statics scroll-list-item'>
            <div class='item-name'>
              名称
            </div>
            <div class='item-status'>等级</div>
          </div>
          <vue-seamless-scroll v-if='srollOption.autoPlay' :data='listData' class='scroll-warp'
                               :class-option='srollOption'>
            <ul class='scroll-list'>
              <li class='scroll-list-item' :class='{"scroll-list-odd":item.type==="category"}' v-for='(item, index) in listData'
                  :key='index'>
                <div class='item-name' :title='item.name' >
                  {{ item.name }}
                </div>
                <div class='item-status' >{{  item.level || '--'  }}</div>
              </li>
            </ul>
          </vue-seamless-scroll>
          <div v-else class='scroll-warp'>
            <ul class='scroll-list'>
              <li class='scroll-list-item' :class='{"scroll-list-odd":item.type==="category"}' v-for='(item, index) in listData'
                  :key='index'>
                <div class='item-name' :title='item.title'>
                  {{ item.name }}
                </div>
                <div class='item-status'>{{ item.level || '--' }}</div>
              </li>
            </ul>
          </div>
        </div>

      </slot>
    </div>
  </div>
</template>
<script>
import ZrBigscreenTitle from '@views/zrBigscreens/modules/ZrBigscreenTitle.vue'
import vueSeamlessScroll from 'vue-seamless-scroll'
import resizeObserverMixin from '@views/statsCenter/com/resizeObserverMixin'
import { indicators,indicatorTypes } from '@views/zrBigscreens/modules/zrUtil'
import {
  getAction
} from '@/api/manage'
export default {
  name: 'ZrOpLeftList',
  components: { ZrBigscreenTitle, vueSeamlessScroll },
  mixins: [resizeObserverMixin],
  props:{
    projectId:{
      type:String,
      default:''
    },
  },
  data() {
    return {
      listData: [],
      srollOption: {
        step: 0.5, // 步长
        speed: 100, // 滚动速度
        timer: 3000,// 滚动时间间隔
        autoPlay: false,
        limitMoveNum: 10000,
        singleHeight: 36 ,
      },
      maxNum: 0,
      colors:[
        '#55A7F4', // 正常
        '#F4A655', // 异常
        '#F45555'  // 故障
      ],
      nodes:[],
    }
  },
  created() {
    this.getMetricList()
  },
  mounted() {
    this.$nextTick(()=>{
      this.setPlayState()
    })

  },
  watch:{
   /* projectId:{
      handler(newVal, oldVal){
        this.getMetricList()

      },
      immediate:true
    }*/
  },
  methods: {
    // 获取指标评估结果列表
    getMetricList(){
      let temp = window.zrIndicatorTypes || indicatorTypes;
      this.listData = this.mergeList(temp);
    },
    //整合类和指标列表
    mergeList(list){
      let tem = [];
      list.forEach(item=>{
        tem.push(item);
        let temList = window.zrIndicators ||indicators
        let temArr = temList.filter(el=>el.type === item.id)
        tem = tem.concat(temArr);
      })
     return tem
    },
    // 屏幕变化回调
    resizeObserverCb() {
      this.setPlayState()
    },
    //设置滚动状态
    setPlayState() {
      if (this.$refs.zrNationalNodes && this.listData.length) {
        let bounded = this.$refs.zrNationalNodes.getBoundingClientRect()
        this.maxNum = Math.floor((bounded.height - 51 - 116) / 36)
        if (this.maxNum>0 && this.maxNum < this.listData.length) {
          this.srollOption.limitMoveNum = this.maxNum
          this.srollOption.autoPlay = true
          return
        }
      }
      this.srollOption.autoPlay = false
      this.srollOption.limitMoveNum = this.listData.length + 1
    }
  }
}
</script>


<style scoped lang='less'>
.zr-national-nodes {
  height: 100%;
  .national-nodes-content {
    height: calc(100% - 51px);
    background: linear-gradient(to right, rgba(29, 78, 140, 0.3), rgba(29, 78, 140, 0.0));
    overflow: hidden;
    padding: 0px 12px;

  }
}
.nodes-statics{
  background: rgba(94, 140, 199, 0.5);
  color: rgba(237, 245, 255, 0.95);
  max-width: 401px;
}
.scroll-warp {
  height: 100%;
  overflow: hidden;
  max-width: 401px;
  .scroll-list {
    width: 100%;
    height: 100%;
    margin: 0px;
    padding: 0px;
  }


}
.scroll-list-item {
  display: flex;
  align-items: center;
  color: rgba(237, 245, 255, 0.95);
  font-size: 14px;
  justify-content: space-between;
  height: 36px;
  .item-name {
    width: 60%;
    overflow: hidden;
    position: relative;
    padding: 0 8px;
    line-height: 1;
    opacity: 0.95;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .item-status {
    width: 40%;
    text-align: center;
    opacity: 0.95;
  }
}

.scroll-list-odd {
  background: rgba(29, 78, 140, 0.25);
}
</style>