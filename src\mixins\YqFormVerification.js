import { duplicateCheck } from '@api/api'

export const YqFormVerification = {
    data() {
        return {
            phoneRegExp: /^1\d{10}$/,
            //telephoneRegExp:/^(0\d{2,3}-\d{7,8})$/
            telephoneRegExp: /^(0\d{2,3}-\d{7,8})|(0\d{9,11})|(\d{3,4})$/
        }
    },
    methods: {
        /**
         *form表单的手机号码验证
         * @param rule
         * @param value
         * @param callback
         */
        validatePhoneFun(rule, value, callback) {
            if (!value) {
                callback('请输入手机号码')
            } else {
                if (new RegExp(this.phoneRegExp).test(value)) {
                    callback()
                } else {
                    callback("请输入正确格式的手机号码!");
                }
            }
        },
        /**
         *form表单的座机号码验证
         * @param rule
         * @param value
         * @param callback
         */
        validateTelephoneFun_1(rule, value, callback) {
            if (value) {
                if (new RegExp(this.telephoneRegExp).test(value)) {
                    callback()
                } else {
                    callback("请输入正确格式的座机号码!");
                }
            }
            callback()
        },

        /**
         * 非form形式的座机号码验证函数
         * @param value
         * @returns {{tips: string, isSuccess: boolean}}
         */
        validateTelephoneFun_2(value) {
            let obj = {
                isSuccess: true,
                tips: ''
            }
            if (!value) {
                obj.isSuccess = false
                obj.tips = '请座机号码'
            } else {
                if (new RegExp(this.telephoneRegExp).test(value)) {
                } else {
                    obj.isSuccess = false
                    obj.tips = '请输入正确格式的座机号码，例如：0531-5555555'
                }
            }
            return obj
        },
        /**
         *form表单的邮箱验证
         * @param rule
         * @param value
         * @param callback
         */
        validateEmailFun(rule, value, callback) {
            if (!value) {
                callback()
            } else {
                if (new RegExp(/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/).test(value)) {
                    callback()
                } else {
                    callback("请输入正确格式的邮箱!")
                }
            }
        },
        /**
         * 编号验证：以字母开头,可包含字母、数字、英文横线、下划线或英文句号
         * @param rule
         * @param value
         * @param callback
         */
        validateCodeFun_1(rule, value, callback) {
            if (!value) {
                callback()
            } else {
                if (new RegExp(/^[a-zA-Z]([a-zA-Z0-9-_.]*)$/).test(value)) {
                    callback()
                } else {
                    callback("格式不正确！要求以字母开头，可包含字母、数字、英文横线、下划线或英文句号。")
                }
            }
        },
        /**
         * 编号验证：以字母开头,可包含字母、数字
         * @param rule
         * @param value
         * @param callback
         */
        validateCodeFun_2(rule, value, callback) {
            if (!value) {
                callback()
            } else {
                if (new RegExp(/^[a-zA-Z]([a-zA-Z0-9]*)$/).test(value)) {
                    callback()
                } else {
                    callback("格式不正确！要求以字母开头，可包含字母、数字。")
                }
            }
        },

        /**
         * 名称：以字母或汉字开头，包含字母、数字、汉字
         * @param rule
         * @param value
         * @param callback
         */
        validateNameFun_1(rule, value, callback) {
            if (!value) {
                callback()
            } else {
                if (new RegExp(/^[a-zA-Z\u4e00-\u9fa5][a-zA-Z0-9\u4e00-\u9fa5]*$/).test(value)) {
                    callback()
                } else {
                    callback("格式不正确！要求以字母或汉字开头，包含字母、数字、汉字。")
                }
            }
        },
        /**
         * 账号：以字母开头,可包含字母、数字、英文横线、下划线
         * @param rule
         * @param value
         * @param callback
         */
        validateNameFun_2(rule, value, callback) {
            if (!value) {
                callback()
            } else {
                if (new RegExp(/^([a-zA-Z0-9-_]*)$/).test(value)) {
                    callback()
                } else {
                    callback("格式不正确！要求以字母开头，可包含字母、数字、英文横线、下划线。")
                }
            }
        },
        /**
         * 非form形式情况下的手机号码验证
         * @param rule
         * @param value
         * @param callback
         * @constructor
         */
        phoneValidateFun(value) {
            let obj = {
                isSuccess: true,
                tips: ''
            }
            if (!value) {
                obj.isSuccess = false
                obj.tips = '请输入手机号码'
            } else {
                if (new RegExp(this.phoneRegExp).test(value)) {
                } else {
                    obj.isSuccess = false
                    obj.tips = '请输入正确格式的手机号码'
                }
            }
            return obj
        },

        /**
         * 通话时长校验
         * @param value
         * @returns {{tips: string, isSuccess: boolean}}
         * @constructor
         */
        talkTimeValidateFun(value) {
            let obj = {
                isSuccess: true,
                tips: ''
            }
            if (!value) {
                obj.isSuccess = false
                obj.tips = '请输入通话时长'
            } else {
                //if(new RegExp(/^\d|(\d*:[0-5][0-9])$/).test(value)){
                if (new RegExp(/^\d*:[0-5][0-9]$/).test(value)) {
                } else {
                    obj.isSuccess = false
                    obj.tips = '请输入正确格式的通话时长,例如：100:59'
                }
            }
            return obj
        }
    }
}