<template>
  <a-row :gutter='10' style='height: 100%;' class='vScroll'>
    <a-col style='width:100%;height: 100%;display: flex;flex-direction: column'>
      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <a-table ref='table' bordered row-key='id' :columns='columns' :dataSource='dataSource'
          :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}" :pagination='ipagination' :loading='loading'
          @change='handleTableChange'>
          <span slot='action' class='caozuo' slot-scope='text, record'>
            <a @click='handleDetailPage(record)'>查看</a>
          </span>
        </a-table>
      </a-card>
    </a-col>
  </a-row>
</template>

<script>
  import {
    mixinDevice
  } from '@/utils/mixin'
  import {
    getAction
  } from '@/api/manage'
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import {
    YqFormSearchLocation
  } from '@/mixins/YqFormSearchLocation'
  export default {
    name: 'nodeMonitorList',
    mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
    data() {
      return {
        description: '负载均衡节点监控页面',
        formItemLayout: {
          labelCol: {
            style: 'width:80px'
          },
          wrapperCol: {
            style: 'width:calc(100% - 80px)'
          }
        },
        // 表头
        columns: [{
            title: '本地站点ID',
            dataIndex: 'localServer',
            customRender: (value, row, index) => {
              const obj = {
                children: value,
                attrs: {}
              };
              if (row.size != null) {
                obj.attrs.rowSpan = row.size
              } else {
                obj.attrs.rowSpan = 0
              }
              return obj
            },
            customCell: () => {
              let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
              return {
                style: cellStyle
              }
            }
          }, {
            title: '远程站点ID',
            dataIndex: 'name',
            customCell: () => {
              let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '链路状态',
            dataIndex: 'status'
          },
          {
            title: '探测次数',
            dataIndex: 'allCounts'
          },
          {
            title: '失败次数',
            dataIndex: 'fallCounts'
          },
          {
            title: '探测协议',
            dataIndex: 'checktype',
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            fixed: 'right',
            width: 140,
            scopedSlots: {
              customRender: 'action'
            }
          }
        ],
        url: {
          list: '/distributedStorage/loadbalance/info',
          delete: '/device/deviceInfo/delete',
          deleteBatch: '/device/deviceInfo/deleteBatchDevice',
        },
      }
    },
    mounted() {},
    methods: {
      loadData(arg) {
        if (!this.url.list) {
          this.$message.error('请设置url.list属性!')
          return
        }
        //加载数据 若传入参数1则加载第一页的内容
        if (arg === 1) {
          this.ipagination.current = 1
        }
        var params = this.getQueryParams() //查询条件
        this.loading = true
        getAction(this.url.list, params).then((res) => {
          if (res.success && res.result) {
            this.dataSource = res.result.records || res.result
            if (this.dataSource.length < 9) {
              this.clientHeight = false
            }
            this.ipagination.total = res.result.total ? res.result.total : 0
          }
          if (res.code === 510) {
            this.$message.warning(res.message)
          }
          this.loading = false
        })
      },
    }
  }
</script>
<style lang='less' scoped>
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';

  .stateBox {
    margin-left: 20px;
  }

  .stateImg {
    vertical-align: middle
  }

  .alarmStatus {
    margin-left: 8px;
  }

  .overlay {
    color: #409eff
  }
</style>