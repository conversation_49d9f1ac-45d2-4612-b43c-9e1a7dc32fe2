<template>
  <!-- 设备在线情况 -->
  <div style="height: 100%;width: 100%;">
    <recent-time @changeType="changeType" :selectedIndex="selectedIndex"></recent-time>
    <div ref="baseEcharts" style="height: 100%;width: 100%;"></div>
  </div>
</template>

<script>
import recentTime from './recentTime.vue'
import { chartMixins } from './chartMixins'
export default {
  name: 'online',
  mixins: [chartMixins],
  components: {
    recentTime
  },
  props: {
    chartData: {
      type: Array,
      default: () => []
    },
    fontSizeObject: {
      type: Object,
      default: function () {
        return {
          legendFontSize: 8,
          xAxisFontSize: 8,
          yAxisFontSize: 10
        }
      }
    }
  },
  watch: {
    chartData: {
      handler(nVal, oVal) {
        this.$nextTick(() => {
          this.initData(nVal)
        })
      },
      deep: true,
      immediate: true
    }
  },
  data() {
    return {
      myChart: null,
      selectedIndex: 0
    }
  },
  methods: {
    changeType(index) {
      this.selectedIndex = index
      this.initData(this.chartData)
    },
    initData(data) {
      if (data.length <= 0) {
        return
      }
      let xData = []
      let barData = []
      let lineData = []

      // x轴数据
      let xAllData = data.map(item => item.time)
      // 柱状图数据
      let barAllData = data.map(item => item.value1) // 开机时长
      //折线图数据
      let lineAllData = data.map(item => item.value2) // 开机次数

      if (this.selectedIndex == 0 && data.length > 6) {
        // 近七日数据
        xData = xAllData.slice(data.length - 7, data.length)
        barData = barAllData.slice(data.length - 7, data.length)
        lineData = lineAllData.slice(data.length - 7, data.length)
      } else {
        // 全部数据
        xData = xAllData
        barData = barAllData
        lineData = lineAllData
      }

      this.myChart = this.$echarts.init(this.$refs.baseEcharts)
      this.myChart.clear()
      let color = ['#31AF5D', '#FFA32F']
      let option = {
        color: color,
        grid: {
          top: '20%',
          left: '6%',
          right: '7%',
          bottom: 4,
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            let html = `${params[0].name}<br/>`
            let unit = ''
            params.map((item, i) => {
              if (item.seriesIndex == 0) {
                unit = 'h'
              }
              if (item.seriesIndex == 1) {
                unit = '次'
              }
              html += `${item.marker}${item.seriesName} ${item.value || 0} ${unit} <br/>`
            })
            return html
          }
        },
        legend: {
          top: '2%',
          left: '7%',
          itemWidth: 7,
          itemHeight: 7,
          textStyle: {
            fontSize: this.fontSizeObject.legendFontSize
          }
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: true,
            axisLine: {
              show: true,
              lineStyle: {
                color: 'rgba(0,0,0,0.15)'
              }
            },
            axisLabel: {
              textStyle: {
                color: 'rgba(0,0,0,0.45)',
                fontSize: this.fontSizeObject.xAxisFontSize
              }
            },
            axisTick: {
              show: false
            },
            data: xData
          }
        ],
        yAxis: [
          {
            type: 'value',
            axisTick: {
              show: false
            },
            splitLine: {
              show: true,
              lineStyle: {
                type: 'solid',
                color: 'rgba(0,0,0,0.15)',
                width: 1
              }
            },
            axisLine: {
              show: false
            },
            axisLabel: {
              formatter: '{value}h',
              textStyle: {
                color: 'rgba(0,0,0,0.45)',
                fontSize: this.fontSizeObject.yAxisFontSize
              }
            }
          },
          {
            type: 'value',
            name:'单位: 次',
            nameTextStyle: {
                color: "rgba(0,0,0,0.45)",
                fontSize: this.fontSizeObject.yAxisFontSize
            },
            axisTick: {
              show: false
            },
            position: 'right',
            splitLine: {
              show: false
            },
            axisLine: {
              show: false
            },
            axisLabel: {
              formatter: '{value}',
              textStyle: {
                color: 'rgba(0,0,0,0.45)',
                fontSize: this.fontSizeObject.yAxisFontSize
              }
            }
          }
        ],
        dataZoom: [
          {
            xAxisIndex: [0],
            show: false, //是否显示滑动条，不影响使用
            start: 0, // 从头开始。
            endValue: 30,
            realtime: true, //是否实时更新
          },
          {
            type: 'inside',
            xAxisIndex: 0,
            zoomOnMouseWheel: true, //滚轮是否触发缩放
            moveOnMouseMove: true, //鼠标滚轮触发滚动
            moveOnMouseWheel: true
          }
        ],
        series: [
          {
            type: 'bar',
            name: '在线时长',
            barGap: 0.2,
            barWidth: this.selectedIndex == 1 ? 9: 12,
            label: {
              show: false,
              position: 'inside',
              color: 'rgba(0,0,0,0.45)',
              fontSize: 10
            },
            data: barData
          },
          {
            type: 'line',
            name: '当天开机次数',
            yAxisIndex: 1,
            data: lineData,
            symbol: 'circle',
            showAllSymbol: true,
            emphasis: {
              focus: 'series' //高亮显示
            },
            symbolSize: 4,
            lineStyle: {
              color: `${color[1]}`
            },
            itemStyle: {
              // 折线拐点标志的样式
              normal: {
                color: `${color[1]}`,
                // borderColor: 'rgba(255, 234, 0, 0.5)',
                borderWidth: 4
              }
            }
          }
        ]
      }

      this.myChart.setOption(option)
    }
  }
}
</script>