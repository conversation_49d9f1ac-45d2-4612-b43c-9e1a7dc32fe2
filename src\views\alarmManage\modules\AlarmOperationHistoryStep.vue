<template>
  <div>
    <div v-if='dataSource.length>0' class='content-box'>
      <div class="title-box">
        <span class="title">历史记录</span>
      </div>
      <div class='step-top-pos-container'>
<!--        <a-steps progress-dot size='small'>-->
        <a-steps size='small'>
          <a-step status='wait' v-for='(item,index) in dataSource' :id='"locate_step_"+index' :key='"locate_step_"+index'>
            <div slot='title' class='title-slot'>
              <div class='title-content'>{{item.name}}</div>
              <div class='title-line' v-if='index<dataSource.length-1'></div>
            </div>
            <div slot='description' class='description-slot'>
              <div class='description-content'>操作人:{{item.assignName_dictText}}</div>
              <div class='description-content'>操作时间:{{item.createTime}}</div>
            </div>
          </a-step>
        </a-steps>
      </div>
    </div>
  </div>
</template>
<script>
import {JeecgListMixin} from '@/mixins/JeecgListMixin'
import { getAction } from '@api/manage'
export default {
  name: "AlarmOperationHistoryStep",
  components:{},
  mixins: [JeecgListMixin],
  props:{
    alarmInfo:{
      type:Object,
      required:true,
      default:{}
    }
  },
  data() {
    return {
      disableMixinCreated:true,
      columns: [
        {
          title: '操作名称',
          dataIndex: 'name'
        },
        {
          title: '操作人',
          dataIndex: 'assignName_dictText'
        },
        {
          title: '负责人',
          dataIndex: 'headName_dictText',
        },
        {
          title: '操作时间',
          dataIndex: 'createTime'
        }
      ],
      url: {
        list: '/alarm/assign/history/queryList',//列表
      }
    }
  },
  watch: {
    alarmInfo: {
      handler(val) {
        this.queryParam={}
        this.dataSource=[]
        if (Object.keys(val).length > 0) {
          this.queryParam.alarmId = val.id
          this.loadData(1)
        }
      },
      deep: true,
      immediate: true
    }
  }
}
</script>

<style scoped lang="less">
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
.content-box{
  //margin-bottom: 20px;
  .title-box {
    //margin-bottom: 10px;
  }

  .title {
    padding-left: 7px;
    border-left: 4px solid #1e3674;
  }
}
.step-top-pos-container {
  height: 85px;
  line-height: 85px;
  overflow: hidden;
  /*overflow-x:auto;*/
  max-width: 100%;
  display: inline-flex;
  justify-content: start;
  align-items: center;
  flex-flow: row nowrap;
  //margin-bottom: 16px;
}

::v-deep .ant-steps {
  overflow: hidden;
  overflow-x: auto;

  .ant-steps-item {
    margin:0px 0px 8px 0px !important;
    min-width: 260px !important;

    .ant-steps-item-description {
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      white-space: nowrap !important;
    }
  }

  .ant-steps-item-active,.ant-steps-item-wait {
    .ant-steps-item-icon {
      //border-color: #40a9ff !important;
      border-color: rgba(0, 0, 0, 0.65) !important;

      .ant-steps-icon {
        //color: #40a9ff !important;
        color:rgba(0, 0, 0, 0.65) !important
      }
    }

    .ant-steps-item-title {
      //color: #40a9ff !important;
      color:rgba(0, 0, 0, 0.65) !important
    }

    .ant-steps-item-description {
      //color: #40a9ff !important;
      color:rgba(0, 0, 0, 0.65) !important
    }
  }
}

::v-deep .ant-steps .ant-steps-item:not(.ant-steps-item-active) > .ant-steps-item-container[role='button']:hover .ant-steps-item-title,
::v-deep .ant-steps .ant-steps-item:not(.ant-steps-item-active) > .ant-steps-item-container[role='button']:hover .ant-steps-item-subtitle,
::v-deep .ant-steps .ant-steps-item:not(.ant-steps-item-active) > .ant-steps-item-container[role='button']:hover .ant-steps-item-description {
  //color: #40a9ff !important;
  color:rgba(0, 0, 0, 0.65) !important
}

::v-deep .ant-steps .ant-steps-item:not(.ant-steps-item-active):not(.ant-steps-item-process) > .ant-steps-item-container[role='button']:hover .ant-steps-item-icon {
  //border-color: #40a9ff !important;
  border-color: rgba(0, 0, 0, 0.65) !important
}

::v-deep .ant-steps .ant-steps-item:not(.ant-steps-item-active):not(.ant-steps-item-process) > .ant-steps-item-container[role='button']:hover .ant-steps-item-icon .ant-steps-icon {
  //color: #40a9ff !important;
  color:rgba(0, 0, 0, 0.65) !important
}
::v-deep .ant-steps-horizontal:not(.ant-steps-label-vertical) .ant-steps-item-description{
  max-width: 200px;
}
::v-deep .ant-steps-small .ant-steps-item-title{
  padding-right: 0px !important;
}
.title-slot{
  display: flex;
  justify-content: left;
  align-items: center;
  flex-flow: row nowrap;

  .title-content{
    display: inline-block;
  }
  .title-line{
    display: inline-block;
    margin: 0 10px;
    width: 200px;
    height: 1px;
    background-color: #E8E8E8
  }
}
.description-slot{
  overflow: hidden;
  .description-content{
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
</style>