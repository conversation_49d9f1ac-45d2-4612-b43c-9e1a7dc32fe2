<template>
  <a-modal
    :title="title"
    :width="modalWidth"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭"
    wrapClassName="ant-modal-cust-warp"
    style="top:5%;height: 95%;overflow: auto"
  >
     <div style="margin-top: 25px;">
      <a-spin :spinning="confirmLoading">
        <a-form :form="form">
          <a-row>
            <a-col>
              <a-form-item label="描述" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-textarea placeholder="请输入退回原因" v-decorator="['description', validatorNode.description]"></a-textarea>
              </a-form-item>
            </a-col>
            <a-col>
              <a-form-item label="附件" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <div class="clearfix">
                    <a-upload
                    action="https://www.mocky.io/v2/5cc8019d300000980a055e76"
                    list-type="picture-card"
                    :file-list="fileList"
                    @preview="handlePreview"
                    @change="handleChange"
                    >
                    <div v-if="fileList.length < 8">
                        <a-icon type="plus" />
                        <div class="ant-upload-text">
                        Upload
                        </div>
                    </div>
                    </a-upload>
                    <a-modal :visible="previewVisible" :footer="null" @cancel="onCancel">
                    <img alt="example" style="width: 100%" :src="previewImage" />
                    </a-modal>
                </div>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-spin>
     </div>
  </a-modal>
</template>
<script>
import pick from "lodash.pick";
export default {
    name:'todoManageSendback',
    components:{
    },
    data(){
        return{
            title: '操作',
            confirmLoading: false,
            /* 弹框宽 */
            modalWidth: '55%',
            form: this.$form.createForm(this),
            visible: false,
            labelCol: {
                xs: { span: 24 },
                sm: { span: 5 }
            },
            wrapperCol: {
                xs: { span: 24 },
                sm: { span: 16 }
            },
            // 上传相关
            previewVisible: false,
            previewImage: '',
            fileList: [
                // {
                //   uid: '-1',
                //   name: 'image.png',
                //   status: 'done',
                //   url: 'https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png',
                // }
            ],
            // 校验
            validatorNode: {
                description: {
                    rules: [
                        { required: true, message: '请输入事件描述!' },
                        {
                            min: 2,
                            max: 30,
                            message: '事件描述长度在 2 到 80 个字符',
                            trigger: 'blur'
                        }
                    ]
                }
            }
        }
    },
    methods:{
        add() {
            this.edit({});
        },
        edit(record) {
            this.form.resetFields();
            this.model = Object.assign({}, record);
            this.visible = true;
            this.$nextTick(() => {
                this.form.setFieldsValue(
                pick(
                    this.model,
                    "id",
                    "description"
                )
                );
            });
        },
        // 关闭弹框
        close() {
        this.$emit('close')
        this.visible = false
        this.current = 0
        },
        // 提交
        handleOk() {
        let that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
            if (!err) {
            that.confirmLoading = true
            let formData = Object.assign(that.model, values)
            let params = {
                id: formData.id,
                type: formData.type,
                priority: formData.priority,
                source: formData.source,
                contactWay: formData.contactWay,
                title: formData.title,
                description: formData.description
            }
            }
        })
        },
        handleCancel() {
            this.close()
        },
        onIndex(index){
        },
        // tab
        callback(key) {
        },
        // 上传相关
        onCancel() {
            this.previewVisible = false
        },
        async handlePreview(file) {
            if (!file.url && !file.preview) {
                file.preview = await getBase64(file.originFileObj)
            }
            this.previewImage = file.url || file.preview
            this.previewVisible = true
        },
        handleChange({ fileList }) {
            this.fileList = fileList
        }
    }
}
</script>
<style scoped></style>
