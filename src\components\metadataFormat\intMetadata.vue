<template>
  <a-form-item label="单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
    <a-select :getPopupContainer="(node) => node.parentNode" allowClear show-search labelInValue placeholder="请选择单位"
      @change="onChange" option-filter-prop="children" :filter-option="filterOption" @focus="handleFocus"
      @blur="handleBlur" v-model="formatValue">
      <!-- v-decorator="['formatValue']" -->
      <!-- <a-select-opt-group> -->
      <span slot="label">常用单位</span>
      <!-- <a-select-option value="%">百分比 / %</a-select-option>
        <a-select-option value="2">次  / count</a-select-option>  -->
      <a-select-option v-for="item in selectData" :key="item.key">{{item.value}}</a-select-option>
      <!-- <a-select-option :key="formatValue.key" :value="formatValue.key">{{formatValue.label}}</a-select-option> -->
      <!-- </a-select-opt-group> -->
    </a-select>
  </a-form-item>

</template>

<script>
  export default {
    name: 'intMetadata',
    props: ['typeFormatValue'],
    components: {

    },
    data() {
      return {
        value: {},
        labelCol: {
          xs: {
            span: 5
          },
          sm: {
            span: 5
          }
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          }
        },
        formatValue: [],
        selectData: [{
            key: "2",
            value: "次  / count"
          },
          {
            key: "%",
            value: "百分比 / %"
          }
        ]
      }
    },
    methods: {
      onChange(value) {
        this.value = value;
        this.$emit('changElemType', JSON.stringify(value));
      },
      filterOption(input, option) {
        return (
          option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
        );
      },
      handleBlur() {},
      handleFocus() {},
    },
    mounted() {
      if (undefined != this.typeFormatValue && '' != this.typeFormatValue) {
        var obj = JSON.parse(this.typeFormatValue);
        this.formatValue = obj;
      }
    }
  }
</script>

<style>
</style>