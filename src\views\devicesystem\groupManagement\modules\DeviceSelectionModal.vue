<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    :centered="true"
    :confirmLoading="confirmLoading"
    switchFullscreen
    @ok="handleOk"
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }"
    @cancel="handleCancel"
    cancelText="关闭"
  >
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :lg="12" :md="14" :sm="16" :xs="18">
            <a-form-item label="产品名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-tree-select
                :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }"
                allowClear
                v-model="currentValue"
                :getPopupContainer="(node) => node.parentNode"
                placeholder="请选择产品名称"
                @select="selectProduct"
                @change="changeProduct"
                :tree-data="productTreeData"
                tree-icon
              ></a-tree-select>
            </a-form-item>
          </a-col>
          <a-col :lg="12" :md="14" :sm="16" :xs="18">
            <a-form-item label="所属单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-tree-select
                :getPopupContainer='(node) => node.parentNode'
                v-model='searchedDepKey'
                tree-node-filter-prop='title'
                :replaceFields='replaceFields'
                :treeData='departsTreeData'
                show-search
                :maxTagCount='1'
                :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                placeholder='请选择单位'
                allow-clear
                @change='onChangeDeparts'
              >
              </a-tree-select>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <div>
      <div>
        <a-table
          style="width: 100%"
          size="middle"
          bordered
          :row-key="(record,index)=>{return record.id}"
          :columns="columns"
          :dataSource="dataSource"
          :pagination="ipagination"
          :loading="loading"
          :hideDefaultSelections="true"
          :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
          :rowSelection="{ selectedRowKeys: selectedRowKeys,onChange: onSelectChange }"
          @change="handleTableChange"
        ></a-table>
      </div>
    </div>
  </j-modal>
</template>

<script>
import JDictSelectTag from '@/components/dict/JDictSelectTag'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { getAction } from '@/api/manage'

export default {
  name: 'ResourceSelectionModal',
  components: {
    JDictSelectTag
  },
  mixins: [JeecgListMixin],
  data() {
    return {
      title: '',
      width: 800,
      visible: false,
      confirmLoading: false,
      disableSubmit: false,
      form: this.$form.createForm(this),
      model: {},
      labelCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 8
        },
        md: {
          span: 8
        },
        lg: {
          span: 8
        },
        xl: {
          span: 8
        }
      },
      wrapperCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 16
        }
      },
      url: {
        productTreeList: '/assetscategory/assetsCategory/selectTree',
        list: '/device/deviceGroup/getResourceList',
        bindDevice: '/device/deviceGroup/bindDevice', // 绑定设备
      },
      columns: [
        {
          title: '产品名称',
          dataIndex: 'productName',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 150px;max-width:300px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '设备名称',
          dataIndex: 'name',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 150px;max-width:300px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: 'IP',
          dataIndex: 'ip',
          customCell: () => {
            let cellStyle = 'text-align: center;width:160px'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '所属单位',
          dataIndex: 'momgDeptName',
          customCell: () => {
            let cellStyle = 'text-align: center;width:160px'
            return {
              style: cellStyle
            }
          }
        },
      ],
      productTreeData: [],
      disableMixinCreated: true,
      currentValue: undefined,
      searchedDepKey: undefined,
      departsTreeData:[],
      replaceFields: {
        children: 'children',
        title: 'deptName',
        key: 'deptId',
        value: 'deptId'
      },
    }
  },
  created() {
    this.loadDepartsData();
  },
  mounted() {
    this.getProductTreeData()
  },
  methods: {
    loadDepartsData() {
      getAction('/sys/sysDepart/queryAllTree').then((res) => {
        for (let i = 0; i < res.length; i++) {
          let temp = res[i]
          this.departsTreeData.push(temp)
        }
      })
    },
    getProductTreeData() {
      getAction(this.url.productTreeList).then(res => {
        if (res.success) {
          this.productTreeData = res.result
          this.setDisabled(this.productTreeData)
        }
      })
    },
    setDisabled(data) {
      if (data.length && data.length > 0) {
        for (let i = 0; i < data.length; i++) {
          data[i] = {
            ...data[i],
            isLeaf: data[i].children.length == 0,
            icon:
              data[i].type != 'product' ? (
                <a-icon type="folder" style="color:#409eff" />
              ) : (
                <a-icon type="file" style="color:#409eff" />
              )
          }
          if (data[i].children.length > 0) {
            this.setDisabled(data[i].children)
          }
        }
      }
    },

    selectProduct(value, label, extra) {
      this.currentValue = value
      this.queryParam.option = value
      this.queryParam.type = extra.selectedNodes[0].data.props.type || 'category'
      this.type = extra.selectedNodes[0].data.props.type
      this.loadData()
    },
    changeProduct(value, label, extra) {
      if (!value) {
        this.queryParam.option = undefined
        this.queryParam.type = undefined
        this.loadData()
      }
    },
    onChangeDeparts(value, label, extra) {
      if (!value) {
        this.queryParam.momgDeptId = undefined
      }else{
        this.queryParam.momgDeptId = value
      }
      this.loadData()
    },
    add() {
      this.edit({})
    },
    edit(record, idList) {
      if (idList && idList.length > 0) {
        this.selectedRowKeys = idList.split(',')
      } else {
        this.onClearSelected()
      }
      this.taskId = record.id
      this.queryParam.groupId = record.id
      this.queryParam.category = undefined
      this.queryParam.type = undefined
      this.currentValue = undefined
      this.visible = true
      this.loadData(1)
    },
    close() {
      this.visible = false
    },
    handleOk() {
      const that = this
      if(that.dataSource.length===0){
        that.close()
        return
      }
      if (that.taskId == null) {
          that.$emit('ok', that.selectedRowKeys, that.selectionRows)
          that.close()
      } else {
        if (that.selectedRowKeys.length == 0) {
          that.$message.warning('请选择要添加的设备!')
        } else {
          that.confirmLoading=true
          let ids=that.selectedRowKeys.join(',')
          let param = {
            groupId: this.taskId,
            deviceId: ids
          }
          getAction(this.url.bindDevice, param).then((res) => {
            if (res.success) {
              that.$emit('loadTableData')
              that.close()
            } else {
              this.$message.warning(res.message)
            }
            that.confirmLoading=false
          }).catch((err)=>{
            this.$message.warning(err.message)
            that.confirmLoading=false
          })
        }
      }
    },
    handleCancel() {
      this.close()
    }
  }
}
</script>

<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';

::v-deep .ant-table-wrapper .ant-spin-nested-loading {
  padding-right: 0px;
}
</style>