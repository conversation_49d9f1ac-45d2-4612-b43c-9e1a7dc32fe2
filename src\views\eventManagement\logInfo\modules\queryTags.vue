<template>
  <div>
    <div class='tags-wrapper'>
      <span v-for='(item,index) in tagList' :id='"tag_"+index' :key='"tag_"+index'  class='tag-item-wrapper' >
        <span class='tag-title' :class='{"tag-title-disable":item.enable!=1}' @click='selectQuery(item,index,$event)'>
          <span v-if='item.inverse==1' class='tag-inverse'>!</span>
          <span  v-if='item.alias.length>0':title='item.term'>{{setText(item.alias,textLength)}}</span>
          <span v-else :title='item.term'>{{setText(item.term,textLength)}}</span>
        </span>
        <span v-if="!isLogCategoryField(item)" class='tag-btn' title='删除' @click='deleteQuery(item)'><a-icon type='close'/></span>
      </span>
    </div>
    <!--    <a-popover ref="popover" v-model="popoverVisible" placement='bottomLeft' trigger='click'>-->
    <a-popover
      ref="popover"
      :visible="popoverVisible"
      @visibleChange="handleClickChange"
      placement='bottomLeft'
      trigger='click'>
      <template slot='content'>
        <div class='main-box-content' v-if='popoverVisible && activeMenuKey==activeMenuKeyEnum.menuUI'>
          <ul style='list-style: none;margin: 0;padding: 0'>
            <!--编辑-->
            <li class='li-menu-item' v-if="!isLogCategoryField(curQuery)"  @click='editQuery(curQuery)'>
              <a-icon class='li-menu-icon' type='edit' />编辑
            </li>
            <!--禁用-->
            <li v-if='curQuery&&curQuery.enable==enableStatusEnum.enabled' class='li-menu-item'
                @click='enableQuery(enableStatusEnum.disabled)'>
              <a-icon class='li-menu-icon' type='eye-invisible' />禁用
            </li>
            <!--启用-->
            <li v-else class='li-menu-item' @click='enableQuery(enableStatusEnum.enabled)'>
              <a-icon class='li-menu-icon' type='eye' />启用
            </li>
            <!--包含结果-->
            <li class='li-menu-item' @click='invertQuery' v-if='curQuery&&curQuery.inverse==1'>
              <a-icon class='li-menu-icon' type='swap' />包含结果
            </li>
            <!--排除结果-->
            <li class='li-menu-item' @click='invertQuery' v-else>
              <a-icon class='li-menu-icon' type='swap' />排除结果
            </li>
            <!--删除-->
            <li class='li-menu-item' v-if="!isLogCategoryField(curQuery)" @click='deleteQuery(null)'>
              <a-icon class='li-menu-icon' type='delete'/>删除
            </li>
          </ul>
        </div>
        <!--编辑查询条件界面-->
        <div class='query-form-content' v-else-if='activeMenuKey==activeMenuKeyEnum.addQueryUI'>
          <query-form
            ref='queryForm'
            :log-analyze-id='logAnalyzeId'
            :field-list='fieldList'
            :group-id='activeQueryGroup&&activeQueryGroup.id?activeQueryGroup.id:""'
            :btn-name='"更新筛选"'
            @ok='completeEditing'
            @cancel='backQueryMenu'>
          </query-form>
        </div>
      </template>
      <template slot='title'>
        <span class='query-form-headline' v-if='activeMenuKey==activeMenuKeyEnum.addQueryUI'>编辑筛选</span>
      </template>
      <div v-if='curQuery' class='tag-item-wrapper' :style="`opacity:0;background:red;position: absolute;content:'';left:${left};top:${top};display:${popoverVisible?'block':'none'};`">
        <span class='tag-title' :class='{"tag-title-disable":curQuery.enable!=1}'>
          <span v-if='curQuery.inverse==1' class='tag-inverse'>!</span>
          <span  v-if='curQuery.alias.length>0'>{{setText(curQuery.alias,textLength)}}</span>
          <span v-else>{{setText(curQuery.term,textLength)}}</span>
        </span>
        <!--        <span class='tag-btn'><a-icon type='close'/></span>-->
      </div>
    </a-popover>
  </div>
</template>
<script>
import { getAction, deleteAction, putAction, putParamsAction } from '@api/manage'
import { activeMenuKeyEnum, enableStatusEnum } from '@views/eventManagement/logInfo/modules/status'
import queryForm from '@views/eventManagement/logInfo/modules/queryForm.vue'
import yqIcon from '@comp/tools/SvgIcon'

export default {
  name: 'queryTags',
  components: { yqIcon, queryForm },
  props: {
    logAnalyzeId: {
      type: String,
      required: false,
      default: ''
    },
    fieldList:{
      type: Array,
      required: false,
      default: ()=>{return []}
    }
  },
  data() {
    return {
      textLength:15,
      popoverVisible:false,
      left:0,
      top:0,
      curQuery:null,
      activeMenuKeyEnum:activeMenuKeyEnum,
      activeMenuKey:activeMenuKeyEnum.menuUI,

      enableStatusEnum: enableStatusEnum,
      tagList: [],
      activeQueryGroup: null,
      url: {
        queryTagsList: '/logAnalyze/term/list',//获取当前激活查询分组下的查询条件
        delete: '/logAnalyze/term/delete',//删除某个查询条件
        enable: '/logAnalyze/term/edit',//禁用、启用某个查询条件
        inverse: '/logAnalyze/term/edit',//取反某个查询条件

        allEnable: '/logAnalyze/term/allEnable',//全部禁用、启用查询条件
        allInverse: '/logAnalyze/term/allInverse',//全部取反查询条件
        allDelete: '/logAnalyze/group/clearTerms'//全部删除查询条件
      }
    }
  },
  watch:{
    popoverVisible:{
      handler(nval,oval){
        if (!nval){
          let timer= setTimeout(()=>{
            this.activeMenuKey=activeMenuKeyEnum.menuUI
            clearTimeout(timer)
          },100)
        }
      },
      deep:true
    }
  },
  methods: {
    /**初始化*/
    initData(group){
      this.activeQueryGroup = group
      this.getTagList(group)
    },
    /**加载查询条件*/
    getTagList(group) {
      this.tagList.splice(0,this.tagList.length)
      if (Object.keys(group).length > 0) {
        getAction(this.url.queryTagsList, { queryId: group.id, logAnalyzeId: this.logAnalyzeId }).then((res) => {
          //console.log('所有查询条件res===', res)
          if (res.success) {
            this.tagList = res.result
            //console.log('所有查询条件this.tagList===', this.tagList)
            if (this.tagList.length > 0) {
              let enable = this.tagList.every((item) => {
                return item.enable == enableStatusEnum.enabled
              })
              let disable = this.tagList.every((item) => {
                return item.enable == enableStatusEnum.disabled
              })
              //全部启用状态
              if (enable) {
                this.$emit('setEnableStatus', enableStatusEnum.enabled)
              }
              //全部禁用状态
              else if (disable) {
                this.$emit('setEnableStatus', enableStatusEnum.disabled)
              }
              //部分禁用、部分启用状态
              else {
                this.$emit('setEnableStatus', enableStatusEnum.both)
              }
            }
            //查询条件为空时，全部不可操作
            else {
              this.$emit('setEnableStatus', enableStatusEnum.noBoth)
            }
          } else {
            this.$emit('setEnableStatus', enableStatusEnum.noBoth)
            this.$message.warning(res.message)
          }
        }).catch((err) => {
          this.$emit('setEnableStatus', enableStatusEnum.noBoth)
          this.$message.warning(err.message)
        })
      }
    },
    /**设置标签显示长度*/
    setText(text,length){
      if (text){
        if(text.length>length){
          return text.slice(0,length)+"..."
        }else{
          return text
        }
      }
    },
    /**点击某个查询，出现弹窗，并动态设置弹窗位置*/
    selectQuery(query, index,e) {
      this.curQuery=query
      this.activeMenuKey=activeMenuKeyEnum.menuUI
      this.popoverVisible=false
      this.left=(e.pageX - e.offsetX-2)+"px"
      this.top=(e.pageY- e.offsetY -2)+"px"
      this.$nextTick(() => {
        this.popoverVisible = true;
      })
    },
    handleClickChange(visible){
      this.popoverVisible=visible
      if (this.popoverVisible===false){
        this.curQuery=null
      }
    },
    /**编辑某个查询条件*/
    editQuery(query) {
      if (query) {
        this.activeMenuKey = activeMenuKeyEnum.addQueryUI
        this.$nextTick(() => {
          this.$nextTick(() => {
            this.$refs.queryForm.edit(query)
          })
        })
      }
    },
    /**编辑返回*/
    backQueryMenu(){
      this.activeMenuKey=activeMenuKeyEnum.menuUI
      this.$refs.queryForm.initForm()
    },
    /**编辑成功后重新加载查询条件，并隐藏弹窗*/
    completeEditing(){
      this.getTagList(this.activeQueryGroup)
      this.popoverVisible=false
    },
    /**禁用、启用某个查询条件*/
    enableQuery(enable) {
      this.popoverVisible=false
      let id=this.curQuery?this.curQuery.id:''
      this.curQuery=null
      if (id){
        putAction(this.url.enable,{id:id,enable:enable.toString()}).then((res)=>{
          if (res.success) {
            this.getTagList(this.activeQueryGroup)
          } else {
            this.$message.warning(res.message)
          }
        }).catch((err) => {
          this.$message.warning(err.message)
        })
      }
    },
    /**取反某个查询条件*/
    invertQuery() {
      this.popoverVisible=false
      let id=this.curQuery?this.curQuery.id:''
      let inverse=this.curQuery.inverse==1?0:1
      let query=JSON.parse(this.curQuery.query)
      query.operator=this.setOperator(query.operator,inverse)
      this.curQuery=null
      if (id){
        putAction(this.url.enable,{id:id,inverse:inverse,query:JSON.stringify(query)}).then((res)=>{
          if (res.success) {
            this.getTagList(this.activeQueryGroup)
          } else {
            this.$message.warning(res.message)
          }
        }).catch((err) => {
          this.$message.warning(err.message)
        })
      }
    },
    /**设置取反运算符*/
    setOperator(value,inverse) {
      let operator=''
      switch (value){
        case "equal":
        case 'notEqual':
          operator=inverse==1? "notEqual":'equal'
          break;
        case "orEqual":
        case 'orNotEqual':
          operator=inverse==1? "orNotEqual":'orEqual'
          break;
        case "withinRange":
        case "outsideRange":
          operator=inverse==1? "outsideRange":'withinRange'
          break;
        case "exist":
        case "notExist":
          operator=inverse==1? "notExist":'exist'
          break;
        default:
          break;
      }
      return operator
    },
    /**删除某个查询条件*/
    deleteQuery(query=null) {
      this.popoverVisible=false
      let id=query?query.id:(this.curQuery?this.curQuery.id:'')
      this.curQuery=null
      if (id){
        deleteAction(this.url.delete, { termId: id }).then((res) => {
          if (res.success) {
            this.getTagList(this.activeQueryGroup)
            this.popoverVisible=false
          } else {
            this.$message.warning(res.message)
          }
        }).catch((err) => {
          this.$message.warning(err.message)
        })
      }
    },

    /**全部删除：清空所有查询条件*/
    deleteAllQuery() {
      deleteAction(this.url.allDelete, { queryId: this.activeQueryGroup.id }).then((res) => {
        if (res.success) {
          this.getTagList(this.activeQueryGroup)
        } else {
          this.$message.warning(res.message)
        }
      }).catch((err) => {
        this.$message.warning(err.message)
      })
    },
    /**全部禁用/启用所有查询条件*/
    enableHandler(operation) {
      putParamsAction(this.url.allEnable, { queryId: this.activeQueryGroup.id, enable: operation }).then((res) => {
        if (res.success) {
          this.getTagList(this.activeQueryGroup)
        } else {
          this.$message.warning(res.message)
        }
      }).catch((err) => {
        this.$message.warning(err.message)
      })
    },
    /**全部取反所有查询条件*/
    invertAllQuery() {
      putParamsAction(this.url.allInverse, { queryId: this.activeQueryGroup.id }).then((res) => {
        if (res.success) {
          this.getTagList(this.activeQueryGroup)
        } else {
          this.$message.warning(res.message)
        }
      }).catch((err) => {
        this.$message.warning(err.message)
      })
    },
    /** 判断日志类型需要隐藏操作按钮 */
    isLogCategoryField(item) {
      if (!item) return false
      try {
        const query = JSON.parse(item.query)
        return query.field === 'log_category'
      } catch (e) {
        return false
      }
    },
  }
}
</script>

<style lang='less' scoped>
.main-box-content {
  .li-menu-item {
    font-size: 14px;
    padding: 4px 0px;
    margin-bottom: 0px;
    cursor: pointer;

    .li-menu-icon {
      margin-right: 8px;
    }
  }

  .li-menu-item:hover {
    color: #409eff;
    cursor: pointer;
  }
}

.query-form-content{
  width: 640px;
}
.tag-item-wrapper {
  display: inline-block;
  white-space: nowrap;
  padding: 0px 4px;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  font-size: 14px;
  background-color: #fafafa;
  margin: 2px 2px;

  .tag-title{
    cursor: pointer;
    color: rgba(0,0,0,0.85);

    .tag-inverse {
      margin-right: 2px;
      color: rgba(255, 0, 0, 1);
    }
  }

  .tag-title-disable {
    color: #918f8f;
    text-decoration: line-through;
  }

  .tag-title:hover,.tag-title-disable:hover {
    color: #409eff
  }

  .tag-btn {
    margin-left: 6px;
    font-size: 12px;
  }

  .tag-btn:hover {
    cursor: pointer;
    color: #409eff
  }
}

.tag-item-wrapper:hover{
  border: 1px solid #d9d9d9;
  background-color: #ecf5ff;
}
</style>