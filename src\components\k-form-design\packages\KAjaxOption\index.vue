<template>
  <div class="ajax-options">
    <a-form-item label="接口">
      <a-input v-model="value.url" placeholder="请输入请求地址" />
    </a-form-item>
    <a-form-item label="请求方式">
      <a-select :options="typeOptions" v-model="value.type" />
    </a-form-item>
    <a-form-item label="请求参数">
      <a-button @click="editParams(1)" style="width: 100%">编辑数据</a-button>
    </a-form-item>
    <a-form-item label="header信息">
      <a-button @click="editParams(2)" style="width: 100%">编辑数据</a-button>
    </a-form-item>
    <a-form-item label="解析函数">
      <a-button @click="editParams(3)" style="width: 100%">编辑数据</a-button>
    </a-form-item>
    <a-modal
      title="编辑请求参数"
      :visible="visible"
      @ok="visible = false"
      @cancel="visible = false"
      cancelText="关闭"
      :destroyOnClose="true"
      centered
      width="850px"
    >
      <div class="json-box-9136076486841527" v-if="editType === 1">
        <codemirror style="height: 100%" ref="myEditor" v-model="value.params"></codemirror>
      </div>
      <div class="json-box-9136076486841527" v-else-if="editType === 2">
        <codemirror style="height: 100%" ref="myEditorHeader" v-model="value.header"></codemirror>
      </div>
      <div class="json-box-9136076486841527" v-else-if="editType === 3">
        <codemirror
          style="height: 100%"
          ref="myEditorFunc"
          v-model="value.callFunc"
          :options="{
            mode: 'javascript',
            extraKeys: { 'Ctrl-Space': 'autocomplete' },
          }"
        ></codemirror>
      </div>
    </a-modal>
  </div>
</template>
<script>
import { codemirror } from 'vue-codemirror-lite'
export default {
  name: 'KAjaxOption',
  components: {
    codemirror,
  },
  props: {
    value: {
      type: Object,
      required: true,
    },
    type: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      typeOptions: [
        {
          label: 'GET',
          value: 'GET',
        },
        {
          label: 'POST',
          value: 'POST',
        },
      ],
      visible: false,
      editType: 1,
    }
  },
  created() {},
  mounted() {},
  watch: {
    value: {
      deep: false,
      immediate: true,
      handler() {
        
      },
    },
  },
  methods: {
    editParams(t) {
      this.editType = t
      this.visible = true
    },
  },
}
</script>
<style lang="less" scoped>
.ajax-options {
  border: 1px #8c8c8c solid;
  padding: 5px;
  border-radius: 8px;
}
::v-deep .CodeMirror{
  height: 100%;
}
</style>