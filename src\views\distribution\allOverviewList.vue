<template>
  <a-row class='row-wrapper' type='flex' justify='space-between'>
    <a-col :lg='6' :md='8' :sm='10' :xl='4' :xs='10' class="left-col-wrapper">
      <cluster-fields @change-field='setCluster'></cluster-fields>
    </a-col>
    <a-col :lg='18' :md='16' :sm='14' :xl='20' :xs='14' class="right-col-wrapper">
      <div class="info-wrapper">
        <div class="area-box">
          <div class="area-title-box">
            <span class="area-title">基本信息</span>
          </div>
          <a-descriptions :column="{ xxl: 2, xl: 2, lg: 2, md: 2, sm: 1, xs: 1 }" bordered>
            <a-descriptions-item label="集群名称">{{ data.clusterName }}</a-descriptions-item>
            <a-descriptions-item label="运行时长">{{ data.runtime }}</a-descriptions-item>
            <a-descriptions-item>
             <span slot="label">
               在线节点数
               <span
                 style="display:inline-block;height: 6px;width: 6px;border-radius: 3px;background-color: green;margin-left: 8px;"></span>
             </span>
              {{ data.onlineNode }}
            </a-descriptions-item>

            <a-descriptions-item>
             <span slot="label">
               在线磁盘数
               <span
                 style="display:inline-block;height: 6px;width: 6px;border-radius: 3px;background-color: green;margin-left: 8px;"></span>
             </span>
              {{ data.onlineDisk }}
            </a-descriptions-item>

            <a-descriptions-item>
             <span slot="label">
               离线节点数
               <span
                 style="display:inline-block;height: 6px;width: 6px;border-radius: 3px;background-color: red;margin-left: 8px;"></span>
             </span>
              {{ data.offlineNode }}
            </a-descriptions-item>

            <a-descriptions-item>
             <span slot="label">
               离线磁盘数
               <span
                 style="display:inline-block;height: 6px;width: 6px;border-radius: 3px;background-color: red;margin-left: 8px;"></span>
             </span>
              {{ data.offlineDisk }}
            </a-descriptions-item>

            <a-descriptions-item label="总存储容量">{{ data.totalStorageCapacity }}</a-descriptions-item>
            <a-descriptions-item label="可用存储容量">{{ data.availableStorageCapacity }}</a-descriptions-item>
          </a-descriptions>
        </div>

        <div class="area-box">
          <div class="area-title-box">
            <span class="area-title">性能信息</span>
          </div>
          <a-table :columns='columns' :dataSource='dataSource' row-key='id' bordered style="margin-top: 16px;"
                   :pagination="false">
            <span slot="Percent" slot-scope="text">{{ (text * 100).toFixed(0) }}%</span>
            <template slot="disk" slot-scope="text,record">
              <span style='color: #409eff;cursor: pointer' @click='handleDetailPage(record)'>{{ text }}</span>
            </template>
          </a-table>
        </div>
      </div>
    </a-col>
  </a-row>
</template>

<script>
import { getAction } from '@/api/manage'
import ClusterFields from '@views/distribution/dataSecurity/modules/ClusterFields.vue'

export default {
  name: 'allOverviewList',
  components: {
    ClusterFields
  },
  data() {
    return {
      treeData: [],
      dataSource: [],
      data: {},
      columns: [{
        title: '节点名称',
        align: 'center',
        dataIndex: 'nodeName'
      }, {
        title: '磁盘使用率',
        align: 'center',
        dataIndex: 'allDiskUsedPercent',
        scopedSlots: {
          customRender: 'Percent'
        }
      }, {
        title: '内存使用量',
        align: 'center',
        dataIndex: 'memUsageString'
      }, {
        title: '磁盘延迟(ms)',
        align: 'center',
        dataIndex: 'allDiskLatencyAverage'
      }, {
        title: 'IO操作等待数',
        align: 'center',
        dataIndex: 'requestsWaitNum'
      }],
      url: {
        // add: '/distributedStorage/cluster',
        info: '/distributedStorage/cluster/info'
      }
    }
  },
  created() {
    // this.getData()
  },
  methods: {
    setCluster(cluster) {
      // this.cluster = cluster
      this.getInfo(cluster.id)
    },
    getData() {
      this.treeData = [{
        clusterName: '全部集群',
        id: '0',
        children: []
      }]
      getAction(this.url.add).then((res) => {
        this.treeData[0].children = res.result
        this.getInfo(this.treeData[0].children[0].id)
      })
    },
    getInfo(id) {
      getAction(this.url.info, {
        clusterId: id
      }).then((res) => {
        this.data = res.result
        this.dataSource = res.result.nodeInfos
      })
    },
    handleDetailPage: function(record) {
      this.$parent.pButton2(1, record)
    },
    seChange(e) {
      this.getInfo(e[0])
    }
  }
}
</script>

<style scoped lang="less">
.row-wrapper {
  height: 100%;

  .left-col-wrapper {
    height: 100%;
    background: #fff;
    padding: 16px;
    border-radius: 3px;
  }

  .right-col-wrapper {
    height: 100%;

    .info-wrapper {
      height: 100%;
      overflow: hidden;
      overflow-y: auto;
      margin-left: 16px;
      background: #fff;
      border-radius: 3px;
      padding: 24px;

      .area-box {
        margin-bottom: 20px;
        margin-right: 1px;

        .area-title-box {
          margin-bottom: 10px;

          .area-title {
            font-size: 14px;
            padding-left: 7px;
            border-left: 4px solid #1e3674;
          }
        }
      }
    }
  }
}
</style>