<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container>
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-item label="字段标识" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['code', validatorRules.assetsCode]" :allowClear="true" autocomplete="off"
                placeholder="请输入字段标识"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="字段名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['name', validatorRules.assetsName]" :allowClear="true" autocomplete="off"
                placeholder="请输入字段名称"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="字段类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag type="list" v-decorator="['type', validatorRules.assetsType]" :trigger-change="true"
                dictCode="type_code" placeholder="请选择字段类型" />
            </a-form-item>
          </a-col>
          <a-col :span="24" v-if="showDictcode">
            <a-form-item label="数据字典" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-select :getPopupContainer="(node) => node.parentNode"
                v-decorator="['dictType', validatorRules.assetsDictcode]" optionFilterProp="children"
                placeholder="请选择数据字典" allowClear showSearch>
                <a-select-option v-for="item in this.dictList" :key="item.dictCode" :value="item.dictCode">
                  {{ item.dictName }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="字段描述" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['description', validatorRules.assetsDescription]" :allowClear="true"
                autocomplete="off" placeholder="请输入字段描述"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="必填字段" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-switch v-decorator="['isInput', { initialValue: false, valuePropName: 'checked' }]" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
import { httpAction, getAction, deleteAction } from '@/api/manage'
  import pick from 'lodash.pick'
  import JFormContainer from '@/components/jeecg/JFormContainer'

  export default {
    name: 'DesignTemplateFieldForm',
    components: {
      JFormContainer,
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false,
      },
    },
    data() {
      return {
        form: this.$form.createForm(this, {
          onFieldsChange: this.onFieldsChange
        }),
        model: {},
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          },
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          },
        },
        confirmLoading: false,
        // initFieldType:'',
        validatorRules: {
          assetsCode: {
            rules: [{
                required: true,
                message: '请输入字段标识!'
              },
              {
                min: 2,
                max: 20,
                message: '字段标识在2-20个字符之间'
              },
            ],
          },
          assetsName: {
            rules: [{
                required: true,
                message: '请输入字段名称!'
              },
              {
                min: 2,
                max: 20,
                message: '字段名称在2-20个字符之间'
              },
            ],
          },
          assetsType: {
            rules: [{
              required: true,
              message: '请选择字段类型!'
            }],
          },
          assetsDictcode: {
            rules: [{
              required: true,
              message: '请输入字典类型'
            }],
          },
          assetsDescription: {
            rules: [{
                required: true,
                message: '请输入字段描述!'
              },
              {
                min: 2,
                max: 50,
                message: '字段描述在2-50个字符之间'
              },
            ],
          },
        },
        url: {
        //   add: '/extendField/extendField/add',
        //   edit: '/extendField/extendField/edit'
        },
        showDictcode: false,
        dictList: []
      }
    },
    created() {
      this.getDict()
    },
    methods: {
      getDict() {
        getAction('sys/dict/getAllDict').then((res) => {
          if (res.success) {
            this.dictList = res.result
          }
        })
      },
      // 监听表单字段变化
      onFieldsChange(props, fields) {
        // 表单type为下拉框 显示字典类型
        if (fields.type) {
          this.showDictcode = fields.type.value === '下拉框'
        }
      },
      add() {
        this.edit(this.model)
      },
      edit(record) {
        this.form.resetFields()
        record['isInput'] = Boolean(record['isInput'])
        this.model = Object.assign({}, record)
        // this.initFieldType=this.model.id?this.model.type:''
        this.visible = true
        this.$nextTick(() => {
          this.form.setFieldsValue(
            pick(
              this.model,
              'code',
              'name',
              'type',
              'description',
              'isInput'
            )
          )
          if (record.type === "下拉框") {
            // 等到下拉框组件渲染完成 赋值
            this.$nextTick(() => {
              this.form.setFieldsValue({
                "dictType": record.dictType
              })
            })

          }
        })
      },
      submitForm() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            // if (that.model.id&&that.initFieldType!==values.type){
            //   this.$confirm({
            //     title: '确认提交',
            //     okText: '是',
            //     cancelText: '否',
            //     content: '字段类型发生改变，可能会导致知识库表单（弹窗）中有关字段数据显示异常，确认修改吗？',
            //     onOk: function () {
            //       that.lastSubmitForm(values)
            //     }
            //   })
            // }else{
              that.lastSubmitForm(values)
            // }
          }
        })
      },
      lastSubmitForm(values){
        const that = this
        that.confirmLoading = true
        let httpurl = ''
        let method = ''
        if (!that.model.id) {
          httpurl += that.url.add
          method = 'post'
        } else {
          httpurl += that.url.edit
          method = 'put'
        }

        values['isInput'] = Number(values['isInput'])
        let formData = Object.assign(that.model, values)
        httpAction(httpurl, formData, method)
          .then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.$emit('ok')
              that.model = {}
            } else {
              that.$message.warning(res.message)
              that.model = {}
            }
          })
          .finally(() => {
            that.confirmLoading = false
          })
      },
      popupCallback(row) {
        this.form.setFieldsValue(
          pick(
            row,
            'code',
            'name',
            'type',
            'description'
          )
        )
      },
    },
  }
</script>