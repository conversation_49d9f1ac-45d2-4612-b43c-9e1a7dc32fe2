<template>
  <j-modal
    :title='title'
    :width='width'
    :centered='true'
    :visible='visible'
    :destroyOnClose='true'
    switchFullscreen
    cancelText='关闭'
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @cancel='handleCancel'
    :fullscreen.sync='fScreen'
  >
    <a-spin :spinning='confirmLoading'>
      <div id="config-container" style='height: 100%'>
        <textarea id="config-editor" style='width: 100%;height: 100%;'></textarea>
      </div>
    </a-spin>
  </j-modal>
</template>
<script>
// import dedent from 'dedent'
import CodeMirror from 'codemirror'
import 'codemirror/lib/codemirror.css'
import 'codemirror/mode/shell/shell.js'
export default {
  name: 'ViewConfigModal',
  props: {},
  data() {
    return {
      title: '新增',
      width: '1300px',
      fScreen: true,
      disableSubmit: false,
      visible: false,
      confirmLoading: false,
      cmdStr: ``
    }
  },
  methods: {
    loadConfig(record) {
      this.visible = true
      this.fScreen = true
      this.cmdStr=record&&record.length>0?record.replaceAll('<br/>','\n'):''
      this.$nextTick(()=>{
        this.init()
      })
    },
    close() {
      this.visible = false
    },
    handleCancel() {
      this.close()
    },
    init(){
      let that=this
      let container=document.getElementById('config-editor')
      var editor = CodeMirror.fromTextArea(container, {
        //tabSize: 4,
        //styleActiveLine: true,
        lineNumbers: true,
        //lineWrapping: true,
        //line: true,
        mode: 'text/x-sh',
        readOnly: true
      });
      var deviceConfig = that.cmdStr;
      editor.setValue(deviceConfig);
    }
  }
}
</script>
<style scoped lang='less'>
@import '~@assets/less/normalModal.less';

::v-deep .ant-modal {
  height: 100%;

  .ant-modal-content {
    height: 100% !important;

    .ant-modal-body {
      height: calc(100% - 55px - 53px) !important;

      .ant-spin-nested-loading {
        height: 100% !important;

        .ant-spin-container {
          height: 100% !important;
        }
      }
    }
  }
}

::v-deep .CodeMirror {
  border: 1px solid #ddd;
  background: #fff !important;
  color: #000 !important;
  height: 100%;

  div{
    color: #000 !important;
  }
  span{
    color: #000 !important;
  }
}

</style>