<template>
  <div style="margin-top: 12px">
    <a-table
      bordered
      :pagination="false"
      rowKey="id"
      :columns="columns"
      :data-source="tableData"
    >
      <a slot="name" slot-scope="text">{{ text }}</a>
    </a-table>
  </div>
</template>
<script>
let $this;

export default {
  name: "MonthStyle",
  props: {
    days: {
      default: () => [],
    },
    currentMonth: {
      default: 1,
    },
    currentYear: {
      default: 1970,
    },
  },
  data() {
    return {
      layWidth: 0,
      layHeight: 0,
      tableData: [],
    };
  },
  created() {
    $this = this;
    this.getTableData();
  },
  mounted() {},
  computed: {
    columns() {
      let map = {
        1: "周一",
        2: "周二",
        3: "周三",
        4: "周四",
        5: "周五",
        6: "周六",
        7: "周日",
      };
      let columns = [
        {
          title: "时间",
          dataIndex: "time",
          key: "time",
          //   scopedSlots: { customRender: "time" },
          width: 120,
          align: "center",
          customHeaderCell: this.customHeaderCellFunc,
        },
        {
          title: "",
          dataIndex: "1",
          key: "1",
        },
        {
          title: "",
          dataIndex: "2",
          key: "2",
          ellipsis: true,
        },
        {
          title: "",
          dataIndex: "3",
          key: "3",
          // ellipsis: true,
        },
        {
          title: "",
          dataIndex: "4",
          key: "4",
          // ellipsis: true,
        },
        {
          title: "",
          dataIndex: "5",
          key: "5",
          // ellipsis: true,
        },
        {
          title: "",
          dataIndex: "6",
          key: "6",
          // ellipsis: true,
        },
        {
          title: "",
          dataIndex: "7",
          key: "7",
          // ellipsis: true,
        },
      ];
      if ($this.days[0]) {
        let temObj = $this.days[0].dayArr;
        columns.forEach((el) => {
          if (el.dataIndex !== "time") {
            let dateStr =
              temObj[el.dataIndex - 1].month +
              "/" +
              temObj[el.dataIndex - 1].date;
            el.title = map[el.dataIndex] + " " + dateStr;
            el.align = "center";
            el.dateObj = temObj[el.dataIndex - 1];
            el.customHeaderCell= this.customHeaderCellFunc;
            if (this.isToday(temObj[el.dataIndex - 1])) {
              el.customRender = this.customRenderFunc;
              el.customCell = this.customCellFunc;
            }
          }
        });
      }
      return columns;
    },
  },
  methods: {
      customHeaderCellFunc(column) {
      return {
        style: {
          background: "#f5f8fa",
        }
      };
    },
    customRenderFunc(value, record, index) {
      const obj = {
        children: value,
        attrs: {},
      };
      return obj;
    },
    rowClassFunc(record, idx) {
      console.log("行跟属性", record, idx);
    },
    customCellFunc(cellData, ridx) {
      return {
        style: { "background-color": "#fcf8e3", color: "red" },
        on: {
          click: (event) => { }, 
          dblclick: (event) => {},
          contextmenu: (event) => {},
          mouseenter: (event) => {},
          mouseleave: (event) => {},
        },
      };
    },
    getTableData() {
      for (let i = 0; i < 24; i++) {
        this.tableData.push({
          id: i,
          time: `${i}时`,
        });
      }
      console.log("获取到数据", this.tableData);
    },
    isToday(item) {
      let d = new Date();
      if (
        item.month === d.getMonth() + 1 &&
        item.date === d.getDate() &&
        item.year === d.getFullYear()
      ) {
        return true;
      } else {
        return false;
      }
    },
  },
};
</script>
<style lang="less" scoped>
ul {
  padding: 0;
  margin: 0;
}
li {
  list-style: none;
}
.event-calender {
  width: 100%;
  margin: 0 auto;
}
.weeks {
  margin-top: 20px;
  display: flex;
  border: 1px solid #e1e4e7;
  border-left: none;
  border-bottom: none;
}
.week {
  height: 50px;
  line-height: 50px;
  flex: 1;
  text-align: center;
  border-left: 1px solid #e1e4e7;
  background: rgb(245, 248, 250);
}
.other-m {
  color: rgba(51, 71, 91, 0.45);
  background: rgb(245, 245, 245);
}
.today {
  color: red;
  background-color: #fcf8e3;
}
// .other-m-bg{

// }
.event-container {
  width: 100%;
  box-sizing: border-box;
  position: absolute;
  height: 0;
  .event-item {
    box-sizing: border-box;
    padding-top: 2px;
    position: absolute;
    .event-content {
      color: #ffffff;
      cursor: pointer;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      word-break: break-all;
      &.is-start {
        margin-left: 1px;
        border-top-left-radius: 12px;
        border-bottom-left-radius: 12px;
      }
      &.is-end {
        border-top-right-radius: 12px;
        border-bottom-right-radius: 12px;
      }
      .event-text {
        padding-left: 5px;
      }
    }
  }
}
.c-container {
  border: 1px solid #eee;
  border-top: none;
  box-sizing: border-box;
}
.c-header {
  display: flex;
  .c-h-cell {
    height: 180px;
    box-sizing: border-box;
    text-align: right;
    padding-right: 15px;
    flex: 1;
    border-top: 1px solid #e1e4e7;
    border-right: 1px solid #e1e4e7;
    &:last-child {
      border-right: none;
    }
  }
  .cell-day {
    display: inline-block;
    width: 100%;
    font-size: 16px;
    line-height: 45px;
    // cursor: pointer;
  }
}
.event-bg {
  position: relative;
  display: flex;
  .bg-cell {
    box-sizing: border-box;
    flex: 1;
    border-top: 1px solid #e1e4e7;
    border-right: 1px solid #e1e4e7;
    &:last-child {
      border-right: none;
    }
  }
}
</style>

