<template>
  <a-modal
    :title="title"
    :width="modalWidth"
    :visible='visible'
    :maskClosable='false'
    :confirmLoading='confirmLoading'
    @cancel="handleCancel"
    cancelText="关闭"
  >
    <div v-if="generateStartFormVisible">
      <k-form-build
      :value="startFormJson"
      :dynamicData="dynamicData"
      :defaultValue="variables"
      :disabled="disabledEdit"
      @assetsHandler="assetsHandler"
      ref="generateStartForm" />
    </div>
     <assets-update-list ref="assetsUpdateModal"></assets-update-list>
    <template slot="footer">
      <a-button key="close" @click="handleCancel"> 关闭 </a-button>
    </template>
  </a-modal>
</template>

<script>
import { getAction } from '@/api/manage'
import AssetsUpdateList from '@/views/cmdb/assets/assetsUpdate/AssetsUpdateList'
export default {
  name: 'businessAdd',
   components: {
    AssetsUpdateList,
  },
  data() {
    return {
      title: '详情',
      confirmLoading: false,
      modalWidth: '1200px',
      visible: false,
      required: false,
      generateStartFormVisible: false,
      startFormJson: {},
      variables: null,
      disabledEdit: true,
      business: null,
      dynamicData: {
        bpmnDepartTreeData: []
        }
    }
  },
  created() {
    this.getTreeData()
  },
  methods: {
     //打开资产编辑
    assetsHandler(e){
      if (this.variables && this.variables.tempProcessInstanceId) {
        this.$refs.assetsUpdateModal.tempId = this.variables.tempProcessInstanceId
      } else {
        this.$refs.assetsUpdateModal.tempId = ''
      }
      this.$refs.assetsUpdateModal.init(e)
    },
    init(record) {
        this.visible = true
        getAction('/flowable/processInstance/formData', { processInstanceId: record.id }).then((res) => {
        console.log(res)
        if (res.code != 200) {
          this.$message.error(res.message)
        } else {
          const data = res.result
          this.showBusinessKey = data.showBusinessKey
          this.businessKey = data.businessKey
          if (data && data.renderedStartForm) {
            this.startFormJson = JSON.parse(data.renderedStartForm)
            this.variables = data.variables
            this.generateStartFormVisible = true
          }
        }
      })
    },
    getTreeData() {
      getAction('/sys/sysDepart/queryTreeList').then((res) => {
        this.dynamicData.bpmnDepartTreeData = res.result
      })
    },
    close() {
      this.generateStartFormVisible = false
      this.visible = false
    },
    handleCancel() {
      this.close()
    },
  },
}
</script>
<style lang="less" scoped>
@import '~@assets/less/YQNormalModal.less';
/deep/ .ant-modal {
  top: 30px;
}
/deep/ .ant-modal-body {
  max-height: calc(100vh - 55px - 53px - 30px - 30px); // 55:tab选项卡高度;53:按钮区域高度;30:距离顶部/底部的间距
  overflow: auto;
}
</style>