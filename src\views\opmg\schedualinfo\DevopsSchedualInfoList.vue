<template>
  <a-card :bordered="false" style="height: 100%; overflow: hidden; overflow-y: auto" class="vScroll">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24"> </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd">新增</a-button>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay" style='text-align: center'>
          <a-menu-item key="1" @click="batchDel">删除</a-menu-item>
        </a-menu>
        <a-button> 批量操作 <a-icon type="down"/></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <a-table
      ref="table"
      bordered
      rowKey="id"
      :columns="columns"
      :dataSource="dataSource"
      :pagination="ipagination"
      :loading="loading"
      :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      class="j-table-force-nowrap"
      @change="handleTableChange"
    >
      <template slot="htmlSlot" slot-scope="text">
        <div v-html="text"></div>
      </template>
      <template slot="imgSlot" slot-scope="text">
        <span v-if="!text" style="font-size: 14px">无图片</span>
        <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width: 80px; font-size: 14px" />
      </template>
      <template slot="fileSlot" slot-scope="text">
        <span v-if="!text" style="font-size: 14px">无文件</span>
        <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
          下载
        </a-button>
      </template>

      <span slot="action" slot-scope="text, record" class="caozuo">
        <a @click="handleEdit(record)">编辑</a>

        <a-divider type="vertical" />
        <a @click="handleDetail(record)">详情</a>
        <a-divider type="vertical" />
        <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
          <a>删除</a>
        </a-popconfirm>
      </span>
    </a-table>

    <devops-schedual-info-modal ref="modalForm" @ok="modalFormOk"></devops-schedual-info-modal>
  </a-card>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import DevopsSchedualInfoModal from './modules/DevopsSchedualInfoModal'
import JSuperQuery from '@/components/jeecg/JSuperQuery.vue'

export default {
  name: 'DevopsSchedualInfoList',
  mixins: [JeecgListMixin, mixinDevice],
  components: {
    DevopsSchedualInfoModal,
    JSuperQuery,
  },
  data() {
    return {
      description: '班次管理表管理页面',
      // 表头
      columns: [
        // {
        //   title: '#',
        //   dataIndex: '',
        //   key:'rowIndex',
        //   width:60,
        //   align:"center",
        //   customRender:function (t,r,index) {
        //     return parseInt(index)+1;
        //   }
        // },
        {
          title: '名称',
          align: 'center',
          dataIndex: 'name',
        },
        {
          title: '开始时间',
          align: 'center',
          dataIndex: 'startTime',
        },
        {
          title: '结束时间',
          align: 'center',
          dataIndex: 'endTime',
        },
        // {
        //   title:'创建人',
        //   align:"center",
        //   dataIndex: 'createBy'
        // },
        // {
        //   title:'创建日期',
        //   align:"center",
        //   dataIndex: 'createTime',
        //   customRender:function (text) {
        //     return !text?"":(text.length>10?text.substr(0,10):text)
        //   }
        // },
        // {
        //   title:'更新人',
        //   align:"center",
        //   dataIndex: 'updateBy'
        // },
        // {
        //   title:'更新日期',
        //   align:"center",
        //   dataIndex: 'updateTime',
        //   customRender:function (text) {
        //     return !text?"":(text.length>10?text.substr(0,10):text)
        //   }
        // },
        // {
        //   title:'所属部门',
        //   align:"center",
        //   dataIndex: 'sysOrgCode'
        // },
        // {
        //   title: '操作',
        //   dataIndex: 'action',
        //   align:"center",
        //   fixed:"right",
        //   width:147,
        //   scopedSlots: { customRender: 'action' }
        // }
      ],
      url: {
        list: '/schedualinfo/devopsSchedualInfo/list',
        delete: '/schedualinfo/devopsSchedualInfo/delete',
        deleteBatch: '/schedualinfo/devopsSchedualInfo/deleteBatch',
        exportXlsUrl: '/schedualinfo/devopsSchedualInfo/exportXls',
        importExcelUrl: 'schedualinfo/devopsSchedualInfo/importExcel',
      },
      dictOptions: {},
      superFieldList: [],
    }
  },
  created() {
    this.getSuperFieldList()
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
  },
  methods: {
    initDictConfig() {},
    getSuperFieldList() {
      let fieldList = []
      fieldList.push({ type: 'string', value: 'name', text: '名称', dictCode: '' })
      fieldList.push({ type: 'datetime', value: 'startTime', text: '开始时间' })
      fieldList.push({ type: 'datetime', value: 'endTime', text: '结束时间' })
      // fieldList.push({type:'string',value:'createBy',text:'创建人',dictCode:''})
      // fieldList.push({type:'date',value:'createTime',text:'创建日期'})
      // fieldList.push({type:'string',value:'updateBy',text:'更新人',dictCode:''})
      // fieldList.push({type:'date',value:'updateTime',text:'更新日期'})
      // fieldList.push({type:'string',value:'sysOrgCode',text:'所属部门',dictCode:''})
      this.superFieldList = fieldList
    },
  },
}
</script>
<style lang="less" scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
</style>
