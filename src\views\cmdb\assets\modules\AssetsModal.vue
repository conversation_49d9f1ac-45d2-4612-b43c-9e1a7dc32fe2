<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    :centered="true"
    :destroyOnClose="true"
    switchFullscreen
    @ok="handleOk"
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }"
    @cancel="handleCancel"
    cancelText="关闭"
  >
    <assets-form ref="realForm" @ok="submitCallback" :disabled="disableSubmit" :tempId="tempId" :assetsOperateType="assetsOperateType" @addAssetsChangeOk="submitAssetsChangeCallback"></assets-form>
  </j-modal>
</template>

<script>
import AssetsForm from './AssetsForm'
import AFormItem from 'ant-design-vue/es/form/FormItem'
import { httpAction, getAction } from '@/api/manage'
import ARow from 'ant-design-vue/es/grid/Row'

export default {
  name: 'AssetsModal',
  components: {
    ARow,
    AFormItem,
    AssetsForm,
  },
  data() {
    return {
      title: '',
      width:'1000px',
      visible: false,
      disableSubmit: false,
    }
  },
  props: {
    // 资产变更--临时变更id
    tempId: {
      type: String,
      default: '',
    },
    // 资产操作类型
    assetsOperateType: {
      type: String,
      default: '',
    }
  },
  computed: {},
  methods: {
    add() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.add()
      })
    },
    edit(record) {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.edit(record)
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      if (this.assetsOperateType == 'addTempAssets' || this.assetsOperateType=='editTempAssets' || this.assetsOperateType == 'editHasAssets') {
        this.$nextTick(() => {
          this.$refs.realForm.submitAssetsChangeForm()
        })
      } else {
        this.$nextTick(() => {
          this.$refs.realForm.submitForm()
        })
      }
    },
    submitCallback() {
      this.$emit('ok')
      this.visible = false
    },
    handleCancel() {
      this.close()
    },
    // 资产变更新增的数据
    submitAssetsChangeCallback(data) {
      console.log(data,'新增的数据1')
      this.$emit('addAssetsChangeOk', data)
      this.visible = false
    },
  },
}
</script>
<style lang='less' scoped>
@import '~@assets/less/normalModal.less';
/*.p-info-title {
  font-family: PingFangSC-Medium;
  font-size: 18px;
  color: #000000;
}

.span-status {
  div {
    margin: 0;
    margin-top: 10px;
  }
}

.span-status div {
  margin: 0;
}

.div-info {
  margin-top: 12px;
}*/
</style>