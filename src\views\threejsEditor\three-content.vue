<template>
  <div style='height: 100%;width: 100%' ref='threeBox' :id='elementId'></div>
</template>
<script>
import * as THREE from 'three'
import { TransformControls } from 'three/examples/jsm/controls/TransformControls'
import threeInitMixin from '@views/threejsEditor/threeUtils/threeInitMixin'
import { jiguiMixins } from './threeUtils/jiguiMixins'
import { createMixins } from './threeUtils/createMixins'
import { clickMixins } from './threeUtils/clickMixins'
// import { dragControl } from './threeUtils/dragControl'
import { getFileAccessHttpUrl } from '@/api/manage'

export default {
  mixins: [threeInitMixin, createMixins, jiguiMixins, clickMixins],
  props: {
    threeJson: {
      type: String,
      default: ''
    },
    modelsArr: {
      type: Array,
      default: () => [],
      required: true
    },
    isEditor: {
      type: Boolean,
      default: true
    },
    elementId: {
      type: String,
      default: 'WebGL-output'
    }
  },
  data() {
    return {
      type: 1,
      dragControls: null,
      transformControls: null,
      webglHeight: 0,
      webglWidth: 0,
      imgBaseUrl: process.env.BASE_URL,
      renderer: null,
      confirmshow: false,
      boxW: 0,
      boxH: 0,
      animationFrameId: null,
      container: null
    }
  },

  created() {
    this.$root.$on('addTest', (e, t) => {
      this.createThreeObject(e, t)
    })
    this.$root.$on('showChild', (e) => {
      this.changeTransObj(e)
    })
    this.$root.$on('modelInfoChange', () => {
      if (this.transformControls.object) {
        this.transformControls.attach(this.transformControls.object)
      }
    })
    this.$root.$on('unitChange', (group, data) => {
      this.jiguiWall(group, data)
    })
  },
  mounted() {
    this.init()
  },
  watch: {
    '$store.state.threejs.bgConfig'(e) {
      if (e) {
        let configData = e
        if (configData.bgType === 'color') {
          scene.background = new THREE.Color(configData.bgColor)
        } else if (configData.bgType === 'pic' && configData.bgPic) {
          let url = getFileAccessHttpUrl(configData.bgPic)
          scene.background = new THREE.TextureLoader().load(url)
        } else {
          scene.background = new THREE.Color()
        }
      }
    }
  },
  destroyed() {
    this.$root.$off('addTest')
    this.$root.$off('showChild')
    this.$root.$off('modelInfoChange')
    this.$root.$off('unitChange')
    this.destroyThreejs()
  },
  beforeDestroy() {
    if (this.controls) {
      this.controls.dispose()
    }
  },
  methods: {
    initTransformControls() {
      let that = this
      // 添加平移控件
      this.transformControls = new TransformControls(camera, this.renderer.domElement)
      this.transformControls.name = 'transformControls'
      // this.transformControls.showX = true;
      // this.transformControls.showY = true;
      // this.transformControls.showZ = true;
      //平移控件事件监听
      this.transformControls.addEventListener('change', function(e) {
        if (that.confirmshow) return
        if (e.target.object && Object.values(e.target.object.scale).some((el) => el < 0)) {
          that.confirmshow = true
          that.transformControls.enabled = false
          that.$warning({
            title: '模型大小缩放不能小于0',
            content: '',
            keyboard: false,
            cancelText: '',
            onOk() {
              let temTarget = that.transformControls.object
              let scaleobj = temTarget.scale
              for (let k in scaleobj) {
                if (scaleobj[k] < 0) {
                  scaleobj[k] = 1
                }
              }

              that.transformControls.detach(that.transformControls.object)
              that.transformControls.enabled = true
              let tmier = setTimeout(() => {
                that.confirmshow = false
                that.transformControls.attach(temTarget)
                clearTimeout(tmier)
              }, 300)
            },
            onCancel() {
            }
          })
          return
        }

        if (e.target.object && e.target.object.groupId) {
          let groupTarget = scene.children.find((el) => {
            return el.uuid === e.target.object.groupId
          })
          // console.log("拖拽 === ",groupTarget)
          groupTarget.position.x = e.target.object.position.x
          groupTarget.position.y = e.target.object.position.y
          groupTarget.position.z = e.target.object.position.z
          groupTarget.rotation.x = e.target.object.rotation.x
          groupTarget.rotation.y = e.target.object.rotation.y
          groupTarget.rotation.z = e.target.object.rotation.z
          if (groupTarget.userData.glb) {//glb模型整体缩放
            groupTarget.scale.y = e.target.object.scale.y
            groupTarget.scale.x = e.target.object.scale.x
            groupTarget.scale.z = e.target.object.scale.z
            // console.log("拖拽 缩放=== ",e.target.object.scale)
          } else if (groupTarget.name === 'yq_group_jigui') {//机柜不改变模型大小 根据单元改变
            Object.assign(groupTarget.scale, e.target.object.scale)
          }
        }
        that.$root.$emit('update-model-info')
      })
      this.transformControls.addEventListener('mouseDown', (e)=>{
        this.controls.enablePan = false
        this.controls.enableRotate = false
      })
      this.transformControls.addEventListener('mouseUp', (e)=>{
        this.controls.enablePan = true
        this.controls.enableRotate = true
      })
      scene.add(this.transformControls)
    },

    changeThreeMode(modeType) {
      //"translate", "rotate"  "scale"
      this.transformControls.setMode(modeType)
    },
    async init() {
      this.container = document.getElementById(this.elementId)
      this.$store.commit('threejs/changeEditorStatus', this.isEditor)
      if (this.$refs.threeBox) {
        let rect = this.$refs.threeBox.getBoundingClientRect()
        this.boxH = rect.height
        this.boxW = rect.width
      }
      this.initScene()
      this.importScene()
      this.initCamera()
      this.initRenderer()
      this.initLight()
      this.initControls()
      this.initTransformControls()
      // this.initDragControls();
      this.container.removeEventListener('dblclick', this.onMouseDblclick)
      this.container.removeEventListener('resize', this.onWindowResize)
      this.container.addEventListener('dblclick', this.onMouseDblclick)
      this.container.addEventListener('resize', this.onWindowResize)
      this.animate()
      // addEventListener("keydown", this.onKeyDown, false);
    },
    //添加模型
    createThreeObject(e, t) {
      let outerMesh = scene.children.find((el) => {
        return el.name === 'yq_outerMesh'
      })
      if (outerMesh) scene.remove(outerMesh)
      let newObj = this[e.create](t)
      console.log('newObj', newObj)
      if (newObj) {
        scene.add(newObj)
        if (e.type === 'wall-door') {
          this.$parent.delGeo()
        }
        this.changeTransObj(newObj)
      }
    },
    changeTransObj(target) {
      this.$parent.threeMode = 'translate'
      this.transformControls.setMode('translate')
      if (this.transformControls.object) {
        this.transformControls.detach(this.transformControls.object)
      }
      if (target) {
        this.transformControls.attach(target)
        this.$root.$emit('show-obj-params', target)
        this.$parent.showMode = true
      }
    },
  }
}
</script>
<style scoped>
#WebGL-output {
  overflow: hidden;
}
</style>