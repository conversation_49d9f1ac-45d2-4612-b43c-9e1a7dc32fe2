<template>
  <div style="height:100%">
      <component :is="pageName" :data="data"/>
  </div>
</template>
<script>
  import OrderFromList from './OrderFromList'
  import OrderFromDetails from './modules/OrderFromDetails'
  export default {
    name: "OrderFromManage",
    data() {
      return {
        isActive: 0,
        data:{}
      };
    },
    components: {
      OrderFromList,
      OrderFromDetails
    },
    created(){
      this.pButton1(0);
    },
    //使用计算属性
    computed: {
      pageName() {
        switch (this.isActive) {
          case 0:
            return "OrderFromList";
            break;

          default:
            return "OrderFromDetails";
            break;
        }
      }
    },
    methods: {
      pButton1(index) {
        this.isActive = index;
      },
      pButton2(index,item) {
        this.isActive = index;
        this.data = item;
      }
    }
  }
</script>