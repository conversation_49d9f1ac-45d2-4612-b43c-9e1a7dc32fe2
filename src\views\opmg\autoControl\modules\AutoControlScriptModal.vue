<template>
  <j-modal :title='title' :width='800' :visible='visible' :confirmLoading='confirmLoading' switchFullscreen
    @ok='handleOk' @cancel='handleCancel' cancelText='关闭'>
    <a-spin :spinning='confirmLoading'>
      <a-form :form='form'>
        <a-form-item :labelCol='labelCol' :wrapperCol='wrapperCol' label='脚本名称'>
          <a-input placeholder='请输入脚本名称' v-decorator="['scriptName',validatorRules.scriptName]" autocomplete="off"
            allowClear />
        </a-form-item>
        <a-form-item :labelCol='labelCol' :wrapperCol='wrapperCol' label='脚本类型'>
          <a-select placeholder='请选择脚本类型' v-decorator="['scriptLanguage', validatorRules.scriptLanguage]"
            :getPopupContainer='(node) => node.parentNode' :allowClear='true'>
            <a-select-option v-for='language in languageList' :key='language.value' :value='language.value'>{{
                language.title
              }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item :labelCol='labelCol' :wrapperCol='wrapperCol' label='运行脚本'>
          <a-textarea placeholder='请输入运行脚本(使用回车换行)' v-decorator="['scriptContext']" :rows="3" allowClear
            autocomplete="off" />
        </a-form-item>
        <a-form-item :labelCol='labelCol' :wrapperCol='wrapperCol' class='two-words' label='图标'>
          <j-image-upload v-decorator="['icon']" :isMultiple='true' bizPath='image/productIcon' :number="1">
          </j-image-upload>
        </a-form-item>
        <a-form-item :labelCol='labelCol' :wrapperCol='wrapperCol' label='附件'>
          <j-svg-upload v-decorator="['scriptFile']" :isMultiple='true' :number="1"></j-svg-upload>
        </a-form-item>
        <a-form-item :labelCol='labelCol' :wrapperCol='wrapperCol' label='描述'>
          <a-textarea placeholder='请输入描述' v-decorator="['description',validatorRules.description]" allowClear
            autocomplete="off" :rows="3" />
        </a-form-item>
      </a-form>
    </a-spin>
  </j-modal>
</template>

<script>
  import {
    httpAction
  } from '@/api/manage'
  import pick from 'lodash.pick'
  import {
    ajaxGetDictItems,
    getDictItemsFromCache
  } from '@api/api'
  import JSvgUpload from '@/components/jeecg/JSvgUpload'
  import JImageUpload from '@/components/jeecg/JImageUpload'
  export default {
    name: 'AutoControlScriptModal',
    components: {
      JSvgUpload,
      JImageUpload
    },
    data() {
      return {
        title: '操作',
        visible: false,
        model: {},
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          }
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          }
        },
        confirmLoading: false,
        form: this.$form.createForm(this),
        validatorRules: {
          scriptName: {
            rules: [{
              required: true,
              message: "请输入脚本名称"
            }, {
              min: 1,
              max: 30,
              message: "脚本名称应在1-30个字符之间"
            }]
          },
          scriptLanguage: {
            rules: [{
              required: true,
              message: "请选择脚本类型"
            }]
          },
          scriptContext: {
            rules: [{
              required: true,
              message: "请输入运行脚本"
            }]
          },
          description: {
            rules: [{
              max: 200,
              message: "描述不能超过200个字符"
            }]
          },
        },
        url: {
          add: '/autoControl/script/add',
          edit: '/autoControl/script/edit'
        },
        languageList: []
      }
    },
    created() {
      this.initDictData()
    },
    methods: {
      initDictData() {
        if (getDictItemsFromCache('script_language')) {
          this.languageList = getDictItemsFromCache('script_language')
          return
        }
        //根据字典Code, 初始化字典数组
        ajaxGetDictItems('script_language', null).then((res) => {
          if (res.success) {
            this.languageList = res.result
          }
        })
      },
      add() {
        this.edit({})
      },
      edit(record) {
        this.form.resetFields()
        this.model = Object.assign({}, record)
        this.visible = true
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.model, 'scriptName', 'icon', 'scriptLanguage',
            'scriptContext', 'scriptFile', 'description'))
          //时间格式化
        })

      },
      close() {
        this.$emit('close')
        this.visible = false
      },
      handleOk() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.model.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'put'
            }
            let formData = Object.assign(this.model, values)
            //时间格式化
            httpAction(httpurl, formData, method).then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            }).finally(() => {
              that.confirmLoading = false
              that.close()
            })


          }
        })
      },
      handleCancel() {
        this.close()
      }


    }
  }
</script>

<style lang='less' scoped>

</style>