<template>
  <a-row :gutter='10' style='height: 100%;' class='vScroll'>
    <a-col style='width:100%;height: 100%;display: flex;flex-direction: column'>
      <!-- 查询区域 -->
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0', marginRight: '12px' }" class="card-style"
              style="width: 100%">
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="产品名称" >
                  <a-select :getPopupContainer='(target) => target.parentNode'
                            showSearch
                            :allowClear='true'
                            v-model='queryParam.productId'
                            placeholder='请选择产品'
                            optionFilterProp='children'>
                    <a-select-option v-for='(item, index) in productList' :key='index' :value='item.id'>
                      {{ item.name }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="设备名称">
                  <a-input :maxLength='maxLength' placeholder="请输入设备名称" v-model="queryParam.name" :allowClear="true" autocomplete="off" />
                </a-form-item>
              </a-col>
              <a-col :span="spanValue">
                <a-form-item label="在线状态">
                  <j-dict-select-tag v-model="queryParam.status" placeholder="请选择在线状态" dictCode="device_status" />
                </a-form-item>
              </a-col>
              <a-col :span="spanValue" v-show='toggleSearchStatus'>
                <a-form-item label="告警状态">
                  <j-dict-select-tag v-model="queryParam.alarmStatus" placeholder="请选择告警状态" dictCode="device_alarm_status" />
                </a-form-item>
              </a-col>
              <a-col :span="spanValue" v-show='toggleSearchStatus'>
                <a-form-item label="启用状态" >
                  <j-dict-select-tag v-model="queryParam.enable" placeholder="请选择启用状态" dictCode="device_enable_status" />
                </a-form-item>
              </a-col>
              <a-col :span='spanValue' v-show='toggleSearchStatus'>
                <a-form-item label='标签'>
                  <a-select :getPopupContainer='(target) => target.parentNode'
                            mode='multiple'
                            showSearch
                            :allowClear='true'
                            :maxTagCount='1'
                            :maxTagTextLength='5'
                            v-model='queryParam.tagKeys'
                            placeholder='请选择标签'
                            optionFilterProp='children'>
                    <a-select-option v-for='(item, index) in taginfoSelectData' :key='index' :value='item.tagKey'>
                      {{ item.tagName }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="colBtnsSpan()">
                    <span class="table-page-search-submitButtons"
                          :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                      <a-button type="primary" class="btn-search btn-search-style" @click="dosearch">查询</a-button>
                      <a-button class="btn-reset btn-reset-style" @click="searchReset">重置</a-button>
                      <a v-if="isVisible" class="btn-updown-style" @click="doToggleSearch">
                        {{ toggleSearchStatus ? '收起' : '展开' }}
                        <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                      </a>
                    </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered="false" style="width: 100%; flex: auto">
        <div class="table-operator table-operator-style">
          <!-- <a-button @click="handleAdd">新增</a-button> -->
          <a-button @click="handleExport">导出</a-button>
<!--          在设备信息管理-->
<!--          <a-dropdown v-if="selectedRowKeys.length > 0">-->
<!--            <a-menu slot='overlay' style='text-align: center'>-->
<!--              <a-menu-item key='1' @click='batchDel'>删除</a-menu-item>-->
<!--            </a-menu>-->
<!--            <a-button> 批量操作-->
<!--              <a-icon type='down' />-->
<!--            </a-button>-->
<!--          </a-dropdown>-->
<!--          <a-button v-if='selectedRowKeys.length > 0' @click='batchEnableOperate(1)'>启用</a-button>-->
<!--          <a-button v-if='selectedRowKeys.length > 0' @click='batchEnableOperate(0)'>禁用</a-button>-->
          <a-popover title="监控状态说明">
            <template slot="content">
              <div style="display:flex;">
                <div>
                  <span>在线：</span>
                  <img src='~@assets/bigScreen/28.png' alt="" class="stateImg" />
                </div>
                <div class="stateBox">
                  <span>离线：</span>
                  <img src='~@assets/bigScreen/57.png' alt="" class="stateImg" />
                </div>
                <div class="stateBox">
                  <span>告警：</span>
                  <img src='~@assets/bigScreen/56.png' alt="" class="stateImg" />
                </div>
                <div class="stateBox">
                  <span style="margin-left: 5px">禁用：</span>
                  <a-icon type="stop" theme="twoTone"  two-tone-color="#eb2f96" style="font-size:16px" class="stateImg"/>
                  <span style="margin-left: 5px"></span>
                </div>
              </div>
            </template>
            <a-icon type="question-circle" theme="twoTone" style="font-size: 18px" />
          </a-popover>
        </div>
        <a-table
          ref="table"
          bordered
          :row-key="(record,index)=>{return record.id}"
          :columns="columns"
          :dataSource="dataSource"
          :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
          :pagination="ipagination"
          :loading="loading"
          :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          @change="handleTableChange">
          <template slot="htmlSlot" slot-scope="text" width="500">
            <div v-html="text"></div>
          </template>
          <template slot="imgSlot" slot-scope="text">
            <span v-if="!text" style="font-size: 14px">无图片</span>
            <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width: 80px; font-size: 14px" />
          </template>

          <template slot="status" slot-scope="text, record">
                <span v-if="record.enable == 1" >
                  <img v-if='record.status==1' src='~@assets/bigScreen/28.png' alt="" class="stateImg" />
                  <img v-else src='~@assets/bigScreen/57.png' alt="" class="stateImg" />
                  <img v-if='record. alarmStatus==1' src='~@assets/bigScreen/56.png' alt="" class="stateImg alarmStatus" />
                </span>
            <span v-else>
                  <a-icon type="stop" theme="twoTone" two-tone-color="#eb2f96" style="font-size: 16px" class="stateImg " />
                </span>
            <span style="margin-left: 10px">{{ text }}</span>
          </template>

          <span slot="action" class="caozuo" slot-scope="text, record">
                <a @click="handleDetailPage(record,2)">查看</a>
                <a-divider type="vertical" />
                 <a @click="handleDetailPage(record,1)">子设备</a>
<!--                <a-dropdown>-->
<!--                  <a class="ant-dropdown-link">更多-->
<!--                    <a-icon type="down" /></a>-->
<!--                  <a-menu slot="overlay">-->
<!--                    <a-menu-item>-->
<!--                      <a @click="bindaTag(record)" class='overlay'>标签</a>-->
<!--                    </a-menu-item>-->
<!--                    <a-menu-item>-->
<!--                      <a @click="handleEdit(record)" class='overlay'>编辑</a>-->
<!--                    </a-menu-item>-->
<!--                     <a-menu-item v-if="!!record.gatewayName">-->
<!--                      <a @click="getUntie(record)" class='overlay'>解绑</a>-->
<!--                    </a-menu-item>-->
<!--                    <a-menu-item>-->
<!--                      <a @click="deleteRecord(record)" class='overlay'>删除</a>-->
<!--                    </a-menu-item>-->
<!--                    <a-menu-item>-->
<!--                      <a class='overlay' @click="deviceEnable(record)" v-if="record.enable === 1">禁用</a>-->
<!--                      <a class='overlay' @click="deviceEnable(record)" v-else>启用</a>-->
<!--                    </a-menu-item>-->
<!--                  </a-menu>-->
<!--                </a-dropdown>-->
              </span>
          <template slot="Enable" slot-scope="text,record">
            <div v-if="record.enable === 1">
              <!-- <a-icon type="check-circle" theme="twoTone" two-tone-color="#52c41a" /> -->
              <a-icon type="pause-circle" theme="twoTone"  two-tone-color="#52c41a" style="font-size:20px"/>
              <!-- <span style="margin-left: 5px">{{record.name}}</span> -->
            </div>
            <div v-else>
              <!-- <a-icon type="stop" theme="twoTone" two-tone-color="#eb2f96"/> -->
              <a-icon type="play-circle" theme="twoTone"  two-tone-color="#eb2f96" style="font-size:20px"/>
              <!-- <span style="margin-left: 5px">{{record.name}}</span> -->
            </div>
          </template>
          <template slot="tooltip" slot-scope="text">
            <a-tooltip placement="topLeft" :title="text" trigger="hover">
              <div class="tooltip">
                {{ text }}
              </div>
            </a-tooltip>
          </template>
          <template slot="tagInfoList" slot-scope="text">
            <a-popover title="标签">
              <template slot="content">
                <div v-for="item in text" style="margin: 5px 0">
                      <span
                        :style="{'background-color':item.tagColor, color:'white','border-radius':'10px',padding:'2px 10px'}">
                      {{item.tagName}}
                      </span>
                </div>
              </template>
              <a-icon type="environment" />
              <!-- <div style="display:flex,direction:column">
                <p v-for="item in text"
                  :style="{'background-color':item.tag_color, color:'white',padding:'2px 1px',}">
                  {{item.tag_name}}
                  </p>
              </div> -->
            </a-popover>
          </template>
        </a-table>
      </a-card>
    </a-col>
    <cloud-info-modal ref="modalForm" @ok="modalFormOk"></cloud-info-modal>
    <device-tag-modal ref='deviceTagModal' @tagOk='tagOk'></device-tag-modal>
  </a-row>
</template>

<script>
import '@assets/less/TableExpand.less'
import {mixinDevice} from '@/utils/mixin'
import {JeecgListMixin} from '@/mixins/JeecgListMixin'
import cloudInfoModal from '@views/devicesystem/modules/DeviceInfoFormModal.vue'
import {getAction,postAction,deleteAction} from '@api/manage'
import JDictSelectTag from '@comp/dict/JDictSelectTag.vue'
import { YqFormSearchLocation} from '@/mixins/YqFormSearchLocation'
import deviceTagModal from '@views/devicesystem/modules/DeviceTagModal.vue'
var deviceForTag = '';
export default {
  name: 'Prometheus',
  mixins: [JeecgListMixin, mixinDevice, YqFormSearchLocation],
  components: {
    deviceTagModal,
    cloudInfoModal,
    JDictSelectTag,
  },
  props: {
    // 云平台的类型 麒麟云平台：QiLinCloud, ZStack云平台：ZStackCloud
    transfer: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      maxLength:50,
      description: '云平台管理列表页面',
      formItemLayout: {
        labelCol: {style: 'width:80px'},
        wrapperCol: {
          style: 'width:calc(100% - 80px)'
        }
      },
      taginfoSelectData: [],
      queryParam: {
        transfer: 'Prometheus_Server',
        // transfer: this.transfer
      },
      // 表头
      columns: [
        {
          title: '设备名称',
          dataIndex: 'name',
          scopedSlots: { customRender: 'status' },
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
            return { style: cellStyle }
          },
        },
        {
          title: '产品名称',
          dataIndex: 'productName',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 150px;max-width:300px'
            return { style: cellStyle }
          },
        },
        {
          title: '通信协议',
          dataIndex: 'transferProtocol',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 100px;max-width:300px'
            return { style: cellStyle }
          },
        },
        {
          title: '所属网关',
          dataIndex: 'gatewayName',
          customRender: (text, record, index) => {
            if (record.gatewayName == '' || record.gatewayName == null || record.gatewayName == undefined) {
              return "未绑定网关"
            } else {
              return text
            }
          },
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 100px;max-width:300px'
            return { style: cellStyle }
          },
        },
        {
          title: '添加时间',
          dataIndex: 'createTime',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 100px;max-width:300px'
            return { style: cellStyle }
          },
        },
        // {
        //   title: '关联资产',
        //   dataIndex: 'assetsName',
        //   customCell: () => {
        //     let cellStyle = 'text-align: center;min-width: 110px;max-width:300px'
        //     return { style: cellStyle }
        //   },
        // },
        {
          title: '标签',
          dataIndex: 'tagInfoList',
          scopedSlots: {
            customRender: 'tagInfoList'
          },
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 100px;max-width:180px;'
            return {
              style: cellStyle
            }
          },
        },
        {
          title: '设备说明',
          dataIndex: 'description',
          scopedSlots: { customRender: 'tooltip' },
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 120px;max-width:400px'
            return { style: cellStyle }
          },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 140,
          scopedSlots: { customRender: 'action' },
        },
      ],
      url: {
        //detachTags: '/utl/tagresource/detachTags', //解绑标签
        list: '/device/deviceInfo/vcenterList',
        delete: '/device/deviceInfo/delete',
        deleteBatch: '/device/deviceInfo/deleteBatchDevice',
        exportXlsUrl: "/device/deviceInfo/exportXls",//'/topo/device/exportXls',
        importExcelUrl: 'device/deviceInfo/importExcel',
        deviceEnable: '/device/deviceInfo/updateEnable',
        batchEnable: '/device/deviceInfo/batchUpdateEnable',
      },
      productList: [],

    }
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
  },
  created() {
    this.getProductList()
    this.queryAllTags()
  },
  methods: {
    getProductList() {
      getAction("/prometheus/device/getProductListByProtocol", { protocol: "Prometheus_Server" }).then((res) => {
        if (res.success) {
          this.productList = res.result
        }
      })
    },
    getUntie(record) {
      let that = this
      if(record.gatewayName==''||record.gatewayName==null||record.gatewayName==undefined){
        that.$message.warning( "该设备未绑定网关")
        return
      }
      let params = {
        gatewayId: record.gatewayId,
        ids: record.id
      }
      that.$confirm({
        title: '确认操作',
        okText: '是',
        cancelText: '否',
        content: '是否确定解绑选中的设备?',
        onOk: function() {
          postAction(that.url.unbund, params).then(res => {
            if (res.success) {
              that.$message.success(res.message)
              that.loadData()
            } else {
              that.$message.warning(res.message)
            }
          })
        }
      })
    },
    queryAllTags() {
      getAction('/device/deviceInfo/taginfo4Select').then((res) => {
        if (res.success) {
          this.taginfoSelectData = res.result
        }
      })
    },
    tagOk() {
      this.loadData()
    },
    bindaTag(record) {
      this.$refs.deviceTagModal.visible = true
      this.$refs.deviceTagModal.bindaTag(record)
    },
    handleAdd: function () {
      this.$refs.modalForm.add()
      this.$refs.modalForm.title = '添加设备'
      this.$refs.modalForm.disableSubmit = false
    },
    //删除
    deleteRecord(record) {
      if (!this.url.delete) {
        this.$message.error('请设置url.delete属性!')
        return
      }
      var that = this
      this.$confirm({
        title: '确认删除',
        okText: '是',
        cancelText: '否',
        content: '是否删除选中数据?',
        onOk: function () {
          that.loading = true
          deleteAction(that.url.deleteBatch, {
            ids: record.id
          }).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.loadData()
            } else {
              that.$message.warning(res.message)
              that.loadData()
            }
          })
        },
      })
    },
    //设备禁用/启用
    deviceEnable(record) {
      let enable = record.enable
      getAction(this.url.deviceEnable, {
        enable: enable === 1 ? 0 : 1,
        deviceId: record.id
      }).then((res) => {
        if (res.success) {
          if (enable === 1) {
            this.$message.success('禁用成功!')
          } else {
            this.$message.success('启用成功!')
          }
          // this.ipagination.cur
          this.loadData()
        }
      })
    },
    //批量禁用、启用
    batchEnableOperate(enable) {
      if (!this.url.batchEnable) {
        this.$message.error('请设置url.batchConfirm属性!')
        return
      }
      if (this.selectedRowKeys.length <= 0) {
        this.$message.warning('请选择一条记录！')
        return
      }
      var that = this
      this.$confirm({
        title: '确认操作',
        okText: '是',
        cancelText: '否',
        content: '是否确定修改选中数据?',
        onOk: function() {
          that.loading = true
          getAction(that.url.batchEnable, {
            ids: that.selectedRowKeys.join(','),
            enable: enable
          })
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.loadData()
                that.onClearSelected()
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.loading = false
            })
        },
      })
    },
    handleDetailPage: function(record,idx) {
      this.$parent.pButton2(idx, record)
    },
    //表单查询,点击查询按钮，默认查询第一页
    dosearch() {
      this.loadData(1)
    },
    searchReset() {
      //重置form表单，不重置tree选中节点
      this.queryParam = { transfer: 'Prometheus_Server',}
      this.loadData(1)
    },
    // 导出功能
    handleExport() {
      this.handleExportXls('Prometheus','Prometheus_Server')
    }
  },
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';

.stateBox {
  margin-left: 20px;
}
.stateImg {
  vertical-align: middle
}
.alarmStatus {
  margin-left: 8px;
}
.overlay {
  color: #409eff
}
</style>