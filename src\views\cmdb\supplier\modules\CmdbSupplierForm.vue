<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot='detail'>
        <a-row>
          <a-col :span="24">
            <a-form-item label="供应商名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                v-decorator="['name', validatorRules.name]"
                :allowClear="true"
                autocomplete="off"
                placeholder="请输入供应商名称"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="联系人名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                v-decorator="['contactName', validatorRules.contactName]"
                :allowClear="true"
                autocomplete="off"
                placeholder="请输入联系人名称"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="联系人邮箱" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                v-decorator="['contactEmail', validatorRules.contactEmail]"
                :allowClear="true"
                autocomplete="off"
                placeholder="请输入联系人邮箱,如：<EMAIL>"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="联系人电话" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                v-decorator="['contactTel', validatorRules.contactTel]"
                :allowClear="true"
                autocomplete="off"
                placeholder="请输入联系人电话"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item class="two-words" label="网站" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                v-decorator="['website', validatorRules.website]"
                :allowClear="true"
                autocomplete="off"
                placeholder="请输入网站，如：www.baidu.com"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import pick from 'lodash.pick'
import { validateDuplicateValue, checkPhone } from '@/utils/util'
import JFormContainer from '@/components/jeecg/JFormContainer'
import JDictSelectTag from '@/components/dict/JDictSelectTag'
import { phoneValidator } from '@/mixins/phoneValidator'
import fa from 'element-ui/src/locale/lang/fa'

export default {
  name: 'CmdbSupplierForm',
  components: {
    JFormContainer,
    JDictSelectTag,
  },
  mixins: [phoneValidator],
  props: {
    //流程表单data
    formData: {
      type: Object,
      default: () => {},
      required: false,
    },
    //表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false,
    },
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false,
    },
  },
  data() {
    return {
      form: this.$form.createForm(this),
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      confirmLoading: false,
      validatorRules: {
        name: {
          rules: [
            { required: true, message: '请输入供应商名称' },
            { min: 2, max: 30, message: '供应商长度在2-30个字符', trigger: 'blur' },
          ],
        },
        contactName: {
          rules: [
            { required: true, message: '请输入联系人名称' },
            { min: 2, max: 20, message: '联系人长度在2-20个字符', trigger: 'blur' }
          ],
        },
        contactEmail: {
          rules: [
            {
              required: false,
              pattern: /^([a-z0-9_\.-]+)@([\da-z\.-]+)\.([a-z\.]{2,6})$/,
              message: '请输入正确的邮箱地址',
            },
            { min: 0, max: 255, message: '邮箱地址长度不超过255个字符' }
          ],
        },
        contactTel: {
          rules: [
            { required: true, message: '请输入手机号或座机号' },
            { validator: this.phone, trigger: 'blur' },
          ],
        },
        website: {
          rules: [
            {
              required: false,
              pattern:
                /^([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+\.(com|cn|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum))(:[0-9]+)*(\/($|[a-zA-Z0-9.,?'\\+&%$#=~_-]+))*$|^(https?|ftp):\/\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+\.(com|cn|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum))(:[0-9]+)*(\/($|[a-zA-Z0-9.,?'\\+&%$#=~_-]+))*$/,
              message: '请输入正确的网址',
            },
            { min: 0, max: 2550, message: '网址长度不超过2550个字符' }
          ],
        },
      },
      url: {
        add: '/supplier/cmdbSupplier/add',
        edit: '/supplier/cmdbSupplier/edit',
        queryById: '/supplier/cmdbSupplier/queryById',
      },
    }
  },
  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    },
  },
  created() {
    //如果是流程中表单，则需要加载流程表单data
    this.showFlowData()
  },
  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(
          pick(this.model,
            'name',
            'contactName',
            'contactEmail',
            'contactTel',
            'website',
            //'delflag'
          )
        )
      })
    },
    //渲染流程表单数据
    showFlowData() {
      if (this.formBpm === true) {
        let params = { id: this.formData.dataId }
        getAction(this.url.queryById, params).then((res) => {
          if (res.success) {
            this.edit(res.result)
          }
        })
      }
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let formData = Object.assign(this.model, values)
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    popupCallback(row) {
      this.form.setFieldsValue(pick(row,
        'name',
        'contactName',
        'contactEmail',
        'contactTel',
        'website',
        //'delflag'
      ))
    },
  },
}
</script>
<style lang="less" scoped>
::v-deep .two-words > div > label {
  letter-spacing: 4px;
}
::v-deep .two-words > div > label::after {
  letter-spacing: 0px;
}
</style>