<template>
  <div id='alarmDiv' class='alarm-div'>
    <a-descriptions size='small' :column='1' title='设备信息' v-if='showDevcInfo'>
      <a-descriptions-item label='设备名称'> {{ showDevcInfo.name || '' }}</a-descriptions-item>
      <a-descriptions-item v-if="status" label='设备状态'>
        <span class='status-dot' :style='{background:status.color}'></span>
        {{ status.title }}
      </a-descriptions-item>
      <a-descriptions-item v-if='showDevcInfo.ip' label='设备ip'> {{ showDevcInfo.ip || '' }}</a-descriptions-item>
      <a-descriptions-item v-if='showDevcInfo.cpu' label='cpu使用率'>
        {{ showDevcInfo.cpu || '' }}
      </a-descriptions-item>
      <a-descriptions-item v-if='showDevcInfo.mem' label='内存使用率'>
        {{ showDevcInfo.mem || '' }}
      </a-descriptions-item>
      <a-descriptions-item v-if='showDevcInfo.inSpeed' label='下行速率'>
        {{ showDevcInfo.inSpeed || '' }}
      </a-descriptions-item>
      <a-descriptions-item v-if='showDevcInfo.outSpeed' label='上行速率'>
        {{ showDevcInfo.outSpeed || '' }}
      </a-descriptions-item>
      <a-descriptions-item v-if='showDevcInfo.healthlevel' label='健康等级'>
        {{ showDevcInfo.healthlevel || '' }}
      </a-descriptions-item>
      <a-descriptions-item v-if='showDevcInfo.healthDegree !== undefined && showDevcInfo.healthDegree !== null'
                           label='健康度'>
        {{ showDevcInfo.healthDegree }}
      </a-descriptions-item>
      <a-descriptions-item v-if='showDevcInfo.risklevel' label='风险等级'>
        {{ showDevcInfo.risklevel || '' }}
      </a-descriptions-item>
      <a-descriptions-item v-if='showDevcInfo.riskDegree !== undefined && showDevcInfo.riskDegree !== null'
                           label='风险度'>
        {{ showDevcInfo.riskDegree }}
      </a-descriptions-item>
    </a-descriptions>
    <div style='margin-top: 24px' v-if='alarmInfo && showDevcInfo.status==1'>
      <a-descriptions size='small' :column='1' :title="idx===0?'告警信息':''" style='margin-bottom:12px'
                      v-for='(item,idx) in alarmInfo' :key='idx'>
        <a-descriptions-item label='告警名称'> {{ item.alarmName || '' }}</a-descriptions-item>
        <a-descriptions-item label='告警级别'>
          <span v-if='item.alarmLevel && levelMap[item.alarmLevel]'
                style='width: 10px;height: 10px;border-radius: 50%;display: inline-block'
                :style='{background:levelMap[item.alarmLevel].color}'></span>
          {{ item.alarmLevel && levelMap[item.alarmLevel] ? levelMap[item.alarmLevel].title || '' : '' }}
        </a-descriptions-item>
        <a-descriptions-item v-for='(reason, idx) in item.alarmDisplay' :key='idx' :label='reason.alarmRuleText'>
          {{ reason.alarmValueText }}
        </a-descriptions-item>
      </a-descriptions>
    </div>

    <!-- <h4 v-if="showDevcInfo || showDevcInfo.name">{{ showDevcInfo.name}}</h4>
    <div v-if="alarmInfo">
      <div>
        告警名称：
        <span>{{ alarmInfo.templateName || '' }}</span>
      </div>
      <div>
        告警级别：
        <span>{{ alarmInfo.level?(alarmInfo.level === '10' ? '一般告警' : '严重告警'):  '' }}</span>
      </div>
    </div>
    <div v-if="showDevcInfo">
      <div>
        设备ip：
        <span>{{ showDevcInfo.ip || '' }}</span>
      </div>
      <div>
        cpu使用率：
        <span>{{  showDevcInfo.cpu || '' }}</span>
      </div>
      <div>
        内存使用率：
        <span>{{ showDevcInfo.mem || '' }}</span>
      </div>
    </div> -->
  </div>
</template>

<script>
export default {
  props: {
    showDevcInfo: {
      type: Object,
      default: () => null
    },
    alarmInfo: {
      type: Array,
      default: () => null
    },
    alarmLevelList: [],
    onOffColors: {
      type: Object,
      default: () => {
        return { onLine: '#52c41a', offLine: '#8c8c8c' }
      }
    },
    alarmColor:{
      type:String,
      default:"#FFAE38"
    }
  },
  data() {
    return {
      levelMap: {}
    }
  },
  created() {
    // 告警级别
    this.alarmLevelList.forEach((el) => {
      this.levelMap[el.value] = el
    })
  },
  computed:{
    status(){
      if (this.showDevcInfo.status === 0) {
        return {
          title:'故障',
          color:this.onOffColors.offLine
        }
      } else if (this.showDevcInfo.status === 1) {
        if(this.alarmInfo){
          return {
            title:'不稳定',
            color:this.alarmColor
          }
        }else{
          return {
              title:'正常',
            color:this.onOffColors.onLine
          }
        }
      }
      return ""
    }
  },
  methods: {}
}
</script>

<style lang='less' scoped>
//.alarm-div::-webkit-scrollbar {
//  display: none;
//}
.alarm-div {
  max-height: 300px;
  max-width: 500px;
  overflow-y: auto;

  h4 {
    height: 30px;
    line-height: 30px;
    // padding-left: 15px;
    margin-bottom: 0px;
    color: #1f366e;
  }

  p {
    height: 30px;
    line-height: 30px;
    margin-bottom: 0px;
    color: #565656;
  }

  .status-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: inline-block
  }
}
</style>