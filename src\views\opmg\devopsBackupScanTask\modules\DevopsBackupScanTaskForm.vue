<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="disabled">
      <a-form-model ref='form' :model='model' :rules='validatorRules' slot='detail' v-bind='formItemLayout'>
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="任务名称" prop='taskName'>
              <a-input v-model="model.taskName" :allowClear="true" autocomplete="off" placeholder="请输入任务名称"/>
            </a-form-model-item>
          </a-col>

          <a-col :span="24">
            <a-form-model-item label="操作类型" prop='operateType'>
              <j-dict-select-tag type="radio" v-model="model.operateType" dictCode="scanTaskOperationType" placeholder="请选择操作类型" />
            </a-form-model-item>
          </a-col>
          <!--          <a-col :span="24">
            <a-form-model-item label="备份类型" prop='conType'>
              <j-dict-select-tag type="radio"
                v-model="model.conType" dictCode="resource_type" placeholder="请选择备份类型" />
            </a-form-model-item>
          </a-col>-->
          <a-col :span="24">
            <a-form-model-item label="触发任务码" prop='tarkCron'>
              <j-cron v-model="model.tarkCron" @change="setCorn">
              </j-cron>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="待扫描设备" prop='resourceId'>
              <a-select
                v-model='model.resourceId'
                :allow-clear='true'
                placeholder="请选择待扫描设备">
                <a-select-option v-for='item in datasourceList' :disabled="model.datadestId&&item.id===model.datadestId" :key='"datasourceId_" + item.id' :label='item.resourceName'
                                 :value='item.id'>
                  {{ item.resourceName }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="扫描类型" prop='scanType'>
              <j-dict-select-tag type="radio" v-model='model.scanType' dictCode="scan_type" placeholder="请选择扫描类型" @input="scanTypeChang" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24" v-if="model.scanType == 1">
            <a-form-model-item label="备份策略" prop='backupId'>
              <a-select
                v-model='model.backupId'
                :allow-clear='true'
                placeholder="请选择备份策略">
                <a-select-option v-for='item in proIdList' :key='"proId_" + item.id' :label='item.proName'
                                 :value='item.id'>
                  {{ item.proName }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="24" v-if="model.scanType == 2">
            <a-form-model-item label="备份任务" prop='backupId'>
              <a-select
                v-model='model.backupId'
                :allow-clear='true'
                placeholder="请选择备份任务">
                <a-select-option v-for='item in taskList' :key='"task_" + item.id' :label='item.taskName'
                                 :value='item.id'>
                  {{ item.taskName }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="待扫描文件路径" prop='fileAdd'>
              <a-input v-model="model.fileAdd" :allowClear="true" autocomplete="off" placeholder="请输入待扫描文件路径"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="是否推送消息" prop='whPushMes'>
              <j-switch v-model="model.whPushMes" @change="switchChange"></j-switch>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
<!--            <a-form-model-item label="邮箱地址" prop='emailAddress' :rules="[{required: !disabledFlag,min: 1, max: 200,validator:validateEmailAddress,trigger:'blur'}]">
              <a-input v-model="model.emailAddress" placeholder="请输入邮箱地址" :disabled="disabledFlag"/>
            </a-form-model-item>-->

            <a-form-model-item label="通知模板" prop='pushAddress' :rules="[{required: !disabledFlag,validator:validatePushAddress,trigger:'blur'}]">
              <a-select
                :disabled="disabledFlag"
                v-model="model.pushAddress"
                :allow-clear="true"
                :show-search="true"
                option-filter-prop="label"
                placeholder="请选择通知模板">
                <a-select-option v-for="item in noticeTemplateList" :key="'noticeTemplate_'+item.type" :label="item.code"
                                 :value="item.type">
                  {{ item.code }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="状态" prop='execstate'>
              <j-dict-select-tag
                type="radio"
                v-model='model.execstate'
                dictCode="quartz_status" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>
  import {httpAction,getAction} from '@/api/manage'
  import {duplicateCheck} from '@/api/api'

  export default {
    name: 'DevopsBackupScanTaskForm',
    components: {},
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false,
      },
    },
    data() {
      return {
        confirmLoading: false,
        formItemLayout: {
          labelCol: {
            xs: { span: 24 },
            sm: { span: 24 },
            md: { span: 5 }
          },
          wrapperCol: {
            xs: { span: 24 },
            sm: { span: 24 },
            md: { span: 16 }
          }
        },
        model: {},
        disabledFlag:false,
        datasourceList:[],
        proIdList:[],
        taskList:[],
        noticeTemplateList: [], //通知模板下拉数据
        validatorRules: {
          taskName:  [
            {required: true,message:"请输入任务名称"},
            {min:2,max:30,message:"任务名称长度在2到30个字符",trigger: 'blur' },
            {validator: this.validateTaskName,trigger: 'blur' },
          ],
         /* conType: [
            {required: true, message: '请选择备份类型'}
          ],*/
          operateType:[
            {required: true, message: '请选择操作类型'}
          ],
          tarkCron: [
            {required: true, message: '请输入触发任务码'}
          ],
          resourceId: [
            {required: true, message: '请选择待扫描设备'}
          ],
          scanType:  [
            {required: true,message: '请选择扫描类型'}
          ],
          backupId: [
            {required: true,message: '请选择备份策略/备份任务'}
          ],
          fileAdd:  [
            {required: true,message: '请输入文件存储路径'}
          ],
          whPushMes:[
            {required: true,message: '请选择是否推送消息'}
          ],
          execstate:[
            {required: true,message: '请选择状态'}
          ]
        },
        url: {
          add: '/devopsBackupScanTask/devopsBackupScanTask/add',
          edit: '/devopsBackupScanTask/devopsBackupScanTask/edit',
          queryProIds:'/devopsbackuppro/devopsBackupPro/list',
          queryDatasourceList:'/devopsbackupresource/devopsBackupResource/list',
          queryTaskList:'/devopsBackupTask/devopsBackupTask/list',
          noticeTemplateList: '/sys/notice/template/templateList', // 通知模板下拉数据,
        }
      }
    },
    created() {
      this.confirmLoading=true
      Promise.all([ this.getNoticeTemplateList(),this.getDatasourceList(),this.getTasks(),this.getProIds()]).then((res)=>{
        this.confirmLoading=false
      })
    },
    methods: {
      /*获取通知模板下拉数据*/
      getNoticeTemplateList() {
        return new Promise((resolve, reject) => {
          this.noticeTemplateList = []
          let route = this.$route.fullPath
          getAction(this.url.noticeTemplateList, {
            business: route
          }).then((res) => {
            if (res.success) {
              this.noticeTemplateList = res.result
            } else {
              this.$message.warning(res.message)
            }
            resolve(true)
          }).catch((err) => {
              this.$message.error(err.message)
              resolve(true)
            })
        })
      },
      /*获取备份设备下拉数据*/
      getDatasourceList(){
        return new Promise((resolve, reject) => {
          this.datasourceList = []
          getAction(this.url.queryDatasourceList, { pageSize: -1 }).then((res) => {
            if (res.success && res.result && res.result.records) {
              this.datasourceList = res.result.records
            } else {
              this.datasourceList = []
              if (!res.success) {
                this.$message.warning(res.message)
              }
            }
            resolve(true)
          }).catch((err) => {
            this.$message.error(err.message)
            resolve(true)
          })
        })
      },
      /*获取备份策略下拉数据*/
      getProIds(){
        return new Promise((resolve, reject) => {
          this.proIdList = []
          getAction(this.url.queryProIds, { pageSize: -1 }).then((res) => {
            if (res.success && res.result && res.result.records) {
              this.proIdList = res.result.records
            } else {
              this.proIdList = []
              if (!res.success) {
                this.$message.warning(res.message)
              }
            }
            resolve(true)
          }).catch((err) => {
            this.$message.error(err.message)
            resolve(true)
          })
        })
      },
      /*获取备份任务下拉数据*/
      getTasks(){
        return new Promise((resolve, reject) => {
          this.taskList = []
          getAction(this.url.queryTaskList, { pageSize: -1 }).then((res) => {
            if (res.success && res.result && res.result.records) {
              this.taskList = res.result.records
            } else {
              this.taskList = []
              if (!res.success) {
                this.$message.warning(res.message)
              }
            }
            resolve(true)
          }).catch((err) => {
            this.$message.error(err.message)
            resolve(true)
          })
        })
      },
      add() {
        this.edit({
          // conType:1,
          operateType:0,
          tarkCron:"0 0 0 * * ? *",
          execstate:0,
          scanType:1,
          whPushMes:'N',
          backupId:undefined,
          // emailAddress:'',
          pushAddress:undefined
        })
      },
      setCorn(data) {
        if (data && data.target != null) {
          let dataList = data.target.value.split(' ')
          if (dataList[0] == '*') {
            this.$message.warning('请确认是否每秒都执行')
          }
        } else {
          let dataList = data.split(' ')
          if (dataList[0] == '*') {
            this.$message.warning('请确认是否每秒都执行')
          }
        }
      },
      edit(record) {
        this.model = Object.assign({}, record)
        this.disabledFlag=this.model.whPushMes==='N'?true:false
        if (this.model.pushAddress===''){
          this.model.pushAddress=undefined
        }
        this.$refs.form.resetFields()
      },
      validateTaskName(rule, value, callback) {
        var params = {
          tableName: 'devops_backup_scan_task',
          fieldName: 'task_name',
          fieldVal: value,
          dataId: this.model.id,
        }
        duplicateCheck(params).then((res) => {
          if (res.success) {
            callback()
          } else {
            callback('任务名称已存在!')
          }
        })
      },
      validateEmailAddress(rule, value, callback){
        if (rule.required) {
          if (!value){
            callback('请输入邮箱地址')
          }else if(!new RegExp(/^([a-z0-9_\.-]+)@([\da-z\.-]+)\.([a-z\.]{2,6})$/).test(value)){
            callback("请输入正确的邮箱地址")
          }else if(value.length<rule.min||value.length>rule.max){
            callback('邮箱地址长度应在'+rule.min+"到"+rule.max+"之间")
          }else {
            callback()
          }
        }else {
          callback()
        }
      },

      validatePushAddress(rule, value, callback){
        if (rule.required) {
          if (!value){
            callback('请选择通知模板')
          }else {
            callback()
          }
        }else {
          callback()
        }
      },
      scanTypeChang(value) {
        this.model.backupId=undefined
      },
      submitForm() {
        const that = this
        // 触发表单验证
        this.$refs.form.validate((success, values) => {
          if (success&&!that.confirmLoading) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!that.model.id) {
              httpurl += that.url.add
              method = 'post'
            } else {
              httpurl += that.url.edit
              method = 'put'
            }
            let formData =JSON.parse(JSON.stringify(Object.assign(that.model, values)))
            if (!that.model.pushAddress){
              formData.pushAddress=''
            }
            httpAction(httpurl, formData, method)
              .then((res) => {
                if (res.success) {
                  that.$message.success(res.message)
                  that.$emit('ok')
                } else {
                  that.$message.warning(res.message)
                }
                that.confirmLoading = false
              }).catch(()=>{
              that.$message.error(res.message)
            })
          }
        })
      },
      switchChange(checked) {
       /* if (Object.keys(this.model).includes('emailAddress')){
          this.$refs.form.clearValidate('emailAddress')
        }
        this.model.emailAddress=''*/

        if (Object.keys(this.model).includes('pushAddress')){
          this.$refs.form.clearValidate('pushAddress')
        }
        this.model.pushAddress=undefined
        if (checked == 'N') {
          this.disabledFlag = true
        } else {
          this.disabledFlag = false
        }
      }
    }
  }
</script>