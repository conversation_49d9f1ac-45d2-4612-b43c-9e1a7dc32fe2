<template>
  <j-modal
    ref='modal'
    :title="title"
    :width="width"
    :visible="visible"
    :centered="true"
    switchFullscreen
    :maskClosable="false"
    @ok="handleOk"
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }"
    @cancel="handleCancel"
    cancelText="关闭">
    <div style='overflow-y: auto;padding:0 8px' :style='{height:isFullScreen?"calc(100vh - 150px)":"70vh"}'>
      <SoftwareInfoForm ref="realForm" @ok="submitCallback" :types="types" :disabled="disableSubmit"> </SoftwareInfoForm>
    </div>
  </j-modal>
</template>

<script>
import SoftwareInfoForm from './SoftwareInfoForm'
export default {
  name: 'SoftwareModal',
  components: {
    SoftwareInfoForm,
  },
  props: {
    //类型
    types: {
      type:Array,
      default: () => [],
      required: true
    }
  },
  data() {
    return {
      title: '',
      width: "60%",
      visible: false,
      disableSubmit: false,
    }
  },
  computed: {
    isFullScreen() {
      return this.$refs.modal?this.$refs.modal.innerFullscreen:false;
    }
  },
  methods: {
    add() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.add()
      })
    },
    edit(record) {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.edit(record)
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      this.$refs.realForm.submitForm()
    },
    submitCallback() {
      this.$emit('ok')
      this.visible = false
    },
    handleCancel() {
      this.close()
    },
  },
}
</script>
<style scoped lang='less'>
@import '~@assets/less/normalModal.less';
</style>