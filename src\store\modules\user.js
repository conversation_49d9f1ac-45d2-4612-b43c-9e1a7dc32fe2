import Vue from 'vue'
import {
  login,
  autoLogin,
  logout,
  UkeyLogin,
  phoneLogin,
  thirdLogin,
  UkeyLoginBT,
  oneClickHelpLogin,
  oneClickHelpAutoLogin,
  SsoAccessToken, SsoLogout
} from '@/api/login'
import { ACCESS_TOKEN, USER_NAME, USER_INFO, USER_AUTH, USER_DEPART, SYS_BUTTON_AUTH, UI_CACHE_DB_DICT_DATA, TENANT_ID, CACHE_INCLUDED_ROUTES, PLATFORM_TYPE, ROLE_CODES, ONE_PLATFORM_FLAG } from "@/store/mutation-types"
import { welcome, getMultipleURL } from "@/utils/util"
import { queryPermissionsByUser, queryPermissionsByRouterAndComponent } from '@/api/api'
import { getAction } from '@/api/manage'
import router from '@/router'
const user = {
  state: {
    token: '',
    username: '',
    realname: '',
    tenantid: '',
    welcome: '',
    avatar: '',
    permissionList: [],
    sysTypeMenu: [],
    info: {},
    ca: false,
    roleCodes: '',
    departs: [],
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token
    },
    SET_NAME: (state, { username, realname, welcome }) => {
      state.username = username
      state.realname = realname
      state.welcome = welcome
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar
    },
    SET_CA: (state, b) => {
      state.ca = b;
    },
    SET_PERMISSIONLIST: (state, permissionList) => {
      state.permissionList = permissionList
    },
    SET_SYS_TYPE_MENU: (state, plist) => {
      state.sysTypeMenu = plist
    },
    SET_INFO: (state, info) => {
      state.info = info
    },
    SET_DEPART: (state, departs) => {
      state.departs = departs
    },
    SET_TENANT: (state, id) => {
      state.tenantid = id
    },
    ROLE_CODES: (state, roleCodes) => {
      state.roleCodes = roleCodes
    },
  },

  actions: {
    // CAS验证登录
    // ValidateLogin({ commit }, userInfo) {
    //   return new Promise((resolve, reject) => {
    //     getAction("/sys/cas/client/validateLogin",userInfo).then(response => {
    //       if(response.success){
    //         const result = response.result
    //         const userInfo = result.userInfo
    //         Vue.ls.set(ACCESS_TOKEN, result.token)
    //         Vue.ls.set(USER_NAME, userInfo.username)
    //         Vue.ls.set(USER_INFO, userInfo)
    //         commit('SET_TOKEN', result.token)
    //         commit('SET_INFO', userInfo)
    //         commit('SET_NAME', { username: userInfo.username,realname: userInfo.realname, welcome: welcome() })
    //         commit('SET_AVATAR', userInfo.avatar)
    //         resolve(response)
    //       }else{
    //         resolve(response)
    //       }
    //     }).catch(error => {
    //       reject(error)
    //     })
    //   })
    // },
    ValidateLogin({ commit }, userInfo) {
      return new Promise((resolve, reject) => {
        getAction("/sys/cas/client/validateLogin", userInfo).then(response => {
          if (response.success) {
            Vue.ls.set("LOGIN_TYPE", "account")
            const result = response.result
            const userInfo = result.userInfo
            Vue.ls.set(ACCESS_TOKEN, result.token)
            Vue.ls.set(USER_NAME, userInfo.username)
            Vue.ls.set(USER_INFO, userInfo)
            Vue.ls.set(ROLE_CODES, result.userInfo.roleCodes)
            Vue.ls.set(USER_DEPART, result.departs)
            commit('SET_TOKEN', result.token)
            commit('SET_INFO', userInfo)
            commit('SET_DEPART', result.departs)
            commit('SET_NAME', { username: userInfo.username, realname: userInfo.realname, welcome: welcome() })
            commit('SET_AVATAR', userInfo.avatar)

            resolve(response)
          } else {
            resolve(response)
          }
        }).catch(error => {
          reject(error)
        })
      })
    },
    // 登录
    Login({ commit,dispatch}, userInfo) {
      return new Promise((resolve, reject) => {
        login(userInfo).then(response => {
          if (response.code == '200') {
            const result = response.result
            const userInfo = Object.assign(result.userInfo, { terminalInfo: result.terminalInfo })
            Vue.ls.set(ACCESS_TOKEN, result.token)
            Vue.ls.set(USER_NAME, userInfo.username)
            Vue.ls.set(USER_INFO, userInfo)
            Vue.ls.set(ROLE_CODES, result.userInfo.roleCodes)
            Vue.ls.set(UI_CACHE_DB_DICT_DATA, result.sysAllDictItems)
            Vue.ls.set(USER_DEPART, result.departs)
            Vue.ls.set("LOGIN_TYPE", "account")
            commit('SET_TOKEN', result.token)
            commit('SET_INFO', userInfo)
            commit('SET_DEPART', result.departs)
            commit('SET_NAME', { username: userInfo.username, realname: userInfo.realname, welcome: welcome() })
            commit('SET_AVATAR', userInfo.avatar)
            resolve(response)
          } else {
            reject(response)
          }
        }).catch(error => {
          reject(error)
        })
      })
    },
    // 运维助手登录
    OneClickHelpLogin({ commit }, userInfo) {
      return new Promise((resolve, reject) => {
        oneClickHelpLogin(userInfo).then(response => {
          if (response.code == '200') {
            Vue.ls.set("LOGIN_TYPE", "account")
            const result = response.result
            const userInfo = Object.assign(result.userInfo, { terminalInfo: result.terminalInfo })
            Vue.ls.set(ACCESS_TOKEN, result.token)
            Vue.ls.set(USER_NAME, userInfo.username)
            Vue.ls.set(USER_INFO, userInfo)
            Vue.ls.set(ROLE_CODES, result.userInfo.roleCodes)
            Vue.ls.set(UI_CACHE_DB_DICT_DATA, result.sysAllDictItems)
            Vue.ls.set(USER_DEPART, result.departs)
            commit('SET_TOKEN', result.token)
            commit('SET_INFO', userInfo)
            commit('SET_DEPART', result.departs)
            commit('SET_NAME', { username: userInfo.username, realname: userInfo.realname, welcome: welcome() })
            commit('SET_AVATAR', userInfo.avatar)
            resolve(response)
          } else {
            reject(response)
          }
        }).catch(error => {
          reject(error)
        })
      })
    },
    // 运维助手自动登录
    OneClickHelpAutoLogin({ commit }, userInfo) {
      return new Promise((resolve, reject) => {
        oneClickHelpAutoLogin(userInfo).then(response => {
          if (response.code == '200') {
            Vue.ls.set("LOGIN_TYPE", "account")
            const result = response.result
            const userInfo = Object.assign(result.userInfo, { terminalInfo: result.terminalInfo })
            Vue.ls.set(ACCESS_TOKEN, result.token)
            Vue.ls.set(USER_NAME, userInfo.username)
            Vue.ls.set(USER_INFO, userInfo)
            Vue.ls.set(ROLE_CODES, result.userInfo.roleCodes)
            Vue.ls.set(UI_CACHE_DB_DICT_DATA, result.sysAllDictItems)
            Vue.ls.set(USER_DEPART, result.departs)
            commit('SET_TOKEN', result.token)
            commit('SET_INFO', userInfo)
            commit('SET_DEPART', result.departs)
            commit('SET_NAME', { username: userInfo.username, realname: userInfo.realname, welcome: welcome() })
            commit('SET_AVATAR', userInfo.avatar)
            resolve(response)
          } else {
            reject(response)
          }
        }).catch(error => {
          reject(error)
        })
      })
    },
    //从其他平台跨入本平台：登录
    autoLogin({ commit }, userInfo) {
      return new Promise((resolve, reject) => {
        autoLogin(userInfo).then(response => {
          if (response.code == '200') {
            Vue.ls.set("LOGIN_TYPE", "account")
            const result = response.result
            const userInfo = result.userInfo
            Vue.ls.set(ACCESS_TOKEN, result.token)
            Vue.ls.set(USER_NAME, userInfo.username)
            Vue.ls.set(USER_INFO, userInfo)
            Vue.ls.set(ROLE_CODES, result.userInfo.roleCodes)
            Vue.ls.set(UI_CACHE_DB_DICT_DATA, result.sysAllDictItems)
            sessionStorage.setItem(PLATFORM_TYPE, result.platformType)
            Vue.ls.set(USER_DEPART, result.departs)
            commit('SET_TOKEN', result.token)
            commit('SET_INFO', userInfo)
            commit('SET_DEPART', result.departs)
            commit('SET_NAME', { username: userInfo.username, realname: userInfo.realname, welcome: welcome() })
            commit('SET_AVATAR', userInfo.avatar)
            resolve(response)
          } else {
            reject(response)
          }
        }).catch(error => {
          reject(error)
        })
      })
    },
    // Ukey登录
    UkeyLogin({ commit }, userInfo) {
      return new Promise((resolve, reject) => {
        UkeyLogin(userInfo).then(response => {
          if (response.code == '200') {
            Vue.ls.set("LOGIN_TYPE", "ukey")
            const result = response.result
            const userInfo = result.userInfo
            Vue.ls.set(ACCESS_TOKEN, result.token)
            Vue.ls.set(USER_NAME, userInfo.username)
            Vue.ls.set(USER_INFO, userInfo)
            Vue.ls.set(ROLE_CODES, result.userInfo.roleCodes)
            Vue.ls.set(UI_CACHE_DB_DICT_DATA, result.sysAllDictItems)
            commit('SET_TOKEN', result.token)
            commit('SET_INFO', userInfo)
            commit('SET_NAME', { username: userInfo.username, realname: userInfo.realname, welcome: welcome() })
            commit('SET_AVATAR', userInfo.avatar)
            resolve(response)
          } else {
            reject(response)
          }
        }).catch(error => {
          reject(error)
        })
      })
    },
    // 新疆Ukey登录
    UkeyLoginBT({ commit }, userInfo) {
      return new Promise((resolve, reject) => {
        let url = getMultipleURL("$UKEY")
        UkeyLoginBT(url, userInfo).then(response => {
          if (response.code == '200') {
            Vue.ls.set("LOGIN_TYPE", "ukey")
            const result = response.result
            const userInfo = result.userInfo
            Vue.ls.set(ACCESS_TOKEN, result.token)
            Vue.ls.set(USER_NAME, userInfo.username)
            Vue.ls.set(USER_INFO, userInfo)
            Vue.ls.set(ROLE_CODES, result.userInfo.roleCodes)
            Vue.ls.set(UI_CACHE_DB_DICT_DATA, result.sysAllDictItems)
            Vue.ls.set(USER_DEPART, result.departs)
            commit('SET_TOKEN', result.token)
            commit('SET_INFO', userInfo)
            commit('SET_DEPART', result.departs)
            commit('SET_NAME', { username: userInfo.username, realname: userInfo.realname, welcome: welcome() })
            commit('SET_AVATAR', userInfo.avatar)
            resolve(response)
          } else {
            reject(response)
          }
        }).catch(error => {
          reject(error)
        })
      })
    },
    //sso access_token 登录
    SsoAccessTokenLogin({ commit }, params) {
      return new Promise((resolve, reject) => {
        SsoAccessToken(params).then(response => {
          if (response.code == '200') {
            Vue.ls.set("LOGIN_TYPE", "sso_access_token")
            const result = response.result
            const userInfo = result.userInfo
            Vue.ls.set(ACCESS_TOKEN, result.token)
            Vue.ls.set(USER_NAME, userInfo.username)
            Vue.ls.set(USER_INFO, userInfo)
            Vue.ls.set(ROLE_CODES, result.userInfo.roleCodes)
            Vue.ls.set(UI_CACHE_DB_DICT_DATA, result.sysAllDictItems)
            Vue.ls.set(USER_DEPART, result.departs)
            commit('SET_TOKEN', result.token)
            commit('SET_INFO', userInfo)
            commit('SET_DEPART', result.departs)
            commit('SET_NAME', { username: userInfo.username, realname: userInfo.realname, welcome: welcome() })
            commit('SET_AVATAR', userInfo.avatar)
            resolve(response)
          } else {
            reject(response)
          }
        }).catch(error => {
          reject(error)
        })
      })
    },
    //手机号登录
    PhoneLogin({ commit }, userInfo) {
      return new Promise((resolve, reject) => {
        phoneLogin(userInfo).then(response => {
          if (response.code == '200') {
            Vue.ls.set("LOGIN_TYPE", "account")
            const result = response.result
            const userInfo = result.userInfo
            Vue.ls.set(ACCESS_TOKEN, result.token)
            Vue.ls.set(USER_NAME, userInfo.username)
            Vue.ls.set(USER_INFO, userInfo)
            Vue.ls.set(UI_CACHE_DB_DICT_DATA, result.sysAllDictItems)
            commit('SET_TOKEN', result.token)
            commit('SET_INFO', userInfo)
            commit('SET_NAME', { username: userInfo.username, realname: userInfo.realname, welcome: welcome() })
            commit('SET_AVATAR', userInfo.avatar)
            resolve(response)
          } else {
            reject(response)
          }
        }).catch(error => {
          reject(error)
        })
      })
    },
    /*通过路由和组件名，获取用户权限菜单及平台信息*/
    GetMenuPermissions({ commit }, params) {
      return new Promise(function (resolve, reject) {
        queryPermissionsByRouterAndComponent(params).then((res) => {
          if (res.success) {
            if (res.result == null) {
              reject({
                success: false,
                message: '当前用户没有目标菜单权限，请联系管理员分配权限'
              })
            } else {
              const menuData = res.result.menu
              const authData = res.result.auth
              const allAuthData = res.result.allAuth
              sessionStorage.setItem(USER_AUTH, JSON.stringify(authData))
              sessionStorage.setItem(SYS_BUTTON_AUTH, JSON.stringify(allAuthData))
              if (menuData && menuData.length > 0) {
                //update--begin--autor:qinfeng-----date:20200109------for：JEECG-63 一级菜单的子菜单全部是隐藏路由，则一级菜单不显示------
                menuData.forEach((item, index) => {
                  if (item['children']) {
                    let hasChildrenMenu = item['children'].filter((i) => {
                      return !i.hidden || i.hidden == false
                    })
                    if (hasChildrenMenu == null || hasChildrenMenu.length == 0) {
                      item['hidden'] = true
                    }
                  }
                })
                //update--end--autor:qinfeng-----date:20200109------for：JEECG-63 一级菜单的子菜单全部是隐藏路由，则一级菜单不显示------
                commit('SET_PERMISSIONLIST', menuData)
                resolve({
                  success: true,
                  message: res.message,
                  menu: res.result.menu,
                  platformType: res.result.platformType
                })
              }
              else {
                reject({
                  success: false,
                  message: '当前用户没有目标菜单权限，请联系管理员分配权限'
                })
              }
            }
          }
          else {
            reject({
              success: false,
              message: res.message
            })
          }
        }).catch(error => {
          reject({
            success: false,
            message: error
          })
        })
      })
    },

    /* 通过平台编号，获取用户权限菜单信息*/
    GetPermissionList({ commit, dispatch }, param) {
      return new Promise((resolve, reject) => {
        let params = { platformType: param };
        queryPermissionsByUser(params).then(response => {
          if (response.result == null) {
          } else {
            const menuData = response.result.menu;
            const authData = response.result.auth;
            const allAuthData = response.result.allAuth;
            //Vue.ls.set(USER_AUTH,authData);
            sessionStorage.setItem(USER_AUTH, JSON.stringify(authData));
            sessionStorage.setItem(SYS_BUTTON_AUTH, JSON.stringify(allAuthData));

            if (menuData && menuData.length > 0) {
              //update--begin--autor:qinfeng-----date:20200109------for：JEECG-63 一级菜单的子菜单全部是隐藏路由，则一级菜单不显示------
              menuData.forEach((item, index) => {
                if (item["children"]) {
                  let hasChildrenMenu = item["children"].filter((i) => {
                    return !i.hidden || i.hidden == false
                  })
                  if (hasChildrenMenu == null || hasChildrenMenu.length == 0) {
                    item["hidden"] = true
                  }
                }
              })
              //update--end--autor:qinfeng-----date:20200109------for：JEECG-63 一级菜单的子菜单全部是隐藏路由，则一级菜单不显示------
              commit('SET_PERMISSIONLIST', menuData)
            } else {
              Vue.prototype.$warning({
                title: '提示',
                content: '没有菜单权限，请联系管理员！',
                onOk() {
                  dispatch('Logout').then(() => {
                    router.push({ path: '/user/login' })
                    window.location.reload()
                  })
                }
              });
              // sessionStorage.setItem(PLATFORM_TYPE, 1)
              // resolve("1")
            }
            resolve(response)
          }
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 登出
    Logout({ commit, state }) {
      return new Promise((resolve) => {
        let logoutToken = state.token;
        commit('SET_TOKEN', '')
        commit('SET_PERMISSIONLIST', [])
        Vue.ls.remove(ACCESS_TOKEN)
        Vue.ls.remove(UI_CACHE_DB_DICT_DATA)
        Vue.ls.remove(CACHE_INCLUDED_ROUTES)
        Vue.ls.remove("USER-INFO-XML")
        Vue.ls.remove(USER_DEPART)
        Vue.ls.remove("LOGIN_TYPE")
        // Vue.ls.remove(PLATFORM_TYPE)
        sessionStorage.removeItem(PLATFORM_TYPE)
        sessionStorage.removeItem("sso_access_token")
        Vue.ls.remove(ONE_PLATFORM_FLAG)
        logout(logoutToken).then((res) => {
          if (window._CONFIG['casSSo'] == 'true') {
            let sevice = 'http://' + window.location.host + '/'
            let serviceUrl = encodeURIComponent(sevice)
            // window.location.href = process.env.VUE_APP_CAS_BASE_URL + '/logout?service=' + serviceUrl
            window.location.href = window._CONFIG['casPrefixUrl'] + '/logout?service=' + serviceUrl
          }
          resolve(res)
        }).catch(() => {
          resolve()
        })
      })
    },

    // Logout({ commit, state }) {
    //   return new Promise((resolve) => {
    //     let logoutToken = state.token;
    //     commit('SET_TOKEN', '')
    //     commit('SET_PERMISSIONLIST', [])
    //     Vue.ls.remove(ACCESS_TOKEN)
    //     logout(logoutToken).then(() => {
    //       var sevice = "http://"+window.location.host+"/";
    //       var serviceUrl = encodeURIComponent(sevice);
    //       window.location.href = window._CONFIG['casPrefixUrl']+"/logout?service="+serviceUrl;
    //       //resolve()
    //     }).catch(() => {
    //       resolve()
    //     })
    //   })
    // },
    // 第三方登录
    ThirdLogin({ commit }, param) {
      return new Promise((resolve, reject) => {
        thirdLogin(param.token, param.thirdType).then(response => {
          if (response.code == '200') {
            const result = response.result
            const userInfo = result.userInfo
            Vue.ls.set(ACCESS_TOKEN, result.token)
            Vue.ls.set(USER_NAME, userInfo.username)
            Vue.ls.set(USER_INFO, userInfo)
            commit('SET_TOKEN', result.token)
            commit('SET_INFO', userInfo)
            commit('SET_NAME', { username: userInfo.username, realname: userInfo.realname, welcome: welcome() })
            commit('SET_AVATAR', userInfo.avatar)
            resolve(response)
          } else {
            reject(response)
          }
        }).catch(error => {
          reject(error)
        })
      })
    },
    saveTenant({ commit }, id) {
      Vue.ls.set(TENANT_ID, id)
      commit('SET_TENANT', id)
    },
    saveSysTypeMenu({ commit }, param) {
      commit('SET_SYS_TYPE_MENU', param)
    }

  }
}

export default user