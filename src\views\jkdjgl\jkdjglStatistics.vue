<template>
  <div class="jkdjgl-statistics" id='jkdjglStatistics'>
    <a-row :gutter="[gutter, vgutter]" type='flex'>
      <a-col
        v-for="(item, index) in echartsCom"
        :key="item.id"
        :xs="24"
        :xl="12"
        :xxl="8"
      >
        <a-card :title='item.name'>
          <div class='chart-box' style="width: 100%; height: 293px">
            <component
              :ref='"jkdj_"+item.type+"_"+item.id'
              v-if='isReady' :is='item.type'
              :id='"jkdj_"+item.type+"_"+item.id'
              :chartData='item.chartData'
              :option='item.option || {}'
              :colors='item.colors ||[]'
            >

            </component>
          </div>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>
<script>
import EchartsDoughnut from '@comp/chart/echarts/EchartsDoughnut.vue'
import EchartsPie from '@comp/chart/echarts/EchartsPie.vue'
import EchartsLine  from '@comp/chart/echarts/EchartsLine.vue'
import { getAction } from '@api/manage'
export default {
  name: 'jkdjglStatistics',
  components: {
    Doughnut:EchartsDoughnut,
    Pie:EchartsPie,
    EchartsLine:EchartsLine,
  },
  data() {
    return {
      gutter: 16,
      vgutter: 16,
      colCount: 8,
      echartsCom:[
        {
          id: "device_collect_waves",
          name:"设备采集波次统计图",
          type:"Doughnut",
          url:"",
          chartData: [],
          colors:['#5ab1ef', '#b6a2de', '#67e0e3', '#2ec7c9','#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc'],
        },
        // {
        //   id: "device_collect_count",
        //   name:"设备采集数统计图",
        //   type:"Pie",
        //   url:"",
        //   chartData: [],
        //   colors:['#5ab1ef', '#b6a2de', '#67e0e3', '#2ec7c9','#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc'],
        // },
        {
          id: "device_collect_trend",
          name:"设备采集波次近7天趋势图",
          type:"EchartsLine",
          url:"",
          chartData: [],
          option: {
            xAxis:[{data: []}],
            grid: { left: '5%', right: '35', top: '30', bottom: 0, containLabel: true, }
          },
          colors:['#5ab1ef', '#b6a2de', '#67e0e3', '#2ec7c9','#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc'],
        },
        {
          id:"device_collect_fail_trend",
          name:"设备失败波次近7天趋势图",
          type:"EchartsLine",
          url:"",
          chartData: [],
          option: {
            xAxis:[{data: []}],
            grid: { left: '5%', right: '35', top: '30', bottom: 0, containLabel: true, }
          },
          colors:['#5ab1ef', '#b6a2de', '#67e0e3', '#2ec7c9','#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc'],
        },
        {
          id: "alarm_collect_waves",
          name:"告警推送波次统计图",
          type:"Doughnut",
          url:"",
          chartData: [],
          colors:["#00a8e1", "#99cc00" ,"#e30039", "#fcd300", "#800080" ,"#00994e", "#ff6600" ,"#808000" ,"#f0da49" ,"#f7af59" ,"#f06464" ,"#9489fa"].reverse(),
        },
        // {
        //   id: "alarm_collect_count",
        //   name:"告警推送数统计图",
        //   type:"Pie",
        //   url:"",
        //   chartData: [],
        //   colors:["#00a8e1", "#99cc00" ,"#e30039", "#fcd300", "#800080" ,"#00994e", "#ff6600" ,"#808000" ,"#f0da49" ,"#f7af59" ,"#f06464" ,"#9489fa"].reverse(),
        // },
        {
          id:"alarm_collect_trend",
          name:"告警推送波次近7天趋势图",
          type:"EchartsLine",
          url:"",
          chartData: [],
          option: {
            xAxis:[{data: []}],
            grid: { left: '5%', right: '35', top: '30', bottom: 0, containLabel: true, }
          },
          colors:["#00a8e1", "#99cc00" ,"#e30039", "#fcd300", "#800080" ,"#00994e", "#ff6600" ,"#808000" ,"#f0da49" ,"#f7af59" ,"#f06464" ,"#9489fa"].reverse(),
        },{
          id:"alarm_collect_fail_trend",
          name:"告警失败波次近7天趋势图",
          type:"EchartsLine",
          url:"",
          chartData: [],
          option: {
            xAxis:[{data: []}],
            grid: { left: '5%', right: '35', top: '30', bottom: 0, containLabel: true, }
          },
          colors:["#00a8e1", "#99cc00" ,"#e30039", "#fcd300", "#800080" ,"#00994e", "#ff6600" ,"#808000" ,"#f0da49" ,"#f7af59" ,"#f06464" ,"#9489fa"].reverse(),
        },
      ],
      isReady: false,
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.getTrendData();

    })
  },
  computed: {

  },
  methods: {
    getTrendData() {
      let url = "/abutment/system/trendCount"
      if(this.$JKDJSTATICS){
        url = this.$JKDJSTATICS+url
      }
      let deviceCollectTrend = this.echartsCom.find(el=> el.id === "device_collect_trend");
      let alarmCollectTrend = this.echartsCom.find(el=> el.id === "alarm_collect_trend");
      let deviceFailTrend = this.echartsCom.find(el=> el.id === "device_collect_fail_trend");
      let alarmFailTrend = this.echartsCom.find(el=> el.id === "alarm_collect_fail_trend");
      deviceCollectTrend.chartData =[]
      alarmCollectTrend.chartData =[]
      getAction(url).then((res)=>{
        if(res.success && res.result) {
          let devInfo = res.result.deviceResult || null;
          let alarmInfo = res.result.alarmResult || null;
          let sysInfo = res.result.systemList || null;
          deviceCollectTrend.option.xAxis[0].data = devInfo ? Object.keys(devInfo) : []
          deviceFailTrend.option.xAxis[0].data = devInfo ? Object.keys(devInfo) : []
          alarmCollectTrend.option.xAxis[0].data = alarmInfo ? Object.keys(alarmInfo) : []
          alarmFailTrend.option.xAxis[0].data = alarmInfo ? Object.keys(alarmInfo) : []

          let devValue = devInfo ? Object.values(devInfo) : []
          let alarmValue = alarmInfo ? Object.values(alarmInfo) : []
          if (sysInfo && sysInfo.length) {
            if (devValue.length > 0) {
              deviceCollectTrend.chartData = sysInfo.map(el => {
                return {
                  name: el.systemName, data: devValue.map(del => {
                    let t = del.find(e => e.systemId == el.id)
                    if (t) {
                      return t.value;
                    } else {
                      return 0;
                    }
                  })
                }
              })
              deviceFailTrend.chartData = sysInfo.map(el => {
                return {
                  name: el.systemName, data: devValue.map(del => {
                    let t = del.find(e => e.systemId == el.id)
                    if (t) {
                      return t.value1;
                    } else {
                      return 0;
                    }
                  })
                }
              })
            }
            if (alarmValue.length > 0) {
              alarmCollectTrend.chartData = sysInfo.map(el => {
                return {
                  name: el.systemName, data: alarmValue.map(del => {
                    let t = del.find(e => e.systemId == el.id)
                    if (t) {
                      return t.value;
                    } else {
                      return 0;
                    }
                  })
                }
              })
              alarmFailTrend.chartData = sysInfo.map(el => {
                return {
                  name: el.systemName, data: alarmValue.map(del => {
                    let t = del.find(e => e.systemId == el.id)
                    if (t) {
                      return t.value1;
                    } else {
                      return 0;
                    }
                  })
                }
              })
            }
          }
        }
      }).finally(()=>{
        this.getChartPieData()
      })
    },
    getChartPieData(){
      let url = "/abutment/system/pieCount"
      if(this.$JKDJSTATICS){
        url = this.$JKDJSTATICS+url
      }
      getAction(url).then((res) => {
        if (res.success && res.result) {
          let devInfo = res.result.deviceCountVos||[];
          let deviceCollectPie = this.echartsCom.find(el=> el.id === "device_collect_waves");
          let alarmCollectPie = this.echartsCom.find(el=> el.id === "alarm_collect_waves");
          deviceCollectPie.chartData =[]
          alarmCollectPie.chartData =[]
          devInfo.forEach((item,index)=>{
            deviceCollectPie.chartData.push({name:item.systemName,value:item.waves})
            // this.echartsCom[1].chartData.push({name:item.systemName,value:item.records})
          })
          let alarmInfo = res.result.alarmCountVos||[]
          alarmInfo.forEach((item,index)=>{
            alarmCollectPie.chartData.push({name:item.systemName,value:item.waves})
            // this.echartsCom[4].chartData.push({name:item.systemName,value:item.records})
          })
          this.isReady = true;
        }
      })
    }
  }
}
</script>
<style scoped lang='less'>
.jkdjgl-statistics{
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
}
#jkdjglStatistics [class~='ant-col'] {
  background: transparent;
  border: 0;
}
.chart-box{
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>