<template>
  <div style="height: 100%">
    <a-spin :spinning='spinLoading' wrapperClassName="custom-ant-spin">
      <a-row style="height: 100%;">
        <!-- 左侧指标树 -->
        <a-col :xl="24" :lg="24" :md="24" :sm="24" :xs="24" style="font-size: 14px; padding:24px;background-color:#fff;height: 100%; overflow: hidden; overflow-y: auto;border-left:1px solid #eee;">
          <!-- 顶部报告卡片 -->
          <task-card
            v-if="nodeInfo"
            :task-info="taskInfo"
            :nodeInfo="nodeInfo"
            :catEvaluateResult="catEvaluateResult"
            @startEditing="spinLoading=true"
            @completeEditing="reload"
            @getGo="getGo">
          </task-card>
          <a-table
            style="margin-top: 24px"
            ref="table"
            bordered
            rowKey="id"
            :columns="columns"
            :dataSource="dataSource"
            :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
            :pagination="ipagination"
            :loading="loading"
            @change="handleTableChange"
          >
            <span class="caozuo" slot="action" slot-scope="text, record">
              <a :class="{'disabledStatus':record.evaluateStatus!==2&&record.evaluateStatus!==1 }" @click="handleDetail(record)">查看问卷</a>
            </span>
            <template slot="tooltip" slot-scope="text">
              <a-tooltip placement="topLeft" :title="text" trigger="hover">
                <div class="tooltip">
                  {{ text }}
                </div>
              </a-tooltip>
            </template>
            <template slot="evaluateStatus" slot-scope="text, record">
              <a-tag class="tag" :color="statusConfig(record.evaluateStatus).color">
                {{ statusConfig(record.evaluateStatus).text }}
              </a-tag>
            </template>
            <template slot="link" slot-scope="text, record">
              <a v-if="text.length > 0" style="color: #409eff">{{ text }}</a>
              <span v-else>0</span>
            </template>
          </a-table>
        </a-col>
      </a-row>
    </a-spin>
    <!-- 查看问卷 -->
    <addQuestionModal ref="modalForm" @ok="modalFormOk"></addQuestionModal>
  </div>
</template>
<script>
import {postAction, getAction } from '@/api/manage'
import MetricsTree from './MetricsTree.vue'
import { downloadFile } from '@/api/manage'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import addQuestionModal from '@views/operationalEvaluationNew/materials/modules/addQuestionModal.vue'
import taskCard from './taskCard.vue'
import { getStatusConfig } from '@/views/operationalEvaluationNew/modules/statusConfig'
export default {
  name: 'UnitSubmissionList',
  mixins: [JeecgListMixin],
  components: {
    MetricsTree,
    addQuestionModal,
    taskCard,
  },
  props: {
    pTaskInfo: {
      type: Object,
      default: () => ({}),
      required: true,
    },
    pNodeInfo: {
      type: Object,
      default: () => ({}),
      required: true,
    }
  },
  data() {
    return {
      spinLoading:false,
      taskInfo:null,
      nodeInfo: null,
      catEvaluateResult:null,
      columns: [
        {
          title: '单位名称',
          dataIndex: 'departName',
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
            return {
              style: cellStyle,
            }
          },
          scopedSlots: {
            customRender: 'tooltip',
          },
        },
        // {
        //   title: '行政区划',
        //   dataIndex: 'areaName',
        // },
        {
          title: '联系人',
          dataIndex: 'contacts',
        },
        {
          title: '联系电话',
          dataIndex: 'mobile',
        },
        {
          title: '填报状态',
          dataIndex: 'evaluateStatus',
          scopedSlots: {
            customRender: 'evaluateStatus',
          },
        },
        // {
        //   title: '填报时间',
        //   dataIndex: 'submissionTime',
        // },
        // {
        //   title: '问卷文件链接',
        //   dataIndex: 'fileLink',
        //   customCell: () => {
        //     let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
        //     return {
        //       style: cellStyle,
        //     }
        //   },
        //   scopedSlots: {
        //     customRender: 'link',
        //   },
        // },
        // {
        //   title: '附件数量',
        //   dataIndex: 'attachmentNum',
        // },
        {
          title: '操作',
          dataIndex: 'action',
          fixed: 'right',
          width: 100,
          scopedSlots: { customRender: 'action' },
        },
      ],
      url: {
        list: '/devops/projectInfo/deptList', // 获取单位列表
        dynamicGetEvaluateResult: '/devops/projectInfo/dynamicGetEvaluateResult',
        metricsQueryById:'/devops/metricsInfo/queryById',
        metricsTypeQueryById:'/evaluate/metricsType/queryById'
      },
      disableMixinCreated: true,
    }
  },
  created() {
    this.nodeInfo=null
    this.init()
  },
  computed: {},
  methods: {
    init(){
      this.taskInfo=this.pTaskInfo
      this.nodeInfo=this.pNodeInfo
      this.queryParam.completeFlag=''
     if (this.taskInfo.previewStatus){
       this.queryParam.completeFlag=this.taskInfo.info.previewStatus==='1'?'unComplete':'complete'
     }else {
       this.taskInfo.previewStatus=this.pNodeInfo.info.status
     }

      this.queryParam.projectId= this.taskInfo.id
     if (this.nodeInfo.type == 'category') {
        this.queryParam.metricsTypeId = this.nodeInfo.id
        this.queryParam.metricsId = ''
        this.getDynamicGetEvaluateResult()
      } else if (this.nodeInfo.type == 'metric') {
        this.queryParam.metricsId =  this.nodeInfo.id
        this.queryParam.metricsTypeId = ''
      }
      this.loadData(1)
    },
    // 获取填报状态的颜色配置
    statusConfig(status) {
      return getStatusConfig(status)
    },
    getGo() {
      this.$parent.pButton1(1)
    },
    getDynamicGetEvaluateResult(){
      this.spinLoading=true
      this.catEvaluateResult=null
      let params={
        projectId:this.queryParam.projectId,
        metricsTypeId:this.queryParam.metricsTypeId
      }
      getAction(this.url.dynamicGetEvaluateResult,params).then((res)=>{
        if (res.success&&res.result){
          this.catEvaluateResult=res.result
        }else {
          this.$message.warning(res.message)
        }
        this.spinLoading=false
      }).catch((err)=>{
        this.$message.warning(err.message)
      })
    },
    // 打开问卷
    handleDetail(record) {
      if (record.evaluateStatus==2||record.evaluateStatus==1){
        this.$refs.modalForm.initData(record.p2d2mArray)
        this.$refs.modalForm.title = '查看详情'
        this.$refs.modalForm.disableSubmit = true
      }
    },
    /*编辑了评估结果后，重新加载页面数据：树*/
    async reload(){
      this.spinLoading=true
      if (this.nodeInfo.type==='category'){
        await this.QueryMetricsTypeById()
      }else if(this.nodeInfo.type==='metric'){
        await this.QueryMetricsById()
      }
      this.loadData(1)
    },
    async QueryMetricsById(){
      await getAction(this.url.metricsQueryById, { projectId: this.taskInfo.id,id:this.nodeInfo.id }).then((res) => {
        if (res.success&&res.result) {
          this.nodeInfo=res.result
        }else if(!res.success){
          this.$message.warning(res.message)
        }
      }).catch((err)=>{
        this.$message.warning(err.message)
      })
    },
    async QueryMetricsTypeById(){
      await getAction(this.url.metricsTypeQueryById, { projectId: this.taskInfo.id,id:this.nodeInfo.id }).then((res) => {
        if (res.success&&res.result) {
          this.nodeInfo=res.result
        }else if(!res.success){
          this.$message.warning(res.message)
        }
      }).catch((err)=>{
        this.$message.warning(err.message)
      })
    },
    loadData(arg) {
      if (!this.url.list) {
        this.$message.error('请设置url.list属性!')
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1
      }
      var params = this.getQueryParams() //查询条件
      getAction(this.url.list, params).then((res) => {
        if (res.success && res.result) {
          this.dataSource = res.result.records || res.result
          if (this.dataSource.length < 9) {
            this.clientHeight = false
          }
          this.ipagination.total =res.result.total?res.result.total:0
        }
        if (res.code === 510) {
          this.$message.warning(res.message)
        }
        this.spinLoading=false
      })
    },
  }
}
</script>
<style scoped lang="less">
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
.disabledStatus{
  color: #e8e8e8;;
}
</style>
