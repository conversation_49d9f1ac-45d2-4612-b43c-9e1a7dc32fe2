<template>
  <a-row :gutter="10" style="height: 100%;" class="vScroll">
    <a-col style="width:100%;height: 100%;display: flex;flex-direction: column">
      <!-- 查询区域 -->
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0', marginRight: '12px' }" class="card-style"
        style="width: 100%">
        <div class="orderBox">
          <div v-for="item in orderList" class="order_box">
            <div class="order_name">{{ item.name }}</div>
            <div class="order_bottom">
              <span class="order_value">{{ item.value }}</span>
              <span class="order_unit">{{ item.unit }}</span>
            </div>
          </div>
        </div>
      </a-card>
      <a-card :bordered="false" style="width: 100%; flex: auto">
        <!-- 操作按钮区域 -->
        <div class="table-operator table-operator-style">
          <a-button @click="backendExportData('工单统计表')">导出</a-button>
        </div>
        <a-table ref="table" bordered rowKey="id" :columns="columns" :dataSource="dataSource" :pagination="ipagination"
          :loading="loading" @change="handleTableChange">
          <span slot="percent" slot-scope="text">{{ text + '%' }}</span>
        </a-table>
      </a-card>
    </a-col>
  </a-row>
</template>

<script>
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import {
    getAction
  } from '@/api/manage'
  import {
    YqFormSeniorSearchLocation
  } from '@/mixins/YqFormSeniorSearchLocation'
  import {
    YqFormSearchLocation
  } from '@/mixins/YqFormSearchLocation'
  export default {
    name: 'orderStatisticsList',
    mixins: [JeecgListMixin, YqFormSearchLocation, YqFormSeniorSearchLocation],
    data() {
      return {
        orderList: [{
          value: 30,
          unit: '个',
          name: '工单总数'
        }, {
          value: 20,
          unit: '个',
          name: '已处理工单'
        }, {
          value: 10,
          unit: '个',
          name: '未处理工单'
        }, {
          value: 100,
          unit: '%',
          name: '及时处理率'
        }, {
          value: 80,
          unit: '%',
          name: '解决率'
        }, {
          value: 79,
          unit: '%',
          name: '好评率'
        }],
        // 表头
        columns: [{
          title: '工程师',
          dataIndex: 'assignee_dictText',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 130px;max-width:300px'
            return {
              style: cellStyle
            }
          }
        },
          {
            title: '接单数量',
            dataIndex: 'allCount',
            customCell: () => {
              let cellStyle = 'text-align: right;min-width: 150px;max-width:350px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '已处理工单',
            dataIndex: 'completedCount',
            customCell: () => {
              let cellStyle = 'text-align: right;min-width: 150px;max-width:350px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '未处理工单',
            dataIndex: 'unCompletedCount',
            customCell: () => {
              let cellStyle = 'text-align: right;min-width: 150px;max-width:350px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '拒单数量',
            dataIndex: 'rejectCount',
            customCell: () => {
              let cellStyle = 'text-align: right;min-width: 150px;max-width:350px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '及时处理率',
            dataIndex: 'handTimelyRate',
            scopedSlots: {
              customRender: 'percent'
            },
            customCell: () => {
              let cellStyle = 'text-align: right;min-width: 110px;max-width:350px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '解决率',
            dataIndex: 'resolutionRate',
            scopedSlots: {
              customRender: 'percent'
            },
            customCell: () => {
              let cellStyle = 'text-align: right;min-width: 110px;max-width:350px'
              return {
                style: cellStyle
              }
            }
          },
          {
            title: '好评率',
            dataIndex: 'goodRate',
            scopedSlots: {
              customRender: 'percent'
            },
            customCell: () => {
              let cellStyle = 'text-align: right;min-width: 110px;max-width:350px'
              return {
                style: cellStyle
              }
            }
          }
        ],
        url: {
          list: '/serviceProvider/serviceProviderUserStatistics',
          exportXlsUrl: '/serviceProvider/exportExcel',
          Statistics: '/serviceProvider/serviceProviderStatistics',

        },
      }
    },
    created() {
      this.getStatistics()
    },
    mounted() {
    },
    methods: {
      getStatistics() {
        getAction(this.url.Statistics).then((res) => {
          this.orderList[0].value = res.result&&res.result.allCount != null ? res.result.allCount : 0
          this.orderList[1].value = res.result&&res.result.completedCount != null ? res.result.completedCount : 0
          this.orderList[2].value = res.result&&res.result.unCompletedCount != null ? res.result.unCompletedCount : 0
          this.orderList[3].value = res.result&&res.result.handTimelyRate != null ? res.result.handTimelyRate : 0
          this.orderList[4].value = res.result&&res.result.resolutionRate != null ? res.result.resolutionRate : 0
          this.orderList[5].value = res.result&&res.result.goodRate != null ? res.result.goodRate : 0
        })
      },
    }
  }
</script>
<style lang='less' scoped>
  @import '~@assets/less/common.less';
  @import '~@assets/less/scroll.less';

  .orderBox {
    width: 100%;
    display: flex;
    justify-content: space-around;

    .order_box {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      .order_name {
        font-size: 18px;
        color: rgba(0, 0, 0, 0.65);
        font-weight: 500;
      }

      .order_bottom {
        margin-bottom: 10px;

        .order_value {
          font-size: 40px;
          color: #409EFF;
          font-weight: 700;
        }

        .order_unit {
          font-size: 14px;
          color: rgba(0, 0, 0, 0.65);
          font-weight: 500;
          margin-left: 5px;
        }
      }
    }
  }
</style>