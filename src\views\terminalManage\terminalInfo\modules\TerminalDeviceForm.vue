<template>
  <a-spin :spinning='confirmLoading'>
    <j-form-container :disabled='formCode'>
      <a-form-model ref='form' slot='detail' :labelCol='labelCol' :wrapperCol='wrapperCol' :model='model'
                    :rules='validatorRules'>
        <a-row :gutter='24'>
          <a-col v-bind='formItemLayout'>
            <a-form-model-item label='终端名称' prop='name'>
              <a-input v-model='model.name' placeholder='请输入终端名称' :allowClear='true' autocomplete='off' />
            </a-form-model-item>
          </a-col>
          <a-col v-bind='formItemLayout'>
            <a-form-model-item label='ip地址' prop='ip'>
              <a-input v-model='model.ip' placeholder='请输入终端ip' :allowClear='true' autocomplete='off' />
            </a-form-model-item>
          </a-col>
          <a-col v-bind='formItemLayout'>
            <a-form-model-item label='mac地址' prop='macAddr'>
              <a-input v-model='model.macAddr' placeholder='请输入终端mac地址' :allowClear='true' autocomplete='off' />
            </a-form-model-item>
          </a-col>
          <a-col v-bind='formItemLayout'>
            <a-form-model-item label='操作系统' prop='osType'>
              <j-dict-select-tag v-model='model.osType' placeholder='请选择操作系统类型' dictCode='os_type' />
            </a-form-model-item>
          </a-col>
          <a-col v-bind='formItemLayout'>
            <a-form-model-item label='cpu类型' prop='cpuType'>
              <j-dict-select-tag v-model='model.cpuType' placeholder='请选择cpu类型' dictCode='cpuType' />
            </a-form-model-item>
          </a-col>
          <a-col v-bind='formItemLayout'>
            <a-form-model-item label='cpu架构' prop='cpuArch'>
              <j-dict-select-tag v-model='model.cpuArch'
                                 placeholder='请选择cpu架构'
                                 dictCode='cpuArch' />
            </a-form-model-item>
          </a-col>
          <a-col v-bind='formItemLayout'>
            <a-form-model-item label='使用人' prop='username'>
              <j-select-user-by-dep-no-right
                ref='JSelectUserByDepEnhance'
                v-model='model.username'
                :disabled='model.id&&model.bindStatus=="已绑定"'
                :multi='false'
                :allowClear='true'
                :userIds.sync='userIds'
                :ccToVos.sync='ccToVos'>
              </j-select-user-by-dep-no-right>
            </a-form-model-item>
          </a-col>
          <a-col v-bind='formItemLayout'>
            <a-form-model-item label='联系电话' prop='phone'>
              <a-input v-model='model.phone' placeholder='请输入联系电话' :allowClear='true' disabled
                       autocomplete='off' />
            </a-form-model-item>
          </a-col>
          <a-col v-bind='formItemLayout'>
            <a-form-model-item label='单位' prop='deptId' v-if='!model.username'>
              <a-tree-select v-model='model.deptId'
                             :getPopupContainer='(node) => node.parentNode'
                             tree-node-filter-prop='title'
                             :replaceFields='replaceFields'
                             :treeData='treeData'
                             show-search
                             :searchValue='bsearchKey'
                             style='width: 100%'
                             :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                             placeholder='请选择单位'
                             allow-clear
                             @change='onChangeTree'
                             @search='onSearch'
                             @select='onSelect'>
              </a-tree-select>

            </a-form-model-item>
            <a-form-model-item v-else label='单位' prop='deptId'>
              <a-select
                placeholder='请选择单位'
                v-model='model.deptId'
                :disabled='!model.username'
                :allowClear='true'
                :getPopupContainer='(node) => node.parentNode'
                :dropdownClassName='"custom-select-dropdown"'
                @change='onUserDeptChange'>
                <a-select-option v-for='item in selectOption' :key='item.id' :value='item.id'>
                  {{ item.departName }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>

          <a-col v-bind='formItemLayout'>
            <a-form-model-item label='行政区划' prop='addrId'>
              <yq-area-cascader-select v-model='model.addrId' disabled placeholder='请选择行政区划'>
              </yq-area-cascader-select>
            </a-form-model-item>
          </a-col>
          <a-col v-bind='formItemLayout'>
            <a-form-model-item label='地址' prop='address'>
              <a-input v-model='model.address' placeholder='请输入地址' :disabled='true' :allowClear='true'
                       autocomplete='off' />
            </a-form-model-item>
          </a-col>
          <!--          <a-col v-bind='formItemLayout'>-->
          <!--            <a-form-model-item label="使用部门" prop='userDepartment'>-->
          <!--              <a-input v-model="model.userDepartment" placeholder="请输入使用部门" :allowClear='true' autocomplete='off' disabled/>-->
          <!--            </a-form-model-item>-->
          <!--          </a-col>-->
          <a-col v-bind='formItemLayout'>
            <a-form-model-item label='管理人' prop='administrator'>
              <a-input v-model='model.administrator' placeholder='请输入管理人' :allowClear='true' autocomplete='off' />
            </a-form-model-item>
          </a-col>
          <a-col v-bind='formItemLayout'>
            <a-form-model-item label='管理部门' prop='adminDepartment'>
              <a-input v-model='model.adminDepartment' placeholder='请输入管理部门' :allowClear='true'
                       autocomplete='off' />
            </a-form-model-item>
          </a-col>
          <a-col v-bind='formItemLayout'>
            <a-form-model-item label='SN' prop='sn'>
              <a-input v-model='model.sn' placeholder='请输入SN' :allowClear='true' autocomplete='off' />
            </a-form-model-item>
          </a-col>

          <a-col v-bind='formItemLayout'>
            <a-form-model-item label='详细地址' prop='addrDetail'>
              <a-input v-model='model.addrDetail' placeholder='请输入详细地址' :allowClear='true' autocomplete='off' />
            </a-form-model-item>
          </a-col>
          <a-col v-bind='formItemLayout'>
            <a-form-model-item class='two-words' label='位置' prop='positionTag'>
              <a-input v-model='model.positionTag' placeholder='请输入位置' :allowClear='true' autocomplete='off' />
            </a-form-model-item>
          </a-col>
          <a-col v-bind='formItemLayout'>
            <a-form-model-item class='two-words' label='描述' prop='description'>
              <a-textarea v-model='model.description' placeholder='请输入描述' :auto-size="{ minRows: 1, maxRows: 6 }" :allowClear='true' autocomplete='off'>
              </a-textarea>
            </a-form-model-item>
          </a-col>
          <a-col v-bind='formItemLayout'>
            <a-form-model-item label='终端类型' prop='terminalType'>
              <j-dict-select-tag v-model='model.terminalType' :allowClear='false' placeholder='请选择终端类型'
                                 dictCode='resources_type' />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import { ajaxGetDictItem, ajaxGetDictItems, getProgectList } from '@/api/api'
import YqAreaCascaderSelect from '@/components/areaDict/YqAreaCascaderSelect'
import JSelectUserByDepNoRight from '@/components/flowable/JSelectUserByDepNoRight'

export default {
  name: 'TerminalDeviceForm',
  components: {
    YqAreaCascaderSelect,
    JSelectUserByDepNoRight
  },
  data() {
    return {
      formCode: false,
      confirmLoading: false,
      formItemLayout: {
        md: { span: 12 },
        sm: { span: 24 }
      },
      labelCol: {
        xs: { span: 24 },
        sm: { span: 7 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      replaceFields: {
        children: 'children',
        title: 'deptName',
        key: 'deptId',
        value: 'deptId'
      },
      treeData: [],
      bsearchKey: '',
      allDepts: '',
      terminalmacipcheck_num: '',
      // isEnable: false,
      // macEnable: false,
      // ipEnable: false,
      //dept: {},
      model: {
        terminalType: '6'
      },
      validatorRules: {
        sn: [
          { required: false, min: 2, max: 50, message: 'sn长度应在2-50个中英文、数字之间', trigger: 'blur' },
          { pattern: /^[a-zA-Z0-9]*$/, message: 'sn号为中英文、数字' }
        ],
        osType: [
          { required: false, message: '请选择操作系统类型', trigger: 'blur' }
        ],
        userDepartment: [
          { required: false }
        ],
        adminDepartment: [
          { required: false, min: 2, max: 30, message: '管理部门长度应在2-30个字符之间', trigger: 'blur' },
          {
            pattern: /^(([a-zA-Z]+|[0-9]+|[a-zA-Z0-9-_]+|[\u4E00-\u9FA5]))*$/,
            message: '管理部门仅限中英文、数字、特殊字符_、-'
          }
        ],
        administrator: [
          { required: false, min: 2, max: 10, message: '管理人应为2-10个汉字', trigger: 'blur' },
          { pattern: /^[\u4E00-\u9FA5]*$/, message: '请输入汉字' }
        ],
        name: [
          { required: true, message: '请输入终端名称' },
          { min: 2, max: 50, message: '终端名称长度应在 2-50 之间', trigger: 'blur' }
        ],
        ip: [
          { required: true, message: '请输入终端IP' },
          {
            pattern: /^((25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))\.){3}(25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))$/,
            message: '请输入正确的IP地址'
          }
        ],
        macAddr: [
          { required: true, message: '请输入终端MAc地址' },
          {
            pattern: /^[a-fA-F\d]{2}:[a-fA-F\d]{2}:[a-fA-F\d]{2}:[a-fA-F\d]{2}:[a-fA-F\d]{2}:[a-fA-F\d]{2}$|^[a-fA-F\d]{2}-[a-fA-F\d]{2}-[a-fA-F\d]{2}-[a-fA-F\d]{2}-[a-fA-F\d]{2}-[a-fA-F\d]{2}$/,
            message: '请输入正确的MAC地址'
          }
        ],
        username: [
          { required: false }
        ],
        cpuArch: [
          { required: true, message: '请输入cpu架构' }
        ],
        deptId: [
          { required: true, message: '请选择单位' }
        ],
        phone: [
          { required: false, message: '请输入联系电话' },
          {
            pattern: /^1[34578][0-9]{9}$|^(0[0-9]{2,3}\-)?([2-9][0-9]{6,7})+(\-[0-9]{1,4})?$/,
            message: '请输入正确的联系电话'
          }
        ],
        description:[
          { required: false,max:200,message: '描述长度应在 200 个字符之内！' }
        ]
      },
      url: {
        add: '/terminal/terminalDevice/add',
        edit: '/terminal/terminalDevice/edit',
        queryById: '/terminal/terminalDevice/queryById'
        // getPlatformCode: '/data/reportAndConverge/getPlatformCode',
      },
      userIds: '',
      ccToVos: [],
      selectOption: []
    }
  },
  watch: {
    'ccToVos': {
      immediate: true,
      handler(val) {
        if (val && val.length) {
          this.selectOption = val[0].allocateOrgList
          this.model.phone = val[0].phone
          this.model.deptId = this.selectOption[0]?.id
          if (this.model.deptId) {
            this.model.addrId = this.selectOption[0]?.cityId
            this.model.address = this.selectOption[0]?.address
          }
          // if (val[0].orgCodeTxt) {
          //   var list =  val[0].orgCodeTxt.split(',')
          //   this.model.userDepartment = list[0]
          // } else {
          //   this.model.userDepartment = ''
          // }
        } else {
          this.selectOption = []
          this.model.phone = ''
          this.model.deptId = undefined
          this.model.addrId = undefined
          this.model.address = ''
        }
      }
    }
  },
  created() {
    this.loadAllMomgDept()
    this.getDepartList()
    this.getterminalmacipcheckValue()
  },
  mounted() {
  },
  methods: {
    // getPlatformCode(id) {
    //   getAction(this.url.getPlatformCode).then((res) => {
    //     if (res.success) {
    //       if (res.result == id) {
    //         this.formCode = true
    //       }
    //     }
    //   })
    // },
    getterminalmacipcheckValue() {
      //从字典中获取项目栏MacIP配置值
      ajaxGetDictItem('terminalmacipcheck').then((res) => {
        if (res.success) {
          this.terminalmacipcheck_num = res.result[0].value
          //根据获取到的value值判断MacIP是否必输
          this.macip()
        } else {
          this.validatorRules.macAddr[0].required = true
          this.validatorRules.ip[0].required = true
        }
      })
    },
    macip() {
      if (this.terminalmacipcheck_num == '01') {
        this.validatorRules.macAddr[0].required = false
        this.validatorRules.ip[0].required = true
        // if (this.isEnable && true) {
        //   this.ipEnable = true
        // }
      } else if (this.terminalmacipcheck_num == '00') {
        this.validatorRules.macAddr[0].required = false
        this.validatorRules.ip[0].required = false
      } else if (this.terminalmacipcheck_num == '10') {
        this.validatorRules.macAddr[0].required = true
        // if (this.isEnable && true) {
        //   this.macEnable = true
        // }
        this.validatorRules.ip[0].required = false
      } else {
        this.validatorRules.macAddr[0].required = true
        // if (this.isEnable && true) {
        //   this.macEnable = true
        // }
        this.validatorRules.ip[0].required = true
        // if (this.isEnable && true) {
        //   this.ipEnable = true
        // }
      }
    },
    add() {
      this.edit({
        addrId: '',
        deptId: '',
        cpuArch: '',
        cpuType: '',
        osType: '',
        terminalType: '6'
      })
    },
    edit(record) {
      this.visible = true
      // this.getPlatformCode(record.platformCode)
      this.$nextTick(() => {
        if (record.id) {
          this.isEnable = true
          getAction('/sys/user/getUserInfoByCondition', { username: record.username }).then(res => {
            if (res.success) {
              this.selectOption = res.result.allocateOrgList || []
            }
          })
        }
        this.model = Object.assign({}, record)
        this.model.addrId = record.addrId != undefined && record.addrId != null && record.addrId !== '' ? record.addrId.toString() : undefined
        this.model.deptId =  record.deptId != undefined && record.deptId != null && record.deptId !== '' ? record.deptId : undefined
        this.model.cpuArch = record.cpuArch != undefined && record.cpuArch != null && record.cpuArch !== '' ? record.cpuArch : undefined
        this.model.cpuType = record.cpuType != undefined && record.cpuType != null && record.cpuType !== '' ? record.cpuType : undefined
        this.model.osType = record.osType != undefined && record.osType != null && record.osType !== '' ? record.osType : undefined
        this.model.phone = record.username ? record.phone : ''
      })
    },
    loadAllMomgDept() {
      let that = this
      getAction('/sys/sysDepart/queryAllDepts').then((res) => {
        that.allDepts = res.result
      })
    },
    // queryAddress(id) {
    //   let that = this
    //   getAction('/sys/sysDepart/queryById', { id: id }).then((res) => {
    //     that.dept = res.result
    //     this.form.setFieldsValue(pick({ address: that.dept.address }, 'address'))
    //   })
    // },
    getDepartList() {
      getAction('/sys/sysDepart/queryAllTree').then((res) => {
        for (let i = 0; i < res.length; i++) {
          let temp = res[i]
          this.treeData.push(temp)
        }
      })
    },
    onChangeTree(value) {
    },
    onUserDeptChange(){
      let select = this.selectOption.find(el => {
        return el.id === value
      })
      if (select) {
        this.model.addrId = select.cityId
        this.model.address = select.address
      } else {
        this.model.addrId = undefined
        this.model.address = ''
      }
    },
    onSearch(e) {
      this.bsearchKey = e
    },
    onSelect() {
      getAction('/sys/sysDepart/queryById', {
        id: arguments[0]
      }).then((res) => {
        if (res.success) {
          this.model.addrId = res.result.cityId
          // this.model.address = res.result.address
          this.model.address = undefined
        }
      })
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.$refs.form.validate((err, values) => {
        if (err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let formData = JSON.parse(JSON.stringify(this.model))
          formData.addrId = formData.addrId !== undefined ? formData.addrId : ''
          formData.cpuArch = formData.cpuArch !== undefined ? formData.cpuArch : ''
          formData.cpuType = formData.cpuType !== undefined ? formData.cpuType : ''
          formData.osType = formData.osType !== undefined ? formData.osType : ''
          formData.deptId = formData.deptId !== undefined ? this.model.deptId : ''

          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
              that.confirmLoading = false
            }).catch((err) => {
            that.$message.warning(err.message)
            that.confirmLoading = false
          })
        }
      })
    }
  }
}
</script>
<style lang='less' scoped>
::v-deep .two-words > div > label {
  letter-spacing: 4px;
}

::v-deep .two-words > div > label::after {
  letter-spacing: 0px;
}
</style>