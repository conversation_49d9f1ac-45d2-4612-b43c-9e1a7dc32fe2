<template>
  <a-row :gutter="10" style="height: 100%" class="vScroll zhl zhll">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <!-- 操作按钮区域 -->
      <a-card :bordered="false" style="width: 100%; flex: auto">
        <div class="table-operator table-operator-style">
          <a-button @click="handleAdd">新增</a-button>
          <a-dropdown v-if="selectedRowKeys.length > 0">
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item key="1" @click="batchDel">删除</a-menu-item>
            </a-menu>
            <a-button> 批量操作 <a-icon type="down" /></a-button>
          </a-dropdown>
        </div>
        <!-- table区域-begin -->
        <a-table
          ref="table"
          bordered
          rowKey="id"
          :columns="columns"
          :dataSource="dataSource"
          :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
          :pagination="ipagination"
          :loading="loading"
          :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          @change="handleTableChange"
        >
          <template slot="pathType" slot-scope="text">
            <div >{{getType(text)}}</div>
          </template>
          <template slot="priority" slot-scope="text">
            <div >{{getpriority(text)}}</div>
          </template>
          <template slot="htmlSlot" slot-scope="text">
            <div v-html="text"></div>
          </template>
          <template slot="imgSlot" slot-scope="text">
            <span v-if="!text" style="font-size: 14px">无图片</span>
            <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width: 80px; font-size: 14px" />
          </template>
          <template slot="fileSlot" slot-scope="text">
            <span v-if="!text" style="font-size: 14px">无文件</span>
            <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="uploadFile(text)">
              下载
            </a-button>
          </template>

          <span class="caozuo" slot="action" slot-scope="text, record">
            <a @click="handleEdit(record)">编辑</a>
            <a-divider type="vertical" />
            <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
              <a>删除</a>
            </a-popconfirm>
          </span>
          <template slot="tooltip" slot-scope="text">
            <a-tooltip placement="topLeft" :title="text" trigger="hover">
              <div class="tooltip">
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
      </a-card>

      <itilSla-modal ref="modalForm" @ok="modalFormOk"></itilSla-modal>
    </a-col>
  </a-row>
</template>

<script>
// import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import ItilSlaModal from './modules/ItilSlaModal'
import { ajaxGetDictItem } from '@/api/api'
export default {
  name: 'ItilSlaList',
  mixins: [JeecgListMixin, mixinDevice],
  components: {
    ItilSlaModal,
  },
  data() {
    return {
      description: '服务级别协议管理页面',
      pathTypes: [],
      prioritys:[],
      // 表头
      columns: [
        {
          title: '流程类型',
          // dataIndex: 'pathType_dictText',
          dataIndex: 'pathType',
          scopedSlots: { customRender: 'pathType' },
        },
        {
          title: '优先级',
          // dataIndex: 'priority_dictText',
          dataIndex: 'priority',
          scopedSlots: { customRender: 'priority' },
        },
        {
          title: 'SLA响应时间',
          dataIndex: 'responseTime',
        },
        {
          title: 'SLA完成时间',
          dataIndex: 'completeTime',
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 160,
          scopedSlots: { customRender: 'action' },
        },
      ],
      url: {
        list: '/itilsla/itilSla/list',
        delete: '/itilsla/itilSla/delete',
        deleteBatch: '/itilsla/itilSla/deleteBatch',
        exportXlsUrl: '/itilsla/itilSla/exportXls',
        importExcelUrl: 'itilsla/itilSla/importExcel',
      },
      dictOptions: {},
    }
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
  },
  created() {
    this.getDict()
  },
  methods: {
    getType(text){
      let item = this.pathTypes.find(el=>el.value == text)
      return item?item.text:text;
    },
    getpriority(text){
      let item = this.prioritys.find(el=>el.value == text)
      return item?item.text:text;
    },
    getDict() {
      ajaxGetDictItem('bpm_process_type').then((res) => {
        if (res.success) {
          this.pathTypes = res.result
        }
      })
      ajaxGetDictItem('priority').then((res) => {
        if (res.success) {
          this.prioritys = res.result
        }
      })
    },
    initDictConfig() {},
  },
}
</script>
<style lang="less" scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
/*表头样式*/
::v-deep .ant-table-thead > tr > th {
  text-align: center;
  white-space: nowrap;
}
/*给table列设置宽度*/
::v-deep .ant-table-scroll .ant-table-thead > tr > th,
::v-deep .ant-table-scroll .ant-table-tbody > tr > td {
  /*流程类型*/
  &:nth-child(2) {
    min-width: 200px;
  }

  /*优先级*/

  &:nth-child(3) {
    min-width: 50px;
  }

  /*SLA响应时间*/

  &:nth-child(4) {
    min-width: 100px;
  }

  /*SLA完成时间*/

  &:nth-child(5) {
    min-width: 100px;
  }
}
/*内容对齐方式、省略显示*/
::v-deep .ant-table-tbody > tr > td {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;

  &:first-child,
  &:nth-child(2),
  &:nth-child(3) {
    text-align: center;
  }
  &:nth-child(4),
  &:nth-child(5) {
    text-align: right;
  }
}
</style>
