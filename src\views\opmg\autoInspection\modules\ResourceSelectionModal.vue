<template>
  <j-modal :title='title' :width='width' :visible='visible' :centered='true' :confirmLoading='confirmLoading'
           switchFullscreen @ok='handleOk' :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }" @cancel='handleCancel'
           cancelText='关闭'>
    <div class='table-page-search-wrapper'>
      <a-form layout='inline' @keyup.enter.native='searchQuery'>
        <a-row :gutter='24'>
          <a-col :lg='12' :md='14' :sm='16' :xs='18' v-if="queryParam.inspectionType == '1'">
            <a-form-item label='产品名称' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <a-tree-select :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }" allowClear v-model='currentValue'
                             :getPopupContainer='(node) => node.parentNode' placeholder='请选择产品名称' @select='selectProduct'
                             @change='changeProduct' :tree-data='productTreeData' tree-icon>
              </a-tree-select>
            </a-form-item>
          </a-col>
          <a-col  :lg='12' :md='14' :sm='16' :xs='18' v-if="queryParam.inspectionType == '2'">
            <a-form-item label='应用名称' :labelCol='labelCol' :wrapperCol='wrapperCol' style='width: 100%'>
              <a-input :maxLength='maxLength' placeholder='请输入应用名称' :allowClear='true' v-model='queryParam.name'></a-input>
            </a-form-item>
          </a-col>
          <a-col :lg='8' :md='10' :sm='8' :xs='6' v-if="queryParam.inspectionType == '2'">
            <a-button type='primary' @click='searchQuery'>查询</a-button>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <div>
      <div
        style='line-height:30px;height:30px;margin-bottom: 5px;border-style:solid;border-width: 1px;border-color: #e8e8e8;background-color:#b8b8b8 '>
        <a-checkbox style='margin-left: 23px' v-model='isSelectAll' @change='onSelectAllPage'>全选</a-checkbox>
      </div>
      <div>
        <a-table style='width: 100%' size='middle' bordered :row-key='(record,index)=>{return record.id}'
                 :columns='columns' :dataSource='dataSource' :scroll='dataSource.length>0?{x:"max-content"}:{}'
                 :pagination='ipagination' :loading='loading' :hideDefaultSelections='true'
                 :rowSelection='{ selectedRowKeys: selectedRowKeys,onChange: onSelectChange }' @change='handleTableChange'>
        </a-table>
      </div>
    </div>
  </j-modal>
</template>

<script>
import JDictSelectTag from '@/components/dict/JDictSelectTag'
import pick from 'lodash.pick'
import {
  JeecgListMixin
} from '@/mixins/JeecgListMixin'
import {
  getAction,
  postAction
} from '@/api/manage'
import { filterObj } from '@/utils/util'
import fa from 'element-ui/src/locale/lang/fa'

export default {
  name: 'ResourceSelectionModal',
  components: {
    JDictSelectTag
  },
  mixins: [JeecgListMixin],
  data() {
    return {
      maxLength:50,
      title: '',
      width: 800,
      visible: false,
      confirmLoading: false,
      disableSubmit: false,
      form: this.$form.createForm(this),
      model: {},
      labelCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 8
        },
        md: {
          span: 8
        },
        lg: {
          span: 8
        },
        xl: {
          span: 8
        }
      },
      wrapperCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 16
        }
      },
      url: {
        //list: '/device/deviceInfo/list',
        productTreeList: '/assetscategory/assetsCategory/selectTree',
        list: '/autoInspection/devopsAutoInspection/getResourceList',
        selectedRowsList: '/autoInspection/devopsAutoInspection/bindDevice',
        formList: '/product/product/productList'
      },
      columns: [],
      tableList: [],
      tableIdList: [],
      selectedRowKeys: [],
      selectionRows: [],
      formList: [],
      productTreeData: [],
      productId: '',
      type: '',
      isSelectAll: false,
      disableMixinCreated: true,
      currentValue: undefined,
    }
  },
  created() {

  },
  mounted() {
    this.getProductTreeData()
  },
  methods: {
    onSelectChange(selectedRowKeys, selectionRows) {
      this.selectedRowKeys=selectedRowKeys
      this.selectionRows = selectionRows
      if (this.isSelectAll) {
        this.isSelectAll = false
        this.selectedRowKeys = []
        this.selectionRows = []
      } else {
        this.selectedRowKeys = selectedRowKeys
        this.selectionRows = selectionRows
      }
    },
    //全选当前页
    onSelectAll(selected, row) {
      if (this.isSelectAll) {
        this.isSelectAll = false
        this.selectionRows = []
        this.selectedRowKeys = []
      } else {
        this.selectionRows = this.dataSource
        this.selectedRowKeys = this.dataSource.map(pro => pro.id)
      }
    },
    //选中所有页面，并记录状态，切换页的时候，根据状态决定当前页时候选中所有数据
    onSelectAllPage() {
      this.selectionRows = this.isSelectAll == true ? this.dataSource : []
      this.selectedRowKeys = this.isSelectAll == true ? this.dataSource.map(pro => pro.id) : []
    },
    handleTableChange(pagination, filters, sorter) {
      //分页、排序、筛选变化时触发
      //TODO 筛选
      if (Object.keys(sorter).length > 0) {
        this.isorter.column = sorter.field
        this.isorter.order = 'ascend' == sorter.order ? 'asc' : 'desc'
      }
      this.ipagination = pagination
      this.loadData()
    },
    loadData(arg) {
      let that = this
      if (!this.url.list) {
        this.$message.error('请设置url.list属性!')
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1
      }
      var params = this.getQueryParams() //查询条件
      this.loading = true
      getAction(this.url.list, params).then((res) => {
        if (res.success) {
          this.dataSource = res.result.records || res.result
          this.selectedRowKeys = this.isSelectAll == true ? this.dataSource.map(pro => pro.id) : []
          if (this.dataSource.length < 9) {
            this.clientHeight = false
          }
          this.ipagination.total = res.result.total
        }
        if (res.code === 510) {
          this.$message.warning(res.message)
        }
        this.loading = false
      })
    },
    loadAllData(arg) {
      let that = this
      if (!this.url.list) {
        this.$message.error('请设置url.list属性!')
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1
      }
      this.loading=true
      var params = this.getQueryParams1() //查询条件
      return new Promise((resolve, reject)=>{
        getAction(this.url.list, params).then((res) => {
          if (res.success) {
            let dataSource = res.result.records || res.result
            let keys = dataSource && dataSource.length > 0 ? dataSource.map(pro => pro.id) : []
            let rows = dataSource && dataSource.length > 0 ? dataSource : []
            resolve({
              success: true,
              keys: keys,
              rows: rows,
              message:'请求成功'
            })
          }
          if (res.code === 510) {
            reject({
              success: false,
              keys: [],
              rows: [],
              message:res.message
            })
          }
        }).catch((err)=>{
          reject({
            success: false,
            keys: [],
            rows: [],
            message:err.message
          })
        })
      })
    },
    getQueryParams1() {
      //获取查询条件
      let sqp = {}
      if (this.superQueryParams) {
        sqp['superQueryParams'] = encodeURI(this.superQueryParams)
        sqp['superQueryMatchType'] = this.superQueryMatchType
      }
      var param = Object.assign(sqp, this.queryParam, this.isorter, this.filters)
      param.field = this.getQueryField()
      param.pageNo = this.ipagination.current
      param.pageSize = this.ipagination.total
      return filterObj(param)
    },

    getProductTreeData() {
      getAction(this.url.productTreeList).then((res) => {
        if (res.success) {
          this.productTreeData = res.result
          this.setDisabled(this.productTreeData)
        }
      })
    },

    setDisabled(data) {
      if (data.length && data.length > 0) {
        for (let i = 0; i < data.length; i++) {
          data[i] = {
            ...data[i],
            isLeaf: (data[i].children.length == 0),
            icon: (data[i].type != 'product' ?
              < a-icon type = 'folder' style = 'color:#409eff' /> :
              < a-icon type = 'file' style = 'color:#409eff' /> )
          }
          if (data[i].children.length > 0) {
            this.setDisabled(data[i].children)
          }
        }
      }
    },

    selectProduct(value, label, extra) {
      this.currentValue = value
      this.queryParam.productId = value
      this.queryParam.type = extra.selectedNodes[0].data.props.type
      this.productId = value
      this.type = extra.selectedNodes[0].data.props.type
      this.isSelectAll = false
      this.loadData()
    },
    changeProduct(value, label, extra) {
      if (!value) {
        this.queryParam.productId = undefined
        this.queryParam.type = undefined
        this.isSelectAll = false
        this.loadData()
      }
    },
    add() {
      this.edit({})
    },
    edit(record, idList) {
      this.inspectionType = record.inspectionType
      this.queryParam.inspectionType = record.inspectionType
      this.queryParam.id = record.id
      this.queryParam.exclusionIds =idList
      this.queryParam.productId = undefined
      this.queryParam.type = undefined
      this.taskId = record.id
      this.currentValue = undefined
      this.productId = undefined
      this.type = undefined
      this.onClearSelected()
      this.visible = true
      this.setcolumns(record.inspectionType)
      this.loadData(1)
    },
    onClearSelected() {
      this.isSelectAll = false
      this.selectedRowKeys = []
      this.selectionRows = []
    },
    setcolumns(inspectionType) {
      if (inspectionType == '1') {
        this.columns = [{
          title: '设备名称',
          align: 'center',
          dataIndex: 'name'
        },
          {
            title: '产品名称',
            align: 'center',
            dataIndex: 'categoryName'
          },
          {
            title: 'IP',
            align: 'center',
            dataIndex: 'ip'
          }
        ]
      } else if (inspectionType == '2') {
        this.columns = [{
          title: '应用名称',
          align: 'center',
          dataIndex: 'name'
        }]
      }
    },
    close() {
      this.visible = false
    },
    handleOk() {
      const that = this
      if(that.dataSource.length===0){
        that.close()
        return
      }
      if (that.taskId == null) {
        if (!this.isSelectAll) {
          that.$emit('ok', that.selectedRowKeys, that.selectionRows)
          that.close()
        }
        else {
          that.confirmLoading=true
          that.loadAllData().then((res)=>{
            if(res.success){
              that.$emit('ok', res.keys, res.rows)
              that.close()
            }
            that.confirmLoading=false
          }).catch((err)=>{
            that.$message.warning(err.message)
            that.confirmLoading=false
            return
          })
        }
      } else {
        if (that.selectedRowKeys.length == 0) {
          that.$message.warning('请选择要添加的设备!')
        }
        else {
          that.confirmLoading=true
          let ids = ''
          if (!this.isSelectAll) {
            ids=that.selectedRowKeys.join(',')
          }
          let param = {
            id: this.taskId,
            inspectionType: this.inspectionType,
            isSelectAll: this.isSelectAll,
            deviceId: ids,
            productId: this.productId,
            type: this.type,
            topInfoName: this.queryParam.topoName
          }
          postAction(this.url.selectedRowsList, param).then((res) => {
            if (res.success) {
              // that.$message.success("添加成功!");
              that.$emit('loadTableData')
              that.close()
            } else {
              this.$message.warning(res.message)
            }
            that.confirmLoading=false
          }).catch((err)=>{
            this.$message.warning(err.message)
            that.confirmLoading=false
          })
        }
      }
    },
    handleCancel() {
      this.close()
    }
  }
}
</script>

<style lang='less' scoped>
@import '~@assets/less/normalModal.less';

::v-deep .ant-table-wrapper .ant-spin-nested-loading {
  padding-right: 0px;
}
</style>