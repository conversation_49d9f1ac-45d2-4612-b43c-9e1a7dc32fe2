<template>
  <card-frame :title='"终端设备告警量TOP5"' :sub-title='""'>
    <div slot='bodySlot' class='empty-wrapper' v-if='alarmData.length===0'>
      <a-spin :spinning='loading' v-if='loading' class='spin'></a-spin>
      <a-list :data-source='[]' v-else />
    </div>
    <div slot='bodySlot' id='home-device-alarm' v-else>
      <device-alarm-top5 class='chart' :chart-data='alarmData' :unit='"个"' :tipDescription='"告警数量"'></device-alarm-top5>
    </div>
  </card-frame>
</template>
<script>
import { getAction } from '@api/manage'
import cardFrame from '@views/statsCenter/com/cardFrame.vue'
import deviceAlarmTop5 from '@views/statsCenter/homepageStatistics/modules/barTop.vue'
export default {
  name: "deviceAlarm",
  props: {
    adcode: {
      type: String,
      required: false,
      default: ''
    },
    handleRefresh: {
      type: Number,
      required: false,
      default: 0,
    }
  },
  components: {
    cardFrame,
    deviceAlarmTop5
  },
  data() {
    return {
      loading: false,
      alarmData: [],
      url: {
        alarmCount: '/data-analysis/index/terminalAlarmTop',
      }
    }
  },
  watch: {
    adcode: {
      handler(nVal, oVal) {
        //this.getAlarmCount(nVal)
        this.getMockJson()
      },
      deep: true,
      immediate: true
    },
    handleRefresh: {
      handler(nVal, oVal) {
        //this.getAlarmCount(this.adcode)
        this.getMockJson()
      }
    }
  },
  methods: {
    getAlarmCount(adcode) {
      this.loading = true
      this.alarmData = []
      getAction(this.url.alarmCount, { adCode: adcode, top: 5 }).then((res) => {
        this.alarmData = []
        if (res.success) {
          if (res.result.length > 0) {
            this.alarmData = res.result
          }
        } else {
          this.$message.warning(res.message)
        }
        this.loading = false
      }).catch((err) => {
        this.$message.warning(err.message)
        this.loading = false
      })
    },
    getMockJson(){
      this.loading = true
      this.alarmData = []
      getAction(location.origin+"/statsCenter/mock/homeData.json").then((res) => {
        if(res){
          this.alarmData =res.deviceAlarmData
        }
        this.loading = false
      }).catch((err)=>{
        this.loading = false
      })
    }
  }
}
</script>

<style scoped lang="less">
#home-device-alarm{
  height: 100%;

  .chart{
    height: 100%;
    //padding-top:0.4rem;//32/80
    padding-top:0.225rem;//18/80
  }
}
</style>