<template>
  <!-- cpu使用率情况 -->
  <div style="height: 100%;width: 100%;">
    <recent-time @changeType="changeType" :selectedIndex="selectedIndex" style="top: 50px;left:28px;"></recent-time>
    <div ref="baseEcharts" style="height: 100%;width: 70%;margin-left:25%"></div>
  </div>
</template>

<script>
import recentTime from './recentTime.vue'
import { chartMixins } from './chartMixins'
export default {
  name: 'cpuUsage',
  mixins: [chartMixins],
  components: {
    recentTime
  },
  props: {
    chartData: {
      type: Array,
      default: () => []
    },
    fontSizeObject: {
        type: Object,
        default: function () {
        return {
          legendFontSize: 8,
          xAxisFontSize: 8,
          yAxisFontSize: 10
        }
      }
    }
  },
  watch: {
    chartData: {
      handler(nVal, oVal) {
        this.$nextTick(() => {
          this.initData(nVal)
        })
      },
      deep: true,
      immediate: true
    }
  },
  data() {
    return {
      myChart: null,
      selectedIndex: 0
    }
  },
  methods: {
    changeType(index) {
      this.selectedIndex = index
      this.initData(this.chartData)
    },
    initData(data) {
      if (data.length <= 0) {
        return
      }
      let xData = []
      let yData = []
      let xAllData = []
      let yAllData = [] // 30日cpu使用量率数据
      data.map(item => {
        xAllData.push(item.time.substr(5, 10))
        yAllData.push(item.value)
      })
      if (this.selectedIndex == 0 && data.length > 6) {
        // 截取近七日数据
        xData = xAllData.slice(data.length - 7, data.length)
        yData = yAllData.slice(data.length - 7, data.length)
      } else {
        // 全部数据
        xData = xAllData
        yData = yAllData
      }
      this.myChart = this.$echarts.init(this.$refs.baseEcharts)
      this.myChart.clear()
      let option = {
        polar: {
          radius: [0, '80%']
        },
        radiusAxis: {
          zlevel: 3,
          axisLine: {
            show: true,
            lineStyle: {
              color: 'rgba(0, 0, 0, 0.15)'
            }
          },
          axisLabel: {
            show: true,
            textStyle: {
              fontSize: this.fontSizeObject.xAxisFontSize,
              color: 'rgba(0, 0, 0, 0.45)'
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: 'rgba(0, 0, 0, 0.15)',
              width: 1
            }
          }
        },
        angleAxis: {
          type: 'category',
          data: xData,
          zlevel: 2,
          startAngle: 90,
          axisLabel: {
            color: 'rgba(0, 0, 0, 0.45)', // 外环文字颜色
            fontSize: this.fontSizeObject.xAxisFontSize
          }
        },
        tooltip: {
          trigger: 'axis',
          formatter: function(params) {
            if (params[0].value !== '' && params[0].value !== null && params[0].value !== undefined) {
              return `${params[0].name}：${params[0].value}%`
            } else {
              return `${params[0].name}：无数据`
            }
          }
        },
        series: {
          type: 'bar',
          name: '磁盘使用情况',
          data: yData,
          coordinateSystem: 'polar',
          color: '#5B8FF9',
          zlevel: 1
        },
        animation: false
      }

      this.myChart.setOption(option)
    }
  }
}
</script>