<template>
<div class='zr-national-op-status'>
  <zr-bigscreen-title title='运行状态'></zr-bigscreen-title>
  <div class='op-status-content'>
    <div class='op-status-chart-box'>
      <polar-liquid-fill></polar-liquid-fill>
    </div>
  </div>
</div>
</template>
<script>
import ZrBigscreenTitle from '@views/zrBigscreens/modules/ZrBigscreenTitle.vue'
import PolarLiquidFill from '@views/zrBigscreens/zrEcharts/PolarLiquidFill.vue'
export default {
  name: 'ZrNationalOpStatus',
  components: { ZrBigscreenTitle,PolarLiquidFill },
  data() {
    return {
      // 这里可以添加需要的数据
    }
  },
  created() {
    // 组件创建时的逻辑
  },
  mounted() {
    // 组件挂载后的逻辑
  },
  beforeDestroy() {
    // 组件销毁前的逻辑
  },
  methods: {
    // 这里可以添加方法
  }
}
</script>

<style scoped lang='less'>
.zr-national-op-status{
  .op-status-content{
    padding: 28px;
    display: flex;
    justify-content: center;
    align-items: center;
    .op-status-chart-box{
      width: 206px;
      height: 206px;
      //background: #18456F;
    }
  }
}
</style>