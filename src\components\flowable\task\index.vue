<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
        <a-row :gutter="24" ref='row'>
          <a-col :offset="22" :span="2" justify="end" style="margin-bottom: 10px;">
            <a-button icon="rollback" style="margin-left: 8px" onClick="javascript :history.go(-1);">返回</a-button>
          </a-col>
          <a-col v-show="getVisible('processInstanceName')" :span="spanValue">
            <a-form-item :label="getTitle('processInstanceName')">
              <a-input :allow-clear='true' autocomplete='off' placeholder='请输入业务标题'
                v-model='queryParam.processInstanceName' />
            </a-form-item>
          </a-col>
          <a-col v-show="getVisible('name')" :span="spanValue">
            <a-form-item :label="getTitle('name')">
              <a-input :allow-clear='true' autocomplete='off' placeholder='请输入任务名称' v-model='queryParam.taskName' />
            </a-form-item>
          </a-col>
          <a-col v-show="getVisible('createTime')" :span="spanValue">
            <a-form-item :label="getTitle('createTime')">
              <a-range-picker style='width: 100%' :getCalendarContainer="node=> node.parentNode"
                format="YYYY-MM-DD HH:mm:ss" showTime v-model="queryParam.searchCreateTime"
                :placeholder="['开始时间', '结束时间']" @change="onCreteTimeChange" />
            </a-form-item>
          </a-col>
          <a-col v-show="getVisible('endTime')" :span="spanValue">
            <a-form-item :label="getTitle('endTime')">
              <a-range-picker style='width: 100%' :getCalendarContainer="node=> node.parentNode"
                format="YYYY-MM-DD HH:mm:ss" showTime v-model="queryParam.searchEndTime" :placeholder="['开始时间', '结束时间']"
                @change="onEndTimeChange" />
            </a-form-item>
          </a-col>
          <a-col v-show="getVisible('ownerName')" :span="spanValue">
            <a-form-item :label="getTitle('ownerName')">
              <a-select :getPopupContainer='node=>node.parentNode' :allow-clear='true' v-model="queryParam.taskOwner"
                show-search placeholder="请选择所有人" option-filter-prop="children" :filter-option="filterOption">
                <a-select-option v-for="(item, key) in userList" :key="key" :value="item.username">
                  <div style="display: inline-block; width: 70%" :title="item.realname">
                    {{ item.realname }}
                    <span style="font-size: 6px; color: rgba(0, 0, 0, 0.45);">{{
                        '(' + item.username + ')'
                      }}</span>
                  </div>

                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col v-show="getVisible('assigneeName')" :span="spanValue">
            <a-form-item :label="getTitle('assigneeName')">
              <a-select :getPopupContainer='node=>node.parentNode' :allow-clear='true' v-model="queryParam.taskAssignee"
                show-search placeholder="请选择执行人" option-filter-prop="children" :filter-option="filterOption">
                <a-select-option v-for="(item, key) in userList" :key="key" :value="item.username">
                  <div style="display: inline-block; width: 70%" :title="item.realname">
                    {{ item.realname }}
                    <span style="font-size: 6px; color: rgba(0, 0, 0, 0.45);">{{
                        '(' + item.username + ')'
                      }}</span>
                  </div>

                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="spanValue">
            <span class="table-page-search-submitButtons" style="overflow: hidden;">
              <a-button icon="search" type="primary" @click="searchQuery">查询</a-button>
              <a-button icon="reload" style="margin-left: 8px" @click="searchReset">重置</a-button>
              <a v-if="queryItems.length>0" style="margin-left: 8px" @click="doToggleSearch">{{queryName}}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
              </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!--自定义查询项 -->
    <div v-if="toggleSearchStatus" class="custom-query-item">
      <a-checkbox-group v-model="settingQueryItems" :defaultValue="settingQueryItems" style="width:100%"
        @change="onQuerySettingsChange">
        <a-row :gutter="24">
          <template v-for="(item,index) in queryItems">
            <a-col v-show='item.checked' :span='querySpanValue' class='col-checkbox'>
              <a-checkbox :disabled="item.disabled" :value="item.dataIndex">
                <j-ellipsis :length="7" :value="item.title"></j-ellipsis>
              </a-checkbox>
            </a-col>
          </template>
        </a-row>
      </a-checkbox-group>
    </div>
    <!-- 自定义查询项-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator" v-if="selectedRowKeys.length > 0">
      <a-dropdown>
        <a-menu slot="overlay" style='text-align: center'>
          <a-menu-item key="1" @click="batchDel">
            删除
          </a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px">
          批量操作
          <a-icon type="down" />
        </a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <a-table ref="table" size="middle" bordered rowKey="id" :columns="columns" :dataSource="dataSource"
        :pagination="ipagination" :loading="loading"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }" @change="handleTableChange">
        <div slot="filterDropdown">
          <a-card>
            <a-checkbox-group @change="onColSettingsChange" v-model="settingColumns" :defaultValue="settingColumns">
              <a-row style="width: 400px">
                <template v-for="(item, index) in defColumns">
                  <template v-if="item.key != 'rowIndex' && item.dataIndex != 'action'">
                    <a-col :span="12">
                      <a-checkbox :value="item.dataIndex">
                        <j-ellipsis :value="item.title" :length="10"></j-ellipsis>
                      </a-checkbox>
                    </a-col>
                  </template>
                </template>
              </a-row>
            </a-checkbox-group>
          </a-card>
        </div>
        <a-icon slot="filterIcon" type="setting" :style="{ fontSize: '16px', color: '#108ee9' }" />

        <span slot="action" slot-scope="text, record">
          <a @click="handleDetail(record)">查看</a>
          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多
              <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleInfo(record)">查看详情</a>
              </a-menu-item>
              <a-menu-item v-if="record.endTime == null && (record.assignee == null || record.assignee == '')">
                <a @click="btnClaim(record)">认领并执行</a>
              </a-menu-item>
              <a-menu-item v-if="record.endTime == null && record.assignee === $store.getters.userInfo.username">
                <a @click="btnUnclaim(record)">取消认领</a>
              </a-menu-item>
              <a-menu-item v-if="
                  record.endTime == null &&
                  record.assignee != null &&
                  record.assignee === $store.getters.userInfo.username">
                <a @click="btnExcuteTask(record)">执行</a>
              </a-menu-item>
              <a-menu-item v-if="record.endTime==null&&record.assignee==null">
                <a @click="assignment(record)">指定处理人</a>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>
      </a-table>
    </div>
    <!-- table区域-end -->

    <!-- 查看区域 -->
    <process-task-detail-modal ref="processTaskDetail" @ok="modalFormOk"></process-task-detail-modal>

    <!-- 一查看详情区域 -->
    <ProcessInstanceInfoModal ref="instanceInfoModalForm" @ok="modalFormOk"></ProcessInstanceInfoModal>

    <execute-task v-if="dialogExcuteTaskVisible" :visible.sync="dialogExcuteTaskVisible"
      :processInstanceId.sync="processInstanceId" :execute-task-id="executeTaskId" @ok="modalFormOk"></execute-task>

    <!--    选择用户选择区-->
    <j-select-user-by-dep-modal-enhance ref="selectModal" :modalWidth="1250" @ok="selectedUser" :multi="false">
    </j-select-user-by-dep-modal-enhance>
  </a-card>
</template>

<script>
  import {
    getUserList
  } from '@/api/api'
  import JSuperQuery from '@/components/jeecg/JSuperQuery.vue'
  import JInput from '@/components/jeecg/JInput.vue'
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import Vue from 'vue'
  import {
    filterObj
  } from '@/utils/util'
  import {
    putAction
  } from '@/api/manage'
  import ProcessTaskDetailModal from '../../../views/flowable/process-task/modules/ProcessTaskDetailModal.vue'
  import ProcessInstanceInfoModal from '../../../views/flowable/process-instance/modules/ProcessInstanceInfoModal'
  import ExecuteTask from '../../../views/flowable/task-todo/modules/executeTask.vue'
  import JSelectUserByDepModalEnhance from '@/components/flowable/modules/JSelectUserByDepModalEnhance'
  import {
    YqFormSeniorSearchLocation
  } from '@/mixins/YqFormSeniorSearchLocation'

  export default {
    name: 'processTask',
    mixins: [JeecgListMixin, YqFormSeniorSearchLocation],
    components: {
      JSuperQuery,
      JInput,
      ProcessTaskDetailModal,
      ProcessInstanceInfoModal,
      ExecuteTask,
      JSelectUserByDepModalEnhance,

    },
    data() {
      return {
        description: '单表示例列表',
        formItemLayout: {
          labelCol: {
            style: 'width:100px',
          },
          wrapperCol: {
            style: 'width:calc(100% - 100px)',
          },
        },
        //字典数组缓存
        sexDictOptions: [],
        importExcelUrl: `${window._CONFIG['domianURL']}/test/jeecgDemo/importExcel`,
        //列设置
        settingColumns: [],
        //列定义
        columns: [{
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            isUsed: false,
            customCell: () => {
              let cellStyle = 'text-align:center;width:60px'
              return {
                style: cellStyle
              }
            },
            customRender: function (t, r, index) {
              return parseInt(index) + 1
            },
          },
          {
            title: '业务标题',
            dataIndex: 'processInstanceName',
            isUsed: true,
            customCell: () => {
              let cellStyle = 'text-align:center'
              return {
                style: cellStyle
              }
            },
          },
          {
            title: '任务名称',
            dataIndex: 'name',
            isUsed: true,
            customCell: () => {
              let cellStyle = 'text-align:center'
              return {
                style: cellStyle
              }
            },
          },
          {
            title: '开始时间',
            dataIndex: 'createTime',
            isUsed: true,
            customCell: () => {
              let cellStyle = 'text-align:center;width:160px'
              return {
                style: cellStyle
              }
            },
          },
          {
            title: '结束时间',
            dataIndex: 'endTime',
            isUsed: true,
            customCell: () => {
              let cellStyle = 'text-align:center;width:160px'
              return {
                style: cellStyle
              }
            },
          },
          {
            title: '所有人',
            dataIndex: 'ownerName',
            isUsed: true,
            customCell: () => {
              let cellStyle = 'text-align:center'
              return {
                style: cellStyle
              }
            },
          },
          {
            title: '执行人',
            dataIndex: 'assigneeName',
            isUsed: true,
            customCell: () => {
              let cellStyle = 'text-align:center'
              return {
                style: cellStyle
              }
            },
          },
          {
            title: '操作',
            dataIndex: 'action',
            fixed: 'right',
            align: 'center',
            isUsed: false,
            width: 150,
            scopedSlots: {
              customRender: 'action',
            },
            /*scopedSlots: {
              filterDropdown: 'filterDropdown',
              filterIcon: 'filterIcon',
              customRender: 'action',
            },*/
          },
        ],
        url: {
          list: '/flowable/task/list',
          delete: '/test/jeecgDemo/delete',
          deleteBatch: '/test/jeecgDemo/deleteBatch',
          exportXlsUrl: '/test/jeecgDemo/exportXls',
        },
        executeTaskId: '',
        dialogExcuteTaskVisible: false,
        userList: []
      }
    },
    created() {
      this.getColumns(this.columns)
      this.getuserList()
    },
    methods: {
      changeStartUser(e, node) {
        if (!e) {
          this.queryParam.taskOwner = undefined
        }
      },
      changeAssigneeUser(e, node) {
        if (!e) {
          this.queryParam.taskAssignee = undefined
        }
      },
      assignment(record) {
        this.executeTaskId = record.id
        this.$refs.selectModal.showModal()
      },
      selectedUser(users) {
        putAction('/flowable/task/adminAssign', {
          taskId: this.executeTaskId,
          userId: users[0].username,
          message: '管理员分配处理人'
        }).then(({
          message
        }) => {
          this.$message.success(message)
          this.loadData()
        })

      },
      getuserList() {
        let param = {
          pageSize: 10000
        }
        getUserList(param).then((res) => {
          if (res.success) {
            this.userList = res.result.records
          }
        })
      },
      filterOption(input, option) {
        return (
          option.componentOptions.children[0].children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
        )
      },
      getQueryParams() {
        //高级查询器
        let sqp = {}
        if (this.superQueryParams) {
          sqp['superQueryParams'] = encodeURI(this.superQueryParams)
          sqp['superQueryMatchType'] = this.superQueryMatchType
        }
        if (this.$route.query && this.$route.query.processInstanceId) {
          this.queryParam.processInstanceId = this.$route.query.processInstanceId
        }
        var param = Object.assign(sqp, this.queryParam, this.isorter, this.filters)
        param.field = this.getQueryField()
        param.pageNo = this.ipagination.current
        param.pageSize = this.ipagination.pageSize
        delete param.birthdayRange //范围参数不传递后台
        return filterObj(param)
      },
      //查看
      handleDetail(record) {
        if (!record.id) {
          this.$message.error('任务ID不存在')
          return
        }
        this.$refs.processTaskDetail.init(record)
        this.$refs.processTaskDetail.title = '查看'
        this.$refs.processTaskDetail.disableSubmit = false
      },
      //查看详情
      handleInfo(record) {
        if (!record.id) {
          this.$message.error('任务ID不存在')
          return
        }
        this.$refs.instanceInfoModalForm.init(record.processInstanceId)
        this.$refs.instanceInfoModalForm.title = '查看'
        this.$refs.instanceInfoModalForm.disableSubmit = false
      },
      //认领
      btnClaim(record) {
        putAction('/flowable/task/claim', {
            taskId: record.id
          })
          .then((res) => {
            if (res.code == 200) {
              this.$message.success(res.message)
              this.loadData()
              this.btnExcuteTask(record)
            } else {
              this.$message.error(res.message)
            }
          })
          .catch((response) => {
            this.loadData()
          })
      },
      btnUnclaim(record) {
        putAction('/flowable/task/unclaim', {
          taskId: record.id
        }).then((res) => {
          this.$message.success(res.message)
          this.loadData()
        })
      },
      //执行
      btnExcuteTask(record) {
        if (!record.id) {
          this.$message.error('任务ID不存在')
          return
        }
        this.executeTaskId = record.id
        this.processInstanceId = record.processInstanceId
        this.dialogExcuteTaskVisible = true
      },

      onCreteTimeChange: function (value, dateString) {
        this.queryParam.taskCreatedAfter = dateString[0]
        this.queryParam.taskCreatedBefore = dateString[1]
      },
      onEndTimeChange: function (value, dateString) {
        this.queryParam.taskCompletedAfter = dateString[0]
        this.queryParam.taskCompletedBefore = dateString[1]
      },
      //列设置更改事件
      onColSettingsChange(checkedValues) {
        var key = this.$route.name + ':colsettings'
        Vue.ls.set(key, checkedValues, 7 * 24 * 60 * 60 * 1000)
        this.settingColumns = checkedValues
        const cols = this.defColumns.filter((item) => {
          if (item.key == 'rowIndex' || item.dataIndex == 'action') {
            return true
          }
          if (this.settingColumns.includes(item.dataIndex)) {
            return true
          }
          return false
        })
        this.columns = cols
      },
      initColumns() {
        //权限过滤（列权限控制时打开，修改第二个参数为授权码前缀）
        //this.defColumns = colAuthFilter(this.defColumns,'testdemo:');

        var key = this.$route.name + ':colsettings'
        let colSettings = Vue.ls.get(key)
        if (colSettings == null || colSettings == undefined) {
          let allSettingColumns = []
          this.defColumns.forEach(function (item, i, array) {
            allSettingColumns.push(item.dataIndex)
          })
          this.settingColumns = allSettingColumns
          this.columns = this.defColumns
        } else {
          this.settingColumns = colSettings
          const cols = this.defColumns.filter((item) => {
            if (item.key == 'rowIndex' || item.dataIndex == 'action') {
              return true
            }
            if (colSettings.includes(item.dataIndex)) {
              return true
            }
            return false
          })
          this.columns = cols
        }
      },
    },
  }
</script>
<style scoped lang='less'>
  @import '~@assets/less/common.less';
  @import '~@assets/less/YQCommon.less';
</style>