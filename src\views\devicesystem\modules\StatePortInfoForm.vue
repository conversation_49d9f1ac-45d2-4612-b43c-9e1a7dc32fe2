<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form
        :form="form"
        slot="detail"
        style="height: 550px; padding-right: 20px; overflow-y: auto; overflow-x: hidden"
      >
        <div class="colorBox">
          <span class="colorTotal">端口流量</span>
        </div>
        <div class="everyParticular" v-for="(item, index) in stateInfos" :key="index">
          <div><span>{{ item.portDesc.name }}</span><span>{{ item.portDesc.value }}</span></div>
          <div><span>{{ item.portStatus.name }}</span><span>{{ item.portStatus.value }}</span></div>
          <div><span>{{ item.bandWidth.name }}</span><span>{{ item.bandWidth.value }}</span></div>
          <div><span>{{ item.portType.name }}</span><span>{{ item.portType.value }}</span></div>
          <div><span>{{ item.inputFlow.name }}</span><span>{{ item.inputFlow.value }}</span></div>
          <div><span>{{ item.outputFlow.name }}</span><span>{{ item.outputFlow.value }}</span></div>
        </div>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import pick from 'lodash.pick'
import { validateDuplicateValue } from '@/utils/util'
import JFormContainer from '@/components/jeecg/JFormContainer'
import { getDeviceInfoMap } from '@/api/device'
export default {
  name: 'DeviceInfoForm',
  components: {
    JFormContainer,
    getAction,
  },
  props: {
    // 流程表单data
    formData: {
      type: Object,
      default: () => {},
      required: false,
    },
    // 表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false,
    },
    // 表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false,
    },
  },
  data() {
    return {
      form: this.$form.createForm(this),
      stateInfos: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 7 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 14 },
      },
      confirmLoading: false,
    }
  },
  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    },
  },
  created() {},
  watch: {},
  methods: {
    /*      modalFormOk() {
        // 新增/修改 成功时，重载列表
        // this.loadData();
      },
      //资产删除数据
      doDelete(record) {
        this.dataSource = []
      },
      //获取资产返回数据
      getTableData(data) {
        this.dataSource = data
      },
      //弹出资产选择页面
      handleAssets() {
        this.$refs.assetsForm.add()
        this.$refs.assetsForm.width = '1200px'
        this.$refs.assetsForm.title = '资产页面'
        this.$refs.assetsForm.disableSubmit = false
      },
      queryAllProduct() {
        getAction(this.url.queryAllProduct).then(res => {
          if (!!res) {
            this.productList = res
          }
        })
      },
      selDynamicTempByProId(e) {
        if (!!e) {
          let paramObj = {
            productId: e.trim()
          }
          getAction(this.url.queryRelaTemplate, paramObj).then(res => {
            if (!!res) {

              res.forEach(ele => {
                if (ele.defaultValue != null && ele.defaultValue.length > 0) {
                  ele.connectValue = ele.defaultValue
                }
              })
              this.collectInfoList = res
            }
          })
        }
      },
      selDynamicTempByDeviId(e) {
        if (!!e) {
          let paramObj = {
            deviceId: e.trim()
          }
          getAction(this.url.queryRelaTemplate, paramObj).then(res => {
            if (!!res) {
              this.collectInfoList = res
            }
          })
        }
      },*/
    add() {
      this.edit({})
    },
    edit(record) {
      this.stateInfos = record
    },
    //编辑设备是根据资产id查询关联资产
    getAssetsByAssetsId(id) {
      getAction(this.url.list, { id: id }).then((res) => {
        if (res.success) {
          this.dataSource = res.result.records
          //this.ipagination={};
          // this.dataSource = res.result.records;
          // this.ipagination={};
          // if (res.result.total) {
          //   this.ipagination.total = res.result.total
          // }
        }
      })
    },
    // 渲染流程表单数据
    showFlowData() {
      if (this.formBpm === true) {
        let params = { id: this.formData.dataId }
        getAction(this.url.queryById, params).then((res) => {
          if (res.success) {
            this.edit(res.result)
          }
        })
      }
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'post'
          }
          //参数模板数据拼装
          let paramTemplate = []
          that.collectInfoList.forEach((ele) => {
            paramTemplate.push({
              connectName: ele.connectName,
              connectValue: ele.connectValue,
            })
          })
          //设备数据
          let formData = Object.assign(this.model, values)
          formData.assetsId = that.dataSource.length > 0 ? this.dataSource[0].id : ''
          let info = {
            deviceInfo: formData,
            templateList: paramTemplate,
          }
          httpAction(httpurl, info, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    popupCallback(row) {
      this.form.setFieldsValue(pick(row, 'name', 'productId', 'categoryId', 'assetsId', 'deviceCode', 'description'))
    },
  },
}
</script>

<style lang="less" scoped="scoped">
.colorBox {
  margin-bottom: 18px;
}
.colorTotal {
  padding-left: 7px;
  border-left: 4px solid #1e3674;
}
.lineClass {
  border-top: 1px solid #000;
}
.ant-form-item {
  margin-bottom: 12px;
}
.btn-assets {
  width: 101px;
  height: 28px;
  background: #ecf5ff;
  border: 1px solid #b3d8ff;
  border-radius: 4px;
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: #409eff;
  margin-bottom: 15px;
}
.everyParticular {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 16px;
  border: 1px solid #e8e8e8;
  border-bottom: none;
  div {
    width: 50%;
    height: 40px;
    display: flex;
    border-bottom: 1px solid #e8e8e8;
    span:nth-child(1) {
      width: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #fafafa;
      border-right: 1px solid #e8e8e8;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    span:nth-child(2) {
      width: 50%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      padding:0 10px;
      display: flex;
      align-items: center;
      border-right: 1px solid #e8e8e8;
    }
  }
  div:nth-child(2n + 0) {
    span:nth-child(2) {
      border-right: none;
    }
  }
}
</style>
