<template>
  <div style="height: 100%">
    <keep-alive exclude='CmdbAssetsCategory1Details'>
      <component :is="pageName" :data="data"/>
    </keep-alive>
  </div>
</template>
<script>
  import CmdbAssetsCategory1List from './CmdbAssetsCategory1List'
  import CmdbAssetsCategory1Details from './modules/CmdbAssetsCategory1Details'
  export default {
    name: 'CmdbAssetsCategory1Manage',
    data() {
      return {
        isActive: 0,
        data: {}
      }
    },
    components: {
      CmdbAssetsCategory1List,
      CmdbAssetsCategory1Details,
    },
    created() {
      this.pButton1(0)
    },
    //使用计算属性
    computed: {
      pageName() {
        switch (this.isActive) {
          case 0:
            return 'CmdbAssetsCategory1List'
            break

          default:
            return 'CmdbAssetsCategory1Details'
            break
        }
      },
    },
    methods: {
      pButton1(index) {
        this.isActive = index
      },
      pButton2(index, item) {
        this.isActive = index
        this.data = item
      }
    },
  }
</script>