<template>
    <j-modal :title="type == 'edit' ? '编辑资产' : '查看资产'"
        :width="1000"
        :visible='visible'
        :maskClosable="false"
        :centered='true'
        :destroyOnClose="true"
        switchFullscreen
        :loading="loading"
        :confirmLoading="confirmLoading"
        @cancel="handleCancel">
        <template slot="footer">
            <a-button key="close"
                @click="handleCancel"> 关闭 </a-button>
            <a-button v-if="type == 'edit'" type="primary"
                style="margin-left: 5px"
                :loading="confirmLoading"
                @click="submit">
                确定
            </a-button>
        </template>
        <!-- 操作按钮区域 -->
        <div class="table-operator"
            v-if="type == 'edit'">
            <a-button @click="handleAdd1">新增资产</a-button>
            <a-button @click="chooseHasAssets">选择资产</a-button>
            <a-dropdown v-if="selectedRowKeys.length > 0">
                <a-menu slot='overlay' style='text-align: center'>
                  <a-menu-item key='1' @click='batchDel'>删除</a-menu-item>
                </a-menu>
                <a-button> 批量操作
                    <a-icon type='down' />
                </a-button>
            </a-dropdown>
            <!-- table区域-begin -->
        </div>
        <a-table ref="table"
            bordered
            rowKey="id"
            :columns="columns"
            :dataSource="dataSource"
            :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
            :pagination="false"
            :rowSelection='{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }'>
            <span slot="operateType"
                slot-scope="text">{{ text == 0 ? '新增' : '变更' }}</span>
            <span slot="action"
                slot-scope="text, record, index">
                <a @click="openCard(record)">查看</a>
                <span v-if="type == 'edit'">
                    <a-divider type="vertical" />
                    <a @click="handleAssetsUpdateEdit(record, index)">编辑</a>
                </span>
                <span v-if="type == 'edit'">
                    <a-divider type="vertical" />
                    <a-popconfirm title="确定删除吗?"
                        @confirm="() => handleDelete(record)"
                        placement="topLeft">
                        <a>删除</a>
                    </a-popconfirm>
                </span>
            </span>
            <template slot="tooltip"
                slot-scope="text">
                <a-tooltip placement="topLeft"
                    :title="text"
                    trigger="hover">
                    <div class="tooltip">
                        {{ text }}
                    </div>
                </a-tooltip>
            </template>
        </a-table>
        <!-- 新增/编辑资产弹框 -->
        <assets-modal ref="modalForm"
            :tempId="tempId"
            :assetsOperateType="assetsOperateType"
            @addAssetsChangeOk="AddAssetsChange"></assets-modal>
        <!-- 已有资产弹框 -->
        <select-assets-modal ref="selectAssetsModal"
            @selectFinished="selectOK"
            :tempId="tempId"></select-assets-modal>
        <!-- 查看资产详情弹框 -->
        <assets-update-modal ref="lookAssetsInfoModal"
            :data="newdata"
            :tempId="tempId"></assets-update-modal>
    </j-modal>
</template>

<script>
import uuidv1 from 'uuid/v1'
import { deleteAction, getAction, postAction, } from '@/api/manage'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import SelectAssetsModal from '@/views/cmdb/assets/assetsUpdate/SelectAssetsModal'
import AssetsUpdateModal from '@/views/cmdb/assets/assetsUpdate/AssetsUpdateModal'
export default {
    name: 'AssetsUpdateList',
    mixins: [JeecgListMixin],
    components: {
        uuidv1,
        SelectAssetsModal,
        AssetsUpdateModal,
        AssetsModal: () => import('@/views/cmdb/assets/modules/AssetsModal')
    },
    data() {
        return {
            type: 'scan', // 默认是0查看资产详情,1编辑资产
            visible: false,
            confirmLoading: false,
            tempId: '', // 临时资产变更id
            newdata: {},
            dataSource: [],
            assetsChangeList: [],
            loading: false,
            disableMixinCreated: true,
            columns: [
                {
                    title: '资产编号',
                    dataIndex: 'assetsCode',
                    customCell: () => {
                        let cellStyle = 'text-align: center'
                        return {
                            style: cellStyle,
                        }
                    },
                },
                {
                    title: '资产名称',
                    dataIndex: 'assetsName',
                    customCell: () => {
                        let cellStyle = 'text-align: center'
                        return {
                            style: cellStyle,
                        }
                    },
                },
                {
                    title: '资产类型',
                    dataIndex: 'assetsCategoryText',
                    customCell: () => {
                        let cellStyle = 'text-align: center'
                        return {
                            style: cellStyle,
                        }
                    },
                },
                {
                    title: '供应商',
                    dataIndex: 'producerName',
                    customCell: () => {
                        let cellStyle = 'text-align: center'
                        return {
                            style: cellStyle,
                        }
                    },
                },
                {
                    title: '型号',
                    dataIndex: 'assetsModel',
                    customCell: () => {
                        let cellStyle = 'text-align: center'
                        return {
                            style: cellStyle,
                        }
                    },
                },
                {
                    title: '资产状态',
                    dataIndex: 'statusName',
                    customCell: () => {
                        let cellStyle = 'text-align: center'
                        return {
                            style: cellStyle,
                        }
                    },
                },
                {
                    title: '入库日期',
                    dataIndex: 'storageTime',
                    customRender: function (text) {
                        return !text ? '' : text.length > 10 ? text.substr(0, 10) : text
                    },
                    customCell: () => {
                        let cellStyle = 'text-align: center'
                        return {
                            style: cellStyle,
                        }
                    },
                },
                {
                    title: '操作类型',
                    dataIndex: 'operateType',
                    customCell: () => {
                        let cellStyle = 'text-align: center'
                        return {
                            style: cellStyle,
                        }
                    },
                    scopedSlots: {
                        customRender: 'operateType'
                    }
                },
                {
                    title: '操作',
                    dataIndex: 'action',
                    align: 'center',
                    fixed: 'right',
                    width: 147,
                    scopedSlots: {
                        customRender: 'action',
                    },
                },
            ],
            url: {
                list: '/device/deviceInfoAct/listByTempProcessInstanceId',
                deleteBatch: '/device/deviceInfoAct/deleteBatch',
            },
            assetsOperateType: "" // 资产操作类型
        }
    },
    methods: {
        // 获取tempId ,没有需要前端新生成一个uuid
        init(e) {
            this.visible = true
            this.type = e
            this.getTempId();
        },
        getTempId() {
            console.log(this.tempId, 'tempId')
            if (!!this.tempId) {
                // 查资产临时表
                this.loadData(this.tempId)
            } else {
                this.tempId = uuidv1();
            }
        },
        // 获取列表信息 将要展示的附加字段添加到列表数据中
        loadData() {
            if (!this.tempId) {
                return
            }
            this.loading = true
            getAction(this.url.list + `?tempProcessInstanceId=${this.tempId}`, {}).then((res) => {
                if (res.success) {
                    this.dataSource = res.result.records || res.result
                    if (this.dataSource.length < 9) {
                        this.clientHeight = false
                    }
                }
                if (res.code === 510) {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.loading = false
            })
        },
        AddAssetsChange(data) {
            console.log(data, '新增的数据2')
            // 新增成功时，重载列表
            this.loadData();
        },
        handleAdd1: function () {
            // 新增临时表资产
            this.assetsOperateType = 'addTempAssets'
            this.$refs.modalForm.add();
            this.$refs.modalForm.title = '新增';
            this.$refs.modalForm.disableSubmit = false;
        },
        handleAssetsUpdateEdit: function (record) {
            // 编辑临时表资产
            this.assetsOperateType = 'editTempAssets'
            this.$refs.modalForm.edit(record);
            this.$refs.modalForm.title = '编辑';
            this.$refs.modalForm.disableSubmit = false;
        },
        chooseHasAssets() {
            // 弹出资产选择页面
            this.$refs.selectAssetsModal.visible = true
        },
        openCard(record) {
            // 查看资产详情
            this.$refs.lookAssetsInfoModal.newdata = record;
            this.$refs.lookAssetsInfoModal.init();
        },
        batchDel: function () {
            if (this.selectedRowKeys.length <= 0) {
                this.$message.warning('请选择一条记录！')
                return
            } else {
                var ids = ''
                for (var a = 0; a < this.selectedRowKeys.length; a++) {
                    ids += this.selectedRowKeys[a] + ','
                }
                var that = this
                this.$confirm({
                    title: '确认删除',
                    okText: '是',
                    cancelText: '否',
                    content: '是否删除选中数据?',
                    onOk: function () {
                        that.loading = true
                        deleteAction(that.url.deleteBatch, { ids: ids, tempProcessInstanceId: that.tempId })
                            .then((res) => {
                                if (res.success) {
                                    that.$message.success(res.message)
                                    that.loadData()
                                    that.onClearSelected()
                                } else {
                                    that.$message.warning(res.message)
                                }
                            })
                            .finally(() => {
                                that.loading = false
                            })
                    }
                })
            }
        },
        handleDelete(record) {
            deleteAction(this.url.deleteBatch, { ids: record.id, tempProcessInstanceId: this.tempId })
                .then((res) => {
                    if (res.success) {
                        this.$message.success(res.message)
                        this.loadData()
                    } else {
                        this.$message.warning(res.message)
                    }
                })
        },
        selectOK(data) {
            // 添加已有资产
            console.log(data, '选中的资产')
            this.loadData()
        },
        submit() {
            if (this.dataSource.length == 0) {
                return this.$message.warning('请添加一条数据！')
            } else {
                this.$emit('setAssetsChange', this.dataSource, this.tempId)
                this.handleCancel();
            }
        },
        handleCancel() {
            this.$emit('setAssetsChange', this.dataSource, this.tempId)
            this.visible = false
        },
    }
}

</script>
<style lang='less' scoped>@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';</style>
