<template>
  <div ref="handleTopHistogram"></div>
</template>

<script>
import echarts from 'echarts'

export default {
  name: 'columnChart',
  props: {
    unit: {
      type: String,
      default: ''
    },
    title: '',
    chartData: {
      type: Array,
      default: () => []
    }
  },
  watch: {
    chartData: {
      handler(nVal, oVal) {
        this.$nextTick(() => {
          this.handleTopHistogram(nVal)
        })
      },
      deep: true,
      immediate: true
    }
  },
  data() {
    return {
      myChart: null
    }
  },
  methods: {
    handleTopHistogram(data) {
      if (data.length <= 0) {
        return
      }
      let xData = data.map(item => item.name)
      let yData = data.map(item => item.value)
      this.myChart = this.$echarts.init(this.$refs.handleTopHistogram)
      this.myChart.clear()
      this.myChart.setOption({
        grid: {
          left: '5%',
          bottom: '0',
          top: '6%',
          containLabel: false
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'none'
          },
          formatter: '{b}：{c}' + this.unit
        },
        xAxis: {
          splitLine: { show: false },
          axisLabel: { show: false },
          axisTick: { show: false },
          axisLine: { show: false },
          type: 'value'
        },
        yAxis: [
          {
            type: 'category',
            inverse: true, //排序
            axisLabel: {
              show: true,
              textStyle: {
                color: '#C9D2FA',
                fontSize: 16
              }
            },
            splitLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            },
            show: false,
            data: xData
          }
        ],
        series: [
          {
            type: 'bar',
            zlevel: 1,
            barWidth: 4,
         /*   itemStyle: {
              borderRadius: 4,
              color: '#03FFFF'
            },*/
            label: {
              show: true,
              normal: {
                show: true,
                position: 'right',
                padding: [0, 10, 0, 5],
                textStyle: {
                  fontSize: 14,
                  color: '#fff'
                },
                formatter: '{c}' + ' ' + this.unit
              }
            },
            labelLine: {
              show: false
            },
            data: yData,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  1,
                  0,
                  [
                    {
                      offset: 0,
                      color: '#01FFFF' // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: '#0281FF' // 100% 处的颜色
                    }
                  ],
                  false
                )
              }
            }
          },
          {
            // 顶部圆点
            type: 'scatter',
            emphasis: {
              scale: false
            },
            // xAxisIndex: 1,
            symbolSize: 7,
            symbolOffset: [0, 0],
            itemStyle: {
              color: '#0281FF',
              borderWidth: 1,
              opacity: 1
            },
            // z: 2,
            data: yData
          },
          {
            // 配置label标签
            z: 1,
            show: true,
            type: 'bar',
            // xAxisIndex: 1,
            barGap: '-100%',
            barWidth: 4,
            itemStyle: {
              borderRadius: 4,
              color: 'transparent'
            },
            label: {
              show: true,
              align: 'left',
              verticalAlign: 'bottom',
              position: 'left',
              fontSize: 12,
              color: '#fff',
              padding: [0, 0, 8, 10],
              formatter: function(data) {
                return xData[data.dataIndex]
              }
            },
            data: yData
          }
        ]
      })
      window.addEventListener('resize', this.changeResize)
    },
    changeResize() {
      if (this.myChart){
        this.myChart.resize()
      }
    }
  },
  destroyed() {
    if (this.myChart) {
      window.removeEventListener('resize', this.changeResize)
    }
  }
}
</script>

<style scoped lang="less">
</style>