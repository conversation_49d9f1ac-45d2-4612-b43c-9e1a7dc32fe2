<template>
  <yq-modal
    :title="title"
    :width="width"
    :visible="visible"
    :destroyOnClose="true"
    switchFullscreen
    @ok="handleOk"
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }"
    @cancel="handleCancel"
    cancelText="关闭"
  >
    <question-Info ref="realForm" @ok="submitCallback" :data="question" :disabled="disableSubmit"></question-Info>
  </yq-modal>
</template>

<script>
import QuestionInfo from './QuestionInfo'
import { httpAction, getAction, deleteAction } from '@/api/manage'
export default {
  inject: ['reload'],
  name: 'QuestionInfoModal',
  data() {
    return {
      title: '',
      width: 800,
      record: {},
      status: '',
      visible: false,
      disableSubmit: false,
      imgStatus: '../../../assets/img/red-circle.png', //状态标识图标
      closable: false, //不显示右上方关闭按钮
      question: {},
    }
  },
  props: {
    data: {
      type: Object,
    },
  },
  components: {
    QuestionInfo,
  },
  mounted() {
  },
  methods: {
    submitCallback() {
      this.$emit('ok')
      this.visible = false
    },
    handleCancel() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      this.$emit('close')
      this.visible = false
    },
    show(questionId) {
      getAction('/question/question/queryById', {id : questionId}).then((res) => {
        if (res.success) {
          this.question = res.result
          //先数据赋值，再展示弹窗
          this.visible = true
        }
      })
    },
    //返回上一级
    getGo() {
      this.$parent.pButton2(0)
    },
  },
}
</script>

<style scoped >
.ant-modal-body {
  background-color: #eee;
}
.div-info {
  position: relative;
  background-color: white;
  height: 117px;
  margin-bottom: 16px;
  padding: 10px 0 0 24px;
  border-radius: 3px;
}
.cls-div {
  position: absolute;
  top: 6px;
  right: 11px;
  cursor: pointer;
}
.p-info-title {
  line-height: 45px;
  height: 45px;
  margin-bottom: 0px;
  font-family: PingFangSC-Medium;
  font-size: 18px;
  color: #000000;
}
.p-info-product {
  line-height: 45px;
  height: 45px;
  margin-bottom: 0px;
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
}
.span-assets {
  margin-left: 119px;
}
.span-status {
  margin-left: 66px;
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
}
.span-status > img {
  width: 6px;
  height: 6px;
}
.status {
  padding-left: 7px;
}
.alarm-info {
  background-color: white;
  height: calc(100% - 135px);
  padding: 25px;
  overflow-y: auto;
}
</style>
