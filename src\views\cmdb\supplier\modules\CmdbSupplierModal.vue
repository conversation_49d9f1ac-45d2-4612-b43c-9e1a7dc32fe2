<template>
  <j-modal
    :title="title"
    :centered='true'
    :width="width"
    :visible="visible"
    switchFullscreen
    @ok="handleOk"
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @cancel="handleCancel"
    cancelText="关闭">
    <cmdb-supplier-form ref="realForm" @ok="submitCallback" :disabled="disableSubmit"></cmdb-supplier-form>
  </j-modal>
</template>

<script>

  import CmdbSupplierForm from './CmdbSupplierForm'
  export default {
    name: 'CmdbSupplierModal',
    components: {
      CmdbSupplierForm
    },
    data () {
      return {
        title:'',
        width:800,
        visible: false,
        disableSubmit: false
      }
    },
    methods: {
      add () {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.add();
        })
      },
      edit (record) {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.edit(record);
        })
      },
      close () {
        this.$emit('close');
        this.visible = false;
      },
      handleOk () {
        this.$refs.realForm.submitForm();
      },
      submitCallback(){
        this.$emit('ok');
        this.visible = false;
      },
      handleCancel () {
        this.close()
      }
    }
  }
</script>
<style lang="less" scoped>
@import '~@assets/less/normalModal.less';
</style>