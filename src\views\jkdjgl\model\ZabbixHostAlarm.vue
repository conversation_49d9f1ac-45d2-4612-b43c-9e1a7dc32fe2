<template>
  <div>
    <a-table ref="table" bordered :rowKey="(record,index)=>{return record.id}" :columns="columns"
             :dataSource="dataSource" :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}" :pagination="ipagination"
             :loading="loading" @change="handleTableChange">
      <!-- 字符串超长截取省略号显示-->
      <template slot="index" slot-scope="text,record,index">
        <span>{{index+1}}</span>
      </template>
      <template slot="severity" slot-scope="text,record,index">
        <span>{{severitys[text]}}</span>
      </template>
      <span slot="templateContent" slot-scope="text">
            <j-ellipsis :value="text" :length="25" />
          </span>
      <template slot="tooltip" slot-scope="text">
        <a-tooltip placement="topLeft" :title="text" trigger="hover">
          <div class='tooltip'>
            {{ text }}
          </div>
        </a-tooltip>
      </template>
    </a-table>
  </div>
</template>
<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'

export default {
  name: 'ZabbixHostAlarm',
  components: {  },
  mixins: [JeecgListMixin, YqFormSearchLocation],
  props:{
    record:{
      type:Object,
      default:()=>{return {}},
      required:true,
    }
  },
  data(){
    return{
      columns: [
        {
          title: '序号',
          dataIndex: 'index',
          scopedSlots: {
            customRender: 'index'
          },
          width: 80,
          customCell: () => {
            let cellStyle = 'text-align: center'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '名称',
          dataIndex: 'name',
          scopedSlots: {
            customRender: 'name'
          },
          customCell: () => {
            let cellStyle = 'text-align: center'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '级别',
          dataIndex: 'severity',
          scopedSlots: {
            customRender: 'severity'
          },
        },
        {
          title: '告警状态',
          dataIndex: 'status',
          scopedSlots: {
            customRender: 'status'
          },
        },
        {
          title: '触发时间',
          dataIndex: 'clockTime',
          customCell: () => {
            let cellStyle = 'text-align: center'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '恢复时间',
          dataIndex: 'rClockTime',
          customCell: () => {
            let cellStyle = 'text-align: center'
            return {
              style: cellStyle
            }
          }
        },{
          title: '持续时长',
          dataIndex: 'duration',
          customCell: () => {
            let cellStyle = 'text-align: center'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '确认状态',
          dataIndex: 'acknowledged',
          scopedSlots: {
            customRender: 'acknowledged'
          },
        },
        // {
        //   title: '操作',
        //   align: 'center',
        //   width: 180,
        //   fixed: 'right',
        //   dataIndex: 'action',
        //   scopedSlots: {
        //     customRender: 'action'
        //   },
        // },
      ],
      url: {
        list: '/device/deviceInfo/getZabbixProblemByHostId',
      },
      disableMixinCreated:true,
      severitys: {
        '0': '未分类',
        '1': '信息',
        '2': '警告',
        '3': '一般',
        '4': '高',
        '5': '灾难',
      }
    }
  },
  created() {
    this.queryParam.hostId = this.record.hostid;
    this.loadData()
  },
  mounted() {
  },
  methods:{
    alarmClick(record){
      this.$refs.jkdjDeviceAlarm.show(record)
    }
  }
}
</script>



<style scoped lang='less'>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';

/deep/.ant-table-tbody .ant-table-row td {
  padding-top: 5px;
  padding-bottom: 5px;
}
</style>