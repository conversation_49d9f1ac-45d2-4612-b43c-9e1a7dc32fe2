
<template>
  <div class="ripple-container" @click="triggerRipple">
    <div v-for="(wave, index) in waves"
         :key="index"
         class="ripple-wave"
         :style="wave.style"></div>
    <slot></slot>
  </div>
</template>

<script>
export default {
  data() {
    return {
      waves: [],
      counter: 0
    }
  },
  methods: {
    triggerRipple(event) {
      const rect = this.$el.getBoundingClientRect()
      const size = Math.max(rect.width, rect.height)
      const x = event.clientX - rect.left
      const y = event.clientY - rect.top

      this.waves.push({
        style: {
          width: `${size}px`,
          height: `${size}px`,
          left: `${x - size/2}px`,
          top: `${y - size/2}px`,
          opacity: 1
        },
        key: this.counter++
      })

      setTimeout(() => {
        this.waves.shift()
      }, 1000)
    }
  }
}
</script>

<style>
.ripple-container {
  position: relative;
  overflow: hidden;
  display: inline-block;
}

.ripple-wave {
  position: absolute;
  border-radius: 50%;
  background-color: rgba(24, 144, 255, 0.3);
  transform: scale(0);
  animation: ripple 1s linear;
}

@keyframes ripple {
  to {
    transform: scale(2);
    opacity: 0;
  }
}
</style>
