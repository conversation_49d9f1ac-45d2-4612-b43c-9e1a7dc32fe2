<template>
  <div style="height: 100%;width: 100%;">
    <card-frame :title="'人工服务'" :showHeadBgImg="true" :showFooter="true">
      <div class="artificial-service" slot="bodySlot">
        <div class="form-box">
          <a-form :form="form" @submit="handleSubmit" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
            <a-row type="flex" justify="center" align="top">
              <a-col :span="24">
                <a-form-item label="问题类型" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
                  <a-select v-decorator="['questionType', { rules: [{ required: true, message: '请选择问题类型' }] }]"
                    placeholder="请选择问题类型" :getPopupContainer="(node) => node.parentNode">
                    <a-select-option v-for="(item, kk) in dataType" :key="kk" :value="item.id">
                      {{ item.label }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item label="联系人" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" >
                  <a-input disabled v-decorator="[
                  'quizzer',
                  { initialValue: quizzer, rules: [{ required: true, message: '请输入联系人' }] },
                ]" />
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item :label="`部\u3000门`" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
                  <a-select v-decorator="['company', { rules: [{ required: true, message: '请选择部门' }] }]"
                            placeholder="请选择部门" :getPopupContainer="(node) => node.parentNode"  allowClear>
                    <a-select-option v-for="(item, kk) in departOptions" :key="kk" :value="item.value">
                      {{ item.label }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item label="联系方式" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
                  <a-input disabled v-decorator="[
                  'contact',
                  {
                    initialValue: contact,
                    rules: [{ required: true, message: '请输入联系人电话' }, { validator: this.phone }],
                  },
                ]" />
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item :label="'地\u3000址'" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
                  <a-input v-decorator="['region', validatorRules.region]" :allowClear="true" autocomplete="off"
                    placeholder="请输入地址"></a-input>
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item label="提交问题" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
                  <a-textarea
                    v-decorator="['question', { rules: [{ required: true, message: '请输入问题描述' }] }]"
                    :rows="8"
                    style="width: 100%"
                    placeholder="如果您想对我们的产品提出咨询或需求帮助，请在这里填写，运维人员收到后将为您提供人工服务！"
                    :maxLength="500"
                    @input="descInput"   />
                  <span
                    style="position: absolute; right: 10px; bottom: -1vh; font-size: 12px; color: rgba(250,250,250,.7)">{{ remnant }}/500</span>
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item :wrapper-col="{ span: 12, offset: 5 }">
                  <a-button type="primary" html-type="submit" style="
                  margin-left: 54%;
                  margin-top: 60px;
                  height: 32px;
                  width: 106px;
                  background: #409eff;
                  letter-spacing: 2px;
                  font-size: 14px;
                  font-weight: 400;
                ">
                    提交反馈
                  </a-button>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </div>
        <div class="waiter">
          <img src="/oneClickHelp/waiter.png" alt="" />
        </div>
        <div class="waiterBottom">
          <img src="/oneClickHelp/waiterBottom.png" alt="" />
        </div>
      </div>
    </card-frame>
  </div>
</template>
<script>
  import {
    httpAction,
    getAction,
    deleteAction
  } from '@/api/manage'
  import YqAreaCascaderSelect from '@/components/areaDict/YqAreaCascaderSelect'
  import cardFrame from '../oneClickHelp/localDeviceInfo/modules/CardFrame.vue'
  import {
    phoneValidator
  } from '@/mixins/phoneValidator'
  import {
    getHostNameLocal
  } from '@/utils/util'
  import { mapGetters } from 'vuex'
  export default {
    components: {
      YqAreaCascaderSelect,
      cardFrame
    },
    mixins: [phoneValidator],
    name: 'artificialServices',

    data() {
      return {
        quizzer: '',
        contact: '',
        region: '',
        cityId: '',
        desc: '',
        form: this.$form.createForm(this, {
          name: 'coordinated'
        }),
        dataType: [],
        model: {},
        url: {
          getDictCodeChild: '/sys/dict/getDictCodeChild',
          list: '/question/question/listOften1',
          add: '/question/question/add',
          getUserInfo: '/sys/user/getInfo',
        },
        validatorRules: {
          quizzer: {
            rules: [{
              required: true,
              message: '请输入联系人姓名'
            }],
          },
          region: {
            rules: [{
              required: true,
              message: '请输入地址'
            }],
          },
        },
        remnant: 500,
        departOptions: [],
      }
    },
    created() {
      this.initData()
      this.getUserDeparts()
      this.getDict()
    },
    mounted() {},
    methods: {
      ...mapGetters([ 'userInfo',"departs"]),
      getUserDeparts() {
        getAction("/sys/user/getCurrentUserDeparts").then((res) => {
          if (res.success) {
            let departs =res.result.list
            this.departOptions = departs.map(el=>{
              return {
                label:el.departName,
                value:el.id,
              }
            })
          }
        })
      },
      initData() {
        let userInfo = this.userInfo()
         this.quizzer = userInfo.realname
         this.contact = userInfo.phone
      },
      getDict() {
        let params = {
          dictCode: 'serviceRequestClass',
        }
        getAction(this.url.getDictCodeChild, params).then((res) => {
          if (res.success) {
            this.dataType = res.result
            this.firstId = this.dataType[0].id
          }
        })
      },
      descInput(e) {
        this.remnant = 500 - e.target.value.length
      },
      getSubmit() {},
      handleSubmit(e) {
        e.preventDefault()
        let hostName = getHostNameLocal()
        if (hostName === null) {
          this.$message.warning("请检查是否已经添加终端信息？")
          return;
        }
        this.form.validateFields((err, values) => {
          if (!err) {
            var that = this
            let method = 'post'
            let formData = Object.assign(this.model, values)
            formData.ip = hostName
            httpAction(that.url.add, formData, method)
              .then((res) => {
                if (res.success) {
                  this.form.resetFields()
                  that.$message.success(res.message)
                } else {
                  this.form.resetFields()
                  that.$message.warning(res.message)
                }
              })
              .finally(() => {
                that.confirmLoading = false
                this.form.resetFields()
              })
          }
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  @import '~@assets/less/onclickStyle.less';

  /deep/ .ant-form-item-label>label {
    font-size: 16px;
  }

  /deep/ .ant-select-selection {
    border: 1px solid #144E90;
  }

  /deep/ .ant-input {
    background-color: rgba(255, 255, 255, 0);
    color: rgba(255, 255, 255, .7);
    border: 1px solid #144E90;
  }

  .artificial-service {
    background-image: url('../../assets/img/yunweiBackground.png');
    background-size: 100%;
    background-repeat: no-repeat;
    background-color: #091425;
    height: 100%;
    width: 100%;
    display: flex;
    padding-right: 50px;
    flex-direction: column;
    align-items: center;
    position: relative;
  }

  .form-box {
    width: 50%;
    height: calc(100% - 50px);
    padding-top: 60px;
  }

  .waiter {
    position: absolute;
    bottom: 115px;
    right: 45px;
  }

  .waiterBottom {
    position: absolute;
    bottom: 0;
    right: 45px;
  }

  /deep/ .submitQuestions {
    width: 100%;

    div {
      .ant-form-item-label {
        width: 9.2% !important;
      }
    }
  }

  ::v-deep .ant-form-item-required {
    color: rgba(0, 0, 0, 0.65);
  }
</style>