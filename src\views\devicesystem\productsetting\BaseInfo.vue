<template>
  <div>
    <table class="gridtable">
      <tr>
        <td class="leftTd">标识</td>
        <td class="rightTd">{{ record.name }}</td>
        <td class="leftTd">产品分类</td>
        <td class="rightTd">{{ record.assetsCategoryName }}</td>
      </tr>
      <tr >
        <td class="leftTd">传输协议</td>
        <td class="rightTd">
          <span>{{ modeProtocol.toString() }}</span>
        </td>
        <td class="leftTd">采集模式</td>
        <td class="rightTd">{{ collectTypes.toString() }}</td>
      </tr>
<!--      <tr v-if="pullModeProtocol.length > 0">-->
<!--        <td class="leftTd">传输协议</td>-->
<!--        <td class="rightTd">-->
<!--          <span>{{ pullModeProtocol.toString() }}</span>-->
<!--        </td>-->
<!--        <td class="leftTd">采集模式</td>-->
<!--        <td class="rightTd">拉模式</td>-->
<!--      </tr>-->
      <tr>
        <td class="leftTd">是否上线</td>
        <td class="rightTd"><span v-if="record.isOnline == '0'">是</span> <span v-else>否</span></td>
        <td class="leftTd">支持型号</td>
        <td class="rightTd">{{ record.supportModel }}</td>
      </tr>
      <tr>
        <td class="leftTd">描述</td>
        <td class="rightTd" colspan='3'>{{ record.remark }}</td>
      </tr>
    </table>
  </div>
</template>

<script>
export default {
  data() {
    return {
      record: {},
      pushModeProtocol: [],
      pullModeProtocol: [],
      modeProtocol: [],
      collectTypes:[],
    }
  },
  methods: {
    show(record) {
      this.record = record
      this.pushModeProtocol = []
      this.pullModeProtocol = []
      this.modeProtocol = []
      this.collectTypes = [];
      if (record.selectedProtocolList?.length > 0) {
        for (let i = 0; i < record.selectedProtocolList.length; i++) {
          this.modeProtocol.push(record.selectedProtocolList[i].transferProtocol)
          if (record.selectedProtocolList[i].collectType == '1' && !this.collectTypes.includes("拉模式")){
            this.collectTypes.push("拉模式")
          }else if (record.selectedProtocolList[i].collectType == '0' && !this.collectTypes.includes("推模式")){
            this.collectTypes.push("推模式")
          }else if (record.selectedProtocolList[i].collectType == '2' && !this.collectTypes.includes("其他")){
            this.collectTypes.push("其他")
          }
          // if (record.selectedProtocolList[i].collectType == '1') {
          //   this.pullModeProtocol.push(record.selectedProtocolList[i].transferProtocol)
          //   continue
          // }
          // this.pushModeProtocol.push(record.selectedProtocolList[i].transferProtocol)
        }
      }
    },
  },
}
</script>

<style scoped>
table.gridtable {
  font-family: verdana, arial, sans-serif;
  font-size: 14px;
  color: #606266;
  border-width: 1px;
  border-color: #e8e8e8;
  border-collapse: collapse;
  text-align: left;
  width: 100%;
}
table.gridtable td {
  border-width: 1px;
  border-style: solid;
  border-color: #e8e8e8;
}
.leftTd {
  width: 17%;
  background-color: #fafafa;
  padding: 16px 24px;
  text-align: center;
}
.rightTd {
  width: 35%;
  padding: 16px 24px;
}
</style>
