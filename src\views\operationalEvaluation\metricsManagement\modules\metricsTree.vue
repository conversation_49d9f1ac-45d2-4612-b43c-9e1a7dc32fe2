<template>
  <div class="cla-card">
    <div class="tree-wrapper vScroll">
      <div v-if="categoryTree.length == 0" style="cursor: pointer;margin-left: 10px;" @click="handleAddType">
        <div style="padding: 0 2px; background: #409eff; margin-right: 6px; border-radius: 2px; display: inline-block">
          <a-icon type="plus" style="color: #ffffff"></a-icon>
        </div>
        <span style="font-size: 14px">添加类别</span>
      </div>

      <div v-if="categoryTree.length > 0" style="margin-bottom: 5px">
        <a-dropdown :trigger="['contextmenu']">
          <div
            class="loadAllBtn"
            @click="loadAll"
            style="user-select: none"
            :style="{
              backgroundColor: selectedIds.length == 0 ? '#f1f1f1' : '#fff',
              color: selectedIds.length == 0 ? '#409eff' : 'rgba(0, 0, 0, 0.65)',
            }"
          >
            <a-icon type="appstore" />
            全部类别
          </div>
          <template #overlay>
            <a-menu slot="overlay">
              <a-menu-item @click="() => handleAddType({})">添加类别</a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </div>
      <a-tree
        :tree-data="categoryTree"
        :replaceFields="replaceFields"
        :autoExpandParent="autoExpandParent"
        :selectedKeys="selectedKeys"
        @select="onSelect"
      >
        <template #title="{ key: treeKey, title, ...record }">
          <a-dropdown :trigger="['contextmenu']">
            <span>{{ record.typeName }}</span>
            <template #overlay>
              <a-menu slot="overlay">
                <a-menu-item @click="() => handleEditType({ id: treeKey, ...record })">编辑类别</a-menu-item>
                <a-menu-item @click="() => confirmDelete({ id: treeKey, ...record })">删除类别</a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </template>
      </a-tree>
    </div>
    <metrics-category-modal
      :node-type="categoryTree.length <= 0 ? 'city' : ''"
      ref="modalTypeForm"
      @refresh="refresh"
    ></metrics-category-modal>
  </div>
</template>

<script>
import { deleteAction, getAction } from '@/api/manage'
import metricsCategoryModal from './metricsCategoryModal'
export default {
  name: 'metricsTree',
  components: {
    metricsCategoryModal,
  },
  data() {
    return {
      loading: false,
      categoryTree: [],
      url: {
        getMetricsCategory: '/evaluate/metricsType/list', // 获取评估指标类别列表
        deleteMetricsCategory: '/evaluate/metricsType/delete', // 删除评估指标类别
      },
      autoExpandParent: true,
      replaceFields: {
        key: 'id',
        title: 'typeName',
        children: 'children',
      },
      selectedKeys: [],
      selectedIds: '',
    }
  },
  created() {
    this.loadTree()
  },
  methods: {
    // 查询树
    loadTree() {
      let that = this
      that.categoryTree = []
      this.loading = true
      let param = {}
      getAction(this.url.getMetricsCategory, param).then((res) => {
        if (res.success) {
          if (res.result && res.result.length > 0) {
            that.categoryTree = res.result
          }
          this.loading = false
        }
      })
    },
    //类别的添加
    handleAddType() {
      this.$refs.modalTypeForm.add()
      this.$refs.modalTypeForm.title = '新增'
    },
    //类别的编辑
    handleEditType(record) {
      this.$refs.modalTypeForm.edit(record)
      this.$refs.modalTypeForm.title = '编辑'
    },
    //类别的删除
    async confirmDelete(node) {
      if (!node || !node.id) {
        this.$message.warning('请选择要删除的类别')
        return
      }

      let tip = '是否删除该类别?'
      let _this = this
      this.$confirm({
        title: '确认删除',
        okText: '是',
        cancelText: '否',
        content: tip,
        onOk: function () {
          _this.deleteType(node.id)
        },
      })
    },
    //删除类别
    async deleteType(id) {
      await deleteAction(this.url.deleteMetricsCategory, { id: id }).then((res) => {
        if (res.success) {
          this.$message.success(res.message)
          this.$emit('refreshData')
          this.loadTree()
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    refresh() {
      // 需要刷新类别列表
      this.loadTree()
    },
    // 选择树的方法
    onSelect(selectedKeys, e) {
      //当前节点被选中
      if (selectedKeys.length > 0) {
        this.selectedKeys = selectedKeys
        this.selectedIds = e.node.dataRef.id
        //向父组件弹射抛值
        this.$emit('refreshData', this.selectedIds)
      }
    },
    loadAll() {
      if (this.selectedKeys != []) {
        this.selectedKeys = []
      }
      this.selectedIds = ''
      this.$emit('refreshData')
    },
  },
}
</script>

<style lang="less" scoped>
@import '~@assets/less/scroll.less';
.cla-card {
  height: 100% !important;
  background-color: #ffffff;
  border-radius: 3px;
  display: flex;
  flex-direction: column;
}
.tree-wrapper {
  flex: auto;
  padding: 20px 14px 14px 14px;
}
.loadAllBtn:hover {
  background-color: #fff;
  color: #409eff !important;
  cursor: pointer;
}

.loadAllBtn {
  padding: 5px;
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1.5;
  padding-left: 10px;
}
</style>
