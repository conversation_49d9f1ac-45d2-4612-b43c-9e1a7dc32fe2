<template>
  <keep-alive>
    <component :is="pageName" :journal-info="journalInfo" :device-info='deviceInfo'/>
  </keep-alive>
</template>
<script>
  import JournalList from './JournalList.vue'
  import JournalModal from './JournalModal.vue'
  export default {
    name: "JournalDetails",
    data() {
      return {
        isActive: 0,
        deviceInfo:{},
        journalInfo:{},
      };
    },
    components: {
      JournalList,
      JournalModal
    },
    //使用计算属性
    computed: {
      pageName() {
        switch (this.isActive) {
          case 0:
            return "JournalList";
            break;
          default:
            return "JournalModal";
            break;
        }
      }
    },
    methods: {
      show(index,item) {
        this.isActive = index;
        this.deviceInfo = item;
      },
      pButton1(index) {
        this.isActive = index;
      },
      pButton2(index,item) {
        this.isActive = index;
        this.journalInfo = item;
      }
    },
  }
</script>