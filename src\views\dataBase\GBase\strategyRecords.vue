<template>
  <a-row :gutter='10' style='height: 100%' class='vScroll'>
    <a-col style='width: 100%; height: 100%; display: flex; flex-direction: column'>
      <!-- 查询区域 -->
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <div class='table-page-search-wrapper'>
          <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
            <a-row :gutter='24' ref='row'>
              <a-col :span='spanValue' v-if='!taskId'>
                <a-form-item label='任务名称'>
                  <a-input :maxLength='maxLength' v-model='queryParam.taskName' :allow-clear='true'
                           autocomplete='off'
                           placeholder='请输入任务名称' />
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label='开始时间' >
                  <a-range-picker v-model='filterTimes' show-time style='width: 100%' @change='startChange'>
                  </a-range-picker>
                </a-form-item>
              </a-col>
              <a-col :span='spanValue' v-if='!taskId'>
                <a-form-item label='执行类型'>
                  <a-select v-model='queryParam.recordType' placeholder='请选择日志类型'
                            style='width: 100%' :allowClear='true' :options='recordTypes'>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span='spanValue' v-show='toggleSearchStatus' v-if='!taskId'>
                <a-form-item label='数据库'>
                  <a-select v-model='queryParam.targetDb' placeholder='请选择数据资源库'
                            style='width: 100%' :allowClear='true' :options='dataBases'>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span='colBtnsSpan()'>
                <span class='table-page-search-submitButtons'
                      :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button class='btn-search btn-search-style' type='primary' @click='searchQuery'>查询</a-button>
                  <a-button class='btn-reset btn-reset-style' @click='searchReset'>重置</a-button>
                  <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>

      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <!-- 操作按钮区域 -->
<!--        <div class='table-operator table-operator-style'>
          <a-button class='btn-add' @click='handleAdd'>新增</a-button>
          <a-dropdown v-if='selectedRowKeys.length > 0'>
            <a-menu slot="overlay" style='text-align: center'>
              <a-menu-item key='1' @click='batchDel'>删除</a-menu-item>
            </a-menu>
            <a-button>批量操作
              <a-icon type='down' />
            </a-button>
          </a-dropdown>
        </div>-->

        <!-- table区域-begin -->
        <a-table ref='table'
                 bordered
                 :rowKey='(record,index)=>{return record.id}'
                 :columns='columns'
                 :dataSource='dataSource'
                 :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
                 :pagination='ipagination'
                 :loading='loading'
                 @change='handleTableChange'>
          <!-- 字符串超长截取省略号显示-->
          <template slot='index' slot-scope='text,record,index'>
            <span>{{ index + 1 }}</span>
          </template>
          <span slot='templateContent' slot-scope='text'>
            <j-ellipsis :value='text' :length='25' />
          </span>
          <span slot='action' slot-scope='text, record' class='caozuo' style='padding-top: 10px;padding-bottom: 10px;'>
            <a @click='handleDetail(record)'>查看</a>
<!--            <a-divider type='vertical' />
            <a-popconfirm title='确定删除吗?' @confirm='() => handleDelete(record.id)'>
              <a>删除</a>
            </a-popconfirm>-->
              <a-divider type='vertical' v-if='record.recordType === "0"'/>
            <a-popconfirm v-if='record.recordType === "0"' title='确定执行还原操作吗?' @confirm='() => backHandle(record)'>
              <a>还原</a>
            </a-popconfirm>

          </span>
          <template slot='tooltip' slot-scope='text'>
            <a-tooltip placement='topLeft' :title='text' trigger='hover'>
              <div class='tooltip'>
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
      </a-card>
      <!-- table区域-end -->
      <LogDetail ref='logDetail'></LogDetail>
    </a-col>
  </a-row>
</template>

<script>
import {
  JeecgListMixin
} from '@/mixins/JeecgListMixin'
import {
  deleteAction,
  getAction,
  putAction
} from '@/api/manage'
import {
  YqFormSearchLocation
} from '@/mixins/YqFormSearchLocation'
import LogDetail from '@views/dataBase/GBase/modules/LogDetail.vue'
import moment from 'moment'
export default {
  name: 'strategyRecords',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components: {
    LogDetail,
  },
  props: {
    taskId:{
      type:String,
      default:"",
    }
  },
  data() {
    return {
      maxLength:50,
      formItemLayout: {
        labelCol: {
          style: 'width:80px'
        },
        wrapperCol: {
          style: 'width:calc(100% - 80px)'
        }
      },
      // 表头
      columns: [
        {
          title: '序号',
          dataIndex: 'index',
          scopedSlots: {
            customRender: 'index'
          },
          customCell: () => {
            let cellStyle = 'width:60px;text-align: center'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '任务名称',
          dataIndex: 'taskName',
          scopedSlots: {
            customRender: 'taskName'
          }
        },
        {
          title: '数据库',
          dataIndex: 'dbInstance',
        },
        {
          title: '执行类型',
          dataIndex: 'recordType',
          customRender: (text) => {
            return text == 0  ? '备份' : '恢复'
          },
          scopedSlots: {
            customRender: 'recordType'
          }
        }, {
          title: '执行结果',
          dataIndex: 'successFlag',
          customRender: (text,record) => {
            if(record.executeStatus === "执行完毕"){
              return text  ? '成功' : '失败'
            }else{
              return '--'
            }

          },
          scopedSlots: {
            customRender: 'successFlag'
          }
        },
        {
          title: '执行日志',
          dataIndex: 'executeLog',
          ellipsis: true,
          customCell: () => {
            let cellStyle = 'max-width: 300px;text-align: center'
            return {
              style: cellStyle
            }
          }
        },
        {
          title: '开始时间',
          dataIndex: 'startTime',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 100px;max-width:260px'
            return { style: cellStyle }
          },
        }, {
          title: '结束时间',
          dataIndex: 'endTime',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 100px;max-width:260px'
            return { style: cellStyle }
          },
        },{
          title: '执行状态',
          dataIndex: 'executeStatus',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 100px;max-width:260px'
            return { style: cellStyle }
          },
        },{
          title: '备份文件路径',
          dataIndex: 'backFile',
          customCell: () => {
            let cellStyle = 'text-align: center;min-width: 100px;max-width:260px'
            return { style: cellStyle }
          },
        },
        // {
        //   title: '备份记录ID',
        //   dataIndex: 'backRecordId',
        //   customCell: () => {
        //     let cellStyle = 'text-align: center;min-width: 100px;max-width:260px'
        //     return { style: cellStyle }
        //   },
        // },
        {
          title: '操作',
          align: 'center',
          width: 180,
          fixed: 'right',
          dataIndex: 'action',
          scopedSlots: {
            customRender: 'action'
          }
        }
      ],
      url: {
        list: '/gbase/task/recordList',
        delete: '/gbase/task/deleteBatchRecord',
        deleteBatch: '/gbase/task/deleteBatchRecord',
        backUrl:"/gbase/task/enableRecoveryOneShot",
      },
      disableMixinCreated: true,
      dataBases: [],
      filterTimes: [],
      recordTypes: [
        {
          label: '恢复',
          value: '1'
        }, {
          label: '备份',
          value: '0'
        }
      ]
    }
  },
  created() {
    this.getDatabases()
    if(this.taskId){
      this.queryParam.taskId = this.taskId;
    }
    this.loadData()
  },
  mounted() {
  },
  computed: {
    importExcelUrl: function() {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    }
  },
  methods: {
    /*
  * 获取数据库
  * */
    getDatabases() {
      getAction('gbase/manage/list', { pageNo: 1, pageSize: -1 }).then((res) => {
        if (res.success) {
          this.dataBases = res.result.records.map(el => {
            return { label: el.dbInstance, value: el.id }
          })
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    backHandle(record) {
      let that = this
      putAction(this.url.backUrl, record).then((res) => {
        if (res.success) {
          // this.$message.success(res.message)
          this.$confirm({
            title: '提示',
            okText: '确认',
            content: '还原操作执行成功，需要手动点击查询查看记录 ?',
            onOk() {
              return that.loadData()
            },
            onCancel() {},
          })

        } else {
          this.$message.warning(res.message)
        }
      })
    },
    handleDelete: function(id) {
      if (!this.url.delete) {
        this.$message.error('请设置url.delete属性!')
        return
      }
      var that = this
      deleteAction(that.url.delete, { ids: id }).then((res) => {
        if (res.success) {
          //重新计算分页问题
          that.reCalculatePage(1)
          that.$message.success(res.message)
          that.loadData()
        } else {
          that.$message.warning(res.message)
        }
      })
    },
    handleDetail(record) {
      if(record.executeStatus === "执行完毕"){
        this.$refs.logDetail.show(record)
      }
      else{
        this.$message.warning("任务没有执行完毕，无法查看详情")
      }
    },
    // 重置
    searchReset() {
      this.queryParam = {}
      this.filterTimes = [];
      this.loadData(1)
    },
    startChange(arr){
      if(arr.length > 0){
        this.queryParam.startTimeCondition = moment(arr[0]).format("YYYY-MM-DD HH:mm:ss")
        this.queryParam.endTimeCondition = moment(arr[1]).format("YYYY-MM-DD HH:mm:ss")
      }else{
        this.queryParam.startTimeCondition = undefined
        this.queryParam.endTimeCondition = undefined
      }
      // console.log("时间变化 === >",e,this.queryParam)
    },
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';

/deep/ .ant-table-tbody .ant-table-row td {
  padding-top: 5px;
  padding-bottom: 5px;
}
</style>