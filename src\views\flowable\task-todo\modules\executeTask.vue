<template>
  <j-modal
    :maskClosable="false"
    :visible="dialogExcuteTaskVisibleInChild"
    :destroyOnClose="true"
    :width="1200"
    :centered='true'
    switch-fullscreen
    title="执行任务"
    @cancel="handleCancel"
  >
    <template slot="footer">
      <a-button @click="dialogExcuteTaskVisibleInChild = false">关闭</a-button>
      <a-button @click="reset">重置</a-button>
      <span v-for="button in buttonList">
        <a-button
          @click="doButton(button)"
          style="margin-left: 5px"
          v-if="button.value!='users'&&button.value!='noUsers'&&button.value!='withdraw'&&buttons.includes(button.value)"
          type="primary"
          :loading="confirmLoading"
        >{{ button.title }}</a-button>
      </span>
      <a-button
        type="primary"
        style="margin-left: 5px"
        :loading="confirmLoading"
        @click="doComplete"
      >提交</a-button>
    </template>
    <div class="tab-border">
      <a-tabs default-active-key="processInstanceForm">
        <a-tab-pane key="processInstanceForm" force-render tab="任务表单">
          <div :style="{height: `${scrollHeight || '476'}px`}" class="form-wrapper">
            <k-form-build
              v-if="generateTaskFormVisible"
              ref="generateFormTask"
              :dynamicData="dynamicData"
              :value="taskFormJson"
              :disabled="formDisabled"
              :defaultValue="taskFormData"
              class="tab-pane-box-padding"
              @change="kfbChange"
              @assetsHandler="assetsHandler"
            />
          </div>
        </a-tab-pane>
        <a-tab-pane key="comments" tab="过程意见">
          <a-table
            :bordered="true"
            :columns="columns"
            :data-source="data"
            :pagination="pagination"
            :row-key="(record,index)=>{return record.id}"
            :scroll="scro(data)"
            class="tab-pane-box-padding"
            size="small"
            @change="handleTableChange"
          ></a-table>
        </a-tab-pane>
        <a-tab-pane key="processIntanceImage" tab="实时流程图">
          <div class="tab-pane-box-padding">
            <process-picture-real :row="processInstanceId"></process-picture-real>
            <!-- <img style="max-width: 1200px; max-height: 600px" :src="imagePath" alt="流程图" /> -->
          </div>
          <!--        <img :src="imagePath" alt="流程图"/>-->
        </a-tab-pane>
      </a-tabs>

      <div ref="bottomFixed">
        <a-divider style="margin: 0px 0px 20px" type="horizontal"></a-divider>
        <!--意见-->
        <a-form-model ref="form" :model="model" :rules="validatorRules">
          <a-row style="padding: 0 5px 20px 5px">
            <a-col :span="20" :offset="2">
              <a-form-model-item
                label="备注信息"
                :labelCol="{style: `width:${labelWidth}` }"
                :wrapperCol="{style: `width: calc(100% - ${labelWidth} - 98px)`}"
                prop="message"
                class="form-item"
              >
                <a-textarea
                  v-model="model.message"
                  :auto-size="{minRows:2,maxRows:4}"
                  placeholder="请输入备注信息"
                  style="margin-bottom:0;"
                />
              </a-form-model-item>
              <common-phrases-list
                @choose="getCommnonPhrases"
                :module="'taskHandle'"
                class="commnonPhrases"
              ></common-phrases-list>
            </a-col>
          </a-row>
          <!-- 指定办理人  -->
          <a-row v-if="buttons.includes('users')||buttons.includes('noUsers')" style="padding: 0 5px 20px 5px">
            <a-col :span="20" :offset="2">
              <a-form-model-item
                :labelCol="{style: `width:${labelWidth}` }"
                :wrapperCol="{style: `width: calc(100% - ${labelWidth})`}"
                :required="buttons.includes('users')"
                label="指定办理人"
                class="form-item"
              >
                <j-select-user-by-dep-enhance
                  @getFrom="getFrom"
                  ref="assignNextNodeHandle"
                  v-model="assignNextNode"
                  :ccToVos.sync="nextCcToVos"
                  :url="url"
                  :multi="true"
                  :userIds.sync="assignNextNode"
                ></j-select-user-by-dep-enhance>
              </a-form-model-item>
            </a-col>
          </a-row>
          <!--  抄送 -->
          <a-row style="padding: 0 5px 15px 5px">
            <a-col :span="20" :offset="2">
              <a-form-model-item
                :labelCol="{style: `width:${labelWidth}` }"
                :wrapperCol="{style: `width: calc(100% - ${labelWidth})`}"
                label="抄送给"
                class="form-item"
              >
                <j-select-user-by-dep-enhance
                  ref="JSelectUserByDepEnhance"
                  v-model="userIds"
                  :ccToVos.sync="ccToVos"
                  :multi="true"
                  :userIds.sync="userIds"
                ></j-select-user-by-dep-enhance>
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </div>
    </div>
    <!--    选择用户选择区-->
    <j-select-user-by-dep-modal-enhance
      ref="selectModal"
      :modalWidth="1250"
      :multi="multi"
      @ok="selectedUser"
    ></j-select-user-by-dep-modal-enhance>
    <!--    退回节点-->
    <task-back-nodes
      v-if="dialogTaskBackNodesVisible"
      :executeTaskId="executeTaskId"
      :visible.sync="dialogTaskBackNodesVisible"
      @backTaskFinished="backTaskFinished"
    ></task-back-nodes>
    <!-- 表单区域 -->
    <Process-instance-start
      v-if="dialogStartInstanceVisible"
      :dialogStartInstanceVisible.sync="dialogStartInstanceVisible"
      :showDdraft="false"
      :associationId="processInstanceId"
      :process-definition="processDefinition"
    ></Process-instance-start>
    <!-- 变更资产弹框 -->
    <assets-update-list ref="assetsUpdateModal" @setAssetsChange="setAssetsChange"></assets-update-list>
  </j-modal>
</template>

<script>
import { getAction, putAction } from '@/api/manage'
import JSelectUserByDepEnhance from '@/components/flowable/JSelectUserByDepEnhance'
import JSelectUserByDepModalEnhance from '@/components/flowable/modules/JSelectUserByDepModalEnhance'
import TaskBackNodes from './TaskBackNodes'
import ProcessPictureReal from '@views/flowable/process-instance/modules/ProcessPictureReal'
import { ajaxGetDictItems, getDictItemsFromCache } from '@api/api'
import ProcessInstanceStart from '@views/flowable/process-instance-start/module/ProcessInstanceStart.vue'
import commonPhrasesList from '@views/flowable/CommonPhrases/modules/commonPhrasesList.vue'
import AssetsUpdateList from '@/views/cmdb/assets/assetsUpdate/AssetsUpdateList'

export default {
  name: 'executeTask',
  components: {
    ProcessInstanceStart,
    JSelectUserByDepEnhance,
    JSelectUserByDepModalEnhance,
    TaskBackNodes,
    ProcessPictureReal,
    commonPhrasesList,
    AssetsUpdateList
  },
  data() {
    return {
      url:"",
      processDefinition: undefined,
      dialogStartInstanceVisible: false,
      buttonList: [],
      nextCcToVos: [],
      pagination: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '30'],
        showTotal: (total, range) => {
          return range[0] + '-' + range[1] + ' 共' + total + '条'
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0
      },
      columns: [
        {
          title: '任务节点',
          dataIndex: 'taskName',
          key: 'taskName',
          customCell: () => {
            let cellStyle = 'text-align:center'
            return { style: cellStyle }
          }
        },
        {
          title: '操作类型',
          dataIndex: 'typeDesc',
          key: 'typeDesc',
          customCell: () => {
            let cellStyle = 'text-align:center'
            return { style: cellStyle }
          }
        },
        {
          title: '操作人',
          dataIndex: 'userName',
          key: 'userName',
          customCell: () => {
            let cellStyle = 'text-align:center'
            return { style: cellStyle }
          }
        },
        {
          title: '操作时间',
          key: 'time',
          dataIndex: 'time',
          customCell: () => {
            let cellStyle = 'text-align:center;width:180px'
            return { style: cellStyle }
          }
        },
        {
          title: '意见',
          key: 'fullMessage',
          dataIndex: 'fullMessage',
          customCell: () => {
            let cellStyle = 'text-align:center'
            return { style: cellStyle }
          }
        }
      ],
      dynamicData: {
        bpmnDepartTreeData: []
      },
      data: [],
      userIds: '',
      selectUserQueryConfig: [{ key: 'phone', label: '电话' }],
      confirmLoading: false,
      activeName: 'processInstanceForm',
      startFormKey: undefined,
      taskFormKey: undefined,
      startFormJson: undefined,
      taskFormJson: undefined,
      generateTaskFormVisible: false,
      taskFormData: {},
      showBusinessKey: false,
      businessKey: undefined,
      processInstanceFormData: undefined,
      startUserId: '',
      initiator: false,
      selectUserMultipleSelect: false,
      selectUserType: '',
      message: '',
      ccToVos: [],
      dialogTaskBackNodesVisible: false,
      buttons: [],
      assignNextNode: '',
      multi: false,
      list: [],
      layout: {
        labelCol: { span: 4 },
        wrapperCol: { span: 20 }
      },
      labelWidth: '100px', // 配置备注信息、指定办理人、抄送人的label宽度
      validatorRules: {
        /*message: [{required: true, message: '请输入审批意见!'},
          {min: 2, max: 500, message: '长度在 2 到 200 个字符', trigger: 'change'}],*/
      },
      model: {
        message: ''
      },
      formDisabled: false,
      tempProcessInstanceId: null, // 临时流程id
      scrollHeight: null
    }
  },
  props: {
    processDefinitionVersion: { type: Number },
    taskDefinitionKey: { type: String },
    selectRow: {},
    visible: {
      type: Boolean,
      default: function() {
        return false
      }
    },
    executeTaskId: {
      type: String,
      default: function() {
        return ''
      }
    },
    processInstanceId: {
      type: String,
      default: function() {
        return ''
      }
    },
    modelKey: {
      type: String
    }
  },
  computed: {
    dialogExcuteTaskVisibleInChild: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    imagePath: function() {
      return (
        window._CONFIG['domianURL'] +
        '/flowable/diagramView?processInstanceId=' +
        this.processInstanceId +
        '&time=' +
        new Date()
      )
    }
  },
  created() {
    this.initData()
    this.initDictData('process_button', 'buttonList')
    this.getTreeData()
  },
  mounted() {
    window.onresize = () => {
      this.getHeight()
    }
    // 办理页面打开的时候，如果不设置这个，会请求一个错误的url。
    // 但是如果流程下一个节点有分支判断，有指定下一级办理人按钮，打开任务执行页面就报错提示条件表示式判断错误的问题，得去改这个选择办理人的组件（jeecg官方组件不建议修改，不行就自己封装一个组件）
    this.url= "/flowable/model/queryUsersByTaskId?taskId="+this.executeTaskId
  },
  methods: {
    getFrom(){
        this.url= "/flowable/model/queryUsersByTaskId?taskId="+this.executeTaskId+"&variables="+JSON.stringify(this.$refs.generateFormTask.form.getFieldsValue())
    },
    // 计算表单可滚动区域高度
    getHeight() {
      this.$nextTick(() => {
        const modalBody = window.innerHeight - 53 - 55 - 30 - 30 // .ant-modal-body弹框的高度
        this.fixedHeight = this.$refs.bottomFixed.offsetHeight // 计算下半部分固定高度(意见、抄送、办理人)
        this.scrollHeight = modalBody - this.fixedHeight - 48 - 45 // 计算上半部分可滚动表单高度
      })
    },
    doButton(data) {
      if (data.value === 'ASSIGN') {
        this.doAssign()
      } else if (data.value === 'DELEGATE') {
        this.doDelegate()
      } else if (!this.initiator && data.value === 'BACK') {
        this.doBack()
      } else if (data.value === 'STOP') {
        this.doStop()
      } else if (data.value === 'proposeEvent' || data.value === 'proposeQuestion') {
        this.propose(data.description)
      }else if (data.value === 'REJECT') {
        this.reject()
      }
    },
    propose(key) {
      getAction('/flowable/processDefinition/listMyself?processDefinitionKey=' + key).then(res => {
        if (res.result.records[0].id) {
          this.processDefinition = res.result.records[0]
          this.dialogStartInstanceVisible = true
        }
      })
    },
    reject(){
      // 触发审批意见表单验证
      this.$refs.form.validate(valid => {
        if (valid) {
          if (this.model.message) {
            this.message = this.model.message
          } else {
            this.message = '已拒单'
          }
          this.confirmLoading = false
          // this.checkMessage()
          putAction('/flowable/task/reject', {
            taskId: this.executeTaskId
          }).then(res => {
            this.$message.success(res.message)
            this.dialogExcuteTaskVisibleInChild = false
            this.$store.commit('TASK_TIMESTIMP', res.timestamp)
            this.$emit('close')
          })
        } else {
          this.confirmLoading = false
          return false
        }
      })
    },
    initDictData(dictCode, list) {
      //优先从缓存中读取字典配置
      if (getDictItemsFromCache(dictCode)) {
        this[list] = getDictItemsFromCache(dictCode)

        return
      }

      //根据字典Code, 初始化字典数组
      ajaxGetDictItems(dictCode, null).then(res => {
        if (res.success) {
          console.log(res)
          this[list] = res.result
        }
      })
    },
    getJsonValue(obj, name) {
      let result = null
      let value = null
      for (const key in obj) {
        value = obj[key]
        if (key == name && typeof value === 'string' && value.includes('score')) {
          this.list.push(value)
        } else {
          if (typeof value == 'object') {
            result = this.getJsonValue(value, name)
          }
        }
      }
      return result
    },
    kfbChange(val, key) {
      // 针对月度考核做分数核算
      if (this.selectRow.modelKey === 'assessMonth') {
        if (key.includes('score') && key !== 'final') {
          this.getJsonValue(this.taskFormJson, 'defaultValue')
          let keyword = this.list.pop()
          this.$nextTick(() => {
            this.$refs.generateFormTask.getData().then(values => {
              let sum = 0
              let scoreSum = `${keyword}_sum`
              for (let k in values) {
                if (k.includes(keyword) && k !== scoreSum && k !== 'final') {
                  let keys = k.split('_')
                  let percentage = values[`percentage_${keys[2]}`]
                  let value = values[k]
                  sum += (value * parseFloat(percentage)) / 100
                }
              }
              let data = {}
              data[scoreSum] = sum
              this.$refs.generateFormTask.setData(data)
            })
          })
        }
      }
    },
    //表格页切换（后端发送所有数据，前端进行换页）
    handleTableChange(pagination, filters, sorter) {
      //分页、排序、筛选变化时触发
      //TODO 筛选
      if (Object.keys(sorter).length > 0) {
        this.isorter.column = sorter.field
        this.isorter.order = 'ascend' == sorter.order ? 'asc' : 'desc'
      }
      this.ipagination = pagination
    },
    doSelectUser() {
      this.multi = true
      this.$refs.selectModal.showModal()
      this.selectUserType = 'assignNextNode'
    },
    getTreeData() {
      getAction('/sys/sysDepart/queryTreeList').then(res => {
        this.dynamicData.bpmnDepartTreeData = res.result
      })
    },
    reset() {
      this.$refs.generateFormTask.reset()
      this.businessKey = undefined
      this.message = undefined
      this.ccToVos = []
      this.userIds = ''

      this.$refs.JSelectUserByDepEnhance.value = ''
      this.$refs.JSelectUserByDepEnhance.storeVals = ''
      this.$refs.JSelectUserByDepEnhance.textVals = ''
    },
    //退回流程
    doBack() {
      this.confirmLoading = true
      // 触发审批意见表单验证
      this.$refs.form.validate(valid => {
        if (valid) {
          if (this.model.message) {
            this.message = this.model.message
          } else {
            this.message = ' '
          }
          this.confirmLoading = false
          this.dialogTaskBackNodesVisible = true
        } else {
          this.confirmLoading = false
          return false
        }
      })
    },
    backTaskFinished(backNode) {
      putAction('/flowable/task/back', {
        taskId: this.executeTaskId,
        activityId: backNode.nodeId,
        activityName: backNode.nodeName,
        userId: backNode.userId,
        message: this.message
      }).then(res => {
        this.$message.success(res.message)
        this.dialogExcuteTaskVisibleInChild = false
        this.$store.commit('TASK_TIMESTIMP', res.timestamp)
        this.$emit('close')
      })
    },
    selectedUser(users) {
      if (this.selectUserType === 'assign') {
        putAction('/flowable/task/assign', {
          taskId: this.executeTaskId,
          userId: users[0].username,
          message: this.message
        }).then(res => {
          this.$message.success(res.message)
          this.selectUserVisible = false
          this.dialogExcuteTaskVisibleInChild = false
          this.$store.commit('TASK_TIMESTIMP', res.timestamp)
          this.$emit('close')
        })
      } else if (this.selectUserType === 'delegate') {
        putAction('/flowable/task/delegate', {
          taskId: this.executeTaskId,
          userId: users[0].username,
          message: this.message
        }).then(res => {
          this.$message.success(res.message)
          this.selectUserVisible = false
          this.dialogExcuteTaskVisibleInChild = false
          this.$store.commit('TASK_TIMESTIMP', res.timestamp)
          this.$emit('close')
        })
      } else if (this.selectUserType === 'assignNextNode' && users.length > 0) {
        let usernames = users.map(user => user.username).reduce((v1, v2) => v1 + ',' + v2)
        if (usernames.length > 0) {
          this.assignNextNode = usernames
        }
      }
    },
    scro(data) {
      return data.length > 0 ? { x: true } : {}
    },

    handleCancel() {
      this.dialogExcuteTaskVisibleInChild = false
      // this.$emit('ok')
    },
    initData() {
      const current = this
      getAction('/flowable/task/executeTaskData', {
        taskId: this.executeTaskId
      }).then(res => {
        const { result } = res
        current.startUserId = result.startUserId
        current.showBusinessKey = result.showBusinessKey
        current.businessKey = result.businessKey
        current.startFormKey = result.startFormKey
        current.taskFormKey = result.taskFormKey
        current.initiator = result.initiator
        current.buttons = result.buttons ? result.buttons : []
        if (result.renderedTaskForm) {
          current.taskFormJson = JSON.parse(result.renderedTaskForm)
          // console.log("表单数据****DDDD**** ==== ",current.taskFormJson)

          if (result.variables) {
            current.taskFormData = result.variables
            this.formDisabled = result.variables.taskAllowEdit === '2'
            if (result.variables.tempProcessInstanceId) {
              this.tempProcessInstanceId = result.variables.tempProcessInstanceId
            }
          }
          current.generateTaskFormVisible = true
        } else if (result.renderedStartForm) {
          current.taskFormJson = JSON.parse(result.renderedStartForm)
          this.formDisabled = true
          if (result.variables) {
            current.taskFormData = result.variables
            if (result.variables.tempProcessInstanceId) {
              this.tempProcessInstanceId = result.variables.tempProcessInstanceId
            }
          }
          current.generateTaskFormVisible = true
        }
        if (current.generateTaskFormVisible) {
          current.activeName = 'taskForm'
        }
        if (!current.generateTaskFormVisible) {
          current.fullscreen = false
        }
        // 计算底部固定元素高度
        this.getHeight()
      })

      getAction('flowable/processInstance/comments', {
        processInstanceId: this.processInstanceId
      })
        .then(res => {
          this.data = res.result
        })
        .finally(() => {})

      // 初始化抄送人
      // 该组件被多个实例引用
      let requestBody
      if (this.selectRow) {
        // 我的待办 列表数据 信息更加详细
        requestBody = {
          nodeId: this.selectRow.taskDefinitionKey,
          proDefVersion: this.selectRow.processDefinitionVersion,
          modelKey: this.selectRow.modelKey
        }
      } else {
        // 消息通知
        requestBody = {
          nodeId: this.taskDefinitionKey,
          proDefVersion: this.processDefinitionVersion,
          modelKey: this.modelKey
        }
      }
      console.log(requestBody)
      getAction('/notification/actZNotification/queryByNodeIdAndVersion', requestBody).then(
        ({ success, message, result }) => {
          if (success) {
            if (result != null) {
              this.userIds = result.users
            }
          }
        }
      )
    },
    doComplete() {
      // this.checkMessage()
      const that = this

      if (that.buttons.includes('users')) {
        if (!that.assignNextNode) {
          that.$message.error('未指定办理人')
          return
        }
      }

      that.confirmLoading = true
      let arr = []
      if (this.ccToVos) {
        this.ccToVos.forEach(v => {
          arr.push(v.username)
        })
      }

      // 触发审批意见表单验证
      this.$refs.form.validate(valid => {
        if (valid) {
          that.message = that.model.message === '' ? '已处理' : that.model.message
          that.confirmLoading = false
          if (that.$refs.generateFormTask) {
            that.$refs.generateFormTask.getData().then(values => {
                // 校验资产变更
                if (values.changeManageClass && values.changeManageClass.includes('资产变更')) {
                  if (!values.assetsChangeList || values.assetsChangeList.length == 0) {
                    this.$message.error('未添加变更资产')
                    return
                  }
                }

                that.confirmLoading = true
                if (values && values != undefined) {
                  Object.assign(values, {
                    tempProcessInstanceId: this.tempProcessInstanceId
                  })
                  let realValues = values
                  if (that.startFormKey && that.taskFormKey && that.startFormKey === that.taskFormKey) {
                    const processInstanceFormData = JSON.stringify(values)
                    realValues = Object.assign({processInstanceFormData},values)
                  }
                  return putAction('/flowable/task/complete', {
                    taskId: that.executeTaskId,
                    message: that.message,
                    initiator: that.initiator, //发起人
                    values: realValues, //任务表单
                    ccUserIds: arr, //抄送人
                    assignNextNode: that.assignNextNode
                  }).then(res => {
                    if (res.success) {
                      that.$message.success(res.message)
                      that.dialogExcuteTaskVisibleInChild = false
                      that.confirmLoading = false
                      that.$store.commit('TASK_TIMESTIMP', res.timestamp)
                      that.$emit('close')
                      that.$emit('ok')
                    } else {
                      that.confirmLoading = false
                      that.$message.error(res.message)
                    }
                  })
                }
              })
              .catch(e => {
                console.info(e)
              })
          } else {
            that.confirmLoading = true
            putAction('/flowable/task/complete', {
              taskId: that.executeTaskId,
              message: that.message,
              ccUserIds: arr, //抄送人
              assignNextNode: that.assignNextNode
            }).then(res => {
              if (res.success) {
                that.$message.success(res.message)
                that.dialogExcuteTaskVisibleInChild = false
                that.$store.commit('TASK_TIMESTIMP', res.timestamp)
                that.confirmLoading = false
                that.$emit('close')
              } else {
                that.confirmLoading = false
                that.$message.error(res.message)
              }
            })
          }
        } else {
          that.confirmLoading = false
          return false
        }
      })
    },

    //终止流程
    doStop() {
      this.confirmLoading = true
      // 触发审批意见表单验证
      this.$refs.form.validate(valid => {
        if (valid) {
          if (this.model.message) {
            this.message = this.model.message
          } else {
            this.message = '已终止'
          }
          this.confirmLoading = false
          // this.checkMessage()
          putAction('/flowable/task/stopProcessInstance', {
            taskId: this.executeTaskId,
            message: this.message
          }).then(res => {
            this.$message.success(res.message)
            this.dialogExcuteTaskVisibleInChild = false
            this.$store.commit('TASK_TIMESTIMP', res.timestamp)
            this.$emit('close')
          })
        } else {
          this.confirmLoading = false
          return false
        }
      })
    },
    //转办流程
    doAssign() {
      this.confirmLoading = true
      // 触发审批意见表单验证
      this.$refs.form.validate(valid => {
        if (valid) {
          if (this.model.message) {
            this.message = this.model.message
          } else {
            this.message = '已转办'
          }
          this.confirmLoading = false
          this.multi = false
          this.$refs.selectModal.showModal()
          this.selectUserType = 'assign'
        } else {
          this.confirmLoading = false
          return false
        }
      })
    },
    //委派流程任务
    doDelegate() {
      this.confirmLoading = true
      // 触发审批意见表单验证
      this.$refs.form.validate(valid => {
        if (valid) {
          if (this.model.message) {
            this.message = this.model.message
          } else {
            this.message = '已委派'
          }
          this.confirmLoading = false
          this.multi = false
          this.$refs.selectModal.showModal()
          this.selectUserType = 'delegate'
        } else {
          this.confirmLoading = false
          return false
        }
      })
    },
    getCommnonPhrases(value) {
      this.model.message += value
      this.$refs.form.validateField('message')
    },
    //监听到资产变化
    setAssetsChange(data, tempId) {
      console.log(data, '变更的资产')
      this.tempProcessInstanceId = tempId
      this.$refs.generateFormTask.setData({
        assetsChangeList: data
      })
    },
    //打开资产编辑
    assetsHandler(e) {
      if (this.tempProcessInstanceId) {
        this.$refs.assetsUpdateModal.tempId = this.tempProcessInstanceId
      } else {
        this.$refs.assetsUpdateModal.tempId = ''
      }
      this.$refs.assetsUpdateModal.init(e)
    }
  }
}
</script>

<style lang="less" scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/YQCommon.less';
@import '~@assets/less/YQNormalModal.less';

.tab-border {
  border: 1px solid #e8e8e8;
}

.tab-pane-box-padding {
  padding: 0px 5px !important;
}

//非全屏模式下，重写实时流程图containers高度样式
::v-deep .containers {
  //非全屏模式下，流程图容器containers的高度设置如下
  height: calc(100vh - 100px - 56px - 24px - 1px - 60px - 32px - 12px - 21px - 52px - 72px - 1px - 24px - 56px - 24px) !important;
}

//在全屏模式下，实时流程图容器containers的高度样式如下；
.j-modal-box.fullscreen {
  ::v-deep .containers {
    height: calc(100vh - 56px - 24px - 1px - 66px - 32px - 12px - 21px - 52px - 72px - 1px - 24px - 56px) !important;
  }
}

::v-deep .ant-table-scroll {
  overflow: hidden !important;
  overflow-x: auto !important;

  .ant-table-body {
    margin: 0px !important;
    overflow: hidden !important;
    overflow-x: auto !important;
  }
}
.form-item {
  display: flex;
  margin-bottom: 0;
}
.commnonPhrases {
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  margin: auto;
  width: 88px;
  padding: 0;
  height: 34px;
}
.form-wrapper {
  overflow: auto;
  padding-bottom: 20px;
  margin-bottom: 10px;
}
/deep/ .ant-modal {
  padding-bottom: 0;
}
::-webkit-scrollbar {
  width: 6px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
}
</style>
