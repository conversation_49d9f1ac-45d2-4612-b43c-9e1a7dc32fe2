<template>
  <div style="height:100%">
    <keep-alive exclude='scanTaskChildrenList, scanTaskDetails, scanTaskChildrenDetails'>
      <component style="height:100%" :is="pageName" :data="data" />
    </keep-alive>
  </div>
</template>
<script>
  import scanTaskList from './scanTaskList'
  import scanTaskDetails from './modules/scanTaskDetails'
  import scanTaskChildrenList from './modules/scanTaskChildrenList'
  export default {
    name: "scanTaskManagement",
    data() {
      return {
        isActive: 0,
        data: {},
      }
    },
    components: {
      scanTaskList,
      scanTaskDetails,
      scanTaskChildrenList
    },
    created() {
      this.pButton1(0);
    },
    //使用计算属性
    computed: {
      pageName() {
        switch (this.isActive) {
          case 0:
            return "scanTaskList";
          case 1:
            return "scanTaskDetails";
          case 2:
            return "scanTaskChildrenList";
          default:
            return "";
        }
      }
    },
    methods: {
      pButton1(index) {
        this.isActive = index;
      },
      pButton2(index, item) {
        this.isActive = index;
        this.data = item
      }
    }
  }
</script>