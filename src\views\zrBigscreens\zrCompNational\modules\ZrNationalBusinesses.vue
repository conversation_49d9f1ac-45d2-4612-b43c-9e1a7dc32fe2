<template>
  <div class='zr-national-businesses'>
    <zr-bigscreen-title title='应用系统'></zr-bigscreen-title>
    <div class='national-businesses-content'>
      <div class='national-businesses-list-box'>
        <slot>
          <div v-if='!srollOption.autoPlay' class='system-list' ref='systemList' @click='goBusiness'>
            <div class='system-list-item' ref='systemListItem' v-for='businessData in listData'
                 :key='businessData.businessName' :data-systemid='businessData.id'>
              <div class='status-header-bar'>
                <div :title="businessData.businessName" class='status-header-bar-left'>
                  {{ businessData.businessName }}
                </div>
                <div class='status-header-bar-right' :style='{background:businessData.statusColor}'>
                  {{ businessData.statusLabel }}
                </div>
              </div>
              <div class='status-body' :style='{ backgroundImage:`url(${businessData.businessImage})` }'>
              </div>
            </div>
          </div>
          <vue-seamless-scroll v-else :data='listData' class='scroll-warp'
                               :class-option='srollOption' @click.native='goBusiness'>
            <div class='system-list'>
              <div class='system-list-item' ref='systemListItem' v-for='businessData in listData'
                   :key='businessData.businessName' :data-systemid='businessData.id'>
                <div class='status-header-bar'>
                  <div :title="businessData.businessName" class='status-header-bar-left'>
                    {{ businessData.businessName }}
                  </div>
                  <div class='status-header-bar-right' :style='{background:businessData.statusColor}'>
                    {{ businessData.statusLabel }}
                  </div>
                </div>
                <div class='status-body' :style='{ backgroundImage: `url(${businessData.businessImage})` }'>
                </div>
              </div>
            </div>
          </vue-seamless-scroll>
        </slot>
      </div>

    </div>
  </div>
</template>
<script>
import ZrBigscreenTitle from '@views/zrBigscreens/modules/ZrBigscreenTitle.vue'
import { businessStatus, systemList } from '@views/zrBigscreens/modules/zrUtil'
import vueSeamlessScroll from 'vue-seamless-scroll'
import resizeObserverMixin from '@views/statsCenter/com/resizeObserverMixin'

export default {
  name: 'ZrNationalBusinesses',
  components: { ZrBigscreenTitle, vueSeamlessScroll },
  mixins: [resizeObserverMixin],
  props: {
    systemList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      srollOption: {
        step: 1, // 步长
        speed: 100, // 滚动速度
        timer: 3000,// 滚动时间间隔
        autoPlay: false,
        limitMoveNum: 10000,
        singleHeight: 247
      },
      maxNum: 0,
      imgUrl:window._CONFIG['domianURL']+"/",
      listData:[],
    }
  },
  created() {
  },
  mounted() {
    this.$nextTick(()=>{
      this.resizeObserverCb()
    })
  },
  watch:{
    systemList:{
      handler(newVal){
        if(this.listData.length>0){
          this.listData = []
          this.$nextTick(()=>{
            this.listData = newVal || []
          })
        }else{
          this.listData = newVal || []
        }
        this.mapList()
        this.resizeObserverCb()
      },
      deep:true,
      immediate:true,
    }
  },
  computed: {
  },
  methods: {
    mapList(){
      this.listData = this.listData.map(el => {
        let status = businessStatus.find(sel => sel.value === el.status)
        el.statusColor = status ? status.color : ''
        el.statusLabel = status ? status.label : ''
        return el
      })
    },
    goBusiness(e){
      let targetP = e.target.parentNode
      let id = targetP.dataset.systemid || targetP.parentNode.dataset.systemid
      if(id){
        const sysInfo = this.listData.find(el=>el.id == id)
        if(sysInfo){
          this.$router.push({
            path: '/operationsView/business',
            query: {
              deptId:sysInfo.deptId,
              id:sysInfo.id,
            }
          })
        }
      }

    },
    //监听页面缩放 更新中间区域高度
    resizeObserverCb() {
      if (this.listData.length > 0 && this.$refs.systemList) {
        let listRect = this.$refs.systemList.getBoundingClientRect()
        let itemRect = this.$refs.systemListItem[0].getBoundingClientRect()
        this.maxNum = Math.floor(listRect.height / (itemRect.height + 19))
        if (this.maxNum > 0 && this.maxNum < this.listData.length) {
          this.srollOption.autoPlay = true
          this.srollOption.limitMoveNum = this.maxNum
          this.srollOption.singleHeight = itemRect.height + 19
        } else {
          this.srollOption.autoPlay = false
          this.srollOption.limitMoveNum = 10000
        }
      }

    },
  }
}
</script>

<style scoped lang='less'>
.zr-national-businesses {
  height: 100%;
  overflow: hidden;

  .national-businesses-content {
    margin-top: -5.5px;
    padding: 12px 0px 0px 12px;
    height: calc(100% - 51px);
    overflow: hidden;
    background: linear-gradient(to right, rgba(29, 78, 140, 0.3), rgba(29, 78, 140, 0.0));
    .national-businesses-list-box{
      max-width: 401px;
      height: 100%;
    }

  }
  .system-list {
    height: 100%;
    width: 100%;
    overflow: hidden;
  }
  .system-list-item {
    margin-bottom: 19px;
    width: 100%;
    cursor: pointer;
    .status-header-bar {
      width: 100%;
      //width: 417px;
      height: calc(38 / 19.2 * 1vw);
      max-height: 38px;
      background: rgba(94, 140, 199, 0.5);
      display: flex;
      justify-content: space-between;
      align-items: center;

      .status-header-bar-left {
        font-weight: 400;
        font-size: calc(16 / 19.2 * 1vw);
        color: #FEFEFF;
        opacity: 0.95;
        margin-left: calc(13 / 19.2 * 1vw);
        line-height: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        @media (min-width: 1920px) {
          font-size: 16px;
        }
      }

      .status-header-bar-right {
        width: calc(53 / 19.2 * 1vw);
        max-width: 53px;
        height: calc(23 / 19.2 * 1vw);
        max-height: 23px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 400;
        font-size: calc(14 / 19.2 * 1vw);
        color: #FFFFFF;
        margin-right: calc(40 / 19.2 * 1vw);
        @media (min-width: 1920px) {
          font-size: 14px;
          margin-right: 40px;
        }
      }
    }

    .status-body {
      width: 100%;
      height: 0;
      padding-top: 52.25%;
      border: 1px solid rgba(244, 244, 244, 0.69);
      background-size: 100% 100%;
    }
  }

  .scroll-warp {
    height: 100%;
    width: 100%;
    overflow: hidden;
  }
}
</style>