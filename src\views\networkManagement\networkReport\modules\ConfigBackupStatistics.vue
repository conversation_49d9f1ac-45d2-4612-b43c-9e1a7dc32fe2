<template>
  <div style='height: 100%'>
    <card-frame>
      <div slot='titleSlot' class='title'>
        <yq-icon class='icon' type="netConfig"></yq-icon>
        <span class='text'>配置备份汇总</span>
      </div>
      <div slot='bodySlot' class='body-height' v-if='chartData.length>0'>
        <pieChart :name="'配置备份汇总'"  :chartData='chartData'></pieChart>
      </div>
      <div slot='bodySlot' class='body-height body-empty' v-else>
        <a-spin :spinning='loading' v-if='loading'></a-spin>
        <a-list :data-source="[]" v-else/>
      </div>
    </card-frame>
  </div>
</template>
<script>
import { getAction } from '@api/manage'
import yqIcon from '@comp/tools/SvgIcon'
import cardFrame from '@views/networkManagement/networkReport/modules/CardFrame.vue'
import pieChart from '@views/networkManagement/networkReport/modules/MulElementsPieChart.vue'
export default {
  name: "ConfigBackupStatistics",
  components: { cardFrame, yqIcon,pieChart },
  data() {
    return {
      loading:false,
      chartData:[],
      dictCode : 'Network',
      url: {
        requestDataUrl: '/net/device/devConfigureBackCount',
      }
    }
  },
  created() {
    this.getChartData()
  },
  methods: {
    getChartData() {
      this.loading=true
      this.chartData=[]
      getAction(this.url.requestDataUrl,{dictCode:this.dictCode}).then((res) => {
        if (res.success) {
          let s=res.result.success?res.result.success:0
          let f=res.result.failed?res.result.failed:0
          let n=res.result.none?res.result.none:0
          if(s!=0||f!=0||n!=0){
            this.chartData = [{name:'备份成功',value:s},{name:'备份失败',value:f},{name:'未备份',value:n}]
          }
        } else {
          this.$message.error(res.message)
        }
        this.loading=false
      }).catch((err)=>{
        this.$message.error(err.message)
        this.loading=false
      })
    },
  }
}
</script>