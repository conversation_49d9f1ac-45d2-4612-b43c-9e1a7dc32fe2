<template>
  <!-- 历史告警区域分布 -->
  <div class="view">
    <div class="tab-view">
      <a-tree-select
        v-model="defaultValue"
        tree-node-filter-prop="title"
        :treeData="roomList"
        :replaceFields="replaceFields"
        style="width: 2rem;margin-left:0.12rem;"
        :dropdownStyle="{ maxHeight: '240px', overflow: 'auto',maxWidth: '300px' }"
        placeholder="请选择机房"
        :allow-clear='false'
        @change="changeRoom"
      ></a-tree-select>
    </div>
    <div v-if="chartData.length===0">
      <a-list :data-source="[]" />
    </div>
    <div ref="roomAlarmStatistics" style="height: 100%;width: 100%;" v-else></div>
  </div>
</template>
<script>
import Empty from '@/components/oneClickHelp/Empty.vue'
import { getAction } from '@/api/manage'
export default {
  components: {
    Empty
  },
  data() {
    return {
      url: {
        getRoom: '/topo/room/tree',
        list: '/monitor/Overview/alarm/region'
      },
      chartData: [],
      roomList: [],
      defaultValue: undefined,
      replaceFields: {
        children: 'children',
        title: 'name',
        key: 'id',
        value: 'id'
      }
    }
  },
  props: {
    timeData: {
      type: Object,
      default: () => {}
    }
  },
  watch: {
    timeData: {
      deep: true,
      handler(val) {
        this.getData()
      }
    }
  },
  mounted() {
    this.getRoom()
  },
  methods: {
    changeRoom(e) {
      this.defaultValue = e
      this.getData(e)
    },
    setRoomList(list) {
      for(let i =0 ; i < list.length; i++) {
        list[i].disabled = list[i].type !== "room"
        if(!list[i].disabled) {
          this.roomList.push(list[i])
        }
        if(list[i].children) {
          this.setRoomList(list[i].children)
        }
      }
    },
    getRoom() {
      this.roomList=[]
      getAction(this.url.getRoom).then(res => {
        if (res.result) {
          if (res.result.length > 0) {
            this.setRoomList(res.result)
            this.defaultValue = this.roomList[0].id
            this.getData()
          }
        }
      })
    },
    getData(id) {
      if (this.roomList.length == 0) {
        return
      }
      if (!this.defaultValue) {
        if (this.myChart) {
          this.chartData = []
          this.myChart.dispose()
        }
        return
      }
      getAction(this.url.list, {
        startTime: this.timeData.startTime?this.timeData.startTime:null,
        endTime: this.timeData.endTime?this.timeData.endTime:null,
        roomId: this.defaultValue
      }).then(res => {
        if (res.code == 200) {
          this.chartData = res.result
          if (this.chartData.length > 0) {
            this.$nextTick(() => {
              this.initChart(res.result)
            })
          } else {
            if (this.myChart) {
              this.myChart.dispose()
            }
          }
        }
      })
    },
    initChart(data) {
      if (data.length == 0) {
        if (this.myChart) {
          this.myChart.clear()
        }
        // 显示暂无数据
        return
      }
      let xData = data.map(item => item.alarmDate)
      let arr = Object.keys(data[0].alarmLevels).filter(val=>val!='null')
      let series = []
      arr.map((key, i) => {
        series.push({
          name: key || '未知',
          type: 'bar',
          barGap: '8%',
          barWidth: 10,
          data: data.map(item => item.alarmLevels[key].alarmCount),
          itemStyle: {
            normal: {
              color: data[0].alarmLevels[key].color
            }
          }
        })
      })

      this.myChart = this.$echarts.init(this.$refs.roomAlarmStatistics)
      this.myChart.clear()
      let option = {
        grid: {
          top: '22%',
          left: '2%',
          right: '1%',
          bottom: 0,
          containLabel: true
        },
        legend: {
          type: 'scroll',
          icon: 'rect',
          left: 0,
          top: 0,
          show: true,
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            color: '#4E5969',
            fontSize: 12
          }
        },
        tooltip: {
          show: true,
          formatter: function(params) {
            let html = ``
            let newStr = params.name.replace(/(.+?)\-(.+?)\-(.+)/, '$1年$2月$3日')
            html = `${newStr}</br>${params.seriesName}有${params.value}条`
            return html
          }
        },
        dataZoom: [
          {
            id: 'dataZoomY',
            xAxisIndex: [0],
            show: false, //是否显示滑动条，不影响使用
            type: 'slider', // 这个 dataZoom 组件是 slider 型 dataZoom 组件
            startValue: 0, // 从头开始。
            endValue: 6,
            zoomLock: true,
            showDataShadow: false, //是否显示数据阴影 默认auto
            backgroundColor: 'rgba(255,255,255,0)',
            showDetail: false, //即拖拽时候是否显示详细数值信息 默认true
            realtime: true, //是否实时更新
            filterMode: 'filter',
            handleIcon: 'circle',
            handleStyle: {
              color: 'rgba(205,205,205,1)',
              borderColor: 'rgba(205,205,205,1)'
            },
            moveHandleSize: 0,
            brushSelect: false //刷选功能，设为false可以防止拖动条长度改变 ************（这是一个坑）
          },
          {
            type: 'inside',
            xAxisIndex: 0,
            zoomOnMouseWheel: false, //滚轮是否触发缩放
            moveOnMouseMove: true, //鼠标滚轮触发滚动
            moveOnMouseWheel: true
          }
        ],
        xAxis: {
          data: xData,
          axisLabel: {
            color: 'rgba(0,0,0,0.45)',
            fontSize: 10
          },
          axisTick: {
            show: false
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: 'rgba(0,0,0,0.3)'
            }
          }
        },
        yAxis: [
          {
            type: 'value',
            axisLabel: {
              color: 'rgba(0,0,0,0.45)',
              fontSize: 10
            },
            axisLine: {
              show: false
            },
            splitLine: {
              lineStyle: {
                color: 'rgba(0,0,0,0.12)',
                type: 'solid'
              }
            },
            axisTick: {
              show: false
            }
          }
        ],
        series: series
      }

      this.myChart.setOption(option)
      window.addEventListener('resize', () => {
        this.myChart.resize()
      })
    }
  }
}
</script>
<style scoped lang="less">
::-webkit-scrollbar {
  display: none;
  /*隐藏滚动条*/
}
.view {
  .roomView {
    position: absolute;
    top: 30px;
    right: 0;
    width: 200px;
    overflow: hidden;
    .content {
      display: flex;
      flex-direction: row;
      .item {
        width: 100%;
        // height: 20px;
        font-size: 12px;
        color: #000;
        margin-bottom: 5px;
        padding: 2px 10px;
        .name {
          cursor: pointer;
          overflow: hidden; /*溢出的部分隐藏*/
          white-space: nowrap; /*文本不换行*/
          text-overflow: ellipsis; /*ellipsis:文本溢出显示省略号（...）；clip：不显示省略标记（...），而是简单的裁切*/
        }

        &.active {
          color: #fff;
          background-color: #749cf3;
        }
      }
    }
  }
}
.tab-view {
  height: 30px;
  font-size: 0.175rem;
  position: absolute;
  top: 0.25rem;
  right: 0.25rem;
}
::v-deep .ant-tabs-bar {
  border-bottom: none;
}
::v-deep .ant-tabs-nav .ant-tabs-tab {
  padding: 0;
}
::v-deep .ant-tabs-ink-bar {
  width: 0;
  height: 0;
}
::v-deep .ant-tabs-nav .ant-tabs-tab {
  padding: 0 10px 1px 10px;
  margin: 0 4px 0 0;
  color: #000;
}
// tab选中的样式
::v-deep .ant-tabs-tab-active {
  color: #fff !important;
  background-color: #749cf3;
}
</style>