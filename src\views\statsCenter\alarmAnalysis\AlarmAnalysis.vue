<template>
  <div class="body">
    <background-card>
      <a-row slot='big-screen-content' class="home-row">
        <a-col class="home-col-side">
          <div class="left-top">
            <fold-line ref="trendLine" :day1="'7'" :day2="'30'" @alarmTrend="alarmTrend" @alarmTrend1="alarmTrend1">
            </fold-line>
          </div>
          <div class="left-bottom">
            <product-top10></product-top10>
          </div>
        </a-col>
        <a-col class="home-col-middle">
          <div class="core-core">
            <alarm-all></alarm-all>
          </div>
        </a-col>
        <a-col class="home-col-side">
          <div class="right-top">
            <distribution></distribution>
          </div>
          <div class="right-bottom">
            <alarm-num></alarm-num>
          </div>
        </a-col>
      </a-row>
    </background-card>
  </div>
</template>
<script>
  import echarts from 'echarts/lib/echarts'
  import foldLine from '@views/statsCenter/alarmAnalysis/modules/foldLine.vue'
  import productTop10 from '@views/statsCenter/alarmAnalysis/modules/productTop10.vue'
  import alarmAll from '@views/statsCenter/alarmAnalysis/modules/alarmAll.vue'
  import alarmNum from '@views/statsCenter/alarmAnalysis/modules/alarmNum.vue'
  import distribution from '@views/statsCenter/alarmAnalysis/modules/distribution.vue'
  import backgroundCard from '@views/statsCenter/com/backgroundCard.vue'
  import vueSeamlessScroll from 'vue-seamless-scroll'
  import {
    deleteAction,
    getAction,
    putAction,
    httpAction
  } from '@/api/manage'
  export default {
    components: {
      vueSeamlessScroll,
      foldLine,
      alarmNum,
      distribution,
      alarmAll,
      productTop10,
      backgroundCard
    },
    data() {
      return {
        screenHeight: window.innerHeight,
        trendUrl: '/data-analysis/alarm/alarmLevelTrend',
      }
    },
    mounted() {
      window.addEventListener('resize', this.handleResize, true)
    },
    methods: {
      handleResize() {
        this.screenHeight = window.innerHeight
      },
      // 本月告警趋势分析折线图数据
      alarmTrend(day) {
        getAction(this.trendUrl, {
          isDay: 1,
          time: day
        }).then((res) => {
          if (res.code == 200) {
            let alarmTime = res.result.time
            let alarmTitle = res.result.title
            let alarmValue = res.result.value
            let alarmColor = []
            res.result.value.forEach((ele) => {
              alarmColor.push(ele.color)
            })
            this.$refs.trendLine.warningTrendLineChart(alarmTime, alarmTitle, alarmValue, alarmColor)
          }
        })
      },
      // 本月告警趋势分析折线图数据
      alarmTrend1(day) {
        getAction(this.trendUrl, {
          isDay: 0,
          time: day
        }).then((res) => {
          if (res.code == 200) {
            let alarmTime = res.result.time
            let alarmTitle = res.result.title
            let alarmValue = res.result.value
            let alarmColor = []
            res.result.value.forEach((ele) => {
              alarmColor.push(ele.color)
            })
            this.$refs.trendLine.warningTrendLineChart(alarmTime, alarmTitle, alarmValue, alarmColor)
          }
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  .topTitle {
    height: 16%;
    display: flex;
    align-items: center;
    font-size: 0.225rem
      /* 18/80 */
    ;
    color: #45c5e0;
    padding-left: 0.15rem
      /* 12/80 */
    ;
    letter-spacing: 0.025rem
      /* 2/80 */
    ;
  }

  .body {
    padding: 0rem
      /* 20/80 */
      0.2rem 0.1125rem 0.2rem;
    width: 100%;
    height: 100%;
    background-image: url('/statsCenter/alarm/alarmBg.png');
    background-repeat: no-repeat;
    background-size: 90% 100%;
    background-position: center;
    overflow: auto;

    .home-row {
      height: 100%;
      width: 100%;
      display: flex;
      padding: 0 0.2rem;
      flex-flow: row nowrap;
      justify-content: center;
      align-items: start;

      .home-col-side {
        width: 5.25rem; //420/80px
        height: 100%;

        .left-top {
          width: 100%;
          height: 36%;
          background: rgba(250, 250, 250, 0);
          border-radius: 0.075rem
            /* 6/80 */
          ;
        }

        .left-bottom {
          width: 100%;
          margin-top: 30px;
          margin-bottom: 30px;
          height: calc(64% - 60px);
          background: rgba(250, 250, 250, 0);
          border-radius: 0.075rem
            /* 6/80 */
          ;
        }
      }

      .home-col-middle {
        height: 100%;
        width: calc(100% - (5.25rem * 2));
        padding: 0 0.2rem;

        .core-core {
          width: 100%;
          height: 100%;
        }
      }

      .home-col-side {
        width: 5.25rem; //420/80px
        height: 100%;

        .right-top {
          width: 100%;
          height: 44%;
          position: relative;
          background: rgba(250, 250, 250, 0);
          border-radius: 0.075rem
            /* 6/80 */
          ;
        }

        .right-bottom {
          width: 100%;
          height: calc(56% - 60px);
          margin-top: 30px;
          margin-bottom: 30px;
          background: rgba(250, 250, 250, 0);
          border-radius: 0.075rem
            /* 6/80 */
          ;
        }
      }
    }
  }
</style>