<template>
  <a-row :gutter="10" style="height: 100%;" class="vScroll">
    <a-col style="width: 100%; height: 100%; display: flex; flex-direction: column">
      <!-- 查询区域 -->
      <a-card :bordered="false" :bodyStyle="{ paddingBottom: '0' }" style="min-width: 820px;" class="card-style">
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
            <a-row :gutter="24" ref="row">
              <a-col :span="spanValue">
                <a-form-item label="时间范围">
                  <a-range-picker class="a-range-picker-choice-date" @change="onChange" v-model="queryParam.alarmTime"
                    format="YYYY-MM-DD" :placeholder="['开始时间', '截止时间']" />
                </a-form-item>
              </a-col>
              <a-col :span="colBtnsSpan()">
                <span class="table-page-search-submitButtons"
                  :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button class="btn-search btn-search-style" type="primary" @click="searchQuery">查询</a-button>
                  <a-button class="btn-reset btn-reset-style" @click="searchReset">重置</a-button>
                  <a v-if="isVisible" class="btn-updown-style" @click="doToggleSearch">
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered="false" style="width: 100%;min-width: 820px; flex: auto;margin-bottom: 12px;">
        <div class="colorBox">
          <span class="colorTotal">告警级别</span>
        </div>
        <div v-for="(item, index) in alarmList" :key="index">
          <div class="alarm_box">
            <div class="alarm_left">
              <div class="alarmName_box" :style="{ 'background-color': item.alarmColor }">
                <div class="alarmName" :style="item.levelName.length < 3 ? { 'font-size': '16px', margin: '0 0 1px 3px' } : item.levelName.length > 5
                  ? { 'font-size': '8px' } : { 'font-size': '12px' }">
                  {{ item.levelName }}</div>
              </div>
              <div class="alarmIInfo">
                <div>
                  <a-icon type="alert" theme="filled" :style="{ 'font-size': '100px', color: item.alarmColor }" />
                </div>
                <div class="alarmNum_box">
                  <span style="font-size: 14px;color: #909399;">告警数量</span>
                  <span :style="{ color: item.alarmColor }" class="alarmNum">{{ item.alarmCount }}</span>
                </div>
              </div>
            </div>
            <div class="alarm_center" v-if="item.tableList && item.tableList.length > 0">
              <table class="alarm_table">
                <thead>
                  <tr class="alarm_th">
                    <th>设备名称</th>
                    <th>告警策略</th>
                    <th>最近告警时间</th>
                    <th>重复告警次数</th>
                  </tr>
                </thead>
                <tbody>
                  <tr class="alarm_td" v-for="(ele, i) in item.tableList" :key="i">
                    <td>{{ ele.deviceName }}</td>
                    <td>{{ ele.templateName }}</td>
                    <td>{{ ele.alarmTime2 }}</td>
                    <td style="color: #409EFF;">{{ ele.repeatTimes }}</td>
                  </tr>
                </tbody>
              </table>
              <div style="text-align: center; position: relative; margin: 30px 30px 30px auto">
                <a-pagination v-model="item.current" :total="item.total" :defaultPageSize="4"
                  @change="dataChange(item, $event)">
                </a-pagination>
              </div>
            </div>
            <div
              style="height: 100%;width: 65%;min-width: 470px;display: flex;align-items: center;justify-content: center;"
              v-else>
              <a-empty />
            </div>
            <div class="alarm_right">
              <div style="height: 100%;width: 100%;" :id="'alarmPie' + index"></div>
            </div>
          </div>
          <div style="height: 1px; width: 85%;margin: 0 auto;background-color: #E4E4E4;margin-bottom: 30px;"></div>
        </div>
      </a-card>
      <a-card :bordered="false" style="width: 100%;min-width: 820px; flex: auto">
        <div class="colorBox">
          <span class="colorTotal">告警策略</span>
        </div>
        <a-table ref="table" bordered rowKey="id" :columns="columns" :dataSource="dataSource"
          :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}" @change="handleTableChange">
        </a-table>
      </a-card>
    </a-col>
  </a-row>
</template>

<script>
import echarts from 'echarts/lib/echarts'
import {
  JeecgListMixin
} from '@/mixins/JeecgListMixin'
import JEllipsis from '@/components/jeecg/JEllipsis'
import {
  deleteAction,
  getAction,
} from '@/api/manage'
import JDictSelectTag from '@/components/dict/JDictSelectTag.vue'
import {
  YqFormSearchLocation
} from '@/mixins/YqFormSearchLocation'

export default {
  name: 'alarmView',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components: {
    JEllipsis,
    JDictSelectTag,
  },
  data() {
    return {
      alarmList: [],
      alarmData: [],
      tableData: [],
      alarmNum: 0,
      // 告警策略表头
      columns: [{
        title: '序号',
        dataIndex: '',
        key: 'rowIndex',
        isUsed: false,
        customCell: () => {
          let cellStyle = 'text-align:center;width:60px'
          return {
            style: cellStyle
          }
        },
        customRender: function (t, r, index) {
          return parseInt(index) + 1
        },
      }, {
        title: '告警策略',
        dataIndex: 'templateName',
        customCell: () => {
          let cellStyle = 'text-align: center;min-width: 130px;max-width:300px'
          return {
            style: cellStyle
          }
        },
      },
      {
        title: '绑定设备数量',
        dataIndex: 'deviceNum',
        customCell: () => {
          let cellStyle = 'text-align: center;min-width: 130px;max-width:300px'
          return {
            style: cellStyle
          }
        },
      },
      {
        title: '告警数量',
        dataIndex: 'alarmCount',
        customCell: () => {
          let cellStyle = 'text-align: center;min-width: 130px;max-width:300px'
          return {
            style: cellStyle
          }
        },
      },
      {
        title: '最近告警时间',
        dataIndex: 'alarmTime',
        customCell: () => {
          let cellStyle = 'text-align: center;min-width: 150px;max-width:300px'
          return {
            style: cellStyle
          }
        },
      },
      {
        title: '重复次数',
        dataIndex: 'repeatTimes',
        customCell: () => {
          let cellStyle = 'text-align: center;min-width: 150px;max-width:300px'
          return {
            style: cellStyle
          }
        },
      },
      ],
      url: {
        list: 'alarm/view/alarmTemplateView',
        alarmStatistic: 'alarm/view/alarmLevelStatistic',
        alarmView: 'alarm/view/alarmLevelView'
      },
    }
  },
  created() {
    this.getAlarmList()
  },
  mounted() { },
  methods: {
    dataChange(item, e) {
      this.alarmList.forEach((ele, i) => {
        if (this.alarmList[i].alarmLevel == item.alarmLevel) {
          this.alarmList[i].current = e
          this.getData(item.alarmLevel, i, e)
        }
      })
    },
    getData(level, index, pagination) {
      this.queryParam.level = level
      this.queryParam.pageSize = 4
      this.queryParam.pageNo = pagination ? pagination : 1
      let formData = this.queryParam
      getAction(this.url.alarmView,
        formData
      ).then((res) => {
        if (res.success) {
          if (pagination && pagination != null) {
            this.alarmList[index].tableList = res.result.records
            this.alarmList[index].current = pagination
            this.alarmList[index].total = res.result.total
          } else {
            this.alarmList[index].tableList = res.result.records
            this.alarmList[index].current = 1
            this.alarmList[index].total = res.result.total
          }
          this.$forceUpdate()
        }
      })
    },
    getAlarmList() {
      this.alarmNum = 0
      this.alarmList = []
      getAction(this.url.alarmStatistic, {
        startTime: this.queryParam.startTime,
        endTime: this.queryParam.endTime
      }).then((res) => {
        this.alarmNum = 0
        this.alarmList = []
        if (res.success && res.result && res.result.length > 0) {
          this.alarmList = res.result
          this.alarmList.forEach((ele, index) => {
            if (this.alarmList[index].levelName == null) {
              this.alarmList.splice(index, 1)
            }
            this.alarmNum += Number(ele.alarmCount)
            this.getData(ele.alarmLevel, index)
            this.$nextTick(() => {
              this.getPie(ele, index)
            })
          })
        }
      })
    },
    getPie(data, index) {
      let num = this.alarmNum - data.alarmCount
      let myChart = this.$echarts.init(document.getElementById('alarmPie' + index))
      myChart.setOption({
        tooltip: {
          trigger: "item",
          formatter: "{a} <br/>{b} : {c}({d}%)",
        },
        series: [{
          name: "告警占比",
          type: "pie",
          radius: '60%',
          center: ['50%', '40%'],
          labelLine: {
            show: false
          },
          label: {
            show: false
          },
          data: [{
            value: data.alarmCount,
            name: data.levelName
          },
          {
            value: num,
            name: "其他"
          },
          ],
          itemStyle: {
            normal: {
              borderWidth: 7,
              borderColor: '#ffffff',
            },
            emphasis: {
              borderWidth: 0,
              shadowBlur: 0,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          color: [
            data.alarmColor,
            '#409EFF',
          ],
        }],
      })
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    },
    searchQuery() {
      this.getAlarmList()
      this.loadData(1)
    },
    searchReset() {
      this.queryParam = {}
      this.searchQuery()
    },
    onChange(date, dateString) {
      this.queryParam.startTime = dateString[0]
      this.queryParam.endTime = dateString[1]
    },
  },
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';

.colorBox {
  margin-bottom: 18px;

  .colorTotal {
    font-size: 16px;
    color: #252631;
    font-weight: 500;
  }
}

.alarm_box {
  display: flex;
  justify-content: space-between;
  height: 300px;
  padding: 0 20px;
  background-color: #FFFFFF;

  .alarm_left {
    height: 100%;
    width: 17%;
    min-width: 168px;
    display: flex;
    justify-content: space-between;

    .alarmName_box {
      width: 60px;
      height: 60px;
      clip-path: polygon(100% 0, 0 100%, 0 0);

      .alarmName {
        transform: rotate(-45deg);
        color: #ffffff;
        font-size: 14px;
        margin-top: 5px;
        margin-left: -5px;
      }
    }

    .alarmIInfo {
      height: 100%;
      width: calc(100% - 60px);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      margin-top: -40px;

      .alarmNum_box {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 10px;

        .alarmNum {
          font-size: 28px;
          margin-left: 20px;
          font-weight: 700;
        }
      }
    }

  }

  .alarm_center {
    height: 100%;
    width: 65%;
    min-width: 470px;

    .alarm_table {
      width: 100%;
      height: 50px;
      text-align: center;


      .alarm_th {
        font-size: 16px;
        color: #010101;
        font-weight: 400;
        line-height: 2;
      }

      .alarm_td {
        font-size: 14px;
        color: #909399;
        font-weight: 400;
        line-height: 3;
      }
    }
  }

  .alarm_right {
    height: 100%;
    width: 18%;
    min-width: 150px;
  }

  ::deep.ant-table-tbody .ant-table-row td {
    padding-top: 10px;
    padding-bottom: 10px;
  }
}
</style>