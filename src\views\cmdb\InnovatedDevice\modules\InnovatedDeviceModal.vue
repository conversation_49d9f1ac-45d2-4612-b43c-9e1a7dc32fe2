<template>
  <j-modal
    :title="title"
    :width="modalWidth"
    :visible="visible"
    :centered='true'
    switchFullscreen
    :destroyOnClose='true'
    @ok="handleOk"
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @cancel="handleCancel"
    cancelText="关闭"
  >
    <a-spin :spinning='confirmLoading'>
      <j-form-container :disabled='disableSubmit'>
        <a-form-model ref='form' slot='detail' :model='model' :rules='validatorRules' :labelCol='labelCol' :wrapperCol='wrapperCol'>
          <a-row>
            <a-col :span='24'>
              <a-form-model-item  class='two-words' label='型号' prop='model'>
                <a-input v-model='model.model' :allowClear='true'
                         autocomplete='off' placeholder='请输入型号'/>
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
              <a-form-model-item  class='two-words' label='产品分类' prop='productCategoryId'>
                <j-tree-select-expand v-model="model.productCategoryId" show-search treeNodeFilterProp="title"
                                      placeholder="请选择产品分类" dict="cmdb_assets_category,category_name,id" pidField="parent_id"
                                      condition='{"delflag":0,"category_state":"0","is_monitorable":"1"}' pidValue="0"
                />
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
              <a-form-model-item label='供应商' prop='supplier'>
                <a-select
                  :disabled='disableSupplier'
                  v-model="model.supplier"
                  :allow-clear='true'
                  :show-search='true'
                  option-filter-prop='label'
                  placeholder='请选择供应商'>
                  <a-select-option v-for='item in supplierList' :key='"supplier_"+item.id' :label='item.name' :value='item.id'>
                    {{ item.name }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span='24'>
              <a-form-model-item label='所属期' prop='catalogue'>
                <j-dict-select-tag v-model="model.catalogue" placeholder="请选择所属期"
                                   dictCode="innovatedDeviceCatalogue" />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </j-form-container>
    </a-spin>
  </j-modal>
</template>
<script>

import { getAction, httpAction } from '@api/manage'

export default {
  name: 'InnovatedDeviceModal',
  components: {},
  data() {
    return {
      title: '',
      modalWidth: '800px',
      visible: false,
      disableSupplier:false,
      disableSubmit: false,
      confirmLoading: false,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      model: {},
      supplierList: [],
      validatorRules: {
      /*  model: [{
          required: true,
          pattern: /^([a-zA-Z][a-zA-Z0-9_]{4,63})$/,
          message: '以字母开头，可包含字母、数字或下划线，5-64个字符',
          trigger: 'blur'
        }],*/
        model: [{required: true,message: '请输入型号！'},{ min: 2, max: 100, message: '型号长度在2-100个字符之间' },],
        productCategoryId: [{ required: true, message: '请输入产品分类!' }],
        supplier: [{ required: true, message: '请选择供应商!' }],
        catalogue: [{ required: true, message: '请选择所属期!' }]
      },
      url: {
        add: '/itInnovate/cmdbItInnovate/add',
        edit: '/itInnovate/cmdbItInnovate/edit',
        supplier: '/supplier/cmdbSupplier/queryAllSupplier',//获取供应商下拉数据
      }
    }
  },
  created() {
    this.getAllSupplier()
  },
  methods: {
    add(record) {
      this.edit(record)
    },
    edit(record) {
      this.visible = true
      this.$nextTick(() => {
        this.model=Object.assign({}, record)
      })
    },
    close() {
      this.$emit('close');
      this.visible = false;
    },
    handleOk() {
      const that = this
      that.$refs.form.validate((err, value) => {
        if (err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!that.model.id) {
            httpurl += that.url.add
            method = 'post'
          } else {
            httpurl += that.url.edit
            method = 'put'
          }
          let formData = { ...that.model}

          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
                that.close()
              } else {
                that.$message.warning(res.message)
              }
              that.confirmLoading = false
            }).catch((res) => {
            that.$message.warning(res.message)
            that.confirmLoading = false
          })
        }
      })
    },
    handleCancel() {
      this.close()
    },
    /*获取供应商下拉数据*/
    getAllSupplier() {
      return new Promise((resolve, reject) => {
        getAction(this.url.supplier).then((res) => {
          if (res.success) {
            this.supplierList = res.result
            resolve({ success: true, message: res.message })
          } else {
            reject({ success: false, message: res.message })
          }
        }).catch((err) => {
          reject({ success: false, message: err.message })
        })
      })
    },
  }
}
</script>
<style scoped lang='less'>
@import '~@assets/less/normalModal.less';
</style>