<template>
  <div style="height: 100%">
    <keep-alive exclude='UnitSubmissionList'>
      <component :is="pageName" :data="data" :p-task-info="taskInfo" :p-node-info="nodeInfo"/>
    </keep-alive>
<!--    <component :is="pageName" :data="data" :routeType="'organization'" />-->
  </div>
</template>
<script>
import operationalOrganizationDetail from './modules/operationalOrganizationDetail'
import operationalOrganizationList from './operationalOrganizationList'
import UnitSubmissionList from './modules/UnitSubmissionList'
export default {
  name: 'operationalOrganizationManage',
  data() {
    return {
      isActive: 0,
      data: {},
      taskInfo:{},
      nodeInfo:{}
    }
  },
  components: {
    operationalOrganizationList,
    UnitSubmissionList,
   operationalOrganizationDetail
  },
  created() {
    this.pButton1(0)
  },
  //使用计算属性
  computed: {
    pageName() {
      switch (this.isActive) {
        case 0:
          return 'operationalOrganizationList'
        break;
        case 1:
          return 'operationalOrganizationDetail'
          break;
        default:
          return 'UnitSubmissionList'
          break
      }
    }
  },
  methods: {
    pButton1(index) {
      this.isActive = index
    },
    pButton2(index, item) {
      this.isActive = index
      this.data = item
    },
    pButton3(index, item) {
      this.isActive = index
      this.nodeInfo=item.nodeInfo
      this.taskInfo=item.taskInfo
    }
  }
}
</script>
