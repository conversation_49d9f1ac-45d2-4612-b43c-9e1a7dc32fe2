<template>
  <div style="height:100%">
    <keep-alive exclude='AlarmAssetsModal'>
      <component :is="pageName" :data="data"/>
    </keep-alive>
  </div>
</template>
<script>
  import AlarmAssetsList from './AlarmAssetsList'
  import AlarmAssetsModal from './modules/AlarmAssetsModal'
  export default {
    name: "AlarmAssetsManage",
    data() {
      return {
        isActive: 0,
        data:{}
      };
    },
    components: {
      AlarmAssetsList,
      AlarmAssetsModal
    },
    created(){
      this.pButton1(0);
    },
    //使用计算属性
    computed: {
      pageName() {
        switch (this.isActive) {
          case 0:
            return "AlarmAssetsList";
            break;

          default:
            return "AlarmAssetsModal";
            break;
        }
      }
    },
    methods: {
      pButton1(index,item) {
        this.isActive = index;
        this.data = item;
      },
      pButton2(index,item) {
        this.isActive = index;
        this.data = item;
      }
    }
  }
</script>