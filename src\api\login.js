import api from './index'
import { axios } from '@/utils/request'
import  Vue from 'vue'
/**
 * login func
 * parameter: {
 *     username: '',
 *     password: '',
 *     remember_me: true,
 *     captcha: '12345'
 * }
 * @param parameter
 * @returns {*}
 */
export function login(parameter) {
  return axios({
    url: '/sys/login',
    method: 'post',
    data: parameter
  })
}
//运维助手登录
export function oneClickHelpLogin(parameter) {
  return axios({
    url: '/sys/terminalLogin',
    method: 'post',
    data: parameter
  })
}
//运维助手自动登录
export function oneClickHelpAutoLogin(parameter) {
  return axios({
    url: '/sys/terminalAutoLogin',
    method: 'post',
    data: parameter
  })
}
export function UkeyLogin(parameter) {
  return axios({
    url: '/sys/UkeyLogin',
    method: 'post',
    data: parameter
  })
}

export function phoneLogin(parameter) {
  return axios({
    url: '/sys/phoneLogin',
    method: 'post',
    data: parameter
  })
}

export function getSmsCaptcha(parameter) {
  return axios({
    url: api.SendSms,
    method: 'post',
    data: parameter
  })
}

// export function getInfo() {
//   return axios({
//     url: '/api/user/info',
//     method: 'get',
//     headers: {
//       'Content-Type': 'application/json;charset=UTF-8'
//     }
//   })
// }

export function logout(logoutToken) {
  return axios({
    url: '/sys/logout',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
      'X-Access-Token':  logoutToken
    }
  })
}

/**
 * 第三方登录
 * @param token
 * @param thirdType
 * @returns {*}
 */
export function thirdLogin(token,thirdType) {
  return axios({
    url: `/sys/thirdLogin/getLoginUser/${token}/${thirdType}`,
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/*
从其他平台跨入本平台的登录
 */
export function autoLogin(parameter) {
  return axios({
    url: '/sys/autoLogin',
    method: 'post',
    data: parameter
  })
}

export function UkeyLoginBT(url,parameter) {
  return axios({
    url: url,
    method: 'post',
    data: parameter
  })
}
export function SsoAccessToken(parameter) {
  return axios({
    url: "/sys/zyLogin",
    method: 'get',
    params: parameter
  })
}

export function SsoLogout(token,parameter) {
  console.log("SsoLogout",window.config.customization?.cz_zhongruan?.logoutUrl)
  let exitUrl = window.config.customization?.cz_zhongruan?.exitUrl;
  return new Promise(resolve=>{
    axios({
      url: window.config.customization?.cz_zhongruan?.logoutUrl+"/"+token,
      method: 'post',
      data: parameter,
      headers: {
        'client_ip': window.config.customization?.cz_zhongruan?.client_ip
      }
    }).then(res=>{
      //成功
      if (res.rsltcode == '0'){
        if(exitUrl){
          window.location.href = exitUrl;
        }else if(window.close){
          window.close();
        }
      }else{
        //失败
        Vue.prototype.$message.error(res.rsltmsg)
      }

    }).catch(err=>{
      //失败
      // let ww = window.open("http://localhost:3000/#/user/login","_blank")
      // setTimeout(()=>{
      //   console.log("SsoLogout*****")
      //   ww.close()
      // },1000*20)
      Vue.prototype.$message.error({
        content: "注销接口请求失败！",
        duration: 3,
       /* onClose(){
          window.location.href = exitUrl;
        }*/
      })
    })
  })


}