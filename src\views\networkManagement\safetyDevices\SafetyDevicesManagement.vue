<template>
  <div style="height:100%">
    <keep-alive exclude='DeviceInfoModal'>
      <component style="height:100%" :is="pageName" :data="data" :is-editing='false' :render-states='renderStates'/>
    </keep-alive>
  </div>
</template>
<script>
import SafetyDeviceList from './SafetyDeviceList.vue'
import DeviceInfoModal from '@views/devicesystem/deviceshow/DeviceInfoModal.vue'
export default {
  name: "SafetyDevicesManagement",
  data() {
    return {
      isActive: 0,
      data: {},
      renderStates:{
        showBaseInfo:true,
        showStateInfo:true,
        showDeviceFunction:true,
        showJournal:true,
        showAlarm:true,
        showDeviceAlarm:true,
      }
    };
  },
  components: {
    SafetyDeviceList,
    DeviceInfoModal
  },
  created() {
    this.pButton1(0);
  },
  //使用计算属性
  computed: {
    pageName() {
      switch (this.isActive) {
        case 0:
          return "SafetyDeviceList";
        default:
          return "DeviceInfoModal";
      }
    }
  },
  methods: {
    pButton1(index) {
      this.isActive = index;
    },
    pButton2(index, item) {
      this.isActive = index;
      this.data = item
    }
  }
}
</script>