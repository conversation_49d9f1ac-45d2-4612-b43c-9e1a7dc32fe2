<template>
  <div style='height: 100%;overflow: hidden;overflow-y: auto'>
    <div class='header-info'>
      <p class='p-info-title'>
        <span class="title">
          <span>{{ record.assetsName }}</span>
            <span class='span-status'>
              <img v-if="record.alarmType === '1'" src='~@/assets/img/alarm-red.png' />
              <img v-else src='~@/assets/img/alarm-yew.png' />
              <span class='status'>{{ record.alarmType === '1' ? '已过期' : '即将过期' }}</span>
            </span>
        </span>
        <span class="back">
          <img alt='' src='~@/assets/return1.png' @click='getGo'>
        </span>
      </p>
      <p class='p-info-product'>
        <span class='span-assets'>资产编号：{{ record.assetsCode }}</span>
        <span class='span-assets'>资产名称：{{ record.assetsName }}</span>
      </p>
    </div>
    <div class='alarm-info'>
      <alarm-assets-info ref='alarmInfo'></alarm-assets-info>
    </div>
  </div>
</template>

<script>
import AlarmAssetsInfo from './AlarmAssetsInfo.vue'

export default {
  inject: ['reload'],
  name: 'AlarmAssetsModal',
  data() {
    return {
      record: {}
    }
  },
  components: {
    AlarmAssetsInfo
  },
  props: {
    data: {
      type: Object
    }
  },
  watch: {
    data: function(val, oldVal) {
      this.record = val
      this.$refs.alarmInfo.show(val)
    }
  },
  mounted() {
    this.record = this.data
    this.$refs.alarmInfo.show(this.data)
  },
  methods: {
    //返回上一级
    getGo() {
      this.$parent.pButton2(0)
    }
  }
}
</script>

<style scoped lang="less">
.header-info {
  background-color: white;
  padding: 24px;
  margin-bottom: 16px;
  border-radius: 3px;

  .p-info-title {
    display: flex;
    align-items: start;
    justify-content: space-between;
    flex-flow: row nowrap;
    margin-bottom: 6px;
    font-size: 18px;
    color: #000000;

    .title {
      display: inline-block;
      width: calc(100% - 30px);

      & > span {
        padding-right: 10px;
      }

      .span-status {
        white-space: nowrap;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);

        img {
          width: 6px;
          height: 6px;
        }

        .status {
          padding-left: 4px;
        }
      }
    }

    .back {
      display: inline-block;
      width: 30px;
      text-align: right;

      img {
        width: 20px;
        height: 20px;
        cursor: pointer
      }
    }
  }

  .p-info-product {
    display: flex;
    align-items: center;
    justify-content: start;
    flex-flow: row wrap;
    margin-bottom: 0px;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);

    .span-assets {
      margin-right: 50px;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
  }
}

.alarm-info {
  background-color: white;
  padding: 24px;
  border-radius: 3px;
}
</style>
