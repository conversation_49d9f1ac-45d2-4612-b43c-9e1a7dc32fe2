<template>
  <a-modal :maskClosable='false' :title='title' :width="1200" :visible="visible" :confirmLoading="confirmLoading"
    :destroyOnClose="true" @cancel="handleCancel" cancelText="关闭">
    <a-spin :spinning="confirmLoading">
      <a-descriptions bordered title="任务信息" size="small">
        <a-descriptions-item label="任务ID">{{ model.id }}</a-descriptions-item>
        <a-descriptions-item label="任务名称">{{ model.name }}</a-descriptions-item>
        <a-descriptions-item label="任务分类">{{ model.category }}</a-descriptions-item>
        <a-descriptions-item label="任务描述">{{ model.description }}</a-descriptions-item>
        <a-descriptions-item label="所有人">{{ model.owner }}</a-descriptions-item>
        <a-descriptions-item label="所有人姓名">{{ model.ownerName }}</a-descriptions-item>
        <a-descriptions-item label="认领时间">{{ model.claimTime }}</a-descriptions-item>
        <a-descriptions-item label="执行人">{{ model.assignee }}</a-descriptions-item>
        <a-descriptions-item label="执行人姓名">{{ model.assigneeName }}</a-descriptions-item>
        <a-descriptions-item label="到期日">{{ model.dueDate }}</a-descriptions-item>

        <a-descriptions-item label="结束时间">{{ model.endTime }}</a-descriptions-item>
        <a-descriptions-item label="状态">
          <a-badge status="success" text="激活" v-if="!model.suspended" />
          <a-badge status="error" text="挂起" v-else />
        </a-descriptions-item>
        <a-descriptions-item label="持续时间">{{ model.durationInMillis }}</a-descriptions-item>
        <a-descriptions-item label="工作时间">{{ model.workTimeInMillis }}</a-descriptions-item>
        <a-descriptions-item label="优先级">{{ model.priority }}</a-descriptions-item>
        <a-descriptions-item label="任务编码">{{ model.taskDefinitionKey }}</a-descriptions-item>
        <a-descriptions-item label="流程实例ID">{{ model.processInstanceId }}</a-descriptions-item>
        <a-descriptions-item label="业务标题">{{ model.processInstanceName }}</a-descriptions-item>
        <a-descriptions-item label="流程ID">{{ model.processDefinitionId }}</a-descriptions-item>
        <a-descriptions-item label="流程名称">{{ model.processDefinitionName }}</a-descriptions-item>
        <a-descriptions-item label="流程描述">{{ model.processDefinitionDescription!="null"?model.processDefinitionDescription:"" }}</a-descriptions-item>
        <a-descriptions-item label="流程编码">{{ model.processDefinitionKey }}</a-descriptions-item>
        <a-descriptions-item label="流程分类">{{ filterDictText(dictOptions,model.processDefinitionCategory) }}</a-descriptions-item>
        <a-descriptions-item label="流程版本">{{ model.processDefinitionVersion }}</a-descriptions-item>
        <a-descriptions-item label="流程发布ID">{{ model.processDefinitionDeploymentId }}</a-descriptions-item>
        <a-descriptions-item label="表单编码">{{ model.formKey }}</a-descriptions-item>
        <a-descriptions-item label="流程启动人">{{ model.processInstanceStartUserName }}</a-descriptions-item>
        <a-descriptions-item label="发起人完成任务">{{ model.initiatorCanCompleteTask }}</a-descriptions-item>
        <a-descriptions-item label="是否候选组">{{ model.memberOfCandidateGroup==='true'?"是":"否" }}</a-descriptions-item>
        <a-descriptions-item label="是否候选成员">{{ model.memberOfCandidateUsers==='true'?"是":"否" }}</a-descriptions-item>
        <a-descriptions-item label="委派状态">{{ model.delegationState }}</a-descriptions-item>
        <a-descriptions-item label="干系人">{{ model.involvedPeopleNames }}</a-descriptions-item>
      </a-descriptions>
    </a-spin>
    <template slot="footer">
      <a-button @click="handleCancel">关闭</a-button>
    </template>
  </a-modal>
</template>
<script>
  import {
    getAction
  } from '@/api/manage'
  import PageLayout from '@/components/page/PageLayout'

  import DetailList from '@/components/tools/DetailList'
  import { filterDictText, initDictOptions } from '@comp/dict/JDictSelectUtil'

  const DetailListItem = DetailList.Item
  export default {
    name: 'ProcessInstanceDetailModal',
    components: {
      PageLayout,
      DetailList,
      DetailListItem,
    },
    data() {
      return {
        title: '操作',
        visible: false,
        model: {},
        dictOptions: [],
        confirmLoading: false,
        url: {
          add: '/test/jeecgDemo/add',
          edit: '/test/jeecgDemo/edit',
        },
      }
    },
    created() {this.initDictConfig()},
    methods: {
      initDictConfig() {
        //初始化字典 - 流程分类
        initDictOptions('bpm_process_type').then((res) => {
          if (res.success) {
            this.dictOptions = res.result || []
          }
        })
      },
      filterDictText(dictOptions, text) {
        if (dictOptions instanceof Array) {
          for (let dictItem of dictOptions) {
            if (text === dictItem.value) {
              return dictItem.text
            }
          }
        }
        return text
      },
      init(record) {
        console.log(record)
        getAction('/flowable/task/queryById', {
          taskId: record.id
        }).then((res) => {
          console.log(res)
          this.model = Object.assign({}, res.result)
          this.visible = true
        })
      },
      close() {
        //this.$refs.form.resetFields()
        this.$emit('close')
        this.visible = false
      },
      handleCancel() {
        this.close()
      },
    },
  }
</script>
<style lang='less' scoped>
  @import '~@assets/less/YQNormalModal.less';
</style>