<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container>
      <a-form :form="form" slot="detail">
        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item label="元素名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['name']" placeholder="请输入元素名称" :allowClear="true" autocomplete="off" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="元素类别" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['group']" placeholder="请选择元素类别" :allowClear="true" autocomplete="off" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item class="two-words" label="图标" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <!-- <j-image-upload v-decorator="['cmpIcon']"></j-image-upload> -->
              <a-upload action="https://www.mocky.io/v2/5cc8019d300000980a055e76" list-type="picture-card"
                :file-list="fileList" @preview="handlePreview" @change="handleChange">
                <div v-if="fileList.length < 8">
                  <a-icon type="plus" />
                  <div class="ant-upload-text">
                    上传
                  </div>
                </div>
              </a-upload>
              <a-modal :visible="previewVisible" :footer="null" @cancel="handleCancel">
                <img alt="example" style="width: 100%" :src="previewImage" />
              </a-modal>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
  import {
    httpAction,
    getAction
  } from '@/api/manage'
  import pick from 'lodash.pick'
  import {
    validateDuplicateValue
  } from '@/utils/util'
  import JFormContainer from '@/components/jeecg/JFormContainer'
  import JImageUpload from '@/components/jeecg/JImageUpload'
  import {
    getDeviceInfoMap
  } from '@/api/device'

  function getBase64(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result);
      reader.onerror = error => reject(error);
    });
  }

  export default {
    name: 'DeviceInfoForm',
    // mixins: [JeecgListMixin],
    components: {
      JFormContainer,
      JImageUpload,
      getAction,
    },
    data() {
      return {
        previewVisible: false,
        previewImage: '',
        fileList: [],
        form: this.$form.createForm(this),
        model: {
          name: '',
          cmpIcon: '',
        },
        formItemLayout: {
          md: {
            span: 12
          },
          sm: {
            span: 24
          },
        },
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 4
          },
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 18
          },
        },
        confirmLoading: false,
        url: {
          add: '/device/deviceInfo/add',
          edit: '/device/deviceInfo/edit',
        },
      }
    },
    created() {},
    methods: {
      handleCancel() {
        this.previewVisible = false;
      },
      async handlePreview(file) {
        if (!file.url && !file.preview) {
          file.preview = await getBase64(file.originFileObj);
        }
        this.previewImage = file.url || file.preview;
        console.log(this.previewImage, 'this.previewImage');
        this.previewVisible = true;
      },
      handleChange(info) {
        let files = info.fileList
        this.fileList = files.slice(-1)
      },
      add() {
        this.edit({})
      },
      edit(record) {
        this.form.resetFields()
        Object.assign(this.model, record)
        this.visible = true
        this.$nextTick(() => {
          this.form.setFieldsValue(
            pick(
              this.model,
              'name',
              'cmpIcon'
            )
          )
        })
      },
      submitForm() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.model.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'post'
            }
            httpAction(httpurl, info, method)
              .then((res) => {
                if (res.success) {
                  that.$message.success(res.message)
                  that.$emit('ok', {
                    deviceInfo: formData,
                    collectInfoList: that.collectInfoList,
                  })
                } else {
                  that.$message.warning(res.message)
                }
              })
              .finally(() => {
                that.confirmLoading = false
              })
          }
        })
      },
      // popupCallback(row) {
      //   this.form.setFieldsValue(pick(row, 'name', 'cmpIcon'))
      // },
    },
  }
</script>

<style lang='less' scoped='scoped'>

</style>