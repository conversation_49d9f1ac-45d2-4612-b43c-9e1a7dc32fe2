<template>
  <div :id="type+'_chart_'+index" :style="{height: height}"></div>
</template>
<script>

export default {
  name: "rateChart",
  props: {
    //为了规定渲染区域id，避免同页面应用id冲突
    type: {
      type: String,
      required: false,
      default: 'rate'
    },
    //在列表中使用时，有可能每行type取值可能相同，用index进一步规定渲染区域id，避免同页面应用id冲突
    index: {
      type: [Number, String],
      required: false,
      default: 0
    },
    //图表渲染去高度
    height: {
      type: String,
      required: false,
      default: '24px'
    },
    //进度颜色块
    colors: {
      type: Array,
      required: false,
      default: () => ['#5ad989', '#dc9932', '#da3545']
    },
    //进度分块
    limitData: {
      type: Array,
      required: false,
      default: () => [50, 80, 100]
    },
    //当前完成进度
    data: {
      type: Array,
      required: true,
      default: []
    },
    //若data数据中包含单位，此处一定要传单位
    unit: {
      type: String,
      required: false,
      default: '%'
    },
    //进度条最大取值
    max: {
      type: Number,
      required: false,
      default: 100
    },
    //数据显示位置
    position: {
      type: String,
      required: false,
      default: 'top'
    },
    //bar类型：分块填充，整块填充
    barType:{
      type: String,
      required: false,
      default:'pictorialBar'
    },
    //分块的宽高
    symbolSize:{
      type: Number,
      required: false,
      default: 8
    },
    //bar的宽度
    barWidth:{
      type: Number,
      required: false,
      default: 8
    },
    //边框颜色
    borderColor:{
      type: String,
      required: false,
      default:'rgba(16, 41, 74, 0)'
    },
    //bar背景色
    bgBarColor:{
      type: String,
      required: false,
      default:'rgba(16, 41, 74, 0.1)'
    }
  },
  data() {
    return {}
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      let rich = {}
      for (let i = 0; i < this.colors.length; i++) {
        let key = 'color' + i
        rich[key] = { color: this.colors[i] }
      }
      let charData=[]
      for (let i=0;i<this.data.length;i++) {
        let value = this.data[i] + ''// 转为字符串处理
        let val=0
        if (!value) {
          charData.push(0)
          continue
        } // 空值返回 0

        if (this.unit && value.includes(this.unit)) {
          let numericPart = value.split(this.unit)[0]; // 提取数值部分
          val= isNaN(numericPart) ? 0 : numericPart; // 判断是否为有效数字
          charData.push(val)
          continue
        }

        let  res=isNaN(value) ? 0 : value; // 如果无单位或为数字，直接返回
        charData.push(res)
      }

      let maxData = new Array(this.data).fill(this.max)
      let myChart = this.$echarts.init(document.getElementById(this.type + '_chart_' + this.index))
      myChart.setOption({
        //backgroundColor: 'rgba(16, 41, 74, .6)',
        backgroundColor: 'rgba(16,41,74,0)',
        grid: {
          left: 1,
          top: 14,
          bottom: 0,
          right: 1,
          containLabel: true,
        },
        legend: {
          show: false,
        },

        xAxis: {
          splitLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          axisLabel: {
            show: false,
          },
        },
        yAxis: {
          splitLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          axisLabel: {
            show: false,
          },
          data: [],
        },
        series: [
          {
            // 分隔
            type: this.barType,
            itemStyle: {
              normal: {
                color: (params) => {
                  let idx = 0
                  let v = this.getPureValue(params)
                  for (let i = 0; i < this.limitData.length; i++) {
                    if (v <= this.limitData[i]) {
                      idx = i
                      break
                    }
                  }
                  return this.colors[idx];
                }
              }
            },
            barWidth: this.barWidth,
            silent: true,
            symbolRepeat: 'fixed',
            symbolMargin: 1,
            symbol: 'rect',
            symbolClip: true,
            symbolSize: [3, this.symbolSize],
            symbolPosition: 'start',
            z: 1,
            zlevel: 2,
            data: charData,
          },
          {
            type: 'bar',
            barGap: '-100%',
            barWidth: this.barWidth,
            silent: true,
            itemStyle: {
              normal: {
                color: 'transparent',
                // shadowColor: '#ffffff',
                // shadowBlur: 10,
                barBorderRadius: 0,
                borderColor: this.borderColor,
              },
            },
            label: {
              // normal: {
              show: true,
              position: this.position,
              fontSize: 12,
              offset: [0, 3],
              formatter: (params) => {
                // 获取当前值
                let v = this.getPureValue(params);
                // 返回带有 rich 样式的字符串
                let res = this.unit ? v + this.unit : v
                let idx = 0;
                for (let i = 0; i < this.limitData.length; i++) {
                  if (v <= this.limitData[i]) {
                    idx = i;
                    break;
                  }
                }
                return `{color${idx}|${res}}`;  // 使用富文本标识符
              },
              rich: rich,
            },
            z: -12,
            zlevel: 3,
            data: maxData,
          },
          {
            // 分隔
            type: this.barType,
            itemStyle: {
              normal: {
                color: this.bgBarColor,
              },
            },
            barWidth: this.barWidth,
            silent: true,
            symbolRepeat: 'fixed',
            symbolMargin: 1,
            symbol: 'rect',
            symbolClip: true,
            symbolSize: [3, this.symbolSize],
            symbolPosition: 'start',
            // symbolOffset: [2, 0.2],
            z: 0,
            zlevel: 1,
            data: maxData,
          },
        ],
      })
    },
    getPureValue(params) {
      let value = this.data[params.dataIndex] + ''// 转为字符串处理
      if (!value) return 0; // 空值返回 0

      if (this.unit && value.includes(this.unit)) {
        let numericPart = value.split(this.unit)[0]; // 提取数值部分
        return isNaN(numericPart) ? 0 : numericPart; // 判断是否为有效数字
      }

      return isNaN(value) ? 0 : value; // 如果无单位或为数字，直接返回
    }
  }
}
</script>

<style scoped lang="less">

</style>