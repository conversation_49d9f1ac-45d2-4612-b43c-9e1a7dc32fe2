<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container>
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="24" v-show="assetsStatusList.length > 0">
            <a-form-item :label="'资产状态'" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-select :placeholder="'请选择资产状态'" :getPopupContainer="(node) => node.parentNode" :allowClear="true"
                style="width: 100%" v-decorator="['statusId']" @change="selectAssetsStatus">
                <a-select-option v-for="item in assetsStatusList" :value="item.id" :key="item.id">{{ item.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="24" v-for="(item, idx) in this.additional" :key="idx">
            <a-form-item :label="item.name" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-if="item.type == '单行文本'" v-decorator="[item.value,
                          {
                            initialValue: item.value,
                          }]" :placeholder="'请输入'+item.name" :allowClear="true" autocomplete="off"
                @change="changeConParam($event, idx)"></a-input>
              <a-textarea v-if="item.type == '文本文档'||item.type == '多行文本'" v-decorator="[item.value,
                          {
                            initialValue: item.value,
                          }]" :placeholder="'请输入'+item.name" :allowClear="true" autocomplete="off"
                @change="changeConParam($event, idx)"></a-textarea>
              <j-dict-select-tag v-if="item.type == '下拉框'" v-decorator="[item.value,
                          {
                            initialValue: item.value,
                          }]" :dictCode="item.dictType" :placeholder="'请选择'+item.name" :trigger-change="true"
                @change="changeConParam1($event, idx)" :allowClear="true" />
              <a-input-number v-if="item.type == '计数器'" v-decorator="[item.value,
                          {
                            initialValue: item.value,
                          }]" :placeholder="'请输入'+item.name" :allowClear="true"
                @change="changeConParam1($event, idx)" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
  import {
    httpAction,
    getAction
  } from '@/api/manage'
  import pick from 'lodash.pick'

  export default {
    name: 'AssetsStatus',

    data() {
      return {
        form: this.$form.createForm(this, {
          name: 'asset_info_form'
        }),
        assetsStatusList: [],
        additional: [],
        confirmLoading: false,
        labelCol: {
          xs: {
            span: 24,
          },
          sm: {
            span: 4,
          },
        },
        wrapperCol: {
          xs: {
            span: 24,
          },
          sm: {
            span: 16,
          },
        },
        ids: '',
        model: {
          statusName: ''
        },
        url: {
          sub: 'assets/assets/updateBatchStatus',
          queryStatusByAssetsCategoryId: 'assets/assets/queryStatusByAssetsCategoryId', //根据资产类型id，获取资产状态下拉数据
          findCodeNameBatch: 'extendField/extendField/findCodeNameBatch?'
        },
      }
    },
    methods: {
      batchChange(ids, categoryId) {
        this.ids = ids
        this.getCodeNameBatch(categoryId)
        this.getAssetsStatusByAssetsCategoryId(categoryId)
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.model, 'statusName'))
        })
      },
      getCodeNameBatch(id) {
        getAction(this.url.findCodeNameBatch, {
          assetsCategoryId: id
        }).then((res) => {
          if (!!res) {
            this.additional = res.result
          }
        })
      },
      //选择资产状态，获取状态id
      selectAssetsStatus(key, option) {
        this.model.statusName = option.componentOptions.children[0].text
      },
      //根据资产类型id获取资产状态下拉数据
      getAssetsStatusByAssetsCategoryId(categoryId) {
        getAction(this.url.queryStatusByAssetsCategoryId, {
          assetsCategoryId: categoryId
        }).then((res) => {
          if (!!res) {
            this.assetsStatusList = res.result
          }
        })
      },
      changeConParam(e, idx) {
        this.additional[idx].value = e.target.value
      },
      changeConParam1(e, idx) {
        this.additional[idx].value = e
      },
      getFiledList() {
        let extendValue = []
        this.additional.forEach((item) => {
          extendValue.push({
            type: item.type != null ? item.type : null,
            name: item.name != null ? item.name : null,
            fieldId: item.id != null ? item.id : null,
            value: item.value != null ? item.value : null
          })
        })
        return extendValue
      },
      //提交
      submitForm(record) {
        const that = this
        // 触发表单验证
        this.form.validateFields(this.arrValidateFields, (err, values) => {
          if (!err) {
            that.confirmLoading = true
            let httpurl = this.url.sub
            let method = 'put'
            let formData = Object.assign(this.model, values)
            formData.ids = this.ids
            formData.extendValue = this.getFiledList()
            httpAction(httpurl, formData, method)
              .then((res) => {
                if (res.success) {
                  that.$message.success(res.message)
                  that.$emit('ok')
                } else {
                  that.$message.warning(res.message)
                }
              })
              .finally(() => {
                that.confirmLoading = false
              })
          }
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  .edit-btn {
    background: #ecf5ff;
    border: 1px solid #b3d8ff;
    border-radius: 4px;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #409eff;
    width: 120px;
    height: 28px;
    cursor: pointer;
    margin: 0px 0px 24px 0px;
  }

  .tabs-container {
    height: calc(100% - 135px);
  }

  .ant-tabs-bar {
    margin-bottom: 24px !important;
  }

  ::v-deep .ant-tabs {
    height: 100%;
    background-color: white;
    padding: 0 20px;
    border-radius: 3px;

    .ant-tabs-content {
      height: calc(100% - 60px);

      .ant-tabs-tabpane-active {
        height: 240px;
        overflow-y: auto;
        overflow-x: hidden;
      }
    }
  }

  // .ant-spin-container{
  //   height:100%;
  // }
  // .jeecg-form-container-disabled{
  //   height: calc(100% - 52px);
  //   overflow-y: auto;
  // }
  .relation-btn-div {
    display: flex;
    justify-content: flex-start;
    padding-bottom: 8px;

    .add-btn {
      height: 28px;
      margin-right: 16px;
      background: #ecf5ff;
      border: 1px solid #b3d8ff;
      border-radius: 4px;
      font-family: PingFangSC-Regular;
      font-size: 14px;
      color: #539cf7;
    }

    .enlarge-btn {
      height: 28px;
      background: #ecf5ff;
      border: 1px solid #b3d8ff;
      border-radius: 4px;
      font-family: PingFangSC-Regular;
      font-size: 14px;
      color: #539cf7;
    }
  }

  /deep/ .ant-row {
    div:nth-child(5) {
      .ant-form-item {
        .ant-form-item-label {
          label {
            // letter-spacing: 4px;
          }

          label:after {
            letter-spacing: 0;
          }
        }
      }
    }
  }

  ::v-deep .two-words>div>label {
    letter-spacing: 4px;
  }

  ::v-deep .two-words>div>label::after {
    letter-spacing: 0px;
  }

  .enlargeButton {
    position: absolute;
    right: 14px;
    top: 106px;
    z-index: 100;
  }
</style>