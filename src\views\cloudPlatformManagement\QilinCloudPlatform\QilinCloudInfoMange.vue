<template>
  <div style="height:100%">
    <keep-alive exclude='DeviceInfoModal'>
      <component style="height:100%" :is="pageName" :data="data" :is-editing='true' :transfer="transfer" :render-states='renderStates'/>
    </keep-alive>
  </div>
</template>
<script>
  import QilinCloudInfoList from '@/views/cloudPlatformManagement/cloudPlatform/cloudInfoList'
  import DeviceInfoModal from '@/views/devicesystem/deviceshow/DeviceInfoModal'
  export default {
    name: "QilinCloudInfoMange",
    data() {
      return {
        isActive: 0,
        data: {},
        transfer: 'QiLinCloud',
        renderStates:{
          showBaseInfo:true,
          showStateInfo:true,
          showDeviceFunction:true,
          showQilinCloudHost:true,
          showAlarm:true,
        }
      };
    },
    components: {
      QilinCloudInfoList,
      DeviceInfoModal
    },
    created() {
      this.pButton1(0);
    },
    //使用计算属性
    computed: {
      pageName() {
        switch (this.isActive) {
          case 0:
            return "QilinCloudInfoList";
            break;

          default:
            return "DeviceInfoModal";
            break;
        }
      }
    },
    methods: {
      pButton1(index) {
        this.isActive = index;
      },
      pButton2(index, item) {
        this.isActive = index;
        this.data = item;
      }
    }
  }
</script>