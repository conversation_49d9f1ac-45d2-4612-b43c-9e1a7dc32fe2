<template>
  <div style="height:100%">
      <component :is="pageName" :data="data"/>
  </div>
</template>
<script>
  import strategyList from './strategyList'
  import strategyDetails from './modules/strategyDetails'
  export default {
    name: "strategyManage",
    data() {
      return {
        isActive: 0,
        data:{}
      };
    },
    components: {
      strategyList,
      strategyDetails
    },
    created(){
      this.pButton1(0);
    },
    //使用计算属性
    computed: {
      pageName() {
        switch (this.isActive) {
          case 0:
            return "strategyList";
            break;

          default:
            return "strategyDetails";
            break;
        }
      }
    },
    methods: {
      pButton1(index) {
        this.isActive = index;
      },
      pButton2(index,item) {
        this.isActive = index;
        this.data = item;
      }
    }
  }
</script>