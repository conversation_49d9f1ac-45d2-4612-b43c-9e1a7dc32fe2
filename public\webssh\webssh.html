<!doctype html>
<html>

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>WebSSH</title>
  <!--  link的href属性值不能以斜杠 / 结尾 部署环境下会获取不到文件-->
  <link rel="stylesheet" href="./xterm.min.css" />
</head>
<style>
  * {
    margin: 0;
    padding: 0;
  }

  html,
  body {
    height: 100%;
    background: #fff;
    overflow: hidden;
  }

  .inps {
    height: calc(100% - 46px);
    text-align: center;
    width: 47%;
    float: left;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    margin-right: 16px;
  }

  input[type="button"] {
    height: 32px;
    padding: 0 15px;
    font-size: 14px;
    font-family: PingFangSC-Regular;
    border-radius: 4px;
    border: 1px solid;
  }

  #btnT {
    color: #409eff;
    background-color: #f0f7ff;
    border-color: #b3d7ff;
    margin-top: 24px;
  }

  #btnC {
    background: #fff;
    border: 1px solid #dcdfe6;
    color: #747679;
    margin-top: 24px;
  }

  #btnT:hover {
    color: #409eff;
    background-color: #f0f7ff;
    border-color: #b3d7ff;
  }

  #btnT:active {
    color: #409eff;
    background-color: #f0f7ff;
    border-color: #b3d7ff;
  }

  #btnC:hover {
    color: #40a9ff;
    background-color: #fff;
    border-color: #40a9ff;
  }

  input[type="text"],
  input[type="password"] {
    height: 28px;
    padding: 4px 11px;
    width: calc(100% - 20% - 50px);
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    font-size: 14px;
    line-height: 1.5;
    color: rgba(0, 0, 0, 0.65);
    margin-bottom: 24px;
  }

  input[type="text"]:hover,
  input[type="password"]:hover {
    border-color: #40a9ff;
  }

  input[type="text"]:focus,
  input[type="password"]:focus {

    outline-color: #40a9ff;
    border-radius: 4px;
  }

  .ters {
    height: calc(100% - 46px);
    width: calc(100% - 47% - 64px);
    float: left;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
  }

  #terminal {
    height: calc(100% - 54px);
  }

  .span {
    width: 16%;
    float: left;
    text-align: right;
    padding-right: 10px;
    line-height: 39.9999px;
    white-space: nowrap;
    color: rgba(0, 0, 0, 0.85);
    font-size: 14px;
  }

  .inpsIn {

    /* display: inline-block; */
    float: left;
  }
</style>

<body>
<div style="width: 100%;height: 100%;padding: 23px 23px;">
  <div class="inps">
    <div>
      <p
        style="padding: 16px 24px;background-color: #fafafa;border-bottom:1px solid #e8e8e8;font-size: 16px;margin-top: 0;text-align: left;">
        SSH远程控制</p>
    </div>
    <div style="padding-top: 36px;">
      <div><span class="span">IP地址</span> <input type="text" id="host" class="inpsIn" placeholder="请输入IP地址">
        &nbsp;
      </div>
      <div><span class="span">端 口</span> <input type="text" id="port" class="inpsIn" value="22"
                                                  placeholder="请输入端口">&nbsp;
      </div>
      <div><span class="span">用户名</span><input type="text" id="username" class="inpsIn" value="root"
                                                  placeholder="请输入用户名">&nbsp;
      </div>
      <div><span class="span">密 码</span><input type="password" id="password" class="inpsIn" placeholder="请输入密码">&nbsp;
      </div>
      <div style="margin-top: 24px;">
        <input type="button" id="btnT" onclick="submits()" value="连接">&nbsp;
        <input type="button" id="btnC" onclick="resets()" value="重置">
      </div>
    </div>
  </div>
  <div class="ters">
    <div>
      <p
        style="padding: 16px 24px;background-color: #fafafa;border-bottom:1px solid #e8e8e8;font-size: 16px;margin-top: 0;text-align: left;">
        终端</p>
    </div>
    <div id="terminal" style='overflow-y: auto'></div>
  </div>

</div>


<script src="./jquery-3.4.1.min.js"></script>
<script src="../config.js"></script>
<script src="./xterm.min.js" charset="utf-8"></script>
<script src="./webssh.js" charset="utf-8"></script>
<script>

  $(document).ready(function () {
    var height = $(document).height();
    // $('#terminal').height(height - 86 +'px');
    // $('.xterm-viewport').height(height - 86 +'px');
    // $('.xterm-scroll-area').height(height - 86 +'px');
    // $('.xterm-selection').height(height - 86 +'px');
    // $('.xterm-cursor-blink').height(height - 86 +'px');
    var param = getQueryVariable();
    $("#host").val(param)
  });

  function getQueryVariable(variable) {
    var query = window.location.search.substring(1);
    var vars = query.split("&");
    for (var i = 0; i < vars.length; i++) {
      var pair = vars[i].split("=");
      if (pair[0] == 'ip') {
        return pair[1];
      }
    }
    return ('');
  }

  var terminal = document.getElementById('terminal')
  var currentCommand=''
  function resets() {
    $("#host").val('');
    $("#port").val('22');
    $("#username").val('root');
    $("#password").val('');
    terminal.innerHTML = '';
  }

  function submits() {
    var host = $("#host").val();
    var port = $("#port").val();
    var username = $("#username").val();
    var password = $("#password").val();
    terminal.innerHTML = '';
    openTerminal(
      {
        operate: 'connect',
        host: host,//IP
        port: port,//端口号
        username: username,//用户名
        password: password//密码
      }
    );
  }
  function getCurrentCommand(term) {
    //.active
    // term.refresh(0, term.rows - 1);// 强制刷新缓冲区
    const buffer = term.buffer;
    // const cursorY = buffer.cursorY;//存在光标位置不更新问题
    const cursorY = term._core.buffer.ybase + term._core.buffer.y;
    //console.log('cursorY==',cursorY)
    let line = buffer.getLine(cursorY)?.translateToString(true); // 获取当前行文本
    let returnLine=''
    //console.log('获取当前行文本截取前line===',line)
    if (line&&line.trim().indexOf('#')>-1){
      let idx=line.trim().indexOf('#')+1
      returnLine=line[idx]?line.substring(idx).trim():''
    }
    //console.log('截取后全量命令:',returnLine);
    return returnLine;
  }
  function getFullBufferText(term) {
    let content = '';
    for (let i = 0; i < term.rows; i++) {
      const line = term.buffer.getLine(i)?.translateToString(true);
      content += line + '\n';
    }
    return content;
  }
  function openTerminal(options) {
    var client = new WSSHClient();
    var term = new Terminal({
      cols: 97,
      rows: 32,
      cursorBlink: true, // 光标闪烁
      cursorStyle: "block", // 光标样式  null | 'block' | 'underline' | 'bar'
      scrollback: 800, //回滚
      tabStopWidth: 8, //制表宽度
      screenKeys: true
    });
    // term.onKey(e=>{
    //   console.log("键盘输入 == ",e)
    //   const printable = !e.domEvent.altKey && !e.domEvent.altGraphKey && !e.domEvent.ctrlKey && !e.domEvent.metaKey
    //   if (e.domEvent.keyCode === 13) {
    //     console.log("键盘输入 == ",term)
    //     client.sendClientData("l");
    //     client.sendClientData("s");
    //     client.sendClientData("");
    //   } else if (e.domEvent.keyCode === 8) { // back 删除的情况
    //     if (term._core.buffer.x > 2) {
    //       term.write('\b \b')
    //     }
    //   } else if (printable) {
    //     term.write(e.key)
    //   }
    //   console.log(1,'print', e.key)
    // })

    term.onData(key => {
      //console.log('currentCommand===',currentCommand)
      if (key === '\r' || key === '\n') {
        //console.log('Enter 键被按下');
        client.sendEnterData(currentCommand);
        currentCommand=''
        // 处理 Enter 逻辑
      } else {
        client.sendClientData(key);
      }
    })
    // term.on("key",function(e){console.log("keykey == ",e.domEvent)})
    // term.on('data', function (data) {
    //   console.log("键盘输入 == ",data)
    //   term.write(data)
    //   //键盘输入时的回调函数
    //   // client.sendClientData(data);
    // });
    term.open(document.getElementById('terminal'));
    //在页面上显示连接中...
    term.write('Connecting...\r');

    let helperEl = document.querySelector(".xterm-char-measure-element")
    if(helperEl){
      let pbox = document.getElementById("terminal").getBoundingClientRect();
      let cbox = helperEl.getBoundingClientRect()
      let row = Math.floor(pbox.height / cbox.height)
      let col = Math.floor(pbox.width / cbox.width)
      term.resize(col,row)
    }
    //执行连接操作
    client.connect({
      onError: function (error) {
        //连接失败回调
        term.write('Error: ' + error + '\r\n');
      },
      onConnect: function () {
        //连接成功回调
        client.sendInitData(options);
      },
      onClose: function () {
        //连接关闭回调
        term.write("\rconnection closed");
      },
      onData: function (jsonData) {
        let obj=JSON.parse(jsonData)
        //console.log('得到回调数据obj===',obj)
        if (obj.messageType==='ssh'){
          //收到数据时回调
          term.write(obj.data);
          term.scrollToBottom();
          //从term缓存获取当前命令
          let timer = setTimeout(()=>{
            clearTimeout(timer)
            timer = null;
            currentCommand = getCurrentCommand(term);
          },100)
        }
      }
    });
  }
</script>
</body>

</html>