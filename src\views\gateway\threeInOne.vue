<template>
  <div class='mouse'>
    <div class='sys-box'>
      <div class='sys-item' v-for='item in sysInfo' :key='item.type' @click='sysClick(item)'
           @mouseenter='sysEnter(item)' @mouseleave='sysLeave()'>
        <div class='sys-icon'>
          <img v-if='sysType==item.type' :src='item.iconH' style='scale: 0.99'>
          <img v-else :src='item.icon'>
        </div>
        <div class='sys-name'>{{ item.name }}</div>
        <div class='sys-des'>{{ item.description }}</div>
        <div class='sys-arrow' v-show='sysType==item.type'></div>
      </div>
    </div>
    <div class='sys-bottom'>
      <div class='sys-bottom-text'>
        {{ sysBottomText }}
      </div>
    </div>
  </div>
</template>
<script>
import { getAction } from '@api/manage'
import store from '@/store'
import { PLATFORM_TYPE } from '@/store/mutation-types'
import { generateBigscreenRouter, generateIndexRouter } from '@/utils/util'
import router from '@/router'
import { mapActions } from 'vuex'

export default {
  name: 'threeInOne',
  data() {
    return {
      sysInfo: [],
      loading: false,
      sysType: '',
      bottomText: "运维管理系统" ,
      sysBottomText: '' ,
    }
  },
  created() {
    if (window.config.customization?.cz_zhongruan) {
      this.sysInfo = window.config.customization.cz_zhongruan.sysList
      this.bottomText = window.config.customization.cz_zhongruan.bottomText || '运维管理系统';
    }
    this.sysBottomText = this.bottomText
    // console.log('三合一页面',this.$store.state.app.ssoAccessToken,this.sysInfo)
  },
  mounted() {

  },
  methods: {
    ...mapActions(['Logout', 'GetPermissionList']),
    sysEnter(item) {
      this.sysType = item.type
      this.sysBottomText = item.name
    },
    sysLeave() {
      this.sysType = ''
      this.sysBottomText = this.bottomText
    },
    sysClick(item) {
      if (this.loading) return
      this.loading = true
      let timer = setTimeout(() => {
        clearTimeout(timer)
        this.loading = false
      }, 1000)
      if(this.$store.state.app.ssoAccessToken){
        let type = item.type
        switch (type) {
          case 'sa':
            if (item.link) {
              console.log("动态感知拼接的链接", item.link + this.$store.state.app.ssoAccessToken)
              window.location.href = item.link  + this.$store.state.app.ssoAccessToken
            } else {
              this.$message.warning('该系统的连接没有配置！')
            }
            break
          case 'ls':
            if (item.link) {
              console.log("大屏可视化拼接的链接", item.link + this.$store.state.app.ssoAccessToken)
              window.location.href = item.link + this.$store.state.app.ssoAccessToken
            } else {
              this.$message.warning('该系统的连接没有配置！')
            }

            break
          case 'yw':
            // window.location.href = window.location.origin + "/?&threeInOne=TIO&access_token="+ this.$store.state.app.ssoAccessToken
            getAction('license/licenseTest')
              .then((res) => {
                if (res.status == 200 || res.code == 200) {
                  store.dispatch('SsoAccessTokenLogin', { access_token: this.$store.state.app.ssoAccessToken })
                    .then(res => {
                      getAction('/sys/permission/getUserPlatformTypeByToken').then(perRes => {
                        if (perRes.success) {
                          if (perRes.result == '') {
                            store.dispatch('Logout').then(() => {
                              this.$message.error({ content: '没有菜单权限，请联系管理员！', duration: 5 })
                            })
                          } else {
                            let userPlatforms = [...perRes.result.split(',')]
                            this.entrancePlanning(userPlatforms[0])
                            // window.location.href = window.location.origin
                          }
                        }
                      })

                    }).catch((err) => {
                    this.$router.push('/user/login')
                  })
                } else {
                  this.$router.push({ path: '/user/login', query: { authorizedOrNot: 1 } })
                }

              })
              .catch((err) => {
                window.location.href = window.location.origin + '/#/user/login?authorizedOrNot=1'
              })
            break
        }
      }
      else{
        this.$message.warning("没有access_token信息！")
      }
    },
    entrancePlanning(index) {
      this.$ls.set(PLATFORM_TYPE, index)
      this.GetPermissionList(index).then((res) => {
        if (res === '1') {
          this.$message.warning('没有添加菜单！')
          return
        }

        const menuData = res.result.menu
        var redirect = ''
        if (menuData && menuData.length > 0) {
          let firsMenu = menuData[0]
          redirect = firsMenu.children && firsMenu.children.length > 0 ? firsMenu.children[0].path : firsMenu.path
        } else {
          return
        }

        let constRoutes = []
        if (index === 4 || index === 8) {
          constRoutes = generateBigscreenRouter(menuData)
        } else {
          constRoutes = generateIndexRouter(menuData)
        }
        // 添加主界面路由
        store.dispatch('UpdateAppRouter', {
          constRoutes
        }).then(() => {
          // 根据roles权限生成可访问的路由表
          // 动态添加可访问路由表
          router.addRoutes(store.getters.addRouters)
          this.$router.push({
            path: redirect
          })
          // }
        })
      })
    }
  }
}
</script>


<style scoped lang='less'>
.fade-enter-active, .fade-leave-active {
  transition: opacity .5s;
}
.fade-enter, .fade-leave-to /* .fade-leave-active below version 2.1.8 */ {
  opacity: 0;
}
.mouse {
  width: 100%;
  height: 100%;
  min-width: 1600px;
  min-height: 900px;
  background-image: url(/threeInOne/tioBg.png);
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  .sys-box {
    display: flex;
    margin-bottom: 60px;
    .sys-item {
      position: relative;
      background-image: url(/threeInOne/tioSysBg.png);
      background-size: 100% 100%;
      width: 341px;
      height: 493px;
      border-radius: 16px;
      display: flex;
      flex-direction: column;
      align-items: center;
      font-size: 32px;
      cursor: pointer;

      .sys-icon {
        width: 170px;
        height: 170px;
        margin-top: 80px;

        img {
          width: 100%;
          height: 100%;
        }
      }

      .sys-name {
        width: 195px;
        height: 50px;
        line-height: 50px;
        background-size: 100% 100%;
        background-image: url(/threeInOne/tioNameBg.png);
        //opacity: 0.4;
        text-align: center;
        margin-top: 50px;
        font-weight: bold;
        font-size: 20px;
        color: rgba(255, 255, 255, 1);
        text-stroke: 1px #FFFFFF;
      }

      .sys-des {
        margin-top: 5px;
        font-weight: 400;
        font-size: 14px;
        color: #FFFFFF;
        line-height: 29px;
        background: linear-gradient(0deg, #9CCEFF 0%, #FFFFFF 100%);
        opacity: 0.99;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent
      }

      .sys-arrow {
        width: 91px;
        height: 83px;
        background-size: 100% 100%;
        background-image: url(/threeInOne/tioSysArrow.png);
        position: absolute;
        top: -83px
      }

      &:nth-child(1) {
        margin-right: 100px;
      }

      &:nth-child(2) {
        margin-right: 100px;
      }

      &:hover {
        background-image: url(/threeInOne/tioSysBgH.png);
      }
    }
  }
  .sys-bottom {
    width: 100%;
    height: 149px;
    background-image: url(/threeInOne/tioBottom.png);

    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    position: absolute;
    bottom: 0;
    .sys-bottom-text{
      position: absolute;
      width: 210px;
      height: 40px;
      //background: #af0000;
      letter-spacing: 5px;
      top:38px;
      left: calc(50% - 100px);
      text-align: center;
      font-size: 23px;
      color: #B5E6FF;
      line-height: 40px
    }
  }
}
</style>