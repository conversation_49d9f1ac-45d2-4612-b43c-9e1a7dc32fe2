<template>
  <div class='user-wrapper' :class='theme'>
    <!-- <a-button @click="entrancePlanning(1)">监控管理</a-button>
    <a-button style="margin-left: 10px;"
              @click="entrancePlanning(2)">运维管理</a-button> -->
    <span style='font-family: PingFangSC-Regular; font-size: 16px'></span>
    <a
      v-if="systemType !== '1' && systemType !== '3' && settings && !oneClickHelp"
      @click='entrancePlanning(5)'
      style='padding: 0 14px; padding-left: 31px'
    >
      <a-icon type='setting' class='icon-item' />
      <!--      <img src="~@/assets/setting.png" alt="" />-->
    </a>
    <!-- update_begin author:zhaoxin date:20191129 for: 做头部菜单栏导航 -->
    <!-- update-begin author:sunjianlei date:20191@20 for: 解决全局样式冲突的问题 -->
    <span v-if='!oneClickHelp' class='action' @click='showClick'>
      <a-icon type='search' class='icon-item'></a-icon>
      <!--      <img src="~@/assets/search.png" alt="" />-->
    </span>
    <!-- update-begin author:sunjianlei date:******** for: 菜单搜索改为动态组件，在手机端呈现出弹出框 -->
    <component
      v-if='!oneClickHelp'
      :is='searchMenuComp'
      v-show='searchMenuVisible || isMobile()'
      class='borders'
      :visible='searchMenuVisible'
      title='搜索菜单'
      :footer='null'
      @cancel='searchMenuVisible = false'
    >
      <a-select
        class='search-input'
        showSearch
        :showArrow='false'
        placeholder='搜索菜单'
        optionFilterProp='children'
        :filterOption='filterOption'
        :open='isMobile() ? true : null'
        :getPopupContainer='(node) => node.parentNode'
        :style="isMobile() ? { width: '100%', marginBottom: '50px' } : {}"
        @change='searchMethods'
        @blur='hiddenClick'
      >
        <a-select-option v-for='(site, index) in searchMenuOptions' :key='index' :value='site.id'>{{
            site.meta.title
          }}
        </a-select-option>
      </a-select>
    </component>
    <!-- update-end author:sunjianlei date:******** for: 菜单搜索改为动态组件，在手机端呈现出弹出框 -->
    <!-- update-end author:sunjianlei date:20191220 for: 解决全局样式冲突的问题 -->
    <!-- update_end  author:zhaoxin date:20191129 for: 做头部菜单栏导航 -->
    <!--<span class="action">
      <a class="logout_title" target="_blank" href="http://doc.jeecg.com">
        <a-icon type="question-circle-o"></a-icon>
      </a>
    </span>-->
    <header-notice v-if='!oneClickHelp' class='action' @toMyAnnouncement='toMyAnnouncement' />
    <a-dropdown v-if='!oneClickHelp'>
      <span class='action action-full ant-dropdown-link user-dropdown-menu'>
        <a-avatar
          class='avatar'
          size='small'
          icon='user'
          style='margin: 0 10px 5px 0; vertical-align: middle'
          :src='userAvatar'
        />
        <!--        <a-avatar  class="avatar" size="small" :src="userAvatar" />-->
        <span v-if='isDesktop()'>欢迎您，{{ nickname() }}</span>
      </span>
      <a-menu slot='overlay' class='user-dropdown-menu-wrapper' v-if='!oneClickHelp'>
        <a-menu-item key='0' @click='showUserInfo'>
          <a-icon type='user' class='icon-item' />
          <span>个人中心</span>
        </a-menu-item>
        <!-- <a-menu-item key="1">
          <router-link :to="{ name: 'account-settings-base' }">
            <a-icon type="setting" class='icon-item'/>
            <span>账户设置</span>
          </router-link>
        </a-menu-item> -->
        <!--<a-menu-item key="3"  @click="systemSetting">
          <a-icon type="tool" class='icon-item'/>
          <span>系统设置</span>
        </a-menu-item>-->
        <a-menu-item key='4' @click='updatePassword'>
          <a-icon type='setting' class='icon-item' />
          <span>密码修改</span>
        </a-menu-item>
        <!-- <a-menu-item key="5" @click="updateCurrentDepart">
          <a-icon type="cluster"  class='icon-item'/>
          <span>切换部门</span>
        </a-menu-item> -->
        <a-menu-item key='6' @click='clearCache'>
          <a-icon type='sync' class='icon-item' />
          <span>清理缓存</span>
        </a-menu-item>
        <!--        <a-menu-item key="8" @click="openBindingIDModal">
                  <a-icon type="cluster" class="icon-item" />
                  <span>绑定身份证号</span>
                </a-menu-item>-->
        <!-- 沈阳定制去掉7 -->
        <!--        <a-menu-item key="7" @click="showModal">
                  <a-icon type="cluster" class="icon-item" />
                  <span>绑定微信公众号</span>
                </a-menu-item>-->
        <!-- <a-menu-item key="2" disabled>
           <a-icon type="setting" class='icon-item'/>
           <span>测试</span>
         </a-menu-item>
         <a-menu-divider/>
         <a-menu-item key="3">
           <a href="javascript:;" @click="handleLogout">
             <a-icon type="logout" class='icon-item'/>
             <span>退出登录</span>
           </a>
         </a-menu-item>-->
      </a-menu>
      <a-menu slot='overlay' class='user-dropdown-menu-wrapper' v-else>
        <!-- <a-menu-item key="0">
         <router-link :to="{ name: 'account-center' }">
           <a-icon type="user" class="icon-item" />
           <span>个人中心</span>
         </router-link>
       </a-menu-item> -->
        <a-menu-item key='4' @click='updatePassword1'>
          <a-icon type='setting' class='icon-item' />
          <span>密码修改</span>
        </a-menu-item>
        <a-menu-item key='11' @click='updateActivation'>
          <a-icon type='form' class='icon-item' />
          <span>修改注册信息</span>
        </a-menu-item>
        <!-- <a-menu-item key="12" @click="unBindTerminal">
          <a-icon type="disconnect" class="icon-item" />
          <span>解除绑定</span>
        </a-menu-item> -->
      </a-menu>
    </a-dropdown>
    <!-- <span v-else class="action action-full ant-dropdown-link user-dropdown-menu">
      <a-avatar
        class="avatar"
        size="small"
        icon="user"
        style="margin: 0 10px 5px 0; vertical-align: middle"
        :src="userAvatarHelp"
      />
      <span>欢迎使用</span> -->
    <span v-if='oneClickHelp' class='telphone'>
      <a-icon type='phone' style='transform: scaleX(-1);'></a-icon>
      {{ telphone }}
    </span>
    <a-modal v-model='bindingID' title='绑定身份证' @ok='bindingOk'>
      <a-form :label-col='{ span: 6 }' :wrapper-col='{ span: 14 }'>
        <a-form-item label='身份证号'>
          <a-input placeholder='请输入身份证' v-model='cardId' />
        </a-form-item>
      </a-form>
    </a-modal>
    <a-modal title='请扫二维码' :visible='visible' @ok='handleCancel' @cancel='handleCancel'>
      <div class='erwei'>
        <div>
          <img :src='image' style='width: 200px; height: 200px' align='middle' />
        </div>
        <div v-if='type === 1'>
          <a-menu-item @click='clearImage'>
            <a-icon type='sync' class='icon-item' />
            <span style='color: #f00'>已绑定微信，点击清除</span>
          </a-menu-item>
        </div>
      </div>
    </a-modal>
    <span class='action' v-if="(systemType === '0' && !onePtmFlag) || oneClickHelp" @click='sysChange'>
      <!--<a-icon type="search"></a-icon>-->
      <a-icon type='appstore' class='icon-item' title='系统切换' />
      <!--        <yq-icon type='gateway' title='系统切换'></yq-icon>-->
      <!--        <img src="~@/assets/return.png" alt="" title="返回门户" />-->
    </span>

    <span class='action' v-if='!oneClickHelp'>
      <a class='logout_title' href='javascript:;' @click='handleLogout'>
        <a-icon type='poweroff' title='退出登录' class='icon-item' />
      </a>
    </span>
    <user-password ref='userPassword'></user-password>
    <user-info-modal ref='userInfoModal'></user-info-modal>
    <depart-select ref='departSelect' :closable='true' title='部门切换'></depart-select>
    <setting-drawer ref='settingDrawer' :closable='true' title='系统设置'></setting-drawer>
    <activation-modal ref='activation' :hostName='hostName'></activation-modal>
  </div>
</template>

<script>
import Vue from 'vue'
import yqIcon from './SvgIcon'
import HeaderNotice from './HeaderNotice'
import UserPassword from './UserPassword'
import SettingDrawer from '@/components/setting/SettingDrawer'
import DepartSelect from './DepartSelect'
import { mapActions, mapGetters, mapState } from 'vuex'
import { mixinDevice } from '@/utils/mixin.js'
import router from '@/router'
import { getFileAccessHttpUrl, getAction } from '@/api/manage'
import { generateIndexRouter, generateBigscreenRouter, getQueryStringRegExp } from '@/utils/util'
import { UI_CACHE_DB_DICT_DATA, PLATFORM_TYPE, ONE_PLATFORM_FLAG } from '@/store/mutation-types'
import store from '@/store'
import GatewayMenus from './GatewayMenus.vue'
import ActivationModal from '@/views/oneClickHelp/modules/ActivationModal.vue'
import UserInfoModal from '@views/account/center/UserInfoModal.vue'
import { SsoLogout } from '@/api/login'

export default {
  name: 'UserMenu',
  mixins: [mixinDevice],
  props: {
    theme: {
      type: String,
      required: false,
      default: 'dark'
    },
    oneClickHelp: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      settings: true,
      // update-begin author:sunjianlei date:******** for: 头部菜单搜索规范命名 --------------
      searchMenuOptions: [],
      searchMenuComp: 'span',
      searchMenuVisible: false,
      visible: false,
      image: '',
      type: '',
      bindingID: false,
      cardId: '',
      userAvatar: '',
      userAvatarHelp: '',
      systemType: window._CONFIG['system_Type'],
      onePtmFlag: false,
      // update-begin author:sunjianlei date:******** for: 头部菜单搜索规范命名 --------------
      url: {
        checkHasMenu: '/sys/permission/checkHasMenu' //菜单权限
      },
      telphone: window.config.oneClickHelp.telphone,
      hostName: '',
      sso: false
    }
  },
  //provider/inject：简单来说就是在父组件 provide 中提供变量，子组件 inject 中来注入，然后可以在子组件内部使用 provide 的变量 APP.vue
  inject: ['reload'],
  components: {
    UserInfoModal,
    HeaderNotice,
    UserPassword,
    DepartSelect,
    SettingDrawer,
    yqIcon,
    GatewayMenus,
    ActivationModal
  },

  /* update_begin author:zhaoxin date:20191129 for: 做头部菜单栏导航*/
  created() {
    let lists = []
    this.onePtmFlag = Vue.ls.get(ONE_PLATFORM_FLAG)
    this.searchMenus(lists, this.permissionMenuList)
    this.searchMenuOptions = [...lists]
    this.getAvatar()
    this.MenuJudgment()
    this.sso = sessionStorage.getItem('sso_access_token') ? true : false
  },
  mounted() {
    this.hostName = getQueryStringRegExp('hostname')
    //如果是单点登录模式
    if (window.CONFIG && window.CONFIG['casSSo'] == 'true') {
      let depart = this.userInfo().orgCode
      if (!depart) {
        this.updateCurrentDepart()
      }
    }

    //  let depart = this.userInfo().orgCode;
    //   if(!depart){
    //     this.updateCurrentDepart();
    //   }
  },
  computed: {
    ...mapState({
      // 后台菜单
      permissionMenuList: (state) => state.user.permissionList
    })
  },
  /* update_end author:zhaoxin date:20191129 for: 做头部菜单栏导航*/
  watch: {
    // update-begin author:sunjianlei date:******** for: 菜单搜索改为动态组件，在手机端呈现出弹出框
    '$store.state.user.ca'(v) {
      if (v) {
        this.$store.commit('SET_CA', false)
        this.getAvatar()
      }
    },
    device: {
      immediate: true,
      handler() {
        this.searchMenuVisible = false
        this.searchMenuComp = this.isMobile() ? 'a-modal' : 'span'
      }
    }
    // update-end author:sunjianlei date:******** for: 菜单搜索改为动态组件，在手机端呈现出弹出框
  },
  methods: {
    //修改绑定信息
    updateActivation() {
      if (this.hostName === null || this.hostName === '') {
        this.$message.warning('没有终端信息，无法修改信息！')
        return
      }
      this.$refs.activation.title = '修改绑定信息'
      this.$refs.activation.edit()
    },
    sysChange() {
      if (this.oneClickHelp) {
        if (this.hostName === null || this.hostName === '') {
          this.$message.warning('请检查是否已经添加终端信息？')
          return
        }
        this.$router.push({ path: '/oneClickHelp/index' })
      } else {
        this.$router.push('/gateway/gateway')
      }
    },
    toMyAnnouncement(routerInfo) {
      let that = this
      that.$emit('toMyAnnouncement', routerInfo)
    },
    //判断权限中心菜单权限
    MenuJudgment() {
      getAction(this.url.checkHasMenu).then((res) => {
        if (res.result.platform_5 == '0') {
          this.settings = false
        }
      })
    },
    showModal() {
      getAction('/wx/getCode').then((res) => {
        if (res.code == 200) {
          this.image = res.result.image
          this.type = res.result.type
        }
      })
      this.visible = true
    },
    openBindingIDModal() {
      getAction('/sys/user/findUserId').then((res) => {
        this.cardId = res.message
      })
      this.bindingID = true
    },
    bindingOk() {
      if (this.cardId == '') {
        this.$message.error('输入框不能为空')
        return
      } else if (this.cardId.length == 18 || this.cardId.length == 15) {
        getAction('/sys/user/updateUserCard', {
          cardId: this.cardId
        }).then((res) => {
        })
        this.bindingID = false
      } else {
        this.$message.error('请输入正确的身份证号')
        return
      }
    },

    clearImage() {
      getAction('/wx/del').then((res) => {
        if (res.code == 200) {
          this.showModal()
        }
      })
      this.visible = true
    },

    handleCancel(e) {
      this.visible = false
    },

    /* update_begin author:zhaoxin date:20191129 for: 做头部菜单栏导航*/
    showClick() {
      this.searchMenuVisible = true
    },
    hiddenClick() {
      this.shows = false
    },
    /* update_end author:zhaoxin date:20191129 for: 做头部菜单栏导航*/
    ...mapActions(['Logout', 'GetPermissionList']),
    ...mapGetters(['nickname', 'avatar', 'userInfo']),
    //2021-1-28
    entrancePlanning(index) {
      sessionStorage.setItem(PLATFORM_TYPE, index)
      const that = this
      that.GetPermissionList(index).then((res) => {
        const menuData = res.result.menu
        var redirect = ''
        if (menuData[0].children == null || menuData[0].children == undefined) {
          return
        } else {
          redirect = menuData[0].children[0].path
        }
        let constRoutes = []
        if (index == 4) {
          constRoutes = generateBigscreenRouter(menuData)
        } else {
          constRoutes = generateIndexRouter(menuData)
        }
        let that = this
        // 添加主界面路由
        store
          .dispatch('UpdateAppRouter', {
            constRoutes
          })
          .then(() => {
            // 根据roles权限生成可访问的路由表
            // 动态添加可访问路由表
            router.addRoutes(store.getters.addRouters)
            that.$router.push({
              path: redirect
            })
            that.reload()
            // }
          })
      })
    },
    getAvatar() {
      getAction('/sys/user/getUserAvatar').then((res) => {
        if (res.success) {
          this.$store.commit('SET_AVATAR', res.result)
          this.$nextTick(() => {
            this.userAvatar = ''
            this.userAvatar = getFileAccessHttpUrl(res.result)
          })
        }
      })
    },
    handleLogout() {
      const that = this
      if (this.sso && window.config.customization.cz_zhongruan?.threeInOne) {
        that.$router.push({
          path: '/gateway/threeInOne'
        })
        return
      }
      const ssoAccessToken = sessionStorage.getItem('sso_access_token')
      this.$confirm({
        title: '提示',
        content: '真的要注销登录吗 ?',
        okText: '确定',
        cancelText: '取消',
        onOk() {
          return that
            .Logout({})
            .then(() => {
              let exitUrl = window.config.customization?.cz_zhongruan?.exitUrl
              let logoutUrl = window.config.customization?.cz_zhongruan?.logoutUrl
              //单点登录执行统一的退出登录接口
              if (that.sso) {
                if (logoutUrl) {
                  SsoLogout(ssoAccessToken)
                  return
                } else if (exitUrl) {
                  window.location.href = exitUrl
                  return
                }
              }
              that.$router.push({
                path: '/user/login'
              })

            })
            .catch((err) => {
              that.$message.error({
                title: '错误',
                description: err.message
              })
            })
        },
        onCancel() {
        }
      })
    },
    returnClick() {
    },
    showUserInfo() {
      this.$refs.userInfoModal.show()
    },
    updatePassword1() {
      let username = this.userInfo().username
      this.$refs.userPassword.oneClickHelp = true
      this.$refs.userPassword.show(username)
    },
    updatePassword() {
      let username = this.userInfo().username
      this.$refs.userPassword.show(username)
    },
    updateCurrentDepart() {
      this.$refs.departSelect.show()
    },
    systemSetting() {
      this.$refs.settingDrawer.showDrawer()
    },
    /* update_begin author:zhaoxin date:20191129 for: 做头部菜单栏导航*/
    searchMenus(arr, menus) {
      for (let i of menus) {
        if (!i.hidden && 'layouts/RouteView' !== i.component) {
          arr.push(i)
        }
        if (i.children && i.children.length > 0) {
          this.searchMenus(arr, i.children)
        }
      }
    },
    filterOption(input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
    },
    // update_begin author:sunjianlei date:20191230 for: 解决外部链接打开失败的问题
    searchMethods(value) {
      let route = this.searchMenuOptions.filter((item) => item.id === value)[0]
      if (route.meta.internalOrExternal === true || route.component.includes('layouts/IframePageView')) {
        window.open(route.meta.url, '_blank')
      } else {
        this.$router.push({
          path: route.path
        })
      }
      this.searchMenuVisible = false
    },
    // update_end author:sunjianlei date:20191230 for: 解决外部链接打开失败的问题
    /*update_end author:zhaoxin date:20191129 for: 做头部菜单栏导航*/
    /*update_begin author:liushaoqian date:20200507 for: 刷新缓存*/
    clearCache() {
      getAction('sys/dict/refleshCache')
        .then((res) => {
          if (res.success) {
            //重新加载缓存
            getAction('sys/dict/queryAllDictItems').then((res) => {
              if (res.success) {
                Vue.ls.remove(UI_CACHE_DB_DICT_DATA)
                Vue.ls.set(UI_CACHE_DB_DICT_DATA, res.result, 7 * 24 * 60 * 60 * 1000)
              }
            })
            this.$message.success('刷新缓存完成！')
          }
        })
        .catch((e) => {
          this.$message.warn('刷新缓存失败！')
        })
    }
    /*update_end author:liushaoqian date:20200507 for: 刷新缓存*/
  }
}
</script>

<style lang='less' scoped>
/* update_begin author:zhaoxin date:20191129 for: 让搜索框颜色能随主题颜色变换*/
/* update-begin author:sunjianlei date:20191220 for: 解决全局样式冲突问题 */
.user-wrapper .search-input {
  width: 180px;
  color: rgb(146, 143, 143);
  background-color: #f4f7f9;
  border-radius: 12px;

  /deep/ .ant-select-selection {
    background-color: inherit;
    border: 0;
    border-bottom: 1px solid white;

    &__placeholder,
    &__field__placeholder {
      color: inherit;
    }
  }
}

/* update-end author:sunjianlei date:20191220 for: 解决全局样式冲突问题 */
/* update_end author:zhaoxin date:20191129 for: 让搜索框颜色能随主题颜色变换*/
</style>

<style scoped>
.logout_title {
  color: inherit;
  text-decoration: none;
}

.icon-item {
  font-size: 15px;
}

img {
  width: 14px;
  height: 14px;
}

.erwei {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.action {
  height: 100% !important;
  line-height: inherit !important;
}

.telphone {
  margin-left: 14px;
}
</style>