.lookTop{
  height: 100%;
  overflow: hidden;
  overflow-y: auto;
  margin-bottom: 16px
}
.lookEditionWrapper{
  margin-bottom: 16px
}
.lookEdition{
  display: inline-block;
  float: left;
  margin-left: 10px;
  color: #409eff;
  cursor: pointer
}
.editIcon{
  margin-right: 6px;
}

.lookReturn{
  display: inline-block;
  float: right;
  text-align: right;
  margin-bottom: 12px
}
.returnImage{
  width: 20px;
  height: 20px;
  cursor: pointer
}
.lookCustomTable{
  white-space: nowrap;
  overflow: hidden;
  overflow-x: auto
}
table.gridtable {
  font-family: verdana, arial, sans-serif;
  font-size: 14px;
  color: #606266;
  border-width: 1px;
  border-color: #e8e8e8;
  border-collapse: collapse;
  text-align: left;
  width: 100%;
  word-break: break-word;
}
table.gridtable td {
  border-width: 1px;
  border-style: solid;
  border-color: #e8e8e8;
}
.leftTd {
  width: 17%;
  background-color: #fafafa;
  padding: 16px 24px;
  text-align: center;
}
.rightTd {
  width: 35%;
  padding: 16px 24px;
}