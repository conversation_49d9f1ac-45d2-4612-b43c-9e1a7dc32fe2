<template>
  <a-row :gutter='10' style='height: 100%' class='vScroll'>
    <a-col style='width: 100%; height: 100%; display: flex; flex-direction: column'>
      <!-- 查询区域 -->
      <a-card :bordered='false' :bodyStyle="{ paddingBottom: '0' }" class='card-style'>
        <div class='table-page-search-wrapper-style'>
          <a-form layout='inline' @keyup.enter.native='searchQuery' v-bind='formItemLayout'>
            <a-row :gutter='24' ref='row'>
              <a-col :span='spanValue'>
                <a-form-item label='单位'>
                  <a-tree-select
                    :getPopupContainer='(node) => node.parentNode'
                    v-model='searchedDepKey'
                    tree-node-filter-prop='title'
                    :replaceFields='replaceFields'
                    :treeData='departsTreeData'
                    show-search
                    :searchValue='bSearchedDepKey'
                    multiple
                    :maxTagCount='1'
                    :dropdownMatchSelectWidth="true"
                    :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                    placeholder='请选择单位'
                    allow-clear
                    @change='onChangeDeparts'
                    @search='onSearchDeparts'
                    @select='onSelectDeparts'>
                  </a-tree-select>
                </a-form-item>
              </a-col>
              <a-col :span='spanValue'>
                <a-form-item label='报告时间'>
                  <a-date-picker v-model='reportTime' @change="dateChange" format="YYYY-MM-DD" style="width:100%" />
                  <!-- <a-range-picker v-model='reportTime' @change="dateChange" /> -->
                </a-form-item>
              </a-col>
              <a-col :span='colBtnsSpan()'>
                <span class='table-page-search-submitButtons'
                      :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}">
                  <a-button type='primary' class='btn-search btn-search-style' @click='dosearch'>查询</a-button>
                  <a-button class='btn-reset btn-reset-style' @click='doreset'>重置</a-button>
                  <a v-if='isVisible' class='btn-updown-style' @click='doToggleSearch'>
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <!-- 查询区域-END -->
      <a-card :bordered='false' style='width: 100%; flex: auto'>
        <!-- 操作按钮区域 -->
        <div class='table-operator table-operator-style'>
          <a-button @click="handleExportXls('信创报告')" v-has="'terminal:exportXls'">导出</a-button>
        </div>
        <!-- table区域-begin -->
        <a-table
          ref='table'
          bordered
          rowKey='id'
          :columns='columns'
          :dataSource='dataSource'
          :scroll='dataSource.length>0?{x:"max-content"}:{}'
          :pagination='ipagination'
          :loading='loading'
          @change='handleTableChange'
        >
          <template slot='htmlSlot' slot-scope='text'>
            <div v-html='text'></div>
          </template>
          <template slot='tooltip' slot-scope='text'>
            <a-tooltip placement='topLeft' :title='text' trigger='hover'>
              <div class='tooltip'>
                {{ text }}
              </div>
            </a-tooltip>
          </template>
        </a-table>
      </a-card>
    </a-col>
  </a-row>
</template>

<script>
import { getAction } from '@/api/manage'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'

export default {
  name: 'ReportList',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  data() {
    return {
      description: '设备信创报告页面',
      formItemLayout: {
        labelCol: {
          style: 'width:90px'
        },
        wrapperCol: {
          style: 'width:calc(100% - 90px)'
        }
      },
      //单位tree
      searchedDepKey: undefined,
      bSearchedDepKey: '',
      departsTreeData: [],
      replaceFields: {
        children: 'children',
        title: 'departName',
        key: 'key',
        value: 'id'
      },
      //表头
      columns: [
        {
          title: '序号',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function (t, r, index) {
            return parseInt(index) + 1
          },
        },
        {
          title: '单位',
          dataIndex: 'deptName',
          align: 'left',
          scopedSlots: {
            customRender: 'tooltip'
          }
        },{
          title: '设备数量',
          dataIndex: 'deviceTotal',
          align: 'center',
          scopedSlots: {
            customRender: 'deviceTotal'
          }
        },{
          title: '开机数量',
          dataIndex: 'onlineCount',
          align: 'center',
          scopedSlots: {
            customRender: 'onlineCount'
          }
        },{
          title: '关机数量',
          dataIndex: 'offlineCount',
          align: 'center',
          scopedSlots: {
            customRender: 'offlineCount'
          }
        },{
          title: '使用率(%)',
          dataIndex: 'onlineRate',
          align: 'center',
          scopedSlots: {
            customRender: 'onlineRate'
          }
        },{
          title: '国产化率(%)',
          dataIndex: 'nationalRate',
          align: 'center',
          scopedSlots: {
            customRender: 'nationalRate'
          }
        }
      ],
      url: {
        list: 'device/statis/findDeviceXcReports',
        exportXlsUrl: '/device/statis/findDeviceXcReportsExcel',
        queryMyDeptTreeList: '/device/statis/queryMyDeptTreeList'
      },
      isVisible: false,
      reportTime: "",
    }
  },
  created() {
    this.loadDepartsData()
  },
  computed: {
    importExcelUrl: function() {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    }
  },
  methods: {
    handleTableChange(pagination, filters, sorter) {
      //分页、排序、筛选变化时触发
      //TODO 筛选
      if (Object.keys(sorter).length > 0) {
        this.isorter.column = sorter.field
        this.isorter.order = 'ascend' == sorter.order ? 'asc' : 'desc'
      }
      this.ipagination = pagination
    },
    dateChange(dates, dateStrings) {
      this.queryParam.time  = dateStrings
      console.log('dateStrings', dateStrings,this.queryParam.time) 
      // this.queryParam.startTime = dateStrings[0]
      // this.queryParam.endTime = dateStrings[1]
    },
    loadDepartsData() {
      getAction(this.url.queryMyDeptTreeList).then((res) => {
        if (res.success && res.result ) {
          for (let i = 0; i < res.result.length; i++) {
            let temp = res.result[i]
            this.departsTreeData.push(temp)
          }
        }
      })
    },
    onChangeDeparts(value) {
      // this.queryParam.deptId = value.join(",")
    },
    onSearchDeparts(e) {
      this.bSearchedDepKey = e
    },
    onSelectDeparts() {
    },
    //表单查询,点击查询按钮，默认查询第一页
    dosearch() {
      if (!this.searchedDepKey && this.bSearchedDepKey) {
        this.searchedDepKey = this.bSearchedDepKey
      }
      if (Array.isArray(this.searchedDepKey)) {
        this.queryParam.deptId = this.searchedDepKey.join(',')
      } else if (typeof this.searchedDepKey === 'string') {
        this.queryParam.deptId = this.searchedDepKey
        // this.searchedDepKey = ""
      }
      this.bSearchedDepKey = ''
      this.loadData(1)
    },
    //重置
    doreset() {
      this.searchedDepKey = undefined
      this.bSearchedDepKey = ''
      this.reportTime = ""
      this.queryParam = {}
      this.loadData(1)
    }
  }
}
</script>
<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
</style>