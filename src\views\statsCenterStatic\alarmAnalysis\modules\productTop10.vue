<template>
  <card-frame :title='"告警产品TOP10"' :sub-title='""'>
    <div slot='bodySlot' style="height: 100%;width: 100%;" id="alarmTOP10"></div>
  </card-frame>
</template>

<script>
  import cardFrame from '@views/statsCenter/com/cardFrame.vue'
  import {
    getAction
  } from '@/api/manage'
  export default {
    components: {
      cardFrame
    },
    data() {
      return {
        url: {
          productAlarmTop10: 'data-analysis/alarm/productAlarmTop10'
        }
      };
    },
    mounted() {
      this.getAlarm()
    },
    methods: {
      getAlarm() {
        getAction(location.origin + "/statsCenter/mock/alarmData.json").then((res) => {
          this.getTop10(res.productAlarmTop10)
        })
      },
      getTop10(data) {
        let xData = data.xData
        let yData = data.yData
        let max = []
        yData.forEach((ele) => {
          max.push(yData[0] + 10)
        })
        let myChart = this.$echarts.init(document.getElementById('alarmTOP10'))
        myChart.setOption({
          grid: {
            left: "5%",
            right: "10%",
            bottom: "5%",
            top: "5%",
            containLabel: false,
          },
          xAxis: [{
              show: false,
              type: 'value'
            },
            {
              show: false,
              type: 'value'
            },
          ],
          yAxis: [{
              show: false,
              data: yData,
            },
            {
              show: true,
              data: yData.reverse(),
              // offset: -45,
              position: "right",
              axisLabel: {
                lineHeight: 0,
                verticalAlign: "bottom",
                fontSize: 13,
                color: "rgba(255,255,255,.6)",
                formatter: "{value}",
              },
              axisLine: {
                show: false,
              },
              splitLine: {
                show: false,
              },
              axisTick: {
                show: false,
              },
            },
          ],
          series: [{
              show: true,
              type: "bar",
              barGap: "-100%",
              xAxisIndex: 1,
              barWidth: 4,
              itemStyle: {
                borderRadius: 4,
                color: '#03FFFF',
              },
              label: {
                show: false,
              },
              labelLine: {
                show: false,
              },
              z: 2,
              data: yData.reverse(),
            },
            {
              type: "effectScatter",
              xAxisIndex: 1,
              symbolSize: 7,
              symbolOffset: [0, -3],
              rippleEffect: { //涟漪特效
                period: 5, //动画时间，值越小速度越快
                brushType: 'stroke', //波纹绘制方式 stroke, fill
                scale: 4, //波纹圆环最大限制，值越大波纹越大
                number: 2,
              },
              itemStyle: {
                color: '#FFFFFF',
                shadowColor: "rgba(34, 223, 223, 0.5)",
                shadowBlur: 5,
                borderWidth: 1,
                opacity: 1,
              },
              z: 2,
              data: yData.reverse(),
              animationDelay: 1500,
              animationDuration: 1500
            },
            {
              z: 1,
              show: true,
              type: "bar",
              xAxisIndex: 1,
              barGap: "-100%",
              barWidth: 10,
              itemStyle: {
                borderRadius: 4,
                color: "#0D2D51",
              },
              label: {
                show: true,
                // offset: [150, -15],
                align: 'left',
                verticalAlign: "bottom",
                position: "left",
                fontSize: 12,
                color: "#fff",
                padding: [0, 0, 12, 10],
                formatter: function (data) {
                  return xData[data.dataIndex];
                },
              },
              data: max,
            },
          ],
        })
        window.addEventListener("resize", () => {
          myChart.resize();
        });
      }
    }
  }
</script>

<style scoped lang="less">

</style>