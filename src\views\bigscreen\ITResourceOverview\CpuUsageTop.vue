<template>
  <div class="cpu-usage-top">
    <div ref='cpuUsageTopChart' class='cpu-usage-top-chart'></div>
  </div>
</template>
<script>
import * as echarts from 'echarts';
import resizeObserverMixin from '@views/statsCenter/com/resizeObserverMixin'
import { getAction } from '@api/manage'

export default {
  name: 'CpuUsageTop',
  mixins: [resizeObserverMixin],
  data() {
    return {
      myChart: null,
      yAxisLeft: ['Top1', 'Top2', 'Top3', 'Top4', 'Top5'],
      yAxisRight: ['--', '--', '--', '--', '--'],
      barValue: [0, 0, 0, 0, 0],
      names: ['', '', '', '', ''],
    }
  },
  created() {
  },
  mounted() {
    var chartDom = this.$refs.cpuUsageTopChart;
    this.myChart = echarts.init(chartDom);
    this.initChart()
  },
  methods: {
    async initChart() {
      await getAction('/openAPI/getDevCpuOccupyTop').then(res=>{
        if(res.success && res.result && res.result.length){
          console.log("获取到的CPU使用率Top5",res)
          let tem = res.result;
          for(let i = 0; i < tem.length; i++){
            if(this.names[i] !== undefined){
              this.names[i] = tem[i].name
              this.barValue[i] = tem[i].cpuRate
              this.yAxisRight[i] = tem[i].cpuRate + '%'
            }else{
              break
            }
          }
        }
      })
      let option = {
        title: {
          text: 'CPU使用率Top5',
          textStyle: {
            color: 'rgba(255,255,255,0.7)',
            fontSize: 14
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: (params) => {
            // console.log("fjjfj",params)
            let data = params[0]
            if(this.names[data.dataIndex]){
              return this.names[data.dataIndex] + '：' + data.value + '%';
            }
            else{
              return "无数据"
            }
          }
        },
        grid: {
          left: 50,
          right: 65,
          bottom: 0,
          top:42,
          // containLabel: true
        },
        legend: {
          show: false
        },
        xAxis: {
          show: false,
          max:100,
          type: 'value',
        },
        yAxis: [
          {
            type: 'category',
            inverse: true,
            //#B7C8EB
            axisLabel: {
              color: 'rgba(255,255,255,0.85)',
              fontSize: 14
            },
            axisTick: {
              show: false
            },
            axisLine: {
              show: true,
              lineStyle:{
                color: 'rgba(183,200,235,0.29)',
              }
            },
            data: this.yAxisLeft
          },
          {
            type: 'category',
            position: 'right',
            inverse: true,
            alignTicks: true,
            offset: 5,
            axisTick: {
              show: false
            },
            axisLine: {
              show: true,
              lineStyle:{
                color: 'rgba(183,200,235,0.29)',
                type:"dotted",
              }
            },
            axisLabel: {
              color: 'rgba(255,255,255,0.7)',
              fontSize: 14
            },
            data: this.yAxisRight
          },
        ],
        series: [
          {
            // name: 'CPU使用率Top5',
            type: 'bar',
            data: this.barValue,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                { offset: 0, color: 'rgba(66,232,224,0.9)' },
                { offset: 1, color: 'rgba(55,255,252,0.9)' }
              ])
            },
            barWidth: 8,
            showBackground: true,
            backgroundStyle: {
              color: 'rgba(29, 97, 140, 0.15)'
            },
            label: {
              show: true,
              position: [2, -16],
              color: 'rgba(255,255,255,0.6)',
              fontSize: 12,
              formatter: (params) => {
                return this.names[params.dataIndex];
              }
            }
          }
        ]
      };
      option && this.myChart.setOption(option);
    },
    // 屏幕变化回调
    resizeObserverCb(){
      this.myChart.resize()
    },
  }
}
</script>
<style lang="less" scoped>
.cpu-usage-top {
  height: 100%;
  width: 100%;
  .cpu-usage-top-chart {
    height: 100%;
    width: 100%;
  }
  //background: #fff;
  //padding: 12px;

}
</style>
