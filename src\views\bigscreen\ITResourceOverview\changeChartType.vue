<template>
  <div class="time">
    <div
      v-for="(item, index) in typeList"
      :key="index"
      :class="[selectedIndex == index ? 'active' : '', 'item']"
      @click="changeType(index)"
    >
      {{ item }}
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {}
  },
  props: {
    selectedIndex: 0,
    typeList: {
      type: Array,
      default: function () {
        return ['端口总速率', '上下行速率']
      },
    },
  },
  methods: {
    changeType(index) {
      this.$emit('changeType', index)
    },
  },
}
</script>

<style scoped lang="less">
.time {
  display: flex;
  align-items: center;
  position: absolute;
  top: 20px;
  left: 16px;
  z-index: 99;
  .item {
    color: rgba(29, 157, 206, 0.4);
    background: none;
    border: 1px solid rgba(29, 157, 206, 0.4);
    margin-left: 5px;
    padding: 2px 10px;
    font-size: 12px;
    cursor: pointer;
    &.active {
      font-family: Source Han Sans CN;
      font-weight: 300;
      font-size: 12px;
      color: #e2ebf1;
      background: rgba(29, 157, 206, 0.2);
      border: 1px solid #1f99c7;
    }
  }
}
</style>
