<template>
  <div style="height: 100%">
    <div class="task-container">
      <div class="left-box">
        <metrics-tree
          :inputFlag="false"
          @selected="treeSeletedSearch"
          :tree-url="'/evaluate/metricsType/treeNew'"
          :btnIconName="'appstore'"
          :is-show-all-btn="true"
          :btnName="'全部指标'"
          :fieldKey="'id'"
          :is-show-btn-icon="true"
          :is-show-icon="false"
          style="padding-left: 15px"
        >
        </metrics-tree>
      </div>
      <div class="right-box">
        <div style="width: 100%">
          <div class="tabs-view">
            <div :class="['tab', activeKey == 1 ? 'active' : '']" @click="changeTab(1)">待填报列表</div>
            <div :class="['tab', activeKey == 2 ? 'active' : '']" @click="changeTab(2)">历史填报列表</div>
          </div>
          <div style="padding: 25px 5px 5px 0; height: calc(100% - 35px)">
            <a-card :bordered="false" :bodyStyle="{ padding: '0' }">
              <div class="table-page-search-wrapper">
                <a-form layout="inline" @keyup.enter.native="searchQuery" v-bind="formItemLayout">
                  <a-row :gutter="24" ref="row">
                    <a-col :span="spanValue">
                      <a-form-item label="报告名称">
                        <a-input
                          :maxLength='maxLength'
                          :allowClear="true"
                          autocomplete="off"
                          v-model="queryParam.projectName"
                          placeholder="请输入报告名称"
                        />
                      </a-form-item>
                    </a-col>

                    <a-col :span="colBtnsSpan()">
                      <span
                        class="table-page-search-submitButtons"
                        :style="(toRight && { float: 'right', overflow: 'hidden' }) || {}"
                      >
                        <a-button type="primary" class="btn-search btn-search-style" @click="searchQuery"
                          >查询</a-button
                        >
                        <a-button class="btn-reset btn-reset-style" @click="searchReset">重置</a-button>
                        <a v-if="isVisible" class="btn-updown-style" @click="doToggleSearch">
                          {{ toggleSearchStatus ? '收起' : '展开' }}
                          <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                        </a>
                      </span>
                    </a-col>
                  </a-row>
                </a-form>
              </div>
            </a-card>
            <a-card :bordered="false" style="flex: auto" :bodyStyle="{ padding: '0' }">
              <a-table
                ref="table"
                bordered
                :rowKey="
                  (record, index) => {
                    return index
                  }
                "
                :columns="columns"
                :dataSource="dataSource"
                :scroll="dataSource.length > 0 ? { x: 'max-content' } : {}"
                :pagination="ipagination"
                :loading="loading"
                @change="handleTableChange"
              >

             <span class="caozuo" slot="action" slot-scope="text, record">
  <a v-if="activeKey == 1 && record.p2d2mList && record.p2d2mList[0] && record.p2d2mList[0].metricsData" @click="handleEdit(record)">编辑问卷</a>
  <a v-else-if="activeKey == 1 && record.p2d2mList && record.p2d2mList.length > 0" @click="handleEdit(record)">填报</a>
  <a v-else-if="activeKey == 2" @click="handleDetail(record)">查看问卷</a>
</span>
                <template slot="tooltip" slot-scope="text">
                  <a-tooltip placement="topLeft" :title="text" trigger="hover">
                    <div class="tooltip">
                      {{ text }}
                    </div>
                  </a-tooltip>
                </template>
                <template slot="evaluationCycle" slot-scope="text, record">
                  {{ record.startTime }} ~ {{ record.endTime }}
                </template>
              </a-table>
            </a-card>
            <addQuestionModal ref="modalForm" @ok="modalFormOk"></addQuestionModal>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import MetricsTree from '@views/operationalEvaluationNew/operationalOrganization/modules/MetricsTree.vue'
import '@/assets/less/TableExpand.less'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { getAction } from '@/api/manage'
import { YqFormSearchLocation } from '@/mixins/YqFormSearchLocation'
import addQuestionModal from '@views/operationalEvaluationNew/materials/modules/addQuestionModal.vue'

export default {
  name: 'fillQuestionnaireList',
  mixins: [JeecgListMixin, YqFormSearchLocation],
  components: {
    MetricsTree,
    addQuestionModal,
  },
  data() {
    return {
      maxLength: 50,
      activeKey: 1,
      showTree: true,
      metricsId: '', // 记录左侧树选中的指标
      info: {
        id: 1,
      },
      formItemLayout: {
        labelCol: {
          style: 'width:90px',
        },
        wrapperCol: {
          style: 'width:calc(100% - 90px)',
        },
      },
      // 表头
      columns: [
        {
          title: '报告名称',
          dataIndex: 'projectName',
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
            return {
              style: cellStyle,
            }
          },
          scopedSlots: {
            customRender: 'tooltip',
          },
        },
        {
          title: '单位名称',
          dataIndex: 'deptNameStr',
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
            return {
              style: cellStyle,
            }
          },
          scopedSlots: {
            customRender: 'tooltip',
          },
        },
        {
          title: '评估指标',
          dataIndex: 'metricsNameStr',
          customCell: () => {
            let cellStyle = 'text-align: left;min-width: 150px;max-width:300px'
            return {
              style: cellStyle,
            }
          },
          scopedSlots: {
            customRender: 'tooltip',
          },
        },
        {
          title: '评估周期',
          dataIndex: 'evaluationCycle',
          scopedSlots: {
            customRender: 'evaluationCycle',
          },
        },
        // {
        //   title: '已填报单位数',
        //   dataIndex: 'submittedUnitsNumber',
        //   scopedSlots: {
        //     customRender: 'number',
        //   },
        // },
        // {
        //   title: '未填报单位数',
        //   dataIndex: 'unsubmittedUnitsNumber',
        //   scopedSlots: {
        //     customRender: 'number',
        //   },
        // },
        // {
        //   title: '状态',
        //     dataIndex: 'status',
        //   scopedSlots: {
        //     customRender: 'status'
        //   },
        // },
        {
          title: '发起人',
          dataIndex: 'sender',
        },
        {
          title: '操作',
          dataIndex: 'action',
          fixed: 'right',
          width: 100,
          scopedSlots: { customRender: 'action' },
        },
      ],
      url: {
        list: '/devops/projectInfo/projectListByCondition',
        delete: '/evaluate/projectInfo/delete',
        deleteBatch: '/evaluate/projectInfo/deleteBatch',
      },
    }
  },
  mounted() {},
  methods: {
    changeTab(key) {
      this.activeKey = key
      this.searchReset()
    },
    // 增加折叠展开
    fold() {
      this.showTree = false
    },
    open() {
      this.showTree = true
    },
    treeSeletedSearch(e, type) {
      if (e) {
        if (type === 'category') {
          this.queryParam.metricsTypeId = e
          this.queryParam.metricsId = null
        }
        if (type === 'metric') {
          this.queryParam.metricsId = e
          this.queryParam.metricsTypeId = null
        }
      } else {
        this.queryParam.metricsId = null
        this.queryParam.metricsTypeId = null
      }

      if (this.activeKey == 1) {
        this.queryParam.isHistory = null
      } else {
        this.queryParam.isHistory = 'true'
      }
      this.loadData(1)
    },
    loadData(arg) {
      if (!this.url.list) {
        this.$message.error('请设置url.list属性!')
        return
      }
      // 加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1
      }
      this.loading = true
      getAction(this.url.list, this.queryParam).then((res) => {
        if (res.success) {
          // 处理数据，将嵌套结构扁平化
          this.dataSource = res.result.map((item) => {
            return {
              id: item.projectInfo.id,
              projectName: item.projectInfo.projectName,
              deptNameStr: item.deptInfo.departName,
              startTime: item.projectInfo.startTime,
              endTime: item.projectInfo.endTime,
              sender: item.projectInfo.sender,
              // 保留原始数据，以便后续操作使用
              ...item,
            }
          })
          this.ipagination.total = res.result.length
        }
        this.loading = false
      })
    },
    searchReset() {
      if (this.activeKey == 1) {
        this.queryParam = {
          metricsId: this.metricsId,
          metricsTypeId: this.metricsTypeId,
          isHistory: null,
        }
      } else {
        this.queryParam = {
          metricsId: this.metricsId,
          metricsTypeId: this.metricsTypeId,
          isHistory: 'true',
        }
      }

      this.loadData(1)
    },
    // 去填报
    handleEdit: function (record) {
      console.log('打开==', record)
      this.$refs.modalForm.initData(record.p2d2mList)
      this.$refs.modalForm.title = '填报'
      this.$refs.modalForm.disableSubmit = false
    },
    handleDetail: function (record) {
      this.$refs.modalForm.initData(record.p2d2mList)
      this.$refs.modalForm.title = '查看详情'
      this.$refs.modalForm.disableSubmit = true
    },
    handleTableChange(pagination, filters, sorter) {
      //分页、排序、筛选变化时触发
      //TODO 筛选
      if (Object.keys(sorter).length > 0) {
        this.isorter.column = sorter.field;
        this.isorter.order = "ascend" == sorter.order ? "asc" : "desc"
      }
      this.ipagination = pagination;
      // this.loadData();
    }
  },
}
</script>
<style lang="less" scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
.card-style {
  height: 100%;
}
.tabs-view {
  display: flex;
  align-items: center;
  .tab {
    padding: 6px 15px;
    color: rgba(0, 0, 0, 0.45);
    border: 1px solid rgba(0, 0, 0, 0.2);
    background: #fff;
    border-radius: 5px;
    margin-right: 10px;
    cursor: pointer;
    &.active {
      color: #409eff;
      border: 1px solid #f5f5f5;
      // background: #f0f2f5;
      background: #f5f5f5;
    }
  }
}
.task-container {
  width: 100%;
  height: 100%;
  flex: 1;
  display: flex;
  overflow-x: hidden;
  overflow-y: auto;
  // padding-left: 24px;
  // padding-right: 24px;
  .openArrow {
    width: 12px;
    height: 48px;
    line-height: 48px;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    margin: auto;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.25);
    background: #f0f2f5;
    border-radius: 0 11px 11px 0;
    cursor: pointer;
    &:hover {
      color: #fff;
      background-color: #4080df;
    }
  }
  .foldArrow {
    width: 12px;
    height: 48px;
    line-height: 48px;
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    margin: auto;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.25);
    background: #f0f2f5;
    border-radius: 11px 0 0 11px;
    cursor: pointer;
    &:hover {
      color: #fff;
      background-color: #4080df;
    }
  }
  .left-box {
    overflow-x: hidden;
    overflow-y: auto;
    // padding-left: 15px;
    padding-right: 15px;
    width: 290px;
    border-right: 1px solid #e8e7e7;
    position: relative;
  }
  .right-box {
    width: 100%;
    background: #fff;
    flex: 1;
    overflow-x: hidden;
    overflow-y: auto;
    padding-top: 24px;
    padding-left: 24px;
    padding-right: 24px;
    .attribute-list {
      margin-bottom: 25px;
    }
  }
}
</style>
