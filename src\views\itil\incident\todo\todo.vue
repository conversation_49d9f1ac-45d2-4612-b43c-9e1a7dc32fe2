<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <!-- 搜索区域 -->
      <a-form layout="inline">
        <!-- 上三选择 -->
        <a-row :gutter="24">
          <a-col :span="5">
            <a-form-item label="类型">
              <!-- v-model="" @change="" -->
              <a-select placeholder="请选择" allowClear style="width: 93%;">
                <a-select-option value="1">1</a-select-option>
                <a-select-option value="2">2</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="5" style="padding-left: 0;">
            <a-form-item label="优先级">
              <a-select placeholder="请选择" allowClear style="width: 93%;">
                <a-select-option value="1">1</a-select-option>
                <a-select-option value="2">2</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="5" style="padding-left: 0;">
            <a-form-item label="状态">
              <a-select placeholder="请选择" :allowClear="true" style="width: 93%;">
                <a-select-option value="0">未处理</a-select-option>
                <a-select-option value="1">已处理</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="5" style="padding-left: 0;">
            <a-form-item label="标题">
              <a-input placeholder="请输入" :allowClear="true" style="width: 93%;"></a-input>
            </a-form-item>
          </a-col>
          <!-- 按钮区域 -->
          <a-col :span="4" style="padding-left: 31px;">
            <a-button type="primary">查询</a-button>
            <a-button type="primary" style="margin-left:15px">重置</a-button>

            <!-- <a @click="handleToggleSearch"
                    style="margin-left: 8px">
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                    </a> -->
          </a-col>
        </a-row>
        <!-- 下三按钮区 -->
        <a-row style="margin-bottom:16px">
          <a-col>
            <a-button type="primary">批量导出</a-button>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- table区域-begin -->
    <div>
      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="data"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        @change="handleTableChange"
        style="height: 467px;overflow:hidden;overflow-y:auto"
      >
        <!-- 操作 -->
        <span slot="action" slot-scope="text, record">
          <a @click="handleDeatails(record)">查看详情</a>
          <a-divider type="vertical" />
          <a @click="handleDispose(record.id)">处理</a>
          <a-divider type="vertical" />
          <a @click="handleSendback(record)">退回</a>
        </span>
      </a-table>
    </div>
    <todoDispose ref="todoDispose"></todoDispose>
    <todoDetails ref="todoDetails" @ok="modalFormOk"></todoDetails>
    <todoSendback ref="todoSendback"></todoSendback>
  </a-card>
</template>
<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import todoDispose from './modules/todoDispose'
import todoDetails from './modules/todoDetails'
import todoSendback from './modules/todoSendback'
export default {
  name: 'todo',
  mixins: [JeecgListMixin],
  components: {
    todoDispose,
    todoDetails,
    todoSendback
  },
  data() {
    return {
      // 表格假数据点击按钮需要
      data: [
        {
          id: '01',
          uri: 'SJ202009100001',
          title: '电脑闪屏',
          type: '鼓掌故障',
          priority: '低',
          flag: '新建',
          createTime: '2020-09-10 10:54:05',
          slaResponse: '0d 3h',
          slaAccomplish: '0d 8h'
        },
        {
          id: '02',
          uri: 'SJ202009100002',
          title: '电脑卡顿',
          type: '故障',
          priority: '低',
          flag: '新建',
          createTime: '2020-09-10 15:00:05',
          slaResponse: '0d 2h',
          slaAccomplish: '0d 2h'
        }
      ],
      // 表头
      columns: [
        // {
        //     title: "#",
        //     dataIndex: "",
        //     key: "rowIndex",
        //     width: 60,
        //     align: "center",
        //     customRender: function(t, r, index) {
        //         return parseInt(index) + 1;
        //     }
        // },
        {
          title: '编号',
          align: 'center',
          dataIndex: 'uri'
        },
        {
          title: '标题',
          align: 'center',
          dataIndex: 'title'
        },
        {
          title: '类型',
          align: 'center',
          dataIndex: 'type'
        },
        {
          title: '优先级',
          dataIndex: 'priority',
          align: 'center'
        },
        {
          title: '当前状态',
          dataIndex: 'flag',
          align: 'center'
          // scopedSlots: { customRender: "" }
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          align: 'center'
        },
        {
          title: 'SLA响应',
          dataIndex: 'slaResponse',
          align: 'center'
          // scopedSlots: { customRender: "" }
        },
        {
          title: 'SLA完成',
          dataIndex: 'slaAccomplish',
          align: 'center'
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: ''
      }
    }
  },
  methods: {
    handleDispose: function(id) {
      this.$refs.todoDispose.show(id)
      this.$refs.todoDispose.visible = true
    },
    handleDeatails: function(record) {
      this.$refs.todoDetails.edit(record)
      this.$refs.todoDetails.title = '事件详情'
      this.$refs.todoDetails.disableSubmit = false
    },
    handleSendback(record) {
      this.$refs.todoSendback.edit(record)
      this.$refs.todoSendback.title = '事件退回'
      this.$refs.todoSendback.disableSubmit = false
    }
  }
}
</script>
<style scoped></style>
