<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    :destroyOnClose="true"
    switchFullscreen
    :class="{toolmodal:key==='1',iframemodal:key==='2'}"
    @ok="handleOk"
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @cancel="handleCancel"
    cancelText="关闭">
    <test-tool ref="realForm" @ok="submitCallback" v-if="key === '1'" :disabled="disableSubmit" :data="data"></test-tool>
    <iframe :src="url" v-else frameborder="0" width="100%" height="100%" scrolling="auto"></iframe>
  </j-modal>
</template>

<script>

  import testTool from '@/views/opmg/tool/testTool'
  export default {
    name: 'TerminalDeviceModal',
    components: {
      testTool
    },
    data () {
      return {
        title:'',
        width:1200,
        visible: false,
        disableSubmit: false,
        data:null,
        url:'',
        key:''
      }
    },
    methods: {
      add () {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.add();
        })
      },
      edit (key, record) {
        this.visible=true
        this.key = key
        let origin = window.location.origin
        this.url = `${origin}/webssh/webssh.html?ip=${record||''}`
        this.$nextTick(()=>{
          if(key==='1'){
            this.$refs.realForm.ip = `${record || ''}`
          }
        })
      },
      close () {
        this.$emit('close');
        this.visible = false;
      },
      handleOk () {
        this.close()
      },
      submitCallback(){
        this.$emit('ok');
        this.visible = false;
      },
      handleCancel () {
        this.close()
      }
    }
  }
</script>
<style scoped lang="less">
.iframemodal {
  & ::v-deep .ant-modal-body{
    height: 600px;
  }
} 
</style>