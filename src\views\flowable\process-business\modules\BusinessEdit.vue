<template>
  <a-modal
    :title="title"
    :width="modalWidth"
    :visible='visible'
    :maskClosable='false'
    :confirmLoading='confirmLoading'
    :destroyOnClose="true"
    @cancel="handleCancel"
    cancelText="关闭"
  >
    <div v-if="generateStartFormVisible">
      <k-form-build
      ref="generateStartForm"
      :value="startFormJson"
      :dynamicData='dynamicData'
      :defaultValue="variables"
      @submit="handleSubmit"
      @assetsHandler="assetsHandler"
      />
    </div>
    <template slot="footer">
      <a-button key="close" @click="handleCancel"> 关闭 </a-button>
      <a-button key="back" type="primary" @click="edit"> 保存 </a-button>
      <a-button type="primary" :disabled="submitLoading"  @click="submitApply"> 提交申请 </a-button>
    </template>
    <assets-update-list ref="assetsUpdateModal" @setAssetsChange="setAssetsChange"></assets-update-list>
  </a-modal>
</template>

<script>
import { getAction, postAction } from '@/api/manage'
import AssetsUpdateList from '@/views/cmdb/assets/assetsUpdate/AssetsUpdateList'
export default {
  name: 'businessAdd',
  components: {
    AssetsUpdateList,
  },
  data() {
    return {
      title: '编辑',
      confirmLoading: false,
      modalWidth: '1200px',
      form: this.$form.createForm(this),
      visible: false,
      required: false,
      generateStartFormVisible: false,
      startFormJson: {},
      variables: null,
      business: null,
      businessKey:null,
      dynamicData: {
        bpmnDepartTreeData: []
      },
      submitLoading:false,
      tempProcessInstanceId: null// 临时流程id
    }
  },
  created() {
    this.getTreeData()
  },
  props:{
  },
  methods: {
     //打开资产编辑
    assetsHandler(e){
      if (this.tempProcessInstanceId) {
        this.$refs.assetsUpdateModal.tempId = this.tempProcessInstanceId
      } else {
        this.$refs.assetsUpdateModal.tempId = ''
      }
      this.$refs.assetsUpdateModal.init(e)
    },
    //监听到资产变化
    setAssetsChange(data, tempId) {
      this.tempProcessInstanceId = tempId
      this.$refs.generateStartForm.setData({
        assetsChangeList:data,
      })
    },
    init(record) {
      this.business = record
      console.log(record)
      this.visible = true
      getAction('/business/actZBusiness/queryById', { id: record.id }).then((res) => {
        if (res.success) {
          const data = res.result
          if (data && data.formValue) {
            this.startFormJson = JSON.parse(data.formJson)
            this.variables = data.variables
            this.generateStartFormVisible = true
            if (this.variables.tempProcessInstanceId) {
              this.tempProcessInstanceId = this.variables.tempProcessInstanceId
            }
          }
        }
      })
    },
    getTreeData() {
      getAction('/sys/sysDepart/queryTreeList').then((res) => {
        this.dynamicData.bpmnDepartTreeData = res.result
      })
    },
    submitApply(){
      if (this.$refs.generateStartForm) {
        this.$refs.generateStartForm.getData().then((values) => {
          let tempUrl='/business/actZBusiness/saveAndApply'
          Object.assign(values, { tempProcessInstanceId: this.tempProcessInstanceId })
          // 校验资产变更
          if (values.changeManageClass&&values.changeManageClass.includes('资产变更')) {
            if (!values.assetsChangeList || values.assetsChangeList.length == 0) {
              this.$message.error('未添加变更资产')
              return
            }
          }
          //工单来源：告警转工单
          let formValue=JSON.parse(this.business.formValue)
          if (formValue.alarmId&&formValue.alarmId.length>0){
            Object.assign(values, { alarmId: formValue.alarmId })
            tempUrl='/alarm/alarmHistory/saveAndApply'
          }
          let formData = {}
          formData.formValue = JSON.stringify(values)
          formData.id = this.business.id
          formData.assignNextNode = formValue.assignNextNode
          this.submitLoading = true;
          postAction(tempUrl, formData).then((res) => {
            if (res.success) {
              this.$message.success(res.message)
            } else {
              this.$message.warning(res.message)
            }
            this.handleCancel()
            this.submitLoading = false
          }).catch((err)=>{
            this.$message.warning(err.message)
            this.submitLoading = false
          })
        })
      }else{
        this.$message.error("流程表单不存在！")
      }
    },
    handleSubmit() {},
    edit() {
      if (this.$refs.generateStartForm) {
        this.$refs.generateStartForm.getFieldsValue().then((values) => {
          let formData = {}
          Object.assign(values, { tempProcessInstanceId: this.tempProcessInstanceId })

          let formValue=JSON.parse(this.business.formValue)
          if (formValue.alarmId&&formValue.alarmId.length>0){
            Object.assign(values, { alarmId: formValue.alarmId })
          }
          if (formValue.assignNextNode&&formValue.assignNextNode.length>0){
            Object.assign(values, { assignNextNode: formValue.assignNextNode })
          }

          formData.formValue = JSON.stringify(values)
          formData.id = this.business.id
          postAction('/business/actZBusiness/edit', formData).then((res) => {
            if (res.success) {
              this.$message.success(res.message)
            } else {
              this.$message.error('编辑失败！')
            }
            this.handleCancel()
          })
        })
      }
    },
    doStartInstance() {
      if (this.$refs.generateStartForm) {
        this.$refs.generateStartForm.getData().then((values) => {
            console.log(values)
            if (values && values != undefined) {
              let processInstanceFormData = JSON.stringify(values)
              let realValues = Object.assign({ processInstanceFormData }, values)
              postAction('/flowable/processInstance/start', {
                processDefinitionId: this.business.procDefId,
                businessKey: this.businessKey,
                values: realValues,
                ccUserIds: [],
              }).then((res) => {
                if (res.code == 200) {
                  this.$message.success('发起成功')
                  this.delete()
                } else {
                  this.$message.error('发起失败!')
                }
                this.handleCancel()
              })
            }
          })
          .catch((e) => {})
      } else {
        postAction('/flowable/processInstance/start', {
          processDefinitionId: this.business.procDefId,
          businessKey: this.businessKey,
          ccUserIds: [],
        }).then((res) => {
          if (res.code == 200) {
            this.$message.success('发起成功')
            this.delete()
          } else {
            this.$message.error('发起失败!')
          }
          this.handleCancel()
        })
      }
    },
    delete(){
      this.$emit('deleteRecord',this.business.id)
    },
    close() {
      this.generateStartFormVisible = false
      this.visible = false
      this.$emit('ok')
    },
    handleCancel() {
      this.close()
    },
  },
}
</script>
<style lang="less" scoped>
@import '~@assets/less/YQNormalModal.less';
/deep/ .ant-modal {
  top: 30px;
}
/deep/ .ant-modal-body {
  max-height: calc(100vh - 55px - 53px - 30px - 30px); // 55:tab选项卡高度;53:按钮区域高度;30:距离顶部/底部的间距
  overflow: auto;
}
</style>