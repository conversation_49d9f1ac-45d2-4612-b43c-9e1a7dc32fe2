<template>
  <j-modal
    ref='perModal'
    title="预约"
    :width="width"
    :visible="visible"
    :destroyOnClose="true"
    switchFullscreen
    :centered='true'
    @ok="handleOk"
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }"
    @cancel="handleCancel"
    cancelText="关闭">
    <div style='height: 50vh;overflow-y: auto'>
      <a-form-model
        ref="ruleForm"
        :model="form"
        :rules="rules"
        :label-col="labelCol"
        :wrapper-col="wrapperCol"
      >
        <a-form-model-item label="设备名称" prop="deviceName">
          <a-input v-model="form.deviceName" :disabled="editstatus" placeholder='请输入设备名称' />
        </a-form-model-item>
        <a-form-model-item label="预约时间" prop="perDates">
          <a-range-picker v-model='form.perDates'    :disabled-date="disabledDate"  @change="onPerDateChange" />
        </a-form-model-item>
        <a-form-model-item label="使用人" prop="userName">
          <a-input v-model="form.userName" :disabled="editstatus" placeholder='请输入使用人名称'  type="text" />
        </a-form-model-item>
        <a-form-model-item label="支撑项目" prop="supportingProject">
          <a-select  style="width: 100%" v-model='form.supportingProject' :disabled="editstatus" placeholder='请选择支撑项目' :options="projects"></a-select>
        </a-form-model-item>
        <a-form-model-item label="备注" prop="desc">
          <a-input v-model="form.desc" type="textarea" />
        </a-form-model-item>
      </a-form-model>
    </div>
  </j-modal>
</template>
<script>
import {projects} from './projects'
import moment from 'moment'
export default {
  name: 'PerModal',
  components: { },
  data() {
    return {
      projects,
      title: '',
      width: '1000px',
      visible: false,
      disableSubmit: false,
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
      other:"",
      rules: {
        deviceName: [
          { required: true, message: '请输入设备名称', trigger: 'blur' },
          {  max: 20, message: '最多20个字符', trigger: 'blur' },
        ],
        perDates: [
          { required: true, message: '请选择预约时间', trigger: 'change' },
        ],
        supportingProject: [
          { required: true, message: '请选择支撑项目', trigger: 'change' },
        ],
        userName: [
          { required: true, message: '请输入使用人', trigger: 'blur' },
          {  max: 10, message: '最多10个字符', trigger: 'blur' },
        ],  desc: [
          {  max: 100, message: '最多100个字符', trigger: 'blur' },
        ],
      },
      form: {
        deviceName: '',
        perDates: [],
        startTime: '',
        endTime: '',
        supportingProject: undefined,
        userName: '',
        desc: '',
      },
      dates:[
        {
          startTime: '2024-11-23 00:00:00',
          endTime: '2024-11-23  23:59:59',
        },{
          startTime: '2024-11-27 00:00:00',
          endTime: '2024-11-28  23:59:59',
        },{
          startTime: '2024-12-02 00:00:00',
          endTime: '2024-12-02 23:59:59',
        },
      ],
      editstatus:false,
    }
  },
  created() {
      console.log()
  },
  methods: {
    //禁用时间
    disabledDate(value) {
      if(value){
        for(let i=0;i<this.dates.length;i++){
          if(value.valueOf() >= moment(this.dates[i].startTime ).valueOf() && value.valueOf() <= moment(this.dates[i].endTime ).valueOf()){
            console.log(value.valueOf(),moment(this.dates[i].startTime ).valueOf(),moment(this.dates[i].endTime ).valueOf())
            console.log(value,moment(this.dates[i].startTime ),moment(this.dates[i].endTime ))
            return true
          }
        }
        return value && value.valueOf() < Date.now()
      }

    },
    initForm(data) {
      return {
        deviceName: '',
        perDates: [],
        startTime: '',
        endTime: '',
        supportingProject: undefined,
        userName: '',
        desc: '',
      }
    },
    add(device) {
      this.editstatus = false;
      Object.assign(this.form, this.initForm())
      this.form.deviceName = device.deviceName
      this.visible = true
    },
    edit(record) {
      this.editstatus = true;
      record.perDates = [moment(record.startTime), moment(record.endTime)]
      Object.assign(this.form, record)
      // this.form.perDates = [moment(record.startTime), moment(record.endTime)]
      this.visible = true
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      this.$emit('ok')
      this.close()
    },
    onSubmit() {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          alert('submit!');
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },

    handleCancel() {
      this.close()
    },
    //预约时间变化
    onPerDateChange(e,strs){
      this.form.startTime = strs[0] + ' 00:00:00'
      this.form.endTime = strs[1] + ' 23:59:59'
    }
  },

}
</script>
<style scoped lang='less'>

</style>