<template>
  <div class="page-box">
    <div class="page-inside">
      <div class="core-box">
        <div class="coreList">
          <div class="coreList-item" v-for="(item, index) in dataList" :key="index">
            <div class="coreList-item-top">
              <img v-if="item.categoryName == '服务器'" src="../../assets/bigScreen/32.png" alt="" />
              <img v-else-if="item.categoryName == '终端'" src="../../assets/bigScreen/33.png" alt="" />
              <img v-else-if="item.categoryName == '数据库'" src="../../assets/bigScreen/34.png" alt="" />
              <img v-else-if="item.categoryName == '应用服务器'" src="../../assets/bigScreen/35.png" alt="" />
              <img v-else-if="item.categoryName == '安全设备'" src="../../assets/bigScreen/36.png" alt="" />
              <img v-else-if="item.categoryName == '网络设备'" src="../../assets/bigScreen/37.png" alt="" />
              <img v-else-if="item.categoryName == '存储'" src="../../assets/bigScreen/38.png" alt="" />
              <img v-else-if="item.categoryName == '总计'" src="../../assets/bigScreen/40.png" alt="" />
              <img v-else-if="item.categoryName == ''" src="../../assets/bigScreen/39.png" alt="" />
              <img v-else src="../../assets/bigScreen/39.png" alt="" />
              <span> {{ item.categoryName }} </span>
            </div>
            <div class="coreList-item-core">
              <span>总数：{{ item.statData.total }} 台</span>
              <div>
                <span><img src="../../assets/bigScreen/28.png" alt="" class="stateImg" /></span>
                <span style="padding-left: 5px">在线：{{ item.statData.status1 }} 台</span>
              </div>
              <div>
                <span><img src="../../assets/bigScreen/57.png" alt="" class="stateImg" /></span>
                <span style="padding-left: 5px">离线：{{ item.statData.status0 }} 台</span>
              </div>
              <div>
                <span><img src="../../assets/bigScreen/56.png" alt="" class="stateImg" /></span>
                <span style="padding-left: 5px">告警：{{ item.statData.alarmNum }} 台</span>
              </div>
              <div>
                <span>
                  <a-icon type="stop" theme="twoTone" two-tone-color="#eb2f96" style="font-size: 12px" /></span>
                <span style="padding-left: 5px">禁用：{{ item.statData.disableNum }} 台</span>
              </div>
            </div>
          </div>
        </div>
        <div style="text-align: right; position: relative; margin: 30px 30px 30px auto;">
          <a-pagination show-size-changer :hideOnSinglePage="false" :default-current="dataCurrent" :total="dataTotal"
            @change="onChange" :page-size="pageSize" :pageSizeOptions="['18', '36', '54']"
            :show-total="(total) => `共 ${dataTotal} 条`" @showSizeChange="onShowSizeChange" size="small">
          </a-pagination>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  import {
    getAction
  } from '@/api/manage'

  export default {
    data() {
      return {
        dataTotal: 0,
        pageSize: 18,
        dataList: [],
        dataCurrent: 1,
        url: {
          list: '/data-analysis/cmdb-category/list',
          device: '/data-analysis/cmdb-category/device',
        },
      }
    },
    mounted() {
      this.list()
    },
    methods: {
      list() {
        getAction(this.url.list, {
          pageSize: this.pageSize,
          current: this.dataCurrent
        }).then((res) => {
          if (res.code == 200) {
            this.dataList = res.result.records
            this.dataTotal = res.result.total
          }
        })
      },
      onChange(pageNumber, pageSize) {
        this.dataCurrent = pageNumber
        this.pageSize = pageSize
        this.list()
      },
      onShowSizeChange(current, pageSize) {
        this.pageSize = pageSize
        this.dataCurrent = current
        this.list()
      },
    },
  }
</script>
<style lang="less" scoped>
  /deep/.ant-pagination {
    color: white;
  }

  /deep/.ant-pagination-item-active {
    background: rgba(255, 255, 255, 0.3);
  }

  /deep/.ant-pagination-item a {
    color: white;
  }

  /deep/.ant-pagination-item-link {
    color: white;
  }

  .stateImg {
    height: 12px;
    width: 12px;
  }

  .page-box {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;

    .page-inside {
      background: #111217;
      width: calc(100% - 32px);
      height: calc(100% - 32px);

      .top {
        width: 100%;
        height: 60px
          /* 58/80 */
        ;
        display: flex;
        justify-content: flex-end;
        color: #fff;
        padding-right: 13px;

        .img {
          display: flex;

          .img-item {
            display: flex;
            align-items: center;
            margin-left: 0.2875rem
              /* 23/80 */
            ;

            span {
              margin-left: 0.1375rem
                /* 11/80 */
              ;
            }
          }
        }
      }

      .core-box {
        width: 100%;
        height: calc(100% - 76px);
        overflow-y: scroll;

        .coreList {
          display: flex;
          flex-wrap: wrap;
          flex-direction: row;
          margin-top: 20px;

          .coreList-item {
            width: calc((100% - 16px) / 6 - 16px);
            height: 180px
              /* 238/80 */
            ;
            margin: 0 0 16px 16px;
            background: #212226;
            display: flex;
            flex-direction: column;
            padding: 8px
              /* 23/80 */
            ;

            .coreList-item-top {
              height: 0.8125rem
                /* 65/80 */
              ;
              display: flex;
              align-items: center;

              img {
                width: 0.3125rem
                  /* 25/80 */
                ;
                height: 0.3125rem
                  /* 25/80 */
                ;
              }

              span {
                font-size: 0.2rem
                  /* 16/80 */
                ;
                color: #02c6ec;
                margin-left: 0.15rem
                  /* 12/80 */
                ;
                padding-top: 0.1rem
                  /* 8/80 */
                ;
              }
            }

            .coreList-item-core {
              color: #fff;
              display: flex;
              flex-direction: column;
              font-size: 0.175rem
                /* 14/80 */
              ;

              span {
                margin: 0.025rem
                  /* 4/80 */
                  0;
              }
            }

            .coreList-item-bottom {
              height: 0.6rem
                /* 48/80 */
              ;
              display: flex;
              align-items: center;
              justify-content: flex-end;

              img {
                margin-left: 0.1rem
                  /* 8/80 */
                ;
              }
            }
          }
        }
      }
    }
  }

  ::-webkit-scrollbar {
    background-color: #eee;
    display: none;
  }
</style>