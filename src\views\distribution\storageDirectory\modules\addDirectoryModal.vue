<template>
  <j-modal
    :title='title'
    :width='width'
    :centered='true'
    :visible='visible'
    :destroyOnClose='true'
    cancelText='关闭'
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @ok='handleOk'
    @cancel='handleCancel'
  >
    <a-spin :spinning='confirmLoading'>
      <j-form-container :disabled='disableSubmit'>
        <a-form-model ref='form' :model='model' :rules='validatorRules' slot='detail' v-bind='formItemLayout'>
          <a-row>
            <a-col :span='24'>
              <a-form-model-item label='文件夹名称' prop='folderName'>
                <a-input v-model='model.folderName' :allow-clear='true' placeholder='请输入文件夹名称'/>
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </j-form-container>
    </a-spin>
  </j-modal>
</template>
<script>
import { getAction, httpAction ,postAction} from '@api/manage'
export default {
  name: 'AddClusterUsersModal',
  data() {
    return {
      title: '新增',
      width: '600px',
      disableSubmit: false,
      visible: false,
      confirmLoading: false,
      formItemLayout: {
        labelCol: {
          xs:{span:24 },
          sm:{span:24},
          md:{span:5}
        },
        wrapperCol: {
          xs:{span:24 },
          sm:{span:24},
          md:{span:18}
        }
      },
      model: {},
      validatorRules: {
        folderName: [
          { required: true,min: 1,max: 30,validator: this.validateFolderName}
        ]
      },
      url: {
        add: '/distributedStorage/create',
        edit: '/distributedStorage/edit'
      }
    }
  },
  methods: {
    validateFolderName(rule, value, callback) {
      if (rule.required){
        if (!value){
          callback('请输入文件夹名称')
        }else {
          let { min, max, fullField } = rule
          let reg= new RegExp(`^.{${min},${max}}$`)
          if (!reg.test(value)){
            callback(`长度${min}到${max}个字符`)
          } else {
            callback()
          }
        }
      }else {
        callback()
      }
    },
    add(record) {
      this.edit(record)
    },
    edit(record) {
      this.visible = true
      this.$nextTick(() => {
        this.model=JSON.parse(JSON.stringify(record))
        if (this.model.folderName){
          this.model.oldFName=this.model.folderName
        }
      })
    },
    handleOk() {
      let that = this
      that.$refs.form.validate((err, values) => {
        if (err) {
          that.confirmLoading = true
          let httpurl =''
          let method = ''
          let formData = JSON.parse(JSON.stringify(that.model))
          let pArr=that.model.pathArray
          formData.path=pArr.length>0?pArr.join('/')+'/'+that.model.folderName:that.model.folderName
          if (!that.model.oldFName){
             httpurl = that.url.add
             method = 'post'
            delete formData.folderName
          }else {
            httpurl = that.url.edit
            method = 'put'
            formData.pathFrom=pArr.length>0?pArr.join('/')+'/'+that.model.oldFName:that.model.oldFName
            formData.pathTo=formData.path
            delete formData.folderName
            delete formData.oldFName
            delete formData.path
          }
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
                that.close()
              } else {
                that.$message.warning("操作失败")
              }
              that.confirmLoading = false
            }).catch((err) => {
            that.$message.warning("操作失败")
            that.confirmLoading = false
          })
        }
      })
    },
    handleCancel() {
      this.close()
    },
    close() {
      this.visible = false
    },
  }
}
</script>
<style scoped lang='less'>
@import '~@assets/less/normalModal.less';
</style>