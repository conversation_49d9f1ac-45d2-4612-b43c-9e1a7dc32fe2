<template>
  <j-modal
    :title="title"
    :width="width"
    :centered='true'
    :visible="visible"
    switchFullscreen
    :destroyOnClose="true"
    @ok="handleOk"
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @cancel="handleCancel"
    cancelText="关闭" 
    >
    <extend-field-list ref="realForm" @ok="submitCallback" :disabled="disableSubmit"></extend-field-list>
  </j-modal>
</template>
<script>

  import ExtendFieldList from './ExtendFieldList'
  export default {
    name: 'ExtendFieldListModal',
    components: {
      ExtendFieldList
    },
    data () {
      return {
        title:'',
        width:1000,
        visible: false,
        disableSubmit: false
      }
    },
    methods: {
      edit1 (record) {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.edit(record);
        })
      },
      
      close () {
        this.$emit('close');
        this.visible = false;
      },
      handleOk () {
       this.close()
      },
      submitCallback(){
        this.$emit('ok');
        this.visible = false;
      },
      handleCancel () {
        this.close()
      }
    }
  }
</script>
<style lang="less" scoped>
@import '~@assets/less/normalModal.less';
</style>
