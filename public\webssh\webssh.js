function WSSHClient(deviceId='') {
  this._connection = null;
  this._isConnected = false;
  //设备信息--远程管理创建连接会传设备id
  this._deviceId=deviceId
}

function getUUID() {
  var s = [];
  var hexDigits = "0123456789abcdef";
  for (var i = 0; i < 32; i++) {
    s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
  }
  s[14] = "4"; // bits 12-15 of the time_hi_and_version field to 0010
  s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1); // bits 6-7 of the clock_seq_hi_and_reserved to 01
  s[8] = s[13] = s[18] = s[23];
  return s.join("");
}
function getSSHPath(deviceId='') {
  var sshPath;
  //获取config.js中apiUrl
  var apiUrl = window.config.apiUrl;

  //临时变量,转为小写,防止大小写不匹配比对结果不准确
  var lowApiUrl = apiUrl.toLowerCase();
  var root;
  if (lowApiUrl.startsWith("http") || lowApiUrl.startsWith("https")) {
    //aipUrl中为全路径 协议+ip+端口+contextPath
    root = apiUrl;
    //去掉尾部多余 '/'
    if (root.lastIndexOf('/') === root.length - 1) {
      root = root.substring(0, root.lastIndexOf('/'))
    }
  } else {
    //aipUrl只包含contextPath
    //从地址栏获取当前路径
    root = window.document.location.origin;
  }
  console.log('========root===',root)

  var uuid = getUUID();
  let userId = ''
  if (window.localStorage.pro__Login_Userinfo) {
    let info = JSON.parse(window.localStorage.pro__Login_Userinfo)
    if (info.value && info.value.id) {
      userId = info.value.id
    }
  }
  if (userId) {
    sshPath = root + '/websocket/' + uuid + ',' + userId + '_ssh';
    if (deviceId) {
      sshPath = root + '/websocket/' + uuid + ',' + userId + ',' + deviceId + '_ssh';
    }
  } else {
    sshPath = root + '/websocket/' + uuid + '_ssh';
  }
  //根据不同协议使用 ws或wss
  var lowerSshPath = sshPath.toLowerCase();
  if (lowerSshPath.startsWith("http")) {
    //http协议使用ws
    sshPath = "ws:" + sshPath.substring(sshPath.indexOf("//"))
  } else {
    //https协议使用wss
    sshPath = "wss:" + sshPath.substring(sshPath.indexOf("//"))
  }
  return sshPath;
}

WSSHClient.prototype.connect = function (options) {
  // 先关闭已有连接
  this.close();

  if (window.WebSocket) {
    try {
      //如果支持websocket
      this._connection = new WebSocket(getSSHPath(this._deviceId));
      this._isConnected = false;
      // 保存回调函数
      this._callbacks = {
        onError: options.onError || function(){},
        onConnect: options.onConnect || function(){},
        onData: options.onData || function(){},
        onClose: options.onClose || function(){}
      };

      const self = this;
      this._connection.onopen = function() {
        self._isConnected = true;
        self._callbacks.onConnect();
      };

      this._connection.onmessage = function(evt) {
        var data = evt.data.toString();
        self._callbacks.onData(data);
      };

      this._connection.onclose = function(evt) {
        self._callbacks.onClose(evt);
        self._cleanup();
      };

      this._connection.onerror = function(error) {
        self._callbacks.onError(error);
        self._cleanup();
      };
    }catch (error) {
      this._cleanup();
      options.onError(error);
    }
  } else {
    //否则报错
    options.onError('WebSocket Not Supported');
    return;
  }
};

WSSHClient.prototype.close = function() {
  if (this._connection) {
    try {
      // 检查 WebSocket 状态
      if (this._connection.readyState === WebSocket.OPEN ||
        this._connection.readyState === WebSocket.CONNECTING) {
        this._connection.close(1000, 'Normal closure');
      }
    } catch (error) {
      console.error('关闭 WebSocket 时出错:', error);
    } finally {
      this._cleanup();
      this._connection = null;
    }
  }
};
WSSHClient.prototype._cleanup = function() {
  // 清理连接状态和回调
  this._isConnected = false;

  if (this._connection) {
    // 移除所有事件监听器
    this._connection.onopen = null;
    this._connection.onmessage = null;
    this._connection.onclose = null;
    this._connection.onerror = null;
  }

  this._callbacks = null;
};
WSSHClient.prototype.send = function (data) {
  this._connection.send(JSON.stringify(data));
};

WSSHClient.prototype.sendInitData = function (options) {
  //连接参数
  this._connection.send(JSON.stringify(options));
}

WSSHClient.prototype.sendClientData = function (data) {
  //发送指令
  this._connection.send(JSON.stringify({"operate": "command", "command": data}))
}
WSSHClient.prototype.sendEnterData = function (data) {
  //console.log('命令sendEnterData===',data)
  //发送指令
  this._connection.send(JSON.stringify({"operate": "enterData", "enterData": data}))
}
// var client = new WSSHClient();
