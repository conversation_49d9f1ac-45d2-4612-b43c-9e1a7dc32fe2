<template>
  <div class='zr-national-status' ref='zrNationalNodes'>
    <div class='national-status-content' v-if='listData.length'>
      <slot>
        <vue-seamless-scroll v-if='srollOption.autoPlay' :data='listData' class='scroll-warp'
                             :class-option='srollOption'>
          <ul class='scroll-list'>
            <li class='scroll-list-item' v-for='(item, index) in listData'
                :key='index'>
              <div v-if='item.name === "运行状态"'>
                <span>{{item.name}}：</span>
                <span :style='{color:item.color}'>{{item.label}}</span>
              </div>
              <div v-else class='days-item' style='display: flex;justify-content: center'>
                <days-box :days='item.value' :label='item.name' :unit='"天"'></days-box>
              </div>
            </li>
          </ul>
        </vue-seamless-scroll>
      </slot>
    </div>
  </div>
</template>
<script>
import ZrBigscreenTitle from '@views/zrBigscreens/modules/ZrBigscreenTitle.vue'
import vueSeamlessScroll from 'vue-seamless-scroll'
import resizeObserverMixin from '@views/statsCenter/com/resizeObserverMixin'
import { StatusInfo, businessStatus } from '@/views/zrBigscreens/modules/zrUtil'
import {ajaxGetDictItems} from '@/api/api'
import DaysBox from './DaysBox.vue'
import { getAction, postAction, httpAction } from '@/api/manage'
export default {
  name: 'ZrStatusInfo',
  components: { ZrBigscreenTitle, vueSeamlessScroll, DaysBox },
  mixins: [resizeObserverMixin],
  props:{
    scene:{
      type:String,
      default:'',
    },
    deptId:{
      type:String,
      default:""
    }
  },
  data() {
    return {
      listData: [],
      srollOption: {
        step: 0.8, // 步长
        speed: 100, // 滚动速度
        timer: 3000,// 滚动时间间隔
        autoPlay: true,
        limitMoveNum: 2,
        singleHeight: 60 ,
        waitTime: 5000,
      },
      maxNum: 0,
      colors:[
        '#55A7F4', // 正常
        '#F4A655', // 异常
        '#F45555'  // 故障
      ],
      dictData:[],
      orignData:[
        {
          "value": 0,
          "text": "稳定运行",
        },
        {
          "value":0,
          "text": "连续运行",
        },
        {
          "value":2,
          "text": "运行状态",
        }
      ]
    }
  },
  created() {
   /* this.listData = StatusInfo.filter(el=>el.show).map(item=>{
      if(item.name==='运行状态'){
        let stu = businessStatus.find(el=>el.value===item.value)
        if(stu){
          item.label = stu.label
          item.color = stu.color
        }
      }
      return item
    })*/
  },
  mounted() {
    this.getStatusInfo()
  },
  methods: {
    async getStatusInfo(){
      await this.getStatusFromDict()
      getAction('/monitor/situation/getNodeStatusAndDevList',{
        rootDeptId:this.deptId,
        withNodeDev:"true",
        withRunTime:"true",
      }).then(res=>{
        if(res.success && res.result && res.result[0] && res.result[0].nodeDevObject){
          let tem = res.result[0].nodeDevObject
          let day = 0
          if(tem && tem.sysUpTime){
            let temArr = tem.sysUpTime.split('天')
            day = temArr.length>1?temArr[0]:"0"
          }
          this.orignData[0].value = day
          this.orignData[1].value = day
          if(tem.status == 0){
            this.orignData[2].value = 2
          }else if(tem.alarmStatus === 0){
            this.orignData[2].value = 1
          }else{
            this.orignData[2].value = 0
          }
          this.dictData.forEach(el=>{
            if(el.text === '运行状态'){
               this.orignData[2].value = el.value
            }else if(el.text === "连续运行"){
              this.orignData[1].value = el.value
            } else if(el.text === "稳定运行"){
              this.orignData[0].value = el.value
            }
          })
        }
      }).finally(()=>{
        this.datMap()
      })
    },
    //获取字典里数据
    async getStatusFromDict(){
      ajaxGetDictItems('ZR_BIGSCREEN_STATUS').then(res => {
          if(res.success){
            this.dictData = res.result.filter(el=>Number(el.value)>=0);
          }
      })
    },
    datMap(){
      this.listData = this.orignData.map(item => {
        let tem = {
          name: item.text,
          value: Number(item.value),
        }
        if(tem.name==='运行状态'){
          let stu = businessStatus.find(el=>el.value==item.value)
          if(stu){
            tem.label = stu.label
            tem.color = stu.color
          }
        }
        return tem
      })
    },
    // 屏幕变化回调
    resizeObserverCb() {
    },
  }
}
</script>


<style scoped lang='less'>
.zr-national-status {
  height: 60px;
  .national-status-content {
    height: 100%;
    overflow: hidden;
  }
}
.scroll-warp {
  height: 100%;
  overflow: hidden;

  .scroll-list {
    width: 100%;
    height: 100%;
    margin: 0px;
    padding: 0px;
  }


}
.scroll-list-item {
  display: flex;
  align-items: center;
  color: rgba(237, 245, 255, 0.95);
  font-size: 24px;
  justify-content: center;
  height: 60px;
  font-weight: bold;
  line-height: 60px;
  letter-spacing: 3px;
}

</style>