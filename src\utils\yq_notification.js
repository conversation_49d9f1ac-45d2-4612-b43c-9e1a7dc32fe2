// import Vue from 'vue'
import { notification } from 'ant-design-vue'
import '../assets/less/yq_notification.css'
//弹窗样式
function optionAddStyleAttrs(option,tmProps){
  let location=setMarginLeft(tmProps.position,tmProps.width)
  option.style= {
    width: tmProps.width + 'px',
    marginLeft:location
  }
}
function setMarginLeft(position,width){
  if(position==='topCenter'){
    return `${(384 - width) /2 }px`
  }
  else if(position==='bottomRight'||position==='topRight'){
    return `${384 + 8 - width}px`
  }
  else{
    return 0+'px'
  }
}
//根据信息类型，显示不同的图标
function optionAddIconAttrs(option,tmProps){
  if (tmProps.showTypeIcon) {
    let iconAttrs = matchIconAttrs(tmProps)
    option.icon = (h) => {
      return h('a-icon', {
        props: { type: iconAttrs.iconName },
        attrs: { class: iconAttrs.colorClass }
      })
    }
  }
}
function matchIconAttrs(tmProps) {
 let typeIconAttrs=[
    { type: 'success', iconName: 'check-circle', colorClass: 'ant-notification-notice-icon-success' },
    { type: 'info', iconName: 'info-circle', colorClass: 'ant-notification-notice-icon-info' },
    { type: 'warning', iconName: 'exclamation-circle', colorClass: 'ant-notification-notice-icon-warning' },
    { type: 'error', iconName: 'close-circle', colorClass: 'ant-notification-notice-icon-error' },
    { type: 'alarm', iconName: 'warning', colorClass: 'ant-notification-notice-icon-alarm' }
  ]
  let iconAttrs = typeIconAttrs.filter((item) => {
    return item.type === tmProps.typeName ? true : false
  })
  return iconAttrs[0]
}
//自定义按钮，数组格式，例如
function optionAddBtnAttrs(option,tmProps){
  if (tmProps.btns.length > 0) {
    option.btn = h => {
      return h('div', {}, getButtons(tmProps,tmProps.btns,tmProps.key)
      );
    }
  }
}
function getButtons(tmProps){
  let btnArr=[]
  if(tmProps.btns.length>0){
    tmProps.btns.map((item)=>{
      let h= tmProps.dom.$createElement
     let btn=h('a-button',
        {
          props: {
            type: item.type?item.type:'default',
            size: 'default',//large/default/small
          },
          attrs:{
            style:'margin-right:8px'
          },
          on: {
            click: () =>{
              if(typeof item.event==='function'){
                item.event()
              }
              notification.close(tmProps.key)
            }
          },
        },
        item.name?item.name:'按钮')
      btnArr.push(btn)
    })
  }
  return btnArr
}

export function yq_notification(obj) {

  let tmProps = {
    //消息框自动关闭的延时时间，单位秒s
    duration: 5,//小于等于0:手动关闭;n>0秒，n秒后自动关闭，n默认值5,
    //消息框弹出位置
    position: 'bottomRight',//可取值：topCenter、bottomRight、bottomLeft、topRight、topLeft,
    //消息框宽度
    width: 384,
    //控制图标显隐
    showTypeIcon: true,//控制标题前的图标显隐，可取值：true、false,
    //消息类型
    typeName: 'info',//可取值：success/info/warning/error/alarm,
    //消息框标题
    title: '消息',
    //控制左上角关闭按钮显隐
    showClose: true,
    closeIconName: 'close',
    closeEvent: () => {
    },
    //控制更多按钮显隐
    showMore: 'block',//控制更多UI的显隐，可取值：block、none,
    //更多按钮左侧图标
    moreIconName: 'double-right',
    //更多按钮显示文本名称
    moreText: '更多',
    //更多按钮调用方法
    moreEvent: () => {
    },
    //消息内容是数组格式
    content: ['消息内容是数组格式'],
   /* content: [h('div',{},[
      h("a-icon",{props:{type:'smile'},attrs:{style:'color:green;font-size:20px'}}),
      h("div",{},'你好 啊！！！')
    ])],*/
    //自定义点击按钮
    //要求格式，譬如：[{type:'primary',name:'确认',event:function}]
    // ()=>{
    //   return [
    //     {
    //       type:'primary',
    //       name:'确认',
    //       event:()=>{}
    //     }
    //   ]}
    btns: () => {
      return []
    }
  }
 tmProps = Object.assign(tmProps, obj)
 tmProps.dom=this
  if(tmProps.width<384){
    tmProps.width=384
  }
  // if(!document.body.style.getPropertyValue('--notification-topCenter')){
  //   document.body.style.setProperty('--notification-topCenter', tmProps.width +'px')
  // }
  //let h = document.body.$createElement
  let h = tmProps.dom.$createElement
  tmProps.key= `open${Date.now()}`
  let option = {
    key:tmProps.key,
    /*getContainer:()=>{
     return  document.body.querySelector('.ant-layout-content')
    },*/
    placement: tmProps.position,
    message: tmProps.title,
    closeIcon: <a-icon type={tmProps.closeIconName} class={tmProps.showClose} />,
    onClose: () => {
      if (typeof tmProps.closeEvent === 'function') {
        tmProps.closeEvent()
      }
      notification.close(tmProps.key)
    },
    duration: tmProps.duration,
    description: h => {
      return h('div', null, [
        //更多
        h('div', { attrs: { style: `display:${tmProps.showMore};text-align:right` } }, [
          h('span', {
              class: ['ant-notification-more'],
              on: {
                click: e => {
                  tmProps.moreEvent();
                  notification.close(tmProps.key)
                }
              }
            },
            [
              h('a-icon', { props: { type: tmProps.moreIconName } }),
              h('span', `${tmProps.moreText}`)])
        ]),
        //内容
        // h('div', { attrs: { style: 'margin:0px 16px' } }, `${tmProps.content}`)
        // h('div', { attrs: { style: 'margin:0px 16px;max-height:calc(100vh * 0.7);overflow:auto', } },[h("p", { domProps: { innerHTML: tmProps.content } }, null)])
        h('div', { attrs: { style: 'margin:0px 0px;max-height:calc(100vh * 0.7);overflow:auto', } },[h("p", { domProps: { innerHTML: tmProps.content } }, null)])
      ])
    }
  }

  optionAddStyleAttrs(option,tmProps)
  optionAddIconAttrs(option,tmProps)
  optionAddBtnAttrs(option,tmProps)

  notification.open(option)
}