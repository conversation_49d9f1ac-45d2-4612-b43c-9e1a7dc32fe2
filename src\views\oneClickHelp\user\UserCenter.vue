<template>
  <div class="page-header-index-wide page-header-wrapper-grid-content-main">
    <card-frame :title="'用户信息'" v-if="!edit" showHeadBgImg>
      <div class="user-info-body" slot="bodySlot">
        <div class="account-center-avatarHolder">
          <a-avatar :size="104" icon="user" :src="userAvatar" />
        </div>
        <div class="account-center-detail">
          <p>
            <a-icon type="contacts" />
            <span>{{ userData.username }}</span>
          </p>
          <a-divider />
          <p>
            <a-icon type="idcard" />
            <span>{{ userData.realname }}</span>
          </p>
          <a-divider />
          <p>
            <a-icon type="phone" />
            <span>{{ userData.phone }}</span>
          </p>
          <a-divider />
          <p>
            <a-icon type="bank" />
            <span>{{ userData.departName }}</span>
          </p>
          <a-divider />
        </div>
        <span v-has="'oneClickHelp:user'" class="edit-btn" @click="editInfo">
          <a-icon type="edit" />
          <span style="margin-left: 5px">修&nbsp;改</span>
        </span>
      </div>
    </card-frame>

    <card-frame :title="'用户信息修改'" v-else showHeadBgImg>
      <div class="info-content" slot="bodySlot">
        <app-page :departList="departList" @cancelEdit="edit = false" @ok="editOK"></app-page>
      </div>
    </card-frame>
    <!-- <a-card :bordered="false" style="height: 100%" v-if="!edit">
      <div class="center-title">
        <h1>个人资料</h1>
        <span v-has="'oneClickHelp:user'"  class="edit-btn" @click="editInfo">
          <a-icon type="edit" theme="twoTone" />
        </span>
      </div>

    </a-card>
    <a-card v-else style="height: 100%" :bordered="false">
      <div class="center-title">
        <h1>个人信息编辑</h1>
      </div>

    </a-card> -->
  </div>
</template>

<script>
import AppPage from './App'
import { mapGetters } from 'vuex'
import { getFileAccessHttpUrl, getAction } from '@/api/manage'
import { queryDepartTreeList } from '@/api/api'
import cardFrame from '../localDeviceInfo/modules/CardFrame.vue'
export default {
  components: {
    AppPage,
    cardFrame,
  },
  data() {
    return {
      userData: {},
      uploadAction: window._CONFIG['domianURL'] + '/sys/common/upload',
      userAvatar: '',
      url: {
        getInfo: '/sys/user/getInfo', //个人信息
        updateUserInfo: '/sys/user/updateUserInfo', //编辑个人信息
        upload: window._CONFIG['domianURL'] + '/sys/common/upload', //图片上传
      },
      edit: false,
      departList: [],
      
    }
  },
  created() {
     this.getInfo()
    this.getDepartList()
  },
  mounted() {
    this.getAvatar()
  },

  methods: {
    ...mapGetters(['nickname', 'avatar', 'userInfo', 'departs']),
    getAvatar() {
      getAction('/sys/user/getUserAvatar').then((res) => {
        if (res.success) {
          this.$store.commit('SET_AVATAR', res.result)
          this.$store.commit('SET_CA', true)
          this.$nextTick(() => {
            this.userAvatar = ''
            this.userAvatar = getFileAccessHttpUrl(res.result)
          })
        }
      })
      // return getFileAccessHttpUrl(this.avatar());
    },
    getInfo() {
      getAction("/sys/user/getInfo").then((res) => {
          if(res.success){
            this.userData = res.result;
            let names = [];
            let depts = this.departs();
            if (depts && depts.length > 0) {
              depts.forEach((el) => {
                names.push(el.departName)
              })
            }
            this.userData.departName = names.join()
          }
      })

    },
    editInfo() {
      this.edit = true
    },
    editOK() {
      this.getInfo()
      this.getAvatar()
      this.edit = false
    },
    // 获取单位列表
    getDepartList() {
      // getAction('/sys/sysDepart/queryAllTree').then((res) => {
      // })
      queryDepartTreeList().then(res=>{
        if(res.success && res.result){
          this.departList = res.result && res.result.length > 0 ? res.result : []
        }
      })
    },
   
  },
}
</script>

<style lang="less" scoped>
.user-info-body {
  padding-top: 20px;
  position: relative;
  .edit-btn {
    // margin-left: 12px;
    cursor: pointer;
    position: absolute;
    right: 16px;
    top: 16px;
    color: #fff;
    font-size: 16px;
  }
}
.page-header-wrapper-grid-content-main {
  width: 100%;
  height: 100%;
  min-height: 100%;
  transition: 0.3s;

  .account-center-avatarHolder {
    text-align: center;
    margin-bottom: 48px;
  }

  .account-center-detail {
    // margin-top: 60px;
    width: 60%;
    max-width: 650px;
    margin: auto;
    p {
      color: #fff;
      font-size: 20px;
      margin-bottom: 8px;
      padding-left: 26px;
      position: relative;
      span {
        margin-left: 12px;
      }
    }
  }
}

.avatar-uploader > .ant-upload {
  width: 104px;
  height: 104px;
}

#uploadImg {
  font-size: 14px;
  overflow: hidden;
  // position: absolute;
  // margin-left: -35px;
}

::v-deep .ant-upload-list {
  display: none;
}
/deep/ .ant-divider {
  background: #144e90;
}

#file {
  position: absolute;
  z-index: 100;
  opacity: 0;
  filter: alpha(opacity=0);
  margin-left: 50;
}

.avatar-uploader > .ant-upload {
  width: 128px;
  height: 128px;
}
.center-title {
  display: flex;
  align-items: end;
  margin-bottom: 20px;
  h1 {
    margin-bottom: 0px;
    line-height: 1;
  }
}
.info-content {
  display: flex;
  justify-content: center;
  padding-top: 48px;
}
</style>