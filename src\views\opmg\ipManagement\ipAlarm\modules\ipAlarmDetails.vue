<template>
  <a-card style="height: 100%;">
    <a-row>
      <a-col :span="24">
        <span>
        </span>
        <span style="float: right;margin-bottom: 12px;"><img src="~@/assets/return1.png" alt="" @click="getGo"
            style="width: 20px;height: 20px;cursor: pointer"></span>
      </a-col>
      <a-col :span="24">
        <table class="gridtable">
          <tr>
            <td class="leftTd">审计任务</td>
            <td class="rightTd">{{ record.auditTaskName }}</td>
            <td class="leftTd">IP地址</td>
            <td class="rightTd">{{ record.ipAddress }}</td>
          </tr>
          <tr>
            <td class="leftTd">告警时间</td>
            <td class="rightTd">{{ record.alarmTime }}</td>
            <td class="leftTd">审计策略</td>
            <td class="rightTd">{{record.auditStrategyText}}</td>
          </tr>
          <tr>
            <td class="leftTd">处理状态</td>
            <td class="rightTd">{{ record.handleStatus == 1 ? '已完成' : record.handleStatus == 2 ? '已关闭' : '未处理' }}</td>
            <td class="leftTd">所属网段</td>
            <td class="rightTd">{{ record.segmentName }}</td>
          </tr>
          <tr>
            <td class="leftTd">部门</td>
            <td class="rightTd">{{ record.departName }}</td>
            <td class="leftTd">位置</td>
            <td class="rightTd">{{ record.location }}</td>
          </tr>
        </table>
      </a-col>
    </a-row>
  </a-card>
</template>

<script>
  import {
    httpAction,
    getAction
  } from '@/api/manage'
  import pick from 'lodash.pick'
  export default {
    name: 'ipAlarmDetails',
    props: {
      data: {
        type: Object
      }
    },
    data() {
      return {
        form: this.$form.createForm(this),
        record: {},
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          },
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          },
        },
        confirmLoading: false,
        url: {
          queryById: "/devops/ip/auditTaskAlarm/queryById"
        },
      }
    },
    mounted() {
      this.getRecord(this.data.id)
    },
    methods: {
      getRecord(id) {
        getAction(this.url.queryById, {
          id: id
        }).then((res) => {
          this.record = res.result
        })
      },
      //返回上一级
      getGo() {
        this.$parent.pButton2(0);
      }
    }
  }
</script>
<style scoped>
  table.gridtable {
    font-family: verdana, arial, sans-serif;
    font-size: 14px;
    color: #606266;
    border-width: 1px;
    border-color: #e8e8e8;
    border-collapse: collapse;
    text-align: left;
    width: 100%;
  }

  table.gridtable td {
    border-width: 1px;
    border-style: solid;
    border-color: #e8e8e8;
  }

  .leftTd {
    width: 17%;
    background-color: #FAFAFA;
    padding: 16px 24px;
    text-align: center;
  }

  .rightTd {
    width: 35%;
    padding: 16px 24px;
  }
</style>