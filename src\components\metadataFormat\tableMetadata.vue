<template>
  <a-form-item label="表头值" :labelCol="labelCol" :wrapperCol="wrapperCol">
      <a-row ref="tableCont" v-for="(item,index) in list" :key="index">
          <a-col :span="20">
            <a-row>
                <a-col :span="11">
                    <a-input placeholder="请输入表头标识" v-model="item.a"></a-input>
                </a-col>
                <a-col :span="2" style="text-align:center;">
                    →
                </a-col>
                <a-col :span="11">
                    <a-input placeholder="请输入表头名称" v-model="item.b"></a-input>
                </a-col>
            </a-row>
        </a-col>
        <a-col :span="4">
            <a-col :span="12" class="iconStyle" @click="addClick" ref="add">
                <a-icon type="plus-circle" />
            </a-col>
            <a-col :span="12" class="iconStyle" @click="delClick(index)" ref="emty">
                <a-icon type="minus-circle" style="color:red;"/>
            </a-col>
        </a-col>
      </a-row>
  </a-form-item>
</template>

<script>
  export default {
    name:'tableMetadata',
    data() {
      return {
        labelCol: {
          xs: { span: 5 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        },
        value: '',
        list:[
            {
                a:"1",b:"2"
            },
            {
                a:"3",b:"4"
            },
        ]
      }
    },
    methods: {
        addClick(){
            this.list.push({a: '', b: ''})
        },
        delClick(index){
            this.list.splice(index,1);
        }
    },
    mounted(){
        this.$refs['tableCont'][0].$el.lastChild.children[1].style.display="none";
    }
  }
</script>

<style scoped>
    .iconStyle{
        text-align:center;
        font-size: 20px;
        cursor: pointer;
    }
</style>
