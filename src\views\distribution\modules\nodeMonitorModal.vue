<template>
  <j-modal :title='title' :width='width' :visible='visible' :destroyOnClose='true'
           :okButtonProps="{ class:{'jee-hidden': disableSubmit} }" :centered='true' @ok='handleOk' @cancel='handleCancel'
           cancelText='关闭'>
    <a-spin :spinning='confirmLoading'>
      <j-form-container :disabled='disableSubmit'>
        <a-form-model ref='form' slot='detail' :model='model' :rules='validatorRules' :labelCol='labelCol'
                      :wrapperCol='wrapperCol'>
          <a-row>
            <a-col :span='24'>
              <a-form-model-item label='类型' prop='type'>
                <a-select v-model="model.type" :getPopupContainer='(node) => node.parentNode' @change="typeChange">
                  <a-select-option :value="'cluster'">集群</a-select-option>
                  <a-select-option :value="'node'">节点</a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span='24' v-if="addType == 'cluster'">
              <a-form-model-item label='集群名称' prop='clusterName'>
                <a-input style='width: 100%' v-model='model.clusterName' :allow-clear='true' autocomplete='off'
                         placeholder='请输入集群名称' />
              </a-form-model-item>
            </a-col>
            <a-col :span='24' v-if="addType == 'cluster'">
              <a-form-model-item label="集群标识" prop='minioAliasName'>
                <a-input v-model='model.minioAliasName' placeholder="请输入集群标识" :allowClear="true" autocomplete="off"
                         :disabled="canEditDevCode" />
              </a-form-model-item>
            </a-col>
            <a-col :span='24' v-if="addType == 'cluster'">
              <a-form-model-item label='集群IP' prop='clusterIp'>
                <a-input style='width: 100%' v-model='model.clusterIp' :allow-clear='true' autocomplete='off'
                         placeholder='请输入集群IP' />
              </a-form-model-item>
            </a-col>
            <a-col :span='24' v-if="addType == 'cluster'">
              <a-form-model-item label='集群端口号' prop='clusterPort'>
                <a-input style='width: 100%' v-model='model.clusterPort' :allow-clear='true' autocomplete='off'
                         placeholder='请输入集群端口号' />
              </a-form-model-item>
            </a-col>
            <a-col :span='24' v-if="addType == 'cluster'">
              <a-form-model-item label='用户名' prop='userName'>
                <a-input style='width: 100%' v-model='model.userName' :allow-clear='true' autocomplete='off'
                         placeholder='请输入用户名' />
              </a-form-model-item>
            </a-col>
            <a-col :span='24' v-if="addType == 'cluster'">
              <a-form-model-item label='密码' prop='password'>
                <a-input-password style='width: 100%' v-model='model.password' :allow-clear='true' autocomplete='off'
                                  placeholder='请输入密码' />
              </a-form-model-item>
            </a-col>
            <a-col :span='24' v-if="addType == 'node'">
              <a-form-model-item label='集群' prop='clusterId'>
                <a-select v-model="model.clusterId" :getPopupContainer='(node) => node.parentNode' placeholder='请选择集群'
                          allowClear>
                  <a-select-option v-for="item in nodeList" :value="item.id">{{ item.clusterName }}</a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span='24' v-if="addType == 'node'">
              <a-form-model-item label='节点名称' prop='nodeName'>
                <a-input style='width: 100%' v-model='model.nodeName' :allow-clear='true' autocomplete='off'
                         placeholder='请输入节点名称' />
              </a-form-model-item>
            </a-col>
            <a-col :span='24' v-if="addType == 'node'">
              <a-form-model-item label='节点IP' prop='nodeIp'>
                <a-input style='width: 100%' v-model='model.nodeIp' :allow-clear='true' autocomplete='off'
                         placeholder='请输入节点IP' />
              </a-form-model-item>
            </a-col>
            <a-col :span='24' v-if="addType == 'node'">
              <a-form-model-item label='节点端口号' prop='nodePort'>
                <a-input style='width: 100%' v-model='model.nodePort' :allow-clear='true' autocomplete='off'
                         placeholder='请输入节点端口号' />
              </a-form-model-item>
            </a-col>
            <a-col :span='24' v-if="addType == 'node'">
              <a-form-model-item label='机柜号' prop='cabinetNumber'>
                <a-input style='width: 100%' v-model='model.cabinetNumber' :allow-clear='true' autocomplete='off'
                         placeholder='请输入机柜号' />
              </a-form-model-item>
            </a-col>
            <a-col :span='24' v-if="addType == 'node'">
              <a-form-model-item label='槽位号' prop='slotNumber'>
                <a-input style='width: 100%' v-model='model.slotNumber' :allow-clear='true' autocomplete='off'
                         placeholder='请输入槽位号' />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </j-form-container>
    </a-spin>
  </j-modal>
</template>

<script>
import {
  httpAction,
  getAction,
} from '@api/manage'

export default {
  name: 'nodeMonitorModal',
  data() {
    return {
      addType: 'cluster',
      title: '',
      width: '800px',
      visible: false,
      disableSubmit: false,
      confirmLoading: false,
      labelCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 5
        }
      },
      wrapperCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 16
        }
      },
      wrapperCol1: {
        xs: {
          span: 6
        },
        sm: {
          span: 4
        }
      },
      model: {
        type: 'cluster',
        clusterName: '',
        clusterIp: '',
        clusterPort: '',
        clusterId: null,
        minioAliasName: '',
        userName: "",
        password: "",
        nodeName: '',
        nodeIp: '',
        nodePort: '',
        slotNumber: '',
        cabinetNumber: '',
      },
      nodeList: [],
      validatorRules: {
        clusterName: [{
          required: true,
          message: '请输入集群名称！'
        },
          {
            min: 1,
            max: 50,
            message: '集群名称长度应在1-50之间！'
          }
        ],
        userName: [{
          required: true,
          message: '请输入用户名！'
        },
          {
            min: 1,
            max: 30,
            message: '用户名长度应在1-30之间！'
          }
        ],
        password: [{
          required: true,
          message: '请输入密码！'
        }],
        minioAliasName: [{
          required: true,
          message: '请输入集群标识'
        },
          {
            min: 4,
            max: 34,
            message: '集群标识长度应在4-34之间！'
          },
          {
            pattern: /^([a-zA-Z])(([a-zA-Z]+|[0-9]+|[a-zA-Z0-9-]){3,33})$/,
            message: '可包含字母、数字、英文横线,以英文字母开头,4-34个字符'
          }
        ],
        nodeName: [{
          required: true,
          message: '请输入节点名称'
        },
          {
            min: 1,
            max: 50,
            message: '节点名称长度应在1-50之间！'
          }
        ],
        clusterIp: [{
          required: true,
          message: '请输入集群IP！'
        },
          {
            pattern: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
            message: '请输入正确的IP地址!'
          }
        ],
        nodeIp: [{
          required: true,
          message: '请输入节点IP！'
        },
          {
            pattern: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
            message: '请输入正确的IP地址!'
          }
        ],
        slotNumber: [{
          required: false
        },
          {
            min: 1,
            max: 6,
            message: '槽位号需要在1-999999之间！'
          },
          {
            pattern: /\d+$/,
            message: '请输入正确的槽位号!'
          }
        ],
        cabinetNumber: [{
          required: false
        },
          {
            min: 1,
            max: 6,
            message: '机柜号需要在1-999999之间！'
          },
          {
            pattern: /\d+$/,
            message: '请输入正确的机柜号!'
          }
        ],
        clusterPort: [{
          required: true,
          message: '请输入集群端口号！'
        }, {
          pattern: /^([1-9]{1}|[1-9]\d{1}|[1-9]\d{2}|[1-9]\d{3}|[1-5][0-9]{4}|6[0-5][0-5][0-3][0-5])$/,
          message: '请输入正确端口号！'
        }],
        nodePort: [{
          required: true,
          message: '请输入节点端口号！'
        }, {
          pattern: /^([1-9]{1}|[1-9]\d{1}|[1-9]\d{2}|[1-9]\d{3}|[1-5][0-9]{4}|6[0-5][0-5][0-3][0-5])$/,
          message: '请输入正确端口号！'
        }],
        clusterId: [{
          required: true,
          message: '请选择集群！'
        }]
      },
      canEditDevCode: false,
      url: {
        info: '/distributedStorage/node',
        cluster: '/distributedStorage/cluster',
        nodeEdit: '/distributedStorage/node'
      }
    }
  },
  created() {

  },
  methods: {
    getNode() {
      getAction(this.url.cluster).then((res) => {
        this.nodeList = res.result
        //回显集群用户名密码
        if(this.model.clusterId && this.addType == 'cluster'){
          let c = this.nodeList.find(el=>el.id === this.model.clusterId)
          if(c){
            this.model.userName = c.userName
            this.model.password = c.password
          }
        }
      })
    },
    typeChange(e) {
      this.addType = e
      if(this.model.clusterId && this.addType == 'cluster'){
        let c = this.nodeList.find(el=>el.id === this.model.clusterId)
        if(c){
          this.model.userName = c.userName
          this.model.password = c.password
          this.model.minioAliasName = c.minioAliasName
        }
      }
      this.$refs.form.clearValidate()
    },
    add() {
      this.edit(this.model,false)
    },
    edit(record,edit=true) {
      this.addType = record.type
      this.visible = true
      this.canEditDevCode = edit
      this.$nextTick(() => {
        this.model = Object.assign(this.model, record)
        this.$forceUpdate()
        this.getNode()
      })
    },
    close() {
      this.model = {
        type: 'cluster',
        clusterName: '',
        clusterIp: '',
        clusterPort: '',
        clusterId: null,
        minioAliasName: '',
        userName: "",
        password: "",
        nodeName: '',
        nodeIp: '',
        nodePort: '',
        slotNumber: '',
        cabinetNumber: '',
      }
      this.addType = 'cluster'
      this.loading = false
      this.visible = false
    },
    handleOk() {
      const that = this
      that.$refs.form.validate((err, value) => {
        if (err) {
          that.confirmLoading = true
          let  httpurl = that.url.cluster
          if (this.addType == 'cluster') {
            let method = this.canEditDevCode?'put':'post'
            let formData = {
              clusterName: '',
              clusterIp: '',
              clusterPort: '',
              minioAliasName: '',
              userName: "",
              password: "",
              id: '',
            }
            formData.id = that.model.clusterId != null ? that.model.clusterId : null
            formData.clusterPort = that.model.clusterPort
            formData.clusterIp = that.model.clusterIp
            formData.clusterName = that.model.clusterName
            formData.minioAliasName = that.model.minioAliasName
            formData.userName = that.model.userName
            formData.password = that.model.password
            httpAction(httpurl, formData, method)
              .then((res) => {
                if (res.success) {
                  that.$message.success(res.message)
                  that.$emit('ok')
                  that.close()
                } else {
                  that.$message.warning(res.message)
                }
                that.confirmLoading = false
              }).catch((res) => {
              that.$message.warning(res.message)
              that.confirmLoading = false
            })
          } else {
            let method = this.model.id?'put':'post'
            let formData = {
              ...that.model
            }
            httpAction(that.url.info, formData, method)
              .then((res) => {
                if (res.success) {
                  that.$message.success(res.message)
                  that.$emit('ok')
                  that.close()
                } else {
                  that.$message.warning(res.message)
                }
                that.confirmLoading = false
              }).catch((res) => {
              that.$message.warning(res.message)
              that.confirmLoading = false
            })
          }
        }
      })
    },
    submitCallback() {
      this.$emit('ok')
      this.visible = false
    },
    handleCancel() {
      this.close()
    }
  }
}
</script>
<style scoped lang='less'>
@import '~@assets/less/normalModal.less';
</style>