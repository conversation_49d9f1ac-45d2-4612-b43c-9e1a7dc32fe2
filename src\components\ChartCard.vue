<template>
  <a-card :loading="loading" :body-style="{ padding: '20px 24px 8px' }" :bordered="false">
    <div class="chart-card-header">
      <!--头部属性名称，属性值-->
      <div class="meta">
        <span>
          <div class="chart-card-title">
            <a-tooltip>
              <template slot="title">
               {{code}}
              </template>
              {{ title }}
            </a-tooltip>
          </div>
        </span>
        <span class="chart-card-action">
          <slot name="action"></slot>
        </span>
      </div>
      <div class="total">
        <span>{{ total }}</span><span style="font-size: 18px"> {{ unit }}</span>
      </div>
    </div>

    <!--展示卡片（1）-->
    <div class="chart-card-content" v-if="flag != ''">
      <!--折线图-->
      <div class="content-fix" id="contArea" v-if="flag === '1'">
        <mini-area :padding='[36, 0, 0, 0]' :isBottom='true' :height='70' :dataSource="miniAreaData" />
      </div>
      <!--饼图-->
      <div class="content-fix content-pie" v-else-if="flag === '2'">
        <mini-pie :plotHeight='100' :dataSource="miniPieData" />
      </div>
      <!--仪表盘-->
      <div class="content-fix1" v-else-if="flag === '3'">
        <DashChartDemo v-if="dashChart != ''" :height="100" :dataSource="dashChart" />
      </div>
      <!--文本框-->
      <div class="content-fix content-text" v-else-if="flag === '4'">
        <div style='margin-top: 8px' :title='"标识：" + textChart.name'>标识： {{ textChart.name }}</div>
        <div :title='"值：" + textChart.value'>值： {{ textChart.value }}</div>
        <div :title='"信息更新时间：" + textChart.timestamp'>信息更新时间： {{ textChart.timestamp }}</div>
      </div>
    </div>

    <!--展示卡片（2）-->
    <div class="chart-card-content" v-else>
      <div class="content-fix">
        <slot></slot>
      </div>
    </div>

    <div class="chart-card-footer">
      <div class="field">
        <slot name="footer"></slot>
      </div>
    </div>
  </a-card>
</template>

<script>
import MiniArea from '@/components/chart/MiniArea'
import MiniPie from '@/components/chart/MiniPie'
import DashChartDemo from '@/components/chart/DashChartDemo'

export default {
  name: 'ChartCard',
  components: {
    MiniArea,
    MiniPie,
    DashChartDemo,
  },
  props: {
    title: {
      type: String,
      default: '',
    },
    code: {
      type: String,
      default: '',
    },
    total: {
      type: String,
      default: '',
    },
    unit: {
      type: String,
      default: '',
    },
    loading: {
      type: Boolean,
      default: false,
    },
    chartData: {
      type: Object,
      default: () => { },
    },
    flagType: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      flag: '',
      miniAreaData: [
        /*{ x:"2021-08-09 8:00:08" , y:41.01 },
        { x:"2021-08-09 8:00:20" , y:85.01 },
        { x:"2021-08-09 8:35:08" , y:65.01 },
        { x:"2021-08-09 8:59:08" , y:52.01 },
        { x:"2021-08-09 10:00:08"  ,y:85.01 },
        { x:"2021-08-09 12:00:08" , y:65.01 },
        { x:"2021-05-13 05:04:05" , y:41.01 },
        { x:"2021-05-13 05:04:26" , y:55.01 }*/
      ],
      miniPieData: [
        /* {
           item:'aaaa',
           count:25,
         },
         {
           item:'vvvv',
           count:100-25,
         }*/
      ],
      dashChart: [
        /* { value:6 },
          { value:7 }*/
      ],
      textChart: {
        /*          name:'test',
        value:110*/
      },
    }
  },
  watch: {
    chartData(val) {
      this.getChartData(val, this.flagType)
    },
  },
  mounted() {
    this.getChartData(this.chartData, this.flagType)
  },
  methods: {
    getChartData(chartData, flagType) {
      if (flagType == 'pie') {
        this.flag = '2'
        this.miniPieData = [];
        let firstCount = parseFloat(this.chartData.value[this.chartData.value.length - 1].value.toFixed(2))
        this.miniPieData.push({
          item: this.chartData.name,
          count: firstCount,
        })
        this.miniPieData.push({
          item: "剩余",
          count: parseFloat((100 - firstCount).toFixed(2))
        });
      } else if (this.flagType == 'gauge') {
        this.flag = '3';
        this.dashChart = [];
        // this.chartData.value[this.chartData.value.length - 1].value=5
        this.dashChart.push({
          'value': parseFloat(this.chartData.value[this.chartData.value.length - 1].value)
        });
      } else if (this.flagType == 'line') {
        this.flag = '1';
        this.miniAreaData = [];
        this.chartData.value.forEach(ele => {
          this.miniAreaData.push({
            x: ele.timestamp,
            y: ele.value
          });
        })
      } else if (this.flagType == 'text') {
        this.flag = '4';
        this.textChart.name = this.chartData.name;
        this.textChart.value = this.chartData.value[this.chartData.value.length - 1].value + this.chartData.unit + '';
        this.textChart.timestamp = this.chartData.value[this.chartData.value.length - 1].timestamp;
      }
    },
  },
}
</script>

<style lang='less' scoped>
.chart-card-header {
  position: relative;
  overflow: hidden;
  width: 100%;
  z-index: 10;
  .meta {
    position: relative;
    overflow: hidden;
    width: 100%;
    color: rgba(0, 0, 0, 0.45);
    font-size: 14px;
    line-height: 22px;
  }
}

.chart-card-action {
  cursor: pointer;
  position: absolute;
  top: 0;
  right: 0;
  z-index: 100;
}

.chart-card-footer {
  border-top: 1px solid #e8e8e8;
  padding-top: 9px;
  margin-top: 8px;

  >* {
    position: relative;
  }

  .field {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin: 0;
  }
}

.chart-card-content {
  margin-bottom: 12px;
  position: relative;
  height: 70px;
  width: 100%;

  .content-fix {
    position: absolute;
    left: 0;
    top: 0px;
    bottom: 0;
    width: 100%;
    height: 100%;
  }

  .content-pie {
    position: absolute;
    left: 0;
    top: -30px;
    bottom: 0;
    width: 100%;
    height: 100px;
  }

  .content-text {
    display: flex;
    flex-flow: column nowrap;
    justify-content: end;
    align-items: start;

    div {
      width: 100%;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  .content-fix1 {
    position: absolute;
    left: 0;
    bottom: 0;
    top: -85px;
    width: 100%;
    height: 176px;
    overflow: hidden;
  }

  #contArea {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: auto;

    .antv-chart-mini {
      height: 100%;
    }

    .chart-wrapper {
      top: 0px;
      bottom: 0px !important;
    }
  }
}

.total {
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  white-space: nowrap;
  color: #000;
  margin-top: 4px;
  margin-bottom: 0;
  font-size: 30px;
  line-height: 38px;
  height: 38px;
}

/deep/ .chart-card-title {
  width: 150px;
  height: 22px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>