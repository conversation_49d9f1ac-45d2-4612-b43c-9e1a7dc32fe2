<template>
  <j-modal
    :title="title"
    :width="1200"
    :visible='visible'
    :maskClosable='false'
    :centered='true'
    :destroyOnClose='true'
    :confirmLoading="confirmLoading"
    switch-fullscreen
    @cancel="handleCancel"
    cancelText="关闭"
  >
    <template slot="footer">
      <a-button type='primary' @click="addKnowledge" v-if='itilProcessItem.length>0'>加入知识库</a-button>
      <a-button type='primary' v-if="showWithdraw" @click="withdraw">撤回</a-button>
      <a-button @click="handleCancel">关闭</a-button>
    </template>
    <a-tabs class='tab-border'  default-active-key="comments" @change="callback">
      <a-tab-pane  key="comments" tab="过程意见">
        <a-table
          :bordered='true'
          class='tab-pane-box-padding'
          :columns='columns'
          :data-source='data'
          :row-key='(record,index)=>{return record.id}'
          :scroll='scro(data)'
          size='small'
          :pagination='ipagination'
          @change='handleTableChange'
        >
        </a-table>
      </a-tab-pane>
      <a-tab-pane key="processIntanceForm" tab="流程表单" >
        <div class="form-wrapper">
          <div v-if="showExportButton" class="custom-button-container">
            <!-- 添加的按钮 -->
            <a-button type='primary' key='custom-button' @click='handleCustomButton(processInstanceId)'>导出wps</a-button>
          </div>
          <div class="tab-pane-box-padding">
            <k-form-build
              v-if="generateStartFormVisible"
              :value="startFormJson"
              :disabled='true'
              :dynamicData='dynamicData'
              :defaultValue='variables'
              ref="KFB"
              @assetsHandler="assetsHandler"
            />
          </div>
        </div>
        <!-- <fm-generate-form :data="startFormJson" :value="variables" ref="formMaking"> </fm-generate-form> -->
      </a-tab-pane>
      <a-tab-pane key="processIntanceImage" tab="实时流程图">
       <div class="tab-pane-box-padding">
         <process-picture-real
         v-if="showPicture"
         :row="processInstanceId"
         ></process-picture-real>
          <!-- <img style="max-width: 1200px; max-height: 600px" :src="imagePath" alt="流程图" /> -->
       </div>
      </a-tab-pane>
      <!-- 深圳暂时用不到关联子流程、关联父流程,暂时隐藏 -->
      <a-tab-pane v-if="sysType !== '3'" key="processAssociation"  tab="关联子流程">
        <myProcess :processInstanceId="processInstanceId" showType="1"/>
      </a-tab-pane>
      <a-tab-pane v-if="sysType !== '3'" key="processAssociation2"  tab="关联父流程">
        <myProcess :processInstanceId="processInstanceId" showType="2"/>
      </a-tab-pane>
    </a-tabs>
    <add-knowledge-modal ref='addKnowledge' :add-from-flowable='true'></add-knowledge-modal>
    <assets-update-list ref="assetsUpdateModal"></assets-update-list>
  </j-modal>
</template>
<script>
import { getAction, postAction1 } from '@/api/manage'
import ProcessPictureReal from './ProcessPictureReal'
import myProcess from './processAssociation'
import addKnowledgeModal from '@views/opmg/knowledgeManagement/knowledgeBase/modules/AddKnowledgeModal.vue'
import AssetsUpdateList from '@/views/cmdb/assets/assetsUpdate/AssetsUpdateList'
import { ajaxGetDictItems, getDictItemsFromCache, queryConfigureDictItem } from '@/api/api'
const columns = [
  {
    title: '任务节点',
    dataIndex: 'taskName',
    key: 'taskName',
    customCell: () => {
      let cellStyle = 'text-align:center'
      return { style: cellStyle }
    }
  },
  {
    title: '操作类型',
    dataIndex: 'typeDesc',
    key: 'typeDesc',
    customCell: () => {
      let cellStyle = 'text-align:center'
      return { style: cellStyle }
    }
  },
  {
    title: '操作人',
    dataIndex: 'userName',
    key: 'userName',
    customCell: () => {
      let cellStyle = 'text-align:center'
      return { style: cellStyle }
    }
  },
  {
    title: '操作时间',
    key: 'time',
    dataIndex: 'time',
    customCell: () => {
      let cellStyle = 'text-align:center;width:180px'
      return { style: cellStyle }
    }
  },
  {
    title: '意见',
    key: 'fullMessage',
    dataIndex: 'fullMessage',
    customCell: () => {
      let cellStyle = 'text-align:center'
      return { style: cellStyle }
    }
  }
]
export default {
  name: 'ProcessInstanceInfoModal',
  components:{
    ProcessPictureReal,myProcess,
    addKnowledgeModal,
    AssetsUpdateList,
  },
  props: {
    showWithdraw:false,
    taskId:""
  },
  data() {
    return {
      title: '操作',
      visible: false,
      confirmLoading: false,
      /* 分页参数 */
      ipagination: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '30'],
        showTotal: (total, range) => {
          return range[0] + '-' + range[1] + ' 共' + total + '条'
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0,
      },
      dynamicData: {
        bpmnDepartTreeData: []
      },
      /* 排序参数 */
      isorter:{
        column: 'createTime',
        order: 'desc',
      },
      data: [],
      columns,
      url: {
        getInstanceInfoUrl: 'flowable/processInstance/comments',
        imgUrl: window._CONFIG['domianURL'] + '/flowable/diagramView',
        queryProcessDefinitionKey:'/kbase/relation/check',//获取流程定义key
      },
      params: {},
      imagePath: '',
      startFormJson: {},
      variables: undefined,
      showBusinessKey: false,
      businessKey: undefined,
      generateStartFormVisible: false,
      paneH:0,
      tabKey:"comments",
      processInstanceId:null,
      disableMixinCreated:true,
      showPicture:false,
      showExportButton: false,
      itilProcessKey:[],//记录需要加入知识库的表单类型（问题、事件、工单）
      itilProcessItem:[],
      sysType: window._CONFIG['system_Type'],
    }
  },
  created() {
    this.getTreeData()
    this.getItilProcessKey('itilProcessKey')
  },
  destroyed() {
    console.log('销毁查看详情模态框')
  },
  methods: {
    withdraw(){
      var this_ = this
      this.$confirm({
        title: '确认撤回',
        okText: '是',
        cancelText: '否',
        content: '是否撤回该流程?',
        onOk: function() {
          getAction("/flowable/task/withdraw", {
            processInstanceId: this_.processInstanceId
          }).then((res) => {
            if (res.success) {
              this_.$message.success(res.message)
              this_.getInfo()
              this_.$emit("ok")
            } else {
              this_.$message.warning(res.message)
            }
          })

        }
      })
    },
    //打开资产编辑
    assetsHandler(e){
      if (this.variables && this.variables.tempProcessInstanceId) {
        this.$refs.assetsUpdateModal.tempId = this.variables.tempProcessInstanceId
      } else {
        this.$refs.assetsUpdateModal.tempId = ''
      }
      this.$refs.assetsUpdateModal.init(e)
    },
    scro(data){
      return data.length>0?{x:true}:{};
    },
    //表格页切换（后端发送所有数据，前端进行换页）
    handleTableChange(pagination, filters, sorter) {
      //分页、排序、筛选变化时触发
      //TODO 筛选
      if (Object.keys(sorter).length > 0) {
        this.isorter.column = sorter.field;
        this.isorter.order = "ascend" == sorter.order ? "asc" : "desc"
      }
      this.ipagination = pagination;
    },
    init(processInstanceId, isAddKnowledge = false, record=null) {
      this.processInstanceId = processInstanceId;
      this.isProcessFinished = record && record.endTime ? true : false
      this.visible = true
      this.$nextTick(() => {
        //this.paneH = this.$refs.tabPaneBox.getBoundingClientRect().height
      })
      this.getInfo()
      if (record && record.status == 6) {
        this.showProcessInstanceForm(record.id)
      } else {
        this.showProcessInstanceForm()
      }

      this.itilProcessItem = []
      if (isAddKnowledge) {
        this.getProcessDefinitionKey(processInstanceId)
      }
    },
    getInfo() {
      getAction(this.url.getInstanceInfoUrl, { processInstanceId: this.processInstanceId })
        .then((res) => {
          this.data = res.result
        })
        .finally(() => {})
    },
    callback(key) {
      this.tabKey = key;
      if (key == 'processIntanceForm') {
        if (this.generateStartFormVisible) {
          // this.$refs.KFB.disable(["input_1653528447588","input_1653528460488","number_1653528462109"])
        }
      } else if (key == 'processIntanceImage') {
        this.showPicture = true;
      }
    },
    getTreeData() {
      getAction('/sys/sysDepart/queryTreeList').then((res) => {
        this.dynamicData.bpmnDepartTreeData = res.result
      })
    },
    showProcessInstanceForm(id) {
      getAction('/flowable/processInstance/formData', {
        processInstanceId: this.processInstanceId,
        type: !!id ? 'read' : ''
      }).then((res) => {
        if (res.code != 200) {
          this.$message.error(res.message)
        } else {
          const data = res.result
          this.showBusinessKey = data.showBusinessKey
          this.businessKey = data.businessKey
          if (data && data.renderedStartForm) {
            this.startFormJson = JSON.parse(data.renderedStartForm)
            this.variables = data.variables
            this.generateStartFormVisible = true
          }
          const wordTemplatePath = data.variables.wordTemplatePath;
          if (wordTemplatePath) {
            this.showExportButton = true;
          } else {
            this.showExportButton = false;
          }
        }
      })
    },
    showImage() {
      this.imagePath = this.url.imgUrl + '?processInstanceId=' + this.processInstanceId + '&time=' + new Date()
      //console.log(this.imagePath)
    },
    close() {
      this.generateStartFormVisible = false
      this.$emit('close')
      this.visible = false
    },
    handleCancel() {
      this.imagePath = null
      this.data = []
      this.close()
    },
    //wps
    handleCustomButton: function(processInstanceId) {
      //console.log('id ==== ', processInstanceId)
      // 处理导出 wps 按钮点击事件的逻辑
      if (!processInstanceId) {
        this.$message.error('流程实例ID不存在')
        return
      }
      const wpsMapping = {
        '/templates/事件登记表.docx': '事件登记表',
        '/templates/变更申请单.docx': '变更申请单',
        '/templates/问题记录单.docx': '问题记录单',
        '/templates/事件登记表.wps': '问题记录单',
        '/templates/变更申请单.wps': '问题记录单',
        '/templates/问题记录单.wps': '问题记录单',
      };
      let wpsName = '未知文档';

      if (this.variables && this.variables.wordTemplatePath) {
        const wordTemplatePath = this.variables.wordTemplatePath;
        if (wpsMapping.hasOwnProperty(wordTemplatePath)) {
          wpsName = wpsMapping[wordTemplatePath];
        }
      }
      postAction1('/wpsTemplate/generate/wps', processInstanceId, {
        responseType: 'blob',
        headers: {
          'Content-Type': 'application/json; charset=utf-8'
        }
      }).then(response => {
        //console.log(response)
        //console.log('response.data=' + response.data)
        // 将响应数据转换为Blob对象
        const blob = new Blob([response], { type: 'application/force-download' })
        // 创建一个链接并模拟点击以下载文档
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.setAttribute('download', wpsName+'.wps')
        document.body.appendChild(link)
        link.click()

        // 释放资源
        window.URL.revokeObjectURL(url)
        this.confirmLoading = false
      }).catch(error => {
        console.error(error)
        this.confirmLoading = false
      })
    },
    /*获取加入知识库的表单类型（问题、事件、工单）*/
    getItilProcessKey(dictCode){
      getAction('/kbase/relation/getDict').then((res)=>{
        if (res.success){
          this.itilProcessKey=res.result
        }
      })
    },
    /*根据流程实例id，获取流程定义key*/
    getProcessDefinitionKey(processInstanceId){
      getAction(this.url.queryProcessDefinitionKey,{processInstanceId: processInstanceId}).then((res)=>{
        if (res.success){
          this.knowledgeBtnActiveStatus(res.result)
        }
      })
    },
    /*是否激活加入知识库按钮*/
    knowledgeBtnActiveStatus(processInfo){
      this.itilProcessItem=[]
      if (this.itilProcessKey.length){
      this.itilProcessItem= this.itilProcessKey.filter((item,index)=>{
          let obj=JSON.parse(item.value)
          if(obj.processDefinitionKey===processInfo.processDefinitionKey){
            item.processDefinitionKey=processInfo.processDefinitionKey
            item.processDefinitionName=processInfo.processDefinitionName
            return item
          }
        })
      }
    },
    /*加入知识库*/
    addKnowledge(){
      let keys=Object.keys(this.variables)
      if(keys.length>0){
        let attKeys=keys.filter((item)=>{
          //根据custom_attachments_前缀找到附件属性
          if(item.includes("custom_attachments_")){
            return item
          }
        })
        let urlArr=[]
        for (let i=0;i<attKeys.length;i++){
          let attArr=this.variables[attKeys[i]]
          for (let k=0;k<attArr.length;k++){
            urlArr.push(attArr[k].url)
          }
        }

        this.$refs.addKnowledge.addKnowledgeFromProcess({
          title:this.variables.title,
          plan:this.variables.plan,
          files:urlArr.length>0? urlArr.join(','):'',
          processInstanceId: this.processInstanceId,
          processDefinitionKey:this.itilProcessItem[0].processDefinitionKey,
          processDefinitionName:this.itilProcessItem[0].processDefinitionName,
        })
        this.$refs.addKnowledge.title = '新增';
        this.$refs.addKnowledge.disableSubmit = false;
      }else {
        this.$message.warning("暂无数据")
      }
    }
  },
}
</script>
<style lang="less" scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/YQCommon.less';
@import '~@assets/less/YQNormalModal.less';
.tab-border{
  border: 1px solid #e8e8e8
}
.tab-pane-box-padding{
  padding: 0px 5px !important;
}
/*过程意见，table样式*/
::v-deep .ant-table-tbody > tr > td ,::v-deep .ant-table-thead > tr > th{
  word-break: break-word !important;
  white-space: normal !important;
  overflow: hidden !important;
}
::v-deep .ant-table-scroll{
  overflow: hidden !important;
  overflow-x: auto !important;
   .ant-table-body {
    margin: 0px !important;
    overflow: hidden !important;
    overflow-x: auto !important;
  }
}
//在全屏模式下，实时流程图容器containers的高度样式如下；至于非全屏模式下，containers高度样式在流程图组件中设定
.j-modal-box.fullscreen{
  ::v-deep .containers {
    height: calc(100vh - 56px - 24px - 1px - 60px - 32px - 12px - 1px - 24px - 56px) !important;
  }
}
.form-wrapper {
  height: calc(100vh - 55px - 53px - 30px - 30px - 48px - 45px - 50px);
  overflow:auto;
  padding-bottom:20px;
}
/deep/ .ant-modal {
  padding-bottom: 0;
}
</style>
