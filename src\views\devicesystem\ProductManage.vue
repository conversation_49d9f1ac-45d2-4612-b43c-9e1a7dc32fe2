<template>
  <div style="height:100%;">
    <keep-alive>
      <component :is="pageName" :obj="obj"/>
    </keep-alive >
  </div>
</template>
<script>
  import ProductList from './ProductList'
  import ProductEdit from './modules/ProductEdit'
  export default {
    name: "ProductManage",
    data() {
      return {
        isActive: 0,
        obj:{},
      };
    },
    
    components: {
      ProductList,
      ProductEdit
    },
    created(){
      this.pButton1(0);
    },
    //使用计算属性
    computed: {
      pageName() {
        switch (this.isActive) {
          case 0:
            return "ProductList";
            break;

          default:
            return "ProductEdit";
            break;
        }
      }
    },
    methods: {
      pButton1(index,item) {
        this.isActive = index;
        this.obj = item;
      },
      pButton2(index,item) {
        this.isActive = index;
        this.data = item;
        //this.reload();
      }
    }
  }
</script>