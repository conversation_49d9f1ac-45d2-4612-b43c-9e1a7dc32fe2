<template>
  <a-card :loading="loadingToRead" style="width: 100%; height: 100%">
    <div slot="title">
      <a-icon type="read" />
      我的待阅 【<a slot="extra" @click="goPage">{{ dataSourceCount2 }}</a>】
      <a-tooltip title="刷新">
        <a-icon type="sync" style="color: #ccc; cursor: pointer; font-size: 14px" @click="loadToRead" />
      </a-tooltip>
    </div>
    <a slot="extra" @click="goPage">更多
      <a-icon type="double-right" />
    </a>
    <a-table :body-style="{ overflowX: 'auto' }"
             size="middle"
             rowKey="id"
             bordered
             :columns="columnstoread"
             :dataSource="dataSource2"
             :pagination="false">
              <span slot="action" slot-scope="text, record">
                <a @click="btnViewToRead(record)">查看</a>
              </span>
    </a-table>
    <process-instance-info-modal ref="processInstanceInfoModalForm"></process-instance-info-modal>
  </a-card>
</template>
<script>
import { getAction } from '@api/manage'
import ProcessInstanceInfoModal from '@views/flowable/process-instance/modules/ProcessInstanceInfoModal.vue'

export default {
  name: 'MyPendingReview',
  components: { ProcessInstanceInfoModal },
  data(){
    return{
      loadingToRead: false,
      dataSourceCount2: 0,
      dataSource2: [],
      url: {
        toReadList: '/flowable/processInstance/listCcToMe',
        myProcessList: '/flowable/processInstance/listStartedByMe',
        businessList: '/business/actZBusiness/list',
        withdrowUrl: '/flowable/processInstance/delete',
        applyUrl: '/business/actZBusiness/sbApply',
        deleteBusiness: '/business/actZBusiness/delete',
      },
      columnstoread: [
        {
          title: '流程实例名称',
          width: '31%',
          dataIndex: 'name',
          align: 'center',
        },
        {
          title: '发起人',
          width: '15%',
          dataIndex: 'startUserName',
          align: 'center',
        },
        {
          title: '开始时间',
          align: 'center',
          width: '25%',
          dataIndex: 'startTime',
        },
        {
          title: '操作',
          width: '25%',
          dataIndex: 'action',
          scopedSlots: {
            customRender: 'action'
          },
          align: 'center',
        },
      ],
    }
  },
  created() {
    this.loadToRead()
  },
  mounted() {
  },
  methods:{
    loadToRead() {
      if (!this.url.toReadList) {
        this.$message.error('请设置url.toReadList!')
        return
      }
      this.loadingToRead = true
      getAction(this.url.toReadList, {
        pageNo: 1,
        pageSize: 6
      }).then((res) => {
        if (res.success) {
          this.dataSource2 = res.result.records || res.result
          this.dataSourceCount2 = res.result.total
        } else {
          this.$message.warning(res.message)
        }
        this.loadingToRead = false
      })
    },
    btnViewToRead(record) {
      this.$refs.processInstanceInfoModalForm.init(record.id)
      this.$refs.processInstanceInfoModalForm.title = '流程实例信息'
      this.$refs.processInstanceInfoModalForm.disableSubmit = false
      if (record.status===6){
        getAction("/flowable/processInstance/updateStatue",{id:record.id}).then((res)=>{
          if(!res.success){
            this.$message.error(res.message)
          }else {
            this.loadToRead()
          }
        })
      }
    },
    goPage(arg) {
      this.$router.push({
        path: '/flowable/taskRead'
      })
    },
  }
}
</script>

<style scoped lang='less'>
/deep/ .ant-card-body{
  height: calc(100% - 56px);
  overflow-y: auto;
}
</style>