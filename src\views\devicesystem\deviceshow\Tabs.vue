<template>
  <div class="tabs-container">
    <a-tabs :animated="false" class="device-tabs" :active-key="defaultActiveKey" default-active-key="1"
            @change="callback">
      <a-tab-pane key="1" tab="设备信息" v-if='curRenderStates.showBaseInfo || curRenderStates.showStateInfo || curRenderStates.showDataAnalysis'>
        <div class="info-function">
          <!-- 基础信息 -->
          <base-Info :editabled="isEditing" ref="baseInfo" @changeDeviceBaseInfo='changeDeviceBaseInfo' v-if="curRenderStates.showBaseInfo"></base-Info>
          <!-- 状态信息 -->
          <state-Info ref="stateInfo" :deviceInfo="record"></state-Info>
          <!-- 数据分析 -->
          <data-analysis ref="dataAnalysis" :productId="deviceInfo.productId" :deviceInfo="deviceInfo" v-if="curRenderStates.showDataAnalysis"></data-analysis>
        </div>
      </a-tab-pane>
      <a-tab-pane key="4" tab="日志管理" :force-render="false" v-if='curRenderStates.showJournal'>
        <journal-management class='com' ref="journalManagement"></journal-management>
      </a-tab-pane>
      <a-tab-pane key='5' tab='虚拟机管理' :force-render='false' v-if='curRenderStates.showVirtualMachine'>
        <virtual-machine-list ref='virtualMachineList'></virtual-machine-list>
      </a-tab-pane>
      <a-tab-pane key='6' tab='云主机管理' :force-render='false' v-if='curRenderStates.showCloudHost'>
        <cloud-host-list ref='cloudHostlist'></cloud-host-list>
      </a-tab-pane>
      <a-tab-pane key='6' tab='云主机管理' :force-render='false' v-if='curRenderStates.showQilinCloudHost'>
        <qilin-cloud-host-list ref='cloudHostlist'></qilin-cloud-host-list>
      </a-tab-pane>
      <a-tab-pane key='6' tab='云主机管理' :force-render='false' v-if='curRenderStates.showZStackCloudHost'>
        <ZStack-cloud-host-list ref='cloudHostlist'></ZStack-cloud-host-list>
      </a-tab-pane>
      <a-tab-pane key="7" tab="告警策略" :force-render="false" v-if='curRenderStates.showAlarm'>
        <alarm-management class='com' ref="alarmManagement"></alarm-management>
      </a-tab-pane>
      <a-tab-pane key="8" tab="设备面板" :force-render="false" v-if='curRenderStates.showVisView'>
        <panel-topo-view
        class='com'
        ref="panelTopoView"
        :productId="deviceInfo.productId"
        :deviceInfo="deviceInfo"
        ></panel-topo-view>
      </a-tab-pane>
      <!-- <a-tab-pane key="11" tab="端口列表" :force-render="false" v-if='curRenderStates.showDevicePorts'>
       <device-port-list class='com' :deviceInfo="deviceInfo"></device-port-list>
      </a-tab-pane> -->
      <a-tab-pane key="9" tab="设备告警" :force-render="false" v-if='curRenderStates.showDeviceAlarm'>
        <device-alarm-management class='com' ref="deviceAlarmManagement"></device-alarm-management>
      </a-tab-pane>
      <a-tab-pane key="10" :tab="deviceRestoreVisible==1?'备份还原':'备份'" :force-render="false" v-if='curRenderStates.showBacResConfig'>
        <bac-res-config class='com' ref="bacResConfig"></bac-res-config>
      </a-tab-pane>
      <a-tab-pane key="11" tab="拓扑视图" :force-render="false" v-if="curRenderStates.showTopoView">
        <topo-info class='com' ref="topoInfo"></topo-info>
      </a-tab-pane>
<!--      <a-tab-pane key="12" tab="视图详情" :force-render="false"  v-if='iframeUrl'>-->
<!--       <iframe id='grafana-box' width='100%' height='100%' style='border: none' :src='iframeUrl'></iframe>-->
<!--      </a-tab-pane>-->
    </a-tabs>
  </div>
</template>

<script>
import baseInfo from '@views/devicesystem/deviceshow/BaseInfo.vue'
import stateInfo from '@views/devicesystem/deviceshow/StateInfo.vue'
import dataAnalysis from '@views/devicesystem/deviceshow/DataAnalysis.vue'
import alarmManagement from '@views/devicesystem/deviceshow/AlarmManagement.vue'
import deviceAlarmManagement from '@views/devicesystem/deviceshow/DeviceAlarmManagement.vue'
import PanelTopoView from '@views/devicesystem/productsetting/paneltopo/PanelTopoView.vue'
import journalManagement from '@views/devicesystem/deviceshow/JournalManagement.vue'
import {getAction} from '@/api/manage'
import virtualMachineList from '@views/vmware/ESXI/modules/VirtualMachineList.vue'
import cloudHostList from '@views/cloudPlatformManagement/cloudPlatform/modules/CloudHostList.vue'
import QilinCloudHostList from '@views/cloudPlatformManagement/QilinCloudPlatform/modules/QilinCloudHostList.vue'
import ZStackCloudHostList from '@views/cloudPlatformManagement/ZStackCloudPlatform/modules/ZStackCloudHostList.vue'
import bacResConfig from '@views/networkManagement/networkDevice/modules/BackupAndRestoreConfig.vue'
import { queryConfigureDictItem, queryConfigureDictItems } from '@api/api'
import DevicePortList from './DevicePortList.vue'
import topoInfo from '@views/devicesystem/deviceshow/topoInfo.vue'
export default {
  name: 'Tabs',
  components: {
    baseInfo,
    stateInfo,
    dataAnalysis,
    journalManagement,
    alarmManagement,
    virtualMachineList,
    cloudHostList,
    QilinCloudHostList,
    ZStackCloudHostList,
    deviceAlarmManagement,
    PanelTopoView,
    bacResConfig,
    DevicePortList,
    topoInfo
  },
  props: {
    isEditing: {
      type: Boolean,
      default: true,
      required: false,
    },
    deviceInfo:{
      type:Object,
      required: false,
      default: ()=>{ return {}}
    },
    renderStates:{
      type:Object,
      required: false,
      default:{}
    },

  },
  data() {
    return {
      defaultActiveKey: '1',
      deviceRestoreVisible:'0',
      url: {
        getUrl: '/device/deviceInfo/getUrl',
      },
      record: {},
      curRenderStates:{
        showBaseInfo:false,
        showStateInfo:false,
        showDataAnalysis:false,
        showJournal:false,
        showVirtualMachine:false,
        showCloudHost:false,
        showQilinCloudHost: false,
        showZStackCloudHost: false,
        showAlarm:false,
        showVisView:false,
        showDeviceAlarm:false,
        showBacResConfig:false,
        showTopoView:false,
      },
      iframeUrl:"",
    }
  },

  watch:{
    deviceInfo(newVal,oldVal){
      this.show(newVal)
    }
  },
  created() {
    this.show(this.deviceInfo)
    this.getConfigureDict()
  },
  mounted() {
    this.curRenderStates={...this.renderStates}
    this.getVisViewVisible()
  },
  methods: {
    getVisViewVisible() {
      this.curRenderStates.showVisView = false;
      // 判断该设备的产品类是否设置了面板 详情显示设备面板标签
      getAction('/product/panelInfo/queryAllByProductId', { productId: this.deviceInfo.productId })
        .then(res => {
          if (res.success && res.result && res.result.length > 0) {
            this.curRenderStates.showVisView = true;
          }
        })
    },
    /*新增：获取配置字典，设置是否显示还原功能*/
    getConfigureDict() {
      queryConfigureDictItem({
        parentCode: 'deviceRestoreVisible',
        childCode: 'restoreVisible'
      }).then((res) => {
        if (res.success) {
          this.deviceRestoreVisible = res.result
        } else {
          this.deviceRestoreVisible = '0'
        }
      }).catch(() => {
        this.deviceRestoreVisible = '0'
      })
    },
    callback(key) {
      this.defaultActiveKey = key
      let that = this
      setTimeout(function () {
        if (key == '1') {
          if (that.curRenderStates.showBaseInfo) {
            that.$refs.baseInfo && that.$refs.baseInfo.show(that.record)
          }
          if (that.curRenderStates.showStateInfo) {
            that.$refs.stateInfo && that.$refs.stateInfo.show(that.record)
          }
          if (that.curRenderStates.showDataAnalysis) {
            that.$refs.dataAnalysis && that.$refs.dataAnalysis.show(that.record)
          }
          // 显示数据分析
        } else if (key == '4') {
          that.$refs.journalManagement && that.$refs.journalManagement.show(0,that.record)
        } else if (key == '5') {
          that.$refs.virtualMachineList && that.$refs.virtualMachineList.show(that.record)
        } else if (key == '6') {
          that.$refs.cloudHostlist && that.$refs.cloudHostlist.show(that.record)
        }else if (key == '7') {
          that.$refs.alarmManagement && that.$refs.alarmManagement.show(0,that.record)
        } else if (key == '8') {
          that.$refs.visView && that.$refs.visView.show(that.record.panelJson)
        } else if (key == '9') {
          that.$refs.deviceAlarmManagement && that.$refs.deviceAlarmManagement.show(0,that.record)
        }else if (key == '10') {
          that.$refs.bacResConfig && that.$refs.bacResConfig.show(that.record,that.deviceRestoreVisible)
        }else if (key == '11') {
          if (that.curRenderStates.showTopoView) {
            that.$refs.topoInfo && that.$refs.topoInfo.show(that.record)
          }
        }
      }, 100)
    },
    show(record) {
      this.record = record
      getAction(this.url.getUrl, { id: record.id }).then((res) => {
        if (res.success) {
          let url = res.result
          this.record['selfUrl'] = url
    /*      if(this.deviceInfo && this.deviceInfo.selfUrl){
            let ipStr = window.location.host
            queryConfigureDictItems({
              parentCode: 'grafanaProxy',
              childCode: 'url'
            }).then((res) => {
              if (res.success && res.result) {
                res.result.forEach(el=>{
                  let arys = el.value.split('>')
                  if (!!ipStr && ipStr === arys[0]) {
                    this.iframeUrl = arys[1] +this.deviceInfo.selfUrl;
                  }
                })
              }
            })
          }*/
        }else{

        }
      }).catch(err=>{
        console.log("/device/deviceInfo/getUrl == ",err)
      }).finally(()=>{
        this.defaultActiveKey = this.record.tabKey ? (this.record.tabKey + '') : '1'
        this.callback(this.defaultActiveKey)
      })
    },
    changeDeviceBaseInfo(){
      // this.$emit('update:deviceInfo',info)
      this.$emit('updateDeviceInfo')
    },
  },
}
</script>
<style>
#grafana-box .css-srjygq{display: none}
</style>
<style lang='less' scoped>
.preloader__logo{display: none}

.tabs-container {
  height: 100%;
  //height: calc(100% - 135px);
}

.ant-tabs-bar {
  margin-bottom: 24px !important;
}

::v-deep .ant-tabs {
  height: 100%;
  background-color: white;
  padding: 8px 24px 24px;
  border-radius: 3px;

  .ant-tabs-content {
    height: calc(100% - 60px);
    .ant-tabs-tabpane-active {
      height: 100%;
     /* overflow-y: auto;
      overflow-x: hidden;*/
    }
  }
  .com{
    height: 100%;
   /* overflow-y: auto;
    overflow-x: hidden;*/
  }
}
/* 基本信息的样式，基础信息、状态信息、设备功能合成一个tab页 */
.info-function {
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  .scroll {
    height: auto;
  }
}
</style>
