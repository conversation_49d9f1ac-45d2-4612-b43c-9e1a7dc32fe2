<template>
  <j-modal :width="modalWidth" :visible="visible" :title="title" @ok="handleSubmit" @cancel="close" cancelText="关闭">
    <div style="height: 550px; overflow: hidden; overflow-y: auto; background-color: #ececec; padding: 10px">
      <a-row style="background-color: #ececec; padding: 10px; margin: -10px; white-space: nowrap">
        <a-col :span="6" style="overflow: hidden; padding-right: 10px">
          <a-card :bordered="false" style="overflow-x: auto; width: 100%">
            <!--组织机构-->
            <a-directory-tree
              selectable
              :selectedKeys="selectedDepIds"
              :checkStrictly="true"
              :dropdownStyle="{ overflow: 'auto' }"
              :treeData="departTree"
              :expandAction="false"
              :expandedKeys.sync="expandedKeys"
              @select="onDepSelect"
            />
          </a-card>
        </a-col>
        <a-col :span="18" style="overflow: hidden">
          <a-card :bordered="false" style="width: 100%; overflow-x: auto">
            用户账号:
            <a-input-search
              :style="{ width: '150px', marginBottom: '15px' }"
              placeholder="请输入账号"
              v-model="queryParam.username"
              @search="onSearch"
            ></a-input-search>
            <a-button @click="searchReset(1)" style="margin-left: 20px" icon="redo">重置</a-button>
            <!--用户列表-->
            <a-table
              ref="table"
              :scroll="scrollTrigger"
              rowKey="id"
              :columns="columns"
              :dataSource="dataSource"
              :pagination="ipagination"
              :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange, type: getType }"
              :loading="loading"
              @change="handleTableChange"
            >
            </a-table>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </j-modal>
</template>

<script>
import Vue from 'vue'
import { USER_INFO } from '@/store/mutation-types'
import { filterObj } from '@/utils/util'
import { queryDepartTreePowerList, getUserList, queryUserByDepId,getUsersByCondition,queryUserByDepIdWithPage } from '@/api/api'

export default {
  name: 'JSelectUserByDepModal',
  components: {},
  props: ['modalWidth', 'multi', 'userIds'],
  data() {
    return {
      queryParam: {
        username: '',
      },
      columns: [
        {
          title: '用户账号',
          align: 'center',
          dataIndex: 'username',
        },
        {
          title: '用户姓名',
          align: 'center',
          dataIndex: 'realname',
        },
        {
          title: '性别',
          align: 'center',
          dataIndex: 'sex',
          width: 30,
          customRender: function (text) {
            if (text === 1) {
              return '男'
            } else if (text === 2) {
              return '女'
            } else {
              return text
            }
          },
        },
        {
          title: '手机',
          align: 'center',
          dataIndex: 'phone',
          width: 150,
        },
        {
          title: '部门',
          align: 'center',
          dataIndex: 'orgCodeTxt',
        },
      ],
      scrollTrigger: {},
      dataSource: [],
      selectedRowKeys: [],
      selectUserRows: [],
      selectUserIds: [],
      title: '根据部门选择用户',
      ipagination: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '30'],
        showTotal: (total, range) => {
          return range[0] + '-' + range[1] + ' 共' + total + '条'
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0,
      },
      isorter: {
        column: 'createTime',
        order: 'desc',
      },
      selectedDepIds: [],
      departTree: [],
      visible: false,
      form: this.$form.createForm(this),
      loading: false,
      expandedKeys: [],
    }
  },
  computed: {
    // 计算属性的 getter
    getType: function () {
      return this.multi == true ? 'checkbox' : 'radio'
    },
  },
  watch: {
    userIds: {
      immediate: true,
      // handler() {
      //   // this.initUserNames()
      // },
    },
  },
  created() {
    // 该方法触发屏幕自适应
    this.resetScreenSize()
    //   this.loadData()
  },
  methods: {
    initUserNames() {
      if (this.userIds && this.userIds.length > 0) {
        // 这里最后加一个 , 的原因是因为无论如何都要使用 in 查询，防止后台进行了模糊匹配，导致查询结果不准确
        let values = this.userIds.split(',') + ','
        getUsersByCondition({
          username: values,
          pageNo: 1,
          pageSize: values.length,
        }).then((res) => {
          if (res.success) {
            let selectedRowKeys = []
            let realNames = []
            res.result.records.forEach((user) => {
              realNames.push(user['realname'])
              selectedRowKeys.push(user['id'])
            })

            this.selectedRowKeys = selectedRowKeys
            this.$emit('initComp', realNames.join(','))
          }
        })
      } else {
        // JSelectUserByDep组件bug issues/I16634
        this.$emit('initComp', '')
      }
    },
    async loadData(arg) {
      if (arg === 1) {
        this.ipagination.current = 1
      }
      // const userInfo = Vue.ls.get(USER_INFO)
      // if (userInfo.orgCode == this.departTree[5].orgCode) {
      //   this.initQueryUserByDepId(this.departTree[0].id)
      // } else {
      //   // this.initQueryUserByDepId(this.departTree[0].id)
      // }

      if (this.selectedDepIds && this.selectedDepIds.length > 0) {
        await this.initQueryUserByDepId(this.ipagination.current,this.ipagination.pageSize,this.selectedDepIds,this.queryParam.username)
      } else {
        this.loading = true
        let params = this.getQueryParams() //查询条件
        await getUsersByCondition(params)
          .then((res) => {
            if (res.success) {
              this.dataSource = res.result.records
              this.ipagination.total = res.result.total
            }
          })
          .finally(() => {
            this.loading = false
          })
      }
    },
    // 触发屏幕自适应
    resetScreenSize() {
      let screenWidth = document.body.clientWidth
      if (screenWidth < 500) {
        this.scrollTrigger = { x: 800 }
      } else {
        this.scrollTrigger = {}
      }
    },
    showModal() {
      this.visible = true
      this.queryDepartTree()
      this.initUserNames()
      this.form.resetFields()
    },
    getQueryParams() {
      let param = Object.assign({}, this.queryParam, this.isorter)
      param.field = this.getQueryField()
      param.pageNo = this.ipagination.current
      param.pageSize = this.ipagination.pageSize
      return filterObj(param)
    },
    getQueryField() {
      let str = 'id,'
      for (let a = 0; a < this.columns.length; a++) {
        str += ',' + this.columns[a].dataIndex
      }
      return str
    },
    searchReset(num) {
      let that = this
      that.selectedRowKeys = []
      that.selectUserIds = []
      that.selectedDepIds = []
      //if (num !== 0) {
        that.queryParam = {}
        that.loadData(1)
      //}
      
    },
    close() {
      this.searchReset(0)
      this.visible = false
    },
    handleTableChange(pagination, filters, sorter) {
      //TODO 筛选
      if (Object.keys(sorter).length > 0) {
        this.isorter.column = sorter.field
        this.isorter.order = 'ascend' === sorter.order ? 'asc' : 'desc'
      }
      this.ipagination = pagination
      this.loadData()
    },
    handleSubmit() {
      let that = this
      this.getSelectUserRows()
      that.$emit('ok', that.selectUserRows, that.selectUserIds)
      that.searchReset(0)
      that.close()
    },
    //获取选择用户信息
    getSelectUserRows(rowId) {
      let dataSource = this.dataSource
      let userIds = ''
      this.selectUserRows = []
      for (let i = 0, len = dataSource.length; i < len; i++) {
        if (this.selectedRowKeys.includes(dataSource[i].id)) {
          this.selectUserRows.push(dataSource[i])
          userIds = userIds + ',' + dataSource[i].username
        }
      }
      this.selectUserIds = userIds.substring(1)
    },
    // 点击树节点,筛选出对应的用户
    onDepSelect(selectedDepIds) {
      //拿到当前登陆的用户的信息
      const userInfo = Vue.ls.get(USER_INFO)
      if (selectedDepIds[0] != null) {
        if (userInfo.orgCode == this.departTree[0].orgCode) {
          this.initQueryUserByDepId(null,null,selectedDepIds,this.queryParam.username) // 调用方法根据选选择的id查询用户信息
          if (this.selectedDepIds[0] !== selectedDepIds[0]) {
            this.selectedDepIds = [selectedDepIds[0]]
          }
        } else {
          if (this.departTree[0].id != selectedDepIds[0]) {
            this.initQueryUserByDepId(null,null,selectedDepIds,this.queryParam.username) // 调用方法根据选选择的id查询用户信息
            if (this.selectedDepIds[0] !== selectedDepIds[0]) {
              this.selectedDepIds = [selectedDepIds[0]]
            }
          }
        }
      }
    },
    onSelectChange(selectedRowKeys, selectionRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectionRows = selectionRows
    },
    onSearch() {
      this.loadData(1)
    },
    // 根据选择的id来查询用户信息
    initQueryUserByDepId(pageNo,pageSize,selectedDepIds,username) {
      this.loading = true
      return queryUserByDepIdWithPage({pageNo:pageNo,pageSize:pageSize, id: selectedDepIds.toString() ,username:username})
        .then((res) => {
          if (res.success) {
            this.dataSource = res.result.records
            this.ipagination.total = res.result.total
          }else{
			this.dataSource = []
			this.ipagination.total = 0
		  }
        })
        .finally(() => {
          this.loading = false
        })
    },
    queryDepartTree() {
      queryDepartTreePowerList().then((res) => {
        if (res.success) {
          this.departTree = res.result
          // 默认展开父节点
          this.expandedKeys = this.departTree.map((item) => item.id)
          this.loadData()
        }
      })
    },
    modalFormOk() {
      this.loadData()
    },
  },
}
</script>

<style scoped>
.ant-table-tbody .ant-table-row td {
  padding-top: 10px;
  padding-bottom: 10px;
}

#components-layout-demo-custom-trigger .trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color 0.3s;
}
</style>