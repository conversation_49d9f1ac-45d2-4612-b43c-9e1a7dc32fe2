.ant-notification-notice{
/*  max-height: calc(100vh * 0.8);
  overflow: auto;*/
}
.ant-notification-topCenter {
  top: 10% !important;
  bottom: auto !important;
  /*left: calc((100vw - var(--notification-topCenter)) /2) !important;*/
  left: calc((100vw - 384px ) /2) !important;
  right: auto !important;
  margin: auto;
}
.ant-notification-topLeft,.ant-notification-topRight{
  top:10% !important;
}
.ant-notification-topLeft,
.ant-notification-bottomLeft {
  /*240左侧菜单宽度240px + 16px */
  /*left: 256px !important;*/
  left: 16px !important;
  margin-right: 0 !important;
  margin-left: 0 !important;
  /*margin-left: 256px !important;*/
  /*margin-left: 24px;*/
}
.ant-notification-topCenter .ant-notification-fade-enter.ant-notification-fade-enter-active
{
  -webkit-animation-name: NotificationCenterFadeIn !important;
  animation-name: NotificationCenterFadeIn !important;
}
@-webkit-keyframes NotificationCenterFadeIn {
  0% {
    top: -1000px;
    opacity: 0;
  }
  100% {
    top: 10%;
    opacity: 1;
  }
}
@keyframes NotificationCenterFadeIn {
  0% {
    top: -1000px;
    opacity: 0;
  }
  100% {
    top: 10%;
    opacity: 1;
  }
}

/*自定义的告警类型图标颜色，其他类型的颜色在public/color.less文件中查看*/
.ant-notification-notice-icon-alarm {
  color: #dc010c;
}

.ant-notification-more {
  cursor: pointer;
  color: rgba(0, 0, 0, 0.45);
  text-align: right;
  font-size: 14px;
  margin-bottom: 16px;
}

.ant-notification-more:hover {
  color: rgba(0, 0, 0, 0.85);
}