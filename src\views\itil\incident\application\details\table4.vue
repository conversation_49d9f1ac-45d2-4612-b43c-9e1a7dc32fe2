<template>
    <a-table
        :columns="columns"
        :data-source="data"
        ref="table"
        size="middle"
        bordered
        rowKey="id"
    >
    </a-table>
</template>
<script>
export default {
  // 关联问题
  name: 'table4',
  data() {
    return {
      columns: [
        {
          title: '序号',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function(t, r, index) {
            return parseInt(index) + 1
          }
        },
        {
          title: '问题编号',
          align: 'center',
          dataIndex: ''
        },
        {
          title: '问题标题',
          align: 'center',
          dataIndex: ''
        },
        {
          title: '问题类型',
          align: 'center',
          dataIndex: ''
        },
        {
          title: '问题状态',
          align: 'center',
          dataIndex: ''
        }
      ],
      data: []
    }
  }
}
</script>
<style scoped></style>
