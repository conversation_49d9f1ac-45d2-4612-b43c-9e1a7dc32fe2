<template>
  <div>
    <table class="gridtable">
      <tr>
        <td class="leftTd">问题类型</td>
        <td class="rightTd">{{ record.questionType }}</td>
        <td class="leftTd">提问时间</td>
        <td class="rightTd">{{ record.createTime }}</td>
      </tr>
      <tr>
        <td class="leftTd">问题描述</td>
        <td class="rightTd">{{ record.question }}
        </td> <td class="leftTd">所属单位</td>
        <td class="rightTd">{{ record.company }}</td>
<!--        <td class="leftTd">所属地区</td>
        <td class="rightTd">{{ record.regionName }}</td>-->
      </tr>
      <tr>
        <td class="leftTd">提问人</td>
        <td class="rightTd">{{ record.quizzer }}</td>
        <td class="leftTd">联系方式</td>
        <td class="rightTd">{{ record.contact }}</td>
      </tr>
<!--      <tr>
        <td class="leftTd">设备地址</td>
        <td class="rightTd">{{ record.ip }}</td>
        <td class="leftTd">分配人</td>
        <td class="rightTd">{{ record.confirmor }}</td>
      </tr>-->
      <tr>
        <td class="leftTd">详细地址</td>
        <td class="rightTd">{{ record.region }}</td>
        <td class="leftTd">分配人</td>
        <td class="rightTd">{{ record.confirmor }}</td>
      </tr>
      <tr>
        <td class="leftTd">处理人</td>
        <td class="rightTd">{{ record.answerer }}</td>
        <td class="leftTd">分配时间</td>
        <td class="rightTd">{{ record.confirmTime }}</td>
      </tr>
      <tr>
        <td class="leftTd">处理时间</td>
        <td class="rightTd">{{ record.answerTime }}</td>
        <td class="leftTd">解决方案</td>
        <td class="rightTd">{{ record.answererContent }}</td>
      </tr>
    </table>
    <!-- <a-tabs :animated="false" defaultActiveKey="1" v-if="record.commType != '1'">
      <a-tab-pane tab="历史" key="1">
        <history-record ref="HistoryInfoForm"></history-record>
      </a-tab-pane>
    </a-tabs> -->
  </div>
</template>
<script>
import HistoryRecord from './HistoryRecord.vue'
export default {
  name: 'QuestionInfo',
  components: {
    HistoryRecord,
  },
  props: {
    data: {
      type: Object,
    },
  },
  data() {
    return {
      size: 'middle',
      record: {},
    }
  },
  created() {},
  watch: {
    data: function (val, oldVal) {
      this.record = Object.assign({}, val)
    },
  },
  mounted() {
    this.record = Object.assign({}, this.data)
    console.log("问题详情 === ",this.record)
    if (this.record != {} && this.record.commType != '1') {
      this.$refs.HistoryInfoForm.getDataList(this.record.id)
    }
  },
  methods: {},
}
</script>
<style lang="less" scoped>
table.gridtable {
  font-family: verdana, arial, sans-serif;
  font-size: 14px;
  color: #fff;
  border-width: 1px;
  border-color: #0A368B;
  border-collapse: collapse;
  text-align: left;
  width: 100%;
}
table.gridtable td {
  border-width: 1px;
  border-style: solid;
  border-color: #0A368B;
}
.leftTd {
  width: 17%;
  background-color: rgba(21, 85, 175, 0.4);
  padding: 16px 24px;
  text-align: center;
}
.rightTd {
  width: 35%;
  padding: 16px 24px;
}
/deep/ .ant-tabs-nav .ant-tabs-tab-active{
  color:#fff;
}
/deep/ .ant-empty-description{
  color:white !important;
}
</style>