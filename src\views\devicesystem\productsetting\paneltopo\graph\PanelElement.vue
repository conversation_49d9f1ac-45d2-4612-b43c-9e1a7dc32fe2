<template>
  <div class="icon-box" :style="{ fontSize: sizeNum + 'px'}">
    <YqSvg
      v-if="iconType"
      :type="iconType"
      :outSide="outSideUrl + iconType"
      :style="{ color: iconColor,transform:`rotate(${iconAngle}deg)` }"
    />
  </div>
</template>

<script>
import YqSvg from '@/components/tools/SvgIcon/index.js'
export default {
  name: 'PanelElement',
  inject: ['getGraph', 'getNode'],
  components: {
    YqSvg,
  },
  data() {
    return {
      sizeNum: 18,
      iconColor: '',
      iconType: '',
      outSideUrl:window._CONFIG['downloadUrl'] + '/',
      iconWidth:18,
      iconHeight:18,
      iconAngle:0,
    }
  },
  beforeCreate(){
  },
  mounted() {
    const self = this
    const node = this.getNode()
    const { typeIcon,iconColor,angle } = node.getData()
    const size = node.size()
    this.sizeNum = Math.min(size.width,size.height)
    this.iconType = typeIcon
    this.iconColor = iconColor;
    this.iconAngle = angle;
    // 监听数据改变事件
    node.on("change:data", ({ current }) => {
         this.iconColor = current.iconColor;
         this.iconAngle = current.angle;
    });
    node.on('change:size', ({ current }) => {
   
      this.sizeNum = Math.min(current.width, current.height)
    })
  },
  methods: {
    add() {
      const node = this.getNode()
      //   const { num } = node.getData();
      node.setData({
        // num: num + 1,
      })
    },
  },
}
</script>
<style lang="less" scoped>
.icon-box{
  overflow: hidden;
  width: 100%;
  height: 100%;
  line-height: 1;
}
</style>