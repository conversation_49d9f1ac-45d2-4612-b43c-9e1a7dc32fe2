<template>
  <div class="table-operator scroll" ref="tableCard">
    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay" style='text-align: center'>
          <a-menu-item key="1" @click="batchDel"> 删除 </a-menu-item>
        </a-menu>
        <a-button>批量操作<a-icon type="down" /></a-button>
      </a-dropdown>
    </div>
    <a-table
      v-if="tableH"
      style="margin-right: 1px"
      ref="table"
      bordered
      :columns="columns"
      :dataSource="dataSource"
      :loading="loading"
      :rowKey="
        (record) => {
          return record.index
        }
      "
      :pagination="false"
      :scroll="{ x: tableW, y: tableH }"
      @change="handleTableChange"
    >
      <template slot="alarmStatus" slot-scope="text">
        <span v-if="text == 1" style="color: red">告警</span>
        <span v-else>正常</span>
      </template>
      <template slot="portStatus" slot-scope="text">
        <span v-if="text == 1" style="color: green">连接</span>
        <span v-else-if="2">未连接</span>
        <span v-else-if="3">其他</span>
      </template>
      <a slot="action" slot-scope="record" @click="showDetail(record)" >详情</a>
      <!-- 字符串超长截取省略号显示-->
      <span slot="templateContent" slot-scope="text">
        <j-ellipsis :value="text" :length="25" />
      </span>
      <template slot="tooltip" slot-scope="text">
        <a-tooltip placement="topLeft" :title="text" trigger="hover">
          <div class="tooltip">
            {{ text }}
          </div>
        </a-tooltip>
      </template>
    </a-table>
  </div>
</template>

<script>
import { deleteAction, getAction, putAction } from '@/api/manage'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { queryConfigureDictItem } from '../../../api/api'
export default {
  name: 'devicePortList',
  mixins: [JeecgListMixin],
  props: {
    deviceInfo: {
      type: Object,
      required: true,
      default: ()=>null,
    },
    type:{
      type: String,
      default: "0",
      required: true,
    },
    oid:{
      type: String,
      default: "",
      required: true,

    },
  },
  data() {
    return {
      alarmHistory: {},
      associationId: '',
      formUrl: '',
      processDefinitionKey: '',
      startUrl: '',
      dialogStartInstanceVisible: false,
      processDefinition: undefined,
      // 表头
      columns: [
        {
          title: '序号',
          dataIndex: 'index',
          customCell: () => {
            let cellStyle = 'text-align: center;'
            return {
              style: cellStyle,
            }
          },
        },
        {
          title: '端口描述',
          dataIndex: 'portDesc',
          customCell: () => {
            let cellStyle = 'text-align: center;'
            return {
              style: cellStyle,
            }
          },
        },
        {
          title: '端口协议',
          dataIndex: 'portType',
          customCell: () => {
            let cellStyle = 'text-align: center;'
            return {
              style: cellStyle,
            }
          },
        },
        {
          title: '端口状态',
          dataIndex: 'portStatus',
          scopedSlots: {
            customRender: 'portStatus',
          },
          customCell: () => {
            let cellStyle = 'text-align: center;'
            return {
              style: cellStyle,
            }
          },
        },
        {
          title: '告警状态',
          dataIndex: 'alarmStatus',
          scopedSlots: {
            customRender: 'alarmStatus',
          },
          customCell: () => {
            let cellStyle = 'text-align: center;'
            return {
              style: cellStyle,
            }
          },
        },
        {
          title: '输入流量',
          dataIndex: 'inputFlow',
          customCell: () => {
            let cellStyle = 'text-align: center;'
            return {
              style: cellStyle,
            }
          },
        },
        {
          title: '输出流量',
          dataIndex: 'outputFlow',
          customCell: () => {
            let cellStyle = 'text-align: center;'
            return {
              style: cellStyle,
            }
          },
        },
        {
          title: '端口输入速率',
          dataIndex: 'inSpeed',
          customCell: () => {
            let cellStyle = 'text-align: center;'
            return {
              style: cellStyle,
            }
          },
        },
        {
          title: '端口输出速率',
          dataIndex: 'outSpeed',
          customCell: () => {
            let cellStyle = 'text-align: center;'
            return {
              style: cellStyle,
            }
          },
        },
        {
          title: '操作',
          key: 'operation',
          fixed: 'right',
          width: 100,
          scopedSlots: { customRender: 'action' },
        },
      ],
      url: {
        list: '/alarm/alarmHistory/list',
      },
      disableMixinCreated: true,
      alarmLevelList: [],
      tableH: 0,
      tableW: 0,
      baseList:[],
      loading:false,
    }
  },

  created() {
    this.getPortData()
  },
  mounted() {
    let rect = this.$refs.tableCard.getBoundingClientRect()
    this.tableH = rect.height - 55
    this.tableW = rect.width
  },
  methods: {
    getPortData(isRefresh) {
      let params = {
        deviceCode: this.deviceInfo.deviceCode,
        productId: this.deviceInfo.productId,
        topoType: this.type,
      }
      if(isRefresh){
        params.connectStr = JSON.stringify({ oid: this.oid })
      }
      this.loading = true;
      getAction('/net/device/devicePortInfo', params).then((res) => {
        this.loading  = false;
        if (res.success && res.result) {
          this.baseList = res.result.values
          let values = res.result.values
          let mapV = values.map((el) => {
            let tem = {}
            for (let key in el) {
              tem[key] = el[key].value + el[key].unit
            }
            return tem
          })
          this.dataSource = mapV
        }
      }).catch(err=>{
        this.loading = false;
      })
    },
    showDetail(record){
      let info = this.baseList.find(el=>{
        return el.index.value+el.index.unit === record.index
      })
      this.$emit("showDetail",info)
    },
  },
}
</script>

<style lang='less' scoped>
@import '~@assets/less/common.less';
@import '~@assets/less/scroll.less';
.scroll {
  height: 100%;
  overflow: hidden;
  overflow-y: auto;
}
.confirm {
  color: rgba(0, 0, 0, 0.25) !important;
}
</style>